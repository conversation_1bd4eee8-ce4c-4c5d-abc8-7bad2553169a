# Makefile for Blawger development
# Provides convenient commands for development, testing, and deployment

.PHONY: help install install-dev test test-unit test-integration test-performance
.PHONY: lint format type-check security-check quality-check coverage
.PHONY: docs docs-serve clean build package upload
.PHONY: pre-commit setup-dev benchmark profile

# Default target
help: ## Show this help message
	@echo "Blawger Development Commands"
	@echo "============================"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Installation
install: ## Install package for production
	pip install -e .

install-dev: ## Install package with development dependencies
	pip install -e ".[dev]"
	pre-commit install

setup-dev: install-dev ## Complete development environment setup
	@echo "Setting up development environment..."
	@echo "✅ Dependencies installed"
	@echo "✅ Pre-commit hooks installed"
	@echo "🎉 Development environment ready!"

# Testing
test: ## Run all tests
	pytest

test-unit: ## Run unit tests only
	pytest tests/unit/ -v

test-integration: ## Run integration tests only
	pytest tests/integration/ -v

test-performance: ## Run performance tests
	pytest tests/performance/ -v --benchmark-only

test-fast: ## Run tests excluding slow ones
	pytest -m "not slow"

test-coverage: ## Run tests with coverage report
	pytest --cov=gwernpy --cov-report=html --cov-report=term-missing

test-parallel: ## Run tests in parallel
	pytest -n auto

# Code Quality
lint: ## Run all linting checks
	flake8 gwernpy tests
	bandit -r gwernpy -c pyproject.toml

format: ## Format code with black and isort
	black gwernpy tests
	isort gwernpy tests

format-check: ## Check code formatting without making changes
	black --check gwernpy tests
	isort --check-only gwernpy tests

type-check: ## Run type checking with mypy
	mypy gwernpy

security-check: ## Run security checks
	bandit -r gwernpy -c pyproject.toml
	safety check

quality-check: format-check lint type-check security-check ## Run all quality checks

# Coverage
coverage: ## Generate coverage report
	pytest --cov=gwernpy --cov-report=html --cov-report=xml --cov-report=term-missing
	@echo "Coverage report generated in htmlcov/index.html"

coverage-serve: coverage ## Serve coverage report
	python -m http.server 8080 -d htmlcov

# Documentation
docs: ## Build documentation
	cd docs && make html

docs-serve: docs ## Serve documentation locally
	cd docs/_build/html && python -m http.server 8000

docs-clean: ## Clean documentation build
	cd docs && make clean

# Benchmarking and Profiling
benchmark: ## Run performance benchmarks
	pytest tests/performance/ --benchmark-only --benchmark-sort=mean

benchmark-compare: ## Compare benchmarks with baseline
	pytest tests/performance/ --benchmark-only --benchmark-compare

profile: ## Profile the application
	python -m cProfile -o profile.stats -m gwernpy.cli build --help
	python -c "import pstats; p = pstats.Stats('profile.stats'); p.sort_stats('cumulative').print_stats(20)"

# Development utilities
clean: ## Clean build artifacts and cache
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf coverage.xml
	rm -rf profile.stats
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

clean-all: clean ## Clean everything including virtual environments
	rm -rf .venv/
	rm -rf .tox/

# Pre-commit
pre-commit: ## Run pre-commit hooks on all files
	pre-commit run --all-files

pre-commit-update: ## Update pre-commit hooks
	pre-commit autoupdate

# Building and packaging
build: clean ## Build package
	python -m build

package: build ## Create distribution packages
	@echo "Package built successfully!"
	@echo "Files created:"
	@ls -la dist/

check-package: package ## Check package integrity
	twine check dist/*

upload-test: check-package ## Upload to test PyPI
	twine upload --repository testpypi dist/*

upload: check-package ## Upload to PyPI
	twine upload dist/*

# CLI testing
cli-test: ## Test CLI commands
	@echo "Testing CLI installation..."
	blawger version
	@echo "✅ CLI working correctly"

cli-demo: ## Run CLI demo
	@echo "Creating demo site..."
	rm -rf demo-site
	blawger init demo-site
	cd demo-site && blawger build
	@echo "✅ Demo site created in demo-site/"

# Development server
serve-demo: cli-demo ## Serve demo site
	cd demo-site && blawger serve --no-open

# Git hooks and workflow
git-hooks: ## Install git hooks
	pre-commit install --hook-type pre-commit
	pre-commit install --hook-type commit-msg
	pre-commit install --hook-type pre-push

# Release workflow
release-check: quality-check test coverage ## Pre-release checks
	@echo "🔍 Running pre-release checks..."
	@echo "✅ Code quality checks passed"
	@echo "✅ All tests passed"
	@echo "✅ Coverage requirements met"
	@echo "🎉 Ready for release!"

# Environment info
env-info: ## Show environment information
	@echo "Environment Information"
	@echo "======================"
	@echo "Python version: $$(python --version)"
	@echo "Pip version: $$(pip --version)"
	@echo "Virtual environment: $${VIRTUAL_ENV:-Not activated}"
	@echo "Current directory: $$(pwd)"
	@echo "Git branch: $$(git branch --show-current 2>/dev/null || echo 'Not a git repository')"
	@echo "Git status:"
	@git status --porcelain 2>/dev/null || echo "Not a git repository"

# Performance monitoring
monitor: ## Monitor performance during development
	@echo "Starting performance monitoring..."
	python -c "
import psutil
import time
import os
process = psutil.Process(os.getpid())
print(f'Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB')
print(f'CPU usage: {process.cpu_percent()}%')
"

# Dependency management
deps-check: ## Check for dependency updates
	pip list --outdated

deps-update: ## Update dependencies (use with caution)
	pip install --upgrade pip
	pip install --upgrade -e ".[dev]"

# Security
security-audit: ## Run comprehensive security audit
	bandit -r gwernpy -c pyproject.toml
	safety check
	pip-audit

# Database and cache management (for future use)
cache-clear: ## Clear application caches
	@echo "Clearing caches..."
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	find . -type d -name __pycache__ -exec rm -rf {} +
	@echo "✅ Caches cleared"

# Continuous Integration helpers
ci-install: ## Install dependencies for CI
	pip install -e ".[dev]"

ci-test: ## Run tests in CI environment
	pytest --cov=gwernpy --cov-report=xml --cov-fail-under=90

ci-quality: ## Run quality checks in CI
	black --check gwernpy tests
	isort --check-only gwernpy tests
	flake8 gwernpy tests
	mypy gwernpy
	bandit -r gwernpy -c pyproject.toml

# Development workflow shortcuts
dev: install-dev pre-commit ## Quick development setup
	@echo "🚀 Development environment ready!"

quick-test: ## Quick test run (unit tests only, no coverage)
	pytest tests/unit/ -x -v

full-check: clean quality-check test coverage ## Complete check before commit
	@echo "🎉 All checks passed! Ready to commit."

# Help for specific areas
help-test: ## Show testing help
	@echo "Testing Commands:"
	@echo "  test           - Run all tests"
	@echo "  test-unit      - Run unit tests only"
	@echo "  test-integration - Run integration tests"
	@echo "  test-performance - Run performance tests"
	@echo "  test-fast      - Skip slow tests"
	@echo "  coverage       - Generate coverage report"

help-quality: ## Show code quality help
	@echo "Code Quality Commands:"
	@echo "  format         - Format code with black and isort"
	@echo "  lint           - Run flake8 linting"
	@echo "  type-check     - Run mypy type checking"
	@echo "  security-check - Run security analysis"
	@echo "  quality-check  - Run all quality checks"
