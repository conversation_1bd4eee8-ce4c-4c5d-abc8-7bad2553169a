"""
Tests for the parser modules.
"""
import unittest
import os
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

from gwernpy.parser.markdown import MarkdownParser, MarkdownParserError
from gwernpy.parser.frontmatter import <PERSON><PERSON>atterPars<PERSON>, FrontMatterError
from gwernpy.parser.orgmode import OrgModeParser


class TestFrontMatterParser(unittest.TestCase):
    """Test cases for the front matter parser."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parser = FrontMatterParser()
    
    def test_extract_empty(self):
        """Test extracting front matter from empty content."""
        metadata, content = self.parser.extract("")
        self.assertEqual(metadata, {})
        self.assertEqual(content, "")
    
    def test_extract_no_frontmatter(self):
        """Test extracting front matter from content without front matter."""
        test_content = "# Hello World\n\nThis is a test."
        metadata, content = self.parser.extract(test_content)
        self.assertEqual(metadata, {})
        self.assertEqual(content, test_content)
    
    def test_extract_with_frontmatter(self):
        """Test extracting front matter from content with front matter."""
        test_content = "---\ntitle: Test\ndate: 2023-01-01\n---\n# Hello World\n\nThis is a test."
        expected_metadata = {"title": "Test", "date": "2023-01-01"}
        expected_content = "# Hello World\n\nThis is a test."
        
        metadata, content = self.parser.extract(test_content)
        self.assertEqual(metadata, expected_metadata)
        self.assertEqual(content, expected_content)
    
    def test_extract_with_required_fields(self):
        """Test extracting front matter with required fields."""
        parser = FrontMatterParser(required_fields=["title", "date"])
        test_content = "---\ntitle: Test\ndate: 2023-01-01\n---\n# Content"
        
        metadata, content = parser.extract(test_content)
        self.assertEqual(metadata["title"], "Test")
        self.assertEqual(metadata["date"], "2023-01-01")
    
    def test_extract_with_missing_required_fields(self):
        """Test extracting front matter with missing required fields."""
        parser = FrontMatterParser(required_fields=["title", "date", "author"])
        test_content = "---\ntitle: Test\ndate: 2023-01-01\n---\n# Content"
        
        with self.assertRaises(FrontMatterError):
            parser.extract(test_content)
    
    def test_extract_with_default_values(self):
        """Test extracting front matter with default values."""
        parser = FrontMatterParser(
            default_values={"author": "Anonymous", "draft": True}
        )
        test_content = "---\ntitle: Test\ndate: 2023-01-01\n---\n# Content"
        
        metadata, content = parser.extract(test_content)
        self.assertEqual(metadata["title"], "Test")
        self.assertEqual(metadata["date"], "2023-01-01")
        self.assertEqual(metadata["author"], "Anonymous")
        self.assertEqual(metadata["draft"], True)
    
    def test_extract_with_callable_default(self):
        """Test extracting front matter with callable default values."""
        def get_author():
            return "Generated Author"
            
        parser = FrontMatterParser(
            default_values={"author": get_author}
        )
        test_content = "---\ntitle: Test\n---\n# Content"
        
        metadata, content = parser.extract(test_content)
        self.assertEqual(metadata["author"], "Generated Author")


class TestMarkdownParser(unittest.TestCase):
    """Test cases for the Markdown parser."""
    
    @patch('gwernpy.parser.markdown.markdown_it')
    def setUp(self, mock_markdown_it):
        """Set up test fixtures with mocked markdown-it."""
        # Mock the markdown_it module
        self.mock_md = MagicMock()
        mock_markdown_it.MarkdownIt.return_value = self.mock_md
        
        # Create the parser
        self.parser = MarkdownParser()
    
    def test_parse_empty(self):
        """Test parsing empty content."""
        metadata, content = self.parser.parse("")
        self.assertEqual(metadata, {})
        self.assertEqual(content, "")
    
    def test_parse_with_frontmatter(self):
        """Test parsing content with front matter."""
        test_content = "---\ntitle: Test\ndate: 2023-01-01\n---\n# Hello World\n\nThis is a test."
        expected_metadata = {"title": "Test", "date": "2023-01-01"}
        expected_content = "# Hello World\n\nThis is a test."
        
        metadata, content = self.parser.parse(test_content)
        self.assertEqual(metadata, expected_metadata)
        self.assertEqual(content, expected_content)
    
    def test_render_html(self):
        """Test rendering Markdown to HTML."""
        test_content = "# Hello World\n\nThis is a test."
        self.mock_md.render.return_value = "<h1>Hello World</h1>\n<p>This is a test.</p>"
        
        html = self.parser.render_html(test_content, {})
        self.assertEqual(html, "<h1>Hello World</h1>\n<p>This is a test.</p>")
        self.mock_md.render.assert_called_once_with(test_content)
    
    def test_render_html_with_frontmatter(self):
        """Test rendering Markdown with front matter to HTML."""
        test_content = "---\ntitle: Test\n---\n# Hello World"
        self.mock_md.render.return_value = "<h1>Hello World</h1>"
        
        html = self.parser.render_html(test_content)
        self.assertEqual(html, "<h1>Hello World</h1>")
        self.mock_md.render.assert_called_once_with("# Hello World")
    
    def test_parse_file(self):
        """Test parsing a Markdown file."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.md') as temp:
            temp.write("---\ntitle: Test File\n---\n# File Content")
            temp_path = temp.name
        
        try:
            metadata, content = self.parser.parse_file(temp_path)
            self.assertEqual(metadata, {"title": "Test File"})
            self.assertEqual(content, "# File Content")
        finally:
            # Clean up
            os.unlink(temp_path)
    
    def test_parse_file_not_found(self):
        """Test parsing a non-existent file."""
        with self.assertRaises(FileNotFoundError):
            self.parser.parse_file("non_existent_file.md")


class TestOrgModeParser(unittest.TestCase):
    """Test cases for the org-mode parser."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parser = OrgModeParser()
    
    def test_parse_empty(self):
        """Test parsing empty content."""
        metadata, content = self.parser.parse("")
        self.assertEqual(metadata, {})
        self.assertEqual(content, "")
    
    def test_parse_metadata(self):
        """Test parsing content with metadata."""
        # TODO: Implement test once parser is implemented
        pass


if __name__ == "__main__":
    unittest.main()
