"""
Front matter parser for gwern-python.

This module handles extraction and validation of front matter metadata
from Markdown and other content files.
"""
from typing import Dict, Any, Tuple, Optional, List
import re
import yaml
from datetime import datetime


class FrontMatterError(Exception):
    """Exception raised for errors in front matter parsing."""
    pass


class FrontMatterParser:
    """Parser for YAML front matter in content files."""
    
    # Regular expression for extracting front matter
    FRONT_MATTER_PATTERN = re.compile(r"^---\s*\n(.*?)\n---\s*\n", re.DOTALL)
    
    def __init__(self, required_fields: Optional[List[str]] = None, 
                 default_values: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the front matter parser.
        
        Args:
            required_fields: Optional list of fields that must be present
            default_values: Optional dictionary of default values for missing fields
        """
        self.required_fields = required_fields or []
        self.default_values = default_values or {}
    
    def extract(self, content: str) -> Tuple[Dict[str, Any], str]:
        """
        Extract front matter from content.
        
        Args:
            content: Raw content with optional front matter
            
        Returns:
            Tuple containing (metadata dict, content without front matter)
        """
        match = self.FRONT_MATTER_PATTERN.match(content)
        
        if not match:
            # No front matter found, return empty metadata and original content
            metadata = self._apply_defaults({})
            return metadata, content
        
        # Extract the YAML content
        yaml_content = match.group(1)
        remaining_content = content[match.end():]
        
        try:
            metadata = yaml.safe_load(yaml_content) or {}
        except yaml.YAMLError as e:
            raise FrontMatterError(f"Invalid YAML in front matter: {str(e)}") from e
        
        # Apply defaults and validate
        metadata = self._apply_defaults(metadata)
        self._validate(metadata)
        
        return metadata, remaining_content
    
    def _apply_defaults(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply default values to metadata.
        
        Args:
            metadata: Extracted metadata
            
        Returns:
            Metadata with defaults applied
        """
        result = metadata.copy()
        
        # Apply defaults for missing fields
        for key, value in self.default_values.items():
            if key not in result:
                # If the default is a callable, call it to get the value
                if callable(value):
                    result[key] = value()
                else:
                    result[key] = value
        
        return result
    
    def _validate(self, metadata: Dict[str, Any]) -> None:
        """
        Validate metadata against required fields.
        
        Args:
            metadata: Metadata to validate
            
        Raises:
            FrontMatterError: If required fields are missing
        """
        missing_fields = [field for field in self.required_fields if field not in metadata]
        
        if missing_fields:
            raise FrontMatterError(f"Missing required front matter fields: {', '.join(missing_fields)}")


# Common default value functions
def current_date() -> str:
    """Return the current date in ISO format."""
    return datetime.now().date().isoformat()


def current_datetime() -> str:
    """Return the current datetime in ISO format."""
    return datetime.now().isoformat()
