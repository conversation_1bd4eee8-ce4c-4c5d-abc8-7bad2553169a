"""
Math extension for Markdown parser.

This module provides support for LaTeX math expressions in Markdown content.
"""
import re
from typing import Dict, Any, Optional, List, Callable


class MathExtension:
    """Extension for handling LaTeX math expressions in Markdown."""
    
    # Regular expressions for inline and block math
    INLINE_MATH_PATTERN = re.compile(r'(?<!\\\$)\$([^\$]+?)\$(?!\$)')
    BLOCK_MATH_PATTERN = re.compile(r'(?<!\\\$)\$\$([\s\S]+?)\$\$(?!\$)')
    
    def __init__(self, renderer: Optional[Callable[[str, bool], str]] = None) -> None:
        """
        Initialize the math extension.
        
        Args:
            renderer: Optional function to render math expressions
                     Takes (expression, is_block) and returns HTML
        """
        self.renderer = renderer or self._default_renderer
    
    def process(self, content: str) -> str:
        """
        Process math expressions in content.
        
        Args:
            content: Markdown content with math expressions
            
        Returns:
            Content with math expressions processed
        """
        # Process block math first (to avoid conflicts with inline math)
        content = self.BLOCK_MATH_PATTERN.sub(
            lambda m: self.renderer(m.group(1), True),
            content
        )
        
        # Process inline math
        content = self.INLINE_MATH_PATTERN.sub(
            lambda m: self.renderer(m.group(1), False),
            content
        )
        
        return content
    
    def _default_renderer(self, expression: str, is_block: bool) -> str:
        """
        Default renderer for math expressions.
        
        Args:
            expression: Math expression to render
            is_block: Whether this is a block-level expression
            
        Returns:
            HTML representation of the math expression
        """
        if is_block:
            return f'<div class="math-block">\\[{expression}\\]</div>'
        else:
            return f'<span class="math-inline">\\({expression}\\)</span>'


def create_math_plugin(md):
    """
    Create a math plugin for markdown-it.
    
    Args:
        md: markdown-it instance
        
    Returns:
        Plugin function
    """
    def math_plugin(md):
        # Implementation would depend on markdown-it's plugin system
        # This is a placeholder
        pass
    
    return math_plugin
