<title>Gwern.net Search</title>
<style id="search-styles">
html {
	font-size: 18px;
}
body {
	margin: 0;
	padding: 0.5em;
	background-color: #fff;
	font-family: sans-serif;
	pointer-events: none;
	display: flex;
	align-items: center;
}
main {
	flex: 1 0 auto;
}
#search-where-selector label,
.searchform {
	pointer-events: auto;
}
.search-where-form {
	margin: 0;
}
#search-where-selector {
	margin: 0.625em -1px 0 -1px;
	border: 1px solid #ccc;
	padding: 0.375em 0.25em 0.625em 0.25em;
}
#search-where-selector legend {
	padding: 0 0.25em;
	color: #777;
}
#search-where-selector label {
	padding: 0.25em;
}
.searchform {
	display: flex;
	margin: 0;
}
.searchform input.search {
	appearance: none;
	border: none;
	background-color: #fff;
	color: #000;
	outline: 1px solid #ccc;
	flex: 1 1 auto;
	height: 2em;
	padding: 0.25em 0.5em;
	font-size: inherit;
}
.searchform:focus-within input.search {
	outline: 2px solid #ccc;
}
.searchform input.search:focus {
	outline: 2px solid #777;
}
.searchform input.search::placeholder {
	color: #777;
}
.searchform button {
	appearance: none;
	border: none;
	background-color: #e4e4e4;
	height: 2em;
	padding: 0.25em 0.5em;
	flex: 0 0 5em;
	margin: 0 0 0 0.5em;
	outline: 1px solid #ccc;
	cursor: pointer;
	font-size: inherit;
}
.searchform:focus-within button {
	outline: 2px solid #ccc;
}
.searchform button:hover {
	background-color: #777;
	color: #fff;
}
.searchform button:hover,
.searchform button:focus {
	outline: 2px solid #777;
}
</style>
<style id="search-styles-dark" media="all and (prefers-color-scheme: dark)">
body {
	background-color: #161616;
}
#search-where-selector {
	color: #f1f1f1;
	border: 1px solid #5c5c5c;
}
#search-where-selector legend {
	color: #a6a6a6;
}
.searchform input.search {
	background-color: #161616;
	color: #f1f1f1;
	outline: 1px solid #5c5c5c;
}
.searchform:focus-within input.search {
	outline: 2px solid #5c5c5c;
}
.searchform input.search:focus {
	outline: 2px solid #a6a6a6;
}
.searchform input.search::placeholder {
	color: #a6a6a6;
}
.searchform button {
	background-color: #404040;
	outline: 1px solid #5c5c5c;
	color: #f1f1f1;
}
.searchform:focus-within button {
	outline: 2px solid #5c5c5c;
}
.searchform button:hover {
	background-color: #a6a6a6;
	color: #000;
}
.searchform button:hover,
.searchform button:focus {
	outline: 2px solid #a6a6a6;
}
</style>
<main>
	<form action="https://www.google.com/search" class="searchform" method="get" name="searchform" target="_blank">
		<input autocomplete="on" class="form-control search" placeholder="Gwern.net search" required="required" type="text">
		<input class="query" name="q" type="hidden">
		<button class="button" type="submit">Search</button>
	</form>
	<fieldset id="search-where-selector">
		<legend>Search in:</legend>
		<label for="search-all"><input type="radio" id="search-all" name="search-where" value="site:gwern.net" checked /> All</label>
		<label for="search-essays"><input type="radio" id="search-essays" name="search-where" value="site:gwern.net -site:gwern.net/doc/" /> Essays only</label>
		<label for="search-docs"><input type="radio" id="search-docs" name="search-where" value="site:gwern.net/doc/" /> Docs only</label>
	</fieldset>
</main>
