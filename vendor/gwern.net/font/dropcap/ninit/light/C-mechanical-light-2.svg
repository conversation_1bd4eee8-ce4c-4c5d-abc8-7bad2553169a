<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="140 53 1266 1374"><!--oldViewBox="0 0 1530 1532"--><style>.B{fill:#f0f0f0}.C{fill:#fff}.D{fill:#d7d7d7}.E{fill:#d8d7d8}.F{fill:#282828}.G{fill:#474748}.H{fill:#c9c8c9}.I{fill:#8c8b8b}.J{fill:#eaeaea}.K{fill:#373737}.L{fill:#838383}.M{fill:#a7a6a6}.N{fill:#d1cfd0}.O{fill:#141415}.P{fill:#454545}.Q{fill:#1e1d1e}.R{fill:#bfbebe}.S{fill:#6a6a6a}.T{fill:#010000}.U{fill:#262526}.V{fill:#616061}.W{fill:#b0afaf}.X{fill:#5a595a}.Y{fill:#919091}</style><path d="M310 240c2-1 2-1 4 0l1 2-1 2h-2c-2 0-2 0-3-2l1-2zm714 342l2-1c2 2 2 2 2 5l-2 2c-2 0-2-1-4-2v-3l2-1zM259 971c1-1 1-1 2-1 4 0 7 2 8 5 2 4 1 8 1 12 0 11-1 24 2 34 4 16 24 14 33 24 3 3 5 7 6 11 1 9 0 18 0 27l-1 44c-2-3-1-9-2-13l-3-41c-1-6-1-13-2-18-1-2-3-4-5-5v-1c-9-4-18-6-26-13-9-7-7-18-8-29l-2-21-1-1c-1 6 0 13 0 19l-2 41c0 9 0 18-1 28l-3-65-1-20c0-4-1-8 0-11s2-5 5-6zm221-733c-5-10-17-10-27-12-4-2-8-4-11-6h29c2-1 2-3 3-5-3-3-25-7-31-9 10 1 21 3 31 3 4 0 7-3 11-3 15 2 30 6 44 8v-12l-28-7c-6-1-5-2-8-7-4 0-6-1-9-3-1-2-1-4-1-6s1-4 3-5c2 0 4-1 6 0l3 1 3-4c4-3 24 3 30 4l1-38c0-4-1-9 3-12 3-2 8-1 12-1l4-68c3 3 3 29 3 35 0 3 1 27 2 29 1 1 4 2 5 4 1 1 2 3 2 4 2 9 1 19 1 27v59c5 0 9 1 13 2v-76-1c6-7 13-12 20-18 5-4 10-10 16-14 3-2 8-4 12-4 7 0 14 2 19 7 8 8 8 18 10 28 0 4 2 8 2 12 1 6 0 12 0 19v36c5-2 9-5 14-8l2-1c1-1 0-5 0-7v-23c0-9-2-20 4-27 4-6 12-7 19-8v-24c0-2 1-4 3-6 1-1 3-1 4-1 11-2 24 0 35 1l47 3c-7 1-15 1-23 1l-57 3v24c7 0 13 3 20 4h13l31 3v1c-13 1-25 0-38 0-5 0-10 2-15 1-2 0-2 0-3 1 0 3 0 6-2 8-4 6-16 6-22 7l-1 21v9l21-9 1-1c-1-2-2-4-2-6 0-4 2-9 5-12 4-4 9-2 14-4 5-3 8-8 13-10 6-3 14-2 21-2l24 1v1l-30 2c0 2 1 4-1 6-1 4-4 7-7 10 10-2 20-5 31-8l52-10c58-8 121-6 177 9 50 13 96 36 137 66 32 22 61 49 86 79 62 76 100 174 116 270l9 73 1 20c0 1 0 5 1 6 1 2 6 5 7 7 9 8 19 16 26 25l-19-13c-3-2-7-5-11-6-5-2-73-2-78 0-5 1-8 2-13 1-12-4-164-3-166-1l-26 19 20-23c2-2 7-6 8-9 1-1 0-6 0-8l-4-65c-3-26-7-51-16-75-6-16-14-31-27-42-9-7-20-11-31-10s-20 7-27 15c-8 11-12 22-14 35-2 9-2 18-2 27v34c0 17-1 34 0 50l6 5-6 5v72c0 12-1 24 1 36 0 1 0 1-1 2v9 20 73l117-2 278 1 59 1c14 1 29 0 43 2-11 2-23 1-35 2l-87 3-2 36c3 2 5-1 7 2l-1 2h-6c-2 4-1 15-2 19l-8 136-1 47c0 8 1 17 0 24l-5-107c-1 12-1 23-1 34l-2 69c0 12 0 24-1 35l-7-151c-3-1-4-1-6-1-2 3-2 7-2 10-4 19-9 37-14 56-13 43-34 85-59 121-14 21-29 40-47 57-2 2-23 20-23 22-1 4-1 10-1 15-2 18-3 36-4 55 0 8 2 19 0 27-1 3-3 6-5 9-1 1-2 2-4 3-2 2-5 3-8 3-10 3-20 0-28-5-7-5-18-13-19-22-2-11-1-23-1-35l-10 5-2 68c0 11 0 23-2 34l-3-84h-1l-5 99-5-107c-19 7-38 15-58 20-19 6-39 9-58 14 0 4 1 11-2 14-1 1-3 1-4 2-1-1-1-1-2-1-1-3 0-11 0-14-27 2-54 4-80 2 0 7-5 88-6 91l-5-93-73-13v20c0 8 1 17-4 23-6 6-19 6-27 7-4 1-8 2-12 2-3-1-6-2-8-4l-3-6c0-1 0-1-1-2-16 0-33-3-46-13v-39l26-2-80-34-1 55v18c0 3 0 6-1 8v1l-2-130v49 61c0 6 1 15-1 21-1-4-1-12-1-17l-1-32c0-5 1-24-1-27-1 3-1 9-1 12l-2 27c0 3 0 8-1 11l-2 2-2 40c-1 7-1 14-2 20-2-15-2-31-2-46 0-4 0-13-2-16l-3 1c-3 2-2 10-2 14l-1 27v14c-1 4-4 7-7 9-2 2-4 2-5 5-2-1-1 0-2-2-12 0-18-2-26-11-9-8-8-15-8-27v-28l-1-1c-2-1-4-3-5-5-2-4-3-10-3-15l-3-15c-1-3-1-6-2-8h-2c-1 3 0 6-2 8 0-2 1-8 0-10-1-1-2 0-1-2l2-1c1-6 0-15-1-21-3-1-8-2-10-4-1-2-1-7-2-8 0 0-3-1-3-2-3-1-6-3-7-6-1-1 0-4 0-5l1-2v-1c-4-5-6-12-8-18h-33l-3 134-1-1-7-138c-7-6-12-11-13-20-1-10-1-21-1-31 0-4 1-8 0-12-2-1-3 0-5 0-2 4-1 11-1 16l-2 36c0 8 0 16-2 24l-4-77c-12-2-24-6-36-11-7-3-14-5-21-9-11-5-19-12-28-19l-5 219-1 32c0 2 0 8-1 10-1-2-1-8-1-11l-1-33-7-147c-2-23-1-45-4-67-3 14-2 28-3 43l-3 112v30c0 5 0 11-2 17l-5-192-2-62c-1-10 0-21-2-31-7-27-4-63 9-88-16-1-42 1-54-12-4-5-4-11-4-17 0-1-1-4-1-4 0-1-9-3-10-3l-12-6c-38-27-54-79-50-123 2-29 13-59 31-81 3-4 7-8 10-11 1-1 4-3 4-4 1-3-1-10 1-14 6-4 13-8 20-11 4-2 11-3 14-6 1-1 1-4 1-5 4-2 12-1 17 0l43 4c2 1 9 2 12 1l1-1 2 1 4 1c2-2 2-6 2-8s0-5-1-7c-1-1-3 0-5 0-2 2-1 7-2 11v-13c-9 1-14 0-21-6-4-4-9-8-9-14-1-4 0-8 3-11 1-2 3-2 5-3v-1c-15-1-30 0-45 0-5 0-15 1-19-1-3-2-7-1-10-1h-17c-2 0-5 0-6-1s-1-2-2-3c0-2 0-4 2-5 4-4 13-3 18-5s8-5 13-5h2c4 0 7 3 11 4 20 2 39-2 59 2 2 0 2 0 3-1 2-1 2-3 3-5l-2-2c-1 0-2 0-3 1-6 1-15 0-21 0h-45l-20-1c-8-1-18 1-25-1-2-3-2-4-2-8l-138-7c4-3 59-3 69-4 2 0 9 1 11-1 2 0 4-3 5-4l12-11c2-3 9-7 10-10h-2v-1c1-1 2-2 4-2 3 0 7-3 9-5v-1l-3-1 1-2c2 0 4 1 6 0 2 0 7-7 9-8l2-2h-10v-1l50-2c-3-7-8-11-11-17-3-5-4-11-6-16-10-3-16-7-21-16-8-13-11-37-7-52 2-7 6-11 11-15 4-31 4-62 19-90 2-3 4-7 6-9s5-4 6-7c2-2 2-5 3-8 2-5 5-10 9-13 9-6 19-9 29-7 15 2 24 13 32 23 7-17 18-31 36-39 8-3 15-2 23 0l23 4c0-13-5-31 5-40 1-2 5-4 7-4 1 0 2 1 3 1 5 4 35 7 41 5 5-1 8-2 13 0h3z" class="T"></path><path d="M732 919h1c0 4 0 9-1 14v-14z" class="O"></path><path d="M637 525c2 0 4-1 6 0 0 1 0 2-1 2-2 1-2 1-5 1v-3z" class="G"></path><path d="M398 872h0c2-1 4-1 6 0l-2 2-3 2-1-1v-3z" class="P"></path><path d="M1148 580l13-1-3 3h-8l-2-2z" class="E"></path><path d="M760 631v7h1v1c1 2 1 7 0 9l-2-1c0-5 0-11 1-16z" class="O"></path><path d="M1034 1318h5c2 1 2 3 5 4l-2 1c-4-1-5-3-8-5z" class="K"></path><path d="M661 932l-1 5c-3 0-4-1-6-2 0-2 0-1 1-2l6-1z" class="J"></path><path d="M655 905c2 0 6 0 8 1l-1 2h-7l-1-1 1-2z" class="B"></path><path d="M587 731h1v5l-3 3h-1v-3c0-2 1-3 3-5z" class="W"></path><path d="M563 715c1-2 0-6 1-9h1l1 2v9l-1 1h-2v-3z" class="Y"></path><path d="M232 552l3-2c1 1 1 1 3 2 0 1-1 1-1 2v3h-3c-1-1-2-3-2-5z" class="F"></path><path d="M234 621h2c4-1 23-1 25 1h-17c-4 0-7 1-10-1z" class="V"></path><path d="M637 525v3c3 0 3 0 5-1 0 3-1 4-2 6-3 0-3 1-5-1 1-2 1-4 2-7z" class="P"></path><path d="M1261 432c4-2 3 0 7 0l2 1c-2 2-3 3-6 4l-3-5z" class="I"></path><path d="M482 222c0 1-1 2-1 3-2 0-3 0-5-2 0-3 0-5 1-8 0 2 0 2 2 3 1 1 1 2 1 3l2 1z" class="E"></path><path d="M1058 1322l-1 4-8 2v-1c-1-2-1-2 1-4v1c3 0 5-1 8-2z" class="G"></path><path d="M639 906l1-1c2 0 8 0 11 1l-1 2c-3 0-9 0-11-1v-1z" class="J"></path><path d="M532 622c2 0 3-1 4-2l3 4c-1 1-1 3-1 4v1c-3-2-4-4-6-7z" class="I"></path><path d="M418 641c2-1 3-1 5 0 0 2-1 4-2 6h-2c-2-2-2-2-3-5l2-1z" class="L"></path><path d="M738 504l12-1-2 7-1 1-2-4c-1-2-5-2-7-3z" class="M"></path><path d="M640 1066l6 7h0l-1 2 1 1-3-1-3 1v-10z" class="O"></path><path d="M496 1156c3 1 6 3 8 6l1 2c-1 1-4 1-5 1l-2-1 2-1-4-7z" class="U"></path><path d="M440 1023l1 3c-2 3-6 4-9 6 1-6 3-6 8-9z" class="D"></path><path d="M409 552l2 3-8 14c0-1 0-2 1-3l1-2-2-1 6-11z" class="Q"></path><path d="M593 606h0c1 1 1 1 1 2 2 4 0 7-1 11l-2 1c0-5 0-9 2-14z" class="D"></path><path d="M409 552l5-13v1 2l-1 4h1v-2l1-1c0-1 1-2 1-4l1 1-6 15-2-3z" class="O"></path><path d="M860 1352h1c2 1 5 1 7 3v2l-2 1c-3 0-4 0-6-1v-5z" class="G"></path><path d="M800 847c1 0 3 1 5 1l-2 3c-2 2-5 6-6 6-1-3 2-8 3-10z" class="D"></path><path d="M631 520c-2 0-3 0-5-1-1-3 0-4 1-7v4c1 0 2 0 3-1-2-1-1-1-2-2l4-2c0 1 1 2 1 3 0 2 0 1-1 2-1 2-1 2-1 4z" class="M"></path><path d="M615 1321l1 1v2c1 3 0 6-2 9l-1-3-1-1v2l-1-1c0-4 2-6 4-9zm546-742h11l-3 1c1 0 1 0 3 1h6l-19 1h-1l3-3z" class="D"></path><path d="M761 752l3-1 2 2c0 2 0 4-2 7h-2l-1-1c-1-2-1-4 0-7z" class="O"></path><path d="M541 602l-1 8-6 5h-1c1-2 1-2 1-4l7-9z" class="F"></path><path d="M585 352l4-4v-1-2l3 15-7-8z" class="H"></path><path d="M425 806c1 0 2 0 2 1 0 3-1 9-2 11h-2v-9l2-2v-1z" class="P"></path><path d="M1139 936h-20c4-3 9-2 13-3 3 1 9 0 11 2h-1c-1 0-2 0-3 1z" class="G"></path><path d="M494 670c2 0 3 0 5 1v6l-1 1-4-1c-1-2 0-5 0-7z" class="K"></path><path d="M616 195c4-2 8-6 12-6 2 2 1 4 1 7l-1 1v-5h-1c-4 3-6 4-11 3z" class="M"></path><path d="M1106 1000c-2-1-3-2-5-3 0-3 1-8 2-10h2v2l2 1h0c-1 3-3 6-2 8l1 2z" class="Q"></path><path d="M565 692l1 16-1-2h-1c-1 3 0 7-1 9l-1-19 3-4z" class="I"></path><path d="M228 842h1 9c1 2 0 3 0 4l-2 2c-1 0-2-1-3-1-2-1-4-3-5-5z" class="L"></path><path d="M347 652v-2c2-1 5-1 8-1-1 3-1 5-3 7-1 0-3 1-4 0v-1c0-1-1-2-1-3z" class="D"></path><path d="M494 697c2-1 3 0 5 1v2h-1v-1l-2 2 2 2c1 0 1 2 1 2-2 1-3 1-5 1-2-3-1-7 0-9zM331 511l14 2c3 0 6 0 8 2-6 0-15 0-22-1v-3z" class="L"></path><path d="M425 948l1-2c5-5 9-4 16-4-5 3-12 6-17 6z" class="G"></path><path d="M486 695l3-1 1 2v10h-4c-1-3 0-8 0-11z" class="W"></path><path d="M484 686v-4c2-2 3-1 6-1 1 2 1 5 1 7-2 1-2 1-4 0l-3-1v-1z" class="N"></path><path d="M654 935c2 1 3 2 6 2v7l-5 2-1-1v-1-9z" class="E"></path><path d="M711 870c0 3 0 6 1 9 2 4-1 11 2 15v1c-2 0-2 0-4-1-1-8 1-16 1-24z" class="F"></path><path d="M593 635c2 2 3 5 3 7 0 3-2 4-4 5l-2 1h0c0-4 1-9 3-13z" class="M"></path><path d="M832 1066c2 0 4-1 7 1 1 1 3 2 3 4h-2c-3-1-9 0-11-2 0-2 1-2 3-3z" class="R"></path><path d="M656 265c4-3 9-7 13-8l2 3-2 1-1 1c-1 1 0 1-1 2h-3-1c-2 0-3 0-4 1h-1-2z" class="L"></path><path d="M610 1068c-1-7 0-16 0-23l1-1 1 1v15c0 2-1 5 0 7l-2 1z" class="K"></path><path d="M244 920c4-1 12 3 17 4h1v1c-1 1-3 1-5 1-5 0-10-2-13-6z" class="I"></path><path d="M678 275v-2l-2-2c-1-4-4-8-6-12l15 16h-7z" class="D"></path><path d="M751 147c0 2 1 4-1 6v-1l-4 4-4-9h9z" class="H"></path><path d="M280 507c1 1 1 2 2 2-3 5-9 8-15 9l-5 1c2-1 5-3 8-4 4-2 7-5 10-8zm-51 324c3 1 6 1 9 1-1 2-1 2 0 4-1 1-4 1-6 2v1c3 0 3 0 5 1l-1 1c-3 0-4 0-7-2v-1c2 0 2-1 3-2 0-1-1-1-2-2h0l-1-3z" class="P"></path><path d="M656 265h2 1c1-1 2-1 4-1h1 3c-3 3-8 6-12 8h-1v-5l2-2z" class="I"></path><path d="M384 465c4 1 11 3 12 7l1 1c-2 1-2 2-4 2h-1l-1-1c-1-3-3-4-6-5l-1-4z" class="P"></path><path d="M1009 1332c2 0 2-1 4 0v8l-6 3c0-2-1-3-1-4 0-2 1-4 3-5l1-1-1-1z" class="K"></path><path d="M553 1076c2-1 4-2 5-4l1-1 2 1c4 0 8-1 11-4 0 3-1 4-3 5-4 2-11 3-16 3z" class="G"></path><path d="M1325 668l15-1 1 1-1 1-11 1c-6 0-12 1-17-1 4-1 9-1 13-1z" class="Y"></path><path d="M562 696c-1-7-1-14-6-20l-4-4c6 4 10 8 12 15l1 5-3 4z" class="S"></path><path d="M416 819l3-2h1v-1c0-4 1-10 0-13v-1h1 3l-1 2 2 2v1l-2 2v9c-2 1-5 1-7 1z" class="V"></path><path d="M1306 601c2-1 5-1 7-1 7 0 15-1 21 0l1 3c-1-1-1-1-3 0l-5-1-21-1z" class="F"></path><path d="M829 1142c2-3 1-9 3-11v8l-2 26-1-23z" class="I"></path><path d="M708 1246l4 3h-4l-1 4c2 2 5 1 8 2-3 0-7 1-9-1l-1 1c-2 0-2 0-4-1-1-2 0-4 1-7h5l1-1z" class="V"></path><path d="M896 380l-3-1c3-3 15-3 20-4-2 3-3 4-6 5s-9 1-11 0z" class="E"></path><path d="M451 504c-2 4-2 10-4 13-1 0-2 0-3-1l2-15 1 3h4z" class="M"></path><path d="M827 1280c1-8-2-17-1-25 2 0 3 1 4 2-1 2-1 5-1 7v15l-2 1z" class="V"></path><path d="M715 1255c-3-1-6 0-8-2l1-4h4l13 6h-10z" class="L"></path><path d="M299 696c-1 2-1 4 0 6 2 2 6 4 8 5 0 2 1 3 0 5l-9-6c0-1-1-2-1-3-1-1-1-2-1-4l1-2 2-1z" class="G"></path><path d="M731 156h2v1c-2 4-4 9-8 11l-2-2v-2c2-3 6-6 8-8z" class="J"></path><path d="M1107 1249c1 2 1 5 1 8v1c-1-1-2-3-4-3s-4 2-6 3c-1-2-2-2-3-3 4-2 7-4 12-6z" class="H"></path><path d="M734 1213c0 4 4 8 7 11 1 1 2 2 2 3l-4 2c-2-2-6-10-8-13l3-3z" class="K"></path><path d="M1285 518l-3-1-2-3v-1c4 0 14-2 16 0-2 3-6 3-8 5h-3zM601 722v4c-4 3-8 8-12 11 1-4 3-12 6-15l4 4 1-1 1-3z" class="H"></path><path d="M1307 940l18 2 2 3c-5-1-10-1-14-1-3 1-5 1-7 0l1-4z" class="R"></path><path d="M261 365h1l1 1v3c0 4 0 7-3 10-1 0-1 0-3-1 0-5 1-9 4-13z" class="C"></path><path d="M774 1279c4-1 6 1 10 1 1 7 1 13 1 20v10c-3-2 0-18-2-22v-2-2c-1-1-2-1-3-1-2 0-2 1-3 2 0-2-1-4-3-6z" class="H"></path><path d="M1070 1316c4 0 5-1 8-3v1c-6 5-14 9-21 12l1-4c1-1 3-1 4-2l-1-1h-5 2c1 0 2-1 3-1 3 0 5-1 8-1l1-1z" class="K"></path><path d="M771 1014l9-1v8c-3 0-7 1-9-1v-6z" class="P"></path><path d="M509 929c-7-4-14-8-20-13 5 2 13 7 19 7h1c1 0 2 1 3 2h0c-1 1 0 1-2 0-1 2-1 2-1 4z" class="R"></path><path d="M552 460l2 1v1c3 3 10 1 14 2v3c-2 2-12 1-15 1-1-2-1-5-1-8z" class="F"></path><path d="M1132 933h22c4 0 9-1 14-1v1c-3 2-7 2-9 2-7 0-14 1-20 1 1-1 2-1 3-1h1c-2-2-8-1-11-2zM353 426c3 0 6 0 10-1-4 8-12 10-19 12 3-3 8-5 9-10v-1z" class="K"></path><path d="M1267 516v-1c1-3 3-6 1-9v-1c-1-1-1-3 0-5h3c2 3 3 6 4 10 1 2 3 4 4 6-3-1-4-3-5-6l-1-2-2 2c-1 1 0 2-2 4 0 1-1 2-2 2z" class="N"></path><path d="M1283 441c2-2 4-4 6-5 2 1 3 1 4 3 2 3 3 6 4 9l-4 1c0-3 1-4-1-7l-2 1c-1-1-1-1-2-1l-2 2c-1-1-2-1-2-2l-1-1zm-540 868c2 0 5 0 8 1 4 2 7 4 12 4 0 1-1 2-1 3h-1c-7 1-13-4-18-8z" class="O"></path><path d="M551 351c2 0 6 0 9 1 1 3 2 5 2 8-1 0-3 1-5 0-3-1-5-6-6-9z" class="B"></path><path d="M665 249h1c1 2 1 3 1 5l-12 7-2 1v-2c0-1 1-4 1-4 1-1 4-2 5-2 2-2 4-3 6-5h0z" class="I"></path><path d="M1055 1140h3l3 1v14l4 1c-2 0-4 0-7 1h-1c1-5-2-10-1-15l1-1h-2v-1z" class="F"></path><path d="M957 545h3l4 3 6 5v1l-1 5h-2c-4-4-8-8-10-14z" class="H"></path><path d="M883 1160l2-1 5 1h0c1 1 3 1 4 2 0 2 2 5 3 7-5-2-13-3-17-7 2 0 3 0 4-1l-1-1z" class="D"></path><path d="M1254 1025l2-9c6 0 12 1 17 2l1 2c-5 2-10-1-15 0l-1 1c0 1-1 2-2 3l-2 1z" class="W"></path><path d="M553 422h1c2 1 3 1 5 1 3-2 6-3 7-6v-2c1-1 1-2 1-3l2-1c2 3 2 9 2 12h-15c-1 0-3 1-4 0l1-1z" class="V"></path><path d="M915 1106h3c2 3 5 7 9 10l7 8h0c-2-1-3-1-4-2s-2-2-3-2c2 2 3 4 4 6-6-5-11-13-16-20z" class="H"></path><path d="M588 1093c0-3 0-3 2-5v-2c-1-2 0-7 0-9 1 2 1 6 1 9 2 1 6-2 8-2 0 3 0 5-1 7-3 2-6 2-10 2z" class="K"></path><path d="M280 812h2l2 2c1 5 1 7-1 11l-2 1c-1-1-2-1-2-3-1-4-1-8 1-11z" class="C"></path><path d="M609 1210c0-4 2-5 5-7l1-1c2 2 2 3 2 5s0 3-1 5l-6 5c-1-3-1-5-1-7z" class="D"></path><path d="M568 860v-23c0-3 0-7 1-11l1-1c1 4 0 11 0 15 0 8 1 14 0 21-1 0-2-1-2-1z" class="N"></path><path d="M1133 615h2l-5 1 1 1-1 1-18 1-1 6c-1-3-2-6-2-9h2l22-1z" class="B"></path><path d="M261 695l1 1 1 10c-2 2-5 2-7 3l-1-1c0-3 1-6 1-9 2-2 3-3 5-4z" class="F"></path><path d="M492 556l1 1h1v-1l1 1c-4 10-11 20-18 29l-1-1c7-9 11-19 16-29z" class="V"></path><path d="M384 1047l-2-2 1-1c9-1 19 0 28 0l2 1c-3 1-7 1-10 1l-19 1z" class="M"></path><path d="M969 899c1 2 1 4 1 7v20h-18-1c2-3 8-1 12-2 1-1 3 0 5-1l1-24z" class="F"></path><path d="M794 874c2 2 2 3 2 6 1 5 1 11 1 16 1 2 1 5 1 7-2-1-2-1-3-2-2-8-2-18-1-27z" class="C"></path><path d="M1262 411c3 4 10 16 10 21l-2 1-2-1c-1-6-5-13-7-19l1-2z" class="W"></path><path d="M612 227l2 8c-3 2-5 4-8 6l-2-1c-1 0-1-1-1-1 0-5 6-9 9-12z" class="H"></path><path d="M334 1012c1 1 1 0 2 1l-2 10c-2 2-3 2-6 3l-2-1 1-1c0-2 2-10 2-10 2-1 3 0 5-2z" class="S"></path><path d="M592 694l1 11c-2 2-4 4-7 5-1-2-2-5-2-7 1-3 6-7 8-9z" class="D"></path><path d="M255 402c2-1 2 0 4 0 0 0 0 1 1 2 1 4 0 9-3 12l-2 1c-1-1-1-1-1-2-1-4-1-9 1-13z" class="B"></path><path d="M994 1324h2v5c-3 2-8 2-12 3l-1-1v-5c4-1 7-2 11-2z" class="I"></path><path d="M555 1097c2 0 6 1 8 3-1 4-2 8-5 11-2-1-3-4-4-6v-1c2-3 2-4 1-7z" class="N"></path><path d="M640 1040c1 5 1 11 2 17 1 8 3 13 9 18l-5-2h0l-6-7c-1-6-1-12-1-19 1-2 1-5 1-7zm412 212c-7 0-14 0-21-2-3-1-7-3-11-4 9 0 17 2 26 3h0l6 2-1 1h1z" class="L"></path><path d="M588 757c1 0 2-1 3-1l2 7c-2 2-4 4-6 5l-2 1h-1c-1-3 0-5 0-8 1-2 2-3 4-4z" class="M"></path><path d="M922 594l11 4h0c-1 2-1 1-1 3l-2 2c-3 0-6 0-10-1v-7l2-1z" class="E"></path><path d="M739 1229l4-2 15 14c-2-1-3-1-4 0l1 3c-6-5-11-10-16-15z" class="D"></path><path d="M1040 1103c-2-3-2-7-3-10l-4-16c3 4 4 9 6 13s4 7 6 11c-2 0-3 1-5 2h0z" class="I"></path><path d="M770 1244h2 1l1-18h1l3 22 6 5-2 1-7-5c-1-2-4-4-5-5z" class="L"></path><path d="M1190 277l7 8v1l-3 1v1c-1 1-2 1-3 2l-2-2v2l-3 1-1-2c0-3 3-4 4-7l-1-1h-1v-2c2 0 2 0 3-2z" class="H"></path><path d="M593 665c1 2 2 4 2 6 1 2-1 4-2 5-1 2-3 3-5 3-1 0-2-1-2-2-1-2-1-4-1-6l7-6c-1 4-2 7-2 10h1l2-10z" class="M"></path><path d="M424 240c8-1 14 1 21 2l15 3h-4c-9-1-17-2-26-2-3 0-7 1-10 1 1-2 2-3 4-4z" class="B"></path><path d="M1249 439c4-2 8-4 12-7l3 5-15 9c0-3 1-4 0-7z" class="Y"></path><path d="M604 565h16l-9 6-7 6v-12z" class="N"></path><path d="M568 796h0c1 1 1 2 2 3 1 5 2 20-1 24l-1-1v-4l-1-17c-1 1-3 2-4 4 0-3 3-7 5-9z" class="E"></path><path d="M588 787l2-1c2 2 3 5 4 7-2 3-5 4-8 5 0 1-1 1-2 1-1-2-1-5-1-7 1-3 3-4 5-5z" class="R"></path><path d="M699 598l-2 1-1 3-1 1c-3 2-7 1-10 1-2-1-3-2-4-3 3-3 9-3 13-4h1v-1c2 1 3 2 4 2zm239 274l1-1c1-3 0-7 2-10l2-1c1 2 3 2 4 4l-1 3h4l1 1-1 1c-1 0-2-1-3 0l2 1 1 1c-2 0-3 0-4 1h-1-7z" class="O"></path><path d="M819 1013c1 0 2-1 3-1v9h-10l-2-1v-6c1 0 1 0 2 1h1l1-2 1 1c1-1 2-1 4-1z" class="I"></path><path d="M339 1014l4-2 2 2c-2 11-2 22-2 33-1-4-2-7-2-12v-1h-1c1-2 1-4 0-7 0-3 1-9-1-13z" class="O"></path><path d="M541 602l12-9 1 1v4c-5 2-10 9-14 12l1-8z" class="P"></path><path d="M720 890c1-2 1-5 1-7 1-6 1-13 3-19v31h-10v-1c2 0 4 0 6-1v-3z" class="L"></path><path d="M397 337v4h2c2-2 2-4 1-6 0-1 0-2-1-2l1-1c4 4 9 8 10 13-2 1-4 0-6 0l-9-3h1l1-5z" class="D"></path><path d="M1264 983l7 1c3 2 6 1 9 1 6 0 11 0 17 3h-8-24l-1-1v-4z" class="N"></path><path d="M692 138h2c2-1 8-1 11 1 0 2 0 3-1 5l-12 1c-1-2 0-5 0-7z" class="C"></path><path d="M945 872l8 3c2 2 5 3 8 4 2 0 6 1 7 0l1-16v18c-2-1-4-1-6-1l-1-1c-5 0-21-1-25-5l1-2h7z" class="J"></path><path d="M545 1232l2-3c0 2 1 4 1 6l-1 29c0 5 0 11-1 16-1-5 1-12 0-16-2-6 0-12-1-18-1-3 0-10 0-14z" class="B"></path><path d="M1212 663h25c3 0 6-1 9 0v2c-10 1-24 1-34 0v-2z" class="J"></path><path d="M298 706c-5-2-14-3-18-6v-2c2-2 13-1 17-1l-1 2c0 2 0 3 1 4 0 1 1 2 1 3z" class="P"></path><path d="M269 693h4c1 3 1 8 0 11l-6 1c-2 1-1 0-3 0 0-2 0-7 2-10 0-1 2-1 3-2z" class="K"></path><path d="M713 907v-10c3 0 8-1 11 0-1 3 0 5-2 8-3 1-6 2-8 3l-1-1z" class="I"></path><path d="M741 1199l9 1c2 2 4 3 5 5l-5 1c-4 1-7 3-10 5h0-1l2-12z" class="U"></path><path d="M589 903h1c2 2 2 6 3 9-2 2-5 3-8 5-1 0 0 0-2-1 0-2-1-4-1-6 1-3 5-5 7-7zm570-321c-4 1-8 1-12 1l-28 1c-3 0-9 2-12 0l1-1 40-3 2 2h8 1z" class="N"></path><path d="M499 184l2-1v-2c5 0 12 2 17 3 1 0 4 1 5 1l-2 1-1 2c-7 0-12-2-19-1h-1l-2-1 1-2z" class="U"></path><path d="M369 517l2-10c0 2 0 3 3 4s7 1 11 3c1 1 1 2 0 4-6 0-11-1-16-1z" class="Q"></path><path d="M340 632c7-1 13 0 20 1 1 1 1 0 2 2v-1-1h8l1-2 1 2-1 2-3 1h-18c-2 0-5 0-6-1-1 0-3-2-4-3z" class="U"></path><path d="M236 409c1-3 2-5 4-7l1 37-2-1c-2-4-2-11-3-16v-13z" class="L"></path><path d="M968 540c-1-4-4-6-6-9-3-3-6-7-7-11 4 3 7 5 12 6l2 1c1 4 0 8 1 12-1 0-1 1-2 1z" class="R"></path><path d="M590 814h2c0 1 0 1 1 2 1 2 1 4 0 6-2 3-4 4-7 6l-2-1-1-2c-1-1-1-3 0-5 2-3 5-4 7-6z" class="N"></path><path d="M754 885c1 0 2 0 3 1h4l1-1c1 0 2 0 3 1-2 3-3 1-4 5v1c3 1 6 0 9 1-4 0-21 1-24-1l1-2h2 1v-2c1-2 2-2 4-3z" class="K"></path><path d="M906 588l9 3v10l-8 1c-2-3-1-11-1-14z" class="E"></path><path d="M1038 1238l-1-1-20-6c8 0 21-1 26 5 2 1 4 4 5 6-3-1-7-2-10-4z" class="I"></path><path d="M661 416l-8 8-4-16c5 2 9 3 15 4l-3 4h0z" class="N"></path><path d="M593 871h0c0 4 0 10-3 13-1 2-4 3-6 3-1-1-2-3-2-4-1-8 7-6 10-11l1-1z" class="E"></path><path d="M654 912c3-2 8-1 11-1 1 3 0 6 0 8h-1l-1 15c-1 4 0 10-2 13l-1-3v-7l1-5-1-2c2-3 1-5 1-8v-9c-1-2-5-1-7-1z" class="F"></path><path d="M465 1012h-6v-1c0-4-1-13 0-16 2 0 3 0 5 1l1 5 1 1c0 3 0 4-1 6v4z" class="H"></path><path d="M448 1034c6 1 11 3 16 3l1 3-1 1c-1 1-2 2-3 2-3 1-7 1-9-1-3-2-3-6-4-8z" class="B"></path><path d="M536 130c2 9 1 18 1 27 0 7 1 14-1 21l-1-1c-2-4-1-8-1-12 0-12 0-23 2-35z" class="E"></path><path d="M770 1289c1 3 1 7 1 10 0 7-1 14-1 21l-1 1c-1-1-2-1-2-2-1-1 0-18 0-21s0-5 2-7l1-2z" class="F"></path><path d="M333 325l10 2 5 10-11-1-4-11z" class="B"></path><path d="M342 1067c2 0 3-1 4 0l2 11c-1 1-4 0-6 0-1 0-3-1-4-2s-2-3-2-5c2-2 4-3 6-4z" class="W"></path><path d="M645 328c-1 6-4 11-7 16l-2 3c-3 0-4 0-6-2 1-1 2 0 4 0v-1l-3-2v-3c2-3 6-4 9-6 2-1 4-3 5-5z" class="L"></path><path d="M539 545c3 0 4-1 6 0 3 2 3 3 4 5l-4-2c-2 1-2 3-3 5l-1 1-4 5c-1-1-2-1-2-2-2-2-2-4-1-6 1-3 3-4 5-6z" class="V"></path><path d="M591 843l1 1 2 6-9 8h-2c-1-2-1-5-1-8 3-3 5-5 9-7z" class="N"></path><path d="M691 267h2 1c4 4 9 6 14 8-1 1-5 4-6 4-5-1-11-7-15-10l4-2z" class="D"></path><path d="M725 609l2-3c0 8 1 16 0 23 0 6-1 11-1 16v39c0 6 1 14-1 19-1-8 0-16 0-24v-51c0-6-1-13 0-19z" class="G"></path><path d="M715 160c3 0 3 0 5 1 1 3 1 4 1 7-2 2-3 3-5 4l-4 1-2-1v-6c1-3 3-4 5-6z" class="B"></path><path d="M285 449l1 4 1 1v-1l3-1c-2 6-5 12-8 17-2 0-1 0-2-1v-2l-1-1-1 2-9 8h0c7-8 12-16 16-26z" class="W"></path><path d="M542 711c1 0 1 0 2 1 1 6 1 14 1 20 0 3 0 7-1 10h-2l-1-1c0-10-1-21 1-30z" class="R"></path><path d="M280 507c8-9 14-19 19-30 2-5 3-11 6-15l1 1-3 9c-6 13-11 27-21 37-1 0-1-1-2-2z" class="G"></path><path d="M977 1339c2-1 2-1 4 0 0 3 1 7-1 9l-7 1c-1 0-2 0-3-1 0-3-1-6 0-9h7z" class="P"></path><path d="M753 184c11-9 25-10 37-15l-8 8-1-1c-3 0-3 0-6 1-2 1-5 1-7 3-5-1-10 3-15 4z" class="U"></path><path d="M1279 430c2-2 4-3 6-4l3 2c-1 3-3 4-5 5-6 4-12 8-19 11l-1 1-2-2 15-10c1 0 3-1 3-3z" class="V"></path><path d="M603 1015c1 3 0 7 0 10l-1 27c-1 6 1 15-1 21-1-6-1-13-1-18l1-35v-13c0 2 0 7 1 8h1z" class="O"></path><defs><linearGradient id="A" x1="258.655" y1="713.359" x2="272.1" y2="716.223" xlink:href="#B"><stop offset="0" stop-color="#a3a2a3"></stop><stop offset="1" stop-color="#c7c7c7"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M275 722c-8-3-15-4-24-4 10-5 19-8 30-3-3 0-5 0-8-1l-1 6c2 0 2 0 4 2h-1z"></path><path d="M753 184c5-1 10-5 15-4 2-2 5-2 7-3v1l-3 2c-1 1-1 1-1 2-1 2-2 4-4 5l-3 1h0c-1-1-3-1-4-1 2-2 4-4 6-5-8 3-16 6-24 8 3-3 7-5 11-6z" class="P"></path><path d="M1198 931h4l1 1c-6 5-14 10-20 13-3 2-6 4-9 4h-4l1-1h1c9-3 18-11 26-17z" class="K"></path><path d="M706 612c-2 0-2 0-3-2s-1-2 0-4c8-1 15 1 22 3-1 6 0 13 0 19-1-5-1-10-1-15l-4-1-2-1c-4-1-9-1-12 1z" class="H"></path><path d="M357 519l10 1c-1 3-1 8-3 11-2 1-6 1-9 1 2-3 2-10 2-13z" class="B"></path><path d="M992 1336h1 1c0 4 1 7 0 10l-8 2-2-1-1-10 9-1z" class="P"></path><path d="M1089 1031c1 1 7 3 8 5 1 1 1 2 3 4h-1c2 2 2 2 4 3 3 1 7 6 9 9-3-1-5-2-7-3l-16-12c-1-1-2-1-3-2l3-4z" class="K"></path><path d="M448 1034l1-4c3-2 8-2 11-2 2 0 3 1 4 2 1 2 2 6 1 8v2l-1-3c-5 0-10-2-16-3z" class="H"></path><path d="M331 1029c-1 5-1 12 0 16 1 3 0 6 0 8l1 1-4 2-1-1c-1-7-1-15-1-23 1-2 3-2 5-3z" class="I"></path><path d="M1007 1343l-11 3v-11l13-3 1 1-1 1c-2 1-3 3-3 5 0 1 1 2 1 4z" class="P"></path><path d="M788 841c2 0 6-1 8 1 0 5-1 8-4 13l-1 2h-3v-1c-2-4-1-11 0-15z" class="D"></path><path d="M397 473c1 2 0 5 0 8-2 7-3 17-6 23h-1c-1-5 1-12 1-18 1-4 0-8 0-12l1 1h1c2 0 2-1 4-2z" class="F"></path><path d="M397 473c1 2 0 5 0 8-1-1-2-1-3-1h-1l-1-5h1c2 0 2-1 4-2z" class="G"></path><path d="M545 995c-3-7-6-13-9-19-7-10-16-20-25-28v-1c5 3 9 7 13 11 3 4 13 19 16 20 1 1 3 1 4 1l-2 1c1 2 2 3 3 5v10z" class="S"></path><path d="M1160 1013c-6-2-12-5-18-9 10 3 19 7 29 11 4 1 10 2 14 5l-1 1c0 1 1 2 1 4-8-4-16-10-25-12zM273 335c4 1 6 1 8 5 4 6 5 18 4 25-1 2-2 2-3 2h0c1-7 0-17-3-23-1-3-4-6-6-9z" class="R"></path><path d="M386 328l-21-3c-6-1-32-2-36-6 2-2 14 0 18 0 1 1 3 2 4 3 3 1 8 0 11 1l15 2c3 0 7 0 10 2l-1 1z" class="V"></path><path d="M835 1219l1 1c0 3-1 5-1 8 0 1-1 4-1 5 1 0 3 1 4 1 4 2 10 4 14 9h-1c-6-2-12-5-17-7l-1 10h-1l-1-1c0-10 0-17 4-26z" class="J"></path><path d="M597 1380h1v3l1 27c0 3 0 7-1 10 0 1-2 2-3 2h-1l3-42z" class="C"></path><path d="M374 656l2-2c7-1 14-1 22-1 5 0 10-1 16 0l-1 1 2 2c-6 1-14 0-21 0-6 0-13 1-20 0zm959 304h2l1 1c-1 8-1 18-3 26h-2c-1 0-1-1-1-1 0-6 0-13 1-19v-5c0-1 1-1 2-2z" class="M"></path><path d="M534 180h2c3 3 1 40 0 46-1 2 0 1-1 2-1-9-1-17-1-26v-22z" class="E"></path><path d="M733 1308c2 3 1 7 2 10v22c-2 1-2 1-4 1-1-3 0-6 0-9v-16c0-2 0-5-1-7l2 1 1-2z" class="S"></path><path d="M552 1079l-1-1c1-4 2-10 4-13h2c4 1 7 1 11 1h3l1 2h0c-3 3-7 4-11 4l-2-1-1 1c-1 2-3 3-5 4l-1 3z" class="I"></path><path d="M545 1246c1 6-1 12 1 18 1 4-1 11 0 16 1 2 0 5 0 7 0 7 1 16-2 22-2-7-1-17 0-25l1-38z" class="C"></path><path d="M274 451h0c3-5 4-7 9-8-5 12-12 21-22 30v-2l3-3c4-5 8-11 10-17z" class="E"></path><defs><linearGradient id="C" x1="480.822" y1="1067.375" x2="478.932" y2="1057.855" xlink:href="#B"><stop offset="0" stop-color="#5d5d5d"></stop><stop offset="1" stop-color="#868486"></stop></linearGradient></defs><path fill="url(#C)" d="M488 1051l2 3h0l1-1c1-1 1-1 2-1-4 9-10 14-19 19l-6 2v-4l7-3c5-3 12-9 13-15z"></path><path d="M245 846c1 0 1 0 2 1l-2 9c-2 4-4 10-9 12-2 1-4 0-6-1-5-3-8-11-9-16v-1l1 3c2 4 4 9 8 11 2 0 4 0 6-1 6-3 7-12 9-17z" class="C"></path><path d="M278 467l1-2 1 1v2c1 1 0 1 2 1-4 8-10 16-17 22-4 3-11 9-16 8h2c10-6 16-14 22-23 2-3 4-5 5-8v-1z" class="M"></path><path d="M723 170h2c0 2-2 4-3 5-1 3-3 5-3 8-1 1-1 2-2 4l-4-1-2-1c-1-3-2-6-1-9 5-1 8-3 12-5l1-1z" class="V"></path><path d="M1325 547l2 1-1 2c-4 0-13-1-17 3-1 2-2 5-3 7 1 4 1 7 1 11h0l-1 2 1 3-4-8v-11c1-2 2-5 4-7 4-4 13-2 18-3z" class="E"></path><path d="M1303 557l2 2h0c0-3 1-4 2-6h1c0 1-2 2-2 3s1 1-1 2l1 2c1 4 1 7 1 11h0l-1 2 1 3-4-8v-11z" class="H"></path><path d="M377 994c2 3 0 7 0 10-2 7-2 15-3 22 0 2 0 6-1 8-2 0-2 1-3-1 0-8 1-17 3-25 1-5 2-9 4-14z" class="D"></path><path d="M834 869c-3-2-7-4-10-6 11 3 20 9 26 18 3 5 5 12 3 17-1 4-3 7-4 10-1-3-1-5-1-7l2-15c-2-5-4-8-8-12-2-2-6-4-8-5z" class="S"></path><path d="M985 1075c-1-6 2-12 4-18 1 3 2 6 2 9 1 7-1 17-2 24-1-4-3-4-4-7v-1c1-2 1-5 0-7z" class="Q"></path><path d="M622 134h1c2 1 2 3 3 5-2 4-9 8-13 11-1 1-1 1-2 1 0-3 1-5 2-7 2-5 4-8 9-10z" class="E"></path><path d="M456 462l6-3 1 1c0 2 0 5 1 8 0 4 1 7 3 11l-1 9c0-1 0-3-1-4v-1c0-1 0-2-1-4l-1-1h1v-1l-1-2v-1c0-1 0-2-1-3v-1-1h-1c0 2 0 2-1 3l-2 2v1l-3 1 1-14z" class="F"></path><path d="M367 811c1 3 1 7 1 9l2 2c1 2 0 7 0 9 1 10 1 20-1 29l1 2h-3l-1-1v-3l1-10v-37z" class="H"></path><path d="M700 1280l33 28-1 2-2-1-32-25c3 0 6 2 9 5 1 1 1 1 3 2-3-4-8-6-10-10v-1z" class="D"></path><path d="M388 1134c2 0 2-1 4 0 2 2 4 4 4 6 0 4-2 7-5 10h-2c-1-2-2-3-3-6v-7h-1l3-3z" class="Q"></path><path d="M388 1134c2 0 2-1 4 0 0 4 0 6-2 8-1 1-2 2-4 2v-7h-1l3-3z" class="K"></path><path d="M828 1075h1c3-1 13-1 15 1 1 3 0 3-2 6l-3 3c-3-1-8-1-10-3-1-1-1-5-1-7z" class="H"></path><path d="M1006 1075l2 2h2c1 4 1 14 4 17v-3l1 1 1 13c-1 1 0 1-1 1-1-2-2-3-2-4s0-1-1-3l-5-8-1-16z" class="U"></path><path d="M654 912c2 0 6-1 7 1v9c0 3 1 5-1 8h-5l-1-1v-17z" class="C"></path><path d="M1251 1134v5c1 1 2 2 2 3v1c-6 10-13 20-21 29l15-29c1-2 2-3 1-5 0-1 2-3 3-4z" class="O"></path><path d="M540 610c4-3 9-10 14-12l1 2c-5 8-12 14-19 20-1 1-2 2-4 2 0-3-1-5-1-8l2 1h1l6-5z" class="J"></path><path d="M426 996c4-4 7-6 13-6l2 1c-13 9-21 16-27 30v-2c0-7 8-18 12-23z" class="E"></path><path d="M927 1025c6-6 13-11 18-15l24-21 1 2c-1 4-3 5-5 7l-36 28-2-1z" class="L"></path><path d="M775 177c3-1 3-1 6-1l1 1c-2 3-4 6-7 8-5 5-13 7-19 9-5 1-9 3-13 4 4-3 9-5 13-7 3-1 5-2 8-3h0l3-1c2-1 3-3 4-5 0-1 0-1 1-2l3-2v-1z" class="G"></path><path d="M775 178h3v1c-2 1-3 2-5 2l-1-1 3-2z" class="X"></path><path d="M994 1200c9-1 19 1 28 3 5 5 8 11 11 18-5-4-6-10-11-13-9-5-20-2-28-8z" class="D"></path><path d="M347 319c12 1 23 2 35 4 5 1 14 2 18 5v1 1c-5 0-9-1-14-2l1-1c-3-2-7-2-10-2l-15-2c-3-1-8 0-11-1-1-1-3-2-4-3z" class="P"></path><path d="M417 430v2c-5 3-32 0-39 0l1-14v6 2c2 2 5 2 7 3 10 1 20 1 29 1h2z" class="M"></path><path d="M1106 1016l-10-4 3-10c5 2 10 4 14 6 1 3 1 3 2 5l-1-1c-3 0-5 1-8 1l-1 1 1 2z" class="P"></path><path d="M504 244l21 5c5 1 9 1 13 4-8 1-15 0-23-1-4-1-8-1-11-4v-4z" class="H"></path><path d="M682 363c3 0 7 1 10 1l-7 17c-1-2-2-4-4-5h-1l-1-1h-2l5-12z" class="E"></path><path d="M404 345c-8 2-16 0-24-1-6-1-12-1-18-2v-1l5-2-2-1 1-2c6 5 21 5 29 6l9 3z" class="U"></path><path d="M481 491c2-1 4 0 6-1 2 0 4-1 5-1 6-1 10 0 15-2l-1 4c-1 0-2 0-3 1-4 0-9-1-13 0v1c0 2-1 3-3 5h-2v-5c-1-1-1-1-3-1l-4 25h-1v-11l1-3c1-4 1-8 3-12z" class="G"></path><path d="M323 533h1c6-1 13 2 19 3 3 0 8 0 10 1-2 2-2 2-4 2l-2 1c-5 0-23 0-26-3 0-2 1-3 2-4z" class="F"></path><path d="M664 887v15h-10v-13l10-2h0z" class="B"></path><path d="M1151 433c0-3-3-6-5-8-6-8-12-16-18-23l-4-4c7 3 12 10 17 16 6 6 12 13 17 21l1 3c-3-2-5-4-8-4v-1z" class="J"></path><path d="M1185 1039c1 0 1 0 2-1 3-1 3-3 3-6h1l4 2c2 1 4 2 5 4-1 4-2 7-4 11-2 0-1 0-3-1-2-3-5-5-8-7v-2z" class="L"></path><path d="M755 207l-1-1c-2 1-5 2-7 2-1 0-1-1-2-2 2-2 6-2 9-3 4-1 9-3 13-5 3-2 5-5 10-5 1 2 1 4 2 6-3 0-6 0-8 1-5 2-11 6-16 7z" class="S"></path><path d="M1303 568l4 8-1-3 1-2v1c1 3 2 6 5 7 4 3 12 2 17 3 1 1 0 1 0 3-4 3-13 1-17 1h-1c-4-6-7-10-8-18zM363 453l21 2v1c0 3-1 6 0 9l1 4-8-2c-1-4-3-8-5-11l-9-1v-2z" class="M"></path><path d="M752 1230c-5-4-9-9-12-15 1-1 7-2 9-3 3-1 6-2 9-4-2 2-4 5-6 7-1 3 0 7 1 10v4l-1 1z" class="K"></path><path d="M403 569v1c-6 12-16 22-27 31 3-8 9-12 14-18l13-20 2 1-1 2c-1 1-1 2-1 3z" class="O"></path><path d="M921 893c1-2 1-3 3-4h1v36l-3-1c-2-2-1-5-1-7 0-8 1-15 0-23v-1z" class="B"></path><path d="M626 217h2c1 2 2 5 1 7-1 4-8 7-11 9l-2 1h-1v-9c4-3 7-6 11-8z" class="H"></path><path d="M1292 977c4-3 6-1 10-2v12c-2 1-3 1-5 1-6-3-11-3-17-3-3 0-6 1-9-1l29-2v-1c-2 1-5 0-7-1l-1-3z" class="E"></path><path d="M681 1198c3-1 4-1 6 0 4 3 6 9 8 14 3 7 7 15 12 22h0c-1 2-1 3-2 4h-1c-5-8-8-17-12-26-2-4-3-8-6-11-1-2-2-2-4-2l-1-1z" class="H"></path><path d="M240 366c3-12 7-27 15-37 3-3 5-5 9-6-10 12-19 28-22 43h-2z" class="D"></path><path d="M1304 651h35l1 1v1l-58 2 1-3 21-1z" class="E"></path><path d="M835 1219c3-4 6-8 11-10 5-3 11-5 15-9h1c1 2 1 4 0 6-4 4-10 7-15 9-4 1-8 2-11 5l-1-1z" class="C"></path><defs><linearGradient id="D" x1="1164.551" y1="514.958" x2="1173.221" y2="518.287" xlink:href="#B"><stop offset="0" stop-color="#2d2c2d"></stop><stop offset="1" stop-color="#444545"></stop></linearGradient></defs><path fill="url(#D)" d="M1160 483c7 13 10 29 13 44-2-1-3-1-5-2-1-1-1-2-1-3-1-3-1-3-3-5 0-1 0-1-1-2v-1c0-2-1-7 1-9v1l2 3 1 1v-4l-1-1v-1l-1-1v-3-1l-1-1-2-7-1-1v-2l-1-1v-4z"></path><path d="M1306 982h8c2 0 2-1 4-1h6c1-1 3-1 5-1v7c-3 2-21 1-25 1l-1-1v-3c1-1 2-1 3-2z" class="H"></path><path d="M615 586h2c1 1 1 1 1 2s1 7 1 8c-2 3-8 4-10 7l-5 5c1-4 4-5 3-8l-3-3v-2c3-3 7-6 11-9z" class="E"></path><path d="M917 894h1l1-8 1 7h1v1c1 8 0 15 0 23 0 2-1 5 1 7l3 1v1h-4l-4 1-2-1 2-2v-30z" class="U"></path><path d="M869 1034l1-8 1 2v2c1 4 0 9 1 13v6 15l-1 54-2-84z" class="E"></path><path d="M817 957c0 2 0 3 1 5l1-1 1-1c1 2 0 6 1 9 1 2 0 4 1 6v37c-1 0-2 1-3 1 0-1 0-6-1-6 0 0-1 0-1-1-1 0-2-1-3-2l4-2 2-16c1-7 0-14-1-21l-1-1-1 2v-9z" class="D"></path><path d="M1277 483c7-4 14-9 21-12 2-1 4-2 6-2l2 4c-2 2-5 5-7 6l-2 1c-1 0-4-1-5 0-2 1-3 4-6 4s-6-1-9-1z" class="N"></path><path d="M531 350c5-1 12 1 18 1l3 9c-5-1-15-1-19-4-1-2-2-4-2-6z" class="B"></path><path d="M380 869h3v2l2-2c3 0 7 0 9 1 0 3 0 5-1 7 0 1-1 1-2 2-3 1-5 2-8 2h-1l-2 2h-1l-1-1c1-1 1-2 1-3 0-2 0-5 1-6s1-2 0-3v-1z" class="Q"></path><path d="M1107 1249c2 0 2 0 4 1 2 2 4 5 5 7 4 6 9 14 11 21-3-4-6-9-11-11-1 1-2 2-2 3l-2-1h1c2-4-4-8-5-11v-1c0-3 0-6-1-8z" class="G"></path><path d="M430 640l11 2c0 3 1 8-1 11l-2-1c-5 0-9 0-14-1 2-4 4-7 6-11z" class="L"></path><path d="M695 596c3-3 4-2 8-2h23l1 1v5h-1l-27-2c-1 0-2-1-4-2z" class="V"></path><path d="M646 1076l-1-1 1-2 5 2c1 0 2 1 4 1 4 2 9 4 13 5 2 1 5 1 7 2l1 2v2c-3 0-6 0-10 1h0c-2-2-4-3-7-4-5-2-9-5-13-8z" class="F"></path><path d="M617 645l1 1c0 1 1 3 1 4 0 7-11 13-16 17l1-9c0-3 0-4 2-5h3c2-4 4-6 8-8z" class="H"></path><path d="M718 225h1l1 9c-1 4-1 8-5 11-2 1-4 1-7 0-1-1-2-1-2-2l5-3c2-3 1-9 1-13 2-2 4-2 6-2z" class="M"></path><path d="M757 773c3 0 3 0 5 1 0 9 2 20 1 29-2 0-4 1-5 0-1-10-1-20-1-30z" class="U"></path><defs><linearGradient id="E" x1="1246.127" y1="486.058" x2="1239.143" y2="500.162" xlink:href="#B"><stop offset="0" stop-color="#7c7c7d"></stop><stop offset="1" stop-color="#9a999a"></stop></linearGradient></defs><path fill="url(#E)" d="M1243 480h3l-2 22-47 1c3-2 9-1 12-1 9 0 20 0 29-2l2-1s1-3 1-4l2-15z"></path><path d="M299 696l-1-6 1-1c5 1 9 3 13 6l1 1c-4 3-5 6-6 11-2-1-6-3-8-5-1-2-1-4 0-6z" class="V"></path><path d="M1306 982c-2 0-1 0-2-1v-5c7-1 15 0 22-2 1-1 2-1 2-2v-1c1 3 1 6 1 9-2 0-4 0-5 1h-6c-2 0-2 1-4 1h-8z" class="E"></path><path d="M755 921v-12h12v14h11c1 2 1 3 1 5l-1-4c-3-1-6 0-9 0 0 3-1 9 1 13h-3v-11l1-1c-2-2-5-1-7-2-1-3 0-7-1-10h-4c0 3 0 5-1 8z" class="V"></path><path d="M1320 527l2-1c2 4 2 9 3 13h-18c-2-1-3-2-4-5l1-2h5c3-2 7 0 10-1 1-1 1-2 1-4z" class="D"></path><path d="M1330 863h-86-17c-2 0-6 1-9 0l1-2c4-2 39-1 47-1l-1 1c-6 1-15 0-22 0v1h59c9 0 19-1 28 1h0z" class="J"></path><path d="M1047 1249c-10-2-18-7-26-12 6-1 12 0 17 1 3 2 7 3 10 4l6 7h-7z" class="Y"></path><path d="M924 1266c2-1 5 0 8 0l15 2c2 3 4 7 5 10-4-2-9-3-13-4s-13-2-16-4l1-4zM583 414h14l1 13-12-2v-4l-1-1v4h-1c0-4-1-7-1-10z" class="C"></path><path d="M302 800h1c5 6 5 18 4 26-1 5-2 12-6 17l-1-1c0-3 1-5 0-8 1-2 0-1 1-2 2-7 3-17 2-24-1-2-1-5-1-8z" class="N"></path><path d="M500 639c1 8 0 17 0 26l-1 1-5-1c-2-4 1-19-2-21l1-2 1 1 1 1 2-2c1-2-1-1-3-3 2 0 3 0 5 1l1-1z" class="G"></path><path d="M615 616c1 0 1 0 3 1v8c-3 5-7 7-12 9l-2-1c0-2-1-4-1-6 1-3 9-9 12-11z" class="E"></path><path d="M601 1007l1-9c1-1 2-1 3-1 3 4-1 12 3 14h3c3-3 0-14 4-15 3 3 2 14 1 18l-13 1h-1c-1-1-1-6-1-8zm188 277v-4c4 1 6 6 10 8 3 2 9 2 12 2l23 3-2 1c-6-1-11 0-17 0-8-1-16-1-25-3l-1-7z" class="P"></path><path d="M669 1171l1-1h1c1 1 3 2 4 2l2-1-4 18-9-2c-1-4-1-7 0-11 2 0 6 0 7-2 0-2-1-2-2-3z" class="S"></path><defs><linearGradient id="F" x1="555.431" y1="888.989" x2="545.32" y2="881.046" xlink:href="#B"><stop offset="0" stop-color="#5a5a5a"></stop><stop offset="1" stop-color="#7c7a7c"></stop></linearGradient></defs><path fill="url(#F)" d="M553 868c1 6 4 22 1 27-2 1-3 2-4 3-2-2-2-6-2-9 0-1 0-2-1-3-2-4-1-8-1-11 3-1 4 0 6-2l1-5z"></path><path d="M753 425l-7-2 9-24 2 1-4 8 9 4v1c-1 1-1 2-2 3-1 2-2 3-3 5-1 1-2 2-2 3l-2 1z" class="E"></path><path d="M1254 878c-1 2-1 5-2 8-4 0-16 0-19-3 0-2-1-5 1-7l18 1 2 1z" class="W"></path><defs><linearGradient id="G" x1="418.746" y1="739.157" x2="408.757" y2="737.974" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#424142"></stop></linearGradient></defs><path fill="url(#G)" d="M416 733c3 3 4 9 5 13-5 0-9 1-14 0h0v-1c0-4-1-7-4-10h0l3-2h7c1 1 2 0 3 0z"></path><path d="M1271 412l7-5c2 4 5 8 4 13-1 1-2 1-3 2 1 0 1 1 2 1l1 1c1 0 2 0 3 1v1c-2 1-4 2-6 4-3-1-6-9-7-12l-3-5 2-1z" class="I"></path><path d="M1272 418l-3-5 2-1 1 1h4l1 2c-2 2-3 2-5 3z" class="W"></path><path d="M710 193h10c0 6 0 12-1 17l-1 1c-2 1-6 1-8 1-1-6 0-13 0-19z" class="D"></path><path d="M1157 533c-5 2-11 2-16 4-1 0-2 0-3 1h-2-4v-1h2l14-3 2-2h2c2-1 5-1 7-2h1c3-1 6-2 9-2l-3-3h-1v1l-2 1-1-3c-1-1-1-1-1-3 1-1 1-1 1-2 0-2-1-2-2-4-3 1-7 2-10 3 0 2 0 2 1 3l-1 2-3 1h-1c-2 1-4 1-6-1v-2c-1-3-1-8-1-12l-2-1h3c0 4-1 12 2 15h3c7 0 2-3 5-6 2-1 10-3 12-3l1 2 1 1c2 2 2 2 3 5 0 1 0 2 1 3 2 1 3 1 5 2l-1 2-9 2h-1c-2 0-1 0-2 1h-3v1z" class="F"></path><path d="M614 968c2-1 1-1 3 0 1 3 1 5 1 8-4 4-8 8-13 10h-2c-2-2-1-4-1-7 1-4 9-9 12-11z" class="D"></path><path d="M491 733c1-1 2 0 3 0 5 4 7 10 9 15 3 6 6 11 3 17-1 2 0 1-2 2-2-4-4-10-5-14-2-7-4-14-8-20z" class="V"></path><path d="M615 764h2c0 3 1 6 0 8 0 3-11 10-14 12h-2v-7c1-5 10-10 14-13z" class="D"></path><path d="M492 556c5-13 11-26 14-39 1-9 0-19 3-27h2c0 6-2 13-3 19-3 16-6 33-13 48h0l-1-1v1h-1l-1-1z" class="G"></path><path d="M614 676h2l1 1v8c-4 4-10 8-14 11-2 1-1 1-3 1l1-2c1-10 6-14 13-19z" class="E"></path><path d="M436 462c1 2 2 3 3 4v5c0 7-4 40-8 44l-2-1 7-52z" class="Y"></path><path d="M601 722c0-4 0-5 2-8l13-10 2 9c-5 5-11 9-17 13v-4z" class="J"></path><path d="M1264 982c0-6 2-12 2-18 1-5 1-11 2-16 2 2 3 3 4 5 1 4 1 3 4 5l-1 1v5c-2 1-2 1-4 1l-1 9v8h-6z" class="R"></path><path d="M1272 953c1 4 1 3 4 5l-1 1v5c-2 1-2 1-4 1l1-12z" class="X"></path><path d="M1108 608l26-1c-1 3-1 5-1 8l-22 1c1-4-1-2-2-4-1-1-1-3-1-4z" class="Q"></path><path d="M1247 647h0l1-2 3-1 3 3c0 3 1 6 2 9l-1 3v3 3h6c-4 2-11 1-15 0v-2h0v-8c1-2 1-5 1-8z" class="O"></path><path d="M607 349c-1-1-2-2-3-4l9-7c3-2 7-6 11-7 0 2-16 11-17 16l1 1c1 0 3-2 3-2 3-3 7-7 11-8 3 0 3 1 5 2-3 3-8 9-8 13v1c-1 0-1 0-3-1-2-2-4-2-6-3s-1-1-3-1z" class="S"></path><path d="M357 678c0-2 1-8 2-9 4 0 8 0 11 1v5c-1 2-1 7-1 10-4 0-9 0-12-2v-5z" class="C"></path><path d="M923 823l2 1c1 7 0 15 0 23s2 21-2 28l-1-2c-1-3-1-47 1-50z" class="B"></path><path d="M378 968h1c1 5-6 12-8 17-3 5-6 11-8 17l-1 1v2l-1 3c-1 2-1 3-1 5l-1 3-1 1h-1l1-3c3-18 6-33 20-46z" class="F"></path><path d="M711 794v22c1 4 0 8 1 11 0 1 1 2 1 3 1 0 1 1 1 2v62c-3-4 0-11-2-15-1-3-1-6-1-9v-76z" class="L"></path><path d="M755 207c5-1 11-5 16-7 2-1 5-1 8-1-1 2-1 3-3 4-4 4-11 7-16 8-1 1-2 2-4 1-2 0-5 2-8 2l-2-2c1-3 6-4 9-5z" class="H"></path><path d="M615 735h1c1 1 2 3 2 4 0 3 0 4-2 6-2 3-10 8-14 9-1-3-2-5-4-7l17-12z" class="E"></path><path d="M468 497c0-4 0-7 2-10 3 3 7 2 11 4-2 4-2 8-3 12l-1 3-1-1c-2 0-2 0-4 1v4l-1 1c0 1 0 1-1 3-2-3 0-13-1-17h-1z" class="F"></path><path d="M472 506c1-2 1-4 2-6 0-2 0-1 1-2 2 2 3 3 3 5l-1 3-1-1c-2 0-2 0-4 1z" class="Q"></path><path d="M541 772h3l1 1-1 51-1 1-1-1-1-52z" class="H"></path><path d="M209 575c-2 2-7 2-9 5l-1 1c0 1-4 5-5 5-3 1-9 1-12 1 6-6 11-12 17-16l8 1 2-1 2 1v1h-2v2z" class="R"></path><path d="M810 1064l1-3h1l1 6c0 2 0 3 1 5h2c2-2 3-2 4-5 1 0 2-5 2-7v22c-3 0-7 1-9-1l-1-5-1 1v5c-2 1-2 1-4 0v-2-7c1 2 1 2 2 3l1-1c1-1 0-3 0-5v-6z" class="F"></path><path d="M1014 1091c0-4-2-11 0-14l3 1h1c4 3 5 10 6 15 0 2 0 5 1 7l-1 2c-1 2-1 4-2 5-1-2-3-4-3-7-1-2-2-6-4-8l-1-1z" class="G"></path><path d="M427 1033c-2 0-2 0-3-1 0-4 0-8 2-12 3-7 11-17 19-20l1 6c-4 1-10 8-13 12s-5 9-6 15z" class="S"></path><path d="M214 773c1-4 4-8 7-10 6-4 11-2 17-1-5 3-11 5-15 9l-1 2c-1 3-4 6-6 10-1-1-1-1-2-3 0-1-1-1-2-2 1-1 2-2 2-4v-1z" class="G"></path><path d="M509 359l1-1 8 1c3 0 5 1 7 4l1 2h1c1-1 0-1 1-1 0 2 2 7 3 9l-19-5-3-9z" class="W"></path><path d="M427 1033c1-6 3-11 6-15s9-11 13-12c0 2 1 6 0 8-2 3-5 6-6 9-5 3-7 3-8 9l-5 1zm183-177c2 0 2 0 4 2l2-1c1 1 0 2 0 4-3 5-10 9-16 12l-3 3-1-7-1-1c5-3 9-9 15-12z" class="E"></path><path d="M543 426c1 1 2 2 4 2v3l4 22h-1c0 2 0 3 1 5 0 2 0 8-1 10h0l-2-1c-3-3-1-7-2-11-1-2-1-3-2-6 1-2 0-5 0-7l-1-17z" class="O"></path><path d="M547 431l4 22h-1c0 2 0 3 1 5 0 2 0 8-1 10h0l-2-1c1-3 1-7 1-11-1-4-3-8-3-13 0-4 1-8 1-12z" class="U"></path><path d="M348 356c2 1 5 2 8 3 1 1 2 2 3 4 1 4 2 12 0 15h-1c-2 1-5 1-7 0l-3-22z" class="D"></path><path d="M305 892c2-1 3 0 5 0l1 2c-4 6-10 13-16 18-7 7-16 8-26 8h-6 3 0c8-1 17-3 23-8s10-13 16-19v-1z" class="C"></path><path d="M678 275h7v1l-16 17c-5 5-10 13-17 15l7-11c1 0 3-3 3-3 6-6 10-13 16-19z" class="N"></path><path d="M499 184c0-3 0-7 1-10 10 2 20 3 28 7 1 1 1 2 1 4-2 1-4 1-6 0-1 0-4-1-5-1-5-1-12-3-17-3v2l-2 1z" class="C"></path><path d="M384 455l28 1c0 3 0 8-1 11-2 2-5 2-7 2-2-1-3-2-3-3-5-8-8-10-17-10v-1z" class="X"></path><path d="M762 412h6c3-2 4-6 5-9 2-6 5-12 8-17 0 2-5 11-6 14-4 10-7 20-11 31l-11-6 2-1c0-1 1-2 2-3 1-2 2-3 3-5 1-1 1-2 2-3v-1z" class="D"></path><path d="M515 1251h14c1 3 4 9 3 12l-3 1-8 4-6-17z" class="C"></path><path d="M552 423l-6-1c-4-4-4-11-4-16-1-8-1-16-3-23l1-1h6l-5 1c1 1 1 2 1 3 2 4 3 8 4 13 1 3 1 6 3 8-2 3-2 7 0 10 1 2 3 3 4 5l-1 1z" class="E"></path><path d="M449 804c2-2 5-1 9-1 0 1 1 1 1 2 1 2 0 10 0 12h0c-1 3-1 8-3 10-2 0-3 0-4-1s-3-19-3-22z" class="B"></path><path d="M370 306c3-1 5-1 8 0 6 0 10 3 13 7v2l-13-2c-8-1-17-2-25-4l5-2c4-1 8-1 12-1z" class="O"></path><path d="M1032 1326h3l6 5c-5 3-12 4-18 7l-7 1h-1v-9l17-4z" class="G"></path><path d="M540 867v-4c0-1 2-1 2-2l-2-2v-5c0-6 0-12 2-17v-1c-1-2-1-4-1-6l1-1 2 1 1 33 1 1 2-1 4 1c0 2 1 3 1 4l-1 5c-2 2-3 1-6 2l1-1c0-2 0-2-1-4s-1-3-1-5h-1c0-1-1-2-2-2-1 1-1 3-2 4z" class="I"></path><path d="M548 863l4 1c0 2 1 3 1 4l-1 5c-2 2-3 1-6 2l1-1c2-1 2-2 3-4 1-3 0-4-2-7z" class="L"></path><path d="M487 631l1-29 9 5-2 13v1c-1 3-1 5-1 8-2 0-3 0-5 1l-2 1z" class="I"></path><path d="M630 500c2 1 2 0 4 2 0 0-1 1-1 2 2 1 4 1 7 2 2 1 4 2 5 3l3 3-2 7c-2 1-3-1-4 0-3 0-3 1-5 1-1-2-1-3-2-4-1 2-1 3-4 4 0-2 0-2 1-4 1-1 1 0 1-2 0-1-1-2-1-3-1 0-1 0-1-1s-1-2-1-2c0-1 2-3 2-3 1-1 0-2 0-3l-2-2z" class="L"></path><path d="M633 504c2 1 4 1 7 2 2 1 4 2 5 3-2 1-5 2-7 1l-1-4h-1v6h-1c0-3-1-5-2-8h0z" class="M"></path><path d="M806 1278c1-2 3-5 4-7 0-2 0-3 1-4 3-1 6-1 9 0 0-6 0-13 1-19l1-1c1 1 2 1 3 2v2l-2-1c-1 2-1 25-1 30l-11-3c-1 0-2 1-3 1h-2z" class="O"></path><defs><linearGradient id="H" x1="821.381" y1="1122.926" x2="839.808" y2="1097.669" xlink:href="#B"><stop offset="0" stop-color="#99999b"></stop><stop offset="1" stop-color="#bab8b7"></stop></linearGradient></defs><path fill="url(#H)" d="M829 1142c-1-8-1-17 0-25 0-9 0-19 1-28 1 1 3 1 4 2l-2 48v-8c-2 2-1 8-3 11z"></path><path d="M596 1188v1c2 2 2 3 3 6-5 6-14 12-20 16h-1c-2-3-1-5-1-8 4-5 13-11 19-15z" class="W"></path><path d="M340 978c3 0 4-1 6 1v2l-2 4-2 5-3 9-2 7v2c-3 0-5 0-7-1 1-4 3-8 4-13 2-5 3-12 6-16z" class="V"></path><path d="M344 985l-2-2v-2l2-1 2 1-2 4zm-7 21h-1c-2-2-2-1-2-4 1-1 1-1 1-2 1-2 1-4 3-5 2-2 0-4 1-6 0-1 1-1 1-2l2 3-3 9-2 7z" class="S"></path><path d="M344 1089c-2 2-2 3-2 5 0 5 4 8 1 14-4-1-9-10-11-13 3-5 7-12 13-13 2 1 5 3 6 6h0l1 1c0 2 1 4 1 6-3-3-6-8-9-11l-1 1 1 4zm616 251c3 0 6-1 7 1l1 7h-21c-2 0-4 1-6 0l-1-6c3-3 17-2 20-2z" class="G"></path><path d="M1024 1316h3l-1 5h-1l1 2-1 1-10 2c-1-1-1-2-1-4l-1-1v5c-3 1-6 2-9 2-2 1-4 2-7 1v-5c1-3 13-4 17-5l10-3z" class="L"></path><path d="M818 920l3 1c1 3 0 8 1 11 0 9-1 18 0 27v16c-1-2 0-4-1-6-1-3 0-7-1-9l-1 1-1 1c-1-2-1-3-1-5-1-8 0-16 0-24 0-4-1-9 1-13z" class="M"></path><path d="M1041 1270h2c-5 3-11 5-16 7-14 5-29 10-44 13h-2v-4c3-2 13-3 17-4 14-3 28-8 43-12z" class="H"></path><path d="M356 653l1 1v1c2-1 3-1 4-2 2-1 5-1 7-1 4-1 4-1 6-3l2 2 6-1c2-1 3-1 5-1 9 1 19 0 28 2v1l-1 1c-6-1-11 0-16 0-8 0-15 0-22 1l-2 2c-5 0-16 2-20 0l2-3z" class="N"></path><path d="M615 793h1c2 2 2 4 2 6 0 5-14 13-18 15h-2c-1-2-1-3-1-5 5-7 12-12 18-16z" class="D"></path><path d="M672 169c0-8-1-20 4-27 2-3 5-4 8-5h4c2 2 1 4 1 6-2 1-3 1-4 1-3 1-7 1-9 4-1 1-1 1-1 2v1l1-1 6-3h1c6-1 14 0 20 0-7 5-26-5-28 9-2 8-1 16-1 24-1 4-1 8-1 12l-1-23z" class="C"></path><path d="M405 835c2 1 5 1 6 3v5c1 4 0 14-2 17l-1 1-2 5c-2 0-2 0-3-1v-7c-1-1-1-8-1-9 1-2 1-4 1-6l1-1c-1-2 0-4 1-5v-2z" class="D"></path><path d="M405 835c2 1 5 1 6 3v5l-1 2-2 2v2c-1 2-2 3-2 5v1c-2-5-1-13-1-18v-2z" class="H"></path><path d="M243 367v2l1-2c0-1 1-3 1-4 1-1 1-2 1-3l2-4 1 1c-5 15-8 29-9 45-2 2-3 4-4 7l1-9-1-6-2-1c0-2 1-6 2-8l4-19h2l1 1z" class="I"></path><path d="M240 366h2l1 1c-1 2-1 3-2 5-1 7-3 13-4 20v8l-1-6-2-1c0-2 1-6 2-8l4-19z" class="M"></path><defs><linearGradient id="I" x1="573.075" y1="453.533" x2="582.428" y2="438.345" xlink:href="#B"><stop offset="0" stop-color="#414343"></stop><stop offset="1" stop-color="#646162"></stop></linearGradient></defs><path fill="url(#I)" d="M575 427c1 1 1 2 2 3v-5c2-1 1 0 3-1 0-4-2-11 0-15l3 5c0 3 1 6 1 10-3 2-2 4-3 7-1 7-1 14-1 20v5 3l-4-2c-1-2 0-6 0-9l-1-21z"></path><path d="M580 451v5h0-1v-4l1-1z" class="G"></path><path d="M370 675c1-1 1-2 2-3 1 0 5 1 6 1h20c5 1 11 2 15 1 3-1 6-5 9-7l1 1c-2 3-2 5-5 7-1 2-3 2-4 3-6 0-12 0-18-1h-25l-1-2z" class="C"></path><path d="M493 682c2 0 5 1 7 1l1 1c7 3 15 5 21 10 1 0 5 3 6 4l-38-8 1-2 1 1c0-3 0-5 1-7z" class="G"></path><path d="M493 682c2 0 5 1 7 1l1 1 1 1-1 4h-1c-1 0-2-1-3-1h-4v-6z" class="M"></path><path d="M501 684c7 3 15 5 21 10-5 1-8-4-13-4-1 1-1 1-2 0-3-1-4-2-5-5l-1-1z" class="L"></path><path d="M535 391l2 30c-7 1-10 1-16-1l-1-8c3 1 9-1 12-2 0-1 1-2 1-2-1-3-1-4-1-6v-1c1-1 1-1 1-2 0-3 1-5 2-8zm80 431h1l2 1v6c-2 6-14 11-19 15l-1 1h-1v-9l18-14zM353 426l1-24c2-1 4 0 7 1 3 3 2 8 2 13v9c-4 1-7 1-10 1z" class="D"></path><path d="M799 1068c1-1 1-2 3-3h2l1 1 1 3c0 2 1 3 1 4v7c-1-4-2-7-2-11h-2l-3 3v9c-5 0-10 0-15-1-1-3-1-6-2-10l-1-1h10c2 1 3 0 6 0h0l1-1z" class="Q"></path><defs><linearGradient id="J" x1="553.806" y1="909.658" x2="575.818" y2="871.942" xlink:href="#B"><stop offset="0" stop-color="#908f90"></stop><stop offset="1" stop-color="#bdbdbd"></stop></linearGradient></defs><path fill="url(#J)" d="M568 860s1 1 2 1c0 3 1 6 1 9l-1 34c-8 5-23 6-32 7 5-2 9-4 14-5 5-2 10-2 14-5 3-4 2-34 2-41z"></path><path d="M243 805l4-2c2 2 3 4 5 6 1 13-1 25-5 38-1-1-1-1-2-1 3-14 2-28-2-41z" class="E"></path><path d="M477 238h3v1c8 3 17 4 24 5v4l-1 2c-7 1-16-2-23-3-4-1-8-1-12-2 2-2 4-4 6-5s2-1 3-2z" class="J"></path><path d="M229 831l-2-2c-2 1-2 1-5 1v-2l-1 2c-3 1-14 1-17 0h-1v-3c2-2 9-2 13-2 2 0 5-1 7-3v-1h1v2c2 1 15 2 16 3v5c-2 1-1 1-2 1-3 0-6 0-9-1z" class="B"></path><path d="M422 1153c0 4 2 9 3 13-2-2-5-8-7-11h0c-4 2-7 6-11 9l-9-5 10-10c4-1 11-1 14 0l-1 3 1 1z" class="K"></path><path d="M933 598c6 1 11 2 18 2 1 0 1 0 3 1 2 0 1 0 3 1h1l1 1c2 1 5 2 8 2 2 0 2 0 3 1v4l-1 1-21-4c-5-1-10-2-15-4v-5h0zM615 938h2c2 2 2 5 2 7-2 5-14 12-19 15 0 1-1 1-2 1-1-3-1-5-1-8 3-5 13-11 18-15z" class="J"></path><defs><linearGradient id="K" x1="631.652" y1="1003.871" x2="630.534" y2="976.925" xlink:href="#B"><stop offset="0" stop-color="#7b7a7b"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#K)" d="M629 944l1 1c1 3 0 8 0 11v21h3c0-3-1-6 0-9v-10-6c0-3-1-6 1-8v50c0 8 0 18-1 27v-7c-1-3-1-6-1-9l-2-1c0-4 0-7-1-10s0-8 0-12v-38z"></path><path d="M628 486c4 0 7 0 10 2h0c-1 1-3 1-4 1-1 1-1 3-1 4 1 1 1 1 1 2v1c1 1 2 2 4 3 0 1 0 2 1 3h1c1 1 2 1 3 2l-3 1 3 1c2 0 3 1 4 2 1-2 0-3 1-5l-1-1v-5c-1 0-2 1-3-1h3c2-1 2-3 3-5l3-2-5 23-3-3c-1-1-3-2-5-3-3-1-5-1-7-2 0-1 1-2 1-2-2-2-2-1-4-2v1c-3 2-1 3-2 7h-1l1-22z" class="E"></path><path d="M1198 931c-8 6-17 14-26 17h-1l-1 1c-4 0-14 2-17 0 0-2 0-3 1-4 5-1 10 0 15-2 8-3 13-8 21-11 2-1 5-1 8-1z" class="B"></path><path d="M747 453l-9-2 7-26 8 4c-1 2-1 3-2 5v1 2c0 2 0 1-1 2 0 2 0 4-1 6v2l-2 6z" class="N"></path><path d="M243 805c-2-6-5-13-11-16-1 0-2-1-3-1 2-2 7-3 9-3 3 0 5 0 7 2 5 4 7 12 7 18v4c-2-2-3-4-5-6l-4 2z" class="B"></path><path d="M615 881c2-1 1-1 2 0 1 2 2 4 1 6-1 7-14 14-19 17l-2 2c-1-3-1-7-1-9 6-6 13-11 19-16z" class="D"></path><path d="M1230 440c2 2 3 5 6 6 4-1 9-4 13-7 1 3 0 4 0 7-4 2-13 4-15 7 1 2 1 2 3 3 4-1 7-3 10-5l14-8 2 2-31 17c-2-5-4-10-5-16h1c1 3 3 6 3 8 1 3 1 4 2 6 1-1 2-1 3-2-2-2-4-3-5-6v-2l-1-2c0-1 1-2 2-3-1-1-1-2-2-4v-1z" class="M"></path><path d="M616 909h1c2 3 2 5 2 7-1 5-16 14-21 17-2-2-2-4-2-6 4-7 14-13 20-18z" class="D"></path><path d="M1213 649c0 1 0 3 1 4v5l2 1c2-1 1-2 2-3 2-2 24-1 28-1v8h0c-3-1-6 0-9 0h-25l2-2h1v-1c-2-1-1 0-3-1l1-10z" class="F"></path><path d="M712 1022c0-20-3-40-1-59 2 0 2 0 4 1 2 6 1 14 1 20v32h-1v-8h-2c-1 4 0 9 0 13l-1 1z" class="I"></path><path d="M835 1341c12-2 26 0 38 1 4 0 10 2 14 0 3-1 4-5 5-8l-2 14c-18 0-38-2-55-7z" class="B"></path><path d="M931 1126c-1-2-2-4-4-6 1 0 2 1 3 2s2 1 4 2h0c8 7 16 14 26 20 8 5 18 9 27 13-19-1-43-16-55-30l-1-1z" class="J"></path><path d="M415 412l4 1c-1 5 0 11-2 17h-2c-9 0-19 0-29-1-2-1-5-1-7-3v-2c4 1 9 1 13 1l20 1c0-4-1-8 0-12 2 0 2-1 3-2z" class="N"></path><path d="M616 195c5 1 7 0 11-3h1v5c-3 3-22 17-26 17-1-1-1-3-1-5 0-5 12-11 15-14z" class="H"></path><path d="M263 463l11-12c-2 6-6 12-10 17l-3 3v2c-1 2-1 2-3 3l-1 1c-2 2 0 1-2 2l-1 1c-2 1-4 2-7 3h-1c-2 1-7 1-9 0l-2-2c2-2 4-4 7-5 3 0 6-1 9-2 4-3 8-7 12-11z" class="F"></path><path d="M1084 1135l-15 3-1-1c-2-3-4-9-6-13l-12-22c-1-2-4-5-4-8 7 10 15 21 20 32l1 1c0 2 1 5 2 6h1l1-2v-1l1-1c0-2-1-3 0-5s1-2 3-3c3 4 6 9 9 13v1zM638 912h13c0 4 2 15 0 18-4 1-8 1-12 1l-1-19z" class="B"></path><defs><linearGradient id="L" x1="1261.474" y1="475.825" x2="1257.871" y2="492.633" xlink:href="#B"><stop offset="0" stop-color="#989696"></stop><stop offset="1" stop-color="#b4b4b6"></stop></linearGradient></defs><path fill="url(#L)" d="M1251 502c0-5-1-12 0-18 7-4 15-7 22-11 8-5 16-10 24-14 2-1 4-1 6-2-11 11-26 17-38 25-3 1-9 4-10 7-2 4-1 12-1 16-1-2-2-4-2-6v-3c0 2 0 4-1 6h0z"></path><defs><linearGradient id="M" x1="494.654" y1="924.002" x2="518.193" y2="919.69" xlink:href="#B"><stop offset="0" stop-color="#c5c4c3"></stop><stop offset="1" stop-color="#f7f7f9"></stop></linearGradient></defs><path fill="url(#M)" d="M509 923c-7-3-17-6-20-14l1-1c12 4 23 11 33 19h-3c0 3-1 7 0 10l-11-8c0-2 0-2 1-4 2 1 1 1 2 0h0c-1-1-2-2-3-2z"></path><path d="M238 552c2-2 7-7 10-8s7-1 10-1l13 11h-34c0-1 1-1 1-2zm-23 2l4-6c2-2 5-5 9-5h14l-7 7-3 2c0 2 1 4 2 5v1l-6 3c-5-5-7-5-14-5l1-2z" class="C"></path><path d="M232 552c0 2 1 4 2 5v1l-6 3c-5-5-7-5-14-5l1-2c4 0 13 1 17-2z" class="Q"></path><path d="M869 1034c0-9-3-52 2-58 2-2 6-4 9-4h2v1c-3 2-8 5-9 10s0 10 0 15c-1 15 0 30-1 45-1-4 0-9-1-13v-2l-1-2-1 8z" class="H"></path><path d="M589 938l1 1c2 17 1 35 1 52 0 15 1 31-1 46v-3c-2-8-2-17-2-26v-36c0-10-1-23 1-34z" class="D"></path><path d="M1267 654c4 1 9 1 13 1h1c-1 2-2 4-2 7l2 1-1 2h-14-5-6v-3-3l1-3h0l2-1h9v-1z" class="Q"></path><path d="M1255 662c8 0 18-1 26 1l-1 2h-14-5-6v-3z" class="E"></path><path d="M1267 654c4 1 9 1 13 1h-1c-2 1-3 2-4 3-3 0-6 1-9 1-2 1-5 2-6 2s-3-1-5-2l1-3h0l2-1h9v-1z" class="F"></path><path d="M1267 655c-2 2-3 3-6 4h-3l-2-3 2-1h9z" class="K"></path><defs><linearGradient id="N" x1="615.903" y1="1045.544" x2="645.291" y2="1021.623" xlink:href="#B"><stop offset="0" stop-color="#343433"></stop><stop offset="1" stop-color="#7c7c7d"></stop></linearGradient></defs><path fill="url(#N)" d="M629 994c1 3 1 6 1 10l2 1c0 3 0 6 1 9v7l-2 62-1-2-1-87z"></path><path d="M661 947c2-3 1-9 2-13l1-15h1l1 26c0 2 1 6 0 8h0l-1 41v-3l-3 1c0 2 0 3-1 5v-30c1-7 2-13 0-20z" class="V"></path><defs><linearGradient id="O" x1="364.379" y1="580.507" x2="346.108" y2="566.379" xlink:href="#B"><stop offset="0" stop-color="#545553"></stop><stop offset="1" stop-color="#787678"></stop></linearGradient></defs><path fill="url(#O)" d="M353 555h6c1 5 0 11 0 16l-1 32-2-12c0-2 0-1-1-3-2 3-1 10-1 13h-1v-46z"></path><path d="M688 321h29c-1 2-3 5-5 7h-10-22c-2-2-3-4-5-5h-2l2-1c4-1 9-1 13-1z" class="J"></path><path d="M981 1303c4-2 10-2 13-4h1 1l-1 1v19l-12 2-2-18z" class="M"></path><defs><linearGradient id="P" x1="210.809" y1="836.354" x2="210.389" y2="867.686" xlink:href="#B"><stop offset="0" stop-color="#908f90"></stop><stop offset="1" stop-color="#bfbebf"></stop></linearGradient></defs><path fill="url(#P)" d="M213 869c-6-9-8-23-9-34l9 1c0 10 2 21 5 31-2 0-3 1-5 2z"></path><path d="M260 728h1c7 0 15 3 21 8 16 12 27 39 30 58v3-1c-5-18-14-39-27-53-8-10-18-13-31-14l6-1z" class="B"></path><defs><linearGradient id="Q" x1="428.039" y1="437.37" x2="446.351" y2="429.839" xlink:href="#B"><stop offset="0" stop-color="#c5c3c5"></stop><stop offset="1" stop-color="#ededec"></stop></linearGradient></defs><path fill="url(#Q)" d="M436 462c1-18 0-35-2-53 0-2-1-2 0-3 2-1 3-1 4-1 2 2 2 9 2 12 1 17 1 33-1 49-1-1-2-2-3-4z"></path><defs><linearGradient id="R" x1="495.293" y1="859.444" x2="494.586" y2="842.207" xlink:href="#B"><stop offset="0" stop-color="#787878"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#R)" d="M491 841h1c2 1 7 0 10-1l-5 28-1-1c-3-1-6-1-8-1l3-25z"></path><defs><linearGradient id="S" x1="927.353" y1="968.238" x2="966.741" y2="972.913" xlink:href="#B"><stop offset="0" stop-color="#706f6e"></stop><stop offset="1" stop-color="#99999a"></stop></linearGradient></defs><path fill="url(#S)" d="M926 977l43-18c2 1 1 2 1 4-3 2-7 4-10 5l-17 8c-6 3-10 5-17 7h0v-6z"></path><path d="M1117 1064l-19-13 1-1 18 12 24 14c3 2 8 4 10 7h2l1 2c-2 3-4 5-6 8-2-3-6-6-7-8-1-3-2-5-4-6l-20-15z" class="J"></path><path d="M1148 1086c0-2 0-2 1-3h2v2l-1 2-2-1z" class="B"></path><path d="M1251 502h0c1-2 1-4 1-6v3c0 2 1 4 2 6 0 2-1 3-1 4-1 1-2 2-3 2-5 2-48 3-51 1v-3c2-1 8-1 11-1h24c5 0 10 0 15-1 0-1 1-2 2-4v-1z" class="H"></path><path d="M540 867c1-1 1-3 2-4 1 0 2 1 2 2h1c0 2 0 3 1 5s1 2 1 4l-1 1c0 3-1 7 1 11l1 15-9 4 1-38z" class="M"></path><path d="M380 474h2c2 0 3 0 5 1l-3 18c-1 3-1 6-3 9v1c0 2 8 3 10 4 1 0 2 1 3 3 1 4-1 8-2 12 0 1 0 2-1 3v-4-10l-15-6 3-20c0-4 0-8 1-11z" class="I"></path><path d="M315 702v-2c13 10 22 26 28 41 3 5 5 11 6 16l-1 2 1 1c1 2 0 1 1 3v2 1c-2-3-2-6-5-8h0c-4-11-7-22-13-32-5-9-11-16-17-24z" class="D"></path><path d="M753 429l11 4-7 25-2-2-1-1c-2-2-4-2-7-2l2-6v-2c1-2 1-4 1-6 1-1 1 0 1-2v-2-1c1-2 1-3 2-5zm-387-93c0-2-2-4-4-6 11 1 23 1 33 4 1 1 1 1 2 3l-1 5h-1c-8-1-23-1-29-6z" class="J"></path><path d="M627 158h2c1 3 1 5 1 8-2 3-5 4-7 6-5 4-10 9-16 12-1 0-1 0-2-1v-5c3-7 15-16 22-20z" class="H"></path><path d="M349 757c3 6 4 12 5 18 3 16 5 34 3 50l-1 1-2-1c1-18-2-37-6-56 1 0 1 0 1-1h-1c-1-4-2-7-3-10 3 2 3 5 5 8v-1-2c-1-2 0-1-1-3l-1-1 1-2z" class="E"></path><path d="M340 978c3-2 5-2 8-2 1 1 0 1 0 3 2-1 1-1 3-3h6c0 1-11 29-13 31l-1 1-2-1v-1c0-3 0-4-2-7l3-9 2-5 2-4v-2c-2-2-3-1-6-1z" class="Q"></path><path d="M957 545h0c-2-2-7-5-8-8-1-2-2-3-4-5l1-1 9 6-8-12c3 2 7 3 11 6 4 2 7 6 10 9 3 4 2 9 2 13l-6-5-4-3h-3z" class="R"></path><path d="M1081 1044c1-2 2-3 3-3 5 1 10 5 14 8l1 1-1 1 19 13c-1 1-1 2-1 2-3 0-6-3-9-5h-1-3l-1 1-5-3h-1c-6-4-13-8-15-15z" class="S"></path><path d="M1089 1045c2 2 5 3 6 5 3 5 8 8 12 11h-1-3l-1 1-5-3-2-4c-2-3-5-6-6-10z" class="P"></path><path d="M1081 1044c1-2 2-3 3-3v1c1 1 4 2 5 3 1 4 4 7 6 10l2 4h-1c-6-4-13-8-15-15zm202-603l1 1c0 1 1 1 2 2l2-2c1 0 1 0 2 1l2-1c2 3 1 4 1 7l4-1c-2 5-11 9-15 12l-3-1c0 1-1 1-2 1-2-2-3-4-4-6 1-2 1-1 2-3-2 0-1 1-2 0l4-4 6-6z" class="F"></path><path d="M1273 454c1-2 1-1 2-3-2 0-1 1-2 0l4-4c0 2-1 4-1 6l1 1c3 0 4 0 6-2l1 1c-1 2-3 3-4 5l-1 1c0 1-1 1-2 1-2-2-3-4-4-6z" class="Q"></path><path d="M1286 444l2-2c1 0 1 0 2 1l2-1c2 3 1 4 1 7l-2 4h-3c-3-3-3-7-4-11 0 1 1 1 2 2z" class="T"></path><path d="M1286 444l2-2c1 0 1 0 2 1 1 2 2 3 1 6h-1c-2-1-3-3-4-5z" class="K"></path><path d="M1276 958v1c1 3 1 12 0 15 1 1 5 1 7 1 1 1 1 0 2 0 2-1 3 0 5 0l2 2h0l1 3c2 1 5 2 7 1v1l-29 2-7-1v-1h6v-8l1-9c2 0 2 0 4-1v-5l1-1z" class="S"></path><path d="M1270 974l5 1v2c1-1 1-1 3-1 0 2 0 4-1 6h-7v-8z" class="D"></path><path d="M1272 455l1-1c1 2 2 4 4 6 1 0 2 0 2-1l3 1c-11 8-23 12-35 19 0 1 0 1-1 1h-3l1-2c1-7 13-11 19-14 3-4 6-7 9-9z" class="K"></path><path d="M1263 464c3-4 6-7 9-9 0 2 0 5-2 6h-3l-1 1v2h-3z" class="F"></path><defs><linearGradient id="T" x1="875.486" y1="1244.603" x2="867.352" y2="1224.106" xlink:href="#B"><stop offset="0" stop-color="#d6d6d5"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#T)" d="M886 1206h2c0 5 0 11-1 16-3 11-14 11-21 16-4 2-4 10-5 15l-2 1c-1-1-1-1-2-1v-4c0-3 0-10 2-13 4-5 15-6 21-11 4-5 6-13 6-19z"></path><defs><linearGradient id="U" x1="497.98" y1="499.848" x2="485.84" y2="497.925" xlink:href="#B"><stop offset="0" stop-color="#1d1c1d"></stop><stop offset="1" stop-color="#3a393a"></stop></linearGradient></defs><path fill="url(#U)" d="M485 498h2c2-2 3-3 3-5v-1c4-1 9 0 13 0-2 2-1 8-2 11-1 5-1 10-3 15h-1c0-3 1-7 1-11-3-1-4-1-6 0l-3 6-3-1c0-1 1-1 1-2l-2-1c0-3-1-9 0-11z"></path><path d="M1109 941c6-1 10 3 15 4 8 0 19-2 27 0 0 2 0 3-1 5h-42c0-3 0-6 1-9z" class="C"></path><path d="M858 1173h-1l-18 1c-3 0-7 0-9-1-2 0-2-3-4-4-8-3-17-1-24-8s-6-22-6-31c1 2 1 6 1 9 0 7 2 15 8 20 4 4 9 4 14 6 4 1 8 3 11 4s6 0 9 0c6 0 12 1 18 0l1-2c1 2 0 4 0 6z" class="B"></path><path d="M704 1221h0c4 3 8 9 8 14-2 2-3 1-5 5 2 0 2-1 3-1 1-1 2-1 4-2l1-1-2-2c4-3 6-2 10-2-3 4-3 6-3 11l1 1 13 9c-7-1-13-2-19-5-3-1-6-2-7-4l-1-1h-1l2 3-1 1h-5c0-2 1-4 2-5 1-2 1-2 1-4 1-1 1-2 2-4l1 1c2 0 2 1 3-1l-7-13z" class="S"></path><path d="M459 618c1 3 1 6 1 9 0 7-2 18 1 25 3 9-3 18 5 26h1l4 2c1-1 2-1 2-2l2 1c1 1 5 2 6 3s2 3 3 4v1l3 1c2 1 2 1 4 0l-1 2c-8-2-18 0-25-7-3-3-5-6-5-10-2-7-1-14-1-21v-34z" class="E"></path><path d="M475 679c1 1 5 2 6 3s2 3 3 4v1h-4c-3-1-5 0-6-1-1-3 0-4 1-7zm153-435s1 1 2 1v7c-1 2-25 19-28 21l-2-1c-1-2-1-4-1-6 4-3 8-3 12-4 2 0 4-1 5-2l-11 1c6-6 15-14 23-17z" class="M"></path><defs><linearGradient id="V" x1="482.656" y1="898.653" x2="489.387" y2="872.798" xlink:href="#B"><stop offset="0" stop-color="#040404"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#V)" d="M488 866c2 0 5 0 8 1l1 1-1 3c-1 6-4 12-6 18-1 3-2 7-5 10-2 0-5 1-7 0v-1c5-8 7-19 9-28l1-4z"></path><path d="M488 866c2 0 5 0 8 1l1 1-1 3c-2 1-1 1-2 2l-1 1c-3-1-4-1-6-4l1-4z" class="P"></path><path d="M864 421l-33 2c7-3 15-5 23-8l35-10c-1 2-2 3-3 4-2 2-5 3-8 4-4 2-7 4-11 6h-1l-3 1c1 1 0 1 1 1z" class="E"></path><defs><linearGradient id="W" x1="935.746" y1="541.193" x2="967.457" y2="565.644" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#c2c2c3"></stop></linearGradient></defs><path fill="url(#W)" d="M970 554v14l-22-13c-7-4-14-8-19-14 8 2 14 2 20 6 5 3 14 12 18 12h2l1-5z"></path><path d="M554 594c6 4 9 13 10 20l-1 1c-2-3-4-5-5-8-1 3-4 8-6 10s-10 3-11 6c-1 2 0 5-1 8-1-1-2-1-2-2v-1c0-1 0-3 1-4l-3-4c7-6 14-12 19-20l-1-2v-4z" class="Q"></path><path d="M555 600c0 3 1 7-1 9-2 3-8 6-10 8-1 2-4 4-5 6v1l-3-4c7-6 14-12 19-20z" class="F"></path><defs><linearGradient id="X" x1="791.397" y1="1255.872" x2="800.326" y2="1256.912" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#898989"></stop></linearGradient></defs><path fill="url(#X)" d="M784 1253c3-3 12-11 15-12 1 3 1 6 1 9l1 20c-6-1-14-12-19-16l2-1z"></path><path d="M506 349l-3-9c4 1 12 1 15 4 4 5 8 14 10 20-1 0 0 0-1 1h-1l-1-2c-2-3-4-4-7-4l-8-1-1 1h0l-3-10z" class="V"></path><path d="M506 349c2-1 4-2 7-1 1 0 1 0 2 1l-1 2c-1 0-3 1-4 2 2 2 3-1 4 2v1h2l2 3-8-1-1 1h0l-3-10z" class="S"></path><defs><linearGradient id="Y" x1="266.358" y1="337.901" x2="242.473" y2="346.042" xlink:href="#B"><stop offset="0" stop-color="#605f62"></stop><stop offset="1" stop-color="#878786"></stop></linearGradient></defs><path fill="url(#Y)" d="M264 323c3 0 5 0 7 1 1 2 2 2 2 4s-1 2-2 4c-3 1-5 2-7 3l1 1-1 2c-3 1-6 3-8 6s-4 6-6 10l-1 3-1-1-2 4c0 1 0 2-1 3 0 1-1 3-1 4l-1 2v-2l-1-1c3-15 12-31 22-43z"></path><path d="M621 128c1-1 2-1 4-1v2c-3 0-5 1-8 3-7 6-9 14-10 23-3 2-6 4-10 6-1 1 0 1-2 1-1-4 0-10 0-14 7-6 17-17 26-20z" class="E"></path><path d="M379 405v-1c-3-1-6-1-9-1 0 5 0 26-1 28-3-5 2-29-5-30-3-1-6 0-10-1l1-1 35 4c9 1 19 2 27 5 2 1 2 2 2 5l-4-1c-11-4-25 0-36-7z" class="R"></path><defs><linearGradient id="Z" x1="217.251" y1="780.43" x2="200.226" y2="820.39" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#Z)" d="M214 773v1c0 2-1 3-2 4 1 1 2 1 2 2 1 2 1 2 2 3-2 4-4 9-5 14-1 4-2 8-2 12 0 3 1 11-1 14-2 1-4 1-6 0-1-16 3-36 12-50z"></path><path d="M926 279h3 1c0 2-1 4-2 5-3 0 0-2-2-4v4c-2 2-7 2-10 3l-17 3c-17 5-34 12-50 20-2 1-5 3-7 3 26-16 54-27 84-34z" class="J"></path><path d="M622 276h1c2 2 2 5 3 8-8 6-16 13-25 18h-2c-1-2-1-5-1-7 1-1 4-3 5-4 6-6 12-11 19-15z" class="H"></path><path d="M269 794c1 0 0 0 1 1 0 5-2 8-2 13-1 13 0 25-5 38-2-11-3-21-3-32 1-8 2-14 9-20z" class="D"></path><path d="M563 721h2l1 1c1 9 3 25-4 32-9 10-26 4-38 5-7 0-12 6-17 11l-1 1h-1v-2c11-23 38-3 54-17 6-6 3-22 4-31z" class="I"></path><path d="M228 435h0c4 10 4 34 15 38 2 1 4 1 6 0 5-3 9-7 13-11l1 1c-4 4-8 8-12 11-3 1-6 2-9 2-3 1-5 3-7 5l-6-6-5-10 3 1h2c1 1 2 1 3 1-2-2-1-3-1-5-1-4-3-9-3-13-1-5-1-10 0-14z" class="W"></path><path d="M231 462l4 9c-2 2-4 3-6 4l-5-10 3 1h2c1 1 2 1 3 1-2-2-1-3-1-5z" class="E"></path><defs><linearGradient id="a" x1="791.01" y1="188.392" x2="815.042" y2="194.736" xlink:href="#B"><stop offset="0" stop-color="#969697"></stop><stop offset="1" stop-color="#bbbabb"></stop></linearGradient></defs><path fill="url(#a)" d="M812 187c-1-5-3-8-6-10-4-3-7-3-11-2-3 1-5 3-8 4 2-3 6-5 10-6 5-1 10 0 14 3 4 4 6 9 6 14s-1 10-5 13c-3 4-8 6-13 6-4 0-8-2-11-5-1-1-2-3-3-5l5-1v1c2 2 4 4 6 5h2 1c5 0 8-1 11-5l1-1c1-2 1-4 2-7 0-2 0-3-1-4z"></path><path d="M467 479c5 4 11 7 18 7 9 0 17-6 23-12l-1 13c-5 2-9 1-15 2-1 0-3 1-5 1-2 1-4 0-6 1-4-2-8-1-11-4-2 3-2 6-2 10-1 3-2 17-5 19l3-28 1-9z" class="L"></path><path d="M760 631v-12l18-2c0 4-1 5 2 7 0 3 0 4-2 6-5 4-11 3-16 7l-1 1h-1v-7z" class="U"></path><path d="M942 1109l1 4c5 9 14 16 23 21 11 7 23 14 37 18l13 3 23 2 6 1 1 1-1 1c-24-1-46-5-68-16-12-6-22-15-32-23-2-3-6-5-7-8l4-4z" class="N"></path><defs><linearGradient id="b" x1="721.237" y1="891.296" x2="710.314" y2="847.65" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#b)" d="M714 832c0 2 0 4 1 5 1 4 1 7 1 11h0c2-3 1-9 1-13 1-2 1-4 3-6-1 9-1 18-1 27 0 5-1 12 0 16l1 18v3c-2 1-4 1-6 1v-62z"></path><defs><linearGradient id="c" x1="726.501" y1="925.018" x2="710.175" y2="916.382" xlink:href="#B"><stop offset="0" stop-color="#616162"></stop><stop offset="1" stop-color="#797878"></stop></linearGradient></defs><path fill="url(#c)" d="M714 908h1c3 0 6-1 9-2v30l-11-1v-28l1 1z"></path><defs><linearGradient id="d" x1="1105.664" y1="1065.741" x2="1115.641" y2="1066.193" xlink:href="#B"><stop offset="0" stop-color="#555554"></stop><stop offset="1" stop-color="#6c6b6c"></stop></linearGradient></defs><path fill="url(#d)" d="M1107 1061c3 2 6 5 9 5 0 0 0-1 1-2l20 15c2 1 3 3 4 6 1 2 5 5 7 8h0l-1 1c-4-3-9-6-14-9l-31-23 1-1h3 1z"></path><path d="M483 219l1-2 1 3 42 7c1-2 1-2 1-4l-2-1h1l2 1v11l-35-6c-3 0-9-1-11-3v-4-2z" class="I"></path><path d="M427 1055l-1-1c-1-3-3-9-2-12h4c-1 4 5 11 7 15 5 6 13 11 21 12h12v4c-10 2-20 1-28-4-6-3-11-8-13-14z" class="V"></path><path d="M427 1055v-1c1-2 0-3 0-5h1c0 1 1 2 1 3 1 2 2 4 3 5 1 2 3 4 4 5 2 2 2 5 4 7-6-3-11-8-13-14z" class="X"></path><path d="M265 336c3-1 5-1 8-1 2 3 5 6 6 9 3 6 4 16 3 23h0l-1 1c-1 4 0 11-2 15l-1 1c-1-1-2-1-2-1-1-2 0-12 0-15 0-7-2-20-5-27-2-2-4-3-7-3l1-2z" class="D"></path><path d="M1213 649v-3c7 0 14 1 20 1 5 0 9-1 14 0 0 3 0 6-1 8-4 0-26-1-28 1-1 1 0 2-2 3l-2-1v-5c-1-1-1-3-1-4z" class="G"></path><path d="M878 1250l4 3c7 4 36 10 38 12 0 2 0 3-2 5l-48-13 8-7z" class="C"></path><path d="M645 328c1-3 4-8 6-11l2-2v-3c5 1 9 6 12 9-5 1-10 1-13 3-1 2-2 3-2 5 3 0 7-2 10-2-2 2-2 3-3 6 0 2-1 4-2 5-2 0-4 0-6 1-4 3-6 8-7 12-1-2-1-5-2-8-1 2-1 4-1 7h-1v-6c3-5 6-10 7-16z" class="H"></path><path d="M523 927c6 3 12 9 16 15v1h-5l-1 2c1 4 6 9 10 12s14-1 15 6c1 3 1 5 0 7-1-2-1-5-3-7-1-2-10-1-13-3l-7-7c-5-6-10-11-15-16-1-3 0-7 0-10h3z" class="C"></path><defs><linearGradient id="e" x1="1031.183" y1="1103.29" x2="1041.21" y2="1106.214" xlink:href="#B"><stop offset="0" stop-color="#565657"></stop><stop offset="1" stop-color="#6e6e6d"></stop></linearGradient></defs><path fill="url(#e)" d="M1026 1092c-1-2-1-4-2-6 0-2 0-3-1-4 0-2 0-2 1-3h4c1 0 2 0 3 1 3 4 3 12 5 17 1 4 4 9 5 13 1 5 2 9 2 14h-1l-1 2h-1c-2-9-8-17-10-26l-2-6h-1c-1 0-1-2-1-2z"></path><defs><linearGradient id="f" x1="1024.656" y1="1086.267" x2="1034.298" y2="1094.289" xlink:href="#B"><stop offset="0" stop-color="#2c2c2e"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#f)" d="M1026 1092c-1-2-1-4-2-6 0-2 0-3-1-4 0-2 0-2 1-3h4v1c0 3 3 6 3 9 2 3 2 6 2 10-1 0-2 0-3 1l-2-6h-1c-1 0-1-2-1-2z"></path><path d="M241 576h1v2l-1 3 1 1 1 3c8 0 19-2 26 1h-39c-7 0-16 1-23-1l3-2c1-3 3-4 4-6h8c7 0 13 0 19-1z" class="D"></path><path d="M241 576h1v2l-1 3-31 2c1-3 3-4 4-6h8c7 0 13 0 19-1z" class="B"></path><path d="M339 1014c2 4 1 10 1 13 1 3 1 5 0 7h1v1c0 5 1 8 2 12v3c-3 2-8 4-11 4l-1-1c0-2 1-5 0-8-1-4-1-11 0-16 1-1 3-1 4-1 0-4 3-10 4-14z" class="Q"></path><path d="M339 1014c2 4 1 10 1 13-1 4 0 10-1 13-1 1-1 3-2 4 0-5 0-13-2-16 0-4 3-10 4-14z" class="U"></path><path d="M335 1028c2 3 2 11 2 16v6c-1 1-2 1-3 1-1 1-2 1-3 2 0-2 1-5 0-8-1-4-1-11 0-16 1-1 3-1 4-1z" class="L"></path><defs><linearGradient id="g" x1="454.329" y1="635.512" x2="483.906" y2="651.147" xlink:href="#B"><stop offset="0" stop-color="#797a7c"></stop><stop offset="1" stop-color="#a4a19f"></stop></linearGradient></defs><path fill="url(#g)" d="M466 610l5-4v43c-1 7-2 15-1 22 1 3 1 4 3 7h0c0 1-1 1-2 2l-4-2c-2-7-1-15-1-22 0-15 1-31 0-46z"></path><path d="M489 630c2-1 3-1 5-1 0-3 0-5 1-8v-1 7c2 1 2 1 4 1 2 3 1 8 1 11l-1 1c-2-1-3-1-5-1 2 2 4 1 3 3l-2 2-1-1-1-1-1 2c0 7 1 15-2 21-1 1-2 2-4 2-1-8 0-15 0-22 0-5 0-10 1-14l2-1z" class="S"></path><defs><linearGradient id="h" x1="498.299" y1="633.604" x2="489.297" y2="633.555" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#h)" d="M489 630c2-1 3-1 5-1 0-3 0-5 1-8v-1 7c2 1 2 1 4 1 2 3 1 8 1 11l-1 1c-2-1-3-1-5-1 0-1-2-1-2-2-2 0-2 0-3-1-1-2-1-2 0-4 1 0 3-1 3-1 2 0 1 1 3 0v-1h-6z"></path><path d="M626 302c1 0 1 0 2 1 1 3 1 4 0 7-4 5-10 8-15 12-4 3-8 7-13 9h0c-2-2-2-3-3-5 1-4 5-6 7-8 7-6 14-12 22-16z" class="D"></path><path d="M344 1089l-1-4 1-1c3 3 6 8 9 11 4 11 17 23 26 31 3 3 5 6 9 8l-3 3c-17-10-33-30-41-48zm133-874c2-2 3-3 6-3l1 1h1c4 0 10 1 14 2l27 7 2 1c0 2 0 2-1 4l-42-7-1-3-1 2-1 3-2-1c0-1 0-2-1-3-2-1-2-1-2-3z" class="B"></path><defs><linearGradient id="i" x1="1050.969" y1="1104.711" x2="1074.894" y2="1116.398" xlink:href="#B"><stop offset="0" stop-color="#acaaab"></stop><stop offset="1" stop-color="#efefef"></stop></linearGradient></defs><path fill="url(#i)" d="M1067 1127v-1c-4-12-13-23-19-33-2-5-4-10-7-14v-1-1c3 1 4 3 5 5l12 16c5 8 12 15 17 23-2 1-2 1-3 3s0 3 0 5l-1 1v1l-1 2h-1c-1-1-2-4-2-6z"></path><defs><linearGradient id="j" x1="423.908" y1="547.777" x2="443.99" y2="548.112" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#909090"></stop></linearGradient></defs><path fill="url(#j)" d="M443 519h3l1 1c-5 23-14 45-27 64-3 5-6 9-9 13h-1l-2 3-1-2c12-17 21-34 28-55 3-7 4-17 8-24z"></path><path d="M710 262c-3-4-5-8-8-12 3 1 6 1 9 1 6-1 10-7 15-11 1 3 3 7 3 10 0 5-2 6-4 9s-5 4-8 5v-1c-4-1-3 3-7-1z" class="G"></path><path d="M710 262l12-9c1-1 2-1 3-2h1v5c-1 2-1 1-1 2v1c-2 3-5 4-8 5v-1c-4-1-3 3-7-1z" class="F"></path><path d="M939 1300c1-4 0-8 0-12l34-2 1 1-1 7c-1 0-2 1-3 1h-1c-3 0-4 1-6 1h-1l-1 1c-2 0-3 0-4 1-3 1-7 0-10 1-3 0-5 0-8 1z" class="M"></path><defs><linearGradient id="k" x1="570.57" y1="276.601" x2="550.901" y2="272.611" xlink:href="#B"><stop offset="0" stop-color="#cfcdcd"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#k)" d="M560 220c1 0 1 1 2 1 1 19 0 38 1 57v31c0 5 1 10 0 16-2 0-1 1-3 0V220z"></path><path d="M770 842h14c-1 5-1 10 0 15v1l-11-2c-4-1-8-2-11-4-5-2-6-5-7-9l15-1z" class="C"></path><path d="M724 1349c4 0 8 0 12 1-2 4-1 8-2 13 0 5 0 10 1 15l-12-1c0-5 1-21-1-24 0-2 1-2 2-4z" class="M"></path><path d="M724 735c2 3 1 8 1 12v22l-1 95c-2 6-2 13-3 19 0 2 0 5-1 7l-1-18c-1-4 0-11 0-16 0-9 0-18 1-27 1-2 2-3 4-5v-89z" class="I"></path><path d="M720 829c1-2 2-3 4-5 0 3 1 14-1 15-3 5-2 16-2 22 0 4-1 8-2 11-1-4 0-11 0-16 0-9 0-18 1-27zm-115 498l1 2c-1 2-3 5-4 7s-1 6-3 8c-1 2-5 4-7 6l-12 9c-2 2-4 1-7 1l-1-1v-3c1-4 6-7 9-10l24-19z" class="D"></path><path d="M677 375h2l1 1h1c2 1 3 3 4 5l-12 31-10-3c4-4 5-10 7-16l7-18z" class="J"></path><path d="M355 532c-8-2-19-2-28-3l3-12 27 2c0 3 0 10-2 13z" class="S"></path><path d="M610 1217c-6 4-27 24-33 24-1-2-1-5-1-7 4-6 14-12 19-16 4-4 8-7 12-10l2 2c0 2 0 4 1 7z" class="H"></path><path d="M947 739l-14-3c12-5 23-9 36-13v18l-17-1-5-1z" class="U"></path><path d="M947 739v-1l1-4c2-2 2-3 5-4l1 1c-1 3-1 5-3 7l1 1v1l-5-1z" class="Q"></path><path d="M224 465c-1-4-2-7-4-10 2 10 4 19 8 28l-3-3c-6-9-9-26-7-37v-1c1-2 2-5 3-7 2-2 3-3 5-3l2 3c-1 4-1 9 0 14 0 4 2 9 3 13 0 2-1 3 1 5-1 0-2 0-3-1h-2l-3-1z" class="B"></path><path d="M698 1284l-54-45v-5c9 5 17 13 25 20l31 26v1c2 4 7 6 10 10-2-1-2-1-3-2-3-3-6-5-9-5z" class="E"></path><path d="M889 405l13-4c0 7-4 14-7 20h-31c-1 0 0 0-1-1l3-1h1c4-2 7-4 11-6 3-1 6-2 8-4 1-1 2-2 3-4z" class="J"></path><path d="M466 610c1 15 0 31 0 46 0 7-1 15 1 22h-1c-8-8-2-17-5-26-3-7-1-18-1-25 0-3 0-6-1-9v-2c0-2 5-5 7-6z" class="D"></path><path d="M753 1225h4c2-3 0-4 4-6 4 0 10 2 13 4l1 3h-1l-1 18h-1-2c-1 0-3-2-4-3-5-3-9-7-14-11l1-1v-4z" class="F"></path><path d="M800 847c-1-2-1-4-1-7 7 1 12 1 17 3l23 8c-12 5-24 8-37 8l13-15c-1 0-4 0-6 1-1 1-3 2-4 3-2 0-4-1-5-1z" class="B"></path><path d="M774 1279c-5-4-10-9-15-13l-16-12c-5-4-9-7-12-12 3-1 4 0 6 1 14 7 26 18 38 28 3 2 7 5 9 8v1c-4 0-6-2-10-1z" class="M"></path><defs><linearGradient id="l" x1="671.689" y1="241.796" x2="687.212" y2="262.037" xlink:href="#B"><stop offset="0" stop-color="#b9b9b9"></stop><stop offset="1" stop-color="#dcdadc"></stop></linearGradient></defs><path fill="url(#l)" d="M672 169l1 23c0-4 0-8 1-12v31c0 7-1 15 0 22 1 3 3 6 4 9 1 4 1 8 2 11 2 4 4 6 6 9-3-14-2-26-2-40 0-4-1-10 0-14h1c2 18-2 37 5 54-1 1-2 1-3 2 1 2 3 2 4 3l-4 2-5-5c-4-5-7-10-9-17-1-5-1-11-1-17v-30c0-10-1-20 0-31z"></path><path d="M981 1051l1-1 4 6-9 45c-1 1-2 3-2 4h-3-1c1-5 2-11-2-15l12-39z" class="F"></path><path d="M981 1051c1 2 1 3 1 5 0 1 0 1-1 2-1 4-3 7-4 11v3c-2 6-2 16-1 22 1 3 1 5 1 7-1 1-2 3-2 4h-3-1c1-5 2-11-2-15l12-39z" class="X"></path><defs><linearGradient id="m" x1="581.937" y1="396.646" x2="565.735" y2="410.793" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#m)" d="M560 384l16 2c1 7 2 15 5 22l-1 1c-2 4 0 11 0 15-2 1-1 0-3 1v5c-1-1-1-2-2-3 0-6 1-19-3-24-2 1-1 4-4 6l-2-1 1-1-3-13c0-4-2-6-4-10z"></path><path d="M1182 1042l1-2 2 1c3 2 6 4 8 7l-1 1-1 1v1l2 2c1 3-5 13-8 16l-8 14c0-5-1-9 1-14h1c0-3-2-5-3-8l2-1v-4l-1-1c2-1 2-3 3-5l1-3-1-2 2-3h0z" class="Y"></path><path d="M1182 1042l1-2 2 1c3 2 6 4 8 7l-1 1-1 1c-3-2-7-5-9-8z" class="O"></path><path d="M1178 1056h1v-1c0-1 2-3 3-4 0 3-2 4-2 6l1 3c1 4 0 9-1 13h-1v-4c0-3-2-5-3-8l2-1v-4z" class="P"></path><path d="M1191 1051l2 2c1 3-5 13-8 16l-1-1v-11c2-3 4-4 7-6z" class="M"></path><path d="M554 1034l3 1c2 4-1 15 2 17l1-1c-1 3-4 11-3 14h-2c-2 3-3 9-4 13l1 1c-1 2-2 4-3 5-1-2 0-3 0-5-3 3-5 10-7 14l-2-2-1-3-1-1c2-4 4-7 6-11 6-14 8-27 10-42z" class="K"></path><defs><linearGradient id="n" x1="342.085" y1="838.927" x2="356.227" y2="844.193" xlink:href="#B"><stop offset="0" stop-color="#c7c5c6"></stop><stop offset="1" stop-color="#eeedee"></stop></linearGradient></defs><path fill="url(#n)" d="M345 819l1 1v5h1c2 0 2 0 3 1h2 4l1 1c0 11-2 22-4 32l-11-2c1-13 3-25 3-38z"></path><path d="M664 412l9 2-16 55c1-6 1-12 1-18l-2-17c0-1-1-5 0-5 0-1 0-1 1-1 3-3 4-5 4-9v-3h0l3-4z" class="B"></path><defs><linearGradient id="o" x1="594.937" y1="364.125" x2="617.346" y2="372.935" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#8f8f8e"></stop></linearGradient></defs><path fill="url(#o)" d="M608 387l-7-14c-1-2-3-4-3-7-1-5 0-12 1-17l19 24v2l-1 4-1-1v1a30.44 30.44 0 0 1-8 8z"></path><defs><linearGradient id="p" x1="237.147" y1="877.021" x2="232.782" y2="890.493" xlink:href="#B"><stop offset="0" stop-color="#cfcecf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#p)" d="M213 869c2-1 3-2 5-2l1 1c3 7 8 16 17 19 4 2 9 2 14 2 11 0 23 0 35 1l-2 4h-34c-4 0-11 0-15-1-3-1-6-4-8-5-4-4-7-8-10-13l-3-6z"></path><path d="M213 869c2-1 3-2 5-2l1 1c-1 1-1 1-1 2l-1 2-1 3-3-6z" class="H"></path><defs><linearGradient id="q" x1="376.346" y1="297.717" x2="402.937" y2="299.447" xlink:href="#B"><stop offset="0" stop-color="#858485"></stop><stop offset="1" stop-color="#9e9e9e"></stop></linearGradient></defs><path fill="url(#q)" d="M402 317c-7-9-15-14-26-19l-1-13c14 5 22 11 31 22-2 3-3 6-4 10z"></path><path d="M479 732c2 0 3 1 5 1 11 14 13 32 19 47v1h-11c-1-11-3-22-7-32-2-6-5-12-6-17z" class="C"></path><path d="M1215 386c1 6 7 14 10 19 5 10 10 22 16 31v2h-2c-4-5-7-14-10-20-7-12-16-24-21-38l18-9c3-2 6-3 8-6v-2c4 2 6 8 8 12l20 36-1 2c-3-8-8-15-11-22-5-7-9-15-14-22-6 3-14 7-19 12l-2 2v3z" class="B"></path><defs><linearGradient id="r" x1="904.014" y1="1337.524" x2="903.355" y2="1284.374" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#r)" d="M900 1284h7c3 4 0 46 0 54v1c-2 0-3 1-5 0-1-7-1-15-1-22l-1-33z"></path><path d="M444 980h3c2 1 3 2 4 3 10 0 19 1 28 3 5 1 8 3 12 6 2 4 5 8 8 11l5 5c1 2 2 4 2 7-1 2-3 3-5 4-3-9-7-17-15-23-11-9-26-9-40-7-1-3-1-6-2-9z" class="D"></path><path d="M1166 1171c2 2 3 3 4 5-22 26-49 46-79 62-11 7-24 13-37 14h-2-1l1-1-6-2h1 7 2c16-4 32-13 46-20 8-4 16-9 23-14 11-8 21-17 30-27 4-3 9-8 12-12 0-1-1-3-1-5h0z" class="I"></path><path d="M985 1075c1 2 1 5 0 7v1c1 3 3 3 4 7-1 5-3 15 0 19l1 1c0 6-1 11-4 16l-1 1-6-3c1-3 1-3 0-6-2-2-2-4-4-5l1-4 5-17c0-5 3-12 4-17z" class="P"></path><path d="M981 1092c2 1 2 1 3 3 1 4 1 10 1 15-1 3-4 12-2 14 0 1 2 2 3 2l-1 1-6-3c1-3 1-3 0-6-2-2-2-4-4-5l1-4 5-17z" class="E"></path><path d="M546 382c5 0 9 1 14 2 2 4 4 6 4 10l3 13-1 1c-2-3-5-6-9-6-3 0-6 3-8 5-2-2-2-5-3-8-1-5-2-9-4-13 0-1 0-2-1-3l5-1z" class="D"></path><path d="M546 385c3-1 7 0 10 0 1 3 4 9 2 12-1 2-3 2-5 3l-4 4-2-5c-1-3-2-11-1-14z" class="B"></path><path d="M214 556c7 0 9 0 14 5-3 1-4 3-7 6 1 1 2 1 2 2 2 1 2-1 3 2 1 1 2 1 3 2h17l-4 1c-1 1-1 1-1 2-6 1-12 1-19 1h-8 0c-2-1-4-1-5-2v-2h2v-1l-2-1-3-2-2-2c0-3 1-3 2-5l3-3 4-2 1-1z" class="P"></path><path d="M213 557h7 0l-4 3c-2 0-4 0-7-1l4-2z" class="X"></path><path d="M209 559c3 1 5 1 7 1l9 1v1l-4 2c-1-1-3 0-6-1-3 0-6-1-9-1l3-3z" class="R"></path><path d="M229 573c-4 1-12 3-15 1l2-3c1 0 2 0 3-1v-1l2-2c1 1 2 1 2 2 2 1 2-1 3 2 1 1 2 1 3 2z" class="L"></path><path d="M206 562c3 0 6 1 9 1 3 1 5 0 6 1-4 4-10 6-15 5l-2-2c0-3 1-3 2-5z" class="D"></path><defs><linearGradient id="s" x1="824.153" y1="154.172" x2="837.524" y2="171.229" xlink:href="#B"><stop offset="0" stop-color="#919090"></stop><stop offset="1" stop-color="#d1d0d1"></stop></linearGradient></defs><path fill="url(#s)" d="M858 155c3-1 6-1 9-2v1c-2 2-3 4-3 7l-6 3h-2c-3 1-7 0-10 2v2c-5-1-7-1-11 0-4-1-11 0-14 1-4 0-7 0-9 2-4-2-8-2-13-3 10-3 22-5 33-7l26-6z"></path><path d="M858 155c3-1 6-1 9-2v1c-2 2-3 4-3 7l-6 3h-9c-1-1-2-1-3-1l1-1h0c4-1 8-3 10-6l1-1z" class="N"></path><defs><linearGradient id="t" x1="1308.289" y1="574.414" x2="1310.739" y2="542.16" xlink:href="#B"><stop offset="0" stop-color="#959495"></stop><stop offset="1" stop-color="#cecccd"></stop></linearGradient></defs><path fill="url(#t)" d="M1300 578c-4-8-5-20-4-28v-1c1-2 1-4 3-5 6-5 19-2 26-1l1 1-1 1c-3 0-7 1-11 1 4 1 8 0 11 1-5 1-14-1-18 3-2 2-3 5-4 7v11c1 8 4 12 8 18-5-1-8-4-11-8z"></path><path d="M1300 578c0-1 1-2 2-3v-5c0-6-1-15 3-20 2-2 6-3 9-4 4 1 8 0 11 1-5 1-14-1-18 3-2 2-3 5-4 7v11c1 8 4 12 8 18-5-1-8-4-11-8z" class="L"></path><path d="M1106 1000l-1-2c-1-2 1-5 2-8h0l-2-1v-2c6 2 30 12 33 15l-1 1c-1 2-1 4-1 6v1l1-1c0-1 1-2 1-2h1l-3 6-30-13z" class="F"></path><path d="M1252 608c-4-5-8-9-9-15 0-2 0-1 1-2h16l2 11h17c5 0 13-1 18 0v4c-6 2-16 1-23 1-7 1-14 1-22 1z" class="Y"></path><path d="M1279 602c5 0 13-1 18 0v4c-6 2-16 1-23 1-4-1-10 1-14-2l1-2 17-1h1z" class="M"></path><path d="M1257 370h1c8 9 14 24 19 35-3 3-6 4-9 6-5-9-9-19-14-28-2-3-4-5-5-8 4 0 7-1 8-5z" class="W"></path><path d="M615 1232h1c-1 2-2 3-2 6l2 1 1-1 1 1c-1 3-7 7-10 9l-27 22c-2 1-2 1-4 1-1-3-1-5-1-8 4-5 11-10 17-14 7-6 14-12 22-17z" class="N"></path><defs><linearGradient id="u" x1="1107.134" y1="1014.122" x2="1120.469" y2="1047.521" xlink:href="#B"><stop offset="0" stop-color="#464344"></stop><stop offset="1" stop-color="#939495"></stop></linearGradient></defs><path fill="url(#u)" d="M1131 1047l-39-22 3-9 14 7 14 7-1 2c2 1 2 1 4 1v1h1c4 2 6 4 8 6l1 1h-1l-4 2v4z"></path><defs><linearGradient id="v" x1="530.844" y1="1177.357" x2="563.667" y2="1195.015" xlink:href="#B"><stop offset="0" stop-color="#9e9c9f"></stop><stop offset="1" stop-color="#c3c4c1"></stop></linearGradient></defs><path fill="url(#v)" d="M545 1232l1-85 6-5-3 64c0 10 0 20-1 29 0-2-1-4-1-6l-2 3z"></path><path d="M554 1105c-8 11-16 22-26 31-3 2-6 5-10 5l-1-2c1-5 11-13 15-18 5-7 9-17 15-23 2-2 5-1 8-1 1 3 1 4-1 7v1z" class="B"></path><path d="M1232 887l21 1-1 19-1 2h-20l1-22z" class="I"></path><path d="M614 1292h2c1 3 1 4 1 7-4 5-11 9-16 13-7 6-14 13-22 18h-2c-1-2-1-5-1-8 4-5 10-9 15-12 8-7 15-13 23-18z" class="E"></path><defs><linearGradient id="w" x1="710.139" y1="1181.5" x2="725.515" y2="1170.926" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#w)" d="M731 1216c-6-11 0-24-4-34-3-7-9-13-12-19-10-15-19-34-24-52 2 2 3 5 4 8l10 19 15 25c4 7 11 14 13 22 3 8 1 19 1 28l-3 3z"></path><defs><linearGradient id="x" x1="1157.864" y1="1080.416" x2="1170.687" y2="1078.52" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#x)" d="M1180 1045l1 2-1 3c-1 2-1 4-3 5l1 1v4l-2 1c1 3 3 5 3 8h-1c-2 5-1 9-1 14l-6 8v-1c-1-3-1-6-1-9l-3 10c-2 0-6 1-8 0 0-1-1-1-2-2l-2-3 1-3 1-1c3-3 6-8 8-11l1-2c2-3 4-5 7-9 1-2 1-2 1-4 3-3 4-7 6-11z"></path><path d="M1156 1083c3 2 2 2 3 5l-2 1-2-3 1-3z" class="K"></path><path d="M1170 1081c3-4 0-12 5-16 2 1 2 3 3 4-2 5-1 9-1 14l-6 8v-1c-1-3-1-6-1-9z" class="W"></path><defs><linearGradient id="y" x1="822.487" y1="1057.265" x2="807.462" y2="1034.954" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#929192"></stop></linearGradient></defs><path fill="url(#y)" d="M818 1027h3l1 2c1 10-1 21 0 31 0 2-1 7-2 7-1 3-2 3-4 5h-2c-1-2-1-3-1-5l-1-6h-1l-1 3 1-33c3-1 7-1 9-2l-2-2z"></path><path d="M822 1029c1 10-1 21 0 31 0 2-1 7-2 7-1 3-2 3-4 5h-2c-1-2-1-3-1-5 1 0 1 0 2-1 1 0 3-2 3-3 1-1 0-5 0-6 1-9 0-18 2-27l2-1z" class="X"></path><path d="M1221 1163c2 2 5 6 6 9-5 9-13 15-19 23-4 5-7 11-12 16l-2-13h3c4-4 10-12 11-18 0-2 0-7 1-9s9-5 12-8z" class="O"></path><path d="M848 898v3c0 2 0 4 1 7-2 6-8 12-12 18l-10 16c-1 2-4 5-5 7 0 3 1 7 0 10-1-9 0-18 0-27-1-3 0-8-1-11l-3-1c1-1 2-1 3-1 4 0 6 1 9-1 8-5 16-10 18-20z" class="U"></path><path d="M615 1261l2-1v1c1 1 2 4 2 5-1 5-34 30-40 34-1 0-1 1-3 0v-7c5-6 12-11 18-15 7-6 14-12 21-17z" class="E"></path><defs><linearGradient id="z" x1="1213.645" y1="1025.491" x2="1200.823" y2="984.941" xlink:href="#B"><stop offset="0" stop-color="#777879"></stop><stop offset="1" stop-color="#a5a3a2"></stop></linearGradient></defs><path fill="url(#z)" d="M1213 969c2-2 4-3 6-4-2 17-6 33-11 49-2 7-4 15-7 22-2 0-3-2-5-3h2l3-9-3-2c6-17 11-35 14-53h1z"></path><path d="M1213 969c2 2 2 4 1 7 0 3-2 7-3 10l-10 38-3-2c6-17 11-35 14-53h1z" class="H"></path><path d="M659 297c-2 1-5 4-6 5l-1-22c0-1 1-2 1-2 0-1 15-12 16-14l3 6 1 3c1 2 3 2 5 2-6 6-10 13-16 19 0 0-2 3-3 3z" class="Y"></path><path d="M883 1160l-3 1c-3-4-2-24-2-30 6 1 12 1 17 0v21c0 3 0 7-1 10-1-1-3-1-4-2h0l-5-1-2 1z" class="B"></path><path d="M1018 1139l8 1h6l2 1h3 7c0 3-1 14 1 17l-6-1-23-2c-1-5-1-10 0-15l2-1z" class="T"></path><path d="M1037 1141h7c0 3-1 14 1 17l-6-1 1-1c-2-5-2-10-3-15z" class="F"></path><path d="M800 1027h18l2 2c-2 1-6 1-9 2l-1 33v6c0 2 1 4 0 5l-1 1c-1-1-1-1-2-3 0-1-1-2-1-4l-1-3-1-1h-2c-2 1-2 2-3 3l1-8v-12-21z" class="E"></path><path d="M800 1027h18l2 2c-2 1-6 1-9 2v-2c-3-2-7-1-10-1l1 2c2 0 2 0 4 1v1c-1 2-1 3-1 4l1 2c0 1 0 1 1 3v17c-1-4-1-9-2-12-1-2-2-4-4-5v3l-1 4v-21z" class="H"></path><path d="M800 1048l1-4v-3c2 1 3 3 4 5 1 3 1 8 2 12 0 3 0 10 2 12h1c0 2 1 4 0 5l-1 1c-1-1-1-1-2-3 0-1-1-2-1-4l-1-3-1-1h-2c-2 1-2 2-3 3l1-8v-12z" class="P"></path><path d="M800 1060h3c3 3 3 6 3 9l-1-3-1-1h-2c-2 1-2 2-3 3l1-8z" class="K"></path><defs><linearGradient id="AA" x1="332.948" y1="786.528" x2="363.003" y2="810.022" xlink:href="#B"><stop offset="0" stop-color="#989798"></stop><stop offset="1" stop-color="#cdcdcc"></stop></linearGradient></defs><path fill="url(#AA)" d="M344 770h0l1-1h3c4 19 7 38 6 56l-2 1h-2c-1-1-1-1-3-1h-1v-5l-1-1c0-13-1-25-3-38l-2-10 4-1z"></path><path d="M340 771l4-1 2 1h-1c-2 3-2 7-3 10l-2-10z" class="I"></path><defs><linearGradient id="AB" x1="742.988" y1="858.761" x2="742.909" y2="798.162" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#d4d3d3"></stop></linearGradient></defs><path fill="url(#AB)" d="M739 818c1-5 0-10 1-15v-4c4-1 5 0 8 2l-1 49v10l-1 1h-6c-2-4-1-10-1-15 0-9-1-19 0-28z"></path><defs><linearGradient id="AC" x1="909.305" y1="1238.55" x2="895.296" y2="1207.055" xlink:href="#B"><stop offset="0" stop-color="#2c2c2d"></stop><stop offset="1" stop-color="#4a4949"></stop></linearGradient></defs><path fill="url(#AC)" d="M878 1250c5-6 10-11 14-17 8-12 16-27 27-36 3-3 7-5 10-7-8 14-36 58-47 63l-4-3z"></path><path d="M442 942h3c0 3-1 7-2 10l-4 4c-7 3-16 4-24 7-9 3-16 8-25 11h-4c-1-1-2-3-2-5h1 3c3-1 6-3 10-4l15-8c3-2 8-3 10-6l2-3c5 0 12-3 17-6z" class="X"></path><path d="M1153 1083l3-2 1 1-1 1-1 3 2 3c1 1 2 1 2 2 2 1 6 0 8 0l3-10c0 3 0 6 1 9v1c-16 25-43 46-70 57-8 3-17 7-25 8h-2c-4 1-10 1-14 3h-9l-1-1c2-1 5-1 7-1h1c3-1 5-1 7-1l9-2c5-1 11-2 16-4l19-8c18-8 36-23 49-37l-7-10-3-2h0c2-3 4-5 6-8l-1-2z" class="S"></path><path d="M1153 1083l3-2 1 1-1 1-1 3 2 3c1 1 2 1 2 2v4c2 4 5 2 1 7-4-1-4-7-9-7l-3-2h0c2-3 4-5 6-8l-1-2z" class="G"></path><defs><linearGradient id="AD" x1="711.764" y1="516.448" x2="714.836" y2="474.924" xlink:href="#B"><stop offset="0" stop-color="#989898"></stop><stop offset="1" stop-color="#cbcacb"></stop></linearGradient></defs><path fill="url(#AD)" d="M715 452c0 6-2 10-2 16 0 2-1 4-1 6 2 1 5 1 7 2 1-1 1-3 1-5 0-1 0-1 1-2v-3c0-1 1-2 1-3 0-2 0-1 1-2 0-2-1-3 0-4l1-4c0-1 0-2 1-3l1-1-9 70c-1-1-1 0-1-2v-1c-3 0-5 0-8 1l-1-4c1-20 2-42 8-61z"></path><path d="M1245 1115l1 1c2 2 4 5 4 9-1 2-2 3-2 6l-3 9 1 1v-3l1-1 2-2c0-1 1-2 1-2 2-4-1-7 4-9l2 1-1 4c1-1 2-2 3-4l2-2h1c-3 4-5 8-9 10h-1v1c-1 1-3 3-3 4l-18 28c-1-2-3-4-4-6l-1-1c3-7 8-13 11-20l9-24z" class="F"></path><path d="M732 568h-2c-2-2-1-10-1-14l1-44c6 1 11 1 17 1l1-1c5 2 10 3 14 4l17 4v4h-1c-2 0-3 0-4-1l-24-3h-5c-3 1-5 2-7 5h-3l-1-1c-2 0-1 0-2 1-1 4 0 10 0 14v30 1z" class="D"></path><path d="M745 518c1-1 2-1 3-2h-2v-2c2 0 3 0 6 1l18 4c3 1 6 1 8 3-2 0-3 0-4-1l-24-3h-5z" class="Y"></path><path d="M262 462c7-9 11-21 16-31 3-5 5-10 8-14 2-2 3-3 5-3 1 1 2 1 2 2 5 10 0 26-3 36l-3 1v1l-1-1-1-4c3-6 4-14 5-20-3 4-5 9-7 14-5 1-6 3-9 8h0l-11 12-1-1z" class="C"></path><defs><linearGradient id="AE" x1="700.922" y1="1071.169" x2="690.815" y2="1042.684" xlink:href="#B"><stop offset="0" stop-color="#5b5b5b"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#AE)" d="M713 1021c0-4-1-9 0-13h2v8h1c0 11 3 47-4 54-1 1-3 2-4 2-4 1-9 1-14 1h-27c-4-1-7-1-11-2 5-1 9 0 12-4 2-1 4 0 6 0 7 0 29 2 34-3 7-8 4-32 4-42l1-1z"></path><path d="M713 1021c0-4-1-9 0-13h2v8c0 4 1 12-1 16h-1v-11z" class="M"></path><path d="M741 1158c-9-15-17-30-24-46-2-4-5-9-5-14 2 0 3-1 5-2h1c1 0 2 3 3 5 4 10 8 20 14 30 2 5 6 11 9 16l1 3c-1 2-1 3-2 5s0 1-1 3h-1z" class="G"></path><path d="M411 715c2-1 1-1 3-1v1c1 1 1 2 2 2 2 3 0 0 1 3l2 1 2 3 1 1 6 11 3 6c1 1 1 3 2 5l1 3v1c1 1 1 1 1 2l2 4v3c1 8 3 16 4 24 3 20 2 41 1 61 0 5 1 14-2 18-1-9 0-20 0-30 0-38-3-76-24-109v-2c-2-2-3-4-5-7z" class="B"></path><defs><linearGradient id="AF" x1="795.826" y1="184.459" x2="806.752" y2="196.692" xlink:href="#B"><stop offset="0" stop-color="#797878"></stop><stop offset="1" stop-color="#9e9d9e"></stop></linearGradient></defs><path fill="url(#AF)" d="M800 180c3 0 7 1 9 3 1 1 2 2 2 4h1c1 1 1 2 1 4-1 3-1 5-2 7l-1 1c-3 4-6 5-11 5h-1-2c-2-1-4-3-6-5v-1l-5 1v-1l-1-1c-1-3 1-8 3-11 4-4 8-5 13-6z"></path><path d="M785 198l-1-1c-1-3 1-8 3-11 4-4 8-5 13-6v1l-1 3-5 2-1 1 2 2v2c-1 1-3 1-4 1-1 2-1 2-1 4 2-1 2-1 3-2 2 3-1 6 5 7 3 1 6 0 9-2 2-1 3-3 4-5 0-1 1-2 2-3-1 3-1 5-2 7l-1 1c-3 4-6 5-11 5h-1-2c-2-1-4-3-6-5v-1l-5 1v-1z" class="S"></path><path d="M785 198l-1-1c-1-3 1-8 3-11 4-4 8-5 13-6v1l-1 1c-3 1-6 2-9 4 1 2 2 2 4 4l-1 1h-1c-2 0-4 0-5 2-1 1-2 3-2 5z" class="X"></path><path d="M565 1029c1-2 0-5 0-7-3-16-8-29-14-42 3 3 7 5 11 7l1 1c10 18 12 38 11 59l-1 1c-2-1-4-2-6-4l-2-15z" class="C"></path><defs><linearGradient id="AG" x1="709.954" y1="567.12" x2="712.312" y2="516.017" xlink:href="#B"><stop offset="0" stop-color="#636363"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#AG)" d="M707 513l1 4c3-1 5-1 8-1v1c0 2 0 1 1 2l-1 50h-9c-2-4-1-10-1-15l1-41z"></path><defs><linearGradient id="AH" x1="1037.103" y1="1112.769" x2="1057.766" y2="1123.142" xlink:href="#B"><stop offset="0" stop-color="#aaa7a8"></stop><stop offset="1" stop-color="#d5d5d5"></stop></linearGradient></defs><path fill="url(#AH)" d="M1045 1101l11 17 5 11 6 9-2 1-7 1h-3v1h-11c1-1 2-1 3-1h2c-3-5-5-11-6-16 0-5-1-9-2-14l1-1c-1-2-2-4-2-6h0c2-1 3-2 5-2z"></path><path d="M1042 1109l9 26 1 5h2 1v1h-11c1-1 2-1 3-1h2c-3-5-5-11-6-16 0-5-1-9-2-14l1-1z" class="G"></path><path d="M1051 1135l2-1c0-2 1-4 0-5 0-4-4-8-5-13v-1-1h1l3 4h4l5 11 6 9-2 1-7 1h-3-1-2l-1-5z" class="E"></path><path d="M1056 1118l5 11-3-1c-3-1-5-7-6-10h4z" class="H"></path><path d="M1054 1140c-1-3 1-6 2-8 2 1 5 2 7 4 2 1 1 1 2 3l-7 1h-3-1z" class="B"></path><path d="M379 405c11 7 25 3 36 7-1 1-1 2-3 2-1 4 0 8 0 12l-20-1c-4 0-9 0-13-1v-6-13z" class="C"></path><path d="M906 603c2-1 7 1 9 2v58l-9 1v-61z" class="J"></path><defs><linearGradient id="AI" x1="537.282" y1="449.346" x2="525.947" y2="420.438" xlink:href="#B"><stop offset="0" stop-color="#686868"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#AI)" d="M521 420c6 2 9 2 16 1 0 3-1 9 0 11l1 1c2-2 2-4 3-6l1-1h1l1 17c0 2 1 5 0 7-6-2-12-3-18-3l-1-1-4 1v-27z"></path><path d="M422 1149c12 4 24 5 37 4 8 0 15-2 22-4 2 0 8-1 9-1 1 1 5 7 6 8l4 7-2 1c-3-1-7-6-10-8s-13 3-17 4c-12 2-24 2-37 1l-2-3c-2-4-6-4-10-5l-1-1 1-3z" class="B"></path><path d="M1109 601c0-3 1-4 3-6v-1c-3 0-3 0-4-1 1-1 1-1 3-2 3 0 8 1 12 0 7-1 15 0 22 0h36 16c2 0 4-1 6 0h2c-5 3-17 2-23 2-2 0-3 1-5 1-4 0-11-1-14 0-2 1-4 0-6 0h-18l-8 1v1l1 2h7 1c1 0 1 0 2-1h31c5 0 11 0 17 1h0 4l1-1c-3-2-16-1-20-1v-1c5-1 9 0 14 0 3 0 7 0 9 1h1c-1 2-1 2 0 5l-2-2h0c-4 2-15 0-20 0-14 0-30-1-44 1-2 0-3-1-5-1l-19 2z" class="K"></path><path d="M241 439c1 4 3 7 6 10 2 1 3 1 5 0 3-1 6-5 8-8 6-11 10-24 13-37 1-6 1-13 3-18l2-1 1 1c-2 18-10 51-22 64-3 3-5 6-10 6-3 0-4-1-7-3-10-11-8-32-7-46l1-14 2 1 1 6-1 9v13c1 5 1 12 3 16l2 1z" class="B"></path><path d="M234 393l2 1 1 6-1 9v13c-1-4 0-8-1-13v-2h-2l1-14z" class="R"></path><path d="M238 762c11-2 23-1 33-1 3 0 6 1 9 1l3-3c4 3 4 9 8 13l2 2c-14-2-28-3-42-4-10 0-19-1-29 3l1-2c4-4 10-6 15-9z" class="C"></path><path d="M745 217c4 4 8 8 10 13 4 9 3 20 0 28-5 11-14 19-24 22-8 3-18 4-26 1 1-1 3-4 4-4h4c9 0 18-1 25-7 9-7 14-16 14-27 0-2 0-5-1-7-1-4-4-8-5-12v-3-1l-7 4 6-7z" class="J"></path><path d="M406 975c8-4 17-6 26-8 3 7 6 15 10 23h0l-1 1-2-1c-6 0-9 2-13 6-8-6-13-14-20-21z" class="D"></path><defs><linearGradient id="AJ" x1="842.118" y1="461.88" x2="881.468" y2="427.306" xlink:href="#B"><stop offset="0" stop-color="#6e6e6f"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#AJ)" d="M890 431c-2 5-5 12-5 18v1l-1-1c-19 0-39 4-58 3 9-5 19-7 29-10l35-11z"></path><path d="M664 887c-2-3-3-4-7-5l-2 1-1-4c-2-1-4-1-6-1-2 3 0 5-2 7-1 2-1 5-1 7 2 2 3 1 4 2s1 8 1 8l-12 1v-37c10 0 18 0 27 3 0 6 0 12-1 18h0z" class="D"></path><path d="M755 1244l-1-3c1-1 2-1 4 0l16 13 1-5 7 5c5 4 13 15 19 16-1 2-2 2-3 4 3 2 5 4 8 4h2c1 0 2-1 3-1l11 3c2 0 4 1 5 0l2-1v4h-2c-7 1-19 1-25-1-7-2-13-8-18-13l-29-25z" class="B"></path><path d="M775 1249l7 5c5 4 13 15 19 16-1 2-2 2-3 4-9-6-17-14-24-20l1-5z" class="Q"></path><path d="M1004 1100l1-25h1l1 16 5 8c1 2 1 2 1 3s1 2 2 4c1 0 0 0 1-1l1 8 1 9v17l-6-2h-1l-10-3c3-6 3-15 3-22v-12z" class="L"></path><path d="M1007 1091l5 8c1 2 1 2 1 3s1 2 2 4c1 0 0 0 1-1l1 8 1 9v17l-6-2h2c1-2 1-4 1-6 0-7-1-13-2-20 0-3-1-6-1-9h-2c-2-4-3-7-3-11z" class="K"></path><path d="M1013 1111c4 7 1 13 4 20 1-3 0-6 1-9v17l-6-2h2c1-2 1-4 1-6 0-7-1-13-2-20z" class="G"></path><path d="M1004 1112c1-3 1-5 1-7 2 2 3 6 4 9 2 7 2 15 3 22l-1 1-10-3c3-6 3-15 3-22z" class="C"></path><path d="M675 449h2l-9 30c6 3 16 8 22 6 2-3 3-6 5-9-1 10-3 21-3 31l-24-7 2-2h1l1-2 1-1-1-1h-1l-1-1c-3 0-2 1-4 2 0 0-2 0-3 1 0-4 0-6 1-9l3-9 2-7c1-1 1-2 1-3l1-2v-3l1-3c0-1 1-2 1-3s1-2 1-4v-1c1-1 1-2 1-3z" class="D"></path><path d="M516 323h3c5 2 10 1 14 2 1 0 2 3 3 4 3 0 7-1 10-2l10 14c1 2 3 4 2 6l-3 1-7-1c-1-2-2-5-3-7 0 3 1 5 0 7-5-1-11-2-16-2h-1l-12-22z" class="C"></path><defs><linearGradient id="AK" x1="566.678" y1="457.642" x2="551.028" y2="427.28" xlink:href="#B"><stop offset="0" stop-color="#494949"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#AK)" d="M547 428c2-1 5 0 7 0l14 1v31l-14 1-2-1c0-2 0-5-1-7l-4-22v-3z"></path><defs><linearGradient id="AL" x1="611.922" y1="352.61" x2="637.552" y2="359.716" xlink:href="#B"><stop offset="0" stop-color="#7b7b7b"></stop><stop offset="1" stop-color="#b6b6b6"></stop></linearGradient></defs><path fill="url(#AL)" d="M627 340l4 2 3 2v1c-2 0-3-1-4 0 2 2 3 2 6 2l2-3v6h1c0-3 0-5 1-7 1 3 1 6 2 8 0 1-2 6-3 7l-7 21c-7-10-17-20-25-30 2 0 1 0 3 1s4 1 6 3c2 1 2 1 3 1v-1c0-4 5-10 8-13z"></path><path d="M520 412l-7-41c6 2 12 3 19 4l1 10 2 6c-1 3-2 5-2 8 0 1 0 1-1 2v1c0 2 0 3 1 6 0 0-1 1-1 2-3 1-9 3-12 2z" class="J"></path><path d="M533 385l2 6c-1 3-2 5-2 8 0 1 0 1-1 2v-3c-1-1-2-1-3-1v-7s2 0 2-1c1-1 2-2 2-4z" class="R"></path><path d="M1109 601l19-2c2 0 3 1 5 1 14-2 30-1 44-1 5 0 16 2 20 0h0l2 2v2c-3 2-13 1-17 1l-48 3-26 1c0-2-1-5 1-7z" class="B"></path><path d="M732 919V763l1-1c4 1 8 1 12 1-1 3-1 4-2 5h-2c0-1 0-2-2-2v2 24c-1 3 0 8-1 11-3 4-4 10-5 14 0 9-2 72 0 75l1 1c-1 2-1 4-1 7 0 6 1 12 0 18v1h-1z" class="G"></path><path d="M733 762c4 1 8 1 12 1-1 3-1 4-2 5h-2c0-1 0-2-2-2v2 24c-1 3 0 8-1 11-3 4-4 10-5 14v-16c0-13 2-26 0-39z" class="F"></path><path d="M735 768c2 2 1 3 2 6 0 6 0 11-2 16v-12-10z" class="G"></path><path d="M733 762c4 1 8 1 12 1-1 3-1 4-2 5h-2c0-1 0-2-2-2v2h-1c-1 2-1 4-1 6-1-3 0-4-2-6v10h-1c0 7 0 16-1 23 0-13 2-26 0-39z" class="P"></path><path d="M734 778v-12l1 2v10h-1z" class="K"></path><path d="M492 592h6c6-1 12-1 17 4 9 10 7 25 7 37 0 9 3 18 12 21 7 2 14 2 21 4 8 2 16 7 22 13-10-5-18-8-29-9-5-1-10-1-14-2-27-7-11-40-21-58-5-7-14-8-21-10z" class="C"></path><path d="M588 1093c4 0 7 0 10-2-1 9-2 17-2 26l11-5c1-5 3-9 5-13 0 2-1 5 0 7 1 1 1 0 1 2l1-8v7l1-1v-4h1c0 3 0 5-1 8v1l2 3 5-1c2 0 5 0 6 1-2 5-11 8-17 10l-3 1c-6 0-13 0-20-1v-16c0-4 1-9 1-13l-1-2z" class="F"></path><path d="M608 1125c2-1 3-2 4-3h1l-2-1v-1c-1-1-1 0-2-1 1-4 3-7 5-10l1 1v1l2 3 5-1c2 0 5 0 6 1-2 5-11 8-17 10l-3 1z" class="Q"></path><path d="M615 1111l2 3 5-1c-3 2-6 3-9 5h-1c2-2 2-5 3-7z" class="F"></path><defs><linearGradient id="AM" x1="436.63" y1="455.758" x2="465.492" y2="449.135" xlink:href="#B"><stop offset="0" stop-color="#c7c5c8"></stop><stop offset="1" stop-color="#f4f3f2"></stop></linearGradient></defs><path fill="url(#AM)" d="M446 501c6-33 4-67 2-100l9 4c0 2 1 10-1 11l-4 2 5 1c2 11-1 23-1 33v10l-1 14-4 28h-4l-1-3z"></path><path d="M538 1087l1 1 1 3 2 2c-4 12-18 23-28 32-19 16-45 22-70 21l16-3c6-1 12-4 17-6 26-11 47-25 61-50z" class="X"></path><path d="M491 841l1-59 11 2c2 19 1 38-1 56-3 1-8 2-10 1h-1z" class="B"></path><path d="M415 782l12 4c0 5 1 11 0 17-1 0-2 0-3-1h-3-1v1c1 3 0 9 0 13v1h-1l-3 2c-2 1-4 0-6 0h-7v-2-1c1-3 4-4 6-6 3-2 3-6 3-10-3 0-6-3-10-3-1 0-1 0-2-1 0-4 1-6 3-9 3-2 6-4 10-4l2-1z" class="S"></path><path d="M412 800l6 1c0 5 0 11-4 14-1 1 0 1-1 1-2 1-3 2-3 3h-7v-2-1c1-3 4-4 6-6 3-2 3-6 3-10z" class="L"></path><path d="M403 787c3-2 6-4 10-4 1 1 5 2 5 4 2 3 1 10 0 14l-6-1c-3 0-6-3-10-3-1 0-1 0-2-1 0-4 1-6 3-9z" class="T"></path><path d="M1076 1053c4-2 4-6 5-9 2 7 9 11 15 15h1l5 3 31 23c5 3 10 6 14 9l-3 3-5 5h-1c-2-1-4-3-6-5l-10-9-24-20-12-9c-3-1-6-3-8-6h-2z" class="O"></path><path d="M1078 1053c16 8 31 20 46 31 6 4 13 10 20 13l-5 5h-1c-2-1-4-3-6-5l-10-9-24-20-12-9c-3-1-6-3-8-6z" class="B"></path><path d="M1223 321l-1-2c3 2 6 6 8 9 10 13 20 27 28 42h-1c-3 1-6 2-8 3h-1c-3-5-6-9-10-13-2 0-3 0-5 1-1-1-2-1-3-2-2-2-4-3-4-6l5-2v-1c-2-3-3-4-6-6 2-7-1-17-2-23z" class="N"></path><path d="M1148 1024l1-1c-3-2-5-5-8-6-2-1-2-1-3-3l1-2c1-1 2-2 3-4 2 0 2 0 4 1 4 2 10 5 14 4 9 2 17 8 25 12l-1 2c-6-1-9-7-15-7 0 2 0 2 2 3 3 2 9 4 10 8h5l-3 7 2 1v2l-2-1-1 2h0c-2-1-3-2-5-3-9-6-19-10-29-15z" class="Y"></path><defs><linearGradient id="AN" x1="1153.736" y1="1028.362" x2="1179.089" y2="1034.578" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#3d3c3c"></stop></linearGradient></defs><path fill="url(#AN)" d="M1148 1024l1-1c-3-2-5-5-8-6-2-1-2-1-3-3l1-2c1-1 2-2 3-4 2 0 2 0 4 1 0 5 9 10 12 15 1 2 15 9 18 10 2-2 3-3 5-3h5l-3 7 2 1v2l-2-1-1 2h0c-2-1-3-2-5-3-9-6-19-10-29-15z"></path><path d="M1176 1034c2-2 3-3 5-3h5l-3 7-1 1-6-5z" class="E"></path><path d="M754 885v-3l-1 1h-3l-1-1c2-1 5-4 6-6-1-1-1-1-2-3 9-2 22 0 31 1-1 8-1 17-5 25-5 4-22 1-29 0l-6-2h23c3 0 7 0 10-1-2-2-4-2-7-3s-6 0-9-1v-1c1-4 2-2 4-5-1-1-2-1-3-1l-1 1h-4c-1-1-2-1-3-1z" class="G"></path><path d="M640 1076l3-1 3 1c4 3 8 6 13 8 3 1 5 2 7 4v2c-1 4-2 8-6 11v1l-1-1-3 1-1 1h-4c-4-1-7-1-11-1h-4c-1-1-2-3-3-3-2-2-2-2-2-3-1 2-2 4-5 5-2 1-3 0-5 0v-4c3 0 6-1 9-2v-14l1 2 1 12c2 1 5 1 7 0l1-19z" class="U"></path><path d="M272 311c5 0 9 0 13 3 12 6 20 26 23 38 5 17 4 31 3 48-1 9-1 19-4 27v-3c1-4 1-7 1-11 0-6 1-12 0-17 0-5-2-8-2-13-3-20-5-46-19-61-8-8-12-5-22-6v-1c2-2 4-3 7-4z" class="C"></path><path d="M1004 359h3c0 1 1 2 1 3 1 5 3 9 4 14 0 2 1 4 0 5-1-3-1-7-4-10l-3 1c-2-1-2-2-3-4h-1c1 2 1 2 1 4l-23 8c-5 2-10 4-15 7l-5-12c12-9 30-14 45-16z" class="J"></path><path d="M456 1126l-3 2c-9 4-22 3-31-1-20-8-33-28-41-47-3-6-12-29-10-35 2 0 1 0 3 1l1 4c8 27 21 56 47 70 5 2 11 5 16 5 5 1 9 1 13 1 2 0 4-1 5 0z" class="C"></path><defs><linearGradient id="AO" x1="991.195" y1="1073.717" x2="1002.669" y2="1101.604" xlink:href="#B"><stop offset="0" stop-color="#1e1d21"></stop><stop offset="1" stop-color="#5a5a58"></stop></linearGradient></defs><path fill="url(#AO)" d="M995 1077c-1-2-1-3 0-6v-3l1-1 3 3 1 1 1 22c2 2-1 8 1 9l2-2v12c0 7 0 16-3 22l-2-1-3-1-2-1-9-4 1-1c3-5 4-10 4-16l2-16c0-1 1-4 1-6l2-11z"></path><defs><linearGradient id="AP" x1="1006.178" y1="1106.042" x2="997.199" y2="1120.764" xlink:href="#B"><stop offset="0" stop-color="#12110f"></stop><stop offset="1" stop-color="#2f3032"></stop></linearGradient></defs><path fill="url(#AP)" d="M1001 1093c2 2-1 8 1 9l2-2v12c0 7 0 16-3 22l-2-1h1c1-4 0-11 0-15l1-25z"></path><path d="M995 1077c1 3 0 11 0 14l1 16c0 4-1 8 0 12l3-10c0 4 1 9 0 14l1 10h-1l-3-1-2-1v-1c2-8 1-18 0-27 0-3 0-6-2-9 0-1 1-4 1-6l2-11z" class="I"></path><path d="M999 1123l1 10h-1l-3-1c0-3 1-7 3-9z" class="M"></path><defs><linearGradient id="AQ" x1="991.071" y1="1107.066" x2="990.151" y2="1128.003" xlink:href="#B"><stop offset="0" stop-color="#d2d1d2"></stop><stop offset="1" stop-color="#fffefe"></stop></linearGradient></defs><path fill="url(#AQ)" d="M992 1094c2 3 2 6 2 9 1 9 2 19 0 27v1l-9-4 1-1c3-5 4-10 4-16l2-16z"></path><defs><linearGradient id="AR" x1="326.913" y1="705.679" x2="329.112" y2="764.125" xlink:href="#B"><stop offset="0" stop-color="#6c6c6b"></stop><stop offset="1" stop-color="#949494"></stop></linearGradient></defs><path fill="url(#AR)" d="M315 702c6 8 12 15 17 24 6 10 9 21 13 32h0c1 3 2 6 3 10h1c0 1 0 1-1 1h-3l-1 1h0l-4 1c-4-15-11-32-20-44l-9-12c1-5 2-9 4-13z"></path><defs><linearGradient id="AS" x1="386.096" y1="862.109" x2="395.051" y2="823.729" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#AS)" d="M384 841h1v-18h6c1 0 4 3 5 4 0 2 0 4 2 6l7 2v2c-1 1-2 3-1 5l-1 1c0 2 0 4-1 6 0 1 0 8 1 9-1 3-1 5-2 7h-3c-5 0-11 0-16-2l2-22z"></path><path d="M398 833l7 2v2c-1 1-2 3-1 5l-1 1c0 2 0 4-1 6 0 1 0 8 1 9-1 3-1 5-2 7h-3c-2-9 2-19 1-28l-1-4z" class="R"></path><defs><linearGradient id="AT" x1="540.03" y1="1022.865" x2="565.76" y2="1000.113" xlink:href="#B"><stop offset="0" stop-color="#706e70"></stop><stop offset="1" stop-color="#8f8f8e"></stop></linearGradient></defs><path fill="url(#AT)" d="M544 979c6 0 12 0 16 4 1 2 2 3 3 5l-1-1c-4-2-8-4-11-7 6 13 11 26 14 42 0 2 1 5 0 7-3 6-4 15-5 22l-1 1c-3-2 0-13-2-17l-3-1c-1-14-4-26-9-39v-10c-1-2-2-3-3-5l2-1z"></path><path d="M975 1044h1l1-2c3 2 3 5 5 8l-1 1-12 39c0 1-1 3-1 4l-2 4h-3c-1 2-2 5-3 7l-1-1v-1c0-1 0-2 1-3l1-3-1 1 1-7c-1-3-2-4-4-6l-3 5-3-5 2-3c1-1 2-4 3-5l8-14 10-19h1z" class="O"></path><path d="M957 1085l23-39v3c-1 3-3 6-4 9l-7 20-4 12c0 3-1 5-2 8-1 2-2 5-3 7l-1-1v-1c0-1 0-2 1-3l1-3-1 1 1-7c-1-3-2-4-4-6z" class="G"></path><defs><linearGradient id="AU" x1="401.296" y1="353.215" x2="440.048" y2="356.86" xlink:href="#B"><stop offset="0" stop-color="#c8c7c7"></stop><stop offset="1" stop-color="#f4f4f5"></stop></linearGradient></defs><path fill="url(#AU)" d="M406 307c20 27 27 60 32 93l-7 1c-4-31-11-59-29-84 1-4 2-7 4-10z"></path><path d="M1168 1009h0c-7-2-14-5-20-8l2-9c0-2 1-2 2-3l-2-2v-1c2 0 1 0 2 1h3l26 9h3l2 1 2 1c2 0 2 0 4 1l-1 5v1c-1 1-1 2-2 3v1c1 2 0 4 0 5 0 2-1 4-2 5v-2c-4 0-9-4-12-5-2 0-5-2-7-3z" class="Q"></path><path d="M1178 1007c1-2 1-3 2-5 1-1 3-2 4-2 3 0 4 2 6 4l-1 2h-7l-4 1z" class="R"></path><path d="M1190 1004h1v1c-1 1-1 2-2 3v1c1 2 0 4 0 5 0 2-1 4-2 5v-2c-4 0-9-4-12-5-2 0-5-2-7-3 2-1 5 0 8 0l2-2 4-1h7l1-2z" class="X"></path><path d="M1175 1012c2-2 6-2 9-1 2 1 3 2 5 3 0 2-1 4-2 5v-2c-4 0-9-4-12-5z" class="Y"></path><defs><linearGradient id="AV" x1="392.775" y1="823.936" x2="389.225" y2="800.128" xlink:href="#B"><stop offset="0" stop-color="#a8a7a7"></stop><stop offset="1" stop-color="#dbdada"></stop></linearGradient></defs><path fill="url(#AV)" d="M381 796c-1-2-1-3 0-4h1c2 1 4 2 6 2l3 2h3c1 1 1 1 3 1 2-2 0-7 1-9l5-1c-2 3-3 5-3 9 1 1 1 1 2 1 4 0 7 3 10 3 0 4 0 8-3 10-2 2-5 3-6 6v1 2c-3 1-3 0-4 2-2 2-2 4-3 6-1-1-4-4-5-4h-6v18h-1c0-9 0-18-1-27l-2-18z"></path><path d="M381 796c-1-2-1-3 0-4h1c2 1 4 2 6 2l3 2h3c1 1 1 1 3 1 2-2 0-7 1-9l5-1c-2 3-3 5-3 9 1 1 1 1 2 1 4 0 7 3 10 3 0 4 0 8-3 10h-8c-2-1-5-1-7-3h1v-2h-1c-1-1-2-2-2-4h-1v3c-1 0-1 0-2-1l-6-2-2-5z" class="D"></path><path d="M395 805h1c1 0 2 0 3-2h1 1v7c-2-1-5-1-7-3h1v-2z" class="N"></path><path d="M599 388c4 7 6 14 8 22 5 16 6 31 6 47 0 10 0 20-3 30-1 3-2 9-4 12-3 4-4 12-6 18 0 2-1 3-2 5-1-2 2-9 2-11 5-18 5-35 5-53-1-17-1-34-3-51l-3-19z" class="K"></path><path d="M921 669h4c2 3 2 7 2 10v12 2l-1 12c0 5-1 13 1 18 2 1 2 0 4 2-2 1-4 3-5 4-1 2 0 5 0 7v14c0 10 1 67-1 70h-3c-1-3 0-8 0-11l-1-31v-72c0-12-1-25 0-37z" class="B"></path><defs><linearGradient id="AW" x1="612.225" y1="455.697" x2="624.982" y2="455.067" xlink:href="#B"><stop offset="0" stop-color="#c2c2c3"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AW)" d="M610 391c2 0 6 1 8 2 3 3 7 36 7 42l1 23c0 24-6 53-17 74h-1c6-16 9-34 10-52 3-30 2-60-8-89z"></path><defs><linearGradient id="AX" x1="341.695" y1="511.236" x2="347.719" y2="484.664" xlink:href="#B"><stop offset="0" stop-color="#b1b0b0"></stop><stop offset="1" stop-color="#dcdcdb"></stop></linearGradient></defs><path fill="url(#AX)" d="M333 480c8 4 17 4 26 6v4l-2 25h-4c-2-2-5-2-8-2l-14-2c2-10 2-21 2-31z"></path><path d="M372 361l34 4c3 8 4 15 4 23-11-3-23-5-35-5l-3-22zm763 254l74-2 1 2-1 1-39 1v20l21-1 50 1c6-1 9-6 12-10h2c-2 3-4 6-6 8-3 2-5 4-9 4-7 2-17 1-25 1h-48v-23l-25 1v22h-33v-4h30c1-6 0-13 0-19l-9 1 1-1-1-1 5-1z" class="C"></path><defs><linearGradient id="AY" x1="796.049" y1="1054.256" x2="775.721" y2="1045.045" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#AY)" d="M776 1030h-1v-1l25-2v21 12l-1 8-1 1h0c-3 0-4 1-6 0h-10c-2-12 0-25-2-38-1 0-2 0-3-1h-1z"></path><path d="M776 1030h-1v-1l25-2v21 12l-1 8-1 1h0c-3 0-4 1-6 0l-1-39c-4 1-11 0-15 0z" class="C"></path><defs><linearGradient id="AZ" x1="794.528" y1="1054.339" x2="790.787" y2="1033.942" xlink:href="#B"><stop offset="0" stop-color="#676668"></stop><stop offset="1" stop-color="#8b8a8a"></stop></linearGradient></defs><path fill="url(#AZ)" d="M791 1030l3 1c1 4 1 8 1 11 0 5 1 10 1 15l2 12c-3 0-4 1-6 0l-1-39z"></path><path d="M792 1069h2v-11l2-1 2 12c-3 0-4 1-6 0z" class="P"></path><path d="M823 1101h4c0 2 0 4-1 6 0 1-15 3-18 4-6 2-14 1-20 1l-33 1-7-11 75-1z" class="G"></path><defs><linearGradient id="Aa" x1="691.031" y1="1370.993" x2="719.47" y2="1355.104" xlink:href="#B"><stop offset="0" stop-color="#676667"></stop><stop offset="1" stop-color="#8b8a8a"></stop></linearGradient></defs><path fill="url(#Aa)" d="M721 1373v4l-29-1v-25l26-2h6c-1 2-2 2-2 4l-1 20z"></path><path d="M718 1349h6c-1 2-2 2-2 4l-1 20c0 2 0 2-2 3l-1-1c-3-7 0-18 0-26z" class="W"></path><defs><linearGradient id="Ab" x1="1269.671" y1="1031.288" x2="1254.812" y2="1029.488" xlink:href="#B"><stop offset="0" stop-color="#737373"></stop><stop offset="1" stop-color="#929091"></stop></linearGradient></defs><path fill="url(#Ab)" d="M1254 1025l2-1c1-1 2-2 2-3l1-1c5-1 10 2 15 0v6l-2 6c-1 1-2 1-4 1s-3 0-5 2c-1 2-2 3-2 5v5c-1 1-1 3-1 5l-2 1c-3-2-3-6-3-8l-2-1c-1 1-1 2-1 4-5 16-11 31-17 46-4 10-8 20-14 28l-4 6c-10 19-23 37-37 53l-17 18h-1c6-8 13-15 19-22 5-6 10-12 14-18 22-31 39-65 50-100 3-11 5-21 8-31l1-1z"></path><path d="M1255 1043c0-2 1-3 2-5h1l3 2v5c-1 1-1 3-1 5l-2 1c-3-2-3-6-3-8z" class="M"></path><path d="M559 340c5 0 9 3 12 6 18 14 22 39 25 60l-15-2c-1-6-1-12-2-18-2-9-6-18-10-26l-6-12c-2-3-4-5-4-8z" class="C"></path><path d="M699 367l-1-4h1c3-5 5-10 8-15 4-7 8-15 13-21 7 5 16 11 22 17-4 7-8 16-14 22 0-2-1-2-2-4-4-2-9-4-13-5l-9 18v-4l2-3 1-2c0-1 0-1 1-2s0-1 1-2l3-6-1-1c-3 1-5 5-6 8l-1 2c-1 2-3 1-5 2z" class="B"></path><defs><linearGradient id="Ac" x1="1080.645" y1="1072.96" x2="1109.709" y2="1067.867" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#6f6f6f"></stop></linearGradient></defs><path fill="url(#Ac)" d="M1076 1053h2c2 3 5 5 8 6l12 9 24 20 10 9c2 2 4 4 6 5h1c-2 2-5 5-8 7v-2c-5-1-15-10-19-13-1-2-5-5-7-6l-27-20-1-2c-1 0-1 0-2-1-2-2-4-3-6-4l7-8h0z"></path><path d="M1114 1089l1-1-2-1 1-3c3 1 5 5 6 8l1 1c1 3 2 2 5 3 2 1 8 7 8 8l-2 2c-1-2-3-4-5-5l-13-12z" class="L"></path><defs><linearGradient id="Ad" x1="1070.907" y1="1079.83" x2="1125.68" y2="1079.084" xlink:href="#B"><stop offset="0" stop-color="#0e0d0e"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#Ad)" d="M1076 1053c0 6 1 9 6 13l18 12 11 9s2 2 3 2l13 12c2 1 4 3 5 5l-1 1c-5-1-15-10-19-13-1-2-5-5-7-6l-27-20-1-2c-1 0-1 0-2-1-2-2-4-3-6-4l7-8z"></path><path d="M1026 1092s0 2 1 2h1l2 6c2 9 8 17 10 26h1l1-2h1c1 5 3 11 6 16h-2c-1 0-2 0-3 1h-7-3l-2-1h-6l-8-1h0v-17l-1-9-1-8-1-13c2 2 3 6 4 8 0 3 2 5 3 7 1-1 1-3 2-5l1-2c-1-2-1-5-1-7l2-1z" class="B"></path><path d="M1026 1092s0 2 1 2l7 47-2-1c-1-4-1-7-2-11 0-2 0-2-2-3l-4-24 1-2c-1-2-1-5-1-7l2-1z" class="Q"></path><path d="M1015 1092c2 2 3 6 4 8 0 3 2 5 3 7 1-1 1-3 2-5l4 24c2 1 2 1 2 3 1 4 1 7 2 11h-6l-8-1h0v-17l-1-9-1-8-1-13z" class="R"></path><path d="M1017 1113l1-2c4 3 3 10 5 14 2 3 1 6 2 8 0 2 2 4 3 6l-2 1-8-1h0v-17l-1-9z" class="B"></path><defs><linearGradient id="Ae" x1="1069.252" y1="1102.361" x2="1078.245" y2="1113.9" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#8c8b8c"></stop></linearGradient></defs><path fill="url(#Ae)" d="M1047 1076c1-1 2-1 4-2l2 1c3 4 7 7 10 10l14 15c2 3 6 7 8 10 3 3 7 7 10 11 1 2 3 5 5 6l1 1-13 6-4 1v-1c-3-4-6-9-9-13-5-8-12-15-17-23 2-1 2-1 2-3l-14-17 1-2z"></path><path d="M1047 1076c1-1 2-1 4-2l7 8c1 6 5 10 8 15-7-6-12-15-19-20v-1z" class="Q"></path><path d="M1051 1074l2 1c3 4 7 7 10 10l14 15c2 3 6 7 8 10-2 0-2 0-4-2v2 3h-1c-2-5-6-9-8-13l-6-9c-2-3-5-6-8-9l-7-8z" class="N"></path><path d="M1060 1095c7 9 15 18 21 28l2 3 3 6-2 2c-3-4-6-9-9-13-5-8-12-15-17-23 2-1 2-1 2-3z" class="U"></path><path d="M1081 1113v-3-2c2 2 2 2 4 2 3 3 7 7 10 11 1 2 3 5 5 6l1 1-13 6-4 1v-1l2-2-3-6c0-5-3-8-3-13h1z" class="B"></path><path d="M1080 1113h1 0c3 5 9 13 7 19v1 1l-4 1v-1l2-2-3-6c0-5-3-8-3-13z" class="M"></path><defs><linearGradient id="Af" x1="857.636" y1="1300.221" x2="841.42" y2="1262.28" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#Af)" d="M825 1249c3 1 5 2 7 3 1 1 2 1 3 1l8 3 2 1 4 2c2 1 4 2 6 2 2 1 3 1 4 2s1 1 1 2c-2 8-2 16-3 24 0 5 0 10-1 15l1 1-2 1-8-3c-2-1-5-2-7-4-3-2-5-4-8-5l2-1c1 0 1 0 2-1 4-8 4-21 4-31l1-2h-1l-2-1-2-1-7-3c-2-1-3-2-4-3v-2z"></path><path d="M840 1261l2 1-2 24c-1 2-2 7 0 8 2 5 11 8 16 10l1 1-2 1-8-3c-2-1-5-2-7-4-3-2-5-4-8-5l2-1c1 0 1 0 2-1 4-8 4-21 4-31z" class="B"></path><path d="M664 1187h-34-7c-1 0-2-3-3-3l-9-9c-4-7-8-14-14-19h0c0-5 1-9 2-13h12c-1 2-1 3-2 4h-1c1 5 5 12 7 17 2 3 3 8 6 10 13 3 29 2 43 2-1 4-1 7 0 11z" class="R"></path><defs><linearGradient id="Ag" x1="702.14" y1="371.007" x2="692.206" y2="417" xlink:href="#B"><stop offset="0" stop-color="#cfcece"></stop><stop offset="1" stop-color="#f3f3f3"></stop></linearGradient></defs><path fill="url(#Ag)" d="M699 367c2-1 4 0 5-2l1-2c1-3 3-7 6-8l1 1-3 6c-1 1 0 1-1 2s-1 1-1 2l-1 2-2 3v4l-10 21c6 3 10 5 16 6 2-2 3-6 5-9v1c-3 8-5 16-9 23l-1 1c1 1 1 2 2 3l-1 1-29-9c5-13 10-27 17-39 2-4 4-4 5-7z"></path><defs><linearGradient id="Ah" x1="1187.867" y1="992.797" x2="1208.19" y2="1008.169" xlink:href="#B"><stop offset="0" stop-color="#626062"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#Ah)" d="M1192 999l5-22c2-6 2-14 5-19h1c-1 3-1 6-2 9l1 5c2 1 8-2 10-3-3 18-8 36-14 53l3 2-3 9h-2 0l-1 1-4-2h-1c0 3 0 5-3 6-1 1-1 1-2 1l-2-1 3-7h-5c-1-4-7-6-10-8-2-1-2-1-2-3 6 0 9 6 15 7l1-2c0-2-1-3-1-4l1-1 2-1c1-1 2-3 2-5 0-1 1-3 0-5v-1c1-1 1-2 2-3v-1l1-5z"></path><path d="M1196 1033l-2-2c2-3 3-6 4-9l3 2-3 9h-2 0z" class="D"></path><path d="M1192 999l5-22c2-6 2-14 5-19h1c-1 3-1 6-2 9v2c-1 5-1 10-2 15-3 16-8 32-13 47h-5c-1-4-7-6-10-8-2-1-2-1-2-3 6 0 9 6 15 7l1-2c0-2-1-3-1-4l1-1 2-1c1-1 2-3 2-5 0-1 1-3 0-5v-1c1-1 1-2 2-3v-1l1-5z" class="N"></path><defs><linearGradient id="Ai" x1="1005.904" y1="1315.635" x2="1001.895" y2="1288.189" xlink:href="#B"><stop offset="0" stop-color="#afaeae"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#Ai)" d="M981 1303v-9l43-10c0 2 0 4 1 6l-2 2c0 2 0 3 1 4 0 5 0 10 1 15h2c-3 3-9 4-13 5v-15l-1-1c-2 4-1 12 0 16l-16 3-1-20h-1-1c-3 2-9 2-13 4z"></path><defs><linearGradient id="Aj" x1="1102.131" y1="1284.304" x2="1038.607" y2="1322.736" xlink:href="#B"><stop offset="0" stop-color="#363636"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#Aj)" d="M1034 1318c-1-1-2-2-2-3 2-3 19-7 23-9l31-13 18-9 3 9c-11 8-22 14-35 19h-1l-1 4-1 1c-3 0-5 1-8 1-1 0-2 1-3 1h-2 5l1 1c-1 1-3 1-4 2-3 1-5 2-8 2v-1l3-2c-2-1-7 0-9 1-3-1-3-3-5-4h-5z"></path><defs><linearGradient id="Ak" x1="727.4" y1="491.676" x2="760.369" y2="463.44" xlink:href="#B"><stop offset="0" stop-color="#bebcbe"></stop><stop offset="1" stop-color="#e7e7e6"></stop></linearGradient></defs><path fill="url(#Ak)" d="M738 504h-7l-1-2 1-1 9 1 1-2c-3-2-7 0-10-2v-4c2-2 5-1 8-1 0-2 1-3 0-5h-6c-2-4 4-29 5-34l17 2 2 2-7 45-12 1z"></path><defs><linearGradient id="Al" x1="715.93" y1="788.391" x2="743.11" y2="716.134" xlink:href="#B"><stop offset="0" stop-color="#cac9c9"></stop><stop offset="1" stop-color="#fdfcfd"></stop></linearGradient></defs><path fill="url(#Al)" d="M745 763v-25c0-3 0-7-1-11 0-2-1-2-3-4-4-1-7 0-11 0h-19v-15c4 1 8 1 13 1v5c6 4 24-4 26 7 2 13-1 26-1 39l-1 41c-3-2-4-3-8-2v4c-1 5 0 10-1 15-1-5 0-10-1-15 1-3 0-8 1-11v-24-2c2 0 2 1 2 2h2c1-1 1-2 2-5z"></path><path d="M300 834c-2 6-6 15-11 19-3 3-7 6-12 6-2 0-4-1-6-3-4-4-2-17-2-23v1c1 3 1 6 3 9 3 2 6 4 9 4s6-2 8-4c6-7 7-18 6-27-1-6-3-15-8-19-4-2-9-1-13 0l2-12c5 0 11 0 16 2 4 3 8 8 10 13 0 3 0 6 1 8 1 7 0 17-2 24-1 1 0 0-1 2z" class="C"></path><path d="M1279 1022l1-2 8 3 10 2-12 38c-3-1-4-1-7-1v-1c-2-1-2-2-2-5l1-2h-1c-2 1-4 1-6 1-5 0-8-2-11-5 0-2 0-4 1-5v-5c0-2 1-3 2-5 2-2 3-2 5-2s3 0 4-1l2-6c0 1 1 2 2 3l3-7z" class="G"></path><path d="M1272 1044l2 1v3h-2-3v-1l3-3z" class="F"></path><path d="M1263 1040c2-2 3-4 6-5 3 1 5 1 8 3 1 2 2 5 2 8l-3 6c-3 1-7 2-10 0-2 0-3-2-5-3l1-2c2 2 4 3 7 4 2 0 4-1 6-2 2-2 1-4 1-7-1-2-2-2-4-3-4 0-5 0-7 2l-2-1z" class="R"></path><path d="M1261 1040c0-2 1-3 2-5 2-2 3-2 5-2s3 0 4-1l2-6c0 1 1 2 2 3 0 2-1 3 0 5h1c2 0 3 1 4 3 2 3 1 6 1 9v1c-1 2-3 4-5 5h-1l3-6c0-3-1-6-2-8-3-2-5-2-8-3-3 1-4 3-6 5-1 2-1 3-2 5v-5z" class="U"></path><path d="M1261 1045c1-2 1-3 2-5l2 1c-1 2-2 3-2 5l-1 1-1 2c2 1 3 3 5 3 3 2 7 1 10 0h1c2-1 4-3 5-5 2-1 4-1 6 0 0 2-1 4-2 6h0c-2 3-5 6-7 8-2-1-2-2-2-5l1-2h-1c-2 1-4 1-6 1-5 0-8-2-11-5 0-2 0-4 1-5z" class="I"></path><path d="M1279 1022l1-2 8 3 10 2-12 38c-3-1-4-1-7-1v-1c2-2 5-5 7-8h0c1-2 2-4 2-6-2-1-4-1-6 0v-1c0-3 1-6-1-9-1-2-2-3-4-3h-1c-1-2 0-3 0-5l3-7z" class="G"></path><path d="M1279 1022l1-2 8 3c-2 2-6 1-9 1v-2z" class="X"></path><path d="M1252 877l39-2-1 2c1 1 2 2 3 4 0 3 0 7-1 10v17c-2 0-4 0-5-1l-2-3v3c-4 1-7 1-10 1s-8 1-11-1c1-3 2-10 0-13-1 0-3 1-4 1-1-5 1-10 0-15-2-2-3-2-6-2l-2-1z" class="R"></path><path d="M1264 907h6c2-3-1-9 3-11l1 1 1 11c-3 0-8 1-11-1z" class="I"></path><path d="M1285 907c-2 0-4 1-6-1 0-5 1-11 1-17 0-3 0-9 1-13h5c1 2 0 7 0 10h0c-2 3-1 13-1 17v1 3z" class="J"></path><path d="M968 999l1-1 1 1c-1 3-1 4-4 6 0 0-1 0-1 1l-1 1v1c1-1 3-3 5-3l1 1-5 4 1 1 3-1 1 1c0 3-2 5-4 7l-19 17c-3 3-7 7-10 9h-1c-2 3-6 5-9 7v-1c-1-3-1-7 0-9l-1-1c0-3-1-8 0-11 0-1 2-2 3-3l36-28c1 1 1 1 3 1z" class="O"></path><path d="M968 999l1-1 1 1c-1 3-1 4-4 6 0 0-1 0-1 1l-1 1v1c1-1 3-3 5-3l1 1-5 4 1 1 3-1 1 1c0 3-2 5-4 7l-19 17c-3 3-7 7-10 9h-1c4-6 12-12 18-18 1-2 4-4 5-6-1-1-1-2-2-4 0-1 1-2 2-3l-1-1-13 12c-6 5-11 12-17 16l-1 1v-3c1-1 4-3 5-4l24-23c2-2 4-4 5-6 2-2 4-4 7-6z" class="K"></path><path d="M965 998c1 1 1 1 3 1-3 2-5 4-7 6-1 2-3 4-5 6l-24 23c-1 1-4 3-5 4v3l-1-1c0-3-1-8 0-11 0-1 2-2 3-3l36-28z" class="F"></path><defs><linearGradient id="Am" x1="762.702" y1="985.545" x2="781.05" y2="978.862" xlink:href="#B"><stop offset="0" stop-color="#7f7e7f"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#Am)" d="M770 937c-2-4-1-10-1-13 3 0 6-1 9 0l1 4v12l2 3c0 2 1 4 1 7l1 46-1-6h-1c0 6 1 27-1 31v-8l-9 1-2-63-1-7c0-2 0-3 2-5l1-1-1-1z"></path><path d="M779 940l2 3c0 2 1 4 1 7l1 46-1-6h-1c0 6 1 27-1 31v-8l-1-55c1-5 0-13 0-18z" class="O"></path><defs><linearGradient id="An" x1="766.349" y1="960.878" x2="785.128" y2="955.584" xlink:href="#B"><stop offset="0" stop-color="#bab9ba"></stop><stop offset="1" stop-color="#dadad9"></stop></linearGradient></defs><path fill="url(#An)" d="M770 937c-2-4-1-10-1-13 3 0 6-1 9 0l1 4v12c0 5 1 13 0 18-2 9 1 20-1 29v5 1l-2-1c-4-4-2-31-3-38-1-1-2-2-4-3l-1-7c0-2 0-3 2-5l1-1-1-1z"></path><path d="M768 944c3 3 4 6 5 10-1-1-2-2-4-3l-1-7z" class="R"></path><path d="M1110 917h14 30 94 21c1 0 5 0 6 1 3 1 17 19 19 22-5 0-14-9-17-13-1-1-3-3-4-3-1-1-3-1-4-1h-36l-79-1c-14 0-30 1-44-1-1-1-1-2 0-4z" class="C"></path><path d="M671 508c-2 0-3 0-4 1-2 6-1 13-1 19v33l-30-5c10-15 15-33 19-51l17 2v1h-1z" class="B"></path><defs><linearGradient id="Ao" x1="849.575" y1="792.542" x2="775.555" y2="728.977" xlink:href="#B"><stop offset="0" stop-color="#9d9f98"></stop><stop offset="1" stop-color="#cbc7cf"></stop></linearGradient></defs><path fill="url(#Ao)" d="M810 620h1v70l1-1c0-1 0-2 1-3 1-2 1-9 1-11v-36c0-8-2-19 1-26v224c-2 1-3 0-5 0-1-6 0-13 0-19v-47-102-49z"></path><path d="M340 632c1 1 3 3 4 3 1 1 4 1 6 1h18l3-1 1-2v3l38 3c2 0 2-1 3 1l-1 1c-1 1 0 1-1 2l2 1 3 4c-1 1-1 1-2 1-9-1-18 1-27 0-2 0-3 0-5 1l-6 1-2-2c-2 2-2 2-6 3-2 0-5 0-7 1-1 1-2 1-4 2v-1l-1-1c0-1-1-2-1-4-3 0-6 0-8 1v2c-1 3-1 3-3 5-7 1-13-2-19-6l9 2c2 1 3 1 5 0 2-2 3-4 4-6 0-4-1-8-3-10-3-3-5-3-8-4h-1c2-1 6-1 9-1z" class="B"></path><path d="M585 352l-11-11 1-1 3 2 1-1c1-1 0-152 0-165v-24c0-4 0-9 1-12 0-5 22-18 27-22l1 1-14 12c-4 3-8 6-10 10v200c2 1 5 2 7 4h0-2v2 1l-4 4z" class="E"></path><defs><linearGradient id="Ap" x1="1061.894" y1="1305.699" x2="1037.388" y2="1279.524" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#adacac"></stop></linearGradient></defs><path fill="url(#Ap)" d="M1025 1290l44-16c0 1 0 3 1 4 1 4 6 13 9 16-15 6-32 14-48 17-2-4-4-12-7-15-1-1-1-2-1-4l2-2z"></path><path d="M237 557c18 0 38-2 54 4l16 2 5 2c1 1 2 1 3 2-1 1-4 1-6 1l-2-1c-6-1-14 0-20 1l-2 1c0 1 1 3 2 4h-41-17c-1-1-2-1-3-2-1-3-1-1-3-2 0-1-1-1-2-2 3-3 4-5 7-6l6-3v-1h3z" class="C"></path><path d="M237 557c18 0 38-2 54 4-4 1-9 0-14 0l-21-1h-13l-1 1v-1c-2-2-5-2-7-2h-1v-1h3z" class="G"></path><defs><linearGradient id="Aq" x1="1247.677" y1="1088.103" x2="1305.69" y2="1083.427" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#494948"></stop></linearGradient></defs><path fill="url(#Aq)" d="M1298 1025c3-8 5-16 5-24h1c1 1 0 5 0 7l-5 21 1 1 3-3h2c-3 28-16 55-28 80-4 9-9 20-15 29-1-1 0-1-1-2-1 0-1 0-1 1l-7 8v-1c0-1-1-2-2-3v-5-1h1c4-2 6-6 9-10 3-5 5-12 8-17l4-7c2-3 4-7 5-10l-1-2 9-24 12-38z"></path><path d="M1298 1025c3-8 5-16 5-24h1c1 1 0 5 0 7l-5 21c-4 14-8 29-13 43-3 6-6 11-8 17l-1-2 9-24 12-38z" class="E"></path><defs><linearGradient id="Ar" x1="284.262" y1="772.722" x2="318.575" y2="764.646" xlink:href="#B"><stop offset="0" stop-color="#d1d0d0"></stop><stop offset="1" stop-color="#f9f8f9"></stop></linearGradient></defs><path fill="url(#Ar)" d="M275 722h1c-2-2-2-2-4-2l1-6c3 1 5 1 8 1 8 2 15 9 21 16 2 2 4 4 5 7 1 1 1 1 1 2h-1l-3 2c2 0 3 0 5-1 8 8 15 38 16 49 2 14 2 27 0 41-1 4-1 8-2 12l-1-22c-2-32-10-69-35-90-3-4-7-6-12-9z"></path><defs><linearGradient id="As" x1="689.534" y1="567.162" x2="672.311" y2="511.178" xlink:href="#B"><stop offset="0" stop-color="#3f3f3f"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#As)" d="M672 507c3 1 12 3 14 2 3-1 1-2 5-1v1c1 2 0 6 0 8l-1 17v35c-6 1-13 3-19 4 0-2 1-4 1-5 1-1 2-1 3-1 2-1 2-1 3-3 1-3 1-9 0-12l-1-1h-5c-2-3-2-35-1-40 0 0 0-1 1-1l-1-2h1v-1z"></path><defs><linearGradient id="At" x1="1142.053" y1="1068.897" x2="1157.917" y2="1062.017" xlink:href="#B"><stop offset="0" stop-color="#c2c1c3"></stop><stop offset="1" stop-color="#f3f2f1"></stop></linearGradient></defs><path fill="url(#At)" d="M1089 1031l1-1 40 21h0c3 1 5 2 7 3h1l1 1c1 0 1 0 2-2-2 0-3-1-4-1-1-1-2-2-3-2l-3-2v-1l20 11c1-1 1-2 2-4 1 1 3 1 4 1 2-1 3-1 4-1 0-1-1-2-1-3l1-1 13 4v2c0 2 0 2-1 4-3 4-5 6-7 9l-1 2c-2 3-5 8-8 11l-1-1c0-1 0-1-2-2-7-4-16-10-22-15l-20-12c-2-3-6-8-9-9-2-1-2-1-4-3h1c-2-2-2-3-3-4-1-2-7-4-8-5z"></path><path d="M1129 1054c5 4 13 9 16 14 2 4 6 7 9 10v1c-7-4-16-10-22-15-1-1-2-3-3-3v-7z" class="I"></path><path d="M1131 1047l20 11 6 3c2 2 8 4 9 7l-2 2c-4-1-8-4-11-6l-16-8c-2-1-5-3-7-5h0c3 1 5 2 7 3h1l1 1c1 0 1 0 2-2-2 0-3-1-4-1-1-1-2-2-3-2l-3-2v-1z" class="K"></path><defs><linearGradient id="Au" x1="1105.096" y1="1051.076" x2="1128.207" y2="1050.936" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#Au)" d="M1097 1036a313.77 313.77 0 0 1 25 14c2 1 6 4 7 4v7c1 0 2 2 3 3l-20-12c-2-3-6-8-9-9-2-1-2-1-4-3h1c-2-2-2-3-3-4z"></path><path d="M1161 1050l13 4v2c0 2 0 2-1 4-3 4-5 6-7 9l-1 2-1-1 2-2c-1-3-7-5-9-7l-6-3c1-1 1-2 2-4 1 1 3 1 4 1 2-1 3-1 4-1 0-1-1-2-1-3l1-1z" class="H"></path><path d="M1171 1059l2 1c-3 4-5 6-7 9l-1 2-1-1 2-2c-1-3-7-5-9-7l2-2c3 2 6 2 9 3h1l2-3z" class="E"></path><path d="M1161 1050l13 4v2c0 2 0 2-1 4l-2-1c-3-2-7-3-10-5 0-1-1-2-1-3l1-1z" class="D"></path><defs><linearGradient id="Av" x1="941.007" y1="991.424" x2="966.068" y2="994.64" xlink:href="#B"><stop offset="0" stop-color="#626060"></stop><stop offset="1" stop-color="#787879"></stop></linearGradient></defs><path fill="url(#Av)" d="M944 977l26-11v14c-1 3 0 5-3 7-8 6-16 12-25 17-4 3-9 7-14 10l-1-1-2 1 1-6v-25h0c7-2 11-4 17-7l1 1z"></path><path d="M926 1008l1-1c2 0 5 0 6 2l-2 1c-2 2-2 1-4 1l-1-3z" class="F"></path><path d="M926 983h0c7-2 11-4 17-7l1 1c-3 2-6 3-9 5 0 1 0 1 1 2 2 2 2 7 1 10v1c1 3 1 5 2 7-1 2-3 6-6 7-1-2-4-2-6-2l-1 1h0v-25z" class="K"></path><path d="M944 977l26-11v14c-6 2-11 5-16 8-5 2-9 4-13 7-1 0-2 0-4-1 1-3 1-8-1-10-1-1-1-1-1-2 3-2 6-3 9-5z" class="I"></path><defs><linearGradient id="Aw" x1="779.619" y1="1230.294" x2="787.643" y2="1158.037" xlink:href="#B"><stop offset="0" stop-color="#1f201f"></stop><stop offset="1" stop-color="#444243"></stop></linearGradient></defs><path fill="url(#Aw)" d="M741 1158h1c1-2 0-1 1-3s1-3 2-5h0l2 2c2 2 4 6 6 8l10 13c12 15 26 27 41 39l1 1c1 1 2 1 3 2 2 2 5 4 8 6l2 2c2 1 1 0 2 1 2 1 3 1 4 2l1 1c1 1 0 1 1 2-2 3 0 6-2 8-6-2-10-5-15-8-27-17-52-43-68-71z"></path><defs><linearGradient id="Ax" x1="1170.611" y1="903.753" x2="1222.458" y2="891.701" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#848484"></stop></linearGradient></defs><path fill="url(#Ax)" d="M1179 885c1-1 2-1 4 0 3 0 7 1 10 1l31 1-2 23h-53c6-8 9-16 10-25z"></path><defs><linearGradient id="Ay" x1="738.955" y1="1311.075" x2="793.312" y2="1384.609" xlink:href="#B"><stop offset="0" stop-color="#d4d4d4"></stop><stop offset="1" stop-color="#fcfbfb"></stop></linearGradient></defs><path fill="url(#Ay)" d="M777 1285c1-1 1-2 3-2 1 0 2 0 3 1v2 2c2 4-1 20 2 22v51c0 9 1 19 0 28-1 3-2 6-5 8s-8 3-12 3c-6 1-19 5-25 3-2 0-3-2-5-4 0-3-1-6 0-8l6 4c9-1 27-1 33-7 1-2 1-3 1-5 1-9-1-18-1-26v-72z"></path><path d="M858 1167v-1c-1-14-1-28-1-42v-82-25c0-4 0-10-1-14 0-2-1-5-3-6-4-4-11-3-17-3-1 0-3 0-4-1v-2c2-2 15-1 18-1 4 0 7 1 9 5 2 2 2 4 3 6 1 7 0 14 0 21v32l1 116 13 1c4 0 7-1 10 1l-24 1-2 16c-1-5-2-11-2-16 0-2 1-4 0-6z" class="C"></path><defs><linearGradient id="Az" x1="1218.196" y1="1114.02" x2="1263.468" y2="1068.783" xlink:href="#B"><stop offset="0" stop-color="#323133"></stop><stop offset="1" stop-color="#5f605f"></stop></linearGradient></defs><path fill="url(#Az)" d="M1252 1046c0-2 0-3 1-4l2 1c0 2 0 6 3 8l6 6-3 5-6 17c-1 2-3 5-2 8v1c-4 14-12 27-18 40h0c-5-3-10-5-14-8 6-8 10-18 14-28 6-15 12-30 17-46z"></path><path d="M1252 1046c0-2 0-3 1-4l2 1c0 2 0 6 3 8l6 6-3 5h-1c-2 2-3 5-4 8l-4-23v-1z" class="Y"></path><defs><linearGradient id="BA" x1="772.323" y1="1370.846" x2="737.737" y2="1358.919" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#464546"></stop></linearGradient></defs><path fill="url(#BA)" d="M739 1349l30 1 2 25c0 3 0 5-2 8-3 3-9 2-13 1l-3-1c-5-1-12-2-17-4l-1-1c-1-5-1-10-1-15 1-5 0-9 2-13l3-1z"></path><path d="M736 1350l3-1c-1 10-1 20-2 29l-1 1-1-1c-1-5-1-10-1-15 1-5 0-9 2-13z" class="E"></path><defs><linearGradient id="BB" x1="909.351" y1="1002.34" x2="930.648" y2="998.556" xlink:href="#B"><stop offset="0" stop-color="#040504"></stop><stop offset="1" stop-color="#292729"></stop></linearGradient></defs><path fill="url(#BB)" d="M917 927l4-1h4l1 51v6 25l-1 6 1 11v1l1-1 2 1c-1 1-3 2-3 3-1 3 0 8 0 11l1 1c-1 2-1 6 0 9v1c3-2 7-4 9-7h1c-4 4-7 8-10 13v1 1l-3-1 3 7h-4c-2-1-2-2-3-4-5-15-4-29-3-45l1-70c0-6 1-14-1-19z"></path><path d="M921 926h4l1 51v6 25l-1 6 1 11v1l1-1 2 1c-1 1-3 2-3 3-1 3 0 8 0 11l1 1c-1 2-1 6 0 9v1c3-2 7-4 9-7h1c-4 4-7 8-10 13v1 1l-3-1c-5-16-2-34-3-51v-81z" class="B"></path><path d="M567 1373l25 1 1 49c-2 1-4 2-5 2-7 0-11-3-16-8-1-1-4-4-5-6s0-7 0-10v-28z" class="C"></path><defs><linearGradient id="BC" x1="733.513" y1="982.004" x2="781.761" y2="954.152" xlink:href="#B"><stop offset="0" stop-color="#767475"></stop><stop offset="1" stop-color="#8d8d8c"></stop></linearGradient></defs><path fill="url(#BC)" d="M755 921c1-3 1-5 1-8h4c1 3 0 7 1 10 2 1 5 0 7 2l-1 1v11c-1 18 0 38 0 56 0 5-2 15 0 19l1 1-3 2v7h-10V921z"></path><path d="M659 498l-2-1-1-1 20-80 28 8c0 3-1 6-2 8l-4 15h-2c-5-2-11-3-16-4-2 2-2 3-3 5v1h-2c0 1 0 2-1 3v1c0 2-1 3-1 4s-1 2-1 3l-1 3v3l-1 2c0 1 0 2-1 3l-2 7-3 9c-1 3-1 5-1 9 1-1 3-1 3-1 2-1 1-2 4-2l1 1h1l1 1-1 1-1 2h-1l-2 2-9-2z" class="B"></path><path d="M659 498l1-1 1-4v-2l1-3v-2l1-3v-1c1-1 1-2 1-3l1-2v-3l1-2c1-1 1-2 1-3l1-3v-3l1-2c1-1 1-2 1-3l1-2v-3l1-2c1-1 1-2 1-3l1-3v-3l1-2 1-1h1v2 3c-1 2-1 2-1 4l-1 1c0 1 0 2-1 3v1c0 2-1 3-1 4s-1 2-1 3l-1 3v3l-1 2c0 1 0 2-1 3l-2 7-3 9c-1 3-1 5-1 9 1-1 3-1 3-1 2-1 1-2 4-2l1 1h1l1 1-1 1-1 2h-1l-2 2-9-2zm648 442h-2l-1-3-2 1h1c1 2 2 3 2 6h-7c0-1-1-1-1-2 0-3-1-4-3-6l-1-3 2-2c0-1-1-6-3-7l-4-2c0 3 4 5 5 9h-2v-1a34.47 34.47 0 0 0-12-12h63l-2 26h-10l-3 1-2-3-18-2z" class="E"></path><path d="M1325 942c2-2 2-3 4-3l1 1v4l-3 1-2-3z" class="O"></path><path d="M607 118c4-5 7-9 14-9 6 0 11 3 15 7 2 2 4 5 5 7 0 3 1 19 1 20h1c4 3 6 5 6 10 1 8 1 17 1 26l-1 47v57c0 10 1 22 0 32-1 3-3 6-5 8-4 5-8 8-14 10 4-5 12-10 13-16 2-6 1-16 1-23v-53l-1-52c0-10 1-19 0-28 0-7-2-13-3-20-1-5 0-11-2-16 0-3-2-6-4-8-3-3-9-6-14-5-4 0-9 4-12 7l-1-1z" class="B"></path><path d="M711 794v-60c4 0 9-1 13 1v89c-2 2-3 3-4 5-2 2-2 4-3 6 0 4 1 10-1 13h0c0-4 0-7-1-11-1-1-1-3-1-5 0-1 0-2-1-2 0-1-1-2-1-3-1-3 0-7-1-11v-22z" class="J"></path><path d="M706 612c3-2 8-2 12-1l2 1 4 1c0 5 0 10 1 15v51c0 8-1 16 0 24 0 2 0 2-1 3l-13-2-1-87c0-3-2-3-4-5z" class="B"></path><defs><linearGradient id="BD" x1="958.296" y1="1334.908" x2="960.632" y2="1301.41" xlink:href="#B"><stop offset="0" stop-color="#6c6b6c"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#BD)" d="M961 1297c3-1 10-2 14-1l1 5 3 21c-4-2-6-4-9-7h-1c0 2 0 2 2 4 3 2 6 5 9 7l1 6c-3 2-8 1-11 2l-29 2c-1-11-3-23-1-33l-1-3c3-1 5-1 8-1 3-1 7 0 10-1 1-1 2-1 4-1z"></path><path d="M961 1297c3-1 10-2 14-1l1 5c-12 1-25 3-36 2l-1-3c3-1 5-1 8-1 3-1 7 0 10-1 1-1 2-1 4-1z" class="D"></path><path d="M901 211c2 0 4-1 6 0l1 33-38 6-10-30 41-9z" class="J"></path><path d="M1217 381h1c6-1 12-5 17-8 11 18 23 37 29 57-3 1-7 3-11 1v1c-1 2-2 4-4 5l-3 1c-4-4-7-12-9-16-6-12-13-26-22-36v-3l2-2z" class="C"></path><defs><linearGradient id="BE" x1="583.319" y1="456.731" x2="601.062" y2="452.585" xlink:href="#B"><stop offset="0" stop-color="#bbb9ba"></stop><stop offset="1" stop-color="#e1e0e0"></stop></linearGradient></defs><path fill="url(#BE)" d="M581 431l2 1 16 2-1 49c1 1 2 1 3 1v1l-3 1c-2 3-2 9-2 13-1 6-5 29-9 33h-2l6-24h-4c-1 0-2-1-4-1l2-2-2-1c-1 2 0 1-2 3l-1-1 1-3c-1-2-1-4 0-7 1-4 0-10 0-15v-8c-2 0-3 1-5 0v-9-7l4 2v-3-5c0-6 0-13 1-20z"></path><path d="M585 484l9 1c0 8-1 16-3 23h-4c-1 0-2-1-4-1l2-2-2-1c-1 2 0 1-2 3l-1-1 1-3c3-2 0-6 2-9 1-3-1-7 2-10z" class="K"></path><defs><linearGradient id="BF" x1="592.329" y1="474.555" x2="587.776" y2="459.523" xlink:href="#B"><stop offset="0" stop-color="#797878"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#BF)" d="M581 431l2 1h0v28l12-1v24c-3 0-7-1-10 0v1c-3 3-1 7-2 10-2 3 1 7-2 9-1-2-1-4 0-7 1-4 0-10 0-15v-8c-2 0-3 1-5 0v-9-7l4 2v-3-5c0-6 0-13 1-20z"></path><path d="M581 431l2 1h0c-1 7-1 14-1 20v29h-1v-8c-2 0-3 1-5 0v-9-7l4 2v-3-5c0-6 0-13 1-20z" class="U"></path><path d="M576 457l4 2c0 4 1 7 0 11-2 1-2 1-3 1l-1-7v-7z" class="F"></path><path d="M1098 1258c2-1 4-3 6-3s3 2 4 3c1 3 7 7 5 11h-1l2 1-6 6c3 3 7 6 9 10v2l-1 1c-4-3-7-6-11-9l-26 14c-3-3-8-12-9-16-1-1-1-3-1-4l-44 16c-1-2-1-4-1-6 25-7 48-17 71-29 1 1 2 1 3 3z" class="B"></path><path d="M1098 1258c2-1 4-3 6-3s3 2 4 3c1 3 7 7 5 11h-1l-5 5c-3-3-8-12-9-16z" class="T"></path><path d="M1092 1263l2-1c4 5 6 12 11 18l-26 14c-3-3-8-12-9-16-1-1-1-3-1-4l23-11z" class="V"></path><path d="M1069 1274l23-11c0 2 0 2-1 5-1 1-2 1-4 2-4 1-15 5-17 8-1-1-1-3-1-4z" class="I"></path><path d="M333 480l-2-16c-2-2-4-2-6-3-2-2-1-6 0-8h38v2c-2 1-3 1-4 3-2 4-2 13 0 17 0 1 2 2 4 3h5l7-7-1 9-3 27-2 10h-4l-5-1-3-1 2-25v-4c-9-2-18-2-26-6z" class="C"></path><path d="M357 515l2-25c0 4 0 8 1 12 1 2 0 5-1 8v4l1 2-3-1z" class="B"></path><path d="M365 517l1-2c0-3 1-6 1-10 1-4-1-14 1-17 1 1 1 0 2 1 2-3 3-6 4-9l-3 27-2 10h-4z" class="H"></path><path d="M913 924c1-1 1-6 1-8v-23h0c0 5-1 12 1 17 0-3-1-6 0-9v-3-1-3h2v30l-2 2 2 1c2 5 1 13 1 19l-1 70c-1 16-2 30 3 45 1 2 1 3 3 4h4c1 2 2 5 3 7l3 5v2c1 2 3 4 3 6l3 3 3 5 3 3h0c-1 2-7 9-8 10l-3 3h-1c-8-10-17-24-21-36 3-4 0-15 0-19 0-9-1-18 1-26V924z" class="G"></path><path d="M920 1061c1 2 1 3 3 4h4c1 2 2 5 3 7l3 5v2l-1 1c-2-1-3-2-4-3l-2 2c-1-2-1-3-1-5l1-1-1-3c-1-2-2-2-3-3l-1 2-5 4c0-2 0-3 2-4l2-7v-1z" class="O"></path><path d="M912 1073c3-4 0-15 0-19 0-9-1-18 1-26v1 15c0 2 0 4 1 6s1 5 1 7v1 5h2l1 1c-2 3-4 5-3 9 0 1 1 3 2 4 1-2 0-1 1-2 0-2 2-4 3-6l1-2c1 1 2 1 3 3l1 3-1 1c0 2 0 3 1 5-1 3-3 5-5 7 4 7 8 14 14 19h1l1 1-3 3h-1c-8-10-17-24-21-36z" class="L"></path><path d="M925 1074c0 2 0 3 1 5-1 3-3 5-5 7l-1-4c1-3 3-5 5-8z" class="G"></path><defs><linearGradient id="BG" x1="941.693" y1="1089.465" x2="922.645" y2="1090.202" xlink:href="#B"><stop offset="0" stop-color="#383839"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#BG)" d="M926 1079l2-2c1 1 2 2 4 3l1-1c1 2 3 4 3 6l3 3 3 5 3 3h0c-1 2-7 9-8 10l-1-1h-1c-6-5-10-12-14-19 2-2 4-4 5-7z"></path><path d="M933 1079c1 2 3 4 3 6l3 3 3 5-6-1c-3-3-1-7-4-11v-1l1-1z" class="F"></path><path d="M466 1002c2-1 3-1 6 0 8 2 15 9 19 15 5 11 5 23 2 35-1 0-1 0-2 1l-1 1h0l-2-3c-1 6-8 12-13 15l-7 3h-12c-8-1-16-6-21-12-2-4-8-11-7-15 1 0 9-1 9 0 1 0 2 3 2 4 2 2 4 5 7 7 5 4 14 5 20 4 5-1 9-4 11-7 6-8 4-17 3-25v-2c-2-5-11-10-15-11v-4c1-2 1-3 1-6z" class="E"></path><path d="M491 1017c5 11 5 23 2 35-1 0-1 0-2 1l-1 1h0l-2-3c2-6 4-13 4-18l-5-1v-1c1 0 1-1 2-1h1c0-3 1-3-1-6h-5c2-2 5-3 6-5l1-2z" class="I"></path><path d="M313 939c-4 3-7 6-11 8-1-1-1-1-2-1v2c-3 1-5 3-8 3-4 2-11 3-15 1-1-3-2-6-2-9 2-3 14-5 18-6 28-15 40-46 48-75l11 2-6 21c-5 16-13 31-24 45l-9 9z" class="B"></path><path d="M313 939l-3-3c-1-2-1-2-1-5l2-2c2-1 3-2 5-3v-1h1l1 2c1 1 2 2 4 3h0l-9 9z" class="E"></path><path d="M307 563l25 1c1 1 1 4 1 6s0 6-2 8l-3 1v1 2c0 2-1 4-3 6-2-1-6 0-8-1 0 0-1-1-2-1-1-1-4 0-6 0-9 0-21 2-29 0h-11c-7-3-18-1-26-1l-1-3-1-1 1-3v-2h-1c0-1 0-1 1-2l4-1h41c-1-1-2-3-2-4l2-1c6-1 14-2 20-1l2 1c2 0 5 0 6-1-1-1-2-1-3-2l-5-2z" class="X"></path><path d="M281 583l22 1v1c-3 2-18 1-22 0h-7c2-1 5-1 7-2z" class="N"></path><path d="M242 582l39 1c-2 1-5 1-7 2h7l-1 1h-11c-7-3-18-1-26-1l-1-3z" class="J"></path><path d="M308 573c8-1 15-1 23 0 0 2 0 3 1 4l-1 1-3 1c-3-2-10-1-15-2v-2h-2v1c-1 2-7 4-10 5h-9c-5 0-10 0-15-1h-15c3 0 6 0 8-1 3 0 6-2 9-1 9 1 18 1 27-1 1 0 3 0 4-1l-2-3z" class="L"></path><path d="M313 575c2-1 4-1 7-2l1 1c2 1 6-1 8 0 1 1 2 3 3 3l-1 1-3 1c-3-2-10-1-15-2v-2z" class="D"></path><path d="M246 573h41l1 1c6-1 13 1 20-1l2 3c-1 1-3 1-4 1-9 2-18 2-27 1-3-1-6 1-9 1-2 1-5 1-8 1-6-1-15 2-20-2v-2h-1c0-1 0-1 1-2l4-1z" class="M"></path><path d="M246 573h41l1 1-30 2c-5 0-11 1-16 0h-1c0-1 0-1 1-2l4-1z" class="T"></path><path d="M307 563l25 1c1 1 1 4 1 6s0 6-2 8l1-1c-1-1-1-2-1-4-8-1-15-1-23 0-7 2-14 0-20 1l-1-1c-1-1-2-3-2-4l2-1c6-1 14-2 20-1l2 1c2 0 5 0 6-1-1-1-2-1-3-2l-5-2z" class="L"></path><path d="M289 569c7-1 13-1 20 0h2l2 2c6 1 14 1 20-1 0 2 0 6-2 8l1-1c-1-1-1-2-1-4-8-1-15-1-23 0-7 2-14 0-20 1l-1-1c-1-1-2-3-2-4l2-1 2 1h0z" class="S"></path><path d="M289 569c7-1 13-1 20 0h2l2 2h-17c-3 0-6 1-9-1l2-1z" class="B"></path><defs><linearGradient id="BH" x1="1268.774" y1="1134.573" x2="1252.122" y2="1061.809" xlink:href="#B"><stop offset="0" stop-color="#212120"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#BH)" d="M1260 1050c3 3 6 5 11 5 2 0 4 0 6-1h1l-1 2c0 3 0 4 2 5v1c3 0 4 0 7 1l-9 24 1 2c-1 3-3 7-5 10l-4 7c-3 5-5 12-8 17h-1l-2 2c-1 2-2 3-3 4l1-4-2-1c-5 2-2 5-4 9 0 0-1 1-1 2l-2 2-1 1v3l-1-1 3-9c0-3 1-4 2-6 0-4-2-7-4-9l-1-1 13-29-2-5-3 7v-1c-1-3 1-6 2-8l6-17 3-5-6-6 2-1z"></path><path d="M1262 1067c1 2 1 7 0 9 0 3-3 7-4 10l-2-5 6-14z" class="O"></path><path d="M1260 1050c3 3 6 5 11 5 2 0 4 0 6-1h1l-1 2c-4 1-7 1-12 1l-3 10-6 14-3 7v-1c-1-3 1-6 2-8l6-17 3-5-6-6 2-1z" class="H"></path><path d="M1279 1062c3 0 4 0 7 1l-9 24c-2 4-4 7-5 11h-1l3-8c1-3 1-5 1-8-1-7 2-13 4-20z" class="F"></path><defs><linearGradient id="BI" x1="820.061" y1="902.507" x2="795.304" y2="875.853" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#BI)" d="M834 869c2 1 6 3 8 5 4 4 6 7 8 12l-2 15v-3l-1-1c-2-1-6 1-8 2-8 3-18 3-26 4-5 0-10 1-15 0 0-2 0-5-1-7 0-5 0-11-1-16 0-3 0-4-2-6 4-2 15-1 20-1l2 2c1-1 1-1 1-2 4-3 12-4 17-4z"></path><path d="M827 898c3-4 7-5 11-7 3-2 5-4 8-4l1 2c-5 4-12 5-17 9l-1 1-1 1-1-1v-1z" class="F"></path><path d="M834 869c2 1 6 3 8 5 1 3 2 4 3 6s2 2 1 4v-1c-1-1-2-2-3-2 1 1 1 1 2 3 0 1 1 2 1 3-3 0-5 2-8 4-4 2-8 3-11 7-2-3-2-6-4-9-2-5-5-9-7-14 1-1 1-1 1-2 4-3 12-4 17-4z" class="Q"></path><defs><linearGradient id="BJ" x1="756.551" y1="609.289" x2="755.533" y2="559.541" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#656464"></stop></linearGradient></defs><path fill="url(#BJ)" d="M732 567l1 1 1-2c3-3 8-6 12-6 5 0 11-1 16 0l8 2c3 0 6 1 9 2-1 2-1 1 0 2 0-1 0-2 1-4v-1 40c0 8 1 16 0 23-3-2-2-3-2-7v-6c-2 1-18 0-22 0 1-13 0-24-8-35-5-6-9-7-16-8v-1z"></path><path d="M760 600c4-1 15-2 18 0v2l-1 1c-5-1-11 1-16 1-2-2-2-2-1-4z" class="T"></path><defs><linearGradient id="BK" x1="1150.684" y1="1025.098" x2="1169.466" y2="1039.398" xlink:href="#B"><stop offset="0" stop-color="#9d9c9e"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#BK)" d="M1106 1016l-1-2 1-1c3 0 5-1 8-1l1 1c-1-2-1-2-2-5l6 3c10 5 19 10 29 13 10 5 20 9 29 15 2 1 3 2 5 3l-2 3c-2 4-3 8-6 11v-2l-13-4-1 1c0 1 1 2 1 3-1 0-2 0-4 1-1 0-3 0-4-1-1 2-1 3-2 4l-20-11v-4l4-2h1l-1-1c-2-2-4-4-8-6h-1v-1c-2 0-2 0-4-1l1-2-14-7h6c-2-1-3-1-3-3l-1-1-5-3z"></path><path d="M1176 1043c-2-1-2-2-3-4h4c2 1 3 2 5 3l-2 3-3-1-1-1z" class="E"></path><path d="M1167 1039c3 1 4 1 6 3l3 1 1 1-1 3-1 1-5-4-2-2h-1l1-1-1-2z" class="M"></path><path d="M1177 1044l3 1c-2 4-3 8-6 11v-2l-1-1-8-6c2-2 3-2 5-3l5 4 1-1 1-3z" class="H"></path><defs><linearGradient id="BL" x1="1142.071" y1="1025.227" x2="1163.038" y2="1044.902" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#a1a1a1"></stop></linearGradient></defs><path fill="url(#BL)" d="M1140 1024c2 0 5 0 6 1s2 3 3 4c6 4 12 6 18 10l1 2-1 1h1l2 2c-2 1-3 1-5 3-1 0-4-2-5-3l-20-10c-1-2-1-3 0-5v-3-2z"></path><defs><linearGradient id="BM" x1="1121.97" y1="1014.349" x2="1120.398" y2="1029.006" xlink:href="#B"><stop offset="0" stop-color="#575657"></stop><stop offset="1" stop-color="#747575"></stop></linearGradient></defs><path fill="url(#BM)" d="M1106 1016l-1-2 1-1c3 0 5-1 8-1l1 1c-1-2-1-2-2-5l6 3 1 2c6 4 14 7 20 11v2 3c-1 2-1 3 0 5l-29-15-5-3z"></path><defs><linearGradient id="BN" x1="1140.691" y1="1038.828" x2="1147.876" y2="1056.626" xlink:href="#B"><stop offset="0" stop-color="#979596"></stop><stop offset="1" stop-color="#c6c4c5"></stop></linearGradient></defs><path fill="url(#BN)" d="M1111 1019l29 15 20 10c1 1 4 3 5 3l8 6 1 1-13-4-1 1c0 1 1 2 1 3-1 0-2 0-4 1-1 0-3 0-4-1-1 2-1 3-2 4l-20-11v-4l4-2h1l-1-1c-2-2-4-4-8-6h-1v-1c-2 0-2 0-4-1l1-2-14-7h6c-2-1-3-1-3-3l-1-1z"></path><defs><linearGradient id="BO" x1="1123.444" y1="1043.242" x2="1156.912" y2="1030.2" xlink:href="#B"><stop offset="0" stop-color="#0e0e13"></stop><stop offset="1" stop-color="#52514d"></stop></linearGradient></defs><path fill="url(#BO)" d="M1111 1019l29 15 20 10c1 1 4 3 5 3l8 6 1 1-13-4-38-20-14-7h6c-2-1-3-1-3-3l-1-1z"></path><path d="M456 935l-2-1c1-3 0-4 2-5 13-1 25 5 35 14l15 15c20 20 37 44 39 72 1 23-10 45-26 61-12 13-26 22-44 26-4 1-8 1-12 2 3-2 8-3 12-4 11-4 19-8 28-16 12-11 23-25 30-40l3-5c3-7 5-14 2-21l-2-19 3-3-5-1c-2-7-6-14-9-21l-5-8-7-8c-4-4-8-7-13-11-12-11-26-27-44-27z" class="C"></path><defs><linearGradient id="BP" x1="1235.538" y1="635.615" x2="1226.389" y2="589.453" xlink:href="#B"><stop offset="0" stop-color="#676767"></stop><stop offset="1" stop-color="#929292"></stop></linearGradient></defs><path fill="url(#BP)" d="M1191 636c7-1 15 0 23 0l-1-45 26-1c0 3-1 6 0 9 2 2 4 5 6 7 4 6 8 12 11 19l-1 2h-2c-3 4-6 9-12 10l-50-1z"></path><defs><linearGradient id="BQ" x1="543.94" y1="510.494" x2="530.724" y2="446.329" xlink:href="#B"><stop offset="0" stop-color="#151415"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#BQ)" d="M521 447l4-1 1 1h-2v1l19 5c1 5 1 10 0 16l-6 3 3-1c2 0 2 1 3 1 1 9-2 18-1 27l18 1c3 0 6 0 8 1l1 4c-4 4-13 1-18 3 0 1 0 2-1 3-4 1-8 0-12 0l-21-1 4-63z"></path><path d="M810 620c-1-19 0-38 0-56l-1-116v-35c0-6 1-12 0-18-4-5-17-4-24-6 7-2 15-2 22-2 3 0 4 0 7 3 1 1 1 2 1 3 1 7 0 15 0 22v46 152c-3 7-1 18-1 26v36c0 2 0 9-1 11-1 1-1 2-1 3l-1 1v-70h-1z" class="D"></path><defs><linearGradient id="BR" x1="640.669" y1="1040.724" x2="679.448" y2="1022.517" xlink:href="#B"><stop offset="0" stop-color="#2b2b2c"></stop><stop offset="1" stop-color="#4a4a49"></stop></linearGradient></defs><path fill="url(#BR)" d="M666 953c1 10 0 21 1 31l1 47v19c0 6 4 7 6 11v6c-2 0-4-1-6 0-3 1-9 2-12 0-3-1-5-3-7-6-2-2-3-6-3-10-1-10 0-21 0-31 0-7-1-16 1-24 3-1 8 0 11 1l2 1 1-1c1-2 1-3 1-5l3-1v3l1-41h0z"></path><path d="M653 1025c2 0 5 0 8 1v3 1c-1 2-1 3-2 4-3 0-6 0-8-1l-1-2c0-3 1-4 3-6z" class="G"></path><path d="M647 996c3-1 8 0 11 1l2 1c-1 3 0 6-2 9-3 0-5-2-7-3-1-1-2-1-3-1l-1-7z" class="Q"></path><defs><linearGradient id="BS" x1="667.582" y1="981.46" x2="626.913" y2="957.257" xlink:href="#B"><stop offset="0" stop-color="#cecdcd"></stop><stop offset="1" stop-color="#f6f6f6"></stop></linearGradient></defs><path fill="url(#BS)" d="M639 1047V934c4 0 10-1 14 0l-1 10 2 1 1 1 5-2 1 3c2 7 1 13 0 20v30l-1 1-2-1c-3-1-8-2-11-1-2 8-1 17-1 24 0 10-1 21 0 31 0 4 1 8 3 10 2 3 4 5 7 6 3 2 9 1 12 0-3 4-7 3-12 4-2-2-5-3-7-5-3-3-4-7-5-10-2-9 0-21 0-30l-1-46c-1 6-1 13-2 19l-1 41c0 2 0 5-1 7z"></path><path d="M653 948c3 0 4 1 7 3 1 3 0 5 0 8-2 2-3 2-5 3h-3c-2-1-3-2-4-3-1-3-1-5 0-7s2-3 5-4z" class="T"></path><path d="M710 938l1-1c3-1 10 1 12 3 5 3 9 8 11 13 3 8 1 21 1 30v53c0 6-1 15 1 21s8 9 12 11l-1 13c-4-2-8-4-12-7s-7-7-9-11c-2-8-2-18-2-26v-50c0-8 1-17 0-25-1-6-3-9-8-12-2-1-5-1-7-3l1-9z" class="B"></path><defs><linearGradient id="BT" x1="983.778" y1="1156.173" x2="977.969" y2="1105.446" xlink:href="#B"><stop offset="0" stop-color="#0d0c0e"></stop><stop offset="1" stop-color="#3d3d3c"></stop></linearGradient></defs><path fill="url(#BT)" d="M957 1085c2 2 3 3 4 6l-1 7 1-1-1 3c-1 1-1 2-1 3v1l1 1c1-2 2-5 3-7h3l2-4c0-1 1-3 1-4 4 4 3 10 2 15h1 3l-1 5 2-1-1 4c2 1 2 3 4 5 1 3 1 3 0 6l6 3 9 4 2 1 3 1 2 1 10 3h1l6 2h0l-2 1c-1 5-1 10 0 15l-13-3c-14-4-26-11-37-18-9-5-18-12-23-21l-1-4c1-3 5-7 5-10h1c3-3 5-6 6-9l3-5z"></path><path d="M975 1113c2 1 2 3 4 5 1 3 1 3 0 6l-8-4 4-7z" class="B"></path><path d="M947 1099h1c0 3 4 7 7 8l1 1c-1 1-1 2-2 2l-2 1c-3 0-6 2-9 2l-1-4c1-3 5-7 5-10z" class="G"></path><path d="M957 1085c2 2 3 3 4 6l-1 7c-2 3-4 6-4 10l-1-1c-3-1-7-5-7-8 3-3 5-6 6-9l3-5zm12 5c4 4 3 10 2 15h1 3l-1 5c-1 2-1 4-3 6-2 1-2 1-4 0l-8-6 7-12 2-4c0-1 1-3 1-4z" class="C"></path><path d="M969 1090c4 4 3 10 2 15h1 3l-1 5c-1 2-1 4-3 6-2 1-2 1-4 0l1-1c1-3 3-15 2-19-1-2-1-1-2-2 0-1 1-3 1-4z" class="S"></path><path d="M1204 543l56-1c0 4 1 12 0 15-3 6-11 12-15 17-2 1-4 3-6 4v3c-5 1-11 0-16 0h-26c4-1 8-1 12-1l-5-37z" class="R"></path><defs><linearGradient id="BU" x1="1175.005" y1="1027.069" x2="1242.923" y2="1117.127" xlink:href="#B"><stop offset="0" stop-color="#201f1e"></stop><stop offset="1" stop-color="#4e4f50"></stop></linearGradient></defs><path fill="url(#BU)" d="M1168 1161l1-2 6-6c1-3 4-5 6-8l1-1c6-8 12-17 16-25l7-12c3-5 7-12 9-18 0-2 2-4 3-7l3-6 2-7 1-2 3-7 1-3 6-24c1-1 1-2 1-3l1-4 2-6 2-12c1-4 0-9 2-13v-6c1-4 1-8 2-12v-3c3-2 6-2 9-2l-7 45c-10 44-26 89-51 127-5 8-11 14-17 22-2 3-5 7-7 10-1-2-2-3-4-5v-1c-3-5 1-4 2-9z"></path><defs><linearGradient id="BV" x1="1289.988" y1="601.296" x2="1277.122" y2="545.667" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#cdcccc"></stop></linearGradient></defs><path fill="url(#BV)" d="M1265 558c0-3 1-6 1-9h1c2-1 3-1 5-1 4 2 10 3 14 3 2 0 5 1 6 0s1 0 1-2c0-1 0 0 1-1v-1-1l3-2-1 1c-4 10-3 24 1 34 2 4 4 8 7 9s7 1 9 1c5 0 13 1 18 0 1 1 1 3 2 4l1 1c-8 2-18 1-26 1l-39 1c0-4-1-11-5-14-4-2-15-1-20-1 7-7 16-15 21-23z"></path><path d="M390 282l51 9c14 3 26 9 36 20 5 6 9 12 13 18l-65-10c-2-1-5-7-7-9-8-11-16-20-28-28z" class="C"></path><defs><linearGradient id="BW" x1="668.253" y1="1254.195" x2="684.353" y2="1202.65" xlink:href="#B"><stop offset="0" stop-color="#424142"></stop><stop offset="1" stop-color="#5d5d5c"></stop></linearGradient></defs><path fill="url(#BW)" d="M686 1260c-4-2-8-6-11-9-7-6-15-12-21-19v-21c0-2-1-4 1-6 3 0 6 0 8 2l1 1h1c5 0 10-5 13-9v-1h3l1 1c2 0 3 0 4 2 3 3 4 7 6 11 4 9 7 18 12 26-5 4-7 9-8 15l1 1c-1 3-3 6-3 9l-1 1-3-2c-2-1-1-1-2-2l-1-1-1 1z"></path><path d="M690 1262c1-2 3-3 4-5l2-4 1 1c-1 3-3 6-3 9l-1 1-3-2z" class="K"></path><path d="M674 1242c1 0 2 1 2 2l1 3 2-1c1 2 2 3 3 4l-1 2c-2-1-6-4-7-6s-1-2 0-4zm292-224c3 1 3-1 6 0-1 1 0 1-1 2-1 2-1 3-2 5l3-1 1 1c-1 2-2 3-2 5l2-1v1c0 3-1 5-3 8 3-1 3-3 6-2 1 3 1 5-1 8h-1l-10 19-8 14c-1 1-2 4-3 5l-2 3 3 5c-1 3-3 6-6 9h-1l-2-3h0l-3-3-3-5-3-3c0-2-2-4-3-6v-2l-3-5c-1-2-2-5-3-7l-3-7 3 1v-1-1c3-5 6-9 10-13 3-2 7-6 10-9l19-17z" class="U"></path><path d="M930 1060h0c0 2-1 3 0 5 4-2 7-7 10-10l1-1c-3 7-7 13-11 18-1-2-2-5-3-7l-3-7 3 1 3 1z" class="D"></path><path d="M933 1077l9-9h1l-2 4c2-1 2-1 4-3 1-1 1-2 3-2-3 5-8 11-10 17l-2 1c0-2-2-4-3-6v-2z" class="J"></path><path d="M948 1045h1c0 1-1 2-2 3s0 0-1 2c-1 1-3 4-5 4l-1 1c-3 3-6 8-10 10-1-2 0-3 0-5h0c0-1 0-1 1-2l1-1c5-4 9-10 16-12z" class="F"></path><path d="M938 1084c1 1 1 2 3 3 1-2 1-2 2-3 6-5 9-13 15-18 7-6 10-16 16-24v2l-10 19-8 14c-1 1-2 4-3 5l-2 3 3 5c-1 3-3 6-6 9h-1l-2-3h0l-3-3-3-5-3-3 2-1z" class="S"></path><path d="M939 1088c1 0 2 0 3 1h1l1-1c2-3 3-5 6-6 1-1 2-1 3 0l-2 3 3 5c-1 3-3 6-6 9h-1l-2-3h0l-3-3-3-5z" class="B"></path><path d="M951 1085l3 5c-1 3-3 6-6 9h-1l-2-3h0c2-3 4-7 6-11zm15-67c3 1 3-1 6 0-1 1 0 1-1 2l-2 1h-1c-2 4-6 6-8 10-2 3-7 6-9 10-1 1-3 2-3 4-7 2-11 8-16 12l-1 1c-1 1-1 1-1 2l-3-1v-1-1c3-5 6-9 10-13 3-2 7-6 10-9l19-17z" class="Q"></path><path d="M628 1137l2 2h2c3-1 7 0 10 0 2-1 5 0 8-1h12l3 1-7 1h0l4 1c2 1 4 1 5 3l1 2 5 4h1l9 7-1 2-5 12-2 1c-1 0-3-1-4-2h-1l-1 1c1 1 2 1 2 3-1 2-5 2-7 2-14 0-30 1-43-2-3-2-4-7-6-10 1-2 1-1 1-3 0-4 1-9 3-12s6-5 6-8l-3-1c1-1 1-1 3-1 1 0 2 0 3-1v-1z" class="C"></path><path d="M628 1137l2 2h2c3-1 7 0 10 0 2-1 5 0 8-1h12l3 1-7 1h0l4 1c1 3 1 5 1 8h-1c0 2 1 4-1 6v-1-12l-30-1v1c0 10-1 20-3 30 4 0 14 1 18-1v2h-17c-2 0-6 0-8 1-3-2-4-7-6-10 1-2 1-1 1-3 0-4 1-9 3-12s6-5 6-8l-3-1c1-1 1-1 3-1 1 0 2 0 3-1v-1z" class="P"></path><path d="M662 1141c2 1 4 1 5 3l1 2 5 4h1l9 7-1 2-5 12-2 1c-1 0-3-1-4-2h-1l-1 1c1 1 2 1 2 3-1 2-5 2-7 2-14 0-30 1-43-2 2-1 6-1 8-1h17v-2c-2-3-3-6-3-10 1-2 2-4 4-6 4-3 10-2 14-1v1c2-2 1-4 1-6h1c0-3 0-5-1-8z" class="T"></path><defs><linearGradient id="BX" x1="675.225" y1="1160.114" x2="667.101" y2="1163.59" xlink:href="#B"><stop offset="0" stop-color="#1c1a1c"></stop><stop offset="1" stop-color="#363636"></stop></linearGradient></defs><path fill="url(#BX)" d="M662 1141c2 1 4 1 5 3l1 2 5 4h1l9 7-1 2-5 12-2 1c-1 0-3-1-4-2h-1l-1 1-1 1 1 1c-2 1-2 1-4 0 2-4 3-6 2-11 0-4-3-5-6-7 2-2 1-4 1-6h1c0-3 0-5-1-8z"></path><path d="M668 1146l5 4h1l9 7-1 2-4-3h0l-1-1-4-1c-2-2-4-5-5-8z" class="F"></path><path d="M675 1172c-1-2-2-3-3-5 1-4 2-9 6-11l4 3-5 12-2 1z" class="I"></path><path d="M952 379h2c3 3 4 6 6 10l-12 9c-12 11-24 26-29 42s-4 35-4 52v62c0 11 1 23 0 34h-2c-2 0-4 0-6-2l-1-72c0-20-1-42 2-63 4-30 20-54 44-72z" class="J"></path><defs><linearGradient id="BY" x1="1077.759" y1="1101.945" x2="1116.482" y2="1111" xlink:href="#B"><stop offset="0" stop-color="#cecdce"></stop><stop offset="1" stop-color="#f7f6f7"></stop></linearGradient></defs><path fill="url(#BY)" d="M1061 1068c3-2 6-5 8-7 2 1 4 2 6 4 1 1 1 1 2 1l1 2 27 20c2 1 6 4 7 6 4 3 14 12 19 13v2l-9 6-4 3c-5 4-11 7-17 10l-1-1c-2-1-4-4-5-6-3-4-7-8-10-11-2-3-6-7-8-10l-14-15c-3-3-7-6-10-10l1-1v-2l4-3c1-1 2-1 3-1z"></path><defs><linearGradient id="BZ" x1="1068.217" y1="1090.129" x2="1082.321" y2="1088.242" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#5f5f60"></stop></linearGradient></defs><path fill="url(#BZ)" d="M1064 1072l21 22c-1 3-1 4 0 7 1 1 1 1 1 2-2-1-3-2-4-4s-2-3-4-4c-2-2-3-4-6-4l-1-1-4-6c-3-5-4-7-3-12z"></path><path d="M1072 1091c3 0 4 2 6 4 2 1 3 2 4 4s2 3 4 4c0-1 0-1-1-2-1-3-1-4 0-7 6 8 15 18 17 27-2-2-4-5-6-6-1-1-4-1-5-1l-19-23z" class="S"></path><path d="M1061 1068c3-2 6-5 8-7 2 1 4 2 6 4 1 1 1 1 2 1l1 2c0 2 2 3 3 4 1 3 1 6 2 9-2 0-3-2-5-4 1 3 4 6 4 9-7-6-13-13-21-18h0z" class="U"></path><path d="M1069 1061c2 1 4 2 6 4 1 1 1 1 2 1l1 2c0 2 2 3 3 4 1 3 1 6 2 9-2 0-3-2-5-4l-9-12-1-1v-1h1v-2z" class="K"></path><path d="M1078 1077c2 2 3 4 5 4h1c6 5 11 12 17 17 1 0 2 1 3 1l16 15-2 4c-1-1-2-1-3-1-2-1-7-6-9-8-8-8-17-15-24-23 0-3-3-6-4-9z" class="F"></path><path d="M1058 1069c1-1 2-1 3-1h0v2l3 2c-1 5 0 7 3 12l4 6 1 1 19 23c1 0 4 0 5 1 2 1 4 4 6 6l2 2v1l-4 3c-2-1-4-4-5-6-3-4-7-8-10-11-2-3-6-7-8-10l-14-15c-3-3-7-6-10-10l1-1v-2l4-3z" class="U"></path><path d="M1091 1114c1 0 4 0 5 1 2 1 4 4 6 6l2 2v1l-2-1-3 1-8-10z" class="V"></path><path d="M1058 1069c1-1 2-1 3-1h0v2l3 2c-1 5 0 7 3 12l4 6c-2-1-3-3-4-4l-1-1c-3-5-4-9-9-12v-1l1-3z" class="F"></path><path d="M1081 1072c-1-1-3-2-3-4l27 20c2 1 6 4 7 6 4 3 14 12 19 13v2l-9 6-4 3h0l2-4-16-15c-1 0-2-1-3-1-6-5-11-12-17-17h-1c-1-3-1-6-2-9z" class="H"></path><path d="M1081 1072l9 7c-2 1-4 2-6 2h-1c-1-3-1-6-2-9z" class="P"></path><path d="M1122 1115c-1-2 0-4-1-6-2-4-9-8-10-12 0-2 0-2 1-3 4 3 14 12 19 13v2l-9 6z" class="B"></path><path d="M1090 1079c8 6 15 16 21 23v1c-2-1-4-3-6-5l-1 1h0c-1 0-2-1-3-1-6-5-11-12-17-17 2 0 4-1 6-2z" class="X"></path><defs><linearGradient id="Ba" x1="373.118" y1="1030.423" x2="417.599" y2="989.936" xlink:href="#B"><stop offset="0" stop-color="#686868"></stop><stop offset="1" stop-color="#818080"></stop></linearGradient></defs><path fill="url(#Ba)" d="M406 975c7 7 12 15 20 21-4 5-12 16-12 23v2l-3 13c-12 1-26 2-38 0 1-2 1-6 1-8 1-7 1-15 3-22 0-3 2-7 0-10 1-2 3-7 5-8 3-1 7-2 10-3 5-2 10-5 14-8z"></path><path d="M1118 870l43 1 3 1c1 2 1 3 1 5l-3 2 1 1 14 1c1 1 1 2 2 4-1 9-4 17-10 25l-57-2 6-38z" class="F"></path><path d="M1135 879h1c2 2 2 1 2 4l-2 1c-1 1-1 0-2 0 0-3 0-3 1-5z" class="T"></path><path d="M283 298c4 0 8 0 12 1 11 3 19 12 25 21 13 23 17 48 17 73 0 13-1 24-4 37v-24h0c-2 3-1 8-3 10h-8v-3l1 2c2 0 3 1 5 0l1-2 1-3c-1-2-3-2-5-2l-3 1c-1-1 0-6 0-8l-1-21c-1-14-5-29-11-42l2 25c-1-1-1-4-1-6l-1-4c-3-15-10-35-24-44-6-4-14-3-21-1h-1c6-5 11-9 19-10z" class="C"></path><path d="M629 944v-67-149-109-51c0-4 0-9 1-12l2-1 1 1 1 99 1 142-1 50c0 4 1 11 0 15v2h-1l1 2v78c-2 2-1 5-1 8v6 10c-1 3 0 6 0 9h-3v-21c0-3 1-8 0-11l-1-1z" class="H"></path><defs><linearGradient id="Bb" x1="938.823" y1="707.409" x2="958.848" y2="659.921" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#f9f9f9"></stop></linearGradient></defs><path fill="url(#Bb)" d="M921 669c3-3 10-2 14-3l35-4v30c-4 1-8 1-11 3h11v12c-5 3-10 5-16 8-1 0-4 2-6 2l-17 8c-2-2-2-1-4-2-2-5-1-13-1-18l1-12v-2-12c0-3 0-7-2-10h-4z"></path><path d="M927 679l4 3-1 1v1c1 2-1 4-2 5 0 1 0 1-1 2v-12z" class="H"></path><path d="M952 706l-1-2c2 1 5 1 6 2-1 3-2 4-5 6-1 1-2 1-4 1 2-1 4-2 5-4l-1-3z" class="R"></path><path d="M927 693v2c1 2 1 5 1 8 2-1 3-2 6-1l1 1c4 2 11 0 13 2h-1-21l1-12z" class="H"></path><path d="M926 705h21l5 1 1 3c-1 2-3 3-5 4 2 0 3 0 4-1l2 2h-2c-1 0-3 1-4 2l-2 1h2l-17 8c-2-2-2-1-4-2-2-5-1-13-1-18z" class="W"></path><path d="M505 1175h2c2 6 2 12 3 17h-20c-3 1-3 1-5 3 0 3 0 3 1 6 6 3 20 1 27 1l9 32-16 1c-8 0-19 1-27-1-5-2-9-5-13-8v-43l39-8z" class="C"></path><path d="M612 1067l1-1c0-5-1-18 2-22l3-1 1 1 2 53v4c2 0 3 1 5 0 3-1 4-3 5-5 0 1 0 1 2 3 1 0 2 2 3 3h4c4 0 7 0 11 1h4l1-1 3-1 1 1v-1c4-3 5-7 6-11v-2h0c4-1 7-1 10-1l8 30h-8c-2 0-4 1-5 2h-1c-2 0-1 0-3 1h0c-1 0-3 1-4 1h0c0 2 1 2 0 3v2l2 1c-1 1-1 2-2 3s-2 2-2 3l3 3-2 2h-12c-3 1-6 0-8 1-3 0-7-1-10 0h-2l-2-2c1-3 3-4 5-5l2-2c1-3 1-4 0-7-1-1-2-2-4-3l-1-5-2-1c-1-1-4-1-6-1l-5 1-2-3v-1c1-3 1-5 1-8h-1v4l-1 1v-7l-1 8c0-2 0-1-1-2-1-2 0-5 0-7l-1-2c-1 0-2 0-3-1 1-3 1-3 3-5v-6l1-1v-1l-1-1-1-14 2-1z" class="G"></path><path d="M633 1112l3-3c1-1 2-1 3-2l-2-2h0 4c4-2 8-1 13-1l1 1 3 2h-8c5 1 10 1 15 3h0 2 1 1 1v1c1 2 1 4 2 6-2 0-2-1-3 0l-2 1v2c-1 0-3 1-4 1h0-5l1-1h-7l-1-1c-3-1-6 0-10 0v1c-3 0-5-1-8-2 0-2-1-3-1-6h1z" class="X"></path><path d="M668 1110h1 1v1c1 2 1 4 2 6-2 0-2-1-3 0l-2 1v2c-1 0-3 1-4 1h0-5l1-1h-7l-1-1 16-7 1-2z" class="W"></path><path d="M668 1110h1 1v1c1 2 1 4 2 6-2 0-2-1-3 0l-2 1v2c-1 0-3 1-4 1v-1l6-7-2-1 1-2z" class="Y"></path><path d="M633 1112l3-3c1-1 2-1 3-2l-2-2h0 4c4-2 8-1 13-1l1 1 3 2h-8-3l1 3h-1c0 1 0 2-1 3l1 1 3-3c3-1 5-1 8 0l1 1c-2 1-3 1-5 2-3 1-4 1-8 1-1-2-1-1-1-2 0-2-2-4-3-6l-3 2c-2 1-4 1-5 4h0l4 4c-2 0-2 0-4-1 0-1-1-2-1-4z" class="P"></path><path d="M654 1104l1 1 3 2h-8-3l-3-1v-1h9l1-1z" class="D"></path><path d="M666 1088c4-1 7-1 10-1l8 30h-8c-2 0-4 1-5 2h-1c-2 0-1 0-3 1h0v-2l2-1c1-1 1 0 3 0-1-2-1-4-2-6v-1h-1-1-1-2 0c-5-2-10-2-15-3h8l-3-2c2-1 3-2 5-3v-1c4-3 5-7 6-11v-2h0z" class="I"></path><path d="M666 1090c2-1 2-1 4-1 1 3-2 9-4 12-2 4-4 5-8 6l-3-2c2-1 3-2 5-3v-1c4-3 5-7 6-11z" class="N"></path><path d="M666 1088c4-1 7-1 10-1l8 30h-8c-2 0-4 1-5 2h-1c-2 0-1 0-3 1h0v-2l2-1c1-1 1 0 3 0l6-3c2-6-4-13-3-18 1-2 1-3 0-4v-2-1c-3-1-6-1-9-1zm-25 32v-1c4 0 7-1 10 0l1 1h7l-1 1h5c0 2 1 2 0 3v2l2 1c-1 1-1 2-2 3s-2 2-2 3l3 3-2 2h-12c-3 1-6 0-8 1-3 0-7-1-10 0h-2l-2-2c1-3 3-4 5-5 1 0 2 1 4 1l1-1c0-2-1-4-1-6h0c0-1 0-2 1-3v-1c2 0 4 0 5-1l-2-1z" class="L"></path><path d="M654 1132l1-1c1-1 1 0 1-2h2l3 4 3 3-2 2h-12c-3 0-5 0-7-1 0-1 1-2 2-3l4 1c2 0 3-2 5-3z" class="J"></path><path d="M654 1132v-1c-3-2-6 0-9-2l1-3c2-2 9-4 12-5h5c0 2 1 2 0 3v2l2 1c-1 1-1 2-2 3s-2 2-2 3l-3-4h-2c0 2 0 1-1 2l-1 1z" class="D"></path><defs><linearGradient id="Bc" x1="739.845" y1="1317.289" x2="724.107" y2="1253.807" xlink:href="#B"><stop offset="0" stop-color="#232223"></stop><stop offset="1" stop-color="#4a4949"></stop></linearGradient></defs><path fill="url(#Bc)" d="M697 1254l2 3c3 2 6 1 10 1 10 0 24-2 32 4v1l15 12c1 1 3 3 5 4 2 2 0 1 2 2s7 6 7 8l-1 2c-3 1-3 2-4 5l-2 18c-5 0-8-2-12-4-3-1-6-1-8-1l-57-49 1-1 1 1c1 1 0 1 2 2l3 2 1-1c0-3 2-6 3-9z"></path><path d="M697 1254l2 3v3c-1 1-2 3-4 3h-1c0-3 2-6 3-9z" class="G"></path><defs><linearGradient id="Bd" x1="757.209" y1="570.055" x2="763.413" y2="519.483" xlink:href="#B"><stop offset="0" stop-color="#5b5b5b"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#Bd)" d="M745 518h5l24 3c1 1 2 1 4 1h1v-4c2 0 5 1 7 2h4c2 4 1 30 1 36-4 0-6 1-10 2 0 1-1 2-1 3v1c-1 2-1 3-1 4-1-1-1 0 0-2-3-1-6-2-9-2l-8-2c-5-1-11 0-16 0-4 0-9 3-12 6l-1 2-1-1v-30c0-4-1-10 0-14 1-1 0-1 2-1l1 1h3c2-3 4-4 7-5z"></path><path d="M779 518c2 0 5 1 7 2l2 1v1h-5c-2 5 1 19-2 22l-1-1c-2-6-1-14-1-21v-4z" class="J"></path><path d="M745 518h5c3 3 6 5 6 9 1 3 0 5-2 7-2 3-5 4-8 4-2 0-4-1-6-3s-4-6-3-10c0 0 0-1 1-2 2-3 4-4 7-5z" class="T"></path><path d="M921 607h1c2-1 5 0 6 1l1 4c0-1 1-2 2-4 12 1 26 5 39 7v42l-49 5v-55z" class="B"></path><path d="M929 634h1c2 0 3 0 4 2l-1 2h-3l-1-1v-3z" class="L"></path><path d="M931 621h3l1 2c-2 2-3 3-5 3l-2 1-1-1v-2c1-2 2-2 4-3z" class="E"></path><defs><linearGradient id="Be" x1="1213.394" y1="1203.986" x2="1178.957" y2="1154.949" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#404041"></stop></linearGradient></defs><path fill="url(#Be)" d="M1162 1197h1l17-18c14-16 27-34 37-53l5 2c3 0 7 2 9 5 0 6-4 13-7 18-1 1 0 1-1 2 0 2-1 3-2 4-1-2-1-2-1-5-1 0-1 1-2 2 0 3 1 6 3 9-3 3-11 6-12 8s-1 7-1 9c-1 6-7 14-11 18h-3c0-1 0-1-1-2-3-2-6-3-10-3-3 1-5 3-7 5 0 2-1 3 0 5 0 2 2 3 3 4 4 2 7 5 11 8-6 7-11 14-17 21l-6-6h-2c-1 1-1 2 0 3v1 1c-4-4-5-9-8-13v-3l-3-3c-1-3 0-4 0-6 1-4 3-6 5-9 1-2 2-3 3-4z"></path><path d="M660 327c3-1 7-1 10 0l1 1c-1 1-1 0-2 1 1 2 2 2 4 2 12 3 24 0 36 2l-1 1-16 30c-3 0-7-1-10-1 1-3 2-5 2-8-2-2-2-3-4-3h-2c0 10-6 21-9 29l-10 23c-2-1-9-5-11-5 3 3 8 5 11 7-1 0-3-1-5-1s-5-2-7-3c-1 0-2-3-3-4-3-5-6-10-9-14 1-9 5-18 8-26 2-5 4-10 7-14 1-2 3-4 5-6 1-1 2-3 2-5 1-3 1-4 3-6z" class="J"></path><path d="M654 347c7 2 14 5 20 9l-16 39-21-10c6-13 9-26 17-38z" class="T"></path><defs><linearGradient id="Bf" x1="481.212" y1="989.416" x2="470.845" y2="934.57" xlink:href="#B"><stop offset="0" stop-color="#b7b6b6"></stop><stop offset="1" stop-color="#e2e2e1"></stop></linearGradient></defs><path fill="url(#Bf)" d="M456 935c18 0 32 16 44 27 5 4 9 7 13 11l7 8 5 8c-2 1-5 2-8 2-4 1-8 1-12 2-2 0-5 4-7 5l1 5c-3-3-6-7-8-11-4-3-7-5-12-6-9-2-18-3-28-3-1-1-2-2-4-3h-3l-6-15c10-5 14-10 16-21 0-3 0-6 2-9z"></path><defs><linearGradient id="Bg" x1="507.276" y1="994.778" x2="503.276" y2="978.322" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#Bg)" d="M491 992c-1-1-1-2-1-3l-1-1c1-1 2-3 4-5 1-1 5-2 6-3v-2c0-1 1-2 2-3 1 0 1 0 2 1l1 2h6c4 1 7 2 10 3l5 8c-2 1-5 2-8 2-4 1-8 1-12 2-2 0-5 4-7 5l1 5c-3-3-6-7-8-11z"></path><defs><linearGradient id="Bh" x1="680.048" y1="1231.847" x2="708.396" y2="1132.158" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#a5a4a4"></stop></linearGradient></defs><path fill="url(#Bh)" d="M676 1117h8c1 2 3 4 2 6 5 7 7 16 11 24l13 25c3 4 7 10 8 15 2 7 0 17 0 24l-5 6h1c6 3 8 8 12 13v1l-3 1c-4 0-6-1-10 2l2 2-1 1c-2 1-3 1-4 2-1 0-1 1-3 1 2-4 3-3 5-5 0-5-4-11-8-14h0c-5-7-8-16-13-23-3-4-8-4-13-4l10-26c1-2 2-4 4-6l-9-5-9-7h-1l-5-4-1-2c-1-2-3-2-5-3l-4-1h0l7-1-3-1 2-2-3-3c0-1 1-2 2-3s1-2 2-3l-2-1v-2c1-1 0-1 0-3h0c1 0 3-1 4-1h0c2-1 1-1 3-1h1c1-1 3-2 5-2z"></path><path d="M688 1168c1-2 2-4 4-6l3 3v4c-2 0-4 0-7-1z" class="M"></path><path d="M665 1139c3 0 6 1 8-1 1 3 1 4 1 6v6h-1l-5-4-1-2c-1-2-3-2-5-3l-4-1h0l7-1z" class="T"></path><path d="M676 1117h8c1 2 3 4 2 6l-1 1c-2 1-3 2-5 2l-3 1c-2 0-4 1-6 1-1 1-3 2-4 2l-2 1-2-1c1-1 1-2 2-3l-2-1v-2c1-1 0-1 0-3h0c1 0 3-1 4-1h0c2-1 1-1 3-1h1c1-1 3-2 5-2z" class="L"></path><path d="M676 1117c1 0 2 0 3 2-1 0-2 1-3 1h-5-1c-1 1-1 1-3 1-1 1-3 2-4 3 1-1 0-1 0-3h0c1 0 3-1 4-1h0c2-1 1-1 3-1h1c1-1 3-2 5-2z" class="I"></path><path d="M676 1117h8c1 2 3 4 2 6l-1 1c-2-1-3-1-5-1-1-1-2-1-3-2l-1-1c1 0 2-1 3-1-1-2-2-2-3-2z" class="Y"></path><path d="M618 375v-2c6 6 13 12 18 19 16 21 19 49 19 75l-2 22-3 2c-1 2-1 4-3 5h-3c1 2 2 1 3 1v5l1 1c-1 2 0 3-1 5-1-1-2-2-4-2l-3-1 3-1c-1-1-2-1-3-2h-1c-1-1-1-2-1-3-2-1-3-2-4-3v-1c0-1 0-1-1-2 0-1 0-3 1-4 1 0 3 0 4-1h0c-3-2-6-2-10-2 1-10 1-21 0-31l-1-4-1 7-1-23c0-6-4-39-7-42-2-1-6-2-8-2l-2-4a30.44 30.44 0 0 0 8-8v-1l1 1 1-4z" class="C"></path><path d="M618 375l8 53 2 26v1l-1-4-1 7-1-23c0-6-4-39-7-42-2-1-6-2-8-2l-2-4a30.44 30.44 0 0 0 8-8v-1l1 1 1-4z" class="I"></path><path d="M924 591l-3-1c-1-5 0-10 0-15v-32-53c0-14-1-29 2-42v-2c5-21 22-42 41-53 12-8 26-14 40-16 2 1 2 2 3 5-2 1-5 1-7 2-5 1-10 3-15 5-30 11-57 40-59 73v55c0 10 0 19-1 29v3c5 4 10 7 15 10l22 11c1 1 7 3 8 4 1 2 0 12 0 14l-19-5c-1 1-1 2 0 4 1-1 1-2 2-2 1-1 14 3 16 4 0 0 0 1 1 1v14l-1 1-10-2-1-1h-1c-2-1-1-1-3-1-2-1-2-1-3-1-7 0-12-1-18-2l-11-4c1-1 1-2 2-3z" class="B"></path><path d="M924 591c5 0 14 3 18 5 4 1 5 4 9 4-7 0-12-1-18-2l-11-4c1-1 1-2 2-3z" class="O"></path><path d="M842 489l4-1h17c7 0 17-1 23 5 2 2 3 4 3 8 0 10-1 22-1 33l-1 88-4 259-1-317v-42c0-6 1-13 0-19-1-1-1-3-2-4-2-2-5-3-8-3-8-1-27-2-33 4-2 1-2 4-3 6-1 24 1 49 1 73l1 146v85l-6-234-1-57c0-6-1-16 1-21 1-5 6-7 10-9zm318 792l1 1c1 9 1 18 2 27v34c0 6 2 18-1 23l-1 1-4 3c-4 3-11 4-15 3-11-1-22-12-29-21v-9-29l19-12c3 5 6 11 9 16 1 2 2 5 4 7 2 1 3 1 4 1 2-1 4-1 5-3-1-9-9-20-13-27l19-15z" class="C"></path><path d="M867 153h33 9c0-1 1-2 1-2 1 1 1 2 1 4l-1 1c-1 4 0 10 0 14v36c-18 1-34 5-51 10l-11-34v-1c2-3 2-5 1-8 0-2-2-4-3-5v-2c3-2 7-1 10-2h2l6-3c0-3 1-5 3-7v-1z" class="B"></path><path d="M420 247l40 7c3 2 5 5 8 6 6 3 15 4 22 5l35 8-15 21-8 13c-2 1-14-1-17-2-6-3-11-9-17-12-14-10-31-12-48-15v-31z" class="C"></path><path d="M781 943v-22l12-1 1 2 2-2 3-1c2 2 0 3 3 3l1-2c3 0 5 1 8 3h0c1-1 1-2 1-3 1 2 1 5 1 8 1 7 0 17 2 24v7h0c1 5 0 10 2 15v-8l1-2 1 1c1 7 2 14 1 21l-2 16-4 2c1 1 2 2 3 2 0 1 1 1 1 1 1 0 1 5 1 6-2 0-3 0-4 1l-1-1-1 2h-1c-1-1-1-1-2-1v6l2 1h-29v-25l-1-46c0-3-1-5-1-7z" class="B"></path><path d="M800 1020v-27c1 3 2 6 1 8-1 5 0 11 0 16h0v-8l1 1 1 9h2l2-1h1c0-3 1-6-1-8l1-1c1-1 1-2 2-3v-1c1-1 2-1 3-2l1 1c1 1 2 2 3 2 0 1 1 1 1 1 1 0 1 5 1 6-2 0-3 0-4 1l-1-1-1 2h-1c-1-1-1-1-2-1v6h-10z" class="H"></path><path d="M782 950c1 5 2 11 2 17 0 2 0 5 2 7 1-2 0-7 1-10 0 3 2 7 2 10 1 7 0 14 0 21l2 25h9 10l2 1h-29v-25l-1-46z" class="L"></path><path d="M807 1010v-2c1-1 1-2 0-4v-1c1-1 1-3 1-5-2-2-1-5-1-8 1-2 1-10 1-13h-4l-1-2h1l4-1c5-1 1-10 3-13 0-2 2-2 4-2h0c1 5 0 10 2 15v-8l1-2 1 1c1 7 2 14 1 21l-2 16-4 2-1-1c-1 1-2 1-3 2v1c-1 1-1 2-2 3l-1 1z" class="N"></path><path d="M1200 534h3v5l1 4 5 37c-4 0-8 0-12 1h-19-6c-2-1-2-1-3-1l3-1c7-2 26 1 31-4v-1l-50 2-47 2-1-13c-1-3-2-6-1-9l5-1 45-8c5-1 9-1 14-2-4-1-9 1-12 1-18 3-35 7-53 8l-1-2c4-1 8-2 12-2l21-4 65-12z" class="C"></path><path d="M1153 576l-47 2-1-13c-1-3-2-6-1-9l5-1-1 2c7 4 17 2 25 3 5 0 10 1 15 2-1 1-2 0-4 0l-5-1h-7c-1-1-3-1-5-1 0 3 1 3 2 5s0 3 0 5c0 1 1 2 1 3v3h1 11c1 0 1 0 2-1 2-1 4 0 6 1h3z" class="H"></path><path d="M665 698v15l-10-1h-17v-26l19-1c-6-2-13-1-19-1v-54l22-2c0 3 1 12 1 14-1 1-2 1-2 3l2 1 1-1c-1-4-1-9 0-13 0-4 1-8 0-12l-1-1c-2 2-2 4-2 6-3 3-17 2-21 2v-63l19 3c2 0 7 0 9 2v5c-1 2-9 1-11 2l-1 1c3 2 8 1 11 1v26l-1-1c-1 0-2-1-3-2-2-1-5-1-7 0-2 0-3 2-5 5 0 3 0 5 3 8 1 1 3 2 4 2 4 0 6-2 8-4v-1l1 38v49z" class="B"></path><path d="M665 649v49c-1 0-5-2-5-3 0-3 2-7 2-11 1-6-2-17 1-22 1-3 1-9 2-13z" class="N"></path><defs><linearGradient id="Bi" x1="1297.682" y1="650.154" x2="1289.577" y2="603.615" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#6e6e6d"></stop></linearGradient></defs><path fill="url(#Bi)" d="M1306 601l21 1 5 1c2-1 2-1 3 0l2 4c-1 3-1 6 0 8 0 4 1 8 1 11l1 15-1 2h-5-1c-8 2-19 1-27 5 0 1-1 2-1 3l-21 1-1 3h-1-1c-4 0-9 0-13-1v1h-9l-2 1h0c-1-3-2-6-2-9 1-7 4-13 8-19 0-2-1-3 0-5 0-2 0-3-1-4-3-1-5-6-7-9l-2-2c8 0 15 0 22-1 7 0 17 1 23-1v-4h1c2-1 6-1 8-1z"></path><path d="M1258 655c0-1 1-2 1-3 3 0 6 0 9 1l-1 1v1h-9z" class="R"></path><path d="M1281 653l1-1c-2-4-1-12 0-17 1 6 2 11 1 17l-1 3h-1v-2z" class="G"></path><path d="M1268 653c3-2 9-1 12-1l1 1v2h-1c-4 0-9 0-13-1l1-1z" class="E"></path><path d="M1337 607c-1 3-1 6 0 8l-2 1-3 1c-2-1-2-1-3-2 0-2-1-6 1-8h7z" class="U"></path><path d="M1332 617l3-1c0 2 0 4 1 5l-2 1c-6 0-15 1-21-1 3-6 13-1 18-4h1z" class="L"></path><path d="M1254 610l4-2c1 2 0 1 1 3 2 0 4 0 5 1 3 0 3 1 5 3-1 2-1 3-3 5-1 0-2 1-3 1-1 1-1 2 0 4v1 1c-1 1-1 0-1 1 0-2-1-3 0-5 0-2 0-3-1-4-3-1-5-6-7-9z" class="U"></path><path d="M1254 610l4-2c1 2 0 1 1 3 2 0 4 0 5 1h-4v4l2 1-1 2c-3-1-5-6-7-9z" class="G"></path><path d="M1333 643c1-4 0-6-1-9l-2-3c0-2 0-3 1-5h7l1 15-1 2h-5z" class="K"></path><path d="M1327 602l5 1v1h2 1v2c-7 1-15 1-22 1-3 0-7 0-10-2l1-1 23-2z" class="E"></path><path d="M1266 860c9 0 76-1 79 2 1 5 1 11 0 16l-2 29c-4 1-10 0-14 0l-1-11c-1 4-1 8-1 11l-35 1v-17c1-3 1-7 1-10-1-2-2-3-3-4l1-2 39-2c3 0 9 0 12-2l-114 1-3 10-1 2c-5 1-11 0-16 0h-9c-2 0-4 0-6 2-3 0-7-1-10-1-2-1-3-1-4 0-1-2-1-3-2-4l-14-1-1-1 3-2c0-2 0-3-1-5l-3-1-43-1c-4 0-7-1-10-2 11-2 24 0 36 0h57 13 3l1-1 1-2c4 3 117 3 123 1v-2c-3-2-8-1-12-1h0c-9-2-19-1-28-1h-59v-1c7 0 16 1 22 0l1-1z" class="B"></path><path d="M1332 883c2 0 3 0 5 1 0 3 0 6-1 9h-4l-1-1c0-3 0-6 1-9z" class="T"></path><path d="M1166 871l53 1h9l-3 10-1 2c-5 1-11 0-16 0h-9c-2 0-4 0-6 2-3 0-7-1-10-1-2-1-3-1-4 0-1-2-1-3-2-4l-14-1-1-1 3-2c0-2 0-3-1-5l-3-1h5z" class="L"></path><path d="M1219 872h9l-3 10c-1 0-2 0-3-1-2-3-3-5-3-9z" class="W"></path><path d="M1161 871h5l14 1c0 2-2 8-1 11 7 0 14 0 20 1-2 0-4 0-6 2-3 0-7-1-10-1-2-1-3-1-4 0-1-2-1-3-2-4l-14-1-1-1 3-2c0-2 0-3-1-5l-3-1z" class="G"></path><path d="M912 1073c-2-7-5-14-5-21-1-13-1-26-1-39v-65-278l9-1c1 4 0 10 0 13v37c1 8 0 15 1 22v3c0 2 1 12-1 13 0 14-1 28 0 42 1 3-1 10 1 12h1 1c1 1 1 0 2 1 0 4 0 5-2 8v2c2 1 2 1 5 1-2 3-2 47-1 50l1 2-1 2-2 1-1 8-1 8h-1-2v3 1 3c-1 3 0 6 0 9-2-5-1-12-1-17h0v23c0 2 0 7-1 8v104c-2 8-1 17-1 26 0 4 3 15 0 19z" class="J"></path><defs><linearGradient id="Bj" x1="925.614" y1="867.113" x2="908.141" y2="869.852" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#Bj)" d="M915 719c1 8 0 15 1 22v3c0 2 1 12-1 13 0 14-1 28 0 42 1 3-1 10 1 12h1 1c1 1 1 0 2 1 0 4 0 5-2 8v2c2 1 2 1 5 1-2 3-2 47-1 50l1 2-1 2-2 1-1 8-1 8h-1-2v3 1 3c-1 3 0 6 0 9-2-5-1-12-1-17h0v23c0 2 0 7-1 8v-79-7-15c2-9 1-18 1-27 0-25-1-51 1-77z"></path><path d="M913 823l4 2c0 2 0 3-1 4v5l1 18h-1c0-1 1-2-1-3v8c-1-4 1-8-2-12v-7-15z" class="K"></path><defs><linearGradient id="Bk" x1="1258.344" y1="548.766" x2="1264.89" y2="471.624" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#f2f2f2"></stop></linearGradient></defs><path fill="url(#Bk)" d="M1306 473l11 34-17 1v-12c-1 4-1 8-1 13h-18c-2-4-6-10-10-11-2 0-4 0-5 2 1 7 3 11-1 17l-6 6h1c3-2 5-4 7-7h0c1 0 2-1 2-2 2-2 1-3 2-4l2-2 1 2c1 3 2 5 5 6l2 4c2 0 3-1 4-2h3l11 2 1-1 1-6c2-1 10-1 13-1 0 4-1 7 0 11 3 0 3-3 5 0-2 2-6 0-7 3 3 2 6 2 8 1 0 2 0 3-1 4-3 1-7-1-10 1h-5l-1 2c1 3 2 4 4 5-3 1-5 2-8 3l-2 2-3 2v1 1c-1 1-1 0-1 1 0 2 0 1-1 2s-4 0-6 0c-4 0-10-1-14-3-2 0-3 0-5 1h-1c0 3-1 6-1 9l-2-16c7 0 15 0 20-2-5-1-15 0-20-2v-4h-1c0 2 0 3-2 4l-36 1h-8c-1 0-1-1-2-1-2 1-8 1-11 1v-5h-3c2-1 2-1 3-1 0-4-1-8-2-12l34-1c4 0 12 1 15 0 2-1 6-4 7-5 4-4 3-15 3-21l17-11c3 0 6 1 9 1s4-3 6-4c1-1 4 0 5 0l2-1c2-1 5-4 7-6z"></path><path d="M638 713l19 1c3 1 6 0 8 2 0 2 1 4-1 6h-8l1 2h8v94 28 17c-3 0-5-1-8-1l-19-1v-43c5 0 14 1 18-1-4-2-14-1-18-1V713zm428 512c-2 1-1 1-2 1l-19-29c7-2 15-5 22-8 19-8 37-21 53-35l21-18 16 23c2 2 4 5 6 6l5-4c-1 5-5 4-2 9v1h0l-3-2c-6 1-12 7-16 11-2 2-5 4-7 4l-3-2c1 1 2 2 2 4-6 6-14 11-21 15-2 0-1 0-2-1v3c-7 5-16 10-24 14-2-1-3-1-5-3l15-11c-2 0-11 6-14 8l-4 3c-4 3-13 9-18 11z" class="B"></path><path d="M1066 1225c-1-3-3-7-4-9l-9-16 15-3 9 9c3 2 5 5 7 8-4 3-13 9-18 11z" class="F"></path><path d="M1116 1200l-4-5-1 1v1c-2 1-3 3-5 4h-1v-2-1c-1-1-2-3-3-5-2-2-4-6-3-8 7-1 10-4 15-8l-1-2c2-1 4-3 6-4 1-1 2-2 4-3l1-1 1-1 2 2 3 3c1 2-1 1 1 2 1 1 2 2 3 4h0c1 2 2 3 3 5 1 1 2 2 2 4-6 6-14 11-21 15-2 0-1 0-2-1z" class="J"></path><defs><linearGradient id="Bl" x1="978.671" y1="779.536" x2="826.873" y2="753.973" xlink:href="#B"><stop offset="0" stop-color="#89888b"></stop><stop offset="1" stop-color="#c0bfbe"></stop></linearGradient></defs><path fill="url(#Bl)" d="M907 426c-2 9-5 18-5 28l-1 43v98l1 222v159c0 26-2 53 0 79 2 15 8 29 16 43 10 18 29 35 47 45-2 1-6-1-8-3-5-3-11-8-16-12l-9-9c-1 0-3-3-4-3h-1c-4-3-7-7-9-10h-3s-2-3-3-3c-7-12-13-27-15-41-1-13-1-27-1-41l1-70-1-311V538c0-15 1-31 0-45 0-3 0-5-2-7-5-5-17-4-23-4 5-9 11-21 14-31v-1-1h2c4-6 6-15 9-22 3 1 5 2 8 3 1-2 2-3 3-4z"></path><path d="M913 375c8-1 17-1 23-7-4-3-14-4-18-4l-14-3c4-1 7-1 10-2 5-1 9-3 13-4 12-1 23-1 35-3-14-4-30-6-44-11l43-1c13 0 26 1 39 0-15-4-31-6-45-12 57 1 121 12 163 54 0 1 1 2 2 3-34-24-78-40-120-32-31 5-62 21-80 47-5 8-10 17-13 26-1 1-2 2-3 4-3-1-5-2-8-3-3 7-5 16-9 22h-2c0-6 3-13 5-18 2-3 4-6 5-10 3-6 7-13 7-20 5-2 7-5 10-10l-30-4c6 0 10 0 16-5-4 1-7 3-11 3h-5-1c1-2 1-1 1-3-2 0-3 0-5-1 2-1 4-1 6-1 3-1 5 0 8 0v1c-2 0-4 0-6 2h2c4 1 6-1 9-3 2 1 8 1 11 0s4-2 6-5zm-59-152l2-1c2 3 3 7 4 10l6 19c-24 11-47 21-68 37h-1l9-7-1-1c-22 14-40 34-56 54l-4 5c-6-7-13-13-20-18 31-45 78-80 129-98z" class="B"></path><path d="M753 304c3 0 6-1 9 1 2 1 3 5 3 7-1 3-3 5-5 7-2 1-2 1-4 1-3-1-6-3-7-6-1-2-1-4 0-7 1-2 2-2 4-3zm82-62c2 0 4-1 5 0 2 1 4 2 5 4s1 4 0 7c-1 2-3 3-5 5h-5c-2-1-3-2-4-4-2-2-2-5-1-8 2-2 3-3 5-4z" class="T"></path><path d="M696 572h25c5 0 10-1 14 0 3 0 6 2 9 4 1 2 3 4 4 7 2 8 1 18 1 26l-1 66c1 3 0 8 2 10 3 4 11 5 15 6s8 3 11 6c4 4 7 10 8 15s1 11 1 16v23l-1 87-8 1-1-83v-24c0-5 1-11-1-16-2-4-5-8-8-11-3-2-7-2-10-3-10-3-16-8-18-19-1-9 0-18 0-26v-46c0-7 1-21-2-26-1-2-4-3-6-4-6-1-13 0-19 0-8 0-22-2-29 4 0 1-1 2-1 3-1 6 0 13 0 19v47 343c-2-23-1-46-1-69l-2-142-2-122-1-48c0-10-1-20 0-30 0-2 1-5 2-7 5-5 12-7 19-7z" class="B"></path><defs><linearGradient id="Bm" x1="846.44" y1="1347.421" x2="833.628" y2="1274.533" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2b2b"></stop></linearGradient></defs><path fill="url(#Bm)" d="M857 1305h2c2-11 2-23 3-34v-6h1c3 0 4 1 6 2l3 1 3 1h1l2 1 1 1 9 3c2 2 2 4 2 7l-4 25c-2 1-4 0-5 0-1-2-1-3-1-5-1 1 0 3-1 4-1 2-9 0-10 1 0 1 0 2-1 2 1 1 0 2 1 3 2 1 4 1 5 1s2 1 3 1h6v-1c-2 0-5 1-7-1-3 0-4 0-6-1-1-2-1-2 0-3 2 0 7 1 9-1l3 1c2 1 3 0 5-1v-1-2c1-2 0-3 1-5v-2l1-4c1-2 0-1 1-2v34h1v-43c4 12 1 28 1 40 0 4 1 8 1 12l-1 1c-1 3-2 7-5 8-4 2-10 0-14 0-12-1-26-3-38-1-2-1-2-2-4-3-15-4-30-6-43-12 0-4 0-8 1-12 1-2 1-1 1-3h0l1-1c0-2 0-1-1-2l-1-1c1-7 0-15 0-23l1 7c9 2 17 2 25 3 6 0 11-1 17 0 3 1 5 3 8 5 2 2 5 3 7 4l8 3 2-1z"></path><path d="M850 1323h2l1 1-2 2c-2 0-1 0-2-1l1-2zm5-17v2c-3 2-6 3-9 3-4 1-9 2-12 0-2-1-3-2-5-3-3 0-6-1-9-2-1-2-1-2-1-4l1-1c8-1 18 2 27 2l8 3z" class="T"></path><path d="M1160 483c0-3-2-7-4-10-10-24-26-41-44-59-6-6-13-10-21-15-23-13-46-18-73-19-2-6-3-15-5-22h7c36 0 77 12 103 38-3 3-10 8-12 13l1 1c5-1 10-2 14-4 7 5 12 12 17 19l2 3c2 2 4 5 6 5v1c3 0 5 2 8 4l-1-3c9 11 20 26 24 40l-2 3 1 1h0c2-1 1-1 2-1 4 5 17 41 16 47h0c-3 0-5-1-7-2l-1 1v2c-11 3-23 4-34 7v-1h3c1-1 0-1 2-1h1l9-2 1-2c-3-15-6-31-13-44z" class="C"></path><path d="M1143 425l2 3c-1 2-2 2-4 3l-2 1c-2-1-3-2-4-3v-1c3-3 4-3 8-3z" class="B"></path><path d="M1145 428c2 2 4 5 6 5v1c-3 1-6 4-8 6-1-1-1-2-1-4 1-1 1-2 1-4l-3 1-1-1 2-1c2-1 3-1 4-3z" class="N"></path><path d="M1151 434c3 0 5 2 8 4 0 2 0 2-1 4-2 3-6 7-10 7h-1c-2-3-2-7-4-9 2-2 5-5 8-6z" class="T"></path><path d="M1227 446c-11-21-22-42-36-62-14-19-30-35-47-52l21-29 15-21c2-3 4-7 6-8l4 3c-1 2-1 2-3 2v2h1l1 1c-1 3-4 4-4 7l1 2 3-1 1 1c-1 5-10 10-11 15l2 1c3-2 6-6 8-9 3-4 6-8 10-11 5 7 12 13 17 20-3 2-5 4-8 6-2-1-3-2-4-4v1l4 6-1 2 2 2c2-1 4-3 6-5s4-3 7-4c-2 3-7 7-7 10 2 1 3 1 6 1 1-1 1-1 2-1 1 6 4 16 2 23-2 1-6 4-7 6v1c2 2 3 2 6 2 0 2 1 4 0 5-3 6-14 7-18 15 0 1 1 3 2 5l-5 6c3 10 8 19 13 28l14 28v1c1 2 1 3 2 4-1 1-2 2-2 3l1 2v2c1 3 3 4 5 6-1 1-2 1-3 2-1-2-1-3-2-6 0-2-2-5-3-8h-1zm-472-47c24-55 64-96 121-119 56-22 120-21 176 4 26 11 49 27 70 46 39 34 70 77 89 126l6 18c-1 2-2 3-4 4-1 0-1 0-2-1s-2-6-2-8l-9-22c-4-9-9-17-13-25-18-31-42-56-68-81 0-2 1-6 1-8-2 2-2 5-3 8-9-7-18-15-28-21-3-2-7-5-10-6 9 7 19 14 28 22 19 16 36 36 50 57 3 4 8 10 9 15-1 1-1 1-3 1-10-13-17-26-27-38-39-46-93-78-153-86-18-3-35-2-53-1l2-6 27-5c-9-1-18-1-27 0-2 0-10 2-12 1l-1-1v1c-4 3-13 3-18 5-8 2-15 4-22 6-56 20-98 62-122 115l-2-1zm57-228c2-2 5-2 9-2 3-1 10-2 14-1-2 3-4 6-3 10 1 3 2 4 5 6h8l11 33-23 9-22 11c-1 1-4 2-5 2l-12 8-17 11c-23 18-41 38-59 61h-49c-5-3-8-7-11-11l17-19c2 1 5 3 8 3l-7-4 12-11c5 3 11 6 17 8 12 3 24 1 34-5s17-15 20-26c3-14-1-26-8-37l-1-1v-1l6-3c2 1 3 0 4-1 5-1 12-4 16-8 1 0 3-1 3-1 1 0 8 8 10 9 3 2 5 2 8 3 6 0 14-2 18-6 3-2 5-4 8-5 0-5 1-10 0-14-1-7-5-14-11-18z" class="B"></path><path d="M760 211c1 4 5 4 3 8l-1 1c-3-2-5-3-8-3h-1-2l-1-1v-1l6-3c2 1 3 0 4-1z" class="J"></path><path d="M811 237c0-1 1-2 2-3h1-4l-2-2 2-2c3 0 7 0 10-2 5-2 6-5 12-4 0 1 1 1 1 2l-22 11z" class="E"></path><path d="M459 408v-2c12 0 24 3 36 4 4 1 8 2 12 1-9-4-19-5-28-9 0-3 1-7 2-11l-5 11c-9-1-19-4-28-3-3-23-9-47-18-68l67 12 8 24-26-5c-5-2-11-3-16-5l-1 1c3 2 8 3 11 4l24 6c2 0 7 1 9 2v3l4 17c4 21 6 44 4 66l-3 34h-2c0-4 2-15 0-18l-1 2c-6 6-14 12-23 12-7 0-13-3-18-7-2-4-3-7-3-11-1-3-1-6-1-8l-1-1-6 3v-10c0-10 3-22 1-33l-5-1 4-2c2-1 1-9 1-11 2 2 1 4 1 6l1-3z" class="C"></path><path d="M456 452v-1l1-1c2 1 4 1 6 1l-1-1 3-1c-1 7-1 13-1 19-1-3-1-6-1-8l-1-1-6 3v-10z" class="S"></path><defs><linearGradient id="Bn" x1="455.816" y1="449.403" x2="468.709" y2="420.547" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#Bn)" d="M457 405c2 2 1 4 1 6l1-3c1 1 1 2 1 5l1-1c2-1 5 0 7 0l2 2-2 6 1 1c4 1 7 1 9 4 0 4-6 9-8 13-2 3-4 8-5 11l-3 1 1 1c-2 0-4 0-6-1l-1 1v1c0-10 3-22 1-33l-5-1 4-2c2-1 1-9 1-11z"></path><path d="M488 430c3 0 6 0 9 1 4 2 7 5 9 9 3 8 2 18-1 26s-8 12-16 16c-3 0-7 0-11-1-4-3-6-7-8-11-2-8-1-21 3-28 3-7 8-10 15-12z" class="T"></path><path d="M367 811c-2-33-8-69-27-97-3-4-6-9-10-12l-2-2v-1c7 1 15 1 21 3l2 3v-2l2 1v2l1 1 2-1c-1-2-1-2-2-2 3-1 6-1 9 0 1 1 1 1 1 3 2-1 2-2 3-2h1c12 2 23 2 35 3l18 3c4 0 13 0 16 2s10 10 10 14l-2 2c2 1 2 1 4 1 3 3 5 8 7 12 12 25 15 56 14 83l-4-35c-1-5-1-10-3-15l-1-4c2 19 3 39 1 58h-1l-4-1v-3c1-2 1-5 1-8h0c0-2 1-10 0-12 0-1-1-1-1-2-4 0-7-1-9 1-1-1-1 0-1-1v-1 40c0 5 1 12-1 18-1-35 0-67-10-100v-3l-2-4c0-1 0-1-1-2v-1l-1-3c-1-2-1-4-2-5l-3-6-6-11-1-1-2-3-2-1c-1-3 1 0-1-3-1 0-1-1-2-2v-1c-2 0-1 0-3 1 2 3 3 5 5 7v2c-1 2 0 6 0 9-1 0-2 1-3 0h-7l-3 2h0c3 3 4 6 4 10v1c-8-2-29-2-34-9l-5 5 25 4c4 1 11 2 15 4 0 3 3 16 6 18h1c2 4 3 8 4 12l-42-9c1-3 2-12 0-15v14h-2c-1 2-2 3-1 6 0 1 0 1 1 2v-4c2-1 4-1 6 0 9 0 18 2 26 5 3 1 5 2 8 3l-2 1c-4 0-7 2-10 4l-5 1c-1 2 1 7-1 9-2 0-2 0-3-1h-3l-3-2c-2 0-4-1-6-2h-1c-1 1-1 2 0 4l2 18c1 9 1 18 1 27l-2 22h-2c-3-1-4-1-7-1h-3l-1-2c2-9 2-19 1-29 0-2 1-7 0-9l-2-2c0-2 0-6-1-9z" class="C"></path><path d="M400 757h3c2 1 4 3 4 6v1l1 1-1 3c-1-1 0-1-1-2h-1c-1-1-2-1-3-2-1 0-1-1-2-2-1-2-1-3 0-5z" class="B"></path><path d="M373 862l2-34h0l1 1c0 1 0 1 1 2 2-3 1-11 1-15 3 15 2 32 2 47-3-1-4-1-7-1z" class="K"></path><path d="M403 735c-7 1-17 2-24-1-1 0-1-1-1-2 2-4 14-9 19-12l-6-6 7 3 5-6 8 4c2 3 3 5 5 7v2c-1 2 0 6 0 9-1 0-2 1-3 0h-7l-3 2h0z" class="T"></path><path d="M416 722v2c-1 2 0 6 0 9-1 0-2 1-3 0h-7c2-2 4-4 5-7 0-1 1-2 2-4l1 1 2-1z" class="F"></path><defs><linearGradient id="Bo" x1="497.865" y1="1101.047" x2="434.7" y2="1010.565" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#Bo)" d="M499 1003l-1-5c2-1 5-5 7-5 4-1 8-1 12-2 3 0 6-1 8-2 3 7 7 14 9 21l5 1-3 3 2 19c3 7 1 14-2 21l-3 5c-7 15-18 29-30 40-9 8-17 12-28 16-4 1-9 2-12 4-2 1-2 1-4 1v1c0 2 0 3-2 4l-1 1c-1-1-3 0-5 0-4 0-8 0-13-1-5 0-11-3-16-5-26-14-39-43-47-70 3-1 6-1 9-1l1-1-1-1 19-1c3 0 7 0 10-1 0 6 2 12 5 17 4 10 13 17 23 21 11 3 25 3 35-3h1c6-4 14-12 18-18 8-12 9-29 6-43 2-1 4-2 5-4 0-3-1-5-2-7l-5-5z"></path><path d="M438 1125c4 0 9 1 12 0 1-1 1-1 1-2h-2c-1 0-2 0-3-1l-1-1h-1v-1c2 0 4-1 6 0s6 1 9 1c0 2 0 3-2 4l-1 1c-1-1-3 0-5 0-4 0-8 0-13-1z" class="O"></path><path d="M402 1065c2-1 4-1 6 0s4 2 5 4 2 4 1 7-4 5-7 7h-6c-2 0-4-1-5-3s-2-4-2-6c1-4 4-7 8-9z" class="T"></path><path d="M400 1075h4l2 3 4-3 1 1-1 2-1 1c-2 2-3 2-6 2-2-1-3-2-4-3l1-3z" class="G"></path><defs><linearGradient id="Bp" x1="516.484" y1="1012.438" x2="511.43" y2="991.303" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#999"></stop></linearGradient></defs><path fill="url(#Bp)" d="M499 1003l-1-5c2-1 5-5 7-5 4-1 8-1 12-2 3 0 6-1 8-2 3 7 7 14 9 21l5 1-3 3c-2 0-2-1-4-1l-13 4c-3 1-6 1-8 1-1-1-1 0-1-1-1-1-1-1-1-2-1-2-2-4-2-6-1-1 0-4 0-6-1-1-1-3-2-4-2 1-2 2-3 3l2 2v4l-5-5z"></path><path d="M520 1011c5-2 9-1 14-1l5 1-3 3c-2 0-2-1-4-1-3-1-5-1-8 0h-1l-2-2h-1z" class="Q"></path><path d="M520 1011h1l2 2h1c3-1 5-1 8 0l-13 4c-3 1-6 1-8 1-1-1-1 0-1-1 2-1 5-1 7-3l1-2 2-1zm-13 42v-3c0-3 1-4 3-6 2-1 3-1 6-1l1 3c1 2 2 4 5 5 1-4-4-18-4-22 8-1 13-1 20 4 3 7 1 14-2 21l-3 5c-4 3-8 5-13 4-7-2-10-4-13-10z" class="T"></path><path d="M503 1099c-4-4-8-9-11-14-1-1-4-3-4-4 6-4 13-12 16-19v-1l1-3c0-2 0-3 2-5 3 6 6 8 13 10 5 1 9-1 13-4-7 15-18 29-30 40z" class="K"></path><path d="M980 854l121 4c1 31-1 63-5 94-5 29-11 59-27 85-2 4-5 8-8 11-9 10-20 17-33 18-10 0-20-4-27-11-10-9-16-23-19-37s-2-28-2-42v-82c0-3 0-5-3-7l1-1c0-1 0-2 1-3 2-3 1-24 1-29z" class="C"></path><path d="M914 177c-1-8 0-17 0-24 9 0 19-1 28 0 14 2 29 4 43 8 60 12 115 41 162 79 9 7 20 15 28 23l-42 61c-16-13-32-25-50-34-46-26-97-37-150-30-63 7-120 37-160 87-18 24-32 52-41 80l-6 22-1 1c-1 1-1 2-1 3l-1 4c-1 1 0 2 0 4-1 1-1 0-1 2 0 1-1 2-1 3v3c-1 1-1 1-1 2 0 2 0 4-1 5-2-1-5-1-7-2 0-2 1-4 1-6 0-6 2-10 2-16 3-23 12-46 21-67 26-57 73-105 131-127 1 1 2 2 3 2h2l1-3c2-3 11-4 14-5 24-5 49-8 74-7 9 0 21 3 31 1-27-5-53-4-80-3 1-1 2-3 2-5 1-2 0-6 0-8v-20c32-1 63 0 94 6l31 7c2 0 2 0 4-1 0-2 0-3-1-5-25-5-50-10-76-12-18-1-35 0-52 0v-28z" class="J"></path><path d="M1142 259l2 2c-1 4-4 7-7 11h-2l-1-2c2-4 6-7 8-11z" class="Q"></path><path d="M1141 258l1 1c-2 4-6 7-8 11-1-2-2-4-3-7l1-1c3-2 5-3 9-4z" class="D"></path><defs><linearGradient id="Bq" x1="1096.318" y1="239.942" x2="1102.779" y2="231.001" xlink:href="#B"><stop offset="0" stop-color="#0a0b0b"></stop><stop offset="1" stop-color="#2f2d2f"></stop></linearGradient></defs><path fill="url(#Bq)" d="M1132 262h1c0-1 1-4 0-4 0-3-7-6-9-7l-65-36c-4-2-16-6-18-9l1-1c27 9 52 23 76 37 8 5 17 9 23 16-4 1-6 2-9 4z"></path><path d="M917 193c6-1 12 0 18 0 19 0 39 1 58 4l18 3c3 0 7 1 10 1h1v3c-1 1-1 1-2 1-5 0-11-1-16-2l-30-3c-19-2-38-1-57-3-1-2 0-2 0-4zm-3-16c-1-8 0-17 0-24 9 0 19-1 28 0 14 2 29 4 43 8l-3 2 18 4c6 1 12 2 18 5l-1 11h1c7 2 14 5 21 7 31 11 61 27 89 44 9 6 23 13 29 22v2l-1 2h-3c-1 0-4-4-5-5-7-7-15-11-23-17l-48-26c-21-11-44-22-68-25v-12c-15-5-35-8-51-7l-3 17c-5 1-11 2-16 2-7 0-15-1-22-2l-1-2c-1 0-1 0-2-1v-5z" class="T"></path><defs><linearGradient id="Br" x1="942" y1="167.646" x2="973.74" y2="165.805" xlink:href="#B"><stop offset="0" stop-color="#c4c3c3"></stop><stop offset="1" stop-color="#e7e6e8"></stop></linearGradient></defs><path fill="url(#Br)" d="M942 153c14 2 29 4 43 8l-3 2-24-4c-3 0-6-1-9 0-3 5-3 19-3 25h-3c-2-4-1-8-1-12v-19z"></path><path d="M914 177c-1-8 0-17 0-24 9 0 19-1 28 0v19c0 4-1 8 1 12-6 1-13 1-19 0-3 0-5-1-8-1-1 0-1 0-2-1v-5z" class="C"></path><path d="M924 163c2-1 4-1 6-1 2 1 4 2 6 4v8c-2 2-2 3-5 4h-6c-2-1-3-2-4-3-2-3-2-5-1-7 1-3 2-4 4-5z" class="T"></path></svg>