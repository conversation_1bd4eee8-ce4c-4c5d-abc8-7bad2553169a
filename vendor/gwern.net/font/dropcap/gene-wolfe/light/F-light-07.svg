<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="110 32 854 919"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#d8d7d6}.C{fill:#adacab}.D{fill:#e9e8e7}.E{fill:#f4f3f3}.F{fill:#dbd9d9}.G{fill:#aaa9a8}.H{fill:#b3b2b1}.I{fill:#d2d1d0}.J{fill:#8b8a89}.K{fill:#c5c4c3}.L{fill:#eeedec}.M{fill:#626261}.N{fill:#979696}.O{fill:#7d7c7c}.P{fill:#201f1e}.Q{fill:#a4a4a2}.R{fill:#151414}.S{fill:#727271}.T{fill:#fdfdfc}.U{fill:#545352}</style><path d="M414 103c2 1 5 3 7 4l-2 2c-2-2-4-3-6-5l1-1z" class="M"></path><path d="M516 136l3-7 1 3-2 7c-1-1-1-2-2-3h0zm-89-23c4 3 8 4 13 5-2 1-4 1-6 1h-1c-3-1-5-2-7-4l1-2z" class="P"></path><path d="M519 129c2-5 6-9 11-12-4 5-7 9-10 15l-1-3z" class="R"></path><path d="M217 252c-2-1-3-2-5-3-2-3-2-5-2-8 0-1 0-1 1-2 1 3 1 4 3 6v1c0 2 2 3 4 5h0l-1 1zm594-5c3-3 4-5 5-8v1c1 3 1 5-1 8-2 2-4 3-6 5v-2h0l3-3-1-1z" class="P"></path><path d="M412 105c2 3 6 7 6 11 1 2 1 3 1 5v1c-2-5-5-11-9-16l2-1z" class="R"></path><path d="M519 220s0 1 1 2c-1 2 0 0-1 1-1 2 1 5 0 7 0 1-1 1 0 2v2 2c1 0 1 0 1-1h0v-1-1c-1-1 0-1 0-2v-1h0l1 1v1-2c0-2 0-1 1-2 0-1 0-2 1-3h0c-1 5-2 11-4 15h-1l-1-1 1-18 1-1z" class="I"></path><path d="M410 106c-3-3-6-4-11-4-1 0 0 0-1-1h1c5-2 10 0 15 2l-1 1-1 1-2 1zm202-2c4-2 10-5 14-4 1 0 1 1 1 1-4 1-9 2-12 6-3 5-5 12-8 17 1-4 3-8 3-12v-1c1-2 2-3 3-5h0-1v-2z" class="P"></path><path d="M584 119c4-2 10-2 14-4 5-3 8-8 13-11h1v2l-1 1c-4 2-6 6-9 9-4 3-9 4-14 4h0c-1-1-2-1-3-1h-1z" class="U"></path><path d="M536 399c3-3 5-6 8-9v1c-6 8-10 17-13 27l-9 27-3 15h-1l1-5c2-3 2-9 3-13l7-26 7-17z"></path><path d="M504 487v-8s1-1 2-1c-1-1-1-2-1-3h0l-1-1c0-1 0-2 1-3 0 2 2 4 3 6-1-3-2-6-2-8l1 1c0 1 0 1 1 2v1 1c0 1 0 1 1 2v1 1l1 1v1h0c-1 0-1 0-1-1l-1-1v3h0v1l1 1v1c1 1 1 1 1 2v1l1 1v1c0 1 1 1 1 2v1c0 1 0 1 1 2v1 2l1 1v2h0v2c1 1 1 3 1 5h-1c-1 1-1 1-1 2 0-2 0-5-1-7v-2h0c-1-1-1-2-1-2v-2-1l-1-1c0-3 0 1 0-2h0c-1-1-1-1-1-2h0c0-2-1-3-2-4v2c0 1-1 2 0 3 0 1 0 1 1 3v1 1l1 1h0l1 3v1h0v2c-1-2-1-3-2-5s-1-4-2-6h0l-2-5z" class="B"></path><path d="M260 99c0-10 1-23 3-33 1-4 2-8 4-11s5-5 9-6c14-2 41 19 52 26 4 3 9 6 13 9h0l-6-3c-2 0-6-3-7-3h-2c-12-10-23-19-37-25-4-2-8-3-12-3-4 1-7 3-9 6-7 11-5 27-7 39l-1 4z" class="P"></path><path d="M511 238c1-1 1-6 1-8-1-1-1-1-1-2v-2l-1-1v-2-6c-1-2-1-4-1-6-1-1 0-3 0-4-1-1-1-3-1-4v-4l-1-2v-2l-1-2v-2l-1-2v-1h0c1 0 1 1 1 2 1 1 1 1 1 2 1 1 1 1 1 2s0 1 1 2v1 1 1 1c1 0 1 1 1 2s0 1 1 2c1 0 1 1 1 2v-4-1c0-1 1-3 0-4v-1-2l-1-1 1-1-1-1v-1-2-1l-1-4-1-1c0-1 0-1 1-2 1 1 1 1 1 3v2c1 0 1 0 1 2v2c1 0 1 0 1 2h0c0 2 0 3 1 4v1 2 2l1 1v2 1h1v2c0 2 0 5-1 7-1 3-1 7-1 10l1 1c0 1 0 2-1 3-1-2-1-5-1-7 0 3 1 6 1 10v9c-1 4-1 9-2 13 0-4 0-9-1-14z" class="B"></path><path d="M513 220l-1-1v-1-4l1 1h1v-4c0-1 0-1 1-2 0-1 0-2 1-3 0 2 0 5-1 7-1 3-1 7-1 10l1 1c0 1 0 2-1 3-1-2-1-5-1-7z" class="F"></path><path d="M510 503c1 1 1 5 2 7 0 1 0 3 1 4 0 1 0 1 1 2v2-2c1-2 1-4 1-5 1-2 0-3 0-4 0-2 1-6 2-8 0-1 0-2 1-3v-2l2-7v-2c1-2 1-3 2-4v-2c0-1 0-1 1-2v-2l1-2c0-1 0-1 1-2 1 3-2 7-2 10-3 10-6 19-7 29v3c-1 3-1 6-2 10v1 1 2h-1v2h-1v-1c-1-1-1-3-1-4l-2-3c0-2 0-3-1-4l-1-3c-1-3-2-5-3-8v-2c0-1-2-3-2-4v-8-6l1 1c1 2 1 4 2 6l1-1c1 2 1 4 2 6s1 3 2 5z" class="K"></path><path d="M539 381l3-5 1 1c-5 7-8 14-11 21h0v1 1l-2 3-1 3h1l1 1-1 1 1 1c0-1 0-2 1-2 0-2 0-2 1-3 0-1 1-2 1-3l1-1 1-1-7 17-7 26-1-2h0l-2-4-2 10c-1-4 1-7 1-10s0-7 1-10c0-3 1-6 2-10l5-14h0l13-21z" class="R"></path><path d="M532 398h0v1 1l-2 3-1 3h1l1 1-1 1 1 1c0-1 0-2 1-2 0-2 0-2 1-3 0-1 1-2 1-3l1-1 1-1-7 17c-1-2 0-6-1-7l-1 1v1c-1 1-1 2-1 3-2 5-4 10-5 15v1h-1l5-18c3-5 4-10 7-14z" class="I"></path><path d="M539 381l3-5 1 1c-5 7-8 14-11 21-3 4-4 9-7 14v-2c0-1 0-2 1-3 0-2 1-3 0-5h0l13-21z" class="P"></path><path d="M526 414c0-1 0-2 1-3v-1l1-1c1 1 0 5 1 7l-7 26-1-2h0l-2-4c1-2 1-3 2-5l6-16-1-1z" class="C"></path><path d="M519 436c1-2 1-3 2-5 1 2 1 5 0 7v2h0l-2-4z" class="N"></path><defs><linearGradient id="A" x1="510.048" y1="444.168" x2="523.64" y2="470.234" xlink:href="#B"><stop offset="0" stop-color="#b0aeab"></stop><stop offset="1" stop-color="#d4d5d6"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M517 468l1 1-1 1v2 1 2 1 2l3-6 2-7c0-1 1-2 1-2l3-11c2-6 5-13 5-20 0-1 0-3 1-4l1-4c1-5 2-10 4-14l1-1c0-1 0-1 1-2l1-2v1c0 2-1 3-2 5l-2 6-1 3c-1 1-1 2-1 3s0 2-1 2v3c-1 2-1 5-2 7v3c0 1 0 2-1 4 0 1 0 3-1 5l-2 7-2 5c0 2 1 0 0 2h-1l-1 4-1 4c-1 2-1 4-2 6 0 2 0 3-1 4 0 1 0 2-1 3 0 1 1 2 0 3v4h-1l-1-3v-1l1-1v-2c-1 0-1 1-1 2l-1-1c1-2 1-2 0-4v8 9h0-1v-1c-2-2-1-3-2-4 0-2 0-2 1-3v-3c0-2 0-3-1-4 1-3 0-5 0-8h1l1 10h0c0-10 0-19 1-29v-4l1-2c1 0 1-2 1-2l2-10 2 4h0l1 2c-1 4-1 10-3 13l-1 5h1c0 1 0 2-1 4h0v3l-1 1z"></path><path d="M517 468v-3l-1-1c0-2 0-4 1-6v2c0-2 0-2 1-3v-1-1h1l-1 5h1c0 1 0 2-1 4h0v3l-1 1z" class="B"></path><path d="M516 136c-1 4-1 9-1 12 0 11 0 21 1 32 7-22 17-41 35-56-3 4-6 7-9 10l-2 2-1 2-1 1c-2 1-2 3-3 4s-2 2-2 3l-3 3v1 1l2-2v1h1l-3 3-1 2-1 2c-1 2-1 3-2 4l-1 5h0c0 1 0 2-1 3v2l-1 1c0 2 1 4 0 5 0 1 0 2-1 3v3c0 2 0 4-1 5 0 2 1 5 0 7v5c-2 2-2 3-2 5s0 5-1 6c0 2 1 4 0 6-1 1-1 1-1 2v1 1 2h0v-1c-1-3-1-6-2-9 1-2 1-5 1-7v-2h-1v-1-2l-1-1v-2-2c1-2 0-4 0-6-1-2 0-6 0-8-1-1-1-4-1-5 1-2 0-5 0-7 1-11 1-23-1-34 1 0 1 1 2 1 0 2-1 6 0 8v6c1-1 1-2 0-3v-2-4c1 0 1 0 0-1 0-1 1-3 1-4h0l1-1h0z" class="L"></path><path d="M518 139h0l2-1c-1 2-1 3-2 5l9-9c5-4 9-9 14-13 4-3 8-6 12-8 11-6 19-8 24-20h0v5c-2 10-16 14-24 18-3 1-5 3-8 5-13 9-24 21-27 37 9-19 24-34 43-42-3 2-7 4-9 7l-1 1c-18 15-28 34-35 56-1-11-1-21-1-32 0-3 0-8 1-12 1 1 1 2 2 3zm-57-30c-4-3-8-6-10-11 0-2-1-4-1-5h1c1 3 3 6 6 9s8 5 13 8c4 2 9 5 13 8 9 6 18 15 26 24 1-6-1-11-4-16-2-4-4-6-7-9 6 3 12 10 14 16v3c2 11 2 23 1 34 0 2 1 5 0 7-5-15-12-28-23-40-6-8-14-15-22-22h0c-2 0-3-1-4-3h0 0l-3-3z"></path><path d="M461 109c20 10 40 21 48 43 1 1 1 2 1 4-2-4-3-7-5-10-7-10-16-18-26-24-3-2-7-5-10-7h-1 0c-2 0-3-1-4-3h0 0l-3-3z" class="E"></path><path d="M339 319c2-3 6-9 9-11h4c16-1 36 0 49 12 16 15 20 38 21 59v93 161 91c0 24 1 48-3 71-2 12-6 23-14 31-7 8-17 12-26 13-4 6-6 13-10 19-2 4-5 8-7 11-11-4-20-10-29-18-4-3-8-6-11-10-8-8-13-18-17-28-4-8-6-17-7-26 2 3 3 7 5 10 3 4 6 9 9 13a30.44 30.44 0 0 0 8 8l6 6c2 3 3 7 4 11 3 6 7 11 12 16-4-6-8-13-10-20-3-11-1-22 2-32 1-4 2-7 4-10 0-1 1-4 2-5 0 0-1-2-2-3-1-5-3-9-6-13-4-7-9-14-15-20-2-2-5-5-8-6-2-1-5 0-7 1-1 0-1 1-2 2 0 2 1 4 1 5v1c-2 0-3-2-4-3-2-4-1-8 0-12l1-2c-32-14-61-38-74-72-3-7-5-14-6-21-1-4-1-9-2-13-1 8 1 15 2 23 2 20 5 41 12 61 3 8 7 18 13 25 3 3 7 6 12 6s8-2 12-5c-5 6-10 10-17 11-7 0-13-2-18-6-6-5-9-11-13-17l-11-18c-4-6-8-12-11-18-9-15-15-33-18-50-3-13-4-27-8-39 1 18-2 43-15 57-3 3-6 5-10 7 1-2 3-4 4-6 5-8 6-18 6-27s-2-17-4-26c-2-17-2-31 4-47 2-6 6-13 10-18 2-2 5-4 7-5-12 3-23 6-34 13-4 2-8 5-10 9-4 8-3 16-1 24-4-5-6-10-5-16 0-8 3-15 8-20l2-3h0c-4 3-9 9-10 14-1 1-1 2-2 3 2-7 4-13 8-19-7 0-14 1-20 0-11-2-22-7-28-16-3-5-5-11-4-16 1-1 1-3 2-4v1c0 5 1 10 5 14 4 5 10 7 16 7 6 1 11-1 16-5 1-2 3-4 3-6 1-2-2-7-2-10-2-5-3-10-2-16-5 1-9 1-12-1-7-2-9-7-12-13 4 4 7 8 13 9 5 0 9-2 13-5 3-3 9-10 9-14l1-5c0-5 2-8 7-11h0v-1l-1 1c-3 2-6 4-7 8l-2 4c0-3 1-7 2-10 1-4 7-7 11-9-3-8-6-15-7-23-1-5 1-10 0-14v-1h0v-2c-1-2 0-3 0-4s1-1 1-2v-3c-1 1-1 1-1 3v1c-1 1-1 2-1 3s0 1-1 3h0c-3 9-6 17-13 23l8-1c-3 2-6 3-10 2h-1l2-1h0c9-5 12-35 14-45 4-17 14-34 29-43h-1c-2 4-5 6-8 9-9 13-11 30-10 45 2 14 5 26 17 35 2 3 6 4 9 7 3 2 5 6 8 9 9 9 22 11 35 10 11-1 21-4 31-10 2-2 6-4 8-6 0-1 0-1 1-2-1 0-2 1-2 1-12 10-34 15-49 13-11-1-22-6-29-15h-1c16 4 32 4 47-5l13-8 3-3c0 2-1 2-2 4h1v1c1 0 2-1 2-2l2-2h1c5-6 9-13 13-19 4-9 8-18 14-25-6 16-12 31-22 45h1c0 1 0 1 1 1-3 4-5 7-8 10-1 1-1 1-1 2l-3 3 1 1c3-2 6-4 9-7 9-8 15-17 22-27l11-13c6-7 10-15 15-22l-1-1-14 18c-2 2-3 5-5 7 0-3 2-5 4-8v-3c1-1 2-3 2-4s-1-1-2-2c0 1-1 2-2 2l-1-1h-1v-1-1l-2 2c1-4 6-8 9-12 8-7 17-14 26-19l17-9h-2c-5 0-9 2-13 3-18 7-32 19-44 34v-2c3-6 9-11 14-16 7-6 13-12 22-16 12-7 26-9 39-13v-1h1l3-2c1-1 2-1 2-3l1-2z"></path><path d="M404 504h4v2l-2 1c0-1-1-2-2-3z" class="C"></path><path d="M390 454h3l3 2c-3 0-5 0-8-1h1c0-1 0 0 1-1z" class="N"></path><path d="M197 654l1 8h-2l-1-6c1 0 1-1 2-2z" class="H"></path><path d="M389 451l1 1v2c-1 1-1 0-1 1h-1c-2 0-3-1-5-2 1-1 3-1 5 0h0l1-2z" class="Q"></path><path d="M299 366l6-5h0c0 1-1 2-2 3h1 1l-5 4-1-2z" class="E"></path><path d="M388 445h1c2 1 3 1 5 1v1 1l-2 1-4-2v-2z" class="G"></path><path d="M405 527l3-3v6l-2 1c0-1-1-1-3-2l2-2z" class="C"></path><path d="M405 527c1 1 1 1 1 3 1 1 1 0 0 1 0-1-1-1-3-2l2-2z" class="G"></path><path d="M405 451c1-1 2-2 3-2v4c-2 1-3 1-4 2-1-1-1-1-2-1h0v-1c1 0 2 0 3-1v-1z" class="M"></path><path d="M201 675l1 6c0 1-1 2-2 4l-2-6c2-1 2-2 3-4z" class="C"></path><path d="M312 563c6 0 11 0 16 1v1h0-1c-1 1-3 0-4 0h0c-4-2-8 0-11-2z" class="D"></path><path d="M275 404c-2 1-6 4-8 6 2-4 4-7 7-9 1 0 1 0 1 1v1h1l-1 1z" class="B"></path><path d="M133 543c1-1 1-1 2-1 2 0 3-1 5-2 0 1 1 1 2 1-3 2-7 4-11 5l2-3h0z" class="M"></path><path d="M142 468c2 1 3 2 5 3-3 1-6 3-8 4l3-7z" class="H"></path><path d="M110 535c-5 1-11-1-16-4 6 1 11 2 17 2h-1c-1 1-3 1-4 1h2c1 1 2 1 2 1z" class="B"></path><path d="M343 468c4 2 7 8 11 12-5-4-9-6-13-9 1-1 2-1 3-1-1-1-1-1-1-2z" class="I"></path><path d="M382 448c3 1 5 3 7 3l-1 2h0c-2-1-4-1-5 0-2 0-3-1-5-2 2-1 3-1 4-3z" class="K"></path><path d="M402 524c2-2 4-4 6-4 0 3-3 5-4 7h-1-1l-6 7c1-3 5-7 7-9l-1-1z" class="O"></path><path d="M381 395c4 3 6 8 8 13-3-2-5-3-8-5h0l2-1c0-1-1-2-1-2l3 1c0-1-4-4-4-6z" class="F"></path><path d="M381 438h0c-1-1-2-3-2-4-1-1-1-2 0-3 1 0 2 0 3 1 2 1 3 2 3 4-1 1-2 2-4 2z" class="R"></path><path d="M394 463h10c1 0 3-1 4-1v1c-2 2-7 2-10 2l-9-1c3 0 3 0 5-1z" class="K"></path><path d="M394 448h0c6-1 7-6 11-6h1c-1 2-2 4-4 5-3 2-6 3-10 2l2-1z" class="H"></path><path d="M194 501c-2 6-3 11-5 17 0-6-1-13 2-19v4h0c2 0 1-2 3-2z" class="K"></path><path d="M299 366l1 2-9 7c-2 2-4 5-7 6 4-5 10-10 15-15z" class="D"></path><path d="M370 395l1-1h-1l1-1 7 5h1l-1-1 1-1c1 1 1 2 2 3l1 1s1 1 1 2l-2 1h0l-11-8z" class="I"></path><path d="M353 704c6-1 9 0 14 3l-8-2-14 2c3-2 5-3 8-3z" class="O"></path><path d="M298 646c4-4 10-8 16-8 1-1 3 0 4 0h1-1c-5 1-9 2-13 5l-5 5v-1c1-1 1-2 2-3h0 0c-1 1-2 2-4 2h0z" class="D"></path><path d="M394 797c4 2 7 5 9 8 0 2-1 3-1 4-2-1-2-3-4-5-1-2-3-3-5-5l1-2z" class="Q"></path><path d="M347 322c5-1 11 0 16 0l-16 4c-1-1-3-1-5 0h-2l1-1h3c1-1 2-1 3-2v-1z" class="C"></path><path d="M392 631c5 6 8 12 11 20l-13-19 2-1z" class="J"></path><path d="M332 326h1l3-2c1-1 2-1 2-3l1 2 8-1v1c-1 1-2 1-3 2h-3l-1 1h2c2-1 4-1 5 0l-8 2v-1c-1-1-1-1-1-2-1 0-2 0-2 1-2 0-2 1-4 0z" class="B"></path><path d="M378 563c3 6 5 11 7 17-4-4-9-9-11-14 1 0 1 1 2 1l1 1h2v-2c-1-1-1-2-1-3z" class="C"></path><path d="M405 451v1c-1 1-2 1-3 1v1h0c1 0 1 0 2 1-3 0-6 1-8 1l-3-2h-3v-2h3c4 0 8 0 12-1z" class="O"></path><path d="M390 452h3c1 0 2 1 4 1v1h-4-3v-2z" class="G"></path><path d="M249 607h1v-2 2h1c0 1 1 2 1 4v1c-1 7-2 12-2 19-2-7-1-16-1-24z" class="H"></path><path d="M386 363c4 1 8 3 12 5 2 2 6 3 8 5h0c-3 0-7-3-11-4-2-1-6-1-8-3h0 1 1v-1c-1 0-1 0-2-1h0l-1-1z" class="C"></path><path d="M306 419h14c1 1 2 1 3 1 5 1 10 2 14 4-5-1-10-2-16-2-3-1-7 0-11 0l1-1h4c1 0 1-1 3-1 1 0 1 1 3 0h-4c-3-1-7 0-10-1h-1z" class="D"></path><path d="M385 436l3 9v2l-3-1c-3-2-3-5-4-8 2 0 3-1 4-2z" class="F"></path><path d="M392 509h1c5 1 10 0 14-1l1 1c-3 3-16 4-20 4 2-2 2-2 5-2h0 0-4l-4-1v-1h7z" class="K"></path><path d="M196 662h2c1 4 1 9 3 13-1 2-1 3-3 4l-2-7c0-1 0-1 1-1 1-2-1-7-1-9z" class="D"></path><path d="M384 561l-3-20c3 1 5 2 8 4h0-3c-1 1-2 2-2 4-1 3 1 7 1 11l-1 1z" class="G"></path><defs><linearGradient id="C" x1="192.569" y1="643.673" x2="196.931" y2="646.327" xlink:href="#B"><stop offset="0" stop-color="#8b8c8b"></stop><stop offset="1" stop-color="#a8a8a4"></stop></linearGradient></defs><path fill="url(#C)" d="M195 632l2 22c-1 1-1 2-2 2l-2-17v-3h1 1v-4z"></path><path d="M406 562l1 1c1 0 1 1 1 2 1 1 0 5 0 7 0 8 1 17 0 25-1-8-1-17-1-25-1-2 0-7-2-9v-1h1z" class="G"></path><path d="M208 594c3-1 6-5 8-7h1l-6 12h0c-1-1-3 0-5 1l2-6zm181-49c2 1 3 2 5 5v-1c-1 4-1 8 0 13 0 3 1 7 1 10-1-1-1-2-2-3h-1c-2 0 0-3-1-5v-3c-1-1-1-1 0-2 1-2 1-7 1-10l-3-4h0z" class="N"></path><path d="M205 585l2 4c-2 8-4 17-5 25l-1 2v4 1h-1v-7c1-10 3-19 5-29z" class="O"></path><path d="M348 847c3 4 6 10 10 12h0c0 1 1 2 1 2l1 1h0l-1 1-3-4h-1v1 1c-3-1-6-5-8-7l1-1h0c1-2-2-4-2-6h2z" class="P"></path><path d="M327 474c6 1 11 3 16 7 5 3 9 7 14 9l3 2c-9-3-16-8-24-12-3-1-6-1-9-3h1 1 2l2 1h1l1 1 2 1h2l1 1c1 1 3 3 5 3-2-3-4-4-6-5s-5-2-7-3h-1-2c-1 0-2-1-2-2zm0 175h0c-2-2-5-3-7-3-3 0-6 2-8 4s-4 6-3 9c0 3 1 5 3 7 1 0 4 1 4 2-2 0-4-1-6-3s-3-4-3-7c0-4 2-7 5-9 2-3 5-4 9-4 2 0 5 1 6 3 1 1 1 1 1 2l-1-1z" class="I"></path><path d="M311 379c-9 3-17 6-26 11-3 1-6 2-9 5h0-1c6-6 16-9 23-13 4-2 7-5 11-6h0c-1 2-3 2-4 4 1 0 1 0 2-1h1 0 3z" class="B"></path><defs><linearGradient id="D" x1="322.72" y1="354.708" x2="302.099" y2="358.237" xlink:href="#B"><stop offset="0" stop-color="#c9c7c9"></stop><stop offset="1" stop-color="#fcfaf8"></stop></linearGradient></defs><path fill="url(#D)" d="M316 352c2 0 4-1 6-2 0 0 1 1 1 2l-18 12h-1-1c1-1 2-2 2-3h0 0c4-4 7-6 11-9z"></path><path d="M188 616l1-1c2 7 4 14 4 21v3h0c-1-1-1-2-1-3l-1 2c0-1 0-1-1-2l-1 1v-2c-1-7 0-13-1-19z" class="F"></path><path d="M317 823h0c6 4 8 13 11 19 2 3 3 6 6 9-10-7-13-18-17-28z" class="J"></path><path d="M229 438c-3 3-11 3-14 4l2-1c9-4 15-12 21-20h1c0 1 0 1 1 1-3 4-5 7-8 10-1 1-1 1-1 2l-3 3 1 1z" class="B"></path><path d="M286 489c-9 4-17 8-24 15-3 2-6 6-9 8h0c10-11 18-20 32-26l-1 1c-2 2-5 3-7 4h2 1c0-1 1-1 2-2v1l1-1h1 2z" class="I"></path><path d="M402 524l1 1c-2 2-6 6-7 9 0 1-1 2-3 3v1l-2 2v1l-6-3 4-3s8-6 9-8l4-3z" class="C"></path><path d="M389 535s2 1 3 0c1 0 1-2 3-1l-1 1-1 3-2 2v1l-6-3 4-3z" class="F"></path><path d="M332 326c2 1 2 0 4 0 0-1 1-1 2-1 0 1 0 1 1 2v1l-23 10c3-3 6-6 10-8 2-1 4-2 5-3h1v-1z" class="D"></path><path d="M299 711h7l7 1c4 1 10 0 14 0h1c3 2 5-1 8 0v1l-19 2c-5 0-9-1-13-2-1 0-3 0-3-1-1 0-1-1-2-1zm-34-108c1-1 2-1 3-1-6 11-10 20-11 32v-3l-1 1v-7c2-8 4-15 9-22z" class="C"></path><path d="M362 639c9-1 14 1 21 6-5-2-10-3-16-3-7 0-11 2-16 7l2-3c2-4 5-6 9-7h0z" class="B"></path><path d="M312 579c2-1 6 0 9 0l1-1c1 1 2 1 4 1 1 0 0 0 1 1 1 0 2-1 3-1-1 0 0 0-1-1 1-1 2-1 4-1l3 1 1 1h2 1c2 1 2 2 4 1l-3-1v-1c4 1 10 4 13 7h1c-6-2-13-4-19-6 3 3 8 5 12 8 1 0 3 1 4 2-3 0-7-3-10-5-9-4-20-5-30-5z" class="D"></path><path d="M389 464c-6-2-13-4-18-8-8-5-15-14-17-23-1-3-1-6-2-9 1 2 2 5 3 8 2 5 4 10 7 14 8 10 19 15 32 17-2 1-2 1-5 1z" class="I"></path><defs><linearGradient id="E" x1="334.189" y1="339.227" x2="326.997" y2="352.87" xlink:href="#B"><stop offset="0" stop-color="#979693"></stop><stop offset="1" stop-color="#c5c4c6"></stop></linearGradient></defs><path fill="url(#E)" d="M337 340c1 1 3 2 4 3h1c-7 2-13 5-19 9 0-1-1-2-1-2-2 1-4 2-6 2 5-5 15-9 21-12z"></path><path d="M287 361c3-2 6-4 8-6 2-1 4-3 6-3-3 2-6 5-8 7l-5 7c-1 1-2 2-2 3l-1-1-14 18c-2 2-3 5-5 7 0-3 2-5 4-8 1 0 1-1 2-2s2-2 2-3c1-2 2-2 2-3 2-4 6-6 8-9 1-1 1-2 2-3 1-2 2-2 1-4z" class="M"></path><path d="M150 498c3-5 6-10 10-13s8-5 13-4c-9 2-13 9-19 16-1 1-2 2-3 2l-1-1z" class="C"></path><defs><linearGradient id="F" x1="404.037" y1="513.207" x2="388.766" y2="515.971" xlink:href="#B"><stop offset="0" stop-color="#a4a2a3"></stop><stop offset="1" stop-color="#d0cfce"></stop></linearGradient></defs><path fill="url(#F)" d="M384 514l12-1c3 0 9-2 12-1 0 1 0 2-1 2-1 1-3 3-4 3h-3c-1 1-1 1-2 1h-2l-1-1h-10 2v-1h-1c-1-1-1-1-2-1v-1z"></path><defs><linearGradient id="G" x1="393.351" y1="500.099" x2="387.054" y2="513.097" xlink:href="#B"><stop offset="0" stop-color="#a8a6a4"></stop><stop offset="1" stop-color="#f8f8fa"></stop></linearGradient></defs><path fill="url(#G)" d="M379 506c8 0 17 0 25-2 1 1 2 2 2 3-4 1-10 2-14 2h-7-1-4-1c0-1-1-1-2-1h-1c1-1 1-1 2-1l1-1z"></path><defs><linearGradient id="H" x1="218.292" y1="434.672" x2="225.788" y2="423.207" xlink:href="#B"><stop offset="0" stop-color="#b4b2b2"></stop><stop offset="1" stop-color="#d7d5d3"></stop></linearGradient></defs><path fill="url(#H)" d="M226 422l3-3c0 2-1 2-2 4h1v1c1 0 2-1 2-2l2-2h1c-7 10-21 21-33 23h0c9-4 15-9 22-16 1-2 3-4 4-5z"></path><defs><linearGradient id="I" x1="303.477" y1="701.152" x2="302.243" y2="706.579" xlink:href="#B"><stop offset="0" stop-color="#cecdce"></stop><stop offset="1" stop-color="#f2f1f1"></stop></linearGradient></defs><path fill="url(#I)" d="M284 700c5 1 10 3 15 4 8 1 16-1 24-2-5 3-10 5-16 5h-3c-2 0-4 0-6-1-4 0-12-3-14-6z"></path><path d="M400 490l7-2c1 1 1 1 1 3v10c0 1-1 1-1 1-2 0-4 1-6 1v-1h3l-2-2c-1-1-1-1-1-2-1-1-1-1-1-3l2-3 1-1v-1h-3z" class="B"></path><path d="M407 502c0-3-3-5-2-7 0-2 2-3 3-4v10c0 1-1 1-1 1z" class="G"></path><path d="M189 637l1-1c1 1 1 1 1 2l1-2c0 1 0 2 1 3h0l2 17 1 6c0 2 2 7 1 9-1 0-1 0-1 1-4-11-6-24-7-35z" class="E"></path><defs><linearGradient id="J" x1="378.388" y1="608.16" x2="361.69" y2="608.817" xlink:href="#B"><stop offset="0" stop-color="#a4a2a2"></stop><stop offset="1" stop-color="#dcdcdb"></stop></linearGradient></defs><path fill="url(#J)" d="M352 601c9 2 20 4 27 9v3 1c1 1 1 0 1 1-7-4-13-6-20-8l-1 1-2-2h1 3l1 1c-1-1-3-2-4-3v-1c-2 0-3 0-5-1l-1-1z"></path><path d="M400 581h0c-1-5-5-24-3-28 2 1 4 3 5 4h-1c-1 1-2 1-2 2v4l3 15c1-5 0-10 1-15 0-2 0-2 2-2l1 1h-1c-1 1-1 1-1 2-1 6 0 13 0 19-1 0-2 1-2 1l-1-4v2c0-1 0-1-1-2v1z" class="C"></path><defs><linearGradient id="K" x1="203.223" y1="700.948" x2="207.335" y2="684.319" xlink:href="#B"><stop offset="0" stop-color="#8d8e8a"></stop><stop offset="1" stop-color="#aeadaf"></stop></linearGradient></defs><path fill="url(#K)" d="M202 681c6 16 14 30 22 45h0c-10-13-17-26-24-41 1-2 2-3 2-4z"></path><defs><linearGradient id="L" x1="397.101" y1="523.99" x2="382.39" y2="535.498" xlink:href="#B"><stop offset="0" stop-color="#8b8b88"></stop><stop offset="1" stop-color="#bbb9b8"></stop></linearGradient></defs><path fill="url(#L)" d="M385 538l-5-3h0c2-2 4-3 6-4l15-10c2-1 5-4 7-4-1 3-4 4-6 6-2 1-3 2-4 4h0c-1 2-9 8-9 8l-4 3z"></path><path d="M256 561l6-3c-8 7-20 16-21 27v1h-1v-3h0c-1 1-2 0-2 1s-1 2-2 3c0-3 2-6 3-9l-1-1h0c4-6 10-11 16-15 0 0 1-1 2-1z" class="I"></path><path d="M340 702c1-2 3-3 4-4 1 1 1 1 1 2 4-4 7-7 13-7 1 0 2 0 3 1-1 0-1 0-2 1-9 1-14 10-21 15h-1-1v-1c-1 0-3 0-4 1 2-2 7-6 8-8z" class="S"></path><path d="M303 415c13-1 28 1 38 10 1 0 1 0 1 1h0 0c-3-2-5-3-8-5h-1c-3-1-6-2-10-2v1c-1 0-2 0-3-1h-14c-4 1-9 1-14 1-4 1-8 3-12 4 1-1 2-2 3-2l1-1c5-1 10-2 15-2l10-1h5c-1-2-5-1-7-2-1 0-2 0-4-1z" class="H"></path><defs><linearGradient id="M" x1="410.866" y1="541.248" x2="401.922" y2="546.373" xlink:href="#B"><stop offset="0" stop-color="#7d7e7d"></stop><stop offset="1" stop-color="#a2a19f"></stop></linearGradient></defs><path fill="url(#M)" d="M406 531l2-1v27-1c-2-3-5-5-7-7 0-7 4-11 5-18z"></path><path d="M367 783c8-8 21-13 33-13 2 1 5 1 7 1-10 2-19 3-27 8-3 2-6 4-10 6-1 0-2 0-3-2z" class="O"></path><defs><linearGradient id="N" x1="177.196" y1="461.369" x2="177.304" y2="451.664" xlink:href="#B"><stop offset="0" stop-color="#9d9d9a"></stop><stop offset="1" stop-color="#c3c1c3"></stop></linearGradient></defs><path fill="url(#N)" d="M162 450c12 7 24 9 38 10-1 1-4-1-6 1 2 0 5 0 7 1h0c-2 0-4 0-6 1-9 1-25-4-32-10l-1-3z"></path><defs><linearGradient id="O" x1="410.707" y1="601.079" x2="397.727" y2="598.056" xlink:href="#B"><stop offset="0" stop-color="#7f807f"></stop><stop offset="1" stop-color="#a5a4a2"></stop></linearGradient></defs><path fill="url(#O)" d="M400 581v-1c1 1 1 1 1 2v-2l1 4s1-1 2-1c1 15 5 29 5 44-2-6-3-12-4-19l-5-27z"></path><path d="M238 577l1 1c-1 3-3 6-3 9-1 2-3 6-3 8-4 13-5 25-4 37l1 4v1c-3-9-3-18-3-27 1-2 1-5 1-8 2-9 5-17 10-25z" class="D"></path><path d="M267 672c7 11 17 19 29 24 4 2 9 2 12 4-2 0-5 0-7-1-12-2-20-7-28-16-3-3-6-6-8-10 0-1 1-1 2-1z" class="E"></path><defs><linearGradient id="P" x1="285.064" y1="363.056" x2="276.226" y2="371.739" xlink:href="#B"><stop offset="0" stop-color="#706f6d"></stop><stop offset="1" stop-color="#8b898a"></stop></linearGradient></defs><path fill="url(#P)" d="M287 361c1 2 0 2-1 4-1 1-1 2-2 3-2 3-6 5-8 9 0 1-1 1-2 3 0 1-1 2-2 3s-1 2-2 2v-3c1-1 2-3 2-4s-1-1-2-2c0 1-1 2-2 2l-1-1h-1v-1-1l3-3v2l18-13z"></path><path d="M270 376l2-2h0l4-2h1l1 1c-1 1-3 2-4 3v2c0 1-2 1-2 2v3c-1 1-1 2-2 2v-3c1-1 2-3 2-4s-1-1-2-2z" class="Q"></path><path d="M385 560c3 15 6 30 11 45 1 5 4 10 5 15 2 4 3 8 5 12l-1 1c-4-6-6-12-8-18-3-8-4-17-6-24-3-10-7-20-7-30l1-1z" class="C"></path><defs><linearGradient id="Q" x1="278.338" y1="541.512" x2="261.162" y2="563.488" xlink:href="#B"><stop offset="0" stop-color="#727170"></stop><stop offset="1" stop-color="#919193"></stop></linearGradient></defs><path fill="url(#Q)" d="M249 560c12-10 25-13 40-13 3 0 7-1 10 0h1c-13 1-26 3-38 9-2 1-4 2-6 4v1c-1 0-2 1-2 1 0-1 1-2 1-3-2 0-4 0-6 1z"></path><path d="M338 710c2-1 4-2 6-2 8-1 16 0 22 5 2 2 4 5 6 8-1 0-2-1-2-2-5-4-12-7-17-7l-17 1v-1c-3-1-5 2-8 0 2 0 3 0 5-1 1-1 2-1 3-1h1 1z" class="O"></path><path d="M231 544l1-2c7-5 17-12 26-11 4 0 6 1 8 4-1 3-7 4-10 6l-1-1 3-3 1-1-1-1c-4-2-9 1-13 2-3 2-5 3-7 5-1 0-3 2-4 2-2 0-2 1-3 2l-1 1h-1c1-1 1-2 2-3h0z" class="D"></path><path d="M263 431c10-11 27-14 40-16 2 1 3 1 4 1 2 1 6 0 7 2h-5l-10 1c-5 0-10 1-15 2l-1 1c-1 0-2 1-3 2-3 2-7 4-10 6l-4 3-1 1c-1-1-1-1-1-2 1-1 3-2 4-3h-1c-1 1-2 2-4 2z" class="F"></path><path d="M328 650v1c-2-1-5-1-7 0s-4 3-5 5c0 2 0 3 1 4 1 2 3 2 5 3h0c0 1-1 2-2 3-2 1-3 1-5 0s-4-2-5-4c-1-3 0-5 0-7 1-3 4-6 7-7s7 0 10 1l1 1z" class="E"></path><path d="M347 854c-6-8-8-16-10-25-3-14 1-27 8-38 5-7 11-13 19-17 2-2 5-3 8-4h0c-13 7-27 20-32 35-3 8-4 18-2 27 1 5 3 10 6 15 1 2 3 4 4 6l-1 1z" class="J"></path><defs><linearGradient id="R" x1="210.972" y1="567.463" x2="218.972" y2="569.87" xlink:href="#B"><stop offset="0" stop-color="#727070"></stop><stop offset="1" stop-color="#a4a5a3"></stop></linearGradient></defs><path fill="url(#R)" d="M205 585c4-15 13-32 26-41h0c-1 1-1 2-2 3h1l1-1c1-1 1-2 3-2-14 13-22 27-27 45l-2-4z"></path><defs><linearGradient id="S" x1="407.706" y1="534.115" x2="393.281" y2="542.918" xlink:href="#B"><stop offset="0" stop-color="#abaaa9"></stop><stop offset="1" stop-color="#eaeae7"></stop></linearGradient></defs><path fill="url(#S)" d="M396 534l6-7h1l-1 3h0l1-1c2 1 3 1 3 2-1 7-5 11-5 18l-10-8v-1l2-2v-1c2-1 3-2 3-3z"></path><defs><linearGradient id="T" x1="185.464" y1="619.906" x2="204.317" y2="591.14" xlink:href="#B"><stop offset="0" stop-color="#999"></stop><stop offset="1" stop-color="#e9e8e7"></stop></linearGradient></defs><path fill="url(#T)" d="M194 584l1 3h1 3v-1l1-1c-4 16-5 31-5 47v4h-1l-1-20c0-11 0-21 1-32z"></path><path d="M298 646h0c2 0 3-1 4-2h0 0c-1 1-1 2-2 3v1l-1 1c-1 2-2 3-2 5-2 5-2 12 1 17 4 9 15 12 23 15-6 0-16-3-21-7-4-3-7-8-8-13 0-7 1-14 6-20z" class="E"></path><defs><linearGradient id="U" x1="220.474" y1="462.056" x2="220.52" y2="448.443" xlink:href="#B"><stop offset="0" stop-color="#a09f9e"></stop><stop offset="1" stop-color="#e9e6e6"></stop></linearGradient></defs><path fill="url(#U)" d="M200 460c14-1 28-5 38-13 2-1 5-6 7-6-6 11-16 16-27 20-3 1-7 1-10 2h-13c2-1 4-1 6-1h0c-2-1-5-1-7-1 2-2 5 0 6-1z"></path><path d="M395 572v4l2 11 6 27c1 2 3 6 3 8l-1 1-11-34-5-30c0-2-1-6-1-8s1-2 1-3h1c2 4 1 8 1 11-1 1-1 1 0 2v3c1 2-1 5 1 5h1c1 1 1 2 2 3z" class="G"></path><path d="M219 619c1 0 1 2 1 3 1 8 2 16 4 24 2 5 4 9 6 13 2 5 4 10 7 14 7 11 15 20 24 28-4 0-15-12-18-16-6-7-10-15-14-24-7-13-11-27-10-42z" class="T"></path><path d="M241 586v-1c2-4 7-9 10-12-2 3-5 7-7 10-6 11-11 27-11 39-1 6 1 12 2 18 0 1 0 1 1 2h-1c-5-13-5-29-2-43 1-4 3-8 3-12 1-1 2-2 2-3s1 0 2-1h0v3h1z" class="D"></path><path d="M249 560c2-1 4-1 6-1 0 1-1 2-1 3-6 4-12 9-16 15h0c-5 8-8 16-10 25 0 3 0 6-1 8l-1 1v-2l1-8c1-5 3-9 4-14h-1c-1 3-2 6-3 8h-2c5-14 13-25 24-35z" class="G"></path><defs><linearGradient id="V" x1="157.992" y1="541.157" x2="161.508" y2="518.343" xlink:href="#B"><stop offset="0" stop-color="#b8b7b8"></stop><stop offset="1" stop-color="#e2e0dd"></stop></linearGradient></defs><path fill="url(#V)" d="M162 529c6-3 11-6 15-12 1-2 2-4 4-5-2 8-6 13-13 17l-26 12c-1 0-2 0-2-1 6-3 10-8 15-14 2 0 3-1 5-2 1-2 2-3 3-4h0c1 1 1 1 0 2v1c0 1 0 1-1 2l-2 2-3 4v1c2-1 3-3 5-3z"></path><path d="M297 481h1c3 0 7-1 10-2 11-2 23-2 33 5l3 2c-6-1-11-3-17-4-9-2-22 1-32 4l-9 3h-2-1l-1 1v-1c-1 1-2 1-2 2h-1-2c2-1 5-2 7-4l1-1c1-2 7-3 10-4h0c1-1 1-1 2-1z" class="D"></path><path d="M308 708h8c12-2 19-11 26-20v1c-3 4-6 10-11 13-4 4-10 6-16 8 3 0 7 0 10-1 5-1 10-4 15-7-1 2-6 6-8 8 1-1 3-1 4-1v1c-1 0-2 0-3 1-2 1-3 1-5 1h-1c-4 0-10 1-14 0l-7-1h-7 0l-12-5-1-2h1c7 1 13 6 21 4z" class="F"></path><defs><linearGradient id="W" x1="266.446" y1="694.283" x2="272.227" y2="684.47" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#9b9c99"></stop></linearGradient></defs><path fill="url(#W)" d="M287 706c-4-1-8-3-12-6-11-7-20-23-23-36 8 15 16 28 32 36 2 3 10 6 14 6 2 1 4 1 6 1h3v1h1c-8 2-14-3-21-4h-1l1 2z"></path><path d="M300 495h4c10 0 20 2 29 7 3 1 6 3 8 5-14-7-29-9-45-9-13 2-23 7-34 13-1 0-2 1-3 1 2-2 6-4 9-6 5-3 9-6 15-8s11-3 17-3z" class="N"></path><defs><linearGradient id="X" x1="412.074" y1="639.131" x2="375.043" y2="618.657" xlink:href="#B"><stop offset="0" stop-color="#7a7978"></stop><stop offset="1" stop-color="#a2a2a1"></stop></linearGradient></defs><path fill="url(#X)" d="M379 610c6 4 11 8 16 13 4 4 7 7 10 12v-2l1-1c3 6 3 11 3 18-3-5-7-11-11-16-5-7-11-13-18-19 0-1 0 0-1-1v-1-3z"></path><path d="M378 451c-7-5-12-11-15-19 0-2-2-8-1-10l7 2 3 1v3c1 9 4 15 10 20-1 2-2 2-4 3z" class="B"></path><path d="M328 354c3 0 5-2 7-2 1 0 1 1 2 1s2-1 3-1l2-1c4 0 12-1 15 0-15 4-29 9-43 16-5 2-10 5-14 8 8-8 17-16 28-21z" class="E"></path><path d="M146 461l2-2-1-1 1-1c9 6 19 12 30 13 7 1 13 0 20-1-6 3-18 4-25 3s-14-3-22-2h-1l-3 1c-2-1-3-2-5-3 1-3 2-5 4-7z" class="N"></path><path d="M142 468c1-3 2-5 4-7v3l-1 1h-1l-1 2 1 1h1v1l3 1v-1l2 1-3 1c-2-1-3-2-5-3z" class="C"></path><defs><linearGradient id="Y" x1="208.447" y1="632.507" x2="200.496" y2="632.491" xlink:href="#B"><stop offset="0" stop-color="#d0d0cf"></stop><stop offset="1" stop-color="#fefcfd"></stop></linearGradient></defs><path fill="url(#Y)" d="M204 609c1-1 1-2 2-4v-1h4c-5 17-8 34-7 52 0 8 1 16 2 25-2-5-3-11-4-16-2-14-2-30 0-44v-1c1-3 2-7 3-11z"></path><path d="M227 595c1-2 2-5 3-8h1c-1 5-3 9-4 14l-1 8v2c0 4-1 9 0 14 0 15 4 28 11 42v1c-4-6-7-11-10-18-6-13-6-31-4-46l2-9h2z" class="E"></path><path d="M225 595h2c0 3 0 6-1 8l-3 1 2-9z" class="D"></path><defs><linearGradient id="Z" x1="374.717" y1="619.171" x2="355.594" y2="606.596" xlink:href="#B"><stop offset="0" stop-color="#c6c5c5"></stop><stop offset="1" stop-color="#f3f3f1"></stop></linearGradient></defs><path fill="url(#Z)" d="M359 612h0c-1 0-2-1-2-1-2-1-3-2-4-2v-1h-1c0-1 1-2 1-3 1 1 2 1 3 1h1l2 2 1-1c13 5 25 14 34 24-2 0-3-2-4-3-3-3-6-7-10-9v1h0c4 3 8 8 12 11l-2 1c-2-2-6-7-9-8 0 1 3 3 3 3 7 7 11 14 15 22-3-4-5-8-8-12-5-7-12-14-20-18 0-1-1-1-2-2-3-1-6-3-10-5z"></path><defs><linearGradient id="a" x1="391.68" y1="333.062" x2="339.509" y2="346.75" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#949493"></stop></linearGradient></defs><path fill="url(#a)" d="M337 340c15-6 29-7 44-5 3 0 8 0 10 1s4 4 6 6h-2l-12-3c-14-2-27-1-41 4h-1c-1-1-3-2-4-3z"></path><defs><linearGradient id="b" x1="378.667" y1="429.243" x2="380.91" y2="418.905" xlink:href="#B"><stop offset="0" stop-color="#cbcbca"></stop><stop offset="1" stop-color="#f0efee"></stop></linearGradient></defs><path fill="url(#b)" d="M347 411c18 3 36 8 52 17 3 1 6 3 9 5 1 1 0 2 0 4-1 0-4-2-5-3-5-2-9-5-14-7s-11-4-17-6c-9-3-17-5-25-10z"></path><path d="M128 404c2-11 3-21 6-32 1 2 0 5-1 7-1 5-2 13 0 18v1c1 6 4 12 5 18 1 3 3 6 3 10-3-7-5-14-7-20l-1-5-1-3c0 4 1 8 2 12 2 4 3 10 4 14 2 5 5 9 8 13 5 5 10 9 16 13l1 3c-2-1-4-2-5-3-8-5-18-16-22-25-2-7-3-14-4-21h-1c0 2 1 4 1 6 1 9 2 17 6 26h0c-2-3-3-5-4-8-2-6-3-11-3-16-1 3-1 5-1 8-1-5 1-10 0-14v-1h0v-2c-1-2 0-3 0-4s1-1 1-2v-3c-1 1-1 1-1 3v1c-1 1-1 2-1 3s0 1-1 3h0z" class="B"></path><defs><linearGradient id="c" x1="402.728" y1="358.85" x2="392.359" y2="365.615" xlink:href="#B"><stop offset="0" stop-color="#878785"></stop><stop offset="1" stop-color="#b6b5b4"></stop></linearGradient></defs><path fill="url(#c)" d="M369 353c11-1 22 1 33 0l4 17c-1-1-16-7-16-7-2-1-3-1-4-1h-1l1-1h1c0-1-1-3-2-4s-2-2-4-2h-1c-2 0-3 0-5-1-1-1-4-1-6-1z"></path><path d="M385 357c2 0 2 0 3 1 0 1 1 2 2 3v2c-2-1-3-1-4-1h-1l1-1h1c0-1-1-3-2-4z" class="C"></path><defs><linearGradient id="d" x1="200.755" y1="594.246" x2="175.785" y2="541.735" xlink:href="#B"><stop offset="0" stop-color="#969797"></stop><stop offset="1" stop-color="#bdbcba"></stop></linearGradient></defs><path fill="url(#d)" d="M191 534h1c0-1 1-1 1-2 1-2 1-3 2-4h0c1 1 0 2 0 4-1 2-2 6-1 9-5 16-7 32-7 49 0 6-1 13 1 19l1 6-1 1c-1-7-3-13-4-19-3-21 1-44 7-63z"></path><defs><linearGradient id="e" x1="252.388" y1="642.368" x2="264.939" y2="640.139" xlink:href="#B"><stop offset="0" stop-color="#acacab"></stop><stop offset="1" stop-color="#dcdbda"></stop></linearGradient></defs><path fill="url(#e)" d="M261 602v1c0 2-2 4-2 6h1 1c0-3 2-5 4-6-5 7-7 14-9 22v7l1-1v3c0 9 1 17 4 25l6 13c-1 0-2 0-2 1h0c-7-8-12-20-13-32-2-11 2-29 9-39z"></path><path d="M256 625v7l1-1v3c0 9 1 17 4 25-1-1-2-2-2-3l-1-1v-2l-1-1v-2-1-2c-1-2 0-6-1-8v-1c-1-2-1-10 0-13z" class="G"></path><path d="M155 526l3-7c-3 3-6 6-9 8s-7 3-10 5c2-2 5-4 7-6 7-6 15-14 19-23 2-4 5-12 10-13l1-1v1c-1 2-3 4-4 6-2 6-1 14-3 20-2 5-4 9-7 13-2 0-3 2-5 3v-1l3-4 2-2c1-1 1-1 1-2v-1c1-1 1-1 0-2h0c-1 1-2 2-3 4-2 1-3 2-5 2z" class="D"></path><defs><linearGradient id="f" x1="371.105" y1="483.543" x2="379.695" y2="499.349" xlink:href="#B"><stop offset="0" stop-color="#d1cfcf"></stop><stop offset="1" stop-color="#f9f9f8"></stop></linearGradient></defs><path fill="url(#f)" d="M399 482l9-1v6l-14 4h-3c-1 1-2 1-3 1-4 1-9 2-12 5h-1-2c-2 1-6 2-8 2-1 0-3 0-4 1l-2-1h-14c-2 0-4-1-6-1-1 0-2 0-4-1h8c4 1 8 0 12 0 12-2 22-8 34-12 3-1 6-2 8-2l2-1z"></path><defs><linearGradient id="g" x1="302.782" y1="585.463" x2="267.79" y2="565.663" xlink:href="#B"><stop offset="0" stop-color="#6a696b"></stop><stop offset="1" stop-color="#adacaa"></stop></linearGradient></defs><path fill="url(#g)" d="M249 607c1-7 3-13 6-19 8-17 25-30 43-36 13-4 28-2 40 4h-1l-11-3c-7-1-13 1-20 3-11 3-21 5-30 12-10 7-15 15-20 25-1 2-2 4-2 6v1c-1 2-2 5-3 7h-1v-2 2h-1z"></path><defs><linearGradient id="h" x1="382.725" y1="334.763" x2="368.894" y2="355.777" xlink:href="#B"><stop offset="0" stop-color="#706e6d"></stop><stop offset="1" stop-color="#b5b6b5"></stop></linearGradient></defs><path fill="url(#h)" d="M328 354c14-10 33-15 50-14 6 0 12 1 17 2h2c2 3 3 6 4 9-11-3-22-3-34-2l-8 2h-2c-3-1-11 0-15 0l-2 1c-1 0-2 1-3 1s-1-1-2-1c-2 0-4 2-7 2z"></path><path d="M359 351l-3-3-2-1h0c4-1 8 0 12 0l1 2-8 2z" class="C"></path><path d="M136 471c-7 11-9 24-13 36-1-2-2-4-3-7-2-8 0-16 3-23 1-3 3-6 3-9 1-4 2-8 5-11 2-2 5-5 9-5 2 0 3 1 5 3h-1c-1 2-1 2-1 4h0 3 0c-2 5-7 8-10 12z" class="T"></path><path d="M144 455c-1 2-1 2-1 4h0c-2 1-4 2-7 3h0c-2 0-5 3-7 5v-1c3-5 10-10 15-11z" class="P"></path><path d="M143 459h3 0c-2 5-7 8-10 12v-1-2h-1c2-2 5-4 6-7-2 1-3 1-5 1 3-1 5-2 7-3z" class="I"></path><defs><linearGradient id="i" x1="266.466" y1="575.478" x2="242.66" y2="550.349" xlink:href="#B"><stop offset="0" stop-color="#696968"></stop><stop offset="1" stop-color="#878685"></stop></linearGradient></defs><path fill="url(#i)" d="M256 541l-1 1h0 1c14-6 33-11 48-5 3 1 5 3 8 4l11 8v1c-5-3-9-6-14-8-12-7-26-5-39-1-24 7-41 26-53 46h-1c-2 2-5 6-8 7 1-4 2-9 5-12 3-6 8-11 13-15 5-6 11-13 17-18l12-9 1 1z"></path><path d="M378 809c3 0 5 0 8 1s6 3 8 6c0 2 0 3-2 5-2 3-6 7-10 7l-3-3c-1-2 0-3 0-5 1-1 1-1 1-3h-1c-2 0-4 1-6 3s-2 5-2 8c0 4 2 7 4 9l2 1c1-1 1-2 3-3l-9 17c-3-4-6-8-7-13-2-7-2-15 1-21 3-5 7-7 13-9z" class="T"></path><path d="M234 544c1 0 3-2 4-2 2-2 4-3 7-5 4-1 9-4 13-2l1 1-1 1-3 3-12 9c-6 5-12 12-17 18-5 4-10 9-13 15-3 3-4 8-5 12l-2 6c2-1 4-2 5-1l-1 5h-4v1c-1 2-1 3-2 4-1 4-2 8-3 11v-4l1-2c1-8 3-17 5-25 5-18 13-32 27-45z"></path><path d="M206 600c2-1 4-2 5-1l-1 5h-4v1c-1 2-1 3-2 4l2-9z" class="B"></path><path d="M111 533c7-1 13-4 18-9 8-9 10-18 10-29h-1c-1 3-3 6-5 9 1-6 4-13 5-20l3 3c3 5 4 10 3 16v1c2-1 4-4 6-6l1 1c1 0 2-1 3-2 2 0 3-2 5-1 1 1 1 2 0 4 0 3-4 7-6 10-6 8-12 16-21 20-6 4-14 6-22 5 0 0-1 0-2-1h-2c1 0 3 0 4-1h1z" class="D"></path><defs><linearGradient id="j" x1="252.2" y1="586.351" x2="359.889" y2="587.445" xlink:href="#B"><stop offset="0" stop-color="#d3d2d0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#j)" d="M291 567c6-3 15-4 21-4 3 2 7 0 11 2h0c1 0 3 1 4 0h1 0v-1c10 1 22 5 30 10v1l2 1v1l-2-1-1 1c-6-3-12-5-19-6-17-4-36-2-52 6-15 7-28 20-34 35v-1c0-2-1-3-1-4 1-2 2-5 3-7l2-4c8-12 20-26 35-29z"></path><path d="M291 567c6-3 15-4 21-4 3 2 7 0 11 2h-12l-4 1-10 2c0-1 1-1 2-1h1l-1-1h0c-1 0-3 1-5 1h-3z" class="I"></path><path d="M367 783c1 2 2 2 3 2 9 3 17 6 24 12l-1 2c-6-4-13-5-21-4-8 2-15 8-19 15-5 9-7 18-4 28 2 10 9 17 15 25-2-1-4-3-6-4h0c-4-2-7-8-10-12-5-12-7-25-4-38 2-8 7-16 13-21 3-2 7-3 10-5zm3-161c5 5 10 9 13 15l-3-3c-10-10-25-15-39-16-3-1-6 0-10 0 9-3 17-2 26-1-6-4-12-7-19-7-13-2-29 0-41 7l-7 7c2-6 7-11 12-14 14-11 34-12 50-9l1 1c2 1 3 1 5 1v1c1 1 3 2 4 3l-1-1h-3-1-1c-1 0-2 0-3-1 0 1-1 2-1 3h1v1c1 0 2 1 4 2 0 0 1 1 2 1h0c4 2 7 4 10 5 1 1 2 1 2 2h-1l-4-2c1 1 4 3 4 5z" class="T"></path><path d="M359 612c4 2 7 4 10 5 1 1 2 1 2 2h-1l-4-2c1 1 4 3 4 5-4-3-7-7-11-10z" class="P"></path><path d="M261 602c7-11 20-20 32-24 14-5 34-6 48 0v1l3 1c-2 1-2 0-4-1h-1-2l-1-1-3-1c-2 0-3 0-4 1 1 1 0 1 1 1-1 0-2 1-3 1-1-1 0-1-1-1-2 0-3 0-4-1l-1 1c-3 0-7-1-9 0-5 1-9 1-13 2 10 1 20 3 30 6l16 6c-1 1-2 0-3 0-4-1-10-5-14-5l11 6c-3 1-7 2-11 1s-8-4-12-5c-16-4-36-1-47 12h-1c-1 0-2 0-3 1-2 1-4 3-4 6h-1-1c0-2 2-4 2-6v-1z" class="E"></path><path d="M394 491c2 0 4-1 6-1h3v1l-1 1-2 3c0 2 0 2 1 3 0 1 0 1 1 2l2 2h-3v1c-10 2-20 2-30 2 2 1 5 1 8 1l-1 1c-1 0-1 0-2 1h1c1 0 2 0 2 1h1 4 1v1l4 1h4 0 0c-3 0-3 0-5 2h-6l2 1v1c1 0 1 0 2 1h1v1h-2 10l1 1h2c1 0 1 0 2-1h3c-4 2-8 4-12 4h-1c-2 1-6 1-9 0-3 0-6-1-8-2-7-3-14-8-21-12l-14-8h0c2 1 5 1 8 1 4 0 9 0 13-1h0l2 1c1-1 3-1 4-1 2 0 6-1 8-2h2 1c3-3 8-4 12-5 1 0 2 0 3-1h3z" class="L"></path><path d="M384 509h1v1h-9l8-1z" class="B"></path><path d="M385 517h10l1 1h2c1 0 1 0 2-1h3c-4 2-8 4-12 4h-1c-2 1-6 1-9 0h8l-2-1 1-1c-2 0-6-1-9-1 2-1 4-1 6-1z" class="C"></path><path d="M358 574l15 10c-1-2-3-4-5-6-5-6-11-11-17-16-2-1-6-3-7-5-4-4-9-9-14-12-3-2-6-3-8-6 5 3 11 7 16 11 2 1 4 2 5 4s3 3 5 4c-2-3-4-5-7-7-1-1-6-5-6-6 7 4 12 9 17 14-7-12-19-20-32-26h0c17 4 32 15 42 30h1c-4-6-8-12-14-16-2-2-5-4-7-5-5-4-10-7-17-10-4-1-9-2-13-4 1 0 4 0 6 1 10 0 20 4 28 9 4 2 8 4 11 6h1c2 1 5 4 6 4v-1-1l1 1c0 1 1 2 2 3 0 0 0 1 1 2 0 0 1 1 1 3l2 2v2c1 2 2 2 2 4v1 2h-1l-3-4c6 14 14 26 18 41-5-8-10-16-19-21-3-2-7-4-11-5l1-1 2 1v-1l-2-1v-1z" class="L"></path><path d="M358 544c-3-3-7-7-11-10-12-8-25-11-39-13 8-1 16 0 23 2 2 1 5 3 7 3-11-7-24-11-37-9-5 0-10 2-15 3 8-4 15-6 24-7-4-2-8-2-12-2-15 0-32 3-44 10l-8 4h0c20-15 44-22 69-18 20 3 38 15 50 32 6 7 9 15 13 24 0 1 0 2 1 3v2h-2l-1-1c-1 0-1-1-2-1l-1-3c0-2-1-2-2-4v-2l-2-2c0-2-1-3-1-3-1-1-1-2-1-2-1-1-2-2-2-3l-1-1v1 1c-1 0-4-3-6-4zm-49-168c2-1 4-2 5-3h0c-5 1-11 3-15 5-3 1-7 5-10 5 3-2 7-6 11-7 5-2 9-6 14-8 17-8 36-15 55-15 2 0 5 0 6 1 2 1 3 1 5 1h1c2 0 3 1 4 2s2 3 2 4h-1l-1 1h1v1l1 1h0c1 1 1 1 2 1v1h-1-1 0l-4-1v1c6 2 21 9 24 13l1 10c-4-4-8-7-13-10v1l13 10v6l-8-5 6 7c0 1 2 2 2 3 1 2 0 4 0 6v11c-3-7-7-13-12-19 2 5 4 11 4 17-4-5-6-9-10-14-3-4-6-6-9-9h-1l1 2c0 2 4 5 4 6l-3-1-1-1c-1-1-1-2-2-3l-1 1 1 1h-1l-7-5-1 1h1l-1 1c-3-2-6-4-10-5 4 3 6 6 8 10l2 3c-8-4-16-10-24-12 4 3 7 6 9 10-7-4-14-4-23-5-13-1-25 0-38 5-5 2-9 4-14 7-3 2-5 4-9 6 2-3 5-5 7-7-3 1-6 3-8 6 1-3 2-5 4-8l1-1 1-1h-1v-1c0-1 0-1-1-1 5-4 10-7 16-10 6-2 12-5 18-6 7-2 14-3 22-4l29-4c-16-4-32-1-48 2h-3 0-1c-1 1-1 1-2 1 1-2 3-2 4-4h0z" class="E"></path><path d="M344 363h5c-3 0-6 1-10 2h-3c-1 1-1 1-2 0 3-1 7-2 10-2zm-26 27c1-1 5 0 6 0l-15 2c-2 0-4 1-6 1h0l3-1c4-1 8-2 12-2z" class="D"></path><path d="M366 383h1 2c1 0 2 0 4 1 1 1 3 0 5 2-2 0-3 1-5 0h-1c-1 0-2-1-4-2l-2-1z" class="L"></path><path d="M351 387c4 1 9-1 13 1v1h-2c-2-1-2-1-3 0v1h0-1c-3-1-5-2-7-3z" class="D"></path><path d="M380 355h1c2 0 3 1 4 2s2 3 2 4h-1l-1 1-9-3h6c0-1-1-2-2-4z" class="B"></path><path d="M352 368c10-1 20 0 30 5-3 0-5-1-8-2-10-2-20-2-30-2 2-1 5-1 8-1z"></path><path d="M280 424c4-1 8-3 12-4 5 0 10 0 14-1h1c3 1 7 0 10 1h4c-2 1-2 0-3 0-2 0-2 1-3 1h-4l-1 1h0c11 2 25 4 35 11 6 3 10 8 14 13 10 14 23 20 40 22 2 0 8-1 9 1v4 1c0 2 1 5 0 7h-4l-4 1h-1l-2 1v-1h-1c-2 1-4 1-6 1-2 1-4 1-6 1-11 0-20-3-28-9-6-4-10-9-16-11-7-3-15-4-22-5-8 0-17 0-25 2-5 1-10 4-15 6h1c5-2 10-3 15-4 9-3 20-3 29-2h1c7 1 13 3 18 6l1 1c0 1 0 1 1 2-1 0-2 0-3 1-6-3-11-4-17-5 2 1 4 1 6 2 9 3 17 8 25 15h1c2 1 4 3 5 4-2-1-5-2-8-3-3-2-5-4-8-5-5-3-11-5-17-6h-1c-5-1-16-1-21 2 6 0 14-2 21-1 0 1 1 2 2 2h2 1c2 1 5 2 7 3s4 2 6 5c-2 0-4-2-5-3l-1-1h-2l-2-1-1-1h-1l-2-1h-2-1-1c-11-1-20 2-30 4-1 0-1 0-2 1h0c-7 1-15 4-21 7l-6 4-25 25c1-3 3-7 5-10s5-5 8-8h-1c-16 13-30 31-40 49-3 5-5 9-7 14l-4 9-4 13-1 1v1h-3-1l-1-3c-1 11-1 21-1 32l1 20h-1c0-7-2-14-4-21l-1-6c-2-6-1-13-1-19 0-17 2-33 7-49-1-3 0-7 1-9 0-2 1-3 0-4h0c-1 1-1 2-2 4 0 1-1 1-1 2h-1c0-4 2-8 3-11 0-2 2-5 2-6 4-10 7-21 13-29l1-1c-4 3-8 5-12 8-2 2-3 4-4 6-2 0-1 2-3 2h0v-4c1-4 4-8 7-11 4-4 9-7 14-10 7-5 13-10 20-15 11-10 20-22 31-32 2 0 3-1 4-2h1c-1 1-3 2-4 3 0 1 0 1 1 2l1-1 4-3c3-2 7-4 10-6z" class="E"></path><path d="M268 493h-3 0-1l1-1c3-2 5-3 9-3l-6 4z" class="B"></path><path d="M204 572c-1-5 1-13 3-17l1 1-1 2v4l1 1-4 9z" class="D"></path><path d="M330 468c-3 1-8 1-11 0-2-1-4-1-6-1-1 1-1 1-2 0h0 1c1-1 1-1 3-1h1c2-1 5-1 7-1l1 1c2 1 4 1 6 2z" class="B"></path><path d="M399 468c2 0 8-1 9 1v4h-8-1l-1 1h0l-1-1c2-1 3 0 4-1h2v-3c-2 0-2 0-4-1h0z" class="I"></path><path d="M294 452h0c3 0 8-1 11 0 5 2 11 1 16 2 1 0 3 0 4 1h3v1l-14-1c-5-2-12-4-18-3-2 1-3 0-5 1h-2c-2 1-4 1-6 2h-1c2-1 4-2 6-2l1-1h5z" class="F"></path><path d="M219 509c0-1 1-3 2-4 2-3 4-6 7-9-1 2-1 3-3 5h1c1-1 1-1 2-1h0c0 2-3 5-4 7-1 1-2 3-4 5l-1-3zm-25 14h0c0 2 0 4-1 6h1l1-4h1c0-1 1-1 1-2l1 1v1c0 2-2 5-3 7s-1 4-1 6l3-6c1-3 1-4 4-6l-7 15c-1-3 0-7 1-9 0-2 1-3 0-4h0c-1 1-1 2-2 4 0 1-1 1-1 2h-1c0-4 2-8 3-11zm213-48l1-1c0 2 1 5 0 7h-4l-4 1h-1l-2 1v-1h-1c-2 1-4 1-6 1 2-2 4-1 7-1v-1h-1c-1 0-2 0-3-1h2c1 0 1-1 2-1h2l1-1c1 0 2 1 3 0h1 1c0-1 1-1 1-2h1-7c-2 1-5 1-8 1l15-2z" class="B"></path><path d="M357 463v-1c-10-12-22-16-37-17l-30-1c13-5 28-5 42-1-4-3-9-4-13-6 9 1 16 5 24 8-3-3-6-7-9-10-2-1-6-2-7-4 11 4 21 10 27 19 3 4 6 8 9 11 9 10 23 15 36 14h8l-15 2-5 1c-10 1-20-1-29-7-3-3-6-6-10-9l6 3 1-1-1-2h1c1 0 1 1 2 1h0z"></path><path d="M354 465l1-1-1-2h1c1 0 1 1 2 1h0c3 3 6 6 9 8-4-1-8-3-12-6z" class="K"></path><path d="M197 523c5-13 11-26 20-37 6-9 15-13 23-20 8-8 16-17 24-24 4-3 9-5 13-7 10-4 22-6 33-4-7 3-15 4-22 7-17 6-28 18-39 31 9-1 18 1 27 5-21-1-39 0-55 14-8 7-15 18-20 28-1 3-3 6-3 8l-1-1zm4 3c4-8 8-15 14-22 6-6 13-12 20-17l-7 9h0c-3 3-5 6-7 9-1 1-2 3-2 4-14 22-23 49-25 75-1 11-1 21-1 32l1 20h-1c0-7-2-14-4-21l-1-6c-2-6-1-13-1-19 0-17 2-33 7-49l7-15zM697 78c11-9 23-17 36-24 5-3 12-6 19-5 3 1 6 3 8 6 6 9 7 26 7 37 1 20 0 39-2 59l-9 51c-2 11-3 23-5 34h0c6-2 11-3 17-5 5-1 11-2 15-5 2-2 3-4 5-7 1 3 0 5-1 7-3 7-10 9-16 12-6 4-10 11-11 18 2-5 5-11 10-13 3-1 7 0 10 1 11 4 20 10 31 3l1 1-3 3h0v2c-4 2-8 2-12 2-2 1-5 0-6 1 4 1 8 2 12 4-12 0-24 2-36 4 3 1 6 1 9 1 9 3 18 10 24 17 8 8 13 15 26 15 4 1 9 0 12-4l3-3v4c-2 4-5 6-9 8-7 3-16 3-23 0-4-1-7-4-10-6h0c1 2 3 4 6 6 6 4 13 4 20 5-5 1-10 2-15 0-2 0-4-1-6-1l11 12a30.44 30.44 0 0 0 8 8c3 1 7 2 10 3 9 1 17 0 26 3 2 1 3 1 4 2h1c2 1 4 3 6 4l2 2h1l2 2h0c13 10 22 29 25 45 2 14 4 25 10 37-5-4-7-10-9-16l-1-1-1 1v1c-1 1-1 3-1 5l-1 2h0c-1 9-3 18-7 26 6 3 11 6 12 13 1 2 1 4 1 5-1 0-1-1-1-2-1-3-3-6-6-8-2-1-4-2-6-2h0c4 2 8 4 10 7 1 3 1 7 1 10-3-5-4-10-10-12-2-1-4-2-6-1-1 0-2 1-2 2 1 1 5 2 7 4 4 2 6 5 9 8 2 1 4 4 5 6 5 7 9 13 19 13 5-1 9-4 12-7l1-2c-2 6-5 10-11 13-4 2-8 2-13 1 0 4 0 7-1 10 0 5-4 11-3 15 0 3 2 4 3 6 4 4 9 6 15 6s12-2 17-6c4-5 5-10 5-16v-1c2 6 3 11 0 17-4 8-12 14-21 17-9 4-19 3-29 3 3 6 7 13 7 19-3-8-7-15-14-19 2 3 5 5 7 8 5 6 7 15 6 24-1 3-3 6-5 9 2-9 3-16-2-24-4-7-11-10-17-13-9-4-18-7-27-10 2 2 5 4 7 6 8 8 12 20 15 31 1 9 2 17 1 26-3 19-11 41-1 59 1 3 3 5 5 8-10-6-15-13-19-24-5-13-6-25-6-39-5 18-6 37-11 56-3 9-7 18-11 27-3 7-8 12-11 18l-15 25c-5 7-9 14-17 18-6 3-12 4-18 2-5-2-8-5-11-9 4 3 7 5 13 4 5-1 9-5 12-9 6-9 10-20 13-30 5-18 8-35 10-53 1-7 2-14 2-21-1 3-1 6-2 10l-3 15c-3 8-7 17-11 25-13 21-33 37-54 49l-13 6v1c2 3 3 7 1 11 0 2-2 3-4 4h0v-1c0-2 1-4 0-6l-2-2c-5 0-9 1-13 4-11 10-19 24-24 38 7 13 12 29 9 43-3 12-8 20-13 30 6-6 11-14 14-23 2-3 3-7 5-10 2-4 8-7 11-11 7-8 11-17 16-26-3 23-13 47-31 62-1 0-1 1-2 1-3 2-5 5-8 7-8 7-18 12-28 16-17 6-32 5-49-3 1 2 3 4 4 6 8 10 19 19 30 25-3 0-7-1-11-2-10-3-19-6-29-10-6-3-11-7-17-11l-9 8c-3 1-6 3-7 6l-1 1c-1 4 1 10 2 14-3-3-5-8-6-12-11 9-20 20-27 32l-9 15c-5 6-10 12-16 17-6-5-12-11-16-18l-8-13c-6-9-12-18-20-25-3-4-6-7-10-9-1 5-2 9-5 13 1-4 2-9 1-14-1-4-5-6-8-9l-8-6c-3 1-5 3-7 4-3 2-7 4-11 6-12 6-24 10-38 13 14-8 26-18 36-31h-2c-5 3-10 4-15 6-14 2-27-1-40-6 4-5 8-12 13-16 2-1 4-1 6-2 9-1 19-1 28-1l50-1 64 1c14 0 28 0 42 1 0 3 1 6 3 9-1-10-2-18 1-27l-2 2c-4 5-10 4-16 5-18 0-35-2-49-15-8-8-12-18-13-28-3-12-2-25-2-37v-43-157c11 0 24 0 35 4 13 4 23 13 30 25 8 16 8 38 7 55 0 8-1 16-4 24 8-2 16-2 23 2l1 1c-3-6-4-11-5-17-1-13-2-25-2-38v-58l1-89c1-9 1-19 3-28h-3c-5 1-10 0-15 0h0v-3-9c-1-7-1-14 0-21 2-13 7-24 14-35l-1-1c-2 0-6-1-8 0-2 0-4 0-5-1-2 1-4 4-5 6l2-5v-1c-1 1-2 1-2 2-2-1-3-1-5-1-1 1-2 3-3 4h-1l2-4h-1l-3 4-1-1-3 5-13 21h0l-5 14c-1 4-2 7-2 10-1 3-1 7-1 10s-2 6-1 10c0 0 0 2-1 2l-1 2v4c-1 10-1 19-1 29h0l-1-10h-1v-3c0-1-1-2-1-3-1 1-1 0-1 2h0v1 1l1 1h0c0 1 1 2 1 3v1h0v2 1l-1 1h0c-1-1-1-1-1-2v-1c-1-1-1-2-1-2v-2-1l-2-2-1-1c0 2 1 5 2 8-1-2-3-4-3-6-1 1-1 2-1 3l1 1h0c0 1 0 2 1 3-1 0-2 1-2 1v8l2 5h0l-1 1c-1-2-1-4-2-6l-1-1v6 8c0 1 2 3 2 4v2c1 3 2 5 3 8l1 3c1 1 1 2 1 4l2 3c0 1 0 3 1 4v1h1v-2h1v-2-1-1c1-4 1-7 2-10v-3c1-10 4-19 7-29 0-3 3-7 2-10 0-1 0-1 1-2v-1c0-1 1-3 2-4 0-1 0-2 1-3v-1l1-1v-1-2c1-1 1-1 1-2v-1c0-1 0-2 1-3v-1-2c0-1 1-2 1-2v-1c0-1 0-1 1-2h0l2-4 1-1h0c2 1 3 2 5 3 2 0 11 2 13 4 1 2 2 8 3 11 2 19 0 41-12 56-10 14-25 21-42 23l-16 2V323c51-1 109 2 149 39 21 20 39 48 40 78 0 12 1 25-7 35l1 1c4-4 7-8 12-9s11 0 14 3h1c-2-4-4-8-5-12-2-7-2-15-3-22l-1-29 2-77c1-12 1-23 3-35-8 1-16 1-24 1h-51-196-51c-11 0-24-1-36 1 2 1 4 2 5 4 4 5 0 8-1 13l-1 2c-3 1-9-1-11 1h-2c2 1 5 0 8 0h1 3s1 1 1 2l-1 2c0 2-1 2-2 3l-3 2h-1v1c-13 4-27 6-39 13-9 4-15 10-22 16-5 5-11 10-14 16v2c12-15 26-27 44-34 4-1 8-3 13-3h2l-17 9c-9 5-18 12-26 19-3 4-8 8-9 12l2-2v1 1h1l1 1c1 0 2-1 2-2 1 1 2 1 2 2s-1 3-2 4v3c-2 3-4 5-4 8 2-2 3-5 5-7l14-18 1 1c-5 7-9 15-15 22l-11 13c-7 10-13 19-22 27-3 3-6 5-9 7l-1-1 3-3c0-1 0-1 1-2 3-3 5-6 8-10-1 0-1 0-1-1h-1c10-14 16-29 22-45-6 7-10 16-14 25-4 6-8 13-13 19h-1l-2 2c0 1-1 2-2 2v-1h-1c1-2 2-2 2-4l-3 3-13 8c-15 9-31 9-47 5h1c7 9 18 14 29 15 15 2 37-3 49-13 0 0 1-1 2-1-1 1-1 1-1 2-2 2-6 4-8 6-10 6-20 9-31 10-13 1-26-1-35-10-3-3-5-7-8-9-3-3-7-4-9-7-12-9-15-21-17-35-1-15 1-32 10-45 3-3 6-5 8-9h1 0c0-1 1-1 2-2h1l2-2c2-1 3-2 5-2h1c0-1 0-1 1-1 6-2 11-3 17-3 4 0 9 0 13-2 5-1 9-6 12-9l11-13c-8 3-13 3-22 2 5-1 11-1 15-3 5-1 12-5 14-10-4 4-9 7-15 9-7 3-15 1-22-2-5-3-6-7-7-12 2 2 3 4 5 6 4 2 8 3 12 2 18-1 24-19 38-27 7-4 12-4 19-6-6-1-12-3-18-3-6-1-12-1-18-1 4-2 8-3 12-4-2-1-4 0-6 0-4 0-9-1-12-4l1-1h0c-2-2-4-3-4-5 12 11 23-1 36-3 3-1 6-1 8 0v-1c-1-1-3-3-5-4l-4-3c-4-2-7-4-9-7-1-3-2-6-1-9 1 1 0 1 1 2 7 13 25 9 36 16l1 1-3-24-10-57c-3-19-4-38-4-58l1-4c2-12 0-28 7-39 2-3 5-5 9-6 4 0 8 1 12 3 14 6 25 15 37 25h2c1 0 5 3 7 3l6 3h0c12 7 24 15 34 25 8 7 14 18 24 23-5-4-14-12-14-19 0-2 1-5 2-7 1 1 1 0 1 1-1 4 0 6 3 9 4 5 14 3 20 4 2 0 4 0 6 1 1 1 1 3 2 3v-2-1c0-2 0-3-1-5 0-4-4-8-6-11l1-1c2 2 4 3 6 5l2-2 6 6-1 2c2 2 4 3 7 4h1c2 0 4 0 6-1 6 1 12 3 18 6 4 2 7 5 12 7-3-4-6-8-10-11-5-4-18-9-20-15 1 0 0 0 1 1 5 4 12 6 17 10 14 9 24 24 33 38-15-13-36-23-56-21h0c4 1 8 2 12 4 17 7 32 23 41 38l3 5c-4-3-6-8-10-12-13-14-31-21-49-26-8-1-17-3-24-7 2 4 4 7 7 9s6 3 9 5c6 2 12 6 16 11 6 6 9 13 13 20 1 3 2 7 4 10l6 6 8 8c11 13 21 29 30 44l-5-15 5 8h0c1 1 1 2 2 3 0-2 0-5 1-7v-1c-2-6-4-13-7-20-8-18-22-37-37-51v-1c11 7 19 17 26 28v-1c1 0 1-1 1-1 1 0 1 0 2 1s0 0 0 1l1 1c1 1 2 3 3 4 0 1 1 2 1 3l3 5c1 1 1 2 2 3 0-1 0-1 1-1l-2-8c-1-1-1-2-1-3v-1c0-1 0-1-1-2v-1l1-1v1h1v-2-2h0l1 1 1 1v-1l1-1v2l1 1v2 1l2 4v2 1c1 0 1 1 1 2s1 2 1 2l1 7v2l1 1v4l3 19c1 5 1 10 1 14 1-4 1-9 2-13v-9c0-4-1-7-1-10 0 2 0 5 1 7 1-1 1-2 1-3l-1-1c0-3 0-7 1-10 1 3 1 6 2 9v1h0v-2-1-1c0-1 0-1 1-2 1-2 0-4 0-6 1-1 1-4 1-6s0-3 2-5c0 3-1 7-1 10-1 3 0 5-1 7v3l-1 1-1 18 1 1h1c2-4 3-10 4-15l3-7c0-2 1-3 2-5h-1l1-1c0-1 0-2 1-3 1-3 10-20 11-21 1 1 1 1 1 2l-1 2 1-1 3-4v-1h1l11-12c4-4 8-7 13-11 1 0 2-2 3-2-17 16-33 34-42 56-2 6-4 12-6 19-1 3-3 6-3 10-1 3-2 6-2 9v4c-1 1-1 2-1 3h1v-4c1-1 2-4 2-5s-1-1 0-2c0-4 1-7 3-10h0c-1 3-2 8-3 11l1 1 7-20h0c-1 7-3 14-6 21 3-4 5-8 7-11 6-10 12-21 19-30 4-5 8-9 12-13 4-3 8-6 10-10 2-2 3-6 4-9 2-7 6-13 10-18s9-9 15-13c4-2 9-3 13-6 3-2 5-5 7-8-2 0-4 2-7 2-7 2-14 3-21 5-19 5-35 14-48 28l-9 12 3-6c9-16 25-32 41-40 5-2 9-4 14-5h-1c-12 0-26 3-36 9-7 4-13 9-19 15 8-16 20-31 34-41 5-3 11-5 15-9 1-1 1-1 2-1-2 3-5 4-7 6-9 6-15 12-19 22 7-6 13-11 22-14h1c1 0 2 0 3 1h0c5 0 10-1 14-4 3-3 5-7 9-9l1-1h1 0c-1 2-2 3-3 5v1c-4 6-5 14-11 19v2h2c5 0 7-6 9-11l1-2h3c6-1 17 1 22-3 3-2 4-5 5-9v-1c1 1 1 3 1 5 1 8-9 15-14 20 10-5 15-14 23-21 10-10 21-19 33-26h1c0-1 1-1 1-2h1c1-1 2-2 3-2l5-3v-1l2 1z"></path><path d="M712 713l3-1-1 3h-3l1-2z" class="G"></path><path d="M716 712h1l1 2-4 1 1-3h1z" class="C"></path><path d="M717 712l4-1v3h-3l-1-2z" class="N"></path><path d="M536 758s2 0 2 1v2h-1c-1 1-1 1-2 1 1-2 1-2 1-4z" class="T"></path><path d="M502 347l-1-10 2 7c0 1-1 2-1 3z" class="M"></path><path d="M728 519c1 0 1 0 3 1h5c-1 1-10 1-12 1l4-2z" class="P"></path><path d="M533 246c1-2 2-3 3-5 1 2 1 2 0 4h0c-1 1-2 1-2 2l-1-1z" class="L"></path><path d="M426 171l-5-7 5 3v4z" class="O"></path><path d="M873 445l1 2c-1 1-3 2-4 3h-1 1v-3l3-2z" class="I"></path><path d="M802 548l4 5h-1c-2-1-3-1-4-2l-2-1 3-2z" class="Q"></path><path d="M721 220l1 2-5 6v-1c0-2 0-3 1-4h1v-1l2-2z" class="S"></path><path d="M628 432c1-1 1-1 3-1 0 1 1 3 0 4 0 2-1 2-2 3l-1-6z" class="R"></path><path d="M653 473l4-3 3 2-6 4 1-2v-1h-2z" class="I"></path><path d="M326 242l7 4c-4 0-8-1-11-3h3l1-1z" class="O"></path><path d="M760 671l2 2c-1 1-2 4-4 5v-1l1-1c-1 0-2 1-3 1l4-6z" class="B"></path><path d="M757 139l1 2h0l1 1h0c0 4-1 8-2 12v-15z" class="O"></path><path d="M426 167c1 0 2 2 3 2v1c0 1 1 2 2 3l-2 1-3-3v-4z" class="J"></path><path d="M879 440l1 2-6 5-1-2 6-5z" class="D"></path><path d="M671 222l-8-4 13 1h-5l2 1c-1 1-2 1-2 2zM550 343l1-2h4c1 1 0 2 0 4-3 0-3 0-5-2z" class="U"></path><path d="M762 667h4l-4 6-2-2 2-4z" class="H"></path><path d="M797 544c2 1 3 3 5 4l-3 2-4-5c1 0 1-1 2-1z" class="C"></path><path d="M704 334c2 0 7 5 9 6-3-1-8-2-9-3v-3z" class="L"></path><path d="M718 228l1 1c2-1 3-1 5-1-3 2-5 5-8 7 0-2 1-3 2-5h0v-1-1z" class="S"></path><path d="M358 291h0c1 0 3 0 4-1 6 0 13 0 19 1h-17c-2 1-6 1-8 0h2z" class="P"></path><path d="M533 246l1 1c0-1 1-1 2-2l-5 9h-2c1-3 2-5 4-8z" class="B"></path><path d="M657 470c3-2 6-3 8-5h1c-1 3-4 5-6 7l-3-2z" class="C"></path><path d="M465 209l-6-14c2 1 4 4 6 7 0 2 0 4 1 6l-1 1z" class="N"></path><path d="M465 202l3 5c0 1-1 2-1 4 1 2 1 3 1 4l-3-6 1-1c-1-2-1-4-1-6z" class="J"></path><path d="M464 214c2 3 3 7 3 10h-1c-1-2-3-5-4-8 1-1 2-1 2-2z" class="O"></path><path d="M519 762c0-1 4-1 5-1v1l-2 2v1 3c-1 1-2 1-3 1v-1l1-1c0-1 0-4-1-5z" class="B"></path><path d="M277 139c1-6 3-13 5-18l-3 19v3c0-2 0-4-1-6v2h-1zm155 26c1-2-1-6-2-8l6 6-1 3h-2l-1-1z" class="M"></path><path d="M630 281c-5 2-9 4-13 6h0c2-4 6-7 10-8l-2 2 1 1 1-1h3z" class="B"></path><path d="M837 503c1 5 1 9 0 13l-3-13c2 1 2 1 3 0z" class="G"></path><path d="M421 286c2 2 3 5 3 7-1 0-1 1-1 0-2-1-4-4-5-6h3v-1z" class="J"></path><path d="M658 290c3 0 8 0 11 1-3 1-7 0-10 0-3 1-6 1-9 2h-1v-1c2-2 6-2 9-2z" class="R"></path><path d="M722 247h1v1h1 1l-5 5c-2 0-4 1-7 0l1-1c3-1 7-2 8-5z" class="N"></path><path d="M295 206l8 13c-2-1-2-2-3-2l-3-3h-1c-1-3-2-4-1-8z" class="M"></path><path d="M815 334c5 3 11 2 17 1l-1 1c-3 1-6 3-10 2-2-1-5-2-6-4z" class="E"></path><path d="M874 492c2 4 3 7 6 9h-3l-5-4c0-1-1-2-2-3 2 0 3 0 4-2z" class="H"></path><path d="M608 571v-10c1-1 3-3 4-3 2 0 2 0 3 1 0 2 0 4-1 6 0-2-1-4-3-5 0 0-1 0-1 1-1 1 0 3-1 4-1 2-1 4-1 6z" class="C"></path><path d="M502 476v4l1-1 1 8 2 5h0l-1 1c-1-2-1-4-2-6l-1-1v6-16z" class="H"></path><path d="M710 311c2 0 3 0 6 1 1 1 2 0 3 2l-16-1c2 0 5-1 6-2h1z" class="B"></path><path d="M430 168l-3-3h1 0v-2c-1-1-2-3-3-5l7 7 1 1h2l1 1v1h0-1c-1-1-2-1-4-1l-1 1z" class="J"></path><path d="M446 283c3 3 6 8 8 12l-10-10h0c1-1 2-1 2-2z" class="H"></path><path d="M663 520l12-12c-1 2-3 4-4 7-2 1-3 4-5 6-1-1-2-1-3-1z" class="B"></path><path d="M507 762c-1 1-1 3-2 5h0-1l-1-2-1 1v1c-1-1-1-6-1-7l5 1 1 1z" class="E"></path><path d="M611 515c1 0 2 0 3 1h1 1c1 1 1 1 2 1h2l1 1c0 1 0 1 1 2v1s1 0 1 1l2 1c-6-1-10-5-14-8zm-26-229c-1 1-2 2-3 2-2 3-5 5-7 7 1-4 5-9 8-12l1 1v-1l1 1-1 1 1 1z" class="C"></path><path d="M564 360c2 0 3-1 4 0 0 2-1 3-1 5 0 1 0 2-1 2v-1h-5l3-6z" class="O"></path><path d="M758 117l-3-19c1 2 1 4 2 6 2 7 3 12 3 20l-1-1c0-2 0-4-1-6z" class="J"></path><path d="M626 449c4 1 6 0 9 0-2 1-4 3-6 4-3-1-6 0-9 1v-2l6-3z" class="B"></path><path d="M627 551c2 0 2-1 3-2l-1 13-1-2h0-3l2-9z" class="C"></path><path d="M271 122c2-5 5-10 8-15 0 2-2 7-3 9l-3 10-1-1c0-1 0-2-1-3z" class="M"></path><path d="M828 519c2 1 2 1 4 1l3 12h-1c-2-2-2-5-4-8v-1l-2-4z" class="B"></path><path d="M399 277c6 3 9 6 14 11-5-3-11-5-17-7h4 1s0-1 1-1h1c-2 0-3-1-4-2v-1z" class="I"></path><path d="M633 394c1 1 2 2 3 4-3 3-6 5-10 8 2-5 4-8 7-12z" class="H"></path><path d="M233 253c6 0 13 0 19 4l-11-1-1-1h-6l1-1-2-1z" class="J"></path><path d="M698 713h0c5 0 9 1 14 0l-1 2c-7 1-13 0-19-1l1-1h5z" class="C"></path><path d="M808 427c3 3 5 6 9 9 2 1 5 3 7 5-7-3-13-6-18-11 2 0 3 1 5 3h1l-1-1c-1-2-3-3-3-5z" class="K"></path><path d="M757 495c5 4 10 10 13 15l-8-8-9-6h3l1-1z" class="I"></path><path d="M539 356c1-3 2-5 3-7 1-3 2-6 4-9h1v1c0 3-2 6-3 9 0 1-2 4-3 5-1 0-1 1-2 1z" class="U"></path><path d="M535 762l-1 1-2 2c-2-1-3-4-3-6l6-2v1h1c0 2 0 2-1 4z" class="E"></path><path d="M505 251c2 1 2 2 4 2 2 3 2 12 2 16-3-5-4-12-6-18z" class="J"></path><path d="M415 278h1c2 3 4 5 5 8v1h-3c-2-3-5-4-7-6h3 2 1c-1-1-2-2-2-3z" class="K"></path><path d="M578 283l2-2 1 1c-1 1-1 1-1 3h0c-1 2-4 6-6 7-1 1-2 2-3 2 0-1 5-10 7-11z" class="G"></path><path d="M586 493h0c0 1 0 1 1 2 1 2 3 3 4 5 5 4 11 8 15 13-1 0-3-2-4-3l-8-6-9-6 1-5z" class="J"></path><path d="M633 394c2-2 5-5 8-6 1 1 1 1 2 3l-7 7c-1-2-2-3-3-4z" class="N"></path><path d="M602 576v-7-1c-1 1-2 1-2 3l-1-1c1-3 3-4 5-5l1 12h-1l-1 7c0-2 1-5 0-7l-1-1z" class="Q"></path><path d="M591 222l1 1c3 0 4-5 9-5-3 3-5 5-8 9-1 1-1 1-3 2l-1-1c0-3 1-4 2-6h0z" class="K"></path><path d="M762 607h1c1 1 2 1 3 2v1c1 1 2 1 3 2v-3c2 3 3 7 4 11l-1 1c-2-2-3-5-4-7l-1-2-1-1h-2l-2-4z" class="F"></path><path d="M524 761l4-1c1 2 2 4 1 6-1 1-3 1-5 2-1-1-1-2-2-4l2-2v-1z" class="D"></path><path d="M593 638c-2 1-4 4-6 5h-1c0-5 3-8 5-12 1 2 1 3 1 5l1 1h0v1z" class="H"></path><path d="M664 454h1v3c-1 2-3 4-5 5l-10 7h-1l15-15z" class="C"></path><path d="M696 253c-1-1-4-1-5-1-2-1-3-4-4-5h12l-3 6z" class="S"></path><path d="M497 271c2 5 4 10 5 15 1 3 2 6 2 9-2-4-4-8-5-12l-1-1c-1-2-2-4-1-6v-1-4z" class="D"></path><path d="M296 253c5 4 10 7 16 9l-1 1c-2 1-8-2-11-3l-1-1-4-3c1 0 2 1 3 1l4 2-3-2c-1-1-2-2-2-3l-1-1z" class="B"></path><path d="M356 256l1-1c3 0 11 0 13 1h-4c-2 0-3 1-4 1-2 0-4-1-6 0h-4c-5 1-9 3-13 4v-1c3-2 5-3 9-4h8z" class="R"></path><path d="M643 481h1c5 0 12-4 16-6 1-1 5-4 6-4v1c0 1-1 3-2 4-2 1-4 2-6 2-4 2-8 3-13 4h-4c1-1 1-1 2-1z" class="B"></path><path d="M796 455c-7-4-11-9-15-15 5 4 10 8 15 11l1 2-1 2z" class="F"></path><path d="M611 585h1v-8h1c0 8 0 17-2 25-1-4-1-8 0-12h0v-5z" class="B"></path><path d="M218 251c5 2 9 3 15 2l2 1-1 1h6l1 1c-2 0-4 1-6 0s-4 0-6 0c-4 0-9-1-12-4l1-1z" class="Q"></path><path d="M794 430c1 0 1 1 3 1 3 3 5 6 9 8 2 2 5 2 7 3-4 0-11-1-15-5h0c0-2-3-5-4-7z" class="C"></path><path d="M721 711c7-1 13-4 19-7 0 2-3 3-5 4 2 0 2-1 3-1h1v-1h2 0c1-1 2-1 3-1-7 4-15 7-23 9v-3z" class="O"></path><path d="M588 270v1c-1 2-3 4-3 7l1-1 1-1h1l-8 9c0-2 0-2 1-3l-1-1-2 2c2-5 6-9 10-13zm-281 24l26 11c-2 1-5-1-7-2l-10-4c-3-1-6-1-9-2l-1-1v-1c0-1 0-1 1-1z" class="F"></path><path d="M520 373h0v6l1 1c0 3-1 9 1 11v1l-3 11v-2l1-28z" class="G"></path><defs><linearGradient id="k" x1="568.273" y1="219.825" x2="566.423" y2="215.456" xlink:href="#B"><stop offset="0" stop-color="#747475"></stop><stop offset="1" stop-color="#8d8a87"></stop></linearGradient></defs><path fill="url(#k)" d="M569 211v2h3l1-1h1c-1 1-1 2-2 3v-1c-3 5-6 8-10 12l7-15z"></path><path d="M429 169c4 3 7 6 11 10-1 0-2 1-3 1s-1 0-2 1l-6-7 2-1c-1-1-2-2-2-3v-1z" class="G"></path><path d="M421 107l6 6-1 2c2 2 4 3 7 4 1 1 2 1 4 2h-1c-8-1-12-5-17-12l2-2z" class="U"></path><defs><linearGradient id="l" x1="541.172" y1="292.774" x2="539.4" y2="283.599" xlink:href="#B"><stop offset="0" stop-color="#797977"></stop><stop offset="1" stop-color="#949395"></stop></linearGradient></defs><path fill="url(#l)" d="M537 291l1 1v-1-1c1-1 1-1 1-2s1-1 2-2c0-1 1-3 2-4h0v-1h1l-2 13c-2 1-4 0-5 0-1-1-1-1 0-3z"></path><path d="M299 234l3 3c0 3 2 2 2 4 1 1 1 5 0 6-3-2-6-5-9-8 1 0 2-1 3 0h3 0c0-2-1-2-2-4v-1z" class="H"></path><path d="M728 201c1-1 2-3 2-5v5 2h1l-5 11c-1 3-2 6-4 8l-1-2c0-1 0-1 1-2 2-3 5-9 6-13v-4z" class="U"></path><path d="M773 602c1-1 0 0 1-2h0l1-1h0c2 10 2 19 2 29-1-2-1-8-1-10-1-2-1-5-2-7h1c0-3-1-6-2-9z" class="H"></path><path d="M707 645c4 0 7 1 10 4 2 2 4 5 4 9 0 3-1 6-4 8-2 2-4 2-6 2h0c2-1 4-2 6-4 1-1 2-5 2-7-1-4-2-7-5-8-3-3-6-3-10-3h-1c1-1 2-1 4-1z" class="D"></path><path d="M504 236l5 17c-2 0-2-1-4-2l-4-10h0c1 1 1 2 2 3 0-2 0-5 1-7v-1z" class="I"></path><defs><linearGradient id="m" x1="580.274" y1="347.464" x2="578.779" y2="356.707" xlink:href="#B"><stop offset="0" stop-color="#3f3d3c"></stop><stop offset="1" stop-color="#555652"></stop></linearGradient></defs><path fill="url(#m)" d="M574 360h1c3-4 3-9 5-13 2 0 2 0 3 1-1 4-4 8-6 13h1v1c-1 1-2 2-2 3h-1c0-1 0-2 1-3l-1-1-1 1v-1-1z"></path><defs><linearGradient id="n" x1="570.902" y1="345.088" x2="567.477" y2="358.381" xlink:href="#B"><stop offset="0" stop-color="#545251"></stop><stop offset="1" stop-color="#696968"></stop></linearGradient></defs><path fill="url(#n)" d="M564 360l7-15h2c1 2 0 4-1 5l-4 10h0c-1-1-2 0-4 0z"></path><path d="M249 235c2 0 2 0 4 1 4 2 7 4 10 8s4 9 5 13c-3-5-5-10-10-14h0v-1c-1-1-3-3-5-4l-4-3z" class="J"></path><defs><linearGradient id="o" x1="462.894" y1="218.577" x2="453.852" y2="209.673" xlink:href="#B"><stop offset="0" stop-color="#888985"></stop><stop offset="1" stop-color="#a7a7a6"></stop></linearGradient></defs><path fill="url(#o)" d="M456 205h1v1c0 1 1 3 2 4 1 2 2 5 2 7 1 3 2 6 2 9l-10-18 2-2 1 1v-2z"></path><path d="M295 229c1 2 3 3 4 5v1c1 2 2 2 2 4h0-3c-1-1-2 0-3 0-2-3-4-6-6-10l3 2c1 0 2 1 4 2 0-1-1-1-1-2v-2z" class="I"></path><path d="M723 208c2-1 4-6 5-7v4c-1 4-4 10-6 13-1 1-1 1-1 2l-2 2c0-1 0-3 1-4 0-4 1-7 3-10z" class="P"></path><path d="M634 557c0-7 0-17 5-22h0c0 4-1 8-1 12-1 3-1 7-2 10l-1-2c1-1 1-1 1-2h-1l-1 4z" class="C"></path><path d="M619 555c0-1 0-3 1-4 1-3 3-5 5-6 2 0 3-1 5 0v4c-1 1-1 2-3 2v-2c0-1 0-1-1-1s-2 0-3 1-2 2-1 3v1 9h0c-2-2-2-4-2-6l-1-1z" class="O"></path><path d="M690 525h1c12-4 24-6 37-6l-4 2c-7-1-16 1-23 3-2 1-5 3-7 3v-1c1 0 3-2 4-2s2 0 3-1c-2 0-5 1-7 2-1 0-1 0-2 1-1 0-2 0-3-1h1z" class="D"></path><path d="M537 291c1-8 6-18 11-25l-4 15h-1v1h0c-1 1-2 3-2 4-1 1-2 1-2 2s0 1-1 2v1 1l-1-1z" class="C"></path><path d="M418 116l11 16-1 1c-2 0-4 0-6-2-1-2-3-5-3-7v-2-1c0-2 0-3-1-5z" class="D"></path><defs><linearGradient id="p" x1="585.808" y1="349.437" x2="582.782" y2="361.215" xlink:href="#B"><stop offset="0" stop-color="#5b5a58"></stop><stop offset="1" stop-color="#747574"></stop></linearGradient></defs><path fill="url(#p)" d="M578 361l9-12c1 0 2 1 3 1v1c-2 3-4 8-7 11-2-1-4 0-5 0v-1z"></path><path d="M588 248c0 1-2 4-3 5-1 2-2 4-3 7-1 2-3 4-4 7-1 4-4 9-7 13 5-12 10-25 16-37v1c0 2 0 2-1 4h1v-1l1 1z" class="D"></path><path d="M707 305l16-6c1 1 1 1 2 1l1-1h2l1 1c-1 1-2 1-3 1l-3 1v1c-5 3-11 3-17 4h0c0-1 1-1 1-2z" class="K"></path><path d="M868 524c1 2 4 6 6 6 3 2 5 5 8 7s7 5 10 7c-5-1-9-3-14-6 0 0-2 0-2-1s-1-3-2-4c-2-3-5-5-6-9z" class="G"></path><path d="M303 202c5 9 9 18 13 27l-1 1c-5-4-7-13-9-18-1-3-2-6-4-9l1-1zm339 87h0c2-2 17-2 20-2 1 0 2 1 2 1 2 0 4-1 6 0h4c7 0 13 3 19 6-2 0-5-1-7-2-3-1-7-2-11-3-10-1-22 0-33 0z" class="S"></path><defs><linearGradient id="q" x1="586.209" y1="654.004" x2="591.222" y2="646.012" xlink:href="#B"><stop offset="0" stop-color="#999897"></stop><stop offset="1" stop-color="#bdbcba"></stop></linearGradient></defs><path fill="url(#q)" d="M590 644c1 0 1 1 2 1s1 0 2 1v-2h0v-1c1 0 2-1 3-1h0c0 1-1 2-1 3s-1 2-2 4v-1l-1 1c-1 3-3 6-6 7 0 0-1 0-1-1 0-5 1-8 4-11z"></path><defs><linearGradient id="r" x1="578.164" y1="347.695" x2="570.074" y2="361.245" xlink:href="#B"><stop offset="0" stop-color="#595857"></stop><stop offset="1" stop-color="#727271"></stop></linearGradient></defs><path fill="url(#r)" d="M568 363c1-1 2-3 3-5 2-4 3-9 6-12h1c1 3-3 11-4 14v1 1c0 1-1 3-2 4h-1l1-2c-1 0-2-1-3-1h0-1z"></path><path d="M608 571c0-2 0-4 1-6l1 4c-1 2-1 4-1 5 1 2 1 4 2 6v5 5h0c-2 2-2 4-3 7h0v-4l-1-1 1-16v-5z" class="D"></path><path d="M608 571c0-2 0-4 1-6l1 4c-1 2-1 4-1 5v8 3c-1-1-1-1-1-2v-7-5z" class="L"></path><path d="M870 494c-2-4-6-10-11-12l-4-1c2-1 5-1 7 0 5 2 10 7 12 11-1 2-2 2-4 2z" class="K"></path><path d="M723 707h-2c-5 0-13-2-17-5 11 2 19 3 30 0l-1 1h1c-2 2-4 2-6 3l-5 1z" class="C"></path><path d="M622 562v-9-1c-1-1 0-2 1-3s2-1 3-1 1 0 1 1v2l-2 9-2 10c-1-2 0-4 0-5-1-1-1-2-1-3z"></path><path d="M523 225l3-7c0-2 1-3 2-5 0 2 0 3-1 4-2 5-4 10-5 15 0 1 0 2 1 3-2 9-6 16-6 25h-1c0-5 0-9 1-14v-7l1 1h1c2-4 3-10 4-15z" class="H"></path><path d="M580 797v1c-2 7-7 14-10 21-1 4-1 9-3 12-1 1-1 1-2 1l-1-1c1-9 6-16 10-23 3-3 4-7 6-11z" class="J"></path><path d="M585 258l1 2c-2 2-2 3-2 6h1l2-1h0l-7 7v1c-2 1-7 8-9 10 0-2 3-6 5-8 1-2 2-4 2-6 2-5 4-8 7-11z" class="I"></path><path d="M302 184c3 5 5 9 8 13l8 11c1 1 1 3 1 4h-1c-9-8-12-17-16-28z" class="M"></path><path d="M695 850c0-2 2-4 3-6 3-6 5-12 8-17 2-2 4-4 5-6-2 7-4 14-9 20-1 2-4 5-5 7v1c-1 0-1 1-2 1z" class="J"></path><path d="M297 196c1 0 1 1 2 1v-3c0 1 0 1 1 2l3 6-1 1-1-2c0 2 1 5 2 6 1 3 2 5 2 7 2 4 3 7 5 10v2c-2-3-3-6-5-9l-5-8-1-2v-3c0-1-1-3-1-4-1-2-1-3-1-4z" class="P"></path><path d="M271 122c1 1 1 2 1 3l1 1c-1 6-2 11-2 17 0 4 0 7-1 11-1-5-3-19-2-24 1 1 1 0 1 1 1-3 1-6 2-9z" class="S"></path><path d="M587 698l8-5c4-2 8-4 11-7v-1h-1c3-7 12-12 18-15-4 4-10 7-13 11-1 1-1 4-2 5-1 2-2 3-3 4-5 3-11 6-17 9l-1-1z" class="B"></path><path d="M809 251v2c-4 2-8 2-12 2-2 1-5 0-6 1-2 1-5 0-7 0-3 0-6 1-9 1 3-2 6-3 10-4h10c5 1 9 0 14-2z" class="N"></path><path d="M675 225c5 2 10 5 13 8l3 2c-1 1-1 1-2 1-3 0-6-1-8-4-2-2-5-2-7-3l-6-3c2-1 4 0 7 0v-1z" class="U"></path><defs><linearGradient id="s" x1="595.366" y1="163.206" x2="591.27" y2="170.494" xlink:href="#B"><stop offset="0" stop-color="#6e6d6c"></stop><stop offset="1" stop-color="#848382"></stop></linearGradient></defs><path fill="url(#s)" d="M587 169c5-4 9-8 15-11-2 6-4 12-9 15 0-1 0-1 1-2l-7-2z"></path><path d="M609 565c1-1 0-3 1-4 0-1 1-1 1-1 2 1 3 3 3 5v2l-1 3v4 3h-1v8h-1v-5c-1-2-1-4-2-6 0-1 0-3 1-5l-1-4z" class="R"></path><path d="M610 569v5l1-10h1c0 3 0 7 1 10v3h-1v8h-1v-5c-1-2-1-4-2-6 0-1 0-3 1-5z" class="Q"></path><defs><linearGradient id="t" x1="597.487" y1="468.428" x2="608.513" y2="456.572" xlink:href="#B"><stop offset="0" stop-color="#8a8a82"></stop><stop offset="1" stop-color="#cecdd5"></stop></linearGradient></defs><path fill="url(#t)" d="M620 465c-12 1-23-1-34-4h0c6-2 16 1 22 2h10l2 2z"></path><defs><linearGradient id="u" x1="651.401" y1="483.535" x2="667.758" y2="479.562" xlink:href="#B"><stop offset="0" stop-color="#908f8d"></stop><stop offset="1" stop-color="#b1b0af"></stop></linearGradient></defs><path fill="url(#u)" d="M670 475l1 1c-4 7-15 11-22 12h0c3-3 6-5 10-7l7-5c1 0 1 1 2 1l2-2z"></path><defs><linearGradient id="v" x1="720.771" y1="219.619" x2="711.229" y2="224.881" xlink:href="#B"><stop offset="0" stop-color="#656464"></stop><stop offset="1" stop-color="#81807e"></stop></linearGradient></defs><path fill="url(#v)" d="M726 197c0 4-2 7-3 11-2 3-3 6-3 10-1 1-1 3-1 4v1h-1c-1 1-1 2-1 4v1c-2 1-3 3-4 3-1-2 1-5 1-7l6-13c0-2 2-4 2-6 2-2 3-5 4-8z"></path><path d="M818 488l2 1v-1-1c2 1 5 3 8 4 1 1 3 3 5 4l1-1c2 3 3 6 3 9-1 1-1 1-3 0-3-8-9-10-15-14h0l-1-1z" class="K"></path><defs><linearGradient id="w" x1="496.969" y1="350.772" x2="511.531" y2="364.728" xlink:href="#B"><stop offset="0" stop-color="#5a5b58"></stop><stop offset="1" stop-color="#9b9a9b"></stop></linearGradient></defs><path fill="url(#w)" d="M503 344c3 10 5 22 4 32h0l-5-29c0-1 1-2 1-3z"></path><path d="M734 639s1 0 1 1c4 3 6 11 6 15 0 8-2 16-7 22 1-3 3-6 4-9v-1-10c-1-5-3-10-5-14v-1c3 3 5 8 6 13 0 1 0 3 1 5v-1c1-7-2-15-6-20z" class="O"></path><path d="M468 207c5 7 7 14 8 22-3-3-6-7-7-11-1-1-1-2-1-3s0-2-1-4c0-2 1-3 1-4z" class="U"></path><defs><linearGradient id="x" x1="325.704" y1="307.099" x2="322.796" y2="315.901" xlink:href="#B"><stop offset="0" stop-color="#cbcac7"></stop><stop offset="1" stop-color="#edecf1"></stop></linearGradient></defs><path fill="url(#x)" d="M319 308c7 0 15 1 22 3l-28 3-3-1c3-1 10-2 12-3v-1c-1 1-1 1-2 1h-1v-2z"></path><path d="M707 301c4-2 8-4 12-7 1 2 1 3 1 4 3-1 6-1 9-1l-6 2-16 6h0c-1-1 0-1-1-1 0-1 1-2 1-3z" class="R"></path><path d="M707 301c4-2 8-4 12-7 1 2 1 3 1 4-4 1-8 2-12 4-1 0-1 0-1-1zm-18 405c-3-2-5-4-7-7 9 6 17 13 28 12h1c2 0 3 0 5 1h-1l-3 1c-5 1-9 0-14 0h0c-2-1-3-1-4-2h2c-2-2-5-3-7-5z" class="F"></path><path d="M285 284c12-2 26-2 37 3-1 1-2 1-4 1-3 0-7-1-10-2-8-1-15-1-22 0l-1-2z" class="D"></path><path d="M509 452c-1-1-2-5-2-6l-4-12c-2-5-2-10-2-15l9 27v3 1 2h-1 0z" class="C"></path><path d="M506 761c5 1 9 1 13 1 1 1 1 4 1 5l-1 1v1h-1-1l-1-5c0 2 0 3-1 5h-1l-1-1c-1 1-1 1-2 1v-1c0-1 1-2 1-3-1 1-1 2-3 3v-1c-1 0-1 0-2 1-1-1 0-4 0-6h0l-1-1z" class="D"></path><path d="M718 229v1h0c-1 2-2 3-2 5-3 3-8 7-11 9-4 1-8 1-12 2 1-1 0-1 1-1 10-4 16-9 24-16z" class="N"></path><path d="M532 373h4l1 2h0c-2 2-4 4-5 7-1 1-3 6-3 6-3 1-2 4-5 5 1-6 3-12 6-17l2-3z" class="K"></path><path d="M532 260l1 3c-3 6-6 12-8 19l2 2-1 4-2-1c0 2-1 5-2 6-2-2 5-20 4-25l1-1v-1h1 1l1-1v-1c1-2 2-3 2-4z" class="B"></path><path d="M525 282l2 2-1 4-2-1 1-5z" class="R"></path><path d="M445 261c2 3 3 5 5 9 1 3 3 6 4 10 0 2 1 4 2 5-2-2-3-4-5-6 0-2-3-4-4-5l-2-3c-1-1-1-2-2-2 0-1-1-1-1-2l1-1h1c0-2 0-4 1-5z" class="H"></path><path d="M445 261c2 3 3 5 5 9l-2 2c-2-1-2-2-3-3l-2-3h1c0-2 0-4 1-5z" class="I"></path><path d="M301 319c13-3 25-5 39-5l-1 2c-3 1-9-1-11 1h-2c-3 1-5 1-8 1-2 0-3 0-5 1h-1-2c-3 1-5 1-8 0h-1zm317 144c14-2 24-8 32-19 4-5 5-11 7-16 1 4-3 12-5 16-6 10-18 18-30 20l-2 1-2-2z" class="F"></path><path d="M722 553c-3 1-5 0-8-1-7 0-13 1-20 2 4-2 9-4 13-6l1 1c6 0 12 0 17 1v1h1c0 2 0 2-1 3v-1h-3 0z" class="O"></path><path d="M524 372c2 0 4-1 6-1 1 1 0 3 0 4l-8 17v-1c-2-2-1-8-1-11l1-2 2-4v-2z" class="C"></path><path d="M629 453c-2 1-3 1-5 2-6 2-15 2-21-2-2-1-3-2-3-4l9 3h0 11v2c3-1 6-2 9-1z" class="O"></path><path d="M620 452v2h-1c-4 0-7 0-10-2h11z" class="G"></path><path d="M703 356c9 4 16 10 24 17-8-4-16-7-24-11v-6z" class="E"></path><path d="M758 493c3 0 3 1 5 3 0-1 0-1 1-1v-1c2 1 5 3 6 5 1 0 1 0 1 1l2 1c1 2 3 4 5 6 3 4 5 9 7 14-8-11-17-20-27-28z" class="L"></path><path d="M656 639c2-1 5-1 8 0 6 2 9 5 12 10-4-3-6-5-11-6h-1c-8-2-17 0-25 3 4-3 7-5 11-6 2-1 4-1 6-1z" class="E"></path><path d="M524 393c3-1 2-4 5-5l-3 12v2h0l-5 14c-1-1-1-6-1-8 0-5 2-11 4-15z" class="H"></path><path d="M515 213c1 3 1 6 2 9v1h0v-2-1-1c0-1 0-1 1-2 1-2 0-4 0-6 1-1 1-4 1-6s0-3 2-5c0 3-1 7-1 10-1 3 0 5-1 7v3l-1 1v1c-1 2 0 6-1 8-1 7 0 15-2 22h-1c1-5 2-21 0-25 1-1 1-2 1-3l-1-1c0-3 0-7 1-10z" class="B"></path><defs><linearGradient id="y" x1="489.744" y1="292.591" x2="480.22" y2="275.253" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#969695"></stop></linearGradient></defs><path fill="url(#y)" d="M478 263l3 3c-1 1 1 3 1 4 2 4 4 8 5 12 2 4 2 8 3 12v1l-3-1c-1-1-1-1-1-2-2-6-3-11-4-17-1-4-3-8-4-12z"></path><path d="M526 519h0l14-27c1-2 2-5 4-7-3 11-7 23-15 32-1 2-3 5-5 6v-1c0-1 1-2 2-3z" class="Q"></path><path d="M676 219v-1c4 0 7-2 10-4 5-2 10-5 15-9-2 5-9 9-13 11l-10 3c4 1 7 2 11 2-2 1-6 3-8 3-3 0-7-1-10-2h0c0-1 1-1 2-2l-2-1h5z" class="M"></path><defs><linearGradient id="z" x1="553.194" y1="225.703" x2="559.26" y2="204.99" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#91908d"></stop></linearGradient></defs><path fill="url(#z)" d="M567 198v1c-1 1-2 3-3 5v1l-1 1c-1 1-2 4-3 5-3 6-5 12-8 18v-9c2-9 8-15 15-22z"></path><path d="M295 162c1 3 1 6 2 10 2 8 3 17 7 26 1 4 4 9 7 12 2 3 5 5 7 8s3 8 5 11c-3-3-6-7-8-11-4-6-9-11-12-17-3-8-5-17-6-25-1-5-2-10-2-14z" class="M"></path><path d="M629 438l-1 4c-1 3-5 5-8 7-3 1-6 1-10 0-2-1-4-2-5-4l1-1c1 0 2-1 3 0 2 1 5 5 8 5 2-1 4-3 5-4 2-4 5-10 4-13v-1h0 1l1 1 1 6z" class="E"></path><path d="M757 139h0c-1-11-5-21-9-32l1 1 9 18v-9c1 2 1 4 1 6l1 1h0l-1 18h0l-1-1h0l-1-2z" class="S"></path><path d="M574 362l1-1 1 1c-1 1-1 2-1 3h1c0-1 1-2 2-3 1 0 3-1 5 0l-8 10c-1-1-2-1-2-2h-1c-2 0-3 0-4-1h-2l-1 1-1 1v-2c-1-1-2-1-3-1l-1 1-1-1 1-2h1 5v1c1 0 1-1 1-2l1-2h1 0c1 0 2 1 3 1l-1 2h1c1-1 2-3 2-4z" class="J"></path><defs><linearGradient id="AA" x1="563.764" y1="217.113" x2="560.736" y2="209.387" xlink:href="#B"><stop offset="0" stop-color="#79787a"></stop><stop offset="1" stop-color="#8d8d88"></stop></linearGradient></defs><path fill="url(#AA)" d="M567 198c1-1 2-1 3-2v1c0 3-1 6-1 9-2 6-6 12-9 18 1-6 1-12 3-18l1-1v-1c1-2 2-4 3-5v-1z"></path><path d="M567 198c1-1 2-1 3-2v1h0l-3 6h0c-1 3 0 8-3 10 0-4 1-7 0-9 1-2 2-4 3-5v-1z" class="S"></path><path d="M591 590l3-15c1 7 1 15 1 22-1 2 0 4-1 6l-3 12c-2-2 0-7 0-10v-1-10-4z" class="F"></path><path d="M595 597h1c0 2 0 4-1 6-1 4-2 9-4 12l1 1c1 3 1 5 1 8h0c0-1 1-1 2-2 0 2 0 3-1 4l-1 1h0l-4 5c0 1-2 3-3 3v-12h1l2-2 2-6 3-12c1-2 0-4 1-6z" class="C"></path><path d="M589 632c-1-4 0-8 1-12 0 3 0 5 1 7h2 0l-4 5z" class="K"></path><path d="M591 615l1 1c1 3 1 5 1 8h0c0-1 1-1 2-2 0 2 0 3-1 4l-1 1h-2c-1-2-1-4-1-7l1-5z" class="F"></path><path d="M537 375c1 2 1 4 2 6l-13 21v-2l3-12s2-5 3-6c1-3 3-5 5-7z" class="E"></path><path d="M489 252l1-1-1-1v-1c3 3 5 7 8 10h0v-1-1c2 3 3 6 4 9h1c0 1 1 2 0 3l6 24c-2-2-2-6-3-8-3-12-8-22-16-33z" class="B"></path><path d="M507 376c2 7 1 16 2 23 0 4 1 8 1 12l-5-22c0-3-3-7-3-10-1-4-1-10 0-15l5 18v1-6-1h0z" class="N"></path><path d="M724 184h1c-2 7-5 17-9 22-2 3-5 5-7 8h-1 0c0-2 1-4 1-6 2-4 5-7 8-11 2-4 4-9 7-13z" class="M"></path><path d="M311 304l1-1-1-1h0 1l1 1h1c2 1 8 3 10 2 2 0 5 1 7 2-7 0-14-1-21 1l-11 2h-4-1-1c0-1 0-1 1-2 1 0 3-1 4-1 4-1 8-3 12-2h4 0l-3-1z" class="F"></path><path d="M542 751h0c1-1 2-2 3-2 2-2 7-6 8-8 1-1 1-2 2-3l1 1v1h0 1c-2 4-7 11-11 13l-1 1h-1c-1 2 0 3-2 4-1 1-2 1-4 1 0-1-2-1-2-1h-1v-1c3-2 5-3 7-6z" class="D"></path><path d="M573 252c2-6 5-13 11-16v2l-2 2h0c-2 2-6 9-5 13-1 1-1 1-1 2-3 4-4 8-5 12-2-1 0-2-1-4h-1-1l5-11z" class="O"></path><path d="M573 252c2-6 5-13 11-16v2l-2 2h0c-1 0-2 0-3 2v1c0 1-1 2-1 3v1c-1 1-2 1-2 3 0 1-1 3-3 4v-2z" class="Q"></path><path d="M640 623l1 1 1-1v1l-6 7v1c3-3 6-6 10-9h1l-9 9c-1 2-2 3-4 4v1c0 1-1 1-1 2 0 3-6 8-7 9 1-2 3-5 3-7v-1c-1 3-3 5-5 8l6-12v-2c2-4 7-8 10-11z" class="J"></path><path d="M512 452c0-2-1-4-1-7-2-8-6-16-8-25-1-6-2-11-2-17 1 2 2 5 2 8l4 13c2 5 4 9 4 14 0 2 0 3 1 4 0 1 1 1 1 1v1 11c-1-1-1-1-1-3z" class="G"></path><path d="M744 286c-11-4-21-2-32 1-2 1-3 1-5 0v-1c9-3 18-4 28-4h10l5 1h1 1 0c1 1 2 1 2 1-1 1-3 1-4 1v1h-2v1c-2-1-2-2-4-1z" class="D"></path><path d="M756 677c1 0 2-1 3-1l-1 1v1c0 1-2 2-3 4-10 11-23 18-38 19 1-1 6-2 7-3 13-4 23-11 32-21z" class="E"></path><path d="M642 289h0l-9 3-9 3c3-2 8-5 11-6 2-1 5-1 7-2 2-2 4-2 7-2 6-1 14-1 20 0h1c1 1 2 2 4 3h-4c-2-1-4 0-6 0 0 0-1-1-2-1-3 0-18 0-20 2h0z" class="M"></path><path d="M657 772h1c12 6 21 15 27 27 7 13 7 27 2 40v1c1-4 2-7 2-10 1-6 1-13 0-18-4-17-18-32-32-40z" class="C"></path><defs><linearGradient id="AB" x1="598.781" y1="483.276" x2="610.955" y2="491.89" xlink:href="#B"><stop offset="0" stop-color="#8b8888"></stop><stop offset="1" stop-color="#bebfbd"></stop></linearGradient></defs><path fill="url(#AB)" d="M610 491c-9-2-17-4-24-9l30 6-2 3 1 1h0l1 1h0l-2-1c-1 0-1 0-2-1l-1 1-1-1z"></path><defs><linearGradient id="AC" x1="516.391" y1="341.461" x2="527.637" y2="360.863" xlink:href="#B"><stop offset="0" stop-color="#504f4f"></stop><stop offset="1" stop-color="#777776"></stop></linearGradient></defs><path fill="url(#AC)" d="M519 369l1-24c0-2 0-5 1-7h4c1 1 0 2 0 4-1 5-2 11-2 17h0v2 1c0 1 0 1-1 2-1 2-1 3-3 5z"></path><path d="M370 283c6 0 12 1 19 3 3 0 5 0 8 1 6 1 12 5 18 8-6-2-11-5-17-7-5-2-11-2-17-3-6 0-13-1-19 0-5 1-9 3-14 4l-14 4s1 0 1-1c2-1 4-2 7-3 9-4 18-5 28-6z" class="U"></path><path d="M655 497c6-2 12-1 18-1h21l-14 4c-3-1-6 1-8 1-4 1-13 2-17 0h3 7l11-2h-10l-11-2z" class="N"></path><path d="M528 213h-1l1-1c0-1 0-2 1-3 1-3 10-20 11-21 1 1 1 1 1 2l-1 2 1-1 3-4v-1h1c-10 15-18 32-22 49-1-1-1-2-1-3 1-5 3-10 5-15 1-1 1-2 1-4z" class="D"></path><path d="M825 676c1 3 1 5 1 7l1 1c-1 3-3 7-5 10-5 11-12 22-19 32h-1l8-13c6-12 12-24 15-37z" class="C"></path><path d="M838 615v5c0 15-1 31-5 46l-1-2v-3c1-1 0-2 0-3 1-3 1-5 1-8v-3h1l-1-1c0-4 1-9 2-13 1-6 1-12 3-18z" class="E"></path><defs><linearGradient id="AD" x1="719.243" y1="217.663" x2="739.811" y2="215.995" xlink:href="#B"><stop offset="0" stop-color="#7c7b77"></stop><stop offset="1" stop-color="#a09f9f"></stop></linearGradient></defs><path fill="url(#AD)" d="M742 199v2c0 2-1 3-2 5 0 2-2 5-3 7l-2 3-11 12c-2 0-3 0-5 1l-1-1c3-4 8-9 11-13h3c1-1 3-4 4-6 2-3 4-6 6-10z"></path><path d="M529 254h2 0l1 1 1 1h-1c0 1 0 2-1 3-1 2-2 5-3 7h-1v1l-1 1-8 25v1-4c1-2 2-3 2-5 1-3 1-5 1-7-1 3-2 7-3 10 0-4 1-8 3-12 2-8 5-15 8-22z" class="D"></path><path d="M652 282c7 0 14 1 21 2l12 3c2 1 4 1 5 2h-1l-15-3c-2 0-3-1-5-2-5-1-14-1-19 0h-1c-6 1-13 3-19 5-5 2-11 5-16 6 10-8 25-12 38-13z" class="U"></path><defs><linearGradient id="AE" x1="728.73" y1="292.059" x2="726.829" y2="287.349" xlink:href="#B"><stop offset="0" stop-color="#c1bebf"></stop><stop offset="1" stop-color="#e2e1e1"></stop></linearGradient></defs><path fill="url(#AE)" d="M744 286c2-1 2 0 4 1 1 0 2 1 3 1-1 1-1 1-1 2h-2c-11-2-22 0-33 5-2 1-5 3-7 4v-2c8-6 15-10 25-11h11 0z"></path><path d="M895 396v3h0v-2h1v7c-1 14-6 27-16 38l-1-2c11-12 13-28 16-44z" class="L"></path><path d="M634 590h1c1-1 1-2 2-3l1-5c1-2 1-3 2-4h0c0 2 0 4-1 5-1 2-1 4-2 6v3c-1 2-1 5-1 7h1c-5 9-11 18-14 27h0c1-6 3-11 4-16 3-6 4-13 7-20z" class="H"></path><defs><linearGradient id="AF" x1="753.492" y1="542.418" x2="763.508" y2="564.082" xlink:href="#B"><stop offset="0" stop-color="#757273"></stop><stop offset="1" stop-color="#878985"></stop></linearGradient></defs><path fill="url(#AF)" d="M767 558h1c-11-7-25-10-37-11 11-3 28 2 38 7l8 5-5 1-5-2z"></path><path d="M619 391c2 1 3 1 5 1h1l1 1c0 2-2 3-3 5-4 5-9 10-14 15 2-6 3-12 6-17l4-5z" class="T"></path><path d="M703 348h2c13 8 29 20 38 33h-1c-7-8-16-16-25-21-4-3-9-6-13-9l-1-3z" class="E"></path><path d="M511 438h1c1-3-1-9-2-13l-4-14c-2-5-4-10-4-14-1-5-1-10-1-15 3 7 5 15 6 23l3 16c0 3 2 6 2 10 1 4 1 8 1 12 0 0-1 0-1-1-1-1-1-2-1-4z" class="N"></path><defs><linearGradient id="AG" x1="588.88" y1="501.344" x2="599.303" y2="519.475" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#bdbdbb"></stop></linearGradient></defs><path fill="url(#AG)" d="M601 520c-5-4-10-7-14-12-2-1-2-5-2-7 4 2 8 5 12 8 2 2 5 4 8 6v2l3 2-5-2-1 1c-1 1 0 2 1 3l-2-1z"></path><path d="M714 495c8-1 17 1 25 3-13 0-25 0-37 3-7 1-13 3-18 6l-1-1c10-6 20-9 31-11z" class="N"></path><path d="M356 291c-4 1-8 3-12 3 1 0 2-1 3-1 4-2 10-4 15-5h12c1 0 3 0 5-1-6 0-13-1-18 0-2 1-3 1-5 2-2 0-5 1-7 1l1-1c2-1 3-1 5-1 2-1 4-2 5-2 4-1 9 0 13 0h11c5 2 9 3 14 5 2 1 4 1 5 3-11-4-22-5-34-5-3 0-7 0-11 1v1h-2z" class="M"></path><path d="M741 367c-1-2-2-4-4-6s-4-4-7-6h1l18 12c4 2 8 5 11 8h-3c-1 0-2-1-3-2-1 0-2 0-2 1 0 2 2 4 3 5s1 2 2 2l2 4v1l-1-1c-2-3-5-6-7-9-2-1-3-3-5-4-1-2-3-4-5-5z" class="N"></path><path d="M446 185h1l1 1v-1l-1-2h1l2 2v-1l-1-1h1c3 6 7 12 9 18 2 4 3 9 5 13 0 1-1 1-2 2l-5-11h-1l-2-3c0-1-1-2-1-3l1-1c0-2-3-5-4-7-1-1-1-1-1-2-1-1-1-1-1-2l-1-1-1-1z" class="G"></path><path d="M594 190c3-4 7-9 12-11l-14 27h-1l-1 2c0 1 0 0-1 1 0-2 2-5 3-7-2 3-4 5-5 7h-1c0-1 1-3 1-4 1-3 3-6 4-8h1c2-2 2-4 2-7z" class="B"></path><defs><linearGradient id="AH" x1="586.045" y1="464.823" x2="600.753" y2="472.946" xlink:href="#B"><stop offset="0" stop-color="#898887"></stop><stop offset="1" stop-color="#a2a2a0"></stop></linearGradient></defs><path fill="url(#AH)" d="M586 471v-8l16 3c-2 1-2 1-2 3s0 3 2 3l5 2h0 0c-2 0-3 0-4 1h-1 0c-1 0-1-1-2-1h0-2c-1-1-1-1-2-1h-3c-1-1-1-1-2-1h-1c-2 0-3 0-4-1z"></path><path d="M711 711c-7-2-13-5-18-10-2-2-6-7-6-9 3 4 6 7 10 10 7 6 16 7 26 6 1 0 2 0 2-1l17-4c-1 1-1 1-2 1-6 3-12 6-19 7l-4 1h-1c-2-1-3-1-5-1z" class="D"></path><path d="M720 211l-16 20c1-5 4-11 7-15 3-3 6-6 8-9 6-8 8-20 10-29 1-6 3-13 4-19h0 0c-1 12-5 25-7 37v1c-1 3-2 6-4 8 0 2-2 4-2 6z" class="M"></path><path d="M323 254h13 0c-1 3-1 6-3 8-2 0-12 0-13-1-2-1-5-2-7-4h0 4c2 0 4-1 6-3zm248 13l-1 1-3 6c-2 4-4 9-6 13-1 3-1 6-4 7h-1l13-46c1-2 2-5 4-6v1c-5 11-8 22-11 33-1 4-3 8-3 13h0l9-26h1 1c1 2-1 3 1 4z" class="S"></path><path d="M831 642l2 1v3l1 1h-1v3c0 3 0 5-1 8 0 1 1 2 0 3v3l1 2-6 18-1-1c0-2 0-4-1-7 2-2 2-6 3-9l3-25z" class="B"></path><defs><linearGradient id="AI" x1="529.728" y1="342.352" x2="519.858" y2="376.816" xlink:href="#B"><stop offset="0" stop-color="#494847"></stop><stop offset="1" stop-color="#a6a5a4"></stop></linearGradient></defs><path fill="url(#AI)" d="M523 359c2-4 3-9 4-13 1-3 1-7 3-9 1 4 0 8-1 12-1 7-2 13-5 20 0 1-1 4 0 5l-2 4-1 2-1-1v-6h0l-1-4c2-2 2-3 3-5 1-1 1-1 1-2v-1-2z"></path><path d="M708 419l-1-1h0c-1 1-2 1-4 1v-3c10-3 22-2 31 0 3 1 6 1 9 3h-1c-1 0-2 0-3-1-2 0-3-1-5 0h0c1 0 2 0 3 1h1 1l2 1v1h1v1c-1-1-3-1-5-2-2 0-4 0-5-1-8-2-16 0-24 0z" class="C"></path><path d="M661 609h1-1c-1 2-4 2-5 4 1 2 1 2 1 4 1-1 3-2 5-3h1 1c-13 6-22 15-31 25 0-1 1-1 1-2v-1c2-1 3-2 4-4l9-9h-1c-4 3-7 6-10 9v-1l6-7v-1l-1 1-1-1c7-5 13-10 21-14z" class="B"></path><path d="M895 396c0-9-1-16-3-25 3 4 4 9 5 14 1 8 2 15 4 23l-1-1-1 1v1c-1 1-1 3-1 5l-1 2h0c-2 7-3 13-7 20h-1c4-7 10-25 7-32v-7h-1v2h0v-3z" class="F"></path><path d="M701 479c-6 1-12 2-18 4s-11 6-16 9l-1-1 16-10c-3 1-6 2-8 3l-12 6 13-9c7-4 13-4 21-5 2 0 5 0 8 1-1 1-2 1-3 2z" class="K"></path><path d="M586 623v-24c0-4-2-11 0-14h1c-1 2-1 4-1 6 0 3 1 4 0 6v1h1c1-3 2-6 4-8v4 10 1c0 3-2 8 0 10l-2 6-2 2h-1z" class="G"></path><path d="M591 594v10 1l-1 2h-1v-1l2-12z" class="B"></path><path d="M589 606v1h1l1-2c0 3-2 8 0 10l-2 6c-1-2-1-4-1-6 1-3 0-6 1-9z" class="K"></path><path d="M591 233h1c-4 7-8 14-11 21-2 5-5 9-6 13s-3 7-4 10l-2 3h0c1-6 6-10 6-16 0-1 0-1 1-2v-1-1c-2 3-2 7-4 10 0-2 1-4 2-7 1-2 1-4 2-6 2-5 5-11 8-16-1 0-2 2-3 4l-4 8c-1-4 3-11 5-13h0l2-2v-2l4-2c2 0 2 0 3-1z" class="L"></path><defs><linearGradient id="AJ" x1="307.839" y1="228.588" x2="296.161" y2="233.412" xlink:href="#B"><stop offset="0" stop-color="#c2c1bf"></stop><stop offset="1" stop-color="#fdfcfd"></stop></linearGradient></defs><path fill="url(#AJ)" d="M288 213c2 3 3 6 6 7 6 11 15 21 26 26-1 1-4 0-5 0l-1-1c-1 0-1 0-2-1l-5-3c-2-1-3-2-5-4l-3-3c-1-2-3-3-4-5h0l-2-2-2-4-1-2c0-1 0-2-1-3-1-2-1-3-1-5z"></path><path d="M718 483v-1h-2-2-3c-2 0-2 0-3-1 0 0 1 1 2 1 1-1 1-1 2-1 2 1 4 0 6 1 3 0 7-1 11 0 2 0 5 0 7 1h1c1 0 2 1 3 1h0l17 11-1 1h-3c-1 0-3-2-4-2-3-2-7-4-10-5-7-3-13-5-21-6z" class="D"></path><path d="M299 207c-10-19-13-38-14-59 1 2 1 4 1 5l2 12 5 19c2 4 3 8 4 12 0 1 0 2 1 4 0 1 1 3 1 4v3z" class="M"></path><path d="M493 870h1c7 21 10 42 5 64v1h-1c-1-3 0-7 0-11 1-5 1-10 1-14 0-14-3-26-6-40z" class="J"></path><path d="M584 197c1 0 2-1 3-2 0 2 0 3-1 4l-1 1c-2 2-3 3-3 5-1 1-1 1-1 2l-1 1c0 2-1 3-2 4-1 2-2 3-2 4-1 1-2 3-2 3h-1c-1 1-1 1-2 1 1-2 1-3 1-5 1-1 1-2 2-3h-1l-1 1h-3v-2c0-1 1-4 1-4l2-5c1 2 1 3 2 5l2-4c1-1 1-2 2-3v1 2c-1 1-1 1-1 2 1 0 3-4 4-5l3-3z" class="G"></path><path d="M569 211c0-1 1-4 1-4l2 1 1 1c1 0 1 0 2-1v1l-1 3h-1l-1 1h-3v-2z" class="C"></path><defs><linearGradient id="AK" x1="444.062" y1="166.155" x2="441.438" y2="183.345" xlink:href="#B"><stop offset="0" stop-color="#848585"></stop><stop offset="1" stop-color="#b2b0af"></stop></linearGradient></defs><path fill="url(#AK)" d="M436 163c5 6 11 13 14 20h-1l1 1v1l-2-2h-1l1 2v1l-1-1h-1 0v-1l-11-11-4-3-1-2 1-1c2 0 3 0 4 1h1 0v-1l-1-1 1-3z"></path><path d="M431 170c2-1 3-1 4-1 2 1 3 2 4 3h0c-2 0-3 0-4-2v3l-4-3z" class="Q"></path><path d="M435 173v-3c1 2 2 2 4 2h0l5 5-1 1c1 1 2 3 3 4v2l-11-11zm170 342c10 5 18 14 27 22 0 1-1 1-2 1l-5 2-2-2-12-16-3-3-3-2v-2z" class="G"></path><path d="M623 538s1-2 0-2c0-2-4-4-3-5l1-1 4 5c1 1 2 1 3 2l2 1-5 2-2-2z" class="F"></path><path d="M730 196c4-12 7-23 10-36l1-13c1-2 0-3 1-5 0 6 0 12-1 18-1 15-3 29-10 43h-1v-2-5z" class="M"></path><path d="M513 444c0 2 1 4 1 6v-1c1-3 0-6 0-10l1-14c0-3 0-7 1-9 1 1 0 4 0 5v16c1-4 1-9 1-13l3-16c0 2 0 7 1 8-1 4-2 7-2 10-1 3-1 7-1 10s-2 6-1 10c0 0 0 2-1 2l-1 2v4c-1 10-1 19-1 29h0l-1-10c0-5 0-11-1-15 0-2-1-3-1-5h0l1-1c0 2 0 2 1 3v-11zm22 423c1 2-3 16-4 19-1 10-2 21-2 31 0 6 2 13 1 19l-1-1c-5-18-3-34 1-52 2-5 3-11 5-16z" class="J"></path><path d="M606 484c1-1 3 0 4 0l12 1c6 1 11 3 16 4 8 3 16 5 24 5-9 1-16 0-26-2h0-2c-1-1-2-1-3-1 0 0-1-1-2-1l-4-1h-5l-14-5z" class="D"></path><defs><linearGradient id="AL" x1="781.134" y1="546.988" x2="776.953" y2="530.37" xlink:href="#B"><stop offset="0" stop-color="#d8d6d3"></stop><stop offset="1" stop-color="#fffeff"></stop></linearGradient></defs><path fill="url(#AL)" d="M776 545c-1-1-3-3-5-4-3-1-6-3-9-5l1-1c2-2 4-3 7-3 10-1 20 6 27 12-1 0-1 1-2 1-6-5-14-11-23-10-1 0-2 1-3 1v1c6 7 15 11 21 18-1 0-2-1-4-2-3-3-6-6-10-8z"></path><defs><linearGradient id="AM" x1="585.98" y1="474.361" x2="602.916" y2="480.835" xlink:href="#B"><stop offset="0" stop-color="#878685"></stop><stop offset="1" stop-color="#a1a09f"></stop></linearGradient></defs><path fill="url(#AM)" d="M586 471c1 1 2 1 4 1h1c1 0 1 0 2 1h3c1 0 1 0 2 1h2 0c1 0 1 1 2 1h0c2 0 3 0 4 1h1c1 0 2 0 3 1l2 1h-5c-2 2-3 1-5 1-1 0-2 2-2 2-1 1-1 1-2 1l-12-2v-9z"></path><path d="M704 648c3-1 5 0 7 1 3 1 5 3 6 6s1 5-1 8c-1 1-2 3-4 3-2 1-4 1-6 0l-2-2v-1l2-1c1 0 3 0 4-2 1 0 1-1 1-2 0-2-1-4-3-5-3-3-6-3-9-2h-1v-1c3-2 3-2 6-2z" class="T"></path><defs><linearGradient id="AN" x1="312.663" y1="218.424" x2="308.789" y2="239.838" xlink:href="#B"><stop offset="0" stop-color="#666"></stop><stop offset="1" stop-color="#959593"></stop></linearGradient></defs><path fill="url(#AN)" d="M296 214h1l3 3c1 0 1 1 3 2 7 9 14 16 23 23l-1 1h-3c-7-4-12-10-18-15-1-2-3-4-4-6-2-2-3-5-6-6 0-1 1-2 2-2z"></path><path d="M486 192v-1c1 0 1-1 1-1 1 0 1 0 2 1s0 0 0 1l1 1c1 1 2 3 3 4 0 1 1 2 1 3l3 5c1 1 1 2 2 3 0-1 0-1 1-1l5 21c0 1 0 3 1 5 1 3 2 6 2 10-6-18-12-35-22-51z" class="B"></path><path d="M593 206l5-9c-1 4-3 8-2 11 0 1 0 1-1 2l-1 2c0 2-1 3-1 4-1 1-1 2-1 2l-5 9-1 1c-1 0-3 0-4-1 0-2 1-4 2-6 1-3 2-7 3-10v-1-1c1-2 3-4 5-7-1 2-3 5-3 7 1-1 1 0 1-1l1-2h1 1z" class="K"></path><path d="M587 209c1-2 3-4 5-7-1 2-3 5-3 7 1-1 1 0 1-1l1-2h1 1l-2 3c-3 5-4 9-5 14v1-1c1-1 1 0 1-1 0 1 0 3-1 4 1 0 1 0 1 1l-1 1c-1 0-3 0-4-1 0-2 1-4 2-6 1-3 2-7 3-10v-1-1z" class="N"></path><defs><linearGradient id="AO" x1="549.467" y1="668.229" x2="528.513" y2="680.45" xlink:href="#B"><stop offset="0" stop-color="#8d8e8e"></stop><stop offset="1" stop-color="#c8c7c5"></stop></linearGradient></defs><path fill="url(#AO)" d="M529 686h0c0-2 0-4 1-6 1-6 6-12 11-15 3-1 5-2 8-2l-2 6c-5 2-9 5-11 11-2 4-2 8-2 12v-1c-1-2-1-3-1-5l1-1-3-1h-1c0 3 0 6-1 9h-1 0v-3c0-1 1-2 1-4z"></path><defs><linearGradient id="AP" x1="653.198" y1="214.511" x2="652.806" y2="223.885" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#818180"></stop></linearGradient></defs><path fill="url(#AP)" d="M668 226c-5-2-10-2-15-3h-14c3-3 8 0 12-2l-11-1c3-1 6-1 9-1-6-3-14-4-20-5 7-2 16-1 22 2 4 1 8 4 12 5 4 2 8 3 12 4v1c-3 0-5-1-7 0z"></path><defs><linearGradient id="AQ" x1="818.624" y1="569.884" x2="807.02" y2="571.595" xlink:href="#B"><stop offset="0" stop-color="#656566"></stop><stop offset="1" stop-color="#949490"></stop></linearGradient></defs><path fill="url(#AQ)" d="M801 551c1 1 2 1 4 2h1c10 11 15 28 18 42l2 11c1 3 1 6 1 9 0 1 1 3 0 4-1-1-1-2-1-3v-1c0-2 0-4-1-5-3-15-6-30-14-44-3-5-7-10-10-15z"></path><path d="M502 709v-1h-1c-1-3 0-7 0-10v-27l5 5h-1l-1 1s1 1 0 1c0 1 0 2 1 2v9 2c0-2 0-4 1-6 0 3 0 6 1 9v5c-1 3-1 7-1 11-1-1-3-1-4-1z" class="H"></path><path d="M502 709c0-2 0-3 1-4v-1c0-4 2-10 3-14 0 3 0 6 1 8v1c-1 3-1 7-1 11-1-1-3-1-4-1z" class="B"></path><defs><linearGradient id="AR" x1="585.781" y1="593.958" x2="603.791" y2="604.082" xlink:href="#B"><stop offset="0" stop-color="#c6c4c2"></stop><stop offset="1" stop-color="#eeeeed"></stop></linearGradient></defs><path fill="url(#AR)" d="M596 597c0-8 0-14-1-22l3-3c1 5 2 12 1 17v2h0c0 3 1 7 0 9v7h-1c-1 2 0 4-1 6v4l-1 1c0 2-1 2-1 4-1 1-2 1-2 2h0c0-3 0-5-1-8l-1-1c2-3 3-8 4-12 1-2 1-4 1-6z"></path><path d="M440 179l6 6h0l1 1 1 1c0 1 0 1 1 2 0 1 0 1 1 2 1 2 4 5 4 7l-1 1c0 1 1 2 1 3s-1 1-2 2h-1 0c-1-3-5-9-7-11h-2c-1-1-3-3-3-4a30.44 30.44 0 0 1-8-8l16 10h0c-4-4-9-7-12-10 1-1 1-1 2-1s2-1 3-1z" class="I"></path><path d="M808 621c1 4 0 8-1 12-2 18-11 38-23 52-5 6-11 11-17 16h-1c14-12 24-26 31-43 3-5 6-11 7-17 2-6 2-13 4-19v-1z" class="E"></path><path d="M502 500c0 1 2 3 2 4v2l3 8 1 3c1 1 1 2 1 4l2 3c0 1 0 3 1 4v1c-3 1-7 1-10 1v-30z" class="G"></path><path d="M561 679h23l-23 14c-2-3-1-10 0-14z" class="H"></path><path d="M759 370l1-1v-1c1 1 1 2 1 3 2 1 5 3 5 5 3 8 7 15 11 22 6 12 12 23 20 33-2 0-2-1-3-1h-1c0-1-2-2-2-3l-1-1c0-1-1-1-1-2l-4-6-1-3c-1-1-3-3-3-5-1-5-5-7-7-11-3-4-5-9-7-13-1-2-2-5-4-8l-1-2c-1-3-2-4-3-6z" class="D"></path><path d="M290 115c2 1 2 3 3 5l6 15c0 2 1 5 2 6 3 0 5 1 8 1 11 4 18 17 23 27-5-5-8-10-12-16l-3-3c-3-3-8-5-12-5-2 0-3 1-4 2-2 3 1 16 2 20-1-2-1-4-2-5l-2-12c-2-12-5-23-9-35z" class="M"></path><path d="M607 798c0 1-3 8-3 10-2 9-2 18 0 28 2 9 8 18 16 23h1v1l-2-1-4-2c-8-6-13-14-15-24-2-12 0-25 7-35z" class="J"></path><defs><linearGradient id="AS" x1="604.99" y1="515.877" x2="618.995" y2="541.674" xlink:href="#B"><stop offset="0" stop-color="#c3c3c2"></stop><stop offset="1" stop-color="#f6f5f4"></stop></linearGradient></defs><path fill="url(#AS)" d="M603 521c-1-1-2-2-1-3l1-1 5 2 3 3 12 16 2 2-7 4h0c0-3-1-3-2-5l-4-6c-1-3-4-4-5-6s-2-4-3-5h-1v-1z"></path><path d="M545 722c-1 1-2 1-3 1-1-2-1-6-2-9 0-1 1-2 2-3 2-3 8-7 12-8 1 2 1 2 1 4 1 3 0 5-2 8-3 3-5 5-8 7z" class="F"></path><path d="M545 722c0-3 0-7 3-9 1-2 5-6 7-6 1 3 0 5-2 8-3 3-5 5-8 7z" class="T"></path><path d="M387 214c4-1 7 0 11 0-3 1-7 1-11 3-3 1-6 3-9 4l-1 1h6v1c-6 0-12 1-19 2-5 1-10 3-15 3 4-3 10-5 16-7 7-3 14-6 22-7zm350-99v1l-6 23-4 18c-1 4-1 9-3 12 0-5 4-18 2-22-1-2-3-2-5-2-6 0-10 4-14 8v1c-4 5-7 10-11 15 2-3 3-7 5-10 4-5 7-12 14-15 2-1 4-2 6-2l2-1 3-3v3l1-1 10-25z" class="M"></path><defs><linearGradient id="AT" x1="626.577" y1="373.694" x2="617.923" y2="385.306" xlink:href="#B"><stop offset="0" stop-color="#757473"></stop><stop offset="1" stop-color="#969594"></stop></linearGradient></defs><path fill="url(#AT)" d="M604 384c7-5 16-8 24-10 2 1 3 2 4 4 1 0 1 1 2 2h1l-9 4h-1l1-1c-2 0-4 1-6 1-3 1-4 0-7 2l-1-1v1l-1 1c-2-2-5-2-7-3z"></path><defs><linearGradient id="AU" x1="692.601" y1="255.262" x2="714.28" y2="258.241" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#999897"></stop></linearGradient></defs><path fill="url(#AU)" d="M719 257v1c-4 1-8 2-11 3-5 1-9 1-14 1l-3-8c8 1 14 1 22-1 3 1 5 0 7 0 0 1-1 3-1 4z"></path><defs><linearGradient id="AV" x1="586.794" y1="168.661" x2="576.476" y2="201.062" xlink:href="#B"><stop offset="0" stop-color="#858583"></stop><stop offset="1" stop-color="#b5b5b5"></stop></linearGradient></defs><path fill="url(#AV)" d="M587 169l7 2c-1 1-1 1-1 2-4 5-11 9-13 14 0 1 0 2-1 3l-1 1v1c-1 2-2 3-4 5-2 3-3 6-5 9 0-3 1-6 1-9v-1c5-5 6-11 9-17 3-4 6-7 8-10z"></path><path d="M295 256h-1c-16-11-14-30-17-47l7 16v1 1h-1c0 1-1 1 0 2h0c1 1 2 3 2 5 2 7 6 13 11 19l1 1c0 1 1 2 2 3l3 2-4-2c-1 0-2-1-3-1z" class="L"></path><path d="M587 195c3-1 5-3 7-5 0 3 0 5-2 7h-1c-1 2-3 5-4 8 0 1-1 3-1 4h1v1h0c-4 5-6 11-9 16h-3c-1-3 3-9 4-12-2 3-4 10-7 11h-2l4-6s1-2 2-3c0-1 1-2 2-4 1-1 2-2 2-4l1-1c0-1 0-1 1-2 0-2 1-3 3-5l1-1c1-1 1-2 1-4z" class="Q"></path><path d="M587 195c3-1 5-3 7-5 0 3 0 5-2 7h-1c-3 3-6 10-8 14-1-3 2-6 2-9v-2l1-1c1-1 1-2 1-4z" class="F"></path><path d="M574 219s1-2 2-3c0-1 1-2 2-4 1-1 2-2 2-4l1-1c0-1 0-1 1-2 0-2 1-3 3-5v2c0 3-3 6-2 9 0 1-1 3-2 4-1-1-1-1-1-3h1l-1-1c0 1-1 2-1 3-2 3-4 10-7 11h-2l4-6z" class="H"></path><path d="M759 386v-1l-2-4c-1 0-1-1-2-2s-3-3-3-5c0-1 1-1 2-1 1 1 2 2 3 2h3c1 1 2 3 3 3 2 3 3 6 4 8 2 4 4 9 7 13 2 4 6 6 7 11-2-2-4-5-6-8h-2c-3-3-6-6-8-9-2-2-4-5-6-7z" class="G"></path><path d="M765 393v-3l3 3c3 3 5 6 7 9h-2c-3-3-6-6-8-9z" class="B"></path><path d="M821 618h2v-1l-1-1v-6c0-2 0-2 1-4 1 4 3 8 3 11 3 20 1 42-4 62h0l2-14c1-12 0-25-1-38l-1-6-1-3z" class="D"></path><path d="M824 619c0 3 0 5-1 7v1l-1-6 2-2z" class="B"></path><path d="M821 618h2v-1l-1-1v-6c0-2 0-2 1-4 1 4 3 8 3 11-1 0-2-1-2-2s0-2-1-4c0 2 1 4 1 5s-1 2 0 3l-2 2-1-3z" class="K"></path><path d="M708 638c5-1 9 0 13 2 6 4 11 10 13 17 1 6 0 13-3 18-5 8-15 10-24 11h-2c2-1 4-1 6-2 5-1 10-3 13-7 4-3 6-8 6-13 1-7-2-14-6-19-5-5-10-7-17-7h1z" class="L"></path><path d="M860 504c-2-4-3-9-6-12 0-1-2-3-2-4 8 6 10 13 15 21 5 9 12 16 20 21-10-3-15-8-21-17v1l8 16c-2 0-5-4-6-6h0c-1-2-2-3-4-5-1-2-1-3-2-5-1-1-1-3-1-4h-1v-1-3c1 0 0 0 1-1 0 0 0-1-1-1z" class="D"></path><path d="M318 195c2 1 4 5 6 6 2 2 4 3 6 4 4 3 7 5 11 6 3 1 5 2 8 2h0c-3 2-9 1-11-1-2-1-4-3-6-3l2 2c4 2 10 6 14 7 5 1 10 0 15 0 1 0 2-1 4 0-6 5-17 8-24 8-2 0-4-1-5-2 0-1 0 0 1-1l9-3c-4-2-7-3-10-5-9-5-15-12-20-20z" class="M"></path><defs><linearGradient id="AW" x1="817.815" y1="587.463" x2="807.46" y2="597.302" xlink:href="#B"><stop offset="0" stop-color="#696969"></stop><stop offset="1" stop-color="#a6a5a2"></stop></linearGradient></defs><path fill="url(#AW)" d="M801 573c2 0 2 1 4 2s5 5 6 7l2 2h2l1 1c2 4 4 7 5 11 0 1 1 3 1 4 0 2 1 5 1 6-1 2-1 2-1 4v6l1 1v1h-2l-1-3c-4-16-9-29-19-42z"></path><path d="M820 615c1-2 1-3 1-5 0-3-1-7 1-10 0 2 1 5 1 6-1 2-1 2-1 4v6l1 1v1h-2l-1-3z" class="C"></path><path d="M664 564l1-1c3-3 6-6 9-10 7-8 17-13 28-18v1c-10 5-23 12-28 22 3-2 5-5 9-8h0l6-3c2-1 3-2 5-3 6-2 13-6 20-7h0c-1 1-1 1-2 1l-9 3c-7 3-14 6-20 11-3 2-6 6-9 8 2 0 4-2 7-3 9-5 18-11 27-15 4-2 8-4 12-5h0l-12 6c-7 3-13 8-20 11l-14 8c-3 1-6 4-10 6v-1c-1 1-2 1-2 1h-1v-1l3-3z" class="F"></path><defs><linearGradient id="AX" x1="328.702" y1="257.188" x2="311.857" y2="237.548" xlink:href="#B"><stop offset="0" stop-color="#6b6b6b"></stop><stop offset="1" stop-color="#9e9c9a"></stop></linearGradient></defs><path fill="url(#AX)" d="M304 247c1-1 1-5 0-6 0-2-2-1-2-4 2 2 3 3 5 4l5 3c1 1 1 1 2 1l1 1c1 0 4 1 5 0 8 2 14 2 21 1-1 1-3 4-4 5s-5 1-7 1c-9 0-17 1-26-5v-1z"></path><path d="M604 636h0c-1 4-4 7-4 11 0 2-2 8-4 10 1 1 1 2 1 4l1 1v-1s0-1 1-1h0c-1 3-3 6-5 9a30.44 30.44 0 0 1-8 8v-11-5c0-1 2-2 2-3 2-2 4-5 5-8v-1l1-1v1l1 1c4-3 7-10 9-14z" class="H"></path><path d="M594 669l-1-1c-1-3 2-8 3-11 1 1 1 2 1 4l1 1v-1s0-1 1-1h0c-1 3-3 6-5 9z" class="B"></path><path d="M704 477l3 1h0 4l3 2c2-1 2 0 4 0 3 0 7 0 10 1 3 0 5 1 7 1 1 0 1 1 2 1h-1c-2-1-5-1-7-1-4-1-8 0-11 0-2-1-4 0-6-1-1 0-1 0-2 1-1 0-2-1-2-1 1 1 1 1 3 1h3 2 2v1c-2 1-5 0-8 0-4-1-8-1-13 0h4c3 0 6 1 9 1 6 0 11 1 16 2 4 1 7 2 11 4 9 4 20 8 26 15 1 1 4 3 4 4-5-4-10-8-16-12-6-3-13-5-19-7-12-3-23-6-35-6l-1-1-11 2 16-6h0c1-1 2-1 3-2z" class="I"></path><path d="M704 477l3 1h0 4l3 2c-3 1-10 0-13-1h0c1-1 2-1 3-2z" class="C"></path><defs><linearGradient id="AY" x1="302.705" y1="240.282" x2="292.524" y2="245.623" xlink:href="#B"><stop offset="0" stop-color="#c4c2c1"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#AY)" d="M284 225c7 12 16 24 29 29h10c-2 2-4 3-6 3h-4 0c2 2 5 3 7 4l-6-1c-13-4-22-15-29-26 0-2-1-4-2-5h0c-1-1 0-1 0-2h1v-1-1z"></path><path d="M313 257c-3-1-5-3-7-5l7 3v-1h10c-2 2-4 3-6 3h-4 0z" class="J"></path><defs><linearGradient id="AZ" x1="294.552" y1="319.719" x2="263.658" y2="312.397" xlink:href="#B"><stop offset="0" stop-color="#d3d4d5"></stop><stop offset="1" stop-color="#fffefd"></stop></linearGradient></defs><path fill="url(#AZ)" d="M298 313h-1c-3 0-6 1-9 2-14 4-28 7-39 17v-1c5-8 17-12 26-16 6-2 12-5 19-7-1 1-1 1-1 2h1 1 4v1c6-2 13-3 20-3v2h1c1 0 1 0 2-1v1c-2 1-9 2-12 3h-4c-2 0-6 1-8 0z"></path><path d="M299 311c6-2 13-3 20-3v2c-3 0-7 1-10 2-4 0-8 1-12 0l2-1z" class="E"></path><defs><linearGradient id="Aa" x1="558.877" y1="346.123" x2="563.143" y2="367.473" xlink:href="#B"><stop offset="0" stop-color="#4e4b4b"></stop><stop offset="1" stop-color="#8e908d"></stop></linearGradient></defs><path fill="url(#Aa)" d="M549 371c1 0 3-3 4-4l6-11c2-3 5-11 9-13v1c0 7-5 15-7 21l-1 1-1 2 1 1 1-1c1 0 2 0 3 1v2l1-1 1-1h2c1 1 2 1 4 1h1c0 1 1 1 2 2l-2 2-1-1c-2 0-6-1-8 0-2 0-4 0-5-1-2 1-4 4-5 6l2-5v-1c-1 1-2 1-2 2-2-1-3-1-5-1-1 1-2 3-3 4h-1l2-4 2-2z"></path><defs><linearGradient id="Ab" x1="755.471" y1="695.589" x2="748.039" y2="687.922" xlink:href="#B"><stop offset="0" stop-color="#828280"></stop><stop offset="1" stop-color="#abaaa8"></stop></linearGradient></defs><path fill="url(#Ab)" d="M734 702c5-2 10-3 14-6 10-5 18-16 23-25 1-2 2-5 3-6-5 15-13 31-28 39l-2 1c-1 0-2 0-3 1h0-2v1h-1c-1 0-1 1-3 1 2-1 5-2 5-4 1 0 1 0 2-1l-17 4c0 1-1 1-2 1v-1l5-1c2-1 4-1 6-3h-1l1-1z"></path><path d="M635 380l1 1 5 6-7 4-2 1c-3 3-6 5-9 8h0v-1-1c1-2 3-3 3-5l-1-1h-1c-2 0-3 0-5-1l-4 5v-1l3-5v-1c-1-2-4-2-6-2l-7 5c-2 1-3 3-5 4-1 1-2 3-3 3h-1c2-2 4-4 7-6l8-6 1-1v-1l1 1c3-2 4-1 7-2 2 0 4-1 6-1l-1 1h1l9-4z" class="C"></path><path d="M619 391c1-1 1-2 4-3h2c0 2 1 1 2 2v1c2 0 4 1 5 1-3 3-6 5-9 8h0v-1-1c1-2 3-3 3-5l-1-1h-1c-2 0-3 0-5-1z" class="Q"></path><path d="M636 381l5 6-7 4c-3-1-4-1-7-1 1-2 4-5 5-6 2-1 3-2 4-3z" class="J"></path><path d="M583 283l7-8c10-9 23-13 36-16 4-1 9-3 14-3-8 3-17 6-24 11h0-1-1c-1 0-2 1-3 2s-2 1-3 1h-1-1l-6 4-2 1h-1l-3 3-1 1h-1c-1 1-1 1-1 2l-6 5-1-1 1-1-1-1v1l-1-1z" class="B"></path><defs><linearGradient id="Ac" x1="549.662" y1="343.367" x2="537.411" y2="372.164" xlink:href="#B"><stop offset="0" stop-color="#575654"></stop><stop offset="1" stop-color="#a0a09f"></stop></linearGradient></defs><path fill="url(#Ac)" d="M544 350c2-2 4-5 6-7 2 2 2 2 5 2-1 5-4 9-7 13l-7 10c0 1-2 2-2 4v1c-1 0-2 1-2 2l-1-2h-4c0-2 2-5 3-7 0-2 2-5 3-7 0-1 1-2 1-3h0c1 0 1-1 2-1 1-1 3-4 3-5z"></path><defs><linearGradient id="Ad" x1="535.231" y1="339.502" x2="532.694" y2="368.862" xlink:href="#B"><stop offset="0" stop-color="#51504f"></stop><stop offset="1" stop-color="#8e8f8d"></stop></linearGradient></defs><path fill="url(#Ad)" d="M524 372l13-33 6 1c-2 5-4 10-4 16h0c0 1-1 2-1 3-1 2-3 5-3 7-1 2-3 5-3 7l-2 3v-1c0-1 1-3 0-4-2 0-4 1-6 1z"></path><path d="M659 784c-4 0-6-2-8-4-7-3-14-5-21-4-14 1-26 10-34 21v-1c1-3 3-6 6-9 5-5 12-11 19-12 3-1 6-1 9-1v-1c-9 0-18-2-27-1h-1c2-2 7-2 10-2 17-1 35 2 49 14h-2z" class="J"></path><defs><linearGradient id="Ae" x1="554.851" y1="342.451" x2="554.659" y2="362.535" xlink:href="#B"><stop offset="0" stop-color="#535151"></stop><stop offset="1" stop-color="#848483"></stop></linearGradient></defs><path fill="url(#Ae)" d="M542 369c4-7 9-12 13-19 1-2 3-7 5-8 1 0 2 0 3 1 0 2-1 4-2 6-2 6-6 12-10 18l-2 4-2 2h-1l-3 4-1-1-3 5c-1-2-1-4-2-6h0c0-1 1-2 2-2v-1l3-3z"></path><path d="M542 369c1 0 1 0 2-1l2-1h0c2-1 4-1 5 0l-2 4-2 2h-1l-3 4-1-1-3 5c-1-2-1-4-2-6h0c0-1 1-2 2-2v-1l3-3z" class="H"></path><path d="M539 373h1c1 0 1-1 2-1h2l1 1-3 3-3 5c-1-2-1-4-2-6h0c0-1 1-2 2-2z" class="F"></path><path d="M626 449c7-5 10-10 11-18l1-7 12-3c-1 10-5 21-14 27l-1 1c-3 0-5 1-9 0z" class="L"></path><path d="M602 576l1 1c1 2 0 5 0 7l1-7h1c0 9 0 18-1 27 0 1-1 2-1 4l-1 3v2l-2 6-1 4-1 1v3c-1 1-1 1-2 3s-2 4-3 7l-1-1c0-2 0-3-1-5l2-4h0l1-1c1-1 1-2 1-4s1-2 1-4l1-1v-4c1-2 0-4 1-6h1v-7c1-2 0-6 0-9 1-5 3-9 3-15z" class="D"></path><path d="M869 534l-12-7c-6-4-9-10-10-17 2 4 4 8 6 11 3 3 8 6 11 8-2-4-5-10-6-15-2-6-2-14-6-19s-9-9-14-12c8 2 12 6 17 14 1 2 2 5 4 6l1 1c1 0 1 1 1 1-1 1 0 1-1 1v3 1h1c0 1 0 3 1 4 1 2 1 3 2 5 2 2 3 3 4 5h0c1 4 4 6 6 9 1 1 2 3 2 4l-7-3z" class="F"></path><path d="M869 534v-1l-1-1 1-1c1 1 3 2 4 2h1c1 1 2 3 2 4l-7-3z" class="K"></path><defs><linearGradient id="Af" x1="615.168" y1="631.564" x2="655.857" y2="620.661" xlink:href="#B"><stop offset="0" stop-color="#858481"></stop><stop offset="1" stop-color="#b2b2b3"></stop></linearGradient></defs><path fill="url(#Af)" d="M623 626l-1 6a30.44 30.44 0 0 1 8-8c7-8 15-14 25-18l-2 2 1 2h1 0 2c-5 2-10 5-14 8-7 6-13 13-19 20-3 5-6 9-9 13 2-6 5-13 6-20l2-5h0z"></path><defs><linearGradient id="Ag" x1="596.123" y1="620.57" x2="604.312" y2="622.535" xlink:href="#B"><stop offset="0" stop-color="#d1cfce"></stop><stop offset="1" stop-color="#f6f5f4"></stop></linearGradient></defs><path fill="url(#Ag)" d="M607 592l1 1v4h0c1-3 1-5 3-7-1 4-1 8 0 12l-1 4c-1 0-1 1-1 2-1 2-2 5-3 7 0 1-1 2-1 4l-1 3c-1 1 0 1-1 2v1s0 1-1 2v1c0 1 0 2-1 2v2c0 1-1 2-1 3l-1 1-1 3c0 1-1 2-1 3h0c-1 0-2 1-3 1v1h0v2c-1-1-1-1-2-1s-1-1-2-1l3-6v-1h0c1-3 2-5 3-7s1-2 2-3v-3l1-1 1-4 2-6v-2l1-3c0-2 1-3 1-4 2-4 2-8 3-12z"></path><defs><linearGradient id="Ah" x1="436.817" y1="295.77" x2="430.683" y2="275.73" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#d8d7d5"></stop></linearGradient></defs><path fill="url(#Ah)" d="M422 272c2 1 2 1 4 1l2 2 1-1 4 2v1l4 2v1c1 2 5 4 7 6 2 3 4 5 6 8-5-1-11 0-16 0-5-7-10-12-17-16h-1-1c0-1-1-1-2-2h2c1 0 1 1 2 1s1 1 2 2h9l3-1c-2-2-5-4-8-5l-1-1z"></path><defs><linearGradient id="Ai" x1="743.031" y1="309.845" x2="741.565" y2="317.529" xlink:href="#B"><stop offset="0" stop-color="#cecdcc"></stop><stop offset="1" stop-color="#fdfdfc"></stop></linearGradient></defs><path fill="url(#Ai)" d="M716 306c4 0 7-1 11 0 8 1 17 6 25 9 6 2 13 5 18 9 3 1 5 4 7 7-2-1-4-3-5-4-2-2-5-3-7-4-13-5-27-9-41-12h0-3-4c-3-1-5-1-7-1l-1 1c-1 0-2 0-4-1h1c3-2 6-3 10-4z"></path><path d="M709 311c-1 0-2 0-4-1h1c3-2 6-3 10-4 2 1 3 1 5 1h1l1 1c-1 1-4-1-5 0v2h5l1 1h0-3-4c-3-1-5-1-7-1l-1 1z" class="I"></path><defs><linearGradient id="Aj" x1="856.935" y1="460.846" x2="868.459" y2="480.633" xlink:href="#B"><stop offset="0" stop-color="#747372"></stop><stop offset="1" stop-color="#acadae"></stop></linearGradient></defs><path fill="url(#Aj)" d="M882 462c0 2 5 10 6 13-12-10-24-3-38-3-7 1-13 0-20-2h3c11 1 23 0 33-4 3-2 6-4 10-6 1 0 2-1 3-2 2 0 6 1 8 1 0 3 10 7 9 10h0c-2-2-4-4-6-5l-1-1c-2-1-4-2-6-2l-1 1z"></path><defs><linearGradient id="Ak" x1="833.757" y1="461.909" x2="832.919" y2="450.389" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#c7c6c5"></stop></linearGradient></defs><path fill="url(#Ak)" d="M870 447v3h-1 1c-12 11-32 14-48 13-9 0-18-3-26-8l1-2-1-2c5 3 11 6 17 7 20 4 40 0 57-11z"></path><path d="M764 611h2l1 1 1 2c1 2 2 5 4 7l1-1c1 4 3 8 3 12 0 11-3 23-8 32l-2 3h-4l1-1c6-14 8-28 5-43-1-4-2-8-4-12z" class="J"></path><path d="M498 199c-1-1-1-2-1-3v-1c0-1 0-1-1-2v-1l1-1v1h1v-2-2h0l1 1 1 1v-1l1-1v2l1 1v2 1l2 4v2 1c1 0 1 1 1 2s1 2 1 2l1 7v2l1 1v4 4l2 17c1 3 0 11 0 14l-1-5c0-2-1-4-1-6 0-4-1-7-2-10-1-2-1-4-1-5l-5-21-2-8z" class="D"></path><path d="M498 199h0l7 26 2 8h0l2 8h1v-1c1 3 0 11 0 14l-1-5c0-2-1-4-1-6 0-4-1-7-2-10-1-2-1-4-1-5l-5-21-2-8z" class="N"></path><defs><linearGradient id="Al" x1="696.503" y1="249.038" x2="730.636" y2="242.153" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#a9a8a6"></stop></linearGradient></defs><path fill="url(#Al)" d="M714 243h1c9-2 14-9 19-16 0 1-1 3-2 5-1 1-1 1 0 2h4c-3 6-8 10-12 14h-1v-1h-1c-6 5-16 6-24 6h-2l3-6c5 0 10-2 15-4z"></path><defs><linearGradient id="Am" x1="509.931" y1="716.169" x2="527.055" y2="734.693" xlink:href="#B"><stop offset="0" stop-color="#cfccca"></stop><stop offset="1" stop-color="#f0f0ef"></stop></linearGradient></defs><path fill="url(#Am)" d="M535 719h2l2 7-2 1c-11 6-24 7-36 4v-10c2 2 3 4 5 4 1 0 2-1 3-1 3 0 4 1 6 2v1c1-1 3-2 4-2 1-1 3 0 4-1h0l2 1 10-6z"></path><defs><linearGradient id="An" x1="602.018" y1="290.048" x2="632.212" y2="265.644" xlink:href="#B"><stop offset="0" stop-color="#b6b5b3"></stop><stop offset="1" stop-color="#fffffd"></stop></linearGradient></defs><path fill="url(#An)" d="M616 267c3-1 6-2 10-2h1 4c1 1 2 1 2 2s0 0 1 1 1 3 1 4 1 0-1 1l-1 1c-8 3-16 6-22 13-3 3-5 6-8 9 1-6 2-11 6-15 1-2 3-3 4-5 1-1 2-1 3-1h0c2 0 5-3 6-4-1-1-1-2-2-4h0 0c-3 1-5 1-8 2h-1c1-1 2-2 3-2h1 1z"></path><defs><linearGradient id="Ao" x1="656.474" y1="551.848" x2="645.615" y2="544.151" xlink:href="#B"><stop offset="0" stop-color="#d4d4d4"></stop><stop offset="1" stop-color="#fefbfa"></stop></linearGradient></defs><path fill="url(#Ao)" d="M663 520c1 0 2 0 3 1-7 12-14 25-19 38l-7 19c-1 1-1 2-2 4l-1 5c-1 1-1 2-2 3h-1c5-19 9-41 20-58 2-5 6-8 9-12z"></path><path d="M692 714h-3c-3-1-6-1-9-2-7 0-13 1-19 4l-6 6c1-4 3-6 5-8 6-6 12-6 20-5l-1-1c-6-3-14-2-20-1 1-1 3-2 4-3 8-3 17 3 25 6l-3-2c-6-5-11-11-19-14h0 0c8-3 13 4 18 9 1 1 3 3 5 3 2 2 5 3 7 5h-2c1 1 2 1 4 2h-5l-1 1z" class="O"></path><path d="M870 351c2 1 4 5 5 7 5 10 4 25 1 35-4 13-12 22-24 28-8 4-17 7-25 4-1-1-2-1-3-2l5 1c9 2 17-3 24-8 11-7 19-15 22-29 2-13 2-26-5-36z" class="H"></path><defs><linearGradient id="Ap" x1="744.002" y1="579.446" x2="758.209" y2="569.507" xlink:href="#B"><stop offset="0" stop-color="#8b8c8a"></stop><stop offset="1" stop-color="#bcbbb9"></stop></linearGradient></defs><path fill="url(#Ap)" d="M722 553h0 3v1c1-1 1-1 1-3h-1v-1c18 5 34 17 44 33 3 5 5 10 6 16h0l-1 1h0c-1 2 0 1-1 2 0-1-1-1-1-2 0-2 0-4-1-5-2-5-4-10-7-14-10-13-25-23-41-25-4-1-8-1-12 0h-6l4-1c4-1 8-2 12-1h2l-1-1z"></path><path d="M502 476c0-2-1-4 0-6h0c-1-4 0-8-1-11 0-2 0-4 1-6l4 10h1c-2-5-4-9-5-14s-1-10-1-15c4 7 6 14 8 21 1 2 1 3 2 5v-2c-1-2-2-4-2-6h0 1v-2-1c1 1 1 2 1 4h0c0 2 1 3 1 5 1 4 1 10 1 15h-1v-3c0-1-1-2-1-3-1 1-1 0-1 2h0v1 1l1 1h0c0 1 1 2 1 3v1h0v2 1l-1 1h0c-1-1-1-1-1-2v-1c-1-1-1-2-1-2v-2-1l-2-2-1-1c0 2 1 5 2 8-1-2-3-4-3-6-1 1-1 2-1 3l1 1h0c0 1 0 2 1 3-1 0-2 1-2 1v8l-1-8-1 1v-4z" class="K"></path><path d="M559 738c0-1 1-2 1-3l2-2v-1c1-1 2-5 4-5 0-1 1-1 2-1-8 17-18 39-37 46-9 3-20 4-29 0l-1-1c0-1 0-2 1-3v1c2 0 5 1 7 1 7 1 13 1 20-1 10-4 17-12 23-20 2-3 5-6 7-10v-1z" class="E"></path><path d="M156 352h0v1c-6 10-6 24-4 34v1c3 9 7 18 15 23 7 6 19 14 28 13 4 0 6-1 10-1l-2 1c-2 1-4 2-7 2-10 2-21-3-29-9-11-9-17-21-18-35-1-11 0-22 7-30z" class="G"></path><path d="M407 265v-1c-6-3-12-5-18-9 10 2 23 5 33 9 4 1 7 4 11 6 1 0 1 0 2 1v1h1l10 11c0 1-1 1-2 2h0l-7-5v-1l-4-2v-1l-4-2-1 1-2-2c-2 0-2 0-4-1-1 0-1-1-2-1-2 0-3-1-4-1l-3-2h-1c-2-1-3 0-4-1l1-1v-1h-2z" class="B"></path><path d="M653 412h2l1 2c-1 2-2 2-4 2l-14 4c-9 2-18 5-26 10-9 4-17 10-25 15l-1 1v-1l1-2c3-4 7-7 10-9 17-12 36-18 56-22z" class="F"></path><path d="M396 281c-18-4-37-3-54 3-6 2-12 5-17 6h0l14-8c20-10 39-12 60-5v1c1 1 2 2 4 2h-1c-1 0-1 1-1 1h-1-4z" class="E"></path><path d="M627 279c14-8 34-7 50-3 10 3 18 8 27 13-2 1-8-3-10-3-4-2-9-3-14-4-17-4-34-5-50-1h-3l-1 1-1-1 2-2z" class="L"></path><defs><linearGradient id="Aq" x1="627.499" y1="472.42" x2="630.712" y2="483.54" xlink:href="#B"><stop offset="0" stop-color="#cdcbc9"></stop><stop offset="1" stop-color="#f6f5f6"></stop></linearGradient></defs><path fill="url(#Aq)" d="M619 476c12 3 23 1 34-3h2v1l-1 2c-3 2-7 3-11 5-1 0-1 0-2 1-6 1-12 2-19 2v1l-12-1c-1 0-3-1-4 0l-8-2c1 0 1 0 2-1 0 0 1-2 2-2 2 0 3 1 5-1h5l-2-1c-1-1-2-1-3-1l1-1h8l3 1h0z"></path><path d="M600 481l5 1c5 1 12 1 17 2v1l-12-1c-1 0-3-1-4 0l-8-2c1 0 1 0 2-1z" class="C"></path><path d="M616 475l3 1h0 1c0 1 0 1 1 1h-2v1l1 1h0-2c-2-1-3 0-5-1h-1 0l-2-1c-1-1-2-1-3-1l1-1h8z" class="B"></path><defs><linearGradient id="Ar" x1="410.671" y1="264.88" x2="415.318" y2="252.723" xlink:href="#B"><stop offset="0" stop-color="#d7d6d4"></stop><stop offset="1" stop-color="#fcfbfb"></stop></linearGradient></defs><path fill="url(#Ar)" d="M433 270c-12-10-26-18-41-22-5-2-11-3-16-4-1 0-7 0-7-1 24-2 48 6 66 22 6 5 12 12 16 19 2 3 4 7 6 11-3-3-5-6-7-9-3-5-9-12-14-14h-1v-1c-1-1-1-1-2-1z"></path><path d="M619 559v4c0 2 0 3 1 5 0 4-1 8-2 12 2-2 2-2 2-4v8c0 5-1 9-2 14-1 1-2 3-3 5v1 1c-1 2-2 4-3 7v1c0 1 0 2-1 3v2l-1 4-2 3-4 11c-2 4-5 11-9 14l-1-1c1-2 2-3 2-4s1-2 1-3 1-2 1-3l1-3 1-1c0-1 1-2 1-3v-2c1 0 1-1 1-2v-1c1-1 1-2 1-2v-1c1-1 0-1 1-2l1-3c0-2 1-3 1-4 1-2 2-5 3-7 0-1 0-2 1-2 2-3 3-8 4-12l5-35z" class="L"></path><path d="M619 559v4c0 2 0 3 1 5 0 4-1 8-2 12l-3 19c-1 3-2 5-3 8 0-4 3-10 2-13l5-35z" class="C"></path><path d="M741 367c2 1 4 3 5 5 2 1 3 3 5 4 2 3 5 6 7 9l1 1c2 2 4 5 6 7 2 3 5 6 8 9h2c2 3 4 6 6 8 0 2 2 4 3 5l1 3 4 6c0 1 1 1 1 2l1 1c0 1 2 2 2 3h1c1 2 4 5 4 7-4-2-7-5-9-8-7-6-12-14-18-22l-10-12c-2-2-3-4-4-5-6-8-11-15-16-23zm146 92c5 2 11 5 14 10 5 9 9 20 6 31l-3 7c-4-7-4-13-5-21-2-10-9-18-17-24l1-1c2 0 4 1 6 2l1 1c2 1 4 3 6 5h0c1-3-9-7-9-10z" class="E"></path><defs><linearGradient id="As" x1="612.861" y1="362.161" x2="598.227" y2="380.726" xlink:href="#B"><stop offset="0" stop-color="#646362"></stop><stop offset="1" stop-color="#9f9e9d"></stop></linearGradient></defs><path fill="url(#As)" d="M590 374c5-4 10-8 15-11 2-1 4-2 7-2h1v1h-1c-4 3-7 7-10 11 5-3 10-6 15-8 2 1 4 2 6 4-4 2-8 4-11 6-2 2-5 4-7 5s-4 4-6 5c-2-2-6-2-8-2l-5-5h0l4-4z"></path><path d="M590 374c2 2 4 5 8 5 2 0 5 0 7 1-2 1-4 4-6 5-2-2-6-2-8-2l-5-5h0l4-4z" class="Q"></path><path d="M423 181l-4-6 8 6c0-1 0-1-1-3h1c1 0 3 1 4 2v1a30.44 30.44 0 0 0 8 8c0 1 2 3 3 4h2c2 2 6 8 7 11h0 1c1-1 2-1 2-2l2 3v2l-1-1-2 2v-1 3l3 8 3 8h-6-1l-7-12-2-3-1-1-2-2v-1h1l2 2v1l1-2h1s-1-1-1-2c-1-1-1-2-1-4h0v-1l-1-1c0-2-1-3-1-4h-1l-1-1c-3-4-7-6-11-10h1l-5-5-1 1z" class="C"></path><path d="M453 210l3 8c-1 0-2 0-3-1l-1-1h0v-1c0-2 0-3 1-5z" class="G"></path><path d="M443 202l2 3v1c2 2 3 5 4 8h-1c-1 0-2-1-2-1v-2l-1-1h0c-1 1 0 2 0 4l-2-3-1-1-2-2v-1h1l2 2v1l1-2h1s-1-1-1-2c-1-1-1-2-1-4h0z" class="F"></path><path d="M442 193h2c2 2 6 8 7 11h0 0v1l1 1v2c-2-1-4-4-5-6 0-1 0-1-1-2h-1c0-1-1-2-1-3s-2-3-2-4z" class="B"></path><path d="M453 226v-1l-2-4-1-3 1-1c0 1 1 1 1 2v1h1c1-1 0-1 0-3 1 1 2 1 3 1l3 8h-6z" class="N"></path><path d="M423 181l-4-6 8 6c0-1 0-1-1-3h1c1 0 3 1 4 2v1a30.44 30.44 0 0 0 8 8c0 1 2 3 3 4 0 1 2 3 2 4s1 2 1 3h1c0 2 1 4 2 6v1c-2-3-4-6-5-9l-2-2h-1l-1-1c-3-4-7-6-11-10h1l-5-5-1 1z" class="F"></path><path d="M427 181c0-1 0-1-1-3h1c1 0 3 1 4 2v1a30.44 30.44 0 0 0 8 8c0 1 2 3 3 4 0 1 2 3 2 4-5-6-11-10-17-16z" class="P"></path><path d="M602 168c6 0 11-6 15-10-1 3-3 6-6 9-3 4-7 7-10 11-6 5-13 12-17 19l-3 3c-1 1-3 5-4 5 0-1 0-1 1-2v-2-1c-1 1-1 2-2 3l-2 4c-1-2-1-3-2-5l2-5c2-2 3-3 4-5v-1l1-1c1-1 1-2 1-3l13-12 2-2c2-3 4-5 8-7l-6 6h1c1-1 3-2 4-4z" class="K"></path><path d="M593 175l2-2c2-3 4-5 8-7l-6 6h1c1-1 3-2 4-4 0 1 1 2 2 2l-16 13h-1c2-2 6-5 6-7v-1z" class="S"></path><path d="M602 168c6 0 11-6 15-10-1 3-3 6-6 9v-1l-1 1c-4 5-11 10-16 15 2-3 10-10 10-11v-1h0c-1 0-2-1-2-2z" class="M"></path><path d="M703 363c11 4 21 9 30 17 2 1 4 3 6 5-6-4-12-7-18-8-1-1-2-1-3-1 12 4 23 10 32 18 4 4 9 10 12 16-2-1-9-10-12-13-3-2-6-4-8-6-8-5-17-9-25-11-5-2-10-3-14-5v-3-1c-1-2 0-5 0-8z" class="L"></path><path d="M626 265c6-1 12-1 17-2 7-1 13-3 20-3 6-1 11 0 17 2 3 0 8 1 11 2-12-1-22-3-33 1-2 0-5 1-7 2 6 0 12 0 18 2 5 0 9 1 14 1 0 1-1 1-2 2-3 0-3 0-5 1-2 0-7-1-9-1-11-1-22 0-34 2l1-1c2-1 1 0 1-1s0-3-1-4-1 0-1-1-1-1-2-2h-4-1z" class="E"></path><path d="M669 269c5 0 9 1 14 1 0 1-1 1-2 2-3 0-3 0-5 1-2 0-7-1-9-1-2-2-9-2-12-3h10 4z" class="G"></path><defs><linearGradient id="At" x1="598.508" y1="295.515" x2="603.668" y2="263.839" xlink:href="#B"><stop offset="0" stop-color="#acacab"></stop><stop offset="1" stop-color="#eeedeb"></stop></linearGradient></defs><path fill="url(#At)" d="M611 269h1c3-1 5-1 8-2h0 0c1 2 1 3 2 4-1 1-4 4-6 4h0c-1 0-2 0-3 1h0c-8 5-15 11-20 18h-15l9-9 4-4c0-1 0-1 1-2h1l1-1 3-3h1l2-1 6-4h1 1c1 0 2 0 3-1z"></path><path d="M423 181l1-1 5 5h-1c4 4 8 6 11 10l1 1h1c0 1 1 2 1 4l1 1v1h0c0 2 0 3 1 4 0 1 1 2 1 2h-1l-1 2v-1l-2-2h-1v1l2 2 1 1v1c1 5 5 9 6 14-1 1-2 1-3 1l-2-2h0c0 1 1 2 0 3h-2l-3-5c-1-1-2-2-2-3-1-1 0-1-1-2l-1-3c-1-2-2-3-2-5 0-4-3-7-3-11l2 2-9-20z" class="L"></path><path d="M444 225l1-1c-1-5-4-11-7-16 1 1 2 1 3 2v1h1v-1l1 1v1c1 5 5 9 6 14-1 1-2 1-3 1l-2-2z" class="C"></path><path d="M433 210c0-4-3-7-3-11l2 2c1 1 1 1 1 2 1 2 2 4 3 7 2 2 4 5 5 7 0 1 1 2 1 3 1 2 2 3 2 5 0 1 1 2 0 3h-2l-3-5c-1-1-2-2-2-3-1-1 0-1-1-2l-1-3c-1-2-2-3-2-5z" class="K"></path><path d="M441 217c-1 0-2-1-2-2l-1 1c-1-1-2-3-2-4l-3-9h0c1 2 2 4 3 7 2 2 4 5 5 7z" class="H"></path><defs><linearGradient id="Au" x1="768.256" y1="562.275" x2="776.015" y2="546.472" xlink:href="#B"><stop offset="0" stop-color="#676767"></stop><stop offset="1" stop-color="#838281"></stop></linearGradient></defs><path fill="url(#Au)" d="M707 548c3-4 10-7 14-10 3-2 6-3 10-3 13-2 25 2 37 7 3 1 6 3 8 3 4 2 7 5 10 8 2 1 3 2 4 2l11 11c6 6 12 11 15 19l-1-1h-2l-2-2c-1-2-4-6-6-7s-2-2-4-2c-7-9-15-18-25-24-13-7-32-13-47-11-5 1-9 2-14 5-2 2-4 4-7 5h-1z"></path><defs><linearGradient id="Av" x1="733.999" y1="173.429" x2="749.172" y2="179.552" xlink:href="#B"><stop offset="0" stop-color="#5c5c5b"></stop><stop offset="1" stop-color="#767574"></stop></linearGradient></defs><path fill="url(#Av)" d="M729 215c8-11 11-24 14-37 2-11 5-23 5-34 0-9-2-17-3-25 2 3 3 8 3 12 2 10 4 20 5 31 0 3 0 6-1 10v4l-1 4-2 6v-2-3l-7 18c-2 4-4 7-6 10-1 2-3 5-4 6h-3z"></path><path d="M749 181c0-1 0-3 1-4h0v1l1-1v3l-2 6v-2-3z" class="O"></path><path d="M537 480l-6 14h0c4-8 8-15 14-23 0 4 0 8-2 12-1 5-5 9-7 14-3 6-6 12-9 19l-1 3c-1 1-2 2-2 3v1c-3 3-7 5-11 6v-2h1v-2-1-1c1-4 1-7 2-10v-3l1 3v6 1c2-1 2-4 3-6 0-1 0-1 1-2s1-2 1-3c-1-2 0-3 0-5l1-1c0 1 0 1 1 2 1-2 2-3 2-5 0-3 1-6 3-8 1-4 4-10 8-12z" class="C"></path><path d="M523 503c0 1 0 1 1 2-2 5-3 11-6 16l-2 3v-11-3l1 3v6 1c2-1 2-4 3-6 0-1 0-1 1-2s1-2 1-3c-1-2 0-3 0-5l1-1z" class="B"></path><path d="M617 467c13-1 27-8 35-19 3-3 5-6 6-10 0-1 1-4 2-4-1 11-8 23-15 30-8 7-18 10-28 10h-1v1h-8l-1 1h-1c-1-1-2-1-4-1h1c1-1 2-1 4-1h0 0l-5-2c-2 0-2-1-2-3s0-2 2-3l4 1h11z" class="E"></path><path d="M606 467h11 0c-3 1-5 0-8 2l-4 1 1 2-2-1c-1 0-2-1-2-2 1-1 2-2 4-2z" class="K"></path><path d="M602 466l4 1c-2 0-3 1-4 2 0 1 1 2 2 2l2 1c2 0 3 1 4 1 2 0 4 0 5 1h1v1h-8l-1 1h-1c-1-1-2-1-4-1h1c1-1 2-1 4-1h0 0l-5-2c-2 0-2-1-2-3s0-2 2-3z" class="H"></path><defs><linearGradient id="Aw" x1="585.058" y1="520.134" x2="602.943" y2="528.366" xlink:href="#B"><stop offset="0" stop-color="#8a8988"></stop><stop offset="1" stop-color="#b9b8b6"></stop></linearGradient></defs><path fill="url(#Aw)" d="M586 514v-4h0l10 7c1 1 3 3 5 3l2 1v1h1c1 1 2 3 3 5s4 3 5 6l4 6c1 2 2 2 2 5h0l-6 4-4 2-1 1c-1-3-1-5-2-8-1-4-3-9-5-12l-2 1-6-6-1 1c0-1-1-1-1-2-2-1-3-2-4-3-1-2 0-6 0-8z"></path><path d="M608 550c0-1-1-3-1-4-1-4-3-8-3-11 1-1 0-1 1-1 1 2 1 2 1 3 1 1 1 1 1 2l1 2 1 1v2h1v1l1 1h0c0 1 0 1 1 2l-4 2z" class="E"></path><defs><linearGradient id="Ax" x1="586.224" y1="511.326" x2="601.825" y2="524.524" xlink:href="#B"><stop offset="0" stop-color="#868584"></stop><stop offset="1" stop-color="#bbbbb9"></stop></linearGradient></defs><path fill="url(#Ax)" d="M586 514v-4h0l10 7c1 1 3 3 5 3l2 1v1h1c1 1 2 3 3 5l-3-3c-1 2-1 3-1 4l2 3-2-1c-1-3-4-5-6-7-1-1-2-2-3-2l-1-1-1-2h-1l-1-1-2-1-2-2z"></path><defs><linearGradient id="Ay" x1="599.941" y1="531.063" x2="617.622" y2="541.441" xlink:href="#B"><stop offset="0" stop-color="#d3d2d1"></stop><stop offset="1" stop-color="#fffefd"></stop></linearGradient></defs><path fill="url(#Ay)" d="M605 531l-2-3c0-1 0-2 1-4l3 3c1 2 4 3 5 6l4 6c1 2 2 2 2 5h0l-6 4c-1-1-1-1-1-2h0l-1-1v-1h-1v-2l-1-1-1-2c0-1 0-1-1-2 0-1 0-1-1-3 1 0 0-2 0-3z"></path><defs><linearGradient id="Az" x1="289.673" y1="171.592" x2="276.343" y2="176.09" xlink:href="#B"><stop offset="0" stop-color="#4b4b4a"></stop><stop offset="1" stop-color="#757474"></stop></linearGradient></defs><path fill="url(#Az)" d="M277 139h1v-2c1 2 1 4 1 6v-3c0 17 3 36 9 53 2 4 5 9 7 13-1 4 0 5 1 8-1 0-2 1-2 2l-2-1c-1 0-3-6-3-7h-2c0-3-2-6-3-8l-6-17c-1-4-3-8-4-12 1-3 0-8 1-11 0-7 0-15 2-21z"></path><path d="M278 183l1-1c2 2 9 22 10 25v1h-2c0-3-2-6-3-8l-6-17z" class="J"></path><defs><linearGradient id="BA" x1="741.784" y1="462.789" x2="823.733" y2="447.8" xlink:href="#B"><stop offset="0" stop-color="#d3d2cf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#BA)" d="M742 422v-1h-1v-1l-2-1h-1-1c-1-1-2-1-3-1h0c2-1 3 0 5 0 1 1 2 1 3 1h1c6 2 13 5 18 10 12 9 21 21 33 32 5 5 11 9 17 14 8 6 18 11 23 19l-1 1c-2-1-4-3-5-4-3-1-6-3-8-4v1 1l-2-1c0-1-1-2-3-3-1-1-2-2-3-4-3-2-5-4-8-6h-1l-2-2-2-2c-4-2-6-6-8-8-3-1-7-5-9-8l-3-3c-1-1-2-3-3-4l-2-2c-1-1-4-6-6-7-1 0-2-2-3-3-2-1-1 0-2-1l-1-1-2-2c-2-1-1 0-2-1l-1-1c-1-1-2-2-3-2-3-2-6-3-8-5-2 0-2-1-4-1z"></path><defs><linearGradient id="BB" x1="546.548" y1="672.408" x2="547.934" y2="703.21" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#c8c8c5"></stop></linearGradient></defs><path fill="url(#BB)" d="M535 693c1-2 1-3 1-5 2-8 4-13 11-18 0 1-1 2-1 3l-5 11c7-5 12-5 20-5-1 4-2 11 0 14-6 4-12 8-18 11l-5 3c-1-2-1-2-1-4-1-2-2-4-2-7 0-1-1-2 0-3z"></path><path d="M537 703c1 0 2 0 4 1h2l-5 3c-1-2-1-2-1-4z" class="B"></path><defs><linearGradient id="BC" x1="608" y1="608.937" x2="617.138" y2="612.298" xlink:href="#B"><stop offset="0" stop-color="#d3d2d0"></stop><stop offset="1" stop-color="#f9f8f7"></stop></linearGradient></defs><path fill="url(#BC)" d="M619 555l1 1c0 2 0 4 2 6h0c0 1 0 2 1 3 0 1-1 3 0 5l2-10h3 0l1 2c-4 26-11 52-20 77-3 7-6 15-10 21h0c-1 0-1 1-1 1v1l-1-1c0-2 0-3-1-4 2-2 4-8 4-10 0-4 3-7 4-11h0l4-11 2-3 1-4v-2c1-1 1-2 1-3v-1c1-3 2-5 3-7v-1-1c1-2 2-4 3-5 1-5 2-9 2-14v-8c0 2 0 2-2 4 1-4 2-8 2-12-1-2-1-3-1-5v-4-4z"></path><path d="M622 562h0c0 1 0 2 1 3 0 1-1 3 0 5-1 7-1 13-3 20 0 2-1 5-2 8 1-5 2-9 2-14 1-4 1-8 1-12 1-3 0-7 1-10z" class="R"></path><path d="M619 555l1 1c0 2 0 4 2 6-1 3 0 7-1 10 0 4 0 8-1 12v-8c0 2 0 2-2 4 1-4 2-8 2-12-1-2-1-3-1-5v-4-4z" class="B"></path><path d="M619 555l1 1c0 2 0 4 2 6-1 3 0 7-1 10v-8h-1v4c-1-2-1-3-1-5v-4-4z" class="H"></path><defs><linearGradient id="BD" x1="627.281" y1="480.741" x2="639.049" y2="510.825" xlink:href="#B"><stop offset="0" stop-color="#d0cfcd"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#BD)" d="M620 489h5l4 1c1 0 2 1 2 1 1 0 2 0 3 1h2 0c5 2 9 3 14 4 1 0 3 0 5 1l11 2h10l-11 2h-7-3 0c-1 0-2-1-4 0l1 1v1h-6c-3 0-5 1-7 1h-11c-5-1-10-1-14-3l-2-1h-2l-5-2-1-1c0-1-1-2-2-2h-1l-2-1v-1l1-1c1 0 3 0 4-1l5 1 1-1 1 1 1-1c1 1 1 1 2 1l2 1h0l-1-1h0l-1-1 2-3c2 0 3 1 4 1z"></path><path d="M610 491l1 1 1-1c1 1 1 1 2 1l-5 1v2c-2 0-2 0-3-1h-2c-1 0-2-1-3-1h-1v-1c1 0 3 0 4-1l5 1 1-1z" class="C"></path><path d="M604 491l5 1c-2 0-3 0-5 1v1c-1 0-2-1-3-1h-1v-1c1 0 3 0 4-1z" class="G"></path><path d="M651 501c-1 0-3 1-5 0l1-1h-1-1c-4-1 1 0-2 0-1 0-2-1-3-1 2-1 7 0 10 0h0c1-1 2 0 3 0v1h2 1c2 1 5 1 7 0h5c0-1-1-1-2-1h10l-11 2h-7-3 0c-1 0-2-1-4 0zm52-122c15 3 30 8 42 16 6 4 13 11 14 17l-8-7c0 1 1 2 2 3l5 7c-4-2-8-6-12-9-9-6-20-10-31-11h-12v-16z" class="E"></path><defs><linearGradient id="BE" x1="925.663" y1="516.192" x2="866.293" y2="505.343" xlink:href="#B"><stop offset="0" stop-color="#d0cecf"></stop><stop offset="1" stop-color="#fcfbfa"></stop></linearGradient></defs><path fill="url(#BE)" d="M880 501l4 4c0-7 0-14 5-20h0l4 16c-2-2-3-4-3-7l-1-3c-1 10 0 20 6 28 5 7 12 12 21 14h0c6 0 12-1 17-1-11 5-24 5-35 0-7-3-11-7-16-12l-6-7c-4-4-7-9-9-14v-4h2l3 2 5 4h3z"></path><path d="M877 511c2 2 4 5 5 7v2l-6-7c0-1 1-1 1-2z" class="B"></path><path d="M867 499c2 0 2 1 3 2 2 4 5 7 7 10 0 1-1 1-1 2-4-4-7-9-9-14z" class="K"></path><path d="M709 311l1-1c2 0 4 0 7 1h4 3c2 0 4 1 5 1 7 2 12 6 18 9 4 2 7 3 11 6 3 2 5 5 8 7l9 9c5 4 9 9 15 10 3 1 5 0 8-1v1c-3 3-5 4-8 4-5 0-12-4-15-8-3-3-5-6-8-9-3-2-7-4-11-6s-7-4-11-5c-9-4-18-5-27-5-3-1-6-1-9-1-1 0-3 1-4 1 0-1 0-1-1-1 1-2 0-4 1-6 2-1 6 0 8 0 11 1 23 3 34 6-5-3-12-5-18-6-1-1-10-2-10-3-1-2-2-1-3-2-3-1-4-1-6-1h-1z" class="L"></path><path d="M603 554c-7 7-11 15-18 22v-39c0-4 0-11 1-15 1 1 2 2 4 3 0 1 1 1 1 2l1-1 6 6 2-1c2 3 4 8 5 12 1 3 1 5 2 8l-4 3z" class="C"></path><path d="M598 532l2-1c2 3 4 8 5 12 1 3 1 5 2 8l-4 3c-1-8-1-16-5-22z" class="E"></path><path d="M774 611c-6-15-20-28-34-35-24-11-53-8-77 3-3 1-6 2-8 4-3 2-5 3-8 5 4-4 7-7 11-10 9-7 22-12 33-14 21-3 43-1 61 12 8 6 15 14 20 24 0 1 1 1 1 2 1 3 2 6 2 9h-1z" class="L"></path><path d="M525 471c0-1 0-1 1-2v-1c0-1 1-3 2-4 0-1 0-2 1-3v-1l1-1v-1-2c1-1 1-1 1-2v-1c0-1 0-2 1-3v-1-2c0-1 1-2 1-2v-1c0-1 0-1 1-2h0l2-4c4 6 7 14 8 21l-2 5 2-2c1 2 1 5 0 7l-7 11c-4 2-7 8-8 12-2 2-3 5-3 8 0 2-1 3-2 5-1-1-1-1-1-2l-1 1c0 2-1 3 0 5 0 1 0 2-1 3s-1 1-1 2c-1 2-1 5-3 6v-1-6l-1-3c1-10 4-19 7-29 0-3 3-7 2-10z" class="E"></path><path d="M523 503l7-29c0-2 1-3 1-4h1v-2c0-1 1-3 2-4v-1c0-1 1-1 1-2h1c0 1 0 1-1 2 0 1 0 2-1 2 0 3-1 5-2 7v1l-1 1v3l-1 3c1-2 2-3 3-4l2-2c2-3 4-7 7-10l2-2c1 2 1 5 0 7l-7 11c-4 2-7 8-8 12-2 2-3 5-3 8 0 2-1 3-2 5-1-1-1-1-1-2z" class="I"></path><path d="M776 568l-10-9 1-1 5 2 5-1 6 6c10 10 19 24 21 38 1 4 2 9 1 13h0c2 20-6 39-18 55h0c8-14 16-33 13-50l-4-21c0-3-3-12-5-13 0 2 1 4 2 7 4 16 6 36-2 51 1-7 4-13 4-20 0-19-6-39-19-52 3 1 8 6 11 9-3-5-6-10-11-14z" class="D"></path><path d="M802 607h1c0-2 0-2 1-4 1 4 2 9 1 13-1-2-1-4 0-6h-1v3 2l-1-1-1-7z" class="C"></path><path d="M776 568l-10-9 1-1 5 2 7 6h0c-2-1-4-2-6-4l-1 1c1 0 1 1 2 1 1 1 2 3 2 4z" class="B"></path><path d="M772 560l5-1 6 6c0 2 0 4 2 6l2 3v1l-8-9-7-6z" class="N"></path><path d="M783 565c10 10 19 24 21 38-1 2-1 2-1 4h-1c-2-12-8-22-15-32v-1l-2-3c-2-2-2-4-2-6z" class="H"></path><path d="M665 603c4-1 9-2 13-3 15-1 32 0 44 9 5 3 11 8 14 14-11-11-26-14-40-14-10 0-18 2-25 8 8-1 16-1 23 0-13 1-29 3-40 11-4 3-7 7-11 10 6-11 19-20 28-27l-7 3h-1-1c-2 1-4 2-5 3 0-2 0-2-1-4 1-2 4-2 5-4h1-1c-1 0-3 0-4 1h-2 0-1l-1-2 2-2c2-1 8-2 10-3z" class="E"></path><path d="M655 606c2-1 8-2 10-3l-1 2c-1 0-2 1-2 2h1 3 0c-1 1-3 1-4 2h-1c-1 0-3 0-4 1h-2 0-1l-1-2 2-2z" class="C"></path><defs><linearGradient id="BF" x1="519.354" y1="675.586" x2="520.269" y2="709.479" xlink:href="#B"><stop offset="0" stop-color="#c6c5c4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#BF)" d="M506 676h0c6 2 16 3 21 0h2l1-1-1 6v5c0 2-1 3-1 4v3h0 1c1-3 1-6 1-9h1l3 1-1 1c0 2 0 3 1 5v1l1 1c-1 1 0 2 0 3 0 3 1 5 2 7 0 2 0 2 1 4-11 3-20 4-32 3 0-4 0-8 1-11v-5c-1-3-1-6-1-9-1 2-1 4-1 6v-2-9c-1 0-1-1-1-2 1 0 0-1 0-1l1-1h1z"></path><path d="M506 685c0-2-1-5 0-6h3c-1 3-1 5-1 8-1 2-1 5-1 7-1-3-1-6-1-9zm13-4c1-1 2-1 3 0l-2 11v2h0l-1-13z"></path><path d="M509 679c1 1 2 1 3 2 0 2 1 7 0 8h-1l-1-1c-1 0-2-1-2-1 0-3 0-5 1-8z" class="B"></path><path d="M689 590c7-3 15-5 23-7 3 0 12-1 14-2-3-2-7-2-10-2-10-1-21 0-32 3h0l1-1c-6 1-11 3-16 6h0c13-11 31-15 48-13 18 2 35 11 46 25 2 3 6 7 6 10v3c-1-1-2-1-3-2v-1c-1-1-2-1-3-2h-1l-1-2c-2-2-4-5-7-7-6-5-15-8-23-9-7-1-14-1-21 1-6 2-12 6-19 5h-1l3-3h0l-3 1h-3l-10 2c1-1 10-4 12-5z" class="E"></path><path d="M689 590l1 1h0c1-1 2-1 3-1l8-3 1 1c-2 1-4 1-7 2-1 0-4 1-5 2v1h-3l-10 2c1-1 10-4 12-5z" class="G"></path><path d="M725 248c12-11 20-24 26-39-2 17-2 35-17 46l-7 5 1-1 29-5c-1 1-3 3-5 4s-5 2-8 3c-5 2-10 4-15 5h-1c-1 0-2 1-4 1h-3v1h-3c-1 0-1 0-2 1h-2l-1 3h2 1c-1 1-2 1-3 1-2 1-5 2-6 3l-3 1c-2 1-5 2-7 3l-1-1h-2l-2-1c-5-2-10-4-16-5 2-1 2-1 5-1 1-1 2-1 2-2 1-1 3-1 4-1l10-2c9-2 20-4 28-9 11-7 16-16 19-27-6 11-13 21-25 26 0-1 1-3 1-4l5-5z" class="L"></path><path d="M697 269c4 0 9-2 13-2h0c-3 2-10 3-14 4l-2 1s0 1 1 1h1 1 1v1l-1 1h1l3-1 1 1c-3 1-7 2-10 3-5-2-10-4-16-5 2-1 2-1 5-1 1-1 2-1 2-2 1-1 3-1 4-1l10-2c-1 1-2 1-3 2h1 2z" class="H"></path><path d="M691 276c1-1 5-2 7-2l-1 1h1c-1 1-2 1-3 1h-1-3zm-4-7l10-2c-1 1-2 1-3 2h1 2c-3 1-8 1-10 0z" class="F"></path><path d="M691 276c-1 0-1 0-1-1-2 0-3-1-4-2-1 0-1 0-1-1 2 0 2 0 3-1h3 5l-2 1s0 1 1 1h1 1 1v1c-2 0-6 1-7 2z" class="I"></path><defs><linearGradient id="BG" x1="621.337" y1="502.7" x2="665.455" y2="517.995" xlink:href="#B"><stop offset="0" stop-color="#d9d6d6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#BG)" d="M587 490h-1l-1-7c2 1 3 2 4 4 5 3 9 4 15 4-1 1-3 1-4 1l-1 1v1l2 1h1c1 0 2 1 2 2l1 1 5 2h2l2 1c4 2 9 2 14 3h11c2 0 4-1 7-1h6v-1l-1-1c2-1 3 0 4 0h0c4 2 13 1 17 0 2 0 5-2 8-1-6 2-11 4-16 7-10 10-19 17-34 17-2-1-4-1-5-1l-2-1c0-1-1-1-1-1v-1c-1-1-1-1-1-2l-1-1h-2c-1 0-1 0-2-1h-1-1c-1-1-2-1-3-1-3-2-5-4-7-6-4-4-8-8-11-12-1 0-3-3-4-4l-2-3z"></path><path d="M655 501c4 2 13 1 17 0 2 0 5-2 8-1-6 2-11 4-16 7 1-1 1-2 3-2l1-1c1-1 1-1 2-1l1-1h-2c-2 1-4 1-6 1-2 2-6 1-9 2-1 1-2 1-4 1-1-2-4-1-5-1v-1l1-1h6v-1l-1-1c2-1 3 0 4 0h0z" class="D"></path><path d="M593 497c2 0 2 1 3 2 1 0 2 1 3 1s2 0 3 1 1 0 2 1c1 0 1 0 2 1h1c2 1 3 2 5 2l1 1h0c1 1 1 1 2 1l2 1c1 0 2 1 3 2h2l1-1h4l1 1h0-1c-1 0-2 0-2 1h0c2 1 5 0 7 3h2l2 1h3 2v1l-1 1c1 0 1 0 2 1h0c-1 0-1 1-1 1h-6v1c-2 0-3 0-5-1h-4c-2 0-3-1-5-1l-1-1h-2c-1 0-1 0-2-1h-1-1c-1-1-2-1-3-1-3-2-5-4-7-6-4-4-8-8-11-12z" class="J"></path><path d="M632 514c-3 0-6-1-8-1-1-1 0-1-1-1s-3 0-4-1h-1c-1 0-2 0-2-1l-2-1h-1l1-1 2 1c1 0 2 1 4 1h2l1-1h4l1 1h0-1c-1 0-2 0-2 1h0c2 1 5 0 7 3z" class="F"></path><defs><linearGradient id="BH" x1="613.103" y1="517.158" x2="635.266" y2="510.879" xlink:href="#B"><stop offset="0" stop-color="#a3a4a0"></stop><stop offset="1" stop-color="#d5d2d8"></stop></linearGradient></defs><path fill="url(#BH)" d="M630 519c2 0 4 1 5 0-1-1-4-1-6-1s-4 0-6-1h-2c-2-1-4-1-6-2h0c-3-1-4-4-6-6h2c0 1 1 1 2 2 1 0 3 1 4 2l4 1h1c1 1 2 1 3 1h3l5 1c2 0 5 0 6-1h2v1l-1 1c1 0 1 0 2 1h0c-1 0-1 1-1 1h-6v1c-2 0-3 0-5-1z"></path><path d="M587 490h-1l-1-7c2 1 3 2 4 4 5 3 9 4 15 4-1 1-3 1-4 1l-1 1v1l2 1h1c1 0 2 1 2 2l1 1 5 2h2l2 1c4 2 9 2 14 3h11c2 0 4-1 7-1l-1 1v1c1 0 4-1 5 1h0l-7 1h-7c-5 1-10 1-14 0h-4-1c-1-1-2-1-4-1l-1-1c-2 0-3-1-5-2h-1c-1-1-1-1-2-1-1-1-1 0-2-1s-2-1-3-1-2-1-3-1c-1-1-1-2-3-2-1 0-3-3-4-4l-2-3z" class="S"></path><path d="M639 504c2 0 4-1 7-1l-1 1v1c1 0 4-1 5 1h0c-3-1-6-1-9 0h-9c2-1 5-1 7-2z" class="B"></path><path d="M628 504h11c-2 1-5 1-7 2h-9c-2-1-5-1-7-1 3-2 8 0 12-1z" class="H"></path><path d="M587 490h-1l-1-7c2 1 3 2 4 4 5 3 9 4 15 4-1 1-3 1-4 1l-1 1v1l2 1h1c1 0 2 1 2 2l1 1 5 2h2l2 1c4 2 9 2 14 3-4 1-9-1-12 1-4 0-11-3-14-6-2-1-4-4-7-4-1-1-1-1-3-2h-3l-2-3z" class="G"></path><path d="M587 490h-1l-1-7c2 1 3 2 4 4s4 4 6 5c1 1 2 1 3 3-3-2-7-3-10-5h-1 0z" class="M"></path><path d="M589 487c5 3 9 4 15 4-1 1-3 1-4 1l-1 1v1l2 1h1c1 0 2 1 2 2h-1c-1 1-1 0-1 0l-1-1c-1 0-2 0-3-1-1-2-2-2-3-3-2-1-5-3-6-5z" class="C"></path><path d="M379 224c5-1 10 0 15 1l4 1c20 6 35 17 47 35-1 1-1 3-1 5h-1l-1 1c0 1 1 1 1 2 1 0 1 1 2 2l2 3c1 1 4 3 4 5-5-3-9-9-13-13-12-10-27-18-42-22-14-3-35-5-47 3-5 3-11 8-12 14 0 0 0 1-1 1v-1c2-9 8-15 15-21 3-2 7-2 11-3 9-2 18-2 27-1h1c-1 0-2 0-3-1-12-3-25-2-36 3l2-5c-1 1-3 1-4 2l-6 2 5-4c0-1 1-2 2-2 1-1 4-1 5-2 6-2 13-3 20-4l4-1z" class="E"></path><path d="M394 225l4 1c-2 1-2 1-5 1 1-1 1-1 1-2z" class="L"></path><path d="M379 224c5-1 10 0 15 1 0 1 0 1-1 2-3-1-7-1-9-2l-1-1c-2 1-2 1-4 0z" class="F"></path><defs><linearGradient id="BI" x1="544.658" y1="583.216" x2="595.26" y2="734.507" xlink:href="#B"><stop offset="0" stop-color="#c1c0c2"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#BI)" d="M634 557l1-4h1c0 1 0 1-1 2l1 2c-3 19-7 37-12 55-7 25-15 49-29 71-5 9-12 16-18 24h1l9-9 1 1c-9 7-15 17-20 27-1 0-2 0-2 1-2 0-3 4-4 5v1l-2 2c0 1-1 2-1 3l-2 2h-1 0v-1l-1-1c-1 1-1 2-2 3-1 2-6 6-8 8-1 0-2 1-3 2h0l-8 5c-10 5-23 5-33 2v-13c3 1 6 2 10 2 11 1 24-1 33-9 6-5 10-11 15-18l12-19c4-6 8-11 13-16 6-6 11-10 16-17 9-14 14-31 19-46 6-21 12-44 15-65z"></path><path d="M586 378h0l5 5c2 0 6 0 8 2l-7 7h1l11-8c2 1 5 1 7 3l-8 6c-3 2-5 4-7 6h1c1 0 2-2 3-3 2-1 3-3 5-4l7-5c2 0 5 0 6 2v1l-3 5c-2 3-4 6-6 8-3 5-4 9-8 13l5-15c-6 6-7 13-11 21 1-7 3-12 5-18-9 10-11 23-17 34 0-4 0-9 1-14 1-4 3-8 4-13-4 7-7 14-9 21 0 4-1 8-2 10h-3c-5-1-10 0-14 0 4-24 9-46 26-64z" class="E"></path><path d="M536 241c6-8 16-11 25-12 10-2 23-1 33 1h1c3-3 6-5 8-8 11-9 26-19 41-20 4 0 8 1 11 4l4 3c-14-3-27 0-39 7-5 4-10 8-15 13-2 1-4 3-6 5 0 1 0 2-1 3h0l-1-1c-4 3-6 8-9 12l-1-1v1h-1c1-2 1-2 1-4v-1c1-2 3-6 5-8 1-1 1-1 1-2h-1-1c-16 2-32 16-41 29-5 6-11 13-15 21-1 4-2 7-4 11h-1c1-6 4-10 6-15-4 4-5 8-7 13l-1 1c0-3 1-7 2-10-2 2-2 10-4 11-1-2 0-5 0-6l1-4-2-2c2-7 5-13 8-19l-1-3c0 1-1 2-2 4v1l-1 1h-1c1-2 2-5 3-7 1-1 1-2 1-3h1l-1-1-1-1h0l5-9h0c1-2 1-2 0-4z" class="T"></path><path d="M532 260c1-2 2-3 3-5h0l2-3c2-3 4-5 6-7s5-3 7-5c0 0 1-1 2-1h1l1-1h2l-1 2h0c-11 5-16 13-22 23l-1-3z" class="L"></path><path d="M556 238c1 1 1 2 1 3-2 2-3 4-6 6-1 1-1 2-3 2-2 3-5 5-8 9-6 7-10 17-13 26l-2-2c2-7 5-13 8-19 6-10 11-18 22-23h0l1-2z" class="P"></path><path d="M556 238c1 1 1 2 1 3-2 2-3 4-6 6-1 1-1 2-3 2-1 0-2 1-3 2-5 4-8 9-11 14 1-5 5-9 8-13 1-2 2-4 4-5 2-2 6-4 8-7h1 0l1-2z" class="F"></path><path d="M649 561c4-10 10-20 17-28 13-15 31-25 51-27 17-1 32 2 48 9 7 3 13 6 19 11-4-1-7-4-11-5-17-8-38-11-57-9 6 1 12 2 17 4-15-2-29 1-43 9h-1c1 1 2 1 3 1 1-1 1-1 2-1 2-1 5-2 7-2-1 1-2 1-3 1s-3 2-4 2v1c-11 5-22 13-30 22v1c5-5 10-8 16-11 11-6 23-11 35-11l1 1c-6 2-12 3-18 5-10 5-20 14-28 22-2 2-4 5-6 8l-3 3v1h1s1 0 2-1v1c-8 7-16 14-22 22l-5 9h-1c0-2 0-5 1-7v-3c1-2 1-4 2-6 1-1 1-3 1-5l9-17z" class="E"></path><path d="M645 577l8-11 1 1c-4 6-9 11-12 18-1 2-1 3-1 5h1l-5 9h-1c0-2 0-5 1-7v-3c3-2 4-5 5-8 1-1 2-2 3-4z" class="I"></path><path d="M640 578l9-17c0 5-4 11-4 16-1 2-2 3-3 4-1 3-2 6-5 8 1-2 1-4 2-6 1-1 1-3 1-5z" class="F"></path><path d="M585 258c11-13 21-23 37-30 5-2 10-3 15-4 12-1 26 2 37 6l6 3c0 1 1 1 1 2h0-1c-1-1-2-2-3-2h-1c0 1 1 2 0 3-2 1-9-2-12-2-3-1-7-1-10-1-8 0-15 2-23 4 5 0 9-1 13-1 6-1 13-1 20 0 3 1 6 1 9 3 4 1 7 4 9 6 5 4 6 7 8 12h0-1c-2-6-9-10-14-12-18-7-40-3-57 3l1 1c5-2 11-4 16-5 6-1 14-2 20-1h-1c-13 2-27 6-38 12-10 6-20 14-28 21h-1l-1 1-1 1c0-3 2-5 3-7v-1l9-8h0c-7 4-13 10-18 16-2 3-3 6-6 9 1-5 5-9 7-14v-1l7-7h0l-2 1h-1c0-3 0-4 2-6l-1-2zm143 8h9c12 1 26 3 37 10 6 5 13 12 17 18-4-1-7-5-10-8-14-10-29-16-46-15 23 3 44 14 58 33 5 6 8 13 9 20v1c1 7 0 17-4 23 1-13-4-25-13-35-10-11-25-15-39-15-8 0-15 3-23 5v-1l3-1c1 0 2 0 3-1l-1-1h-2l-1 1c-1 0-1 0-2-1l6-2c-3 0-6 0-9 1 0-1 0-2-1-4 9-3 19-5 28-4h5-2c0-1 0-1 1-2-1 0-2-1-3-1v-1h2v-1c1 0 3 0 4-1 0 0-1 0-2-1h0-1-1c-9-3-20-2-30-1-5 1-10 2-14 4h-1c-3-1-8-4-11-7h2l1 1c2-1 5-2 7-3l3-1c1-1 4-2 6-3 1 0 2 0 3-1h-1-2l1-3h2c1-1 1-1 2-1h3v-1h3c2 0 3-1 4-1z" class="E"></path><path d="M754 285h1c2 0 5 1 7 2 12 5 21 12 28 24-11-10-22-20-36-26z" class="R"></path><path d="M661 784c11 6 18 13 21 26 4 13 1 28-6 40-3 5-8 11-14 14 10-11 18-22 17-37-1-10-6-19-14-25-6-5-13-8-21-7-7 1-14 4-18 11-5 5-7 12-6 19 1 5 4 10 8 13 5 3 10 4 16 3 3 0 7-3 9-6s3-7 2-10-2-5-4-7c-2-1-3-1-5-1v1 1c1 2 2 4 1 6s-2 3-3 3c-3 1-5 1-7 0s-3-4-4-6c-1-3 0-5 1-7 2-4 6-5 9-6 5-1 10 1 14 4s7 9 7 14c1 8-2 16-7 23-4 5-10 7-16 8-7 0-13-2-19-6-7-7-12-16-12-26-1-8 1-16 7-22 7-9 20-12 31-16 3-1 7-3 11-4h2z" class="T"></path><path d="M311 304c-8-3-17-6-26-7-15-1-30 3-41 13-9 8-14 19-16 30v9c-2-7-4-13-3-21 1-15 11-29 22-38 14-11 34-21 52-19h0 0c-4 0-9-1-14 0-6 1-11 1-17 3-12 4-21 13-32 20 1-2 3-3 4-5 5-5 10-10 15-13 12-8 28-10 42-10-5-1-10-3-15-5-2-1-5-2-7-3s-3-3-4-4l28 5 1 1c3 1 9 4 11 3l1-1c12 4 25 6 37 6-4 2-8 2-12 1h-3 1 0c1 0 2 1 3 1 2 0 5 1 7 1 2-1 5-1 7-2 4-1 9-1 13-1 3-1 9 0 11-1-7-3-16-5-23-5-4 0-7 1-11 1 2-1 6-2 8-2 8-1 15-2 23 0 6 0 11 2 17 3 6 0 11 0 17 1h2v1l-1 1c1 1 2 0 4 1h1l3 2c1 0 2 1 4 1 1 0 1 1 2 1l1 1c3 1 6 3 8 5l-3 1h-9c-1-1-1-2-2-2s-1-1-2-1h-2c1 1 2 1 2 2s1 2 2 3h-1-2-3c-17-9-37-11-56-9-5 2-11 3-16 6l-4 2c-2-1-4-2-6-2 2 1 4 1 6 2l-12 7h0c-3-2-6-2-8-3-10-3-22-3-32 0l-2 1 4-1 1 2-4 2c1 0 1-1 2-1 5 0 10-1 15 0 2 0 4 1 6 2h0c-1 0-4-1-6-1 2 1 4 2 7 3 2 0 5 0 7 1l10 6c-3-1-7-2-10-3-5-2-12-6-17-7-7 0-14 1-20 2v1c6-1 13-2 19-1 4 1 8 2 12 4-1 0-1 0-1 1v1l1 1c-2 0-4-1-4-1 3 2 7 4 11 5 3 1 7 2 9 3l-10-1-1-1h-1 0l1 1-1 1z" class="E"></path><path d="M337 276c-2 0-3-1-4-1l-4-2c-1 0-1 0-2-1h0c-2-1-3-1-4-2h0 1c2 1 5 0 8 1h2 1c0 1 1 1 2 1l3 1 1 1v1c-1 1-3 1-4 1z" class="D"></path><path d="M337 272h9l3-1h2l2-1h2l1-1c1 0 1 0 2 1-1 0-1 1-2 1l-1 1c-5 2-11 3-16 6l-2-2c1 0 3 0 4-1v-1l-1-1-3-1z" class="F"></path><path d="M278 282h2c-16 5-28 15-41 26l-1 1v-1c3-4 6-7 10-11 8-7 19-11 30-15z"></path><path d="M759 370c-10-10-20-18-32-24-4-2-10-4-13-7h0c1-1 2-1 3-1 21 3 40 18 53 34h0v-1c-4-5-9-10-13-14-6-5-12-11-18-15-11-6-23-8-35-12v-6c5 1 9 2 13 4 8 3 16 6 23 10l13 9c4 3 8 4 12 7l12 6c3 2 7 3 10 4 2 1 5 1 8 2 8 4 11 10 13 18-1-2-3-5-6-7-4-3-9-4-14-6 2 4 4 7 5 10 5 12 8 25 15 36 5 6 13 11 21 12 10 2 22-2 31-8 11-8 19-21 21-34 1-12 0-27-7-37-5-7-13-12-20-13-8-1-16 1-21 6-6 4-9 11-9 18-1 6 0 13 4 18 3 3 6 4 10 5 3 0 6-1 9-3 2-2 2-5 2-8s0-5-2-7-3-2-5-1-2 4-3 7c0 1 0 1-2 1-1 0-2 0-4-1-1-2-2-4-2-6 0-3 1-6 3-8s5-3 9-3 7 2 9 5c4 3 5 8 5 14-1 4-3 9-7 12-3 3-9 4-14 4-5-1-10-4-13-8-5-6-6-15-5-23s5-15 12-20 17-8 25-7h3 1c2 1 3 1 4 2h1c2 1 4 3 6 4l2 2h1l2 2h0l6 9c7 12 10 26 8 40-1 10-5 24-12 32h0l8-9v1c-3 5-8 11-14 15-2 1-4 3-7 5s-5 6-8 9c-4 4-10 7-15 8-17 4-35 1-50-8-5-3-9-7-12-11 3 2 5 5 8 7 13 8 30 11 45 10 13-2 21-7 30-16-16 5-31 5-46-3-4-3-8-6-12-10l4 6c0 2 2 3 3 5l1 1h-1c-2-2-3-3-5-3-2-2-4-4-5-6h-1c3 4 5 7 9 11-4-3-7-5-10-8-14-14-19-36-33-51 0-2-3-4-5-5 0-1 0-2-1-3v1l-1 1zm-461-57c2 1 6 0 8 0h4l3 1c-11 2-24 5-34 12l22-7h1c3 1 5 1 8 0h2 1c2-1 3-1 5-1 3 0 5 0 8-1 2 1 5 0 8 0h1 3s1 1 1 2l-1 2c0 2-1 2-2 3l-3 2h-1v1c-13 4-27 6-39 13-9 4-15 10-22 16-5 5-11 10-14 16v2c12-15 26-27 44-34 4-1 8-3 13-3h2l-17 9c-9 5-18 12-26 19-3 4-8 8-9 12l2-2v1 1h1l1 1c1 0 2-1 2-2 1 1 2 1 2 2s-1 3-2 4v3c-2 3-4 5-4 8 2-2 3-5 5-7l14-18 1 1c-5 7-9 15-15 22l-11 13c-7 10-13 19-22 27-3 3-6 5-9 7l-1-1 3-3c0-1 0-1 1-2 3-3 5-6 8-10-1 0-1 0-1-1h-1c10-14 16-29 22-45-6 7-10 16-14 25-4 6-8 13-13 19h-1l-2 2c0 1-1 2-2 2v-1h-1c1-2 2-2 2-4l-3 3-13 8c-15 9-31 9-47 5h1c7 9 18 14 29 15 15 2 37-3 49-13 0 0 1-1 2-1-1 1-1 1-1 2-2 2-6 4-8 6-10 6-20 9-31 10-13 1-26-1-35-10-3-3-5-7-8-9-3-3-7-4-9-7-12-9-15-21-17-35-1-15 1-32 10-45 3-3 6-5 8-9h1 0c0-1 1-1 2-2h1l2-2c2-1 3-2 5-2h1c0-1 0-1 1-1 3 0 5-1 8 0 6 0 11 2 17 4 3-1 6 0 9-1 3 0 6-1 9-1l-2 1c-5 3-9 3-14 2h-1a30.44 30.44 0 0 1 8 8c5 7 7 18 5 26-1 6-5 12-10 16-4 3-10 3-15 2s-8-3-10-7c-3-5-3-11-2-16 1-4 4-8 8-10 3-1 7-2 10 0 3 1 5 3 7 6 1 2 1 5 0 7s-2 3-4 4h-4c0-2 0-3-1-4 0-3 0-3-2-4s-3-1-5 1c-2 1-3 4-3 6 0 3 1 7 4 9 2 2 5 3 8 2 4 0 7-2 10-4 4-6 5-13 4-20-1-6-3-12-8-16-6-5-15-7-22-6-8 1-15 7-20 13-7 10-8 24-6 36 2 14 10 27 22 35 8 6 19 10 30 8 18-3 25-19 31-35 2-8 4-15 8-22h1c-9 1-16 6-21 12 1-5 4-9 7-13 7-7 16-6 25-11l24-13 16-10c5-3 9-5 14-7 11-5 20-8 32-8l1-1h-1-4l-11 1c-6 1-12 1-18 2-13 2-27 6-38 14-7 4-11 10-17 14-4 3-9 6-14 5-2-1-4-2-5-4h6c7-1 12-6 17-10l8-8c4-3 6-6 10-9l12-6c5-3 11-5 16-7z" class="E"></path><path d="M266 393c2-2 3-5 5-7l14-18 1 1c-5 7-9 15-15 22 0-1 1-2 1-3l1-1v-1l2-1v-1l1-1h-1c-2 2-4 5-6 7-3 3-5 6-7 9-1 0-2 1-3 1s-1 0-1-1h0c2-1 4-1 5-3l3-3z" class="D"></path><defs><linearGradient id="BJ" x1="273.117" y1="381.065" x2="256.915" y2="392.258" xlink:href="#B"><stop offset="0" stop-color="#b3b1b2"></stop><stop offset="1" stop-color="#d3d2d0"></stop></linearGradient></defs><path fill="url(#BJ)" d="M264 377l2-2v1 1h1l1 1c1 0 2-1 2-2 1 1 2 1 2 2s-1 3-2 4v3c-2 3-4 5-4 8l-3 3c-1 2-3 2-5 3v-1c-1 0-1 1-2 1h0c1-2 2-4 3-5l1-2 1-4v-2c0-2 0-3 1-4 0-2 0-3 2-5z"></path><path d="M264 377l2-2v1 1h1l1 1c-1 1-2 2-2 3h-1c0 1-1 2-1 3l-1 1c-1 1-1 2-1 3h-1v-2c0-2 0-3 1-4 0-2 0-3 2-5z" class="F"></path><path d="M697 78l-31 27c-17 16-33 37-43 59-1 4-5 10-5 15 7-15 17-26 27-39l15-19c13-15 27-30 43-44 11-10 24-21 39-26 4-1 9-2 13 0 5 3 7 10 8 15 3 10 3 21 3 31 1 32-3 65-12 95-5 15-12 29-18 42h-4c-1-1-1-1 0-2 1-2 2-4 2-5-5 7-10 14-19 16h-1c10-7 17-17 22-27h-1l2-3c1-2 3-5 3-7 1-2 2-3 2-5v-2l7-18v3 2l2-6 1-4 5-22c1-4 2-8 2-12l1-18c2-14 3-28 2-43-1-5-2-12-5-16-2-2-4-3-6-4-3 0-7 1-10 3-10 5-19 14-27 22-5 4-9 8-12 13-10 15-23 26-35 39h0c8-5 16-12 22-19 4-6 8-12 12-17-4 12-10 22-19 30l-18 15c-8 9-14 18-23 26h1c6-4 11-8 16-13s9-10 13-15c3-3 6-5 8-7 5-4 9-6 16-5 3 0 5 2 7 5 0 1 1 1 1 2l1 2c-8-3-14-3-22 1-6 2-10 8-14 13s-8 10-13 14c-5 5-11 8-16 13 11-5 20-10 33-10 2 0 5 0 7 1s4 2 5 4c-9-2-15 0-23 4 8 0 21-1 27 5 2 1 3 3 4 5-3-2-4-4-7-5h-1c-9-3-19 0-28 3l11 2c-5 0-9-1-14 0-11 3-22 9-31 14-6 4-12 7-17 12 3-4 5-7 7-12l-6 7-5 5c-5 0-6 5-9 5l-1-1 1-4s0-1 1-2c0-1 1-2 1-4l1-2c1-1 1-1 1-2 2-8 7-16 11-23 9-17 18-35 29-50 7-10 15-19 23-28 5-5 11-10 16-15 3-2 7-4 9-7h1c0-1 1-1 1-2h1c1-1 2-2 3-2l5-3v-1l2 1z" class="E"></path><path d="M601 208c0-1 1-1 2-2v1 2c-1 3-1 4-3 6l-1-1h-1 0v-1l3-5z" class="F"></path><path d="M601 208c2-3 5-6 7-8 1-2 1-4 2-5l1 1-2 3c-1 4-4 7-6 10v-2-1c-1 1-2 1-2 2z" class="B"></path><path d="M600 215l-1 1h0 1l1-1h0c1-2 3-2 5-2l-5 5c-5 0-6 5-9 5l-1-1 1-4s0-1 1-2c0-1 1-2 1-4v1c0 3-2 6-2 10 2-2 3-3 4-5 0-1 0-2-1-2l1-1c1 0 1-1 2-2v1h0 1l1 1z" class="I"></path><path d="M749 181v3 2c-2 7-5 15-8 22-1 2-3 6-5 8h-1l2-3c1-2 3-5 3-7 1-2 2-3 2-5v-2l7-18z" class="J"></path><path d="M708 419c8 0 16-2 24 0 1 1 3 1 5 1 2 1 4 1 5 2 2 0 2 1 4 1 2 2 5 3 8 5 1 0 2 1 3 2l1 1c1 1 0 0 2 1l2 2 1 1c1 1 0 0 2 1 1 1 2 3 3 3 2 1 5 6 6 7l2 2c1 1 2 3 3 4l3 3c2 3 6 7 9 8 2 2 4 6 8 8l2 2 2 2h1c3 2 5 4 8 6 1 2 2 3 3 4 2 1 3 2 3 3l1 1h0c2 3 3 7 5 10 3 7 6 14 8 21-2 0-2 0-4-1l2 4v1c2 3 2 6 4 8h1c2 5 3 11 5 17 3 16 6 32 3 48-1 6-3 10-4 16h0l-1 7v-5c-2 6-2 12-3 18-1 4-2 9-2 13v-3l-2-1c1-8 1-15 1-23s-1-16-2-24l-8-27-5-10-3-6-5-11c-9-14-22-30-36-40l-2-1c0-1 0-1-1-1-1-2-4-4-6-5v1c-1 0-1 0-1 1-2-2-2-3-5-3-6-4-12-7-18-9h0c-1 0-2-1-3-1s-1-1-2-1c-2 0-4-1-7-1-3-1-7-1-10-1-2 0-2-1-4 0l-3-2h-4v-1c0-1-1-2 0-3h8c-2-1-5-1-8-1-1-1-1-6-2-8 3 0 6 1 9 1l-9-3v-2l1-1c10 0 20 1 30 3l15 4c-2-1-5-2-7-3-13-5-25-6-38-5h-1v-5c3-1 7 0 10-1h-2c-2 0-7 1-8 0l-1-4v-4c2-1 5-1 7-1 8 0 16 0 24-1-10-2-20-4-30-3h-1c-1-1 0-2 0-4l-1-11 10-2c-2-1-8 1-10 0v-3c1-1 3-1 5-1z" class="E"></path><path d="M755 481c3-1 8 0 12 0l12 2h-4c-2-1-6-1-8-1-4-1-9 0-12-1z" class="D"></path><path d="M726 448c5-1 11-1 16 1h1c1 0 1 0 2 1h-1c-6-2-11-2-18-2h0z" class="B"></path><path d="M704 449v-4l1 2h6c5 1 10 0 15 1h0l-22 1z" class="F"></path><path d="M817 558h2s0-1-1-2c0-1 0-2 1-3h0c1 3 4 11 4 13 0 1-1 1-1 2l-5-10z" class="L"></path><path d="M822 509c-4-6-7-11-11-16-8-9-19-16-31-18-10-2-21-2-31-1 8-4 20-6 29-5-6-8-13-17-22-23-4-3-9-5-14-7-8-3-16-4-23-7-1-1-3-1-4-2 16-2 34 1 47 12 9 7 17 16 25 24 6 5 12 8 17 13 11 11 18 26 24 40l2 4c-2 0-7-12-8-14z"></path><path d="M833 587c0-5-2-12-3-18-3-20-10-42-21-60l-13-19c9 5 17 15 23 23 3 1 4 5 5 7l7 14 1-1-1-1c0-2 0-2-1-3l-1-2c0-2 1 0 0-1v-1l-3-6-3-6c-1-1-1-3-1-4 1 2 6 14 8 14v1c2 3 2 6 4 8h1c2 5 3 11 5 17 3 16 6 32 3 48-1 6-3 10-4 16h0l-1 7v-5c-2 6-2 12-3 18-1 4-2 9-2 13v-3l-2-1c1-8 1-15 1-23s-1-16-2-24h1v2 5c1 1 0 4 1 5v5-1c1-1 1-7 1-8l-1-1c0-2 1-4 0-5 0-4-1-7 1-10z"></path><path d="M833 587c1 9 1 18 0 27 0 9 1 19 0 29l-2-1c1-8 1-15 1-23s-1-16-2-24h1v2 5c1 1 0 4 1 5v5-1c1-1 1-7 1-8l-1-1c0-2 1-4 0-5 0-4-1-7 1-10z" class="D"></path><path d="M822 509c1 2 6 14 8 14v1c2 3 2 6 4 8h1c2 5 3 11 5 17 3 16 6 32 3 48-1 6-3 10-4 16h0l-1 7v-5l1-7c2-6 2-13 2-20-1-15-3-32-8-47-4-10-9-19-14-28 3 1 4 5 5 7l7 14 1-1-1-1c0-2 0-2-1-3l-1-2c0-2 1 0 0-1v-1l-3-6-3-6c-1-1-1-3-1-4z" class="G"></path><path d="M261 95c2-12 0-28 7-39 2-3 5-5 9-6 4 0 8 1 12 3 14 6 25 15 37 25h2c1 0 5 3 7 3l6 3c19 12 38 31 50 50 11 15 19 32 28 48 4 9 10 18 14 28 0 2 1 3 2 5l1 3c1 1 0 1 1 2 0 1 1 2 2 3l2 5-1 1c-2 0-3 1-5 0l1 1c3 0 7-1 11-1 15-2 33 0 44 12 12 11 19 36 21 52h0c-1-4-2-10-4-13 0 3 1 7 2 10h-1 0c0-3-1-5-2-7-1-5-2-10-5-14 1-1 0-2 0-3h-1c-1-3-2-6-4-9v1 1h0c-3-3-5-7-8-10v1l1 1-1 1c-4-5-9-8-15-12v1c3 2 8 5 11 8 5 5 8 10 10 16h0c-3-6-7-12-12-16h-1c6 6 12 14 15 22h0v4 1c-1 2 0 4 1 6l3 11h0l-5-12h0l2 9 1 3c-1-3-3-6-4-9-1-2-1-5-2-7v4c1 4 4 9 4 12-2-1-3-7-5-9-3-7-7-13-11-18l-3-3c-10-13-18-25-35-29 1 1 2 2 2 3l1 1h1c1 1 2 3 3 5 3 6 5 14 6 21l1 1h0v1c1 1 1 2 1 3 0 3 1 5 2 8 0 1 0 1 1 3l1 3c1 2 1 5 1 8 1 1 1 3 1 4-3-4-4-7-6-12l-1-2-18-38c-2-3-4-9-8-11l7 14-6-9c-8-9-17-18-29-24-10-5-23-7-34-3 2-4 4-6 8-7 10-4 22 2 31 6l-1-2c-9-7-21-13-33-15-4-1-9 0-13 0 3-2 8-2 11-2-7-3-13-4-21-4-6 0-11 2-15 6v1h-1c1-2 2-4 4-6 7-6 19-6 28-5l-4-2c-7-4-15-4-22-2 2-2 5-4 8-4 14-4 28 4 39 10-4-5-10-9-14-14-11-11-17-26-33-30-6-1-11 0-16 3h0-1c0-2 1-3 2-4 1-3 4-5 8-6 7-2 13 2 19 6-8-7-16-15-21-24-2-4-4-8-5-12 4 6 8 13 13 19 6 7 13 13 21 18 0-1 0 0-1-1-9-8-17-17-24-26-6-7-10-15-16-21-6-7-14-14-21-20-6-4-15-11-23-10-2 0-5 2-6 4-3 4-4 11-4 17-2 14-2 28 1 42 1-7 2-12 3-19 1-1 1-1 1-2v1 2c-1 3-2 7-2 10l-1 13c-1 5 1 19 2 24l4 17c1 4 3 8 4 12l6 17c1 2 3 5 3 8h2c0 1 2 7 3 7l2 5c-3-1-4-4-6-7 0 2 0 3 1 5 1 1 1 2 1 3l1 2 2 4 2 2h0v2c0 1 1 1 1 2-2-1-3-2-4-2l-3-2c-7-15-13-31-18-47-8-28-10-57-10-87z" class="T"></path><path d="M287 208h2c0 1 2 7 3 7l2 5c-3-1-4-4-6-7 0-1-1-2-1-3v-1l1 2h1l-2-2v-1zm148 21h-1c0-1 0-1-1-2-2-2-3-3-4-5l-7-6-1-1 1-1 2 3c0-1-1-2-1-3v-1l1 2c2 0 2 1 3 1l1 1c0-1 0-2 1-3 1 4 2 6 4 9h2l4 3c-1-1-2-3-2-5h0l-2-2h0v-4l1 3c1 1 0 1 1 2 0 1 1 2 2 3l2 5-1 1c-2 0-3 1-5 0z" class="B"></path><path d="M441 240v-1l-4-6h1c6 7 10 18 14 27 1 1 1 3 2 4 0-1 0-2-1-3l-6-17h0l1 1c0-1 0-2-1-3h1c0 1 1 2 1 3 1 0 0 1 0 2l3 6c1 3 2 9 4 11l1 1h0v1c1 1 1 2 1 3 0 3 1 5 2 8 0 1 0 1 1 3l1 3c1 2 1 5 1 8 1 1 1 3 1 4-3-4-4-7-6-12l-1-2-18-38c2 1 5 8 6 10h1v-2l-1-2-3-6c0-1-1-1-1-2v-1z" class="P"></path><path d="M441 240c6 8 9 20 13 29h-1v1l4 9v2l-18-38c2 1 5 8 6 10h1v-2l-1-2-3-6c0-1-1-1-1-2v-1z" class="C"></path><path d="M326 78h2c1 0 5 3 7 3 5 5 10 10 16 14 17 15 33 31 44 51 7 11 11 22 15 34-2-4-4-8-7-12-5-8-12-14-17-22l-27-34c-10-12-22-23-33-34z"></path></svg>