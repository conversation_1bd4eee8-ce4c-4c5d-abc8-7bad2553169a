<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="108 54 838 904"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#511617}.C{fill:#c2c1c1}.D{fill:#d8d6d6}.E{fill:#979497}.F{fill:#a4a1a4}.G{fill:#b0aeb0}.H{fill:#79292d}.I{fill:#b9b7b8}.J{fill:#952123}.K{fill:#dcdad9}.L{fill:#949093}.M{fill:#a32b2e}.N{fill:#cecdcd}.O{fill:#a9a7a9}.P{fill:#451e22}.Q{fill:#6d1618}.R{fill:#e16463}.S{fill:#6a1618}.T{fill:#6d282c}.U{fill:#a22227}.V{fill:#401e22}.W{fill:#540f10}.X{fill:#c8c7c7}.Y{fill:#340d0f}.Z{fill:#080404}.a{fill:#e27675}.b{fill:#8d2e31}.c{fill:#2f1718}.d{fill:#160b0c}.e{fill:#d62e2e}.f{fill:#413942}.g{fill:#4c1314}.h{fill:#f2f1ef}.i{fill:#858287}.j{fill:#db3333}.k{fill:#8b888e}.l{fill:#282125}.m{fill:#e1e0dc}.n{fill:#8e1315}.o{fill:#e14645}.p{fill:#d93535}.q{fill:#6c1e1f}.r{fill:#bb2d31}.s{fill:#30252b}.t{fill:#80181b}.u{fill:#951518}.v{fill:#7a757c}.w{fill:#48191a}.x{fill:#62333a}.y{fill:#4a1b1d}.z{fill:#e6e4e2}.AA{fill:#e46461}.AB{fill:#6f6a71}.AC{fill:#8b373b}.AD{fill:#cc3133}.AE{fill:#b32629}.AF{fill:#6e6a71}.AG{fill:#b41c22}.AH{fill:#666168}.AI{fill:#9e9699}.AJ{fill:#eea3a3}.AK{fill:#2c2329}.AL{fill:#1d0d0d}.AM{fill:#49434c}.AN{fill:#251415}.AO{fill:#e48281}.AP{fill:#bf1920}.AQ{fill:#131114}.AR{fill:#1e191c}.AS{fill:#a3595c}.AT{fill:#da9a9b}.AU{fill:#8a464b}.AV{fill:#e45d5d}.AW{fill:#f6b8b8}.AX{fill:#fcfcfb}.AY{fill:#c07172}.AZ{fill:#523941}.Aa{fill:#f29593}.Ab{fill:#f4d1d2}.Ac{fill:#be8385}</style><path d="M229 360h-1c1-1 3-2 5-3l-2 3h-2z" class="h"></path><path d="M375 580l1 2-2 1-1-3h2z" class="AD"></path><path d="M652 140c1-1 1-1 2-1 2-1 4-1 5 0h-2v1h-3-2zm159 452c1 3 2 6 1 9l-2-4v-1l1 1v-1-4z" class="h"></path><path d="M528 169c0-1 1-1 2-1v4c-1 0-2 1-2 1v-4z" class="t"></path><path d="M465 424c2-2 4-3 7-4l1 1c-1 1-2 1-3 2-1 0-3 1-5 1z" class="i"></path><path d="M227 364c-1 1-4 1-5 2-1-1-1-1-2-1h0l6-3 1 2z" class="h"></path><path d="M189 299c2 0 4-1 6-1l3 1-4 2c-2-1-3-2-5-2z" class="Aa"></path><path d="M258 329v3l-2 4-1 3v-5c1-1 2-3 3-5z" class="I"></path><path d="M830 291v-1l3 3 2 3-3 2c-1-3-2-5-2-7z" class="Z"></path><path d="M868 298c3 0 5-2 7-3v3l-1 1c-2 0-3 0-5 1l-1-2z" class="a"></path><path d="M276 429c2 2 4 6 4 9h-2c-1-3-2-6-2-9z" class="K"></path><path d="M246 799l1 4v5 1c-1-1-1-2-2-3v-1h-1v-1c0-1-1-1 0-2h1 0v-2l1-1z" class="m"></path><path d="M362 571c2 0 3 1 5 1 1 2 2 3 4 4l1 3c-2-1-2-3-5-4l-4-2-1-2z" class="AI"></path><path d="M439 130c2-1 5 0 7-1l-1 4-5 1v-3l-1-1z" class="B"></path><path d="M588 342l1 1-3 3-3 2c-1-1-1-2-1-3 1-2 4-2 6-3z" class="C"></path><path d="M601 131l8 1c1 0 3 1 4 1l-2 1c-1 1-1 1-1 2-1 0-2-1-3-1v-2l-1-1h-4-1v-1z" class="n"></path><path d="M446 129h8l-4 3-5 1 1-4z" class="u"></path><path d="M563 393c1 0 1 0 2 1-1 1-2 3-3 5 0 1-2 3-3 4l-2-2 3-2v-1c1-1 1-1 1-2 1-1 2-2 2-3z" class="h"></path><path d="M530 172c0 4 0 8 2 12l1 4a24.56 24.56 0 0 1-5-15s1-1 2-1z" class="Q"></path><path d="M528 169c0-4 1-8 3-12 0 1 1 1 1 2v2 1c-1 2-2 4-2 6-1 0-2 0-2 1z" class="M"></path><path d="M829 141c4-1 8-3 12-4l3 1-12 4h-3v-1z" class="Aa"></path><path d="M531 152h2c1 3 0 4 0 6l-1 1h0c0-1-1-1-1-2v-2c-3-1-5-1-7-2l7-1z" class="n"></path><path d="M201 291c2-3 3-7 3-10v-8c2 6 2 11 1 17h-1c0-1-1 0 0-1v-1h0c-1 2-2 2-3 3z" class="AW"></path><path d="M524 153h-3v-1c5-1 10-3 15-6-1 1-2 3-4 3l1 1c-1 0-2 1-2 2l-7 1z" class="o"></path><path d="M786 334c1-2 1-3 3-5v1h1l-1 15c-1-2-1-3-1-5h0v-1c1-2 0-2 0-4h-1v2 1 2l-1-1c0-2-1-4 0-5z" class="N"></path><path d="M735 626c0 2 1 2 1 3v1h1l1 1c2 1 1 3 2 4v2 1c0 2-1 9 0 10v1 1 2 2c-3-4 0-13-1-18-1-1-1-3-2-4s-1-1-3-1v-1h1v-4z" class="D"></path><path d="M187 309l3-2c3-2 5-4 8-4-3 4-7 7-11 9v-3z" class="b"></path><path d="M573 410c-2-3-4-8-4-11 1 1 2 2 2 4v2h1l3-4 1 1v1c-1 1-1 2 0 4-2 1-3 2-3 3z" class="D"></path><path d="M793 788c1 2 1 5 1 9-2 4-4 8-7 12h0c1-2 2-4 2-6 2-5 3-10 4-15z" class="C"></path><path d="M246 350c0-5 2-12 3-17v11c1-1 1-2 1-2 0-1 2-2 2-3l-3 9v1h-1l-1-2-1 3z" class="D"></path><path d="M830 291c-2-4-1-10-1-13l2-6h0c-1 7-1 12 2 18h0v3l-3-3v1z" class="AS"></path><path d="M310 462l-1-1c0-1-1-2-1-4s-2-5-3-7h1l1 1v-1c1 1 2 3 2 4 1 1 0 1 1 2v2l1 1v2c0 1 0 1 1 2v3h0v3l1 1c0 1 0 3-1 4-1-4-1-8-2-12h0zm291-170h0l-1 2h-1v-2c1-1 1-1 1-2v-2c-1-2-1-3-2-5 1 0 3 1 4 1 2 1 4 1 6 2v2c-1-1-3-1-4-1l-1 1-1 1c0 1 0 2-1 3z" class="h"></path><path d="M360 782l1 1v3c1 1 1 1 1 3h1v3l1 1h0l1 1v9c-1 0-1-1-1-2v-1c0-1-1-2-1-3v-1l-1-1v-2c-1-1 0-1-1-2h0v-2c-1-2-1-2-1-3v-2l-1-1 1-1zm-46-269l1-2c1 1 2 1 2 2l2 2h1c1 1 1 3 2 4s1 1 1 2h0v1c-1 0-1 0-2-1v2h-1c-1-3-2-5-4-7 0-1-1-2-2-3z" class="D"></path><path d="M201 291c1-1 2-1 3-3h0v1c-1 1 0 0 0 1h1c0 1-1 4-2 5-2 0-3 3-5 4l-3-1c2-2 4-4 6-7z" class="AJ"></path><path d="M577 405c1 3 2 6 2 9-1 1-1 2-2 2l-2-2-2-4c0-1 1-2 3-3h0l1-2z" class="G"></path><path d="M576 407h0c1 1 1 3 1 4-1 1-1 2-2 3l-2-4c0-1 1-2 3-3z" class="F"></path><path d="M242 301c-4 1-8 0-12-1-2 0-3-1-5-1-3-1-4-1-6-3l1-1c0 1 1 1 2 1l1 1h0 3c1 1 1 1 2 1 1 1 3 0 4 1v1h1 1c1 0 2 0 3 1 0-1 0-1 1-2h4 5c-2 1-2 1-3 1-2 0-1 0-2 1zm366-15c5 1 11 1 16 0 3 0 6 0 8-1h1c1-1 2-1 3-1v1c-8 4-19 4-28 3v-2z" class="D"></path><path d="M490 198s-3 1-4 1c-4 1-11 0-14-2 1 0 3 1 4 1h7c5-2 7-5 9-9 1 4-1 6-2 9z" class="R"></path><path d="M251 304h2c-2 3-5 5-6 8h0 0l-2 1c0 1-1 1-1 1h-1v-6c3-2 4-3 8-4z" class="K"></path><path d="M282 459c-4-1-7-2-10-3l-2-2c-1 0-2-1-3-2-2-1-3-2-3-3h1v-1c0-1 1-2 1-2v-1l1-1h-1l1-1h3c-1 1-3 3-3 5 0 1 1 3 2 4 3 3 9 4 13 7z" class="m"></path><path d="M493 183c2 2 2 2 2 4 1 2 1 3 2 5l1 1c-3 2-5 4-8 5 1-3 3-5 2-9l1-6z" class="b"></path><path d="M399 138c4 0 7-1 10-2 4-1 8-2 12-2l-1 1-13 4c-3 0-7 1-10 3l-4-1 2-1c2 0 3-1 4-2z" class="AO"></path><path d="M421 134l18-4 1 1v3h-2c-1 0-3 1-4 1s-3 0-4 1l-1-2h-2c-2 0-5 1-7 1l1-1z" class="W"></path><path d="M431 133h1c2 0 4-2 6-1v2c-1 0-3 1-4 1s-3 0-4 1l-1-2h-2c1-1 2-1 4-1z" class="B"></path><path d="M431 133h2l1 2c-1 0-3 0-4 1l-1-2h-2c1-1 2-1 4-1z" class="Y"></path><path d="M310 462h0c1 4 1 8 2 12v13l-3 12v-1-1l-1-1c1-2 2-7 2-9 0-3 0-6 1-9 0-5-1-11-1-16zm-87-161l1 1v1c1 2 4 3 7 4 1 0 2 1 3 1 3 2 6 3 9 6l-1 1-1-2h-1l-1-1c-1 0-1-1-2-1h0c-1-1-1-1-2-1l-1-1h0c-1-1-2 0-3-1s0 0-2 0h0l1 2c1 1 1 1 1 2-2-1-3-3-4-5h0l-1-1v1h0v1h1v1c1 1 2 2 2 3 1 1 2 3 3 5h-1c-1-1-1-2-2-3s-2-2-3-4-1-4-2-5l-1-2v-2z" class="m"></path><path d="M321 523l1 1v1 1 1c2 1 3 0 4 1h-3c-3-1-6-4-7-7h-1v-2c-1-3-3-8-2-10 1 1 0 2 1 4 1 1 2 2 2 3 2 2 3 4 4 7h1z" class="X"></path><path d="M577 405c1 0 1-1 3-1 0 1 1 2 1 4l1 1c1 1 0 3 0 5h0c0 2 0 2-1 4v1c0 1 0 1 1 2l1 1v1c1 0 1 0 2 1-2 0-5-4-7-6 0 0-1-1-1-2 1 0 1-1 2-2 0-3-1-6-2-9z" class="N"></path><path d="M789 768h1v1l1-1c2 5 4 11 4 17v7l-1 2v3c0-4 0-7-1-9-1-3-1-6-1-8 0-1-2-9-2-10l-1-2z" class="K"></path><path d="M792 780c1 0 2 1 2 1v3 10 3c0-4 0-7-1-9-1-3-1-6-1-8z" class="N"></path><path d="M471 353c0-1 1-1 2-2 2-2 4-3 6-5 3-1 5-1 7-3 3-2 6-6 9-9h0c-2 4-5 7-7 10-2 2-3 3-5 4-3 1-5 2-7 4-3 0-3 0-5 2v-1z" class="G"></path><path d="M582 364v-3c-1-1-1-2-2-3v-2l-1 1c1 1 1 1 1 2l-1-1c-2-4-5-7-9-9l-1-3h1c1 1 3 1 4 2 2 2 5 4 7 7l1 2c1 1 1 1 1 2v1c2 3 2 5 2 9-2-2-2-2-2-4l-1-1z" class="z"></path><path d="M673 814c0-11 1-22 5-32l1 1h0l-1 4h-1c0 2 0 4-1 5v3 1 4h0v1c0 1 0 2-1 3h0c0 3 1 0 0 2v1l1 1v1l-1 1v1c0 1 0 1-1 2l1 1h0v2 1c0 1 0 1 1 2v1l-2-1c-1 0-1-3-1-5z" class="D"></path><defs><linearGradient id="A" x1="673.431" y1="807.34" x2="674.515" y2="827.163" xlink:href="#B"><stop offset="0" stop-color="#b9b8ba"></stop><stop offset="1" stop-color="#e0dfde"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M670 800v2-3h1l1 1c-1 4-1 10 1 14 0 2 0 5 1 5l2 1v1c1 1 1 0 2 1v2h1c1 2 0 6 0 8v1c-1 2 0 2-2 4h-1c1-1 1-2 2-3 0-3 0-4-2-7v-2-1l-1-1c-3-3-3-6-4-9-1-5-1-9-1-14z"></path><path d="M609 132c8 0 16 2 23 4l15 3h-3-1c-3-1-7-1-10-2l-2-1c-1 1-1 1-2 1l2 1c1 2 2 2 3 3h0-5c-1-1-2-2-4-2l-1-1-2-2c-1-1-6-2-8-2l-1-1c-1 0-3-1-4-1z" class="AA"></path><path d="M622 136c2 0 5 1 7 1l2 1c1 2 2 2 3 3h0-5c-1-1-2-2-4-2l-1-1-2-2z" class="c"></path><path d="M366 138h33c-1 1-2 2-4 2l-2 1-8 2c-1 0-3 1-4 0h0c1-1 0-1 1-1h1 0c-5 0-11-2-16-2h0 5v-1h-6v-1z" class="AW"></path><path d="M185 300l4-1c2 0 3 1 5 2l-3 1-1 1-7 2h-4c-2-1-5-1-7-2 0 0-1-2-1-3 3 0 7 1 10 0h4z" class="R"></path><path d="M181 300h4c2 1 4 1 6 2l-1 1c-2-1-5 0-6-1-1 0-2-1-3-2z" class="a"></path><path d="M185 300l4-1c2 0 3 1 5 2l-3 1c-2-1-4-1-6-2z" class="AO"></path><path d="M233 357c1-1 3-2 4-3 3-3 4-5 4-9 1-6-1-13-4-19l-1-1c-1-2-3-5-4-7v-1c7 8 12 17 11 28-1 5-2 10-6 13-3 3-7 4-10 6l-1-2c2 0 2-1 3-2h2l2-3z" class="z"></path><defs><linearGradient id="C" x1="480.682" y1="416.302" x2="482.772" y2="421.091" xlink:href="#B"><stop offset="0" stop-color="#7f7c82"></stop><stop offset="1" stop-color="#939397"></stop></linearGradient></defs><path fill="url(#C)" d="M472 420c7-3 14-6 21-2 2 0 3 1 4 2-1 1-3 1-4 1-3 0-6 1-9 2l2-2c1-1 3-1 4-2-2-1-6 1-7 2-2-1-3-1-4 0l-9 2c1-1 2-1 3-2l-1-1z"></path><path d="M637 283c1-1 2-1 3-2l1-1h-1l6-6c3-4 7-11 8-16 0-1 1-3 2-4v-1-2c1-1 1-1 1-2v-3l1-1v-2h1v1c-1 2 0 3 0 5-1 2-1-1-1 1 0 3-1 4-2 7 0 1-1 2-1 3h0l-1 3h1v-1c1-2 1-2 2-3 0 1 0 1-1 3v1l-1 1v1c0 1-1 2-1 3h-1c-3 7-8 13-16 15z" class="K"></path><path d="M198 299c2-1 3-4 5-4-1 3-3 6-5 8-3 0-5 2-8 4l-3 2h-2c2-1 3-1 4-2-2-1-3 0-5 1h-12 0c-2-1-3-1-5-2 4 0 8 0 12-1h4l7-2 1-1 3-1 4-2z" class="B"></path><path d="M179 305h4 0c-2 1-4 1-6 1 2 1 4 1 6 1-3 1-8 1-11 1-2-1-3-1-5-2 4 0 8 0 12-1z" class="W"></path><defs><linearGradient id="D" x1="887.92" y1="270.308" x2="894.229" y2="280.34" xlink:href="#B"><stop offset="0" stop-color="#712f36"></stop><stop offset="1" stop-color="#974147"></stop></linearGradient></defs><path fill="url(#D)" d="M889 266l3 3c1 0 0 0 1-1s3-1 4-2c-1 2-2 3-2 5-1 3-2 5-1 9l-1 2-3 3s0 1-1 1h-1c-1-2-2-3-2-5 2-5 3-10 3-15z"></path><path d="M353 541h1 1 1c-1-1-2-1-3-2h0c-2-1-5-2-7-3-9-3-17-5-27-5h-9c-2 0-4 1-5 0h4c2 0 3 0 5-1h5 0l-1-1c-1 0-1 0-2-1s-1 0-2-1h0c-1-2-2-4-2-6v-1l-1-1c0-2-1-6 0-7 0 2 0 5 1 7 1 3 1 6 4 8s5 3 9 2h1c1 1 3 1 4 1 1 1 3 0 5 0v1h3l5 2c1 0 2 1 3 1v1h2v1h1 0l1 1h1s1 1 2 1v-2c-1-2-3-4-3-6l-1-1v-1c3 3 3 8 7 11l2 4c-2 0-4-1-5-2z" class="m"></path><path d="M454 129c3 0 5 0 8 1l5 1c0 1-1 1-2 2h2 0c-2 1-4 1-6 1h-1c-1 0-3 0-4 1-10 0-19 0-28 2l-1-1h3c1-1 3-1 4-1s3-1 4-1h2l5-1 5-1 4-3z" class="j"></path><path d="M464 132l1 1h2 0c-2 1-4 1-6 1h-1-3c2-1 4-2 7-2z" class="o"></path><path d="M462 130l5 1c0 1-1 1-2 2l-1-1h-11c3-1 6 0 9-2z" class="AP"></path><path d="M454 129c3 0 5 0 8 1-3 2-6 1-9 2h-3l4-3z" class="U"></path><path d="M262 298c2-1 5-3 7-3 1 2 1 3 2 5 0 1 0 2 1 3 0 0 0 1 1 2l1 2c1 2 1 3 3 4l2 4-8 2c1-1 1-1 2-1v-1c-1 0-2 0-3-1h1v-1l-1-1-1-1h2l-2-3-1 1v-1h0v-2h-1 0v-1l-1-1 1-1c-1-1-1-2-2-3v-1-1h-1-2z" class="D"></path><defs><linearGradient id="E" x1="439.451" y1="878.541" x2="434.347" y2="886.786" xlink:href="#B"><stop offset="0" stop-color="#d0cdcc"></stop><stop offset="1" stop-color="#f3f2f0"></stop></linearGradient></defs><path fill="url(#E)" d="M442 875c1-3 4-6 6-9h1c0 1 0 1 1 1v1c-2 4-5 7-7 10-1 1-1 1-1 2 1 1 2 1 2 1-1 2-3 5-5 7-1 1-2 1-3 1s-2-2-3-3 0-4 0-5h1 1l1-1h2l1-1c0-1 0-1 1-2l2-2z"></path><path d="M244 802c-1-1-2-2-2-4h0c-1-1-1-2-1-3v-1c-1-1-1-1-1-2v-1c-1-2-1-8-1-10v3c0 1-1 0 0 1 0 2 0 4-1 6v-9h1c1-12 6-22 10-33l1 2 1-2h0c-3 8-5 15-7 23 0 1-1 2-1 3v3c-1 1-1 2-1 3v3c-1 2-1 8 0 10v1c1 2 1 3 1 4 1 1 1 1 1 3z" class="m"></path><path d="M788 546c1 0 1 1 2 1l1 3c2 2 3 5 4 8 3 3 6 8 8 12 0 2 1 3 1 4v1l-1 1-2-2h0-1l-10-14h2c-1-1-1-2-1-2-2-3-2-4-3-7v-3-2h0z" class="K"></path><path d="M833 290c4 5 8 8 15 9v1h10c4 0 7-1 10-2l1 2c-1 1-2 1-3 2v1h-6c-1 1-1 2-2 2h-5v-1l-1 1c-5-1-12-3-15-7l-2-2-2-3v-3z" class="AJ"></path><path d="M837 298c1 0 2 1 3 1 1 1 2 1 3 1l2 2c2-1 5 0 7 1 0 1 0 1 1 1l-1 1c-5-1-12-3-15-7z" class="AT"></path><path d="M858 300c4 0 7-1 10-2l1 2c-1 1-2 1-3 2v1h-6c-1 1-1 2-2 2h-5v-1c0-1-2-2-4-3h-1c1-1 2 0 3 0 2 0 4 0 6-1h1z" class="AO"></path><path d="M281 609c1-4 3-6 6-9 5-6 11-10 17-14l1 2c-1 1-1 1-1 2s-1 3-1 4c-1 1-2 3-1 5h0c0 2 1 2 0 3h-1v-3h0v-3c-1 0-2 0-2 1h-1l-2 2h-2c0 1-1 2-2 2-1 1-1 2-3 2v1l-6 3c-1 0-1 0-1 1l-1 1v2-2z" class="I"></path><path d="M771 566c5 2 11 5 16 8 2 2 4 4 7 5l-1-3c1 1 3 2 5 2 3 3 5 6 7 9l1 1 4 9 2 4v2c0-1 0-1-1-1l-1-1c0-1-1-2-1-2l-1 1c-2-2-2-5-4-8l-2-2-2-3-3-3c-2-1-3-3-5-4h0c-1-2-5-4-7-6l-1-1c-1 0-2 0-2-1l-7-3-1-1h-1l-2-1v-1z" class="m"></path><path d="M797 582c2 1 6 3 7 6l2 5-9-11z" class="G"></path><path d="M804 588l1-1 1 1 4 9 2 4v2c0-1 0-1-1-1-1-3-3-6-5-9l-2-5z" class="F"></path><path d="M793 576c1 1 3 2 5 2 3 3 5 6 7 9l-1 1c-1-3-5-5-7-6l-3-3-1-3z" class="AI"></path><path d="M270 443h2l-2 3c0 3 0 4 3 6 0 1 0 1 1 1l7 3 3 1 2 1c1-1-1-3 1-4 3 1 5 3 7 6v8 3 1c-1 1-1 3-1 4v1 1c-1 1-1 3-1 5h0c0 1 0 1-1 2v2 1s-1 1-1 2v3l-1 1c0 1-1 3-1 4v1h0c0 1-1 2-1 3v1 1c-1 0-2 1-2 3v1l-1 1c0 1-2 4-2 6 0 0 0 1-1 1 0 1 0 3-1 4l-1 1-1 3v1h-1c8-18 13-34 16-53 1-3 0-6 0-9-1-3-3-5-6-7v3c1 8 0 17-2 25v-2c1-3 0 2 0-1 1-1 0-1 1-2v-4l1-1c-1-3 0-8-1-10l-1-1v-1c0-1-1-2-2-3-1 0 0 1-1 0-4-3-10-4-13-7-1-1-2-3-2-4 0-2 2-4 3-5z" class="K"></path><path d="M499 150c4 2 8 2 13 3-2 1-4 2-5 3 1 12 2 23-6 34h-1c1-1 0-1 1-2 1-3 2-6 2-9 1-1 1-3 1-4h0c0-6 0-13-3-19 0-1-2-3-2-4v-2z" class="AK"></path><path d="M277 621c1 1 1 2 1 4 4 1 8-2 11-3 4-2 7-3 11-3h6l1 3c-1 1-3 1-4 2h-2-4c-1 1-2 1-3 2h-1-1s-1 1-2 1h-1 0c-1 1-2 1-3 1l-4 1h-1c-1 0-4 1-5 0l-1-3c0-2 1-3 2-5zm501 118h1l-1-1v-1c1 0 2 1 2 2v1l2 2 2 5 2 4c3 4 5 9 6 14 2 4 4 8 4 13 1 4 2 10-1 14h0v-7c0-6-2-12-4-17l-1 1v-1h-1s-1-2-1-3l-3-10c-2-5-5-10-7-16z" class="D"></path><path d="M276 601v1c1 5 0 11 0 17h-1v-2c-1-2 0-3 0-4-1-2-1-4-1-6l-2-2v-1c-1 0-2-1-3-1v-1c-1 0-2 0-3-1h0c-1 0-2-1-2-1h-1v-1c-2-1-2-2-2-4s1-5 3-6c1 0 2-1 3 0 4 2 7 5 8 9l1 3z" class="K"></path><path d="M267 589c4 2 7 5 8 9l1 3c-2 0-6-2-8-4s-2-5-2-7l1-1z" class="AI"></path><path d="M292 613c1 0 2-1 3-1 2 1 8 1 9 1 1 1 2 5 2 6h-6c-4 0-7 1-11 3-3 1-7 4-11 3 0-2 0-3-1-4l2-2c4-3 8-4 13-6z" class="L"></path><path d="M150 132l-1 7v2c0 1 2 3 3 3-1 2-1 3-2 4l-1-1c-2 2-2 5-3 7l-5 13v-3-2h-1c0 1 0 2-1 3 0 2-1 4-2 5s-1 1-1 3h-3l8-24 3-11v1c0 2 0 3-1 4v2l2-4v1l1-2c2 1 1 1 2 1 1-3 0-6 2-9z" class="R"></path><path d="M144 138v1c0 2 0 3-1 4v2l2-4v1c0 3-1 8 0 11v1c-1 2-2 3-2 5l-2 5v-2c1-3 2-6 1-9 0-1 1-1 1-3 1-1 1-1 1-2l-1 2-1-1c1-1 1-2 1-3l-1 4-1-1 3-11z" class="a"></path><path d="M150 132l-1 7v2c0 1 2 3 3 3-1 2-1 3-2 4l-1-1c-2 2-2 5-3 7l-5 13v-3l2-5c0-2 1-3 2-5v-1c-1-3 0-8 0-11l1-2c2 1 1 1 2 1 1-3 0-6 2-9z" class="E"></path><path d="M145 142l1-2c2 1 1 1 2 1-1 5-2 9-3 13v-1c-1-3 0-8 0-11z" class="j"></path><path d="M143 98l-3-16c3 5 6 11 9 16 6 8 13 14 20 21 10 10 22 19 36 22h2l3 2h-2c-10-2-19-6-28-10v-1-1c-1-1-1-1-2-1-1-1-2-3-3-3-3-2-4-4-6-6s-4-2-5-4l-1-1c-5-5-10-9-14-15l-1-2h0l-1 1c-1 0-1 0-1-1-1-1-1-2-2-3 0 1-1 2-1 2z" class="e"></path><path d="M499 483v-6c1-7-3-26-7-31l-1-1c-1 0-1-1-1-2l-3-3h1l3 3 1 1c1 2 2 5 3 7 0 1 1 1 1 2v1l1 3c0 1 1 3 1 4s0 2 1 3c0 1-1 1 0 2v3l1 1c0 2-1 3 1 5 1 0 1 1 1 2 0-2 1-6 0-8v-6c0-1 0-3-1-4v-4c-1-2 0-3-1-4v-4c-1-1 0-2-1-3v-2-2c-1 0 0-1 0-2v1c0 1 0 1 1 2v2c0 1 0 1 1 2 0 1-1 2 0 3h0v6l1 9 2 18c0 2 1 4 1 6s-1 4 0 6c1 1 1 2 1 3v2l1 1v2c0 1 1 1 1 2v2l1 1-1 2 1 1v2l-1 1v11l-1-1v-3-3c-1 0-1-1-1-2l-3-14-1-4v-3c-1-4 0-7-2-10h-1z" class="N"></path><path d="M500 483v-4c2 2 2 10 3 13 0 2 1 4 2 7 1 1 1 3 1 5 0 1 0 0 1 1v3 1c0 1 0 0 1 2v1 11l-1-1v-3-3c-1 0-1-1-1-2l-3-14-1-4v-3c-1-4 0-7-2-10z" class="C"></path><path d="M467 131l3 1 3 1 4 1c12 5 23 15 36 18 0 1 0 1-1 1-5-1-9-1-13-3h0c-2-1-6-4-8-5s-3-1-5 0c-10-6-19-9-30-10 1-1 3-1 4-1h1c2 0 4 0 6-1h0-2c1-1 2-1 2-2z" class="R"></path><path d="M473 133l4 1h-1c-2 1-3 1-5 0l2-1z" class="o"></path><path d="M467 131l3 1 3 1-2 1-4-1h0-2c1-1 2-1 2-2z" class="j"></path><path d="M470 132l3 1-2 1-4-1h0 1c1 0 2 0 2-1z" class="p"></path><path d="M143 98s1-1 1-2c1 1 1 2 2 3 0 1 0 1 1 1l1-1h0l1 2v1c1 7 1 13 1 20v10c-2 3-1 6-2 9-1 0 0 0-2-1l-1 2v-1l-2 4v-2c1-1 1-2 1-4v-1c-1-2 0-6 1-9v-15l-2-16z" class="o"></path><path d="M143 98s1-1 1-2c1 1 1 2 2 3 0 1 0 1 1 1l1-1h0l1 2v1l-1 2h0v10h0-1-2l-2-16z" class="j"></path><path d="M272 443l2 1c5 1 8 2 12 4 9 6 15 12 20 22h-1l-1-2c0-1-1-2-1-3l-1-1c0-1 0-1-1-1-2 0-2 0-3-1h0l-2-1h-1v2c-1 1 0 3-1 5v-8c-2-3-4-5-7-6-2 1 0 3-1 4l-2-1-3-1-7-3c-1 0-1 0-1-1-3-2-3-3-3-6l2-3z" class="N"></path><path d="M274 444c5 1 8 2 12 4-1 2-1 5-1 7 0 0-1-1-2-1-3-1-8-2-9-6-1-2-1-2 0-4z" class="AI"></path><path d="M329 487c2 2 4 3 6 5 2 3 4 6 6 10l4 11 6 14c1 1 2 4 3 5l2 7c-4-3-4-8-7-11l-1-3-1-2-2-2v-2-1h-1l-1 1-1-1v-1l-1 1-6-9c-1-3-2-7-3-10 0-3-1-5-2-7 0-2 0-3-1-5z" class="D"></path><path d="M460 860c6-4 12-8 19-9l11 27c-2-2-2-3-3-5l-1-2c0-1-1-2-1-3-1-2-2-4-2-7-1-1-1-2-3-3h0l-2-1c-2-1-3-1-5-1v1h-1c-1 0-1 1-2 1l-1 1s-1 0-1 1c-3 3-7 6-10 9-5 4-9 9-14 12 0 0-1 0-2-1 0-1 0-1 1-2 2-3 5-6 7-10 3-3 7-5 10-8z" class="N"></path><path d="M742 138h37c-2 2-2 2-2 4-3 1-5 1-8 2l-4 2h0-1l-8 1-5-1c-2 0-4-1-6-1 0-1-2-1-3-2-1-2-5-3-7-3 2-1 4-1 7-2z" class="AW"></path><path fill="#f0c8ca" d="M753 144c1 0 3 0 4 2h-2l-2-2z"></path><path d="M735 140c2-1 4-1 7-2 1 2 3 3 4 4 3 1 5 1 7 2l2 2h-4c-2 0-4-1-6-1 0-1-2-1-3-2-1-2-5-3-7-3z" class="Ab"></path><defs><linearGradient id="F" x1="794.356" y1="360.479" x2="808.56" y2="358.036" xlink:href="#B"><stop offset="0" stop-color="#acacae"></stop><stop offset="1" stop-color="#d3d2d0"></stop></linearGradient></defs><path fill="url(#F)" d="M811 313l1 2c0 1-1 2-1 2-1 1-1 0-1 1l-1 2h0c-1 1-3 3-5 4h0c0 2-1 2-2 3l-1 1v1c-1 1-1 2-2 3 0 0-1 1-1 2v1c0 1-1 2-1 2v1c-1 5 0 13 3 17 1 2 2 3 3 4h1c2 2 6 4 9 4h3l1 2c-3 1-11 0-14-1-4-1-7-4-9-7h-1c1-1 1-1 0-2v-6-1c0-5-1-9 1-13v-2l1-1c2-7 10-13 16-19z"></path><path d="M594 413v1c1 0 2 1 3 1l2 1c1 1 0 1 1 1 2 1 2 2 3 4h1v2l1 1c1 1 1 2 2 3 0 1-1 2 0 3 0 1 0 1 1 3v3l1 2c0 1 1 2 1 4h0c0 1 0 2 1 3v3c0 1 0 2 1 3v3c0 1 0 2 1 3 0 3 1 12-1 14 0 1 1 3 0 4 0 1 0 1-1 2v3c0 1 0 1-1 2v2c-1 1-1 2-1 3v1c-1 1-1 4-1 5l-1 2v1 2 1l-1 1c-1-2 1-7 1-10 1-11 2-23 2-35-1-9-3-19-7-28-2-3-3-5-5-7v-1c-1-2-3-3-4-5l1-1z" class="N"></path><path d="M255 396h-1c-2-1-3-2-5-2l-12-6c-6-3-13-7-17-12h1c0-2 1-3 1-5 1 0 1 0 2-1h2l6 2v1c1 1 2 1 3 2l-1 1c3 2 7 4 10 7 2 2 4 6 6 7 2 2 6 5 7 8l-2-2z" class="K"></path><path d="M255 396c-3-2-5-5-7-7-5-3-10-5-15-8-2-1-4-3-4-4-1-2-1-2-1-4 2 0 4 2 6 3 3 2 7 4 10 7 2 2 4 6 6 7 2 2 6 5 7 8l-2-2z" class="F"></path><defs><linearGradient id="G" x1="465.079" y1="380.558" x2="476.602" y2="380.903" xlink:href="#B"><stop offset="0" stop-color="#a9a7a9"></stop><stop offset="1" stop-color="#d5d5d5"></stop></linearGradient></defs><path fill="url(#G)" d="M471 353v1c2-2 2-2 5-2 2-2 4-3 7-4-2 1-3 3-4 4v-1l-4 4-1 2v1h-1 0c0 1 0 2-1 3 0 2-1 5-2 7v1h0v1 2l-1 1c0 1 0 3 1 4 0 1-1 3 0 5 0 1 0 1 1 3v2c0 1 0 1 1 2h0c0 2 1 2 2 3v-1c0 1 0 2 1 3v1l2 2c1 1 0 0 0 1 1 1 2 1 2 2h1l1 2h-1c-1-1-1-2-3-2v-1l-1 1h0s0 1 1 2l1 1c1 0 0 0 1 1 1 0 2 1 3 1 1 1 0 0 2 1l1 1h0c-3 0-5-1-7-3-3-3-8-7-10-11-1-2-1-4-1-6l-2-10c-1-7 0-16 4-22l2-2z"></path><defs><linearGradient id="H" x1="455.987" y1="850.291" x2="461.129" y2="863.621" xlink:href="#B"><stop offset="0" stop-color="#82797e"></stop><stop offset="1" stop-color="#9e9698"></stop></linearGradient></defs><path fill="url(#H)" d="M476 842l2 6 1 3c-7 1-13 5-19 9-3 3-7 5-10 8v-1c-1 0-1 0-1-1h-1c-2 3-5 6-6 9-2 1-2 1-4 1l3-4 12-16 2-2c6-5 13-9 21-12z"></path><path d="M460 860h-5 0c0-1 0-1 1-2 2-1 4-3 6-5 5-4 10-4 16-5l1 3c-7 1-13 5-19 9z" class="L"></path><defs><linearGradient id="I" x1="736.601" y1="671.186" x2="747.725" y2="669.421" xlink:href="#B"><stop offset="0" stop-color="#bab8ba"></stop><stop offset="1" stop-color="#dcdbdb"></stop></linearGradient></defs><path fill="url(#I)" d="M734 631c2 0 2 0 3 1s1 3 2 4c1 5-2 14 1 18h0v1l1 1v1c0 1-1 2 0 3 0 1 0 3 1 4 0 1-1 3 0 4 0 1 0 1 1 2 0 1-1 2 0 3 0 1 0 1 1 2v3l1 1v1l1 3 2 5 1 1v-1c0 1 0 1 1 2v-2h0s0 1 1 2v1c1 1 1 2 1 3l1 1c1 0 1 1 1 2h1l1 1v1l4 5-1 1h0c-2 0-2-2-3-3-1-2-1-2-3-3h0s0 1 1 2c-1-1-2-1-3-1-2-4-4-9-6-13-6-12-12-28-13-42v-9l1-2c1-1 1-2 2-3h-1z"></path><path d="M733 634c1-1 1-2 2-3 1 4 1 6 0 9-1 1-1 3-2 4v-4 1c0 1 0 3-1 4v-9l1-2z" class="O"></path><defs><linearGradient id="J" x1="258.294" y1="415.169" x2="287.196" y2="416.988" xlink:href="#B"><stop offset="0" stop-color="#c8c7c6"></stop><stop offset="1" stop-color="#f3f2f1"></stop></linearGradient></defs><path fill="url(#J)" d="M250 390c2 1 3 1 4 2s3 1 4 2 3 2 5 4c6 4 12 10 16 16l2 4v2c2 4 3 7 5 11 0 1 0 5 2 6-2 0-4 0-6 1h-2c0-3-2-7-4-9-3-8-7-15-12-22-2-2-6-6-7-9s-5-6-7-8z"></path><path d="M250 390c2 1 3 1 4 2 3 3 6 5 8 8l2 2h1c7 8 12 18 16 28 0 2 1 5 1 7v1h-2c0-3-2-7-4-9-3-8-7-15-12-22-2-2-6-6-7-9s-5-6-7-8z" class="N"></path><path d="M536 146l9-5c14-8 29-12 46-11 3 0 6 0 10 1v1h1 4l1 1-2 2h0v1c-1 0-2-1-3-1h-2-2c-1 0-2 0-2-1l-3 1c-6 0-12 0-17 1-13 1-24 6-34 13h0l-7 4-2-1h-2c0-1 1-2 2-2l-1-1c2 0 3-2 4-3z" class="e"></path><path d="M659 139h17l33-1c6 0 15-1 21 1h1c3-1 8-2 11-1-3 1-5 1-7 2-6 0-13 0-18 3-2 1-3 1-4 2-1 0-1 1-2 1l-1 5v2h-1c-1-2-2-3-4-3l-5-3h-3c-3-1-6-1-10-1 0-1-1-2-1-3h-4-7c-1-1-4-1-5-1l-16-2h3v-1h2z" class="z"></path><path d="M686 143h12c5 0 10 3 15 2-1 0-1 1-2 1l-1 5v2h-1c-1-2-2-3-4-3l-5-3h-3c-3-1-6-1-10-1 0-1-1-2-1-3z" class="AE"></path><path d="M704 148c1-1 3 0 4 0 1-1 1-2 3-2h0l-1 5v2h-1c-1-2-2-3-4-3l-5-3 4 1z" class="U"></path><path d="M700 147l4 1c2 0 3 0 4 1 1 2 1 3 1 4-1-2-2-3-4-3l-5-3z" class="M"></path><path d="M598 539c0-2-1-4-1-6 0-1 1-4 2-5 2-8 6-16 9-24l6-12c3-8 5-16 8-24 3-12 7-24 8-36h0c1 1 1 3 1 4-1 2-1 3-1 4v1c-1 0 0 1-1 2v1h1c1 0 1 1 1 1h1c0 4-2 9-3 14l-10 27c-1 4-2 7-3 10l-6 13-5 13c-1 4-3 8-4 12v4c-1 3-3 7-4 10h-1c0-3 1-6 2-9z" class="C"></path><path d="M814 302h1 0c0 4-2 8-4 11-6 6-14 12-16 19l-1 1v2c-2 4-1 8-1 13v1 6c1 1 1 1 0 2-1-2-1-3-2-4 0-2-1-3-1-5l-1-3 1-15h-1v-1c-2 2-2 3-3 5v-2l1-1v-1l1-1c0-2 4-4 6-5 1-1 1-2 2-3-1 0-2 1-3 1l-1 1c-1 1-2 2-4 2h-1c1-4 2-7 5-11h1c1-2 3-4 5-5l3-2 10-4 3-1z" class="O"></path><path d="M790 348c1-6 0-13 4-18-1 4-2 8-3 13 0 2 1 8 0 10 0-2-1-3-1-5z" class="F"></path><path d="M810 307c0 5-8 11-11 16-2 2-3 4-5 7h0c-4 5-3 12-4 18l-1-3 1-15h-1v-1c3-3 7-6 10-9 4-4 7-9 11-13z" class="E"></path><defs><linearGradient id="K" x1="797.327" y1="309.608" x2="801.953" y2="317.039" xlink:href="#B"><stop offset="0" stop-color="#a9a8aa"></stop><stop offset="1" stop-color="#d2d1d2"></stop></linearGradient></defs><path fill="url(#K)" d="M811 303l3-1c-1 2-3 3-4 5-4 4-7 9-11 13-3 3-7 6-10 9-2 2-2 3-3 5v-2l1-1v-1l1-1c0-2 4-4 6-5 1-1 1-2 2-3-1 0-2 1-3 1l-1 1c-1 1-2 2-4 2h-1c1-4 2-7 5-11h1c1-2 3-4 5-5l3-2 10-4z"></path><path d="M795 318l1-1 1 1c-1 0-2 1-3 1-1 1-2 1-3 2h-1c1-2 4-3 5-3z" class="X"></path><defs><linearGradient id="L" x1="442.06" y1="431.989" x2="465.869" y2="429.196" xlink:href="#B"><stop offset="0" stop-color="#afaeb0"></stop><stop offset="1" stop-color="#d1cfcf"></stop></linearGradient></defs><path fill="url(#L)" d="M457 406c4-1 7 4 10-1v-2l1 2h-1v1l-1 1h0v2 1l-1 1-1 1c0 2-2 3-3 4 0 1 0 1-1 1 0 3-2 4-3 6h1 1c0-2 1-2 2-2l1-1h0l-1 1c-2 2-3 4-3 7 0 1 0 1 1 2-3 4-5 7-6 11s-1 8-1 11l1 5h-2l-3-9c-1-1-1-3-3-5v1c-1-1-1-2-2-3l-1-1-1-2v-3c2 0 2 0 2-1 1-6 3-14 6-19 2-3 5-6 8-9z"></path><path d="M448 448l1 1c1 2 1 3 2 5v1-1c1-1 1-1 1-2l1 5h-2l-3-9z" class="G"></path><path d="M443 434c1 2 1 4 0 6h-1l-1-2v-3c2 0 2 0 2-1z" class="I"></path><path d="M676 827c2 3 2 4 2 7-1 1-1 2-2 3s-3 1-5 1l-1-1c-1 0-2 1-3 0-1 0-1 0-2-1h-3c-2 0-2 0-3-1-2 0-3 1-4 0-3 0-14-1-15 0-2 0-3 0-4 1h-2-2c-1 1-2 2-3 2s-1 0-2 1c0 1-1 1-2 2-1 0-2 1-2 1l-1 1-1 1-1 1c-1 0 0 0-1 1l-3 3-2 2h0c0 1 0 1-1 2 0 1-1 2-2 4v2l-2 4v2l-1 3c-1 2-2 5-2 7v1c0 1 0 2-1 3v2c0 1 0 1-1 3v2l-1 1-1 1c-1-1-1-2-2-3v-2l-1-1-1 1h-1c-1 0-1 0-2-1v-1c0 1 0 2 1 3l1 1v1 1l-2-2-2-3c0-1 0-2-1-3l-3-3c0-1-1-1-2-2s0 0 0-2c-1-1-3-3-4-5l-6-5 1-2c7 6 12 14 19 21h1 0l1-4c0 2 1 4 2 6 1-5 1-10 3-15 1-7 4-15 8-21 10-13 28-18 44-16 5 0 16 4 19 2 1-2 1-4 1-6z" class="K"></path><path d="M484 426l4-1c1 0 1 0 2-1 1 0 1 1 3 0v-1c2-1 2-1 4 0v-1c1 1 1 1 3 1 1 2 2 4 4 6s4 4 7 6h-1l-2-1h-1v1l1 1 1 2v1c-1-1-1 0-1-1-1-1 0-1-1-1s0 0-1 1c1 0 1 0 2 1 0 1 0 2 1 3v1h0c-2-1-2-3-4-5 0-1-1-2-1-3l-1-1-1-2c0-1-1 0-1-1l-1 1h-2c-1 1-3 1-4 1h-1c-1-1-2-1-4-1-1-1-1-1-2-1h-1-2c0 1-1 1-1 2s-2 2-3 2l-1 1-1 1s-1 0-1 1h-1c-1 1-2 2-2 3v3 1c0 1 1 3 0 4v4h0c1 1 1 2 2 2l1 1h0l2 3c2 3 8 6 9 9h2s1 0 1 2h0c2 1 3 2 4 3v1c1 1 1 2 2 3 0 0 1 1 1 2 1 1 0 1 1 3v1h0 1c0 3 0 6-1 9-3-8-8-14-14-21l-3-3-7-8h0c0 2 1 3 2 4v2l1 1h1c0 1 1 2 1 3-2-1-6-7-7-10h0c-1-1-1-2-1-2v-3c-1-4-2-10-1-14 1 0 1 0 1-1h0c1-3 3-4 5-6l-1-2c-2 0-4 1-5 2-2 1-3 1-5 2l-1 1c2-6 14-7 19-11z" class="X"></path><path d="M674 824l1 1h1v2c0 2 0 4-1 6-3 2-14-2-19-2-16-2-34 3-44 16-4 6-7 14-8 21-2 5-2 10-3 15-1-2-2-4-2-6v-3c-1-4-1-6-1-10l1-4 2-11 1 3v-1l1-2h0c1-1 0-1 1-1l-1 3h1c1-3 3-6 5-9l1-1c15-13 31-16 50-14 4 0 7 1 11 1 2-1 2-2 3-4z" class="F"></path><path d="M674 824l1 1c-1 2-1 3-2 4-5 2-13-1-18-1-18-2-39 5-49 22-3 5-4 11-6 17 0 2-1 5-1 7-1-4-1-6-1-10l1-4 2-11 1 3v-1l1-2h0c1-1 0-1 1-1l-1 3h1c1-3 3-6 5-9l1-1c15-13 31-16 50-14 4 0 7 1 11 1 2-1 2-2 3-4z" class="L"></path><path d="M603 851h1c-2 5-4 11-5 16h0l-1-3 1-4c0 1 0 1 1 1 0-3 2-6 3-10z" class="k"></path><path d="M601 849l1 3v-1l1-2h0c1-1 0-1 1-1l-1 3c-1 4-3 7-3 10-1 0-1 0-1-1l2-11z" class="i"></path><defs><linearGradient id="M" x1="546.941" y1="858.2" x2="561.11" y2="873.028" xlink:href="#B"><stop offset="0" stop-color="#979598"></stop><stop offset="1" stop-color="#d8d7d6"></stop></linearGradient></defs><path fill="url(#M)" d="M557 852l1-1h1 0c2-1 3-1 5-1l-1 1v1-1c2 0 4 0 5 1l1 1c3 2 6 4 9 7l-1 2-3-4-5 5-1 2c-1 1-2 3-3 5-1 0-1 1-2 1-1 2 0 0-1 1 0 2-3 4-5 5 0 1-1 1-2 2h0c-1 1-2 1-3 1v1c-1 0-2 1-4 1h0-2l-1 1h-2c-1 1-1 1-1 2h-1v1c0 1 0 2-1 3h-1l13-35h1c1-1 1-3 3-3v1h1z"></path><defs><linearGradient id="N" x1="631.727" y1="820.583" x2="637.763" y2="840.521" xlink:href="#B"><stop offset="0" stop-color="#77696d"></stop><stop offset="1" stop-color="#88868b"></stop></linearGradient></defs><path fill="url(#N)" d="M644 818c11-1 20 1 30 6-1 2-1 3-3 4-4 0-7-1-11-1-19-2-35 1-50 14l-1 1c-2 3-4 6-5 9h-1l1-3c-1 0 0 0-1 1h0l-1 2v1l-1-3c2-5 4-9 8-13 9-12 21-17 35-18z"></path><path d="M627 828c1-1 2-2 3-2h1 4c1-1 2-1 4-2 5-1 10-1 16-1h-1c-2 0-5 1-7 1h-1c-1 1-3 0-4 1h-1c-1 0-2 1-3 1h0-2l-2 1c-3 0-4 1-7 1z" class="v"></path><path d="M627 828c3 0 4-1 7-1l2-1h2 0c1 0 2-1 3-1h1c1-1 3 0 4-1 2 1 3 1 5 1 2 1 5 0 7 1h2v1c-19-2-35 1-50 14l-1 1v-2l2-1 1-1c1-1 1-1 2-1v-1c1-1 2-2 3-2l2-1v-1c2-2 6-2 8-4z" class="E"></path><path d="M286 743c7-3 14-5 22-6 9-1 19 1 27 5 12 5 18 15 22 27l2 8c0 1 0 1 1 1 1 1 2 1 2 2h1l1 4v9h0l-1-1v-3h-1c0-2 0-2-1-3v-3l-1-1-4-10c0-2-1-3-2-4s0-2-1-4-2-3-3-4c-1-2-5-6-6-7l-1-1c-1 0-3-2-4-3l-2-2h0c-1 0-2-1-2-1l-4-2c-1 0-1 0-2-1l-4-1c-3-1-8 0-12 0h0-1-1-1-1-1l-1 1c-15 0-28 6-38 16-4 3-8 7-10 11l-2 2-5 7c-1 3-3 5-4 8v1l-1 1s-1 2-2 2c0-6 3-12 4-18l3-6h4c2-2 4-5 6-7 6-7 13-12 21-16l3-1zm483-156h0c1 1 1 1 2 1l2 2c0 2 1 6 0 8-1 1-1 2-3 3v1h-1c-2 1-3 2-4 3l-1 2c-1 2-2 1-2 3-1 2 0 3-1 4 0 1 0 1-1 3 0 1 1 2 0 3v1c0 2-2 7-3 9-1 0-4 0-5-1h-2c-1 0-1 0-2-1h-1l-3-1h-1l-2-1h-2-3v-1l-1 1v-1h-1v1c-1 0 0 0-1-1-1 0-2-1-3-1-2-1-3-1-4-2l1-2 1-3c0-1 1-3 2-3 0-1 1 0 2 0 2 0 3 0 5-2 3 0 12 3 15 5l2 2 1 2h1v-11-7c1-4 2-7 4-10l3-3c1-1 3-1 3-2h2l1-1z" class="N"></path><path d="M766 588h2c2 3 2 5 2 8-1 3-4 3-6 5-5 4-7 14-7 20h-1v-11c2-2 2-5 4-8 2-2 7-5 8-7 0-3 0-5-2-7z" class="C"></path><path d="M766 588c2 2 2 4 2 7-1 2-6 5-8 7-2 3-2 6-4 8v-7c1-4 2-7 4-10l3-3c1-1 3-1 3-2z" class="E"></path><path d="M763 590c1 2 1 3 1 4-2 3-5 7-8 9 1-4 2-7 4-10l3-3z" class="v"></path><defs><linearGradient id="O" x1="743.072" y1="613.238" x2="742.512" y2="622.829" xlink:href="#B"><stop offset="0" stop-color="#7e797d"></stop><stop offset="1" stop-color="#9b9597"></stop></linearGradient></defs><path fill="url(#O)" d="M737 612c3 0 12 3 15 5l2 2 1 2 1 1c0 1 1 2 1 3s0 2-1 3h-1c-8-4-16-6-25-7-1 0-2-1-3-1l1-3c0-1 1-3 2-3 0-1 1 0 2 0 2 0 3 0 5-2z"></path><path d="M728 617h1c3 1 6 0 9 0s7 2 10 4c-6 0-12-1-18 0-1 0-2-1-3-1l1-3z" class="L"></path><path d="M126 276l3 4v-1c5 7 10 13 17 17l7 4 4 2h5c1-2 0-3-1-5l5 2 5 1c0 1 1 3 1 3 2 1 5 1 7 2-4 1-8 1-12 1 2 1 3 1 5 2h0 12c2-1 3-2 5-1-1 1-2 1-4 2h2v3c-8 2-17 4-25 1v1c-3-1-7-2-10-3-13-5-23-13-31-23l3-4-1-4c1-2 1-3 3-4z" class="u"></path><path d="M185 309h2v3c-8 2-17 4-25 1h4c1 1 7 1 9 0h1 1c2-1 3 0 5-1l-1-1h1c1-2 2-2 3-2zm-28-5c2 0 4 0 6 1 1 0 3 0 4 1 2 1 3 1 5 2h0 12c-1 1-5 1-7 1-3 0-4 0-6 2h0-1-3l-1-1c-2 0-2-1-4-1 0-1-1-1-2-2-1 0-1-1-2-1h1 3 0c-1-1-2-1-2-1-1 0-2 0-3-1h0z" class="S"></path><path d="M163 305c1 0 3 0 4 1 2 1 3 1 5 2h0c-3 0-6 0-9-1v-2z" class="q"></path><path d="M129 279c5 7 10 13 17 17l7 4 4 2h5c1-2 0-3-1-5l5 2 5 1c0 1 1 3 1 3 2 1 5 1 7 2-4 1-8 1-12 1-1-1-3-1-4-1-2-1-4-1-6-1-1-2-3-2-5-3s-4-2-6-4c-5-2-10-7-13-11-2-2-3-4-4-6v-1z" class="p"></path><path d="M166 299l5 1c0 1 1 3 1 3-2 1-4 0-6 0v-4z" class="j"></path><path d="M161 297l5 2v4 1c-3 0-6-1-9-2h5c1-2 0-3-1-5z" class="r"></path><defs><linearGradient id="P" x1="775.358" y1="299.305" x2="803.389" y2="317.194" xlink:href="#B"><stop offset="0" stop-color="#afadb0"></stop><stop offset="1" stop-color="#faf9f7"></stop></linearGradient></defs><path fill="url(#P)" d="M769 293l3 2 3 2h1c5 2 12 3 17 3 2 1 4 1 5 1 6 0 11-1 17-2-1 2-3 2-4 4l-10 4-3 2c-2 1-4 3-5 5h-1c-3 4-4 7-5 11h0v1h-1v-1h1v-3c0-1 0-1 1-2h-1-1-1 0l-1-1h-1c0-1-1-2-1-2-1 1-1 1-3 1 0 1-1 2-2 3 0 2-1 3-1 5h1v2l1 1 2 3v2l1 1c0 1 0-1 0 1l1 1v2h0v1l1 1v2 1 1l1 2-1 1c0-2-1-5-1-7-2-8-5-14-10-20l-4-3-4-3h-1c-1-1-2-2-2-3l8-19z"></path><path d="M797 306c1 1 3 1 4 1l-3 2-1-3z" class="K"></path><path d="M773 304v-3-1-1c0-1 1-2 2-2h1c-1 1-1 1-1 2 0 2 0 3-1 5h-1z" class="O"></path><path d="M764 315c0-2 1-3 2-4 1-2 2-3 2-4 1-2 2-4 2-6 1-2 1-4 2-6l3 2c-1 0-2 1-2 2v1 1 3c-1 3-3 8-6 11 0 1 0 1 1 2v1l-4-3z" class="E"></path><path d="M769 293l3 2c-1 2-1 4-2 6 0 2-1 4-2 6 0 1-1 2-2 4-1 1-2 2-2 4h-1c-1-1-2-2-2-3l8-19z" class="L"></path><defs><linearGradient id="Q" x1="783.694" y1="301.689" x2="792.537" y2="316.491" xlink:href="#B"><stop offset="0" stop-color="#9c9a9e"></stop><stop offset="1" stop-color="#d5d4d3"></stop></linearGradient></defs><path fill="url(#Q)" d="M784 319c0-1-1-2-1-2-1-3-2-5-2-7-1-2-1-4-1-6 0-1 1-1 1-1 5-3 11 2 15 3h1l1 3c-2 1-4 3-5 5h-1c-3 4-4 7-5 11h0v1h-1v-1h1v-3c0-1 0-1 1-2h-1-1-1 0l-1-1z"></path><path d="M629 137c1 0 1 0 2-1l2 1c3 1 7 1 10 2h1 3l5 1h2l16 2c1 0 4 0 5 1h7 4c0 1 1 2 1 3 4 0 7 0 10 1h3l5 3c0 2 1 4 3 6h0-7 0v1h0 0-1l-1 2c1 4 4 8 6 11l-1 1v-1c-1 0-1 0-1-1l-1-1c0-1 0-2-1-3h0-1c-1 0-1 1-2 1-4 1-7 0-10-2v1c-2 0-4-2-6-4-3-2-7-4-10-6l-3-1-1-1-4-2-7-3c-2 0-8-3-11-4-2-1-4 0-6-1s-4-2-6-2h0c-1-1-2-1-3-3l-2-1z" class="Z"></path><path d="M629 137c1 0 1 0 2-1l2 1c3 1 7 1 10 2h1 3l5 1h2l16 2c1 0 4 0 5 1h0l2 1-1 1v1h0-1-1l1 1h-1c2 1 4 1 6 2v1c-2 0-4 0-6-1h0-3c-3 0-6-2-9-2h-1c-2 0-4-1-6-2-3 0-5-1-7-1l2 1 7 2v1c-2 0-8-3-11-4-2-1-4 0-6-1s-4-2-6-2h0c-1-1-2-1-3-3l-2-1z" class="o"></path><path d="M647 139l5 1h2l16 2c-1 0-3 0-4 1-2 0-5 1-7 0 1-1 2-1 3-1h-3l-7-1-3-1c-2 0-3 0-5-1h3z" class="j"></path><path d="M631 138c5 1 10 3 16 4 5 2 10 3 15 5h-1c-2 0-4-1-6-2-3 0-5-1-7-1l2 1 7 2v1c-2 0-8-3-11-4-2-1-4 0-6-1s-4-2-6-2h0c-1-1-2-1-3-3z" class="AL"></path><path d="M675 143h7 4c0 1 1 2 1 3 4 0 7 0 10 1h3l5 3c0 2 1 4 3 6h0-7 0v1h0 0-1l-1 2-1-2c-1-1-5-2-7-2l-20-6h3 0c2 1 4 1 6 1v-1c-2-1-4-1-6-2h1l-1-1h1 1 0v-1l1-1-2-1h0z" class="p"></path><path d="M675 143h7 4c0 1 1 2 1 3h-1c-1 1 0 1 0 2l1 2-12-3-1-1h1 1 0v-1l1-1-2-1h0z" class="AP"></path><path d="M682 143h4c0 1 1 2 1 3h-1c-1 1 0 1 0 2-2 0-4-1-5-2 0-1 0-2 1-3z" class="U"></path><path d="M687 146c4 0 7 0 10 1h3l5 3c0 2 1 4 3 6h0-7 0v1h0 0-1v-1h0c-2-2-5-3-7-4-2 0-4-1-6-2l-1-2c0-1-1-1 0-2h1z" class="T"></path><path d="M806 373l1-1c2-1 4-2 6-2v3h0-1v1h1l-1 1c0 1 0 1 1 2-1 1-1 1-1 3-1 1-6 4-7 5l-1 1-1 1h0-1v1h-1-1l-1 1c-1 0-2 1-3 2l-1 1c-3 1-5 1-7 3s0 0-2 1c-1 1-1 2-3 3-1 0-2 2-2 2l-1 1c0 1-1 1-1 2l-2 2-1 1-1 2c0 1 0 1-1 2 0 2 0 0-1 2v1h0c0 2 0 3-1 4 0 1 0 2-1 3v2c0 1 0 1-1 2v2c0 1 0 1-1 3v1c0 1 0 1-1 2v3l-1 1c0 1-1 2 0 4h0 2v1 1h-1c0 1 0 1 1 3v1l-1 3v3 1c-1 1-1 3-1 4l-1 1c0 1 0 1 1 2 0 1 0 4-1 6 0 1 0 0 1 1l-1 1v2h1l-1 1v1l1 1h-1l1 1c0 2-1 4 0 5 0 1 0 4 1 5l-1 1 1 2v2h0v1 1c0 1 0 2 1 3h-1l1 1-1 1 1 1v2l1 1c0 2 0 1 1 3v2c1 1 1 2 1 3v3h0c0 2 0 2 1 3h0c0 1 1 1 1 1 0 1 1 2 1 3s2 2 1 3c-1 2 1 4 1 6 1 1 1 1 1 2l1 1c0 1 1 1 2 2v1l2 2-1 1v1c-2-1-3-2-4-3v-1-1c-2-1-3-3-4-5-2-2-3-5-4-8-3-8-6-16-7-25-1-4-1-8-2-12l-1-10c1-5 0-11 1-16l3-6c1-3 1-5-1-7l3-1-5-1c1 0 2 0 3-1h0l-2-1c0-9 2-16 6-24 2-4 5-8 7-12 4-4 7-9 11-12 5-4 11-7 16-12 2-2 4-5 4-7z" class="N"></path><path d="M764 441c2 0 3 0 4 1h0l-2 1-5-1c1 0 2 0 3-1h0z" class="AX"></path><defs><linearGradient id="R" x1="742.903" y1="501.734" x2="801.463" y2="515.734" xlink:href="#B"><stop offset="0" stop-color="#838287"></stop><stop offset="1" stop-color="#e7e6e4"></stop></linearGradient></defs><path fill="url(#R)" d="M763 444c2 2 2 4 1 7l-3 6c-1 5 0 11-1 16l1 10c1 4 1 8 2 12 1 9 4 17 7 25 1 3 2 6 4 8 1 2 2 4 4 5v1 1c1 1 2 2 4 3v-1l1-1c0 2 3 7 5 8v2h0v2 3c1 3 1 4 3 7 0 0 0 1 1 2h-2c-7-10-14-19-20-28-13-21-21-44-18-70v-6h0v-4h0c1-1 0-3 1-5v-1l5-1 1-1c2 1 3 0 4 0z"></path><defs><linearGradient id="S" x1="749.489" y1="451.583" x2="757.808" y2="458.864" xlink:href="#B"><stop offset="0" stop-color="#66636d"></stop><stop offset="1" stop-color="#8a888d"></stop></linearGradient></defs><path fill="url(#S)" d="M753 446l5-1c1 2 1 2 1 5 0 1-1 3-2 4l-3 2h0 1c-1 2 0 0-1 1-1 2-1 7-2 9v-1-3-6h0v-4h0c1-1 0-3 1-5v-1z"></path><path d="M763 444c2 2 2 4 1 7l-3 6c-1 5 0 11-1 16v1 1c-1 1-1 1-1 2v1 1c-1 1-1 2-1 3v2h-1v2h0v-11c0-2-1-3 0-5v-1c-2-2 0-6-1-9s3-6 4-9c1-2 1-3 1-5l-2-2c2 1 3 0 4 0z" class="L"></path><path d="M448 288c0-2 1-3 3-4h0l1 1h-1v1l-1 1v3c1 2 3 2 3 4l1 1-1 1s0 1-1 2c0 1-1 2-2 3v1s0 1-1 1c0 2 0 2-1 3l-1 1h0c-1 1-2 1-2 2l-2 2c0 1 0 1-1 2s-1 2-1 3v1c0 1-1 6 0 8 0 1 0 0 1 2h0c0 1 0 1 1 2v1l2 1h0c0 2 1 3 2 4h1c1 1 1 1 1 3l1-1c1 0 5 0 6 1 1 0 1 1 2 1 1 1 2 1 2 2h1c1 0 3 1 4 1h1c1 0 1 0 2 1s1 1 1 2v1 1c0 1-1 2-2 3 0 2 0 4-1 5v1 1c-1 0-1 1-1 2-1 2 0 4-1 5 0 1 0 3-1 4 0 1 1 1 0 2 0 1 0 2-1 2-1 1-1 2-3 2 1-1 3-2 3-3 1-2 1-6 1-9l-4 3h-1-1c-3 1-5 0-8 0-2-1-4-1-6-3h0l-3-3h-1c0-1 0-1-1-2 0-1-1-2-1-3v-1l-1-1c0-2 0-4-1-5-2-1-2-3-3-4s-8-2-9-3h-1 6l10-1c1 0 3 0 5-1h1 2c1 0 1 0 2-1 0-1 0 0-1-1-2-2-5-5-6-8l-1-1c-1-3-3-9-2-13 1 0 1 0 0-1v-1l3-3 2-2c2-3 5-7 5-11h0c0-4-1-5 0-8z" class="D"></path><path d="M469 345c-3 0-6-2-9-2l-2-1h2 0c3 0 5 1 8 1h0c1 1 1 1 1 2z" class="K"></path><path d="M449 365v-2c3 0 7 1 10 0l1-1h1c4-1 5-8 6-12 0 2 0 4-1 5v1 1c-1 0-1 1-1 2-1 2 0 4-1 5 0 1 0 3-1 4 0 1 1 1 0 2 0 1 0 2-1 2-1 1-1 2-3 2 1-1 3-2 3-3 1-2 1-6 1-9l-4 3h-1-1c-3 1-5 0-8 0z" class="m"></path><path d="M446 338l6 1h0c-4 1-7 3-9 6-1 2-1 3-1 4l1 1c1 5 3 9 5 13l-1-1c-3-2-6-3-7-7-2-3-1-7 0-11h0c1-3 2-4 4-6h2z" class="E"></path><path d="M755 583v-1c1 0 1 0 2-1h1 0l2-1h0c1-1 1-1 2-1h1 1c1 1 1 0 1 1l1 1v1h1v1c0 1 1 2 1 2 1 1 1 1 1 2l-1 1h-2c0 1-2 1-3 2l-3 3c-2 3-3 6-4 10v7 11h-1l-1-2-2-2c-3-2-12-5-15-5-1 0-3 1-4 1v-1h-1c-1 1-1 1-3 1l1-2c0-1 1-2 1-3 1-3 2-6 2-9v-1l-1-1s-1 0-1-1c-1-1-2-2-2-4v-1c-1-2-2-3-3-5v-1l-2-2 1-1-2-2c5 0 11 3 17 3h12 1c1-1 1 0 2 0z" class="h"></path><path d="M723 580c5 0 11 3 17 3h12 1c1-1 1 0 2 0-1 2-4 1-6 1-1 1-4 1-5 1h-5c-2-1-4-1-5-2-3 0-5-1-7-1h0v1l-2-1-2-2z" class="z"></path><path d="M726 585c6 3 12 7 16 12v1c-3-1-5-3-9-3l-1-1v3s-1 0-1-1c-1-1-2-2-2-4v-1c-1-2-2-3-3-5v-1z" class="O"></path><defs><linearGradient id="T" x1="761.698" y1="599.845" x2="749.867" y2="614.299" xlink:href="#B"><stop offset="0" stop-color="#d6d5d8"></stop><stop offset="1" stop-color="#fbfaf9"></stop></linearGradient></defs><path fill="url(#T)" d="M753 611c0-1 0-2 1-3-1-2-3-5-2-7 1-3 6-7 8-8-2 3-3 6-4 10v7 11h-1l-1-2-1-8z"></path><defs><linearGradient id="U" x1="746.331" y1="599.738" x2="737.835" y2="616.281" xlink:href="#B"><stop offset="0" stop-color="#b6b4b5"></stop><stop offset="1" stop-color="#f4f3f1"></stop></linearGradient></defs><path fill="url(#U)" d="M732 597v-3l1 1c4 0 6 2 9 3v-1c5 4 8 8 11 14l1 8-2-2c-3-2-12-5-15-5-1 0-3 1-4 1v-1h-1c-1 1-1 1-3 1l1-2c0-1 1-2 1-3 1-3 2-6 2-9v-1l-1-1z"></path><path d="M734 601h1c1 0 2 1 3 2-1 0 0 0-1-1-1 0-2-1-3-1z" class="N"></path><path d="M732 597v-3l1 1h1c1 1 2 2 3 2l3 3h-1v2l-2-2h-1c0-1-1-1-2-1v1l-1-1v-1l-1-1z" class="I"></path><path d="M301 554c10 1 21 1 31 3 6 1 11 3 17 5 3 1 7 2 10 3 2 1 6 4 9 3l3 8c-2-1-3-2-4-4-2 0-3-1-5-1l1 2c-8-3-17-6-25-8-10-2-20-3-30-3-9 0-19 0-28 1-8 1-16 3-23 7-5 3-11 7-16 11-2 1-5 2-7 4-4 4-7 10-10 14 0-2 1-5 3-7 0-1 1-1 1-2 0-2 2-3 3-5 6-11 17-21 26-29l1 1h-1c-5 4-9 9-13 14-2 1-3 3-5 5l-1 1c-1 1-2 2-3 4l-2 3c1-1 3-2 4-4s2-4 5-5l1-1h0l3-3v-1c1-2 2-3 3-4l4-3h1l1-1c1-1 1-1 2-1h2l7-3 7-2c9-2 18-2 28-2z" class="F"></path><path d="M243 574h0l3-3v-1c1-2 2-3 3-4l1 2c1 0 2-1 3-1-3 3-7 5-10 7z" class="AI"></path><path d="M266 558l7-2-3 2c2 1 4 0 6 0h0 1c5 0 27-1 30 0h0c5 0 9 1 14 1v1h-24c-5-1-13 0-18 1h-3c-4 1-7 0-10 2-1 0-3 0-4 1-2 0-4 1-6 2-1 0-2 1-3 1s-2 1-3 1l-1-2 4-3h1l1-1c1-1 1-1 2-1h2l7-3z" class="L"></path><path d="M266 558l7-2-3 2c2 1 4 0 6 0h0 1 0c-1 1-5 2-6 1l-5-1z" class="E"></path><defs><linearGradient id="V" x1="310.274" y1="541.646" x2="327.791" y2="587.022" xlink:href="#B"><stop offset="0" stop-color="#877778"></stop><stop offset="1" stop-color="#9a979c"></stop></linearGradient></defs><path fill="url(#V)" d="M301 554c10 1 21 1 31 3 6 1 11 3 17 5 3 1 7 2 10 3 2 1 6 4 9 3l3 8c-2-1-3-2-4-4-2 0-3-1-5-1-6-3-12-6-19-8s-15-2-22-3v-1c-5 0-9-1-14-1h0c-3-1-25 0-30 0h-1 0c-2 0-4 1-6 0l3-2c9-2 18-2 28-2z"></path><path d="M180 223h4l32 24c2 2 4 4 7 5l9 9c2 3 4 5 6 7 2 3 5 6 7 9 2 2 4 5 6 8 2 2 7 6 7 9v4 1c-5 1-11 2-16 2 1-1 0-1 2-1 1 0 1 0 3-1h0 2v-3-2c-1-1-2-1-2-2-1-2-3-3-4-4s-1-2-2-3-2-1-2-3c-1 0-2-1-2-2l-3-3c0-1-1-1-1-2-1-1-2-2-2-3-1-1-2-1-2-2l-7-7s-1-2-2-2c-2-1-2-2-3-3-2-1 0 1-2-1l-2-2-3-2-6-4-15-9-6-3-5-4c-1-1-2-1-2-2-2 0-3-1-4-3h-1-1l-2 1c-1 0 0 0-1 1l-2 1-1 1h-2c3-3 7-5 11-7 2-1 4-1 7-2z" class="C"></path><path d="M446 290l2-2c-1 3 0 4 0 8h0c0 4-3 8-5 11l-2 2-3 3v1c1 1 1 1 0 1-1 4 1 10 2 13l1 1c1 3 4 6 6 8 1 1 1 0 1 1-1 1-1 1-2 1h-2-1c-2 1-4 1-5 1l-10 1h-6-2l-7-2-4-2c0-2 1-5 1-7s1-5 2-7 3-5 3-7l1-1c1 0 2-1 2-2 2-1 3-3 6-3 1 0 1-1 1-1 2-3 4-3 7-4 1 0 1-1 1-1 2 0 4 1 5 2v-2c1-4 2-6 4-9h1l1-1c0-1 1-2 2-3z" class="AB"></path><path d="M416 314c1 0 2-1 2-2 2-1 3-3 6-3l-3 3c-3 4-5 9-6 14-2 4-3 7-1 11l-1 1-4-2c0-2 1-5 1-7s1-5 2-7 3-5 3-7l1-1z" class="AM"></path><path d="M446 290l2-2c-1 3 0 4 0 8h0c0 4-3 8-5 11l-2 2-3 3h0l-1-3c-2 0-5 2-7 4-5 3-9 10-9 15 0 2 0 5 1 7 4 2 7 4 11 4h-2c-1 0-2 0-3 1h-6-2c0-3-1-3-2-5-1-1-1-4-1-6-1-5 5-13 8-16 4-4 8-6 13-6h1c0-5 1-8 4-12 1-1 1-1 1-2s1-2 2-3z" class="E"></path><path d="M428 340c1-1 2-1 3-1h2c-4 0-7-2-11-4-1-2-1-5-1-7 0-5 4-12 9-15 2-2 5-4 7-4l1 3h0v1c1 1 1 1 0 1-1 4 1 10 2 13l1 1c1 3 4 6 6 8 1 1 1 0 1 1-1 1-1 1-2 1h-2-1c-2 1-4 1-5 1l-10 1z" class="O"></path><path d="M437 325h1c2 4 5 10 10 12h0c-1 1-1 1-2 1h-2-1c-2 1-4 1-5 1l-1-3c1-1 1-2 2-4l-2-7z" class="h"></path><path d="M437 336c1-1 1-2 2-4 1 3 2 4 4 6-2 1-4 1-5 1l-1-3z" class="k"></path><path d="M437 334l-2 2c-2 1-5 1-7 0l-1-1h-1c-1-1-1-1-2-1v-4l-1-1 1-2h0l1-4c2-6 6-9 12-12 0 3-1 8 1 11v3h-1l2 7c-1 2-1 3-2 4v-2z" class="C"></path><path d="M437 334c-1-3 0-6 0-9h0l2 7c-1 2-1 3-2 4v-2zm46 87c1-1 5-3 7-2-1 1-3 1-4 2l-2 2c3-1 6-2 9-2 1 0 3 0 4-1l3 3c-2 0-2 0-3-1v1c-2-1-2-1-4 0v1c-2 1-2 0-3 0-1 1-1 1-2 1l-4 1c-5 4-17 5-19 11-1 1-3 3-3 4 1 2 0 5 0 7 0 4 1 14 3 17 0 0 1 0 0 1h0l2 2c3 3 5 7 5 10l1 1 3 6c1 3 1 7 2 10h0v2l1 6v3c1 4-1 8-1 12v4c-2-3-2-5-3-8l-6-13-1-3c-1 0-4-7-4-9-2-3-4-7-5-10-2-4-4-8-5-13h0l-2-4c1 0 1 0 1-1 1-1 0-3 0-4l-1-5c0-3 0-7 1-11s3-7 6-11c1-2 4-4 6-6 2 0 4-1 5-1l9-2c1-1 2-1 4 0z" class="E"></path><path d="M468 498h1c1 0 2 0 2 1 1 1 1 1 1 2-2 0-2-1-3 0l-1-3z" class="G"></path><path d="M454 466c1 2 2 3 3 5l1 1 3 6c1 2 2 4 2 6s1 3 1 5c-2-3-4-7-5-10-2-4-4-8-5-13z" class="k"></path><path d="M470 423l9-2c1-1 2-1 4 0-10 3-21 4-26 14l-3 6h-1c1-4 3-7 6-11 1-2 4-4 6-6 2 0 4-1 5-1z" class="L"></path><path d="M460 468c2 3 5 6 7 9l1 1c0 1 1 2 1 2l1 3 2 4h0l2 2v1l4 7 1 6v3c1 4-1 8-1 12 0-2 0-3-1-4 0-4 1-8 0-11l-1-1h1l-4-8c-2-6-5-13-8-19-2-2-4-4-5-7z" class="O"></path><path d="M457 464c2 1 2 2 3 3l1 1c2 2 3 5 6 6l2 3 1 1c1 1 2 2 2 4v-4l1 1 3 6c1 3 1 7 2 10h0v2l-4-7v-1l-2-2h0l-2-4-1-3s-1-1-1-2l-1-1c-2-3-5-6-7-9-1-1-2-2-3-4z" class="I"></path><path d="M469 477l1 1c1 1 2 2 2 4v-4l1 1c1 3 1 6 1 9l-5-11z" class="N"></path><defs><linearGradient id="W" x1="454.209" y1="450.419" x2="480.332" y2="461.077" xlink:href="#B"><stop offset="0" stop-color="#b7b6b8"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#W)" d="M497 420l3 3c-2 0-2 0-3-1v1c-2-1-2-1-4 0v1c-2 1-2 0-3 0-1 1-1 1-2 1l-4 1c-5 4-17 5-19 11-1 1-3 3-3 4 1 2 0 5 0 7 0 4 1 14 3 17 0 0 1 0 0 1h0l2 2c3 3 5 7 5 10v4c0-2-1-3-2-4l-1-1-2-3c-3-1-4-4-6-6l-1-1c-1-1-1-2-3-3h0c-3-5-3-14-1-19l1-5c1-4 4-8 8-10 6-3 13-5 19-7 3-1 6-2 9-2 1 0 3 0 4-1z"></path><path d="M462 441c1 2 0 5 0 7 0 4 1 14 3 17 0 0 1 0 0 1h0c-2-1-4-4-5-7-2-6-1-13 2-18z" class="L"></path><path d="M497 420l3 3c-2 0-2 0-3-1v1c-2-1-2-1-4 0v1c-2 1-2 0-3 0-1 1-1 1-2 1l-4 1c-6 1-13 4-18 7 0 1 0 1-1 1v-2h1l-1-2c6-3 13-5 19-7 3-1 6-2 9-2 1 0 3 0 4-1z" class="I"></path><path d="M133 173h3c0-2 0-2 1-3s2-3 2-5c1-1 1-2 1-3h1v2 3l-2 8-1 4c-1 0-1 1-1 2-1 1-1 2-2 4v1h1l1-1c1 1 1 2 1 3l3-2h1l-1 2c1 1 1 1 2 1l-4 6-3 4-4 6c-1 2-3 3-4 5s-2 5-3 7l1 1v1l1-2v1l-2 5-1 3-1 4-1 5-1-2c-2 5-2 10-2 16 1 6 1 12 3 17 1 3 4 7 4 10-2 1-2 2-3 4l1 4-3 4c-8-11-12-24-13-37-1-4-1-8 0-11 1-17 9-31 16-47l9-20z" class="AA"></path><path d="M123 222c1 0 1 1 2 1l-1 3-2-1 1-3z" class="C"></path><path d="M122 225l2 1-1 4-2-1c0-1 1-3 1-4z" class="X"></path><path d="M121 229l2 1-1 5-1-2v-4z" class="N"></path><path d="M125 217l1 1v1l1-2v1l-2 5c-1 0-1-1-2-1l2-5z" class="G"></path><path d="M135 186h1l1-1c1 1 1 2 1 3l3-2h1l-1 2c1 1 1 1 2 1l-4 6-3 4-4 6c-1 2-3 3-4 5l3-6c-1-1-1-1-1-2s0-1 1-3h0c1-5 2-10 4-13z" class="AS"></path><path d="M108 251c1-5-1-10 1-14v-4c1-2 1-4 1-6 3 0 5 0 7 1 0 1 0 1 1 1h1c1 1 1 2 1 2 0 1-1 1-1 2v1 2 2c-1 1-1 1-1 2 1 3 1 6 1 8v1c1 6 1 12 3 17 1 3 4 7 4 10-2 1-2 2-3 4l1 4-3 4c-8-11-12-24-13-37z" class="o"></path><path d="M701 742l4-2c4-2 10-4 14-4h7 5c4 0 8 1 12 2 5 1 11 3 16 4 1 0 2-1 2-1 2 1 5 4 7 3 2 3 5 5 6 8l5 5v1l3 5 3 6 3 3 1 4v2c2 6 1 12 1 17-1-1-1-2-1-4s-1-4-2-6c-4-10-11-19-20-26-9-8-22-15-34-15h-1c-1 0-2 0-3-1v1l-1-1c-2 0-6 1-7 0h-1-1-1-1l-2 1h0c-1 1-2 1-3 2h-1v1l-1-1v1l-3 1c-1 1-2 1-3 2-4 3-8 6-11 10 0 1-1 2-2 3 0 1-1 2-2 2 0 1-1 2-2 2 0 1-1 1-1 2l-3 6c0 1-1 2-2 3 0 1-1 4-2 5l-1-1c-4 10-5 21-5 32-2-4-2-10-1-14l-1-1h-1v3-2c-1-3-1-6 0-8v-2c-1-3 1-8 2-12h1v-1l1 1 4-10c3-5 5-11 9-14l6-6c3-3 5-4 8-6z" class="X"></path><path d="M701 742l4-2c4-2 10-4 14-4h7 5c4 0 8 1 12 2 5 1 11 3 16 4 1 0 2-1 2-1 2 1 5 4 7 3 2 3 5 5 6 8l5 5v1l3 5 3 6 3 3 1 4v2l-1 1c-1-2-2-5-3-7l-2-3c-1-2-3-3-5-5-8-9-17-16-29-20-6-2-11-3-17-4-4-1-11-1-15 0h-4-1c-1 1-3 1-4 2-1 0-2 1-3 1s-3 1-4 2h-1-1 0c-1 1-1 0-2 2h0-1c-1 0-2 1-3 2v2l-1-1-1 2v1l-1 1c-1 2-2 3-3 4s0 0-1 2l-1 1-4 8c-2 2-3 5-4 8v2l-1 2c-1 2-2 6-2 9-1 3-1 7-2 10l-1-1h-1v3-2c-1-3-1-6 0-8v-2c-1-3 1-8 2-12h1v-1l1 1 4-10c3-5 5-11 9-14l6-6c3-3 5-4 8-6z" class="O"></path><path d="M785 769l3 3 1 4c-1 0-2-1-2-2l-1-1-1-3v-1z" class="F"></path><path d="M678 768c3-5 5-11 9-14-2 3-5 7-6 10-5 10-7 20-10 30-1 0-1-1-1-2v-2c-1-3 1-8 2-12h1v-1l1 1 4-10z" class="E"></path><path d="M701 742l4-2c4-2 10-4 14-4h7 5c4 0 8 1 12 2 5 1 11 3 16 4 1 0 2-1 2-1 2 1 5 4 7 3 2 3 5 5 6 8l5 5v1l3 5 3 6v1l-5-8-2-2c-1-1-2-1-3-2l-10-7c-10-6-21-11-32-13h-17l-5 1-10 3z" class="L"></path><path d="M711 739c16-6 34 1 49 7 3 2 6 3 9 5 2 1 4 4 6 5l1 1h0v-1l-1-1-1-1 1-1c2 3 4 6 5 9l-2-2c-1-1-2-1-3-2l-10-7c-10-6-21-11-32-13h-17l-5 1z" class="AI"></path><defs><linearGradient id="X" x1="472.77" y1="481.235" x2="493.07" y2="475.387" xlink:href="#B"><stop offset="0" stop-color="#b1b0b2"></stop><stop offset="1" stop-color="#dedddd"></stop></linearGradient></defs><path fill="url(#X)" d="M466 436c2-1 3-1 5-2 1-1 3-2 5-2l1 2c-2 2-4 3-5 6h0c0 1 0 1-1 1-1 4 0 10 1 14v3s0 1 1 2h0c1 3 5 9 7 10 0-1-1-2-1-3h-1l-1-1v-2c-1-1-2-2-2-4h0l7 8 3 3c6 7 11 13 14 21 1-3 1-6 1-9 2 3 1 6 2 10v3l-1 2v4c1 4 0 7 1 11 0 1 0 3-1 4h0-1l-1 8c-1-1 0-3-1-4-1 1-1 2-2 3s-3 1-4 0h-2-3l-3 2c0-1 0-3 1-4-1 0-2 1-3 1 0 2-1 3-2 4l-2-5v-4c0-4 2-8 1-12v-3l-1-6v-2h0c-1-3-1-7-2-10l-3-6-1-1c0-3-2-7-5-10l-2-2h0c1-1 0-1 0-1-2-3-3-13-3-17 0-2 1-5 0-7 0-1 2-3 3-4l1-1z"></path><path d="M484 477c2 0 3 2 5 3v1l1 1s1 1 1 2h1l-1 1h0-1l-5-6h0l-1-2z" class="D"></path><path d="M466 436c2-1 3-1 5-2 1-1 3-2 5-2l1 2c-2 2-4 3-5 6h0c-1 2-1 3-2 5v1h0v-4-1c1-2 3-4 3-7-2 1-4 1-6 3l-1-1z" class="I"></path><path d="M489 497c1 2 2 3 2 5 1 0 1 0 1 1v2c1 3 0 5 1 7 1-1 1-1 1-3 0 3 0 6-1 8h-1v-2l-1-5c-1-3-3-6-2-9v-2h0v-2z" class="F"></path><path d="M489 497c1 2 2 3 2 5v3c-1 0-1-2-2-4v-2h0v-2z" class="N"></path><path d="M484 489l-9-19 1 1 3 5h1v-1l8 18 1 4v2h0l-5-10z" class="K"></path><path d="M466 436l1 1c-1 0-1 1-1 2h-1v4c0 1 0 1-1 2-2 6 0 13 2 18l1 5-2-2h0c1-1 0-1 0-1-2-3-3-13-3-17 0-2 1-5 0-7 0-1 2-3 3-4l1-1z" class="F"></path><defs><linearGradient id="Y" x1="491.044" y1="511.709" x2="507.746" y2="497.544" xlink:href="#B"><stop offset="0" stop-color="#8c8c92"></stop><stop offset="1" stop-color="#bcbab9"></stop></linearGradient></defs><path fill="url(#Y)" d="M500 483c2 3 1 6 2 10v3l-1 2v4c1 4 0 7 1 11 0 1 0 3-1 4h0-1l-1 8c-1-1 0-3-1-4v-6c1-7 3-16 1-23 1-3 1-6 1-9z"></path><path d="M494 509c0-1 0-2 1-3 0-1 0-3-1-4s-1-1-1-2v-1-1-1l-1-1c0-1-1-2-1-3-1-1 0-1-1-1 0-1-1-2-1-2v-1l-1-2v-1c1 2 3 4 4 7 2 5 4 8 4 14 0 4 0 9-2 13h-1c1 2 2 3 3 4-1 1-3 1-4 0h-2-3l-3 2c0-1 0-3 1-4l1-3 1 1c0-1 1-2 2-3l1-1c0-2 0-4 1-6l1 5v2h1c1-2 1-5 1-8z" class="I"></path><path d="M491 510l1 5c0 2 0 3-2 4 0 1-1 1-2 1h-1c0-1 1-2 2-3l1-1c0-2 0-4 1-6z" class="D"></path><path d="M466 463c2 1 2 3 3 5 2 1 3 2 4 4v1c0 1 0 0 1 1 0 2 2 4 2 6 1 1 1 2 2 3h0c1 1 2 1 3 2 1 2 1 3 3 4l5 10v2c-1 3 1 6 2 9-1 2-1 4-1 6l-1 1c-1 1-2 2-2 3l-1-1-1 3c-1 0-2 1-3 1 0 2-1 3-2 4l-2-5v-4c0-4 2-8 1-12v-3l-1-6v-2h0c-1-3-1-7-2-10l-3-6-1-1c0-3-2-7-5-10l-1-5z" class="C"></path><path d="M481 504h1c1 1 3 4 3 6l2 2v2h1c0 1 1 2 1 3-1 1-2 2-2 3l-1-1h0c1-1 1-2 1-3l-2-2c-2-2-1-5-4-6v-4z" class="F"></path><path d="M479 503h1v-2h0v1c1 1 1 1 1 2v4c3 1 2 4 4 6l2 2c0 1 0 2-1 3h0l-1 3c-1 0-2 1-3 1 0 2-1 3-2 4l-2-5v-4c0-4 2-8 1-12v-3z" class="I"></path><path d="M481 508c3 1 2 4 4 6l2 2c0 1 0 2-1 3h0l-1 3c-1 0-2 1-3 1-1-4-1-11-1-15z" class="E"></path><path d="M466 463c2 1 2 3 3 5 2 1 3 2 4 4v1c0 1 0 0 1 1 0 2 2 4 2 6 1 1 1 2 2 3h0c2 4 5 9 6 14h0c1 1 1 2 2 4l-1 1h0c-3-2-5-4-7-7h0 0c-1-3-1-7-2-10l-3-6-1-1c0-3-2-7-5-10l-1-5z" class="G"></path><defs><linearGradient id="Z" x1="502.04" y1="558.202" x2="515.874" y2="556.645" xlink:href="#B"><stop offset="0" stop-color="#99989b"></stop><stop offset="1" stop-color="#d1cfcf"></stop></linearGradient></defs><path fill="url(#Z)" d="M509 511v2c1 1 1 1 1 2v2c1 1 1 1 1 3v2c1 1 1 1 1 2 1 1 0 2 0 3 1 1 1 2 1 3v3c1 0 1 0 1 2h0v1 1h0v1 2h1v-3c0-5-1-10 0-15 1-4 1-9 0-13v-1c1-2 1 0 1-2v-1c0-3 0-7 1-9v-1l-2 50-1 20c0 6-1 14 0 20l1 3c-1 3-1 15-2 17l-9-22-4-9-17-40-3-7c1-1 2-2 2-4 1 0 2-1 3-1-1 1-1 3-1 4l3-2h3 2c1 1 3 1 4 0s1-2 2-3c1 1 0 3 1 4l1-8h1 0c1-1 1-3 1-4-1-4 0-7-1-11v-4l1-2 1 4 3 14c0 1 0 2 1 2v3 3l1 1v-11l1-1z"></path><g class="G"><path d="M505 521c1 2 1 5 1 7h0c-1-2-1-4-1-7z"></path><path d="M503 515v-2c1 1 2 7 2 8l-1 2v-2c-1-2-1-3-1-4v-2z"></path></g><path d="M499 525l1-8h1c1 5 0 10 0 14 0 7 0 15 1 22 1 3 0 7 1 11 1 2 0 5 1 8s0 6 1 10v1h-1 0l-4-9-17-40-3-7c1-1 2-2 2-4 1 0 2-1 3-1-1 1-1 3-1 4l3-2h3 2c1 1 3 1 4 0s1-2 2-3c1 1 0 3 1 4z" class="L"></path><path d="M498 521c1 1 0 3 1 4v4h-1-2c0-1 0-1 1-2 0-2 0-2-1-3 1-1 1-2 2-3z" class="k"></path><path d="M496 524c1 1 1 1 1 3-1 1-1 1-1 2s0 1-1 1c-1 1-1 1-2 0l-1 6v-7-5c1 1 3 1 4 0z" class="v"></path><path d="M482 523c1 0 2-1 3-1-1 1-1 3-1 4l3-2h3l1 3c1 2 1 7 0 10v1-9l-1 1c-2 2-3 3-6 4h-1l-3-7c1-1 2-2 2-4z" class="O"></path><path d="M484 526l3-2h3l1 3-2 2c-1 2-2 3-4 3l-1-1c-1-2 0-3 0-5z" class="X"></path><defs><linearGradient id="a" x1="169.966" y1="112.622" x2="160.07" y2="150.02" xlink:href="#B"><stop offset="0" stop-color="#0c0200"></stop><stop offset="1" stop-color="#302123"></stop></linearGradient></defs><path fill="url(#a)" d="M149 101c4 6 9 10 14 15l1 1c1 2 3 2 5 4s3 4 6 6c1 0 2 2 3 3 1 0 1 0 2 1v1 1h-1c0 3 2 5 3 8v3c0 2 1 4 0 5 0 2-1 3-2 5 0 1-1 1 0 2v1l-3 3c-1 0-2 1-3 3-1 0-2 1-3 1-3 1-6 4-9 6l-2 1c-2 0-3 1-5 2-2 0-4 2-6 3-1 1-2 1-3 2-1 0-2 1-3 1v1l-2-1h-3l1-4 2-8 5-13c1-2 1-5 3-7l1 1c1-1 1-2 2-4-1 0-3-2-3-3v-2l1-7v-10c0-7 0-13-1-20v-1z"></path><path d="M152 145h1c1-3 4-6 4-9v-3c1-1 1-1 1-2v-1c1 2 0 4-1 7v1l2 2h0c-1 1-1 2-3 3 0 2-1 3-1 5h0l-3-3z" class="l"></path><path d="M157 138c2-1 3-1 4-2 1 0 2-1 3-1h0c2 1 2 2 4 3 1 0 2 1 2 2 1 2 2 3 3 4s2 2 2 3c0 2 0 2-2 3 0 1-1 1-1 1h-1-2-2c-1 0 1 0-1 0 0-2-1-3-1-4v-1c-2-1-2-2-3-3s-1-2-2-2c0-1 0-1-1-1l-2-2z" class="V"></path><path d="M162 143l5 2c1 1 1 1 0 2 2 1 2 2 4 2v-1l1 1-1 1h1v1h-1-2-2c-1 0 1 0-1 0 0-2-1-3-1-4v-1c-2-1-2-2-3-3z" class="P"></path><path d="M159 140c1 0 1 0 1 1 1 0 1 1 2 2s1 2 3 3v1c0 1 1 2 1 4h1 2 2 1s1 0 1-1c1 2 2 2 3 3h0l1 4c-3 2-8 5-11 5-1-1-1 0-1-1h-4c-1-1-1-1 0-2l-1-1c-2-1-5-3-6-5 0-1 0-2-1-3h0l-1 1v1c0-2-1-3-2-4 1-1 1-2 2-4v1l3 3h0c0-2 1-3 1-5 2-1 2-2 3-3h0z" class="f"></path><path d="M160 141c1 0 1 1 2 2s1 2 3 3l-2 1-1-1c-1-2-2-3-2-5z" class="x"></path><path d="M165 146v1c0 1 1 2 1 4h1 2 2l-3 3h-2c0-1 0-2-1-3s-1-2-2-4l2-1z" class="AZ"></path><path d="M152 144v1l3 3 5 10c-2-1-5-3-6-5 0-1 0-2-1-3h0l-1 1v1c0-2-1-3-2-4 1-1 1-2 2-4z" class="AF"></path><path d="M163 116l1 1c1 2 3 2 5 4s3 4 6 6c1 0 2 2 3 3 1 0 1 0 2 1v1 1h-1c0 3 2 5 3 8v3c0 2 1 4 0 5 0 2-1 3-2 5 0 1-1 1 0 2v1l-3 3c-1 0-2 1-3 3-1 0-2 1-3 1-3 1-6 4-9 6l-2 1c-2 0-3 1-5 2-2 0-4 2-6 3-1 1-2 1-3 2-1 0-2 1-3 1v1l-2-1h-3l1-4 2-8 5-13c1-2 1-5 3-7l1 1c1 1 2 2 2 4v-1l1-1h0c1 1 1 2 1 3 1 2 4 4 6 5l1 1c-1 1-1 1 0 2h4c0 1 0 0 1 1 3 0 8-3 11-5 1-2 3-4 4-7 1-7-1-13-5-18-2-4-5-7-8-10-2-1-4-3-5-5v-1z" class="AZ"></path><path d="M141 167l5-13c1-2 1-5 3-7l1 1c1 1 2 2 2 4v-1l1-1h0c1 1 1 2 1 3l-1 8c0 1 0 3-1 5l-6 12c-1 0-2 1-3 1v1l-2-1h-3l1-4 2-8z" class="Z"></path><path d="M152 151l1-1h0c1 1 1 2 1 3l-1 8c0 1 0 3-1 5h-2 0c-2 0-3 1-4 2 2-6 5-11 6-16v-1z" class="d"></path><path d="M152 151l1-1h0c1 1 1 2 1 3l-1 8c0 1 0 3-1 5h-2 0c1-2 1-4 2-6 1-3 0-6 0-9z" class="AB"></path><path d="M146 168c1-1 2-2 4-2h0 2l-6 12c-1 0-2 1-3 1v1l-2-1h-3l1-4c0 1 0 2 2 2 2-1 3-4 4-6l1-3z" class="v"></path><path d="M146 168c1-1 2-2 4-2h0c-2 5-5 10-9 13h-3l1-4c0 1 0 2 2 2 2-1 3-4 4-6l1-3z" class="l"></path><defs><linearGradient id="b" x1="276.296" y1="540.831" x2="326.181" y2="600.365" xlink:href="#B"><stop offset="0" stop-color="#c0bfc0"></stop><stop offset="1" stop-color="#eae7e6"></stop></linearGradient></defs><path fill="url(#b)" d="M257 556c4-3 10-7 14-9 21-11 46-14 69-10 4 1 8 3 13 4 1 1 3 2 5 2l1 2 9 23c-3 1-7-2-9-3-3-1-7-2-10-3-6-2-11-4-17-5-10-2-21-2-31-3-10 0-19 0-28 2l-7 2-7 3h-2c-1 0-1 0-2 1l-1 1h-1l-4 3c-1 1-2 2-3 4v1l-3 3h0l-1 1c-3 1-4 3-5 5s-3 3-4 4l2-3c1-2 2-3 3-4l1-1c2-2 3-4 5-5 4-5 8-10 13-14h1l-1-1z"></path><path d="M301 554h-1c-5-1-10 0-15 0 1-1 3-1 5-1h2c0 1 1 0 2 0h1 1c2 0 4 1 6 1 1 0 2 0 3-1 2 0 4 1 7 0s11-1 14 0l3 1c4 0 7 2 10 3 1 1 2 2 3 2h2s1 1 2 1l1 1h2v1c-6-2-11-4-17-5-10-2-21-2-31-3z" class="h"></path><defs><linearGradient id="c" x1="240.968" y1="242.456" x2="222.171" y2="261.341" xlink:href="#B"><stop offset="0" stop-color="#706e77"></stop><stop offset="1" stop-color="#bfbebd"></stop></linearGradient></defs><path fill="url(#c)" d="M180 223c8-3 18-3 27-2h0 4 1 0c2 0 3 0 4 1h1c3 0 4 1 7 3l-2 1c6 3 10 7 15 11h0l3 3c3 4 7 8 9 13 8 12 14 25 20 38l11 23-1 1-2-4c-2-1-2-2-3-4l-1-2c-1-1-1-2-1-2-1-1-1-2-1-3-1-2-1-3-2-5-2 0-5 2-7 3l-4 1v-1-4c0-3-5-7-7-9-2-3-4-6-6-8-2-3-5-6-7-9-2-2-4-4-6-7l-9-9c-3-1-5-3-7-5l-32-24h-4z"></path><path d="M207 221h4 1 0c2 0 3 0 4 1h1c3 0 4 1 7 3l-2 1c-5-2-10-4-15-5h0z" class="H"></path><defs><linearGradient id="d" x1="550.326" y1="526.368" x2="619.174" y2="585.632" xlink:href="#B"><stop offset="0" stop-color="#c7c6c9"></stop><stop offset="1" stop-color="#fffff9"></stop></linearGradient></defs><path fill="url(#d)" d="M585 528c2-3 4-6 5-10 6-16 10-31 10-48 0-3-1-15 0-16v3 1c1 1 1 1 1 2v2h0v1 3c0 1 0 1 1 2s0 3 0 4l1 1v6-3h1v1h1v-2-6-12-1c0 10 1 21 0 32-2 16-7 32-14 46-2 3-3 3-1 6 2 1 3 0 5 0l3-1c-1 3-2 6-2 9v1c-1 1-1 3-2 4l2 2-1 4c0 3-1 6-2 9-1 6-2 12-4 18h0c-2 7-5 13-7 19-1 1-2 4-2 4 1 1 2 3 2 4v-1h1v-1l2-2c-1 3-3 9-4 10l-8 19-3 7-1 3-6-13c-1-2-2-2-4-3-1 0-4-1-5-2-1 0-3-1-3-3 2-3 3-5 6-8 0 0 0-1 1-1h1l1-1c1-1 2-2 3-4l2-1c2-3 4-6 5-9 1-2 3-3 3-5 0-1 0 0 1-1l1-2c0-1 0-2 1-3l1-2v-3c1-1 1 0 1-2h0l1-1c0-2 1-4 1-6 0-5 1-10 0-14v-4c-1-4-1-10-4-13h-1c0-4 1-7 3-11l1 1c1-2 3-4 4-6 0-1 1-2 2-3z"></path><path d="M594 553l2 2-1 4c0-1 0-2-1-3v-3z" class="m"></path><path d="M578 536l1 1c1-2 3-4 4-6 0-1 1-2 2-3-3 5-6 9-9 14 1 3 3 6 4 8h-1l-3-6c0 1-1 2 0 3h-1c0-4 1-7 3-11z" class="z"></path><path d="M589 547c1 1 0 2 1 3h0v-3c1 4 2 8 1 12v6-2c-2-3-1-7-2-10-1-2 0-4 0-6z" class="D"></path><path d="M563 635c2 2 4 5 5 8 0 1 0 1 1 2v-2c-1-2-1-4-1-5-1-1 0-3 0-5h-1c0-4 0-8 1-11 1-7 9-14 13-20 2-3 3-6 4-9v1h1c0-2 1-4 1-5 1-1 1-2 2-3h0c-2 7-5 13-7 19-1 1-2 4-2 4 1 1 2 3 2 4v-1h1v-1l2-2c-1 3-3 9-4 10l-8 19-3 7-1 3-6-13z" class="z"></path><path d="M570 645v-6 1h1v-1l1-2c0-1 0-1 1-2v-2l-1 2h-1l1-1 4-10v1h1l1-2c0-1 0-1 1-2h0l2-2-8 19-3 7z" class="D"></path><defs><linearGradient id="e" x1="889.849" y1="307.429" x2="871.505" y2="238.259" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#28252b"></stop></linearGradient></defs><path fill="url(#e)" d="M909 223l1-1 2 7v-1c0-1 0-1 1-2 0 1 0 3 1 4 1-1 2-2 4-2h2c1-2 2-3 2-5 3 10 5 20 3 32-2 18-13 36-28 47-12 9-28 14-44 12-7-1-13-5-18-11-1-2-2-4-3-5l3-2 2 2c3 4 10 6 15 7l1-1v1h5c1 0 1-1 2-2h6v-1c1-1 2-1 3-2 2-1 3-1 5-1l1-1v-3l4-4c3-3 5-6 7-10 0 2 1 3 2 5h1c1 0 1-1 1-1l3-3 1-2c-1-4 0-6 1-9l2-1v1h0 1c1-1 1-2 2-3s4-1 5-3c-1-1-1-1 0-3v1c1-1 1-5 1-7h0l2 1v-2l1-6c0-6 0-13-2-19v-3l3 1c-1-1-1-3-2-4l1-1z"></path><path d="M911 248l2 1c-1 2-1 4-1 6l-1-2c-1-1 0-4 0-5z" class="Q"></path><path d="M858 305c1 0 1-1 2-2h6v-1 1l-1 1h3c-3 1-6 1-10 1z" class="a"></path><path d="M911 237h2v11 1l-2-1v-3-8z" class="x"></path><path d="M911 245c1 1 1 1 2 3v1l-2-1v-3z" class="T"></path><path d="M909 223l1-1 2 7c0 3 0 5 1 8h-2l-1-9c-1-1-1-3-2-4l1-1z" class="AU"></path><path d="M878 301c2-1 4-2 7-3-4 3-11 6-16 6h-1-3l1-1v-1c1-1 2-1 3-2 2-1 3-1 5-1l1-1 3 3z" class="AY"></path><path d="M875 298l3 3h-4v-2l1-1z" class="R"></path><path d="M875 295l4-4 1 3c1 0 2 1 3 1 1 1 3 1 4 0l1 1c-1 1-2 2-3 2-3 1-5 2-7 3l-3-3v-3z" class="p"></path><path d="M907 227l3 1 1 9v8 3c0 1-1 4 0 5l-1 2h-2l1-6c0-6 0-13-2-19v-3z" class="AQ"></path><path d="M911 253l1 2c-2 9-5 17-9 24l-1-1c1-3 3-5 3-9v1l-1-1v-1l1-2v-3c1-1 1-5 1-7h0l2 1v-2h2l1-2z" class="U"></path><path d="M908 255h2c-1 5-2 10-5 14l3-12v-2z" class="d"></path><path d="M906 256l2 1-3 12h0v1l-1-1v-1l1-2v-3c1-1 1-5 1-7h0z" class="P"></path><path d="M900 268c1-1 4-1 5-3-1-1-1-1 0-3v1 3l-1 2v1l1 1v-1c0 4-2 6-3 9l-2 3h-2c-3-1-2-3-4 0l-1 1 1-2c-1-4 0-6 1-9l2-1v1h0 1c1-1 1-2 2-3z" class="T"></path><path d="M895 271l2-1v1l-3 9c-1-4 0-6 1-9z" class="AU"></path><path d="M893 282l1-1c2-3 1-1 4 0h2l2-3 1 1c-4 6-9 13-15 17l-1-1c-1 1-3 1-4 0-1 0-2-1-3-1l-1-3c3-3 5-6 7-10 0 2 1 3 2 5h1c1 0 1-1 1-1l3-3z" class="J"></path><path d="M240 367c1 1 0 3 0 4s1 1 1 1c1 1 1 2 2 3v1c8 4 16 7 24 9v1c1 1 2 1 3 1s2 0 3-1l16 4c3 0 7 0 9 1 3 1 10 3 12 2l2 7 2 6 4 10 5 14 11 35c0 1 0 3-1 4l-2 4h0v-1l1-2h0v-1l1-1v-2l-1-4h0c-1-2-1-4-2-5v-1h0v-2h-1v-1-1l-1-1v-1c0-1 0-1-1-2v-1h0l-1-1v-2h0c0-1 0-1-1-2 0-1 0-2-1-4h0v-2h-1l-1-3v-1c-2 2-4 3-7 4-4 2-8 4-14 5-2-2-3-5-5-7l-1-1v-1c-1 0-1 0-2 1s-3 3-5 3v1c-2-1-2-5-2-6-2-4-3-7-5-11v-2l-2-4c-4-6-10-12-16-16-2-2-4-3-5-4s-3-1-4-2-2-1-4-2-4-5-6-7c-3-3-7-5-10-7l1-1c-1-1-2-1-3-2v-1c2 0 6-4 8-5z" class="m"></path><path d="M281 420c1-1 1-1 2-1v2-1c2 2 2 3 3 5l2 7v2l1 1-1 1-1-3c0-1-1-1-1-2-2-4-3-7-5-11z" class="z"></path><defs><linearGradient id="f" x1="277.525" y1="415.934" x2="295.612" y2="426.84" xlink:href="#B"><stop offset="0" stop-color="#b6b6b5"></stop><stop offset="1" stop-color="#e7e5e3"></stop></linearGradient></defs><path fill="url(#f)" d="M280 412l1-1c1 1 1 1 3 1 3 3 7 6 8 11 1 1 1 3 1 5v4h0l-1-1c0-2-2-4-3-6 0-2-1-2-2-3l-1-1-2-2-1 1v1-2c-1 0-1 0-2 1v-2l-2-4h0v-1l1-1z"></path><path d="M280 412l2 3c-1 1-1 2-1 3l-2-4h0v-1l1-1z" class="N"></path><path d="M281 418c0-1 0-2 1-3 2 3 4 4 5 7l-1-1-2-2-1 1v1-2c-1 0-1 0-2 1v-2z" class="D"></path><path d="M269 399l-2-3h0c10-1 19 1 28 3 5 1 10 2 14 6 4 3 5 8 9 11l5 14-3 3c-1-1-1-2-2-3l1-1v-1l1-1c-1-1-1-1-1-2h0c0-1-1-2-1-2v-1c0-1-1-1-1-3h0c0-2-1-2-2-3v2c1 1 1 2 1 3h-1c-1-3-4-6-4-10 0-1-1-1-1-2l-1-1v-1c-2-2-4-3-7-3-2-1-5-2-7-3-6-2-19-5-26-2z" class="C"></path><path d="M240 367c1 1 0 3 0 4s1 1 1 1c1 1 1 2 2 3v1c8 4 16 7 24 9v1c1 1 2 1 3 1s2 0 3-1l16 4c3 0 7 0 9 1 3 1 10 3 12 2l2 7 2 6 4 10c-4-3-5-8-9-11-4-4-9-5-14-6-9-2-18-4-28-3h0l2 3 15 13c-2 0-2 0-3-1l-1 1-1 1v1h0c-4-6-10-12-16-16-2-2-4-3-5-4s-3-1-4-2-2-1-4-2-4-5-6-7c-3-3-7-5-10-7l1-1c-1-1-2-1-3-2v-1c2 0 6-4 8-5z" class="AI"></path><path d="M274 390c1 0 6 0 6 1-2 1-6 0-8 0l2-1z" class="k"></path><path d="M298 391c3 1 10 3 12 2l2 7c-4-4-7-5-12-7h1l-1-1h-2v-1z" class="E"></path><path d="M240 367c1 1 0 3 0 4s1 1 1 1c1 1 1 2 2 3v1c8 4 16 7 24 9v1c1 1 2 1 3 1l1 1h0v1l3 1-2 1-2-1c-3-1-6 0-9-1l-2-1c-6-2-11-7-18-8 0 0-6-4-6-5-1-1-2-1-3-2v-1c2 0 6-4 8-5z" class="i"></path><path d="M261 386c-2 0-4 0-6-1v-1h2l4 2z" class="v"></path><path d="M257 384c2 0 3 0 5 1l5 1c1 1 2 1 3 1l1 1h0v1l-10-3-4-2z" class="E"></path><path d="M240 367c1 1 0 3 0 4s1 1 1 1c1 1 1 2 2 3v1c-4-2-7-3-11-3v-1c2 0 6-4 8-5z" class="D"></path><path d="M235 375c0 1 6 5 6 5 2 1 4 3 6 5l12 6c2 1 4 3 6 5 0-1-1-2-1-3h0 0c5 1 9 0 14 1 3 0 7 1 10 2 6 1 15 1 21 5 2 1 3 3 5 5l4 10c-4-3-5-8-9-11-4-4-9-5-14-6-9-2-18-4-28-3h0l2 3 15 13c-2 0-2 0-3-1l-1 1-1 1v1h0c-4-6-10-12-16-16-2-2-4-3-5-4s-3-1-4-2-2-1-4-2-4-5-6-7c-3-3-7-5-10-7l1-1z" class="I"></path><defs><linearGradient id="g" x1="748.022" y1="357.145" x2="780.215" y2="389.946" xlink:href="#B"><stop offset="0" stop-color="#a7a4a6"></stop><stop offset="1" stop-color="#dfdedd"></stop></linearGradient></defs><path fill="url(#g)" d="M761 312c0 1 1 2 2 3h1l4 3 4 3c5 6 8 12 10 20 0 2 1 5 1 7 1 2 1 3 2 5 0 0 0 1 1 2v1h0l1 1h0v2h0c2 5 4 8 8 11l7 1c2 1 3 0 5 0h0c1-1 2-1 3-1 1-1 3-2 5-2l2-2v1 1h-1c-1 0-3 1-3 2-2 0-4 1-6 2l-1 1-3 2v-1l1-1c-2-1-3 0-4 0-5 1-9 4-14 6-11 4-22 7-33 9-3 1-7 2-10 2l-4 1 2-4 3-3v-1c1-1 2-2 2-4h0v-2h0l1-1c1 0 1-1 1-2l-1-1 1-1 1 1c2 1 2 2 2 3h2l-1-2c0-3 0-5-1-7l-2-4-2-2-2-4h0l1-2h1c2-3 3-6 4-9 1-2 2-3 3-6l-1-3v-2h1l-2-1 4-10c1-3 2-7 4-10l1-2z"></path><path d="M780 369h1v-1l1 1-1 3c-1-2-1-2-1-3z" class="N"></path><path d="M782 369v-2l1 1c0 2-1 3-2 5l-1-1h1l1-3z" class="K"></path><path d="M741 387l1 1h1c-1 1-1 1-2 1h0c1 1 1 1 2 1l-4 1 2-4z" class="G"></path><path d="M779 354l2 2h0l1 2-1 4h0l-1-1c0-2-1-5-1-7z" class="F"></path><path d="M782 358c0 4 0 7-1 10v1h-1v-8l1 1h0l1-4z" class="C"></path><path d="M757 372l2 1c-1 2-3 6-6 6h-1v-1c3-2 4-3 5-6z" class="K"></path><path d="M749 373c2 1 2 2 2 3h2l-1 2v1c0 2 0 2-1 3-2-1-1-3-1-4-1-2-1-3-1-5z" class="F"></path><path d="M767 356v4 2 1c0 4-3 9-5 13h-1v-1h1c0-2-1-2 0-3 0-2 2-3 1-4l1-1c0-1 0-2 1-2 0-3 1-6 2-9z" class="G"></path><path d="M765 352l1-2v-2h1v-3-1l1 1v6c0 1-1 2-1 3-1 1-1 1 0 2-1 3-2 6-2 9 0-3 0-9-1-11l1-2z" class="O"></path><path d="M766 340c1-2 1-2 3-2 3 1 5 3 7 6l3 6 1 1c1 1 1 3 1 5h0l-2-2c-1-2-1-4-2-5-2-3-4-7-7-8l-1-1h0l-1-1-2 1z" class="E"></path><path d="M761 348l2-6c0-2 1-3 2-5h2 1 3c2 1 4 4 5 7-2-3-4-5-7-6-2 0-2 0-3 2s-1 3-1 5v1h0c-1 2-1 4 0 6l-1 2h0c0 1-1 2 0 3v4c0-2 0-4-1-5 0-3-1-5-2-8z" class="L"></path><defs><linearGradient id="h" x1="770.049" y1="345.014" x2="785.273" y2="346.041" xlink:href="#B"><stop offset="0" stop-color="#aaa8a8"></stop><stop offset="1" stop-color="#d9d7d8"></stop></linearGradient></defs><path fill="url(#h)" d="M772 321c5 6 8 12 10 20 0 2 1 5 1 7 1 2 1 3 2 5 0 0 0 1 1 2v1h0l1 1h0v2h0 0c-1 0-1-1-1-2-2 2-1 10-3 11l-1-1v2l-1-1c1-3 1-6 1-10l-1-2c0-2 0-4-1-5 0-2-1-5-2-7 0-1-1-3-1-4-1-2-1-3-2-5l-3-5-1-3c0-2 0-4 1-6z"></path><path d="M755 337l1-1c0-2 2-3 3-4 0-2 1-2 2-3l1-1c1 0 3 0 4 1 2 1 3 1 6 1h0l3 5c1 2 1 3 2 5 0 1 1 3 1 4 1 2 2 5 2 7l-1-1-3-6c-1-3-3-6-5-7h-3-1-2c-1 2-2 3-2 5l-2 6-1-3-1-3-2-2-1-2-1-1z" class="v"></path><path d="M757 340c2-3 2-6 4-9h3c1-1 1-1 2 0 2 0 4 1 6 3 1 0 1 1 2 1h1c1 2 1 3 2 5h-1c-1-1-2-3-2-4-3-2-7-4-11-4-2 3-3 7-4 10l-2-2z" class="AF"></path><path d="M760 345v-1c1-1 2-4 2-6l3-4c2 0 5 1 7 2 2 2 4 7 6 8 1 2 2 5 2 7l-1-1-3-6c-1-3-3-6-5-7h-3-1-2c-1 2-2 3-2 5l-2 6-1-3z" class="i"></path><path d="M755 337l1-1c0-2 2-3 3-4 0-2 1-2 2-3l1-1c1 0 3 0 4 1 2 1 3 1 6 1h0l3 5h-1c-1 0-1-1-2-1-2-2-4-3-6-3-1-1-1-1-2 0h-3c-2 3-2 6-4 9l-1-2-1-1z" class="AH"></path><path d="M761 312c0 1 1 2 2 3h1l4 3 4 3c-1 2-1 4-1 6l1 3h0c-3 0-4 0-6-1-1-1-3-1-4-1l-1 1c-1 1-2 1-2 3-1 1-3 2-3 4l-1 1-2-2h1l-2-1 4-10c1-3 2-7 4-10l1-2z" class="AM"></path><defs><linearGradient id="i" x1="763.169" y1="317.751" x2="768.364" y2="319.125" xlink:href="#B"><stop offset="0" stop-color="#6f6c74"></stop><stop offset="1" stop-color="#848185"></stop></linearGradient></defs><path fill="url(#i)" d="M763 315h1l4 3 4 3c-1 2-1 4-1 6l-1-1c-1-2-3-5-5-7l-3-1 1-2v-1z"></path><path d="M761 312c0 1 1 2 2 3v1l-1 2 3 1-2 2-1-1h-1c-1 2-3 3-5 4 1-3 2-7 4-10l1-2z" class="f"></path><path d="M761 312c0 1 1 2 2 3v1l-1 2c-1-1-1-3-2-4l1-2z" class="AH"></path><path d="M754 335c1-4 4-7 8-10h2l1 1h3c1 1 1 1 2 0l1 1 1 3h0c-3 0-4 0-6-1-1-1-3-1-4-1l-1 1c-1 1-2 1-2 3-1 1-3 2-3 4l-1 1-2-2h1z" class="f"></path><defs><linearGradient id="j" x1="745.899" y1="356.697" x2="759.947" y2="363.949" xlink:href="#B"><stop offset="0" stop-color="#838186"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#j)" d="M753 335l2 2 1 1 1 2 2 2 1 3 1 3c1 3 2 5 2 8s0 6-1 10h0c-1 3-2 5-3 7l-2-1c-1 3-2 4-5 6l1-2-1-2c0-3 0-5-1-7l-2-4-2-2-2-4h0l1-2h1c2-3 3-6 4-9 1-2 2-3 3-6l-1-3v-2z"></path><defs><linearGradient id="k" x1="751.667" y1="354.461" x2="762.963" y2="356.405" xlink:href="#B"><stop offset="0" stop-color="#aeacaf"></stop><stop offset="1" stop-color="#dddbda"></stop></linearGradient></defs><path fill="url(#k)" d="M753 335l2 2 1 1 1 2 2 2 1 3 1 3c1 3 2 5 2 8s0 6-1 10h0c-1 3-2 5-3 7l-2-1 1-2c3-9 1-19-3-27 0-1-1-2-1-3l-1-3v-2z"></path><path d="M753 335l2 2 1 1h-1v5c0-1-1-2-1-3l-1-3v-2z" class="F"></path><defs><linearGradient id="l" x1="300.461" y1="148.234" x2="300.532" y2="137.399" xlink:href="#B"><stop offset="0" stop-color="#f9cac9"></stop><stop offset="1" stop-color="#f5e0e0"></stop></linearGradient></defs><path fill="url(#l)" d="M207 141h10 0 2c4-2 11-2 16-2l13-1h44l24-1 14 1h26c3 0 7-1 10 0v1h6v1h-5 0c5 0 11 2 16 2h0-1c-1 0 0 0-1 1h0l-2 1 2 1 1 1c-2 0-4 1-6 1-3-1-8 0-11 0-2 1-4 2-6 2-3 1-7 1-10 4-1 1-2 1-3 1h-1v3l-2-1-1 1-7 3-1-2-2 1c-4-1-7 1-11 2-1 0-2 1-3 1-1 1-2 2-3 2l-1-1c1 0 1-1 1-1l-2-1c-2 1-3 1-5 1-1 1-2 1-3 2h-7c-3-1-5-2-8-2l-1-2c-1 0-2-1-3-1-2-1-3-3-5-4v-1h-1v-2l-1 1c-1 0-2 0-3-1-1 0-1 0-1 1l-2 2-3-3-4 1-7-4-14-3h-5c-3 0-6-1-9-1-4 0-7 0-11 1-1-1-1-1-1-2l-6-1h-1-2l-3-2z"></path><path d="M374 144h5l2 1c-2 0-3 0-5 1v-1l-2-1z" class="a"></path><path d="M366 145l8-1 2 1v1c-3 0-6 1-9 0l-1-1z" class="AA"></path><path d="M381 145l1 1c-2 0-4 1-6 1-3-1-8 0-11 0l2-1c3 1 6 0 9 0 2-1 3-1 5-1z" class="R"></path><path d="M241 145c5 0 12 0 17 2l-1 1h1l1 1-14-3-4-1z" class="a"></path><path d="M219 144l15 1h7l4 1h-5c-3 0-6-1-9-1-4 0-7 0-11 1-1-1-1-1-1-2z" class="B"></path><path d="M258 147l9 3 3 2-4 1-7-4-1-1h-1l1-1z" class="AV"></path><path d="M335 144c1 0 4 1 5 1 2 0 4-1 6-2 5-1 9-1 14 1l6 1 1 1-2 1c-2 1-4 2-6 2-3 1-7 1-10 4-1 1-2 1-3 1h-1c-1-1-1-1-2 0h-4 2c1-1 2-1 4-2h-1c-1-1-1-1-1-2s0-2-1-2l-2-1c-2-1-3-2-5-3z" class="u"></path><path d="M360 144l6 1 1 1-2 1c-2 1-4 2-6 2-3 1-7 1-10 4-1 1-2 1-3 1h-1c-1-1-1-1-2 0h-4 2c1-1 2-1 4-2h1c3-4 5-2 9-3h1c1-1 2-2 3-2h2c0-2 0-2-1-3z" class="M"></path><path d="M268 143l-4-2c3 0 6 0 9 1 7 1 13 3 20 1 0 1-1 1-2 1-4 2-7 4-10 8l-1 2v-2l-1 1c-1 0-2 0-3-1-1 0-1 0-1 1l-2 2-3-3-3-2c0-1 1-2 1-3v-4z" class="e"></path><path d="M269 144c4 0 7 2 10 4 1 1 1 1 1 2-1 1-1 0-1 1l-10-7z" class="AA"></path><path d="M268 143l1 1 10 7 1 1-1 1c-1 0-2 0-3-1-1 0-1 0-1 1l-2 2-3-3-3-2c0-1 1-2 1-3v-4z" class="AW"></path><path d="M300 141c7-2 13 1 20 1 5 0 10 0 15 2 2 1 3 2 5 3l2 1c1 0 1 1 1 2s0 1 1 2h1c-2 1-3 1-4 2h-2 4c1-1 1-1 2 0v3l-2-1-1 1-7 3-1-2-2 1c-4-1-7 1-11 2-1 0-2 1-3 1-1 1-2 2-3 2l-1-1c1 0 1-1 1-1l-2-1c-2 1-3 1-5 1-1 1-2 1-3 2h-7c-3-1-5-2-8-2l-1-2c-1 0-2-1-3-1-2-1-3-3-5-4v-1h-1l1-2c3-4 6-6 10-8 1 0 2 0 2-1l4-1c1 0 2-1 3-1z" class="AC"></path><path d="M309 160h4v1h0c-2 1-3 1-5 1l1-2z" class="H"></path><path d="M334 158l9-3h0 1l-1 1-1 1-7 3-1-2z" class="AJ"></path><path d="M297 142c1 0 2-1 3-1 3 0 7 0 10 1 2 1 5 2 6 4 1 0 1 1 1 2l-2-1-1 1c0-1-1-2-2-3s-4-1-7-1c-2 0-3 0-5 1 1-1 2-2 3-2-2-1-4-1-6-1z" class="AK"></path><path d="M343 150c0 1 0 1 1 2h1c-2 1-3 1-4 2h-2-1c-1 1-1 1-2 1h0c-2 1-4 2-5 2h-4c-2 1-4 1-6 0h-1c-1 1-2 1-3 1h0c0-2 0-3 1-5l1 1h3c1 1 2 1 4 2 1-1 3-1 4-1l3-1c2 0 5-1 7-2v-1c2 0 2 0 3-1z" class="T"></path><path d="M319 143v7c8 1 13 1 21-2v-1h0l2 1c1 0 1 1 1 2-1 1-1 1-3 1v1c-2 1-5 2-7 2l-3 1c-1 0-3 0-4 1-2-1-3-1-4-2h-3l-1-1c1-3 0-7 1-10z" class="S"></path><path d="M320 142c5 0 10 0 15 2 2 1 3 2 5 3h0v1c-8 3-13 3-21 2v-7l1-1z" class="g"></path><path d="M305 144c3 0 6 0 7 1s2 2 2 3l1-1 2 1c1 2 0 4-1 6s-2 3-4 4c-2 0-3 1-4 0h-1-1c-2-1-4-2-5-4-1-1-1-2-1-4h1c1-3 2-4 4-6z" class="m"></path><path d="M301 154c-1-1-1-2-1-4h1c1 3 2 5 5 6h3 2l-1 1h-1-2l1 1h0-1-1c-2-1-4-2-5-4z" class="l"></path><path d="M314 148l1-1 2 1c1 2 0 4-1 6s-2 3-4 4c-2 0-3 1-4 0h0l-1-1h2 1l1-1h-2c1 0 2-1 2-1 3-2 3-4 3-7z" class="AR"></path><path d="M293 143l4-1c2 0 4 0 6 1-1 0-2 1-3 2 2-1 3-1 5-1-2 2-3 3-4 6h-1c0 2 0 3 1 4l-1 1c2 2 4 5 7 5h2l-1 2c-1 1-2 1-3 2h-7c-3-1-5-2-8-2l-1-2c-1 0-2-1-3-1-2-1-3-3-5-4v-1h-1l1-2c3-4 6-6 10-8 1 0 2 0 2-1z" class="AL"></path><path d="M284 155c1 0 2-1 3-1 5 1 5-3 9-3 1-1 1-1 2-1 0-1 0-2 1-3l1-1c-1 2-1 4-1 7h-3c-1 1-1 2-3 2h0l-1 1h0c-1 1-2 2-3 2-2-1-4-1-5-3z" class="c"></path><path d="M299 153v4l-5 1c-1 0-1 1-2 2l-1-1h-5c-2-1-3-3-5-4v-1l3 1c1 2 3 2 5 3 1 0 2-1 3-2h0l1-1h0c2 0 2-1 3-2h3z" class="B"></path><path d="M300 145c2-1 3-1 5-1-2 2-3 3-4 6h-1c0 2 0 3 1 4l-1 1c2 2 4 5 7 5h2l-1 2c-1 1-2 1-3 2h-7c-3-1-5-2-8-2l-1-2c-1 0-2-1-3-1h5l1 1c1-1 1-2 2-2l5-1v-4c0-3 0-5 1-7v-1z" class="T"></path><path d="M289 160c5 0 9 2 13 2h6c-1 1-2 1-3 2h-7c-3-1-5-2-8-2l-1-2z" class="AC"></path><defs><linearGradient id="m" x1="815.612" y1="247.476" x2="837.416" y2="272.952" xlink:href="#B"><stop offset="0" stop-color="#a4a3a5"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#m)" d="M852 221c14 4 26 10 32 22l2 3c1 3 2 7 3 10h-1c0-1 0-2-1-3v-1-2l-2-2v-1l-2-5-4-4h-1c0 2-3 3-4 4l-3 2-5 3-2 1c-3 2-5 3-8 4l-4 1-3 2-6 3h-2v1c-1 0-2 1-3 1l-2 1-3 3-6 4c-1 1-2 2-4 3l-3 3c0 2-2 3-3 4 0 1-1 2-2 2 0 1-1 1-1 2 0 2-2 2-2 3l-1 1-1 2v1l-2 2h0c0 3-3 7-5 9h4c1-1 2-1 3-1h2c1-1 2-1 3-1h0 2l1-1h1c-1 1-1 1-1 2h-1v1l1 1v1 1h0v2 1 1l-1 1-1 2h0c-1 1-1 1-1 2l-3 3-1-2c2-3 4-7 4-11h0-1l-3 1c1-2 3-2 4-4-6 1-11 2-17 2-1 0-3 0-5-1h1l-1-1h-2l-1 1-1-1h-1c0-1 0-1-1-2 1-6 3-10 5-15 0-1 1-3 1-4 2-5 6-12 9-15l5-6c2-3 5-7 9-9l1-1 7-5 19-11c3-2 9-6 9-10z"></path><defs><linearGradient id="n" x1="318.737" y1="183.041" x2="255.829" y2="129.934" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#4d1f1f"></stop></linearGradient></defs><path fill="url(#n)" d="M245 146l14 3 7 4 4-1 3 3 2-2c0-1 0-1 1-1 1 1 2 1 3 1l1-1v2h1v1c2 1 3 3 5 4 1 0 2 1 3 1l1 2c3 0 5 1 8 2h7c1-1 2-1 3-2 2 0 3 0 5-1l2 1s0 1-1 1l1 1c1 0 2-1 3-2 1 0 2-1 3-1 4-1 7-3 11-2l2-1 1 2 7-3 1-1 2 1-2 4c-1 4-2 6-5 9l2 2-2 2c-2 3-4 7-7 9-4 1-5 3-7 5l-1 1-2-1-8 7c-8 5-14 6-23 4h-1l-1 1c-3-1-5-3-7-4l-8-6c-4-4-6-8-7-13v-1c-1 1-1 0-2 1l-2 3-1-1c4-5 5-8 4-14-3-6-6-8-11-12-3-1-5-2-7-3-1-1-1-1-2-1s-2-1-3-1h-1l5-1-6-1h5z"></path><path d="M298 165c2 0 3 1 4 2-4 0-7 0-11-2h2 5z" class="e"></path><path d="M280 161c2 0 4 0 6 1l2-1c1 1 1 1 2 1 3 0 5 1 8 2v1h-5-2c-4-1-8-2-11-4z" class="o"></path><path d="M280 152v2h1v1c2 1 3 3 5 4 1 0 2 1 3 1l1 2c-1 0-1 0-2-1l-2 1c-2-1-4-1-6-1-1 0-3-2-4-2l-10-6 4-1 3 3 2-2c0-1 0-1 1-1 1 1 2 1 3 1l1-1z" class="R"></path><path d="M281 155c2 1 3 3 5 4 1 0 2 1 3 1l1 2c-1 0-1 0-2-1-2-1-3-1-5-1s-3-1-4-2c1-1 1-2 2-3z" class="AJ"></path><path d="M280 152v2h1v1c-1 1-1 2-2 3l-2-1c-1-1-2-1-4-2l2-2c0-1 0-1 1-1 1 1 2 1 3 1l1-1z" class="AT"></path><path d="M275 153c1 2 2 2 2 4-1-1-2-1-4-2l2-2z" class="AJ"></path><path d="M314 163l1 1c1 0 2-1 3-2 1 0 2-1 3-1 4-1 7-3 11-2l2-1 1 2-4 2-8 2c-7 2-14 3-21 3-1-1-2-2-4-2v-1h7c1-1 2-1 3-2 2 0 3 0 5-1l2 1s0 1-1 1z" class="j"></path><path d="M313 161l2 1s0 1-1 1c-3 1-6 1-9 1 1-1 2-1 3-2 2 0 3 0 5-1z" class="J"></path><path d="M332 159l2-1 1 2-4 2-8 2h0l-1-1 10-4z" class="AT"></path><path d="M343 156l2 1-2 4c-1 4-2 6-5 9l-1 1s-1 0-2 1v1l-1-1c-1 1-3 2-4 2-1 3-4 3-7 4h-4l6-5c2-2 6-5 8-7l3-3 1-1h-5-1l4-2 7-3 1-1z" class="P"></path><path d="M335 160l7-3 1 2c-2 3-7 5-10 7l3-3 1-1h-5-1l4-2z" class="Z"></path><path d="M325 173c6 2 6-3 11-4h0l4-4c1-2 1-2 2-3l1-1c-1 4-2 6-5 9l-1 1s-1 0-2 1v1l-1-1c-1 1-3 2-4 2-1 3-4 3-7 4h-4l6-5z" class="y"></path><path d="M266 176v-4c2 2 3 5 5 7 5 6 10 9 18 10h4v1c1 0 1 1 1 2 2 1 6 0 8 1 1 0 1-1 2 0 2 1 6-1 7 0 1 0 1 2 2 2-8 5-14 6-23 4h-1l-1 1c-3-1-5-3-7-4l-8-6c-4-4-6-8-7-13v-1z" class="U"></path><path d="M271 179c5 6 10 9 18 10-3 1-6 0-9-1h-3c-3-2-5-5-6-9z" class="M"></path><path d="M266 177c2 1 3 3 4 5l6 9c4 3 8 5 13 8l-1 1c-3-1-5-3-7-4l-8-6c-4-4-6-8-7-13z" class="AV"></path><defs><linearGradient id="o" x1="313.731" y1="174.161" x2="314.36" y2="192.488" xlink:href="#B"><stop offset="0" stop-color="#5e1f23"></stop><stop offset="1" stop-color="#992e32"></stop></linearGradient></defs><path fill="url(#o)" d="M338 170l2 2-2 2c-2 3-4 7-7 9-4 1-5 3-7 5l-1 1-2-1-8 7c-1 0-1-2-2-2-1-1-5 1-7 0-1-1-1 0-2 0-2-1-6 0-8-1 0-1 0-2-1-2v-1c1 0 3 0 5-1 8-2 15-5 21-10h4c3-1 6-1 7-4 1 0 3-1 4-2l1 1v-1c1-1 2-1 2-1l1-1z"></path><path d="M338 170l2 2-2 2c-2 3-4 7-7 9-4 1-5 3-7 5l-1 1-2-1c6-4 11-10 15-15l1-2 1-1z" class="a"></path><path d="M338 170l2 2-2 2v-1h-2l1-2 1-1z" class="AO"></path><defs><linearGradient id="p" x1="745.09" y1="384.794" x2="761.747" y2="409.272" xlink:href="#B"><stop offset="0" stop-color="#5c565e"></stop><stop offset="1" stop-color="#716e75"></stop></linearGradient></defs><path fill="url(#p)" d="M743 390c3 0 7-1 10-2 11-2 22-5 33-9 5-2 9-5 14-6 1 0 2-1 4 0l-1 1v1l3-2c0 2-2 5-4 7-5 5-11 8-16 12-4 3-7 8-11 12-2 4-5 8-7 12-4 8-6 15-6 24-1-1-3-1-4-2l-5-1c-1 0-3 0-5-1-3 0-6 0-8-2-1-2-2-4-1-6h-2c-4 4-7 9-11 13l-1-1-1-1h-1-1v-1l-5-1-3-2c2-3 3-8 4-12 4-8 7-17 10-25l2-6h1 3 0l2-1h3l4-1z"></path><path d="M731 392h3v1l-1 1c-2 2-3 3-5 4l2-6h1z" class="AM"></path><path d="M731 408l-1-1c1 0 1-1 1-1-2 1-3 5-5 7v1c2-5 4-9 7-13 1 0 2 0 3-1s3-2 4-2c3-1 7-2 9-4s5-2 8-3c6-1 13-2 19-4 4-1 5-3 9-3l-2 2c-1 0-2 1-3 1-1-1-5 2-6 3h-4c-1 1-2 1-3 2h-5c-1 1-2 1-3 2-6 1-12 2-18 5-2 1-4 2-5 3-2 1-4 4-5 6h0z" class="AF"></path><path d="M731 408h0c1-2 3-5 5-6 1-1 3-2 5-3 6-3 12-4 18-5 1-1 2-1 3-2h5c1-1 2-1 3-2h4c1-1 5-4 6-3 1 0 2-1 3-1-3 3-6 5-10 7v1c-1 0-1 1-2 1v-1c1 0 0 0 1-1h-1v-1c-11 2-23 4-32 10l-1 1c-3 2-5 5-6 8-3 6-6 12-7 19-1 3-1 6-1 8l-1 1h-1v-1-1-1c0-2-1-4 0-6l4-11 3-8c0-2 1-2 2-3z" class="v"></path><path d="M723 439l1-1c0-2 0-5 1-8 1-7 4-13 7-19 1-3 3-6 6-8l1-1c9-6 21-8 32-10v1h1c-1 1 0 1-1 1v1c1 0 1-1 2-1l-10 9h-1v-1c1-1 1-1 1-2h1-6c-1 0-3 0-5 1h-2c-2 1-2 1-4 0-3 1-5 3-7 6-3 2-6 4-8 8-2 3-3 8-4 12 0 1-1 3-1 5-1 2 0 6-2 7v1l-1-1h-1z" class="k"></path><path d="M747 401c3-1 6-3 9-3 4-2 9-2 13-3-2 3-4 5-7 7 1-1 1-1 1-2h1-6c-1 0-3 0-5 1h-2c-2 1-2 1-4 0z" class="G"></path><defs><linearGradient id="q" x1="732.281" y1="413.802" x2="755.581" y2="422.242" xlink:href="#B"><stop offset="0" stop-color="#9d9b9f"></stop><stop offset="1" stop-color="#c9c6c5"></stop></linearGradient></defs><path fill="url(#q)" d="M747 401c2 1 2 1 4 0h2c2-1 4-1 5-1h6-1c0 1 0 1-1 2v1c-5 4-9 8-14 13-2 4-6 10-9 12h-2c-4 4-7 9-11 13l-1-1v-1c2-1 1-5 2-7 0-2 1-4 1-5 1-4 2-9 4-12 2-4 5-6 8-8 2-3 4-5 7-6z"></path><path d="M806 373c0 2-2 5-4 7-5 5-11 8-16 12-4 3-7 8-11 12-2 4-5 8-7 12-4 8-6 15-6 24-1-1-3-1-4-2l-5-1c-1 0-3 0-5-1-3 0-6 0-8-2-1-2-2-4-1-6 3-2 7-8 9-12 5-5 9-9 14-13h1l10-9v-1c4-2 7-4 10-7l2-2 6-3c4-2 8-5 12-7v1l3-2z" class="E"></path><path d="M789 385v1c-3 4-7 7-10 11-4 3-6 8-9 12-5 8-9 14-10 23-1 2-1 4-1 6h-1l-5-1c2-5 4-11 6-16 4-8 9-15 15-21 5-5 9-10 15-15z" class="i"></path><path d="M791 381c4-2 8-5 12-7v1c-4 2-8 5-12 8 0 0-2 1-2 2-6 5-10 10-15 15-6 6-11 13-15 21-2 5-4 11-6 16-1 0-3 0-5-1l1-1v-4-2c3-5 4-10 8-14 1-3 4-5 6-8 8-10 19-17 28-26z" class="C"></path><path d="M748 436l1-1v-4-2c3-5 4-10 8-14l-6 13h1c-1 2-2 5-3 8h-1z" class="O"></path><path d="M785 384l6-3c-9 9-20 16-28 26-2 3-5 5-6 8-4 4-5 9-8 14v2 4l-1 1c-3 0-6 0-8-2-1-2-2-4-1-6 3-2 7-8 9-12 5-5 9-9 14-13h1l10-9v-1c4-2 7-4 10-7l2-2z" class="E"></path><defs><linearGradient id="r" x1="386.311" y1="236.675" x2="416.993" y2="234.182" xlink:href="#B"><stop offset="0" stop-color="#a8a7a9"></stop><stop offset="1" stop-color="#e8e8e7"></stop></linearGradient></defs><path fill="url(#r)" d="M390 200c0-2 1-4 2-5 4-8 10-15 16-20v1c-1 1-1 2-2 3-2 1-4 3-5 5l-1 1v2c1 1 1 1 1 2v1c0 1 0 1 1 2v3l1 1v3c0 1 0 2 1 4v25c0 3-1 7 0 9 0 1 0 3 1 4l-1 1 1 1v3h1l-1 1 1 1h0v1 1h1l-1 1 1 1h0v1 1l1 1h0v1l1 1h-1c1 2 1 3 2 4v1h0v1l1 1v2l1 1c0 1 1 2 1 4 1 1 1 3 2 5l1 1 2 3v1h1l3 3c1 1 2 1 4 1h1c2 0 2 0 4 1l1 1h2v1h0c-1 2-1 3-2 4h0c3-1 6 0 9-1l5-1c-1 1-2 2-2 3l-1 1h-1c-2 3-3 5-4 9v2c-1-1-3-2-5-2 0 0 0 1-1 1-3 1-5 1-7 4 0 0 0 1-1 1-3 0-4 2-6 3 0 1-1 2-2 2l-1 1c-1 0 0-1 0-1v-2l-1 3-1-1v-3c1-1 1-1 1-2l1-1v-2c1-1 1-2 1-3h-2c-3-4-10-4-15-6-3-2-5-4-8-6v-1c-2-1-2-2-2-3 0-2 0-4-1-6h-1c-1-1-1-5-1-7-2-12-3-23-3-35 1-3 0-5 1-8v-5c0 1 0 2 1 3v2 1h0 0l1-8v-10c0-4 2-10 4-14z"></path><path d="M390 200c1 4-2 7-2 11v3-1l1 1c-1 3-1 6-2 10v8c-1 1-1 2-1 3l-1 1v-4h0l1-8v-10c0-4 2-10 4-14z" class="E"></path><defs><linearGradient id="s" x1="380.586" y1="272.563" x2="407.742" y2="257.835" xlink:href="#B"><stop offset="0" stop-color="#7e7c83"></stop><stop offset="1" stop-color="#abaaac"></stop></linearGradient></defs><path fill="url(#s)" d="M383 239c1-3 0-5 1-8v-5c0 1 0 2 1 3v2 1h0v4l1-1c0-1 0-2 1-3 0 3-1 6 0 9 2 5 2 10 5 15h1v-2c3 8 6 18 14 23 1 1 1 1 1 2 1 2 5 4 8 4 5 2 10 3 15 3l1 1h2v1h0c-1 2-1 3-2 4h0c3-1 6 0 9-1l5-1c-1 1-2 2-2 3l-1 1h-1c-2 3-3 5-4 9v2c-1-1-3-2-5-2 0 0 0 1-1 1-3 1-5 1-7 4 0 0 0 1-1 1-3 0-4 2-6 3 0 1-1 2-2 2l-1 1c-1 0 0-1 0-1v-2l-1 3-1-1v-3c1-1 1-1 1-2l1-1v-2c1-1 1-2 1-3h-2c-3-4-10-4-15-6-3-2-5-4-8-6v-1c-2-1-2-2-2-3 0-2 0-4-1-6h-1c-1-1-1-5-1-7-2-12-3-23-3-35z"></path><defs><linearGradient id="t" x1="380.538" y1="254.197" x2="431.899" y2="290.643" xlink:href="#B"><stop offset="0" stop-color="#9f9da0"></stop><stop offset="1" stop-color="#fafaf9"></stop></linearGradient></defs><path fill="url(#t)" d="M427 291h-2c-13-2-24-7-31-18-5-7-10-18-8-26 1 0 1 0 2 1v2c2 4 1 7 3 10 1 4 3 8 5 11h1c7 9 16 13 27 15 3 1 6 1 8 1h2v1h0c-1 2-1 3-2 4l-5-1z"></path><defs><linearGradient id="u" x1="401.478" y1="299.003" x2="412.775" y2="287.739" xlink:href="#B"><stop offset="0" stop-color="#adabad"></stop><stop offset="1" stop-color="#dadada"></stop></linearGradient></defs><path fill="url(#u)" d="M391 290l-1-3v-4-1l1 1c8 9 15 12 27 12h5l4-4 5 1h0c3-1 6 0 9-1l5-1c-1 1-2 2-2 3l-1 1h-1c-2 3-3 5-4 9v2c-1-1-3-2-5-2 0 0 0 1-1 1-3 1-5 1-7 4 0 0 0 1-1 1-3 0-4 2-6 3 0 1-1 2-2 2l-1 1c-1 0 0-1 0-1v-2l-1 3-1-1v-3c1-1 1-1 1-2l1-1v-2c1-1 1-2 1-3h-2c-3-4-10-4-15-6-3-2-5-4-8-6v-1z"></path><path d="M426 300h-1 1 0z" class="K"></path><path d="M414 315v-3c1-3 2-7 5-9-1 2-2 6-2 7l1 1-2 3-1 1c-1 0 0-1 0-1v-2l-1 3z" class="X"></path><path d="M432 292c3-1 6 0 9-1l5-1c-1 1-2 2-2 3l-1 1c-3 0-5 2-7 3l-3 1h-1-1c-3 1-6 1-9 1v-1c-1 0-1 0-2-1 1-1 3-1 4-1s2 0 3-1h1-5l4-4 5 1h0z" class="D"></path><path d="M427 291l5 1h0 6c-2 2-8 2-10 3h-5l4-4z" class="C"></path><path d="M436 297c2-1 4-3 7-3h-1c-2 3-3 5-4 9v2c-1-1-3-2-5-2 0 0 0 1-1 1-3 1-5 1-7 4 0 0 0 1-1 1-3 0-4 2-6 3 0 1-1 2-2 2l2-3-1-1c0-1 1-5 2-7h0c1 0 2-1 3-1h1c1 0 2-1 3-2h0 1 1l4-2h1l3-1z" class="N"></path><path d="M436 297h0v1c-1 1-2 1-3 0l3-1z" class="X"></path><defs><linearGradient id="v" x1="420.35" y1="305.344" x2="430.69" y2="307.46" xlink:href="#B"><stop offset="0" stop-color="#423b44"></stop><stop offset="1" stop-color="#56545a"></stop></linearGradient></defs><path fill="url(#v)" d="M418 311c1-2 3-4 5-5 3-3 6-4 10-3 0 0 0 1-1 1-3 1-5 1-7 4 0 0 0 1-1 1-3 0-4 2-6 3 0 1-1 2-2 2l2-3z"></path><defs><linearGradient id="w" x1="400.868" y1="351.293" x2="445.5" y2="343.298" xlink:href="#B"><stop offset="0" stop-color="#8a888e"></stop><stop offset="1" stop-color="#c8c8c7"></stop></linearGradient></defs><path fill="url(#w)" d="M391 291c3 2 5 4 8 6 5 2 12 2 15 6h2c0 1 0 2-1 3v2l-1 1c0 1 0 1-1 2v3l1 1 1-3v2s-1 1 0 1c0 2-2 5-3 7s-2 5-2 7-1 5-1 7l4 2 7 2h2 1c1 1 8 2 9 3s1 3 3 4c1 1 1 3 1 5l1 1v1c0 1 1 2 1 3 1 1 1 1 1 2h1l3 3h0c2 2 4 2 6 3 3 0 5 1 8 0h1 1l4-3c0 3 0 7-1 9 0 1-2 2-3 3l-2 1c-1 1-2 1-3 3h0c1 2 1 4 2 6v3c-2 1-4 1-6 1 0 1-1 3 0 4h0c1 2 1 1 1 3h0l1 1h0v1l1 1c1 1 1 1 1 2l2 2h-1l1 1v1l1-1c1 0 1 0 2-1 2 0 3-1 4-2h1c-3 2-6 3-9 5h0l-2 2 1 1 3-2c-3 3-6 6-8 9-3 5-5 13-6 19 0 1 0 1-2 1 0-7 2-16 6-22v-2c-2 0-3 0-4-1v-1l-2 1c-1-1-4-3-5-4l-1 1-1-1h1v-1c0-1-2-2-3-3-3-3-5-7-8-11-2-4-5-12-8-14l-3-6c-1-1-2-6-3-7-5-14-10-27-14-41-2-8-4-16-5-24 1-1 1-2 1-4-1-1-1-3-1-4z"></path><path d="M410 329c0 2-1 5-1 7h0c-1-1-1 0-1-1 0-3 0-4 2-6z" class="F"></path><path d="M430 393h-1c0-2-1-2-2-3v-1l9 9h-1l-5-5z" class="C"></path><path d="M413 371l1 1h1c0 2 1 5 2 5 2 1 3 5 4 7 2 5 6 10 9 14 1 2 4 6 7 6l6 5-2 1c-1-1-4-3-5-4l-1 1-1-1h1v-1c0-1-2-2-3-3-3-3-5-7-8-11-2-4-5-12-8-14l-3-6z" class="I"></path><path d="M420 341c2 1 5 1 7 2s3 3 5 3v1 1l1 1c2 2 3 6 4 9-1-1-3-3-4-5-4-6-11-4-16-8l1-1h4-2v-1h2l-2-2z" class="C"></path><path d="M409 336l4 2 7 2h2 1c1 1 8 2 9 3s1 3 3 4c1 1 1 3 1 5l1 1v1c0 1 1 2 1 3 1 1 1 1 1 2h1l3 3h0c2 2 4 2 6 3 3 0 5 1 8 0h1 1l4-3c0 3 0 7-1 9 0 1-2 2-3 3l-2 1c-1 1-2 1-3 3h0c1 2 1 4 2 6v3c-2 1-4 1-6 1 0 1-1 3 0 4h0c1 2 1 1 1 3h0l1 1h0v1l1 1c1 1 1 1 1 2l2 2h-1l1 1v1l1-1c1 0 1 0 2-1 2 0 3-1 4-2h1c-3 2-6 3-9 5h0l-2 2 1 1 3-2c-3 3-6 6-8 9-3 5-5 13-6 19 0 1 0 1-2 1 0-7 2-16 6-22v-2c-2 0-3 0-4-1v-1l-6-5c-1-2-2-3-4-5h-1c-1-2-2-4-2-6l5 5h1c3 3 8 6 13 8-2-2-5-4-6-7-2-2-3-5-5-7v-1c-1-2-1-4 0-5 0-1 1-1 2-1s1 0 2-1c2-1 2-3 3-4 1-2 2-2 4-2l1-1c1 0 1 0 2-1h0c0-2-1-4-2-5 0-2-2-3-3-3l-3-1h-1c-2-1-2-3-3-5h1l-4-4c-1-3-2-7-4-9l-1-1v-1-1c-2 0-3-2-5-3s-5-1-7-2c-2 0-4 0-6-1-3-1-4-2-5-4h0z" class="N"></path><path d="M280 314l1 2 3 8 3 6 1 3 7 18v1l-3 3v1l-1 1-2 2v1c1 1 2 1 4 1s3 0 4 1c2 2 2 4 3 6 2 5 4 11 6 16l2 4 2 5c-2 1-9-1-12-2-2-1-6-1-9-1l-16-4c-1 1-2 1-3 1s-2 0-3-1v-1c-8-2-16-5-24-9v-1c-1-1-1-2-2-3 0 0-1 0-1-1s1-3 0-4l4-5 2-12 1-3 1 2h1v-1l3-9 3-5v5l1-3 2-4v-3c3-5 8-8 13-12l8-2 1-1z" class="z"></path><path d="M268 385c1 0 3 1 5 1-1 1-2 1-3 1s-2 0-3-1v-1h1z" class="k"></path><defs><linearGradient id="x" x1="287.234" y1="361.581" x2="287.762" y2="369.856" xlink:href="#B"><stop offset="0" stop-color="#959296"></stop><stop offset="1" stop-color="#b2b1b1"></stop></linearGradient></defs><path fill="url(#x)" d="M285 372c0-4 2-8 4-12 1 1 2 1 4 1s3 0 4 1c2 2 2 4 3 6 2 5 4 11 6 16h-4c-2-1-3-3-4-5v-1h0v-1c0-1 0 0-1-1-1-4-2-7-4-9h-2c-2 2-3 5-3 8-1 2-1 4-1 6-1 0-1 1-2 1l-1-1-1-4 1-1c0-1 0-3 1-4z"></path><path d="M284 376c0-1 0-3 1-4h1c0 1 0 1-1 3v2h0l-1-1z" class="G"></path><defs><linearGradient id="y" x1="290.374" y1="351.872" x2="274.875" y2="367.379" xlink:href="#B"><stop offset="0" stop-color="#c8c6c7"></stop><stop offset="1" stop-color="#edebe7"></stop></linearGradient></defs><path fill="url(#y)" d="M284 336h0v1 7c0 2 1 2 2 4 1 0 1 1 2 2 0 2 2 3 3 3 1 1 1 2 1 3l-1 1-2 2v1c-2 4-4 8-4 12-1 1-1 3-1 4l-1 1 1 4c-5-3-8-9-10-15-1-4-1-9 0-13 1-6 3-10 7-15 1-1 2-2 3-2z"></path><path d="M284 336h0v1c-4 10-9 22-4 33 0 2 1 5 3 7l1 4c-5-3-8-9-10-15-1-4-1-9 0-13 1-6 3-10 7-15 1-1 2-2 3-2z" class="h"></path><defs><linearGradient id="z" x1="243.011" y1="343.429" x2="264.121" y2="384.214" xlink:href="#B"><stop offset="0" stop-color="#aaa8aa"></stop><stop offset="1" stop-color="#dfdedd"></stop></linearGradient></defs><path fill="url(#z)" d="M280 314l1 2 3 8 3 6 1 3 7 18v1l-3 3v1c0-1 0-2-1-3-1 0-3-1-3-3-1-1-1-2-2-2-1-2-2-2-2-4v-7-1h0c-1 0-2 1-3 2-4 5-6 9-7 15l-1-2c-1-1-3-1-4-1s-1 0-1 1c-3 1-3 3-5 4 0 1-2 1-2 1-2 7-4 12-4 19 1 2 2 4 4 5 1 1 6 3 7 5h-1c-8-2-16-5-24-9v-1c-1-1-1-2-2-3 0 0-1 0-1-1s1-3 0-4l4-5 2-12 1-3 1 2h1v-1l3-9 3-5v5l1-3 2-4v-3c3-5 8-8 13-12l8-2 1-1z"></path><path d="M255 334v5l-2 4v-2l1-2v-1c-1 3-3 8-5 10l3-9 3-5z" class="C"></path><path d="M240 367l4-5c0 4-1 7-3 10 0 0-1 0-1-1s1-3 0-4z" class="h"></path><defs><linearGradient id="AA" x1="267.533" y1="334.816" x2="272.973" y2="351.185" xlink:href="#B"><stop offset="0" stop-color="#b6b4b5"></stop><stop offset="1" stop-color="#e7e5e3"></stop></linearGradient></defs><path fill="url(#AA)" d="M261 356c1-7 3-14 8-20 2-2 5-3 7-3l1 1h-1l1 1c0 1 1 1 2 2l2 1c-4 5-6 9-7 15l-1-2c-1-1-3-1-4-1s-1 0-1 1c-3 1-3 3-5 4 0 1-2 1-2 1z"></path><path d="M280 314l1 2 3 8 3 6 1 3 7 18v1l-3 3v1c0-1 0-2-1-3-1 0-3-1-3-3-1-1-1-2-2-2-1-2-2-2-2-4v-7-1h0v-1h0c-4-2-7-7-11-6-3 1-7 5-8 7l-1 1-3 6c-3 8-5 15-8 24 0-3-1-10 0-12l1-4c0-2 0-3 1-5 0-1 1-3 1-5l1-4-1-1 2-4v-3c3-5 8-8 13-12l8-2 1-1z" class="i"></path><path d="M258 345l-4 10h-1l1-4h1l3-6z" class="v"></path><path d="M255 346l1 1c2-3 2-7 4-9l2-2c0-1 1-2 2-3 0 1-1 2-1 4l-1 1c-2 2-3 5-4 7l-3 6h-1c0-2 0-3 1-5z" class="AB"></path><path d="M256 341l2-1c0-3 3-3 4-5v-1c1-4 7-8 10-9l1-1h1c1 0 6-1 7 0l6 6 1 3h-1c-2 0-2-2-4-3-3-5-8-6-13-3-3 2-3 4-6 6-1 1-2 2-2 3l-2 2c-2 2-2 6-4 9l-1-1c0-1 1-3 1-5z" class="AF"></path><path d="M284 335l3-1h0v1c0 7 4 11 8 16v1l-3 3v1c0-1 0-2-1-3-1 0-3-1-3-3-1-1-1-2-2-2-1-2-2-2-2-4v-7-1h0v-1z" class="C"></path><path d="M280 314l1 2 3 8 3 6-6-6c-1-1-6 0-7 0h-1l-1 1c-3 1-9 5-10 9v1c-1 2-4 2-4 5l-2 1 1-4-1-1 2-4v-3c3-5 8-8 13-12l8-2 1-1z" class="AH"></path><path d="M258 332c1-1 1-1 3-2l-4 7-1-1 2-4z" class="O"></path><path d="M281 316l3 8c-3-2-5-2-9-1-1 0-1-1-2-1s-2 1-2 1c-1 0-2-1-2-1 1-1 4-3 6-4 2 0 4 0 6-2z" class="f"></path><defs><linearGradient id="AB" x1="269.544" y1="325.337" x2="261.358" y2="322.249" xlink:href="#B"><stop offset="0" stop-color="#908d94"></stop><stop offset="1" stop-color="#b1afb0"></stop></linearGradient></defs><path fill="url(#AB)" d="M280 314l1 2c-2 2-4 2-6 2-2 1-5 3-6 4-3 3-6 5-8 8-2 1-2 1-3 2v-3c3-5 8-8 13-12l8-2 1-1z"></path><path d="M893 85c0-1 1-2 2-3-4 14-6 30-5 46 1 18 7 35 14 52l18 43c0 2-1 3-2 5h-2c-2 0-3 1-4 2-1-1-1-3-1-4-1 1-1 1-1 2v1l-2-7c-2-6-4-12-7-18l-3-3c-3-5-6-10-10-14-3-4-6-7-8-11l-12-10c-2-2-4-3-6-5l-4-3c-2 0-4-1-6 0-5-2-11-4-16-6-2-1-4-1-6-2s-3-1-4-1l-1-1v-1h0l-1-1 1-1 1-1 4-2 12-4-3-1 5-3c4-4 8-6 12-10 7-5 13-12 19-18 4-4 7-9 10-13 2-3 3-6 6-8z" class="l"></path><path d="M876 166v-2-1c-1-1-1-3-1-4l1 2c0 1 1 2 2 3l2 1c1 0 2 1 2 2v1 1l-2-2-2 2-1-1-1-2z" class="AM"></path><path d="M882 167c1 1 6 6 6 7v2c0 1 1 2 2 2s1 1 2 0c2-1 3-3 3-5v4l-2 2c0 1 0 1-1 2h-1v-2c-2 0-2-1-3-1h-1c-2-1-3-3-4-5 0-1 0-1-1-2h-1l1-1c1 1 1 2 2 3s1 2 2 3h1c-1-1-1-1-1-2v-1l-1-1c-1-1 0-1-1-2l-2-2v-1z" class="f"></path><path d="M889 153l6 20c0 2-1 4-3 5-1 1-1 0-2 0s-2-1-2-2v-2s1 1 2 1 2-1 3-2c0-7-3-14-5-20h1z" class="AF"></path><path d="M882 176c3 2 6 4 8 8 1 2 3 3 4 5 4 5 8 9 9 15l-3-3c-3-5-6-10-10-14-3-4-6-7-8-11z" class="AB"></path><path d="M866 125h3c-5 6-11 14-13 22v-1-4l-1-1-8 4-2-1 4-1c4-2 8-4 10-8v-1c-2 0-3-1-4-1h0 0c4-2 8-5 11-8h0z" class="J"></path><path d="M884 112h1c-1 14 0 27 4 41h-1l-3-9-2 2c-2 5-3 10-3 14v5l-2-1c-1-1-2-2-2-3l-1-2 1-1c0-1 0-2 1-3 1-2 2-4 2-7 1-2 2-3 3-4 0-2 2-4 2-5s0-2-1-3c0-8 0-17 1-24z" class="E"></path><path d="M882 144c0 1-1 3-1 4-2 4-2 7-2 11l1 1v5l-2-1c-1-1-2-2-2-3l-1-2 1-1c0-1 0-2 1-3 1-2 2-4 2-7 1-2 2-3 3-4z" class="v"></path><path d="M876 161l2-1c1 1 0 2 0 4-1-1-2-2-2-3z" class="AU"></path><path d="M846 134c3 0 5-1 8-1h1 0c1 0 2 1 4 1v1c-2 4-6 6-10 8l-4 1c-6 1-12 1-18 1l1-1 4-2 12-4-3-1 5-3z" class="c"></path><path d="M846 134c3 0 5-1 8-1l-10 5-3-1 5-3z" class="AJ"></path><path d="M883 136c1 1 1 2 1 3s-2 3-2 5c-1 1-2 2-3 4 0 3-1 5-2 7-1 1-1 2-1 3l-1 1c0 1 0 3 1 4v1 2l-2 1c-1-1-3-3-4-5-3 0-5-2-7-4 4-2 8-7 11-10 1-2 3-5 5-7 1 0 3-1 3-2l1-3z" class="V"></path><path d="M870 162h-1l-2-2c4-4 8-7 12-12h0c0 3-1 5-2 7-1 1-1 2-1 3l-1 1c0 1 0 3 1 4v1 2l-2 1c-1-1-3-3-4-5z" class="x"></path><path d="M880 160c0-4 1-9 3-14l2-2 3 9c2 6 5 13 5 20-1 1-2 2-3 2s-2-1-2-1c0-1-5-6-6-7 0-1-1-2-2-2v-5z" class="Z"></path><path d="M847 145l8-4 1 1v4 1c1 4 1 8 4 11-2 0-4-1-6 0-5-2-11-4-16-6-2-1-4-1-6-2s-3-1-4-1l-1-1v-1h0l-1-1 1-1c6 0 12 0 18-1l2 1z" class="H"></path><path d="M827 145c6 0 12 0 18-1l2 1c-3 1-6 1-9 1-3 1-9 0-10 2v1l-1-1v-1h0l-1-1 1-1z" class="U"></path><path d="M846 134c4-4 8-6 12-10 7-5 13-12 19-18 4-4 7-9 10-13 2-3 3-6 6-8-3 9-7 18-8 27h-1v-3l-15 16h-3 0c-3 3-7 6-11 8h0-1c-3 0-5 1-8 1z" class="AW"></path><path d="M866 125c-1-1-1 0-1-1 2-3 4-5 6-7l6-6v-1h1v2c-2 2-3 5-5 6l-3 3-4 4h0z" class="AJ"></path><path d="M222 226l2-1c3 2 7 2 10 3 5 0 10 5 15 5h1 1 0l2 1h0l-1 1 1 1c4 1 7 4 10 6l6 6 5 7 3 6h1l4 11c1 0 1 1 2 1l1 4 7 17 4 12h-1l6 19c0 3 1 6 2 9h0c0 2 1 3 1 4v1l2 7 2 6 2 9 1 4h0l1 8c2 2 2 3 3 6l1 1h0c0 2 0 3 1 4s2 1 3 1c0 0 1 2 1 3l-1 1h1c2 2 1 2 1 5l1 1h1c1 1 1 2 1 3v1l2 2c-1 1-1 2 0 4l1 1 1 1v2c0 1 1 1 1 2v1 4h1c1 1 2 3 2 5 0 1 1 3 1 5v1 5c1 2 1 4 2 5v6c1 0 1-1 2-1h2 1v-2l1 2h4 2l1-1h0l1 1c-2 1-2 1-3 2l-1 3c1 1 3 1 4 1-2 0-4 0-6 1 1 0 3 1 4 1v1h-1-1l-2 6v3c-1 2-2 4-4 6 0 1-2 2-2 3l-4 1-1-1c1-1 1-3 1-4l-11-35-5-14-4-10-2-6-2-7-2-5-2-4c-2-5-4-11-6-16-1-2-1-4-3-6-1-1-2-1-4-1s-3 0-4-1v-1l2-2 1-1v-1l3-3v-1l-7-18-1-3-3-6-3-8-1-2-11-23c-6-13-12-26-20-38-2-5-6-9-9-13l-3-3h0c-5-4-9-8-15-11z" class="AG"></path><path d="M311 365h0l1 8-1-1c0-1-1-3-1-4l1-3z" class="AE"></path><path d="M302 335l1-1c0 2 1 3 1 4v1l-1 1c-1 0-1-1-2-2 0-2 0-2 1-3z" class="U"></path><path d="M302 335l1-1c0 2 1 3 1 4l-2-1v-2z" class="AP"></path><path d="M295 352c2 2 3 5 3 7l-1-1c-3 0-3-1-5-3l3-3z" class="AB"></path><path d="M304 339l2 7 2 6 2 9-1 2h-1l-1-1 1-1c0-3 0-4-2-7-1-1-3-2-3-4s-2-4-2-6h0c1 2 2 3 2 5 1 2 1 2 2 3l1-1c-1-1-1 0-1-1-1-2 0-3 0-4-1-1-2-1-2-3h1c0-1 0-2-1-3l1-1z" class="AE"></path><path d="M306 346l2 6h-2c0-2-1-3-1-5l1-1z" class="AP"></path><path d="M335 446l5 2 4 1c1 0 3 1 4 1v1h-1-1l-2 6c-1-1-3-2-4-4h0c-1-1-1-2-2-3h0c-1-1-2-2-3-2v-2z" class="n"></path><path d="M346 451h-3l-1-1c-1-1-2-1-3-2h1l4 1c1 0 3 1 4 1v1h-1-1z" class="W"></path><path d="M292 356v-1c2 2 2 3 5 3l1 1 2 3v2c3 7 6 16 8 24l-2-4c-2-5-4-11-6-16-1-2-1-4-3-6-1-1-2-1-4-1s-3 0-4-1v-1l2-2 1-1z" class="k"></path><path d="M291 357l1 2h4c1 1 3 2 4 3v2l-4-4c-2 0-4 0-7-1l2-2z" class="i"></path><path d="M292 356v-1c2 2 2 3 5 3l1 1 2 3c-1-1-3-2-4-3h-4l-1-2 1-1z" class="v"></path><path d="M349 441h0l1 1c-2 1-2 1-3 2l-1 3c1 1 3 1 4 1-2 0-4 0-6 1l-4-1-5-2c-1-1-5-1-7-2h9l5-2h4 2l1-1z" class="p"></path><path d="M237 237c3 0 6 3 8 4a30.44 30.44 0 0 1 8 8c0 1 1 1 1 3 1 0 1 0 2 1 1 0 2 1 3 2h0c0 1 0 1 1 2s1 1 1 2l1 1c1 0 1 0 1 1l8 12c1 2 2 3 3 4s1 2 2 3c0 4 3 5 4 7 2 1 1 2 2 3s1 1 1 3c1 1 2 1 2 3 1 3 4 5 5 8 2 2 2 4 4 6v-1h1v-3l6 19c0 3 1 6 2 9-1-1-2-5-2-7-1-1-1-3-2-4 0-1-1-2-1-3-1 0-1-1 0-2v-1l-1-1 1-1h-1v-1l-1 2c-1-1-2-1-2-2-1-1-1-2-2-4-1-1-2-1-2-3-1-3-5-6-6-9v-1c0-1 0-2-1-2l-2-1c0-2-1-3-1-4-1-3-3-4-5-6v-2c-2-1-1-3-3-5-1-2-3-4-4-6 0-2-2-4-3-5-1-2-2-3-3-4s-2-2-2-3c-1 0-1 0-1-1l-1-1v-1c-1-2-4-3-5-4 0 0 0-1-1-2h0c-1-3-4-6-6-8-2-1-3-1-4-2l-1-1-1 1-3-3z" class="u"></path><path d="M295 310c0 1 0 3 1 4l-1 1c0-1-1-1-1-2l1-3z" class="n"></path><path d="M280 287c2 1 1 2 2 3s1 1 1 3c1 1 2 1 2 3 1 3 4 5 5 8 2 2 2 4 4 6v-1h1v1l-1 3c-1-2-2-4-3-5-1-5-5-7-6-12 0-1-2-2-3-3 0-1 0-2-1-2-1-1 0-2-1-4z" class="J"></path><path d="M222 226l2-1c3 2 7 2 10 3 5 0 10 5 15 5h1 1 0l2 1h0l-1 1 1 1c4 1 7 4 10 6l6 6 5 7 3 6h1l4 11c1 0 1 1 2 1l1 4 7 17 4 12h-1v3h-1v1c-2-2-2-4-4-6-1-3-4-5-5-8 0-2-1-2-2-3 0-2 0-2-1-3s0-2-2-3c-1-2-4-3-4-7-1-1-1-2-2-3s-2-2-3-4l-8-12c0-1 0-1-1-1l-1-1c0-1 0-1-1-2s-1-1-1-2h0c-1-1-2-2-3-2-1-1-1-1-2-1 0-2-1-2-1-3a30.44 30.44 0 0 0-8-8c-2-1-5-4-8-4h0c-5-4-9-8-15-11z" class="t"></path><path d="M249 233h1 1 0l2 1h0l-1 1 1 1h-1c-1 0-2 0-4-1l1-2z" class="B"></path><path d="M253 236c4 1 7 4 10 6l-1 3h0c-1-1-2-1-2-2h-1c-1-1-2-2-3-2v-1c0-2-2-3-4-4h1z" class="Q"></path><path d="M263 242l6 6 5 7 3 6h1l4 11c-1-1-1-2-2-3 0-1 0-2-1-2 0-1-1-1-1-2-1-1-1-3-2-4-1 0-2 0-2-1-1 0-1-1-1-2 0 0-1-1-2-1s-1-1-2-1l1-1c0-2-3-3-4-4v-2l-1-1-3-3 1-3z" class="W"></path><path d="M779 138l26 2c8 0 17 0 24 1v1h3l-4 2-1 1-1 1 1 1h0v1l1 1c1 0 2 0 4 1s4 1 6 2c5 2 11 4 16 6 2-1 4 0 6 0l4 3c2 2 4 3 6 5l12 10c2 4 5 7 8 11 4 4 7 9 10 14l3 3c3 6 5 12 7 18l-1 1-1 1c-2-1-2-4-4-5h-1c0-1 0-1-1-2-1-2-1-3-2-4s-2-2-2-3h0c-1-2-6-7-8-7h0l-1-1-1 1-3-3c-1 0-3-1-4 0-1 0 0 0-1 1l-2-1c-3-4-20-5-26-6h-1c-1 0-2 1-3 1l-4 1-5 1v-1c-2-1-7 1-10 0l-10-2h-2l-2-2c-3-1-6-3-9-5v1c-3-2-7-3-10-4-2-1-4-1-5-2s-2-1-3-2c-1 0-1 0-1-1l-5-4c-3 7-7 13-11 18l-2 2c0 1-1 2-2 3-2 2-4 3-7 5-1 0-3 1-5 1-2 1-4 1-5 3l-1 1c-1 0-1 0-2-2 1 0 1-1 1-2l-2-2-3-1c-5-1-12-6-17-8l2-2-4-3-7-5-1 1c-5-4-8-9-11-14-2-3-5-7-6-11l1-2h1 0 0v-1h0 7 0c-2-2-3-4-3-6 2 0 3 1 4 3h1v-2l1-5c1 0 1-1 2-1 1-1 2-1 4-2 5-3 12-3 18-3 2 0 6 1 7 3 1 1 3 1 3 2 2 0 4 1 6 1l5 1 8-1h1 0l4-2c3-1 5-1 8-2 0-2 0-2 2-4z" class="Z"></path><path fill="#f0c8ca" d="M770 153c1-1 3-2 4-3 3-2 7-3 11-4 1 0 1 1 1 1-6 2-11 4-16 7v-1z"></path><path d="M790 144c3 0 5 0 7 1l1 1h0c-4 1-8 1-12 1 0 0 0-1-1-1h0l-1-1c1-1 4-1 5-1h1z" class="AJ"></path><path d="M789 144h1c-2 1-3 2-4 2h-1l-1-1c1-1 4-1 5-1z" class="AW"></path><path d="M900 213h1c1 0 2 1 2 1l2 3c0 1 1 2 2 4v-1l-1-1v-1c-1-2 0-1-1-2v-1-1c-1-1-2-3-3-5h0l1-1c3 5 4 10 6 15l-1 1c-2-1-2-4-4-5h-1c0-1 0-1-1-2-1-2-1-3-2-4z" class="d"></path><path d="M890 187c4 4 7 9 10 14l3 3c3 6 5 12 7 18l-1 1c-2-5-3-10-6-15l-14-20 1-1z" class="AF"></path><path d="M724 188h1l1 1h4v-1c-1 0-1-1-2-2l1-2c5 3 8 4 13 5-2 2-3 3-6 3-1 0-2 1-4 2l-4-3-4-3z" class="P"></path><path d="M822 168c4 1 6 2 8 5 1 3 2 5 1 8 0 1-2 3-3 4h-2-1c-3 1-5 1-8 0 0-1 0-2-1-2l1-1s1 1 2 1v-1l2 1h4l1-1c1-1 2-2 3-4 0-1-1-4-2-5v-1-1l-1-1c-2 0-3-1-4-2z" class="s"></path><path d="M829 142h3l-4 2-1 1-1 1 1 1h0v1l1 1c1 0 2 0 4 1l-1 1c-5-1-11-3-16-4-6-1-12-1-17-1h0c9 1 18-1 27-3l4-1z" class="J"></path><path d="M829 142h3l-4 2-1 1-1 1c-1 1-1 1-3 1h-1c1-2 2-3 3-4l4-1z" class="AC"></path><path d="M815 192c3 1 5 1 8 2 1 1 1 1 2 1h1c1 0 2 0 3 1h2c0-1 0-2 1-2h0v1h3v-2h1l1-1h2c1 0 1-1 2-2v-1c1 0 1-2 3-2v-1h1c1 0 1 0 2 1l-1 2c0-1 1-1 1-2h0 1c0 1-1 3-3 4l-1 1h-1c-1 1-2 1-3 1v1l1 1 2-1 1 1c-2 0-3 0-5 1-2-1-7 1-10 0l-10-2h-2l-2-2z" class="AK"></path><path d="M817 185c0-1-1-1-2-2-2-2-4-4-4-7 0-2 1-3 2-5 3-3 5-3 9-3 1 1 2 2 4 2l1 1c-3 0-5-1-8 0l-1 1-1-1-1 2c-1 1-1 4 0 5 1 2 2 3 3 4h0v1c-1 0-2-1-2-1l-1 1c1 0 1 1 1 2z" class="f"></path><path d="M761 157c1 1 2 0 3 0h1c-6 5-14 9-22 10 0-1 0-1-1-2-2-1-6 1-9-1h5 1c4-1 7-2 11-4 1 1 2 1 2 1 2 0 3 0 5-1 1-1 0 0 1-2l3-1z" class="AT"></path><path d="M781 172c1 0 1 1 1 2v1c-3 7-7 13-11 18h-4v-1l5-4c-1 0 0 0-1-1h0-2-1c-1 0-1 0-2 1l-2 1-1-1 6-3 7-5c2-2 5-5 5-8z" class="AY"></path><path d="M769 185l1 1c3-3 7-6 10-9-3 4-5 8-8 11-1 0 0 0-1-1h0-2-1c-1 0-1 0-2 1l-2 1-1-1 6-3z" class="AC"></path><path d="M702 157h1c1 0 2 1 4 0 3 0 6 1 9 1l9 3c1 0 3 1 4 1v1c3 1 6 1 10 1h-1-5c3 2 7 0 9 1 1 1 1 1 1 2-8 1-18-2-26-4-4-1-8-2-11-4h-1l-3-2z" class="AO"></path><path d="M702 157h1c1 0 2 1 4 0 3 0 6 1 9 1l9 3c1 0 3 1 4 1v1h0c-3 1-8 0-11-1-1 0-3-1-5-2h-1-1c-2-1-3-1-5-1h-1l-3-2z" class="a"></path><path d="M816 178c-1-1-1-4 0-5l1-2 1 1 1-1c3-1 5 0 8 0v1 1c1 1 2 4 2 5-1 2-2 3-3 4l-1 1h-4l-2-1h0c-1-1-2-2-3-4z" class="h"></path><path d="M816 178c-1-1-1-4 0-5l1-2 1 1 1-1c3-1 5 0 8 0v1 1c1 1 2 4 2 5-1 2-2 3-3 4l-1 1h-4l-2-1h0c2-1 4-1 5-1 2-1 2-2 3-4 0-1 0-2-1-2-1-2-2-3-4-3-1 0-4 0-5 1-1 2-1 3-1 5z" class="E"></path><path d="M832 150c2 1 4 1 6 2 5 2 11 4 16 6 2-1 4 0 6 0l4 3c2 2 4 3 6 5l12 10c2 4 5 7 8 11l-1 1c-11-13-23-23-39-30-6-3-12-5-19-7l1-1z" class="v"></path><path d="M862 163l2-2c2 2 4 3 6 5-3 0-4 0-5-2l-3-1z" class="AF"></path><path d="M854 158c2-1 4 0 6 0l4 3-2 2c-1-1-2-2-3-2-2-1-3-2-5-3z" class="AH"></path><path d="M777 142l15-1v1c-1 1-2 1-3 2-1 0-4 0-5 1l1 1h0c-4 1-8 2-11 4-1 1-3 2-4 3v1c-2 1-3 3-5 3h-1c-1 0-2 1-3 0l-3 1c0-2 1-2 2-3v-3c2-2 3-4 5-6h0l4-2c3-1 5-1 8-2z" class="z"></path><path d="M769 151c0 2 0 1 1 2v1c-2 1-3 3-5 3h-1c-1 0-2 1-3 0l2-2 2-1c1-1 3-2 4-3z" class="AJ"></path><path d="M763 155l2-1c0 1 0 1-1 3h0c-1 0-2 1-3 0l2-2zm6-4c4-3 10-5 15-6l1 1h0c-4 1-8 2-11 4-1 1-3 2-4 3-1-1-1 0-1-2z" class="Aa"></path><path d="M769 144c-1 2-2 3-3 4 0 1 0 0-1 1s-2 4-3 5l1 1-2 2-3 1c0-2 1-2 2-3v-3c2-2 3-4 5-6h0l4-2z" class="Ab"></path><defs><linearGradient id="AC" x1="843.404" y1="160.776" x2="846.301" y2="194.405" xlink:href="#B"><stop offset="0" stop-color="#020504"></stop><stop offset="1" stop-color="#3f3540"></stop></linearGradient></defs><path fill="url(#AC)" d="M848 187l1-1v-5h0c-1-4-4-9-7-11-3-3-6-6-9-10 3 1 4 2 6 4 1 0 2 0 3 1 2 0 4 3 6 4 3 3 4 7 6 10v1c1 4 2 8 0 11 0 1-1 2-2 3h-1c-1 0-2 1-3 1l-4 1-5 1v-1c2-1 3-1 5-1l-1-1-2 1-1-1v-1c1 0 2 0 3-1h1l1-1c2-1 3-3 3-4z"></path><path d="M844 195l-1-1-2 1-1-1v-1c1 0 2 0 3-1h1l-1 2c1-1 2-1 3-1h1v1l-3 1z" class="s"></path><path d="M779 138l26 2c8 0 17 0 24 1v1l-4 1c-9 2-18 4-27 3l-1-1c-2-1-4-1-7-1h-1c1-1 2-1 3-2v-1l-15 1c0-2 0-2 2-4z" class="Ab"></path><path d="M701 157h1l3 2-1 1c2 4 7 7 10 10l11 10c1 2 3 3 4 4l-1 2c1 1 1 2 2 2v1h-4l-1-1h-1l-7-5-1 1c-5-4-8-9-11-14-2-3-5-7-6-11l1-2h1 0z" class="AN"></path><path d="M700 157h1c2 5 4 9 6 14 3 4 7 8 10 12l-1 1c-5-4-8-9-11-14-2-3-5-7-6-11l1-2z" class="b"></path><path d="M781 172v-2-1c0-2 0-3 1-4h0c0-2 4-6 5-6l2-1h0c2 0 4 0 6 1 1 1 2 2 3 4 0 1 1 1 1 2 1 2 1 3 1 5v1c0 1-1 2-2 3s-1 1-2 1v1h-2 0c-1 0-3 0-4-1l-3-2c-3-1-3-1-4-3v3l1 1h0c1 3 5 6 8 7 4 3 11 1 14 6v1c-3-2-7-3-10-4-2-1-4-1-5-2s-2-1-3-2c-1 0-1 0-1-1l-5-4v-1c0-1 0-2-1-2z" class="AQ"></path><path d="M787 173c-1-1-2-3-2-4s0-2 1-3c1-2 3-3 5-3s3 0 5 2c2 1 2 2 3 3-1 3-1 4-4 6h0c-2 0-3 0-5 1l-3-2z" class="AX"></path><path d="M742 143c1 1 3 1 3 2 2 0 4 1 6 1l5 1 8-1h1c-2 2-3 4-5 6v3c-1 1-2 1-2 3-1 2 0 1-1 2-2 1-3 1-5 1 0 0-1 0-2-1-4 2-7 3-11 4-4 0-7 0-10-1v-1c-1 0-3-1-4-1h8v-1l-4-1v-2c3 2 5 2 8 2 2-1 4-2 6-4-1 1-2 2-3 1l1-2c0-1 1-3 2-4 0-1-1-4-2-5l1-2z" class="M"></path><path d="M742 143c1 1 3 1 3 2l4 3c-2 3-3 5-6 7-1 1-2 2-3 1l1-2c0-1 1-3 2-4 0-1-1-4-2-5l1-2z" class="AN"></path><path d="M756 147l8-1h1c-2 2-3 4-5 6v3c-1 1-2 1-2 3-1 2 0 1-1 2-2 1-3 1-5 1 0 0-1 0-2-1-4 2-7 3-11 4-4 0-7 0-10-1v-1c2-1 5-1 7-1 5 0 8-1 12-3l1-1c2-1 3-2 5-3l1-1c1-1 2-2 2-4 0-1-1-2-1-2z" class="p"></path><path d="M760 152v3c-1 1-2 1-2 3-1 2 0 1-1 2-2 1-3 1-5 1 0 0-1 0-2-1 4-2 7-5 10-8z" class="AJ"></path><path d="M763 188l1 1 2-1c1-1 1-1 2-1h1 2 0c1 1 0 1 1 1l-5 4v1h4l-2 2c0 1-1 2-2 3-2 2-4 3-7 5-1 0-3 1-5 1-2 1-4 1-5 3l-1 1c-1 0-1 0-2-2 1 0 1-1 1-2l-2-2-3-1c-5-1-12-6-17-8l2-2 4 3c2-1 3-2 4-2 3 0 4-1 6-3 5 2 11 2 16 1l1-1 4-1z" class="H"></path><path d="M758 190l1-1h1c1 1 2 1 3 1h1c-1 1-5 2-6 2h-1v-1h0l1-1z" class="x"></path><path d="M726 193l2-2 4 3c3 1 5 2 7 3h1c3 2 8 2 12 1 1 0 3 0 4-1h1c4-1 6-3 10-4v-1 1l-2 3c-6 4-12 5-19 4h-2c-1 0-1 0-1 1-5-1-12-6-17-8z" class="p"></path><path d="M767 193h4l-2 2c0 1-1 2-2 3-2 2-4 3-7 5-1 0-3 1-5 1-2 1-4 1-5 3l-1 1c-1 0-1 0-2-2 1 0 1-1 1-2l-2-2-3-1c0-1 0-1 1-1h2c7 1 13 0 19-4l2-3z" class="AJ"></path><path d="M756 202l-1 2c-2 1-4 1-5 3l-1 1c-1 0-1 0-2-2 1 0 1-1 1-2l-2-2c4 1 7 1 10 0z" class="M"></path><path d="M767 193h4l-2 2c0 1-1 2-2 3-2 2-4 3-7 5-1 0-3 1-5 1l1-2c4-2 7-3 10-5l-1-1 2-3z" class="J"></path><path d="M767 193h4l-2 2-3 2-1-1 2-3z" class="R"></path><path d="M717 143c5-3 12-3 18-3 2 0 6 1 7 3l-1 2c1 1 2 4 2 5-1 1-2 3-2 4l-1 2c1 1 2 0 3-1-2 2-4 3-6 4-3 0-5 0-8-2v2l4 1v1h-8l-9-3c-3 0-6-1-9-1-2 1-3 0-4 0h-1-1 0v-1h0 7 0c-2-2-3-4-3-6 2 0 3 1 4 3h1v-2l1-5c1 0 1-1 2-1 1-1 2-1 4-2z" class="b"></path><path d="M729 159c-1 0-3-1-4-3 0-1 0-1 1-2h1l2 3v2z" class="r"></path><path d="M710 151l2 4c1 0 2 0 4-1s6-4 7-7c0-1 1-2 2-3h0c0 1 0 2-1 3 0 2 0 3-1 5l-1 1c-1 1-3 2-4 3s-1 1-2 1h-2c-1-1-2-1-3-1h0-3c-2-2-3-4-3-6 2 0 3 1 4 3h1v-2z" class="J"></path><path d="M717 143h0l8-1 1 1s0 1-1 1h0c-1 1-2 2-2 3-1 3-5 6-7 7s-3 1-4 1l-2-4 1-5c1 0 1-1 2-1 1-1 2-1 4-2z" class="d"></path><path d="M727 148c0-1 1-1 1-2 2-2 4-2 6-1 2 0 2 0 4 1 1 2 2 3 2 5 0 1 0 2-1 3h2l-1 2c1 1 2 0 3-1-2 2-4 3-6 4-3 0-5 0-8-2l-2-3v-1c0-1-1-3 0-5z" class="m"></path><path d="M734 145c2 0 2 0 4 1 1 2 2 3 2 5 0 1 0 2-1 3h2l-1 2c1 1 2 0 3-1-2 2-4 3-6 4-3 0-5 0-8-2l-2-3v-1c0-1-1-3 0-5 0 3 1 4 3 6 1 1 4 1 6 0 1-1 3-2 3-4l-5-5z" class="D"></path><path d="M727 153c2 2 4 4 7 3h1v2l1 1h1c-3 0-5 0-8-2l-2-3v-1z" class="V"></path><path d="M739 154h2l-1 2c1 1 2 0 3-1-2 2-4 3-6 4h-1l-1-1v-2c2 0 3-1 4-2z" class="P"></path><path d="M664 204c1 2 4 6 4 8 3 5 4 10 6 15 4 10 6 20 7 31l3 28c0 4 0 8-1 12l-2 19-1-1-10 4c-3 1-6 4-8 7-1 1-2 0-3 0-2 4-6 8-8 13-1 0-1 1-2 1l-1 1c-2 5-3 10-5 14l-2 5c-1 1-2 2-2 4l-3 6c-1 1-1 2-2 3l-3 7h-1c-1 2-4 5-5 7 0 3-3 4-4 6v1l-8 7c0 1 0 0-1 1 0 1-1 1-1 2l-1 1-3 2c-1 1-3 2-5 3l-1 1h-1c-1 1-2 1-4 1l-1 1v-1h-1l-1 1c1 2 3 3 4 5v1h-1l-5-7c0-1-1-1-1-2h-1l-3-2h0c-1-1-1-2-2-3-1 0-2-3-4-3v1c-2 0-2 1-3 1l-1 2h0c-1-2-1-3 0-4v-1l-1-1-3 4h-1v-2l1 1c2-2 4-5 5-7 1-1 1-2 2-3v1h1v-1c1-2 1-2 1-4h-1v-2c1-1 1-1 1-2v-2c1-2 1-3 1-5l1-1v-1-9c-1-2-1-3-1-4l1 1c0 2 0 2 2 4 0 2 0 8-1 10v3c0 1 0 1-1 2v2c0 1 0 1-1 2v3c0 1-1 2-1 3-1 1-1 2-1 3l-3 5c0 1-1 1-1 2h0v2l4-5c2 1 3 2 4 3l1 1c0-1 0-1-1-2-1-2-1-2-1-4s-1-3-1-5l1-2c2-5 4-11 5-17 1-7 0-21-5-27l3-2 3-3-1-1c3 0 6-1 8-3 4-1 9-4 11-7s4-7 5-10h0v-3c0-2 0-4-1-6l-1-1c0-1-1-1-1-2-3-1-5-4-7-6h1c1 0 1 1 2 1h1c-1 0-1-1-1-1l-1-1h1c0-1-1-1-1-2-1-1-1-1-1-2h0l-1-1c0-2-1-3-1-6 1-1 1-2 1-3l1-1 1-1c1 0 3 0 4 1 9 1 20 1 28-3v-1l1-1c8-2 13-8 16-15h1c0-1 1-2 1-3v-1l1-1v-1c1-2 1-2 1-3v-1-1c0-2 1-3 1-4 1-2 1-4 1-5v-2c1-2 1-2 1-3v-2c1-2 1-2 1-3v-2c1-2 1-2 1-3 1-2 0-3 0-4 1-2 1-4 1-5 1-2 0-7 1-9h0v-1-1-9z" class="h"></path><path d="M642 336c0 2 1 3 1 4-1 2-2 4-2 6l-1-1c0-1 0-2 1-3 0-2 0-4 1-6z" class="m"></path><path d="M611 342v3h0l-6-3h3 3z" class="C"></path><path d="M584 406c2 1 4 2 6 2v1c1 0 1 0 1 1h-1l-4-1h0c-1-1-1-2-2-3z" class="K"></path><path d="M613 330c1 1 1 2 1 3-1 2 0 4 0 6-1 0-4-1-4-2 1-2 2-4 3-7z" class="C"></path><path d="M607 339h2c1 1 1 2 2 3h-3-3c-1-1-3-2-4-3h6z" class="F"></path><path d="M613 330c1-4 2-9 3-14 2 5-3 19 3 21l1 1h0c-1 0-1 0-2-1h-3v1 1h1 2c-1 1-3 1-4 0 0-2-1-4 0-6 0-1 0-2-1-3zm-1-8c1 2 0 4-1 6-1 3-2 6-4 7-1 1-3 2-4 2v1l4 1h-6c-1 0-2 0-3 1s-2 1-3 2l-6 1-1-1c3 0 6-1 8-3 4-1 9-4 11-7s4-7 5-10z" class="z"></path><path d="M591 375l1 1c-2 2-1 6-1 8s-1 5-1 7c0 1-1 1-1 2v2c0 1-1 3-2 4v-1h-1 0v-2-3l1-2v-2l1-3v-1c0-1 1-2 1-3 0-2 0-4 1-6l1-1z" class="D"></path><path d="M583 348l3-2c0 5 2 7 4 12v4h1c-1 1 0 1-1 1v1h1 0-2c1 1 2 1 3 2 1 0 1 0 1 1v4c1 1 1 2 1 3l-2 2-1-1c0-1 1-1 2-2 0-1-1-2-1-3v-2l-1-2c-1 1 1 5-1 6-1-2-1-4-1-5v10c-1 2-1 5-2 8-1 2-2 3-2 6-1 3-1 5 0 9l-1 1c-1-3-1-6-1-9 2-5 4-11 5-17 1-7 0-21-5-27z" class="K"></path><path d="M601 292c1-1 1-2 1-3l1-1 2 2v1l6 5c0 3 0 5 1 7v1c1 0 2 1 3 1 4 2 8 3 12 6 3 2 7 7 9 7l1 2 1 3 1 1c1 3 2 9 0 12 0 1-1 2-2 2-2 2-7 2-10 2h0c1-1 2-1 3-1l1 1 1-1h2v-1l1 1v-2h0v-1-2h-1v-1c0-4-1-6-3-10 0-1 0-1-1-2s-2-1-2-2l-1-1c-2 0-5-4-8-5-1-1-3-2-4-2h-1l-1-1-2 2c-1-3-3-5-5-7-1 0-1-1-1-1l-1-1h1c0-1-1-1-1-2-1-1-1-1-1-2h0l-1-1c0-2-1-3-1-6z" class="z"></path><g class="C"><path d="M605 291l6 5c0 3 0 5 1 7v1 3c-3-4-6-7-7-11-1-2-1-4 0-5z"></path><path d="M612 304c1 0 2 1 3 1 4 2 8 3 12 6 3 2 7 7 9 7l1 2 1 3 1 1c1 3 2 9 0 12 0 1-1 2-2 2 0-5 0-10-3-15-4-7-11-13-19-15-1 0-2 0-3-1h0 0v-3z"></path></g><path d="M612 304c1 0 2 1 3 1 4 2 8 3 12 6 3 2 7 7 9 7l1 2 1 3-1 1c-3-3-6-8-9-10-1-1-1 0-2-1l-2-2c-3-1-6-3-9-4h-3 0v-3z" class="G"></path><path d="M627 311c3 2 7 7 9 7l1 2h0c-3-1-8-6-10-9z" class="O"></path><defs><linearGradient id="AD" x1="620.663" y1="352.253" x2="643.417" y2="367.968" xlink:href="#B"><stop offset="0" stop-color="#dbd9d7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AD)" d="M669 299c0 1-1 2-1 3 4-1 8-5 12-6h2l1-3v5l-2 19-1-1-10 4c-3 1-6 4-8 7-1 1-2 0-3 0-2 4-6 8-8 13-1 0-1 1-2 1l-1 1c-2 5-3 10-5 14l-2 5c-1 1-2 2-2 4l-3 6c-1 1-1 2-2 3l-3 7h-1c-1 2-4 5-5 7 0 3-3 4-4 6v1l-8 7c0 1 0 0-1 1 0 1-1 1-1 2l-1 1-3 2c-1 1-3 2-5 3l-1 1h-1c-1 1-2 1-4 1l-1 1v-1h-1l-1 1c1 2 3 3 4 5v1h-1l-5-7c0-1-1-1-1-2h-1l-3-2 4 1h1l1-1c1 0 1 0 2-1l3-1h0 1l2-1 1-1c1 0 0 1 1 0 21-13 31-34 38-57 0-1 1-2 1-2 0-2 1-4 2-6 0-1-1-2-1-4-1-1 0-3 0-4-1-3-1-7-2-10 1-4-2-12 0-16 2-3 8-4 11-4h6c1 0 3 1 4 1l2 2v3c3-3 4-6 6-9z"></path><path d="M650 336c1-3 3-8 5-10h3c-2 3-5 8-8 10z" class="E"></path><path d="M658 326l1 1c-2 4-6 8-8 13-1 0-1 1-2 1l1-5c3-2 6-7 8-10z" class="O"></path><defs><linearGradient id="AE" x1="636.132" y1="312.719" x2="651.717" y2="323.664" xlink:href="#B"><stop offset="0" stop-color="#a09e9f"></stop><stop offset="1" stop-color="#dbd9d9"></stop></linearGradient></defs><path fill="url(#AE)" d="M651 302h6c1 0 3 1 4 1l-1 1c-1-1-1-1-2-1l-1 1-5 4c-1 2-2 3-3 4v1c-1 2-1 3-1 5s-1 5-2 7c0 4 0 12-3 15 0-1-1-2-1-4-1-1 0-3 0-4-1-3-1-7-2-10 1-4-2-12 0-16 2-3 8-4 11-4z"></path><path d="M651 302h6c-1 1 0 1-1 1-3 2-6 4-8 7l-3 9v-3-1-2c0-5 5-7 8-10h-1l1-1h-2z" class="C"></path><path d="M657 302c1 0 3 1 4 1l-1 1c-1-1-1-1-2-1l-1 1-5 4c-1 2-2 3-3 4v1c-1 2-1 3-1 5s-1 5-2 7c-1 1 0 1-1 1-1-2 0-5 0-7l3-9c2-3 5-5 8-7 1 0 0 0 1-1z" class="N"></path><defs><linearGradient id="AF" x1="670.533" y1="298.872" x2="674.409" y2="312.706" xlink:href="#B"><stop offset="0" stop-color="#b8b7b8"></stop><stop offset="1" stop-color="#e6e6e5"></stop></linearGradient></defs><path fill="url(#AF)" d="M669 299c0 1-1 2-1 3 4-1 8-5 12-6h2l1-3v5l-2 19-1-1s1-1 0-1c-3-2-9-1-12 0-1 0-2 1-4 1l-2-2c0-2 0-3 1-6 3-3 4-6 6-9z"></path><defs><linearGradient id="AG" x1="686.375" y1="239.61" x2="618.984" y2="304.762" xlink:href="#B"><stop offset="0" stop-color="#cac9ca"></stop><stop offset="1" stop-color="#f7f6f5"></stop></linearGradient></defs><path fill="url(#AG)" d="M664 204c1 2 4 6 4 8 3 5 4 10 6 15 4 10 6 20 7 31l3 28c0 4 0 8-1 12v-5l-1 3h-2c-4 1-8 5-12 6 0-1 1-2 1-3-2 3-3 6-6 9v-3l-2-2c-1 0-3-1-4-1h-6c-3 0-9 1-11 4-2 4 1 12 0 16v5-1c0-1 0-1-1-2l-1-1-1-3-1-2c-2 0-6-5-9-7-4-3-8-4-12-6-1 0-2-1-3-1v-1c-1-2-1-4-1-7l-6-5v-1l-2-2 1-1c1 0 3 0 4 1 9 1 20 1 28-3v-1l1-1c8-2 13-8 16-15h1c0-1 1-2 1-3v-1l1-1v-1c1-2 1-2 1-3v-1-1c0-2 1-3 1-4 1-2 1-4 1-5v-2c1-2 1-2 1-3v-2c1-2 1-2 1-3v-2c1-2 1-2 1-3 1-2 0-3 0-4 1-2 1-4 1-5 1-2 0-7 1-9h0v-1-1-9z"></path><path d="M611 296c3 2 7 3 10 4-3 2-6 0-8 3h-1c-1-2-1-4-1-7z" class="N"></path><path d="M641 295h0c-4 0-9 1-13 0-1 0-2 0-3-1h-2-2c-5 0-8-1-12-3h3c5 1 11 0 16 0h0c-3 1-6 1-9 1 2 1 4 1 6 1v-1h1c2 1 4 1 6 2h2l1 1h6z" class="K"></path><path d="M657 259v-1-1c0-2 1-3 1-4 1 1 1 2 2 3-2 9-7 19-15 24-3 2-6 3-9 5v-1l1-1c8-2 13-8 16-15h1c0-1 1-2 1-3v-1l1-1v-1c1-2 1-2 1-3z" class="N"></path><path d="M659 270c1-2 2-5 3-6v7 3c-1 0-1 1-1 2 0 2-1 4-1 7 0 2-2 4-4 5l-1 1h-1c-1 1-2 2-4 2l1 1 3-1v1h0 2 0c-4 2-10 3-15 3h-6l-1-1h-2c-2-1-4-1-6-2h4v-1c6 3 15 1 20-1h1c1-1 2-1 3-2s4-3 4-4c1-1 0-2 1-3l-1-1v-1-1l1-1c0-2 0-3-1-5l1-2z" class="D"></path><path d="M659 270c2 2 0 8 0 11l-1-1v-1-1l1-1c0-2 0-3-1-5l1-2z" class="X"></path><defs><linearGradient id="AH" x1="625.76" y1="302.711" x2="626.257" y2="317.788" xlink:href="#B"><stop offset="0" stop-color="#827f83"></stop><stop offset="1" stop-color="#a29fa4"></stop></linearGradient></defs><path fill="url(#AH)" d="M621 300c1 1 2 1 3 2 5 5 10 9 12 16-2 0-6-5-9-7-4-3-8-4-12-6-1 0-2-1-3-1v-1h1c2-3 5-1 8-3z"></path><path d="M613 303h2 1l2 1 1 1h-4c-1 0-2-1-3-1v-1h1z" class="i"></path><path d="M621 300c1 1 2 1 3 2l-9 1h-2c2-3 5-1 8-3z" class="D"></path><path d="M658 272c1 2 1 3 1 5l-1 1v1 1l1 1c-1 1 0 2-1 3 0 1-3 3-4 4s-2 1-3 2h-1c-5 2-14 4-20 1v1h-4-1v1c-2 0-4 0-6-1 3 0 6 0 9-1h0c8-2 15-3 22-8v-1c4-2 6-6 8-10z" class="C"></path><path d="M678 264l2-2v-1c0-1 1-2 1-3l3 28c0 4 0 8-1 12v-5l-1 3h-2c-4 1-8 5-12 6 0-1 1-2 1-3s1-3 1-4l4-12c2-6 4-12 4-19z" class="G"></path><path d="M678 264v-7c1-1 1-2 1-3-1-1-1-1-1-2-1-4-3-4-4-8 0-1 0-2-1-4-2 3-2 7-2 11-1 10-1 21-3 30-1 5-2 9-5 14v-5l1-13 2-20c0-11 3-23 1-33l-1-10v-4l2 3v-1c3 5 4 10 6 15 4 10 6 20 7 31 0 1-1 2-1 3v1l-2 2z" class="I"></path><path d="M726 441c4-4 7-9 11-13h2c-1 2 0 4 1 6 2 2 5 2 8 2 2 1 4 1 5 1l5 1c1 1 3 1 4 2l2 1h0c-1 1-2 1-3 1l5 1-3 1c-1 0-2 1-4 0l-1 1-5 1v1c-1 2 0 4-1 5h0v4h0v6c-3 26 5 49 18 70 6 9 13 18 20 28l10 14h1 0l2 2 1-1v-1c1 3 3 6 4 9l3 9v4 1l-1-1v1l-4-9-1-1c-2-3-4-6-7-9-2 0-4-1-5-2l1 3c-3-1-5-3-7-5-5-3-11-6-16-8v1c-2 0-3-1-4-1h-1l-1-1h-1-1-3c-1-1-2-1-3-1-1-1-4 0-5 0-3-1-6-1-8-1h-5c-1 1-1 1-3 1h0c1 1 1 2 2 3s1 2 2 4v2c1 2 1 9 0 10-6 0-12-3-17-3l2 2-1 1c-1 0-4-2-4-2-3-2-6-4-9-4-7-2-13-3-20-3 1 0 2 0 3-1h1-3l-1-1h0c-5 0-10 2-14 0h4l-1-2c-5 1-16 4-20 9v2h-1l-1 4v1h1l-3 2h-1l3-10c1-2 3-4 3-6 1-1 0-1 1-2l4-10c1-1 2-1 3-2l2-3c1-3 5-4 6-7v-3l-3 2-1-1c1-3 3-6 5-8 3-1 4-3 6-5l6-9v-8l1-3c2-5 5-11 6-17l4-8c1-5 5-8 4-14l4-15c0-5 1-9 2-13l2-1c2-1 3-4 3-6 0-1 0-2 1-2l5 1v1h1 1l1 1 1 1z" class="z"></path><path d="M742 500l1 1v6c0 3 1 8 2 12 1 2 1 3 1 5-2-2-3-4-4-6l-2-8v-1-1l1 2v2 2l1 1 1 2c0 2 0 3 1 4 0 0 1-1 1-2l-1-2v-2c0-1 0-1-1-2v-2-1c-1-1-1 0-1-1v-3-1-5z" class="K"></path><path d="M788 565l5 4c4 4 10 11 13 17v2l-1-1c-2-3-4-6-7-9s-6-7-9-10l-1-3z" class="D"></path><path d="M739 468h0c1-1 1-2 2-3h0l2-4c0-2 4-5 6-7h1c0-1 1-1 2-2v4h0l-1-1c-1 0-1 1-2 2-1 2-3 4-6 5 0 1 0 1-1 2-1 4-1 7-2 10v3c-1-1-1-2-1-3v1l-1 2v4 9c1 2 1 3 1 6h-1c-1-10-1-19 1-28h0z" class="N"></path><path d="M731 475c1-3 2-6 4-9 1-1 4-6 6-6 0 1 0 1-1 2l-1 6h0-1c-6 5-7 14-7 21v1h0c0-1-1-3-1-4h0v-5c1-2 1-4 1-6z" class="K"></path><path d="M731 475c1-3 2-6 4-9 1-1 4-6 6-6 0 1 0 1-1 2s-2 2-3 4h0l-4 6c0 1-1 3-1 5l-1 5-1 4v-5c1-2 1-4 1-6z" class="D"></path><path d="M727 485c0-3-1-6 0-9 2-5 8-13 12-17 1-1 2-1 4-2 3-2 5-4 8-6l1 1h0c-1 1-2 1-2 2h-1c-2 2-6 5-6 7l-2 4h0c-1 1-1 2-2 3h0l1-6c1-1 1-1 1-2-2 0-5 5-6 6-2 3-3 6-4 9-3 2-3 4-3 7-1 1-1 2-1 3h0z" class="X"></path><path d="M753 447c-1 2 0 4-1 5l-1-1c-3 2-5 4-8 6-2 1-3 1-4 2-4 4-10 12-12 17-1 3 0 6 0 9v2c-1-1-1-2-1-3l-3 23c-1 4-1 9-4 12l-1 2-1-2c3-9 6-16 5-25 1-5 2-9 3-13s0-8 3-12c1 0 5-7 6-8 2-2 4-4 6-5s4-2 5-3c3-2 5-4 8-6z" class="G"></path><defs><linearGradient id="AI" x1="702.527" y1="529.213" x2="710.018" y2="529.326" xlink:href="#B"><stop offset="0" stop-color="#6b6871"></stop><stop offset="1" stop-color="#838288"></stop></linearGradient></defs><path fill="url(#AI)" d="M709 520c7-6 11-18 13-26 1 9-2 16-5 25l1 2h0c0 2-1 2-2 3h-1l-1 2 1 1h0c2-1 3-1 5 0s4 1 6 1h1c-3 2-6 3-9 3l-17 2h-1-1c0-2 2-3 3-5l4-7 3-4h0v3z"></path><path d="M706 521l3-4h0v3l-5 7-3 6h-1-1c0-2 2-3 3-5l4-7z" class="AH"></path><defs><linearGradient id="AJ" x1="710.139" y1="531.479" x2="719.529" y2="526.075" xlink:href="#B"><stop offset="0" stop-color="#84838a"></stop><stop offset="1" stop-color="#acaaac"></stop></linearGradient></defs><path fill="url(#AJ)" d="M718 531v-1h-5-2-2c0-4 6-8 8-10v-1l1 2h0c0 2-1 2-2 3h-1l-1 2 1 1h0c2-1 3-1 5 0s4 1 6 1h1c-3 2-6 3-9 3z"></path><path d="M718 521l1-2c3-3 3-8 4-12l3-23c0 1 0 2 1 3v-2h0c2 10 5 21 5 32 0 3-1 6-3 8 0 1-1 2-2 3h-1c-2 0-4 0-6-1s-3-1-5 0h0l-1-1 1-2h1c1-1 2-1 2-3h0z" class="C"></path><defs><linearGradient id="AK" x1="676.372" y1="498.08" x2="770.179" y2="454.213" xlink:href="#B"><stop offset="0" stop-color="#d0cecd"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AK)" d="M726 441c4-4 7-9 11-13h2c-1 2 0 4 1 6 2 2 5 2 8 2 2 1 4 1 5 1l5 1c1 1 3 1 4 2l2 1h0c-1 1-2 1-3 1l5 1-3 1c-1 0-2 1-4 0l-1 1-5 1v1c-3 2-5 4-8 6-1 1-3 2-5 3s-4 3-6 5c-1 1-5 8-6 8-3 4-2 8-3 12s-2 8-3 13c-2 8-6 20-13 26v-3h0l-3 4-4 7c-1 2-3 3-3 5h-2l-6 2c1-2 3-4 4-5h0c-1 0-2 1-3 2h-1c-1 1-2 2-3 2-1 1-2 1-3 1 0 0-1-1-1-2l6-9v-8l1-3c2-5 5-11 6-17l4-8c1-5 5-8 4-14l4-15c0-5 1-9 2-13l2-1c2-1 3-4 3-6 0-1 0-2 1-2l5 1v1h1 1l1 1 1 1z"></path><path d="M718 482h2l-1 2-2 3 1-5z" class="L"></path><path d="M707 514c0 1 1 1 0 2v1c0 1-1 2-1 4l-4 7c-1 2-3 3-3 5h-2l-6 2c1-2 3-4 4-5 3-2 5-5 7-8l5-8z" class="f"></path><path d="M702 522c1 3-1 5-3 8 0 1-1 2-2 3l-6 2c1-2 3-4 4-5 3-2 5-5 7-8z" class="AM"></path><path d="M722 439h1 1l1 1 1 1c-1 3-2 5-4 8-1 4-1 8-3 12 0 2 1-1 0 2 0 1-1 3-2 5-1 5-2 11-3 17v-2c-4 5-4 12-8 17l5-13c2-4 2-7 2-11 1-3 1-6 2-10 1-7 1-14 5-21 2-2 2-4 2-6z" class="K"></path><path d="M724 439l1 1 1 1c-1 3-2 5-4 8 1-2 2-5 2-7v-3z" class="D"></path><path d="M690 524c1-1 3-4 4-5 5-12 12-24 17-36v4l-5 13-6 13c-1 3-2 6-3 8-3 3-5 7-6 11-1 1-2 2-3 2-1 1-2 1-3 1 0 0-1-1-1-2l6-9z" class="I"></path><path d="M688 534l1-2c1-3 2-5 4-7h0l3-6c0-1 1-2 2-3 1-2 1-2 2-3l-3 8c-3 3-5 7-6 11-1 1-2 2-3 2z" class="C"></path><defs><linearGradient id="AL" x1="691.289" y1="495.18" x2="709.711" y2="496.32" xlink:href="#B"><stop offset="0" stop-color="#848388"></stop><stop offset="1" stop-color="#b8b7b9"></stop></linearGradient></defs><path fill="url(#AL)" d="M701 488c7-5 6-10 10-17l1 1c1 3 0 8-1 11h0c-5 12-12 24-17 36-1 1-3 4-4 5v-8l1-3c2-5 5-11 6-17l4-8z"></path><defs><linearGradient id="AM" x1="709.106" y1="496.708" x2="720.405" y2="496.792" xlink:href="#B"><stop offset="0" stop-color="#605d65"></stop><stop offset="1" stop-color="#817f86"></stop></linearGradient></defs><path fill="url(#AM)" d="M722 477c2-3 3-6 5-8h1c-3 4-2 8-3 12s-2 8-3 13c-2 8-6 20-13 26v-3h0l-3 4c0-2 1-3 1-4v-1c1-1 0-1 0-2s1-2 2-3c0 0 1-2 1-3 3-7 5-14 7-21l2-3 1-2 2-5z"></path><path d="M707 514c0-1 1-2 2-3 0 2 0 3-1 5 0 0 0 1-1 1v-1c1-1 0-1 0-2z" class="AB"></path><path d="M722 477c1 2 0 5-1 7h0v-2l-1 3-1-1 1-2 2-5z" class="v"></path><path d="M717 437l5 1v1c0 2 0 4-2 6-4 7-4 14-5 21-1 4-1 7-2 10 0 4 0 7-2 11v-4h0c1-3 2-8 1-11l-1-1c-4 7-3 12-10 17 1-5 5-8 4-14l4-15c0-5 1-9 2-13l2-1c2-1 3-4 3-6 0-1 0-2 1-2z" class="C"></path><path d="M761 442l5 1-3 1c-1 0-2 1-4 0l-1 1-5 1v1c-3 2-5 4-8 6-1 1-3 2-5 3s-4 3-6 5c-1 1-5 8-6 8h-1c-2 2-3 5-5 8l-2 5h-2c1-3 1-6 2-9 1-6 3-11 6-17 2-5 8-9 12-11 7-4 15-3 23-3z" class="F"></path><path d="M733 457c4-2 7-5 10-7h2c-5 4-10 8-14 12h-1c1-1 1-2 2-3 0-1 0-1 1-2z" class="D"></path><path d="M726 456v4l-1 1-1 5c0 1-1 2-1 2l-2 4-1 2v-1c1-6 3-11 6-17z" class="L"></path><defs><linearGradient id="AN" x1="746.907" y1="439.376" x2="753.093" y2="448.624" xlink:href="#B"><stop offset="0" stop-color="#bbb9ba"></stop><stop offset="1" stop-color="#e8e6e4"></stop></linearGradient></defs><path fill="url(#AN)" d="M761 442l5 1-3 1c-1 0-2 1-4 0l-1 1-5 1-8 4h-2c-3 2-6 5-10 7 0-1 1-2 1-3h1c0-2 1-3 2-4l1-1c0-1 0-1 1-2 0-1 0-2-1-2 7-4 15-3 23-3z"></path><path d="M691 532h1c1-1 2-2 3-2h0c-1 1-3 3-4 5l6-2h2 1l-1 1c-1 1-7 1-7 2h2 1v-1c1 0 3 0 4 1s2 1 4 0h1c9 0 18-1 27 0 16 1 31 8 43 17 4 3 7 6 10 8 1 1 2 4 4 4l1 3c3 3 6 7 9 10-2 0-4-1-5-2l1 3c-3-1-5-3-7-5-5-3-11-6-16-8v1c-2 0-3-1-4-1h-1l-1-1h-1-1-3c-1-1-2-1-3-1-1-1-4 0-5 0-3-1-6-1-8-1h-5c-1 1-1 1-3 1h0c1 1 1 2 2 3s1 2 2 4v2c1 2 1 9 0 10-6 0-12-3-17-3l2 2-1 1c-1 0-4-2-4-2-3-2-6-4-9-4-7-2-13-3-20-3 1 0 2 0 3-1h1-3l-1-1h0c-5 0-10 2-14 0h4l-1-2c-5 1-16 4-20 9v2h-1l-1 4v1h1l-3 2h-1l3-10c1-2 3-4 3-6 1-1 0-1 1-2l4-10c1-1 2-1 3-2l2-3c1-3 5-4 6-7v-3l-3 2-1-1c1-3 3-6 5-8 3-1 4-3 6-5 0 1 1 2 1 2 1 0 2 0 3-1 1 0 2-1 3-2z" class="X"></path><path d="M767 557c5 2 10 3 14 6-3 0-11-3-14-4h2l-2-2z" class="v"></path><path d="M675 553l8-4c1 0 2-1 3-1-1 2-6 4-8 5v1h-1l-1 1-4 3v1 3l-1 1c0 1 1 1 0 2h2c1-1 1-1 2-1v1c-3 2-7 2-10 4l-3 1h0l4-10c1-1 2-1 3-2l2-3 4-2z" class="L"></path><path d="M666 560c1-1 2-1 3-2-1 3-4 8-4 11l-3 1h0l4-10z" class="AF"></path><path d="M679 563h1c15-5 31-7 47-8 14-1 27-1 40 2l2 2h-2l-2-1h-1-1c1 2 0 1 1 2-1 0-2-1-3-1-1-1 0-1-1-1h-1-1l-1-1 1 2-3 1c-1 0-3 0-4-1l-2 1c-1 0-1 0-2-1-5-1-11 1-15 0-1-1-2 0-3-1h-4c-3 1-6 1-9 1-6 1-13 3-19 5-3 1-6 3-9 4 0 0-1 1-2 1l-3 1h-3c-5 1-16 4-20 9v2h-1l-1 4v1h1l-3 2h-1l3-10c1-2 3-4 3-6 1-1 0-1 1-2h0l3-1c3-2 7-2 10-4l4-2z" class="AB"></path><path d="M689 562h0l-1 1v1l-1 1h0l-1-1h0c1-2 1-2 3-2z" class="AH"></path><path d="M679 563l1 1-9 3c0 1 0 0-1 1-1 0-2 1-3 2v1c2-1 3-1 4-2 2-1 4-1 6-2h1c-2 1-4 1-5 2-2 1-3 2-5 2-4 2-6 6-10 7 1-2 3-4 3-6 1-1 0-1 1-2h0l3-1c3-2 7-2 10-4l4-2z" class="f"></path><path d="M755 560l3-1-1-2 1 1h1 1c1 0 0 0 1 1 1 0 2 1 3 1-1-1 0 0-1-2h1 1l2 1c3 1 11 4 14 4l6 4 2 1c3 3 6 7 9 10-2 0-4-1-5-2l1 3c-3-1-5-3-7-5-5-3-11-6-16-8v1c-2 0-3-1-4-1h-1l-1-1h-1-1-3c-1-1-2-1-3-1-1-1-4 0-5 0-3-1-6-1-8-1h-5c-1 1-1 1-3 1l-4-1c-4-1-11 0-15 2-2 1-5 2-7 3h-4l-2 2h-6-15l3-1c1 0 2-1 2-1 3-1 6-3 9-4 6-2 13-4 19-5 3 0 6 0 9-1h4c1 1 2 0 3 1 4 1 10-1 15 0 1 1 1 1 2 1l2-1c1 1 3 1 4 1z" class="L"></path><path d="M755 560l3-1-1-2 1 1h1 1c1 0 0 0 1 1 1 0 2 1 3 1-1-1 0 0-1-2h1 1l2 1c3 1 11 4 14 4l6 4-1 1c-2 0-4-2-5-2-4-3-11-4-16-5l-1-1h-9z" class="E"></path><path d="M686 569c3 0 5 1 7-1 3-1 6-2 9-2 6-2 11-3 16-4 9-2 18-2 27-1 8 0 18 0 26 3l9 4c4 1 8 4 11 7 1 0 1 1 2 1l1 3c-3-1-5-3-7-5-5-3-11-6-16-8v1c-2 0-3-1-4-1h-1l-1-1h-1-1-3c-1-1-2-1-3-1-1-1-4 0-5 0-3-1-6-1-8-1h-5c-1 1-1 1-3 1l-4-1c-4-1-11 0-15 2-2 1-5 2-7 3h-4l-2 2h-6-15l3-1z" class="O"></path><path d="M732 563c11-2 23-1 33 1 2 1 4 1 6 2v1c-2 0-3-1-4-1h-1l-1-1h-1-1-3c-1-1-2-1-3-1-1-1-4 0-5 0-3-1-6-1-8-1h-5c-1 1-1 1-3 1l-4-1z" class="D"></path><path d="M698 570h6l2-2h4c2-1 5-2 7-3 4-2 11-3 15-2l4 1h0c1 1 1 2 2 3s1 2 2 4v2c1 2 1 9 0 10-6 0-12-3-17-3l2 2-1 1c-1 0-4-2-4-2-3-2-6-4-9-4-7-2-13-3-20-3 1 0 2 0 3-1h1-3l-1-1h0c-5 0-10 2-14 0h4l-1-2h3 15z" class="X"></path><path d="M683 570h15v1h5v1h-6c-5-1-10-1-15-1l-1 1-1-2h3z" class="K"></path><path d="M692 573c11 0 21 3 31 7l2 2-1 1c-1 0-4-2-4-2-3-2-6-4-9-4-7-2-13-3-20-3 1 0 2 0 3-1h1-3z" class="m"></path><defs><linearGradient id="AO" x1="693.341" y1="572.463" x2="753.84" y2="527.751" xlink:href="#B"><stop offset="0" stop-color="#74727a"></stop><stop offset="1" stop-color="#adacad"></stop></linearGradient></defs><path fill="url(#AO)" d="M691 532h1c1-1 2-2 3-2h0c-1 1-3 3-4 5l6-2h2 1l-1 1c-1 1-7 1-7 2h2 1v-1c1 0 3 0 4 1s2 1 4 0h1c9 0 18-1 27 0 16 1 31 8 43 17-2 1-5-1-7-2-8-2-16-6-23-8h-2c-5-2-11-2-16-3-14 0-27 3-40 8-1 0-2 1-3 1l-8 4-4 2c1-3 5-4 6-7v-3l-3 2-1-1c1-3 3-6 5-8 3-1 4-3 6-5 0 1 1 2 1 2 1 0 2 0 3-1 1 0 2-1 3-2z"></path><defs><linearGradient id="AP" x1="696.601" y1="547.645" x2="695.868" y2="541.079" xlink:href="#B"><stop offset="0" stop-color="#848087"></stop><stop offset="1" stop-color="#9f9fa4"></stop></linearGradient></defs><path fill="url(#AP)" d="M675 553h0l3-3c7-5 17-8 26-10 3 0 7-1 10-1 4 1 8-1 12 1-14 0-27 3-40 8-1 0-2 1-3 1l-8 4z"></path><defs><linearGradient id="AQ" x1="674.555" y1="545.099" x2="695.778" y2="533.38" xlink:href="#B"><stop offset="0" stop-color="#9e9ca0"></stop><stop offset="1" stop-color="#d7d6d6"></stop></linearGradient></defs><path fill="url(#AQ)" d="M691 532h1c1-1 2-2 3-2h0c-1 1-3 3-4 5l6-2h2 1l-1 1c-1 1-7 1-7 2h2 1v-1c1 0 3 0 4 1s2 1 4 0h1l-9 3c-1 0-3 1-3 0l-6 2c-3 2-7 2-9 4l-3 2-1-1c1-3 3-6 5-8 3-1 4-3 6-5 0 1 1 2 1 2 1 0 2 0 3-1 1 0 2-1 3-2z"></path><path d="M686 541v-1h0c2-1 4-1 6-1l-6 2z" class="X"></path><path d="M715 709c6-1 13-1 20 0h1c6 2 11 5 16 8 14 11 24 24 31 40 1 4 2 7 4 10l1 5-3-3-3-6-3-5v-1l-5-5c-1-3-4-5-6-8-2 1-5-2-7-3 0 0-1 1-2 1-5-1-11-3-16-4-4-1-8-2-12-2h-5-7c-4 0-10 2-14 4l-4 2c-3 2-5 3-8 6l-6 6c-4 3-6 9-9 14l-4 10-1-1v1h-1c-1 4-3 9-2 12v2c-1 2-1 5 0 8 0 5 0 9 1 14 1 3 1 6 4 9l1 1v1h-1l-1-1c-10-5-19-7-30-6-14 1-26 6-35 18-4 4-6 8-8 13l-2 11-1 4c0 4 0 6 1 10v3l-1 4h0-1c-7-7-12-15-19-21-3-3-6-5-9-7l-1-1c-1-1-3-1-5-1v1-1l1-1c-2 0-3 0-5 1h0-1l-1 1h-1v-1c-2 0-2 2-3 3h-1c1-4 3-9 5-13l1-4 2-4 1-3v-1l1-3 2-3 5-13 1 2h0l1 3v-2-2h1v-1l3-6 2-2 1-1h0c1-3 0-6 1-9 0 1 1 2 2 3h0c2 1 3 2 4 3l-1-3h-1l-1-1c-1-4 4-6 5-9v-1l1 1h1l2 1 1-1c-1 0-1 0-2-1l-4-5h-1v-1l-1-3-1-4 2-5 5-13 1 1 1 1h1l2 3c1 0 1 0 2-1-1-1-1-2 0-3 0-2 0-3 2-4h1l1-2 1 3v-2l2-3c1 2 2 4 3 5h0l-1 1 2 2v1c2 3 0-1 2 2v1c0 1 2 2 2 3 1 1 3 3 5 4 1 0 1 0 2 1l1 1 1 1c4 3 10 5 14 6 1-1 1-1 2-1 3 0 7 0 10-1l3-1c2-1 2-1 3-2l3-5c2-3 4-6 5-10 1-5 3-9 3-14v-2c0-2-2-4-2-6h0v-1l2 1c7-3 14-4 20-9 2-2 14-8 17-10 4-2 9-3 14-4z" class="m"></path><path d="M578 801c1-3 0-6 1-9 0 1 1 2 2 3h0l4 5c-3 0-3-2-4-3v3l-1 2-2-1z" class="I"></path><path d="M684 723c0 2-3 4-5 4-2 1-4 2-5 3-3 2-7 0-9 3 1 2 1 3 2 5 2-2 3-3 5-4l2-1v1l-6 6c-2-3-4-6-4-8 7-3 14-4 20-9z" class="O"></path><path d="M644 818v-1c3 0 9-1 12 0h1s1 0 2 1h2c1 0 3 1 5 2h1c1 0 3 1 4 1-1-1-1-2-1-3-1-1-1-2-1-3-1-1-1-1-1-2v-1c-1-3-1-7-2-10h0l1-1c1 5 1 10 3 15h1l-1-1 1-1c1 3 1 6 4 9l1 1v1h-1l-1-1c-10-5-19-7-30-6z" class="D"></path><path d="M750 733c2 0 3 0 5 1 5 3 10 6 14 10h1 0c1 0 2 0 3 2h0c3 4 6 7 8 12 1 1 1 3 1 5l-3-5v-1l-5-5c-1-3-4-5-6-8-3-2-6-5-10-7-3-1-5-3-8-4z" class="F"></path><path d="M715 709c6-1 13-1 20 0h1c6 2 11 5 16 8v1h0l-1-1c-1 0-2-1-4-2s-3-2-5-1h1c2 1 3 1 5 3h1l4 4h0c-8-6-17-9-27-10-2 0-5 1-7 0h2v-1h4s1 0 2 1h5 2-2c-1-1-2-2-4-2h-7c-2 1-4 1-6 0z" class="O"></path><path d="M752 717c14 11 24 24 31 40 1 4 2 7 4 10l1 5-3-3-3-6c0-2 0-4-1-5-2-5-5-8-8-12h0c2 0 4 3 5 5l2 2c0-1-2-4-3-5l-3-5c0-1 0-1-1-2v-1c-1-2-3-3-4-4 0-1-1-2-1-3-1-1-3-2-4-4s-5-5-7-7c-1 0-1-1-2-2-1 0-2-1-2-2h-1v-1z" class="G"></path><path d="M663 768c1-2 2-4 2-6v-1-3c0-1 0 0 1-1v2c1 2-1 5-1 7-3 6-5 12-8 18-1 2-4 6-4 8-1 2-1 3-2 4-2 2-3 4-5 6l-1-1c1-1 1-2 2-2v-1c-9 8-19 13-31 14-3 1-6 1-9 0h-2c-1 0-1 0-1-1h-2c-2 0-3-1-4-2h0l3 1c11 4 22 1 32-4 8-3 14-8 18-15 1-1 2-3 3-4 4-6 7-13 9-19z" class="D"></path><path d="M663 760v4c-1 1 0 3 0 4-2 6-5 13-9 19-1 1-2 3-3 4-4 7-10 12-18 15-10 5-21 8-32 4l-3-1c-5-3-9-5-13-9l-4-5c2 1 3 2 4 3h1c1 0 1 0 2 1 11 7 23 8 36 6 4-1 7-3 10-5 4-3 8-8 12-11 6-9 12-18 17-29z" class="F"></path><path d="M646 789v1h1l3-3c-1 2-4 4-5 6-2 2-4 5-7 6-1 1-2 1-4 1 4-3 8-8 12-11z" class="O"></path><path d="M633 806c2-3 5-4 7-6 2-3 5-5 7-7 2-1 3-2 4-4l1 1h-1v1c-4 7-10 12-18 15z" class="G"></path><defs><linearGradient id="AR" x1="707.153" y1="731.071" x2="717.65" y2="761.032" xlink:href="#B"><stop offset="0" stop-color="#72656a"></stop><stop offset="1" stop-color="#938c8f"></stop></linearGradient></defs><path fill="url(#AR)" d="M672 778c1-4 3-9 4-13 5-13 11-25 25-32 15-7 33-6 49 0 3 1 5 3 8 4 4 2 7 5 10 7-2 1-5-2-7-3 0 0-1 1-2 1-5-1-11-3-16-4-4-1-8-2-12-2h-5-7c-4 0-10 2-14 4l-4 2c-3 2-5 3-8 6l-6 6c-4 3-6 9-9 14l-4 10-1-1v1h-1z"></path><path d="M689 749c1-1 2-1 4-1l-6 6c-4 3-6 9-9 14v-4h1c0-2-1-1 0-2l10-13z" class="i"></path><path d="M689 749c2-3 7-7 10-9 1 0 1 0 2-1 1 0 1-1 2-1 3-1 7-3 9-3 3 0 5-1 7-1h5c1 0 2-1 3-1 1 1 2 1 3 1h5v1c1 0 2 1 3 1h1 1c1 0 2 1 3 2-4-1-8-2-12-2h-5-7c-4 0-10 2-14 4l-4 2c-3 2-5 3-8 6-2 0-3 0-4 1z" class="E"></path><defs><linearGradient id="AS" x1="568.968" y1="845.425" x2="589.568" y2="842.433" xlink:href="#B"><stop offset="0" stop-color="#b5b4b5"></stop><stop offset="1" stop-color="#eae9e8"></stop></linearGradient></defs><path fill="url(#AS)" d="M569 810l1 2h0l1 3 3 9c0 1 1 2 1 4 0 1 0 2 1 3 2 2 3 5 4 8h0c0 1 1 2 2 3s1 2 2 3l1 1h0c2 3 3 9 4 12v2l1 1v2c0 2 1 3 1 4h1v-2h0c1 1 0 0 1 2 0 1 0 2 1 3v1h0-1l5 10h0-1c-7-7-12-15-19-21-3-3-6-5-9-7l-1-1c-1-1-3-1-5-1v1-1l1-1c-2 0-3 0-5 1h0-1l-1 1h-1v-1c-2 0-2 2-3 3h-1c1-4 3-9 5-13l1-4 2-4 1-3v-1l1-3 2-3 5-13z"></path><path d="M562 826l2-3 1 1 2 6c-2-1-3-3-5-4z" class="AF"></path><path d="M562 826c2 1 3 3 5 4 0 1 1 1 1 2h-1l-6-3 1-3z" class="i"></path><path d="M569 810l1 2h0c-1 2-2 3-1 5l1 5h0 0l-1-3h0c-1-1-1-2-1-2h-1l-1 3v1h1-1c0 1-1 2-1 3l-1-1 5-13z" class="F"></path><path d="M572 827l1-2 2 3c0 1 0 2 1 3 2 2 3 5 4 8-2-2-2-4-3-5l-1-1-2-4v1l3 8c2 3 4 7 6 11 1 1 0 0 0 1-4-6-8-15-11-23z" class="z"></path><path d="M570 822l-1-5c-1-2 0-3 1-5l1 3 3 9c0 1 1 2 1 4l-2-3-1 2c0-2-1-4-2-5z" class="N"></path><path d="M561 829l6 3h1l1 3 1 4c-1 1 0 1-1 1l-1-1-1-2c-2-3-5-4-7-4l1-3v-1z" class="L"></path><path d="M561 829l6 3h1l1 3-1-1c-3 0-4-3-7-4v-1z" class="k"></path><path d="M560 833c2 0 5 1 7 4l1 2 1 1c1 0 0 0 1-1l1 4v2l-1 1v2h0c-1-1-3-2-4-3-3-2-6-3-9-4l1-4 2-4z" class="I"></path><path d="M560 833c2 0 5 1 7 4l1 2 1 1c1 0 0 0 1-1l1 4h-2l-2-2c-1-2-2-3-3-4h0l-2-3h-1c-1 1-1 2-2 3h-1l2-4z" class="G"></path><path d="M557 841c3 1 6 2 9 4 1 1 3 2 4 3h0l3 1a53.56 53.56 0 0 1 15 15l3 4c1 1 2 2 2 3l5 10h0-1c-7-7-12-15-19-21-3-3-6-5-9-7l-1-1c-1-1-3-1-5-1v1-1l1-1c-2 0-3 0-5 1h0-1l-1 1h-1v-1c-2 0-2 2-3 3h-1c1-4 3-9 5-13z" class="I"></path><path d="M564 850c9 1 17 8 23 14h1l3 4v2c-7-6-13-15-22-17l-1-1c-1-1-3-1-5-1v1-1l1-1z" class="O"></path><path d="M560 848c4 0 6 0 10 2h0l1-1 1 1 1-1a53.56 53.56 0 0 1 15 15h-1c-6-6-14-13-23-14-2 0-3 0-5 1h0-1l-1 1c0-2 1-2 2-3l1-1z" class="AI"></path><defs><linearGradient id="AT" x1="558.466" y1="841.671" x2="560.246" y2="849.597" xlink:href="#B"><stop offset="0" stop-color="#787076"></stop><stop offset="1" stop-color="#8f8d8f"></stop></linearGradient></defs><path fill="url(#AT)" d="M557 841c3 1 6 2 9 4 1 1 3 2 4 3h0l3 1-1 1-1-1-1 1h0c-4-2-6-2-10-2l-1 1c-1 1-2 1-2 3h-1v-1c-2 0-2 2-3 3h-1c1-4 3-9 5-13z"></path><path d="M566 845c1 1 3 2 4 3-2 0-3-1-4-1v-2z" class="i"></path><path d="M559 849c-1-1-2-1-2-1h0c0-1 1-1 2-1l1-1c1 0 2 0 3 1-1 0-2 0-3 1l-1 1z" class="k"></path><defs><linearGradient id="AU" x1="599.492" y1="752.978" x2="653.405" y2="778.526" xlink:href="#B"><stop offset="0" stop-color="#cdcccd"></stop><stop offset="1" stop-color="#f1f0ef"></stop></linearGradient></defs><path fill="url(#AU)" d="M662 732c1 1 1 1 2 3s2 3 2 5l1 1-1 1c0 1 1 3 0 4h0c0 2 0 4-1 5 0 3-1 6-2 9-5 11-11 20-17 29-4 3-8 8-12 11-3 2-6 4-10 5-13 2-25 1-36-6-1-1-1-1-2-1h-1l-1-3h-1l-1-1c-1-4 4-6 5-9v-1l1 1h1l2 1 1-1c-1 0-1 0-2-1l-4-5h-1v-1l-1-3-1-4 2-5 5-13 1 1 1 1h1l2 3c1 0 1 0 2-1-1-1-1-2 0-3 0-2 0-3 2-4h1l1-2 1 3v-2l2-3c1 2 2 4 3 5h0l-1 1 2 2v1c2 3 0-1 2 2v1c0 1 2 2 2 3 1 1 3 3 5 4 1 0 1 0 2 1l1 1 1 1c4 3 10 5 14 6 1-1 1-1 2-1 3 0 7 0 10-1l3-1c2-1 2-1 3-2l3-5c2-3 4-6 5-10 1-5 3-9 3-14v-2c0-2-2-4-2-6h0z"></path><path d="M635 774c1-1 1-1 2-1 3 0 7 0 10-1l3-1c-4 3-8 3-14 3h-1zm5 17v1l-5 2c-3 2-9 1-13 1h0l-1-1h7c3 0 5 0 8-1l3-1 1-1z" class="C"></path><path d="M651 774h0l1 1c-2 2-3 3-5 4-4 2-8 0-12-1l-1-1h1c1 0 2 1 3 1 5 1 9-1 13-4z" class="K"></path><path d="M606 760c2 1 3 2 5 4 3 5 10 9 15 12h-3c-6-4-14-9-17-16z" class="AI"></path><defs><linearGradient id="AV" x1="631.866" y1="776.441" x2="630.71" y2="786.106" xlink:href="#B"><stop offset="0" stop-color="#908a8c"></stop><stop offset="1" stop-color="#aca9ab"></stop></linearGradient></defs><path fill="url(#AV)" d="M623 776h3l14 7 3 1h1l-1 1c-2 0-4 1-6 2-1 1-2 2-4 2h-1v-2h0v-1c-1-2-3-5-5-7-1-1-3-2-4-3z"></path><path d="M640 783l3 1h1l-1 1c-2 0-4 1-6 2 0-1 0 0-1-1h0v-1c1 0 3-1 4-2h0z" class="F"></path><path d="M601 748l1 3c1 3 2 6 4 9 3 7 11 12 17 16 1 1 3 2 4 3 2 2 4 5 5 7v1h0l-1-1h0c-4 0-5-1-8-3-4-2-7-5-10-7l-2-1-2-2c-1 0-2-1-3-2 0-2-2-3-4-4 0-1-1-2-2-2h-1c-2-2-3-4-4-7 1 0 1 0 2-1-1-1-1-2 0-3 0-2 0-3 2-4h1l1-2z" class="G"></path><path d="M599 755c6 6 10 12 15 17l1 1c2 1 3 2 4 3 3 2 7 4 9 7-11-5-18-12-26-21-2-2-2-4-3-7z" class="E"></path><path d="M601 748l1 3c1 3 2 6 4 9 3 7 11 12 17 16 1 1 3 2 4 3 2 2 4 5 5 7v1h0l-1-1-3-3c-2-3-6-5-9-7-1-1-2-2-4-3l-1-1c-5-5-9-11-15-17l1-5 1-2z" class="k"></path><path d="M590 753l1 1 1 1h1l2 3c1 3 2 5 4 7h1c1 0 2 1 2 2 2 1 4 2 4 4 1 1 2 2 3 2l2 2 2 1c3 2 6 5 10 7 3 2 4 3 8 3h0l1 1v2h1c2 0 3-1 4-2 2-1 4-2 6-2l1-1h-1 0c2-1 3-1 4-2h1c-1 2-3 1-2 4-2 2-4 4-6 5l-1 1-3 1c-3 1-5 1-8 1h-7l1 1-8-1c-7-1-12-4-19-5-2 0-4-2-6-4l2 1 1-1c-1 0-1 0-2-1l-4-5h-1v-1l-1-3-1-4 2-5 5-13z" class="K"></path><path d="M620 792c2 0 4 0 6 1 1 0 2 1 2 1h-7l1 1-8-1 1-2c1 0 3 1 5 0z" class="X"></path><path d="M612 780c2 0 3 2 5 3 3 2 7 4 9 6 1 1 1 1 1 2h1l-1 1c-1-1-2-1-3-2-2-1-2-2-4-3-1 0-1-1-1-1l-1-1c-1 0-6-4-6-5z" class="N"></path><path d="M592 755h1l2 3c1 3 2 5 4 7h1c1 0 2 1 2 2 2 1 4 2 4 4 1 1 2 2 3 2l2 2 2 1c3 2 6 5 10 7 3 2 4 3 8 3h0l1 1v2h1c2 0 3-1 4-2 2-1 4-2 6-2-3 3-7 6-11 6h-1-1c0-1-2-2-2-2h-1-1c-2-2-6-4-9-6-2-1-3-3-5-3-2-1-3-3-5-3-2-1-4-5-6-8l-1 1c-3-4-6-10-8-15z" class="C"></path><path d="M602 767c2 1 4 2 4 4 1 1 2 2 3 2l2 2 2 1c3 2 6 5 10 7 3 2 4 3 8 3h0l1 1v2c-1-1-3-1-5-2h0l-5-3h-1l-3-2c0-1-1-1-2-2h0l-1-1-4-3c-4-2-6-5-9-8v-1z" class="I"></path><defs><linearGradient id="AW" x1="589.182" y1="754.208" x2="593.697" y2="771.975" xlink:href="#B"><stop offset="0" stop-color="#6e6b73"></stop><stop offset="1" stop-color="#98969b"></stop></linearGradient></defs><path fill="url(#AW)" d="M590 753l1 1 1 1c2 5 5 11 8 15 1 2 3 4 4 6h0c0 1 1 1 0 2-3-1-5-1-8-3-2-2-5-2-8-5l-1-1-1-2-1-1 5-13z"></path><path d="M588 770c3 0 4 1 7 2 1 0 2 0 4 1 1 1 3 2 5 3h0c0 1 1 1 0 2-3-1-5-1-8-3-2-2-5-2-8-5z" class="O"></path><defs><linearGradient id="AX" x1="585.518" y1="770.424" x2="615.325" y2="797.204" xlink:href="#B"><stop offset="0" stop-color="#adabac"></stop><stop offset="1" stop-color="#dbdad9"></stop></linearGradient></defs><path fill="url(#AX)" d="M585 766l1 1 1 2 1 1c3 3 6 3 8 5l-1 2c5 3 10 5 15 9 1 1 6 3 7 5h1c1 0 1 1 2 1-2 1-4 0-5 0l-1 2c-7-1-12-4-19-5-2 0-4-2-6-4l2 1 1-1c-1 0-1 0-2-1l-4-5h-1v-1l-1-3-1-4 2-5z"></path><path d="M592 785l1 1v1c-1 0-1 0-2-1l1-1z" class="I"></path><path d="M585 766l1 1c0 1 0 2-1 3v1c-1 2-1 3-1 4l-1-4 2-5z" class="F"></path><path d="M587 769l1 1c3 3 6 3 8 5l-1 2c-3-2-6-4-8-7v-1z" class="D"></path><path d="M182 141c-1-3-3-5-3-8h1c9 4 18 8 28 10h2 2 1l6 1c0 1 0 1 1 2 4-1 7-1 11-1 3 0 6 1 9 1l6 1-5 1h1c1 0 2 1 3 1s1 0 2 1c2 1 4 2 7 3 5 4 8 6 11 12 1 6 0 9-4 14l1 1 2-3c1-1 1 0 2-1v1c1 5 3 9 7 13 2 6 3 11 3 17v4h1v1c0 3 0 4-1 6-1 3 0 6-3 9-2 0-1 1-3 1h-2 0c-1 1-2 2-2 3l-5 2h-5c-1 0-2 0-3 1l-2-1h0-1-1c-5 0-10-5-15-5-3-1-7-1-10-3s-4-3-7-3h-1c-1-1-2-1-4-1h0-1-4 0c-9-1-19-1-27 2-3 1-5 1-7 2-4 2-8 4-11 7-9 7-16 16-18 27-1 4-1 10 0 14 0 1 2 6 2 7 4 7 8 13 15 17 1 2 2 3 1 5h-5l-4-2-7-4c-7-4-12-10-17-17v1l-3-4c0-3-3-7-4-10-2-5-2-11-3-17 0-6 0-11 2-16l1 2 1-5 1-4 1-3 2-5v-1l-1 2v-1l-1-1c1-2 2-5 3-7s3-3 4-5l4-6 3-4 4-6c-1 0-1 0-2-1l1-2h-1l-3 2c0-1 0-2-1-3l-1 1h-1v-1c1-2 1-3 2-4 0-1 0-2 1-2h3l2 1v-1c1 0 2-1 3-1 1-1 2-1 3-2 2-1 4-3 6-3 2-1 3-2 5-2l2-1c3-2 6-5 9-6 1 0 2-1 3-1 1-2 2-3 3-3l3-3v-1c-1-1 0-1 0-2 1-2 2-3 2-5 1-1 0-3 0-5v-3z" class="Z"></path><path d="M241 171h2c1 1 1 1 1 3l-2 1v-1l-1-3z" class="l"></path><path d="M191 163h1l1 1-6 6c0-2 2-4 4-5l-1-1 1-1zm-50 48l-5 4-1-1c2-1 3-3 5-4l1 1z" class="d"></path><path d="M160 171l2-1c1 0 2 0 3 1l-4 3c-1 0-1-1-2-1l1-2z" class="AT"></path><path d="M159 173c1 0 1 1 2 1l-4 4-2-1c0-1 3-3 4-4z" class="F"></path><path d="M139 195h1c1-1 1-1 2-1l-5 7-1-2 3-4z" class="I"></path><path d="M136 199l1 2-4 7c0-1-1-2-1-3l4-6z" class="G"></path><path d="M181 177l1-1v1h0c-2 4-1 8-2 12v2l-1-1v-1-6c1-1 1-2 1-3 0 0 1-1 1-2v-1z" class="f"></path><path d="M206 191h14l-2 1c-2 1-4 1-7 1h0c-2 0-3-1-5-2zm35-11v-7c-1 0-1 0 0-1-1-1-1-1-1-2 1-1 1-1 1-2-1-2-1-3 1-5 1-2 4-4 7-5l-1 1c-2 1-3 2-4 3-2 2-2 2-2 4h1v2l-1 1-1 2 1 3c0 2 0 5-1 6z" class="d"></path><path d="M196 187l1-1 1 1h1c-2-4-3-6-2-11h0c0 5 0 8 4 12 1 1 2 2 4 2v1h1c2 1 3 2 5 2-1 0-2 1-3 1s-2-1-3-2c-3 0-5-3-8-5h-1z" class="AR"></path><path d="M146 185c2 0 2 1 2 2-1 2-4 6-6 7-1 0-1 0-2 1h-1l4-6 3-4z" class="F"></path><path d="M155 177l2 1-9 9c0-1 0-2-2-2 2-3 6-6 9-8z" class="AI"></path><path d="M128 210c1-2 3-3 4-5 0 1 1 2 1 3l-6 10v-1l-1 2v-1l-1-1c1-2 2-5 3-7z" class="F"></path><path d="M140 210c2-1 5-3 7-4l8-3c1-1 2-2 4-2 0 0 1-1 2-1s1 0 2-1h0v1c-2 2-6 2-8 4h1 3c2-1 2-1 4-1v1c-2 0-5 0-6 1-6 1-11 3-16 6l-1-1z" class="s"></path><path d="M212 143h1l6 1c0 1 0 1 1 2l-11 3c-1 0-3 1-5 1v-1c1 0 2-1 4-1l4-1-3-1v-1c2 0 2 0 3-2z" class="y"></path><path d="M212 143h1v1l2 1c-1 1-2 1-3 2l-3-1v-1c2 0 2 0 3-2z" class="l"></path><path d="M129 279h1c2 1 4 5 5 7 3 2 5 4 7 6l1 1h1v-1c-1-1-1-1-1-2 0-2-1-3-2-5h0c-1-2-1-4-2-5 4 4 4 10 8 14v2h-1c-7-4-12-10-17-17h0z" class="P"></path><path d="M242 174v1 5h1c0-1 1-1 2-2v5h0c-1 1-2 1-2 2l-1 1v-1c-2 1-3 3-4 5l-1 2h-1 0c-1 1-2 1-3 1h1v-1c2-1 1-2 2-3l1-1v-1c1-2 2-3 3-5v-1l1-1c1-1 1-4 1-6z" class="AR"></path><path d="M171 199c1-1 2-2 3-2v-1c1-1 1-1 1-2s1-1 1-2c1-1 1-2 1-3v-1-1l-1-1 1-1v2h1c0-2 0-4 1-5 0-2 0-3 2-5v1c0 1-1 2-1 2 0 1 0 2-1 3v6 1l1 1c-1 1-2 3-2 4l1 1h-1c-1 1-1 1-1 2-2 0-4 1-6 1zm74-21h0v-1c-1-1 0-3 0-4 0 1 0 0 1 1v2c0 2 2 4 2 6 0 1 1 2 2 2h1c1-1 3-1 4-1h0c1-1 2-1 3-1v-1c1 0 2-1 3-2l1 1c-1 1-1 1-3 2h0c-1 1-2 1-3 2h-2c-2 1-3 1-5 1l-2 1h1v1c0 1 0 1-2 1v-1h1-3v-1c-1 0-1 0-1-1s1-1 2-2h0v-5z" class="s"></path><path d="M121 233l1 2c-2 14-1 31 6 43l1 1h0v1l-3-4c0-3-3-7-4-10-2-5-2-11-3-17 0-6 0-11 2-16z" class="Ab"></path><path d="M189 155l3-1h3c-7 3-14 6-20 10-3 2-7 6-10 7-1-1-2-1-3-1 3-2 6-5 9-6 1 0 2-1 3-1 1-2 2-3 3-3l3-3 6-3 3 1z" class="Ac"></path><path d="M186 154l3 1-15 8c1-2 2-3 3-3l3-3 6-3z" class="AM"></path><path d="M230 189l1 1 3-5c0-1 0-1 1-2v-1-1-1-1-3h0v-4c-1-1-2-1-2-2 1 0 0 0 2 1h1v1c1-1 0-2 0-3v-1c-1-1-1-1-1-2v-1l2 3v3l1 5v6 1c-1 4-5 8-7 11 1 0 1 0 2-1 1 0 2 0 3-1h0 1c0 1-1 1-2 2h0c-2 0-2 0-3 1h-1-1-3v1h-2l-1 1-2-1 1-1c3-1 5-4 7-6h0zm-48-40h6c4-1 8-1 12-2 2 0 5 0 8 1-2 0-3 1-4 1v1c-1 1-8 4-9 4h-3l-3 1-3-1-6 3v-1c-1-1 0-1 0-2 1-2 2-3 2-5z" class="l"></path><path d="M186 153h1l-1 1-6 3v-1c0-1 1-1 1-2h1c1-1 3-1 4-1z" class="x"></path><path d="M186 153c4 0 6-3 9-3h1c1 0 4 1 5 0l3-1v1c-1 1-8 4-9 4h-3l-3 1-3-1 1-1h-1z" class="AZ"></path><path d="M182 191h4c1 0 2 1 3 1 1-1 2-3 4-4 1 0 2 0 3-1h1c3 2 5 5 8 5 1 1 2 2 3 2s2-1 3-1h0c3 0 5 0 7-1 1 1 2 1 3 2-3 1-5 1-7 1-4 1-7 1-10 0h-2-3-8v-1h-2-1v-1c-3 0-4-1-6-2z" class="AK"></path><path d="M188 193c3 0 8 1 11-1 1 1 1 0 2 2-1 0-1 0-2 1h0-8v-1h-2-1v-1z" class="s"></path><path d="M146 178c1-1 2-1 3-2 2-1 4-3 6-3 2-1 3-2 5-2l-1 2c-1 1-4 3-4 4-3 2-7 5-9 8l-3 4c-1 0-1 0-2-1l1-2h-1l-3 2c0-1 0-2-1-3l-1 1h-1v-1c1-2 1-3 2-4 0-1 0-2 1-2h3l2 1v-1c1 0 2-1 3-1z" class="b"></path><path d="M135 185c1-2 1-3 2-4 0-1 0-2 1-2h3l2 1c-1 0-2 0-3 2l1 1c-1 0-3 0-4 1h-1l-1 1z" class="AS"></path><path d="M220 191c2-1 4-3 5-5v-1c2-3 2-6 1-9v-2c1 2 1 3 1 4v1 4l2 1v-1c0-1 1-2 1-4 1-1 0-2 0-3v-1-2c-1-1-1-2-1-3v-1-1c-1-2 0-3 1-4l1 1c2 4 2 13 1 18v1c0 2-1 3-2 5h0c-2 2-4 5-7 6l-1 1c-1 0-2 0-3 1-3 1-14 1-17 0v-2h2c3 1 6 1 10 0 2 0 4 0 7-1-1-1-2-1-3-2l2-1z" class="AR"></path><path d="M230 182c1-4 1-7 1-10v-2l-1-1h1v1 1c0 1 0 2 1 4h0v1 5l-2 1z" class="s"></path><path d="M230 182l2-1c-1 3-1 5-2 8-2 2-4 5-7 6l-1 1c-1 0-2 0-3 1-3 1-14 1-17 0v-2h2c3 1 6 1 10 0 2 0 4 0 7-1h1c5-3 7-7 8-12z" class="f"></path><path d="M243 166c2-2 5-6 9-6 2 0 3 0 5 1h1c1 0 3 3 3 4 1 2 0 4 0 5v1h0c-1 2-3 4-4 5h-1-1c-1 1-2 1-3 1l-1-1h-1c-1-1-3-3-3-4-2 0-2 0-3-1 0-1-1-2-1-3v-2z" class="l"></path><path d="M252 163h2c2 0 4 1 5 3 0 2 0 4-1 6-1 1-2 2-4 2s-2 0-4-1c-1-1-2-1-3-3v-5c2-1 3-2 5-2z" class="AX"></path><path d="M182 141c-1-3-3-5-3-8h1c9 4 18 8 28 10h2 2c-1 2-1 2-3 2v1l3 1-4 1c-3-1-6-1-8-1-4 1-8 1-12 2h-6c1-1 0-3 0-5v-3z" class="AQ"></path><path d="M182 141l9 3v1h-3l-2-1-3-1c0 1 0 1-1 1v-3z" class="s"></path><path d="M210 143h2c-1 2-1 2-3 2v1c-6 0-12 0-18-1v-1c4 1 8 0 12 0h2c2 0 2 0 3-1h2z" class="x"></path><path d="M210 143h2c-1 2-1 2-3 2l-6-1h2c2 0 2 0 3-1h2zm-28 1c1 0 1 0 1-1l3 1 2 1h3c6 1 12 1 18 1l3 1-4 1c-3-1-6-1-8-1-4 1-8 1-12 2h-6c1-1 0-3 0-5z" class="AK"></path><path d="M186 144l2 1h1l-2 1c-1 0-1 0-2-1l1-1z" class="l"></path><path d="M208 169h5c3 2 5 4 7 7 0 3 0 4-2 6-1 3-4 4-7 4-2 1-4 0-5 0-3-1-5-3-6-6-1-1 0-3 1-5 1-3 3-5 7-6z" class="AM"></path><path d="M207 171h5c1 0 2 1 3 2 2 2 1 3 1 5-2 2-2 2-4 3s-4 0-6 0l-2-3v-4l3-3z" class="AX"></path><defs><linearGradient id="AY" x1="189.469" y1="195.832" x2="190.952" y2="204.692" xlink:href="#B"><stop offset="0" stop-color="#1c1414"></stop><stop offset="1" stop-color="#3d272c"></stop></linearGradient></defs><path fill="url(#AY)" d="M180 191v-2l2 2c2 1 3 2 6 2v1h1 2v1h8 3v2c3 1 14 1 17 0 1-1 2-1 3-1l2 1-1 1h0-1-2c-1 0-1 1-2 1h-1v1c-1 1-3 1-4 1l-36 2c-4 0-8 1-11 2l-3-1v-1l3-2h0l-1-1s0-1 1-1 1-1 2-1v-1h2l2-2c0-1 0-1 1-2l1 1c-1 2-2 3-4 4h-1l-2 2 1 1 1-1c1 0 1-1 2-1 2 0 4-1 6-1 0-1 0-1 1-2h1l-1-1c0-1 1-3 2-4z"></path><path d="M180 191v-2l2 2c2 1 3 2 6 2v1h1 2v1h8 3v2c-8-1-17-2-25 1 0-1 0-1 1-2h1l-1-1c0-1 1-3 2-4z" class="AM"></path><path d="M191 195c-3 0-8 1-11 0l1-2c3 0 5 0 7 1h1 2v1z" class="AK"></path><defs><linearGradient id="AZ" x1="162.975" y1="209.41" x2="201.896" y2="265.769" xlink:href="#B"><stop offset="0" stop-color="#090000"></stop><stop offset="1" stop-color="#4d1818"></stop></linearGradient></defs><path fill="url(#AZ)" d="M266 176v1c1 5 3 9 7 13 2 6 3 11 3 17v4h1v1c0 3 0 4-1 6-1 3 0 6-3 9-2 0-1 1-3 1h-2 0c-1 1-2 2-2 3l-5 2h-5c-1 0-2 0-3 1l-2-1h0-1-1c-5 0-10-5-15-5-3-1-7-1-10-3s-4-3-7-3h-1c-1-1-2-1-4-1h0-1-4 0c-9-1-19-1-27 2-3 1-5 1-7 2-4 2-8 4-11 7-9 7-16 16-18 27-1 4-1 10 0 14 0 1 2 6 2 7 4 7 8 13 15 17 1 2 2 3 1 5h-5l-4-2-7-4h1v-2c-4-4-4-10-8-14-1-2-1-4-1-6-1-3-2-6-2-9 0-4 1-9 0-13 0-4 2-9 3-13 2-4 5-7 8-11 1-3 4-5 6-7v-1-1c3-3 6-8 10-9 1 0 2-1 3-1 2-1 4-2 5-2 2 0 4 0 5-1v-1h-5c-2 1-3 1-5 1h-1c-2 0-4 1-6 1 0 1-2 1-2 1h-1c1-1 1-2 1-3 1-1 4-1 6-1l3 1c3-1 7-2 11-2l36-2c1 0 3 0 4-1v-1h1c1 0 1-1 2-1h2 1 0l1-1 1-1h2v-1h3 1 1c1-1 1-1 3-1h0c1-1 2-1 2-2l1-2c1-2 2-4 4-5v1l1-1c0 1 0 1 1 1v1h3-1v1c2 0 2 0 2-1v-1h-1l2-1c2 0 3 0 5-1h2c1-1 2-1 3-2h0c2-1 2-1 3-2l2-3c1-1 1 0 2-1z"></path><path d="M175 204l8-1c1 1 3 0 4 1h-1c-1 0-2 0-3 1-2 1-3 1-5 0h-1l-2-1z" class="Z"></path><path d="M146 280c4 7 8 13 15 17 1 2 2 3 1 5h-5l-4-2c1 0 1-1 2-1v-1l-5-5c-2-3-3-6-4-10h0v-3z" class="q"></path><path d="M166 205c3-1 7-2 11-2l36-2 21 3h0v1l-1 1c-10-3-20-3-31-3-6 0-13-1-19 0l-8 1c-3 1-7 1-10 2-2 0-4 1-6 1 0 1-2 1-2 1h-1c1-1 1-2 1-3 1-1 4-1 6-1l3 1z" class="AU"></path><path d="M163 204l3 1c-3 0-5 0-7 2 0 1-2 1-2 1h-1c1-1 1-2 1-3 1-1 4-1 6-1z" class="AQ"></path><path d="M233 215l6 3 1 1c1 1 1 1 1 2h1 1v1 1l-1-1c-2 0-3-1-5-1h-8c-2 0-4-2-7-2-2 0-4 1-5 0s-2-1-3-1c-2 0-5 1-7 3h0c-9-1-19-1-27 2-3 1-5 1-7 2 1-1 2-2 4-2l5-2h2l1-1h2 4c5-1 10 0 15 0 1-1 1-2 3-2v-1c-1-1-2 0-3-1h-1c2-1 5 1 6 1h9c2 0 4-1 6 0l1 1 1-1c2 0 3-1 5-2z" class="w"></path><path d="M205 216c-2 0-3 0-4 1h-2c1-1 1-1 2-1v-1h-2c-4 1-7 0-11 3v1h-3c-4-1-8 4-12 4 2-2 8-5 11-5 1 0 1 0 2-1h1c3-1 7-3 10-3h1 2c3 0 6-1 8-1h8c1 0 3 0 4-1 0-1 0-1 1-2 1 1 3 1 5 2 1 0 2 1 3 1l4 2c-2 1-3 2-5 2l-1 1-1-1c-2-1-4 0-6 0h-9c-1 0-4-2-6-1z" class="Y"></path><defs><linearGradient id="Aa" x1="269.804" y1="184.14" x2="225.303" y2="235.774" xlink:href="#B"><stop offset="0" stop-color="#030707"></stop><stop offset="1" stop-color="#672122"></stop></linearGradient></defs><path fill="url(#Aa)" d="M266 176v1c1 5 3 9 7 13 2 6 3 11 3 17v4h1v1c0 3 0 4-1 6-1 3 0 6-3 9-2 0-1 1-3 1h-2 0c-1 1-2 2-2 3l-5 2h-5c-1 0-2 0-3 1l-2-1h0-1-1c-5 0-10-5-15-5-3-1-7-1-10-3s-4-3-7-3h-1c-1-1-2-1-4-1h0-1-4c2-2 5-3 7-3 1 0 2 0 3 1s3 0 5 0c3 0 5 2 7 2h8c2 0 3 1 5 1l1 1v-1-1l-1-6c-2-3-3-5-5-8l-4-1 1-1v-1h0l-21-3c1 0 3 0 4-1v-1h1c1 0 1-1 2-1h2 1 0l1-1 1-1h2v-1h3 1 1c1-1 1-1 3-1h0c1-1 2-1 2-2l1-2c1-2 2-4 4-5v1l1-1c0 1 0 1 1 1v1h3-1v1c2 0 2 0 2-1v-1h-1l2-1c2 0 3 0 5-1h2c1-1 2-1 3-2h0c2-1 2-1 3-2l2-3c1-1 1 0 2-1z"></path><path d="M273 198v1 1h0v-2z" class="Z"></path><path d="M261 195l2-1 2 1-3 1-1-1z" class="Y"></path><path d="M257 196l4-1 1 1-2 1c-1-1-2-1-3-1h0z" class="w"></path><path d="M263 194c2-1 3-1 5-1v1l-3 1-2-1z" class="P"></path><path d="M276 211h1v1c0 3 0 4-1 6h-2c1-2 1-5 2-7z" class="H"></path><path d="M250 197c2 0 5-1 7-1 1 0 2 0 3 1l-4 1h-5l-1-1z" class="g"></path><path d="M237 203c2 0 5 0 7 1 1-1 0-1 1-1 1-1 1-1 1 0h2l1 1c0 1 1 1 2 1-5 0-10 0-15-1l1-1z" class="Y"></path><path d="M257 191c0 1-1 3-2 4l2 1h0c-2 0-5 1-7 1v-2h0c2 0 3-1 4-2h1l2-2z" class="s"></path><path d="M274 218h2c-1 3 0 6-3 9-2 0-1 1-3 1h-2c3-3 5-6 6-10z" class="J"></path><path d="M249 230h1 3c1 1 3 1 5 1 1-1 0-1 1-1s1 0 2 1c3 0 4-2 7-3-1 1-2 2-2 3l-5 2h-5c-1-1-1-1-2-1-2 0-3-1-5-2z" class="T"></path><path d="M217 200h16-1c-3 1-9 0-11 1l1 1v-1c2 0 2 0 4 1h4c1 1 0 1 2 0 1 0 1 0 2 1h3l-1 1h-2 0l-21-3c1 0 3 0 4-1z" class="V"></path><path d="M250 195v2l1 1h5l-4 1v1c2 1 5 0 7 1h1 0c-2 1-5 1-7 0h-2 0c-2-2-2-2-3-2h-2c-1-1-2-1-2-2 2 0 4-1 6-2zm-13-3l1-2c1-2 2-4 4-5v1c0 1 0 2-1 4v1l-4 5h1 0c-1 1-3 1-4 1h-2v-1c1-1 2-1 2-2h1 0c1-1 2-1 2-2z" class="AN"></path><g class="c"><path d="M242 196c3-3 7-5 12-7 1 0 3 0 5-1 0-2 1-2 3-3v1 1c-1 1-3 3-5 4l-2 2h-1c-1 1-2 2-4 2l-1-1h0c-1 1-3 1-4 1-1 1-2 1-3 1z"></path><path d="M224 197l1-1h2v-1h3 1 1c1-1 1-1 3-1h-1c0 1-1 1-2 2v1h2c1 0 3 0 4-1h4c1 0 2 0 3-1 1 0 3 0 4-1h0l1 1h0c-2 1-4 2-6 2 0 1 1 1 2 2h2c1 0 1 0 3 2-3 0-5 0-8-1h0c-1 0-1 1-2 1-2 0-5 0-7-1h0-1-16v-1h1c1 0 1-1 2-1h2 1 0l1-1z"></path></g><g class="V"><path d="M234 200c2-2 4 0 7-2l1 1c-1 2-6 1-8 1h0z"></path><path d="M242 196c1 0 2 0 3-1 1 0 3 0 4-1h0l1 1h0c-2 1-4 2-6 2h-2l-1 1c-3-1-5 1-7-1 1 0 3 0 4-1h4z"></path></g><path d="M212 221c1 0 1-1 2-1s3 0 4 1c2 0 3 1 5 1l9 1c3 1 8 3 10 2l2 2h1c1 1 2 2 4 3s3 2 5 2c1 0 1 0 2 1-1 0-2 0-3 1l-2-1h0-1-1c-5 0-10-5-15-5-3-1-7-1-10-3s-4-3-7-3h-1c-1-1-2-1-4-1z" class="S"></path><path d="M265 205c1-1 4-1 6 0 0-2 0-2 1-3h2c1 0 1 1 1 3v2l-3 2v2 1c-1 1-3 1-4 2-6 3-12 8-19 9-1 0-2 1-3 0h-1c5-7 11-10 18-12l3-1c-1 0-4 0-5-1v-1l4-1-1-2h1z" class="Z"></path><path d="M265 205c2 0 8-1 9 1l-1 1h-8l-1-2h1z" class="AC"></path><path d="M265 207h8c-2 1-4 2-7 3-1 0-4 0-5-1v-1l4-1z" class="s"></path><defs><linearGradient id="Ab" x1="251.326" y1="203.945" x2="240.951" y2="218.464" xlink:href="#B"><stop offset="0" stop-color="#521b1d"></stop><stop offset="1" stop-color="#762b2d"></stop></linearGradient></defs><path fill="url(#Ab)" d="M234 204h2c5 1 10 1 15 1h1 12l1 2-4 1v1c1 1 4 1 5 1l-3 1c-7 2-13 5-18 12l-2-1v-1l-1-6c-2-3-3-5-5-8l-4-1 1-1v-1z"></path><path d="M237 207c2 1 3 1 4 3l1 5-5-8z" class="B"></path><path d="M261 207v1 1c1 1 4 1 5 1l-3 1c-1-2-5-1-8-2l1-1 5-1z" class="P"></path><path d="M234 204h2c5 1 10 1 15 1h1v2l-18-2v-1z" class="AC"></path><path d="M252 205h12l1 2-4 1v-1l-5 1-1 1-2-2h-1v-2z" class="J"></path><path d="M253 207h8l-5 1-1 1-2-2z" class="T"></path><defs><linearGradient id="Ac" x1="591.249" y1="134.004" x2="634.939" y2="268.314" xlink:href="#B"><stop offset="0" stop-color="#090100"></stop><stop offset="1" stop-color="#472f36"></stop></linearGradient></defs><path fill="url(#Ac)" d="M613 133l1 1c2 0 7 1 8 2l2 2 1 1c2 0 3 1 4 2h5c2 0 4 1 6 2s4 0 6 1c3 1 9 4 11 4l7 3 4 2 1 1 3 1c3 2 7 4 10 6 2 2 4 4 6 4v-1c3 2 6 3 10 2 1 0 1-1 2-1h1 0c1 1 1 2 1 3l1 1c0 1 0 1 1 1v1l1-1c3 5 6 10 11 14l1-1 7 5 4 3-2 2c5 2 12 7 17 8l3 1 2 2c0 1 0 2-1 2 1 2 1 2 2 2l1-1c2 1 4 1 5 3 1 1 0 2-1 4l-1 3-3 4h0c-1 2-2 3-2 5h0l-2 5-6 25c0 1-1 1-1 1-1 1-4 6-4 7 0 2 1 5-1 7-1 3-1 5-1 8-1 3-2 7-2 10h1l-1 3-2 8 2 3-1 3c-1 1-1 3-1 4l-1 3c0 2-1 6-1 8h-2c0 1-1 3-1 4l-1 3c0 1-1 3-1 4l-1-1-1 1 1-3c0-1-1-4-1-6h1v-6l-1-2-1-2c0-1-1-2-2-3-2-1-4-1-7-1-4 1-8 6-11 9 1-3 2-7 4-10v-1c-1-1-1-2-1-4 1-1 1-2 1-3h-1l1-1c0-2 0-2-1-3v-1c-1-1-1-2-1-3-1-1-2-3-3-4 0-1 0-1-1-2 1-1 1-1 0-2v-1c0-1 0-2-1-3 0-1 0-1-1-2v-2c-2-1-3-3-4-5 0-1-1-1-1-1 0-1-1-1-1-2v-1l-1 1-2-1v-2c0 2 0 1-1 2h-1v8c1 3 1 5 1 8l-1 3-3-28c-1-11-3-21-7-31-2-5-3-10-6-15 0-2-3-6-4-8-5-8-11-15-17-22 0-1-2-3-3-3-2-2-5-4-7-6-18-12-41-14-62-14-10 1-22 3-29 12-3 3-4 6-5 10 0 5 0 10 4 13 3 3 7 4 11 4l6-1h0c-6 3-13 3-19 1-4-2-8-6-10-10l-1-4c-2-4-2-8-2-12v-4c0-2 1-4 2-6v-1-2h0l1-1c0-2 1-3 0-6l2 1 7-4h0c10-7 21-12 34-13 5-1 11-1 17-1l3-1c0 1 1 1 2 1h2 2c1 0 2 1 3 1v-1h0l2-2v2c1 0 2 1 3 1 0-1 0-1 1-2l2-1z"></path><path d="M537 154h1-1zm-5 8v-1l1 1-1 1v-1z" class="Z"></path><path d="M689 222l2 2-1 1h-1v-3z" class="P"></path><path d="M533 152l2 1c0 2 0 4-1 6v1h0c-1 0-1-1-2-1l1-1c0-2 1-3 0-6z" class="Z"></path><path d="M667 174h2l4 1c2 4 5 6 7 10-2-1-2-1-4-1-1 0-2-2-2-2l-7-8z" class="P"></path><path d="M651 161c1 0 1-1 2-1s2 0 3 1c1 0 2 1 3 2l3 2c0 3 1 4 3 5l2 2c1 0 2 1 2 2h-2l-9-7c-1-1-3-3-5-4l-2-2z" class="V"></path><path d="M676 184c2 0 2 0 4 1l2 4 6 9c3 3 5 7 6 11l-2-1c-2-3-3-6-5-9-1-1-2-4-3-5-2-3-5-7-8-10z" class="AB"></path><path d="M673 174c2 1 3 2 4 3l11 12c0 1 0 3 1 4s1 3 2 5l-1-1-2 1-6-9-2-4c-2-4-5-6-7-10v-1z" class="AM"></path><path d="M684 188l6 9-2 1-6-9 2-1z" class="l"></path><path d="M673 174c2 1 3 2 4 3l-1 1 8 10-2 1-2-4c-2-4-5-6-7-10v-1z" class="s"></path><path d="M694 226c0-1-1-1-1-2v-1l-1-1-1-2v-2c-1-3-4-4-5-7l1 1v-2l1-1c1 2 2 4 2 6l3 7 3 6c0 2 1 3 2 5 0 2 0 4 1 6h1v3c1 2 1 3 2 5l1-1-1-1v-1c-1-1-1-2-1-3v-1l-1-1v-1-1l-1-1 1-1h-1c-1-1-1-1-1-2l1-1c-1-1-1-1-1-2l-1-2c-1-2-1-4-2-5l-2-5-2-5c-1-3-3-6-4-9v-1c-1-1-2-2-3-4 0-1 0-2-1-2v-1h0c1 1 2 3 3 4h0v1l1 1v-3c2 3 3 6 5 9l2 1 1 1 4 11 4 10v1l3 6v3c1 2 1 3 1 4l1 2h0l-1 1h-1l-1 1h0l-1-1h0v1l-1-2-1 2h0l-4-11h-1 0c-2-2-2-4-2-6l-1-3h0v-3z" class="c"></path><path d="M695 232l-1-3h0v-3c0 1 1 2 1 4 1 2 3 5 3 8h-1 0c-2-2-2-4-2-6z" class="w"></path><path d="M687 199c2 3 3 6 5 9l3 8v4l-1-2v-1l-2-4h0v-1l-2-4v-1l-3-5v-3z" class="y"></path><path d="M694 209l1 1 4 11 4 10v1l3 6v3c1 2 1 3 1 4l1 2h0l-1 1h-1l-1 1h0l-1-1h0v1l-1-2-2-10-2-5-2-6-1-3-1-3v-4l-3-8 2 1z" class="b"></path><path d="M695 216c1 1 2 3 2 4l-1 3-1-3v-4z" class="T"></path><path d="M697 220c1 2 1 3 2 5l-2 1-1-3 1-3z" class="B"></path><path d="M697 226l2-1 2 5-2 2-2-6z" class="q"></path><path d="M699 232l2-2 2 5-1 2h-1 0l-2-5z" class="H"></path><path d="M701 237h0 1l1-2c1 4 2 7 3 11v2l-1 1h0l-1-1h0v1l-1-2-2-10z" class="t"></path><g class="Z"><path d="M541 181c-1 1-1 2-1 3h0 0c0-3 0-4-2-5l-1-1v-2c1-7 5-12 10-16 12-10 32-15 48-13 7 1 17 1 24 4 3 1 5 2 8 4-5 0-7-2-11-3-8-1-16 0-24 1h0c-2 1-3 0-5 1-4 0-10 0-14 3l2 2c-10 1-22 3-29 12-3 3-4 6-5 10z"></path><path d="M613 133l1 1c2 0 7 1 8 2l2 2 1 1c2 0 3 1 4 2l10 5h0c0 1 2 3 2 4l-2 1c1 1 2 2 4 3h0c2 1 3 1 4 2h2 3c2 2 6 4 7 6v1c-1-1-2-2-3-2-1-1-2-1-3-1s-1 1-2 1l2 2h-1l-1-1c-1-1-1 0-2-1v-1h-2c-1-1-2-1-3-1s-2 0-3-1l-6-3c-1 0-2 0-3-1h-1c-2 0-2 0-3-1l-1-1h-1c-1 1-2 0-3-1-1 0 0 0-1-1h-2c-3-2-8-3-11-4-5-2-10-2-15-3-6 0-13-1-20 0-1 0-5 1-6 1s-2 1-2 1h-2c-2 1-3 1-4 2-1 0-2 0-3 1h-1c-1 1-1 1-2 1-1 2-2 2-3 2-3 0-4 1-6 2-1 0-1 0-2 1l-2-1c-1-1-1-1-1-2 1 0 1-1 2-2h0 0c10-7 21-12 34-13 5-1 11-1 17-1l3-1c0 1 1 1 2 1h2 2c1 0 2 1 3 1v-1h0l2-2v2c1 0 2 1 3 1 0-1 0-1 1-2l2-1z"></path></g><path d="M611 136h0c1 1 1 2 2 3l-5-2h0l3-1z" class="AP"></path><path d="M593 135l3-1c0 1 1 1 2 1h2 2c1 0 2 1 3 1v-1h0l2-2v2c1 0 2 1 3 1h1l-3 1h0l-15-2z" class="U"></path><path d="M643 154h0c2 1 3 1 4 2h2 3c2 2 6 4 7 6v1c-1-1-2-2-3-2-1-1-2-1-3-1s-1 1-2 1l-9-7h1z" class="P"></path><path d="M613 133l1 1c2 0 7 1 8 2l2 2 1 1c2 0 3 1 4 2l10 5h0c0 1 2 3 2 4l-2 1c1 1 2 2 4 3h-1c-9-6-18-11-29-15-1-1-1-2-2-3h0-1c0-1 0-1 1-2l2-1z" class="U"></path><path d="M613 133l1 1-1 1 2 2-4-1h0-1c0-1 0-1 1-2l2-1z" class="u"></path><path d="M614 134c2 0 7 1 8 2l2 2 1 1c1 1 2 2 2 3-4-2-7-4-12-5l-2-2 1-1z" class="B"></path><path d="M625 139c2 0 3 1 4 2l10 5h0c0 1 2 3 2 4l-2 1c-3-2-5-5-8-6-1-1-2-2-4-3 0-1-1-2-2-3z" class="W"></path><defs><linearGradient id="Ad" x1="669.728" y1="263.534" x2="699.264" y2="231.867" xlink:href="#B"><stop offset="0" stop-color="#c31820"></stop><stop offset="1" stop-color="#57272b"></stop></linearGradient></defs><path fill="url(#Ad)" d="M647 182c3 0 6 5 8 7l8 12h3c0-1-2-3-3-4h0l-1-1h0 0c2 0 2 0 3 2 4 5 7 8 11 12 2 1 3 2 5 3 0 1-1 1-1 2 1 2 6 4 8 6l1 1v3h1c1 1 2 3 2 4l1 1v1l2 1c0 2 0 4 2 6h0 1l4 11-1 1-1 1 1 1-1 1v1l1 1c2 2 2 5 2 7 1 1 1 1 1 2h1l1 1h0l3 2v1 4l-1 1 1 1c0 1 0 3-1 4h1v3h-1l3 3c0 1 1 1 1 1h0c1 1 1 1 1 2v8c-1 1-1 2-1 3 0 3-1 7-2 10v1c-4 1-8 6-11 9 1-3 2-7 4-10v-1c-1-1-1-2-1-4 1-1 1-2 1-3h-1l1-1c0-2 0-2-1-3v-1c-1-1-1-2-1-3-1-1-2-3-3-4 0-1 0-1-1-2 1-1 1-1 0-2v-1c0-1 0-2-1-3 0-1 0-1-1-2v-2c-2-1-3-3-4-5 0-1-1-1-1-1 0-1-1-1-1-2v-1l-1 1-2-1v-2c0 2 0 1-1 2h-1v8c1 3 1 5 1 8l-1 3-3-28c-1-11-3-21-7-31-2-5-3-10-6-15 0-2-3-6-4-8-5-8-11-15-17-22z"></path><path d="M697 244c1 2 1 4 2 5v2l-2-1v-6z" class="T"></path><path d="M690 230h3v1 4c-1-1-2-2-2-3s-1-1-1-2z" class="B"></path><path d="M688 227h1l1 1c1 1 1 1 2 1l1 1h-3c-2-1-2-3-2-3-1 0-2 0-3-1h2l1 1zm5 4l2 1c0 2 0 4 2 6h0l-1 1-1-3c-1 0-1-1-2-1v-4z" class="P"></path><path d="M688 221l1 1v3h1c1 1 2 3 2 4-1 0-1 0-2-1l-1-1h-1c0-1-1-2-1-2 0-2 0-3 1-4z" class="T"></path><path d="M697 238h1l4 11-1 1-1 1 1 1-1 1v1l-1-3v-2c-1-1-1-3-2-5h0c0-2-1-3-1-5l1-1z" class="B"></path><path d="M701 264c2 2 2 4 2 6 1 2 1 4 2 5 1 3 1 8 1 11l-1 2c-1-2-1-3-1-5-1-1-3-6-3-8 1-4 0-7 0-11z" class="J"></path><path d="M701 255c2 2 2 5 2 7 1 1 1 1 1 2h1l1 1h0l1 4v4 7c1 2 0 4-1 6 0-3 0-8-1-11-1-1-1-3-2-5 0-2 0-4-2-6h0v-9z" class="H"></path><path d="M705 264l1 1h0l1 4-2-1v-4z" class="g"></path><path d="M705 268l2 1v4 7h-1l-1-12z" class="W"></path><path d="M706 265l3 2v1 4l-1 1 1 1c0 1 0 3-1 4h1v3h-1l3 3c0 1 1 1 1 1h0c1 1 1 1 1 2v8c-1 1-1 2-1 3 0 3-1 7-2 10v1c-4 1-8 6-11 9 1-3 2-7 4-10v-1c1-1 1-2 1-3-1-4 3-10 1-14v-2l1-2c1-2 2-4 1-6v-7-4l-1-4z" class="p"></path><path d="M706 265l3 2v1 4c-1 3 0 7-1 9l-1-8v-4l-1-4z" class="r"></path><path d="M707 273l1 8c-1 8-1 15-4 23-1-4 3-10 1-14v-2l1-2c1-2 2-4 1-6v-7z" class="S"></path><path d="M703 308h5c0-1 0-1 1-2h0c0-1 1-2 1-3v-2c1-1 1-1 1-2v-1h1c0 3-1 7-2 10v1c-4 1-8 6-11 9 1-3 2-7 4-10z" class="AA"></path><defs><linearGradient id="Ae" x1="699.366" y1="188.812" x2="674.362" y2="212.78" xlink:href="#B"><stop offset="0" stop-color="#03090a"></stop><stop offset="1" stop-color="#5b2a2d"></stop></linearGradient></defs><path fill="url(#Ae)" d="M629 141h5c2 0 4 1 6 2s4 0 6 1c3 1 9 4 11 4l7 3 4 2 1 1 3 1c3 2 7 4 10 6 2 2 4 4 6 4v-1c3 2 6 3 10 2 1 0 1-1 2-1h1 0c1 1 1 2 1 3l1 1c0 1 0 1 1 1v1l1-1c3 5 6 10 11 14l1-1 7 5 4 3-2 2c5 2 12 7 17 8l3 1 2 2c0 1 0 2-1 2 1 2 1 2 2 2l1-1c2 1 4 1 5 3 1 1 0 2-1 4l-1 3-3 4h0c-1 2-2 3-2 5h0l-2 5-6 25c0 1-1 1-1 1-1 1-4 6-4 7 0 2 1 5-1 7-1 3-1 5-1 8-1 3-2 7-2 10h1l-1 3-2 8 2 3-1 3c-1 1-1 3-1 4l-1 3c0 2-1 6-1 8h-2c0 1-1 3-1 4l-1 3c0 1-1 3-1 4l-1-1-1 1 1-3c0-1-1-4-1-6h1v-6l-1-2-1-2c0-1-1-2-2-3-2-1-4-1-7-1v-1c1-3 2-7 2-10 0-1 0-2 1-3v-8c0-1 0-1-1-2h0s-1 0-1-1l-3-3h1v-3h-1c1-1 1-3 1-4l-1-1 1-1v-4-1l-3-2h0l-1-1h-1c0-1 0-1-1-2 0-2 0-5-2-7l-1-1v-1l1-1-1-1 1-1 1-1h0l1-2 1 2v-1h0l1 1h0l1-1h1l1-1h0l-1-2c0-1 0-2-1-4v-3l-3-6v-1l-4-10-4-11-1-1c-1-4-3-8-6-11l2-1 1 1c-1-2-1-4-2-5s-1-3-1-4l-11-12c-1-1-2-2-4-3v1l-4-1c0-1-1-2-2-2l-2-2c-2-1-3-2-3-5l-3-2v-1c-1-2-5-4-7-6h-3-2c-1-1-2-1-4-2h0c-2-1-3-2-4-3l2-1c0-1-2-3-2-4h0l-10-5z"></path><path d="M717 236v-1c2 3 1 5 2 8v1h-1l-1-5v-3z" class="c"></path><path d="M644 148l-2-2 1-1c3 0 4 1 7 3-2-1-5-2-6-1h2c1 1 1 2 1 3l-3-2z" class="AN"></path><path d="M661 153l3-2 4 2 1 1c1 1 1 2 2 3h-3 0l-7-4z" class="c"></path><path d="M668 153l1 1c1 1 1 2 2 3h-3 0v-4z" class="AL"></path><path d="M659 163v-1l14 12v1l-4-1c0-1-1-2-2-2l-2-2c-2-1-3-2-3-5l-3-2z" class="x"></path><path d="M701 193c3 0 3 5 6 6 3 4 5 9 7 13h0c-1 1-1 1-1 2l-12-21z" class="AB"></path><path d="M639 146l5 2 3 2c1 2 2 3 4 5l1 1h-3-2c-1-1-2-1-4-2h0c-2-1-3-2-4-3l2-1c0-1-2-3-2-4z" class="g"></path><path d="M641 150l6 3c1 1 3 1 4 2l1 1h-3-2c-1-1-2-1-4-2h0c-2-1-3-2-4-3l2-1z" class="T"></path><path d="M629 141h5c2 0 4 1 6 2s4 0 6 1c3 1 9 4 11 4l7 3-3 2c-3-2-7-5-11-5-3-2-4-3-7-3l-1 1 2 2-5-2h0l-10-5z" class="Y"></path><path d="M701 206h0 1c3 6 9 13 11 20 0 2 0 3 1 4 1-3-2-8-3-12l-1-1c-1-1-1-1-1-2v-1c3 4 5 11 6 16 1 2 1 4 2 6v3c-1-3-2-5-4-8v-1s-1-1-2-1-2-3-3-4c0-4-3-7-3-11-1-3-3-6-4-8z" class="d"></path><path d="M705 214c4 4 6 10 8 16 0 0-1-1-2-1s-2-3-3-4c0-4-3-7-3-11z" class="s"></path><path d="M677 165c4 0 21 19 23 23l5 6 2 5c-3-1-3-6-6-6l-24-28z" class="f"></path><path d="M675 163c1 0 1-1 2 0s3 2 5 3 4 3 6 4c1 0 2 1 2 1l1 1c4 2 8 7 11 10v1l-1 1v1c1 0 1 1 1 2 1 2 3 5 5 7h-2l-5-6c-2-4-19-23-23-23 0-1-2-2-2-2z" class="s"></path><path d="M669 154l3 1c3 2 7 4 10 6 2 2 4 4 6 4h1l1 2c1 1 1 0 1 1 1 1 3 3 3 4 1 2 3 3 5 4s5 4 6 6c1 4 6 6 8 10-1-1-2-1-3-2-1-2-5-6-8-8-3-3-7-8-11-10l-1-1s-1-1-2-1c-2-1-4-3-6-4s-4-2-5-3-1 0-2 0l-7-6h3c-1-1-1-2-2-3z" class="l"></path><path d="M669 154l3 1c3 2 7 4 10 6 2 2 4 4 6 4h1l1 2c1 1 1 0 1 1 1 1 3 3 3 4-2-1-3-2-4-3l-8-5c-3-2-6-4-9-5l-2-2c-1-1-1-2-2-3z" class="d"></path><path d="M669 154l3 1 1 4-2-2c-1-1-1-2-2-3z" class="s"></path><path d="M688 189c4 4 9 10 11 15l2 2c1 2 3 5 4 8 0 4 3 7 3 11-4-2-6-8-7-12l-6-3-1-1c-1-4-3-8-6-11l2-1 1 1c-1-2-1-4-2-5s-1-3-1-4z" class="f"></path><path d="M699 204l2 2c1 2 3 5 4 8 0-1-1-1-1-1-2-1-2-2-3-4s-2-3-2-5z" class="s"></path><path d="M695 210l6 3c1 4 3 10 7 12 1 1 2 4 3 4s2 1 2 1v1c2 3 3 5 4 8l1 5c0 1 1 5 0 6h-1c0-2 0-3-1-5l-1 1v3c-2-1-1-2-3-3h-1l-2 2h0v-5l-3-2v-3l-3-6v-1l-4-10-4-11z" class="x"></path><path d="M713 231c2 3 3 5 4 8l1 5c0 1 1 5 0 6h-1c0-2 0-3-1-5 1-3-2-6-2-9v-2c-1-1-1-2-1-3z" class="P"></path><path d="M702 182c3 2 7 6 8 8 1 1 2 1 3 2 4 3 10 9 12 14 3 4 6 9 7 13h1v5 4c0 1 1 3 1 4l-4 8-1 1-1 2c0-1-1-1-1-1-2-1-2-2-3-4 0-1-1-3-1-4h0c-1 0-2-1-2-2h-1l-1 1-2-7c-1-3-2-7-3-9l-1-3c0-1 0-1 1-2h0c-2-4-4-9-7-13l-2-5h2c-2-2-4-5-5-7 0-1 0-2-1-2v-1l1-1v-1z" class="f"></path><path d="M721 218c1-2 0-1 2-3 1 1 2 1 3 1h1c-3 1-4 1-6 2z" class="AZ"></path><path d="M713 214c0-1 0-1 1-2l1 4s-1 0-1 1l-1-3zm4 12l1-2c1 2 3 6 2 8l-1 1-2-7z" class="AH"></path><path d="M714 217c0-1 1-1 1-1l3 8-1 2-3-9z" class="AF"></path><path d="M705 194h2l2 5c1 1 5 8 4 8v1c1 1 1 3 1 4-2-4-4-9-7-13l-2-5zm-3-12c3 2 7 6 8 8 1 1 2 1 3 2 4 3 10 9 12 14-5-3-8-8-13-11-3-3-7-7-10-12v-1z" class="AK"></path><path d="M727 216v-1c1 1 1 2 2 3 1 0 2 1 3 1h0 1v5 4c0 1 1 3 1 4l-4 8-1 1-1 2c0-1-1-1-1-1-2-1-2-2-3-4 0-1-1-3-1-4 0-2-1-4-1-6v-1l1-1-2-2v-4h0v-2c2-1 3-1 6-2z" class="x"></path><path d="M729 218c1 0 2 1 3 1h0 1v5l-4-6z" class="l"></path><path d="M688 165v-1c3 2 6 3 10 2 1 0 1-1 2-1h1 0c1 1 1 2 1 3l1 1c0 1 0 1 1 1v1l1-1c3 5 6 10 11 14l1-1 7 5 4 3-2 2c5 2 12 7 17 8l3 1 2 2c0 1 0 2-1 2 1 2 1 2 2 2l1-1c2 1 4 1 5 3 1 1 0 2-1 4l-1 3-3 4h0c-1 2-2 3-2 5h0c-4 2-7 5-9 8v-1h-1c0 1 0 1-1 2 0 1 0 1-1 1h-1v-1c-2 2-3 5-4 6l-1-1 4-8c0-1-1-3-1-4v-4-5h-1c-1-4-4-9-7-13-2-5-8-11-12-14-2-4-7-6-8-10-1-2-4-5-6-6s-4-2-5-4c0-1-2-3-3-4 0-1 0 0-1-1l-1-2h-1z" class="AL"></path><path d="M717 183l7 5 4 3-2 2-1-1c-3-2-6-5-9-8l1-1z" class="M"></path><path d="M743 201l3 1 2 2c0 1 0 2-1 2 1 2 1 2 2 2l-5 9-1-1-2 2c-3 5-4 10-7 14 0-1-1-3-1-4v-4-5c1 2 1 4 1 5v4 1h0l1-2v-1c0-2 1-4 0-6h0 1v-1-1c0-1 0-2 1-3h0c2-6 6-7 9-11-1 0-1 0-2-1-1 0-1-1-1-2z" class="w"></path><path d="M741 218c1-4 4-8 6-12 1 2 1 2 2 2l-5 9-1-1-2 2z" class="n"></path><path d="M750 207c2 1 4 1 5 3 1 1 0 2-1 4l-1 3-3 4h0c-1 2-2 3-2 5h0c-4 2-7 5-9 8v-1h-1c0 1 0 1-1 2 0 1 0 1-1 1h-1v-1c-2 2-3 5-4 6l-1-1 4-8c3-4 4-9 7-14l2-2 1 1 5-9 1-1z" class="c"></path><path d="M738 229l1 2-1 1h-1-1l2-3z" class="AN"></path><path d="M741 218l2-2 1 1-6 12-2 3-1 3c-2 2-3 5-4 6l-1-1 4-8c3-4 4-9 7-14z" class="M"></path><path d="M739 233c0-2 2-6 3-7v-1c1-1 1-2 1-3l2-1c1-2 3-4 5-5 1 0 1 1 2 1h1l-3 4h0c-1 2-2 3-2 5h0c-4 2-7 5-9 8v-1z" class="V"></path><path d="M739 233c3-3 4-7 6-10 2-2 3-2 5-2-1 2-2 3-2 5h0c-4 2-7 5-9 8v-1z" class="w"></path><defs><linearGradient id="Af" x1="728.612" y1="262.213" x2="722.752" y2="262.269" xlink:href="#B"><stop offset="0" stop-color="#5b1f21"></stop><stop offset="1" stop-color="#4b2832"></stop></linearGradient></defs><path fill="url(#Af)" d="M739 234c2-3 5-6 9-8l-2 5-6 25c0 1-1 1-1 1-1 1-4 6-4 7 0 2 1 5-1 7-1 3-1 5-1 8-1 3-2 7-2 10h1l-1 3-2 8 2 3-1 3c-1 1-1 3-1 4l-1 3c0 2-1 6-1 8h-2c0 1-1 3-1 4l-1 3c0 1-1 3-1 4l-1-1-1 1 1-3c0-1-1-4-1-6h1v-6l-1-2-1-2c0-1-1-2-2-3-2-1-4-1-7-1v-1c1-3 2-7 2-10 0-1 0-2 1-3v-8c0-1 0-1-1-2h0s-1 0-1-1l-3-3h1v-3h-1c1-1 1-3 1-4l-1-1 1-1v-4-1l-3-2h0l-1-1h-1c0-1 0-1-1-2 0-2 0-5-2-7l-1-1v-1l1-1-1-1 1-1 1-1h0l1-2 1 2v-1h0l1 1h0l1-1h1l1-1h0l-1-2c0-1 0-2-1-4l3 2v5h0l2-2h1c2 1 1 2 3 3v-3l1-1c1 2 1 3 1 5h1c1 2 2 5 2 7 1 1 2 2 2 3v3c1-1 1-2 1-3v-5c0-7-2-16-4-22l1-1h1c0 1 1 2 2 2h0c0 1 1 3 1 4 1 2 1 3 3 4 0 0 1 0 1 1l1-2 1-1 1 1c1-1 2-4 4-6v1h1c1 0 1 0 1-1 1-1 1-1 1-2h1v1z"></path><path d="M726 298c1-2 1-7 1-9l1-1h0c0 1 0 2 1 3-1 3-1 6-1 9-1 1-1 2-2 3h-1l1-5z" class="V"></path><path d="M730 276c0 1 0 3 1 5v-2l1-1v-3h0c0-3 0-3 2-4-1 3-1 5-1 8-1 3-2 7-2 10h1l-1 3-2 8-1 2v-2c0-3 0-6 1-9 1-5 0-10 1-15z" class="AC"></path><path d="M719 233l1-1h1c1 6 3 12 3 18 2 11 2 23 2 35l-1-2v2h-1c0-1-1-3-1-4-1-3-1-4 0-7h0c0-3-1-7-2-10-1-2-1-4-1-7 1 1 2 2 2 3v3c1-1 1-2 1-3v-5c0-7-2-16-4-22z" class="AY"></path><path d="M723 255l1 11 1 17v2h-1c0-1-1-3-1-4-1-3-1-4 0-7h0c0-3-1-7-2-10-1-2-1-4-1-7 1 1 2 2 2 3v3c1-1 1-2 1-3v-5z" class="B"></path><path d="M723 255l1 11c0 2 0 3-1 5l-1-8c1-1 1-2 1-3v-5z" class="T"></path><path d="M725 283l1 2v13l-1 5h1c1-1 1-2 2-3v2l1-2 2 3-1 3c-1 1-1 3-1 4l-1 3c0 2-1 6-1 8h-2c0 1-1 3-1 4l-1 3c0 1-1 3-1 4l-1-1-1 1 1-3c0-1-1-4-1-6h1v-6l-1-2c1-1 1-3 1-4h0v-7l1-1h1l1-18h1v-2z" class="q"></path><path d="M728 304l2 2c-1 1-1 3-1 4h-2l1-6z" class="R"></path><path d="M729 300l2 3-1 3-2-2v-2l1-2zm-4 21l2-11h2l-1 3c0 2-1 6-1 8h-2z" class="o"></path><path d="M722 303h1v10l-2-1v-1h0v-7l1-1z" class="t"></path><path d="M721 311v1l2 1v10h-1v-3c-1 1 0 5-1 5v-2-6l-1-2c1-1 1-3 1-4z" class="Q"></path><path d="M721 311v1c1 1 1 3 1 5h-1l-1-2c1-1 1-3 1-4z" class="U"></path><path d="M725 283l1 2v13l-1 5h0c0 1 0 2-1 3v-1l-1-1c1-2 1-4 1-5 1-5 1-9 1-14v-2z" class="Q"></path><path d="M712 270l1 1h1c2-1 2-2 2-3s1-1 1-2l4 5h0c1 1 1 1 2 3h0c-1 3-1 4 0 7 0 1 1 3 1 4l-1 18h-1l-1 1v7h0c0 1 0 3-1 4l-1-2c0-1-1-2-2-3-2-1-4-1-7-1v-1c1-3 2-7 2-10 0-1 0-2 1-3v-8-8-5l-1-4z" class="b"></path><path d="M722 295v1 7l-1 1v-1-5h1c-1-1 0-2 0-3z" class="n"></path><path d="M713 274h1c2 2 1 7 1 10l-2-5v-5z" class="U"></path><path d="M721 271c1 1 1 1 2 3h0c-1 3-1 4 0 7 0 1 1 3 1 4l-1 18h-1v-7-1l-1-24z" class="Q"></path><path d="M713 279l2 5c-1 4-1 11 0 14l1 1c0 1 0 2 1 3 0 1 1 3 0 4v1 1h1 2l1 3h0c0 1 0 3-1 4l-1-2c0-1-1-2-2-3-2-1-4-1-7-1v-1c1-3 2-7 2-10 0-1 0-2 1-3v-8-8z" class="M"></path><path d="M739 234c2-3 5-6 9-8l-2 5-6 25c0 1-1 1-1 1-1 1-4 6-4 7 0 2 1 5-1 7-2 1-2 1-2 4h0v3l-1 1v2c-1-2-1-4-1-5-1-1 0-4 0-5-1-4-2-11-1-16 0-1 1-1 1-2l-1-1v-4h-1l1-1c0-2 0-3-1-4l1-2 1-1 1 1c1-1 2-4 4-6v1h1c1 0 1 0 1-1 1-1 1-1 1-2h1v1z" class="H"></path><path d="M737 238c2-1 4-3 7-5l-4 6h-2c-1 1-1 4-2 5-1 0-3 1-4 1 1-1 1-2 2-3s2-2 3-4z" class="q"></path><path d="M735 235v1h1c1 0 1 0 1-1 1-1 1-1 1-2h1v1l-2 4c-1 2-2 3-3 4s-1 2-2 3h-1c-1-1-1-2-2-4l1-1 1 1c1-1 2-4 4-6z" class="Y"></path><path d="M729 241l1-1 1 1v2c1 0 2-1 3-1-1 1-1 2-2 3h-1c-1-1-1-2-2-4z" class="B"></path><path d="M739 234c2-3 5-6 9-8l-2 5c0 1-1 2-2 2-3 2-5 4-7 5l2-4zm-10 7c1 2 1 3 2 4l-1-1v1 3c0 1 1 3 2 4 0 1 0 1-1 2-2 5 1 11-1 16v1c-1-4-2-11-1-16 0-1 1-1 1-2l-1-1v-4h-1l1-1c0-2 0-3-1-4l1-2z" class="T"></path><path d="M706 241l3 2v5h0l2-2h1c2 1 1 2 3 3v-3l1-1c1 2 1 3 1 5h1c1 2 2 5 2 7 0 3 0 5 1 7 1 3 2 7 2 10-1-2-1-2-2-3h0l-4-5c0 1-1 1-1 2s0 2-2 3h-1l-1-1 1 4v5 8c0-1 0-1-1-2h0s-1 0-1-1l-3-3h1v-3h-1c1-1 1-3 1-4l-1-1 1-1v-4-1l-3-2h0l-1-1h-1c0-1 0-1-1-2 0-2 0-5-2-7l-1-1v-1l1-1-1-1 1-1 1-1h0l1-2 1 2v-1h0l1 1h0l1-1h1l1-1h0l-1-2c0-1 0-2-1-4z" class="H"></path><path d="M716 245c1 2 1 3 1 5h1c1 2 2 5 2 7 0 3 0 5 1 7 1 3 2 7 2 10-1-2-1-2-2-3h0l-4-5c1-1 1-1 1-2l-2-2c0-5-2-11-4-16 2 1 1 2 3 3v-3l1-1z" class="T"></path><path d="M707 248l1-1c2 7 4 15 4 23l1 4v5 8c0-1 0-1-1-2h0s-1 0-1-1l-3-3h1v-3h-1c1-1 1-3 1-4l-1-1 1-1v-4h0 1v-1-3l-1-3-2-13z" class="r"></path><path d="M709 268h1l1 2v8c-1-2-1-5-2-6v-4h0z" class="AE"></path><path d="M709 268h1l1 2h-1l-1-2z" class="M"></path><path d="M703 247l1 2v-1h0l1 1h0l1-1h1l2 13 1 3v3 1h-1 0v-1l-3-2h0l-1-1h-1c0-1 0-1-1-2 0-2 0-5-2-7l-1-1v-1l1-1-1-1 1-1 1-1h0l1-2z" class="AG"></path><path d="M708 261h1l1 3h-2v-3z" class="U"></path><path d="M704 261h2v4l-1-1h-1v-3z" class="w"></path><path d="M708 264h2v3 1h-1 0v-1c0-1 0-2-1-3z" class="J"></path><path d="M703 257c2 1 1 0 2 1l1 3h-2c0-1 0-2-1-4z" class="y"></path><path d="M704 252l1 3v3c-1-1 0 0-2-1l-1-4c1 0 2 0 2-1z" class="w"></path><path d="M703 247l1 2v3c0 1-1 1-2 1l1 4c1 2 1 3 1 4v3c0-1 0-1-1-2 0-2 0-5-2-7l-1-1v-1l1-1-1-1 1-1 1-1h0l1-2z" class="S"></path><path d="M703 247l1 2v3c0 1-1 1-2 1l-1-3 1-1h0l1-2z" class="Y"></path><path d="M704 249v-1h0l1 1h0l1-1h1l2 13h-1l-2-6h-1l-1-3v-3z" class="AC"></path><path d="M704 249v-1h0l1 1h0c1 2 2 4 1 6h-1l-1-3v-3z" class="M"></path><path d="M520 455c-1-2 0-3 0-5 0-4 0-8 2-11 2-4 5-6 8-9 6-5 13-13 21-14 9-2 21 5 28 10 4 1 6 5 8 8 3 5 6 11 6 17 1 2 1 5 2 7 0 6 1 13 0 19-1 2 0 4-1 6 0 3 0 6-1 8l-6 25-3 10c-1 1 0 1-1 2l-1 1-1 2h0c0 2-2 4-3 5-2 4-3 7-3 11h1c3 3 3 9 4 13v4c1 4 0 9 0 14 0 2-1 4-1 6l-1 1h0c0 2 0 1-1 2v3l-1 2c-1 1-1 2-1 3l-1 2c-1 1-1 0-1 1 0 2-2 3-3 5-1 3-3 6-5 9l-2 1c-1 2-2 3-3 4l-1 1h-1c-1 0-1 1-1 1-3 3-4 5-6 8 0 2 2 3 3 3 1 1 4 2 5 2 2 1 3 1 4 3l6 13c-3 10-9 20-13 29l-6 9h0l-1 1h0l-2-1c-2-3-3-5-5-7l-5-14c-2-3-3-6-4-10-1 0-1-2-2-2 1-1 0-2 0-3l-3-7c-2-4-4-8-5-13-2-3-3-7-5-11l-5-14c1-2 1-14 2-17l-1-3c-1-6 0-14 0-20l1-20 2-50 1-20c0-4 0-8 1-11l1-9z" class="z"></path><path d="M540 439l1 1c-2 2-4 5-5 8-1 1-1 2-2 2-1 2-2 5-2 7 0 1 0 1-1 2h0v2h1 0v4h0c-2 3-1 5-2 8v1c0 1-1 2-2 3h0v-12-1c1-1 1-2 2-3h0v-1-1c0-2 1-4 2-6 2-5 4-10 8-14z" class="C"></path><defs><linearGradient id="Ag" x1="532.954" y1="651.561" x2="535.137" y2="617.36" xlink:href="#B"><stop offset="0" stop-color="#a19ea1"></stop><stop offset="1" stop-color="#c2c1c2"></stop></linearGradient></defs><path fill="url(#Ag)" d="M527 593l2 8c0 2-1 4 0 5v4 6l4 4c0 1 1 1 3 2h5v-2c1-2 0 0 1-1v-2l1-1h0 0c-1 3-3 8-2 10l-5 17c-1 2-2 6-3 8l-2 2c1-1 0-2 0-3l-3-7c1-16 2-34-2-50h1z"></path><path d="M515 588h0c1 0 2-1 3-2 2 0 4 0 5 1 2 2 2 4 3 6 4 16 3 34 2 50-2-4-4-8-5-13-2-3-3-7-5-11l-5-14c1-2 1-14 2-17z" class="E"></path><path d="M518 619c1 1 2 2 2 3l1 1v-1l2 4h-1v1c1 1 1 2 1 3-2-3-3-7-5-11z" class="L"></path><defs><linearGradient id="Ah" x1="502.222" y1="487.913" x2="552.203" y2="508.672" xlink:href="#B"><stop offset="0" stop-color="#8a888e"></stop><stop offset="1" stop-color="#c2c1c1"></stop></linearGradient></defs><path fill="url(#Ah)" d="M520 455c-1-2 0-3 0-5 0-4 0-8 2-11 2-4 5-6 8-9 6-5 13-13 21-14 9-2 21 5 28 10h-1-1c0-1 0-1-1-1h0c-2 0-4 1-6 2h-2-1l-1 1c-9 3-18 5-25 12l-1-1c-4 4-6 9-8 14-1 2-2 4-2 6v1 1h0c-1 1-1 2-2 3v1 12c-1 4-1 8-3 12v3 1c0 2-1 3-1 5v1 3c-1 2 0 3-1 5s0 4-1 6c-1 4 1 8 0 12s-1 9-1 14v3 15c0 4 0 8-1 11 1 1 1 2 1 4h1c0 4-1 9-1 13h1c2 2 3 4 5 7h0v1h-1c-1-2-1-4-3-6-1-1-3-1-5-1-1 1-2 2-3 2h0l-1-3c-1-6 0-14 0-20l1-20 2-50 1-20c0-4 0-8 1-11l1-9z"></path><path d="M520 455v8l-1 1h0l1-9zm5 1v-2h-1c2-6 5-12 8-17l1 1c-1 2-3 4-4 7v1c-2 3-3 7-4 10z" class="k"></path><path d="M529 446c2-4 6-10 9-13 1-1 2-2 4-3v1c-1 2-3 3-3 5-1 0-2 1-2 2-1 1-2 1-2 3-1 0-2 1-2 2-3 5-5 10-7 16-1 1-1 1-1 2l-1 1c0-2 1-4 1-6 1-3 2-7 4-10z" class="E"></path><path d="M542 431c1 1 1 2 1 3h-1c0 1 1 2 0 3v-1h-1 0c-2 2-3 3-3 4-1 1-2 1-2 3h-1c0 1-1 1-1 2l-3 7h0c-1 1-1 3-2 4v2l-1 1v2l-1 2v2s-1 1-1 2c-1 1-1 2-1 3l-1 1v4c-1 1-1 3-1 4v4l-3 17c0-3 1-7 1-11l1-11c1-5 2-10 2-16l1-1c0-1 0-1 1-2 2-6 4-11 7-16 0-1 1-2 2-2 0-2 1-2 2-3 0-1 1-2 2-2 0-2 2-3 3-5z" class="O"></path><path d="M520 500l3-17v-4c0-1 0-3 1-4v-4l1-1c0-1 0-2 1-3 0-1 1-2 1-2v-2l1-2v-2l1-1v-2c1-1 1-3 2-4h0l3-7c0-1 1-1 1-2h1c0-2 1-2 2-3 0-1 1-2 3-4l-1 3c-4 4-6 9-8 14-1 2-2 4-2 6v1 1h0c-1 1-1 2-2 3v1 12c-1 4-1 8-3 12v3 1c0 2-1 3-1 5v1 3c-1 2 0 3-1 5s0 4-1 6c-1 4 1 8 0 12s-1 9-1 14v3 15c0 4 0 8-1 11v-3c0-1 0-2-1-4v-12-20 3c0 1 0 1-1 2v1 1 4c1 2 1 3 0 5v-1c-1 1 0 1-1 2h0c-1-2 0-10 0-13 1-1 0-4 0-5 1-2 1-3 1-4 1-1 0-2 0-3 1-2 1-3 1-4 1-1 0-2 0-3 1-2 1-3 1-4v-10z" class="I"></path><defs><linearGradient id="Ai" x1="553.158" y1="557.154" x2="581.121" y2="561.632" xlink:href="#B"><stop offset="0" stop-color="#c4c3c3"></stop><stop offset="1" stop-color="#edeae6"></stop></linearGradient></defs><path fill="url(#Ai)" d="M547 615l8-25c10-35 20-70 22-107v-22c-1-2-1-5-1-7s0-3-1-5v-1c-1-3-2-5-3-8h3 2c6 0 12 7 16 11 1 2 1 5 2 7 0 6 1 13 0 19-1 2 0 4-1 6 0 3 0 6-1 8l-6 25-3 10c-1 1 0 1-1 2l-1 1-1 2h0c0 2-2 4-3 5-2 4-3 7-3 11h1c3 3 3 9 4 13v4c1 4 0 9 0 14 0 2-1 4-1 6l-1 1h0c0 2 0 1-1 2v3l-1 2c-1 1-1 2-1 3l-1 2c-1 1-1 0-1 1 0 2-2 3-3 5-1 3-3 6-5 9l-2 1c-1 2-2 3-3 4l-1 1h-1c-1 0-1 1-1 1-3 3-4 5-6 8 0 2 2 3 3 3 1 1 4 2 5 2 2 1 3 1 4 3l6 13c-3 10-9 20-13 29l-6 9h0l-1 1h0l-2-1c-2-3-3-5-5-7l-5-14c-2-3-3-6-4-10-1 0-1-2-2-2l2-2c1-2 2-6 3-8l5-17v1c0 3-1 5-2 8 2-3 2-6 4-8 1-2 2-4 2-7l2-5z"></path><defs><linearGradient id="Aj" x1="535.219" y1="643.321" x2="543.384" y2="644.139" xlink:href="#B"><stop offset="0" stop-color="#9a999c"></stop><stop offset="1" stop-color="#bcbabb"></stop></linearGradient></defs><path fill="url(#Aj)" d="M547 618l1-1v-1c1-2 1-3 2-4 1 3-1 7-2 9-1 1-1 3-1 4l-2 11-3 9c-1 2 0 4-1 6v1c1 1 1 0 0 1v4 4c-1 2-2 5-2 7 0 1 2 5 2 6 1 1 1 2 2 3v-1l1-1c0 2-1 2-2 4l-5-14c-2-3-3-6-4-10-1 0-1-2-2-2l2-2c1-2 2-6 3-8l5-17v1c0 3-1 5-2 8 2-3 2-6 4-8 1-2 2-4 2-7l2-5v3z"></path><path d="M547 615v3l-1 5c0-2 0-2-1-3l2-5z" class="C"></path><path d="M533 651c1-2 2-6 3-8h1c0 3-2 11-4 12-1 0-1-2-2-2l2-2z" class="G"></path><path d="M531 653l2-2v4c-1 0-1-2-2-2z" class="O"></path><path d="M536 643l5-17v1c0 3-1 5-2 8 2-3 2-6 4-8-1 4-3 8-4 11-1 2-2 3-2 5h-1z" class="K"></path><defs><linearGradient id="Ak" x1="566.773" y1="659.335" x2="544.788" y2="657.925" xlink:href="#B"><stop offset="0" stop-color="#908e94"></stop><stop offset="1" stop-color="#bab8b8"></stop></linearGradient></defs><path fill="url(#Ak)" d="M559 632c2 1 3 1 4 3l6 13c-3 10-9 20-13 29l-6 9h0l-1 1h0l-2-1c-2-3-3-5-5-7 1-2 2-2 2-4l1-3c2-8 6-16 8-24 2-4 3-9 5-13 0-1 1-2 1-3z"></path><path d="M559 657l-8 22v-1l1-1c0-2 0-2-1-3 1-1 1-2 1-2v-2c1-1 1-1 1-2 1-1 0-2 0-3l1-2c-1-1-1-1-1-2s1-3 2-5c1-3 2-9 5-11 1-1 1-1 3-1v3c-1 4-3 7-4 10z" class="C"></path><path d="M559 657c-1-1-1-1-1-2h0c1-4 1-7 4-9l1 1c-1 4-3 7-4 10z" class="X"></path><defs><linearGradient id="Al" x1="618.072" y1="695.045" x2="801.404" y2="722.161" xlink:href="#B"><stop offset="0" stop-color="#d8d6d5"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Al)" d="M658 586v-1l1-4h1v-2c4-5 15-8 20-9l1 2h-4c4 2 9 0 14 0h0l1 1h3-1c-1 1-2 1-3 1 7 0 13 1 20 3 3 0 6 2 9 4 0 0 3 2 4 2l2 2v1c1 2 2 3 3 5v1c0 2 1 3 2 4 0 1 1 1 1 1l1 1v1c0 3-1 6-2 9 0 1-1 2-1 3l-1 2c2 0 2 0 3-1h1v1c1 0 3-1 4-1-2 2-3 2-5 2-1 0-2-1-2 0-1 0-2 2-2 3l-1 3-1 2c1 1 2 1 4 2 1 0 2 1 3 1 1 1 0 1 1 1h1v4h-1v1h1c-1 1-1 2-2 3l-1 2v9c1 14 7 30 13 42 2 4 4 9 6 13 1 0 2 0 3 1h0v2c1 1 1 1 1 2l1 1h0l1 1v1h1v1l1 1v1h0c1 1 1 1 1 2l1 1h1v1 1l1 2h1c0 1 1 2 1 2l2 2v2h1c1 1 1 2 2 4 1 1 1 0 1 1 1 1 1 2 2 2v1l1 1c0 1 1 3 2 4l2 2c2 6 5 11 7 16l3 10c0 1 1 3 1 3l1 2h-1v1c0 1 0 1 1 2h0c1 2 0 1 0 2s1 1 1 1v2 1c1 0 0 1 0 2v1h1v6c-1 1-1 3-1 5v2c-1 0 0-2 0-3 1-2 0-5 0-7-1-1 0-4-1-5v-1-1-2c-1-1-1-1-1-2v-3c-1-1 0-1-1-2 0 0-1-1-1-2-2-3-3-6-4-10-7-16-17-29-31-40-5-3-10-6-16-8h-1c-7-1-14-1-20 0-5 1-10 2-14 4-3 2-15 8-17 10-6 5-13 6-20 9l-2-1v1h0c0 2 2 4 2 6v2c0 5-2 9-3 14-1 4-3 7-5 10l-3 5c-1 1-1 1-3 2l-3 1c-3 1-7 1-10 1-1 0-1 0-2 1-4-1-10-3-14-6l-1-1-1-1c-1-1-1-1-2-1-2-1-4-3-5-4 0-1-2-2-2-3v-1c-2-3 0 1-2-2v-1l-2-2 1-1h0 1c2-4 4-8 5-12v-7h1c0-1-1-2-2-2l1-1v-1c-2-1-3-2-4-3h0c-1-1-1-2-2-2l-1-1-2-2c1-1 2-1 3-3 1-1 2-1 3-3l1-1 2-1 3-1c4-2 7-3 11-3 2-1 4 0 6-1-4-2-9-2-14-3l1-1c1-1 2-1 4-1l2-1c-1 0-2 0-3-1 0-1 0-2 1-3 1 0 1 0 2-1s1-1 2-1h1l-1-1c2 0 3-1 4-2-2-1-2-2-2-4v-1l-1-2v-2l-1-1v-2c0-3-1-8 0-11v-1c0-2 1-4 2-5 0-2 0-3 1-4 1-2 2-2 3-3h2 6c2 1 3 0 5 1h0l4 1h1c3 1 5 3 6 6h1v-1l-1-1c-2-3-4-5-6-7l-1 1-1 1c-1-1-3-1-4-2l-2-1c-1-1-6 0-7 0l-1-1 2-1h0l1-2-2-1-1 1h-4 0-2l21-53 4-5 3-3h0c1-2 1-2 1-3h-1z"></path><path d="M751 700c1 0 2 0 3 1h0v2c1 1 1 1 1 2l1 1h0l1 1v1h1v1l1 1v1h0c1 1 1 1 1 2l1 1c-4-3-7-10-10-14z" class="D"></path><path d="M662 731c1 0 2 0 3-1h0 2l2-1h1 2c1-1 0-1 1-1s2-1 3-1c2-1 4-2 6-4h1 0l2-1c0-1 1-1 2-2h0 1l1-1 2-2h1c1 0 2-1 3-2h0 1s0-1 1-1h1l-2-1h5c-3 2-15 8-17 10-6 5-13 6-20 9l-2-1z" class="h"></path><path d="M690 661c4 1 12 2 15 5h0l-1 1c1 0 2 1 3 2-3 0-5-1-7-3-3-2-7-2-10-4v-1z" class="AB"></path><path d="M687 654l-1-1h0c2-2 5-4 8-4h-1c1 1 2 0 3 0 2 0 6 0 8 1h2c1 0 2 1 3 1h0c1 1 1 1 2 1v1c-8-3-15-4-23 0l-1 1z" class="N"></path><path d="M662 748v-1c0-5-2-8-6-11l-1-1c-2-2-3-4-4-6-1-1-2-1-3-2h0c2 1 4 1 6 2v1l3 3c1 2 4 4 5 6s0 7 0 8v1zm52-67c2 3 2 5 1 8-1 2-1 3-2 5 0 0-1 2-1 3h1l1 1 1-1h1c-2 2-3 0-4 2h6c1-1 1-2 2-2l1-1c0-1 0-1 1-2v-1-1-2h0 1v4c0 2-3 4-5 5s-7 0-10-1c0-1 2-2 3-4l1-1c1-1 2-3 2-4h0l1-1v-5c-1-1-1-1-1-2z" class="K"></path><path d="M686 661h4v1c3 2 7 2 10 4 2 2 4 3 7 3 2 2 3 4 4 6-2 0-3-1-4-3-1 0-2-1-3-2-2-1-4-3-7-4s-10-2-12-5h1z" class="i"></path><path d="M722 633c3 2 8 1 11 1l-1 2c-4-1-8 0-12 0-2 1-2 2-3 4v1c-1 3 0 5 0 7v1 1h0v-1l-1-1c-1-1-2-2-2-4s0-4 1-6l1 3c0-3 1-5 2-7l4-1z" class="z"></path><path d="M654 729l8 3c0 2 2 4 2 6v2c0 5-2 9-3 14-1-2 0-4 1-6v-1c0-1 1-6 0-8s-4-4-5-6l-3-3v-1z" class="I"></path><path d="M631 741l1 1c-1 4 0 8 3 11 2 2 5 4 8 5v1c-1 0-3 1-4 0h0c-3-1-5-2-7-5-3-3-3-6-2-11l1-2z" class="h"></path><path d="M617 734l9 2c-3 3-7 5-9 9-1 1-1 2-2 3v-4c1-1 1-2 1-3 0-3-1-5 1-7h0z" class="O"></path><path d="M655 701h1l1 1-1 2c0 3-2 5-3 7v4 4 1l-1-2c-2-3-3-7-2-10 0-3 2-5 5-7z" class="X"></path><path d="M614 732s1 1 1 2h2c-2 2-1 4-1 7 0 1 0 2-1 3v4l-2 2c-2 1-3 2-4 3-1 0-1-1-2-2h0 1c2-4 4-8 5-12v-7h1z" class="L"></path><path d="M615 734h2c-2 2-1 4-1 7 0 1 0 2-1 3v4l-2 2v-5l1-1v-1l1-9z" class="F"></path><path d="M634 732v-1 1l1-2c3 5-2 11-1 16 0 3 3 6 5 7l1 3c2 0 3 1 3 2-3-1-6-3-8-5-3-3-4-7-3-11l-1-1c0-2 1-3 1-4v-1l2-1v-3z" class="I"></path><path d="M632 742c0-1 1-2 1-3h1c-1 5-2 9 1 13 2 2 3 3 5 4 2 0 3 1 3 2-3-1-6-3-8-5-3-3-4-7-3-11z" class="G"></path><path d="M632 736v1c0 1-1 2-1 4l-1 2-3 4c-2 0-3-1-5-1l-1-2h-1c0-2 2-3 3-5l5-3h2 2z" class="O"></path><path d="M632 736v1c-2 2-4 4-6 5-2-1-2-2-3-3l5-3h2 2z" class="L"></path><path d="M692 702l8 4v1l-1-1c-1 0-2-1-2-1-1 1-3 3-4 3h-2c-2 0-2 0-4 1l-2-1-1 2h-1v-1h-1v2l-2-2 1-1-2-1-1 1v-1h-2c-1 1-1 3-2 4v-5l-1-2c2-1 3-2 6-1 1 1 3 3 4 3l2-1c2 0 5 0 8-1h0l1-1-2-1z" class="X"></path><path d="M673 704c2-1 3-2 6-1 1 1 3 3 4 3l1 1h-1c-2-1-4-2-7-3l-2 2h0l-1-2z" class="C"></path><path d="M713 678h1c1 2 2 4 2 7h1c0 2-2 7-3 8l-1 1c1-2 1-3 2-5 1-3 1-5-1-8v4c0 2-1 5-3 6-3 2-5 1-7 0-4 0-6-2-9-3-4-2-10-3-14-5v-1c-1 0-2-1-4-2 2 1 3 1 4 2 2 0 4 0 5 1 5 2 11 4 16 5 2 0 4 1 5 1v-1h0l5-1c1-1 2-4 1-6v-3z" class="N"></path><path d="M707 689v-1h0l5-1c-1 2-1 3-2 3s-2-1-3-1z" class="O"></path><defs><linearGradient id="Am" x1="625.245" y1="721.298" x2="636.494" y2="725.891" xlink:href="#B"><stop offset="0" stop-color="#a3a2a4"></stop><stop offset="1" stop-color="#c2c0c0"></stop></linearGradient></defs><path fill="url(#Am)" d="M622 715h0c2 0 3 0 5 1l2-1h7c3 3 8 4 12 6 1 1 3 1 4 2-1 1-1 1-3 1h0c-4-2-10-5-14-5-1 1-1 1-1 3 1 3 4 6 4 10 1 5-2 10-1 15v1c0 1 1 2 1 3 1 1 1 1 1 2-2-1-5-4-5-7-1-5 4-11 1-16l-1 2v-1 1c-2-2-3-6-5-8-2-3-5-6-7-9z"></path><defs><linearGradient id="An" x1="632.93" y1="707.769" x2="633.952" y2="720.726" xlink:href="#B"><stop offset="0" stop-color="#7b757b"></stop><stop offset="1" stop-color="#969699"></stop></linearGradient></defs><path fill="url(#An)" d="M633 707c8 2 13 7 18 13l2 2v1h-1c-1-1-3-1-4-2-4-2-9-3-12-6h-7l-2 1c-2-1-3-1-5-1h0l-2-1h0l-3-2-1-1c4-2 7-3 11-3 2-1 4 0 6-1z"></path><path d="M620 714l1-1c1 0 3 0 4-1 3 0 7 0 10 2l1 1h-7l-2 1c-2-1-3-1-5-1h0l-2-1h0z" class="k"></path><defs><linearGradient id="Ao" x1="620.788" y1="700.596" x2="652.726" y2="703.761" xlink:href="#B"><stop offset="0" stop-color="#cdcccc"></stop><stop offset="1" stop-color="#fdfcf9"></stop></linearGradient></defs><path fill="url(#Ao)" d="M630 687c2 2 4 4 7 6l6 3c3 3 8 4 12 5h0c-3 2-5 4-5 7-1 3 0 7 2 10l-1 2c-5-6-10-11-18-13-4-2-9-2-14-3l1-1c1-1 2-1 4-1l2-1c-1 0-2 0-3-1 0-1 0-2 1-3 1 0 1 0 2-1s1-1 2-1h1l-1-1c2 0 3-1 4-2-2-1-2-2-2-4v-1z"></path><path d="M626 701c2 0 3 0 5 1-2 1-5 0-7 0l2-1zm3-6l2-1c1 1 2 1 3 2h-2c-1 0-2 0-4 1l1-1-1-1h1z" class="D"></path><defs><linearGradient id="Ap" x1="610.987" y1="719.015" x2="624.503" y2="730.852" xlink:href="#B"><stop offset="0" stop-color="#989698"></stop><stop offset="1" stop-color="#e2e1e0"></stop></linearGradient></defs><path fill="url(#Ap)" d="M613 712l3-1 1 1 3 2h0l2 1c2 3 5 6 7 9 2 2 3 6 5 8v3l-2 1h-2-2-2l-9-2h0-2c0-1-1-2-1-2 0-1-1-2-2-2l1-1v-1c-2-1-3-2-4-3h0c-1-1-1-2-2-2l-1-1-2-2c1-1 2-1 3-3 1-1 2-1 3-3l1-1 2-1z"></path><path d="M611 722h1v1h-1v-1zm-2 3l1-1h0l-1 1h0z" class="F"></path><path d="M613 712l3-1 1 1c-1 1-2 1-2 2s-1 1-1 1l-1-1h-1l1-2zm-1 6h0v1c-1 1-1 1-1 2-2 1-2 2-4 2l-1-1c2-2 4-3 6-4z" class="L"></path><path d="M613 729l3 3 4-1 1 1h-1c-1 1-2 1-3 2h0-2c0-1-1-2-1-2 0-1-1-2-2-2l1-1z" class="K"></path><path d="M617 712l3 2h0-1c0 3 2 5 4 7-1 0-2 0-2-1-1-1-1-1-2-1-1-2-2-4-4-5 0-1 1-1 2-2z" class="C"></path><path d="M610 714l1-1c1 2 1 3 1 5-2 1-4 2-6 4l-2-2c1-1 2-1 3-3 1-1 2-1 3-3z" class="i"></path><defs><linearGradient id="Aq" x1="619.748" y1="724.673" x2="631.425" y2="723.307" xlink:href="#B"><stop offset="0" stop-color="#c6c5c6"></stop><stop offset="1" stop-color="#f8f7f7"></stop></linearGradient></defs><path fill="url(#Aq)" d="M620 714l2 1c2 3 5 6 7 9 2 2 3 6 5 8v3l-2 1h-2-2-2l-9-2c1-1 2-1 3-2h1c2-1 5-2 7-4-2-2-3-5-5-7s-4-4-4-7h1z"></path><path d="M628 728c2 2 2 3 3 5 0 1 0 2 1 2-1 0-2 1-3 1h1-2-2l-9-2c1-1 2-1 3-2h1c2-1 5-2 7-4z" class="D"></path><path d="M636 656h6c2 1 3 0 5 1h0l4 1h1c3 1 5 3 6 6h1c1 2 1 4 1 6 1 2 1 5 2 7 2 5 6 8 9 11l11 8 3 2 7 4 2 1-1 1h0c-3 1-6 1-8 1l-2 1c-1 0-3-2-4-3-3-1-4 0-6 1-1-2-1-3-2-4-6-6-9-13-13-20-1-2-2-4-2-5v-1c0-1-1-1-1-2h0l-2-4v-1-1h0c-1-2-2-2-3-3s-2-1-3-2-2-1-3-1-1-1-2-1c-1-1-2-1-3-2h-1c-1 0-1-1-2-1z" class="I"></path><path d="M636 656h6c2 1 3 0 5 1h0l4 1h1c3 1 5 3 6 6h1c1 2 1 4 1 6 1 2 1 5 2 7 2 5 6 8 9 11h-1c-1 0-2-1-3-2 0 2 2 3 3 5l1 3-1 1c-3-3-5-7-7-11-2-6-3-13-7-18-2-4-5-5-9-5-1-1-2-1-3-1s-1-1-2-1c-1-1-2-1-3-2h-1c-1 0-1-1-2-1z" class="E"></path><defs><linearGradient id="Ar" x1="679.21" y1="684.54" x2="680.715" y2="704.465" xlink:href="#B"><stop offset="0" stop-color="#848284"></stop><stop offset="1" stop-color="#aeabad"></stop></linearGradient></defs><path fill="url(#Ar)" d="M670 695l1-1-1-3c-1-2-3-3-3-5 1 1 2 2 3 2h1l11 8 3 2 7 4 2 1-1 1h0-1c-1 0-2 0-2-1-2 0-2-1-4 0-2 0-2 0-3 2-2-1-5-4-8-5v1h-1-1c-1-1-2-4-3-6z"></path><path d="M682 696l3 2c-1 1-1 1-1 2l-1-1c-1-1-1-1-1-3z" class="F"></path><path d="M677 643h1v1c-1 1-2 3-2 5 0 1 0 3-1 4 0 2 0 5 1 7l1 1c1-2 1-2 1-3h0c1 1 2 2 4 2l4 1h0-1c2 3 9 4 12 5s5 3 7 4c1 1 2 2 3 2 1 2 2 3 4 3l1 2 1 1v3c1 2 0 5-1 6l-5 1h0v1c-1 0-3-1-5-1-5-1-11-3-16-5h3v-1c0-1 1-1 2-1l-3-2c-1-3-5-5-7-8-2-2-3-5-4-8l-1-2c-1-1-1-2-1-3-1-4 0-11 2-15z" class="I"></path><path d="M691 681l7 3c-3 0-6-1-9-1v-1c0-1 1-1 2-1z" class="E"></path><path d="M677 661c1-2 1-2 1-3h0c1 1 2 2 4 2l4 1h0-1c2 3 9 4 12 5s5 3 7 4c1 1 2 2 3 2 1 2 2 3 4 3l1 2 1 1v3 1c-3-1-3-2-5-4-5-7-13-11-21-14-3-1-8-1-10-3z" class="F"></path><path d="M682 660l4 1h0-1c2 3 9 4 12 5s5 3 7 4c1 1 2 2 3 2 1 2 2 3 4 3l1 2v1c-2 0-5-4-7-5-6-6-13-7-20-10-2-1-2-1-3-3z" class="L"></path><path d="M677 663v1c4 1 8 2 12 4 6 2 12 5 16 10v1c2 2 2 4 3 6l-1 2c-3 0-7-2-9-3l-7-3-3-2c-1-3-5-5-7-8-2-2-3-5-4-8z" class="m"></path><defs><linearGradient id="As" x1="629.217" y1="673.963" x2="651.249" y2="675.534" xlink:href="#B"><stop offset="0" stop-color="#8e8c91"></stop><stop offset="1" stop-color="#c3c1c0"></stop></linearGradient></defs><path fill="url(#As)" d="M634 656h2c1 0 1 1 2 1h1c1 1 2 1 3 2 1 0 1 1 2 1s2 0 3 1 2 1 3 2 2 1 3 3c-2-1-3 0-5 0-2 3 0 7-1 11l-2 8c-1 1-1 3-1 4l2 3c0 1 1 1 1 1 1 1 3 1 4 2 2 2 2 4 4 5v1c-4-1-9-2-12-5l-6-3c-3-2-5-4-7-6l-1-2v-2l-1-1v-2c0-3-1-8 0-11v-1c0-2 1-4 2-5 0-2 0-3 1-4 1-2 2-2 3-3z"></path><path d="M644 689l2 3-1 1h0c-1-1-1-2-1-4z" class="I"></path><path d="M638 692l1 1c-1-3-5-5-6-8l3 2c2 2 6 6 7 9h0l-6-3 1-1z" class="L"></path><path d="M629 685v-2l-1-1v-2c0-3-1-8 0-11v-1c0-2 1-4 2-5 0-2 0-3 1-4 1-2 2-2 3-3 0 2-1 3-1 5-1 0-1 1-1 2-1 1-1 0-1 1v2c-2 3-1 7-2 10 0 3 1 6 3 8 1 1 1 2 1 3h0c1 2 2 3 3 5h1 1l-1 1c-3-2-5-4-7-6l-1-2z" class="i"></path><defs><linearGradient id="At" x1="652.088" y1="642.018" x2="669.082" y2="661.367" xlink:href="#B"><stop offset="0" stop-color="#939195"></stop><stop offset="1" stop-color="#bbb9b9"></stop></linearGradient></defs><path fill="url(#At)" d="M661 645c6 0 11-1 17-5h1 0l-2 3c-2 4-3 11-2 15 0 1 0 2 1 3l1 2c1 3 2 6 4 8 2 3 6 5 7 8l3 2c-1 0-2 0-2 1v1h-3c-1-1-3-1-5-1-1-1-2-1-4-2-3-1-6-3-8-6-3-5-4-9-9-12l-1 1-1-1c-2-3-4-5-6-7l-1 1-1 1c-1-1-3-1-4-2l-2-1c-1-1-6 0-7 0l-1-1 2-1h0l1-2 2-1h-1 2l3-3-2-2 1-1h3c1 0 2 1 3 1h1c2 1 4 0 6 1h4z"></path><path d="M639 650l2-1 3 1-1 1c-1 0-1 1-2 1h-3l1-2z" class="v"></path><path d="M645 646l2 1v1c2 1 3 0 5 0l10 1c-6 1-14 0-20 0l3-3z" class="N"></path><path d="M661 645c6 0 11-1 17-5h1 0l-2 3c-2 4-3 11-2 15 0 1 0 2 1 3l1 2c1 3 2 6 4 8 2 3 6 5 7 8-5-2-9-4-14-8l-5-7h2c1-2 2-4 2-6 0-4 1-8 2-12-5 1-9 2-13 3l-10-1c-2 0-3 1-5 0v-1l-2-1-2-2 1-1h3c1 0 2 1 3 1h1c2 1 4 0 6 1h4z" class="h"></path><path d="M669 664h2c1-2 2-4 2-6 0 2 0 5 1 6v3 1 3l-5-7z" class="z"></path><path d="M647 643c1 0 2 1 3 1h1c2 1 4 0 6 1h4c-4 1-10 0-13 1 1 1 3 2 4 2-2 0-3 1-5 0v-1l-2-1-2-2 1-1h3z" class="K"></path><defs><linearGradient id="Au" x1="656.729" y1="672.341" x2="680.771" y2="661.159" xlink:href="#B"><stop offset="0" stop-color="#7f7d84"></stop><stop offset="1" stop-color="#99979a"></stop></linearGradient></defs><path fill="url(#Au)" d="M643 651h4c2 1 5 1 7 2 6 1 11 5 15 10v1l5 7c5 4 9 6 14 8l3 2c-1 0-2 0-2 1v1h-3c-1-1-3-1-5-1-1-1-2-1-4-2-3-1-6-3-8-6-3-5-4-9-9-12l-1 1-1-1c-2-3-4-5-6-7l-1 1-1 1c-1-1-3-1-4-2l-2-1c-1-1-6 0-7 0l-1-1 2-1h0 3c1 0 1-1 2-1z"></path><defs><linearGradient id="Av" x1="638.493" y1="653.959" x2="651.007" y2="654.016" xlink:href="#B"><stop offset="0" stop-color="#605c64"></stop><stop offset="1" stop-color="#76757b"></stop></linearGradient></defs><path fill="url(#Av)" d="M641 652c2 0 5 0 7 1 1 0 1 1 2 1s1 1 2 1l-1 1-1 1c-1-1-3-1-4-2l-2-1c-1-1-6 0-7 0l-1-1 2-1h0 3z"></path><defs><linearGradient id="Aw" x1="660.387" y1="598.067" x2="715.194" y2="637.382" xlink:href="#B"><stop offset="0" stop-color="#b6b5b6"></stop><stop offset="1" stop-color="#e1e0df"></stop></linearGradient></defs><path fill="url(#Aw)" d="M658 586v-1l1-4h1v-2c4-5 15-8 20-9l1 2h-4c4 2 9 0 14 0h0l1 1h3-1c-1 1-2 1-3 1 7 0 13 1 20 3 3 0 6 2 9 4 0 0 3 2 4 2l2 2v1c1 2 2 3 3 5v1c0 2 1 3 2 4 0 1 1 1 1 1l1 1v1c0 3-1 6-2 9 0 1-1 2-1 3l-1 2c2 0 2 0 3-1h1v1c1 0 3-1 4-1-2 2-3 2-5 2-1 0-2-1-2 0-1 0-2 2-2 3l-1 3-1 2c1 1 2 1 4 2 1 0 2 1 3 1 1 1 0 1 1 1h1v4h-1v1h1c-1 1-1 2-2 3-3 0-8 1-11-1l-4 1c-1 2-2 4-2 7l-1-3c-1 2-1 4-1 6v-2-2c-2-1-5-1-8-2-1 0-2 0-3 1l-1 2-3 3-3 3c3 0 6 0 8 1l5 2h0c-2 0-2 0-3-1h-1-1c-3 0-4-1-7 0h-3c-3 0-6 2-8 4h0l1 1c-1 1-5 4-5 6h0c-2 0-3-1-4-2h0c0 1 0 1-1 3l-1-1c-1-2-1-5-1-7 1-1 1-3 1-4 0-2 1-4 2-5v-1h-1l2-3h0-1c-6 4-11 5-17 5h-4c-2-1-4 0-6-1h-1c-1 0-2-1-3-1h-3l-1 1 2 2-3 3h-2 1l-2 1-2-1-1 1h-4 0-2l21-53 4-5 3-3h0c1-2 1-2 1-3h-1z"></path><path d="M668 626l2 1h-1-3l2-1zm-11-21h0c0-1 0-2 1-3 1 1 0 1 1 1l-1 2c-1 4-2 7-1 11-1-1-1-2-1-3v-1-1c0-2 1-4 1-6z" class="G"></path><path d="M715 638v-1s1-1 1-2h0c0-3 3-6 5-8h1c-2 2-3 3-4 6h0 0 4l-4 1c-1 2-2 4-2 7l-1-3z" class="D"></path><path d="M659 603l2-2h0c2-1 3-3 5-4-2 5-5 8-6 13-1-2-1-2-1-4l-1-1 1-2z" class="E"></path><path d="M723 591c4 2 6 5 9 9 1 0 1-1 1-2v1c0 3-1 6-2 9 0-4-1-7-4-10h-1v-3c-1 0 0 0-1-1 0-1-1-2-2-2h-1l1-1z" class="C"></path><path d="M666 620h0 0c4 6 11 5 17 6 3 0 6 1 9 2 3 0 6 2 8 4h-1v1l-1-1c-7-6-17-4-26-6-3-2-5-3-6-6z" class="K"></path><defs><linearGradient id="Ax" x1="662.636" y1="605.294" x2="661.223" y2="625.16" xlink:href="#B"><stop offset="0" stop-color="#969399"></stop><stop offset="1" stop-color="#b0b0b2"></stop></linearGradient></defs><path fill="url(#Ax)" d="M658 605l1 1c0 2 0 2 1 4 1 6 3 12 8 16l-2 1c-5-4-6-6-9-11-1-4 0-7 1-11z"></path><path d="M720 581s3 2 4 2l2 2v1c1 2 2 3 3 5v1c0 2 1 3 2 4 0 1 1 1 1 1l1 1c0 1 0 2-1 2-3-4-5-7-9-9v-1l-3-3h1v-1h1l-6-3c2-1 2-1 4 0v-2z" class="G"></path><path d="M720 581s3 2 4 2l2 2v1c1 2 2 3 3 5v1c-2-2-4-5-7-6l-6-3c2-1 2-1 4 0v-2z" class="i"></path><path d="M720 581s3 2 4 2l2 2v1c-2 0-5-2-6-3v-2z" class="L"></path><path d="M722 627l3 1 9 2v1h1c-1 1-1 2-2 3-3 0-8 1-11-1h-4 0 0c1-3 2-4 4-6z" class="I"></path><path d="M725 628c-1-1-1-2-2-2 0-2-2-2-3-2-1-1-3-2-4-3 0-1-1-2 0-4v-2c-1-3-2-9-1-12 1 0 2-1 4-1 0 1 1 1 2 1 2 0 5 1 6 3 2 2 2 2 2 5h1l-1 2c2 0 2 0 3-1h1v1c1 0 3-1 4-1-2 2-3 2-5 2-1 0-2-1-2 0-1 0-2 2-2 3l-1 3-1 2c1 1 2 1 4 2 1 0 2 1 3 1 1 1 0 1 1 1h1v4h-1l-9-2z" class="m"></path><path d="M658 586v-1l1-4h1v-2c4-5 15-8 20-9l1 2h-4c4 2 9 0 14 0h0l1 1h3-1c-1 1-2 1-3 1 7 0 13 1 20 3 3 0 6 2 9 4v2c-2-1-2-1-4 0l6 3h-1c-5-3-11-4-17-6l-2 1-1-1h-1v1h-2c-2-1-4 1-7 0-1 0-2 1-4 1-8 2-15 4-21 10-4 3-6 6-8 10-1 1-1 2-1 3h0l-2 5c-1 4-2 6-3 9-1 2-2 5-3 7l-1 1c0 3-1 4-2 6-1 1-2 1-2 2-1 1-1 3-2 4l3 3h-1c1 1 2 1 3 1h0-3l-1 1 2 2-3 3h-2 1l-2 1-2-1-1 1h-4 0-2l21-53 4-5 3-3h0c1-2 1-2 1-3h-1z" class="F"></path><path d="M648 627v-3l2-4c0-1 0-2 1-3l2-4c0-2 1-2 2-3-1 4-2 6-3 9-1 2-2 5-3 7l-1 1z" class="G"></path><path d="M641 643c0-1-1-2-1-4h2l3 3h-1c1 1 2 1 3 1h0-3l-1 1 2 2-3 3h-2 1l-2 1-2-1c0-1 1-2 2-3s1-2 2-3z" class="I"></path><path d="M641 643c1 1 1 1 1 2s-1 2-2 3v1h1l-2 1-2-1c0-1 1-2 2-3s1-2 2-3z" class="G"></path><path d="M639 638c1-1 1-2 1-3 3-4 5-9 6-13 3-9 7-18 11-26 4-6 13-11 20-13-6 3-12 6-17 12-3 3-6 9-7 14v1l-1 2-1 1c-1 1-3 6-3 8-2 4-3 8-5 12h0c-1 2-2 4-4 5z" class="E"></path><path d="M675 581c7-3 16-5 23-3-7 1-14 1-21 5-7 2-16 7-20 13-4 8-8 17-11 26-1 4-3 9-6 13 0 1 0 2-1 3v3c0 1-1 3-2 4l-2 4h2l-1 1h-4c1-1 0-1 1-1 2-3 4-7 5-10 1-4 3-8 5-12l6-18c2-4 3-8 5-11s5-7 8-9h0c1-1 2-1 3-2h1l2-2c1 0 1-1 2-1l1-1c1 0 2 0 2-1 1 0 1 0 2-1z" class="L"></path><path d="M655 592c2 0 3-1 5-2l4-4c2-1 8-5 11-5-1 1-1 1-2 1 0 1-1 1-2 1l-1 1c-1 0-1 1-2 1l-2 2h-1c-1 1-2 1-3 2h0c-3 2-6 6-8 9s-3 7-5 11l-6 18c-2 4-4 8-5 12-1 3-3 7-5 10-1 0 0 0-1 1h0-2l21-53 4-5z" class="i"></path><defs><linearGradient id="Ay" x1="658.339" y1="581.211" x2="718.95" y2="579.369" xlink:href="#B"><stop offset="0" stop-color="#65636c"></stop><stop offset="1" stop-color="#8b8a8e"></stop></linearGradient></defs><path fill="url(#Ay)" d="M658 586v-1l1-4h1v-2c4-5 15-8 20-9l1 2h-4c4 2 9 0 14 0h0l1 1h3-1c-1 1-2 1-3 1 7 0 13 1 20 3 3 0 6 2 9 4v2c-2-1-2-1-4 0-6-2-11-4-18-5-7-2-16 0-23 3-3 0-9 4-11 5l-4 4c-2 1-3 2-5 2l3-3h0c1-2 1-2 1-3h-1z"></path><defs><linearGradient id="Az" x1="662.978" y1="574.067" x2="692.405" y2="581.403" xlink:href="#B"><stop offset="0" stop-color="#b3b2b3"></stop><stop offset="1" stop-color="#edebec"></stop></linearGradient></defs><path fill="url(#Az)" d="M658 586v-1l1-4h1v-2c4-5 15-8 20-9l1 2h-4c4 2 9 0 14 0h0l1 1h3-1c-1 1-2 1-3 1-11 1-24 5-33 12z"></path><defs><linearGradient id="BA" x1="570.869" y1="430.979" x2="701.542" y2="520.46" xlink:href="#B"><stop offset="0" stop-color="#bc141f"></stop><stop offset="1" stop-color="#f43e3a"></stop></linearGradient></defs><path fill="url(#BA)" d="M684 286l1-3c0-3 0-5-1-8v-8h1c1-1 1 0 1-2v2l2 1 1-1v1c0 1 1 1 1 2 0 0 1 0 1 1 1 2 2 4 4 5v2c1 1 1 1 1 2 1 1 1 2 1 3v1c1 1 1 1 0 2 1 1 1 1 1 2 1 1 2 3 3 4 0 1 0 2 1 3v1c1 1 1 1 1 3l-1 1h1c0 1 0 2-1 3 0 2 0 3 1 4v1c-2 3-3 7-4 10 3-3 7-8 11-9 3 0 5 0 7 1 1 1 2 2 2 3l1 2 1 2v6h-1c0 2 1 5 1 6l-1 3c0 2 0 7-1 9-2 2-2 4-3 6-7 33-23 63-33 95l-88 231-11 24-3 8-11 21c0 1 1 2 2 3l-3 5-1 1-2 2c0-1-1-2-1-3h0c-1 0-2 1-2 1h0l-1 1c0-1 1-3 1-4-1-2 0-4 1-6s1-3 1-6l-1 1c0 2-1 3-2 4l-1 1c-1 1-2 3-4 4l-1-1v-3l-1-1c1-3 1-7 1-10 1-2 1-4 1-6v-2h-2c-1-1-1-1-1-2-1-1 0-1 0-2l-1-1c0-2 0-3 2-4l-1-2 2-6v-1h0v-4c0 1 0 1-1 1-1-1-1-2-1-3 1-2 1-4 2-6 4-9 10-19 13-29l1-3 3-7 8-19c1-1 3-7 4-10l-2 2v1h-1v1c0-1-1-3-2-4 0 0 1-3 2-4 2-6 5-12 7-19h0c2-6 3-12 4-18 1-3 2-6 2-9l1-4-2-2c1-1 1-3 2-4v-1h1c1-3 3-7 4-10v-4c1-4 3-8 4-12l5-13 6-13c1-3 2-6 3-10l10-27c1-5 3-10 3-14 0-1 0-2 1-3h0v-1c0-1 0-2 1-3v-2c0-1 0-2 1-3v-2-2l1-2v-1-4c1-4 0-10 0-14l1-1-1-1v-3l-1-1 1-1-1-1v-1l-1-1v-1-1s0-1-1-1v-1h0l-2-4c0-2-1-3-2-4l-3 3v1l-1 1c-1 1-1 2-3 3h-1c1-2 4-3 4-6 1-2 4-5 5-7h1l3-7c1-1 1-2 2-3l3-6c0-2 1-3 2-4l2-5c2-4 3-9 5-14l1-1c1 0 1-1 2-1 2-5 6-9 8-13 1 0 2 1 3 0 2-3 5-6 8-7l10-4 1 1 2-19c1-4 1-8 1-12z"></path><path d="M690 305h1l1 2v4h-1c0-1 0-1-1-1l-1-1-1-1h0v-1h1l1-2z" class="e"></path><path d="M610 598c-1 5-3 9-5 14 1-1 1-2 2-3h0c1 2-1 4-2 6-1 1-1 2-1 4-1 1-2 3-3 5-1 1-1 2-1 3-2 1-2 4-4 6 0-2 1-4 2-6 2-6 5-12 7-17l5-12z" class="p"></path><path d="M586 677h0l2-2c0-1 0-2 1-2l2-2c0-2 1-4 2-5 0-1 1-2 1-2h1l1 1c-4 1-3 7-6 9h-1c0 1 0 2-1 3s-1 2-1 4v1l-1 1c1 0 0 1 1 2l-1 1 1 1c1 0 2-1 2-2v-1c1-1 1-1 1-2l1-1v-2c0-1 1-1 1-2v-1l1-1c0-1 0-2 1-3l1 1-11 24v-4h1c1-1 1-3 1-4 0-2-1-1-2-3l2-1c-1-2-1-2-3-3l1-1h1 1l-2-1v-1l1-1 1-1z" class="r"></path><path d="M718 334l1-1c0-1 0-2 1-3h0l1-1-1 3c0 2 0 7-1 9-2 2-2 4-3 6l-1-1 1-1v-2l1-1v-2-2c-1 1-1 1-1 2-2-1-3-1-4-2v-1c1-1 1-1 2-1h1c-1-1-2-1-4-2l-2 2h-1c-2 1-2 1-3 2v1c-1 2-2 3-4 3-1 0-2-1-3-2 2 0 2 0 4 1 2-2 0-1 1-3 0-1 1-2 2-2 1-1 2-1 3-1 1-1 2-1 2-2s1-1 1-2c1-1 1-1 3 0 0 1-1 1 0 3 1 0 1 0 3 1h0 1v-1z" class="e"></path><path d="M714 337h1 1l-1 2c-1-1-1-1-2-1l1-1z" class="AP"></path><path d="M592 629v-1c1-1 1-2 2-3 0-1 0-1 1-1 0-3 1-4 2-6l8-18v3l1-1c0-1 2-3 2-5v-1c1-1 1-2 1-3l2-2 1 1-1 1c0 2-1 4-1 5l-5 12v-3-1c0 1 0 1-1 2l-1 1c-1 1-1 2-1 3-2 3-3 6-5 9-4 8-9 17-12 25-1 3-6 7-9 8h-2v-4h0l1-1h0v3 1h1c1-1 2-2 4-3s3-2 4-4h0l1-2c0-1 0-1 1-2 0-1 0 0 1-1v-1c0-1 0-1 1-2v-1l2-4c1-1 1 0 1-1h-3l-1 1h-1c-1 0-2-1-2-2 1-1 1-1 2-3l-2-1v-1c0-1 0-1 1-2h0 0 2 1c0 1 0 1-1 2v1c1 0 2 1 3 2h2z" class="AD"></path><path d="M585 631h1c1 1 1 1 1 2-2 0-2 0-2-1v-1zm0-7h2c-1 1-1 2-2 3l-1-1 1-2z" class="e"></path><path d="M592 629v1l-1 1h-3s0-1-1-1l-1-1 1-1c1 1 1 2 3 1h2z" class="p"></path><path d="M603 609c1-2 1-4 3-5 2-3 3-8 5-12v1c0 2-1 4-1 5l-5 12v-3-1c0 1 0 1-1 2l-1 1z" class="r"></path><path d="M710 309c3 0 5 0 7 1 1 1 2 2 2 3l1 2 1 2v6h-1c0 2 1 5 1 6l-1 1h0c-1 1-1 2-1 3l-1 1v-2-4c1-2 1-1 1-3v-1c0-2 0-3-1-4l-1-1-1 2h-1c-1 0-3-1-4-1h-1-1l-3 2h0-2c-1 0-2 1-3 1s-2 0-3-1l1-1v-3c3-3 7-8 11-9z" class="AE"></path><path d="M719 313l1 2 1 2v6h-1c0-2-1-5-2-7-3-1-5 0-8 0 0 0 1-2 2-2 2-1 5 1 7-1z" class="y"></path><path d="M710 309c3 0 5 0 7 1 1 1 2 2 2 3-2 2-5 0-7 1-1 0-2 2-2 2h-1l-5 4c-2 1-3 1-4 1h-1v-3c3-3 7-8 11-9z" class="Y"></path><path d="M717 310c1 1 2 2 2 3-2 2-5 0-7 1-1 0-2 2-2 2h-1c1-1 1-2 2-3 3 0 4-1 6-3z" class="B"></path><defs><linearGradient id="BB" x1="606.73" y1="576.021" x2="583.599" y2="573.633" xlink:href="#B"><stop offset="0" stop-color="#d2d0d2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#BB)" d="M601 538v-4c1 2 1 2 3 4h0 8l-27 71h0l-2 2v1h-1v1c0-1-1-3-2-4 0 0 1-3 2-4 2-6 5-12 7-19h0c2-6 3-12 4-18 1-3 2-6 2-9l1-4-2-2c1-1 1-3 2-4v-1h1c1-3 3-7 4-10z"></path><path d="M596 548h1c1-3 3-7 4-10 0 5-1 9-4 13 0 1-1 2-1 4l-2-2c1-1 1-3 2-4v-1z" class="D"></path><defs><linearGradient id="BC" x1="663.127" y1="320.261" x2="674.918" y2="385.911" xlink:href="#B"><stop offset="0" stop-color="#bbb8b8"></stop><stop offset="1" stop-color="#eee"></stop></linearGradient></defs><path fill="url(#BC)" d="M681 317v4l-2 9-1 4-1 4c0 1 0 4-1 5l-5 17-2 7-2 7-6 17v-1c-3-3-5-8-5-12-3-11-1-22 0-34v-4c1-2 1-5 3-7h0c1-1 2-4 3-6 2-3 5-6 8-7l10-4 1 1z"></path><path d="M670 337c2-6 6-13 11-16l-2 9c-1 1-1 1-2 1l-1 1c-3 1-4 4-6 5z" class="AH"></path><defs><linearGradient id="BD" x1="661.058" y1="356.914" x2="671.302" y2="363.311" xlink:href="#B"><stop offset="0" stop-color="#8e8a8f"></stop><stop offset="1" stop-color="#a6a5a6"></stop></linearGradient></defs><path fill="url(#BD)" d="M670 337c2-1 3-4 6-5l1-1c1 0 1 0 2-1l-1 4-1 4c0 1 0 4-1 5l-5 17-2 7-2 7c-1-1-2-3-3-4-2-4 0-17 2-21v-1c1-4 2-8 4-11z"></path><path d="M666 348l2-2-1 3h-1v-1z" class="k"></path><path d="M670 337c2-1 3-4 6-5l1-1c1 0 1 0 2-1l-1 4h0c-5 2-9 6-10 11v1l-2 2c1-4 2-8 4-11z" class="i"></path><path d="M669 367c-1 0-1-1-1-1-1-2-1-4 0-5 0-4 0-10 2-14 1-4 4-8 7-9 0 1 0 4-1 5l-5 17-2 7z" class="I"></path><path d="M671 360l-1-2v1c-1-5 1-10 3-13 1-2 1-3 3-3l-5 17z" class="C"></path><path d="M603 609l1-1c1-1 1-1 1-2v1 3c-2 5-5 11-7 17-1 2-2 4-2 6l-11 27h0 1 1l1 1c-1 1-1 2-1 2v1c1 1 1 2 1 4l-1 1 1 1-2 7-1 1-1 1v1l2 1h-1-1l-1 1c2 1 2 1 3 3l-2 1c1 2 2 1 2 3 0 1 0 3-1 4h-1v4l-3 8-11 21c0 1 1 2 2 3l-3 5-1 1-2 2c0-1-1-2-1-3h0c-1 0-2 1-2 1h0l-1 1c0-1 1-3 1-4-1-2 0-4 1-6s1-3 1-6l-1 1c0 2-1 3-2 4l-1 1c-1 1-2 3-4 4l-1-1v-3l-1-1c1-3 1-7 1-10 1-2 1-4 1-6v-2h-2c-1-1-1-1-1-2-1-1 0-1 0-2l-1-1c0-2 0-3 2-4l-1-2 2-6v-1h0v-4c0 1 0 1-1 1-1-1-1-2-1-3 1-2 1-4 2-6 4-9 10-19 13-29l1-3 3-7h0v3c0 1 0 2 1 3v4l1 1-1 1h0v4h2c3-1 8-5 9-8 3-8 8-17 12-25 2-3 3-6 5-9 0-1 0-2 1-3z" class="AE"></path><path d="M583 683c-1-1-1 0-2-1l1-1h2l-1 1v1z" class="AD"></path><path d="M569 682c1-2 1-2 2-3 0-1 0-1 1-2h0l1-1c1-1 2-2 3-4l1 1c-1 1-3 3-3 5v1h-1-1c-1 2-1 2-3 3z" class="J"></path><path d="M569 682c2-1 2-1 3-3h1 1l-4 9c-1 2-2 3-2 6 0 1 0 2-1 3 1 1 1 1 2 1-1 2-1 5-2 7v1c1 2 1 2 0 3-1 0-1 0-1 1h0l2 2-3 8-1 1c0 2-1 3-2 4l-1 1c-1 1-2 3-4 4l-1-1v-3l-1-1c1-3 1-7 1-10 1-2 1-4 1-6v-2h-2c-1-1-1-1-1-2-1-1 0-1 0-2l-1-1c0-2 0-3 2-4l-1-2 2-6v3c0 1 0 2 1 3h3s1-1 1-2c2-2 4-6 6-8h1l-1-2 2-2z" class="b"></path><path d="M567 706c1 2 1 2 0 3-1 0-1 0-1 1l-1-2 2-2z" class="J"></path><path d="M583 682c2 1 2 1 3 3l-2 1c1 2 2 1 2 3 0 1 0 3-1 4h-1v4l-3 8-11 21c0 1 1 2 2 3l-3 5-1 1-2 2c0-1-1-2-1-3h0c-1 0-2 1-2 1h0l-1 1c0-1 1-3 1-4-1-2 0-4 1-6s1-3 1-6l3-8-2-2h0c0-1 0-1 1-1 1-1 1-1 0-3v-1c1-2 1-5 2-7 3 0 3-1 5-2h1c0-1 1-1 1-2 1-1 2-3 2-5 1 0 1-1 1-2 2-1 3-3 4-4v-1z" class="M"></path><path d="M584 693v-3l2-1c0 1 0 3-1 4h-1z" class="p"></path><path d="M565 734l5-8c0 1 1 2 2 3l-3 5-1 1-2 2c0-1-1-2-1-3z" class="a"></path><path d="M659 327c1 0 2 1 3 0-1 2-2 5-3 6h0c-2 2-2 5-3 7v4c-1 12-3 23 0 34 0 4 2 9 5 12v1l-49 147h-8 0c-2-2-2-2-3-4 1-4 3-8 4-12l5-13 6-13c1-3 2-6 3-10l10-27c1-5 3-10 3-14 0-1 0-2 1-3h0v-1c0-1 0-2 1-3v-2c0-1 0-2 1-3v-2-2l1-2v-1-4c1-4 0-10 0-14l1-1-1-1v-3l-1-1 1-1-1-1v-1l-1-1v-1-1s0-1-1-1v-1h0l-2-4c0-2-1-3-2-4l-3 3v1l-1 1c-1 1-1 2-3 3h-1c1-2 4-3 4-6 1-2 4-5 5-7h1l3-7c1-1 1-2 2-3l3-6c0-2 1-3 2-4l2-5c2-4 3-9 5-14l1-1c1 0 1-1 2-1 2-5 6-9 8-13z" class="h"></path><path d="M649 352l3-6c1-1 1-1 1-2l1-1c1-1 1-2 2-3v4h-1c-1 1-1 2-1 3-2 3-4 6-5 10-1 0-1 1-1 1 0-2 1-4 1-6z" class="D"></path><defs><linearGradient id="BE" x1="640.273" y1="349.261" x2="654.025" y2="365.993" xlink:href="#B"><stop offset="0" stop-color="#bebcbd"></stop><stop offset="1" stop-color="#e8e7e5"></stop></linearGradient></defs><path fill="url(#BE)" d="M659 327c1 0 2 1 3 0-1 2-2 5-3 6h0c-2 2-2 5-3 7-1 1-1 2-2 3l-1 1c0 1 0 1-1 2l-3 6c0 2-1 4-1 6-1 5-1 10-1 15 0 2 0 4-1 6-1-3-1-6-1-10 0-1-1-4-1-5l-1-3h0l-1-1-1 1 2-5c2-4 3-9 5-14l1-1c1 0 1-1 2-1 2-5 6-9 8-13z"></path><path d="M649 341c1 0 1-1 2-1 0 1 0 2-2 3l-1-1 1-1z" class="G"></path><path d="M641 361l2-5c1 3 1 5 1 8l-1-3h0l-1-1-1 1z" class="D"></path><path d="M649 352c0-3 1-5 2-7 2-4 4-10 7-13l1 1c-2 2-2 5-3 7-1 1-1 2-2 3l-1 1c0 1 0 1-1 2l-3 6z" class="K"></path><path d="M427 134h2l1 2h-3l1 1c-8 3-15 6-22 12l-10 9c-3 3-5 6-8 9l-6 9c-2 2-3 4-4 6l-1 2c-1 2-2 3-3 5v1l-1 2-2 3v1c-1 1-1 2-1 3h-1v2l-1 1h0c0 1-1 2-1 3v1s-1 1-1 2c0 2-1 4-1 6 0 1 0 1-1 2v2 1c0 1 0 2-1 3 0 1 1 4 0 5v11c0 2 0 4 1 6l5 26 2 7v2 1c1 1 1 1 1 2v1 1l32 99-1 2c1 3 2 4 3 6l2 1 4 7v2c-4 1-8 0-12-1-1-1-3-2-4-3l-1-1v2l6 12 2 4-1 1h-2l1 3v6 4l6 28v4h-1c-1-1-2-1-2-2v-2c-2-3-5-6-9-7-4-2-7-3-11-3h0l-5-1v1h-1l-1-1h-2c-3 1-6 2-9 2h-1c-5 1-10 1-15 1-1 0-3 0-4-1l1-3c1-1 1-1 3-2l-1-1h0l-1 1h-2-4l-1-2v2h-1-2c-1 0-1 1-2 1v-6c-1-1-1-3-2-5v-5-1c0-2-1-4-1-5 0-2-1-4-2-5h-1v-4-1c0-1-1-1-1-2v-2l-1-1-1-1c-1-2-1-3 0-4l-2-2v-1c0-1 0-2-1-3h-1l-1-1c0-3 1-3-1-5h-1l1-1c0-1-1-3-1-3-1 0-2 0-3-1s-1-2-1-4h0l-1-1c-1-3-1-4-3-6l-1-8h0l-1-4-2-9-2-6-2-7v-1c0-1-1-2-1-4h0c-1-3-2-6-2-9l-6-19h1l-4-12-7-17-1-4c-1 0-1-1-2-1l-4-11h-1l-3-6-5-7-6-6c-3-2-6-5-10-6l-1-1 1-1h0c1-1 2-1 3-1h5l5-2c0-1 1-2 2-3h0 2c2 0 1-1 3-1 3-3 2-6 3-9 1-2 1-3 1-6v-1h-1v-4c0-6-1-11-3-17l8 6c2 1 4 3 7 4l1-1h1c9 2 15 1 23-4l8-7 2 1 1-1c2-2 3-4 7-5 3-2 5-6 7-9l2-2-2-2c3-3 4-5 5-9l2-4v-3h1c1 0 2 0 3-1 3-3 7-3 10-4 2 0 4-1 6-2 3 0 8-1 11 0 2 0 4-1 6-1l-1-1-2-1 2-1c1 1 3 0 4 0l8-2 4 1c3-2 7-3 10-3l13-4c2 0 5-1 7-1z" class="AL"></path><path d="M358 312h1c1 2 1 2 1 4l-1 1-1-1v-4z" class="V"></path><path d="M381 143c1 1 3 0 4 0l2 1-5 2-1-1-2-1 2-1z" class="AA"></path><path d="M314 224l1 1c0 1 0 2-1 4v4h0v-4c-1 1-1 1-1 2s0 2-1 3c0-3 1-6 2-10z" class="V"></path><path d="M385 143l8-2 4 1-10 2-2-1z" class="a"></path><path d="M336 258s1-1 1-2l1 15-1 1-1-4v-10z" class="p"></path><path d="M369 331c1 0 1 0 2-1l1 3 2 7h-1v-2l-1 4h-1v-4l-1-2c0-1 0-3-1-5z" class="a"></path><path d="M369 331c1 0 1 0 2-1l1 3c-2 1-1 3-1 5l-1-2c0-1 0-3-1-5zm-24-177h1v1c0 6-3 12-6 17l-2-2c3-3 4-5 5-9l2-4v-3z" class="R"></path><path d="M365 180v-1c0-1 1-2 1-2 1-1 1-2 2-3v-1l1-1c1-2 3-4 5-6h0l2-2c2-1 3-4 5-4-2 3-5 6-7 9s-3 5-6 7c-1 1-1 1-2 3l-1 1z" class="AQ"></path><path d="M361 308c0-3-3-12-2-14l1-1v-2h1v2 1c1 3 3 7 2 10 0 1-1 2-2 4z" class="W"></path><path d="M352 326l3-3v-1h-1c-2-1-6-5-7-7v-1c2 0 2 0 3 1 1 0 2 1 3 1 2 0 3 1 3 3 1 1 1 2 0 3 0 2-1 3-2 5l-1 3v-3h0-1v-1z" class="B"></path><path d="M360 336c1 0 1-1 1-2s0-1 1-2v-1c1-1 1-1 2-1 0 1-1 2-1 3h0 2v1l-1 1v1c0 1-1 2-2 2v12c0 1 0 2-1 3l-2-2c1-3 0-6 1-8v-7z" class="g"></path><path d="M361 353c1-1 1-2 1-3v-12c0 4 1 7 1 11h1v-1s0-1 1-1c0-2-1-3 0-4v-2l1-1 1 1c-1 2-1 3-1 5 0 1-1 2-1 2v1c1 1 0 2 0 3h1c0 2 0 2-1 3 0 2 0 5 1 6v1l-1 1h-1c0-2 0-2-1-3 0-2-1-5-2-7z" class="Q"></path><path d="M367 328l3 8 1 2v4 3 1h-1c-2 0-2 0-4 2 0 1 0 3 1 4 0 3 0 5-1 7v2c-1-1-1-4-1-6 1-1 1-1 1-3h-1c0-1 1-2 0-3v-1s1-1 1-2c0-2 0-3 1-5h1c1 1 2 2 2 4v1l1-1-1-1v-3c-1-2-1-3-2-4v-2c-1-2-1-1-1-2v-1c-1-1 0-3 0-4z" class="S"></path><path d="M339 220h1c-1 6-3 13-3 19v17c0 1-1 2-1 2-1 2-1 4-1 5v-1-6-1-2c-1-1 0-1 0-1h-1v-2c1-2 1-3 1-5l1-10 3-15z" class="AC"></path><path d="M358 199l-7 18h-2c0 1 0 1-1 2v1h1 0c-1 2-1 3-1 5-1 0-2-1-2 0-1 1-1 4-2 5-1 2-1 4-2 6 1-7 4-14 6-21 1-1 2-4 3-5 0-1 1-2 1-3l2-2s0-1 1-2h0l3-4z" class="s"></path><path d="M360 358c0-1-1-2-1-3s-1-2-1-2c0-2-1-2-1-4 1-4 0-9 1-14 1-2 1-3 1-5v-1c1-1 1-3 1-4h0c0-1 0-2 1-2h0v2c-1 2 0 2 0 3 1 1-1 3-1 5v3h0v7c-1 2 0 5-1 8l2 2c1 2 2 5 2 7 1 1 1 1 1 3h-2l1-1-1-1c-1-1-1-2-2-3z" class="B"></path><path d="M402 383s0-1-1-1l-1-1c1-1 1-2 1-3v-1c-1-1-1-3-2-5v-1l-1-2c0-1 0-1-1-2 0-1-1-2-1-3-1-2-1-4-2-6s-1-5-2-6l-1-1v-1l-1-1c1-1 1-1 0-2 0-1-1-2-1-3s-1-2-1-2l-4-13v-1c-1-2-2-5-2-7-1-1-1-2-1-3s-1-1-1-2v-1-1l-1-2c-1-1-1-3-1-4-1-1-1-3-1-4l-1-2c-1-2-1-4-1-6-1-1-1 0-1-1v-1-1c-1 0 0-1-1-2 0-2-1-4-1-6v-1l32 99-1 2-1-2z" class="q"></path><path d="M365 147c3 0 8-1 11 0l-13 4c-4 1-13 5-17 4v-1c1 0 2 0 3-1 3-3 7-3 10-4 2 0 4-1 6-2z" class="a"></path><path d="M363 304l5 17 3 9c-1 1-1 1-2 1 1 2 1 4 1 5-1-2-2-5-3-8l-4-12c0-3 0-6-2-8 1-2 2-3 2-4z" class="j"></path><path d="M368 321l3 9c-1 1-1 1-2 1 0-3-2-7-1-10z" class="a"></path><path d="M381 160c2 0 7-7 12-7-4 3-7 6-11 9-5 4-10 11-13 17l-4 6c-1 1-1 2-2 3 0 1-2 2-2 3h-1c1-3 3-8 5-11l1-1c1-2 1-2 2-3 3-2 4-4 6-7s5-6 7-9z" class="AR"></path><path d="M330 241l2-3 2-1c1 0 1-1 2-2l-1 10c0 2 0 3-1 5l-1 1c-1 1-1 1-1 2h-1l-4 3h0c0-2 0-5 1-8 1-2 2-5 2-7z" class="T"></path><path d="M342 304h1c1-4 1-9 2-13 2-2 2-5 4-8 1-1 1-3 1-4 2-7 2-15 3-22l1 1c-1 9-1 19-4 28-2 8-6 16-7 24v1h-2v-1-2l1-4z" class="AD"></path><path d="M341 310v-2l2 2v1h-2v-1z" class="e"></path><path d="M321 188l2 1 1-1c2-2 3-4 7-5-8 8-16 14-25 19h-2c-3 1-6 0-9 0-2 0-5-2-7-2l1-1h1c9 2 15 1 23-4l8-7z" class="AA"></path><path d="M295 202l1-1c3 0 5-1 8 1-3 1-6 0-9 0z" class="R"></path><path d="M412 399l-1 1c-3 0-5 0-7-1h0c-3-1-4-3-6-5h1l1 2c2 1 4 2 6 2l-6-11c-1-1-1-1-1-2v-1c-1-1-2-1-2-3-2-3-4-5-5-8v-1c-1-1-1-2-1-3 0-2-2-3-3-5 0-1 0-1 1-2v1s0 1 1 2c2 4 2 9 6 12 1 2 1 3 2 4s2 2 3 2h1l1 2c1 3 2 4 3 6l2 1 4 7z" class="W"></path><defs><linearGradient id="BF" x1="380.951" y1="380.19" x2="390.549" y2="373.31" xlink:href="#B"><stop offset="0" stop-color="#ce7b72"></stop><stop offset="1" stop-color="#ff8a8f"></stop></linearGradient></defs><path fill="url(#BF)" d="M379 361v-3c-1-1-1 0-1-1h2l7 21c3 6 5 13 8 18v2c-1 1-1 1-2 1-3-4-4-10-6-15l-4-10-4-13z"></path><path d="M342 236c1-2 1-4 2-6 1-1 1-4 2-5 0-1 1 0 2 0 0 2-2 5-2 7 0 3 0 6-1 8s-1 5-2 7c-2 9-1 19-2 28v-2c-1-1-1-2-1-3 1-1 1-1 1-2h-1l1-1v-1l-1-1v-12c0-3 0-6 1-9l1-8z" class="AN"></path><path d="M393 153c1-1 2-2 4-3-6 6-12 12-17 20l-8 13-2-2 1-1c-1-1-2-1-2-1 3-6 8-13 13-17 4-3 7-6 11-9z" class="AZ"></path><path d="M303 236l1 1c0 1 0 2 1 4l1 5 1 1c2 2 1 6 2 9 0-2 0-3 1-5v-6-1-3-1c1 0 0-1 0-2h1c1 1 0 3 0 5 2 2 1 5 2 7l1 2c1-1 1-2 2-3 0 1-1 3-1 4v6h-1-2-1v4 6h-1v-4l-3-1v-2l-2-7c1-1 1-4 1-5l-2-6-1-8z" class="l"></path><path d="M311 243c2 2 1 5 2 7-1 2-1 3-2 4v-4-7z" class="x"></path><path d="M306 250c1 3 2 6 2 10l1 1-2 1-2-7c1-1 1-4 1-5z" class="M"></path><path d="M308 260c1-1 2-1 2-1 0-1 0-3 1-5v1c0 3-1 5 0 8v6h-1v-4l-3-1v-2l2-1-1-1z" class="s"></path><path d="M309 261l1 4-3-1v-2l2-1z" class="J"></path><path d="M313 250l1 2c1-1 1-2 2-3 0 1-1 3-1 4v6h-1-2-1v4c-1-3 0-5 0-8v-1h0c1-1 1-2 2-4z" class="AU"></path><path d="M313 250l1 2c1-1 1-2 2-3 0 1-1 3-1 4v1c-1 0-1 0-1 1h-3v-1h0c1-1 1-2 2-4z" class="H"></path><path d="M427 134h2l1 2h-3l1 1c-8 3-15 6-22 12l-10 9c-3 3-5 6-8 9l-6 9v-1l-2 2h0l1-2v-1l1-1c1-1-1 0 0-1h1v-1l1-1v-1h0l1-1v-1h0l1-1c1-1 0-1 1-1 0-1 1-1 1-2l3-3 11-11h0l3-2h0c1-1 1-2 2-2 1-1 2-1 3-2l1-1c1 0 2 0 3-1 0-1 0-1 1-1l2-1 2-1h1s1-1 2-1h1c1-1 1-1 2-1h1c-2-1-3 0-5 0-1 1-2 0-4 1h-1c-1 1-6 2-8 2h-1l13-4c2 0 5-1 7-1z" class="d"></path><path d="M396 158h-1c2-4 7-8 10-10l1 1-10 9z" class="V"></path><path d="M405 148c2-2 4-3 5-4 6-4 11-6 17-8l1 1c-8 3-15 6-22 12l-1-1z" class="B"></path><path d="M360 191h1c0-1 2-2 2-3 1-1 1-2 2-3l-2 6c-3 3-5 8-6 12l-5 21c-1 3-1 6-2 8-1 3-3 5-3 8-1 2-1 5-2 8-1 7-2 14-2 21 0 5 0 10-1 15-1-3-1-7-1-9 1-9 0-19 2-28 1-2 1-5 2-7s1-5 1-8c0-2 2-5 2-7s0-3 1-5h0-1v-1c1-1 1-1 1-2h2l7-18 2-8z" class="c"></path><path d="M369 179s1 0 2 1l-1 1 2 2c-5 11-10 21-13 33-2 10-3 21-4 32 0 1-1 0-1 0 0-5 1-11 0-16h0c2-2 1-3 1-5v-1c0-1 1-2 1-4v-2-1l1-1v-4c1-3 1-4 1-7 1-3 1-6 2-9 1-1 2-2 2-4l1-1v-2l2-6 4-6z" class="x"></path><path d="M354 327l3-3v3c0 1 1 3 0 4v1c0 7-3 17 0 23h-2l1 2c-2-1-5-1-7-1 0 0-1-1-1-2v-1c0-2 0-4-1-6l-2-13-1-8s2 1 3 1c1-1 1-3 4-3l1 2v1h1 0v3l1-3z" class="d"></path><path d="M350 344l1-1 1 6-1 1-1-2v-4z" class="b"></path><path d="M347 347l2 1h1l1 2 1-1c1 2 1 5 3 6l1 2c-2-1-5-1-7-1 0 0-1-1-1-2v-1c0-2 0-4-1-6z" class="r"></path><path d="M347 347l2 1h1l1 2 1 3c-1 1-2 0-3 0h-1c0-2 0-4-1-6z" class="T"></path><defs><linearGradient id="BG" x1="344.867" y1="328.093" x2="354.708" y2="342.155" xlink:href="#B"><stop offset="0" stop-color="#390b0b"></stop><stop offset="1" stop-color="#672223"></stop></linearGradient></defs><path fill="url(#BG)" d="M344 326s2 1 3 1c1-1 1-3 4-3l1 2v1h1 0v3l-1 3v4l-1 3v3l-1 1v4h-1l-2-1-2-13-1-8z"></path><path d="M349 339v-3c1-2 2-3 3-3v4c-2 0-2 1-3 2z" class="B"></path><path d="M349 339c1-1 1-2 3-2l-1 3v3l-1 1h0c-1-2-1-4-1-5z" class="H"></path><path d="M350 344v-3l1-1v3l-1 1h0z" class="t"></path><path d="M335 194c0 3 0 4-2 6l2 1-8 12c-6 11-8 22-11 34v2c-1 1-1 2-2 3l-1-2c-1-2 0-5-2-7 0-2 1-4 0-5 0-2 0-3 1-4s1-2 1-3 0-1 1-2v4h0v-4c1-2 1-3 1-4l-1-1c0-3 2-6 3-9l1-2c1-1 2-3 2-4h0c2-3 4-6 8-8 0 0 1-1 1-2l6-5z" class="s"></path><defs><linearGradient id="BH" x1="311.641" y1="220.977" x2="320.359" y2="229.523" xlink:href="#B"><stop offset="0" stop-color="#401f23"></stop><stop offset="1" stop-color="#38333e"></stop></linearGradient></defs><path fill="url(#BH)" d="M317 215h2c-1 2-1 4-1 5v1h0s1-1 1-2h0l2-1c0 2-1 4-3 5h0v3 1c-1 2-1 7-3 9 0-1-1-3-1-3v-4c1-2 1-3 1-4l-1-1c0-3 2-6 3-9z"></path><path d="M335 194c0 3 0 4-2 6l2 1-8 12v-1c1-1 1-2 2-3-2 0-3 1-4 2-1 2-3 5-4 7l-2 1h0c0 1-1 2-1 2h0v-1c0-1 0-3 1-5h-2l1-2c1-1 2-3 2-4h0c2-3 4-6 8-8 0 0 1-1 1-2l6-5z" class="AR"></path><path d="M320 209c1 2 1 3 0 5v1c-1 2-1 4-2 5 0-1 0-3 1-5h-2l1-2c1-1 2-3 2-4z" class="c"></path><path d="M318 213l1 1v1h-2l1-2z" class="V"></path><path d="M333 200l2 1-8 12v-1c1-1 1-2 2-3-2 0-3 1-4 2-1 2-3 5-4 7l-2 1c0-1 1-2 1-3 1 0 0 0 1-1h0l1-2c2-4 8-10 11-13z" class="AK"></path><path d="M363 191v2l-1 1c0 2-1 3-2 4-1 3-1 6-2 9 0 3 0 4-1 7v4l-1 1v1 2c0 2-1 3-1 4v1c0 2 1 3-1 5h0c1 5 0 11 0 16 0 0 1 1 1 0l-1 10h0l-1-1c-1 7-1 15-3 22 0 1 0 3-1 4-2 3-2 6-4 8-1 4-1 9-2 13h-1l-1-3v-4l-1-14v-4h0c0 2 0 3 1 5v3c1-1 1-1 1-2l-1-1h1c1-5 1-10 1-15 0-7 1-14 2-21 1-3 1-6 2-8 0-3 2-5 3-8 1-2 1-5 2-8l5-21c1-4 3-9 6-12z" class="T"></path><path d="M363 191v2l-1 1c0 2-1 3-2 4-1 3-1 6-2 9 0 3 0 4-1 7v4l-1 1v1 1 2l-1 1v-4c-1 0-1 1-1 2-1 2-2 3-2 5-1 4-2 7-2 12h1 0c-1 1-1 1-1 2l-3 12h0c-1-1 0-4 0-5l3-16c1-2 1-5 2-8l5-21c1-4 3-9 6-12z" class="P"></path><path d="M350 232l-3 16c0 1-1 4 0 5-2 7-1 14-2 20-1 3 0 5-1 7 0 2-1 5-1 7 0 3 0 8-2 10l-1-14v-4h0c0 2 0 3 1 5v3c1-1 1-1 1-2l-1-1h1c1-5 1-10 1-15 0-7 1-14 2-21 1-3 1-6 2-8 0-3 2-5 3-8z" class="y"></path><path d="M372 342l1-4v2h1l2 5 4 12h-2c0 1 0 0 1 1v3l4 13c0 2 0 2 1 4v2l-2 2h-2c-2 2 0 3-4 4v2h-2l-1-1v-2l-1 1-1-1v-2-2c0-1 0-2-1-3l2-4v-1l-1-1h-2 0c-2 1-5 2-8 2l-4-1-2-1v-1h-1l-3-3v-1l-1-2-1-9c2 0 5 0 7 1l4 1c1 1 1 2 2 3l1 1-1 1h2 1l1-1v-1-2c1-2 1-4 1-7-1-1-1-3-1-4 2-2 2-2 4-2h1v-1-3h1z" class="J"></path><path d="M371 345c1 1 2 3 2 4v2h-2l-1-1h0v-4h0 1v-1z" class="u"></path><path d="M372 342l1-4v2h1l2 5c-1 1-1 2-2 3l-2-6z" class="AO"></path><path d="M376 345l4 12h-2c0 1 0 0 1 1v3l-5-13c1-1 1-2 2-3z" class="Aa"></path><path d="M376 370c2 3 1 6 1 10-1 1-2 1-2 2-1 1 0 2-1 3l-2-2h-1v-2l2-1c1-1 2-4 3-5v-2-3h0z" class="S"></path><path d="M355 371h5 2c3 0 6-1 9-3h1l1 1h0l-4 3c-2 1-5 2-8 2l-4-1-2-1v-1z" class="o"></path><path d="M367 361c0-2 0-6 2-8 2 3 2 6 2 8 0 1 1 2 1 3v1l-1 1h-2l-1-1h1v-2c-2 2-3 3-5 3l1-3 1-1h1v-1z" class="M"></path><path d="M367 361l2-2h1c1 1 0 2 0 3s0 1-1 1c-2 2-3 3-5 3l1-3 1-1h1v-1z" class="AG"></path><path d="M373 369l2-1 1 2h0v3 2c-1 1-2 4-3 5l-2 1c0-1 0-2-1-3l2-4v-1l-1-1h-2 0l4-3h0z" class="W"></path><path d="M373 369l1 1c0 1-1 3-2 4v-1l-1-1h-2 0l4-3z" class="AL"></path><path d="M349 356c2 0 5 0 7 1l4 1c1 1 1 2 2 3l1 1-1 1h2 1l-1 3-2 1c-2 0-6 1-8 0v-1l-3 1-1-2-1-9z" class="d"></path><path d="M355 365l5-1-2 2h-1-1l-1-1z" class="u"></path><path d="M364 363h1l-1 3-2 1v-1h-4l2-2 2-1h2z" class="n"></path><path d="M350 365c2 0 4-1 5 0l1 1h1 1 4v1c-2 0-6 1-8 0v-1l-3 1-1-2z" class="S"></path><path d="M327 256l4-3h1c0-1 0-1 1-2l1-1v2h1s-1 0 0 1v2 1 6 1c0-1 0-3 1-5v10l1 4 1-1 2 8v4l1 14v4l1 3-1 4v2h-1l-3-1c-7-2-15-4-21-8l1-1-1-1v-6c0 1 1 2 1 2v1h1l1-1h1c1-1 2-2 2-3 1-2 3-3 4-5 0-1 0-1 1-2s0-2 0-3c0 0 1-1 1-2v-4c-1-4-1-9-1-13v-7h0z" class="J"></path><path d="M327 256l4-3h1c0-1 0-1 1-2l1-1v2l-1 2h-1c-1 0 0 0-1 1v1 1c-1 1-1 2-2 3 0-2 0-3-2-4z" class="Q"></path><path d="M337 272l1-1 2 8v4l-2 2v-2c-1-4-1-8-1-11z" class="e"></path><path d="M327 256h0c2 1 2 2 2 4s1 5-1 7v5 4c-1-4-1-9-1-13v-7z" class="t"></path><path d="M340 283l1 14v4l1 3-1 4v2h-1l-3-1c-7-2-15-4-21-8l1-1s1 1 2 1c5 2 9 4 14 5l3 1h0v-7c1 1 0 4 1 5s1 1 1 2v1l1 1v-8l-2-1v-1c0-2 1-3 1-4v-10l2-2z" class="j"></path><path d="M340 310h0v-9h1l1 3-1 4v2h-1z" class="R"></path><path d="M316 293c0 1 1 2 1 2v1h1l1-1h1c1-1 2-2 2-3 1-2 3-3 4-5 0-1 0-1 1-2s0-2 0-3c0 0 1-1 1-2 1 2 1 4 2 5l1 7c1 2 1 4 2 5v2c2 2 1 4 1 6l-1 1c-5-1-9-3-14-5-1 0-2-1-2-1l-1-1v-6z" class="B"></path><path d="M330 285l1 7-1 2h-2c1-2 1-6 2-9z" class="T"></path><path d="M328 294h2c0 1-1 2-2 4h-1c-1 0-2 1-4 1-1 1-4 1-6 1 3-1 8-2 10-4 1 0 1-2 1-2z" class="Q"></path><path d="M331 292c1 2 1 4 2 5v2c2 2 1 4 1 6l-1 1c-5-1-9-3-14-5-1 0-2-1-2-1l-1-1 1 1c2 0 5 0 6-1 2 0 3-1 4-1h1c1-2 2-3 2-4l1-2z" class="t"></path><path d="M333 299c2 2 1 4 1 6l-1 1c-5-1-9-3-14-5 4 0 7 2 11 3 2-2 2-3 3-5z" class="U"></path><defs><linearGradient id="BI" x1="336.224" y1="314.357" x2="325.713" y2="343.2" xlink:href="#B"><stop offset="0" stop-color="#3d0909"></stop><stop offset="1" stop-color="#681618"></stop></linearGradient></defs><path fill="url(#BI)" d="M315 301h1c6 4 14 6 21 8l3 1h1v1h2v3 4l1 8h0l1 8 2 13c1 2 1 4 1 6v1l-1-1-2 1-2-2-2-3c-1-3-2-5-3-7-1-4-4-8-6-11-2 0-2 0-3-2-2 0-3 0-4-1-1 3-1 5-1 8 0 0 0 1-1 2 1 3 2 6 2 9-2-4-2-9-4-13 0-4-1-9-3-12l-1-6 1-5-1-2 1-1c-1-3-1-4-3-6v-1z"></path><path d="M323 338v-12l2 1v1c-1 3-1 5-1 8 0 0 0 1-1 2z" class="H"></path><path d="M323 326h2l6 3 1 1v1c-2 0-2 0-3-2-2 0-3 0-4-1v-1l-2-1z" class="S"></path><path d="M334 327c2 1 3 2 4 4s2 3 3 5h1 0c1 1 1 2 2 4l-2 2-8-15z" class="e"></path><path d="M342 335c1-1 1-2 2-2l1 1 2 13c1 2 1 4 1 6v1l-1-1-5-11 2-2c-1-2-1-3-2-4v-1z" class="AV"></path><path d="M315 301h1c6 4 14 6 21 8v1c-1 0-1 0-2 1h-2l-2 1c-2 1-3 1-5 2h0l1 2-2 1c-1 0-2-1-3-2s-2-1-3-1l-1-3-1-2 1-1c-1-3-1-4-3-6v-1z" class="Y"></path><path d="M340 310h1v1h2v3 4l1 8h0l1 8-1-1c-1 0-1 1-2 2v1h0-1c-1-2-2-3-3-5s-2-3-4-4c-1-1-2-3-3-4h0c0-1-1-1-1-2s0-2 1-3c1 0 2 0 2-1 1-2 1-2 1-4h0c1-1 2-1 4-2 1 0 1 0 2-1z" class="U"></path><path d="M340 312l2 2-1 2h-2l1-4z" class="M"></path><path d="M341 311h2v3l-1-1v1l-2-2 1-1z" class="r"></path><path d="M338 317c0 3 2 11 0 13v1c-1-2-2-3-4-4-1-1-2-3-3-4l1-1c1 1 3 2 4 2 1-2 0-2 1-4 0-1 1-2 1-3z" class="H"></path><path d="M331 323h0c0-1-1-1-1-2s0-2 1-3c1 0 2 0 2-1 1-2 1-2 1-4h0c1-1 2-1 4-2v6c0 1-1 2-1 3-1 2 0 2-1 4-1 0-3-1-4-2l-1 1z" class="Q"></path><path d="M342 314v-1l1 1v4l1 8h0l1 8-1-1c-1 0-1 1-2 2h0v-1c-2-3-2-10-2-13 0-2-1-3-1-5h2l1-2z" class="AD"></path><path d="M341 330v-5c1-3 0-5 2-7l1 8h-2c-1 1-1 0-1 1 1 1 0 2 0 3z" class="e"></path><path d="M341 330c0-1 1-2 0-3 0-1 0 0 1-1h2 0l1 8-1-1c-1 0-1 1-2 2h0l-1-5z" class="o"></path><defs><linearGradient id="BJ" x1="327.275" y1="214.087" x2="355.946" y2="229.244" xlink:href="#B"><stop offset="0" stop-color="#000404"></stop><stop offset="1" stop-color="#441919"></stop></linearGradient></defs><path fill="url(#BJ)" d="M329 199h0l2-2c2-1 4-3 6-5 1-2 2-4 3-5 2-1 4-4 6-6l1-2c1-2 3-3 4-4 1-2 3-3 4-4 1-3 5-7 7-9 2-1 4-3 6-5l1-1c2-1 3-3 5-4h2 1c-1 1-1 2-2 2 0 1 0 2-1 3v1c-2 2-3 3-4 5l-4 5-7 12-3 4-11 22-3 9c0 1-1 4-2 5h-1l-3 15c-1 1-1 2-2 2l-2 1-2 3c0 2-1 5-2 7-1 3-1 6-1 8v7c0 4 0 9 1 13v4c0 1-1 2-1 2 0 1 1 2 0 3s-1 1-1 2c-1 2-3 3-4 5 0 1-1 2-2 3h-1l-1 1h-1v-1s-1-1-1-2l-1-7-1-9h-1v-1l-1 1c-1-2-1-3-2-5v-3h1v-6-4h1 2 1v-6c0-1 1-3 1-4v-2c3-12 5-23 11-34l8-12-2-1c2-2 2-3 2-6l-6 5z"></path><path d="M366 166c0-2 1-3 3-4l1 1-4 5v-2z" class="AQ"></path><path d="M358 177l1 3-3 4h-1c0-3 2-5 3-7z" class="s"></path><path d="M338 213c2-4 4-7 7-11-1 3-2 5-3 8v1 1l-1 1c-1-1-2 0-3 0z" class="AK"></path><path d="M328 239l2-2v4c0 2-1 5-2 7-1 3-1 6-1 8v7c-2-2 1-20 1-24z" class="B"></path><path d="M366 166v2l-7 12-1-3s1-1 1-2c2-3 4-6 7-9z" class="l"></path><path d="M338 213c1 0 2-1 3 0l-1 3c0 1-1 2-1 3s-1 2-2 3l-2 2c-1 1-2 1-3 2 1-4 4-9 6-13z" class="c"></path><path d="M315 259l-1 6h0v12h-1v-1l-1 1c-1-2-1-3-2-5v-3h1v-6-4h1 2 1z" class="AU"></path><path d="M311 269h0l2 7-1 1c-1-2-1-3-2-5v-3h1z" class="AC"></path><path d="M315 259l-1 6h0v-1c-2 1-2 1-3 2v3h0v-6-4h1 2 1z" class="b"></path><path d="M355 184h1l-11 22-3 9c0 1-1 4-2 5h-1v-1c0-1 1-2 1-3l1-3 1-1v-1-1l3-8 10-18z" class="f"></path><path d="M335 194l5-5c3-3 5-6 8-10a188.83 188.83 0 0 1 20-20h0c-4 5-9 9-13 14-7 8-14 18-20 28l-2-1c2-2 2-3 2-6z" class="l"></path><path d="M335 224l2-2c1-1 2-2 2-3v1l-3 15c-1 1-1 2-2 2l-2 1-2 3v-4l-2 2 1-5 3-8c1-1 2-1 3-2z" class="P"></path><path d="M329 234h1c1 0 2-1 2-1l-1 3c-1 1 0 1-1 1l-2 2 1-5z" class="T"></path><path d="M332 226c1-1 2-1 3-2 0 3-1 6-3 9 0 0-1 1-2 1h-1l3-8z" class="y"></path><path d="M273 190l8 6c2 1 4 3 7 4 2 0 5 2 7 2 3 0 6 1 9 0h2c-5 5-5 14-5 21l1 7v2l1 4 1 8 2 6c0 1 0 4-1 5l2 7v2l3 1v4 3c1 2 1 3 2 5l1-1v1h1l1 9 1 7v6l1 1-1 1h-1v1c2 2 2 3 3 6l-1 1 1 2-1 5 1 6c2 3 3 8 3 12 2 4 2 9 4 13 0-3-1-6-2-9 1-1 1-2 1-2 0-3 0-5 1-8 1 1 2 1 4 1 1 2 1 2 3 2 2 3 5 7 6 11 1 2 2 4 3 7l2 3 2 2 2-1 1 1c0 1 1 2 1 2l1 9 1 2v1l3 3h1v1l2 1 4 1c3 0 6-1 8-2h0 2l1 1v1l-2 4c1 1 1 2 1 3v2 2l1 1 1-1v2l1 1h2v-2c4-1 2-2 4-4h2l2-2v-2c-1-2-1-2-1-4l4 10c2 5 3 11 6 15 1 0 1 0 2-1l6 12 2 4-1 1h-2l1 3v6 4l6 28v4h-1c-1-1-2-1-2-2v-2c-2-3-5-6-9-7-4-2-7-3-11-3h0l-5-1v1h-1l-1-1h-2c-3 1-6 2-9 2h-1c-5 1-10 1-15 1-1 0-3 0-4-1l1-3c1-1 1-1 3-2l-1-1h0l-1 1h-2-4l-1-2v2h-1-2c-1 0-1 1-2 1v-6c-1-1-1-3-2-5v-5-1c0-2-1-4-1-5 0-2-1-4-2-5h-1v-4-1c0-1-1-1-1-2v-2l-1-1-1-1c-1-2-1-3 0-4l-2-2v-1c0-1 0-2-1-3h-1l-1-1c0-3 1-3-1-5h-1l1-1c0-1-1-3-1-3-1 0-2 0-3-1s-1-2-1-4h0l-1-1c-1-3-1-4-3-6l-1-8h0l-1-4-2-9-2-6-2-7v-1c0-1-1-2-1-4h0c-1-3-2-6-2-9l-6-19h1l-4-12-7-17-1-4c-1 0-1-1-2-1l-4-11h-1l-3-6-5-7-6-6c-3-2-6-5-10-6l-1-1 1-1h0c1-1 2-1 3-1h5l5-2c0-1 1-2 2-3h0 2c2 0 1-1 3-1 3-3 2-6 3-9 1-2 1-3 1-6v-1h-1v-4c0-6-1-11-3-17z" class="b"></path><path d="M292 294h2v1 2c-1 1 1 3 2 4 1 2 1 4 2 6l-2-1-4-12z" class="x"></path><path d="M317 337v-1c0-1 1-1 1-1 1 2 2 5 2 7h-1v-1c0 3 1 6 0 8h0l-2-12z" class="a"></path><path d="M296 306l2 1c1 1 1 3 1 4h-1v-1c-1 3 1 4 1 7 1 1 1 2 1 4 0 1 1 2 1 4l-6-19h1z" class="H"></path><path d="M315 319l2 9 1 7s-1 0-1 1v1c-1-4-1-9-3-13l1-5z" class="R"></path><path d="M285 277h1c2 1 3 1 4 3 0 2 0 2 2 3 1 0 1 0 2 1s2 2 2 4c1 2 2 6 3 7 3 2 5 8 5 12h2v1c0 2 0 3 1 4l-3-3h0c0-1-1-2-1-3v-1-1c-1-1-1-1-1-2h0c-1-3-2-5-4-7h-1v-3c-1 0-1-1-1-2l-1-2c0-1-1-4-2-4-1-1-2 0-3-1h0c-1 4 0 5 1 8h1 0c2 1 2 1 2 3h-2l-7-17z" class="T"></path><path d="M309 288l3 13v4c1 1 1 4 1 5 1 3 2 7 2 9l-1 5c0-1 0-1-1-1 1-1 1-1 0-2v-2-2c-2-1-1-1-2-2v-4c0-1 0-2-1-3v-1c0-1-1-1-1-2 1-5-1-11-1-16 1 0 0 0 1-1z" class="q"></path><path d="M311 315c1-3 0-6 0-9l1-1c1 1 1 4 1 5l-1 1c0 2 0 3 1 5v3-2c-2-1-1-1-2-2z" class="H"></path><path d="M313 319v-3c-1-2-1-3-1-5l1-1c1 3 2 7 2 9l-1 5c0-1 0-1-1-1 1-1 1-1 0-2v-2z" class="Q"></path><path d="M311 365c2 0 2 1 3 2v3h1v-1l1-1c2 3 4 6 4 10 1 2 1 4 1 6s1 2 0 5h0-1l1-1c0-1-1-3-1-3-1 0-2 0-3-1s-1-2-1-4h0l-1-1c-1-3-1-4-3-6l-1-8z" class="U"></path><path d="M312 301h2 1v1c2 2 2 3 3 6l-1 1 1 2-1 5 1 6c2 3 3 8 3 12 0 3 0 6 1 8v3c0 1 0 1-1 1l-1-4c0-2-1-5-2-7l-1-7-2-9c0-2-1-6-2-9 0-1 0-4-1-5v-4z" class="d"></path><path d="M316 308h2l-1 1-1 1v-2z" class="g"></path><path d="M316 310l1-1 1 2-1 5-1-6zm-1-8c2 2 2 3 3 6h-2c-1-2-1-4-1-6z" class="B"></path><path d="M318 322c2 3 3 8 3 12 0 3 0 6 1 8v3c0 1 0 1-1 1l-1-4c0-2-1-5-2-7l-1-7c1 1 1 3 2 5v-4h0v-2c-1-2-1-3-1-5z" class="AN"></path><defs><linearGradient id="BK" x1="333.135" y1="379.144" x2="327.613" y2="380.546" xlink:href="#B"><stop offset="0" stop-color="#d8494b"></stop><stop offset="1" stop-color="#f18d8a"></stop></linearGradient></defs><path fill="url(#BK)" d="M319 349c1-2 0-5 0-8v1h1l1 4 21 67h0l1 5v1c-1 0-1 0-2-2l-15-44c-3-8-5-16-7-24z"></path><path d="M278 261c1 0 1 0 2 1l2-2h2c0 1 1 2 2 2 0-1 1-1 2-1 1 1 2 1 3 2l1-1 1 1h0c0 1 0 2 1 3l1 1v1c1 1 2 2 2 3 0 2 0 3 1 4s2 3 2 3v2l1-1 6 22c0 1 1 2 1 3 1 2 0 4 0 6 1 1 2 3 2 5v1c1 2-1 4 1 6l-1 1v-2c-1-2-1-3-1-4s-3-4-2-5c-1-1-1-2-1-4v-1h-2c0-4-2-10-5-12-1-1-2-5-3-7 0-2-1-3-2-4s-1-1-2-1c-2-1-2-1-2-3-1-2-2-2-4-3h-1l-1-4c-1 0-1-1-2-1l-4-11z" class="P"></path><path d="M284 273h1c1 1 1 2 1 4h-1l-1-4z" class="x"></path><defs><linearGradient id="BL" x1="290.827" y1="244.459" x2="273.89" y2="284.937" xlink:href="#B"><stop offset="0" stop-color="#140b0c"></stop><stop offset="1" stop-color="#361b1b"></stop></linearGradient></defs><path fill="url(#BL)" d="M268 228h2c2 0 1-1 3-1 0 1-2 3-2 4h1c1 0 2 1 3 1v3h1l-1 1v1l6 4v-1s1 1 2 1v-1c1 1 1 1 1 2l2 1h0v1c1 2 2 4 4 5 10 12 14 26 18 40 0 5 2 11 1 16v-3c0-1-1-1-2-2v1l-6-22-1 1v-2s-1-2-2-3-1-2-1-4c0-1-1-2-2-3v-1l-1-1c-1-1-1-2-1-3h0l-1-1-1 1c-1-1-2-1-3-2-1 0-2 0-2 1-1 0-2-1-2-2h-2l-2 2c-1-1-1-1-2-1h-1l-3-6-5-7-6-6c-3-2-6-5-10-6l-1-1 1-1h0c1-1 2-1 3-1h5l5-2c0-1 1-2 2-3h0z"></path><path d="M275 235h1l-1 1v1l-5-2h5z" class="l"></path><path d="M281 241v-1s1 1 2 1v-1c1 1 1 1 1 2l2 1h0v1l-5-3z" class="P"></path><path d="M256 233h5l-1 2-7-1h0c1-1 2-1 3-1z" class="AS"></path><path d="M272 231c1 0 2 1 3 1v3h-5-9l7-1h3l1-2-1-1h1z" class="b"></path><path d="M268 228h2c2 0 1-1 3-1 0 1-2 3-2 4l1 1-1 2h-3l-7 1h-1l1-2 5-2c0-1 1-2 2-3h0z" class="M"></path><path d="M266 231l-2 2c1 1 2 1 4 1l-7 1h-1l1-2 5-2z" class="o"></path><path d="M269 248l1 1 2 2v-1c1-1 1-1 3-2 1 0 1-1 2-1s1 0 2-1h2 0c2 1 3 2 4 3v1c-1 0-2 1-3 1-2 0-2 0-3 1s-3 2-5 3l-5-7z" class="c"></path><path d="M274 255c2-1 4-2 5-3s1-1 3-1c1 0 2-1 3-1 6 6 10 13 13 21 1 3 2 5 3 8l-1 1v-2s-1-2-2-3-1-2-1-4c0-1-1-2-2-3v-1l-1-1c-1-1-1-2-1-3h0l-1-1-1 1c-1-1-2-1-3-2-1 0-2 0-2 1-1 0-2-1-2-2h-2l-2 2c-1-1-1-1-2-1h-1l-3-6z" class="V"></path><defs><linearGradient id="BM" x1="304.395" y1="240.251" x2="284.943" y2="252.536" xlink:href="#B"><stop offset="0" stop-color="#030304"></stop><stop offset="1" stop-color="#211515"></stop></linearGradient></defs><path fill="url(#BM)" d="M273 190l8 6c2 1 4 3 7 4 2 0 5 2 7 2 3 0 6 1 9 0h2c-5 5-5 14-5 21l1 7v2l1 4 1 8 2 6c0 1 0 4-1 5l2 7v2l3 1v4 3c1 2 1 3 2 5l1-1v1h1l1 9 1 7v6l1 1-1 1h-1-1-2l-3-13c-1 1 0 1-1 1-4-14-8-28-18-40-2-1-3-3-4-5v-1h0l-2-1c0-1 0-1-1-2v1c-1 0-2-1-2-1v1l-6-4v-1l1-1h-1v-3c-1 0-2-1-3-1h-1c0-1 2-3 2-4 3-3 2-6 3-9 1-2 1-3 1-6v-1h-1v-4c0-6-1-11-3-17z"></path><path d="M307 264l3 1v4 3h-1c-1-3-1-6-2-8z" class="M"></path><path d="M283 240l-1-1h0 0 3l6 6-5-2h0l-2-1c0-1 0-1-1-2z" class="c"></path><path d="M288 212h1c2 0 2 1 3 2 0 1 0 2 1 3v1h-1l1 1v1l-5-8z" class="u"></path><path d="M278 203c0 3 1 6 0 9h-1v-1h-1v-4l1-1c0-1 0-2 1-3z" class="q"></path><path d="M302 243h0c1 1 1 3 2 4h0v-1-1-1h0l2 6c0 1 0 4-1 5l-3-12z" class="r"></path><path d="M286 243l5 2c3 2 4 5 5 9h0l-1-2c-2-1-3-3-5-3-2-1-3-3-4-5v-1z" class="V"></path><path d="M292 214l4 4h1c0 1 1 2 2 2v3h-1v1l-1-1-1 1c-1-1-1-1-2-1 0 0-1-2-1-3v-1l-1-1h1v-1c-1-1-1-2-1-3z" class="n"></path><path d="M293 220v-1l-1-1h1l4 4v1l-1 1c-1-1-1-1-2-1 0 0-1-2-1-3z" class="M"></path><path d="M277 212h1c0 2 1 9 0 11l-1 3c-1 2-1 4-2 6h0c-1 0-2-1-3-1h-1c0-1 2-3 2-4 3-3 2-6 3-9 1-2 1-3 1-6z" class="T"></path><path d="M275 226c1-1 1-2 2-3v-1l1 1-1 3c-1 1-2 0-2 0z" class="AC"></path><path d="M275 226s1 1 2 0c-1 2-1 4-2 6h0c-1 0-2-1-3-1l3-5z" class="J"></path><path d="M306 277h1c0 1 1 3 1 5 0 1 1 2 1 3l1 3v1 1l1-1v-2-3c-1-2-1-1-1-2v-1c0-2 0-3-1-5h0 1l1 7 1 7c1 3 1 8 2 11h-2l-3-13-3-11z" class="Z"></path><path d="M290 249c2 0 3 2 5 3l1 2h0c5 7 8 15 10 23l3 11c-1 1 0 1-1 1-4-14-8-28-18-40z" class="AC"></path><path d="M309 272h1c1 2 1 3 2 5l1-1v1h1l1 9 1 7v6l1 1-1 1h-1-1c-1-3-1-8-2-11l-1-7-1-7-1-4z" class="AS"></path><path d="M309 272h1c1 2 1 3 2 5l1-1v1 5h-1l-1 1-1-7-1-4z" class="b"></path><path d="M312 290c1-1 0-1 1-1 0 1 0 2 1 3h1c0-1 0-2-1-4 0-1 0-1 1-2l1 7v6l1 1-1 1h-1-1c-1-3-1-8-2-11z" class="AY"></path><path d="M299 220h1l1 3 1 7v2l1 4 1 8h0v1 1 1h0c-1-1-1-3-2-4h0c-1-1-3-7-3-8l-5-12c1 0 1 0 2 1l1-1 1 1v-1h1v-3z" class="AG"></path><path d="M299 220h1l1 3 1 7v2l-1-1c0-1-1-1-1-2h0-1l-2-5h-1l1-1 1 1v-1h1v-3z" class="U"></path><path d="M297 223l1 1v-1h1l1 4-3-3h-1l1-1z" class="b"></path><path d="M299 220h1l1 3 1 7-2-3-1-4v-3z" class="u"></path><path d="M273 190l8 6c2 1 4 3 7 4 2 0 5 2 7 2 3 0 6 1 9 0h2c-5 5-5 14-5 21l-1-3h-1c-1 0-2-1-2-2h-1l-4-4c-1-1-1-2-3-2h-1l-2-2s-2-2-2-3l-6-6v2c-1 1-1 2-1 3l-1 1c0-6-1-11-3-17z" class="S"></path><path d="M290 208l-2 1-1-1h1c0-1 1-2 1-3h1c1 1 2 2 2 4v1h-1c0-1-1-1-1-2z" class="u"></path><path d="M273 190l8 6v1l-1-1h-2l-3-3v1 1h1c1 0 1 2 2 3l1-1h1c-1 1-1 1-1 2-1 2 0 1-1 2v2c-1 1-1 2-1 3l-1 1c0-6-1-11-3-17z" class="Q"></path><path d="M290 208c0 1 1 1 1 2h1 1v1c1 1 3 2 3 3l1 1h0c0 1 1 2 1 3h0c1 1 1 1 2 1v-1 2h-1c-1 0-2-1-2-2h-1l-4-4c-1-1-1-2-3-2h-1l-2-2c1 0 1 0 2 1h2 0v-3z" class="J"></path><path d="M290 208c0 1 1 1 1 2s0 1 1 2c2 1 3 4 5 6h-1l-4-4c-1-1-1-2-3-2h-1l-2-2c1 0 1 0 2 1h2 0v-3z" class="Q"></path><path d="M325 328c1 1 2 1 4 1 1 2 1 2 3 2 2 3 5 7 6 11 1 2 2 4 3 7l2 3 2 2 2-1 1 1c0 1 1 2 1 2l1 9 1 2v1l3 3h1v1l2 1 4 1c-1 1-3 1-4 2-1 2-2 5-2 6l-2 8c-1 1-1 3-1 4l-1 3c-1 2-1 5-1 7 0 1 0 1-1 2v5h-2c0 1 1 2 1 3v3l1 5 2 6c0 1 1 3 1 4l3 2h0l1-1 2 4c-1 2-1 2-1 4h-1-7 0l-1 1h-2l-5-25c1 2 1 2 2 2v-1l-1-5h0l-21-67c1 0 1 0 1-1v-3c-1-2-1-5-1-8 2 4 2 9 4 13 0-3-1-6-2-9 1-1 1-2 1-2 0-3 0-5 1-8z" class="P"></path><path d="M352 432l3 2h0l1-1 2 4c-1 2-1 2-1 4l-1-1h0c-3-1-3-5-4-8z" class="q"></path><path d="M356 433l2 4c-1 2-1 2-1 4l-1-1c-1-2-1-4-1-6l1-1z" class="J"></path><path d="M341 417c1 2 1 2 2 2v-1l-1-5h0c4 8 6 17 7 26v2l-1 1h-2l-5-25z" class="o"></path><path d="M348 442v-2l1-1v2l-1 1z" class="R"></path><path d="M346 357c1 1 1 1 1 2l2 6c-1 1-1 2-2 2l-3 3-3 1c0 1-1 1-1 1-1 2-2 3-3 5 0 2-1 6 0 8 0 6 3 10 4 15 2 6 4 12 7 17l1 5-3-3c0-1-1-3-1-4l-1-2v-1c-1-2-1-3-2-5v-1-2c-1-1-1-1-1-2-1-1-1-3-2-5-3-6-6-16-3-23 1-2 3-3 4-4s1-3 2-4c2-3 4-3 4-8v-1z" class="w"></path><path d="M347 359l2 6c-1 1-1 2-2 2l-3 3-3 1c0 1-1 1-1 1 0-1 0-2 1-3s2-3 4-5l2-2v-3z" class="W"></path><path d="M349 365v4c1 2 2 3 2 4l2 2h2l2-2 4 1c-1 1-3 1-4 2-1 2-2 5-2 6l-2 8c-1 1-1 3-1 4l-1 3c-1 2-1 5-1 7 0 1 0 1-1 2v5h-2c0 1 1 2 1 3v3c-3-5-5-11-7-17-1-5-4-9-4-15-1-2 0-6 0-8 1-2 2-3 3-5 0 0 1 0 1-1l3-1 3-3c1 0 1-1 2-2z" class="B"></path><path d="M348 414c-2-2-3-6-4-8-1-4-3-8-4-12v-2c-1-2-2-7-2-9 1-3 0-5 2-7h0l2 2v1c-1 1-2 3-2 4v3l2 8c0 2 1 3 2 5 1 4 1 8 3 12 0 1 1 2 1 3z" class="H"></path><path d="M340 386c1 1 2 2 2 3 0 2 1 3 2 5h1l1 1 1-1 1 1c0 1 0 1-1 2v6h1v1c0 1 0 0 1 1v1 5h-2c-2-4-2-8-3-12-1-2-2-3-2-5l-2-8z" class="t"></path><path d="M342 379c2 0 4 0 5 1l1 1-1 3s0 1-1 1v9h-1-1c-1-2-2-3-2-5 0-1-1-2-2-3v-3c0-1 1-3 2-4z" class="M"></path><path d="M342 389v1c1 1 2 2 3 2v2h-1c-1-2-2-3-2-5zm1-4c1-1 1-2 2-3 0 1 1 2 1 3l-2 2-1-1v-1z" class="U"></path><path d="M349 365v4c1 2 2 3 2 4l2 2h2c-1 1-1 2-2 2l-1 2-1-1c-2 1-2 2-3 3l-1-1c-1-1-3-1-5-1v-1l-2-2h0l2-2c1 0 3-2 3-2v-1c0-1 2-3 2-4 1 0 1-1 2-2z" class="AG"></path><path d="M349 365v4 1c0 1 0 2 1 3-2 1-4 1-6 2l-2-1c1 0 3-2 3-2v-1c0-1 2-3 2-4 1 0 1-1 2-2z" class="t"></path><path d="M342 374l2 1-1 1v1h3c1 1 6-1 6-1l1 1-1 2-1-1c-2 1-2 2-3 3l-1-1c-1-1-3-1-5-1v-1l-2-2h0l2-2z" class="Q"></path><path d="M357 373l4 1c-1 1-3 1-4 2-1 2-2 5-2 6l-2 8c-1 1-1 3-1 4l-1 3c-1 2-1 5-1 7 0 1 0 1-1 2v-1c-1-1-1 0-1-1v-1h-1v-6c1-1 1-1 1-2l-1-1-1 1-1-1h1v-9c1 0 1-1 1-1l1-3c1-1 1-2 3-3l1 1 1-2c1 0 1-1 2-2l2-2z" class="j"></path><path d="M348 381c1-1 1-2 3-3l1 1-1 6-4-1 1-3z" class="t"></path><path d="M346 385c1 0 1-1 1-1l4 1c-1 3-2 8-2 11l2 1c-1 2-1 5-1 7 0 1 0 1-1 2v-1c-1-1-1 0-1-1v-1h-1v-6c1-1 1-1 1-2l-1-1-1 1-1-1h1v-9z" class="n"></path><path d="M349 396l2 1c-1 2-1 5-1 7 0 1 0 1-1 2v-1-9z" class="o"></path><path d="M325 328c1 1 2 1 4 1 1 2 1 2 3 2 2 3 5 7 6 11 1 2 2 4 3 7l2 3 2 2 2-1 1 1c0 1 1 2 1 2l1 9 1 2v1l3 3h1v1l2 1-2 2h-2l-2-2c0-1-1-2-2-4v-4l-2-6c0-1 0-1-1-2-3 0-4 2-6 4l-3 4-1 2-1 3-1 2s0 1-1 2l-4-13c-2-3-3-10-4-14 0-3-1-6-2-9 1-1 1-2 1-2 0-3 0-5 1-8z" class="M"></path><path d="M329 335c1 0 3 0 4 1 1 2 2 4 5 6 1 2 2 4 3 7l-1-1-2-2c-1-2-3-4-5-6-1-2-3-3-4-5z" class="J"></path><path d="M329 361c0-3-1-7-2-10s-1-7-1-10c-1-2-2-6-1-8 0-1 1-1 2-2 1 1 1 2 2 3h-3v5c0 1 1 3 1 4l4 17c1 4 1 8 3 10v2s0 1-1 2l-4-13z" class="Q"></path><path d="M325 328c1 1 2 1 4 1 1 2 1 2 3 2 2 3 5 7 6 11-3-2-4-4-5-6-1-1-3-1-4-1v-1c-1-1-1-2-2-3-1 1-2 1-2 2-1 2 0 6 1 8 0 3 0 7 1 10s2 7 2 10c-2-3-3-10-4-14 0-3-1-6-2-9 1-1 1-2 1-2 0-3 0-5 1-8z" class="q"></path><path d="M335 370c-2-2-2-5-2-8-2-6-4-12-4-18 0-1 0-2-1-3h0v-3h2c1 1 2 2 2 3l1 1 1 1v1l3 5c1 0 2-1 3-1l1 1 2 3-2-1c0 1-1 2-2 2h-3c-2 1-2 4-2 6 2 3 0 6 2 8l-1 3z" class="U"></path><path d="M334 359v-1c-1-1 0-1-1-2v-1c-1-3-2-6-1-8-1-2-1-3-1-4l1-1c0 3 2 5 4 7h1c1 0 2-1 3-1l1 1 2 3-2-1c0 1-1 2-2 2h-3c-2 1-2 4-2 6z" class="AE"></path><path d="M340 348l1 1 2 3-2-1c-2-1-2-1-4-1l-1-1h1c1 0 2-1 3-1z" class="AG"></path><path d="M334 359c0-2 0-5 2-6h3c1 0 2-1 2-2l2 1 2 2 2-1 1 1c0 1 1 2 1 2l1 9 1 2v1l3 3h1v1l2 1-2 2h-2l-2-2c0-1-1-2-2-4v-4l-2-6c0-1 0-1-1-2-3 0-4 2-6 4l-3 4-1 2c-2-2 0-5-2-8z" class="p"></path><path d="M351 373l1-2c-1 0-1-1-1-2v-1l3 3h1v1l2 1-2 2h-2l-2-2z" class="e"></path><path d="M334 359c0-2 0-5 2-6h3c1 0 2-1 2-2l2 1 2 2-1 1h-2c-1 1-2 1-2 2h-2v1c0 1 0 1-1 3v3 1l-1 2c-2-2 0-5-2-8z" class="AD"></path><defs><linearGradient id="BN" x1="351.565" y1="400.975" x2="377.61" y2="411.201" xlink:href="#B"><stop offset="0" stop-color="#090101"></stop><stop offset="1" stop-color="#270d0d"></stop></linearGradient></defs><path fill="url(#BN)" d="M361 374c3 0 6-1 8-2h0 2l1 1v1l-2 4c1 1 1 2 1 3v2 2l1 1 1-1v2l1 1h2v-2c4-1 2-2 4-4h2l2-2v-2c-1-2-1-2-1-4l4 10c2 5 3 11 6 15 1 0 1 0 2-1l6 12 2 4-1 1h-2l1 3v6 4l6 28v4h-1c-1-1-2-1-2-2v-2c-2-3-5-6-9-7-4-2-7-3-11-3h0l-5-1v1h-1l-1-1h-2c-3 1-6 2-9 2h-1c-5 1-10 1-15 1-1 0-3 0-4-1l1-3c1-1 1-1 3-2l-1-1h7 1c0-2 0-2 1-4l-2-4-1 1h0l-3-2c0-1-1-3-1-4l-2-6-1-5v-3c0-1-1-2-1-3h2v-5c1-1 1-1 1-2 0-2 0-5 1-7l1-3c0-1 0-3 1-4l2-8c0-1 1-4 2-6 1-1 3-1 4-2z"></path><path d="M360 419h0l2 7-1-1h0c-1-1-1-2-2-3h1v-3z" class="AL"></path><path d="M357 383c0-1 0-1 1-1l1-1c1-2 2-4 5-4-2 3-4 5-6 8-1 0-1-1-1-2z" class="B"></path><path d="M351 397l1-3 1 2c-1 3-1 6-1 9l-2-1c0-2 0-5 1-7z" class="AD"></path><path d="M362 417l2 3c0 2 0 2-1 3v1l1 1 1 4h0l-1 2-2-5-2-7 1-1 1-1z" class="n"></path><path d="M362 417l2 3c0 2 0 2-1 3 0-2-1-3-2-5l1-1z" class="B"></path><path d="M353 420l3 13-1 1h0l-3-2c0-1-1-3-1-4h2l-1-5v-2l1-1z" class="u"></path><path d="M351 428h2c0 2 1 4 2 6l-3-2c0-1-1-3-1-4z" class="S"></path><path d="M352 394c0-1 0-3 1-4l2-8c0 1 1 1 2 1 0 1 0 2 1 2l-5 11-1-2z" class="T"></path><path d="M350 404l2 1c0 5 0 10 1 15l-1 1c-1-3-1-6-3-10v-5c1-1 1-1 1-2z" class="r"></path><path d="M347 411h2c2 4 2 7 3 10v2l1 5h-2l-2-6-1-5v-3c0-1-1-2-1-3z" class="J"></path><path d="M348 414c0 2 1 2 1 4s2 3 3 5l1 5h-2l-2-6-1-5v-3z" class="t"></path><path d="M361 374c3 0 6-1 8-2h0l-3 3-2 2c-3 0-4 2-5 4l-1 1c-1 0-1 0-1 1-1 0-2 0-2-1s1-4 2-6c1-1 3-1 4-2z" class="y"></path><path d="M370 378c1 1 1 2 1 3v2 2c0 1-3 6-4 7h-3v4h-1c-1-1-1-3-1-4h0c0-3 4-10 6-11 0-1 1-1 2-2v-1h0z" class="B"></path><path d="M362 392h0c0-3 4-10 6-11 0-1 1-1 2-2v-1 3c0 1-1 2-1 3l-4 7c-1 1-2 1-3 1z" class="Y"></path><path d="M366 405v2l1 1h0c0 2 1 4 2 6h-2 0c0 2-1 3-2 4l-1 2-2-3-1 1-1 1h0c0-2-1-3-1-5v-1l1 1 1-2h1c0-2 2-5 4-7z" class="W"></path><path d="M360 414l1-2h1c0 1 0 2-1 3l-1-1z" class="y"></path><path d="M367 408h0c0 2 1 4 2 6h-2 0c0 2-1 3-2 4l-1 2-2-3c0-3 4-6 5-9z" class="S"></path><path d="M364 399h0v3l1 1v1l1 1c-2 2-4 5-4 7h-1l-1 2-1-1v1l-1-5h0c-1-1-2-4-1-6h3c1-2 2-3 4-4z" class="Y"></path><path d="M364 402l1 1v1l1 1c-2 2-4 5-4 7h-1l-1 2-1-1v1l-1-5h0c2 1 2 1 2 2v-2h0c0-3 3-5 4-7z" class="B"></path><path d="M365 404l1 1c-2 2-4 5-4 7h-1c0-2 0-4 1-6 1 0 2 0 3-2h0z" class="g"></path><path d="M364 420l1-2c1-1 2-2 2-4h0 2l1 3 1 3c0 1 0 0 1 1 0 2 1 3 1 5l4 9v1l-1 1-2 1-6 3v-1c-2-3-3-6-4-9l1-2h0l-1-4-1-1v-1c1-1 1-1 1-3z" class="t"></path><path d="M366 425l1-1c1 1 1 1 1 2l-1 1h0l-1-2z" class="n"></path><path d="M364 425l1-1c1 0 1 0 1 1l1 2-2 2-1-4z" class="T"></path><path d="M368 426c1 2 2 4 3 7l-3-2c0-1-1-3-1-4l1-1z" class="U"></path><path d="M367 427h0c0 1 1 3 1 4l1 3-2-1c0-1-1-3-2-4h0l2-2z" class="H"></path><path d="M368 431l3 2v1c1 1 1 2 1 3l-1 1-1-1c0-2 0-2-1-3l-1-3z" class="J"></path><path d="M371 434c1 1 2 2 3 4l-6 3v-1-1l2-2 1 1 1-1c0-1 0-2-1-3z" class="b"></path><path d="M364 431l1-2c1 1 2 3 2 4l2 1c1 1 1 1 1 3l-2 2v1c-2-3-3-6-4-9zm7-46l1 1v1l1 4v1h1l1 3 1 3h0-1c0 2-1 5-1 7 0 3 0 4-1 6-1 1-2 1-3 1s-1-1-1-2l-1-1v-1h-1 0l-1-1v-2l-1-1v-1l-1-1v-3h0l-1-3h1v-4h3c1-1 4-6 4-7z" class="U"></path><path d="M373 392h1l1 3c-1 1-1 2-1 3l-1-2c0 1-1 1-1 2v2c0 1 0 1-1 2v1h-1c-1 1-2 2-2 3h-1c-1 0 0 0-1 1v-2l-1-1v-1-1c1 0 1 1 2 2 3-2 3-5 5-8l1-1v-3z" class="n"></path><path d="M373 391v1 3l-1 1c-2 3-2 6-5 8-1-1-1-2-2-2v1l-1-1v-3h1c0 1 1 2 1 2h1c0-2 1-4 2-6 0-1 0 0 1-1 0-1-1-1 0-1l3-2z" class="H"></path><path d="M371 385l1 1v1l1 4-3 2c-1 0 0 0 0 1-1 1-1 0-1 1-1 2-2 4-2 6h-1s-1-1-1-2h-1 0l-1-3h1v-4h3c1-1 4-6 4-7z" class="W"></path><path d="M372 387l1 4-3 2 1-1c0-2 0-3 1-5z" class="S"></path><path d="M364 396v-4h3c-1 0-1 1-1 2s0 2-1 3c-1 0 0 0-1-1z" class="w"></path><path d="M376 398h1c1 1 1 1 1 3l1 2 1 1v1l1 1v1c0 1 1 3 0 4l2 2c0 1 1 2 1 3-1 3 1 4 1 6 0 1-1 3-1 4-1 2-3 4-4 7h-1l-1-2h-1v4l-4-9c0-2-1-3-1-5-1-1-1 0-1-1l-1-3-1-3c-1-2-2-4-2-6h1v1l1 1c0 1 0 2 1 2s2 0 3-1c1-2 1-3 1-6 0-2 1-5 1-7h1 0z" class="AG"></path><path d="M372 421c1 1 1 2 2 4l1 1h3l1 2v5l-1-2h-1v4l-4-9c0-2-1-3-1-5z" class="r"></path><path d="M383 374l4 10c0 2-1 3 0 4 0 3 0 6 1 8l1 1c0 2-1 4 0 6 0 1 0 0-1 1v8c-1 3-1 5-2 8l-2-2v-1-1c0-1-1-2-1-3l-2-2c1-1 0-3 0-4v-1l-1-1v-1l-1-1-1-2c0-2 0-2-1-3h-1l-1-3-1-3h-1v-1l-1-4v-1l1-1v2l1 1h2v-2c4-1 2-2 4-4h2l2-2v-2c-1-2-1-2-1-4z" class="AE"></path><path d="M374 392c1 0 1 1 2 1 1 1 3 1 4 2h1c1 0 1 0 2-1l1 1-1 2v1c1 1 2 2 2 4 0 1 1 2 2 3 0-1 1-1 1-2v-7l1 1c0 2-1 4 0 6 0 1 0 0-1 1v8c-1 3-1 5-2 8l-2-2v-1-1c0-1-1-2-1-3l-2-2c1-1 0-3 0-4v-1l-1-1v-1l-1-1-1-2c0-2 0-2-1-3h-1l-1-3-1-3z" class="AP"></path><path d="M381 406l2 1v6l-2-2c1-1 0-3 0-4v-1z" class="AD"></path><path d="M386 420c1-3 1-5 2-8v-8c1-1 1 0 1-1-1-2 0-4 0-6 0 2 1 3 1 5h1c3 4 2 12 3 16 0 3 0 7-1 10-2 0-2 2-3 4h0l-1 3c-3 4-7 7-12 10h-2c-3 1-6 2-9 2h-1c-5 1-10 1-15 1-1 0-3 0-4-1l1-3c1-1 1-1 3-2l-1-1h7 1c0-2 0-2 1-4 0 2 1 3 0 5 4 0 7-1 10-1l6-3 2-1 1-1v-1-4h1l1 2h1c1-3 3-5 4-7 0-1 1-3 1-4 0-2-2-3-1-6v1 1l2 2z" class="j"></path><path d="M384 416v1 1l2 2v7l-1 1c0 1 0 2-1 2h0 0l-2 2c0-1 2-4 2-6 0-1 1-3 1-4 0-2-2-3-1-6z" class="e"></path><path d="M379 433h1c1-3 3-5 4-7 0 2-2 5-2 6l-1 2-2 2h-1l1-1c-2 1-1 2-3 2l1-1v-1-4h1l1 2z" class="AD"></path><path d="M381 434l1 2c-2 3-4 6-7 7l-5 1c-2 1-3 2-4 3h-1c-5 1-10 1-15 1-1 0-3 0-4-1l1-3c1-1 1-1 3-2l-1-1h7 1c0-2 0-2 1-4 0 2 1 3 0 5 4 0 7-1 10-1l6-3 2-1c2 0 1-1 3-2l-1 1h1l2-2z" class="AP"></path><path d="M357 441c0-2 0-2 1-4 0 2 1 3 0 5h0l1 1c-2 1-6 0-8 1l2 2c3 1 8 0 12 1-5 1-10 1-15 1-1 0-3 0-4-1l1-3c1-1 1-1 3-2l-1-1h7 1z" class="e"></path><path d="M357 441c0-2 0-2 1-4 0 2 1 3 0 5h0-8l-1-1h7 1z" class="n"></path><path d="M387 384c2 5 3 11 6 15 1 0 1 0 2-1l6 12 2 4-1 1h-2l1 3v6 4l6 28v4h-1c-1-1-2-1-2-2v-2c-2-3-5-6-9-7-4-2-7-3-11-3h0l-5-1v1h-1l-1-1c5-3 9-6 12-10l1-3h0c1-2 1-4 3-4 1-3 1-7 1-10-1-4 0-12-3-16h-1c0-2-1-3-1-5l-1-1c-1-2-1-5-1-8-1-1 0-2 0-4z" class="J"></path><path d="M389 438l2 1-2 3-1-3 1-1z" class="AA"></path><path d="M390 432h0c1-2 1-4 3-4 0 3-1 6-2 10v1l-2-1 1-3h-1l1-3z" class="a"></path><path d="M390 432l1 1v2h-1-1l1-3z" class="R"></path><path d="M389 435h1l-1 3-1 1 1 3-5 4h0l-5-1v1h-1l-1-1c5-3 9-6 12-10z" class="AO"></path><path d="M379 445c1 0 2 0 3-1h3c1-2 2-3 3-5l1 3-5 4h0l-5-1z" class="R"></path><path d="M387 384c2 5 3 11 6 15 1 0 1 0 2-1l6 12 2 4-1 1h-2l1 3v6 4l-1-3c0-2-2-4-2-6 0-1 0-1-1-2 0-2 0-3-1-4v-1-1c-1-1-1-4-2-4v3c0 1 1 1 1 2-1 2-1 3-1 5v1c-1-4 0-12-3-16h-1c0-2-1-3-1-5l-1-1c-1-2-1-5-1-8-1-1 0-2 0-4z" class="e"></path><path d="M393 399c1 0 1 0 2-1l6 12 2 4-1 1h-2 0l-7-16z" class="AJ"></path><path d="M400 415c1-1 0-2 0-3l1-2 2 4-1 1h-2 0z" class="AT"></path><path d="M387 388c1 2 2 4 2 6l-1 2c1 0 2 0 2 1 2 1 1 2 2 3 1 2 1 2 2 3l1 2c1 2 2 5 3 8 1 1 1 2 1 3v1c1 3 1 5 2 7v4l-1-3c0-2-2-4-2-6 0-1 0-1-1-2 0-2 0-3-1-4v-1-1c-1-1-1-4-2-4v3c0 1 1 1 1 2-1 2-1 3-1 5v1c-1-4 0-12-3-16h-1c0-2-1-3-1-5l-1-1c-1-2-1-5-1-8z" class="r"></path><path d="M304 586c9-5 17-9 27-11 15-3 30 1 42 10h0 1c1 2 2 4 3 7l4 9 7 18 1 3 8 20 4 9 2 5 5 12c1 2 2 4 2 6 0 3-1 6-2 9 2 2 3 2 5 3 1 1 2 4 4 5l4 10c0 2 1 3 1 5l4 9 3 5v1l2 4v2c2 5 5 10 5 14 3 5 4 10 6 14l1 3 3 8 1 4 9 21c1 4 3 8 5 12l3 10 2 5 10 24c-8 3-15 7-21 12l-2 2-12 16-3 4c2 0 2 0 4-1l-2 2c-1 1-1 1-1 2l-1 1h-2l-1 1h-1-1c1-3 1-9 0-12 0-2-1-2-1-4-1-3-2-7-4-10v-1s-1-1-1-2c-1-1-2-2-2-3-1-1-2-2-2-3-2 0-1 0-2-1-1 0-1-1-2-2l-1 1-1-2v1l-3-3c-1 0-1 0-2-1-1 0-4-1-5-2h-2c-1-1-1-1-2-1h-2c-1-1-1-1-2-1h-2c-2-1-2-1-3-1h-1-1 0-1 0-2 0c-6-1-11 1-17 1-2 0-7 1-9 0-2 0-2-1-3-3l-1 1c0-1-1-3 0-4 0-2 1-3 2-4l1-1 2-2c4-1 8-3 12-4 8-1 15 0 22 1l1-1h-1-3c-2-2-5 0-7-1h-2c-2 1-5-1-7 0-1 0-2 0-3 1h-2l-3 1h-1c-1 1-2 1-3 1-1 1-2 1-3 2-1 0-2 1-3 1l1-1c0-1 0-1 1-2v-2c1-1 1-2 1-3s0-2 1-3v-3c1-2 1-8 1-10-1-2-1-5 0-8h-1v3l-1-1v-9l-1-4h-1c0-1-1-1-2-2-1 0-1 0-1-1l-2-8c-4-12-10-22-22-27-8-4-18-6-27-5-8 1-15 3-22 6l-3 1c-8 4-15 9-21 16-2 2-4 5-6 7h-4l-3 6c-1 6-4 12-4 18 0 3 0 6 1 8l-1 1v2h0-1c0-2 0-2-1-3 0-1 0-2-1-4v-1c-1-2-1-8 0-10v-3c0-1 0-2 1-3v-3c0-1 1-2 1-3h0c1-1 1-3 2-4 0-1 0-2 1-3l1-5v-2c1-1 1-2 2-3 1-5 4-10 6-14l2-2 4-8c1-1 1-2 2-3l1-2s1-1 1-2c1-1 1 0 2-1l2-4 6-8c0-1 1-2 1-3l2-2c1-2 2-4 3-5l1-2 1-2 1-1v-2c1-1 1 0 1-1l6-11v-1c0-1 1-2 1-3h0l1-2c0-1 1-2 1-3 0-2 1-3 1-4h0l-1 2v-4h0l1-1v-1c0-1 0-1 1-2 0-1 0-3 1-4v-2c0-1 1-3 1-4s0-2 1-3v-1-2c1-2 0-3 0-4v-4-1-3c-1-1-1-7 0-8 0-1 0-1 1-2v-1h2c1-1 3-1 4-2l-1-3c0-1-1-5-2-6-1 0-7 0-9-1-1 0-2 1-3 1-5 2-9 3-13 6-1-3 0-7 2-10v2-2l1-1c0-1 0-1 1-1l6-3v-1c2 0 2-1 3-2 1 0 2-1 2-2h2l2-2h1c0-1 1-1 2-1v3h0v3h1c1-1 0-1 0-3h0c-1-2 0-4 1-5 0-1 1-3 1-4s0-1 1-2l-1-2z" class="z"></path><path d="M369 680v-1h0v-1-1h1v-1-1c0-1 0 0 1-1v-2c-1-1 0-1-1-2l2-2c0 3 0 5-1 7v2c-1 1-1 2-2 3zm72 162h0c1 1 1 2 1 4l1-1h0l-2 6v2c-1-2 1-9 0-11zm-77-117c3 1 6 2 9 4h-4-1c-1-1-1-1-2-1v-1c-2 0-2-1-2-2z" class="h"></path><path d="M325 692h0v1l1 1h0l-3-1h-1c-1-1-1-3-1-4l1-1 1 1 2 3z" class="m"></path><path d="M436 880c1-1 1-3 2-5 0-2 1-4 1-7l1 4v-1l1 1-3 4c2 0 2 0 4-1l-2 2c-1 1-1 1-1 2l-1 1h-2z" class="C"></path><path d="M400 816h2 2l3 1c1 0 2 0 3 1h5c2 1 10 0 13 1h0-1c-4 0-8 1-11 0h-6-1-2-1-1v-1c-1-1-1-1-2-1-1-1-2 0-3-1z" class="h"></path><path d="M376 731c0 1 0 1-1 2s-1 2-2 4l-1 2c0 2-1 4-1 6l-1 1c0-3-1-6 0-9s3-5 6-6z" class="G"></path><path d="M279 619c-1-3 0-7 2-10v2c0 1-1 1-1 3h0v2l1 1c1-1 2-1 3-2v-1h1v-1c1 0 2-1 3-1h1c1-1 1-1 2-1 0-1 0-1 1-1h1 1c0 1-1 1-1 2l-1 1c-5 2-9 3-13 6z" class="K"></path><path d="M315 631c1 1 2 1 2 3h1v2 2h0 0l-1-2-1-1h-1-5c-2-1-3-1-4-1s-3 0-4-1c5 0 9 0 13-2z" class="D"></path><path d="M289 700h0 1 0l1 1-2 2-1 2-1 3h-1c-1 3-4 5-6 7h-1c0-3 2-4 3-5l1-1 3-5h1v-1c1-1 1-2 2-3z" class="h"></path><path d="M371 754l3 11 5 9h0c1 1 1 2 2 3l1 1v1c1 1 2 3 3 4v1c-2-1-3-3-4-4 0-2-2-3-3-5s-2-5-4-7h0c1 2 1 3 1 4v1c-3-6-3-13-4-19z" class="X"></path><path d="M369 601h0c2 0 4 2 5 4l1 3c1 0 1 2 1 3v-1h0c1-2 1-3 1-5h0c0 1 1 2 0 2v1l1 1c-1 1-1 1-1 3-1 0 0 1 0 2-1 1-1 1-1 3l-1 1v1l-2 3-1 1h-1v-1c2-4 4-8 4-12l-1-1v-2l-1-1c0-1-1-2-2-4l-1 1-1-2z" class="D"></path><path d="M366 783h1c0 1 1 3 1 5l1 3 2 6h0v1l1 1v2l3 6h0v1s-1-1-1-2h-1c0-1-1-2-1-3l-1-5h-1v-2-1c-1-1-1-1-1-2l-1-1c1-1 0-2 0-3l-1-1h0v5l1 1v3 1l1 2c0 1 0 1 1 2v2h0l1 1h-1 0c0-1-1-2-1-3-1-1-1-1-1-2v-1c-1-2-1-3-1-4v-3c-1-1-1-2-1-3-1-1 0-5 0-6z" class="h"></path><path d="M353 626c1-1 3-1 4-1 1 1 2 1 3 1h0l-1 1h-3c-1 0-2 1-3 1h0-4c-1 1-3 1-4 1h-1-3c-1 1-1 1-2 1h-1-2c1-1 1-2 3-2h0c1-1 1-1 2-1h3c1-1 3-1 5-1 1-1 3 0 4 0z" class="AX"></path><path d="M302 633h-1v1h0l-1-1c0-1 0-2 1-3 2-1 6-2 8-2 3 0 4 1 6 3-4 2-8 2-13 2z" class="N"></path><path d="M340 662c1 0 3 0 4-1-2 3-9 6-12 8l-6 4h-1 0-1c0-1 1-1 1-2 4-5 9-7 15-9z" class="v"></path><path d="M325 671c4-5 9-7 15-9l-2 2c-4 2-8 6-13 7z" class="AB"></path><path d="M371 750c2 4 3 11 5 14 1 2 2 5 4 7l6 9 5 7 2 2h0c-3-2-7-7-9-10h0l-6-9c0 1 1 2 1 3v1h0l-5-9-3-11c-1-2-1-2 0-4z" class="K"></path><path d="M242 795c0-1 0 0 1-1v2-1c1-4 1-7 1-10 1-2 0-4 0-5 1-3 0 0 0-2 1-1 0-1 1-1v-3l1-2v-2l1-3 2-5c0-2 1-4 2-6 1-3 3-7 5-10l1-2 1-2 1-2c1-1 2-2 3-4 1-1 2-1 2-2 2-4 6-7 9-10l1 1c-6 5-12 11-16 18-8 14-13 28-14 43 0 5 0 10 1 14v2h0-1c0-2 0-2-1-3 0-1 0-2-1-4z" class="AX"></path><path d="M304 711c7-2 15-2 22 0v1h1c-5 0-11-1-16 1h-2c-6 0-12 2-18 4h0c-3 1-5 1-7 3-2 0-3 1-4 1h-1c4-3 8-5 13-7 4-1 8-3 12-3z" class="k"></path><path d="M292 714c4-1 8-3 12-3h1c-1 1-2 1-2 1h1c0 1-10 3-11 3 0 0-1 0-1-1h0z" class="i"></path><defs><linearGradient id="BO" x1="268.49" y1="727.123" x2="271.08" y2="733.55" xlink:href="#B"><stop offset="0" stop-color="#9e9aa0"></stop><stop offset="1" stop-color="#b1b3b4"></stop></linearGradient></defs><path fill="url(#BO)" d="M291 717h3c-6 2-14 5-18 10v1l-3 3c0 2-3 3-4 4v1c-1 1-3 2-4 3v-1-1h0c-2 0-2 1-3 2l-1 1v1h-1v1l-1 1v1l-1-1c4-7 10-13 16-18l5-4h1c1 0 2-1 4-1 2-2 4-2 7-3z"></path><path d="M451 846c1 1 1 6 1 9l1 1-12 16-1-1v1l-1-4c3-9 6-15 12-22z" class="I"></path><path d="M353 719l-1 1v-1c-1 0-2-1-2-1-1-1-1-1-2-1s-1 0-2-1h-1c-1 0 0 0-1-1-1 0-2-1-3-1-2-1-5-3-7-3-2-1-3-1-4-2h0-1l1-1v-1c1 0 1-1 2-2v-1l2-1h2c1 0 1 0 3-1l-5 4h1c3 1 5-1 8-2l1-1 2 2v2c1 2 1 2 1 5 1 2 4 4 6 7z" class="K"></path><path d="M334 706h1c3 1 5-1 8-2l1-1 2 2v2c1 2 1 2 1 5-1-1-3-5-4-5 0 0-7 3-9 2-1 0-2-1-3-2 1 0 2-1 3-1z" class="X"></path><path d="M340 685l5-1 3-1h2l1-1h2l1-1h3c3-2 5-5 8-7v-1c1-1 1-1 1-2h1l2-4 1-2v-1c1-1 2-1 3-2v1 3h-1c-1 1-2 2-2 4l-2 2s0 1-1 2l-6 6c-1 0-1 0-2 1s0 1-1 1l-3 2h-1c-2 1-3 1-4 2l-4 1h-2l-1 1h-2l-6 1h-1c-1 0-2 1-3 1h0-1 0c-1 0-2 0-3 1v-1c-1 1-1 1-1 2h-1l-2-3c1 0 3-1 4-2l1-1 6-1c2 1 4 0 6 0z" class="D"></path><path d="M328 686l6-1c2 1 4 0 6 0-4 2-10 1-13 5-1 1-1 1-1 2h-1l-2-3c1 0 3-1 4-2l1-1z" class="X"></path><defs><linearGradient id="BP" x1="354.579" y1="704.469" x2="355.647" y2="724.42" xlink:href="#B"><stop offset="0" stop-color="#b7b5b6"></stop><stop offset="1" stop-color="#e8e7e4"></stop></linearGradient></defs><path fill="url(#BP)" d="M346 705c1 1 3 1 4 0h3c1-1 2-2 4-3h1l-1 1 1 3v4 3l1 2 1 3c1 2 2 4 4 6l-1 1c-1 0-2 0-3-1h0c-2-1-3-2-5-4-1 0-1-1-2-1-2-3-5-5-6-7 0-3 0-3-1-5v-2z"></path><path d="M346 705c1 1 3 1 4 0h3c1-1 2-2 4-3h1l-1 1c-1 1-2 2-2 3l-1-1h0c-1 1-2 1-3 1l-2 2c-1-1-2-1-3-1v-2z" class="G"></path><path d="M276 728l5-3c16-9 33-12 51-7 5 2 9 5 14 6h0 0c2 1 4 1 5 3h1c0 1 1 1 1 2h0v1l-3-2c-1 0-1 0-1-1h-1c-2 0-1 0-2-1h-2c1 1 2 1 3 1l3 3h1c1 1 2 1 3 2 0 1 1 1 1 2l-2-2c-6-4-13-8-20-10-2-1-6-1-8-1l-1-1h-2v-1h-2c-1-1-1-1-2-1h-1c-1 0-2 0-3-1-3 0-10-1-12 1h-2c-1 0-2 1-2 1-2 1-3 1-4 2l-7 3h-1l-1 1c-3 2-6 3-9 5-1 0-2 1-3 1l3-3z" class="D"></path><path d="M259 744l-1 1c0 1-1 1-1 2v1h0c-1 1-1 2-2 3h1c0 1 0 1 1 2 0 1 0 0 1 1 2-4 4-6 7-9h0c1-1 3-3 4-3 1-1 1-2 3-2h0c-3 2-6 5-9 8-1 2-2 3-3 5v1l-1 1-1 1-5 8-1 3-3 6c-1 6-4 12-4 18 0 3 0 6 1 8l-1 1c-1-4-1-9-1-14 1-15 6-29 14-43l1 1z" class="I"></path><path d="M367 832c3-1 6-2 10-2 12-2 25-1 36 5 8 4 14 10 19 17h1l1 3c1 3 2 8 1 11v-3l-1-1v4c0-6-2-11-6-15-10-15-29-21-46-20l-13 2h-7v-2c2 1 2 1 4 1h1z" class="N"></path><path d="M367 832c3-1 6-2 10-2 12-2 25-1 36 5 8 4 14 10 19 17h1l1 3h-1c-1 0 0 0-1-1-2-3-4-6-7-9-11-11-26-16-42-15-5 0-10 2-16 2h0z" class="X"></path><path d="M375 773v-1c0-1 0-2-1-4h0c2 2 3 5 4 7s3 3 3 5c1 1 2 3 4 4 4 5 8 11 13 15 3 5 9 7 14 9 0 0 1 0 2 1 3 2 10 2 14 2h-12c-5-1-9-3-14-5-12-7-23-20-27-33z" class="C"></path><path d="M398 799c4 2 7 4 11 5 2 1 4 1 6 2 3 0 7 0 10-1h3l5-1c4-1 16-7 19-10v-1h0l1 1c-6 8-15 15-25 17-4 0-11 0-14-2-1-1-2-1-2-1-5-2-11-4-14-9z" class="N"></path><path d="M369 681v4h0c-1 2-2 3-2 4l-1 2-5 6-2 5h-1 0-1c-2 1-3 2-4 3h-3c-1 1-3 1-4 0l-2-2-1 1c-3 1-5 3-8 2h-1l5-4c1 0 3-1 5-2s5-2 7-4c7-4 13-9 18-15z" class="k"></path><path d="M344 700v1c-3 1-5 3-8 4 1 0 2 1 3 0h1v-1h1l1-1c1 0 1 0 2-1h2l1-1h0c2 1 3 0 4 0 1-1 2-1 2-1 2-1 3-2 5-3 1 1 0 2 0 3v2h0-1c-2 1-3 2-4 3h-3c-1 1-3 1-4 0l-2-2-1 1c-3 1-5 3-8 2h-1l5-4c1 0 3-1 5-2z" class="E"></path><path d="M344 703h1l2 1c2 0 2 0 4-1h1l1 1c1-2 4-3 5-4v2h0-1c-2 1-3 2-4 3h-3c-1 1-3 1-4 0l-2-2zm-18 8c2 1 5 2 7 3 7 3 15 8 21 12v1l-1-1h0l-1-1h-1 0l-2-1-1-1h0-1c0-1 0-1-1-1h0c-2-1-3-1-4-1h0c1 0 1 1 1 1h1l2 2h0c-5-1-9-4-14-6-18-5-35-2-51 7l-5 3v-1c4-5 12-8 18-10h-3 0c6-2 12-4 18-4h2c5-2 11-1 16-1h-1v-1z" class="O"></path><path d="M326 711c2 1 5 2 7 3h-3 0c-10 0-19-1-28 1-3 1-6 1-8 2h-3 0c6-2 12-4 18-4h2c5-2 11-1 16-1h-1v-1z" class="L"></path><path d="M362 824l2-2v6c1 1 1 1 2 1 3 1 9-2 13-3h0c12-2 21 0 32 4 2 1 5 2 7 4h0c4 3 6 7 11 9v-1c1 1 2 2 2 4 1 2 2 4 2 6h-1c-5-7-11-13-19-17-11-6-24-7-36-5-4 0-7 1-10 2h-1c-2 0-2 0-4-1l-1-6 1-1z" class="F"></path><path d="M361 825l1-1c0 2 1 3 1 6 1 0 1 1 2 1 2 0 4-1 6-1l10-2c14-2 31 2 42 12 1 1 6 6 7 6h1 0c1 2 2 4 2 6h-1c-5-7-11-13-19-17-11-6-24-7-36-5-4 0-7 1-10 2h-1c-2 0-2 0-4-1l-1-6z" class="I"></path><path d="M306 592v-1c5-6 14-10 22-11l1-1c8-1 18-2 26 1h1c3 1 7 3 10 5l2 1s1 0 2 1 2 2 3 2l4 3 4 9h-1c-1 0-1-1-1-2-1-1-1-2-2-3h0c0-1 0 0-1-1-2 0-5-3-7-4-1-1-2-1-3-2-1 0-2-1-3-2h-1s-1 0-2-1-2-1-3-1l-1-1c-2-1-6-2-8-3-1 0-1 0-2-1h-2c-1-1-7-1-8-1-2 1-3 1-4 1h-4l-2 1-3 1h1c-1 1-2 1-3 1-1 1-1 2-3 3-1 1-1 0-2 2l-4 4h-1c-1 2-3 4-5 5v1c-1 1-1 1-1 2l-1 1h0v1c-1 1-1 2-1 4v1h0c-1-2-2-4-2-5l-1-1h-1v-1h1l1-1h0v3h1c1-1 0-1 0-3h0c-1-2 0-4 1-5 1 0 2-1 3-2z" class="X"></path><path d="M303 594c1 0 2-1 3-2-2 2-3 5-4 7h0c-1-2 0-4 1-5z" class="G"></path><path d="M304 586c9-5 17-9 27-11 15-3 30 1 42 10h0 1c1 2 2 4 3 7l-4-3c-1 0-2-1-3-2s-2-1-2-1l-2-1c-3-2-7-4-10-5h-1c-8-3-18-2-26-1l-1 1c-8 1-17 5-22 11v1c-1 1-2 2-3 2 0-1 1-3 1-4s0-1 1-2l-1-2z" class="F"></path><defs><linearGradient id="BQ" x1="456.004" y1="827.187" x2="465.496" y2="847.313" xlink:href="#B"><stop offset="0" stop-color="#bab8b9"></stop><stop offset="1" stop-color="#dcdbdc"></stop></linearGradient></defs><path fill="url(#BQ)" d="M466 818l10 24c-8 3-15 7-21 12l-2 2-1-1c0-3 0-8-1-9 1-2 3-3 5-4 0-1 0-1 1-1h1c5-5 8-16 8-23z"></path><path d="M456 842l-1 1c0 3-3 7-1 10l1 1-2 2-1-1c0-3 0-8-1-9 1-2 3-3 5-4z" class="X"></path><path d="M355 658h0c1-1 1-1 1-2l1-1h1 0v-2h0c0 5 1 7-2 11s-7 4-11 6c-2 0-3 1-5 2l-2 2c0 1 0 0-1 1l-1 1c-3 3-7 5-8 10l-1 1c-1 1-3 2-4 2l-1-1v-1c-1-2-1-4-1-6 0-3 1-5 3-8h1 0 1l6-4c3-2 10-5 12-8 1-1 5-1 7-1l4-2z" class="O"></path><path d="M326 676l-4 11c-1-2-1-4-1-6v1h1c1-3 2-4 4-6h0z" class="F"></path><path d="M324 673h1 0 1l6-4c-2 2-3 3-4 5l-2 2h0c-2 2-3 3-4 6h-1v-1c0-3 1-5 3-8z" class="i"></path><path d="M351 660c-1 1-1 2-3 3-4 2-8 3-12 5-3 2-6 4-8 6 1-2 2-3 4-5 3-2 10-5 12-8 1-1 5-1 7-1z" class="L"></path><path d="M355 658h0c1-1 1-1 1-2l1-1h1 0v-2h0c0 5 1 7-2 11s-7 4-11 6c-2 0-3 1-5 2l-2 2c0 1 0 0-1 1l-1 1c-3 3-7 5-8 10l-1 1-2-1v-1c1-9 11-15 18-18 3-1 7-2 10-4 1-1 3-2 3-4l-1-1z" class="D"></path><defs><linearGradient id="BR" x1="391.923" y1="634.026" x2="365.318" y2="655.284" xlink:href="#B"><stop offset="0" stop-color="#d9d7d7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#BR)" d="M381 601l7 18 1 3 8 20 4 9-11-1c0-1-3-1-5-1-7 0-14 0-22-2v-1h-1 0c-1 0-2 0-2-1h-1l-1 1c-1-1-1-1-1-2l-1-1v-1c2 1 4 1 6 1l2-1h6c1-1 1-1 3-1h2c1-1 3 0 4-1h1 1c1-1 3-2 4-2h1l-2-1c1 0 1 0 2-1l3-1h1 1 1v-1h0c0-2 0-3-1-4l-1-1h1l-2-4c-1-1-1-1-1-2v-2c-1 0-1 0-1-1l-1-1v-3l-1-1c0-1-1-1-1-3h0c-1-1-1-1-1-2s-1-1-1-3v-1l-2-5h1z"></path><defs><linearGradient id="BS" x1="398.607" y1="819.162" x2="396.127" y2="838.306" xlink:href="#B"><stop offset="0" stop-color="#81777b"></stop><stop offset="1" stop-color="#aea3a3"></stop></linearGradient></defs><path fill="url(#BS)" d="M364 822c4-1 8-3 12-4 8-1 15 0 22 1 4 1 8 2 11 4 8 4 16 11 20 19h0v1c-5-2-7-6-11-9h0c-2-2-5-3-7-4-11-4-20-6-32-4h0c-4 1-10 4-13 3-1 0-1 0-2-1v-6z"></path><path d="M379 826c0-1 0 0 1-1s3-1 5-2h-2c11-3 22 1 31 6v1h-3c-11-4-20-6-32-4z" class="L"></path><defs><linearGradient id="BT" x1="435.746" y1="766.836" x2="421.278" y2="800.193" xlink:href="#B"><stop offset="0" stop-color="#bab8b9"></stop><stop offset="1" stop-color="#e7e6e5"></stop></linearGradient></defs><path fill="url(#BT)" d="M435 761h0l4-6h1l1 2 1-2 1 3 3 8 1 4 9 21c1 4 3 8 5 12l3 10-2-2c0-2-1-5-2-7l-1-1v-1c-1-1-1-3-2-4 0-1 0-1-1-2h0v-1 1 1 3h-1v-1 2h-1v-6-1c0-2 0-4-1-6v-1h0l-1-1v-3h-1c0-2 0-2-1-3-2 2-4 5-6 7-4 3-11 5-17 6-3 1-6 1-9 2h0-8c-1 0-2-1-3-1h-1c4-1 13-9 15-12 2-2 4-5 5-8l4-6 3-3 2-4z"></path><path d="M437 773c3-2 6-5 9-7l1 4-18 13v-2h-1 0v-1c2-1 4-3 6-4l3-3z" class="K"></path><defs><linearGradient id="BU" x1="434.994" y1="762.164" x2="439.006" y2="769.836" xlink:href="#B"><stop offset="0" stop-color="#848186"></stop><stop offset="1" stop-color="#a19ea0"></stop></linearGradient></defs><path fill="url(#BU)" d="M435 761h0l4-6h1l1 2 1-2 1 3 3 8c-3 2-6 5-9 7l-5 2-1-1-1 1v-3c1-2 2-4 3-7l2-4z"></path><path d="M442 755l1 3c-1 1-2 2-4 2l2-3 1-2z" class="E"></path><path d="M435 761h0l4-6h1l1 2-2 3c-3 5-6 9-8 14l-1 1v-3c1-2 2-4 3-7l2-4z" class="X"></path><path d="M360 654l-1-8h0c1 0 2 1 3 1h1c8 2 15 2 22 2 2 0 5 0 5 1l11 1 2 5c-7-2-15-3-21 0h0c-4 2-7 6-8 9v1h-1v-3-1c-1 1-2 1-3 2v1l-1 2-2 4h-1c0 1 0 1-1 2v1c-3 2-5 5-8 7h-3l-1 1h-2l-1 1h-2l-3 1-5 1c-2 0-4 1-6 0l-6 1c1-5 5-7 8-10l1-1c1-1 1 0 1-1l2-2c2-1 3-2 5-2 4-2 8-2 11-6v1c1-1 2-1 2-3h0c1-1 1-1 1-2v-1c0-2 0-3 1-5z" class="E"></path><path d="M341 682h1c5 0 9-2 14-4 2-1 5-4 7-6l4-6 1-1c0-2 2-4 4-5l7-5c-2 2-4 4-5 6v1 1h-1v-1c-1 1-2 1-3 2v1l-1 2-2 4h-1c0 1 0 1-1 2v1c-3 2-5 5-8 7h-3l-1 1h-2l-1 1h-2l-3 1-5 1c-2 0-4 1-6 0l7-3z" class="I"></path><defs><linearGradient id="BV" x1="363.147" y1="644.715" x2="371.13" y2="655.713" xlink:href="#B"><stop offset="0" stop-color="#aca9aa"></stop><stop offset="1" stop-color="#cac8c9"></stop></linearGradient></defs><path fill="url(#BV)" d="M360 654l-1-8h0c1 0 2 1 3 1h1c8 2 15 2 22 2 2 0 5 0 5 1-3 0-6 0-10 1h-1c-6 2-11 7-15 12-1 0-2 0-3-1 0-1-1-6-1-8z"></path><path d="M360 654c0 2 1 7 1 8 1 1 2 1 3 1-3 4-6 8-9 11-4 3-9 5-14 8l-7 3-6 1c1-5 5-7 8-10l1-1c1-1 1 0 1-1l2-2c2-1 3-2 5-2 4-2 8-2 11-6v1c1-1 2-1 2-3h0c1-1 1-1 1-2v-1c0-2 0-3 1-5z" class="h"></path><defs><linearGradient id="BW" x1="319.409" y1="729.185" x2="313.69" y2="772.719" xlink:href="#B"><stop offset="0" stop-color="#7f7376"></stop><stop offset="1" stop-color="#ccc8c7"></stop></linearGradient></defs><path fill="url(#BW)" d="M272 740c14-10 33-14 50-11 13 3 22 9 29 20 6 9 10 20 12 31h-1c0-1-1-1-2-2-1 0-1 0-1-1l-2-8c-4-12-10-22-22-27-8-4-18-6-27-5-8 1-15 3-22 6l-3 1c-8 4-15 9-21 16-2 2-4 5-6 7h-4l1-3 5-8 1-1 1-1v-1c1-2 2-3 3-5 3-3 6-6 9-8z"></path><path d="M283 744c-2 0-5 0-8 1h0v-1c4-1 7-1 11-1l-3 1z" class="AI"></path><path d="M373 666h1v-1c1-3 4-7 8-9h0c6-3 14-2 21 0l5 12c1 2 2 4 2 6 0 3-1 6-2 9 2 2 3 2 5 3 1 1 2 4 4 5l4 10c0 2 1 3 1 5l4 9 3 5v1l2 4v2c2 5 5 10 5 14 3 5 4 10 6 14l-1 2-1-2h-1l-4 6h0l-2 4-3 3-4 6c-1 3-3 6-5 8-2 3-11 11-15 12h1-4c-3-1-5-1-8-3l-2-2-2-2-5-7-6-9c-2-2-3-5-4-7-2-3-3-10-5-14l-1-4 1-1c0-2 1-4 1-6l1-2c1-2 1-3 2-4s1-1 1-2l2-2h-5c-3-2-6-3-9-4h-1l1-1c-2-2-3-4-4-6l-1-3-1-2v-3-4l-1-3 1-1h0 1l2-5 5-6 1-2c0-1 1-2 2-4h0v-4-1c1-1 1-2 2-3v-2c1-2 1-4 1-7h0v-2h1z" class="D"></path><path d="M359 715l1-1c0 1 1 2 0 4l-1-3z" class="N"></path><path d="M378 729h0l7-1c-4 2-8 5-12 9 1-2 1-3 2-4s1-1 1-2l2-2z" class="C"></path><path d="M370 746l1-1c0-2 1-4 1-6 0 3 0 5 1 8v4c1 5 3 12 7 16 0 0 1 1 1 2h0l-3-3c0-1-1-1-2-2-2-3-3-10-5-14l-1-4zm41-11h1c1 3 3 5 5 8l3 5 2 2c1 0 2-1 3-1h1 1c-3 2-4 6-6 9h0c1-2 1-3 1-4l-3-3h1c0-2-1-4-2-5v-1l-7-9c-1-1-2 0-3 0h-1l4-1z" class="I"></path><path d="M390 719h1c1 0 2-1 3-1 2-1 4-2 6-1l2 2v2h1 0 1c1-1 2-1 3-2-1 2-5 6-5 8l1 3-2 3v1l-1-1v1c0 1 1 1 2 1 0 1 1 2 1 3-1 1-1 1-1 2 1 5 0 9-3 12l-3 3c1-2 3-3 4-6v-1c1-2 1-4 0-6l-1-3v-2h-1v-1-1c0-2 0-3-1-4h0c1-2 1-2 1-3v-2l1-1c0-3 0-3-2-5h-2-2l-2 1h-2c0 1-1 1-1 1-3 0-6 1-8 1 1-1 1-2 3-3h0c3 1 5 0 7-1z" class="C"></path><path d="M402 727l1 3-2 3v1l-1-1c0-3 1-4 2-6z" class="h"></path><defs><linearGradient id="BX" x1="408.273" y1="736.767" x2="409.157" y2="749.205" xlink:href="#B"><stop offset="0" stop-color="#a19fa1"></stop><stop offset="1" stop-color="#bebcbc"></stop></linearGradient></defs><path fill="url(#BX)" d="M402 735l5 1h1c1 1 3 2 3 4 2 1 3 2 3 4l2 2c-2 1-2 0-3 0-2 0-7 4-9 5h0c-1 3-2 6-4 8h-1c-1 0-2 1-3 0-2 0-4 0-5-1l1-1c1-1 2-1 3-2h1l3-3c3-3 4-7 3-12 0-1 0-1 1-2 0-1-1-2-1-3z"></path><path d="M402 735l5 1h1c1 1 3 2 3 4l-2-2c-1-1-2-1-3-1l-1 1c0 1 2 3 1 5h-2l-1-5c0-1-1-2-1-3z" class="G"></path><path d="M396 755l3-3c3-3 4-7 3-12 0-1 0-1 1-2l1 5c0 5 0 11-3 14l-2 2c-1 0-2 1-3 0-2 0-4 0-5-1l1-1c1-1 2-1 3-2h1zm32-6l2-1v2h2 0c-2 7-5 12-11 18l-10 8c-7 2-14 1-21-1 2-2 5-1 7-2 2 0 4 0 6-1h0c8-2 15-7 18-14 2-3 3-7 6-9h1z" class="m"></path><path d="M373 666h1v-1c1-3 4-7 8-9h0c6-3 14-2 21 0l5 12c1 2 2 4 2 6h-1c0 1-1 1-1 2-1 1-1 3-2 4h-1l-6 6h-1c2-1 4-3 4-5h1c1-1 1-2 1-3v-1c-1-1-1-1-1-2v-1c-1-1 0-2-1-3 0-2-1-3-1-4 0-2-1-7-2-8-2-1-3-1-5-1v-1c-1 0-3 0-4 1h-1-3l-1 1c-1 0-2 0-2 1h-1c-2 1-4 3-5 5v1l-1 4v3c-1 2 0 4-1 6 0 1 0 1-1 2v2 1c-1 1-1 2-2 3l-1 2c-1 2-2 4-3 5l-3 4-1 2c-2 3-5 7-5 11l1 3-1 1-1-2v-3-4l-1-3 1-1h0 1l2-5 5-6 1-2c0-1 1-2 2-4h0v-4-1c1-1 1-2 2-3v-2c1-2 1-4 1-7h0v-2h1z" class="C"></path><path d="M360 702c0 2 0 7-1 8h-1v-4c1-1 2-2 2-4z" class="G"></path><path d="M358 702h1l2-5v3l-1 2c0 2-1 3-2 4l-1-3 1-1h0z" class="E"></path><path d="M373 666h1v-1c1-3 4-7 8-9h0c6-3 14-2 21 0l5 12c1 2 2 4 2 6h-1c0 1-1 1-1 2-3-2-3-8-4-11 0-2 0-6-2-8-3-2-11-2-15-1s-7 2-10 6c-4 6-3 14-5 20-1 5-4 9-7 13-1 2-2 4-4 5v-3l5-6 1-2c0-1 1-2 2-4h0v-4-1c1-1 1-2 2-3v-2c1-2 1-4 1-7h0v-2h1z" class="F"></path><path d="M371 677c1 3 0 6-2 9 0 1 0 1-1 2s-1 2-2 3l1-2c0-1 1-2 2-4h0v-4-1c1-1 1-2 2-3z" class="E"></path><defs><linearGradient id="BY" x1="418.207" y1="712.314" x2="409.857" y2="735.509" xlink:href="#B"><stop offset="0" stop-color="#aba9aa"></stop><stop offset="1" stop-color="#ecebea"></stop></linearGradient></defs><path fill="url(#BY)" d="M422 713l4 2 3 5c-1 1-1 2-2 3l-1 2-1 1v1l-1 1c-2 2-3 4-5 5l-7 2h-1l-4 1-5-1c-1 0-2 0-2-1v-1l1 1v-1l2-3-1-3c0-2 4-6 5-8l5-4c1-1 2-2 3-2 1-1 5 0 7 0z"></path><path d="M401 733c1 0 10 0 10 1v1l-4 1-5-1c-1 0-2 0-2-1v-1l1 1v-1zm14-20c0 1 0 1-1 1-4 4-5 8-9 13v1c0 1-1 2-2 2h0l-1-3c0-2 4-6 5-8l5-4c1-1 2-2 3-2z" class="m"></path><defs><linearGradient id="BZ" x1="426.386" y1="747.705" x2="437.223" y2="751.35" xlink:href="#B"><stop offset="0" stop-color="#959196"></stop><stop offset="1" stop-color="#b5b3b4"></stop></linearGradient></defs><path fill="url(#BZ)" d="M429 720v1l2 4v2c2 5 5 10 5 14 3 5 4 10 6 14l-1 2-1-2h-1l-4 6h0v-1l-1 1-2 1c-1 1-1 1-2 1-3 1-5 4-9 5 6-6 9-11 11-18h0-2v-2l-2 1h-1-1-1c-1 0-2 1-3 1l-2-2-3-5c-2-3-4-5-5-8l7-2c2-1 3-3 5-5l1-1v-1l1-1 1-2c1-1 1-2 2-3z"></path><path d="M432 762v-1l3-8v1l-2 5h1c1-1 2-3 3-5v-1h0c0 3-2 5-3 8l-2 1z" class="I"></path><path d="M429 721l2 4v2l-2 2c-1 0-1-1-2-2v-1c1-1 2-3 2-5z" class="E"></path><path d="M436 741c3 5 4 10 6 14l-1 2-1-2h-1l-4 6h0v-1l-1 1c1-3 3-5 3-8 1-1 1-3 1-5h0c-1-2-1-3-1-4-1 0-1-2-1-3z" class="O"></path><path d="M425 744c0-5-1-14 2-18v1 4h2l1-1c1 2 2 5 2 6 1 2 1 4 1 6v1c0-1-1-2-1-3-1-2-1-3-2-4v-1h-1c-1 1-1 1-2 3 0 2-1 3-1 5v2l-1-1z" class="F"></path><path d="M429 720v1c0 2-1 4-2 5-3 4-2 13-2 18l1 5h-1c0-1-1-2-1-2-2-7-3-13 0-19l1-1v-1l1-1 1-2c1-1 1-2 2-3z" class="D"></path><path d="M427 738c1-2 1-2 2-3h1v1c1 1 1 2 2 4 0 1 1 2 1 3v4c0 1 0 2-1 3h0-2v-2l-2 1h-1-1l-1-5 1 1v-2c0-2 1-3 1-5z" class="I"></path><path d="M427 738v8s2 1 2 2c0 0-1 0-1 1h-1-1l-1-5 1 1v-2c0-2 1-3 1-5z" class="G"></path><path d="M419 733c2-1 3-3 5-5-3 6-2 12 0 19 0 0 1 1 1 2-1 0-2 1-3 1l-2-2-3-5c-2-3-4-5-5-8l7-2z" class="k"></path><path d="M417 740c1 2 2 4 4 5 0 1 0 2-1 3l-3-5v-3z" class="L"></path><path d="M420 748c1-1 1-2 1-3 1 1 2 1 2 2h1s1 1 1 2c-1 0-2 1-3 1l-2-2z" class="E"></path><path d="M412 735l7-2c0 1 0 2-1 3h-3v1l2 3v3c-2-3-4-5-5-8zm22 26l1-1v1l-2 4-3 3-4 6c-1 3-3 6-5 8-2 3-11 11-15 12h1-4c-3-1-5-1-8-3l-2-2-2-2-5-7-6-9c-2-2-3-5-4-7 1 1 2 1 2 2l3 3h0c2 1 4 3 6 5h1 1l1 1c7 2 14 3 21 1l10-8c4-1 6-4 9-5 1 0 1 0 2-1l2-1z" class="F"></path><path d="M376 764c1 1 2 1 2 2l3 3h0c2 1 4 3 6 5h1 1c-5 1-5-1-9-3h0c-2-2-3-5-4-7z" class="G"></path><path d="M386 780l3 2c1 1 1 1 2 1l2 1c1 1 0 1 1 1 2 0 5 3 7 5-4-1-6-3-10-3l-5-7z" class="O"></path><path d="M401 790c0 1 1 1 2 1 3-1 5-3 8-5 5-3 10-7 15-12-1 3-3 6-5 8-2 3-11 11-15 12h1-4c-3-1-5-1-8-3l-2-2-2-2c4 0 6 2 10 3z" class="C"></path><path d="M421 782c-2 3-11 11-15 12h1-4c-3-1-5-1-8-3h3s1 1 3 1c1 0 3 1 4 0 2 0 5-2 7-4l6-4 3-2z" class="X"></path><defs><linearGradient id="Ba" x1="409.382" y1="769.441" x2="434.625" y2="769.093" xlink:href="#B"><stop offset="0" stop-color="#7e7b81"></stop><stop offset="1" stop-color="#a09d9f"></stop></linearGradient></defs><path fill="url(#Ba)" d="M434 761l1-1v1l-2 4-3 3c-4 3-7 7-12 9-3 2-5 4-8 5l-6 3c-1 1-1 2-2 3h-1c3-5 6-8 10-12l10-8c4-1 6-4 9-5 1 0 1 0 2-1l2-1z"></path><path d="M408 676c0-1 1-1 1-2h1c0 3-1 6-2 9 2 2 3 2 5 3 1 1 2 4 4 5l4 10c0 2 1 3 1 5l4 9-4-2c-2 0-6-1-7 0-1 0-2 1-3 2l-5 4c-1 1-2 1-3 2h-1 0-1v-2l-2-2c-2-1-4 0-6 1-1 0-2 1-3 1h-1c-2 1-4 2-7 1h0l1-2c0-1 0-2-1-2l1-1c0-3 0-5-1-7 0-3-2-5-4-6v-1c0-1 4-2 5-2 4-2 7-4 10-6h1v-1c2-1 5-5 7-7v-1h1v-1l3-3c1-1 1-3 2-4z" class="m"></path><path d="M384 718c3-4 6-7 10-9-1 2-2 3-3 4v1h0c-1 1-3 2-3 4h1l1 1c-2 1-4 2-7 1h0l1-2z" class="AI"></path><path d="M408 676c0-1 1-1 1-2h1c0 3-1 6-2 9-1 1-1 3-2 4 0-1 0-2 1-3v-3h0v-1l1-1v-1l-1 1c0 2-1 3-2 4-3 4-6 8-10 10v-1c2-1 5-5 7-7v-1h1v-1l3-3c1-1 1-3 2-4z" class="X"></path><path d="M411 690l6 6c1 2 2 4 4 5 0 2 1 3 1 5l-3-1h0 1c-1-2-3-5-4-7l-6-3c0-2 0-4 1-5z" class="D"></path><path d="M408 683c2 2 3 2 5 3 1 1 2 4 4 5l4 10c-2-1-3-3-4-5l-6-6-1-1v-1l-3-1c-1 0-2 1-2 2h-1l2-2c1-1 1-3 2-4z" class="C"></path><path d="M404 706c5-1 10-1 15-1l3 1 4 9-4-2c-2 0-6-1-7 0-1 0-2 1-3 2l-5 4c-1 1-2 1-3 2h-1 0-1v-2l-2-2c-2-1-4 0-6 1-1 0-2 1-3 1h-1l-1-1h-1c0-2 2-3 3-4h0v-1c1-1 2-2 3-4 3-2 7-2 10-3z" class="k"></path><path d="M391 714h2c2 1 3 0 5 0-2 0-4 1-5 2l-4 2h-1c0-2 2-3 3-4z" class="E"></path><path d="M393 714c6-3 11-4 17-3-4 1-8 1-12 3-2 0-3 1-5 0z" class="v"></path><path d="M393 716c2 0 4-1 6-2 1 0 3 1 5 1 4 3 4-1 8 0l-5 4c-1 1-2 1-3 2h-1 0-1v-2l-2-2c-2-1-4 0-6 1-1 0-2 1-3 1h-1l-1-1 4-2z" class="F"></path><path d="M404 706c5-1 10-1 15-1l3 1 4 9-4-2c-5-4-12-5-18-6v-1z" class="l"></path><defs><linearGradient id="Bb" x1="799.194" y1="602.879" x2="517.572" y2="517.277" xlink:href="#B"><stop offset="0" stop-color="#000204"></stop><stop offset="1" stop-color="#310d0c"></stop></linearGradient></defs><path fill="url(#Bb)" d="M782 175l5 4c0 1 0 1 1 1 1 1 2 1 3 2s3 1 5 2c3 1 7 2 10 4v-1c3 2 6 4 9 5l2 2h2l10 2c3 1 8-1 10 0v1l5-1 4-1c1 0 2-1 3-1h1c6 1 23 2 26 6l2 1c1-1 0-1 1-1 1-1 3 0 4 0l3 3 1-1 1 1h0c2 0 7 5 8 7h0c0 1 1 2 2 3s1 2 2 4c1 1 1 1 1 2h1c2 1 2 4 4 5 1 1 1 3 2 4l-3-1v3c2 6 2 13 2 19l-1 6v2l-2-1h0c0 2 0 6-1 7v-1c-1 2-1 2 0 3-1 2-4 2-5 3s-1 2-2 3h-1 0v-1l-2 1c0-2 1-3 2-5-1 1-3 1-4 2s0 1-1 1l-3-3 1-4-1-6c-1-3-2-7-3-10l-2-3c-6-12-18-18-32-22 0 4-6 8-9 10l-19 11-7 5-1 1c-4 2-7 6-9 9l-5 6c-3 3-7 10-9 15 0 1-1 3-1 4-2 5-4 9-5 15 1 1 1 1 1 2h1l1 1 1-1h2l1 1h-1c-5 0-12-1-17-3h-1l-3-2-3-2-8 19-1 2c-2 3-3 7-4 10l-4 10 2 1h-1v2l1 3c-1 3-2 4-3 6-1 3-2 6-4 9h-1l-1 2h0l2 4 2 2 2 4c1 2 1 4 1 7l1 2h-2c0-1 0-2-2-3l-1-1-1 1 1 1c0 1 0 2-1 2l-1 1h0v2h0c0 2-1 3-2 4v1l-3 3-2 4h-3l-2 1h0-3-1l-2 6c-3 8-6 17-10 25-1 4-2 9-4 12l3 2c-1 0-1 1-1 2 0 2-1 5-3 6l-2 1c-1 4-2 8-2 13l-4 15c1 6-3 9-4 14l-4 8c-1 6-4 12-6 17l-1 3v8l-6 9c-2 2-3 4-6 5-2 2-4 5-5 8l1 1 3-2v3c-1 3-5 4-6 7l-2 3c-1 1-2 1-3 2l-4 10c-1 1 0 1-1 2 0 2-2 4-3 6l-3 10h1l3-2c0 1 0 1-1 3h0l-3 3-4 5-21 53h2 0 4l1-1 2 1-1 2h0l-2 1 1 1c1 0 6-1 7 0l2 1c1 1 3 1 4 2l1-1 1-1c2 2 4 4 6 7l1 1v1h-1c-1-3-3-5-6-6h-1l-4-1h0c-2-1-3 0-5-1h-6-2c-1 1-2 1-3 3-1 1-1 2-1 4-1 1-2 3-2 5v1c-1 3 0 8 0 11v2l1 1v2l1 2v1c0 2 0 3 2 4-1 1-2 2-4 2l1 1h-1c-1 0-1 0-2 1s-1 1-2 1c-1 1-1 2-1 3 1 1 2 1 3 1l-2 1c-2 0-3 0-4 1l-1 1c5 1 10 1 14 3-2 1-4 0-6 1-4 0-7 1-11 3l-3 1-2 1-1 1c-1 2-2 2-3 3-1 2-2 2-3 3l2 2 1 1c1 0 1 1 2 2h0c1 1 2 2 4 3v1l-1 1c1 0 2 1 2 2h-1v7c-1 4-3 8-5 12h-1c-1-1-2-3-3-5l-2 3v2l-1-3-1 2h-1c-2 1-2 2-2 4-1 1-1 2 0 3-1 1-1 1-2 1l-2-3h-1l-1-1-1-1-5 13-2 5 1 4 1 3v1h1l4 5c1 1 1 1 2 1l-1 1-2-1h-1l-1-1v1c-1 3-6 5-5 9l1 1h1l1 3c-1-1-2-2-4-3h0c-1-1-2-2-2-3-1 3 0 6-1 9h0l-1 1-2 2-3 6v1h-1v2 2l-1-3h0l-1-2-5 13-2 3-1 3v1l-1 3-2 4-1 4c-2 4-4 9-5 13l-13 35c-3 6-5 13-7 20l-15 40c-2-1-2-5-3-6l-6-18-18-47-11-27-1-3-2-6-10-24-2-5-3-10c0-1 0-3-1-3 0-1 0-2-1-3v-1l-1-1v-1c0-1 0-1-1-2 0-1-1-2-1-3-1-2-1-3-1-4l1-1c0-1 0-1-1-2v-2h0c3-2 1-2 2-4 0-1 1-1 1-2-1-1-1-2-1-3v-2c0-2 0-4-1-6h0l1-1v-4h-1c0-2 0-1-1-2v-1c1-2 1-3 1-5-1-2-1-4-1-5-1-1-1-2-1-4h0l-1-1v-1-2c0-1 0-2-1-3v-1l2-1h1c0 2 2 5 3 7 0 2 1 5 2 6s2 2 2 4l1-1c2 6 4 13 6 19l1-1 3 11c1 1 2 1 4 2 2 0 4-1 7-1l2 3h2c2 0 5 1 8 0 4-1 6-2 7-5h1c1 2 1 2 0 3 0 2-1 2-2 3v1h1v3l1 1h1l3-1c1-1 1-3 2-4h2l2-1 2-5 4-7c1-2 5-5 6-8v-2c1-2 4-3 4-5s1-3 2-5c1-1 5-4 6-5v-2l2 1c-1 3-2 5-2 8v2c2-3 6-6 7-10 2-4 4-8 5-13 0-2 1-3 2-4v-1c0-3 1-6 1-10h3 0c0 3 0 7-1 10l1 1v3l1 1c2-1 3-3 4-4l1-1c1-1 2-2 2-4l1-1c0 3 0 4-1 6s-2 4-1 6c0 1-1 3-1 4l1-1h0s1-1 2-1h0c0 1 1 2 1 3l2-2 1-1 3-5c-1-1-2-2-2-3l11-21 3-8 11-24 88-231c10-32 26-62 33-95 1-2 1-4 3-6 1-2 1-7 1-9l1-1 1 1c0-1 1-3 1-4l1-3c0-1 1-3 1-4h2c0-2 1-6 1-8l1-3c0-1 0-3 1-4l1-3-2-3 2-8 1-3h-1c0-3 1-7 2-10 0-3 0-5 1-8 2-2 1-5 1-7 0-1 3-6 4-7 0 0 1 0 1-1l6-25 2-5h0c0-2 1-3 2-5h0l3-4 1-3c1-2 2-3 1-4-1-2-3-2-5-3 1-2 3-2 5-3 2 0 4-1 5-1 3-2 5-3 7-5 1-1 2-2 2-3l2-2c4-5 8-11 11-18z"></path><path d="M458 768l1 1-1-1z" class="B"></path><path d="M782 206h3l1 2-5-1 1-1z" class="f"></path><path d="M753 280l2-1c-1 2 0 4-1 6l-1 1v-3h-1c1-1 1-2 1-3z" class="AK"></path><path d="M815 199l5-1 1 2h-1c-1 1-1 0-2 1h-1-1l1-1-2-1z" class="Z"></path><path d="M536 807h1c0 3-1 6-3 9h-1l3-9z" class="AQ"></path><path d="M610 714c-1 2-2 2-3 3-1 2-2 2-3 3v1l-2-1c2-3 5-4 8-6z" class="v"></path><path d="M576 722c0 1 0 2-1 3h0l-2 3v1 1h-1v1 1h-1 0l-2 2 3-5 4-7z" class="d"></path><path d="M785 206l6 1c2 0 4 0 7 1h-12l-1-2z" class="AB"></path><path d="M597 734h2c-1 1-1 3-2 4h-1l-2 5c0 1-1 1-1 2l4-11zm194-527l11-1c1 0 1 0 1 1l-5 1c-3-1-5-1-7-1z" class="AF"></path><path d="M903 219h1l3 8v3l-5-8h1l1 1c0 1 1 2 2 3h0l-1-2h0v-1l-1-1v-1l-1-1v-1z" class="Z"></path><path d="M904 219c2 1 2 4 4 5 1 1 1 3 2 4l-3-1-3-8z" class="AR"></path><path d="M501 855l2 1 2 7v1c-2-1-3-1-3-3v-1l-1-1v-4z" class="c"></path><path d="M781 207c-2 0-3-1-4-3-2-2-3-4-2-6h0c1 4 4 6 7 8l-1 1z" class="AM"></path><path d="M579 781l4-10 1 4 1 3h-1-1-1l-3 3z" class="v"></path><path d="M751 335c2 3 0 6-1 9l-3 3 4-12z" class="AH"></path><path d="M823 203s1 0 2-1h11 3l-2 1-13 1-1-1z" class="AM"></path><path d="M496 843c1 0 2 1 3 1 2 4 2 8 4 12l-2-1c-1-2-1-3-2-6-1-1-1-2-2-4l-1-2z" class="Y"></path><path d="M739 367c1-4 3-8 5-12l1 2h0c-1 5-2 7-6 10z" class="AM"></path><path d="M659 586c0 1 0 1-1 3h0l-3 3-4 5 4-9h1l3-2z" class="f"></path><path d="M689 500h0c0-1 0-2 1-3 0-2 1-3 1-4l1-1-1-1c0-1 1-3 2-4v1l2-5c0-2 1-2 1-3 1-1 1-1 1-2h1l-2 6-7 16z" class="AL"></path><path d="M732 319l3-2v1l1-1c1 1 1 1 1 2l-4 8v-2-1l-2-3 1-2z" class="J"></path><path d="M732 319l3-2v1l-2 6-2-3 1-2z" class="AS"></path><path d="M747 347l3-3c-1 3-2 9-4 11l-1 2-1-2 3-8z" class="AF"></path><path d="M593 745c0-1 1-1 1-2l2-5h1v2l-1 8c-2 1-2 1-5 1 1-2 1-3 2-4zm159-411l2 1h-1v2l1 3c-1 3-2 4-3 6-1 3-2 6-4 9h-1c2-2 3-8 4-11s3-6 1-9l1-1z" class="AB"></path><path d="M811 205l12-2 1 1-21 3c0-1 0-1-1-1l9-1z" class="AH"></path><path d="M623 665h0l1-4 1-1v-1-1c0-1 1-1 1-2h1v-1h1v-1l-1-1 2-4 1 1 1 1h0 0c-2 2-2 3-3 6l-2 4-1 2h0c-1 0-1 1-2 2z" class="d"></path><path d="M731 321l2 3v1 2l-6 11c-1-2 0-3 1-5 1-3 1-5 2-8v-1l1-3z" class="r"></path><path d="M731 321l2 3v1c-1 0-1 0-2 1l-1-1v-1l1-3z" class="M"></path><path d="M637 649l2 1-1 2h0l-2 1v1c-2 0-3 0-5 1-1 0-2 1-3 2 1-3 1-4 3-6h0 0l-1-1h0 2 0 4l1-1z" class="f"></path><path d="M637 649l2 1-1 2h0-3c0-1 1-1 1-2l1-1z" class="AH"></path><path d="M623 665c1-1 1-2 2-2h0l-8 22c0-1-1-2-1-2v-1c0-3 3-6 4-10 1-2 2-5 3-7z" class="Z"></path><path d="M848 195c1 0 2-1 3-1h1c6 1 23 2 26 6-6-1-13-3-19-4-4-1-7-1-11-1z" class="f"></path><path d="M738 310h1c0 1 0 3-1 4l1 2-2 3c0-1 0-1-1-2l-1 1v-1l-3 2v-2l4-7h2z" class="b"></path><path d="M738 314l1 2-2 3c0-1 0-1-1-2 1-1 2-2 2-3z" class="U"></path><path d="M738 310c0 1-1 2-1 3s0 1-1 1c-1 1-1 2-1 3l-3 2v-2l4-7h2z" class="J"></path><path d="M745 357l2 4v1l-1 1c-4 3-6 8-10 12l3-8c4-3 5-5 6-10z" class="AH"></path><path d="M803 211c1 0 1-1 2 0-3 2-7 3-10 5-6 2-10 6-15 9h0l3-2c1-1 1-2 2-3h-1l-4 2v1l-3 2h0-1c5-4 9-6 15-9 4-2 8-4 12-5z" class="AK"></path><path d="M488 821l1 1c1 1 1 2 2 4l1 1c0-1-1-2-1-3v-2l8 22c-1 0-2-1-3-1 0-2-1-5-2-7l-6-15z" class="q"></path><path d="M760 258l2-4v1c0 2-2 5-1 7-3 6-5 11-6 17l-2 1v-1l1-3c1-6 4-12 6-18z" class="l"></path><path d="M747 361l2 2c-3 2-6 4-8 7-3 5-4 12-9 16l4-11c4-4 6-9 10-12l1-1v-1z" class="AF"></path><path d="M758 260c-3 10-7 19-13 29h0c-1-1-1-3-1-4v-2c1 1 0 2 1 4v-1h1c1-2 1-4 2-6 3-4 4-10 7-14 1-2 2-5 3-6z" class="Z"></path><defs><linearGradient id="Bc" x1="598.152" y1="725.747" x2="601.461" y2="730.677" xlink:href="#B"><stop offset="0" stop-color="#534f58"></stop><stop offset="1" stop-color="#6a666c"></stop></linearGradient></defs><path fill="url(#Bc)" d="M602 720l2 1 2 2v3h-1c-1 1-1 1-1 2l-2-1v3h-1c-1 1-2 3-2 4h-2c1-4 2-8 4-11l1-3z"></path><path d="M602 720l2 1h0l-2 2h-1l1-3z" class="f"></path><path d="M604 721l2 2v3h-1c-1 1-1 1-1 2l-2-1h0c1-2 2-3 2-6h0zm70-174l3-2v3c-1 3-5 4-6 7l-2 3c-1 1-2 1-3 2l1-2 3-10 1-2 1 1h2z" class="AH"></path><path d="M667 558l3-10 2 1 1 1c-1 1-3 6-5 7l-1 1z" class="f"></path><path d="M569 760v-3l1-1v-1c1-1 2-3 2-5h0l3-6h0c1-4 2-9 4-11l1-1v-1h1v1 2l-1 2v3l-2 4v2h0l-1 1-2 4c0 1-1 3-1 4-1 1-1 2-1 2h-1c1-1 1-1 1-2v-1l1-2h-1c-2 2-3 6-4 9z" class="Z"></path><path d="M746 310c-1-6 6-7 8-11 2-2 0-3 1-5l1 1c1 3 3 4 6 5-6 3-11 6-16 10z" class="AS"></path><path d="M886 246h0c1 1 2 1 2 3h0v1c1 1 0 0 1 2 0 1 1 2 1 3v1 2 2c1 1 1 2 1 2h1c1-2 2-2 3-4s2-2 2-4v-1-3s-1 1-1 2l-1 1v-1l1-2v-1l1-1c0-1 0-1 1-2 0-1 0-2-1-3v-1c0-2-1-5-2-6v-1c3 5 5 12 5 18-1 3-1 6-1 9v-9c-1 3-4 9-7 11-1-1-2-1-2-2l-1-6c-1-3-2-7-3-10z" class="l"></path><path d="M538 803c0-1-1-1-1-2s1-2 1-3c2-5 4-9 5-13l-1-1h-2l-2 4c0-1 0-2 1-3 0-1 0-2 1-2h3c1 1 0 1 1 2v1l2-5c0-1 1-1 1-2 1-2 2-3 3-5v-1c0-1 1-1 1-2 1-5 5-10 7-15l3-6h1v1c-9 17-18 34-24 52zm-5 13c-1 2-1 4-2 6s-1 4-1 5l-1 2c0 2-1 3-1 5-1 1-1 0-1 1s0 1-1 2v-3l1-2 1-1v-2c0-1 1-2 1-3l1-4 1-1v-2c-1 1-1 1-1 2l-5 14v2 1h-1v-1-2l1-1v-1-1l1-1v-2c1-1 1-1 1-2h0v-1c1-1 1-1 1-2v-1-1l2-2c0-2 0-3 1-4l1-2c0-2 2-5 3-7 0-1 0-1 1-2h0l1-1-1 3-3 9z" class="Z"></path><path d="M890 262c0 1 1 1 2 2 3-2 6-8 7-11v9 3l-2 1c-1 1-3 1-4 2s0 1-1 1l-3-3 1-4z" class="x"></path><path d="M749 363l2 4c-3 0-5 1-7 3-4 3-5 8-7 13l-6 9h-1l2-6c5-4 6-11 9-16 2-3 5-5 8-7z" class="AB"></path><path d="M597 740c1 2 1 3 1 4-1 1-1 2-1 3 1 0 2-1 2-1v3 1c-2 1-2 2-2 4-1 1-1 2 0 3-1 1-1 1-2 1l-2-3h-1l-1-1-1-1 1-4c3 0 3 0 5-1l1-8z" class="k"></path><path d="M597 747c1 0 2-1 2-1v3 1c-2 1-2 2-2 4-1 1-1 2 0 3-1 1-1 1-2 1l-2-3h-1l-1-1 1-2 4 1 1-6z" class="E"></path><path d="M758 260l2-2c-2 6-5 12-6 18l-1 3v1c0 1 0 2-1 3 0 1-1 1-1 2l-1 1-1 1c-1 2-3 3-5 5l-1-6v1l-1 1v1l-1 1c-1 1-1 1-2 1v-1c1 0 3-3 4-4v-1h1c0 1 0 3 1 4h0c6-10 10-19 13-29z" class="s"></path><path d="M479 798c0-1 1-2 1-3l8 19c1 2 3 5 3 8v2c0 1 1 2 1 3l-1-1c-1-2-1-3-2-4l-1-1-6-14c0-3-2-7-3-9z" class="H"></path><path d="M751 367c1 2 1 4 1 7l-2-3h-1-3c-1 1-4 4-5 6s-2 5-3 8c-1 2-4 4-4 7h0-3l6-9c2-5 3-10 7-13 2-2 4-3 7-3z" class="k"></path><path d="M581 705v1c-1 1-1 2-2 4 0 1 0 1 1 2 0-1 1-3 2-3 0-1 0-2 1-3 0-1 1-1 2-2l-7 13c0 1-2 4-2 5l-4 7c-1-1-2-2-2-3l11-21z" class="AT"></path><path d="M562 751c2-3 4-7 5-10 1-1 0-1 1-1h0 1l-7 13c-5 7-9 16-12 23l-13 31h-1l1-3 1-1c6-18 15-35 24-52z" class="l"></path><path d="M527 776l5-6 2 1c-6 7-12 14-16 23 0 1-2 4-2 6-2 1-3 3-5 3-1 1-2 1-4 1h0l4-1c2-2 2-3 3-6 2-3 3-6 4-9 3-4 6-7 8-11l1-1z" class="b"></path><path d="M762 254c4-7 9-13 15-18 7-6 13-12 21-17v1c-15 12-28 24-37 42-1-2 1-5 1-7v-1z" class="f"></path><path d="M907 237c1 2 1 8 1 11l1 1-1 6v2l-2-1h0c0 2 0 6-1 7v-1c-1 2-1 2 0 3-1 2-4 2-5 3s-1 2-2 3h-1 0v-1l-2 1c0-2 1-3 2-5l2-1v-3c0-3 0-6 1-9h0v8 1c-1 1 0 2 0 3v-2c1-1 0-2 0-3 1-1 1-1 1-2 1 0 2-1 3-2 0-1 0-1 1-1v-1c0-1 1-1 1-2v-3c1-2 1-8 1-11h-1l1-1z" class="AK"></path><path d="M899 265l-2 6h0v-1l-2 1c0-2 1-3 2-5l2-1z" class="H"></path><path d="M908 248l1 1-1 6v2l-2-1c1-3 1-5 2-8z" class="x"></path><path d="M900 268c1-4 4-8 6-12 0 2 0 6-1 7v-1c-1 2-1 2 0 3-1 2-4 2-5 3z" class="V"></path><path d="M734 392c0-3 3-5 4-7l3-8c1-2 4-5 5-6h3 1l2 3 1 2h-2c0-1 0-2-2-3l-1-1-1 1 1 1c0 1 0 2-1 2l-1 1h0v2h0c0 2-1 3-2 4v1l-3 3-2 4h-3l-2 1z" class="E"></path><path d="M736 391c1-2 3-4 4-6 2-3 4-9 8-11 0 1 0 2-1 2l-1 1h0v2h0c0 2-1 3-2 4v1l-3 3-2 4h-3z" class="O"></path><path d="M741 297c1 0 2-1 2-1l2 1c-1 1-2 2-2 3 0 3-1 6-1 8 1 0 1-1 2-2 0 2-1 2-1 3 0 0-1 0-1 1 1 0 1 1 2 1l-5 5-1-2c1-1 1-3 1-4h-1-2v-1h-1c0-1 0-2-1-2v-1c0-1-1-2-1-3l1-6v1c0 1 1 2 1 2l-1 1v3c1 0 1 1 1 2 1-1 1-1 1-2l1-1c0-2 1-3 2-4l1-2h1z" class="V"></path><path d="M736 309c0-1 1-2 1-4 2-1 2-2 3-4h0 1v2h1l1-2v-1c0 3-1 6-1 8l-1 2h-2c0-1 0-2 1-3v-2l-3 3-1 1z" class="P"></path><path d="M736 309l1-1 3-3v2c-1 1-1 2-1 3h2l1-2c1 0 1-1 2-2 0 2-1 2-1 3 0 0-1 0-1 1 1 0 1 1 2 1l-5 5-1-2c1-1 1-3 1-4h-1-2v-1h0z" class="H"></path><path d="M836 202c5-1 10 0 15 0 11 1 21 3 31 6l2-1 3 3c6 3 11 7 15 12l5 8c2 6 2 13 2 19l-1-1c0-3 0-9-1-11 0-7-4-13-9-18s-11-8-18-10c-14-5-28-6-43-6l2-1h-3z" class="f"></path><path d="M882 208l2-1 3 3c-2 0-3-1-5-2z" class="l"></path><path d="M714 435l3 2c-1 0-1 1-1 2 0 2-1 5-3 6l-2 1c-1 4-2 8-2 13-1 1-2 1-2 3h0l-4 8c-1 4-3 11-7 14l2-6 16-43z" class="AB"></path><path d="M731 292l2 1-1 1h3l-1 3-1 6c0 1 1 2 1 3v1c1 0 1 1 1 2h1v1l-4 7v2l-1 2-1 3-1-1-1 1h-2l1-3c0-2 1-6 1-8l1-3c0-1 0-3 1-4l1-3-2-3 2-8z" class="U"></path><path d="M729 323l1-7 1 1c1 1 0 1 1 0v2l-1 2-1 3-1-1z" class="T"></path><path d="M733 303c0 1 1 2 1 3v1c1 0 1 1 1 2h1v1l-4 7c-1 1 0 1-1 0l-1-1 1-5 2-8z" class="y"></path><path d="M731 292l2 1-1 1h3l-1 3-1 6-2 8h-1c0 2-1 3-1 4 0-1 0-1-1-2l1-3c0-1 0-3 1-4l1-3-2-3 2-8z" class="AG"></path><path d="M731 292l2 1-1 1-1 9-2-3 2-8z" class="AY"></path><path d="M811 205l2-2h0c2 0 4 0 6-1h5 1c1-1 4 0 5-1 2 0 4 0 6-1h0l1-1c1 0 2 1 3 0h5c2 1 5 0 7 0 11 1 22 3 32 8l-2 1c-10-3-20-5-31-6-5 0-10-1-15 0h-11c-1 1-2 1-2 1l-12 2z" class="AR"></path><path d="M463 748c2 6 4 13 6 19l1-1 3 11v2l-2 1c-3-1-5-1-8 0 0 1-1 1-1 2s0 1 1 1c2 1 5 2 8 2l6 2h-1c1 2 2 4 3 5l1 3c0 1-1 2-1 3 1 2 3 6 3 9-1-1-1-2-2-3-1-5-3-8-5-13 0-1-1-2-1-3v-1l-1-1h-1-1-1c-1 0-3-1-3-1h-1c-1-1-1 0-2-1-2 0-2 0-3 1 0 1 0 2 1 2v1 1c1 1 1 3 1 5h0c0 2 0 4-1 5v-4-1c-1-2-1-3-1-5-1-1-1-3-1-4h0v-3c0-1 1-1 2-2 1 0 1 0 2-1h2c2-4-2-8-1-12-1-1 0-2-1-4 0-1-1-2 0-3 0-1 0-1 1-2l-1-2v-2c-1-2-1-3-2-5l1-1z" class="w"></path><path d="M478 795c-1-2-3-5-3-7l1-1c1 2 2 4 3 5l1 3c0 1-1 2-1 3l-1-3z" class="q"></path><path d="M478 795c0-1 0-1 1-3l1 3c0 1-1 2-1 3l-1-3z" class="T"></path><path d="M470 766l3 11v2l-2 1v-1-1c0-4-2-8-2-11l1-1z" class="c"></path><path d="M744 292c2-2 4-3 5-5l1-1 1-1c0-1 1-1 1-2h1v3l1-1v5l1 4c-1 2 1 3-1 5-2 4-9 5-8 11l-2 1c-1 0-1-1-2-1 0-1 1-1 1-1 0-1 1-1 1-3-1 1-1 2-2 2 0-2 1-5 1-8 0-1 1-2 2-3l-2-1s-1 1-2 1h0v-1-3l1 1c1-1 1-2 2-2z" class="T"></path><path d="M753 286c0 3 0 6-1 9h-1c-1 2-2 3-4 4l-2-2-2-1 1-1c0-1 2 0 3-1 4-1 5-5 6-8z" class="w"></path><path d="M754 290l1 4c-1 2 1 3-1 5-2 4-9 5-8 11l-2 1c-1 0-1-1-2-1 0-1 1-1 1-1 0-1 1-1 1-3 1-1 0-3 1-5l2 2c1-1 4-3 5-4 2-2 2-4 2-6v-2-1z" class="b"></path><path d="M744 292c2-2 4-3 5-5l1-1 1-1c0-1 1-1 1-2h1v3h0c-1 3-2 7-6 8-1 1-3 0-3 1l-1 1s-1 1-2 1h0v-1-3l1 1c1-1 1-2 2-2z" class="P"></path><path d="M744 292c2-2 4-3 5-5l1-1 1-1c0-1 1-1 1-2h1l-1 1c-2 4-2 7-6 9h-2-1c0 1 0 1-1 1 1-1 1-2 2-2z" class="c"></path><path d="M579 781l3-3h1 1 1v1h1l4 5c1 1 1 1 2 1l-1 1-2-1h-1l-1-1v1c-1 3-6 5-5 9l1 1h1l1 3c-1-1-2-2-4-3h0c-1-1-2-2-2-3-1 3 0 6-1 9h0l-1 1-2 2-3 6v1h-1v2 2l-1-3h0l-1-2 9-25 1-4z" class="E"></path><path d="M579 781l3-3h1 1 1v1 1c-1 0-1-1-2-1-1 1-1 2-1 3-1 1-3 2-4 3l1-4z" class="L"></path><path d="M572 810c-1 0-1 0-1-1l1-3c1-1 2-3 3-5l1-4c1-2 1-4 2-6h0l1 1c-1 3 0 6-1 9h0l-1 1-2 2-3 6z" class="O"></path><path d="M586 779l4 5c1 1 1 1 2 1l-1 1-2-1h-1l-1-1v1c-1 3-6 5-5 9l1 1h1l1 3c-1-1-2-2-4-3h0c0-1 0 0-1-1l-1-2c-2-5 4-6 5-10v-1c2 0 3 1 4 2-1-1-2-3-2-4z" class="G"></path><path d="M516 800h0c-2 10-2 22-1 32v15c1 4 2 9 1 12-3-15-3-33-9-48l-3-7h3c2 0 3 0 4-1 2 0 3-2 5-3z" class="V"></path><path d="M724 325c0-1 1-3 1-4h2l-1 3h2l1-1 1 1v1c-1 3-1 5-2 8-1 2-2 3-1 5l-4 10h0l-4 9c0 1-2 6-3 7l-5 12c0-1-1-2-1-3l4-12 5-20c1-2 1-7 1-9l1-1 1 1c0-1 1-3 1-4l1-3z" class="b"></path><path d="M728 324c0 2-1 3-2 4v-4h2z" class="AC"></path><path d="M719 357c-1-4 1-6 3-9h1l-4 9z" class="U"></path><path d="M724 325c0-1 1-3 1-4h2l-1 3v4c-1 1-1 1-1 2v1l-1-1v-5z" class="J"></path><path d="M714 361h0c0 3-1 6-1 8l1-2 1-2v-1h1l-5 12c0-1-1-2-1-3l4-12z" class="AD"></path><path d="M716 347c1-2 1-4 3-6l-5 20-4 12c0 1 1 2 1 3-1 3-2 6-4 9l-14 39c-3 8-6 17-10 25 0-2 0-3 1-5h0l-1-2c10-32 26-62 33-95z" class="a"></path><path d="M707 385c-1-4 2-8 3-12 0 1 1 2 1 3-1 3-2 6-4 9z" class="e"></path><path d="M782 175l5 4h-1c-2-1-2-1-3-2h-1c0 1 0 2-1 2-1 1-1 3-2 4 0 0 0 1-1 2v1l-1 1c-1 1-2 3-4 4 0 1-1 2-2 3s0 1-1 2v1l-2 2c-1 1-1 2-1 2l-2 4h0l-2 3c0 1-1 2-1 3s-1 1-1 3v1l-4 7c-1 2-2 3-2 5 0 1-1 1-1 2l-1 2c0 2 1-1 0 2-1 1-1 2-2 3v2c-1 1-1 2-2 3v1 1 1h-1v1 1 1h-1v1c0 1 0 1-1 2v2 1c-1 0-1 1-1 2s-1 3-1 4-1 2-1 3-1 3-2 4v2c0 1-1 2-1 3l-1 5v1h0l1 1c-1 1 0 2-1 3v2s-1 1-1 2c-1-2 0-6 0-8l3-11 12-36v-2c0-1-1-1 0-2v-1c0-1-1-1-1-2v-1h0-1 0l-1-1 3-4 1-3c1-2 2-3 1-4-1-2-3-2-5-3 1-2 3-2 5-3 2 0 4-1 5-1 3-2 5-3 7-5 1-1 2-2 2-3l2-2c4-5 8-11 11-18z" class="Z"></path><path d="M760 203c3-2 5-3 7-5l-5 10c0-1-1-2-1-3h0-1-1l1-2z" class="W"></path><path d="M755 204c2 0 4-1 5-1l-1 2h1 1 0c0 1 1 2 1 3l-9 22v-2c0-1-1-1 0-2v-1c0-1-1-1-1-2v-1h0-1 0l-1-1 3-4 1-3c1-2 2-3 1-4-1-2-3-2-5-3 1-2 3-2 5-3z" class="Q"></path><path d="M755 204c2 0 4-1 5-1l-1 2c-1 0-2 1-3 2 0 1 0 2-1 3-1-2-3-2-5-3 1-2 3-2 5-3z" class="t"></path><defs><linearGradient id="Bd" x1="568.835" y1="793.861" x2="548.556" y2="791.627" xlink:href="#B"><stop offset="0" stop-color="#211b1c"></stop><stop offset="1" stop-color="#3d343f"></stop></linearGradient></defs><path fill="url(#Bd)" d="M569 760c1-3 2-7 4-9h1l-1 2v1c0 1 0 1-1 2h1l-30 84c0-1 0-1 1-2v-2h-1v-4c0-1 0-2 1-3v-3l1-1c0-2 1-3 1-5l1-1v-2l2-5 1-4 2-3 1-3v-1-1c1 0 1-1 1-2h0v-1l1-1v-2c0-1 0 0 1-1v-2c1-1 2-3 2-5l1-1c0-2 1-3 2-5l3-8c2-4 4-8 5-12h0z"></path><defs><linearGradient id="Be" x1="598.783" y1="730.553" x2="603.273" y2="742.742" xlink:href="#B"><stop offset="0" stop-color="#6a6670"></stop><stop offset="1" stop-color="#9b9a9d"></stop></linearGradient></defs><path fill="url(#Be)" d="M604 720l2 2 1 1c1 0 1 1 2 2h0c1 1 2 2 4 3v1l-1 1c1 0 2 1 2 2h-1v7c-1 4-3 8-5 12h-1c-1-1-2-3-3-5l-2 3v2l-1-3-1 2h-1v-1-3s-1 1-2 1c0-1 0-2 1-3 0-1 0-2-1-4v-2c1-1 1-3 2-4 0-1 1-3 2-4h1v-3l2 1c0-1 0-1 1-2h1v-3l-2-2v-1z"></path><path d="M601 748v-1h-1c0-1 0-2 1-3h1 1c-1 2-1 2-1 4v1 2l-1-3z" class="F"></path><path d="M605 730h2c1 2 2 5 1 7-1 3-3 6-5 9h1l-2 3v-1c0-2 0-2 1-4l1-1c3-4 2-8 1-13z" class="C"></path><defs><linearGradient id="Bf" x1="603.349" y1="731.039" x2="610.478" y2="745.555" xlink:href="#B"><stop offset="0" stop-color="#787581"></stop><stop offset="1" stop-color="#9e9c9d"></stop></linearGradient></defs><path fill="url(#Bf)" d="M604 720l2 2 1 1c1 0 1 1 2 2h0c1 1 2 2 4 3v1l-1 1c1 0 2 1 2 2h-1v7c-1 4-3 8-5 12h-1c-1-1-2-3-3-5h-1c2-3 4-6 5-9 1-2 0-5-1-7h-2 0l-1-2h0c0-1 0-1 1-2h1v-3l-2-2v-1z"></path><path d="M606 723l1 1 1 2v3h-2l-1 1-1-2h0c0-1 0-1 1-2h1v-3z" class="AB"></path><path d="M606 723l1 1v3h-2l-1 1h0c0-1 0-1 1-2h1v-3z" class="AF"></path><path d="M604 720l2 2 1 1c1 0 1 1 2 2h0c1 1 2 2 4 3v1l-1 1c1 0 2 1 2 2h-1v7c-1 4-3 8-5 12l-1-1v-1c1-2 3-4 4-7h0l1-2c1-5-2-10-4-14l-1-2-1-1-2-2v-1z" class="C"></path><path d="M750 221h0l1 1h0 1 0v1c0 1 1 1 1 2v1c-1 1 0 1 0 2v2l-12 36-3 11-3 10v7h-3l1-1-2-1 1-3h-1c0-3 1-7 2-10 0-3 0-5 1-8 2-2 1-5 1-7 0-1 3-6 4-7 0 0 1 0 1-1l6-25 2-5h0c0-2 1-3 2-5z" class="o"></path><path d="M733 293c0-3 0-4 2-6v7h-3l1-1z" class="M"></path><path d="M735 274c1 2 1 2 0 4v2c0 2 0 3-1 4s-2 5-2 5h-1c0-3 1-7 2-10 0-2 1-3 2-5z" class="p"></path><path d="M734 271c2-2 1-5 1-7 0-1 3-6 4-7 0 0 1 0 1-1 0 2 0 3-1 5l-4 13c-1 2-2 3-2 5 0-3 0-5 1-8z" class="M"></path><path d="M750 221h0l1 1h0 1 0v1c0 1 1 1 1 2v1c-1 1 0 1 0 2v2l-12 36c0-1-1-1-1-2s1-4 1-6l3-9 4-19c0-2 1-3 0-4 0-2 1-3 2-5z" class="T"></path><path d="M750 221h0l1 1h0 1c0 2-1 4-3 6 0 0-1 1-1 2 0-2 1-3 0-4 0-2 1-3 2-5z" class="H"></path><path d="M788 180c1 1 2 1 3 2s3 1 5 2c3 1 7 2 10 4v-1c3 2 6 4 9 5l2 2h2l10 2c3 1 8-1 10 0v1l5-1c-1 1-2 1-3 1s-1 0-2 1h-2-1-2-3-3c-1 0-1 1-2 1l2 1c-1 0-1 1-2 1h-2v-1h0-2-1l-1-2-5 1c-1 0-5 2-5 2-1-1-1-1-2-1h-1l-1 1c-3 1-8 1-11 0l1-1c-4 0-7-3-10-5-2-1-4-2-4-4 1-1 0-1 1-1 1 1 3 1 4 3 3 2 7 5 10 6 1 0 1 0 2 1h0 3 1c1 0 1 0 2-1h2 1v-1h-4c-2-1-2-2-3-4 0-1-8-8-9-10v-1h-1c-1-1-2-1-3-2v-1z" class="AQ"></path><path d="M806 187c3 2 6 4 9 5l2 2h2l10 2c3 1 8-1 10 0v1c-4 1-10 1-14 0l-4-1c-5-2-8-3-12-6-1-1-2-2-3-2v-1z" class="s"></path><path d="M788 180c1 1 2 1 3 2s3 1 5 2c3 1 7 2 10 4 1 0 2 1 3 2v1c2 1 4 2 5 4h-1l1 1c-1 0-2 1-3 1h-2c0-1-1-1-2-1h-2-1c-1-1-2-1-3-2 0-1-8-8-9-10v-1h-1c-1-1-2-1-3-2v-1z" class="Z"></path><path d="M503 776h1c1 2 1 2 0 3 0 2-1 2-2 3v1h1v3l1 1h1l3-1-1 3-1 2h-2c-2 1-3 1-5 1h-2l-1 2c3 3 8 6 11 10h0-3c-6-9-17-14-27-17l-6-2c-3 0-6-1-8-2-1 0-1 0-1-1s1-1 1-2c3-1 5-1 8 0l2-1v-2c1 1 2 1 4 2 2 0 4-1 7-1l2 3h2c2 0 5 1 8 0 4-1 6-2 7-5z" class="b"></path><path d="M462 782l13 1c-2 1-3 1-4 2-3 0-6-1-8-2-1 0-1 0-1-1z" class="R"></path><path d="M473 777c1 1 2 1 4 2 2 0 4-1 7-1l2 3c-4-1-9-1-13-2v-2z" class="P"></path><path d="M475 783c3 1 6 2 8 2 4 2 7 3 10 4h7l4-2h1l3-1-1 3-1 2h-2c-2 1-3 1-5 1h-2l-1 2c3 3 8 6 11 10h0-3c-6-9-17-14-27-17l-6-2c1-1 2-1 4-2z" class="AO"></path><path d="M496 794c-1-1-1 0-2-1l-2-2h-1v-1c1 0 4 1 6 1 2 1 5 0 7 0-2 1-3 1-5 1h-2l-1 2z" class="AU"></path><path d="M504 787h1l3-1-1 3-1 2c-1-1-1-1-2-1h-3-1c-3 0-4 0-7-1h7l4-2z" class="M"></path><path d="M696 484c4-3 6-10 7-14l4-8h0c0-2 1-2 2-3l-4 15c1 6-3 9-4 14l-4 8c-1 6-4 12-6 17l-1 3v8l-6 9c-2 2-3 4-6 5-2 2-4 5-5 8l1 1h-2l-1-1 6-14 12-32 7-16z" class="i"></path><path d="M705 474c1 6-3 9-4 14l-4 8-1-1 3-7c1 0 1-2 2-3 1-4 3-7 4-11z" class="X"></path><path d="M677 532c1 2-1 6-2 8 1-1 0-1 1-1l4-6h-1v-1c0-1 0-2 1-2v-1c0-1 1-1 1-2v-1h0v2l-2 4c2-1 3-4 4-6l1 1c0 4-5 7-6 11-2 2-4 5-5 8l1 1h-2l-1-1 6-14z" class="v"></path><path d="M686 518h0c0-1 1-2 1-3s1-3 2-4c0-1 1-4 1-5v-1c0-2 2-4 2-6l1-1v1c1-1 1-1 1-2h0l2-2 1-4c1-1 1-2 2-3l-3 7 1 1c-1 6-4 12-6 17l-1 1-1 1v-1c-1 1-2 4-3 4z" class="L"></path><path d="M696 495l1 1c-1 6-4 12-6 17l-1 1-1 1v-1c1-3 1-5 2-7 1-1 1-2 1-2l4-10z" class="G"></path><path d="M689 514v1l1-1 1-1-1 3v8l-6 9c-2 2-3 4-6 5 1-4 6-7 6-11l-1-1c0-2 2-5 3-7v-1c1 0 2-3 3-4z" class="i"></path><path d="M689 514v1l1-1 1-1-1 3c-2 3-4 7-7 10 0-2 2-5 3-7v-1c1 0 2-3 3-4z" class="F"></path><path d="M636 653l1 1c1 0 6-1 7 0l2 1c1 1 3 1 4 2l1-1 1-1c2 2 4 4 6 7l1 1v1h-1c-1-3-3-5-6-6h-1l-4-1h0c-2-1-3 0-5-1h-6-2c-1 1-2 1-3 3-1 1-1 2-1 4-1 1-2 3-2 5v1c-1 3 0 8 0 11v2l1 1v2l1 2v1c0 2 0 3 2 4-1 1-2 2-4 2l1 1h-1c-1 0-1 0-2 1s-1 1-2 1c-1 1-1 2-1 3 1 1 2 1 3 1l-2 1c-2 0-3 0-4 1l-1 1v1h-7c-1 1-2 1-3 1-1-2 6-18 8-21l8-22 1-2 2-4c1-1 2-2 3-2 2-1 3-1 5-1v-1z" class="AB"></path><path d="M636 653l1 1c1 0 6-1 7 0l2 1c-2 0-4 0-5-1-1 0-2 0-3 1h-2c-2 0-2 0-3 1h-1c-2 1-2 2-3 3v1c-1 1-1 1-1 2l-2 4v2 1c0 2 0 8-1 10l-1-2v1h-1v2c0-1-1-3 0-4l2 1c0-4 0-7 1-10v-6l2-4c1-1 2-2 3-2 2-1 3-1 5-1v-1z" class="AF"></path><path d="M625 663l1-2v6c-1 3-1 6-1 10l-2-1c-1 1 0 3 0 4l-2 3v1c-1 1-1 2-2 3l-4 6s-1 1-1 2l-1 2-3 6v2h2c-1 1-2 1-3 1-1-2 6-18 8-21l8-22z" class="AH"></path><path d="M619 705l-4-1c-1 0-1 0-2-1 1-1 1-1 1-2 2-2 3-4 4-6s2-3 3-4c0-2-1-1 0-2 2-2 3-2 3-5l1-4 4 5 1 2v1c0 2 0 3 2 4-1 1-2 2-4 2l1 1h-1c-1 0-1 0-2 1s-1 1-2 1c-1 1-1 2-1 3 1 1 2 1 3 1l-2 1c-2 0-3 0-4 1l-1 1v1z" class="C"></path><path d="M683 442l1 2h0c-1 2-1 3-1 5-2 7-5 14-8 21l-18 47-52 138-17 41c-1 2-2 6-3 8-1 1-2 1-2 2-1 1-1 2-1 3-1 0-2 2-2 3-1-1-1-1-1-2 1-2 1-3 2-4v-1l3-8 11-24 88-231z" class="AO"></path><path d="M556 715h0c0 3 0 7-1 10l1 1v3l1 1c2-1 3-3 4-4l1-1c1-1 2-2 2-4l1-1c0 3 0 4-1 6s-2 4-1 6c0 1-1 3-1 4l1-1h0s1-1 2-1h0c0 1 1 2 1 3l2-2-4 7-4 5-2 1-5 6-19 17-2-1-5 6-1 1c-2 4-5 7-8 11-1 3-2 6-4 9-1 3-1 4-3 6l-4 1c-3-4-8-7-11-10l1-2h2c2 0 3 0 5-1h2l1-2 1-3c1-1 1-3 2-4h2l2-1 2-5 4-7c1-2 5-5 6-8v-2c1-2 4-3 4-5s1-3 2-5c1-1 5-4 6-5v-2l2 1c-1 3-2 5-2 8v2c2-3 6-6 7-10 2-4 4-8 5-13 0-2 1-3 2-4v-1c0-3 1-6 1-10h3z" class="x"></path><path d="M526 777l-3 2-1-1c1 0 2-1 2-2l2-2 1 2-1 1z" class="T"></path><path d="M565 720c0 3 0 4-1 6s-2 4-1 6c0 1-1 3-1 4l1-1h0s1-1 2-1h0c0 1 1 2 1 3l2-2-4 7-2-1-2 2-1-1v1c-2 1-5 7-7 8-1-2 2-4 3-5v-2l1-1c1-2 1-3 3-3h0 1v-1h-1l1-1v-2c1-1 1-2 1-2 1-2 1-3 1-4 1-2 1-3 1-5 1-1 1-2 1-4l1-1z" class="AC"></path><path d="M563 732c0 1-1 3-1 4l1-1h0s1-1 2-1c-2 2-3 4-4 6v-4l2-4z" class="J"></path><path d="M565 734h0c0 1 1 2 1 3l2-2-4 7-2-1-2 2-1-1 2-2c1-2 2-4 4-6z" class="Ac"></path><path d="M559 742l1 1 2-2 2 1-4 5-2 1-5 6-19 17-2-1 20-19c2-1 5-7 7-8v-1z" class="AY"></path><path d="M553 754c0-2 1-3 1-4 2-2 2-2 4-2l-5 6z" class="Ac"></path><path d="M532 749c1-1 5-4 6-5v-2l2 1c-1 3-2 5-2 8v2l-7 12c-1 1-2 4-3 5-2 3-5 5-7 7-1 2-3 3-3 5-1 1-1 3-2 4s-2 3-3 4h2v1l-1 1-1-1v3l1 2v1c-1 3-1 4-3 6l-4 1c-3-4-8-7-11-10l1-2h2c2 0 3 0 5-1h2l1-2 1-3c1-1 1-3 2-4h2l2-1 2-5 4-7c1-2 5-5 6-8v-2c1-2 4-3 4-5s1-3 2-5z" class="P"></path><path d="M510 782h2l2-1c-1 2-1 3-2 5h-1c0 1-1 3-2 3h-2l1-3c1-1 1-3 2-4z" class="V"></path><defs><linearGradient id="Bg" x1="794.81" y1="244.908" x2="820.689" y2="264.242" xlink:href="#B"><stop offset="0" stop-color="#727079"></stop><stop offset="1" stop-color="#afaeb0"></stop></linearGradient></defs><path fill="url(#Bg)" d="M769 293c11-24 20-49 42-64 12-9 27-10 41-8 0 4-6 8-9 10l-19 11-7 5-1 1c-4 2-7 6-9 9l-5 6c-3 3-7 10-9 15 0 1-1 3-1 4-2 5-4 9-5 15 1 1 1 1 1 2h1l1 1 1-1h2l1 1h-1c-5 0-12-1-17-3h-1l-3-2-3-2z"></path><defs><linearGradient id="Bh" x1="346.825" y1="448.177" x2="562.199" y2="465.016" xlink:href="#B"><stop offset="0" stop-color="#0e0707"></stop><stop offset="1" stop-color="#241010"></stop></linearGradient></defs><path fill="url(#Bh)" d="M428 137c9-2 18-2 28-2 11 1 20 4 30 10 2-1 3-1 5 0s6 4 8 5h0v2c0 1 2 3 2 4 3 6 3 13 3 19h0c0 1 0 3-1 4 0 3-1 6-2 9-1 1 0 1-1 2l-2 3-1-1c-1-2-1-3-2-5 0-2 0-2-2-4h0c0-6-2-11-7-16-10-9-23-9-36-9-14 1-25 5-36 13-2 1-4 2-5 4h-1c-6 5-12 12-16 20-1 1-2 3-2 5-2 4-4 10-4 14v10l-1 8h0 0v-1-2c-1-1-1-2-1-3v5c-1 3 0 5-1 8 0 12 1 23 3 35 0 2 0 6 1 7h1c1 2 1 4 1 6 0 1 0 2 2 3v1c0 1 0 3 1 4 0 2 0 3-1 4 1 8 3 16 5 24 4 14 9 27 14 41 1 1 2 6 3 7l3 6c3 2 6 10 8 14 3 4 5 8 8 11 1 1 3 2 3 3v1h-1l1 1 1-1c1 1 4 3 5 4l2-1v1c1 1 2 1 4 1v2c-4 6-6 15-6 22v3l1 2 1 1c1 1 1 2 2 3v-1c2 2 2 4 3 5l3 9h2c0 1 1 3 0 4 0 1 0 1-1 1l2 4h0c1 5 3 9 5 13 1 3 3 7 5 10 0 2 3 9 4 9l1 3 6 13c1 3 1 5 3 8l2 5 3 7 17 40 4 9 9 22 5 14c2 4 3 8 5 11 1 5 3 9 5 13l3 7c0 1 1 2 0 3 1 0 1 2 2 2 1 4 2 7 4 10l5 14c2 2 3 4 5 7l2 1h0l1-1h0l6-9c-1 2-1 4-2 6 0 1 0 2 1 3 1 0 1 0 1-1v4h0v1l-2 6 1 2c-2 1-2 2-2 4l1 1c0 1-1 1 0 2 0 1 0 1 1 2h2v2c0 2 0 4-1 6h0-3c0 4-1 7-1 10v1c-1 1-2 2-2 4-1 5-3 9-5 13-1 4-5 7-7 10v-2c0-3 1-5 2-8l-2-1v2c-1 1-5 4-6 5-1 2-2 3-2 5s-3 3-4 5v2c-1 3-5 6-6 8l-4 7-2 5-2 1h-2c-1 1-1 3-2 4l-3 1h-1l-1-1v-3h-1v-1c1-1 2-1 2-3 1-1 1-1 0-3h-1c-1 3-3 4-7 5-3 1-6 0-8 0h-2l-2-3c-3 0-5 1-7 1-2-1-3-1-4-2l-3-11-1 1c-2-6-4-13-6-19l-1 1c0-2-1-3-2-4s-2-4-2-6c-1-2-3-5-3-7h-1l-2 1v1c1 1 1 2 1 3v2 1l1 1h0c0 2 0 3 1 4 0 1 0 3 1 5 0 2 0 3-1 5v1c1 1 1 0 1 2h1v4l-1 1h0c1 2 1 4 1 6v2c0 1 0 2 1 3 0 1-1 1-1 2-1 2 1 2-2 4h0v2c1 1 1 1 1 2l-1 1c0 1 0 2 1 4 0 1 1 2 1 3 1 1 1 1 1 2v1l1 1v1c1 1 1 2 1 3 1 0 1 2 1 3-2-4-4-8-5-12l-9-21-1-4-3-8-1-3c-2-4-3-9-6-14 0-4-3-9-5-14v-2l-2-4v-1l-3-5-4-9c0-2-1-3-1-5l-4-10c-2-1-3-4-4-5-2-1-3-1-5-3 1-3 2-6 2-9 0-2-1-4-2-6l-5-12-2-5-4-9-8-20-1-3-7-18-4-9c-1-3-2-5-3-7v-2l2-1-1-2h-2l-1-1-1-3-3-8-9-23-1-2-2-4-2-7c-1-1-2-4-3-5l-6-14-4-11c-2-4-4-7-6-10-2-2-4-3-6-5l-2-2c-1 0-1 0-2-1l4-6 2-5h0l2-4 1 1 4-1c0-1 2-2 2-3 2-2 3-4 4-6v-3l2-6h1 1v-1c-1 0-3-1-4-1 2-1 4-1 6-1 5 0 10 0 15-1h1c3 0 6-1 9-2h2l1 1h1v-1l5 1h0c4 0 7 1 11 3 4 1 7 4 9 7v2c0 1 1 1 2 2h1v-4l-6-28v-4-6l-1-3h2l1-1-2-4-6-12v-2l1 1c1 1 3 2 4 3 4 1 8 2 12 1v-2l-4-7-2-1c-1-2-2-3-3-6l1-2-32-99v-1-1c0-1 0-1-1-2v-1-2l-2-7-5-26c-1-2-1-4-1-6v-11c1-1 0-4 0-5 1-1 1-2 1-3v-1-2c1-1 1-1 1-2 0-2 1-4 1-6 0-1 1-2 1-2v-1c0-1 1-2 1-3h0l1-1v-2h1c0-1 0-2 1-3v-1l2-3 1-2v-1c1-2 2-3 3-5l1-2c1-2 2-4 4-6l6-9c3-3 5-6 8-9l10-9c7-6 14-9 22-12z"></path><path d="M416 401v-3h1l3 3h-4z" class="d"></path><path d="M497 626c1 0 1 0 2 1 0 1 0 1-1 2-1 0-1 0-2-1 0-1 0-1 1-2z" class="w"></path><path d="M461 146c-3-1-6-1-8-1h1 1c3-1 9-1 11 0-2 0-3 0-5 1z" class="c"></path><path d="M403 385l1-2 4 9-2-1c-1-2-2-3-3-6z" class="Q"></path><path d="M468 148c3 1 5 1 7 3h-10c1-1 3 0 5-1h-1-1c-2 0-3-1-5-1h0 2 4 0l-1-1z" class="d"></path><path d="M387 281h1c1 2 1 4 1 6 0 1 0 2 2 3v1c0 1 0 3 1 4 0 2 0 3-1 4l-4-18z" class="k"></path><path d="M475 151h2v1h-2l2 2-9-2h-10l2-1c2-1 3 0 5 0h10z" class="AL"></path><path d="M499 638c1 2 1 3 2 5 0 1 1 2 2 4 0 1 0 2 1 3 0 0-1 1-1 2-1-1 0 0 0-1l-2-2v-1l-4-9 2-1zm-33-493h2l1 1h2c1 1 1 1 2 1 1 1 2 1 3 2h-2l1 1s1 0 2 1h-2c-2-2-4-2-7-3-2-1-5-2-7-2 2-1 3-1 5-1z" class="P"></path><path d="M499 160c2 3 4 6 4 10 0 1 0 5 1 5 0 1 0 3-1 4s-1 2-1 3l-1 3h-1c-1-2 0-2 0-3h0 1c0-1 0-2 1-3h0v-3-2-1c0-1 1-2 0-2v-2-2l-3-7z" class="g"></path><path d="M490 625c1 0 2 1 3 2h1l1 3 1 1 1 1c0 1 0 2 1 3l1 3-2 1-3-6c0-1-1-2-2-3 0-2-1-3-2-5z" class="Y"></path><path d="M383 239c-1-8 1-17 3-25v10l-1 8h0 0v-1-2c-1-1-1-2-1-3v5c-1 3 0 5-1 8z" class="L"></path><path d="M459 502h0v-1c0-1-1-2 0-3 0 1 1 2 1 3 1 1 1 2 2 3l-1 1c0 2 2 3 3 5 1 1 1 2 1 3s0 2 1 2c0 1 0 2 1 2l-2 1-1-2c-1-2-2-4-3-5v-2c1-1-2-5-2-7z" class="P"></path><path d="M416 377c3 2 6 10 8 14 0 2 1 3 1 5v2 1c-4-6-7-15-9-22z" class="E"></path><path d="M424 391c3 4 5 8 8 11l-1 1-1-1c-2 1-1 4-1 6-2-3-3-6-4-9v-1-2c0-2-1-3-1-5z" class="F"></path><path d="M443 441c1 1 1 2 2 3v-1c2 2 2 4 3 5l3 9h2c0 1 1 3 0 4 0 1 0 1-1 1l-9-21z" class="O"></path><path d="M456 496l-1-3c-1-1-2-2-2-3v-1c0-2 0-2-1-3v-1c-2-2-3-2-3-4-1-2-1-2-1-3-1-1-2-2-2-4h0c1 1 1 1 1 2 2 2 3 5 4 7l1-2c1 1 0 1 0 2 1 1 2 2 2 3 1 1 1 1 1 2l1 1v2c1 0 1 1 1 2l2 2-1 1h0c-1 0-1 1-1 2h0c-1-1-1-1-1-2z" class="P"></path><path d="M492 630c1 1 2 2 2 3l3 6 4 9c0 2 1 3 2 5 0 2 0 4 1 5v1 1l1 1c0 1 0 1 1 2v2c-1-2-1-3-2-5-1-1-1-1-1-2l-1-1v-1l-2-2-8-24z" class="c"></path><path d="M458 152h10l1 1s1 0 2 1l1 2h-3c-3-2-9 1-11-2-1 0-1-1-2 0h-1-1c-2 1-3 1-5 1-2 1-3 0-5 0-1 1-2 1-3 1l-2 1h-1c2-1 4-2 7-3 4-1 8-1 13-2z" class="V"></path><path d="M539 686v2l-11 27c-4 7-5 14-8 21v2l-1 1-1 3v-1l1-1v-4l1-2c4-11 8-23 13-35 2-4 4-9 6-13z" class="AK"></path><path d="M468 152l9 2c1 0 2 1 3 1l1 1c1 0 1 1 2 1v1l1 1h0c1 1 2 1 4 2 0 1 0 1 1 2 1 2 4 3 5 6-3-1-5-5-7-6-1-1-2-1-2-1-1-1-1-1-2-1-1-1-2-1-2-1-1-1-1-1-2-1h-1c-1-1-2-2-3-2s-2 0-3-1l-1-2c-1-1-2-1-2-1l-1-1z" class="Y"></path><path d="M486 145c2-1 3-1 5 0s6 4 8 5h0v2c0 1 2 3 2 4 3 6 3 13 3 19h0c-1 0-1-4-1-5 0-4-2-7-4-10-2-6-9-11-13-15z" class="AP"></path><path d="M532 735v5c0 1 1 2 2 3-1 2-2 4-2 6-1 2-2 3-2 5s-3 3-4 5v2c-1 3-5 6-6 8l-4 7c0-2 1-5 2-7 1-3 4-6 5-9l9-25z" class="V"></path><path d="M441 464l-1-1c0-2-1-3-1-5h0c-1-1 0-1-1-2 0-1-1-1-1-2-1-1-1-1-1-2v-1l-1-1c-1-1-1-2-1-3-1-2-1-1-1-2-1-1-1-2-1-3-1-1-1-2-2-2l-1-1v-1c-1-1-1-2 0-3-1-1-2-1-3-2s0-2-1-3c0-1-1-2-2-2h1c1 0 2 1 3 2v2h0c2 1 2 2 3 3l-1 1c0 1 1 2 2 3v1l2 1 1 1c-1-1 0-2-1-3 0 0-1-1-1-2-1-1 0-2-1-3s-2-2-2-4c-1-1 0-2-1-3s-1-1-1-2v-1c-1-1-1-1-2-1s-1-1-2 1h0c0 1-1 1-1 2h-1c1-2 1-3 3-4 1-1 1-1 2 0 1 0 2 1 2 2v1 1c1 1 1 2 2 3 0 2 1 3 2 4s1 2 1 3 0 2 1 3c0 0 1 0 1 1s-1 2 0 3 2 2 2 3l-1 1c-1-1-1-3-2-4v2c1 1 0 0 1 2 0 1 1 1 2 2v3s1 1 1 2h0c0-1 1-2 1-2 0 1 0 1 1 3v2h1v1c1 1 1 1 1 2h1c0 1 0 1 1 2h-1c-1-1-1-2-2-2h0c0 1 0 1 1 2 0 1 0 1 1 2 0 1 1 2 1 3 1 1 1 0 1 1h1v-1l1 1-1 1c0 1 1 1 1 1l1 1c1 1 1 2 1 3s1 1 2 2h0v2h0l-2-2c0-1-1-2-2-3l1-1h-1-1c-2-1-2-4-4-6v2h-1v-3c-1-1-1 0 0-1z" class="Y"></path><path d="M486 567c0 1 1 2 1 3l2 2v2c1 0 1 1 1 2v1c1 0 1 1 1 2 1 1 1 1 1 2 1 1 1 1 1 3 0 1 1 1 1 2l1 3c1 0 0 0 1 1 0 1 1 2 1 3v2h0c0 1 0 1 1 2v2c1 1 1 2 2 3h0c1 1 1 2 2 3v3s1 1 1 2 0 1 1 2v1c0 2 1 2 1 3l2 4v3l5 14h0c-1-2-1-1-1-2-1-2-1-4-2-6l-1-1v-2l-1-1v-1c-1-1 0-2-1-2 0-1 0-2-1-3 0-1 0-2-1-3v3l1 1v1c0 1 0 2 1 3v2l1 1v1 1s2 1 2 2-1 3 0 5c1 1 1 1 1 3l1 1c1 2 1 3 2 5v1c0 1 1 2 1 4 0 1 1 2 1 3h0c1 1 1 2 1 3s1 2 1 4-1 5 0 7v2h-1v-3h-1v-1c1-2 1-5 0-6v-2-1c-1-1-1-2-1-3v-1c-1-1-1-2-2-3h0v-2c-1-2-1-4-1-5-1-2-2-3-2-4s-1-1-1-2c-1-2 0-3 0-5h-1l-1-1c0-1-1-4-1-5s-1-1-1-2c-1-1 0-3-1-4-1-2 0-4-1-6v-1c0-1-1-2-1-2l-1-1v-1c-2-2-2-4-2-6h0l-2-7c0-1-1-1-1-2 0-3-2-7-4-10 0-2 0-4-1-5-1-4-3-7-4-11z" class="AN"></path><defs><linearGradient id="Bi" x1="416.059" y1="427.138" x2="424.127" y2="427.701" xlink:href="#B"><stop offset="0" stop-color="#0e0406"></stop><stop offset="1" stop-color="#230909"></stop></linearGradient></defs><path fill="url(#Bi)" d="M418 411v-1-2c0-2 1-4 3-5 0 2 0 2-1 4 0 2 1 6 0 8s0 8 0 10c1 1 1 1 1 2v4c0 1 0 0 1 2v1 4c0 1 0 0 1 1 0 2-1 3 0 5 0-2 0-5 1-7 0 4 1 7 2 11 0 2 0 4 1 6l-1 2 1 1c-2-1-2-2-3-4l-1-3v1h-1l-2-7-2-9v-4l-1-8v-1c0-4 0-7 1-11z"></path><path d="M420 425c1 1 1 1 1 2v4c0 1 0 0 1 2v1 4c0 1 0 0 1 1 0 2-1 3 0 5 0-2 0-5 1-7 0 4 1 7 2 11 0 2 0 4 1 6l-1 2c-1-4-3-8-4-12-1-6-2-13-2-19zm129 262h0c0 2-1 4-1 6l-3 10-4 17-3 10c-1 3-3 6-3 9 0 1-1 2-1 4-1-1-2-2-2-3v-5l3-7c2-6 3-11 4-17l6-17c1-2 3-5 3-7h1z" class="w"></path><path d="M535 728v7c-1 0 0 1 0 2v2c0 1-1 2-1 4-1-1-2-2-2-3v-5l3-7z" class="y"></path><path d="M549 687l1-1c0 4 0 6-2 10-1 3-1 7-1 11l-5 23h1c0 1-1 1-2 2v1c0 1 0 2-1 3 1 1 0 1 0 3l1 1c0 1 0 2-1 3l-2-1v2c-1 1-5 4-6 5 0-2 1-4 2-6 0-2 1-3 1-4 0-3 2-6 3-9l3-10 4-17 3-10c0-2 1-4 1-6z" class="B"></path><path d="M500 654c1 0 0 0 1 1v1 1c-1-1-2-2-2-4h0l-1-1v-1c-1-1-2-1-2-2v-1c0-1-1-1-1-2v-1-1c-1 0 0-1-1-2s-1-3-1-4c-1-1-1-2-1-3-1 0-1 0-1-1v-1-3c-2-2 0-1 0-2-1 0-1-2-1-2-1-2-1-3-2-4l-1-1v-3c-1-1-1-2-2-3 0-1 0-2-1-2v-1-2c-1-1-1-3-1-4-1-1-2-1-2-3v-1c-1-1-1-1-1-2l-1-1v-1l-1-2c-1-1-1-1-1-2 0-3-1-4-2-6v-2-1c-1-1 0-1-1-2s-1-2-1-3l-1-1h0l-1-1h1c-1-1-1-1-1-2h1v1l7 16c1 1 2 3 2 4 1 2 2 3 3 5 0 2 0 3 2 5 0 2 1 3 2 5l1 3c1 2 1 3 2 4 1 2 3 4 3 7v1h-1c-1-1-2-2-3-2 1 2 2 3 2 5l8 24z" class="d"></path><path d="M479 593c1 1 2 3 2 4 1 2 2 3 3 5 0 2 0 3 2 5 0 2 1 3 2 5l1 3c1 2 1 3 2 4 1 2 3 4 3 7v1h-1c-1-1-2-2-3-2-2-5-4-10-5-15-1-1-1-2-1-3-1-3-3-6-4-9 0-1 0-2-1-3v-1-1z" class="V"></path><path d="M491 619c1 2 3 4 3 7v1h-1c-1-3-2-5-2-8z" class="y"></path><path d="M450 158v-1c1 0 3-1 4-1h1c3 1 6 0 10 1l5 1h0c2 0 2 0 3 1s2 0 3 1h1c1 1 2 1 2 1l1 1c2 0 3 1 4 2l2 2c1 0 1 1 2 1l1 1h1 1l-4-5c2 1 4 5 7 6h1 1c2 0 2 1 4 2h0c-2 0-2 0-3-1l-2 1 1 2v1c1 2 3 3 4 5v3h0c0 1-1 1 0 3h1l1-3c0-1 0-2 1-3 0 3-1 6-2 9-1 1 0 1-1 2l-2 3-1-1c-1-2-1-3-2-5 0-2 0-2-2-4h0c0-6-2-11-7-16-10-9-23-9-36-9z" class="B"></path><path d="M547 686c-2 2-3 6-4 10-3 6-6 13-8 20-2 6-3 12-5 18 0-1 0-2-1-2h0c-1-2 2-3 2-5l-2 1h0c1-2 1-3 2-5v-1-2-3h1 0c1-1 1-2 1-3l3-5v-1l2-5v-1l1-1v-1c0-1 1-2 1-3h0v-1c1-1 0-2 1-3 1-2 2-3 3-5 0-2 0-2 1-3 0-1 0-1-1-2l-1-1v-1l-2-2v-1-1l-1-1c-1-1-1-2-1-3h-1c0-2 0-3-1-4h0v-1c0-1-1-1-1-2v-1l-1-1c0-1 0-2-1-3v-1c-1-1-1-1-1-2h-1l1-1-1-2h-1v-1c0-1 0-2-1-2v-1-1c0-1-1-1-1-2s0-1-1-1v-1-1l-1-1v-1-1c-1-1-2-3-3-5h0v-2h-1v-2c-1-1-1-2-1-3l-1-1v-1l-1-1c0-1 0-2-1-2v-2l-1-1c0-1 0-2-1-2v-1-1l-1-1-3-9c-1-1-1-1-1-2s0-1-1-1v-1-1l-1-1v-2c-1 0-1 0-1-1s0-1-1-2h0v-1c0-1-1-1-1-2 0-2-1-3-2-5v-1l-1-2c0-1-1-1-1-2l-1-1 1-1-2-1 1-1c-1-1-1-1-1-2h-1v-1l-1-2h0l1-1 4 9 9 22 5 14c2 4 3 8 5 11 1 5 3 9 5 13l3 7c0 1 1 2 0 3 1 0 1 2 2 2 1 4 2 7 4 10l5 14c2 2 3 4 5 7z" class="d"></path><path d="M467 518h2 0c0 1 1 2 2 4v1 1 1c1 1 1 2 1 4l1 1c0 1 0 2 1 3 0 0 0 1 1 2v1h0c1 2 2 4 2 6h0l1 1v1 1c0 1 0 0 1 1v2l1 1v2 1h0c1 1 1 2 2 3v2 1h1v2l1 1s1 1 1 2v3l1 1h0c1 4 3 7 4 11 1 1 1 3 1 5 2 3 4 7 4 10 0 1 1 1 1 2l2 7h0 0c0-1-1-1-1-2v3h1v4c1 2 1 3 1 5v1l1 2 1 1v3l1 1v1c0 2 0 3 2 5h0v3c1 2 0 5 2 6 0 1 0 1 1 1v3l-1 1v2 5h-1c0-1-1-2 0-3v-3-1c1-1 1-3 0-4l-1-1c-1-2 0-4-1-6 0-1 0-1-1-3h1c-1-1-2-2-2-3s0-3-1-4 0-3-1-4c0-1-1-2-1-3-1-1 0-3-1-4 0-2-1-5-1-7-1-1-1-2-2-3v-1-1c-1-1-1-1-1-3-1-1-1-1-1-2s0-2-1-3v-2h0c-1-2-1-4-1-5-1-1-2-2-2-3v-1c0-2 0-3-1-5 0 1 0 1-1 2h0c0-2 0-4-1-5 0-1 0-1-1-2 0-1 0-2-1-3s-1-1-1-3v-1l-1-2c0-1-1-2-1-2-1-1-1-2-2-3h-1v-1c-1-1-1-1-1-2v-3l-1-3c-1-1-1-1-1-2 1 0 1 1 1 1 0 1 1 1 1 2v-2c0-1-1-2-1-4-1-2-2-3-3-5v-1c-1-3-1-5-3-8l1-1c0-1-1-1-1-2s0-1-1-2l-1-1v-1z" class="P"></path><path d="M476 547c1 2 2 4 2 6h-1v-1c-1-1-1-1-1-2v-3z" class="Y"></path><path d="M484 590l1-1-1-1v-1h2c1 1 2 1 3 3v1 2c0 1 0 1 1 2s1 2 1 4l1 2c0 1 0 2 1 3h0c0 1 0 1 1 2 1 2 0 2 0 4 0 1 2 2 2 3v2c0 1 0 1 1 2 0 1-1 4 0 5 0 2 2 2 0 4h0c-2 0-2 1-3 0 0-3-2-5-3-7-1-1-1-2-2-4l-1-3c-1-2-2-3-2-5-2-2-2-3-2-5-1-2-2-3-3-5v-5l2 5h0l1 1c0 1 1 1 1 2l2 2h1c0-2-1-2-2-3v-6l-2-3z" class="g"></path><path d="M481 592l2 5h0 0c0 2 1 3 2 5 0 2 1 3 1 5-2-2-2-3-2-5-1-2-2-3-3-5v-5z" class="W"></path><path d="M429 408c0-2-1-5 1-6l1 1 1-1c1 1 3 2 3 3v1h-1l1 1 1-1c1 1 4 3 5 4l2-1v1c1 1 2 1 4 1v2c-4 6-6 15-6 22v3l-12-30z" class="G"></path><path d="M435 407l1-1c1 1 4 3 5 4 0 1 1 1 1 3l-7-6z" class="C"></path><path d="M556 677c-1 2-1 4-2 6 0 1 0 2 1 3 1 0 1 0 1-1v4h0v1l-2 6 1 2c-2 1-2 2-2 4l1 1c0 1-1 1 0 2 0 1 0 1 1 2h2v2c0 2 0 4-1 6h0-3c0 4-1 7-1 10v1c-1 1-2 2-2 4-1 5-3 9-5 13-1 4-5 7-7 10v-2c0-3 1-5 2-8 1-1 1-2 1-3l-1-1c0-2 1-2 0-3 1-1 1-2 1-3v-1c1-1 2-1 2-2h-1l5-23c0-4 0-8 1-11 2-4 2-6 2-10h0l6-9z" class="q"></path><path d="M556 677c-1 2-1 4-2 6 0 1 0 2 1 3 1 0 1 0 1-1v4h0v1l-2 6 1 2c-2 1-2 2-2 4l1 1c0 1-1 1 0 2 0 1 0 1 1 2h2v2c0 2 0 4-1 6h0-3c-2 0-3 1-4 2l-1-1v-1h0v-1c0-3 0-4 1-6 1-5 0-9 1-14 1-3 1-5 0-8l6-9z" class="t"></path><path d="M557 709c0 2 0 4-1 6h0v-4h0l1-2z" class="H"></path><defs><linearGradient id="Bj" x1="463.32" y1="507.96" x2="449.561" y2="513.644" xlink:href="#B"><stop offset="0" stop-color="#3a1819"></stop><stop offset="1" stop-color="#5c1c1e"></stop></linearGradient></defs><path fill="url(#Bj)" d="M424 437v-1l2-1c1 1 1 2 2 4 0 0 0 1 1 2 0 0 1 1 1 2 1 1 0 0 1 2 0 1 2 3 2 4 1 1 1 1 1 2l1 1v1h0v1c1 1 0 1 1 1 0 1 0 2 1 3v1h0c1 2 3 3 4 5-1 1-1 0 0 1v3h1c0 1 0 1 1 2l1 2h0v1c1 0 1 1 1 2v1h0c1 1 1 2 2 3v1h0c1 1 0 0 1 2 0 2 2 4 3 7v1c1 1 1 2 2 3v1c1 1 1 1 1 2 1 1 0 1 1 2v1l1 2c0-1 1-1 0-2v-3c0 1 0 1 1 2h0c1 1 1 1 1 3h0l1 1c0 2 3 6 2 7v2c1 1 2 3 3 5l1 2 2-1v1 1l1 1c1 1 1 1 1 2s1 1 1 2l-1 1c2 3 2 5 3 8v1c1 2 2 3 3 5 0 2 1 3 1 4v2c0-1-1-1-1-2 0 0 0-1-1-1 0 1 0 1 1 2l1 3v3c0 1 0 1 1 2v1 2l1 1c0 1 0 2 1 3 0 1 0 2 1 3s1 2 1 3v1c0 1 0 1 1 2v3c1 0 0 0 1 1 0 1 0 1 1 2v2c0 1 0 2 1 3 0 1 1 2 1 3 1 1 1 1 1 2l1 2c-1 0-1 1-2 1h-2v1l1 1-1 1v-1c0-1 0-1-1-2-1-2-2-4-2-6 0-1-1-1-1-2v-4l-2-1c0-2-1-4-1-6 0-1-1-1-1-1l1-2c-1-1-1-2-2-3l-1-1c0-2-1-4-2-6v-5c-2-2-2-3-3-5-1-3-2-5-2-7-1-1-1-2-1-3-1-3-2-6-4-9-1-2-3-5-4-7 0-2 0-4-1-5-2-6-5-11-7-16 0-2-1-3-2-4v-1l1-2-3-2c0-4-3-7-4-11-1-2-1-3-2-5-1-1-2-2-3-4-1-1-1-3-2-5s-1-3-1-5h-2c0-2 0-2-1-3h-1c1 1 1 1 1 2-1 2 1 6 1 9h0c-1 1 0 1-1 1l-4-14c-1-2-1-4-1-6-1-4-2-7-2-11z"></path><path d="M456 496c0 1 0 1 1 2h0c1 1 1 1 1 3h0l1 1c0 2 3 6 2 7v2l-1-1v-1c0-1-1-1-2-2v-3c-1-1-2-1-2-3 0-1 1-1 0-2v-3z" class="Y"></path><path d="M427 454l4 14c1 0 0 0 1-1h0c0-3-2-7-1-9 0-1 0-1-1-2h1c1 1 1 1 1 3h2c0 2 0 3 1 5s1 4 2 5c1 2 2 3 3 4 1 2 1 3 2 5 1 4 4 7 4 11l3 2-1 2v1c1 1 2 2 2 4 2 5 5 10 7 16 1 1 1 3 1 5 1 2 3 5 4 7 2 3 3 6 4 9 0 1 0 2 1 3 0 2 1 4 2 7 1 2 1 3 3 5v5c1 2 2 4 2 6l1 1c1 1 1 2 2 3l-1 2s1 0 1 1c0 2 1 4 1 6l2 1v4c0 1 1 1 1 2 0 2 1 4 2 6 1 1 1 1 1 2v1l2 3v6c1 1 2 1 2 3h-1l-2-2c0-1-1-1-1-2l-1-1h0l-2-5v5c0-1-1-3-2-4l-7-16c0-3-1-5-1-7l-1-1h-1l-1-2c-1-1-1-2-2-3v-2l-1-1v-1l-1 2-24-63h0l-8-20c0-1 0-3 1-4l-4-9-2-9-1-1 1-2z" class="S"></path><path d="M458 536c1 2 2 3 3 4 0 2 0 3 1 4 1 3 2 5 3 7v4c-3-5-5-13-7-19z" class="Q"></path><path d="M471 570l10 22v5c0-1-1-3-2-4l-7-16c0-3-1-5-1-7z" class="g"></path><path d="M427 454l4 14c1 0 0 0 1-1 1 4 2 6 3 9s1 5 2 7c3 5 5 9 5 15-3-4-4-10-6-14l-3-9-4-9-2-9-1-1 1-2z" class="W"></path><path d="M433 475l3 9c2 4 3 10 6 14l1 1c-1 0-2-1-3-1v1h0l-8-20c0-1 0-3 1-4z" class="AN"></path><path d="M440 499v-1c1 0 2 1 3 1l13 34 2 3c2 6 4 14 7 19l5 14h-1l-1-2c-1-1-1-2-2-3v-2l-1-1v-1l-1 2-24-63z" class="d"></path><defs><linearGradient id="Bk" x1="442.576" y1="555.664" x2="466.817" y2="545.19" xlink:href="#B"><stop offset="0" stop-color="#380a0b"></stop><stop offset="1" stop-color="#871b1a"></stop></linearGradient></defs><path fill="url(#Bk)" d="M395 396l1 1c1 1 3 2 4 3 4 1 8 2 12 1h1 3 4l1 1h0v1c-2 1-3 3-3 5v2 1c-1 4-1 7-1 11v1l1 8v4l2 9 2 7h1v-1l1 3c1 2 1 3 3 4l2 9 4 9c-1 1-1 3-1 4l8 20h0l24 63 31 89 1 3v2h0c1 1 1 1 1 2v2h1l1 5 1 2 2 5c0 1 1 2 1 3v1s1 1 1 2h0l1 1c0 1 1 2 1 3l-2-1-1 1c-1-2-2-3-4-3h-1c-1-1-1-1-1-2h-1v2 2l1 1v1l1 3 1 1v1 2h-1v1l-3-6c-1-1-1-1-2-1h0c0 1 0 1-1 2v-2c-1-1-1-3-3-3v-2l-4-7c0-1-2-3-2-4l-3-3c0-2-1-3-2-4l-3-4-2-2-5-6c-1 1-1 1-3 0-1-1-2-2-3-4v-3h0c0-1-1-3-1-3v-1l1 1c1-1 1-1 0-2 0-2-1-2-1-4v-2h0l-5-10c-1-1-1-2-2-3h-3c-1 1-2 0-3 0-1-1-1-3-2-4-2-3-3-6-4-9l-2-2v-2l-1-6v-3l-1-2v-2c0-1 0-3-1-5h1c-1-1-2-1-3-2l-1-1h1l-2-1-5-2-6-3-2-2c1 0 2-1 3-1v-1l-1-3c1-1 2-2 4-3 0-1 1-2 1-3 3-5 5-12 6-19v-1h2v-5l2-1c0 1 1 1 1 2h1l-3-11-1-3v-1h-1l-1-1c-1 1-1 1-1 2 0-1-1-2-2-4v2c0-1-1-2-1-3v-2l-1-4c0-2-1-3-2-5h-2l-1-3h-1l-1-1c-1-2-1-4-1-6v-3l-1-2 1-1c0-1-1-2-1-3s0-1-1-2v-1c-1-2-2-6-4-7l-1-1c0-1-1-1 0-2l-1-2h1c-2-5-4-10-5-15l-1-2-6-28v-4-6l-1-3h2l1-1-2-4-6-12v-2z"></path><path d="M415 477l2-1 1 2-2 1-1-2z" class="R"></path><path d="M436 522l2 4h-2l-1-3 1-1zm34 98l2-2 2 6c-1 0-1 0-1-1h-1-1l-1-3z" class="a"></path><path d="M490 665h0l1 2 1 1v1l1 1v2h1v4l-4-11z" class="c"></path><path d="M461 589l3 9h-1l-1-1c-1-2-1-4-2-5l-1-1 2-2z" class="a"></path><path d="M416 439c1-2 0-3 2-4l2 9-1 1v-1-1l-1 1c-1-1-1-3-1-4l-1-1z" class="u"></path><path d="M438 526l3 6 1 4c-1 0-1 1-2 1h-1l-3-11h2z" class="AA"></path><path d="M440 537v-4l1-1 1 4c-1 0-1 1-2 1z" class="a"></path><path d="M422 451h1v-1l1 3c1 2 1 3 3 4l2 9c-1-1-2-3-2-4l-1 1-4-12z" class="AN"></path><path d="M426 463l1-1c0 1 1 3 2 4l4 9c-1 1-1 3-1 4l-6-16z" class="AL"></path><path d="M431 512l1-2 4 12-1 1v-1h-1l-1-1c-1 1-1 1-1 2 0-1-1-2-2-4v-2c0-1 0-2 1-3v-2z" class="R"></path><path d="M430 517c0-1 0-2 1-3 2 3 3 5 4 8h-1l-1-1c-1 1-1 1-1 2 0-1-1-2-2-4v-2z" class="e"></path><path d="M414 444v6c0 3 0 6 1 8s2 6 2 8c1 1 2 3 2 4 0 0-1 0-1 1 0 0 0 1 1 2-1-1-2-2-2-3s0-1-1-2l-1-2v-1l-1-2v-1c0-3-1-5-2-7l1-1v1-10l1-1z" class="B"></path><path d="M448 553l7 20h-2c-2-6-4-11-6-16v-1c0-2 0-2 1-3z" class="AO"></path><path d="M442 536c1 1 1 2 1 3l5 12v2c-1 1-1 1-1 3v-1c-1-1-2-4-3-6h0c-1-2-2-6-3-8l-1-4c1 0 1-1 2-1z" class="a"></path><path d="M447 555v-3l1-1v2c-1 1-1 1-1 3v-1z" class="R"></path><path d="M442 536c1 1 1 2 1 3 0 2-1 2 0 4l1 1 1 4-1 1h0c-1-2-2-6-3-8l-1-4c1 0 1-1 2-1z" class="AA"></path><path d="M486 657c1 2 3 7 4 8l4 11 4 14v1l-3-6-1-4-1-3c0-2-2-4-3-6l-5-14 1-1z" class="R"></path><path d="M409 421h0c2-2 6-7 7-9l1-1h1c-1 4-1 7-1 11l-1-6c-2 3-3 4-4 7h0c-1 1-1 1-1 2-1 2-3 4-2 5 1 3 1 3 0 6 0 1 0 2 1 3 0 1-1 2 0 4 0 1 0 2 1 3v3c0 1 0 1 1 2h0v3 1c1 2 2 4 2 7-1-1-1-2-1-3h0c-1-2-1-2-1-3v-1l-1-1c0-1-1-3-1-5v-2h0v-2c-1-2-1-3-1-5v-3c-1-1-1 0-1-1v-2l-1-3v-2-1c1-1 0-2 1-4 0-1 1-2 1-3z" class="W"></path><path d="M447 557c2 5 4 10 6 16h2l1 2 2 4 3 10-2 2 1 1c-3-1-6-4-8-6-1-1-3-2-4-3h1c1 1 0 0 2 1h0l1 1v-2l1 1h1c0-2-1-3-1-5v-1-1l-1-1c0-1 0-1-1-2 0-2-1-4-1-5s-1-2-1-2v-2l-1-1v-2c-1-2-1-3-1-5z" class="H"></path><path d="M455 573l1 2-2 1-1-3h2z" class="R"></path><path d="M456 575l2 4h-3l-1-3 2-1z" class="a"></path><path d="M455 579h3l3 10-2 2c-2-4-3-8-4-12z" class="AA"></path><path d="M412 455v-1-3h0c-1-1-1-1-1-2v-3c-1-1-1-2-1-3-1-2 0-3 0-4-1-1-1-2-1-3 1-3 1-3 0-6-1-1 1-3 2-5 0-1 0-1 1-2h0c1-3 2-4 4-7l1 6v1l1 8v4c-2 1-1 2-2 4v-1c-2 2-2 3-2 6l-1 1v10-1l-1 1z" class="Q"></path><path d="M416 438c0-2 0-4-1-6v-3c0-2 0-5 1-7l1 1 1 8v4c-2 1-1 2-2 4v-1z" class="S"></path><path d="M419 488c0-1 0-1-1-2v-1c-1-2-2-6-4-7l-1-1c0-1-1-1 0-2l-1-2h1l2 4 1 2 2-1 11 25c1 2 3 5 3 7l-1 2v2c-1 1-1 2-1 3v2 2c0-1-1-2-1-3v-2l-1-4c0-2-1-3-2-5h-2l-1-3h-1l-1-1c-1-2-1-4-1-6v-3l-1-2 1-1c0-1-1-2-1-3z" class="AA"></path><path d="M422 492c3 5 5 11 7 17l2 3v2c-1 1-1 2-1 3v2 2c0-1-1-2-1-3v-2l-1-4c0-2-1-3-2-5-1-3-1-5-2-7-1-3-2-5-2-8z" class="AG"></path><path d="M429 509l2 3v2c-1 1-1 2-1 3-1-3-2-5-1-8z" class="AE"></path><path d="M419 488c0-1 0-1-1-2v-1c-1-2-2-6-4-7l-1-1c0-1-1-1 0-2l-1-2h1l2 4 1 2 6 13c0 3 1 5 2 8 1 2 1 4 2 7h-2l-1-3h-1l-1-1c-1-2-1-4-1-6v-3l-1-2 1-1c0-1-1-2-1-3z" class="S"></path><path d="M424 500c1 2 1 4 2 7h-2l-1-3h-1l-1-1 2-2c0 1 1 1 1 2v1-1-3z" class="J"></path><path d="M435 577l3 1 10 5c1 1 3 2 4 3 2 2 5 5 8 6 1 1 1 3 2 5l1 1h1l8 20-2 2-3-6c-1-5-3-11-6-15-4-5-12-10-18-13 0 0-4-3-5-3-1-1-2-1-3-2l-1-1h1l-2-1c1-1 2-1 2-2z" class="R"></path><path d="M452 590l1-1-2-2h0l1-1c2 2 5 5 8 6 1 1 1 3 2 5h-1c-1 0-1-1-1-1-3-2-5-4-8-6z" class="AD"></path><path d="M435 577l3 1 10 5c1 1 3 2 4 3l-1 1h0l2 2-1 1-17-10-2-1c1-1 2-1 2-2z" class="b"></path><path d="M444 549h0c1 2 2 5 3 6v1 1c0 2 0 3 1 5v2l1 1v2s1 1 1 2 1 3 1 5c1 1 1 1 1 2l1 1v1 1c0 2 1 3 1 5h-1l-1-1v2l-1-1h0c-2-1-1 0-2-1h-1l-10-5v-1-1c1-2 1-4 1-6 0-3 1-5 1-8v-4h0l2-3 1 2h0l-1-6v-1c1 1 1 2 1 3h0 1c0-1-1-2 0-3v-1z" class="B"></path><path d="M440 558l2-3 1 2v1c2 5 5 13 4 18l-1 1v-1c-1-2-3-3-4-4-3-3 0-7-2-10v-4h0z" class="Q"></path><path d="M440 558l2-3 1 2v1c-3 2-2 6-2 9 0 2 0 4 1 5-3-3 0-7-2-10v-4h0z" class="H"></path><defs><linearGradient id="Bl" x1="457.309" y1="593.629" x2="454.715" y2="605.264" xlink:href="#B"><stop offset="0" stop-color="#3c0a0c"></stop><stop offset="1" stop-color="#721819"></stop></linearGradient></defs><path fill="url(#Bl)" d="M438 583c1 0 5 3 5 3 6 3 14 8 18 13 3 4 5 10 6 15v1c-1-1-1-3-1-4-1-1-1-2-1-3h-3c-1-1-2-2-4-2l-2-2-1 1c-1 1 0 2 0 2v2 1l-1 2h0l-1 2c1 1 2 2 2 3l1 4c-1-1-1-2-2-3h-3c-1 1-2 0-3 0-1-1-1-3-2-4-2-3-3-6-4-9l-2-2v-2l-1-6v-3l-1-2v-2c0-1 0-3-1-5h1z"></path><path d="M452 602l1 1c1-1 2-1 3 0l2 1h-2l-1 1c-1 1 0 2 0 2v2l-2-3v1l-1-1-1-2c0-1 0-1 1-2z" class="H"></path><path d="M452 602l1 1v3 1l-1-1-1-2c0-1 0-1 1-2z" class="Y"></path><path d="M438 583c1 0 5 3 5 3 1 2 2 3 4 4 1 1 1 2 2 4s1 4 2 6l1 2c-1 1-1 1-1 2l-2-3h0c-2 0-2 0-3-1v-1c-2 0-1 1-2 0l-2-3-3-3v-1l-1-2v-2c0-1 0-3-1-5h1z" class="c"></path><path d="M449 599l2 1 1 2c-1 1-1 1-1 2l-2-3v-2z" class="P"></path><path d="M438 590c2 1 4 2 5 4 2 2 4 3 6 5v2h0c-2 0-2 0-3-1v-1c-2 0-1 1-2 0l-2-3-3-3v-1l-1-2z" class="B"></path><path d="M438 583c1 0 5 3 5 3 1 2 2 3 4 4 1 1 1 2 2 4v3 1l-1-1c-2-1-4-5-6-6-1-2-2-2-4-3 0-1 0-3-1-5h1z" class="d"></path><path d="M439 592v1l3 3 2 3c1 1 0 0 2 0v1c1 1 1 1 3 1h0l2 3 1 2 1 1v-1l2 3v1l-1 2h0l-1 2c1 1 2 2 2 3l1 4c-1-1-1-2-2-3h-3c-1 1-2 0-3 0-1-1-1-3-2-4-2-3-3-6-4-9l-2-2v-2l-1-6v-3z" class="W"></path><path d="M453 606l2 3v1l-1 2h0l-2-6 1 1v-1z" class="w"></path><path d="M439 592v1l3 3 2 3c1 1 0 0 2 0v1c1 1 1 1 3 1h0l2 3 1 2-2-1c-3-1-4-1-5-4-1-1-2-1-3-1-1-1-2-4-3-5v-3z" class="g"></path><path d="M442 605c1 1 2 2 2 4l1-1v-2c1 0 1 0 1 1 1 1 3 2 5 4l2 3h0c1 1 2 2 2 3l1 4c-1-1-1-2-2-3h-3c-1 1-2 0-3 0-1-1-1-3-2-4-2-3-3-6-4-9z" class="H"></path><path d="M453 614h0c1 1 2 2 2 3l1 4c-1-1-1-2-2-3h0c-1-2-1-3-1-4z" class="Q"></path><path d="M435 536l2-1c0 1 1 1 1 2h1 1l1 4c1 2 2 6 3 8v1c-1 1 0 2 0 3h-1 0c0-1 0-2-1-3v1l1 6h0l-1-2-2 3h0v4c0 3-1 5-1 8 0 2 0 4-1 6v1 1l-3-1c0 1-1 1-2 2l-5-2-6-3-2-2c1 0 2-1 3-1v-1l-1-3c1-1 2-2 4-3 0-1 1-2 1-3 3-5 5-12 6-19v-1h2v-5z" class="AL"></path><path d="M433 541h2c-1 10-3 21-11 28v1h-1l-1-3c1-1 2-2 4-3 0-1 1-2 1-3 3-5 5-12 6-19v-1z" class="R"></path><path d="M426 564c0 2-1 3-2 5v1h-1l-1-3c1-1 2-2 4-3z" class="AA"></path><path d="M440 537l1 4c1 2 2 6 3 8v1c-1 1 0 2 0 3h-1 0c0-1 0-2-1-3v1l1 6h0l-1-2-2 3h0v4c0 3-1 5-1 8 0 2 0 4-1 6v1 1l-3-1c0 1-1 1-2 2l-5-2-6-3-2-2c1 0 2-1 3-1v-1h1c0 1 0 1 1 2 1 0 3 1 5 0h1s1-1 2-1c0-1 3-4 4-5 0-2 1-7 1-9 0-4 2-9 1-13-1-2-1-5-1-7h1 1z" class="P"></path><path d="M432 575h0c2-1 2-4 3-6l1-1c0 1 1 1 2 2h0c-1 2 0 4-2 6l-1 1-2-2h-1z" class="V"></path><path d="M433 575h1l1-1 3-4c-1 2 0 4-2 6l-1 1-2-2z" class="g"></path><path d="M440 558h0v4c0 3-1 5-1 8 0 2 0 4-1 6v1 1l-3-1h0l1-1c2-2 1-4 2-6h0v-1c0-4 1-7 2-11z" class="W"></path><path d="M422 574c2-1 4-1 6 0l4 1h1l2 2h0c0 1-1 1-2 2l-5-2-6-3z" class="H"></path><path d="M441 541c1 2 2 6 3 8v1c-1 1 0 2 0 3h-1 0c0-1 0-2-1-3v1l1 6h0l-1-2-2 3h0 0c-1-1-1 0 0-1 0-4-1-7-1-10 0-2 1-4 2-6z" class="g"></path><path d="M440 552c1 1 1 1 2 3l-2 3v-6z" class="S"></path><path d="M440 552v-2h1l1 1 1 6h0l-1-2c-1-2-1-2-2-3z" class="W"></path><path d="M395 396l1 1c1 1 3 2 4 3 4 1 8 2 12 1h1 3 4l1 1h0v1c-2 1-3 3-3 5v2 1h-1l-1 1c-1 2-5 7-7 9h0c0-2 1-2 2-3l1-1-1-1-5 4h0c-1 4 0 7 0 12v2 3l1 1v4 1 2h1v3 1c0 1 1 2 1 4h0c1 1 1 1 1 2s0 1 1 2v1l-1 1 1 3 6 14-2 1-2-4c-2-5-4-10-5-15l-1-2-6-28v-4-6l-1-3h2l1-1-2-4-6-12v-2z" class="AN"></path><path d="M411 404h0c3-1 5-2 9-1-2 2-6 6-9 7h-1l1-1v-4-1z" class="AG"></path><path d="M410 410h1c-2 3-5 5-6 9h0c-1 1-1 1-1 2s0 1-1 2c-1-1-1-3-1-4l-1-1-1-3h2l1-1h0c1 0 1 1 1 0 0 0 0-1 1-1 1-1 4-2 5-3z" class="AJ"></path><path d="M395 396l1 1c1 1 3 2 4 3 4 1 8 2 12 1h1 3 4l1 1-1 1c-4-1-6 0-9 1h0v1 4l-1 1c-1 1-4 2-5 3-1 0-1 1-1 1 0 1 0 0-1 0h0l-2-4-6-12v-2z" class="r"></path><path d="M396 397c1 1 3 2 4 3 4 1 8 2 12 1h1 3 4l1 1-1 1c-4-1-6 0-9 1h0c-1 0-2 0-3 1s-2 1-4 1c-2-1-4-1-5-3 0-1 0-1-1-2s-1-3-2-4z" class="AE"></path><path d="M400 400c4 1 8 2 12 1h1c-2 1-3 2-5 2h-6l-2-3z" class="M"></path><path d="M401 418l1 1c0 1 0 3 1 4 1-1 1-1 1-2s0-1 1-2v5c0 12 2 24 5 35l1 3 6 14-2 1-2-4c-2-5-4-10-5-15l-1-2-6-28v-4-6z" class="a"></path><path d="M402 419c0 1 0 3 1 4 1-1 1-1 1-2s0-1 1-2v5l-1 5h0-1l-1-1c-1-3 0-6 0-9z" class="AO"></path><path d="M408 458l2 4c1 3 3 8 4 11 0-1 0-2-1-3l-2-6v-2l6 14-2 1-2-4c-2-5-4-10-5-15z" class="AV"></path><path d="M455 609v-2s-1-1 0-2l1-1 2 2c2 0 3 1 4 2h3c0 1 0 2 1 3 0 1 0 3 1 4v-1l3 6 1 3h1 1c0 1 0 1 1 1l6 18 4 10 2 4v1l-1 1 5 14c1 2 3 4 3 6l1 3 1 4c-1-1-1-1-2-1h0c0 1 0 1-1 2v-2c-1-1-1-3-3-3v-2l-4-7c0-1-2-3-2-4l-3-3c0-2-1-3-2-4l-3-4-2-2-5-6c-1 1-1 1-3 0-1-1-2-2-3-4v-3h0c0-1-1-3-1-3v-1l1 1c1-1 1-1 0-2 0-2-1-2-1-4v-2h0l-5-10-1-4c0-1-1-2-2-3l1-2h0l1-2v-1z" class="U"></path><path d="M455 610c0 2 1 4 2 6l-2 1c0-1-1-2-2-3l1-2h0l1-2z" class="B"></path><path d="M484 652l2 4v1l-1 1-1-1-2-4 2-1z" class="R"></path><path d="M486 656v1l-1 1-1-1 2-1z" class="AA"></path><path d="M480 642l4 10-2 1-3-10h0l1 2v-3z" class="a"></path><path d="M456 607c1 0 1 0 2 1l2 2c1 0 2 1 3 1l1 1v2c1 0 1 1 1 1h-1v-1c-2 0-3-1-5-2h0-2-1c1-1 2-1 3-2l-3-3z" class="Q"></path><path d="M455 617l2-1 5 12h-1v-1l-1-2c-1 1-1 1 0 3 0 1 1 2 1 3h0l-5-10-1-4z" class="w"></path><path d="M471 623h1 1c0 1 0 1 1 1l6 18v3l-1-2h0c-4-6-6-14-8-20z" class="R"></path><path d="M476 652h1c1 1 2 2 3 2 4 6 5 13 10 18 1 2 3 4 3 6l1 3 1 4c-1-1-1-1-2-1h0c0 1 0 1-1 2v-2c-1-1-1-3-3-3v-2l-4-7c0-1-2-3-2-4l-3-3c0-2-1-3-2-4l-3-4h1v-1h1 0l-1-3v-1z" class="Q"></path><path d="M489 674c1 2 3 5 5 7l1 4c-1-1-1-1-2-1h0c0 1 0 1-1 2v-2c-1-1-1-3-3-3v-2c1-2 0-3 0-5z" class="q"></path><path d="M475 657h1v-1h1 0l6 8v4l-3-3c0-2-1-3-2-4l-3-4z" class="AL"></path><path d="M483 664c2 3 3 7 6 10 0 2 1 3 0 5l-4-7c0-1-2-3-2-4v-4z" class="S"></path><path d="M461 631c0-1-1-2-1-3-1-2-1-2 0-3l1 2v1h1l2 3c2 2 3 3 4 6 3 4 5 10 8 15v1l1 3h0-1v1h-1l-2-2-5-6c-1 1-1 1-3 0-1-1-2-2-3-4v-3h0c0-1-1-3-1-3v-1l1 1c1-1 1-1 0-2 0-2-1-2-1-4v-2z" class="c"></path><path d="M464 631c2 2 3 3 4 6 3 4 5 10 8 15v1l-12-22z" class="u"></path><path d="M461 633c3 5 5 8 6 14l1 2c-1 1-1 1-3 0-1-1-2-2-3-4v-3h0c0-1-1-3-1-3v-1l1 1c1-1 1-1 0-2 0-2-1-2-1-4z" class="B"></path><path d="M462 642l5 5 1 2c-1 1-1 1-3 0-1-1-2-2-3-4v-3z" class="AE"></path><path d="M375 445h2l1 1h1v-1l5 1h0c4 0 7 1 11 3 4 1 7 4 9 7v2c0 1 1 1 2 2h1v-4l1 2c1 5 3 10 5 15h-1l1 2c-1 1 0 1 0 2l1 1c2 1 3 5 4 7v1c1 1 1 1 1 2s1 2 1 3l-1 1 1 2v3c0 2 0 4 1 6l1 1h1l1 3h2c1 2 2 3 2 5l1 4v2c0 1 1 2 1 3v-2c1 2 2 3 2 4 0-1 0-1 1-2l1 1h1v1l1 3 3 11h-1c0-1-1-1-1-2l-2 1v5h-2v1c-1 7-3 14-6 19 0 1-1 2-1 3-2 1-3 2-4 3l1 3v1c-1 0-2 1-3 1l2 2 6 3 5 2 2 1h-1l1 1c1 1 2 1 3 2h-1c1 2 1 4 1 5v2l1 2v3l1 6v2l2 2c1 3 2 6 4 9 1 1 1 3 2 4 1 0 2 1 3 0h3c1 1 1 2 2 3l5 10h0v2c0 2 1 2 1 4 1 1 1 1 0 2l-1-1v1s1 2 1 3h0v3c1 2 2 3 3 4 2 1 2 1 3 0l5 6 2 2 3 4c1 1 2 2 2 4l3 3c0 1 2 3 2 4l4 7v2c2 0 2 2 3 3v2c1-1 1-1 1-2h0c1 0 1 0 2 1l3 6v-1h1c1 5 3 11 5 16 4 18 7 37 8 55 0 2 0 7-1 9l-1 9v3c-1 1-1 3-2 4l-3 1h-1l-1-1v-3h-1v-1c1-1 2-1 2-3 1-1 1-1 0-3h-1c-1 3-3 4-7 5-3 1-6 0-8 0h-2l-2-3c-3 0-5 1-7 1-2-1-3-1-4-2l-3-11-1 1c-2-6-4-13-6-19l-1 1c0-2-1-3-2-4s-2-4-2-6c-1-2-3-5-3-7h-1l-2 1v1c1 1 1 2 1 3v2 1l1 1h0c0 2 0 3 1 4 0 1 0 3 1 5 0 2 0 3-1 5v1c1 1 1 0 1 2h1v4l-1 1h0c1 2 1 4 1 6v2c0 1 0 2 1 3 0 1-1 1-1 2-1 2 1 2-2 4h0v2c1 1 1 1 1 2l-1 1c0 1 0 2 1 4 0 1 1 2 1 3 1 1 1 1 1 2v1l1 1v1c1 1 1 2 1 3 1 0 1 2 1 3-2-4-4-8-5-12l-9-21-1-4-3-8-1-3c-2-4-3-9-6-14 0-4-3-9-5-14v-2l-2-4v-1l-3-5-4-9c0-2-1-3-1-5l-4-10c-2-1-3-4-4-5-2-1-3-1-5-3 1-3 2-6 2-9 0-2-1-4-2-6l-5-12-2-5-4-9-8-20-1-3-7-18-4-9c-1-3-2-5-3-7v-2l2-1-1-2h-2l-1-1-1-3-3-8-9-23-1-2-2-4-2-7c-1-1-2-4-3-5l-6-14-4-11c-2-4-4-7-6-10-2-2-4-3-6-5l-2-2c-1 0-1 0-2-1l4-6 2-5h0l2-4 1 1 4-1c0-1 2-2 2-3 2-2 3-4 4-6v-3l2-6h1 1v-1c-1 0-3-1-4-1 2-1 4-1 6-1 5 0 10 0 15-1h1c3 0 6-1 9-2z" class="Z"></path><path d="M498 723l1 4-1 2-1-4c1-1 1-1 1-2z" class="M"></path><path d="M496 717l2 6c0 1 0 1-1 2l-2-7 1 1v1-3z" class="r"></path><path d="M469 696c0-3 0-8 1-11v1c0 5 1 10 0 15v-6l-1 1z" class="x"></path><path d="M494 709c1 3 2 5 2 8v3-1l-1-1c-1 0-3-7-3-7l1-1 1-1z" class="p"></path><path d="M471 728c3 8 5 18 8 26h-2l-3-10-4-16h1z" class="AC"></path><path d="M457 686c-1-2-2-3-2-5-1-1-1-1-1-2v-1-2l-1-2-1-1v-3l-1-1c0-1-1-2-1-3s0-2-1-3v-4c1 1 1 3 2 4v-1-2c2 5 4 11 5 16l-1 1c0 3 1 6 2 9z" class="g"></path><path d="M460 689c1 2 2 3 3 5v-1c-1-1-1-2-1-3 1-4-1-7 0-10v1 1l1 1v3l1 1c0 2-1 6 0 8l1-2c2 2 2 4 2 6v3l-1 1h0v4l-5-15-1-3z" class="d"></path><path d="M464 695l1-2c2 2 2 4 2 6v3l-1 1c-1-2-1-5-2-8z" class="B"></path><path d="M477 754h2l9 27h-2l-2-3c-3-7-5-16-7-24z" class="H"></path><path d="M464 687v-3h1v3c1 0 1 0 2 1l-1 1h2v-1c0-4 1-9 1-13h0v-1 1c1 4 1 7 1 11v-1c-1 3-1 8-1 11h0c0 1-1 2-1 3h-1c0-2 0-4-2-6l-1 2c-1-2 0-6 0-8z" class="V"></path><path d="M501 735c2 10 5 21 5 31-1 3 0 7-2 10h-1c1-5 1-9 0-13l-4-27 2-1z" class="x"></path><path d="M470 728l-10-35 1-1 5 15v-4h0l1-1v-3h1c0-1 1-2 1-3h0l1-1v6 8c-1 3-1 6-1 9s1 7 2 10h-1z" class="b"></path><path d="M466 703c1 1 2 3 2 4 1 2 0 4 0 6l-2-6v-4z" class="f"></path><path d="M469 696c0 4 0 8-1 11 0-1-1-3-2-4h0l1-1v-3h1c0-1 1-2 1-3z" class="P"></path><path d="M438 669c0-2-1-4-1-6 0-3-1-5-1-8l1 1 1 4v1 1h0l1 1 14 51c-1 0-2-1-3-2-2-3-2-8-3-12l-7-20c-1-3-2-7-2-11z" class="e"></path><path d="M432 648c0-5-2-11-2-17 1-2 0-3 2-4l4 23 2 10c1 1 1 2 1 3l-1-1h0v-1-1l-1-4-1-1c0 3 1 5 1 8 0 2 1 4 1 6h-1l-2-8v-1l-2 2c0-2 0-3-1-4 0-2-1-4-1-6h0v-4h1z" class="AD"></path><path d="M431 648h1l3 12-2 2c0-2 0-3-1-4 0-2-1-4-1-6h0v-4z" class="W"></path><path d="M474 744l3 10c2 8 4 17 7 24-3 0-5 1-7 1-2-1-3-1-4-2l-3-11-2-6h3v-3h1l1-1c2-4 0-6 0-10 0-1 0-1 1-2z" class="w"></path><path d="M487 755c1 2 0 8 0 10-1-2-1-4-1-5-1-1-1-1-1-2-2-4-2-8-3-12-2-6-4-13-5-19h0l1 2v-1c1-1 1-2 1-3 0 0 1-1 1-2l-1-1 2-2 2 1h2v-1c0-1-1-1 0-3 2 1 1 7 2 9v-1-2c1-6 0-11 0-16l1-1c1 4 1 7 1 11h0l-2 38z" class="c"></path><path d="M487 755c-1-7 0-14 0-20l1-17 1-1-2 38z" class="AC"></path><path d="M433 626c3 1 7 2 9 4 5 3 9 8 13 12 2 2 3 4 5 6 1 0 2 0 2 1 1 1 1 2 2 2v-1l1-1h0c2 1 2 1 3 0l5 6 2 2 3 4c1 1 2 2 2 4 0 0 0 1-1 2l1 2-2-3h-1c0 4 3 8 4 13l6 12c0 1 1 3 1 4l6 14-1 1-1 1-6-16-8-14c-11-22-25-41-46-55h0 1z" class="AA"></path><path d="M488 695l6 14-1 1-1 1-6-16h0c2 2 3 6 4 9l1-1-2-3v-1c-1-1 0 0 0-1l-1-1v-2z" class="o"></path><path d="M460 648c1 0 2 0 2 1 1 1 1 2 2 2v-1l1-1h0c2 1 2 1 3 0l5 6 2 2 3 4c1 1 2 2 2 4 0 0 0 1-1 2l1 2-2-3h-1c0 4 3 8 4 13l-21-31z" class="t"></path><path d="M473 655l2 2 3 4h-1c-2 0-5-3-7-5 1 0 2 0 3-1z" class="U"></path><path d="M468 649l5 6c-1 1-2 1-3 1l-5-7h0c2 1 2 1 3 0z" class="AG"></path><path d="M440 601v2l2 2c1 3 2 6 4 9 1 1 1 3 2 4 1 0 2 1 3 0h3c1 1 1 2 2 3l5 10h0v2c0 2 1 2 1 4 1 1 1 1 0 2l-1-1v1s1 2 1 3h0v3c1 2 2 3 3 4h0l-1 1v1c-1 0-1-1-2-2 0-1-1-1-2-1-2-2-3-4-5-6-4-4-8-9-13-12-2-2-6-3-9-4h-1l-2-1c-1 0-2-1-3-1h-1c-1-1-2-1-3-1h-1-3v-1h-2c-1 1-1 1-2 1s-2 0-3-1v-1c4-2 6-2 10-2l5-1 7 1 4-1c0-1 1-3 1-5v-3l1-6v-3z" class="n"></path><path d="M455 633c2 3 4 6 7 9h0v3l-7-9v-3z" class="j"></path><path d="M447 623c1 0 1 1 2 1h2c2 3 3 4 3 7l1 2v3c-2-2-4-4-6-7 0-1-1-1-1-2-1-1 0-2 0-3l-1-1z" class="r"></path><path d="M447 623c1 0 1 1 2 1h2c2 3 3 4 3 7l-6-7-1-1z" class="M"></path><path d="M448 618c1 0 2 1 3 0h3c1 1 1 2 2 3l5 10h0v2c0 2 1 2 1 4 1 1 1 1 0 2l-1-1v1s1 2 1 3c-3-3-5-6-7-9l-1-2c0-3-1-4-3-7l-3-6z" class="q"></path><path d="M440 601v2l2 2c1 3 2 6 4 9 1 1 1 3 2 4l3 6h-2c-1 0-1-1-2-1l1 1c0 1-1 2 0 3 0 1 1 1 1 2l-2-1-1 1-2-1c-3-1-5-2-7-3-1 0-3 0-4 1h-1l-2-1c-1 0-2-1-3-1h-1c-1-1-2-1-3-1h-1-3v-1h-2c-1 1-1 1-2 1s-2 0-3-1v-1c4-2 6-2 10-2l5-1 7 1 4-1c0-1 1-3 1-5v-3l1-6v-3z" class="U"></path><path d="M439 610l1 2c0 3 0 4-2 6h0c0-1 1-3 1-5v-3z" class="r"></path><path d="M440 601v2l2 2c1 3 2 6 4 9 1 1 1 3 2 4l3 6h-2c-1 0-1-1-2-1l-2-4c0-1-1-1-1-2s0-2-1-3-1-3-2-4l-1 2-1-2 1-6v-3z" class="J"></path><path d="M440 601v2c0 3 0 4 1 6v1l-1 2-1-2 1-6v-3z" class="AG"></path><path d="M427 618l7 1 6 3c3 1 5 5 7 6l-1 1-2-1c-3-1-5-2-7-3-1 0-3 0-4 1h-1l-2-1c-1 0-2-1-3-1h-1c-1-1-2-1-3-1h-1-3v-1h-2c-1 1-1 1-2 1s-2 0-3-1v-1c4-2 6-2 10-2l5-1z" class="g"></path><path d="M427 618l7 1 6 3-1 1h-3c-3-1-5-1-9-1h-1c1 0 2-1 3-1h2 4-1c-1 0-2-1-3-1-2 0-5 0-8-1h-1l5-1z" class="B"></path><path d="M440 622c3 1 5 5 7 6l-1 1-2-1c-3-1-5-2-7-3-1 0-3 0-4 1h-1l-2-1c-1 0-2-1-3-1h-1c-1-1-2-1-3-1h-1l1-1h3 1c4 0 6 0 9 1h3l1-1z" class="S"></path><path d="M426 622h1c2 0 3 0 5 1h2l3 2c-1 0-3 0-4 1h-1l-2-1c-1 0-2-1-3-1h-1c-1-1-2-1-3-1h-1l1-1h3z" class="u"></path><path d="M457 686c-1-3-2-6-2-9l1-1 3 10c0 1 1 3 1 3l1 3-1 1 10 35 4 16c-1 1-1 1-1 2 0 4 2 6 0 10l-1 1h-1v3h-3l2 6-1 1c-2-6-4-13-6-19-1-3-2-8-4-11l-4-15c2 2 2 6 3 8l3 8c0-1 0-1-1-2v-2l-1-1v-2c0-1 0-1-1-1 0-2 1-3 0-4s-1-2-1-3v-1c0-1 0-1 1-2-1-10-6-20-7-30l3 11c2-4 0-8 2-13 1-1 1-1 1-2z" class="B"></path><path d="M458 720l10 40 2 6-1 1c-2-6-4-13-6-19-1-3-2-8-4-11l-4-15c2 2 2 6 3 8l3 8c0-1 0-1-1-2v-2l-1-1v-2c0-1 0-1-1-1 0-2 1-3 0-4s-1-2-1-3v-1c0-1 0-1 1-2z" class="d"></path><defs><linearGradient id="Bm" x1="505.914" y1="717.546" x2="479.553" y2="718.856" xlink:href="#B"><stop offset="0" stop-color="#130b0c"></stop><stop offset="1" stop-color="#5f1b1f"></stop></linearGradient></defs><path fill="url(#Bm)" d="M480 665l3 3c0 1 2 3 2 4l4 7v2c2 0 2 2 3 3v2c1-1 1-1 1-2h0c1 0 1 0 2 1l3 6v-1h1c1 5 3 11 5 16 4 18 7 37 8 55 0 2 0 7-1 9l-1 9v3c-1 1-1 3-2 4l-3 1h-1l-1-1v-3h-1v-1c1-1 2-1 2-3 1-1 1-1 0-3 2-3 1-7 2-10 0-10-3-21-5-31l-2 1v-3l-1-4 1-2-1-4-2-6c0-3-1-5-2-8l-6-14c0-1-1-3-1-4l-6-12c-1-5-4-9-4-13h1l2 3-1-2c1-1 1-2 1-2z"></path><path d="M500 731l1 4-2 1v-3l1-2z" class="AC"></path><path d="M499 727l1 4-1 2-1-4 1-2z" class="J"></path><path d="M486 679c1 1 2 1 3 2 2 0 2 2 3 3l-4 1c0-2-1-4-2-6z" class="Q"></path><path d="M485 672l4 7v2c-1-1-2-1-3-2l-2-3 1-1v-3z" class="b"></path><path d="M480 665l3 3c0 1 2 3 2 4v3l-1 1c-1-3-2-5-4-7l-1-2c1-1 1-2 1-2z" class="n"></path><path d="M492 684v2c1-1 1-1 1-2h0c1 0 1 0 2 1l3 6v-1h1c1 5 3 11 5 16 4 18 7 37 8 55 0 2 0 7-1 9l-1 9v3c-1 1-1 3-2 4l-3 1h-1l-1-1v-3h-1v-1c1-1 2-1 2-3 1-1 1-1 0-3 2-3 1-7 2-10v7h1l2-1c2-11-2-25-4-36-2-9-3-18-6-26-3-9-7-17-11-25l4-1z" class="T"></path><path d="M509 779c0-3 0-7 2-9l-1 9h-1z" class="Q"></path><path d="M509 779h1v3c-1 1-1 3-2 4l-3 1h-1l-1-1c1-1 2-2 3-2 2-2 2-3 3-5z" class="H"></path><path d="M412 622c1 1 2 1 3 1s1 0 2-1h2v1h3 1c1 0 2 0 3 1h1c1 0 2 1 3 1l2 1h0v1c-2 1-1 2-2 4 0 6 2 12 2 17h-1v4h0c0 2 1 4 1 6 1 1 1 2 1 4l2-2v1l2 8h1c0 4 1 8 2 11l7 20c1 4 1 9 3 12 1 1 2 2 3 2l2 8h0l4 15c2 3 3 8 4 11l-1 1c0-2-1-3-2-4s-2-4-2-6c-1-2-3-5-3-7h-1l-2 1v1c1 1 1 2 1 3v2 1l1 1h0c0 2 0 3 1 4 0 1 0 3 1 5 0 2 0 3-1 5v1c1 1 1 0 1 2h1v4l-1 1h0c1 2 1 4 1 6v2c0 1 0 2 1 3 0 1-1 1-1 2-1 2 1 2-2 4h0v2c1 1 1 1 1 2l-1 1c0 1 0 2 1 4 0 1 1 2 1 3 1 1 1 1 1 2v1l1 1v1c1 1 1 2 1 3 1 0 1 2 1 3-2-4-4-8-5-12l-9-21-1-4-3-8-1-3c-2-4-3-9-6-14 0-4-3-9-5-14v-2s1 0 1-1l1-1s1-1 1-2c0-2 1-5 0-6s-1-2-1-3l1-1v-7c-1-3 0-6-1-8l-1-1c-1-3-3-4-3-7v-1c0-2-1-3-1-5 0-3-1-7-3-10 0-1-1-3-1-4-2-2-2-3-3-6s-2-7-3-10c0-3-2-5-3-8l-3-11c0-1-1-2-1-3s0-1-1-2v-3c2-1 3-1 5-1v1h1l-1-1h-4v-1l1-1z" class="t"></path><path d="M412 622c1 1 2 1 3 1s1 0 2-1h2l-1 1c0 1 0 2-1 3v1h-1c-1 0 0 0-1-1h-1l1-1h1l-1-1h-4v-1l1-1z" class="S"></path><path d="M435 660v1l2 8v1l-1-2v2l-2-5-1-3 2-2z" class="H"></path><path d="M435 660v1c0 1 0 2-1 4l-1-3 2-2z" class="S"></path><g class="J"><path d="M432 658h-1 0c-1-2-2-4-2-6-2-4-3-8-5-12v-2c-1-1-1-2-2-3v-1-2c-1 0-1-1-1-1v-1c-1-2-1-2-1-4l4 9 4 10 3 7c0 2 1 4 1 6z"></path><path d="M419 623h3 1c1 0 2 0 3 1h1c1 0 2 1 3 1l2 1h0v1c-2 1-1 2-2 4 0 6 2 12 2 17h-1v4h0l-3-7-4-10-4-9h0l-1-3z"></path></g><path d="M429 639l1 5c1 1 1 3 1 4v4h0l-3-7c1-1 1-4 1-6z" class="g"></path><path d="M426 632l3 7c0 2 0 5-1 6l-4-10 2 2v1-6z" class="V"></path><path d="M419 623h3 1c2 3 2 6 3 9v6-1l-2-2-4-9h0l-1-3z" class="Y"></path><path d="M450 712c1 1 2 2 3 2l2 8h0l4 15c2 3 3 8 4 11l-1 1c0-2-1-3-2-4s-2-4-2-6c-1-2-3-5-3-7h-1l-2 1v1c1 1 1 2 1 3v2 1l1 1h0c0 2 0 3 1 4 0 1 0 3 1 5 0 2 0 3-1 5v1c1 1 1 0 1 2h1v4l-1 1h0c1 2 1 4 1 6v2c0 1 0 2 1 3 0 1-1 1-1 2-1 2 1 2-2 4h0l-2-1-1-1c3-1 1 0 3 0 1-1 1-1 0-2l1-2c-1-3 0-7-1-10v-1c0-1 1-1 1-2 0-2-1-3-2-4v-1-3c1 0 1 0 1-1s-4-18-4-18c-1-2-1-3-1-4-1-1-1-2-1-2v-1c0-2 1-2 2-3 0-2 0-3-1-4h1 0c0-2 0-3-1-5h0 1l-1-3z" class="S"></path><path d="M450 712c1 1 2 2 3 2l2 8h0l4 15h0c-1-1-1 0-1-1l-5-13c-1-3-1-6-2-8l-1-3z" class="p"></path><path d="M332 483c1 1 4-1 6-1 1-1 2-2 3-2h1c-2 1-4 4-6 5 0 1 1 1 2 2l6 7c3 4 7 8 10 11l1-2 3 2a57.31 57.31 0 0 0 11 11l-1 1c2 2 5 4 8 6l13 13 7 6s1 2 2 2l10 14 4 7c1 2 2 3 2 5l1 1c2-1 3-3 4-4l1 1 2-1 1 3v1c-1 0-2 1-3 1l2 2 6 3 5 2 2 1h-1l1 1c1 1 2 1 3 2h-1c1 2 1 4 1 5v2l1 2v3l1 6v3l-1 6v3c0 2-1 4-1 5l-4 1-7-1-5 1c-4 0-6 0-10 2v1l-1 1v1h4l1 1h-1v-1c-2 0-3 0-5 1v3c1 1 1 1 1 2s1 2 1 3l3 11c1 3 3 5 3 8 1 3 2 7 3 10s1 4 3 6c0 1 1 3 1 4 2 3 3 7 3 10 0 2 1 3 1 5v1c0 3 2 4 3 7l1 1c1 2 0 5 1 8v7l-1 1c0 1 0 2 1 3s0 4 0 6c0 1-1 2-1 2l-1 1c0 1-1 1-1 1l-2-4v-1l-3-5-4-9c0-2-1-3-1-5l-4-10c-2-1-3-4-4-5-2-1-3-1-5-3 1-3 2-6 2-9 0-2-1-4-2-6l-5-12-2-5-4-9-8-20-1-3-7-18-4-9c-1-3-2-5-3-7v-2l2-1-1-2h-2l-1-1-1-3-3-8-9-23-1-2-2-4-2-7c-1-1-2-4-3-5l-6-14-4-11c-2-4-4-7-6-10-2-2-4-3-6-5l-2-2c0-1 2-1 3-1h1l1-1z" class="n"></path><path d="M359 524l1-1 1 2c0 1 0 1-1 2l-1-3z" class="S"></path><path d="M367 533c0 1 1 2 2 3l-2 1-2-3 2-1z" class="H"></path><path d="M355 522l2 3c1 1 1 2 1 3h-1l-2-6z" class="u"></path><path d="M358 520l2 3-1 1c-1 0-2-2-2-3 1 0 1 0 1-1z" class="B"></path><path d="M367 537l2-1 3 4-1 2c-1-2-3-4-4-5z" class="q"></path><path d="M385 559l4 6-2 2-4-8h2z" class="S"></path><path d="M361 525h1l5 8-2 1c-2-2-4-5-5-7 1-1 1-1 1-2z" class="q"></path><path d="M389 565c2 3 4 6 5 9l-1 3-3-5c0-2-1-3-3-5l2-2z" class="u"></path><path d="M372 540c5 6 10 12 13 19h-2l-2-3c-2-5-6-10-10-14l1-2z" class="H"></path><path d="M357 528h1c1 2 2 3 3 4s2 1 2 3l3 2v3l1 1 1 1c1 1 1 1 2 3l-5-5c-1-1-1 0-2-1 1 3 2 4 3 5 0 1 0 3 1 4h-1c-1 0-1 0-2 1-1-1-1-1-1-3v-1c0-1-1-3-2-4s-1-2-1-3h0v-1l-2-2 1-1c-2-1-2-2-2-3v-3z" class="AE"></path><path d="M361 532c1 1 2 1 2 3l3 2v3l-1-1c-2-1-1-1-2-3-1-1-1-2-2-4z" class="u"></path><path d="M357 531c1 2 2 3 3 5s2 3 3 5c1 1 2 2 3 4v3c-1 0-1 0-2 1-1-1-1-1-1-3v-1c0-1-1-3-2-4s-1-2-1-3h0v-1l-2-2 1-1c-2-1-2-2-2-3z" class="AG"></path><path d="M341 502l1 1c1 1 3 2 4 4l2 2 1 2v2c2 3 5 6 6 9l2 6v3c0 1 0 2 2 3l-1 1 2 2v1h0c0 1 0 2 1 3h-1-1-1c-1-2-2-5-3-8l-1-1c-1-1-2-4-3-5l-6-14-4-11z" class="M"></path><path d="M341 502l1 1c1 1 3 2 4 4l2 2 1 2v2l-1-2c-1-1-2-3-4-3 1 1 2 3 2 4l-1 1-4-11z" class="H"></path><path d="M362 525h1v-1c-1-1-1-1-1-3l1 2c2 4 4 7 7 11 4 5 8 11 13 16h0c2 2 4 5 6 9 1 0 2 4 3 4 1 4 3 9 6 12 0 1 0 2 1 3 1 0 2 0 2-1l6 12v1c0 1 0 2-1 3l3 8h0l1 3 1 2 1 3 2 6c-1 0 0 0-1 1-1-1-1-3-2-5l-4-9c-2-6-5-13-9-19l-1-1h-1s0-1-1-1l-2-4 1-3c-1-3-3-6-5-9l-4-6c-3-7-8-13-13-19l-3-4c-1-1-2-2-2-3l-5-8z" class="j"></path><path d="M394 574c1 3 3 6 4 9l-1-1h-1s0-1-1-1l-2-4 1-3z" class="t"></path><path d="M399 578c1 0 2 0 2-1l6 12v1c0 1 0 2-1 3l-7-15z" class="Y"></path><path d="M384 549v-1c7 1 12 6 17 10 2 1 5 4 6 6s3 3 3 6l1 2-1 1 1 2c-1 0-3-2-5-2-1 0-2 1-3 1l-1 2-1 1h0c0 1-1 1-2 1-1-1-1-2-1-3-3-3-5-8-6-12-1 0-2-4-3-4-2-4-4-7-6-9l1-1z" class="c"></path><path d="M392 563c2 2 3 5 5 8l-1-2c0-1-2-3-2-5 0-1 0-1 1-2 2 0 3 0 5 1l2 1v1h-4c-1 1-1 1-1 2l1 3 1 2-1 3c-3-3-5-8-6-12z" class="Y"></path><path d="M400 563l2 1v1h-4c-1 1-1 1-1 2l1 3c-1-2-3-5-3-6 1-1 3-1 5-1z" class="B"></path><path d="M398 570l-1-3c0-1 0-1 1-2h4c1 1 2 1 3 2s3 2 4 4l1 2 1 2c-1 0-3-2-5-2-1 0-2 1-3 1l-1 2-1 1h0c0 1-1 1-2 1-1-1-1-2-1-3l1-3-1-2z" class="W"></path><path d="M399 572c0 2 1 4 2 5h0c0 1-1 1-2 1-1-1-1-2-1-3l1-3z" class="V"></path><path d="M402 576c-1-2-2-3-2-5l4-2c1 2 2 2 4 2h1l1 2 1 2c-1 0-3-2-5-2-1 0-2 1-3 1l-1 2z" class="S"></path><path d="M384 549v-1c7 1 12 6 17 10 2 1 5 4 6 6s3 3 3 6l1 2-1 1-1-2c-1-2-3-3-4-4s-2-1-3-2v-1h1c-1 0-1-1-2-1v-1l-17-13z" class="j"></path><path d="M401 562c2 1 3 2 5 3l-1 2c-1-1-2-1-3-2v-1h1c-1 0-1-1-2-1v-1z" class="V"></path><path d="M406 565l4 5 1 2-1 1-1-2c-1-2-3-3-4-4l1-2z" class="w"></path><path d="M402 576l1-2c1 0 2-1 3-1 2 0 4 2 5 2 1 1 1 2 2 3l1 1 1 3 1 3h-1c0 2 0 3 1 4 1 2 1 3 1 5h2v3h1l1 1h1v1l2 1v4c-1 2-2 4-2 6 0 1-1 3-2 5-1 0-1 0-2 1 1 1 1 1 2 1 2 1 5 1 7 1l-5 1c-4 0-6 0-10 2 1-1 1-2 1-3l3-4h-1l-1 1-2-6-1-3-1-2-1-3h0l-3-8c1-1 1-2 1-3v-1l-6-12h0l1-1z" class="AE"></path><path d="M413 605v3l-1 1-1-3 2-1z" class="y"></path><path d="M409 601h2 0l2 4-2 1-1-2-1-3z" class="g"></path><path d="M409 601h2 0c0 1 0 2-1 3l-1-3z" class="Y"></path><path d="M413 608c1 2 2 3 2 6l-1 1-2-6 1-1z" class="W"></path><path d="M417 594h2v3h1l1 1-1 4c-2-2-1-4-2-6-1-1-1-1-1-2z" class="J"></path><path d="M407 590c1 3 3 7 4 11h-2 0l-3-8c1-1 1-2 1-3z" class="V"></path><path d="M402 576l1-2c1 0 2-1 3-1 2 0 4 2 5 2 1 1 1 2 2 3l1 1 1 3 1 3h-1l-3-4c0-1-2-2-4-3l-2 2c0 2 0 4 1 5v4l-6-12h0l1-1z" class="J"></path><path d="M421 598h1v1l2 1v4c-1 2-2 4-2 6 0 1-1 3-2 5-1 0-1 0-2 1 1 1 1 1 2 1 2 1 5 1 7 1l-5 1c-4 0-6 0-10 2 1-1 1-2 1-3l3-4c2-3 3-8 4-12l1-4z" class="R"></path><path d="M407 564c1 0 2 1 3 0l2 2v-1c1 2 2 3 2 5l1 1c2-1 3-3 4-4l1 1 2-1 1 3v1c-1 0-2 1-3 1l2 2 6 3 5 2 2 1h-1l1 1c1 1 2 1 3 2h-1c1 2 1 4 1 5v2l1 2v3l1 6v3l-1 6v3c0 2-1 4-1 5l-4 1-7-1c-2 0-5 0-7-1-1 0-1 0-2-1 1-1 1-1 2-1 1-2 2-4 2-5 0-2 1-4 2-6v-4l-2-1v-1h-1l-1-1h-1v-3h-2c0-2 0-3-1-5-1-1-1-2-1-4h1l-1-3-1-3-1-1c-1-1-1-2-2-3l-1-2 1-1-1-2c0-3-2-4-3-6z" class="M"></path><path d="M435 593l-1 3c0 2 0 2-1 4l-1 1-1 2h-3v-3c0-1 0-2 1-3v2h1c3-2 3-3 5-6z" class="b"></path><path d="M419 584a30.44 30.44 0 0 1 8 8v2l-2-1v-1c-1 0-2-1-3-2-1-2-3-4-3-6z" class="AE"></path><path d="M417 575l1 1c5 2 9 5 13 9v4h2v1c1-1 3-3 3-4 0 1 0 2-1 3v4c-2 3-2 4-5 6h-1v-2-2-1c0-2 0-3-1-5-1-1-1-3-2-4-3-3-6-5-9-8h0v-2z" class="t"></path><path d="M418 576c5 2 9 5 13 9v4h2v1c0 1 0 3-1 4-2-1-2-4-2-6-1-1-1-2-2-4-3-3-7-5-10-8z" class="Q"></path><path d="M425 592v1l2 1v6 3c-1 1 0 1-1 2 1 1 1 2 1 3 1 0 1 0 2-1h0c1-1 1-2 1-3l2-1v-2l1-1c1-2 1-2 1-4l1 1c-1 2-1 4-2 6l-1 4v1c-1 0-3 3-2 3-4 3-6 3-9 5l-1-1h0c1-2 2-4 2-5 0-2 1-4 2-6v-4c0-2 0-3-1-4 0-2 0-2 2-4z" class="U"></path><path d="M425 592v1l2 1v6c-2 3-3 7-5 10 0-2 1-4 2-6v-4c0-2 0-3-1-4 0-2 0-2 2-4z" class="AD"></path><path d="M425 592v1h0c0 2 0 3 1 4 0 1 0 2-1 3s0 3-1 4v-4c0-2 0-3-1-4 0-2 0-2 2-4z" class="p"></path><path d="M419 567l1 1 2-1 1 3v1c-1 0-2 1-3 1l2 2 6 3 5 2 2 1h-1l1 1c1 1 2 1 3 2h-1l-1 1v2c0 1-2 3-3 4v-1h-2v-4c-4-4-8-7-13-9l-1-1c-1 0-1-1-2-2l1-1-1-1h0c2-1 3-3 4-4z" class="B"></path><path d="M428 578l2 1 2 2-1 1c-1-1-3-2-4-3l1-1z" class="Y"></path><path d="M418 573l10 5-1 1-6-3c-1 0-2 0-3-1v-2z" class="P"></path><path d="M433 580h1l1 1c1 1 2 1 3 2h-1l-1 1v2c0 1-2 3-3 4v-1h-2v-4l1 1c1-2 1-4 1-6z" class="g"></path><path d="M435 581c1 1 2 1 3 2h-1l-1 1v2c0 1-2 3-3 4v-1c0-2 0-4 1-5s1-1 1-3z" class="H"></path><path d="M419 567l1 1 2-1 1 3v1c-1 0-2 1-3 1l2 2 6 3 5 2 2 1h-1-1l-3-1-2-1-10-5-2-1-1-1h0c2-1 3-3 4-4z" class="j"></path><path d="M407 564c1 0 2 1 3 0l2 2v-1c1 2 2 3 2 5l1 1h0l1 1-1 1c1 1 1 2 2 2v2l2 7c0 2 2 4 3 6 1 1 2 2 3 2-2 2-2 2-2 4 1 1 1 2 1 4l-2-1v-1h-1l-1-1h-1v-3h-2c0-2 0-3-1-5-1-1-1-2-1-4h1l-1-3-1-3-1-1c-1-1-1-2-2-3l-1-2 1-1-1-2c0-3-2-4-3-6z" class="AD"></path><path d="M415 578c1 1 2 3 2 4h-2l-1-3 1-1z" class="y"></path><path d="M417 582l2 6-1 1-2-4-1-3h2z" class="g"></path><path d="M411 572l2 1 1 2 1 3-1 1-1-1c-1-1-1-2-2-3l-1-2 1-1z" class="y"></path><path d="M413 578v-2l1-1 1 3-1 1-1-1z" class="P"></path><path d="M420 592l1-1c1 2 1 3 1 5h1c1 1 1 2 1 4l-2-1v-1h-1l-1-1h-1v-3h0 1v-2h0z" class="j"></path><path d="M420 592h0v5h-1v-3h0 1v-2z" class="t"></path><path d="M407 564c1 0 2 1 3 0l2 2c0 2 1 4 2 6l-1 1-2-1-1-2c0-3-2-4-3-6z" class="AG"></path><path d="M416 585l2 4 1-1 1 4v2h-1 0-2c0-2 0-3-1-5-1-1-1-2-1-4h1z" class="n"></path><path d="M419 588l1 4v2h-1c0-2-1-4-1-5l1-1z" class="q"></path><path d="M436 586v-2l1-1c1 2 1 4 1 5v2l1 2v3l1 6v3l-1 6v3c0 2-1 4-1 5l-4 1-7-1c-2 0-5 0-7-1-1 0-1 0-2-1 1-1 1-1 2-1h0l1 1c3-2 5-2 9-5-1 0 1-3 2-3v-1l1-4c1-2 1-4 2-6l-1-1 1-3v-4c1-1 1-2 1-3z" class="o"></path><path d="M430 611c-1 0 1-3 2-3 0 1 0 1 2 2 0-1 1-1 2-2l-1 2c0 1-2 1-2 1-1 0-1 1-2 1 0 1-1 0-1-1z" class="e"></path><path d="M435 612c0 2-1 3-1 5h-11 2c1-1 2-1 3-1s1-1 2-1h3v-2c1 0 2 0 2-1z" class="AA"></path><path d="M438 604h2l-1 6v3c0 2-1 4-1 5l-4 1-7-1c-2 0-5 0-7-1h3 11c0-2 1-3 1-5v-2l1-2h1l1-4z" class="a"></path><path d="M438 604h2l-1 6v3l-1-1c-1 2 0 4-2 5h-2c0-2 1-3 1-5v-2l1-2h1l1-4z" class="AV"></path><path d="M436 586v-2l1-1c1 2 1 4 1 5v2l1 2v3l1 6v3h-2l-1 4h-1c-1 1-2 1-2 2-2-1-2-1-2-2v-1l1-4c1-2 1-4 2-6l-1-1 1-3v-4c1-1 1-2 1-3z" class="p"></path><path d="M435 597v-2h3c0 3 0 6-1 8-1-1-1-2-1-3s0-1-1-2v1c0 1 0 2-1 3l-1 1c1-2 1-4 2-6z" class="r"></path><path d="M437 603l1 1-1 4h-1c-1 1-2 1-2 2-2-1-2-1-2-2v-1h2c1-1 2-2 3-4z" class="o"></path><path d="M436 586v-2l1-1c1 2 1 4 1 5v2l1 2v3l1 6v3h-2l-1-1h0c1-2 1-5 1-8h-3v2l-1-1 1-3v-4c1-1 1-2 1-3z" class="j"></path><path d="M436 586v-2l1-1c1 2 1 4 1 5v2-1c-1 1-1 4 0 6h-3v2l-1-1 1-3v-4c1-1 1-2 1-3z" class="J"></path><path d="M332 483c1 1 4-1 6-1 1-1 2-2 3-2h1c-2 1-4 4-6 5 0 1 1 1 2 2l6 7c3 4 7 8 10 11l1-2 3 2a57.31 57.31 0 0 0 11 11l-1 1c2 2 5 4 8 6l13 13 7 6s1 2 2 2l10 14 4 7v1l-2-2c-1 1-2 0-3 0-1-2-4-5-6-6-5-4-10-9-17-10v1l-1 1h0c-5-5-9-11-13-16-3-4-5-7-7-11l-1-2c0 2 0 2 1 3v1h-1-1l-1-2-2-3c0 1 0 1-1 1-1-1-1-2-2-3-2-3-3-5-6-7l-1-2-2-2c-1-2-3-3-4-4l-1-1c-2-4-4-7-6-10-2-2-4-3-6-5l-2-2c0-1 2-1 3-1h1l1-1z" class="p"></path><path d="M390 541c2 1 3 3 5 5l-1 1-2-2h1c-2-2-2-3-3-4z" class="y"></path><path d="M348 505c1 1 3 3 4 5l-4-1-2-2 2-2z" class="B"></path><path d="M342 503v-2h2l4 4-2 2c-1-2-3-3-4-4z" class="y"></path><path d="M335 492c3 1 7 6 9 9h-2v2l-1-1c-2-4-4-7-6-10z" class="w"></path><path d="M348 509l4 1c3 3 4 7 6 10 0 1 0 1-1 1-1-1-1-2-2-3-2-3-3-5-6-7l-1-2z" class="S"></path><path d="M355 503l3 2a57.31 57.31 0 0 0 11 11l-1 1c-5-4-10-8-14-12l1-2z" class="w"></path><path d="M362 521l-1-3h1c7 4 14 9 20 15 2 2 6 5 8 8 1 1 1 2 3 4h-1 0c-2-2-5-5-8-6-3-2-6-4-8-6s-5-5-8-6c-2-1-3-3-5-4l-1-2z" class="Y"></path><path d="M363 523c2 1 3 3 5 4 3 1 6 4 8 6s5 4 8 6c3 1 6 4 8 6h0l2 2 1-1c3 3 7 7 9 11 3 2 4 5 6 7-1 1-2 0-3 0-1-2-4-5-6-6-5-4-10-9-17-10v1l-1 1h0c-5-5-9-11-13-16-3-4-5-7-7-11z" class="g"></path><path d="M370 534c4 2 8 5 12 8 2 1 4 1 6 2s4 3 6 5c4 2 6 7 10 8 3 2 4 5 6 7-1 1-2 0-3 0-1-2-4-5-6-6-5-4-10-9-17-10v1l-1 1h0c-5-5-9-11-13-16z" class="u"></path><defs><linearGradient id="Bn" x1="411.265" y1="626.619" x2="393.514" y2="632.693" xlink:href="#B"><stop offset="0" stop-color="#980c11"></stop><stop offset="1" stop-color="#c41920"></stop></linearGradient></defs><path fill="url(#Bn)" d="M354 532l1 1 3 8h1 1 1c1 1 2 3 2 4v1c0 2 0 2 1 3 1-1 1-1 2-1h1c-1-1-1-3-1-4-1-1-2-2-3-5 1 1 1 0 2 1l5 5v2c2 1 3 3 5 4v3c2 2 2 2 3 4 1 3 4 6 5 8 3 5 6 10 8 15 1 2 3 4 4 7 2 2 2 5 3 8 2 3 3 6 5 9 2 5 2 9 5 12 2 2 3 4 2 7v1 3c1 1 1 1 1 2s1 2 1 3l3 11c1 3 3 5 3 8 1 3 2 7 3 10s1 4 3 6c0 1 1 3 1 4 2 3 3 7 3 10 0 2 1 3 1 5v1c0 3 2 4 3 7l1 1c1 2 0 5 1 8v7l-1 1c0 1 0 2 1 3s0 4 0 6c0 1-1 2-1 2l-1 1c0 1-1 1-1 1l-2-4v-1l-3-5-4-9c0-2-1-3-1-5l-4-10c-2-1-3-4-4-5-2-1-3-1-5-3 1-3 2-6 2-9 0-2-1-4-2-6l-5-12-2-5-4-9-8-20-1-3-7-18-4-9c-1-3-2-5-3-7v-2l2-1-1-2h-2l-1-1-1-3-3-8-9-23-1-2-2-4-2-7z"></path><path d="M402 632h0l2 1v1l-1 1h0v1c-1-2-1-3-1-4zm-38-83c1-1 1-1 2-1h1c1 1 2 1 2 2 0 2 2 3 2 5l-2 1-2-1v-1c-1-1-1-2-1-4h0l-2-1z" class="AP"></path><path d="M389 622h1 0c1 1 1 2 1 3l1 1v-2c1 1 1 2 1 3l1 1c1 2 0 3 0 5 1 3 3 5 3 9l-8-20z" class="e"></path><path d="M410 674c3 5 5 12 7 17-2-1-3-4-4-5-2-1-3-1-5-3 1-3 2-6 2-9z" class="AI"></path><path d="M374 583l2-1c2 5 4 10 6 16 0 1 1 3 2 5 0 1 0 2 1 3v1c1 3 2 5 3 7v5l-7-18-4-9c-1-3-2-5-3-7v-2z" class="j"></path><path d="M354 532l1 1 3 8h1 1 1c1 1 2 3 2 4v1c0 2 0 2 1 3l2 1h0c0 2 0 3 1 4v1l2 1c1 1 1 2 1 4 1 2 1 3 2 5v5c1 1 1 2 2 3s0 2 0 3c1 1 1 2 1 4h-2l-1-1-1-3-3-8-9-23-1-2-2-4-2-7z" class="AE"></path><path d="M367 555l2 1c1 1 1 2 1 4 0-1 0-1-1-1h0l-1-1c0-1-1-2-1-3z" class="e"></path><path d="M354 532l1 1 3 8h1 1 1c1 1 2 3 2 4v1c0 2 0 2 1 3l1 3h-1l-1-1c0-2 0-3-1-4s-1-1-1-2h0c-1-1 0-2-2-2v2l-1-2-2-4-2-7z" class="r"></path><defs><linearGradient id="Bo" x1="380.918" y1="504.319" x2="429.43" y2="512.238" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#4e0b0b"></stop></linearGradient></defs><path fill="url(#Bo)" d="M375 445h2l1 1h1v-1l5 1h0c4 0 7 1 11 3 4 1 7 4 9 7v2c0 1 1 1 2 2h1v-4l1 2c1 5 3 10 5 15h-1l1 2c-1 1 0 1 0 2l1 1c2 1 3 5 4 7v1c1 1 1 1 1 2s1 2 1 3l-1 1 1 2v3c0 2 0 4 1 6l1 1h1l1 3h2c1 2 2 3 2 5l1 4v2c0 1 1 2 1 3v-2c1 2 2 3 2 4 0-1 0-1 1-2l1 1h1v1l1 3 3 11h-1c0-1-1-1-1-2l-2 1v5h-2v1c-1 7-3 14-6 19 0 1-1 2-1 3-2 1-3 2-4 3l-2 1-1-1c-1 1-2 3-4 4l-1-1c0-2-1-3-2-5l-4-7-10-14c-1 0-2-2-2-2l-7-6-13-13c-3-2-6-4-8-6l1-1a57.31 57.31 0 0 1-11-11l-3-2-1 2c-3-3-7-7-10-11l-6-7c-1-1-2-1-2-2 2-1 4-4 6-5h-1c-1 0-2 1-3 2-2 0-5 2-6 1l-1 1h-1c-1 0-3 0-3 1-1 0-1 0-2-1l4-6 2-5h0l2-4 1 1 4-1c0-1 2-2 2-3 2-2 3-4 4-6v-3l2-6h1 1v-1c-1 0-3-1-4-1 2-1 4-1 6-1 5 0 10 0 15-1h1c3 0 6-1 9-2z"></path><path d="M390 513l2 1c0 2 1 4 1 6-1-2-2-3-3-4v-3z" class="Q"></path><path d="M422 504h1l1 3 1 2-2 1-1-6z" class="M"></path><path d="M426 534c1 2 1 4 1 7v3c-1 1-1 2-2 3l1-13z" class="p"></path><path d="M415 525h1l1 1h4v1h-5v7 2c0 1 0 2 1 3v2c-3-4-1-12-2-16z" class="W"></path><path d="M426 507c1 2 2 3 2 5l-1-1-1 1c0 1 1 3 1 4h0c-1-1-1-2-2-4l-1 1-1-3 2-1-1-2h2z" class="n"></path><path d="M423 510l2-1c0 1 1 2 0 3l-1 1-1-3z" class="AP"></path><path d="M411 480c2 0 3 2 4 3v1c2 1 2 3 4 4 0 1 1 2 1 3l-1 1 1 2v3c-2-5-3-10-6-14l-3-3z" class="q"></path><defs><linearGradient id="Bp" x1="421.389" y1="526.743" x2="431.611" y2="529.757" xlink:href="#B"><stop offset="0" stop-color="#d42828"></stop><stop offset="1" stop-color="#e05151"></stop></linearGradient></defs><path fill="url(#Bp)" d="M424 513l1-1c1 2 1 3 2 4v3l2 12v10l-1 1-1-1c0-3 0-5-1-7l-2-21z"></path><path d="M385 480c0-2 1-3 2-5h0c1-2 0-4 0-7h1c0 2 1 4 1 6l1 6v4c1 7 1 14 1 21v3l1 6-2-1c-1-2-1-3-3-4l-4-4c1-3-1-5-1-8 0-2 1-3 1-5 2-3 1-9 2-12z" class="u"></path><path d="M383 492c0 3 1 6 2 9v4c2 2 4 2 6 3l1 6-2-1c-1-2-1-3-3-4l-4-4c1-3-1-5-1-8 0-2 1-3 1-5z" class="J"></path><path d="M385 480c0-2 1-3 2-5h0c1-2 0-4 0-7h1c0 2 1 4 1 6l1 6-1 1c0 2 0 4-1 6 0 2 0 4-1 6l1 2c-2-2-2-3-2-5s0-6-1-8v-2z" class="n"></path><path d="M387 493h0c0-3-1-8 1-11 1-3-1-5 1-8l1 6-1 1c0 2 0 4-1 6 0 2 0 4-1 6z" class="U"></path><path d="M387 493c1-2 1-4 1-6 1-2 1-4 1-6l1-1v4c1 7 1 14 1 21h-2-1l-1-1v-3c1-2 1-4 1-6l-1-2z" class="r"></path><path d="M387 493c1-2 1-4 1-6 1-2 1-4 1-6l1-1v4c-1 2-1 5-1 8 0 2 0 8-1 10 0 1 0 2 1 3h-1l-1-1v-3c1-2 1-4 1-6l-1-2z" class="AG"></path><path d="M427 516h0c0-1-1-3-1-4l1-1 1 1 1 4v2c0 1 1 2 1 3v-2c1 2 2 3 2 4 0-1 0-1 1-2l1 1h1v1l1 3 3 11h-1c0-1-1-1-1-2l-2 1v5h-2v1c-1 7-3 14-6 19 0 1-1 2-1 3-2 1-3 2-4 3l-2 1-1-1c2-4 4-7 4-12l2-8c1-1 1-2 2-3v-3l1 1 1-1v-10l-2-12v-3z" class="AD"></path><path d="M432 523c0-1 0-1 1-2l1 1v4l-1 1-1-4z" class="j"></path><path d="M433 527l1-1 1 10v5h-2 0c1-5 1-9 0-14z" class="e"></path><path d="M434 522h1v1l1 3 3 11h-1c0-1-1-1-1-2l-2 1-1-10v-4z" class="Y"></path><path d="M429 531v1c1 3 1 7 1 10 0 2 0 4-1 6v3c0 4-3 7-4 11 1-1 1-2 2-2l1-3c1-2 1-4 2-5v-1l1-1v-5h1v-1c0-1 1-1 1-2-1 7-3 14-6 19 0 1-1 2-1 3-2 1-3 2-4 3l-2 1-1-1c2-4 4-7 4-12l2-8c1-1 1-2 2-3v-3l1 1 1-1v-10z" class="e"></path><path d="M427 541l1 1 1-1c0 2 0 5-1 7h-1v-4-3z" class="o"></path><path d="M393 454c2 1 4 1 6 2h2l3 2c0 1 1 1 2 2h1v-4l1 2c1 5 3 10 5 15h-1l1 2c-1 1 0 1 0 2l1 1c2 1 3 5 4 7v1c1 1 1 1 1 2-2-1-2-3-4-4v-1c-1-1-2-3-4-3-4-4-8-8-13-11-3-3-7-4-10-6l-1 1h-1v-1l-3-1h-2c-1-1-1-2-3-2v-2h1 0 3l1 1 2-2c-1 0-2-1-2-1 3-2 6-2 10-2z" class="n"></path><path d="M378 458h1l8 4 1 1-1 1h-1v-1l-3-1h-2c-1-1-1-2-3-2v-2z" class="c"></path><path d="M371 456l3 1c1 0 3 1 4 1v2c2 0 2 1 3 2h2l3 1v1h1c0 1 1 3 1 4h-1c0 3 1 5 0 7h0c-1 2-2 3-2 5-1 3 0 9-2 12 0 2-1 3-1 5 0 3 2 5 1 8h0l-1-2v2h-2l-2-2c-1-2-2-3-3-4l-8-10c-1-2-3-5-4-7l-1-2-1-1c1 0 2-1 2-1l-1-5v-3c-1-3 0-5 2-8h0l3-4v-1c2 0 3 0 4-1z" class="B"></path><path d="M367 458c3-1 5-1 8 2 1 2 2 4 2 7l-2-2c0-3-1-4-3-6-3 1-5 1-8 3h0l3-4z" class="V"></path><path d="M362 473c2 4 5 8 6 12-2 0-1-2-3-1 0 1 1 1 1 1 1 1 1 3 1 4-1-2-3-5-4-7l-1-2-1-1c1 0 2-1 2-1l-1-5z" class="AN"></path><path d="M363 478c1 1 1 2 0 4l-1-2-1-1c1 0 2-1 2-1z" class="d"></path><path d="M371 456l3 1c1 0 3 1 4 1v2c2 0 2 1 3 2v1l1 2 1 4h-1v3h0-1c0-1 0-2-1-3l-2-6v-1c-1-1 0-2-1-3 0-1-1-1-2-1v1l1 1c1 1 1 2 1 3s1 1 0 3h0c1 3 0 3 2 5v1c-2-2-2-3-2-5 0-3-1-5-2-7-3-3-5-3-8-2v-1c2 0 3 0 4-1z" class="AL"></path><path d="M381 463l1 2-1 2-1-3s1 0 1-1z" class="Q"></path><path d="M378 460c2 0 2 1 3 2v1c0 1-1 1-1 1l-2-4z" class="S"></path><path d="M382 465l1 4h-1v3h0l-1-5 1-2z" class="q"></path><path d="M381 462h2l3 1v1h1c0 1 1 3 1 4h-1c0 3 1 5 0 7h0c-1 2-2 3-2 5-1 3 0 9-2 12 0 2-1 3-1 5 0 3 2 5 1 8h0l-1-2c-1-5 0-9 0-14 0-1-1-3 0-5 2-5 1-8 0-12v-3h1l-1-4-1-2v-1z" class="H"></path><path d="M381 462h2l3 1v1 1 1 4h-2v-1h-1l-1-4-1-2v-1z" class="g"></path><path d="M381 462h2v4l1 1v2h-1l-1-4-1-2v-1z" class="y"></path><path d="M375 445h2l1 1h1v-1l5 1h0c4 0 7 1 11 3 4 1 7 4 9 7v2l-3-2h-2c-2-1-4-1-6-2-4 0-7 0-10 2 0 0 1 1 2 1l-2 2-1-1h-3 0-1c-1 0-3-1-4-1l-3-1c-1 1-2 1-4 1v1l-3 4h0c-2 3-3 5-2 8v3l1 5s-1 1-2 1l-1-2-1 1h0c1 6-3 8-6 13-1 1-1 3-2 4v1l4 7-1 2c-3-3-7-7-10-11l-6-7c-1-1-2-1-2-2 2-1 4-4 6-5h-1c-1 0-2 1-3 2-2 0-5 2-6 1l-1 1h-1c-1 0-3 0-3 1-1 0-1 0-2-1l4-6 2-5h0l2-4 1 1 4-1c0-1 2-2 2-3 2-2 3-4 4-6v-3l2-6h1 1v-1c-1 0-3-1-4-1 2-1 4-1 6-1 5 0 10 0 15-1h1c3 0 6-1 9-2z" class="B"></path><path d="M365 453h5c2-1 3-1 5-1-3 1-7 1-9 2h-2l1-1z" class="W"></path><path d="M354 454c2-1 5 0 7-1h4l-1 1-1 1v1l-2-1-1-1h-6z" class="g"></path><path d="M346 451h1 1l-2 10v-3-1h-1c0 1 0 1-1 2v1-3l2-6z" class="j"></path><path d="M375 445h2l1 1h1v-1l5 1c-2 1-5 2-7 2-9 2-19 4-29 2-1 0-3-1-4-1 2-1 4-1 6-1 5 0 10 0 15-1h1c3 0 6-1 9-2z" class="o"></path><path d="M375 445h2l1 1h1v-1l5 1c-2 1-5 2-7 2s-3 0-4-1l2-2z" class="AV"></path><path d="M395 449c4 1 7 4 9 7v2l-3-2h-2c-2-1-4-1-6-2-4 0-7 0-10 2 0 0 1 1 2 1l-2 2-1-1h-3 0-1c-1 0-3-1-4-1l-3-1-2-1c-1 0-2 0-3-1 2-1 6-1 9-2h4 6 5 4c1 0 2-1 3-1l-2-2z" class="Q"></path><path d="M369 455c1-1 2-1 4-1l1 1h1 0l-1 2-3-1-2-1z" class="B"></path><path d="M375 455c1 1 2 1 3 1 1 1 1 0 1 0v-2l1-1 1 1 6-1c2 0 4 1 6 1-4 0-7 0-10 2 0 0 1 1 2 1l-2 2-1-1h-3 0-1c-1 0-3-1-4-1l1-2z" class="S"></path><path d="M354 454h6l1 1 2 1-3 3c-1 1-2 1-3 3l-1 1c-1 0-1 0-1 1-2 2-3 4-5 5l-8 11h-1c-1 0-2 1-3 2-2 0-5 2-6 1l-1 1h-1c-1 0-3 0-3 1-1 0-1 0-2-1l4-6 2-5h0l2-4 1 1 4-1c0-1 2-2 2-3 2-2 3-4 4-6v-1c1-1 1-1 1-2h1v1 3l-2 4 1 1c2-2 3-3 4-5 2-2 3-3 5-4v-2-1z" class="M"></path><path d="M354 454h6l1 1h0c-1 1-2 2-4 2 0 1 0 1-1 1-5 2-10 14-16 14l4-7 1 1c2-2 3-3 4-5 2-2 3-3 5-4v-2-1z" class="W"></path><path d="M344 460v-1c1-1 1-1 1-2h1v1 3l-2 4-4 7-2 3c-2 3-4 6-6 8l-1 1h-1c-1 0-3 0-3 1-1 0-1 0-2-1l4-6 2-5h0l2-4 1 1 4-1c0-1 2-2 2-3 2-2 3-4 4-6z" class="AA"></path><path d="M335 474l2-1c0 1-1 2-1 3-2 2-4 5-6 6 1-2 2-4 3-5s2-2 2-3z" class="AV"></path><path d="M329 478c1 0 3-1 4-1-1 1-2 3-3 5l-1 1c1 0 1 1 2 1h-1c-1 0-3 0-3 1-1 0-1 0-2-1l4-6z" class="R"></path><path d="M333 469l1 1 4-1c-1 2-2 3-3 5 0 1-1 2-2 3-1 0-3 1-4 1l2-5h0l2-4z" class="j"></path><path d="M344 460v-1c1-1 1-1 1-2h1v1 3l-2 4-4 7-2 3c-1 0-1 0-2 1 0-1 1-2 1-3l-2 1c1-2 2-3 3-5 0-1 2-2 2-3 2-2 3-4 4-6z" class="o"></path><defs><linearGradient id="Bq" x1="349.693" y1="468.882" x2="368.338" y2="488.119" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#221313"></stop></linearGradient></defs><path fill="url(#Bq)" d="M364 454h2c1 1 2 1 3 1l2 1c-1 1-2 1-4 1v1l-3 4h0c-2 3-3 5-2 8v3l1 5s-1 1-2 1l-1-2-1 1h0c1 6-3 8-6 13-1 1-1 3-2 4v1l4 7-1 2c-3-3-7-7-10-11l-6-7c-1-1-2-1-2-2 2-1 4-4 6-5l8-11c2-1 3-3 5-5 0-1 0-1 1-1l1-1c1-2 2-2 3-3l3-3v-1l1-1z"></path><path d="M364 454h2c1 1 2 1 3 1l2 1c-1 1-2 1-4 1v1l-3 4c0-2 1-4 2-6h0l-3-1 1-1z" class="Z"></path><path d="M338 487l6-6c0 3-2 6-1 8l1 2v3l-6-7z" class="d"></path><path d="M342 480l8-11c2-1 3-3 5-5 0-1 0-1 1-1l1-1c1-2 2-2 3-3l-12 18-4 4-6 6c-1-1-2-1-2-2 2-1 4-4 6-5z" class="R"></path><path d="M351 496c-2-2-3-4-4-6 2-3 6-6 7-9l2-2 1-1c0-1 1-2 2-2l1 1-1 1h0c1 6-3 8-6 13-1 1-1 3-2 4v1z" class="W"></path><defs><linearGradient id="Br" x1="381.77" y1="560.188" x2="397.23" y2="499.312" xlink:href="#B"><stop offset="0" stop-color="#6a080d"></stop><stop offset="1" stop-color="#941617"></stop></linearGradient></defs><path fill="url(#Br)" d="M360 477l1 2 1 1 1 2c1 2 3 5 4 7l8 10c1 1 2 2 3 4l2 2h2v-2l1 2h0l4 4c2 1 2 2 3 4v3c1 1 2 2 3 4 9 9 18 20 25 30 2 2 3 4 5 5 0 5-2 8-4 12-1 1-2 3-4 4l-1-1c0-2-1-3-2-5l-4-7-10-14c-1 0-2-2-2-2l-7-6-13-13c-3-2-6-4-8-6l1-1a57.31 57.31 0 0 1-11-11l-3-2-4-7v-1c1-1 1-3 2-4 3-5 7-7 6-13h0l1-1z"></path><path d="M382 503l1 2v3l-3-3h2v-2z" class="g"></path><path d="M383 505h0l4 4c-1 1-1 2-2 2l-2-3v-3z" class="W"></path><path d="M387 509c2 1 2 2 3 4v3l-1-1-4-4c1 0 1-1 2-2z" class="B"></path><path d="M398 544l1-1v-1c4 4 11 11 13 16h-1 0c-1-2-2-3-3-5l-1-1h0l-2-2-3-3h0c1 1 2 3 3 4 0 1 1 2 2 3s1 1 2 3v1h-1l-10-14z" class="Q"></path><path d="M360 477l1 2 1 1c-1 1-1 1 0 3s1 4 2 6v3s-1 1-1 2c1 2 4 5 3 7-1 0-4-3-5-4-2 0-3 1-4 3 0 1 1 4 1 5l-3-2-4-7v-1c1-1 1-3 2-4 3-5 7-7 6-13h0l1-1z" class="S"></path></svg>