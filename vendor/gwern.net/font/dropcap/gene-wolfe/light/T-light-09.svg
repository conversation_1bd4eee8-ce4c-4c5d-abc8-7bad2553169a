<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="170 51 699 871"><!--oldViewBox="0 0 1024 1023"--><style>.B{fill:#2c2c2a}.C{fill:#21211f}.D{fill:#363634}.E{fill:#b2b2b1}.F{fill:#31312e}.G{fill:#181817}.H{fill:#11100f}.I{fill:#3f3f3d}.J{fill:#454542}.K{fill:#262524}.L{fill:#4a4a48}.M{fill:#070707}.N{fill:#d4d3d3}.O{fill:#151513}.P{fill:#91908f}.Q{fill:#3b3b3a}.R{fill:#c3c2c2}.S{fill:#1d1d1a}.T{fill:#a6a5a4}.U{fill:#cdcccb}.V{fill:#d7d7d6}.W{fill:#e7e6e6}.X{fill:#b9b8b7}.Y{fill:#50504d}.Z{fill:#555553}.a{fill:#1b1b1a}.b{fill:#a09f9e}.c{fill:#bebdbc}.d{fill:#8a8988}.e{fill:#252522}.f{fill:#acabaa}.g{fill:#e1e0df}.h{fill:#7a7978}.i{fill:#c9c8c7}.j{fill:#ecebeb}.k{fill:#848482}.l{fill:#737271}.m{fill:#80807e}.n{fill:#f3f3f3}.o{fill:#646462}.p{fill:#9b9b99}.q{fill:#979695}.r{fill:#61605f}.s{fill:#585856}.t{fill:#fff}.u{fill:#010101}.v{fill:#6d6d6b}.w{fill:#5d5d5b}.x{fill:#11110f}.y{fill:#686866}</style><path d="M417 491l1 1c0 2 0 3 1 5l1 3v1h0v1c-1-2-2-3-3-5s0-4 0-6z" class="j"></path><path d="M407 599h5 2c1 1 2 1 3 2v1c0 1 0 2-1 3-2-2-4-4-6-5-1 0-2 0-3-1z" class="D"></path><path d="M626 497l7 1c-3 1-5 1-8 1-2 1-5 0-7 1v-1-1-1h2l1 1c2 0 3 0 5-1z" class="r"></path><path d="M413 258c2 0 2 0 4 1 1 1 2 1 4 0l1 2c-4 1-7 2-11 2 0-1 0-1 1-1v-1h0l1 1h1 0c1-1 1-2 1-3-1 0-3 0-4 1h-1 0l1-2h2z" class="j"></path><path d="M627 682c2 0 5 0 7 1 0 1 1 2-1 3l-1 1v1l-1-1v-1l-2-1h-3c0-1 1-2 1-3z" class="Q"></path><path d="M639 380h13 7c-3 1-5 2-8 2-2-1-5 0-7 0-1 0-3-1-5-1h1l-1-1h0z" class="K"></path><path d="M650 827l9 10-1 2h-1-1l-1-1h0v2h0l-1 1h-1v-4s1 0 2-1c0-1-1-1-1-2-1-1-1-2-2-3s-1-2-2-4z" class="m"></path><path d="M413 674l1 1 1 1v2 4c-1 2 0 7 0 10 0 0 0 2-1 2v5c-1-3 0-6-1-8 0-6-1-12 0-17z" class="f"></path><path d="M620 638h0c1 1 2 0 3 0-3 3-6 5-6 9v1c0 1 0 1-1 1h0c-1 1-1 1-2 1 0-1-1-2 0-3v-2l-1-1-1 1v-3l1 1h2 1c1 0 1-1 2-2 0-2 1-2 2-3z" class="C"></path><path d="M381 656l-8-3h23c-1 2-3 0-5 2-3 1-7 1-10 1z" class="B"></path><path d="M177 116c5 1 9 4 12 7-1 1-2 1-3 1 1 3 4 4 4 8l-13-16z" class="Z"></path><path d="M415 682c2 3 4 7 4 10l-1 1v5h-2l-2-4c1 0 1-2 1-2 0-3-1-8 0-10z" class="n"></path><path d="M617 682h10c0 1-1 2-1 3h3c-2 2-4 2-7 2h-1-2-1l-1-1v-4z" class="F"></path><path d="M622 687l-1-2h0 4 1 3c-2 2-4 2-7 2z" class="C"></path><path d="M615 488l1 2 2 1c4 0 10 1 14 3h0c-5 1-10-2-14-1v2l8 2c-2 1-3 1-5 1l-1-1h-2v1 1c-1-1-2-2-3-2v-9z" class="Z"></path><path d="M409 593h1l1-1h3c1 0 2 1 3 1v4 4c-1-1-2-1-3-2h-2-5c1-1 1 0 2-1 1 0 1-1 2-2l-2-1s0-1-1-1l1-1z" class="n"></path><path d="M409 593h1l1-1h3c1 0 2 1 3 1v4c-1 0-3-1-4-1-1-1 0-1-2-1h0c-1-1-2-1-2-2z" class="W"></path><path d="M617 659h0l6 1h4c1 1 2 1 3 2-1 1-3 2-5 2s-7 0-8 2v2c-1-1-1-3-1-4l1-4v-1z" class="B"></path><path d="M676 789h24l-17 5h-1c0-1-1-1-2-1-1-1-2-1-4-1v-3z" class="I"></path><path d="M416 675h2 1c-1 3-1 5 0 8l1 1c1 2 2 2 4 3h0v1h-2l1 1c-1 1-2 1-3 3h-1c0-3-2-7-4-10v-4h1v-3z" class="N"></path><path d="M415 678h1l1 3c1 3 2 5 5 7h0l1 1c-1 1-2 1-3 3h-1c0-3-2-7-4-10v-4zm199-149l2 2 1-1c0-1 0-2 1-3h2l-2 1v2c2 1 8 2 11 2l1 1c-4 2-9 1-12 1-1 3 0 7-1 10v-9l-2-1c-1 0-2 0-3-1 0-1-1-2 0-3l2-1z" class="D"></path><path d="M494 837c1 10-1 20 1 30 0 1 0 2 1 3v3c0 2 0 4-1 6-1 4-1 9-1 13 0 1 1 3 0 4h-1c0-9 0-17-1-26h1v-2h1v-1c-1-2-1-3-1-5 2-8 1-17 1-25z" class="K"></path><path d="M622 455l2-1h1c2 3 8 0 10 3 0 1-1 1-2 1l-7 1h0c-1 4-2 6-5 8 0 0-1 0-2 1 0-2 0-3 1-4l1-1v-2c1-3 1-4 1-6z" class="D"></path><path d="M613 666c0-1-1-2 0-3 1 1 2 1 3 1h0c0 1 0 3 1 4 6 1 13 1 19 2-6 1-12 2-19 2v2c1 0 1 1 1 1-1 1-1 2-2 2h-2v-1c2-2 0-6 1-9v-3c-1 1-2 1-2 2z" class="K"></path><path d="M639 762h11c2 0 4 0 5 1-1 0-7 3-8 3-3 2-7 2-11 3l1-1c-1-1-1-2-1-3l-1-1c1-1 3-1 4-1v-1z" class="Y"></path><path d="M636 765c2 0 5 0 7-1l1 1c1 0 2 0 3 1-3 2-7 2-11 3l1-1c-1-1-1-2-1-3z" class="B"></path><path d="M416 243c1-2 1-3 3-4 2 3 1 10 2 15-1 1-1 4 0 5-2 1-3 1-4 0-2-1-2-1-4-1h3v-15z" class="n"></path><path d="M613 666c0-1 1-1 2-2v3c-1 3 1 7-1 9v1h2c1 0 1-1 2-2 0 0 0-1-1-1 5 1 10 1 15 3-5 1-10 2-14 1-1 0-2 1-2 1h-2l-1-1c-1 1-1 2-3 2v1c-1 0-1-1-2-1h0c5-4 3-8 5-13v-1zm-94 242v-1c1-1 1-3 1-4l1-1c2 3-2 6 2 9v31c0 3 1 13 0 14v1c-1-1-1 0-1-1-1-11-1-23-1-34 0-3 1-8 0-11-1 0-1-1-2-1v-2h0z" class="D"></path><path d="M172 110c7-1 14 0 20-1h76c-5 1-11 0-16 0h-31c-3 1-7 0-10 1 2 1 4 0 6 0l-7 1c-6 0-12 1-19 0-3 0-11-1-13 1-2 0-4-1-6-2z" class="R"></path><path d="M488 863c0 2-1 6 0 8v1 8 21c0 2 0 7-1 8h-1l-1-46h3z" class="K"></path><path d="M410 516c-4 0-7-2-11-3l-11-3c4-1 9 0 13 0s9-1 14 0h0l1 1v2l-1 1c-1 0-2-1-2-1-1 0-2 1-2 2l-1 1z" class="o"></path><path d="M420 501l2 1h0v7l1 3v2 1l-2 2-1 1c-3-1-7-1-10-2l1-1c0-1 1-2 2-2 0 0 1 1 2 1l1-1v-2l-1-1h0l3-2c-1-1-2-2-2-4v-2h4v-1z" class="M"></path><path d="M416 504c2 0 3-1 5 0v8l-2 1-1-1v-3-1c-1-1-2-2-2-4z" class="W"></path><path d="M418 508v1 3l1 1c-1 0-2 0-2 1v1c1 1 3 2 4 2l-1 1c-3-1-7-1-10-2l1-1c0-1 1-2 2-2 0 0 1 1 2 1l1-1v-2l-1-1h0l3-2z" class="D"></path><path d="M637 772h0l23 1c-3 2-7 2-9 4-3 1-7 3-10 3h-1-3c-1-2-1-2 0-4 0-1 0 0 1-1-1-1-1-2-1-3z" class="I"></path><defs><linearGradient id="A" x1="640.398" y1="773.726" x2="643.929" y2="779.492" xlink:href="#B"><stop offset="0" stop-color="#131211"></stop><stop offset="1" stop-color="#2d2d2c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M638 775c3 0 6-1 8 0h2c1 0 2 1 3 2-3 1-7 3-10 3h-1-3c-1-2-1-2 0-4 0-1 0 0 1-1z"></path><path d="M496 873c2-3 1-4 2-6h1v-1l1 2v9c0 1 0 3-1 4l-1 1v10 27c0 4 1 9-1 13h0c-1-2-1-6-1-9l1-19v-14-7c0-1-1-3-2-4h0c1-2 1-4 1-6z" class="w"></path><path d="M624 313l3 1c1 1 0 1 1 0l1 7c2 0 5-1 7 0s3 5 3 7 0 6-1 8v-2-1l-2 2h-1v-1c0-3-1-6-3-8v-1-1l-3 1c-1-2-1-2-3-2l-1 1h0c1 1 1 1 1 2h-2c-1-2-1-3-1-4l3-3c-1-2 0-2 0-4h-2v-2z" class="J"></path><path d="M632 326l2-2h1c1 3 2 7 1 11h-1v-1c0-3-1-6-3-8z" class="K"></path><path d="M494 825c1 2 1 4 1 7 1 1 1 1 0 2 0 2 1 3 1 5 0 1 0 1 1 3h0c1 1 1 2 0 3v1h0c1 2 0 3 1 5 1 3-1 5 2 7v10h0l-1-2v1h-1c-1 2 0 3-2 6v-3c-1-1-1-2-1-3-2-10 0-20-1-30v-12z" class="Z"></path><path d="M417 578h1v2l1-1c0 2-1 3 0 4l1 1v3h1l1-2h1v11h-1c-1-1-2-1-3-2 0-2 1-7 0-9-2 2-1 2-1 4-1 0-2-1-3-1h-1c-2-1-4-1-6-2h0c-2 0-3 0-4-1 2-2 2-1 3-2h-3c-1 1-1 1-2 1-1-1-2 0-4-1h-1 0-1v-1h5c2 0 4-1 6-1 3 0 8 1 10 0v-3z" class="N"></path><defs><linearGradient id="C" x1="619.345" y1="689.674" x2="629.145" y2="697.252" xlink:href="#B"><stop offset="0" stop-color="#4b4a47"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#C)" d="M629 685l2 1v1l1 1c1 0 1 0 2 1 0 2 1 3 0 4h-2c1 1 2 1 2 2v2l-14-1h-4v-2c1-1 3-2 4-2s1 1 2 0v-3h-3v-1-1h2 1c3 0 5 0 7-2z"></path><path d="M629 685l2 1v1l1 1c1 0 1 0 2 1-1 1-1 1-2 1l-10-1h-3v-1-1h2 1c3 0 5 0 7-2z" class="O"></path><defs><linearGradient id="D" x1="609.728" y1="525.797" x2="622.643" y2="503.922" xlink:href="#B"><stop offset="0" stop-color="#121314"></stop><stop offset="1" stop-color="#403f37"></stop></linearGradient></defs><path fill="url(#D)" d="M615 497c1 0 2 1 3 2v1 18c3 0 6 0 9 1h0c-2 2-8 0-9 2v2c2 1 4 1 6 2s5 2 7 3c-3 0-7 0-11-1h-2c-1 1-1 2-1 3l-1 1-2-2-1-1h0c1-1 1-2 1-3h-1v-1c2-4 1-6 1-11 1-1 1-14 1-16z"></path><path d="M617 468h1c-1 2-1 3 0 5 3 1 8 0 11 1 1 2 0 4 0 5l-1 1-10 4v7l-2-1-1-2v-6c0-4-1-9 0-13h1l1-1z" class="o"></path><path d="M629 474c1 2 0 4 0 5l-1 1c-1-3-4-1-6-2-1-1-3-1-4-2v-1c3-1 7 0 10 0l1-1z" class="j"></path><path d="M815 348c1 0 0-2 0-2l1-1c1 2-1 5 0 7l1-2v-1 81h0l-5-41c1 2 1 2 1 4l1-1v-1c0-2 0-2 1-3h0c1-2 1-7 1-9 0-7 1-14 0-20l-1-1v-10z" class="K"></path><path d="M326 867l-1-1h0l-6 1-19 4c-1 0-3 0-4 1v2c-1 1-1 2-1 3v1c-1-1-1-4-1-6-1 0-3 1-4 0l1-1h4c1-1 2 0 4-1h3c1-1 0-1 1-1h4l2-1c1 0 3 0 4-1h2l12-3 18-7c3-1 6-3 10-4v4c-2 0-3 2-5 2-2 2-5 2-7 3 0 1-1 1-1 2s1 1 0 2h-1v-3c-3 0-5 1-7 3-2 1-4 3-6 3l-2-1v-1h0z" class="U"></path><path d="M326 867v-2c1-1 2-1 3-1h1 2c-1 2-3 3-4 5l-2-1v-1z" class="j"></path><defs><linearGradient id="E" x1="417.936" y1="467.444" x2="411.354" y2="475.692" xlink:href="#B"><stop offset="0" stop-color="#0b0b0a"></stop><stop offset="1" stop-color="#353433"></stop></linearGradient></defs><path fill="url(#E)" d="M412 464c1 0 1-1 2 0 2 1 3 2 4 4l1 1c1 1 2 2 2 4 2 1 2 1 3 3l1 1v1l-1 5c0 2 0 1-1 2 0 1-1 2-1 4v1c-1 1-2 3-3 4 0-1 0-2-1-2l-1-1h0c1-3 1-6 2-8h-1s0 1-1 2c0 1 0 1-1 1-1-1-1-2-1-4v-10c0-1 0-3-1-4 0-2-2-3-2-4z"></path><path d="M419 471c2 3 1 7 1 11l-1 1h-1s0 1-1 2c0 1 0 1-1 1-1-1-1-2-1-4 1 0 1-1 2-2 0-1 0-2 1-3 0-2 1-4 1-6z" class="Y"></path><path d="M419 469c1 1 2 2 2 4 2 1 2 1 3 3l1 1v1l-1 5c-1-1-1-1-1-2 0-2 1-2 1-4h-1l-1 1c-1 1-1 2-2 3v1c0-4 1-8-1-11l-1-1 1-1z" class="O"></path><path d="M420 482v-1c1-1 1-2 2-3l1-1h1c0 2-1 2-1 4 0 1 0 1 1 2 0 2 0 1-1 2 0 1-1 2-1 4v1c-1 1-2 3-3 4 0-1 0-2-1-2l-1-1h0c1-3 1-6 2-8l1-1z" class="M"></path><defs><linearGradient id="F" x1="622.499" y1="444.764" x2="613.381" y2="446.657" xlink:href="#B"><stop offset="0" stop-color="#181816"></stop><stop offset="1" stop-color="#333232"></stop></linearGradient></defs><path fill="url(#F)" d="M617 412c3 8 2 16 4 24 1 6 3 12 4 18h-1l-2 1c0 2 0 3-1 6v2l-1 1c-1 1-1 2-1 4h-1-1v-42c1-1 0-2 0-3 1-4-1-8 0-11z"></path><path d="M621 436c1 6 3 12 4 18h-1l-2 1c0-6-3-13-1-19z" class="L"></path><path d="M617 378l15 1 7 1h0 0l1 1h-1c2 0 4 1 5 1 2 0 5-1 7 0-3 2-6 3-10 4-5 1-10 1-15 4l-2-2-7 1h-1l2-2-1-3v-5-1z" class="Q"></path><path d="M617 378l15 1-1 1c-5 1-9 1-13 1l-1-2v-1z" class="L"></path><path d="M617 384h13l-3 2v1l-3 1-7 1h-1l2-2-1-3z" class="B"></path><path d="M627 386v1l-3 1-7 1h-1l2-2 9-1z" class="k"></path><path d="M619 650c6 2 11 1 17 2 2 1 5 0 7 1 6 0 13-1 19 0-4 1-7 2-11 3-3 1-7 2-10 2-1 0-3 1-4 1-2 1-4 2-7 3-1-1-2-1-3-2h-4l-6-1h0l-1-1c0-1 0-1-1-3l-2-1c2 0 2 0 3-1 0-1 0-1 1-3l1 1 1-1z" class="w"></path><path d="M616 653c2 1 2 1 4 1l1 2h1c1-1 1-1 3-1h2l1 2c-4 0-8 0-11 2h0 0l-1-1c0-1 0-1-1-3l-2-1c2 0 2 0 3-1z" class="Z"></path><path d="M616 653c2 1 2 1 4 1l1 2h-4c0 1 0 1-1 2 0-1 0-1-1-3l-2-1c2 0 2 0 3-1z" class="D"></path><path d="M628 657h0c2 0 3-1 4-1h11c1-1 3-1 4-1v1c-1 1-2 1-4 1h-3l1 1c-1 0-3 1-4 1-2 1-4 2-7 3-1-1-2-1-3-2h-4l-6-1h0c3-2 7-2 11-2z" class="o"></path><path d="M627 660h1l-4-1c2 0 5-1 8-1h2c1 0 2 0 3 1-2 1-4 2-7 3-1-1-2-1-3-2z" class="I"></path><path d="M625 291c1 0 3 0 4 1 2 1 4 1 6 0 2 0 2 0 3 1 3 3 3 7 3 11 0 2-1 7-3 9-1 1-2 1-3 1-2 0-4-1-6-1l-1 1c-1 1 0 1-1 0 0-1 0-1-1-2l1-1h2c0-2 0-2-2-4h0l-1-1c0-2 0-2 1-4h1v-2-1l2-1-2-1v-2-2l-3-2z" class="Q"></path><path d="M636 308c1 2 1 2 1 3h0c-1 1-1 2-2 2l-1-1 1-3 1-1z" class="H"></path><path d="M637 307v-2c-1-3 0-6-1-8v-1h1c0 1 1 2 1 4v1c1 2 1 7 0 9l-1 1c0-1 0-1-1-3l1-1z" class="G"></path><path d="M630 298l2 2 1-1c0-1-1-1-1-2h1c1 2 1 5 1 7-2 0-4-1-5-1l-1-4 2-1z" class="k"></path><path d="M627 302h1v-2-1l1 4c1 0 3 1 5 1l2 2 1 1-1 1-1 1c-1-2-1-3-3-4-2 1-3 1-5 2h0l-1-1c0-2 0-2 1-4z" class="C"></path><path d="M611 641h1v1 3l1-1 1 1v2c-1 1 0 2 0 3 1 0 1 0 2-1h0c1 0 1 0 1-1l2 2-1 1-1-1c-1 2-1 2-1 3-1 1-1 1-3 1l2 1c1 2 1 2 1 3l1 1v1l-1 4h0c-1 0-2 0-3-1v-1c-1-1-2-2-3-4h-1-3l-1 1c-3-1-5-1-8 0l-4 4-2 2h0v-1h-1l-1-1 2-2c1-1 2-1 2-2l2-2v-2h-2c-1 0-2-1-2-1-1 0-2 0-2-1h-1l-1-1c-2-1-2-1-2-2l3 1c2 1 6 2 8 2v-1h1c4-1 6-3 10-5 0-2 1-4 1-6h3z" class="K"></path><path d="M611 654l1-1 1 1 2 1c1 2 1 2 1 3l1 1v1l-1 4h0c-1 0-2 0-3-1v-1c-1-1-2-2-3-4v-1h-1l-1-1 3-2z" class="G"></path><path d="M613 662c1 0 3-1 4-2l-1 4h0c-1 0-2 0-3-1v-1z" class="H"></path><path d="M611 654l1-1 1 1 2 1c-1 2-1 3-2 4l-2-2h-1-1l-1-1 3-2z" class="M"></path><path d="M607 647c-1 1-1 2-1 3-1 1-1 2-1 3h0c0 1 0 1 1 2h1-1-1c-1 0-2 1-3 0h-1c-3 0-2 1-3 2h-2v-2h-1-2c-1 0-2-1-2-1-1 0-2 0-2-1h-1l-1-1c-2-1-2-1-2-2l3 1c2 1 6 2 8 2v-1h1c4-1 6-3 10-5z" class="Q"></path><path d="M611 641h1v1 3l1-1 1 1v2c-1 1 0 2 0 3 1 0 1 0 2-1h0c1 0 1 0 1-1l2 2-1 1-1-1c-1 2-1 2-1 3-1 1-1 1-3 1l-1-1-1 1-2-1-2 2h-1c-1-1-1-1-1-2h0c0-1 0-2 1-3 0-1 0-2 1-3 0-2 1-4 1-6h3z" class="J"></path><path d="M611 641h1v1 3c-1 1-1 2-2 3-1 0-2 1-3 2 0 0-1 2-2 3h0c0-1 0-2 1-3 0-1 0-2 1-3 0-2 1-4 1-6h3z" class="O"></path><path d="M417 647v-1l1 1c2 1 3 2 5 2v2l1 2c-2 1-3 3-3 5l-1 2c-1 1-2 1-3 1h-5l-1 3-13-3-2-2h-4l-4-1h-3v-2h-4c3 0 7 0 10-1 2-2 4 0 5-2 7 0 14-1 20 0h0l2-1 1-1-2-4z" class="P"></path><path d="M416 653h0c2 0 3 0 5 1-1 1-1 2-2 2 0 1 2 1 0 2v-1h-1v-1l-1-1-1-1v-1z" class="B"></path><path d="M417 655l1 1v1h1c-4 0-7 0-11-1h2c1-1 6-1 7-1z" class="Z"></path><path d="M412 659h8v1c-1 1-2 1-3 1h-5l-7-1c3 0 5 0 7-1z" class="b"></path><path d="M392 659c6 0 14-1 20 0-2 1-4 1-7 1h-7v1l-2-2h-4z" class="f"></path><path d="M417 647v-1l1 1c2 1 3 2 5 2v2h-2v1c1 0 0 0 1 1-1 0-1 0-1 1-2-1-3-1-5-1l2-1 1-1-2-4z" class="Q"></path><path d="M398 661v-1h7l7 1-1 3-13-3z" class="M"></path><defs><linearGradient id="G" x1="403.998" y1="651.575" x2="405.002" y2="655.925" xlink:href="#B"><stop offset="0" stop-color="#272724"></stop><stop offset="1" stop-color="#464041"></stop></linearGradient></defs><path fill="url(#G)" d="M396 653c7 0 14-1 20 0v1l1 1c-1 0-6 0-7 1h-2c-5-1-11-1-17-1 2-2 4 0 5-2z"></path><path d="M657 789h3 16v3c2 0 3 0 4 1 1 0 2 0 2 1h1c-5 1-13 2-18 1h-5 1c1 0 3 0 5 1h1v1c-1 1-3 2-5 2s-4 0-6 1c-1 0-2-1-3-1-4 0-12 1-16-1 1-1 1-3 1-4v-1c0-1-1-1 0-2 6 1 11 0 17 0h3v-1l-1-1z" class="O"></path><path d="M638 794l17-1c-2 2-3 1-5 1-1 0-1 1-2 1-2 0-3-1-4 0h-1-4l-1-1z" class="M"></path><path d="M638 793c0-1-1-1 0-2 6 1 11 0 17 0h8l1 1c-1 0-1 0-2 1-2 0-6-1-7 0l-17 1v-1z" class="r"></path><path d="M657 789h3 16v3l-14 1c1-1 1-1 2-1l-1-1h-8 3v-1l-1-1z" class="J"></path><path d="M657 789h3l1 1h5v1h-8v-1l-1-1z" class="a"></path><path d="M626 326c0-1 0-1-1-2h0l1-1c2 0 2 0 3 2l3-1v1 1c2 2 3 5 3 8v1h1l2-2v1 2c-1 2-1 3-3 4l-6-2-1 6c0 3 0 6-1 9 0 3-1 4-3 5s-3 0-5-2h0l1-1c-1-2-1-5-1-7v-9-1h1l1-1h1v-1c-1 0-1 1-2 1h-1c0-2 0-2 1-4v1l1-1v-1h2l1-1v1c1 0 1-1 2-1 0-2-1-3 0-5z" class="S"></path><path d="M636 335l2-2v1 2c-1 2-1 3-3 4l-6-2-1 6c-2 0-4-1-5-3h0 2l1-2h-1c-1-1-1-2 0-3 2-1 2 0 4 0v1h3c0 1 0 1 1 1 1-2 1-3 2-4v1h1z" class="Q"></path><path d="M626 326c0-1 0-1-1-2h0l1-1c2 0 2 0 3 2l3-1v1 1c2 2 3 5 3 8-1 1-1 2-2 4-1 0-1 0-1-1h-3v-1l1-1c0 1 0 1 1 1l-1-3h-2v1l-2-1v-1-1c0-2-1-3 0-5z" class="K"></path><path d="M632 325v1c2 2 3 5 3 8-1 1-1 2-2 4-1 0-1 0-1-1h-3v-1l1-1c0 1 0 1 1 1l-1-3c0-1 1-1 2-2l1 1 1-1-1-1v-1-1l-2 2c-2-1-2-1-2-3l3-2z" class="m"></path><path d="M619 339h1c1 1 1 2 2 3 0 1 2 1 2 2 1 0 1 1 1 1 1 1 1-1 2 1 0 1 0 3-1 4v2l1 1c0 3-1 4-3 5s-3 0-5-2h0l1-1c-1-2-1-5-1-7v-9z" class="Q"></path><path d="M619 339h1c1 1 1 2 2 3v13h-2c-1-2-1-5-1-7v-9z" class="n"></path><path d="M625 743l2 1c0 1 1 2 2 2 1 2 1 5 1 7h1c3 1 6 1 9 2v1c-1 2-5 3-7 5h-4c-2-1-5 0-7 0-1-1-2-1-3-1h-9-1v-2l2-1c0-1 1-2 2-3l-3-2-1-3c0-2 1-3 2-5h3l9 2h3v-1l-1-2z" class="Q"></path><path d="M626 755c2 0 3 0 4 1h0c1 2 1 2 2 2v1h-5v-2h0l-1-2z" class="O"></path><path d="M620 756v-1h2c1 0 1-1 3-2v3l2 1h0v2h-4c-1 0-3-1-5-1 0-1 1-1 2-2z" class="K"></path><path d="M623 759l2-2h2v2h-4z" class="C"></path><path d="M609 749h1c0 1 1 1 1 1l2-1v-2h1c1 1 1 4 1 6v3l-2-2-3-2-1-3z" class="J"></path><path d="M625 743l2 1c0 1 1 2 2 2 1 2 1 5 1 7v3h0c-1-1-2-1-4-1l1 2-2-1v-3c-2 1-2 2-3 2h-2v1l-1-1-1 1v-1l-1-4h0c0-2 0-3 1-4 1 0 3 0 4-1h1 3v-1l-1-2z" class="F"></path><path d="M617 751l1-1c1 0 1 1 2 2-1 2-1 2-2 3l-1-4zm12-5c1 2 1 5 1 7v3h0c-1-1-2-1-4-1v-5l1-1c1-1 1-2 2-3z" class="e"></path><path d="M635 578c1 0 2 0 3-1 1 0 2-1 3-2s1-1 2-1l1 1 3 6c-1 1-1 1-2 1l-2 2c1 2 2 2 3 3l2-1c2 2 1 5 1 7 1 1 1 3 0 4-2 0-3-1-5 0v1c1 2 1 2 3 2l1 1c0 3-3 8-5 11-1 1-3 2-4 2h-1c0-1-1-1-1-1h-3 0l4-4h0c0-2 1-3 1-4h-1c0-2-1-3 0-4l-2-2v-1l-1-1 1-7 1-1v-1h0c-2 0-2-3-2-5 0-3-2-5-2-7l2 2z" class="L"></path><path d="M649 597v-2l-1 1c-1-1-2-1-3-2h-1v-1c1-1 2-1 4-1 0 0 0 1 1 1h0c1 1 1 3 0 4z" class="V"></path><path d="M648 601c0 3-3 8-5 11-1 1-3 2-4 2h-1c0-1-1-1-1-1h-3 0l4-4h0 0l3 3 2-2c1-1 0-1 1-2s2-3 3-5c0-1 0-1 1-2h0z" class="e"></path><path d="M635 578c1 0 2 0 3-1 1 0 2-1 3-2s1-1 2-1l1 1v1c0 2 1 2 1 4v1h-1l-1-2c-1 1-1 1-1 2-1 0-1-1-2-3-1 2 0 3 0 5l2 1h0-1v1c1 2 1 5 1 7v1c-1 2-1 4-2 7v1c0 1-1 2-1 4h-1c0-2-1-3 0-4l-2-2v-1l-1-1 1-7 1-1v-1h0c-2 0-2-3-2-5 0-3-2-5-2-7l2 2z" class="U"></path><path d="M635 583c0-3-2-5-2-7l2 2c1 3 4 6 5 9 0 2-1 3-1 5v1c0 1-1 1-2 1 0 1 1 1 1 2 1 2 1 3 0 5l-2-2v-1l-1-1 1-7 1-1v-1h0c-2 0-2-3-2-5z" class="C"></path><path d="M620 290h1c1 0 2 1 2 1h1l1-1v1l3 2v2 2l2 1-2 1v1 2h-1c-1 2-1 2-1 4l1 1h0c2 2 2 2 2 4h-2l-1 1c1 1 1 1 1 2l-3-1v2h2c0 2-1 2 0 4l-3 3c0 1 0 2 1 4h2c-1 2 0 3 0 5-1 0-1 1-2 1v-1l-1 1h-2v1l-1 1v-1c-1 2-1 2-1 4h1c1 0 1-1 2-1v1h-1l-1 1h-1v1 9l-2-1c-2-1-2-2-3-4l2-2v-3l1-1c1-2 1-15 0-17v-4c2-2 0-4 1-6v-5-2c1-1 0-2 0-3 1-1 0-2 1-3h0 0v-1c1-2 1 0 1-2h0v-4z" class="M"></path><path d="M627 302c0-1-1-1-1-2-1 0-1-1-2-1l1-2c1 1 1 1 2 1l1-1 2 1-2 1v1 2h-1z" class="O"></path><path d="M620 301l1-1 2 2h1v3l-2 1h-1l1-1-1-1h-2l1-3zm-1 20l1-4v-1-6c0-1 0-1 1-2l1 1h1l1-1 1 1c-1 1-2 2-2 3l1 1v2h2c0 2-1 2 0 4l-3 3c0 1 0 2 1 4h2c-1 2 0 3 0 5-1 0-1 1-2 1v-1l-1 1h-2c-1 0-1 0-2-1l1-1c0-2 0-4-1-6v-3z" class="C"></path><path d="M619 321c2 0 2 0 3-1 1 1 1 0 1 1-1 1-2 2-2 3 1 2 1 2 3 3v-1h2c-1 2 0 3 0 5-1 0-1 1-2 1v-1l-1 1h-2c-1 0-1 0-2-1l1-1c0-2 0-4-1-6v-3z" class="J"></path><path d="M624 326h2c-1 2 0 3 0 5-1 0-1 1-2 1v-1l-1 1h-2c-1 0-1 0-2-1l1-1h0c1-1 1-1 2-1s2 0 3-1h0l-1-1v-1z" class="F"></path><defs><linearGradient id="H" x1="769.786" y1="863.966" x2="760.148" y2="890.465" xlink:href="#B"><stop offset="0" stop-color="#d2cecc"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#H)" d="M723 869c1 1 4 1 5 2h3l5 1h4c1 0 2 0 3 1h4 2 4c6 1 13 1 19 2l19 1c5 0 11 0 16 2h0s1 0 2 1c-11 1-24 0-35 0l-50-1-1-1c-1-2 0-5 0-8z"></path><path d="M723 869c1 1 4 1 5 2h3l5 1h4c-2 2-7 1-10 1 1 1 2 1 3 1h-9l-1 3c-1-2 0-5 0-8z" class="W"></path><path d="M512 902l3-2v1l1 2c0 2 0 4 1 6l2-1v2c0 9 1 18 0 27 0 4-1 8 0 11-3 3 1 18-1 20-2-2-1-15-1-19s-2-9-2-14c0-1 0-2-1-3v15c-1 1-1 1-2 0-1 0-2-1-3-2-2-4-1-11-1-15 0-2 0-3-1-5l1-2 2 2 1-11 1-1v-11z" class="u"></path><path d="M517 909l2-1v2c0 9 1 18 0 27-1-3-1-5-1-8 0-1 1-3 0-4l-1-1v-4c-1-4-1-7 0-11z" class="C"></path><path d="M511 914l1-1 1 25c0 3 0 7-1 9-1 0-2-1-3-2-2-4-1-11-1-15 0-2 0-3-1-5l1-2 2 2 1-11z" class="n"></path><path d="M508 923l2 2v19l-1 1c-2-4-1-11-1-15 0-2 0-3-1-5l1-2z" class="D"></path><defs><linearGradient id="I" x1="698.81" y1="859.472" x2="710.937" y2="882.744" xlink:href="#B"><stop offset="0" stop-color="#bfbebc"></stop><stop offset="1" stop-color="#edecee"></stop></linearGradient></defs><path fill="url(#I)" d="M683 856c12 6 24 10 37 13h2 1c0 3-1 6 0 8l1 1c-11 1-22 0-33 0v-6c-1-2-1-4-1-5v-2c1-2 1-2 1-4-3-1-5-2-8-4h0v-1z"></path><defs><linearGradient id="J" x1="224.558" y1="338.045" x2="184.193" y2="310.585" xlink:href="#B"><stop offset="0" stop-color="#ccc9ca"></stop><stop offset="1" stop-color="#f8f9f7"></stop></linearGradient></defs><path fill="url(#J)" d="M202 278l1-1 2 2 4 2 2 1c-1 2-2 5-1 6-2 8-3 15-3 23l-1 27c0 2-1 6-1 8v25l-1 4v-3h-1c0 1 0 2-1 3v-2c-1-5 0-11 0-16v-21-58z"></path><path d="M202 278l1-1 2 2c-2 6-1 14-2 20v7l1 1-1 1v7 20 12c0 1 0 1-1 1v-12-58z" class="f"></path><path d="M505 868c1 0 3-1 4-2 0 3-1 8 0 11 1 1 1 3 1 4v7l1 1c1 3-1 7 1 10-1 4 0 9-1 13v2l-1 11-2-2-1 2-2 1h0v-2h0l-1-1v-8c0 1 0 2-1 3-1 4 0 10 0 14h-1c-1-3 0-7 0-10l-1-23v-7-1c1-2 1-3 2-5l1 1v1c1-1 0-9 1-12l-1-3c1-2 1-3 1-5z" class="M"></path><path d="M508 923c0-3 1-7 2-10h0l1 1-1 11-2-2z" class="Y"></path><path d="M505 868c1 0 3-1 4-2 0 3-1 8 0 11 1 1 1 3 1 4h-2c-1 1-1 2-1 4 0 3 0 6-2 8v11l-1 1c-2-2 0-5-1-8v-6-2l1-1c1-1 0-9 1-12l-1-3c1-2 1-3 1-5z" class="C"></path><path d="M505 868c1 0 3-1 4-2 0 3-1 8 0 11 0 1 0 2-1 3-1-2 0-5 0-7-2 1-2 2-3 3l-1-3c1-2 1-3 1-5z" class="s"></path><path d="M504 873l1-2c1-1 2 0 3 0v2c-2 1-2 2-3 3l-1-3zm101-214l1-1h3 1c1 2 2 3 3 4v1c-1 1 0 2 0 3v1c-2 5 0 9-5 13h-2c-3 1-5 1-8 1l2 1h-2c-2 1-3 1-4 0-2 0-3-2-4-3l-1-1c0-1-1-2-1-3 0-2 1-2 0-4 1 0 1-1 1-1l1-1h0l1-4h0l2-2 4-4c3-1 5-1 8 0z" class="L"></path><path d="M607 670l-1 4-2 2-2-3h1l2-2c0-1 1-1 2-1z" class="B"></path><path d="M606 674l2-3 1 1c0 2 0 3-2 5l-3 1-1-1 1-1 2-2zm-13-11l4-4c3-1 5-1 8 0 2 1 4 2 5 4v1c1 1 1 3 0 4v2l-1-1c-2-3-5-7-8-7h-2l-1 1h-1l-2 3-2-3z" class="h"></path><path d="M598 663h1l3 1c2 2 4 4 5 6-1 0-2 0-2 1l-2 2h-1l-1 1c-1 0-2-1-3-2h-1l-3-2c0-2 0-3 1-4l2-3h1z" class="Q"></path><path d="M597 663l1 2-2 2 1 2 1 3h-1l-3-2c0-2 0-3 1-4l2-3z" class="B"></path><path d="M597 669c1-1 2-2 3-2h1l-2 1v1h0 2v-1-1l1 1c2 0 2 1 3 3l-2 2h-1l-1 1c-1 0-2-1-3-2l-1-3z" class="P"></path><path d="M602 668c2 0 2 1 3 3l-2 2c-1-1-2-1-2-2v-1c1-1 1-2 1-2z" class="f"></path><path d="M591 665h0l2-2 2 3c-1 1-1 2-1 4l3 2h1c1 1 2 2 3 2l1-1 2 3-1 1 1 1h-1c-1 0-3 1-4 0h-1c-1 0-2-1-3-2v1c1 1 1 2 3 2h0c2 1 5 1 7 0l1 1c-3 1-5 1-8 1l2 1h-2c-2 1-3 1-4 0-2 0-3-2-4-3l-1-1c0-1-1-2-1-3 0-2 1-2 0-4 1 0 1-1 1-1l1-1h0l1-4z" class="k"></path><path d="M594 670l3 2h1c1 1 2 2 3 2l1-1 2 3-1 1c-2 0-4-1-6-2s-3-3-3-5z" class="I"></path><path d="M591 665h0l2-2 2 3c-1 1-1 2-1 4l-1 1c1 1 1 3 1 4l-1 1h-1c-1-2-1-4-2-7l1-4z" class="y"></path><path d="M590 669h0c1 3 1 5 2 7 2 2 4 3 6 5h0l2 1h-2c-2 1-3 1-4 0-2 0-3-2-4-3l-1-1c0-1-1-2-1-3 0-2 1-2 0-4 1 0 1-1 1-1l1-1z" class="O"></path><path d="M199 160c3 3 3 6 4 10 2 6 5 11 10 15 2-2 3-4 5-6 3 1 2 0 4 2v3c3 1 6 2 9 1l1 2c0 1-1 2-2 3h0-3l-2-1-4-1c-2 0-3-1-5-1 0 1-1 2-1 3h0v2h0c-1 1-1 3-2 4 0 2-1 3-2 4v1c0 3-1 4-3 5h0c-2 1-2 1-3 2v1h0v1c-1 4-2 6-3 10l-1-47c0-4-2-8-2-13z" class="R"></path><path d="M216 187c-1-1-1-1-1-2l2-2c1 0 3 1 5 1 3 1 6 2 9 1l1 2c0 1-1 2-2 3h0-3l-2-1-4-1c-2 0-3-1-5-1z" class="C"></path><path d="M207 197v-1c1-1 0 0 1-2l1-1-1-1v-1c2-2 3-2 5-4l2 2c-1 1-1 2-2 3-2 2-3 5-3 8-2 1-2 2-2 4v2h0c-2 1-2 1-3 2v1h0v1c-1-1-1 0-2-1l2-2v-1h-1v-2c0-1 1-2 0-3v-1c-1-1 0-2 0-3h3z" class="U"></path><path d="M204 197h3c0 2-1 4-2 7h-1c0-1 1-2 0-3v-1c-1-1 0-2 0-3z" class="X"></path><path d="M201 173l1-2v1c1 1 1 1 1 2 2 2 2 11 1 14h0l1 1-2 3h0l1 2-1 2 1 1c0 1-1 2 0 3v1c1 1 0 2 0 3v2h1v1l-2 2c1 1 1 0 2 1-1 4-2 6-3 10l-1-47z" class="N"></path><path d="M268 109h356c-3 1-6 1-9 1h-12-40-17-127-28-33v22h-2v-22h-63c0 2-1 4-1 6-2-2 0-4-1-5l-74-1c-2 0-4 1-6 0 3-1 7 0 10-1h31c5 0 11 1 16 0z" class="E"></path><defs><linearGradient id="K" x1="440.482" y1="621.061" x2="432.76" y2="651.001" xlink:href="#B"><stop offset="0" stop-color="#221c1f"></stop><stop offset="1" stop-color="#3a3f3a"></stop></linearGradient></defs><path fill="url(#K)" d="M448 622l1 1c0 4-5 8-8 11-1 2-3 4-5 6s-3 4-5 6l-6 6h2v1c-1 2-1 3-1 4h-1v3l-1 1h1l1-3h1v3h2l2 1h2v1l-1 1c2 2 4 4 4 7l1 3c0 2-1 4-2 7l-1 2-1 2c-2 2-4 3-7 4h-3l-1-1h2v-1h0c-2-1-3-1-4-3l-1-1c-1-3-1-5 0-8h-1-2v3h-1v-2l-1-1-1-1v-1c1 0 1-1 1-1 1-2 3-4 3-6l-1-1c-2 0-3 0-5-1l1-3h5c1 0 2 0 3-1l1-2c0-2 1-4 3-5l-1-2v-2c2 0 3-2 4-3h0c5-4 9-9 12-14 3-3 6-6 8-10h1z"></path><path d="M425 660c0-1-1-1-1-1h-1v-1l1-2c0-2 1-3 1-4h2v1c-1 2-1 3-1 4h-1v3z" class="o"></path><path d="M412 661h5l1 2c0 2-1 1-2 2-2 0-3 0-5-1l1-3z" class="S"></path><path d="M416 675c1-1 1-3 1-4v-2c1-2 5-6 7-6 3-1 6-1 8 1s4 4 4 7c-2 0-2-1-4 1v-1c-1-1-3-3-5-4h-2c-4 2-4 4-6 7v1h-1-2z" class="c"></path><path d="M425 667h2c2 1 4 3 5 4v1c2-2 2-1 4-1l1 3c0 2-1 4-2 7l-1 2-1 2c-2 2-4 3-7 4h-3l-1-1h2v-1h0c-2-1-3-1-4-3l-1-1c-1-3-1-5 0-8v-1c2-3 2-5 6-7z" class="X"></path><path d="M432 672c2-2 2-1 4-1l1 3c-3-1-3 0-5-2z" class="V"></path><path d="M425 667c2 1 2 2 3 3-1 1-1 1-3 1-1 1-3 3-3 5v1h1l1-1 3 2-1 1 1 1c1-1 2-1 3-2v1h0c-1 1-2 3-2 4v1c2 0 2-2 4-4l1 1c0 1-1 1-1 2l-1 1c-1 2-3 3-6 3h-1 0c-2-1-3-1-4-3l-1-1c-1-3-1-5 0-8v-1c2-3 2-5 6-7z" class="f"></path><path d="M629 797c1 1 2 1 4 1l1 1 1-2c1-3 0-6 1-8 3 1 5 0 8 0h13l1 1v1h-3c-6 0-11 1-17 0-1 1 0 1 0 2v1c0 1 0 3-1 4 4 2 12 1 16 1 1 0 2 1 3 1 0 1-1 3-3 3-4 2-10 2-14 2 0 2 0 2 1 4 2 1 4 1 6 1 4 0 8 0 12 1l-15 3h0c3 4 5 9 7 13 1 2 1 3 2 4l-2 1-1-1h-1c0 1-1 0-2 0v-3l-2-2c1-1 1-1 1-2 0 0 0-1-1-1 0-1 0-4-1-5-2-1-11 0-14 2l-1-2h0c-3-6-8-11-11-17h-1v-1c1 0 2-2 3-3 0-1-1-2-1-3v-2l1-1s1 1 2 1 1 0 2 1l-1 2c1 0 0 0 1 1h1c1 0 2 1 3 2l2-1z" class="u"></path><path d="M629 816l-2-2c0-1 1-1 1-2 0 0 0-1-1-1 0-1 1-2 1-2h2v2l1 1c-1 1-1 3-2 4z" class="H"></path><path d="M631 812l1-4v-3h1c1 2 0 4-1 6 0 2-1 5-2 7l1 1 1-1c3-1 7-1 10-2l1-2c3 4 5 9 7 13 1 2 1 3 2 4l-2 1-1-1h-1c0 1-1 0-2 0v-3l-2-2c1-1 1-1 1-2 0 0 0-1-1-1 0-1 0-4-1-5-2-1-11 0-14 2l-1-2 1-2c1-1 1-3 2-4z" class="Q"></path><path d="M522 859c0-2-1-2-1-3v-1c1-2 1-3 1-4v-1c1 3 1 6 1 8 1 2 1 3 1 5v1l1-3v20 6l1 1v1h1v-1l1-1c0 2 1 5 0 7-1-1-2-1-3-2-1 2-1 3-1 5l-1 14c-4-3 0-6-2-9l-1 1c0 1 0 3-1 4v1h0l-2 1c-1-2-1-4-1-6l-1-2v-1l-3 2v11l-1 1v-2c1-4 0-9 1-13-2-3 0-7-1-10l-1-1v-7c0-1 0-3-1-4-1-3 0-8 0-11h1 1v-2h0c1-2 1-4 1-6l2-1v1 8c0 2 0 3 1 5v-3c0-2 1-7 2-9h0 1c2 0 3 2 4 3v-3z" class="K"></path><path d="M516 903l1-1h1v1c0 2 0 3 1 5h0l-2 1c-1-2-1-4-1-6z" class="O"></path><path d="M518 879c1 1 1 1 1 3l-1 1c1 0 1 1 2 2v5c0 1-1 3-1 4v3h0 0l-1-3c0 1 0 3-1 4-1-2 0-4 0-5h0c2-4 0-10 1-14z" class="B"></path><path d="M517 859h1c0 1 0 2 1 3v2c0 5-1 10-1 15-1 4 1 10-1 14l-1-11-1 2v-13-3c0-2 1-7 2-9h0z" class="l"></path><path d="M515 868h1v1c1 3 1 9 0 13l-1 2v-13-3z" class="L"></path><path d="M517 859h1c0 1 0 2 1 3l-1 4h0c-1 1-1 2-1 3h-1v-1h-1c0-2 1-7 2-9h0zm-5-1l2-1v1 8c0 2 0 3 1 5v13 15h0c-2-2-1-8-1-11l-1 1c0 4 0 8-1 13v11l-1 1v-2c1-4 0-9 1-13-2-3 0-7-1-10l-1-1v-7c0-1 0-3-1-4-1-3 0-8 0-11h1 1v-2h0c1-2 1-4 1-6z" class="h"></path><path d="M509 866h1 1v9c0 1 0 1 1 1v4c-1 3 0 7 0 11v8h0c-2-3 0-7-1-10l-1-1v-7c0-1 0-3-1-4-1-3 0-8 0-11z" class="Q"></path><path d="M522 859c0-2-1-2-1-3v-1c1-2 1-3 1-4v-1c1 3 1 6 1 8 1 2 1 3 1 5v1l1-3v20 6l1 1v1h1v-1l1-1c0 2 1 5 0 7-1-1-2-1-3-2-1 2-1 3-1 5l-3 1h-1v-8-5c-1-1-1-2-2-2l1-1c0-2 0-2-1-3 0-5 1-10 1-15v-2c-1-1-1-2-1-3 2 0 3 2 4 3v-3z" class="J"></path><path d="M518 859c2 0 3 2 4 3 1 3 1 6 1 9 1 3 2 5 1 7 0-1-1-2-1-2v-1 3h-1c-1-2-1-4-1-5-2-3 0-6-2-9v-2c-1-1-1-2-1-3z" class="o"></path><path d="M522 859c0-2-1-2-1-3v-1c1-2 1-3 1-4v-1c1 3 1 6 1 8 1 2 1 3 1 5v1l1-3v20h0l-1 1v-4c1-2 0-4-1-7 0-3 0-6-1-9v-3z" class="d"></path><path d="M520 885c0-2 0-5 1-6 1 1 0 2 0 4 1 2 2 3 4 4v1-1l1 1v1h1v-1l1-1c0 2 1 5 0 7-1-1-2-1-3-2-1 2-1 3-1 5l-3 1h-1v-8-5z" class="a"></path><path d="M521 883c1 2 2 3 4 4v1-1l1 1v1h1v-1l1-1c0 2 1 5 0 7-1-1-2-1-3-2l-1-1v1c-1-1-1-1-1-2v-1c-2-2-1-4-2-6z" class="K"></path><defs><linearGradient id="L" x1="663.859" y1="878.437" x2="680.99" y2="855.116" xlink:href="#B"><stop offset="0" stop-color="#c5c4c4"></stop><stop offset="1" stop-color="#ecebea"></stop></linearGradient></defs><path fill="url(#L)" d="M664 842c6 6 13 9 19 14v1h0c3 2 5 3 8 4 0 2 0 2-1 4v2c0 1 0 3 1 5v6l-2-1c-8 1-17 0-25-1h-1-2-3l5-29v-3l1-1v-1z"></path><path d="M669 862h3l3 3v1c-1 1 0 1-2 1 1 0 1-1 1-1-2 0-3-1-5-2v-2z" class="V"></path><path d="M689 877v-4h-2v-1c1-2 1-3 1-4v-1h1 1c0 1 0 3 1 5v6l-2-1zm-25-35c6 6 13 9 19 14v1c-1 0-3-1-4-2l-2-1c-2-2-4-3-6-4-1-1-2-1-3-2l-1 2-4-3v-3l1-1v-1z" class="i"></path><path d="M661 871c-1-1-1-1-1-2l1-1c1-1 0-4 1-5 0-1 1-2 2-2h2c1 0 1-1 2 0l1 1v2c2 1 3 2 5 2 0 0 0 1-1 1-4 1-6 6-9 9h-1-2v-5z" class="R"></path><path d="M666 861c1 0 1-1 2 0l1 1v2c-1 0-3 0-4-1l1-2z" class="U"></path><path d="M661 871c0-2 1-2 2-3h0v1c0 1 0 2 1 3l2-2h1c-1 2-2 4-4 5v1h-2v-5z" class="E"></path><path d="M422 502h3c0 1 2 1 2 2 1 1 2 2 4 2l1 1 2 1c1 1 2 1 3 2h0 2l1 1 1 2-1 1c1 1 1 1 3 2v1c0 1 0 1 1 2l-1 1c-1 1-1 0-3 1 1 1 1 1 0 3h1v3c0 2 1 8 0 10-1 0-1 1-2 2l-1-1c1-3 1-4 0-6v5l-2 1 1 2-1 3-1 3c-2 1-3 3-5 4-1 1-4 2-5 2v2h0l-4-1c-1-2-3-3-4-4l-4-7c-1-2 0-7 0-10s1-6 4-9c1-1 2-1 2-2 1-2 1-2 1-3l1-1 2-2v-1-2l-1-3v-7h0z" class="T"></path><path d="M414 530c2 1 2 1 3 2l-1 3 1 5-3-2v-3-3-2z" class="c"></path><path d="M434 523v-1c2 2 3 4 4 7 0 1 1 2 0 3v5l-2 1 1 2c-2 2-4 3-6 3 3-3 4-5 5-10l-3 1v-4l-1-1v-1c0-1 1-1 1-2s-1-1 0-3h1z" class="C"></path><path d="M434 523c2 4 2 6 2 10l-3 1v-4l-1-1v-1c0-1 1-1 1-2s-1-1 0-3h1z" class="R"></path><path d="M431 543c2 0 4-1 6-3l-1 3-1 3c-2 1-3 3-5 4-1 1-4 2-5 2v2h0l-4-1c-1-2-3-3-4-4l-4-7h1 0c2 2 5 5 8 5 3 1 6-1 8-3l1-1z" class="M"></path><path d="M417 532c1-1 2-1 3-2 2-2 5-3 8-3l2 2c0 1 1 0 2 0l1 1v4c0 2 0 4-2 5-2 2-5 5-8 4-3 0-4-1-6-3l-1-5 1-3h0z" class="C"></path><path d="M417 532c1-1 2-1 3-2 2-2 5-3 8-3l2 2c0 1 1 0 2 0l1 1-1 2h-4c-1-1 0-1-2-1v1 1h0-1c-1-1-2-1-2 0l-2 1c0 2 0 3 1 5l1 1-1 1-3-3v-1c-1-1-2-1-3-2l1-3h0z" class="M"></path><path d="M422 502h3c0 1 2 1 2 2 1 1 2 2 4 2l1 1 2 1c1 1 2 1 3 2h0 2l1 1 1 2-1 1c1 1 1 1 3 2v1c0 1 0 1 1 2l-1 1c-1 1-1 0-3 1 1 1 1 1 0 3h1v3c0 2 1 8 0 10-1 0-1 1-2 2l-1-1c1-3 1-4 0-6 1-1 0-2 0-3-1-3-2-5-4-7v1h-1c-1 2 0 2 0 3s-1 1-1 2v1c-1 0-2 1-2 0l-2-2c-3 0-6 1-8 3-1 1-2 1-3 2h0c-1-1-1-1-3-2v2h-1c0-3 1-6 4-9 1-1 2-1 2-2 1-2 1-2 1-3l1-1 2-2v-1-2l-1-3v-7h0z" class="h"></path><path d="M422 502h3c0 1 2 1 2 2 1 1 2 2 4 2l1 1 2 1-2 1h-2c-1 2 0 5 0 7l-1-2c-1 0-1-1-2 0-2 0-2 0-3-1h0c1-2 3-1 4-2l1-1-1-1h0-1 0c-2 1-3 0-5 0v-7h0z" class="P"></path><path d="M422 502h3c0 1 2 1 2 2 1 1 2 2 4 2l1 1c-3 0-5 0-7-1l-3-4h0z" class="p"></path><path d="M422 502l3 4v1c2 1 2 1 4 1v2l-1-1h0-1 0c-2 1-3 0-5 0v-7z" class="k"></path><path d="M414 530c1-2 2-4 4-6 0 1 0 1 1 2h1c1-1 2-1 3-2l-1-1c-1 1-2 1-3 1v-1c2 0 3-1 4-2 2-1 4-1 6-1 2 1 3 2 4 3-1 2 0 2 0 3s-1 1-1 2v1c-1 0-2 1-2 0l-2-2c-3 0-6 1-8 3-1 1-2 1-3 2h0c-1-1-1-1-3-2z" class="T"></path><path d="M428 525l-3-3 2-2h2c2 1 3 2 4 3-1 2 0 2 0 3s-1 1-1 2l-4-3z" class="N"></path><path d="M428 525l4 3v1c-1 0-2 1-2 0l-2-2c-3 0-6 1-8 3-1 1-2 1-3 2l3-6c3-1 5-1 8-1z" class="G"></path><path d="M434 508c1 1 2 1 3 2h0 2l1 1 1 2-1 1c1 1 1 1 3 2v1c0 1 0 1 1 2l-1 1c-1 1-1 0-3 1v-1l-2 1v1h-1 0l-1-1v-3c-1 1-2 1-3 0-1 0-2-1-3-2 0-2-1-5 0-7h2l2-1z" class="P"></path><path d="M434 508c1 1 2 1 3 2l-1 1v3l-2 1h-1l-1 1c2 1 2 1 4 1l1-1c0 2 0 2 2 4-1 1-2 1-3 1v-3c-1 1-2 1-3 0-1 0-2-1-3-2 0-2-1-5 0-7h2l2-1z" class="q"></path><path d="M434 515c-1-1 0-1-1-2 0-1-1-2-1-2l1-1c1 0 2 0 3 1v3l-2 1z" class="f"></path><path d="M216 187c2 0 3 1 5 1l4 1 2 1h3 2 1c1 0 3 0 5-1v1l-1 2c-1 1-2 2-3 4l-2 4c-2 2-3 4-5 6-1 2-2 4-4 6-1 3-7 12-7 14v3l-1 1s1 2 1 3c-1 2-4 9-4 11-1-2-2-2-4-2l-1 3v1h0c-1 2-2 4-4 5l1 1h1c-1 2-1 4 0 6v1c-2 5-1 10-2 16l-1 1h1v1l-1 1c-1-2 0-6 0-8l-1-13 1-27c-1-3 0-7 0-10 1-4 2-6 3-10v-1h0v-1c1-1 1-1 3-2h0c2-1 3-2 3-5v-1c1-1 2-2 2-4 1-1 1-3 2-4h0v-2h0c0-1 1-2 1-3z" class="c"></path><path d="M206 228c1 0 2-1 3-1 0 2 0 4-1 6s-1 3-1 5h-1l-2-1h0c1-1 1-1 1-2 1-1 1-4 1-6h0v-1z" class="V"></path><path d="M207 224h1c0 1 1 1 2 1h3c0 2-2 6-3 8v-1c-1 0-2 1-2 1 1-2 1-4 1-6-1 0-2 1-3 1 1-1 1-3 1-4z" class="f"></path><path d="M205 210v-1h0v-1c1-1 1-1 3-2l-1 6-1 2c-1 1-1 2-2 4-1 1 0 6 0 8v2c1 2 1 5 0 7h0c-1-2-1-3-1-5h-1 0c-1-3 0-7 0-10 1-4 2-6 3-10z" class="i"></path><path d="M222 211l1 1c-1 3-7 12-7 14v3l-1 1s1 2 1 3c-1 2-4 9-4 11-1-2-2-2-4-2l-1 3c0-3 2-9 3-12 1-2 3-6 3-8h-3c-1 0-2 0-2-1h-1c-1-2-2-3-2-4v-1l2 1c1 1 2 1 4 1 1-1 1-2 2-3-1-2-1-3-2-4h-3c1-1 1-1 2-1 2 1 3 1 4 3l1 1c1 1 1 1 1 2 2-2 4-5 6-8z" class="B"></path><path d="M216 226v3l-1 1s1 2 1 3c-1 2-4 9-4 11-1-2-2-2-4-2 3-6 4-11 8-16z" class="i"></path><path d="M216 187c2 0 3 1 5 1l4 1 2 1h3 2 1c1 0 3 0 5-1v1l-1 2c-1 1-2 2-3 4l-2 4c-2 2-3 4-5 6-1 2-2 4-4 6l-1-1c-2 3-4 6-6 8 0-1 0-1-1-2l-1-1c-1-2-2-2-4-3-1 0-1 0-2 1l-2 2-2 2c1-2 1-3 2-4l1-2 1-6h0c2-1 3-2 3-5v-1c1-1 2-2 2-4 1-1 1-3 2-4h0v-2h0c0-1 1-2 1-3z" class="R"></path><path d="M216 202l2-1v1c0 1-1 2-2 4h-1c0-1-1-2-1-3l2-1zm7 1l-1-3c1-1 1-2 2-3l3-3h1c-2 3-2 4-3 7l-2 2z" class="X"></path><path d="M209 210c1 1 1 2 2 2h2 3v1c0 2 0 3-1 4l-1-1c-1-2-2-2-4-3-1 0-1 0-2 1l-2 2-2 2c1-2 1-3 2-4l1-2 2-2z" class="f"></path><path d="M228 194c1-1 1-1 2-1-1 3-2 4-3 7 0 1-1 2-2 4 1 0 1 0 1 1v1c-1 0-1 0-2 1v1c-2 1-4 2-5 3l-1 1c-1 0-1-1-2-1v-1l1-1 3-3 3-3h0l2-2c1-3 1-4 3-7z" class="U"></path><path d="M230 190h2 1c1 0 3 0 5-1v1l-1 2c-1 1-2 2-3 4l-2 4c-2 2-3 4-5 6-1 2-2 4-4 6l-1-1c1-1 1-2 2-3v-1c1-1 1-1 2-1v-1c0-1 0-1-1-1 1-2 2-3 2-4 1-3 2-4 3-7-1 0-1 0-2 1h-1c0-1 1-2 1-3-1-1 0-1-1-1h3z" class="E"></path><path d="M216 187c2 0 3 1 5 1l4 1c-1 1-1 2-2 3l1 1c-1 1-2 1-2 2-1 2 0 3-2 4h0-1-1c-1 0-1 0-1-1-2 0-3 1-3 3l-1 1c-1 1-2 2-3 4 0 0 0 1 1 1l-1 1c-1 0 0 0-1 1v1l-2 2 1-6h0c2-1 3-2 3-5v-1c1-1 2-2 2-4 1-1 1-3 2-4h0v-2h0c0-1 1-2 1-3z" class="X"></path><path d="M418 589c0-2-1-2 1-4 1 2 0 7 0 9 1 1 2 1 3 2h1v6s1-1 1-2c1 1 0 2 1 3 6 1 13 0 19 0v2c-1 0-3 0-4 1v2c2 0 3-1 4-1 1 4 0 8 1 12h-3l2 1 1 1v-1c1 1 0 1 2 1v-1l1 2h-1c-2 4-5 7-8 10-3 5-7 10-12 14h0c-1 1-2 3-4 3s-3-1-5-2l-1-1v1h-1l1-5v-16l-1-21c1-1 1-2 1-3v-1-4-4c-1 0-2-1-3-1l3-1h0l1-2z" class="c"></path><path d="M427 641v1c-1 2-2 3-3 4v2l-1-1v-1l-2-2v-2l6-1z" class="g"></path><path d="M420 604v12c0 4 0 8 1 12l-1 2v1h0c-1-6-1-12-1-18v-7c1-1 1-1 1-2z" class="E"></path><path d="M427 641l-2-2c-1 1-3 1-4 1v-4c1 1 3 1 4 1l2-1c2 1 2 1 3 2l-3 4v-1z" class="U"></path><path d="M416 605c1-1 1-2 1-3 2 7 1 16 1 23l-1 1-1-21z" class="E"></path><path d="M420 616l2-1v-2h1c0 3-1 6 1 9-1 1-2 1-3 2 1 1 1 1 2 1 0 2-2 3-2 3-1-4-1-8-1-12z" class="U"></path><path d="M417 642v1c1-1 1-1 1-3l1 1c0 1 0 3 2 4 0 1 1 1 2 1v1l1 1v-2c1 0 2-1 3-1v1c-1 1-2 3-4 3s-3-1-5-2l-1-1v1h-1l1-5z" class="N"></path><path d="M421 628s2-1 2-3c-1 0-1 0-2-1 1-1 2-1 3-2 1 1 2 3 4 3h0l1 1h0c0 1 0 2 1 4l-1 1c-2 0-4 0-6 1h-2 0l-1-2 1-2z" class="W"></path><path d="M423 632c2-1 4-1 6-1l1 1 1-1v2c0 1 1 1 2 2l-3 3c-1-1-1-1-3-2l-2 1c-1 0-3 0-4-1v-4h2z" class="F"></path><path d="M423 632c2-1 4-1 6-1l1 1c0 1 0 2-1 3l-2-2c-1 0-3 0-4-1z" class="D"></path><path d="M431 631v2c0 1 1 1 2 2l-3 3c-1-1-1-1-3-2l2-1c1-1 1-2 1-3l1-1z" class="X"></path><path d="M419 594c1 1 2 1 3 2h1v6s1-1 1-2c1 1 0 2 1 3v3 3 2h-2v2h-1v2l-2 1v-12c0-2 1-5 1-6h-2v-4z" class="g"></path><path d="M423 602s1-1 1-2c1 1 0 2 1 3v3 3 2h-2v-9z" class="L"></path><path d="M423 611h2 0c0 2-1 5 0 8 1 0 2 0 3 1 5-1 10-1 14-1l2 1 1 1c-2 0-4 0-6 1-1 2-3 3-4 5l-4 4-1 1-1-1 1-1c-1-2-1-3-1-4h0l-1-1h0c-2 0-3-2-4-3-2-3-1-6-1-9v-2z" class="Q"></path><path d="M428 620c5-1 10-1 14-1l2 1 1 1c-2 0-4 0-6 1l-1-1c-3-1-6 0-9 0-1 0-1-1-2-1h-1 2z" class="B"></path><path d="M428 625v-1c0-1-1-1-2-1l-1-1c2-1 5-1 7 0l6-1 1 1c-1 2-3 3-4 5l-4 4-1 1-1-1 1-1c-1-2-1-3-1-4h0l-1-1z" class="q"></path><path d="M428 625v-1c0-1-1-1-2-1l-1-1c2-1 5-1 7 0v1c-1 0-1 0-2 2h1 2c-1 2-2 3-3 5-1-2-1-3-1-4h0l-1-1z" class="X"></path><path d="M445 620c1 1 0 1 2 1v-1l1 2h-1c-2 4-5 7-8 10-3 5-7 10-12 14h0v-1c-1 0-2 1-3 1 1-1 2-2 3-4l3-4 3-3c-1-1-2-1-2-2v-2l4-4c1-2 3-3 4-5 2-1 4-1 6-1v-1z" class="T"></path><path d="M445 620c1 1 0 1 2 1v-1l1 2h-1l-2-1c-5 4-9 9-12 14-1-1-2-1-2-2v-2l4-4c1-2 3-3 4-5 2-1 4-1 6-1v-1z" class="R"></path><path d="M425 603c6 1 13 0 19 0v2c-1 0-3 0-4 1v2c2 0 3-1 4-1 1 4 0 8 1 12h-3c-4 0-9 0-14 1-1-1-2-1-3-1-1-3 0-6 0-8h0v-2-3-3z" class="U"></path><path d="M425 611c2 1 2 1 3 3 0 1 0 0-1 1s-2 2-2 4c-1-3 0-6 0-8z" class="E"></path><path d="M425 606h8c2-1 3-1 5-1l1 2-1 1h-1c-2-1-9-1-11 1h-1v-3z" class="T"></path><path d="M427 258c7-1 14-5 21-8 3-2 7-3 10-4v3c-1 6-1 13 0 20v3l-1 8-6 1c-11 0-21 6-32 9h-4c-2-1-7-1-9-3h6c1-1 1-1 1-2l1-1h0c-1-2-1-4-1-6l-2-1c-1-1-2-1-3-2h1 3l1-1c0-2 0-3-1-4s-1-2-1-4h0c-3-1-7-2-11-3h11c4 0 7-1 11-2l5-3z" class="j"></path><path d="M446 276v-2l3-3c1 1 2 2 2 4l-5 1z" class="N"></path><path d="M427 258c7-1 14-5 21-8 3-2 7-3 10-4v3l-37 13c-1 0-2 1-3 1l-3 2c0 1 1 1 1 2-1 0-2-1-2-1l-2-2c0 1-1 0-1 1v1h0c-3-1-7-2-11-3h11c4 0 7-1 11-2l5-3z" class="S"></path><path d="M451 275l7-3-1 8-6 1c-11 0-21 6-32 9h-4c-2-1-7-1-9-3h6l34-11 5-1z" class="G"></path><path d="M418 263c1 0 2-1 3-1 0 2 0 2 2 3h1c1 0 2-1 3-1 1 2 2 4 2 6h0v4l1 1v-1c1 0 1-1 2-1 1 1 1 2 1 3 1-1 1 0 2-1 1 0 2 0 3 1h0c-2 2-3 2-6 3-1 0-2 1-3 1l-8 3h-2-1c1-1 1-1 1-3h-1c-2 0-2 0-4-1l-1-1-2-1c-1-1-2-1-3-2h1 3l1-1c0-2 0-3-1-4s-1-2-1-4v-1c0-1 1 0 1-1l2 2s1 1 2 1c0-1-1-1-1-2l3-2z" class="V"></path><path d="M423 267h3c1 2 1 3 1 5-1 2-2 2-4 3h-2v-1-3-1l1 1 1-1v-3zm-12-1v-1c0-1 1 0 1-1l2 2s1 1 2 1c0-1-1-1-1-2l3-2c1 1 3 2 4 4l-2 2c-1 1-1 2-1 3-1 0-2 0-3-1h-3v3c0-2 0-3-1-4s-1-2-1-4z" class="W"></path><path d="M436 543l4 2h1 0l1 1h0v2c0 1-1 3-1 4l1 3h4 1 1 1l2 1c1 0 1 0 2 1 1 6 3 15 1 21v4c1 3 1 7 0 9l-1 1h0c0 2-1 3-3 4l-2 1v8h0l-1 15v1c-2 0-1 0-2-1v1l-1-1-2-1h3c-1-4 0-8-1-12-1 0-2 1-4 1v-2c1-1 3-1 4-1v-2c-6 0-13 1-19 0-1-1 0-2-1-3 0 1-1 2-1 2v-6-11h-1l-1 2h-1v-3l-1-1c-1-1 0-2 0-4l-1 1v-2h-1c-1-3 0-7 0-10 0-1-1-2-1-3 0-5 0-10 1-15v-1c1 1 3 2 4 4l4 1h0v-2c1 0 4-1 5-2 2-1 3-3 5-4l1-3z" class="T"></path><path d="M423 559c1 0 3-1 5-1 2 1 3 1 5 2l-1 1h-1l-2 1-1 1h-2v3c-1-1-1-3-1-4v-1l-1-1c-1 1-1 3-1 5v-6z" class="N"></path><path d="M431 558c5 0 9-1 13-1l1 2-1 3v1c-1 0-2-1-3-2h0l-1-1v1c0 1-1 1-1 2-1 0-2-1-3-1h0l-1 1c-2 0-3-1-4-2h1l1-1c-2-1-3-1-5-2h3z" class="i"></path><path d="M436 543l4 2h1 0l1 1h0v2c0 1-1 3-1 4l-1 3c1 1 4 0 4 2-4 0-8 1-13 1v-1h-4v-2c-1-1-1-1-2-1v-2c1 0 4-1 5-2 2-1 3-3 5-4l1-3z" class="l"></path><path d="M436 543l4 2h1l-1 1h-1-1-3l1-3z" class="v"></path><path d="M423 565c0-2 0-4 1-5l1 1v1 4c1 1 9 0 11 0 3-1 6-1 9-1v1 1h-1-3c-4 0-12 0-16 1v3 7 2 1l1 1h0c0 1 1 2 1 2h2c-1 1-1 2-1 2l-3 3h0v2 3 4 4h1 0c2-1 4 0 6-1 3 1 6 0 10 0h3c0 1 0 1-1 2-6 0-13 1-19 0-1-1 0-2-1-3 0 1-1 2-1 2v-6-11h-1l-1 2h-1v-3l-1-1c-1-1 0-2 0-4h1c0 2 1 2 3 4v-1-17z" class="Y"></path><path d="M425 581l1 1h0c0 1 1 2 1 2h2c-1 1-1 2-1 2l-3 3c-1-2 0-6 0-8z" class="X"></path><path d="M439 591h5v4h-4l1 1 3 1s1 0 1 1c-1 1-1 1-1 2h-2v1c-4 0-7 1-10 0-2 1-4 0-6 1h0-1v-4-4-3h14z" class="E"></path><path d="M425 598c1 0 1-1 2-1 2 0 3 0 5 1l-1 1-1 1v1l2-1h0l1-1h0l-1 2c-2 1-4 0-6 1h0-1v-4z" class="c"></path><path d="M439 591h5v4h-4c-5-1-10 0-15-1v-3h14z" class="W"></path><path d="M425 571v-3c4-1 12-1 16-1h3c1 1 1 2 1 3 0 2 0 4-1 7h0-14l-5 1v-7z" class="i"></path><path d="M425 571v-3c4-1 12-1 16-1h3c1 1 1 2 1 3h-3l-1-1c0 1-1 1-1 2l-2-1-1-1c-1 0-1 1-2 1-1 1-3 0-4 0h-4v1l1 1v1h-1l-1-1-1-1z" class="N"></path><path d="M440 571c0-1 1-1 1-2l1 1h3c0 2 0 4-1 7h0-14 3v-1c1-1 2-2 3-2h0c1 0 1-1 2-2 0 0 0-1-1-1l1-1 2 1z" class="E"></path><path d="M438 570l2 1 1 2h0l-1 2c-2 0-3 0-4-1 1 0 1-1 2-2 0 0 0-1-1-1l1-1z" class="V"></path><path d="M440 571c0-1 1-1 1-2l1 1h3c0 2 0 4-1 7-1-1-2-1-3-1l1-1-1-2h0l-1-2z" class="R"></path><path d="M440 571c0-1 1-1 1-2l1 1 1 2c-1 1-1 1-2 1l-1-2z" class="T"></path><path d="M417 549c1 1 3 2 4 4l4 1h0c1 0 1 0 2 1v2h4v1h-3c-2 0-4 1-5 1v6 17 1c-2-2-3-2-3-4h-1l-1 1v-2h-1c-1-3 0-7 0-10 0-1-1-2-1-3 0-5 0-10 1-15v-1z" class="W"></path><path d="M417 549c1 1 3 2 4 4l4 1h0c1 0 1 0 2 1v2h4v1h-3c-2 0-4 1-5 1s-2 1-3 0-1-3-1-5h-1v2h-1v-6-1z" class="K"></path><path d="M425 554c1 0 1 0 2 1v2c-2 0-4 0-6-1-1-1-1-1 0-2h4 0z" class="V"></path><path d="M444 577h2v2 10h-2v1c-1 0-3 1-5 1h-14v-2h0l3-3s0-1 1-2h-2s-1-1-1-2h0l-1-1v-1-2l5-1h14z" class="t"></path><path d="M444 577h2v2h-1l-20 1v-2l5-1h14z" class="B"></path><path d="M446 579v10h-2 0c-2 0-3-2-4-3l1-1c-1-1 0-1-1-2l-1 1h-6c-2 0-3-1-4 0h-2s-1-1-1-2h0 1c3 2 7 0 10 0 2 0 5 1 7 0 1-1 1-2 1-3h1z" class="b"></path><g class="R"><path d="M441 585h0 2c1 1 1 1 1 2v2c-2 0-3-2-4-3l1-1z"></path><path d="M439 584l1-1c1 1 0 1 1 2l-1 1c1 1 2 3 4 3h0v1c-1 0-3 1-5 1h-14v-2h0l3-3s0-1 1-2 2 0 4 0h6z"></path></g><path d="M439 584l1-1c1 1 0 1 1 2l-1 1c-2 1-3 1-4 1l-1 1h-1c-1 0-1 0-1-1v-1c2 0 4 0 5-2h1zm-10 0c1-1 2 0 4 0l-1 1c-1 1-2 1-3 2l2 2h-6 0l3-3s0-1 1-2z" class="N"></path><path d="M440 586c1 1 2 3 4 3h0v1c-1 0-3 1-5 1h-14v-2h6 5s0-1-1-1l1-1c1 0 2 0 4-1z" class="D"></path><path d="M440 586c1 1 2 3 4 3h0-8s0-1-1-1l1-1c1 0 2 0 4-1z" class="E"></path><defs><linearGradient id="M" x1="459.185" y1="571.945" x2="441.503" y2="580.445" xlink:href="#B"><stop offset="0" stop-color="#2e312b"></stop><stop offset="1" stop-color="#3f3d40"></stop></linearGradient></defs><path fill="url(#M)" d="M448 555h1l2 1c1 0 1 0 2 1 1 6 3 15 1 21v4c1 3 1 7 0 9l-1 1h0c0 2-1 3-3 4l-2 1v8h0l-1 15v1c-2 0-1 0-2-1v1l-1-1-2-1h3c-1-4 0-8-1-12-1 0-2 1-4 1v-2c1-1 3-1 4-1v-2c1-1 1-1 1-2h-3v-1h2c0-1 0-1 1-2 0-1-1-1-1-1l-3-1-1-1h4v-4h-5c2 0 4-1 5-1v-1h2v-10-2-14c0-2 0-4-1-6v-1h0l2-1h1z"></path><path d="M448 555h1l2 1v8l1 2c-1 3-3 6-4 7v-18z" class="Q"></path><path d="M446 577v-14c0-2 0-4-1-6v-1h0l2-1h1v18 32l-1 15v1c-2 0-1 0-2-1v1l-1-1-2-1h3c-1-4 0-8-1-12-1 0-2 1-4 1v-2c1-1 3-1 4-1v-2c1-1 1-1 1-2h-3v-1h2c0-1 0-1 1-2 0-1-1-1-1-1l-3-1-1-1h4v-4h-5c2 0 4-1 5-1v-1h2v-10-2z" class="g"></path><path d="M444 589h2l-1 16c0 5 1 10 0 15v1l-1-1-2-1h3c-1-4 0-8-1-12-1 0-2 1-4 1v-2c1-1 3-1 4-1v-2c1-1 1-1 1-2h-3v-1h2c0-1 0-1 1-2 0-1-1-1-1-1l-3-1-1-1h4v-4h-5c2 0 4-1 5-1v-1z" class="p"></path><path d="M451 191c1 1 2 3 2 4l-2 1c1 1 1 3 1 4h1 2v2h1l2 6h0c0 2 1 4 0 7 0 1 1 1 0 2-1 2-1 5-1 7 1 5 1 11 1 16v6c-3 1-7 2-10 4-7 3-14 7-21 8l-5 3-1-2c-1-1-1-4 0-5-1-5 0-12-2-15l8-6h0l-10 6c-1-5-1-12-3-17l-3-6v-2l1-1h1c1 2 2 2 4 2 1 1 1 1 3 1v-1h2l1-1c2-1 3-3 5-5v2h2c1-1 2-1 3-2h1l1-1 2-2c1-1 0-1 1-2 0-1 2-2 2-2 1-2 1-3 2-4v-1l1-2h0l2-2h2l1-1c1 0 2 0 3-1z" class="R"></path><path d="M452 214l-2-1v-1l1-1 1-1c0-1 1 0 2-1l2-2 1 3h0l-1 1-4 3z" class="N"></path><path d="M423 224c1-1 2-2 3-2 1-1 2-1 3-2h1 1l1 2-2 3c1 1 1 0 0 1v-2c-2 0-5 1-7 1v-1z" class="X"></path><path d="M423 224l-2 2h3l-1 1c-3 0-1 3-2 5-1 0-1-1-1-2v-2c-1-1-2-1-2-2 0-2 0-3-1-4s-1-1-1-2h1v1l1 2h3 2v1z" class="U"></path><path d="M452 200h1l-1 1h0s1 0 1 1c1 1 1 2 1 3h0l-2 2c-1-1 0-1 0-2-2 0-3 2-5 1-1-1-1-2-1-3v-1-2c1 2 0 3 2 4 1-1 1-2 2-3l2-1z" class="f"></path><path d="M451 191c1 1 2 3 2 4l-2 1c1 1 1 3 1 4l-2 1c-1 1-1 2-2 3-2-1-1-2-2-4h-1 0c-1 0-2 1-2 1l-3 1c1-2 1-3 2-4v-1l1-2h0l2-2h2l1-1c1 0 2 0 3-1z" class="q"></path><path d="M442 198l2-1 1 1 2 1v-1h2v3h1c-1 1-1 2-2 3-2-1-1-2-2-4h-1 0c-1 0-2 1-2 1l-3 1c1-2 1-3 2-4z" class="T"></path><path d="M451 191c1 1 2 3 2 4l-2 1h-2s-1-1-1-2c-2 2-3 1-5 1h0l2-2h2l1-1c1 0 2 0 3-1z" class="m"></path><path d="M440 202l3-1c-2 1-3 2-4 3v3h-1c-1 0-1 0-2 1-1 3-4 5-5 8-1 2 0 2-1 3l-2-1v-2h-1c-1 2-1 1-3 2-1 0-1 0-1 1-1 0-3 1-4 2h-2v-1c-1-2-2-3-3-4s-2-1-3-2l1-1h1c1 2 2 2 4 2 1 1 1 1 3 1v-1h2l1-1c2-1 3-3 5-5v2h2c1-1 2-1 3-2h1l1-1 2-2c1-1 0-1 1-2 0-1 2-2 2-2z" class="f"></path><path d="M430 211c1-1 2-1 3-2-1 3-2 4-4 5h-1l2-3z" class="T"></path><path d="M423 214c2-1 3-3 5-5v2h2l-2 3s-1-1-2 0c-1 0-2 1-3 1h0v2h-1-2l-1 1c-2 0-4-2-5-2-1-1-2-1-3-2l1-1h1c1 2 2 2 4 2 1 1 1 1 3 1v-1h2l1-1z" class="q"></path><path d="M453 200h2v2h1l2 6h0c0 2 1 4 0 7v-1c-1 2-2 3-3 4h-1l-1 1c-1 1-2 2-4 3v2c-1 2-1 5-1 6s-1 2-1 4h-1l-1-1v-1c-2 1-3 1-4 3-1 1-1 2-2 3h-1c0-2 0-2-1-3-1 1-1 2-2 3h-1c0-1 0-2-1-3h0v-3c1-1 2-2 2-3v-1l-1 1-1-1c1 0 2-1 3-2l1-1h1c0-1 1-1 2-2h1l2-2c1-1 2-1 3-2l2-2 4-3 4-3 1-1h0l-1-3c0-1 0-1-1-3l-1 1h0c0-1 0-2-1-3 0-1-1-1-1-1h0l1-1z" class="X"></path><path d="M441 235c-1 0-1-1-1-1h-1-1v-5c1-2 2-2 4-3l-1 9z" class="T"></path><path d="M442 226c-1 0 0-1 0-1l1 1h0 1v-1l-2-1c1-2 2-2 3-3-1 1-1 2-1 3l1 1c-1 2-1 4 0 7-2 1-3 1-4 3l1-9z" class="R"></path><path d="M458 208c0 2 1 4 0 7v-1c-1 2-2 3-3 4h-1l-1 1c-1 1-2 2-4 3l-1-3c2-1 4-3 6-4l3-3 1-4z" class="W"></path><path d="M448 219l1 3v2c-1 2-1 5-1 6s-1 2-1 4h-1l-1-1v-1c-1-3-1-5 0-7l-1-1c0-1 0-2 1-3l3-2z" class="N"></path><path d="M449 222c2-1 3-2 4-3l1-1h1c1-1 2-2 3-4v1c0 1 1 1 0 2-1 2-1 5-1 7 1 5 1 11 1 16v6c-3 1-7 2-10 4-7 3-14 7-21 8l-5 3-1-2c-1-1-1-4 0-5 0-1 0-1 1-1 1-1 3-1 4-2 3-2 2-9 2-12v-2c2-3 3-3 4-6h1-1c0 1 0 2 1 3v1h0c1 1 1 2 1 3h1c1-1 1-2 2-3 1 1 1 1 1 3h1c1-1 1-2 2-3 1-2 2-2 4-3v1l1 1h1c0-2 1-3 1-4s0-4 1-6v-2z" class="j"></path><path d="M449 222c2-1 3-2 4-3l1-1h1c1-1 2-2 3-4v1c0 1 1 1 0 2-1 2-1 5-1 7l-1 1v-7c-2 2-3 4-4 6-1 1-1 1-3 1v5h-1c0-1 0-4 1-6v-2z" class="t"></path><path d="M421 254c0-1 0-1 1-1 1-1 3-1 4-2 3-2 2-9 2-12v-2c2-3 3-3 4-6h1-1c0 1 0 2 1 3v1h0c1 1 1 2 1 3h1c1-1 1-2 2-3 1 1 1 1 1 3h1c1-1 1-2 2-3 1-2 2-2 4-3v1l-1 2v1 2h-2 0c-2 0-2 1-3 2 0 0-1 0-2 1 0 5-2 9-6 13-2 1-4 2-5 4h1 0l-5 3-1-2c-1-1-1-4 0-5z" class="V"></path><path d="M433 235c1 1 1 2 1 3h1v1c0 2 0 3-1 6h-1c-2-3-1-7 0-10z" class="c"></path><path d="M575 611c1-1 1 0 1-1h3c1 1 2 2 2 3l2 2h1c3 3 5 4 9 5 1 0 1 0 2 1l1-2v-2c4 3 8 3 12 4 2 0 8 0 9 1l-1 1c1 2 1 3 2 4v2l9 4c-2 2-7 4-7 5-1 1-2 1-2 3-1 1-1 2-2 2h-1-2l-1-1v-1h-1-3c0 2-1 4-1 6-4 2-6 4-10 5h-1v1c-2 0-6-1-8-2l-3-1c0 1 0 1 2 2l1 1h1c0 1 1 1 2 1 0 0 1 1 2 1h2v2l-2 2c0 1-1 1-2 2l-2 2 1 1h1v1l-1 4h0l-1 1s0 1-1 1c1 2 0 2 0 4 0 1 1 2 1 3l1 1c1 1 2 3 4 3 1 1 2 1 4 0h2l-2-1c3 0 5 0 8-1h2 0c1 0 1 1 2 1v-1c2 0 2-1 3-2l1 1-2 2c1 1 3 1 5 1v4l1 1h1v1 1h3v3c-1 1-1 0-2 0s-3 1-4 2v2h4-28-9c-3 0-6 0-9 1v-2l-1-10v-4-28-7-23c1-2 1-3 1-4s0-1 1-2v-6h0z" class="M"></path><path d="M579 675c0-2 1-5 0-6v-1h0c1 0 1 1 2 2l1 1c-1 1-2 2-3 4zm10-12l1 1h1v1l-1 4h0c-1 0-2 0-3-1 1-2 1-3 2-5z" class="a"></path><path d="M576 645c1 0 1 1 2 0 1 1 2 1 2 2v1c0 1 1 1 1 2h0l-2 1v-1-1l-1-1c-2 3-2 4-2 7-1-3 0-6-1-9l1-1z" class="S"></path><path d="M573 685l1-2c1-4 0-8 1-12l1 13v1c-2 2-1 4-1 6v1c0 1 0 2-1 3l-1-10z" class="Y"></path><path d="M582 658c2-1 5 0 7 0l1 1h0 0-1c-1 0-2 1-3 1l1 2c-1 1-2 2-4 3 0 0-1 0-1-1-2 0-2-2-2-3 0-2 1-2 2-3z" class="r"></path><path d="M582 671c1-1 2-1 3-1 2 2 2 2 2 4h-5v4h1l-1 1h-1l1 1-1 3 2 2c-1 0-2 1-4 0v-1l-1-1h2v-1l1-1c-1-1-1-1-1-2l-1-1v-3c1-2 2-3 3-4z" class="C"></path><path d="M584 641c-1 2-1 3 0 5 1-2 0-3 1-5 0 1 0 2 1 4h0c-1 1-1 2-1 3-1-1-1-1-2 0h-1c1 1 2 2 3 2h0c0 1 0 1 2 2l1 1h1c0 1 1 1 2 1 0 0 1 1 2 1-1 0-3 1-5 0-1-1 0-1-1-1h-2l-1-1c-1 1-1 1-2 1l-3-3 2-1h0c0-1-1-1-1-2v-1c0-1-1-1-2-2h2l4-4z" class="B"></path><path d="M582 680l-1-1h1l1-1h-1v-4h5l1 1c0 1 1 2 1 3l1 1c1 1 2 3 4 3 1 1 2 1 4 0l3 1c-2 1-6 1-8 1l1 2h-4-3l-2-1h-2l-2-2 1-3z" class="Q"></path><path d="M584 682l1-1 2 2v3l-2-1-1-3z" class="K"></path><path d="M582 680l1 3 1-1 1 3h-2l-2-2 1-3z" class="B"></path><path d="M587 674l1 1c0 1 1 2 1 3l1 1c1 1 2 3 4 3 1 1 2 1 4 0l3 1c-2 1-6 1-8 1l1 2h-4-3v-3c1 0 2 0 3-1l-2-1-1-1h1l1-1-4-2v-1l2-1v-1z" class="D"></path><path d="M590 686l2-2h1l1 2h-4z" class="B"></path><path d="M608 680h0c1 0 1 1 2 1v-1c2 0 2-1 3-2l1 1-2 2c1 1 3 1 5 1v4l1 1c-3 0-7 1-10 0l-14-1-1-2c2 0 6 0 8-1l-3-1h2l-2-1c3 0 5 0 8-1h2z" class="K"></path><path d="M574 695c1-1 1-2 1-3v-1c0-2-1-4 1-6v2h2c4 0 8 1 13 1v3l2 1s0 1-1 1v1 2h-9c-3 0-6 0-9 1v-2z" class="Q"></path><path d="M574 695c1-1 1-2 1-3v-1c0-2-1-4 1-6v2h2l2 2 1 1c0-1 1-1 2-2l1 1c-1 2-3 3-3 4l1 1 1 2c-3 0-6 0-9 1v-2z" class="J"></path><path d="M575 611h0l1 12c0 2-1 4 1 6-2 3-1 4-1 6-1 4-1 7-1 11 1 3 0 6 1 9l-1 16c-1 4 0 8-1 12l-1 2v-4-28-7-23c1-2 1-3 1-4s0-1 1-2v-6z" class="Z"></path><path d="M579 629c0-1 1-2 1-3 3-1 6 0 9 0v3l-1 1-1 1-2 4c-1 2-1 4-1 6l-4 4h-2c-1 1-1 0-2 0l-1 1c0-4 0-7 1-11 0-2-1-3 1-6v3h1v-1c0-1 0-1 1-2z" class="M"></path><path d="M582 633l1-2h4l-2 4c-1-1-2-2-3-2z" class="S"></path><path d="M579 629c0-1 1-2 1-3 3-1 6 0 9 0v3l-1 1-1-1v1l-3-3c-2 1-2 3-3 5-1 0-2 0-2-1v-2z" class="C"></path><path d="M582 633c1 0 2 1 3 2-1 2-1 4-1 6l-4 4h-2c-1 1-1 0-2 0l-1 1c0-4 0-7 1-11v5c1 1 2 1 3 1h1 1c0-1 0-2 1-3l2-2c-1-1-1-2-2-2v-1z" class="e"></path><path d="M576 645c0-1 1-2 3-3 1 1 1 1 1 3h-2c-1 1-1 0-2 0z" class="M"></path><path d="M591 688h2l13 1c2 0 4-1 6 0h7 3v3c-1 1-1 0-2 0s-3 1-4 2v2h4-28v-2-1c1 0 1-1 1-1l-2-1v-3z" class="J"></path><path d="M596 692h1c0 1 0 2-1 3h0l-1-1c0-1 0-2 1-2zm5 0l1 1c1 0 2 0 3 1-1 0-1 0-2 1-2-1-2-1-3-2l1-1z" class="Y"></path><path d="M593 688l13 1c2 0 4-1 6 0h-5l-1 3c-1 0-1-1-1-1l-1-2c-1 1-2 1-2 2v2l-1-1h-2c1-1 1-1 0-2h0l-2 2h0-1v-1h-3v-3z" class="Q"></path><path d="M597 627c5 2 8 5 10 9 1 2 1 4 1 5 0 2-1 4-1 6-4 2-6 4-10 5h-1v1c-2 0-6-1-8-2l-3-1h0c-1 0-2-1-3-2h1c1-1 1-1 2 0 0-1 0-2 1-3h0c-1-2-1-3-1-4-1 2 0 3-1 5-1-2-1-3 0-5 0-2 0-4 1-6l2-4 1-1 1-1c3-1 5-2 8-2z" class="l"></path><path d="M598 634c1 1 3 2 4 4 0 1 0 3-1 4-1 2-3 2-5 3-1-1-3-2-4-4l1-1h1l-1-1c1-2 1-2 3-2 4 1 2 2 4 5l1-1c0-1 0-2-1-3v-1c-1-1-1-2-2-3z" class="S"></path><path d="M585 641c1-4 3-8 7-11l4-1c1 0 3 0 5 1 3 2 5 6 6 10v2c0 1-1 2-1 3h-1c0-2 0-3 1-4l-1-3c0-1 0-1-1-1-1-1-1-2-1-3-2-1-3-1-4-2s-4-1-6 0c-3 1-5 4-5 7-1 3-1 5 0 7 2 2 4 4 7 5l1 1v1c-2 0-6-1-8-2l-3-1h0c-1 0-2-1-3-2h1c1-1 1-1 2 0 0-1 0-2 1-3h0c-1-2-1-3-1-4z" class="C"></path><path d="M575 611c1-1 1 0 1-1h3c1 1 2 2 2 3l2 2h1c3 3 5 4 9 5 1 0 1 0 2 1l1-2v-2c4 3 8 3 12 4 2 0 8 0 9 1l-1 1c1 2 1 3 2 4v2l9 4c-2 2-7 4-7 5-1 1-2 1-2 3-1 1-1 2-2 2h-1-2l-1-1v-1h-1-3c0-1 0-3-1-5-2-4-5-7-10-9-3 0-5 1-8 2v-3c-3 0-6-1-9 0 0 1-1 2-1 3-1 1-1 1-1 2v1h-1v-3c-2-2-1-4-1-6l-1-12z" class="u"></path><path d="M591 622c-2 0-3 0-5-1-1-2-3-4-6-6l-1-1 2-1 2 2c2 3 5 5 8 7z" class="O"></path><path d="M584 615c3 3 5 4 9 5 1 0 1 0 2 1l-1 1h-3c-3-2-6-4-8-7h1z" class="J"></path><path d="M609 632c2 2 4 5 6 7v1c-1 1-1 1 0 3h-2l-1-1v-1h-1c-2-2 0-4-1-6 0-1-1-2-2-2l1-1z" class="F"></path><path d="M597 627l2-1c3 2 7 3 10 6l-1 1c1 0 2 1 2 2 1 2-1 4 1 6h-3c0-1 0-3-1-5-2-4-5-7-10-9z" class="a"></path><path d="M596 617c4 3 8 3 12 4 2 0 8 0 9 1l-1 1c-1 1-1 2-2 2h-3-1l-6-1c0-1 0-1-1-2l-1 1c-2 0-3 0-4-1l-2-3v-2z" class="J"></path><path d="M217 110l74 1c1 1-1 3 1 5 0-2 1-4 1-6h63v22c-26 0-52 1-78-1-14-1-28-5-41-7-20-4-40-7-59-12 2-2 10-1 13-1 7 1 13 0 19 0l7-1z" class="n"></path><path d="M191 111c7 1 13 0 19 0l-1 1 1 1h-1c-1 0-1 0-2 1l-1-1h-1l-1 1c-1 0-1 0-1-1-1-1-6 0-8-1-2 0-3 0-4-1z" class="W"></path><path d="M332 124h15c1 1 3 1 5 1-9 0-20-1-28 0l-9 2h-6-1c-3 1-7 0-9 0h-3s-1 0-1-1h3 12l11-1c3-1 7 0 10-1h1z" class="g"></path><path d="M204 114l1-1h1l1 1h3c4 1 8 0 13 0h16 4c1-1 2-1 2-1l2 3h-11c-11 0-22 0-32-2z" class="R"></path><path d="M245 113l22 2c5 0 10 0 14 1 2 0 7-1 7 0 1 0 1 1 1 1v2l-1-1-1-1-1 1h-2l-1 2v1h1 0c1-1 1-2 3-2h0c-1 1-2 2-2 3-2 0-2 0-4-1v-1-3h1c-2-1-5-1-6-1h-16c-5 0-9 1-13 0l-2-3z" class="V"></path><path d="M331 116c7-1 15 0 22 1v1h-7c-9 2-19 1-28 1h-16-7c-1 0 0 0-1-1l37-2z" class="N"></path><defs><linearGradient id="N" x1="704.867" y1="119.303" x2="705.899" y2="132.637" xlink:href="#B"><stop offset="0" stop-color="#b7b6b5"></stop><stop offset="1" stop-color="#dfdede"></stop></linearGradient></defs><path fill="url(#N)" d="M669 109l172 1c2-1 4-1 6 0-5 2-10 3-14 4l-28 5-43 9c-9 2-17 3-25 3-1-1-1-1-1-2h0v2h-1c-1 1-3 1-4 1h-10-45v-2c-1-1 0-1-1-1v-3c-1-2-1-4-1-6l1-7v-2c-2-2-8-1-11-2h5z"></path><path d="M721 110h15v6 2l1 1h1v2l-1 1c0-1-1-1-1-1v-2h-1-3c-1 0-1 1-2 1s-3-1-4 0c-3 2-34 3-38 2-2 0-3 0-4-1h1c-3-1-7 0-9 0v-11h45z" class="U"></path><path d="M721 110h15v6c-3-2-9-1-12-2 1 0 3-1 5 0h0c1-1 0-2 1-3h0c-2 0-5 0-8-1h-1z" class="j"></path><defs><linearGradient id="O" x1="709.094" y1="123.73" x2="709.99" y2="115.497" xlink:href="#B"><stop offset="0" stop-color="#8d8c8b"></stop><stop offset="1" stop-color="#bebcbb"></stop></linearGradient></defs><path fill="url(#O)" d="M685 121l9-2c2 0 3 0 5-1l1-1h0 3c2-1 6-1 8-1 1-1 2 0 3 0l-2-1c-6-1-12 0-18-1 4 0 23 0 26 2 4 3 11 0 15 2l1 1v-1l1 1h1v2l-1 1c0-1-1-1-1-1v-2h-1-3c-1 0-1 1-2 1s-3-1-4 0c-3 2-34 3-38 2-2 0-3 0-4-1h1z"></path><path d="M841 110c2-1 4-1 6 0-5 2-10 3-14 4l-28 5-43 9c-9 2-17 3-25 3-1-1-1-1-1-2v-6c1-1 1 0 3-1 0-2 0-4-1-5-1-2 0-5 0-8 26 2 53 0 79 1l19 1h4c0-1 1-1 1-1z" class="j"></path><path d="M456 428l1 1c0 3-2 17-1 19 1 4 1 8 1 12v35l-2 1h-1c0 1-1 2-1 3v1c0 3 0 4-2 7v1l-1 5-2 1h-1c-1-1 0-1-1-1v1c-1 1-1 2-1 2h-2c-2-1-2-1-3-2l1-1-1-2-1-1h-2 0c-1-1-2-1-3-2l-2-1-1-1c-2 0-3-1-4-2 0-1-2-1-2-2h-3l-2-1h0v-1l-1-3c-1-2-1-3-1-5 1 0 1 1 1 2 1-1 2-3 3-4v-1c0-2 1-3 1-4 1-1 1 0 1-2l1-5v-1l-1-1c-1-2-1-2-3-3 0-2-1-3-2-4l-1-1c-1-2-2-3-4-4-1-1-1 0-2 0l-3-3h-2c-1 0-1 0-2-1 0-3 1-6 0-8 0-2 0-4 1-5l1-7-1-1 1-3c1-2 1-2 3-3h11c1-1 3 0 5 0h0l17-2 7-1c0-1 5-1 6-2z" class="V"></path><path d="M432 445v-1c1-1 1-2 3-2 0 1 0 1 1 1l1-1c2 1 3 0 4 1l1 2h-2c-1-1-2-1-3 0l-2 1-2-2-1 1z" class="T"></path><path d="M432 445l1-1 2 2c2 1 3 2 5 3h0c-1 1-2 1-4 1-1 0-1-1-2-1h-1c0-2 0-3-1-4z" class="w"></path><path d="M416 441c4-1 8 1 12 0h7c1 0 1 0 1 1h1l-1 1c-1 0-1 0-1-1-2 0-2 1-3 2v1c1 1 1 2 1 4h1c1 0 1 1 2 1-3 1-6 2-10 2l1-1v-4-1h-1c0-1-1-1-1-2h0c-2-1-3-1-4-1s-1-1-2-2l-1 1s-1-1-2-1z" class="R"></path><path d="M428 447c0-1 1-1 1-2l2 1v1l-2 1h-1v-1z" class="g"></path><path d="M427 446v-2c1-1 2-1 4-1v3l-2-1c0 1-1 1-1 2h-1v-1z" class="N"></path><path d="M456 428l1 1c0 3-2 17-1 19 1 4 1 8 1 12h-1c-1-1-2-1-2-1h-1-1v3h-1v-2h0v-1c1-2 2-4 1-6h-2l-5 1h-4v-1c1-2 1-2 3-4l1 2h1c1-2 2-2 4-3h1c1-2 3-5 3-6 0-2-1-3-1-5h0c1-1 1-1 2-1v-1c-1 0-1 0-2-1v-1c0-1 0-2 1-2h0v2h1l1 1v-4c-2 0-4 0-6 1v-1c0-1 5-1 6-2z" class="i"></path><path d="M416 441c1 0 2 1 2 1l1-1c1 1 1 2 2 2s2 0 4 1h0c0 1 1 1 1 2h1v1 4l-1 1-6 1c-2 0-4 1-7 1l-1-2c-2-2-1-6-2-8v-1c2-2 4-2 6-2z" class="L"></path><path d="M416 441c1 0 2 1 2 1v2h-1-5v2 3 3c-2-2-1-6-2-8v-1c2-2 4-2 6-2z" class="P"></path><path d="M412 446h1v3 2h1l1-1c1 0 1 1 2 1s2-1 3-2l1-1h1l1 2c1-1 2-1 3-1v-2-1h1v1 4l-1 1-6 1c-2 0-4 1-7 1l-1-2v-3-3z" class="h"></path><path d="M426 433l17-2 1 11-1 1h-2c-1-1-2 0-4-1h-1c0-1 0-1-1-1h-7c-4 1-8-1-12 0-2 0-4 0-6 2v1 7c1 2 0 4-1 6l-2 1 1 2-1 1c-1 0-1 0-2-1 0-3 1-6 0-8 0-2 0-4 1-5l1-7-1-1 1-3c1-2 1-2 3-3h11c1-1 3 0 5 0h0z" class="n"></path><path d="M407 440l1 1v1c-1 3-1 7-1 11 0 1 0 2-1 3v-9l1-7z" class="g"></path><path d="M410 433h11c1-1 3 0 5 0h0l-1 1c0 1 0 0-1 1v1h-4l-3 1c-2 1-5 0-7 1h-1c-1 0-2 0-3 1l1-3c1-2 1-2 3-3z" class="V"></path><defs><linearGradient id="P" x1="422.07" y1="453.161" x2="433.991" y2="472.585" xlink:href="#B"><stop offset="0" stop-color="#121110"></stop><stop offset="1" stop-color="#343430"></stop></linearGradient></defs><path fill="url(#P)" d="M445 454l5-1c0 2 0 3-1 5v1l-1 2c0 1 0 1 1 2h-2v1c-1 1-2 1-3 1-2 0-3 1-4 2l-1 1c-1 1-1 2-2 3v-3c-1 1-1 2-2 2-1 1-2 3-2 4l-2-4-1 1v1h-3-1c-1 2-1 3-2 4-1-2-1-2-3-3 0-2-1-3-2-4l-1-1c-1-2-2-3-4-4-1-1-1 0-2 0l-3-3h-2l1-1-1-2h3c2-1 5-1 8-1 7-2 15-3 23-3h4z"></path><path d="M426 464l-1 1-2-2v-1l2-1v1 1c0 1 0 1 1 1z" class="K"></path><path d="M425 461h4v1c1 1 2 0 3 1l-1 1v1l-1 1-1-2h-1c-1 1 0 1-1 1l-1-1c-1 0-1 0-1-1v-1-1z" class="C"></path><defs><linearGradient id="Q" x1="426.735" y1="452.413" x2="439.959" y2="459.646" xlink:href="#B"><stop offset="0" stop-color="#1e1c1e"></stop><stop offset="1" stop-color="#3d3d38"></stop></linearGradient></defs><path fill="url(#Q)" d="M445 454h4l-2 1c-1 1-2 2-3 2-2 1-5 4-6 6v1l-1-2c0-1 1-2 1-4-3-2-9 0-13 0l-1-1c-1 0-2-1-3 0h-3c7-2 15-3 23-3h4z"></path><path d="M445 454l5-1c0 2 0 3-1 5v1l-1 2c0 1 0 1 1 2h-2v1c-1 1-2 1-3 1-2 0-3 1-4 2l-1 1c-1 1-1 2-2 3v-3l1-4v-1c1-2 4-5 6-6 1 0 2-1 3-2l2-1h-4z" class="b"></path><path d="M438 464v-1c1-2 4-5 6-6 1 0 2-1 3-2v1c-1 1-1 2-2 3v1h-1c-2 0-2 1-3 2h0c0 2-1 3-2 4v2c-1 1-1 2-2 3v-3l1-4z" class="d"></path><defs><linearGradient id="R" x1="456.716" y1="496.974" x2="433.831" y2="480.96" xlink:href="#B"><stop offset="0" stop-color="#abaaa9"></stop><stop offset="1" stop-color="#d3d3d2"></stop></linearGradient></defs><path fill="url(#R)" d="M450 453h2c1 2 0 4-1 6v1h0v2h1v-3h1 1s1 0 2 1h1v35l-2 1h-1c0 1-1 2-1 3v1c0 3 0 4-2 7v1l-1 5-2 1h-1c-1-1 0-1-1-1v1c-1 1-1 2-1 2h-2c-2-1-2-1-3-2l1-1-1-2-1-1h-2 0c-1-1-2-1-3-2l-2-1-1-1c-2 0-3-1-4-2 0-1-2-1-2-2h-3l-2-1h0v-1l-1-3c-1-2-1-3-1-5 1 0 1 1 1 2 1-1 2-3 3-4v-1c0-2 1-3 1-4 1-1 1 0 1-2l1-5v-1l-1-1c1-1 1-2 2-4h1 3v-1l1-1 2 4c0-1 1-3 2-4 1 0 1-1 2-2v3c1-1 1-2 2-3l1-1c1-1 2-2 4-2 1 0 2 0 3-1v-1h2c-1-1-1-1-1-2l1-2v-1c1-2 1-3 1-5z"></path><path d="M454 492v1 2h0v1c0 1-1 2-1 3v1l-1 4-2 2v1-1-1c1-2 0-6 2-9h0l2-4z" class="T"></path><path d="M450 453h2c1 2 0 4-1 6v1h0v2 3l-2 1v1c-1 2-1 3-2 5 0-1 0-1 1-2v-2h0l-2 1c-1-1-1-1-2-1l1-1 1 1 1-1v-3-1h2c-1-1-1-1-1-2l1-2v-1c1-2 1-3 1-5z" class="E"></path><path d="M450 507v-1l2-2 1-4c0 3 0 4-2 7v1l-1 5-2 1h-1c-1-1 0-1-1-1v1c-1 1-1 2-1 2h-2c-2-1-2-1-3-2l1-1-1-2c1 1 2 0 3 0v-1h1v2c0 1-1 1 0 2h1v-1-1h0v-4c1-1 2 0 3-1l1 1 1-1z" class="p"></path><defs><linearGradient id="S" x1="457.674" y1="478.035" x2="449.263" y2="476.957" xlink:href="#B"><stop offset="0" stop-color="#a8a7a4"></stop><stop offset="1" stop-color="#d9d8d8"></stop></linearGradient></defs><path fill="url(#S)" d="M452 462v-3h1 1s1 0 2 1h1v35l-2 1h-1v-1h0v-2-1-3c-1 0-1 1-1 2h0c0-1 0-2-1-3v2c-1 1-1 2-2 3h-1c0-4-1-10 1-13v-4-2c0-2 1-4 2-7v-5z"></path><path d="M447 464v3l-1 1-1-1-1 1h0l-2 2v1h1c1 0 1 1 1 2v1-2h-1c-1 1-2 2-3 4l1 2h0c-1 1-1 2-1 3v1c-1 1-1 3-1 5l-1 1c1 1 0 1 0 2s1 1 1 3v4c1 0 1 0 2 1 0 1 0 2-1 3 1 1 1 1 1 2l1 1c-1 1-1 1-1 2v1h2v1h-1 0c-1 1-2 1-3 1 0 0 1 0 1 1l1 1h2c-1 0-2 1-3 0l-1-1h-2 0c-1-1-2-1-3-2l-2-1-1-1c-2 0-3-1-4-2 0-1-2-1-2-2h-3l-2-1h0v-1l-1-3c-1-2-1-3-1-5 1 0 1 1 1 2 1-1 2-3 3-4v-1c0-2 1-3 1-4 1-1 1 0 1-2l1-5v-1l-1-1c1-1 1-2 2-4h1 3v-1l1-1 2 4c0-1 1-3 2-4 1 0 1-1 2-2v3c1-1 1-2 2-3l1-1c1-1 2-2 4-2 1 0 2 0 3-1z" class="c"></path><path d="M436 477l1-1 1-2c0-2 1-3 2-4l1 1-1 1s-1 0-1 1 0 1 1 2c-1 1-3 3-3 5v3c-1 1-2 2-1 4v2l-1 1c-1 0-1-1-2-2v-1h2v-2h0v-3c1-2 1-2 1-3v-1-1z" class="E"></path><path d="M447 464v3l-1 1-1-1-1 1h0c-2 0-2 1-4 2-1 1-2 2-2 4l-1 2-1 1 1-6c1-1 1-2 2-3l1-1c1-1 2-2 4-2 1 0 2 0 3-1zm-20 40h3c2 1 0 2 3 2v1c1 1 1 1 2 1v-3c2 0 1 0 3 1v1c1 1 2 1 3 1v-1h2v1h-1 0c-1 1-2 1-3 1 0 0 1 0 1 1l1 1h2c-1 0-2 1-3 0l-1-1h-2 0c-1-1-2-1-3-2l-2-1-1-1c-2 0-3-1-4-2zm-3-17c1-1 2-1 3-2s1-1 1-2c1 1 1 1 1 2h0 1c1 0 1-3 2-4 0 1 1 1 2 1l2-4v1c0 1 0 1-1 3v3h0v2h-2v1c1 1 1 2 2 2v2l-3-1c0-1-1-2-1-3l-1 1c-1 1-1 1-1 2-1-1-1-2-1-3l-1-1c-1 1-2 2-3 2s-2 1-2 1v-1l2-2z" class="T"></path><path d="M427 492h1 1 2c1 2 0 2 0 3l2 1h1c-1 1-2 1-3 1l-2-2c-1 0-1 1-2 2 1 2 6 2 6 4-1 1-2 1-3 0l-2-1h-1l-1 1-1-1v2h-3l-2-1h0v-1c1-1 1-1 1-3h3l1-1-3-2 1-1 3-1h1z" class="U"></path><path d="M420 501c1-1 2-2 4-2l1 1v2h-3l-2-1h0z" class="T"></path><path d="M425 496l-3-2 1-1 3-1h1c0 1-1 2-2 3 0 2 2 2 1 3-1-1-1-1-1-2z" class="t"></path><path d="M427 472h3v-1l1-1 2 4c0-1 1-3 2-4 1 0 1-1 2-2v3l-1 6v1l-2 4c-1 0-2 0-2-1-1 1-1 4-2 4h-1 0c0-1 0-1-1-2 0 1 0 1-1 2s-2 1-3 2l-2 2c0-2 1-3 1-4 1-1 1 0 1-2l1-5v-1l-1-1c1-1 1-2 2-4h1z" class="D"></path><path d="M424 476c1-1 1-2 2-4h1c0 2 0 4-2 6v-1l-1-1z" class="F"></path><path d="M437 471l-1 6v1l-2 4c-1 0-2 0-2-1-1 1-1 4-2 4h-1 0c0-1 0-1-1-2 0 1 0 1-1 2s-2 1-3 2c1-2 1-4 3-5l1-2 3-3h3c2-2 2-3 3-5v-1z" class="q"></path><path d="M437 685l1-2c1 1 2 1 3 2v1c-1 2-1 3-1 5l-2 1h0c1 2 1 4 2 6 0 1 1 2 1 3h1c1 0 1 0 2-1l2 2h1 1v2h0l-2 1c0 1 0 1 1 2 0 1-1 2 0 4v4l-2 4 2 1-1 1-1 1-3 3v3c1-2 2-3 4-4 4 1 5 1 8 4 0 1 1 3 2 4h0c-1 2 0 7-1 8-1 2-3 4-4 4h-2l2 2 1 3h3v-1c-1-1-1-1-2-1l1-1h1l1-4v15 6 1h2l1 1h1c0 1-1 1-1 2-1 1 0 6 0 8h-37c-2-1-5-1-7 0v3h0c0-2 0-3-1-6 0-1-1-1-2-2s-2-3-4-4c-1-1-2-1-3-2h0c-1-2-1-2 0-4 0-1 0-3 1-4v-2c0-1 0-2 1-3l1-1c1 1 2 1 3 1s1 0 2 1h1v-2c-1-3 1-6-1-9l-6-15c-1-1-2-2-2-3 1-1 2-1 1-2 0-2-2-4-2-6 1 1 2 3 3 4 1 2 1 2 0 4 0 3 3 6 4 9 0 1 1 2 1 2 1 1 1 1 1 2s1 2 1 4c1-1 0-3 0-4l-4-9c0-1-1-1-1-2-1 0-1-1-1-2h1c0 1 0 1 1 2 1-3 0-4 1-6 0-3 0-4 1-7h0c0-2 0-3 1-5v-4c1 0 1-1 1-2v-2-5l2 4h2v-5l1-1h1c1-2 2-2 3-3h3c3-1 5-2 7-4l1-2c2 0 2 1 3 2z" class="D"></path><path d="M420 722v-9c1 1 1 1 2 1h1c1 2 1 4 1 6h-1c-1 1-1 1-1 2h-2zm-7-15c1 5 1 9 2 13l-1 1v3 1c-2-1-2-1-3-3 1-1 1-1 1-2v-4c1-1 0-2 0-4h0c0-2 0-3 1-5z" class="N"></path><path d="M414 699v-5l2 4h2v4c0 3 0 7-1 10h-1c-1-1-1-2-2-4l1-3-2-2c1 0 1-1 1-2v-2z" class="g"></path><path d="M414 699v-5l2 4c0 2 0 3-1 4h0l-1-1v-2z" class="s"></path><path d="M420 722h2c0-1 0-1 1-2l1 3c1 0 1 0 1 1l2 2h1c0 1 0 2-1 2h-1l-2 2c1 2 1 4 2 6-1 1 0 1-1 1v2h-1l-1 1s-2-1-2-2c-1-1 0-5 0-7l-1 3h-1l1-3v-9z" class="E"></path><path d="M424 723c1 0 1 0 1 1l2 2h1c0 1 0 2-1 2h-1l-2 2c0-1-1-3 0-4h0l-1-1 1-2z" class="C"></path><path d="M421 731v-2h0c1 3-1 5 3 8 0-1 1-1 2-1-1 1 0 1-1 1v2h-1l-1 1s-2-1-2-2c-1-1 0-5 0-7z" class="B"></path><path d="M422 712c1-1 2-1 2-1 2 1 2 0 4 0l1 1 1 2v3c1 0 2-1 3-1v1s-1 1-2 1v4c0 3-1 6-2 8-1 0-1 0-1-1l-2-1h1c1 0 1-1 1-2h-1l-2-2c0-1 0-1-1-1l-1-3h1c0-2 0-4-1-6 0 0 1-1 2-1-1-1-1-1-3-1z" class="b"></path><path d="M429 712l1 2v3c1 0 2-1 3-1v1s-1 1-2 1v4c0 3-1 6-2 8-1 0-1 0-1-1l-2-1h1c1 0 1-1 1-2h-1v-3l1-1v-1-5-3l1-1z" class="B"></path><path d="M427 696v6c0 2 0 4 1 5v2 2c-2 0-2 1-4 0 0 0-1 0-2 1l-1-1v-1c-1-1-1-1-1-2h-1v-4l1-1-1-1c0-1 0-1 1-2s0-2 0-3h2c0-1 0 0 1-1v2h1c1-1 2-2 3-2z" class="R"></path><path d="M421 710c0-1 0-2 1-3l1 1 1 1-2 2-1-1z" class="W"></path><path d="M424 709v-2l2 1v-1-3c1-1 0-1 0-2h1c0 2 0 4 1 5v2 2c-2 0-2 1-4 0 0 0-1 0-2 1l-1-1v-1h0l1 1 2-2z" class="c"></path><path d="M437 685l1-2c1 1 2 1 3 2v1c-1 2-1 3-1 5l-2 1-2 2-2-1c-1 1-2 1-3 1s-2 0-3 1l-1 1c-1 0-2 1-3 2h-1v-2c-1 1-1 0-1 1h-2c0 1 1 2 0 3s-1 1-1 2h-1v-4-5l1-1h1c1-2 2-2 3-3h3c3-1 5-2 7-4l1-2c2 0 2 1 3 2z" class="E"></path><path d="M434 683c2 0 2 1 3 2-1 2-2 3-3 5 0-2 0-3-1-5l1-2z" class="K"></path><path d="M428 695l1-2h2c2-1 3-1 4-3h0 3v2l-2 2-2-1c-1 1-2 1-3 1s-2 0-3 1z" class="i"></path><path d="M433 685c1 2 1 3 1 5l-4 2c-1-1-1-2-1-3h-3c3-1 5-2 7-4z" class="C"></path><path d="M423 689h3 3c0 1 0 2 1 3l-2 1h-1-1c-2 0-4 0-6-1 1-2 2-2 3-3z" class="G"></path><path d="M419 692h1c2 1 4 1 6 1h1 1l-4 1v4h-1v-2c-1 1-1 0-1 1h-2c0 1 1 2 0 3s-1 1-1 2h-1v-4-5l1-1z" class="l"></path><path d="M414 750c2 0 3 0 5-1v-7l-1-1h-1l-1 1c1 2 1 4 1 6l-1 1h0c-1-1-1-2 0-3 0-2-1-4 0-5v-4h1v3l2 1c1 2 1 7 0 9h2 3 6 13l9-1h3v-1c-1-1-1-1-2-1l1-1h1l1-4v15l-2-2v-1c-8 0-16 1-24 0h-1v1h-6v1 1c-4 0-11 1-14-2-1 0-2 0-3-1 0-1 0-2 1-3l1-1c1 1 2 1 3 1s1 0 2 1h1v-2z" class="i"></path><path d="M455 749v-1c-1-1-1-1-2-1l1-1h1l1-4v15l-2-2v-1c-8 0-16 1-24 0h-1-3c-4-1-9-1-13-1v-1h1c3 1 5 0 7 1 8 1 17 0 25 0h8l1-4z" class="b"></path><path d="M406 754c0-1 0-2 1-3l1-1c1 1 2 1 3 1s1 0 2 1v1c4 0 9 0 13 1h3v1h-6v1 1c-4 0-11 1-14-2-1 0-2 0-3-1z" class="R"></path><path d="M436 694l2-2h0c1 2 1 4 2 6 0 1 1 2 1 3h1c1 0 1 0 2-1l2 2h1 1v2h0l-2 1c0 1 0 1 1 2 0 1-1 2 0 4v4h-2c-1-1-1-1-2 0-2-1-2-1-4-1-1 0-2 0-3 1 1 2 1 2 1 4h-1c-1-1-1-1-1-2v-2h0-1l-1 1c-1 0-2 1-3 1v-3l-1-2-1-1v-2-2c-1-1-1-3-1-5v-6l1-1c1-1 2-1 3-1s2 0 3-1l2 1z" class="X"></path><path d="M440 712c-1 0-1 0-2-1v-1c0-3 2-5 2-8l-1-1c-1-2-2-6-1-9 1 2 1 4 2 6 0 1 1 2 1 3v3l2 2v4h-1v-1-1h-1c0 1-1 2-1 4z" class="f"></path><path d="M441 701h1c1 0 1 0 2-1l2 2h1 1v2h0l-2 1c0 1 0 1 1 2 0 1-1 2 0 4v4h-2c-1-1-1-1-2 0-1-2-2-3-3-3 0-2 1-3 1-4h1v1 1h1v-4l-2-2v-3z" class="b"></path><path d="M435 695v1h0c1 2 3 5 3 6s-1 3-1 3c-1 2-2 4-2 6 0 1 0 2-1 3h-1v-1c-2 0-2 0-3 1l-1-2-1-1v-2-2l2 1 1-3c0-2 2-2 4-3l-1-3c1-1 1-2 1-4z" class="W"></path><path d="M435 702v1c-1 2-2 3-4 5h-1l1-3c0-2 2-2 4-3z" class="j"></path><path d="M428 707l2 1h1c1 1 2 2 2 4v1c-2 0-2 0-3 1l-1-2-1-1v-2-2z" class="N"></path><path d="M428 695c1-1 2-1 3-1s2 0 3-1l2 1-1 2v-1c0 2 0 3-1 4l1 3c-2 1-4 1-4 3l-1 3-2-1c-1-1-1-3-1-5v-6l1-1z" class="t"></path><path d="M434 699l-2-2 1-2h1 1c0 2 0 3-1 4z" class="n"></path><path d="M406 754c1 1 2 1 3 1 3 3 10 2 14 2v-1-1h6v-1h1c8 1 16 0 24 0v1l2 2v6 1h-2-37c-3 0-7-1-10 0h-2 0c-1-2-1-2 0-4 0-1 0-3 1-4v-2z" class="j"></path><path d="M406 754c1 1 2 1 3 1l-1 1c1 1 2 1 3 1v2c-2 1-2 0-4 0v-3h-1v-2z" class="n"></path><defs><linearGradient id="T" x1="437.657" y1="760.318" x2="446.843" y2="749.682" xlink:href="#B"><stop offset="0" stop-color="#c4c0c3"></stop><stop offset="1" stop-color="#d8dad6"></stop></linearGradient></defs><path fill="url(#T)" d="M429 754h1c8 1 16 0 24 0v1l2 2v6 1h-2v-1c1-2 1-5 0-7-1 1-1 1-2 1h0l-2 3h-1l-1-1v-1h-10c0 1 0 2-1 3-1 0-2 0-2-1-1 0-1-2-1-3h-7-4v-1-1h6v-1z"></path><path d="M417 764h37 2 2l1 1h1c0 1-1 1-1 2-1 1 0 6 0 8h-37c-2-1-5-1-7 0v3h0c0-2 0-3-1-6 0-1-1-1-2-2s-2-3-4-4c-1-1-2-1-3-2h2c3-1 7 0 10 0z" class="C"></path><path d="M407 764c3-1 7 0 10 0h6 2l1 1h2c0 1 1 1 1 2-6 1-11-1-17 0h0c-1-1-2-1-2-1 2-2 4 0 5-2h-8z" class="Y"></path><path d="M412 770l1-1h0c1-1 3-1 4-1l13 1c1 1 2 1 3 1l1 1h3 0l1 1h-6c-1 1-8 1-9 0h-9c0-1-1-1-2-2z" class="Q"></path><path d="M430 769c5-1 10-1 16 0 2 0 5-1 6 0 2 0 4 2 5 3 1 0 2 0 2 1-6 0-12 0-18-1h-3l-1-1h0-3l-1-1c-1 0-2 0-3-1z" class="D"></path><defs><linearGradient id="U" x1="450.924" y1="758.626" x2="436.304" y2="772.612" xlink:href="#B"><stop offset="0" stop-color="#363334"></stop><stop offset="1" stop-color="#50514a"></stop></linearGradient></defs><path fill="url(#U)" d="M417 764h37 2 2l1 1h0c-1 1-1 1-1 2h-21-8c0-1-1-1-1-2h-2l-1-1h-2-6z"></path><path d="M439 714c2 0 2 0 4 1 1-1 1-1 2 0h2l-2 4 2 1-1 1-1 1-3 3v3c1-2 2-3 4-4 4 1 5 1 8 4 0 1 1 3 2 4h0c-1 2 0 7-1 8-1 2-3 4-4 4h-2l2 2 1 3-9 1h-13-6l1-1c-1-2-1-1-3-1v-2c0-2 0-4 1-6l1-1h1v-2c1 0 0 0 1-1-1-2-1-4-2-6l2-2 2 1c0 1 0 1 1 1 1-2 2-5 2-8v-4c1 0 2-1 2-1v-1l1-1h1 0v2c0 1 0 1 1 2h1c0-2 0-2-1-4 1-1 2-1 3-1z" class="d"></path><path d="M426 728l2 1c-1 1-2 2-1 4v1 1l3 5c0 3 0 7 1 9-1 1-2 0-3 0-1-1-1 0 0-1v-1-2c-2 1-2 0-3 0s-1 1-3 1c0-2 0-4 1-6l1-1h1v-2c1 0 0 0 1-1-1-2-1-4-2-6l2-2z" class="D"></path><path d="M424 739c2 1 2 1 3 3l1 3c-2 1-2 0-3 0s-1 1-3 1c0-2 0-4 1-6l1-1z" class="V"></path><path d="M431 736h-1c1-1 1-2 2-3h3v1c2 1 3 3 4 5 1 1 3 1 4 2h3c3-1 5-1 7-3 1 0 1 0 2 2h0c-1 2-3 4-4 4h-2-3c-1 0-2-1-3-1s-1-1-2-1h-1c-1 0-1-1-2-2l-1-1c0-1 0-1-1-1v1c1 1 1 2 2 2 1 1 0 1 1 2-1 0-1 1-2 2 1 0 2 0 2 1h-2c-1-1-3-2-3-4-2-2-2-3-3-6z" class="K"></path><path d="M431 736h-1c1-1 1-2 2-3h3v1 4h-1v2 2c-2-2-2-3-3-6z" class="H"></path><path d="M446 724c4 1 5 1 8 4 0 1 1 3 2 4h0c-1 2 0 7-1 8h0c-1-2-1-2-2-2-2 2-4 2-7 3h-3c-1-1-3-1-4-2-1-2-2-4-4-5v-1h4c1-1 0-1 0-3l2-1 1-1c1-2 2-3 4-4z" class="d"></path><g class="I"><path d="M446 724c4 1 5 1 8 4 0 1 1 3 2 4h0c-1 2 0 7-1 8h0c-1-2-1-2-2-2v-3c0-3 0-6-2-8-2-1-3-1-5-1s-3 2-4 3h0-1l1-1c1-2 2-3 4-4z"></path><path d="M443 741l1-1h-1c-1-2-2-4-2-7 0-1 0-2 1-3h1c2-2 3-2 6-2 0 0 1 0 2 1 1 2 1 4 1 6 0 1 0 2-1 3-2 1-2 1-5 1l1-1h1c2-2 3-3 3-5-1-2-1-2-2-3-2 0-3 2-4 1h-2c-1 1-2 3-1 4 0 2 3 4 4 6h-3z"></path></g><path d="M439 714c2 0 2 0 4 1 1-1 1-1 2 0h2l-2 4 2 1-1 1-1 1-3 3v3l-1 1-2 1c0 2 1 2 0 3h-4-3c-1 1-1 2-2 3h1v1c-1 1-1 2-1 3l-3-5v-1-1c-1-2 0-3 1-4 0 1 0 1 1 1 1-2 2-5 2-8v-4c1 0 2-1 2-1v-1l1-1h1 0v2c0 1 0 1 1 2h1c0-2 0-2-1-4 1-1 2-1 3-1z" class="p"></path><path d="M433 724l1-1 3 1c0 1 0 0-1 1s-2 1-2 2h-2l1-3z" class="E"></path><path d="M437 719c0-2 0-2-1-4 1-1 2-1 3-1 1 1 1 2 1 4-1 0-1 1-1 1h-2z" class="g"></path><path d="M441 719h1c1 1 1 1 0 3-1 1-3 0-5 0-1 0-1 0-2-1v-1c3 0 4 0 6-1z" class="T"></path><path d="M445 719l2 1-1 1-1 1-3 3v3l-1 1-2 1c0 2 1 2 0 3h-4l-1-1h2c1-2 0-2 0-3v-2c1-1 2-2 3-2 1-1 5-3 5-4 1-1 1-2 1-2z" class="E"></path><path d="M428 729c0 1 0 1 1 1 1-2 2-5 2-8l2 2-1 3v4c1-1 1-3 3-3l1 1c0 1 1 1 0 3h-2l1 1h-3c-1 1-1 2-2 3h1v1c-1 1-1 2-1 3l-3-5v-1-1c-1-2 0-3 1-4z" class="P"></path><path d="M581 573c1-1 1-2 1-2 0-2 2-3 3-4l2-3c1-1 1-2 2-2l1-1 1-1c2-1 4-5 6-5 0-1 2 0 2 0 2-1 4-3 6-4h3c1 2 2 2 2 4v2h0l2-2c2 1 3 2 4 3l1 2h0c1 2 1 5 2 6s3 1 4 2c1 0 1 1 3 1v1c2 0 3 1 4 2v1c1 0 2 1 3 2v1c0 2 2 4 2 7 0 2 0 5 2 5h0v1l-1 1-1 7 1 1v1l2 2c-1 1 0 2 0 4h1c0 1-1 2-1 4h0l-4 4h0 3s1 0 1 1l-1 1v1l-2 2c-1 1-2 2-3 2l-9 4-5 2v1c-1-1-1-2-2-4l1-1c-1-1-7-1-9-1-4-1-8-1-12-4v2l-1 2c-1-1-1-1-2-1-4-1-6-2-9-5h-1l-2-2c0-1-1-2-2-3h-3c0 1 0 0-1 1h0v6c-1 1-1 1-1 2s0 2-1 4l-1-40c0-1-1-2-2-3-2-1-4-3-6-5v-1c-1 1-1 2-2 4l-1-2v-2-4h-4-3l-1-2h2l2-2c-2-2-3-4-5-6 0-1 1-2 1-2 1-2 2-2 4-3l1 2 1 1 3 2 12 12 3-3h1v1l1 1h1s1 1 1 2z" class="j"></path><path d="M584 580h1v-1l-1-1c0-2-1-3-2-3l1-1c1 1 2 2 2 4h0 1l1-1c2 0 3 1 4 2 1 0 1 1 2 2h-3v1h-2l-4-1v-1z" class="g"></path><path d="M574 572l3-3h1v1l1 1h1s1 1 1 2c-1 1-2 2-2 3 2 2 3 3 5 4v1c-2-1-3-2-5-3l-5-6z" class="U"></path><path d="M617 560h0c1 2 1 5 2 6s3 1 4 2h-4c0 1 1 1 1 2v1l-1 1v1c-1 1-1 3-3 4h-1-1v-1l1-2c3-5 3-8 2-14z" class="F"></path><path d="M630 573c1 0 2 1 3 2v1c0 2 2 4 2 7s0 5-1 7c-1 1-2 2-2 3 0-2 0-4 1-6 0-3-1-6-2-9 0-1 1-1 1-1v-1c-1-1-1-2-2-3h0z" class="i"></path><path d="M597 605c2 0 3 0 5 1h3s1 1 2 1h0l2 1c2 1 3 1 6 1l2-1-2 3h-2l-8-2c-3-1-6-2-8-4z" class="V"></path><path d="M602 596h0l-1-1c0-2-1-3-2-4v-3c-1 0-2 0-2-1s1 0 3-1l-1-2v-1h0c1-2 3-2 5-3-2 2-3 4-3 6v2 1c0 2 1 3 2 4v3l2-1c0 1 1 1 2 2-2 0-3 0-5-1z" class="N"></path><path d="M595 602l2 2v1c2 2 5 3 8 4-1 0-2 1-3 0-1 0-2 0-4-1 0 1 0 1-1 1h0-1c0 1-1 2-1 3-1-2-3-3-4-4l3-1c1-2-1-2-1-4l2-1z" class="S"></path><path d="M623 568c1 0 1 1 3 1v1c2 0 3 1 4 2v1h0c1 1 1 2 2 3v1s-1 0-1 1l-1-1-2-2-3-2c-2 0-3-2-5-2v-1c0-1-1-1-1-2h4z" class="W"></path><path d="M590 582v-1h3v1 2h1l1-1v2h-1-1c-1 1-2 3-2 4s1 1 1 2c-1 2 0 4 1 6 1 1 1 0 2 1 0 3 1 2 2 4v2l-2-2c-2-2-3-3-4-5-2-5-2-10-1-15z" class="N"></path><path d="M609 601h0c-2 1-1 2-2 3h-3c-2-1-3-1-4-2v-4c1-1 1 0 1-1l1-1c2 1 3 1 5 1l3 2-1 2z" class="t"></path><path d="M635 583c0 2 0 5 2 5h0v1l-1 1-1 7 1 1v1 1c-1-1-1-2-1-2-2 1-4 4-5 6l-2-1c0-1-1-1-1-2 2-2 3-5 5-8h0c0-1 1-2 2-3 1-2 1-4 1-7z" class="c"></path><path d="M610 599h3 1l4-1v1l1 1c1 1-1-1 0 1l1 2c0 1 0 1-1 2-3 1-5 1-8 1l-2-2v-3l1-2z" class="q"></path><path d="M630 604c1-2 3-5 5-6 0 0 0 1 1 2v-1l2 2c-1 1 0 2 0 4h1c0 1-1 2-1 4h0l-4 4-2 1-2-2-2-3-1-1h-1l-2 2c-1 0-2 1-3 1l-2 1c-2-1-4-1-6-1h0 2l2-3c4 0 8-4 11-5l2 1z" class="K"></path><path d="M629 609l-1-1c1-1 2-2 3-2l1 1-1 2h-2z" class="G"></path><path d="M635 607c0-1-1-2-1-3h1c1 0 2 0 3 1h1c0 1-1 2-1 4h0l-3-2z" class="t"></path><path d="M635 607l3 2-4 4-2 1-2-2-2-3h1 2c1 0 1 0 2 1h1 0c0-1 1-2 1-2v-1z" class="N"></path><path d="M628 603l2 1c-5 4-8 6-15 7l2-3c4 0 8-4 11-5z" class="i"></path><path d="M579 578c2 1 3 2 5 3l4 1h2c-1 5-1 10 1 15 1 2 2 3 4 5l-2 1c0 2 2 2 1 4l-3 1h0l-3-3c-1-1-2-1-3-2v-1c0-1-1-3-1-4h0l-1-4-1-1c1-1 1-2 2-3 0 0 1-1 2-1v-2l-1-1c-2-1-3-1-3-4l-1-1c0-1-1-1-2-1v-2z" class="L"></path><path d="M585 586c0-1 0-1 1-2h2l-1 6c-1-1-1-2-1-3l-1-1z" class="a"></path><path d="M579 578c2 1 3 2 5 3l4 1v2h-2c-1 1-1 1-1 2-2-1-3-1-3-4l-1-1c0-1-1-1-2-1v-2z" class="B"></path><path d="M588 598h1c0 1 1 2 3 2l1 1c-1 2-2 1-2 3 0 1-1 2 0 3v1l-3-3c-1-1-2-1-3-2v-1l2-3s0-1 1-1z" class="C"></path><path d="M587 599l3 5h-1l-1 1c-1-1-2-1-3-2v-1l2-3z" class="D"></path><path d="M586 587c0 1 0 2 1 3v1l-1 1c1 1 2 2 3 4v2h-1c-1 0-1 1-1 1l-2 3c0-1-1-3-1-4h0l-1-4-1-1c1-1 1-2 2-3 0 0 1-1 2-1v-2z" class="B"></path><path d="M584 598c0-1 1-1 1-1 1-1 0-2 2-2l1 1v2c-1 0-1 1-1 1l-2 3c0-1-1-3-1-4h0z" class="L"></path><defs><linearGradient id="V" x1="611.198" y1="576.558" x2="607.368" y2="581.851" xlink:href="#B"><stop offset="0" stop-color="#3b3b37"></stop><stop offset="1" stop-color="#575655"></stop></linearGradient></defs><path fill="url(#V)" d="M614 576v1h1 1v1 1c1 0 2 1 3 1 3 4 5 5 5 10 0 3-1 4-3 6l1 2-1 1c-2-1 0-2-3-1l-4 1h-1-3l-3-2c-1-1-2-1-2-2l-2 1v-3c-1-1-2-2-2-4v-1-2c0-2 1-4 3-6l10-4z"></path><path d="M605 584c-1-1-1-1 0-3l2 1-2 2z" class="w"></path><path d="M616 593v1h1c1 0 2-1 2-2s1-2 1-2h1c0 1-1 2-1 2l1 2c-1 1-2 1-3 2h-4c-2 1-2 0-4-1h-1l-1-1c2 0 2 0 4-1h4z" class="E"></path><path d="M620 582c1 1 2 3 2 5 1 2 0 4-1 7l-1-2s1-1 1-2h-1s-1 1-1 2-1 2-2 2h-1v-1c1-1 2-2 2-3 1-1 1-2 0-3-1-3-3-4-5-5h1c2 0 2 0 4 1 1 0 1 0 2-1z" class="U"></path><path d="M616 579c1 0 2 1 3 1 0 1 0 2 1 2-1 1-1 1-2 1-2-1-2-1-4-1h-1c-2 1-3 2-4 4-1 1-1 3 0 5 1 1 2 1 3 2-2 1-2 1-4 1h0c-3-3-4-6-3-10l2-2c2-1 4-2 7-2 0 0 1 0 2-1h0z" class="V"></path><path d="M613 582c2 1 4 2 5 5 1 1 1 2 0 3 0 1-1 2-2 3h-4c-1-1-2-1-3-2-1-2-1-4 0-5 1-2 2-3 4-4z" class="u"></path><path d="M619 612l2-1c1 0 2-1 3-1l2-2h1l1 1 2 3 2 2 2-1h0 3s1 0 1 1l-1 1v1l-2 2c-1 1-2 2-3 2l-9 4-5 2v1c-1-1-1-2-2-4l1-1c-1-1-7-1-9-1-4-1-8-1-12-4v2l-1 2c-1-1-1-1-2-1l-2-2v-1l2-2v-2s1 0 1-1h1c0-1 1-2 1-3h1 0c1 0 1 0 1-1 2 1 3 1 4 1 1 1 2 0 3 0l8 2h0c2 0 4 0 6 1z" class="H"></path><path d="M618 614c0 1 1 1 1 2s1 2 2 3v1l-1 1h0 1s1 1 1 2c1 0 1 1 1 1l-5 2v1c-1-1-1-2-2-4l1-1c-1-1-7-1-9-1 0-1 1-2 1-3l1-2v-1l1 2c0 1 0 2 1 2 0-1 0-1 1-2 1 1 1 1 3 1 1-1 1-2 2-4z" class="N"></path><path d="M620 621h1s1 1 1 2c1 0 1 1 1 1l-5 2c1-2 1-2 0-4l2-1z" class="D"></path><path d="M626 613c2-1 2-1 4-1l2 2 2-1h0 3s1 0 1 1l-1 1v1l-2 2c-1 1-2 2-3 2l-9 4s0-1-1-1c0-1-1-2-1-2h-1 0l1-1v-1c-1-1-2-2-2-3s-1-1-1-2h0 3v-1l2 1h0c2 1 2 1 2 3h1c1-2 0-2 0-3v-1z" class="Q"></path><path d="M627 618l4-2c0 1 0 2 1 3l2-2 1 1c-1 1-2 2-3 2h-2-1c-1 1-1 1-2 1l1-1-1-2z" class="J"></path><path d="M626 613c2-1 2-1 4-1l2 2 2-1h0 3s1 0 1 1l-1 1v1l-2 2-1-1-2 2c-1-1-1-2-1-3v-1c-2 0-3 0-5-2z" class="Z"></path><path d="M618 614h0 3v-1l2 1h0c2 1 2 1 2 3h1c1-2 0-2 0-3v-1c2 2 3 2 5 2v1l-4 2-6 3h0-1 0l1-1v-1c-1-1-2-2-2-3s-1-1-1-2z" class="U"></path><path d="M618 614h0 3v-1l2 1-1 1c0 1 1 2 0 3l-1 1c-1-1-2-2-2-3s-1-1-1-2z" class="e"></path><path d="M597 609h0c1 0 1 0 1-1 2 1 3 1 4 1 1 1 2 0 3 0l8 2h0c2 0 4 0 6 1h-1-1-2 0c-3 0-6 0-8-1l-1 1v3c1 2 2 0 1 3h2c0 1-1 2-1 3-4-1-8-1-12-4v2l-1 2c-1-1-1-1-2-1l-2-2v-1l2-2v-2s1 0 1-1h1c0-1 1-2 1-3h1z" class="R"></path><path d="M593 615l3 2v2l-1 2c-1-1-1-1-2-1l-2-2v-1l2-2z" class="H"></path><path d="M597 609h0c1 0 1 0 1-1 2 1 3 1 4 1 1 1 2 0 3 0l8 2h0c2 0 4 0 6 1h-1-1-2 0c-3 0-6 0-8-1l-1 1v3c1 2 2 0 1 3l-1-1-2 1h0v-3c0-1 1-1 1-3-2 0-3 3-5 4-1-2-3-2-4-4l1-3z" class="G"></path><path d="M557 566c-2-2-3-4-5-6 0-1 1-2 1-2 1-2 2-2 4-3l1 2 1 1 3 2 12 12 5 6v2c1 0 2 0 2 1l1 1c0 3 1 3 3 4l1 1v2c-1 0-2 1-2 1-1 1-1 2-2 3l1 1 1 4h0c0 1 1 3 1 4v1c1 1 2 1 3 2l3 3h0c1 1 3 2 4 4h-1c0 1-1 1-1 1v2l-2 2v1l2 2c-4-1-6-2-9-5h-1l-2-2c0-1-1-2-2-3h-3c0 1 0 0-1 1h0v6c-1 1-1 1-1 2s0 2-1 4l-1-40c0-1-1-2-2-3-2-1-4-3-6-5v-1c-1 1-1 2-2 4l-1-2v-2-4h-4-3l-1-2h2l2-2z" class="l"></path><path d="M583 594l1 4h0l-2 2h0c0-2-1-3-2-4l-2 1v-1-1c2 0 3-1 5-1z" class="c"></path><path d="M575 611v-10l4 9h-3c0 1 0 0-1 1h0z" class="C"></path><path d="M578 595v-1-2h0l2-2v-1h-1l1-1-2-2c0-1 1-1 2-1v-3l1-1 1 1c0 3 1 3 3 4l1 1v2c-1 0-2 1-2 1-1 1-1 2-2 3l1 1c-2 0-3 1-5 1z" class="E"></path><path d="M582 593c-1-3 0-8 0-11 0 3 1 3 3 4l1 1v2c-1 0-2 1-2 1-1 1-1 2-2 3z" class="D"></path><path d="M584 598c0 1 1 3 1 4v1c1 1 2 1 3 2l3 3h0c1 1 3 2 4 4h-1c0 1-1 1-1 1v2l-2 2v1l2 2c-4-1-6-2-9-5-1-1 0 0-1-2 0-1 0-1-1-2h0l-1-2c-2-2-2-2-1-4l-1-1 3-3v-1l2-2z" class="X"></path><path d="M584 598c0 1 1 3 1 4v1h-1c-1 0-1 0-2-2v-1l2-2z" class="R"></path><path d="M580 605c1-1 2-1 3-1l1 1c0 1-1 1-1 2v1c2 0 2-1 3-1-1 2-1 2-2 3-1 0-1 0-2 1h0l-1-2c-2-2-2-2-1-4z" class="e"></path><path d="M586 607c1 1 2 2 2 3s0 3-1 4l1 1v-1c0-1 1-1 1-2 2 0 2 1 2 2v3 1l2 2c-4-1-6-2-9-5-1-1 0 0-1-2 0-1 0-1-1-2 1-1 1-1 2-1 1-1 1-1 2-3z" class="B"></path><path d="M586 607c1 1 2 2 2 3-2 1-1 4-5 3 0-1 0-1-1-2 1-1 1-1 2-1 1-1 1-1 2-3z" class="P"></path><path d="M557 566c-2-2-3-4-5-6 0-1 1-2 1-2 1-2 2-2 4-3l1 2 1 1 3 2 12 12 5 6v2c-1 0-1 1-1 1-2-1-3-2-4-4-2-2-8-7-11-9-1 3 0 6-1 8l-1-2v-4h-4-3l-1-2h2l2-2z" class="H"></path><path d="M561 564v6h-4-3l-1-2h2l2-2 1 1 1-1 2-2z" class="a"></path><path d="M557 566c-2-2-3-4-5-6 0-1 1-2 1-2 1-2 2-2 4-3l1 2 1 1h-1l-1-1h-1l2 2-1 1c1 1 2 3 4 4l-2 2-1 1-1-1z" class="C"></path><path d="M592 851l2 1 3 1c7 5 13 9 21 12l-1 1 1 2c2 0 5 1 7 1 10 4 21 5 31 6l2 1h3 2 1c8 1 17 2 25 1l2 1c11 0 22 1 33 0l50 1c11 0 24 1 35 0 2 0 8-1 10 0l-114 12c-27 3-55 5-83 3-16-1-32-3-47-9-6-3-12-7-17-11l1-1c1 0 2-1 3-2l9-6 1-1c1 0 2-2 3-2l4-3 2-1c1-1 3-3 5-4 0 2-1 3 0 4 0 1 1 3 1 4 2 0 3 0 4 1h0c0-1 1-2 1-2v-1c0-2 0-3-1-5l2-1-2-2 1-1z" class="u"></path><path d="M587 881l3 1c1 1 1 2 2 3-2 1-3 1-4 0 0-1-1-2-1-4z" class="a"></path><path d="M575 874h0 3 2v2l-1-1c-2 2-1 2-1 4l-1 4s-1-1-1-2h0v-3c-1-2-1-3-1-4z" class="F"></path><path d="M587 877v-4l1 1 1-1h1c-1 1-1 2-1 3 1 1 1 1 1 2 1 2 1 2 1 4h1v-2h1l1 1c0 2 0 3-1 4h-1c-1-1-1-2-2-3l-3-1v-4z" class="B"></path><path d="M598 863h3l4 3c4 3 6 6 9 9h-1l-2 2c-1 1-2 2-4 3 1 1 1 1 1 2v1c-1-1-1-1-1-2-1 0-2 1-2 1-1 1 0 1-1 2l-1-1c-1-1-1-2-2-2v2c-1 1-1 1-2 1l-2 1c-1-1-1-1-1-2l2-1h2l-1-3 1-1h0v-3l1-1c1 0 1 0 2 1l1 1c0-2-1-3 0-5-2-1-3-3-4-5h0l-2-3z" class="K"></path><path d="M592 851l2 1 3 1c7 5 13 9 21 12l-1 1 1 2h-1c-2 0-5-1-7-2h-1l-1-1c-2-1-4-1-5-2h-1 1c0 1 1 1 2 2l2 1c1 1 1 1 2 1l6 6c0 1-1 1-1 2-3-3-5-6-9-9l-4-3h-3l-2-2c-1 0-1-1-2-1-1-1-1-1-2 0 0-2 0-3-1-5l2-1-2-2 1-1z" class="H"></path><path d="M592 851l2 1 3 1c-1 1-2 1-2 2-1 0-2-1-2-1l-2-2 1-1z" class="R"></path><path d="M597 853c7 5 13 9 21 12l-1 1c-2 0-4-1-6-2-6-2-11-5-16-9 0-1 1-1 2-2z" class="U"></path><path d="M592 860c1-1 1-1 2 0 1 0 1 1 2 1l2 2 2 3h0c1 2 2 4 4 5-1 2 0 3 0 5l-1-1c-1-1-1-1-2-1l-1 1v3h0l-1 1 1 3h-2l-2 1c-1-2-2-4-3-5v2h-1v2h-1c0-2 0-2-1-4 0-1 0-1-1-2 0-1 0-2 1-3h-1l-1 1-1-1 1-1 2-1v-3c0-2 2-4 2-7v-1z" class="I"></path><path d="M592 860c1-1 1-1 2 0 1 0 1 1 2 1l2 2 2 3-2 1c-1-1-1-2-2-3 0-1-1-2-1-2h-1c0 1 0 1-1 2v2 4c-2 0-1 0-2-1-1 0 0 0-1-1 0-2 2-4 2-7v-1z" class="B"></path><path d="M593 876c2-1 0-1 2-2 0 1 0 1 2 1 0-1 1-1 1-1 1 0 1 1 2 1h0v3h0l-1 1 1 3h-2l-2 1c-1-2-2-4-3-5v2h-1v-5l1 1z" class="F"></path><path d="M592 880v-5l1 1c1 2 2 4 3 5l2-2c1 0 0-1 1 0l1 3h-2l-2 1c-1-2-2-4-3-5v2h-1z" class="G"></path><path d="M581 858c1-1 3-3 5-4 0 2-1 3 0 4 0 1 1 3 1 4 2 0 3 0 4 1h0c0-1 1-2 1-2 0 3-2 5-2 7v3l-2 1-1 1v4h-1c0 1 0 3 1 5l-3 3-1-1v-2c-3-2-4-4-4-7l1 1v-2h-2-3 0c0 1 0 2 1 4v3c-2-1-3-4-4-4h-1s-2-2-2-3v-3h0l1-1h1l-1-2 2-1-1-2 1-1c1 0 2-2 3-2l4-3 2-1z" class="H"></path><path d="M575 874c-1-3 0-6 0-8h1v2c0 2 0 2 1 4l-1 1h1l1 1h-3 0z" class="K"></path><path d="M576 868h1v-3l1-1c1 2-1 7 3 8v-4c1 2 0 5 1 7v1c0 2 0 4 1 6-3-2-4-4-4-7l1 1v-2h-2l-1-1h-1l1-1c-1-2-1-2-1-4z" class="G"></path><path d="M579 859l2-1c0 2 0 4 1 6h0c-1 1-1 2 0 3v2l2 1c1 0 1 1 2 1v1 1l1-2 1 1-1 1v4h-1c0 1 0 3 1 5l-3 3-1-1v-2c-1-2-1-4-1-6v-1c-1-2 0-5-1-7h0c0-1-1-1-1-2 0-2-1-5-1-7z" class="C"></path><path d="M581 858c1-1 3-3 5-4 0 2-1 3 0 4 0 1 1 3 1 4 2 0 3 0 4 1h0c0-1 1-2 1-2 0 3-2 5-2 7v3l-2 1-1-1-1 2v-1-1c-1 0-1-1-2-1l-2-1v-2c-1-1-1-2 0-3h0c-1-2-1-4-1-6z" class="G"></path><path d="M587 871c0-1 0-2-1-4 0-1 0 0-1-1l1-1c1 0 1 1 2 1v1c1 0 1 1 2 1h0v3l-2 1-1-1z" class="F"></path><defs><linearGradient id="W" x1="838.111" y1="256.883" x2="790.932" y2="271.705" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#282726"></stop></linearGradient></defs><path fill="url(#W)" d="M778 133c1-1 3-1 4-1l1 2 3 1c2 0 4 1 6 2l4 4c1 0 1 0 2 1 0 1 1 1 1 2l2-1h1c1 0 1 1 2 2h1 0l1-1c1 1 1 1 2 1h1 1l1 1h0c2 0 3 0 4-1 1 1 2 1 3 2 0 2 0 2 1 3 2 3-1-1 1 1l1 1c-1 4-2 7-2 11l-2 48v138 1l-1 2c-1-2 1-5 0-7l-1 1s1 2 0 2h0v-6c-1 0-1 0-2 1v-6c-1-6-1-11-1-16v-6h1c1-3 0-6 0-9l-4-31v2h-1c-1-4-2-8-2-13-1-1-2-3-2-4v-2c1-2-3-8-4-11-1-5-1-12-4-16v-2c-1-1-2-3-3-4l-2-5c-1-3-2-4-4-6v-1l-6-9h-1v-3 1c0-2 0-2-1-3-2-4-5-7-7-10l-10-9-1-1-3-3c-1 0-2-1-3-2l-1-1c1 0 2-1 2-1v-2c1 1 2 1 3 2v2c1 0 1 0 2-1h-1l-1-1c0-1 1-2 2-2l5 5 1-2h0c0-3-2-4-4-6l-1-1 1-1c1 0 1-1 2-1h1l-1-1c-1-2-1-3-1-5h1v1l1-1c-1-2 0-6-1-9 0-1 0-2 1-3 2-4 7-7 10-10 1 0 2-1 3-2l-1-1z"></path><path d="M813 337c0-2 0-4 1-5v1l1 9c-1 0-1 0-2 1v-6z" class="X"></path><path d="M807 189c2 1 4 3 5 5l-2 1v1h2l1 1-1 1h-2v-1l-1 1c-2-2-2-4-2-6v-3z" class="O"></path><path d="M812 321v-6h1l1 18v-1c-1 1-1 3-1 5-1-6-1-11-1-16z" class="E"></path><path d="M806 191l1 1c0 2 0 4 2 6l1-1v1h2c1 3 1 5 0 8h1l-1 1c-1-1-2-3-3-4v-1c-2-4-3-6-3-11z" class="B"></path><path d="M810 198h2c1 3 1 5 0 8 0-1-1-1-1-2-1-2-1-4-1-6z" class="M"></path><path d="M789 191h1l2 4-1 1 1 1-1 1v1c1 1 2 1 3 3v1s1 0 1 1c2 1 3 6 4 8l-3-2-7-11c0-1 1-1 1-2s0-2-1-3v-3z" class="I"></path><path d="M808 229c1 4 7 18 6 21 0 1-1 1-1 2 0-2-2-6-2-7l-4-14h1v-2z" class="B"></path><path d="M802 220c2 4 4 7 5 11l4 14-1 1-1-1c0-1 0-2-1-3 0-3-2-9-4-12-1-1-2-2-2-3 0-3-1-5 0-7z" class="G"></path><path d="M798 226c1 0 2 0 2 2h0c1 1 2 2 3 2h1c2 3 4 9 4 12h0c-1-1-1-2-1-2l-1-4c0-1-1-2-1-2h-1c-1-1-2-3-3-4 0 1 0 2-1 4l1 4 1-1h2c1 2 1 2 1 4-1-1-1-1-1-2s-1-1-1-1l-1 2v1 3l-3-10-2-5h0c0-1 1-2 1-3z" class="e"></path><path d="M797 229h2c1 1 1 2 1 3l-1 2-2-5z" class="J"></path><path d="M800 200c1-1 1-1 3-1l6 4c1 1 2 3 3 4l1-1h0v2c1 1 1 2 0 2v3l-2-2-1-1c-2 0-2 0-3-1v-1l-1-1-1-1c0-1 0-1-1-1v-1c-2-1-3-3-4-4z" class="M"></path><path d="M796 229h1 0 0l2 5 3 10 7 31v2h-1c-1-4-2-8-2-13-1-1-2-3-2-4v-2c1-2-3-8-4-11-1-5-1-12-4-16v-2z" class="R"></path><path d="M793 188h0c1-3 1-5 4-7l3 3c-1 1-1 2-1 3h1l1 4c-1 1-1 2-1 3l-1 1h0c0 2-1 3-1 4-1-2-3-3-3-6l-2 1-2 2 1-1-2-4v-2c1-1 2 0 3-1z" class="C"></path><path d="M792 195v-1c0-2-1-3-1-4l1-1c0 1 1 1 1 1 1 1 0 1 1 1v2h1l-2 1-2 2 1-1z" class="O"></path><path d="M800 184c1 1 2 1 3 1s1 0 1-1l1 1-2 2h1 1l2 2v3l-1-1c0 5 1 7 3 11v1l-6-4c-2 0-2 0-3 1h-1l-1-1c0-1 1-2 1-4h0l1-1c0-1 0-2 1-3l-1-4h-1c0-1 0-2 1-3z" class="a"></path><path d="M800 194h3l2 2-1 1h-1l-1 1c-1-1-1-2-2-4z" class="D"></path><path d="M804 187h1l2 2v3l-1-1c-1 0-1 0-2-1s0-2 0-3z" class="e"></path><path d="M800 187l2 2c1 1 0 3 1 5h-3 0c0-1 0-2 1-3l-1-4z" class="B"></path><path d="M772 189h1l6 7 2-1 2 4 3 5 2 2 3-2 4 7 1-1h0l3 2 9 17v2h-1c-1-4-3-7-5-11-1 2 0 4 0 7 0 1 1 2 2 3h-1c-1 0-2-1-3-2h0c0-2-1-2-2-2 0 1-1 2-1 3h0-1c-1-1-2-3-3-4l-2-5c-1-3-2-4-4-6v-1l-6-9h-1v-3 1c0-2 0-2-1-3-2-4-5-7-7-10z" class="Y"></path><path d="M786 204l2 2c1 1 4 7 4 8l-2-1v1c-1-1-5-8-5-9l1-1z" class="P"></path><path d="M797 222l-4-8 1-1c4 1 3 4 5 5v-2c1 1 2 3 3 4-1 2 0 4 0 7 0 1 1 2 2 3h-1c-1 0-2-1-3-2h0c0-2-1-2-2-2v-1c-1-1-1-2-1-3z" class="B"></path><path d="M772 189h1l6 7 2-1 2 4 3 5-1 1c0 1 4 8 5 9l4 8h0 3c0 1 0 2 1 3v1c0 1-1 2-1 3h0-1c-1-1-2-3-3-4l-2-5c-1-3-2-4-4-6v-1l-6-9h-1v-3 1c0-2 0-2-1-3-2-4-5-7-7-10z" class="i"></path><path d="M794 222h0 3c0 1 0 2 1 3v1c0 1-1 2-1 3h0c-1-3-2-5-3-7z" class="L"></path><path d="M781 195l2 4 3 5-1 1c-2-1-5-7-6-9l2-1z" class="T"></path><path d="M761 173h-1l-1-1c0-1 1-2 2-2l5 5 1-2c4 4 8 7 11 11 1-1 1-1 1-3 2 1 3 1 5 1l1-1 1 4 3 3h0c0 1 0 1-1 2l1 1v3c1 1 1 2 1 3s-1 1-1 2l7 11h0l-1 1-4-7-3 2-2-2-3-5-2-4-2 1-6-7h-1l-10-9-1-1-3-3c-1 0-2-1-3-2l-1-1c1 0 2-1 2-1v-2c1 1 2 1 3 2v2c1 0 1 0 2-1z" class="f"></path><path d="M780 189l5 6c-1 1-1 2-2 4l-2-4c-1-1-2-3-3-4l2-2z" class="k"></path><path d="M765 175h2s1 2 2 2l7 7 4 5-2 2c-1-1-1-2-2-3 0-1-1-1-1-1v-2l-4-4-6-6z" class="P"></path><path d="M783 199c1-2 1-3 2-4l6 9-3 2-2-2-3-5z" class="y"></path><path d="M771 181l4 4v2s1 0 1 1c1 1 1 2 2 3s2 3 3 4l-2 1-6-7c-2-1-3-3-4-4l2-4z" class="E"></path><path d="M779 181c2 1 3 1 5 1l1-1 1 4 3 3h0c0 1 0 1-1 2l1 1v3c1 1 1 2 1 3s-1 1-1 2c-1-3-3-6-5-8s-4-5-6-7c1-1 1-1 1-3z" class="w"></path><path d="M786 185l3 3h0c0 1 0 1-1 2l1 1v3c-1-1-2-2-3-4l-2-2c0-1 0-1-1-1h-1l1-1c1-1 2-1 3-1z" class="Z"></path><path d="M756 170c1 1 2 1 3 2v2c1 0 1 0 2-1 2 0 3 1 4 2h0l6 6-2 4c1 1 2 3 4 4h-1l-10-9-1-1-3-3c-1 0-2-1-3-2l-1-1c1 0 2-1 2-1v-2z" class="R"></path><path d="M778 133c1-1 3-1 4-1l1 2 3 1c2 0 4 1 6 2l4 4c1 0 1 0 2 1 0 1 1 1 1 2l2-1h1c1 0 1 1 2 2h1 0l1-1c1 1 1 1 2 1h1 1l1 1h0c2 0 3 0 4-1 1 1 2 1 3 2 0 2 0 2 1 3 2 3-1-1 1 1l1 1c-1 4-2 7-2 11l-2 48v-5c-1-1-1-2-1-3v-10c0-1 1-2 0-3l-2-1c0-1 0-1 1-2l1 1v-1l-1-1-4-4v-1l-3-2h0-2-1l1-1h1v-1l-1-1v-1h-1l-1 1c0 1 0 1-1 1h0c-2 1-5 3-6 4-3 2-3 4-4 7h0c-1 1-2 0-3 1v2h-1l-1-1c1-1 1-1 1-2h0l-3-3-1-4-1 1c-2 0-3 0-5-1 0 2 0 2-1 3-3-4-7-7-11-11h0c0-3-2-4-4-6l-1-1 1-1c1 0 1-1 2-1h1l-1-1c-1-2-1-3-1-5h1v1l1-1c-1-2 0-6-1-9 0-1 0-2 1-3 2-4 7-7 10-10 1 0 2-1 3-2l-1-1z" class="l"></path><path d="M769 163c2 2 4 3 4 7 3 0 4-1 6 0h0l1 1c-1 0-1 1-2 0-1 0-1 0-2 1-2-1-4-2-6-4v-2s0-1-1-2v-1z" class="J"></path><path d="M766 158h3c0 1 1 4 0 5v1c1 1 1 2 1 2v2l-3-3v-1c-1-2-1-3-2-5l1-1z" class="I"></path><path d="M780 159c1 2 2 3 3 3 2 1 4 1 5 1l1-1c1 2 1 4 1 6h-1l-6 6c-3-1-5-1-7-2 1-1 1-1 2-1 1 1 1 0 2 0l-1-1c1-2 1-4 1-5v-1l-1-4 1-1z" class="B"></path><path d="M789 162c1 2 1 4 1 6h-1c-1-1-2-1-3 0-1-1-1-2-2-2h-1v-1c3 0 3 0 5-2l1-1z" class="G"></path><path d="M766 164l-1-1c-1-2-1-3-1-5h1v1c1 2 1 3 2 5v1l3 3c2 2 4 3 6 4s4 1 7 2l6-6h1c2-2 4-3 6-5 0 1 0 2-1 3h0l-3 3c-1 1-1 1-3 2-1 0-3 2-4 3l10-5s1 0 2-1h1c-1 1-4 4-5 4-1 1-1 0-2 1s-2 1-4 2h0l-1 1c-4 1-7 0-10-2l-1 2v1 1l4 3c0 2 0 2-1 3-3-4-7-7-11-11h0c0-3-2-4-4-6l-1-1 1-1c1 0 1-1 2-1h1z" class="E"></path><path d="M766 164c0 2 1 3 2 5 2 2 5 4 8 5l-1 2v1 1l4 3c0 2 0 2-1 3-3-4-7-7-11-11h0c0-3-2-4-4-6l-1-1 1-1c1 0 1-1 2-1h1z" class="y"></path><path d="M798 168l2-1v1c-1 1-2 1-3 2v2h3l2 1c2 1 4 1 6 2v1l-1 1-1-1v-1h-1l-1 1c0 1 0 1-1 1h0c-2 1-5 3-6 4-3 2-3 4-4 7h0c-1 1-2 0-3 1v2h-1l-1-1c1-1 1-1 1-2h0l-3-3-1-4-1 1c-2 0-3 0-5-1l-4-3v-1-1l1-2c3 2 6 3 10 2l1-1h0c2-1 3-1 4-2s1 0 2-1c1 0 4-3 5-4z" class="J"></path><path d="M788 175l1 1v1 1c-1 0-2 1-2 1h-1v-2c0-1 1-1 2-2z" class="C"></path><path d="M794 177c-2-1-2-1-3-2l1-2h1c1 1 2 1 3 2l-2 2z" class="K"></path><path d="M789 188v-2c0-1-1-2-1-3s0-1 1-2l1 1 1 2c0 2 0 2 1 4h1c-1 1-2 0-3 1v2h-1l-1-1c1-1 1-1 1-2h0z" class="F"></path><path d="M776 174c3 2 6 3 10 2l1-1h0 1c-1 1-2 1-2 2v2h0c-2-1-2 0-4 0-4 1-4 0-7-3l1-2z" class="B"></path><path d="M800 172l2 1h-1l-2 1 1 1h2c-1 1-1 1-2 1-3 2-6 6-9 8h0l-1-2c0-2 2-4 4-5l2-2 1 1c1 0 0 0 1-1h-1-1c1-2 2-2 4-3z" class="P"></path><path d="M802 173c2 1 4 1 6 2v1l-1 1-1-1v-1h-1l-1 1c0 1 0 1-1 1h0c-2 1-5 3-6 4-3 2-3 4-4 7h0-1c-1-2-1-2-1-4h0c3-2 6-6 9-8 1 0 1 0 2-1h-2l-1-1 2-1h1z" class="l"></path><path d="M801 143h1c1 0 1 1 2 2h1 0l1-1c1 1 1 1 2 1h1 1l1 1h0c2 0 3 0 4-1 1 1 2 1 3 2 0 2 0 2 1 3 2 3-1-1 1 1l1 1c-1 4-2 7-2 11l-1-1-1-1c-1-2-2-1-3-2-2-1-2-2-2-3l-2-3v2 1 2c-1 0-1 1-2 1s-3-1-4 1v1c-1 2-3 4-4 6l-2 1h-1c-1 1-2 1-2 1l-10 5c1-1 3-3 4-3 2-1 2-1 3-2l3-3h0c1-1 1-2 1-3v-1l-1-2 1-1c1-2 1-3 0-4v-2l1-2 1 1h0l1-1c0-1 0-1-1-2h0v-1l2-1c0-1 0-2-1-3l2-1z" class="w"></path><path d="M803 151h1c1-1 2-1 3-1l-1 1v1c1 1 1 1 3 1l1-1 1 1h-1v2 1c-2 0-4 1-6 2v1h-2 0c0-1 1-3 1-4v-5 1z" class="I"></path><path d="M801 143h1c1 0 1 1 2 2h1 0c-1 1-1 1-1 2l1 1h-1c-1 1-1 1-1 3v-1 5c0 1-1 3-1 4-1 2-1 3-3 5 0-2 1-3 1-4l-2-1v-7h0l1-1c0-1 0-1-1-2h0v-1l2-1c0-1 0-2-1-3l2-1z" class="P"></path><path d="M801 143h1c1 0 1 1 2 2h1 0c-1 1-1 1-1 2l1 1h-1c-1 1-1 1-1 3v-1l-2-5v-2z" class="C"></path><path d="M799 144l2-1v2c-1 2 0 5 0 8v1 1c-1 1-1 3-1 5l-2-1v-7h0l1-1c0-1 0-1-1-2h0v-1l2-1c0-1 0-2-1-3z" class="J"></path><path d="M805 145l1-1c1 1 1 1 2 1h1 1l1 1h0c2 0 3 0 4-1 1 1 2 1 3 2 0 2 0 2 1 3 2 3-1-1 1 1l1 1c-1 4-2 7-2 11l-1-1-1-1c-1-2-2-1-3-2-2-1-2-2-2-3l-2-3h1l-1-1-1 1c-2 0-2 0-3-1v-1l1-1c-1 0-2 0-3 1h-1c0-2 0-2 1-3h1l-1-1c0-1 0-1 1-2z" class="G"></path><path d="M805 145l1-1c1 1 1 1 2 1h1 1l1 1-1 1h1l1-1 1 1v2c1 0 1 0 2 1h-1 0-2s-1 0-2-1l-2-1h-3l-1-1c0-1 0-1 1-2z" class="B"></path><path d="M810 153h1l-1-1-1 1c-2 0-2 0-3-1v-1l1-1c4 1 8 3 11 5 1 1 1 0 0 1v2c-2 0-3 1-4 0l-2-2h0l-2-3z" class="r"></path><path d="M810 153l2 3c0 1 0 2 2 3 1 1 2 0 3 2l1 1 1 1-2 48v-5c-1-1-1-2-1-3v-10c0-1 1-2 0-3l-2-1c0-1 0-1 1-2l1 1v-1l-1-1-4-4v-1l-3-2h0-2-1l1-1h1v-1l1-1v-1c-2-1-4-1-6-2l-2-1h-3v-2c1-1 2-1 3-2v-1c1-2 3-4 4-6v-1c1-2 3-1 4-1s1-1 2-1v-2-1-2z" class="u"></path><path d="M810 153l2 3c0 1 0 2 2 3 1 1 2 0 3 2l1 1-10-3c1 0 1-1 2-1v-2-1-2z" class="l"></path><path d="M778 133c1-1 3-1 4-1l1 2 3 1c2 0 4 1 6 2l4 4c1 0 1 0 2 1 0 1 1 1 1 2 1 1 1 2 1 3l-2 1v1h0c1 1 1 1 1 2l-1 1h0l-1-1-1 2v2c1 1 1 2 0 4l-1 1 1 2v1c-2 2-4 3-6 5 0-2 0-4-1-6l-1 1c-1 0-3 0-5-1-1 0-2-1-3-3l-1 1c0-1-1-1-1-2h0v-5c-1-1-1-2-2-2l-2 1-1-1-4 2c-1 2-1 3 0 5h-3c-1-2 0-6-1-9 0-1 0-2 1-3 2-4 7-7 10-10 1 0 2-1 3-2l-1-1z" class="u"></path><path d="M769 151h0c2-3 4-3 7-3l-5-4h0c3 0 4 2 7 3v-2h1l1 2h3c-1 1-2 1-3 2l-3-2v1 1 2l1 1h0c0 2 0 2 1 3 0 1 1 3 1 4l-1 1c0-1-1-1-1-2h0v-5c-1-1-1-2-2-2l-2 1-1-1-4 2v-2z" class="Q"></path><path d="M778 133c1-1 3-1 4-1l1 2 3 1c-2 0-4 0-6 1h-1c-3 1-8 4-9 7h0l-1 1c-1 1-2 4-3 6v2h1c1 0 1 0 2-1v2c-1 2-1 3 0 5h-3c-1-2 0-6-1-9 0-1 0-2 1-3 2-4 7-7 10-10 1 0 2-1 3-2l-1-1z" class="e"></path><path d="M792 137l4 4c1 0 1 0 2 1 0 1 1 1 1 2 1 1 1 2 1 3l-2 1v1h0c1 1 1 1 1 2l-1 1h0l-1-1c0-2-3-4-4-6h-1c-3 0-6 0-8 2 0-2 0-2-1-3h-1-1l-1-1h0l2-1h1c2 0 3-1 5 1l-2 1h4 1c1-1 1 0 3 0 0-2-4-3-4-5 0 0 1-1 2-1v-1z" class="G"></path><path d="M784 147c2-2 5-2 8-2h1c1 2 4 4 4 6l-1 2v2c1 1 1 2 0 4l-1 1 1 2v1c-2 2-4 3-6 5 0-2 0-4-1-6l-1 1c-1 0-3 0-5-1-1 0-2-1-3-3 0-1-1-3-1-4-1-1-1-1-1-3h0l-1-1v-2-1-1l3 2c1-1 2-1 3-2h1z" class="v"></path><path d="M791 148l2 2v-1c0-1 0-1-1-1 1 0 2 1 3 2h0c0 1 1 2 1 3v2c1 1 1 2 0 4l-1 1 1 2v1c-2 2-4 3-6 5 0-2 0-4-1-6l2-1 3-3v-3h-1v-2c0-1-2-1-2-2-1 1-2 1-2 1h-1c2-1 2-3 3-4z" class="F"></path><path d="M791 161c1 1 1 1 1 2h1c2-1 2-2 2-4s0-2 1-4h0c1 1 1 2 0 4l-1 1 1 2v1c-2 2-4 3-6 5 0-2 0-4-1-6l2-1z" class="I"></path><path d="M793 155l-3-1c-1 1-2 1-3 2l-1 2c-1-1-2-2-2-3h-1v1 3c-3-2-2-4-3-7 1-2 3-3 4-4 3 0 4-1 7 0-1 1-1 3-3 4h1s1 0 2-1c0 1 2 1 2 2v2z" class="G"></path><path d="M609 779c1-1 1-1 2-1l2-2 1 1h0c-2 2-3 2-3 5 0 1 0 1 2 1l-1-1v-1c1-1 2 0 3 0 2-1 2-2 4-3v2 1h1l1 1v2l1 1c1 1 1 4 2 6 1 3 2 4 5 6l-2 1c-1-1-2-2-3-2h-1c-1-1 0-1-1-1l1-2c-1-1-1-1-2-1s-2-1-2-1l-1 1v2c0 1 1 2 1 3-1 1-2 3-3 3v1h1c3 6 8 11 11 17h0l1 2c3-2 12-3 14-2 1 1 1 4 1 5 1 0 1 1 1 1 0 1 0 1-1 2l2 2v3c1 0 2 1 2 0h1l1 1 2-1c1 1 1 2 2 3 0 1 1 1 1 2-1 1-2 1-2 1v4h1l1-1h0v-2h0l1 1h1 1l1-2 5 5v1l-1 1v3l-5 29-2-1c-10-1-21-2-31-6-2 0-5-1-7-1l-1-2 1-1c-8-3-14-7-21-12l-3-1-2-1-1 1 2 2-2 1c1 2 1 3 1 5v1s-1 1-1 2h0c-1-1-2-1-4-1 0-1-1-3-1-4-1-1 0-2 0-4-2 1-4 3-5 4l-2 1-4 3c-1 0-2 2-3 2l-1-1-2-2c1-2 1-4 0-6l-1-1c0-1 0-1 1-2l-1-2h2l1-1h2c0-2 0-1 1-3l-1-1c-1-1 0-2-1-3l-1-1c-1-1-1-2-2-3v-1-4l-5-10v-2h-4v-1l2-1-1-5h0v-4-6-1c-1-1-1-1 0-2h0c-1-2-2-4-2-6 0-3 0-5-1-7v-1c-2 1-3 1-4 2-1-1-2-1-3-1-2 0-3 1-5 2v-2c2 0 3-1 4-2l1-1c1 0 2 0 4-1v-3h1v2h1c1 0 2-1 3-1l2-2 1-2 1 2h2 0l2 1h0c1 1 2 1 3 1h7v-1l3 1 2 2h0c4-1 10 0 13-2h1 2c2 0 4-1 6-2 1-1 2-1 4-1z" class="u"></path><path d="M595 833v-1c1 0 2 0 3-1 0 2 0 2 1 4l1 1v1l-1 1h0-1 0v-1c-1-2-2-3-3-4z" class="M"></path><path d="M629 841l1-1v-1l2-2v-1c1 0 1 0 2-1 0 2-1 4-1 6l-1 1-2 2c0-1-1-2-1-3z" class="G"></path><path d="M593 832l2 1c1 1 2 2 3 4v1h0 1v3c-1 1 0 1-1 2v1h-1v-3c0-1-1-1-2-2v-2l-1-1c-1-1-1-3-1-4z" class="e"></path><path d="M620 836l-1-2v-1c1 0 2 1 3 2s2 2 4 3c1 1 2 1 3 2v1c0 1 1 2 1 3-1 1-3 1-4 2-1-1-1-2 0-3l1 1v-1-1c-1-3-5-5-7-6z" class="C"></path><path d="M634 835c1 0 1-1 1-2 1-2 3-4 3-6 1-2 1-4 3-6v1c0 1 0 3-1 4-1 4-2 8-5 11v2l-2 3h-1l1-1c0-2 1-4 1-6z" class="K"></path><path d="M643 818c1 1 1 4 1 5 1 0 1 1 1 1 0 1 0 1-1 2l2 2c-1 1-1 2-1 3-1 1 0 3 0 4-1 0-1 0-1 1-2-1-2-1-3-2l2-4c-1-1-1-1-2-1 1-1 1-2 2-3h0c1-3 1-5 0-8z" class="a"></path><path d="M620 836l-2 2c-1-1-2-2-2-3v-2c1-1 1-1 3-1l1 1 4-4 1-1h1c-1 2-2 2-3 3l-1 1c0 1 1 2 2 2l1 1c2 1 3 1 4 3v2c-1-1-2-1-3-2-2-1-3-2-4-3s-2-2-3-2v1l1 2z" class="M"></path><path d="M589 814l2-1h2 14l7 2c2 1 5 2 6 4v1h-1c-8-6-22-7-31-4l1-2z" class="Y"></path><path d="M640 826v1c0 2 0 3-1 5 1 1 1 2 2 3h0v-1h0c1 1 1 1 3 2l1 1-1 1h-1c-1 1-1 3-2 4v1l-1 1c-1 0-1-1-2-1-1-2-1-3 0-4v-1h-2l-1-1c3-3 4-7 5-11z" class="O"></path><path d="M584 816c2 3 5 7 7 10 1-2 1-2 0-3l1-2c1 1 2 2 2 3l2 1c1 1 2 1 3 2v1h-1v2c1 1 0 1 0 1-1 1-2 1-3 1v1l-2-1c0-1-1-2-2-2-2-1-1-2-3-3v-1l-2-2-2 1-2-2h-2-1v-1c-1-1-1-1-1-2h-2-1-2 0l-2-2 2-2h3 3 1 4z" class="H"></path><path d="M580 816h3c1 1 1 1 1 3h-4c0-1 0-2-1-3h1z" class="M"></path><path d="M575 820c2-1 5-1 7 0l4 4-2 1-2-2h-2-1v-1c-1-1-1-1-1-2h-2-1z" class="J"></path><path d="M591 823l1-2c1 1 2 2 2 3l2 1v1 3h0c1 1 0 1 1 2l-1 1-3-4s-1-1-1-2h-1c1-2 1-2 0-3z" class="S"></path><path d="M596 801c1-3-1-5 0-8 0-1 0 0 1-1 0 4 1 6 3 9h1l-2 1c0 2 1 3 2 4s1 1 1 3v1c0 1 1 1 1 2h-3v1h7-14-2l-2 1-1 2h-1c0 2 2 5 3 7h1c1 1 1 1 0 3-2-3-5-7-7-10l-1-2-1-2v-1l1-1h0v-4l1-1h2s0 2 2 2h1v1h1v-2h4v-2h0c1 0 2 1 2 2l1-1v-1h1v-1l-2-2z" class="w"></path><path d="M601 806c1 1 1 1 1 3v1c0 1 1 1 1 2h-3v1h7-14-2l-2 1c-1 0-3 0-4-1s-1-2-1-4c1 0 2 0 3 1 2 1 3 0 5 0v2h1 1c1-1 1-2 1-4h1c0 1 0 0 1 1 0-1 1-2 2-3l2 1v-1z" class="K"></path><path d="M602 844v-2c0-1 0 0-1-1s0-1-1-2l2-1v-2c0-1 1-1 2-2 0-2 2-4 3-4 2-1 4 0 5 1 2 1 2 4 2 6v1h-1c0 2-2 2-3 3h-1c-1 1-1 1-2 1l-2 1c0 1 1 2 1 3-1 1-1 2-2 2h-1c-1 0-2 1-3 1l1 1c2 0 2-1 4 0 1-1 1-1 2-1 1-2 4-3 6-3h1l-3 3c0 1 0 2 1 3l-2 2-2-2-1 1c-2 0-5 0-6-2h-3 0v-2l-3 2-1 1-2-1 2-1c1-1 2-2 4-2 1-1 3-2 4-3v-1z" class="x"></path><path d="M609 831h1l2 2v1c1 1 1 1 1 2-1 2-3 3-4 4l-2-1c-2-2-2-3-2-5 1-2 2-2 4-3z" class="M"></path><path d="M586 824l2 2v1c2 1 1 2 3 3 1 0 2 1 2 2s0 3 1 4l1 1v2c1 1 2 1 2 2v3h1v-1l1 2c1 0 0 0 2-1h1v1c-1 1-3 2-4 3-2 0-3 1-4 2l-2 1-1 1h-2l-1 1-1-1h0l2-2v-1l1-1c0-1-4-5-6-6-1-2-2-3-3-5h0c0-1 0-1 2-2 1 1 0 1 1 1v-2c-1-1-1-1-2-3 1 0 2-1 3-1 0-2 0-3-1-5l2-1z" class="Y"></path><path d="M590 841l-1-1 1-1h1l1 1h2l1-1c1 1 2 1 2 2v3h1v-1l1 2c1 0 0 0 2-1h1v1c-1 1-3 2-4 3l-1-2v1c-1 0-1 0-2 1-1 0-2 1-4 1v-1c1-1 3-1 3-2-1-2-1-1-2-2s-2-2-2-3z" class="B"></path><path d="M586 824l2 2v1c2 1 1 2 3 3 1 0 2 1 2 2s0 3 1 4l1 1v2l-1 1h-2l-1-1h-1l-1 1 1 1-2 2c-1-1-1-1-1-2l-2-1v-1c1 0 1 0 1-1l-1-2v-1l-1-1c-1-1-1-1-2-3 1 0 2-1 3-1 0-2 0-3-1-5l2-1z" class="F"></path><path d="M571 796c0 2 1 3 1 5v1 3c0 2-1 5 1 7l1 1-2 2c2 0 3 0 4 1h0-3l-2 2 2 2h0 2 1 2c0 1 0 1 1 2v1h1 2l2 2c1 2 1 3 1 5-1 0-2 1-3 1 1 2 1 2 2 3v2c-1 0 0 0-1-1-2 1-2 1-2 2h0c1 2 2 3 3 5 2 1 6 5 6 6l-1 1-6-6c-1 0-1 0-2-1-1-2-3-3-4-5-4-5-8-10-10-16 0-1 0-1-1-1l-2 1h-4v-1l2-1-1-5c2 0 3 0 4 1l1 2v1h2v-1-3l1 1 1 1v-1c-1-1-1-3-1-5l-1-11h0c1 0 1-1 1-1l2-2z" class="f"></path><path d="M561 814c2 0 3 0 4 1l1 2-1 1-3 1-1-5z" class="S"></path><path d="M572 815h-1v-3c-1-2 0-3 0-5 0-1-1-1-1-2 0-2 0-2 2-3v3c0 2-1 5 1 7l1 1-2 2z" class="q"></path><path d="M577 833l-3-6c0-1 0-1-1-2 0-2-2-6-3-7h-1v-1l4-1-2 2 2 2h0 2 1l-1 1 2 2c0 1 0 2 1 3h0-1v2 2l1-1c0 2 0 2-1 4z" class="Y"></path><path d="M575 821l2 2c0 1 0 2 1 3h0-1l-3-1c0-1 1-3 1-4z" class="I"></path><path d="M576 820h2c0 1 0 1 1 2v1h1 2l2 2c1 2 1 3 1 5-1 0-2 1-3 1 1 2 1 2 2 3v2c-1 0 0 0-1-1-2 1-2 1-2 2h0l-2-2-2-2c1-2 1-2 1-4l-1 1v-2-2h1 0c-1-1-1-2-1-3l-2-2 1-1z" class="s"></path><path d="M578 829h1c1 1 2 2 2 3v1l-2 2-2-2c1-2 1-2 1-4z" class="J"></path><path d="M580 823h2l2 2c1 2 1 3 1 5-1 0-2 1-3 1v-1c0-1 0-2-1-2v-3l-1-2z" class="Q"></path><path d="M563 778l1 2h2 0l2 1h0c-1 1-2 1-2 2-1 0 0 0 0 1h0 2c1 0 2 0 2 1s1 2 1 2c1 1 3 2 4 3v6h-1l-2 5c0-2-1-3-1-5l-2 2s0 1-1 1h0l1 11c0 2 0 4 1 5v1l-1-1-1-1v3 1h-2v-1l-1-2c-1-1-2-1-4-1h0v-4-6-1c-1-1-1-1 0-2h0c-1-2-2-4-2-6 0-3 0-5-1-7v-1c-2 1-3 1-4 2-1-1-2-1-3-1-2 0-3 1-5 2v-2c2 0 3-1 4-2l1-1c1 0 2 0 4-1v-3h1v2h1c1 0 2-1 3-1l2-2 1-2z" class="d"></path><path d="M570 788h0c0 1 1 2 2 3l1-1 1 1c0 2-2 2-3 4h0c-1-1-1 0-2-1l1-1c1-2 0-3 0-5z" class="b"></path><path d="M567 790c1 1 1 2 1 4h1c1 1 1 0 2 1h0v1l-2 2s0 1-1 1h0c-1-3-1-6-1-9z" class="R"></path><path d="M567 790v-1c0-1 0-3 1-4 0 1 1 2 2 3 0 2 1 3 0 5l-1 1h-1c0-2 0-3-1-4z" class="U"></path><path d="M563 798v-7-2c1-1 1-1 3-1l1 13c-2 1-2 1-4 1v-4z" class="t"></path><path d="M567 801l1 13v3 1h-2v-1l-1-2-2-13c2 0 2 0 4-1z" class="N"></path><path d="M558 787c1 0 3 0 4 1 0 3 0 7 1 10v4l2 13c-1-1-2-1-4-1h0v-4-6-1c-1-1-1-1 0-2h0c-1-2-2-4-2-6 0-3 0-5-1-7v-1z" class="e"></path><path d="M563 778l1 2h2 0l2 1h0c-1 1-2 1-2 2-1 0 0 0 0 1-1 1-1 3 0 4-2 0-2 0-3 1v2 7c-1-3-1-7-1-10-1-1-3-1-4-1-2 1-3 1-4 2-1-1-2-1-3-1-2 0-3 1-5 2v-2c2 0 3-1 4-2l1-1c1 0 2 0 4-1v-3h1v2h1c1 0 2-1 3-1l2-2 1-2z" class="N"></path><path d="M551 788c3-2 7-5 11-4v4c-1-1-3-1-4-1-2 1-3 1-4 2-1-1-2-1-3-1z" class="O"></path><path d="M564 821l2-1c1 0 1 0 1 1 2 6 6 11 10 16 1 2 3 3 4 5 1 1 1 1 2 1l6 6v1l-2 2h0l1 1 1-1h2l2 2-2 1c1 2 1 3 1 5v1s-1 1-1 2h0c-1-1-2-1-4-1 0-1-1-3-1-4-1-1 0-2 0-4-2 1-4 3-5 4l-2 1-4 3c-1 0-2 2-3 2l-1-1-2-2c1-2 1-4 0-6l-1-1c0-1 0-1 1-2l-1-2h2l1-1h2c0-2 0-1 1-3l-1-1c-1-1 0-2-1-3l-1-1c-1-1-1-2-2-3v-1-4l-5-10v-2z" class="M"></path><path d="M569 833c3 5 6 9 10 13 1 1 4 3 5 4-1 1-1 2-1 3v1 1l-1-1v-1c0-3-3-3-5-5h0-2s-1 1-2 1h0c0-2 0-1 1-3l-1-1c-1-1 0-2-1-3l-1-1c-1-1-1-2-2-3v-1-4z" class="F"></path><path d="M573 845h3v1s1 1 1 2h-2s-1 1-2 1h0c0-2 0-1 1-3l-1-1z" class="I"></path><path d="M575 848h2 0c2 2 5 2 5 5v1l1 1v-1-1c0-1 0-2 1-3 1 1 1 1 1 3h1v1c-2 1-4 3-5 4l-2 1-4 3c-1 0-2 2-3 2l-1-1-2-2c1-2 1-4 0-6l-1-1c0-1 0-1 1-2l-1-2h2l1-1h2 0c1 0 2-1 2-1z" class="r"></path><path d="M571 849h2 0v1c-1 1-1 1-1 2l-2-2 1-1z" class="Z"></path><path d="M575 848h2 0c2 2 5 2 5 5v1l1 1h-1l-1-1h-1-2-1-1c0-1 1-2 0-3v-2l-1-1z" class="L"></path><path d="M571 863c0-2 0-4 1-6v-1c-1 0-2-1-3-2l2-1h2c0 1 0 2 1 3v-1h1 1v1h-1c-2 0-2 1-3 2v1c2 1 1-1 3-1v4c-1 0-2 2-3 2l-1-1z" class="v"></path><path d="M609 779c-1 1-1 1-1 2h-1c-3 1-5 3-7 5v1c0 1-1 3-2 5h-1c-1 1-1 0-1 1-1 3 1 5 0 8l2 2v1h-1v1l-1 1c0-1-1-2-2-2h0v2h-4v2h-1v-1h-1c-2 0-2-2-2-2h-2l-1 1v4h0l-1 1v1l1 2 1 2h-4-1-3 0c-1-1-2-1-4-1l2-2-1-1c-2-2-1-5-1-7v-3-1l2-5h1v-6c-1-1-3-2-4-3 0 0-1-1-1-2s-1-1-2-1h-2 0c0-1-1-1 0-1 0-1 1-1 2-2 1 1 2 1 3 1h7v-1l3 1 2 2h0c4-1 10 0 13-2h1 2c2 0 4-1 6-2 1-1 2-1 4-1z" class="q"></path><path d="M594 796l1 2h-1v1l-1 1v1l-1-1s-1-1-1-2l-1 1c0 1 0 2-1 3h-1c-1-1-1-2 0-3l1 1v-1-2h1l1 1c1-1 2-1 3-2z" class="T"></path><path d="M583 799l3-1v5h1s1 1 1 2l2-2h2v1l-1 1c1-1 2-1 2-2l1 1h0v2h-4v2h-1v-1h-1c-2 0-2-2-2-2h-2v-2c-1-1-1-2-1-4z" class="m"></path><path d="M589 789c2 0 2 0 4 1 0 1-1 2-1 3h0v3l1-1 1 1c-1 1-2 1-3 2l-1-1h-1v2 1l-1-1-1-4c-1 0-1 0-2-1 0-2 0-1 1-2l1-1c0-1 1-2 2-2z" class="T"></path><path d="M587 795l1-1 1 1h0v-1l1-1c1 1 1 2 2 3l1-1 1 1c-1 1-2 1-3 2l-1-1h-1v2 1l-1-1-1-4z" class="E"></path><path d="M609 779c-1 1-1 1-1 2h-1c-3 1-5 3-7 5v1c0 1-1 3-2 5h-1c-1 1-1 0-1 1-1 3 1 5 0 8-1 0-2 0-3-1l1-1v-1h1l-1-2-1-1-1 1v-3h0c0-1 1-2 1-3-2-1-2-1-4-1 1-1 2 0 3-1-2-1-4 0-6-2h4 0c1 0 2 1 2 1 2-2 2-2 4-2 1 0 1 1 2 1l1-2-2-2h2c2 0 4-1 6-2 1-1 2-1 4-1z" class="P"></path><path d="M592 788c1 0 2 0 2 1 1-1 1-1 1-2h1c0 1 0 1-1 2l1 3-3 2v1l-1 1v-3h0c0-1 1-2 1-3-2-1-2-1-4-1 1-1 2 0 3-1z" class="b"></path><path d="M578 781l3 1 2 2h0c4-1 10 0 13-2h1l2 2-1 2c-1 0-1-1-2-1-2 0-2 0-4 2 0 0-1-1-2-1h0-4-1v1c0 2-1 2-2 4v8c0 2 0 3 1 4v2l-1 1v4h0l-1 1c-2-7-2-16-3-23v-2c1-2 1-2 1-4h-2v-1z" class="o"></path><path d="M571 782h7 2c0 2 0 2-1 4v2c1 7 1 16 3 23v1l1 2 1 2h-4-1-3 0c-1-1-2-1-4-1l2-2-1-1c-2-2-1-5-1-7v-3-1l2-5h1v-6c-1-1-3-2-4-3 0 0-1-1-1-2s-1-1-2-1h-2 0c0-1-1-1 0-1 0-1 1-1 2-2 1 1 2 1 3 1z" class="F"></path><path d="M583 814h-5l-1-1h2l-1-1 1-2h1c0 1 1 1 1 2h1l1 2z" class="G"></path><path d="M571 782h7 2c0 2 0 2-1 4v2c-2-1-3-2-5-3h-1l-1-1c-1-1-1-1-1-2z" class="O"></path><path d="M573 812v-5c2-3 0-3 1-5l3-1c0 2 0 3 1 5l1 2v1h-1c-1 0-1-1-2-2-1 0-1 1-2 2l1 1-1 3-1-1z" class="Z"></path><path d="M570 785c2 1 3 1 5 2h0c1 2 3 3 2 4v1c-1 2 0 6 0 9l-3 1c-1 2 1 2-1 5v5c-2-2-1-5-1-7v-3-1l2-5h1v-6c-1-1-3-2-4-3 0 0-1-1-1-2z" class="y"></path><path d="M609 779c1-1 1-1 2-1l2-2 1 1h0c-2 2-3 2-3 5 0 1 0 1 2 1l-1-1v-1c1-1 2 0 3 0 2-1 2-2 4-3v2 1h1l1 1v2l1 1c1 1 1 4 2 6 1 3 2 4 5 6l-2 1c-1-1-2-2-3-2h-1c-1-1 0-1-1-1l1-2c-1-1-1-1-2-1s-2-1-2-1l-1 1v2c0 1 1 2 1 3-1 1-2 3-3 3v1h1c3 6 8 11 11 17h0l1 2h0c2 0 3 1 4 0s3 0 4 0h0c-2 1-3 1-5 2h-3-1-1c-1-1-1-1-3-2 1 1 1 2 1 3h-1c-1 0-3-2-4-3v-1c-1-2-4-3-6-4l-7-2h-7v-1h3c0-1-1-1-1-2v-1c0-2 0-2-1-3s-2-2-2-4l2-1h-1c-2-3-3-5-3-9h1c1-2 2-4 2-5v-1c2-2 4-4 7-5h1c0-1 0-1 1-2z" class="a"></path><path d="M601 806c-1-1-2-2-2-4l2-1 3 2c1 1 3 1 5 1l2-2h1c0 1 0 2 1 3 0 0 1 0 1 1 1 1 2 2 3 2v1h-1l-3-2h-1v1c1 1 2 1 3 2 0 0 0 2 1 3v-1h1l1 1c-1 1 0 1-1 1s-2 0-3 1l-7-2h-7v-1h3c0-1-1-1-1-2v-1c0-2 0-2-1-3z" class="M"></path><path d="M609 779c1-1 1-1 2-1l2-2 1 1h0c-2 2-3 2-3 5 0 1 0 1 2 1l1 1 1-1h0l2 2c0 2 1 2 2 3 0 1 1 1 1 2l-1 1-1-1c0 1-1 1-1 2 0 2 1 3 0 5l-4 4c-1 1-1 0-1 1h-1l-2 2c-2 0-4 0-5-1l-3-2h-1c-2-3-3-5-3-9h1c1-2 2-4 2-5v-1c2-2 4-4 7-5h1c0-1 0-1 1-2z" class="b"></path><path d="M600 787c2-1 3-3 5-3 2-1 4-1 5 1v1h-1c-1 0-3 0-4 1l-1 2c-1 0-2 1-3 1l-1 3h-2v-1c1-2 2-4 2-5z" class="c"></path><path d="M609 779c1-1 1-1 2-1l2-2 1 1h0c-2 2-3 2-3 5 0 1 0 1 2 1l1 1 1-1h0l2 2c0 2 1 2 2 3 0 1 1 1 1 2l-1 1-1-1c0 1-1 1-1 2h0l-1-1c-2-2-3-5-6-6-1-2-3-2-5-1-2 0-3 2-5 3v-1c2-2 4-4 7-5h1c0-1 0-1 1-2z" class="B"></path><path d="M607 788h3 2l1 2c1 1 1 1 2 1h1l1 1h0c0 2 1 3 0 5l-4 4c-1 1-1 0-1 1h-1l-2 2c-2 0-4 0-5-1v-1l-1-1c-2-2-1-1-1-2s-1-2-1-3h1c1 2 2 4 5 5 2 0 3-1 5-2h0-5c-1-1-3-2-4-4v-4c1-2 2-2 4-3z" class="o"></path><path d="M607 788h3c1 1 1 2 2 4l-1 2c-1 1-1 1-2 1s-2 0-3-1c1-1 2-1 2-2 0-2 0-2-1-2v-2z" class="H"></path><path d="M646 828v3c1 0 2 1 2 0h1l1 1 2-1c1 1 1 2 2 3 0 1 1 1 1 2-1 1-2 1-2 1v4h1l1-1h0v-2h0l1 1h1 1l1-2 5 5v1l-1 1v3l-5 29-2-1c-10-1-21-2-31-6-2 0-5-1-7-1l-1-2 1-1c-8-3-14-7-21-12l-3-1 1-1 3-2v2h0 3c1 2 4 2 6 2l1-1 2 2 2-2c-1-1-1-2-1-3l3-3c1 0 2 0 3-1l1 1h1l-2-1v-3l2 1c1-1 0-1 1-2l-1-1h0c2 1 3 1 5 2 1 0 1 0 2 1-1 1-1 2 0 3 1-1 3-1 4-2l2-2h1l2-3v-2l1 1h2v1c-1 1-1 2 0 4 1 0 1 1 2 1l1-1v-1c1-1 1-3 2-4h1l1-1-1-1c0-1 0-1 1-1 0-1-1-3 0-4 0-1 0-2 1-3z" class="v"></path><path d="M618 865c3 1 4 3 8 2h2 2v-1-1l2-2 3 6c-3-1-6-1-9-1l-1 1c-2 0-5-1-7-1l-1-2 1-1z" class="T"></path><path d="M635 850c2 0 3-1 4-2l1-2c1-2 5-1 7-1-2 2-4 5-6 6h-2c-2 0-2 0-4-1z" class="I"></path><path d="M628 853l1 1-3 6c-1 1-1 1-1 2h-1-1c-1-2-2-3-3-3h-1-3c2-1 4-1 5-1l1 1v-2h1v-2c1 0 2 0 3 1 1-1 2-2 2-3z" class="l"></path><path d="M648 831h1l1 1 2-1c1 1 1 2 2 3l-1 1c-1 0-2-1-2-2h-1c0 2 1 3 2 4l-1 2c0 1-1 1-1 2v1l-3 3v-6-5c2 0 1 0 2-1l-1-2zm-23 16c1 0 2 1 3 2l1 1h0l-1 3c0 1-1 2-2 3-1-1-2-1-3-1v2h-1v2l-1-1c-1 0-3 0-5 1h-1c0-1-1-2-1-2h-2v-1l1-1h0c1 0 1 0 2-1h0 1c1 1 3 1 4 0 1-2-1-2-1-4 1 0 2 0 3 1l2 1c0-1 1-1 1-2s0-1-1-2l1-1z" class="Y"></path><path d="M625 847c1 0 2 1 3 2l1 1h0c-1 0-1 0-2 1l-2 2-1-1h0c0-1 1-1 1-2s0-1-1-2l1-1z" class="I"></path><path d="M613 855c2 1 4 1 6 1 1 0 2 1 3 1v2l-1-1c-1 0-3 0-5 1h-1c0-1-1-2-1-2h-2v-1l1-1z" class="m"></path><path d="M644 852h4c-2 1-3 1-4 2-3 1-6 5-9 7h-1c-2 1-3 2-5 2h-1c1-1 2-2 2-3l-2-1 1-2 3-5h5 7z" class="b"></path><path d="M629 857s2 0 2-1c1 0 1-1 2-1 1-1 2 0 4 0 1-1 2-1 3-1v1c-1 2-4 3-6 6-2 1-3 2-5 2h-1c1-1 2-2 2-3l-2-1 1-2z" class="E"></path><path d="M614 846c1 0 2 0 3-1l1 1h1l-2-1v-3l2 1c1-1 0-1 1-2l-1-1h0c2 1 3 1 5 2 1 0 1 0 2 1-1 1-1 2 0 3l-1 1-1 1c1 1 1 1 1 2s-1 1-1 2l-2-1c-1-1-2-1-3-1 0 2 2 2 1 4-1 1-3 1-4 0h-1 0c-1 1-1 1-2 1-1-1-2-1-3-1l2-2c-1-1-1-2-1-3l3-3z" class="B"></path><path d="M614 846c1 0 2 0 3-1l1 1h1l-2-1v-3l2 1c1-1 0-1 1-2l-1-1h0c2 1 3 1 5 2h-2c0 2 2 2 1 4h-2l-1 1c1 1 1 1 1 2-1 1-1 1-1 0-1-1-1-2-2-2-1 1 0 2-2 2h-1c0 1 0 2-1 3l-1-1-1 1c-1-1-1-2-1-3l3-3z" class="S"></path><path d="M646 828v3c1 0 2 1 2 0l1 2c-1 1 0 1-2 1v5 6h0c-2 0-6-1-7 1l-1 2c-1 1-2 2-4 2l-4 1v-2-2c-1 1-1 2-2 3l-1-1c-1-1-2-2-3-2l1-1c1-1 3-1 4-2l2-2h1l2-3v-2l1 1h2v1c-1 1-1 2 0 4 1 0 1 1 2 1l1-1v-1c1-1 1-3 2-4h1l1-1-1-1c0-1 0-1 1-1 0-1-1-3 0-4 0-1 0-2 1-3z" class="F"></path><path d="M631 847l2-1h2v1c0 1 0 1-1 2h-3v-2z" class="e"></path><path d="M635 839v-2l1 1h2v1c-1 1-1 2 0 4l-1-1-1 1 1 2h0l-2-1-2-2 2-3z" class="C"></path><path d="M635 839c1 1 1 3 0 4v1l-2-2 2-3z" class="O"></path><path d="M659 837l5 5v1l-1 1v3l-5 29-2-1c-10-1-21-2-31-6l1-1c3 0 6 0 9 1l-3-6 3-2c3-2 6-6 9-7 1-1 2-1 4-2h-4l1-1c1-2 2-3 5-3h0v-2c1-1 2-2 4-3l1 1h0l-1-3 1-1h0v-2h0l1 1h1 1l1-2z" class="R"></path><path d="M659 837l5 5v1l-1 1v3l-5 29-2-1 5-32c-1 1-1 0-1 2-1 1-2 1-3 2 0 1-1 2-2 2-2 0-5 2-7 3h-4l1-1c1-2 2-3 5-3h0v-2c1-1 2-2 4-3l1 1h0l-1-3 1-1h0v-2h0l1 1h1 1l1-2z" class="P"></path><path d="M655 849c1-2 2-2 2-4 1-1 0-1 0-2v-2c1-1 2-1 4-1 1 1 0 2 0 3-1 1-1 0-1 2-1 1-2 1-3 2 0 1-1 2-2 2z" class="b"></path><path d="M592 696h28l14 1h1c-3 2-10 5-11 8 0 2 0 2-1 3l-1 4v1 1l-1 3c1 10 2 18 6 27l-2-1 1 2v1h-3l-9-2h-3c-1 2-2 3-2 5l1 3 3 2c-1 1-2 2-2 3l-2 1v2h1 9c1 0 2 0 3 1 2 0 5-1 7 0h4l6 1v1c-1 0-3 0-4 1l1 1c0 1 0 2 1 3l-1 1 1 3c0 1 0 2 1 3-1 1-1 0-1 1-1 2-1 2 0 4h3 1l-3 1v7c2 1 4 1 6 1-3 0-5 1-8 0-1 2 0 5-1 8l-1 2-1-1c-2 0-3 0-4-1-3-2-4-3-5-6-1-2-1-5-2-6l-1-1v-2l-1-1h-1v-1-2c-2 1-2 2-4 3-1 0-2-1-3 0v1l1 1c-2 0-2 0-2-1 0-3 1-3 3-5h0l-1-1-2 2c-1 0-1 0-2 1-2 0-3 0-4 1-2 1-4 2-6 2h-2-1c-3 2-9 1-13 2h0l-2-2-3-1v1h-7c-1 0-2 0-3-1h0l-2-1h0-2l-1-2-1 2-2 2c-1 0-2 1-3 1h-1v-2h-1v3c-2 1-3 1-4 1l-1 1c-1 1-2 2-4 2v2c-1 1-1 1-2 1v-1h-1c0-3 1-5 1-8 0-1-1-2-1-3s1-2 1-4c0-1-1-2-1-3 0-2 1-6 0-8-1-1-1-1-1-2s2-2 2-2v-1c0-1 0-1-1-2h0c-1-1-1-2-1-3h2c0-1 0-2 1-2 1-1 1-2 2-3 0-2 1-3 2-5v-1l-3 1v-2c1-2 2-3 1-5 0-2-1-3-3-4h1v-2l1-1 1-1c0-1 0-2 1-3l1-6v-1 1l1-1c2 1 2 0 4 0l1-1 1-1c3-1 5-2 8-3 2-2 2-2 2-4 0-1-1-2-2-2-2-1-2 0-4 0h0v-3l3-2 3-2 4-2c1-1 2-1 4-2 3-1 6-1 9-1h9z" class="o"></path><path d="M585 771c1-1 2 0 3-1v-2h3c0 1-1 2-2 4h0l-2 2c-1 0-1 0-2-1v2l-1 1h1 2l1 1-2 1h-3c1-1 1-1 1-2l1-5z" class="p"></path><path d="M602 767v-1h1c1 1 1 1 1 2l1 1h0c-1 1-2 1-3 2-1 0-1 0-2 1s-2 1-3 2h0c1 1 0 2 0 3v1c-3 0-6 1-9 1l2-2h2c1-1-1-1-1-2l1-1 1 2h1c1-1 2-2 3-4s4-2 5-5z" class="Z"></path><path d="M572 771h1l2-2v1h3l1 1h0l-2 1v3l1 2v1h0l-2 1h-4c-1 0-1 0-2 1h5 4-1c-1 1-2 1-3 1h0 3v1h-7c-1 0-2 0-3-1h0l-2-1c0-1 0-1 1-2 1 1 1 1 3 1v-2l1-1 1 1v-1-4-1z" class="m"></path><path d="M576 779v-1l-1-1c-1-2-1-3 0-5h2v3l1 2v1h0l-2 1z" class="h"></path><path d="M567 761h0c0 2 1 2 2 4v2l1 1 2-1v4 1 4 1l-1-1-1 1v2c-2 0-2 0-3-1-1 1-1 1-1 2h0-2l-1-2c-1-1-1-3 0-5v-5c1 0 1-1 1-1h2c0-2 0-4 1-6z" class="p"></path><path d="M563 768c1 0 1-1 1-1h2c0 1 1 3 1 4h-1v1 5 3h-2l-1-2c-1-1-1-3 0-5v-5z" class="t"></path><path d="M621 762c0 2-1 3-3 5-3 3-4 7-8 9-1 1-2 2-3 2h-1c-8 3-17 2-25 4l-3-1h-3 0c1 0 2 0 3-1h1c2-1 1-2 2-4v-1l1-1c0 1 0 2-1 3v2c2 1 5 0 7 0 3 0 6-1 9-1v-1c0-1 1-2 0-3h0c1-1 2-1 3-2s1-1 2-1c1-1 2-1 3-2h0 0c1-2 2-2 4-3 0 1-1 2-1 3l-2 1 1 1h2c1-1 1-3 2-3h1l2 2 6-7 1-1z" class="E"></path><path d="M605 769h0c1-2 2-2 4-3 0 1-1 2-1 3l-2 1 1 1h2c1-1 1-3 2-3h1l2 2c-5 5-10 7-17 8v-1c0-1 1-2 0-3h0c1-1 2-1 3-2s1-1 2-1c1-1 2-1 3-2h0z" class="Q"></path><path d="M621 762l1-1h3v1c0 2-1 2-2 4l-1 1c-1 1-1 2-1 3l1 2 1-1s1 0 2-1h2 2c1 1 0 2 0 3v2l-3-2v1 2c-1 0-2 0-2 1-1 1-1 1-1 3l-1 1-1 1-1-1h-1v-1-2c-2 1-2 2-4 3-1 0-2-1-3 0v1l1 1c-2 0-2 0-2-1 0-3 1-3 3-5h0l-1-1-2 2c-1 0-1 0-2 1-2 0-3 0-4 1-2 1-4 2-6 2h-2-1c-3 2-9 1-13 2h0l-2-2c8-2 17-1 25-4h1c1 0 2-1 3-2 4-2 5-6 8-9 2-2 3-3 3-5z" class="C"></path><path d="M619 780c1-1 2-1 2-2v-1c0-1 0-1 1-2 0-1 3-2 4-2v1 2c-1 0-2 0-2 1-1 1-1 1-1 3l-1 1-1 1-1-1h-1v-1z" class="F"></path><path d="M610 760h9c1 0 2 0 3 1 2 0 5-1 7 0h4l6 1v1c-1 0-3 0-4 1l1 1c0 1 0 2 1 3l-1 1 1 3c0 1 0 2 1 3-1 1-1 0-1 1h-1l-1-1h-1v2c-2-1-3-2-5-2v-2c0-1 1-2 0-3h-2-2c-1 1-2 1-2 1l-1 1-1-2c0-1 0-2 1-3l1-1c1-2 2-2 2-4v-1h-3l-1 1-1 1-6 7-2-2h-1c-1 0-1 2-2 3h-2l-1-1 2-1c0-1 1-2 1-3l2-2 1-1-2-3z" class="H"></path><path d="M612 768c2-1 2-1 2-2s0-2 1-3c1 1 3 1 4 0h1l-6 7-2-2z" class="B"></path><path d="M637 768l-1 1 1 3c0 1 0 2 1 3-1 1-1 0-1 1h-1l-1-1h-1v2c-2-1-3-2-5-2v-2l1 2 1-1c1-1 1 0 3 0 0 0 1 0 2 1h0 1v-2l-1-4 1-1z" class="S"></path><path d="M626 773l3 2c2 0 3 1 5 2v-2h1l1 1h1c-1 2-1 2 0 4h3 1l-3 1v7c2 1 4 1 6 1-3 0-5 1-8 0-1 2 0 5-1 8l-1 2-1-1c-2 0-3 0-4-1-3-2-4-3-5-6-1-2-1-5-2-6l-1-1v-2l1-1 1-1c0-2 0-2 1-3 0-1 1-1 2-1v-2-1z" class="e"></path><path d="M630 782l2-1c1 0 2 1 3 2-1 2 0 5-1 6l-1 1c0-1-2-2-3-2v-3-3z" class="G"></path><path d="M630 785h4v4l-1 1c0-1-2-2-3-2v-3z" class="M"></path><path d="M626 773l3 2c2 0 3 1 5 2l-1 2c1 1 1 2 2 4-1-1-2-2-3-2l-2 1c-2 1-2 2-3 4 0 2 1 3 2 5-1 0-1 1-2 1l-1-2-1 1h-1c-1-2-1-5-2-6l-1-1v-2l1-1 1-1c0-2 0-2 1-3 0-1 1-1 2-1v-2-1z" class="k"></path><path d="M626 773l3 2c2 0 3 1 5 2l-1 2c-1-1-2-1-3-1-2 1-3 3-4 6-2 0-1 0-2-1s-1-1-1-2h-1l1-1c0-2 0-2 1-3 0-1 1-1 2-1v-2-1z" class="I"></path><path d="M601 759c2 0 4 0 6-1 3-1 0-11 2-14h2c-1 2-2 3-2 5l1 3 3 2c-1 1-2 2-2 3l-2 1v2h1l2 3-1 1-2 2c-2 1-3 1-4 3h0l-1-1c0-1 0-1-1-2h-1v1c-2 0-2 0-4-1v1l-1-2h-1l-3 1v1h2c0 1 0 1-1 1-1 1-3 3-5 4 1-2 2-3 2-4h-3v2c-1 1-2 0-3 1v-2h-1c-1 2-1 3-2 4-1-1 0-2 0-3l-2-2 1-1-1-2c-1 2-4 2-6 2s-3-1-5-2c-1-2-2-2-2-4h0-2l-1 1v-1l1-5v2c1 1 2 1 3 0 2 2 5 1 6 1l2-2 1-1c1 0 1 0 2-1v-1h1c0-2 1-2 1-3 1-1 2-1 3-1h1c0 1 1 3 2 3h1 1 1l2-5 2 1 2-1h0c0 1 1 1 2 2h2c0 2-1 2-3 3l3 2 3 2-2 2z" class="k"></path><path d="M610 752l3 2c-1 1-2 2-2 3l-2 1c0-2 1-4 1-6zm-14 9c1 2 1 2 4 2-2 0-5 1-7 1v-3h3z" class="D"></path><path d="M577 756c1 0 1 0 2-1 0 1 1 2 1 3l-1 1h-5l2-2 1-1z" class="L"></path><path d="M582 758l1-1v-1h2c1 1 2 2 2 3h-8l1-1h2z" class="D"></path><path d="M583 755c0 1 1 0 2 1v-2h1l2 2 2-2v1 1c-1 1-1 2-1 3h-2c0-1-1-2-2-3h-2v1l-1 1v-3h1z" class="I"></path><path d="M577 760l7 1h-1c-1 2-1 4-1 6v3l-2-2 1-1-1-2c1-1 1 0 0-1s-2-2-2-3l-1-1z" class="C"></path><path d="M596 761c3 0 7 0 11-1h0c-2 3-4 2-7 3-3 0-3 0-4-2z" class="F"></path><path d="M584 769c0-3 1-5 1-8h8v3c-2 0-5 0-7 1 0 1-1 3-1 4h-1z" class="K"></path><path d="M575 761l2-1 1 1c0 1 1 2 2 3s1 0 0 1c-1 2-4 2-6 2l-1-2 1-2c0-1 0-1 1-2z" class="H"></path><path d="M581 751c1-1 2-1 3-1h1c0 1 1 3 2 3h1l-2 1h-1v2c-1-1-2 0-2-1h-1v3h-2c0-1-1-2-1-3v-1h1c0-2 1-2 1-3z" class="J"></path><path d="M581 751h2v2c-1 0-2 0-3 1 0-2 1-2 1-3z" class="D"></path><path d="M580 754c1-1 2-1 3-1v2h-1v3h-2c0-1-1-2-1-3v-1h1z" class="F"></path><path d="M609 760h1l2 3-1 1-2 2c-2 1-3 1-4 3h0l-1-1c0-1 0-1-1-2h-1v1c-2 0-2 0-4-1 2-1 5-2 7-3h2c1-1 2-2 2-3z" class="D"></path><path d="M604 768c2-1 1-1 2-2 0-1 1-2 2-2 1-1 2 0 3 0l-2 2c-2 1-3 1-4 3h0l-1-1z" class="J"></path><path d="M565 756v2c1 1 2 1 3 0 2 2 5 1 6 1l1 2c-1 1-1 1-1 2l-1 2 1 2c-2 0-3-1-5-2-1-2-2-2-2-4h0-2l-1 1v-1l1-5z" class="c"></path><path d="M567 761c2 0 2 1 3 1s1-1 3 0l1 1-1 2 1 2c-2 0-3-1-5-2-1-2-2-2-2-4z" class="C"></path><path d="M594 749l2-1h0c0 1 1 1 2 2h2c0 2-1 2-3 3l3 2 3 2-2 2h-8-4c0-1 0-2 1-3v-1-1l-2 2-2-2 2-1h1 1l2-5 2 1z" class="D"></path><path d="M594 749l2-1h0c0 1 1 1 2 2h2c0 2-1 2-3 3l-1 1-2 1-1-1v-1c-1-1 0-3 1-4z" class="a"></path><path d="M593 754c1-2 1-2 2-3 1 1 1 2 1 3l-2 1-1-1z" class="x"></path><path d="M596 754l1-1 3 2 3 2-2 2h-8c0-1 0-2-1-2v-1-1h1 1l2-1z" class="S"></path><path d="M617 716c1-1 2-2 3-2h1 1l-1 3c1 10 2 18 6 27l-2-1 1 2v1h-3l-9-2h-3-2c-2 3 1 13-2 14-2 1-4 1-6 1l2-2-3-2-3-2c2-1 3-1 3-3h-2c-1-1-2-1-2-2h0l-2 1-2-1-2 5h-1-1-1c-1 0-2-2-2-3h-1v-2c1 0 2 0 3-1 0-1-1-2 0-3v-1l-2-1c1 0 2-2 2-3 1-1 1-1 2-1 1 1 1 1 1 2l1 1 1-3-2-2h2c1 0 1-1 2-1-1-1-1-2-1-3l1-2c1 0 1 0 1-1h1 2l2-1h2 1c1 1 2 1 3 2 0 1 0 1 1 2h1c1-1 2-1 2-2s0-2-1-2c0-2 0-3-1-4l2-1v-2c0-2 0-2-1-3l2 1v2l1 1c2-2 3-4 4-5h1v-1z" class="J"></path><path d="M598 750c1-2 0-2 2-4 1 1 1 2 1 3 0 0 0 1-1 1h-2z" class="B"></path><path d="M617 716c1-1 2-2 3-2h1 1l-1 3-1-2h-1l-1 1 1 1c-1 0-2 1-2 2s0 3-1 4h0 0c0-1-1-2-1-3l-1 1c0 1-1 1-1 2l-1-1c2-2 3-4 4-5h1v-1z" class="Q"></path><path d="M603 751v-7-3h0v-4h1 1c0 1 1 1 1 2v1c1 3 1 6 1 9v1c-1 1-1 2-3 2h0l-1-1z" class="Z"></path><path d="M594 730c1 0 1 0 1-1h1 2l2-1h2c-1 2-1 3-1 5l1 1c1 0 1-1 2-2h-1l1-1 1 1v2c-1 2-2 3-3 4s-1 2-2 3v3h-1c-1-1-1-2-2-3v-1l-2-2c-2-1-2 0-3 0l-2-2h2c1 0 1-1 2-1-1-1-1-2-1-3l1-2z" class="s"></path><path d="M607 749c1-2 0-5 0-7 1-1 2-1 3-1l1-1 3 3v-1-1-1c2 0 2 0 3 1v-1l2 2c0-1 0-2 1-3 0-1 0-1 2-2v1c1 2 2 3 2 5h1l1 2v1h-3l-9-2h-3-2c-2 3 1 13-2 14-2 1-4 1-6 1l2-2-3-2c2-1 2-3 3-4l1 1h0c2 0 2-1 3-2v-1z" class="e"></path><path d="M614 744h1c1-1 1 0 2-1l1-1v1c2 1 4 0 6 0h1l1 2v1h-3l-9-2z" class="O"></path><path d="M607 750c0 2 0 4-1 6l-1 1h-2l-3-2c2-1 2-3 3-4l1 1h0c2 0 2-1 3-2z" class="L"></path><path d="M592 738c1 0 1-1 3 0l2 2v1c1 1 1 2 2 3h1l1 1-1 1v-1h0v1h0c-2 2-1 2-2 4-1-1-2-1-2-2h0l-2 1-2-1-2 5h-1-1-1c-1 0-2-2-2-3h-1v-2c1 0 2 0 3-1 0-1-1-2 0-3v-1l-2-1c1 0 2-2 2-3 1-1 1-1 2-1 1 1 1 1 1 2l1 1 1-3z" class="J"></path><path d="M585 750l1-1 3 3v1h-1-1c-1 0-2-2-2-3z" class="L"></path><path d="M600 744l1 1-1 1v-1h0v1h0c-2 2-1 2-2 4-1-1-2-1-2-2h0l-2 1-2-1s0-1 1-2h4v2h1c0-1 1-2 1-4h1z" class="I"></path><path d="M587 739c1-1 1-1 2-1 1 1 1 1 1 2l1 1h1c-1 1-1 3-1 4-1-1-1-2-1-2h-1c-1 1-1 1-1 2v3l-2 1-1 1h-1v-2c1 0 2 0 3-1 0-1-1-2 0-3v-1l-2-1c1 0 2-2 2-3z" class="Z"></path><defs><linearGradient id="X" x1="565.61" y1="736.526" x2="571.744" y2="738.063" xlink:href="#B"><stop offset="0" stop-color="#cac9ca"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#X)" d="M566 710s1 1 1 2-1 3-1 5h2c1-1 0-2 0-3l2-2v-3l1 2c1 2 1 4 1 6v2c1 0 2 1 2 2 1 1 2 1 3 2v-1c2-1 4-2 7-2h1c1 2 2 2 4 2l1-1h0c1 1 1 1 2 1v1c1 1 0 1 1 1h1l1 2h0l-2 1c0 1 0 2 1 2v1l-1 2c0 1 0 2 1 3-1 0-1 1-2 1h-2l2 2-1 3-1-1c0-1 0-1-1-2-1 0-1 0-2 1 0 1-1 3-2 3l2 1v1c-1 1 0 2 0 3-1 1-2 1-3 1v2c-1 0-2 0-3 1 0 1-1 1-1 3h-1v1c-1 1-1 1-2 1l-1 1-2 2c-1 0-4 1-6-1-1 1-2 1-3 0v-2l-1 5h-1v-1h-1l-1-1 2-8c0-4 1-8 2-12v-5l-1-1 1-1v-4c1-4 0-9 0-12l-1-1v-1c2-2 2-2 2-4z"></path><path d="M572 721l1 1c0 2 0 3 2 4-1 1-1 2-1 3 1 0 1 1 1 0 1 0 2-1 3-2l1 1c0 1-1 2-1 3h-2v1h2c0 1-1 2-2 3-1 0-1 1-2 2 0 1 1 2 1 2l1 1-2 2s0 1 1 2l-2 4 2 2c-1 3-2 3-1 6l2 1-2 2c-1 0-4 1-6-1v-1l4-22c1-5 0-9 0-14z" class="h"></path><path d="M572 735c2 2 1 4 1 7 0 1-1 1 0 1 1 3 0 5-1 8-1 1-1 3-1 5v1h0-3l4-22z" class="b"></path><defs><linearGradient id="Y" x1="565.595" y1="717.547" x2="568.172" y2="739.748" xlink:href="#B"><stop offset="0" stop-color="#1c1c1a"></stop><stop offset="1" stop-color="#494a43"></stop></linearGradient></defs><path fill="url(#Y)" d="M566 710s1 1 1 2-1 3-1 5h2c1-1 0-2 0-3l2-2c-1 7-1 13-1 19-1 4-2 8-2 12l-2 13-1 5h-1v-1h-1l-1-1 2-8c0-4 1-8 2-12v-5l-1-1 1-1v-4c1-4 0-9 0-12l-1-1v-1c2-2 2-2 2-4z"></path><defs><linearGradient id="Z" x1="578.593" y1="744.046" x2="582.352" y2="733.945" xlink:href="#B"><stop offset="0" stop-color="#595957"></stop><stop offset="1" stop-color="#6f6f6d"></stop></linearGradient></defs><path fill="url(#Z)" d="M582 734h1l1-1 1 1h0v1h-1l2 2s0 1 1 1v1h0c0 1-1 3-2 3l2 1v1c-1 1 0 2 0 3-1 1-2 1-3 1v2c-1 0-2 0-3 1 0 1-1 1-1 3h-1v1c-1 1-1 1-2 1l-1 1-2-1c-1-3 0-3 1-6l-2-2 2-4c-1-1-1-2-1-2l2-2-1-1s-1-1-1-2c1-1 1-2 2-2l2 1c2-1 2 0 4-2z"></path><g class="L"><path d="M575 739l1-1h1 0c0 2 0 3 1 4l-1 1-2 1c-1-1-1-2-1-2l2-2-1-1z"></path><path d="M585 742l2 1v1c-1 1 0 2 0 3-1 1-2 1-3 1v2c-1 0-2 0-3 1 0 1-1 1-1 3h-1v1c-1 1-1 1-2 1l-1 1-2-1c-1-3 0-3 1-6l-2-2 2-4 2-1v1h1c1 0 1 1 2 2l-1 1 1 1 1-1 1 1c2-2 2-3 3-6z"></path></g><path d="M576 755v-2c2 0 2 0 3 1v1c-1 1-1 1-2 1l-1-1z" class="I"></path><path d="M577 743v1h1v5c0 1 1 1 1 2h-2c0 1-2 1-1 2v2l1 1-1 1-2-1c-1-3 0-3 1-6l-2-2 2-4 2-1z" class="Z"></path><path d="M572 719c1 0 2 1 2 2 1 1 2 1 3 2v-1c2-1 4-2 7-2h1c1 2 2 2 4 2l1-1h0c1 1 1 1 2 1v1c1 1 0 1 1 1h1l1 2h0l-2 1c0 1 0 2 1 2v1l-1 2c0 1 0 2 1 3-1 0-1 1-2 1h-2l2 2-1 3-1-1c0-1 0-1-1-2-1 0-1 0-2 1h0v-1c-1 0-1-1-1-1l-2-2h1v-1h0l-1-1-1 1h-1c-2 2-2 1-4 2l-2-1c1-1 2-2 2-3h-2v-1h2c0-1 1-2 1-3l-1-1c-1 1-2 2-3 2 0 1 0 0-1 0 0-1 0-2 1-3-2-1-2-2-2-4l-1-1v-2z" class="o"></path><path d="M590 736h0v1l-1-1 1-2c1-2-1 0-1-2l1-2v-3h1c0 2 1 3 2 5 0 1 0 2 1 3-1 0-1 1-2 1h-2z" class="y"></path><path d="M582 734l-1-2c0-1 1-2 2-3h2l-1 2v1h1 0l1-1c1 0 1 0 2 2-1 1-2 1-2 2l1 1v2c-1 0-1-1-1-1l-2-2h1v-1h0l-1-1-1 1h-1z" class="v"></path><path d="M555 718l1-1 8-3v1l1 1c0 3 1 8 0 12v4l-1 1 1 1v5c-1 4-2 8-2 12l-2 8 1 1h1v1h1v1l1-1h2c-1 2-1 4-1 6h-2s0 1-1 1v5c-1 2-1 4 0 5l-1 2-2 2c-1 0-2 1-3 1h-1v-2h-1v3c-2 1-3 1-4 1l-1 1c-1 1-2 2-4 2v2c-1 1-1 1-2 1v-1h-1c0-3 1-5 1-8 0-1-1-2-1-3s1-2 1-4c0-1-1-2-1-3 0-2 1-6 0-8-1-1-1-1-1-2s2-2 2-2v-1c0-1 0-1-1-2h0c-1-1-1-2-1-3h2c0-1 0-2 1-2 1-1 1-2 2-3 0-2 1-3 2-5v-1l-3 1v-2c1-2 2-3 1-5 0-2-1-3-3-4h1v-2l1-1 1-1c0-1 0-2 1-3l1-6v-1 1l1-1c2 1 2 0 4 0l1-1z" class="f"></path><path d="M556 759s0 1 1 1l1-1v-2l1-2 1-1c1-3-1-3 0-6h0l3 3-2 8 1 1h1v1l-5 2-11 5v-1c0-1 0-1 1-3v1h1c1 0 1-1 2-1l1-1h2l1-1 1-1v-2z" class="X"></path><path d="M557 744c0-2 1-3 2-5v5c-1 1-1 2-1 4s-1 6-2 9v2 2l-1 1-1 1h-2l-1 1c-1 0-1 1-2 1h-1v-1c-1-2-1-4 0-6v-1c1-3 3-7 5-10 1 0 2-1 2-2l2-1z" class="R"></path><path d="M548 765l1-2v-1c0-2 0-2 1-3l1 1h2l2 2-1 1h-2l-1 1c-1 0-1 1-2 1h-1zm7-20l2-1c0 3-1 4-1 6v2c-1 2 0 3-1 5h-4c0-1 1-3 1-5 1-1 1-1 1-2v-1-2c1 0 2-1 2-2z" class="N"></path><path d="M555 718l1-1 8-3v1l1 1c0 3 1 8 0 12v-2c-1 1-2 2-3 2v-3c-1 2 0 5-1 7l1 1-1 2c0 1 1 3 0 5-1 1-1 2-1 2l-1 2v-5c-1 2-2 3-2 5l-2 1c0 1-1 2-2 2l1-5 1-2v-2c-1-1-1-1-1-3v-1c-1-1-2-3-3-4v-1-1h-1c0 2 0 2 1 4h-1-2c0-1-1-1-1-3 0-1 0-2 1-3l1-6v-1 1l1-1c2 1 2 0 4 0l1-1z" class="T"></path><path d="M557 737v-1c0-1 1-2 1-3h1 1c-1 1 0 5-1 6v-1l-2-1z" class="c"></path><path d="M557 737l2 1v1h0c-1 2-2 3-2 5l-2 1v-1c0-3 0-4 2-7z" class="U"></path><path d="M562 725v-3h2c1 1 1 3 1 4-1 1-2 2-3 2v-3z" class="i"></path><path d="M555 718l1-1 8-3v1h-1l-2 7v1c-1-1-1-4-2-5l-1 1h-1l-1 1h-2c-2 1-3 2-4 3v1l-2 2 1-6v-1 1l1-1c2 1 2 0 4 0l1-1z" class="I"></path><path d="M554 720h2l1-1h1c-1 2-2 4-2 6l1 1c0 3 0 5-1 7 0 2-1 5-1 7v-2c-1-1-1-1-1-3v-1c-1-1-2-3-3-4v-1-1h-1c0 2 0 2 1 4h-1-2c0-1-1-1-1-3 0-1 0-2 1-3l2-2v-1c1-1 2-2 4-3z" class="d"></path><path d="M551 729l1-2h2c0 1 1 3 1 4v1c0 1 0 2-1 3v-1c-1-1-2-3-3-4v-1z" class="q"></path><path d="M548 726l2-2v-1c1-1 2-2 4-3l-2 6h-2l-1 1 1 1c0 2 0 2 1 4h-1-2c0-1-1-1-1-3 0-1 0-2 1-3z" class="s"></path><path d="M550 728h1v1 1c1 1 2 3 3 4v1c0 2 0 2 1 3v2l-1 2-1 5c-2 3-4 7-5 10v1c-1 2-1 4 0 6-1 2-1 2-1 3v1l11-5 5-2h1v1l1-1h2c-1 2-1 4-1 6h-2s0 1-1 1v5c-1 2-1 4 0 5l-1 2-2 2c-1 0-2 1-3 1h-1v-2h-1v3c-2 1-3 1-4 1l-1 1c-1 1-2 2-4 2v2c-1 1-1 1-2 1v-1h-1c0-3 1-5 1-8 0-1-1-2-1-3s1-2 1-4c0-1-1-2-1-3 0-2 1-6 0-8-1-1-1-1-1-2s2-2 2-2v-1c0-1 0-1-1-2h0c-1-1-1-2-1-3h2c0-1 0-2 1-2 1-1 1-2 2-3 0-2 1-3 2-5v-1l-3 1v-2c1-2 2-3 1-5 0-2-1-3-3-4h1v-2l1-1 1-1c0 2 1 2 1 3h2 1c-1-2-1-2-1-4z" class="B"></path><path d="M563 761h1v1l-1 2v1c-2 0-3 1-5 0v-2l5-2z" class="a"></path><path d="M564 762l1-1h2c-1 2-1 4-1 6h-2s0 1-1 1v-4l1-2z" class="U"></path><path d="M544 790l1-2-1-1h1c1-3 1-6 0-8s0-1 0-2c1-1 0-3 0-4 0-2 1-1 3-2v-1h2v3c-1 1 0 2-1 3 0 3 0 4 1 6l-2 2v1l2 1c-1 1-2 2-4 2v2c-1 1-1 1-2 1v-1z" class="m"></path><path d="M550 728h1v1 1c1 1 2 3 3 4v1c0 2 0 2 1 3v2l-1 2-1 5c-2 3-4 7-5 10v1c-1 2-1 4 0 6-1 2-1 2-1 3v1l-2 2c0-2-1-7 0-9h1l-1-1v-4h2v-3l1-1c1-1 3-6 3-7l-1-1v-2c-2-2 0-5-1-7 0-1 0-2-1-3h2 1c-1-2-1-2-1-4z" class="y"></path><path d="M551 730c1 1 2 3 3 4v1c0 2 0 2 1 3v2l-1 2v-4c0-1 0 0-1-1l-1 1h-1c-1-2 0-6 0-8z" class="m"></path><path d="M560 770c1-2 1-3 1-4h1c0 2-1 4 0 7h-1l-2 2c0 2 1 3 1 5l-1 1 1 1c-1 0-2 1-3 1h-1v-2h-1v3c-2 1-3 1-4 1l-1 1-2-1v-1l2-2c-1-2-1-3-1-6 1-1 0-2 1-3v-3h3l2-2h2l2-2v1c0 1 0 2 1 3h0z" class="b"></path><path d="M550 770h3v2s-1 1-1 2 1 3 0 4v1l-3-3c1-1 0-2 1-3v-3z" class="d"></path><path d="M553 770l2-2h2l2-2v1c0 1 0 2 1 3h0l1 1-2 1c-1 0-1 0-2 1l-1 1h-1c0-2 0-3-2-4h0z" class="P"></path><path d="M592 696h28l14 1h1c-3 2-10 5-11 8 0 2 0 2-1 3l-1 4v1 1h-1-1c-1 0-2 1-3 2v1h-1c-1 1-2 3-4 5l-1-1v-2l-2-1c1 1 1 1 1 3v2l-2 1c1 1 1 2 1 4 1 0 1 1 1 2s-1 1-2 2h-1c-1-1-1-1-1-2-1-1-2-1-3-2h-1-2l-2 1h-2-1c0 1 0 1-1 1v-1c-1 0-1-1-1-2l2-1h0l-1-2h-1c-1 0 0 0-1-1v-1c-1 0-1 0-2-1h0l-1 1c-2 0-3 0-4-2h-1c-3 0-5 1-7 2v1c-1-1-2-1-3-2 0-1-1-2-2-2v-2c0-2 0-4-1-6l-1-2v3l-2 2c0 1 1 2 0 3h-2c0-2 1-4 1-5s-1-2-1-2c0-1-1-2-2-2-2-1-2 0-4 0h0v-3l3-2 3-2 4-2c1-1 2-1 4-2 3-1 6-1 9-1h9z" class="u"></path><path d="M588 712l1 1 2-1v1h1 1c0-1 0-1 1-2h0c2 0 3-1 4 0v1c-1 1-1 0-2 1-1 0-2 1-3 2-1 0-1-1-2-1v-1l-2 1-1-1v-1zm-18-3v-5l1-1c0 1 1 2 1 3l-1 1c0 1 1 1 1 2 2 1 3 2 4 2 0 1-1 2-2 2s-2-1-3-2l-1-2zm53-1h0l-1-1c-6-1-13-1-19-1h0v-1h-4c1 0 2-1 3 0h1 2c6 1 13 0 19 0 0 2 0 2-1 3z" class="H"></path><path d="M580 712c1-1 2-2 3-2 2 1 3 1 5 2v1l1 1 2-1v1c1 0 1 1 2 1v1c-3-1-3 0-6 1l-1 1-2-1c-2 1-3 1-5 1h-2v1l-1-1-2 1c-1 0-1-1-2-2 0-2 0-4-1-6 1 1 2 2 3 2s2-1 2-2h2v2 1l2-2z" class="S"></path><path d="M580 712c1-1 2-2 3-2 2 1 3 1 5 2v1l-1 1c0-1 0 0-1-1-1 0-1-1-2-1v1c-2 0-2-1-4-1z" class="G"></path><path d="M598 711c5-1 11-2 16 0v1h2c1-1 2 0 3 0h3v1 1h-1-1c-1 0-2 1-3 2v1h-1c-1 1-2 3-4 5l-1-1v-2l-2-1-1-1c-2 0-3 0-5-1-3 0-6 1-8 1-1 0-1 0-2 1-1 0-4-1-6-1 3-1 3-2 6-1v-1c1-1 2-2 3-2 1-1 1 0 2-1v-1z" class="K"></path><path d="M608 717c3 0 6 1 9-1v1h-1c-1 1-2 3-4 5l-1-1v-2l-2-1-1-1z" class="F"></path><path d="M598 711c5-1 11-2 16 0v1h2c1-1 2 0 3 0h3v1h-3c-2 0-3 0-4 1-1 0-1 0-2 1v-2c-1-1-2-1-3-2-1 1-2 2-3 2v1h-1c-1-1-1-1-2-1v-1c-2 0-2 0-3 1-1 0-2-1-3-1v-1z" class="a"></path><path d="M587 717c2 0 5 1 6 1 1-1 1-1 2-1 2 0 5-1 8-1 2 1 3 1 5 1l1 1c1 1 1 1 1 3v2l-2 1c1 1 1 2 1 4 1 0 1 1 1 2s-1 1-2 2h-1c-1-1-1-1-1-2-1-1-2-1-3-2h-1-2l-2 1h-2-1c0 1 0 1-1 1v-1c-1 0-1-1-1-2l2-1h0l-1-2h-1c-1 0 0 0-1-1v-1c-1 0-1 0-2-1h0l-1 1c-2 0-3 0-4-2h-1c-3 0-5 1-7 2v1c-1-1-2-1-3-2 0-1-1-2-2-2v-2c1 1 1 2 2 2l2-1 1 1v-1h2c2 0 3 0 5-1l2 1 1-1z" class="Z"></path><defs><linearGradient id="a" x1="606.586" y1="731.898" x2="594.655" y2="719.829" xlink:href="#B"><stop offset="0" stop-color="#4d4d4b"></stop><stop offset="1" stop-color="#696967"></stop></linearGradient></defs><path fill="url(#a)" d="M594 724h-1v-1h2l-1-1 1-2 1-1s1 1 3 2l1-1h1c1-1 1-1 2-1v1c0 1-1 1-1 2v1h2l1 1v2h0 1l2-2h0c1 1 1 2 1 4 1 0 1 1 1 2s-1 1-2 2h-1c-1-1-1-1-1-2-1-1-2-1-3-2h-1-2l-2 1h-2-1c0 1 0 1-1 1v-1c-1 0-1-1-1-2l2-1h0l-1-2z"></path><path d="M624 109c6-1 13 0 19 0 8 0 17-1 26 0h-5c3 1 9 0 11 2v2l-1 7c0 2 0 4 1 6v3c1 0 0 0 1 1v2h-1-23-41-135-85-21-12v-22h33 28 127 17 40 12c3 0 6 0 9-1z" class="n"></path><path d="M657 123c2-1 3-1 5-1l-1 2c-1 0-2 0-3-1h-1zm-207-7c2 0 5 0 7 1l-13 2-1-1-1-1c3 0 5 0 8-1z" class="N"></path><path d="M450 116l2-1v-2l1-1 1 1h3 0c1 1 2 1 2 2v2h-2c-2-1-5-1-7-1z" class="W"></path><path d="M657 123c-1-1-5-1-6 0h-7c-4 1-8 1-12 0l20-1c1-1 1-2 1-3-3-2-7-1-10-2-1 0-3 1-4 0h8v-1h-1 3c1-1 2-1 3-1 1 1 7 1 10 1v-1c2 0 4 0 6 1h0-1-1l-2 1h-13l2 1 1 1c1 1 1 1 2 1 3 0 3 0 5-2h2v3h0v1h-1c-2 0-3 0-5 1z" class="g"></path><path d="M674 120h0c0 2 0 4 1 6v3c1 0 0 0 1 1v2h-1v-1c-1-2 0 1-1-2h-1-4c-2 0-4 1-6-1v-2-4-1c4-1 8 1 11-1z" class="U"></path><path d="M425 120c1 0 2-1 3-1s1 1 2 1 1 0 1-1h1l2 2c-3 0-6 1-8 2-6 4-44 2-53 1h-1l53-1v-3z" class="V"></path><path d="M546 110h17c6 1 13 0 19 1h13c5 0 11-1 16 0h2 10c3 1 5 1 8 1h32c4 0 8 1 12 1l-1 7h0c0-2 0-4-1-6h-2c-6-1-12-1-18-1h-41c-7 0-15 1-22 0-2-1-4 0-6-1-1 0-1 0-2 1-2-1-2-1-4-1-2 1-5 0-8 0s-8 1-12 0c-1 0-1-1-2-1-3-1-7 0-10-1z" class="W"></path><path d="M624 109c6-1 13 0 19 0 8 0 17-1 26 0h-5c3 1 9 0 11 2v2c-4 0-8-1-12-1h-32c-3 0-5 0-8-1h-10-2c-5-1-11 0-16 0h-13c-6-1-13 0-19-1h40 12c3 0 6 0 9-1z" class="U"></path><path d="M391 110h28c1 2 1 3 0 5h1c2-1 4 0 5 0l10 1c1 0 6 0 7 1l1 1 1 1c-3 0-7 1-10 2l-2-2h-1c0 1 0 1-1 1s-1-1-2-1-2 1-3 1c0 0 0-1-1-1h-7-21l-30-2c6-1 13-1 20-3 3-1 6-1 9-1h7c2-1 2 0 4-2l6 3c1-1 1-2 2-3l-22-1h-1zm68 337c1 0 2 0 3 1l1-1c2 0 4 0 6 1-1 7 0 15 0 22v73 135 60 27c0 1 0 2 1 3-3 1-7-2-9-3h-1-1l-1-1h-2v-1-6-15l-1 4h-1l-1 1c1 0 1 0 2 1v1h-3l-1-3-2-2h2c1 0 3-2 4-4 1-1 0-6 1-8h0c-1-1-2-3-2-4-3-3-4-3-8-4-2 1-3 2-4 4v-3l3-3 1-1 1-1-2-1 2-4v-4c-1-2 0-3 0-4-1-1-1-1-1-2l2-1h0v-2h-1-1l-2-2c-1 1-1 1-2 1h-1c0-1-1-2-1-3-1-2-1-4-2-6h0l2-1c0-2 0-3 1-5v-1c-1-1-2-1-3-2l-1 2c-1-1-1-2-3-2l1-2c1-3 2-5 2-7l-1-3c0-3-2-5-4-7l1-1v-1h-2l-2-1h-2v-3h-1l-1 3h-1l1-1v-3h1c0-1 0-2 1-4v-1h-2l6-6c2-2 3-4 5-6s4-4 5-6c3-3 8-7 8-11l-1-1-1-2 1-15h0v-8l2-1c2-1 3-2 3-4h0l1-1c1-2 1-6 0-9v-4c2-6 0-15-1-21-1-1-1-1-2-1l-2-1h-1-1-1-4l-1-3c0-1 1-3 1-4v-2h0l-1-1h0-1l-4-2 1-3-1-2 2-1v-5c1 2 1 3 0 6l1 1c1-1 1-2 2-2 1-2 0-8 0-10v-3h-1c1-2 1-2 0-3 2-1 2 0 3-1l1-1c-1-1-1-1-1-2v-1h2s0-1 1-2v-1c1 0 0 0 1 1h1l2-1 1-5v-1c2-3 2-4 2-7v-1c0-1 1-2 1-3h1l2-1v-35c0-4 0-8-1-12l3-1z" class="g"></path><path d="M457 495l-1 4h0c-1-1-2 0-3 0 0-1 1-2 1-3h1l2-1z" class="p"></path><path d="M453 721l1-2h1c0 1 1 2 1 3v10c-1-1-2-3-2-4 0-2-1-5-1-7z" class="L"></path><path d="M456 732v10l-1 4h-1l-1 1c1 0 1 0 2 1v1h-3l-1-3-2-2h2c1 0 3-2 4-4 1-1 0-6 1-8z" class="w"></path><path d="M458 764v-1c1-3 1-7 1-10v-22c1 3 0 7 1 11h0v-9h0c1 7 1 14 1 21 0 3 0 7-1 10l1 1h-1-1l-1-1z" class="X"></path><path d="M453 554v-3l1 1h0l1-3h1v2h0l1 28v13l-1-2-1-1 1-1c0-2 0-4-2-6v-4c2-6 0-15-1-21v-3z" class="Z"></path><defs><linearGradient id="b" x1="440.343" y1="533.229" x2="466.608" y2="517.191" xlink:href="#B"><stop offset="0" stop-color="#52594f"></stop><stop offset="1" stop-color="#847c82"></stop></linearGradient></defs><path fill="url(#b)" d="M453 499c1 0 2-1 3 0h0v37c0 5 1 11 0 15h0v-2h-1l-1 3h0l-1-1v3c-1-1-1-2-2-3h-2 0c0-2 0-3-1-5 0-1-1-1-1-2s1-1 2-2c1-2 1-4 1-6l1-1h-1c-1-2 0-6 0-7 1-2 0-3 1-5v-2c-1 0-1 0-1-1 1-4 0-8 1-12v-1c2-3 2-4 2-7v-1z"></path><path d="M453 499c1 0 2-1 3 0h-2v6c0 1-1 3-1 5-1 3-1 7-1 10l-1 1c-1 0-1 0-1-1 1-4 0-8 1-12v-1c2-3 2-4 2-7v-1z" class="d"></path><path d="M449 542s1-1 1 0h1c1-2 1-5 1-6v13c0 1-1 1-1 2h-2 0c0-2 0-3-1-5 0-1-1-1-1-2s1-1 2-2z" class="v"></path><path d="M449 551v-3c0-2 0-4 1-6l1 1v6 1l1-1c0 1-1 1-1 2h-2 0z" class="w"></path><path d="M451 521l1-1c1 2 1 4 1 6l-1 4v6c0 1 0 4-1 6h-1c0-1-1 0-1 0 1-2 1-4 1-6l1-1h-1c-1-2 0-6 0-7 1-2 0-3 1-5v-2z" class="l"></path><defs><linearGradient id="c" x1="456.038" y1="543.458" x2="437.514" y2="520.999" xlink:href="#B"><stop offset="0" stop-color="#737271"></stop><stop offset="1" stop-color="#949493"></stop></linearGradient></defs><path fill="url(#c)" d="M451 508c-1 4 0 8-1 12 0 1 0 1 1 1v2c-1 2 0 3-1 5 0 1-1 5 0 7h1l-1 1c0 2 0 4-1 6-1 1-2 1-2 2s1 1 1 2c1 2 1 3 1 5h0 2c1 1 1 2 2 3v3c-1-1-1-1-2-1l-2-1h-1-1-1-4l-1-3c0-1 1-3 1-4v-2h0l-1-1h0-1l-4-2 1-3-1-2 2-1v-5c1 2 1 3 0 6l1 1c1-1 1-2 2-2 1-2 0-8 0-10v-3h-1c1-2 1-2 0-3 2-1 2 0 3-1l1-1c-1-1-1-1-1-2v-1h2s0-1 1-2v-1c1 0 0 0 1 1h1l2-1 1-5z"></path><path d="M443 526c1-1 1 0 2 0 1 1 1 2 1 4h-1c0-1 0-2-1-2-1-1-1-1-1-2z" class="m"></path><path d="M447 534c1 0 1 1 2 2l-1 2h-1l-1-1c0-2 0-2 1-3z" class="d"></path><path d="M437 540l-1-2 2-1v-5c1 2 1 3 0 6l1 1 1 1c-1 1-1 1-1 3l2 1c1-2 1-2 1-4h1c1 1 1 2 1 4l-3 1h0-1l-4-2 1-3z" class="Q"></path><path d="M444 544c-1 3-1 5 0 7l1 1 2-1 1 1 1-1h2c1 1 1 2 2 3v3c-1-1-1-1-2-1l-2-1h-1-1-1-4l-1-3c0-1 1-3 1-4v-2h0l-1-1 3-1z" class="y"></path><path d="M449 551h2c1 1 1 2 2 3v3c-1-1-1-1-2-1l-2-1h-1-1-1c-1-1-1-1-1-2 1 0 2-1 3-1l1-1z" class="L"></path><defs><linearGradient id="d" x1="454.734" y1="583.029" x2="446.668" y2="648.473" xlink:href="#B"><stop offset="0" stop-color="#1a1918"></stop><stop offset="1" stop-color="#5a5a58"></stop></linearGradient></defs><path fill="url(#d)" d="M454 582c2 2 2 4 2 6l-1 1 1 1 1 2-1 67c-1-2-1-6-1-9-1-2-2-4-4-6v-1c-2-1-3-3-5-3h0c-1-1-2-1-3-1s-3-1-5 1c1 0 1 1 2 2h1l-1 1-2-1h0l-2 2h-1c-1 1-1 2-2 2h-2c2-2 3-4 5-6s4-4 5-6c3-3 8-7 8-11l-1-1-1-2 1-15h0v-8l2-1c2-1 3-2 3-4h0l1-1c1-2 1-6 0-9z"></path><defs><linearGradient id="e" x1="456.147" y1="678.559" x2="432.474" y2="677.57" xlink:href="#B"><stop offset="0" stop-color="#535251"></stop><stop offset="1" stop-color="#979694"></stop></linearGradient></defs><path fill="url(#e)" d="M435 644h1l2-2h0l2 1 1-1h-1c-1-1-1-2-2-2 2-2 4-1 5-1s2 0 3 1h0c2 0 3 2 5 3v1c2 2 3 4 4 6 0 3 0 7 1 9v63c0-1-1-2-1-3h-1l-1 2c0 2 1 5 1 7-3-3-4-3-8-4-2 1-3 2-4 4v-3l3-3 1-1 1-1-2-1 2-4v-4c-1-2 0-3 0-4-1-1-1-1-1-2l2-1h0v-2h-1-1l-2-2c-1 1-1 1-2 1h-1c0-1-1-2-1-3-1-2-1-4-2-6h0l2-1c0-2 0-3 1-5v-1c-1-1-2-1-3-2l-1 2c-1-1-1-2-3-2l1-2c1-3 2-5 2-7l-1-3c0-3-2-5-4-7l1-1v-1h-2l-2-1h-2v-3h-1l-1 3h-1l1-1v-3h1c0-1 0-2 1-4v-1h-2l6-6h2c1 0 1-1 2-2z"></path><path d="M435 644h0c1 2 1 2 0 4h1c0-2 0-3 2-4 1 1 1 1 2 3l-1-1c0 2-1 2-1 4l-1 1c-1 1-1 2-3 2h0 1 1 2l-2 1 1 1-1 1c1 1 2 1 2 1-1 1-1 2-2 3-2-1-3-1-5 0v2l-2-1h-2v-3h-1l-1 3h-1l1-1v-3h1c0-1 0-2 1-4v-1h-2l6-6h2c1 0 1-1 2-2z" class="k"></path><path d="M427 652c1 0 1-1 2-1l1 1-1 2c-1 0-1 0-2-1v-1z" class="P"></path><path d="M434 653c-1 1-2 1-3 0v-1c1 0 2-1 2-2s0-1 1-2l1 1h1l2-3h1c0 2-1 2-1 4l-1 1c-1 1-1 2-3 2h0z" class="d"></path><path d="M429 661v-3c0-1-1-1-1-2 3-1 5-2 8-2l1 1-1 1c1 1 2 1 2 1-1 1-1 2-2 3-2-1-3-1-5 0v2l-2-1z" class="P"></path><path d="M448 656h0c1-1 1-1 1-2l1-1v-1-1h0v-2c2 4 0 7 1 11v8 15c0 1 1 2 1 3 0 2-1 4-2 6 0 2 0 5 1 7 0 2 1 4 0 6 3 5-2 11 2 16 0 2 1 5 1 7-3-3-4-3-8-4-2 1-3 2-4 4v-3l3-3 1-1 1-1-2-1 2-4v-4c-1-2 0-3 0-4-1-1-1-1-1-2l2-1h0v-2h-1-1l-2-2c-1 1-1 1-2 1h-1c0-1-1-2-1-3-1-2-1-4-2-6h0l2-1c0-2 0-3 1-5v-1c-1-1-2-1-3-2l-1 2c-1-1-1-2-3-2l1-2c1-3 2-5 2-7l-1-3c0-3-2-5-4-7l1-1v-1h-2v-2c2-1 3-1 5 0 1-1 1-2 2-3h1c1 1 1 2 1 3v-1h2 2 1v4c2 2 3 3 3 6h0v-3c-1-3-1-6 0-10z" class="m"></path><path d="M447 720l1 1h1 1v3l-1-1c0-1 0-1-1-2l-1 2v1h-1c-2 1-3 2-4 4v-3l3-3 1-1 1-1z" class="p"></path><path d="M442 690c2 1 3 2 4 4 0 2-1 2-2 4h-4c-1-2-1-4-2-6h0l2-1 2-1z" class="C"></path><path d="M447 702l1-3h0l1 2v5c0 2 1 5 0 8v3c0 1 0 1-1 2v2l-1-1-2-1 2-4v-4c-1-2 0-3 0-4-1-1-1-1-1-2l2-1h0v-2h-1z" class="P"></path><path d="M445 663c2 2 3 3 3 6h0v-3c-1-3-1-6 0-10v4 6c1 1 0 3 1 5 1 3 0 5 1 7v1c-1 3 1 6-1 9l-1 1-1-1-1 1-1-1h-1c1-1 2 0 3-1 0-2 0-4-1-6v-1c1-1 1-3 1-5 0-1 0-3-1-4h0c1-3 0-6-1-8z" class="d"></path><defs><linearGradient id="f" x1="439.361" y1="656.674" x2="443.771" y2="681.865" xlink:href="#B"><stop offset="0" stop-color="#90908e"></stop><stop offset="1" stop-color="#adacac"></stop></linearGradient></defs><path fill="url(#f)" d="M438 657h1c1 1 1 2 1 3v-1h2 2 1v4c1 2 2 5 1 8h0c1 1 1 3 1 4 0 2 0 4-1 5v1c1 2 1 4 1 6-1 1-2 0-3 1v2h-2l-2 1c0-2 0-3 1-5v-1c-1-1-2-1-3-2l-1 2c-1-1-1-2-3-2l1-2c1-3 2-5 2-7l-1-3c0-3-2-5-4-7l1-1v-1h-2v-2c2-1 3-1 5 0 1-1 1-2 2-3z"></path><path d="M440 670v-3h1l1 2c1 2 1 2 1 4v2c0 1-1 3-1 4v-2c0-3 0-4-2-7z" class="E"></path><path d="M431 662v-2c2-1 3-1 5 0v3c-1 0-1-1-3-1h-2z" class="d"></path><path d="M442 683c1 0 2-1 3-1v-6l2-1c0 2 0 4-1 5v1c0 1 0 2-1 3 0 0 0 1-1 1h-1c-1 0-1 1-2 1h0v-1l1-2z" class="T"></path><path d="M446 681c1 2 1 4 1 6-1 1-2 0-3 1v2h-2l-2 1c0-2 0-3 1-5h0c1 0 1-1 2-1h1c1 0 1-1 1-1 1-1 1-2 1-3z" class="q"></path><path d="M440 670c2 3 2 4 2 7v2 4l-1 2c-1-1-2-1-3-2l1-2v-5l1-1c-1-2 0-3 0-5z" class="X"></path><path d="M433 663c3 3 5 6 5 11l1 7-1 2-1 2c-1-1-1-2-3-2l1-2c1-3 2-5 2-7l-1-3c0-3-2-5-4-7l1-1z" class="D"></path><defs><linearGradient id="g" x1="514.598" y1="713.384" x2="544.757" y2="711.276" xlink:href="#B"><stop offset="0" stop-color="#060406"></stop><stop offset="1" stop-color="#31322e"></stop></linearGradient></defs><path fill="url(#g)" d="M526 537h2c1 3 1 4 0 7 0 1 1 1 1 3 0 1 1 3 1 5l3 6 1 1v-1h2v1l1-1h1l1 1c0-2 0-2 1-3v-1l2 2c0 1 0 1-1 2 0 2 2 2 2 3v1l-1 1-1-1v5 3c1 3-1 5 1 7v2l1 1 1-1v-1l-1-1c0-1-1-2 0-3v-1l1-1v-1c-1-1-1-1-1-2 0-2 0-3 1-5h0c1-1 1-2 1-2v-1l1-1 1-1 1 1c1 2 3 3 4 5l1 2 1 2h3 4v4 2l1 2c1-2 1-3 2-4v1c2 2 4 4 6 5 1 1 2 2 2 3l1 40v23 7 28 4l1 10v2c-2 1-3 1-4 2l-4 2-3 2-3 2v3h0c2 0 2-1 4 0 1 0 2 1 2 2 0 2 0 2-2 4l-8 3-1 1-1 1c-2 0-2 1-4 0l-1 1v-1 1l-1 6c-1 1-1 2-1 3l-1 1-1 1v2h-1c2 1 3 2 3 4 1 2 0 3-1 5v2l3-1v1c-1 2-2 3-2 5-1 1-1 2-2 3-1 0-1 1-1 2h-2c0 1 0 2 1 3h0c1 1 1 1 1 2v1s-2 1-2 2 0 1 1 2c1 2 0 6 0 8 0 1 1 2 1 3 0 2-1 3-1 4s1 2 1 3c0 3-1 5-1 8h1v1c1 0 1 0 2-1 2-1 3-2 5-2 1 0 2 0 3 1 1-1 2-1 4-2v1c1 2 1 4 1 7 0 2 1 4 2 6h0c-1 1-1 1 0 2v1 6 4h0l1 5-2 1v1h4v2l5 10v4 1c1 1 1 2 2 3l1 1c1 1 0 2 1 3l1 1c-1 2-1 1-1 3h-2l-1 1h-2l1 2c-1 1-1 1-1 2l1 1c1 2 1 4 0 6l2 2 1 1-1 1-9 6c-1 1-2 2-3 2l-1 1c-6-4-10-8-15-13h-2l-1 62h0c-2-1-1-5-1-7 0-5 0-11-1-16 0 1-1 1-2 2l-1 1c0 3 1 9 0 12h-1c-1-1-1-1-1-3h0c-1-2-1-4-1-6h0v-1-2h-2v3c-1 3 0 6 0 9v18c0 2 0 5-1 7h0-1v-32c0-5-1-10 0-14 1-2 0-5 0-7l-1 1v1h-1v-1l-1-1v-6-20-38-2l1-39V537z"></path><path d="M548 716c1 0 1 0 1 1v2 1c-2-1-3 0-4 0l3-4z" class="C"></path><path d="M535 733l-2-2h0v-8h0v-1-1h1v-2h1c1 1 2 2 2 3h0c-1 1-1 1-2 1v-1c-2 2-2 5-1 7v2c1 1 1 1 1 2z" class="F"></path><path d="M526 867c1 1 2 1 2 3 0 1 0 5-1 6h-1v1l1 1h-1-1l1-11z" class="M"></path><path d="M534 622c2 0 0-1 2-3l1 3c1 1 2 1 3 2l-1 1c-2 0-2 0-2 1-1 0-2-1-2-2h0-2l-1-1h0l2-1z" class="K"></path><path d="M537 626c0-1 0-1 2-1 0 1 1 2 1 4v1c1 1 1 2 3 3h0c-2 0-3 0-4-1l-1 1v1h0 1l-2 2-1-1s-1 0-1-1c-1 1-2 1-2 2l-4-2v-1c2 0 2 0 3 1l1-1h2l1-2 2-2 1-1c0-1-2-1-2-2z" class="Q"></path><path d="M525 861v-38l1 44-1 11h1 1c1 2 0 3 0 4s-1 2-1 3c1 0 1 0 2 1v1l-1 1v1h-1v-1l-1-1v-6-20z" class="G"></path><path d="M530 552l3 6 1 1c0 2 0 7 1 9h0c-1 2-1 4-1 5-1-1-1-1-1-3-1-1-1-1-1-2v-1l-1-1v-3c-1-1-1-2-1-3v-1-2c-1-1-1-3-1-4l1-1z" class="B"></path><path d="M529 653v-3-10c-1-2 0-3-1-5h0v-2l2-1c1 0 2 0 3 1l-1 1c-1-1-1-1-3-1v1l4 2c0-1 1-1 2-2 0 1 1 1 1 1-1 1-2 1-3 2v3s-1 0-1 1c-1 1 0 3-1 5h1c0 2 0 4-1 6v1h-2z" class="F"></path><path d="M546 621l1-1c2 2 1 3 1 5l-1 2h0c1 1 1 1 0 1v2c0 1-1 1-1 2l-1 1h-2c-2-1-2-2-3-3v-1c0-2-1-3-1-4l1-1h1c3 0 4-2 5-3z" class="M"></path><path d="M537 722h1c1 1 1 2 2 3v2c0 2 0 3 1 5h0c1 0 2 0 3 1-3 0-5 0-7 2-1 1-1 3-2 5-1-1-2-1-2-3 0-1 1-3 2-4 0-1 0-1-1-2v-2c-1-2-1-5 1-7v1c1 0 1 0 2-1h0z" class="I"></path><path d="M540 727h-3v-1c1-1 2-1 3-1v2z" class="L"></path><path d="M535 740c1-2 1-4 2-5 2-2 4-2 7-2h0c2 1 3 2 3 4 1 2 0 3-1 5l-3-1s-1 1-2 1h-1-2c-1-1-2-1-3-2z" class="b"></path><path d="M541 742c-1-2-2-2-2-4 0-1 0-2 1-3s1-1 3-1l3 3c-1 2-1 3-3 4 0 0-1 1-2 1z" class="H"></path><path d="M545 720c1 0 2-1 4 0l-1 6c-1 1-1 2-1 3l-1 1-1 1v2h-1 0c-1-1-2-1-3-1h0c-1-2-1-3-1-5v-2c-1-1-1-2-2-3l1-1c3 1 3 1 6-1z" class="M"></path><path d="M531 882l1 1c1 4 1 7 1 11l1-3h0c1 1 1 1 1 2v8h0v2c0 3 1 9 0 12h-1c-1-1-1-1-1-3h0c-1-2-1-4-1-6h0v-1-2h-2v3c-1 3 0 6 0 9v18c0 2 0 5-1 7v-36c0-5-1-12 1-16 1 3 0 8 1 12 1-2 0-4 0-5 0-4 1-8 0-13z" class="N"></path><path d="M533 894l1-3h0c1 1 1 1 1 2v8h0v2c0 3 1 9 0 12h-1c-1-1-1-1-1-3v-18z" class="H"></path><path d="M539 602v-3l1 1c1 3 0 7 1 11l-2 2c1 0 3 0 4 1 2 1 3 2 3 4 1 1 1 2 0 3s-2 3-5 3h-1c-1-1-2-1-3-2l-1-3c-2 2 0 3-2 3h-1v-2c1-1 1-2 1-3l-1-1v-2c0-1 1-1 0-3h0c1-2 1-4 1-6s1-3 3-5l2 2z" class="J"></path><path d="M539 602v-3l1 1c1 3 0 7 1 11l-2 2c-1 0-3 3-4 4h0c-1-1-1-1-1-2 1-1 1-1 1-2v-3h1l-1-2v-1l2-1v-2h2v-2z" class="Z"></path><path d="M543 614c2 1 3 2 3 4 1 1 1 2 0 3s-2 3-5 3h-1c-1-1-2-1-3-2v-3h0l1-1 1-1v-1l1-1c1 0 2 0 3-1h0z" class="I"></path><path d="M536 811c0-1 1-1 1-2l1 3v3 4l-1 1c1 2 0 0 1 1v3 1 1c0 2-1 4-1 5 1 1 1 0 1 1 0 2 0 2 1 3v6c-1-1-1-1-1-2s0-1-1-2v-2l-2-1 1 1c-1 0-2 1-3 2 0 2 1 4 1 6-1 1-1 3-1 4l-1-1c0-1-1-1-1-1v-7-1-7c-1-2-1-4-1-6-1-4 0-10 0-15 1 2 0 5 1 7h0v-4c1-1 1-2 1-3 1 1 2 2 4 2z" class="G"></path><path d="M534 832s-1 1-2 1c0-3 1-7 0-10h0c1-2 0-4 1-6l1 1v2h0v1l1 1c0 2 1 4 2 6-2 1-1 2-3 4z" class="H"></path><path d="M536 811c0-1 1-1 1-2l1 3v3 4l-1 1c1 2 0 0 1 1v3 1 1c0 2-1 4-1 5 1 1 1 0 1 1 0 2 0 2 1 3v6c-1-1-1-1-1-2s0-1-1-2v-2l-2-1-1-2c2-2 1-3 3-4-1-2-2-4-2-6l-1-1h1v-2c0-1 0-2 1-3 0-1 0-1-1-2l-1 1h-1c1-2 1-2 2-3l1 1 1-1-1-1z" class="F"></path><path d="M530 809v-30c0-3 0-7 1-10v-1l1-6c1 3 1 6 1 8 0 3-1 9 1 11 1 0 2-1 4-2v1l-1 1 1 1v3h-2v1c1 0 1 0 2 1v1l-2 4v1c1 1 1 1 1 2 1 2 1 3 1 4s0 2-1 3l-2 2h0c1 1 2 3 2 4v1c0 1-1 1-1 2-2 0-3-1-4-2 0 1 0 2-1 3v4h0c-1-2 0-5-1-7z" class="D"></path><path d="M536 792v1l-1 2c1 2 1 4 1 6l1 1h-1c-1-1-1-2-1-4v-1l-1-1c0-2 1-2 2-4z" class="I"></path><path d="M535 804c1 1 2 3 2 4v1c0 1-1 1-1 2-2 0-3-1-4-2 0-2 2-4 3-5z" class="M"></path><path d="M539 835v-3 15c0 2 0 4 1 6v7c0 1 0 1 1 1l-1 62h0c-2-1-1-5-1-7 0-5 0-11-1-16 0 1-1 1-2 2l-1 1v-2h0v-8c0-1 0-1-1-2h0l-1 3c0-4 0-7-1-11l-1-1c0-1 0-1 1-2l1 1v-7h0l-1-8c1-2 1-5 1-6-1-1-1-1-1-2v-4c-1-2 0-4 0-6v-2l1 1c0-1 0-3 1-4 0-2-1-4-1-6 1-1 2-2 3-2l-1-1 2 1v2c1 1 1 1 1 2s0 1 1 2v-6z" class="O"></path><path d="M537 871l1 3v14c0 1 0 4-1 6v-5l-2-2c2-5-1-12 2-16z" class="T"></path><path d="M533 874v-1-3c1-1 2-1 3-2h1v3c-3 4 0 11-2 16l2 2v5 2l1-1v5c0 1-1 1-2 2l-1 1v-2h0v-8c0-1 0-1-1-2h0l-1 3c0-4 0-7-1-11l-1-1c0-1 0-1 1-2l1 1v-7z" class="a"></path><path d="M535 887l2 2v5 2l1-1v5c0 1-1 1-2 2l-1 1v-2c1-4 0-9 0-14z" class="N"></path><path d="M535 834l2 1v2c1 1 1 1 1 2s0 1 1 2c0 2 0 5-1 7v4h1v1c-1 1-1 1 0 2v1h0c-1 3-1 10 0 13l1 1v3c0 1 0 3-1 4v5 2 4c-1-3 0-8 0-12 0 0-1-1-1-2l-1-3v-3h-1c-1 1-2 1-3 2v3 1h0l-1-8c1-2 1-5 1-6-1-1-1-1-1-2v-4c-1-2 0-4 0-6v-2l1 1c0-1 0-3 1-4 0-2-1-4-1-6 1-1 2-2 3-2l-1-1z" class="M"></path><path d="M532 846l1 1v9c0 1 1 2 0 3v-6l-1-5v-2zm3-12l2 1v2c1 1 1 1 1 2s0 1 1 2c0 2 0 5-1 7v-8h-2l-1-2c1-1 1-1 1-2v-1l-1-1z" class="C"></path><path d="M540 555l2 2c0 1 0 1-1 2 0 2 2 2 2 3v1l-1 1-1-1v5 3c1 3-1 5 1 7v2l1 1 1-1v-1l-1-1c0-1-1-2 0-3v-1l1-1v-1c-1-1-1-1-1-2 0-2 0-3 1-5h0c1-1 1-2 1-2v-1l1-1v1c0 3 0 7-1 10v3h-1c0 1-1 1 0 2l1-1c0 2 0 4-1 6 2 3 1 8 3 12-1 2 0 4-1 6l-1 3h0c1 1 1 2 1 3v1 2c0 1-1 1-2 1l-1-1-1 2 1 1h-2l1-1h-1c-1-4 0-8-1-11l-1-1v3l-2-2c-2 2-3 3-3 5l-1-2v-3-3c1-2-1-7 1-9h2l1-2c-2 0-2-1-3-2h0c-2 1-1 3-2 5v11c0 1 0 1-1 2v-6c1-3 0-6 0-8l1-1c0-3 0-4 2-6l2-2c-1-1-1-1-1-2-1-1-1-2-1-4 0-1 0-3 1-5h0c-1-2-1-7-1-9v-1h2v1l1-1h1l1 1c0-2 0-2 1-3v-1z" class="x"></path><path d="M545 572c0-1-1-1-2-2 0-1 0-2 1-2 0-1 1-2 1-2 0-1 1-3 1-4 0 3 0 7-1 10z" class="C"></path><path d="M533 603c0-3 0-13 2-15 1 1 2 0 4 0h1v12l-1-1v3l-2-2c-2 2-3 3-3 5l-1-2z" class="B"></path><path d="M539 588h1v12l-1-1v3l-2-2v-2l-1-1h2l1-1c0-2-1-1-2-2 2-2 1-4 2-6z" class="Y"></path><path d="M539 559c0-2 0-2 1-3v15 9c0 1 1 3 0 4 0 0-1 1-1 2-2 0-3-1-5-3v-2l2-2c-1-1-1-1-1-2-1-1-1-2-1-4 0-1 0-3 1-5h0c-1-2-1-7-1-9v-1h2v1l1-1h1l1 1z" class="L"></path><path d="M537 558h1l1 1v4l-2 2c-1 1-1 2 0 3l-1 2v1 1c1 2 1 4 1 6-1 0-1 0-1 1-1-1-1-1-1-2-1-1-1-2-1-4 0-1 0-3 1-5h0c-1-2-1-7-1-9v-1h2v1l1-1z" class="D"></path><path d="M531 653v-1c1 0 2 1 3 1v1c1 1 1 2 3 2h0l1 1c1 1 1 1 3 2h0v2c0 1 1 2 1 3 1 0 4 2 6 2v-1s1 2 2 3c1 0 1 0 2 1l-1 1c0 1 0 1 1 1v1l1 5c-1-1-2-1-2-1l-2 3c-1 2-3 4-5 5-1 0-2 1-3 0h-1-2l2 10v4 5c-1 1-2 1-3 1-2 1-2 1-4 3l-1 2c-1 0-1-1-1-1 0-1 0-2 1-3s1-1 2-3c-1-1-1-2-1-2v-5h-1c1-2 1-6 1-8 1-1 0-3 1-4 0-2 0-1-1-2-1 0-2-1-3 0l-1 6h0v-34h2z" class="Q"></path><path d="M538 684l2 10-2-1v-4c0-1-1-1-1-2l-1-1h0l2-2z" class="J"></path><path d="M533 665l1 1v-2c0-2 0-3 1-4 1 0 2 0 3 1 0 1-1 3-2 4h-1l-2 3-1-1c0-1 0-1 1-2z" class="F"></path><path d="M531 653v-1c1 0 2 1 3 1v1c1 1 1 2 3 2h0l1 1c1 1 1 1 3 2h0c-2 0-2 1-3 2-1-1-2-1-3-1-1 1-1 2-1 4v2l-1-1c0-1-1-2 0-4v-2c-1-1-1-3-1-4v-1l-1-1z" class="C"></path><path d="M539 664c1 0 1 1 2 2h1v1l-2 2c-1 1-2 2-2 4l1 3c-1 1-3 1-4 2v-2c-1-5 1-8 4-12z" class="E"></path><path d="M533 707v-1c0-1 1-2 2-3 0-1-1-3-1-4 0-3-1-6 0-9h1l2 2 1 1 2 1v4 5c-1 1-2 1-3 1-2 1-2 1-4 3zm6-43c1-1 1-2 2-3 0 1 1 2 1 3 1 0 4 2 6 2v-1s1 2 2 3c1 0 1 0 2 1l-1 1c0 1 0 1 1 1v1l1 5c-1-1-2-1-2-1l-2 3c-1 2-3 4-5 5-1 0-2 1-3 0h-1l-1-1h-1c-1-2-2-3-3-5 1-1 3-1 4-2l-1-3c0-2 1-3 2-4l2-2v-1h-1c-1-1-1-2-2-2z" class="L"></path><path d="M540 669l2-2 5 3-3 1-4-2z" class="H"></path><path d="M549 671h3v1l1 5c-1-1-2-1-2-1s0-1-1-2c0-1-1-2-1-3z" class="B"></path><path d="M542 664c1 0 4 2 6 2v-1s1 2 2 3c1 0 1 0 2 1l-1 1c0 1 0 1 1 1h-3 0c-1-1-2-3-3-3-1-1-2-1-2-2l-2-2z" class="D"></path><path d="M540 669l4 2 3-1c0 1 1 2 1 3s-1 2-1 3h0c-2 0-3 1-5 1h0c-1 0-2 0-2-1h-1l-1-3c0-2 1-3 2-4z" class="M"></path><path d="M540 669l4 2c-2 0-3 0-3 2l1 4h0c-1 0-2 0-2-1h-1l-1-3c0-2 1-3 2-4z" class="G"></path><path d="M539 676h1c0 1 1 1 2 1h0c2 0 3-1 5-1v1s0 1-1 1h-1c0 2 1 3-1 4-1 0-1 1-2 1-1 1 0 1-1 1h-1l-1-1h-1c-1-2-2-3-3-5 1-1 3-1 4-2z" class="h"></path><path d="M540 676c0 1 1 1 2 1 0 2-1 4-2 6h-1-1c1-3 1-5 2-7z" class="k"></path><path d="M539 676h1c-1 2-1 4-2 7-1-2-2-3-3-5 1-1 3-1 4-2z" class="T"></path><path d="M546 561l1-1 1 1c1 2 3 3 4 5l1 2 1 2h3 4v4 2l1 2v89-4-13-6c-1 1-1 6-1 8h0v-5l-2-2c-2-2-2-4-2-7h-1v-3s-1 0-1-1c-1-1-3-1-4-3v2c-1-1-2-2-4-3v-2c1 0 1 0 0-1h0l1-2c0-2 1-3-1-5l-1 1c1-1 1-2 0-3 0-2-1-3-3-4-1-1-3-1-4-1l2-2h1l-1 1h2l-1-1 1-2 1 1c1 0 2 0 2-1v-2-1c0-1 0-2-1-3h0l1-3c1-2 0-4 1-6-2-4-1-9-3-12 1-2 1-4 1-6l-1 1c-1-1 0-1 0-2h1v-3c1-3 1-7 1-10v-1z" class="k"></path><path d="M558 585h3v8h0c-2-2-2-2-2-4l-1-4z" class="P"></path><path d="M559 632c0-1 0-4 1-5h1v20l-2-2c-2-2-2-4-2-7h-1c1-1 2-1 3-1v-5z" class="f"></path><path d="M556 583c1-1 2-1 3-2l-1 4 1 4c0 2 0 2 2 4h0v3 5c0 7 1 15 0 23v3h-1c-1 1-1 4-1 5v-8-9-9c0-3 0-7-1-11h-1l-1 1v3h-1l1-16z" class="E"></path><path d="M559 596h2v5c-2-2-2-2-2-5z" class="T"></path><path d="M559 589c0 2 0 2 2 4h0v3h-2v-7z" class="p"></path><path d="M556 599v-3l1-1h1c1 4 1 8 1 11v9 9 8 5c-1 0-2 0-3 1v-3s-1 0-1-1c-1-1-3-1-4-3v-2l2-1v-2c2-1 2-1 2-3l1-1-1-3 1-6h-1c0-2-2-3-2-4 0-3 2-5 0-7l-1-2 1-1h2 1z" class="N"></path><path d="M555 599h1v8c-1 2 0 4 0 6h0-1c0-2-2-3-2-4 0-3 2-5 0-7l-1-2 1-1h2z" class="R"></path><path d="M551 631v-2l2-1v-2c2-1 2-1 2-3 1 2 1 5 1 8v4s-1 0-1-1c-1-1-3-1-4-3z" class="E"></path><path d="M547 594c1 2-1 5 1 7 0-1 0 0 1-1v-1l3 1 1 2c2 2 0 4 0 7 0 1 2 2 2 4h1l-1 6 1 3-1 1c0 2 0 2-2 3v2l-2 1v2 2c-1-1-2-2-4-3v-2c1 0 1 0 0-1h0l1-2c0-2 1-3-1-5l-1 1c1-1 1-2 0-3 0-2-1-3-3-4-1-1-3-1-4-1l2-2h1l-1 1h2l-1-1 1-2 1 1c1 0 2 0 2-1v-2-1c0-1 0-2-1-3h0l1-3c1-2 0-4 1-6z" class="b"></path><path d="M553 602c2 2 0 4 0 7 0 1 2 2 2 4h1l-1 6 1 3h-3v-2-1c-1-2 0-4-1-5v-11l1-1z" class="f"></path><path d="M555 613h1l-1 6h-2v-5l1-1c0 1 0 1 1 2v-2z" class="X"></path><path d="M547 594c1 2-1 5 1 7 0-1 0 0 1-1v-1l3 1 1 2-1 1h-2c-2 1-1 5-1 7-1 2 0 5 1 7l-1 2s1 1 0 2c0 2 0 4 1 5-1 1-1 2-3 2 1 0 1 0 0-1h0l1-2c0-2 1-3-1-5l-1 1c1-1 1-2 0-3 0-2-1-3-3-4-1-1-3-1-4-1l2-2h1l-1 1h2l-1-1 1-2 1 1c1 0 2 0 2-1v-2-1c0-1 0-2-1-3h0l1-3c1-2 0-4 1-6z" class="B"></path><path d="M541 611h1l-1 1h2l-1-1 1-2 1 1-1 1c0 1 1 1 1 2h1c1 1 2 1 3 2 0 2 0 3 1 4 0 0 1 1 0 2 0 2 0 4 1 5-1 1-1 2-3 2 1 0 1 0 0-1h0l1-2c0-2 1-3-1-5l-1 1c1-1 1-2 0-3 0-2-1-3-3-4-1-1-3-1-4-1l2-2z" class="a"></path><path d="M546 561l1-1 1 1c1 2 3 3 4 5l1 2 1 2h3v3h0c1 1 1 1 1 2v3l1 3c-1 1-2 1-3 2l-1 16h-2l-1 1-3-1v1c-1 1-1 0-1 1-2-2 0-5-1-7-2-4-1-9-3-12 1-2 1-4 1-6l-1 1c-1-1 0-1 0-2h1v-3c1-3 1-7 1-10v-1z" class="D"></path><path d="M548 561c1 2 3 3 4 5-2 1-3 1-4 3h0c1 2 1 4 1 5v1l-1 1h0c1 2 1 3 1 4l3 3-1 1v5l1 1v3 2c0 1-1 3-1 3l-2 1v-2c1-2 1-5 0-7h0l1-1v-6l-2-1c0-1 0-2-1-2 0-7-2-13 1-19z" class="h"></path><path d="M549 599l2-1s1-2 1-3v-2-3l-1-1v-5l1-1-3-3c0-1 0-2-1-4h0l1-1v1 1c1 0 1 1 1 1 1 1 1 1 2 1s2-2 2-2l1-1v5l1 2-1 16h-2l-1 1-3-1h0z" class="k"></path><path d="M555 581l1 2-1 16h-2v-1h-1c0-2 1-4 1-5 0-2-1-3-1-4 1-1 1-2 2-3 0-2 0-4 1-5z" class="q"></path><path d="M549 575v-1c0-1 0-3-1-5h0c1-2 2-2 4-3l1 2 1 2h3v3h0c1 1 1 1 1 2v3l1 3c-1 1-2 1-3 2l-1-2v-5l-1 1s-1 2-2 2-1 0-2-1c0 0 0-1-1-1v-1-1z" class="P"></path><path d="M555 576l1 1c0 1 0 1 1 2l1-1 1 3c-1 1-2 1-3 2l-1-2v-5z" class="p"></path><path d="M562 578c1-2 1-3 2-4v1c2 2 4 4 6 5 1 1 2 2 2 3l1 40v23 7 28 4l1 10v2c-2 1-3 1-4 2l-4 2-3 2c-1-4-1-8-1-12v-24-89z" class="j"></path><path d="M566 701c-2-4-1-10 0-14v-1 5h1c1 2 1 4 1 7h0v-16h0c2-3 0-11 1-14 0-2 2-3 3-5v-3c0-3 0-5 1-7v28 4l1 10v2c-2 1-3 1-4 2l-4 2z" class="V"></path><path d="M573 681v4l1 10v2c-2 1-3 1-4 2-1-4-2-13 0-17 1 0 2 0 3-1h0z" class="i"></path><path d="M571 689h0c1 0 1 0 1 1 1 1 0 4 1 6h-2c-1-2 0-4 0-7z" class="V"></path><path d="M547 630c2 1 3 2 4 3v-2c1 2 3 2 4 3 0 1 1 1 1 1v3h1c0 3 0 5 2 7l2 2v5h0c0-2 0-7 1-8v6 13 4 24c0 4 0 8 1 12l-3 2v3h0c2 0 2-1 4 0 1 0 2 1 2 2 0 2 0 2-2 4-3 1-5 2-8 3l-1 1-1 1c-2 0-2 1-4 0l-1 1v-1-2c0-1 0-1-1-1v-2l-1-1h-1v3c-1 1-2 3-3 3-1 1-3 2-4 1l-2-2v-2c-1-1-1-1-1-2v-3c0-3 2-5 4-7v-1-5-4l-2-10h2 1c1 1 2 0 3 0 2-1 4-3 5-5l2-3s1 0 2 1l-1-5v-1c-1 0-1 0-1-1l1-1c-1-1-1-1-2-1-1-1-2-3-2-3v1c-2 0-5-2-6-2 0-1-1-2-1-3v-2h0c-2-1-2-1-3-2l-1-1h0c-2 0-2-1-3-2v-1c-1 0-2-1-3-1 1-2 1-4 1-6h-1c1-2 0-4 1-5 0-1 1-1 1-1v-3c1-1 2-1 3-2l1 1 2-2h-1 0v-1l1-1c1 1 2 1 4 1h0 2l1-1c0-1 1-1 1-2z" class="o"></path><path d="M551 676s1 0 2 1c-1 3 0 6-3 8 0 1-1 2-2 3 0-2 0-3 1-4l2-2c0-1 0-2-2-3l2-3z" class="S"></path><path d="M538 716l-1-1v-2c0-3 1-5 3-7l1 3-2 4v3h-1z" class="C"></path><path d="M540 706h2c1-1 1-1 3 0l5 6c0 1 0 5-1 5 0-1 0-1-1-1v-2l-1-1h-1v3c-1 1-2 3-3 3-1 1-3 2-4 1l-2-2v-2l1 1v-1h1v-3l2-4-1-3z" class="F"></path><path d="M540 706h2c1-1 1-1 3 0l-1 1c-2 0-2 0-3 2l-1-3z" class="B"></path><path d="M542 715h-1c0-1 0-3 1-4 0-2 1-2 2-2 1 1 1 2 2 4-1 1-2 1-2 2h-2z" class="K"></path><path d="M538 716h1v-3h1v3l1 2h2l1-2c-1 0-1-1-2-1h2c0-1 1-1 2-2v3c-1 1-2 3-3 3-1 1-3 2-4 1l-2-2v-2l1 1v-1z" class="B"></path><path d="M557 654l-1 5v7c0 3 0 5 1 8 0 2 0 3 1 5h-1c-2 3 0 8-1 11h0l-1-2c-2 0-2 1-3 2v5c-1 0-2 0-3 1l1-7s1-1 1-2v-1h2v-2c1-1 1-2 2-3v1h1c0-1 0-1-1-2v-5c-1-1-1-2-2-4l-1-4c-1-2 0-4 0-6l1-1v-1c1-1 2-2 3-2l1-3z" class="d"></path><path d="M557 654l-1 5v4c-1-1-1-2-1-3l-1 1v6c1 2 2 5 1 8-1-1-1-2-2-4l-1-4c-1-2 0-4 0-6l1-1v-1c1-1 2-2 3-2l1-3z" class="h"></path><path d="M549 679c2 1 2 2 2 3l-2 2c-1 1-1 2-1 4 0 1-1 2-1 3v10c0 3 1 4 2 7-2-2-5-4-7-4h-2v-1-5-4l-2-10h2 1c1 1 2 0 3 0 2-1 4-3 5-5z" class="x"></path><path d="M540 698l2 3h1c1 1 1 2 3 3 0-2 0-3-1-5 0-1 1-2 1-3h-2l-1-1 2-1c0-1 1-2 2-3h0v10c0 3 1 4 2 7-2-2-5-4-7-4h-2v-1-5z" class="C"></path><path d="M552 695v-5c1-1 1-2 3-2l1 2c-1 3 0 5 0 7v1l3-2v2l-1 3v3c1 0 1-1 2-1v2 3h0c2 0 2-1 4 0 1 0 2 1 2 2 0 2 0 2-2 4l-8 3-1 1-1 1c-1 0-1-1-2-1 0-2 1-5 0-6 0-2-2-3-2-4v-4c-1-1-1-2-1-3v-2c-1-1 0-3 0-3 1-1 2-1 3-1z" class="P"></path><path d="M552 706l1-3c1 2 1 3 2 3l3 1-3 9c0-1-1-1-1-2v-2c-1-1-1-3-2-4v-2z" class="E"></path><path d="M558 704c1 0 1-1 2-1v2 3h0c2 0 2-1 4 0 1 0 2 1 2 2 0 2 0 2-2 4l-8 3-1 1v-2c1-2 2-6 3-9v-3z" class="q"></path><path d="M552 695v-5c1-1 1-2 3-2l1 2c-1 3 0 5 0 7v1l3-2v2l-1 3v3 3l-3-1c-1 0-1-1-2-3l-1 3c0-1-1-4 0-5v-1c-1-2 0-3 0-5z" class="f"></path><path d="M553 703c1-1 1-3 1-5h0l1-1v7h0l1 1 2-3v-1 3 3l-3-1c-1 0-1-1-2-3z" class="X"></path><path d="M551 631c1 2 3 2 4 3 0 1 1 1 1 1v3h1c0 3 0 5 2 7l2 2v5h0c0-2 0-7 1-8v6 13 4 24c0 4 0 8 1 12l-3 2v-2c-1 0-1 1-2 1v-3l1-3v-2l-3 2v-1c0-2-1-4 0-7h0c1-3-1-8 1-11h1c-1-2-1-3-1-5-1-3-1-5-1-8v-7l1-5c0-3 0-7-1-11l-1-4c-1-2-2-4-4-6v-2z" class="m"></path><path d="M559 698c2 2 1 3 1 5-1 0-1 1-2 1v-3l1-3z" class="b"></path><path d="M559 689c-1-3-1-6-1-8 1-3 1-3 3-4v4 2l-2-1c-1 2 0 5 0 7z" class="d"></path><path d="M556 666c1 3 1 6 2 10h0v-1l1-1c1-3-1-5 2-8v11c-2 1-2 1-3 4 0 2 0 5 1 8v7l-3 2v-1c0-2-1-4 0-7h0c1-3-1-8 1-11h1c-1-2-1-3-1-5-1-3-1-5-1-8z" class="p"></path><defs><linearGradient id="h" x1="562.657" y1="659.441" x2="554.729" y2="661.069" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#b3b1b1"></stop></linearGradient></defs><path fill="url(#h)" d="M551 631c1 2 3 2 4 3 0 1 1 1 1 1v3h1c0 3 0 5 2 7l2 2v5 1 8 5c-3 3-1 5-2 8l-1 1v1h0c-1-4-1-7-2-10v-7l1-5c0-3 0-7-1-11l-1-4c-1-2-2-4-4-6v-2z"></path><path d="M561 661c-2-2-2-4-2-7l1-1h1v8z" class="P"></path><defs><linearGradient id="i" x1="549.611" y1="662.571" x2="538.233" y2="633.825" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2a28"></stop></linearGradient></defs><path fill="url(#i)" d="M547 630c2 1 3 2 4 3 2 2 3 4 4 6l1 4c1 4 1 8 1 11l-1 3c-1 0-2 1-3 2v1l-1 1c0 2-1 4 0 6l1 4-1 1v-1c-1 0-1 0-1-1l1-1c-1-1-1-1-2-1-1-1-2-3-2-3v1c-2 0-5-2-6-2 0-1-1-2-1-3v-2h0c-2-1-2-1-3-2l-1-1h0c-2 0-2-1-3-2v-1c-1 0-2-1-3-1 1-2 1-4 1-6h-1c1-2 0-4 1-5 0-1 1-1 1-1v-3c1-1 2-1 3-2l1 1 2-2h-1 0v-1l1-1c1 1 2 1 4 1h0 2l1-1c0-1 1-1 1-2z"></path><path d="M540 655c-1-1-3-2-4-4-2-3-2-6-1-10l1 2h1c0 3 0 5 2 7l2 2-1 3z" class="o"></path><path d="M556 643c1 4 1 8 1 11l-1 3c-1 0-2 1-3 2v1l-1 1c0 2-1 4 0 6l1 4-1 1v-1c-1 0-1 0-1-1l1-1c-1-1-1-1-2-1-1-1-2-3-2-3v-1l1-1c0-2 0-3 2-4h1v-1c0-2 1-1 2-3 0-1 0-2 1-3l1-1v-8z" class="s"></path><path d="M551 638l1 2c1 5 0 9-2 13-2 2-4 3-7 3-1 0-2 0-3-1l1-3h4c2-1 3-2 5-4 1-3 0-5-1-7 2-1 2-2 2-3z" class="L"></path><path d="M547 630c2 1 3 2 4 3 2 2 3 4 4 6l-3 1-1-2c0 1 0 2-2 3 1 2 2 4 1 7-2 2-3 3-5 4h-4l-2-2c-2-2-2-4-2-7h-1l-1-2 2-5 2-2h-1 0v-1l1-1c1 1 2 1 4 1h0 2l1-1c0-1 1-1 1-2z" class="u"></path><path d="M539 634h2 0c2 1 3 1 5 2v1c-3 0-5 0-7 3-1 1-2 2-2 3h-1l-1-2 2-5 2-2z" class="X"></path><path d="M547 630c2 1 3 2 4 3 2 2 3 4 4 6l-3 1-1-2c0 1 0 2-2 3v-1l-3-3v-1c-2-1-3-1-5-2h0-2-1 0v-1l1-1c1 1 2 1 4 1h0 2l1-1c0-1 1-1 1-2z" class="F"></path><path d="M539 634h-1 0v-1l1-1c1 1 2 1 4 1h0 0c1 1 2 0 4 0 2 2 3 3 4 5 0 1 0 2-2 3v-1l-3-3v-1c-2-1-3-1-5-2h0-2z" class="l"></path><path d="M534 752v-1c-1 0-1 0-2-1 0-1 0-2 1-3h2c1 0 2 1 2 2h0c1-1 1-1 1-2l1-1c-1-1-1-1-3-1h0c-2-1-3-2-3-3 2 1 2 2 5 2 0 0 1 1 2 1h1c0 1 0 1 1 1 2 0 3-1 4-2l3-1v1c-1 2-2 3-2 5-1 1-1 2-2 3-1 0-1 1-1 2h-2c0 1 0 2 1 3h0c1 1 1 1 1 2v1s-2 1-2 2 0 1 1 2c1 2 0 6 0 8 0 1 1 2 1 3 0 2-1 3-1 4s1 2 1 3c0 3-1 5-1 8h1v1c1 0 1 0 2-1 2-1 3-2 5-2 1 0 2 0 3 1 1-1 2-1 4-2v1c1 2 1 4 1 7 0 2 1 4 2 6h0c-1 1-1 1 0 2v1 6 4h0l1 5-2 1v1h4v2l5 10v4 1c1 1 1 2 2 3l1 1c1 1 0 2 1 3l1 1c-1 2-1 1-1 3h-2l-1 1h-2l1 2c-1 1-1 1-1 2l1 1c1 2 1 4 0 6l2 2 1 1-1 1-9 6c-1 1-2 2-3 2l-1 1c-6-4-10-8-15-13h-2c-1 0-1 0-1-1v-7c-1-2-1-4-1-6v-15 3c-1-1-1-1-1-3 0-1 0 0-1-1 0-1 1-3 1-5v-1-1-3c-1-1 0 1-1-1l1-1v-4-3l-1-3v-1c0-1-1-3-2-4h0l2-2c1-1 1-2 1-3s0-2-1-4c0-1 0-1-1-2v-1l2-4v-1c-1-1-1-1-2-1v-1h2v-3l-1-1 1-1v-1c0-2 0-3 1-4h0c-1-2-1-2-3-2h-1v-2c-1-2-1-3-1-5s1-3-1-5v-2l-1-1c0-3 0-4 2-6z" class="M"></path><path d="M550 841h2c1 2 1 5 3 7 0 2 1 3 2 4l1 1v1h1 0l-1 2-3-3c0-2-1-1-2-2v-3l-2-2-1-5z" class="F"></path><path d="M555 853l3 3 1-2c0 2 0 3 1 4h1c1 1 1 1 2 1h1c-1 2-1 2-1 4h1c0 1 0 2-1 3 0 1 0 1-1 2h0l-1-1c0-1 0-2-1-2-2-4-4-8-5-12z" class="B"></path><path d="M560 858h1c1 1 1 1 2 1h1c-1 2-1 2-1 4h1c0 1 0 2-1 3 0 1 0 1-1 2h0l-1-1 1-3c1-1 0-2 0-3h-1v1h-1v-4z" class="D"></path><path d="M540 799l-1 33v3c-1-1-1-1-1-3 0-1 0 0-1-1 0-1 1-3 1-5v-1-1-3c-1-1 0 1-1-1l1-1v-4-3l-1-3v-1c0-1-1-3-2-4h0l2-2c1-1 1-2 1-3h2z" class="L"></path><path d="M535 804h0l2-2c1 2 1 5 0 6 0-1-1-3-2-4z" class="B"></path><path d="M546 790c2-1 3-2 5-2 1 0 2 0 3 1h0-1l-3 1h0c-1 1-3 1-4 2-1 2-1 6-1 9l1 1h0l-1 2 2 2c0 2-1 3-1 4l1 1h0v5l1 1-1 2c0-1-2-2-2-2-1-3 1-2-1-5 0 1 0 1-1 1-1-4 0-10 0-15v-8h1v1c1 0 1 0 2-1z" class="S"></path><path d="M564 859l1-7c0-1 1-2 2-2h1l1 2c-1 1-1 1-1 2l1 1c1 2 1 4 0 6l2 2 1 1-1 1-9 6c-1 1-2 2-3 2l-1-1c2-1 4-2 4-4h0c1-1 1-1 1-2 1-1 1-2 1-3h-1c0-2 0-2 1-4z" class="B"></path><path d="M568 858h1v1c-1 0-2 1-3 2v1h-1v-2c0-1 1-2 3-2z" class="C"></path><path d="M547 816v-5h0l-1-1c0-1 1-2 1-4l-2-2 1-2 1 2v-1h1v2c-1 2-1 3 0 5h0 1 1l-1 2h0c1 2 1 5 1 7h0c1-1 0-2 1-3h3v4c0 1-1 2-2 3h0l2 1v1l-6 6v1h-2c1-3 1-4 1-7v-6l1-2-1-1z" class="r"></path><path d="M547 816v-1l2-1v2c0 3 0 6 1 9l1 1c-1 2-2 3-3 4v1 1h-2c1-3 1-4 1-7v-6l1-2-1-1z" class="Y"></path><path d="M546 792c1-1 3-1 4-2-1 1-1 2-2 3 1 1 1 1 1 2v1 1l2 2v1l-1 2 1 1c1 0 2-1 4-2v1 1c-2 2-3 4-3 6 0 1 1 1 1 2v3l-1 1 2 1h0-3c-1 1 0 2-1 3h0c0-2 0-5-1-7h0l1-2h-1-1 0c-1-2-1-3 0-5v-2h-1v1l-1-2h0l-1-1c0-3 0-7 1-9z" class="v"></path><path d="M546 792c1-1 3-1 4-2-1 1-1 2-2 3 1 1 1 1 1 2v1 1c-1 1-1 2-1 4-1-1-1-3-1-4h-1v-5z" class="h"></path><path d="M558 787v1c1 2 1 4 1 7 0 2 1 4 2 6h0c-1 1-1 1 0 2l-2-1c0 1 0 1-1 1l-1-1h-2v-1c-2 1-3 2-4 2l-1-1 1-2v-1l-2-2v-1-1c0-1 0-1-1-2 1-1 1-2 2-3h0l3-1h1 0c1-1 2-1 4-2z" class="o"></path><path d="M558 787v1c-1 3 0 6-1 10v-5h-2v-3l-1-1h0c1-1 2-1 4-2z" class="l"></path><path d="M555 793h2v5c0 1-1 2 0 4h-2v-1c-2 1-3 2-4 2l-1-1 1-2h1l1-1c2-2 2-4 2-6z" class="m"></path><path d="M554 789l1 1v3c0 2 0 4-2 6l-1 1h-1v-1l-2-2v-1-1c0-1 0-1-1-2 1-1 1-2 2-3h0l3-1h1z" class="d"></path><path d="M549 795l1 1h1l1 1-1 2-2-2v-1-1z" class="m"></path><path d="M555 802h2l1 1c1 0 1 0 1-1l2 1v1 6 4h0l1 5-2 1v1l-1 1c-2 1-3 2-5 3v-1l-2-1h0c1-1 2-2 2-3v-4h0l-2-1 1-1v-3c0-1-1-1-1-2 0-2 1-4 3-6v-1z" class="h"></path><path d="M556 807h0l1-1c1-2 2-1 4-2v6c0-1-1-2-1-3-1 0-2 1-3 2 0 1 1 1 0 2v-3l-1-1z" class="y"></path><path d="M555 817c2 0 4 1 5 3h0v1l-1 1-2-2-1 2v1h-1v-6z" class="Y"></path><path d="M555 802h2l1 1c1 0 1 0 1-1l2 1v1c-2 1-3 0-4 2l-1 1h0v5h-1v-9-1z" class="d"></path><path d="M555 817c1-2 1-3 2-4 1 0 2 0 3 1h0 1 0l1 5-2 1h0c-1-2-3-3-5-3z" class="v"></path><path d="M538 750l1-2h0v1l1 50h-2c0-1 0-2-1-4 0-1 0-1-1-2v-1l2-4v-1c-1-1-1-1-2-1v-1h2v-3l-1-1 1-1v-1c0-2 0-3 1-4h0c-1-2-1-2-3-2h-1v-2c-1-2-1-3-1-5s1-3-1-5v-2l-1-1c0-3 0-4 2-6v1c1 0 1 0 2-1 0-1 1-1 2-2z" class="Z"></path><path d="M536 792l2-4v7h-1c0-1 0-1-1-2v-1z" class="L"></path><path d="M535 771v-4h1c1 2 1 3 2 4v1l-2 1h-1v-2z" class="p"></path><path d="M536 752c0-1 1-1 2-2v7l-1 1c1 1 1 2 1 3l-1-1-1-1v3c2 2 2 2 2 4h-1v-1l-1-1-2 2c0-2 1-3-1-5v-2l-1-1c0-3 0-4 2-6v1c1 0 1 0 2-1z" class="F"></path><path d="M533 759l-1-1c0-3 0-4 2-6v1c1 0 1 0 2-1v2 1l-1-1c-2 2-2 3-2 5z" class="S"></path><path d="M560 821h4v2l5 10v4 1c1 1 1 2 2 3l1 1c1 1 0 2 1 3l1 1c-1 2-1 1-1 3h-2l-1 1h-2-1c-1 0-2 1-2 2l-1 7h-1c-1 0-1 0-2-1h-1c-1-1-1-2-1-4h0-1v-1l-1-1c-1-1-2-2-2-4-2-2-2-5-3-7h-2l-1-8h0l-1-1v-1l6-6c2-1 3-2 5-3l1-1z" class="I"></path><path d="M566 835l1 1c1 1 1 1 0 2 1 2 2 3 3 4v2c1-1 1-1 1-2h1c1 1 0 2 1 3l1 1c-1 2-1 1-1 3h-2v-4c-1 0-1-1-2-1 0-1 0-1-1-2-3-2-2-2-1-4 0-1 0-2-1-3z" class="Y"></path><path d="M564 833l2 2c1 1 1 2 1 3-1 2-2 2 1 4 1 1 1 1 1 2 1 0 1 1 2 1l-4-1h1-1c-1 0-2 0-3 1 0 1 0 1-1 2v-4-1l-1 1-2-2v-1h2v-2c0-1-1-1-1-2h2c0-1 1-2 1-2v-1z" class="r"></path><path d="M560 832l1 1 2 1v-1h1v1s-1 1-1 2h-2c0 1 1 1 1 2v2h-2v1 2c-1 0-1-1-2 0l-1 1 1 1h0c-1 1-1 1-1 2-1 1-1 1-2 1-2-2-2-5-3-7v-3c-1-1-1-1 0-2h1 3v-2c2 0 3 0 4-2z" class="Y"></path><path d="M556 841h0l3-3 1 2v1 2c-1 0-1-1-2 0l-1 1v1c-1-1-1-2-1-4z" class="B"></path><path d="M552 841l1-1v-2h1c1 1 1 0 0 2v2h1l1-1c0 2 0 3 1 4v-1l1 1h0c-1 1-1 1-1 2-1 1-1 1-2 1-2-2-2-5-3-7z" class="J"></path><path d="M560 821h4v2h-2c-1 1-2 2-3 2v3l1 1 1-1v2h1c-1 1-1 2-2 2-1 2-2 2-4 2v2h-3-1c-1 1-1 1 0 2v3h-2l-1-8h0l-1-1v-1l6-6c2-1 3-2 5-3l1-1z" class="L"></path><path d="M561 830h1c-1 1-1 2-2 2-1 2-2 2-4 2l1-2c1-1 2-2 4-2z" class="r"></path><path d="M554 828c2-1 3-2 5-3v3l-3 3h-2v-3z" class="Z"></path><path d="M549 833c1-2 3-5 5-5v3c-1 0-1 0-2 1l-1 1 1 1 1-1 1 1-1 2h-1c-1 1-1 1 0 2v3h-2l-1-8z" class="I"></path><path d="M560 821h4v2h-2c-1 1-2 2-3 2-2 1-3 2-5 3-2 0-4 3-5 5h0l-1-1v-1l6-6c2-1 3-2 5-3l1-1z" class="a"></path><path d="M560 841l2 2 1-1v1 4c1-1 1-1 1-2 1-1 2-1 3-1h1-1l4 1v4l-1 1h-2-1c-1 0-2 1-2 2l-1 7h-1c-1 0-1 0-2-1h-1c-1-1-1-2-1-4h0-1v-1l-1-1c-1-1-2-2-2-4 1 0 1 0 2-1 0-1 0-1 1-2h0l-1-1 1-1c1-1 1 0 2 0v-2z" class="Z"></path><path d="M557 847h2l2-1h1c0 1 0 1-1 2h-1c0 1 1 1 1 2l-1 3-1 1h-1v-1l-1-1c-1-1-2-2-2-4 1 0 1 0 2-1z" class="Q"></path><path d="M567 844l4 1v4l-1 1h-2-1c-1 0-2 1-2 2l-1 7h-1c-1 0-1 0-2-1h-1c-1-1-1-2-1-4h0l1-1 2-1 1-3h0 2c0-1 1-2 2-3v-1-1z" class="J"></path><path d="M567 844l4 1v4l-1 1h-2v-1-1c-1-1-1-2-1-3v-1z" class="y"></path><path d="M574 295c-1-8 0-17 0-24l8-1h3l18 6c1 0 2 1 3 1h2c1 1 4 2 5 2 2 1 4 1 5 2 1 0 2 1 2 3 1 1 0 3 1 5l-1 1v4h0c0 2 0 0-1 2v1h0 0c-1 1 0 2-1 3 0 1 1 2 0 3v2 5c-1 2 1 4-1 6v4c1 2 1 15 0 17l-1 1v3l-2 2c1 2 1 3 3 4l2 1c0 2 0 5 1 7l-1 1h-1v7l-1 15v1 5l1 3-2 2h1l7-1 2 2c-3 1-6 1-9 2v11c0 2 1 5 0 7v2c-1 3 1 7 0 11 0 1 1 2 0 3v42l-1 1h-1c-1 4 0 9 0 13v6 9c0 2 0 15-1 16 0 5 1 7-1 11v1h1c0 1 0 2-1 3h0l1 1-2 1c-1 1 0 2 0 3 1 1 2 1 3 1l2 1v9 16h0l-1-2c-1-1-2-2-4-3l-2 2h0v-2c0-2-1-2-2-4l-5-5-2-2-6-7-6-7-2-2-1-2-7-7-7-8 2-3c-2-1-5-4-6-5-2-3-5-4-6-7v-21c-1 2-1 5-2 8v-5c0-1 0-2-1-4-2-8-1-18-1-26v-41-12c0-5-1-12 0-16v-4l2-1-1-1v-1l1-1c0-3 0-4-1-6 1 0 2 1 3 1 1-1 1-1 1-2l-1-2h-2c-1-1-2-1-4-1v-2l1-10 1-2v-1c1-2 2-5 3-7l1 1 1 1h11v-46z" class="u"></path><path d="M592 463l1 1 2 2c-1 1-2 1-2 3 0 1 0 1-1 2h0v-8z" class="M"></path><path d="M592 463c0-2 0-3 1-5v-3h1c0 3 0 6-1 9l-1-1z" class="S"></path><path d="M590 346c1-3 1-5 2-7v-1c0-1 0-1 1-1l1 2 1 1-3 4h0c-1 1-1 1-1 2h0-1z" class="e"></path><path d="M581 360s1 0 1 1v2l1 1c0 2 0 5-1 6-1-1-1-2-1-3v-2c-1-1 0-1 0-2-1-1-1-1 0-3z" class="H"></path><path d="M585 353h1l1 2c2 1 2 2 2 4h-2-1 0c-1-1-1-2-1-3h1v-1l-1-1v-1z" class="G"></path><path d="M586 282l1 2-1 2v1c-1-1-3-1-4 0l-1 2v-1-3-1c1-1 3-1 5-2zm-11 23c2 3 1 7 2 10v5 8h-1c-1-1 0-6 0-8v-6l-1-1v-8z" class="H"></path><path d="M588 298h0v3 2c1 1 1 1 1 2h2 1l-1 1-1 1h-2-1v1h-1l-1-1c1 0 1 0 2-1-1 0-1 0-1-1-1-2 1-5 2-7z" class="x"></path><path d="M586 506h1l1 1-1 1v2 1h0c0 3-1 4-2 6h-1l-1-1v-1c-1-2-1-4 0-5h1v4h1v-2c1-2 1-3 1-6z" class="M"></path><path d="M601 538h2 2 2c1 0 2 0 3 1 0 1-1 1 0 3v2h-1l-1-1v-3h0v-1l-1 1v3l-1 1-1-1c0-1-1-2-2-3-1 0-1 0-2-1v-1z" class="H"></path><path d="M593 506l1 1v2h2c1 1 0 2 0 3l-1 1-1-1c-1 0-2 0-2 1l-1 1h0v-2-3h0v-1l-2 2h0-1v-2c2-2 1-1 3-1 0 0 1-1 2-1z" class="C"></path><path d="M579 327c-1-1-1-1-1-2l2 1c1-1 1-1 1-3 1 0 2 1 3 2l2 3 3 2c1 1 0 1 1 1l-1 1-1 1h0c0-1-1-1-2-1l-2 1c-2-2-3-3-3-5l1-1v-1c-2 0-2 0-3 1z" class="e"></path><path d="M588 346s-1 0-1 1v1-1l-1-1c-1 0-1 0-2 1h0l-2-2c-1-1-1-3-1-4s1-2 2-3c0-1 0-2 1-3h1c0 1 0 1 1 2v1 3l1 1s-1 1-1 2l1 1 1 1z" class="x"></path><path d="M590 331l2 1c2 0 2-1 4-2h0c1-2 2-3 3-5l1 1c-1 1-1 2-2 3 0 1-1 2-1 3-1 0-1 0-1 1-2 1-3 2-5 1h-1v2c0 1 0 1-1 2l1 1c-1 2-2 4-2 6v1l-1-1-1-1c0-1 1-2 1-2l-1-1v-3l2-5h0l1-1 1-1z" class="K"></path><path d="M615 534c-4 1-12 1-17 0-1-1-2-1-2-2-1-2-1-4 0-5l1 2 3 1 1-1 1 1c2 0 3 0 5 1 1 0 1 0 2-1l1 1-1 1c1 1 1 1 3 1 1 1 2 1 3 1z" class="B"></path><path d="M597 417v1c1 1 2 2 2 4v3c0 1-1 2-2 3s-2 1-3 1c-1-1-2-1-2-2-1-1-1-2-1-3 1-1 1-2 2-3-1-1 0-1 0-2l4-2z" class="C"></path><path d="M579 327h-2v-1c0-1 0-1 1-1 0-1 1-2 2-3h-2v-1c2 0 3 1 4 1v-1c-1 0-2 0-4-1v-1-1-1c0-2 1-4 2-5s2-1 3-1c1-1 1-3 0-4v-1h1l1 1 1 1h1v2c-2 1-5 2-5 4-1 1 0 1 0 2h0l1-2h1c0 1 0 2-1 3-1 2 0 6 1 8-1-1-2-2-3-2 0 2 0 2-1 3l-2-1c0 1 0 1 1 2z" class="O"></path><path d="M589 388c2-2 4-4 6-4 3 1 5 3 7 5-2 0-4-1-5-2h0l2 2-1 2c-1-1-1 0-1-1l-1 1h1v2c-1 1-2 2-4 2-1 0-2-1-3-2s0-3 0-5h-1z" class="s"></path><path d="M590 388l4-2v1h-1c-1 1-2 3-2 4v1h1l2-2 2 1h1v2c-1 1-2 2-4 2-1 0-2-1-3-2s0-3 0-5z" class="u"></path><path d="M599 389l1 1v-1c1 3 0 6-1 8 0 1-2 3-4 3-1 1-2 0-4 0-2-2-3-4-4-6 0-2 1-4 2-6h1c0 2-1 4 0 5s2 2 3 2c2 0 3-1 4-2v-2h-1l1-1c0 1 0 0 1 1l1-2z" class="L"></path><path d="M574 295c1 3 1 7 1 10v8l1 1v6c0 2-1 7 0 8v5 2c-1 1 0 5 0 6v2c-1 3-1 6-1 9 1 1 1 1 1 3h-1v2c1 6 0 12 0 18-1-6 1-13 0-18-2-3 0-6-1-10v-6-46z" class="C"></path><path d="M593 506h-1l1-2 1 1v1c1-1 1-1 1-2l1-1h1c-1 1-1 2-1 3l2 1h1v-1h5l-4 4c-1 1-2 3-2 5s2 4 2 7l-1 1-1-1c-1 0-1-1-2-1h-3c-1 0-2-1-3-2h0v-1c-1-1 0-3 1-4h0l1-1c0-1 1-1 2-1l1 1 1-1c0-1 1-2 0-3h-2v-2l-1-1z" class="G"></path><path d="M597 317l-1-2h1l2 2v1c2 2 1 4 1 6 0 1 1 1 1 3-1 1-2 2-2 4h0l3-3v2c-1 1-1 1-1 2l-1 1h-1c0 1 0 2-1 4h0c1 1 1 1 0 2v2l-1 1c0-1-1-2-1-3 1-2 1-3 1-5l-1-1c0-1 0-1 1-1 0-1 1-2 1-3 1-1 1-2 2-3l-1-1c-1 2-2 3-3 5h0c-2 1-2 2-4 2l-2-1c-1 0 0 0-1-1l-3-2h2 5v-1l1-1c0-1 1-2 1-3l1-3c1-1 1-1 0-3h1z" class="a"></path><path d="M597 317c1 1 2 1 2 3v2c-2 2-1 2-2 4-2 3-5 4-8 4l-3-2h2 5v-1l1-1c0-1 1-2 1-3l1-3c1-1 1-1 0-3h1z" class="F"></path><path d="M598 489l-1-1c-1 0-2-1-3-2-1 0-1-1-2-1l-2 4-1-1s0-1 1-1c0-2 1-2 2-3-3-3-5-5-6-9l1-1v1c1 3 3 4 4 7l4-9c1-2 1-4 1-6 1-4 2-8 2-12l1 4 1 1v-3-1l2 5c0 2 0 5 1 7-1 2 0 4 0 6s-1 2-1 3v9c0 1 1 4 1 5-1 0-1 0-1 1h-1v-4c-1-6-1-14 0-20v-8l-1 1c0 1 0 3-1 5 0 1-1 1-1 2l-5 14v2l1 1 4 2v2z" class="Y"></path><path d="M593 482l5-14c0-1 1-1 1-2 1-2 1-4 1-5l1-1v8c-1 6-1 14 0 20v4c0 2 0 2 1 4-2 1-2 0-4 0 0-2 0-3 1-5h0l-1-2v-2l-4-2-1-1v-2z" class="O"></path><path d="M593 482l4-3v1 4c-1 1-2 1-3 1l-1-1v-2z" class="M"></path><path d="M602 425v-2-1l1 1c0 2 0 5 1 7l-1 9v5 13c0 1 0 3-1 4l-2-5v1 3l-1-1-1-4c1-10 1-20 3-30h1z" class="Z"></path><path d="M601 437c0 2 0 4 1 6l1 1v13c0 1 0 3-1 4l-2-5c1-1 1-3 1-4v-15z" class="C"></path><path d="M602 425v-2-1l1 1c0 2 0 5 1 7l-1 9v5l-1-1c-1-2-1-4-1-6l1-12z" class="H"></path><path d="M604 458v19 7l2 1v13l-1 1c0 1 0 2-1 3l1 2h-1-2-1l-1-1h-1v3 1h-1l-2-1c0-1 0-2 1-3s1-1 1-2v-1l1-2-1-2c2 0 2 1 4 0-1-2-1-2-1-4h1c0-1 0-1 1-1 0-1-1-4-1-5v-9c0-1 1-1 1-3s-1-4 0-6 0-7 1-10z" class="Q"></path><path d="M604 484l2 1v13l-1 1-1-15z" class="T"></path><path d="M599 498h1 1s1 0 2 1h-1l-1 1c1 1 2 1 2 3l-1 1h-1l-1-1h-1v3 1h-1l-2-1c0-1 0-2 1-3s1-1 1-2v-1l1-2z" class="C"></path><path d="M604 430l1-7h1 0v5l1 12v8c-1 1 0 4 0 6l-1 14c0 2 1 4 0 6v11l-2-1v-7-19c-1 3 0 8-1 10-1-2-1-5-1-7 1-1 1-3 1-4v-13-5l1-9z" class="k"></path><path d="M603 444v-5 13c0 2 1 4 1 6-1 3 0 8-1 10-1-2-1-5-1-7 1-1 1-3 1-4v-13z" class="B"></path><path d="M604 484c1-4 1-8 1-11 0-1-1-3 0-4h1v5 11l-2-1z" class="E"></path><path d="M587 310c1-1 3-1 5-1 0 1 1 2 2 2l3 2 1-1c1 1 1 1 1 2v1c1 2 2 1 4 1 0 1 0 2-1 3h0v1c1 1 1 1 1 2v1 1 2c-1 1-1 1-1 2l-3 3h0c0-2 1-3 2-4 0-2-1-2-1-3 0-2 1-4-1-6v-1l-2-2h-1l1 2h-1c1 2 1 2 0 3l-1 3c0 1-1 2-1 3l-1 1v1h-5-2l-2-3c-1-2-2-6-1-8 1-1 1-2 1-3h-1l-1 2h0c0-1-1-1 0-2 0-2 3-3 5-4z" class="Q"></path><path d="M587 315l1 1v1l-1 2-3-1 3-3z" class="G"></path><path d="M588 317l1 1h-1c0 1 0 2-1 2v1c0 1 1 2 2 3h1c1 1 1 1 3 1l1 1-1 1v1h-5l-4-4v-6l3 1 1-2z" class="S"></path><path d="M588 317l1 1h-1c0 1 0 2-1 2v1c0 1 1 2 2 3h1c1 1 1 1 3 1l1 1-1 1h-3c-2-1-2-1-3-3s-1-3 0-5l1-2z" class="L"></path><path d="M587 315c1-1 2-2 4-1h1 0c2 1 3 2 4 3 1 2 1 2 0 3l-1 3c0 1-1 2-1 3l-1-1c-2 0-2 0-3-1h-1c-1-1-2-2-2-3v-1c1 0 1-1 1-2h1l-1-1v-1l-1-1z" class="a"></path><path d="M590 324c1-1 2-2 3-2h1v-1c-1-1-1-2-2-3h-2c1-1 2-1 2-1 2 0 3 2 4 3l-1 3c0 1-1 2-1 3l-1-1c-2 0-2 0-3-1z" class="o"></path><path d="M596 343h3c0-2 0-1 1-2h2l2 2 2-2c1 0 1 0 2-1l1 1-1 1h0c-1 1-2 1-2 2h-1l-1 1 3 3v1l3 2c1 1 1 2 1 3 1 1 1 1 1 2v3c-2-2-1-3-2-5s-2-4-3-5h-1c-1-1-1-2-3-2v2h0v1c1 2 0 5 1 8v26 2 10c-1 2-1 5-1 8-1-2 0-6 0-8l-1-1-1-28c-1-2-1-3-1-5v-7h-1c1-2 0-2 0-3h-1c-1 1 0 4 0 6v2h0c0 1 0 1-1 1l-3-2v-1c-1 1-1 1-2 1l-1-7c-1-2-1-4-1-6h1 0c0-1 0-1 1-2h0v2c2 0 3-2 4-3z" class="O"></path><path d="M594 351h1s1-1 2-1v1 1 4c0 2 0 3 1 4 0 1 0 1-1 1l-3-2 1-1c-1-2-1-2 0-3 1-3-1-2-2-4h1z" class="B"></path><path d="M601 367c0-3 0-7-1-10v-3h1c1 1 1 0 1 1v3c1 1 0 4 1 6h-1v3c1 1 1 2 1 3v9c1 4 0 7 0 11 0 2 1 4 1 6-1 2-1 5-1 8-1-2 0-6 0-8l-1-1-1-28z" class="D"></path><path d="M596 343c3 0 2 0 4 2 0 2 0 2-1 3s-1 1-2 1c-1-1-2-2-3-2v4h-1c1 2 3 1 2 4-1 1-1 1 0 3l-1 1v-1c-1 1-1 1-2 1l-1-7c-1-2-1-4-1-6h1 0c0-1 0-1 1-2h0v2c2 0 3-2 4-3z" class="a"></path><path d="M599 506v-3h1l1 1h1 2 1 2l1 1h-2l-2 1v2h1c2-1 3-1 5-1 2 2 3 3 4 6 0 5 1 7-1 11v1h1c0 1 0 2-1 3h0l1 1-2 1c-1 1 0 2 0 3-2 0-2 0-3-1l1-1-1-1c-1 1-1 1-2 1-2-1-3-1-5-1l-1-1-1 1-3-1-1-2 1-2h1v-3l1 1 1-1c0-3-2-5-2-7s1-4 2-5l4-4h-5z" class="K"></path><path d="M602 530c0-2 0-3 2-4 0 1 1 1 2 2l3 1v1c-1 1-1 1-2 1-2-1-3-1-5-1z" class="C"></path><path d="M598 522l1 1c1 0 1 0 1 1 1 2 2 3 1 5l-1 1-3-1-1-2 1-2h1v-3z" class="Q"></path><path d="M599 506v-3h1l1 1h1 2 1 2l1 1h-2l-2 1v2h1l1 1h1 2v1h-1-2c-1 1-2 1-2 3l-1 1v5c1 1 1 1 1 2v2c-2-2-3-4-3-6 0-1 0-2-1-3 0 2 0 5 1 7 1 1 1 2 1 3-1 0-1-1-2-2 0-3-2-5-2-7s1-4 2-5l4-4h-5z" class="Z"></path><path d="M605 508c2-1 3-1 5-1 2 2 3 3 4 6 0 5 1 7-1 11-2 1-3 1-5 1-2 1-3-1-4-2v-2c0-1 0-1-1-2v-5l1-1c0-2 1-2 2-3h2 1v-1h-2-1l-1-1z" class="C"></path><path d="M607 511h3v2c0 1 1 1 1 2 0 2 0 3-1 4-2 0-2 0-3 1l-1-1v-2c0-3 0-4 1-6z" class="H"></path><path d="M605 508c2-1 3-1 5-1 2 2 3 3 4 6 0 5 1 7-1 11-2 1-3 1-5 1-2 1-3-1-4-2v-2c0-1 0-1-1-2v-5l1-1c0-2 1-2 2-3h2c-1 1-3 2-3 3-1 2-1 4 0 6 0 2 2 2 3 3l3-1c1 0 1 0 1-1 2-2 1-6 0-8s-1-2-3-3h-2-1l-1-1z" class="w"></path><path d="M586 282c-1 0-1-1-2-1-1-1-2-1-2-1-1-1-3-3-3-5 2 0 4 0 6 1 4 1 9 2 13 4h2l8 2-1 3h0c-1 0-1 0-1-1h-1v2c-1 1-2 1-3 2l-1 1v-1h-2l-1 1c-1 1-1 2-2 3l1 1c0 1-2 2-1 2 1 2 1 3 2 6l-2 2c0 1 0 2-1 3v1c-1 1-1 2-1 4-1 0-2-1-2-2-2 0-4 0-5 1v-2-1h1 2l1-1 1-1h-1-2c0-1 0-1-1-2v-2-3h0l-1-1c-1-2 0-6 0-8 0-1 0-1-1-2v-1l1-2-1-2z" class="C"></path><path d="M596 295c1 2 1 3 2 6l-2 2c-1-3-1-5 0-8z" class="O"></path><path d="M587 289l1 4c1-1 0-2 2-3 0 2 0 3 2 4v1h-2v2l-1-1v-1h-1l-1 2c-1-2 0-6 0-8z" class="M"></path><path d="M587 284c2 2 3 2 3 5v1c-2 1-1 2-2 3l-1-4c0-1 0-1-1-2v-1l1-2z" class="G"></path><path d="M596 287c0 1 0 1-1 2s-1 1-2 0c-1-3 1-3 2-5l-2-2c2-1 3-1 4-1 0 2-1 3-1 6z" class="D"></path><path d="M587 297l1-2h1v1l1 1v2c1-1 1-1 1-2h1l2 1c0 1 0 2-1 2l-1 1v3 1h-1-2c0-1 0-1-1-2v-2-3h0l-1-1z" class="G"></path><path d="M598 280h2l8 2-1 3h0c-1 0-1 0-1-1h-1v2c-1 1-2 1-3 2l-1 1v-1h0 0l-3-3-1 1-1 1c0-3 1-4 1-6l1-1z" class="D"></path><path d="M601 283h2v1l-1 1h-1v-2z" class="I"></path><path d="M598 280h2c-1 2-1 2 0 3h-2v2l-1 1-1 1c0-3 1-4 1-6l1-1z" class="G"></path><path d="M603 349v-2c2 0 2 1 3 2h1c1 1 2 3 3 5s0 3 2 5c1 1 1 29 1 32-1 0-1 1-1 1v1l-1 1v4c1 0 2 1 2 2v8c-1 2-1 5-1 7l-1-1v7l-1 6h-1v2c0 1 1 3 0 4v7 1c0 1 0 0-1 1v7 1c1 1 0 3 0 4l-1-1 1-1c1-4 0-10-1-13-1-4 1-8 0-11h-1v-5h0-1l-1 7c-1-2-1-5-1-7l-1-1v1 2h-1l1-4c1-8 0-18 0-26l1 1c0 2-1 6 0 8 0-3 0-6 1-8v-10-2-26c-1-3 0-6-1-8v-1h0z" class="M"></path><path d="M611 398c1 0 2 1 2 2v8c-1 2-1 5-1 7l-1-1h0v-1l-1 1-1-1c0-2 0-6 1-8 2-2 1-4 1-7z" class="x"></path><path d="M606 379c0 1 0 3 1 4 0 1 0 1-1 2-1 3 0 8 0 12l1 1h-1v8 5c-1 0-1-2-2-4 1-1 0-4 0-6 0-5 1-10 0-15v-2h1c1-2 0-4 1-5z" class="Y"></path><path d="M603 349c1 2 2 4 2 6 1 6 1 12 1 18v6c-1 1 0 3-1 5h-1v-26c-1-3 0-6-1-8v-1h0z" class="s"></path><path d="M604 386c1 5 0 10 0 15 0 2 1 5 0 6 1 2 1 4 2 4v1 7c0 1 1 3 0 4h0-1l-1 7c-1-2-1-5-1-7l-1-1v1 2h-1l1-4c1-8 0-18 0-26l1 1c0 2-1 6 0 8 0-3 0-6 1-8v-10z" class="B"></path><path d="M603 413c0-3 0-5 1-8v2h0c1 2 1 4 2 4v1l-2 4v1c-1 0-1-3-1-4z" class="x"></path><path d="M603 413c0 1 0 4 1 4v-1l2-4v7c0 1 1 3 0 4h0-1l-1 7c-1-2-1-5-1-7l-1-1v1 2h-1l1-4v1c2-1 1-7 1-9z" class="G"></path><path d="M608 282l4 2h0 3c1 1 2 1 3 1 1 1 0 1 0 3h-1l1 2 1 1c-1 2-1 4 0 6h0c-1 1 0 2-1 3 0 1 1 2 0 3v2 5c-1 2 1 4-1 6v4c1 2 1 15 0 17l-1 1v3l-2 2-2 2-1-1c2-1 3-2 4-4l-1-1c-1 0-1 0-2-1l2-1h1v-1c-1-3 0-5-1-8h0v-3c-1 0-1 0-2-1h-3c-1 1-1 1-2 1v1h-1l-1-1c1-1 1-1 1-2-1 0-1 0-3 1v-1-1c0-1 0-1-1-2v-1h0c1-1 1-2 1-3-2 0-3 1-4-1v-1c0-1 0-1-1-2l-1 1-3-2c0-2 0-3 1-4v-1c1-1 1-2 1-3l2-2c-1-3-1-4-2-6-1 0 1-1 1-2l-1-1c1-1 1-2 2-3l1-1h2v1l1-1c1-1 2-1 3-2v-2h1c0 1 0 1 1 1h0l1-3z" class="e"></path><path d="M607 325c0-2 1-3 2-5l2 2-2 2c-1 1-1 1-2 1z" class="S"></path><path d="M603 316l1 1 2-1v5 1l-3-3h-1 0c1-1 1-2 1-3z" class="x"></path><path d="M611 322c0-2 0-3-1-4v-5c2 2 2 3 3 6v1 2l1 1v2c-1 0-1 0-2-1h-3l2-2z" class="O"></path><path d="M598 301c0-1 1-1 2-1v3c1 1 0 1 0 2l1 1c1 0 1 0 2-1h-1c0-1 0-1-1-2l1-3 2 2c0 1 0 2 1 3 0 2 0 4 1 6v1h0v4l-2 1-1-1c-2 0-3 1-4-1v-1c0-1 0-1-1-2l-1 1-3-2c0-2 0-3 1-4v-1c1-1 1-2 1-3l2-2z" class="H"></path><path d="M603 316v-1c0-2 0-2 1-3h2v4l-2 1-1-1z" class="M"></path><path d="M608 282l4 2c-2 1-2 2-3 4h1v3 2h1c-1 1-1 3-1 3 0 2 1 4 1 6h-2c0 2 0 5-1 7l-1-3c-1-2-1-2-1-4 0-1 0-2-1-2-1-1-2-2-3-2v1 1l-1 3c1 1 1 1 1 2h1c-1 1-1 1-2 1l-1-1c0-1 1-1 0-2v-3c-1 0-2 0-2 1-1-3-1-4-2-6-1 0 1-1 1-2l-1-1c1-1 1-2 2-3l1-1h2v1l1-1c1-1 2-1 3-2v-2h1c0 1 0 1 1 1h0l1-3z" class="I"></path><path d="M600 294h1c1 1 1 1 2 0l1 1-1 1c-1 0-3 1-4 0 1 0 1-1 1-2zm9 8c0-1 0-2-1-4v-1l2-1c0 2 1 4 1 6h-2z" class="D"></path><path d="M610 293l-2 2-1-1v-5h1l2 2v2z" class="J"></path><path d="M596 295c-1 0 1-1 1-2l-1-1c1-1 1-2 2-3v1 3 1l2-3c1 1 0 2 0 3s0 2-1 2c0 2 0 2 1 3v1c-1 0-2 0-2 1-1-3-1-4-2-6zm16-11h0 3c1 1 2 1 3 1 1 1 0 1 0 3h-1l1 2 1 1c-1 2-1 4 0 6h0c-1 1 0 2-1 3 0 1 1 2 0 3v2 5c-1 2 1 4-1 6v4c1 2 1 15 0 17l-1 1v3l-2 2-2 2-1-1c2-1 3-2 4-4l-1-1c-1 0-1 0-2-1l2-1h1v-1-3l1-1v-4c0-1 0-1-1-2 1-2 1-4 0-6v-2c0-2-2-4-2-7h0l-1-2v-3h-2-1v4c0 1 1 2 1 3h-1l-1-1v-3c1-2 1-5 1-7h2c0-2-1-4-1-6 0 0 0-2 1-3h-1v-2-3h-1c1-2 1-3 3-4z" class="B"></path><path d="M611 293l1 1c0 1 0 3-1 4h1v6h0c-1-1-1-1-1-2 0-2-1-4-1-6 0 0 0-2 1-3z" class="e"></path><path d="M618 300c-1 0-1 0-2 1l-1-1c0-2-1-4 0-5 2 0 2 1 4 2-1 1 0 2-1 3z" class="I"></path><path d="M612 284h3c1 1 2 1 3 1 1 1 0 1 0 3h-1l1 2v1h0-2-1c-1 0-1 0-1-1s0-1-1-2c0-2 0-3-1-4z" class="D"></path><defs><linearGradient id="j" x1="592.208" y1="407.916" x2="633.407" y2="422.765" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#322e27"></stop></linearGradient></defs><path fill="url(#j)" d="M610 351l2-1h1v1l1 1v2h0c0-1 0-1 1-2 1 1 0 4 2 4h1v7l-1 15v1 5l1 3-2 2h1l7-1 2 2c-3 1-6 1-9 2v11c0 2 1 5 0 7v2c-1 3 1 7 0 11 0 1 1 2 0 3v42l-1 1h-1c-1 4 0 9 0 13v6 9c0 2 0 15-1 16-1-3-2-4-4-6-2 0-3 0-5 1h-1v-2l2-1h2l-1-1h-2l-1-2c1-1 1-2 1-3l1-1v-13-11c1-2 0-4 0-6l1-14c0-2-1-5 0-6v-8l-1-12h1c1 3-1 7 0 11 1 3 2 9 1 13l-1 1 1 1c0-1 1-3 0-4v-1-7c1-1 1 0 1-1v-1-7c1-1 0-3 0-4v-2h1l1-6v-7l1 1c0-2 0-5 1-7v-8c0-1-1-2-2-2v-4l1-1v-1s0-1 1-1c0-3 0-31-1-32v-3c0-1 0-1-1-2 0-1 0-2-1-3z"></path><path d="M616 415c0-3-1-7 0-10v-4c0-3 0-7 1-9v11c0 2 1 5 0 7s-1 3-1 5h0z" class="G"></path><path d="M616 415h0c0-2 0-3 1-5v2c-1 3 1 7 0 11 0 1 1 2 0 3l-2 2 1-13z" class="w"></path><path d="M614 354c0-1 0-1 1-2 1 1 0 4 2 4h1v7h-1 0l-1 1-1 1-1-11z" class="H"></path><path d="M612 463v18h1c-1 2-1 3-1 5l1 1v1c-1 2 0 6-2 7v-1c-1-1-1-2-1-2-1-2-1-5 0-7h0l1-1c1-2 0-4 1-6 0-5-1-10 0-15z" class="o"></path><path d="M615 365l1-1 1-1h0 1l-1 15v1 5l1 3-2 2v-1c-1-3-2-21-1-23z" class="a"></path><path d="M615 428l2-2v42l-1 1h-1v-41z" class="q"></path><path d="M613 487v-4c1-2 0-4 1-5 0 1 1 2 1 4v6 9c0 2 0 15-1 16-1-3-2-4-4-6-2 0-3 0-5 1h-1v-2l2-1c1 1 2 1 3 1 1-1 1-2 2-3 1-2 0-6 0-8 2-1 1-5 2-7v-1z" class="G"></path><path d="M611 495c2-1 1-5 2-7 0 3 1 18 0 20-2-1-2-1-2-2v-3c1-2 0-6 0-8z" class="Z"></path><path d="M611 414l1 1v48c-1 5 0 10 0 15-1 2 0 4-1 6l-1 1h0v-5-53l1-6v-7z" class="P"></path><defs><linearGradient id="k" x1="602.2" y1="462.271" x2="612.508" y2="470.982" xlink:href="#B"><stop offset="0" stop-color="#333336"></stop><stop offset="1" stop-color="#55544e"></stop></linearGradient></defs><path fill="url(#k)" d="M606 428h1c1 3-1 7 0 11 1 3 2 9 1 13l-1 1 1 1c0-1 1-3 0-4v-1-7c1-1 1 0 1-1v-1-7c1-1 0-3 0-4v-2h1v53 5c-1 2-1 5 0 7 0 0 0 1 1 2v1c0 2 1 6 0 8-1 1-1 2-2 3-1 0-2 0-3-1h2l-1-1h-2l-1-2c1-1 1-2 1-3l1-1v-13-11c1-2 0-4 0-6l1-14c0-2-1-5 0-6v-8l-1-12z"></path><path d="M610 480v5c-1 2-1 5 0 7 0 0 0 1 1 2v1c0 2 1 6 0 8-1 1-1 2-2 3-1 0-2 0-3-1h2l-1-1h-2l-1-2c1-1 1-2 1-3l1-1h2c1-2 0-7 1-10 0-2 0-6 1-8z" class="J"></path><path d="M558 347v-1c1-2 2-5 3-7l1 1 1 1h11v6c1 4-1 7 1 10 1 5-1 12 0 18 0 5 0 10-1 15v81c1 12 0 25 0 37-2-1-5-4-6-5-2-3-5-4-6-7v-21c-1 2-1 5-2 8v-5c0-1 0-2-1-4-2-8-1-18-1-26v-41-12c0-5-1-12 0-16v-4l2-1-1-1v-1l1-1c0-3 0-4-1-6 1 0 2 1 3 1 1-1 1-1 1-2l-1-2h-2c-1-1-2-1-4-1v-2l1-10 1-2z" class="j"></path><path d="M569 371h1c0 3 1 6 0 9-1-1-1-2-1-3v-1l-1-1-1 4v-1-6l2-1z" class="X"></path><path d="M560 373l2 1h1v2l-1 56c-1-3-1-7-1-10v-18-24h-1v4c-1 2 0 4-1 7 0 1 0 3-1 4 0-5-1-12 0-16v-4l2-1v-1z" class="p"></path><path d="M560 373l2 1h1v2l-1 3h-1-3v-4l2-1v-1z" class="S"></path><path d="M574 471c1 12 0 25 0 37-2-1-5-4-6-5-2-3-5-4-6-7 2 0 2-1 4-1 1 1 0 3 1 4l1-1c1-1 2-2 3-4s1-5 1-7c1-5 1-10 2-15v-1zm-14-87v-4h1v24 18c0 3 0 7 1 10v9 3 31c-1 2-1 5-2 8v-5c0-1 0-2-1-4-2-8-1-18-1-26v-41-12c1-1 1-3 1-4 1-3 0-5 1-7z" class="X"></path><path d="M558 395c1-1 1-3 1-4 1-3 0-5 1-7v20c-1 3-1 5-1 7-1 6 0 12 0 17 0 6-1 12 0 18v6l1 13c0 2 0 5 1 7v-4-2-8c0-4-1-11 1-14v31c-1 2-1 5-2 8v-5c0-1 0-2-1-4-2-8-1-18-1-26v-41-12z" class="b"></path><defs><linearGradient id="l" x1="573.756" y1="344.825" x2="569.121" y2="369.837" xlink:href="#B"><stop offset="0" stop-color="#a4a2a2"></stop><stop offset="1" stop-color="#ccc"></stop></linearGradient></defs><path fill="url(#l)" d="M558 347v-1c1-2 2-5 3-7l1 1 1 1h11v6c1 4-1 7 1 10 1 5-1 12 0 18 0 5 0 10-1 15h0l-1-1v-2-7c-1-3-1-6-1-9h-1v6 12h0c-2-1-1-7-1-9 1-3 0-6 0-9h-1c0-1 0-2-1-2l-2-1c-2 2-3 2-3 5v1h0-1l-2-1v1l-1-1v-1l1-1c0-3 0-4-1-6 1 0 2 1 3 1 1-1 1-1 1-2l-1-2h-2c-1-1-2-1-4-1v-2l1-10 1-2z"></path><path d="M574 347c1 4-1 7 1 10 1 5-1 12 0 18 0 5 0 10-1 15h0l-1-22c0-7-1-14 1-21z" class="I"></path><path d="M566 341l4 1c1 1 0 3 0 4v13h-1c-1-1-1-2-2-2h-1c-1-2 0-3 0-5v-2h-1l1-1v-2-2-4z" class="F"></path><path d="M558 347v-1c1-2 2-5 3-7l1 1 1 1h0 3v4 2 2l-1 1h1v2c0 2-1 3 0 5h1c1 0 1 1 2 2h1c0 3 0 5-1 8-1 0-2 1-3 1-2 2-3 2-3 5v1h0-1l-2-1v1l-1-1v-1l1-1c0-3 0-4-1-6 1 0 2 1 3 1 1-1 1-1 1-2l-1-2h-2c-1-1-2-1-4-1v-2l1-10 1-2z" class="J"></path><path d="M563 341h3v4c-1-1 0-1-1-1h-1v-2l-1-1z" class="L"></path><path d="M562 362h1l1-1c-1-1-1-2-2-3v-2c2 2 4 3 3 6 0 1 0 2-1 3l-1-1-1-2z" class="Y"></path><path d="M563 364l1 1c-1 2-1 5-3 7l-1 1v1l-1-1v-1l1-1c0-3 0-4-1-6 1 0 2 1 3 1 1-1 1-1 1-2z" class="V"></path><path d="M557 349c1 1 2 1 2 2v3 2 3c0 1 1 2 1 3-1-1-2-1-4-1v-2l1-10z" class="Z"></path><path d="M352 144h3l3-1c2 0 7-1 10 0h0c3-1 8 1 11 1 2 0 3 0 5-1l3 1c12 2 26 6 35 14l8 5c4 3 7 6 10 9 2 2 3 3 3 5v1l3 3c2 3 4 7 5 10-1 1-2 1-3 1l-1 1h-2l-2 2h0l-1 2v1c-1 1-1 2-2 4 0 0-2 1-2 2-1 1 0 1-1 2l-2 2-1 1h-1c-1 1-2 1-3 2h-2v-2c-2 2-3 4-5 5l-1 1h-2v1c-2 0-2 0-3-1-2 0-3 0-4-2h-1l-1 1v2l3 6c2 5 2 12 3 17l10-6h0l-8 6c-2 1-2 2-3 4-6-2-11-5-17-7-28-7-59 1-85 13-21 9-42 23-58 40-9 10-17 21-24 33-5 9-11 19-14 29-7 18-10 39-13 59l-1 14c0 3-1 5-1 8-1-8 0-15 0-22l1-35 1-4v-25c0-2 1-6 1-8l1-27c0-8 1-15 3-23-1-1 0-4 1-6l-2-1-4-2-2-2v-1h-1l1-1c1-6 0-11 2-16v-1c-1-2-1-4 0-6h-1l-1-1c2-1 3-3 4-5h0v-1l1-3c2 0 3 0 4 2 0-2 3-9 4-11 0-1-1-3-1-3l1-1v-3c0-2 6-11 7-14 2-2 3-4 4-6 2-2 3-4 5-6l2-4c1-2 2-3 3-4l1-2h2c1 1 1 1 1 2l2-1v2l1 1 4 4 15-16c3-2 5-4 7-5 5-3 8-8 14-8 1 0 5-4 6-5l12-6c11-6 23-9 35-10v-3l1 1h0c2-1 3-1 6-1l4-1h4z" class="M"></path><path d="M390 214h2v2h-1l-2-1v-1h1z" class="O"></path><path d="M312 163c1 0 2 1 4 2l-1 1h-3v-3z" class="a"></path><path d="M331 164h1l2 3-1 2c-1-1-1-1-2-1 0-1-1-2-1-3h0l1-1z" class="x"></path><path d="M289 189c1 0 0-1 1-2l2 1h1c1 1 1 2 1 3h0-1l-1 1c-2-1-2-2-3-3z" class="H"></path><path d="M300 187c0-4-3-7-4-11 3 2 4 5 6 8l-1 1s1 1 0 2h-1z" class="C"></path><path d="M262 196c-1 0-2 0-3-1 1-1 1-1 3-1l1 1h2 1c1 2 1 3 1 5 0 1 0 1-1 1s-2 0-3-1v-1c-1 0-2-1-3-2l2-1z" class="H"></path><path d="M262 196c-1 0-2 0-3-1 1-1 1-1 3-1l1 1h0c1 1 0 1 1 2l1 1-1 1c0-1-1-2-2-3z" class="O"></path><path d="M379 199c3 0 6 1 9 2s6 1 9 3l-3 1-15-4v-2z" class="c"></path><path d="M306 187c-2-4-4-8-6-11-1-2-3-4-3-6l2-1v1 1c1 1 1 2 2 3h0c0 1 1 2 1 2l2 3c1 1 2 3 3 4l2 2 1 1h-1v2c-1-1 0-1-1-2l-2 1z" class="F"></path><path d="M252 215v-1h1l-1-1c0 1-1 1-2 1l-1-1 3-1 1-1c-2-1-4-3-5-4h1c2 1 5 2 6 4v1l5 6s-1 0-2 1l2 2c-1 0-1 0-2 1h-1l-1-1c1-1 1-1 2-1-1-1-2-3-3-3l-3-2z" class="C"></path><path d="M394 205l3-1 8 3 6 4v1 1l-1 1c-6-4-10-7-16-9z" class="i"></path><path d="M284 191h1v-1c1-1 1-1 1-2-1-1-2-2-2-3v-1l4 4 1 1c1 1 1 2 3 3l1-1v1l-1 1-1 2s-1-2-2-3c-1 2-1 2-1 4h-1c-1-1-1 0-1-1h-1-1l-2-1v-4c1 0 2 0 2 1z" class="a"></path><path d="M284 191h1v-1c1-1 1-1 1-2-1-1-2-2-2-3v-1l4 4v1s1 2 1 3c-1 0-1 1-2 2l-1-1h-1c0 1 0 0-1 1l-1-1 1-2z" class="H"></path><path d="M308 213c2-1 4-2 6-2 1-1 1-1 2 0-8 5-16 8-24 13-2 2-8 8-10 8l-2-2h2l1-1 4-3c3-3 7-5 11-8 1 0 2-1 3-2l4-2c1 0 2-1 3-1z" class="P"></path><path d="M255 211l7 6 8 7c0 2 0 3-1 4v-2h0c-1 1-2 2-2 3l-3-1v-1l-2 2v-1l-1-3h-2l-1 1v1l-1-1 1-2h1l-1-2c1-1 1-1 2-1l-2-2c1-1 2-1 2-1l-5-6v-1z" class="I"></path><path d="M260 218c2 1 3 2 4 3s1 1 1 2 0 1-1 2l-1-1c0-1-1-1-1-1l-1 1c2 1 3 0 5 1v1 1c-1 0-1 0-2 1v-1l-2 2v-1l-1-3h-2l-1 1v1l-1-1 1-2h1l-1-2c1-1 1-1 2-1l-2-2c1-1 2-1 2-1z" class="B"></path><path d="M222 242c1 0 1 0 1-1h0l1-1c0-2 2-3 3-5l-5 13c0 2-1 3-1 5-1 1-1 1-2 3l3 1v1h-1c-2 0-2 0-4-1v1l-3-2 3-9c0-1 0-3 1-4 2 0 2 1 4-1z" class="J"></path><path d="M218 243c2 0 2 1 4-1-2 5-4 9-5 14v1 1l-3-2 3-9c0-1 0-3 1-4z" class="V"></path><defs><linearGradient id="m" x1="222.657" y1="297.126" x2="225.71" y2="311.613" xlink:href="#B"><stop offset="0" stop-color="#605e5c"></stop><stop offset="1" stop-color="#757676"></stop></linearGradient></defs><path fill="url(#m)" d="M222 306c3-6 7-12 10-18l1 1c-6 12-10 24-15 36l-4 12c-1 2-2 4-2 6h-1c2-8 4-15 7-23 1-1 2-3 2-4 0-2 1-2 0-4-2 0-3-1-5-1-2-1-4-1-6-2h0 3l1-1 1-2c0 1 0 2 1 2h1l2-1h0l2 1h0v-2l1-1v2c0-1 1-1 1-1z"></path><path d="M214 306c0 1 0 2 1 2h1l2-1h0l2 1h0v-2l1-1v2c0-1 1-1 1-1l-2 5-8-2h0l1-1 1-2z" class="B"></path><path d="M242 205c2 0 5-3 6-5l1 1c-1 1-2 2-2 4l-3 3c-6 8-13 18-17 27-1 2-3 3-3 5l-1 1h0c0 1 0 1-1 1-2 2-2 1-4 1 0-1 1-2 1-3l1-3 1-4c1-1 2-3 4-4s3-3 4-5c5-6 8-13 13-19z" class="h"></path><path d="M282 194l2 1h1 1c0 1 0 0 1 1h1c0-2 0-2 1-4 1 1 2 3 2 3l1-2c0 1 0 3-1 3-1 1-1 1-1 3-1 0-1 1-2 1 0 1 0 2-1 3v1 2c-2 0-2 1-4 0l-1-2h-5l-1-1 2-1-1-1v1c-2 0-2 0-3-1v2h-3v-1l2-1h0c-1-2-2-3-4-3l-2 2c0-2 0-3-1-5h2l2-1 1 2c1 0 1-1 2-1 2 1 3 1 5 1 1-1 1-2 2-3l2 1z" class="e"></path><path d="M274 201v-2h0l1-1 3 1s-1 1-1 2v1c-2 0-2 0-3-1z" class="H"></path><defs><linearGradient id="n" x1="247.593" y1="274.431" x2="273.283" y2="229.786" xlink:href="#B"><stop offset="0" stop-color="#4e4f4c"></stop><stop offset="1" stop-color="#787778"></stop></linearGradient></defs><path fill="url(#n)" d="M278 231h1l1 2c-3 4-7 7-11 10-9 9-19 19-25 30-2 0-2 0-3-1-1-2-6-5-8-6l1-1c1-1 1-1 2-1l2-1 2 1h0c2 0 2-1 3 0l3 1 1-1 5-6c1-1 5-6 6-8l11-11 6-5 3-3z"></path><path d="M236 264l2-1 2 1h0c2 0 2-1 3 0l3 1-4 6-8-6c1-1 1-1 2-1z" class="S"></path><path d="M236 264l2-1 2 1h0v2c-1 0-1-1-2-2h-2 0z" class="O"></path><g class="C"><path d="M240 264c2 0 2-1 3 0v3l-1 1-2-2v-2z"></path><path d="M229 255c1 1 2 2 3 2s4-1 5-2h2v-1c2-1 3-2 5-2 1 1 0 2 1 3 0 1 2 2 2 2l1-1 4 2-5 6-1 1-3-1c-1-1-1 0-3 0h0l-2-1-2 1c-1 0-1 0-2 1l-1 1c-3-1-6-4-8-5-1 0-2-2-3-3v-1l1 1h1v-2h1c0 1 1 2 2 2h1v-2l1-1z"></path></g><path d="M240 264l1-2h0-2c0-1-1-1-2-2h-1-1c1-2 1-2 3-2s3 0 5-1h2 1c-1 1-1 2 0 3l-1 2h1v1l1 1-1 1-3-1c-1-1-1 0-3 0h0z" class="B"></path><path d="M252 215l3 2c1 0 2 2 3 3-1 0-1 0-2 1l1 1h1l1 2h-1l-1 2 1 1v-1l1-1h2l1 3c0 1-1 1 0 2s2 0 2 2c1 0 1 1 1 1-1 1-1 1-2 1-1-1-2-3-3-3v4h-1c-1 1-2 3-3 4h0c-1 0-1 0-2 1h1c0 1 0 1 1 2h-3c-3 2-4 5-6 8-1 1-1 0-2 1h0c1 1 1 2 2 3 0 1 1 1 1 2l-1 1s-2-1-2-2c-1-1 0-2-1-3-2 0-3 1-5 2v1h-2c-1 1-4 2-5 2s-2-1-3-2h1v-3h0l1 1c1-1 2-1 3-2s1-1 1-2v-1c1 0 1 0 2 1s1 2 1 3c-1 1-1 1 0 2 0-1 1-2 1-2 0-1-1-1-1-3 1-1 2-2 3-2 1 1 2 2 3 2 0-2 1-2 1-4 0-1 1-2 1-3h1c0-1 1-2 2-3 1 0 2 1 3 1v-1c-1 0-1-1-1-2h1c1-2 0-2 0-4h2c0 1 0 1-1 3 1 0 1 1 2 2h1c0-1 1-2 2-3h1c1-1 0-3 1-5 1-1 1-2 1-3-1-1-1-1-2-1v1l-2 1-1 2v-3h-2l-1-2h0c-2-2-2-2-3-4v-1c0-1 0-2 1-3l1-1v-1z" class="G"></path><path d="M253 225v-3l1-1v1 1l1 1v2l-1 1-1-2z" class="S"></path><path d="M337 145l1 1v1c-1 2 0 4 1 7h0l1 4c0 2 1 3 1 5 1 0 1 0 2-1v1c1 1 1 2 1 4l-1-1v-1l-1 1c1 2 1 4 2 6l-1 1-1-2h-1v2c-1 1-2 2-2 3s0 1-1 2l1 2h-3c-1-1 0-1 0-2l-2-1h0l1 2-2 2-1-1-1-1-1 1v2 1l-2-1h-3-2c0-1-2-2-3-4v-5c0 1 0 2 1 2v1h3 0c1 0 2 0 3-1v-2h-2v-1h0l1-1c1-1 0-1 2-1l1 1h3l1-1 2 2h2v-4l-1-1 1-1v-1h-1l1-1v-3l-1-1s1-1 0-2v-1c0-1-1-2-1-3h1 1c0-2-1-4 0-6h0v-3z" class="O"></path><path d="M325 182l1-1 3-1h1v2 1l-2-1h-3z" class="S"></path><path d="M341 171h-1v-3-2c-1-1 0-3-1-5l-1-2v-1c0-2 0-3 1-4l1 4c0 2 1 3 1 5 1 0 1 0 2-1v1c1 1 1 2 1 4l-1-1v-1l-1 1c1 2 1 4 2 6l-1 1-1-2h-1z" class="G"></path><path d="M260 197c1 1 2 2 3 2v1c1 1 2 1 3 1s1 0 1-1l2-2c2 0 3 1 4 3h0l-2 1v1h3v-2c1 1 1 1 3 1v-1l1 1-2 1 1 1 1 2v1c-1 0-2 0-3-1-1 0-2 0-2-1h-1l-1 1c1 1 2 2 4 3h-1-2v1h0l3 1 1 2v1c-1 1-1 2-1 3h0-1l-2-1c-1 0-1 1-2 2-2 0-2 0-3-1s-1-1-2-1h-1l-3-3-1-1-7-9-2-1v-1c2 0 4 0 5-1v-1l1-1h0l1 2c1-1 2-2 2-3z" class="O"></path><path d="M271 206h-1v-1c1-1 4-1 4-1 1 0 1 2 3 2h1v1c-1 0-2 0-3-1-1 0-2 0-2-1h-1l-1 1z" class="C"></path><path d="M253 203v-1c1 1 2 3 4 4h0c1-2 1-3 1-4l1-1c1 1 1 3 1 5l2 1c-1 1-1 2 0 3s4 2 4 3v2c-2-1-3-2-5-2l-1-1-7-9z" class="S"></path><path d="M266 215h3v-1c-1-1-1-1-1-2v-1l-2-1c-1 0-1-1-2-1l1-1h3c0 1 1 1 2 2h1v2h2l2-1 1 2v1c-1 1-1 2-1 3h0-1l-2-1c-1 0-1 1-2 2-2 0-2 0-3-1s-1-1-2-1h-1l-3-3c2 0 3 1 5 2z" class="e"></path><path d="M272 216l1-1 1-1v1 2l-2-1z" class="S"></path><path d="M267 229c0-1 1-2 2-3h0v2c1 1 1 1 2 1h2c1 1 0 1 0 2l2 3-6 5-11 11c-1 2-5 7-6 8l-4-2c0-1-1-1-1-2-1-1-1-2-2-3h0c1-1 1 0 2-1 2-3 3-6 6-8h3c-1-1-1-1-1-2h-1c1-1 1-1 2-1h0c1-1 2-3 3-4h1v-4c1 0 2 2 3 3 1 0 1 0 2-1 0 0 0-1-1-1 0-2-1-1-2-2s0-1 0-2v1l2-2v1l3 1z" class="C"></path><path d="M267 229c0-1 1-2 2-3h0v2c1 1 1 1 2 1v1l-1 1c-1 0-2 1-3 2l-1 1c0-2 0-2-1-3l2-2z" class="F"></path><path d="M271 229h2c1 1 0 1 0 2l2 3-6 5c0-1 0-1 1-2v-2l-1-1c0-1 1-2 1-3l1-1v-1z" class="Q"></path><path d="M247 254v-1c1-1 1-1 2 0l3-3h1c0 1 0 2 1 3h1c0-1 1-1 2-2v-1h1c-1 2-5 7-6 8l-4-2c0-1-1-1-1-2z" class="B"></path><path d="M209 309c2 1 4 1 6 2 2 0 3 1 5 1 1 2 0 2 0 4 0 1-1 3-2 4-3 8-5 15-7 23h1l-1 2v3l-2 9c0 3-2 7-2 11 0 1-1 1-1 2l-1 1v-25c0-2 1-6 1-8l1-27c0-1 1-1 2-2z" class="u"></path><path d="M211 343h1l-1 2v3l-2 9c0 3-2 7-2 11 0 1-1 1-1 2l-1 1v-25c1 3 1 7 1 10l1-1v4h1l1-7c0-2 0-3 1-4v-2c0-1 0-1 1-2v-1z" class="O"></path><defs><linearGradient id="o" x1="326.756" y1="172.697" x2="327.851" y2="202.566" xlink:href="#B"><stop offset="0" stop-color="#161613"></stop><stop offset="1" stop-color="#545452"></stop></linearGradient></defs><path fill="url(#o)" d="M309 185c1 0 1-1 2-2h0v-1c-1-1-1-2-2-3h1c0 1 0 1 1 2l3 3c0-1-1-1-1-3l1-1v-2-2-2-1l1 2c1 0 1 1 2 1v-1c0-1 1-2 0-3h0v-1l1 1h1c-1-2-1-1-3-2l1-1c1 0 1 0 2 1 0 1 0 2 1 3h0v5c1 2 3 3 3 4h2 3l2 1v-1-2l1-1 1 1 1 1 2-2-1-2h0l2 1c0 1-1 1 0 2h3l-1-2c1-1 1-1 1-2s1-2 2-3v-2h1l1 2v3c1 0 1 0 2 1 2 8 5 16 6 25l-2-1c-11 3-20 5-30 9-1-1-1 0-1-1-1-1-1-2-1-3-4-7-7-13-11-19l2-1c1 1 0 1 1 2v-2h1l-1-1z"></path><path d="M315 191c1 0 1-1 1 0l1 3h-2l-1-2 1-1z" class="L"></path><path d="M335 191c1 1 2 1 4 1 0 0 0-1 1-1l1 1h3l1 1-1 1h0l-1-1h-1l1 2c-1 0-1 0-2 1-2-1-5-1-8-2v-1s1 0 2-1v-1zm-15 12v-1l1 1v-1c1-1 2-1 3-1 1-1 1-2 1-3l1-2c1 0 1 0 2 1h0l1-1h1v2c-1 1-2 0-4 1l1 1c2 1 3 1 5 0l1-2h0c1 1 1 1 1 2 1 1 3 1 4 1 2-1 0 0 2 0 1-1 2-1 2-1h1v1l-12 2c-1 0-5 1-6 0-1 0-2-1-2-1h-1c-1 1-1 1-2 1z" class="Y"></path><path d="M317 206v-2c0-1 0-1 1-2h0v-2c1 1 1 0 2-1v1c0 1 0 2-1 2l1 1c1 0 1 0 2-1h1s1 1 2 1c1 1 5 0 6 0l12-2 6-1v1c-11 3-20 5-30 9-1-1-1 0-1-1-1-1-1-2-1-3z" class="q"></path><path d="M317 206v-2c0-1 0-1 1-2h0v-2c1 1 1 0 2-1v1c0 1 0 2-1 2l1 1c1 0 1 0 2-1h1s1 1 2 1c1 1 5 0 6 0-4 2-9 3-13 6-1-1-1-2-1-3z" class="s"></path><path d="M341 183h1c1 1 2 1 3 2l2 8-2 1v-1l-1-1h-3l-1-1c-1 0-1 1-1 1-2 0-3 0-4-1h-3-1c-2-1-5-1-7-1h0 2 0l-1-1c1 0 1 0 2-1h0-2-1-2l1-2h3 1c1 0 1 1 2 2h5v-1c1 0 2-2 3-2 2-1 2 1 4-1v-1z" class="Q"></path><path d="M341 184l1 1v1c-1 0-1 1-1 2-2-1-3-2-4-2v1l-2 1v1l-1-1v-1c1 0 2-2 3-2 2-1 2 1 4-1z" class="F"></path><path d="M341 171h1l1 2v3c1 0 1 0 2 1 2 8 5 16 6 25l-2-1v-1c-1-2-2-5-2-7l-2-8c-1-1-2-1-3-2h-1v1c-2 2-2 0-4 1-1 0-2 2-3 2 0-2-1-2-3-4l-1 1c-1-1-2-1-2-2l2 1v-1-2l1-1 1 1 1 1 2-2-1-2h0l2 1c0 1-1 1 0 2h3l-1-2c1-1 1-1 1-2s1-2 2-3v-2z" class="K"></path><path d="M341 183h-1l2-2v-1h-1c1-2 1-2 2-2 1 1 1 3 2 4v1 2c-1-1-2-1-3-2h-1z" class="B"></path><path d="M217 257c2 1 2 1 4 1h1c1 1 2 3 3 3 2 1 5 4 8 5 2 1 7 4 8 6v2 1c-2 5-5 10-8 14l-1-1c-3 6-7 12-10 18 0 0-1 0-1 1v-2l-1 1v2h0l-2-1h0l-2 1h-1c-1 0-1-1-1-2l-1 2-1 1h-3 0c-1 1-2 1-2 2 0-8 1-15 3-23-1-1 0-4 1-6 0-4 1-8 2-12l4-11v-1-1z" class="G"></path><path d="M235 281c-1 0-1-1-2-2l1-1 2 2-1 1z" class="C"></path><path d="M214 301c1 0 2-1 3-1h1v-1l-2-1c1-2 1-2 3-2l1 1h0 1 2l1 2h-1-1-2v1 1h1c0 1 1 2 1 3l-1 1-1 1v2h0l-2-1h0l-2 1h-1c-1 0-1-1-1-2v-5z" class="D"></path><path d="M214 284l4-1 1 1c0 1 1 1 2 1h1c1 3 0 3-1 5v3h-1l1 1 1 1-1 1v1h-1 0l-1-1c-2 0-2 0-3 2l2 1v1h-1c-1 0-2 1-3 1v5l-1 2-1 1h-3 0c-1 1-2 1-2 2 0-8 1-15 3-23l1 1c0-3 0-3 3-5h0z" class="C"></path><path d="M211 299c1 0 1 0 2 1 0 1 0 1-1 3l-1-1v-3z" class="F"></path><path d="M214 284l4-1 1 1c0 1 1 1 2 1h1c1 3 0 3-1 5v3h-1l1 1 1 1-1 1v1h-1 0l-1-1c-2 0-2 0-3 2l2 1v1h-1c-1 0-2 1-3 1 0-2 1-2 0-4l1-1 1 1c0-1-1-2 0-3 1 0 1 0 1 1h1v-1c-1-1-1-2-2-3-1 2-2 3-3 4l-1-1v-1l2-1c0-1 1-2 2-3 2-1 3 0 5-1l-2-2h-1c-1 0-2 0-3-1l-1-1z" class="B"></path><path d="M217 257c2 1 2 1 4 1h1c1 1 2 3 3 3 2 1 5 4 8 5 2 1 7 4 8 6v2 1c-2 5-5 10-8 14l-1-1 3-7 1-1c0-1 1-2 2-3s1-1 1-3l-1-1-2 2c0 1 0 1-1 2-2-1-1-2-4-2 0 1 1 2 0 3v2h-2c-1-1-1-1 0-2 0-1 0-1-1-2l1-2-1-1h-2l-1-1c0-1 1-2 1-3v-1l-1-1c-1 2-1 3-1 5 1 1 1 2 2 3h1l-2 1c1 1 1 1 1 2-1 1-2 1-2 3l1 1c-1 1-1 0-1 1-1 0-1 2-2 2h-1c-1 0-2 0-2-1l-1-1-4 1h0c-3 2-3 2-3 5l-1-1c-1-1 0-4 1-6 0-4 1-8 2-12l4-11v-1-1z" class="e"></path><path d="M217 257c2 1 2 1 4 1h1c1 1 2 3 3 3 2 1 5 4 8 5 2 1 7 4 8 6v2 1c-2 5-5 10-8 14l-1-1 3-7 1-1c0-1 1-2 2-3s1-1 1-3l-1-1c-1 0-2 0-2-1-2-1-4-2-5-3s-2-2-4-2c-2-2-4-3-7-5l-1 2 2 2 1 1-1 2h-3c2 2 2 0 2 3-2 2-1 2-1 4l-1 1v2l-1 1c-1 2 1 2-1 3h-1c-1-1-2-1-2-1v-5l1-1 2 1v-4h-1 0c-1-1-1-2-2-3l4-11v-1-1z" class="O"></path><path d="M220 262l-2-1 1-1c1 1 2 2 4 3 6 4 12 7 18 11v1c-2 5-5 10-8 14l-1-1 3-7 1-1c0-1 1-2 2-3s1-1 1-3l-1-1c-1 0-2 0-2-1-2-1-4-2-5-3s-2-2-4-2c-2-2-4-3-7-5z" class="r"></path><defs><linearGradient id="p" x1="278.014" y1="198.043" x2="303.891" y2="218.914" xlink:href="#B"><stop offset="0" stop-color="#22221e"></stop><stop offset="1" stop-color="#555453"></stop></linearGradient></defs><path fill="url(#p)" d="M302 184c1 0 1 1 2 2 0 1 1 2 1 3l10 19 1 2v1c-1-1-1-1-2 0-2 0-4 1-6 2-1 0-2 1-3 1l-4 2c-1 1-2 2-3 2-4 3-8 5-11 8l-4 3-1 1h-2 0l-1 1h-1l-3 3-2-3c0-1 1-1 0-2h-2c-1 0-1 0-2-1 1-1 1-2 1-4l-8-7c2 0 2 1 3 2s1 1 2 1c-2-3-5-5-7-8h0l1 1 3 3h1c1 0 1 0 2 1s1 1 3 1c1-1 1-2 2-2l2 1h1 0c0-1 0-2 1-3v-1l-1-2-3-1h0v-1h2 1c-2-1-3-2-4-3l1-1h1c0 1 1 1 2 1 1 1 2 1 3 1v-1l-1-2h5l1 2c2 1 2 0 4 0v-2-1c1-1 1-2 1-3 1 0 1-1 2-1 0-2 0-2 1-3 1 0 1-2 1-3l1-1v-1h1 0c0-1 0-2-1-3 0-1-1-2 0-2l1 1c1 1 1 2 3 2 0 0 0-1 1-1s2-1 2-1h1c1-1 0-2 0-2l1-1z"></path><path d="M298 212h1l1 2h-2v-2zm0 6v-3 1l2-1 1 1c-1 1-2 2-3 2z" class="Y"></path><path d="M305 214c0-2 0-2 1-4h4l-2 3c-1 0-2 1-3 1z" class="Z"></path><path d="M315 208l1 2v1c-1-1-1-1-2 0-2 0-4 1-6 2l2-3c2-1 3-2 5-2z" class="r"></path><path d="M299 192c1-1 1-1 1-3 3 0 2-1 4-3 0 1 1 2 1 3v1c-1 0-2 1-3 2-1 0-1 0-2 1v1c-1-1-1-1-1-2z" class="B"></path><path d="M272 223h3l1-1c1 0 1 0 1 1v2c1 1 2 3 4 3h1 1v1l-1 1h-2 0c-3-2-5-5-8-7z" class="J"></path><path d="M302 184c1 0 1 1 2 2-2 2-1 3-4 3 0 2 0 2-1 3-2-1-2-1-3-1l-1 2 1 1-2 2c-1-1-1-1-1-3 1 1 1 1 1 2v-2l-1-1v-1h1 0c0-1 0-2-1-3 0-1-1-2 0-2l1 1c1 1 1 2 3 2 0 0 0-1 1-1s2-1 2-1h1c1-1 0-2 0-2l1-1z" class="K"></path><path d="M260 212l1 1 3 3c3 2 5 5 8 7s5 5 8 7l-1 1h-1l-3 3-2-3c0-1 1-1 0-2h-2c-1 0-1 0-2-1 1-1 1-2 1-4l-8-7c2 0 2 1 3 2s1 1 2 1c-2-3-5-5-7-8h0z" class="H"></path><path d="M275 228l3 3-3 3-2-3 2-3z" class="Y"></path><path d="M270 224l5 4-2 3c0-1 1-1 0-2h-2c-1 0-1 0-2-1 1-1 1-2 1-4z" class="J"></path><path d="M270 177c5-3 8-8 14-8h-1c-4 4-9 7-14 11-7 6-13 13-20 21l-1-1c-1 2-4 5-6 5-5 6-8 13-13 19-1 2-2 4-4 5s-3 3-4 4l-1 4-1 3c0 1-1 2-1 3-1 1-1 3-1 4l-3 9 3 2v1l-4 11c-1 4-2 8-2 12l-2-1-4-2-2-2v-1h-1l1-1c1-6 0-11 2-16v-1c-1-2-1-4 0-6h-1l-1-1c2-1 3-3 4-5h0v-1l1-3c2 0 3 0 4 2 0-2 3-9 4-11 0-1-1-3-1-3l1-1v-3c0-2 6-11 7-14 2-2 3-4 4-6 2-2 3-4 5-6l2-4c1-2 2-3 3-4l1-2h2c1 1 1 1 1 2l2-1v2l1 1 4 4 15-16c3-2 5-4 7-5z" class="o"></path><path d="M208 261c0-2 1-4 2-6 1 0 1 0 3 1-1 0-2 1-2 1-1 2 0 4-2 5l-1-1z" class="N"></path><path d="M232 208c1-3 4-7 6-10 2-1 3-3 4-4l2 2-6 6c0 1-1 2-2 3h0c-1 0-2 1-3 3h-1z" class="c"></path><path d="M208 242c2 0 3 0 4 2l-1 1c0 3-1 5-2 7h-1c-1 0-2 0-2-1 0-2 0-3 1-5h0v-1l1-3z" class="N"></path><path d="M209 252v-2-1c-1-1-1-2 0-3 1 0 1 0 2-1 0 3-1 5-2 7z" class="i"></path><path d="M205 252c1 1 3 1 3 3h0c-3 6-4 15-5 21h-1l1-1c1-6 0-11 2-16v-1c-1-2-1-4 0-6z" class="W"></path><path d="M216 240l1-2h1s1 1 1 2-1 2-1 3c-1 1-1 3-1 4l-3 9-3-3h-1c1-2 1-4 2-5l4-10v2z" class="j"></path><path d="M216 238v2l1 1h-1v2l-1 1c-1 2 1 3-1 5h-1c-1 3-1 3-2 4h-1c1-2 1-4 2-5l4-10z" class="R"></path><path d="M213 256l4 3-4 11c-1 4-2 8-2 12l-2-1c-1-2-3-3-5-4l4-16 1 1c2-1 1-3 2-5 0 0 1-1 2-1z" class="n"></path><path d="M238 190h2c1 1 1 1 1 2l-13 19c-5 7-8 14-12 22 0-1-1-3-1-3l1-1v-3c0-2 6-11 7-14 2-2 3-4 4-6 2-2 3-4 5-6l2-4c1-2 2-3 3-4l1-2z" class="t"></path><path d="M232 200l2-4c1-2 2-3 3-4 0 3-2 6-4 8v1c-1-1-1 0-1-1z" class="W"></path><path d="M244 196l2 2 1 1c-1 2-3 4-5 6-5 6-8 13-13 19-1 2-2 4-4 5s-3 3-4 4l-1 4-1 3c0-1-1-2-1-2h-1l-1 2v-2l8-17c2-4 5-9 8-13h1c1-2 2-3 3-3h0c1-1 2-2 2-3l6-6z" class="N"></path><path d="M220 237l-2-2c0-1 1-2 1-2l1-1 1 1-1 4z" class="g"></path><path d="M244 196l2 2h0c-1 1-2 1-2 2l-3 3c-2 2-3 4-4 6-2 2-3 3-4 5l-1 1c-2 4-4 8-6 11l-2 2h-1c0-1-1-1 0-2 0-1 1-1 1-1v-1l3-3c1-2 1-3 2-5 2-3 3-5 5-8 1-1 2-2 2-3h0c1-1 2-2 2-3l6-6z" class="W"></path><path d="M352 144h3l3-1c2 0 7-1 10 0h0c3-1 8 1 11 1 2 0 3 0 5-1l3 1c12 2 26 6 35 14l8 5c4 3 7 6 10 9 2 2 3 3 3 5v1l3 3c2 3 4 7 5 10-1 1-2 1-3 1l-1 1h-2l-2 2h0l-1 2v1c-1 1-1 2-2 4 0 0-2 1-2 2-1 1 0 1-1 2l-2 2-1 1h-1c-1 1-2 1-3 2h-2v-2c-2 2-3 4-5 5l-1 1h-2v1c-2 0-2 0-3-1-2 0-3 0-4-2h-1-1v-1-1l-6-4-8-3c-3-2-6-2-9-3s-6-2-9-2v2h-3-19-4-1l-1 1c-1-9-4-17-6-25-1-1-1-1-2-1v-3l1-1c-1-2-1-4-2-6l1-1v1l1 1c0-2 0-3-1-4v-1c-1 1-1 1-2 1 0-2-1-3-1-5l-1-4h0c-1-3-2-5-1-7v-1h0c2-1 3-1 6-1l4-1h4z" class="u"></path><path d="M351 162c0-2 0-4 1-5h1v5h-2z" class="x"></path><path d="M375 160c1 0 1 0 1 1s0 1 1 3h0l-1 1v1l-1-1c-1-2-1-4 0-5z" class="O"></path><path d="M340 158h1v-2l1-1c1 2 1 5 1 7-1 1-1 1-2 1 0-2-1-3-1-5z" class="H"></path><path d="M371 163l-2 1h-1c-1-1-1-2-1-2 1-2 2-2 3-2h1v-1l1 1v2l-1 1z" class="G"></path><path d="M393 159c2 1 2 1 2 3 0 1 0 2-1 3-1 0-1 0-2 1l-2-2h0v-1h1c0-1 0-2 1-2h1 0v-1-1z" class="a"></path><path d="M413 167l1-1h2v-1l-2 1-1-1s0-1-1-2c1-1 1-1 3-1 0 1 0 1 1 3h1v1 2c-1 1-1 1-3 1 0-1 0-1-1-2z" class="O"></path><path d="M338 146h0c2-1 3-1 6-1l4-1h4l1 2-15 1v-1z" class="m"></path><path d="M361 160v-1c1-1 3-1 4 0 1 0 1 0 1 1v1 1c1 0 1 1 2 2l-1 1-1 1-1-1h-1c-1 0-1 0-2-1v-2h0l-1-2z" class="S"></path><path d="M405 164c1 1 2 1 3 0l1-1h1v1c0 1 0 2 1 3 1 0 1 1 2 0v2l-1 2-1-1c-1 0-1 0-2 1-1 0-1-1-1-1v-2c-1-1-2 0-4-2l1-2z" class="O"></path><path d="M408 168h1 2 0c1 1 1 1 2 1l-1 2-1-1c-1 0-1 0-2 1-1 0-1-1-1-1v-2z" class="K"></path><path d="M393 159c-1-2-2-1-3-2 1-1 1-2 3-3h1v1c1 1 1 1 3 1l-1 1-1 1c0 2 0 3 2 5 1 0 1 0 2-1 1 1 2 1 2 3h-1c-1-1-1 0-2-1l-3 2h-1v-1c1-1 1-2 1-3 0-2 0-2-2-3z" class="H"></path><path d="M376 168h0c1-1 1-2 1-2l1-1v-2c0-1 0-1 1-1l1 1h1l1-2 1-1c1 3 1 6 1 9v-1c0-2 0-2 1-3l-3 21c-1 2-1 3-1 5l-1 2c-1-2 0-7 0-9 1-4 2-8 1-12l-1-1-1 1h-1v-2-1h-2v1l-1-1 1-1z" class="S"></path><defs><linearGradient id="q" x1="384.396" y1="149.912" x2="385.584" y2="144.338" xlink:href="#B"><stop offset="0" stop-color="#7c7d7a"></stop><stop offset="1" stop-color="#a7a7a6"></stop></linearGradient></defs><path fill="url(#q)" d="M352 144h3l3-1c2 0 7-1 10 0h0c3-1 8 1 11 1 2 0 3 0 5-1l3 1c12 2 26 6 35 14h-3l-3-2c-5-3-10-4-15-6-16-4-31-5-48-4l-1-2z"></path><defs><linearGradient id="r" x1="392.218" y1="164.301" x2="393.704" y2="184.631" xlink:href="#B"><stop offset="0" stop-color="#21201e"></stop><stop offset="1" stop-color="#484846"></stop></linearGradient></defs><path fill="url(#r)" d="M385 165c0-3 1-6 2-9v-1-2h0l1-1h1c0 2-1 4-1 5v6 2l2-1h0l2 2c1-1 1-1 2-1v1h1l3-2c1 1 1 0 2 1h1l4-1-1 2c2 2 3 1 4 2v2s0 1 1 1v1c-1 1-1 2-1 3h0v1h0c-1 2-2 3-2 5v-1c-1-1-2-1-3-2l-2 2h-3c-1 2-1 4-1 6l-1 1-1 1c-1-1-1-1-2-1l-3 1v-2h-1c-1 0-2 0-3-1 0-1 0-2-1-3h-1c-1 1-1 1-1 3h1l-2 1 3-21z"></path><path d="M392 169h1c1 0 1 1 2 1h1v2s-1 0-2-1c-1 0-2-1-2-2z" class="D"></path><path d="M389 186c-1-1-1-2-1-4h2c3-1 4 3 7 4l-1 1-1 1c-1-1-1-1-2-1l-3 1v-2h-1z" class="L"></path><path d="M394 166h1l3-2c1 1 1 0 2 1h1l4-1-1 2c2 2 3 1 4 2-2 0-2 1-3 1l-1-1c-2-1-5 1-8 0-1 0-1-1-2-2z" class="S"></path><path d="M405 169c1 0 1-1 3-1v2s0 1 1 1v1c-1 1-1 2-1 3h0v1h0c-1 2-2 3-2 5v-1c-1-1-2-1-3-2l-2 2h-3c-1-2 0-2 0-4v-1-1h1l3-3h1l1-1v-1h1z" class="I"></path><path d="M405 169c1 0 1-1 3-1v2s0 1 1 1v1c-2 1-2 1-3 0-1 0-1-1-1-2h-1v-1h1z" class="F"></path><defs><linearGradient id="s" x1="361.33" y1="160.514" x2="365.121" y2="194.482" xlink:href="#B"><stop offset="0" stop-color="#070704"></stop><stop offset="1" stop-color="#545452"></stop></linearGradient></defs><path fill="url(#s)" d="M361 160l1 2h0v2c1 1 1 1 2 1h0l2 2 2-1c1 0 0 0 2-1h0c1-1 1-1 1-2l1-1c0 1 1 2 1 3l3 3-1 1 1 1v-1h2v1 2h1l1-1 1 1c1 4 0 8-1 12 0 2-1 7 0 9l1-2-2 6v2 2h-3-19-4-1l-1 1c-1-9-4-17-6-25-1-1-1-1-2-1v-3l1-1c-1-2-1-4-2-6l1-1v1l1 1c0-2 0-3-1-4l2 1 1-1c0 1 0 1 1 2h0 1v-3h0 1v-1l1 1v2c0 1 1 4 1 5-1-3 0-5 0-7h2 1c1 0 1 1 3 2v-1l1 1h2c1 1 0 0 2 1-1-2 0-3-1-5z"></path><path d="M364 165l2 2-2 2-1-1c0-1 0-2 1-3z" class="M"></path><path d="M344 172c-1-2-1-4-2-6l1-1v1l1 1 1 5c1 1 2 0 4 1h3l-1 1c1 1 1 2 3 2l-1 1h-2c-2 1-2 0-4 0 0-1-2-2-2-2v-2c0-1-1 1-1-1z" class="S"></path><path d="M356 196v-1c1-1 2-2 3-2l-1-2 2-1 4-4 1 1v2 2h0 1l-1 2c-1 0-1-1-1-1-1 1-1 1-1 2h-1c-1 0-1 0-1-1h-1c-1 1-3 3-4 3z" class="Z"></path><defs><linearGradient id="t" x1="366.754" y1="187.227" x2="367.66" y2="198.19" xlink:href="#B"><stop offset="0" stop-color="#51514f"></stop><stop offset="1" stop-color="#717070"></stop></linearGradient></defs><path fill="url(#t)" d="M377 186h0c-1-2-2-2-3-2 1-1 1-1 2-1s3 1 4 1c0 2-1 7 0 9l1-2-2 6v2 2h-3-19-4-1-1v-1c1-1 2-1 2-2 1 0 2 0 2-1l1-1c1 0 3-2 4-3h1c0 1 0 1 1 1h1c0-1 0-1 1-2 0 0 0 1 1 1l1-2 1-1v-2c1-1 2-2 4-2l2 2c0 1 0 1 1 1l2-1v-1l1-1z"></path><path d="M377 197c0-1 0-3-1-4v-1-1c0-1 0-2 1-3 1 2 1 3 1 4 0 2 0 3-1 5z" class="s"></path><path d="M353 201l2-2c2-1 3-1 6-2-2 1-3 2-4 3v1h-4z" class="h"></path><path d="M377 186h0c-1-2-2-2-3-2 1-1 1-1 2-1s3 1 4 1c0 2-1 7 0 9l1-2-2 6v2 2h-3l1-1v-3c1-2 1-3 1-5 0-1 0-2-1-4 1-1 0-1 0-2z" class="I"></path><defs><linearGradient id="u" x1="360.65" y1="203.398" x2="372.275" y2="195.493" xlink:href="#B"><stop offset="0" stop-color="#88888a"></stop><stop offset="1" stop-color="#a3a2a1"></stop></linearGradient></defs><path fill="url(#u)" d="M361 197c1 1 1 1 2 1 4-1 9 0 13 1l1 1-1 1h-19v-1c1-1 2-2 4-3z"></path><defs><linearGradient id="v" x1="410.24" y1="170.896" x2="420.968" y2="195.579" xlink:href="#B"><stop offset="0" stop-color="#383835"></stop><stop offset="1" stop-color="#757574"></stop></linearGradient></defs><path fill="url(#v)" d="M416 156l3 2h3l8 5c4 3 7 6 10 9 2 2 3 3 3 5v1l3 3c2 3 4 7 5 10-1 1-2 1-3 1l-1 1h-2l-2 2h0l-1 2v1c-1 1-1 2-2 4 0 0-2 1-2 2-1 1 0 1-1 2l-2 2-1 1h-1c-1 1-2 1-3 2h-2v-2c-2 2-3 4-5 5l-1 1h-2v1c-2 0-2 0-3-1-2 0-3 0-4-2h-1-1v-1-1l-6-4-8-3c-3-2-6-2-9-3s-6-2-9-2v-2l2-6c0-2 0-3 1-5l2-1h-1c0-2 0-2 1-3h1c1 1 1 2 1 3 1 1 2 1 3 1h1v2l3-1c1 0 1 0 2 1l1-1 1-1c0-2 0-4 1-6h3l2-2c1 1 2 1 3 2v1c0-2 1-3 2-5h0v-1h0c0-1 0-2 1-3v-1c1-1 1-1 2-1l1 1 1-2v-2c1 1 1 1 1 2 2 0 2 0 3-1v-2-1h-1c1-2 3-4 2-6h0-3v1l-1-1c1-1 1-1 2-3h0z"></path><path d="M416 156l3 2h3l8 5c0 1 0 2 1 3s2 1 2 3l3 3 2 1-3 3-1 1-4 4c-1 0-2-1-3-1v-4h-1c0 1-1 2-2 3 0-1-1-1-2-2-2-1-1-4-1-6-2 0-3 2-5 2 0 0-1 0-1-1h-1c-1 0-1 0-2 1h-1v-3l1 1 1-2v-2c1 1 1 1 1 2 2 0 2 0 3-1v-2-1h-1c1-2 3-4 2-6h0-3v1l-1-1c1-1 1-1 2-3h0z" class="B"></path><path d="M427 173l-2-1h0c0 2-1 2-2 3h-1c0-2 1-3 1-4h1 1 2v-3l1-1h0v3c1 0 1 0 1 1l-2 2z" class="e"></path><path d="M419 158h3l8 5c0 1 0 2 1 3s2 1 2 3c-1 0-1-1-2-1-1-2-4-4-6-5-2-2-4-3-6-5z" class="v"></path><path d="M429 171c0 2 1 2 1 3 1 1 2 2 3 2 2-1 2-1 3-4l2 1-3 3-1 1-4 4c-1 0-2-1-3-1v-4h-1l1-2v-1l2-2z" class="I"></path><path d="M429 171c0 2 1 2 1 3l-1 2h-1v-2h-1v-1l2-2z" class="Q"></path><path d="M416 156l3 2c2 2 4 3 6 5h0l-4 6h-4l-1 1s-1-1-2-1c2 0 2 0 3-1v-2-1h-1c1-2 3-4 2-6h0-3v1l-1-1c1-1 1-1 2-3h0z" class="C"></path><defs><linearGradient id="w" x1="400.168" y1="179.27" x2="398.456" y2="200.375" xlink:href="#B"><stop offset="0" stop-color="#464643"></stop><stop offset="1" stop-color="#797977"></stop></linearGradient></defs><path fill="url(#w)" d="M398 180h3l2-2c1 1 2 1 3 2v1h1c0 1 1 1 0 3h0l-1 1h1l-1 3h-1v1h-1c-1 0-1-1-1-2 0 2-1 2 1 3h2c0 1 1 1 1 2s-1 2-1 3v1h0 1c1-1 1-2 3-3 1 1 1 2 2 3v1-1l2 2 2-2v-1l1 1h2 4l-12 16v-1l-6-4-8-3c-3-2-6-2-9-3s-6-2-9-2v-2l2-6c0-2 0-3 1-5l2-1h-1c0-2 0-2 1-3h1c1 1 1 2 1 3 1 1 2 1 3 1h1v2l3-1c1 0 1 0 2 1l1-1 1-1c0-2 0-4 1-6z"></path><path d="M386 192c0-2 1-2 2-3h4c0 2-1 3-1 5l-1 1c-2-1 0-1-1-2 0-1-1-1-1-2l-2 1z" class="r"></path><defs><linearGradient id="x" x1="383.925" y1="182.411" x2="385.187" y2="195.55" xlink:href="#B"><stop offset="0" stop-color="#474645"></stop><stop offset="1" stop-color="#696966"></stop></linearGradient></defs><path fill="url(#x)" d="M382 186l2-1h-1c0-2 0-2 1-3h1c1 1 1 2 1 3 1 1 2 1 3 1h1v2l3-1c1 0 1 0 2 1l1-1v2h-1l-1-1-2 1h-4c-1 1-2 1-2 3 0 1 1 2 1 4h-1v1h-1v1h2l1 2v1c-3-1-6-2-9-2v-2l2-6c0-2 0-3 1-5z"></path><defs><linearGradient id="y" x1="383.726" y1="193.456" x2="384.071" y2="199.177" xlink:href="#B"><stop offset="0" stop-color="#696767"></stop><stop offset="1" stop-color="#7b7b78"></stop></linearGradient></defs><path fill="url(#y)" d="M379 197c2 0 3 0 4-2l-1-2h2c1 1 1 2 1 3v1 1h2l1 2v1c-3-1-6-2-9-2v-2z"></path><defs><linearGradient id="z" x1="405.754" y1="195.479" x2="405.725" y2="209.489" xlink:href="#B"><stop offset="0" stop-color="#747472"></stop><stop offset="1" stop-color="#8a8988"></stop></linearGradient></defs><path fill="url(#z)" d="M414 198l2-2v-1l1 1h2 4l-12 16v-1l-6-4-8-3c-3-2-6-2-9-3v-1c1 0 1-1 2-2 0 0 0-1 1-2l1 1c3 1 6 3 8 4 1 0 6 1 7 0 0 0 0-1 1-1h1l1-2h2 2z"></path><path d="M390 198c3 1 6 3 9 5 2 0 4 1 6 2v1 1l-8-3c-3-2-6-2-9-3v-1c1 0 1-1 2-2z" class="k"></path><defs><linearGradient id="AA" x1="439.068" y1="189.925" x2="423.942" y2="205.602" xlink:href="#B"><stop offset="0" stop-color="#636361"></stop><stop offset="1" stop-color="#888787"></stop></linearGradient></defs><path fill="url(#AA)" d="M430 163c4 3 7 6 10 9 2 2 3 3 3 5v1l3 3c2 3 4 7 5 10-1 1-2 1-3 1l-1 1h-2l-2 2h0l-1 2v1c-1 1-1 2-2 4 0 0-2 1-2 2-1 1 0 1-1 2l-2 2-1 1h-1c-1 1-2 1-3 2h-2v-2c-2 2-3 4-5 5l-1 1h-2v1c-2 0-2 0-3-1-2 0-3 0-4-2h-1-1v-1l12-16 12-20 3-3-2-1-3-3c0-2-1-2-2-3s-1-2-1-3z"></path><path d="M425 209l2-1v-1l1-1v1 2c-2 2-3 4-5 5 0 0 0-1 1-2s1-2 1-3h0z" class="P"></path><path d="M440 193c1 1 2 1 3 2h0l-1 2c-2 2-3 3-6 4v-4c1-1 1-1 1-2v-1c0 1 0 1 1 1l1-1 1-1z" class="l"></path><path d="M436 201c3-1 4-2 6-4v1c-1 1-1 2-2 4 0 0-2 1-2 2-1 1 0 1-1 2l-2 2-1 1h-1c-1 1-2 1-3 2v-1c1-2 3-3 4-5 1-1 1-3 2-4z" class="P"></path><defs><linearGradient id="AB" x1="443.77" y1="187.858" x2="445.415" y2="192.393" xlink:href="#B"><stop offset="0" stop-color="#595957"></stop><stop offset="1" stop-color="#6f6f6e"></stop></linearGradient></defs><path fill="url(#AB)" d="M436 182c1-1 4-5 5-6l2 2 3 3c2 3 4 7 5 10-1 1-2 1-3 1l-1 1h-2l-2 2c-1-1-2-1-3-2l-1 1-1-1v-2h1v-1-1c-1-1-3-3-4-5l1-2z"></path><path d="M443 185l2 1c1 0 2 0 2 1v1h-1l-1-1c-1-1-1-1-1-2h-1z" class="L"></path><path d="M439 189l2-1v-1h1c0 2-1 3-2 4l1 1-1 1-1 1-1-1v-2h1v-1-1z" class="o"></path><path d="M436 182c1-1 4-5 5-6l2 2 3 3c-1 2-1 3-1 5l-2-1v-2c-1 1 0 1-1 1v1h1c0 1-1 2-1 2h-1v1l-2 1c-1-1-3-3-4-5l1-2z" class="J"></path><path d="M435 184l1-2c1 2 3 3 3 4l3-1h1c0 1-1 2-1 2h-1v1l-2 1c-1-1-3-3-4-5z" class="s"></path><path d="M430 163c4 3 7 6 10 9 2 2 3 3 3 5v1l-2-2c-1 1-4 5-5 6l-1 2-5 7c1 1 1 3 0 5s-2 3-3 5h1v2c-1 1-3 2-4 3 1 1 2 1 2 2l-1 1h0c0 1 0 2-1 3s-1 2-1 2l-1 1h-2v1c-2 0-2 0-3-1-2 0-3 0-4-2h-1-1v-1l12-16 12-20 3-3-2-1-3-3c0-2-1-2-2-3s-1-2-1-3z" class="k"></path><path d="M417 213c1-1 2-1 3-1 1-1 3-2 5-3-1 2-2 3-3 4h-5z" class="h"></path><path d="M430 191c1 1 1 3 0 5s-2 3-3 5h1v2c-1 1-3 2-4 3 1 1 2 1 2 2l-1 1h0 0c-2 1-4 2-5 3-1 0-2 0-3 1-1-1-1-1-1-2 1-2 1-3 2-4 2-2 2-4 5-6 1 1 2 2 3 2l1-1-3-2c1-3 4-7 6-9z" class="v"></path><defs><linearGradient id="AC" x1="434.975" y1="173.712" x2="430.525" y2="190.788" xlink:href="#B"><stop offset="0" stop-color="#181617"></stop><stop offset="1" stop-color="#32332f"></stop></linearGradient></defs><path fill="url(#AC)" d="M438 173l2 2c-1 3-19 27-22 31-1 1-4 5-5 7h-1-1v-1l12-16 12-20 3-3z"></path><path d="M847 110v1c-2 2-4 5-7 7-7 10-15 23-19 34l-1-1c-2-2 1 2-1-1-1-1-1-1-1-3-1-1-2-1-3-2-1 1-2 1-4 1h0l-1-1h-1-1c-1 0-1 0-2-1l-1 1h0-1c-1-1-1-2-2-2h-1l-2 1c0-1-1-1-1-2-1-1-1-1-2-1l-4-4c-2-1-4-2-6-2l-3-1-1-2c-1 0-3 0-4 1l1 1c-1 1-2 2-3 2-3 3-8 6-10 10-1 1-1 2-1 3 1 3 0 7 1 9l-1 1v-1h-1c0 2 0 3 1 5l1 1h-1c-1 0-1 1-2 1l-1 1 1 1c2 2 4 3 4 6h0l-1 2-5-5c-1 0-2 1-2 2l1 1h1c-1 1-1 1-2 1v-2c-1-1-2-1-3-2v2s-1 1-2 1l-5-4-6-4-2-1-2-1-2-1-2-2-3-2-7-3h-1l-4-2h-1c-4-2-9-3-12-4l-5-2c-11-3-21-3-32-3l-24 3c-3-2-7 0-9 1h-2-2c-5 1-11 3-16 5l-9 6c-5 2-8 6-12 10v-2c1-1 0-2 1-3-1-1 0-1-1-2l-2 1-6 6-1 1c0 2-2 2-3 4 0 1-3 6-4 7l-7 14-1 2c0 1 0 0-1 2 0 0-1 1-1 2 0 2-1 4-1 6s0 3-1 5v1s-2 1-2 2c0 0 0 1-1 1h-3v4l1 2v4h-3c-1 0-3 0-4 1h-2c-1-1-2-1-3-1h-1c-1-1-2-1-2-2v-3c0-1 1-1 0-2l-1 4c-1 0-2 1-2 1h-1-2c0 3-2 4-3 5-2-1-2-1-2-3l-13 14-7 7-13-13c-1 0-2-1-3 0l-1 1c-1-1-1-1-1-2l-1-1c-2-3-4-5-6-7h-1c0-2-1-2-1-3v-1-1l-1-2c-5-5-10-10-14-15-2-3-5-5-7-8l-6-13c-1-2-3-5-4-7l-2-2c-1 0-2-1-3-2v-1h-1l-1-1c-2-1-5-4-6-6h0c-1-1-2-1-2-2-2-1-3-2-4-2h-1c-3-1-6-4-8-5h-1c2 2 7 5 7 7v2l-14-11c-3-1-7-3-10-5l-1-1s0-1 1-2h0c-1-1-2-2-3-2h0l-12-4c-3-1-7-1-10-2v1c1 1 1 1 2 1l3 1s1 0 1 1h-2l-2 1v1h0l-2 2-3-1c-2 1-3 1-5 1-3 0-8-2-11-1h0c-3-1-8 0-10 0l-3 1h-3-4l-4 1c-3 0-4 0-6 1h0l-1-1v3c-12 1-24 4-35 10l-12 6c-1 1-5 5-6 5-6 0-9 5-14 8-2 1-4 3-7 5l-15 16-4-4-1-1v-2l-2 1c0-1 0-1-1-2h-2v-1c-2 1-4 1-5 1h-1-2 0c1-1 2-2 2-3l-1-2c-3 1-6 0-9-1v-3c-2-2-1-1-4-2-2 2-3 4-5 6-5-4-8-9-10-15-1-4-1-7-4-10h0c-1-2 0-4 0-6-1 0-2-5-2-6l-3-8-4-8c0-4-3-5-4-8 1 0 2 0 3-1-3-3-7-6-12-7l-5-5v-1c2 1 4 2 6 2 19 5 39 8 59 12 13 2 27 6 41 7 26 2 52 1 78 1h2 12 21 85 135 41 23 1 45 10c1 0 3 0 4-1h1v-2h0c0 1 0 1 1 2 8 0 16-1 25-3l43-9 28-5c4-1 9-2 14-4z" class="M"></path><path d="M261 136c1 0 1 0 2 1v1h-1-1v-2zm25 4c2 0 3 0 4 1-1 0-1 0-1 1h-1v1c-1 0-2-1-3-1v-1l1-1z" class="S"></path><path d="M482 146l-1-3v-1c0-1 0-2 1-3 1 1 2 1 3 2v1l-1 1c-2 1-2 1-2 3h0z" class="K"></path><path d="M256 137l2-1c1 1 1 2 2 3v3h-1-1-1c-1-1 0-3-1-4v-1z" class="B"></path><path d="M301 141c1-1 2-2 4-1h1c1 0 1 0 2-1 1 1 2 1 2 2l-1 1-5 2c-1-2-1-3-3-3z" class="H"></path><path d="M310 141l10-3v1c1 1 1 2 2 2-2 1-4 1-6 1-2-1-5 0-7 0l1-1z" class="r"></path><path d="M804 145l1-1v-1-1l2-2v-1h-2 0l1-1h1 2c0 1 0 2 1 3l1-1 1 1h-1 0c-2 1-2 2-2 3v1h-1c-1 0-1 0-2-1l-1 1h0-1z" class="a"></path><path d="M247 138l1-1c0-1 0-1 1-2 1 1 0 1 2 1-1-1-1-2-1-4 1 1 1 2 2 3h1 1l2 2h-3c-2 1-2 2-2 4h-1l-3-3z" class="F"></path><path d="M256 137h0v1c1 1 0 3 1 4h1l-1 1-1 1c-1 0-2-1-3-1 0-1 0-2-1-3v1c-1 1-1 1 0 2l-2-2h1c0-2 0-3 2-4h3z" class="I"></path><path d="M738 142h0v-3h0l1-1 2 2c2-1 5-1 8-2v2 1h-1l-1-1-1 1c-1 1-2 1-2 1h-2c-1 0-2 1-2 1l-2-1z" class="G"></path><path d="M749 138h0l2 2v-1c1 1 2 1 3 2h4l1 2c-1-1-2-1-3-1h-4c-3 0-6 0-9 1-2 0-3 1-5 1v-2l2 1s1-1 2-1h2s1 0 2-1l1-1 1 1h1v-1-2zm-485 17c2-2 1-4 3-6 1 0 1 0 2-1v-1c1-2 1-2 3-2 0 2-2 3-3 4 0 2 0 2 1 3-1 2-2 3-3 4h-2-2l1-1z" class="K"></path><defs><linearGradient id="AD" x1="352.766" y1="138.819" x2="349.64" y2="132.475" xlink:href="#B"><stop offset="0" stop-color="#29272b"></stop><stop offset="1" stop-color="#3b3932"></stop></linearGradient></defs><path fill="url(#AD)" d="M341 135h1c4 0 7-1 11-1 0 0 0 1 1 2l1-1h3 1c1 0 1 0 2 1-4 0-5-1-7 1-4 0-7 0-10 1-1 0-2-2-2-3h-1z"></path><path d="M272 145h1c2-1 2-1 3-2 1-2 0-4 1-6l1 1h0c-1 2 0 2 1 4l1-1c1 2 1 3 1 5h0c-1 0 0-1-1-1-1 1-1 1-1 2h-1v-1c-1 0-2 0-2 1-2-1-2-1-3 0s-2 3-2 4l-1 1c-1-1-1-1-1-3 1-1 3-2 3-4z" class="O"></path><path d="M721 142c4 0 6 1 10 3h2 2c1 1 2 2 3 4 1 0 2 1 3 1s2 1 3 1h-6l-17-9z" class="a"></path><path d="M547 142l2-1c1 0 3-1 4-1h2v-1l1-1 1 2h2 2c1-1-1-1 1-1h2 1c1 1 3 0 4 0-1 1-2 1-3 2l-1-1-1 1-1-1-4 2-1-1v3s0 1 1 1h0-4v-1l-1 1v-1c-1-1-2-1-3-2l-1 1c-1 0-2 0-3-1z" class="C"></path><path d="M361 136c2 0 5-1 7-2h1c2-1 4 0 6 1 0-1 1-1 2-2l1 1c1 0 2 0 3 1-2 0-4 2-6 2h-21c2-2 3-1 7-1z" class="B"></path><path d="M782 132c6 0 10 0 14 4 1 2 4 5 5 7l-2 1c0-1-1-1-1-2-1-1-1-1-2-1l-4-4c-2-1-4-2-6-2l-3-1-1-2z" class="F"></path><path d="M439 141c1 1 2 2 4 3 1 0 3-1 3-2 1-1 1-1 1-2 1 1 2 1 2 3v-3s1-1 2-1v1c1 2 0 3 1 4 2-1 2-1 4-1l1 1 1-1v2h-1v2h-4c-1 0 0-1-2-1h-1l-1-1c-1 1-1 0-1 1h-1l-1-1h-2c-1-1-2-1-3-1h-1-1v-3z" class="D"></path><path d="M301 141c2 0 2 1 3 3l-3 1c-1-1-2-1-3-1h0c-1 1-2 1-3 2-1 0-2-1-3 0h0 0-1c-1 0-1 0-2 1v-1l-2-1h-4s-1 1-1 2h0l-1-1h0l2-2h4c1 1 2 0 4 0-1-1-1-2 0-3 1 0 3 1 4 1h0 1c1 0 1-1 2-1h3z" class="S"></path><path d="M751 139c2 0 3 0 4-1 1 0 1 1 2 1l2 1c2 0 2 0 3 1l1 1 1-1 1-1c3-3 8-6 13-7h0l1 1c-1 1-2 2-3 2-3 0-4 1-7 2-2 1-4 4-6 6l-4-1h0l-1-2h-4c-1-1-2-1-3-2z" class="x"></path><path d="M330 137l10-3 1 1h1c0 1 1 3 2 3l-4 1-2 1-3 1c-1 0-1 0-1-1h-1-4l-1-1c0 1-1 1-1 1l-1-1c1-1 2-1 4-1h0v-1z" class="J"></path><path d="M342 135c0 1 1 3 2 3l-4 1v-1c1-1 1-2 2-3z" class="I"></path><path d="M330 137l10-3v1c-1 1-1 1-1 2h-3-1l1 2-1 1-1-1h-5-1c0 1-1 1-1 1l-1-1c1-1 2-1 4-1h0v-1z" class="Q"></path><path d="M738 151h6v1 1l2-1s1 0 2 1h3 1 1c0 2 0 2 2 3l1 2 1-1c1 3 1 3 0 5-1 0-1-1-2-2l-2 1c-4-3-8-6-12-8l-3-2z" class="L"></path><path d="M508 139l1-1v-2c2 1 2 3 3 3 2 0 2 1 4 1l1 1c1-2 1-3 1-5v1c1 0 1 1 1 1v1l-1-1v2c2 1 1 3 3 4v-2h1c1 0 2-1 2-2 2 0 2 0 4 1v1c0 1-1 2-2 3v-1l-2 1v2 1h-1v-1-1-3l-1 1v1h-1-1c0-1-1-2-1-3h-1v-1c-1 2-1 2-2 3h-4l-1-2v-1l-1-1-2 2h-1l1-3z" class="K"></path><path d="M753 153h1c1 0 2-1 3-2 1 0 2 1 3 1h0v-3h0 2l1-1v-1l1-1h2c-1 1-1 2-1 3 1 3 0 7 1 9l-1 1v-1h-1c0 2 0 3 1 5l1 1h-1c-1 0-1 1-2 1l-1 1c-2-1-3-2-5-4 1-2 1-2 0-5l-1 1-1-2c-2-1-2-1-2-3z" class="G"></path><path d="M755 156c1-1 0-1 0-3 2 0 2 0 4 1 0 0 1 1 2 1v-2h1v-3h1v3h1c0 2 0 5-1 7v-1h-1c0-1 0-2-1-3-1 1-1 1-1 2h0c-1-1-2-1-2-1h-1l-1 1-1-2z" class="J"></path><path d="M765 149c1 3 0 7 1 9l-1 1v-1h-1c0 2 0 3 1 5l1 1h-1c-1 0-1 1-2 1l-1 1c-2-1-3-2-5-4 1-2 1-2 0-5h1s1 0 2 1h0c0-1 0-1 1-2 1 1 1 2 1 3h1v1c1-2 1-5 1-7l1-4z" class="r"></path><path d="M691 138c5 0 11-1 17 1l13 3 17 9 3 2h0c1 1 1 2 2 3h-1 0c-1-1-1-1-2-1h-1 0l-8-3c-6-5-14-8-21-10-2-1-5-1-7-2h-3l-1-1c-2 0-4 0-6 1l-2-1v-1z" class="E"></path><path d="M452 144l1-1 1-1h1 1v-1-1l1-1v-3l1 4h1s0-1 1-1c1 1 1 2 2 3 1 0 2-2 3-3 1 0 1 1 2 0l1 1c1-1 0-1 2-1h1c0-1 1-1 1-1 1 1 1 1 2 1v2c0-1 0-2 1-3 1 1 1 2 1 3h1 0l-1-1v-1l2 1h1 0v-2l1 1c0 1 0 1 1 2v4h-1v4c-1 0-4 0-4 1-1 1-1 2-1 3l-1-1v-2-1-1-1c0-2-1-2-2-3-3-1-3-1-4 1l-2-2-2 1c-2 0-1 0-2 1-1 0-2 1-3 1l-1-1v-2l-1 1-1-1c-2 0-2 0-4 1z" class="B"></path><path d="M474 150l1-1v-4c2-1 4 0 5 0v4c-1 0-4 0-4 1-1 1-1 2-1 3l-1-1v-2z" class="Z"></path><path d="M292 146h0 0c1-1 2 0 3 0 1-1 2-1 3-2h0c1 0 2 0 3 1-4 2-10 4-14 6-1 0-2 0-3 1s-2 1-3 2l-2-2v2c-1 1-1 0-3 1l-1 1s-2 0-2 1c-1 0-1 2-1 3l-1 1c-1 0-2 1-3 1h0c-1 2-3 3-4 3h0c0-1 0-1-1-2v-1l1-1h-2v-1l1-4h0 2 2c1-1 2-2 3-4l1-1c0-1 1-3 2-4s1-1 3 0c0-1 1-1 2-1v1h1c0-1 0-1 1-2 1 0 0 1 1 1l1 1h0c0-1 1-2 1-2h4l2 1v1c1-1 1-1 2-1h1z" class="I"></path><path d="M263 156h2c0 3 0 3-1 5h0-2v-1l1-4h0z" class="G"></path><path d="M264 165c0-2 1-5 3-6 0-1 0-1 1-2 0 0 0-1 1-1 1-1 2-1 3-2v-1h4c1 1 1 1 3 1-1 1-1 0-3 1l-1 1s-2 0-2 1c-1 0-1 2-1 3l-1 1c-1 0-2 1-3 1h0c-1 2-3 3-4 3h0z" class="w"></path><path d="M271 151c0-1 1-3 2-4s1-1 3 0c0-1 1-1 2-1v1h1c0-1 0-1 1-2 1 0 0 1 1 1l1 1h0c0-1 1-2 1-2h4l2 1v1c1-1 1-1 2-1h1l-1 1-1 1-2-2h0l-1 3h0l-2 1-2-1-1 1h-1c-1-1-1 0-2-1l-1-1c-2 1-4 1-5 2s-2 1-2 1z" class="K"></path><path d="M189 123l3 3 5 3c1 1 3 1 4 1h7c1 1 3 0 4 1l-4 5-1 2-1-1c-1 2-1 3-2 4v1h-4c0 2 0 3-1 4h-1v-4l-4-2-4-8c0-4-3-5-4-8 1 0 2 0 3-1z" class="T"></path><path d="M192 126l5 3c-1 1-1 2-2 3l2 2-1 2-1 1c-2-3-3-8-3-11z" class="P"></path><path d="M201 130h7l-1 1-1 1c0 1-1 2-2 3-2 2-3 5-4 7 0 2 0 3-1 4h-1v-4c1-1 6-9 6-11l-3-1z" class="k"></path><path d="M208 130c1 1 3 0 4 1l-4 5-1 2-1-1c-1 2-1 3-2 4v1h-4c1-2 2-5 4-7 1-1 2-2 2-3l1-1 1-1z" class="b"></path><path d="M402 137l2-2 1-1h3 4l1 1h1v-1l1-1c1 1 2 1 2 3h1 2 1c0 1 1 2 1 3h0l3 1h3c0-1 1-1 0-2h-1c1-1 1-1 2-1l1-1c1 2-1 4 2 4h1c1-1 1-1 1-2h1c0 1 0 1 1 3h0 2v-1l-1-1h1c1 1 1 1 1 2v3h1v1h-1-3 0c-1 0-1-1-2-1-2 0-5-1-8 0-2-1-5-1-7-2-1 0-1 1-2 1l-1-1h-2-5 0l-12-4h1c2-1 2-1 4-1z" class="O"></path><path d="M408 139h0l2-2h1v-2h1c1 1 1 2 1 3h1c1 0 1-1 2-2l2 2 1-2c1 1 1 2 1 3l1 1-1 1-1-1c-1 1-1 1-2 1l-1-1-1 1-2-1h-1-2c0-1-1-1-2-1z" class="C"></path><path d="M402 137v1l1-1c1 0 1 1 2 1s1 0 2-1c0 1 1 1 1 2 1 0 2 0 2 1h2 1l2 1 1-1 1 1c1 0 1 0 2-1l1 1 1-1h1c2 1 6 1 7 1h1 0 2 1 2 1 0 2v-1l-1-1h1c1 1 1 1 1 2v3h1v1h-1-3 0c-1 0-1-1-2-1-2 0-5-1-8 0-2-1-5-1-7-2-1 0-1 1-2 1l-1-1h-2-5 0l-12-4h1c2-1 2-1 4-1z" class="Y"></path><path d="M485 142l3 2c1-1 1-1 2-1h0l1-1c1 0 1 0 2-1v-1h1c1-1 1-1 3-1h1 1 0c0-1 0-1 1-2h1v4h2l3-3 2 1-1 3h1l2-2 1 1v1l1 2v1h-1v-1l-1 1h-2c-1-1-1-3-1-4-1 0-1 1-1 1-1 1-3 0-4 1 0 1 0 3-1 4l-1-1c-1 1-1 1-1 2l-1 2c-2-1-2 1-4-1 0 3 0 4 1 7l-1 1c0-1-2-2-2-1-1 0-2 0-3 1-1-1-2-2-2-3h-2c-1 0-1-1-2-1l-1 1h-1l-1-1v-4-4h1l1 1h0c0-2 0-2 2-3l1-1z" class="B"></path><path d="M484 143h1c1 1 1 1 2 1l1 2 2-1 1 1h0l1-1c1 2 0 3 1 4h1 0c0 3 0 4 1 7l-1 1c0-1-2-2-2-1-1 0-2 0-3 1-1-1-2-2-2-3h-2c-1 0-1-1-2-1l-1 1h-1l-1-1v-4-4h1l1 1h0c0-2 0-2 2-3z" class="q"></path><path d="M481 145l1 1h0v3 5h-1l-1-1v-4-4h1z" class="F"></path><path d="M484 143h1c1 1 1 1 2 1l1 2 2-1 1 1h0l1-1c1 2 0 3 1 4v2l-1-1h-1c-1 0-1-1-1-2-1 1-1 1-2 1s-3 0-4-1c-1 1-1 1-2 1v-3c0-2 0-2 2-3z" class="D"></path><path d="M409 142h5 2l1 1c1 0 1-1 2-1 2 1 5 1 7 2 3-1 6 0 8 0 1 0 1 1 2 1h0 3 1v-1h1c1 0 2 0 3 1h2l1 1h1c0-1 0 0 1-1l1 1h1c2 0 1 1 2 1h4v-2h1l1 1c1 0 2-1 3-1 1-1 0-1 2-1l2-1 2 2c1-2 1-2 4-1 1 1 2 1 2 3v1 1 1 2h0c-1-1-2-1-3-1s-1 1-2 2l-1-1c-1 1-1 0-1 1h-1v-3h0-2l-1 1v1l-1-2c-2 1-3 1-4 2s-1 0-2 0c-1 1-2 1-2 2-1-1-1-1-1-2l-2-1-1 1c0-1 0-2-1-2h-1l-2-1c-3 1-4 0-6 0-1 0-2 0-3-1h-1c-3-1-8-3-11-3h-2c0-1-2 0-3-1-2 0-4 0-5-1h-2c-1 0-1 0-1 1-1-1-2-2-3-2z" class="y"></path><path d="M451 151c2-1 2 0 4 0 1 0 2-1 2-2 1 0 2 1 2 1l1-1c2 0 4-1 6-1l1-1v1h1 1c0 1 0 1 1 1h1l1-1 1 1h1v1 2h0c-1-1-2-1-3-1s-1 1-2 2l-1-1c-1 1-1 0-1 1h-1v-3h0-2l-1 1v1l-1-2c-2 1-3 1-4 2s-1 0-2 0c-1 1-2 1-2 2-1-1-1-1-1-2l-2-1z" class="m"></path><path d="M458 145l1 1c1 0 2-1 3-1 1-1 0-1 2-1l2-1 2 2c1-2 1-2 4-1 1 1 2 1 2 3v1h-1c-1-1-1-1-1-2l-2 2-1-1h-1c-2-1-4-1-6 0s-3 1-4 1-2 1-2 1h-1l-1-1-1 1-1-1c-2 0-4 0-6-1h-6l-1-1-1-1v2h-1l-1-2h3 1v-1h1c1 0 2 0 3 1h2l1 1h1c0-1 0 0 1-1l1 1h1c2 0 1 1 2 1h4v-2h1z" class="Z"></path><path d="M731 152l8 3h0 1c1 0 1 0 2 1h0 1c-1-1-1-2-2-3h0c4 2 8 5 12 8l2-1c1 1 1 2 2 2 2 2 3 3 5 4l1 1c2 2 4 3 4 6h0l-1 2-5-5c-1 0-2 1-2 2l1 1h1c-1 1-1 1-2 1v-2c-1-1-2-1-3-2v2s-1 1-2 1l-5-4-6-4-2-1-2-1-2-1-2-2v-2c-1-2-4-3-5-4h2c1 1 2 1 4 1-2-1-3-2-4-3h-1z" class="V"></path><path d="M741 153c4 2 8 5 12 8h0v1l1 2v1c-1 0-1 0-2-1-4-3-9-6-13-9h0 1c1 0 1 0 2 1h0 1c-1-1-1-2-2-3h0z" class="f"></path><path d="M756 170c-1-1-2-1-2-2l-1-1h-1c-4-3-8-4-11-7 2-1 8 5 10 5l1-1c1 1 1 1 2 1v-1l-1-2v-1h0l2-1c1 1 1 2 2 2 2 2 3 3 5 4l1 1c2 2 4 3 4 6h0l-1 2-5-5c-1 0-2 1-2 2l1 1h1c-1 1-1 1-2 1v-2c-1-1-2-1-3-2z" class="E"></path><path d="M753 161l2-1c1 1 1 2 2 2 2 2 3 3 5 4l1 1c2 2 4 3 4 6-2-1-3-3-5-4l-9-8h0z" class="m"></path><path d="M214 134c3-2 5-3 9-3h1c1 1 2 1 5 1h1c1 1 1 1 2 1h3 0c4 1 9 3 12 5l3 3 2 2c-1-1-1-1 0-2v-1c1 1 1 2 1 3 1 0 2 1 3 1l1-1 1-1h1 1v2c3 3 3 7 4 11l-1 1h0l-1 4v1h2l-1 1v1c1 1 1 1 1 2h0l-1 1c-1 2-3 4-5 6 0-2-1-3-1-4-1 0-1 1-2 0v-1l-2-3 1-2 1-6h-1c1-2 1-3 0-4 0-1 0-1-1-2 0-1 0-3-1-5h0l-2-1c-2-4-6-7-11-8-1-1-3-1-4-2s-5 1-7 0h0c-1 1-2 1-3 1h0-1v-2c-1 1-2 1-3 0h0l-1 2-2-1h-4z" class="L"></path><path d="M259 142h1v2c-1 1-1 2-2 3h-1v-3l2-2h0z" class="J"></path><path d="M252 145h1c1 1 1 1 1 3h1 2v1c0 1 0 1 1 2l-2 2h2v4 3c-1-2-1-3-2-4h-1-1c1-2 1-3 0-4 0-1 0-1-1-2 0-1 0-3-1-5z" class="h"></path><path d="M258 157l1-1c1 0 1-1 1-1 1-1 1-1 2-1 1 1 1 1 1 2l-1 4v1h2l-1 1v1c1 1 1 1 1 2h0l-1 1c-1 2-3 4-5 6 0-2-1-3-1-4-1 0-1 1-2 0v-1l-2-3 1-2 1-6h1c1 1 1 2 2 4v-3z" class="k"></path><path d="M262 161h2l-1 1v1c1 1 1 1 1 2h0l-1 1h-1l-1-2 1-3z" class="K"></path><path d="M261 164l1 2h1c-1 2-3 4-5 6 0-2-1-3-1-4l4-4z" class="D"></path><path d="M258 157l1-1c1 0 1-1 1-1 1-1 1-1 2-1 1 1 1 1 1 2l-1 4c-1 0-1 0-2 1h0c0 2-1 3-2 4h-1c0-2 0-4 1-5v-3z" class="w"></path><path d="M536 140h3c1 1 1 2 1 3h1l1-2v1c1 0 2 1 3 1v5 1c-2 1-3 3-4 4v2l-1 1v8 2l-1 1h0l-5-5-1-1h-1c0-1-2-2-3-4h-1l-3-4v-4l-1-2v-2l2-1v1c1-1 2-2 2-3v1h2c2-2 3-2 6-3z" class="F"></path><path d="M525 149l1 2c1 0 1-1 2-2v4c0 1 1 2 2 3v-1c1 0 1-1 0-1v-3h1c0 1 0 2 1 3v2h1c1 2 2 3 2 5l-1 1-1-1h-1c0-1-2-2-3-4h-1l-3-4v-4z" class="L"></path><path d="M538 148v-2l1-1c0 2 0 2 1 3h4l1 1c-2 1-3 3-4 4v2l-1 1c0-1-1-2-3-3l-1 1v2l-1 1-1-2-1 1h-1v-2-2c0-1 0 0 1-1h0 1c0-1 0-1 1-2 0 1 1 2 2 2 0-1 1-2 1-3z" class="I"></path><path d="M537 153h1c1-1 1-2 1-3 1 1 2 2 2 3v2l-1 1c0-1-1-2-3-3z" class="L"></path><path d="M537 153c2 1 3 2 3 3v8 2l-1 1h0l-5-5 1-1c0-2-1-3-2-5l1-1 1 2 1-1v-2l1-1z" class="r"></path><path d="M536 140h3c1 1 1 2 1 3h1l1-2v1c1 0 2 1 3 1v5 1l-1-1h-4c-1-1-1-1-1-3l-1 1v2c-1-1-1-2-1-3h-1v4c-1-1-1-1-1-2-1 1-1 1-2 1l-1-1v-3c-1 1-1 0-1 1h-2l-1-2h2c2-2 3-2 6-3z" class="a"></path><path d="M539 145c1 0 3-1 4-1 1 2 1 2 1 4h-4c-1-1-1-1-1-3z" class="B"></path><path d="M528 143h2c2-2 3-2 6-3l-1 3-1-1-1 1c1 2 1 2 1 4h-2v-3c-1 1-1 0-1 1h-2l-1-2z" class="x"></path><path d="M499 148c0-1 0-1 1-2l1 1c1-1 1-3 1-4 1-1 3 0 4-1 0 0 0-1 1-1 0 1 0 3 1 4h2l1-1v1h1v-1h4c1-1 1-1 2-3v1h1c0 1 1 2 1 3h1 1v-1l1-1v3 1 1h1v-1l1 2v4l-7-6c-2 1-3 4-4 6v1c0 1-1 2-1 2l-1-1-1-1c-2 1-4 3-5 4-2 0-3 0-4 1-2 1-4 3-6 4h-1-1l-5-6c1-1 2-1 3-1 0-1 2 0 2 1l1-1c-1-3-1-4-1-7 2 2 2 0 4 1l1-2z" class="k"></path><path d="M497 160v-2l1-1c0-1 0-2 1-3 0 1 1 1 1 1 1 1 4 1 5 1l-3 3c-2 1-4 3-6 4l1-3z" class="P"></path><path d="M489 157c1-1 2-1 3-1 0-1 2 0 2 1l-1 1c2 0 3 0 4 2l-1 3h-1-1l-5-6z" class="H"></path><path d="M517 146l1 1h0c-2 1-3 4-4 6v1c0 1-1 2-1 2l-1-1-1-1c-2 1-4 3-5 4-2 0-3 0-4 1l3-3c1-1 2-2 3-2l9-8z" class="U"></path><path d="M499 148c0-1 0-1 1-2l1 1c1-1 1-3 1-4 1-1 3 0 4-1 0 0 0-1 1-1 0 1 0 3 1 4h2l1-1v1h1v-1h4c1-1 1-1 2-3v1h1c0 1 1 2 1 3h1 1v-1l1-1v3 1 1h1v-1l1 2v4l-7-6h0l-1-1h-4c-1 1-3 1-4 1l-1 1h-1-1 0-2v2l-1-1v-1h-1l-1 2h0l-1-1-1-1z" class="D"></path><path d="M518 147c1-1 1-1 2-1 1 1 2 2 2 3h1l1-1v-1l1 2v4l-7-6h0z" class="Z"></path><path d="M381 135c1 0 5 0 6 1v1c1 1 1 1 2 1l3 1s1 0 1 1h-2l-2 1v1h0l-2 2-3-1c-2 1-3 1-5 1-3 0-8-2-11-1h0c-3-1-8 0-10 0l-3 1h-3-4l-4 1c-3 0-4 0-6 1h0l-1-1c-2 0-4 1-6 1h1c0-1 0-1 1-1-2-2-4-1-6-2-1 0-2 0-3 1h-1-1s-1 1-2 1l-4 1h-5l-3 1c6-3 13-4 19-5 3-1 5 0 8-1l3-1 2-1 4-1c3-1 6-1 10-1h21c2 0 4-2 6-2z" class="P"></path><path d="M388 139l3 1-2 1v1h0l-2 2-3-1c-2 1-3 1-5 1-1-1-1-1-1-3l1-1c4 1 6 1 9-1z" class="f"></path><path d="M384 143l-2-1v-1c3 0 5 0 7 1h0l-2 2-3-1z" class="R"></path><path d="M381 135c1 0 5 0 6 1v1c1 1 1 1 2 1l3 1s1 0 1 1h-2l-3-1-8-1-5-1c2 0 4-2 6-2z" class="J"></path><path d="M354 137h21c1 0 5 1 5 1-5 2-11 1-17 1l-17 1c-2 0-5 1-6 1s-1-1-2-1l2-1 4-1c3-1 6-1 10-1z" class="Z"></path><path d="M327 142h7 4c4 1 6 1 9 0 1-1 2 0 3 0 2-1 8 0 11 0 3-1 6-1 10-1h0c1 0 3 0 4-1h4l-1 1c0 2 0 2 1 3-3 0-8-2-11-1h0c-3-1-8 0-10 0l-3 1h-3-4l-4 1c-3 0-4 0-6 1h0l-1-1c-2 0-4 1-6 1h1c0-1 0-1 1-1-2-2-4-1-6-2-1 0-2 0-3 1h-1-1s-1 1-2 1l-4 1h-5l-3 1c6-3 13-4 19-5z" class="b"></path><path d="M412 144c0-1 0-1 1-1h2c1 1 3 1 5 1 1 1 3 0 3 1h2c3 0 8 2 11 3h1c1 1 2 1 3 1 2 0 3 1 6 0l2 1h1c1 0 1 1 1 2l1-1 2 1c0 1 0 1 1 2 0-1 1-1 2-2 1 0 1 1 2 0s2-1 4-2l1 2v-1c1 1 1 1 2 3l-2 4c0 1 0 2 1 3v1l-2 3-1-1c0-1 1-2 0-3s-1-1-1-2h-1l-2 5v2l-1 1c-1-2-1-2-3-3l-1-1c-1-1-1-1-2 0 1 1 2 1 3 3v1h-1l1 1v1l1 1c0 1 1 2 2 3-1 0-2-1-3-2v-1h-1l-1-1c-2-1-5-4-6-6h0c-1-1-2-1-2-2-2-1-3-2-4-2h-1c-3-1-6-4-8-5h-1c2 2 7 5 7 7v2l-14-11c-3-1-7-3-10-5l-1-1s0-1 1-2h0z" class="E"></path><path d="M454 154c0-1 1-1 2-2 1 0 1 1 2 0s2-1 4-2l1 2v-1c1 1 1 1 2 3l-2 4c-1-1-1-1-3-2-1 0-2 1-3 1l-1-2h-1v3h-1l-1-1c-1 0-1 0-2 1 0 0-2 0-2 1 0-1 0-1 1-2 0-1 0-2 1-3v1l1 1c0-1 1-2 2-2z" class="q"></path><path d="M463 151c1 1 1 1 2 3l-2 4c-1-1-1-1-3-2-1 0-2 1-3 1l-1-2h-1c0-1 0-1 1-1h1c1 0 2-1 3-1s1 0 2-1v4c1-2 1-3 1-4v-1z" class="T"></path><path d="M434 153h3c1 1 2 1 3 1s1 1 2 1v1c1 2 4 5 6 6h2l1-3h1l2 2 2-1 1 1c-1 1-1 2-1 3h1v2l-1 1c-1-2-1-2-3-3l-1-1c-1-1-1-1-2 0 1 1 2 1 3 3v1h-1l1 1v1l1 1c0 1 1 2 2 3-1 0-2-1-3-2v-1h-1l-1-1c-2-1-5-4-6-6h0c-1-1-2-1-2-2-1-3-7-6-9-8z" class="U"></path><path d="M415 143c1 1 3 1 5 1 1 1 3 0 3 1h2c3 0 8 2 11 3h1c1 1 2 1 3 1 2 0 3 1 6 0l2 1h1c1 0 1 1 1 2l1-1 2 1c0 1 0 1 1 2-1 0-2 1-2 2l-1-1v-1c-1 1-1 2-1 3-1 1-1 1-1 2-1 0-1 1-1 1l-2-2h-1c-3-2-1-3-1-5-1 1-2 1-2 1-1 0-1-1-2-2-2 0-3-1-5-2 0 0-2-1-2-2h-4c-2 0-4-1-6-1-1 1-7-2-9-3l1-1z" class="d"></path><path d="M412 144c0-1 0-1 1-1h2l-1 1c2 1 8 4 9 3l11 6c2 2 8 5 9 8-2-1-3-2-4-2h-1c-3-1-6-4-8-5h-1c2 2 7 5 7 7v2l-14-11c-3-1-7-3-10-5l-1-1s0-1 1-2h0z" class="l"></path><path d="M412 144c4 2 8 3 11 5 0 2 3 3 3 4-2 0-3-2-4-1-3-1-7-3-10-5l-1-1s0-1 1-2z" class="P"></path><path d="M569 139h1l1 1 4-1h0c1 0 2 1 3 1s1-1 2-1h2c2-1 5 0 7-1 1 0 1 1 2 0h2 1s1-1 2 0c1 0 1 0 2-1h2c2 0 2 0 3 1 1 0 1 0 1-1h3v-2h1c1 3 1 1 4 2h1c1 0 1-1 3 0v1c2 2 1 0 3 0 0 0 1 1 2 1l2-2c3 1 4 0 7 0s5 0 9 1l-10 2c-2 0-4 1-6 2-3 1-6 1-8 3-3 1-4 2-7 2-2 0-4 2-6 2-1 0-1 0-1 1h-4c-3 0-7 0-10 1h0l-2 1h-1l-1 2c-1 0-2 0-2 1l-2-1h-1v-1c-1 0-1 0-2 1-1-1-1-2-1-3h-1v2h-1v-3l-1 1c0 1 0 2-1 2l1 1-1 1c-1 0-1-1-1-2-1-1-1-2-2-3l-1-1c-1-1-1-2-2-2h-1c-2-1-3-1-5-2h0c-1 0-1-1-1-1v-3l1 1 4-2 1 1 1-1 1 1c1-1 2-1 3-2z" class="e"></path><path d="M565 147c0-2 0-3 1-4 1 1 0 2 1 4l1-3h1v2h1v-1l1-1v2h4l1-2c0 2 1 3 2 4 1-1 1 0 1-1 1-1 1-1 3-2v2l1 1v-4h1l1 2h1v2h-2c-1 1-1 2-2 3-1 0-1 1-1 2l-1-1c-1 1-1 1-1 2h-1v-1c-1 0-1 0-2 1-1-1-1-2-1-3h-1v2h-1v-3l-1 1c0 1 0 2-1 2l1 1-1 1c-1 0-1-1-1-2-1-1-1-2-2-3l-1-1c-1-1-1-2-2-2z" class="J"></path><path d="M576 144h-1c1-1 1-1 2-1 1-1 2-1 2-2h1 2c1 1 1 1 2 1 0 0 1 1 2 1l1-1c2-1 2-1 4 0 1-1 4-2 5-2 1 1 2 0 3 0h1l2 1c2 0 1-2 3-3l1 2 2-2 1 1c1 0 1 0 2 1h2 1 8c3-1 4-1 7 0-2 0-4 1-6 2-2-1-4-1-7 0-2 0-4 1-6 1h-1l-1-1h-1-1-1-1c-1 1-2 1-4 1h-1c-1 0-2 0-3 1s-1 1-2 1l-1-2c-2 1-1 2-3 1h0c-1 0-1 1-2 2h-2-1l-1-2h-1v4l-1-1v-2c-2 1-2 1-3 2 0 1 0 0-1 1-1-1-2-2-2-4z" class="D"></path><path d="M586 146h2c1-1 1-2 2-2h0c2 1 1 0 3-1l1 2c1 0 1 0 2-1s2-1 3-1h1c2 0 3 0 4-1h1 1 1 1l1 1h1c2 0 4-1 6-1 3-1 5-1 7 0-3 1-6 1-8 3-3 1-4 2-7 2-2 0-4 2-6 2-1 0-1 0-1 1h-4c-3 0-7 0-10 1h0l-2 1h-1l-1 2c-1 0-2 0-2 1l-2-1c0-1 0-1 1-2l1 1c0-1 0-2 1-2 1-1 1-2 2-3h2v-2z" class="L"></path><defs><linearGradient id="AE" x1="551.898" y1="145.354" x2="562.758" y2="175.04" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2b"></stop></linearGradient></defs><path fill="url(#AE)" d="M545 143v-2h1c0 1 0 1 1 2v-1c1 1 2 1 3 1l1-1c1 1 2 1 3 2v1l1-1v1h4c2 1 3 1 5 2h1c1 0 1 1 2 2l1 1c1 1 1 2 2 3 0 1 0 2 1 2 0 3 1 5 1 8-1 2-2 4-3 7v1l-4 4-3 2c-2 1-4 2-5 2h-5l-3-2c-4-3-7-6-10-10l1-1v-2-8l1-1v-2c1-1 2-3 4-4v-1-5z"></path><path d="M558 148h2c2 1 2 1 3 3l-1 1c-1 1-2 1-3 0s-2-1-2-2l1-2z" class="U"></path><path d="M545 143v-2h1c0 1 0 1 1 2v-1c1 1 2 1 3 1l1-1c1 1 2 1 3 2v1l-3-1c0 1 0 1-1 2h-1c-1 1-3 2-4 2v-5z" class="e"></path><path d="M548 157c2-1 3-1 5-1h0 1 1 2c1 1 2 1 3 1l1-1 1 1h0 0l3-3c1 0 2 1 3 2v2l1 2h-1c0 1-1 2-1 3-1 3-2 7-5 9h-2c2-2 4-3 5-6-1-1-1-2-2-2 0-1 0-2-1-3l-2-1v-1l-1-1-2 1c-1 0-1 0-2-1v-1h-2l-1 1c-2 0-3 0-4-1z" class="H"></path><path d="M567 163c1 1 1 1 1 2s0 2-1 3h0c-1 2-2 4-4 4l-2 2c-1 0-2 0-3 1h2c1 1 1 2 2 2-2 1-4 2-5 2h-5l-3-2c-4-3-7-6-10-10l1-1c1 1 1 1 3 1v-2c1 3 3 5 5 7 1 1 4 1 5 1 3 0 4 0 7-1h2c3-2 4-6 5-9z" class="B"></path><path d="M558 175h2c1 1 1 2 2 2-2 1-4 2-5 2h-5l-3-2 1-1c1-1 6-1 8-1h0z" class="a"></path><path d="M541 155v1c1 1 0 3 2 3 1 0 1 0 3-1h2v-1c1 1 2 1 4 1l1-1h2v1c1 1 1 1 2 1l2-1 1 1v1l2 1c1 1 1 2 1 3 1 0 1 1 2 2-1 3-3 4-5 6-3 1-4 1-7 1-1 0-4 0-5-1-2-2-4-4-5-7v2c-2 0-2 0-3-1v-2-8l1-1z" class="V"></path><path d="M541 155v1c1 1 0 3 2 3 1 0 1 0 3-1h2v-1c1 1 2 1 4 1l1-1h2v1c1 1 1 1 2 1l-1 1h-2v-1c-5 0-7 0-10 3 0 1-1 2-1 3v2c-2 0-2 0-3-1v-2-8l1-1z" class="C"></path><path d="M557 159l2-1 1 1v1l2 1c1 1 1 2 1 3v-1h-2c-1-1-2-1-3-2h-1v2c0 1 0 1-1 2-1 0-2 1-3 0-1 1-2 1-2 3h0c-1 1-1 1-2 1-1-1-1-2-1-4v-1l2-2h2 2v-2h2l1-1z" class="g"></path><path d="M551 168c0-2 1-2 2-3 1 1 2 0 3 0 1-1 1-1 1-2v-2h1c1 1 2 1 3 2h2v1c1 0 1 1 2 2-1 3-3 4-5 6-3 1-4 1-7 1-1-1-1-3-2-5zm-77-16l1 1c0-1 0-2 1-3 0-1 3-1 4-1v4l1 1h1l1-1c1 0 1 1 2 1h2c0 1 1 2 2 3l5 6c-1 0-2 0-2 1s1 3 2 4c-1 1-2 3-4 3v1c-1 1 0 2-2 3-1 1-1 1-1 2-1 1-2 1-2 2h-1c-1 1-1 2-2 3-2 1-4 3-5 4l-3 3-3 3-3 3-6-13c-1-2-3-5-4-7l-2-2c-1-1-2-2-2-3l-1-1v-1l-1-1h1v-1c-1-2-2-2-3-3 1-1 1-1 2 0l1 1c2 1 2 1 3 3l1-1v-2l2-5h1c0 1 0 1 1 2s0 2 0 3l1 1 2-3v-1c-1-1-1-2-1-3l2-4c-1-2-1-2-2-3l1-1h2 0v3h1c0-1 0 0 1-1l1 1c1-1 1-2 2-2s2 0 3 1h0z" class="n"></path><path d="M483 164l2-2c1 2 3 3 4 5-1 0-1 1-2 2 0 1-1 1-1 2 0-2 0-5-1-6h-1-1v-1z" class="g"></path><path d="M457 164l2-5h1c0 1 0 1 1 2s0 2 0 3l1 1 2-3v2h1 0c0 2-1 4-1 7l-1-4h0l1-1-1-1c0 1-1 3-2 4s-1 2-3 2c-1-1-2-2-3-4 0-1-1-2-2-3h0c2 1 2 1 3 3l1-1v-2z" class="W"></path><path d="M457 164l2-5h1c0 1 0 1 1 2s0 2 0 3l1 1c0 1 0 2-1 3l-1-1v-3h0c-1 1-2 2-2 3v1l-1 1-1-2h0l1-1v-2z" class="N"></path><path d="M468 171h2v-1c1 1 0 3 2 4 1 0 2 0 3 1 2 0 6 1 8 3v1c-1 1-2 2-4 2-3 1-7 0-9-2-3-2-5-4-6-8l4 1v-1z" class="a"></path><path d="M474 152l1 1c0-1 0-2 1-3 0-1 3-1 4-1v4l1 1h1l1-1c1 0 1 1 2 1h2c0 1 1 2 2 3l5 6c-1 0-2 0-2 1s1 3 2 4c-1 1-2 3-4 3 1-1 0-3-1-4-1-2-3-3-4-5l-2 2-1-2c-1 0-1 0-2 1h0c-2 0-3 0-4 1h-1c-1 1-2 3-2 4 0 3 0 4 2 7-1-1-2-1-3-1-2-1-1-3-2-4v1h-2v1l-4-1h0c0-3 1-5 1-7h0-1v-2-1c-1-1-1-2-1-3l2-4c-1-2-1-2-2-3l1-1h2 0v3h1c0-1 0 0 1-1l1 1c1-1 1-2 2-2s2 0 3 1h0z" class="M"></path><path d="M467 156h4s1 0 2-1h4l1 1c-4 1-8 2-11 5 0 1-1 2-2 3h0-1v-2-1l3-5z" class="U"></path><path d="M468 171c0-2 0-4 1-5 2-2 2-4 5-5 1 0 2-1 3 0h3 4l1 1-2 2-1-2c-1 0-1 0-2 1h0c-2 0-3 0-4 1h-1c-1 1-2 3-2 4 0 3 0 4 2 7-1-1-2-1-3-1-2-1-1-3-2-4v1h-2z" class="C"></path><path d="M474 152l1 1c0-1 0-2 1-3 0-1 3-1 4-1v4l1 1h1l1-1c1 0 1 1 2 1h2c0 1 1 2 2 3l5 6c-1 0-2 0-2 1l-4-4c-3-4-6-4-10-4l-1-1h-4c-1 1-2 1-2 1h-4l-3 5c-1-1-1-2-1-3l2-4c-1-2-1-2-2-3l1-1h2 0v3h1c0-1 0 0 1-1l1 1c1-1 1-2 2-2s2 0 3 1h0z" class="R"></path><path d="M474 152l1 1c0-1 0-2 1-3 0-1 3-1 4-1v4l-1-1c-2 2-3 1-4 2-1 0-1-1-1-1h-3l-2 2-1-1h-1v2l-3 5c-1-1-1-2-1-3l2-4c-1-2-1-2-2-3l1-1h2 0v3h1c0-1 0 0 1-1l1 1c1-1 1-2 2-2s2 0 3 1h0z" class="P"></path><path d="M320 138l10-1v1h0c-2 0-3 0-4 1l1 1s1 0 1-1l1 1h4 1c0 1 0 1 1 1-3 1-5 0-8 1-6 1-13 2-19 5l3-1h5l4-1c1 0 2-1 2-1h1 1c1-1 2-1 3-1 2 1 4 0 6 2-1 0-1 0-1 1h-1c2 0 4-1 6-1v3c-12 1-24 4-35 10l-12 6c-1 1-5 5-6 5-6 0-9 5-14 8-2 1-4 3-7 5l-15 16-4-4-1-1v-2l-2 1c0-1 0-1-1-2h-2v-1c3-1 5-2 7-4l6-6 3-3c2-1 3-3 4-4 2-2 4-4 5-6l1-1c1 0 3-1 4-3h0c1 0 2-1 3-1l1-1c0-1 0-3 1-3 0-1 2-1 2-1l1-1c2-1 2 0 3-1v-2l2 2c1-1 2-1 3-2s2-1 3-1c4-2 10-4 14-6l3-1 5-2c2 0 5-1 7 0 2 0 4 0 6-1-1 0-1-1-2-2v-1z" class="p"></path><path d="M279 154v-2l2 2c1-1 2-1 3-2s2-1 3-1c-2 2-4 3-7 4-2 1-3 3-5 3s-3 1-3 2c0-1 0-3 1-3 0-1 2-1 2-1l1-1c2-1 2 0 3-1z" class="o"></path><path d="M251 179l1 1c-3 3-7 6-8 9v1 1h4v1l-1 2v-1c-2 0-2 0-3 1l-1-1v-2l-2 1c0-1 0-1-1-2h-2v-1c3-1 5-2 7-4l6-6z" class="U"></path><path d="M292 152l9-3c-2 2-4 3-6 4l-1 2h-1-2s-1 0-1 1c-1 1-2 1-3 2-3 1-5 4-8 4-1 2-2 1-3 2-2 1-3 2-4 3l-2-1 1-1c2-1 4-3 6-4l5-3 1-1c1 0 2-1 4-2h1c0-1 1-1 2-1l2-2z" class="T"></path><path d="M320 138l10-1v1h0c-2 0-3 0-4 1l1 1s1 0 1-1l1 1h4 1c0 1 0 1 1 1-3 1-5 0-8 1-6 1-13 2-19 5l-7 2-9 3v-1l24-9c2 0 4 0 6-1-1 0-1-1-2-2v-1z" class="Z"></path><path d="M257 177c4 0 4-2 7-4 1 0 1-1 2-1v1c-1 2-3 3-4 5h0l1 4-15 16-4-4c1-1 1-1 3-1v1l1-2v-1h-4v-1l13-13z" class="j"></path><path d="M308 147l3-1h5l4-1c1 0 2-1 2-1h1 1c1-1 2-1 3-1 2 1 4 0 6 2-1 0-1 0-1 1h-1c2 0 4-1 6-1v3c-12 1-24 4-35 10l-12 6c-1 1-5 5-6 5-6 0-9 5-14 8-2 1-4 3-7 5l-1-4h0c1-2 3-3 4-5v-1c-1 0-1 1-2 1-3 2-3 4-7 4 3-2 6-4 8-7h0c2-1 3-2 5-4l2 1c1-1 2-2 4-3 1-1 2 0 3-2 3 0 5-3 8-4 1-1 2-1 3-2 0-1 1-1 1-1h2 1l1-2c2-1 4-2 6-4l7-2z" class="m"></path><path d="M272 172l7-5 1 1c-4 2-8 4-10 9-2 1-4 3-7 5l-1-4h0c1-2 3-3 4-5v-1c-1 0-1 1-2 1-3 2-3 4-7 4 3-2 6-4 8-7v1c1 0 1 1 2 1l3-3v1 3l2-1z" class="g"></path><path d="M270 166l2 1c1-1 2-2 4-3 1-1 2 0 3-2 3 0 5-3 8-4 1-1 2-1 3-2 0-1 1-1 1-1l1 2h0c-1 1-1 1-1 2-6 4-15 7-20 12l1 1-2 1v-3-1l-3 3c-1 0-1-1-2-1v-1h0c2-1 3-2 5-4z" class="N"></path><path d="M308 147l3-1h5l4-1c1 0 2-1 2-1h1 1c1-1 2-1 3-1 2 1 4 0 6 2-1 0-1 0-1 1h-1c-13 3-25 8-36 13l-1-1 1-1c0-1 0-1-1-1v-1l1-2c2-1 4-2 6-4l7-2z" class="R"></path><path d="M214 134h4l2 1 1-2h0c1 1 2 1 3 0v2h1 0c1 0 2 0 3-1h0c2 1 6-1 7 0s3 1 4 2c5 1 9 4 11 8l2 1h0c1 2 1 4 1 5 1 1 1 1 1 2 1 1 1 2 0 4h1l-1 6-1 2 2 3v1c1 1 1 0 2 0 0 1 1 2 1 4-1 1-2 3-4 4l-3 3-6 6c-2 2-4 3-7 4-2 1-4 1-5 1h-1-2 0c1-1 2-2 2-3l-1-2c-3 1-6 0-9-1v-3c-2-2-1-1-4-2-2 2-3 4-5 6-5-4-8-9-10-15-1-4-1-7-4-10h0c-1-2 0-4 0-6-1 0-2-5-2-6l-3-8 4 2v4h1c1-1 1-2 1-4h4v-1c1-1 1-2 2-4l1 1 1-2 1 1 1-1h1v1l3-3z" class="f"></path><path d="M216 144l1-1c-1 2-1 5 0 6 0 1 2 2 2 2l-1 1h0c0 1-1 0-2 0-1-2-1-5 0-8z" class="E"></path><path d="M229 147h5c1 1 2 2 3 2-2 1-3 1-5 2h-2l-1-1h0c-1 0-2 1-3 2 0 1 0 0-1 1v-1c1-2 3-3 4-5z" class="R"></path><path d="M235 159l-1 1h-2l-1-1h-1c-2-1-2-1-2-2l1-2h-1c1-1 1-2 3-3 1 1 0 2 2 3l2-1c0-1 1-1 2-1 1 1 1 1 1 2 0 2-1 3-3 4z" class="i"></path><path d="M221 139c1 0 3-1 4 0s1 0 2 1c1 0 2 1 2 2l1 1 1 1v2l-3 1c-3 2-4 3-5 6-1 1-1 2-1 4h-1v-3l-2-3s-2-1-2-2c-1-1-1-4 0-6l4-4z" class="g"></path><path d="M225 146h1v1l-3 3h-1v-2c1-2 1-2 3-2z" class="n"></path><path d="M221 139c1 0 3-1 4 0s1 0 2 1c1 0 2 1 2 2h-2l-1-1c-2 0-4 2-5 4-1 0-1 2-1 3 0 2 1 4 1 6l-2-3s-2-1-2-2c-1-1-1-4 0-6l4-4z" class="c"></path><path d="M225 135h0c1 0 2 0 3-1h0c2 1 6-1 7 0s3 1 4 2c5 1 9 4 11 8l2 1h0c1 2 1 4 1 5 1 1 1 1 1 2 1 1 1 2 0 4h1l-1 6-1 2-2 3v1h-1c0-2 0-4-1-5-2-3-3-5-5-8v-1h-1c-1 2-1 4-1 6-1 0-1 1-2 2l-3 3c-1-1-1-1-2-1l5-5-1-2c-1 2-1 2-3 3l-1-1c2-1 3-2 3-4l1-1c0-2-1-4-2-5-1 0-2-1-3-2h-5-1l3-1v-2l-1-1-1-1c0-1-1-2-2-2-1-1-1 0-2-1s-3 0-4 0l2-1c1-1 2-1 3-2l-1-1z" class="I"></path><path d="M229 136c1 0 4 0 5 1 3 2 4 2 7 2 0 1 0 2-1 2s-2 0-3 1h-2c-1-1-1-1-1-2v-2h-2c-2 0-2-1-3-2z" class="F"></path><path d="M225 135h0c1 0 2 0 3-1h0c2 1 6-1 7 0s3 1 4 2c5 1 9 4 11 8-1 0-1-1-2-2h-3l1-1c-1-1-2 0-4 0v-2c-1 0 0-1-1 0-3 0-4 0-7-2-1-1-4-1-5-1h0-3l-1-1z" class="H"></path><path d="M223 138c2 0 4-1 6 1 1 1 0 0 0 1 1 0 2 2 3 2 2 1 3-1 4 2h1v-1h1v3h1v-2c1 0 1-1 2-1h1l-1 3h1c1-1 2-2 4-3l1 2h1l1 1c-1 2 0 2 1 3v-1h1s1 2 2 2l-1 1c-1 0-1 0-1 1l-1-1h-1c-1-1-1-2-1-3h-2c-1-1 0-2-2-2v4 1l-1 1c-1 0-1 0-2-1l-1 1c-1 0-1 1 0 1 0 2 0 1-1 3v1c-1 2-1 2-3 3l-1-1c2-1 3-2 3-4l1-1c0-2-1-4-2-5-1 0-2-1-3-2h-5-1l3-1v-2l-1-1-1-1c0-1-1-2-2-2-1-1-1 0-2-1s-3 0-4 0l2-1z" class="Z"></path><path d="M239 157v-1c1-2 1-1 1-3-1 0-1-1 0-1l1-1c1 1 1 1 2 1l1-1v-1-4c2 0 1 1 2 2h2c0 1 0 2 1 3h1l1 1c0-1 0-1 1-1l1-1v3 2l1 1h1l-1 6-1 2-2 3v1h-1c0-2 0-4-1-5l-5-8v-1h-1c-1 2-1 4-1 6-1 0-1 1-2 2l-3 3c-1-1-1-1-2-1l5-5-1-2z" class="r"></path><path d="M240 159c1-1 1-2 1-3s1-2 1-3c1 0 1 0 2 1v1-1h-1c-1 2-1 4-1 6-1 0-1 1-2 2l-3 3c-1-1-1-1-2-1l5-5z" class="J"></path><path d="M244 155h3 0c1 2 2 2 3 2h1 1v1h1v1c0 1 0 2 1 3l-1 2-2 3v1h-1c0-2 0-4-1-5l-5-8z" class="Z"></path><path d="M242 160c0-2 0-4 1-6h1v1l5 8c1 1 1 3 1 5h1v-1l2-3 2 3v1c1 1 1 0 2 0 0 1 1 2 1 4-1 1-2 3-4 4l-3 3-6 6c-2 2-4 3-7 4-2 1-4 1-5 1h-1-2 0c1-1 2-2 2-3l-1-2c-3 1-6 0-9-1v-3c-2-2-1-1-4-2l1-1c0-2 1-3 2-4s1-3 2-3c1-2 2-4 4-5h1l2-2h1l-3-2h4l1 2h1 1c1 0 1 0 2 1l3-3c1-1 1-2 2-2z" class="L"></path><path d="M237 184h3c2 0 1-1 3-1l2 2c-2 2-4 3-7 4-2 1-4 1-5 1h-1-2 0c1-1 2-2 2-3l-1-2h0 3l3-1z" class="B"></path><path d="M253 164l2 3v1c-1 1-2 3-4 4 0 1-1 2-1 3l-1 1h-1c0 2-4 5-5 5l-1 1-2-2h1l-1-2-1 1c-2 2-4 2-6 2l-1-1c1-2 1-1 0-2v-1l1-1c0 1 0 2 1 2h2l2-1c2-1 5-2 7-3l3-4 2-2h1v-1l2-3z" class="q"></path><path d="M248 170l2 1c0 1 0 1-1 2 0 1-1 1-2 2 0-1-1-1-2-1l3-4z" class="T"></path><path d="M238 177c2-1 5-2 7-3 1 0 2 0 2 1-2 2-4 4-6 5l-1-2-1 1c-2 2-4 2-6 2l-1-1c1-2 1-1 0-2v-1l1-1c0 1 0 2 1 2h2l2-1z" class="f"></path><path d="M221 174l2 1c1 1 3 1 4 2 1 0 1 1 2 1 1 1 2 1 3 2l1 1c2 0 4 0 6-2l1-1 1 2h-1l2 2-5 2-3 1h-3 0c-3 1-6 0-9-1v-3c-2-2-1-1-4-2l1-1c0-2 1-3 2-4z" class="E"></path><path d="M240 180l2 2-5 2-3 1v-1h1c1-2 3-3 5-4z" class="T"></path><path d="M223 175c1 1 3 1 4 2 1 0 1 1 2 1 1 1 2 1 3 2l1 1-6 1c-2-1-2-1-2-2h1c2 1 1 1 2 1-1-1-2-3-4-3-1 0-1 0-1-1v-2z" class="b"></path><path d="M242 160c0-2 0-4 1-6h1v1l5 8c1 1 1 3 1 5l-2 2-3 4c-2 1-5 2-7 3l-2 1h-2c-1 0-1-1-1-2l-1 1v1c1 1 1 0 0 2-1-1-2-1-3-2-1 0-1-1-2-1-1-1-3-1-4-2l-2-1c1-1 1-3 2-3 1-2 2-4 4-5h1l2-2h1l-3-2h4l1 2h1 1c1 0 1 0 2 1l3-3c1-1 1-2 2-2z" class="F"></path><path d="M246 169l-1-3 1-1 2 1v1 2h-2z" class="Z"></path><path d="M240 162c1 2 1 3 1 5h-2l-1-1-1 1h-3-1c0-1 1-2 1-3h1c1 0 1 0 2 1l3-3z" class="B"></path><path d="M248 167c1 1 1 1 2 1l-2 2-3 4c-2 1-5 2-7 3v-2c1 0 2-1 3-1h0l1-2 1-1h2s1-1 1-2h2v-2z" class="L"></path><path d="M242 160c0-2 0-4 1-6h1v1l5 8c1 1 1 3 1 5-1 0-1 0-2-1v-1c-1-3-2-5-4-7l-1 1h-1z" class="B"></path><path d="M228 162h4l1 2h1c0 1-1 2-1 3h1 3c2 1 2 2 3 4v2c1 0 2-1 2-1l-1 2h0c-1 0-2 1-3 1-1-2-5-5-7-7-1 0-2-1-3-2l2-2h1l-3-2z" class="I"></path><path d="M228 166l2-2h1c1 2 0 2 0 4-1 0-2-1-3-2z" class="C"></path><path d="M221 174c1-1 1-3 2-3 1-2 2-4 4-5h1c1 1 2 2 3 2 2 2 6 5 7 7v2l-2 1h-2c-1 0-1-1-1-2l-1 1v1c1 1 1 0 0 2-1-1-2-1-3-2-1 0-1-1-2-1-1-1-3-1-4-2l-2-1z" class="q"></path><path d="M221 174c1-1 1-3 2-3 1-2 2-4 4-5h1c1 1 2 2 3 2 2 2 6 5 7 7v2l-2 1v-2l-1-1v-1c-2-1-3-2-4-2h-2c0-1-1-1-2-2-1 0-2 0-3 1h-1l1 1c1 2 2 3 3 5-1-1-3-1-4-2l-2-1z" class="d"></path><path d="M214 134h4l2 1 1-2h0c1 1 2 1 3 0v2h1l1 1c-1 1-2 1-3 2l-2 1-4 4-1 1c-1 2-2 3-3 5v5c0 2-1 4 0 6h0c0 2 0 4 2 5h1c0 1 0 1-1 1 1 2 2 3 3 4h1 2c0-1 0-3-1-4l1-1h2l1-1 1-1-1-2v-2c1 0 3 2 4 3l3 2h-1l-2 2h-1c-2 1-3 3-4 5-1 0-1 2-2 3s-2 2-2 4l-1 1c-2 2-3 4-5 6-5-4-8-9-10-15-1-4-1-7-4-10h0c-1-2 0-4 0-6-1 0-2-5-2-6l-3-8 4 2v4h1c1-1 1-2 1-4h4v-1c1-1 1-2 2-4l1 1 1-2 1 1 1-1h1v1l3-3z" class="I"></path><path d="M207 156l1-6 3-1c-1 5-1 9 1 14-1 1-2 1-3 1v-2l-2-6z" class="f"></path><path d="M206 146c1 1 2 2 4 2v-1c1 1 1 0 1 1v1l-3 1-1 6c-1-1-2-1-3-1-1-2 1-7 2-9z" class="X"></path><path d="M208 142c1 0 2 0 3-1 0-1 1-2 2-2s1 1 2 2l-3 4-1 3c0-1 0 0-1-1v1c-2 0-3-1-4-2l2-4z" class="E"></path><path d="M214 134h4l2 1 1-2h0c1 1 2 1 3 0v2h1l1 1c-1 1-2 1-3 2l-2 1-4 4-1 1c-1 2-2 3-3 5 0-1-1-2-1-4l3-4c-1-1-1-2-2-2s-2 1-2 2c-1 1-2 1-3 1 0-1 2-3 3-5l3-3z" class="T"></path><path d="M215 141c2-3 6-5 9-6h1l1 1c-1 1-2 1-3 2l-2 1-4 4-1 1c-1 2-2 3-3 5 0-1-1-2-1-4l3-4z" class="D"></path><path d="M208 136l1 1 1-1h1v1c-1 2-3 4-3 5l-2 4c-1 2-3 7-2 9 1 0 2 0 3 1l2 6-1 3c-1 0-2 1-3 1h-1c0-2 0-5-1-7h-3l-1 1c-1-2 0-4 0-6-1 0-2-5-2-6l-3-8 4 2v4h1c1-1 1-2 1-4h4v-1c1-1 1-2 2-4l1 1 1-2z" class="c"></path><path d="M194 140l4 2v4h1c1-1 1-2 1-4h4c-1 1-2 1-3 2 0 2 0 4-1 6l-1-1c-1 0-1-1-2-1l-3-8z" class="E"></path><path d="M208 136l1 1 1-1h1v1c-1 2-3 4-3 5l-2 4c-1 2-3 7-2 9l-1 2h-2c1-7 3-13 6-19l1-2z" class="a"></path><path d="M204 155c1 0 2 0 3 1l2 6-1 3c-1 0-2 1-3 1h-1c0-2 0-5-1-7h-3l-1 1c-1-2 0-4 0-6v1 3h1l1-1h0 2l1-2z" class="N"></path><path d="M221 170c0-1 0-3-1-4l1-1h2l1-1 1-1-1-2v-2c1 0 3 2 4 3l3 2h-1l-2 2h-1c-2 1-3 3-4 5-1 0-1 2-2 3s-2 2-2 4l-1 1c-2 2-3 4-5 6-5-4-8-9-10-15-1-4-1-7-4-10h0l1-1h3c1 2 1 5 1 7h1c1 0 2-1 3-1l1-3v2c1 0 2 0 3-1 1 3 3 6 5 9 2-1 3-1 4-2z" class="B"></path><path d="M209 162v2c1 0 2 0 3-1 1 3 3 6 5 9l2 2c0 2-2 3-4 5 0 1-1 1-2 2l-1-1h-1c-3-3-6-10-7-14h1c1 0 2-1 3-1l1-3z" class="i"></path><path d="M212 180v-4h2v2l1 1c0 1-1 1-2 2l-1-1z" class="N"></path><path d="M639 138c3-1 7-2 11-2 9 0 17 1 26 1 4 0 11-1 15 1v1l2 1c2-1 4-1 6-1l1 1h3c2 1 5 1 7 2 7 2 15 5 21 10h1c1 1 2 2 4 3-2 0-3 0-4-1h-2c1 1 4 2 5 4v2l-3-2-7-3h-1l-4-2h-1c-4-2-9-3-12-4l-5-2c-11-3-21-3-32-3l-24 3c-3-2-7 0-9 1h-2-2c-5 1-11 3-16 5l-9 6c-5 2-8 6-12 10v-2c1-1 0-2 1-3-1-1 0-1-1-2l-2 1-6 6-1 1c0 2-2 2-3 4 0 1-3 6-4 7l-7 14-1 2c0 1 0 0-1 2 0 0-1 1-1 2 0 2-1 4-1 6s0 3-1 5v1s-2 1-2 2c0 0 0 1-1 1h-3v4l1 2v4h-3c-1 0-3 0-4 1h-2c-1-1-2-1-3-1h-1c-1-1-2-1-2-2v-3c0-1 1-1 0-2l-1 4c-1 0-2 1-2 1h-1-2c0 3-2 4-3 5-2-1-2-1-2-3l5-5 5-6v-3c0-1 5-6 7-7h1c0-2 0-2-1-3 1-2 4-3 5-5 1-1 2-2 2-3 1-1 0-2 0-2l-1-1c-1-2-2-2-3-3-1-2-2-3-4-4v-2c-1-2-1-2-1-3h5c1 0 3-1 5-2l3-2 4-4v-1c1-3 2-5 3-7 0-3-1-5-1-8l1-1-1-1c1 0 1-1 1-2l1-1v3h1v-2h1c0 1 0 2 1 3 1-1 1-1 2-1v1h1l2 1c0-1 1-1 2-1l1-2h1l2-1h0c3-1 7-1 10-1h4c0-1 0-1 1-1 2 0 4-2 6-2 3 0 4-1 7-2 2-2 5-2 8-3 2-1 4-2 6-2l10-2z" class="c"></path><path d="M583 158l3-3h3l1 1c-1 1-1 1-2 1-1 1-1 1-1 3h0v-2c-1-1-2 0-4 0z" class="U"></path><path d="M583 158c2 0 3-1 4 0v2c-1 2-3 5-4 7v1h-2 0v-1c0-2 0-4-1-5 0-1 0-1 1-2v1l1 1c1-1 1-2 1-4z" class="g"></path><path d="M602 153c1-1 2-2 4-3h1c0-1 1-1 2-1l8-3c6-3 12-4 19-5l-4 2-8 3v-1l-5 2-6 1c-3 1-6 4-8 6-1 0-2-1-3-1z" class="V"></path><path d="M572 163l2-1v-2h3 1c1 0 1 1 2 3v-1c1 1 1 3 1 5v1h0 2c-3 3-5 6-8 9-1 2-2 4-3 5v-1c1-1 1-2 1-2v-1h-2c0-1 1-3 0-5v-1c-2 1-3 2-5 3h-1l4-4v-1c1-3 2-5 3-7z" class="j"></path><path d="M572 163l2-1v-2h3 1c1 0 1 1 2 3l-1 2h-1 0c-1-1-2-1-3-1l-1-1c-1 2-1 3-2 4v2l-2 2h-1v-1c1-3 2-5 3-7z" class="N"></path><path d="M693 140c2-1 4-1 6-1l1 1h3c2 1 5 1 7 2 7 2 15 5 21 10h1c1 1 2 2 4 3-2 0-3 0-4-1h-2c-5-1-9-4-13-5-5-2-11-4-16-5-3-1-6-1-9-1 0-1 0-2 1-3z" class="W"></path><path d="M624 146c-2 1-5 2-7 3l1 1c7-3 14-6 21-8 3-1 6-1 8-2 1-1 0 0 2-1l1 2h0c1-1 1-1 1-2l30-1c2 1 7 1 10 1v3c-2 2-5 0-8 0-6 2-14 0-20 1h0c2 2 4 1 7 1l-24 3c-3-2-7 0-9 1h-2-2c-5 1-11 3-16 5l-9 6c-5 2-8 6-12 10v-2c1-1 0-2 1-3-1-1 0-1-1-2l-2 1v-1c-2 1-3 2-4 3v-1c1-1 0-1 1-2 0-1 1-1 2-2 1 0 2-1 3-2v-1c2-2 4-3 6-4 1 0 2 1 3 1 2-2 5-5 8-6l6-1 5-2v1z" class="g"></path><path d="M663 143c2 2 4 1 7 1l-24 3c-3-2-7 0-9 1h-2-2c10-3 19-4 30-5z" class="d"></path><path d="M587 160l1 1c3-1 4-2 5-4v-2c1 1 2 1 3 2v1c-1 1-2 2-3 2-1 1-2 1-2 2-1 1 0 1-1 2v1c1-1 2-2 4-3v1l-6 6-1 1c0 2-2 2-3 4 0 1-3 6-4 7l-7 14-1 2c0 1 0 0-1 2 0 0-1 1-1 2 0 2-1 4-1 6s0 3-1 5v1s-2 1-2 2c0 0 0 1-1 1h-3v4l1 2v4h-3c-1 0-3 0-4 1h-2c-1-1-2-1-3-1h-1c-1-1-2-1-2-2v-3c0-1 1-1 0-2l-1 4c-1 0-2 1-2 1h-1-2c0 3-2 4-3 5-2-1-2-1-2-3l5-5 5-6v-3c0-1 5-6 7-7h1c0-2 0-2-1-3 1-2 4-3 5-5 1-1 2-2 2-3 1-1 0-2 0-2l-1-1c-1-2-2-2-3-3-1-2-2-3-4-4v-2c-1-2-1-2-1-3h5c1 0 3-1 5-2l3-2h1c2-1 3-2 5-3v1c1 2 0 4 0 5h2v1s0 1-1 2v1c1-1 2-3 3-5 3-3 5-6 8-9v-1c1-2 3-5 4-7h0z" class="i"></path><path d="M565 206s1 1 1 2-1 3-2 4h-1l2-6z" class="V"></path><path d="M560 201c0 1 0 2-1 3s-2 1-2 2l-1 3h-1l-2-1 7-7z" class="B"></path><path d="M571 178h2v1s0 1-1 2v1c-1 4-2 7-3 10h0v2c0 1-1 1-1 2l-1-1c1-2 0-3 1-6 0 0 0-1 1-2l-1-1 3-8z" class="t"></path><path d="M564 196h1c1 3 1 6 0 10h0l-2 6-1 1v3 4c-1 1-2 2-3 1 1-2 1-5 1-8v-5l-2-2h-1c0-1 1-1 2-2s1-2 1-3c1-2 3-4 4-5z" class="E"></path><path d="M564 196c1 2-1 6-2 9-1 2-1 5-2 8v-5l-2-2h-1c0-1 1-1 2-2s1-2 1-3c1-2 3-4 4-5z" class="S"></path><path d="M587 160l1 1c3-1 4-2 5-4v-2c1 1 2 1 3 2v1c-1 1-2 2-3 2-1 1-2 1-2 2-1 1 0 1-1 2v1c1-1 2-2 4-3v1l-6 6-1 1c0 2-2 2-3 4 0 1-3 6-4 7h-1c-2 1-3 4-5 7 1-2 1-3 2-5l1-2v-1c-3 2-4 6-5 9l-1 3h-1-1c1-3 2-6 3-10 1-1 2-3 3-5 3-3 5-6 8-9v-1c1-2 3-5 4-7h0z" class="N"></path><path d="M578 176h1v2 2c-1 0-1-1-2-2 0-1 0-1 1-2z" class="t"></path><path d="M565 175h1c2-1 3-2 5-3v1c1 2 0 4 0 5l-3 8-2 4v1c-1 0-2 0-3-1v-1l-1 1v1h1l-2 1-1-1c-1-2-2-2-3-3-1-2-2-3-4-4v-2c-1-2-1-2-1-3h5c1 0 3-1 5-2l3-2z" class="n"></path><path d="M552 179h5c-1 0-2 1-3 1 1 1 1 2 2 2 1 1 2 0 3 1 2 3 4 5 6 7h1v1c-1 0-2 0-3-1v-1l-1 1v1h1l-2 1-1-1c-1-2-2-2-3-3-1-2-2-3-4-4v-2c-1-2-1-2-1-3z" class="W"></path><path d="M557 206h1l2 2v5c0 3 0 6-1 8 1 1 2 0 3-1l1 2v4h-3c-1 0-3 0-4 1h-2c-1-1-2-1-3-1h-1c-1-1-2-1-2-2v-3c0-1 1-1 0-2l-1 4c-1 0-2 1-2 1h-1-2c0 3-2 4-3 5-2-1-2-1-2-3l5-5 5-6 6-7 2 1h1l1-3z" class="M"></path><path d="M547 215l6-7 2 1-1 1-1 3h-2c-1 0-2 2-2 2l-5 5c-1 0-1 0-2 1l5-6z" class="C"></path><path d="M518 147l7 6 3 4h1c1 2 3 3 3 4h1l1 1 5 5h0c3 4 6 7 10 10l3 2c0 1 0 1 1 3v2c2 1 3 2 4 4 1 1 2 1 3 3l1 1s1 1 0 2c0 1-1 2-2 3-1 2-4 3-5 5 1 1 1 1 1 3h-1c-2 1-7 6-7 7v3l-5 6-5 5-13 14-7 7-13-13c-1 0-2-1-3 0l-1 1c-1-1-1-1-1-2l-1-1c-2-3-4-5-6-7h-1c0-2-1-2-1-3v-1-1l-1-2c-5-5-10-10-14-15-2-3-5-5-7-8l3-3 3-3 3-3c1-1 3-3 5-4 1-1 1-2 2-3h1c0-1 1-1 2-2 0-1 0-1 1-2 2-1 1-2 2-3v-1c2 0 3-2 4-3-1-1-2-3-2-4s1-1 2-1h1 1c2-1 4-3 6-4 1-1 2-1 4-1 1-1 3-3 5-4l1 1 1 1s1-1 1-2v-1c1-2 2-5 4-6z" class="u"></path><path d="M487 192v-1c0-2 1-3 2-4 0 1 1 1 2 2l-2 2-2 1z" class="M"></path><path d="M494 183c0 3-1 5 0 8-1 0-1 1-2 1v-2l-1-1c-1-1-2-1-2-2 1-2 3-3 5-4z" class="C"></path><path d="M494 182c0-1 0-1 1-2l1 1v1l1-1 1-3c0-1 1-1 2-2 1 1 1 1 2 1h1c-1 1-2 5-3 6-2 1-3 1-3 4 0 1 0 0-1 1l-1-6h-1z" class="G"></path><path d="M491 189l1 1v2c1 0 1-1 2-1v2l-1 6h-1l-3 1-3 1c-1 1-2 1-3 2v1 1c-1-2-3-3-4-5 1-1 2-1 3-3 1-1 1 0 2-1s3-2 3-4l2-1 2-2z" class="o"></path><path d="M489 200c-1-1 0-2 0-3l1-1 1 1c1 1 1 1 1 2l-3 1z" class="J"></path><path d="M491 189l1 1v2c1 0 1-1 2-1v2l-1 2h-1 0c-1-1-1-1-1-2h-1v2c-2 1-3 2-5 2v2h-1l-2-2c1-1 1 0 2-1s3-2 3-4l2-1 2-2z" class="Y"></path><path d="M518 147l7 6 3 4h1c1 2 3 3 3 4-1 1-1 0-2 2 1 1 1 2 2 3v2l2 3c0 1-1 3-3 4 1-1 0-2 0-4h-2l-1-1-1-2-2-3c1 0 1-1 2-2l-3-3-2-2c-1-1-4-4-6-5l-2 1v-1c1-2 2-5 4-6z" class="W"></path><path d="M528 157h1c0 2 0 3-1 5l-2-2 1-2 1-1zm-6 1c0-1 0-3 2-4 1 0 1 0 1 1v4l-1 1-2-2z" class="j"></path><path d="M527 163l5 5 2 3c0 1-1 3-3 4 1-1 0-2 0-4h-2l-1-1-1-2-2-3c1 0 1-1 2-2z" class="O"></path><path d="M506 158c1-1 3-3 5-4l1 1 1 1-37 38c-1-1-2-1-3-1v-1c0-1 0-1 1-2l1-1h-1l3-3c1-1 3-3 5-4 1-1 1-2 2-3h1c0-1 1-1 2-2 0-1 0-1 1-2 2-1 1-2 2-3v-1c2 0 3-2 4-3-1-1-2-3-2-4s1-1 2-1h1 1c2-1 4-3 6-4 1-1 2-1 4-1z" class="j"></path><path d="M502 159c1-1 2-1 4-1-2 3-8 9-11 10h-1 0c-1-1-2-3-2-4s1-1 2-1h1 1c2-1 4-3 6-4z" class="t"></path><path d="M503 177v3 11l-1-3v-3c-3 3-2 7-2 10h0c0 6-1 11-1 16-1-2-1-3-1-5 0 1-1 1-2 1h0c0 2-1 4 0 6v3l-2-1-1 1-10-11v-1-1c1-1 2-1 3-2l3-1 3-1h1l1-6v-2c-1-3 0-5 0-8v-1h1l1 6c1-1 1 0 1-1 0-3 1-3 3-4 1-1 2-5 3-6z" class="d"></path><path d="M498 206v-12c0-2 0-4 1-6v-1c1-2 2-5 4-7v11l-1-3v-3c-3 3-2 7-2 10h0c0 6-1 11-1 16-1-2-1-3-1-5z" class="E"></path><path d="M494 182h1l1 6-1 3 1 1v3 12c0 2-1 4 0 6v3l-2-1c0-2 0-4-1-6 1-3 1-7 0-10l1-6v-2c-1-3 0-5 0-8v-1z" class="c"></path><path d="M493 199c1 3 1 7 0 10 1 2 1 4 1 6l-1 1-10-11v-1-1c1-1 2-1 3-2l3-1 3-1h1z" class="v"></path><path d="M486 201h2l-1 2 1 1v3l-1 1c1 1 1 1 3 2l1-1 1 1v-1h1c1 2 1 4 1 6l-1 1-10-11v-1-1c1-1 2-1 3-2z" class="Z"></path><path d="M486 201h2l-1 2c0 1-1 2-2 2l-2-1v-1c1-1 2-1 3-2z" class="d"></path><path d="M500 195h0c0-3-1-7 2-10v3l1 3h0 1v22h2v3l1-1v1c1 0 1 0 2 1s2 1 3 1l1 1v3l-1 1c0 2 0 3-1 5h-1l-1 1 1 1 1-1c1 1 3 1 4 1l1 1c-1 2-1 3 0 5v1h-2c-2-1-5-4-6-6l-15-15 1-1 2 1v-3c-1-2 0-4 0-6h0c1 0 2 0 2-1 0 2 0 3 1 5 0-5 1-10 1-16z" class="q"></path><path d="M507 225l3 1v2l-1 1 1 1 1-1c1 1 3 1 4 1l1 1c-1 2-1 3 0 5h-1c-4-3-7-6-9-9 0-1 0-1 1-2z" class="j"></path><path d="M500 195h0c0-3-1-7 2-10v3l1 3h0c-1 9 0 17 0 26h0c-1-1-1-4-1-5s-1-2-1-2c0-2 1-2 0-3 0-2 0-2 1-3v-1-3h0l-1-1c0-2-1-2-1-4z" class="c"></path><path d="M500 195c0 2 1 2 1 4l1 1h0v3 1c-1 1-1 1-1 3 1 1 0 1 0 3 0 0 1 1 1 2s0 4 1 5v8c-2-2-3-4-4-6h1l-1-8c0-5 1-10 1-16z" class="i"></path><path d="M504 213h2v3l1-1v1c1 0 1 0 2 1s2 1 3 1l1 1v3l-1 1c0 2 0 3-1 5h-1v-2l-3-1v-2l-1 1h-1c-2-3-1-8-1-11z" class="c"></path><path d="M507 215v1c1 0 1 0 2 1s2 1 3 1l1 1v3l-1 1-3-2c-1-1-2-1-3-2v-3l1-1z" class="n"></path><path d="M504 179c0-4 8-10 10-13l1-1v2 47c-2 2-2 3-2 5l-1-1c-1 0-2 0-3-1s-1-1-2-1v-1l-1 1v-3h-2v-22-12z" class="W"></path><path d="M507 206l3-3h1v3c1 1 1 1 2 1v1c-1 1-2 3-2 4l1 1 1 1c-1 2-2 2-4 3h1l2 1c-1 0-2 0-3-1s-1-1-2-1v-1-1l2 1c2 0 2 0 3-1v-1h-2c0-1 1-3 1-4-1-1-2-2-4-3z" class="g"></path><path d="M504 191v-12c1 3 0 6 1 9 0 2 2 2 1 3-1 2-2 3-1 4v1c1 1 1 3 1 4v1l-1 5h1 1c2 1 3 2 4 3 0 1-1 3-1 4h2v1c-1 1-1 1-3 1l-2-1v1l-1 1v-3h-2v-22z" class="i"></path><path d="M474 189h1l-1 1c-1 1-1 1-1 2v1c1 0 2 0 3 1l-2 2c1 2 3 3 5 4 1 2 3 3 4 5l10 11 15 15c1 2 4 5 6 6h2v-1c-1-2-1-3 0-5l1-1v-2-2h0l1-1v3 1l1-8h0v4l1-2c0 2 0 3 2 4s3 1 5 0c1 0 2-1 2-1 1 0 2-1 3-2l2-2 1-1 2-1v-1l6-6 11-11c1 1 1 1 1 3h-1c-2 1-7 6-7 7v3l-5 6-5 5-13 14-7 7-13-13c-1 0-2-1-3 0l-1 1c-1-1-1-1-1-2l-1-1c-2-3-4-5-6-7h-1c0-2-1-2-1-3v-1-1l-1-2c-5-5-10-10-14-15-2-3-5-5-7-8l3-3 3-3z" class="W"></path><path d="M521 237h-1c0-1 0-1 1-3h2c1 0 1 0 3-1l1 1c-2 1-3 2-6 3z" class="n"></path><path d="M516 231l1-1v-2-2h0l1-1v3 1c0 2 0 5 1 7-1 1-1 3-2 4l-3-3h2v-1c-1-2-1-3 0-5z" class="Q"></path><path d="M490 220l14 14c-1 0-2-1-3 0l-1 1c-1-1-1-1-1-2l-1-1c-2-3-4-5-6-7h-1c0-2-1-2-1-3v-1-1z" class="I"></path><path d="M468 195l3-3h0v2l1 1v2c2 1 2 1 3 2 1 0 3 3 3 4v1h-1c2 2 6 5 7 7 0 1 1 1 2 2 2 1 2 2 3 5-5-5-10-10-14-15-2-3-5-5-7-8z" class="U"></path><path d="M543 213l11-11c1 1 1 1 1 3h-1c-2 1-7 6-7 7v3l-5 6-5 5-13 14-3-3c3-1 4-2 6-3l6-6c1-2 4-6 6-6 0-1 0-1 1-1l1-1c-1-1-1-1-1-2l3-3 1 1c1-1 1-1 1-2-1 1 0 1-1 1v-1c-1 0-2 1-3 2s-2 3-3 4h-1v-1l6-6z" class="g"></path><path d="M532 161h1l1 1 5 5h0c3 4 6 7 10 10l3 2c0 1 0 1 1 3v2c2 1 3 2 4 4 1 1 2 1 3 3l1 1s1 1 0 2c0 1-1 2-2 3-1 2-4 3-5 5l-11 11-6 6v1l-2 1-1 1-2 2c-1 1-2 2-3 2 0 0-1 1-2 1-2 1-3 1-5 0s-2-2-2-4l-1 2v-4h0l-1 8v-1-3l-1 1h0v2 2l-1 1-1-1c-1 0-3 0-4-1l-1 1-1-1 1-1h1c1-2 1-3 1-5l1-1v-3c0-2 0-3 2-5v-47h1 0l1 1c0-2 0-4 1-6h0l1 1v2h1c1 0 1 0 1-1 1 0 3 1 4 1l2 3 1 2 1 1h2c0 2 1 3 0 4 2-1 3-3 3-4l-2-3v-2c-1-1-1-2-2-3 1-2 1-1 2-2z" class="n"></path><path d="M518 166v-4l1 1v2h1c1 0 1 0 1-1 1 0 3 1 4 1l2 3 1 2v3h-1c-3-1-7-4-9-7z" class="L"></path><defs><linearGradient id="AF" x1="528.032" y1="191.593" x2="504.558" y2="202.599" xlink:href="#B"><stop offset="0" stop-color="#1d210e"></stop><stop offset="1" stop-color="#514c5c"></stop></linearGradient></defs><path fill="url(#AF)" d="M515 167h1 0l1 1c0-2 0-4 1-6h0v4 45c0 3 0 7 1 10l-1 8v-1-3l-1 1h0v2 2l-1 1-1-1c-1 0-3 0-4-1l-1 1-1-1 1-1h1c1-2 1-3 1-5l1-1v-3c0-2 0-3 2-5v-47z"></path><path d="M515 214c0 5 1 11 0 16-1 0-3 0-4-1l-1 1-1-1 1-1h1c1-2 1-3 1-5l1-1v-3c0-2 0-3 2-5z" class="N"></path><defs><linearGradient id="AG" x1="525.207" y1="192.045" x2="532.412" y2="193.228" xlink:href="#B"><stop offset="0" stop-color="#2d2d2f"></stop><stop offset="1" stop-color="#4d4d49"></stop></linearGradient></defs><path fill="url(#AG)" d="M528 170l1 1h2c0 2 1 3 0 4s0 2 0 4v1l2-2c1 0 1 0 1 1h1 1l1-1c2 2 1 5 1 8l1 11c0 1-1 3 0 4v1h3v6l-1 5h1 0 1l-6 6v1l-2 1-1 1-2 2c-1 1-2 2-3 2-2-6-1-14-1-20v-33-3z"></path><path d="M532 184l1 1c1 9 1 18 1 28v6 3l-2 2c-1-3 0-6 0-9v-31z" class="p"></path><path d="M531 180l2-2c1 0 1 0 1 1h1 1c-1 4-1 7-1 11v17c0 4-1 9 0 14l-1 1v-3-6c0-10 0-19-1-28l-1-1c0-1 0-3-1-4z" class="w"></path><path d="M537 178c2 2 1 5 1 8l1 11c0 1-1 3 0 4v1h3v6l-1 5h1 0 1l-6 6v1l-2 1c-1-5 0-10 0-14v-17c0-4 0-7 1-11l1-1z" class="N"></path><path d="M539 202h3v6l-1 5h1 0 1l-6 6 2-4c-1-3 0-10 0-13z" class="D"></path><path d="M532 161h1l1 1 5 5h0c3 4 6 7 10 10l3 2c0 1 0 1 1 3v2c2 1 3 2 4 4 1 1 2 1 3 3l1 1s1 1 0 2c0 1-1 2-2 3-1 2-4 3-5 5l-11 11h-1 0-1l1-5v-6h-3v-1c-1-1 0-3 0-4l-1-11c0-3 1-6-1-8l-1 1h-1-1c0-1 0-1-1-1l-2 2v-1c0-2-1-3 0-4 2-1 3-3 3-4l-2-3v-2c-1-1-1-2-2-3 1-2 1-1 2-2z" class="n"></path><path d="M533 161l1 1 5 5-2 2c0-1 0-1-1-1-2-2-3-3-3-7z" class="g"></path><path d="M539 167h0c3 4 6 7 10 10l3 2c0 1 0 1 1 3v2l-9-9c-3-2-5-4-7-6l2-2z" class="N"></path><defs><linearGradient id="AH" x1="544.837" y1="181.465" x2="548.387" y2="190.345" xlink:href="#B"><stop offset="0" stop-color="#1c1b19"></stop><stop offset="1" stop-color="#363535"></stop></linearGradient></defs><path fill="url(#AH)" d="M540 179l2 1h1l10 10c-1 0-2 1-2 1l-3-1-1-1-1-1h-1l-1-1c-1 0-1 0-2-1l-1-1 1-1 1-1c0-2-2-3-3-4z"></path><path d="M534 171l2 1c2 3 5 5 7 8h-1l-2-1v-1h-2l1 1v3l-1 4c0-3 1-6-1-8l-1 1h-1-1c0-1 0-1-1-1l-2 2v-1c0-2-1-3 0-4 2-1 3-3 3-4z" class="G"></path><path d="M534 171l2 1c-2 2-2 2-1 5h2v1l-1 1h-1-1c0-1 0-1-1-1l-2 2v-1c0-2-1-3 0-4 2-1 3-3 3-4z" class="D"></path><defs><linearGradient id="AI" x1="542.481" y1="190.317" x2="537.79" y2="195.433" xlink:href="#B"><stop offset="0" stop-color="#1b1d18"></stop><stop offset="1" stop-color="#302e30"></stop></linearGradient></defs><path fill="url(#AI)" d="M539 182v-3l-1-1h2v1c1 1 3 2 3 4l-1 1-1 1 1 1-1 5c1 3 1 5 1 9v1 1h-3v-1c-1-1 0-3 0-4l-1-11 1-4z"></path><path d="M539 201s0-1 1-2l2 1v1 1h-3v-1z" class="B"></path><path d="M539 182v-3l-1-1h2v1c1 1 3 2 3 4l-1 1-1 1v2h-1v-4l-1-1z" class="H"></path><path d="M542 186c1 1 1 1 2 1l1 1h1l1 1 1 1 3 1s1-1 2-1c2 2 4 5 6 7-1 2-4 3-5 5l-11 11h-1 0-1l1-5v-6-1-1c0-4 0-6-1-9l1-5z" class="R"></path><path d="M542 201v-1-3h1c1-1 2-2 2-3l1 1c1 1 1 2 2 2l1 1v2c-1 0-1 0-2-1v1 2 1l-1 2h0l-2 1-2 2h0v-6-1z" class="U"></path><path d="M546 205c-1-1-2-1-3-2l2-3 2 2v1l-1 2z" class="j"></path><path d="M542 186c1 1 1 1 2 1l1 1h1l1 1 1 1 3 1s1-1 2-1c2 2 4 5 6 7-1 2-4 3-5 5l-11 11h-1 0-1l1-5h0l2-2 2-1h0l1-2 2 3 4-4c0-1 0-3 1-4-1-1 0-2-1-3l-2 1c-1 0-1-1-2-1v-3h-1c-1 0-1-1-1-1-2-1-4-1-6 0l1-5z" class="Y"></path><path d="M547 203l2 3-7 7h0-1l1-5h0l2-2 2-1h0l1-2z" class="g"></path><path d="M670 144c11 0 21 0 32 3l5 2c3 1 8 2 12 4h1l4 2h1l7 3 3 2 2 2 2 1 2 1 2 1 6 4 5 4 1 1c1 1 2 2 3 2l3 3 1 1 10 9c2 3 5 6 7 10 1 1 1 1 1 3v-1 3h1l6 9v1c2 2 3 3 4 6l2 5c1 1 2 3 3 4v2c3 4 3 11 4 16 1 3 5 9 4 11v2c0 1 1 3 2 4 0 5 1 9 2 13h1v-2l4 31c0 3 1 6 0 9h-1v6c0 5 0 10 1 16v6c1-1 1-1 2-1v6h0v10l1 1c1 6 0 13 0 20 0 2 0 7-1 9h0c-1 1-1 1-1 3v1l-1 1c0-2 0-2-1-4s-1-5-2-7l-2-12c-4-14-8-29-14-43-5-10-11-19-17-28-19-25-45-44-74-56-22-9-48-13-71-5-3 1-6 2-10 4-1 0 0 0-1 1h1c2-1 4-1 6-1 3 0 6-1 9 1l1 1c1 2 2 3 2 5v1c1 3 0 9-2 12 0 1-1 2-2 2l-1 1c2 1 3 2 4 4 2 5 2 10 0 15 0 1-1 2-2 3-2 1-2 1-4 0-1-1-1-2-1-3l-1-1c-2 3 0 5-4 6h-1-1c-1-1-3 0-4 0-1-2 0-4-1-5 0-2-1-3-2-3-1-1-3-1-5-2-1 0-4-1-5-2h-2c-1 0-2-1-3-1l-18-6h-3l-8 1c0 7-1 16 0 24v46h-11l-1-1c2-3 1-17 1-21v-17-6c-1 0-1-1-2-2v-2h-1l-1 3-2-1-1 1c0-1-1-3-1-4l-1 1h-2-1v1c-1-1-2-1-4-1l-3 4-1 1-2 2-1-1-1-5v-1c-1-2 0-5-1-6h-1c-1 1-1 2-1 3 0-1 0-1-1-2l-1 1c-1 0-1 0-2-1l-1-1c-2 1-2 3-3 4-1 0-2 0-3 1 0 1-1 2-3 3l-3 1c-1 0-2-1-3-2l-1-3 3 2 4-5c2-2 5-5 7-8l3-3 4-5 8-9c2-3 4-5 6-7v1h2c1 1 2 1 3 0-1 0-2 0-3-1l3-3c2-1 4 0 7 0v-1-7l1-2v-16-4l-1-2v-4h3c1 0 1-1 1-1 0-1 2-2 2-2v-1c1-2 1-3 1-5s1-4 1-6c0-1 1-2 1-2 1-2 1-1 1-2l1-2 7-14c1-1 4-6 4-7 1-2 3-2 3-4l1-1 6-6 2-1c1 1 0 1 1 2-1 1 0 2-1 3v2c4-4 7-8 12-10l9-6c5-2 11-4 16-5h2 2c2-1 6-3 9-1l24-3z" class="u"></path><path d="M651 151c2 0 2 1 3 2h0l-3 2v-4z" class="B"></path><path d="M691 155c1 1 1 1 2 3l-1 1c-1 0-1 1-2 2h-1c1-2 1-4 2-6zm-40-4v-1c0-1 0-1 1-2h3v5h-1 0c-1-1-1-2-3-2z" class="a"></path><path d="M646 167h0c0-3 0-7-1-9v-1h2 0v4h0c0 1 0 5-1 6z" class="H"></path><path d="M631 246l2 1c0 1 1 4 0 5l-1 1-2-3c0-2 0-2 1-4z" class="V"></path><path d="M645 172l1-1 1 2-1 2h-2l-1-1c0 2 0 3 1 4v1l-1 1c-2-1-1-1-2-3v2c-1-1-1-2-1-3h0l1-1 1 1c0-1 0-1 1-2s1-1 2 0l1-1-1-1z" class="H"></path><path d="M691 155v-1c1-1 2-1 3-1 2 0 4 0 6 1v1c-1 1-1 2-2 2h-1l-2-1c0 1-1 1-2 2-1-2-1-2-2-3z" class="M"></path><path d="M646 167c1-1 1-5 1-6h0c2 3 2 7 3 10l-3 2h0l-1-2-1 1h-2c0-1-1-2 0-4 1 1 2 1 2 2h1c1-1 0-2 0-3zm-55 30c-2-2-2-5-3-7-1-1-1-1-1-2h1c0 1 1 1 2 2h0c0-2-1-3-2-5 1-1 2-2 3-2l1 1c-1 2-1 3 0 6v2l1 1-2 4z" class="G"></path><path d="M655 148c1 1 2 1 2 2 1 1 1 3 0 5h0c1 1 1 2 2 2 1 1 2 1 4 0l1 1v-1l1-1c1 1 0 1 0 3l-1-1 1 3c-2 1-4 0-6 0 0-1-1-2-1-3 0-2-1-2-1-3l-1 1-1-1v-2-5z" class="x"></path><path d="M592 184h1l-1 2h0c1 2 1 3 1 4s1 2 1 2h3l2 1h-3c-1 1-1 3-1 4l1 2c0 1 0 2-1 3-1 0-1 0-1-1-1-2-2-2-3-3v-1l2-4-1-1v-2c-1-3-1-4 0-6z" class="H"></path><path d="M650 171c0 2 0 3-1 4v1c1 2 0 4 1 7 1 0 1 0 1 1 0 2 0 3 1 4 0 3-1 6 0 9v1c0 1 0 3-1 3v-3l-1-1 1-1v-1-4c0-1 0-4-1-5l-2 1v-1l1-1-2-2h-1v-2c-1-2-1-3-1-6h-1 2l1-2h0l3-2z" class="G"></path><path d="M650 171c0 2 0 3-1 4v1c1 2 0 4 1 7 0 1 0 1-1 1l-1-1c-1-2 0-2-1-4l1-2-2-2 1-2h0l3-2z" class="C"></path><defs><linearGradient id="AJ" x1="734.871" y1="228.13" x2="737.129" y2="219.37" xlink:href="#B"><stop offset="0" stop-color="#a3a39b"></stop><stop offset="1" stop-color="#bab8bc"></stop></linearGradient></defs><path fill="url(#AJ)" d="M728 217c8 4 14 9 21 14l1 1h-1c0 1 0 1-1 2-8-7-16-12-24-17h4z"></path><path d="M628 242c3 0 6-1 9 1l1 1v3c1 3 1 6 1 8l-1 1v-2l-1-2-1 1h0l-1 1v-1h-1v1l-2 2h0l-2-1 2-2h0l1-1c1-1 0-4 0-5l-2-1v-1c-1 0-2-2-3-3z" class="C"></path><path d="M682 200c4-1 12 3 16 4h1l15 6 1 1h1l1-1 1 1 5 3 5 3h-4c-14-7-27-12-42-16v-1z" class="X"></path><path d="M681 157l1 1v-1h1l1 1c2 0 3-1 4-2l1 1h-1v7c0-2 0-2 1-3 1 1 1 2 1 2l-1 1c-1 1-1 3-1 4-2 5-2 11-3 16-1 2-1 4-1 5 0 2 0 2-1 3h-1l-1 1c0-2 0-4 1-5 0-2 1-5 1-7h0c1-2 1-4 2-7 1-2 1-8 2-11v-2h-1-3c-1-1-2 0-3 0 0-1 1-2 1-4z" class="G"></path><path d="M643 180l1-1v-1c-1-1-1-2-1-4l1 1h1c0 3 0 4 1 6v2h1l2 2-1 1v1c-1 1-2 1-3 2l-1 1 1 1v1l-1-1h-1c1 1 1 1 0 1h0c-2-2-3-2-5-3v-2c-1-2 0-3-1-5v-1h1c-1-1-1-2-1-2l1-1h0c0 1 1 1 1 2l1 1h0c2 0 2-1 4 0l-1-1z" class="S"></path><path d="M593 184c1 2 2 3 2 5v1-1-2l2-2c0-1-1-4-2-5l-2-1v-1h1c2 0 2 2 4 3v-3h-1l-1-1 1-1c2 2 3 3 4 5 2 3 3 5 5 7l-1 1h-1l-1-1v1c0 1 1 2 1 2 0 3 2 6 1 8-1-3-2-6-4-8h-1l-1 1 1 1v3 1c-1-1-1-2-1-3v-1l-2-1h-3s-1-1-1-2 0-2-1-4h0l1-2z" class="O"></path><path d="M752 233l1 2 6 6 10 10c4 6 7 13 12 19l3 4-2 2c-1-1-2-3-3-4l-4-8c-5-7-11-14-17-20-2-3-6-6-9-9l2-2h1z" class="E"></path><path d="M648 187l2-1c1 1 1 4 1 5v4 1l-1 1 1 1v3l-4 1-2 1-2 1c0-2 0-4-1-4-1-1-1-1-1-2h0c0-2-1-3-2-5l-1-4c2 1 3 1 5 3h0c1 0 1 0 0-1h1l1 1v-1l-1-1 1-1c1-1 2-1 3-2z" class="e"></path><path d="M645 191l-1-1 1-1h2c-1 2-1 2-2 2z" class="B"></path><path d="M641 198c2 0 2-1 3 0s1 2 1 3c-1 1 0 1 0 2l-2 1c0-2 0-4-1-4-1-1-1-1-1-2z" class="D"></path><path d="M647 202c-1-1-1-2-2-3 1-1 1-1 3-1v-1-2h0l-1-1-1-1h2c1 0 1 1 1 2h2v1l-1 1 1 1v3l-4 1z" class="F"></path><path d="M633 148h2 2c2-1 6-3 9-1-10 2-19 4-28 8-6 3-11 7-16 11-7 6-15 13-19 22l-4 7v-2c0-4 2-6 5-10 3-5 7-10 12-14 4-4 7-8 12-10l9-6c5-2 11-4 16-5z" class="q"></path><path d="M804 333l1-1c2 2 2 6 3 8l1 5v1c0 1 2 9 2 9v-1l1 2v-1h1c0-2-1-4 0-6l1-1-1-5c1-1 1-1 2-1v6h0v10l1 1c1 6 0 13 0 20 0 2 0 7-1 9h0c0-8-2-17-4-25l-7-30z" class="d"></path><path d="M813 343c1-1 1-1 2-1v6h0v10l1 1c-1 4 0 9-1 13h0l-2-1c1-5-1-12-2-16v-1l1 2v-1h1c0-2-1-4 0-6l1-1-1-5z" class="E"></path><path d="M813 343c1-1 1-1 2-1v6h0v10-3l-1 3c-1-2 0-7 0-10l-1-5z" class="c"></path><path d="M813 349c0 6 0 12 1 18 1 1 1 3 1 5l-2-1c1-5-1-12-2-16v-1l1 2v-1h1c0-2-1-4 0-6z" class="o"></path><path d="M810 309h1c1 4 0 8 1 12 0 5 0 10 1 16v6l1 5-1 1c-1 2 0 4 0 6h-1v1l-1-2v1s-2-8-2-9v-1l-1-5c-1-2-1-6-3-8l-1 1-5-15v-1c4-3 7-6 11-8z" class="h"></path><path d="M810 309l1 1-3 3c-1 2-5 5-7 5h-2v-1c4-3 7-6 11-8z" class="Q"></path><path d="M615 197c-1-1-2-4-3-5-4-6-8-13-12-18-1-1-1 0-1-1l1-1c1 1 2 2 2 4l4 6 2 1c1 2 2 4 4 5h0l1-1c0-1 0-1-1-2l2-1c2 2 1 4 3 5 1 2 1 4 3 5 0 0 1 0 2 1v-1-1l1-1h-1c1-2 2-3 2-4h1 1l3-1 1 1-1 1v1c1 0 1 0 2-1h1c1 0 3 1 4 2l1 1 2 1c1 2 2 3 2 5h0c0 1 0 1 1 2 1 0 1 2 1 4-1 0-2 1-4 1-5 2-10 5-14 9l-10-17z" class="C"></path><path d="M615 190h0l1 3h-1-1v-2l1-1z" class="G"></path><path d="M625 188h1c1 1 1 2 2 3l-2 2v-2c0-1-1-2-1-3z" class="S"></path><path d="M626 200c-2 0-4 0-5 1h0l-1-1 1-2c-2 0-2 0-3-1l1-1c2 0 2 0 3 1h2c1 1 1 2 2 3z" class="K"></path><path d="M624 197l2-1 2 2 3-3h1v1c1 1 1 1 2 1 1-1 1-1 2-1h1c1 1 0 1 1 1s1 1 2 1h1 0c0 1 0 1 1 2 1 0 1 2 1 4-1 0-2 1-4 1-5 2-10 5-14 9l-10-17h2c1 2 1 3 2 5 2 2 5 6 6 10h1l1-1c-1-2 0-2-1-3 0-1-1-2-1-2l-2-1h1v-1-1l-1-1v-1c1 0 1 0 3 1v-1-1c-1-1-1-2-2-3z" class="F"></path><path d="M636 198h1v1l-1 2c-1-1-1-1-2-1l2-2z" class="K"></path><path d="M630 198h0c1 1 0 1 0 3 1 0 1 1 1 2l-1 1h-1v-2l-2-2v-1l3-1z" class="S"></path><path d="M583 188v1c0 2-2 4-2 6 1 1 2 2 2 3 0 2 0 3 1 4l-1 1c-1-1-2-2-2-4v-1c-1 0-1 0-2 1 1 1 1 2 2 3h-2v1l1 1v1 1c-1 0-1 0-2-1-1 1-1 1-1 2l2 3-1 1-1-2-1 1v4c1 1 2 1 3 2l1 1 38 24v1h-1l1 3v6h0c1 2 0 7 1 8l1 1c-1 1-1 0-1 1-2-1-3-2-4-2h-1l-2-1c-1 0-1 0-2-1h-2-1c-1-1-3-1-4-2l-6-2-10-3h-1c-3-2-4-1-7-1l2-2c1 0 3 0 4 1 2 1 4 1 6 2 0-1 1-2 1-2 0-1-1-2-1-2h1s0 1 1 2 1 1 3 1l1-1-1-1 1-1v1h1c1 1 1 2 1 2h1c0-2 1-4 0-6h-1c1-2 0-4 2-5v-1l1-1h1l2 1h0 1l1 1c0 1 1 1 2 2l1-1v1c1 0 1 1 2 1h2 1v-1c-1 0-1 0-2-1s-1 0-2-1h0l-1-1c-1 0-2-2-3-2-2-1-4-3-6-4l-17-11c-3-1-5-2-7-4h-3c1-7 2-14 5-21l4-7z" class="x"></path><path d="M614 250v-2-1c1-1 2-2 4-2v6h0v1l-1 1-1-2h-2v-1z" class="G"></path><path d="M614 250l3-2 1 1c-1 1-1 1 0 2h0v1l-1 1-1-2h-2v-1z" class="B"></path><path d="M603 236l2 1h0 1l1 1c0 1 1 1 2 2l1-1v1c1 0 1 1 2 1h2v3h1l-1 1c-2 0-2 0-2-1l-1-1c0-1-1-1-1-2l-1 1c0 1 0 2 1 3h-1 0-1-1c0-1 0-1-1-1 0-1 0-1 1-2l-1-1v-1-1l-1-1-2 2v-2l-1-1 1-1z" class="G"></path><path d="M599 249h1c0-2 1-4 0-6h-1c1-2 0-4 2-5v-1l1-1h1l-1 1 1 1v2l2-2 1 1v1 1l1 1c-1 1-1 1-1 2v1c-1 0-1 0-2-1 0-1-1-2-1-2-1 0-1 1-2 2v6c-1 0-1 0-2-1z" class="M"></path><path d="M581 247c1 0 3 0 4 1 2 1 4 1 6 2s4 1 5 2l4 1h2l1 1h3l1-1h2c1-2-1-2 1-4 1 1 1 2 1 3v2l3 1c1-1 0-1 0-1 1-1 1-2 2-3h0l1 2 1-1v-1c1 2 0 7 1 8l1 1c-1 1-1 0-1 1-2-1-3-2-4-2h-1l-2-1c-1 0-1 0-2-1h-2-1c-1-1-3-1-4-2l-6-2-10-3h-1c-3-2-4-1-7-1l2-2z" class="a"></path><path d="M574 216h3c2 2 4 3 7 4l17 11c2 1 4 3 6 4 1 0 2 2 3 2l1 1h0c1 1 1 0 2 1s1 1 2 1v1h-1-2c-1 0-1-1-2-1v-1l-1 1c-1-1-2-1-2-2l-1-1h-1 0l-2-1h-1l-1 1v1c-2 1-1 3-2 5h1c1 2 0 4 0 6h-1s0-1-1-2h-1v-1l-1 1 1 1-1 1c-2 0-2 0-3-1s-1-2-1-2h-1s1 1 1 2c0 0-1 1-1 2-2-1-4-1-6-2-1-1-3-1-4-1l-7 1v-32z" class="u"></path><path d="M602 166c5-4 10-8 16-11-1 2-2 3-1 6v3c1 1 1 2 1 3l-1 1 1 1c0 1 1 2 1 3v1h1 3-1l1-2h1v2c2 0 2 0 3-1 0-1 0-1-1-2l1-2h-3l-1 1v1c-2-1-2-1-2-3v-2c0-1 0 0-1-1l1-1h1c1 1 1 3 1 4 1 0 1 0 2-1 1-2-2-6-1-7 1 1 2 2 2 3 0 2 1 3 1 5 1 1 1 4 3 5 0 0 2 1 3 1v-4s0-1-1-1h-1l2-2-1-2c0-1 0-2-1-2 1-2 1-2 0-3h1 0v2c1 1 1 1 1 2v2h1v1c1 1 1 3 1 4v2h1l1-3c0 1 1 1 1 2 1 1 1 3 1 4v1 1 2 1c0-1-1-1-1-2h0l-1 1s0 1 1 2h-1v1c1 2 0 3 1 5v2l1 4-2-1-1-1c-1-1-3-2-4-2h-1c-1 1-1 1-2 1v-1l1-1-1-1-3 1h-1-1c0 1-1 2-2 4h1l-1 1v1 1c-1-1-2-1-2-1-2-1-2-3-3-5-2-1-1-3-3-5l-2 1c1 1 1 1 1 2l-1 1h0c-2-1-3-3-4-5l-2-1-4-6c1-1 2-1 3-2 0-1 0-1 1-1h1v-1c-2 0-2 0-4-1 1-1 3-1 4-1 0-2-2-2-3-3l-2-1z" class="O"></path><path d="M626 176l2-2 1-1 1 2h0c-1 1-2 1-3 1h-1zm-4 17c-1-1-1-2-1-3 1-2 0-3 1-5h0l-1-2h1l3 3-1 1v1c0 1-1 2-2 4h1l-1 1z" class="S"></path><path d="M626 176h1l2 2c0 1 0 1-1 3l1 1v1c-1 0-1 0-2-1l-1 1h-1v-2l-1-1h-1l2-2c0-1 1-1 1-2z" class="M"></path><path d="M604 167l1-1c2 0 2 0 3 1v3h0l3 3v1l-3 2c0-1-1-1-1-2h-2c0-1 0-1 1-1h1v-1c-2 0-2 0-4-1 1-1 3-1 4-1 0-2-2-2-3-3z" class="C"></path><path d="M620 173v2 1c-2 1-6 4-7 6h0v1h-1 0l-1-1c2-3 4-5 6-7l-1-1 1-1v-4l-1-1-2 2h-1v-1c0-1-1-2-1-2h3c1 0 1-2 2-3 1 1 1 2 1 3l-1 1 1 1c0 1 1 2 1 3v1h1z" class="e"></path><path d="M785 275c1 0 3-2 4-3 5-4 10-9 15-12 0 1 1 3 2 4 0 5 1 9 2 13h1v-2l4 31c0 3 1 6 0 9h-1v6c-1-4 0-8-1-12h-1c-4 2-7 5-11 8l-16-40 2-2z" class="b"></path><path d="M785 275c1 0 3-2 4-3 5-4 10-9 15-12 0 1 1 3 2 4l-1 1c-5 2-8 7-12 10-1 0-1 0-1 1-1 1-2 2-4 2v2h-1v-1c0-1-1-2-2-4z" class="P"></path><defs><linearGradient id="AK" x1="657.68" y1="155.067" x2="678.192" y2="192.006" xlink:href="#B"><stop offset="0" stop-color="#161513"></stop><stop offset="1" stop-color="#3b3b39"></stop></linearGradient></defs><path fill="url(#AK)" d="M654 153h1v2l1 1 1-1c0 1 1 1 1 3 0 1 1 2 1 3 2 0 4 1 6 0l-1-3 1 1h1l2-2 1 1 1-1c1-1 2-2 3-2l1 2h3 1 2 1c0 2-1 3-1 4 1 0 2-1 3 0h3 1v2c-1 3-1 9-2 11-1 3-1 5-2 7h0c0 2-1 5-1 7-1 1-1 3-1 5v2c-1 2-1 3-2 5l-1 1c-2-1-5-1-8-1-5 0-10 0-16 1h0c-2-2-1-8-1-11s0-8-1-10v-1-3-1-5l-1-15 3-2z"></path><path d="M684 174h1c-1 3-1 5-2 7 0-1-1-2-2-2v-1l3-3v-1z" class="F"></path><path d="M668 174c3 0 4-1 6-1v1h-2c0 2-1 2-2 3l-2 1v-3-1z" class="B"></path><path d="M668 178h-1c-1-1-2-1-3-2h-1 1l1-1c0-1 0-1 1-1h2 0v1 3z" class="e"></path><path d="M665 159h1l2-2 1 1 1-1c1-1 2-2 3-2l1 2h3v1c-2 0-3 0-4-1l-2 2c-1 1 0 1-1 2h-1l-1-2h0l-1 2h-1l-1-2z" class="M"></path><path d="M653 170c0-1-1-3 0-5h1c0 1 0 2 1 3h0l-1 2 1 1 1-1c1 1 1 1 1 2v1l-2-1-1 1c0 1 0 1 1 1l-2 3v-7z" class="D"></path><path d="M653 190h0c1-2 1-3 1-5 1 0 2 0 3 1v1c0 1 0 1 1 2 0 1 0 0-1 1-1 0-1 1-2 2v1h-1v1c-1 1 0 3 0 4v3h0c-2-2-1-8-1-11z" class="B"></path><path d="M659 161c2 0 4 1 6 0l-1-3 1 1 1 2v1h-1c0 2 0 3-1 5l-2-1-1 1h-1c-1-2-1-4-1-6z" class="G"></path><path d="M654 198c0-1-1-3 0-4v-1h1v-1c1-1 1-2 2-2 0 1 0 2 1 3h1v-2l1 2h1v1h1 1l-3 3c-2 1-3 0-4 1h-2 0z" class="D"></path><path d="M654 153h1v2 1c1 2-1 4 1 6-1 1-2 0-2 2 1 0 1 0 2-1l1 1c-1 2-1 3-2 4h0c-1-1-1-2-1-3h-1c-1 2 0 4 0 5h-1l-1-15 3-2z" class="F"></path><defs><linearGradient id="AL" x1="661.091" y1="194.345" x2="669.356" y2="203.596" xlink:href="#B"><stop offset="0" stop-color="#565654"></stop><stop offset="1" stop-color="#6d6c6f"></stop></linearGradient></defs><path fill="url(#AL)" d="M660 197c4 1 8 0 12 0 2 1 4 2 7 2v1l-1 1c-2-1-5-1-8-1-5 0-10 0-16 1v-3h0 2c1-1 2 0 4-1z"></path><path d="M675 185l2 2 2-1v-1l1 2v1-1l1-1h0v-1c1-2 1 0 1-2h0l1-2c0 2-1 5-1 7-1 1-1 3-1 5v2c-1 2-1 3-2 5v-1l-2-3h-3-2v-3c-1 0-1 0-2-1h-2l-1-1 1-1h2c2-2 1-3 1-5 1 0 2 0 3-1l1 1z" class="D"></path><path d="M672 193c1 0 2 1 3 2l-1 1h-2v-3zm3-8l2 2 2-1v-1l1 2v1-1l1-1h0v-1c1-2 1 0 1-2h0l1-2c0 2-1 5-1 7-1 1-1 3-1 5v2h-3-1v-2l1-1c-2-1-3-1-4-1h-1 1c1-2 0-3 0-4l1-2z" class="F"></path><path d="M680 161c1 0 2-1 3 0h3 1v2c-1 3-1 9-2 11h-1l-1-1c1-1 0-1 1-2l-2-1h0c-2 0-2 0-3 1v1c-1 0-1 0-2 1-1 0-1-1-2-1s-1 1-2 1v-1h0-2l1-2-1-1 1-1 1 1c2-1 1-1 2-2 0-1 0-1-1-1l-1-1c1 0 3-1 4-1 1-1 1-2 2-3h1z" class="e"></path><path d="M574 260v-10l5-1c3 0 4-1 7 1h1l10 3 6 2c1 1 3 1 4 2h1 2c1 1 1 1 2 1l2 1h1c1 0 2 1 4 2h-2 0l2 2h2c1-1 2 0 4 0l1-1v-1h-1l-2-2c1-1 1-1 2-1s1 1 3 1c1 1 1 0 2 1l1-1v-1l2 2-1 3c1 1 2 1 4 1l-1 1c2 1 3 2 4 4 2 5 2 10 0 15 0 1-1 2-2 3-2 1-2 1-4 0-1-1-1-2-1-3l-1-1c-2 3 0 5-4 6h-1-1c-1-1-3 0-4 0-1-2 0-4-1-5 0-2-1-3-2-3-1-1-3-1-5-2-1 0-4-1-5-2h-2c-1 0-2-1-3-1l-18-6h-3v-2l-1 1c-1-1-4-2-5-3 0-1 0-1-1-2 0-1-1-3-1-4z" class="H"></path><path d="M613 264c1 0 1 0 2 1l1 2h-2l-1 1v-1-3zm-3-5l1 1v2c-1 0-1 1-2 1 0 1 0 1 1 2-1 1-1 1-2 1l-1-1c1-2 1-3 0-4l2-1 1-1z" class="C"></path><path d="M625 283l-1 1h-1v-2c-1 0-1 0-2-1v-9l4 4 1 1v2 3c-1 0-1 0-1 1h0z" class="a"></path><path d="M614 276l1-1c-1-2-1-3 0-5 0-1 1-1 1-1h1v-3h1v3l1 1c0 2-1 4-1 6 1 2 1 3 0 5-1-1-3-1-5-2 0-1 0-1-1-2h0v-1h2z" class="S"></path><path d="M630 260l1-1v-1l2 2-1 3c1 1 2 1 4 1l-1 1c2 1 3 2 4 4 2 5 2 10 0 15 0 1-1 2-2 3-2 1-2 1-4 0-1-1-1-2-1-3l-1-1c-2 3 0 5-4 6h-1l1-2h1v-1c-1-2-2-2-3-3h0c0-1 0-1 1-1v-3-2l4 1c1 1 2 1 2 2 1 0 1 0 2 1v-1l-2-2h0l-2-2c-1 0-2-1-3-1l-2-2v-1c-1-1-1-2-2-3 0-1-1-1-2-2v-2-1h3c2 0 3 0 5-1l1-3z" class="F"></path><path d="M631 271c1 0 2 0 2 1 1 1 1 3 1 4l-2 2h0l-2-2c-1 0-2-1-3-1l1-4h3z" class="D"></path><path d="M621 265l2 1 2 2 1 1c1-1 1-1 1-2 0 1 1 1 2 1h4c-1 2-1 2-2 3h-3l-1 4-2-2v-1c-1-1-1-2-2-3 0-1-1-1-2-2v-2z" class="S"></path><path d="M630 260l1-1v-1l2 2-1 3c1 1 2 1 4 1l-1 1c-2 0-4 0-5-1-3 1-5 2-7 2l-2-1v-1h3c2 0 3 0 5-1l1-3z" class="U"></path><path d="M633 268l-1-1c1-1 2 0 3 0 2 1 3 3 3 5v2h1c0 4-1 8-2 12h-2-1c0-2 0-1 1-2 0-1 1-2 1-2v-2l-1 1v-1c1-1 1-2 2-3 0-3 0-5-1-7-1-1-2-2-3-2z" class="w"></path><path d="M574 260v-10l5-1c3 0 4-1 7 1h1l10 3v2c2 1 5 2 7 3 2 0 4 0 6 1l-1 1-2 1c1 1 1 2 0 4l1 1c0 1 1 3 1 4v1l1 1v2c1 1 2 1 4 2h-2v1h0c1 1 1 1 1 2-1 0-4-1-5-2h-2c-1 0-2-1-3-1l-18-6h-3v-2l-1 1c-1-1-4-2-5-3 0-1 0-1-1-2 0-1-1-3-1-4z" class="B"></path><path d="M607 265h0-1l-2-2 1-1h0v-1h2c1 1 1 2 0 4z" class="F"></path><path d="M586 263h1v1c1 0 1 0 2 1h0 1c0 2 2 3 2 5h1l1 1h-3v-1c-2 0-3-1-4-1h-2 0v1h-3v-2h2v-3l2-2z" class="S"></path><path d="M584 265l2-2 1 3h-1v1l2 1-1 1h-2 0v1h-3v-2h2v-3z" class="G"></path><path d="M593 270v-2-1l1-1 1 1v1c1-1 1-2 2-2 1-3 1-3 3-5l1 1 1 2c-1 2-2 1-3 2 1 2 3 2 2 5v1l1 1h0l2-2c1 1 0 2 1 3h1v-2h1 1l1-2v1l1 1v2c1 1 2 1 4 2h-2v1h0c1 1 1 1 1 2-1 0-4-1-5-2h-2c-1 0-2-1-3-1l-18-6v-1h0 2c1 0 2 1 4 1v1h3l-1-1z" class="C"></path><path d="M601 271h0c-1 0-2 0-3-1v-3l1-1c1 2 3 2 2 5z" class="G"></path><path d="M574 260v-10l5-1c3 0 4-1 7 1h1l10 3v2l-7-2c-2-1-4-1-6 0h0l1 2-1 1c-2-1-2-2-3-3h-1l1 1c1 1 2 3 2 5-1 2-1 2 0 3l1 3v3h-2l-1 1c-1-1-4-2-5-3 0-1 0-1-1-2 0-1-1-3-1-4z" class="u"></path><defs><linearGradient id="AM" x1="765.471" y1="220.736" x2="791.411" y2="249.044" xlink:href="#B"><stop offset="0" stop-color="#6b6a69"></stop><stop offset="1" stop-color="#91908f"></stop></linearGradient></defs><path fill="url(#AM)" d="M780 204h1l6 9v1c2 2 3 3 4 6l2 5c1 1 2 3 3 4v2c3 4 3 11 4 16 1 3 5 9 4 11-1 0-3 2-5 3l-15 13-3-4c-5-6-8-13-12-19l-10-10-6-6-1-2h-1c-1 0-1 0-2-1h1l16-15 5-5 7-7 2-1z"></path><path d="M792 234h-1-1l-1-1c0-1 1-1 1-2l1-1h-1 0l3-5c1 1 2 3 3 4v2c-1 0-2-1-3-1s-1 1-2 2h0 1l1 1-1 1z" class="P"></path><path d="M780 204h1c0 3-3 5-5 6l-18 17c-2 2-5 4-6 6h-1c-1 0-1 0-2-1h1l16-15 5-5 7-7 2-1z" class="D"></path><path d="M769 229c3-5 8-8 11-12l-1-2h1l1 1 1-1h0v-1h1c1-1 2-1 4-1v1c2 2 3 3 4 6h-1c0 1-1 2-1 3v1c-2 2-5 5-8 6-1 1-2 1-3 1v1l-1-1 2-2c-1 0-3 1-4 2 0 0-1 0-1 1-2 0-2 0-3-1s-1-1-2-1v-1z" class="v"></path><path d="M787 214c2 2 3 3 4 6h-1-2l-1 1-1-1h1c0-1 1-3 1-4h-2v-1l1-1z" class="d"></path><path d="M769 229c3-5 8-8 11-12l-1-2h1l1 1 1-1h0v-1h1c1-1 2-1 4-1v1l-1 1h-1v2c-2 3-6 8-10 10l-3 3h0l-1-2c-1 0-1 0-2 1h0z" class="w"></path><path d="M792 234l1-1-1-1h-1 0c1-1 1-2 2-2s2 1 3 1c3 4 3 11 4 16 1 3 5 9 4 11-1 0-3 2-5 3l-15 13-3-4v-1c0-2-2-4-3-5s0-1 0-2l2-2c2-1 3-3 5-4s2-2 4-3v-1l-1-1-1 1-1 1c-1 1-1 1-2 1-1 1-2 2-3 2-1 1-1 1-2 1-1 1-3 2-5 2 0-1 1-1 2-2s2-3 4-3c1 0 1-1 2-1 2-1 4-3 5-4h1l1-3-1-1v-1l-2-3 5-6 1-1z" class="p"></path><path d="M788 249l1-3-1-1v-1l-2-3 5-6 1 2h1l1-1v3l-3 3v1l1 1c-1 2-2 3-4 5z" class="P"></path><defs><linearGradient id="AN" x1="722.649" y1="207.401" x2="766.319" y2="210.495" xlink:href="#B"><stop offset="0" stop-color="#30302e"></stop><stop offset="1" stop-color="#7d7d7c"></stop></linearGradient></defs><path fill="url(#AN)" d="M749 169l5 4 1 1c1 1 2 2 3 2l3 3 1 1 10 9c2 3 5 6 7 10 1 1 1 1 1 3v-1 3l-2 1-7 7-5 5-16 15-1-1c-7-5-13-10-21-14l-5-3-5-3-1-1c2-7 5-14 8-19l1 1c1-1 1-1 1-2l1-1 1 1h1l1-1 3-3c0-1 1-2 1-2h1v2l2-2 2-2c0-3 4-8 5-10 1-1 1-1 3-1l1-2z"></path><path d="M751 205h1l2-2h0 2c-1 1-2 3-3 4 0 1-1 2-2 3v-1h1l1-2c-1-1 0-1-1-1-1-1-1 0-2-1h1z" class="w"></path><path d="M751 205l-1-2c0-1 0-1 1-2v-1c0-1 0-2 1-4 0 3 0 4 3 5 1 0 1-1 2-1l-1 3h0-2 0l-2 2h-1z" class="o"></path><path d="M754 192l2 4c0 1 1 1 1 2l2-3h1v1c-1 2-2 3-1 5v2 3l-2 3v2l-1-1c0-1 0-2 1-3h0c-1-2-1-3-1-4l1-3c-1 0-1 1-2 1-3-1-3-2-3-5l1-1v-1c1 0 1-1 1-2z" class="l"></path><path d="M754 192l2 4c0 1 1 1 1 2s0 1-1 1c-2-1-2-1-3-4v-1c1 0 1-1 1-2z" class="h"></path><path d="M742 192v1c1-1 1-1 1-2l1-1c1-1 1-1 2-1-1 2-2 5-3 6-4 6-6 10-12 14l-1 1-3 3h-1c1-1 3-2 3-3 2-5 8-9 10-13 1-2 1-4 3-5z" class="I"></path><path d="M755 174c1 1 2 2 3 2l3 3 1 1-2 4h-1c-1 2-2 3-3 5v1 1h-1l-1 1c0 1 0 2-1 2l-1-1c0 1-1 1-1 2l-2-1c0-2 2-4 1-6l-1 1-2-1 1-2 1-1c0-2 1-3 2-4l1-1c1 0 1-1 2-2l-1-2 2-2z" class="k"></path><path d="M755 182c1 0 1 1 2 2l-1 1h0-1v-3zm3-6l3 3c-1 0-1 1-2 2h0-2l-1-1c1-1 2-2 2-4z" class="P"></path><path d="M748 186l1-1c0-2 1-3 2-4 0 1 0 2-1 4h0c1 0 2-1 3-1 0 1-1 2-1 3v3 1 1l1-1 2-2v2l-1 1c0 1 0 2-1 2l-1-1c0 1-1 1-1 2l-2-1c0-2 2-4 1-6l-1 1-2-1 1-2z" class="l"></path><path d="M749 169l5 4 1 1-2 2 1 2c-1 1-1 2-2 2l-1 1c-1 1-2 2-2 4l-1 1-1 2-1 1c-1 0-1 0-2 1l-1 1c0 1 0 1-1 2v-1l1-2v-1c0-3 0-1-1-1l-1-1c0-1 0-1-1-2h1l-1-3c0-3 4-8 5-10 1-1 1-1 3-1l1-2z" class="r"></path><path d="M747 172c1 2 1 3 0 4v3l-1 1v-1-1-1c-1 1-1 1-2 1v-1c1-2 2-3 3-5z" class="v"></path><path d="M741 185l4-8-1 5v2l1 1v2c1-1 2-1 2-1h1l-1 2-1 1c-1 0-1 0-2 1l-1 1c0 1 0 1-1 2v-1l1-2v-1c0-3 0-1-1-1l-1-1c0-1 0-1-1-2h1z" class="Y"></path><path d="M749 169l5 4 1 1-2 2 1 2c-1 1-1 2-2 2-1-1-1 0-2 0l-2 3h-1 0c0-1 0-2 1-4h0-1v-3c1-1 1-2 0-4l1-1 1-2z" class="l"></path><path d="M740 182l1 3h-1c1 1 1 1 1 2l1 1c1 0 1-2 1 1v1l-1 2c-2 1-2 3-3 5-2 4-8 8-10 13 0-1 0-2-1-3h0c-2 1-3 1-5 2v3 2l-5-3-1-1c2-7 5-14 8-19l1 1c1-1 1-1 1-2l1-1 1 1h1l1-1 3-3c0-1 1-2 1-2h1v2l2-2 2-2z" class="F"></path><path d="M721 204c1 0 1 0 1-1 2-3 3-5 5-7l-3 8-2 5h-1v-1l1-1c-1-1 0-1-1-1-1-1-1 0-1-1l1-1z" class="K"></path><path d="M740 185c1 1 1 1 1 2l1 1c1 0 1-2 1 1v1l-1-1c-1 1-2 2-3 4 0 1-1 1-1 2-1-1-1-2-2-2l1-3c2-2 2-3 3-5z" class="Q"></path><path d="M740 182l1 3h-1c-1 2-1 3-3 5l-1 3c0 1-1 3-2 3v-3c1-1 1-2 1-3l-2 2h-2c0-1 0-1-1-2l1-1 3-3c0-1 1-2 1-2h1v2l2-2 2-2z" class="J"></path><path d="M740 182l1 3h-1c-1 2-1 3-3 5v-3c-1 0-1 0-2 1v1h-1l1-3h-1c0-1 1-2 1-2h1v2l2-2 2-2z" class="s"></path><path d="M725 191l1 1c1-1 1-1 1-2l1-1 1 1h1c1 1 1 1 1 2h2l-3 3h-1v-1l-1-1-3 3c-1 0-1 1-1 1l-1 1-1 1v2c-1 1-1 2-1 3h0l-1 1c0 1-1 2-1 3s0 1-1 2v1l-1-1c2-7 5-14 8-19z" class="I"></path><path d="M762 180l10 9c2 3 5 6 7 10 1 1 1 1 1 3v-1 3l-2 1-7 7-5 5v-1-2-1l-1-1c0-1-1-2-2-2l-2 2h-1l3-4v-2c-2 1-2 0-4 0v-3-2c-1-2 0-3 1-5v-1h-1l-2 3c0-1-1-1-1-2l-2-4 1-1h1v-1-1c1-2 2-3 3-5h1l2-4z" class="b"></path><path d="M771 197c1-1 3-2 4-3l2 4-1 1-2-1-3-1z" class="R"></path><path d="M768 192h-1l-2 2v-1c0-3 0-5 2-7l1 1c0 1-1 2-2 3 1 1 1 1 2 1h1l-1 1z" class="f"></path><path d="M768 187l7 7c-1 1-3 2-4 3v-1s-1 0-1-1l-1-1-1 2h-1l-1-1 2-3 1-1h-1c-1 0-1 0-2-1 1-1 2-2 2-3z" class="X"></path><path d="M759 184l-1 4 1 1c1-1 2-2 2-4h1 1l-1 1c-1 1-3 5-3 6l1 1c1-2 2-4 4-5v1c-1 1-2 3-2 5l1 2h1c1 2-2 3 0 4 1 1 1 1 2 1 1-1 3-2 4-4h1c-1 2-2 2-3 4v1c1 0 1 0 1 1-1 1-3 4-5 5h-1v-2c-2 1-2 0-4 0v-3-2c-1-2 0-3 1-5v-1h-1l-2 3c0-1-1-1-1-2l-2-4 1-1h1v-1-1c1-2 2-3 3-5z" class="P"></path><path d="M759 201h0 2v-2 3l-1 1c1 1 1 2 3 2l1-1 1 1c-1 1-1 2-1 3h-1v-2c-2 1-2 0-4 0v-3-2z" class="d"></path><defs><linearGradient id="AO" x1="767.257" y1="214.763" x2="773.361" y2="197.213" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#bdbbba"></stop></linearGradient></defs><path fill="url(#AO)" d="M777 198c1 1 1 1 2 1 1 1 1 1 1 3v-1 3l-2 1-7 7-5 5v-1-2-1l-1-1c0-1-1-2-2-2l-2 2h-1l3-4h1c2-1 4-4 5-5l5-5 2 1 1-1z"></path><path d="M776 203l2-2h2v3l-2 1-2-2z" class="N"></path><path d="M771 212h-1l2-4c1-2 1-2 2-3l2-2 2 2-7 7z" class="c"></path><path d="M702 147l5 2c3 1 8 2 12 4h1l4 2h1l7 3 3 2 2 2 2 1 2 1 2 1 6 4-1 2c-2 0-2 0-3 1-1 2-5 7-5 10l-2 2-2 2v-2h-1s-1 1-1 2l-3 3-1 1h-1l-1-1-1 1c0 1 0 1-1 2l-1-1c-3 5-6 12-8 19l-1 1h-1l-1-1-15-6h-1c-4-1-12-5-16-4v1h-4l1-1c1-2 1-3 2-5v-2l1-1h1c1-1 1-1 1-3 0-1 0-3 1-5 1-5 1-11 3-16 0-1 0-3 1-4l1-1s0-1-1-2h1c1-1 1-2 2-2l1-1c1-1 2-1 2-2l2 1h1c1 0 1-1 2-2v-1c0-1 1-1 1-1 1-2 1-4 1-6z" class="Y"></path><path d="M698 170c0-1 1-2 2-3v1c1 0 1 0 2-1l2 2v1h-2c-1 0-2 1-2 1-1 0 0-2-2-1z" class="I"></path><path d="M698 170c2-1 1 1 2 1 0 0 1-1 2-1h2l-1 1v1l-1 2-2-2c-1 1-3 1-5 1v-1l1-1c1-1 1-1 2-1zm11-13h3v1c0 1 1 1 2 1h0 4v2l1 1 2-1v3c0 1 0 2-1 3l1 1v-1h0c0 2 0 2-1 4v1l-2 2h-1c0-2 1-3 1-4-1-2-1-2-3-2l1-2-1-2h-1l-1-1c-1 1-2 1-3 3l-2 2c0-1-1-1-1-2h0-3l1-2c0-1 1-2 2-3l2-2c-1-1 0-1 0-2z" class="J"></path><path d="M709 157h3v1c0 1 1 1 2 1v2l-1 1-1-1h-5l2-2c-1-1 0-1 0-2z" class="D"></path><path d="M698 179c0-1-1-1-1-2l1-1c-1 0-2-1-3 0h-2v-1s1-1 1-2c1-1 0-1 1-2h1l-1 1v1c2 0 4 0 5-1l2 2-1 1 1 1h3l1 1v1h-3v2c1 1 1 2 1 3 2 1 3 1 4 2v2h1c1 0 2 0 2 1l1-1c1-1 2-1 4-1h0v1h-2c-1 1-1 2-2 3h-1l-2 1v-2h-1c0 1-1 2-2 3v1h-1v-1-2h-1l-2 1 1-2-2-1c-1-2-1-1-2-2v-1c0-1 0-1-1-2v1h-1-1v-2-1l2-2z" class="Q"></path><path d="M698 179l1 1h1l-1-1s0-1-1-1l1-1v1l1 1 1-1c1 1 1 2 2 3 0 1 0 2 1 3s1 1 3 1v3l-1 2v2 1h-1v-1-2h-1l-2 1 1-2-2-1c-1-2-1-1-2-2v-1c0-1 0-1-1-2v1h-1-1v-2-1l2-2z" class="F"></path><defs><linearGradient id="AP" x1="687.568" y1="189.392" x2="699.258" y2="159.43" xlink:href="#B"><stop offset="0" stop-color="#1b1b18"></stop><stop offset="1" stop-color="#454442"></stop></linearGradient></defs><path fill="url(#AP)" d="M691 167h0l2-2h1l1 1-2 1c0 1 0 1 1 2 1 0 2-1 3-2v-1h0l3-2c1-2 1-3 3-4l1 1-1 2h0l2 1-1 2-2 1-1-1-1 1c-1 1-2 2-2 3-1 0-1 0-2 1h-1c-1 1 0 1-1 2 0 1-1 2-1 2v1h2c1-1 2 0 3 0l-1 1c0 1 1 1 1 2l-2 2v1 2h1 1v-1c1 1 1 1 1 2h0-2v1l-1-1-1-3-1-1-1-1h-1c0 1 0 1-1 2h0c-1 2-3 3-4 4l-3 3c0-1 0-3 1-5 1-5 1-11 3-16l3-1z"></path><path d="M693 170h0v2h-2l-1-1c1-1 1-1 3-1z" class="I"></path><path d="M702 147l5 2c3 1 8 2 12 4-1 1-1 2-3 3h0c-1 1-1 2-2 3h0c-1 0-2 0-2-1v-1h-3c0 1-1 1 0 2l-2 2c-1 1-2 2-2 3l-2-1h0l1-2-1-1c-2 1-2 2-3 4l-3 2h0v1c-1 1-2 2-3 2-1-1-1-1-1-2l2-1-1-1h-1l-2 2h0l-3 1c0-1 0-3 1-4l1-1s0-1-1-2h1c1-1 1-2 2-2l1-1c1-1 2-1 2-2l2 1h1c1 0 1-1 2-2v-1c0-1 1-1 1-1 1-2 1-4 1-6z" class="B"></path><path d="M693 158c1-1 2-1 2-2l2 1c-1 1-2 2-1 3l1 1h1l-1 1c-2-1-3-1-6 0l1 2h-2v1h0c1 1 0 1 1 2l-3 1c0-1 0-3 1-4l1-1s0-1-1-2h1c1-1 1-2 2-2l1-1z" class="e"></path><path d="M700 155l1-1c1-1 1-1 1-2v1h1 1l1-1 1 1-2 2v1h1l1 2v1c-1 0-2 0-2-1-2 1-2 1-3 1l-1-1c-1 1-1 2-2 3h-1l-1-1c-1-1 0-2 1-3h1c1 0 1-1 2-2z" class="S"></path><path d="M702 147l5 2c0 1 1 2 1 3h0c1 0 3 0 4 1v1h0-2l-1 3h0l-2-2-2 1h-1v-1l2-2-1-1-1 1h-1-1v-1c0 1 0 1-1 2l-1 1v-1c0-1 1-1 1-1 1-2 1-4 1-6z" class="O"></path><path d="M707 149c3 1 8 2 12 4-1 1-1 2-3 3h0c-1 1-1 2-2 3h0c-1 0-2 0-2-1v-1h-3l1-3h2 0v-1c-1-1-3-1-4-1h0c0-1-1-2-1-3z" class="B"></path><path d="M691 182h0c1-1 1-1 1-2h1l1 1 1 1 1 3 1 1v-1h2 0v1c1 1 1 0 2 2l2 1-1 2 2-1h1v2 1h1v-1c1-1 2-2 2-3h1v2l2-1h1c1-1 1-2 2-3h2l-1 3h2c0-2 0-2 2-3v1c0 1-1 2-1 3s0 0 1 1l1 1 2-1c0 1 0 1-1 2h-1-3c1 1 1 2 2 3l1 1c-1 2-1 3-2 4s-3 6-3 7l2 1-1 1h-1l-1-1-15-6h-1c-4-1-12-5-16-4v1h-4l1-1c1-2 1-3 2-5v-2l1-1h1c1-1 1-1 1-3l3-3c1-1 3-2 4-4z" class="S"></path><path d="M710 195l2 2-2 3c0-1-1-1-1-1 0-2 0-3 1-4z" class="H"></path><path d="M699 186c1 1 1 0 2 2l2 1-1 2 2-1h1v2l-2 2v-2c-1 1-2 1-2 2-1-2 0-2 0-4l-2-1v-3z" class="C"></path><path d="M706 192c1-1 2-2 2-3h1v2l2-1h1c1-1 1-2 2-3h2l-1 3-2 2c0 2 1 3-1 4v1l-2-2c-1 0-1-1-1-1l-2 2-1-1c-1 1-2 2-2 3h0 1l1 1h0-2 0l-2 2v-1-1c0-1 1-2 1-2v-3l2-2v1h1v-1z" class="B"></path><path d="M709 194l4-3v1c0 2 1 3-1 4v1l-2-2c-1 0-1-1-1-1z" class="C"></path><path d="M682 200l-1-2 2-2c0 1 1 2 2 2 2 0 3 0 5 1 0 1 0 1 1 1 4 1 8 1 11 3h1l1-1v-1l2 2 1 1 1-1c1-1 2-2 2-3l2-3v-1l1 1c0 2 1 3 2 4v5l-1 1h-1v-1c-1-1-1-1-1-2l-1 1c-2 0-4 0-6-1s-4-1-6 0h-1c-4-1-12-5-16-4z" class="e"></path><path d="M717 190c0-2 0-2 2-3v1c0 1-1 2-1 3s0 0 1 1l1 1 2-1c0 1 0 1-1 2h-1-3c1 1 1 2 2 3l1 1c-1 2-1 3-2 4s-3 6-3 7l2 1-1 1h-1l-1-1-15-6c2-1 4-1 6 0s4 1 6 1l1-1c0 1 0 1 1 2v1h1l1-1v-5c-1-1-2-2-2-4l-1-1c2-1 1-2 1-4l2-2h2z" class="D"></path><path d="M717 190c0-2 0-2 2-3v1c0 1-1 2-1 3s0 0 1 1l1 1 2-1c0 1 0 1-1 2h-1-3v-1c-1 1-2 1-3 1v-1h1c2-1 2-2 2-3z" class="I"></path><path d="M691 182c1 1 2 3 3 5h2v1h0v4l-1 2c2 0 3-1 5-2v4h0l-2-1c0 1 0 1-1 2-2 2-3 2-6 2h0l1-1-1-1-1 2c-2-1-3-1-5-1-1 0-2-1-2-2l-2 2 1 2v1h-4l1-1c1-2 1-3 2-5v-2l1-1h1c1-1 1-1 1-3l3-3c1-1 3-2 4-4z" class="H"></path><path d="M694 187h2v1h0-1c-2 1-2 2-3 4h-1c1-2 1-3 3-5z" class="x"></path><path d="M719 153h1l4 2h1l7 3 3 2 2 2 2 1 2 1 2 1 6 4-1 2c-2 0-2 0-3 1-1 2-5 7-5 10l-2 2-2 2v-2h-1s-1 1-1 2l-3 3-1 1h-1l-1-1-1 1c0 1 0 1-1 2l-1-1c-3 5-6 12-8 19l-2-1c0-1 2-6 3-7s1-2 2-4l-1-1c-1-1-1-2-2-3h3 1c1-1 1-1 1-2l-2 1-1-1c-1-1-1 0-1-1s1-2 1-3v-1c-2 1-2 1-2 3h-2l1-3v-1-1l-1 1h-1v-1l-1-1c-1-1-1-1 0-1-1-1-1-1-1-2l-2 1h0v-2-2l2-2c1 0 2 0 3 1 1-1 2-1 2-2l1-1 2-2v-1c1-2 1-2 1-4h0v1l-1-1c1-1 1-2 1-3v-3l-2 1-1-1v-2h-4c1-1 1-2 2-3h0c2-1 2-2 3-3z" class="w"></path><path d="M713 183l1-2v-2l2-1c1 1 1 1 2 0-1 2-2 3-3 4s-1 1-1 2h-1c-1-1-1-1 0-1z" class="r"></path><path d="M723 163l3 1h0c-1 1-1 0-2 1v1l-1 2-1 1h0v2h-2c1-2 1-2 1-4 1-1 1-3 2-4z" class="Y"></path><path d="M732 158l3 2 2 2-1 1h-5c1-2 1-3 1-5z" class="L"></path><path d="M723 168l2 1c-1 1-1 2-2 3v1l-3 2v-1-2-1h2v-2h0l1-1z" class="r"></path><path d="M717 183c1-2 2-3 3-5 1 0 1 2 3 1v2l1 2-1-1-2 2h0c-2-2-1 0-3 0v-1h-1z" class="Y"></path><path d="M727 182l-1-2c0-1 1-2 0-3h-1v-2c1-2 1-2 3-3 1 0 1-2 1-3s1-1 2-2c0 1 0 1 1 1v3l-5 11z" class="r"></path><path d="M717 183h1v1c2 0 1-2 3 0l1 1 1 1-1 2h1c0 1 0 0 1 1h0c-1 1-2 2-2 3l-2 1-1-1c-1-1-1 0-1-1s1-2 1-3v-1c-2 1-2 1-2 3h-2l1-3v-1-1l-1 1h-1v-1c1 0 2-1 3-2z" class="J"></path><path d="M722 185l1 1-1 2h1c0 1 0 0 1 1h0c-1 1-2 2-2 3l-2 1-1-1c1-1 1-1 1-2l-1-1 1-1c0-2 1-2 2-3z" class="Y"></path><path d="M719 153h1l4 2h1l7 3c0 2 0 3-1 5h-1l-2 2h0v-1l-1-1-1 1-3-1c-1 1-1 3-2 4h0v1l-1-1c1-1 1-2 1-3v-3l-2 1-1-1v-2h-4c1-1 1-2 2-3h0c2-1 2-2 3-3z" class="D"></path><path d="M719 153h1c-1 2-1 3-1 4l-1 2v-2h-2v-1h0c2-1 2-2 3-3zm4 10c1-1 2-3 3-4 1 1 2 1 3 2v2h1l-2 2h0v-1l-1-1-1 1-3-1z" class="J"></path><path d="M720 153l4 2h1c-1 1-1 1-1 3h1-1c-2 0-3 2-5 2l-1-1 1-2c0-1 0-2 1-4z" class="F"></path><path d="M720 153l4 2c0 1 0 2-1 2-1 1-3 0-4 0 0-1 0-2 1-4z" class="C"></path><path d="M737 162l2 1-2 4-10 20c-1 1-1 3-2 4-3 5-6 12-8 19l-2-1c0-1 2-6 3-7s1-2 2-4l-1-1c-1-1-1-2-2-3h3 1c1-1 1-1 1-2s1-2 2-3h0c1-2 2-5 3-7l5-11c1-3 2-6 4-8l1-1z" class="K"></path><path d="M722 192c0-1 1-2 2-3l-4 9-1-1c-1-1-1-2-2-3h3 1c1-1 1-1 1-2z" class="L"></path><path d="M739 163l2 1 2 1 6 4-1 2c-2 0-2 0-3 1-1 2-5 7-5 10l-2 2-2 2v-2h-1s-1 1-1 2l-3 3-1 1h-1l-1-1-1 1c0 1 0 1-1 2l-1-1c1-1 1-3 2-4l10-20 2-4z" class="o"></path><path d="M730 184l1-1h1v2l2 1-3 3c0-2 0-3-1-5z" class="r"></path><path d="M739 163l2 1c-1 1-2 2-2 4h1 1v1c-1 1-2 1-2 2l-1 1h-1c0-2 0-2 1-4l-1-1 2-4z" class="s"></path><path d="M741 164l2 1-1 5-1 3c-1-1 0-2 0-4v-1h-1-1c0-2 1-3 2-4z" class="Z"></path><path d="M730 184c1 2 1 3 1 5l-1 1h-1l-1-1-1 1c0 1 0 1-1 2l-1-1c1-1 1-3 2-4 1 1 1 0 2 0l1-3z" class="L"></path><path d="M743 165l6 4-1 2c-2 0-2 0-3 1-1 2-5 7-5 10l-2 2c-1-4 2-7 3-11l1-3 1-5z" class="l"></path><path d="M743 165l6 4-1 2c-2 0-2 0-3 1h-2v-1c1-1 1-2 1-3l-1-1-1 3 1-5z" class="v"></path><path d="M594 163l2-1c1 1 0 1 1 2-1 1 0 2-1 3v2c-5 4-9 9-12 14-3 4-5 6-5 10v2c-3 7-4 14-5 21v32l7-1-2 2-5 1v10c0 1 1 3 1 4 1 1 1 1 1 2 1 1 4 2 5 3l1-1v2l-8 1c0 7-1 16 0 24v46h-11l-1-1c2-3 1-17 1-21v-17-6c-1 0-1-1-2-2v-2h-1l-1 3-2-1-1 1c0-1-1-3-1-4l-1 1h-2-1v1c-1-1-2-1-4-1l-3 4-1 1-2 2-1-1-1-5v-1c-1-2 0-5-1-6h-1c-1 1-1 2-1 3 0-1 0-1-1-2l-1 1c-1 0-1 0-2-1l-1-1c-2 1-2 3-3 4-1 0-2 0-3 1 0 1-1 2-3 3l-3 1c-1 0-2-1-3-2l-1-3 3 2 4-5c2-2 5-5 7-8l3-3 4-5 8-9c2-3 4-5 6-7v1h2c1 1 2 1 3 0-1 0-2 0-3-1l3-3c2-1 4 0 7 0v-1-7l1-2v-16-4l-1-2v-4h3c1 0 1-1 1-1 0-1 2-2 2-2v-1c1-2 1-3 1-5s1-4 1-6c0-1 1-2 1-2 1-2 1-1 1-2l1-2 7-14c1-1 4-6 4-7 1-2 3-2 3-4l1-1 6-6z" class="n"></path><path d="M574 260c0 1 1 3 1 4 1 1 1 1 1 2 1 1 4 2 5 3-2-1-5 0-7 0v-9z" class="G"></path><path d="M562 280h1v16c-1 0-1-1-2-2v-2h-1l-1 3-2-1 1-1c2-4 4-8 4-13z" class="x"></path><path d="M562 216h3c1 0 1-1 1-1 0-1 2-2 2-2v6h1l1 1c-2 2-5 1-7 2l-1-2v-4z" class="R"></path><path d="M563 242c1 9 0 18 0 27 0 3 1 7 0 11h-1c-1-4 0-9 0-14 0-4-1-4-3-6-1-1-2-2-2-3h-1l-1-1c-1 0-2 0-3-1l3-3c2-1 4 0 7 0v-1-7l1-2z" class="T"></path><path d="M555 252c2-1 4 0 7 0v2 3l-1 1c-2-1-3-2-3-3v-1c-1 1-1 1-1 3h-1l-1-1c-1 0-2 0-3-1l3-3z" class="V"></path><path d="M559 260c2 2 3 2 3 6 0 5-1 10 0 14 0 5-2 9-4 13l-1 1-1 1c0-1-1-3-1-4l-1 1h-2l4-8c3-9 5-15 3-24z" class="c"></path><path d="M556 284c0 1-1 2 0 2h1l-1 1v2c0 1 1 3 2 4l-1 1-1 1c0-1-1-3-1-4l-1 1h-2l4-8z" class="E"></path><path d="M557 257c0 1 1 2 2 3 2 9 0 15-3 24l-4 8h-1v1c-1-1-2-1-4-1 2-2 4-5 5-8h-1 0c1-1 1-3 2-4l1-5v-2c0-3 0-6-1-8-2-1-4-2-6-2 0 0 0-1 1-1 2-1 4 1 7 0l1 1c0-1 0-2-1-3s0-1-1-2l2-1h1z" class="W"></path><path d="M552 284c1-2 2-5 3-7v2c0 3-1 4-2 6v2c-1 0-2 1-3 1 0 2 1 3 1 4v1c-1-1-2-1-4-1 2-2 4-5 5-8z" class="U"></path><path d="M555 262l1 1c0 5 0 9-1 14-1 2-2 5-3 7h-1 0c1-1 1-3 2-4l1-5v-2c0-3 0-6-1-8-2-1-4-2-6-2 0 0 0-1 1-1 2-1 4 1 7 0z" class="K"></path><path d="M550 255v1h2c1 1 2 1 3 0l1 1-2 1c1 1 0 1 1 2s1 2 1 3l-1-1c-3 1-5-1-7 0-1 0-1 1-1 1 2 0 4 1 6 2 1 2 1 5 1 8v2l-1 5c-1 1-1 3-2 4h0 1c-1 3-3 6-5 8l-3 4-1 1-2 2-1-1-1-5v-1c-1-2 0-5-1-6h-1c-1 1-1 2-1 3 0-1 0-1-1-2l-1 1c-1 0-1 0-2-1l-1-1c-2 1-2 3-3 4-1 0-2 0-3 1 0 1-1 2-3 3l-3 1c-1 0-2-1-3-2l-1-3 3 2 4-5c2-2 5-5 7-8l3-3 4-5 8-9c2-3 4-5 6-7z" class="o"></path><path d="M540 277c0-1 0-2 1-3l1-1c0-1 0-1 1-1v1h1v-2c1 0 1 0 2-1 0-1 0-1 1-2h0l1 1h1c2 1 4 2 4 4l1 2-1 5c-1 1-1 3-2 4-1-1-2-1-3-1v-4l-1-5-1-1v3l-1-1-1 1c-2 0-3 0-4 1z" class="d"></path><path d="M553 280c-2 0-3-1-4-2 1-1 2-1 3-1 0-2 0-2-1-3l1-1h1l1 2-1 5z" class="P"></path><path d="M540 277c1-1 2-1 4-1l1-1 1 1v-3l1 1 1 5v4c1 0 2 0 3 1h0l-1 2c-1 1-3 3-3 5h-2c-2-1-3-2-4-4 0-3 0-5-1-8v-2z" class="q"></path><path d="M545 286c2-1 3-1 5 0-1 1-3 3-3 5-1-2-1-3-1-4h0l-1-1z" class="X"></path><path d="M545 286l1 1h0c0 1 0 2 1 4h-2c-2-1-3-2-4-4l4-1z" class="V"></path><path d="M532 287c1-2 1-3 3-4 2-2 3-3 5-4 1 3 1 5 1 8 1 2 2 3 4 4h2c0-2 2-4 3-5l1-2h1c-1 3-3 6-5 8l-3 4-1 1-2 2-1-1-1-5v-1c-1-2 0-5-1-6h-1c-1 1-1 2-1 3 0-1 0-1-1-2l-1 1c-1 0-1 0-2-1z" class="k"></path><path d="M539 293h0c1-2 0-4 1-6 1 1 1 2 1 3v3h2c0 1 1 2 1 3l-1 1-2 2-1-1-1-5z" class="p"></path><path d="M550 255v1h2c1 1 2 1 3 0l1 1-2 1c1 1 0 1 1 2s1 2 1 3l-1-1c-3 1-5-1-7 0-1 0-1 1-1 1l-6 6c-1 1-1 2-2 3h0c-1 1-1 0-2 1v1c0 2-2 3-3 4-2 3-3 5-5 7-2 3-6 5-9 8h-3l1-1 4-5c2-2 5-5 7-8l3-3 4-5 8-9c2-3 4-5 6-7z" class="O"></path><path d="M555 256l1 1-2 1c1 1 0 1 1 2s1 2 1 3l-1-1c-2-2-4-3-5-6h2c1 1 2 1 3 0z" class="U"></path><path d="M387 136c3 1 7 1 10 2l12 4h0c1 0 2 1 3 2h0c-1 1-1 2-1 2l1 1c3 2 7 4 10 5l14 11v-2c0-2-5-5-7-7h1c2 1 5 4 8 5h1c1 0 2 1 4 2 0 1 1 1 2 2h0c1 2 4 5 6 6l1 1h1v1c1 1 2 2 3 2l2 2c1 2 3 5 4 7l6 13c2 3 5 5 7 8 4 5 9 10 14 15l1 2v1 1c0 1 1 1 1 3h1c2 2 4 4 6 7l1 1c0 1 0 1 1 2l1-1c1-1 2 0 3 0l13 13 7-7 13-14c0 2 0 2 2 3 1-1 3-2 3-5h2 1s1-1 2-1l1-4c1 1 0 1 0 2v3c0 1 1 1 2 2h1c1 0 2 0 3 1h2c1-1 3-1 4-1h3v16l-1 2v7 1c-3 0-5-1-7 0l-3 3c1 1 2 1 3 1-1 1-2 1-3 0h-2v-1c-2 2-4 4-6 7l-8 9-4 5-3 3c-2 3-5 6-7 8l-4 5-3-2 1 3c1 1 2 2 3 2l3-1c2-1 3-2 3-3 1-1 2-1 3-1 1-1 1-3 3-4l1 1c1 1 1 1 2 1l1-1c1 1 1 1 1 2 0-1 0-2 1-3h1c1 1 0 4 1 6v1l1 5 1 1 2-2 1-1 3-4c2 0 3 0 4 1v-1h1 2l1-1c0 1 1 3 1 4l1-1 2 1 1-3h1v2c1 1 1 2 2 2v6 17c0 4 1 18-1 21l-1-1c-1 2-2 5-3 7v1l-1 2-1 10v2c2 0 3 0 4 1h2l1 2c0 1 0 1-1 2-1 0-2-1-3-1 1 2 1 3 1 6l-1 1v1l1 1-2 1v4c-1 4 0 11 0 16v12 41c0 8-1 18 1 26 1 2 1 3 1 4v5 3c-1-2-2-2-3-3 0-2-1-3-1-5l-3 1c0-1 0-2-1-2 0-1-1-2-2-2v-2l-1-3c-1-1-1-2-2-3v-1l-1-1-3 2c0-1-1-3-2-4h-1l-3-4-10-10 1-1h0c-2-1-2-4-3-5l-1-1c-1 0-2 1-3 1l-1-2c-1-1-2-2-3-2l-1-2h0c-4-3-6-7-9-10h0l-7-6-4-4-4-4-2-3c-1 1-1 2-2 3h-2l-2-3v3c-2 0-3-1-4-2l-2 2c-1 3-2 6-1 9l-1 1c1 1 1 1 1 2l1 1-1 2-3 2-1 1c0-2 0-2-1-3h-2v1c-3-1-5-2-7-2h-1c-2-1-3 0-4 0l-2 1h0c-1 1-6 1-6 2l-7 1-17 2h0c-2 0-4-1-5 0h-11 5l1-1c1-6 1-14 1-20-1-1-1-4-1-6v-14l-14-3c-1 0-3-1-4-2-3 0-6-1-8-2-3-1-6-1-8-2-1-1-1-1-2-1-1-1-1 0-1-1 4-1 9-1 13-1h23 1v-37-34-1l-1-17v-1h4c11-3 21-9 32-9l6-1 1-8v-3c-1-7-1-14 0-20v-3-6c0-5 0-11-1-16 0-2 0-5 1-7 1-1 0-1 0-2 1-3 0-5 0-7h0l-2-6h-1v-2h-2-1c0-1 0-3-1-4l2-1c0-1-1-3-2-4-1-3-3-7-5-10l-3-3v-1c0-2-1-3-3-5-3-3-6-6-10-9l-8-5c-9-8-23-12-35-14l2-2h0v-1l2-1h2c0-1-1-1-1-1l-3-1c-1 0-1 0-2-1v-1z" class="n"></path><path d="M423 431c1-1 2-1 3-1s2 0 3-1c1-2 2-4 1-6 0-2 1-3 2-5v-1c0 2 1 9 0 10l-3 3c-2 1-4 1-6 1z" class="W"></path><path d="M460 347v-6h9v1h-1v2h-3c-2-1-2-1-4 0-1 1 0 2-1 3z" class="B"></path><path d="M426 433c7-1 14-3 21-5 2-1 4-1 5-3 1 0 1-2 1-3l1-1 1 1c-1 1-1 2-1 3 1 0 1 1 2 2v1h0c-1 1-6 1-6 2l-7 1-17 2h0z" class="H"></path><path d="M432 381c1-1 2-1 3 0s1 3 1 5h-1c0 3-1 5-2 7v1-2l-3-2c-1-1-1-1-1-2v-6c1-1 2-1 3-1z" class="V"></path><path d="M433 387l-3 3h0c0-2-1-5 0-7 0 0 1-1 1-2v5h1v-1c0-1 0-1 1-2h0v4z" class="X"></path><path d="M432 381c1-1 2-1 3 0s1 3 1 5h-1l-2 1v-4h0c-1 1-1 1-1 2v1h-1v-5h1z" class="R"></path><path d="M469 243c1 1 2 2 4 3 1 0 3 2 3 3v2l-2 1h-1s-1 0 0 1c0 2-1 8-1 10-1-1-1-1-1-3h1v-2-2c-2 0-1 0-2 1 0 1 0 2-1 3v1-18z" class="J"></path><path d="M420 383l22-7c-3 3-4 4-5 8h0 0c-1 1-1 1-1 2 0-2 0-4-1-5s-2-1-3 0c1-1 1-1 2-1-4 0-8 2-12 3 0 3 1 7 0 10v4-1 7 5h1c1 0 1 0 2-1h2 4-2c-1 1-2 1-3 1h-1c-1 1 0 1-1 1s-2 0-3-1c0-2 1-7 0-9v-2-2c0-4 2-9-1-12z" class="t"></path><path d="M469 261v-1c1-1 1-2 1-3 1-1 0-1 2-1v2 2h-1c0 2 0 2 1 3v14h0c-1 1 0 2 0 4h-1v1h-1c-1 2-1 10 0 13v2h1l-1 1h-1v-37z" class="Q"></path><path d="M460 347c1-1 0-2 1-3 2-1 2-1 4 0l-1 1c0 1-1 1 0 2 1 3 0 5 2 8l1 6h-2l-1-1-1 1h-5l2-14z" class="C"></path><path d="M469 223v3l2 1 1 1h-1c-1 1 1 4 1 6v1l2 1h-2c0 2 1 2 2 3l8 11c1 1 1 2 2 4-1 0-1 0-1 1l-3-2-4-2v-2c0-1-2-3-3-3-2-1-3-2-4-3v-3-17z" class="Z"></path><path d="M477 247l4 5-1 1-4-2v-2l1-2z" class="D"></path><path d="M469 240l1-1c2 1 4 4 5 5l2 3-1 2c0-1-2-3-3-3-2-1-3-2-4-3v-3z" class="Q"></path><path d="M472 263c0-2 1-8 1-10-1-1 0-1 0-1h1l2-1 4 2 3 2c-2 2-5 5-7 8-1 5 2 11 3 16 1 1 1 2 2 4v1 1c0 1 1 2 1 4l-1 1c0 1-1 1-2 2v1c-1-1-2-1-2-1h0c-3-4-4-10-5-15v-14z" class="m"></path><path d="M479 288l2 2c0 1-1 1-2 2l-1-2 1-2z" class="q"></path><path d="M478 290c-1-2-1-5-2-7-1-1-1-2-1-3l1-1c0 2 0 3 1 5 1 1 1 2 2 3v1l-1 2z" class="P"></path><path d="M476 251l4 2 3 2c-2 2-5 5-7 8-1-3-1-5-1-7l2-1v-1l-2-1h-1v-1l2-1z" class="X"></path><path d="M434 377l22-7v1c0 1-3 1-4 1l-10 4-22 7c-1 0-3 0-3 1-1 1-1 2-1 3-6 0-12-1-18 0-3 0-6-1-8-2-3-1-6-1-8-2-1-1-1-1-2-1-1-1-1 0-1-1 4-1 9-1 13-1h23l1 1v1h1c5 0 12-3 17-5z" class="a"></path><path d="M398 387c6-1 12 0 18 0 0-1 0-2 1-3 0-1 2-1 3-1 3 3 1 8 1 12v2 2c1 2 0 7 0 9 1 1 2 1 3 1s0 0 1-1h1c1 0 2 0 3-1h2l2-1h2 1c6-2 14-3 20-6l1 1c-2 1-3 2-5 2-6 1-14 4-21 5 0 1 2 2 2 3s-1 5-1 6v1c-1 2-2 3-2 5 1 2 0 4-1 6-1 1-2 1-3 1s-2 0-3 1c-1 0-1 1-3 1h-1-3c1-6 1-14 1-20-1-1-1-4-1-6v-14l-14-3c-1 0-3-1-4-2z" class="R"></path><path d="M417 412c2 4 1 12 1 16h0c-1 1-1 2-1 3 2 0 2 0 3-1h0v2h-1-3c1-6 1-14 1-20z" class="E"></path><path d="M402 389c4-1 10-1 14 0v3l-14-3z" class="H"></path><path d="M420 430v-8-10c-1 0 0 0 0-1h1l1-1h2c2 0 3-1 4-1s2 0 2-1h1c0 1 2 2 2 3s-1 5-1 6v1c-1 2-2 3-2 5 1 2 0 4-1 6-1 1-2 1-3 1s-2 0-3 1c-1 0-1 1-3 1v-2z" class="t"></path><path d="M427 417h0l1 1-1 6v-1c-1-2-1-4 0-6z" class="W"></path><defs><linearGradient id="AQ" x1="378.207" y1="235.161" x2="469.345" y2="244.845" xlink:href="#B"><stop offset="0" stop-color="#c3c2c0"></stop><stop offset="1" stop-color="#fcfbfb"></stop></linearGradient></defs><path fill="url(#AQ)" d="M387 136c3 1 7 1 10 2l12 4h0c1 0 2 1 3 2h0c-1 1-1 2-1 2l1 1c3 2 7 4 10 5l14 11c15 12 25 29 27 49v3 5c-1 0-2 0-3 1v1l1 1-1 1c-1 2-1 30 0 34 1 2 0 5 0 8v17 36 19h-2l-1-2h0c1-8 0-17 0-25 0-7 1-14 0-21v-1l-1-1c-1-1-2-1-2-3l3-5 1-8v-3c-1-7-1-14 0-20v-3-6c0-5 0-11-1-16 0-2 0-5 1-7 1-1 0-1 0-2 1-3 0-5 0-7h0l-2-6h-1v-2h-2-1c0-1 0-3-1-4l2-1c0-1-1-3-2-4-1-3-3-7-5-10l-3-3v-1c0-2-1-3-3-5-3-3-6-6-10-9l-8-5c-9-8-23-12-35-14l2-2h0v-1l2-1h2c0-1-1-1-1-1l-3-1c-1 0-1 0-2-1v-1z"></path><path d="M453 195c1 2 3 5 3 7h-1v-2h-2-1c0-1 0-3-1-4l2-1z" class="b"></path><path d="M391 140h2c2 1 5 2 8 3s6 2 10 3l1 1c-4 1-9-1-13-3-3-1-6-1-10-2h0v-1l2-1z" class="X"></path><defs><linearGradient id="AR" x1="397.972" y1="138.657" x2="410.216" y2="145.507" xlink:href="#B"><stop offset="0" stop-color="#747271"></stop><stop offset="1" stop-color="#8a8a89"></stop></linearGradient></defs><path fill="url(#AR)" d="M387 136c3 1 7 1 10 2l12 4h0c1 0 2 1 3 2h0c-1 1-1 2-1 2-4-1-7-2-10-3s-6-2-8-3c0-1-1-1-1-1l-3-1c-1 0-1 0-2-1v-1z"></path><path d="M436 163v-2c0-2-5-5-7-7h1c2 1 5 4 8 5h1c1 0 2 1 4 2 0 1 1 1 2 2h0c1 2 4 5 6 6l1 1h1v1c1 1 2 2 3 2l2 2c1 2 3 5 4 7l6 13c2 3 5 5 7 8 4 5 9 10 14 15l1 2v1 1c0 1 1 1 1 3-1 2-3 5-5 6-1 0-2 0-3 1 0-1-1-1-2-2l-7 6-2-1v-1c0-2-2-5-1-6h1l-1-1-2-1v-3l-1-1-1-1-7 1v-1c1-1 2-1 3-1v-5-3c-2-20-12-37-27-49z" class="V"></path><path d="M467 209c0 3 1 9 0 12l-7 1v-1c1-1 2-1 3-1v-5l3-1 1-5z" class="D"></path><path d="M470 205c0-3-1-7-3-11-1-3-5-9-5-12l6 13c2 3 5 5 7 8v3l-2-2v1l-1-1v2l-2-1z" class="S"></path><defs><linearGradient id="AS" x1="460.156" y1="183.916" x2="442.582" y2="187.916" xlink:href="#B"><stop offset="0" stop-color="#262422"></stop><stop offset="1" stop-color="#595857"></stop></linearGradient></defs><path fill="url(#AS)" d="M436 163v-2c0-2-5-5-7-7h1c2 1 5 4 8 5h1c11 12 20 23 25 38 1 4 3 8 3 12l-1 5-3 1v-3c-2-20-12-37-27-49z"></path><path d="M475 203c4 5 9 10 14 15l1 2v1 1c0 1 1 1 1 3-1 2-3 5-5 6-1 0-2 0-3 1 0-1-1-1-2-2l-7 6-2-1v-1c0-2-2-5-1-6h1l-1-1-2-1v-3l-1-1h2l1-1v-6l-1-10 2 1v-2l1 1v-1l2 2v-3z" class="S"></path><path d="M475 232l1-1c0-1 1-1 2-2 2-2 3-3 7-3l-4 4-7 6-2-1v-1c1 0 2-1 3-2z" class="L"></path><path d="M475 203c4 5 9 10 14 15l1 2v1l-11-10c-1-2-2-3-4-3v-2-3z" class="D"></path><path d="M490 222c0 1 1 1 1 3-1 2-3 5-5 6-1 0-2 0-3 1 0-1-1-1-2-2l4-4 5-4z" class="m"></path><path d="M472 206v-2l1 1v-1l2 2v2c0 1 1 2 1 3v2 3l1 1v2c0 1 0 0-1 1l-1 1v2l-2 2c0 2 2 2 2 3v4c-1 1-2 2-3 2 0-2-2-5-1-6h1l-1-1-2-1v-3l-1-1h2l1-1v-6l-1-10 2 1z" class="H"></path><path d="M475 214c-1 2-1 3-1 5-1 1-1 2-3 3h-1l1-1v-6c1 0 2-1 4-1z" class="K"></path><path d="M470 205l2 1c1 3 3 6 3 8-2 0-3 1-4 1l-1-10z" class="D"></path><path d="M451 338c-1 2-1 2-1 4h-1c-1 3-1 7-1 11h0l-1 2v2 2h-1 0 0v1h1v1h-1c-1 1 0 3-1 5h1v-2c1 2 1 3 1 5v1l1 1v1l-11 3c-1 0-2 0-2 1-2-3 0-10-1-12 0 3-1 10 0 13-5 2-12 5-17 5h-1v-1l-1-1h1v-37h2c1-1 2-1 3-1h3 1c1-1 3-1 5-1h0c3 0 7-1 10-1 3-1 7-2 11-2z" class="g"></path><path d="M440 350h0c1 1 1 2 1 3l1 1-1 1c0 1 0 3-1 3-1-2-1-5 0-8z" class="T"></path><path d="M440 350v-3l1-1 1 2h1c1-2 1-4 1-6h1c0 1 0 2-1 3v1c0 3-1 5-2 8l-1-1c0-1 0-2-1-3h0z" class="E"></path><path d="M480 360c1 0 2-1 2-1l3-1 1 1c1 1 1 1 0 2l3 2 1 2v1c2 0 3 1 3 2v2h0c0 2-1 3-1 5v2 1 9 2 2 10 5 1c0 1 0 2 1 3v2l-1 1-2-3c-1 1-1 2-2 3h-2l-2-3v3c-2 0-3-1-4-2l-2 2c-1 3-2 6-1 9l-1 1c1 1 1 1 1 2l1 1-1 2-3 2-1 1c0-2 0-2-1-3h-2v1c-2-6-1-12-1-18 0-16 1-32 0-48l1 2c1-1 1-1 2-1v-1h1 2c1-1 0-1 1-2l1 2h1v-2c1 0 1-1 2-1z" class="F"></path><path d="M476 393l1-2h1c0 1 1 2 1 3v2h-2c0-1 0-2-1-3z" class="Z"></path><path d="M477 377l1 1 1 1c0 1-1 1 0 3 0 1 1 2 0 3-1-2-1-3-2-5l-1-1 1-2z" class="Q"></path><path d="M477 374v-2c1-1 1-1 2-1l2 3v1 1c-1 1-2 1-3 2l-1-1h0v-2-1z" class="C"></path><path d="M481 367v3 4l-2-3c-1 0-1 0-2 1v2c-1-2-2-3-3-5h1 3l3-2z" class="e"></path><path d="M480 360c1 0 2-1 2-1v2c-1 1-1 1-2 1l-1 1 1 1c-3 1-4 1-6 3v2c1 2 2 3 3 5v1h-1c-3-4-4-6-4-11v-1h1 2c1-1 0-1 1-2l1 2h1v-2c1 0 1-1 2-1z" class="l"></path><path d="M485 358l1 1c1 1 1 1 0 2l3 2 1 2v1c2 0 3 1 3 2v2h0l-3 2c0-2 0-3-1-4-2 0-3 0-4-1v-1c-2-1-2 1-2 1h-2l-3 2h-3-1v-2c2-2 3-2 6-3l-1-1 1-1c1 0 1 0 2-1v-2l3-1z" class="O"></path><path d="M485 358l1 1c1 1 1 1 0 2l3 2 1 2c-4-1-6-1-10-1l-1-1 1-1c1 0 1 0 2-1v-2l3-1z" class="k"></path><path d="M476 393c1 1 1 2 1 3h2v-2l2 3v2 2h1l1-1h1l1 3h0l-3-1c0 1 0 2-1 3l1 1c0 1 0 1 1 2h1l1 1-1 1v3c-2 0-3-1-4-2-2-2-3-5-4-8l-1-1c-1-3 0-6 1-9z" class="Y"></path><path d="M479 396v3c0 1 1 1 1 2v3l-1 1c-1-3-2-5-2-9h2z" class="o"></path><path d="M484 400h1c0-2 0-3 1-4l3 3 2-1h0c0 1 1 2 1 3v5 1c0 1 0 2 1 3v2l-1 1-2-3c-1 1-1 2-2 3h-2l-2-3 1-1-1-1h-1c-1-1-1-1-1-2l-1-1c1-1 1-2 1-3l3 1h0l-1-3z" class="F"></path><path d="M489 399l2-1h0c0 1 1 2 1 3v5c-1-1-1-2-1-3v-1l-2-2v2c-1-1-2-1-3-3h3z" class="Q"></path><path d="M484 400h1c0-2 0-3 1-4l3 3h-3c1 2 2 2 3 3l1 2-1 2-3-3v1h-2c-2 1-2 1-2 2l-1-1c1-1 1-2 1-3l3 1h0l-1-3z" class="r"></path><path d="M486 404v-1l3 3-1 2 1 1 1 1c-1 1-1 2-2 3h-2l-2-3 1-1-1-1h-1c-1-1-1-1-1-2s0-1 2-2h2z" class="W"></path><path d="M486 404v-1l3 3-1 2 1 1h-3v-5zm-5-37h2s0-2 2-1v1c1 1 2 1 4 1 1 1 1 2 1 4l3-2c0 2-1 3-1 5v2 1 9 2 2 10c0-1-1-2-1-3h0l-2 1-3-3c-1 1-1 2-1 4h-1-1l-1-6v-6c-1-1 0-3 0-4-1-3-1-6-1-9v-1-4-3z" class="L"></path><path d="M490 372l3-2c0 2-1 3-1 5v2h-2l-1-1 1-4z" class="C"></path><path d="M492 391c-1 3 0 4-2 6l-1-1c0-2-1-4 0-6 1 0 2-1 3-1v2zm0-14v1 9c-1 0-1 0-2-1 1-2 0-2-1-4 0-1 1-3 1-4v-1h2z" class="D"></path><path d="M481 370h0c1 0 2 0 3-1 0 0 0 1 1 1 0 1 0 2 1 2l1 1 1 1h-1c0 1 0 1-1 2l1 1h1v3 3 1l-1 1v1 3l-2 1c1 1 1 2 2 4l-1 2c-1 1-1 2-1 4h-1-1l-1-6v-6c-1-1 0-3 0-4-1-3-1-6-1-9v-1-4z" class="y"></path><path d="M482 388v-2c1 1 1 3 1 4h1l1 1-1 1c0 1 0 2-1 2h-1v-6z" class="m"></path><path d="M471 297h-1v-2c-1-3-1-11 0-13h1v-1h1c0-2-1-3 0-4h0c1 5 2 11 5 15h0l3 5c1 1 2 3 3 4a57.31 57.31 0 0 0 11 11c2 2 5 3 7 5 1 0 2 1 3 1v3l11 3c-1 1-1 1-1 2-1-1-3-1-5-1l-1-1h-5 0c0 2 0 2 1 3s2 1 2 2l-1-1-2 1v2-1l-1 1-1-1-1 1 2 3 1 1c0 2 0 4-1 6l-1-1v-1c-1-1-2-2-2-3h-1c-1 1-1 1-1 2v3l-2 1h0c1 3 0 7 0 9l-1 1-3-1h0c-1 1-1 2-2 3h1l-1 1v-1c-1-1-2-1-4 0v4h0l-3 1s-1 1-2 1-1 1-2 1v2h-1l-1-2c-1 1 0 1-1 2h-2-1v1c-1 0-1 0-2 1l-1-2-2-2-1-6c-2-3-1-5-2-8-1-1 0-1 0-2l1-1h3v-2h1v-1h0v-8c-1-3 0-7 0-10v-25h1l1-1z" class="K"></path><path d="M469 333h1c1 1 1 2 1 4v1c0 1-1 2-2 3v-8z" class="B"></path><path d="M485 327h0l4 4c0 1-1 2-1 3-1 0-2-1-2-1l-1-3-1-2 1-1z" class="O"></path><path d="M469 323l1 1c1 0 1 0 2 1 0 0 0-1 1-1v1 2c-2 0-2 0-3 1 0 1 2 2 1 3l-1 2h-1c-1-3 0-7 0-10z" class="I"></path><path d="M476 329c1-2 3-3 5-4 2 1 3 1 4 2l-1 1c-2 0-3 1-5 0h-1v2h0c-1 1-2 1-3 2l1-3z" class="a"></path><path d="M478 330l2 3v2c0 1 1 2 1 3v1c-2 0-2-1-3-2 0-1-1-2-1-2h-1v1h-1v-1s-1 0-1-1 0-1 1-2 2-1 3-2z" class="O"></path><path d="M478 330h0v-2h1c2 1 3 0 5 0l1 2 1 3c0 2-1 3-2 4l-3 1c0-1-1-2-1-3v-2l-2-3z" class="l"></path><path d="M478 330h0v-2h1c2 1 3 0 5 0l1 2h-3c-1 1 0 0 0 1-1 1-1 1-1 2l-1 2h0v-2l-2-3z" class="K"></path><path d="M476 329v-1c1-2 3-4 5-4 3-1 5 0 7 1h0 1c-1-1-2-1-2-2 3 1 5 2 8 4-1 0-2 0-3 2v1l1 2c-1 4-2 8-6 10-2 1-4 2-6 1-3-1-5-2-7-5v-1h1c1 2 2 3 4 4h0l2 1c2 1 5-1 7-2 1-2 3-4 3-6s0-5-2-6c-1-2-4-3-6-3h-2c-2 1-4 2-5 4z" class="T"></path><path d="M492 330v-1c1-2 2-2 3-2 1 1 3 2 4 4v1l-1 1v2h2c0 2 1 2 1 4-1-1-2-2-2-3h-1c-1 1-1 1-1 2v3l-2 1h0c1 3 0 7 0 9l-1 1-3-1v-1c1-2 1-3 1-4 1 0 1-2 1-2l-1-1c1-1 2-3 2-4 1-2 1-4 0-6l-1-1-1-2z" class="Q"></path><path d="M492 330v-1c1-2 2-2 3-2 1 1 3 2 4 4v1c-3 0-4-1-7-2z" class="f"></path><path d="M493 332l1 1c1 2 1 4 0 6 0 1-1 3-2 4-2 3-5 4-9 4-2 0-3 0-4-1s-3-2-4-2-2-1-3-1c1-2 1-3 1-5h1c2 3 4 4 7 5 2 1 4 0 6-1 4-2 5-6 6-10z" class="M"></path><path d="M471 297c1 1 1 2 2 3v2h1l1 1v1 3l1 1c1 1 1 0 1 1 1 1 2 2 3 4 1 0 2 0 2 1 1 0 1 2 2 3s2 2 2 3h0c-1 1-1 0-2 1l-1 1c-1 0-1 0-2 1h-4l-1 2h-2-1v-1c-1 0-1 1-1 1-1-1-1-1-2-1l-1-1v-25h1l1-1z" class="B"></path><path d="M471 310c1 0 1 0 2 1l1 1h0c-1 1-2 1-3 1h-1l1-3z" class="O"></path><path d="M471 310c0-2-1-2 0-3 1 1 2 0 3 0 1 2 1 2 1 4v1h-1l-1-1c-1-1-1-1-2-1zm0-13c1 1 1 2 2 3v2h1c-2 0-2 1-3 1v1h0c-2-2-1-4-1-6l1-1z" class="C"></path><path d="M473 324h0c-1-1-1-1-2-1v-5-1h1l1 1v2 2c1 0 1 1 2 1v1l-1 1h-1v-1z" class="S"></path><path d="M471 304v-1c1 0 1-1 3-1l1 1v1 3l1 1c1 1 1 0 1 1 1 1 2 2 3 4 1 0 2 0 2 1 1 0 1 2 2 3s2 2 2 3h0c-1 1-1 0-2 1-2 0-5-1-7 0v1l-1-1 1-1h1l2-2c1 0 1 0 1-1l-1-1h0c-1-2-3-3-5-4v-1c0-2 0-2-1-4l-3-3z" class="G"></path><path d="M469 342l1-1 1-1h1v-3l1-1 1 1v1h-1c0 2 0 3-1 5 1 0 2 1 3 1s3 1 4 2 2 1 4 1c4 0 7-1 9-4l1 1s0 2-1 2c0 1 0 2-1 4v1h0c-1 1-1 2-2 3h1l-1 1v-1c-1-1-2-1-4 0v4h0l-3 1s-1 1-2 1-1 1-2 1v2h-1l-1-2c-1 1 0 1-1 2h-2-1v1c-1 0-1 0-2 1l-1-2-2-2-1-6c-2-3-1-5-2-8-1-1 0-1 0-2l1-1h3v-2h1z" class="J"></path><path d="M486 348v-1c1 0 2 1 3 0s2-1 3-1c0 1 0 2-1 4v1h0c-1-2 0-2-1-3l-2 1c-1 0-1 0-2-1z" class="L"></path><path d="M475 344c1 0 3 1 4 2 0 0 0 2-1 2 0 1-1 2-1 2-1 1 0 2 0 3h-1l-1-2 1-1-1-1v-5z" class="B"></path><path d="M486 348c1 1 1 1 2 1l2-1c1 1 0 1 1 3-1 1-1 2-2 3h1l-1 1v-1c-1-1-2-1-4 0l-2-1v-1c0-2 0-2-1-3 1-1 2-1 4-1z" class="s"></path><g class="r"><path d="M485 354l1-1c0-2-1-2-1-4h1c1 1 2 2 2 3s0 0 1 1v1h1l-1 1v-1c-1-1-2-1-4 0z"></path><path d="M478 353l1-1v-2h1v1 3h1 0c1-1 1 0 1-1h1l2 1v4h0l-3 1s-1 1-2 1-1 1-2 1v-2l1-1c0-2 0-3-1-5z"></path></g><path d="M483 353l2 1v4h0l-3 1s-1 1-2 1c0-1 0-1 1-2s1-3 1-5h1z" class="y"></path><path d="M471 350h1c0 1 1 1 1 2l1-1h0c1 2 1 3 3 4l1-2h0c1 2 1 3 1 5l-1 1v2 2h-1l-1-2c-1 1 0 1-1 2h-2-1c0-3 1-6 1-8l-2-1v-4z" class="D"></path><path d="M471 350h1c0 1 1 1 1 2l1-1h0c1 2 1 3 3 4v1l-2 1-1-1 1-1-2-2v2l-2-1v-4z" class="B"></path><path d="M477 355l1-2h0c1 2 1 3 1 5l-1 1v2 2h-1l-1-2c-1 1 0 1-1 2h-2c0-1 0-2 1-3h2c1-1 1-1 1-4v-1z" class="L"></path><path d="M469 342l1-1 1-1h1v-3l1-1 1 1v1h-1c0 2 0 3-1 5 1 0 2 1 3 1v5l1 1-1 1h-1 0l-1 1c0-1-1-1-1-2h-1c-1-1-1 0-1-1-1-2-1-3-3-4 0 0-1 0-1-1h-1 3v-2h1z" class="G"></path><path d="M472 343c1 0 2 1 3 1v5l1 1-1 1h-1 0 0c-1-1-1-2-2-3 0-2-1-3-1-5h1z" class="C"></path><path d="M465 344h1c0 1 1 1 1 1 2 1 2 2 3 4 0 1 0 0 1 1v4l2 1c0 2-1 5-1 8v1c-1 0-1 0-2 1l-1-2-2-2-1-6c-2-3-1-5-2-8-1-1 0-1 0-2l1-1z" class="M"></path><path d="M471 297h-1v-2c-1-3-1-11 0-13h1v-1h1c0-2-1-3 0-4h0c1 5 2 11 5 15h0l3 5c1 1 2 3 3 4a57.31 57.31 0 0 0 11 11c2 2 5 3 7 5 1 0 2 1 3 1v3l11 3c-1 1-1 1-1 2-1-1-3-1-5-1l-1-1h-5 0c0 2 0 2 1 3s2 1 2 2l-1-1-6-3c-3-1-7-4-10-6-2-2-5-4-7-7l-4-5-3-3v-1l-1-1h-1v-2c-1-1-1-2-2-3z" class="F"></path><path d="M475 303c0-2-2-4-2-6 0-3 0-6-1-8v-1h1c1 5 4 9 7 13l6 7c-1 2-3 3-4 4l-4-5-3-3v-1z" class="b"></path><path d="M480 301l6 7c-1 2-3 3-4 4l-4-5h1 3c0-3-3-4-2-6z" class="X"></path><path d="M486 308l10 9 8 4 11 3c-1 1-1 1-1 2-1-1-3-1-5-1l-1-1h-5 0c0 2 0 2 1 3s2 1 2 2l-1-1-6-3c-3-1-7-4-10-6-2-2-5-4-7-7 1-1 3-2 4-4z" class="W"></path><path d="M486 308l10 9h-1l-1 1c-1-1-1-2-2-2h-1c1 0 1 0 2 1 0 1 0 1 1 2-1 1 0 1-1 1s-2-1-4-2v1c-2-2-5-4-7-7 1-1 3-2 4-4z" class="V"></path><path d="M457 280l-3 5c0 2 1 2 2 3l1 1v1c1 7 0 14 0 21 0 8 1 17 0 25h0l-1 2h-4-1c-4 0-8 1-11 2-3 0-7 1-10 1h0c-2 0-4 0-5 1h-1-3c-1 0-2 0-3 1h-2v-34-1l-1-17v-1h4c11-3 21-9 32-9l6-1z" class="n"></path><path d="M430 302c2 12 1 25 0 37h1 3l5-1h3c3-1 6-1 9-2h6 0l-1 2h-4-1c-4 0-8 1-11 2-3 0-7 1-10 1h0c-2 0-4 0-5 1h-1-3c-1 0-2 0-3 1h-2v-34-1l4-1 1-1 2-1c1 0 2-1 3-1l1-1 3-1z" class="V"></path><path d="M417 310c2 1 2 1 3 3v4h-1 0v-1c-2-1-1-4-2-6z" class="U"></path><path d="M416 309l1 1c1 2 0 5 2 6v1h0c-2 3-1 7-1 10l-1 13 1 1c3 0 5-1 7-1s3 0 4-1h2 3l5-1h3c3-1 6-1 9-2h6 0l-1 2h-4-1c-4 0-8 1-11 2-3 0-7 1-10 1h0c-2 0-4 0-5 1h-1-3c-1 0-2 0-3 1h-2v-34z" class="X"></path><path d="M457 280l-3 5c0 2 1 2 2 3l1 1v1l-2 1h0l-9 4-2 1-4 1-4 2-1 1c-2 1-4 2-5 2l-3 1-1 1c-1 0-2 1-3 1l-2 1-1 1-4 1-1-17v-1h4c11-3 21-9 32-9l6-1z" class="t"></path><path d="M428 300l1 1s1 0 2-1c1 0 1 0 2-1h1l2-1 8-3h1l1-1c1-1 0-1 1-1 2 0 3-1 4-1 1-1 2-1 3-2h1v1h0l-9 4-2 1-4 1-4 2-1 1c-2 1-4 2-5 2l-3 1-1 1c-1 0-2 1-3 1l-2 1-1 1-4 1-1-17h1 2c1 2 1 3 2 4 0 1-1 2-1 2-1 2 0 5-1 7h0c1 0 2 0 2-1l8-3z" class="i"></path><path d="M419 290c11-3 21-9 32-9-1 1-3 0-5 1-1 1-1 1-2 1s-2 1-2 1c-1 1-2 1-3 2v2h0l-1-2h0l-5 1c-1 1-3 1-4 1 0 4 0 8-1 12-2 1-5 2-8 3 0 1-1 1-2 1h0c1-2 0-5 1-7 0 0 1-1 1-2-1-1-1-2-2-4h-2-1v-1h4z" class="T"></path><path d="M423 294l1-2h1v1 1l1 1h0l-3 1v-2z" class="E"></path><path d="M415 290h4c1 1 1 1 2 1 1 1 1 2 2 3v2c-1 1-1 2-2 4h1 0l1-1 2 1-1 1h0c-2 0-2 1-3 1l-1 1c0 1-1 1-2 1h0c1-2 0-5 1-7 0 0 1-1 1-2-1-1-1-2-2-4h-2-1v-1z" class="f"></path><defs><linearGradient id="AT" x1="528.465" y1="288.575" x2="536.055" y2="295.97" xlink:href="#B"><stop offset="0" stop-color="#7f7f7d"></stop><stop offset="1" stop-color="#a2a2a0"></stop></linearGradient></defs><path fill="url(#AT)" d="M484 254l1 2h0l2 4h0c1 2 3 3 4 5-1 1-1 1-2 1v2c1-1 1-1 1-2 1 1 2 2 4 3 1 1 2 3 3 4 1 2 3 3 3 4 2 2 3 3 4 5l3 3h0 1c1 0 1 1 2 1 1 1 1 1 2 1s3 3 3 3l1 3c1 1 2 2 3 2l3-1c2-1 3-2 3-3 1-1 2-1 3-1 1-1 1-3 3-4l1 1c1 1 1 1 2 1l1-1c1 1 1 1 1 2 0-1 0-2 1-3h1c1 1 0 4 1 6v1l1 5 1 1 2-2 1-1 3-4c2 0 3 0 4 1v-1h1 2l1-1c0 1 1 3 1 4l1-1 2 1-6 8c-8 8-16 16-28 19-2 1-6 1-6 3l-1 1-1-1-1 1 1 2h0l-3-2c0-1 0-1 1-2l-11-3v-3c-1 0-2-1-3-1-2-2-5-3-7-5a57.31 57.31 0 0 1-11-11c-1-1-2-3-3-4l-3-5s1 0 2 1v-1c1-1 2-1 2-2l1-1c0-2-1-3-1-4v-1-1c-1-2-1-3-2-4-1-5-4-11-3-16 2-3 5-6 7-8 0-1 0-1 1-1z"></path><path d="M528 300h1v-6h1v6 5l-1-3v3l-1-1v-4z" class="f"></path><path d="M530 300l2-1c0-2 0-2-1-3h1c1 1 1 2 1 4h0v2l-1 2c1 0 1 1 2 1-3 2-5 3-8 4 0-1 3-2 5-3l-1-1v-5z" class="p"></path><path d="M533 300l1-1c0-1 0-2 1-3h2 1c1 1 1 2 1 3l1-1 1 1c-2 2-5 4-7 6-1 0-1-1-2-1l1-2v-2h0z" class="T"></path><path d="M517 297h1v3h1l1-2c1-1 1 0 2-1h1 3v2l1-1v-3h1v5 4l1 1v-3l1 3 1 1c-2 1-5 2-5 3l-6 3c-3 1-9 0-12-2 0-1-1-1-1-2-2-1-1-1-2-2l2-7s0 1 1 1h1c1 1 0 1 1 1 0 1 0 2 1 3h3c1-1 1-3 1-4h0l1 1h0c1-1 1-1 1-3v-1z" class="E"></path><defs><linearGradient id="AU" x1="497.013" y1="279.892" x2="501.404" y2="302.124" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#9d9d9b"></stop></linearGradient></defs><path fill="url(#AU)" d="M498 284c0-1 0-4 1-5 1 1 1 2 2 3 0 1 1 2 1 3h1 1v1l3 2 1-1v1c1 1 2 1 3 1 0 1 1 2 1 2l5 5v1 1c0 2 0 2-1 3h0l-1-1h0c0 1 0 3-1 4h-3c-1-1-1-2-1-3-1 0 0 0-1-1h-1c-1 0-1-1-1-1l-2 7c1 1 0 1 2 2 0 1 1 1 1 2-5-2-9-5-13-8l1-1c2-1 2-1 2-2-1-3-1-5-2-8h1l1 1v-8z"></path><path d="M503 304c-1-1-1-1-2-1v-4l-1-1c0-2 0-5 1-6v1c1 0 1 1 2 2 1-2 0-6 1-8 1 3 1 5 1 8 0 2-1 3-1 4-1 1-1 3-1 5z" class="b"></path><path d="M504 286l3 2 1-1v1c1 1 2 1 3 1 0 1 1 2 1 2l5 5v1 1c0 2 0 2-1 3h0l-1-1h0c0 1 0 3-1 4h-3c-1-1-1-2-1-3-1 0 0 0-1-1h-1c-1 0-1-1-1-1l-2 7-2-2c0-2 0-4 1-5 0-1 1-2 1-4v5c1-1 1-3 2-4-1-1-1-4 0-5l1 2c0-2-1-3-1-4l-2-1-1-2z" class="f"></path><path d="M504 286l3 2 1-1v1c1 1 2 1 3 1 0 1 1 2 1 2l5 5v1 1c-1-1-2-1-4-2-1 0-1-2-1-3-1 1-1 4-1 5h-1l-1-7-1 8c0-2-1-4 0-6 0-2-1-3-1-4l-2-1-1-2z" class="m"></path><path d="M484 254l1 2h0l2 4h0c1 2 3 3 4 5-1 1-1 1-2 1v2c1-1 1-1 1-2 1 1 2 2 4 3 1 1 2 3 3 4 1 2 3 3 3 4 2 2 3 3 4 5l3 3h0l4 4c-1 0-2 0-3-1v-1l-1 1-3-2v-1h-1-1c0-1-1-2-1-3-1-1-1-2-2-3-1 1-1 4-1 5v8l-1-1h-1c1 3 1 5 2 8 0 1 0 1-2 2l-1 1-6-6s-1 0-1 1c-1 0-1 1-2 1s-2 1-3 3c-1-1-2-3-3-4l-3-5s1 0 2 1v-1c1-1 2-1 2-2l1-1c0-2-1-3-1-4v-1-1c-1-2-1-3-2-4-1-5-4-11-3-16 2-3 5-6 7-8 0-1 0-1 1-1z" class="p"></path><path d="M486 298c0-2-2-4-1-6h2c2 1 0 1 1 3 1 0 1 1 1 1s-1 0-1 1c-1 0-1 1-2 1z" class="X"></path><path d="M482 289c1 1 0 2 0 3v2 1c-1 0-2 1-2 2l-3-5s1 0 2 1v-1c1-1 2-1 2-2l1-1z" class="d"></path><path d="M491 273c1-1 1-2 1-3h1v3c1-1 1-1 2-1l2 1c1 2 3 3 3 4 2 2 3 3 4 5l3 3h0l4 4c-1 0-2 0-3-1v-1l-1 1-3-2v-1h-1-1c0-1-1-2-1-3-1-1-1-2-2-3-1 1-1 4-1 5v8l-1-1h-1c-1-3 0-9-2-11-1 0-3 0-4-1h0-1v3h-1l-1 1v1l-1 1c1 1 1 0 1 1 2-1 2-1 2-2l1-2 1-1v2l-1 1c0 1-1 2-1 3s-1 2-2 2h-2l1-1v-1l-1-5h0v-1c0-1 2-3 3-4 1 0 1 0 1-1l2-2v-1z" class="k"></path><path d="M491 273c1-1 1-2 1-3h1v3c1-1 1-1 2-1l2 1c1 2 3 3 3 4 2 2 3 3 4 5l3 3h0l4 4c-1 0-2 0-3-1v-1l-1 1-3-2v-1h-1-1c0-1-1-2-1-3-1-1-1-2-2-3-1 1-1 4-1 5h0v-2c0-1-1-3-1-3l-1-1c-1 0-1-1-1-1-1 0-1 1-2 0v-1c-1 1-1 1-2 1v-1-2-1z" class="h"></path><path d="M491 273c1-1 1-2 1-3h1v3c1-1 1-1 2-1l2 1c1 2 3 3 3 4h-1c0 1-1 1-1 2-1-1-1-2-1-3-2-1-2-1-4-1 0-1 0-1-1-1 0 0 0 1-1 2h0v-2-1z" class="y"></path><path d="M484 254l1 2h0l2 4h0c1 2 3 3 4 5-1 1-1 1-2 1v2c1-1 1-1 1-2 1 1 2 2 4 3 1 1 2 3 3 4l-2-1c-1 0-1 0-2 1v-3h-1c0 1 0 2-1 3v1l-2 2c0 1 0 1-1 1-1 1-3 3-3 4v1h0l1 5v1l-1 1c-1-1-3-4-4-5v-1c-1-2-1-3-2-4-1-5-4-11-3-16 2-3 5-6 7-8 0-1 0-1 1-1z" class="s"></path><path d="M488 271c1-1 1-1 2-1 1 1 1 2 1 3v1l-2 2c-1-2-1-3-1-5z" class="l"></path><path d="M482 281l-1-2v-7-4c1-1 1-2 2-3 0-1 0 0 1-1h1 1c1 1 2 4 2 5l-1-1c-1 1-1 1-1 2v1h0c-1-1-1-2-1-3l-1-1c-1 2 0 2 0 4l-2 2c0 2 0 4 1 6l-1 2z" class="o"></path><path d="M482 281l1-2c-1-2-1-4-1-6l2-2c0-2-1-2 0-4l1 1c0 1 0 2 1 3h0v-1c0-1 0-1 1-2l1 1v2c0 2 0 3 1 5 0 1 0 1-1 1-1 1-3 3-3 4v1h0l1 5c-2-2-3-3-4-5l-1-1h1z" class="l"></path><path d="M486 271v-1c0-1 0-1 1-2l1 1v2c0 2 0 3 1 5 0 1 0 1-1 1-1 1-3 3-3 4v1h0c-1 0-1-1-1-1 0-1 0-1 1-2 0 0 0-1 1-1v-1c0-1 0-3 1-4v-1l-1-1z" class="m"></path><path d="M484 254l1 2h0l2 4h0c1 2 3 3 4 5-1 1-1 1-2 1-2-2-5-7-7-8-1 2-1 4-2 6s-1 3-1 5c0 3 1 8 0 10-1-5-4-11-3-16 2-3 5-6 7-8 0-1 0-1 1-1z" class="K"></path><defs><linearGradient id="AV" x1="516.353" y1="297.233" x2="517.286" y2="319.592" xlink:href="#B"><stop offset="0" stop-color="#c5c4c3"></stop><stop offset="1" stop-color="#e9e9e8"></stop></linearGradient></defs><path fill="url(#AV)" d="M552 292h2l1-1c0 1 1 3 1 4l1-1 2 1-6 8c-8 8-16 16-28 19-2 1-6 1-6 3l-1 1-1-1-1 1 1 2h0l-3-2c0-1 0-1 1-2l-11-3v-3c-1 0-2-1-3-1-2-2-5-3-7-5a57.31 57.31 0 0 1-11-11c1-2 2-3 3-3s1-1 2-1c0-1 1-1 1-1l6 6c4 3 8 6 13 8 3 2 9 3 12 2l6-3c3-1 5-2 8-4 2-2 5-4 7-6l2-2 1-1 3-4c2 0 3 0 4 1v-1h1z"></path><path d="M550 296c1 2 1 3 1 5-1-1-2-1-3-2l2-3z" class="N"></path><path d="M547 292c2 0 3 0 4 1-1 1-1 2-2 2l-1 1c-2 2-3 5-6 7 0-1 2-3 3-4l-3 1c0-1 1-1 1-2v-1l1-1 3-4z" class="W"></path><path d="M552 292h2l1-1c0 1 1 3 1 4-1 2-3 4-5 6 0-2 0-3-1-5l-1-1c1 0 1-1 2-2v-1h1z" class="X"></path><path d="M504 318c7 3 14 4 22 1 0 2 0 2-1 3-2 1-6 1-6 3l-1 1-1-1-1 1 1 2h0l-3-2c0-1 0-1 1-2l-11-3v-3z" class="u"></path><path d="M556 295l1-1 2 1-6 8c-8 8-16 16-28 19 1-1 1-1 1-3 9-3 20-10 25-18 2-2 4-4 5-6z" class="K"></path><path d="M544 224h1s1-1 2-1l1-4c1 1 0 1 0 2v3c0 1 1 1 2 2h1c1 0 2 0 3 1h2c1-1 3-1 4-1h3v16l-1 2v7 1c-3 0-5-1-7 0l-3 3c1 1 2 1 3 1-1 1-2 1-3 0h-2v-1c-2 2-4 4-6 7l-8 9-4 5-3 3c-2 3-5 6-7 8l-4 5-3-2s-2-3-3-3-1 0-2-1c-1 0-1-1-2-1h-1 0l-3-3c-1-2-2-3-4-5 0-1-2-2-3-4-1-1-2-3-3-4-2-1-3-2-4-3 0 1 0 1-1 2v-2c1 0 1 0 2-1-1-2-3-3-4-5h0l-2-4h0l-1-2c-1-2-1-3-2-4l-8-11c-1-1-2-1-2-3h2l7-6c1 1 2 1 2 2 1-1 2-1 3-1 2-1 4-4 5-6h1c2 2 4 4 6 7l1 1c0 1 0 1 1 2l1-1c1-1 2 0 3 0l13 13 7-7 13-14c0 2 0 2 2 3 1-1 3-2 3-5h2z" class="T"></path><path d="M541 246v-2c1 0 2 0 2 1 1 2 1 3 2 4l1 1c-1 2-3 6-5 7 0-2 1-3 2-4-1-1-1-2-2-3-1 0-1-1-1-2l1-2z" class="m"></path><path d="M541 246c1 1 2 1 2 3v4h0c-1-1-1-2-2-3-1 0-1-1-1-2l1-2zm7 7l3-3c2-2 3-3 5-4l-6 9c-2 2-4 4-6 7l-8 9-1-1c0-2 0-2 1-3 1-3 4-5 5-7 1-1 1-1 1-2l2-3c1-1 2-2 3-4l1 2z" class="b"></path><path d="M548 253l3-3c2-2 3-3 5-4l-6 9c-2 2-4 4-6 7-1-1-1-1-2-1l6-8z" class="d"></path><path d="M552 237c1-1 1-1 2-1 2 0 3 0 5 1l1 1 2 1c-1 2-4 5-6 7-2 1-3 2-5 4l-3 3-1-2c2-1 3-3 5-4l-1-1-2 2-1 1c-1 0-1 0-2 1l-1-1c2-1 3-1 4-3h-1c1-2 4-5 6-8l-2-1z" class="q"></path><path d="M552 237c1-1 1-1 2-1 2 0 3 0 5 1l1 1v1c-2-1-3 0-4 0s-2 0-2-1l-2-1z" class="V"></path><path d="M523 257v4c1-1 1-2 1-4h2c0-2 0-2 2-3l1 1v-4h1v4 8c0 1 1 2 2 3v1l-1 1-6 7c-1 2-3 4-5 6-1-7 0-14 0-21l2-1v-2h1 0z" class="i"></path><path d="M531 268c-2 0-2 0-3-1 1-2 1-3 2-4 0 1 1 2 2 3v1l-1 1z" class="N"></path><path d="M525 275c-1 2-3 4-5 6-1-7 0-14 0-21l2-1c-1 4 0 6 0 9v6l1 1c0-1 1-2 0-4v-5c1 1 1 3 1 5h0l1 2h0v2z" class="X"></path><path d="M542 224h2l5 5-2-1-2 1 1 1v1l3 3 3 3 2 1c-2 3-5 6-6 8h1c-1 2-2 2-4 3-1-1-1-2-2-4 0-1-1-1-2-1v2l-1 2-1-1v-3l1-1c0-1 0-1-1-2-1-2-1-3-1-4h-2c1-2 2-3 2-6l1-2c1-1 3-2 3-5z" class="E"></path><path d="M542 236c1 2 1 3 3 4h-1v1c0 1 1 2 1 3-2 0-3-2-4-3s-1-1-1-3l2-2z" class="B"></path><path d="M539 229c1-1 3-2 3-5l1 1c0 1 1 2 1 3-2 3-5 5-6 9h-2c1-2 2-3 2-6l1-2z" class="s"></path><path d="M546 231l3 3 3 3 2 1c-2 3-5 6-6 8l-2-1-1-1c0-1-1-2-1-3v-1h1c-2-1-2-2-3-4l3-3 1-2z" class="K"></path><path d="M544 240c1 1 2 1 3 2l1 2v1h-2l-1-1c0-1-1-2-1-3v-1z" class="C"></path><path d="M546 231l3 3-1 1v1c1 0 2 1 3 1v1c0 1 0 1-1 2h-1v-1-1h-1c-1 0-2-2-2-3s-1-1-1-2l1-2z" class="G"></path><path d="M542 236l3-3c0 1 1 1 1 2s1 3 2 3h1v1l-2 3c-1-1-2-1-3-2h1c-2-1-2-2-3-4z" class="d"></path><path d="M542 236l3-3c0 1 1 1 1 2-1 1-1 3-1 4v1c-2-1-2-2-3-4z" class="P"></path><path d="M544 224h1s1-1 2-1l1-4c1 1 0 1 0 2v3c0 1 1 1 2 2h1c1 0 2 0 3 1h2c1-1 3-1 4-1h3v16l-1 2v7 1c-3 0-5-1-7 0l-3 3c1 1 2 1 3 1-1 1-2 1-3 0h-2v-1l6-9c2-2 5-5 6-7l-2-1-1-1c-2-1-3-1-5-1-1 0-1 0-2 1l-3-3-3-3v-1l-1-1 2-1 2 1-5-5z" class="B"></path><path d="M554 227h2c1-1 3-1 4-1l-4 4v1l-2-1v-3z" class="H"></path><path d="M555 252v-3h2l2-1v-1c1-2 2-2 3-3v7 1c-3 0-5-1-7 0z" class="D"></path><path d="M546 231v-1l-1-1 2-1 2 1 10 8c-2-1-3-1-5-1-1 0-1 0-2 1l-3-3-3-3z" class="R"></path><path d="M537 226c0 2 0 2 2 3l-1 2c0 3-1 4-2 6h2c0 1 0 2 1 4 1 1 1 1 1 2l-1 1h-1v7c0 2-1 4 0 6v1 1c-2 3-4 5-6 8v-1c-1-1-2-2-2-3v-8-4h-1v4l-1-1c-2 1-2 1-2 3h-2c0 2 0 3-1 4v-4h0l-1-2h-1v1c0 1 0 1-1 2h-1l-1-1c0 1-1 1-1 2h-1 0c0-2 0-3-1-5h1l-1-4v-1h0c0-1 1-2 2-2l7-7 13-14z" class="E"></path><path d="M538 237c0 1 0 2 1 4 1 1 1 1 1 2l-1 1h-1-1v1h-1v-1h-1l-1 5h-1v-3l-1 1-1 3h0l-1-2v-1c-1 1-1 2-2 3s-2 2-4 3c0 2 0 3-1 4h0 0l-1-2h-1v1c0 1 0 1-1 2h-1l-1-1c0 1-1 1-1 2h-1 0c0-2 0-3-1-5h1 0 1 2l1-1h1l1-2h2c0-1 1-3 2-4h2c1-2 2-3 4-5 0 1 0 0 1 1v-2c1-1 2-2 3-4h2z" class="P"></path><path d="M537 226c0 2 0 2 2 3l-1 2c0 3-1 4-2 6s-2 3-3 4v2c-1-1-1 0-1-1-2 2-3 3-4 5h-2c-1 1-2 3-2 4h-2l-1 2h-1l-1 1h-2-1 0l-1-4v-1h0c0-1 1-2 2-2l7-7 13-14z" class="x"></path><path d="M538 231c0 3-1 4-2 6s-2 3-3 4v2c-1-1-1 0-1-1-2 2-3 3-4 5h-2c-1 1-2 3-2 4h-2l-1 2h-1l-1 1h-2-1 0l-1-4v-1c1 1 1 1 2 1 0 1 0 1 1 2 3-2 7-5 8-8 1-2 3-3 4-4 3-4 5-7 8-9z" class="J"></path><defs><linearGradient id="AW" x1="511.162" y1="251.515" x2="495.001" y2="264.822" xlink:href="#B"><stop offset="0" stop-color="#aeacac"></stop><stop offset="1" stop-color="#dcdbda"></stop></linearGradient></defs><path fill="url(#AW)" d="M491 225h1c2 2 4 4 6 7l1 1c0 1 0 1 1 2l1-1c1-1 2 0 3 0l13 13c-1 0-2 1-2 2h0v1l1 4h-1c1 2 1 3 1 5v1c1 2 0 3 0 5v12c-1-2-2-4-4-5s-3-3-4-4h-1v1l6 7c2 2 6 5 6 8 1-1 2-2 2-3 3-3 5-6 8-8 1 0 2 2 3 3l-3 3c-2 3-5 6-7 8l-4 5-3-2s-2-3-3-3-1 0-2-1c-1 0-1-1-2-1h-1 0l-3-3c-1-2-2-3-4-5 0-1-2-2-3-4-1-1-2-3-3-4-2-1-3-2-4-3 0 1 0 1-1 2v-2c1 0 1 0 2-1-1-2-3-3-4-5h0l-2-4h0l-1-2c-1-2-1-3-2-4l-8-11c-1-1-2-1-2-3h2l7-6c1 1 2 1 2 2 1-1 2-1 3-1 2-1 4-4 5-6z"></path><path d="M522 287v-3c0-1 0-1 1-2s2-1 3-2v-1 2c1-1 2-2 3-2-2 3-5 6-7 8z" class="E"></path><path d="M497 269l3 3 12 15c-1 0-1 0-2-1-1 0-1-1-2-1h-1 0l-3-3c-1-2-2-3-4-5 0-1-2-2-3-4-1-1-2-3-3-4 1 0 1 0 2 1s1 1 2 1l-1-1v-1z" class="Q"></path><path d="M482 250c1 0 2 1 3 2v1c2 2 5 4 6 6 3 4 7 6 8 10v1l1 2-3-3v1l1 1c-1 0-1 0-2-1s-1-1-2-1c-2-1-3-2-4-3 0 1 0 1-1 2v-2c1 0 1 0 2-1-1-2-3-3-4-5h0l-2-4h0l-1-2c-1-2-1-3-2-4z" class="U"></path><path d="M485 256c4 4 9 8 12 13v1l1 1c-1 0-1 0-2-1s-1-1-2-1c-2-1-3-2-4-3 0 1 0 1-1 2v-2c1 0 1 0 2-1-1-2-3-3-4-5h0l-2-4z" class="D"></path><path d="M481 230c1 1 2 1 2 2 1-1 2-1 3-1-1 2-3 3-4 4l-1 1c0 1 0 2 2 2v1c1 0 2 1 3 2h-2c1 2 2 2 2 3 1 2 1 3 2 4 1 0 2-1 2-2v5l8 8 1 1 4 4v1l4 4 6 7c2 2 6 5 6 8-2-1-4-4-6-6l-13-15-25-26h0c-1 0-1 1-1 2-1-1-2-1-2-3h2l7-6z" class="K"></path><path d="M481 230c1 1 2 1 2 2-2 1-5 4-8 5h0c-1 0-1 1-1 2-1-1-2-1-2-3h2l7-6z" class="P"></path><path d="M491 225h1c2 2 4 4 6 7l1 1-6 9c0 1 1 1 1 2l1 1v2l-1 1c-1 0-2-1-2-2s1-1 0-2l-2 2c0 1-1 2-2 2-1-1-1-2-2-4 0-1-1-1-2-3h2c-1-1-2-2-3-2v-1c-2 0-2-1-2-2l1-1c1-1 3-2 4-4 2-1 4-4 5-6z" class="b"></path><path d="M488 232c1-1 3-3 4-5l1 1h0c-1 2-1 2 0 4h4c-1 1-2 2-3 2l-2-2h-1l-1 2h0-1l-1-2z" class="R"></path><path d="M498 232l1 1-6 9c0 1 1 1 1 2l1 1v2l-1 1c-1 0-2-1-2-2s1-1 0-2l-2 2c0 1-1 2-2 2-1-1-1-2-2-4h2 1c1-1 0-2 1-3 0-1 1-1 1-2l-2-2c0-1-1-1-2-1 0-1-1-1-1-1l2-3 1 2h1 0l1-2h1l2 2c1 0 2-1 3-2h1z" class="X"></path><defs><linearGradient id="AX" x1="502.296" y1="251.931" x2="497.073" y2="254.363" xlink:href="#B"><stop offset="0" stop-color="#9e9d9c"></stop><stop offset="1" stop-color="#c0bfbe"></stop></linearGradient></defs><path fill="url(#AX)" d="M499 233c0 1 0 1 1 2l1-1c1-1 2 0 3 0l13 13c-1 0-2 1-2 2h0v1l1 4h-1c1 2 1 3 1 5v1c1 2 0 3 0 5v12c-1-2-2-4-4-5s-3-3-4-4h-1v1l-4-4v-1l-4-4-1-1-8-8v-5l2-2c1 1 0 1 0 2s1 2 2 2l1-1v-2l-1-1c0-1-1-1-1-2l6-9z"></path><path d="M504 234l13 13c-1 0-2 1-2 2-1-1-3-4-3-4-1 0-1 1-2 0-1 0-1-2-2-2h-1v1c-1-2-1-2-1-3-1 1-1 0-1 1v1c-1-1-1-1-1-2h0-2c0-3 0-4-2-6h0l1-1c1-1 2 0 3 0z" class="D"></path><path d="M494 244h2c0-1 1-2 1-3l1 1h0c-1 2-1 3 0 4 1-1 1-2 1-3h1c0 3 0 6-1 8-1 0 0 0-1-1-1 2-1 4-1 6h1v3l-8-8v-5l2-2c1 1 0 1 0 2s1 2 2 2l1-1v-2l-1-1z" class="E"></path><path d="M503 252h-1v-1h1v-1-2l-1 1-1-2c0-1 0-2 1-3v3h1l1-1v1c1 0 2-1 2-1 0 1 0 4 1 5 0-1 1-4 2-5 0 2 0 2 1 3h1 1c0-1 0-1 1-1v2h2l1 4h-1c-1 0-1-1-2-1v-2h-1c-1 2-1 3-2 4h0c0-2 0-3-1-4h-1c-1 3-1 6-1 9v8 1l-4-4v-1l-4-4s2 0 2-1c2-1 0-5 2-7z" class="p"></path><path d="M504 253v1c1-1 1-2 1-3h1c1 3 1 10 0 13-1 0-1 1-3 1v-1l-4-4s2 0 2-1c2-1 0-5 2-7l1 1z" class="E"></path><path d="M503 252l1 1c-1 1-1 2-1 4v2c1 1 0 2 0 3v2l-4-4s2 0 2-1c2-1 0-5 2-7z" class="b"></path><path d="M507 268v-8c0-3 0-6 1-9h1c1 1 1 2 1 4h0c1-1 1-2 2-4h1v2c1 0 1 1 2 1 1 2 1 3 1 5v1c1 2 0 3 0 5v12c-1-2-2-4-4-5s-3-3-4-4h-1z" class="X"></path><path d="M512 261c1 0 1 0 2 1 0 1 0 3-1 5h0l-1-1v-5z" class="i"></path><path d="M559 295l1-3h1v2c1 1 1 2 2 2v6 17c0 4 1 18-1 21l-1-1c-1 2-2 5-3 7v1l-1 2-1 10v2c2 0 3 0 4 1h2l1 2c0 1 0 1-1 2-1 0-2-1-3-1 1 2 1 3 1 6l-1 1v1l1 1-2 1v4c-1 4 0 11 0 16v12 41c0 8-1 18 1 26 1 2 1 3 1 4v5 3c-1-2-2-2-3-3 0-2-1-3-1-5l-3 1c0-1 0-2-1-2 0-1-1-2-2-2v-2l-1-3c-1-1-1-2-2-3v-1l-1-1-3 2c0-1-1-3-2-4h-1l-3-4-10-10 1-1h0c-2-1-2-4-3-5l-1-1c-1 0-2 1-3 1l-1-2c-1-1-2-2-3-2l-1-2h0c-4-3-6-7-9-10h0l-7-6-4-4-4-4 1-1v-2c-1-1-1-2-1-3v-1-5-10-2-2-9-1-2c0-2 1-3 1-5h0v-2c0-1-1-2-3-2v-1l-1-2-3-2c1-1 1-1 0-2l-1-1h0v-4c2-1 3-1 4 0v1l1-1h-1c1-1 1-2 2-3h0l3 1 1-1c0-2 1-6 0-9h0l2-1v-3c0-1 0-1 1-2h1c0 1 1 2 2 3v1l1 1c1-2 1-4 1-6l-1-1-2-3 1-1 1 1 1-1v1-2l2-1 1 1c0-1-1-1-2-2s-1-1-1-3h0 5l1 1c2 0 4 0 5 1l3 2h0l-1-2 1-1 1 1 1-1c0-2 4-2 6-3 12-3 20-11 28-19l6-8z" class="M"></path><path d="M557 304c0 2 0 4-2 5-1 1-2 1-3 1l3-4 2-2z" class="G"></path><path d="M526 438l2 1v9c-2-1-2-4-3-5 1-2 1-3 1-5z" class="d"></path><path d="M520 342l1-3c1 0 1-1 2-2h0c0-1 0-2 2-2 0-1 1-1 1-1l1-1 1 1v2c-2 2-3 3-4 5-1 1-2 1-4 1z" class="G"></path><path d="M559 295l1-3h1v2c-1 4-2 7-4 10l-2 2v-2l-2-1 6-8z" class="E"></path><path d="M503 335l-1-1-2-3 1-1 1 1h1c1 1 0 1 1 1 2 0 2 2 4 2 1 1 2 1 3 2v1h1c1 1 1 2 2 2v2s1 1 1 2l-2-1v-2h-1l-2-1c-2-1-3-2-5-2 0-1-1-2-2-2h0z" class="D"></path><path d="M551 319h1l-1 3h0c-1 2-3 3-5 4-2 0-3 1-4 3-1 0-1 1-2 1l-2 1c1-5 7-9 11-13l1 1h1z" class="Y"></path><path d="M538 335c2 4 2 5 6 7 3 1 5 1 8 1l1 1-2 2h-1c-4 0-8-1-11-4l-1-2-1 12 1 3v6l-1 30v-1-10-30c0-3-1-8 0-11 1-1 1-3 1-4z" class="l"></path><path d="M503 335c1 0 2 1 2 2 2 0 3 1 5 2l2 1h1v2l2 1h2l1 6-1-1c-1 1-2 1-3 1-1-1-2-2-4-2v-2c-1 0-2-1-3-1h-1v-2h-3v-5-2z" class="Q"></path><path d="M503 335c1 0 2 1 2 2 1 2 1 3 3 4l3 1 2 4c1 1 1 1 1 3-1-1-2-2-4-2v-2c-1 0-2-1-3-1h-1v-2h-3v-5-2z" class="l"></path><path d="M503 337l3 3v1 1h-3v-5z" class="d"></path><path d="M563 302v17c-3 0-7 0-10 3v2l1 2h-1l-1-1v-2l-1-1h0l1-3h-1c0-3 1-4 3-6l3 3h3c1 0 1-1 2-2v-1l-3 1c-1 1-2 1-3 0 0-1-1-1-1-3l3-3h0l1-1s0-1 1-1l2-1c0-2-1-1-1-2l2-1z" class="y"></path><path d="M528 448h0c2 1 3 2 4 3s1 2 2 3l2 1h1v-2c-2 0-2 0-3-1 0-1 1-2 1-3-1-1-1-2-1-3 1-3 0-7 0-10 0-4 1-7 0-11v-1c-1-1 0 0 0-2 1-1 0-4 0-5 1-1 2-1 2-1v-7-1c0 2-1 3-1 5-1-1-1 0-1-1 1-2 0-6 0-7v-2h1c0 1 0 2 1 4 0-3-1-15 1-17v1 35l1 18v3 2 1 7l-1 2-10-10 1-1z" class="C"></path><defs><linearGradient id="AY" x1="538.112" y1="295.799" x2="519.913" y2="335.953" xlink:href="#B"><stop offset="0" stop-color="#c3c2c1"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#AY)" d="M553 303l2 1v2l-3 4c-5 5-9 9-14 12-8 5-15 8-22 15-1-2-3-3-5-5-1-1-4-2-5-3 0-1-1-1-2-2s-1-1-1-3h0 5l1 1c2 0 4 0 5 1l3 2h0l-1-2 1-1 1 1 1-1c0-2 4-2 6-3 12-3 20-11 28-19z"></path><path d="M563 319c0 4 1 18-1 21l-1-1c-1 2-2 5-3 7v1c-1 0-1-1-1-1l-3-1-3 1 2-2-1-1c-3 0-5 0-8-1-4-2-4-3-6-7v-4l2-1c1 0 1-1 2-1 1-2 2-3 4-3 2-1 4-2 5-4l1 1v2l1 1h1l-1-2v-2c3-3 7-3 10-3z" class="V"></path><path d="M558 332h2c-1 3-1 5-3 7h-1c0-2 1-3 2-5v-2z" class="t"></path><path d="M558 332c-2-1-2-4-3-6v-1c1-1 1-2 3-2 0 2 0 4 1 6 0 1 0 2 1 3h0-2z" class="j"></path><path d="M548 328c2 0 3 0 5 1 1 1 0 2 0 3l2 1c0 1 0 2-1 3 0 2-2 3-4 4-1 0-2 0-3-1-2-1-3-2-4-4 0-1 0-3 1-4 1-2 2-3 4-3z" class="M"></path><path d="M563 319c0 4 1 18-1 21l-1-1c-1 2-2 5-3 7v1c-1 0-1-1-1-1l-3-1-3 1 2-2-1-1c2-1 4-2 5-4 2-2 2-4 3-7h0v-1c0-3 1-8 0-10h-2c-2 1-3 1-5 3v-2c3-3 7-3 10-3z" class="T"></path><path d="M557 339l1 1-4 5-3 1 2-2-1-1c2-1 4-2 5-4z" class="h"></path><path d="M537 352l1-12 1 2c3 3 7 4 11 4h1l3-1 3 1s0 1 1 1l-1 2-1 10v2c2 0 3 0 4 1h2l1 2c0 1 0 1-1 2-1 0-2-1-3-1 1 2 1 3 1 6l-1 1-1-1c-1-2-1-3-3-4h-6v6 11 6 16c-1 2-1 4-3 6v7c-1 1-1 1-1 2s0 2-1 3v9 9 4h-1-1c-1-1-2-2-4-2h0l-1-18v-35l1-30v-6l-1-3z" class="j"></path><path d="M538 355c1 1 1 1 3 1l1-2c1 1 1 1 1 2 1 2 2 1 2 4 0 0-1 0-2 1v-1c-2 0-4 0-5 1v-6z" class="g"></path><path d="M544 364c6-1 10-1 15 1 1 2 1 3 1 6l-1 1-1-1c-1-2-1-3-3-4h-6c-1 0-2 0-3 1h-1l-1-4z" class="B"></path><path d="M537 352l1-12 1 2c3 3 7 4 11 4h1l3-1 3 1s0 1 1 1l-1 2-1 10s-1-1-2-1h0c0-3 1-3 2-5l-1-4c-2 0-2 0-3 1l-2-1v-2h-1l-2 1h-1c-1 0-2 0-3-1h-1c-1 2-2 3-4 5h-1z" class="t"></path><defs><linearGradient id="AZ" x1="550.093" y1="375.407" x2="544.964" y2="378.558" xlink:href="#B"><stop offset="0" stop-color="#6c6c69"></stop><stop offset="1" stop-color="#878786"></stop></linearGradient></defs><path fill="url(#AZ)" d="M543 369c-2 0-2 0-3-1 1-2 3-3 4-4l1 4h1c1-1 2-1 3-1v6 11 6c-1-1-1-3-1-4l-1-2c-1-1-1-3-2-5 0-1 0-2-1-3l-1 1-2-1v-1c1-1 1-1 2-1v-1-4z"></path><path d="M543 369c-2 0-2 0-3-1 1-2 3-3 4-4l1 4h1c1 2 1 4 0 6v1c-2-1-1-1-2-3 1-1 1-2 1-3-1-1-1-1-2 0z" class="Y"></path><path d="M543 377l1-1c1 1 1 2 1 3 1 2 1 4 2 5l1 2c0 1 0 3 1 4v16c-1 2-1 4-3 6v7c-1 1-1 1-1 2s0 2-1 3v9 9 4h-1-1c-1-1-2-2-4-2h0l-1-18c1 0 1 3 2 3s1 0 2 1v1c-1 1 0 1-1 1h-1v5h1c1-1 1-1 1-2h1c0-2 0-2 1-3v-3c-1 0-1 0-2-1v-2-11-10c0-3 1-6 1-9 1-5 1-14-1-20l2 1z" class="V"></path><path d="M538 444v-5c2 0 3 0 4-1v1 1c-1 1-2 1-1 2l2 2c0 1 0 1-1 2-1-1-2-2-4-2z" class="t"></path><path d="M544 413v11 9 9 4h-1-1c1-1 1-1 1-2l-2-2c-1-1 0-1 1-2v-1c2-2 1-7 1-9 0-6 0-11 1-17z" class="U"></path><path d="M543 377l1-1c1 1 1 2 1 3 0 10 1 20 0 29l-1 1h0v3c-2-3-1-7-1-11 0-8 1-16 0-24z" class="E"></path><path d="M545 379c1 2 1 4 2 5l1 2c0 1 0 3 1 4v16c-1 2-1 4-3 6v7c-1 1-1 1-1 2s0 2-1 3v-11-1-3h0l1-1c1-9 0-19 0-29z" class="P"></path><path d="M549 367h6c2 1 2 2 3 4l1 1v1l1 1-2 1v4c-1 4 0 11 0 16v12 41c0 8-1 18 1 26 1 2 1 3 1 4v5 3c-1-2-2-2-3-3 0-2-1-3-1-5l-3 1c0-1 0-2-1-2 0-1-1-2-2-2v-2l-1-3c-1-1-1-2-2-3v-1l-1-1-3 2c0-1-1-3-2-4h-1l-3-4 1-2v-7-1-2-3h0c2 0 3 1 4 2h1 1v-4-9-9c1-1 1-2 1-3s0-1 1-2v-7c2-2 2-4 3-6v-16-6-11-6z" class="c"></path><path d="M552 405h1l1-10v19c-2 1-2 0-3 0v-6h0l2-2-1-1z" class="g"></path><path d="M547 439l1-13c0-3-1-6 1-8 1 11-1 23 0 35v11 2c2 1 2 1 2 2v1l-2-2c-1-1-1-3-1-4l-1-1c-1-1-1-3-1-4 1-4 0-7-1-11l1-1v2c1-1 1-7 1-9z" class="h"></path><path d="M544 424c1-1 1-2 1-3s0-1 1-2v-7c2-2 2-4 3-6 0 4 1 8 0 12-2 2-1 5-1 8l-1 13c0 2 0 8-1 9v-2c-1-3-1-6-1-9h0l-1 5v-9-9z" class="d"></path><path d="M544 433c0 1 0 2 1 3v-5l1 7 1 1c0 2 0 8-1 9v-2c-1-3-1-6-1-9h0l-1 5v-9z" class="m"></path><path d="M544 442l1-5h0c0 3 0 6 1 9l-1 1c1 4 2 7 1 11 0 1 0 3 1 4l1 1c0 1 0 3 1 4v3c-1-1-1-2-2-3v-1l-1-1-3 2c0-1-1-3-2-4h-1l-3-4 1-2v-7-1-2-3h0c2 0 3 1 4 2h1 1v-4z" class="X"></path><path d="M538 444c2 0 3 1 4 2h1l1 6h-1-1c-1-1-1-1-2-1l-2-1v-1-2-3h0z" class="c"></path><path d="M544 442l1-5h0c0 3 0 6 1 9l-1 1c1 4 2 7 1 11 0 1 0 3 1 4l1 1c0 1 0 3 1 4v3c-1-1-1-2-2-3v-1l-1-1-3 2c0-1-1-3-2-4h-1l-3-4 1-2c1 3 3 5 6 7v-18-4z" class="o"></path><path d="M544 458c0-3 0-8 1-11 1 4 2 7 1 11h-2z" class="s"></path><path d="M544 458h2c0 1 0 3 1 4l1 1c0 1 0 3 1 4v3c-1-1-1-2-2-3v-1l-1-1-3 2c0-1-1-3-2-4h0c1 1 2 1 3 1v-6z" class="J"></path><path d="M549 367h6c0 1 0 2 1 4s0 9 0 12c-2-2-2-4-2-6v18l-1 10h-1c-1 0-1 1-2 0l-1-21h0v-11-6z" class="n"></path><path d="M549 367h6c0 1 0 2 1 4s0 9 0 12c-2-2-2-4-2-6s1-7-1-8c-1 0-2 0-3 2l-1 13h0v-11-6z" class="T"></path><path d="M555 367c2 1 2 2 3 4l1 1v1l1 1-2 1v4c-1 4 0 11 0 16v12 41c0 8-1 18 1 26 1 2 1 3 1 4v5 3c-1-2-2-2-3-3 0-2-1-3-1-5l-3 1c0-1 0-2-1-2 0-1-1-2-2-2v-2l-1-3v-3l2 2v-1c0-1 0-1-2-2v-2-11h0l1-1v-3c4-6-2-16 3-21h0c-1-1-2-2-3-4v-2-1l3-2c0-1 0-2 1-3l2 1v-34c0-3 1-10 0-12s-1-3-1-4z" class="l"></path><path d="M553 419c0-1 0-2 1-3l2 1v9-1l-1-1c-1-1-1-1-2-3v-2h0z" class="U"></path><path d="M553 419h0v2c1 2 1 2 2 3l1 1v1 6c-1-1-2-1-2-3v-1h-1 0c-1-1-2-2-3-4v-2-1l3-2z" class="V"></path><path d="M550 422c2 0 2 0 3 2 1 1 0 2 0 3v1c-1-1-2-2-3-4v-2z" class="T"></path><path d="M557 402v-27h1v4c-1 4 0 11 0 16v12c0-2-1-4-1-5z" class="s"></path><path d="M557 402c0 1 1 3 1 5v41c0 8-1 18 1 26 1 2 1 3 1 4v5 3c-1-2-2-2-3-3 0-2-1-3-1-5 1-1 0-4 1-5v-71z" class="J"></path><path d="M557 473h0v6c1 0 1-4 1-4v1l2 2v5 3c-1-2-2-2-3-3 0-2-1-3-1-5 1-1 0-4 1-5z" class="Q"></path><path d="M553 428h1v1c0 2 1 2 2 3v29 7 6c-1-1-3-4-5-5v-1c0-1 0-1-2-2v-2-11h0l1-1v-3c4-6-2-16 3-21z" class="g"></path><path d="M549 453l1-1v-3h1 1l1 2c-1 2-1 4 0 7 0 1 0 2 1 3h1v1l-2 1h-1s1 0 1 1c1 0 2 1 2 2 1-2 0-3 0-5h1v7 6c-1-1-3-4-5-5v-1c0-1 0-1-2-2v-2-11h0z" class="i"></path><path d="M549 453h0c0 3 1 7 2 10l1 1c1 1 3 2 4 4v6c-1-1-3-4-5-5v-1c0-1 0-1-2-2v-2-11z" class="E"></path><path d="M503 335h0v2 5h3v2h1c1 0 2 1 3 1v2c2 0 3 1 4 2 1 0 2 0 3-1l1 1-1-6c1 0 1 0 3-1h0c2 0 3 0 4-1 1-2 2-3 4-5l-1 65 1 38-2-1c0 2 0 3-1 5l-1-1c-1 0-2 1-3 1l-1-2c-1-1-2-2-3-2l-1-2h0c-4-3-6-7-9-10h0l-7-6-4-4-4-4 1-1v-2c-1-1-1-2-1-3v-1-5-10-2-2-9-1-2c0-2 1-3 1-5h0v-2c0-1-1-2-3-2v-1l-1-2-3-2c1-1 1-1 0-2l-1-1h0v-4c2-1 3-1 4 0v1l1-1h-1c1-1 1-2 2-3h0l3 1 1-1c0-2 1-6 0-9h0l2-1v-3c0-1 0-1 1-2h1c0 1 1 2 2 3v1l1 1c1-2 1-4 1-6z" class="r"></path><path d="M517 375c1 0 1-1 2-2s1-2 2-3c1-2 2-1 4-1l-1 2-1 1h0c-2 1-3 2-4 3h-2z" class="s"></path><path d="M520 342c2 0 3 0 4-1 0 3-3 4-3 8h-1c-1 1-1 3-2 5v-2-3l-1-6c1 0 1 0 3-1h0z" class="C"></path><path d="M520 342c-1 3-1 8-2 10v-3l-1-6c1 0 1 0 3-1z" class="H"></path><path d="M515 362h1c1 0 2 1 4 2h0c1 2 1 3 0 5-1 1-2 3-3 4l-3-1c-2 0-3-1-4-2s-1-2-1-3v-3c1 0 1 0 1-1 1 0 3-1 4-1h1z" class="R"></path><path d="M514 362h1c2 1 2 1 3 3-1 1-1 2-2 2h-1l-1-2v-3z" class="g"></path><path d="M510 363c1 0 3-1 4-1v3l-1 4s-1 0-1 1l-3-3v-3c1 0 1 0 1-1z" class="j"></path><path d="M515 362h1c1 0 2 1 4 2h0c1 2 1 3 0 5-1 1-2 3-3 4l-3-1c-2 0-3-1-4-2s-1-2-1-3l3 3h4c1-1 2-2 3-4l-1-1c-1-2-1-2-3-3z" class="B"></path><path d="M525 369v-1c-1-4 0-8 0-12 0-5-1-12 2-17v17 32 13l1 38-2-1v-7l-2-1c-1-1-1-1-1-3h1v-2-3l-2-1s0-1-1-2v-5h-1v-1c1-1 2 0 3 0v-2c1-2 1-4 1-6h0c-1-1-1-2-1-3 1-2 1-5 1-7v-3l2-1v-1c-1-1-1-1-1-3l1-1c0-1-1-2-1-4-1-2 0-4-1-6 1-2 1-4 0-5l1-2z" class="b"></path><path d="M524 405h0c-1-1-1-2-1-3 1-2 1-5 1-7v-3l1 4v8c1 2 1 2 1 5v7c-1 2-1 3-1 4 0-1-1-2-1-3l1-1c0-2-1-2-2-3v-2c1-2 1-4 1-6z" class="k"></path><path d="M520 414v-1c1-1 2 0 3 0 1 1 2 1 2 3l-1 1c0 1 1 2 1 3 1 3 1 7 1 11l-2-1c-1-1-1-1-1-3h1v-2-3l-2-1s0-1-1-2v-5h-1z" class="h"></path><path d="M521 414l1 1c1 2 1 3 1 5l1 2-2-1s0-1-1-2v-5z" class="w"></path><path d="M510 370c1 1 2 2 4 2l1 3c1 1 1 0 2 0h2c1-1 2-2 4-3h0l1-1c1 1 1 3 0 5 1 2 0 4 1 6 0 2 1 3 1 4l-1 1c0 2 0 2 1 3v1l-2 1v3c0 2 0 5-1 7 0 1 0 2 1 3h0c0 2 0 4-1 6v2c-1 0-2-1-3 0v1l-1 1c-2-1-3-1-4-1 0-1-1-2-1-3-1-1-2-1-3-1l-1-2-1-1h-1v-1h0v-4-1-1c1-2 3-3 2-5v-3c-1-1-1-3-1-4v-2h0v-4-1c0-1 2-3 3-3h0c0-1 0-2-1-3-1-2-1-3-1-4v-1z" class="m"></path><path d="M509 388c2 1 3 2 4 3v2l1 2h-1l-1 1c1 0 1 0 2 1-2 0-3 1-4 2s-1 1-1 2h-1v-1c1-2 3-3 2-5v-3c-1-1-1-3-1-4z" class="P"></path><path d="M524 371c1 1 1 3 0 5h0v10h-2l-1 1h0v-2l1-2v-1-3c1-2 1-5 1-7l1-1z" class="v"></path><path d="M519 381v-1l3 3-1 2v2h0c0 2-1 3-2 4h-3c-1 1-2 0-3 0-1-1-2-2-4-3v-2l2 1c2 1 4 1 6 0 1-1 1-2 2-4v-2z" class="I"></path><path d="M511 380c1-1 3-1 5 0 1 0 2 1 3 1h0v2c-1 2-1 3-2 4-2 1-4 1-6 0l-2-1h0v-4l2-2z" class="g"></path><path d="M511 380c1-1 3-1 5 0 1 0 2 1 3 1h0v2c-2 0-2 1-3 2h-1v-2h-1l-2 1-1-1v-3z" class="j"></path><path d="M510 370c1 1 2 2 4 2l1 3c1 1 1 0 2 0h2c1-1 2-2 4-3h0c0 2 0 5-1 7v3 1l-3-3v1h0c-1 0-2-1-3-1-2-1-4-1-5 0l-2 2v-1c0-1 2-3 3-3h0c0-1 0-2-1-3-1-2-1-3-1-4v-1z" class="l"></path><path d="M509 401c0-1 0-1 1-2s2-2 4-2c1 0 3 1 5 1h0c1 2 1 2 1 5 1 0 1 0 2 1l-3 3v1l1-1c1-1 2-1 4-2 0 2 0 4-1 6v2c-1 0-2-1-3 0v1l-1 1c-2-1-3-1-4-1 0-1-1-2-1-3-1-1-2-1-3-1l-1-2-1-1h-1v-1h0v-4-1h1z" class="o"></path><path d="M520 407c1-1 2-1 4-2 0 2 0 4-1 6h-4l3-3-2-1z" class="l"></path><path d="M519 398h0c1 2 1 2 1 5 0 2-2 3-3 4 1 2 1 1 1 2l-1 2c-1-1-2 0-2-1-1-1-1-1-1-2h0c-2 0-3-1-4-2v-1c2 1 3 1 5 1 1 0 3-2 4-3v-5z" class="Y"></path><path d="M509 401c0-1 0-1 1-2s2-2 4-2c1 0 3 1 5 1v5c-1 1-3 3-4 3-2 0-3 0-5-1h0c0-1-1-3-1-4z" class="W"></path><path d="M508 381h1v1 4h0v2c0 1 0 3 1 4v3c1 2-1 3-2 5v1 1 4h0v1h1l1 1 1 2c1 0 2 0 3 1 0 1 1 2 1 3 1 0 2 0 4 1l1-1h1v5c1 1 1 2 1 2l2 1v3 2h-1c0 2 0 2 1 3l2 1v7c0 2 0 3-1 5l-1-1c-1 0-2 1-3 1l-1-2c-1-1-2-2-3-2l-1-2h0c-4-3-6-7-9-10h0l-7-6-4-4c1-1 1-3 1-4l1-2c0-1 1-2 1-3 1-1 0-1 1-2 0-1 1-1 1-2h1c-1-1-1-1-1-2-1-1-1-3-1-4 1 0 2 1 2 3h0v-4h1v-12-1c2 0 3-1 4-2l1-1z" class="s"></path><path d="M516 418c-1 0-2-1-4-1h1c3 0 4-1 6 1 1 1 2 2 2 4h0c0 1 1 2 1 3-1 3-2 5-4 7h-3-1l5-3s1-1 1-2l-2-1 1-3c1-1 1-1 0-2 0-2-1-2-3-3z" class="F"></path><path d="M504 420c1 2 1 4 3 6 1-1 1-1 1-2 0-2 0-3 1-5v1 4c1 1 2 3 4 3s3 0 5-1l2 1c0 1-1 2-1 2l-5 3-3-3v-2l-1 1-3-1h0l-7-6 4-1z" class="J"></path><path d="M510 408l1 2c1 0 2 0 3 1 0 1 1 2 1 3 1 0 2 0 4 1l1-1h1v5c1 1 1 2 1 2l-1 1c0-2-1-3-2-4-2-2-3-1-6-1h-1c2 0 3 1 4 1-3 0-4 0-7 2v-1-1l3-2c-2-2-3-4-4-6l2-2z" class="d"></path><path d="M511 410c1 0 2 0 3 1 0 1 1 2 1 3h-3c0-2 0-3-1-4z" class="m"></path><path d="M524 430l2 1v7c0 2 0 3-1 5l-1-1c-1 0-2 1-3 1l-1-2c-1-1-2-2-3-2l-1-2h0l1-2h2l1 1c1-1 4-3 4-6z" class="o"></path><path d="M521 438c1 1 2 2 3 4-1 0-2 1-3 1l-1-2c0-1 1-2 1-3z" class="C"></path><path d="M516 437l1-2h2l1 1 1 2c0 1-1 2-1 3-1-1-2-2-3-2l-1-2h0z" class="K"></path><path d="M516 418c2 1 3 1 3 3 1 1 1 1 0 2l-1 3c-2 1-3 1-5 1s-3-2-4-3v-4c3-2 4-2 7-2z" class="V"></path><path d="M515 420c1 0 2 1 4 2v1c-1 1-1 1-2 1s-1-1-1-1l-1-1h-1l1-2z" class="W"></path><path d="M511 424v-1h-1v-1c2-2 2-2 5-2l-1 2-1 2h-2z" class="n"></path><path d="M514 422h1l1 1s0 1 1 1 1 0 2-1h0l-1 3c-2 1-3 1-5 1s-3-2-4-3h2 2l1-2z" class="U"></path><path d="M508 381h1v1 4h0v2c0 1 0 3 1 4v3c1 2-1 3-2 5v1 1 4h0v1h1l1 1-2 2c1 2 2 4 4 6l-3 2v1c-1 2-1 3-1 5 0 1 0 1-1 2-2-2-2-4-3-6l-4 1-4-4c1-1 1-3 1-4l1-2c0-1 1-2 1-3 1-1 0-1 1-2 0-1 1-1 1-2h1c-1-1-1-1-1-2-1-1-1-3-1-4 1 0 2 1 2 3h0v-4h1v-12-1c2 0 3-1 4-2l1-1z" class="p"></path><path d="M508 400v1 1 4h0v1h1l1 1-2 2c1 2 2 4 4 6l-3 2h-1l1-2-1-2c0-1 0-2-1-3h-2-1c-1-2 0-7 0-9h3l1-2z" class="f"></path><path d="M500 398c1 0 2 1 2 3h0v-4h1c0 7-1 15 1 23l-4 1-4-4c1-1 1-3 1-4l1-2c0-1 1-2 1-3 1-1 0-1 1-2 0-1 1-1 1-2h1c-1-1-1-1-1-2-1-1-1-3-1-4z" class="D"></path><path d="M508 381h1v1 4h0v2c0 1 0 3 1 4v3c1 2-1 3-2 5l-1 2h-3v-8l-1-9v-1c2 0 3-1 4-2l1-1z" class="p"></path><path d="M503 384c2 0 3-1 4-2v1c0 1 0 2-1 3v2c1 1 0 3 0 4l-2 2-1-9v-1z" class="X"></path><path d="M504 394l2-2c2 2 3 2 4 3 1 2-1 3-2 5l-1 2h-3v-8z" class="U"></path><path d="M503 335h0v2 5h3v2h1c1 0 2 1 3 1v2c2 0 3 1 4 2 1 0 2 0 3-1l1 1v3 2c1 1 1 4 1 6 0 1 1 2 1 4h0c-2-1-3-2-4-2h-1-1c-1 0-3 1-4 1 0 1 0 1-1 1v3c0 1 0 2 1 3v1c0 1 0 2 1 4 1 1 1 2 1 3h0c-1 0-3 2-3 3h-1l-1 1c-1 1-2 2-4 2v1 12h-1v4h0c0-2-1-3-2-3 0 1 0 3 1 4 0 1 0 1 1 2h-1c0 1-1 1-1 2-1 1 0 1-1 2 0 1-1 2-1 3l-1 2c0 1 0 3-1 4l-4-4 1-1v-2c-1-1-1-2-1-3v-1-5-10-2-2-9-1-2c0-2 1-3 1-5h0v-2c0-1-1-2-3-2v-1l-1-2-3-2c1-1 1-1 0-2l-1-1h0v-4c2-1 3-1 4 0v1l1-1h-1c1-1 1-2 2-3h0l3 1 1-1c0-2 1-6 0-9h0l2-1v-3c0-1 0-1 1-2h1c0 1 1 2 2 3v1l1 1c1-2 1-4 1-6z" class="L"></path><path d="M497 345h1v3c0 2 1 3 0 4h-1v-2c-1-2-1-3 0-5z" class="s"></path><path d="M499 390l2 1v3l-1 1v2-1l1 1-1 1c0 1 0 3 1 4 0 1 0 1 1 2h-1c0 1-1 1-1 2-1 1 0 1-1 2v-18z" class="e"></path><path d="M493 375l2-2v1c1 0 1 1 2 2 1 0 1 1 1 2 0 2 1 4 0 7-1 1 0 3-1 5v4c-1-2 0-3-1-5 0-1 0-1-1-2 2-3 2-6 2-9l-1-1c-1 1-2 1-3 1h-1v-1-2h1z" class="r"></path><path d="M503 335h0v2 5h3v2 1l-3 3v1l-2 1v1h-1c0-2 0-2 1-3v-3h0l-1-1c0-2 0-1 1-2v-2l1 1c1-2 1-4 1-6z" class="B"></path><path d="M503 342h3v2 1l-3 3v-6z" class="p"></path><path d="M491 351l3 1 1-1v4l1 2h0l-1 1v4h2l1 1-2 2h1v4l-1 1s0 1-1 2l-2 1v2h-1c0-2 1-3 1-5h0v-2c0-1-1-2-3-2v-1l-1-2-3-2c1-1 1-1 0-2l-1-1h0v-4c2-1 3-1 4 0v1l1-1h-1c1-1 1-2 2-3h0z" class="I"></path><path d="M493 363l2 4c-1 1-1 2-2 3v-2c0-1-1-2-3-2 2-1 2-2 3-3z" class="B"></path><path d="M491 351l3 1c-2 1-3 1-3 3l1 1c1 2 0 4 0 6l1 1c-1 1-1 2-3 3v-1l-1-2-3-2c1-1 1-1 0-2l-1-1h0v-4c2-1 3-1 4 0v1l1-1h-1c1-1 1-2 2-3h0z" class="J"></path><path d="M485 354c2-1 3-1 4 0v1h0c0 1-1 4 0 5s0 2 0 3l-3-2c1-1 1-1 0-2l-1-1h0v-4z" class="v"></path><path d="M485 354c2-1 3-1 4 0v1h0c-1 0-1 1-3 1v1l-1 1v-4zm7 24h1c1 0 2 0 3-1l1 1c0 3 0 6-2 9 1 1 1 1 1 2 1 2 0 3 1 5v13 6c0 1 0 3-1 4l-4-4 1-1v-2c-1-1-1-2-1-3v-1-5-10-2-2-9z" class="h"></path><path d="M506 344h1c1 0 2 1 3 1v2c2 0 3 1 4 2 1 0 2 0 3-1l1 1v3 2c1 1 1 4 1 6 0 1 1 2 1 4h0c-2-1-3-2-4-2h-1-1c-1 0-3 1-4 1 0 1 0 1-1 1v3c0 1 0 2 1 3v1c0 1 0 2 1 4 1 1 1 2 1 3h0c-1 0-3 2-3 3h-1l-1 1c-1 1-2 2-4 2v1 12h-1v4h0c0-2-1-3-2-3l1-1-1-1v1-2l1-1v-3l-2-1c0-1 0-2 1-3l1-1c-1-3-1-5-1-8v-14-3-5c0-2 1-3 1-5v-1l2-1v-1l3-3v-1z" class="K"></path><path d="M503 368l1 1h0c0 2-1 9 0 11h1 1 1l1 1-1 1c-1 1-2 2-4 2v-16z" class="U"></path><path d="M503 364c1-2 1-4 1-5l3-1v2 3h2 0 1 0c0 1 0 1-1 1v3c0 1 0 2 1 3v1c0 1 0 2 1 4 1 1 1 2 1 3h0c-1 0-3 2-3 3h-1l-1-1h-1-1-1c-1-2 0-9 0-11h0l-1-1v-4z" class="N"></path><path d="M507 360v3h2 0 1 0c0 1 0 1-1 1-2 2-1 3-2 5 0 0 0 2-1 3l-1-2c1-1 1-2 1-4v-1c-1-2 0-3 1-5z" class="c"></path><path d="M506 372c1-1 1-3 1-3 1-2 0-3 2-5v3c0 1 0 2 1 3v1c0 1 0 2 1 4 1 1 1 2 1 3h0c-1 0-3 2-3 3h-1l-1-1h-1-1l1-1c0-1 0-2 1-2v-2-1c0-1 0-2-1-2z" class="E"></path><path d="M506 344h1c1 0 2 1 3 1v2c2 0 3 1 4 2 1 0 2 0 3-1l1 1v3 2c1 1 1 4 1 6 0 1 1 2 1 4h0c-2-1-3-2-4-2h-1-1c-1 0-3 1-4 1h0-1 0-2v-3-2l-3 1c0 1 0 3-1 5v-15-1l3-3v-1z" class="b"></path><path d="M512 359l2-1v-3c1 1 1 0 1 1s1 2 1 3l1 2c-2 0-5 0-7 1v1h-1 0l1-2-1-1 1-1c0 1 0 1 1 1v-2l1 1z" class="R"></path><path d="M508 356c1-1 0-4 2-6v1c0 2 0 2 1 3h0 1c1 2 1 3 0 5l-1-1v2c-1 0-1 0-1-1l-1 1 1 1-1 2h-2v-3-2l1-2z" class="T"></path><path d="M506 344h1c1 0 2 1 3 1v2c2 0 3 1 4 2 1 0 2 0 3-1l1 1v3 2c1 1 1 4 1 6l-2-2v-1-1s1 0 0-1c0-2-1-4-2-4l-1 1h-1l-1-1h-2v-1c0-1 0-2-1-3l-2 1-1-2v-1-1z" class="k"></path><path d="M506 345v1l1 2 2-1c1 1 1 2 1 3-2 2-1 5-2 6l-1 2-3 1c0 1 0 3-1 5v-15-1l3-3z" class="X"></path><path d="M506 346l1 2 2-1c1 1 1 2 1 3-2 2-1 5-2 6-2-2-1-5-1-7-1-1-1-2-1-3z" class="q"></path><path d="M490 410l2 3 4 4 4 4 7 6h0c3 3 5 7 9 10h0l1 2c1 0 2 1 3 2l1 2c1 0 2-1 3-1l1 1c1 1 1 4 3 5h0l-1 1 10 10 3 4h1c1 1 2 3 2 4l3-2 1 1v1c1 1 1 2 2 3l1 3v2c1 0 2 1 2 2 1 0 1 1 1 2l3-1c0 2 1 3 1 5 1 1 2 1 3 3v-3c1-3 1-6 2-8v21c1 3 4 4 6 7 1 1 4 4 6 5l-2 3 7 8 7 7 1 2 2 2 6 7 6 7 2 2 5 5h-3c-2 1-4 3-6 4 0 0-2-1-2 0-2 0-4 4-6 5l-1 1-1 1c-1 0-1 1-2 2l-2 3c-1 1-3 2-3 4 0 0 0 1-1 2 0-1-1-2-1-2h-1l-1-1v-1h-1l-3 3-12-12-3-2-1-1-1-2c-2 1-3 1-4 3 0 0-1 1-1 2 2 2 3 4 5 6l-2 2h-2l-1-2c-1-2-3-3-4-5l-1-1-1 1-1 1v1s0 1-1 2h0c-1 2-1 3-1 5 0 1 0 1 1 2v1l-1 1v1c-1 1 0 2 0 3l1 1v1l-1 1-1-1v-2c-2-2 0-4-1-7v-3-5l1 1 1-1v-1c0-1-2-1-2-3 1-1 1-1 1-2l-2-2v1c-1 1-1 1-1 3l-1-1h-1l-1 1v-1h-2v1l-1-1-3-6c0-2-1-4-1-5 0-2-1-2-1-3 1-3 1-4 0-7h-2v245l-1 39v2 38l-1 3v-1c0-2 0-3-1-5 0-2 0-5-1-8v1c0 1 0 2-1 4v1c0 1 1 1 1 3v3c-1-1-2-3-4-3h-1 0c-1 2-2 7-2 9v3c-1-2-1-3-1-5v-8-1l-2 1c0 2 0 4-1 6h0v2h-1-1c-1 1-3 2-4 2 0 2 0 3-1 5l1 3c-1 3 0 11-1 12v-1l-1-1c-1 2-1 3-2 5v1 7c0-3 0-6-1-8v-5c1-1 0-1 0-2-1-1 0-2-1-3 1-1 1-3 1-4v-9h0v-10c-3-2-1-4-2-7-1-2 0-3-1-5h0v-1c1-1 1-2 0-3h0c-1-2-1-2-1-3 0-2-1-3-1-5 1-1 1-1 0-2 0-3 0-5-1-7v12c0 8 1 17-1 25 0 2 0 3 1 5v1h-1v2h-1c-1-4 0-9-1-13-1 1-1 3-1 4v2c0 2 1 5 0 7-1 0-1-1-1-1l-1 2c-1-2 0-6 0-8h-3v-7c-11 17-31 27-50 33-17 4-35 5-53 6-37 0-72-5-108-9l-69-7h121v-12h0v1l2 1c2 0 4-2 6-3 2-2 4-3 7-3v3h1c1-1 0-1 0-2s1-1 1-2c2-1 5-1 7-3 2 0 3-2 5-2v-4c2-2 7-3 10-5 6-3 11-7 16-12 7-5 12-13 17-20l2-2c1-1 1-2 2-3 2-4 3-7 5-10 1-1 1-2 2-3v-1l-2-1c-4 0-9 0-13 1-3 0-5 1-8 0-1 0-2 0-3-1l1-1-1-1h-1c-2 1-12 0-13-1v-1c-4 0-9-1-14-2l57-1s1-1 1-2v-2c0-1 1-2 1-3h0c0-2 0-2 1-4h0v-3c2-1 5-1 7 0h37c0-2-1-7 0-8 0-1 1-1 1-2h1c2 1 6 4 9 3-1-1-1-2-1-3v-27-60-135-73c0-7-1-15 0-22-2-1-4-1-6-1l-1 1c-1-1-2-1-3-1l-3 1c-1-2 1-16 1-19l-1-1h0l2-1c1 0 2-1 4 0h1c2 0 4 1 7 2v-1h2c1 1 1 1 1 3l1-1 3-2 1-2-1-1c0-1 0-1-1-2l1-1c-1-3 0-6 1-9l2-2c1 1 2 2 4 2v-3l2 3h2c1-1 1-2 2-3z" class="M"></path><path d="M496 525v-3l1 1c1 1 1 1 2 1-1 1-1 2-3 2v-1z" class="B"></path><path d="M470 848h1c0 2 1 2 1 4h-1-1s-1 0-1-1 0-2 1-3zm30-328c1 1 1 2 1 4h-2c-1 0-1 0-2-1l1-1 2-2z" class="C"></path><path d="M521 523c-2 0-4 0-5-1h-1c1-1 1-1 2-1 1-1 1-1 1-2l1 1 2 3z" class="G"></path><path d="M486 565h2v7l-2-1c-1 0 0 0-1-1l1-1v-1l1-1-1-2z" class="H"></path><path d="M499 524h2v4h-2c-1 0-2 0-3-1v-1c2 0 2-1 3-2z" class="D"></path><path d="M500 520l1-1 1 1h1v2 2c0 2-1 3-1 5l-1-1v-4c0-2 0-3-1-4z" class="H"></path><path d="M467 793c-1-2 0-4 0-6v-3c0 1 1 2 1 3h1c1 0 2-1 3 0l-1 1c-1 1-2 3-4 4v1h0zm-79 88h1c1 3 4 7 4 10v-1l-1 1h-1c-1-3-4-6-3-10z" class="G"></path><path d="M503 520h6s0-1 1-1c0 0 0 1 1 0h4v1c-1 1-1 2-2 3l-1-1c0-1 0-1-1-1h-3c-2 0-3 1-5 1v-2z" class="B"></path><path d="M469 787c0-3 1-5 2-7 0-1 1-2 1-3 1-1 0-4 2-5v1h0c-1 4-1 8-2 12v2c-1-1-2 0-3 0z" class="H"></path><path d="M472 842c1 1 3 3 3 5 0 1-1 1-2 2l-2-1-2-2c1-1 2-3 3-4z" class="x"></path><path d="M537 558c0-1-1-2-1-3 2-3 1-7 3-10 1 3 1 7 1 10v1c-1 1-1 1-1 3l-1-1h-1z" class="B"></path><path d="M494 635c0-2-1-6-1-8 0-1 3-4 4-5l2 3-1 1-1 2h-2c0 2 1 2 2 3-2 0-2 1-3 2v1 1z" class="v"></path><path d="M477 698c0-2-1-4-1-6v-1h1c1-1 0-2 0-3l1-1v-1h0v-1c0-1 0-1 1-2h1c-1 4-2 8-1 12 1 2 1 2 0 3h-2z" class="K"></path><path d="M493 586c0 2 1 3 1 5 1 2-1 4 2 5h0c-1 2-1 3-1 5h1l1-1c0 1 0 1 1 2l-2 3-3-3v-16z" class="P"></path><path d="M481 711h0l-1 3v1c1 2 1 3 4 5h0v1l-2-1c-1-1-1-2-2-3l-1 1-1 1c-1-2-1-4-1-5 0-4 1-6 4-8 0 1 0 3-1 4l1 1z" class="O"></path><path d="M488 749c-1 0-2 1-3 1s-2 0-3-1c-2-2-4-6-4-8s1-3 2-5c2-1 3-1 5-1-2 1-3 3-4 4v1c-1 2-1 4 0 6 1 1 2 1 4 2v1c1-1 2-1 3-1v1z" class="B"></path><path d="M485 512c0 1 0 1 1 1l14-2 1 1c-1 0-2 0-3 1h-2v1h5l-14 1c-2 1-4 1-6 0h0c1-1 3-2 4-3z" class="f"></path><path d="M492 652l1-1-1 5h-2l-2 2h-1l-1 2s-1-1-1 0c-1 0-2 0-2 1-1-1 0-1-1-2h0c1-2 0-1 0-3-1 0 0-2 0-3l4 1v2h1v-2c2 0 3-1 5-2z" class="C"></path><path d="M488 863c0-3-1-6 0-8s1-5 2-7h1 0c1 2 0 7 0 9-1 1-1 3-1 4v2c0 2 1 5 0 7-1 0-1-1-1-1l-1 2c-1-2 0-6 0-8z" class="D"></path><path d="M489 721l-1 1h3l3-3v-1c1-2 1-3 1-5 0-1 0 0 1-1l2-1c2 3 2 6 2 10v2l-3 2c0-1 0 0 1-1v-1-6-4h-2v1 1 1c0 2 0 3-1 5s-3 4-5 5c-1 0-2-1-3-1l1-1-3-3h1 3z" class="e"></path><path d="M484 611h1c-1 2-1 3 0 5 1 1 2 2 3 2h0l4 2c2-1 3-2 5-4 1-1 0-1 0-3 1-1 1-1 1-2 1 1 1 2 1 4v1c-2 2-3 5-5 6s-3 1-5 1c-2-1-4-3-5-4-1-3-1-5 0-8z" class="s"></path><path d="M467 793h0v-1c2-1 3-3 4-4l-1 8c-1 1 0 3-1 4 0 3-1 6-2 9h0c-1 2-1 2-3 3l3-12v-1h-5l2-1v-1c1 0 2-2 3-4z" class="e"></path><path d="M494 798c1 3 1 6 1 9 1 3 0 5 1 8 0 5 2 13 0 18l1 1 1 1-1 1v6h0c-1-2-1-2-1-3 0-2-1-3-1-5 1-1 1-1 0-2 0-3 0-5-1-7v-27z" class="r"></path><path d="M490 784c1 4 1 9 1 14v25 10 5l-1 1c1 1 1 5 1 7h-1v-62z" class="D"></path><path d="M480 620c1 0 2 3 3 4s2 2 2 3v4l-4 1h-1c-1 0-2 0-3-1v-1-3-1c1-3 2-4 3-6z" class="a"></path><path d="M484 532l1-1h3 0c1 0 2 0 3 1h2v-1c-1-2-1-3-1-5 1 0 1-1 1-2l1-2h1l1 3v1 1 3l1 3c-1 1-2 1-3 1v3h-1v4h-2l-2-4h1 0c-1-2-2-3-4-5h-2z" class="O"></path><path d="M494 534l-1-1c1-3 1-5 1-7h1v4h1l1 3c-1 1-2 1-3 1z" class="I"></path><path d="M486 532c3 1 4 1 6 3 0 1 0 1 1 2v4h-2l-2-4h1 0c-1-2-2-3-4-5z" class="b"></path><path d="M496 530h2c1 1 1 2 1 3v2h-2c-1 1-2 2-2 3v5 3c0 1 1 3 1 4l1 1c1 2 0 5 0 7-2 1-2 3-4 4v8 13c-1-4-1-9-1-13s0-9 1-13c0-1 1-2 1-3 1-2 1-4 0-5l-1-1 1-1c0-1 0-4-1-6v-4h1v-3c1 0 2 0 3-1l-1-3z" class="s"></path><path d="M501 512l1-1c2-1 5-5 8-6h2c2 2 3 5 5 7h2 0c-2 1-4 1-7 1s-7 1-11 1h-5v-1h2c1-1 2-1 3-1z" class="R"></path><path d="M491 541h2c1 2 1 5 1 6l-1 1 1 1-1 1c0 2-3 5-5 6l-1-1c-1 1-2 1-3 1-3-1-4-2-6-5s-2-6-2-10c1 4 2 6 4 10h4c2 0 3-1 4-1l1-1-1-1 2-2c1-1 1-3 1-5h0z" class="k"></path><path d="M485 735c2 0 3 1 4 2 2 2 2 5 2 8 0 1-2 2-3 4v-1c-1 0-2 0-3 1v-1c-2-1-3-1-4-2-1-2-1-4 0-6v-1c1-1 2-3 4-4z" class="u"></path><path d="M485 735c2 0 3 1 4 2 2 2 2 5 2 8 0 1-2 2-3 4v-1c1-2 2-3 2-5s-1-4-2-5h-3c-2 0-3 1-4 2v-1c1-1 2-3 4-4z" class="J"></path><path d="M458 842h0c1 1 1 2 2 3-1 2-3 5-6 6v-1h-2v1l1 2v2 1c0 2 8 8 10 10 2 1 3 2 4 3v1l-3-3-15-11-4-3v-1l1-1c2 0 3-2 5-4l1 1c1-1 1-2 1-3h0l1-2c2 0 2 1 4-1z" class="O"></path><path d="M488 573v1l-1 2 1 1 1 2v3c0 1 1 2 1 2v4 1l-3 3h-4c-1-1-2-2-2-3l-1-1c-1-2 0-3 1-5v-1c1-1 1-1 2-1h1c1-1 1-1 1-3h0c0-4 1-3 3-5z" class="S"></path><path d="M485 587l-3 1-1-1c0-1 0-2 1-3s2-1 3-2v1c0 1 1 2 1 3 0 0-1 0-1 1z" class="M"></path><path d="M485 582c2 1 3 2 4 3 0 2 0 3-2 5h0-2v-3h0c0-1 1-1 1-1 0-1-1-2-1-3v-1zM383 794v-1c-1 0-2 0-2-1h-1c2-1 26-1 30-1v1l-1 1h0l-2-1h-1c-1 1-5 1-7 1 1 1 2 1 3 1 0 1 0 0 1 0h2 0c-1 1-1 1-2 1v1h4c-4 0-9 0-13 1-3 0-5 1-8 0-1 0-2 0-3-1l1-1-1-1z" class="H"></path><path d="M494 698c1 1 2 5 2 6 1 1 2 2 1 4h0v1 2h1l-2 1c-1 1-1 0-1 1 0 2 0 3-1 5v1l-3 3h-3l1-1c1-1 2-1 3-2s2-3 2-5c-1-2-1-4-3-5h0c-2-1-2-1-4-1-3 0-4 1-6 3l-1-1c1-1 1-3 1-4 1-2 4-4 6-4 2-1 3-1 5 0 1 0 1-1 2-1v-3z" class="F"></path><path d="M494 698c1 1 2 5 2 6 1 1 2 2 1 4h0v1 2-1c-1-1-1-2-2-3-1-2-1-3-1-4v-2-3z" class="y"></path><path d="M484 611l1-2c1-3 3-4 6-4 2 0 3 1 5 2 0 1 1 3 2 4 0 1 0 1-1 2 0 2 1 2 0 3-2 2-3 3-5 4l-4-2h0c-1 0-2-1-3-2-1-2-1-3 0-5h-1z" class="u"></path><path d="M485 611c1 0 0 0 1-1 3-2 4-2 8-2 1 0 2 2 2 3h-2c-1-1 0-1-2-2-1 0-1 0-2 1-1 2-2 2-4 3 0 1 0 2 1 3s1 1 1 2h0c-1 0-2-1-3-2-1-2-1-3 0-5z" class="G"></path><path d="M484 611l1-2c1-3 3-4 6-4 2 0 3 1 5 2 0 1 1 3 2 4 0 1 0 1-1 2l-1-2c0-1-1-3-2-3-4 0-5 0-8 2-1 1 0 1-1 1h-1z" class="P"></path><defs><linearGradient id="Aa" x1="503.683" y1="774.245" x2="487.793" y2="779.263" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#61625e"></stop></linearGradient></defs><path fill="url(#Aa)" d="M493 747h1c1 0 1-1 2-1h1c-1 2-1 2 0 4 1 1 2 4 1 6l-1 2c0 2-1 5 0 7l-1 7v7 6c-1 10 1 20 0 30-1-3 0-5-1-8 0-3 0-6-1-9v-28c0-5 0-10-1-14v-9z"></path><path d="M493 747h1c1 0 1-1 2-1h1c-1 2-1 2 0 4-2 2 0 4-2 6h-2v-9z" class="J"></path><path d="M480 683v-1l-2-2c-2 0-3-1-4-2l1-1c1 1 2 2 3 2h0l1 1c1 0 1 0 2 1h1 1l-1-1c0-1 0-2-1-3 0 0 1-2 0-3v-3c0-1 1-2 1-3 0 0-1-1-2-1l1-1c-1-1 0-1-1-1v-1c0-1 0-1 1-2l-2-1c0-2 1-1 2-3v-2h0v-1-1-1h1 0c0 1-1 3 0 3 0 2 1 1 0 3h0c1 1 0 1 1 2 0-1 1-1 2-1 0-1 1 0 1 0l1-2h1l2-2h2v4c1 1 1 2 1 3l1 1 3 3c1 3 2 4 1 7 0-1 0-2-1-3 0-1-3-2-4-4-2-1-4-1-7-1-1 1-2 2-2 3-1 3-1 8 0 11 1 1 2 2 2 4h0c-1-1-1-1-1-2s0-1-1-1v1l-2 1h1l-1 1-2-1z" class="O"></path><path d="M483 661c0-1 1-1 2-1 0-1 1 0 1 0l1-2h1l2-2h2v4c1 1 1 2 1 3l1 1h-1-1c-1 0-7 1-7 1-1 0-1 0-1-1h-1c-1 0-1-1-1-2l1-1z" class="D"></path><path d="M484 664c1-1 2-2 4-2 2-1 4 0 5 1l1 1h-1-1c-1 0-7 1-7 1-1 0-1 0-1-1z" class="M"></path><path d="M484 532h2c2 2 3 3 4 5h0-1l2 4h0c0 2 0 4-1 5l-2 2 1 1-1 1c-1 0-2 1-4 1h-4c-2-4-3-6-4-10l1-1c0-3 2-6 5-7l2-1z" class="u"></path><path d="M480 540l1-1c0-1 2-2 3-2 2-1 3 0 5 0l2 4h0 0-4c-1 0-1-1-2-1h-5z" class="H"></path><path d="M484 532h2c2 2 3 3 4 5h0-1c-2 0-3-1-5 0-1 0-3 1-3 2l-1 1c0 2 0 4 1 5 0 2 2 3 3 3 2 1 3 0 4 0l1 1-1 1c-1 0-2 1-4 1h-4c-2-4-3-6-4-10l1-1c0-3 2-6 5-7l2-1z" class="c"></path><path d="M497 631c0 3 0 4-1 7v2l2 1v2c-1 2-1 5-2 7v1l-2-2-1 2-1 1 1-2-2-1v-3c0 1-1 1-2 2h-3c-1-1-2-3-3-4l1-1h-1l-1 1c0 2 2 3 3 4v1l1 1h-1-1c-1-1-3-2-3-4h-1c0-1 0-1-1-2v-1-1c0-1 1-4 1-4 2-3 4-4 6-4l1-1c1 1 3 1 4 2h1 0 1 1v-1-1c1-1 1-2 3-2z" class="O"></path><path d="M486 641c1 0 2-1 3-1 1 1 2 2 2 4 0 1-1 2-2 3h-2c-1-1-3-2-3-4 0-1 1-2 2-2z" class="I"></path><path d="M497 631c0 3 0 4-1 7v2l2 1v2c-1 2-1 5-2 7v1l-2-2c1-2 2-5 2-7-1-2-1-3-1-4-1-1-1-2-1-3v-1-1c1-1 1-2 3-2z" class="k"></path><path d="M502 724h0l1-1v31h-1c0 2 0 4-1 6v4h0c-2 1-2 1-2 3-2-1-1 0-2-1v-1c-1-2 0-5 0-7l1-2c1-2 0-5-1-6-1-2-1-2 0-4h-1c-1 0-1 1-2 1h-1v-13c1-2 0-3 1-4h2l1-1 1-2 1-1 1-1v-1h2z" class="h"></path><path d="M501 742h0c-2 0-3-2-4-3l1-1c1-1 1 1 2 1 0 1 1 1 1 1v2z" class="d"></path><path d="M502 724h0l1-1v31h-1c0 2 0 4-1 6v-18-2c0-2 1-3 1-5 0-3 1-8 0-11z" class="Q"></path><path d="M493 747v-13c1-2 0-3 1-4h2v1l-1 6c1 1 1 0 1 1l-1 2 1 1v1l-1 1 2 1h0c0 3 1 4 2 7 1 2 1 5 0 7l1 2-1 1 1 2 1 1h0c-2 1-2 1-2 3-2-1-1 0-2-1v-1c-1-2 0-5 0-7l1-2c1-2 0-5-1-6-1-2-1-2 0-4h-1c-1 0-1 1-2 1h-1z" class="o"></path><path d="M328 869c2 0 4-2 6-3 2-2 4-3 7-3v3h1c1-1 0-1 0-2s1-1 1-2c2-1 5-1 7-3 2 0 3-2 5-2l3 21-32 1v-11l2 1z" class="t"></path><path d="M469 470c3 0 4 1 6 2l3 3c2 2 4 6 7 8l10 11 5 6 7-5 1 1-2 2h0l1 2h-2-1l-1 1 1 2-1 1c0 1 0 1 1 2v2l-1 1-2 1-1 1-14 2c-1 0-1 0-1-1-1 1-3 2-4 3v-10h-1c0-1 1-3 0-5v-1c0-1 0-2 1-4v-1-1c-1-2-1-5 0-7h1l1 1h-1c-1 1-1 2-1 4l1 1h1c1 0 3 1 4 3v2c2 1 2 1 3 3-1 1-1 2-2 3v3h0 0c1-1 0-1 1-1v-1l2-2c1 0 2 0 3 1v-2l1-1v-1c-1-1-1 0-1-1l-2-2h-1c-1-1-2-1-2-2s0-2-1-2v-1c0-1-1-2-2-3-3-2-6-5-6-8-1-2-5-7-7-8v-1l-3-1v17c0 3-1 6 0 9 1 4 0 8 0 11v20c0 4 1 7 0 11-1 1-1 3-1 5v-73z" class="B"></path><path d="M481 494l2-1c1 0 2 1 3 3v2c-2 1-2 1-3 0-2-1-2-1-2-3v-1z" class="T"></path><path d="M481 505v-4c2 0 3 1 4 2v2h-1v1 1c1 0 1 1 2 1l1 1c-1 1-2 2-2 3-1 1-3 2-4 3v-10z" class="P"></path><path d="M507 495l1 1-2 2h0l1 2h-2-1l-1 1 1 2-1 1c0 1 0 1 1 2v2l-1 1h-1c-3-2-8-3-13-3l2-2h2s1 1 2 1v-1-1c0-1 0-1 1-2 2 1 3 2 6 1-1 0-2-1-2-2l7-5z" class="D"></path><path d="M489 506c5 0 10 1 13 3h1l-2 1-1 1-14 2c-1 0-1 0-1-1s1-2 2-3l2-3z" class="T"></path><path d="M485 512c1 0 3-1 5-1 1-1 3-2 5-2s5 0 6 1l-1 1-14 2c-1 0-1 0-1-1z" class="c"></path><path d="M499 625c0-1 0-2 2-3h0c1 4 1 7 1 10h1v49 33 9l-1 1h0-2v1l-1 1-1 1-1 2-1 1v-1c-1-1 1-3 1-4l3-2v-2c0-4 0-7-2-10h-1v-2-1h0c1-2 0-3-1-4 0-1-1-5-2-6s-1-1-2-3c0-3 0-8 1-11v-3c1-2 3-4 5-7 1-3 0-4-1-7l-3-3-1-1c0-1 0-2-1-3v-4l1-5 1-2 2 2v-1c1-2 1-5 2-7v-2l-2-1v-2c1-3 1-4 1-7-1-1-2-1-2-3h2l1-2 1-1z" class="v"></path><path d="M499 625c0-1 0-2 2-3h0c0 5 1 10 0 14l-2 2 1 1h-1c-1 1-1 2-1 2l-2-1v-2c1-3 1-4 1-7-1-1-2-1-2-3h2l1-2 1-1z" class="b"></path><path d="M499 666h2c0 9 1 18 0 26-1-2-1-3-2-5-1-1-2-1-3-1l-2-2c1-2 4-4 4-7h1l1-3c1-2 0-6-1-8z" class="q"></path><path d="M498 641s0-1 1-2h1l-1-1 2-2v17 6 7h-2v-2-1c-1-1-3-2-3-3 0-2 0-5 1-7l-1-2v-1c1-2 1-5 2-7v-2z" class="f"></path><path d="M501 659l-3-3v-3h3v6z" class="E"></path><path d="M493 651l1-2 2 2 1 2c-1 2-1 5-1 7 0 1 2 2 3 3v1 2c1 2 2 6 1 8l-1 3h-1c0 3-3 5-4 7h-1v-3c1-2 3-4 5-7 1-3 0-4-1-7l-3-3-1-1c0-1 0-2-1-3v-4l1-5z" class="k"></path><path d="M493 651l1-2 2 2 1 2c-1 0-3 1-3 1-1 3 0 4 1 6v2c1 1 2 2 3 2l-1 3-3-3-1-1c0-1 0-2-1-3v-4l1-5z" class="l"></path><path d="M493 684h1l2 2c1 0 2 0 3 1 1 2 1 3 2 5 1 5 0 11 0 17 0 2 1 3 0 5h0c-2-2-1-6-1-9-1 0-2 0-3-1h-1c0-1-1-5-2-6s-1-1-2-3c0-3 0-8 1-11z" class="P"></path><path d="M493 684h1l2 2 1 3-1 1c0-1-1-1-2-1v-1-1l-1-1v-2z" class="b"></path><path d="M496 695c1 0 3 0 3 1 1 1 1 7 1 9-1 0-2 0-3-1h1v-7l-2-2z" class="h"></path><path d="M492 695h4l2 2v7h-1-1c0-1-1-5-2-6s-1-1-2-3z" class="k"></path><path d="M502 529c0-2 1-3 1-5v6 5 15h1l2-2v1 2 2c1 2-1 3 1 6v3 2 7h0l-2-1-2 1v1 60h-1c0-3 0-6-1-10h0c-2 1-2 2-2 3l-2-3 3-6h-1v-1c0-2 0-3-1-4s-2-3-2-4v-2h0l2-3c-1-1-1-1-1-2l-1 1h-1c0-2 0-3 1-5h0c-3-1-1-3-2-5 0-2-1-3-1-5v-3-13-8c2-1 2-3 4-4 0-2 1-5 0-7l-1-1c0-1-1-3-1-4v-3-5c0-1 1-2 2-3h2v-2c0-1 0-2-1-3h-2v-3c1 1 2 1 3 1h2l1 1z" class="Y"></path><path d="M498 602c1 2 2 3 3 4v5h-1c-1-2-2-4-3-5l-1-1h0l2-3z" class="b"></path><path d="M500 616v-3h1v9c-2 1-2 2-2 3l-2-3 3-6z" class="h"></path><path d="M496 527c1 1 2 1 3 1h2l1 1c1 1 1 3 0 4-1 0-1-1-2-1v3l-1 1v-1-2c0-1 0-2-1-3h-2v-3z" class="J"></path><path d="M503 550h1l2-2v1 2 2c1 2-1 3 1 6v3 2 7h0l-2-1-2 1v-21z" class="V"></path><path d="M495 543v-5c0-1 1-2 2-3h2v1 2c1 0 1 0 2-1v1 4c1 4 0 8 0 12 1 5 0 10 0 15 0 3 1 6 0 9v-3-3c0-3 0-9-1-11 0 1-1 2-2 3s-1 2-2 2v1h-1v1c0 1-1 1-2 2v-8c2-1 2-3 4-4 0-2 1-5 0-7l-1-1c0-1-1-3-1-4v-3z" class="q"></path><path d="M495 543c1-2 1-3 1-5h1c1 1 1 2 1 4h1c0 1 0 2 1 3-1 1-1 1-1 2 1 1 1 1 1 2-2 0-2 0-3-1l-2-2v-3z" class="f"></path><defs><linearGradient id="Ab" x1="493.377" y1="601.425" x2="504.026" y2="574.038" xlink:href="#B"><stop offset="0" stop-color="#bab8b8"></stop><stop offset="1" stop-color="#ececeb"></stop></linearGradient></defs><path fill="url(#Ab)" d="M493 570c1-1 2-1 2-2v-1h1v-1c1 0 1-1 2-2s2-2 2-3c1 2 1 8 1 11v3 3 28c-1-1-2-2-3-4-1-1-1-1-1-2l-1 1h-1c0-2 0-3 1-5h0c-3-1-1-3-2-5 0-2-1-3-1-5v-3-13z"></path><path d="M497 573c1 1 1 2 2 3v1c-1 1-1 2-1 4l-1 1-1-2c0-2 1-6 1-7z" class="W"></path><path d="M493 570c1-1 2-1 2-2v-1h1v-1c1 0 1-1 2-2s2-2 2-3c1 2 1 8 1 11v3c-1-1-2-1-3-3h0l-1 1c0 1-1 5-1 7l1 8c-1 3-1 5 0 8h-1 0c-3-1-1-3-2-5 0-2-1-3-1-5v-3-13z" class="X"></path><path d="M497 588c-2 0-2-1-3-2v-2l-1-1c2-2 0-6 1-8v-1c1-1 1-2 1-3 1 0 2 0 2-1l1-1c1 1 1 3 2 3h1v3c-1-1-2-1-3-3h0l-1 1c0 1-1 5-1 7l1 8z" class="i"></path><path d="M543 467l3-2 1 1v1c1 1 1 2 2 3l1 3v2c1 0 2 1 2 2 1 0 1 1 1 2 0 2 0 4-1 6 1 0 1 1 2 2-2 1-3 0-5 0l6 6c-2 3-3 4-5 5h-1c-2 2-2 3-4 4-1 0-2 1-3 2-1 0-2 1-3 2l-3-2c-2 2-4 4-7 6l-3 3-1 1-1-2h-2v-1c-2-1-4-4-6-4 0 2 1 3 1 5-2-2-3-5-5-7h-2c-3 1-6 5-8 6l-1 1-1-1 1-1 2-1 1-1v-2c-1-1-1-1-1-2l1-1-1-2 1-1h1 2l-1-2h0l2-2-1-1 13-10 4-3v-3c2-2 4-3 5-5l1-1c1 0 3-1 4-2 0-1 1-2 1-3h0c2 0 2 0 3 1v1l3-2 2-1z" class="N"></path><path d="M539 499c-2-1-4-2-6-4h1c1 0 1 1 2 1h4l-1 3zm4-8l1 1c1 2 0 1 1 3v1c-1 1-2 1-3 2l-1-2c1 0 1-1 2-1l-1-1c-1 0-1 0-1-1l2-2z" class="U"></path><path d="M515 495l1-1c0-1 1-2 2-2l13-12 4 4c-4 1-9 6-13 8 0 1-1 1-2 2l-2 1h0v2c-1-1-2-2-3-2z" class="D"></path><path d="M543 483c3 1 5 2 6 4l6 6c-2 3-3 4-5 5h-1c-2 2-2 3-4 4-1 0-2 1-3 2-1 0-2 1-3 2l-3-2c1 0 3-2 4-3l-1-2 1-3h1l1 2c1-1 2-1 3-2v-1c-1-2 0-1-1-3l-1-1-6-5c2 0 2 0 4-1l-1-1 1-1h2z" class="W"></path><path d="M541 496l1 2c1-1 2-1 3-2v1l-5 4-1-2 1-3h1z" class="E"></path><path d="M541 485c3 2 5 6 7 9 0 1-1 2-3 3v-1-1c-1-2 0-1-1-3l-1-1-6-5c2 0 2 0 4-1z" class="s"></path><path d="M528 479s1-1 2-1l1 2-13 12c-1 0-2 1-2 2l-1 1h-4c0 1 2 3 2 4 4 4 8 11 13 14l-1 1-1-2h-2v-1c-2-1-4-4-6-4 0 2 1 3 1 5-2-2-3-5-5-7h-2c-3 1-6 5-8 6l-1 1-1-1 1-1 2-1 1-1v-2c-1-1-1-1-1-2l1-1-1-2 1-1h1 2l-1-2h0l2-2-1-1 13-10 4-3 4-3z" class="C"></path><path d="M508 496l2-1v1c0 1-1 2-2 4 2 0 3 1 5 1v1c-2 2-6 0-8 0 0-1-1-1 0-2h2l-1-2h0l2-2z" class="H"></path><path d="M543 467l3-2 1 1v1c1 1 1 2 2 3l1 3v2c1 0 2 1 2 2 1 0 1 1 1 2 0 2 0 4-1 6 1 0 1 1 2 2-2 1-3 0-5 0-1-2-3-3-6-4h-2l-1 1 1 1c-2 1-2 1-4 1l-2-2-4-4-1-2c-1 0-2 1-2 1l-4 3v-3c2-2 4-3 5-5l1-1c1 0 3-1 4-2 0-1 1-2 1-3h0c2 0 2 0 3 1v1l3-2 2-1z" class="S"></path><path d="M543 478l-1 1h-6v-1c2-2 4-2 6-2l1 2z" class="E"></path><path d="M534 476c1 2 2 3 2 6h1l2-2h1c0 2-1 2 0 4l1 1c-2 1-2 1-4 1l-2-2-4-4-1-2c2 0 3-1 4-2z" class="H"></path><path d="M535 468c2 0 2 0 3 1v1l3-2v5h-1-1v-1h0l-5 4c-1 1-2 2-4 2-1 0-2 1-2 1l-4 3v-3c2-2 4-3 5-5l1-1c1 0 3-1 4-2 0-1 1-2 1-3h0z" class="E"></path><path d="M541 468v5h-1-1v-1h0l-5 4c-1 1-2 2-4 2-1 0-2 1-2 1l10-9 3-2z" class="B"></path><path d="M543 467l3-2 1 1v1c1 1 1 2 2 3l1 3v2c1 0 2 1 2 2 1 0 1 1 1 2 0 2 0 4-1 6 1 0 1 1 2 2-2 1-3 0-5 0-1-2-3-3-6-4l1-1c0-1 0-1-1-2v-2l-1-2c0-1-1-2-2-3h1v-5l2-1z" class="H"></path><path d="M543 467l3-2 1 1v1l-2 1-1 2h-2l-1 3v-5l2-1z" class="C"></path><path d="M547 467c1 1 1 2 2 3l1 3c-1 1-1 2-1 3v1 2h0v-2c0-1-1-1-1-2s-1-2-2-3c0-2 0-3 1-4h-2l2-1z" class="a"></path><path d="M550 473v2c1 0 2 1 2 2 1 0 1 1 1 2 0 2 0 4-1 6 1 0 1 1 2 2-2 1-3 0-5 0-1-2-3-3-6-4l1-1 4-1 1 1h1c0-1 0-2-1-3v-2-1c0-1 0-2 1-3z" class="K"></path><path d="M550 475c1 0 2 1 2 2 1 0 1 1 1 2 0 2 0 4-1 6-1-2-1-8-2-10z" class="L"></path><path d="M517 439c1 0 2 1 3 2l1 2c1 0 2-1 3-1l1 1c1 1 1 4 3 5h0l-1 1 10 10 3 4h1c1 1 2 3 2 4l-2 1-3 2v-1c-1-1-1-1-3-1h0c0 1-1 2-1 3-1 1-3 2-4 2l-1 1c-1 2-3 3-5 5v3l-4 3-13 10-7 5-5-6-10-11c-3-2-5-6-7-8 0-2 0-4 2-6l1 2h-1l-1 2v1l2-1c1 0 2 1 3 2v-1h2 3c1-1 2-2 4-3 3-3 4-5 9-6 0 0 1-1 2-1h0c0-2 0-4-1-5 1-2 3-3 5-5l9-7c1 0 1 0 2 1h1v-3c0-2-1-2-2-3l-1 1c-1-1-2-1-3-3l3-1z" class="W"></path><path d="M508 454c1 1 2 1 3 2l2 3-1 2c-1 0-3-2-4-2-1 1-2 4-4 5h0c0-2 0-4-1-5 1-2 3-3 5-5z" class="L"></path><path d="M486 474h3c1-1 2-2 4-3 3-3 4-5 9-6-3 3-7 6-9 9v2c0 3 3 7 5 9-1 0-1 0-2 1l-10-12z" class="Y"></path><path d="M517 439c1 0 2 1 3 2l1 2c1 0 2-1 3-1l1 1c1 1 1 4 3 5h0l-1 1 10 10 3 4h1c1 1 2 3 2 4l-2 1-3 2v-1c-1-1-1-1-3-1h0c0 1-1 2-1 3-1 1-3 2-4 2l-1 1-1-1c-1 0-1-1-2 0 0 1 0 2-1 3h0-1v-2c-1-1 0-2-1-4-2 1-3 2-4 3-3 2-7 5-9 8h-1v-1l4-4c5-4 9-7 14-10-1-1-2-2-3-2l-1-1 1-2c-2-1-1-1-2-3-1-1-4-3-5-4-1 2-3 3-4 5h0l-2-3c-1-1-2-1-3-2l9-7c1 0 1 0 2 1h1v-3c0-2-1-2-2-3l-1 1c-1-1-2-1-3-3l3-1z" class="T"></path><path d="M523 452l7 7c-1 2-1 3-1 5-2-1-2-1-3-3 1 0 1 0 2-1h0-2 0c-2-1-2-1-2-3-2-2-3-3-5-4h1v-1h2 1z" class="Y"></path><path d="M517 447c1 0 1 0 2 1l4 4h-1-2v1h-1c-4 0-6 1-8 3-1-1-2-1-3-2l9-7z" class="I"></path><path d="M534 464c0 1 1 2 2 3l-1 1h0c0 1-1 2-1 3-1 1-3 2-4 2l-1 1-1-1c-1 0-1-1-2 0 0 1 0 2-1 3h0-1v-2c1-1 2-3 1-4v-1c1-1 3-3 5-4l4-1z" class="R"></path><path d="M529 467h1v1c-1 2-1 2-3 3 0 0-1 0-1-1v-1c1 0 1-1 2-2h1zm-12-28c1 0 2 1 3 2l1 2c1 0 2-1 3-1l1 1c1 1 1 4 3 5h0l-1 1 10 10 3 4h1c1 1 2 3 2 4l-2 1-3 2v-1c-1-1-1-1-3-1l1-1c-1-1-2-2-2-3l-4 1-1-1c0-2 0-3 1-5l-7-7-4-4h1v-3c0-2-1-2-2-3l-1 1c-1-1-2-1-3-3l3-1z" class="V"></path><path d="M530 459l4 5-4 1-1-1c0-2 0-3 1-5z" class="J"></path><path d="M524 442l1 1c1 1 1 4 3 5h0l-1 1-6-6c1 0 2-1 3-1z" class="I"></path><path d="M540 463h1c1 1 2 3 2 4l-2 1-3 2v-1c-1-1-1-1-3-1l1-1c1-1 1-2 3-2h1v-2z" class="c"></path><path d="M480 469l1 2h-1l-1 2v1l2-1c1 0 2 1 3 2v-1h2l10 12c1-1 1-1 2-1h1c4 0 7-2 10-5v1h1c2-3 6-6 9-8 1-1 2-2 4-3 1 2 0 3 1 4v2h1 0c1-1 1-2 1-3 1-1 1 0 2 0l1 1c-1 2-3 3-5 5v3l-4 3-13 10-7 5-5-6-10-11c-3-2-5-6-7-8 0-2 0-4 2-6z" class="N"></path><path d="M520 485v-1s0-1-1-2c2-1 3-2 5-3v3l-4 3z" class="c"></path><path d="M521 475l2 2c-1 2-3 3-5 4-1 1-2 2-4 3-2 2-8 6-10 9h-2s1-1 1-2h0c1-2 2-3 4-4 3-1 5-4 8-6 1-1 4-2 4-3l2-3z" class="R"></path><path d="M519 473l2 1v1l-2 3c0 1-3 2-4 3-3 2-5 5-8 6-1-1-1-1-1-2 1-1 2-1 3-2h0l1-2c2-3 6-6 9-8z" class="g"></path><path d="M480 469l1 2h-1l-1 2v1l2-1c1 0 2 1 3 2v-1h2l10 12c1-1 1-1 2-1h1c4 0 7-2 10-5v1h1l-1 2h0c-1 1-2 1-3 2 0 1 0 1 1 2-2 1-3 2-4 4h0c0 1-1 2-1 2-1 1-2 1-3 1l2 2h1l-2 2-1-1c-1-2-2-2-4-3h0l-10-11c-3-2-5-6-7-8 0-2 0-4 2-6z" class="t"></path><path d="M509 480v1c-3 3-6 6-9 8-2 0-3-2-4-3 1-1 1-1 2-1h1c4 0 7-2 10-5z" class="Q"></path><path d="M485 483l1-1c3 1 4 3 6 2 1 1 1 1 1 2s0 3 1 4 5 4 5 4l2 2h1l-2 2-1-1c-1-2-2-2-4-3h0l-10-11z" class="j"></path><path d="M422 775h37l1 15c0 3 0 6-1 8l1 2 2-1h0 5v1l-3 12-3 8-51-25c1-2 2-4 2-6 0 0 1-1 1-2v-2c0-1 1-2 1-3h0c0-2 0-2 1-4h0v-3c2-1 5-1 7 0z" class="n"></path><path d="M448 786v-1c1 0 1-1 2-1h2l1-2c1 0 1 0 2 1 1 0 1 1 2 1 0 2 1 3 1 4l2 2c0 3 0 6-1 8v-2c-2-3-2-5-2-9-1 0-5 1-6 1h-2c-1-1-1-1-1-2z" class="W"></path><path d="M422 775h37l1 15-2-2c0-1-1-2-1-4-1 0-1-1-2-1-1-1-1-1-2-1l-1 2h-2c-1 0-1 1-2 1v1l-1-1c0-2-1-3-2-3-1 1-1 1-3 1v-3-1c-2 0-3 0-5 1h-1c-1-1-2-1-3-1-2-1-3-1-4-2-2-1-4-1-7-2z" class="N"></path><path d="M490 410l2 3 4 4 4 4 7 6h0c3 3 5 7 9 10h0l1 2-3 1c1 2 2 2 3 3l1-1c1 1 2 1 2 3v3h-1c-1-1-1-1-2-1l-9 7c-2 2-4 3-5 5 1 1 1 3 1 5h0c-1 0-2 1-2 1-5 1-6 3-9 6-2 1-3 2-4 3h-3-2v1c-1-1-2-2-3-2l-2 1v-1l1-2h1l-1-2c-2 2-2 4-2 6l-3-3c-2-1-3-2-6-2 0-7-1-15 0-22-2-1-4-1-6-1l-1 1c-1-1-2-1-3-1l-3 1c-1-2 1-16 1-19l-1-1h0l2-1c1 0 2-1 4 0h1c2 0 4 1 7 2v-1h2c1 1 1 1 1 3l1-1 3-2 1-2-1-1c0-1 0-1-1-2l1-1c-1-3 0-6 1-9l2-2c1 1 2 2 4 2v-3l2 3h2c1-1 1-2 2-3z" class="Y"></path><path d="M476 451c1 1 1 2 2 3-2 1-3 1-4 2v-1h1c0-2 0-3 1-4z" class="L"></path><path d="M479 455v-2c2-2 3-3 5-4v1c1 1 2 1 4 1v1c1 1 2 2 3 2v1h0v1c-3 3-6 6-10 8-1 0-1 0-2-1s-1-3-1-5l1-3h0z" class="R"></path><path d="M479 455h0 4c2-1 3-1 4-1l1 1c-1 0-1 1-2 1s-2 0-2 1c-3 0-4-1-6 1l1-3h0z" class="f"></path><path d="M496 440c2 0 3 0 5 1l-1 2h-1c-1 1-2 2-3 4l1 1 2-2h1l2 2-11 8v-1h0v-1c-1 0-2-1-3-2v-1c-2 0-3 0-4-1v-1l1-2c1 1 4-2 5-2v-1l6-4z" class="N"></path><path d="M496 440c2 0 3 0 5 1l-1 2h-1 0c-2 1-4 2-5 3s-2 1-3 1h-1v-2-1l6-4z" class="W"></path><defs><linearGradient id="Ac" x1="464.793" y1="443.806" x2="476.267" y2="469.717" xlink:href="#B"><stop offset="0" stop-color="#1f1e1c"></stop><stop offset="1" stop-color="#3b3b38"></stop></linearGradient></defs><path fill="url(#Ac)" d="M473 431l1-1c0 4 0 8 1 11 0 1 0 2 1 2h0v1 2h0v5c-1 1-1 2-1 4h-1v1l2 2-1 2-1 1-1 1 1 1c2 0 2 0 3 1l-1 2h0c1 2 1 3 1 4l-2 2c-2-1-3-2-6-2 0-7-1-15 0-22 1-2 1-2 0-4l1-2 1 1h0c0-2 0-2-1-3h1 1v-3c1-2 0-4 1-6z"></path><path d="M472 448c1 2 2 5 3 7h-1v1l2 2-1 2-1 1v-2c-1-1-1-1-1-2v-2l-1-1c-1-1 0-4 0-6z" class="F"></path><path d="M473 431l1-1c0 4 0 8 1 11 0 1 0 2 1 2h0v1 2h0v5c-1 1-1 2-1 4-1-2-2-5-3-7v-11c1-2 0-4 1-6z" class="I"></path><path d="M458 427c1 0 2-1 4 0h1c2 0 4 1 7 2v-1h2c1 1 1 1 1 3-1 2 0 4-1 6v3h-1-1c1 1 1 1 1 3h0l-1-1-1 2c1 2 1 2 0 4-2-1-4-1-6-1l-1 1c-1-1-2-1-3-1l-3 1c-1-2 1-16 1-19l-1-1h0l2-1z" class="a"></path><path d="M460 428l2-1 1 1c-1 3 0 6 0 8 1 2 0 4-1 6l-1-1v-3c-1-3 0-7-1-10z" class="O"></path><path d="M458 427c1 0 2-1 4 0h1c2 0 4 1 7 2h-1l-2 1c0 2 0 2 1 3 0 2 1 7 0 9 0 1-1 1-1 2l-1-2c-1-1 0-1-1-2v-1c-1-1-1-2-1-3 1-1 1-2 1-4l-2-4-1-1-2 1c-1 0-1 0-2-1z" class="e"></path><path d="M470 429v-1h2c1 1 1 1 1 3-1 2 0 4-1 6v3h-1-1c1 1 1 1 1 3h0l-1-1-1 2c1 2 1 2 0 4-2-1-4-1-6-1l-1 1c-1-1-2-1-3-1 3-1 5-1 8-3 0-1 1-1 1-2 1-2 0-7 0-9-1-1-1-1-1-3l2-1h1z" class="F"></path><path d="M470 429v-1h2c1 1 1 1 1 3-1 2 0 4-1 6v3h-1-1c1 1 1 1 1 3h0l-1-1-1 2c1-2 1-4 0-5v-10h1z" class="a"></path><path d="M498 453c5-4 10-10 16-13 1 2 2 2 3 3l1-1c1 1 2 1 2 3v3h-1c-1-1-1-1-2-1l-9 7c-2 2-4 3-5 5 1 1 1 3 1 5h0c-1 0-2 1-2 1-5 1-6 3-9 6-2 1-3 2-4 3h-3-2v1c-1-1-2-2-3-2l-2 1v-1l1-2h1l-1-2c-2 2-2 4-2 6l-3-3 2-2c5-6 14-12 20-16l1-1z" class="t"></path><path d="M498 453h1c-1 1-1 1-1 2h1c0 1 0 1 1 1l-3 3-2-2c1-1 2-2 2-3l1-1z" class="U"></path><path d="M503 459c1 1 1 3 1 5h0c-1 0-2 1-2 1-5 1-6 3-9 6-2 1-3 2-4 3h-3-2l3-2 16-13z" class="D"></path><path d="M498 453c5-4 10-10 16-13 1 2 2 2 3 3l1-1c1 1 2 1 2 3v3h-1c-1-1-1-1-2-1h-4s-1 1-1 2c-3 2-4 4-8 3l-2 2c-1 0-2 0-3-1h-1z" class="R"></path><path d="M517 443l1-1c1 1 2 1 2 3v3h-1c-1-1-1-1-2-1h-4s-1 1-1 2c-3 2-4 4-8 3 2-2 5-2 6-5h1 1c1-2 3-3 5-4z" class="n"></path><path d="M497 454c0 1-1 2-2 3l-1 2 1 1 1 1c-2 3-4 4-6 5-1 2-3 2-3 4v2l-3 2v1c-1-1-2-2-3-2l-2 1v-1l1-2h1l-1-2c-2 2-2 4-2 6l-3-3 2-2c5-6 14-12 20-16z" class="c"></path><path d="M480 469l1-1c1-1 1-1 3-2l2 1c-1 1-3 2-4 4h-1l-1-2z" class="i"></path><path d="M495 460l1 1c-2 3-4 4-6 5-1 2-3 2-3 4v2l-3 2v1c-1-1-2-2-3-2l-2 1v-1l1-2h1 1c1-2 3-3 4-4l9-7z" class="W"></path><path d="M478 426l3-5c1-1 1-2 2-2l3 3 12 10 1 2 2 2-4 3-1 1-6 4v1c-1 0-4 3-5 2l-1 2c-2 1-3 2-5 4v2h0c0-1 0-1-1-2h0c1-3 1-7 0-9h-2v-1h0c-1 0-1-1-1-2-1-3-1-7-1-11l3-2 1-2z" class="P"></path><path d="M490 431l3-2c1 1 1 1 1 2l1 1-3 3-1 1c-1 0-1-1-2-1l1-4z" class="R"></path><path d="M488 436c0-1-1-3-2-3v2c-1-1-2-1-2-2l1-2h-1-1l-1-1v-1c3-1 4-3 6-4l1 1v2c1 0 1 0 1 1s-1 1-1 2h1l-1 4c1 0 1 1 2 1l-2 1-1-1z" class="E"></path><path d="M478 426l3-5c1-1 1-2 2-2l3 3c-3 0-3 1-5 2v4c0 2-1 3-2 5v3l-1 2h1 1v1l-1 1s1 2 0 2c1 2 2 3 3 4l1 2c1 0 1-1 2-1h0l-1 2c-2 1-3 2-5 4v2h0c0-1 0-1-1-2h0c1-3 1-7 0-9h-2v-1h0c-1 0-1-1-1-2-1-3-1-7-1-11l3-2 1-2z" class="h"></path><path d="M474 430l3-2c0 4 0 8 1 11 0 2-1 3 0 5h-2v-1h0c-1 0-1-1-1-2-1-3-1-7-1-11z" class="L"></path><path d="M499 434l2 2-4 3-1 1-6 4v1c-1 0-4 3-5 2h0c-1 0-1 1-2 1l-1-2c-1-1-2-2-3-4l2-2v-2l1-1s1-1 1-2h0c1 0 1 0 2 1h3l1 1 2-1 1-1h2l1-1c1 0 1 0 3 1l1-1z" class="T"></path><path d="M479 442l2-2v-2l1-1s1-1 1-2h0c1 0 1 0 2 1-2 0-2 1-3 2l1 1c2 0 4-1 5 1v2l-1-1h-1-4c-1 1-1 2 0 3v2c-1-1-2-2-3-4z" class="p"></path><path d="M490 439h3l1-1c1 0 1 1 2 1h1l-1 1-6 4v1c-1 0-4 3-5 2h0c-1 0-1 1-2 1l-1-2v-2c-1-1-1-2 0-3h4l1 1 1 1h1v-3h0l1-1z" class="E"></path><path d="M489 440l1-1 2 2-2 2h-1v-3z" class="X"></path><path d="M482 444l1 1h1c0-1 0-1 1-1h1l-1 2h1v-1c1 0 2-1 2-1h2v1c-1 0-4 3-5 2h0c-1 0-1 1-2 1l-1-2v-2z" class="T"></path><path d="M490 410l2 3 4 4 4 4 7 6h0c3 3 5 7 9 10h0l-5 4-9 7-2-2h-1l-2 2-1-1c1-2 2-3 3-4h1l1-2c-2-1-3-1-5-1l1-1 4-3-2-2-1-2-12-10-3-3c-1 0-1 1-2 2l-3 5-1-1c0-1 0-1-1-2l1-1c-1-3 0-6 1-9l2-2c1 1 2 2 4 2v-3l2 3h2c1-1 1-2 2-3z" class="U"></path><path d="M501 436l3-2v1c-1 1-4 4-3 5 1 0 1 0 2-1 0 1 2 3 2 3-1 2-2 3-3 4l-1-2v-1h2c-1-2-1-2-2-2h0c-2-1-3-1-5-1l1-1 4-3z" class="V"></path><path d="M499 424c1 0 1-1 2 1 1 0 2 1 2 2h1l3 3 2 2v1c-2 3-4 4-6 6-1 1-1 1-2 1-1-1 2-4 3-5v-1h0l1-1c0-3-3-3-3-6-1 0-1-1-2-1h-1v-2z" class="j"></path><path d="M490 410l2 3 4 4 4 4 7 6h0c3 3 5 7 9 10h0l-5 4c-1-1-1-2-2-3 1-1 1-2 1-4l-1-1v-1l-2-2-3-3h-1c0-1-1-2-2-2-1-2-1-1-2-1l-1-1h-1l-2-1c-2-1-4-3-6-5-1-2-2-3-5-4h0v-3l2 3h2c1-1 1-2 2-3z" class="V"></path><path d="M495 422v-2l1-1 1 1c1 1 0 2 0 3l-2-1z" class="W"></path><path d="M480 411c1 1 2 2 4 2h0c3 1 4 2 5 4 2 2 4 4 6 5l2 1h1l1 1v2h1c1 0 1 1 2 1 0 3 3 3 3 6l-1 1h0l-3 2-2-2-1-2-12-10-3-3c-1 0-1 1-2 2l-3 5-1-1c0-1 0-1-1-2l1-1c-1-3 0-6 1-9l2-2z" class="I"></path><path d="M480 411c1 1 2 2 4 2h0c0 2 0 3 1 4-3-1-5-2-7-4l2-2z" class="K"></path><path d="M484 413c3 1 4 2 5 4v4c-2-1-3-2-4-3l1-1-1-1v1c-1-1-1-2-1-4z" class="B"></path><path d="M489 417c2 2 4 4 6 5l2 1h1l1 1v2h1c1 0 1 1 2 1 0 3 3 3 3 6l-1 1h0l-3 2-2-2-1-2h0c0-3-5-6-7-8-1-1-1-2-2-3v-4z" class="D"></path><path d="M495 422l2 1h1l1 1v2h1c1 0 1 1 2 1 0 3 3 3 3 6l-1 1c0-2-2-3-3-4-2-3-5-5-6-8z" class="U"></path><path d="M560 483c1-3 1-6 2-8v21c1 3 4 4 6 7 1 1 4 4 6 5l-2 3 7 8 7 7 1 2 2 2 6 7 6 7 2 2 5 5h-3c-2 1-4 3-6 4 0 0-2-1-2 0-2 0-4 4-6 5l-1 1-1 1c-1 0-1 1-2 2l-2 3c-1 1-3 2-3 4 0 0 0 1-1 2 0-1-1-2-1-2h-1l-1-1v-1h-1l-3 3-12-12-3-2-1-1-1-2-1-2c-1-1-2-1-3-2h-1l-1-2-2 2c-5-2-11-11-15-15l-11-11-1-2h-1l-2-3-1-1 7-5 1-1 3-3c3-2 5-4 7-6l3 2c1-1 2-2 3-2 1-1 2-2 3-2 2-1 2-2 4-4h1c2-1 3-2 5-5l-6-6c2 0 3 1 5 0-1-1-1-2-2-2 1-2 1-4 1-6l3-1c0 2 1 3 1 5 1 1 2 1 3 3v-3z" class="g"></path><path d="M554 545h3v1 1h-3v-2z" class="W"></path><path d="M558 557h6 0l-2 3-3-2-1-1z" class="R"></path><path d="M573 547c1 0 1 0 2 1 0 1 0 2-1 3l-1-1c-1 0-2 2-2 2h-2l1-1c0-2 1-3 3-4z" class="V"></path><path d="M539 517l1-1 1 1 2 1c1 0 1 1 1 2h-2c-2-1-2-1-4 0h0l-1-1 2-2z" class="j"></path><path d="M534 522h1c1 1 2 1 2 2-2 0-2 0-3 1-1 2-1 3-1 4h-1l-2-3h0v-1l4-3z" class="n"></path><path d="M537 531l-3-3 5-3c1 0 2 1 2 3h-1 0c-1 1 0 1-1 2l-2 1z" class="C"></path><path d="M568 503c1 1 4 4 6 5l-2 3-6-6 2-2z" class="B"></path><path d="M551 549c0-1 1-3 2-4h1v2h3v1 1l-1 1v3c-1-1-2-1-3-2h-1l-1-2z" class="N"></path><path d="M589 530l6 7c-3 1-4 2-7 4 0-2 0-2-2-4h1l2-2h1v-1l-1-4zm-25-15c2 0 2 1 3 2h1 0c1 1 1 1 1 2l1-1h1v2l-2 1c0 1-1 2-1 3l-4 4h-2l3-3c1-1 1-2 2-3h1v-1c-1 0-2 0-3-1s-1-2-1-4h0v-1z" class="i"></path><path d="M560 507c4 3 6 6 8 10h-1c-1-1-1-2-3-2v1h-2l-2-2-2-1 2-1-3-2 3-2v-1z" class="L"></path><path d="M560 508c1 1 1 1 1 3v1h-1l-3-2 3-2z" class="I"></path><path d="M561 512l3 3v1h-2l-2-2-2-1 2-1h1z" class="N"></path><path d="M589 541l2 2-1 1s-1 1-1 2l1 1c1-1 3-2 5-3-2 2-3 3-5 4l-2-1v1l1 1h-1c-1-1-1-3-3-4l-2 2-2 1-1 1c0 1 1 1 1 2v1h-3-1c-1 1-2 2-3 2h-1c1-1 3-2 4-3 1-2 3-3 4-4 2-1 3-3 5-4v1h1c0-1 1-2 2-3z" class="R"></path><path d="M559 504h1 0v-2h1v2l1 1c0 1 0 1-1 2h-1v1l-3 2-8 6c-1 0-3 1-5 0h-2v-2h1 0 1l3-3h1v-1l2 1c1 0 4-4 6-5h0l1-2h2z" class="n"></path><path d="M544 514l3-3h1v2l-2 2-2-1z" class="U"></path><path d="M595 537l6 7 2 2-7 7-1-1c1-2 4-3 5-6-2 1-3 1-4 0l-1-1 2-1-1-1c-1 0-1 0-1 1-2 1-4 2-5 3l-1-1c0-1 1-2 1-2l1-1-2-2c-1 1-2 2-2 3h-1v-1l2-2c3-2 4-3 7-4z" class="E"></path><path d="M595 537l6 7h-3c0-1-1-2-1-2h-2c-2-1 0-1-1-3-1 0-1 0-2 1l-3 1c-1 1-2 2-2 3h-1v-1l2-2c3-2 4-3 7-4z" class="p"></path><path d="M579 519l7 7c-2 0-3 0-4 1s-2 1-3 2c-1 0-1 0-2-1l-1 2h-2l-1-2h-1l-2-2-1 2c-1 1-1 1-2 1v-1c1-2 3-2 4-4 2-1 4-2 5-3 1 0 2-1 3-1v-1z" class="c"></path><path d="M573 528l-1-2 1-1c1 0 3 1 5 2h0 4c-1 1-2 1-3 2-1 0-1 0-2-1l-1 2h-2l-1-2z" class="t"></path><path d="M541 528l1-2 1 2-1 1v2c1 1 1 1 3 1 1 1 2 2 2 3l1 1 2 1c2 0 4-2 6-3-1 3-6 6-8 9-4-4-8-7-11-12l2-1c1-1 0-1 1-2h0 1z" class="w"></path><path d="M541 528l1-2 1 2-1 1v2c1 1 1 1 3 1 1 1 2 2 2 3l1 1 2 1-2 1c-1-1-2-1-4-3-1 0-3-3-4-4v-3h1z" class="g"></path><path d="M557 510l3 2-2 1c-2 2-4 3-6 6-1 0-2 0-3 1h1v2h-1v3c-1 0-1 0-1 1s0 1-1 2l-1 1h-4l1-1-1-2-1 2c0-2-1-3-2-3l10-9 8-6z" class="L"></path><path d="M543 525l2-2 3 3c0 1 0 1-1 2l-1 1h-4l1-1-1-2 1-1z" class="V"></path><path d="M543 525l1 1c1 1 1 2 2 3h-4l1-1-1-2 1-1z" class="W"></path><path d="M586 526l1 2c-1 1-3 2-4 3-3 2-6 5-8 7-1 1-2 1-2 2l-6 6-1-1v-1l2-2h0-3l-1 2-1-1c0-1 0 0 1-1 1 0 3 0 4-1 1 0 1-1 1-2l-1-1-1 2-2-1v-1s1-1 2-1l1-1 2-2c1 0 1 0 2-1v-2c1-1 0-2 0-3h1l1 2h2l1-2c1 1 1 1 2 1 1-1 2-1 3-2s2-1 4-1z" class="V"></path><path d="M560 483c1-3 1-6 2-8v21c1 3 4 4 6 7l-2 2-5-5c-2 1-2 1-3 2l2 2h-1-2l-1 2c-2 0-3 0-5-1l-18 16c-2 1-5 4-7 4h-1 0l34-27-4-5-6-6c2 0 3 1 5 0-1-1-1-2-2-2 1-2 1-4 1-6l3-1c0 2 1 3 1 5 1 1 2 1 3 3v-3z" class="J"></path><path d="M551 505l9-6 1 1c-2 1-2 1-3 2l2 2h-1-2l-1 2c-2 0-3 0-5-1z" class="N"></path><path d="M553 479l3-1c0 2 1 3 1 5 1 1 2 1 3 3v1h-2v2l-1 1v2h0c2-1 2-1 3-1 2 2 1 3 1 5l-1 1-1 1-4-5-6-6c2 0 3 1 5 0-1-1-1-2-2-2 1-2 1-4 1-6z" class="s"></path><path d="M557 483c1 1 2 1 3 3v1h-2v2l-1 1v-7z" class="C"></path><path d="M558 513l2 1 2 2h2 0c0 2 0 3 1 4s2 1 3 1v1h-1c-1 1-1 2-2 3l-3 3c-1 0-2 1-3 2l-1 1-1 1-1 1v1c-2 1-4 3-6 3l-2-1-1-1c0-1-1-2-2-3-2 0-2 0-3-1v-2h4l1-1c1-1 1-1 1-2s0-1 1-1v-3h1v-2h-1c1-1 2-1 3-1 2-3 4-4 6-6z" class="W"></path><path d="M551 531c1 0 1 0 2 1l-1 1h0-1v-2zm-4-3h2l1 2c-1 1-1 1-2 1 0 2 1 2 1 3v2h-1l-1-1c0-1-1-2-2-3-2 0-2 0-3-1v-2h4l1-1z" class="j"></path><path d="M558 513l2 1h-1c0 2 0 4-2 5h-1-2c-1 0-1 0-2 2h0c1 1 0 1 0 2h0l-2-1v-2h-1c1-1 2-1 3-1 2-3 4-4 6-6zm-3-20l4 5-34 27h0-2l-1-2h-1l-2-3-1-1 7-5 1-1 3-3c3-2 5-4 7-6l3 2c1-1 2-2 3-2 1-1 2-2 3-2 2-1 2-2 4-4h1c2-1 3-2 5-5z" class="V"></path><path d="M533 514l1 1c-2 1-3 2-4 3-2 2-4 2-5 4v3h0-2l-1-2s0-1 1-1l1-1c2-3 6-5 9-7z" class="W"></path><path d="M539 506c1-1 2-2 3-2 1-1 2-2 3-2 2-1 2-2 4-4 0 2 0 3 1 4l-1 1c-1 0-2-1-3 1-3 2-6 6-9 7-2 0-3 1-4 3-3 2-7 4-9 7l-1 1c-1 0-1 1-1 1h-1l-2-3-1-1 7-5 1-1 3-3c3-2 5-4 7-6l3 2z" class="i"></path><path d="M536 504l3 2-12 10-4 4h-4 0l-1-1 7-5 1-1 3-3c3-2 5-4 7-6z" class="j"></path><path d="M409 798v-1h1 1c7 4 14 8 22 11 9 5 18 10 28 13-2 5-5 10-8 15-1 2-4 5-5 7s-3 3-5 4c-2 3-5 5-7 8-11 8-25 14-38 18l-12 3c-9 1-18 2-28 2l-1-21c-1-2 0-3-2-4 2-2 7-3 10-5 6-3 11-7 16-12 7-5 12-13 17-20l2-2c1-1 1-2 2-3 2-4 3-7 5-10 1-1 1-2 2-3z" class="n"></path><path d="M427 809l1 1-1 2-11 13v-1c1-2 3-5 5-7l6-8z" class="W"></path><path d="M395 826v-1l1-1c1-1 2-3 3-4 2 0 4 1 6 3h0-1c-1 1-1 2-1 2-1 3-7 9-10 11 1-3 3-5 5-8 0-1 1-3 1-4l1-1v-1c-2 0-4 3-5 4z" class="V"></path><path d="M355 853c2-2 7-3 10-5 6-3 11-7 16-12 7-5 12-13 17-20 1 1 2 2 3 2l2 1 2 2c1 1 2 1 2 2l1 1 4 2 1 1s1 1 2 1v1h0c-2 0-3-1-4-2 0 0-1-1-2-1l-4-3h0c-2-2-4-3-6-3-1 1-2 3-3 4l-1 1v1l-1 1c-3 4-6 7-9 11h-2v2l3 6v1c1 0 1 1 1 2 1 1 2 2 2 4l1 1c2 4 6 9 6 14-1-2-2-3-3-5v-2l-4-7-1-2-1-1-4-8c-1-1-1-3-3-3-1 5-15 11-20 15-1 0-1 0-2 1 0 0-1 0-1 1-1-2 0-3-2-4z" class="i"></path><path d="M357 857c0-1 1-1 1-1 1-1 1-1 2-1 5-4 19-10 20-15 2 0 2 2 3 3l4 8 1 1 1 2 4 7v2c1 2 2 3 3 5 1 0 1 1 1 2 1 1 1 2 1 3l-12 3c-9 1-18 2-28 2l-1-21z" class="t"></path><defs><linearGradient id="Ad" x1="517.086" y1="775.424" x2="519.587" y2="531.042" xlink:href="#B"><stop offset="0" stop-color="#bbbab9"></stop><stop offset="1" stop-color="#eeeded"></stop></linearGradient></defs><path fill="url(#Ad)" d="M503 522c2 0 3-1 5-1h3c1 0 1 0 1 1l1 1c2 2 8 8 8 11h2l2 2 1 1v245l-1 39v2 38l-1 3v-1c0-2 0-3-1-5 0-2 0-5-1-8v1c0 1 0 2-1 4v1c0 1 1 1 1 3v3c-1-1-2-3-4-3h-1 0c-1 2-2 7-2 9v3c-1-2-1-3-1-5v-8-1l-2 1c0 2 0 4-1 6h0v2h-1-1c-1 1-3 2-4 2 0 2 0 3-1 5l1 3c-1 3 0 11-1 12v-1l-1-1c-1 2-1 3-2 5v1 7c0-3 0-6-1-8v-5c1-1 0-1 0-2-1-1 0-2-1-3 1-1 1-3 1-4v-9h0v-10c-3-2-1-4-2-7-1-2 0-3-1-5h0v-1c1-1 1-2 0-3v-6l1-1-1-1-1-1c2-5 0-13 0-18 1-10-1-20 0-30v-6-7l1-7v1c1 1 0 0 2 1 0-2 0-2 2-3h0v-4c1-2 1-4 1-6h1v-31-9-33-49-60-1l2-1 2 1h0v-7-2-3c-2-3 0-4-1-6v-2-2-1l-2 2h-1v-15-5-6-2z"></path><path d="M522 761c0-3-1-8 0-10 0 1 0 2 1 3v1l1 4c-1 0-1 2-2 2z" class="E"></path><path d="M508 548c1-4 2-9 3-12l1-1v-4h1c0 1 1 1 0 2 0 2 1 4 0 6v2h1c1-1 0-2 0-4h2l1 1 1-1h0c-1 2-1 2-3 2 0 4 0 7-1 11v30l-1-6c-1-1 0-3 0-5l-1-15c0-3 1-5 0-8h-2l-2 2z" class="V"></path><path d="M503 522c2 0 3-1 5-1h3c1 0 1 0 1 1l1 1c2 2 8 8 8 11h2l2 2h-2v1 1c-1 3-1 7-1 9v-4l-1 2v-2c-1-1-1-2-2-3v-4l-1-1-1 1-1-1-1-3 1-1-1 1c0 2-1 3-1 5s1 3 0 4h-1v-2c1-2 0-4 0-6 1-1 0-1 0-2h-1v4l-1 1c-1 3-2 8-3 12 1 5 0 11-1 16v-2-3c-2-3 0-4-1-6v-2-2-1l-2 2h-1v-15-5-6-2z" class="E"></path><path d="M504 530h2c1 1 1 2 0 3l-1-1c-1 0-1-1-1-2z" class="c"></path><path d="M503 522c2 0 3-1 5-1h3c1 0 1 0 1 1l-2 1c-1 2-1 4-1 6l-1 1v1 1h-1v-2c-1-1-3-1-4 0v-6-2z" class="q"></path><path d="M503 535h4l-1 1-1 1c2 1 2 4 2 6h1v-3c1-2 1-4 1-6 2-3 1-6 3-8 1 0 2 1 2 2l-1 3h-1v4l-1 1c-1 3-2 8-3 12 1 5 0 11-1 16v-2-3c-2-3 0-4-1-6v-2-2-1l-2 2h-1v-15z" class="U"></path><path d="M513 574l1 6 1 26c0 4 0 9 1 13h0v27 3 10c0 1 1 1 1 2 0 6-1 14 0 20 1 2 0 5 0 8 0 2 1 3 0 6l1 12-1 1c0-1 0-2-1-3-1 0-1-1-1-2v-1c-1-1-1-1-2-1l-1-1v4l-1-12c1-8-1-17 1-25v-1-2c1-1 1-3 1-4l1-7v-5l-1-21v-13-4-14-22z" class="i"></path><path d="M513 610l1 1c0 3 0 6 1 9v13c-1-2-1-4-1-6h-1v-13-4z" class="X"></path><path d="M513 627h1c0 2 0 4 1 6l1 28v12-1c-1-1-1-3-1-5v1l-1 5h0v-6-14-5l-1-21z" class="p"></path><defs><linearGradient id="Ae" x1="522.168" y1="679.868" x2="508.332" y2="699.632" xlink:href="#B"><stop offset="0" stop-color="#94948f"></stop><stop offset="1" stop-color="#b3b1b4"></stop></linearGradient></defs><path fill="url(#Ae)" d="M514 673h0l1-5v-1c0 2 0 4 1 5v1c0 7 0 15 1 22l1 12-1 1c0-1 0-2-1-3-1 0-1-1-1-2v-1c1-2 0-4-1-6v-6-17z"></path><path d="M512 666v-2c1-1 1-3 1-4l1-7v14 6 17 6c1 2 2 4 1 6-1-1-1-1-2-1l-1-1v4l-1-12c1-8-1-17 1-25v-1z" class="E"></path><defs><linearGradient id="Af" x1="530.268" y1="792.133" x2="506.075" y2="786.879" xlink:href="#B"><stop offset="0" stop-color="#9a9898"></stop><stop offset="1" stop-color="#cbcaca"></stop></linearGradient></defs><path fill="url(#Af)" d="M510 718l1-1v12 1 1c1 2 1 5 1 8 0 6 0 12-1 18v8 1l2-2c1-1 1 0 2-1v-3l-2-1v-3c1-3 1-4 0-6v-1c0-3 1-5 2-7l1 1c0 1-1 1-1 2-1 1-1 2-1 4 1 2 2 3 1 5v1c-1 1-1 2-1 3 2 2 2 3 2 5h1c0-2-1-4 0-6v-7h1c0 3 0 7-1 9l1 1v3l-1 1v1h1 1c0-1 1-1 2-1 0-1 1-2 1-3 1 0 1-2 2-2l-1 5-1 1h1c0 4-1 9 2 12 0 1 0 3 1 5l-1 39v1h-1l-1-1c-1-2-1-3 0-5 1-4-1-8 0-12 0-2-1-4-1-6h0c0-1-1-2-1-3 0-2-1-4-1-6v-1c-1-1 0-1 0-3-1 0-2-1-2-1v7l-1 9-3 1v1c0 3 1 6-1 9 0 1 1 4 0 6v1l1 1v1h0l-1-1h-1c-1 0-1-1-2-1v-54c1-5 1-10 1-15l-1-31z"></path><path d="M514 802h0v-6c0-4-1-9 2-12l1-1c0-1 0-2 1-3l1 1c-1 0-1 1-1 2v1 7l-1 9-3 1v1z" class="E"></path><path d="M518 784s1 1 2 1c0 2-1 2 0 3v1c0 2 1 4 1 6 0 1 1 2 1 3h0c0 2 1 4 1 6-1 4 1 8 0 12-1 2-1 3 0 5l1 1h1v-1 2 38l-1 3v-1c0-2 0-3-1-5 0-2 0-5-1-8v1c0 1 0 2-1 4v1c0 1 1 1 1 3v3c-1-1-2-3-4-3h-1 0c-1 2-2 7-2 9v3c-1-2-1-3-1-5v-8-1l-2 1c-1-1-1-2-1-3h-1v-2-6h0v-1c1-1 2-1 2-2l2-2h0c0-1-1-2-2-3v-2c1-1 1-1 2-3v-3h-2v-2h0 1l1-1c0-1 0-1-1-2l-2-2v-1c1 1 2 1 3 1h0v-1l-1-1 1-2h0v-1l-1-1v-1c1-2 0-5 0-6 2-3 1-6 1-9v-1l3-1 1-9v-7z" class="p"></path><path d="M514 802v-1l3-1 1-9c0 2 1 6 0 8-1 1-1 2-1 3v1 1 1c0 1 0 1-1 2-2 3 0 6-1 9v1l-1 2-1-1v-1c1-2 0-5 0-6 2-3 1-6 1-9z" class="T"></path><path d="M514 824c3-2 1-4 2-7 0-1 0-1 1-2v-1-5l1-1v-8c1-1 1-1 2-1 2 2 1 9 1 12v4l-1-3h-1v1 1l-1 2v7c-1 2-1 4-1 6l-1 3-2 2v-3h-2v-2h0 1l1-1c0-1 0-1-1-2l-2-2v-1c1 1 2 1 3 1h0z" class="P"></path><path d="M518 816c0-5 0-10 1-15 1 4 1 7 2 10h0v4l-1-3h-1v1 1l-1 2z" class="m"></path><path d="M518 816l1-2v-1-1h1l1 3v13 4 1 1c0 1 0 1 1 2s1 2 1 4c-1 1-1 1-1 3v3c1 1 0 2 0 3v1 1c0 1 0 2-1 4v1c0 1 1 1 1 3v3c-1-1-2-3-4-3h-1 0c-1 2-2 7-2 9v3c-1-2-1-3-1-5v-8-1l-2 1c-1-1-1-2-1-3h-1v-2-6h0v-1c1-1 2-1 2-2l2-2h0c0-1-1-2-2-3v-2c1-1 1-1 2-3l2-2 1-3c0-2 0-4 1-6v-7z" class="k"></path><path d="M510 847l1-1v1h1c1-1 0-1 2-2v4h0c-1 1-1 2-1 2v3l-2 1h-1v-2-6h0z" class="P"></path><path d="M510 847l2 2c0 1 0 1-1 2v4h-1v-2-6z" class="p"></path><path d="M521 833v1c0 1 0 1 1 2s1 2 1 4c-1 1-1 1-1 3v3c1 1 0 2 0 3v1 1c0 1 0 2-1 4v1c0 1 1 1 1 3v3c-1-1-2-3-4-3h-1l2-4c-1-2-1-3-1-5v-3c1-1 2 0 3 0 0-2-2-2-3-3 0-1-1-2-1-2-1-1-2-1-2-2v-1l2-1v-3c0 2 0 4 1 6h1c1 0 2-1 2-2l-1-3c0-1 0-2 1-3z" class="h"></path><path d="M518 850c1 0 2 1 2 2 0 3 0 4 2 7v3c-1-1-2-3-4-3h-1l2-4c-1-2-1-3-1-5z" class="v"></path><path d="M518 816l1-2v-1-1h1l1 3v13 4 1c-1 1-1 2-1 3l1 3c0 1-1 2-2 2h-1c-1-2-1-4-1-6v-1-5c0-2 0-4 1-6v-7z" class="h"></path><path d="M517 834l1-1c1-1 1-2 1-3l-1-1c0-2 0-4 1-5h1l-1 3 1 1 1 4v1c-1 1-1 2-1 3l1 3c0 1-1 2-2 2h-1c-1-2-1-4-1-6v-1z" class="v"></path><defs><linearGradient id="Ag" x1="542.138" y1="717.476" x2="474.765" y2="560.636" xlink:href="#B"><stop offset="0" stop-color="#b1b0b0"></stop><stop offset="1" stop-color="#f7f6f5"></stop></linearGradient></defs><path fill="url(#Ag)" d="M508 548l2-2h2c1 3 0 5 0 8l1 15c0 2-1 4 0 5v22 14 4 13l1 21v5l-1 7c0 1 0 3-1 4v2 1c-2 8 0 17-1 25v-7c-1 2-1 3-1 5v13c-1 1 0 1 0 2v4c-1 1-1 2-1 3v1l-1 7c-1 2 1 5 0 8-1 1-1 1-1 2v1l-1 2h0v-5c0-1 1-2 0-3 0-3-1-2-2-3l1-1c0-2-1-2-1-5 0 1 1 1 1 1l2-2-1-1v2h-1v-2h-2v-33-49-60-1l2-1 2 1h0v-7c1-5 2-11 1-16z"></path><path d="M514 648v5l-1 7c0 1 0 3-1 4v2l-1-2h0 1v-1-2-1-1h0v-8l2-3z" class="R"></path><path d="M513 596v14 4l-1-1v2l-1 1c1-2 0-3 0-5v-14l2-1zm-10-8c1 1 2 1 2 2v3s1 1 1 2c-1 2 0 4 0 5l1 14h-1c-1-2 0-4 0-6h-1c-1-2 0-5 0-7h-1l-1 1v-14z" class="t"></path><path d="M503 572v16 14c0 3 0 6 1 8 0 2-1 4-1 6 2 4 1 11 1 16h0v-3-3c1-1 1-2 1-3h1-1c-2 3 1 8-1 11 0 1 1 1 2 3v-5-9c2 3-1 9 1 12v1 3 6c-1 2 0 3 0 5 0 3 0 9-1 12h0c1 2 1 3 0 5v10 1c-2 1-2 1-3 3v-49-60z" class="U"></path><defs><linearGradient id="Ah" x1="516.476" y1="780.39" x2="494.458" y2="774.256" xlink:href="#B"><stop offset="0" stop-color="#aeaead"></stop><stop offset="1" stop-color="#dcdada"></stop></linearGradient></defs><path fill="url(#Ah)" d="M508 720l1-7v-1c0-1 0-2 1-3v9l1 31c0 5 0 10-1 15v54c1 0 1 1 2 1h1l1 1-1 2 1 1v1h0c-1 0-2 0-3-1v1l2 2c1 1 1 1 1 2l-1 1h-1 0v2h2v3c-1 2-1 2-2 3v2c1 1 2 2 2 3h0l-2 2c0 1-1 1-2 2v1h0v6 2h1c0 1 0 2 1 3 0 2 0 4-1 6h0v2h-1-1c-1 1-3 2-4 2 0 2 0 3-1 5l1 3c-1 3 0 11-1 12v-1l-1-1c-1 2-1 3-2 5v1 7c0-3 0-6-1-8v-5c1-1 0-1 0-2-1-1 0-2-1-3 1-1 1-3 1-4v-9h0v-10c-3-2-1-4-2-7-1-2 0-3-1-5h0v-1c1-1 1-2 0-3v-6l1-1-1-1-1-1c2-5 0-13 0-18 1-10-1-20 0-30v-6-7l1-7v1c1 1 0 0 2 1 0-2 0-2 2-3h0v-4c1-2 1-4 1-6h1v-31-9h2v2h1v-2l1 1-2 2s-1 0-1-1c0 3 1 3 1 5l-1 1c1 1 2 0 2 3 1 1 0 2 0 3v5h0l1-2v-1c0-1 0-1 1-2 1-3-1-6 0-8z"></path><path d="M503 714h2v2h1v-2l1 1-2 2s-1 0-1-1c0 3 1 3 1 5l-1 1c1 1 2 0 2 3 1 1 0 2 0 3v5c0 2-1 4-1 6 1 3 0 5 0 8h0c-1-2-1-4 0-5v-1l-1-1v8 3h1c0 1 0 1-1 2-1 4 1 9-1 12v-11-31-9z" class="c"></path><path d="M508 720l1-7v-1c0-1 0-2 1-3v9l1 31c0 5 0 10-1 15h-1v-7c0-7 1-14-1-21-2 3-1 8-1 11h-2c0-3 1-5 0-8 0-2 1-4 1-6h0l1-2v-1c0-1 0-1 1-2 1-3-1-6 0-8z" class="X"></path><path d="M506 733h0l1-2v-1c0-1 0-1 1-2 1-3-1-6 0-8 0 3 1 6 0 10v1 5c-2 3-1 8-1 11h-2c0-3 1-5 0-8 0-2 1-4 1-6z" class="N"></path><path d="M509 764h1v54c1 0 1 1 2 1h1l1 1-1 2 1 1v1h0c-1 0-2 0-3-1v1l2 2c1 1 1 1 1 2l-1 1h-1 0v2h2v3c-1 2-1 2-2 3v2c1 1 2 2 2 3h0l-2 2c0 1-1 1-2 2v1h0v6 2h1c0 1 0 2 1 3 0 2 0 4-1 6h0-1-1l-2-2v-6c-1-2-1-3-1-5v-2l-1-2c0-2 1-5 0-7 0-4-1-12 1-15l1-1-1-1c0-3-1-6-1-10h1v1 5-9l2-3h0v-1h-1c0-2 0-2 1-3v-1-2l1-1v-35z" class="m"></path><path d="M511 855c0 1 0 2 1 3 0 2 0 4-1 6h0-1v-9h1z" class="P"></path><path d="M510 818c1 0 1 1 2 1h1l1 1-1 2 1 1v1h0c-1 0-2 0-3-1h-1v-5z" class="X"></path><defs><linearGradient id="Ai" x1="515.117" y1="840.759" x2="506.417" y2="828.366" xlink:href="#B"><stop offset="0" stop-color="#959592"></stop><stop offset="1" stop-color="#b1aeb2"></stop></linearGradient></defs><path fill="url(#Ai)" d="M510 823h1v1l2 2c1 1 1 1 1 2l-1 1h-1 0v2h2v3c-1 2-1 2-2 3v2c1 1 2 2 2 3h0l-2 2c0 1-1 1-2 2v-8-15z"></path><path d="M509 799v48h-1v-7-2-2h0c-1 0-1 1-1 2 0 2-1 5-1 7s1 2 0 4l-1-2c0-2 1-5 0-7 0-4-1-12 1-15l1-1-1-1c0-3-1-6-1-10h1v1 5-9l2-3h0v-1h-1c0-2 0-2 1-3v-1-2l1-1z" class="b"></path><path d="M501 760c1-2 1-4 1-6h1v11 15 7c0 4 1 7 1 11h1c2 5-1 10 0 15 0 4 1 7 1 10l1 1-1 1c-2 3-1 11-1 15 1 2 0 5 0 7l1 2v2c0 2 0 3 1 5v6l2 2h1 1v2h-1-1c-1 1-3 2-4 2 0 2 0 3-1 5l1 3c-1 3 0 11-1 12v-1l-1-1c-1 2-1 3-2 5v1 7c0-3 0-6-1-8v-5c1-1 0-1 0-2-1-1 0-2-1-3 1-1 1-3 1-4v-9h0v-10c-3-2-1-4-2-7-1-2 0-3-1-5h0v-1c1-1 1-2 0-3v-6l1-1-1-1-1-1c2-5 0-13 0-18 1-10-1-20 0-30v-6-7l1-7v1c1 1 0 0 2 1 0-2 0-2 2-3h0v-4z" class="O"></path><path d="M500 868h0c0 2 0 8 1 10l2-1v-1h-2 0c1-1 1-1 2-1v-3c0 2 0 4 1 6v1 8l-1-1c-1 2-1 3-2 5v1 7c0-3 0-6-1-8v-5c1-1 0-1 0-2-1-1 0-2-1-3 1-1 1-3 1-4v-9z" class="C"></path><path d="M506 851c0 2 0 3 1 5v6l2 2h1 1v2h-1-1c-1 1-3 2-4 2 0 2 0 3-1 5l1 3c-1 3 0 11-1 12v-1-8-1c-1-2-1-4-1-6v-14h1v6h1 0l-1-2 2-3c-1-2-1-4 0-7v-1z" class="d"></path><path d="M507 862l2 2h1 1v2h-1-1c-1 1-3 2-4 2l2-6z" class="l"></path><path d="M503 829c1 3 0 8 2 11 1 2 0 5 0 7l1 2v2 1c-1 3-1 5 0 7l-2 3 1 2h0-1v-6h-1v-29z" class="f"></path><path d="M497 834c1 0 2-1 2-1v-2c0-3 0-5 1-7v34c-3-2-1-4-2-7-1-2 0-3-1-5h0v-1c1-1 1-2 0-3v-6l1-1-1-1z" class="v"></path><path d="M503 787c0 4 1 7 1 11h1c2 5-1 10 0 15 0 4 1 7 1 10l1 1-1 1c-2 3-1 11-1 15-2-3-1-8-2-11v-42z" class="E"></path><path d="M496 785c1 5 0 11 0 16 2 1 3 1 4 1v-1 9 5 9c-1 2-1 4-1 7v2s-1 1-2 1l-1-1c2-5 0-13 0-18 1-10-1-20 0-30z" class="h"></path><path d="M496 809c2 1 2 1 4 1v5c-1 0-2 0-3-1s0-3-1-5z" class="d"></path><path d="M496 801c2 1 3 1 4 1v-1 9c-2 0-2 0-4-1v-8z" class="q"></path><defs><linearGradient id="Aj" x1="491.099" y1="778.561" x2="506.87" y2="791.107" xlink:href="#B"><stop offset="0" stop-color="#8a8889"></stop><stop offset="1" stop-color="#a1a1a0"></stop></linearGradient></defs><path fill="url(#Aj)" d="M497 765v1c1 1 0 0 2 1 0-2 0-2 2-3v14l-1 23v1c-1 0-2 0-4-1 0-5 1-11 0-16v-6-7l1-7z"></path><path d="M496 772h3c0 2-1 3-1 4-1 1-1 2-2 3v-7z" class="k"></path><path d="M497 765v1c1 1 0 0 2 1 0-2 0-2 2-3v14l-1-10v-1c-1 2-1 4-1 5h-3l1-7z" class="h"></path></svg>