<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="75 12 440 680"><!--oldViewBox="0 0 568 752"--><style>.B{fill:#b8b7b7}.C{fill:#898888}.D{fill:#fefefe}.E{fill:#e9e8e9}.F{fill:#d9d8d9}.G{fill:#c4c4c4}</style><path d="M285 94h1v5h-2v1h2v4 1l-1-1c0-2-3-3-5-4 2-2 4-3 5-5v-1zm88-6c1 0 1 1 1 1 4 3 7 5 11 7 9 4 21 3 30-3l4-4v1c-1 2-3 4-5 6-5 4-12 5-18 4-9-1-17-5-23-12zm-171 0h0c0 1-4 4-5 5-6 5-15 8-23 7s-12-4-17-9l-1-1v-1c5 5 10 9 17 9 12 1 21-3 29-10zm56 39h-20-35H89v-1c3-4 7-11 13-13h0c-8-3-11-7-15-14h-1c-3 0-5-1-8-1h0c-1 2-1 4-2 6-1 5-5 9-9 12-7 3-13 1-21-1h4c2-1 5-1 7-2 6-3 13-8 15-14 0-3 0-6 1-8 0-4 1-7 2-10h1c0 3 2 6 4 8 1 1 3 1 5 1v-1c1 0 1-2 1-2v-9c0-3 2-6 3-9 4-8 12-14 21-17s20-3 28 2c8 4 11 10 14 19 2-2 5-2 8-2 2 0 4 1 6 3s2 5 2 7c-1 3-3 5-5 7v-1c1-2 2-3 1-6 0-1-1-2-2-3-2-1-4-1-6-1-2 1-4 2-4 4-1 2-1 5 0 7 2 6 7 10 12 12 8 4 18 4 26 0 11-4 14-12 19-21 1-3 0-5 0-7-1-8 2-15 6-20 6-8 15-13 25-14 11-1 21 2 30 9 8 6 14 17 15 28 0 5 0 14-4 19-3 2-6 4-8 7l7 5c-3 0-7 0-9 2h0-1c-1-1 0-3 0-5-4 1-8 2-13 3 4-4 7-7 8-11 2-8-1-14-5-21 3 2 5 5 7 8 2 6 1 11-1 17 2-2 3-4 4-7 1-8-2-17-6-24-4-6-11-10-18-12-7-1-16-1-22 4-4 2-7 6-7 11-1 2 0 6 1 9 2 2 5 4 9 5 6 1 10-2 14-6 2-1 2-3 4-4l-1 2c-1 5-4 9-8 11s-11 3-15 1c-5-1-8-5-11-9 0 3 1 7 0 10-3 9-12 15-20 19 5 3 10 3 16 3 5 0 11-1 16-2 10-2 21-7 27-16 4-7 2-15 0-22 3 2 5 3 7 6 1 2 2 6 2 9 0 8-5 14-11 19-1 1-2 2-3 4l3 3c3 1 11 1 14 1 4-1 6-4 11-2h0l1 1c0 1 0 1 1 1 1-1 2-1 3 0l2 2c0-2 1-2 2-3 1 0 2-1 3-2h3c1-1 3-1 4 0h1 0c1 0 1 0 2-1 0-1 0-2-1-4h1s0 1 1 1h0 0c1 0 1-1 1-1 2-2 4-3 7-4-3-3-7-6-9-9-3-4-4-10-4-14 0-12 5-23 14-31 8-7 18-10 29-10 9 0 18 4 25 11 6 7 8 15 8 25 0 3 1 5 2 8 2 4 3 7 6 11 6 6 15 10 24 10 6 0 14-1 19-6 3-3 7-8 7-13 0-2 0-3-2-5s-3-2-6-2c-2 0-3 0-4 2-1 1-2 2-2 4s1 2 1 3c1 1 1 1 1 2-2-2-3-3-4-5l-1-1c0-1 0-4 1-6 0-2 2-4 4-4 3-1 6-1 9 0l2 1c3-8 6-15 14-19 9-5 21-5 30-1 8 3 16 9 19 17 2 4 3 8 3 12 1 2 0 6 1 8h1c1 0 3 0 4-1 2-2 4-4 4-6l1-2c1 2 2 5 2 8 1 3 0 6 1 9 1 7 8 12 14 15 3 1 7 2 11 2-5 2-12 3-17 2s-9-5-11-9c-2-3-2-7-3-10h-1c-2 0-4 1-6 1h-2c-1 0-1 2-2 3-3 5-7 8-12 11h-1c1 0 2 1 3 1 5 2 8 8 10 13H358h-27-10 0c-1 1-1 1-2 1h-1c-2 1-4 0-5 1h-2c0 1 1 1 2 2 0 1 0 1 1 1v1h1c1 1 3 2 5 2h0c2 1 4 1 6 1h11c1 0 3-1 4 0h1l1 1h103 27c6 0 13 0 18-1 1-1 2-2 2-3v-1h1c2 1 5 2 5 5 1 1 1 3 0 5-2 2-7 3-9 3l-166 2h0c1 1 3 1 5 1 12 1 26 1 39 1h76 18c3 0 7-1 10 0s7 3 9 4v1c-1 1-2 1-4 1-3 1-8-4-10-1-2 2-3 4-5 5 6 0 20 1 25-1l1-2c2 1 3 1 4 3 0 1 0 1-1 2-1 4-6 4-9 5-6 1-11 0-17 0 0 1-1 3-2 4l12 6c8 5 14 13 16 22 4 13 3 31-3 42-4 8-11 14-19 17-4 1-9 1-12 1-3-1-5-2-6-4-1-1-2-3-1-4 0-1 1-2 1-3 2 0 2 0 3 1 0 2 1 3 2 3 4 1 9 0 12-2 7-4 12-11 15-18-5 2-10 5-15 4s-9-5-12-9l-2-3-6 12c-1 1-2 2-2 3-1 1 0 3-1 4v10 39 78l-1 71v-15-23l-2-82v-19c0-2 1-5 0-8 0 0-1 0-1-1h0v-2-1-3c1-2 0-5 0-7h0-2c-1 0-2 1-3 1v10c0 2 1 4 0 5h0l-2 196h0c-3 1-5 4-8 6l-10 9c-24 21-48 45-68 71v-1c8-12 9-31 6-45l-9-27h-2v1c3 8 7 16 8 24 1 4 1 8 1 12-1-2-2-5-3-7-2-4-6-7-10-8-3-1-7 0-9 2-2 1-4 4-4 6-1 4 0 7 2 11 0-2-1-4-1-6-1-2 0-6 2-8 2-1 4-2 6-2 3 0 7 1 9 3 3 3 4 8 4 12 1 4 0 9-3 12-5 5-12 5-18 5 4 1 9 2 14 0 5-1 9-5 11-10 0 3 0 7-1 10-2 9-6 17-13 22-11 9-23 11-36 9-5 0-11-1-13-5-1-2-1-3-2-5l-1 1c2 3 2 5 4 8 5 4 12 4 18 4-1 0-3 1-5 2-4 1-9 3-12 7-2 3-3 5-3 9 0 2 1 5 3 6 2 2 4 2 6 2 4 0 6-3 8-6-2 1-4 4-7 5-2 0-4-1-6-2-1-1-2-3-2-5-1-2 0-4 1-6 2-2 4-4 7-5 9-5 20-6 30-10 6-2 13-7 17-12h1l-17 23c-12 17-24 36-33 56-3 5-6 11-6 17l15-26c28-45 65-85 104-120l9-8c1-1 3-3 4-3l-33 31c-29 31-58 64-81 100-7 12-15 25-21 38-9-21-22-40-35-59-16-23-34-44-54-65l-33-33-9-9c-1 0-3-2-3-3 8 6 15 13 22 20 33 30 63 64 89 101l16 26c2 3 3 6 5 9v-1c-2-6-5-12-8-18-12-25-27-48-44-70l-8-9c-2-2-3-5-5-8-17-19-35-38-53-55l-16-15c-3-3-7-6-10-8h0l-1-324h75 26c6 0 13-1 19 0 3 0 7 1 10 2 1 0 1-1 2 0h1 1l1 1h1c0-1 1-1 2-3 0-1 1-1 1-2l-6-3h-1 0c-1-1-2-1-3-2-8-2-19-2-27-2h-33-76v191l-1 50v15c0 3 0 6-1 10v-16-41l-1-104v-35c0-7 0-14-1-21-5-3-9-3-15-3-1 0-3 2-3 3-1 1 0 5 0 6 0 2 0 2-1 3h-2c-2-1-2-3-3-5 0-2 0-5 2-7 1-3 4-4 7-5 6-1 11 1 15 4v-25l-11 1c-9 1-17 5-23 12-4 6-6 14-5 21 1 4 4 8 7 10s7 3 11 2c5-1 8-6 9-11 0-1 0-4 1-5 1 0 2 0 2 1 1 0 2 2 2 3 1 5 0 10-3 14-2 4-7 8-12 9-6 1-11-2-15-5 3 7 7 13 13 18 3 2 8 4 13 3 2 0 4-3 5-4h0c1 0 1 0 2 1 0 1 1 2 0 4 0 2-2 3-3 4-4 3-11 2-16 1-8-2-15-9-19-16-6-12-7-30-4-42 3-10 9-17 17-23 4-2 8-4 11-6l-1-4c-7 0-15 1-22-1-2-1-4-2-4-4-1-1-1-1 0-2 0-1 1-2 2-3l1 1h0c5 3 20 2 25 2-2-2-2-4-4-6-2-1-7 2-8 3-2 0-3 0-5-1 0 0-1 0-1-1 2-2 6-4 9-5h7 12 84 24c5 0 11 0 16-1 2 0 4 0 5-1 0-1-16-1-18-1H104c-6-1-13 0-19-1-3 0-7-1-8-3s-1-3-1-5c1-3 3-4 6-5h0v2h0c1 1 3 2 4 2l159 1 2-1c2-1 5 0 7 0 2-1 4-3 6-3 0-1 1-1 1-1h0c2-1 3-2 4-3h-2c0-1-1-1-2-1-2 0-2 0-3-1z"></path><path d="M268 151c0-1-1-1-1-2v-2c1-2 2-3 3-3 0 2-2 4-2 7z" class="C"></path><path d="M373 293v-11c0-3 0-6 1-9 0 5 0 9 1 13 0 2 0 5-1 7h-1zm-173-1c-1-5 0-13 1-18h0c1 4 0 9 0 14v5l-1-1z" fill="#242425"></path><path d="M174 305h0 0c1 0 1-1 2-1h0v-2c1 3 2 5 2 8l-3 3c0-3 1-6-1-8z" class="E"></path><path d="M201 266h0c-3-5-3-9-2-14 0 1 1 1 1 1 0 3 1 6 2 9v4h-1z" class="B"></path><path d="M352 303c4 2 10 4 12 9v1l-3-3c-3-3-7-3-10-4l-1-1 2-2z" class="E"></path><path d="M246 195h0 4c0 3 2 6 1 9h-1l-1-1c0-2-3-3-4-4 0-1 0-1 1-2v-1-1z" class="C"></path><path d="M176 351c-1-1-1-3-1-5-1-6 0-9 3-14 0 2-1 4-1 6-1 4 0 8 1 11 0 1 0 1-1 2h-1z" class="E"></path><path d="M407 350c2-7 1-15 4-22 0 3-1 5-1 8 0 2 1 4 1 5 0 4-1 6-2 9-1 1-1 0-2 0zm-229-1c2 5 5 9 9 13h-2c-3-2-7-5-8-8 0-1-1-2-1-3h1c1-1 1-1 1-2z" class="F"></path><path d="M250 265c2-7 3-13 3-21h1c1 2 1 4 1 6 0 5-1 11-3 15 0 1 0 1-1 1v-1 1h-1v-1zm75-70h2c0-1 0-1-1-2h0-1l1-1c1 0 1 1 2 2v1h0 2 1c1 1 2 1 3 2l-5-1h-1c1 1 1 1 1 2l-3 4c-1 1 0 2 0 3h-1s-1-2-1-3c-1-2 0-3 1-5l1-1-1-1z" class="C"></path><defs><linearGradient id="A" x1="347.415" y1="405.895" x2="342.655" y2="413.349" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#757475"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M346 405c1 1 2 2 2 3v1h1l-3 3c0 1 0 1-1 2l-4-1c-1 1-1 3-3 4h0c1-4 5-8 8-12z"></path><path d="M409 350c2-2 4-4 5-6h0c0 2-1 3-1 4v1c1 0 2-2 3-1-2 3-6 7-10 8v-1-1-3l1-1c1 0 1 1 2 0z" class="G"></path><path d="M359 254h0c1 3 1 4-1 6h0c1 3 2 5 4 7l2 1h0l-1 1c-1 0-2-1-3-1-1-1-3-2-4-4 0-1-2-4-1-4 0-1 1-1 1-2 1-1 2-2 3-4zm-1 180c2-3 2-5 1-9 0-1-1-3-1-4 2 2 6 5 6 8 0 1 0 2-1 4 0 0-1 1-1 2h0l-1 1-3-2z" class="C"></path><defs><linearGradient id="C" x1="336.902" y1="395.143" x2="334.735" y2="411.214" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#777678"></stop></linearGradient></defs><path fill="url(#C)" d="M331 414c2-7 4-16 10-21 0 3-2 6-3 8l-5 13v-3l-1 1c0 1 0 1-1 2h0z"></path><path d="M215 253h0c0 1 1 2 1 3 1 1 3 2 5 2v1c-1 2-1 4-1 7-2 0-4 1-6 2-1 0-2 1-3 1v-1l3-2c1-1 2-2 2-4 1-2 0-4-2-5v-1c0-1 0-2 1-3z" class="B"></path><defs><linearGradient id="D" x1="354.454" y1="409.309" x2="353.615" y2="414.923" xlink:href="#B"><stop offset="0" stop-color="#5a5a5b"></stop><stop offset="1" stop-color="#737374"></stop></linearGradient></defs><path fill="url(#D)" d="M346 412c4-2 9-4 14-2 2 0 4 2 5 4s1 5 0 7c-1-4 0-7-5-9-3-1-7 0-10 1-2 1-3 1-4 2v-1h-1c1-1 1-1 1-2z"></path><path d="M224 269c2-1 5 0 7-1 3-1 6-3 8-6 1-1 1-2 2-3-2 7-6 13-12 16h0c0-2 0-4 1-6h-6z" class="C"></path><defs><linearGradient id="E" x1="370.14" y1="344.918" x2="370.711" y2="339.877" xlink:href="#B"><stop offset="0" stop-color="#989699"></stop><stop offset="1" stop-color="#b9b7b8"></stop></linearGradient></defs><path fill="url(#E)" d="M381 337c-1 0-2 0-3 1-3 1-7 4-8 8-1 2-1 4-2 6v-3c0-5 0-10 4-13h0 5 2 1l1 1z"></path><path d="M377 448c1 0 2 0 3 1 1 0 1 2 2 3 0 1 0 1 1 2-1 1-1 2-2 3s-3 3-5 3c-1 0-1-1-2-1l1-7h0c0 1 0 1 1 2 1 0 2-1 3-2-1-2-1-3-2-4z" class="G"></path><path d="M325 505c2 0 4 0 6 1 3 2 5 6 6 9s2 8 1 11c-2-3-1-6-2-9 0-1 0-3-1-4 0-1-1-2-2-3-1-2-3-3-5-3s-4 1-5 2c-3 2-3 5-3 8h-1v-7c1-2 4-4 6-5z" class="E"></path><path d="M213 435v-1-2c-1-1 0-3 0-4l6-6c-1 4-4 8-2 12 2 3 5 6 7 8h-1v1h0c-1 0-2-1-2-2l-3-3c-2-1-4-1-5-3z" class="B"></path><defs><linearGradient id="F" x1="336.443" y1="413.721" x2="324.883" y2="429.757" xlink:href="#B"><stop offset="0" stop-color="#7e7c80"></stop><stop offset="1" stop-color="#babbba"></stop></linearGradient></defs><path fill="url(#F)" d="M331 414h0c1-1 1-1 1-2l1-1v3l-3 17h0c0 1-1 1-1 2l-1-1c0 3 0 5-1 7 0-8 1-17 4-25z"></path><path d="M246 505c2-1 4-1 5 0 2 1 5 4 5 6 1 2 1 4 0 6h-1c0-3 0-6-2-8-1-1-3-2-5-2-3 0-5 1-7 3-3 5-2 9-3 13-1 1-1 2-2 2h0c0-4 1-8 2-12 2-4 4-7 8-8z" class="D"></path><path d="M189 194h0c0-1 1-3 2-4 6 6 10 13 11 22-3-4-5-9-8-13-2-2-4-3-5-5z" class="B"></path><defs><linearGradient id="G" x1="358.192" y1="399.88" x2="346.795" y2="405.383" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#G)" d="M346 405c3-3 5-5 9-7 2 0 4 2 5 4h0c-4 1-8 4-11 7h-1v-1c0-1-1-2-2-3z"></path><defs><linearGradient id="H" x1="313.617" y1="260.778" x2="326.652" y2="270.3" xlink:href="#B"><stop offset="0" stop-color="#6d6f6d"></stop><stop offset="1" stop-color="#949295"></stop></linearGradient></defs><path fill="url(#H)" d="M322 278l-1-1c-3-7-4-15-3-23v-8c1 4 1 7 1 10 1 7 3 13 6 19-1 0-2 1-3 1v1 1z"></path><defs><linearGradient id="I" x1="321.284" y1="249.579" x2="329.68" y2="279.224" xlink:href="#B"><stop offset="0" stop-color="#515153"></stop><stop offset="1" stop-color="#999"></stop></linearGradient></defs><path fill="url(#I)" d="M330 278h0c-4-4-6-9-8-14-1-5-3-13-1-18 1 3 1 6 1 9 1 9 6 16 11 22-1 1-1 1-1 2-1-1-2-1-2-1z"></path><path d="M189 194c-1 2-2 4-2 6-1 5 1 9 4 13h-1c-2-2-5-6-5-9-1-5 1-12 4-16s7-5 11-5c1 0 4 3 5 3-2-1-5-2-7-1-3 0-5 3-7 5-1 1-2 3-2 4h0z" class="E"></path><path d="M403 309h-2c-3 0-5 4-7 6 1-4 2-10 5-13 2-2 4-2 7-2 0 4-1 6-3 9z" class="D"></path><defs><linearGradient id="J" x1="373.069" y1="437.412" x2="353.536" y2="437.734" xlink:href="#B"><stop offset="0" stop-color="#8b898b"></stop><stop offset="1" stop-color="#bab8b9"></stop></linearGradient></defs><path fill="url(#J)" d="M362 435h8 3l3-3v2h-1s-1 1-1 2c1 0 1 0 2-1v-1c1 1 1 2 1 3h-2l-1 2c-1-1-2-1-3-1h-8c-1 1-2 0-2 1h-1c-1 0-2 1-2 1h-2c-1 0-1 1-2 1v-1h-1l5-6 3 2 1-1z"></path><defs><linearGradient id="K" x1="358.4" y1="354.475" x2="347.995" y2="334.921" xlink:href="#B"><stop offset="0" stop-color="#797a7a"></stop><stop offset="1" stop-color="#acabac"></stop></linearGradient></defs><path fill="url(#K)" d="M344 337c4 0 7 1 10 2 6 4 10 9 11 16-2-2-4-5-6-8-2-2-4-3-6-5-3-1-6-3-9-3-2 0-4 0-6 2-3 3-2 8-3 13h0v-1c-1-3 0-8 2-11 1-3 4-4 7-5z"></path><defs><linearGradient id="L" x1="436.049" y1="249.188" x2="441.313" y2="263.234" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#a5a5a6"></stop></linearGradient></defs><path fill="url(#L)" d="M436 268c0-5-1-12 1-16 2-3 4-5 5-8v22h0c-1-1-1-1-1-2-1 0-3 1-5 1v3z"></path><defs><linearGradient id="M" x1="343.993" y1="412.661" x2="330.539" y2="434.406" xlink:href="#B"><stop offset="0" stop-color="#7b7a7a"></stop><stop offset="1" stop-color="#bebebf"></stop></linearGradient></defs><path fill="url(#M)" d="M338 417h0c2-1 2-3 3-4l4 1h1v1c-8 7-12 17-13 27 0-1 0-2-1-3l1-2v-1h0 0l-1 1c0 1 0 2-1 3v1c0-9 4-17 7-24z"></path><path d="M258 127c5 0 10 2 14 6l3 3-20 1h-10l2-1c2-1 5 0 7 0 2-1 4-3 6-3 0-1 1-1 1-1h0c2-1 3-2 4-3h-2c0-1-1-1-2-1-2 0-2 0-3-1z" class="G"></path><path d="M209 359c1-7 3-13 9-17 4-3 9-5 13-5 3 1 5 3 7 5s2 7 2 10c0 0-1 0-1 1 0-4 0-9-3-12-2-1-4-2-6-1-6 0-11 4-14 8s-4 7-5 11l-1-1-1 1z" class="B"></path><path d="M442 161l-1 41-6 2c0-5 1-35 0-37h0v19l1 1v4c1 0 1-1 1-2h1v2c1 0 2 0 2-1v-1c1-1 1-2 1-3l1-25z" class="C"></path><path d="M435 186l1 1v4c1 0 1-1 1-2h1v2c1 0 2 0 2-1v-1c1-1 1-2 1-3v10l-5 1-1-11z" class="F"></path><defs><linearGradient id="N" x1="307.03" y1="139.983" x2="315.495" y2="130.832" xlink:href="#B"><stop offset="0" stop-color="#b5b4b6"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#N)" d="M343 137l-42-1c0-1 1-2 2-3 6-5 11-6 18-6h0c-1 1-1 1-2 1h-1c-2 1-4 0-5 1h-2c0 1 1 1 2 2 0 1 0 1 1 1v1h1c1 1 3 2 5 2h0c2 1 4 1 6 1h11c1 0 3-1 4 0h1l1 1z"></path><defs><linearGradient id="O" x1="436.713" y1="264.681" x2="440.506" y2="278.267" xlink:href="#B"><stop offset="0" stop-color="#adadad"></stop><stop offset="1" stop-color="#d6d5d6"></stop></linearGradient></defs><path fill="url(#O)" d="M436 268v-3c2 0 4-1 5-1 0 1 0 1 1 2h0v27s-1 0-1-1h0v-2-1-3c1-2 0-5 0-7h0-2c-1 0-2 1-3 1v10c0 2 1 4 0 5h0v-27z"></path><defs><linearGradient id="P" x1="320.454" y1="278.152" x2="337.783" y2="291.378" xlink:href="#B"><stop offset="0" stop-color="#989898"></stop><stop offset="1" stop-color="#c5c4c4"></stop></linearGradient></defs><path fill="url(#P)" d="M322 278v-1-1c1 0 2-1 3-1 5 7 10 13 18 17-2 0-3-1-5-2v1c-1 2-3 3-3 6-5-6-10-12-13-19z"></path><path d="M225 387c5 2 12 15 14 20v1c2 4 4 8 5 12v2 3l1 8c1 1 0 2 0 3 1 1 1 1 1 2v8l-1 1c0 2 1 5 0 6v6c-1 2-1 5-1 7-1 4-2 8-4 11 0 3-1 5-1 7l-4 10c-2 3-3 6-4 10 1-1 1-1 1-2 1-1 1-2 2-3 0-1 1-2 1-4 0-1 1-1 1-3h0c1-1 1-1 1-2l1 1-13 30h-1-1c2-5 4-10 6-16 5-12 11-25 13-37 2-8 3-15 3-23s0-17-2-25c-3-13-10-23-18-33z" class="E"></path><defs><linearGradient id="Q" x1="373.073" y1="195.09" x2="388.429" y2="192.555" xlink:href="#B"><stop offset="0" stop-color="#bbb9b9"></stop><stop offset="1" stop-color="#dbdadb"></stop></linearGradient></defs><path fill="url(#Q)" d="M376 183c3 0 7 2 10 5 3 2 5 9 5 13 0 5-2 7-5 10 0-2 2-3 2-5 1-4 0-8-2-12-2 1-3 3-4 4-4 4-6 8-9 13 1-2 1-4 1-6 2-6 6-10 10-15l-4-4c-4-2-6-1-10 0v-1c2-1 3-2 6-2z"></path><defs><linearGradient id="R" x1="256.855" y1="268.137" x2="242.958" y2="267.917" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#b6b6b7"></stop></linearGradient></defs><path fill="url(#R)" d="M240 286c9-9 16-21 16-33 0-3 0-7 1-10 3 13 0 28-7 39l-4 7c-1-2-2-2-3-3v-1c-1 0-2 1-3 1z"></path><path d="M202 262c2 4 5 7 10 9 3 1 7 1 10 0l2-2h6c-1 2-1 4-1 6-3 2-5 3-9 3-2 0-4 0-5-1-6-2-11-6-14-11h1v-4z" class="C"></path><path d="M222 271c1 0 2 0 2-1 1 1 1 1 1 2l-1 1v1h-2c-1 0-1 0-2 1-2 0-7-2-9-3l1-1c3 1 7 1 10 0z" class="G"></path><path d="M224 269h6c-1 2-1 4-1 6-3 2-5 3-9 3l2-1h0v-1h-3 0 1c2-1 3-1 4-3l1-1c0-1 0-1-1-2 0 1-1 1-2 1l2-2z" class="B"></path><path d="M201 266h1c3 4 8 7 13 9h1c1 0 2 0 3 1h0 3v1h0l-2 1c-2 0-4 0-5-1-6-2-11-6-14-11z" class="F"></path><defs><linearGradient id="S" x1="340.091" y1="466.19" x2="330.155" y2="469.33" xlink:href="#B"><stop offset="0" stop-color="#b9b9ba"></stop><stop offset="1" stop-color="#f1f2f2"></stop></linearGradient></defs><path fill="url(#S)" d="M327 439c1-2 1-4 1-7l1 1c0-1 1-1 1-2h0c-1 14-1 27 3 40 3 11 7 22 12 33h-2v1c-3-6-5-12-8-18-5-15-9-32-8-48z"></path><defs><linearGradient id="T" x1="345.732" y1="264.554" x2="378.644" y2="298.477" xlink:href="#B"><stop offset="0" stop-color="#979896"></stop><stop offset="1" stop-color="#bfbec0"></stop></linearGradient></defs><path fill="url(#T)" d="M374 293c6-1 10-4 14-8 0-3-2-6-3-10 0-2 0-4 1-6 0 2 0 5 1 7 1 3 4 6 5 10-1 2-2 3-3 4-7 6-18 6-27 5-12-1-24-8-32-17 0 0 1 0 2 1 0-1 0-1 1-2 2 2 4 5 7 7 4 3 9 5 13 6 2 1 5 1 7 2 3 2 9 1 13 1h1z"></path><path d="M243 42c6 0 11 1 16 3 8 4 14 12 17 20 1 2 2 5 1 7-2-1-3-6-4-8s-2-3-3-5l-6-6c-4-3-9-6-15-7-8-2-16-1-23 3-3 1-5 3-7 5l1-2c6-7 14-10 23-10zm84 0h8c9 1 16 5 22 12-4-3-8-6-13-7-10-3-21-1-30 4-5 3-10 8-12 14-1 2-2 4-4 6h0c-1-5 3-12 6-16 6-8 14-12 23-13z" class="D"></path><defs><linearGradient id="U" x1="355.801" y1="259.142" x2="357.029" y2="276.482" xlink:href="#B"><stop offset="0" stop-color="#919190"></stop><stop offset="1" stop-color="#bbb9bb"></stop></linearGradient></defs><path fill="url(#U)" d="M375 255h1 0c1 2 0 6 0 8-2 5-6 9-11 12-5 2-10 3-15 2-7-2-12-8-15-14-1-1-1-3-2-5 2 3 4 7 8 9 3 1 5 2 8 1h2v2c4 3 9 1 13 1 1 0 2-1 3-1 5-4 7-9 8-15z"></path><path d="M231 581c1 0 3 3 5 3 7 5 16 8 25 9 7 0 16 0 22-5 1-1 2-3 3-5 0 2-1 4-2 6-4 5-11 5-17 6 4 2 9 3 12 5s7 5 7 9h0c1 2 1 4 0 5-1 2-2 5-4 6s-4 1-7 1c-3-2-5-4-7-8 2 3 5 6 9 7 2 0 3-1 5-2 1-2 2-3 2-5s0-4-1-6l-4-4c-10-7-22-6-33-11-5-2-11-7-15-11z" class="D"></path><defs><linearGradient id="V" x1="234.185" y1="254.205" x2="199.337" y2="298.857" xlink:href="#B"><stop offset="0" stop-color="#949391"></stop><stop offset="1" stop-color="#bbbabc"></stop></linearGradient></defs><path fill="url(#V)" d="M250 265v1h1v-1 1c-2 10-12 18-21 23-9 6-22 8-33 5-6-1-11-3-14-9 1-3 4-6 5-10 0-2-1-5 0-7 2 7 1 11-2 17 0 1 6 5 7 5 2 1 5 2 7 2l1 1c8 0 15 0 22-3 12-4 22-13 27-25z"></path><path d="M340 177h1c3 0 8 3 11 4 9 7 14 19 16 29-1-2-2-5-4-6-2-2-5-3-7-4h-1c-1 1-1 1-1 2h0c0-1 0-3-1-4l1-1 3 1v-1-2c-2-5-7-6-11-8l-6-4c-1 0-2 1-3 2-4-2-7-4-10-7 2 0 3 1 4 1 3 1 6-1 8-2z" class="G"></path><path d="M230 177h0 1c2 0 4 1 6 2s5 0 7-1h1c-3 3-6 5-9 7l-3-1h-1c-5 2-11 5-14 9-1 1-2 2-2 3 1 1 3 1 4 2v1c-1 1-1 2-2 2s-1 0-1-1v-1c0 1 0 1-1 1-4 1-7 6-9 10 0-6 2-12 5-18 4-7 10-12 18-15zm66-35v1c3 1 5 1 7 1 1 0 3 0 4 1 1 0 1 1 2 2 0 1 0 2-1 3s-1 1-3 2c-4 0-10 0-13 4l-4 5c-2-4-5-8-9-9-3-1-9 1-11-1 0-3 2-5 2-7h7c-2 1-3 1-4 2h0 1l1 1c1 1 2 0 3 1h0c3 2 5 3 8 5v-2c0 1 1 2 1 2h2c2-1 1-5 1-7h0l1-4 3 4v-1h0c1-1 1-2 2-3z" class="B"></path><path d="M290 146l1-4 3 4c1 1 2 2 3 2 2 0 5-1 7-1v1 1c-2 1-5-1-8 0-2 0-4 3-6 4v-7z"></path><path d="M209 359l1-1 1 1c8 0 13 3 18 8 13 15 17 38 18 57 1 9 2 18 1 27-1 14-5 27-10 40l-1-1c1-1 1-2 1-2v-1c1-1 2-3 2-4s1-2 1-3v-1l1-1v-2h0c1-1 1-1 1-2v-1-1c0-1 0-1 1-2h0v-3c1 0 1 0 1-1v-1s0-1 1-1c0-1-1-1 0-2h0v-1c0-2 0-4 1-5v-5c1-2 0-3 0-4s1-1 1-2c-1-2 0-4-1-5v-1c0-1 1-2 1-3-1-1-1-1-1-2 1-2 0-4 0-6v-2-2c0-1 0-1-1-2 0-1 1-4 0-5-1-2 0-4-1-5v1h-1-1c0-2-1-3-1-4h0v-1c-1-1-2-2-2-4l-3-6c-2-4-6-8-9-12-1-1-3-3-5-4-2-2-5-3-6-5h0 1c2 1 5 4 7 5 9 8 15 19 19 30 0-2 0-4-1-6-1-12-6-25-14-35-4-5-10-9-17-9-5-1-8 2-12 5v-1-1c1-2 3-4 5-5 1 0 2 0 4-1h0z" class="F"></path><defs><linearGradient id="W" x1="289.818" y1="180.319" x2="292.894" y2="159.448" xlink:href="#B"><stop offset="0" stop-color="#b0afb0"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#W)" d="M269 160c8 1 13 5 18 10 2-1 4-3 6-5 6-4 12-5 19-6h-1c-1 2-4 2-6 2h1c0 2 1 5 1 6h5c-4 1-8 2-11 3-5 3-10 7-14 12-5-7-10-11-17-13 1 0 1-1 2 0h1 1l1 1h1c0-1 1-1 2-3 0-1 1-1 1-2l-6-3h-1 0c-1-1-2-1-3-2z"></path><path d="M325 195c-5-1-9-1-13 2-1 0-2 1-3 3-1 1-2 5-4 6h-2v-1h2c1-3 0-7 0-9 1-2 5-4 7-5-2 0-4 1-5 1s-3 0-4 1c-4 1-8 4-11 6 3-3 6-5 9-7 1-1 3-3 4-3 3-1 6-1 8-2 12-1 24 4 33 12 4 3 8 6 10 11-7-5-14-11-22-13-1-1-2-1-3-2h-1-2 0v-1c-1-1-1-2-2-2l-1 1h1 0c1 1 1 1 1 2h-2z" class="B"></path><path d="M223 521h1 1c-2 7-2 13-3 20 1-1 1-3 2-5 1-3 4-7 7-9 3-1 7-1 10-1 3 1 5 3 7 6 1 2 1 5 1 8-1 1-2 3-3 4 1-2 2-4 2-6s-1-4-2-6c-2-2-5-3-7-3-3 0-6 1-8 3-4 2-5 7-6 11 0 5 1 9 5 13 5 5 12 4 18 4-4 1-9 2-13 2-5-1-10-5-13-10 2 8 3 16 9 22l-5 2c-1-5-3-9-4-13-4-13-3-29 1-42z" class="D"></path><path d="M423 180h1v1h-1c-3 2-3 4-5 7-2 1-4 2-7 3 2 2 5 5 6 8 1 2 2 6 0 8-1 2-5 2-7 3l3-6c0-3-2-6-4-8-2-3-5-5-9-6s-7 0-10 2h0l-3-5c2-2 6-4 9-5 4-1 9 0 14 0 1 1 4 1 6 1 2-1 4-2 7-3zm-244 3c3 0 7 2 9 4-1 2-2 3-3 5-2-1-4-2-6-2-4-1-7 0-10 3-3 1-6 5-7 8 0 4 1 6 3 8h-6c-2-2-3-3-3-5 0-6 4-10 7-13-2-1-5-1-6-3-2-2-1-5-4-7h-2v-1c2 0 4 1 6 2 7 1 15-2 22 1z" class="F"></path><path d="M171 183h1l-1 1h1 0v-1c1 1 1 1 1 2-2 0-3 0-4 2h0c-1 0 0 0-1 1-1 0-1 0-2 1-1-1-2-1-3-2 2-2 5-3 7-4h1z" class="G"></path><path d="M157 182c7 1 15-2 22 1h-8 0-1c-2 1-5 2-7 4h-1c-2 0-4-3-5-5z" class="B"></path><defs><linearGradient id="X" x1="246.225" y1="206.055" x2="244.103" y2="188.483" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#b9b7b8"></stop></linearGradient></defs><path fill="url(#X)" d="M246 195c-2 0-5 1-7 2-1 1-2 2-4 3 1-1 2-2 3-4-8 4-14 9-20 14 2-5 4-8 8-11 10-9 23-13 36-12 7 0 11 3 16 7 2 2 4 3 6 5l-8-5c-1-1-3-2-5-2-3-1-5-2-9-1 2 1 5 2 8 3v3c-1 2-1 5-1 7 0 1 1 1 2 1v1h-2c-2-2-3-5-4-7-3-3-7-4-10-4h-5-4 0z"></path><path d="M331 441v-1c1-1 1-2 1-3l1-1h0 0v1l-1 2c1 1 1 2 1 3 0 16 2 30 9 44l6 9 2 1h0 2c4 6 9 9 16 10 3 1 5 0 8 0 0 3 0 7 1 10h-6c-9-2-20-9-25-17-10-13-14-29-15-45 0-4-1-9 0-13z" class="F"></path><defs><linearGradient id="Y" x1="377.857" y1="443.557" x2="337.055" y2="480.473" xlink:href="#B"><stop offset="0" stop-color="#a2a1a2"></stop><stop offset="1" stop-color="#e4e3e4"></stop></linearGradient></defs><path fill="url(#Y)" d="M353 440h1v1c1 0 1-1 2-1h2s1-1 2-1h1c0-1 1 0 2-1h8c1 0 2 0 3 1h3c0 1 0 2 1 2-2 1-4 0-5 1 3 1 7 2 9 5 1 2 1 4 1 7-1-1-1-1-1-2-1-1-1-3-2-3-1-1-2-1-3-1-4-3-7-5-12-5-4 0-8 2-10 5-3 2-4 5-5 8 3-1 6-2 8-1s3 2 4 4c0 1 0 2-1 2h0c0-1-1-3-3-3-1-1-3-1-5 0-3 2-5 5-6 8-3 10 0 21 5 30h-2c-6-7-8-16-8-25 0-8 1-18 5-25l6-6z"></path><path d="M406 300c4 2 8 4 11 7 1 1 2 3 3 4 0-2 0-4 1-7 2-3 4-5 7-7-1 3-2 5-3 8 0 4 1 9 1 14 1 9-3 22-10 29-1-1-2 1-3 1v-1c0-1 1-2 1-4h0c0-1 2-4 2-5 3-7 1-15-2-22-2-4-6-7-11-8 2-3 3-5 3-9z" class="E"></path><path d="M108 117h46 55 25c7 0 14 0 20 2-3 1-8 1-11 1l-146 1c1-1 2-3 3-4h8zm295 274c7 2 12 7 15 13 6 9 10 21 7 32-1 3-3 7-3 10h-1v3c1 2 4 4 3 7h0c-2-2-5-5-6-7s-1-4-1-5v-7c-3 3-6 8-10 9-1 1-2 1-3 0-2-1-3-3-4-4 0-2 0-3 1-4 2-1 5 1 7 1 2-1 4-2 5-4 4-6 3-17 2-24-1-9-5-15-12-20zm-66-274h131 7l4 4c-4-1-9-1-14-1h-86c-20 0-39 1-59-1 1-1 3-1 4-1 5-1 9-1 13-1zm39 389c3-1 7-3 8-6 1-2-1-6 0-9 0-5 3-11 3-16 0-3-1-6-2-8 2 1 3 3 4 5 1 7-1 19 8 19 4 0 8-1 10-3 1-1 2-2 3-4l1-1c-1 4-3 8-6 12-5 7-10 14-17 18-4 2-7 3-11 3-1-3-1-7-1-10zm-172 9c-7 2-11 2-17-2-8-5-15-13-20-21-1-3-3-6-4-9 2 2 3 5 6 6 3 2 8 3 11 2 9-2 5-13 6-19 1-2 2-3 2-5 0 3-1 5-1 8 1 5 3 9 3 14 1 4-1 10 1 13s7 4 10 4l2 1 1 8z" class="D"></path><defs><linearGradient id="Z" x1="336.928" y1="316.447" x2="385.382" y2="312.477" xlink:href="#B"><stop offset="0" stop-color="#c9c8c9"></stop><stop offset="1" stop-color="#f5f4f5"></stop></linearGradient></defs><path fill="url(#Z)" d="M335 297c0-3 2-4 3-6v-1c2 1 3 2 5 2l11 5c12 4 25 7 31 19 3 6 3 14 1 21 0 2-1 3-2 4-1-2-2-3-3-4l-1-1h-1-2-5 0c1-3 4-5 5-7 1-3-1-10-2-13-2-6-5-10-11-13-2-1-6-3-8-2s-3 1-4 2l-2 2 1 1c0 2 1 7 1 10-1 1-2 3-3 4l1-7c0-1 0-1-1-2v3h0c-2-3-4-5-4-9l-10-8z"></path><defs><linearGradient id="a" x1="185.034" y1="434.02" x2="236.025" y2="480.286" xlink:href="#B"><stop offset="0" stop-color="#adacad"></stop><stop offset="1" stop-color="#e3e2e3"></stop></linearGradient></defs><path fill="url(#a)" d="M220 498c5-8 9-20 7-29 0-4-2-8-5-11-2 0-3-1-5-1l-3 3v1l-1-1c0-1 0-2 1-3 0-1 2-2 3-2 3-1 6 0 8 2-1-4-3-8-6-11-4-3-8-4-12-3-6 0-9 4-12 8 1 1 3 2 4 3s1 2 1 4c-1 1-1 1-2 1h-3c-2-2-4-4-4-6-1-3-1-5 1-6 2-4 6-4 10-5-2 0-4 0-6-1-1-1-2-2-2-3-1-2-2-4-1-6 0-2 2-4 2-7s-1-6-2-9c2 2 4 5 4 8v7c2 2 4 2 6 3l10 1c1 2 3 2 5 3l3 3c0 1 1 2 2 2h0v-1h1c7 7 9 19 8 29 0 6-1 12-3 17-2 3-4 7-7 10h-2z"></path><path d="M203 434l10 1c1 2 3 2 5 3l3 3c-2-1-4-3-6-3l-6-1h0 1l1-1c-1-2-5 0-7-1l-1-1z" class="C"></path><defs><linearGradient id="b" x1="231.286" y1="321.97" x2="188.609" y2="312.29" xlink:href="#B"><stop offset="0" stop-color="#d3d2d4"></stop><stop offset="1" stop-color="#fdfcfd"></stop></linearGradient></defs><path fill="url(#b)" d="M240 286c1 0 2-1 3-1v1c1 1 2 1 3 3l-8 8-9 7c0 3 0 5 1 7v1c-1-1 0-2-2-2h0c-2 2-3 6-3 9-1-2-1-3-2-5-1-3 0-5 1-8h-1c-7 1-10 4-13 9v-3c2-5 7-6 12-8-2-2-2-3-4-3-5 0-12 3-15 6-4 4-7 10-7 17 0 5 3 7 6 11 2 1 3 3 4 5s1 4 1 6v12h0c0-3 0-6-1-9-1-6-8-10-13-13l-2 2h-1c-2-1-3-3-3-4-2-5-2-10 0-15s5-10 9-13c10-7 23-8 33-13l11-7z"></path><path d="M240 286c1 0 2-1 3-1v1c1 1 2 1 3 3l-8 8-1-2c-3 0-4 2-6 3-1 1-1 1-2 1 0 1 0 1-1 1s-3 1-4 2l-1-1c2-2 5-3 7-4 1 0 1-1 2-1h0v-1c1 0 1 0 2-1 0 0-1-1-1-2l-4 1 11-7z" class="G"></path><path d="M243 571h1 1l7-2c7-3 14-4 21-5 8-1 15-1 22 0l8 1c2 0 3 1 5 0l13 5c2 0 3 1 6 1 4 2 8 4 13 6h-5-103l11-6z" class="B"></path><path d="M446 178c6 0 13 0 18 2 8 3 14 8 18 16 3 5 4 12 2 18-1 4-4 8-8 9-4 2-8 2-12 1-2-2-4-4-5-7 0-1 1-2 1-3l4-6v8h2c1 0 2-1 2-2 1-2 1-6 0-8l-1-2c0-1 1-1 0-3-1-1-2-3-3-3-6-3-13 0-18 2v-22z" class="D"></path><defs><linearGradient id="c" x1="269.874" y1="141.474" x2="311.728" y2="116.541" xlink:href="#B"><stop offset="0" stop-color="#bebdbe"></stop><stop offset="1" stop-color="#e0dedf"></stop></linearGradient></defs><path fill="url(#c)" d="M296 106c3 1 6 1 9 3h1l-1 2h-1c1 0 1 1 1 1 2 1 6 1 7 4 1 1 0 2 0 4-2 2-4 3-7 5-5 3-10 9-11 15l2 2c-1 1-1 2-2 3h0v1l-3-4-1 4h0c0 2 1 6-1 7h-2s-1-1-1-2v2c-3-2-5-3-8-5h0c-1-1-2 0-3-1l-1-1h-1 0c1-1 2-1 4-2h0l3-1c1-1 2-2 2-3-1-5-6-11-9-14-3-2-6-3-8-5s-2-4-3-6c3-2 6-4 9-3l1 1v-1h0l1 1c0 1 0 1 1 1 1-1 2-1 3 0l2 2c0-2 1-2 2-3 1 0 2-1 3-2h3c1-1 3-1 4 0h1 0c1 0 1 0 2-1 0-1 0-2-1-4h1s0 1 1 1h0 0c1 0 1-1 1-1z"></path><path d="M275 124l1-1c1 0 3 3 4 4v1h-1c-1 0-2-1-3-2s-1-1-1-2zm24 0h1 1c-1 2-2 3-3 4-1 0-1 0-2-1v-1c1-1 2-2 3-2zm-7 6h2l1 1c-1 2-1 4-3 6h-1 0c0-3 0-5 1-7zm-10-1c1 0 1 0 2 1s1 5 1 7l-1 1v-1c-2-1-2-3-3-5 0-1 0-2 1-3zm-4 19h0c3-2 5-4 7-7 0 4 0 7 1 10v2c-3-2-5-3-8-5h0zm-12-29v-1c0-1 1-1 1-2 3-1 6-1 8 0v4c-1 1-5 0-7 0 0 0-1 0-2-1zm35-3c2 0 6-1 7 0 0 1 1 1 1 2-1 1-1 2-2 2-2 1-5 1-7 0h0c0-1 0-3 1-4zm-10-5h1c2 2 3 3 4 6 0 2 0 4-1 5s-2 2-3 2h-1 0v-1c-1-1 0-2 0-3 0-3-1-7 0-9z"></path><path d="M272 112h0l1 1c0 1 0 1 1 1 1-1 2-1 3 0l2 2c0-2 1-2 2-3-1 2-1 3-2 5h0c-1-1-2-2-3-2h-1c-2-1-5-1-8 0 0 1-1 1-1 2v1c0 1 0 1-1 2-2-2-2-4-3-6 3-2 6-4 9-3l1 1v-1z" class="F"></path><path d="M284 111h3c1-1 3-1 4 0-1 2 0 6 0 9 0 1-1 2 0 3v1c-1 1-6 1-7 0-1 0-2 0-3-1s-1-3-2-5c1-2 1-3 2-5 1 0 2-1 3-2z" class="B"></path><path d="M284 111c2 2 1 10 1 12 0 1 0 1-1 1s-2 0-3-1-1-3-2-5c1-2 1-3 2-5 1 0 2-1 3-2z"></path><path d="M312 159c4-1 9-1 14-1h22 60 24c3 0 7-1 10 0v3l-1 25c0 1 0 2-1 3v1c0 1-1 1-2 1v-2h-1c0 1 0 2-1 2v-4l-1-1v-19h0-76c-16 0-32-1-47 0h-5c0-1-1-4-1-6h-1c2 0 5 0 6-2h1z" class="D"></path><defs><linearGradient id="d" x1="243.222" y1="439.258" x2="154.756" y2="459.172" xlink:href="#B"><stop offset="0" stop-color="#c2c1c2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#d)" d="M222 498h0c5-2 11-16 13-21 6-14 9-35 3-50-3-6-8-12-15-15-3-1-7-1-10 0-2 1-3 2-4 3-1 2 0 4 0 6h0c-1-3-1-5 0-8 1-2 3-3 5-4 3-1 8 0 11 1l5 4c-4-7-11-14-19-16-3 0-5 0-7 1-2 2-1 5-3 6h-1c-1-2 0-5 0-6l3-3c2 0 3 0 5-1-7-2-14-4-21-1-5 3-9 9-11 14s-3 13-1 18c1 1 2 3 3 4-3-2-5-5-5-8-2-9 1-19 6-26 2-3 4-5 7-7-9 1-16 4-22 11-5 7-7 16-6 24 1 4 2 10 5 12 4 3 8 0 11 3 0 2-1 4-2 6-1 1-2 1-3 1-5-1-8-5-11-8 0 5 2 9-2 14-1 2-2 3-3 6h-1c-1-4 3-6 2-10 0-3-2-6-4-10-4-9-1-22 4-31 5-8 13-16 23-19 12-4 26-2 37 4 15 8 24 23 28 39 5 17 0 44-8 59-7 12-17 22-30 25h0l-1-8-2-1h3c7 0 12-3 16-8h2z"></path><path d="M117 54c3 0 6 0 9 1 4 1 6 4 9 8 2 3 3 7 5 11 1 8 2 15 5 22 4 8 9 13 17 16-5 0-11 1-17 0-4 0-7 0-10-1-2 0-3-1-4-3s2-5 3-8c1-4 0-9-3-13-1-3-4-5-7-6-4-1-8 0-10 2-3 1-5 4-6 7 0 1 0 2 1 3h0s0-1 1-2c0-1 2-3 4-4 3-1 8-2 11-1 2 1 4 3 4 5h0c1 1 1 1 0 2 0-1-1-3-2-4-3-3-6-3-10-2 1 0 2 1 3 1 1 2 3 4 3 6 1 3-1 7-3 9-3 3-7 6-11 7-2 0-5-2-7-4-4-4-7-10-7-16-1-6 1-13 6-18 3-4 8-6 13-7 7 0 10 3 15 7l-2-3c-2-4-5-7-10-8-5-2-12-1-17 2l-6 3h0c6-7 14-11 23-12zm334 0h7c9 1 17 5 22 12-2 0-4-2-5-3-6-3-12-4-18-2-5 2-8 6-11 11l5-4c4-3 8-3 13-2s9 4 12 8c4 6 5 12 4 19-1 6-4 11-9 15-2 1-5 2-7 2-4-1-8-4-10-7s-3-6-3-9c1-2 3-5 5-6l1-1h-1c-4 0-6 1-9 4h0l-2 3h0v-3c1-2 2-4 4-5 3-1 7-1 10 0 4 1 5 4 7 7h0v-5c-2-3-5-5-8-7-3-1-6-1-9 0-4 2-6 5-7 9-1 3-2 9-1 12 1 2 2 3 3 5 0 1-1 2-2 3-4 4-24 2-31 2 4-1 8-3 11-6 8-6 10-16 12-26 1-5 2-11 5-16s6-8 12-10zm-145 55c0-1 0-4-1-6l13 3c-5-6-9-11-8-19 0-5 3-9 5-13-4 3-7 7-8 13 0 5 1 8 2 12-4-6-5-12-3-19 2-8 7-16 14-20s16-6 24-4c5 2 10 5 13 11 1 3 2 7 1 10s-4 5-6 6c-4 2-8 2-11 1s-6-3-8-6l-3-3c1 5 3 9 8 12 4 3 10 4 15 3 5-2 8-5 11-9 0 3 0 6 1 9 4 10 11 15 20 19-1 1-2 2-4 2-3 1-6 1-8 1-8 0-15-1-22-3-9-2-20-8-25-16-3-6-3-12-1-18l1-3c-4 3-7 6-8 11s-1 11 2 15c3 5 8 8 12 12-2 2-4 3-7 3-4 1-8 1-11 0-3 0-6-1-9-2l1-2zm-81 278c-1 0-3-2-3-2-4-3-7-5-10-7-14-7-29-10-42-18-11-7-18-21-20-34-2-7-2-15-2-22 0-20 3-40 15-56 8-13 19-25 34-31 5-2 12-3 18-4 23-3 47-2 71-1l76 2c17 0 33 2 49 1 7-1 13-1 20-3 11-3 20-7 31-10-6 8-11 17-16 25l-21 37c-4 9-9 18-12 28-1-1-1-3-2-4-1-8-4-16-9-22-11-13-30-16-46-19-16-2-31-3-47-5h-3c-1 3 0 102 0 116h5l35 1c8 0 16 0 24-1 6-2 11-4 16-8 3-3 6-7 9-11 0-1 1-3 2-5 1 5 1 11 0 16-1 13-4 25-6 37l-8 55c-2-7-2-15-4-22-3-6-6-13-10-19-3-3-7-5-11-7-7-4-16-4-24-5l-28-1c-1 6 0 13 0 19v40 58 20 12c2 9 5 17 11 24 3 4 6 7 10 10-3 0-4-1-6-1l-13-5c-2 1-3 0-5 0l-8-1c-7-1-14-1-22 0-7 1-14 2-21 5l-7 2h-1-1c10-8 19-19 23-32 1-4 0-9 0-13l1-15v-41-180-34-10c0-1 0-3 1-5-2-2-11-2-14-2-15-1-34-1-49 5-6 2-12 7-17 11-8 7-15 16-20 26-4 9-6 20-6 29l-1 1c2-4 5-10 10-12h3c1 1 2 2 2 3h0v2h0c-1 0-1 1-2 1h0 0c-2 0-4 0-6 1-5 5-6 11-6 17 0 7 3 12 5 19 2 4 6 8 10 12 1 3 5 6 8 8h2c3 2 5 4 8 5 6 3 12 4 18 7 1 1 3 2 4 3h0c1 2 4 3 6 5 2 1 4 3 5 4 3 4 7 8 9 12l3 6c0 2 1 3 2 4v1h0c0 1 1 2 1 4h1 1v-1c1 1 0 3 1 5 1 1 0 4 0 5 1 1 1 1 1 2v2 2c0 2 1 4 0 6 0 1 0 1 1 2 0 1-1 2-1 3v1c1 1 0 3 1 5 0 1-1 1-1 2s1 2 0 4v5c-1 1-1 3-1 5v1h0c-1 1 0 1 0 2-1 0-1 1-1 1v1c0 1 0 1-1 1v3h0c-1 1-1 1-1 2v1 1c0 1 0 1-1 2h0v2l-1 1v1c0 1-1 2-1 3s-1 3-2 4v1s0 1-1 2c0 1 0 1-1 2h0c0 2-1 2-1 3 0 2-1 3-1 4-1 1-1 2-2 3 0 1 0 1-1 2 1-4 2-7 4-10l4-10c0-2 1-4 1-7 2-3 3-7 4-11 0-2 0-5 1-7v-6c1-1 0-4 0-6l1-1v-8c0-1 0-1-1-2 0-1 1-2 0-3l-1-8v-3-2c-1-4-3-8-5-12v-1c-2-5-9-18-14-20z" class="D"></path><path d="M290 375h26l17 1c7 0 14 1 21 3 5 1 9 4 13 8 6 6 10 17 11 25 1 2 1 4 1 6v2c-3-6-6-13-10-19-3-3-7-5-11-7-7-4-16-4-24-5l-28-1c-3-1-5-4-7-6l-9-7zm0-146c7 0 14 1 21 1l32 2c13 1 25 3 38 6 7 2 14 5 20 10 1 2 3 3 4 5 6 9 7 21 7 31 0 1 0 2-1 4-1-8-4-16-9-22-11-13-30-16-46-19-16-2-31-3-47-5h-4c-1-1-3-3-4-3l-11-10zM167 342c0-1-1-1-1-1-2-2-3-4-3-6-4-8-6-17-5-26 0-18 4-36 14-50 2-3 4-6 7-9 10-9 22-15 36-18 24-6 48-4 73-3l-13 8c-2 2-5 3-7 4h0c-2-2-11-2-14-2-15-1-34-1-49 5-6 2-12 7-17 11-8 7-15 16-20 26-4 9-6 20-6 29l-1 1c2-4 5-10 10-12h3c1 1 2 2 2 3h0v2h0c-1 0-1 1-2 1h0 0c-2 0-4 0-6 1-5 5-6 11-6 17 0 7 3 12 5 19z" class="B"></path><path d="M311 358h-6c-2 1-4 2-5 3l-11 7V229h1l11 10c1 0 3 2 4 3h4-3c-1 3 0 102 0 116h5zm-3 207c-5-2-10-6-14-10-3-3-4-8-5-13v-10-17-56-57-17-10h1l9 7c2 2 4 5 7 6-1 6 0 13 0 19v40 58 20 12c2 9 5 17 11 24 3 4 6 7 10 10-3 0-4-1-6-1l-13-5z" class="C"></path></svg>