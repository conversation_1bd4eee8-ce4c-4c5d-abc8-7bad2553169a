<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="142 29 749 933"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#242527}.C{fill:#292a2b}.D{fill:#373738}.E{fill:#2e2e2f}.F{fill:#a7a6a6}.G{fill:#d7d5d5}.H{fill:#8d8c8d}.I{fill:#212223}.J{fill:#c4c3c3}.K{fill:#2d2d2e}.L{fill:#434344}.M{fill:#b7b6b6}.N{fill:#cbcaca}.O{fill:#1d1d1f}.P{fill:#7b7a7b}.Q{fill:#3d3e3f}.R{fill:#191a1b}.S{fill:#605f60}.T{fill:#969695}.U{fill:#48484a}.V{fill:#adadad}.W{fill:#838284}.X{fill:#141416}.Y{fill:#535354}.Z{fill:#bdbcbb}.a{fill:#b3b2b2}.b{fill:#4d4d4f}.c{fill:#393b3d}.d{fill:#a09fa0}.e{fill:#0e0f11}.f{fill:#313335}.g{fill:#737273}.h{fill:#9c9b9c}.i{fill:#6c6c6d}.j{fill:#59595a}.k{fill:#656566}.l{fill:#eae8e7}.m{fill:#dddbdb}.n{fill:#e4e2e0}.o{fill:#f4f2f2}</style><path d="M547 85c2 3 2 5 1 8l-1-1v-4l-1-2 1-1z" class="e"></path><path d="M428 101c0-2 1-4 2-4h1c1 2 3 3 5 4h-1c-2 0-3-1-5 0h-2z" class="B"></path><path d="M468 90c1-4 3-5 5-7l1 1c-1 2-3 4-4 6h-2z" class="f"></path><path d="M246 236v7l-1 9-1 1 1-14 1-3z" class="O"></path><path d="M569 89l3 9-1 1c-1-1-2-2-2-3-1-1-1-4-2-6l2-1z" class="X"></path><path d="M672 144c1 2 4 5 6 6l-2 2-3-3-3-4s1-1 2-1z" class="D"></path><path d="M195 127c3 0 5 0 8-1h3l-1 3h-2-6v-1c-1 0-2-1-2-1z" class="B"></path><path d="M537 84h1l4-4 5 5-1 1s-1-1-3-2c-2 0-3 1-6 2v-2z" class="R"></path><path d="M176 136c0-1 1-1 2-2 0 1 0 1-1 2l1 1-1 2c0 1-1 2-1 2-1 1-2 1-2 2h-3l5-7zm106 481c1-1 1-2 2-3v-3l3 12c0 1-1 2-1 3-2-2-3-7-4-9z" class="B"></path><path d="M797 204l1 2c0 1 0 1 1 2 1-1 1-1 2 0 0 2 0 4-1 6-1-1-2-2-3-2 0-2-1-4-2-5l2-3z" class="R"></path><path d="M291 792h2c-1 5-2 10-2 15l-2-2-1-2v-1l1-3c1-1 1-2 1-3s1-2 1-3v-1z" class="I"></path><path d="M263 789l1-1h1v3h0v2c1 1 1 3 1 5-1 0-2 1-3 1v-1-1c-1-2-3-4-3-7l3-1zm28-4c0 3 1 4 2 6v1h-2c-4 1-7 1-9 3v-2-3h4 3v-2c1 0 1 0 2-1-1-1 0-1 0-2z" class="X"></path><path d="M831 180c0 1 0 2 2 2l1 1v2c-5-3-10-3-15-4-1 0-2-1-3 0-1 0 0 0-1-1 2-1 5 0 7-1l9 1z" class="B"></path><path d="M749 581c2 9 3 16 0 25v-4c1-1 1-2 1-3-1-6-2-11-3-16l2-2zM598 108c0-2 2-3 2-4h-2c1-2 8-8 10-10l1 1c-1 1-3 6-5 6-2 1-2 4-3 6l-3 1z" class="E"></path><path d="M266 798c0 1-1 4 0 5 0 4 1 7 0 11l-1-1v-1c-1-1-1-2-2-3v-1-9c1 0 2-1 3-1z" class="B"></path><path d="M754 823h2 0c0 4 0 5-3 8l-8 7v-3c4-3 8-6 9-12z" class="E"></path><path d="M813 195c1 0 2 0 2 1v2c-2 1-3 1-4 0-5 3-6 6-10 10-1-1-1-1-2 0-1-1-1-1-1-2 3-2 6-4 8-6 1-1 2-2 3-4h2c0 1 0 1 1 2v-1l1-2z" class="C"></path><path d="M767 122c-1-2 0-7 0-10l3 4c0 1 0 3 1 5v5h-2l-1 1h-1v-5z" class="R"></path><path d="M767 122h1c0 1 1 3 1 4l-1 1h-1v-5z"></path><path d="M291 792v1c0 1-1 2-1 3s0 2-1 3h0v-2h0c-3 1-6 2-8 4l-1-1 2-5h0c2-2 5-2 9-3z" class="J"></path><path d="M235 331l1 1c-1 2-3 5-4 8l-1 2c0 1-1 3-1 4l-1 4h1 3l-7 1-1-2h2v-1c2-6 4-12 8-17z" class="R"></path><path d="M742 830l2-2v-3h1c0 2-1 9 0 10v3c-1 0-2 1-3 1-4-1-7-5-10-8h1l3 1h0c2 2 4 4 6 5v-7z" class="e"></path><path d="M556 87c2-5 6-8 10-12l3 14-2 1-3-10-7 8-1-1zM240 318h0v2 1c-3 0-6 3-7 5 4-1 9-2 12-1l-1 2c-1-1-2-1-3-1l-5 6-1-1 2-3v-1h-6c2-4 5-6 9-9z"></path><path d="M779 318l-1-5v-1c2 3 3 8 6 11 3 1 7 0 10 1-4 1-8 2-12 1h-2l-3 1 2-3 1-1-1-2v-2z" class="e"></path><path d="M384 95l7-7c2-2 6-6 8-5l-1 1c-1 2-3 3-5 4 0 2 1 2 1 3s0 2-1 3v1c-1-1-1-2-2-3l-1 1c-1 1-1 2-2 2l-1 1c-2 0-2 0-3-1z" class="X"></path><path d="M462 85c1 2 3 7 5 8 1-1 1-2 1-3h2c1 3 2 4 4 6l-1 4v3l-1-1c0-2-1-4-3-6l-5-2c0-1-1-2-1-3l-3-5 2-1z" class="B"></path><path d="M236 332l5-6c1 0 2 0 3 1h3c-1 1-2 2-4 3l-4 3h1c2 1 3 1 4 1h1 4v1h-5c-3 0-5 0-8 1-1 1-1 2-2 2 0 1-1 2-1 2h-1c1-3 3-6 4-8z" class="N"></path><path d="M770 116h0c2 4 2 7 3 11h12-1c-3 1-7 1-9 1 2 1 5-1 8 0-2 1-9 0-10 1h-8c-4-1-7-1-11-1 2 0 4 0 6-1h0c2 0 5-1 7 0h1l1-1h2v-5c-1-2-1-4-1-5z" class="e"></path><path d="M833 109v-6l1-1c1 2 1 5 2 8s3 5 4 8c-1 2-1 3-1 5h0c-1-1-1-1-1-2h-1v3c-1-3-1-5-1-7s-1-3-3-4h0v-4z" class="B"></path><path d="M572 98v6l7-2h1 1c0 1-1 2-1 3-1 0-1 1-2 2h0-1l-3 4c-1 0-1 0-2-1l-1 1c0-1 0-1-1-2l1-7v-3l1-1z" class="f"></path><path d="M581 102c0 1-1 2-1 3-1 0-1 1-2 2h0-1l-3 4c-1 0-1 0-2-1h0c1-4 5-6 8-8h1z" class="G"></path><path d="M644 121l-2-2 1-1c8 7 19 14 26 23l-1 1c-1-1 0-1-1-1-1-2-2-2-4-3l-19-17z" class="C"></path><path d="M265 813l1 1c-1 2-1 4-1 7h0c0 6 6 11 10 15l-1 1c-2-1-4-3-5-4-2-2-4-3-5-5-2-3-1-8-2-11 0-1 0-2-1-2l4-2z" class="O"></path><path d="M797 212c1 0 2 1 3 2v2h-1c-1-1-2-2-4-2-4 1-8 5-11 8l-8 8c0-2 0-4 1-6h0l1 1v-2l3-2c4-3 8-8 13-9 1 1 1 1 3 0h0 0z" class="B"></path><path d="M201 423l1 1v1c-5 3-12 5-15 11 0 1-1 3-1 4h0c2 1 4 2 6 2-1 1-1 2-1 4-2-1-4-4-6-4h-1c-1-2 0-4 1-6 3-7 9-10 16-13z" class="X"></path><path d="M828 124c3-5 4-9 5-15v4h0c2 1 3 2 3 4s0 4 1 7l-2 1-5 1h-1c-1 0-2-1-2-1l1-1z" class="c"></path><path d="M828 124h0c2-1 3-3 5-4l-3 6h-1c-1 0-2-1-2-1l1-1z" class="U"></path><path d="M773 238c0-3 1-5 1-8v-2-3-1-2c1-1 1-1 2-1l1 3c-1 2-1 4-1 6-1 2-1 4-1 6v12 4c0 1-1 2-1 2v-1c0-1 0-2-1-3-1 0 0 0-1-1v-8c1-1 1-2 1-3z" class="X"></path><path d="M608 94l11-12c2 1 3 3 5 4 2 2 5 4 7 6l-1 2-1-1-1-1h-2c0 1-1 1-1 1l-1-1v-2l-1 1c-1 0-1 0-2-1v-2c0-1-1-3-2-4-3 5-7 10-9 15l-1 2v-3l3-5h-1l-2 2-1-1z" class="B"></path><path d="M281 801c2-2 5-3 8-4h0v2c-1 1-2 2-2 4-1 1-1 2-2 3l-1 1v1c-2 1-5 1-8 1 0 0-1-1-2 0h0c-2-1-2-1-3-1l3-3v-1h1l1 2h1c1 0 1-1 2-2l1-4 1 1z" class="C"></path><path d="M280 800l1 1h2 2v1 2c-2 1-4 1-6 0h0l1-4z" class="M"></path><path d="M212 467c-2 0-3-1-5-1-4-1-9-1-13-1-6-1-11-1-17-2l6-3c4-3 8-6 11-10l1 1c-4 5-7 8-12 11l26 1h1c3 1 8 1 11 1-4 1-8 0-12 0h-5c2 1 4 1 6 2h1 6 2c-2 0-5 0-7 1z"></path><path d="M238 303h2l1 1-1 2h1c1-1 1 0 3 0v1l3 3v2h-4c-2 2-1 4-3 6h0c0-2 1-3 0-4-1-6-5-2-8-3v-2c2-3 4-4 6-6z" class="e"></path><path d="M543 84c2 1 3 2 3 2l1 2v4l1 1h1v1l2-1c3-1 3-3 5-6l1 1-3 6-3 3v5c-2-1-2-3-4-2h0v1l-1 1c-1 0 0 1-1 0v-5-2l-2-1v-2-1h-1l1-2v-5z" class="X"></path><path d="M547 100v-2c1 0 3-1 4-1v5c-2-1-2-3-4-2z" class="T"></path><path d="M543 84c2 1 3 2 3 2l1 2c0 2 0 5-2 7l-2-1v-2-1h-1l1-2v-5z" class="m"></path><path d="M820 164h2 0v-1h2 0c2 0 2 0 3 1l1-1v-1c2 0 2 0 3 1l1 1h2 2 2 0l1 2h-2c-3-1-9-1-12 1h0c4 3 8 8 9 13v3l-1-1c-2 0-2-1-2-2-1-2-2-3-3-5 0-2-2-5-3-6l-1-1v-1l-1 1v1c-1-2-2-3-3-5z" class="I"></path><path d="M738 110c-1-2-1-3 0-6s3-7 5-10v7c1 4 4 10 3 14l-3 1h0c-1-3-2-5-3-7l-2 1z"></path><path d="M740 109c0-3 0-6 1-10l4 15 1 1-3 1h0c-1-3-2-5-3-7z" class="G"></path><path d="M579 102c2-1 4-2 6-4l1-1 3 3c1 3 2 5 1 8h2c1-1 2-1 4-1 1 1 1 1 2 1l3-1c-1 1-3 5-4 6v-2l-1 3h-1c-2-1-4-1-6-1h-3l-1 1c2-3 3-6 3-9 0-2 0-4-2-5-1 0-3 1-5 2h-1-1z" class="C"></path><path d="M586 113h1c2-3 3-4 7-5l3 3-1 3h-1c-2-1-4-1-6-1h-3z" class="l"></path><path d="M637 671c2-1 3-2 4-3v-1 3c-3 6-9 8-15 10s-12 3-19 4v-5c2 0 1 1 2 1s2 0 3 1l2-1c8-1 17-4 23-9z" class="X"></path><path d="M225 212c4 1 6 3 9 6 1 1 2 2 3 2l2 1 2-2c1 0 1 0 2-1l-1-1c1-1 1-1 2-1 0 1 1 2 1 3l-1 1v3h1v1 7h1v5l-1 3-1-7c-2-3-5-6-8-9s-6-7-10-8c-3-1-5-1-7 0v1-2l1-1c2 0 3 0 4-1h1z" class="B"></path><path d="M727 789l2 1c0 1 1 3 2 4v3c1 3 0 6 0 9h-1v1c1 0 1 1 1 2 1 2 0 4-1 7 0 2 0 7 1 10 2 2 3 4 5 6l-3-1h-1c-6-3-3-11-6-16h0l1-1h1l2-2c-3-5-1-10-3-15 0-2-2-4-2-6l2-2z" class="R"></path><path d="M274 114c-1-2-1-2-1-4s2-5 2-7c1-3 1-6 1-9 3 4 6 11 5 16 0 1-1 2-2 3 2 4 5 8 6 13l-1 2h0l-2-2c0-2 0-4-1-6s-2-3-4-4h-1l-1-1h0l-1-1z"></path><path d="M274 114h1v-2c-1-3 2-8 3-12 0 4 2 8 0 11-1 2-2 3-3 4l-1-1z" class="n"></path><path d="M436 101l8 4 8-31 10 11-2 1-1-1-2 1c-2 0-2 0-3 1l-1 1-1 1c0-1 0-2-1-2-1 5-4 11-4 16v3 1c0 1-1 2 0 3v3c-1-2-2-5-4-7h0c-3-2-6-3-8-5h1z" class="X"></path><path d="M451 87l2-9 6 7-2 1c-2 0-2 0-3 1l-1 1-1 1c0-1 0-2-1-2z" class="J"></path><path d="M609 95l2-2h1l-3 5c-2 5-3 10-4 16v3h-1v-1c1-1 0-2 0-3v-1l-1-1c-2 5-1 10-2 16h0 0l-1-3h-1c0 3 1 4 2 6v5h-1 0c-2-3-2-7-4-10l1-6c-1 1-1 3-2 5h0l-1 1c-2-2 2-6 2-8-1 2-2 4-4 5h0v-1c0-1 0 0 1-1 1-2 1-2 1-4h0c1 0 1-1 1-2h1l1-3v2c1-1 3-5 4-6 1-2 1-5 3-6 2 0 4-5 5-6z" class="W"></path><path d="M609 95l2-2h1l-3 5c-2 5-3 10-4 16v3h-1v-1c1-1 0-2 0-3v-1l-1-1c-2 5-1 10-2 16-1-4 0-9 1-13v-4c1-1 1-3 2-5l1-1c-1-1-1-1-1-2v-1c2 0 4-5 5-6z" class="a"></path><path d="M225 206h16l-2 2v1h1v1h1c0 1 1 2 2 2v1c1 0 2 1 2 2v2 7-1h-1v-3l1-1c0-1-1-2-1-3-1 0-1 0-2 1l1 1c-1 1-1 1-2 1l-2 2-2-1c-1 0-2-1-3-2-3-3-5-5-9-6-1 0-2 0-3-1 1-2 1-3 3-5z" class="H"></path><path d="M232 213l-1-3c1-1 1-1 2-1v1c1 1 3 2 4 3 0 1 0 1-1 1-2 0-2 0-4-1z" class="N"></path><path d="M237 220h1c0-1-1-3 0-5h1l-1-2h2v-1l-1-1h0l-1-1h-2c0-1 2-1 4-1v1h1c0 1 1 2 2 2v1c1 0 2 1 2 2v2 7-1h-1v-3l1-1c0-1-1-2-1-3-1 0-1 0-2 1l1 1c-1 1-1 1-2 1l-2 2-2-1z" class="C"></path><path d="M225 206h16l-2 2v1h-6c-1 0-1 0-2 1l1 3c-1-1-2-2-3-2h-1c2 2 5 4 6 6v1c-3-3-5-5-9-6-1 0-2 0-3-1 1-2 1-3 3-5z" class="V"></path><defs><linearGradient id="A" x1="177.719" y1="116" x2="187.59" y2="114.595" xlink:href="#B"><stop offset="0" stop-color="#171718"></stop><stop offset="1" stop-color="#3f3e3e"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M179 127v-1c-2-6 0-10 2-16 1-2 2-5 3-8h1c1 4 0 7 0 11 1 6 3 9 8 13 1 0 1 0 2 1 0 0 1 1 2 1v1h-16 0c-1 0-1-1-2-2z"></path><path d="M181 129c-1-1-1-2 0-3h3 0l1-1h0l1-1v2h7c1 0 1 0 2 1 0 0 1 1 2 1v1h-16 0z" class="e"></path><path d="M744 812l1-1 2-1c1 2-1 2 0 4 1-1 2-1 2-3h2l1-1 1 1h0c1 1 0 1 1 1 0 1 1 2 1 2 1 1 2-1 3 1l-1 3-1 5h0 0-2c-1 6-5 9-9 12-1-1 0-8 0-10l1-2-1-1c0-2 1-2 0-3v-1l1-1-1-1-1 1v-1-4z" class="P"></path><path d="M755 816h1l1 2-1 5h0 0-2c-1-2 0-5 1-7z" class="O"></path><path d="M744 812l1-1 2-1c1 2-1 2 0 4 1-1 2-1 2-3h2l1-1 1 1h0c1 1 0 1 1 1 0 1 1 2 1 2 1 1 2-1 3 1l-1 3-1-2h-1-3c-2 2-3 4-5 6h-1l1-2c0-1-1-1-1-2l1-1h-1l-1-1-1 1v-1-4z" class="I"></path><path d="M247 310c2 0 2 1 3 2 2 2 5 3 7 4 1 1 1 2 2 4h-2l-1 1 2 2h0l-2 4h-1v-2l1-1-1-1-1 1h-1c-1 0-2-1-3-1 1 1 3 2 3 4-2 0-3 0-4-1h0l-4-1c-3-1-8 0-12 1 1-2 4-5 7-5v-1-2c2-2 1-4 3-6h4v-2z" class="P"></path><path d="M243 312l3 2-1 2h1v2h-2v1c1 0 3 0 4 1h0-8v-2c2-2 1-4 3-6z" class="J"></path><path d="M240 321c2 0 5 0 8 1v1-1c-2 0-4 1-6 0v1c1 0 1 1 2 1 1-1 3 0 4 0l1 2-4-1c-3-1-8 0-12 1 1-2 4-5 7-5z" class="Z"></path><path d="M247 310c2 0 2 1 3 2 2 2 5 3 7 4 1 1 1 2 2 4h-2l-1 1 2 2h0l-2 4h-1v-2l1-1-1-1-1 1c-1-1-2-2-2-3-1-1-2-3-3-4-1 0 0 0-1-1l-2-2-3-2h4v-2z" class="R"></path><path d="M249 314c2 0 2 0 3 1v1h-2l-1-2z" class="e"></path><path d="M633 109c1 0 2 0 3-1 1 4 4 7 7 10l-1 1 2 2 19 17c2 1 3 1 4 3 1 0 0 0 1 1l1-1 3 3c-1 0-2 1-2 1l3 4v2c-1-1-1-2-2-2-1-1-1 0-1-1-2-2-4-3-7-4-2-1-4-3-6-3l-1-1-1-1c-1-3-2-4-4-6h0 0c-4-3-6-6-9-10-2-3-5-5-7-9l-2-5z" class="d"></path><path d="M663 138c2 1 3 1 4 3 1 0 0 0 1 1l1-1 3 3c-1 0-2 1-2 1l-7-7z" class="E"></path><defs><linearGradient id="C" x1="636.218" y1="110.268" x2="639.135" y2="119.28" xlink:href="#B"><stop offset="0" stop-color="#29292a"></stop><stop offset="1" stop-color="#434545"></stop></linearGradient></defs><path fill="url(#C)" d="M633 109c1 0 2 0 3-1 1 4 4 7 7 10l-1 1 2 2-1 1 3 4h2c2 0 5 4 6 5 2 2 2 3 3 4l1 1 1 3c-3-2-8-10-11-10l3 4h0 0c-4-3-6-6-9-10-2-3-5-5-7-9l-2-5z"></path><path d="M179 127c1 1 1 2 2 2h0 16 6v1h6l15 1h0c-3 0-6 0-9 1h-15 0l-3 1c0 1 1 2 1 3 2 1 2 2 3 3h-3c0-1-1-2-1-2-1 0-1 0-1 1h0-1-4-1-1-3c-2 0-4 1-6 0l-3 1 1-2-1-1c1-1 1-1 1-2-1 1-2 1-2 2 1-3 3-6 3-9z" class="K"></path><path d="M197 129h6v1h-21l-1-1h16z" class="D"></path><path d="M184 132c5-1 10-1 15-1l1 1h0l-3 1h-10c-1 0-2-1-3-1z" class="L"></path><path d="M179 134c2-2 3-2 5-2 1 0 2 1 3 1l-1 1v2h1l2 2h-3c-2 0-4 1-6 0l-3 1 1-2c0-1 1-2 1-3z" class="S"></path><path d="M179 134c2-2 3-2 5-2 1 0 2 1 3 1l-1 1h-4-3z" class="b"></path><path d="M187 133h10c0 1 1 2 1 3 2 1 2 2 3 3h-3c0-1-1-2-1-2-1 0-1 0-1 1h0-1-4-1-1l-2-2h-1v-2l1-1z" class="g"></path><defs><linearGradient id="D" x1="766.223" y1="248.898" x2="770.121" y2="263.838" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#2b2b2d"></stop></linearGradient></defs><path fill="url(#D)" d="M772 236v5 8c1 1 0 1 1 1 1 1 1 2 1 3v1s1-1 1-2v-4c0 2 0 5 1 6 5-1 9-2 14-4-3 3-6 6-10 9l1 2-2 1c-1 1-3 1-4 1s-1-1-1-1c-1 1-1 1-3 1v2l-3-2c-1-1-2-3-4-4s-10-1-13 0c-1-1-1-1-1-2v-3h0c0-1 0-1 1-2l2 3h1c1-1 3-1 4-1l1-1v1c1 1 3 1 5 2 1 1 2 1 3 1 1-1 1-1 1-2l2-1 1-1h0v-4c1-5 0-9 0-13h1z"></path><path d="M771 263c0-1-1-2-1-3l8-1c1 1 1 2 1 3-1 1-3 1-4 1s-1-1-1-1c-1 1-1 1-3 1z" class="F"></path><path d="M237 503l2-1 9 6 1 2 10 21c2 6 5 12 8 17l1 1c0 1 1 1 2 1l1 3c-1 2 0 4 0 6l-1 1c-1 1-1 3-1 4v-2h0v-1c-1-3-3-6-5-9s-3-6-5-9c-3-6-5-13-7-19-1-4-2-9-5-13-2-3-6-6-10-8z" class="e"></path><path d="M341 127l1 1c2 6 1 12 2 18 7-7 14-15 23-21h0 2l-27 28v-5h0l-2-2h0 0v-2-1h-2c1 0 1 0 2-1h-4-9v-1h-11v-1-1h7c0 1 6 1 7 1 3-1 5 0 8-1h1 1l1-1c0-2-1-4-1-5l1-1v-1l-1-1c-2 2-10 1-13 1l-1-1 1-3c4 1 9 0 14 0z" class="E"></path><path d="M340 133l1-1 1 1c0 1 0 3-1 4v1c0-2-1-4-1-5z" class="L"></path><path d="M327 127c4 1 9 0 14 0-1 1-2 1-2 2-2 0-2-1-3 0h-8l-1 2-1-1 1-3z" class="C"></path><path d="M826 451c2 3 5 6 9 8 2 2 5 3 8 4-8 1-38 1-43 7-3 3-1 9-2 13-1 7-6 13-12 18v-3c6-7 9-12 9-21v-4c0-3 0-6-1-10l42-1c-3-2-6-4-8-6l-4-4 2-1z" class="e"></path><path d="M557 88l7-8 3 10c1 2 1 5 2 6 0 1 1 2 2 3v3l-2 1-2 2h-1v-1l-2 2c-2 0-2-2-3-3h-1c-1 2-1 4-1 5h-1v-3h-1v1h-1v-1c-1-4 0-8-2-11l3-6z" class="m"></path><path d="M557 105v-4l2 1 1-2v1l1 1 1-1c0 1 1 2 2 3 0-1 1-1 2-2 1 1 1 1 2 1h1l-2 2h-1v-1l-2 2c-2 0-2-2-3-3h-1c-1 2-1 4-1 5h-1v-3h-1z" class="J"></path><path d="M741 670c0-1-1-4 0-5v-1c0-1 0-1 1-2 1 1 2 0 3 0l-1 31v-1c-1-4 0-9 0-12-1 0-1 0-1 1v-1l-1 70v1l-1-1-2-1v-77l1-3h0l1 1z" class="I"></path><path d="M739 672l1-3h0l1 1v80l-2-1v-77z"></path><path d="M581 102c2-1 4-2 5-2 2 1 2 3 2 5 0 3-1 6-3 9l1-1h3l-1 2-2 9c-2 0-3 0-5-1 0-1-1-1-2-1 0 0 0 1-1 2l-3-2c0 1-1 2-1 2l-7-4 1-2 1-8 1-1c1 1 1 1 1 2l1-1c1 1 1 1 2 1l3-4h1 0c1-1 1-2 2-2 0-1 1-2 1-3z" class="n"></path><path d="M586 113h3l-1 2-2 2h0c-1 0-1 0-2-1l1-2 1-1z" class="o"></path><path d="M574 111l3-4h1c-2 5-3 7-3 13h1l1-1 1 2 5-5v1l-4 5s0 1-1 2l-3-2c0 1-1 2-1 2l-7-4 1-2 1-8 1-1c1 1 1 1 1 2l1-1c1 1 1 1 2 1z" class="M"></path><path d="M571 111l1-1c1 1 1 1 2 1 0 2 0 3-1 5l-3-1 1-4z" class="N"></path><path d="M570 109c1 1 1 1 1 2l-1 4-1 4 6 3c0 1-1 2-1 2l-7-4 1-2 1-8 1-1z" class="E"></path><path d="M554 781c1 0 2-1 3-2 0 4 1 10 0 13v2l-1 15v-1-1h-1c0 1 0 1 1 2v2h0v2c0 1 0 1-1 2h0l1 1c-1 1-1 1 0 2 0 1 0 1-1 2v3h0c0 2-1 6 0 7v3h0v-3h-1v18c1-3 1-6 1-8h0c0 1 1 7 0 8v2h-1v-2 5h-1v-6 1 4h-1v-2 4 1c0 1 0 1 1 2v1h-1v-1 6c-1-1-1-1-1-2v-1c-1-2-1-2-2-2v-1h0l1-1v-3 3-2-3c1-5 0-12 0-18l2-27c0-8 0-17 2-25z" class="N"></path><path d="M247 327l6 3c1 1 3 2 5 4s4 4 6 7h-1c-1 1-2 2-3 4-1 0-1 1-2 1-3 1-6-1-9-2-1 0-1 0-1-1h-7 0l-5 1c-2-2-2-2-5-2l1-2h1s1-1 1-2c1 0 1-1 2-2 3-1 5-1 8-1h5v-1h-4-1c-1 0-2 0-4-1h-1l4-3c2-1 3-2 4-3z" class="b"></path><path d="M258 334c2 2 4 4 6 7h-1c-1 0-2 0-2 1-1 1-2 1-3 1h0c-1-1 2-2 2-3h0-2c0-1-2-3-3-4h0 1c1-2 1-1 2-2z" class="Q"></path><path d="M249 339c3 1 5 7 8 5l1-1h0c1 0 2 0 3-1 0-1 1-1 2-1-1 1-2 2-3 4-1 0-1 1-2 1-3 1-6-1-9-2-1 0-1 0-1-1v-1l-2-1c1-1 1-1 2 0h1v-2z" class="U"></path><path d="M247 327l6 3h-1v1c0 1 0 1 1 2 1 0 0 0 1 1h-4l-1-1h-4v1h-1c-1 0-2 0-4-1h-1l4-3c2-1 3-2 4-3z" class="H"></path><path d="M240 333h0c1-1 3-2 4-2h3c1 0 2 1 3 2h-1-4v1h-1c-1 0-2 0-4-1z" class="Z"></path><path d="M232 340h1s1-1 1-2c1 0 1-1 2-2 3-1 5-1 8-1 1 1 1 1 3 1v1l-2 1v1h4v2h-1c-1-1-1-1-2 0l2 1v1h-7 0l-5 1c-2-2-2-2-5-2l1-2z" class="V"></path><path d="M732 646h1v4h0c2-1 3-3 4-5l1-4h0c1-2 1-2 1-3 1-1 0-2 0-3 2-3 0-7 1-10h1v10c1 1 0 2 0 4 1 1 1 5 1 7h0 1v-2c1-1 1-2 1-3h1 0v-2c-1-4 0-8 0-12l1 1c0 5 0 10 1 15l1 1c0 3 2 7 4 10l-1 1c-2-1-4-1-5-3-1 4 0 5 2 7l1 2v1h-4c-1 0-2 1-3 0-1 1-1 1-1 2v1c-1 1 0 4 0 5l-1-1h0l-1 3-1-8c0-3-2-1-4-2 1-2 2-4 3-5 0-2 0-2-1-3-2 0-4 0-7 1l3-9z" class="e"></path><path d="M743 644c1-1 1-2 1-3h1 0v-2c-1-4 0-8 0-12l1 1c0 5 0 10 1 15l1 1c0 3 2 7 4 10l-1 1c-2-1-4-1-5-3h0-2v-1h0 1s1 0 1 1c2 0 3 1 5 2-1-2-2-5-3-7l-1-1v-1c0-2-1-2-1-3v2h-2c0 2 0 4-1 6v-4-2z"></path><path d="M631 92l6 5c0 1-1 2-1 3-1 2-1 6 0 8-1 1-2 1-3 1l2 5c2 4 5 6 7 9 3 4 5 7 9 10h0 0c2 2 3 3 4 6l1 1 1 1c-3-1-5-5-8-7-1 0-1 0-2-1h-1v1c-4-4-7-9-11-13l4 6v1c-1 2 0 0-1 1-2-1-6-8-8-11 0-1-1-1-2-1v-1h-1c-1-1-1-2-1-3-1-4-1-7 0-11h0l-1-1c1-2 1-7 4-8l1 1 1-2z" class="W"></path><path d="M631 92l6 5c0 1-1 2-1 3-1 2-1 6 0 8-1 1-2 1-3 1h0c-1-2-1-9-1-11h2v-1l-2-1c-2 3 0 8-2 11h0c-1-3 1-9 0-12v-1l1-2z" class="I"></path><path d="M625 101c1-2 1-7 4-8l1 1v1c1 3-1 9 0 12-1 4 2 11 4 14h1l4 6v1c-1 2 0 0-1 1-2-1-6-8-8-11 0-1-1-1-2-1v-1h-1c-1-1-1-2-1-3-1-4-1-7 0-11h0l-1-1z" class="J"></path><path d="M625 101c1-2 1-7 4-8l1 1v1l-1 1v1s0 1-1 2c-2 6-2 12 0 17h-1c-1-1-1-2-1-3-1-4-1-7 0-11h0l-1-1z" class="T"></path><path d="M220 196c3 0 9 0 11-1 2 0 4 1 6 0 2 0 3-1 5 1 1 3 1 6 0 9h-2v1h2-1-16c-2 2-2 3-3 5 1 1 2 1 3 1h-1c-1 1-2 1-4 1l-1 1v-1c-1-3-2-6-4-9-3-3-5-5-10-6 1 0 2 0 3-1v-1c3-1 9 0 12 0z" class="Y"></path><path d="M229 200h1c0-1 1-1 1-2h1l1 1c1 1 1 1 2 0 2 2 2 2 3 5h-1l-11 1 3-5z" class="J"></path><path d="M205 198c1 0 2 0 3-1v-1c4 3 14 2 19 2l-4 2c0 1 0 1-1 2h1 1 2c0-1 0-1 1-1s1-1 2-1h0l-3 5-1 1c-2 2-2 3-3 5 1 1 2 1 3 1h-1c-1 1-2 1-4 1l-1 1v-1c-1-3-2-6-4-9-3-3-5-5-10-6z" class="C"></path><path d="M265 791l3 1c1 1 0 1 1 1h1c3 1 3 1 5 3 0 2-1 6-1 8v1l-3 3c1 0 1 0 3 1h0c1-1 2 0 2 0v1h-1v1c1 3 1 6 1 9 1 6 1 11 0 16-1 0 0 1-1 0-4-4-10-9-10-15h0c0-3 0-5 1-7 1-4 0-7 0-11-1-1 0-4 0-5 0-2 0-4-1-5v-2h0z" class="T"></path><path d="M271 808l-1 1-2-1v-3c1 0 1-1 2-1h0c2 0 3 0 4 1l-3 3z" class="M"></path><path d="M265 791l3 1c1 1 0 1 1 1h1c2 2 4 4 3 7 0 1-1 2-2 3h-1l-1-1c-1-1-2-3-2-4h-1v5h0c-1-1 0-4 0-5 0-2 0-4-1-5v-2h0z" class="i"></path><path d="M269 793h1c2 2 4 4 3 7 0 1-1 2-2 3h-1l1-1v-3h0 1c0-1-1-3-2-4 0 0 0-1-1-1v-1z" class="U"></path><path d="M284 808v-1l1-1c1-1 1-2 2-3 0-2 1-3 2-4h0l-1 3v1l1 2 2 2h1c-1 1-1 0-2 1s-1 3-1 4 0 1 1 1c1 1 1 1 2 1 1 2 1 1 0 2-1 4 0 9-1 12-2 2-5 4-7 6-1 1-4 4-6 5h-1c-1-1-3-1-3-2l1-1c1 1 0 0 1 0 1-5 1-10 0-16 0-3 0-6-1-9v-1h1v-1c3 0 6 0 8-1z" class="R"></path><path d="M284 808h1l-3 5c-2 4-3 8-3 12l1 7h1v1l-2 2c-1-1 0-9-1-11 0-2 0-3-1-4 0-2 1-5 0-7 0-1-1-2-1-3h0v-1c3 0 6 0 8-1z" class="P"></path><path d="M284 808h1l-3 5-1-1c1-1 1 0 0-2 0 1-1 1-2 2-1-1-2-2-3-2h0v-1c3 0 6 0 8-1z" class="V"></path><path d="M281 832v-8c1-1 2-3 3-4 1-2 1-4 4-4 0 0 1 0 1 1 0 2 0 7-1 10 0 1-3 3-5 5l-2 1v-1z" class="W"></path><path d="M284 808v-1l1-1c1-1 1-2 2-3 0-2 1-3 2-4h0l-1 3v1c-1 2-1 3-1 4s1 1 1 2-1 3-2 4l1 2h0l1 1c-3 0-3 2-4 4-1 1-2 3-3 4v8h-1l-1-7c0-4 1-8 3-12l3-5h-1z" class="E"></path><path d="M203 425h1c2-2 5-3 7-3 1 0 1 1 1 1-1 3-1 5-1 7l2 1v1l2 1v2c-2 2-5 7-7 9l-3-1-1 1-2-1v1h-3c-1 1-1 1-1 2l-1 1c-2-1-4-1-6-1 0-2 0-3 1-4-2 0-4-1-6-2h0c0-1 1-3 1-4 3-6 10-8 15-11h1z" class="C"></path><path d="M198 438h2c2-1 4-3 6-3 0 1 0 2-1 3-2 1-4 1-7 0z" class="U"></path><path d="M186 440l7-2 1 1-1 1h7 1v2h-1c-2 0-5-1-8 0-2 0-4-1-6-2h0z" class="c"></path><path d="M213 432l2 1v2c-2 2-5 7-7 9l-3-1c3-4 6-7 8-11z" class="W"></path><path d="M192 442c3-1 6 0 8 0h1l1 1v1h-3c-1 1-1 1-1 2l-1 1c-2-1-4-1-6-1 0-2 0-3 1-4z" class="E"></path><path d="M203 425h1c2-2 5-3 7-3h0c0 2-2 2-3 3s-2 3-4 4c-1 1-1 2-2 2l7-1v1h0c-1 2 0 3-3 4-2 0-4 2-6 3h-2c-1 1-3 1-4 1l-1-1-7 2c0-1 1-3 1-4 3-6 10-8 15-11h1z" class="Y"></path><path d="M203 425c0 2-3 2-4 3s-2 3-2 4h-1l-2 2c1 0 3-1 4 0h-1l-3 3-1 1-7 2c0-1 1-3 1-4 3-6 10-8 15-11h1z" class="J"></path><path d="M740 596c1 1 1 2 1 3h1c0 10 0 18 6 26l3 3v-1c1-1 1-2 1-3l3 6h-1c-3 1-4 0-7-1-1-1 0-1-1-1l-1-1c0 4-1 8 0 12v2h0-1c0 1 0 2-1 3v2h-1 0c0-2 0-6-1-7 0-2 1-3 0-4v-10h-1c-1 3 1 7-1 10 0 1 1 2 0 3 0 1 0 1-1 3h0l-1 4c-1 2-2 4-4 5h0v-4h-1c2-6 3-11 4-17-3 0-5 1-8 1 0-2 1-3 1-4 3-6 4-10 5-16 2-2 2-2 2-4h0v2c1 1 1 2 1 3l3-15z" class="X"></path><path d="M752 624l3 6h-1c-3 1-4 0-7-1-1-1 0-1-1-1l-1-1c0 4-1 8 0 12v2h0-1c0 1 0 2-1 3 0-5-2-11-1-17v-1l1-1h5l3 3v-1c1-1 1-2 1-3z" class="e"></path><path d="M740 596c1 1 1 2 1 3 1 8 1 18-5 25-1 1-3 3-5 4v-1l-2-1c3-6 4-10 5-16 2-2 2-2 2-4h0v2c1 1 1 2 1 3l3-15z" class="N"></path><path d="M734 610c2-2 2-2 2-4h0v2c1 1 1 2 1 3-1 6-3 11-6 16l-2-1c3-6 4-10 5-16z" class="I"></path><path d="M781 261l4 5c-1 1-1 0-1 2-1 1 1 8 2 9 0 1 1 3 1 4-1 2-2 6-1 8v1 1c-1 1-2 2-2 4l1 1c-1 0-2-1-3-1v-1c-2-2-6-1-8-1-1 1-1 2-1 3v1h-1c-1 2-3 5-4 6h-2l-1 2h1v1c-2 0-4-1-5-1l-1-1-3-3 1-1h1c3-1 5-2 7-5 0 2 0 3 1 4 4-7 5-13 5-21l1-3v-4h0c0-3-1-5-2-6v-2c2 0 2 0 3-1 0 0 0 1 1 1s3 0 4-1l2-1z" class="B"></path><path d="M760 304h3l1-2h2v1l-1 2h1v1c-2 0-4-1-5-1l-1-1z" class="O"></path><path d="M766 295c0 2 0 3 1 4l-2 2h-8l1-1h1c3-1 5-2 7-5z" class="P"></path><path d="M775 285c0-1 0-1 1-1 0-1 0-2 1-3 2 0 4-1 6 0v2l-1 1-7 1zm1-10c2-1 5-1 6 0s1 2 1 4c-2-1-5 0-7-1v-3z" class="d"></path><path d="M773 266h8c1 2 1 3 1 4l-1 1h-6c0-1 0-3-1-4l-1-1z" class="H"></path><path d="M775 285l7-1c0 1 1 1 1 2l-1 3c-2 1-4 0-6 0-1 0-1-1-1-1v-3z" class="h"></path><path d="M775 285l7-1c0 1 1 1 1 2-2 0-5 0-7 1l-1 1v-3z" class="E"></path><defs><linearGradient id="E" x1="541.646" y1="843.779" x2="548.072" y2="844.173" xlink:href="#B"><stop offset="0" stop-color="#a09e9f"></stop><stop offset="1" stop-color="#bfbfbe"></stop></linearGradient></defs><path fill="url(#E)" d="M549 781h5c-2 8-2 17-2 25l-2 27c0 6 1 13 0 18v3 2-3 3l-1 1h0v1c1 0 1 0 2 2v1c0 1 0 1 1 2l1 1h-1 0c0 1 0 2 1 3l-1 1 2 2 1 1c1 1 1 1 1 2l1 1 1 1v1h-1c-6-8-11-16-15-25-1-2-2-4-2-7l-1-2v-3c0-1 1-1 1-2l1-1 1-1v-1h0v-2-1-1l1-1c1-1 2-1 2-2s0-2 1-3l1-3v-2c0-2 0-3 1-5h0v-3c1-2 1-6 0-9v-4-1-1c0-3 1-4 0-6v-2c0-1 1-3 0-4h0l1-3z"></path><path d="M545 840l1 1v1h-1v-2z" class="a"></path><path d="M545 827c0-1 0-2 1-3 1 1 0 2 0 4v3h-1v-3-1z" class="V"></path><path d="M540 844h1c1 2 1 5 1 7-1-2-2-4-2-7z" class="T"></path><path d="M549 821h1v3c-1 1-1 5-1 7v1 7c-1 3-1 7 0 10l-1-1c0-1-1-3 0-5l1-22z" class="J"></path><path d="M731 794l4 2h0l1-1c1 0 2 1 3 2 1 0 1 0 2-1v-1l1 1v1 3 3l1 1v1h1 1c2 0 4 0 6-1h2l-3 4-1 3c0 2-1 2-2 3-1-2 1-2 0-4l-2 1-1 1v4 1l1-1 1 1-1 1v1c1 1 0 1 0 3l1 1-1 2h-1v3l-2 2v7c-2-1-4-3-6-5h0c-2-2-3-4-5-6-1-3-1-8-1-10 1-3 2-5 1-7 0-1 0-2-1-2v-1h1c0-3 1-6 0-9v-3z" class="a"></path><path d="M741 795l1 1v1 3l-5 8c-2 0-2-1-3-1v-3c1-1 1-1 2-1h1v1l1-1c1-1 2-2 2-5v1h1v-3-1z" class="Z"></path><path d="M742 827v-4c1-4 1-8 1-12l1 1v4 1l1-1 1 1-1 1v1c1 1 0 1 0 3l1 1-1 2h-1v3l-2 2v-3z" class="R"></path><path d="M742 800v3l1 1v1h1 1c2 0 4 0 6-1h2l-3 4h-10-3l5-8z" class="B"></path><defs><linearGradient id="F" x1="733.939" y1="822.12" x2="742.375" y2="828.622" xlink:href="#B"><stop offset="0" stop-color="#b2b1ad"></stop><stop offset="1" stop-color="#cbcbd0"></stop></linearGradient></defs><path fill="url(#F)" d="M734 816l1-1v-6h1 1s1 1 2 1l1 2h0l-1 1v1 8 1l1 11 1 1v-1c0-2 0-5 1-7v3 7c-2-1-4-3-6-5 1-1 1-1 2-1h0c0-3-1-5-2-7l-1-5-1-3z"></path><path d="M731 794l4 2h0l1-1c1 0 2 1 3 2 1 0 1 0 2-1v3h-1v-1c0 1-1 2-1 3-1 0-2 0-3 1v-1c0-1 0-2-1-3-2 2-2 5-2 8l1 2v8l1 3 1 5c1 2 2 4 2 7h0c-1 0-1 0-2 1h0c-2-2-3-4-5-6-1-3-1-8-1-10 1-3 2-5 1-7 0-1 0-2-1-2v-1h1c0-3 1-6 0-9v-3z" class="H"></path><path d="M731 826h1 0l1-1 1-3c1 1 1 2 1 3l1-1c1 2 2 4 2 7h0c-1 0-1 0-2 1h0c-2-2-3-4-5-6z" class="d"></path><path d="M285 126c1 1 2 1 3 0 2-3 1-11 1-15 4 4 5 7 5 13 0 1 0 2 1 3h32l-1 3h-1c-3 1-6 1-9 1h-26c-5-1-11-1-16-1h-25l-4-1h-1l-10-1c-1 0-3 0-4-1h7 9c0-7 1-10 5-15l1 14c4 1 8 1 12 0l1-2h1l1 2h1l2-2 5-9 1 1h1c2 1 3 2 4 4s1 4 1 6l2 2h0l1-2z" class="e"></path><path d="M291 117l1 1c1 2 1 6 0 8h-1c-1-3 0-6 0-9z" class="N"></path><path d="M316 129c3 0 6-1 9 1-3 1-6 1-9 1v-2z" class="T"></path><path d="M275 115l1 1h1c2 1 3 2 4 4s1 4 1 6c-2 1-6 0-9 0 0 0-1-1-1-2v-1l-2 1 5-9z" class="l"></path><path d="M275 115l1 1h1c-1 3-4 7-4 10 0 0-1-1-1-2v-1l-2 1 5-9z" class="I"></path><path d="M244 129h72v2h-26c-5-1-11-1-16-1h-25l-4-1h-1z" class="F"></path><path d="M740 109c1 2 2 4 3 7h0l3 7c1 2 1 3 3 3 2-1 1-2 3-2l1 1 1 1 6 1h0c-2 1-4 1-6 1 4 0 7 0 11 1h8 1l-1 1h-7v1l3 1h-8-60l3-1c-3 0-7 0-10-1v-1l-1-2c1-1 1 0 2 0h11 17c1-7 1-11 6-16 0 4-1 12 1 15 1 1 2 1 3 0 1-3 2-7 4-10l3-3-2-3 2-1z" class="X"></path><path d="M743 116h0l3 7-2 2-1 1h-4v-2c-1 0-1 1-3 2l-1-1c1-3 3-6 5-9h1 2z" class="o"></path><path d="M743 116h0l3 7-2 2-3-9h2z" class="I"></path><path d="M765 129h8 1l-1 1h-7v1l3 1h-8-60l3-1c-3 0-7 0-10-1 4-2 10-1 14-1h28 29z" class="h"></path><path d="M704 131c5-1 12 0 17-1h45v1l3 1h-8-60l3-1z" class="c"></path><path d="M752 771c1 1 4 2 5 4 0 1 0 0-1 1-1 3-1 9 0 12 0 1 2 2 2 3-1 5-2 11-2 17l-2 1v3c-1 0 0 0-1-1h0l-1-1-1 1h-2l1-3 3-4h-2c-2 1-4 1-6 1h-1-1v-1l-1-1v-3-3-1l-1-1v1c-1 1-1 1-2 1-1-1-2-2-3-2l-1 1h0l-4-2c0-1 1-1 1-1l1-1v-3h0c0-2 1-1 2-2h3 0l1-1c1-2 2-5 3-7 3-2 5-3 7-6h0l2 1 1-3z" class="R"></path><path d="M754 796h0v3 2l-1 1v2h0-2c0-3 2-6 3-8z" class="e"></path><path d="M749 794c2-2 6-2 9-2l-4 4h0c-3 0-3-1-5-2z" class="l"></path><path d="M739 786c1 0 2 1 3 2 1-1 1-2 2-3v1c0 2 0 2 1 3l-1 1h-1c-1-1-1 0-2-1h-1c-1 0-1 1-2 2h2l-3 1c-1 0-2 0-3-1 2-1 3-2 4-3v-1l1-1z" class="B"></path><path d="M731 794c0-1 1-1 1-1l1-1v-3h0c0-2 1-1 2-2h3 0v1c-1 1-2 2-4 3 1 1 2 1 3 1l3-1 2 1v1 3l-1-1v1c-1 1-1 1-2 1-1-1-2-2-3-2l-1 1h0l-4-2z" class="N"></path><path d="M740 791l2 1v1 3l-1-1c-1-1-2-2-4-3l3-1z" class="K"></path><path d="M749 773l2 1 2 1s1 0 1 1v3 11h-6c0-1 1-2 1-4s1-4 1-6c1-1 2-3 2-5-1 0-2-1-3-2h0z" class="H"></path><path d="M742 793l1 2 1-1c1 1 2 1 3 2h1l1-2c2 1 2 2 5 2-1 2-3 5-3 8-2 1-4 1-6 1h-1-1v-1l-1-1v-3-3-1-3z" class="N"></path><path d="M742 793l1 2 1-1c1 1 2 1 3 2h1l-2 4c0 2-1 3-1 4-1 0-1 0-1 1h-1v-1l-1-1v-3-3-1-3z" class="E"></path><path d="M742 793l1 2 1-1c1 2 1 3 1 5h-2l-1-2v-1-3z" class="I"></path><path d="M459 85l1 1 3 5c0 1 1 2 1 3l5 2c2 2 3 4 3 6l1 1h0v5 1l-4 1-3 1-6 3-3 3 2-1v1c-2 1-5 2-7 3-1 1-2 1-3 1h-1l-4 2-1-1 2-1 4-2-2-6v-3c-1-1 0-2 0-3v-1-3c0-5 3-11 4-16 1 0 1 1 1 2l1-1 1-1c1-1 1-1 3-1l2-1z" class="J"></path><path d="M447 107h0c3 3 4 7 6 11l4-3-1-5c1 1 1 3 1 4l1 1c1-1 1-1 1-2h0l1 1-3 3 2-1v1c-2 1-5 2-7 3-1 1-2 1-3 1h-1l-4 2-1-1 2-1 4-2-2-6v-3c-1-1 0-2 0-3z" class="B"></path><path d="M463 103c1-1 0-7 1-9l5 2c2 2 3 4 3 6l1 1h0v5 1l-4 1-3 1-6 3-1-1c1-1 1-2 1-3h0c1-3 1-5 0-7v-1c1 1 1 1 3 1z" class="M"></path><path d="M466 111c0-1-1-2-1-4-1-1-1-2-1-4l1 1v1h1v-1c-1-2-1-3-1-5h0c0-1 0-2 1-2 1 1 0 3 0 4v1l1 1v2 1h1l1 1v3l-3 1z" class="F"></path><path d="M459 85l1 1 3 5c0 1 1 2 1 3-1 2 0 8-1 9l-1-1c-1 0-3-2-3-3h-3 3l-1-2v-1c-1 0-1 1-1 2h-3c-1 1-2 1-3 2-1 0-1 0-1 1 0 2 1 6 2 8h1v2c1 1 1 2 1 3-1 0-1-1-1-2-1-1-1-2-2-3 0-1-1-2-2-3 0-1 1-2 0-3h-2c0-5 3-11 4-16 1 0 1 1 1 2l1-1 1-1c1-1 1-1 3-1l2-1z" class="G"></path><path d="M459 85l1 1 3 5c0 1 0 1-1 2l-1 3c0 1 0 1-1 2l-1-1v-1h-1v-5h1l-1-1-1 1h0-1c1-2 0-3 1-5l2-1z" class="m"></path><path d="M751 259c3-1 11-1 13 0s3 3 4 4l3 2c1 1 2 3 2 6h0v4l-1 3c0 8-1 14-5 21-1-1-1-2-1-4-2 3-4 4-7 5h-1v-2h-2c0-1 0-1 1-2v-2c0-1-1-1-2-2v-14c-1-5-2-9-4-14v-2l1-1c-1 0-1 0-2-1l1-1z" class="B"></path><path d="M757 292c2 0 4-1 6-1l2 1-1 2c-1 1-1 1-3 0h0-2l-1-2h-1z" class="E"></path><path d="M765 274v8c-1 0 0 1 0 1v-1-1l1-6v7c0 1-1 2-1 2-1 2-2 4-2 6h-6v-1c0-1 0-1 1-2h1c2-2 3-3 4-5l1-1c1-2 0-5 1-7z" class="R"></path><path d="M764 273l1 1c-1 2 0 5-1 7l-1 1h-3v1c-1 1-1 1-1 2-1-1-1-1-1-2v-4-3l1-1 1 1v-3h4z" class="K"></path><path d="M758 279v-3l1-1 1 1-1 6-1 1v-4z" class="Q"></path><path d="M758 279c0-3-2-12-1-14h4c1 3 2 5 3 8h-4v3l-1-1-1 1v3z" class="D"></path><defs><linearGradient id="G" x1="773.752" y1="292.279" x2="753.615" y2="257.686" xlink:href="#B"><stop offset="0" stop-color="#9a999a"></stop><stop offset="1" stop-color="#d8d6d8"></stop></linearGradient></defs><path fill="url(#G)" d="M751 259c3-1 11-1 13 0s3 3 4 4l3 2c1 1 2 3 2 6h0v4l-1 3c0 8-1 14-5 21-1-1-1-2-1-4l1-5h0l-4 1c-2 0-4 1-6 1v-2h6c1-1 2-2 3-2 2-1 2-4 3-5 0-8-2-15-7-21l-10-1c-1 0-1 0-2-1l1-1z"></path><path d="M768 263l3 2c1 1 2 3 2 6h0v4l-1 3c0-6-1-10-4-15z" class="E"></path><path d="M741 750l1 1v-1l1-70v1c0-1 0-1 1-1 0 3-1 8 0 12v1 45 11c1 2 0 4 1 6h1c1 3 4 6 6 8-1 2-3 3-4 5 1 1 2 2 4 3l-1 3-2-1h0c-2 3-4 4-7 6-1 2-2 5-3 7l-1 1h0-3c-1 1-2 0-2 2h0v3l-1 1s-1 0-1 1c-1-1-2-3-2-4l-2-1c2-4 2-10 0-13v-1l2-1c2-1 4-3 5-6v-2h0-1l-1-2c-1-2 5-7 7-9 1-1 0-5 0-6l2 1z" class="X"></path><path d="M733 766l8-6c0 3-1 5-3 7-1 1-2 1-4 1v-2h0-1z" class="o"></path><path d="M734 768c2 0 3 0 4-1l1 1h0v1c0 1 1 1 1 2l1 1 1-1v2h1v-1h1v3h1v-3c0-1-2-1-1-3v1c1 1 2 1 4 2l1 1h0c-2 3-4 4-7 6-1 2-2 5-3 7l-1 1h0-3c-1 1-2 0-2 2h0v3l-1 1s-1 0-1 1c-1-1-2-3-2-4l-2-1c2-4 2-10 0-13v-1l2-1c2-1 4-3 5-6z" class="V"></path><path d="M734 768c2 0 3 0 4-1l1 1c-1 2-3 3-3 5h-1c-2 1-4 2-6 2v-1c2-1 4-3 5-6z" class="E"></path><path d="M727 789c2-4 2-10 0-13v-1l2-1v1 1c1 2 2 3 3 5l2 3c-1 1-1 2-2 3h-1l1-1v-3h0l-1 3c0 2-1 3-2 4l-2-1z" class="B"></path><path d="M768 303c1-1 3-4 4-6h1v-1c0-1 0-2 1-3 2 0 6-1 8 1v1c1 0 2 1 3 1h1c0 1 0 1-1 1-2 2-4 4-5 6 1 2 3 4 4 6-1 1-2 1-3 2-1 0-2 0-3 1v1l1 5v2l1 2-1 1-2 3-2 1-4 1-3 2-6 9-2 1c-2-1-3-3-4-4l1-1c1 1 1 2 2 3 1-1 2-2 2-3l-2 1-5-5c-2-1-3-1-4 0-2 1-4 3-5 4h0c0-2 2-4 4-5v-1h0 0l-2 1v-1l1-3c3-2 5-5 8-5v1l4 2c2-1 0-1 1-3l2-2c1 0 1-1 2-1 0-2 0-2-1-3l2-2-1-2 1-2c1 0 1 0 2-1l-2-2v-1h-1l1-2h2z" class="U"></path><path d="M774 312h1c1-2 4-1 6-1-1 0-2 0-3 1v1l1 5v2h-3-1c-2 1-2 1-4 0h-1 0l3-3h0-2 0c2-1 3-2 4-3l-1-2z" class="G"></path><path d="M779 318v2h-3-1c-2 1-2 1-4 0l2-1h1c1-1 3-1 5-1z" class="J"></path><path d="M779 320l1 2-1 1-2 3-2 1-4 1-3 2h-1l1-1v-1c1-1 2-1 3-2h-2l-1-1c3-2 6-4 9-4h1l-2-1h3z" class="F"></path><path d="M771 326c2-2 5-3 8-3l-2 3-2 1-4 1-3 2h-1l1-1v-1c1-1 2-1 3-2z" class="C"></path><path d="M762 325h1 1v1l-1 1h0 2l-1 2h0 1c0 1-2 3-3 4l1 1 4-4h1l-6 9-2 1c-2-1-3-3-4-4l1-1c1 1 1 2 2 3 1-1 2-2 2-3l-2 1-5-5 1-1c1 1 3 4 5 5h0v-2-1-3h1l-1-1c1-1 2-2 2-3z" class="L"></path><path d="M748 326c3-2 5-5 8-5v1l-1 1h1c2 1 2 1 4 3v2l1 1h-1v3 1 2h0c-2-1-4-4-5-5l-1 1c-2-1-3-1-4 0-2 1-4 3-5 4h0c0-2 2-4 4-5v-1h0 0l-2 1v-1l1-3z" class="g"></path><path d="M760 326v2l1 1h-1v3 1 2h0c-2-1-4-4-5-5l-3-1v-1c2 0 3 1 4 1h1 0l-2-2v-1c1 1 2 1 4 1l1-1z" class="C"></path><path d="M768 303c1-1 3-4 4-6h1v-1c0-1 0-2 1-3 2 0 6-1 8 1v1c1 0 2 1 3 1h1c0 1 0 1-1 1-2 2-4 4-5 6 1 2 3 4 4 6-1 1-2 1-3 2-2 0-5-1-6 1h-1l-3 3-2 2c-3 1-5 5-7 8 0 1-1 2-2 3v-2c-2-2-2-2-4-3h-1l1-1 4 2c2-1 0-1 1-3l2-2c1 0 1-1 2-1 0-2 0-2-1-3l2-2-1-2 1-2c1 0 1 0 2-1l-2-2v-1h-1l1-2h2z" class="O"></path><path d="M770 303l2-1c1 1 2 0 4 1v1s1 0 0 1c2 1 4 1 5 3l-1 1c-2-1-4-3-6-2l-1 1h-1v-2l1-1c-1-1-2 0-3-1v-1z" class="B"></path><path d="M766 303h2c1 2 1 3 4 3h0v2l-6 5-1-2 1-2c1 0 1 0 2-1l-2-2v-1h-1l1-2z" class="K"></path><path d="M768 303c1-1 3-4 4-6h1v-1c0-1 0-2 1-3 2 0 6-1 8 1v1l-2 1 1 1c-1 2-3 1-5 2s-3-1-5 2c0 0-1 1-1 2v1c1 1 2 0 3 1l-1 1h0c-3 0-3-1-4-3z" class="L"></path><path d="M537 86c3-1 4-2 6-2v5l-1 2h1v1 2l2 1v2 5c1 1 0 0 1 0l1-1v-1h0c2-1 2 1 4 2v-5l3-3c2 3 1 7 2 11v1h1v-1h1v3h1c0-1 0-3 1-5h1c1 1 1 3 3 3l2-2v1h1l2-2 2-1-1 7-1 1-1 8-1 2c-18-8-35-13-55-14h8v-1h-1l1-1c3 0 6 0 9 1-1-1-2-3-1-4l1-2-1-1c1-3 2-5 2-8 1-1 6-4 7-4z" class="I"></path><path d="M552 111v-1c0-2 0-2 1-3v4h1l1-1v1h2l2 1v1h4v1c1-1 1 0 1-1h2c0-1 0-2 1-3h1 1l-1 8-16-7z" class="T"></path><path d="M569 103l2-1-1 7-1 1h-1-1c-1 1-1 2-1 3h-2c0 1 0 0-1 1v-1h-4v-1c1 0 0-1 1-2l2 1c1-2 2-2 2-5l2-2v1h1l2-2z" class="a"></path><path d="M564 113c1-2 1-4 3-5l1 2h-1c-1 1-1 2-1 3h-2z" class="d"></path><path d="M551 102h1c0-2 0-4 1-6v9c1 0 0 1 0 2h0c-1 1-1 1-1 3v1l-4-1c-1-1-1 0-2-1v-5l1-4h0c2-1 2 1 4 2z" class="F"></path><path d="M546 104l1 1h2c1 1 1 2 1 3l-1 1c-1 0 0 0-1 1-1-1-1 0-2-1v-5z" class="a"></path><path d="M551 97l3-3c2 3 1 7 2 11v1h1v-1h1v3h1c0-1 0-3 1-5h1c1 1 1 3 3 3 0 3-1 3-2 5l-2-1c-1 1 0 2-1 2l-2-1h-2v-1l-1 1h-1v-4h0c0-1 1-2 0-2v-9c-1 2-1 4-1 6h-1v-5z" class="Z"></path><path d="M537 86c3-1 4-2 6-2v5l-1 2h1v1 2l2 1v2 5 1l-1 5c-3 0-6-1-8-2h-1c0-2 1-3 0-5 0 1-1 3-1 4v1l-5-1c-1-1-2-3-1-4l1-2-1-1c1-3 2-5 2-8 1-1 6-4 7-4z" class="l"></path><path d="M530 90l2 2c-1 3-1 4-3 7l-1-1c1-3 2-5 2-8z" class="n"></path><path d="M542 91h1v1 2l2 1v2 5 1l-1 5c-3 0-6-1-8-2h1c1-1 3-4 3-6l1-1v-2c1-2 0-4 1-6z" class="G"></path><path d="M430 101c2-1 3 0 5 0 2 2 5 3 8 5h0c2 2 3 5 4 7l2 6-4 2-2 1 1 1 4-2h1l-19 13-2-2h-1c-2 3-4 4-7 5-2 2-4 4-6 5l2-3v-3c1 0 1-3 2-4 0-2 1-3 1-5 0-3 1-8 0-11 0-1-2-2-2-3v-4l1 1c0-2-1-3 1-4 1 1 1 2 1 4v1c1-2 1-3 1-5l1 1c1 0 3 1 4 0l1-3c0-1 0-2 1-3h2z" class="l"></path><path d="M428 101h2c-1 4-1 6 0 10l-1-1c-1 0-2-1-3-1 1-2 1-3 1-5 0-1 0-2 1-3z" class="K"></path><path d="M427 104c0 2 0 3-1 5 1 0 2 1 3 1-2 0-6-1-7 1 0 1 1 2 2 3h2v1l1 1c2 2 2 4 3 6v5c-1-1-1-1-1-2-1-1-2-2-2-3 0-2-1-3-1-4-1-3-4-5-6-7 1-2 1-3 1-5l1 1c1 0 3 1 4 0l1-3z" class="Q"></path><path d="M438 125c0-3 0-5-1-8l-1-3v-1-1l1 1v1c1 0 1 1 2 1 0-2 1-5 2-7h0l1 1v2c1 1 1 2 1 4l1 4c0 1 0 1 1 2l-2 1c-2 1-3 2-5 3z" class="m"></path><path d="M426 114c1 0 3-1 5-2v2c-1 1-1 2-1 3v1c1 3 2 7 2 10l6-3c2-1 3-2 5-3l1 1 4-2h1l-19 13-2-2h-1v-1h0c2-1 2-2 3-4v-5c-1-2-1-4-3-6l-1-1v-1z" class="K"></path><path d="M418 110c0-2-1-3 1-4 1 1 1 2 1 4v1c2 2 5 4 6 7 0 1 1 2 1 4 0 1 1 2 2 3 0 1 0 1 1 2-1 2-1 3-3 4h0v1c-2 3-4 4-7 5-2 2-4 4-6 5l2-3v-3c1 0 1-3 2-4 0-2 1-3 1-5 0-3 1-8 0-11 0-1-2-2-2-3v-4l1 1z" class="U"></path><path d="M420 110v1c2 2 5 4 6 7l-2 3c-1-3-4-8-4-11z" class="J"></path><path d="M426 118c0 1 1 2 1 4 0 1 1 2 2 3 0 1 0 1 1 2-1 2-1 3-3 4h0c-1-1-1-5-2-7 0-1 0-2-1-3l2-3z" class="F"></path><path d="M427 122c0 1 1 2 2 3 0 1 0 1 1 2-1 2-1 3-3 4v-9z" class="b"></path><path d="M417 109l1 1 4 8c2 4 2 8 1 12-1 3-2 5-3 7-2 2-4 4-6 5l2-3v-3c1 0 1-3 2-4 0-2 1-3 1-5 0-3 1-8 0-11 0-1-2-2-2-3v-4z" class="W"></path><path d="M422 118c2 4 2 8 1 12-1 3-2 5-3 7-2 2-4 4-6 5l2-3 2-1v-2c1-1 3-5 3-6v-1c1-2 0-8 1-11z" class="P"></path><path d="M840 118v1c1 4 1 6-1 10 2 4 5 9 7 12v2c-1 0-1 1-2 0h0c-7-1-14 0-21 0v-3c-4 0-8-1-11-1-10 0-19 1-28 0h-4v-2h5v-2-2h-2-1-2c-1 0-2 0-3-1h-4-4l-3-1v-1h7l1-1h-1c1-1 8 0 10-1-3-1-6 1-8 0 2 0 6 0 9-1h1 17c3 0 9 1 12-1h2l1 1h3c3 0 5 0 7-2 0 0 1 1 2 1h1l5-1 2-1v-3h1c0 1 0 1 1 2h0c0-2 0-3 1-5z" class="I"></path><path d="M790 137l1 1 1-1h2s0 1 1 1l1-1h5v1h1l1-1v1c1 1 1 0 1 1h-20-4v-2h5 4 1z" class="g"></path><path d="M823 140c4-1 17-1 20 2h1v1c-7-1-14 0-21 0v-3z" class="L"></path><path d="M831 134h8v1c1 1 2 1 2 3-1 1-2 1-3 1v-1c-5 1-9 1-14 1h-4 0l3-2c2 0 7 1 9-1-1-1-1-1-1-2z" class="k"></path><path d="M839 135c1 1 2 1 2 3-1 1-2 1-3 1v-1c-1 0-1-1-2-1l-1-1h0c1 0 3 0 4-1z" class="j"></path><defs><linearGradient id="H" x1="822.609" y1="130.815" x2="811.891" y2="135.185" xlink:href="#B"><stop offset="0" stop-color="#58595a"></stop><stop offset="1" stop-color="#716e70"></stop></linearGradient></defs><path fill="url(#H)" d="M827 132c3 0 7-1 11 0 0 1 1 1 1 2h-8-11c-1 1-2 1-2 0-2 0-5 1-7 1l-1-1v-1c-1 0-2 0-3-1h20z"></path><path d="M811 135c2 0 5-1 7-1 0 1 1 1 2 0h11c0 1 0 1 1 2-2 2-7 1-9 1l-3 2h0l-2-2v1c-1 1-7 1-8 1v-4h1z" class="W"></path><path d="M773 132h13 21c1 1 2 1 3 1v1l1 1h-1v4h-3v-1l-1 1h-2c0-1 0 0-1-1v-1l-1 1h-1v-1h-5l-1 1c-1 0-1-1-1-1h-2l-1 1-1-1h-1-4v-2-2h-2-1-2c-1 0-2 0-3-1h-4z" class="P"></path><path d="M801 134h8v4l-2 1v-1l-1 1h-2c0-1 0 0-1-1v-1l-1 1h-1v-1l-2-1h-2-1l-1-1c2 0 4 0 6-1h0z" class="W"></path><path d="M773 132h13c1 0 2 1 3 2h3 9 0c-2 1-4 1-6 1l1 1h1 2l2 1h-5l-1 1c-1 0-1-1-1-1h-2l-1 1-1-1h-1-4v-2-2h-2-1-2c-1 0-2 0-3-1h-4z" class="h"></path><path d="M785 135h0 4l1 2h-1-4v-2z" class="V"></path><defs><linearGradient id="I" x1="825.9" y1="119.77" x2="815.062" y2="136.037" xlink:href="#B"><stop offset="0" stop-color="#090b0b"></stop><stop offset="1" stop-color="#3f3c3e"></stop></linearGradient></defs><path fill="url(#I)" d="M827 125s1 1 2 1h1l5-1c2 1 3 2 4 2v3c-1 0-2 0-3-1-2 2-3 2-6 2v-1c-2 0-2 0-3 1h-4 1l3 1h-20-21-13-4l-3-1v-1h7l1-1h-1c1-1 8 0 10-1-3-1-6 1-8 0 2 0 6 0 9-1h1 17c3 0 9 1 12-1h2l1 1h3c3 0 5 0 7-2z"></path><path d="M785 127h17c-1 0-2 1-2 1-1 1-4 0-5 0-4 1-8 1-12 1v-1c-3-1-6 1-8 0 2 0 6 0 9-1h1z" class="O"></path><path d="M773 130h24c5 0 9 1 13 1h4l-1-1h1l1-1h1c1 1 5 1 7 1 3-1 6 0 9-1h4c-2 2-3 2-6 2v-1c-2 0-2 0-3 1h-4 1l3 1h-20-21-13-4l-3-1v-1h7z" class="D"></path><path d="M399 83c2 1 4 4 6 6l10 10c2 2 5 4 6 7 0 2 0 3-1 5v-1c0-2 0-3-1-4-2 1-1 2-1 4l-1-1v4c0 1 2 2 2 3 1 3 0 8 0 11 0 2-1 3-1 5-1 1-1 4-2 4l-3 3c-3 3-5 8-9 11-2 1-3 2-5 3v-2c1-1 1-2 1-4l-3 4v-3c0-1 1-1 2-2 2-3 4-5 4-9l3-3v-1l-1-1-5 5h0v-4 1l-3 1h-1l-3 3c-1 1-3 3-5 3 3-2 6-5 7-9v-1c0-2 2-6 3-8 2-3 1-4 2-7v-11c1-1 1-4 1-6-1-1-1-1-1-2l-1-1v-1c0-2-1-2-2-3l-2-3v1c1 3 1 5 1 8-1-2-1-4-1-6l-1-1c0-1-1-1-1-3 2-1 4-2 5-4l1-1z" class="J"></path><path d="M408 106l2 2c2 9 0 16-4 25l-1-1c4-7 3-18 3-26z" class="L"></path><path d="M399 83c2 1 4 4 6 6l10 10c2 2 5 4 6 7 0 2 0 3-1 5v-1c0-2 0-3-1-4-2 1-1 2-1 4l-1-1v4c-1 0-1 1-1 2l-1-5v5h-1c0-11-3-17-10-25 3 6 5 11 6 18l-2-2c0-2-1-5-3-6 0-4-4-14-7-16l1-1z" class="I"></path><path d="M415 110c0-2-1-5-2-8v-4c2 3 3 7 4 11v4c-1 0-1 1-1 2l-1-5z" class="h"></path><path d="M415 110l1 5c0-1 0-2 1-2 0 1 2 2 2 3 1 3 0 8 0 11 0 2-1 3-1 5-1 1-1 4-2 4l-3 3c-3 3-5 8-9 11-2 1-3 2-5 3v-2c1-1 1-2 1-4l-3 4v-3c0-1 1-1 2-2 2-3 4-5 4-9l3-3 4-6v2c3-4 4-11 4-15h1v-5z" class="F"></path><path d="M416 125c0-1 1-3 1-4l1-4c0 3 0 7 1 10 0 2-1 3-1 5l-1-4c0-1 0-1-1-3z" class="M"></path><path d="M416 125c1 2 1 2 1 3l1 4c-1 1-1 4-2 4l-3 3 1-4c-2 2-3 4-6 5 1-2 3-3 4-5 1-3 3-6 4-10z" class="N"></path><path d="M414 135c1-2 2-4 3-7l1 4c-1 1-1 4-2 4l-3 3 1-4z" class="J"></path><path d="M408 140c3-1 4-3 6-5l-1 4c-3 3-5 8-9 11-2 1-3 2-5 3v-2c4-4 7-7 9-11z" class="G"></path><path d="M415 110l1 5c-1 5-2 11-5 16-1 3-3 5-5 7l-6 9-3 4v-3c0-1 1-1 2-2 2-3 4-5 4-9l3-3 4-6v2c3-4 4-11 4-15h1v-5zm-17-26c3 2 7 12 7 16 2 1 3 4 3 6 0 8 1 19-3 26l-5 5h0v-4 1l-3 1h-1l-3 3c-1 1-3 3-5 3 3-2 6-5 7-9v-1c0-2 2-6 3-8 2-3 1-4 2-7v-11c1-1 1-4 1-6-1-1-1-1-1-2l-1-1v-1c0-2-1-2-2-3l-2-3v1c1 3 1 5 1 8-1-2-1-4-1-6l-1-1c0-1-1-1-1-3 2-1 4-2 5-4z" class="D"></path><path d="M401 98c3 11 0 24-6 34h0v-1c0-2 2-6 3-8 2-3 1-4 2-7v-11c1-1 1-4 1-6v-1z" class="F"></path><path d="M398 84c3 2 7 12 7 16 1 6 2 13 0 19 0 1 0 2-1 3s-1 2-2 4h-1c2-7 3-15 3-23-1-6-3-13-8-18l5 13v1c-1-1-1-1-1-2l-1-1v-1c0-2-1-2-2-3l-2-3v1c1 3 1 5 1 8-1-2-1-4-1-6l-1-1c0-1-1-1-1-3 2-1 4-2 5-4z" class="J"></path><path d="M405 100c2 1 3 4 3 6 0 8 1 19-3 26l-5 5h0v-4 1l-3 1h-1l5-9h1c1-2 1-3 2-4s1-2 1-3c2-6 1-13 0-19z" class="W"></path><path d="M401 126h1c1-2 1-3 2-4 0 3 0 5-2 8-1 1-2 2-2 3v1l-3 1h-1l5-9z" class="a"></path><path d="M234 266c1 0 2-1 3 0 3 0 7-1 8 1-1 1-2 1-4 1h1l1 1h0l-1 3 2-1c1 0 2-1 3-3v-2h2 0c-1 3-2 6-2 9-1 2-1 4 0 6 0 4 1 8 2 12 1-1 1-2 2-3h2l-1 1c0 1 0 2 1 3h0c1 1 1 1 3 1 1 0 1 0 1-1v1h0c2 0 4 0 5 1h2l4 10v1 2c1 2 2 3 3 5h0c2 3 3 4 5 6l1 1c2 0 2 0 4-1v1l1 1v1 1l-3 4v2l-1 1h0l-1-1c0-1-1-1-1-1h-1-1c-1-3-4-5-7-6h-1c-1-1-2-2-3-2-2-1-3 0-4-1-1-2-1-3-2-4-2-1-5-2-7-4-1-1-1-2-3-2l-3-3v-1c-2 0-2-1-3 0h-1l1-2-1-1h-2v-1c-2-2-4-4-5-6l1-2c0-4-1-9-2-13-1-1 0-3 1-4 0-3 2-9 1-11z" class="I"></path><path d="M257 295c2 0 4 0 5 1s1 2 0 3h0c-1-1-2-2-3-2l-1-1-1-1zm11 11v1 2 1c-3 0-5-1-7-1h-1v-1c3 0 4-1 8-2z" class="e"></path><path d="M235 285c2 0 6 1 8 1v1c0 1 1 1 1 2h-1c-2 1-5 0-6-1-1 0-1-2-2-3z" class="H"></path><path d="M237 291h1c2 0 6 1 7 3v1h-1c-2 0-6 1-7 0v-4zm12 2c1-1 1-2 2-3h2l-1 1c0 1 0 2 1 3 0 2 0 4 2 5 1 0 4 0 5 1h0l-6 1c-2-2-4-5-5-8z" class="W"></path><path d="M237 266c3 0 7-1 8 1-1 1-2 1-4 1h1l1 1h0l-1 3h-1l-1 1 1 1h0-3c-1-1-2-1-2-3 0-1 1-1 1-2v-1h2v-1h-1l-1-1z" class="L"></path><path d="M244 307h4 1l-1 1c0 1 1 2 2 2h2c3 0 9 1 12 2l1 1h-2c0 1 1 1 2 1 2 1 3 2 4 4h-1-2c-3-1-6-1-9-2-2-1-5-2-7-4-1-1-1-2-3-2l-3-3z" class="P"></path><path d="M268 310v-1c1 2 2 3 3 5h0c2 3 3 4 5 6l1 1c2 0 2 0 4-1v1l1 1v1 1l-3 4v2l-1 1h0l-1-1c0-1-1-1-1-1h-1-1c-1-3-4-5-7-6h-1c-1-1-2-2-3-2-2-1-3 0-4-1-1-2-1-3-2-4 3 1 6 1 9 2h2 1c-1-2-2-3-4-4l1-2h2 0v-2z" class="D"></path><path d="M269 318c1 0 3 2 4 3 0 0-1 1 0 1 0 1 2 2 2 2l3 3 1 1v2l-1 1h0l-1-1c0-1-1-1-1-1l-1-2c-2-3-4-5-8-6-1 0 0 0-1-1h2v-2h1z" class="K"></path><path d="M268 310v-1c1 2 2 3 3 5h0c2 3 3 4 5 6l1 1c2 0 2 0 4-1v1l1 1v1 1l-3 4-1-1-3-3s-2-1-2-2c-1 0 0-1 0-1-1-1-3-3-4-3-1-2-2-3-4-4l1-2h2 0v-2z" class="O"></path><path d="M687 127h6l1 2v1c3 1 7 1 10 1l-3 1h60 8 4 4c1 1 2 1 3 1h2 1 2v2 2h-5v2h-3-8l-1 1h0 1l3 1h5v1c-3 0-9 1-11 0l-7 1h-19c-2 1-23 1-26 1-1 0-1 0-1 2 1 0 3 0 5-1v1h-4c-1 0-1 0-2-1h-1 0v-1c-1 1-1 1-1 2h-1l-1 1v1c-1 0-2-2-3-3-1 1-1 1-1 2-1 0-2 0-3-1h-4c-2 1-3 1-5 1h-2c-4-1-9 0-13 0l-1-2v-1c-1-4 0-12 1-16 2-2 8-1 10-1z" class="l"></path><path d="M757 139c1-2 2-3 4-4h0v3l2 1h0-6z" class="G"></path><path d="M696 138h1 2v1h2v-1l1 1h4 1v-1-3c0 1 1 2 0 4h4c1-1 1-2 1-4l1 4c1 0 3 1 4 0h1c2 1 2 0 5 0l-2 2h-13c-2-1-3 0-5 0-1 0-1 0-2-1l-1 1h-7c2-1 2-1 2-3h1z" class="j"></path><path d="M769 132h4 4c1 1 2 1 3 1h2 1 2v2 2h-5v2h-3-8-6 0l1-2c0 1 0 1 1 1 1-1 0 0 2-1 0 1 0 1 1 1v-5h-1l-1 1v1l-1-3h-4 0 8z" class="a"></path><path d="M776 133l1 1 1 1 1-1h1v3h-3-1l-1-1c0-1 1-2 1-3z" class="m"></path><path d="M769 133h1v1h1l1-1 1 1 1-1h0v2s0 1-1 2h-3 0c-1-1-1-2-1-4z" class="n"></path><path d="M763 139h6l-1 1h0 1l3 1h5v1c-3 0-9 1-11 0-3-1-8 0-11-1h-34l2-2h2 10 22 6z" class="k"></path><path d="M772 141h5v1c-3 0-9 1-11 0-3-1-8 0-11-1 5-1 12 0 17 0z" class="P"></path><path d="M693 141h7l1-1c1 1 1 1 2 1 2 0 3-1 5 0h13 34c3 1 8 0 11 1l-7 1h-19c-2 1-23 1-26 1-1 0-1 0-1 2 1 0 3 0 5-1v1h-4c-1 0-1 0-2-1h-1 0v-1c-1 1-1 1-1 2h-1l-1 1v1c-1 0-2-2-3-3-1 1-1 1-1 2-1 0-2 0-3-1h-4c-2 1-3 1-5 1h-2c-4-1-9 0-13 0l-1-2 1-1c0-1 1-1 2-1h14v-2z" class="X"></path><path d="M693 141h7l1-1c1 1 1 1 2 1 2 0 3-1 5 0h13 34c3 1 8 0 11 1l-7 1h-19-47v-2z" class="H"></path><path d="M687 127h6l1 2v1c3 1 7 1 10 1l-3 1c-2 0-4 0-6 1h-2c1 1 1 2 1 4h1c1 0 1 0 1 1h-1c0 2 0 2-2 3v2h-14c-1 0-2 0-2 1l-1 1v-1c-1-4 0-12 1-16 2-2 8-1 10-1z" class="h"></path><path d="M692 133h1c1 1 1 2 1 4h1c1 0 1 0 1 1h-1c-3-1-7 2-10 0v-1h1v-2c1 0 2-1 3-1s1 1 2 1c1 1 1 1 2 1 0-1 0-2-1-3z" class="g"></path><path d="M678 137c1 1 3 2 4 2s1 0 2-1v-3h1v2 1c3 2 7-1 10 0 0 2 0 2-2 3v2h-14c-1 0-1-1-2-1l1-1 5 1c-1-1-3-1-5-3v-2z" class="b"></path><path d="M687 127h6l1 2v1c3 1 7 1 10 1l-3 1c-2 0-4 0-6 1h-2-1-10v1h0c-1 1-2 2-4 2v1 2c2 2 4 2 5 3l-5-1-1 1c1 0 1 1 2 1-1 0-2 0-2 1l-1 1v-1c-1-4 0-12 1-16 2-2 8-1 10-1z" class="C"></path><path d="M678 136c0-1 0-2 1-3h3v1h0c-1 1-2 2-4 2z" class="F"></path><path d="M687 127h6l1 2h-2c-3-1-8 1-12 0v-1c2 0 4 0 6-1h1z" class="O"></path><path d="M206 126c2 1 5 1 8 1h15 8-7c1 1 3 1 4 1l10 1h1l4 1h25c5 0 11 0 16 1h26c3 0 6 0 9-1h1l1 1c3 0 11 1 13-1l1 1v1l-1 1c0 1 1 3 1 5l-1 1h-1-1c-3 1-5 0-8 1-1 0-7 0-7-1h-7v1 1h-19-14l-41-1v-1h-14-10-6 0-9-2c-1-1-1-2-3-3 0-1-1-2-1-3l3-1h0 15c3-1 6-1 9-1h0l-15-1h-6v-1h2l1-3z" class="l"></path><path d="M246 132l11-1-3 1-1 2h-2l-1 1 1 1h0c1 1 2 1 3 1l1-1v1c1 0 0 0 1 1h0l1 1h-12c1-1 1 0 2-1 1 0 1 0 1-1-2 0-2 0-3-2h0 1v-3z" class="G"></path><path d="M316 132h7v5 2h-7v1l-3-1c0-1 1-1 2-2s1-3 1-5z" class="m"></path><path d="M316 139l1-1c1-2 2-1 3-1h1s1 1 2 0v2h-7z" class="N"></path><path d="M320 137c-2-1-2-1-3-2v-2h3c1 1 1 2 1 3v1h-1z" class="o"></path><path d="M325 130h1l1 1c3 0 11 1 13-1l1 1v1l-1 1-3-1h-7-7-7l-23-1s-2 1-3 0h26c3 0 6 0 9-1z" class="R"></path><path d="M209 130h7c6 0 12 1 17 0h16 25c5 0 11 0 16 1 1 1 3 0 3 0-3 1-7 0-10 0h-26l-11 1h-2-4-10c-4-1-10 0-14 0h-1c3-1 6-1 9-1h0l-15-1z" class="Q"></path><defs><linearGradient id="J" x1="217.699" y1="121.211" x2="227.412" y2="136.19" xlink:href="#B"><stop offset="0" stop-color="#3e3a3d"></stop><stop offset="1" stop-color="#545658"></stop></linearGradient></defs><path fill="url(#J)" d="M206 126c2 1 5 1 8 1h15 8-7c1 1 3 1 4 1l10 1h1l4 1h-16c-5 1-11 0-17 0h-7-6v-1h2l1-3z"></path><path d="M323 132h7 7l3 1c0 1 1 3 1 5l-1 1h-1-1c-3 1-5 0-8 1-1 0-7 0-7-1v-2-5z" class="W"></path><path d="M330 132h7v1l1 1c-1 0-2 1-2 1l-1 1-1 1-1-1c-2-1-7 0-9 0v-3l6-1z" class="T"></path><path d="M257 139h16c3 0 7 1 10 0h1c2 1 6 0 8 0 6 0 13 1 19 0v-1c-1-2-1-3 0-4 0 1 0 4 1 5h1l3 1v1h-19-14l-41-1v-1h3 12z" class="S"></path><path d="M215 132h1c4 0 10-1 14 0h10 4 2v3h-1 0c1 2 1 2 3 2 0 1 0 1-1 1-1 1-1 0-2 1h-3-14-10-6 0-9-2c-1-1-1-2-3-3 0-1-1-2-1-3l3-1h0 15z" class="g"></path><path d="M197 133l3-1c1 1 1 2 2 3l1-1c2 0 4 0 6-1v1c0 1 0 1-1 2h0c-1 0-2 0-2 1h-2c0 1 0 1-1 2h-2c-1-1-1-2-3-3 0-1-1-2-1-3z" class="P"></path><path d="M216 132c4 0 10-1 14 0h1c1 1 1 1 2 1-1 1-1 1-2 1s-1 0-1-1l-1 1h-1l-1 1-1-1h-1l-1 1 1 1h-1c0-1-1-1-1-2h-1-2v1h2l1 2-2 1-1-1h-1-1v-2h-1v2h-1l-1-1-1-1v2s-1 0-2 1v-1-1l-1 1h0-1c0-2 0-2 1-4 1 1 1 1 2 1 2-1 1 0 3 0 0 0 2-1 3-1-1 0-2 0-3-1z" class="W"></path><path d="M244 132h2v3h-1 0c1 2 1 2 3 2 0 1 0 1-1 1-1 1-1 0-2 1h-3-14l-1-2h-1c0 1 0 1-1 2 0-1 0-1-1-2h0-1 0l-1-2h-2v-1h2 1c0 1 1 1 1 2h1l-1-1 1-1h1l1 1 1-1h1l1-1c0 1 0 1 1 1s1 0 2-1c-1 0-1 0-2-1h-1 10 4z" class="d"></path><path d="M244 132h2v3h-1 0c-1 1-2 2-3 2h-2l-2-2 2-3h4z" class="G"></path><path d="M244 132h2v3h-1 0c-1 1-2 2-3 2h-1v-2c0-1 1-1 1-1l2-2z" class="m"></path><path d="M473 83l3-3c4 2 9 5 12 7l1 1h0c2-1 3-1 4-1h2v-2c1-5 3-11 7-13 1-1 3-2 4-3 2-2 2-4 2-6 0-1 0-1 1-2 2 2 3 5 4 8h0c3 1 5 2 6 4 1 3 2 7 3 10v4c2 0 3 1 5 1l10-4v2c-1 0-6 3-7 4 0 3-1 5-2 8l1 1-1 2c-1 1 0 3 1 4-3-1-6-1-9-1l-1 1h1v1h-8l-22 2h-5l-21 7-2 1-3 1v-1l-2 1 3-3 6-3 3-1 4-1v-1-5h0v-3l1-4c-2-2-3-3-4-6 1-2 3-4 4-6l-1-1z" class="R"></path><path d="M505 74c1-2 2-5 4-7l-1 10c-2-1-2-1-3-3z" class="n"></path><path d="M511 96h-2c-1 1-1 1-2 1 0-3 0-6-1-8v-6c0-3-1-6-1-9 1 2 1 2 3 3-1 3-1 6-1 10 1 1 1 2 1 3v1 4h3v1z" class="G"></path><path d="M498 103c-3-4-6-9-9-13 3 0 5-1 8-2h0l1 1c0 2-1 4 0 5v1c1 1 1 1 1 2h0v1c1 1 1 2 1 3l-2 2z" class="n"></path><path d="M497 88c0-6 3-11 6-16 1 5 2 9 3 13v14c0 1 0 3-1 5l-1-1c-2 2-3 1-5 1l-1-1 2-2c0-1 0-2-1-3v-1h0c0-1 0-1-1-2v-1c-1-1 0-3 0-5l-1-1z" class="l"></path><path d="M499 97c2 2 3 4 5 5v1c-2 2-3 1-5 1l-1-1 2-2c0-1 0-2-1-3v-1z" class="Z"></path><path d="M474 84c2-1 3-1 4 0 2 1 7 2 8 4 2 5 5 8 7 12l4 4c-4 1-8 1-12 2l-6 1c-2 1-5 2-6 2v-1-5h0v-3l1-4c-2-2-3-3-4-6 1-2 3-4 4-6z" class="n"></path><path d="M485 106l-1-3c0-1 0-1-1-2l-2-9v-1-1c-1-1-1-1 0-2h0l1 6 1 1 1 3v3l1 2c0 1 1 1 1 1h2v-5h1c0 1 0 2 1 4h1c2-1 2-1 2-3l4 4c-4 1-8 1-12 2zm-7-5l-1-8h0c1 2 2 6 2 8l1 1-1 2v3c-2 1-5 2-6 2v-1-5h0v-3c1 1 2 2 3 2l2-1z" class="J"></path><path d="M473 100c1 1 2 2 3 2l2-1c-1 1-1 2-1 3v2 1c-2-1-3-1-3-2-1-1-1-1-1-2h0v-3z" class="N"></path><path d="M513 69c3 1 5 2 6 4 1 3 2 7 3 10v4c2 0 3 1 5 1l10-4v2c-1 0-6 3-7 4 0 3-1 5-2 8l1 1-1 2c-1 1 0 3 1 4-3-1-6-1-9-1l-1 1-2-1h-10c0-1 1-1 2-1h3l1-1-1-1-1 1h-1c2-2 3-1 5-3h-3v-1l-1-1v-1-1-2s0-1 1-1c-1-1-1-1-2-1h0l-1-1c1 0 2 0 3-1 0 0-1 0-1-1l-1 1-1-1v-1l1-1v-1h-2l1-1h2v-1h-3c1-2 3-3 4-4 0-1 0-2 1-2l-1-1v-6h1v-1z" class="l"></path><path d="M513 69c3 1 5 2 6 4 1 3 2 7 3 10h-1c-1 0-1 0-1-1l-1-1c0-1 0-2-1-3v-3c-1-2-2-3-3-3l-2 1v-3-1z" class="e"></path><path d="M521 83h1v4c2 0 3 1 5 1l10-4v2c-1 0-6 3-7 4 0 3-1 5-2 8l1 1-1 2c-1 1 0 3 1 4-3-1-6-1-9-1l-1 1-2-1c2-3 4-6 7-8l-1-2c-1-1-2-1-2-2v-2-7z" class="E"></path><path d="M521 90h0c2 1 3 1 5 0l1 1v1c-1 1-2 3-3 4l-1-2c-1-1-2-1-2-2v-2z" class="n"></path><path d="M520 104l8-10v1 2 1l1 1-1 2c-1 1 0 3 1 4-3-1-6-1-9-1z" class="G"></path><path d="M276 629v-3c1-1 2-1 3-1l1 1-1 1v4l1 1c1-1 1-3 1-4h0l1 1c0 4 1 8 2 12s3 8 4 13h-4l-3-1v4l4 4c-2 2-3 0-4 1s-2 5-2 7c-1 17 0 35 0 53v20c0 3 0 8 1 11 1 4 5 8 7 11-1 1-3 2-4 4 1 2 2 2 4 3 1 1 4 2 5 4l-1 1c-1 2 0 6 0 9 0 1-1 1 0 2-1 1-1 1-2 1v2h-3-4v3 2h0l-2 5-1 4c-1 1-1 2-2 2h-1l-1-2h-1c0-2 1-6 1-8-2-2-2-2-5-3h-1c-1 0 0 0-1-1l-3-1v-3h-1l-1 1c1-4 0-10-1-14h0c3-2 5-4 7-6 0-1 0-2-1-4l-1-1c0-2 2-4 3-5 3-2 4-4 4-7v-5-56-14c0-3 1-7-1-10h0c0-1 0-3-1-5h-2v-1h0c2-2 3-4 2-7-2 0-4 1-6 1 1-4 3-7 5-11 1-5 1-11 1-15l1-2c1 2 0 13 0 15l-1 1c0 1 1 1 1 2 1-1 1 0 1-1v-4-1h1v-12l1 2z" class="e"></path><path d="M279 640v1c1 2 1 3 2 5 0 1 0 1 1 2-1 1-2 1-4 1 0-3 1-6 1-9z" class="C"></path><path d="M272 763c2-1 2-2 4-2v3c-1 1-2 3-3 3-1-1-1-3-1-4z" class="m"></path><path d="M268 765l4-2c0 1 0 3 1 4-2 1-3 1-4 2 0-1 0-2-1-4z" class="l"></path><path d="M279 762c2 0 4 1 5 3l-2 3c-1 1-1 1-2 1-1-1-2-2-2-3 0-2 0-3 1-4z" class="m"></path><path d="M275 639v-12l1 2c0 4 0 10 1 14 0-3 0-8 1-11l1 8c0 3-1 6-1 9l-1 3h-1c-1-4-1-9-1-13z" class="V"></path><path d="M276 764c0 1 0 2-1 3 1 0 3 0 3 1 2 3 5 5 8 6l-2 2h-1v-2h-1v1c-1 1-4 3-4 4-1 1-1 3-2 4v1l-1 1v-1c0-2-1-2-1-3-2 0-4-3-5-4h2l-2-3h-2l-1 1h-1-3 0c3-2 5-4 7-6 1-1 2-1 4-2 1 0 2-2 3-3z"></path><path d="M274 781c-1-2-2-3-2-5-1-2-2-3-1-5h3c0 1 1 2 2 3l-1 2c-1 1-1 1 0 3 0 1 0 0 1 1 0-1 0-1 2-1-1 1-1 3-2 4v1l-1 1v-1c0-2-1-2-1-3z" class="V"></path><path d="M275 767c1 0 3 0 3 1 2 3 5 5 8 6l-2 2h-1v-2h-1v1c-1 1-4 3-4 4-2 0-2 0-2 1-1-1-1 0-1-1-1-2-1-2 0-3l1 1 2-6c0-1-2-2-3-3v-1z" class="F"></path><path d="M278 779c0-1 3-3 4-4v-1h1v2h1l2-2 2 1c1 1 1 0 1 2-1 3 0 7 0 11v2h-3-4v3 2h0c-2 0-2 0-3-1-2-1-3-1-4-2 0-2 0-3 1-4v-3-1-1c1-1 1-3 2-4z" class="R"></path><path d="M287 780c0 3-1 7-1 10h-4c-1-3 0-6 1-9h2c-1 2-2 4-2 7l1-1c0-2 1-4 2-6l1-1z" class="i"></path><path d="M286 774l2 1c1 1 1 0 1 2-1 3 0 7 0 11v2h-3c0-3 1-7 1-10v-4h-4 0 1l2-2z" class="H"></path><path d="M279 785v-2c1-1 2-2 4-2-1 3-2 6-1 9v3 2h0c-2 0-2 0-3-1-2-1-3-1-4-2 0-2 0-3 1-4v-3-1c1 0 2 0 3 1z"></path><path d="M279 785v-2c1-1 2-2 4-2-1 3-2 6-1 9-1 0-1 0-2 1l-2-1c-1 0-1 0-2-1l2-2c1-1 1-1 1-2z" class="C"></path><path d="M262 775h3 1l1-1h2l2 3h-2c1 1 3 4 5 4 0 1 1 1 1 3v1l1-1v1 3c-1 1-1 2-1 4 1 1 2 1 4 2 1 1 1 1 3 1l-2 5-1 4c-1 1-1 2-2 2h-1l-1-2h-1c0-2 1-6 1-8-2-2-2-2-5-3h-1c-1 0 0 0-1-1l-3-1v-3h-1l-1 1c1-4 0-10-1-14z" class="Z"></path><path d="M276 784v1 3c-1 1-1 2-1 4h-3c-1-1-1 0-2-1 3-2 4-4 5-6l1-1z" class="X"></path><path d="M272 792h3c1 1 2 1 4 2 1 1 1 1 3 1l-2 5-1 4c-1 1-1 2-2 2h-1l-1-2c1-3 2-5 1-8 0-2-2-3-4-4zm-10-17h3 1l1-1h2l2 3h-2c1 1 3 4 5 4 0 1 1 1 1 3l-2 1c-1-2-3-4-5-4v2c1 1 1 2 0 4 0 0-1 1-2 1h0c0-1 1-2 1-3v-4h-1c-1 2 0 7-1 7h-1l-1 1c1-4 0-10-1-14z" class="R"></path><path d="M276 667v-3h1v1c1 0 0 2 0 3v13 71h0c-1 1-1 1-1 2 0 2-1 4-2 5h-4c3-2 4-4 4-7v-5-56-14c0-3 1-7-1-10h3z" class="I"></path><path d="M274 747l2-2v6l-1 1h-1v-5z" class="R"></path><path d="M276 667v78l-2 2v-56-14c0-3 1-7-1-10h3z"></path><path d="M285 487c1 0 1 0 2 1-1 0 0 2-1 2v16c0 2 1 5 0 7s-1 4-1 6l1 1c1 2 0 5 0 8v14l-1-6v7 27c0 1-1 2 0 3v1h0c0 4-1 13 0 17v3c-2 6-1 12-1 17v3c-1 1-1 2-2 3 1 2 2 7 4 9 0-1 1-2 1-3l4 7c-3 0-6-1-8-2l-1 1-1-1h0c0 1 0 3-1 4l-1-1v-4l1-1-1-1c-1 0-2 0-3 1v3l-1-2v12h-1v1 4c0 1 0 0-1 1 0-1-1-1-1-2l1-1c0-2 1-13 0-15l-1 2c-2 0-5 2-7 2l-2-2 2-3c3-7 5-13 4-21-1-3-2-7-2-11 0-7 3-13 2-21h0c-1-3-1-8 0-11h0v2c0-1 0-3 1-4l1-1c0-2-1-4 0-6l-1-3c0-1 0-1-1-2 0-1 0-1 1-2 0-3 0-7 1-10 1-5 2-9 4-14l1-5 9-30z" class="a"></path><path d="M273 593h1l1-4c1 2 0 4 0 6l-2 10c0-4-1-9 0-12z" class="P"></path><path d="M285 487c1 0 1 0 2 1-1 0 0 2-1 2l-1 1c-1 3-5 15-4 18l-2 8v5c2 10 0 20 0 29l-1 1c0-6 1-12-1-18v-12h-2l1-5 9-30z" class="C"></path><defs><linearGradient id="K" x1="272.499" y1="616.605" x2="266.002" y2="611.895" xlink:href="#B"><stop offset="0" stop-color="#242520"></stop><stop offset="1" stop-color="#36343a"></stop></linearGradient></defs><path fill="url(#K)" d="M279 551l-6 42c-1 3 0 8 0 12-1 5-1 10-3 14 0 2-3 8-2 9h-1v-2-1c-1 0-2 1-2 1 3-7 5-13 4-21h1v-3h0l8-50 1-1z"></path><path d="M281 509c-1-3 3-15 4-18l1-1v16c0 2 1 5 0 7s-1 4-1 6l1 1c1 2 0 5 0 8v14l-1-6v7 27-1c-1-1-1-2-1-3v-1c-2 2-1 4-2 7v1c-1 3-1 5-1 8l-2 1c0 2 0 4-1 6 0-1 0 0-1-1 0-17 3-33 4-50v-12c0-2-1-3-1-5s1-4 1-6v-3-2h0z" class="b"></path><path d="M279 582c1-3 1-7 2-9v-2c1-1 0-2 0-3 1-5 2-10 2-15l1-16v-11-5c-1-2-2-6-2-8l1-3v-4h1v2c-1 2-1 5-1 7 1 3 2 5 2 8v13 7 27-1c-1-1-1-2-1-3v-1c-2 2-1 4-2 7v1c-1 3-1 5-1 8l-2 1z" class="m"></path><path d="M275 522h2v12c2 6 1 12 1 18l-8 50h0v3h-1c-1-3-2-7-2-11 0-7 3-13 2-21h0c-1-3-1-8 0-11h0v2c0-1 0-3 1-4l1-1c0-2-1-4 0-6l-1-3c0-1 0-1-1-2 0-1 0-1 1-2 0-3 0-7 1-10 1-5 2-9 4-14z" class="Z"></path><path d="M271 553v-4 1c1 1 1 2 1 3 1-1 1-2 1-2h0c-1 6-1 12-1 17v11c0 5-2 9-3 14 0 3 0 6 1 9v3h-1c-1-3-2-7-2-11 0-7 3-13 2-21h0c-1-3-1-8 0-11h0v2c0-1 0-3 1-4l1-1c0-2-1-4 0-6z" class="R"></path><path d="M275 522h2v12c0 6 0 12-2 18v-7c-1 2-1 4-2 6h0s0 1-1 2c0-1 0-2-1-3v-1 4l-1-3c0-1 0-1-1-2 0-1 0-1 1-2 0-3 0-7 1-10 1-5 2-9 4-14z" class="I"></path><path d="M282 573v-1c1-3 0-5 2-7v1c0 1 0 2 1 3v1c0 1-1 2 0 3v1h0c0 4-1 13 0 17v3c-2 6-1 12-1 17v3c-1 1-1 2-2 3 1 2 2 7 4 9 0-1 1-2 1-3l4 7c-3 0-6-1-8-2l-1 1-1-1h0c0 1 0 3-1 4l-1-1v-4l1-1-1-1c-1 0-2 0-3 1v3l-1-2v12h-1v1 4c0 1 0 0-1 1 0-1-1-1-1-2l1-1c0-2 1-13 0-15l-1 2c-2 0-5 2-7 2l-2-2 2-3s1-1 2-1v1 2h1c-1-1 2-7 2-9h1c1-1 2-1 2-2 1-1 1-3 1-4 0 0 1-1 2-1v-12c0-5 1-9 1-13 1 1 1 0 1 1 1-2 1-4 1-6l2-1c0-3 0-5 1-8z" class="O"></path><path d="M270 619h1c1-1 2-1 2-2 1-1 1-3 1-4 0 0 1-1 2-1-1 7-3 11-8 16-1-1 2-7 2-9zm7-18h1c0 3 0 5 3 8l1 8c1 2 2 7 4 9v1c-1-1-3-2-3-3-6-6-6-16-6-23z" class="N"></path><path d="M282 573v-1c1-3 0-5 2-7v1c0 1 0 2 1 3v1c0 1-1 2 0 3v1h0c0 4-1 13 0 17v3c-2 6-1 12-1 17v3c-1 1-1 2-2 3l-1-8c-3-3-3-5-3-8h-1l1-13c1-2 1-4 1-6l2-1c0-3 0-5 1-8z" class="E"></path><path d="M282 573v-1c1-3 0-5 2-7v1c0 1 0 2 1 3v1c0 1-1 2 0 3-2 5-2 11-3 16-1-2 0-6 0-8v-8z" class="G"></path><path d="M281 581c0-3 0-5 1-8v8c0 2-1 6 0 8 0 3-1 5-1 7 0 3 0 6-1 9l1 4c-3-3-3-5-3-8h-1l1-13c1-2 1-4 1-6l2-1z" class="Z"></path><path d="M281 581c0-3 0-5 1-8v8c0 2-1 6 0 8 0 3-1 5-1 7 0 3 0 6-1 9v-3c-2-6 0-14 1-21z" class="j"></path><path d="M714 223c2 1 2 2 4 3l3-1 1-1c2 2 5 3 8 4h1c1 1 2 3 3 5s1 1 2 2 2 5 3 7v-1c1 1 2 2 2 3h0c1 1 2 1 2 2l2 2 1 1h0 0l2-2 1 2c1 1 2 2 2 3-1 1-1 1-1 2h0v3c0 1 0 1 1 2l-1 1c1 1 1 1 2 1l-1 1v2c2 5 3 9 4 14v14c1 1 2 1 2 2v2c-1 1-1 1-1 2h2v2l-1 1 3 3 1 1c1 0 3 1 5 1l2 2c-1 1-1 1-2 1l-1 2 1 2-2 2c1 1 1 1 1 3-1 0-1 1-2 1l-2 2c-1 2 1 2-1 3l-4-2v-1c-3 0-5 3-8 5l-1 3v1l2-1h0 0v1c-2 1-4 3-4 5h0l-4 5h-2c-3-2-5-5-8-8h0c-1 0-1-1-2-1-2-1-3-2-4-3-2-3-4-7-6-10 0-1-2-3-2-4l-6-11h3v1l1 1c1-1 3-2 4-2h3c1-2 0-3 0-6l-2-4v-4s-1-1-2-1l1-1h0v-1-1l-1 1-1-2h0 2c-1-1-1 0-3-1l1-1h1c1-1 1-1 2-1v-1c-1-2 0-4 0-6l-1-1 1-1c1-2 1-4 1-6 1-1 1-2 2-2l-1-2h0c1-1 2-2 2-4h0c2-3 2-7 0-11-1 0-2-9-3-10 0-2-1-3-2-3l-2-2c-1-1-1-2-1-3 0 1 1 2 2 3 0-3-3-6-4-8v-1z" class="h"></path><path d="M746 282l1 1v1c0 2 0 2 1 4l1 1h0l-3 1h0c0-1 0-2-1-3h0v-3c1-1 1-1 1-2h0z" class="V"></path><path d="M744 318h0c1-2 2-3 3-5 3-6 7-14 8-21 1 1 2 1 2 2v2c-1 1-1 1-1 2-1 3-3 6-4 10v1c-1 3-2 5-4 7-1 2-3 4-5 4l-1 1h-1v-1c1-1 2 0 3-2zm0-17h3v-4l3-6h3c1 3-1 7-2 9-1 5-3 11-8 14-1-1-1-1-1-3v-2c1-1 2-3 2-4s0-1 1-1v-2l-1-1z" class="K"></path><path d="M727 302c-1-2-1-2 0-3 1 2 1 3 3 4h2c1-1 2-1 3-1v1c-1 1-2 1-3 1l4 1v3h-1-1l1-2-2 1c2 3 4 10 8 12 1 0 2-1 3-1-1 2-2 1-3 2-2-1-3-2-4-3l-1-2h0c-1 2 1 3 2 5h-1c-1-1-1-3-3-3l-1 1h-1c0-1-1-2-2-3v-2l-4-7-2-4h3z" class="I"></path><path d="M727 302c0 1 0 2-1 4l-2-4h3z" class="B"></path><path d="M746 255l1 1c1 2 2 3 3 4s1 1 2 1l-1 1v2h-1l3 8v1c1 5 0 10 0 15-1 0-1 0-2-1v-6l-1-1c-1 2 0 2 0 3v3 1l-3-3v-1l-1-1-2-7c-1-2-2-3-2-5 0-1 0 0-1-1l-1-1s0-1 1-1c1-3 2-3 4-3-1-2-1-2-3-3l2-3h0l2-3z" class="D"></path><path d="M747 256c1 2 2 3 3 4s1 1 2 1l-1 1v2h-1c-2-2-3-4-4-6l1-2z" class="L"></path><path d="M744 258c2 3 6 11 6 15v1l-1-1c0-4-2-6-4-9-1-2-1-2-3-3l2-3z" class="G"></path><path d="M742 270c0-1 0 0-1-1l-1-1s0-1 1-1c1-3 2-3 4-3 2 3 4 5 4 9v1c-1-1-1-1-1-2l-1-1v-1c0-2-1-2-2-3l-1 1c1 1 2 2 2 3v1c1 1 1 1 1 2s1 2 2 3v3l-1 1c0 1-1 1-1 2l-1-1-2-7c-1-2-2-3-2-5z" class="K"></path><path d="M744 275l2-1c1 2 2 4 2 7 0 1-1 1-1 2l-1-1-2-7z" class="L"></path><path d="M742 261c2 1 2 1 3 3-2 0-3 0-4 3-1 0-1 1-1 1l1 1c1 1 1 0 1 1 0 2 1 3 2 5l2 7h0c0 1 0 1-1 2v3h0l-1 1v4l1 2c-1 0-1-1-2-1h-1c0 1-1 0-2 1h-1l1 4c2 0 3 0 4-1h1 0l-2 3 1 1 1 1v2c-1 0-1 0-1 1s-1 3-2 4v2c0 2 0 2 1 3-1 1 0 1-1 1-4-4-5-7-6-12 0-2-2-4-2-7 0-2-1-4-1-6l1-5h1c-1-5 0-10 0-15 0-2 1-3 1-5l1 1 2-1 1-1 2-3z" class="d"></path><path d="M742 261c2 1 2 1 3 3-2 0-3 0-4 3-1 0-1 1-1 1l1 1c1 1 1 0 1 1-2 1-3 5-3 7v1l-1 1c-1 0-1 1-2 2v-1-2-1c0-2 2-10 3-12l1-1 2-3z" class="L"></path><path d="M736 281c1-1 1-2 2-2v7l1 8 1 4c2 0 3 0 4-1h1 0l-2 3 1 1 1 1v2c-1 0-1 0-1 1s-1 3-2 4c-3-8-6-17-6-25v-3z" class="B"></path><path d="M733 290l1-5h1c1 9 4 17 7 26 0 2 0 2 1 3-1 1 0 1-1 1-4-4-5-7-6-12 0-2-2-4-2-7 0-2-1-4-1-6z" class="I"></path><path d="M742 270c0 2 1 3 2 5l2 7h0c0 1 0 1-1 2v3h0l-1 1v4l1 2c-1 0-1-1-2-1h-1c0 1-1 0-2 1h-1l-1-8v-7l1-1v-1c0-2 1-6 3-7z" class="G"></path><path d="M739 278s1 0 2 1v2c-1 2-1 5 0 7 1 0 2-1 4-1h0l-1 1v4l1 2c-1 0-1-1-2-1h-1c0 1-1 0-2 1h-1l-1-8v-7l1-1z" class="J"></path><path d="M741 291v-1c1-1 2-2 3-2v4l-2 1-1-2z" class="N"></path><path d="M738 286c1 2 2 4 3 5l1 2 2-1 1 2c-1 0-1-1-2-1h-1c0 1-1 0-2 1h-1l-1-8z" class="M"></path><path d="M715 305c1-1 3-2 4-2h3c1-2 0-3 0-6l-2-4v-4s-1-1-2-1l1-1h0v-1-1l-1 1-1-2h0 2c-1-1-1 0-3-1l1-1h1c1-1 1-1 2-1 2 7 2 14 4 21l2 4 4 7v2c1 1 2 2 2 3h1l1-1c2 0 2 2 3 3h1c-1-2-3-3-2-5h0l1 2c1 1 2 2 4 3v1h1l1-1c2 0 4-2 5-4 2-2 3-4 4-7v-1c1-4 3-7 4-10h2v2l-1 1 3 3 1 1c1 0 3 1 5 1l2 2c-1 1-1 1-2 1l-1 2 1 2-2 2c1 1 1 1 1 3-1 0-1 1-2 1l-2 2c-1 2 1 2-1 3l-4-2v-1c-3 0-5 3-8 5l-1 3v1l2-1h0 0v1c-2 1-4 3-4 5h0l-4 5h-2c-3-2-5-5-8-8h0c-1 0-1-1-2-1-2-1-3-2-4-3-2-3-4-7-6-10 0-1-2-3-2-4l-6-11h3v1l1 1z" class="k"></path><path d="M714 304l1 1h1c0 1 0 2 1 3s2 1 2 2l1 1 1-2h2c0-1 1 0 2 0 1 1 1 1 1 2l-2-1-1 2v1h4c1 1 2 1 2 3h-1l-1-1c-2-1-3 1-4 1s-1-1-3-1c1 1 1 2 1 2 1 2 1 3 2 5-1-1-1-1-1-2-2-2-4-5-5-8v-2l-3-6z" class="K"></path><path d="M756 298h2v2l-1 1 3 3 1 1c1 0 3 1 5 1l2 2c-1 1-1 1-2 1l-1 2 1 2-2 2c1 1 1 1 1 3-1 0-1 1-2 1l-2 2c-1 2 1 2-1 3l-4-2v-1c-3 0-5 3-8 5-1 0-2 1-2 1-1 1-2 2-3 2-1 1-1 1-3 1v-2h1v-1h-1c-1-2-1-2-3-3h0l2 3c-1 0 0 0-1 1v1c-3 0-4 2-7 2v-1h2c1 0 2-1 3-1l1-1c0-1-1-2-1-4v-1c-1-1-2-3-3-5l-1 1v-1h1l1-1c2 0 2 2 3 3h1c-1-2-3-3-2-5h0l1 2c1 1 2 2 4 3v1h1l1-1c2 0 4-2 5-4 2-2 3-4 4-7v-1c1-4 3-7 4-10z" class="B"></path><path d="M759 311h6l1 2-2 2h-1c-3 1-8 1-11 1 0-1 1-2 2-3 1-2 2-1 4-2h1z" class="g"></path><path d="M754 313c1-2 2-1 4-2 1 2 2 3 2 4h-3c-1-1-1-2-3-2z" class="H"></path><path d="M759 311h6l1 2-2 2h-1-3c0-1-1-2-2-4h1z" class="P"></path><path d="M746 327c-2 0-3 1-4 2h0-1c2-2 5-4 7-6s4-4 8-5c2 0 5 0 7 1l-2 2c-1 2 1 2-1 3l-4-2v-1c-3 0-5 3-8 5-1 0-2 1-2 1z" class="S"></path><path d="M756 298h2v2l-1 1 3 3 1 1c1 0 3 1 5 1l2 2c-1 1-1 1-2 1l-1 2h-6-1c-2 1-3 0-4 2-1 1-2 2-2 3h-1c0 2 0 2-2 3-1 0-1-1-1-3 2-2 3-4 4-7v-1c1-4 3-7 4-10z" class="X"></path><path d="M761 305c1 0 3 1 5 1l2 2c-1 1-1 1-2 1l-1 2h-6l-1-1c-1 0-1-1-2-2v-1c1-2 3-2 5-2z" class="H"></path><path d="M756 308c4 0 7 0 10 1l-1 2h-6l-1-1c-1 0-1-1-2-2z" class="B"></path><defs><linearGradient id="L" x1="738.352" y1="261.033" x2="711.749" y2="273.681" xlink:href="#B"><stop offset="0" stop-color="#0c0a0d"></stop><stop offset="1" stop-color="#272927"></stop></linearGradient></defs><path fill="url(#L)" d="M714 223c2 1 2 2 4 3l3-1 1-1c2 2 5 3 8 4h1c1 1 2 3 3 5s1 1 2 2 2 5 3 7v-1c1 1 2 2 2 3h0c1 1 2 1 2 2l2 2 1 1h0 0l2-2 1 2c1 1 2 2 2 3-1 1-1 1-1 2h0v3c0 1 0 1 1 2l-1 1c-1-1-2-2-3-4l-1-1-2 3h0l-2 3-2 3-1 1-2 1-1-1c0 2-1 3-1 5 0 5-1 10 0 15h-1l-1 5c0 2 1 4 1 6 0 3 2 5 2 7h-1v-1c-1 0-2 0-3 1h-2c-2-1-2-2-3-4-1 1-1 1 0 3h-3c-2-7-2-14-4-21v-1c-1-2 0-4 0-6l-1-1 1-1c1-2 1-4 1-6 1-1 1-2 2-2l-1-2h0c1-1 2-2 2-4h0c2-3 2-7 0-11-1 0-2-9-3-10 0-2-1-3-2-3l-2-2c-1-1-1-2-1-3 0 1 1 2 2 3 0-3-3-6-4-8v-1z"></path><path d="M724 229c3 2 4 3 5 6v8c-1-3-2-6-2-9v-1h-1c-2-2-2-2-2-4z" class="B"></path><path d="M719 227l1-1v1h3l1 2c0 2 0 2 2 4-2 1-1 2-2 3v-2c-1-2-1-2-3-3l-2-4z" class="E"></path><path d="M721 231c2 1 2 1 3 3v2c1 4 2 7 0 11 0-6-2-11-3-16z" class="I"></path><path d="M714 223c2 1 2 2 4 3l1 1 2 4c1 5 3 10 3 16h0c-1 0-2-9-3-10 0-2-1-3-2-3l-2-2c-1-1-1-2-1-3 0 1 1 2 2 3 0-3-3-6-4-8v-1z" class="V"></path><path d="M729 235c1 3 2 5 2 8 0 1 1 3 2 4v3 1c1 2 0 3 0 5h0v1c-2 0-2 0-4 2 0-2 1-5 0-7v-9-8z" class="D"></path><path d="M731 243c0 1 1 3 2 4v3 1l-2 2h0v-10z" class="j"></path><path d="M733 251c1 2 0 3 0 5h0l-2 1-1-1 1-3 2-2z" class="X"></path><path d="M727 264s1 1 1 2c-2 7-3 14-2 22 0 2 1 4 1 6v3c-2-6-4-14-4-21 0-4 2-8 4-12z" class="J"></path><path d="M722 224c2 2 5 3 8 4h1c1 1 2 3 3 5s1 1 2 2 2 5 3 7v-1c1 1 2 2 2 3l-2-1-1 2-5 5v-3c-1-1-2-3-2-4 0-3-1-5-2-8s-2-4-5-6l-1-2h-3v-1l-1 1-1-1 3-1 1-1z" class="i"></path><path d="M738 245l1-2 2 1h0c1 1 2 1 2 2l2 2 1 1h0 0l2-2 1 2c1 1 2 2 2 3-1 1-1 1-1 2h0v3c0 1 0 1 1 2l-1 1c-1-1-2-2-3-4l-1-1-2 3h0l-2 3-2 3-1 1-2 1-1-1c0 2-1 3-1 5l-2-2 1-1c0-1-1-2-2-3v-2h-2l-2-1 1-2c2-2 2-2 4-2v-1h0c0-2 1-3 0-5v-1l5-5z" class="X"></path><path d="M740 247h2c3 3 6 6 8 10 0 1 0 1 1 2l-1 1c-1-1-2-2-3-4l-1-1-4-4-2 2-1-3 1-3z" class="N"></path><path d="M738 245l1 2h1l-1 3 1 3-3 3-1 1h-2l-1 1v-1-1h0c0-2 1-3 0-5v-1l5-5z" class="O"></path><path d="M734 256v-4l2-1 1 1-3 4z" class="C"></path><path d="M737 252l2-2 1 3-3 3-1 1h-2l-1 1v-1-1h0 1l3-4z" class="M"></path><path d="M740 253l2-2 4 4-2 3h0l-2 3-2 3-1 1-2 1-1-1c0 2-1 3-1 5l-2-2 1-1c0-1-1-2-2-3v-2h-2l-2-1 1-2c2-2 2-2 4-2v1l1-1h2l1-1 3-3z" class="n"></path><path d="M739 262v-2c1 1 2 1 3 1l-2 3c0-1-1-2-1-2z" class="G"></path><path d="M739 262s1 1 1 2l-1 1-2 1-1-1 1-2c1 0 1-1 2-1z" class="N"></path><path d="M734 257l3 1 2 1c-1 1-4 1-6 1l-1-1 1-1 1-1z" class="o"></path><path d="M732 262l5 1-1 2c0 2-1 3-1 5l-2-2 1-1c0-1-1-2-2-3v-2z" class="D"></path><path d="M740 253l2-2 4 4-2 3-2-2h0c-1 2-3 2-5 2l-3-1h2l1-1 3-3z" class="C"></path><path d="M727 264l1-3 2 1h2v2c1 1 2 2 2 3l-1 1 2 2c0 5-1 10 0 15h-1l-1 5c0 2 1 4 1 6 0 3 2 5 2 7h-1v-1c-1 0-2 0-3 1h-2c-2-1-2-2-3-4v-2-3c0-2-1-4-1-6-1-8 0-15 2-22 0-1-1-2-1-2z" class="B"></path><path d="M727 264l1-3 2 1-2 4c0-1-1-2-1-2z" class="G"></path><path d="M732 264c1 1 2 2 2 3l-1 1-2-1-1 1v-1c1-2 1-2 2-3z" class="e"></path><path d="M731 267l2 1 2 2c0 5-1 10 0 15h-1l-1 5c0-3 1-7-1-10v-2c-1-1-2-1-3-2v-1-1c2-1 0-1-1-2v-2l2-2c1 1 2 2 3 2h0l-2-2v-1z" class="Q"></path><path d="M729 276c1 1 2 1 3 2v2c2 3 1 7 1 10 0 2 1 4 1 6 0 3 2 5 2 7h-1v-1c-1 0-2 0-3 1h-2c-2-1-2-2-3-4v-2-3l3 1c1-1 2-1 3-2l-1-1c-2 0-3 0-5-2 0-2 0-2 1-4 1 1 2 2 3 2h1l-1-1-4-2c0-2 0-2 1-4h0 0v-2h0c1-1 1-2 1-3z" class="g"></path><path d="M729 276c1 1 2 1 3 2v2l-1 3v1 1c-1 0-1 0-1-1h-2v-3h0v-2h0c1-1 1-2 1-3z" class="R"></path><path d="M625 101l1 1h0c-1 4-1 7 0 11 0 1 0 2 1 3h1v1c1 0 2 0 2 1 2 3 6 10 8 11 1-1 0 1 1-1v-1l-4-6c4 4 7 9 11 13v-1h1c1 1 1 1 2 1 3 2 5 6 8 7 2 0 4 2 6 3 3 1 5 2 7 4 0 1 0 0 1 1 1 0 1 1 2 2v-2l3 3 2-2v-2l-1-1c4 0 9-1 13 0h2c2 0 3 0 5-1h4c1 1 2 1 3 1 0-1 0-1 1-2 1 1 2 3 3 3v-1l1-1h1c0-1 0-1 1-2v1h0 1c1 1 1 1 2 1h4 10l1 1h1v10c-1 4 0 9 0 13-1 9-2 18-1 27v1l1 22c0 3 0 5 1 8h-1c-3-1-6-2-8-4l-1 1-3 1c-2-1-2-2-4-3v1c1 2 4 5 4 8-1-1-2-2-2-3 0 1 0 2 1 3l2 2c1 0 2 1 2 3 1 1 2 10 3 10 2 4 2 8 0 11h0c-3-1-10 0-13-1-3 0-6 0-9-2-7-3-11-10-17-15-3-3-6-4-10-6 2 0 3 1 5 0-1-2-1-3-2-5 0-1-1-2-1-2 0-2 0-4 1-5v-1l-2-3v-1h0c3-2 6-1 9-2h1 1v-1c1 0 1 0 2-1 1 0 2 0 3-1v1l-1 1h0 4v-1h0l-3-7-5-11-12-20v1l-16-21c-3-3-6-5-10-8l-7-5c-7-4-12-10-15-17-2-3-3-7-4-11v-3l1-3 1-7z" class="c"></path><path d="M646 134v-1h1c1 1 1 1 2 1 3 2 5 6 8 7 2 0 4 2 6 3 0 1 1 2 2 3 1 3 5 5 7 9h0c0 3 3 6 5 9 2 2 4 5 6 7s4 5 6 7c1 1 2 2 2 4h0c-2-2-3-4-4-6-3-2-5-4-8-7-6-8-12-17-19-24-4-4-9-8-14-12z" class="T"></path><path d="M680 172c4 4 8 9 11 15 4 5 8 12 10 19 0 1 1 3 2 4v1c0 1 1 1 1 2-1 0-2-1-4-2h0c-1 1-2 1-3 1l-1-1-1 2-3-7-5-11c0-1 0-1-1-2-1-2-1-5-3-7-1-3-4-6-4-9v-1l1-2v-2z" class="a"></path><path d="M698 208c2-2-1-4 0-6v1c1 1 1 2 2 3h1c0 1 1 3 2 4v1h-1c0-1-1-1-2-1s-2-1-2-2z" class="d"></path><path d="M689 188l3 3 2 4c1 2 2 5 2 6v1c-2-3-3-6-5-8l-3-6h1z" class="N"></path><path d="M680 174c3 4 7 10 9 14h-1c-1-1-3-2-4-3-2-2-3-5-5-8v-1l1-2z" class="G"></path><path d="M687 195c0-1 0-1-1-2-1-2-1-5-3-7-1-3-4-6-4-9 2 3 3 6 5 8 1 1 3 2 4 3l3 6v1c0 1 1 2 1 3h-1c1 3 3 6 4 9h1c0-1-1-2 0-3l2 4c0 1 1 2 2 2s2 0 2 1h1c0 1 1 1 1 2-1 0-2-1-4-2h0c-1 1-2 1-3 1l-1-1-1 2-3-7-5-11z" class="H"></path><path d="M684 185c1 1 3 2 4 3l3 6v1c0 1 1 2 1 3h-1l-1-1c-1-4-4-8-6-12z" class="Z"></path><path d="M625 101l1 1h0c-1 4-1 7 0 11 0 1 0 2 1 3h1v1c1 0 2 0 2 1 2 3 6 10 8 11 1-1 0 1 1-1v-1c6 7 13 13 19 19 8 8 15 17 22 26v2l-1 2v1c0 3 3 6 4 9 2 2 2 5 3 7 1 1 1 1 1 2l-12-20v1l-16-21c-3-3-6-5-10-8l-7-5c-7-4-12-10-15-17-2-3-3-7-4-11v-3l1-3 1-7z" class="N"></path><path d="M625 101l1 1h0c-1 4-1 7 0 11 0 1 0 2 1 3h1v1l2 4c0 3 2 5 4 8 1 2 4 5 6 7 3 2 6 3 7 7 8 6 14 13 20 21 3 4 6 7 8 11v1l-16-21c-3-3-6-5-10-8l-7-5c-7-4-12-10-15-17-2-3-3-7-4-11v-3l1-3 1-7z" class="k"></path><path d="M625 101l1 1h0c-1 4-1 7 0 11 0 1 0 2 1 3h1v1l2 4c0 3 2 5 4 8 1 2 4 5 6 7 3 2 6 3 7 7l-7-5c-7-5-12-13-15-21 0-3-1-6-1-9l1-7z" class="V"></path><path d="M663 144c3 1 5 2 7 4 0 1 0 0 1 1 1 0 1 1 2 2v-2l3 3 2 2 21 24 15 26c1 2 5 8 4 10v-1l-2 1-2-2h-2v2c-1 0-3 0-3-1 0 0 0-1-1-1v1c-1 0-2-2-2-3v-1c-4-8-8-15-12-23-2-3-3-6-5-8-2-3-4-5-7-8-3-4-7-9-10-14-2-4-6-6-7-9-1-1-2-2-2-3z" class="P"></path><path d="M686 166c2 1 2 2 3 3l2 2 3 4s1 1 1 2l2 2c2 3 4 5 5 8l3 5 1 1s0 1 1 2c2 2 2 5 4 7l3 8-14-24h-1l4 7v1l-2-2c2 4 5 8 5 12-1-1-12-23-14-27-3-4-7-7-9-11h2 1 0z" class="J"></path><path d="M663 144c3 1 5 2 7 4 0 1 0 0 1 1 1 0 1 1 2 2v-2l3 3 2 2 21 24 15 26c1 2 5 8 4 10v-1c-1-1-2-2-2-3-2-3-3-6-5-8s-2-5-4-7c-1-1-1-2-1-2l-1-1-3-5c-1-3-3-5-5-8l-2-2c0-1-1-2-1-2l-3-4-2-2c-1-1-1-2-3-3h0-1-2c-1 0-6-6-7-8 0 3 4 7 6 9 2 5 7 7 9 12l5 8c3 6 6 11 9 18 1 1 1 2 1 4-4-8-8-15-12-23-2-3-3-6-5-8-2-3-4-5-7-8-3-4-7-9-10-14-2-4-6-6-7-9-1-1-2-2-2-3z" class="Z"></path><path d="M676 158h0c0-1-1-2-1-3h1c4 3 7 7 10 11h0-1-2c-1 0-6-6-7-8z" class="N"></path><path d="M710 146c0-1 0-1 1-2v1h0 1c1 1 1 1 2 1h4 10l1 1h1v10c-1 4 0 9 0 13-1 9-2 18-1 27v1l-1-1c0-1 0-1-1-1v-1c-2 1-3 1-4 0h-2 0c-1 1-2 2-3 2l3 2-1 1h-5c0 1 0 3-1 4l-15-26-21-24-2-2 2-2v-2l-1-1c4 0 9-1 13 0h2c2 0 3 0 5-1h4c1 1 2 1 3 1 0-1 0-1 1-2 1 1 2 3 3 3v-1l1-1h1z" class="D"></path><path d="M724 191c1 0 2 0 3 1v2h-3l1-1-1-2z" class="B"></path><path d="M715 200c0-1-1-1-1-2v-1h1 3l3 2-1 1h-5z" class="V"></path><path d="M704 181c1 1 2 1 3 1l-1 2c1 0 2 0 3 1 0 1-1 2-1 3h0v1c-1-1-1-1-1-2-1-2-3-3-3-5v-1z" class="O"></path><path d="M708 188h2l1 1h4v1c0 1 0 0 1 1h-6c-1-1-2-1-2-2v-1z" class="J"></path><path d="M697 164l1 3c0 2-1 4 0 5h2v1 1c1 1 1 1 2 1h3v1c-2 0-3 0-5 1-1-1-2-3-3-4s-1-2-2-3v-1c0-2 1-4 2-5z" class="X"></path><path d="M710 188h6c3 0 8-1 10 1v1l-2 1h-8c-1-1-1 0-1-1v-1h-4l-1-1z" class="a"></path><path d="M713 166h7v3h2v1h-1c-1 0-2 1-4 1h0c-2-1-4-1-6-1l2 1v2h-3v-4l-1-2h1 1c1-1 1-1 2-1z" class="b"></path><path d="M711 167h7l-1 1h-1c-2 0-2 0-4 1h-1v1l2 1v2h-3v-4l-1-2h1 1z" class="I"></path><path d="M709 157h5c2-1 4 0 7 0v1h-2c0 2 0 2 1 3h0c2-1 5-1 7-1l1 1c-1 1-6 0-6 1h1 5v1h-1c-2 0-5-1-6 0h-1c-3 1-5 0-8 0l1 3c-1 0-1 0-2 1h-1l-1-10z" class="R"></path><path d="M719 158h0c0 2 0 2 1 3h0c2-1 5-1 7-1l1 1c-1 1-6 0-6 1h-2c-3 0-5 0-8-1v-1c2-1 3-1 4-1s2 0 3-1z" class="g"></path><path d="M709 157h5c2-1 4 0 7 0v1h-2 0c-2 0-2 0-3 1h-6c0 1 0 3 1 5l1-1 1 3c-1 0-1 0-2 1h-1l-1-10z" class="C"></path><path d="M710 146c0-1 0-1 1-2v1h0 1c1 1 1 1 2 1h4 10l1 1c-2 1-2 2-2 3l-1 1c1 0 1 1 1 1h2v4h-1l-1 1v1l1 1-1 1c-2 0-5 0-7 1h0c-1-1-1-1-1-3h2v-1c-3 0-5-1-7 0h-5v-6c1-2 1-4 1-5z" class="O"></path><path d="M719 156h-6l-2-2c1-1 1-2 1-3s0-1-1-2v-1h1 1v-1l1-1 1 2c0 2-1 3-2 5h1 0c2 1 2 1 3 1l1-1c0 1 1 2 1 3z" class="L"></path><path d="M715 148c1 2 2 3 4 3 1 1 1 1 1 2h1c1 0 0 0 1 1h3l1 1c-2 1-4 1-7 1h0c0-1-1-2-1-3l-1 1c-1 0-1 0-3-1h0-1c1-2 2-3 2-5z" class="C"></path><path d="M704 181l1-1 1-1v-1c-2 0-2 0-4-1 1-1 3-1 4-1 3 0 12 1 14 0h5l1 1h-1v2 1s1 0 2 1l-1 1-1 1h-3c0 1 0 1 1 1h2 0c0 1-1 1-1 1 0 1 1 1 1 1 1 1 0 1 1 2v1c-2-2-7-1-10-1h-6-2 0c0-1 1-2 1-3-1-1-2-1-3-1l1-2c-1 0-2 0-3-1z" class="L"></path><path d="M707 182h4l1 1h-1l2 1h0c1 0 2-1 3 0v2h-2c-1-1-3 0-4 0l-1 1c0 1 0 0-1 1 0-1 1-2 1-3-1-1-2-1-3-1l1-2z" class="E"></path><path d="M695 169v-3c1-3 1-7 1-11 0-2 0-5 1-6 0 1 1 1 1 2 1-1 2-1 3-1 2 0 5-1 8 0h0v1 6l1 10h-1l1 2c-1 2-1 3-1 4-2 0-3 0-5-1v-2l-1-1c0 1 0 1-1 2 0-2 0-3-1-4l-1 1h-1v-2c-1-2-1-4-1-6h-1v4c-1 1-2 3-2 5z" class="M"></path><path d="M701 150c2 0 5-1 8 0h0v1 6l1 10h-1v1c-1-1-2-1-2-2-2-1-2-2-2-3-2 0-1 2-2 3l-1-10c-1-2-1-3-1-4v-2z" class="m"></path><path d="M709 150v1 6l1 10h-1v1c-1-1-2-1-2-2-2-1-2-2-2-3-2 0-1 2-2 3l-1-10c-1-2-1-3-1-4 2 2 2 6 4 8 1 1 1 3 1 4 1 0 1 1 2 1 0-2 0-3 1-4v-10-1z" class="G"></path><path d="M690 147h2c2 0 3 0 5-1h4c1 1 2 1 3 1 0-1 0-1 1-2 1 1 2 3 3 3v-1l1-1h1c0 1 0 3-1 5v-1h0c-3-1-6 0-8 0-1 0-2 0-3 1 0-1-1-1-1-2-1 1-1 4-1 6 0 4 0 8-1 11v3 1c1 1 1 2 2 3s2 3 3 4l-1 1-21-24-2-2 2-2v-2l-1-1c4 0 9-1 13 0z" class="C"></path><path d="M683 156h-2l1-3h2v2l-1 1z" class="X"></path><path d="M684 153l4 1c-1 1-2 1-2 2h-3l1-1v-2z" class="O"></path><path d="M688 154c2 0 3 1 4 1 1-1 2-1 2 0v1h-2c-2 1-5 0-6 0 0-1 1-1 2-2z" class="B"></path><path d="M677 147c4 0 9-1 13 0l1 1 3 1c0 1 0 3-1 4h-3c-3-1-6-1-9-1l-3 2-2-2 2-2v-2l-1-1z" class="X"></path><path d="M677 147c4 0 9-1 13 0l1 1c-4 0-7 1-10 0-1-1-1-1-2-1l-1 1-1-1z" class="E"></path><defs><linearGradient id="M" x1="699.137" y1="252.416" x2="716.792" y2="230.499" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#M)" d="M718 197c1 0 2-1 3-2h0 2c1 1 2 1 4 0v1c1 0 1 0 1 1l1 1 1 22c0 3 0 5 1 8h-1c-3-1-6-2-8-4l-1 1-3 1c-2-1-2-2-4-3v1c1 2 4 5 4 8-1-1-2-2-2-3 0 1 0 2 1 3l2 2c1 0 2 1 2 3 1 1 2 10 3 10 2 4 2 8 0 11h0c-3-1-10 0-13-1-3 0-6 0-9-2-7-3-11-10-17-15-3-3-6-4-10-6 2 0 3 1 5 0-1-2-1-3-2-5 0-1-1-2-1-2 0-2 0-4 1-5v-1l-2-3v-1h0c3-2 6-1 9-2h1 1v-1c1 0 1 0 2-1 1 0 2 0 3-1v1l-1 1h0 4v-1h0l1-2 1 1c1 0 2 0 3-1h0c2 1 3 2 4 2 0-1-1-1-1-2v-1h1l1 1c0-1 0-1 1-1 0 1 1 3 2 3v-1c1 0 1 1 1 1 0 1 2 1 3 1v-2h2l2 2 2-1v1c1-2-3-8-4-10 1-1 1-3 1-4h5l1-1-3-2z"></path><path d="M707 222l-1-3v-1l2 1c0 4 1 7 3 10 0 2 1 5 2 6 1 0 1 1 1 1l1 1v3c-1 2 1 8 0 9-2-8-4-19-8-27z" class="M"></path><path d="M708 219l2 2v-1c-1-1-1 0 0-1h0c2 3 5 7 6 10 0 1 0 2 1 3l2 2c0 1 0 1 1 2v2c0 1 1 1 1 2l1 4c0 2 1 3 1 4v2c-1-1-2-1-2-2l-1 1 1 2c-1-1-2-2-2-3v-2l-1 1c0 1 0 2 1 4h0-1c-1 0-1 1-1 2h-1c0-1-1-2-1-4 1-1-1-7 0-9v-3l-1-1s0-1-1-1c-1-1-2-4-2-6-2-3-3-6-3-10z" class="W"></path><path d="M708 219l2 2v-1c-1-1-1 0 0-1h0c2 3 5 7 6 10 0 1 0 2 1 3l2 2c0 1 0 1 1 2l-1 1c0-1-2-2-2-3l-1 1c1 1 1 2 1 3l-1 1c0-2-1-4-2-6h-1c0-1-1-1-1-2s0-1-1-2h0c-2-3-3-6-3-10z" class="F"></path><path d="M703 229v-2h1c0-1 0-2 1-3 3 8 5 15 7 23 0 2 1 4 0 6v1h-2-2c-2 0-1 0-2-1-2-1-3-1-4-2-3-1-5-4-7-6-1-2-2-2-3-4l-2-2c-1-1-2-1-3-2h1v1l9-3 2-1c0-1 1-2 1-2 1-2 1-2 2-3h1z" class="d"></path><path d="M707 242c1-1 1-2 1-3l-1-2h1 0c0 1 0 2 1 3 1 3 1 5 2 8 1-1 1 0 1-1 0 2 1 4 0 6h-1c0-1 0-1-1-2s-2-5-2-7l-1-2z" class="P"></path><path d="M702 229h1c2 4 3 9 4 13l1 2h-2 0c-1-2-1-3-1-4s-1-1-1-1c-1-1-1-3-1-4s-1-2-2-3v1l-1 2-1-1c0-1 1-2 1-2 1-2 1-2 2-3z" class="S"></path><path d="M702 245v-5h-1c1-2 1-3 2-5 0 1 0 3 1 4 0 0 1 0 1 1s0 2 1 4h0 2c0 2 1 6 2 7s1 1 1 2h1v1h-2-2c-2 0-1 0-2-1-2-1-3-1-4-2v-6z" class="P"></path><path d="M706 244h2c0 2 1 6 2 7s1 1 1 2h1v1h-2-2c-2 0-1 0-2-1v-2c1 0 1 0 2 1-1-3-1-6-2-8z" class="C"></path><path d="M699 234l1 1 1-2v-1c1 1 2 2 2 3-1 2-1 3-2 5h1v5 6c-3-1-5-4-7-6-1-2-2-2-3-4l-2-2c-1-1-2-1-3-2h1v1l9-3 2-1z" class="S"></path><path d="M699 234l1 1 1-2v-1c1 1 2 2 2 3-1 2-1 3-2 5h1v5c-1 0-1 1-1 1 0 1 0 1-1 1l-1-7h-1v4h0l-1-1c0-2 0-3 1-4v-1h0l-1-1c0 1-1 2-1 3h0-1c0-1 1-2 2-4v-1l2-1z" class="g"></path><path d="M691 220l1-2h2l-1 1 2 2 3 1c1-1 0-1 1-1v-1h2c1 0 1 0 2 1 0 1 1 2 2 3-1 1-1 2-1 3h-1v2h-1c-1 1-1 1-2 3 0 0-1 1-1 2l-2 1-9 3v-1h-1-1v-1h-1l-1-1v1c-1 1-1 0-3 0l-1-2c-1-2-1-3-2-5 0-1-1-2-1-2 0-2 0-4 1-5v-1c1 0 2 1 4 0v-1-1l5-1 2 1 2 1z" class="E"></path><path d="M691 220l1-2h2l-1 1 2 2 1 2-2 4c-1-2-3-4-3-7z" class="F"></path><path d="M694 227l2-4 4 9s-1 1-1 2l-2 1-9 3v-1c2-1 5-2 7-4l1-1c0-1-1-3-2-5z" class="M"></path><path d="M699 220h2c1 0 1 0 2 1 0 1 1 2 2 3-1 1-1 2-1 3h-1v2h-1c-1 1-1 1-2 3l-4-9-1-2 3 1c1-1 0-1 1-1v-1z" class="b"></path><path d="M699 220h2c1 0 1 0 2 1 0 1 1 2 2 3-1 1-1 2-1 3h-1v2h-1c0-2-1-5-3-6v-3z" class="T"></path><path d="M687 218l2 1c0 3 1 6 2 9 2 1 3 3 4 5-2 2-5 3-7 4h-1-1v-1c0-1 0-1 1-1 0-1 1-2 1-3l-6-12v-1l5-1z" class="X"></path><path d="M691 228c2 1 3 3 4 5-2 2-5 3-7 4h-1-1v-1c0-1 0-1 1-1 0-1 1-2 1-3l2 2c1-2 1-4 1-6z" class="O"></path><path d="M682 220l6 12c0 1-1 2-1 3-1 0-1 0-1 1h-1l-1-1v1c-1 1-1 0-3 0l-1-2c-1-2-1-3-2-5 0-1-1-2-1-2 0-2 0-4 1-5v-1c1 0 2 1 4 0v-1z" class="c"></path><path d="M680 234c-1-2-1-3-2-5 0-1-1-2-1-2 0-2 0-4 1-5 2 4 5 8 6 13v1c-1 1-1 0-3 0l-1-2z" class="e"></path><path d="M718 197c1 0 2-1 3-2h0 2c1 1 2 1 4 0v1c1 0 1 0 1 1l1 1 1 22c0 3 0 5 1 8h-1c-3-1-6-2-8-4l-1 1-3 1c-2-1-2-2-4-3v1c1 2 4 5 4 8-1-1-2-2-2-3-1-3-4-7-6-10h0c-1 1-1 0 0 1v1l-2-2-2-1v1l1 3h-1c-1-1-2-1-3-1-1-1-1-1-2-1h-2v1c-1 0 0 0-1 1l-3-1-2-2 1-1h-2l-1 2-2-1-2-1-5 1v1 1c-2 1-3 0-4 0l-2-3v-1h0c3-2 6-1 9-2h1 1v-1c1 0 1 0 2-1 1 0 2 0 3-1v1l-1 1h0 4v-1h0l1-2 1 1c1 0 2 0 3-1h0c2 1 3 2 4 2 0-1-1-1-1-2v-1h1l1 1c0-1 0-1 1-1 0 1 1 3 2 3v-1c1 0 1 1 1 1 0 1 2 1 3 1v-2h2l2 2 2-1v1c1-2-3-8-4-10 1-1 1-3 1-4h5l1-1-3-2z" class="B"></path><path d="M718 197c1 0 2-1 3-2h0 2l-2 2h1l3 1c1 1 1 1 0 2h-5l1-1-3-2z" class="d"></path><path d="M714 223c-1 0-3-3-3-4 2 0 6-1 8 0 0 1 1 2 1 3v1l2 1-1 1-3 1c-2-1-2-2-4-3z" class="Z"></path><path d="M715 220h3v4c-1 0-2-1-3-2v-2z" class="G"></path><path d="M722 219h-1v-3s0-1 1-1 2 0 3-1l1 1c-1 0-2 1-3 1v1h2c1-1 1-1 3-1 1 2 1 3 2 4 0 3 0 5 1 8h-1c-3-1-6-2-8-4l-2-1v-1l1-1c0-1 1-1 1-2z" class="I"></path><path d="M722 219l2-1h2v1h-1v1c1 1 2 2 2 3v1h-1c-1-1-2-1-4-1v1l-2-1v-1l1-1c0-1 1-1 1-2z" class="Q"></path><path d="M695 213l1-2 1 1c1 0 2 0 3-1h0c2 1 3 2 4 2l1 2h14v1c-2 1-6 1-9 1-5 0-11 1-16 1h-2l-1 2-2-1-2-1-5 1v1 1c-2 1-3 0-4 0l-2-3v-1h0c3-2 6-1 9-2h1 1v-1c1 0 1 0 2-1 1 0 2 0 3-1v1l-1 1h0 4v-1h0z" class="G"></path><path d="M676 218h1 2 0 8l-5 1v1 1c-2 1-3 0-4 0l-2-3z" class="b"></path><path d="M695 213l1-2 1 1c1 0 2 0 3-1h0c2 1 3 2 4 2l1 2h-18v-1c1 0 1 0 2-1 1 0 2 0 3-1v1l-1 1h0 4v-1h0z" class="Q"></path><path d="M780 325h2c3 6 5 11 7 17l2 7c12 2 24 3 36 7 3 1 7 2 10 4l7 7h-6c-8 1-18 2-25 6-1 2 1 6 2 8 3 9 8 17 16 21-1 1-2 2-3 4l-1-1 2-2h-2l-11-1h-3-30-2l-22 1h-11c-1-1-2-1-3-1-2 1-3 1-4 0-6-1-14 1-20 0h-1c-1-1-1-1-2-1-1-2 0-2 0-4l-3-1h0c2-1 3-1 5-1v-2l1-8-5-1v-1l3-1h-1c-1-1-2-1-2-1h-1c1-3 4-4 6-5l-1-1h-1-1l-1-1 2-1h-3-2l2-2c-1-1-2 0-3-1l-2 1v1c-1 1-1 2-1 3-2 1-4 2-5 4l-1 1c-2 1-4 4-5 6l-1-1v-2l1-1 5-4c1-1 2-1 3-3v-1h1c0-1 1-1 2-2-1-1-1-1-1-2 1-2 1-2 1-3 1-1 0-3 0-4 1-1 1-1 1-2v-3c1-1 1-2 1-4h1c-1 0-3-1-5 0-1 1-1 1-2 1-1 1-3 1-4 2l-1 1h-1c-1-2-1-3 0-4v-1c1-2 2-4 4-4 1 0 1 1 2 1h1v-1c2 0 5 0 6 1h3 1v-1l5-1h-1c0-1-1-1-1-1v-1h4v1h7c1-1 1 0 3-1 1-1 2-1 4-1l-2-2c-1-2-3-3-5-5-1 0-1-1-2-1-1-1-1-1-1-2 1-1 2-1 2-2l1-1h0c3 3 5 6 8 8h2l4-5c1-1 3-3 5-4 1-1 2-1 4 0l5 5 2-1c0 1-1 2-2 3-1-1-1-2-2-3l-1 1c1 1 2 3 4 4l2-1 6-9 3-2 4-1 2-1 3-1z" class="X"></path><path d="M700 354v-1c1-2 2-4 4-4 1 0 1 1 2 1h1v-1c2 0 5 0 6 1h3 1l2 2c2 1 5 1 7 1-6 1-14-2-20 1h-1l-3 3-1 1h-1c-1-2-1-3 0-4z" class="C"></path><path d="M701 358h-1c-1-2-1-3 0-4h2 1c0 1 0 1-1 1l-2 2 1 1z" class="B"></path><path d="M722 348h7 7l-7 1v1l23 1h6l-1 1 1 1h0-21-11c-2 0-5 0-7-1l-2-2v-1l5-1z" class="F"></path><path d="M729 348h7l-7 1v1c-1 0-4 0-5-1h0c2 0 3 0 4-1h1z" class="Z"></path><path d="M752 351h6l-1 1 1 1h0-21l-3-1h0c1-1 3 0 4 0 4 0 12 1 14-1z" class="M"></path><path d="M711 371l-1-1c1-1 1-4 1-6s1-4 2-6h3c1 0 2 0 3-2h0 2v-1c1 1 1 1 2 1h4c2 0 4 0 6 1-1 0-2 1-3 1l-2 2h-1c-2 3-4 4-6 5-1 1-4 3-5 3l-1 1-2 1-2 1z" class="B"></path><path d="M713 368c-1 1 0 1-1 1 0-2 0-2 1-2l-1-1c1-1 3-2 4-3l-1 3-2 2h0z" class="E"></path><path d="M727 356c2 0 4 0 6 1-1 0-2 1-3 1l-2 2h-1c-2 3-4 4-6 5-1 1-4 3-5 3h-3 0l2-2 1-3c1 0 5-4 6-5 2 0 3-1 5-2z" class="D"></path><path d="M715 366c1 0 1 0 1-1 4-2 7-4 11-5-2 3-4 4-6 5-1 1-4 3-5 3h-3 0l2-2z" class="b"></path><defs><linearGradient id="N" x1="781.845" y1="357.833" x2="783.287" y2="349.533" xlink:href="#B"><stop offset="0" stop-color="#c5c4c4"></stop><stop offset="1" stop-color="#eeecee"></stop></linearGradient></defs><path fill="url(#N)" d="M766 347h2c1 0 1 0 2 1h11-7v1h0c15 1 30 3 45 7 5 1 9 2 13 4 3 1 5 3 7 5-10-3-20-6-30-8-17-4-34-3-51-4h0l-1-1 1-1h-6l-23-1v-1l7-1 17 1h8c1-1 3-1 5-2z"></path><path d="M766 347h2c1 0 1 0 2 1h11-7v1h0c-4 1-10 1-13 0 1-1 3-1 5-2z" class="O"></path><path d="M745 335c1-1 3-3 5-4 1-1 2-1 4 0l5 5 2-1c0 1-1 2-2 3-1-1-1-2-2-3l-1 1c1 1 2 3 4 4l2-1v1l-1 2c0 2 1 2 2 3h2v2h1c-2 1-4 1-5 2h-8l-17-1h-7-7-1c0-1-1-1-1-1v-1h4v1h7c1-1 1 0 3-1 1-1 2-1 4-1l-2-2c-1-2-3-3-5-5-1 0-1-1-2-1-1-1-1-1-1-2 1-1 2-1 2-2l1-1h0c3 3 5 6 8 8h2l4-5z" class="K"></path><path d="M760 340l2-1v1l-1 2c0 2 1 2 2 3h2v2c-2-1-4-1-6-2l-3-1 4-4z" class="b"></path><path d="M745 335c1-1 3-3 5-4 1-1 2-1 4 0l5 5 2-1c0 1-1 2-2 3-1-1-1-2-2-3s-2-1-3-2h-1-1v1c1 1 1 0 1 1 1 2 3 4 4 6h0c-1 0-2-1-2-2l-3-3-1 1c1 1 1 2 2 3h-1l-2-2-1-1c-2 0-3 1-4 2l-3 2c0-1 0-1-1-1l4-5z" class="B"></path><path d="M731 332h0c3 3 5 6 8 8h2c1 0 1 0 1 1l3-2c1-1 2-2 4-2l1 1v1c-1 2-5 5-7 6h-5l-2-2c-1-2-3-3-5-5-1 0-1-1-2-1-1-1-1-1-1-2 1-1 2-1 2-2l1-1z" class="O"></path><path d="M742 341l3-2c1-1 2-2 4-2l1 1v1c-3 0-4 2-7 3h-1 0v-1z" class="D"></path><path d="M745 357l21 1c3 0 8-1 11 0 2 1 3 2 5 3s5 2 8 4l-1-1-5-3 1-1c1 1 2 1 4 2l2 1c1 1 1 1 2 1l6 3c1 0 1 1 2 1l1 1c-4 1-7 2-11 3h-4-15c-3-3-6-4-8-6l-3-2c-1-1-2-2-3-2l-2-2h-1c-3-2-7-2-10-3z" class="B"></path><path d="M771 360l1-1c2 1 3 3 6 4l1 1 3 1 5 3c1 1 1 1 2 1h1 0c-1-1-2-2-3-2-3-2-5-4-8-5-2-1-4-2-5-3h3c1 0 2 2 3 2 3 2 5 3 8 4 0 1 0 1 1 1 1 1 1 1 2 1l1 1c1 1 2 1 3 1h1c-1-2-2-1-3-2s-2-1-3-1v-1l-1-1-5-3 1-1c1 1 2 1 4 2l2 1c1 1 1 1 2 1l6 3c1 0 1 1 2 1l1 1c-4 1-7 2-11 3-1-1-3-2-4-3-1 0-2-1-3-1-1-1-4-3-6-4-3 0-5-3-7-4z" class="O"></path><path d="M764 366c0-2-4-4-5-5l1-1c1 0 2 0 3 1h1c2 2 4 4 6 5h1l-7-5v-1c2 0 4 1 6 0h1c2 1 4 4 7 4 2 1 5 3 6 4 1 0 2 1 3 1 1 1 3 2 4 3h-4-15c-3-3-6-4-8-6z" class="C"></path><path d="M780 325h2c3 6 5 11 7 17l-2 2v2 2c-1 1-4 0-6 0h-11c-1-1-1-1-2-1h-2-1v-2h-2c-1-1-2-1-2-3l1-2v-1l6-9 3-2 4-1 2-1 3-1z" class="F"></path><path d="M782 331v1c-1 1-2 1-3 2h-1c-1 1-2 2-4 3v-1l2-2c1-1 3-2 4-4l2 1z" class="G"></path><path d="M782 332l2 5c-2-1-4-1-6 0l-1 1c-1 0-1 1-1 1h1c1 1 1 0 2 1h-4l-2 1v-1l2-2c1-1 3-2 4-4 1-1 2-1 3-2zm-2-7l2 6-2-1c-1-1-1 0-2 0-1 1-3 4-6 5 1-1 2-1 2-3l-2 1v-2l1-1c1-1 1-2 2-3l2-1 3-1z" class="N"></path><path d="M784 337c1 2 3 4 3 6h-1s-1-1-2-1h0c-2-1-4-1-5-2s-1 0-2-1h-1s0-1 1-1l1-1c2-1 4-1 6 0z" class="J"></path><path d="M780 325h2c3 6 5 11 7 17l-2 2v-1c0-2-2-4-3-6l-2-5v-1l-2-6z" class="I"></path><path d="M768 330l3-2c-1 4-5 6-5 10h1c1-1 2-2 4-2-2 2-4 3-5 6l4-3v1l1 1h1c0 1 1 1 1 1v2l-5 2v1h-2-1v-2h-2c-1-1-2-1-2-3l1-2v-1l6-9z" class="S"></path><path d="M768 346v-1c0-1 0-1-1-2 0-1 2-2 3-3l1 1h1c0 1 1 1 1 1v2l-5 2z" class="T"></path><path d="M775 340h4c1 1 3 1 5 2h0c1 0 2 1 2 1h1v1 2 2c-1 1-4 0-6 0h-11c-1-1-1-1-2-1v-1l5-2h2c0-1 1-2 2-3l-2-1z"></path><path d="M775 340h4c1 1 3 1 5 2h0c1 0 2 1 2 1h1v1c-3-2-9-1-12 0 0-1 1-2 2-3l-2-1z" class="a"></path><path d="M770 348c6-3 12-4 17-2v2c-1 1-4 0-6 0h-11z" class="o"></path><path d="M733 357c2-1 5 0 7 0h5c3 1 7 1 10 3h1l2 2c1 0 2 1 3 2l3 2c2 2 5 3 8 6h15c-1 1-4 1-6 1h-23c-6 0-12 0-18 1-4 0-7 1-11 2-1 1-2 2-4 2h-3c-3 0-4 1-6 3h-1c1-3 4-4 6-5l-1-1h-1-1l-1-1 2-1h-3-2l2-2c-1-1-2 0-3-1l2-1 1-1c1 0 4-2 5-3 2-1 4-2 6-5h1l2-2c1 0 2-1 3-1z" class="W"></path><path d="M738 367h1l2 2v1c-2 0-3 0-4-1l1-2z" class="H"></path><path d="M721 374c6-1 13-2 19-2 0 1-1 1-2 1-3 0-6 1-9 2v1c-1 1-2 2-4 2h-3c-3 0-4 1-6 3h-1c1-3 4-4 6-5l-1-1 1-1z" class="C"></path><path d="M721 376l4-1 1 1c-1 1-2 1-4 2-3 0-4 1-6 3h-1c1-3 4-4 6-5z" class="f"></path><path d="M728 367c1 0 1 0 2-1h1c1 0 2 0 3 1h0 1c2-1 1-1 2 0h1l-1 2c-1 2-1 2-3 2h-1c-1-1-1-1-2-1v-1-1l-5 3h-2l-3 3-1 1h-1-1l-1-1 2-1 2-2h1c2-1 4-2 6-4z" class="P"></path><path d="M733 357c2-1 5 0 7 0 1 1 2 1 3 2v2h1l10 9h0l-16-11c2 2 3 4 6 5 2 1 4 3 6 5v1l-9-6-4-4-1 1 9 8c-3 0-6-4-9-7 0-1-1-1-2-2l-1 1 1 1c-1 0-1-1-2-1l-1-2-1 1h-2l2-2c1 0 2-1 3-1z" class="Q"></path><path d="M728 360h2l1-1 1 2h-1l1 1c1 1 1 2 1 4l2 1h-1 0c-1-1-2-1-3-1h-1c-1 1-1 1-2 1-2 2-4 3-6 4h-1l-2 2h-3-2l2-2c-1-1-2 0-3-1l2-1 1-1c1 0 4-2 5-3 2-1 4-2 6-5h1z" class="g"></path><path d="M716 368c1 0 4-2 5-3h2c0 1-2 3-2 4l2-1 1 1h0c1-1 2-1 4-2-2 2-4 3-6 4h-1l-2 2h-3-2l2-2c-1-1-2 0-3-1l2-1 1-1z" class="Y"></path><path d="M715 369v1l1-1h2 0l-1 2h2 0 2l-2 2h-3-2l2-2c-1-1-2 0-3-1l2-1z" class="b"></path><path d="M740 357h5c3 1 7 1 10 3h1l2 2c1 0 2 1 3 2l3 2c2 2 5 3 8 6h-6c-2-2-4-3-6-4-1 0-2-1-3-1 0 0 0 1 1 1 2 1 4 2 5 3h-1c-5-2-11-4-15-9l-4-3c-1-1-2-1-3-2z" class="L"></path><path d="M740 357h5c3 1 7 1 10 3h1l2 2h-1l4 4h0c-3-1-4-4-7-4h-1c-2-1-4-3-7-3 1 1 1 2 1 3h0l-4-3c-1-1-2-1-3-2z" class="f"></path><path d="M802 369c1 0 1-1 2-2-1-1-2-1-2-3l3 3c1 0 7-3 9-3 4-1 9-2 13-1h0l-4 1h0 4 3c2-1 3 0 5 1h1 3v-1l3 2c-1 1-3 1-4 1h0c-8 1-18 2-25 6-1 2 1 6 2 8 3 9 8 17 16 21-1 1-2 2-3 4l-1-1 2-2h-2l-11-1h-3-30-2l-22 1h-11c-1-1-2-1-3-1-2 1-3 1-4 0-6-1-14 1-20 0h-1c-1-1-1-1-2-1-1-2 0-2 0-4l-3-1h0c2-1 3-1 5-1v-2l1-8-5-1v-1l3-1h-1c-1-1-2-1-2-1 2-2 3-3 6-3h3c2 0 3-1 4-2 4-1 7-2 11-2 6-1 12-1 18-1h23c2 0 5 0 6-1h4c4-1 7-2 11-3z" class="R"></path><path d="M798 383l-1-3h0l2-1c2 1 2 3 3 5l-1 1 1 2s-1 0-1-1l-1 1c-1-1-2-4-2-4z" class="S"></path><path d="M802 384l4 7h6 1l2 1-7 1c-1 0-2 0-2 1h-8c2-2 2-2 4-2v-1c0-1-1-3-2-4l1-1c0 1 1 1 1 1l-1-2 1-1z" class="g"></path><path d="M719 382h29-1c-3 1-8 0-11 0 1 1 1 1 2 1l1 1c1 1 1 2 1 3l-1 1-1-1h-3c-1-1-1-1-1-2v-3c-1 0-2 0-3 1 0 1 0 2-1 3v1c-2-2-3-2-5-2-3 2-3 5-5 8l1-8-5-1v-1l3-1z" class="a"></path><path d="M813 391c-2-2-5 1-7-2 0-1 0-3-1-4s-1-1-2-3h1c1 1 1 1 1 2 2-1 2-3 2-4 1 2 2 5 4 7 1-1 0-2-1-3 0-1-1-2-1-4h0c-1-1-1-2-1-3h1c1 1 1 2 1 4h1l3 6 1 1c0 1 1 1 1 2l2 2c0 1 1 2 1 3h1c1 2 3 4 4 5l-1 1c-2-1-3-2-4-4 0 0-1-1-1-2l-3-3h0l-2-1z" class="b"></path><path d="M802 369c1 0 1-1 2-2-1-1-2-1-2-3l3 3c1 0 7-3 9-3 4-1 9-2 13-1h0l-4 1c-8 2-15 6-23 8-5 2-13 2-19 1 2 0 5 0 6-1h4c4-1 7-2 11-3z" class="k"></path><path d="M815 392h0l3 3c0 1 1 2 1 2 1 2 2 3 4 4l1-1 3 3-11-1h-3c0-1-1-1-2-1-2 1-2 0-4 0l-1-1v-1c-1-2-6-3-8-4v-1h8c0-1 1-1 2-1l7-1z" class="K"></path><path d="M816 395h1c0 1 1 2 0 3h-2l-2-2c1 0 1 0 2-1h1z" class="S"></path><path d="M811 401c-1-2-5-5-5-7h1c3 2 8 5 9 8h0-3c0-1-1-1-2-1z" class="V"></path><path d="M798 394h8c0-1 1-1 2-1l-1 1h-1c0 2 4 5 5 7-2 1-2 0-4 0l-1-1v-1c-1-2-6-3-8-4v-1z" class="c"></path><path d="M730 387v-1c1-1 1-2 1-3 1-1 2-1 3-1v3c0 1 0 1 1 2 0 1 0 1-1 2 0 2 0 3 1 5-2 1-2 2-2 3h6c1 1 1 1 1 2s0 2 1 3c-6-1-14 1-20 0h-1c-1-1-1-1-2-1-1-2 0-2 0-4l-3-1h0c2-1 3-1 5-1v-2c2-3 2-6 5-8 2 0 3 0 5 2z" class="J"></path><path d="M720 393c2-3 2-6 5-8 2 0 3 0 5 2 1 2 1 4 1 7l-11 1v-2z" class="Q"></path><path d="M720 402c1-2 0-4 1-5 2 0 6-1 8 0l1 3c0 1 0 1 2 2l1-1v-3-1h6c1 1 1 1 1 2s0 2 1 3c-6-1-14 1-20 0h-1z" class="X"></path><path d="M748 382h4 10 0l1 2h-2l-1 1 1 1c0 1 1 2 1 3-1 0-2 0-2 1h2l1 1 1 3c-1 1-2 1-3 1v1h2l1 1h0c-1 1-2 1-3 2l1 2h-3v-1l-2-3h0c-1 1 0 1 0 2v2c-1 0-1 0-1 1l3 1h-11c-1-1-2-1-3-1-2 1-3 1-4 0s-1-2-1-3 0-1-1-2h-6c0-1 0-2 2-3-1-2-1-3-1-5 1-1 1-1 1-2h3l1 1 1-1c0-1 0-2-1-3l-1-1c-1 0-1 0-2-1 3 0 8 1 11 0h1z" class="G"></path><path d="M734 389h5c1 1 1 3 1 4-2 1-2 1-3 2l-1-1h-1c-1-2-1-3-1-5z" class="M"></path><path d="M752 382h10 0l1 2h-2l-1 1 1 1c0 1 1 2 1 3-1 0-2-1-3-1h-1l-2 2c-3-2-2-3-3-5s-1-2-1-3z" class="N"></path><path d="M772 382c2-1 5-1 7 0 4 1 7-4 9 0v1l1 1v-2h0l2-1h0c1 1 2 1 3 1h2l2 1s1 3 2 4 2 3 2 4v1c-2 0-2 0-4 2v1c2 1 7 2 8 4v1l1 1c2 0 2 1 4 0 1 0 2 0 2 1h-30-2l-22 1-3-1c0-1 0-1 1-1v-2c0-1-1-1 0-2h0l2 3v1h3l-1-2c1-1 2-1 3-2h0l-1-1h-2v-1c1 0 2 0 3-1l-1-3-1-1h-2c0-1 1-1 2-1 0-1-1-2-1-3l-1-1 1-1h2l-1-2h10z" class="j"></path><path d="M784 394c1-1 3-1 4 0 2 0 2 1 3 2h-2c-2 0-3 0-5-1-1 0-1 0-2-1h2zm5-10c1 2 1 5 2 7 2 3 4 6 5 9v1c-4-2-5-8-7-12-1-2-2-3-2-5l1-1 1 1z" class="J"></path><path d="M806 400h0c-3-1-6-1-8-1-3-4-6-8-8-12 3 2 5 5 8 8 2 1 7 2 8 4v1z" class="d"></path><path d="M789 382h0l2-1h0c1 1 2 1 3 1h2l2 1s1 3 2 4 2 3 2 4v1h-2c-1-2-2-2-2-3-1-2-1-2-3-3-1-1-1-1-2-1l-2-2s-1 0-2-1z" class="E"></path><path d="M781 402c-4-4-5-12-7-18l-1-1 1-1 1 1 1-1c2 2 2 5 5 7h0l-2-1v1c1 4 3 8 4 12v1h-2z" class="N"></path><path d="M762 382h10c0 2 1 3 2 5s2 5 2 7c1 1 1 2 1 3v4c-1 0-1 0-3-1l1-1h0-1c0 1 0 1-1 2h-1c0-1 0-1-1-1 0-1-1-2-2-2-1 1-1 0 0 1 0 1 0 1-1 2v1c-2-1-2-3-4-5h0l-1-1h-2v-1c1 0 2 0 3-1l-1-3-1-1h-2c0-1 1-1 2-1 0-1-1-2-1-3l-1-1 1-1h2l-1-2z" class="M"></path><path d="M762 389c0-1-1-2-1-3l-1-1 1-1h2l1 3 1 3 2 5h1l5-2h1l-2 2h3l-2 2h4v4c-1 0-1 0-3-1l1-1h0-1c0 1 0 1-1 2h-1c0-1 0-1-1-1 0-1-1-2-2-2-1 1-1 0 0 1 0 1 0 1-1 2v1c-2-1-2-3-4-5h0l-1-1h-2v-1c1 0 2 0 3-1l-1-3-1-1h-2c0-1 1-1 2-1z" class="m"></path><path d="M394 91l1 1c0 2 0 4 1 6 0-3 0-5-1-8v-1l2 3c1 1 2 1 2 3v1l1 1c0 1 0 1 1 2 0 2 0 5-1 6v11c-1 3 0 4-2 7-1 2-3 6-3 8v1c-1 4-4 7-7 9 2 0 4-2 5-3l2 2c-1 0-3 2-3 3-1 2-3 3-4 4-4 3-7 5-8 9-4 3-8 7-11 10-1 2-2 5-4 7-4 8-10 15-15 23 2 2 2 2 3 2l1-1v2c-1 0-2 0-3 1 1 1 3 2 4 2v1h2c-1 2-2 4-2 5l-3 10h-1l-2 10h1v3l-3 1-2 1c-4 2-8 4-11 6l-8 8c-1 1-3 2-4 3-2 2-4 4-7 5-4 2-10 2-14 2-2 1-6 0-7 2l2 2 2 1-2 1c1 2 1 3 1 5l-1 1h1c1 1 0 1 1 2l-3 1c-1-4-3-8-5-12h0l-3-3c-1-1-2-1-3-1l-2-2-2-4 3-2-2-2 2-7 1-11v2c1-1 1-3 2-4l1-7c0 2 0 3 2 4 2-9 1-19 1-28 1 2 1 5 1 7v1h4v1l-2 2h4c2 0 3-1 5-2 0 0 1 0 1-1l1 1 1-1c0-3 3-7 4-9l2-2 5-9c1 0 3-3 4-4l4-6 7-9 11-11 27-28h-2c5-4 9-7 13-12 2-2 4-8 3-11 0-2-1-3-2-5l3-2c1 1 1 1 3 1l1-1c1 0 1-1 2-2l1-1c1 1 1 2 2 3v-1c1-1 1-2 1-3z" class="F"></path><path d="M384 111c-1 4-4 9-7 12h0c1-2 2-3 3-5l-1-1c1 0 1 0 1-1v-1c1-2 2-3 4-4z" class="Q"></path><path d="M339 166c6-7 11-14 17-20l18-17v-1l1 1c-2 3-4 5-7 7-2 2-7 5-7 8l1-1 1 1c-4 4-8 8-12 13-1 1-5 6-6 6-3 2-6 7-9 10-2 2-3 3-5 4l-1 1c2-5 6-9 9-12z" class="k"></path><path d="M384 95c1 1 1 1 3 1l2 1v-1h1c0 4 0 9-1 13-2 8-6 14-11 20l-9 9-6 6-1-1c12-10 24-21 27-37 0-3 0-6-1-8h-1c0 1 1 1 1 2 1 2 0 6-1 9l-4 9h-1c1-1 2-3 2-5 1-2 1-3 2-4 1-2 1-3 1-6 0-2 0-3-2-4v1l1 1c1 4-1 7-2 10-2 1-3 2-4 4v1c0 1 0 1-1 1l-6 9-1 1v-3-1l-3 2h-2c5-4 9-7 13-12 2-2 4-8 3-11 0-2-1-3-2-5l3-2z" class="O"></path><path d="M369 125l3-2v1 3l-4 4c-3 4-8 7-10 11-1 2-3 2-4 4-3 2-5 6-8 7-2 0-2 0-3 2l-12 12c-2 3-3 6-6 8h0c-3 1-3 3-5 4h0l4-6 7-9 11-11 27-28z" class="M"></path><path d="M325 175h0c3-2 4-5 6-8l12-12c1-2 1-2 3-2l-8 9 1 1v1 2c-3 3-7 7-9 12l-9 13-5 10c-1 4-3 9-6 12l-1 2h0v1h-2v-1-4s-1-1-2-1v-1-1c0-1 1-2 1-3s-1-1-1-2c0-3 3-7 4-9l2-2 5-9c1 0 3-3 4-4h0c2-1 2-3 5-4z" class="d"></path><path d="M307 211l1-1 2-3 1 1c0 2-2 3-1 5l-1 2h0v1h-2v-1-4z" class="H"></path><path d="M316 190l1 1c-1 1-4 5-4 7s0 3-1 4l-1 2-2-2 1-1c2-3 4-7 6-10v-1z" class="i"></path><path d="M315 190l1 1c-2 3-4 7-6 10l-1-1c0-2 1-3 2-5h0v-1l-1 1-1-1 2-2 1 1 3-3z" class="Z"></path><path d="M321 191v-2-3c1 0 1-1 1-1 1-2 5-7 6-7h2l-9 13z" class="T"></path><path d="M309 194l1 1 1-1v1h0c-1 2-2 3-2 5l1 1-1 1 2 2-1 3-2 3-1 1s-1-1-2-1v-1-1c0-1 1-2 1-3s-1-1-1-2c0-3 3-7 4-9z" class="F"></path><path d="M309 202l2 2-1 3-2 3-1 1s-1-1-2-1v-1l4-7z" class="S"></path><path d="M325 175h0c3-2 4-5 6-8l12-12c1-2 1-2 3-2l-8 9c-3 4-21 25-22 28v1l-1-1-3 3-1-1 5-9c1 0 3-3 4-4h0c2-1 2-3 5-4z" class="N"></path><path d="M320 179h0c2-1 2-3 5-4l-3 4c-2 4-5 7-7 11l-3 3-1-1 5-9c1 0 3-3 4-4z" class="V"></path><path d="M380 130v1c3-2 4-4 6-6l2-4h0c0 2-1 4-2 5-2 3-5 7-8 9l-2 2h1c-1 2-1 2-2 3v1l-13 11c-2 1-4 3-6 5l-13 14c-5 5-10 13-13 20-1 0-2 2-2 3-3 3-5 6-6 10-1-1-2-1-3-2l-2 4-1-1v-3-1l5-10 9-13 1-1c2-1 3-2 5-4 3-3 6-8 9-10 1 0 5-5 6-6 4-5 8-9 12-13l6-6 9-9 2 1z" class="N"></path><path d="M345 163c1 0 5-5 6-6v1c-1 1-1 2-1 3 0 2-15 19-18 20 0 1-1 1-1 1 4-7 10-13 14-19z" class="V"></path><path d="M330 178l1-1c2-1 3-2 5-4 3-3 6-8 9-10-4 6-10 12-14 19-5 6-8 13-12 20l-2 4-1-1v-3-1l5-10 9-13z" class="L"></path><path d="M394 91l1 1c0 2 0 4 1 6 0-3 0-5-1-8v-1l2 3c1 1 2 1 2 3v1l1 1c0 1 0 1 1 2 0 2 0 5-1 6 0 1-1 2-1 3-1 2 0 6 0 9l-1-3-1-1-1 5-1-1c-1 4-2 7-4 10-3 8-10 12-16 18-3 2-5 5-8 8h-1l-4 4h0c-3 0-4 3-6 4-4 2-6 6-9 8-1 1-2 2-4 2l13-14c2-2 4-4 6-5l13-11v-1c1-1 1-1 2-3h-1l2-2c3-2 6-6 8-9 1-1 2-3 2-5h0l-2 4c-2 2-3 4-6 6v-1l-2-1c5-6 9-12 11-20 1-4 1-9 1-13h-1v1l-2-1 1-1c1 0 1-1 2-2l1-1c1 1 1 2 2 3v-1c1-1 1-2 1-3z" class="E"></path><path d="M397 92c1 1 2 1 2 3v1l1 1c0 1 0 1 1 2 0 2 0 5-1 6 0 1-1 2-1 3-1 2 0 6 0 9l-1-3-1-1c1-7 0-14 0-21z" class="H"></path><path d="M362 152c2 0 5-4 8-5 3-3 7-6 11-9 6-6 12-13 14-22 1-5 0-10 1-14 0 5 0 10-1 15-1 4-2 7-4 10-3 8-10 12-16 18-3 2-5 5-8 8h-1l-4 4h0c-3 0-4 3-6 4-4 2-6 6-9 8-1 1-2 2-4 2l13-14c2-2 4-4 6-5z" class="F"></path><path d="M387 96l1-1c1 0 1-1 2-2l1-1c1 1 1 2 2 3 3 10 2 21-3 30-3 6-10 13-15 16v-1c1-1 1-1 2-3h-1l2-2c3-2 6-6 8-9 1-1 2-3 2-5h0l-2 4c-2 2-3 4-6 6v-1l-2-1c5-6 9-12 11-20 1-4 1-9 1-13h-1v1l-2-1z" class="M"></path><path d="M380 130c5-5 8-11 10-17 1-2 1-3 2-4 1 2 0 6-1 8-1 3-3 7-5 10v1c-2 3-6 7-9 9h-1l2-2c3-2 6-6 8-9 1-1 2-3 2-5h0l-2 4c-2 2-3 4-6 6v-1z" class="G"></path><path d="M399 117c0-3-1-7 0-9 0-1 1-2 1-3v11c-1 3 0 4-2 7-1 2-3 6-3 8v1c-1 4-4 7-7 9 2 0 4-2 5-3l2 2c-1 0-3 2-3 3-1 2-3 3-4 4-4 3-7 5-8 9-4 3-8 7-11 10-1 2-2 5-4 7l1-4c0-2-1-2-2-3l1-1c0-1-1-2-2-3-2 3-3 4-3 8l-1 1-1-3c-2 0-2 0-4-1 2-4 5-7 8-10h0l4-4h1c3-3 5-6 8-8 6-6 13-10 16-18 2-3 3-6 4-10l1 1 1-5 1 1 1 3z" class="i"></path><path d="M374 148c1-1 3-2 5-3 1-1 3-3 5-4h1l1-1c-1 2-2 4-4 5h-3v1c-2 1-3 2-5 2z" class="T"></path><path d="M366 153c-1 5-9 7-7 13l-1 2c-2 0-2 0-4-1 2-4 5-7 8-10h0l4-4z" class="b"></path><path d="M387 135h1l3-2c-1 2-2 2-2 4l-3 3-1 1h-1c-2 1-4 3-5 4-2 1-4 2-5 3l-2 3-1-1c2-3 7-6 10-9l6-6z" class="h"></path><path d="M397 113l1 1 1 3c-1 2-1 4-1 5-2 3-5 8-5 11-1 1-2 3-4 4 0-2 1-2 2-4l-3 2h-1c4-5 8-11 9-17l1-5z" class="V"></path><defs><linearGradient id="O" x1="393.03" y1="147.004" x2="375.485" y2="144.877" xlink:href="#B"><stop offset="0" stop-color="#86838a"></stop><stop offset="1" stop-color="#a5a7a1"></stop></linearGradient></defs><path fill="url(#O)" d="M399 117c0-3-1-7 0-9 0-1 1-2 1-3v11c-1 3 0 4-2 7-1 2-3 6-3 8v1c-1 4-4 7-7 9 2 0 4-2 5-3l2 2c-1 0-3 2-3 3-1 2-3 3-4 4-4 3-7 5-8 9-4 3-8 7-11 10-1 2-2 5-4 7l1-4c0-2-1-2-2-3l1-1c0-1-1-2-2-3 7-7 15-12 22-18 0-4 7-7 8-11 0-3 3-8 5-11 0-1 0-3 1-5z"></path><path d="M392 143c-1 2-3 3-4 4-4 3-7 5-8 9-4 3-8 7-11 10-1 2-2 5-4 7l1-4c0-2-1-2-2-3l1-1c4-5 9-9 14-13 4-3 8-6 13-9z" class="D"></path><path d="M343 171c2 0 3-1 4-2 3-2 5-6 9-8 2-1 3-4 6-4-3 3-6 6-8 10 2 1 2 1 4 1l1 3 1-1c0-4 1-5 3-8 1 1 2 2 2 3l-1 1c1 1 2 1 2 3l-1 4c-4 8-10 15-15 23 2 2 2 2 3 2l1-1v2c-1 0-2 0-3 1l-6-7v1c1 1 2 3 3 5 0 1 0 2-1 3v1l-4-1 3-3-1-1h-1l-1 1h-1l2-3h-1c-3 3-5 8-8 12l-3 6-2-1v-1c-2 0-2-1-4 1v-1-1l-1-2c-1 2-2 4-4 5h-1v-2c-1 0-1 0-2 1s-2 1-3 1l-1-1c1 0 1-2 1-2l2-5 2-4c1 1 2 1 3 2 1-4 3-7 6-10 0-1 1-3 2-3 3-7 8-15 13-20z" class="a"></path><path d="M334 195l5-8h0c1 1 1 2 3 2v1 1l-1 1h0l-1 1c-1 1-1 2-2 3s-1 0-2 1l-2 2h0c-1 1-2 1-3 3h0l3-7z" class="F"></path><path d="M361 172l-1 3c0 1-1 2-2 3-3 0-5 3-7 4-1 1-3 2-3 4l-6 3v-3c3-3 6-7 10-9 3-2 6-4 9-5z" class="f"></path><path d="M342 189l6-3c0-2 2-3 3-4 2-1 4-4 7-4-2 2-3 3-4 5-1 3-4 5-4 8 0 2-1 3-1 5h1c2 2 2 2 3 2l1-1v2c-1 0-2 0-3 1l-6-7c-1-2-2-3-3-4z" class="L"></path><path d="M364 166c1 1 2 1 2 3l-1 4c-4 8-10 15-15 23h-1c0-2 1-3 1-5 0-3 3-5 4-8 1-2 2-3 4-5 1-1 2-2 2-3l1-3c0-2 2-5 3-6z" class="K"></path><path d="M334 199c-1 2-1 4-2 5s-1 1-1 2h1c0-2 0-2 1-3h1c-1 1-1 3-1 5 2-3 3-7 5-9 0-1 1-2 1-4 1 0 1-1 2-1h4c1 1 2 3 3 5 0 1 0 2-1 3v1l-4-1 3-3-1-1h-1l-1 1h-1l2-3h-1c-3 3-5 8-8 12l-3 6-2-1v-1c-2 0-2-1-4 1v-1-1c2-3 3-6 5-9h0c1-2 2-2 3-3z" class="H"></path><path d="M343 171c2 0 3-1 4-2 3-2 5-6 9-8 2-1 3-4 6-4-3 3-6 6-8 10 2 1 2 1 4 1l1 3c-8 2-15 9-20 16l-5 8-3 7c-2 3-3 6-5 9l-1-2c-1 2-2 4-4 5h-1v-2c-1 0-1 0-2 1s-2 1-3 1l-1-1c1 0 1-2 1-2l2-5 2-4c1 1 2 1 3 2 1-4 3-7 6-10 0-1 1-3 2-3 3-7 8-15 13-20z" class="M"></path><path d="M325 209l7-14h2l-3 7c-2 3-3 6-5 9l-1-2h0z" class="L"></path><path d="M328 194c0 2-4 6-5 9v2c-1 1-1 2-1 3h1v-1c1 0 1-1 2-1h0v3h0c-1 2-2 4-4 5h-1v-2c-1 0-1 0-2 1s-2 1-3 1l-1-1c1 0 1-2 1-2l2-5 2-4c1 1 2 1 3 2 1-4 3-7 6-10z" class="W"></path><path d="M317 206l2-4c1 1 2 1 3 2-1 2-2 4-4 5-1 1-2 2-3 2l2-5z" class="a"></path><defs><linearGradient id="P" x1="339.243" y1="179.299" x2="349.809" y2="183.944" xlink:href="#B"><stop offset="0" stop-color="#444443"></stop><stop offset="1" stop-color="#5b5d5d"></stop></linearGradient></defs><path fill="url(#P)" d="M332 195c3-7 7-14 13-19 3-3 6-5 9-8v-1c2 1 2 1 4 1l1 3c-8 2-15 9-20 16l-5 8h-2z"></path><path d="M345 194v-1l6 7c1 1 3 2 4 2v1h2c-1 2-2 4-2 5l-3 10h-1l-2 10h1v3l-3 1-2 1c-4 2-8 4-11 6l-8 8c-1 1-3 2-4 3-2 2-4 4-7 5-4 2-10 2-14 2-2 1-6 0-7 2l2 2 2 1-2 1c1 2 1 3 1 5l-1 1h1c1 1 0 1 1 2l-3 1c-1-4-3-8-5-12h0l-3-3c-1-1-2-1-3-1l-2-2-2-4 3-2-2-2 2-7 1-11v2c1-1 1-3 2-4l1-7c0 2 0 3 2 4 2-9 1-19 1-28 1 2 1 5 1 7v1h4v1l-2 2h4c2 0 3-1 5-2 0 0 1 0 1-1l1 1 1-1c0 1 1 1 1 2s-1 2-1 3v1 1c1 0 2 1 2 1v4 1h2v-1h0l1-2c3-3 5-8 6-12v1 3l1 1-2 5s0 2-1 2l1 1c1 0 2 0 3-1s1-1 2-1v2h1c2-1 3-3 4-5l1 2v1 1c2-2 2-1 4-1v1l2 1 3-6c3-4 5-9 8-12h1l-2 3h1l1-1h1l1 1-3 3 4 1v-1c1-1 1-2 1-3-1-2-2-4-3-5z" class="O"></path><path d="M344 230c-1 1-1 1-2 1v-1c0-1 0-2 1-4 1 0 2 1 3 1-1 1-1 2-2 3z" class="C"></path><path d="M346 207c2-2 2-3 4-4-1 3-3 7-5 8l1-4z" class="B"></path><path d="M345 194v-1l6 7c1 1 3 2 4 2v1h-5c-2 1-2 2-4 4v-1c1-1 1-2 1-3v-1c1-1 1-2 1-3-1-2-2-4-3-5z" class="F"></path><path d="M341 206l2-4 4 1c0 1 0 2-1 3v1l-1 4-1 2c-1-1-1-1 0-3-1 0-1-1-2-1 0-1-1-2-1-3z" class="P"></path><path d="M328 220v1c0 1-1 3-2 4l1 1h-1c-1 2-2 4-2 5 2 1 4 1 5 2 1 2 1 3 0 4-2-1-5-3-7-6 1-4 4-8 6-11z" class="E"></path><path d="M346 227c1-3 2-6 4-9h1l-2 10h1v3l-3 1-2 1c-1-1-1-2-1-3 1-1 1-2 2-3z" class="e"></path><path d="M347 232l2-4h1v3l-3 1z" class="I"></path><path d="M330 218c3-1 9 0 12 0-3 6-5 12-9 17h-1c-1-5 3-12 5-16-2 0-5 0-7-1z" class="U"></path><path d="M309 216l23-1c3 0 8 0 10 1l1 1-1 1c-3 0-9-1-12 0l-2 2c-2 3-5 7-6 11 2 3 5 5 7 6l2 1h-3s0-1-1-1c-2-1-4-3-6-4 0-1-1-2 0-2 0-2 2-6 3-7s1-2 2-3h-2l2-2v-1l-19-1c-3 0-6 0-9-1l1-1 8 1h2z" class="G"></path><path d="M326 211v1 1c2-2 2-1 4-1v1l2 1 3-6c3-4 5-9 8-12h1l-2 3h1l1-1h1l1 1-3 3-2 4c0 1 1 2 1 3 1 0 1 1 2 1-1 2-1 2 0 3-1 2-1 3-1 4l-1-1c-2-1-7-1-10-1l-23 1v-1h0l1-2c3-3 5-8 6-12v1 3l1 1-2 5s0 2-1 2l1 1c1 0 2 0 3-1s1-1 2-1v2h1c2-1 3-3 4-5l1 2z" class="B"></path><path d="M332 215h7c-1-1-2 0-3-1l5-8c0 1 1 2 1 3 1 0 1 1 2 1-1 2-1 2 0 3-1 2-1 3-1 4l-1-1c-2-1-7-1-10-1z" class="k"></path><path d="M322 224l2-3h2c-1 1-1 2-2 3s-3 5-3 7c-1 0 0 1 0 2 2 1 4 3 6 4 1 0 1 1 1 1h3l3 1-8 8c-1 1-3 2-4 3h-1 0c0-1-1-2-1-3h-1c-1 1-2 2-2 3h-1c-1 1-2 1-3 2h-1c-1 1-1 1-3 1l-1-1v-7h1 0 1l1-2c0-1 0-3 2-5 1-1 1-2 2-4l3-6h0l4-6v2z" class="U"></path><path d="M310 245h2v-1h2c0 2 1 3 0 5-1 0-1 0-2 1v2c-1 1-1 1-3 1l-1-1v-7h1 0 1z" class="S"></path><path d="M321 233c2 1 4 3 6 4 1 0 1 1 1 1v1 1l-1 1v-1-1l-2-1v1 1 1c-2-1-3-2-4-2l1 2c-2 1-2 0-3 1l1 2c-1 1-2 1-3 1v-3c2-2 2-5 3-7v-2h1z" class="k"></path><path d="M321 239h-1c0-1 1-2 1-4l3 3 1 1v1 1c-2-1-3-2-4-2z" class="P"></path><path d="M331 238l3 1-8 8c-1 1-3 2-4 3h-1 0c0-1-1-2-1-3 1-2 3-4 5-6v-1-1-1l2 1v1 1l1-1v-1-1h3z" class="B"></path><path d="M322 224l2-3h2c-1 1-1 2-2 3s-3 5-3 7c-1 0 0 1 0 2h-1v2c-1 2-1 5-3 7v3c-1 1 0 1 0 3-3-1-1-3-2-5l-1 1h-2v1h-2l1-2c0-1 0-3 2-5 1-1 1-2 2-4l3-6h0l4-6v2z" class="P"></path><path d="M322 224l2-3h2c-1 1-1 2-2 3s-3 5-3 7c-1 0 0 1 0 2h-1v2c-1 2-1 5-3 7 0-1 0-2 1-3v-4h0l-1-2 5-9z" class="H"></path><path d="M290 195c1 2 1 5 1 7v1h4v1l-2 2h4c2 0 3-1 5-2 0 0 1 0 1-1l1 1 1-1c0 1 1 1 1 2s-1 2-1 3v1 1c1 0 2 1 2 1v4 1l-8-1-1 1c3 1 6 1 9 1l19 1v1l-2 2-2 3v-2l-4 6c0-2-1-3-1-4v-1h-1c-1 0-1-1-1-1v-2-1c-1 1-2 1-2 1h-1l-1-1c-1 2-1 3-2 4 0 1-1 1-2 2h0c1-2 2-4 2-6h-2c0 1 0 1-1 2h-2l-5 5c-1-1-1-1-3-1h-1c-2 2-5 3-7 4l1-6c2-9 1-19 1-28z" class="E"></path><path d="M300 219c2 0 5-1 7 0 0 1 0 1-1 2h-2l-3-1-1-1z" class="G"></path><path d="M317 223c1-1 2-2 3-2 1-1 1 0 2 0v1l-4 6c0-2-1-3-1-4v-1z" class="d"></path><path d="M300 219l1 1 3 1-5 5c-1-1-1-1-3-1 1-2 2-5 4-6z" class="J"></path><path d="M304 204l1-1c0 1 1 1 1 2s-1 2-1 3v1 1c-1 2-1 2-3 3v-1c-1 1-1 2-3 2l1-4 4-6z" class="T"></path><path d="M290 195c1 2 1 5 1 7v1h4v1l-2 2h4c2 0 3-1 5-2 0 0 1 0 1-1l1 1-4 6h0c-1 1-2 1-3 0h0l-1-1v-1c-1-1-1-1-2-1l-1 1 1 1 1 1-1 1v4h1c1 0 1 0 2 1h-3c0 1 0 1 1 2h-1c1 1 1 1 1 2v3 2h0c-2 2-5 3-7 4l1-6c2-9 1-19 1-28z" class="I"></path><path d="M295 203v1l-2 2h-2v-3h4z" class="i"></path><path d="M294 218c1 1 1 1 1 2v3 2c-2-2-2-3-2-5l1-2z" class="L"></path><path d="M307 219h2c0 2-1 4-2 6h0c1-1 2-1 2-2 1-1 1-2 2-4l1 1h1s1 0 2-1v1 2s0 1 1 1h1v1c0 1 1 2 1 4h0l-3 6c-1 2-1 3-2 4-2 2-2 4-2 5l-1 2h-1 0-1v7-1l-1 1-1-1c0 1 0 2-1 3h-1c-2 1-4 1-6 0-1 0-2 1-4 0l-1 1c-1-2 0-6 1-8h1v-1c0-2 1-5 3-6v-1c1-1 1-2 0-3v-1c1-1 1-2 2-3 2-2 2-5 5-7l1-1-1-1c1-1 1-1 2-1l-1-1c1-1 1-1 1-2z" class="k"></path><path d="M307 244l1 1v7-1l-1 1-1-1c0 1 0 2-1 3l2-10z" class="E"></path><path d="M294 247h1v-1c0-2 1-5 3-6-1 2-2 5-2 7-1 2-1 6-1 8 2-3 4-6 4-9l1-1v1 4c-1 2-1 3 0 4h4c-2 1-4 1-6 0-1 0-2 1-4 0l-1 1c-1-2 0-6 1-8z" class="Q"></path><path d="M307 219h2c0 2-1 4-2 6-2 2-5 6-6 9v1c1-1 1-1 1-2l3-4-1 4-4 8c-1 3-2 3-3 6-1 1 0 0-1 0 0-2 1-5 2-7v-1c1-1 1-2 0-3v-1c1-1 1-2 2-3 2-2 2-5 5-7l1-1-1-1c1-1 1-1 2-1l-1-1c1-1 1-1 1-2z" class="F"></path><path d="M315 222s0 1 1 1h1v1c0 1 1 2 1 4h0l-3 6c-1 2-1 3-2 4-2 2-2 4-2 5l-1 2h-1 0-1l-1-1v-2c1-1 1-2 1-3l1-5 3-6c0-1 0-2 1-3l2-3z" class="U"></path><path d="M315 222s0 1 1 1h1v1l-2 3c-1 0-2 1-2 1v1-4l2-3z" class="Q"></path><path d="M317 224c0 1 1 2 1 4h0c-2 1-3 1-4 3 0 1-1 3-2 4 0-3 2-6 3-8l2-3z" class="Z"></path><path d="M312 235c1-1 2-3 2-4 1-2 2-2 4-3l-3 6c-1 2-1 3-2 4-2 2-2 4-2 5l-1 2h-1 0c0-4 1-7 3-10z" class="M"></path><path d="M287 219c0 2 0 3 2 4l-1 6c2-1 5-2 7-4h1c2 0 2 0 3 1l5-5h2l1 1c-1 0-1 0-2 1l1 1-1 1c-3 2-3 5-5 7-1 1-1 2-2 3v1c1 1 1 2 0 3v1c-2 1-3 4-3 6v1h-1c-1 2-2 6-1 8l1-1c2 1 3 0 4 0 2 1 4 1 6 0h1c1-1 1-2 1-3l1 1 1-1v1l1 1c2 0 2 0 3-1h1c1-1 2-1 3-2h1c0-1 1-2 2-3h1c0 1 1 2 1 3h0 1c-2 2-4 4-7 5-4 2-10 2-14 2-2 1-6 0-7 2l2 2 2 1-2 1c1 2 1 3 1 5l-1 1h1c1 1 0 1 1 2l-3 1c-1-4-3-8-5-12h0l-3-3c-1-1-2-1-3-1l-2-2-2-4 3-2-2-2 2-7 1-11v2c1-1 1-3 2-4l1-7z" class="O"></path><path d="M297 268c-1-1-1 0-1-1-2-1-1-2-1-3l1-1c1 2 1 3 1 5z" class="e"></path><path d="M294 246h0v-1c-1-3-1-5 0-7v-3c1-2 1-3 3-4l-3 15z" class="K"></path><path d="M287 252l2-20c0 2 0 3 1 4 0 3-1 7-1 11-1 1-1 1-1 3l1 1v1c0 1 0 1 1 2 1 2-1 4 0 6l-3-3c-1-1-2-1-3-1l-2-2-2-4 3-2 3 2 1 2z" class="B"></path><path d="M283 248l3 2 1 2c1 1 1 2 2 3l-2 2c-1-1-2-1-3-1l-2-2-2-4 3-2z" class="I"></path><path d="M287 219c0 2 0 3 2 4l-1 6c2-1 5-2 7-4h1c2 0 2 0 3 1l5-5h2l1 1c-1 0-1 0-2 1l1 1-1 1c-3 2-3 5-5 7-1 1-1 2-2 3v1c1 1 1 2 0 3v1c-2 1-3 4-3 6v1h-1v-1l3-15c1-1 1-3 1-4-2 0-4 0-6 2l-3 3-2 20-1-2-3-2-2-2 2-7 1-11v2c1-1 1-3 2-4l1-7z" class="H"></path><path d="M284 230c1-1 1-3 2-4 0 1 1 2 1 2v8l-3-2v-4z" class="F"></path><path d="M284 228v2 4l3 2c-1 2-1 3-1 5s0 3-1 5h-2c1 2 2 1 2 2l1 2-3-2-2-2 2-7 1-11z" class="h"></path><path d="M382 226h9c-3 2-6 3-8 5l-2-1-6 1c-4 0-11 0-15 2h-2v1c2 3 3 6 3 9v2l2 1h1v3c-1 1-1 2-1 3 0-1 0-1-1-2-1 1-1 2-2 3s-2 3-3 4c-2 2-2 3-2 6h4v3h1v5c1 1 1 1 1 2 1 2 2 5 0 7v1l-1 4h2c0 1 1 1 1 2l1 1c-1 1-1 2-1 2-3 3-5 5-7 8l-1 1c-1 1-1 2-2 4l-1 1c-1 1-1 2-1 3 1 1 2 2 4 2 1 0 1 0 2-1 1 1 1 1 1 2 1 0 1 2 2 2h0l1 2h0c-1 1-1 2-2 3h-1l-3-3-3 6-1-1v1h-4l-1 3-2 2v1l-1 1h0c-1 1-1 2-2 3-1-1-2-1-2-1h-2l-1-1v2c-1 1-2 1-3 2l3 1c-1 1-1 2-1 3h-1l-1-1c-3-2-7-4-10-7 0-1 0 0-1-1l-1 1h-1l-1 1c-1-4-4-8-4-12l-1-1v1h0c0 2 1 4 1 6-2-1-3-2-4-2v-1h1l-1-1v-1h-1c-1 2-3 4-5 6h-4l-1 1c-1 0-2-1-3-2l-2 4-1 2c-1 0-2 1-3 2-2 0-4 0-5 2-3 2-5 6-8 8l-6-5h1 1c1-2 3-3 4-5h0l1-1v-2l3-4v-1-1l-1-1v-1c-2 1-2 1-4 1l-1-1c-2-2-3-3-5-6h0c-1-2-2-3-3-5v-2-1l-4-10h-2c-1-1-3-1-5-1h0v-1c0 1 0 1-1 1-2 0-2 0-3-1h0c-1-1-1-2-1-3l1-1h-2c-1 1-1 2-2 3-1-4-2-8-2-12-1-2-1-4 0-6 0-3 1-6 2-9 1-1 4-7 6-8 2 0 6 0 7 1h4c1 0 3-3 3-4 2-3 4-6 7-8h0c2 1 3 2 4 3l2 4 2 2c1 0 2 0 3 1l3 3h0c2 4 4 8 5 12l3-1c-1-1 0-1-1-2h-1l1-1c0-2 0-3-1-5l2-1-2-1-2-2c1-2 5-1 7-2 4 0 10 0 14-2 3-1 5-3 7-5 1-1 3-2 4-3l8-8c3-2 7-4 11-6l2-1 3-1 10-2 6-1 16-2z" class="T"></path><path d="M304 263l1-1c1 1 1 1 1 2v5 1 1l-1-1v-1h-2c-1-2 0-3-1-4 0-1 1-2 2-2z" class="K"></path><path d="M357 251l1 2h2c-1 1-2 3-3 4-2 2-2 3-2 6h-1c0-2 0-3 1-4l-1-1-3 3h-2c1-2 3-3 4-5h-1l1-2 2-1 2-2z" class="k"></path><path d="M353 254l2-1 1 1c0 2-1 2-3 2h-1l1-2z" class="i"></path><path d="M298 262c2 0 4 0 6 1-1 0-2 1-2 2-1 2 0 3-2 4l1 1-1 2c1 0 2 1 3 1l1-1v1c-2 1-3 1-4 1l-1 1-1-1v-3c-1-1 0-1-1-2h-1l1-1c0-2 0-3-1-5l2-1z" class="L"></path><path d="M290 260c2 4 4 8 5 12l3-1v3l1 1 1 1 1-1v1l-2 1v3l-2 10h0c0 6-3 13-5 18-1-1-2-1-1-3s1-4 1-6v-1l1-2h-1l1-2c-1 0-1 0-1-1-1-1-4-2-5-3s-1-1-1-2c1-1 1-2 1-3l-1-2c1-1 1-2 1-3s0-2-1-3l1-1c1-1 1-1 2-1 1-1 2-1 3-1l-1-2 1-1c-1-3-2-6-4-9l2-2z" class="C"></path><path d="M290 260c2 4 4 8 5 12 2 7 0 17-2 24h-1l1-2v-1c0-7 2-14-1-22-1-3-2-6-4-9l2-2z" class="N"></path><path d="M291 272l1-1c3 8 1 15 1 22v1c-1 0-1 0-1-1-1-1-4-2-5-3s-1-1-1-2c1-1 1-2 1-3l-1-2c1-1 1-2 1-3s0-2-1-3l1-1c1-1 1-1 2-1 1-1 2-1 3-1l-1-2z" class="L"></path><path d="M287 276c1 1 1 1 3 1 0 1 0 2 1 3v3h0v2h-4l-1-2c1-1 1-2 1-3s0-2-1-3l1-1z" class="B"></path><path d="M346 275l1 1-1-4c0-1 0-1-1-2 0-1 0-2 1-3 1 2 1 4 2 6l2 4c-1-2-1-5-2-6s-1-2-1-3c0-2 0-3 1-4v-2h1l1 1v1c1 2 3 4 4 6v-2-5h1 4v3h1v5c1 1 1 1 1 2 1 2 2 5 0 7v1l-1 4h2c0 1 1 1 1 2-3 0-5-1-8-1-2 0-4-1-7-1l-1-1v1h-1c1-2 1-2 1-3-1-1-1 0-1-1v-4-2z" class="S"></path><path d="M359 266h1v5c-1-1-1-1-1-3l-1 1s0 1-1 2l-2 2v-1c1-2 1-4 2-6h1v1l1-1z" class="Y"></path><path d="M355 272v1l2-2c1-1 1-2 1-2l1-1c0 2 0 2 1 3s1 1 1 2c-3 2-6 3-9 4l-1-1h1c0-1 0-2-1-3h0l1-1c-1-1-1-2-1-2 2 0 2 1 4 2z" class="c"></path><path d="M352 277c3-1 6-2 9-4 1 2 2 5 0 7v1l-1 4h2c0 1 1 1 1 2-3 0-5-1-8-1-2 0-4-1-7-1l-1-1c1-1 1-2 1-3l-1-1v-2l2 2c3 1 4-1 6-3h0c-1 0-1 1-2 1-1 1-1 1-2 1l-1-1 2-1z" class="T"></path><path d="M348 285c2-1 3-3 5-4h2c2-1 4-1 6-1v1l-1 4h2c0 1 1 1 1 2-3 0-5-1-8-1-2 0-4-1-7-1z" class="O"></path><path d="M360 285v-1c-2 0-2 0-2-2l1-1h2l-1 4z" class="f"></path><path d="M326 250l1 1 2-2c1-1 1-2 2-3h1l-1 2c0 1 1 1 0 2v3l-1 2 1 1c-1 1-1 1-2 1v1h3v-2h0l1-1c0 1 1 1 1 2v2c0 2-1 3-2 4l-1 1-2 2h2c0 2 0 3-1 4l-2 1v-1l2-2c-1 0-2 1-3 1l-1 1 1 2c-1 1-1 1-2 1v-2l-1 1h-1-1l-1 1c1 0 1 1 1 2-1 1-1 0-1 1v-2h-2l1-1-1-2c0-1 1-1 2-2h2l-1-1c-1-1-3 0-4-1v-2-2-2h-1v5 1c0 1 0 1-1 2v2l-2 1c-1-1-1-1-1-2l1-1v1 1l1-1 1-2v-1c-1 1-2 1-2 1-1-1-2-2-2-3v1h0c0 1-1 2-1 3l1 1c-1 1-1 1-2 1s-2 1-2 1c0 1 0 1 1 1 1 1 1 1 2 0 1 2 1 3 0 4h-1c0-1 0-1 1-1v-1h-1l-1-1h0c-1 0-2 0-2-1l-2 1v-1l1-1c1 0 1 0 2-1v-5h0v-4l-1 1-1-1c1-1 1-1 2-1v-1c1-1 3-1 4-1 2-1 5-1 7-2l7-7z" class="L"></path><defs><linearGradient id="Q" x1="335.914" y1="234.504" x2="345.653" y2="251.11" xlink:href="#B"><stop offset="0" stop-color="#d0cfd0"></stop><stop offset="1" stop-color="#f4f3f5"></stop></linearGradient></defs><path fill="url(#Q)" d="M382 226h9c-3 2-6 3-8 5l-2-1-6 1c-4 0-11 0-15 2h-2l-12 3c0 1 0 1 1 2-2 1-1 1-3 1l-1-1c-3 0-5 1-8 3 2 2 2 2 2 4-1 1-1 1-2 1s-1 0-2 1l-1-1h-1c-1 1-1 2-2 3l-2 2-1-1-7 7c-2 1-5 1-7 2-1 0-3 0-4 1v1c-1 0-1 0-2 1l1 1 1-1v4h0l-2-2c0-1 0-1-1-2l-1 1c-2-1-4-1-6-1l-2-1-2-2c1-2 5-1 7-2 4 0 10 0 14-2 3-1 5-3 7-5 1-1 3-2 4-3l8-8c3-2 7-4 11-6l2-1 3-1 10-2 6-1 16-2z"></path><path d="M296 261l12-1v1c-1 0-1 0-2 1l1 1 1-1v4h0l-2-2c0-1 0-1-1-2l-1 1c-2-1-4-1-6-1l-2-1z" class="D"></path><path d="M335 241c2 2 2 2 2 4-1 1-1 1-2 1s-1 0-2 1l-1-1h-1c-1 1-1 2-2 3l-2 2-1-1s1-1 2-1c2-3 5-6 7-8z" class="I"></path><path d="M284 273h2v4c1 1 1 2 1 3s0 2-1 3l1 2c0 1 0 2-1 3 0 1 0 1 1 2s4 2 5 3c0 1 0 1 1 1l-1 2h1l-1 2v1c0 2 0 4-1 6s0 2 1 3v1c-1 2-8 13-10 14v-1l-1-1v-1c-2 1-2 1-4 1l-1-1c-2-2-3-3-5-6h0c-1-2-2-3-3-5v-2h1l1-1 1 2 1 1 4 5c3-6 5-13 6-19l1-8v-4l1-1 1-1v-4l-1-4z" class="i"></path><path d="M284 273h2v4c1 1 1 2 1 3s0 2-1 3l1 2c0 1 0 2-1 3 0 1 0 1 1 2l-2 4c0 2 0 5-2 7-1 3-3 7-3 10 0 1 0 2-1 3v-3-1c2-9 6-18 6-27v-6l-1-4z" class="E"></path><path d="M287 290c1 1 4 2 5 3 0 1 0 1 1 1l-1 2h1l-1 2c-1 2-2 5-4 7h-1l1-1c-1-1-2-1-3-1v3l-2 3v-3c1-2 1-3 0-5 2-2 2-5 2-7l2-4z" class="F"></path><path d="M287 290c1 1 4 2 5 3 0 1 0 1 1 1l-1 2c0 1-1 2-2 3v-2c-1-2-3-3-5-3l2-4z" class="C"></path><path d="M292 298v1c0 2 0 4-1 6s0 2 1 3v1c-1 2-8 13-10 14v-1l-1-1v-1c-2 1-2 1-4 1l-1-1c1-1 3-3 4-5s3-4 3-6l2-3v-3c1 0 2 0 3 1l-1 1h1c2-2 3-5 4-7z" class="I"></path><path d="M283 309l2-3c0 1 1 1 2 2-2 2-3 6-6 8l1 1v1c-2 0-3 1-4 2l-1 1-1-1c1-1 3-3 4-5s3-4 3-6z" class="C"></path><path d="M282 318l1 1 1-2c0-1 0-1 1-2 2-1 2-2 3-4s2-2 4-2c-1 2-8 13-10 14v-1l-1-1v-1c-2 1-2 1-4 1l1-1c1-1 2-2 4-2z" class="f"></path><path d="M282 324h0l6-6c1-2 3-4 4-6l2-4c1 0 1 0 2-1v1h2c-1-1-2-2-2-3l1-1h4l1 1h1l-1-1h0c-2 0-3-1-4-2h1 2 1 0c-1-1-2-1-3-1v-1l1-1c0 1 1 1 2 1v-1h-2v-1l2-1h-1c-1 0-2 1-2 1l-1 1-1-1 1-1c1-1 2-1 3-1 1-1 2-1 3-2v1l-1 2 1 1-1 1h3c-1-1-1-2-1-3h1l-1-1 1-1 2 1v-1c2 1 1 2 1 4 2 0 2-1 4-1l1 1c-2 1-4 2-5 1-2 0-2 0-3 1-2 3-3 8-3 11 0 2-2 3-2 5l-3 6-1 1-2 4-1 2c-1 0-2 1-3 2-2 0-4 0-5 2-3 2-5 6-8 8l-6-5h1 1c1-2 3-3 4-5h0l1-1v-2l3-4z" class="S"></path><path d="M358 233v1c2 3 3 6 3 9v2l2 1h1v3c-1 1-1 2-1 3 0-1 0-1-1-2-1 1-1 2-2 3h-2l-1-2-2 2-2 1-1 2c-3 4-7 6-11 9-1 1-3 2-5 3-1 0-1 0-2 1h-1l-1-1h2v-1h-1l1-2c1-3 3-7 4-10v-2c1-3 4-11 3-13h0c-3 6-5 13-7 19v-2c0-1-1-1-1-2l-1 1h0v2h-3v-1c1 0 1 0 2-1l-1-1 1-2v-3c1-1 0-1 0-2l1-2 1 1c1-1 1-1 2-1s1 0 2-1c0-2 0-2-2-4 3-2 5-3 8-3l1 1c2 0 1 0 3-1-1-1-1-1-1-2l12-3z" class="E"></path><path d="M334 265h2c1-1 3-3 4-5 1-1 2 0 3-1s2-1 2-3c0-1 1-3 1-4v-1h-2v-1c0-1 0-1-1-1l-1-1 1-1c1-1 0-2 1-4v1c1 0 1 0 1 1 0 0 1 0 1 1 1 2 1 3 3 4 1 2 1 2 3 3l1 1-1 2c-3 4-7 6-11 9-1 1-3 2-5 3-1 0-1 0-2 1h-1l-1-1h2v-1h-1l1-2z" class="B"></path><path d="M358 233v1c2 3 3 6 3 9v2l2 1h1v3c-1 1-1 2-1 3 0-1 0-1-1-2-1 1-1 2-2 3h-2l-1-2-2 2-2 1-1-1c-2-5-5-10-8-14 2 0 1 0 3-1-1-1-1-1-1-2l12-3z" class="D"></path><path d="M356 238v3l-3 3c-1-1-1-1-3-1l-1-1 1-2c2-2 4-2 6-2z" class="F"></path><path d="M354 249l1-1c0-1-1-2-1-2 1-1 1-2 2-2 2-1 3-1 5-1v2h0l-6 6c-1-1-1-1-1-2z" class="C"></path><path d="M358 233v1l-2 1c0 1 0 1 1 2h-1v1c-2 0-4 0-6 2l-1 2-2-4c-1-1-1-1-1-2l12-3z" class="K"></path><path d="M347 238l2 4 1 1 4 6c0 1 0 1 1 2l6-6h0l2 1h1v3c-1 1-1 2-1 3 0-1 0-1-1-2-1 1-1 2-2 3h-2l-1-2-2 2-2 1-1-1c-2-5-5-10-8-14 2 0 1 0 3-1z" class="Z"></path><path d="M363 246h1v3c-1 1-1 2-1 3 0-1 0-1-1-2-1 1-1 2-2 3h-2l-1-2 6-5z" class="Y"></path><path d="M276 247c2 1 3 2 4 3l2 4 2 2c1 0 2 0 3 1l3 3h0l-2 2c2 3 3 6 4 9l-1 1 1 2c-1 0-2 0-3 1-1 0-1 0-2 1l-1 1v-4h-2l1 4v4l-1 1-1 1v4l-1 8c-1 6-3 13-6 19l-4-5-1-1-1-2-1 1h-1v-1l-4-10h-2c-1-1-3-1-5-1h0v-1c0 1 0 1-1 1-2 0-2 0-3-1h0c-1-1-1-2-1-3l1-1h-2c-1 1-1 2-2 3-1-4-2-8-2-12-1-2-1-4 0-6 0-3 1-6 2-9 1-1 4-7 6-8 2 0 6 0 7 1h4c1 0 3-3 3-4 2-3 4-6 7-8h0z" class="C"></path><path d="M271 308c2-1 4-2 6-1 0 1 0 1-1 3-1-1-2-1-2-1h-2l-1-1z" class="X"></path><path d="M269 262c-3 8-5 14-5 22 1 1 1 1 2 1s1 0 2 1c2-2 2-6 4-8 1-3 3-6 4-9 1-1 2-1 2-3 2 1 3 7 4 10v2h-3c0-1 1-2 0-3 0-1 0-2-1-3l1-1-1-2-1 1h0c-3 6-7 12-8 18v1c-1 3-1 6 0 9 1 1 1 1 1 2-1-1-2-2-2-3v-1c0-1-2-5-3-6 1 3 1 6 2 8v2c0 1 1 2 1 3l2 3-1 1h-1v-1l-4-10c-2-8-2-19 1-26l3-9 1 1z" class="T"></path><path d="M282 281c1 9-2 15-7 23-1-1-3-3-4-5-3-6-1-12 1-17 1-3 4-9 6-10 1 1 1 2 1 3 1 1 0 2 0 3h3v3z" class="G"></path><defs><linearGradient id="R" x1="260.503" y1="273.465" x2="278.066" y2="265.182" xlink:href="#B"><stop offset="0" stop-color="#1e2020"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#R)" d="M269 262c1-5 4-9 7-12l4 4h2 0l2 2c1 0 2 0 3 1l3 3h0l-2 2c2 3 3 6 4 9l-1 1 1 2c-1 0-2 0-3 1-1 0-1 0-2 1l-1 1v-4h-2l1 4v4l-1 1-1 1-1-2v-3-2c-1-3-2-9-4-10 0 2-1 2-2 3-1 3-3 6-4 9-2 2-2 6-4 8-1-1-1-1-2-1s-1 0-2-1c0-8 2-14 5-22z"></path><path d="M272 278c-1-1-1 0-1-1s1-3 2-4c0-2 0-4 1-5 0-2 0-3 2-4l2 2c0 2-1 2-2 3-1 3-3 6-4 9z" class="R"></path><path d="M282 254l2 2c1 0 2 0 3 1l3 3h0l-2 2c2 3 3 6 4 9l-1 1 1 2c-1 0-2 0-3 1-1 0-1 0-2 1l-1 1v-4h-2l1 4v4l-1 1-3-16c-1-1-2-2-2-3h-1l-2-1h-2c-2 3-4 7-5 10 0 2-1 4-2 5h0c1-7 5-15 8-22l1 1h1c2 1 3 0 5-2h0z" class="o"></path><path d="M282 254l2 2h0c-3 1-5 2-8 2l-1-1 1-1h1c2 1 3 0 5-2h0z" class="K"></path><path d="M284 256c1 0 2 0 3 1l3 3h0l-2 2-2-1v-4c-1 0-2 0-2-1h0z" class="l"></path><path d="M286 261l2 1c2 3 3 6 4 9l-1 1 1 2c-1 0-2 0-3 1-1 0-1 0-2 1l-1 1v-4h-2c0-1-1-3-1-3 0-3-1-5-2-8l5-1z" class="B"></path><path d="M286 273c0-1 0-2 1-2 1-2 1-1 3-1l1 2 1 2c-1 0-2 0-3 1-1 0-1 0-2 1l-1 1v-4z" class="M"></path><path d="M276 247c2 1 3 2 4 3l2 4h0-2l-4-4c-3 3-6 7-7 12l-1-1-3 9c-3 7-3 18-1 26h-2c-1-1-3-1-5-1h0v-1c0 1 0 1-1 1-2 0-2 0-3-1h0c-1-1-1-2-1-3l1-1h-2c-1 1-1 2-2 3-1-4-2-8-2-12-1-2-1-4 0-6 0-3 1-6 2-9 1-1 4-7 6-8 2 0 6 0 7 1h4c1 0 3-3 3-4 2-3 4-6 7-8h0z" class="C"></path><path d="M248 275c1-2 1-3 2-5h0c1 1 1 1 0 2v2 1 2l1 5v6c3 1 7 1 10 2v1c-3 0-5 0-8-1h-2c-1 1-1 2-2 3-1-4-2-8-2-12-1-2-1-4 0-6h1z" class="V"></path><path d="M247 275h1v2 3l-1 1c-1-2-1-4 0-6z" class="Z"></path><path d="M276 247c2 1 3 2 4 3l2 4h0-2l-4-4c-3 3-6 7-7 12l-1-1c-4 0-8 0-12 1-3 5-4 10-6 15v-2-1-2c1-1 1-1 0-2h0c-1 2-1 3-2 5h-1c0-3 1-6 2-9 1-1 4-7 6-8 2 0 6 0 7 1h4c1 0 3-3 3-4 2-3 4-6 7-8h0zm54 35l2-7c2-2 4-5 6-6 1 0 2-1 3-2h2c1 1 1 2 2 3l1 5v2 4c0 1 0 0 1 1 0 1 0 1-1 3h1v-1l1 1c3 0 5 1 7 1 3 0 5 1 8 1l1 1c-1 1-1 2-1 2-3 3-5 5-7 8l-1 1c-1 1-1 2-2 4l-1 1c-1 1-1 2-1 3 1 1 2 2 4 2 1 0 1 0 2-1 1 1 1 1 1 2 1 0 1 2 2 2h0l1 2h0c-1 1-1 2-2 3h-1l-3-3-3 6-1-1v1h-4l-1 3-2 2v1l-1 1h0c-1 1-1 2-2 3-1-1-2-1-2-1h-2l-1-1v2c-1 1-2 1-3 2l3 1c-1 1-1 2-1 3h-1l-1-1c-3-2-7-4-10-7 0-1 0 0-1-1l-1 1h-1l-1 1c-1-4-4-8-4-12l-1-1v1h0c0 2 1 4 1 6-2-1-3-2-4-2v-1h1l-1-1v-1h-1c-1 2-3 4-5 6h-4l-1 1c-1 0-2-1-3-2l1-1 3-6c0-2 2-3 2-5 0-3 1-8 3-11 1-1 1-1 3-1 1 1 3 0 5-1l5-1c1-1 2-1 3-2 1 0 2-2 3-2 1-1 1 0 2-1v-1c0-1 0-1 1-2l1 1c1-2 1-5 1-8z" class="N"></path><path d="M344 288s1 0 2 1h0 1c0 1-1 3 0 5h-2c0 1-1 3-2 3v-3-4l1-2z" class="a"></path><path d="M338 323c-2-1-5-3-7-3h-1v-1l2-2 3 1h3v5z" class="L"></path><path d="M323 299c4 3 9 7 10 12-1 0-1 0-2 1l1-2h-2l-1-1c-2-1-3-2-3-3s0-1-1-2c-1-2-1-3-2-5z" class="E"></path><path d="M334 326c-1-1-2-2-2-3l1-1c1 1 3 1 4 2s2 0 3 0c1 1 1 1 2 1l2 1-1 1h0c-1 1-1 2-2 3-1-1-2-1-2-1h-2l-1-1c-1 0-1-1-2-2z" class="S"></path><path d="M333 332l-8-6c1-1 2-3 4-5v3l1 1 4 1c1 1 1 2 2 2v2c-1 1-2 1-3 2z" class="L"></path><path d="M329 324l1 1 2 2 2 2c-2 0-3 0-4-1s-2-2-2-3l1-1z" class="D"></path><path d="M347 282c0 1 0 1-1 3h1v-1l1 1c3 0 5 1 7 1 3 0 5 1 8 1l1 1c-1 1-1 2-1 2-3 3-5 5-7 8l-1 1c-1 1-1 2-2 4l-1 1c-1 1-1 2-1 3h-1l-1-1-1-1-3-1c-2 0-3-1-4-2 0-1 1-2 2-2l2-1h0c1-1 2-2 3-4l-1-1c-1-2 0-4 0-5h-1 0c-1-1-2-1-2-1h-2c-1-1-1-1-1-2l2-1h1l3-3z" class="F"></path><path d="M348 295c1 1 2 1 4 1l1 1c-2 1-3 0-4 1l1 1h3v2h-2l-8-1 2-1h0c1-1 2-2 3-4z" class="M"></path><path d="M343 300l8 1v1c0 2-1 3-2 4l-1-1-3-1c-2 0-3-1-4-2 0-1 1-2 2-2zm4-11c3-1 8 1 12 1-2 1-7 4-7 6-2 0-3 0-4-1l-1-1c-1-2 0-4 0-5z" class="D"></path><path d="M345 304l3 1 1 1 1 1h1c1 1 2 2 4 2 1 0 1 0 2-1 1 1 1 1 1 2 1 0 1 2 2 2h0l1 2h0c-1 1-1 2-2 3h-1l-3-3-3 6-1-1v1h-4l-1 3-2 2v1l-2-1c-1 0-1 0-2-1l-2-1v-5h-3l1-1-1-1 1-1c0-1 1 0 2 0l1-1c0-1-1-3-1-4-1-1-1-1 0-2l3-1c1-1 2-2 4-3z" class="J"></path><path d="M357 308c1 1 1 1 1 2l-3 3h0c-1-2-1-2 0-4 1 0 1 0 2-1z" class="a"></path><path d="M338 318l1 1 8 1-1 3-2 2v1l-2-1c-1 0-1 0-2-1l-2-1v-5z" class="L"></path><path d="M338 318l1 1 3 3h2c-1 2-1 2-2 3-1 0-1 0-2-1l-2-1v-5zm7-14l3 1 1 1 1 1c-1 1-1 2-3 3 2 1 4 1 5 2l-4 6c-1 1-6-2-7-3l-2-1c0-1-1-3-1-4-1-1-1-1 0-2l3-1c1-1 2-2 4-3z" class="E"></path><path d="M345 304l3 1 1 1 1 1c-1 1-1 2-3 3-2-1-4-1-6-3 1-1 2-2 4-3z" class="M"></path><path d="M330 282l2-7c2-2 4-5 6-6 1 0 2-1 3-2h2c1 1 1 2 2 3l1 5v2 4c0 1 0 0 1 1l-3 3h-1l-2 1c0 1 0 1 1 2h2l-1 2v4l-3 4c-1 1-2 1-3 1l-1-1s0 1-1 2l-1-1v2 1c1 1 1 2 1 4h-1l-8-10 3-6c1-2 1-5 1-8z" class="f"></path><path d="M339 282h1v1l-1 2h-1v-2l1-1z" class="B"></path><path d="M342 288h2l-1 2v4l-3 4c-1 1-2 1-3 1l-1-1v-4-1l-1 1v1 1l-1-1c0-1-1-1-1-2h1v-1c0-2 1-2 2-3h3c1 0 2 0 2-1h1z" class="I"></path><path d="M343 290v4l-3 4c-1-2-2-2-1-4l1-1 3-3z" class="f"></path><path d="M330 282l2-7c2-2 4-5 6-6 1 0 2-1 3-2h2c1 1 1 2 2 3l1 5v2 4c0 1 0 0 1 1l-3 3h-1l-2-3 1-1c0-2-1-4-2-5h0v3l1 1v1h0c-2-2-2-3-2-5h0c-1-1-1-2-1-3-2 1-2 1-3 3v3 1l1 1-1 1h-5z" class="B"></path><path d="M341 273v-4h1c1 2 1 2 0 3l-1 1z" class="R"></path><path d="M341 273l1-1c1 1 1 2 1 2 2 1 2 2 3 3v4c0 1 0 0 1 1l-3 3h-1l-2-3 1-1c0-2-1-4-2-5h0v-1c0-1 1-2 1-2z" class="I"></path><path d="M306 305v-1-1c1-1 5-1 6-1 4-1 8-2 11-3 1 2 1 3 2 5 1 1 1 1 1 2s1 2 3 3l1 1h2l-1 2-3 3c-3 5-6 8-8 13l-1 1c-1-4-4-8-4-12l-1-1v1h0c0 2 1 4 1 6-2-1-3-2-4-2v-1h1l-1-1v-1h-1c-1 2-3 4-5 6h-4l-1 1c-1 0-2-1-3-2l1-1 3-6c2-3 4-7 5-11z" class="O"></path><path d="M306 305c0 3-1 4-2 7 0 1-1 2-1 4l1-1v-1c0 1 0 1-1 2-1 2-1 2-1 4-1 1-2 1-4 2l3-6c2-3 4-7 5-11z" class="K"></path><path d="M312 309c1-1 1-2 2-3 0 1 1 9 0 10h0v1h0c0 2 1 4 1 6-2-1-3-2-4-2v-1h1l-1-1v-1h-1c-1 2-3 4-5 6h-4l-1 1c-1 0-2-1-3-2l1-1c2-1 3-1 4-2 3-1 3-1 5-4 0 0 0-1 1-1l4-6z" class="g"></path><path d="M302 320c3-1 3-1 5-4v4c-3 1-4 1-6 4h0l-1 1c-1 0-2-1-3-2l1-1c2-1 3-1 4-2z" class="B"></path><path d="M307 316s0-1 1-1l4-6-2 8c0 1-1 2-2 3h-1v-4z" class="K"></path><path d="M321 305h1c2 2 3 5 3 7 0 1 1 3 1 3h2c-3 5-6 8-8 13l-1 1c-1-4-4-8-4-12h1 1v-2h1v1c1 0 0 0 1 1l1-1v-2-3l1-1v-5z" class="L"></path><path d="M319 317c1 1 2 2 2 4 0 1-1 1-1 2-2-2-2-3-2-5l1-1z" class="R"></path><path d="M320 314c2 1 3 1 4 1l1 1-3 2c-1 0-1-1-2-2v-2z" class="C"></path><path d="M320 314v-3l1-1v-5 1c1 1 1 2 0 4l2 1c1 1 1 3 1 4-1 0-2 0-4-1z" class="E"></path><path d="M688 396h0c1 1 2 2 2 3l2 2c1 1 2 1 3 1h1c3 0 7-1 10 0v1l15-1c6 1 14-1 20 0 1 1 2 1 4 0 1 0 2 0 3 1h11l22-1h2 30 3l11 1h2l-2 2 1 1c-2 0-3 2-5 2-4 1-9 0-13 2l6 3-1 1c-2 0-4 1-5 3v1c1 2 4 3 6 4 9 4 17 7 20 18-2 2-5 5-7 6h-2l-2-1-3 2 4 4-2 1 4 4c-1 0-2 1-2 1-3 1-7 0-11 1l-2-1h-1-1l-1 1c-4 0-10 1-13 0h-2-1l-1-3c-1 1-2 2-2 3-3 1-9 1-12 1-1-1-1-1-1-2-1-1-1 0-1-1v1 1h0-1c-2 2-10 1-12 1h-1c-1 0-2 1-3 1h-3-3 0c-2 1-8 0-10 0h-1c-4-1-9 0-13 0h-4-1l-16-1h-1c-1 1-2 1-3 2l1 2-6-1c-4-1-7-4-10-6l2-1c-4-3-7-7-11-10-1-2-2-4-3-5v-1c0-1-1-2-2-4h1l1 1h0v-2-1c-1-2-1-3 0-5l1-1 3-1-1-2h-1v-2-3c0-2-1-3-3-4v-1-2l9-2c2 0 2-1 4-1h1l-2-1-1-3c1-1 2-1 3-2 0-3-2-5-3-7h0z" class="X"></path><path d="M696 421l1-1c4 1 7 3 11 6-1 1-2 1-3 2l-9-7z" class="D"></path><path d="M747 419c0-1 2-1 2-1 1-1 1-2 1-3h0l1 1 2 2c1 0 2 1 2 2v1 1c1 0 1 1 1 1l-2 2c-3-1-5-4-7-6z" class="I"></path><path d="M789 424v-2c0-1 1-1 1-1l1 1h2l1 1h0c1-3-2-8-2-11 2 3 3 8 6 10l2-2 2 2-3 3v-1h-3-7z" class="b"></path><path d="M711 419c-1 0-2-1-3-1-1-1-1-1-1-3s0-3 2-4l1 1v2c1 0 1 1 2 1l1 1-1-1 1-1h1l1 1c-1 1 0 1-1 2l2 1 1-1c1 0 1 0 2 1l-3 3v-1c-2-1-4-1-5-1z" class="R"></path><path d="M724 418v-1c1 0 3 3 4 3 1 2 1 3 1 5-1 1 0 1-2 1 0 0-1 1-2 1l-1-1h0c-2-2-3-3-6-4h0c-3 0-5-1-7-3 1 0 3 0 5 1v1l3-3 1-1c2 1 2 1 4 1h0z" class="c"></path><path d="M720 417c2 1 2 1 4 1h0l3 5-1 1h-1v-2-1c-2-2-3-1-5-2v-2z" class="I"></path><path d="M719 418l1-1v2c2 1 3 0 5 2-2 1-2 2-3 2l-2-1-2-1v1h0c-3 0-5-1-7-3 1 0 3 0 5 1v1l3-3z" class="E"></path><path d="M755 421l2 2c1-1 1 0 1-1 2 0 2 1 3 2l1-1c-1-1-1-1-1-2h1c0-3-2-6-2-8h0 1v2c2 3 2 5 4 8l1 1c1-1 2-1 3-2v1h2 1l1 1v-1l-1-1 1-1c0 1 1 2 1 3l1-1c0-2-3-8-3-10 2 3 3 8 6 11h11 7 3v1c-4 1-9 1-13 1l-17-1c-5 0-10 1-15 0h0l2-2s0-1-1-1v-1z" class="S"></path><path d="M731 422c-1-1-2-2-2-3v-1l1-1 2 2 2 3v-1c0-1-1-1-1-2s0-2-1-2c0-1 0-2-1-2l1-1 5 6c1-1 1-2 3-2l1 1-1 2c1-1 2-1 3-2 1 0 2 0 3-1l1 1c2 2 4 5 7 6h0l-15 1h-1l-2-2v-1c-2 1-1 2-1 4h-1l-1-1s-1 0 0-1v-1l-2-2z" class="B"></path><path d="M746 418l1 1c2 2 4 5 7 6h0l-15 1h-1c1-2 3-5 6-6v2l-1 1h1l1-1 1-1c0 1 0 1 1 2v-1l-1-4z" class="P"></path><path d="M691 409h1v1h-1v1 1c-1 0-2 0-3 1v1c0 1-1 2-2 3v1l2 1v-1c1-1 0 0 0-1s1-3 2-4h1v1 2l1 1c1 0 2-2 4-3v1l-2 2 3 3-1 1c-1-1-2-1-3-1-1 1-2 2-4 3h-1c-2-1-5 1-6 3l-1-2h-1v-2-3c0-2-1-3-3-4v-1-2l9-2c2 0 2-1 4-1h1z" class="D"></path><path d="M694 417l3 3-1 1c-1-1-2-1-3-1-1 1-2 2-4 3h-1l1-2c2-1 3-2 5-4z" class="I"></path><path d="M686 410v1c-1 1-4 1-5 2 1 1 2 1 3 1 0 1-1 1-2 2l1 3 1 1v-1l1 1-2 2c0 1-1 1-2 2h0-1v-2-3c0-2-1-3-3-4v-1-2l9-2z" class="C"></path><path d="M677 414h4c2 1 1 4 1 6 0 1 0 2-1 3v1h0-1v-2-3c0-2-1-3-3-4v-1z" class="X"></path><path d="M783 402h30 3l11 1h2l-2 2h0c-4 2-9 1-12 1h-19l-47 1h-38c-3-1-8 0-11-2l-1-1h-1l1-1h7l15-1c6 1 14-1 20 0 1 1 2 1 4 0 1 0 2 0 3 1h11l22-1h2z" class="l"></path><path d="M809 411h0l1-1 6 3-1 1c-2 0-4 1-5 3v1c1 2 4 3 6 4 9 4 17 7 20 18-2 2-5 5-7 6h-2l-2-1-3 2 4 4-2 1c-2-2-5-5-8-6l-2-3c-3-2-5-5-6-7l-1-1c-1-1-1-2-1-4l-1-1c-1 1-2 2-2 3 0 2 0 2 1 4h-1c-2 0-1-2-2-3 0-1-1-1-1-1v-1l-3 1-1-2v-2h-2-8-4-54c-2-1-4 0-6 0v-1c2-1 2-1 2-2l1 1c1 0 2-1 2-1 2 0 1 0 2-1 0-2 0-3-1-5l2 1-1 1 1 1h1v-1l2 2v1c-1 1 0 1 0 1l1 1h1c0-2-1-3 1-4v1l2 2h1l15-1c5 1 10 0 15 0l17 1c4 0 9 0 13-1l3-3-2-2 9-9z" class="O"></path><path d="M803 426v-1h0 2v3l1-1c0 1 0 2 1 2 1 1 1 2 2 2v1c-1 1-3 0-3-1l-1-1-1-5-1 1z" class="X"></path><path d="M809 411v2c0 1-2 2-2 3-2 2-3 4-5 6l-2-2 9-9z" class="L"></path><path d="M803 426l1-1 1 5c-1 1-2 2-2 3 0 2 0 2 1 4h-1c-2 0-1-2-2-3 0-1-1-1-1-1v-1l-3 1-1-2v-2c1 0 2-1 3-1s3-1 4-2z" class="j"></path><path d="M796 429c1 0 2-1 3-1 0 1 1 3 1 4l-3 1-1-2v-2z" class="D"></path><defs><linearGradient id="S" x1="809.604" y1="433.101" x2="816.911" y2="432.626" xlink:href="#B"><stop offset="0" stop-color="#464647"></stop><stop offset="1" stop-color="#606061"></stop></linearGradient></defs><path fill="url(#S)" d="M809 431c2-2-1-1-1-4 1 0 1 0 2-1h1-2l-1-1h0c3 0 5 0 8 1 5 1 8 3 12 6 2 1 5 3 5 6v1c-1 1-3 1-5 1 0 1-1 2-2 2h-1c-2-1-3 0-5 0h4c1 1 1 2 1 3l-3 2 4 4-2 1c-2-2-5-5-8-6l-2-3c-3-2-5-5-6-7l-1-1c-1-1-1-2-1-4 0 1 2 2 3 1v-1z"></path><path d="M817 437h-3l-1-1c2 0 5 0 6-1v-1c-1 0-2-1-3-1s-1 0-1-1c2 0 2 0 4 1h0c2 1 2 1 3 2v1l6 4c-1 0-2 0-3-1h-1-2c-2 0-3-2-5-2z" class="g"></path><path d="M816 426c5 1 8 3 12 6-1 0-1 1-2 1-1-1-2-1-3-1-1-1-2-1-3-1l-1 1-6-3h8c-1-2-3-2-5-2h0v-1z" class="H"></path><path d="M817 437c2 0 3 2 5 2h2 1c1 1 2 1 3 1h0c0 1-1 2-2 2h-1c-2-1-3 0-5 0l-2 1v-1h-2 0c-1-1 0-1-1-1-1-1-1-1-2-1l1-1v-1h2l1-1z" class="c"></path><path d="M817 437c2 0 3 2 5 2h2 1c-1 0-2 0-4 1-2 0-5 0-7-1v-1h2l1-1z" class="k"></path><path d="M819 432l1-1c1 0 2 0 3 1 1 0 2 0 3 1 1 0 1-1 2-1 2 1 5 3 5 6v1c-1 1-3 1-5 1h0l-6-4v-1h2l-5-3z" class="N"></path><path d="M806 431c0 1 2 2 3 1l3 3-2 2c1 2 2 2 3 3h0c1 0 1 0 2 1 1 0 0 0 1 1h0 2v1l2-1h4c1 1 1 2 1 3l-3 2 4 4-2 1c-2-2-5-5-8-6l-2-3c-3-2-5-5-6-7l-1-1c-1-1-1-2-1-4z" class="B"></path><path d="M818 443l2-1h4c1 1 1 2 1 3l-3 2-3-2v-1h-1l-1-1h1z" class="e"></path><path d="M786 429h8 2v2l1 2 3-1v1s1 0 1 1c1 1 0 3 2 3h1c-1-2-1-2-1-4 0-1 1-2 2-3l1 1c0 2 0 3 1 4l1 1c1 2 3 5 6 7l2 3c3 1 6 4 8 6l4 4c-1 0-2 1-2 1-3 1-7 0-11 1l-2-1h-1-1l-1 1c-4 0-10 1-13 0h-2-1l-1-3c-1 1-2 2-2 3-3 1-9 1-12 1-1-1-1-1-1-2-1-1-1 0-1-1v1 1h0-1c-3-5-4-13-6-20h0c-1-1-1-2-2-3 0-1 0-2-1-2 0-1 1-2 1-3 4 0 12 1 14-1h4z" class="W"></path><path d="M792 437h3v2h0l-1 1h-3l1-3z" class="k"></path><path d="M784 434c1 1 2 0 2 2 1 1 2 2 2 4h1c0 1 0 1 1 2h2c0 1 1 1 1 2s1 1 2 1c-1 1-2 1-3 1s-1-1-1-2l-1 1h0c1 1 0 1 1 2h-1c-2-4-5-8-6-13z" class="P"></path><path d="M805 452l4 6h1c-4 0-10 1-13 0h-2-1l-1-3c-1 0-1-1-1-2 1 0 3 1 5 1 0 0 1 1 2 1s1 0 1 1h2c1 0 4 1 5 0-1-1-2-2-2-3v-1z" class="j"></path><path d="M790 448c0 1 0 2 1 3 3 2 10-2 13 1v1c-3 1-9-1-12 0 0 1 0 2 1 2-1 1-2 2-2 3 0-1 0 0-1-1l-2-7 2-2z" class="m"></path><path d="M786 429h8v2l-1 1-1 1 1 1-1 1c2 0 2 0 3 1h1l-1 1h-3l-1 3-1 1h2v1h-2c-1-1-1-1-1-2h-1c0-2-1-3-2-4 0-2-1-1-2-2l-1-1v-1-2c1 0 2 0 3-1z" class="b"></path><path d="M792 437h-1c-1-1-2-2-2-3 1 0 2 0 3 1 2 0 2 0 3 1h1l-1 1h-3z" class="i"></path><path d="M786 429h8v2l-1 1-1 1h0c-1 0-1-1-2-1l-1-1c-1 1 0 2-1 4-1 0-2-2-3-3l-1 1h-1v-1-2c1 0 2 0 3-1z" class="L"></path><path d="M794 429h2v2l1 2c2 4 5 9 8 14 2 2 4 4 6 7 2 1 2 3 4 4l-2-1h-1-1l-1 1h-1l-4-6c0-1-2-3-2-4-1-1-3-3-4-5s-1-4-2-6h-1v-1h-1c-1-1-1-1-3-1l1-1-1-1 1-1 1-1v-2z" class="G"></path><path d="M794 429h2v2c-1 0-1 1-1 1l2 5h-1v-1h-1c-1-1-1-1-3-1l1-1-1-1 1-1 1-1v-2z" class="Y"></path><path d="M805 430l1 1c0 2 0 3 1 4l1 1c1 2 3 5 6 7l2 3c3 1 6 4 8 6l4 4c-1 0-2 1-2 1-3 1-7 0-11 1-2-1-2-3-4-4-2-3-4-5-6-7-3-5-6-10-8-14l3-1v1s1 0 1 1c1 1 0 3 2 3h1c-1-2-1-2-1-4 0-1 1-2 2-3z" class="L"></path><path d="M800 433s1 0 1 1c1 1 0 3 2 3h1l1 1c-1 1-1 2-3 3 0-3-1-6-2-8z" class="P"></path><path d="M816 446c3 1 6 4 8 6l4 4c-1 0-2 1-2 1-4-2-7-5-10-7v-2-2z" class="n"></path><path d="M805 430l1 1c0 2 0 3 1 4l1 1c1 2 3 5 6 7l2 3v2 2h-1c0 1 4 3 4 4h-3-1c-1-3-2-4-4-5l-1-1c-4-1-5-4-8-7 2-1 2-2 3-3l-1-1c-1-2-1-2-1-4 0-1 1-2 2-3z" class="d"></path><path d="M815 450c-1-2-2-3-3-4 0-1 1-2 2-3l2 3v2 2h-1z" class="Z"></path><path d="M807 435l1 1-1 1c1 1 3 3 3 4 0 2 0 3 1 4h0c-1-1-3-2-3-3h1l-4-4c1-1 2-2 2-3h0z" class="H"></path><path d="M805 430l1 1c0 2 0 3 1 4h0c0 1-1 2-2 3l4 4h-1l-2-1h0c0 3 3 5 4 7-4-1-5-4-8-7 2-1 2-2 3-3l-1-1c-1-2-1-2-1-4 0-1 1-2 2-3z" class="W"></path><path d="M782 429h4c-1 1-2 1-3 1v2 1l1 1c1 5 4 9 6 13v1l-2 2 2 7c1 1 1 0 1 1-3 1-9 1-12 1-1-1-1-1-1-2-1-1-1 0-1-1v1 1h0-1c-3-5-4-13-6-20h0c-1-1-1-2-2-3 0-1 0-2-1-2 0-1 1-2 1-3 4 0 12 1 14-1z" class="m"></path><path d="M773 438c0-1 0-1-1-2v-1-1-2h4c1 1 2 1 2 2 1 1 1 1 1 2 1 1 1 1 1 3l1 1c-1 0-1 0-2 1h1l1 1v1c0 1 0 1 1 2h0c-1 0-1 0-2-1h-4l1-1h2v-1h-4v2c1 1 3 1 5 1l-1 1h-3v1c2 0 4 0 5 1h0l-1 1h0 2c1 1 0 0 1 2h0-2v1c-1 0-2 0-3 1h-1c-1-1-1-3-1-4l-3-11z" class="k"></path><path d="M782 429h4c-1 1-2 1-3 1v2 1l1 1c1 5 4 9 6 13v1l-2 2 2 7c1 1 1 0 1 1h-3c1-2 0-3 0-4l-3-9-1-2c0-1 0-1-1-2v1 1c1 1 1 2 1 3s1 3 1 4c1 1 0 1 0 2h-1v-2l-1-2c0-1 0-2-1-2v-1h0c-1-1-1-1-1-2v-1l-1-1h-1c1-1 1-1 2-1l-1-1c0-2 0-2-1-3 0-1 0-1-1-2 0-1-1-1-2-2h-4v2 1 1c1 1 1 1 1 2-1-1-2-3-3-5-1 1 0 4 0 5h0c-1-1-1-2-2-3 0-1 0-2-1-2 0-1 1-2 1-3 4 0 12 1 14-1z" class="S"></path><path d="M783 432v1l1 1c1 5 4 9 6 13v1l-2 2-4-13c-1-1-1-3-1-5z" class="J"></path><path d="M689 423c2-1 3-2 4-3 1 0 2 0 3 1l9 7c1-1 2-1 3-2 5 2 9 2 14 2v1c2 0 4-1 6 0h54c-2 2-10 1-14 1 0 1-1 2-1 3 1 0 1 1 1 2 1 1 1 2 2 3h0c2 7 3 15 6 20-2 2-10 1-12 1h-1c-1 0-2 1-3 1h-3-3 0c-2 1-8 0-10 0h-1c-4-1-9 0-13 0h-4-1l-16-1h-1c-1 1-2 1-3 2l1 2-6-1c-4-1-7-4-10-6l2-1c-4-3-7-7-11-10-1-2-2-4-3-5v-1c0-1-1-2-2-4h1l1 1h0v-2-1c-1-2-1-3 0-5l1-1 3-1c1-2 4-4 6-3h1z" class="f"></path><path d="M720 434c2-1 2 0 4 0h2l-1 3h-1v1h0l-1 1h-1l2 2h0c-1 1-2 1-3 1s-2 1-3 1l-1 1h1c-1 1-2 1-2 2s-1 1-1 2c-1 1-1 1-1 2-1 2-2 4-4 6l7-20c0-1 1-1 1-2h1 1z" class="L"></path><path d="M720 434c2-1 2 0 4 0h2l-1 3h-1v1h0-4v-1h2v-1c-2 0-2 0-2-2z" class="E"></path><path d="M728 432h2l-2 19c0 2 0 7-2 8v1c-2-1-3-1-4-1-2-1-3 0-4 0l-1-1s-2-1-3-1h-2c2-2 2-2 4-3 1 1 1 1 2 1l1-1c-1 0-2 0-3-1h0c3-2 5-5 6-8 1-1 1-3 3-4l-1 2v1l1 1 1-1c1-2 2-4 2-7h-1l-2 1h-1v-1h1l1-3h-2c1 0 3-1 4-1v-1z" class="C"></path><defs><linearGradient id="T" x1="733.131" y1="424.096" x2="740.369" y2="435.904" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#8c8d8e"></stop></linearGradient></defs><path fill="url(#T)" d="M708 426c5 2 9 2 14 2v1c2 0 4-1 6 0h54c-2 2-10 1-14 1-3 1-11 0-13 1h-2v3c0-2 0-2-1-3h-6l-1 2-1 1-1-2c-1-1-9 0-10 1-1 0-2 1-2 2 0 2 0 3-1 5 0 2 1 4 0 6s-1 3-1 5-1 5 0 7c1 1 0 1 1 1v1h-4-1 1v-1c2-1 2-6 2-8l2-19h-2v1c-1 0-3 1-4 1-2 0-2-1-4 0h-1-1c0 1-1 1-1 2l-7 20c0 1-1 2-1 3h0-1c0-1 0 0-1-1 0-2 1-3 2-5 3-7 6-14 8-22-3 0-5 0-8 1l-1-1h0c0-2-2-2-3-3 1-1 2-1 3-2z"></path><path d="M722 429c2 0 4-1 6 0-2 0-4 0-6 1h-10l2-1h0 8z" class="S"></path><path d="M717 436c0-2 0-3 1-5 3 0 7 0 10 1v1c-1 0-3 1-4 1-2 0-2-1-4 0h-1-1c0 1-1 1-1 2z" class="O"></path><path d="M708 426c5 2 9 2 14 2v1h-8 0l-2 1c-1 0-2 0-4 1 0-2-2-2-3-3 1-1 2-1 3-2z" class="Y"></path><path d="M709 432c3-1 5-1 8-1-2 8-5 15-8 22-1 2-2 3-2 5 1 1 1 0 1 1-1 1-2 1-3 2l1 2-6-1c-4-1-7-4-10-6l2-1 1-1 2 1c0-1 1-1 1-2h1 0l3-5 1-1c0-2 1-3 2-4 1 0 0 0 1-1h-4v-1c2-3 5-6 9-9z" class="B"></path><path d="M707 443c1 0 1 1 1 1 0 2-1 3-2 4-1 0-2-1-2-1v-1l2-2 1-1z" class="D"></path><path d="M697 453l3-5c1 1 1 2 2 3 2-1 1-2 3-1l-1 1h0v4h0 1c1-1 0-1 1-1l1 1h0c0-1 1-1 2-2-1 2-2 3-2 5 1 1 1 0 1 1-1 1-2 1-3 2l1 2-6-1c-4-1-7-4-10-6l2-1 1-1 2 1c0-1 1-1 1-2h1 0z" class="Q"></path><path d="M697 453c1 2 2 3 3 3 1-1 1-1 1-3h1c0 2 1 4 2 5 1 0 1 0 1 1h-1c-3 0-6-1-8-3l1-3h0z" class="U"></path><path d="M693 454l2 1c0-1 1-1 1-2h1l-1 3c2 2 5 3 8 3h1c1 0 1-1 2-1 1 1 1 0 1 1-1 1-2 1-3 2l1 2-6-1c-4-1-7-4-10-6l2-1 1-1z" class="N"></path><path d="M730 440c1-2 1-3 1-5 0-1 1-2 2-2 1-1 9-2 10-1l1 2 1 26h-1-1c-4-1-9 0-13 0v-1c-1 0 0 0-1-1-1-2 0-5 0-7s0-3 1-5 0-4 0-6z" class="f"></path><path d="M739 443v3c-1 1-1 1-3 2-1 0-1 0-2-1l5-4z" class="Q"></path><path d="M730 459h4l1-2c1 0 2 0 3 1h1 1c1 0 2 0 2 1l1 1c-4-1-9 0-13 0v-1z" class="L"></path><path d="M730 440h1c1-2 1-3 1-5l1-1c1 1 1 0 2 1 2-1 2-2 4-2 1 1 1 2 1 3v1h-2 0c1 1 1 1 2 1l1 1c-1 2-1 3-2 4l-5 4-2 2h0c1-3 4-5 6-7h-1l-3 3h0c-2 2-3 4-5 6 0-2 0-3 1-5s0-4 0-6z" class="S"></path><path d="M689 423c2-1 3-2 4-3 1 0 2 0 3 1l9 7c1 1 3 1 3 3h0l1 1c-4 3-7 6-9 9v1h4c-1 1 0 1-1 1-1 1-2 2-2 4l-1 1-3 5h0-1c0 1-1 1-1 2l-2-1-1 1c-4-3-7-7-11-10-1-2-2-4-3-5v-1c0-1-1-2-2-4h1l1 1h0v-2-1c-1-2-1-3 0-5l1-1 3-1c1-2 4-4 6-3h1z" class="f"></path><path d="M688 433c-2-2-5-3-6-5 3 0 5 3 7 4h1c2 1 7 7 8 9 3-4 7-7 10-10l1 1c-4 3-7 6-9 9v1h4c-1 1 0 1-1 1-1 1-2 2-2 4l-1 1-3 5h0-1c0 1-1 1-1 2l-2-1c1-1 2-2 3-4 1-1 1-3 2-5l-10-12z" class="H"></path><path d="M682 426c1-2 4-4 6-3h1c-1 1-3 2-3 4 2 1 3-1 4 0l-1 1c0 1-1 1 0 1v1l1 2h-1c-2-1-4-4-7-4 1 2 4 3 6 5h0c-1 0-2-1-3-1h-1c1 1 1 2 3 2-1 2-1 2 0 4s3 3 4 4v1c1 1 1 1 1 2h0c0 2 1 3 2 4 0 2 0 2-1 3-6-5-11-12-15-19-1-2-1-3 0-5l1-1 3-1z" class="B"></path><path d="M682 426c1-2 4-4 6-3l-10 7c3 4 8 13 13 15h1c0 2 1 3 2 4 0 2 0 2-1 3-6-5-11-12-15-19-1-2-1-3 0-5l1-1 3-1z" class="H"></path><path d="M689 423c2-1 3-2 4-3 1 0 2 0 3 1l9 7c1 1 3 1 3 3h0c-3 3-7 6-10 10-1-2-6-8-8-9l-1-2v-1c-1 0 0 0 0-1l1-1c-1-1-2 1-4 0 0-2 2-3 3-4z" class="R"></path><path d="M755 431c2-1 10 0 13-1 0 1-1 2-1 3 1 0 1 1 1 2 1 1 1 2 2 3h0c2 7 3 15 6 20-2 2-10 1-12 1h-1c-1 0-2 1-3 1h-3-3 0c-2 1-8 0-10 0h1l-1-26 1-1 1-2h6c1 1 1 1 1 3v-3h2z" class="n"></path><path d="M758 443h2v1c-1 2-1 3-1 5v-1h1l1 2 1 1v1h0l-3 2-1-5v-1-5z" class="G"></path><path d="M765 434l1 1h2c1 1 1 2 2 3h0c2 7 3 15 6 20-2 2-10 1-12 1 2-1 7 0 10-1-1-2-1-5-2-7 0-2-1-4-1-6l-1-4c-1-1-1-2-1-3h-4v-4z" class="g"></path><path d="M758 442c0-3-1-6 0-9l1-1 1 1v1 3c0 1 0 1 1 2l1-1v2c1 0 1-1 1-1 0-1 1-2 1-2v-4l1 1v4h4c0 1 0 2 1 3l1 4h-1c-1 0-1 0-2-1h1v-1h-3l-1 1-1-1v-2l-1-1v2 1l-1 1c0-2 0-2-1-3-1 1-2 1-3 1z" class="F"></path><path d="M755 431c2-1 10 0 13-1 0 1-1 2-1 3 1 0 1 1 1 2h-2l-1-1-1-1v4s-1 1-1 2c0 0 0 1-1 1v-2l-1 1c-1-1-1-1-1-2v-3-1l-1-1-1 1c-1 3 0 6 0 9v1 5 1l1 5c1 2 1 3 2 5h2c-1 0-2 1-3 1-2-5-2-11-3-15-1 0-2 0-3 1l-1-8h0v-4-3h2z" class="i"></path><path d="M753 434v-3h2l1 5c-2 1-2 1-3 2h0v-4z" class="M"></path><path d="M753 438c1-1 1-1 3-2l1 9c-1 0-2 0-3 1l-1-8z" class="G"></path><path d="M746 431h6c1 1 1 1 1 3v4h0l1 8c1-1 2-1 3-1 1 4 1 10 3 15h-3-3 0c-2 1-8 0-10 0h1l-1-26 1-1 1-2z" class="N"></path><path d="M745 433l1-2c0 1 1 3 1 4 2 1 3 1 2 4 0 0-1 0-1 1h3 1l1 4-6 2c-1-2-1-4-1-6s-1-5-1-7z" class="i"></path><path d="M746 431h6c1 1 1 1 1 3v4h0l1 8v3c0-1-1-1-1-2v-3l-1-4h-1-3c0-1 1-1 1-1 1-3 0-3-2-4 0-1-1-3-1-4z" class="L"></path><path d="M754 446c1-1 2-1 3-1 1 4 1 10 3 15h-3-3 0c-2 1-8 0-10 0h1c1-2 0-5 0-7 2 0 2 0 4-1 1-1 4 0 6-1 0-1 0-1-1-2v-3z" class="n"></path><path d="M769 139h8 3 4c9 1 18 0 28 0 3 0 7 1 11 1v3c7 0 14-1 21 0h0c1 1 1 0 2 0v-2l4 7c-2 1-6 0-9 0-1 0-1 1-2 1-1 2 0 6-2 7l-2-1c0-1 0-2 1-3v2h1l1-2-2-2c-2 0-2 0-3 2v3c1 1 1 2 1 3 2 3 4 3 4 6h0-2-2-2l-1-1c-1-1-1-1-3-1v1l-1 1c-1-1-1-1-3-1h0-2v1h0-2c1 2 2 3 3 5v-1l1-1v1l1 1c1 1 3 4 3 6 1 2 2 3 3 5l-9-1c-2 1-5 0-7 1 1 1 0 1 1 1 1-1 2 0 3 0-3 0-5 0-7 2 0 1 0 2-1 3l1 1 1 3c0 2 1 3 0 5l-1 2v1c-1-1-1-1-1-2h-2c-1 2-2 3-3 4-2 2-5 4-8 6l-1-2-2 3c1 1 2 3 2 5h0 0c-2 1-2 1-3 0-5 1-9 6-13 9l-3 2v2l-1-1h0l-1-3c-1 0-1 0-2 1v2 1 3 2c0 3-1 5-1 8 0 1 0 2-1 3v-5h-1c0 4 1 8 0 13v4h0l-1 1-2 1c0 1 0 1-1 2-1 0-2 0-3-1-2-1-4-1-5-2v-1l-1 1c-1 0-3 0-4 1h-1l-2-3c0-1-1-2-2-3l-1-2-2 2h0 0l-1-1-2-2c0-1-1-1-2-2h0c0-1-1-2-2-3v1c-1-2-2-6-3-7s-1 0-2-2-2-4-3-5c-1-3-1-5-1-8l-1-22v-1c-1-9 0-18 1-27 0-4-1-9 0-13v-10h-1l-1-1h-10v-1c-2 1-4 1-5 1 0-2 0-2 1-2 3 0 24 0 26-1h19l7-1c2 1 8 0 11 0v-1h-5l-3-1h-1 0l1-1z" class="R"></path><path d="M766 236h1 0c-2 2-6 2-8 3h-1l-1-2 1-1h8z" class="L"></path><path d="M754 238c1 1 2 2 4 2 0 0 1-1 2-1h2l2-1c1 0 1 0 2 1h1v3h-2c-3 0-4 0-6-1h-1c-1 1-1 2-2 3-1-2-1-4-2-6z" class="B"></path><path d="M767 156c-2-2-3-1-5-2l1-1h1c-1-1-1-1-2-1v-1h-1-1v-1h1c1-1 2-1 4-1 1 0 3 1 4-1h1 0v2c-1 1-1 3-1 4-1 1-2 0-2 2z" class="I"></path><path d="M783 158l1-2 1 1v1h1c0-1 0 0 1-1h1 0c-1-1 0-1-1-1 1-1 0-1 2-1v1 1c1 1 1 1 1 2v3h0-11l-2 1h-1l1-1h1c1-1 7-1 9-1h0-4-1-2c-1 0-2 0-3-1v-1h6v-1z" class="C"></path><defs><linearGradient id="U" x1="765.427" y1="247.878" x2="760.498" y2="250.394" xlink:href="#B"><stop offset="0" stop-color="#0c0d0e"></stop><stop offset="1" stop-color="#28292a"></stop></linearGradient></defs><path fill="url(#U)" d="M756 244c1-1 1-2 2-3v3c1 0 1 0 2-1l1 2h4c0 1 0 1-1 1l3 3v-3h1c0 1-1 2 0 4v5c0 1 0 1-1 2-4-2-9-10-11-13z"></path><path d="M791 151c2-2 3-1 4-2v1h4 1l1 1c1 0 1 1 1 1-3-1-7 0-11 0l1 8-2 2v-3c0-1 0-1-1-2v-1-1c-2 0-1 0-2 1 1 0 0 0 1 1h0-1c-1 1-1 0-1 1h-1v-1l-1-1-1 2c-2 0-4 1-6 0l1-1h4l1-1h-5l-1-1 3-1h-3v-1c2 0 4-1 6 0 1 0 1 0 2-1 0 1 1 1 2 1h4v-2z" class="D"></path><path d="M792 160l-1-8c4 0 8-1 11 0v2c0 1-1 3 0 5v1h-1l-1 1c-1-1-1-1-2-1h-5-1z" class="G"></path><path d="M793 154h1c1 0 5 0 6 1v2l-1 1v-1l-1-1h-1l1 2h-2v-1-1h-1c-1 0-1-1-2-2z" class="l"></path><path d="M777 163l2-1h11v5l-1 1-1-1-1 1h0-3l1 1h0l1 1h-1c0 1-1 1-1 2l-1 1v-1c-2 1-5 1-6 1l-1-2h0s1-1 2-1l-1-1v-2l-1-1v-3h1z" class="T"></path><path d="M776 163h1l1 1h0c1 0 2 1 3 1s1 0 2-1c0 0 1 0 1 1v1c1 0 1-1 2 1h1 1l-1 1-1-1h-1-6-2l-1-1v-3z" class="H"></path><path d="M779 167h6 1l1 1h0-3l1 1h0l1 1h-1c0 1-1 1-1 2l-1 1v-1c-2 1-5 1-6 1l-1-2h0s1-1 2-1l-1-1v-2h2z" class="L"></path><path d="M777 167h2v2h3l1 1v1c-2 0-5-1-7 0h0s1-1 2-1l-1-1v-2z" class="Q"></path><path d="M758 178v-1c-1-1 0-2 0-3 2 2 6 1 8 1h2l1 19c-1 1-2 0-3 1-2 1-2 1-4 1v-1h-1c-1 0-2 0-3-1v-3l1-1c1-2 3-3 5-4h1-1-1c-2-1-2-1-3-1l-1 1-1-1v-2l2-2c-1 0-1 0-2-1h0l1-1c2-1 6 0 8 0l1-1h-1c-2 0-6 1-8 0h-1z" class="K"></path><path d="M758 178v-1c-1-1 0-2 0-3 2 2 6 1 8 1v1c-2 0-5 0-6 1h-1l-1 1z" class="B"></path><path d="M769 139h8 3 4c9 1 18 0 28 0 3 0 7 1 11 1v3h-34-30l7-1c2 1 8 0 11 0v-1h-5l-3-1h-1 0l1-1z" class="b"></path><path d="M777 141c5-2 12-1 17-1v1h-9l-1 1h6l-1 1h-30l7-1c2 1 8 0 11 0v-1z" class="k"></path><path d="M767 156c0-2 1-1 2-2 0-1 0-3 1-4 2 2 1 27 1 32l-1 1v25c0 3 1 7 0 10h-1v-12c-1-2-1-2-2-3s-1-1 0-3h-3c-2 0-4 1-6-1-1-2 0-3 0-5 1 1 2 1 3 1h1v1c2 0 2 0 4-1 1-1 2 0 3-1l-1-19h-2c-2 0-6 1-8-1 3-1 6 1 9-1v-3h-2c-1 0-2 1-4 0h0c-1 1-2 1-3 0 0-1 0-1 1-2 1 0 2 0 4 1h0c1-1 2-1 4-1 0-1 0-2-1-3h-1c-1-1-4-1-6 0h0l-1-1h0c2-1 6-1 9-1v-1c-3-1-7 1-9-1v-1h3v-3l1-1v1 1 1l1 1h3l1 1v-1h-1l1-1v-3z" class="D"></path><path d="M764 200h3c-1 2-1 2 0 3s1 1 2 3v12h1 0c0 1 0 2 1 3v4 11c0 4 1 8 0 13 0-1-1-1-1-1-1-1 0-6 0-8-1 1-1 1-1 2l-1-1v-11-3-1h-7c-2 0-1 1-3 0 0-1 0-3 1-5h-1c0-2 0-3 1-4 2-1 3 0 5 0l2-2v-1c-2-2-5 0-7-1-1-1-1-1-1-2 2-2 4-1 6-1v-1h-5s0-1-1-1c0-1-1-3 0-4 2-1 4-1 7-1v-2l-1-1z" class="K"></path><path d="M791 151v-1l1-1h1c1-1 0-1 2-1 2-1 5 0 7 0l13-1c2 0 5-1 8 0 1 0 1 0 2 1h1 1c1-1 2-1 3-1h-1c2-1 5-1 8-1v1c-2 0-4 0-6 1l1 1 2 1c1-1 1-1 2-1s1 0 2 1h0v-1l1-2 2 1c-1 0-1 1-2 1-1 2 0 6-2 7l-2-1c0-1 0-2 1-3v2h1l1-2-2-2c-2 0-2 0-3 2v3c1 1 1 2 1 3 2 3 4 3 4 6h0-2-2-2l-1-1c-1-1-1-1-3-1v1l-1 1c-1-1-1-1-3-1h0-2v1h0-2l-2-4c-4-2-10-1-15-1h-1c-1-2 0-4 0-5v-2s0-1-1-1l-1-1h-1-4v-1c-1 1-2 0-4 2z" class="O"></path><path d="M803 154l17-1c-1 1-3 2-4 3-4-1-8-1-12-1l-1-1z" class="W"></path><path d="M820 153l2-1 1 2v-2h1c0 1 1 3 1 4l1-2v3l-10-1c1-1 3-2 4-3z" class="H"></path><path d="M826 154v-1l1-1c1-1 2-1 2-2 1 2 0 4 1 6h0 2l1-1c1 1 1 2 1 3 2 3 4 3 4 6h0-2c-1-1-2-3-4-4h0c-2-1-3-2-5-2l-1-1v-3z" class="d"></path><path d="M833 155c1 1 1 2 1 3 2 3 4 3 4 6h0c-2-3-5-4-8-6v-2h0 2l1-1z" class="R"></path><path d="M802 154h1l1 1c4 0 8 0 12 1l10 1 1 1c2 0 3 1 5 2h0c2 1 3 3 4 4h-2-2l-1-1c-1-1-1-1-3-1v1l-1 1c-1-1-1-1-3-1h0-2v1h0-2l-2-4c-4-2-10-1-15-1h-1c-1-2 0-4 0-5z" class="B"></path><path d="M772 236v-10c-1-2-1-4-1-5-1-2 0-7 0-9v-25c1-2 0-4 0-5v-14c0-1 0-4 1-6h0l-1-1c0-3-1-9 1-12h1v5c0 8 0 15 1 23 0 7-1 15 0 22h1v-6c0-1-1-3-1-5l1 1c4 0 7 0 11-1h1 1v1l1-1v-1h1c4-1 9 0 13 0h1c2 0 5-1 7 0h1l1 3c0 2 1 3 0 5l-1 2v1c-1-1-1-1-1-2h-2c-1 2-2 3-3 4-2 2-5 4-8 6l-1-2-2 3c1 1 2 3 2 5h0 0c-2 1-2 1-3 0-5 1-9 6-13 9l-3 2v2l-1-1h0l-1-3c-1 0-1 0-2 1v2 1 3 2c0 3-1 5-1 8 0 1 0 2-1 3v-5z" class="Q"></path><path d="M794 201h5c2-1 5-1 7-1-2 2-5 4-8 6l-1-2c-1 0-2-2-3-2h-1c-1 0-1-1-2-2 1 0 2 0 3 1z" class="P"></path><path d="M787 197h2l2 1h8l-1 1c-2 0-2 0-4 2-1-1-2-1-3-1 1 1 1 2 2 2h1c1 0 2 2 3 2l-2 3c-2-4-6-6-8-10z" class="C"></path><path d="M773 238v-25h1l2 1c1 0 1 0 2-1v3 1l-2-1v2 3c-1 0-1 0-2 1v2 1 3 2c0 3-1 5-1 8z" class="O"></path><path d="M797 212c-1-1-2-3-2-4l-3-3-4-1c-2 1-5 0-7 0l-3 1v-1c2 0 4 0 6-2v-2c0-1 1-2 2-3h1c2 4 6 6 8 10 1 1 2 3 2 5h0z" class="m"></path><path d="M781 211h-1c-2 0-3-1-4-1h-1v-2c2-2 6-2 8-2 3 0 6 0 9 2l-1 1-1-1-1 1v1h0c-1 1-1 1-1 2l-2-1-2-2-1 1h-2v1z" class="J"></path><defs><linearGradient id="V" x1="804.861" y1="199.383" x2="800.745" y2="191.307" xlink:href="#B"><stop offset="0" stop-color="#141418"></stop><stop offset="1" stop-color="#2c2c2a"></stop></linearGradient></defs><path fill="url(#V)" d="M813 190c0 2 1 3 0 5l-1 2v1c-1-1-1-1-1-2h-2c-7-1-14 0-20 0l-1-1c-1 1-1 1-2 0h-1l-1 1c-1 0 0-1-1 0v2h-1v-1h-1c-2 0-4 0-5 1l-1-1c2-1 1-1 2-3l1 1c2-1 4-1 7-1h0 2 10c2 0 3 0 5-1 1 0 1 0 2-1h7c1 0 1-1 2-2z"></path><path d="M802 193c1 0 1 0 2-1h7v2l-9-1z" class="k"></path><path d="M792 208l2 3v1c-5 1-9 6-13 9l-3 2v2l-1-1h0l-1-3v-3-2l2 1h2l1-1c-1-1-1-2-1-3l2-1h0l-1-1v-1h2l1-1 2 2 2 1c0-1 0-1 1-2h0v-1l1-1 1 1 1-1z" class="T"></path><path d="M783 210l1-1 2 2 2 1h1c0 1 0 1-1 1l-3 2c-1-1-1 0-1-1 0-2 0-2-1-4z" class="M"></path><path d="M781 211v-1h2c1 2 1 2 1 4l-4 5c1 0 2 0 2-1v1c0 1-1 2-2 3-1 0-1 1-2 1v2l-1-1h0l-1-3v-3-2l2 1h2l1-1c-1-1-1-2-1-3l2-1h0l-1-1z" class="E"></path><defs><linearGradient id="W" x1="809.309" y1="188.913" x2="795.942" y2="193.335" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#5a595b"></stop></linearGradient></defs><path fill="url(#W)" d="M804 187c2 0 5-1 7 0h1l1 3c-1 1-1 2-2 2h-7c-1 1-1 1-2 1-2 1-3 1-5 1h-10-2 0c-3 0-5 0-7 1l-1-1s1 0 1-1c2 0 3 0 5-1-2 0-5 1-7 0v-1l-1-2c4 0 7 0 11-1h1 1v1l1-1v-1h1c4-1 9 0 13 0h1z"></path><path d="M787 188h1v1l1-1h1c0 2-1 2-1 3-4 1-9 1-13 0l-1-2c4 0 7 0 11-1h1z" class="N"></path><path d="M804 187c2 0 5-1 7 0 0 1 1 2 0 3h-5 0c-5 0-10 0-14 1h-3c0-1 1-1 1-3h-1v-1h1c4-1 9 0 13 0h1z" class="a"></path><path d="M789 187h1l2 1v3h-3c0-1 1-1 1-3h-1v-1z" class="J"></path><g class="F"><path d="M804 187c2 0 5-1 7 0 0 1 1 2 0 3h-5 0v-1c-1 0-1 0-2-1h1 1l1-1h-3 0z"></path><path d="M802 159h1c5 0 11-1 15 1l2 4c1 2 2 3 3 5v-1l1-1v1l1 1c1 1 3 4 3 6 1 2 2 3 3 5l-9-1c-2 1-5 0-7 1 1 1 0 1 1 1 1-1 2 0 3 0-3 0-5 0-7 2 0 1 0 2-1 3l1 1h-1c-2-1-5 0-7 0h-1c-4 0-9-1-13 0h-1v1l-1 1v-1h-1-1c-4 1-7 1-11 1l-1-1 1-3-1-1s0-1 1-1h0c0-2 1-4 0-6v-1c0-1-1-2 0-2 0-2 0-2 1-3h0l1 2c1 0 4 0 6-1v1l1-1c0-1 1-1 1-2h1l-1-1h0l-1-1h3 0l1-1 1 1 1-1v-5h0l2-2h1 5c1 0 1 0 2 1l1-1h1v-1z"></path></g><path d="M776 177h6v1l-1 1h0l2 1h-6c0-1-1-2-1-3z" class="b"></path><path d="M781 184l2-2v1c0 1 0 2 1 2h1c1 1 2 2 2 3h-1-3c-1 0-1 0-2-1v-2-1z" class="B"></path><path d="M816 178h1v1h5c-2 1-5 0-7 1 1 1 0 1 1 1 1-1 2 0 3 0-3 0-5 0-7 2 0 1 0 2-1 3 0-1-1-2-2-2v-2h-4c2-1 5 0 7-1 1-1 3-3 4-3z" class="c"></path><defs><linearGradient id="X" x1="798.253" y1="172.753" x2="794.143" y2="182.492" xlink:href="#B"><stop offset="0" stop-color="#3a3b38"></stop><stop offset="1" stop-color="#4f4e51"></stop></linearGradient></defs><path fill="url(#X)" d="M782 177h30 2c0 1-1 1-1 1h-2 0c-2 2-7 1-9 1-6 0-13 1-19 1l-2-1h0l1-1v-1z"></path><path d="M789 187c0-1 1-3 1-4s0-1 1-2c3 1 6 1 10 1h4 4v2c1 0 2 1 2 2l1 1h-1c-2-1-5 0-7 0h-1c-4 0-9-1-13 0h-1z" class="Q"></path><path d="M795 183c1 0 2 1 4 1h4l-1 1h-3-7l-1-1 1-1h3z" class="I"></path><path d="M801 182h4 4v2c1 0 2 1 2 2l1 1h-1c-2-1-5 0-7 0h-1l-4-2h3l1-1h-4c-2 0-3-1-4-1 1-1 4-1 6-1z" class="K"></path><path d="M774 188l1-3-1-1s0-1 1-1h0c0-2 1-4 0-6v-1c0-1-1-2 0-2 0-2 0-2 1-3h0l1 2c1 0 4 0 6-1v1c8 0 17-1 25-1h8l-1 2c-1 1-3 1-4 1l-1 1h-14-19l-1 1c0 1 1 2 1 3l-1 1h2v1h-2l1 1c1 0 1 0 3-1h1c-2 2-2 2-4 2l1 1c1 0 2 0 3-1v1 2c1 1 1 1 2 1h3c-4 1-7 1-11 1l-1-1z" class="E"></path><path d="M802 159h1c5 0 11-1 15 1l2 4c1 2 2 3 3 5v-1l1-1v1l1 1c1 1 3 4 3 6 1 2 2 3 3 5l-9-1h-5v-1h-1l1-1h1c1 0 2-1 3-2h-1l1-1v-1c-1 1-2 2-4 3v-1h-1l-1-1 1-2h-8c-8 0-17 1-25 1l1-1c0-1 1-1 1-2h1l-1-1h0l-1-1h3 0l1-1 1 1 1-1v-5h0l2-2h1 5c1 0 1 0 2 1l1-1h1v-1z" class="N"></path><path d="M811 166h3c1 1 1 2 1 3h-2c-1-1-2-1-2-3z" class="Y"></path><path d="M802 159h1c3 2 8 1 12 2-2 2-2 3-5 4-2 0-5-1-7 0-1 0 0 1-1 2v-7-1z" class="D"></path><path d="M821 175h0c1-1 1-1 1-2s0-2 1-2h0c1 1 1 2 0 4v1h1v-1c0-2 0-3 1-4 1 0 1 2 2 3 0 1 0 1 1 1 1 2 2 3 3 5l-9-1h-5v-1h-1l1-1h1c1 0 2-1 3-2z" class="W"></path><path d="M801 160h1v7c1-1 0-2 1-2 2-1 5 0 7 0v1h1c0 2 1 2 2 3h2l-1 1c-9 2-20 1-30 2 0-1 1-1 1-2h1l-1-1h0l-1-1h3 0l1-1 1 1 1-1v-5h0l2-2h1 5c1 0 1 0 2 1l1-1z" class="Q"></path><path d="M801 160h1v7c1-1 0-2 1-2 2-1 5 0 7 0v1c-2 0-5 0-7 1l-1 3h-7c-1 0-5 1-6 0 0-1 0-1 1-2h0 3 9l-1-8z" class="U"></path><path d="M792 160h1 5c1 0 1 0 2 1l1-1 1 8h-9-3-1l1-1v-5h0l2-2z" class="Z"></path><path d="M792 160h1c0 3 1 5 0 8h-3-1l1-1v-5h0l2-2z" class="j"></path><path d="M730 147l1-1 1 6v-1h0l1-4h0l1 11c1-3 1-7 2-10h0v3l1 6v-1l1-8 1-1h0l1 4 1-4h1v1l1-1h0c1 0 2 0 2 1l1-1h1c0 2 0 6 1 7v-8c1 2 1 4 2 6l1-5h0l1 5v-1c1-1 1-3 1-4l1 80v11c1 2 1 4 2 6 2 3 7 11 11 13-1 0-2 0-3-1-2-1-4-1-5-2v-1l-1 1c-1 0-3 0-4 1h-1l-2-3c0-1-1-2-2-3l-1-2-2 2h0 0l-1-1-2-2c0-1-1-1-2-2h0c0-1-1-2-2-3v1c-1-2-2-6-3-7s-1 0-2-2-2-4-3-5c-1-3-1-5-1-8l-1-22v-1c-1-9 0-18 1-27 0-4-1-9 0-13v-10z" class="V"></path><path d="M752 237c0-4 1-8 1-11 0 2-1 9 1 11v-9-1 11c1 2 1 4 2 6 2 3 7 11 11 13-1 0-2 0-3-1-2-1-4-1-5-2v-1l-1 1c-1 0-3 0-4 1h-1l-2-3c0-1-1-2-2-3 1 0 2 0 2-1-1 0-1-1-2-2 0-2-1-4-2-6v-7c2 1 2 2 2 5 1-1 1-2 1-3h1l1 2z" class="P"></path><path d="M749 246l2-1c0 1 0 1 1 1v-1h1v1h1c1 1 1 2 1 4h0-1l-3-2c-1 0-1-1-2-2z" class="b"></path><path d="M749 238c1-1 1-2 1-3h1l1 2v4h-1v3c-1-2-2-5-2-6z" class="H"></path><path d="M754 246v-3h0 0c3 5 7 9 10 13-2-1-4-1-5-2v-1l-1 1c-1 0-3 0-4 1h-1l-2-3c0-1-1-2-2-3 1 0 2 0 2-1l3 2h1 0c0-2 0-3-1-4z" class="c"></path><path d="M748 154v-8c1 2 1 4 2 6l1-5h0l1 5c-1 5 0 12 0 18v47c0 3 0 8-1 10v4-1c-1 1-1 2-1 3 0-3 0-5-1-8l-2-1v-6s0-1 1-2h0v-13-23-16c0-3 1-7 0-10z" class="G"></path><path d="M746 147h1c0 2 0 6 1 7 1 3 0 7 0 10v16 23 13h0c-1 1-1 2-1 2-1 3-2 6-1 9 1 1 1 4 1 6v7c1 2 2 4 2 6 1 1 1 2 2 2 0 1-1 1-2 1l-1-2c-2-2-2-4-3-7 0-1 0-3-1-3v-1l-2-2c-1 0-1 0-1-1h-1c0-2-1-3-1-4l1-3c0 1 1 1 2 2v-2h0v-1-13c0 1 0 2 1 3 0-2 0-5 1-7v-1-23l-1-33c-1-1-1-1-1-3l1-1h0c1 0 2 0 2 1l1-1z" class="L"></path><path d="M742 148l1-1h0c1 0 2 0 2 1s1 5 0 6c-1-1-1-2-2-3s-1-1-1-3z" class="K"></path><path d="M742 212c0 1 0 2 1 3 0-2 0-5 1-7v-1 24c-1-2-2-3-2-5v-1-13z" class="G"></path><path d="M746 147h1c0 2 0 6 1 7 1 3 0 7 0 10v16 23 13h0c-1 1-1 2-1 2-1 3-2 6-1 9v2l-1 1 1-83z" class="N"></path><path d="M738 148l1-1h0l1 4 1-4h1v1c0 2 0 2 1 3l1 33v23 1c-1 2-1 5-1 7-1-1-1-2-1-3v13 1h0v2c-1-1-2-1-2-2l-1 3-1-3c-1-3-1-7-1-10v-21-39l1-8z" class="J"></path><path d="M739 154l2-1v7 17c-1-2-1-4-1-6 0-5 1-12-1-17z" class="o"></path><path d="M742 212v-22c0-6 0-11 1-17l1 11v23 1c-1 2-1 5-1 7-1-1-1-2-1-3z" class="n"></path><path d="M738 148c2 2 0 4 1 6 2 5 1 12 1 17 0 2 0 4 1 6v48l-2-1c0 1 0 2-1 2-1-3-1-7-1-10v-21-39l1-8z" class="l"></path><path d="M730 147l1-1 1 6v-1h0l1-4h0l1 11c1-3 1-7 2-10h0v3l1 6v-1 39 21c0 3 0 7 1 10l1 3c0 1 1 2 1 4h1c0 1 0 1 1 1l2 2v1c1 0 1 2 1 3 1 3 1 5 3 7l-2 2h0 0l-1-1-2-2c0-1-1-1-2-2h0c0-1-1-2-2-3v1c-1-2-2-6-3-7s-1 0-2-2-2-4-3-5c-1-3-1-5-1-8l-1-22v-1c-1-9 0-18 1-27 0-4-1-9 0-13v-10z" class="o"></path><path d="M736 151l1 6v-1 39 21c0 3 0 7 1 10l1 3c0 1 1 2 1 4h1c0 1 0 1 1 1l2 2v1c1 0 1 2 1 3 1 3 1 5 3 7l-2 2h0 0l-1-1-2-2c0-1-1-1-2-2h0c0-1-1-2-2-3-2-5-4-12-4-17l1 1h0v-40c0-11-1-23 0-34z" class="K"></path><path d="M730 147l1-1 1 6v-1h0c1 1 1 1 0 2v6 12 17 34l3 1v1c0 5 2 12 4 17v1c-1-2-2-6-3-7s-1 0-2-2-2-4-3-5c-1-3-1-5-1-8l-1-22v-1c-1-9 0-18 1-27 0-4-1-9 0-13v-10z" class="F"></path><path d="M729 197l1-9v1l1 22v10l-1-1-1-22v-1z" class="G"></path><path d="M571 754h4c-2 2-3 3-4 5-2 4-3 8-5 11-2 4-6 6-9 9-1 1-2 2-3 2h-5l-1 3h0c1 1 0 3 0 4v2c1 2 0 3 0 6v1 1 4c1 3 1 7 0 9v3h0c-1 2-1 3-1 5v2l-1 3c-1 1-1 2-1 3s-1 1-2 2l-1 1v1 1 2h0v1l-1 1-1 1c0 1-1 1-1 2v3l1 2c0 3 1 5 2 7 4 9 9 17 15 25h1 0c6 8 16 15 25 20 21 12 45 19 68 28l-42 4-69 5h-69l-63-4c-18-2-37-2-55-5l40-15 2 2-1 1h13c1 0 4 0 5-1l2-2v1l-1 1c3 1 6 1 9 1h18l1-1h1c1 1 4 1 5 1h15v-1l1-1h1v2c4 0 11 0 13-2l1 2h42 20c4 0 8 0 12-1h2 0 1 5 2 1 3 7c-2-3-6-5-6-8v-2c2 1 3 2 4 3 1 2 4 5 6 7h7l-3-3h-1c-2 1-3 0-5-1v-1h2c-1-1-2-2-4-3 0 0-1 0-2-1h0 0l-12-12-7 2c-2 0-3 1-5 1 0 2 1 2 2 3 2 2 5 5 5 7l-1 1c-1 0-1-1-2-1-2-4-6-7-8-11l-1-1-2-2c-1 1-1 1-2 0h-4c-3-1-8-1-11-1l5 15v1c-2-2-3-5-3-7-1 0-1-1-1-2-1-2-2-4-3-7l-3-8c0-1 0-2-1-3v-2c0-1-1-1-1-2l-1-4v-4h-1v-3l-1-2-1-5v-4l1 1v-3c-1-2-2-5-2-8 0-2-1-4-2-6 0-1-1-3-2-4v-5-25h0c1 4 1 7 1 11 1-1 2-1 4-2l3 3h1l-1-1v-1l-1-2v-1c2 0 2 5 4 5v-2h1c0 1 1 1 1 3 2 2 5 6 7 8 2 0 4 3 5 3-1-2-3-3-5-5-3-3-6-8-8-12 4 5 8 10 13 14h0c-2-3-5-5-7-8s-5-6-5-10h0c1 2 2 3 3 5 1 1 3 3 3 4 1 2 2 3 3 4s1 0 2 1h0 1c0-1 0-2-1-3s-1-1-1-2c-1-2-3-4-4-6-2-3-5-7-6-10v-1c2 2 3 6 6 7 2 2 3 4 5 5h0l-3-4c-3-3-5-6-7-9-1-1-1-1-1-2 2 1 3 3 4 5 2 2 6 8 9 9-2-3-4-5-6-8-2-2-2-4-4-5l-1-1c-1-1-2-3-2-5l1 1c1 2 1 3 2 4 2 2 3 4 5 6 1 1 3 3 5 4-1-1-2-3-4-5-2-3-9-10-9-13 3 2 4 7 7 9 2 1 3 3 5 5 0-2-2-4-3-6l1-1v-1h-1-1c-1-4-5-6-6-10 0-1 0-1-1-2v-1c0-2-1-4 0-5 1 3 4 6 7 9 1 1 2 2 4 3v-1c0-1-2-2-2-2l-5-5c-1-2-2-4-4-6v-2 1c3 5 6 9 11 12v-1l-6-6c-1-2-2-5-5-7v-1c3 1 4 5 6 8 2 2 4 3 6 5l-9-13c3 1 6 7 8 10v-1-1h1c0-1 0-2 1-3v2h1c2 1 4 2 6 2 1 1 3 2 4 2 2 0 3-1 4-2 1 0 3-1 4-2l2-1 3-2c1 0 2-1 3-2 5-2 7-10 10-15z"></path><path d="M535 800v-14c0-3-1-5 0-7 2-1 3 0 5 0-2 2-2 3-2 5l-1 1v1c0 4-1 10-2 14z" class="Y"></path><defs><linearGradient id="Y" x1="536.428" y1="864.679" x2="556.382" y2="866.972" xlink:href="#B"><stop offset="0" stop-color="#929293"></stop><stop offset="1" stop-color="#bababa"></stop></linearGradient></defs><path fill="url(#Y)" d="M552 882c0-3-5-8-6-10l-6-9c-4-10-5-21-7-32h0 1c1 4 1 8 2 11 4 12 9 23 17 33h0c0 2 1 3 2 4 0 1-1 2-2 3h-1z"></path><defs><linearGradient id="Z" x1="576.599" y1="875.282" x2="583.604" y2="915.035" xlink:href="#B"><stop offset="0" stop-color="#b6b6b7"></stop><stop offset="1" stop-color="#eceae7"></stop></linearGradient></defs><path fill="url(#Z)" d="M553 875a57.31 57.31 0 0 0 11 11c14 12 31 20 48 26h-15-6c-2-1-4-3-6-4-4-2-8-5-12-8-2-2-5-4-7-6h0l-3-2c-1-1-2-3-3-4-2-2-4-4-7-6 1-1 2-2 2-3-1-1-2-2-2-4h0z"></path><defs><linearGradient id="a" x1="539.726" y1="806.802" x2="544.668" y2="807.286" xlink:href="#B"><stop offset="0" stop-color="#828182"></stop><stop offset="1" stop-color="#9b9b9b"></stop></linearGradient></defs><path fill="url(#a)" d="M540 779c3 1 6 3 9 2l-1 3h0c1 1 0 3 0 4v2c1 2 0 3 0 6v1 1 4c1 3 1 7 0 9v3h0c-1 2-1 3-1 5v2l-1 3c-1 1-1 2-1 3s-1 1-2 2l-1 1v1 1 2h0v1l-1 1-1 1c0 1-1 1-1 2v3c-1-2-1-3-1-5s-1-4-1-5c-1-3-1-6-1-9l-1-23c1-4 2-10 2-14v-1l1-1c0-2 0-3 2-5z"></path><path d="M543 815h1v1h-1v-1z" class="T"></path><g class="H"><path d="M540 821v-2c1 1 1 1 0 2z"></path><path d="M540 819v-3h0l1 2-1 1zm4-29v1 2h-1l1-3zm-6 47c1-2 0-4 0-7v3l1 1v1l1 2c0 1-1 1-1 2v3c-1-2-1-3-1-5z"></path></g><path d="M536 823h0c1-2 1-4 1-6v1c1 5 0 9 0 14-1-3-1-6-1-9z" class="P"></path><defs><linearGradient id="b" x1="542.569" y1="864.984" x2="546.995" y2="892.922" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#c7c5c3"></stop></linearGradient></defs><path fill="url(#b)" d="M542 891h2l-1-1c-1-1-1-2-2-2v-1c-2-1-2-3-4-4 0-1-1-2-1-3h-1l-5-10c0-1-1-2-1-2l1-1h1 0l-3-6c0-1 1-1 0-2h0v-1c1 1 2 1 3 2h1l-1-2c-1-2-3-3-4-4v-1l6 6c1 0 1 1 2 1-1-2-2-3-4-5-4-4-8-7-9-13 0-2-1-3-2-5l1-1v-1l-1-1v-5c1 2 1 3 1 5 1 1 2 2 3 4l4 4c1 2 3 4 6 6h0l1 3c-1 0-2-1-3-2-3-2-6-5-8-7l2 4c2 5 8 8 11 12 2 3 3 6 4 8 2 5 7 12 11 16h1c3 2 5 4 7 6 1 1 2 3 3 4l3 2h0 0c6 8 14 13 22 18l-12-1-4-4v1l3 4h-21v-1h0 1 5 2 1 3 7c-2-3-6-5-6-8v-2c2 1 3 2 4 3 1 2 4 5 6 7h7l-3-3h-1c-2 1-3 0-5-1v-1h2c-1-1-2-2-4-3 0 0-1 0-2-1h0 0l-12-12-7 2c-2 0-3 1-5 1 0 2 1 2 2 3 2 2 5 5 5 7l-1 1c-1 0-1-1-2-1-2-4-6-7-8-11l-1-1z"></path><path d="M507 801h0c1 4 1 7 1 11 1-1 2-1 4-2l3 3h1l-1-1v-1l-1-2v-1c2 0 2 5 4 5v-2h1c0 1 1 1 1 3v2 1c0 3 3 7 4 9 3 4 7 7 9 11-5-3-9-8-12-14l-1-1h0c0 2 2 5 3 7l1 1c2 2 5 6 8 7l1 2v1c-3-3-9-7-10-10l-1 1c4 4 9 8 12 13-4-3-7-6-10-9h0c2 3 4 5 6 7s3 3 4 5v1h0c-3-2-5-4-6-6l-4-4c-1-2-2-3-3-4 0-2 0-3-1-5v5l1 1v1l-1 1c1 2 2 3 2 5 1 6 5 9 9 13 2 2 3 3 4 5-1 0-1-1-2-1l-6-6v1c1 1 3 2 4 4l1 2h-1c-1-1-2-1-3-2v1h0c1 1 0 1 0 2l3 6h0-1l-1 1s1 1 1 2l5 10h1c0 1 1 2 1 3 2 1 2 3 4 4v1c1 0 1 1 2 2l1 1h-2l-2-2c-1 1-1 1-2 0h-4c-3-1-8-1-11-1l5 15v1c-2-2-3-5-3-7-1 0-1-1-1-2-1-2-2-4-3-7l-3-8c0-1 0-2-1-3v-2c0-1-1-1-1-2l-1-4v-4h-1v-3l-1-2-1-5v-4l1 1v-3c-1-2-2-5-2-8 0-2-1-4-2-6 0-1-1-3-2-4v-5-25z" class="M"></path><path d="M508 827h1c0 2 1 3 2 4l2 3c1 0 1 1 1 2h0l-1-1-2-2c-1 0-1 0-1-1-2-1-2-3-2-5z" class="Z"></path><path d="M409 913h137 44c8 0 17 0 25 1 6 0 13 4 19 6h-37-227c7-2 12-5 19-6 2-1 4-1 6-1h14z" class="o"></path><path d="M189 138h1 1 4 1 0c0-1 0-1 1-1 0 0 1 1 1 2h3 2 9 0 6 10 14v1l41 1h14 19 11v1h9 4c-1 1-1 1-2 1h2v1 2h0 0l2 2h0v5l-11 11-7 9-4 6c-1 1-3 4-4 4l-5 9-2 2c-1 2-4 6-4 9l-1 1-1-1c0 1-1 1-1 1-2 1-3 2-5 2h-4l2-2v-1h-4v-1c0-2 0-5-1-7 0 9 1 19-1 28-2-1-2-2-2-4l-1 7c-1 1-1 3-2 4v-2l-1 11-2 7 2 2-3 2c-1-1-2-2-4-3h0c-3 2-5 5-7 8 0 1-2 4-3 4h-4c-1-1-5-1-7-1-2 1-5 7-6 8h0-2v2c-1 2-2 3-3 3l-2 1 1-3h0l-1-1h-1c2 0 3 0 4-1-1-2-5-1-8-1-1-1-2 0-3 0 0-1 0-2 1-3s3-3 4-5l-1-1v-2c1-1 3-1 6-1v-1l1-1 1-9v-7-5h-1v-7-7-2c0-1-1-2-2-2v-1c-1 0-2-1-2-2h-1v-1h-1v-1l2-2h1-2v-1h2c1-3 1-6 0-9-2-2-3-1-5-1-2 1-4 0-6 0-2 1-8 1-11 1s-9-1-12 0v1c-1 1-2 1-3 1h-1c-1-1-1-1-1-2h2 0c0-3 0-5 2-7l1-2c0-2-1-3-1-5-3-1-5-1-8-1-4 0-10 1-13 3l-1 1v-1-2c1-7 3-11 8-15v-1c-4 0-9-1-13 0h0-1c-1-4 5-7 5-11 0-2 0-2 1-3l1-1c-1-1-3-1-4-1-1 1-1 0-1 1h-2v-2l-11-1 3-5h3c0-1 1-1 2-2 0 0 1-1 1-2l3-1c2 1 4 0 6 0h3z" class="R"></path><path d="M235 151l2-1h4v2l-6-1z" class="D"></path><path d="M255 242h6l-1 2c-1-1-2-1-3-1-1 1-1 1-2 1l-1-1 1-1z" class="K"></path><path d="M256 183h0c2 1 2 1 3 1 1-1 2-1 3 0v1l-1 1c-2 0-4-1-6-1v1l-1-1 1-2h1z" class="S"></path><path d="M258 254h-2c-2-2-1-5-1-6l2-2 1 1c-1 1-2 3-2 4s0 1 1 2l1 1z" class="I"></path><path d="M254 197h7l1 1-1 2c-2 0-5 0-7-1v-2z" class="P"></path><path d="M254 223h4 3 1v3h-1c-2-1-4-1-5-1-1 1-1 1-2 1v-3z" class="Y"></path><path d="M338 143h2v1c-5 0-11 2-16 0 2-2 11-1 14-1z" class="Q"></path><path d="M257 240l-3-1c0-2 0-2 1-3h3c2 1 2 0 4 1l-1 1-3 1-1 1z" class="f"></path><path d="M275 158h0c1-4 0-8 1-11h1c0 5 1 11 0 16v-1c-1-1-1-2-2-3v-1z" class="G"></path><path d="M262 237c0-2-1-2 0-4 2 2 1 4 2 6v1c-1 1-1 2-1 3l-1-2-1 1h-6v-1h1v-1h1l1-1 3-1 1-1z" class="O"></path><path d="M258 239l2 1 2-1v2l-1 1h-6v-1h1v-1h1l1-1z" class="e"></path><path d="M310 142c5-1 12 0 17 0h9 4c-1 1-1 1-2 1-3 0-12-1-14 1-2 0-2-1-3-1h-3l-8-1z" class="C"></path><path d="M261 209h1v5c-1 0-3-1-5-1h-1l-2 1v-1l1-1-1-1v-1c2-1 5-1 7-1z" class="U"></path><path d="M251 247c1 3 1 5 1 8-2 1-2 1-3 1l-1 1 1-1c1 0 1 0 2 1h-12-1v-2c1-1 3-1 6-1v-1l1-1 1 3c1 1 3 0 4 0 2-2 1-5 1-8z" class="e"></path><path d="M283 141h14 19 11v1c-5 0-12-1-17 0l-26 1-1-2z" class="F"></path><path d="M263 243c0-1 0-2 1-3 0 1 0 1 1 2v3c1 2 2 3 3 4l-3 2c-1 0-2 1-3 2-1 0-2 1-2 1h-2l-1-1c1-2 2-3 2-5v-2c1-1 0-1 1-1 1 2-1 5 0 7 1-1 2-7 3-9z" class="E"></path><path d="M265 245c1 2 2 3 3 4l-3 2c-1-2 0-4 0-6z" class="b"></path><path d="M340 146h0l2 2h0v5l-11 11v-2-1c-1 0-2 0-3-1h-3v-3c2 0 4 1 6 0h1c1 0 3 0 5-1-1 0 3-5 3-6v-4h0z" class="D"></path><path d="M337 156c-1 2-2 3-4 4-2-2-4-1-6-2l-1 1 2 1h0-3v-3c2 0 4 1 6 0h1c1 0 3 0 5-1z" class="j"></path><path d="M186 159h3c1-3-1-8 1-10 2 1 1 2 2 4h2 2 7l13 1v1c-4 1-9 0-13 1-4 0-8 2-12 2-2 2-2 2-5 2v-1z" class="P"></path><path d="M217 151c1-1 1-1 3-1 1-1 2-1 4-1h1c2 0 2 0 4 1h1c2 1 1 3 2 4 1 0 1-1 1-2h1v-2h1v3l1-1-1-1 6 1c0 1 0 2 1 4h-1-1l2 1-1 1c-4 0-11-1-14 1v-8h-10z" class="K"></path><path d="M217 151h10v8 1l-1 3-2-2v1h-2l-1 1c-1 0-3 1-4 2v-10h0v-2-2z" class="G"></path><path d="M224 158l1 1-1 2v1h-2l-1 1-2-2 1-1h0 3l-1-2h0l2 1v-1z" class="N"></path><path d="M217 151h10v8 1l-1 3-2-2 1-2-1-1c0-1 1-2 1-3v-2c-2 0-6-1-7 0l-1 2h0v-2-2z" class="M"></path><path d="M227 160l1 1h5 9c1 1 1 1 0 2v3 2 1c-2 1-5 1-8 1h-1c-1-1-2-1-4-1h-2v-2-1h-1v-3l1-3z" class="S"></path><path d="M230 167h-1c0-1 0-1 1-2h1c0-1 1-1 1-2h2c1 1 1 2 0 2 0 1-1 1-2 2h0c-1 0-1 0-1-1l-1 1z" class="h"></path><path d="M230 167l1-1c0 1 0 1 1 1l1 1c2 0 7 1 9 0v1c-2 1-5 1-8 1h-1c-1-1-2-1-4-1l1-2z" class="Y"></path><path d="M216 154l1-1v2 4h-2c-4 0-11-1-14 1h-2c-1 1-2 1-2 1l-1 1c-1 0-3 1-4 2h-1-3-3-1c0-1 1-1 2-2l-1-1v-1c-1 0-1 0-1-1l1-1c0-1 0-1 1-2l1 1-1 1v1 1c3 0 3 0 5-2 4 0 8-2 12-2 4-1 9 0 13-1v-1z" class="B"></path><path d="M265 161c2 3 2 6 2 10v55c0 1 1 2 1 3s1 2 1 3v-3c0-1 0-2 1-3l1 3-1 11c1 2 1 5 0 7l-2 2c-1-1-2-2-3-4v-3c-1-1-1-1-1-2v-1l1-13v-65z" class="H"></path><path d="M265 226c0 2 0 6 1 8v6l-1 2c-1-1-1-1-1-2v-1l1-13z" class="b"></path><path d="M265 242l1-2c2 2 2 2 2 4 1 1 1 2 2 3l-2 2c-1-1-2-2-3-4v-3z" class="j"></path><path d="M267 226c0 1 1 2 1 3s1 2 1 3v-3c0-1 0-2 1-3l1 3-1 11v2c-2-2-1-4-2-6-2-3-1-7-1-10z" class="N"></path><path d="M248 237l1-88 1 2 1 96c0 3 1 6-1 8-1 0-3 1-4 0l-1-3 1-9 1-1c0-2 0-4 1-5z" class="V"></path><path d="M246 243l1-1c0-2 0-4 1-5v14c2-3 1-10 2-13 1 5 0 11 0 16 0-2 0-5 1-7 0 3 1 6-1 8-1 0-3 1-4 0l-1-3 1-9z" class="b"></path><path d="M246 174v-18c0-3 0-7 1-10h0c1 2 0 4 1 6 0-2 0-4 1-6h1v5l-1-2-1 88c-1 1-1 3-1 5l-1 1v-7-5-57z" class="S"></path><path d="M265 161l1-14 1-1 1 3 1-1v-1h0c1 2 1 5 1 8-1 3 0 7 0 11l1 24-1 36c-1 1-1 2-1 3v3c0-1-1-2-1-3s-1-2-1-3v-55c0-4 0-7-2-10z" class="o"></path><path d="M265 161l1-14 1-1 1 3 1-1c-1 5-1 10-2 15 0 3 1 6 0 8 0-4 0-7-2-10z" class="T"></path><path d="M189 138h1 1 4 1 0c0-1 0-1 1-1 0 0 1 1 1 2h3 2 9 0 6 10 14v1l41 1 1 2h-24-68c-6 0-13-1-18 0 0-1 1-1 2-2 0 0 1-1 1-2l3-1c2 1 4 0 6 0h3z" class="b"></path><path d="M189 138h1 1 4 1 0c0-1 0-1 1-1 0 0 1 1 1 2h3 2 9 0 6 10 14v1h-24-11-9-9c-2 0-3 1-4 1s-1-1-2-1c-2 0-5 1-7 1 0 0 1-1 1-2l3-1c2 1 4 0 6 0h3z" class="C"></path><defs><linearGradient id="c" x1="233.355" y1="135.007" x2="262.016" y2="147.294" xlink:href="#B"><stop offset="0" stop-color="#626263"></stop><stop offset="1" stop-color="#8f8d8f"></stop></linearGradient></defs><path fill="url(#c)" d="M218 140h24l41 1 1 2h-24l-43-2 1-1z"></path><path d="M270 155c1-3 1-7 2-10 0 3-1 11 1 13 0-4 0-8 1-12l1 15v-3 1 44l1 15c0 3-1 6 0 10-1 3 0 6 0 9 0 2 0 4 1 5v2c0 1 1 1 2 1 0 0 0 1 1 1h1l2 2-3 2c-1-1-2-2-4-3v-1l-2-3h-1c0 1 0 2-1 2-1-1 0-1 0-2-1-2-1-7 0-10-1-1-1-3-1-4l-1-3 1-36-1-24c0-4-1-8 0-11z" class="C"></path><path d="M273 158c0-4 0-8 1-12l1 15-1 74-1-9c1-2 0-8 0-11v-37c0-7 1-14 0-20z" class="G"></path><path d="M270 155c1-3 1-7 2-10 0 3-1 11 1 13 1 6 0 13 0 20v37c0 3 1 9 0 11v1c-1 4 0 8 0 12h-1v-6c-1-1-1-3-1-4l-1-3 1-36-1-24c0-4-1-8 0-11z" class="J"></path><path d="M217 155h0v10c1-1 3-2 4-2l1-1h2v-1l2 2v3h1v1 2h2c2 0 3 0 4 1h1c2 2 6 1 9 1v3h-2l2 1h-3c1 0 2 0 3 1h-12-1v-1h0l-1 1c-2 0-4 0-6-1-1 1-3 1-4 0h-5c-3 0-9 0-12-1h0c-2-1-3-1-4 0l-2 1 2 2-3 1c-2 1-4 1-6 1l-1 1 1 1 7-2c2 1 2 1 3 2-4 0-10 1-13 3l-1 1v-1-2c1-7 3-11 8-15v-1c1 0 2 0 3 1h2c1 0 2-5 3-6v-1c3-2 10-1 14-1h2v-4z" class="D"></path><path d="M217 168l7-1 1 1-2 2c-2 0-4 0-5-1l-1-1z" class="Y"></path><path d="M226 166h1v1 2h2c2 0 3 0 4 1h-5-5l2-2s1-1 1-2z" class="U"></path><path d="M224 161l2 2v3c0 1-1 2-1 2l-1-1-7 1v-3c1-1 3-2 4-2l1-1h2v-1z" class="Z"></path><path d="M224 161l2 2v3c0 1-1 2-1 2l-1-1h0c0-1-1-3 0-5v-1z" class="H"></path><path d="M243 171v3h-2l2 1h-3c1 0 2 0 3 1h-12-1v-1h0l-1 1c-2 0-4 0-6-1v-2h12c1 0 3 0 5-1h1 1l1-1z" class="K"></path><path d="M215 159c-2 1-10 1-13 1 1 1 7 2 9 3h2v1c-2 0-4 0-6-1v1c1 0 2 1 3 1v1l-4-1-1 1h0l-1 1 1 1-2-1-1 1 1 1c-1 1 0 1-1 1-2 1-3 1-5 2 0-2 0-2 1-3l-1-1h-1v-1h2c1 0 2-5 3-6v-1c3-2 10-1 14-1z" class="G"></path><path d="M193 166c1 0 2 0 3 1v1h1l1 1c-1 1-1 1-1 3 2-1 3-1 5-2 0 1 0 1 1 1h13l1 1c-2 1-6 0-8 0h-7v2h0c-2-1-3-1-4 0l-2 1 2 2-3 1c-2 1-4 1-6 1l-1 1 1 1 7-2c2 1 2 1 3 2-4 0-10 1-13 3l-1 1v-1-2c1-7 3-11 8-15v-1z" class="X"></path><path d="M193 166c1 0 2 0 3 1v1h1l1 1c-1 1-1 1-1 3 2-1 3-1 5-2 0 1 0 1 1 1h13l1 1c-2 1-6 0-8 0h-7v2h0c-2-1-3-1-4 0l-2 1v-1c0-1-1-1-1-2v-1h-1v2h1l1 3h0c-1 0-1 0-1-1h-1l-1 1 2 1c-2 1-4 1-6 1 1-2 1-3 2-5l3-5-1-1v-1z" class="M"></path><path d="M243 176l1 1v1h-1c-2-1-4-1-6 0h-2-4-10l13 1h8 1 1v3h1v-1c1-2 0-5 0-7h1v57h-1v-7-7-2c0-1-1-2-2-2v-1c-1 0-2-1-2-2h-1v-1h-1v-1l2-2h1-2v-1h2c1-3 1-6 0-9-2-2-3-1-5-1-2 1-4 0-6 0-2 1-8 1-11 1s-9-1-12 0v1c-1 1-2 1-3 1h-1c-1-1-1-1-1-2h2 0c0-3 0-5 2-7l1-2c0-2-1-3-1-5-3-1-5-1-8-1-1-1-1-1-3-2l-7 2-1-1 1-1c2 0 4 0 6-1l3-1-2-2 2-1c1-1 2-1 4 0h0c3 1 9 1 12 1h5c1 1 3 1 4 0 2 1 4 1 6 1l1-1h0v1h1 12z" class="E"></path><path d="M242 206l2 2v1c-1 0-2 1-4 1v-1h-1v-1l2-2h1z" class="Z"></path><path d="M233 188c3 0 9-1 11 1v2c-1 1-2 1-3 1l-2-1c-2-1-3-2-5-2l-1-1z" class="M"></path><path d="M234 189c2 0 3 1 5 2l2 1-5-1 3 1v1c-3 0-5-1-8 0h-1-5c-5 0-10 1-14-1h1l1-1v-1h10 1l2-1c2 1 5 0 8 0z" class="S"></path><path d="M234 189c2 0 3 1 5 2l2 1-5-1h-1c-4 0-8 0-11-1l2-1c2 1 5 0 8 0z" class="F"></path><path d="M235 191h1l3 1v1c-3 0-5-1-8 0h-1-5c-5 0-10 1-14-1h1l23-1z" class="C"></path><path d="M216 178l16 1c3 0 7 0 10 1v2c-2 0-5 0-7 1v1l-1 2h-1c-1-1-1-1-2 0h-1v-4h-1l-13-1c-1-1-2-1-3-2 0 0 2-1 3-1z" class="F"></path><path d="M207 189l1 1h0 5v1l-1 1h-1c4 2 9 1 14 1h5c-1 2-3 1-5 2h-5v1c-3 0-9-1-12 0v1c-1 1-2 1-3 1h-1c-1-1-1-1-1-2h2 0c0-3 0-5 2-7z" class="U"></path><path d="M207 189l1 1h0l-2 1c0 1 0 1 1 2 3 2 8 1 11 2h7-5v1c-3 0-9-1-12 0v1c-1 1-2 1-3 1h-1c-1-1-1-1-1-2h2 0c0-3 0-5 2-7z" class="X"></path><path d="M196 175l2-1c1-1 2-1 4 0h0c3 1 9 1 12 1h5c1 1 3 1 4 0l-1 2c-1 0-2 0-3-1l-1 1c-1 0-3-1-5-1-3 0-8-1-11 1 3 0 5 1 8 1h6c-1 0-3 1-3 1-2 0-3 0-5 1h-1v1 1c-3-1-5-1-8-1-1-1-1-1-3-2l-7 2-1-1 1-1c2 0 4 0 6-1l3-1-2-2z" class="H"></path><path d="M196 179l11 1v1 1c-3-1-5-1-8-1-1-1-1-1-3-2z" class="R"></path><path d="M196 175l2-1c1-1 2-1 4 0l1 2c-1 1-3 1-4 1h-1l-2-2z" class="G"></path><path d="M213 179c1 1 2 1 3 2l13 1h1v4h1c1-1 1-1 2 0v2h0l1 1c-3 0-6 1-8 0l-2 1h-1-10-5 0l-1-1 1-2c0-2-1-3-1-5v-1-1h1c2-1 3-1 5-1z" class="U"></path><path d="M207 181h1l3 1v2l1 1 2-1v1 1c-1 1-2 1-4 1-1 1-1 1-2 3l-1-1 1-2c0-2-1-3-1-5v-1z" class="L"></path><path d="M213 179c1 1 2 1 3 2l13 1c-4 3-8 1-12 1h-2c-1-1-1-1-1-2l-3 1-3-1h-1v-1h1c2-1 3-1 5-1z" class="b"></path><path d="M213 179c1 1 2 1 3 2h-8-1v-1h1c2-1 3-1 5-1z" class="W"></path><path d="M210 187h19l4 1h0l1 1c-3 0-6 1-8 0l-2 1h-1-10-5 0c1-2 1-2 2-3z" class="Z"></path><path d="M229 187l4 1h0l1 1c-3 0-6 1-8 0h-3v-1l6-1z" class="J"></path><path d="M287 152h1v-6l1 1 1 24v11 13c0 9 1 19-1 28-2-1-2-2-2-4l-1 7c-1 1-1 3-2 4v-2l-1 11-2 7h-1c-1 0-1-1-1-1-1 0-2 0-2-1v-2c-1-1-1-3-1-5 0-3-1-6 0-9-1-4 0-7 0-10l-1-15v-44c1 1 1 2 2 3v1 1c1-4 1-9 2-14 0-1 0-3 1-4v2h2l1-1h1l1 3v-1c1-1 1-1 1-2h0c1 1 1 3 1 5z" class="n"></path><path d="M280 148h2l1-1v9l-2 3-1-11z" class="L"></path><path d="M281 159l2-3v52h-2v-49z" class="Y"></path><path d="M276 228c2-3 1-15 1-18l1 14c0 2 0 5 1 7 4-5 2-17 2-23h2v6l-1 10c2 2 2 2 2 4l-1 11-2 7h-1c-1 0-1-1-1-1-1 0-2 0-2-1v-2c-1-1-1-3-1-5 0-3-1-6 0-9z" class="c"></path><path d="M282 224c2 2 2 2 2 4l-1 11-2 7h-1c-1 0-1-1-1-1-1 0-2 0-2-1v-2l1 1h1v-3h1c2-1 0-4 1-6 2 0 1 0 2-1v-3c-1-1-1-3-1-5v-1z" class="Y"></path><path d="M287 152h1v-6l1 1 1 24v11 13c0 9 1 19-1 28-2-1-2-2-2-4l-1 7c-1 1-1 3-2 4v-2c0-2 0-2-2-4l1-10v-6-52-9h1l1 3v-1c1-1 1-1 1-2h0c1 1 1 3 1 5z" class="M"></path><path d="M287 152h1v-6l1 1 1 24v11 13c0 9 1 19-1 28-2-1-2-2-2-4l1-41c0-6 1-13 0-19-1-2-1-5-1-7z" class="g"></path><path d="M283 147h1l1 3v11l1 35v12c-1 5-2 10-2 16h-1c0-4 1-7 0-10v-6-52-9z" class="l"></path><path d="M298 151l1 1 1-1v-5c2 0 5 1 6 0 2 0 4-1 5-1 3 0 9 0 11 1 0 0 1 1 1 2 1 1 1 2 2 4 0 1-1 4 0 5h0v3h3c1 1 2 1 3 1v1 2l-7 9-4 6c-1 1-3 4-4 4l-5 9-2 2c-1 2-4 6-4 9l-1 1-1-1c0 1-1 1-1 1-2 1-3 2-5 2h-4l2-2v-1h-4v-1c0-2 0-5-1-7v-13-11l-1-24h0c1 2 1 7 1 9v-3h2l1-1h0 1c1-1 1-1 2-1h2z" class="Q"></path><path d="M289 147h0c1 2 1 7 1 9v-3h2l1-1h0 1c1-1 1-1 2-1h2l-2 2v1h0c-1 1-1 0-1 1v-1c-1 0-1 0-1-1-1 0-1 0-2 1h-1v8l1 1h1c0-1 1-1 2-1h1l1-1v1h0v3l1-1c2-1 7 0 9 0v1c-3 3-12-1-15 2 1 1 3 0 5 0h1c0 1 0 0-1 1h-6c0 1 0 2-1 3l-1-24z" class="I"></path><path d="M312 170c0-1 0-1 1-2h0v1h1s1-1 1-2 0-2 1-3l1 1c1 1 1 1 1 2l1 2h1v3c-1 1-2 2-3 4h0c-2-2-8 0-11-1 0-1 0-1-1-2h0c1-1 0-1 1-2l1 1c0 1 0 1 1 2 1-1 2-1 3-2l1-2z" class="D"></path><path d="M312 170c0-1 0-1 1-2h0v1h1s1-1 1-2 0-2 1-3l1 1c0 2 1 4 0 6-1 1-3 1-4 0l-1-1h0z" class="a"></path><path d="M290 171c1-1 1-2 1-3h6v1h-4 0 1v1h4v3l2-1h0c1 1 2 1 2 1v3h-2c-1 0-2 0-2 1 2 1 4 0 6 1h4v2h-9c-2 0-4 1-5 0-1 0-2 0-3 1l-1 1h0v-11z" class="E"></path><path d="M294 169v1h4v3l-1 1-1-1c-1 0-2 0-3 1l-1-1c1-1 1-1 0-2h0l1-1 1-1z" class="K"></path><path d="M309 154c0-2 0-3 1-5 2 1 4 0 6 1v-1l1 1c-1 4-1 9-1 14-1 1-1 2-1 3s-1 2-1 2h-1v-1h0c-1 1-1 1-1 2l-1 2h-2v-18z" class="G"></path><path d="M298 151l1 1 1-1v-5c2 0 5 1 6 0 2 0 4-1 5-1 3 0 9 0 11 1 0 0 1 1 1 2v1h-2l-1 1-1-1c-1 0-1 0-2 1h0l-1-1v1c-2-1-4 0-6-1-1 2-1 3-1 5-1-2-1-3-4-3h-1-1c-1 0-1 1-2 1l1 1h0 1 1l1 1h-1-1-2 0-2v1l1 1h6 1v4c-3 1-6-1-8 1h0c-2-1-3-1-4-1h-1c0-1 0-1-1-2h-1v-2c2 0 2 0 3-1h0c0-1 0 0 1-1h0v-1l2-2z" class="C"></path><path d="M323 148c1 1 1 2 2 4 0 1-1 4 0 5h0v3h3c1 1 2 1 3 1v1 2l-7 9c-3 1-4 2-6 4 0 1-1 1-1 2v1l-1-1c0-1 0-2 1-3h0c1-2 2-3 3-4v-3h-1l-1-2c0-1 0-1-1-2l-1-1c0-5 0-10 1-14h0c1-1 1-1 2-1l1 1 1-1h2v-1z" class="d"></path><path d="M323 148c1 1 1 2 2 4 0 1-1 4 0 5h0v3 1 1h-2v-13-1z" class="I"></path><path d="M320 169l-2-10h0c1 4 3 8 5 11v-8h2v-1-1h3c1 1 2 1 3 1v1 2l-7 9c-3 1-4 2-6 4 0 1-1 1-1 2v1l-1-1c0-1 0-2 1-3h0c1-2 2-3 3-4v-3z" class="C"></path><path d="M325 160h3c1 1 2 1 3 1v1l-2 3c-1 0-1 0-3-1 0-1 0-1 1-2h-2v-1-1z" class="X"></path><path d="M308 180h3 1 1l1 1-2 2v1c1 0 2-1 3-1h1l-5 9-2 2c-1 2-4 6-4 9l-1 1-1-1c0 1-1 1-1 1-2 1-3 2-5 2h-4l2-2v-1h-4v-1c0-2 0-5-1-7v-13h0l1-1c1-1 2-1 3-1 1 1 3 0 5 0h9z" class="U"></path><path d="M299 183v2h-4v-3h4v1z" class="D"></path><path d="M311 180h1 1l1 1-2 2h-3c-1 1-1 1-2 0h-7-1v-1h0 9v-1l2 1 1-2z" class="L"></path><path d="M300 183h7c1 1 1 1 2 0h3v1l-1 1c0 1 0 1-1 2l-1 1c-2-1-5-1-8-1v-1c-1 0-1 0-2-1l1-2z" class="Y"></path><path d="M301 186s0-1 1-1l5 1c1 0 3-1 4-1 0 1 0 1-1 2l-1 1c-2-1-5-1-8-1v-1z" class="f"></path><path d="M312 184c1 0 2-1 3-1h1l-5 9-2 2c-1 2-4 6-4 9l-1 1-1-1c0 1-1 1-1 1-2 1-3 2-5 2h-4l2-2v-1h0l1-1c-2-1-3-1-4-1v-1c1-1 3-1 4-1v-1h3c1-1 2 0 3-1l-1-1-1 1h-5v-1l-1 1h-1v-2h1l1-1c-1-1 0-3 0-4v-1c1 1 1 2 1 2h2v-2c2-2 9-1 11-1h0l1-1c1-1 1-1 1-2l1-1z" class="L"></path><path d="M295 194c-1-1 0-3 0-4v-1c1 1 1 2 1 2h2 1 9l-1 3h-1c-2-1-5-1-7-1l-1 1h-3z" class="c"></path><path d="M298 191v-2c2-2 9-1 11-1l1 1-2 2h0-9-1z" class="J"></path><path d="M299 191h3l2-2c2-1 3 1 4 2h0-9z" class="M"></path><path d="M296 199c2 0 3 0 5-1h3v3h-3-1l-1 1c1 1 2 2 3 2-2 1-3 2-5 2h-4l2-2v-1h0l1-1c-2-1-3-1-4-1v-1c1-1 3-1 4-1z" class="k"></path><path d="M609 101l1-2c2-5 6-10 9-15 1 1 2 3 2 4v2c1 1 1 1 2 1l1-1v2l1 1s1 0 1-1h2l1 1c-3 1-3 6-4 8l-1 7-1 3v3c1 4 2 8 4 11 3 7 8 13 15 17l7 5c4 3 7 5 10 8l16 21v-1l12 20 5 11 3 7h0v1h-4 0l1-1v-1c-1 1-2 1-3 1-1 1-1 1-2 1v1h-1-1c-3 1-6 0-9 2h0v1l2 3v1c-1 1-1 3-1 5 0 0 1 1 1 2 1 2 1 3 2 5-2 1-3 0-5 0 0 0-3-1-3-2-12-4-24-5-36-6l-1-2v-3h0c1-1-1-3-1-4v-1l-5-11c0-1-1-3-1-4l-3-6c-2 0-3-2-5-3v1h-1v2c-1-1-1-1-2-1-1-1-1-2-2-2-6-7-12-14-19-20-21-19-48-33-77-35-36-3-69 9-97 32-7 6-14 14-20 21l-10 14-2 3-5 9c-1 3-3 5-4 7 1 1 1 2 0 3h1l-16 2-6 1-10 2v-3h-1l2-10h1l3-10c0-1 1-3 2-5h-2v-1c-1 0-3-1-4-2 1-1 2-1 3-1v-2l-1 1c-1 0-1 0-3-2 5-8 11-15 15-23 2-2 3-5 4-7 3-3 7-7 11-10 1-4 4-6 8-9 1-1 3-2 4-4 0-1 2-3 3-3l-2-2 3-3h1l3-1v-1 4h0l5-5 1 1v1l-3 3c0 4-2 6-4 9-1 1-2 1-2 2v3l3-4c0 2 0 3-1 4v2c2-1 3-2 5-3 4-3 6-8 9-11l3-3v3l-2 3c2-1 4-3 6-5 3-1 5-2 7-5h1l2 2 19-13c1 0 2 0 3-1 2-1 5-2 7-3l3-1 2-1 21-7h5l22-2c20 1 37 6 55 14l7 4s1-1 1-2l3 2c1-1 1-2 1-2 1 0 2 0 2 1 2 1 3 1 5 1l2-9 1-2c2 0 4 0 6 1 0 1 0 2-1 2h0c0 2 0 2-1 4-1 1-1 0-1 1v1h0c2-1 3-3 4-5 0 2-4 6-2 8l1-1h0c1-2 1-4 2-5l-1 6c2 3 2 7 4 10h0 1v-5c-1-2-2-3-2-6h1l1 3h0 0c1-6 0-11 2-16l1 1v1c0 1 1 2 0 3v1h1v-3c1-6 2-11 4-16v3z" class="E"></path><path d="M464 115c0 1 1 1 1 2 1 0 2 0 2-1l1-1c1 0 3 1 5 1l-12 4h0v-1h1 1c0-2 0-2-1-3l2-1z" class="N"></path><path d="M504 116c1-1 1-1 2-1 3-1 6-1 10 0 1 0 3-1 5 0l1 1h0v1l-18-1z" class="I"></path><path d="M464 115l21-7v2l-4 1 2 1h1v1h1l-9 2-3 1c-2 0-4-1-5-1l-1 1c0 1-1 1-2 1 0-1-1-1-1-2z" class="Z"></path><path d="M476 115h0c0-1 2-1 2-2h-1l-1-1c2 0 4-1 5-1l2 1h1v1h1l-9 2z" class="N"></path><defs><linearGradient id="d" x1="512.251" y1="184.011" x2="512.244" y2="116.999" xlink:href="#B"><stop offset="0" stop-color="#aba9a9"></stop><stop offset="1" stop-color="#d4d3d3"></stop></linearGradient></defs><path fill="url(#d)" d="M504 116l18 1c8 1 17 3 26 5 19 6 39 19 54 33 7 6 13 14 20 21l9 11-2 1-3-3-1 2-16-17c0-1-1-2-1-2-1-2-4-4-4-7l-4-4c-6-5-12-10-18-14-2-2-6-4-9-6-15-9-34-15-51-17h-8-13l-3 1h-4c-15 2-30 5-43 12-5 2-9 5-13 8-18 11-34 26-48 43l-1-1h0l8-11c6-7 13-13 20-19 11-9 24-18 36-24 9-4 19-8 28-10l23-3z"></path><path d="M604 161l22 24-1 2-16-17c0-1-1-2-1-2-1-2-4-4-4-7z" class="O"></path><defs><linearGradient id="e" x1="560.933" y1="200.645" x2="574.637" y2="115.613" xlink:href="#B"><stop offset="0" stop-color="#a2a1a2"></stop><stop offset="1" stop-color="#d4d3d2"></stop></linearGradient></defs><path fill="url(#e)" d="M512 106c20 1 37 6 55 14l7 4s1-1 1-2l3 2c1-1 1-2 1-2 1 0 2 0 2 1 2 1 3 1 5 1l2-9 1-2c2 0 4 0 6 1 0 1 0 2-1 2h0c0 2 0 2-1 4-1 1-1 0-1 1v1h0c2-1 3-3 4-5 0 2-4 6-2 8l1-1h0c-1 3-2 8 0 11h0l1 2 1 1-1 1h0c3 2 6 5 7 8l20 21c8 9 15 19 20 29 4 7 8 15 10 23h0c-3-2-4-4-5-7s-2-6-4-9c-4-7-9-14-13-21-2-3-4-6-6-8-10-15-25-28-39-38-18-13-40-23-61-25-13-1-27-1-40 1h-1v-1h-1l-2-1 4-1v-2h5l22-2z"></path><path d="M485 108h5c-2 1-3 2-5 2v-2z" class="N"></path><path d="M579 122c1 0 2 0 2 1 2 1 3 1 5 1v5h0l-2 2-10-7s1-1 1-2l3 2c1-1 1-2 1-2z" class="Q"></path><path d="M579 122c1 0 2 0 2 1 2 1 3 1 5 1v5h0c-2-2-5-4-8-5 1-1 1-2 1-2z" class="Z"></path><path d="M589 113c2 0 4 0 6 1 0 1 0 2-1 2h0c0 2 0 2-1 4-1 1-1 0-1 1v1h0c2-1 3-3 4-5 0 2-4 6-2 8l1-1h0c-1 3-2 8 0 11h0l1 2 1 1-1 1h0c3 2 6 5 7 8l-19-16 2-2h0v-5l2-9 1-2z" class="C"></path><path d="M590 125v4c-2-1-3-2-3-4h3z" class="D"></path><path d="M590 125c0-2 0-4 1-6 0-2 1-2 3-3h0c0 2 0 2-1 4-1 1-1 0-1 1v1h0c2-1 3-3 4-5 0 2-4 6-2 8l1-1h0c-1 3-2 8 0 11h0l1 2h-1c-2-2-5-5-5-8v-4z" class="i"></path><path d="M459 117l3-1c1 1 1 1 1 3h-1-1v1h0c-5 3-10 5-15 8-18 9-31 22-44 37l-6 6c-1 2-2 5-4 7-1 1-2 2-3 4v1h0l1 1c14-17 30-32 48-43l2 1c-1 1-1 1-1 2-1 0-1 1-1 1-1 1-4 3-5 3h-1l-1 1h0c1 1 0 1 1 0h1c-21 17-40 35-52 60-2 2-4 7-5 9v3 1c-1 0-1 1-1 1-1 1-1 0-1 1-1-1 0-2-1-3l1-4h0l-1 1v2l-1 1c-2 3 1-2-1 1-1 2 0 4-2 5h-4c1 1 0 1 1 1l-6 1-10 2v-3h-1l2-10h1l3-10c0-1 1-3 2-5h-2v-1c-1 0-3-1-4-2 1-1 2-1 3-1v-2l-1 1c-1 0-1 0-3-2 5-8 11-15 15-23 2-2 3-5 4-7 3-3 7-7 11-10 1-4 4-6 8-9 1-1 3-2 4-4 0-1 2-3 3-3l-2-2 3-3h1l3-1v-1 4h0l5-5 1 1v1l-3 3c0 4-2 6-4 9-1 1-2 1-2 2v3l3-4c0 2 0 3-1 4v2c2-1 3-2 5-3 4-3 6-8 9-11l3-3v3l-2 3c2-1 4-3 6-5 3-1 5-2 7-5h1l2 2 19-13c1 0 2 0 3-1 2-1 5-2 7-3z" class="I"></path><path d="M373 186c0-1 1-3 2-4 2-2 3-3 6-3l-8 10v-3z" class="V"></path><path d="M359 220v1l-2 7v1c1-1 1-1 2-1l1 1-10 2v-3c0-1 0-2 1-3l1 2 2-2c0 1 0 2 1 2h0l1-1 3-6z" class="R"></path><path d="M365 215h0c1 0 1-1 2-1v4c-1 2-2 5-2 7l-1-1c-1 0-2 1-3 0 0-1 0-1 1-2 0-1 1-2 1-3s1-2 2-4z" class="E"></path><path d="M413 139l3-3v3l-2 3c-4 4-8 9-12 13 0-1 0-2 1-2l1-2v-1c4-3 6-8 9-11z" class="Z"></path><path d="M389 183l1 1c-3 4-6 7-9 12l-11 21c-1 2-3 7-5 8 0-2 1-5 2-7v-4c-1 0-1 1-2 1h0l1-2h1l6-13 5-7s1 0 1 1h0l6-6c1-2 3-3 4-5z" class="M"></path><path d="M378 193s1 0 1 1l-12 24v-4c-1 0-1 1-2 1h0l1-2h1l6-13 5-7z" class="D"></path><path d="M373 186v3c-5 9-10 18-14 28v3l-3 6-1 1h0c-1 0-1-1-1-2l-2 2-1-2c-1 1-1 2-1 3h-1l2-10h1l3-10c0-1 1-3 2-5 1-1 2-3 4-5 2-1 4-4 5-6 0-2 2-4 3-5v1c0 1 0 1-1 2v2c2-1 3-4 5-6z" class="d"></path><path d="M358 215c1-3 2-6 4-9-1 2-1 5-2 7-1 1-1 2-1 4v3l-3 6v-3c0-1 1-2 1-3-1 2-2 3-3 4l-1-1c0-2 0-3 1-5h1l1-2c0 1-1 2 0 4 1-2 2-3 2-5z" class="U"></path><path d="M366 192c0 2-1 3-2 5l-2 6c-2 3-4 8-4 12 0 2-1 3-2 5-1-2 0-3 0-4l-1 2h-1c-1 2-1 3-1 5l1 1c1-1 2-2 3-4 0 1-1 2-1 3v3l-1 1h0c-1 0-1-1-1-2l-2 2-1-2c-1 1-1 2-1 3h-1l2-10h1l3-10c0-1 1-3 2-5 1-1 2-3 4-5 2-1 4-4 5-6z" class="Y"></path><path d="M352 218v-1c2-2 4-7 5-9 0 3-3 6-3 10-1 2-1 3-1 5l1 1c1-1 2-2 3-4 0 1-1 2-1 3v3l-1 1h0c-1 0-1-1-1-2l-2 2-1-2c-1 1-1 2-1 3h-1l2-10h1z" class="C"></path><path d="M400 147c0 2 0 3-1 4v2c2-1 3-2 5-3v1l-1 2c-1 0-1 1-1 2l-21 24c-3 0-4 1-6 3-1 1-2 3-2 4-2 2-3 5-5 6v-2c1-1 1-1 1-2v-1c-1 1-3 3-3 5-1 2-3 5-5 6 4-7 9-15 14-22 7-9 15-16 22-25l3-4z" class="N"></path><defs><linearGradient id="f" x1="430.532" y1="144.95" x2="425.499" y2="137.724" xlink:href="#B"><stop offset="0" stop-color="#a9a8a8"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#f)" d="M459 117l3-1c1 1 1 1 1 3h-1-1v1h0c-5 3-10 5-15 8-18 9-31 22-44 37l-6 6c-1 2-2 5-4 7-1 1-2 2-3 4v1h0c-1 2-3 3-4 5l-6 6h0c0-1-1-1-1-1l-5 7-6 13h-1l1-3c0-3 2-5 3-8 8-15 19-27 30-39 9-11 19-20 30-29l19-13c1 0 2 0 3-1 2-1 5-2 7-3z"></path><path d="M378 193c0-2 14-19 16-22-2 6-6 10-10 15l1 2-6 6h0c0-1-1-1-1-1z" class="b"></path><path d="M405 132l1 1v1l-3 3c0 4-2 6-4 9-1 1-2 1-2 2v3c-7 9-15 16-22 25-5 7-10 15-14 22-2 2-3 4-4 5h-2v-1c-1 0-3-1-4-2 1-1 2-1 3-1v-2l-1 1c-1 0-1 0-3-2 5-8 11-15 15-23 2-2 3-5 4-7 3-3 7-7 11-10 1-4 4-6 8-9 1-1 3-2 4-4 0-1 2-3 3-3l-2-2 3-3h1l3-1v-1 4h0l5-5z" class="C"></path><path d="M385 153c2-1 3-3 5-4h0l-1 2h1l-16 18v-2h1v-1c2-2 4-5 6-7 1-2 3-4 4-6z" class="S"></path><path d="M405 132l1 1v1l-3 3-13 14h-1l1-2h0c-2 1-3 3-5 4s-3 3-5 3c1-4 4-6 8-9 1-1 3-2 4-4 0-1 2-3 3-3l-2-2 3-3h1l3-1v-1 4h0l5-5z" class="k"></path><path d="M396 135h1l3-1c-2 2-3 4-5 6l-2-2 3-3z" class="h"></path><path d="M380 156c2 0 3-2 5-3-1 2-3 4-4 6-2 2-4 5-6 7v1h-1v2c-8 8-13 19-20 28l-1 1c-1 0-1 0-3-2 5-8 11-15 15-23 2-2 3-5 4-7 3-3 7-7 11-10z" class="P"></path><path d="M501 120h13 8c17 2 36 8 51 17 3 2 7 4 9 6 6 4 12 9 18 14l4 4c0 3 3 5 4 7 0 0 1 1 1 2 1 3 6 8 8 10 2 3 4 6 5 8 3 5 6 11 8 17h-1c0-1-1-3-1-4l-3-6c-2 0-3-2-5-3v1h-1v2c-1-1-1-1-2-1-1-1-1-2-2-2-6-7-12-14-19-20-21-19-48-33-77-35-36-3-69 9-97 32-7 6-14 14-20 21l-10 14-2 3-5 9c-1 3-3 5-4 7 1 1 1 2 0 3h1l-16 2c-1 0 0 0-1-1h4c2-1 1-3 2-5 2-3-1 2 1-1l1-1v-2l1-1h0l-1 4c1 1 0 2 1 3 0-1 0 0 1-1 0 0 0-1 1-1v-1-3c1-2 3-7 5-9 12-25 31-43 52-60h-1c-1 1 0 1-1 0h0l1-1h1c1 0 4-2 5-3 0 0 0-1 1-1 0-1 0-1 1-2l-2-1c4-3 8-6 13-8 13-7 28-10 43-12h4l3-1z" class="G"></path><path d="M431 157l1 1c-15 11-29 25-39 41-2 2-4 5-5 7v3h0l2-2-5 9c-1 3-3 5-4 7 1 1 1 2 0 3h1l-16 2c-1 0 0 0-1-1h4c2-1 1-3 2-5 2-3-1 2 1-1l1-1v-2l1-1h0l-1 4c1 1 0 2 1 3 0-1 0 0 1-1 0 0 0-1 1-1h0l1-1c3-2 4-6 6-9l7-13c11-16 25-31 41-42z" class="O"></path><path d="M381 223h-1c0-3 2-6 3-9l2 2c-1 3-3 5-4 7z" class="S"></path><path d="M388 206v3h0l2-2-5 9-2-2 5-8z" class="W"></path><path d="M431 157c9-7 19-13 30-17 21-9 45-11 68-7 28 4 56 21 76 41 4 4 7 8 10 12 2 2 3 4 5 6-1 0-3-2-4-3-6-7-12-14-19-21-20-17-43-29-69-33-34-6-69 3-96 23l-1-1z"></path><defs><linearGradient id="g" x1="502.223" y1="208.407" x2="488.357" y2="135.269" xlink:href="#B"><stop offset="0" stop-color="#989897"></stop><stop offset="1" stop-color="#bbbabb"></stop></linearGradient></defs><path fill="url(#g)" d="M432 158c27-20 62-29 96-23 26 4 49 16 69 33 7 7 13 14 19 21 1 1 3 3 4 3h0v1h-1v2c-1-1-1-1-2-1-1-1-1-2-2-2-6-7-12-14-19-20-21-19-48-33-77-35-36-3-69 9-97 32-7 6-14 14-20 21l-10 14-2 3-2 2h0v-3c1-2 3-5 5-7 10-16 24-30 39-41z"></path><path d="M501 120h13 8c17 2 36 8 51 17 3 2 7 4 9 6 6 4 12 9 18 14l4 4c0 3 3 5 4 7 0 0 1 1 1 2 1 3 6 8 8 10 2 3 4 6 5 8 3 5 6 11 8 17h-1c0-1-1-3-1-4l-3-6c-2-3-5-7-7-10-20-31-55-51-90-58-33-6-68 4-95 22h-1c-1 1 0 1-1 0h0l1-1h1c1 0 4-2 5-3 0 0 0-1 1-1 0-1 0-1 1-2l-2-1c4-3 8-6 13-8 13-7 28-10 43-12h4l3-1z" class="K"></path><path d="M494 121h4s-1 1-1 2v1h-2l-1-1v-2z" class="R"></path><path d="M501 120h13 8l-6 1c-3 2-9 0-13 2h-1l-1-3z" class="U"></path><path d="M582 143l-2 1c-2 0-9-5-12-6-2-1-3-2-5-2 0 0-1-1-2-1l-5-2h-2l1-1 2 1h3s1 1 2 1l2 1c3 0 5 1 7 3h1l1-1c3 2 7 4 9 6z" class="D"></path><path d="M609 101l1-2c2-5 6-10 9-15 1 1 2 3 2 4v2c1 1 1 1 2 1l1-1v2l1 1s1 0 1-1h2l1 1c-3 1-3 6-4 8l-1 7-1 3v3c1 4 2 8 4 11 3 7 8 13 15 17l7 5c4 3 7 5 10 8l16 21v-1l12 20 5 11 3 7h0v1h-4 0l1-1v-1c-1 1-2 1-3 1-1 1-1 1-2 1v1h-1-1c-3 1-6 0-9 2h0v1l2 3v1c-1 1-1 3-1 5 0 0 1 1 1 2 1 2 1 3 2 5-2 1-3 0-5 0 0 0-3-1-3-2-12-4-24-5-36-6l-1-2v-3h0c1-1-1-3-1-4v-1l-5-11h1c-2-6-5-12-8-17-1-2-3-5-5-8-2-2-7-7-8-10l16 17 1-2 3 3 2-1-9-11h1l3 3c0 1 1 2 2 3 0-2-3-5-4-6l1-1c2 2 4 5 6 8 4 7 9 14 13 21 2 3 3 6 4 9s2 5 5 7h0c-2-8-6-16-10-23-5-10-12-20-20-29l-20-21c-1-3-4-6-7-8h0l1-1-1-1-1-2h0c-2-3-1-8 0-11 1-2 1-4 2-5l-1 6c2 3 2 7 4 10h0 1v-5c-1-2-2-3-2-6h1l1 3h0 0c1-6 0-11 2-16l1 1v1c0 1 1 2 0 3v1h1v-3c1-6 2-11 4-16v3z" class="I"></path><path d="M636 201c3 3 5 6 7 11 0 1 0 2 1 2h1c1 3 3 7 3 10-1 0-1 0-2-1-1-3-1-6-3-8v3c-1-1-1-2-2-3v-1l-2-3c0-4-2-6-4-9l1-1z" class="i"></path><path d="M655 204c2 4 4 7 6 11-1 3 2 7 3 9h1l-1 1h-2c-2-2-3-7-5-10v-2c-1-1-1-2-2-3h0v-2-4z" class="U"></path><path d="M610 148c-4-2-10-8-12-12-2-3-3-7-2-11 2 3 2 7 4 10h0 1c2 5 7 8 9 13z" class="i"></path><path d="M629 188l2-1c2 3 5 7 7 10 3 6 5 12 7 17h-1c-1 0-1-1-1-2-2-5-4-8-7-11l-3-6c-1-3-3-5-4-7z" class="V"></path><path d="M658 204c1 1 2 1 3 1 1 1 1 3 1 4 3 2 4 8 5 12v1c-1 1 0 3 0 5h0c-2 0-3 0-5-2h2l1-1h-1c-1-2-4-6-3-9 0-3-3-7-4-10l2 2h0l-1-3z" class="c"></path><path d="M657 205l2 2h0c1 1 2 1 2 3h1c1 3 1 6 2 9 1 1 1 3 1 4v1h-1c-1-2-4-6-3-9 0-3-3-7-4-10z" class="Y"></path><path d="M601 135v-5c-1-2-2-3-2-6h1l1 3h0l1 1c0 1 1 2 1 3l-1 1c1 2 3 8 5 9h1s1 1 1 2c1 1 2 3 3 4l8 8c2 2 3 4 4 5 5 6 10 11 14 17 4 5 7 11 11 17v2c2 2 5 5 6 8v4l-4-7h0l2 6 2 6c-2-2-4-9-5-12l-8-14c-6-10-14-20-23-29l-9-10c-2-5-7-8-9-13z" class="F"></path><path d="M609 170l16 17 1-2 3 3c1 2 3 4 4 7l3 6-1 1c2 3 4 5 4 9l2 3v1 1c-1 2 0 5 0 7h-1-2v-1l-1 1c-1 0-1 0-2 1v-3h0c1-1-1-3-1-4v-1l-5-11h1c-2-6-5-12-8-17-1-2-3-5-5-8-2-2-7-7-8-10z" class="M"></path><path d="M638 214c-1-1-1-3-2-4l-3-9 6 10 2 3h-1 0l-2-1v1z" class="Q"></path><path d="M626 185l3 3c1 2 3 4 4 7l3 6-1 1-10-15 1-2z" class="I"></path><path d="M630 205c3 4 5 9 8 13h0v-4-1l2 1h0 1v1 1c-1 2 0 5 0 7h-1-2v-1l-1 1c-1 0-1 0-2 1v-3h0c1-1-1-3-1-4v-1l-5-11h1z" class="D"></path><path d="M659 194c0 2 1 3 1 5l1 2 2 2c1-2 6-4 8-6v-1l2 2h-2l1 1c0 1 0 1 1 2h-1l-2-1v2l2 4c1 3 2 7 3 10l1 1v1l2 3v1c-1 1-1 3-1 5 0 0 1 1 1 2 1 2 1 3 2 5-2 1-3 0-5 0 0 0-3-1-3-2v-2c-1-1-2-3-3-5 0-2-1-3-2-4-1-4-2-10-5-12 0-1 0-3-1-4-1-3-3-5-3-8 0-1 0-2 1-3z" class="R"></path><path d="M671 206h1c1 3 2 7 3 10v-1c-1 0-1-1-1-1l-1 1c-2-3-3-6-2-9z" class="C"></path><path d="M671 196l2 2h-2l1 1c0 1 0 1 1 2h-1l-2-1v2l2 4h-1c-1-2-2-2-3-3-2-1-3 0-5 0 1-2 6-4 8-6v-1z" class="J"></path><path d="M665 205h1c0 1 1 1 1 2l2 6 4 10c1 1 2 3 2 5v1 1c-2-3-3-7-4-10l-6-15z" class="B"></path><path d="M605 117v-3c1-6 2-11 4-16v3c-1 3-2 5-2 9 0 3-1 6-1 9 0 8 4 13 8 19l3 5c0-2 0-3-1-4-3-4-8-12-8-17 0 0 1 1 1 2v1c2 4 3 8 6 12 4 5 9 8 13 12 3 3 6 8 9 10l6 6c3 3 5 5 6 9 3 5 6 8 9 13 0 2 0 3 1 4 0 1 1 2 1 3l1 2v2l1 1c0 1 0 1-1 2l-1-2c0-2-1-3-1-5-1 1-1 2-1 3 0 3 2 5 3 8-1 0-2 0-3-1l1 3h0l-2-2c1 3 4 7 4 10-2-4-4-7-6-11-1-3-4-6-6-8v-2c-4-6-7-12-11-17-4-6-9-11-14-17-1-1-2-3-4-5l-8-8c-1-1-2-3-3-4 0-1-1-2-1-2h-1c-2-1-4-7-5-9l1-1c0-1-1-2-1-3l-1-1h0c1-6 0-11 2-16l1 1v1c0 1 1 2 0 3v1h1z" class="Z"></path><path d="M644 178c2 3 4 7 6 10l8 16 1 3h0l-2-2c-2 0-3-7-4-8-2-4-4-8-7-12v-1l-2-2v-1-2-1z" class="F"></path><path d="M605 117v-3c1-6 2-11 4-16v3c-1 3-2 5-2 9 0 3-1 6-1 9 0 8 4 13 8 19l3 5c1 1 1 1 3 2s6 6 8 9l9 9c3 3 6 7 9 11 0 5 4 9 4 14-2-3-4-7-6-10-12-18-31-30-38-52-1-3-1-6-1-9z" class="E"></path><path d="M617 143c0-2 0-3-1-4-3-4-8-12-8-17 0 0 1 1 1 2v1c2 4 3 8 6 12 4 5 9 8 13 12 3 3 6 8 9 10l6 6c3 3 5 5 6 9 3 5 6 8 9 13 0 2 0 3 1 4 0 1 1 2 1 3l1 2v2l1 1c0 1 0 1-1 2l-1-2c0-2-1-3-1-5-1 1-1 2-1 3 0 3 2 5 3 8-1 0-2 0-3-1l-8-16c0-5-4-9-4-14-3-4-6-8-9-11l-9-9c-2-3-6-8-8-9s-2-1-3-2z" class="b"></path><path d="M651 182l1-1-7-10 4 3c3 5 6 8 9 13 0 2 0 3 1 4 0 1 1 2 1 3l1 2v2l1 1c0 1 0 1-1 2l-1-2c0-2-1-3-1-5-3-4-6-7-8-12z" class="c"></path><path d="M646 174c2 3 4 5 5 8 2 5 5 8 8 12-1 1-1 2-1 3 0 3 2 5 3 8-1 0-2 0-3-1l-8-16c0-5-4-9-4-14z" class="I"></path><defs><linearGradient id="h" x1="673.514" y1="164.481" x2="632.464" y2="170.047" xlink:href="#B"><stop offset="0" stop-color="#21211e"></stop><stop offset="1" stop-color="#424347"></stop></linearGradient></defs><path fill="url(#h)" d="M609 101l1-2c2-5 6-10 9-15 1 1 2 3 2 4v2c1 1 1 1 2 1l1-1v2l1 1s1 0 1-1h2l1 1c-3 1-3 6-4 8l-1 7-1 3v3c1 4 2 8 4 11 3 7 8 13 15 17l7 5c4 3 7 5 10 8l16 21v-1l12 20 5 11 3 7h0v1h-4 0l1-1v-1c-1 1-2 1-3 1-1 1-1 1-2 1v1h-1-1c-3 1-6 0-9 2h0l-1-1c-1-3-2-7-3-10l-2-4v-2l2 1h1c-1-1-1-1-1-2l-1-1h2l-2-2v1c-2 2-7 4-8 6l-2-2c1-1 1-1 1-2l-1-1v-2l-1-2c0-1-1-2-1-3-1-1-1-2-1-4-3-5-6-8-9-13-1-4-3-6-6-9l-6-6c-3-2-6-7-9-10-4-4-9-7-13-12-3-4-4-8-6-12v-1c0-1-1-2-1-2 0 5 5 13 8 17 1 1 1 2 1 4l-3-5c-4-6-8-11-8-19 0-3 1-6 1-9 0-4 1-6 2-9z"></path><path d="M658 165v1 3l-1 1-3-5h4z" class="d"></path><path d="M658 166c1 1 2 4 3 5s3 2 5 3c3 2 6 3 8 6l-1 1c-2-3-5-4-7-6-4-1-7-2-9-5l1-1v-3z" class="F"></path><path d="M641 153c1-1 2-1 3-1 1 1 3 3 4 3 1 2 3 3 5 4l4 4 1 2h-4l-1-1-12-11z" class="d"></path><path d="M657 163l1 2h-4l-1-1c1-1 3-1 4-1z" class="V"></path><path d="M637 159c2-1 2 0 4 0h3c4 4 8 9 12 14-2-1-3-3-5-4-2 0-3-2-4-3s-2-1-4-1l-6-6z" class="W"></path><path d="M616 136c3 4 6 7 10 10 1 1 2 2 3 2l-5-6h0v-1c6 7 13 12 20 18h-3c-2 0-2-1-4 0-3-2-6-7-9-10-4-4-9-7-13-12l1-1z" class="P"></path><defs><linearGradient id="i" x1="659.123" y1="168.085" x2="672.884" y2="166.473" xlink:href="#B"><stop offset="0" stop-color="#9b9d9c"></stop><stop offset="1" stop-color="#c0bcbd"></stop></linearGradient></defs><path fill="url(#i)" d="M659 155l16 21 2 5h-1 0l-2-1c-2-3-5-4-8-6-2-1-4-2-5-3s-2-4-3-5v-1l-1-2-4-4-2-2 1-1c1 2 3 4 5 5l-4-5 2 1h2c1 1 0 1 2 1v-2-1z"></path><path d="M653 156l2 1c4 4 6 8 9 12 4 4 9 7 12 12h0l-2-1c-2-3-5-4-8-6-2-1-4-2-5-3s-2-4-3-5v-1l-1-2-4-4-2-2 1-1c1 2 3 4 5 5l-4-5z" class="K"></path><path d="M643 165c2 0 3 0 4 1s2 3 4 3c2 1 3 3 5 4 1 1 1 2 2 3s3 2 4 4h0c3 0 4 0 7 2 1 2 2 3 3 5h0c1 2 1 4 1 6-1 1-1 2-2 3v1c-2 2-7 4-8 6l-2-2c1-1 1-1 1-2l-1-1v-2l-1-2c0-1-1-2-1-3-1-1-1-2-1-4-3-5-6-8-9-13-1-4-3-6-6-9z" class="j"></path><path d="M666 195v-1c1-5-4-6-4-11l6 9s1 1 1 2 1 1 1 2l1 1c-2 2-7 4-8 6l-2-2c1-1 1-1 1-2l-1-1v-2l-1-2c0-1-1-2-1-3-1-1-1-2-1-4 1 3 3 6 5 10 0 1 1 2 2 3l1-1v-1-3z" class="B"></path><path d="M666 195h2c1 1 1 1 1 2l-3 1v-3z" class="O"></path><path d="M662 180c3 0 4 0 7 2 1 2 2 3 3 5h0c1 2 1 4 1 6-1 1-1 2-2 3v1l-1-1c0-1-1-1-1-2s-1-2-1-2v-2c-1-3-4-7-6-10z" class="L"></path><path d="M643 165c2 0 3 0 4 1s2 3 4 3c2 1 3 3 5 4 1 1 1 2 2 3l-1 1s0 1-1 1v1c3 5 7 10 9 15l-1 2 1 1h0-2c-2-4-4-7-5-10-3-5-6-8-9-13-1-4-3-6-6-9z" class="H"></path><path d="M651 169c2 1 3 3 5 4 1 1 1 2 2 3l-1 1s0 1-1 1v1-1l-5-9z" class="D"></path><path d="M675 175l12 20 5 11 3 7h0v1h-4 0l1-1v-1c-1 1-2 1-3 1-1 1-1 1-2 1v1h-1-1c-3 1-6 0-9 2h0l-1-1c-1-3-2-7-3-10l-2-4v-2l2 1h1c-1-1-1-1-1-2l-1-1h2l-2-2c1-1 1-2 2-3 2-2 4-4 5-6l-5-6 1-1 2 1h0 1l-2-5v-1z" class="V"></path><path d="M674 180l2 1h0 1c1 2 3 4 3 6v2c0 2 2 4 2 6l-1-2c-1-2-2-3-2-5h0l-1-1-5-6 1-1z" class="H"></path><path d="M682 195c0-2-2-4-2-6v-2l8 16 2 4-3 3h-1l-3-6 3 3h1 0l-2-5c-1-3-2-4-3-6v-1z" class="d"></path><path d="M675 175l12 20 5 11 3 7h0v1h-4 0l1-1v-1l-2-5-2-4-8-16c0-2-2-4-3-6l-2-5v-1z" class="f"></path><path d="M688 203v-2l1 2 3 3 3 7h0v1h-4 0l1-1v-1l-2-5-2-4z" class="K"></path><path d="M675 195s1-1 2 0c1 0 1 1 2 2l1 1 3 6 3 6h1l3-3 2 5c-1 1-2 1-3 1-1 1-1 1-2 1l-2-2c-1 0-2 0-3-1h-2c0-1-1-2-1-3-1-3-2-6-4-8-1-1-1-1-1-2l2 2 1-1c-1-2-1-2-3-3l-1-1h2z" class="W"></path><path d="M675 195c4 4 7 12 10 17-1 0-2 0-3-1h-2c0-1-1-2-1-3-1-3-2-6-4-8-1-1-1-1-1-2l2 2 1-1c-1-2-1-2-3-3l-1-1h2z" class="B"></path><path d="M674 196c2 1 2 1 3 3l-1 1-2-2c0 1 0 1 1 2 2 2 3 5 4 8 0 1 1 2 1 3h2c1 1 2 1 3 1l2 2v1h-1-1c-3 1-6 0-9 2h0l-1-1c-1-3-2-7-3-10l-2-4v-2l2 1h1c-1-1-1-1-1-2l-1-1h2l1-2z" class="j"></path><path d="M672 206l-2-4v-2l2 1v3l1 1c1 2 1 4 2 5l1 1c0 1 1 1 1 2 1 0 1 0 1 1h0l1-1-1-1c1-1 1-1 1 0v-4c0 1 1 2 1 3h2c1 1 2 1 3 1l2 2v1h-1-1c-3 1-6 0-9 2h0l-1-1c-1-3-2-7-3-10z" class="Y"></path><path d="M680 211h2c1 1 2 1 3 1l2 2v1h-1-5c1-2 0-3-1-4z" class="R"></path><path d="M609 101l1-2c2-5 6-10 9-15 1 1 2 3 2 4v2c1 1 1 1 2 1l1-1v2l1 1s1 0 1-1h2l1 1c-3 1-3 6-4 8l-1 7-1 3v3c1 4 2 8 4 11 3 7 8 13 15 17l7 5c4 3 7 5 10 8v1 2c-2 0-1 0-2-1h-2l-2-1 4 5c-2-1-4-3-5-5l-1 1 2 2c-2-1-4-2-5-4-1 0-3-2-4-3-1 0-2 0-3 1-5-4-9-8-14-12-4-3-8-7-11-12v-1h0c1 5 5 9 8 13v1h0l5 6c-1 0-2-1-3-2-4-3-7-6-10-10l-1 1c-3-4-4-8-6-12v-1c0-1-1-2-1-2 0 5 5 13 8 17 1 1 1 2 1 4l-3-5c-4-6-8-11-8-19 0-3 1-6 1-9 0-4 1-6 2-9z" class="T"></path><path d="M616 136c-3-4-5-9-6-15v-1c-1-8 0-16 3-23l1-1c1-3 3-6 5-8h1c-6 9-10 23-8 33v1 1c2 7 6 14 12 19h0l5 6c-1 0-2-1-3-2-4-3-7-6-10-10zm5-46h0c1 1 1 1 2 1-1 3-2 6-3 10-3 11 0 21 6 31s18 15 26 24l-1 1c-11-12-30-20-34-38-2-10-1-19 4-29z" class="D"></path><path d="M609 101l1-2c2-5 6-10 9-15 1 1 2 3 2 4v2h0l-1-2h-1c-2 2-4 5-5 8l-1 1c-3 7-4 15-3 23v1c1 6 3 11 6 15l-1 1c-3-4-4-8-6-12v-1c0-1-1-2-1-2 0 5 5 13 8 17 1 1 1 2 1 4l-3-5c-4-6-8-11-8-19 0-3 1-6 1-9 0-4 1-6 2-9z" class="V"></path><path d="M623 91l1-1v2l1 1s1 0 1-1h2l1 1c-3 1-3 6-4 8l-1 7-1 3v3c1 4 2 8 4 11 3 7 8 13 15 17l7 5c4 3 7 5 10 8v1 2c-2 0-1 0-2-1h-2l-2-1 4 5c-2-1-4-3-5-5-8-9-20-14-26-24s-9-20-6-31c1-4 2-7 3-10z" class="F"></path><path d="M649 147c4 3 7 5 10 8v1 2c-2 0-1 0-2-1h-2l-2-1h0l-7-6v-1h1c1 0 2 1 3 2 1 0 1 1 2 1v-2c-1 0-2-1-3-2v-1z" class="T"></path><path d="M621 101c1 4 1 9 2 13s2 8 4 11l-2 2h-1c-2-4-2-8-3-11-1-5-1-10 0-15z" class="h"></path><path d="M624 92l1 1s1 0 1-1h2l1 1c-3 1-3 6-4 8l-1 7-1 3v3c-1-4-1-9-2-13 2-3 2-6 3-9z" class="j"></path><defs><linearGradient id="j" x1="256.924" y1="343.252" x2="272.65" y2="399.009" xlink:href="#B"><stop offset="0" stop-color="#0d0c0d"></stop><stop offset="1" stop-color="#2e3335"></stop></linearGradient></defs><path fill="url(#j)" d="M315 323c0-2-1-4-1-6h0v-1l1 1c0 4 3 8 4 12l1-1h1l1-1c1 1 1 0 1 1 3 3 7 5 10 7l1 1h1c0-1 0-2 1-3l-3-1c1-1 2-1 3-2v-2l1 1h2s1 0 2 1c1-1 1-2 2-3h0l1-1v-1l2-2 1-3h4v-1l1 1c2 1 5 3 6 5s3 3 4 5c-2 1-2 0-2 1v2 1l1 2 3 3-3 5h0c0 1 0 1 1 2 0 2 0 4 1 6 0 3 1 5 1 8 0 2-1 5-2 7h0c-2 7-2 13-2 20v11h0l-1-1c-1-2-3-3-4-4 0 2 4 4 5 6v6c-1 7-3 13-5 19-2 7-6 13-10 18-3 4-8 8-12 11-1 1-2 2-4 2h-2l-3-2-1 2c-2 2-4 3-7 4h-4l-1-1c-1-3-2-7-4-10 0-1 0-3-1-4l-2-9h4l1-2s0-1-1-1l3 1h1v-1c-1 0-2-1-3-2 1-1 2-2 4-3-3 1-4 1-7 0-3 1-7 1-10 1l-14 1h-13-2l3 2-21-1h-5c-3 0-5 0-8-1-2-1-6 0-8 0-3-1-7-1-9 0h-1c-1 1-2 2-3 4l-2-1v-1l-2-1c0-2 0-4 1-7 0 0 0-1-1-1-2 0-5 1-7 3h-1-1v-1l-1-1 10-4-5-6v-1c0-2 1-2 2-3-2-1-3 0-5 0-5-1-8-1-12-4-2-1-3-2-4-3 2-1 4-2 5-3 2-1 4-4 6-6 3-4 6-8 7-13 1-3 2-6 2-9l-12-3c-6-1-14-1-20-1h-1c3-2 5-5 8-7 4-2 9-4 13-5 10-3 20-4 30-6l1 2 7-1h-3-1l1-4c0-1 1-3 1-4 3 0 3 0 5 2l5-1h0 7c0 1 0 1 1 1 3 1 6 3 9 2 1 0 1-1 2-1 1-2 2-3 3-4h1c-2-3-4-5-6-7s-4-3-5-4l-6-3h-3l1-2 4 1h0c1 1 2 1 4 1 0-2-2-3-3-4 1 0 2 1 3 1h1l1-1 1 1-1 1v2h1l2-4h0l-2-2 1-1h2c1 1 2 0 4 1 1 0 2 1 3 2h1c3 1 6 3 7 6h1 1s1 0 1 1l1 1c-1 2-3 3-4 5h-1-1l6 5c3-2 5-6 8-8 1-2 3-2 5-2 1-1 2-2 3-2l1-2 2-4c1 1 2 2 3 2l1-1h4c2-2 4-4 5-6h1v1l1 1h-1v1c1 0 2 1 4 2z"></path><path d="M220 369v-1c0-1 1-1 1-2l1-1v2c1 0 2 1 2 1l1 1c-2 1-3 0-5 0z" class="B"></path><path d="M292 356c3-1 9 0 13 0l1 6c-1-2-3-3-5-4h-2c-1-2-5-1-7-2z" class="K"></path><path d="M309 370c-1-4-1-8-1-11h1l1 3c1 0 1 0 2 1v-1l1-1c0 1 3 3 3 4v1 2h-1-1l3 5-5-5h-1l-1-2h-1v4z" class="Q"></path><path d="M310 362c1 0 1 0 2 1v-1l1-1c0 1 3 3 3 4v1 2h-1c-1-1-1-2-2-3v1h-1c-1-2-1-2-3-3v-1h1z" class="D"></path><path d="M328 361l-2-3h0c3 3 6 6 8 9l5 13v1c0 1 0 1 1 2h0c2 0 3 0 5 1l-1 1c-3 0-7 0-10-1h-5l1-1s0-1 1-1h1v-1c-1-2-2-3-3-4s0-2 0-3-2-3-2-3l1-1c1 2 3 5 4 8 1 2 1 3 3 4 1 0 2 1 3 0-2-7-5-15-10-21z" class="d"></path><path d="M232 368h1 1l2-1c2-1 3-2 4-3 2-2 5-3 7-5l1 1h-1c-1 1 0 1-1 2l1 1 3-2h0c-1 2-2 3-4 4l-4 4h1l1 1h3l-2 1h-20c2-1 5-1 7-2h-1v-1h1z" class="I"></path><path d="M243 369c2-2 4-4 6-5 1-1 3-2 5-3 1-1 2-2 4-3 3 0 7 1 10 0h3c-2 2-4 3-5 4v1l-5 3v-1c-3 2-6 5-9 6h-7l2-1h-3l-1-1z" class="E"></path><path d="M323 371l1 1 1 1v-1l-2-2 1-1 2 2c-1-1-1-2-2-3v-2h-1l1-1c2 1 3 3 4 5l-1 1s2 2 2 3-1 2 0 3 2 2 3 4v1h-1c-1 0-1 1-1 1l-1 1h-4c-5-1-8-2-12-4 0-1-2-2-3-3s-1-5-1-6v-1-4h1l1 2h1l5 5-3-5h1 1v-2-1c1 1 3 3 4 5h0 1c1 0 1 0 2 1z" class="U"></path><path d="M316 365c1 1 3 3 4 5h0 1c1 0 1 0 2 1h-1l1 3v1c-2-2-3-5-5-6-1 1 0 2 0 2l-1 1c-1-2-1-3-2-4h1v-2-1z" class="Q"></path><path d="M326 381l1-1v-1l-1 1c-2-1-2-2-3-3 1 0 2 1 2 1h1l-1-2h0c2 1 2 3 4 4 0 0 2 0 3 1v1h-1c-1 0-1 1-1 1h-4v-2z" class="f"></path><path d="M309 370v-4h1l1 2h1l5 5-3-5h1c1 1 1 2 2 4 0 1 0 1 1 2s1 1 1 3c2 1 5 3 5 5l-1 1h1 1v-3h0l1 1v2h4l-1 1h-4c-5-1-8-2-12-4 0-1-2-2-3-3s-1-5-1-6v-1z" class="g"></path><path d="M315 368c1 1 1 2 2 4 0 1 0 1 1 2s1 1 1 3c0 1 0 1 1 2h0l-7-7c-1-2-1-3-1-4l5 5-3-5h1z" class="b"></path><defs><linearGradient id="k" x1="224.01" y1="371.676" x2="251.498" y2="372.565" xlink:href="#B"><stop offset="0" stop-color="#59595a"></stop><stop offset="1" stop-color="#818182"></stop></linearGradient></defs><path fill="url(#k)" d="M278 358h1c-2 2-4 3-5 4s-1 1-2 1l-5 6h-1 0c-1 2-1 2-3 3h-7v1h6c-4 1-9 1-13 1h-24c-7-1-14-5-21-7l-3-1c-1 0-1 0-2-1h-2-1c-1 0-1 0-3-1 2-1 2-1 4-1l4 1c1 0 1 0 2 1 1 0 5 0 7 1h4l4-4-2 5c1 1 2 1 4 2 2 0 3 1 5 0s4-3 7-4c-1 1-4 3-5 4 5-1 8-5 13-7-3 2-5 4-8 6h-1v1h1c-2 1-5 1-7 2h20 7c3-1 6-4 9-6v1l5-3v-1c1-1 3-2 5-4h7z"></path><path d="M278 358h1c-2 2-4 3-5 4s-1 1-2 1-4 3-5 4c-2 1-3 2-5 3 3-3 7-6 9-9-3 2-7 5-10 8v1c-1-2 5-5 5-7v-1c1-1 3-2 5-4h7z" class="Q"></path><path d="M278 358h0c2 0 3-1 5 0 1 0 1-1 2-1 1-1 5 0 7-1 2 1 6 0 7 2h2c2 1 4 2 5 4 0 2 0 4 1 6v6h0l-1 2-1-1h0-3l-15-1-25-1h-6v-1h7c2-1 2-1 3-3h0 1l5-6c1 0 1 0 2-1s3-2 5-4h-1z" class="H"></path><path d="M291 362l2 1h0c1 2 5 5 5 7h1c0 1 1 1 1 2v1 1l-2-1-1-1v-1h-4v-1c-3 0-4-1-6-2h-1c-2-1-3-1-3-3 1-1 2-2 4-3 1 0 1 0 2 1l2-1z" class="V"></path><path d="M291 362l2 1h0c1 2 5 5 5 7h1c0 1 1 1 1 2v1 1l-2-1-1-1v-1h-4v-1h2v-1-1c-1-1-2-4-4-4l-1 1c-1 0-3 0-3-1l1-1-1-1c1 0 1 0 2 1l2-1z" class="F"></path><path d="M272 370c3 1 6 0 9 0 4 0 8 1 12 1h4v1l1 1 2 1c1 0 1 0 2 1l-15-1-25-1h-6v-1h7 7s2-1 2-2z" class="M"></path><path d="M278 358h0c2 0 3-1 5 0 1 0 1-1 2-1 1-1 5 0 7-1 2 1 6 0 7 2h2c2 1 4 2 5 4 0 2 0 4 1 6v6h0l-1 2-1-1h0-3c-1-1-1-1-2-1v-1-1c0-1-1-1-1-2h-1c0-2-4-5-5-7h0l-2-1-4-2c-1 0-2 0-4 1h0v-1c-3 0-4 3-6 3-1 1-2 1-2 2-1 2-2 2-4 3-1 0-1 1-2 2h1 1 1 0c0 1-2 2-2 2h-7c2-1 2-1 3-3h0 1l5-6c1 0 1 0 2-1s3-2 5-4h-1z" class="c"></path><path d="M298 362l2-2c1 1 2 1 2 2 1 1 1 1 1 2h0c1 2 1 2 1 4 1 1 1 1 1 2s1 3 2 4l-1 2-1-1h1l-1-1c-1-3-1-4-3-5 0-1 0-1 1-2h0c-1-1-1-1-1-2v-1l-4-2z" class="Y"></path><path d="M267 369c2-1 4-2 5-4l1-1c2-1 3-3 6-4v1l-2 2c-1 1-2 1-2 2-1 2-2 2-4 3-1 0-1 1-2 2h1 1 1 0c0 1-2 2-2 2h-7c2-1 2-1 3-3h0 1z" class="T"></path><path d="M287 360l2-1 2 1 1-1c1 1 1 1 3 1h2c0 1 0 1 1 2l4 2v1c0 1 0 1 1 2h0c-1 1-1 1-1 2 2 1 2 2 3 5l1 1h-1 0-3c-1-1-1-1-2-1v-1-1c0-1-1-1-1-2h-1c0-2-4-5-5-7h0l-2-1-4-2z" class="S"></path><path d="M293 363l1-1 2 2h1c4 2 4 6 6 9l1 1 1 1h-3c-1-1-1-1-2-1v-1-1c0-1-1-1-1-2h-1c0-2-4-5-5-7z" class="P"></path><path d="M264 341h2c-1 1-1 2-1 3l2-2 1 1v1c1 0 1 0 1 1l1-1 1 1 2-1 1 1 1-1 1 1s1 0 2 1h0c2-2 4-3 6-5l1 1h0 1 1l-3 3 1 2h0l-2 2 5-1h8l-1-1 7-5h1c0-1 0 0 1-1 0 1 1 2 1 2l2 2h1v-1l2 3c1 0 2 0 3-1 1 1 2 1 2 2l1 1v1h-1v1 1c1 1 3 2 3 3-8-2-14-2-22-2h-24c-27 0-54 0-80 8l-6 3h-5c3-4 8-5 13-7 10-3 21-5 32-6l7-1h-3-1l1-4c0-1 1-3 1-4 3 0 3 0 5 2l5-1h0 7c0 1 0 1 1 1 3 1 6 3 9 2 1 0 1-1 2-1 1-2 2-3 3-4h1z" class="n"></path><path d="M295 347l7-5h1c0-1 0 0 1-1 0 1 1 2 1 2l2 2h1v-1l2 3c1 0 2 0 3-1 1 1 2 1 2 2l1 1v1h-1v1 1c1 1 3 2 3 3-8-2-14-2-22-2h-24l2-1c1-1 3 0 5 0-1 0-2-1-2-1h16c2 0 5-1 6 0h1 1v-1h-6c-2-1-5-1-7-2h8l-1-1z" class="Z"></path><path d="M295 347l7-5h1c0-1 0 0 1-1 0 1 1 2 1 2l2 2h1v-1l2 3c1 0 2 0 3-1 1 1 2 1 2 2l1 1c-2 0-5-1-7-1h-13l-1-1z" class="B"></path><defs><linearGradient id="l" x1="250.819" y1="341.693" x2="277.72" y2="353.718" xlink:href="#B"><stop offset="0" stop-color="#131315"></stop><stop offset="1" stop-color="#3a3a3b"></stop></linearGradient></defs><path fill="url(#l)" d="M264 341h2c-1 1-1 2-1 3l2-2 1 1v1c1 0 1 0 1 1l1-1 1 1 2-1 1 1 1-1 1 1s1 0 2 1h0c2-2 4-3 6-5l1 1h0 1 1l-3 3 1 2h0l-2 2h-41l-9 1h-3-1l1-4c0-1 1-3 1-4 3 0 3 0 5 2l5-1h0 7c0 1 0 1 1 1 3 1 6 3 9 2 1 0 1-1 2-1 1-2 2-3 3-4h1z"></path><path d="M285 342h0 1 1l-3 3-1 1s-1 0-2 1v-2l4-3z" class="E"></path><path d="M267 342l1 1c0 1-1 3-1 4h-6-1l1-1h2c1-1 2-1 2-2l2-2z" class="c"></path><path d="M264 341h2c-1 1-1 2-1 3s-1 1-2 2h-2c0-1-1-1-1-1 1-2 2-3 3-4h1z" class="K"></path><path d="M231 342c3 0 3 0 5 2l5-1h0 7c0 1 0 1 1 1l-2 1c1 1 3 1 4 2-2 1-4 1-5 1h-11c2 1 5 1 7 1h0l-9 1h-3-1l1-4c0-1 1-3 1-4z" class="o"></path><path d="M241 343h0 7c0 1 0 1 1 1l-2 1h-8c1 0 2-1 3-1l-1-1z" class="X"></path><path d="M231 342c3 0 3 0 5 2l5-1 1 1c-1 0-2 1-3 1-3 1-5 1-7 3 1 1 2 0 3 0 2 1 5 1 7 1h0l-9 1h-3-1l1-4c0-1 1-3 1-4z"></path><path d="M231 342c3 0 3 0 5 2l-6 2c0-1 1-3 1-4z" class="M"></path><path d="M305 324c2-2 4-4 5-6h1v1l1 1h-1v1c1 0 2 1 4 2h0v1l-1 1v2l-1 1h1l1 1v1c0 1-1 2-1 3v1c-1 3-3 5-5 8l-2 3-2-2s-1-1-1-2c-1 1-1 0-1 1h-1l-7 5 1 1h-8l-5 1 2-2h0l-1-2 3-3h-1-1 0l-1-1c-2 2-4 3-6 5h0c-1-1-2-1-2-1l-1-1-1 1-1-1-2 1-1-1-1 1c0-1 0-1-1-1v-1l-1-1-2 2c0-1 0-2 1-3h-2c-2-3-4-5-6-7s-4-3-5-4l-6-3h-3l1-2 4 1h0c1 1 2 1 4 1 0-2-2-3-3-4 1 0 2 1 3 1h1l1-1 1 1-1 1v2h1l2-4h0l-2-2 1-1h2c1 1 2 0 4 1 1 0 2 1 3 2h1c3 1 6 3 7 6h1 1s1 0 1 1l1 1c-1 2-3 3-4 5h-1-1l6 5c3-2 5-6 8-8 1-2 3-2 5-2 1-1 2-2 3-2l1-2 2-4c1 1 2 2 3 2l1-1h4z" class="I"></path><path d="M290 340v-2l1 1v-1c0-1 0-1 1-1h0l1 1 2 1h0l2 2c-4-1-3 1-6 2v-3h-1z" class="K"></path><path d="M268 342l2-3c2 1 3 3 5 5l-1 1-1-1-2 1-1-1-1 1c0-1 0-1-1-1v-1l-1-1h1z" class="f"></path><path d="M267 342h1 1c1 0 3 0 3 1l1 1-2 1-1-1-1 1c0-1 0-1-1-1v-1l-1-1z" class="C"></path><path d="M297 323c1 1 2 2 3 2 0 2 0 3 1 5l1 2c-1 0-1 1-2 1v1l-1-1c-2 0-2 1-3 2h0c-1-1-1-1-2-1-1-2-1-2-3-3 1-1 2-2 3-2l1-2 2-4z" class="E"></path><path d="M294 329l1-2c2 1 2 1 3 2v2h-2c-2 0-2-1-2-2z" class="B"></path><path d="M301 336c0 1 1 2 1 3-1 0-2-1-2-2l-1 1v3h-1c-1 1-2 1-2 3v1h0-1-1l-1 1 1 1h1l1 1h-8l-5 1 2-2h0l-1-2 3-3c1 0 1-1 2-1h1v-1h1v3c3-1 2-3 6-2v-1c1-2 0-3 2-4h1 1z" class="c"></path><path d="M258 323h0l-2-2 1-1h2c1 1 2 0 4 1 1 0 2 1 3 2h1c3 1 6 3 7 6h1 1s1 0 1 1l1 1c-1 2-3 3-4 5h-1-1v-2c-1-1-2-1-3-2v-1h-1v-3l-3 4c-2 0-3-1-5-2l1-1h-1 0l-2-1c0-2 0-2 1-4l1-1-1 1-1-1z" class="S"></path><path d="M277 330l1 1c-1 2-3 3-4 5h-1-1v-2c0-1 1-1 1-2l1-1 1 1c1 0 2-1 2-2zm28-6c2-2 4-4 5-6h1v1l1 1h-1v1c1 0 2 1 4 2h0v1l-1 1v2l-1 1h1l1 1v1c0 1-1 2-1 3v1c-1 3-3 5-5 8l-2 3-2-2s-1-1-1-2c-1 1-1 0-1 1h-1l-7 5h-1l-1-1 1-1h1 1 0v-1c0-2 1-2 2-3h1v-3l1-1c0 1 1 2 2 2 0-1-1-2-1-3l-1-2h0v-1c1 0 1-1 2-1l-1-2c-1-2-1-3-1-5l1-1h4z" class="D"></path><path d="M301 324h4c-2 2-3 4-4 6-1-2-1-3-1-5l1-1z" class="P"></path><path d="M315 323v1l-1 1c-1 0-2 0-4 1l1 2-1 1-1-1v-1-1h1c-1-1-2 0-3 0-1 1-1 3-2 4h-1c1-3 1-4 3-6h1l7-1z" class="L"></path><path d="M314 325v2l-1 1h1l1 1v1c0 1-1 2-1 3-1 1-2 3-3 4h-3c0-3-2-5-3-7 1-1 1-3 2-4 1 0 2-1 3 0h-1v1 1l1 1 1-1-1-2c2-1 3-1 4-1z" class="E"></path><path d="M314 325v2l-1 1h1l1 1v1c-2 0-2 1-3 2h-2c-2-1-2-1-2-3l1-1 1 1 1-1-1-2c2-1 3-1 4-1z" class="C"></path><path d="M304 330h1c1 2 3 4 3 7h3c1-1 2-3 3-4v1c-1 3-3 5-5 8l-2 3-2-2s-1-1-1-2c-1 1-1 0-1 1h-1l-7 5h-1l-1-1 1-1h1 1 0v-1c0-2 1-2 2-3h1v-3l1-1c0 1 1 2 2 2 0-1-1-2-1-3l-1-2c2 1 2 1 3 2l1 1v-7z" class="S"></path><path d="M304 330h1c1 2 3 4 3 7h3l-1 2-3 3h-1v-1l-1-2c-1 0-1-1-1-2v-7z" class="Y"></path><path d="M306 341v-4-1h1c0 1 1 2 2 2l1 1-3 3h-1v-1z" class="K"></path><defs><linearGradient id="m" x1="233.177" y1="360.942" x2="234.45" y2="388.268" xlink:href="#B"><stop offset="0" stop-color="#0b0a0a"></stop><stop offset="1" stop-color="#27282a"></stop></linearGradient></defs><path fill="url(#m)" d="M191 405c-2-1-3-2-4-3 2-1 4-2 5-3 2-1 4-4 6-6 3-4 6-8 7-13 1-3 2-6 2-9l-12-3c-6-1-14-1-20-1h-1c3-2 5-5 8-7 4-2 9-4 13-5 10-3 20-4 30-6l1 2c-11 1-22 3-32 6-5 2-10 3-13 7h5l6-3c1 1 1 2 1 3h0c2 1 2 1 3 1h1 2c1 1 1 1 2 1l3 1c7 2 14 6 21 7h24c4 0 9 0 13-1l25 1v1c1 0 2 1 4 1 0 1 0 2 1 3h-1v1c1 0 1 0 2 2h-42-3-1c-1 2-1 3-2 5l-3 6-1-1h-2c-1 1-1 1-2 1l1 2-1 2c0 1-1 2-1 3v1 1h-14-9c-2 1-5 0-7 0h-4c-2 0-11 0-12 1l2 2h-1z"></path><path d="M211 376v1c1 1 3 1 5 1v1c-2 2-3 3-4 5-1 1-3 2-4 4l-3 1c3-4 5-8 6-13z" class="K"></path><path d="M231 391c1-1 2-2 2-4 1 0 1-1 1-1 0-1 0-1 1-2v-1h0c3-2 10-1 13-1h-1c-1 2-1 3-2 5l-3 6-1-1h-2c-1 1-1 1-2 1h-3l1-2v-1c-2 1-2 1-4 1z" class="d"></path><path d="M239 392c1 0 2-2 2-3 0-2 2-4 2-6v-1h4c-1 2-1 3-2 5l-3 6-1-1h-2z" class="J"></path><path d="M234 381l1 1h-1v2c-1 2-3 4-3 7h0c2 0 2 0 4-1v1l-1 2h3l1 2-1 2c0 1-1 2-1 3v1 1h-14v-1c1 0 2 0 3-1 2-1 4-5 4-7 0 0 0-1-1-1v-1h-1l1-1c2-2 3-5 4-8 1-1 1-1 2-1z" class="S"></path><path d="M233 396h4 0l-2 1-1 1c-1 1-2 2-3 2h-2-1l1-3 4-1z" class="J"></path><path d="M234 381l1 1h-1v2c-1 2-3 4-3 7h0c2 0 2 0 4-1v1l-1 2h3l1 2h-5v1l-4 1 1-3 1-1h-2s0-1-1-1v-1h-1l1-1c2-2 3-5 4-8 1-1 1-1 2-1z" class="Z"></path><path d="M221 379l1-1h2c2 1 4 1 6 1s3 0 4 2c-1 0-1 0-2 1-1 3-2 6-4 8l-1 1c-3 0-6 1-8 0h-2c0-2 3-8 4-10v-1-1z" class="Y"></path><path d="M219 391h3v-2c2 0 4 1 6 1l-1 1c-3 0-6 1-8 0z" class="i"></path><path d="M221 379l1-1h2c2 1 4 1 6 1s3 0 4 2c-1 0-1 0-2 1h-1 0-3c-2 0-3 1-4 3h-1v-3l-2-2v-1z" class="I"></path><path d="M196 400l-4 1h0c6-4 12-12 15-19l3-6h1c-1 5-3 9-6 13 0 0-1 1 0 1 1 1 0 1 1 1h8l7-12v1 1c-1 2-4 8-4 10h2c2 1 5 0 8 0h1v1c1 0 1 1 1 1 0 2-2 6-4 7-1 1-2 1-3 1v1h-9c-2 1-5 0-7 0h-4l-1-1c-1-1-3-1-5-1z" class="J"></path><path d="M200 395l3-3h6 3c-1 1-2 3-3 5l-1-2h-2c-2-1-3-1-6 0z" class="D"></path><path d="M200 395c3-1 4-1 6 0h2l1 2-3 5h-4l-1-1c-1-1-3-1-5-1l2-1 2-4z" class="S"></path><path d="M200 395c3-1 4-1 6 0-3 2-6 3-8 4l2-4z" class="K"></path><path d="M213 402c-1-1-4 0-5 0l7-9h11c-1 3-2 5-4 8v1h-9z" class="Q"></path><path d="M222 395h2v1l-1 1c-3 3-6 3-10 3h-2l2-4 9-1z" class="W"></path><path d="M309 371c0 1 0 5 1 6s3 2 3 3c4 2 7 3 12 4h-1c-2 1-4 1-6 1v1c1 1 2 3 4 3 0 1 1 2 2 2v4c1 1 1 1 1 2l2 2-2 2h-1 0c-3 1-5 1-8 1h-2-25 0-13-10-1-29v-1-1c0-1 1-2 1-3l1-2-1-2c1 0 1 0 2-1h2l1 1 3-6c1-2 1-3 2-5h1 3 42c-1-2-1-2-2-2v-1h1c-1-1-1-2-1-3-2 0-3-1-4-1v-1l15 1h3 0l1 1 1-2h0 1c0-2 0-2 1-3z" class="N"></path><path d="M290 402v-5h6c2 0 2 0 4 2-2 0-3 1-4 2-2 1-4 1-6 1z" class="C"></path><path d="M276 402l2-1 1-4h8l2 5h-13z" class="L"></path><path d="M300 399c0 1 1 2 2 2h1c-1-1-1-3-2-4h7 3c-1 1-2 2-2 3v2h7-2-25 1c2 0 4 0 6-1 1-1 2-2 4-2z" class="E"></path><path d="M248 382h3c0 1 1 1 1 2l-1 1v1s0 1-1 1c0 1-1 1-1 2l1 1h0c-1 1-2 2-2 3h-3v1c1 0 1 0 2 1-2 0-2-1-4-1l-1-1 3-6c1-2 1-3 2-5h1z" class="V"></path><path d="M265 402c0-1-1-3 0-5s0-4 1-6l1-6c1-1 1-2 2-3v1c0 4-1 8-1 12 2 0 5 0 6 1v5c-2 0-4-1-5 0h-1l1-1v-3h-1c0 1 0 1-1 3h0c0 1 0 1-1 2h-1z" class="m"></path><path d="M286 385c2-1 3-2 6-1-2 3-3 6-3 10l-1-1v-1h-1l-1 2h-7c0-3 0-7 2-9 1 0 2-1 3 0h1 1z" class="S"></path><path d="M286 385c2-1 3-2 6-1-2 3-3 6-3 10l-1-1v-1h-1l-1 2v-2c0-1 1-1 1-2-2-1-4-2-5-3l1-1h3l-1 1v1h2v-1l-1-2z" class="a"></path><path d="M292 384h7v2l1 4v4h-1l-2 1c-2 0-6 1-8-1 0-4 1-7 3-10z" class="L"></path><path d="M299 394h-1c-1-1-2-2-3-2-1 1-1 2-2 2h-2c0-1 0-2 1-2 0-1 5-3 6-3 0 0 1 1 2 1v4h-1z" class="c"></path><path d="M309 371c0 1 0 5 1 6s3 2 3 3c4 2 7 3 12 4h-1c-2 1-4 1-6 1v1c1 1 2 3 4 3 0 1 1 2 2 2v4c1 1 1 1 1 2l2 2-2 2h-1 0c-3 1-5 1-8 1h-7v-2c0-1 1-2 2-3h-3c0-1-1-1-1-2h-4 0c-2 0-2 0-3-1v-4l-1-4v-2h1 4c1 0 1-1 2-1-4-2-9-1-13-1-1-2-1-2-2-2v-1h1c-1-1-1-2-1-3-2 0-3-1-4-1v-1l15 1h3 0l1 1 1-2h0 1c0-2 0-2 1-3z" class="I"></path><path d="M309 384h5l2 1h0l-1 2c1 1 2 1 3 2s0 0 0 1v3h4c0 2-1 3-1 5-3-3-5-7-7-10-2-2-3-3-5-4z" class="K"></path><path d="M306 383c1 0 2 0 3 1 2 1 3 2 5 4 2 3 4 7 7 10l1 1h-1c-2 1-2 1-4 1l-1-1-1-2h-4-3c0-1-1-1-1-2h-4 0c-2 0-2 0-3-1v-4l-1-4v-2h1 4c1 0 1-1 2-1z" class="h"></path><path d="M316 399l1-1c2 0 2 0 4 1-2 1-2 1-4 1l-1-1zm-7-9c1 1 2 3 2 4v1h-3v-1l1-4z" class="b"></path><path d="M300 384c3 1 5 1 7 1l-1 1h-1l1 1c1 1 2 2 1 4v4h-4c0-1 1-2 2-3-1-1-1-3-1-4-2-2-2-2-5-2v-2h1z" class="D"></path><path d="M299 386c3 0 3 0 5 2 0 1 0 3 1 4-1 1-2 2-2 3h0c-2 0-2 0-3-1v-4l-1-4z" class="C"></path><path d="M307 385c2 0 3 0 4 2 2 2 3 5 4 8-1 1-2 0-4 0h0v-1c0-1-1-3-2-4l-3-4 1-1z" class="K"></path><path d="M222 402h14 29 1 10 13 0 25c0 2-1 3-2 4h1c1 1 2 1 4 2v1l-1 1c-1 0-2-1-4-1l3 3v2l1 1c1 0 1 1 2 2-1 1-2 1-3 2v1h5 1l-8 7c-3 1-4 1-7 0-3 1-7 1-10 1l-14 1h-13-2l3 2-21-1h-5c-3 0-5 0-8-1-2-1-6 0-8 0-3-1-7-1-9 0h-1c-1 1-2 2-3 4l-2-1v-1l-2-1c0-2 0-4 1-7 0 0 0-1-1-1-2 0-5 1-7 3h-1-1v-1l-1-1 10-4-5-6v-1c0-2 1-2 2-3-2-1-3 0-5 0-5-1-8-1-12-4h1l-2-2c1-1 10-1 12-1h4c2 0 5 1 7 0h9z" class="X"></path><path d="M284 417h0c1 1 1 1 2 1h0c1-1 2-2 3-2s1 0 2 1c-1 0-3 3-4 3h-1 0c-1-1-2-2-2-3z" class="E"></path><path d="M252 421v-3h1c0-2 1-2 2-3 1 1 0 1 0 2h2v2h0l-3 3c-1 0-1 0-2-1zm16-3v1h1v-2h1 0 4l1 1-1 2v1h0c-1 0-2 0-2 1h0l-2-1h-1-1l-1-3h1z" class="R"></path><path d="M224 416l1 1-2 5h-1v2h-1c-1 0-2-1-3-2 0-2-1-2-2-3l1-1 2 2c0-1 1-2 2-2v1 1c1-1 2-3 3-4z" class="Q"></path><path d="M224 416c0-1 1-2 2-3h0l1 1h0c-1 2-2 4-2 6l1-1 1-1c1-2 2-2 3-3 1 1 1 1 1 3-1 0-1 1-2 2h0-1c0 1-1 2-2 3h0v-3l-2 3c-1 0-1-1-1-1l2-5-1-1z" class="R"></path><path d="M312 406h1c1 1 2 1 4 2v1l-1 1c-1 0-2-1-4-1l-2-2h-7c-3 0-24 1-25 0h22c4-1 8 0 12-1z" class="I"></path><path d="M260 422l2-1v-2c1-1 1-1 1-2h1v3h1c0-2 1-2 2-4l1-1h0v3h-1l1 3h1 1l2 1-2 1c-1 0 0 0-1-1h-1 0v1 1h-2c-1-1-1-2-3-3 0 1 0 2-1 3l-2-2z" class="C"></path><path d="M279 417h1c2-1 1-1 2-3h0c1 1 2 1 2 3 0 1 1 2 2 3h0 1l-3 5c-2-3-4-5-5-8z" class="B"></path><path d="M315 420h5 1l-8 7c-3 1-4 1-7 0h0c3-2 6-5 9-7z" class="j"></path><path d="M298 411c3-1 7-1 10-1l2 2c1 1 1 2 1 4-2 3-5 5-9 5-2 1-4 1-6 2l-1 1h-1-1c-1 1-2 2-4 1-2 0-3 0-5 1h-2 0-5l1-1v-3l-2-2-1 1c1 1 2 1 2 3l-1 1c-1 0-1 0-2-1l-2-2-1 1v1l-1-1 2-1h0c0-1 1-1 2-1h0v-1l1-2c1 1 1 1 3 1 0-1-1-2-1-3h1l1 1c1 3 3 5 5 8l3-5c1 0 3-3 4-3 1-2 2-3 4-4 1-1 2-1 3-2z" class="c"></path><path d="M298 411c3-1 7-1 10-1l2 2c-2 2-1 4-2 6-1-1-1-3-2-4-1 0-1 1-1 2-2 1-3 2-5 1h0l-2 2h-1v-1c-1 1-3 2-4 3h0l-2 2v-2l3-3v-1c-1 1-2 2-3 2 0 1 0 1-1 1 0-2 3-5 5-6v-1c1-1 2-1 3-2z" class="E"></path><path d="M298 411c3-1 7-1 10-1l2 2c-2 2-1 4-2 6-1-1-1-3-2-4l-1-1c-1 0-1 0-2 1l1 1v1h-1c-1-2-1-3-2-4s-2-1-3-1z" class="C"></path><path d="M208 409v1s1 1 1 2h0c2 1 3 3 4 4 1 2 3 4 5 6 1 1 2 2 3 2h1v-2h1s0 1 1 1l2-3v3h0c1-1 2-2 2-3h1 0c0 1 0 1 1 2h1c0-2 1-3 2-5 0 2 0 4-1 6h1c1-1 1-2 2-3 0-1 0-2 1-3v-1c0-1 0-1 2-1 1 1 0 2 2 3l1-3v-1c2 2 2 4 4 4l3-6c-1 3-3 8-2 10 1 1 2 0 3 0h2l1-1c1 1 1 1 2 1l3-3h0v1c0 1 0 1-1 2h2v-7h0c2 2-1 5 2 7l2 2c1-1 1-2 1-3 2 1 2 2 3 3h2v-1-1h0 1c1 1 0 1 1 1l1 1v-1l1-1 2 2c1 1 1 1 2 1l1-1c0-2-1-2-2-3l1-1 2 2v3l-1 1-56-1c-4-2-8-7-11-10v-1c-1 0-1-1-2-1l-1-1-1 1v-1c0-2 1-2 2-3z" class="Y"></path><path d="M241 415v-1c2 2 2 4 4 4-1 2-2 4-4 5-1-2 0-4 0-7v-1zm-5 1c0-1 0-1 2-1 1 1 0 2 2 3-1 3-3 5-5 7 0-2 1-6 1-8v-1z" class="B"></path><path d="M206 413l1-1 1 1c1 0 1 1 2 1v1c3 3 7 8 11 10l56 1h5 0 2c2-1 3-1 5-1 2 1 3 0 4-1h1 1l1-1c0 1 1 2 2 3 2 0 2 1 4 1h4 0c-3 1-7 1-10 1l-14 1h-13-2l3 2-21-1h-5c-3 0-5 0-8-1-2-1-6 0-8 0-3-1-7-1-9 0h-1c-1 1-2 2-3 4l-2-1v-1l-2-1c0-2 0-4 1-7 0 0 0-1-1-1-2 0-5 1-7 3h-1-1v-1l-1-1 10-4-5-6z" class="O"></path><path d="M213 431c1-1 2-3 2-5h1c1 1 1 1 2 3-1 1-2 2-3 4l-2-1v-1z" class="i"></path><path d="M236 429h31l3 2-21-1h-5c-3 0-5 0-8-1z" class="F"></path><g class="l"><path d="M222 402h14 29 1 10 13 0 25c0 2-1 3-2 4-4 1-8 0-12 1h-22l-50-1h-24c-4 0-8 0-12-1l-2-2c1-1 10-1 12-1h4c2 0 5 1 7 0h9z"></path><path d="M315 323c0-2-1-4-1-6h0v-1l1 1c0 4 3 8 4 12l1-1h1l1-1c1 1 1 0 1 1 3 3 7 5 10 7l1 1h1c0-1 0-2 1-3l-3-1c1-1 2-1 3-2v-2l1 1h2s1 0 2 1c1-1 1-2 2-3h0l1-1v-1l2-2 1-3h4v-1l1 1c2 1 5 3 6 5s3 3 4 5c-2 1-2 0-2 1v2 1l1 2 3 3-3 5h0c0 1 0 1 1 2 0 2 0 4 1 6 0 3 1 5 1 8 0 2-1 5-2 7h0c-2 7-2 13-2 20v11h0l-1-1c-1-2-3-3-4-4 0 2 4 4 5 6v6c-1 7-3 13-5 19-2 7-6 13-10 18-3 4-8 8-12 11-1 1-2 2-4 2h-2l-3-2-1 2c-2 2-4 3-7 4h-4l-1-1c-1-3-2-7-4-10 0-1 0-3-1-4l-2-9h4l1-2s0-1-1-1l3 1h1v-1c-1 0-2-1-3-2 1-1 2-2 4-3l8-7h-1-5v-1c1-1 2-1 3-2-1-1-1-2-2-2l-1-1v-2l-3-3c2 0 3 1 4 1l1-1v-1c-2-1-3-1-4-2h-1c1-1 2-2 2-4h2c3 0 5 0 8-1h0 1l2-2-2-2c0-1 0-1-1-2v-4c-1 0-2-1-2-2-2 0-3-2-4-3v-1c2 0 4 0 6-1h1 4 5c3 1 7 1 10 1l1-1c-2-1-3-1-5-1h0c-1-1-1-1-1-2v-1l-5-13c-2-3-5-6-8-9h0l2 3c-3 0-3-2-6-3-1-1-2-2-4-3 0-1-2-2-3-3v-1-1h1v-1l-1-1c0-1-1-1-2-2-1 1-2 1-3 1l-2-3v1h-1l2-3c2-3 4-5 5-8v-1c0-1 1-2 1-3v-1l-1-1h-1l1-1v-2l1-1v-1h0z"></path></g><path d="M347 435c0-1 1-4 2-5 1-4 2-8 3-11 0-4-1-7 0-10v-4l1 2h1l1 2c0 1 0 1 2 1 0-1-2-3-3-4l1-1c1 1 2 3 3 3v-2c1-1 1-1 2-1-1 7-3 13-5 19-2 7-6 13-10 18v-1c1-1 2-3 3-5l-1-1z" class="J"></path><path d="M347 360h3c1 2 1 3 1 5h2l1 3h1c0 1 0 1 1 2v-1l-1-2v-2c1 0 1 0 1 1 1-1 1-2 1-3l-2-2c1-1 1-1 2-1 0 2 1 6 2 8l1-1h1 1c-2 7-2 13-2 20-1-1-2-1-2-2-1-1-2-2-2-3l-1-1c-1-1-1-2-1-4-1 0-1-1-1-1v-1l-1-1-2-2v-4c-1-2-1-3 0-6 0-1 0 0-1-1h-2-1l1-1z" class="m"></path><path d="M353 345l3 2 1 1v1 4c1 2 0 5 0 7-1 0-1 0-2 1l2 2c0 1 0 2-1 3 0-1 0-1-1-1v2l1 2v1c-1-1-1-1-1-2h-1l-1-3h-2c0-2 0-3-1-5h-3l-1 1c-1-2-2-4-3-5v-2l1-1c1-2 3-4 5-6h1c1 0 2-1 3-2z" class="N"></path><path d="M353 345l3 2c-2 1-3 1-4 3 0-1-1-2-2-3 1 0 2-1 3-2z" class="J"></path><path d="M343 356v-2l1-1c1-2 3-4 5-6h1c1 1 2 2 2 3 1 2 1 3 0 5v-3c-2 2-3 6-5 8l-1 1c-1-2-2-4-3-5z" class="P"></path><path d="M351 320v-1l1 1c2 1 5 3 6 5s3 3 4 5c-2 1-2 0-2 1v2 1l1 2 3 3-3 5h0c0 1 0 1 1 2 0 2 0 4 1 6 0 3 1 5 1 8 0 2-1 5-2 7h0-1-1l-1 1c-1-2-2-6-2-8s1-5 0-7v-4-1l-1-1-3-2c-1 1-2 2-3 2h-1c-2 2-4 4-5 6l-1 1v2h-1c-2-2-2-3-2-5l-5-5c-1 0-1-1-1-2v-1l-2-1s4-4 4-5l-1-1c0-1 0-2 1-3l-3-1c1-1 2-1 3-2v-2l1 1h2s1 0 2 1c1-1 1-2 2-3h0l1-1v-1l2-2 1-3h4z" class="G"></path><path d="M344 325l3 3v1h-2c-1 0-1 0-2-1v-1l1-1v-1z" class="l"></path><path d="M343 348h1v1l-1 1 1 1-1 1h-2l-1-1 2-2 1-1z" class="n"></path><path d="M351 320c1 1 1 2 1 3-1 1-2 1-3 0h0c-1 1-2 1-3 0l1-3h4z" class="l"></path><path d="M344 335l4 3-1 1c0 1-1 1-2 1l-2 2-2-3h1c1-2 1-3 2-4zm14-10c1 2 3 3 4 5-2 1-2 0-2 1v2 1c-1-1-2-2-3-2 0-2-1-3-1-4h-1l3-3z" class="J"></path><path d="M346 331l5-7v1l4 4-5 6-5-2 1-2z" class="Y"></path><path d="M341 339l2 3 2 2c0 1-1 2-2 3v1l-1 1-2 2h0l-5-5 3-3 3-4z" class="E"></path><path d="M338 343v2c1 0 1-1 2 0l1 1c0 1 0 1 1 3l-2 2h0l-5-5 3-3z" class="C"></path><path d="M357 332c1 0 2 1 3 2l1 2 3 3-3 5v-2l-2 1c-2-1-4-3-5-5l3-6z" class="D"></path><path d="M361 336l3 3-3 5v-2l-2 1v-3c1-1 2-2 2-3v-1z" class="T"></path><path d="M353 345c-1-1-2-2-2-3 1 0 1 0 2 1 3 2 7 7 9 10v1c1 2-2 10-2 13l-1 1c-1-2-2-6-2-8s1-5 0-7v-4-1l-1-1-3-2z" class="C"></path><path d="M336 328l1 1 3 3 1-2 3 1h2l-1 2v1l-1 1c-1 1-1 2-2 4h-1l-3 4-3 3c-1 0-1-1-1-2v-1l-2-1s4-4 4-5l-1-1c0-1 0-2 1-3l-3-1c1-1 2-1 3-2v-2z" class="m"></path><path d="M344 331h2l-1 2v1c-1 0-2 0-3-1 1-1 1-2 2-2zm-4 2c1 1 1 1 1 3 0 1-1 1-2 1s-1-1-1-2l2-2z" class="l"></path><path d="M336 328l1 1 3 3v1l-2 2c-1-1-2-1-2-2l-3-1c1-1 2-1 3-2v-2z" class="D"></path><path d="M315 323c0-2-1-4-1-6h0v-1l1 1c0 4 3 8 4 12l1-1h1l1-1c1 1 1 0 1 1 3 3 7 5 10 7l1 1h1l1 1c0 1-4 5-4 5-1 1-1 1-3 1h-1l-2 1v1l1 1c1 1 2 2 3 4 6 5 13 12 14 19v1c1 3 2 6 2 8h-2v1c0 2 0 3-1 4-1-2-2-3-2-6l-2 3-5-13c-2-3-5-6-8-9h0l2 3c-3 0-3-2-6-3-1-1-2-2-4-3 0-1-2-2-3-3v-1-1h1v-1l-1-1c0-1-1-1-2-2-1 1-2 1-3 1l-2-3v1h-1l2-3c2-3 4-5 5-8v-1c0-1 1-2 1-3v-1l-1-1h-1l1-1v-2l1-1v-1h0z" class="O"></path><path d="M342 368l1 1h0v1h1c1 3 2 6 2 8h-2l-1-5c-1-2-1-3-1-5z" class="D"></path><path d="M334 367h2c2 3 3 7 5 10l-2 3-5-13z" class="c"></path><path d="M314 334l1-1c1 0 1 1 1 2 1 2 2 4 3 5v1l-2-1c0-2 0-3-1-4h-1l-1 2-1 2c1 0 2 1 3 2v2h-1v1c-1-1-1-1-2-1-1-1-1 0-2-1-1 0-2-1-2-1 2-3 4-5 5-8z" class="K"></path><path d="M316 350l3 1 1 1c1 0 2 1 3 2 1 0 3 3 3 4h0l2 3c-3 0-3-2-6-3-1-1-2-2-4-3 0-1-2-2-3-3v-1-1h1z" class="C"></path><path d="M314 338l1-2h1c1 1 1 2 1 4l2 1v-1l2 2c3 4 6 6 9 8 6 5 13 12 14 19v1h-1v-1h0l-1-1c-1-3-2-4-4-6-1-2-2-4-4-6-3-3-8-7-11-9-4-3-6-6-9-9z" class="Q"></path><path d="M315 323c0-2-1-4-1-6h0v-1l1 1c0 4 3 8 4 12l1-1h1l1-1c1 1 1 0 1 1 3 3 7 5 10 7l1 1h1l1 1c0 1-4 5-4 5-1 1-1 1-3 1h-1l-2 1v1l1 1c1 1 2 2 3 4-3-2-6-4-9-8l-2-2c-1-1-2-3-3-5 0-1 0-2-1-2l-1 1v-1c0-1 1-2 1-3v-1l-1-1h-1l1-1v-2l1-1v-1h0z" class="U"></path><path d="M326 338c-1-2-2-3-2-6h2c0 1 1 1 2 2 0 1 1 1 2 2 1 0 2 0 3-1l1 1c0 1-1 1-1 2-2 1-2 1-3 1-2-1-2-1-3-1h-1z" class="C"></path><path d="M335 336l1 1c0 1-4 5-4 5-1 1-1 1-3 1h-1l-4-3 2-2h1c1 0 1 0 3 1 1 0 1 0 3-1 0-1 1-1 1-2h1z" class="D"></path><path d="M326 338h1c1 2 2 3 2 5h-1l-4-3 2-2z" class="j"></path><path d="M321 333l3 7 4 3-2 1v1l1 1c1 1 2 2 3 4-3-2-6-4-9-8l1-1c-1-2-2-4-2-5v-1h1v-1-1z" class="G"></path><path d="M315 323c0-2-1-4-1-6h0v-1l1 1c0 4 3 8 4 12l1-1h1l1-1c1 1 1 0 1 1l-2 4v1 1 1h-1v1c0 1 1 3 2 5l-1 1-2-2c-1-1-2-3-3-5 0-1 0-2-1-2l-1 1v-1c0-1 1-2 1-3v-1l-1-1h-1l1-1v-2l1-1v-1h0z" class="N"></path><path d="M320 328h1l1-1c1 1 1 0 1 1l-2 4v1 1h-1l-1-5 1-1z" class="a"></path><path d="M341 377c0 3 1 4 2 6 1-1 1-2 1-4v-1h2c1 7 1 13 1 20 0 13 0 25-4 38 0 1 1 2 1 3 2-1 3-3 3-4l1 1c-1 2-2 4-3 5v1c-3 4-8 8-12 11-1 1-2 2-4 2h-2l-3-2-1 2c-2 2-4 3-7 4h-4l-1-1c-1-3-2-7-4-10 0-1 0-3-1-4l-2-9h4l1-2s0-1-1-1l3 1h1v-1c-1 0-2-1-3-2 1-1 2-2 4-3l8-7h-1-5v-1c1-1 2-1 3-2-1-1-1-2-2-2l-1-1v-2l-3-3c2 0 3 1 4 1l1-1v-1c-2-1-3-1-4-2h-1c1-1 2-2 2-4h2c3 0 5 0 8-1h0 1l2-2-2-2c0-1 0-1-1-2v-4c-1 0-2-1-2-2-2 0-3-2-4-3v-1c2 0 4 0 6-1h1 4 5c3 1 7 1 10 1l1-1c-2-1-3-1-5-1h0c-1-1-1-1-1-2v-1l2-3z" class="B"></path><path d="M339 443c2-2 3-5 4-7 0 1 1 2 1 3 2-1 3-3 3-4l1 1c-1 2-2 4-3 5h-1 0c-1 2-2 3-3 3l-2-1z" class="F"></path><path d="M317 408c3 0 12 1 14-1v4c-2 0-5 0-7 1h0c-1-1-1-1-2-1h-1c-1-1-2-1-2-1l-2-1v-1z" class="R"></path><path d="M339 443l2 1c1 0 2-1 3-3h0 1v1c-3 4-8 8-12 11-1 1-2 2-4 2h-2c1-1 2-1 3-3l9-9z" class="S"></path><path d="M312 409c2 0 3 1 4 1l1-1 2 1c1 1 2 3 4 4 0 1 2 2 2 3h-1v1h1v1c1 0 2 1 4 2 1 1 2 1 3 2h0c1 0 1 0 2 1h1l1 1h-1 0-3v1s-1 0-2 1h-4c0-1-1-1 0-2 1 0 2 0 3 1l1-1c-1-1-2-1-3-1l-2-1h0l-1 1v-1l-3-3h-1-5v-1c1-1 2-1 3-2-1-1-1-2-2-2l-1-1v-2l-3-3z" class="D"></path><path d="M315 412c1 1 2 2 4 3 1 1 2 2 2 3l-1 1h-5c1-1 2-1 3-2-1-1-1-2-2-2l-1-1v-2z" class="B"></path><path d="M323 403l2 1h2 1v-1-3c1-1 1-2 3-2 0 1 1 1 1 1v5s0 1 1 1h1c1 0 2 0 3 1l1 1c-2 1-5 0-7 0-2 2-11 1-14 1-2-1-3-1-4-2h-1c1-1 2-2 2-4h2c3 0 5 0 8-1 0 1 0 1-1 2z" class="c"></path><path d="M324 401c0 1 0 1-1 2l-2 1c-2 2-5 2-8 2h-1c1-1 2-2 2-4h2c3 0 5 0 8-1z" class="G"></path><path d="M323 403l2 1h2 1v-1-3c1-1 1-2 3-2 0 1 1 1 1 1v5s0 1 1 1h1c-1 1-2 1-3 1-2 1-6 1-8 1 0-1-1-2-2-3l2-1z" class="I"></path><path d="M325 384h4 5c0 1-2 2-2 2v9l1 1c0 1 0 2-1 3 0 0-1 0-1-1-2 0-2 1-3 2v3 1h-1-2l-2-1c1-1 1-1 1-2h0 1l2-2-2-2c0-1 0-1-1-2v-4c-1 0-2-1-2-2-2 0-3-2-4-3v-1c2 0 4 0 6-1h1z" class="E"></path><path d="M324 391l1 2 2-1 1 1h2l1 1v2l-3 2s-1 0-1 1l-2-2c0-1 0-1-1-2v-4z" class="C"></path><path d="M321 420l3 3v1l1-1h0l2 1c1 0 2 0 3 1l-1 1c-1-1-2-1-3-1-1 1 0 1 0 2v1c0 1 0 1-1 2s-1 1-1 3l1-1 2-1c1 0 2 0 3 1 1-2 3-4 6-5 1 0 2 1 3 1h0l1 1v2 1c0 1-1 3-1 4l-1 1c0 3-2 5-3 7l-7 8h2c-1 2-2 2-3 3l-3-2-1 2c-2 2-4 3-7 4h-4l-1-1c-1-3-2-7-4-10 0-1 0-3-1-4l-2-9h4l1-2s0-1-1-1l3 1h1v-1c-1 0-2-1-3-2 1-1 2-2 4-3l8-7z" class="Y"></path><path d="M327 442c0-1 0 0 1-1l1-1v-1c0-1 1-1 2-2l1 1c-1 1-1 2-1 4l1 1-4 4c-1-1-1-1-1-2l1-2-1-1z" class="b"></path><path d="M339 428h0c0 2 0 3-1 4-1 2-1 4-2 6l-1 2v2h-1 0-2v1l-1-1c0-2 0-3 1-4h2 0 1v-4h0c0-1 0-1-1-2l-2 2v-1c1-1 2-3 4-4h2l1-1z" class="E"></path><path d="M332 433v1l2-2c1 1 1 1 1 2h0v4h-1 0-2l-1-1c-1 1-2 1-2 2v1l-1 1c-1 1-1 0-1 1l-1 1c0 1 0 1-1 1l-1 2h0c1 1 1 2 1 3v1c0 1 0 2 1 3h1l1-1h2c-1 2-2 2-3 3l-3-2c1 0-4-6-5-7l13-13z" class="Q"></path><path d="M330 432c1-2 3-4 6-5 1 0 2 1 3 1l-1 1h-2c-2 1-3 3-4 4l-13 13c1 1 6 7 5 7l-1 2-4-6-2-4 1-2h-2c0-1 2-1 3-2-2-3-5-5-8-8h1c3 2 6 6 9 8l9-9z" class="H"></path><path d="M322 427l1-1c1 1 2 2 3 2 0 1 0 1-1 2s-1 1-1 3l1-1 2-1c1 0 2 0 3 1l-9 9c-3-2-6-6-9-8v-1c1 0 1-1 2-1 1 1 1 2 1 3l3 1 1-1v-1-1c0-2 1-4 3-5z" class="E"></path><path d="M318 435l1-1v-1-1c0-2 1-4 3-5 0 2 1 2 1 4l-2 2s-1 1-1 2h-2z" class="C"></path><path d="M321 420l3 3v1l1-1h0l2 1c1 0 2 0 3 1l-1 1c-1-1-2-1-3-1-1 1 0 1 0 2v1c-1 0-2-1-3-2l-1 1c-2 1-3 3-3 5v1 1l-1 1-3-1c0-1 0-2-1-3-1 0-1 1-2 1s-2-1-3-2c1-1 2-2 4-3l8-7z" class="O"></path><defs><linearGradient id="n" x1="306.841" y1="434.963" x2="315.213" y2="450.182" xlink:href="#B"><stop offset="0" stop-color="#1e2023"></stop><stop offset="1" stop-color="#3d3e3f"></stop></linearGradient></defs><path fill="url(#n)" d="M308 432l3 1c3 3 6 5 8 8-1 1-3 1-3 2h2l-1 2 2 4 4 6c-2 2-4 3-7 4h-4l-1-1c-1-3-2-7-4-10 0-1 0-3-1-4l-2-9h4l1-2s0-1-1-1z"></path><path d="M308 442c2 1 3 0 5 1h0 1v1l-1 1h-3c-1-1-1-1-2-3z" class="D"></path><path d="M319 449l4 6c-2 2-4 3-7 4h-4l-1-1 2-2c0-1 1-2 1-3l1-1 1-1v1h1 0c1-1 2-2 2-3z" class="Q"></path><path d="M313 456l2-2h1c0 1 1 1 0 2v3h-4l-1-1 2-2z" class="k"></path><path d="M793 455l1 3h1 2c3 1 9 0 13 0l1-1h1 1l2 1c4-1 8 0 11-1 0 0 1-1 2-1 2 2 5 4 8 6l-42 1c1 4 1 7 1 10v4c0 9-3 14-9 21v3c-5 3-10 6-14 10-3 4-4 11-6 15l-5 13c-2 5-5 9-7 13-1 2-3 5-4 7 0 5 1 9 0 13-1 3-1 6-1 9l-2 2 3 16c0 1 0 2-1 3v4c-1 7 1 12 3 18 0 1 0 2-1 3v1l-3-3c-6-8-6-16-6-26h-1c0-1 0-2-1-3l-3 15c0-1 0-2-1-3v-2h0c0 2 0 2-2 4 1-5 2-12 0-17-4 4-7 6-13 5h-4c-6 5-8 11-12 17-9 14-19 26-31 36-6 6-13 8-20 11-3 0-7 1-10 3-1 2-2 3-3 5v-3 1c-1 1-2 2-4 3-6 5-15 8-23 9l-2 1c-1-1-2-1-3-1s0-1-2-1v5c1 3 1 4 3 7l-2 1 2 1h-1c-2 1-7-2-10-3-3-3-5-7-6-12-2-11 4-18 10-26v-1l1-2c-1 1-2 1-3 1-6 5-10 12-12 19h-1c-1 1-1 3-2 5h0v-2c-1-1-1-2-1-3l1-3 1-4h-3c1-4 2-6 4-9 4-4 7-7 12-10h0v-1c1-1 2-3 4-4 1 0 1 0 1-1v-1h0l3-2 5-2h1c2 0 4-3 5-5l1-1 2-2s0 1 1 1l1-1-1-4c1 1 2 1 2 2 1 1 1 2 2 3v-1h1l-1-4c0-4 1-6 3-9 1-1 1-2 2-3s1-1 1-2l-2-1h-1l1-1-1-1h-1-4l-1-1-3-3h-1-2c-2 0-4-1-6-1h0 0c-2 0-2 0-4-1l-1-2 5-3h1l2-1v-3h3 1c1-1 2-1 3-2v-1c1-1 1-2 2-3l8-8c1-1 3-2 5-4l5-5 5-5c21-22 37-47 47-75l1-2 1-3v-2l2-5 3-8-1-1v-2l6 1-1-2c1-1 2-1 3-2h1l16 1h1 4c4 0 9-1 13 0h1c2 0 8 1 10 0h0 3 3c1 0 2-1 3-1h1c2 0 10 1 12-1h1 0v-1-1c0 1 0 0 1 1 0 1 0 1 1 2 3 0 9 0 12-1 0-1 1-2 2-3z" class="O"></path><path d="M761 487c0 1 0 3-1 4s-2 1-3 2c1-2 2-3 3-4l1-2h0z" class="K"></path><path d="M734 585l1 7h0c-2-1-2-1-3-3 1-2 1-3 2-4zm29-108c1-1 1-2 2-3v-1c1-2 2-3 3-4h1l-1 2c0 2-1 3-2 5-1 0-2 0-3 1z" class="C"></path><path d="M700 481h1 3c0-1 1-1 1-2 0 2-2 8-3 10-1-1-1-2-1-4s0-3-1-4z" class="Q"></path><path d="M717 481l3-3v1c0 2-2 5-1 7 0 1-1 2-1 3-1-1-1-1-1-3v-5z" class="C"></path><path d="M726 492v-2c0-1 0-1 1-2 0-1 1-2 1-3v-1-1-1l1 1-1 1h1c1 3 1 9 0 12v-6h-1v3c-1 0-1-1-2-1z" class="B"></path><path d="M698 552l1 3v1c-1 1-3 3-4 5-1 0-2 1-3 1l6-10z" class="T"></path><path d="M714 560l6-11c0 2 0 5-1 7l-3 6c0-1 0-1-1-2h-1z" class="S"></path><path d="M645 640l3-2h-1c4-2 7-5 10-8l1 1c-3 4-7 8-12 9h-1z" class="D"></path><path d="M698 473c1 2 1 3 0 5h1v-1c0-1 1-1 1-2 1 3 0 4 0 7v-1c1 1 1 2 1 4 0-1-1-1-2-2l-1-1h0-1v-2c0-1 0-1-1-2l2-5z" class="U"></path><path d="M701 465l1-1h0c0 1 0 5 1 6l1-1c0 1-1 2-2 4-1 0-1 2-2 2 0 1-1 1-1 2v1h-1c1-2 1-3 0-5l3-8z" class="B"></path><path d="M677 581c0 3-1 4-3 6 0 1-1 1-1 2s0 2-1 3v-1h0c-2 2-2 4-3 6l-2-2-3 1 13-15z" class="W"></path><path d="M719 573c2-2 3-4 6-5l1 1v1c-4 4-5 9-8 15v-4c0-2 1-2 0-4v-1c1-1 1-1 1-3z" class="T"></path><path d="M642 565h3l1-1c3-1 4-4 8-4-2 2-3 3-6 5-3 1-7 6-10 8h-1v-3l5-5z" class="U"></path><path d="M714 560h1c1 1 1 1 1 2v1c0 1 0 3-1 4v-3c-2 2-4 4-5 6l-5 8-1-2 10-16z" class="i"></path><path d="M769 481l-1-1c1-1 1-1 1-2v-1-2c1-2 1-4 3-6 0 0 1 0 2-1h1l-2 8v2l-1 2h-1 0l-2 6v-1-4z" class="B"></path><path d="M637 570v3l-5 4c-2 1-4 3-6 5 1 1 2 1 3 1l-3 3-3 2-1-2v-1c1-1 1-2 2-3l8-8c1-1 3-2 5-4z" class="D"></path><path d="M626 582c1 1 2 1 3 1l-3 3-3 2-1-2v-1l1 1 3-4z" class="C"></path><path d="M658 631c1-1 1-2 2-3 0 3-1 4-1 6l2 1h-1c0 1-1 1-1 2l-1 1h-1c-1 1-2 1-3 1 0 1-1 1-1 1l-6 2c-1 0-2 1-4 2v-1c1-1 2-1 2-2h-4c1 0 3 0 4-1h1c5-1 9-5 12-9z" class="j"></path><path d="M712 524h0c0-1 0-2 1-2l1-2 1-4c1-3 2-5 3-7 2-3 3-6 5-8v1 2l-3 6h0l-5 13c-2 4-5 9-6 13l-2 5c0-2 1-3 1-4l3-9c1-2 1-2 1-4z" class="L"></path><path d="M712 524c0 2 0 2-1 4l-3 9c0 1-1 2-1 4v3c0 1-1 3-1 5l-1-1c0-2 1-4 1-6-2 4-4 9-7 13l-1-3c5-9 10-18 14-28z" class="H"></path><path d="M696 478c1 1 1 1 1 2v2h1 0l1 1c1 1 2 1 2 2 0 2 0 3 1 4-1 1-2 4-2 4-2 0-2 1-3 2h0l1-3h-1-1c-1-1 0-2-2-4v-3l1-2 1-3v-2z" class="Y"></path><path d="M696 480v1 3c1-1 1-1 2-1v4c1 1 2 1 1 2h-1-1v-2h-1c-1-1 0-2 0-3-1 0-1-1-1-1l1-3z" class="S"></path><path d="M695 483s0 1 1 1c0 1-1 2 0 3h1v2h1 1c0 2 0 2-1 3h-1-1c-1-1 0-2-2-4v-3l1-2z" class="g"></path><path d="M635 579l10-6c0 1 0 3-1 4l1 1-1 1-1 1c-1 1-2 3-4 3h-1c-2 1-4 1-6 2l-2-3c2-1 3-2 5-3z" class="J"></path><path d="M630 582c2-1 3-2 5-3l1 3h2 0c0-1 1-1 2-2h3c-1 1-2 3-4 3h-1c-2 1-4 1-6 2l-2-3z" class="F"></path><path d="M719 486l1-5c1-1 1-3 3-4 1 2 0 4 1 6l-3 10-1 3-1 1-1-8-3 8v-1-5-1l1-3 1-1c0 2 0 2 1 3 0-1 1-2 1-3z" class="c"></path><path d="M763 477c1-1 2-1 3-1-1 3-2 6-4 9l-1 2h0l-1 2c-1 1-2 2-3 4h-2c-2-1-3-3-3-5l1-1c0 1 0 1 1 1v-1l-1-1 2-1v1 1h1c3-3 5-6 7-10z" class="L"></path><defs><linearGradient id="o" x1="682.464" y1="630.303" x2="683.547" y2="637.768" xlink:href="#B"><stop offset="0" stop-color="#5c5e5d"></stop><stop offset="1" stop-color="#767678"></stop></linearGradient></defs><path fill="url(#o)" d="M661 651h-2-1c2-2 3-1 5-3v-1c1-1 3-2 4-2 2-2 5-4 7-6 1 0 2 0 3-1 7-5 13-11 19-18-1 3-3 5-5 7-2 4-5 8-8 11l-1-1c-1 2-3 3-5 4-1 0-1 0-3 1l-6 5c-2 1-3 1-4 3-1 0-1 1-3 1h0z"></path><path d="M669 620c2-2 3-3 5-4l-2 4v-1c-1 1-2 3-3 4 0 1 1 2 1 3h-1l-1-3h0c-2 3-4 5-4 8v1h0l-1-1h-1c0 1 0 2 1 3l-2 2-2 1c0-1 1-1 1-2h1l-2-1c0-2 1-3 1-6-1 1-1 2-2 3l-1-1c1-1 2-3 3-5s1-3 3-4c3-1 3-2 6-1z" class="Y"></path><path d="M663 621c3-1 3-2 6-1-1 1-1 2-2 3-2-1-3-1-4-2z" class="Q"></path><path d="M660 628c1-1 1-3 2-3h3c0 1-1 1-1 1-1 1-1 2-1 3v2h-1c0 1 0 2 1 3l-2 2-2 1c0-1 1-1 1-2h1l-2-1c0-2 1-3 1-6z" class="S"></path><path d="M724 583c0-1 0-3 1-4h0 1c1-1 1-1 3-2 1 2 2 4 2 7 0 0-1 1-1 2-1 3-2 5-4 8-3 1-4 0-6 0h-1v-1c2-3 3-6 5-10z" class="Y"></path><path d="M724 583c0-1 0-3 1-4h0 1c1-1 1-1 3-2 1 2 2 4 2 7 0 0-1 1-1 2l-1-1-1 1h-1l1-2v-4s0-1 1-1l-1-1c-2 1-3 4-4 5z" class="D"></path><path d="M661 651h0c2 0 2-1 3-1 1-2 2-2 4-3l6-5c2-1 2-1 3-1 2-1 4-2 5-4l1 1c-8 8-14 16-26 19l-2-5v-2h1v2h1l4-1z" class="h"></path><path d="M774 487l1 1c1 1 0 1 1 1 1 1 1 2 2 3l-1 1 2 2h0l-5-3 3 4 1 2c-1 2-3 3-2 6h0l-4 4-3 3v-1-7c0 1-1 2-2 2 0-2 2-7 4-9v-1l3-8z" class="V"></path><path d="M769 503l1-2h1v-2l1-1 1 2c-1 1-1 2-1 3v1 2c-1 1-1 1 0 2l-3 3v-1-7z" class="P"></path><path d="M774 487l1 1c1 1 0 1 1 1 1 1 1 2 2 3l-1 1 2 2h0l-5-3 3 4-1 1-2-3v1l1 3h0-1c0-1 0 0-1-1-1 1 0 2 1 4-1-1-1 0-1-1l-1-2-1 1v2h-1l-1 2c0 1-1 2-2 2 0-2 2-7 4-9v-1l3-8z" class="L"></path><path d="M623 620c1 1 2 1 2 2 1 1 1 2 2 3v-1h1c2 3 4 6 8 7s8 0 11-2l4-4v1c2-1 3-4 4-5 0 2-1 3 0 5-1 2-2 3-4 4-4 3-8 5-13 4-2 0-5-1-7-2-3-2-5-4-7-8l-1-4z" class="M"></path><path d="M631 632c1 0 2-1 3-1 1 1 4 2 6 2 2-1 6-2 8-4 1-1 2-1 3-2l1 1-1 2c-4 3-8 5-13 4-2 0-5-1-7-2z" class="T"></path><path d="M684 594c1 1 1 2 2 3v1c-5 9-15 18-25 21-1-1-1-2-1-3 1-1 2-2 4-3 1-1 2-1 3-1 2-2 3-4 6-5 0 0 0-1 1-1 5-3 8-7 10-12z" class="G"></path><path d="M774 487c1-4 2-6 5-9 1 1 1 1 2 1v-2l1 1 1-1c-1 0-1-1-1-1l1-1c1 1 2 3 3 4 0-2-1-3-1-4h0c1 1 1 2 2 3h0c1 1 1 1 1 2v1l1 1c0-1 0-2 1-3 0 1 0 0 1 1h0v1c-1 1-1 1-1 2h-2v1h-2 0v2c0-1-1-1-2-1l1 3c-1 0-1 0-1-1-2 1-1 2-2 3l-2-2v1c0 1 1 2 1 3l-3-3v1l1 2v1l-1-1c-1-1-1-2-2-3-1 0 0 0-1-1l-1-1z" class="D"></path><defs><linearGradient id="p" x1="747.388" y1="531.612" x2="774.112" y2="515.888" xlink:href="#B"><stop offset="0" stop-color="#3b3c3c"></stop><stop offset="1" stop-color="#6d6c6f"></stop></linearGradient></defs><path fill="url(#p)" d="M767 505c1 0 2-1 2-2v7 1c-2 4-4 9-5 13-4 8-7 15-11 22 0-6 0-10 3-15 1-2 3-4 4-6 1-4 2-7 4-10 1-3 3-6 3-9v-1z"></path><path d="M699 555c3-4 5-9 7-13 0 2-1 4-1 6l1 1v2h1c-1 3-3 6-4 9-2 2-3 6-4 9 0 1 0 2 1 4l2-3h1c-2 4-5 7-7 10v-1c0-1 1-2 1-4 0-3 1-5 1-8l-4-1h0c-1-1-1-1-1-2s1-2 2-3c1-2 3-4 4-5v-1z" class="J"></path><path d="M699 556c0 3-2 6-4 9-1 1-1 0-1 1h0c-1-1-1-1-1-2s1-2 2-3c1-2 3-4 4-5z" class="V"></path><path d="M705 548l1 1v2h1c-1 3-3 6-4 9-2 2-3 6-4 9 0 1 0 2 1 4l2-3h1c-2 4-5 7-7 10v-1c0-1 1-2 1-4 0-3 1-5 1-8 1-4 2-7 4-11l3-8z" class="a"></path><path d="M627 663v-3c2-2 5-2 7-2-2 3-3 7-3 10l-1 3v1c-1 1-3 3-5 3-1-1-2-1-3-2-2-2-5-6-5-9l2 2h0v-2h0l3 3c0-1-1-4-1-5 1 1 1 1 1 2l1-2c1-1 1-1 2-1l1 2h1z" class="b"></path><path d="M627 663c1 1 1 2 1 3v7l-5-7h0 1c1 1 1 2 2 3l1-1c-1-1-1-3-1-5h1z" class="B"></path><path d="M622 664l1-2c1-1 1-1 2-1l1 2c0 2 0 4 1 5l-1 1c-1-1-1-2-2-3h-1 0l-1-2zm5-1v-3c2-2 5-2 7-2-2 3-3 7-3 10l-1 3h0c-2-1 0-4-1-6h-1v1c0-1 0-2-1-3z" class="K"></path><path d="M772 480c-2 3-2 8-3 12-5 13-9 26-17 39 0-2 0-5 1-7 1-5 4-10 6-15 3-7 5-14 8-21l2-2h0l2-6h0 1z" class="F"></path><path d="M733 511l1-1c1 1 0 3 0 5v9c-1 2-1 3-1 5-1 3 0 7-1 9 0 1 1 2 1 3 0 2 0 3 1 5 0 4 2 10 1 14v1l-1 1v3c-1 2-1 7 0 9v2l2 17c-1 0 0 0-1-1h0l-1-7c-2-10-3-19-3-28-1-16 1-31 2-46z" class="V"></path><path d="M726 492c1 0 1 1 2 1v-3h1v6c1 7 1 15-1 22l-1 3c-1 7-4 15-7 22l-1 2v1c0 1-1 2-1 3-1 3-1 4-3 6h-2 0l3-7c2-1 4-11 5-13 1-4 2-8 3-11l2-10c0-6 1-12 1-19-1 2-2 7-3 8v-2l-1-1c0-3 2-5 3-8z" class="c"></path><path d="M726 514l2 1c0 2 0 3-1 4-1 2-1 4-2 6l-1 6h-1l-2 7c-1 1-1 2-1 3v2l-1 2v1c0 1-1 2-1 3-1 3-1 4-3 6h-2 0l3-7c2-1 4-11 5-13 1-4 2-8 3-11l2-10z" class="K"></path><defs><linearGradient id="q" x1="720.579" y1="469.164" x2="708.854" y2="479.395" xlink:href="#B"><stop offset="0" stop-color="#121619"></stop><stop offset="1" stop-color="#4b4b4d"></stop></linearGradient></defs><path fill="url(#q)" d="M709 480c1-4 2-11 5-13 1 0 1-1 2-1s3 1 5 1c0 1 1 2 1 3 0 2 0 2-1 3h-1l-3 8v5l-1 1-1 3s-1-1-1-2h-1v-1c-1 0-1 1-2 1-1-2-2-2-4-3 0-1 2-4 2-5z"></path><path d="M709 480c1 1 2 4 4 4 1 0 1 0 2-1v1c0 1 0 2 1 3l-1 3s-1-1-1-2h-1v-1c-1 0-1 1-2 1-1-2-2-2-4-3 0-1 2-4 2-5z" class="S"></path><path d="M664 596l3-1 2 2c0 1-1 3-1 3l1 1c1 2-1 5 1 6h0c1-1 1-1 2-1l1-1 1 1c-1 0-1 1-1 1-3 1-4 3-6 5-1 0-2 0-3 1-2 1-3 2-4 3 0-1-1-2-1-4l-2-2c-1-2-2-3-3-4l10-10z" class="H"></path><path d="M657 610c1-1 2-2 2-3 1-2 2-4 4-5v1 1l-1 1c-1 2 1 5-1 7h-2l-2-2z" class="d"></path><path d="M663 604v3h1v-5c1 1 1 3 2 5v-7h1v4l1 1v-5l1 1c1 2-1 5 1 6h0c1-1 1-1 2-1l1-1 1 1c-1 0-1 1-1 1-3 1-4 3-6 5-1 0-2 0-3 1-2 1-3 2-4 3 0-1-1-2-1-4h2c2-2 0-5 1-7l1-1z" class="a"></path><path d="M640 661c3 0 3-2 5-2 2-1 2-2 4-3 1 1 0 2 1 3 1 0 3-1 5-1h0c-4 2-9 3-12 6-2 1-4 5-6 7-6 5-15 8-23 9v-1c1-1 9-3 11-4 2 0 4-2 5-3v-1l1-3c0-3 1-7 3-10l4-1c1 0 1 1 2 2v2z" class="J"></path><path d="M640 661c3 0 3-2 5-2 2-1 2-2 4-3 1 1 0 2 1 3-3 1-5 2-7 3-3 2-8 10-11 9 2-5 4-7 8-10z" class="B"></path><path d="M634 658l4-1c1 0 1 1 2 2v2c-4 3-6 5-8 10h-1v-3c0-3 1-7 3-10z" class="N"></path><path d="M791 480v1h1l1-1 1 1c0-1 1-3 1-4 0 9-3 14-9 21l-10 6h0c-1-3 1-4 2-6l-1-2-3-4 5 3h0l-2-2 1-1 1 1v-1l-1-2v-1l3 3c0-1-1-2-1-3v-1l2 2c1-1 0-2 2-3 0 1 0 1 1 1l-1-3c1 0 2 0 2 1v-2h0 2v-1h2c0-1 0-1 1-2v-1z" class="M"></path><defs><linearGradient id="r" x1="695.362" y1="567.155" x2="687.912" y2="584.534" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#303131"></stop></linearGradient></defs><path fill="url(#r)" d="M693 564c0 1 0 1 1 2h0l4 1c0 3-1 5-1 8 0 2-1 3-1 4v1 1l-7 12c-1 2-2 4-3 5v-1c-1-1-1-2-2-3 0-1 1-1 1-2 2-4 1-8 2-12 0-2 1-3 1-4 2-5 2-8 5-12z"></path><path d="M691 583c1-2 2-4 2-7 0-1 1-3 2-3s1 1 2 2c0 2-1 3-1 4v1 1l-3 1-2 1z" class="F"></path><path d="M693 564c0 1 0 1 1 2-6 6-6 17-6 24l3-7 2-1 3-1-7 12c-1 2-2 4-3 5v-1c-1-1-1-2-2-3 0-1 1-1 1-2 2-4 1-8 2-12 0-2 1-3 1-4 2-5 2-8 5-12z" class="M"></path><path d="M741 481v-6l1-1 1 1h0c1-1 1-1 3-1 1 1 1 5 1 6l-1 1 2 20 2 18c0 3 1 7 0 10-1-3-2-5-2-7l-3-12c0-3-1-6-1-9v-1h-1c-1-3-2-7-2-11h0v-8z" class="V"></path><path d="M741 481v-6l1-1 1 1h0c1-1 1-1 3-1 1 1 1 5 1 6l-1 1-1 5-1-9c-1 4-1 8-1 11-1 0-1-5-1-7h-1z" class="C"></path><path d="M716 562l3-6c0 6 2 12 0 17 0 2 0 2-1 3v1c1 2 0 2 0 4v4l-2 3-3-2-4 7-1-1-2 3c-1 1-1 2-2 2 0-2 0-3 1-5l3-4-1-1c1-2 1-4 1-6v-3h1 0v1h1c1-2 2-3 2-5h-1l-1 1h0l1-4-1-1c1-2 3-4 5-6v3c1-1 1-3 1-4v-1z" class="H"></path><path d="M718 577c1 2 0 2 0 4v4l-2 3-3-2c1-2 4-7 5-9z" class="M"></path><path d="M716 563c2 1 2 3 2 5s-1 4-2 6v1c-1 1-2 2-3 4l-5 9-1-1c1-2 1-4 1-6v-3h1 0v1h1c1-2 2-3 2-5h-1l-1 1h0l1-4-1-1c1-2 3-4 5-6v3c1-1 1-3 1-4z" class="T"></path><path d="M614 667l4 7h1c-2-4-4-7-5-11 2 3 5 9 7 10h1c1 1 2 1 3 2-2 1-10 3-11 4v1l-2 1c-1-1-2-1-3-1s0-1-2-1v5c1 3 1 4 3 7l-2 1c-2-2-5-4-7-6-2-4-2-6-2-10 1 2 1 3 1 5v1l1-1v-1l1-2c0-2 1-4 2-5v-1c1-1 2-2 4-3v2l1-1 1-1 1-1h1 0v-2l1-1 1 2z" class="C"></path><path d="M614 667l4 7h1c-2-4-4-7-5-11 2 3 5 9 7 10h1c1 1 2 1 3 2-2 1-10 3-11 4v1l-2 1c0-1 0-2-1-3 0 0-1-2-2-2 0-2 0-4 1-5h0c1 0 1 1 1 1 1 2 2 3 3 4l1 1v-1c0-2-3-4-4-6l1-1 1 1 4 6h0c-1-4-4-5-4-8l1-1z" class="L"></path><path d="M717 535c1-4 2-8 5-12 0 2 0 3-1 4-1 3-2 5-2 7 0 1-1 2-1 3-1 1-1 1-1 2h1c1-1 2-2 2-4h0 1c-1 2-3 12-5 13l-3 7-7 10c-1 1-2 4-3 5h-1l-2 3c-1-2-1-3-1-4 1-3 2-7 4-9 1-3 3-6 4-9 0-1 0-2 1-3 0 1 0 1 1 2v-1l1-4c1-2 2-5 3-7 0-1 2-5 2-7 1 1 1 1 1 2s0 2 1 2z" class="i"></path><path d="M713 538l1 1v1c-1 1-1 2-1 3 0 2-1 3-2 5h0v-1c0-1 0-1-1-2 1-2 2-5 3-7z" class="P"></path><path d="M715 531c1 1 1 1 1 2s0 2 1 2c-1 3-2 6-4 8 0-1 0-2 1-3v-1l-1-1c0-1 2-5 2-7z" class="H"></path><path d="M703 560h1c1 0 1-1 2-1 0-1 0-1 1-2v3c1 0 1-1 1-2 1-4 5-8 6-12 0-1 0-1 1-2 0 2-1 5-2 7 1-1 2-2 2-4l1 1-3 7-7 10c-1 1-2 4-3 5h-1l-2 3c-1-2-1-3-1-4 1-3 2-7 4-9z" class="d"></path><path d="M697 495h0c1-1 1-2 3-2-7 18-17 36-29 50-5 6-11 11-17 17-4 0-5 3-8 4l-1 1h-3l5-5 4-1c6-2 17-17 21-23l14-24h1c1-1 1-2 3-2 1-2 2-4 2-5 1-3 3-8 5-10z" class="P"></path><path d="M686 512h1c1-1 1-2 3-2-3 6-7 13-11 19-1 2-4 7-6 8h-1v-1l14-24z" class="d"></path><path d="M733 480v-8l1 5c1 0 1-1 1-2l1 1v3h1v-2l1 1v5c1-1 1-2 1-3h1c0 3 0 6 1 9 0 4 1 8 2 11h1v1c0 3 1 6 1 9l3 12c0 2 1 4 2 7l1 4c-1 6-1 10-2 16-1-3-1-5-1-7 0-4-1-7-1-10-2-6-4-11-5-17l-9-35z" class="G"></path><path d="M769 481v4 1h0l-2 2c-3 7-5 14-8 21-2 5-5 10-6 15-1 2-1 5-1 7 0 1 0 1-1 2l-1-4c1-3 0-7 0-10l-2-18c1 0 1 0 2 1h1v-4h0l1-1c1 0 1 0 2 1h1 1l-1-1c1-2 2 0 4-1 1-1 2-3 3-4 1-2 4-5 5-7 0-2 1-3 2-4z" class="D"></path><path d="M751 502v-4h0l1-1c1 0 1 0 2 1h1 1l-1-1c1-2 2 0 4-1 1-1 2-3 3-4 0 3-1 5-2 7h-1l-1-1h0c-1 2-1 2-1 5v1c-1 0-2 0-3 1s-1 1-1 3l-1-1v-2-1c-1-1-1-1-1-2z" class="f"></path><path d="M723 500l1 1v2c1-1 2-6 3-8 0 7-1 13-1 19l-2 10c-1 3-2 7-3 11h-1 0c0 2-1 3-2 4h-1c0-1 0-1 1-2 0-1 1-2 1-3 0-2 1-4 2-7 1-1 1-2 1-4-3 4-4 8-5 12-1 0-1-1-1-2s0-1-1-2c0 2-2 6-2 7-1 2-2 5-3 7l-1 4v1c-1-1-1-1-1-2-1 1-1 2-1 3h-1v-2c0-2 1-4 1-5v-3l2-5c1-4 4-9 6-13l5-13h0l3-6v-2-1-1z" class="j"></path><path d="M720 510v1 4c-1 1-1 0-1 1 1-1 2-2 2-4 1-1 2-2 2-3l1-1v3c-1 0-1 0-1 1-1 1-2 3-1 4 1 0 2-3 2-3 0 1 0 1-1 2h0c0 3-1 5-1 8-3 4-4 8-5 12-1 0-1-1-1-2s0-1-1-2c1-3 4-7 4-11l-1 2c-2 2-2 4-3 6-1-1 0-3 1-4l-1-1 5-13z" class="h"></path><path d="M715 528c1-2 1-4 3-6l1-2c0 4-3 8-4 11 0 2-2 6-2 7-1 2-2 5-3 7l-1 4v1c-1-1-1-1-1-2-1 1-1 2-1 3h-1v-2c0-2 1-4 1-5v-3l2-5c1-4 4-9 6-13l1 1c-1 1-2 3-1 4z" class="J"></path><path d="M715 523l1 1c-1 1-2 3-1 4-1 2-2 3-2 5s-1 4-3 6c0 1-1 2-1 3s0 0-1 1h0c0-3 2-5 3-7h-1-1 0c1-4 4-9 6-13z" class="M"></path><defs><linearGradient id="s" x1="673.222" y1="521.826" x2="677.904" y2="524.754" xlink:href="#B"><stop offset="0" stop-color="#b5b4b5"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#s)" d="M694 485v3c2 2 1 3 2 4h1 1l-1 3c-2 2-4 7-5 10 0 1-1 3-2 5-2 0-2 1-3 2h-1l-14 24c-4 6-15 21-21 23l-4 1c21-22 37-47 47-75z"></path><path d="M686 512l5-9 1 2c0 1-1 3-2 5-2 0-2 1-3 2h-1z" class="h"></path><defs><linearGradient id="t" x1="691.534" y1="495.404" x2="695.466" y2="500.097" xlink:href="#B"><stop offset="0" stop-color="#838182"></stop><stop offset="1" stop-color="#9b9d9d"></stop></linearGradient></defs><path fill="url(#t)" d="M694 488c2 2 1 3 2 4h1 1l-1 3c-2 2-4 7-5 10l-1-2c2-5 3-9 3-15z"></path><path d="M692 562c1 0 2-1 3-1-1 1-2 2-2 3-3 4-3 7-5 12 0 1-1 2-1 4-1 4 0 8-2 12 0 1-1 1-1 2-2 5-5 9-10 12l-1-1-1 1c-1 0-1 0-2 1h0c-2-1 0-4-1-6l-1-1s1-2 1-3c1-2 1-4 3-6h0v1c1-1 1-2 1-3s1-1 1-2c2-2 3-3 3-6l10-13 5-6z" class="F"></path><path d="M677 581l10-13v4c-1 2-2 5-3 6 0 2-1 3-2 4 0 2-1 3-2 5l-1 5v1l-2 2v1l-1 2-1 1h0v-2h-1v3c-1 1-1 2-1 4h-1c0-2 0-4 1-5 0-3 1-7 1-10h-1c0-1 1-1 1-2 2-2 3-3 3-6z" class="T"></path><defs><linearGradient id="u" x1="697.334" y1="502.213" x2="708.204" y2="514.754" xlink:href="#B"><stop offset="0" stop-color="#929190"></stop><stop offset="1" stop-color="#bebdbe"></stop></linearGradient></defs><path fill="url(#u)" d="M707 485c2 1 3 1 4 3 1 0 1-1 2-1v1h1c0 1 1 2 1 2v1 5 1l-7 19-6 12h0c-1-1-1 0-2 0v-1l-2 1-1-2h0c0-1 0-1-1-1-2 2 0-1-1 2l-1 1c-1 0-1 0-2-1l12-22c-2-1-3-1-3-3s2-5 3-7l2-5 1-5z"></path><path d="M701 520l-1-2s1-1 1-2c0-2 2-4 3-7l1 1v1c-1 3-3 6-4 9z" class="M"></path><path d="M705 511l3-1c-3 5-5 12-8 17l-2 1-1-2 4-6c1-3 3-6 4-9z" class="d"></path><path d="M707 485c2 1 3 1 4 3 1 0 1-1 2-1v1h1c0 1 1 2 1 2v1l-3 8c0-3 1-5 0-7h1c0-1 0-1-1-2-1 2-1 3-3 5 0 1 0 0-1 1v1-4c0-1 1-1 0-3l-1 1-1-1 1-5z" class="W"></path><path d="M706 490l1 1 1-1c1 2 0 2 0 3l-2 4c0 3-1 5-2 8-2-1-3-1-3-3s2-5 3-7l2-5z" class="Z"></path><path d="M706 490l1 1 1-1c1 2 0 2 0 3l-2 4v-1h0v-2-1l-2 2 2-5z" class="d"></path><path d="M712 499l3-8v5 1l-7 19-6 12h0c-1-1-1 0-2 0v-1c3-5 5-12 8-17l4-11z" class="L"></path><defs><linearGradient id="v" x1="671.413" y1="535.006" x2="681.67" y2="541.722" xlink:href="#B"><stop offset="0" stop-color="#b5b4b4"></stop><stop offset="1" stop-color="#e0dfde"></stop></linearGradient></defs><path fill="url(#v)" d="M701 502c0 2 1 2 3 3l-12 22c-5 9-11 17-18 26h0c-4 4-11 12-16 14h0c-1 2-2 4-4 5-1 0-2 1-2 2-1 0-1 0-2 1l-1 1h-3v-4h0c5-6 12-11 18-16 16-16 27-34 37-54z"></path><path d="M649 576l-1-1v-1-3c1 0 1 0 2 1v3l-1 1z" class="N"></path><path d="M640 604c4 1 8 1 12 5h0c3 2 4 6 5 9 0 3-1 5-2 8-1-2 0-3 0-5-1 1-2 4-4 5v-1l-4 4c-3 2-7 3-11 2s-6-4-8-7l-1-4c0-4 1-6 3-9 1-1 1-2 2-3s1-1 1-2l-2-1c3-1 6 0 9-1z" class="F"></path><path d="M653 616l1 1c0 1 0 1 1 2l1 1-1 1c-1 1-2 4-4 5v-1-1c2-2 2-5 2-8z" class="n"></path><path d="M644 608c1-1 1-1 2 0 1 0 2 1 4 1l2 2c0-1 0-1-1-2h1c3 2 4 6 5 9 0 3-1 5-2 8-1-2 0-3 0-5l1-1-1-1c-1-1-1-1-1-2l-1-1h0c-1-1-2-1-2-2-2-3-5-5-7-6z" class="G"></path><path d="M640 604c4 1 8 1 12 5h0-1c1 1 1 1 1 2l-2-2c-2 0-3-1-4-1-1-1-1-1-2 0l-2-1-2 1c-4 0-7 1-10 3 1-1 1-2 2-3s1-1 1-2l-2-1c3-1 6 0 9-1z" class="J"></path><path d="M637 611h2v1h-3l-1 1c-1 0-1 1-2 2 0 2-1 2 1 4h1l1 2c-1 1-1 1 0 2 0 1 1 1 2 1l1 1h3l2-2v-1c0-1 1-2 1-2 0-1 1-4 0-5 0-1-3-2-2-3h1c2 3 3 4 3 8 0 2-1 3-2 5-2 1-4 2-6 2-3 0-5-1-7-3s-2-3-2-6 1-4 4-7h3z" class="E"></path><path d="M640 608l5 3-1 1h-1c-1 1 2 2 2 3 1 1 0 4 0 5 0 0-1 1-1 2v1l-2 2h-3l-1-1c-1 0-2 0-2-1-1-1-1-1 0-2l-1-2h-1c-2-2-1-2-1-4 1-1 1-2 2-2l1-1h3v-1h-2l-1-1c-2 0-4 0-6 2-1 2-2 4-2 6 1 4 1 6 4 9h1 2c2 1 6 1 8 1 2-1 5-3 6-4l1-1c1-2 0-5 0-6 1 2 1 5 1 8l-4 4c-3 2-7 3-11 2s-6-4-8-7l-1-4c0-4 1-6 3-9 3-2 6-3 10-3z" class="D"></path><defs><linearGradient id="w" x1="680.082" y1="547.656" x2="690.612" y2="555.213" xlink:href="#B"><stop offset="0" stop-color="#c6c5c5"></stop><stop offset="1" stop-color="#edeceb"></stop></linearGradient></defs><path fill="url(#w)" d="M715 497l3-8 1 8v1c-2 3-3 7-4 10l-7 18c-11 22-24 42-39 60-5 6-11 13-18 17l-6-1s0-1 1-1c1-2 5-4 6-6l15-15c14-16 26-33 35-52h0l6-12 7-19z"></path><path d="M793 455l1 3h1 2c3 1 9 0 13 0l1-1h1 1l2 1c4-1 8 0 11-1 0 0 1-1 2-1 2 2 5 4 8 6l-42 1h-88l-1-2c1-1 2-1 3-2h1l16 1h1 4c4 0 9-1 13 0h1c2 0 8 1 10 0h0 3 3c1 0 2-1 3-1h1c2 0 10 1 12-1h1 0v-1-1c0 1 0 0 1 1 0 1 0 1 1 2 3 0 9 0 12-1 0-1 1-2 2-3z" class="o"></path><defs><linearGradient id="x" x1="695.543" y1="589.153" x2="698.933" y2="592.869" xlink:href="#B"><stop offset="0" stop-color="#716f71"></stop><stop offset="1" stop-color="#838382"></stop></linearGradient></defs><path fill="url(#x)" d="M710 570l1 1-1 4h0l1-1h1c0 2-1 3-2 5h-1v-1h0-1v3c0 2 0 4-1 6l1 1-3 4c-1 2-1 3-1 5 1 0 1-1 2-2l2-3 1 1c-14 21-34 41-58 50 0-1 2-2 2-3 0 0 1 0 1-1 1 0 2 0 3-1h1l1-1 2-1 2-2c-1-1-1-2-1-3h1l1 1h0v-1c0-3 2-5 4-8h0l1 3h1c0-1-1-2-1-3 1-1 2-3 3-4v1l2-4 7-5c4-4 6-8 9-12h0c2-3 4-7 7-10l7-13 1 2 5-8z"></path><path d="M699 592h0c0 1 0 2 1 3h0l1 1h-1c0 1 0 2 1 3h0l-2 2h-1c1-1 0-1 0-2v2l-1-1v-1h-1v-3c2-1 2-2 3-4z" class="H"></path><path d="M697 589c0 2 0 3-1 5-2 3-2 5-2 9v1h0c-1-1-1-3-1-4-1 1-2 3-1 4v2 1l-1 1v-3c-1-2 1-4-1-6h0c2-3 4-7 7-10z" class="i"></path><path d="M710 570l1 1-1 4h0l1-1h1c0 2-1 3-2 5h-1v-1h0-1l-2 9h-1v-6c0 2-1 4-1 6v1h-1c0-3 1-7 2-10l5-8z" class="h"></path><path d="M704 597c1 0 1-1 2-2l2-3 1 1c-14 21-34 41-58 50 0-1 2-2 2-3 0 0 1 0 1-1 1 0 2 0 3-1h1l1-1 2-1 2-2c-1-1-1-2-1-3h1l1 1h0v-1c0-3 2-5 4-8h0l1 3h1c0-1-1-2-1-3 1-1 2-3 3-4v1l2-4 7-5 1 9c1-1 2-3 3-4l4-4c2-2 3-4 5-6l1 1-1 1h0c-2 3-5 5-6 7-2 3-8 7-9 10 6-3 9-10 14-14l1-1c1-2 2-3 3-4l3-3c0-1 1-2 2-2 0-2 1-3 2-4z" class="i"></path><path d="M733 480l9 35c1 6 3 11 5 17 0 3 1 6 1 10 0 2 0 4 1 7 0 3-1 6-2 10v4h0c0 3 0 5-1 8s1 9 1 12l3 16c0 1 0 2-1 3v4c-1 7 1 12 3 18 0 1 0 2-1 3v1l-3-3c-6-8-6-16-6-26h-1c0-1 0-2-1-3l-3 15c0-1 0-2-1-3v-2h0c0 2 0 2-2 4 1-5 2-12 0-17l1-1c1 1 0 1 1 1l-2-17v-2c-1-2-1-7 0-9v-3l1-1v-1c1-4-1-10-1-14-1-2-1-3-1-5 0-1-1-2-1-3 1-2 0-6 1-9 0-2 0-3 1-5v-9c0-2 1-4 0-5l-1 1v-28h0c-1-1 0-2 0-3z" class="B"></path><path d="M737 555c-1-9 0-19 1-27v-1l1 3c-1 9-1 19-1 29v8c0 4 1 8 2 12l-1 1c-2-5-3-11-2-16v-9z" class="G"></path><path d="M733 483c0 1 1 2 1 3v3l2 12c1 8 3 15 3 23v6l-1-3v1c-1 8-2 18-1 27h-1v-2c-2-13 2-26 1-39-1 4-1 7-2 11l-1 10c-1 2-1 4-1 6 0-1-1-2-1-3 1-2 0-6 1-9 0-2 0-3 1-5v-9c0-2 1-4 0-5l-1 1v-28z" class="g"></path><path d="M733 541c0-2 0-4 1-6l1-10c1-4 1-7 2-11 1 13-3 26-1 39v2h1v9c-1 5 0 11 2 16v5c0 3 1 7 1 11l-3 15c0-1 0-2-1-3v-2h0c0 2 0 2-2 4 1-5 2-12 0-17l1-1c1 1 0 1 1 1l-2-17v-2c-1-2-1-7 0-9v-3l1-1v-1c1-4-1-10-1-14-1-2-1-3-1-5z" class="M"></path><path d="M734 576c1 1 1 0 1 1 0 2 1 4 1 6 1 3 1 7 2 10v2 2c2-3 0-9 1-12 0 3 1 7 1 11l-3 15c0-1 0-2-1-3v-2h0c0 2 0 2-2 4 1-5 2-12 0-17l1-1c1 1 0 1 1 1l-2-17z" class="B"></path><path d="M743 591h0c0 1 0 0 1 1l2 5c-1-4-3-9-3-13 0-6-2-11-2-17v-13-19c-1 7 0 14 0 21 0 3-1 6-1 9 0 4 0 9 1 13v1c-1-1-1-4-1-6-1-5-1-9 0-14l-1-14c0-6 1-12 1-17 1 2 1 4 1 6 1 3 1 6 2 10v11h1v-8 1c2 5 1 11 3 15h0c0 3 0 5-1 8s1 9 1 12l3 16c0 1 0 2-1 3v4c-1 7 1 12 3 18 0 1 0 2-1 3v1l-3-3c-6-8-6-16-6-26h-1c0-1 0-2-1-3 0-4-1-8-1-11v-5l1-1 2 12h1z" class="N"></path><path d="M743 591c0 2 1 4 2 6l2 11c0-3 1-7 0-10 0-4-1-7-1-10-1-2 0-4-1-6v-4c-1-2-1-4-1-5-1-2-1-4-1-5 1 1 1 3 1 5v1c0 1 1 2 1 3v1c1 0 0 1 1 2v2 4 2c1 2 2 9 4 11 0 1 0 2-1 3v4c-1 7 1 12 3 18 0 1 0 2-1 3-4-5-5-15-6-21l-3-15h1z" class="O"></path><path d="M740 579l2 12 3 15c1 6 2 16 6 21v1l-3-3c-6-8-6-16-6-26h-1c0-1 0-2-1-3 0-4-1-8-1-11v-5l1-1z" class="J"></path><path d="M692 527c1 1 1 1 2 1l1-1c1-3-1 0 1-2 1 0 1 0 1 1h0l1 2 2-1v1c1 0 1-1 2 0-9 19-21 36-35 52l-15 15c-1 2-5 4-6 6-1 0-1 1-1 1h-2-1c-1-1-2-1-2-1-1 0-2 0-2 1-1 1-1 1-2 1v1h4c-3 1-6 0-9 1h-1l1-1-1-1h-1-4l-1-1-3-3h-1-2c-2 0-4-1-6-1h0 0c-2 0-2 0-4-1l-1-2 5-3h1l2-1v-3h3 1c1-1 2-1 3-2l1 2 3-2 3-3 1-1 2 3c2-1 4-1 6-2h1c2 0 3-2 4-3l1-1 1-1-1-1c1-1 1-3 1-4l1-1v4h3l1-1c1-1 1-1 2-1 0-1 1-2 2-2 2-1 3-3 4-5h0c5-2 12-10 16-14h0c7-9 13-17 18-26z" class="D"></path><path d="M642 589l1-4 2 2-1 5-1 1c-1-1-1-2-1-4z" class="Z"></path><path d="M645 573l1-1v4 3 3l-1 5-2-2c1-2 1-4 1-6l1-1-1-1c1-1 1-3 1-4z" class="G"></path><path d="M639 587l1 2-1 1c0 1 1 1 0 2-2 2-6 4-9 5l-1-1c4 0 8-7 10-9z" class="Y"></path><path d="M642 589c0 2 0 3 1 4 0 4-3 4-5 6h-1c0-1 0 0-1-1l-1 1c-1 0-1 0-2-1 1-1 3-2 5-3s3-3 4-5v-1z" class="M"></path><path d="M650 585v1c1 0 2 0 3-1 0-1 1-2 3-3h0v1 1c-1 1-1 1-2 3-2 3-5 5-8 7l-1-1-1-1 1-5 1-5h0 3l1 1-2 1 2 1h0z" class="T"></path><path d="M646 582h0 3l1 1-2 1 2 1h0l-2 2c0 1 0 2-1 2l-2 2 1 1 2-1h0c-1 2-2 2-3 2l-1-1 1-5 1-5z" class="f"></path><path d="M629 583l1-1 2 3c2-1 4-1 6-2 1 1 2 2 1 4-2 2-6 9-10 9l-2-2 1-1c1 0 1 1 2 2v-1-1c1-1 1-2 3-2h1c1-1 2-1 2-3l-4 2h-2l1-2v-1c-1 0-1 1-2 2h-1 0v-1l-1 1v1c0 1-1 1-2 2 0-3 0-4 1-6l3-3z" class="T"></path><path d="M682 553c3-3 7-9 9-13 1-2 2-4 4-5-5 10-13 20-20 30-4 5-8 11-13 15 0-2 5-7 6-9l-3 2h0l4-6 1 1c1-2 2-3 3-4 3-4 5-8 9-11zm-63 35c1-1 2-1 3-2l1 2-1 2 1 2-1 1-1-1 3 3c3 3 5 5 7 6l1 1c-1 0-2 0-2 1h-1-4l-1-1-3-3h-1-2c-2 0-4-1-6-1h0 0c-2 0-2 0-4-1l-1-2 5-3h1l2-1v-3h3 1z" class="F"></path><path d="M612 598l5-2 3 3h-2c-2 0-4-1-6-1z" class="H"></path><path d="M613 592c1 0 2 0 2 1l-2 2c-1 0 0 1-1 1-2 1-1-1-3 0l-1 1-1-2 5-3h1z" class="V"></path><path d="M621 599h0l2-2c2 1 4 4 6 6h-4l-1-1-3-3z" class="J"></path><path d="M615 593l1 1h1v-2h4l3 3c-1 1-1 1-2 0l-1 1c-3-1-5-1-8-1l2-2z" class="W"></path><path d="M619 588c1-1 2-1 3-2l1 2-1 2 1 2-1 1-1-1h-4v2h-1l-1-1c0-1-1-1-2-1l2-1v-3h3 1z" class="Q"></path><path d="M619 588c1-1 2-1 3-2l1 2-1 2-3-2z" class="I"></path><path d="M692 527c1 1 1 1 2 1l1-1c1-3-1 0 1-2 1 0 1 0 1 1h0l1 2 2-1v1c-1 2-3 7-5 7-2 1-3 3-4 5-2 4-6 10-9 13-4 3-6 7-9 11-1 1-2 2-3 4l-1-1c1-2 6-7 6-9l-5 6-1-1 6-7-1-1c-1 1-1 3-2 3l-1-1c1-1 2-2 2-3l1-1h0c7-9 13-17 18-26z" class="a"></path><path d="M700 527v1c-1 2-3 7-5 7-2 1-3 3-4 5-2 4-6 10-9 13l16-25 2-1z" class="H"></path><path d="M650 575c1-1 1-1 2-1 0-1 1-2 2-2 2-1 3-3 4-5h0c5-2 12-10 16-14l-1 1c0 1-1 2-2 3l1 1c1 0 1-2 2-3l1 1-6 7 1 1 5-6c0 2-5 7-6 9l-4 6h0l3-2c-1 2-6 7-6 9-2 3-5 6-8 7 1-2 1-2 2-3v-1-1h0c-2 1-3 2-3 3-1 1-2 1-3 1v-1h0l-2-1 2-1-1-1h-3 0v-3-3h3l1-1z" class="V"></path><path d="M646 579c3 1 7 0 10 0l-6 6-2-1 2-1-1-1h-3 0v-3z" class="Q"></path><path d="M670 564l5-6c0 2-5 7-6 9l-4 6h0l3-2c-1 2-6 7-6 9-2 3-5 6-8 7 1-2 1-2 2-3 2-3 4-6 7-10 2-3 5-6 7-10z" class="P"></path><defs><linearGradient id="y" x1="647.302" y1="600.68" x2="664.308" y2="677.947" xlink:href="#B"><stop offset="0" stop-color="#c5c3c1"></stop><stop offset="1" stop-color="#ebebee"></stop></linearGradient></defs><path fill="url(#y)" d="M713 586l3 2c-4 8-10 14-15 21-6 7-11 14-18 20-13 11-31 18-47 24-12 3-29 8-35 19-1 1-1 3-2 4 0 4 0 6 2 10 2 2 5 4 7 6l2 1h-1c-2 1-7-2-10-3-3-3-5-7-6-12-2-11 4-18 10-26v-1l1-2c-1 1-2 1-3 1-6 5-10 12-12 19h-1c-1 1-1 3-2 5h0v-2c-1-1-1-2-1-3l1-3 1-4h-3c1-4 2-6 4-9 4-4 7-7 12-10h0v-1c1-1 2-3 4-4 1 0 1 0 1-1v-1h0l3-2 5-2h1c2 0 4-3 5-5l1-1 2 2c-1 1-1 1 0 2v2c0 1-1 1-2 1v1c3 0 4 0 7 1 0 1 1 1 2 2l4 2c3 1 5 1 8 2h4c0 1-1 1-2 2v1c2-1 3-2 4-2l6-2c0 1-2 2-2 3 24-9 44-29 58-50l4-7z"></path><path d="M608 647l2-1c1 2 2 3 2 5-1 1-1 2-1 3-1 2-4 2-3 5 0 1-2 2-3 2h0l2-1c0-1 0-1-1-1h0c0-1 0-1 1-2l1-2c0-1-1-2-2-3h-2c1-2 3-4 4-5z" class="N"></path><path d="M612 644c2 1 2 2 3 5h1c0 2 0 2 2 3-2 0-3 1-4 2l1 1h0l-7 4c-1-3 2-3 3-5 0-1 0-2 1-3 0-2-1-3-2-5l2-2z" class="Z"></path><defs><linearGradient id="z" x1="626.659" y1="648.786" x2="614.959" y2="652.074" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#afaead"></stop></linearGradient></defs><path fill="url(#z)" d="M625 643l1-1c-2-1-3-3-4-5 2 1 4 3 6 5 0 1 2 3 3 4s2 2 2 4l-4 1-14 4h0l-1-1c1-1 2-2 4-2-2-1-2-1-2-3h-1c-1-3-1-4-3-5l1-2h1l2-2h0c1-1 2-1 3-2l1 1v1c1 0 1 0 2-1 1 1 2 2 3 4z"></path><path d="M629 651l-1-1c1 0 2-1 2-1 1-2 0-2 1-3 1 1 2 2 2 4l-4 1z" class="H"></path><path d="M616 640c2 0 2 0 3 2 0 2 1 3 3 4v1l2 2h-1l-1-1h-1v1l-1 1 1 1h-1c-1-1-1-1-1-2h-4c-1-3-1-4-3-5l1-2h1l2-2z" class="h"></path><path d="M614 642l3 1v4c-1-1-2-3-4-5h1z" class="F"></path><path d="M613 642c2 2 3 4 4 5l2 2h-4c-1-3-1-4-3-5l1-2z" class="V"></path><path d="M619 638l1 1v1c1 0 1 0 2-1 1 1 2 2 3 4 1 1 1 1 2 1 1 1 1 2 2 3 0 1 0 2-1 3l-1-1h-1-1v-1h-1v1l-2-2v-1c-2-1-3-2-3-4-1-2-1-2-3-2h0c1-1 2-1 3-2z" class="T"></path><path d="M620 626l2 2c-1 1-1 1 0 2v2c0 1-1 1-2 1v1c3 0 4 0 7 1 0 1 1 1 2 2l4 2c3 1 5 1 8 2h4c0 1-1 1-2 2v1c2-1 3-2 4-2l6-2c0 1-2 2-2 3-5 3-12 5-18 7 0-2-1-3-2-4s-3-3-3-4c-2-2-4-4-6-5 1 2 2 4 4 5l-1 1c-1-2-2-3-3-4-1 1-1 1-2 1v-1l-1-1h-1l-1 1c-2 0-3-1-5-1l2-5c-4 3-9 8-14 10v-1c1-1 2-3 4-4 1 0 1 0 1-1v-1h0l3-2 5-2h1c2 0 4-3 5-5l1-1z" class="K"></path><path d="M620 626l2 2c-1 1-1 1 0 2v2c0 1-1 1-2 1v1c0 1-1 2-2 3-1-2 0-1 0-2s-1-1-1-2c1 0 2-1 3-2l-1-1c0-1 1-1 1-3h-1l1-1z" class="C"></path><path d="M629 637l4 2c3 1 5 1 8 2h4c0 1-1 1-2 2v1h-2l-1-1h-1-2s-1-1-2-1c-1-2-4-3-5-4-1 0-1 0-1-1z" class="S"></path><path d="M628 642c0-1 0-2-1-2l1-1c1 1 2 2 2 3h1l-1-1 1-1c2 1 6 5 9 5l-1-2h1l1 1h2c2-1 3-2 4-2l6-2c0 1-2 2-2 3-5 3-12 5-18 7 0-2-1-3-2-4s-3-3-3-4z" class="P"></path><path d="M600 643c5-2 10-7 14-10l-2 5c2 0 3 1 5 1l1-1h1c-1 1-2 1-3 2h0l-2 2h-1l-1 2-2 2-2 1c-1 1-3 3-4 5h-1v-1l1-2c-1 1-2 1-3 1-6 5-10 12-12 19h-1c-1 1-1 3-2 5h0v-2c-1-1-1-2-1-3l1-3 1-4h-3c1-4 2-6 4-9 4-4 7-7 12-10h0z" class="a"></path><path d="M601 650l6-5 1 2c-1 1-3 3-4 5h-1v-1l1-2c-1 1-2 1-3 1z" class="D"></path><path d="M607 645c2-2 4-5 5-7 2 0 3 1 5 1l1-1h1c-1 1-2 1-3 2h0l-2 2h-1l-1 2-2 2-2 1-1-2z" class="L"></path><path d="M596 650c0 3-3 8-4 10-2 3-3 6-4 9-1 1-1 3-2 5h0v-2c-1-1-1-2-1-3l1-3 1-4c3-5 5-8 9-12z" class="K"></path><defs><linearGradient id="AA" x1="593.633" y1="658.832" x2="590.844" y2="646.063" xlink:href="#B"><stop offset="0" stop-color="#b5b3b5"></stop><stop offset="1" stop-color="#d3d2d1"></stop></linearGradient></defs><path fill="url(#AA)" d="M600 643l1 2c-1 1-1 2-2 2l-3 3h0c-4 4-6 7-9 12h-3c1-4 2-6 4-9 4-4 7-7 12-10z"></path><path d="M306 427c3 1 4 1 7 0-2 1-3 2-4 3 1 1 2 2 3 2v1h-1l-3-1c1 0 1 1 1 1l-1 2h-4l2 9c1 1 1 3 1 4 2 3 3 7 4 10l1 1h4c3-1 5-2 7-4l1-2 3 2h2c2 0 3-1 4-2l3 3 3 3h0l4 4 1-1c1 1 2 1 3 1 2 2 6 5 7 7 1 0 1 0 1 1 1 1 4 3 5 4v1l6 4c1 2 2 3 3 5v2l3 3c3 4 4 7 5 11 1 2 3 4 3 6-1 1-2 1-2 2l-1 4c2 5 3 10 5 15l1 5c0 1 0 1-1 3 5 3 8 12 11 18l2 3 3 4 1 2 2 2c2 1 4 3 5 5l1 2 1 1-1 1 1 1-1 2-1 3c-2 2-4 3-5 5l-2 1v1l1 1c-1 1-1 1-2 1s0 1-1 0h-1l-1 3h2l1-1v3l-1 1v1l-2 2c-2 2-6 6-6 8l1 1 4 5c2 1 3 3 3 5s0 4-1 6l1 1c0 1 1 2 1 3l1 1s1 1 2 1c0 1 3 3 3 4s1 2 2 4l7 7h1c2 2 9 5 9 7l3 3c2 3 4 6 5 9 1 4 1 7 0 11v2c-1 7-4 12-7 19-1 4 0 7 1 12 1 2 3 4 4 6l1 1c3 4 6 7 9 12 1 1 2 2 2 4h-1l-1-1v1c1 2 5 9 5 10-1-3-4-7-6-10-1-1-1-2-2-3l-1 1c-2-1-4-4-5-6v1 2h0l-1-1h-1l-2 2 1 2v3c-2 0-2-1-4-2 1 8 2 14 6 21 2 2 3 4 5 6l-3-1c-3-2-7-7-8-10s-1-6-2-8v-5l-3-1c-6-1-12-6-15-11-1 0-2-2-2-3s3-6 3-8c2-3 3-7 3-11-5-1-11-1-16-4 4-1 9-1 13-3 2-2 4-3 5-6v-1c-8 0-15-3-22-6-3-1-6-3-8-5s-3-6-5-8c-5-3-12-4-18-6-7-3-12-8-18-13-7-7-12-14-18-22l-15-23-2-3h-2c-3 0-6 1-8 0-3-1-5-2-8-4v-3c-1-4 0-13 0-17h0v-1c-1-1 0-2 0-3v-27-7l1 6v-14c0-3 1-6 0-8l-1-1c0-2 0-4 1-6s0-5 0-7v-16c1 0 0-2 1-2-1-1-1-1-2-1l-9 30-1 5c-2 5-3 9-4 14-1 3-1 7-1 10-1 1-1 1-1 2 1 1 1 1 1 2-1 0-2 0-2-1l-1-1c-3-5-6-11-8-17l-10-21-1-2-9-6-2 1c-3-1-6-4-8-6-8-8-9-19-9-30h-8 0c2-1 5-1 7-1h-2-6-1c-2-1-4-1-6-2h5c4 0 8 1 12 0-3 0-8 0-11-1h-1l-26-1c5-3 8-6 12-11l-1-1c2-1 2-2 3-3l1-1c0-1 0-1 1-2h3v-1l2 1 1-1 3 1c2-2 5-7 7-9v-2c1-2 2-3 3-4h1c2-1 6-1 9 0 2 0 6-1 8 0 3 1 5 1 8 1h5l21 1-3-2h2 13l14-1c3 0 7 0 10-1z" class="R"></path><path d="M269 485l3-1-1 7c-1-1-1 0-2-1v-5zm21 97h-1c0-2 1-4 3-5 1 0 1 3 1 4l-1-1-1 1c0 1 1 1 1 2h0c-1 0-1 0-2-1z" class="D"></path><path d="M294 476c-1-2-1-5-1-7l1-1c1 1 1 2 2 4-1 1 0 3 0 5h-1c-1-1 0-1-1-1z" class="O"></path><path d="M290 543v-1c1-1 1-2 2-3l5 9c-2-1-5-3-7-5z" class="D"></path><path d="M429 722h1v1 2h0l-1-1h-1l-2 2 1 2v3c-2 0-2-1-4-2 1-3 4-5 6-7z" class="T"></path><path d="M310 567l6 9h-2c0-1-1-2-2-3h-1c-2-1-2-3-3-5l2-1z" class="S"></path><path d="M269 485l-2-3c0-2 0-3 1-5l1 1h0l1-1h1c0 2 0 2 1 2l1 1-1 4-3 1z" class="O"></path><path d="M408 709h0c0 3-1 5 0 7 0 2 0 4 2 5l1 1c2 0 2 1 3 3h0c-4-1-7-4-9-8 0-3 1-6 3-8z" class="F"></path><path d="M272 479c0-1 1-4 1-5 1-1 1-1 2-1 0-1 0-2 1-3v15 2h-1v-7s0 1-1 1l-1 10c-1-3 1-7 0-11l-1-1z" class="K"></path><path d="M290 502h0c-1-1-1-3 0-4h0c-1-2 0-9 0-11l1-5h1c0 3 1 5 1 7v9h-1v-6h-1v8l-1 2z" class="B"></path><path d="M325 559c3 4 6 8 8 12v4c-1-2-1-2-2-3l-1 1c-1 0-1-1-2-1-1-1-1-3-2-4h0l-1-4v-5z" class="H"></path><path d="M252 480l1-1c0-1 0-1 1-2 1 2 2 5 3 7 0 1 1 2 2 3s1 1 1 3h-1c-1-1-1-1-1-2-1-1-2-3-3-3 1 2 3 5 3 6h-2l-1-1c-2-4-2-6-3-10z" class="I"></path><path d="M296 472l3 11c1 1 1 2 1 3-1 0-1 0-1 1s0 3-1 4c-2-4-3-10-4-15 1 0 0 0 1 1h1c0-2-1-4 0-5z" class="D"></path><path d="M292 498h1v-9c2 5 4 10 5 15 1 1 2 3 2 5h-2c0-1-1-2-1-3-1-2-2-3-2-4 0-2-1-3-1-4l-1 1c0 1 1 2 0 4l-1-1v-2-2z" class="Y"></path><path d="M287 531h1l2 3c0 2 1 3 2 5-1 1-1 2-2 3v1c-2 3-1 5-1 7 0 4 0 10-1 13l-1-32z" class="C"></path><path d="M299 552v-7c1 4 2 9 4 13 2 3 5 6 7 9l-2 1-1-1c-2-1-3-2-4-4l-1-6-2 8c0 1 0 0-1 1 0-2 1-4 1-6 0-3 1-6-1-8z" class="j"></path><path d="M383 666c-2-1-5-4-5-6 1-2 1-2 3-3 1 0 3 0 4 1l1 3 1 5h-4z" class="F"></path><path d="M298 491c1-1 1-3 1-4s0-1 1-1h0l6 16h-1c-1-1-1-1-1-2-1 1-1 2-1 3h0c1 2 1 4 1 6l-6-18z" class="V"></path><path d="M299 552c2 2 1 5 1 8 0 2-1 4-1 6 1-1 1 0 1-1l2-8 1 6h-1v2c0 2 0 5 2 7l1 1h-2c0-2 0-3-1-4-1 1-1 1-1 2h0l-2-2c-1 2-1 3-1 5l-3-4h2l2-18z" class="g"></path><path d="M388 632c3-3 5-5 8-9l1 1c0 1 1 2 1 3l1 1s1 1 2 1c0 1 3 3 3 4s1 2 2 4l-1 1h-1c-1-1-2-2-2-3-1-2-3-3-4-4s-1-2-3-2c-2 1-4 2-6 4l-1-1z" class="f"></path><path d="M399 679c-6-1-15-4-18-9-2-3-3-5-6-7-4-3-8-4-12-6 6 1 11 3 15 7 3 4 4 6 8 8h0c-2-2-2-3-3-6h4c0 2 0 4 1 6l-1 1 9 4 3 1v1h0z" class="M"></path><path d="M258 522c2 2 2 3 3 4h0c3 6 6 14 6 21v1c-3-5-6-11-8-17l1-1c0-2-1-5-2-8z" class="Q"></path><path d="M291 500v-8h1v6 2 2l1 1c1-2 0-3 0-4l1-1c0 1 1 2 1 4-1 2-1 4 0 7h0v8h-1c0 2 0 5 1 7v1 1c-1-2-2-5-2-8-1 0-1-1-1-1v-4c-1-2-1-3-2-5v-6l1-2z" class="S"></path><path d="M291 500c2 6 2 13 2 18-1 0-1-1-1-1v-4c-1-2-1-3-2-5v-6l1-2zm-14-22c0-3 1-5 1-7 0 2 0 6 1 8 0-2 2-9 3-10v3c1-1 1-2 2-4 0 1 0 2 1 2v-1h1v2c0 2 0 4-1 6v-3c-1 2-2 5-3 8v1c0 1 0 1-1 2 1-2 1-4 1-6-1 0-1 1-2 2-2 5-3 12-4 18h0v-2c1-4 3-16 1-19z" class="Q"></path><path d="M290 582c1 1 1 1 2 1h0c0-1-1-1-1-2l1-1 1 1v1l6 9c1 1 1 2 1 3s-1 1-3 1h0c-2 0-4-2-6-4-1-1-2-4-2-6 0-1 1-1 1-2v-1z" class="b"></path><path d="M308 529l17 30v5-1c-2-2-2-3-4-5-3-3-4-6-6-10-1-5-5-9-7-14l1-1c-1-2-1-3-1-4z" class="h"></path><path d="M246 470l-1-1 1-1h0c1 1 2 1 3 1 1 1 1 3 1 5l2 6c1 4 1 6 3 10l-1 2c-1-1-2-3-3-5h-1c-1-1-1-3-2-5-1-4-2-8-2-12z" class="B"></path><path d="M246 470v1c1 2 1 3 2 5 1 1 2 2 2 4l1 2h0c1 1 1 1 1 2l-1 1c-1-2-1-2-3-3-1-4-2-8-2-12z" class="U"></path><path d="M250 474l2 6c1 4 1 6 3 10l-1 2c-1-1-2-3-3-5h-1c-1-1-1-3-2-5 2 1 2 1 3 3l1-1c0-1 0-1-1-2v-3c-1-2-1-3-1-5z" class="Y"></path><path d="M227 489c-1-3-2-6-2-10h1c1 1 1 1 2 1l1 1 1-1v1h1v1h1 1v2c1-1 1-1 2-1v2h0 1v1h1l1 1v1h2v2l-1 2v-1c-1 0-2-1-3-1v-1l-2 2-3-3c-2 0-3 0-4 1z" class="h"></path><path d="M406 637l7 7h1c2 2 9 5 9 7 1 2 3 3 4 5 2 4 3 9 4 13-1 0-1-1-1-2-2-6-5-10-9-14-2-1-4-2-6-4 0-1-1-1-1-2-4-2-7-5-9-9l1-1z" class="d"></path><path d="M235 485c1-1 1-1 1-2v-1l3-3 1 1h1v1h2c1 2 2 5 3 8 1 1 1 2 1 4l2 5-2-1c-1 0-1-1-1-1 0-1-1-1-1-2s-1-2-1-4l-2 1v-2l-1 1h-1v-2h-2v-1l-1-1h-1v-1h-1z" class="D"></path><path d="M237 486l2-3h1v3l2-1h1v2c1 1 1 2 2 2 1 1 1 3 2 4l2 5-2-1c-1 0-1-1-1-1 0-1-1-1-1-2s-1-2-1-4l-2 1v-2l-1 1h-1v-2h-2v-1l-1-1z" class="j"></path><path d="M336 598h1c1 2 2 4 3 5 3 4 7 6 11 9 2 1 6 3 7 4v2 1h0c-9-5-18-10-24-19 0-1 1-1 2-2z" class="m"></path><path d="M281 485c1-1 1-1 1-2v-1l3-8v3c0 1-1 4 0 5 0-1 0-2 1-3l1-4c0 2 0 4-1 5l-1 7-9 30-1-2c1-3 1-6 2-9l3-9v-1-2-1c2-2 0-6 1-8z" class="J"></path><path d="M335 637c3 1 4 3 7 4 6 4 10 9 17 12v-1c0-1 1-2 1-3l-1-2 6 3c0 1 0 3-1 3v1c-1 1-2 3-3 3-11-3-19-12-26-20z" class="G"></path><path d="M359 647l6 3c0 1 0 3-1 3v1c-2 0-2 1-4 1-1 1-3 0-4-1h2 1v-1-1c0-1 1-2 1-3l-1-2z" class="Q"></path><path d="M250 487h1c1 2 2 4 3 5l12 35c0 1 1 2 0 4-2-4-5-8-6-13-4-10-7-21-10-31z" class="V"></path><path d="M315 486c1 0 1 0 2 1l1 2c1 4 3 10 6 13h0 1l1-1h0c1 1 1 1 2 1l1 3-1 2 4 7c2 5 5 9 8 14 1 2 2 3 3 5l-1 2c-11-16-20-32-27-49z" class="W"></path><path d="M325 502l1-1h0c1 1 1 1 2 1l1 3-1 2-3-5z" class="F"></path><path d="M255 490l1 1c1 1 3 5 3 5v1c2 4 6 8 7 12v8 10l-12-35 1-2z" class="U"></path><path d="M374 638c5 0 11 0 15-2l5-2v1 1h-2v1h3c1-1 0-2 2-2v1l-1 1v1h1c0 1 0 1-1 2-1-1-1-1-2-1l-2 2v-1h1l-1-1c-4 1-10 5-13 4l-6-2c-2 0-3-1-4-2-1 0-3-1-5-1l2-2v-1c3 1 5 2 7 2l1 1z" class="f"></path><path d="M366 635c3 1 5 2 7 2l1 1h7v1h0-6l-1 1h1 3c1 0 1 1 2 1 2 0 3-2 5-1-2 1-3 2-4 2s-2 1-2 1l-6-2c-2 0-3-1-4-2-1 0-3-1-5-1l2-2v-1z" class="D"></path><path d="M410 701l3 1c0 1 0 2 1 3 0 3 1 10 0 12h-1c0 2 2 4 3 5l1 1h4c1-1 3-2 4-3h1 2v1c-4 1-11 6-14 4h0c-1-2-1-3-3-3l-1-1c-2-1-2-3-2-5-1-2 0-4 0-7h0c0-2 2-6 2-8z" class="W"></path><path d="M411 722c0-2-1-3-1-5l2-10c1 3 1 7 1 10 0 2 2 4 3 5l1 1h4c1-1 3-2 4-3h1 2v1c-4 1-11 6-14 4h0c-1-2-1-3-3-3z" class="Z"></path><path d="M335 637c-4-4-9-10-12-15h1c9 10 21 19 35 25l1 2c0 1-1 2-1 3v1c-7-3-11-8-17-12-3-1-4-3-7-4z" class="j"></path><path d="M343 584c4 2 7 8 10 11l10 11c0 1-2 4-3 5l-1 3c-1-1-2-3-3-5l-11-19c-1-2-1-4-2-6z" class="P"></path><path d="M356 609l1-1-6-10h0 1c1 1 1 2 2 2 3 2 5 3 5 6 1 2 0 4 1 5l-1 3c-1-1-2-3-3-5z" class="H"></path><defs><linearGradient id="AB" x1="300.09" y1="470.201" x2="306.243" y2="477.112" xlink:href="#B"><stop offset="0" stop-color="#202327"></stop><stop offset="1" stop-color="#3f4143"></stop></linearGradient></defs><path fill="url(#AB)" d="M301 481c-1-2-1-5-1-7-1-2-2-3-1-6 0 1 1 2 1 2h1v-1c1-1 3-2 5-2 0 2 1 4 1 6l2 4 1 5-3 4v-1h-2c0 2 0 3-1 5-1-2-1-4-2-5h0c0-2-1-3-1-4z"></path><path d="M301 481h2 0l1-1c1 0 1 0 2 1v-2c1-2 1-3 1-5v-1l2 4c-1 2-1 3-2 5l-1 1-1-1c-1 1-1 1-1 2h-1l-1 1h0c0-2-1-3-1-4z" class="U"></path><path d="M309 477l1 5-3 4v-1h-2c0 2 0 3-1 5-1-2-1-4-2-5l1-1h1c0-1 0-1 1-2l1 1 1-1c1-2 1-3 2-5z" class="S"></path><path d="M227 489c1-1 2-1 4-1l3 3 2-2v1c1 0 2 1 3 1v1h1l1 2v-1h1c0 1 0 1-1 2l1 1 1-1h0v4h1v-2l1 1v2c0 2 2 4 2 6 1 1 1 1 1 2l-9-6c-4-3-8-6-11-10l-1-3z" class="M"></path><path d="M227 489c1-1 2-1 4-1l3 3 2-2v1c1 0 2 1 3 1v1h1l1 2v-1h1c0 1 0 1-1 2l1 1 1-1h0v4h1v-2l1 1v2c0 2 2 4 2 6-2-1-4-3-5-5-1-3-3-7-6-9h-2l-1-1-2-2h0-2l-1 3-1-3z" class="V"></path><defs><linearGradient id="AC" x1="255.647" y1="511.717" x2="250.12" y2="512.734" xlink:href="#B"><stop offset="0" stop-color="#58585a"></stop><stop offset="1" stop-color="#717172"></stop></linearGradient></defs><path fill="url(#AC)" d="M240 490h1l1-1v2l2-1c0 2 1 3 1 4s1 1 1 2c0 0 0 1 1 1l2 1c3 8 5 17 9 24 1 3 2 6 2 8l-1 1-10-21-1-2c0-1 0-1-1-2 0-2-2-4-2-6v-2l-1-1v2h-1v-4h0l-1 1-1-1c1-1 1-1 1-2h-1v1l-1-2h-1l1-2z"></path><path d="M245 500l1-2c1 1 1 1 1 2 1 2 1 3 2 4v4c1 1 0 1 0 2l-1-2c0-1 0-1-1-2 0-2-2-4-2-6z" class="H"></path><path d="M285 536l1 6v-14c0-3 1-6 0-8l-1-1c0-2 0-4 1-6l1 18 1 32-3 28c-1-4 0-13 0-17h0v-1c-1-1 0-2 0-3v-27-7z" class="M"></path><path d="M259 496c2 2 3 2 5 2 1 0 2-1 3-2 2 1 2 1 4 1-1 8-2 18-2 27 0 1 0 7-1 7l-1 1h-1v-1c1-2 0-3 0-4v-10-8c-1-4-5-8-7-12v-1z" class="K"></path><path d="M277 478c2 3 0 15-1 19v2h0c0 5-2 9-4 14 0 3-1 8-3 11 0-9 1-19 2-27v-6l1-7 1-4c1 4-1 8 0 11l1-10c1 0 1-1 1-1v7h1v-2c1-2 1-5 1-7z" class="V"></path><path d="M277 478c2 3 0 15-1 19v2c-1 2-2 5-2 7-1 2-2 4-2 6 0-7 2-13 1-20v-1l1-10c1 0 1-1 1-1v7h1v-2c1-2 1-5 1-7z" class="N"></path><path d="M276 499c1-6 2-13 4-18 1-1 1-2 2-2 0 2 0 4-1 6s1 6-1 8v1 2 1l-3 9c-1 3-1 6-2 9l1 2-1 5c-2 5-3 9-4 14-1 3-1 7-1 10l-2-15c1 0 1-6 1-7 2-3 3-8 3-11 2-5 4-9 4-14z" class="m"></path><path d="M385 658l6 2c1 1 2 2 3 2h3v1c1 1 1-1 2 1h2c1 1 1 2 2 3 0-1 1-1 2-1h1l1 1h-3v1c0 1 0 0-1 1l1 1-1 3-1 1 1 2h-1c-2 0-2 0-4-1l-2 2-9-4 1-1c-1-2-1-4-1-6l-1-5-1-3z" class="B"></path><path d="M403 669l1 1-1 3-1 1 1 2h-1c-2 0-2 0-4-1h0c2-2 4-4 5-6z" class="U"></path><path d="M385 658l6 2c1 1 2 2 3 2-1 1-1 1-1 3l-1 1-2 2h0c0-1 1-2 1-2l1-3v-1l-1-1v1c-2 0-4 0-5-1l-1-3z" class="I"></path><path d="M388 672h2v-1l2-2h1 0v1c-1 0-1 1-1 2 1 0 1 0 2-1l1-1 2-2 1 1c-1 0-2 1-1 2h0l5-5-5 8c1 1 0 1 1 1h0l-2 2-9-4 1-1z" class="D"></path><path d="M295 502c0 1 1 2 2 4 0 1 1 2 1 3h2l8 20c0 1 0 2 1 4l-1 1c2 5 6 9 7 14l-1-1c-3-3-4-7-7-10h0v1c0 1 1 2 1 3v1c-3-4-4-8-7-12h-1c-3-3-3-8-4-12 0-3 0-7-1-9h0c-1-3-1-5 0-7z" class="W"></path><path d="M300 523c1-1-1-7-1-10l2 5c0 1 1 2 2 3v2 1l1 3-1 1-1-3-1 1 1 1-1 1v-1l-1-1v-1-2z" class="F"></path><path d="M301 530c0-1-1-2-1-2-1-1-1-2-1-3l-1-6c0-2-1-3 0-4 1 2 1 6 2 8h0v2 1l1 1v1l1-1-1-1 1-1 1 3 1-1-1-3v-1l5 11c2 5 6 9 7 14l-1-1c-3-3-4-7-7-10h0v1c0 1 1 2 1 3v1c-3-4-4-8-7-12z" class="a"></path><path d="M317 464c2 3 3 7 5 11 1 3 2 6 2 8v1 1l3 9v1c-1-2-2-5-4-6 0 1 1 2 1 3 2 4 3 7 4 10-1 0-1 0-2-1h0l-1 1h-1 0c-3-3-5-9-6-13l-1-2c-1-1-1-1-2-1v-2c-1-1-3-6-2-8l1 1h0l1-1-1-1c1-1 1-1 1-2h1l-1-4 1-1h1c-1-2-1-3 0-4z" class="Y"></path><path d="M324 502c-1-3-4-9-4-12l1 1c1 1 2 1 3 1 2 4 3 7 4 10-1 0-1 0-2-1h0l-1 1h-1z" class="T"></path><path d="M317 464c2 3 3 7 5 11l-1 2c1 1 1 3 1 5-1-2-1-4-2-6l-1 1c0 1 0 2 1 3v3l1 2h0c-2-2-3-4-5-6l2 8v2l-1-2c-1-1-1-1-2-1v-2c-1-1-3-6-2-8l1 1h0l1-1-1-1c1-1 1-1 1-2h1l-1-4 1-1h1c-1-2-1-3 0-4z" class="f"></path><path d="M411 665c5 4 8 9 9 15-1 2-1 4-3 6l-3 3h0l1 1c-3 2-7 2-10 3 2-1 3-2 5-4h0v-1-2c1-1 1-4 1-5v-1l-1-3-1 1v1c1 1 0 2 0 3 0-2-1-1-2-2-1 1-2 0-4 0-1 0-2-1-4-1h0v-1l-3-1 2-2c2 1 2 1 4 1h1l-1-2 1-1 1-3-1-1c1-1 1 0 1-1v-1h3c0 1 1 1 2 1h0l2 1c1 2 2 2 2 4h1 1l1 1h1l-2-2v-2c-2-1-3-3-4-5z" class="E"></path><path d="M412 681h0c-1-4-3-7-3-11h1l2 2c2 3 3 6 3 10h-1v2h-1v-3h-1 0z" class="j"></path><path d="M415 682l1 1c1-2 0-4 0-6 1 1 1 2 2 3 0 2 0 4-1 6h0l-3 3h0l1 1c-3 2-7 2-10 3 2-1 3-2 5-4h0 0v-1c2-2 2-4 2-7h0 1v3h1v-2h1z" class="n"></path><path d="M403 673h1l1-2c1 1 2 3 2 5v1l1 1c0 1 0 1-1 2s-2 0-4 0c-1 0-2-1-4-1h0v-1l-3-1 2-2c2 1 2 1 4 1h1l-1-2 1-1z" class="Q"></path><path d="M403 676l1 1v-1c1 0 0 0 1-1 0 1 0 2-1 3 0 1 0 1-1 2-1 0-2-1-4-1h0v-1l-3-1 2-2c2 1 2 1 4 1h1z" class="j"></path><path d="M302 565v-2h1c1 2 2 3 4 4l1 1c1 2 1 4 3 5h1c1 1 2 2 2 3h2c1 2 3 5 3 7 2 2 3 4 4 6-1 2-2 2-2 4 0 1-1 1-1 2h-1l-1 1h0v1l-1-1c-3-2-4-3-5-6l-2-2c0-1-1-2-2-4 1-1-3-5-3-7l-2-3v-1h2l-1-1c-2-2-2-5-2-7z" class="H"></path><path d="M306 569c1 2 3 5 5 8l1 2-1 2c0 2 1 3 2 5v1l-1-1c0-1-1-2-1-3l-6-9v-1l-1-1c1-1 1-2 2-3z" class="M"></path><path d="M302 565v-2h1c1 2 2 3 4 4l1 1c1 2 1 4 3 5h1l1 4v1c-1-1-1-2-2-1l-5-8c-1 1-1 2-2 3-2-2-2-5-2-7z" class="P"></path><path d="M302 565c1 1 1 0 1 2 1 0 1 0 1-1 1 1 1 2 2 3-1 1-1 2-2 3-2-2-2-5-2-7z" class="d"></path><path d="M312 573c1 1 2 2 2 3h2c1 2 3 5 3 7 2 2 3 4 4 6-1 2-2 2-2 4 0 1-1 1-1 2h-1v-1h-1-1l1-1h0l-1-1c-1-1-1-2-1-3-2-4-2-7-4-10l-1-2c1-1 1 0 2 1v-1l-1-4z" class="j"></path><path d="M318 593h1c0-1-1-2-1-2l1-1c0-1-1-3-1-5 0-1 0-1 1-2 2 2 3 4 4 6-1 2-2 2-2 4 0 1-1 1-1 2h-1v-1h-1-1l1-1z" class="k"></path><path d="M290 508c1 2 1 3 2 5v4s0 1 1 1c0 3 1 6 2 8v-1-1c-1-2-1-5-1-7h1v-8c1 2 1 6 1 9 1 4 1 9 4 12h1c3 4 4 8 7 12l3 9 1 2v1l2 6 3 6h0c-1-1-1-1-2-1 0-1-1-1-1-2h-1c-1-2-2-3-4-5l-1 1c-6-7-11-20-14-29-3-6-4-15-4-22z" class="U"></path><path d="M295 509c1 2 1 6 1 9 1 4 1 9 4 12h1c3 4 4 8 7 12l3 9 1 2v1l2 6 3 6h0c-1-1-1-1-2-1 0-1-1-1-1-2-1-2-1-5-3-7l-6-14c-2-5-6-11-8-16-1-3-2-6-2-9v-8z" class="h"></path><path d="M294 530h1c2 4 4 10 6 14l-1-7c-1-3-2-5-2-7 2 3 3 5 4 8l-1 1c0 2 3 6 4 7v1c0-2-3-5-2-7 1 1 3 5 3 7 1 3 3 8 5 9 2 2 2 5 3 7h-1c-1-2-2-3-4-5l-1 1c-6-7-11-20-14-29z" class="H"></path><path d="M352 548l11 10h1c4 4 9 7 14 11h0l2-1c1 2 3 4 5 5l3 2c1 0 2 1 3 2 0 1 5 5 6 6h1c2 0 2 1 3 2l-2 1v1l1 1c-1 1-1 1-2 1s0 1-1 0h-1 0c-2 1-3 3-4 4h-1l2-2v-1-4l-1-1h-1c0 2 0 2-1 3-1-1-2-1-2-1-2-1-3-1-4-2s-1-2-2-1h-1l-1-1 1-2c-3 0-5-2-7-4v-1c-1 2 0 3 0 5h-2v-1-2l-2-1c-4-1-8-3-11-6l-6-7c1 0 2 1 3 2 2 2 9 8 13 9h0l1-1v-2-2-1l-3-2h3c0 1 1 2 2 2v1c3 3 7 6 11 8h1l1 1v-1l-3-3-6-3c-3-2-5-5-7-7l-3-3c-2-1-5-4-7-6s-6-5-7-8z" class="L"></path><path d="M378 569h0l2-1c1 2 3 4 5 5l3 2c1 0 2 1 3 2 0 1 5 5 6 6h1c2 0 2 1 3 2l-2 1v1l1 1c-1 1-1 1-2 1s0 1-1 0h-1 0c-2 1-3 3-4 4h-1l2-2v-1-4h2v2h1v-1c1-1 1-1 1-2l-2-1-2-2c-1-1-2-2-4-3-4-2-8-6-11-10z" class="g"></path><path d="M372 578c-1-3-1-6-1-8h0c5 5 12 9 18 13l1 1 1 1c0 2 0 2-1 3-1-1-2-1-2-1-2-1-3-1-4-2s-1-2-2-1h-1l-1-1 1-2c-3 0-5-2-7-4v-1c-1 2 0 3 0 5h-2v-1-2z" class="N"></path><path d="M389 583l1 1-1 2c-1-1-1-1-2-1l2-2z" class="G"></path><path d="M381 581v-1c2 0 3 1 4 2v1c-2 0-3 0-4-2z" class="n"></path><defs><linearGradient id="AD" x1="326.749" y1="580.574" x2="322.751" y2="583.426" xlink:href="#B"><stop offset="0" stop-color="#999799"></stop><stop offset="1" stop-color="#c5c5c4"></stop></linearGradient></defs><path fill="url(#AD)" d="M308 542v-1c0-1-1-2-1-3v-1h0c3 3 4 7 7 10l1 1c2 4 3 7 6 10 2 2 2 3 4 5v1l1 4h0c1 1 1 3 2 4 1 3 2 6 2 9 1 3 1 6 1 9v1c0 1 1 3 3 3v1c1 1 1 2 2 3-1 1-2 1-2 2-2-1-3-3-3-5-4-5-6-11-9-16l-14-20 1-1c2 2 3 3 4 5h1c0 1 1 1 1 2 1 0 1 0 2 1h0l-3-6-2-6v-1l-1-2-3-9z"></path><path d="M322 567h2l1 1h1c1 1 1 3 2 4 1 3 2 6 2 9 1 3 1 6 1 9-2-3-3-7-5-11 0-1-2-3-2-4v-3c-1 1-1 1-1 2l-3-3-1-2 3-2z" class="D"></path><path d="M322 567h2l1 1c0 1 0 2-1 3h-4l-1-2 3-2z" class="R"></path><path d="M308 542v-1c0-1-1-2-1-3v-1h0c3 3 4 7 7 10l1 1c2 4 3 7 6 10 2 2 2 3 4 5v1l1 4h0-1l-1-1h-2l-3 2v2-1c0-1-1-2-1-4v-2c-2-3-4-7-6-11l-1-2-3-9z" class="Z"></path><path d="M321 558c2 2 2 3 4 5v1l1 4h0-1l-1-1h-2l-1-1 1-1c0-3 0-4-1-7z" class="F"></path><path d="M311 551c1-1 1-2 0-3h1c1 3 4 7 5 10 1 2 0 2 0 4 1 0 1 1 1 2-2-3-4-7-6-11l-1-2z" class="a"></path><path d="M374 581c0-2-1-3 0-5v1c2 2 4 4 7 4l-1 2 1 1h1c1-1 1 0 2 1s2 1 4 2c0 0 1 0 2 1 1-1 1-1 1-3h1l1 1v4 1l-2 2h1c1-1 2-3 4-4h0l-1 3h2l1-1v3l-1 1v1l-2 2c-2 2-6 6-6 8-1 0-3 0-5-1l1-1c-4 0-9 0-13 1l-4 2c-1-2-1-3-2-4h1c1-1 1-1 2-1s1-1 2-2h0c2-2 1 0 2 0h2 3 1c2-1 5-1 6-2v-1c-2 0-3 1-4 1h0c-2 0-5-1-6-2l1-1h0l1-1 1-2c-1-1-1 0-1-1l-1-4-1-5-1-1z" class="Y"></path><path d="M378 592v1c2 1 4 2 6 2 0 1-1 2-2 2-2 0-4 0-6-2h0l1-1 1-2z" class="F"></path><path d="M384 585c1 1 2 1 4 2 0 0 1 0 2 1 1-1 1-1 1-3h1v3c-1 1-2 2-2 4-1 1-2 1-4 1h0l-1 1-1-1-2-1h-1-1c1-1 1 0 3-1h1 1 0l-2-2v-1l3 2 1-1-2-1c0-1-1-1-1-2-1-1 0-1 0-1z" class="P"></path><path d="M374 581c0-2-1-3 0-5v1c2 2 4 4 7 4l-1 2 1 1h1c1-1 1 0 2 1 0 0-1 0 0 1 0 1 1 1 1 2l2 1-1 1-3-2v1l2 2h0-1-1v-1h-1c-2-1-3-2-3-4h0l-1-1v1l1 1v3c-1-1-1-1-1-2h0c-1-2-1-2-1-4h2v-1c-2 0-2-1-4-2v1l-1-1z" class="H"></path><path d="M395 592h2l1-1v3l-1 1v1l-2 2c-2 2-6 6-6 8-1 0-3 0-5-1l1-1v-1c1 0 1 0 1-1 2-4 6-7 9-10z" class="J"></path><path d="M381 600l1 2 6-4c-2 1-3 3-5 4v1l2-1v1 1c-4 0-9 0-13 1l-4 2c-1-2-1-3-2-4h1c1-1 1-1 2-1s1-1 2-2h0c2-2 1 0 2 0h2 3 3z" class="E"></path><path d="M371 600c2-2 1 0 2 0h2 3 3c0 2-1 2-1 3h-1v-1c-1 0-3-1-3-1l-2 1c-1-1-2-1-3-2h0z" class="C"></path><defs><linearGradient id="AE" x1="335.541" y1="591.336" x2="359.903" y2="609.414" xlink:href="#B"><stop offset="0" stop-color="#a19f9f"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#AE)" d="M333 571c3 4 6 7 8 11l2 2h0c1 2 1 4 2 6l11 19c1 2 2 4 3 5 0 1-1 3-1 4v-2c-1-1-5-3-7-4-4-3-8-5-11-9-1-1-2-3-3-5h-1c-1-1-1-2-2-3v-1c-2 0-3-2-3-3v-1c0-3 0-6-1-9 0-3-1-6-2-9 1 0 1 1 2 1l1-1c1 1 1 1 2 3v-4z"></path><path d="M338 580c1 1 2 1 3 2l2 2h0c1 2 1 4 2 6-3-2-4-5-6-8l-1-2z" class="H"></path><path d="M339 592h-1c-1-3-3-10-3-13h1l2 1 1 2-1 2h0l3 9v1l2 4h-1c-1-1-1-2-1-3l-1-3h-1z" class="h"></path><path d="M333 571c3 4 6 7 8 11-1-1-2-1-3-2l-2-1h-1c0 3 2 10 3 13h1v2c1 2 1 3 2 5v4h-1c-1-1-2-3-3-5h-1c-1-1-1-2-2-3v-1c-2 0-3-2-3-3v-1c0-3 0-6-1-9 0-3-1-6-2-9 1 0 1 1 2 1l1-1c1 1 1 1 2 3v-4z" class="F"></path><path d="M334 587c1 1 2 2 2 3l1-1v1 1 1h1v1c1 0 1 1 1 1 1 2 1 3 2 5v4h-1c-1-1-2-3-3-5v-2c-2-3-2-6-3-9z" class="T"></path><path d="M333 571c3 4 6 7 8 11-1-1-2-1-3-2l-2-1h-1c0 3 2 10 3 13h1v2s0-1-1-1v-1h-1v-1-1-1l-1 1c0-1-1-2-2-3 1-4-1-9-1-12v-4z" class="W"></path><defs><linearGradient id="AF" x1="417.077" y1="692.616" x2="421.923" y2="699.884" xlink:href="#B"><stop offset="0" stop-color="#39393a"></stop><stop offset="1" stop-color="#525353"></stop></linearGradient></defs><path fill="url(#AF)" d="M417 693c3-2 4-4 6-7 0-1 1-1 1-2 0 3-2 6-2 9v1c0 2 0 5 1 8 0-3 0-5 1-8v1c-1 4 0 7 1 12 1 2 3 4 4 6l1 1c3 4 6 7 9 12 1 1 2 2 2 4h-1l-1-1v1c1 2 5 9 5 10-1-3-4-7-6-10-1-1-1-2-2-3l-1 1c-2-1-4-4-5-6h-1c0-1-1-1-1-1v-1h-2-1c-1 1-3 2-4 3h-4l-1-1c-1-1-3-3-3-5h1c1-2 0-9 0-12-1-1-1-2-1-3l-3-1c0-1 1-3 1-4 2-2 4-3 6-4z"></path><path d="M414 705v-4l1-1v3c0 3 1 7 0 10 0 3 1 6 2 7 1 2 2 2 4 3h-4l-1-1c-1-1-3-3-3-5h1c1-2 0-9 0-12z" class="d"></path><path d="M410 701c0-1 1-3 1-4 2-2 4-3 6-4-2 3-1 7-1 10h-1v-3l-1 1v4c-1-1-1-2-1-3l-3-1z" class="E"></path><path d="M426 715c-1-2-2-3-3-5l-1 1c-2-1-2-5-3-7 0-2 0-6 2-9 0 4 1 7 2 11 0 0 1 1 1 2 1 2 2 3 2 7z" class="i"></path><path d="M422 693v1c0 2 0 5 1 8 0-3 0-5 1-8v1c-1 4 0 7 1 12 1 2 3 4 4 6l1 1c3 4 6 7 9 12 1 1 2 2 2 4h-1l-1-1v1c1 2 5 9 5 10-1-3-4-7-6-10-1-1-1-2-2-3-2-2-5-7-8-8h-1v-1-3h-1c0-4-1-5-2-7 0-1-1-2-1-2-1-4-2-7-2-11 0 0 1-1 1-2z" class="B"></path><path d="M324 492c0-1-1-2-1-3 2 1 3 4 4 6v-1l-3-9v-1-1c9 25 24 50 41 70l8 8c-2 0-3-2-5-3h-4-1l-11-10-5-6-5-7 1-2c-1-2-2-3-3-5-3-5-6-9-8-14l-4-7 1-2-1-3c-1-3-2-6-4-10z" class="G"></path><path d="M329 505l3 6c1 2 3 4 4 7-2-2-2-2-3-4h0-1l-4-7 1-2z" class="V"></path><path d="M343 533c1 1 2 3 4 4-1-2-3-3-3-6l11 15c-3-1-5-3-8-4l-5-7 1-2z" class="P"></path><path d="M332 514h1 0c1 2 1 2 3 4 1 2 8 12 8 13 0 3 2 4 3 6-2-1-3-3-4-4-1-2-2-3-3-5-3-5-6-9-8-14z" class="d"></path><path d="M347 542c3 1 5 3 8 4l1 1c2 1 6 6 9 6l8 8c-2 0-3-2-5-3h-4-1l-11-10-5-6z" class="g"></path><path d="M356 547c2 1 6 6 9 6l8 8c-2 0-3-2-5-3h-4c-2-4-7-7-8-11z" class="h"></path><path d="M314 505l-2-7h1 1 3c11 26 28 51 50 69h0l3 2v1 2 2l-1 1h0c-4-1-11-7-13-9-1-1-2-2-3-2l6 7c3 3 7 5 11 6l2 1v2h-2c-2 0-5-1-7-3l-1-1c-4-3-7-6-10-10 0-1 0-2-1-3 0 0-1 0-1-1-2-2-3-4-5-6 0-1-2-2-3-3h0l-1-2-3-4-2-2c0-1 0-1-1-2l-1-2c-1-1-2-2-2-3l-1-2c-1-1-1-2-1-4-2-4-7-13-11-16l-2-2c0-3-1-6-3-9z" class="G"></path><path d="M353 564h0c-3-3-6-7-8-11l1-1c1 1 1 3 3 4 3 2 6 6 9 8 2 2 4 3 5 5v1c1 1 1 1 2 1h1 0l3 4c-4-1-11-7-13-9-1-1-2-2-3-2z" class="a"></path><path d="M330 532c3 4 5 8 8 12 2 3 5 5 8 8l-1 1c2 4 5 8 8 11h0l6 7c3 3 7 5 11 6l2 1v2h-2c-2 0-5-1-7-3l-1-1c-4-3-7-6-10-10 0-1 0-2-1-3 0 0-1 0-1-1-2-2-3-4-5-6 0-1-2-2-3-3h0l-1-2-3-4-2-2c0-1 0-1-1-2l-1-2c-1-1-2-2-2-3l-1-2c-1-1-1-2-1-4z" class="J"></path><defs><linearGradient id="AG" x1="337.073" y1="546.88" x2="326.286" y2="554.573" xlink:href="#B"><stop offset="0" stop-color="#b8b7b7"></stop><stop offset="1" stop-color="#f2f0f0"></stop></linearGradient></defs><path fill="url(#AG)" d="M302 485h0c1 1 1 3 2 5 0 1 0 3 1 5 4 12 10 24 15 35 4 7 7 15 12 21l5 8 2 2c0 2 1 4 2 6 1 1 2 1 2 3 2 3 4 6 6 8 4 5 9 9 13 14l4 4 5 4h0c-1 1-1 2-2 2s-1 0-2 1h-1c-6-5-11-10-17-16-19-23-34-50-45-78 0-2 0-4-1-6h0c0-1 0-2 1-3 0 1 0 1 1 2h1l-6-16c2 1 1 2 2 3l1-1c-1-1-1-1-1-3h0z"></path><path d="M362 592l4 4v2h-1l-3-3v-3z" class="G"></path><path d="M302 485h0c1 1 1 3 2 5 0 1 0 3 1 5 4 12 10 24 15 35 4 7 7 15 12 21l5 8 2 2c0 2 1 4 2 6 1 1 2 1 2 3-4-4-8-10-12-15-10-17-18-35-25-53l-6-16c2 1 1 2 2 3l1-1c-1-1-1-1-1-3h0z" class="E"></path><path d="M372 605c4-1 9-1 13-1l-1 1c2 1 4 1 5 1l1 1 4 5c2 1 3 3 3 5s0 4-1 6c-3 4-5 6-8 9l-4 1c-2 1-5 1-7 1-5 0-9-2-12-6-2-3-4-7-3-11 0-5 2-7 6-10l4-2z" class="M"></path><path d="M384 605c2 1 4 1 5 1l1 1 4 5c2 1 3 3 3 5s0 4-1 6c-3 4-5 6-8 9l-4 1c-2 1-5 1-7 1-5 0-9-2-12-6-2-3-4-7-3-11 0-5 2-7 6-10l4-2c1 0 0 0 1 1-2 2-5 4-6 7-1 1-2 2-3 4v1 5 1c1 2 3 6 5 7h1 0c1 1 2 1 3 1h0l3 1c2-1 4 0 6 0v-1h2v-1h1c2 0 2-2 2-3 1-1 2-2 2-3h1c0-1 0-2 1-3h0v-1-3l-1-3c-1-1-1-2-1-4h0v-1c-1-1-4-4-6-4v-1h1z" class="G"></path><path d="M384 605c2 1 4 1 5 1l1 1-1 2h0c2 1 2 1 3 3v1h0c-1 0-1-1-1-1l-2-1v-1c-1-1-4-4-6-4v-1h1z" class="l"></path><path d="M373 632h0v-1h-2c-2-2-5-5-5-7v-1h-1v-7h1c1 2 0 5 1 6h1v-3c1-1 1-2 2-3 0 1-1 3 0 4 1-2 1-2 1-3l1-2h0c2-3 3-2 6-3v-1c2 0 4 0 5 1l2 1c0 1 1 1 2 2v6l-4 4h-7c-1-1-1-2-2-3h0c-1-1-1-1-1-2s-1-1-1-1h-1v3h1c0 1 0 1 1 2l1 1c1 0 1 0 2 1h0 2c1 1 1 1 3 1 1-1 1-1 2-1v1c-3 1-4 1-7 0-2-1-4-2-6-4v-1h-1v1c0 1 1 2 2 3l3 3h1c0 1 0 1 1 1h1c1 1 3 1 4 0h1c1 0 2 0 3-1l2-1c0 1 0 3-2 3h-1v1h-2v1c-2 0-4-1-6 0l-3-1z" class="N"></path><path d="M312 594c-1-1-1-2-2-3l-1-1c-1-1-2-3-2-5l1-1c1 2 2 3 2 4l2 2c1 3 2 4 5 6l1 1v-1h0l1-1h1c0-1 1-1 1-2 0-2 1-2 2-4 4 8 8 16 15 22 2 1 3 3 6 4 2 1 4 4 7 5 2 1 4 2 7 3l3 6c1 2 3 5 5 6v1l-2 2c2 0 4 1 5 1 1 1 2 2 4 2l6 2c3 1 9-3 13-4l1 1h-1v1l2-2c1 0 1 0 2 1l-4 4v1l-4 2c-1 0-2 1-2 2v1c-2-1-5-1-8-2l-13-6c-11-4-23-12-31-20s-16-18-23-27l1-1z" class="S"></path><path d="M358 633h2 0c1 0 1 0 1 1v2l2 2h1c2 0 4 1 5 1 1 1 2 2 4 2l-1 1h2c2 1 2 2 4 2h0v1c-1 0-1 0-2-1l-10-3-1-1h-1l-1-1c-3-2-4-3-5-6z" class="b"></path><path d="M358 623l3 6c1 2 3 5 5 6v1l-2 2h-1l-2-2v-2c0-1 0-1-1-1h0-2l-1-4c0-2 0-4 1-6z" class="L"></path><path d="M312 594c-1-1-1-2-2-3l-1-1c-1-1-2-3-2-5l1-1c1 2 2 3 2 4l2 2c1 3 2 4 5 6l1 1v-1h0c2 1 2 2 3 3h0c0 3 1 3 2 5 1 1 2 2 2 3 1 1 2 2 3 4 1 1 3 2 4 4 2 2 5 6 7 7h1c1 2 2 2 3 3v1l3 3c1 0 1 1 2 1s1 1 2 1l-1 1c-4-2-7-6-11-8-3-2-5-5-7-7-2-3-5-6-8-9-2-4-5-6-7-10l-2-2h-1c0-1-1-2-1-2z" class="W"></path><path d="M323 589c4 8 8 16 15 22 2 1 3 3 6 4 2 1 4 4 7 5 2 1 4 2 7 3-1 2-1 4-1 6h-1l-1-1c0-1 0-2-1-3s-1-1-2-1c-1-1-2-1-3-3h-3v-1l-1 1v1c1 1 1 3 1 4v1c-1 0-2-1-3-1v-1c-1-1-2-1-3-3h-1c-2-1-5-5-7-7-1-2-3-3-4-4-1-2-2-3-3-4 0-1-1-2-2-3-1-2-2-2-2-5h0c-1-1-1-2-3-3l1-1h1c0-1 1-1 1-2 0-2 1-2 2-4z" class="H"></path><path d="M325 605h4c1 0 3 3 4 4v1h0l-1 1v-1h0l-1-1v2l-1-1c0-1 0-1-1-1 0-2 0-2-1-3h-1l1 1h-1l-2-2z" class="P"></path><path d="M338 611c2 1 3 3 6 4 2 1 4 4 7 5 2 1 4 2 7 3-1 2-1 4-1 6h-1l-1-1c0-1 0-2-1-3s-1-1-2-1c-1-1-2-1-3-3h-3v-1l-1 1v1 3h0c-1-1-1-2-1-3s-1-2-1-3h-1v3h-1v-5l-1-1v5c-1-1-1-2-1-3h0v-3c-1-2-1-3-1-4z" class="Y"></path><path d="M323 589c4 8 8 16 15 22 0 1 0 2 1 4v3h0l-1-1h-1c0-2 0-2-1-3v2 1l-1-1v-1l1-1-1-1c-1 1-1 1-2 1 1 0 1-1 1-2l-1-2h0v-1c-1-1-3-4-4-4h-4c-1-2-3-4-4-6h0c-1-1-1-2-3-3l1-1h1c0-1 1-1 1-2 0-2 1-2 2-4z" class="g"></path><path d="M310 482l2 3 1 4 4 9h-3-1-1l2 7c2 3 3 6 3 9l2 2c4 3 9 12 11 16 0 2 0 3 1 4l1 2c0 1 1 2 2 3l1 2c1 1 1 1 1 2l2 2 3 4 1 2h0c1 1 3 2 3 3 2 2 3 4 5 6 0 1 1 1 1 1 1 1 1 2 1 3 3 4 6 7 10 10l1 1c2 2 5 3 7 3h2v1h2l1 1 1 5 1 4c0 1 0 0 1 1l-1 2-1 1h0l-1 1c1 1 4 2 6 2h0c1 0 2-1 4-1v1c-1 1-4 1-6 2h-1-3-2c-1 0 0-2-2 0l-5-4-4-4c-4-5-9-9-13-14-2-2-4-5-6-8 0-2-1-2-2-3-1-2-2-4-2-6l-2-2-5-8c-5-6-8-14-12-21-5-11-11-23-15-35-1-2-1-4-1-5 1-2 1-3 1-5h2v1l3-4z" class="F"></path><path d="M339 561c3 1 6 6 7 9 3 1 4 4 6 6h-1l-1-1-1-1h-1v1c1 1 1 1 1 3-2-2-4-5-6-8 0-2-1-2-2-3-1-2-2-4-2-6z" class="L"></path><path d="M365 588s0-1-1-1c0-1-2-3-3-4-3-3-6-5-8-8-1-1-3-3-3-4 2 3 6 8 10 9h0l3 4c3 3 5 6 9 6h0l-1 2c1 1 2 1 4 2l1 1h0l-1 1c1 1 4 2 6 2h0c1 0 2-1 4-1v1c-1 1-4 1-6 2h-1-3l-1-1c-2-1-3-2-4-4 2 1 3 1 4 2v-1l-9-8z" class="P"></path><path d="M349 578c0-2 0-2-1-3v-1h1l1 1 1 1h1l13 12 9 8v1c-1-1-2-1-4-2 1 2 2 3 4 4l1 1h-2c-1 0 0-2-2 0l-5-4-4-4c-4-5-9-9-13-14z" class="Q"></path><path d="M352 566c3 4 6 7 10 10l1 1c2 2 5 3 7 3h2v1h2l1 1 1 5 1 4c0 1 0 0 1 1l-1 2-1 1-1-1c-2-1-3-1-4-2l1-2h0c-4 0-6-3-9-6l-3-4c0-1-2-3-3-5l1-1-1-1c-1-2-5-5-5-7z" class="D"></path><path d="M372 581h2l1 1 1 5h-2l-1 1-1-7z" class="M"></path><path d="M360 580c0-1-2-3-3-5l1-1c1 2 3 4 5 6 0 2 1 3 0 4l-3-4z" class="V"></path><path d="M373 588l1-1h2l1 4c0 1 0 0 1 1l-1 2-1 1-1-1-1-3-1-3z" class="M"></path><path d="M373 588l1-1h2v2l-1 1s-1 0-1 1l-1-3z" class="N"></path><path d="M363 580c2 1 4 2 6 4v1l1 1v2c1 1 2 1 2 2h0c-4 0-6-3-9-6 1-1 0-2 0-4z" class="F"></path><path d="M310 482l2 3 1 4 4 9h-3-1-1l2 7c2 3 3 6 3 9l2 2c4 3 9 12 11 16 0 2 0 3 1 4l1 2c0 1 1 2 2 3l1 2c1 1 1 1 1 2l2 2 3 4 1 2h0c1 1 3 2 3 3s0 2 1 3l-1 1c-1-2-3-5-5-6h0l-1 1 5 4c0 2 2 5 4 6l2 2-1 1c-3-3-5-6-8-8l-4-4c-1-3-3-4-4-7 0-1 0-1-1-1-1-2-2-4-3-5 0-2-1-3-2-4h0v2c1 1 1 3 2 4 1 0 1 1 1 2 1 0 1 0 1 1v1c1 1 1 1 1 2-5-6-8-14-12-21-5-11-11-23-15-35-1-2-1-4-1-5 1-2 1-3 1-5h2v1l3-4z" class="a"></path><path d="M319 516c4 3 9 12 11 16 0 2 0 3 1 4l1 2c0 1 1 2 2 3l1 2c1 1 1 1 1 2l2 2 3 4 1 2h0c1 1 3 2 3 3s0 2 1 3l-1 1c-1-2-3-5-5-6h0l-2-3-2-2c-2-3-5-7-6-10 0-1 0-1-1-2v-1l-1-1-1-1c0-2-1-4-2-5l-4-8c-1-2-2-3-2-5z" class="Z"></path><defs><linearGradient id="AH" x1="316.278" y1="493.602" x2="307.417" y2="511.504" xlink:href="#B"><stop offset="0" stop-color="#959493"></stop><stop offset="1" stop-color="#c5c3c4"></stop></linearGradient></defs><path fill="url(#AH)" d="M310 482l2 3 1 4 4 9h-3-1-1l2 7c2 3 3 6 3 9h0v1 1h0c-1-1-1-2-2-3-1 0-1 0-1-1l-3-6h-1v-1c-1-3-2-8-4-11l-1 1c-1-2-1-4-1-5 1-2 1-3 1-5h2v1l3-4z"></path><path d="M314 505l-1-1v-1l-1-1v-2c-1-2-1-6-1-8l2-3 4 9h-3-1-1l2 7z" class="V"></path><path d="M310 482l2 3c-1 1-3 2-3 3v1c0 1-2 1-3 1 0 5 4 11 5 16h-1v-1c-1-3-2-8-4-11l-1 1c-1-2-1-4-1-5 1-2 1-3 1-5h2v1l3-4z" class="P"></path><path d="M411 665c-9-7-22-9-33-13-15-6-32-13-44-25-6-5-10-11-15-17s-11-12-15-18c-4-7-6-15-11-21v-2h1l1 1 3 4c0-2 0-3 1-5l2 2h0c0-1 0-1 1-2 1 1 1 2 1 4v1l2 3c0 2 4 6 3 7l-1 1c0 2 1 4 2 5l1 1c1 1 1 2 2 3l-1 1c7 9 15 19 23 27s20 16 31 20l13 6c3 1 6 1 8 2v-1c0-1 1-2 2-2l4-2v-1l4-4c1-1 1-1 1-2 1-1 3-1 4-2 0 1 1 2 1 3l-1 1c2 1 1 2 2 4l1-3 3 3c2 2 4 3 5 5l7 10c4 6 6 12 4 19v2c-2 4-4 8-8 10l-1-1h0l3-3c2-2 2-4 3-6-1-6-4-11-9-15z" class="G"></path><path d="M412 649l7 10-1 2h0c-1-2-4-5-5-7v-2l-1-3z" class="J"></path><path d="M401 650c1-1 1-1 1-3h3c0 2 0 2 2 3h-1-1l-1 3c-2 1-3 0-4 1h-1-1l1-1c1-1 1-2 2-3z" class="a"></path><path d="M419 659c4 6 6 12 4 19v2c-2-2-1-7-2-10 0-3-1-6-3-9l1-2z" class="T"></path><path d="M298 574c0-2 0-3 1-5l2 2h0c0-1 0-1 1-2 1 1 1 2 1 4v1l2 3c0 2 4 6 3 7l-1 1c0 2 1 4 2 5l1 1c1 1 1 2 2 3l-1 1c-5-6-9-15-13-21zm99 64c1-1 3-1 4-2 0 1 1 2 1 3l-1 1c2 1 1 2 2 4l1-3 3 3c-1 1-2 2-2 3h-3c0 2 0 2-1 3s-1 2-2 3l-1 1-3-1-9-3v-1c0-1 1-2 2-2l4-2v-1l4-4c1-1 1-1 1-2z" class="P"></path><path d="M392 644v1c-1 1-1 3-2 4h0 1c1-2 2-3 4-5v1l-2 2-1 2c1 1 3 2 5 2l-2 2-9-3v-1c0-1 1-2 2-2l4-2v-1z" class="d"></path><path d="M403 644l1-3 3 3c-1 1-2 2-2 3h-3c0 2 0 2-1 3s-1 2-2 3l-1 1-3-1 2-2c-2 0-4-1-5-2l1-2v1l2-1 1-2 2-1c-1-1 0-1 0-2 2 0 2 1 4 2v2c1-1 1-1 1-2z" class="T"></path><path d="M396 645l3 1c0 2 0 2-1 3l-1-1h-2v-1l1-2z" class="d"></path><path d="M397 651c1 0 2-1 3-2h1v1c-1 1-1 2-2 3l-1 1-3-1 2-2z" class="F"></path><path d="M306 427c3 1 4 1 7 0-2 1-3 2-4 3 1 1 2 2 3 2v1h-1l-3-1c1 0 1 1 1 1l-1 2h-4l2 9c1 1 1 3 1 4 2 3 3 7 4 10l1 1h4c3-1 5-2 7-4l1-2 3 2h2l-3 3-9 5h-1c-10 2-20 2-29 2h-38l-18-2h-22l-26-1c5-3 8-6 12-11l-1-1c2-1 2-2 3-3l1-1c0-1 0-1 1-2h3v-1l2 1 1-1 3 1c2-2 5-7 7-9v-2c1-2 2-3 3-4h1c2-1 6-1 9 0 2 0 6-1 8 0 3 1 5 1 8 1h5l21 1-3-2h2 13l14-1c3 0 7 0 10-1z" class="o"></path><path d="M202 443l2 1-9 7-1-1c2-1 2-2 3-3l1-1c0-1 0-1 1-2h3v-1z" class="R"></path><path d="M218 429h1c2-1 6-1 9 0h-5l1 1c0 1-2 4-2 5-1 2-1 4-2 6-1 0-2 1-2 2 1 1 1 1 1 2v1l-1 2c0 1-2 3-2 4h1v1c0 1-1 1-1 2l1 1 1 2h4-10c-2-1-3-1-4-1h-1c-1 0-1 0-2 1-2 0-10 0-11-1l1-1c5-4 9-8 13-12 2-2 5-7 7-9v-2c1-2 2-3 3-4z" class="U"></path><path d="M218 443c1 1 1 1 1 2v1l-1 2h0c-2 0-3 0-4 1l4-6z" class="J"></path><path d="M214 449c1-1 2-1 4-1h0c0 1-2 3-2 4h1v1c0 1-1 1-1 2l1 1 1 2h4-10c-2-1-3-1-4-1h-1l7-8z" class="G"></path><path d="M216 452h1v1c0 1-1 1-1 2l1 1 1 2h-7c2-2 4-4 5-6z" class="Q"></path><path d="M215 435l1 2 2-3h3v1c-1 2-1 4-3 6s-3 4-5 7l-6 6c0 1-1 2-1 2-4 1-8 0-11 0 5-4 9-8 13-12 2-2 5-7 7-9z" class="S"></path><path d="M205 449c3-3 4-7 8-10 0 2-2 3-2 5h0l-1 1c-1 3-2 4-5 4z" class="g"></path><path d="M211 444c1-2 3-5 6-5l1 2c-2 2-3 4-5 7l-3-3 1-1z" class="T"></path><path d="M210 445l3 3-6 6h-2-1-4l5-5c3 0 4-1 5-4z" class="F"></path><path d="M306 427c3 1 4 1 7 0-2 1-3 2-4 3 1 1 2 2 3 2v1h-1l-3-1c1 0 1 1 1 1l-1 2h-4l2 9c1 1 1 3 1 4l-1 1 4 10c-1 0-1 1-2 1-5 1-12 0-17 0l-1-7-1 1c1 2 1 4 1 5h-15c-1 0-1 0-1-2h-1l-1-1c1-1 1-3 1-4-1-1-1 0-1-1l-1-1 1-4h0v-3c0-2 0-5 1-8v-1c0-1 0-2 1-2v-1h-4l-3-2h2 13l14-1c3 0 7 0 10-1z" class="O"></path><path d="M308 460v-2c-2-3-4-7-5-10v-1h-3c0-1 1-2 2-2l-1-1c0 1-1 1-1 1l-4-4c0 2 1 3 2 5h0c-2-1-3-2-3-4v-1c-1-1-1-1-1-2v-1-1c1-1 2 0 4 0l1 1 1-1c1 0 2 0 3 1l3 11 4 10c-1 0-1 1-2 1z" class="U"></path><path d="M306 427c3 1 4 1 7 0-2 1-3 2-4 3 1 1 2 2 3 2v1h-1l-3-1c1 0 1 1 1 1l-1 2h-4l2 9c1 1 1 3 1 4l-1 1-3-11-2-7h-1l-11 1v11c1 3 2 7 1 10l-1 1v-2c0-3-1-6-1-9 0-4 1-9-1-12h-9-2c1-1 1-1 2-1l-8-1h-1 13l14-1c3 0 7 0 10-1z" class="P"></path><path d="M304 435l-1-3v-1c2 0 4 0 5 1 1 0 1 1 1 1l-1 2h-4z" class="O"></path><path d="M267 429h2 1l8 1c-1 0-1 0-2 1h2 9c2 3 1 8 1 12 0 3 1 6 1 9v2c1 2 1 4 1 5h-15c-1 0-1 0-1-2h-1l-1-1c1-1 1-3 1-4-1-1-1 0-1-1l-1-1 1-4h0v-3c0-2 0-5 1-8v-1c0-1 0-2 1-2v-1h-4l-3-2z" class="B"></path><path d="M276 431h2l-1 1 1 1c0 3-1 5-1 8h-2c0-3-1-8 1-10z" class="Q"></path><path d="M272 443c1 1 1 2 1 3l2 2c0 3-1 9 0 11-1 0-1 0-1-2h-1l-1-1c1-1 1-3 1-4-1-1-1 0-1-1l-1-1 1-4h0v-3z" class="Z"></path><path d="M267 429h2 1l8 1c-1 0-1 0-2 1-2 2-1 7-1 10v7l-2-2c0-1 0-2-1-3 0-2 0-5 1-8v-1c0-1 0-2 1-2v-1h-4l-3-2z" class="d"></path><path d="M278 431h9c2 3 1 8 1 12 0 3 1 6 1 9l-2-2v-1c-3-1-4-3-7-4h-1l-2-2c1-1 1-1 2-1h1c0-1 1-1 3-1v2h2v1-3c0-1-1-1-2-2-1 1-2 1-3 1l-1-1h2c1-1 1-1 2-1s2 0 2 1h1v-1c-2-1-2-2-3-4v-1c-1 0-1 1-1 1-1 1-2 2-3 2v-1l2-2h-3l-1-1 1-1z" class="Y"></path><path d="M228 429c2 0 6-1 8 0 3 1 5 1 8 1h5l21 1h4v1c-1 0-1 1-1 2v1c-1 3-1 6-1 8v3h0l-1 4 1 1c0 1 0 0 1 1 0 1 0 3-1 4l1 1-1 2c-2 1-6 0-8 0h-3l-12 1c-2 0-4 0-6-1l-14-1h-3-4-4l-1-2-1-1c0-1 1-1 1-2v-1h-1c0-1 2-3 2-4l1-2v-1c0-1 0-1-1-2 0-1 1-2 2-2 1-2 1-4 2-6 0-1 2-4 2-5l-1-1h5z" class="k"></path><path d="M229 434l-1-1c0 1 0 1-1 2l1-4v-1c-1 1-1 1-2 1v-1h11v1c-2-1-3-1-5-1-2 2-2 3-3 4z" class="U"></path><path d="M229 434c1-1 1-2 3-4 2 0 3 0 5 1v2h-3l-1 1-3 1-1-1z" class="j"></path><path d="M222 435c1 2 0 3 0 5h1l2-4 1-1c-1 2-2 7-4 8h0l-1 2-2 1v-1c0-1 0-1-1-2 0-1 1-2 2-2 1-2 1-4 2-6z" class="a"></path><path d="M237 433h1l-1 1c-2 1-2 4-3 5 0 2-1 3-2 4v-2l-1-1h0c0-1 1-1 1-2h-3c1-2 2-1 4-2v-2l1-1h3z" class="g"></path><path d="M232 443c1-1 2-2 2-4 1-1 1-4 3-5-1 6-2 11-4 16l-1 1c-1 0-2 0-3 1l-1-2v-1h2l2-5v-1z" class="m"></path><path d="M227 445c2-1 4-1 5-1l-2 5h-2v1l1 2h0l-3 6h-4-4l-1-2-1-1c0-1 1-1 1-2v-1h-1c0-1 2-3 2-4l1-2 2-1c1 0 1 0 2-1 0 1-1 2 0 3 1-2 3-2 4-2z" class="T"></path><path d="M223 447c1-2 3-2 4-2l1 1c-1 1-3 1-5 2v-1h0zm-4 5c0-1 1-1 1-2 2-1 6-1 8-1v1l1 2h0c-3-1-7 0-10 0z" class="V"></path><path d="M219 452c3 0 7-1 10 0l-3 6h-4-4l-1-2-1-1c0-1 1-1 1-2l1 1 1-2z" class="S"></path><path d="M216 455h1c1 0 2-1 4-1 1-1 4-1 5-1l1 1-1 2h-9l-1-1z" class="V"></path><path d="M238 435c1-1 1-2 2-3h3c1 0 1 0 1 1v1h2c1-1 2 0 3-1h0 1c-1 8-1 17-4 25-1 0-1 0 0 1l3 1c-2 0-4 0-6-1l-14-1h-3l3-6h0c1-1 2-1 3-1l1-1 1-1c2-2 2-4 2-7l1-3 1-4z" class="W"></path><path d="M229 452c1-1 2-1 3-1v1l-3 6h-3l3-6h0z" class="n"></path><path d="M240 455c-1 0-1 1-2 2h0c-2 1-5 1-7 0 1-1 1-2 2-3h5l1 1c1-1 1-1 2-1l-1 1z" class="N"></path><path d="M234 449h1 2c1-1 0-1 1-1v1c2 2 2 2 4 2h1v1h-11v-1l1-1 1-1z" class="J"></path><path d="M243 436v1 2c1 0 1 0 2-1v1c-1 0-1 1-3 1v1h2l1 1c-2 1-4 1-6 0 0-2 0-3 1-5h2l1-1z" class="T"></path><path d="M236 442c1 1 3 2 4 2h2l-1 2v2h1c1-1 1-1 1-2v-1h1v1c-1 2-1 4-1 5h-1c-2 0-2 0-4-2v-1c-1 0 0 0-1 1h-2-1c2-2 2-4 2-7z" class="a"></path><path d="M238 435c1-1 1-2 2-3h3c1 0 1 0 1 1v1h2c1-1 2 0 3-1-1 4-2 7-3 11l-1-2-1-1h-2v-1c2 0 2-1 3-1v-1c-1 1-1 1-2 1v-2-1c0-1 1-2 0-3l-5 2z" class="P"></path><path d="M249 433h0 1c-1 8-1 17-4 25-1 0-1 0 0 1l3 1c-2 0-4 0-6-1l-2-1s-1 0-2-1c1-1 1-1 1-2l1-1c1 0 1 1 2 0l3-10c1-4 2-7 3-11z" class="m"></path><path d="M249 430l21 1h4v1c-1 0-1 1-1 2v1c-1 3-1 6-1 8v3h0l-1 4 1 1c0 1 0 0 1 1 0 1 0 3-1 4l1 1-1 2c-2 1-6 0-8 0h-3l-12 1-3-1c-1-1-1-1 0-1 3-8 3-17 4-25v-1l-1-1v-1z" class="S"></path><path d="M271 452c-1 0-2-1-2-1h-1v-1l2-1-1-1-1-1v-1h1 1 1 1l-1 4v2z" class="f"></path><path d="M271 452v-2l1 1c0 1 0 0 1 1 0 1 0 3-1 4h0c0 1-1 1-1 2h-6c1-2 1-4 2-6v1c2 0 2 0 4-1z" class="a"></path><path d="M261 444c3-3 1-11 4-12h2c0 3-1 9-1 12h-2-3z" class="J"></path><path d="M252 436c1 1 0 4 0 6h0c1 0 1-1 2-2v-2c0 2 0 3 1 5h0l-2 2 1 1 1 4 1 2c-1 0-2 1-3 2l1 1h-2c-1-2-2-2-4-3l4-16z" class="N"></path><path d="M256 452h4c0 1 0 0 1 2l-1 5h-4l-9-1 1-6c2 1 3 1 4 3h2l-1-1c1-1 2-2 3-2z" class="n"></path><path d="M252 436c-1-2 0-2 1-4h10c0 3-1 6-1 9v1h-3c1-2 1-2 1-3-1 0-1 0-2 1v-3h0v-1-2c-1 1-1 1-1 2-1 2-1 5-2 7-1-2-1-3-1-5v2c-1 1-1 2-2 2h0c0-2 1-5 0-6z" class="P"></path><path d="M255 443c1-2 1-5 2-7 0-1 0-1 1-2v2 1h0v3c1-1 1-1 2-1 0 1 0 1-1 3h3l-1 2h3 2c-1 5-1 11-2 15h-3v-5c-1-2-1-1-1-2h-4l-1-2-1-4-1-1 2-2h0z" class="G"></path><defs><linearGradient id="AI" x1="304.144" y1="489.563" x2="417.32" y2="550.728" xlink:href="#B"><stop offset="0" stop-color="#161718"></stop><stop offset="1" stop-color="#353637"></stop></linearGradient></defs><path fill="url(#AI)" d="M333 453l3 3 3 3h0l4 4 1-1c1 1 2 1 3 1 2 2 6 5 7 7 1 0 1 0 1 1 1 1 4 3 5 4v1l6 4c1 2 2 3 3 5v2l3 3c3 4 4 7 5 11 1 2 3 4 3 6-1 1-2 1-2 2l-1 4c2 5 3 10 5 15l1 5c0 1 0 1-1 3 5 3 8 12 11 18l2 3 3 4 1 2 2 2c2 1 4 3 5 5l1 2 1 1-1 1 1 1-1 2-1 3c-2 2-4 3-5 5-1-1-1-2-3-2h-1c-1-1-6-5-6-6-1-1-2-2-3-2l-3-2c-2-1-4-3-5-5l-2 1h0c-5-4-10-7-14-11h4c2 1 3 3 5 3l-8-8c-17-20-32-45-41-70 0-2-1-5-2-8-2-4-3-8-5-11v-1l9-5 3-3c2 0 3-1 4-2z"></path><path d="M335 498c-3-3-4-8-5-12l2 3c2 3 4 6 6 8-2 1-2 1-3 1z" class="c"></path><path d="M334 482c2 1 3 3 5 5h0l3 5v1 1 1h0l-1 1c-1-2-3-5-4-7-1-3-3-4-3-7z" class="Q"></path><path d="M353 529l3 2c3 3 5 7 8 11-1 1-1 1-1 3l-10-15v-1z" class="i"></path><path d="M370 522c2 0 3 4 6 3l6 3 1 5h-1c-1-1-3-3-5-3-4-2-8-5-11-8h1 1 2z" class="P"></path><path d="M364 542c3 3 5 5 8 7l3 3c0 2 2 3 3 4s2 3 3 4c-1-1-3-1-5-2-4-4-9-8-13-13 0-2 0-2 1-3zm0 16h4c2 1 3 3 5 3l25 22h-1c-1-1-6-5-6-6-1-1-2-2-3-2l-3-2c-2-1-4-3-5-5l-2 1h0c-5-4-10-7-14-11z" class="W"></path><defs><linearGradient id="AJ" x1="333.255" y1="476.7" x2="338.645" y2="483.29" xlink:href="#B"><stop offset="0" stop-color="#424243"></stop><stop offset="1" stop-color="#5d5d5e"></stop></linearGradient></defs><path fill="url(#AJ)" d="M334 482c-3-3-6-6-8-9 0-1-2-4-1-5v1s0 1 1 2l1-1c1 1 2 4 3 4h1l-2-2h1s1 0 2 1 1 1 2 3c1 3 6 6 8 8h3c1 1 2 2 2 3 2 4 8 6 11 9-3-1-4-2-6-3-1 0-3-1-4-1h-1-1c-1 0-6-4-7-5h0c-2-2-3-4-5-5z"></path><defs><linearGradient id="AK" x1="333.937" y1="506.281" x2="347.602" y2="509.026" xlink:href="#B"><stop offset="0" stop-color="#454748"></stop><stop offset="1" stop-color="#606061"></stop></linearGradient></defs><path fill="url(#AK)" d="M338 497l5 7c0 1 2 2 2 3 0 2 3 6 5 8l-6-5c2 4 5 7 6 11l6 9v1l-3-2v1c-2-2-4-5-5-7-5-8-10-16-13-25 1 0 1 0 3-1z"></path><path d="M353 529c0-1-2-3-3-4l-4-7 1-1v1l2 2 1 1 6 9v1l-3-2z" class="j"></path><path d="M345 507c2 2 5 5 7 8v1l-1 2 2 2c0 2 4 7 5 9l1 1c4 5 12 11 14 17l-2-1c1 2 1 2 1 3-3-2-5-4-8-7-3-4-5-8-8-11v-1l-6-9c-1-4-4-7-6-11l6 5c-2-2-5-6-5-8z" class="W"></path><path d="M356 530c5 6 11 10 15 16 1 2 1 2 1 3-3-2-5-4-8-7-3-4-5-8-8-11v-1z" class="E"></path><path d="M375 552c7 5 16 9 24 11l2 2c2 1 4 3 5 5l1 2h0-1v1 1l-4 4h-3c-1 0-2-2-3-2l-11-9c-3-2-6-5-9-9 2 1 4 1 5 2-1-1-2-3-3-4s-3-2-3-4z" class="P"></path><path d="M378 556c6 2 10 7 15 10h0c0 2 7 5 9 7h-1c-1 0-3-1-4-2l-4-2c-4-3-8-5-12-9-1-1-2-3-3-4z" class="H"></path><path d="M375 552c7 5 16 9 24 11l2 2c2 1 4 3 5 5-1 0-2 1-3 1-1 1-3-1-5-2l-5-3h0c-5-3-9-8-15-10-1-1-3-2-3-4z" class="J"></path><path d="M401 565c2 1 4 3 5 5-1 0-2 1-3 1-1 1-3-1-5-2h3c1 1 1 1 2 0-1-1-1-2-2-3v-1z" class="h"></path><path d="M352 515l7 6c7 6 15 10 23 15 5 3 8 12 11 18l2 3 3 4 1 2c-8-2-17-6-24-11l-3-3c0-1 0-1-1-3l2 1c-2-6-10-12-14-17l-1-1c-1-2-5-7-5-9l-2-2 1-2v-1z" class="H"></path><path d="M352 515l7 6v4c-1-1-3-2-4-4s-1-3-3-5v-1z" class="a"></path><path d="M385 552c-6-3-9-9-14-12v-1h0c1 0 2 1 3 2 1 2 3 3 5 5h0c-2-4-4-7-8-10-1 0-1-1-1-1h0 1c2 2 5 5 7 5 2 1 3 3 4 4-1 0-1 1-2 1l2 1v1l-1 1h1c1 1 2 2 3 4z" class="V"></path><path d="M371 546l2 1 9 6c2 1 5 2 6 3v1l1 1h1c2 0 5 3 8 3l1 2c-8-2-17-6-24-11l-3-3c0-1 0-1-1-3z" class="Q"></path><path d="M359 521c7 6 15 10 23 15 5 3 8 12 11 18l2 3v1c-4-2-7-4-10-6-1-2-2-3-3-4h-1l1-1v-1l-2-1c1 0 1-1 2-1-1-1-2-3-4-4 0-1-1-2-2-2-2-3-5-4-8-6s-6-4-9-7v-4z" class="N"></path><path d="M385 552c-1-2-2-3-3-4h-1l1-1 2 2 1-1c1 1 2 3 4 4 1 1 3 1 4 2l2 3v1c-4-2-7-4-10-6z" class="M"></path><path d="M339 487c1 1 6 5 7 5h1 1c1 0 3 1 4 1 2 1 3 2 6 3l16 6h0c0-2 0-3-1-4v-2c-1-2-2-1-2-3h1 0v-3c3 4 4 7 5 11 1 2 3 4 3 6-1 1-2 1-2 2l-1 4c2 5 3 10 5 15l-6-3c-3 1-4-3-6-3h-2-1-1c-2 0-3-2-5-3-4-4-8-9-12-13l-3-2-5-8 1-1h0v-1-1-1l-3-5z" class="Y"></path><path d="M342 493l2 2-1 1c3 3 3 4 3 8l-5-8 1-1h0v-1-1z" class="U"></path><path d="M349 506c3 1 7 6 10 8h0c-2-3-6-7-9-9l1-1s2 1 2 2c2 1 3 3 4 4l2 1s1 1 2 1l3 2 5 5h-1c-2-1-4-3-6-5l-1 1c3 2 6 4 9 7h-2-1-1c-2 0-3-2-5-3-4-4-8-9-12-13z" class="g"></path><path d="M361 512l-3-3c-1 0-2-1-3-2h2c0 1 1 1 2 2l1 1 2 1v-1-1c1 1 1 2 1 3h1v-2h1v1c0 1 1 2 2 3v-1c-1-1-1-1-1-3h1l1 2 1 1v-3c1 0 1 1 2 1v-1c1 1 1 1 2 1h2c1 0 1 0 2 2 2 5 3 10 5 15l-6-3c-3 1-4-3-6-3-3-3-6-5-9-7l1-1c2 2 4 4 6 5h1l-5-5-3-2z" class="M"></path><path d="M375 511c1 0 1 0 2 2 2 5 3 10 5 15l-6-3-2-2c-1-2-1-2-1-4l-1-1 1-2v-1l-1-3 1-1h2z" class="N"></path><g class="J"><path d="M375 511c0 2-2 3-2 5v-1l-1-3 1-1h2z"></path><path d="M339 487c1 1 6 5 7 5h1 1c1 0 3 1 4 1 2 1 3 2 6 3l16 6h0c0-2 0-3-1-4v-2c-1-2-2-1-2-3h1 0v-3c3 4 4 7 5 11 1 2 3 4 3 6-1 1-2 1-2 2l-1 4c-1-2-1-2-2-2-2-2-6-3-8-4s-4-2-6-2c-1 0-1-1-2-1l-4-2c-5-2-8-5-11-7l-2-2v-1l-3-5z"></path></g><path d="M367 504c4 1 9 4 13 3-1 1-2 1-2 2l-1 4c-1-2-1-2-2-2-2-2-6-3-8-4-1-1-2-2-3-2l-2-1h5z" class="O"></path><path d="M339 487c1 1 6 5 7 5 7 5 14 9 21 12h-5l2 1c1 0 2 1 3 2-2-1-4-2-6-2-1 0-1-1-2-1l-4-2c-5-2-8-5-11-7l-2-2v-1l-3-5z" class="B"></path><defs><linearGradient id="AL" x1="344.973" y1="469.531" x2="361.527" y2="494.469" xlink:href="#B"><stop offset="0" stop-color="#817f81"></stop><stop offset="1" stop-color="#b4b4b2"></stop></linearGradient></defs><path fill="url(#AL)" d="M333 453l3 3 3 3h0l4 4 1-1c1 1 2 1 3 1 2 2 6 5 7 7 1 0 1 0 1 1 1 1 4 3 5 4v1l6 4c1 2 2 3 3 5v2l3 3v3h0-1c0 2 1 1 2 3v2c1 1 1 2 1 4h0l-16-6c-3-3-9-5-11-9 0-1-1-2-2-3h-3c-2-2-7-5-8-8 0-2 0-2-1-3l1-1c1 0 2 0 2 1h1c-1-1-2-2-2-3v-1c-1-1-2-2-3-2l-3-4-2-2c-2-2-1-1-1-3l3-3c2 0 3-1 4-2z"></path><path d="M360 480l9 7 3 3v3h0-1c0 2 1 1 2 3v2c1 1 1 2 1 4l-2-2c-2-1-5-3-6-5l7 4-4-4v-1s0-1-1-1h-1v-2c1 0 0 0 1-1h0l-1-1c-1-1-2-1-2-2-2-1-2-1-3-1l-6-5h1c1 0 1 0 2 1h1 0l-1-1 1-1z" class="Z"></path><path d="M335 469l5 5c6 6 13 10 20 15 3 2 5 4 9 6l4 4-7-4c-1-2-18-12-21-14-2-2-4-3-5-5l-1 1 9 9-1 1c0-1-1-2-2-3h-3c-2-2-7-5-8-8 0-2 0-2-1-3l1-1c1 0 2 0 2 1h1c-1-1-2-2-2-3v-1z" class="D"></path><path d="M333 466h1c1 0 2 0 3 1h1 1c1 0 1 0 1-1h1c1 0 2 1 3 2 3 1 5 3 7 5l7 6s1 1 2 1l-1 1 1 1h0-1c-1-1-1-1-2-1h-1l6 5c-4-1-9-5-12-8l-9-7c-1 1-1 2-1 3l-5-5c-1-1-2-2-3-2l1-1z" class="c"></path><path d="M332 467l1-1c2 2 5 3 8 5h0c-1 1-1 2-1 3l-5-5c-1-1-2-2-3-2z" class="i"></path><path d="M356 481c-1 0-2-1-3-2-4-3-6-6-9-9 2 1 4 3 7 3h0l7 6s1 1 2 1l-1 1 1 1h0-1c-1-1-1-1-2-1h-1z" class="H"></path><path d="M347 487l1-1-9-9 1-1c1 2 3 3 5 5 3 2 20 12 21 14s4 4 6 5l2 2h0l-16-6c-3-3-9-5-11-9z" class="k"></path><defs><linearGradient id="AM" x1="349.053" y1="460.018" x2="343.61" y2="473.424" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#36393b"></stop></linearGradient></defs><path fill="url(#AM)" d="M333 453l3 3 3 3h0l4 4 1-1c1 1 2 1 3 1 2 2 6 5 7 7 1 0 1 0 1 1 1 1 4 3 5 4v1l6 4c1 2 2 3 3 5v2l-9-7c-1 0-2-1-2-1l-7-6c-2-2-4-4-7-5-1-1-2-2-3-2h-1c0 1 0 1-1 1h-1-1c-1-1-2-1-3-1h-1l-1 1-3-4-2-2c-2-2-1-1-1-3l3-3c2 0 3-1 4-2z"></path><path d="M327 461v-1c1 0 2 1 3 2l-1 1-2-2z" class="K"></path><path d="M329 463l1-1c2 1 3 1 5 2v-2c3 1 4 2 6 3v1h-1c0 1 0 1-1 1h-1-1c-1-1-2-1-3-1h-1l-1 1-3-4z" class="f"></path><path d="M343 463l1-1c1 1 2 1 3 1 2 2 6 5 7 7 1 0 1 0 1 1 1 1 4 3 5 4v1c-4-1-6-4-9-7l-8-6z" class="L"></path><path d="M691 271l2 1v-1l1 1s1 1 2 1h2 1l2 1h1l-2 1 1 1h1 2v1l-2-1c-1 1-1 1-1 2 1 1 2 1 3 2-1 1-1 1-2 1l-2 1c0 2 0 3 1 4l-1 1v1 2c0 1 1 2 2 4l-1 1v2h-1l-1 1v1l2 2 4 1c2 0 4 0 6 1l6 11c0 1 2 3 2 4 2 3 4 7 6 10 1 1 2 2 4 3 1 0 1 1 2 1l-1 1c0 1-1 1-2 2 0 1 0 1 1 2 1 0 1 1 2 1 2 2 4 3 5 5l2 2c-2 0-3 0-4 1-2 1-2 0-3 1h-7v-1h-4v1s1 0 1 1h1l-5 1v1h-1-3c-1-1-4-1-6-1v1h-1c-1 0-1-1-2-1-2 0-3 2-4 4v1c-1 1-1 2 0 4h1l1-1c1-1 3-1 4-2 1 0 1 0 2-1 2-1 4 0 5 0h-1c0 2 0 3-1 4v3c0 1 0 1-1 2 0 1 1 3 0 4 0 1 0 1-1 3 0 1 0 1 1 2-1 1-2 1-2 2h-1v1c-1 2-2 2-3 3l-5 4-1 1v2l1 1c1-2 3-5 5-6l1-1c1-2 3-3 5-4 0-1 0-2 1-3v-1l2-1c1 1 2 0 3 1l-2 2h2 3l-2 1 1 1h1 1l1 1c-2 1-5 2-6 5h1s1 0 2 1h1l-3 1v1l5 1-1 8v2c-2 0-3 0-5 1h0l3 1c0 2-1 2 0 4 1 0 1 0 2 1h1l-15 1v-1c-3-1-7 0-10 0h-1c-1 0-2 0-3-1l-2-2c0-1-1-2-2-3h0 0c1 2 3 4 3 7-1 1-2 1-3 2l1 3 2 1h-1c-2 0-2 1-4 1l-9 2v2 1c2 1 3 2 3 4v3 2h1l1 2-3 1-1 1c-1 2-1 3 0 5v1 2h0l-1-1h-1c1 2 2 3 2 4v1c1 1 2 3 3 5 4 3 7 7 11 10l-2 1c3 2 6 5 10 6v2l1 1-3 8-2 5v2l-1 3-1 2c-10 28-26 53-47 75l-5 5-5 5c-2 2-4 3-5 4l-8 8c-1 1-1 2-2 3v1c-1 1-2 1-3 2h-1-3v3l-2 1h-1l-5 3 1 2c2 1 2 1 4 1h0 0c2 0 4 1 6 1h2 1l3 3 1 1h4 1l1 1-1 1h1l2 1c0 1 0 1-1 2s-1 2-2 3c-2 3-3 5-3 9l1 4h-1v1c-1-1-1-2-2-3 0-1-1-1-2-2l1 4-1 1c-1 0-1-1-1-1l-2 2-1 1c-1 2-3 5-5 5h-1l-5 2-3 2h0v1c0 1 0 1-1 1-2 1-3 3-4 4v1h0c-5 3-8 6-12 10-2 3-3 5-4 9h3l-1 4-1 3c0 1 0 2 1 3v2h0c1-2 1-4 2-5h1c2-7 6-14 12-19 1 0 2 0 3-1l-1 2v1c-6 8-12 15-10 26 1 5 3 9 6 12 3 1 8 4 10 3h1l-2-1 2-1h1c2 1 4 2 7 2h4v1c-4 3-9 3-13 4-2 0-4 0-5 1 0 0-1 1-1 2-1 2-1 6-2 9-1 5-3 9-3 14v1l-14 7c-1 5-1 11-4 15-1 3-3 5-5 7h-4c-3 5-5 13-10 15-1 1-2 2-3 2l-3 2-2 1c-1 1-3 2-4 2-1 1-2 2-4 2-1 0-3-1-4-2-2 0-4-1-6-2h-1v-2c-1 1-1 2-1 3h-1v1 1c-2-3-5-9-8-10l9 13c-2-2-4-3-6-5-2-3-3-7-6-8v1c3 2 4 5 5 7l6 6v1c-5-3-8-7-11-12v-1 2c2 2 3 4 4 6l5 5s2 1 2 2v1c-2-1-3-2-4-3-3-3-6-6-7-9-1 1 0 3 0 5v1c1 1 1 1 1 2 1 4 5 6 6 10h1 1v1l-1 1c1 2 3 4 3 6-2-2-3-4-5-5-3-2-4-7-7-9 0 3 7 10 9 13 2 2 3 4 4 5-2-1-4-3-5-4-2-2-3-4-5-6-1-1-1-2-2-4l-1-1c0 2 1 4 2 5l1 1c2 1 2 3 4 5 2 3 4 5 6 8-3-1-7-7-9-9-1-2-2-4-4-5 0 1 0 1 1 2 2 3 4 6 7 9l3 4h0c-2-1-3-3-5-5-3-1-4-5-6-7v1c1 3 4 7 6 10 1 2 3 4 4 6 0 1 0 1 1 2s1 2 1 3h-1 0c-1-1-1 0-2-1s-2-2-3-4c0-1-2-3-3-4-1-2-2-3-3-5h0c0 4 3 7 5 10s5 5 7 8h0c-5-4-9-9-13-14 2 4 5 9 8 12 2 2 4 3 5 5-1 0-3-3-5-3-2-2-5-6-7-8 0-2-1-2-1-3h-1v2c-2 0-2-5-4-5v1l1 2v1l1 1h-1l-3-3c-2 1-3 1-4 2 0-4 0-7-1-11h0c-1-2 0-6 0-9v-27-33-264c1-2 1-3 1-4-2-3-1-11-1-14 0-14 0-29 1-43 0-2-1-6 0-8 0-1 1-1 1-2v-4-13-64c1-2 0-3 0-5l1-3v-1s1-1 1-2v-1s0-1 1-2l2-3c1-2 0-3 1-5 0 0 1-1 2-1 0-2 4-6 6-7 1-1 2-1 2-2-1-1-4-4-4-5v-1l-2-3 1-1c0 1 2 2 3 2l-1-1h1l2 2c1 1 2 2 4 3 1 0 2 0 3-1 1 0 2 1 3 1-2-1-4-3-5-5v-1c1 1 2 1 2 2h1 1l-2-2h7c2 1 2 7 3 9l2-2-2-6h102 31c1-1 4-1 5-1h11v-2-1z"></path><path d="M653 408v1h0 1v-1l-1 7-2 2h-1l3-9z" class="I"></path><path d="M535 675h1c2 0 2 0 4 1l-1 2c-1 1-2 1-3 1l-1-2c-1-1-1-1 0-2z" class="R"></path><path d="M656 440l1 1v2l1 1c1 1 1 2 2 3l1 1c-2 0-4 1-5 1l-1-1c0-3 0-5 1-8z" class="T"></path><path d="M655 397h3 0v2c-2 3-3 6-4 9v1h-1 0v-1l2-11z" class="C"></path><path d="M658 435v1c0 1 0 1 1 2v3c1 0 0 1 0 2h1c2 1 3 2 4 3l-3 2-1-1c-1-1-1-2-2-3l-1-1v-2l-1-1c0-1 1-3 2-5z" class="W"></path><path d="M639 414l1-1v2l-1 17c-1 4-2 9-1 13h-2l3-31z" class="B"></path><path d="M554 624l8-6c-1 2-1 5-3 6 0 1-1 2-2 2-1 3-2 5-5 7l1-2-1 1c0 1 0 1-1 2h-1c-1 2 1 1-1 2h-1l2-2c1-2 1-3 3-4v-1c2 0 3-2 3-3-1 0-1-1-2-1v-1z" class="f"></path><path d="M651 360v-7c0-2 4-13 5-14l1 1c0 2 0 3 1 5 0 1 0 2-1 4-1 1-3 2-4 3s0 3-1 4v1l-1 3z" class="C"></path><path d="M625 413h1v1c1 3 1 7 1 11 1 11 2 21 2 32 0 5 0 10-1 14-1-3 0-6 0-9v-17c0-3-1-7-1-10l-2-22zm-84 253c1 0 0 2 0 3v1c0 1 0 3 1 4 1-3 2-6 4-9l5-7v1c-1 2-3 5-3 8l-2 2v1 1l-1 1c0 1-1 1-1 2s0 1-1 2v1c0 1-1 1-1 1-1-1-1-1 0-2v-1l-1 1c-2-2-1-4-1-6 1-1 0-2 0-3l1-1z" class="e"></path><defs><linearGradient id="AN" x1="539.171" y1="678.679" x2="550.981" y2="673.68" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#32363a"></stop></linearGradient></defs><path fill="url(#AN)" d="M547 670c1 0 1-1 1-1l3-3c-1 1-1 2-1 5 0 0 1 0 1 1h1l-1 1h-1l-1 2c-2 3-4 7-7 10-1-1-2-1-3-2 3-3 7-8 8-12v-1z"></path><path d="M661 428c1 2 0 5 2 7l-1 2 1 1c0 1 0 1-1 1-1 1-1 1-1 2h0 1c1 1 1 1 1 2 2 0 4 1 5 2l-4 1c-1-1-2-2-4-3h-1c0-1 1-2 0-2v-3c-1-1-1-1-1-2v-1c0-2 2-5 3-7z" class="S"></path><path d="M640 450s-1 1-1 2c-2 5-4 11-5 16 0 3-1 7-2 10 0-8 2-16 3-24 1-3 1-6 1-9h2c0 2-1 3 0 5v1l1-2 1 1z" class="e"></path><path d="M706 402c1 0 1 0 2-1v-2h0c-1-1-4-1-6-1v1h-1-2c0-1 0-1 1-2 0-1 1-1 2-1 1-1 1-1 2-1l1 2c2 1 3-1 4 2v1l1 1v-1c1-1 1-1 2-1 0 0 0-1 1-1l2-2 3 1c0 2-1 2 0 4 1 0 1 0 2 1h1l-15 1v-1z" class="R"></path><path d="M662 425c1 1 2 1 2 3v2l1 1c0 1 0 3-1 4v1h1l1 1-1 1s-1 1-1 2h1c1-1 1-1 2-1 1 1 4 3 4 5-1 0-2 1-3 1-1-1-3-2-5-2 0-1 0-1-1-2h-1 0c0-1 0-1 1-2 1 0 1 0 1-1l-1-1 1-2c-2-2-1-5-2-7 0-1 1-2 1-3z" class="f"></path><defs><linearGradient id="AO" x1="540.128" y1="628.314" x2="552.079" y2="633.177" xlink:href="#B"><stop offset="0" stop-color="#0a1014"></stop><stop offset="1" stop-color="#29292d"></stop></linearGradient></defs><path fill="url(#AO)" d="M553 630l-1-1c-4 1-9 5-13 7-2 2-3 2-5 3 3-5 8-8 13-11 2-1 5-3 7-4v1c1 0 1 1 2 1 0 1-1 3-3 3v1z"></path><path d="M585 600v3c-1 1-1 2-1 2v1c-1 1-3 2-3 3l-10 7c-1-1-1-2-1-3l15-13z" class="d"></path><path d="M650 417h1l2-2c-1 5-3 9-5 14l8-9c1-1 2-3 3-3h1v1l-9 10h-1c-4 6-8 15-10 22l-1-1 11-32zm-48 165c1-2 1-2 1-3h-1-2l-1-1h0c1 0 1-1 3 0 1-2 1-3 2-5h1c1 1 2 2 2 4 0 1 1 2 1 2 1 1 1 1 1 2 1 1 2 3 2 4l1 1c0 1 0 1-1 2l1 1h2l-1-1v-2l-1-1c0-2-2-4-3-6l2-1c2 1 3 4 5 6v-1l1 3 1 2h-3v3l-2 1h-1-1c1-1 1-1 2-1h0l-1-1c-1 1-1 1-2 0v-1l-1-1-2 1v-2l1-3-2 1v-1h0c-1-2-2-2-4-2z" class="R"></path><path d="M615 588c-1-1-2-4-3-5v-2l1 1c1 1 2 3 3 4h1l1 2h-3z" class="E"></path><path d="M608 584v-1c1 0 2 1 2 2v1l1 1s-1 1-1 2l-1-1-2 1v-2l1-3z" class="B"></path><path d="M639 414l-1-75c1 2 1 6 1 9l1 23v10h1c1-3 1-7 1-10v-4l1-1v7l-1 19c-1 7-1 16-2 23v-2l-1 1z" class="R"></path><path d="M657 453c2 2 2 5 3 8-1 1-1 1-1 2l1 1c0 1-1 2-1 2 0 2 1 1 0 3s-1 3-1 5h0l-1 1-7 8h-1c1-5 3-9 4-14s2-11 4-16z" class="V"></path><path d="M533 652l1 1v28l1 1c2 0 3 1 4 2v-1c1 1 2 1 3 2l1 1h1 1c1 1 4 0 5 1-1 1-1 1-2 1 0 1 0 1 1 2-2 0-3 0-4 1h-1l-1-1h1v-1h-2c-2 0-5 1-6 2l-1 1h-2l-1-4v-6l1-4v-10-16z" class="R"></path><path d="M543 686h1 1c1 1 4 0 5 1-1 1-1 1-2 1 0 1 0 1 1 2-2 0-3 0-4 1h-1l-1-1h1v-1h-2-4 0c-1-1-2-1-2-2l1-1h6z" class="C"></path><defs><linearGradient id="AP" x1="560.69" y1="631.922" x2="558.032" y2="619.81" xlink:href="#B"><stop offset="0" stop-color="#34373b"></stop><stop offset="1" stop-color="#5c5b5a"></stop></linearGradient></defs><path fill="url(#AP)" d="M565 617l5-4c0 1 0 2 1 3l-1 1h0 2v1s-1 1-2 1c-1 1-1 2-2 3-2 3-14 10-13 13-1 1-2 2-2 3l-1 1-1 1c-2 2-4 5-5 8-1 1-1 2-2 3v-1c2-3 3-6 4-9h1v-1c-1-1 0-1 0-2 1 0 1-1 1-1 1-1 1-2 2-3v-1c3-2 4-4 5-7 1 0 2-1 2-2 2-1 2-4 3-6l3-1z"></path><path d="M565 617l5-4c0 1 0 2 1 3l-1 1-3 2c-2 0-2-1-2-2z" class="i"></path><path d="M570 360c-1 0-2 1-3 2-2 2-1 28-1 33v20 26l7-5h0l-6 6v6 1l-1-6c-2 0-7 4-8 5 1-2 6-5 7-8v-4l-1-14 2-59-3 2h-1c2-2 5-4 5-7-1-3-3-5-5-7h0l3 2v-1c0-2 0-5 1-7 0 2-1 7 1 10 1 1 1 1 2 1 1-1 1-1 1-2h1 0v2 1l-2 2c1 1 0 1 1 1z" class="e"></path><path d="M541 666l2-4v-2c1-1 1-2 1-3l1-1v-1c0-1 0-1 1-2 0-1 1-2 1-4l1-1c0-1 1-2 1-2l1-1s0-1 1-1v-1l1-1c0-1 1-1 1-2v-1c3-2 6-6 9-9h0l-1 1v3c-1 3-4 4-3 7l-4 5c-1-1-2-2-3-2l-2 5c0 1 0 1 1 1h1c0-1 1-2 2-2v1c-1 2-2 4-2 6l7-9h0l-4 7c-1 2-1 4-3 6h0v-1l-5 7c-2 3-3 6-4 9-1-1-1-3-1-4v-1c0-1 1-3 0-3z" class="O"></path><defs><linearGradient id="AQ" x1="552.487" y1="643.486" x2="560.509" y2="635.515" xlink:href="#B"><stop offset="0" stop-color="#414245"></stop><stop offset="1" stop-color="#5a5a5c"></stop></linearGradient></defs><path fill="url(#AQ)" d="M561 631v3c-1 3-4 4-3 7l-4 5c-1-1-2-2-3-2l10-13z"></path><path d="M711 371l2-1c1 1 2 0 3 1l-2 2h2 3l-2 1 1 1h1 1l1 1c-2 1-5 2-6 5h1s1 0 2 1h1l-3 1v1l5 1-1 8v2c-2 0-3 0-5 1h0l-2 2c-1 0-1 1-1 1-1 0-1 0-2 1v1l-1-1v-1c-1-3-2-1-4-2l-1-2h1 4 1c2-2 6 0 9-1 1-2 0-4 0-5l1-1c0-1 0-1-1-2h-2-4c-6 0-4 6-7 9-1 0-1 0-2-1v-4l1-1v-2h1v-1c1-1 1-1 1-2l-1-1h1l1-1c2-2 2-2 4-3l-1-1h0v-1h-3l-3 2c1-2 3-3 5-4 0-1 0-2 1-3v-1z" class="I"></path><path d="M711 371l2-1c1 1 2 0 3 1l-2 2h2 3l-2 1 1 1h1-1c-2 1-3 2-6 2l2-2h0-1c-1 0-2 0-2 1l-3 1-3 2c1-2 3-3 5-4 0-1 0-2 1-3v-1z" class="D"></path><path d="M584 606v5l2 2v-1h4c1 1 2 1 3 2h1l1 1c-1 2-5 4-7 5h-2l-5 4h-1-5v-1l2-1-3-1-4 3c-3 2-5 4-8 6h0l4-5c-3 1-10 10-11 10-1-3 11-10 13-13 1-1 1-2 2-3 1 0 2-1 2-1v-1h-2 0l1-1 10-7c0-1 2-2 3-3z" class="E"></path><path d="M584 606v5l-3 3c-3 1-5 3-8 5-2 1-3 2-5 3 1-1 1-2 2-3 1 0 2-1 2-1v-1h-2 0l1-1 10-7c0-1 2-2 3-3z" class="g"></path><path d="M581 609c-1 3-2 5-5 6-1 1-3 2-3 3v1c-2 1-3 2-5 3 1-1 1-2 2-3 1 0 2-1 2-1v-1h-2 0l1-1 10-7z" class="S"></path><path d="M574 621l7-4c3-1 6-4 10-3 1 1 1 0 2 0h1l1 1c-1 2-5 4-7 5h-2l-5 4h-1-5v-1l2-1-3-1z" class="F"></path><path d="M577 622c1 0 2-1 3-2l2-2c3-1 5-3 8-2l-4 4-5 4h-1-5v-1l2-1z" class="J"></path><path d="M664 449c1 0 2-1 3-1l2 1v1h1 2 0v2h1c1 0 0 0 1 1h0c0 1 0 1 1 2l-1 1h1v1c-1 2-3 3-4 4 2 0 3-3 4-2 0 1-2 4-3 4-3 1-4 5-7 6s-4 4-5 6l-1 1-2-1 1-1h0c0-2 0-3 1-5s0-1 0-3c0 0 1-1 1-2l-1-1c0-1 0-1 1-2-1-3-1-6-3-8 1 0 1-1 2-2l5-2z" class="H"></path><path d="M667 462c1 1 1 0 3 0-1 2-4 5-5 7-3 1-4 4-5 6l-1 1-2-1 1-1c1-1 2-3 3-5v-3l1-1c0-1 0-2 2-3l1 1 1-1h1 0z" class="Y"></path><path d="M664 462l1 1 1-1h1 0c-2 2-4 5-6 7v-3l1-1c0-1 0-2 2-3z" class="g"></path><path d="M659 451l5-2 3 3h1v2l2 1c0 1-1 1-1 2h1c0 1-2 1-3 3l2-1c0 2-1 2-2 3h-1l-1 1-1-1c1 0 1-1 2-2-1 0-2 1-4 2v-1l2-2c-1-1-1 0-2 0 1-2 1-3 1-5-1-2-2-2-4-3z" class="j"></path><path d="M664 449c1 0 2-1 3-1l2 1v1h1 2 0v2h1c1 0 0 0 1 1h0c0 1 0 1 1 2l-1 1h1v1c-1 2-3 3-4 4 2 0 3-3 4-2 0 1-2 4-3 4-3 1-4 5-7 6 1-2 4-5 5-7-2 0-2 1-3 0h0c1-1 2-1 2-3l-2 1c1-2 3-2 3-3h-1c0-1 1-1 1-2l-2-1v-2h-1l-3-3z" class="D"></path><path d="M602 582c2 0 3 0 4 2h0v1l2-1-1 3v2l2-1 1 1v1c1 1 1 1 2 0l1 1h0c-1 0-1 0-2 1h1l-5 3-16 10 1 1-4 1 1 3v-1c-2 0-3-2-4-4h-1s0-1 1-2v-3c1-1 5-4 6-5 4-4 7-9 11-13z" class="K"></path><path d="M608 584l-1 3c-3 4-6 7-9 10h-1c-1 2-2 2-4 2 2-3 8-6 9-10 1-1 1-3 2-4l2-1v1l2-1z" class="U"></path><path d="M585 605c1-1 2-2 4-3 3-2 3-5 6-8h1 0c0-1-1 0 0-1 1-4 5-6 8-8-1 1-1 3-2 4-1 4-7 7-9 10l-1 1c-2 2-4 3-5 6v1l1-1c1-1 1 0 2 0l1-1 1 1-4 1 1 3v-1c-2 0-3-2-4-4z" class="k"></path><path d="M602 582c2 0 3 0 4 2h0l-2 1c-3 2-7 4-8 8-1 1 0 0 0 1h0-1c-3 3-3 6-6 8-2 1-3 2-4 3h-1s0-1 1-2v-3c1-1 5-4 6-5 4-4 7-9 11-13z" class="Y"></path><path d="M574 621l3 1-2 1v1h5 1c2 0 3 0 5-1 1-1 1-1 2-1-8 6-17 13-23 21-3 2-5 5-6 8-2 1-2 3-2 5l-1-2v-1l-1 1-1-1 4-7h0l-7 9c0-2 1-4 2-6v-1c-1 0-2 1-2 2h-1c-1 0-1 0-1-1l2-5c1 0 2 1 3 2l4-5c-1-3 2-4 3-7v-3l1-1c3-2 5-4 8-6l4-3z" class="c"></path><path d="M575 624h5l-5 3c-1-1 0-1 0-3z" class="M"></path><path d="M574 621l3 1-2 1v1c0 2-1 2 0 3l-4 3c-1 1-2 2-3 2 0 2-3 5-4 6-3 1-5 6-8 7 1-2 3-4 4-6-1 0-2 1-2 2-1-3 2-4 3-7v-3l1-1c3-2 5-4 8-6l4-3z" class="S"></path><path d="M574 621l3 1-2 1v1c0 2-1 2 0 3l-4 3c-1 1-2 2-3 2-2 2-4 4-6 5 1-2 3-4 5-5v-1l-3 2v-1c2-3 5-4 6-8h0l4-3z" class="T"></path><path d="M571 630h-1c1-1 1-1 1-2 1-2 2-3 4-5v1c0 2-1 2 0 3l-4 3z" class="a"></path><path d="M594 614h4 2c2-1 4 0 5 0 5 0 11 0 16 1l-1 1c0 1 0 3 1 4-2 0-4 1-5 1h-2-3v1c-2-1-2-1-4-1-5 2-10 4-16 7l-2-1c-3 1-6 5-9 4-3 2-5 3-7 5l-6 6s-1 1-2 1c6-8 15-15 23-21-1 0-1 0-2 1-2 1-3 1-5 1l5-4h2c2-1 6-3 7-5l-1-1z" class="N"></path><path d="M598 621c1 0 1 1 2 1 1-1 3-1 4-2l1 1h2c-5 2-10 4-16 7l-2-1c-3 1-6 5-9 4 2-1 4-2 6-4l12-6z" class="Q"></path><path d="M605 617c2 1 4 0 6-1s5-1 7 1h1c0-1 1-1 1-1 0 1 0 3 1 4-2 0-4 1-5 1h-2-3v1c-2-1-2-1-4-1h-2l-1-1c-1 1-3 1-4 2-1 0-1-1-2-1 1-1 2-1 3-2l4-2z" class="D"></path><path d="M606 619c2-1 5-1 8-1-2 1-4 2-6 2-1-1-2-1-2-1z" class="K"></path><path d="M605 617c2 1 4 0 6-1s5-1 7 1c-1 1-2 1-4 1-3 0-6 0-8 1l-3 1h0l-2-1 4-2z" class="O"></path><path d="M594 614h4 2c2-1 4 0 5 0 5 0 11 0 16 1l-1 1s-1 0-1 1h-1c-2-2-5-2-7-1s-4 2-6 1c-1 0-3-1-4-1-2 0-5 2-7 3l-6 3c-1 0-1 0-2 1-2 1-3 1-5 1l5-4h2c2-1 6-3 7-5l-1-1z" class="L"></path><path d="M580 631c3 1 6-3 9-4l2 1c-8 5-16 12-22 20 0 2 0 3-1 5l-1 1 1 1-6 12c-1 2-2 6-3 9-1-1-2-2-2-3l-3 6c0-1-1-2-1-3v-3h0l-2 1v-1l1-1h-1c0-1-1-1-1-1 0-3 0-4 1-5l-3 3s0 1-1 1c0-1 1-2 1-3 0-3 2-6 3-8h0c2-2 2-4 3-6l1 1 1-1v1l1 2c0-2 0-4 2-5 1-3 3-6 6-8 1 0 2-1 2-1l6-6c2-2 4-3 7-5z" class="U"></path><path d="M557 673c1-4 2-7 4-10 2-5 5-10 8-15 0 2 0 3-1 5l-1 1 1 1-6 12c-1 2-2 6-3 9-1-1-2-2-2-3z" class="h"></path><path d="M556 656l-2 5 1 1c2-3 3-7 5-10v1c-1 3-3 6-4 9-1 1-2 3-3 5 0 2-1 4-1 5h-1c0-1-1-1-1-1 0-3 0-4 1-5l-3 3s0 1-1 1c0-1 1-2 1-3 0-3 2-6 3-8h0c2-2 2-4 3-6l1 1 1-1v1l1 2h-1z" class="Q"></path><path d="M554 653l1 1 1-1v1l1 2h-1c-3 3-3 6-5 10l-3 3s0 1-1 1c0-1 1-2 1-3 0-3 2-6 3-8h0c2-2 2-4 3-6z" class="E"></path><path d="M611 408c1-2 1-4 2-6 1 2 2 4 2 6 1 5 1 10 1 15l-1 32v51 26c-1 2 1 10 0 13-1-1-1-4-1-5v-14l-1 20v1c-1-1-1-3-1-4v-12-1l-1-112v-2c1-2 1-6 0-8z" class="o"></path><path d="M607 595l1 2c2 1 2 1 4 1h0 0c2 0 4 1 6 1h2 1l3 3 1 1h4 1l1 1-1 1h1l2 1c0 1 0 1-1 2s-1 2-2 3c-2 3-3 5-3 9l1 4h-1v1c-1-1-1-2-2-3 0-1-1-1-2-2l1 4-1 1c-1 0-1-1-1-1l-1-4c-1-1-1-3-1-4l1-1c-5-1-11-1-16-1-1 0-3-1-5 0h-2-4-1c-1-1-2-1-3-2h-4v1l-2-2v-5-1h1c1 2 2 4 4 4v1l-1-3 4-1-1-1 16-10z" class="C"></path><path d="M593 610v-1c2-2 6-1 8-1 5-1 11 1 16 2v1c-8-1-16-2-24-1z" class="T"></path><path d="M612 598c2 0 4 1 6 1h2 1l3 3-3 2c-1 0-1 0-2 1s-2 0-3 0l-2-2 1-1h2l1 1-1-2c-1 0-2-1-4-1h0-3l2-2h0z" class="D"></path><path d="M607 595l1 2c2 1 2 1 4 1h0l-2 2c-1 2 0 3-1 5-2 0-4 0-6-2h0c-3 2-8 3-11 3l-1-1 16-10z" class="M"></path><path d="M584 605h1c1 2 2 4 4 4v1c1-1 1 0 2 0h2c8-1 16 0 24 1 1 1 3 1 4 2v2c-5-1-11-1-16-1-1 0-3-1-5 0h-2-4-1c-1-1-2-1-3-2h-4v1l-2-2v-5-1z" class="X"></path><path d="M624 602l1 1h4 1l1 1-1 1h1l2 1c0 1 0 1-1 2s-1 2-2 3c-2 3-3 5-3 9l1 4h-1v1c-1-1-1-2-2-3 0-1-1-1-2-2l1 4-1 1c-1 0-1-1-1-1l-1-4c-1-1-1-3-1-4l1-1v-2c-1-1-3-1-4-2v-1c-1-1 0-1-1-2 1-1 3-1 4-1h1c1-1 0-1 0-3l3-2z" class="G"></path><path d="M624 602l1 1 2 3c0 3-3 6-4 8 0 2-1 4 0 6h0l1 4-1 1c-1 0-1-1-1-1l-1-4c-1-1-1-3-1-4l1-1v-2c-1-1-3-1-4-2v-1c-1-1 0-1-1-2 1-1 3-1 4-1h1c1-1 0-1 0-3l3-2z" class="R"></path><path d="M617 610c2 0 4 0 5 2l1 2c0 2-1 4 0 6h0l1 4-1 1c-1 0-1-1-1-1l-1-4c-1-1-1-3-1-4l1-1v-2c-1-1-3-1-4-2v-1z" class="W"></path><path d="M705 302c2 0 4 0 6 1l6 11c0 1 2 3 2 4 2 3 4 7 6 10 1 1 2 2 4 3 1 0 1 1 2 1l-1 1c0 1-1 1-2 2 0 1 0 1 1 2 1 0 1 1 2 1 2 2 4 3 5 5l2 2c-2 0-3 0-4 1-2 1-2 0-3 1h-7v-1h-4v1s1 0 1 1h1l-5 1v1h-1-3c-1-1-4-1-6-1v1h-1c-1 0-1-1-2-1l3-1h3c1 0 2 0 2-1l-3-4-1-1h-1c0-1 0-2-1-3v-1c-2 0-3-2-4-3l-1-1c-1-1-1-2-2-3 0-1 1-1 0-2h0c1-1 1-2 2-3 0-1 0-1 1-1l2-1 2-1c1 0 1-1 1-1v-2c-1-1-1-1-1-2l-3 2-1 1h-1l-1-1c-1-4 3-5 5-8 0-1 1-1 1-1h1c1 0 1 1 1 2v1 3h0c1 1 1 2 2 2h1c1-1 1-1 2-1h1v-1c1-1 1-1 1-2l-1-1c0-1-1-1 0-2h0v-2h0l-1-1h0v-1c-1-1-1-1-1-2l-1-1c-1-2-2-2-3-2h-1-2 0v-1z" class="B"></path><path d="M736 343l-1 1c0-1-1-2-2-2s-1 0-2-1v-3c2 2 4 3 5 5z" class="C"></path><path d="M724 346l1-1v1-1-1c1-1 2-1 3 0h1 1c1 1 2 1 4 2-2 1-2 0-3 1h-7v-1z" class="c"></path><path d="M729 331c1 0 1 1 2 1l-1 1c0 1-1 1-2 2 0 1 0 1 1 2v1c0 1 0 1-1 1v-1-1h-1c-2 2-3 4-6 6-1 1-1 2-2 2l-1-1c2-2 4-4 5-6 1-1 2-1 3-2l1-1-1-1v-1c1 0 2-1 2-1l1-1z" class="D"></path><path d="M705 302c2 0 4 0 6 1l6 11c0 1 2 3 2 4 0 3 2 5 1 8v2c-1-2-2-3-3-4l-1 1c0 1 1 2 2 2v1l-2-1c-1-2-1-3-1-5l1-1v-3c-1 0-5 2-7 2-1-1-1-2-2-3h-1-1l2-2v-3l-1-1h1c1 0 1 1 1 2v1 3h0c1 1 1 2 2 2h1c1-1 1-1 2-1h1v-1c1-1 1-1 1-2l-1-1c0-1-1-1 0-2h0v-2h0l-1-1h0v-1c-1-1-1-1-1-2l-1-1c-1-2-2-2-3-2h-1-2 0v-1z" class="R"></path><path d="M712 347l-3-4-1-1h-1c0-1 0-2-1-3v-1c-2 0-3-2-4-3l-1-1c-1-1-1-2-2-3 0-1 1-1 0-2h0c1-1 1-2 2-3 0-1 0-1 1-1l2-1 2-1c1 0 1-1 1-1v-2c-1-1-1-1-1-2l-3 2-1 1h-1l-1-1c-1-4 3-5 5-8 0-1 1-1 1-1l1 1v3l-2 2h1l2 5c-2 3-9 3-8 8 1 3 3 5 5 7 1 0 2-1 3-1h0v-1c1-1 1-2 1-2v-2-1c-1 2-2 3-2 5-2-2-4-3-6-6l9-6c3 1 3 2 5 5l1 1v1l2 2c-1 1-4 4-4 5v2l-3 3c-1 0-1 0-2-1h-1l5 6h-1z" class="X"></path><path d="M707 312v3l-2 2c-1 1-2 2-4 3v-1c0-3 4-5 6-7z" class="C"></path><path d="M538 516h0-17v-4c13 0 26 0 39 2h3 1l5 2c2 1 3 1 5 2l3 2h1c3 1 5 3 8 4l3 3c1 1-1 0 1 1 2 0 5 4 6 6s3 2 4 5l3 2c1 2 2 2 3 4v1c1 1 1 1 1 2v1h0c1 1 1 1 1 2l1 2h0c0 3 2 6 3 9v-1l-1-4c1-1 1-2 1-3v-24 1 12c0 1 0 3 1 4v-1l1-20v14c0 1 0 4 1 5 1-3-1-11 0-13v47l-12-20c-5-9-10-18-17-25-13-14-29-17-48-18z" class="l"></path><defs><linearGradient id="AR" x1="687.293" y1="445.541" x2="660.184" y2="463.456" xlink:href="#B"><stop offset="0" stop-color="#0c0d0d"></stop><stop offset="1" stop-color="#3b3d41"></stop></linearGradient></defs><path fill="url(#AR)" d="M663 422c3 6 6 13 11 18h0l2 1s1-1 1-2l1 1c1 1 2 3 3 5 4 3 7 7 11 10l-2 1c3 2 6 5 10 6v2h-2c-3 3-4 7-7 10l-1 1c-1 1-2 1-2 2-1 1-2 2-3 2-2 1-4 2-5 2v-1c1-1 1-1 1-3-1 0-1 0-1-1-2 2-6 5-8 5-3 2-6 4-9 5v-1c1-1 2-2 2-4-4 3-7 7-12 10h-1v1h-1c-1 0-2 1-3 2l-2 1h-1c0-2 2-4 3-6l1-2v-4h1l7-8 2 1 1-1c1-2 2-5 5-6s4-5 7-6c1 0 3-3 3-4-1-1-2 2-4 2 1-1 3-2 4-4v-1h-1l1-1c-1-1-1-1-1-2h0c-1-1 0-1-1-1h-1v-2h0-2-1v-1l-2-1c2-2 4-2 5-3l-1-1c0-2-3-4-4-5-1 0-1 0-2 1h-1c0-1 1-2 1-2l1-1-1-1h-1v-1c1-1 1-3 1-4l-1-1v-2c0-2-1-2-2-3 0-1 1-2 1-3z"></path><path d="M677 439l1 1c1 1 2 3 3 5l-2 2-5-7h0l2 1s1-1 1-2z" class="T"></path><path d="M680 476l5-5h1c0 1-1 2-1 2v2l-4 4h1 2 1c-2 1-4 2-5 2v-1c1-1 1-1 1-3-1 0-1 0-1-1z" class="D"></path><path d="M681 445c4 3 7 7 11 10l-2 1-11-9 2-2z" class="W"></path><defs><linearGradient id="AS" x1="682.622" y1="459.08" x2="680.378" y2="473.42" xlink:href="#B"><stop offset="0" stop-color="#212423"></stop><stop offset="1" stop-color="#494b51"></stop></linearGradient></defs><path fill="url(#AS)" d="M680 467c2-3 6-8 10-8 1 0 1 1 2 1 0 2-7 10-9 11h-1l-5 5-5 5c-3 2-6 4-9 5v-1c1-1 2-2 2-4-4 3-7 7-12 10h-1v1h-1c-1 0-2 1-3 2l-2 1h-1c0-2 2-4 3-6l1-2v-4h1l7-8 2 1 1-1c1-2 2-5 5-6s4-5 7-6c-1 2-3 3-4 5l-2 4h0l1-1h1l2-2h0c1 1 1 1 2 1l3-3-1-1 2-1v1l-1 1 1 1c1-1 2-1 4-1z"></path><path d="M672 470l3-3-1-1 2-1v1l-1 1 1 1c1-1 2-1 4-1-1 1-2 2-4 3v-2c-4 2-7 4-10 7l-1 1h0c1-3 5-5 7-6z" class="E"></path><path d="M673 475c3-2 5-4 9-4l-5 5-4-1z" class="k"></path><path d="M666 475c3 1 3 0 5-1h1c-3 3-7 6-10 8l-3-1 6-5 1-1z" class="H"></path><path d="M673 475l4 1-5 5c-3 2-6 4-9 5v-1c1-1 2-2 2-4 1-2 7-5 8-6z" class="W"></path><defs><linearGradient id="AT" x1="656.901" y1="480.358" x2="649.076" y2="495.129" xlink:href="#B"><stop offset="0" stop-color="#9c9b9a"></stop><stop offset="1" stop-color="#b7b6b8"></stop></linearGradient></defs><path fill="url(#AT)" d="M659 481l3 1-7 7c-1 0-3 2-4 3-1 0-2 1-3 2l-2 1h-1c0-2 2-4 3-6 4-3 8-6 11-8z"></path><path d="M665 469c3-1 4-5 7-6-1 2-3 3-4 5l-2 4h0l1-1h1l2-2h0c1 1 1 1 2 1-2 1-6 3-7 6h0l-6 5c-3 2-7 5-11 8l1-2v-4h1l7-8 2 1 1-1c1-2 2-5 5-6z" class="D"></path><path d="M665 469c3-1 4-5 7-6-1 2-3 3-4 5-2 2-5 6-8 7 1-2 2-5 5-6zm-8 6l2 1c-1 2-3 3-4 5s-4 4-6 6h0v-4h1l7-8z" class="U"></path><path d="M690 475l1-1v3 1c0 1 0-1-1 1v3 1c-1 2-1 7-3 8-3 1-4 4-6 6-1 3-2 5-3 7h-1l2-4-4 5h-1l1-1v-1c1 0 1 0 2-1l1-3h0l-5 5h0c-2 1-3 2-4 4h-1c-6 6-12 12-19 18-4 3-7 5-12 5l3-5c1-4 2-9 3-13l1-2v-3l-1-1-2 1h-1 0c1-5 3-9 5-13h1l2-1c1-1 2-2 3-2h1v-1h1c5-3 8-7 12-10 0 2-1 3-2 4v1c3-1 6-3 9-5 2 0 6-3 8-5 0 1 0 1 1 1 0 2 0 2-1 3v1c1 0 3-1 5-2 1 0 2-1 3-2 0-1 1-1 2-2z" class="I"></path><path d="M677 493l1-1h1c1 0 1-1 2-2h1c-1 2-3 6-5 7h-1c0 1-1 2-2 2 1-2 2-3 3-5v-1z" class="f"></path><path d="M665 495c3 0 4-2 7-2-4 3-9 6-13 8l-1-1c-1-1-3 0-5 0l11-5h1z" class="a"></path><path d="M685 479c1 0 2-1 3-2 0-1 1-1 2-2-2 3-4 5-7 8 0 1-2 2-3 3h0-4l-2 1-3 2v-1l7-7h2c1 0 3-1 5-2z" class="Q"></path><path d="M676 486l3-3h2c2-1 1-1 2 0 0 1-2 2-3 3h0-4z" class="S"></path><path d="M653 500c2 0 4-1 5 0l1 1c-5 2-9 4-13 5l-3 1-2 1v-1c1-2 4-3 6-4l6-3z" class="Z"></path><path d="M674 487l2-1h4 0l-1 1c-2 2-5 5-7 6-3 0-4 2-7 2 1-3 4-4 6-6l3-2z" class="P"></path><path d="M674 487l2-1h4 0l-1 1c-1 0-2 1-3 1s-1-1-2-1z" class="k"></path><path d="M677 493v1c-1 2-2 3-3 5 1 0 2-1 2-2h1l-4 7c-2 1-3 2-4 4h-1l-1-1-3 3s0-1 1-2l4-5-2 1-2-1c-1 0-1 1-2 1h-4l6-3c2-2 3-2 5-3s5-3 7-5z" class="b"></path><path d="M665 501h3c0-1 1-2 2-2v1 1l2-1h0c-1 1-2 3-3 3l-2 1-2-1c-1 0-1 1-2 1h-4l6-3z" class="k"></path><path d="M680 476c0 1 0 1 1 1 0 2 0 2-1 3v1h-2l-7 7v1c-2 2-5 3-6 6h-1l-11 5-6 3c-2 1-5 2-6 4v1h-1 0c1-5 3-9 5-13h1l2-1c1-1 2-2 3-2h1v-1h1c5-3 8-7 12-10 0 2-1 3-2 4v1c3-1 6-3 9-5 2 0 6-3 8-5z" class="U"></path><path d="M652 496h0c3-5 11-8 16-11-3 4-10 7-14 11h1l1 1c1-1 4-4 5-4-1 1-1 1-1 2h2l1-1h1v1l-11 5-6 3v-1c2-2 4-3 5-6z" class="P"></path><defs><linearGradient id="AU" x1="674.771" y1="488.272" x2="661.708" y2="487.705" xlink:href="#B"><stop offset="0" stop-color="#5e5d61"></stop><stop offset="1" stop-color="#7a7b79"></stop></linearGradient></defs><path fill="url(#AU)" d="M655 496l11-8c4-2 8-6 12-7l-7 7v1c-2 2-5 3-6 6h-1v-1h-1l-1 1h-2c0-1 0-1 1-2-1 0-4 3-5 4l-1-1z"></path><path d="M665 481c0 2-1 3-2 4v1c-5 4-10 7-15 11-2 2-4 3-5 5l9-6c-1 3-3 4-5 6v1c-2 1-5 2-6 4v1h-1 0c1-5 3-9 5-13h1l2-1c1-1 2-2 3-2h1v-1h1c5-3 8-7 12-10z" class="h"></path><path d="M659 504h4c1 0 1-1 2-1l2 1 2-1-4 5c-1 1-1 2-1 2l3-3 1 1c-6 6-12 12-19 18-4 3-7 5-12 5l3-5c1-4 2-9 3-13l1-2 5-2 6-2c1-1 3-2 4-3z" class="W"></path><path d="M659 504h4c1 0 1-1 2-1l2 1 2-1-4 5c-1 1-1 2-1 2v1c-2 2-5 3-7 5 1-4 6-6 7-11h0c-1 0-2 0-3 1h0c-2 1-4 1-6 1h0c1-1 3-2 4-3z" class="g"></path><path d="M649 509l1 1h1l1-1h3 1v1c-1 1-3 2-3 4l5-5v1c-1 2-3 3-5 5-2 3-5 5-7 7h-1c2 1 4-2 6-2-2 1-4 3-6 4-1 1-2 3-4 3-1-1-1 0-1-1 1-4 2-9 3-13l1-2 5-2z" class="V"></path><path d="M649 509l1 1h1l1-1h3 1v1c-1 1-3 2-3 4h0c0 1-1 1-2 2h0l1-2h-1l-1-1v-1h2v-1h-1-1-1c-1 0-2 0-3 1-1 0-2 1-3 1h0l1-2 5-2z" class="T"></path><defs><linearGradient id="AV" x1="610.685" y1="615.387" x2="567.328" y2="679.61" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#333537"></stop></linearGradient></defs><path fill="url(#AV)" d="M614 621h2c1 0 3-1 5-1l1 4-2 2-1 1c-1 2-3 5-5 5h-1l-5 2-3 2h0v1c0 1 0 1-1 1-2 1-3 3-4 4v1h0c-5 3-8 6-12 10-2 3-3 5-4 9h3l-1 4h-4v1l-2-1c0 1-1 5-2 6s-1 3-2 4l-2 5c-1 1-2 2-3 4l-2-3-2 3-1 1-1 1c-1-1-1-2-2-3s-2-2-2-3l1-3c-1-1-2-2-3-2h0c1-3 2-7 3-9l6-12-1-1 1-1c1-2 1-3 1-5 6-8 14-15 22-20 6-3 11-5 16-7 2 0 2 0 4 1v-1h3z"></path><path d="M585 646l-3 5c-2 1-4 3-5 5l-1 2-1-1 1-1-1-1c1-3 7-7 10-9z" class="U"></path><path d="M596 639l1 1-2 1c-2 1-4 4-6 5-2 2-5 4-6 7-1 1-2 2-2 4h-1v-2h0v-1c1-1 1-2 2-3l3-5 3-3c1 1 0 1 1 1 3-1 5-3 7-5z" class="S"></path><path d="M596 639h0c3-2 6-3 9-4l-3 2c-2 1-2 1-3 2 1 0 2 0 3-1l3-2v1c0 1 0 1-1 1-2 1-3 3-4 4v1h0c-5 3-8 6-12 10v-1l-2 1c1-2 2-5 3-7 2-1 4-4 6-5l2-1-1-1z" class="c"></path><path d="M607 621c2 0 2 0 4 1v-1h3c-9 5-18 8-26 14-8 5-15 12-20 20l-1-1 1-1c1-2 1-3 1-5 6-8 14-15 22-20 6-3 11-5 16-7z" class="J"></path><path d="M589 646c-1 2-2 5-3 7l2-1v1c-2 3-3 5-4 9h3l-1 4h-4v1l-2-1c0 1-1 5-2 6s-1 3-2 4l-2 5c-1 1-2 2-3 4l-2-3-2 3-1 1-1 1c-1-1-1-2-2-3s-2-2-2-3l1-3v-1c3-9 7-18 13-25h1c-3 4-5 7-7 12 1-2 2-3 3-4 1-2 2-3 3-5l1 1-1 1 1 1 1-2c1-2 3-4 5-5-1 1-1 2-2 3v1h0v2h1c0-2 1-3 2-4 1-3 4-5 6-7z" class="i"></path><path d="M563 684c3-2 2-9 5-12l1 1c-1 1-3 5-3 7h1v1l1 1c-1 1-1 1-1 3l-1 1-1 1c-1-1-1-2-2-3z" class="j"></path><path d="M570 676h-1 0-1l6-12c1-3 1-4 4-5l-1 1c-1 3-3 5-2 8-2 2-3 6-5 8z" class="b"></path><path d="M589 646c-1 2-2 5-3 7l2-1v1c-2 3-3 5-4 9h3l-1 4h-4v1l-2-1c0 1-1 5-2 6s-1 3-2 4l-2 5c-1 1-2 2-3 4l-2-3-2 3c0-2 0-2 1-3l1-3h0-1v-1l2-2c2-2 3-6 5-8-1-3 1-5 2-8l1-1 2-4h0v2h1c0-2 1-3 2-4 1-3 4-5 6-7z" class="K"></path><path d="M580 655h0v2h1c-1 4-4 8-6 11-1-3 1-5 2-8l1-1 2-4z" class="L"></path><path d="M580 663h1l-1 3c0 1-1 5-2 6s-1 3-2 4l-2 5c-1 1-2 2-3 4l-2-3c5-6 9-12 11-19z" class="F"></path><path d="M589 646c-1 2-2 5-3 7l2-1v1c-2 3-3 5-4 9h3l-1 4h-4v1l-2-1 1-3h-1l3-8v-2c1-3 4-5 6-7z" class="f"></path><path d="M582 666l2-4h3l-1 4h-4z" class="F"></path><path d="M589 646c-1 2-2 5-3 7-3 3-3 7-5 10h-1c1-3 2-6 3-8v-2c1-3 4-5 6-7z" class="U"></path><defs><linearGradient id="AW" x1="668.877" y1="501.893" x2="683.194" y2="512.661" xlink:href="#B"><stop offset="0" stop-color="#020404"></stop><stop offset="1" stop-color="#313030"></stop></linearGradient></defs><path fill="url(#AW)" d="M691 474c3-3 4-7 7-10h2l1 1-3 8-2 5v2l-1 3-1 2c-10 28-26 53-47 75l-5 5-5 5c-2 2-4 3-5 4 0-1 0-1 1-2l5-5 14-13c1-2 1-2 2-2l-1-1h-1c-2 1-3 3-5 4 0 1-1 2-2 2-2 4-5 6-8 9l-1 1-1 1-2 2c-1 1-2 4-4 5h-1v1h-2-1c-1 1-1 1-2 0h0c1-1 1-3 1-4 1-5 0-11 3-15 3-6 4-13 8-18v-1l1-5 1-2c5 0 8-2 12-5 7-6 13-12 19-18h1c1-2 2-3 4-4h0l5-5h0l-1 3c-1 1-1 1-2 1v1l-1 1h1l4-5-2 4h1c1-2 2-4 3-7 2-2 3-5 6-6 2-1 2-6 3-8v-1-3c1-2 1 0 1-1v-1-3z"></path><path d="M681 497c2-2 3-5 6-6l-3 9-3-3z" class="D"></path><path d="M673 516c1-1 1-3 2-3l1 1c-4 8-9 16-14 23l-1-1c1-1 1-2 1-3 1-1 2-2 2-3 2-5 6-10 9-14z" class="i"></path><defs><linearGradient id="AX" x1="684.209" y1="502.734" x2="673.16" y2="508.571" xlink:href="#B"><stop offset="0" stop-color="#424243"></stop><stop offset="1" stop-color="#686769"></stop></linearGradient></defs><path fill="url(#AX)" d="M681 497l3 3-8 14-1-1c-1 0-1 2-2 3-1 0-2-1-2-1 0-2 2-4 1-6 0-1 3-3 3-4l4-5-2 4h1c1-2 2-4 3-7z"></path><path d="M671 515s1 1 2 1c-3 4-7 9-9 14 0 1-1 2-2 3 0 1 0 2-1 3l1 1c-7 9-15 18-24 27-2 1-4 3-5 5v1c-1 1-2 4-4 5h-1v1h-2-1c-1 1-1 1-2 0h0c1-1 1-3 1-4 1-5 0-11 3-15h1 1c1-1 3-1 4-3 1 0 1-1 2-1s1 0 2-1c2-1 3-2 5-3 2-2 5-5 8-7 0 0 1 0 1-1 3-1 4-3 4-6l4-4c0-1 8-8 8-11 1-2 2-3 4-5z" class="k"></path><path d="M631 561c1 0 2 0 4-1l2-1h0c-1 2-3 3-4 3v1 1h-4v-2l2-1z" class="c"></path><path d="M671 515s1 1 2 1c-3 4-7 9-9 14 0 1-1 2-2 3l-4 4 4-6h-1c-1 1-2 2-3 4 0 1-1 1-2 2 0-1 1-2 1-3l2-3c0-1 8-8 8-11 1-2 2-3 4-5z" class="P"></path><defs><linearGradient id="AY" x1="625.158" y1="575.385" x2="632.943" y2="562.089" xlink:href="#B"><stop offset="0" stop-color="#0e1012"></stop><stop offset="1" stop-color="#3a3c3f"></stop></linearGradient></defs><path fill="url(#AY)" d="M633 563h0l6-3c1-2 2-4 4-5v1l-6 6c0 1 0 1 1 2-2 1-4 3-5 5v1c-1 1-2 4-4 5h-1v1h-2-1c-1 1-1 1-2 0h0c1-1 1-3 1-4 1-2 4-4 4-6l-1-1c0-2 0-2 2-3v2h4v-1z"></path><defs><linearGradient id="AZ" x1="625.987" y1="554.517" x2="644.513" y2="554.483" xlink:href="#B"><stop offset="0" stop-color="#171615"></stop><stop offset="1" stop-color="#3e4046"></stop></linearGradient></defs><path fill="url(#AZ)" d="M655 535l4-4-2 3c0 1-1 2-1 3l-1 2 1 1c0-2 1-3 2-4l1 1-1 1h0c-1 3-7 9-10 10-4 3-8 7-12 10-2 1-4 2-5 3l-2 1c-2 1-2 1-2 3l1 1c0 2-3 4-4 6 1-5 0-11 3-15h1 1c1-1 3-1 4-3 1 0 1-1 2-1s1 0 2-1c2-1 3-2 5-3 2-2 5-5 8-7 0 0 1 0 1-1 3-1 4-3 4-6z"></path><path d="M673 504h0l5-5h0l-1 3c-1 1-1 1-2 1v1l-1 1h1c0 1-3 3-3 4 1 2-1 4-1 6-2 2-3 3-4 5 0 3-8 10-8 11l-4 4c0 3-1 5-4 6 0 1-1 1-1 1-3 2-6 5-8 7-2 1-3 2-5 3-1 1-1 1-2 1s-1 1-2 1c-1 2-3 2-4 3h-1-1c3-6 4-13 8-18v-1l1-5 1-2c5 0 8-2 12-5 7-6 13-12 19-18h1c1-2 2-3 4-4z" class="i"></path><path d="M672 509c1 2-1 4-1 6-2 2-3 3-4 5 0 3-8 10-8 11l-4 4v-1c1-1 2-3 2-5-3 4-7 8-11 12 0-2 5-6 6-8v-1-1l4-2c2-1 4-4 6-6 2-1 4-3 4-4s0-1 1-1v-4c1 0 5-5 5-5z" class="H"></path><defs><linearGradient id="Aa" x1="638.246" y1="539.657" x2="641.6" y2="534.423" xlink:href="#B"><stop offset="0" stop-color="#767577"></stop><stop offset="1" stop-color="#8c8c8d"></stop></linearGradient></defs><path fill="url(#Aa)" d="M646 533c1-1 4-2 6-2v1c-3 3-6 6-8 9l-3 3h0c-1 0 0-1 0-1l-1-1-1 1 1 1-1 1c-1 1-2 2-3 2-1 2-3 4-3 6v1c-1 2-3 2-4 3h-1-1c3-6 4-13 8-18v-1l1-5h4l1 1 4-2 1 1z"></path><path d="M636 533h4l1 1c-2 1-4 3-6 4l1-5z" class="X"></path><path d="M646 533c1-1 4-2 6-2v1c-3 3-6 6-8 9l-3 3h0c-1 0 0-1 0-1 1-1 2-2 2-3l5-5-1-1-6 5 5-6z" class="h"></path><path d="M627 557c3-6 4-13 8-18l-1 3 1 1-1 1v1l6-5c-1 3-7 6-7 9 2-1 3-3 6-4-1 1-2 2-3 2-1 2-3 4-3 6v1c-1 2-3 2-4 3h-1-1z" class="f"></path><path d="M673 504h0l5-5h0l-1 3c-1 1-1 1-2 1v1l-1 1h1c0 1-3 3-3 4 0 0-4 5-5 5v4c-1 0-1 0-1 1s-2 3-4 4c-2 2-4 5-6 6l-4 2c-2 0-5 1-6 2l-1-1-4 2-1-1h-4l1-2c5 0 8-2 12-5 7-6 13-12 19-18h1c1-2 2-3 4-4z" class="K"></path><path d="M667 514v4c-1 0-1 0-1 1s-2 3-4 4c-2 2-4 5-6 6l-4 2c-2 0-5 1-6 2l-1-1c9-4 15-11 22-18z" class="M"></path><path d="M658 345h1c0-3 0-6-1-7v-1c1 1 2 2 2 3v5c1 1 0 1 2 1v-1c1 0 1 0 2 1 1 0 2 1 2 2v1 2 1c0 1 1 2 1 3l2 2 1 1h0l-1-1h1v-3l1-1v4h1c1 1 1 1 1 3h1v1 9c0-1 1-2 2-2v-1c-1-1-1-2-1-3h1l1 2v1c1 1 2 2 2 4l1 1v3h0c1 1 2 3 1 4l-1 1h-1 0l2-2-1-1c-1 1-1 1-1 2-1 0-1 1-1 1 1 1 1 1 2 3-1 0-1 0-1 1l3-3 1 1 1 1c0 1 0 1 1 2 0 1-1 2 0 3v2c1 0 1 0 2 1 1 0 2 1 2 2v1 1c-1 1 0 1-1 0h-1l1 1h0c1 2 3 4 3 7-1 1-2 1-3 2l1 3 2 1h-1c-2 0-2 1-4 1l-9 2v2 1c2 1 3 2 3 4v3 2h1l1 2-3 1-1 1c-1 2-1 3 0 5v1 2h0l-1-1h-1c1 2 2 3 2 4v1l-1-1c0 1-1 2-1 2l-2-1h0c-5-5-8-12-11-18-2-4-3-9-4-13 0-3-1-7-1-10v-2h0-3c1-2 1-5 1-7l1-12c0-2 0-5-1-7-1-4-3-7-4-11h-1l1-3v-1c1-1 0-3 1-4s3-2 4-3c1-2 1-3 1-4z" class="R"></path><path d="M661 348l1-2c1 1 2 1 2 1 1 2 1 4 1 5h0c-1-2-3-4-4-4z" class="C"></path><path d="M669 360c1-1 2-1 3-1 2 3 0 9 1 12l-2 2h0v-4-2c0-3 1-5-2-7z" class="b"></path><path d="M677 387c0 1 1 1 1 2l3-1h1l-1 1h2l1 1 1 1h1l1 1c1 0 1 2 2 2v1c-1 1 0 1-1 0h-1l1 1h0c-2-1-3-2-5-2-2-1-5-1-6-2-1-2-1-3 0-5z" class="O"></path><path d="M675 388c0-7 1-14 2-21 1 3 2 6 2 9 0 1-1 2-1 3v5s1 1 1 0c1 0 2-1 3-2l1 1c-1 1-1 2-2 3h-3l-1 1-2 1z" class="D"></path><path d="M671 373l2-2c1 8 1 17 1 25 0 3-1 9 1 12 0 2 0 4 1 6l-2-1c-1-3 0-6-1-9 0-2-2-5-2-7l-1-13 1-3v-4-4z" class="c"></path><path d="M675 388l2-1c-1 2-1 3 0 5 1 1 4 1 6 2l-1 1c-1-1-4-2-5-2 0 3-1 16 0 17l3-1c0-1 0-1 1-2s2-2 4-2v1l3-1h0l1 3 2 1h-1c-2 0-2 1-4 1l-9 2v2 1c2 1 3 2 3 4v3 2h1l1 2-3 1-1 1-1-4c0-3-1-7-1-10-1-2-1-4-1-6v-20z" class="g"></path><path d="M685 405v1l3-1h0l1 3c-4-1-6 0-9 1 0-1 0-1 1-2s2-2 4-2z" class="f"></path><path d="M677 415c2 1 3 2 3 4v3 2h1l1 2-3 1c-1-4-2-8-2-12z" class="B"></path><path d="M683 394c2 0 3 1 5 2 1 2 3 4 3 7-1 1-2 1-3 2h0l-3 1v-1c-2 0-3 1-4 2s-1 1-1 2l-3 1c-1-1 0-14 0-17 1 0 4 1 5 2l1-1z" class="O"></path><path d="M683 394c2 0 3 1 5 2 1 2 3 4 3 7-1 1-2 1-3 2h0l-3 1v-1c2-1 2-2 3-5-2-2-4-4-6-5l1-1z" class="L"></path><path d="M661 348c1 0 3 2 4 4h0l1 3 3 5c3 2 2 4 2 7v2 4h0v4 4l-1 3h-1c-1 0-1 0-2 1l-1 3v1h-1c-1-2 2-9 0-12 0 4-1 5-4 8l1 1c0 2-3 4-3 6v2c1-1 1-2 2-3l1-2c0 2-2 4-3 6l-1 2h0-3c1-2 1-5 1-7l1-12c0-2 0-5-1-7-1-4-3-7-4-11h-1l1-3v-1l3 10v-1h0l3-6c1-3 1-6 2-9 0-1 0-2 1-2z" class="k"></path><path d="M656 390l2-2c1 0 0 0 0 1v8h-3c1-2 1-5 1-7z" class="c"></path><path d="M661 364l1-6c2 3 3 7 5 8v1c-1 1-2 2-2 4-1 1-1 2-2 2h0l1-2c0-2-1-4-2-6l-1-1z" class="L"></path><path d="M665 377v-2c1-2 1-3 2-5 1 0 2 0 3 1h1v2h0v4 4l-1 3h-1c-1 0-1 0-2 1l-1 3v1h-1c-1-2 2-9 0-12z" class="P"></path><path d="M667 380c0-1 0-2 1-3h2 1v4l-2-1h-2z" class="T"></path><path d="M667 380h2l2 1-1 3h-1c-1 0-1 0-2 1l-1 3v-4l1-4z" class="V"></path><path d="M661 348c1 0 3 2 4 4h0l1 3 3 5c3 2 2 4 2 7v2c0-2-1-4-1-5-1-1 0-2-2-2-1 2-1 3-1 5v-1c-2-1-3-5-5-8l-1 6v3c0 1-1 2 0 4v1h0c-3-3 0-8-2-11l-1-2c1-3 1-6 2-9 0-1 0-2 1-2z" class="f"></path><path d="M666 355l3 5c3 2 2 4 2 7v2c0-2-1-4-1-5-1-1 0-2-2-2-1 2-1 3-1 5v-1c0-1-1-2-1-3-1-1-1-3-2-4l-1-1c0-1 0-2 1-3 0 1 1 2 1 3h1c-1-2-1-2 0-3z" class="Y"></path><path d="M660 350h1l-1 1 1 1h1 1v3c-1 1-1 0-1 1v1l-1 1c-1 2-1 6-1 8l1 1c0 1-1 2 0 4v1h0c-3-3 0-8-2-11l-1-2c1-3 1-6 2-9z" class="U"></path><defs><linearGradient id="Ab" x1="673.978" y1="412.542" x2="667.04" y2="413.072" xlink:href="#B"><stop offset="0" stop-color="#b2b0b1"></stop><stop offset="1" stop-color="#deddde"></stop></linearGradient></defs><path fill="url(#Ab)" d="M665 377c2 3-1 10 0 12h1v-1l1-3c1-1 1-1 2-1h1l1 13c0 2 2 5 2 7 1 3 0 6 1 9l2 1c0 3 1 7 1 10l1 4c-1 2-1 3 0 5v1 2h0l-1-1h-1c1 2 2 3 2 4v1l-1-1c0 1-1 2-1 2l-2-1h0c-5-5-8-12-11-18-2-4-3-9-4-13 0-3-1-7-1-10v-2l1-2c1-2 3-4 3-6l-1 2c-1 1-1 2-2 3v-2c0-2 3-4 3-6l-1-1c3-3 4-4 4-8z"></path><path d="M672 424l3-1c-1 1-1 1-1 2 1 0 2 0 3-1l1 4c-1 2-1 3 0 5v1 2h0l-1-1h-1c1 2 2 3 2 4v1l-1-1c-3-5-4-9-5-15z" class="c"></path><path d="M677 424l1 4c-1 2-1 3 0 5v1c-2-3-3-5-4-9 1 0 2 0 3-1z" class="B"></path><path d="M671 397c0 2 2 5 2 7 1 3 0 6 1 9l2 1c0 3 1 7 1 10-1 1-2 1-3 1 0-1 0-1 1-2l-3 1-1-27z" class="L"></path><path d="M674 413l2 1c0 3 1 7 1 10-1 1-2 1-3 1 0-1 0-1 1-2h1v-1c-1-1-2-3-2-4v-5z" class="K"></path><path d="M665 377c2 3-1 10 0 12h1v6 4h0v5c1 6 0 11 2 17l1 5c1 2 1 4 2 6l1 2-1 1 3 5h0c-5-5-8-12-11-18-2-4-3-9-4-13 0-3-1-7-1-10v-2l1-2c1-2 3-4 3-6l-1 2c-1 1-1 2-2 3v-2c0-2 3-4 3-6l-1-1c3-3 4-4 4-8z" class="M"></path><path d="M659 409c2 1 2 1 2 3v2c1 1 1 1 1 2v1c1 1 1 2 2 3 1-1 0-3 0-4h1c1 2 2 5 2 7 1 1 1 2 2 3 1 2 1 4 2 6l1 2-1 1 3 5h0c-5-5-8-12-11-18-2-4-3-9-4-13z" class="Z"></path><path d="M524 306h29c-1 0-2 1-3 1h-1c-4 1-10 0-15 0-1 0-2 1-3 1h-8c-1 1-1 2-1 3h5c4 1 10 0 15 0h49 9 3c1 0 1 1 1 1h-81l6 10c1 3 3 6 4 9 1 5 0 12 0 18v42 48c0 8 1 16 0 24l-10 11h-1v25c0 3 0 7-1 10v-64-1c0-1-1-2-1-3v-5-2-7-4c-1-1-1-1-1-2 1-2 1-4 1-6v-11-3-58c-1-2 0-6 0-8v-20c1-3-1-6 1-9h2 1z" class="G"></path><path d="M571 357c6-6 12-11 17-18-1-1-5-2-5-3 2 1 3 1 5 2 1 0 2-2 3-3v1l-1 2c1 3 12 4 15 5 2-1 0-7 1-8l1 9 11 2v-6h1v7l9 2 1-1c0 3-1 7-2 10-1 4-2 8-2 13-1 3-3 7-5 9l1 1c2 3 3 7 3 11l2 21h-1c0-3 0-6-1-8-1-7-2-13-4-19v3c-1 3-1 7-2 10 0 1 0 3-1 4 0-2 0-3 1-5 0-6 0-12-1-18l-4 3c-1 1-3 3-3 4-1 4 0 8 0 11-1 2-5 8-7 9h0c1-1 2-3 3-5l-3-8-4 3-1-1 4-3-12-13-18 9h-1l18-11c-5-6-11-13-17-18h-2c-1 0 0 0-1-1l2-2z" class="O"></path><path d="M607 390h1c0 3 1 8-1 10v-1l-1-1c0-2-1-3-1-4h-1c2-1 2-2 3-3v-1zm1-25c3 4 7 9 8 14l-2 2-5 5-1-21zm-15-24l13 4v13l-13-17zm17 21c3-1 6-1 8-1 0 4 1 8 0 12l-1 4-6-9c0-1-2-2-2-4 0-1 1-2 1-2zm-3-17c3 1 7 2 11 3v12h-1c-3 0-6 0-8 1 0 0 0 1-1 2l-1-1v-17zm12 3c3 0 6 1 8 1h1v2l-3 12c-1 4-1 8-3 12l-2 4h-1c-1-2 1-5 1-8v-7c-1-5-1-11-1-16zm-12 20v1 14c0 2 1 4 0 5 0 1-3 4-3 5l-5-5-8-9c6-3 11-7 16-11zm-18-29l3 2c1 2 2 3 3 4 3 5 7 10 10 15 1 1 1 4 2 6-5 5-10 8-16 12l-19-19 17-20z"></path><path fill="#fefefe" d="M610 408h1c1 2 1 6 0 8v2l1 112v24c0 1 0 2-1 3l1 4v1c-1-3-3-6-3-9h0l-1-2c0-1 0-1-1-2h0v-1c0-1 0-1-1-2v-1c-1-2-2-2-3-4l-3-2c-1-3-3-3-4-5s-4-6-6-6c-2-1 0 0-1-1l-3-3c-3-1-5-3-8-4-1-2-4-3-5-3l-3-2-4-1c-2-1-3-1-5-1-3-1-5-1-8-1-3-1-7 0-10-1h-14-6-1-1v-2c1-3 1-7 1-10v-25h1 1l21-1c17-1 34-2 44-19l6-15c2-5 4-9 7-14l4-9c1-3 2-6 4-7v-1z"></path><path d="M610 408h1c1 2 1 6 0 8v2l1 112v24c0 1 0 2-1 3l1 4v1c-1-3-3-6-3-9l2 2v-7-21-74l-1-17v-14-1h0l-1 3v1c-1 3-1 5-2 8-1 4 0 9-3 12l-1 1-1-1 1-2 1 1v-1c1-1 1-1 1-2 0-2 1-4 1-5 0-3 1-6 1-8s0-3 1-5c1-4 4-11 2-15z" class="W"></path><path d="M601 650c1 0 2 0 3-1l-1 2v1c-6 8-12 15-10 26 1 5 3 9 6 12 3 1 8 4 10 3h1l-2-1 2-1h1c2 1 4 2 7 2h4v1c-4 3-9 3-13 4-2 0-4 0-5 1 0 0-1 1-1 2-1 2-1 6-2 9-1 5-3 9-3 14v1l-14 7c-1 5-1 11-4 15-1 3-3 5-5 7h-4c-3 5-5 13-10 15-1 1-2 2-3 2l-3 2-2 1c-1 1-3 2-4 2-1 1-2 2-4 2-1 0-3-1-4-2-2 0-4-1-6-2h-1v-2c-1 1-1 2-1 3h-1v1 1c-2-3-5-9-8-10h-1-1c-1-1-2-3-2-5l1-2c-1-1-1-3-1-4v-10l1-48c-1-5 0-10 0-16v-2h0l-2-2v-1h1l3 3 1 2h0c1 2 3 4 5 5h1 1l1 1 1 4h2l1-1c1-1 4-2 6-2h2v1h-1l1 1h1c1-1 2-1 4-1-1-1-1-1-1-2 1 0 1 0 2-1-1-1-4 0-5-1h-1-1l-1-1c3-3 5-7 7-10l1-2h1v1l2-1h0v3c0 1 1 2 1 3l3-6c0 1 1 2 2 3h0c1 0 2 1 3 2l-1 3c0 1 1 2 2 3s1 2 2 3l1-1 1-1 2-3 2 3c1-2 2-3 3-4l2-5c1-1 1-3 2-4s2-5 2-6l2 1v-1h4l-1 3c0 1 0 2 1 3v2h0c1-2 1-4 2-5h1c2-7 6-14 12-19z" class="e"></path><path d="M556 739h0l1 2-1 2c-1 1-2 4-4 5h0v-2c1-1 2-1 2-3l-2 2 2-3c1-1 1-2 2-3z" class="R"></path><path d="M548 712c-2 0-6 0-8-2s-1-3-1-5v1h0c1 2 2 3 3 4h1c2 0 4 0 6-1l1 1c-1 0-1 1-2 2z" class="K"></path><path d="M535 774l1-1h2 0 2c1 1 1 1 3 1v-2l2-1c0 2 1 3 0 4s-2 1-4 1-4-1-6-2z" class="C"></path><path d="M566 715c-1 3-1 4-3 6-1 1-3 1-4 2l1 1c-3 1-6 0-9 0 2 0 5-1 7-2l1-1c2-2 4-4 7-6z" class="S"></path><path d="M544 763c-1-1-1-2-2-3l-2 1-2-2c1-2 4-3 6-4 1-1 3-2 5-3h0c0 2-1 3-1 4-1 1-2 2-2 3v1l-1 2c-1 1 0 1-1 1z" class="O"></path><path d="M571 705l2-2-1-2h1c2-1 2-1 4-1l-4 8-3 6c0 2-2 4-3 6-1 0-2 2-2 2-2 1-3 1-5 2l-1-1c1-1 3-1 4-2 2-2 2-3 3-6 2-3 4-7 5-10z" class="g"></path><path d="M548 742c0-1-1-1-1-1-1 0-2 1-3 2-2 1-7 5-10 5l1-5c0-1 1-2 2-2s3 0 5-1 3-4 6-5c4-2 6-5 10-6l-4 4c-1 2-3 3-3 5-1 1-1 2-3 4z" class="R"></path><defs><linearGradient id="Ac" x1="535.405" y1="755.448" x2="554.828" y2="736.916" xlink:href="#B"><stop offset="0" stop-color="#1d1f23"></stop><stop offset="1" stop-color="#393c3d"></stop></linearGradient></defs><path fill="url(#Ac)" d="M548 742c2-2 2-3 3-4 0-2 2-3 3-5v5c1 0 1 1 2 1-1 1-1 2-2 3l-2 3c-2 2-5 5-7 6h0c-3 2-7 4-11 5v-2c1-2 3-2 4-3 5-2 8-5 10-9h0z"></path><path d="M554 706c0-1 1-1 2-2 0 1 0 2 1 3v2c-1 2-1 3-2 4l1 2-1 2c-2 0-3 1-5 1l-1 1c-2 1-6 1-8 0h-1c-1-1-3-2-5-3l5 1c2 0 7-1 8-2-2 0-4 1-7 1-2 0-4 0-5-2v-1h1c2 1 3 2 5 1 2 0 5 0 7-2h-1c1-1 1-2 2-2l4-4z" class="B"></path><path d="M554 706c0-1 1-1 2-2 0 1 0 2 1 3v2c-1 2-1 3-2 4l1 2-1 2c-2 0-3 1-5 1l-1-1c2 0 3 0 3-2h-2l4-2v-1l-1-1c0-1 1-3 1-4v-1z" class="L"></path><path d="M542 695c3 0 6-1 8 1 2 1 2 2 3 4h1c1 1 0 2 0 4-1 2-4 4-5 5-2 1-4 1-6 1h-1c2-1 3-1 4-1l-1-1h-3c-1-1-1-1-1-3v-1c1 1 1 1 1 2h0 2c1 1 1 1 2 1v-1c-1 0-1 0-2-1v-2-2c-1 0-1 2-2 3h0c0-2 0-3 1-5h0l-1-1c-1 1-1 2-2 4h-1v-3l1-1h-2v1l-1-1c0-1 1-1 3-2 0-1 1-1 2-1z" class="I"></path><g class="b"><path d="M542 695c3 0 6-1 8 1 2 1 2 2 3 4v3c-1 2-3 3-4 4h-1l2-2v-4c0-1-1-2-2-3h-1v-2c-2-1-3 0-5-1z"></path><path d="M544 686l6-1c2 1 5 2 7 4l-3 1c1 2 2 5 3 7 2 4 1 9 0 12v-2c-1-1-1-2-1-3-1 1-2 1-2 2l-4 4-1-1c1-1 4-3 5-5 0-2 1-3 0-4h-1c-1-2-1-3-3-4-2-2-5-1-8-1-1 0-2 0-2 1l-1-1-1 1h-1v-2c1-1 2-2 3-2l1-1s1-1 2-1l1 1h1c1-1 2-1 4-1-1-1-1-1-1-2 1 0 1 0 2-1-1-1-4 0-5-1h-1z"></path></g><path d="M540 696l-1-1-1 1h-1v-2c1-1 2-2 3-2l1-1s1-1 2-1l1 1h1c4 0 6 1 9 5h0c-1-1-3-2-5-2 2 1 4 3 5 6h-1c-1-2-1-3-3-4-2-2-5-1-8-1-1 0-2 0-2 1z" class="X"></path><path d="M550 685c2-1 4-1 6 0 1 1 4 3 4 5 1-1 1 0 2-1h0c1 0 2-1 3-1h0c-1 2-1 2-2 3 0 4 2 8 1 13v1c-1 1-1 3-2 4 0 1-1 2-1 3-1 2-3 3-4 5h-2l1-2-1-2c1-1 1-2 2-4 1-3 2-8 0-12-1-2-2-5-3-7l3-1c-2-2-5-3-7-4z" class="a"></path><path d="M550 685c2-1 4-1 6 0 1 1 4 3 4 5 1-1 1 0 2-1h0c1 0 2-1 3-1h0c-1 2-1 2-2 3 0 4 2 8 1 13v1c-1 1-1 3-2 4 0 1-1 2-1 3-1 2-3 3-4 5h-2l1-2c3-4 6-7 7-12 0-6-3-10-6-14-2-2-5-3-7-4z" class="K"></path><path d="M554 679l3-6c0 1 1 2 2 3h0c1 0 2 1 3 2l-1 3c0 1 1 2 2 3s1 2 2 3v1c-1 0-2 1-3 1h0c-1 1-1 0-2 1 0-2-3-4-4-5-2-1-4-1-6 0l-6 1h-1l-1-1c3-3 5-7 7-10l1-2h1v1l2-1h0v3c0 1 1 2 1 3z" class="e"></path><path d="M554 679l3-6c0 1 1 2 2 3h0c-2 3-2 4-5 5h-2c0-1 1-2 2-2z" class="k"></path><path d="M549 675l1-2h1v1l2-1h0v3c0 1 1 2 1 3-1 0-2 1-2 2-1 1-2 1-3 1 1-2 1-5 1-7h-1z" class="E"></path><path d="M571 685c1-2 2-3 3-4l2-5c1-1 1-3 2-4v8c1 1 1 2 1 2l-2 2h0c0 2 0 3-1 5h0c0 2-1 2-2 3h0v1h1c1 0 1 0 2 1h-1-1v1c1 0 1 0 2 1h0v4c-2 0-2 0-4 1h-1l1 2-2 2v-2c-2-1-2-1-3-1-2 3-3 7-6 9l-1 1c0-1 1-2 1-3 1-1 1-3 2-4v-1c1-5-1-9-1-13 1-1 1-1 2-3h0v-1l1-1 1-1 2-3 2 3z" class="H"></path><path d="M567 685l2-3 2 3c0 1 0 1-1 2-2 0-2 0-4-1l1-1z" class="d"></path><path d="M575 695c1 0 1 0 2 1h0c-1 2-1 2-2 3-2 0-4 0-5-2h1 1v-1h-2v-1h5zm0-1c-2 0-3 0-5-1v-2c2-1 3-2 3-4 1-1 1-3 2-5v-1c1 0 1-1 2-2-1 4-1 6-1 10h0c0 2-1 2-2 3h0v1h1c1 0 1 0 2 1h-1-1z" class="F"></path><path d="M562 711l2-3c1-3 2-5 2-8v-6c0-2 1-4 2-7v4c0 2 0 4 1 6h1c1 2 3 2 5 2 1-1 1-1 2-3v4c-2 0-2 0-4 1h-1l1 2-2 2v-2c-2-1-2-1-3-1-2 3-3 7-6 9z" class="C"></path><path d="M552 748c2-1 3-4 4-5v3c1-1 1-2 1-3h1v1c0 2 1 5 0 8-1 1-1 3-2 5-1 3 0 8-2 11l-1 3v1 1c-1 1-1 0 0 1-1 1-3 2-4 2-1 1-2 2-4 2-1 0-3-1-4-2 2 0 3 0 4-1s0-2 0-4 0-5-1-8c1 0 0 0 1-1l1-2v-1c0-1 1-2 2-3 0-1 1-2 1-4h0l3-4z" class="U"></path><path d="M552 748c2-1 3-4 4-5v3l-1 9c0 3-1 5-4 7v1-4l2-4-1 1c-1 0-1 1-2 1h0-2v-1c0-1 1-2 1-4h0l3-4z" class="f"></path><path d="M553 755c-1-2-1-3 0-5h1 0v4l1 1c0 3-1 5-4 7v1-4l2-4z" class="c"></path><defs><linearGradient id="Ad" x1="541.959" y1="764.672" x2="550.705" y2="772.35" xlink:href="#B"><stop offset="0" stop-color="#161a1d"></stop><stop offset="1" stop-color="#3a3b3d"></stop></linearGradient></defs><path fill="url(#Ad)" d="M548 756v1h2 0c1 0 1-1 2-1l1-1-2 4v4h0c0 1-1 3 0 4 1 3-2 6-2 9-1 1-2 2-4 2-1 0-3-1-4-2 2 0 3 0 4-1s0-2 0-4 0-5-1-8c1 0 0 0 1-1l1-2v-1c0-1 1-2 2-3z"></path><path d="M548 756v1h2 0c1 0 1-1 2-1l1-1-2 4v4h0-1l-1 2h-1l1-1v-2c-1-1-1-3-2-3-2 1-1 2-1 5h-1v-2l1-2v-1c0-1 1-2 2-3z" class="B"></path><defs><linearGradient id="Ae" x1="579.926" y1="710.66" x2="559.122" y2="695.122" xlink:href="#B"><stop offset="0" stop-color="#222323"></stop><stop offset="1" stop-color="#383d3f"></stop></linearGradient></defs><path fill="url(#Ae)" d="M582 666h4l-1 3c0 1 0 2 1 3v2h0c1-2 1-4 2-5h1l-2 8v8l1 3 2 5c-1-1-2-1-3-2v2h-1-1v6c-1 1-1 3-1 4 0 2-1 4-1 6-1 1-1 2-1 3v1l-3 3v1h0v1l-1 1h0v1l-2-1-1 1h-1-1c-6 4-8 11-12 17l-4 4-1-2h0c-1 0-1-1-2-1v-5l4-4h0l7-7s1-2 2-2c1-2 3-4 3-6l3-6 4-8v-4h0c-1-1-1-1-2-1v-1h1 1c-1-1-1-1-2-1h-1v-1h0c1-1 2-1 2-3h0c1-2 1-3 1-5h0l2-2s0-1-1-2v-8c1-1 2-5 2-6l2 1v-1z"></path><path d="M573 720c3-4 6-8 8-12l1 5-3 3v1h0v1l-1 1h0v1l-2-1-1 1h-1-1z" class="H"></path><path d="M567 720h1c-4 6-7 13-11 18l-1 1h0c-1 0-1-1-2-1v-5l4-4h0l7-7s1-2 2-2z" class="P"></path><path d="M558 729v1c0 1-1 3-2 4 0 2 0 3 1 4l-1 1h0c-1 0-1-1-2-1v-5l4-4h0z" class="S"></path><path d="M579 682c1 11 3 20-4 29l-7 9h-1c1-2 3-4 3-6l3-6 4-8v-4h0c-1-1-1-1-2-1v-1h1 1c-1-1-1-1-2-1h-1v-1h0c1-1 2-1 2-3h0c1-2 1-3 1-5h0l2-2z" class="h"></path><path d="M576 689c1-2 1-3 1-5h0c1 2 1 7 0 10-1-1-1-1-2-1h-1v-1h0c1-1 2-1 2-3h0z" class="i"></path><path d="M582 666h4l-1 3c0 1 0 2 1 3v2h0c1-2 1-4 2-5h1l-2 8v8l1 3 2 5c-1-1-2-1-3-2v2h-1-1v6c-1 1-1 3-1 4 0 2-1 4-1 6-1 1-1 2-1 3v1l-1-5c2-8 2-15 1-23-1-4-2-8-1-12l1-6v-1z" class="h"></path><path d="M584 672c0 4 0 8 1 12l-1-1c-1-2-2-4-2-6h0c0-3 1-4 2-5z" class="H"></path><path d="M585 669c0 1 0 2 1 3v2h0c1-2 1-4 2-5h1l-2 8v8h-1v2h0c0-1 0-2-1-2v-1c-1-4-1-8-1-12l1-3z" class="D"></path><path d="M561 737c4-6 6-13 12-17h1 1l3 1-1 1s0 1 1 2l1 2c2 1 2 3 2 4 0 2 0 3-1 5 0 3-1 7-3 11-1 3-4 5-6 8-3 5-5 13-10 15-1 1-2 2-3 2l-3 2-2 1c-1-1-1 0 0-1v-1-1l1-3c2-3 1-8 2-11 1-2 1-4 2-5 1-3 0-6 0-8v-1h-1c0 1 0 2-1 3v-3l1-2 4-4z" class="b"></path><path d="M575 720l3 1-1 1s0 1 1 2h-1c1 1 1 2 1 3-1 0-1-1-2-2 0-2-2-3-2-5h1z" class="O"></path><path d="M557 741l4-4c0 3 0 4-1 6-2 2-1 7-1 9h-1 0c1-3 0-6 0-8v-1h-1c0 1 0 2-1 3v-3l1-2z" class="Q"></path><path d="M567 753c0-6-2-10-2-16h1c1 3 1 7 3 9 0-1-1-3-1-5s-1-4-1-7h1c0 4 2 7 2 11h1c0-3-1-5-2-9 0-1 0-3-1-5h1c2 3 2 8 3 11l1-1c-1-3-1-7-2-10-1-1-1 0-1-1 1 1 3 2 3 5 0 2 1 3 2 5h1v-1c-1-1-1-1-1-2h1v-1c0-2-1-3-2-4v-1-1c2 1 2 2 3 4v1h1c0-3-5-7-6-10h1c1 2 3 4 5 5v-1l1-1-1-2h1c2 1 2 3 2 4 0 2 0 3-1 5 0-2 0-4 1-6h-1v1c0 2-1 4-2 7 0 1 0 3-1 4l-4 4c-1 2-4 6-6 8z" class="g"></path><path d="M567 753c2-2 5-6 6-8l4-4c1-1 1-3 1-4 1-3 2-5 2-7v-1h1c-1 2-1 4-1 6 0 3-1 7-3 11-1 3-4 5-6 8-3 5-5 13-10 15h0c0-1 0-2 1-3v-1c0-2 1-4 1-6s0-4 1-5l1 1c1-1 0-3 0-4h1v3h1v-1z" class="a"></path><path d="M558 752c0 2 0 3 1 5h0c0-2 0-6 1-8h1v-6l1-1c0 2 0 3 1 4v-8l2 8c1 1 1 3 1 5h-1c0 1 1 3 0 4l-1-1c-1 1-1 3-1 5s-1 4-1 6v1c-1 1-1 2-1 3h0c-1 1-2 2-3 2l-3 2-2 1c-1-1-1 0 0-1v-1-1l1-3c2-3 1-8 2-11 1-2 1-4 2-5h0z" class="k"></path><path d="M555 773l3-15 2 8c0-4-1-9 1-12v-1c1 2 1 4 2 6h0c0 2-1 4-1 6v1c-1 1-1 2-1 3h0c-1 1-2 2-3 2l-3 2z" class="W"></path><path d="M558 771c-1-2 0-4 1-5s2-1 3-1v1c-1 1-1 2-1 3h0c-1 1-2 2-3 2zm-34-4h-1-1c-1-1-2-3-2-5l1-2c-1-1-1-3-1-4v-10l1-48c-1-5 0-10 0-16v-2h0l-2-2v-1h1l3 3 1 2h0c1 2 3 4 5 5h1 1l1 1 1 4-1 10c0 4 1 9 0 12l-1-2v1s0 1 1 2v13c-1-1-2-3-3-4 1 2 3 4 3 6 1 1 0 6 0 7h0c-1-3-2-5-3-7v1c1 2 2 4 2 6 0 0 1 1 1 2v11c0 2 1 4 0 5v3 2c0-1-1-2-2-2h0v1c1 1 2 2 2 3v1c-1-1-3-4-4-5 0 3 6 7 4 10h0c-1-3-4-7-6-10 0 2 2 5 3 7 2 2 3 6 3 8 0 0-1-1-1-2l-3-6c-2-3-4-4-4-8l-1-1c0 2 0 4 1 6h1c0 1 1 2 2 3 2 2 3 7 5 10v1 1c-2-3-5-9-8-10z" class="T"></path><path d="M532 702c-2-2-3-5-5-8h0v-1l2 2v-1h0c-1-2-3-4-4-5h-1c-1-2-1-2-1-4l1-1v-2c1 2 3 4 5 5h1 1l1 1 1 4-1 10z" class="H"></path><defs><linearGradient id="Af" x1="570.27" y1="702.264" x2="619.265" y2="700.152" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292829"></stop></linearGradient></defs><path fill="url(#Af)" d="M601 650c1 0 2 0 3-1l-1 2v1c-6 8-12 15-10 26 1 5 3 9 6 12 3 1 8 4 10 3h1l-2-1 2-1h1c2 1 4 2 7 2h4v1c-4 3-9 3-13 4-2 0-4 0-5 1 0 0-1 1-1 2-1 2-1 6-2 9-1 5-3 9-3 14v1l-14 7c-1 5-1 11-4 15-1 3-3 5-5 7h-4c2-3 5-5 6-8 2-4 3-8 3-11 1-2 1-3 1-5 0-1 0-3-2-4l-1-2c-1-1-1-2-1-2l1-1-3-1 1-1 2 1v-1h0l1-1v-1h0v-1l3-3v-1c0-1 0-2 1-3 0-2 1-4 1-6 0-1 0-3 1-4v-6h1 1v-2c1 1 2 1 3 2l-2-5-1-3v-8l2-8c2-7 6-14 12-19z"></path><path d="M587 677c1 0 1 0 1 1 1 3 1 5 2 8v1l-2 1-1-3v-8z" class="K"></path><path d="M590 686l1 1c2 2 2 3 5 4h0c-1 2 0 2 0 4l-1-1c-1 0-2-1-2-1-2-2-3-4-3-6v-1z" class="B"></path><path d="M587 724l1-1 4-5h2c1 0 1-1 2-1 0 2-1 5-3 6-1 1-4 1-6 1z" class="N"></path><path d="M590 687c0 2 1 4 3 6 0 0 1 1 2 1l1 1c0-2-1-2 0-4l3 3c1 1 2 2 2 4 0 1-1 2-1 4s-1 4-1 6c-1 3-1 6-3 9-1 0-1 1-2 1h-2l-4 5-1 1-9-3-3-1 1-1 2 1v-1h0l1-1v-1h0v-1l3-3v-1c0-1 0-2 1-3 0-2 1-4 1-6 0-1 0-3 1-4v-6h1 1v-2c1 1 2 1 3 2l-2-5 2-1z" class="Z"></path><g class="L"><path d="M590 687c0 2 1 4 3 6 0 1 0 2 1 4v1h0v2c0 1-1 4-2 5-2 6-4 12-8 16l-6-1v-1h2 1c2 0 2-1 3-2 2-2 3-4 5-6l2-5c1-2 2-4 2-6s-2-5-3-7l-2-5 2-1z"></path><path d="M593 693s1 1 2 1l1 1c0-2-1-2 0-4l3 3c1 1 2 2 2 4 0 1-1 2-1 4s-1 4-1 6c0-2 0-4-1-5-2 4-2 7-4 11l1-5c1-3 2-6 0-9l-1-2v-1c-1-2-1-3-1-4z"></path></g><path d="M593 693s1 1 2 1l1 1c0-2-1-2 0-4l3 3h-1v6h-1 0l-1-1h0c-1 1 0 1-1 1l-1-2v-1c-1-2-1-3-1-4z" class="E"></path><path d="M587 693v-2c1 1 2 1 3 2 1 2 3 5 3 7s-1 4-2 6l-2 5c-2 2-3 4-5 6-1 1-1 2-3 2h-1-2 0l1-1v-1h0v-1l3-3v-1c0-1 0-2 1-3 0-2 1-4 1-6 0-1 0-3 1-4v-6h1 1z" class="W"></path><path d="M582 713v-1c0-1 0-2 1-3h1c0 4-2 6-5 8h0v-1l3-3z" class="F"></path><path d="M583 709c0-2 1-4 1-6 0-1 0-3 1-4v-6h1c0 6 0 11-2 16h-1z" class="V"></path><path d="M587 693v-2c1 1 2 1 3 2 1 2 3 5 3 7s-1 4-2 6l-2 5c-1 0-1 0-1-1h-1l-1 1-1 1h0c2-7 3-11 2-19z" class="Z"></path><path d="M691 271l2 1v-1l1 1s1 1 2 1h2 1l2 1h1l-2 1 1 1h1 2v1l-2-1c-1 1-1 1-1 2 1 1 2 1 3 2-1 1-1 1-2 1l-2 1c0 2 0 3 1 4l-1 1v1 2c0 1 1 2 2 4l-1 1v2h-1l-1 1v1l2 2h-1 0l-1-1h-1v2 31h-1v46c-1 5 0 10-1 15l-1 1c-2-2-2-3-3-5v1c-1-1-1-1-1-2-11-21-17-42-33-60-15-15-34-17-54-17 0 0 0-1-1-1h-3-9-49c-5 0-11 1-15 0h-5c0-1 0-2 1-3h8c1 0 2-1 3-1 5 0 11 1 15 0h1c1 0 2-1 3-1h-29c1-1 1-1 3-1l1 1c1-1 4-1 6-1 0-3 3-5 5-7v-1c1 0 1-1 2-2v-1l1-1v-2-1-3-3c-2-1-2-1-3-1-2 1-2 1-4 1-1-1 0-1-1-3h0c1 0 1 0 2 1h1 0c0-1-1-2-2-2-2-1-4-3-5-5v-1c1 1 2 1 2 2h1 1l-2-2h7c2 1 2 7 3 9l2-2-2-6h102 31c1-1 4-1 5-1h11v-2-1z" class="o"></path><path d="M544 281c1 1 1 2 1 3 0 5 1 9-3 13l-5 5h0c3-4 6-7 6-12l-1-7 2-2z" class="R"></path><path d="M691 271l2 1v-1l1 1s1 1 2 1h2 1l2 1h1l-2 1 1 1h1 2v1l-2-1c-1 1-1 1-1 2 1 1 2 1 3 2h-3l-1-1v-2h-1c0-1 0-1-1-1 0 1 0 2-1 3v1c-1 0-3 1-4 1l-1-1-1 1c-2 2-5 4-7 7-3 3-1 13 0 17l-1 1c0-4-1-8-1-12-1-1-1-2-1-3 0-2 2-4 3-5l11-11h-20c1-1 4-1 5-1h11v-2-1z" class="B"></path><path d="M692 280l4-4h1v3 1c-1 0-3 1-4 1l-1-1z" class="g"></path><path d="M697 279c1-1 1-2 1-3 1 0 1 0 1 1h1v2l1 1h3c-1 1-1 1-2 1l-2 1c0 2 0 3 1 4l-1 1v1 2c0 1 1 2 2 4l-1 1v2h-1l-1 1v1l2 2h-1 0l-1-1h-1v2 31h-1v46c-1 5 0 10-1 15l-1 1c-2-2-2-3-3-5v1c-1-1-1-1-1-2 1-2 0-8 0-11v-36c0-5 1-11 0-16-1-2-2-5-3-7l3 3v-41l1-1 1 1c1 0 3-1 4-1v-1z" class="S"></path><path d="M697 279c1-1 1-2 1-3 1 0 1 0 1 1h1v2l1 1h3c-1 1-1 1-2 1l-2 1c0 2 0 3 1 4l-1 1v1 2c0 1 1 2 2 4l-1 1v2h-1l-1 1v1l2 2h-1 0l-1-1h-1v2 31h-1c-1-4 0-10 0-14v-39-1z" class="O"></path><path d="M553 306h1 2c2-1 9-1 11-1h34 12c5 1 10 0 15 2h2 1c1 0 1 0 2 1h3 0c1 0 1 0 2 1 1 0 2 1 4 1h0c1 1 1 1 2 1 3 2 5 3 8 4l2 2 3 2 2 1v1l2 2 1 1 6 5h0c0-2 1-3 0-4v-1l1-1c1 1 0 2 1 4v2 1l1 1v1c1 1 1 1 1 2l1 2 2 4 2 2h0v-1-2-1c2 3 1 4 2 7h0c0 1 1 3 2 5v2h1c1 2 1 4 2 6l1 2v1c1 1 2 3 2 4l1 4s1 1 1 2v1 2l1 1v2l1 1c0 3 1 9 0 11-11-21-17-42-33-60-15-15-34-17-54-17 0 0 0-1-1-1h-3-9-49c-5 0-11 1-15 0h-5c0-1 0-2 1-3h8c1 0 2-1 3-1 5 0 11 1 15 0h1c1 0 2-1 3-1z" class="n"></path><defs><linearGradient id="Ag" x1="537.339" y1="547.347" x2="495.21" y2="544.721" xlink:href="#B"><stop offset="0" stop-color="#c7c6c3"></stop><stop offset="1" stop-color="#f8f5f0"></stop></linearGradient></defs><path fill="url(#Ag)" d="M521 278l-2-3 1-1c0 1 2 2 3 2l-1-1h1l2 2c1 1 2 2 4 3 1 0 2 0 3-1 1 0 2 1 3 1s2 1 2 2h0-1c-1-1-1-1-2-1h0c1 2 0 2 1 3 2 0 2 0 4-1 1 0 1 0 3 1v3 3 1 2l-1 1v1c-1 1-1 2-2 2v1c-2 2-5 4-5 7-2 0-5 0-6 1l-1-1c-2 0-2 0-3 1h-1-2c-2 3 0 6-1 9v20c0 2-1 6 0 8v58 3 11c0 2 0 4-1 6 0 1 0 1 1 2v4 7 2 5c0 1 1 2 1 3v1 64 2h1 1 6 14c3 1 7 0 10 1 3 0 5 0 8 1 2 0 3 0 5 1l4 1 3 2c1 0 4 1 5 3h-1l-3-2c-2-1-3-1-5-2l-5-2h-1-3c-13-2-26-2-39-2v4h17 0-16l1 1 4 6c2 2 5 5 6 7v6 12 14 7l-1-1-2-1c0 2 2 2 3 4v4 15 55 7 16 10l-1 4v6l-1-1h-1-1c-2-1-4-3-5-5h0l-1-2-3-3h-1v1l2 2h0v2c0 6-1 11 0 16l-1 48v10c0 1 0 3 1 4l-1 2c0 2 1 4 2 5h1 1l9 13c-2-2-4-3-6-5-2-3-3-7-6-8v1c3 2 4 5 5 7l6 6v1c-5-3-8-7-11-12v-1 2c2 2 3 4 4 6l5 5s2 1 2 2v1c-2-1-3-2-4-3-3-3-6-6-7-9-1 1 0 3 0 5v1c1 1 1 1 1 2 1 4 5 6 6 10h1 1v1l-1 1c1 2 3 4 3 6-2-2-3-4-5-5-3-2-4-7-7-9 0 3 7 10 9 13 2 2 3 4 4 5-2-1-4-3-5-4-2-2-3-4-5-6-1-1-1-2-2-4l-1-1c0 2 1 4 2 5l1 1c2 1 2 3 4 5 2 3 4 5 6 8-3-1-7-7-9-9-1-2-2-4-4-5 0 1 0 1 1 2 2 3 4 6 7 9l3 4h0c-2-1-3-3-5-5-3-1-4-5-6-7v1c1 3 4 7 6 10 1 2 3 4 4 6 0 1 0 1 1 2s1 2 1 3h-1 0c-1-1-1 0-2-1s-2-2-3-4c0-1-2-3-3-4-1-2-2-3-3-5h0c0 4 3 7 5 10s5 5 7 8h0c-5-4-9-9-13-14 2 4 5 9 8 12 2 2 4 3 5 5-1 0-3-3-5-3-2-2-5-6-7-8 0-2-1-2-1-3h-1v2c-2 0-2-5-4-5v1l1 2v1l1 1h-1l-3-3c-2 1-3 1-4 2 0-4 0-7-1-11h0c-1-2 0-6 0-9v-27-33-264c1-2 1-3 1-4-2-3-1-11-1-14 0-14 0-29 1-43 0-2-1-6 0-8 0-1 1-1 1-2v-4-13-64c1-2 0-3 0-5l1-3v-1s1-1 1-2v-1s0-1 1-2l2-3c1-2 0-3 1-5 0 0 1-1 2-1 0-2 4-6 6-7 1-1 2-1 2-2-1-1-4-4-4-5v-1z"></path><path d="M527 636c2 2 2 6 4 8 1-3 1-6 1-9v-43c0-1 0-1 1-2v55c0 1 0 3-1 3l-2-3c0-1 0-2-1-3-2 0-2-3-3-4v-2h1z" class="a"></path><path d="M520 525c1-1 1-2 1-3v-1h2 1l1 2h1 1c2 2 5 5 6 7v6 12 14 7c-1-5-1-11-1-16-1-1 0-3 0-4l-1 2h0v-10c0-1-1-1-1-2l-6-6v-1h-2-1c-1-2-1-5-1-7z" class="M"></path><path d="M520 441c0 1 1 2 1 3v1 64 2h1 1 6 14c3 1 7 0 10 1 3 0 5 0 8 1 2 0 3 0 5 1l4 1 3 2c1 0 4 1 5 3h-1l-3-2c-2-1-3-1-5-2l-5-2h-1-3c-13-2-26-2-39-2v4h17 0-16l1 1 4 6h-1-1l-1-2h-1-2v1c0 1 0 2-1 3-1-3 0-7 0-10v-17-57z" class="T"></path><defs><linearGradient id="Ah" x1="534.101" y1="658.047" x2="521.165" y2="654.489" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#bfbebc"></stop></linearGradient></defs><path fill="url(#Ah)" d="M520 671v-1c1-5 0-10 0-15v-20-8l2-2v1 7c0 4 1 15-1 17 0 2-1 4 0 6 1 3 0 7 0 11h1c2-5 0-12 1-17-1-2 0-4 0-5-1-1-1-2-1-3 1-1 1-2 1-3v-1-1h0 1v3 4-2l2-2h0v-2c-1-1-1 0-1-1v-1-1-1l1 1 1 1h-1v2c1 1 1 4 3 4 1 1 1 2 1 3l2 3c1 0 1-2 1-3v7 16 10l-1 4v6l-1-1h-1-1c-2-1-4-3-5-5h0l-1-2-3-3v-1-5z"></path><path d="M526 679c1 1 3 3 3 5v1l2 2h-1-1c-2-1-4-3-5-5h0v-1c1 0 2-1 2-2z" class="F"></path><path d="M520 671l3 3 3 5c0 1-1 2-2 2v1l-1-2-3-3v-1-5z" class="M"></path><path d="M533 668v10l-1 4c0-1-1-2-1-3-1-2-3-3-3-6-1-1 0-1 0-2 2 0 1 2 3 2 0-2 1-3 2-5z" class="H"></path><path d="M390 207l2-3 10-14c6-7 13-15 20-21 28-23 61-35 97-32 29 2 56 16 77 35 7 6 13 13 19 20 1 0 1 1 2 2 1 0 1 0 2 1v-2h1v-1c2 1 3 3 5 3l3 6c0 1 1 3 1 4l5 11v1c0 1 2 3 1 4h0v3l1 2c12 1 24 2 36 6 0 1 3 2 3 2 4 2 7 3 10 6 6 5 10 12 17 15 3 2 6 2 9 2 3 1 10 0 13 1 0 2-1 3-2 4h0l1 2c-1 0-1 1-2 2 0 2 0 4-1 6l-1 1 1 1c0 2-1 4 0 6v1c-1 0-1 0-2 1h-1l-1 1c2 1 2 0 3 1h-2 0l1 2 1-1v1 1h0l-1 1c1 0 2 1 2 1v4l2 4c0 3 1 4 0 6h-3c-1 0-3 1-4 2l-1-1v-1h-3c-2-1-4-1-6-1l-4-1-2-2v-1l1-1h1v-2l1-1c-1-2-2-3-2-4v-2-1l1-1c-1-1-1-2-1-4l2-1c1 0 1 0 2-1-1-1-2-1-3-2 0-1 0-1 1-2l2 1v-1h-2-1l-1-1 2-1h-1l-2-1h-1-2c-1 0-2-1-2-1l-1-1v1l-2-1v1 2h-11c-1 0-4 0-5 1h-31-102l2 6-2 2c-1-2-1-8-3-9h-7l2 2h-1-1c0-1-1-1-2-2v1c1 2 3 4 5 5-1 0-2-1-3-1-1 1-2 1-3 1-2-1-3-2-4-3l-2-2h-1l1 1c-1 0-3-1-3-2l-1 1 2 3v1c0 1 3 4 4 5 0 1-1 1-2 2-2 1-6 5-6 7-1 0-2 1-2 1-1 2 0 3-1 5l-2 3c-1 1-1 2-1 2v1c0 1-1 2-1 2v1l-1 3c0 2 1 3 0 5v64 13 4c0 1-1 1-1 2-1 2 0 6 0 8-1 14-1 29-1 43 0 3-1 11 1 14 0 1 0 2-1 4v264 33 27c0 3-1 7 0 9v25 5c1 1 2 3 2 4 1 2 2 4 2 6 0 3 1 6 2 8v3l-1-1v4l1 5 1 2v3h1v4l1 4c0 1 1 1 1 2v2c1 1 1 2 1 3l3 8c1 3 2 5 3 7 0 1 0 2 1 2 0 2 1 5 3 7v-1l-5-15c3 0 8 0 11 1h4c1 1 1 1 2 0l2 2 1 1c2 4 6 7 8 11 1 0 1 1 2 1l1-1c0-2-3-5-5-7-1-1-2-1-2-3 2 0 3-1 5-1l7-2 12 12h0 0c1 1 2 1 2 1 2 1 3 2 4 3h-2v1c2 1 3 2 5 1h1l3 3h-7c-2-2-5-5-6-7-1-1-2-2-4-3v2c0 3 4 5 6 8h-7-3-1-2-5-1 0-2c-4 1-8 1-12 1h-20-42l-1-2c-2 2-9 2-13 2v-2h-1l-1 1v1h-15c-1 0-4 0-5-1h-1l-1 1h-18c-3 0-6 0-9-1l1-1v-1l-2 2c-1 1-4 1-5 1h-13l1-1-2-2 17-7 6-3c3-3 7-4 10-7 13-9 24-23 30-38 2-6 4-12 4-18 2-14 1-29 1-43 0-3 1-10 0-13-6 0-10-6-15-8h-2c-4 0-7-7-9-10-1-2-3-4-4-7l3 1c-2-2-3-4-5-6-4-7-5-13-6-21 2 1 2 2 4 2v-3l-1-2 2-2h1l1 1h0v-2-1c1 2 3 5 5 6l1-1c1 1 1 2 2 3 2 3 5 7 6 10 0-1-4-8-5-10v-1l1 1h1c0-2-1-3-2-4-3-5-6-8-9-12l-1-1c-1-2-3-4-4-6-1-5-2-8-1-12 3-7 6-12 7-19v-2c1-4 1-7 0-11-1-3-3-6-5-9l-3-3c0-2-7-5-9-7h-1l-7-7c-1-2-2-3-2-4s-3-3-3-4c-1 0-2-1-2-1l-1-1c0-1-1-2-1-3l-1-1c1-2 1-4 1-6s-1-4-3-5l-4-5-1-1c0-2 4-6 6-8l2-2v-1l1-1v-3l-1 1h-2l1-3h1c1 1 0 0 1 0s1 0 2-1l-1-1v-1l2-1c1-2 3-3 5-5l1-3 1-2-1-1 1-1-1-1-1-2c-1-2-3-4-5-5l-2-2-1-2-3-4-2-3c-3-6-6-15-11-18 1-2 1-2 1-3l-1-5c-2-5-3-10-5-15l1-4c0-1 1-1 2-2 0-2-2-4-3-6-1-4-2-7-5-11l-3-3v-2c-1-2-2-3-3-5l-6-4v-1c-1-1-4-3-5-4 0-1 0-1-1-1-1-2-5-5-7-7-1 0-2 0-3-1l-1 1-4-4h0l-3-3-3-3c4-3 9-7 12-11 4-5 8-11 10-18 2-6 4-12 5-19v-6c-1-2-5-4-5-6 1 1 3 2 4 4l1 1h0v-11c0-7 0-13 2-20h0c1-2 2-5 2-7 0-3-1-5-1-8-1-2-1-4-1-6-1-1-1-1-1-2h0l3-5-3-3-1-2v-1-2c0-1 0 0 2-1-1-2-3-3-4-5s-4-4-6-5l3-6 3 3h1c1-1 1-2 2-3h0l-1-2h0c-1 0-1-2-2-2 0-1 0-1-1-2-1 1-1 1-2 1-2 0-3-1-4-2 0-1 0-2 1-3l1-1c1-2 1-3 2-4l1-1c2-3 4-5 7-8 0 0 0-1 1-2l-1-1c0-1-1-1-1-2h-2l1-4v-1c2-2 1-5 0-7 0-1 0-1-1-2v-5h-1v-3h-4c0-3 0-4 2-6 1-1 2-3 3-4s1-2 2-3c1 1 1 1 1 2 0-1 0-2 1-3v-3h-1l-2-1v-2c0-3-1-6-3-9v-1h2c4-2 11-2 15-2l6-1 2 1c2-2 5-3 8-5h-9-1c1-1 1-2 0-3 1-2 3-4 4-7l5-9z"></path><path d="M384 255c0-1-1-1 0-2l3 2-1 1-2-1z" class="X"></path><path d="M635 252h1v1c1 2 1 3 0 4-1-1-2-3-2-4l1-1z" class="e"></path><path d="M434 529l2 1-1 1-1 12h0v-14z" class="E"></path><path d="M508 194c0 2 1 20 1 20-1-1-1-3-1-5 0-5-1-10-1-15h1zM386 520c-2-1-3-5-4-7-1-1 0-1 0-2l1-1c1 2 1 3 2 5 0 1 1 2 1 3v1h1l-1 1z" class="e"></path><path d="M460 684l1 1v3c-1 1-2 1-4 1v-3h0l-1-1 4-1z" class="C"></path><path d="M375 280c-3-1-7-2-9-5h1c1-1 3-1 5 0 1 2 2 3 3 5z" class="V"></path><path d="M360 334v-1-2c0-1 0 0 2-1h1c1 3 2 7 1 9l-3-3-1-2z" class="M"></path><path d="M388 254c-2 3-3 7-4 10v1h1c-1 0-1 0-2 1l-1-1c0-2 1-4 1-5s1-2 1-2c0-2-1-1 0-3l2 1 1-1 1-1z" class="e"></path><path d="M445 728c3 2 5 5 8 7s7 5 9 8l-1 1c-1 0-2-1-2-2-2-1-3-4-4-5h-1l-1-1h-1l1 1v1h0-1c-3-3-6-6-7-10z" class="Q"></path><path d="M455 766l1-1c2 3 3 6 3 8l-1 2-1 1c-2-1-4-2-5-4l3-6z" class="U"></path><path d="M583 260c-1 2-5 3-7 3-2 1-4 2-6 2v-1l5-1-1-31h0c1 2 0 7 1 10l1 21 6-4 1 1z" class="e"></path><path d="M372 275c7 1 14 3 20 5-3 1-5 0-8-1-2-1-3-1-5-2h-3l2 3c3 1 7 2 10 5l-13-5c-1-2-2-3-3-5zm136-8c5-1 10-1 15-1s11 0 16 1l1 1c-2 1-7 1-9 1h-5c-2-1-3-1-5-1-4-1-8-1-13-1z" class="J"></path><path d="M368 418c-2-1-5-4-6-7h0 2c3 1 7 3 8 5l-1 1c1 1 2 2 3 2l1 2c-3 0-4-2-7-3z" class="e"></path><path d="M399 238c5 4 10 8 16 11l16-3 1 1-15 3 13 9h-1l-13-8c-2-1-7 1-9 1l-1-1 7-1-14-11v-1zm227 11l2-1 1 1c1 0 0 0 1 1l1 1c-1 1-1 3-1 3-1 1-1 2-1 3v4h0v1l-1 1c-1 0-2-1-3-1h-2c-1-1-1-1-2-1l-1 1v3l-1 1c0-2-1-2 0-4h0c-2-1-3-2-4-4l2-2c1 2 0 0 0 1 1 1 3 3 4 3h0l1-1c2 0 3-1 5-2 1-2 1-3 1-5 0-1 0-2-2-3z" class="R"></path><path d="M629 257v4h0c-2 0-4 0-6-1h0 1c2-1 3-1 4-3h1z" class="e"></path><path d="M433 603l2-2 17 14h-1l-2-1v-1h-1c-1-1 0-1-1-1l-2-1v-1l-2-1v1h-1 0c1 1 2 1 3 2l-1 1-1-1-1 1c-1-1-2-1-3-2s-1-1-2-1c-1-1-2-1-3-2 0-1-1-3-1-4v-1z" class="j"></path><path d="M599 226c-2-2-4-5-5-8l-7-12c-4 1-8 4-10 7-1 2-2 4-4 5h0c0-3 0-4 1-6h1c-3 0-6 2-8 1 6-2 13-5 18-9v2h3l8 13h0c0-4 1-8 3-11v1l-3 12c1 1 3 3 4 5h-1z" class="R"></path><path d="M588 206c2-2 5-3 8-4l3 6c-2 3-3 7-3 11h0l-8-13z"></path><path d="M388 254l4-11c1-3 1-6 3-8l9-14h1l-9 14c1 1 3 2 3 3v1l-4-3c-2 7-5 13-6 19l17-4 1 1-14 4 3 3v1c-2-2-4-3-7-2-1 0-1 0-2 1-1 2-1 4-2 6h-1v-1c1-3 2-7 4-10z" class="X"></path><path d="M431 246c2-1 2-1 3 0h2 0c-2 0-2 0-3 1l1 14c4-1 19-2 22 0h2c-8 0-16 0-24 1v2l-1-1c-5 0-29 4-32 2-2-1-4-3-5-5v-1l4 4c1 1 2 2 3 2 2 0 4 0 6-1 8 0 16-2 23-2v-1c-1 0-1-1-2-1l-1-1h1 1l1 1h1c0-3 0-11-1-13l-1-1z" class="e"></path><path d="M364 262c2-5 3-10 4-15 1-1 1-2 1-3 1-1 4-1 5-2l-9 28h0c-1-3-1-5-1-8z" class="Y"></path><path d="M686 266l-1-1h1 0c1 1 0 1 1 1h1v3c1 1 1 1 2 0l1 2v1 2h-11c-1 0-4 0-5 1h-31c5-1 11-1 16-1h3 27v-3c-1 0-2 0-2-1h-1v1 1c0 1 0 1-1 1h-2l-1-1h-1c-1-1-3 0-5 0h0c-1-1-1-1-2-1l-1 1c-1-1-3-1-5-1v1h-3l-1 1c-1 0-2 0-3-1v-1c-2 0-4 1-6 0 1 0 3 0 4-1h0c2 0 3-1 4-1l-1 2 1 1c2-1 6-1 8-2l1-1c2 2 7 2 10 2v-1l3-2v-2z" class="O"></path><path d="M362 312l1-1v7l-1 2 1 1v1 2c0 2 1 5 0 6h-1c-1-2-3-3-4-5s-4-4-6-5l3-6 3 3h1c1-1 1-2 2-3h0l-1-2h2z" class="j"></path><path d="M362 312l1-1v7l-1 2v4l-1 1c-2-2-2-3-2-5l-1-3h1c1-1 1-2 2-3h0l-1-2h2z" class="a"></path><path d="M360 312h2v3l-1 1c0 1 1 1 0 3-1 0-1 1-2 1l-1-3h1c1-1 1-2 2-3h0l-1-2z" class="M"></path><path d="M362 456l7 29c-1-2-2-3-3-5-1-4-5-7-6-11h0c-1-2-1-4 0-6 0-1-1-3-1-4 1-2 2-2 3-3z" class="Z"></path><defs><linearGradient id="Ai" x1="450.486" y1="733.551" x2="429.166" y2="716.261" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#6e6e70"></stop></linearGradient></defs><path fill="url(#Ai)" d="M429 713h1l-1-2h1s1 1 2 1h1l6 9c2 2 4 5 6 7 1 4 4 7 7 10-2 0-3 1-4 1l-7-9c0-2-1-3-2-4-3-5-6-8-9-12l-1-1z"></path><path d="M432 247c1 2 1 10 1 13h-1l-1-1h-1l-13-9 15-3z"></path><path d="M366 244l9-3-1 1c-1 1-4 1-5 2 0 1 0 2-1 3-1 5-2 10-4 15v-5h1v-2-1c0-1 0-1 1-2v-2c0-2 0-2 1-3h-1c-1 1-1 2-2 3v1 2 2l-1 1v1 1 1l-1 2c0 2-2 3-2 5h-1v-3h-4c0-3 0-4 2-6 1-1 2-3 3-4s1-2 2-3c1 1 1 1 1 2 0-1 0-2 1-3v-3l2-2z" class="E"></path><path d="M360 253c1-1 1-2 2-3 1 1 1 1 1 2l-1 5c-1 2-1 4-3 6h-4c0-3 0-4 2-6 1-1 2-3 3-4z" class="d"></path><path d="M360 253c1-1 1-2 2-3 1 1 1 1 1 2l-1 5-1-1-1-1c0 1-1 1-2 2h-1c1-1 2-3 3-4z" class="T"></path><path d="M434 608c1 1 2 1 3 2 1 0 1 0 2 1h0c1 1 1 2 2 2l1 1h1c1 0 2 1 3 1v1c-2 1-2 0-3 0h0c2 2 6 3 6 6 2 1 3 2 4 3 1 0 1 1 2 2l1 1c2 1 3 3 5 4v1l-2-1h0c-2 0-6-5-7-6l-8-5c-4-3-10-7-15-8 2-1 2-1 3-2 0-2 1-2 2-3z" class="E"></path><path d="M432 611c1 1 2 2 4 3 4 2 8 7 13 8 2 1 3 2 4 3 1 0 1 1 2 2l1 1c2 1 3 3 5 4v1l-2-1h0c-2 0-6-5-7-6-3-2-5-3-8-5-4-3-10-7-15-8 2-1 2-1 3-2z" class="O"></path><path d="M615 192c1 0 1 1 2 2 1 0 1 0 2 1v-2h1v-1c2 1 3 3 5 3l3 6c0 1 1 3 1 4l5 11v1c0 1 2 3 1 4h0c-5-10-13-20-20-29z" class="T"></path><path d="M631 251v3c0 1 0 2 1 4 1 3 1 6 2 10h4v1c-5 3-60-1-71 0v-1c3-2 10 0 14 0l49 1h1c0-2 1-2 0-3l-2-3h-1l1-1v-1h0v-4c0-1 0-2 1-3 0 0 0-2 1-3z" class="X"></path><path d="M455 742l1 1v1c1 1 1 1 2 3 1 1 2 3 3 4v2h0c1 3 1 7 0 10v8l-1 1-1 1c0-2-1-5-3-8l-1 1v-10l1-1c1-3 0-4-1-6h0 1 0l-1-3v-4z" class="B"></path><path d="M455 756c3 5 5 10 5 16l-1 1c0-2-1-5-3-8l-1 1v-10z" class="D"></path><path d="M351 307c0-1 0-2 1-3l1-1c1-2 1-3 2-4l1-1c2-3 4-5 7-8 0 2-1 2-1 4-1 0-1 1-1 1h1c2 4 2 9 0 12 0 2-1 3-2 4v1c-1 0-1-2-2-2 0-1 0-1-1-2-1 1-1 1-2 1-2 0-3-1-4-2z" class="h"></path><path d="M351 307c0-1 0-2 1-3l1-1c1-2 1-3 2-4l1-1c2-3 4-5 7-8 0 2-1 2-1 4-1 0-1 1-1 1h1c-1 1-1 2-2 3-1 2-4 4-5 6l-1 1h0 1l1-1v1c0 1-1 2-1 3 1 0 1-1 2-2l3-3v1c-1 1-2 3-3 4s-1 1-2 1c-2 0-3-1-4-2z" class="D"></path><defs><linearGradient id="Aj" x1="425.576" y1="635.462" x2="427.897" y2="629.277" xlink:href="#B"><stop offset="0" stop-color="#818282"></stop><stop offset="1" stop-color="#a5a4a4"></stop></linearGradient></defs><path fill="url(#Aj)" d="M406 621l5 1c6 3 13 5 19 10 3 2 7 5 10 7h1c0 2 1 3 2 4v2 1c-12-11-26-19-41-24h1c1-1 2-1 3-1z"></path><path d="M463 270l-2-1h0c5-3 11-2 16-2h31c5 0 9 0 13 1 2 0 3 0 5 1l-47 1c-5 0-11-1-16 0z" class="l"></path><path d="M529 259l-1-1c0-2 2-5 3-7 1-4 4-4 7-6h1l2 1v1c-4 5-5 11-4 18l-7-1c-1-1-1-3-1-5z" class="H"></path><defs><linearGradient id="Ak" x1="457.66" y1="656.997" x2="447.85" y2="659.001" xlink:href="#B"><stop offset="0" stop-color="#23282c"></stop><stop offset="1" stop-color="#4c4b4c"></stop></linearGradient></defs><path fill="url(#Ak)" d="M440 639c3 2 6 5 8 6 3 3 4 6 7 8 1 2 2 5 3 7 3 5 4 9 4 14l-19-28v-1-2c-1-1-2-2-2-4h-1z"></path><path d="M641 240l11 4c2 4 4 8 5 13 0 3 0 6-1 10h0v-4h0v-4-1-2c-1-1-1 0-1-2h0l-1 2h0-1l1 1v2 7c1 0 1 0 1 1-1 1-1 1-2 1v-2l-3-8-7-15c-1-1-1-2-2-3z" class="R"></path><path d="M652 253c1 3 2 10 1 13l-3-8c0-2 1-3 2-5z" class="c"></path><path d="M643 243l1-1c1 0 3 1 3 1l4 8s0 1 1 2c-1 2-2 3-2 5l-7-15z" class="U"></path><path d="M423 186c1 0 1 1 1 2-2 2-8 8-9 12h1c3-1 7-5 9-7s4-3 7-5v1c-5 3-10 8-14 12l19 11v1l-13-7-8-3-11 18h-1l7-12c1-2 2-4 4-6v-1h0-1c-4 5-9 9-13 13-2 2-4 5-5 7-1-3 7-11 9-14l6-9c-1 0-3-1-3-2l4 1 11-12z" class="B"></path><path d="M396 500l5 11c1 1 1 0 1 1l6-3 1 1-6 3c6 11 13 20 23 29 2 2 4 4 7 6 1 0 1-1 2 0 0 0 1 1 2 1s3-2 4-2l1 1c-2 0-4 1-4 2 3 4 7 6 10 9h0c-3-2-8-4-10-7-1 1-2 1-3 2-1 0-1-1-2-1-2-1-3-3-5-4 2 0 3 0 4-1-4-3-9-4-12-8-2-4-5-6-8-10-4-5-7-11-11-16l-6 2h-1c3-1 5-2 7-3l-6-13h1z" class="O"></path><path d="M483 248h0c2-2 8-2 11-2l1 5c1 4 1 10 1 14l-10 1c-1-3 0-5 0-8-1-3-2-6-3-10h0z" class="l"></path><path d="M657 257c3 2 6 4 10 5 2 1 4 3 6 4 1 0 2 1 4 2v-1c1 0 2 1 3 1h1l-1-2c1-1 1-2 1-2l1 1 1-1c1 1 2 1 3 2v2l-3 2v1c-3 0-8 0-10-2l-1 1c-2 1-6 1-8 2l-1-1 1-2c-1 0-2 1-4 1h0c-1 1-3 1-4 1-1-2 0-3 0-4h0c1-4 1-7 1-10z" class="D"></path><path d="M656 267l2 1h2v1c2-1 2-2 4-3v1l2-1h1c-1 1-1 2-3 3-1 0-2 1-4 1h0c-1 1-3 1-4 1-1-2 0-3 0-4h0z" class="C"></path><path d="M406 251l-17 4c1-6 4-12 6-19l4 3 14 11-7 1z"></path><defs><linearGradient id="Al" x1="540.895" y1="244.034" x2="518.97" y2="260.256" xlink:href="#B"><stop offset="0" stop-color="#8e8d8d"></stop><stop offset="1" stop-color="#bbb9b8"></stop></linearGradient></defs><path fill="url(#Al)" d="M529 242c4 0 8 0 12 1h2c1 0 1 0 2 1l-4 2-2-1h-1c-3 2-6 2-7 6-1 2-3 5-3 7l1 1c0 2 0 4-1 6h-7l-1-2-1-1c0-3 0-10 2-12h1c2-3 4-5 7-8z"></path><path d="M381 230l2 1c-1 3-2 6-2 9l-6 1-9 3-2 2h-1l-2-1v-2c0-3-1-6-3-9v-1h2c4-2 11-2 15-2l6-1z" class="K"></path><path d="M367 240c-1-1-1-2 0-4 0-2 0-2 2-3h1c1 2 0 3 0 5v1l-2 1h-1z" class="F"></path><path d="M358 233h2c1 3 1 7 3 10 1-1 2-2 4-3h1c1 0 1 1 1 2l-3 1v1l-2 2h-1l-2-1v-2c0-3-1-6-3-9v-1z" class="N"></path><path d="M381 230l2 1c-1 3-2 6-2 9l-6 1-9 3v-1l3-1c0-1 0-2-1-2l2-1 5-2v-6l6-1z" class="G"></path><path d="M375 231l6-1-3 4c-1 2-2 2-3 3v-6z" class="D"></path><path d="M378 330l-1-7c-1-1-1-4 0-5h0 1c0-1-1-3 0-4v-6h0l1 19c5-2 9-4 14-7 2-1 4-1 6-2 1-1 1-1 1-2v1 1h1c1 0 1 0 2 2 0-2 0-2-1-3h1c1 0 1 0 2-1v1h-1l-1 1c2 5 7 12 10 16l5-2 1 1-6 2c3 4 15 18 19 19v-4c1 2 1 4 1 6h-1 0c-1 0-3-1-4-2-6-5-12-12-17-18-1 0-2 1-3 1 0 2 1 3 2 4l-1 1-2-4-12 5v-1h0l11-6c-2-2-2-4-3-7l-4-10c-2 0-4 2-5 3-4 1-8 3-12 5-1 0-1 1-2 1h-1v3l-1-1z" class="X"></path><path d="M400 319h1c2 1 3 4 4 6 2 3 4 6 5 9v1l-2 1-1-1c-1-3-7-13-7-16z"></path><path d="M494 245c0-2-1-2-1-3s1-1 2 0c2 1 7 0 10 0 1 1 4 1 5 1-1 2-2 3-2 5-2 5-1 12-1 17-2 1-8 0-10 0-1-5 0-10-1-15l-1-1c0-2 0-3-1-4z" class="l"></path><path d="M444 740c0-1-4-8-5-10v-1l1 1h1l7 9c1 0 2-1 4-1h1 0c1 1 1 1 1 3 1 0 1 0 1 1v4l1 3h0-1 0c1 2 2 3 1 6l-1 1v10l-3 6-2-1c2-2 2-4 3-6l-1-1c-1-1-1-2-2-2h0l-3 3c0-5-1-9-2-13 0-1 0-1-1-2 0-2 0-4-1-6v-1l-3-6 3 4 1-1z" class="c"></path><path d="M450 747c3 2 5 4 6 8l-1 1v10l-3 6-2-1c2-2 2-4 3-6l-1-1c-1-1-1-2-2-2v-1c1-2 0-4 2-7 0-1-1-5-2-7z" class="C"></path><path d="M450 762v-1c1-2 0-4 2-7 1 4 1 8 1 11l-1-1c-1-1-1-2-2-2z" class="U"></path><path d="M441 730l7 9c1 0 2-1 4-1h1 0c1 1 1 1 1 3 1 0 1 0 1 1v4l1 3h0-1 0c1 2 2 3 1 6-1-4-3-6-6-8-3-6-6-12-10-17h1z" class="O"></path><path d="M452 738h1 0c1 1 1 1 1 3 1 0 1 0 1 1v4c-2-1-3-3-5-4l-2-3c1 0 2-1 4-1z" class="D"></path><path d="M452 738h1v2c-1 1 0 1-1 1s-1 0-2 1l-2-3c1 0 2-1 4-1z" class="U"></path><path d="M407 252c2 0 7-2 9-1l13 8 1 1c1 0 1 1 2 1v1c-7 0-15 2-23 2-2 1-4 1-6 1-1 0-2-1-3-2l-4-4-3-3 14-4z"></path><path d="M437 695c0-2 1-4 1-6 0-1 0-2 1-3 0 3 0 6 1 9-1 3-1 7 0 10v1l2 3 3 5c2 2 5 4 7 5s4 1 6 1h0c-3 1-4 1-7-1h0-1c0-1-1-1-1-1l-3-2v2l-2-1 2 2c3 2 4 3 7 3h1 6 1c1 0 1 1 1 1-2 2-3 1-4 2l-2-1c-3 1-6 1-8 0-3-1-5-2-7-3l-1-1-1 1-6-9h-1c-1 0-2-1-2-1h-1l1 2h-1c-1-2-3-4-4-6l1-1c0-1 0-1 1-1h1v-1-3l1-1 6-3 2-2z" class="O"></path><path d="M437 695c0-2 1-4 1-6 0-1 0-2 1-3 0 3 0 6 1 9-1 3-1 7 0 10 0 1 0 2 1 3l1 3c-2 0-3-4-4-6 0-1-1-2-2-3 0-2 0-5 1-7z" class="g"></path><path d="M435 697l2-2c-1 2-1 5-1 7 1 1 2 2 2 3-1 2-1 3 0 5 0 1 0 1 1 2s1 3 2 4c2 3 6 5 9 7h5l1 1c-3 1-6 1-8 0-1-2-3-2-4-3-2-1-5-3-6-5-1-3-4-6-4-9 0-1-1-5-2-6-1 0-2 0-3-1l6-3z" class="K"></path><path d="M428 704v-3l1-1c1 1 2 1 3 1 1 1 2 5 2 6 0 3 3 6 4 9 1 2 4 4 6 5 1 1 3 1 4 3-3-1-5-2-7-3l-1-1-1 1-6-9h-1c-1 0-2-1-2-1h-1l1 2h-1c-1-2-3-4-4-6l1-1c0-1 0-1 1-1h1v-1z" class="U"></path><path d="M428 704l5 8h-1c-1 0-2-1-2-1h-1l1 2h-1c-1-2-3-4-4-6l1-1c0-1 0-1 1-1h1v-1z" class="H"></path><path d="M394 612l2-1 1 1v2c1 0 1 1 2 1 0 0 2 0 2-1h7l3-1v1l4 1 9 4c3 2 7 4 10 7 1 0 4 3 5 3l2 3c3 1 5 4 7 6l-1 1-2-2-3-3c2 4 5 8 6 11-2-1-5-4-8-6s-7-5-10-7c-6-5-13-7-19-10l-5-1c-1 0-2 0-3 1h-1-1-2c-1 0-1 1-2 2h0l-1-1c1-2 1-4 1-6s-1-4-3-5z" class="Q"></path><path d="M394 612l2-1 1 1v2c1 0 1 1 2 1 0 0 2 0 2-1h7v2h0-1l-1 1 4 1c0 1 1 3 1 4l-5-1c-1 0-2 0-3 1h-1-1-2c-1 0-1 1-2 2h0l-1-1c1-2 1-4 1-6s-1-4-3-5z" class="B"></path><path d="M406 621v-1h-1c-2-1-4-1-6-2h0c1-2 4-1 6-1h1l4 1c0 1 1 3 1 4l-5-1z" class="I"></path><path d="M411 614l4 1 9 4c3 2 7 4 10 7 1 0 4 3 5 3l2 3s-1 0-1-1l-28-13 1-2c-1-1-2-1-2-2z" class="V"></path><path d="M515 235c1 0 2-1 3-1h-1 4c2 1 5 1 8 2 0 1 1 3 0 4l-2 2c-2 3-4 5-6 8-2 2-2 9-2 12l1 1-1 2c-2 0-9 1-11 0h-1c0-5-1-12 1-17 0-2 1-3 2-5 1 0 3-3 3-4 1-1 2-2 2-4z" class="G"></path><path d="M515 235c1 0 2-1 3-1h-1 4c2 1 5 1 8 2 0 1 1 3 0 4l-2 2c-4-1-8-1-13-2-2 2-4 5-5 7-2 5-1 12-1 18h-1c0-5-1-12 1-17 0-2 1-3 2-5 1 0 3-3 3-4 1-1 2-2 2-4z" class="Y"></path><path d="M379 331v-3h1c1 0 1-1 2-1 4-2 8-4 12-5 1-1 3-3 5-3l4 10c1 3 1 5 3 7l-11 6h0-2c-3 3-9 5-12 7-1-2-1-4-1-5l-1-13z"></path><path d="M473 233h7c1 0 2 1 3 1 2 0 3-1 4 0h1l1-1c1-1 4-1 6-1 1 1 3 0 4 0 4 0 7 1 11 0 6 0 12 2 18 2 1 1 1 1 1 2-3-1-6-1-8-2h-4 1c-1 0-2 1-3 1 0 2-1 3-2 4 0 1-2 4-3 4s-4 0-5-1c-3 0-8 1-10 0-1-1-2-1-2 0s1 1 1 3v1c-3 0-9 0-11 2h0 0l-1 1h0l-2-3c-2-2-4-4-6-4l-2-3v-1c1-1 1-3 1-5z" class="P"></path><path d="M479 243l1-1c1 0 2-1 3-1 3 0 4-1 6-2h1v-1h1c4 3 9 1 13 1h9c0 1-2 4-3 4s-4 0-5-1c-3 0-8 1-10 0-1-1-2-1-2 0s1 1 1 3v1c-3 0-9 0-11 2h0c-1-2-3-4-4-5z" class="G"></path><path d="M473 233h7c1 0 2 1 3 1 2 0 3-1 4 0h1l1-1c1-1 4-1 6-1 1 1 3 0 4 0 4 0 7 1 11 0 6 0 12 2 18 2 1 1 1 1 1 2-3-1-6-1-8-2h-4 1c-1 0-2 1-3 1-2-2-8-1-11-1h-6-3-1-3-2v2h-3c-3-1-8 1-12 1v1c1 3 2 3 5 5 1 1 3 3 4 5h0l-1 1h0l-2-3c-2-2-4-4-6-4l-2-3v-1c1-1 1-3 1-5z" class="U"></path><path d="M671 236l2 1v1h2l4 1-1 1v5c0 3 0 8-1 11l2 2h0c0 1 0 2 1 3l1 3s0 1-1 2l1 2h-1c-1 0-2-1-3-1v1c-2-1-3-2-4-2-2-1-4-3-6-4-4-1-7-3-10-5-1-5-3-9-5-13 0 0 2 1 3 1s2 0 4 1h0l1 1 1-1 2 1v-1c3-4 5-7 8-10z" class="W"></path><path d="M675 260l2-2h2c0 1 0 2 1 3l1 3s0 1-1 2l1 2h-1c-1 0-2-1-3-1-1-1-3-2-4-3l1-4h1z" class="I"></path><path d="M652 244s2 1 3 1l1 2c0 1 1 2 3 4h0c2 0 3 1 4 2h-1l-1-1-1 1c1 1 1 1 3 2l1 1c2 2 6 4 6 6-1-1-2-1-3 0-4-1-7-3-10-5-1-5-3-9-5-13z" class="h"></path><path d="M671 236l2 1v1l-7 9c-1 0 0 0-1 1 0 0 1 1 2 1l10 7 2 2h0-2l-2 2v-3c-2-1-2-1-3-1-1-1-2-2-3-2-4-1-10-5-13-7l-1-2c1 0 2 0 4 1h0l1 1 1-1 2 1v-1c3-4 5-7 8-10z" class="J"></path><path d="M673 238h2l4 1-1 1v5c0 3 0 8-1 11l-10-7c-1 0-2-1-2-1 1-1 0-1 1-1l7-9z" class="O"></path><path d="M678 245c0 3 0 8-1 11l-10-7c4-1 4 1 7 3h1l1 1 1-1c0-2-2-3-2-5 1-1 1-1 3-2z" class="K"></path><defs><linearGradient id="Am" x1="521.765" y1="213.13" x2="497.782" y2="247.903" xlink:href="#B"><stop offset="0" stop-color="#1c131a"></stop><stop offset="1" stop-color="#28322e"></stop></linearGradient></defs><path fill="url(#Am)" d="M474 228l83 1h-1c-3 1-6 0-9 0l-3 9c0 1 2 2 2 3v2l-1 1c-1-1-1-1-2-1h-2c-4-1-8-1-12-1-3 3-5 5-7 8h-1c2-3 4-5 6-8l2-2c1-1 0-3 0-4s0-1-1-2c-6 0-12-2-18-2-4 1-7 0-11 0-1 0-3 1-4 0-2 0-5 0-6 1l-1 1h-1c-1-1-2 0-4 0-1 0-2-1-3-1h-7l1-5z"></path><path d="M495 232c2-1 9 0 12 0 4-1 7 0 11 0h11l1-1c0 1 1 1 1 2h1c2-1 4 0 6 1h1l1-1h2c1 1 1 3 2 5 0 1 2 2 2 3v2l-1 1c-1-1-1-1-2-1h-2c-4-1-8-1-12-1-3 3-5 5-7 8h-1c2-3 4-5 6-8l2-2c1-1 0-3 0-4s0-1-1-2c-6 0-12-2-18-2-4 1-7 0-11 0-1 0-3 1-4 0z" class="Q"></path><path d="M529 242c1-2 1-4 1-6 1 1 1 0 1 1l1 2 2-1c1-1 4-1 5 0l2 2v2c1-1 1 0 2-1v1h1 1l1-1v2l-1 1c-1-1-1-1-2-1h-2c-4-1-8-1-12-1z" class="S"></path><path d="M534 238c1-1 4-1 5 0l2 2h-5v-2h-2z" class="Y"></path><path d="M456 685l1 1h0v3h-1c2 1 2 1 3 1h2s1 1 1 2 0 1-1 2h0c-2 2-4 1-6 3l1 2h1l2 2h3 0v1h-1-2c1 2 1 2 1 3-2 2-4 2-7 1h-1c0 1-1 0 0 1v1c1 2 2 2 4 3 1 0 0 0 1-1h2l-1-2 1-1v1 1l1 1-1 1v1c0 1 0 1-1 1s-2 0-3 1v-1h-1l1 1h0c1 1 0 1 1 1s1 1 2 0c2 0 2 0 3 1v1h-4c1 1 3 1 4 1l1 1c-1 1-1 0-2 1h-1-1c-2 0-4 0-6-1s-5-3-7-5l-3-5-2-3c-1-5 0-8 2-12 2-2 3-4 5-5 2-2 4-3 6-3l3-1z" class="C"></path><path d="M447 700v1c2-4 4-7 8-9l1 1h-1c-2 1-6 5-6 8-1 2-1 6 0 9 2 2 4 5 6 6h0c-2 0-3 0-5-1l-1 1c1 0 3 2 3 3-2-1-5-3-7-5l2-2c-1-4-2-8 0-12z" class="Q"></path><path d="M456 685l1 1h0v3h-1l-1 1c-2 1-4 3-6 5h0c-1 1-1 3-2 5-2 4-1 8 0 12l-2 2-3-5-2-3c-1-5 0-8 2-12 2-2 3-4 5-5 2-2 4-3 6-3l3-1z" class="W"></path><path d="M456 685l1 1h0v3h-1l-1 1c-2 1-4 3-6 5h0-1 0c0-1 1-3 2-4v-1c0-1-2-1-3-1 2-2 4-3 6-3l3-1z" class="b"></path><path d="M456 685l1 1h0v3h-1l-1 1h-2-1c1-1 2-2 2-3l-1-1 3-1z" class="Q"></path><path d="M442 694c2-1 4-1 6-2-2 5-4 10-4 15l-2 2-2-3c-1-5 0-8 2-12z" class="h"></path><defs><linearGradient id="An" x1="466.545" y1="248.287" x2="481.192" y2="265.137" xlink:href="#B"><stop offset="0" stop-color="#c7c5c4"></stop><stop offset="1" stop-color="#f0eeeb"></stop></linearGradient></defs><path fill="url(#An)" d="M464 240l1-1c2 0 5-1 7 0l2 3c2 0 4 2 6 4l2 3h0c2 6 3 10 2 16l-18 1c0-6 0-11-4-15l-3-3c-1-1-3-2-4-4l2-3h0 2l1-1h4z"></path><path d="M464 240l1-1c2 0 5-1 7 0l2 3c2 0 4 2 6 4l-2 1v-1l-1-1c-1 1-2 1-4 1-1-2-1-2-4-2l-1 1c1 1 2 2 2 3v1c0 1 0 1-1 0-1 1-2 1-3 1h-2l-2 1-3-3c-1-1-3-2-4-4l2-3h0 2l1-1h4z" class="T"></path><path d="M457 241h2l1-1h4s0 1-1 2c-1 0-2 0-3 1-1-1-2-1-3-1v-1h0z" class="P"></path><path d="M464 240l1-1c2 0 5-1 7 0l2 3c-2 0-8-1-9 0h2c1 0 1 0 2 1-2 0-4 0-6-1 1-1 1-2 1-2z" class="g"></path><path d="M459 248l2-3c1 0 3 1 4 0 2 1 3 3 5 3v1c0 1 0 1-1 0-1 1-2 1-3 1h-2l-2 1-3-3z" class="F"></path><path d="M427 613h1 1c5 1 11 5 15 8 3 2 5 3 8 5 1 1 5 6 7 6 1 1 2 2 3 4v1h0v1c0 1 0 1-1 2h0c0 1 1 2 0 3l-1-1h0c0-1-1-2-2-3h0 0c0 1 1 2 1 3l2 2v1 1h0l-1-1-1-1c-1 0 0 0-1 1-1-1-1-1-1-3l-1 1v1l1 1-1 1c1 1 2 1 2 3l-2-2 1 2v2 1h-1 0c-1-3-3-5-5-7 1 2 2 3 3 4l1 4c-3-2-4-5-7-8-1-3-4-7-6-11l3 3 2 2 1-1c-2-2-4-5-7-6l-2-3c-1 0-4-3-5-3-3-3-7-5-10-7v-2l-1-2c2 0 3-1 4-2z" class="B"></path><path d="M443 626c1 1 2 2 3 2 2 0 3 1 5 3l-1 1 3 3h0c-3-1-5-4-8-5v1h0l-1-1h-1l-1-2c0-1 0-1 1-2z" class="C"></path><defs><linearGradient id="Ao" x1="461.642" y1="633.315" x2="442.904" y2="627.382" xlink:href="#B"><stop offset="0" stop-color="#1c1f22"></stop><stop offset="1" stop-color="#474648"></stop></linearGradient></defs><path fill="url(#Ao)" d="M444 621l8 5c1 1 5 6 7 6 1 1 2 2 3 4v1h0v1c0 1 0 1-1 2h0c0 1 1 2 0 3l-1-1h0c0-1-1-2-2-3h0c-1-3-5-6-7-8s-3-3-5-3c-1 0-2-1-3-2 0 0-2-2-3-2h2l1 1h1v-1l1-1-1-2z"></path><path d="M427 613h1 1c5 1 11 5 15 8l1 2-1 1v1h-1l-1-1h-2c1 0 3 2 3 2-1 1-1 1-1 2v1l2 2h-1l-2-2v1l-1-1h-1c-1 0-4-3-5-3-3-3-7-5-10-7v-2l-1-2c2 0 3-1 4-2z" class="Y"></path><path d="M425 617c5 0 8 1 11 4l2 1-1 1c-1 0-3-1-4-2-3 0-6-2-8-4z" class="S"></path><path d="M424 617h1c2 2 5 4 8 4 1 1 3 2 4 2 1 2 2 3 3 3h0l-1-2h1c1 0 3 2 3 2-1 1-1 1-1 2v1l2 2h-1l-2-2v1l-1-1h-1c-1 0-4-3-5-3-3-3-7-5-10-7v-2z" class="D"></path><path d="M430 722c1 2 3 5 5 6l1-1c1 1 1 2 2 3 2 3 5 7 6 10l-1 1-3-4 3 6v1c1 2 1 4 1 6 1 1 1 1 1 2 1 4 2 8 2 13l3-3h0c1 0 1 1 2 2l1 1c-1 2-1 4-3 6-2 0-3 1-5-1l-1-1c-1 0-1 0-2-1h-1c-3-3-4-9-7-12-2-2-3-4-5-6-4-7-5-13-6-21 2 1 2 2 4 2v-3l-1-2 2-2h1l1 1h0v-2-1z" class="L"></path><path d="M445 770c0-3 1-6 1-8 0-4-2-7-1-10 1 4 2 8 2 13l3-3h0c1 0 1 1 2 2l1 1c-1 2-1 4-3 6-2 0-3 1-5-1z" class="Y"></path><path d="M427 731v-3l-1-2 2-2c2 3 3 7 5 9v-1c-1-2-2-3-3-5v-1c2 1 3 3 4 5l2 4c3 4 10 25 9 30-1 0-1 1-1 1-1-1-1-3-1-4v-1h-1c-1-1-1-2-1-3 0-3-1-4-2-7h0c-2-5-6-11-10-16 0-1-1-2-2-4z" class="i"></path><path d="M423 729c2 1 2 2 4 2 1 2 2 3 2 4 4 5 8 11 10 16h0c1 3 2 4 2 7 0 1 0 2 1 3h1v1c0 1 0 3 1 4l-1 2h-2c-3-3-4-9-7-12-2-2-3-4-5-6-4-7-5-13-6-21z" class="h"></path><path d="M429 735c4 5 8 11 10 16h0l-1 1 2 4h0v1c-2-1-2-3-3-4-2-4-6-8-7-12 0-2-1-2-1-4h-1l1-2z" class="W"></path><path d="M355 424c1 1 1 1 1 2s2 4 2 5c2 3 3 7 4 10 0 3 1 5 1 8-1 2 0 5-1 7h0c-1 1-2 1-3 3 0 1 1 3 1 4-1 2-1 4 0 6h0c1 4 5 7 6 11l-6-4v-1c-1-1-4-3-5-4 0-1 0-1-1-1-1-2-5-5-7-7-1 0-2 0-3-1l-1 1-4-4h0l-3-3-3-3c4-3 9-7 12-11 4-5 8-11 10-18z" class="O"></path><path d="M352 445c-1-1-2-1-2-3 2 0 4-3 5-4l-1-2c0-1 1-2 1-2v-1-5c0-1 0-1 1-2 0 1 2 4 2 5v1 2 3 1l-1-1-1 1 2 2c0 1-2 1-2 2-1 1-2 2-2 3h-2z" class="Q"></path><path d="M358 431c2 3 3 7 4 10 0 3 1 5 1 8l-7-2-4-2h2c0-1 1-2 2-3 0-1 2-1 2-2l-2-2 1-1 1 1v-1-3-2-1z" class="i"></path><path d="M356 447l2-2 1-1c1-1 2-1 3-3 0 3 1 5 1 8l-7-2z" class="H"></path><path d="M339 459c2 0 2 1 3 2h2c-1-1-1-2-1-2v-3c1-1 2-1 2-2l1-1c1 0 1 0 2-1v-2l2 1c0-1 1-2 2-3l2 1c2 1 5 1 6 3 1 1 2 3 2 4h0c-1 1-2 1-3 3 0 1 1 3 1 4-1 2-1 4 0 6h0c1 4 5 7 6 11l-6-4v-1c-1-1-4-3-5-4 0-1 0-1-1-1-1-2-5-5-7-7-1 0-2 0-3-1l-1 1-4-4z" class="U"></path><path d="M339 459c2 0 2 1 3 2h2c-1-1-1-2-1-2v-3c1-1 2-1 2-2l1-1c1 0 1 0 2-1v-2l2 1c0-1 1-2 2-3l2 1-1 1v2c-1 0-1 0-2-1l-1 1 2 1c0 1-1 1-2 1h-1l1 2h-1-2v2h-2v1l1 1h-1v1l2 2c-1 0-2 0-3-1l-1 1-4-4z" class="f"></path><path d="M360 475c0-3-4-8-6-10v-3c-1-1 1-2 1-3l1-1v-1l1-1h0l-1-2c1 0 2-1 2-1 1-1 1-1 2-1 1 1 2 3 2 4h0c-1 1-2 1-3 3 0 1 1 3 1 4-1 2-1 4 0 6h0c1 4 5 7 6 11l-6-4v-1z" class="h"></path><path d="M457 217h63 25 14c3 1 7 3 10 5h-29l-110 1c2-2 6-4 9-5h0c6-2 13-1 18-1z" class="o"></path><path d="M599 209c3 3 5 6 7 10 1 1 2 2 4 3 3 3 6 8 10 11h2v-1c1 1 1 4 3 4 1 3 2 6 3 8l2-1v1c0 2-1 3 0 6-1-1 0-1-1-1l-1-1-2 1c2 1 2 2 2 3 0 2 0 3-1 5-2 1-3 2-5 2l-1 1h0c-1 0-3-2-4-3 0-1 1 1 0-1l-2 2-9 6h-1c3-2 6-4 8-7-3-1-7-2-10-3-2 0-5-1-7-2l-1 1c-4 3-8 5-12 7l-1-1c4-2 9-4 12-7l1-1c5-2 9-6 14-8l-10-17h1c-1-2-3-4-4-5l3-12z" class="I"></path><path d="M622 232c1 1 1 4 3 4l3 8-1 1c-3-3-3-7-5-10-1-1 0-1 0-2v-1z" class="X"></path><path d="M609 244h1c1 3 2 7 3 10v1c-4 0-7-1-11-2l-6-2 13-7zm13 15h-1c-1-1-3-2-3-3l2-3h0c-1 0-2 1-3 1-3-1-5-8-6-12l10-7h0l4 9c0 1 1 3 1 3 0 2-5 4-5 6 2-2 4-3 5-4 2 1 2 2 2 3 0 2 0 3-1 5-2 1-3 2-5 2zm-23-50c3 3 5 6 7 10 1 1 2 2 4 3v1c2 3 6 8 9 11-3 3-6 6-9 8v-1c-3-5-7-10-10-15-1-2-3-4-4-5l3-12z"></path><path d="M641 240c-1-1-3-1-4-2v-3c-2-7-6-6-11-9h10c12 1 24 2 36 6 0 1 3 2 3 2 4 2 7 3 10 6 6 5 10 12 17 15 3 2 6 2 9 2 3 1 10 0 13 1 0 2-1 3-2 4h0l1 2c-1 0-1 1-2 2 0 2 0 4-1 6-1-1 0-1-1-1h-1l-1-1h-1c-1-1-1-2-2-2h-2-1l-1-1c-2-1-3 0-4-1h-2v-1h-2c-1-1 0-1-1-2 0-1-2-2-3-3-1-2-2-3-2-5-5-5-9-10-15-14l-2-2-4-1h-2v-1l-2-1c-3 3-5 6-8 10v1l-2-1-1 1-1-1h0c-2-1-3-1-4-1s-3-1-3-1l-11-4z" class="m"></path><path d="M646 234h1l1 2c0 1 1 1 2 2l1 1c-2 0-4-1-6-2 0-1 0-2 1-3z" class="I"></path><path d="M696 255l6 3v2h0-4c-1-2-2-3-2-5z" class="U"></path><path d="M645 234c2-2 0-3 4-3l2 1c0 1 0 1-1 2 0 1 1 2 1 3h0l-1 1c-1-1-2-1-2-2l-1-2h-1-1z" class="E"></path><path d="M645 237c-2 0-5-1-6-3 0-1-1-2 0-4 2 0 4 0 5 1 0 1 0 1 1 2v1h1c-1 1-1 2-1 3zm6 2l1-1c1-2 1-3 0-5 0-1 0 0 1-1l6 1c-2 2-3 6-5 8-1 0-2-1-3-2z" class="B"></path><path d="M702 258c4 1 7 2 11 2l-2 2c0 2 0 2-1 3h0c-1-1-1 0-2-1v1l-1 1h-1 0-2v-1h-2c-1-1 0-1-1-2 0-1-2-2-3-3h4 0v-2z" class="L"></path><path d="M698 260h4 0c1 0 1 0 2 1 1 0 1 1 2 1 1-1 1 0 2 0h1c-1 1-2 2-3 4h0-2v-1h-2c-1-1 0-1-1-2 0-1-2-2-3-3z" class="f"></path><path d="M713 260c3 1 7 0 9 2h0l1 2c-1 0-1 1-2 2 0 2 0 4-1 6-1-1 0-1-1-1h-1l-1-1h-1c-1-1-1-2-2-2h-2-1l-1-1c-2-1-3 0-4-1h0 1l1-1v-1c1 1 1 0 2 1h0c1-1 1-1 1-3l2-2z" class="D"></path><path d="M661 246l-6-3c1-4 4-7 6-10l10 3c-3 3-5 6-8 10v1l-2-1z" class="I"></path><path d="M659 240h1c1 1 1 2 1 3h-2c-1 0-1-1-2-1l1-1 1-1z" class="X"></path><path d="M425 229h-2v-1c1-1 4-4 5-4h5c10-1 20-1 30-1l63 1 33-1c4 0 9 0 13 1 2 0 5 3 6 5h-21l-83-1-1 5c0 2 0 4-1 5v1c-2-1-5 0-7 0l-1 1h-4l-1 1h-2c1-3 1-5 0-7l-1-1v-3l1-1h-32 0z" class="o"></path><path d="M425 229l49-1-1 5c0 2 0 4-1 5v1c-2-1-5 0-7 0l-1 1h-4l-1 1h-2c1-3 1-5 0-7l-1-1v-3l1-1h-32 0z" class="O"></path><path d="M457 234c1 0 2 3 3 5v-6c1 1 1 2 1 3v1h1v-3h1c2-1 4-1 6-1l2 2 1 3v1c-2-1-5 0-7 0l-1 1h-4l-1 1h-2c1-3 1-5 0-7z" class="S"></path><path d="M463 234c2-1 4-1 6-1l2 2c-2 1-7 2-8 2v-3z" class="Q"></path><path d="M408 575l2-1c1 1 2 1 3 2 1 2 1 4 2 6 2 3 5 6 7 9 3 3 6 5 8 8 2 1 3 2 5 2l-2 2v1c0 1 1 3 1 4-1 1-2 1-2 3-1 1-1 1-3 2h-1-1c-1 1-2 2-4 2l1 2v2l-9-4-4-1v-1l-3 1h-7c0 1-2 1-2 1-1 0-1-1-2-1v-2l-1-1-2 1-4-5-1-1c0-2 4-6 6-8l2-2v-1l1-1v-3l-1 1h-2l1-3h1c1 1 0 0 1 0s1 0 2-1l-1-1v-1l2-1c1-2 3-3 5-5l1-3 1-2z" class="K"></path><path d="M410 595l9 5-2 2c-1 0-2 0-3-1v1h-1c0-2-1-2-2-3l1-1c-1-1-1-2-2-3h0z" class="M"></path><path d="M397 612h3c1-1 2-1 4-1h1l1-1h8v-1h10c0 1 0 1 1 0 1 0 2 1 3 0 2-1 3-3 4-5h1c0 1 1 3 1 4-1 1-2 1-2 3-1 1-1 1-3 2h-1-1c-1 1-2 2-4 2l1 2v2l-9-4-4-1v-1l-3 1h-7c0 1-2 1-2 1-1 0-1-1-2-1v-2z" class="X"></path><path d="M415 615c3-3 8-2 12-2-1 1-2 2-4 2l1 2v2l-9-4z" class="C"></path><path d="M408 575l2-1c1 1 2 1 3 2 1 2 1 4 2 6 2 3 5 6 7 9 3 3 6 5 8 8 2 1 3 2 5 2l-2 2h-1 0l-1-1c-1 0-1 0-2-1l-3-2c-2-1-3-2-5-4l-1 1c-1-1-2-3-3-4h-2c-1-1-1-2-2-2h-1c-1-1-2-3-3-4h-1-1c0-1 0-1 1-2h1l-1-1c0-2 1-3 2-4h0l-1-2-1 2-1-2 1-2z" class="U"></path><path d="M408 575l2-1c1 1 2 1 3 2 1 2 1 4 2 6 2 3 5 6 7 9-2 0-3-3-5-4l-2-1c-2-1-4-4-5-6 1-1 1-1 1-2h1l-2-2-1 1-1 2-1-2 1-2z" class="j"></path><path d="M407 577l1 2c-1 2-6 8-5 10l1 1 6 5h0c1 1 1 2 2 3l-1 1c1 1 2 1 2 3h1v-1c1 1 2 1 3 1l2-2 2 1c2 2 4 2 6 4l-1 1-1-1c-1 0-3 0-5-1h-7c-1 0-3 1-3 1-1 0-2-1-2-1h0c-1 2-2 3-5 4-2 1-5 1-7 3l-2 1-4-5-1-1c0-2 4-6 6-8l2-2v-1l1-1v-3l-1 1h-2l1-3h1c1 1 0 0 1 0s1 0 2-1l-1-1v-1l2-1c1-2 3-3 5-5l1-3z" class="F"></path><path d="M404 590l6 5h0c1 1 1 2 2 3l-1 1c-1-1-3-3-5-3-1-1-2-2-4-3 1-1 1-2 2-3z" class="J"></path><path d="M395 598l1 1h1c-1 1-1 1-1 2-1 0-1 1-2 2l1 1h1c1-1 2-1 3-1s3-1 3-2l-1-1s0-1-1-1l1-2c3 0 5 2 6 4 1 0 1 1 1 3-1 2-2 3-5 4-2 1-5 1-7 3l-2 1-4-5-1-1c0-2 4-6 6-8z" class="R"></path><path d="M679 239l2 2c6 4 10 9 15 14 0 2 1 3 2 5 1 1 3 2 3 3 1 1 0 1 1 2h2v1h2c1 1 2 0 4 1l1 1h1 2c1 0 1 1 2 2h1l1 1h1c1 0 0 0 1 1l-1 1 1 1c0 2-1 4 0 6v1c-1 0-1 0-2 1h-1l-1 1c2 1 2 0 3 1h-2 0l1 2 1-1v1 1h0l-1 1c1 0 2 1 2 1v4l2 4c0 3 1 4 0 6h-3c-1 0-3 1-4 2l-1-1v-1h-3c-2-1-4-1-6-1l-4-1-2-2v-1l1-1h1v-2l1-1c-1-2-2-3-2-4v-2-1l1-1c-1-1-1-2-1-4l2-1c1 0 1 0 2-1-1-1-2-1-3-2 0-1 0-1 1-2l2 1v-1h-2-1l-1-1 2-1h-1l-2-1h-1-2c-1 0-2-1-2-1l-1-1v1l-2-1-1-2c-1 1-1 1-2 0v-3h-1c-1 0 0 0-1-1h0-1l1 1c-1-1-2-1-3-2l-1 1-1-1-1-3c-1-1-1-2-1-3h0l-2-2c1-3 1-8 1-11v-5l1-1z" class="Q"></path><path d="M679 239l2 2v4 1h-1v-5l-2-1 1-1z" class="B"></path><path d="M683 264c0-1 0-1 1-2l-1-1v-6-10h2c0 1 0 1 1 2l-2 2v7l1 1h1v-6l-1-1 1-1c1 1 1 1 2 3 0 1 0 2-1 2v3l1 1h1v-1h0 1l1 1h1v1h-1l-1 1h3c-1 1-1 1-2 1v1c1 0 0 0 1 1v1h1l2-1v1 1h1l-1 2c-1 0-1 0-2-1l-1 2 1 1-1 1c-1-1-1-1-2-1-1 1-1 1-2 0v-3h-1c-1 0 0 0-1-1h0-1l1 1c-1-1-2-1-3-2z" class="E"></path><path d="M701 297v-2l1-1c-1-2-2-3-2-4v-2-1l1-1c-1-1-1-2-1-4l2-1h0c1 2 4 5 6 7l1 1v1 3c1 0 0 0 1 1h1v2h1v-3-1c1 1 0 2 1 3 0 1 1 2 2 3v1l-2-1v1c-3 0-6 0-9-1l-1-1h-2z" class="C"></path><path d="M703 297c1-1 2-1 2-2-1-2-3-4-4-6h2c1 1 2 1 2 2s0 2 1 3c0 2 0 3-2 4l-1-1z" class="I"></path><path d="M423 186l23-21-3-4 5 4c5-1 10-5 15-7 3-1 6-2 9-4 0-1 0-1-1-2h0l1-1 2 3c5-1 10-3 15-3 5-1 11 0 16-2h0c2 1 2 1 3 2-4 0-8 1-11 0-2 0-4 0-6 1l3 9c-1 1-2 1-3 2h1l-8 4 4 5h-1l-4-6-20 9-2 10h0l6 9 8 12h-1c-6-7-9-15-14-21l-1-1c-1-2-3-5-4-6-8 3-15 7-23 11v-1c-3 2-5 3-7 5s-6 6-9 7h-1c1-4 7-10 9-12 0-1 0-2-1-2z" class="C"></path><path d="M462 174c0 3-1 6-2 9l-1-1c-1-1-2-3-3-5l6-3zm-38 14l24-20 6 9-22 11c-3 2-5 3-7 5s-6 6-9 7h-1c1-4 7-10 9-12z"></path><path d="M491 163c-2 0-5 1-7 2v1l-2-1c-1 1-3 2-5 3-4 2-9 4-13 5-1-1 0-2 0-3l1-8c-1 2-1 7-2 10v1c-1 1-5 3-7 4-3-2-5-7-7-10l15-7c3-2 7-3 10-4h1 1c4-2 11-3 15-4l3 9c-1 1-2 1-3 2z"></path><path d="M397 624h0c1 1 2 1 3 2h0l-1 1h1l1-1c2 0 3 1 5 2h0c1 1 1 0 1 1 1 0 1 1 2 1s1 1 2 1c0-1 0 0 1-1h0v2c1 2 3 3 5 4l1 1h1s1 0 1 1c1 0 2 1 3 2h1l3 2 3 3c2 1 4 2 5 3l3 3v-1c-2-1-3-4-5-5-1 0-1 0-1-1v-1c-3-2-6-3-9-5-1 0-2-1-2-1v-1l1-1c1 0 1 0 3 1h1l2 1c1 1 2 1 3 1 1 1 2 1 3 2h0c2 2 4 4 6 5l-1 1-3-3-1-1h-1c-1-1-2-1-3-2l-1-1h-2l-1-1-2-1h-1l-2-1c1 1 2 1 3 2h2v1c2 1 4 2 6 4 2 0 3 2 4 3 2 1 4 3 6 5l3 3c0 1 0 1 1 2s2 5 4 6h0l1 3v1 1 2l1 1h0l1-1h0c1 1 1 2 1 3v1c0 1 0 1 1 3h0 0c1 1 2 1 2 1h1l-1 1h0-2 1c1 0 2 0 3 1l2 1v2l-1 1-1 1h0l-4 1-3 1c-2 0-4 1-6 3-2 1-3 3-5 5-2 4-3 7-2 12v-1c-1-3-1-7 0-10-1-3-1-6-1-9-1 1-1 2-1 3 0 2-1 4-1 6l-2 2-6 3-1 1v3 1h-1c-1 0-1 0-1 1l-1 1c-1-5-2-8-1-12 3-7 6-12 7-19v-2c1-4 1-7 0-11-1-3-3-6-5-9l-3-3c0-2-7-5-9-7h-1l-7-7c-1-2-2-3-2-4s-3-3-3-4c-1 0-2-1-2-1l-1-1c0-1-1-2-1-3z" class="B"></path><path d="M432 643c2 1 3 2 4 3 5 4 9 8 12 14h0v2c1 3 3 6 4 9-3-2-6-9-8-12l-5-8-1-1c-2-1-3-4-5-5-1 0-1 0-1-1v-1z" class="Q"></path><path d="M417 637c1 1 2 1 3 2h1l8 6c1 1 1 1 2 1 1 1 3 2 4 3 4 4 8 9 11 14l1 2c2 5 3 10 4 15l-1 1c-2-6-7-13-10-18l-2-3c-1-2-1-3-2-4-2-3-5-6-7-9-4-3-8-6-12-10z" class="b"></path><defs><linearGradient id="Ap" x1="458.894" y1="679.891" x2="438.082" y2="673.904" xlink:href="#B"><stop offset="0" stop-color="#040506"></stop><stop offset="1" stop-color="#313335"></stop></linearGradient></defs><path fill="url(#Ap)" d="M437 663l-2-6 1-1c1 1 1 2 2 4l2 3c3 5 8 12 10 18v1c1 0 1 0 1-1 1-1 1-1 1-2 1-2 1-4 0-7v-1h1c0 1-1 2 0 4 0 1 1 2 1 4v1c2 1 5 1 7 3l-1 1h0l-4 1-3 1c-2 0-4 1-6 3-2 1-3 3-5 5-2 4-3 7-2 12v-1c-1-3-1-7 0-10 1-1 0-1 1-2h0l5-5 1-2h1l-3-2c-5-3-5-7-7-12h-1c0-3 0-5-1-6l-1-2 2-1z"></path><path d="M437 663l-2-6 1-1c1 1 1 2 2 4l2 3c-1 4 1 6 3 9 0 1 1 3 2 4s1 2 2 4v1 3h-2c-5-3-5-7-7-12h-1c0-3 0-5-1-6l-1-2 2-1z" class="B"></path><path d="M443 672c0 1 1 3 2 4 0 1 0 3-1 4l-1 1-1-7 1-2z" class="E"></path><path d="M437 663l-2-6 1-1c1 1 1 2 2 4l2 3c-1 4 1 6 3 9l-1 2c-2-4-5-8-5-11z" class="D"></path><defs><linearGradient id="Aq" x1="411.096" y1="647.508" x2="426.587" y2="639.902" xlink:href="#B"><stop offset="0" stop-color="#110f11"></stop><stop offset="1" stop-color="#343737"></stop></linearGradient></defs><path fill="url(#Aq)" d="M397 624h0c1 1 2 1 3 2h0l-1 1h1c1 2 1 2 2 3l1-2c2 1 4 4 6 5 2 2 4 3 6 4h1 1c4 4 8 7 12 10 2 3 5 6 7 9l-1 1 2 6-2 1 1 2h-1c-1 0-1-2-2-3h-2c-1-3-3-6-5-9l-3-3c0-2-7-5-9-7h-1l-7-7c-1-2-2-3-2-4s-3-3-3-4c-1 0-2-1-2-1l-1-1c0-1-1-2-1-3z"></path><path d="M430 653c2 2 3 4 4 7 0 1 1 3 1 4l1 2h-1c-1 0-1-2-2-3h-2c-1-3-3-6-5-9 2 0 2 1 3 2h1v-3z" class="F"></path><path d="M404 633c8 5 20 11 26 20v3h-1c-1-1-1-2-3-2l-3-3c0-2-7-5-9-7h-1l-7-7c-1-2-2-3-2-4z" class="N"></path><path d="M431 663h2c1 1 1 3 2 3h1c1 1 1 3 1 6h1c2 5 2 9 7 12l3 2h-1l-1 2-5 5h0c-1 1 0 1-1 2-1-3-1-6-1-9-1 1-1 2-1 3 0 2-1 4-1 6l-2 2-6 3-1 1v3 1h-1c-1 0-1 0-1 1l-1 1c-1-5-2-8-1-12 3-7 6-12 7-19v-2c1-4 1-7 0-11z" class="h"></path><path d="M437 677c1 1 2 7 2 8v1c-1 1-1 2-1 3 0 2-1 4-1 6l-2 2-1-1-2 1c-1 0-2 1-3 0l1-1c1 0 2 0 3-1h-1-2l2-1c1-1 1-2 2-3h0c-1 0-1 1-2 1l1-3h0c3-3 3-8 4-12z" class="S"></path><path d="M431 663h2c1 1 1 3 2 3h1c1 1 1 3 1 6h1c2 5 2 9 7 12l3 2h-1l-1 2-5 5h0c-1 1 0 1-1 2-1-3-1-6-1-9v-1c0-1-1-7-2-8-1 4-1 9-4 12h0-1l-3 3v-1-1c1-1 1-2 2-2l2-9v-2l-2-1v-2c1-4 1-7 0-11z" class="W"></path><path d="M431 663h2c1 1 1 3 2 3h1c1 1 1 3 1 6v-1c-1-1-1-1-1-2h-1v3h0c-1-1 0-2-2-4v2c1 2 1 7 0 9v-2l-2-1v-2c1-4 1-7 0-11z" class="H"></path><path d="M437 677v-2c2 2 2 5 3 7 1 3 4 5 6 6l-5 5h0c-1 1 0 1-1 2-1-3-1-6-1-9v-1c0-1-1-7-2-8z" class="c"></path><path d="M505 149l2-1 1 1c3 1 7 1 11 0 1 0 3 1 5 0h0c2 1 4 1 6 1l12 4c13 4 24 12 35 20 2 1 3 1 5 0h0c1 1 1 2 1 4l-1 1c-3 0-3-1-5-2l-6-6c-4-2-8-3-11-5l1 2c5 4 7 11 12 15 3 3 6 5 10 7l12 9c1 0 2 0 3-1-3-8-12-12-15-20 6 4 10 8 14 13 1 1 4 5 4 5h2c2-1 5-1 7-1-2 1-6 2-7 4l13 18c2 4 4 7 6 10v1l3 8c-2 0-2-3-3-4v1h-2c-4-3-7-8-10-11-2-1-3-2-4-3-2-4-4-7-7-10v-1l-3-6c-3 1-6 2-8 4h-3v-2l-12-19-13-18-7 3c-4 2-7 2-11 1l-1 1c5 1 10 1 14 4h-1l-2-1c-2 0-3-1-5-1-4-1-7-1-11-3h0l6 16 4 7s1 2 0 2v1l-9-19c-1-1-1-3-2-4-8 2-19 5-24 11v5c2 3 7 6 9 9-1 0-2-2-3-2l-6-6c-2 1-2 2-3 3h-1v-4-1l-6-4c-1 6 1 10 1 16 1 5 1 10 1 15-2-4-2-8-3-12l-2-14-4 3h0l4-4c0-2 0-5-1-7-2-3-6-6-9-9v-1l8 8-2-17h-2-1c1-1 2-1 3-2l-3-9c2-1 4-1 6-1 3 1 7 0 11 0-1-1-1-1-3-2z" class="I"></path><path d="M572 184l10 7 2 2c2 1 8 6 9 8l-2 1-4 2c-5-6-9-13-15-20zm38 38l-1-1c-3-7-10-11-11-19l2-2c6 7 11 14 16 21 2 3 5 7 6 11v1h-2c-4-3-7-8-10-11zm-104-61h2c1 1 1 19 0 21 0 2-2 2-3 3-2 0-3-1-4-2-4-3-4-16-5-21l10-1zm17-10c14 4 26 6 37 15-1 0-2 0-2 1-5 2-8 4-13 4h-2c-2-1-4-1-6-2-4-1-5-9-7-12 0-1-1-3-2-3 1 3 4 8 4 11h-1l-8-14z"></path><path d="M508 151h15c2 4 6 9 7 14 1 2 3 5 4 7l-12 5c-4 2-9 5-12 5l-1-22c6 1 13 2 19 5l1-1c-4-3-12-4-16-4l-4-1s-1-5-1-6v4 2l-12 2c0-2-1-5-2-7v-2l3-1c3 1 7 0 11 0z"></path><path d="M432 408l2 1v65h1c2 1 4 2 5 4-1-1-3-2-5-2-1 1-1 4-1 5 4 4 8 8 12 11v1l-12-10v14c2-1 5-2 7-3h0l-7 5 1 29 17 14h0l-16-12-2-1-1-1-19-18-1-1c-1-1-3 0-4 1l-1-1-6 3c0-1 0 0-1-1l-5-11h-1l6 13c-2 1-4 2-7 3h1l3 12c0 1 0 3 1 3 0 1 1 1 1 1 7 4 16 6 21 13h0l-1-1c-4-5-14-8-21-11 2 4 3 8 5 11 2 5 6 9 9 13l13 17h0l-14-15c-1-2-3-5-5-6l-3 1v-1l2-2c-3-6-5-11-8-17v-1c-1-2-5-5-7-7-2-1-3-3-5-5v-1l1-1 6-3c-7-25-14-51-17-77l-2-11v-4c-1-2-4-4-6-6 3 1 4 3 7 3h0l1 1c2 3 6 6 9 9l3-3h2 2l1 2h1v-1l1-1c12 7 24 15 36 19l1-39z" class="B"></path><path d="M390 428h2l1 2-3 6c-1-2-3-3-4-5 2-1 3-2 4-3zm3 90h1c1 3 2 8 3 12h0l-3-2-7-7 6-3zm3-18l-4-13c3 5 5 7 9 10l9 10c0 1-1 2-2 2l-6 3c0-1 0 0-1-1l-5-11zm-2 16c-1-3-1-5-2-8l-8-32c-1-5-3-9-3-14h1v2c1 6 5 12 7 18 3 6 4 12 6 18l6 13c-2 1-4 2-7 3zm38-17h0c1 2 1 4 1 6v15c0 2 1 5 0 7l-9-9c-3-3-7-6-10-10 1-1 4-2 5-3l13-6zm-55-73l6 7c0 3-1 8-1 12 1-4 2-7 2-11l5 4 3 3v6 23 14c-7-15-12-30-15-46v-12zm17 3c11 6 22 13 33 18l4 1v25c-15-10-27-24-40-36v-1c1-2 3-4 3-6v-1zm-1 13l38 39c-1-3-3-5-4-7h0c1 0 2 1 3 1h1c1 1 0 5 1 6v16l-19 10-20-21v-44z"></path><path d="M378 330l1 1 1 13c0 1 0 3 1 5 3-2 9-4 12-7h2v1l12-5 2 4 1-1c-1-1-2-2-2-4 1 0 2-1 3-1 5 6 11 13 17 18 1 1 3 2 4 2h0 1l1 53-2-1-1 39c-12-4-24-12-36-19l-1 1v1h-1l-1-2h-2-2l-3 3c-3-3-7-6-9-9l-1-1h0l-1-2c-1 0-2-1-3-2l1-1 2 1h1c1-3-1-44-2-50 0-4-1-10 0-12 1-3 0-7 0-10l1-8c0-1 0-4 1-6 1 2-1 19-1 22l4-2c0-1 1-1 2-2l-2-19z"></path><path d="M381 351h0c1 1 1 2 1 3 0 2 1 3 1 4 1 3 2 3 4 4l3 1v1 1c-2 0-4-1-6-2-2-3-3-8-3-12zm11 39h1c0 1 1 2 2 2l1 1h0c0 1 0 2-1 3h0c-1 4 5 12 6 15v1l-4-7-3-6-6 6c2-3 4-5 6-7-2-2-3-3-4-5 0-2 1-2 2-3z" class="B"></path><path d="M432 356h1l1 53-2-1v-28c-1-1-2-2-2-3l2 2c1-2 0-20 0-23z" class="I"></path><path d="M378 330l1 1 1 13c0 1 0 3 1 5 3-2 9-4 12-7h2v1l-2 1c-1 12 0 24 0 36v9c-2-2-1-9-1-11v-34l-6 3c-2 2-4 3-7 4-2 1-4 2-5 4 0 12 2 24 3 35 1 6 1 12 2 17l3 14c3 2 7 4 10 6l1-25h0v4c-1 6 0 13 0 19l1 1 3-2v1l-3 2 1 1-1 1v1h-1l-1-2h-2-2l-3 3c-3-3-7-6-9-9l-1-1h0l-1-2c-1 0-2-1-3-2l1-1 2 1h1c1-3-1-44-2-50 0-4-1-10 0-12 1-3 0-7 0-10l1-8c0-1 0-4 1-6 1 2-1 19-1 22l4-2c0-1 1-1 2-2l-2-19z" class="R"></path><path d="M376 422l1-1h1s1 1 2 1v-1l-2-2c0-3 0-7 1-10 1 2 1 5 2 8v5c1 0 1 1 2 2 2 0 4 2 5 3v1l-3 3c-3-3-7-6-9-9z"></path><path fill="#fefefe" d="M526 269h5-1c-4 2-9 1-12 1h-22-12-1c1 1 1 1 1 2h1l1 1 1-1v-1h5l1 1h3 0 4l1-1h0v1h1c2 0 5-1 6 0h3c1 0 6 0 7 1 2 0 3-1 4 0h1c2 0 6-1 8 0h2c2 0 4 0 6 1h-7l2 2h-1-1c0-1-1-1-2-2v1c1 2 3 4 5 5-1 0-2-1-3-1-1 1-2 1-3 1-2-1-3-2-4-3l-2-2h-1l1 1c-1 0-3-1-3-2l-1 1 2 3v1c0 1 3 4 4 5 0 1-1 1-2 2-2 1-6 5-6 7-1 0-2 1-2 1-1 2 0 3-1 5l-2 3c-1 1-1 2-1 2v1c0 1-1 2-1 2v1l-1 3c0 2 1 3 0 5v64 13 4c0 1-1 1-1 2-1 2 0 6 0 8-1 14-1 29-1 43 0 3-1 11 1 14 0 1 0 2-1 4v264 33 27c0 3-1 7 0 9v25 5c1 1 2 3 2 4 1 2 2 4 2 6 0 3 1 6 2 8v3l-1-1v4l1 5 1 2v3h1v4l1 4c0 1 1 1 1 2v2c1 1 1 2 1 3l3 8c1 3 2 5 3 7 0 1 0 2 1 2 0 2 1 5 3 7v-1l-5-15c3 0 8 0 11 1h4c1 1 1 1 2 0l2 2 1 1c2 4 6 7 8 11 1 0 1 1 2 1l1-1c0-2-3-5-5-7-1-1-2-1-2-3 2 0 3-1 5-1l7-2 12 12h0 0c1 1 2 1 2 1 2 1 3 2 4 3h-2v1c2 1 3 2 5 1h1l3 3h-7c-2-2-5-5-6-7-1-1-2-2-4-3v2c0 3 4 5 6 8h-7-3-1-2-5-1 0-2c-4 1-8 1-12 1h-20-42l-1-2c-2 2-9 2-13 2v-2h-1l-1 1v1h-15c-1 0-4 0-5-1h-1l-1 1h-18c-3 0-6 0-9-1l1-1v-1l-2 2c-1 1-4 1-5 1h-13l1-1-2-2 17-7 6-3 3 1c6-4 13-9 18-14 12-12 21-25 25-41 2-11 2-21 2-32v-36-89-53h4c-1-1-2-1-3-1l-1-1v-4-10-59-113-44l-1-33c0-12-2-25-6-36-3-7-7-12-12-17-15-16-37-24-57-32-3-3-7-4-10-5l-2-3h3c2 1 3 1 5 2 3 1 5 2 8 1l18 5v-1l-25-7c-2 0-4-1-6-2 11-1 22 0 32 0h50c0 4-4 15-1 18 1 2 4 4 6 5 6 6 11 12 13 20 3 8 3 16 3 24v24l-1 43v171c0 27 0 46 1 46 1-4 1-8 1-12v-23-82-121-37c0-8 0-16-1-24 0-8-3-15-7-22l-7-7c-2-1-7-5-7-6v-4l1-2 2-12-1-4c5-1 11 0 16 0l47-1z"></path><path d="M416 899l3 1-8 4-1-2 6-3z" class="E"></path><path d="M495 662l-1-3c0-1-1-1-2-1s-3-2-4-3h0 1 1c1-1 2 0 3-1v-1-2h0c1 0 1 0 1 1h0c1 4 1 7 1 10z" class="l"></path><path d="M474 281c1-2 5-4 7-6 1 0 2 0 2-1h1 1l-3 3v2c0 1-3 3-3 4-1 0-1-1-1-1-2 0-3 0-4-1z" class="n"></path><path d="M393 909l17-7 1 2c-5 2-11 5-16 7l-2-2z" class="X"></path><path d="M507 826v5c1 1 2 3 2 4 1 2 2 4 2 6 0 3 1 6 2 8v3l-1-1v4l1 5 1 2v3h1v4l1 4c0 1 1 1 1 2v2c1 1 1 2 1 3l3 8c1 3 2 5 3 7 0 1 0 2 1 2 0 2 1 5 3 7v-1l1 2h-1c-2-1-3-3-3-6l-1-1c0-1-1-1-1-2 0-2-1-2-1-4l-1-1-1-2c0-1 0-2-1-3v-1-1-1c-1-1-2-3-2-4v-2h-1c1-2 0-2 0-2 0-2-1-4-1-5-1-2-1-1-1-2v-1-1c0-1-1-2-1-2v-2-1c0-1-1-2-1-3v-2l-1-1v-2c-1-2 0-4-1-6-1 0-1 0-1-1v-2c-1-1-1-2-1-3s0-2-1-4l-1 1c-1 1-1 2-1 3h-1v-2c2-4 2-9 3-13z" class="J"></path><path d="M526 269h5-1c-4 2-9 1-12 1h-22-12-1c1 1 1 1 1 2h1l1 1 1-1v-1h5l1 1h3 0 4l1-1h0v1h1c2 0 5-1 6 0h3c1 0 6 0 7 1 2 0 3-1 4 0h1c2 0 6-1 8 0h2c2 0 4 0 6 1h-7l2 2h-1-1c0-1-1-1-2-2v1c1 2 3 4 5 5-1 0-2-1-3-1-1 1-2 1-3 1-2-1-3-2-4-3l-2-2h-1l1 1c-1 0-3-1-3-2l-1 1 2 3c-2-1-3-2-4-3h0v1 1c-1-2-2-2-3-3h-1c1 1 1 2 1 3-1-1-2-3-3-3v2c-1-1-1 0-1-1s0-1-1-1c0 1 0 1-1 2l-1-2h0l-1 1h0l-1-1-1 1c-1-1-1-1-2-1v1c-1 0-1-1-1-2h-1l-2 3c0 1 0 2-1 4h-1l-2 2c-1 2-2 5-4 5l-3 5c4 6 8 12 9 20v19 34 111 354l-1-168c0-3 0-6-1-10l1-239v-67-23c0-4 0-9-1-13-1-6-5-14-9-18-2-2-4-3-5-4l10-14-1-1-2 2v-1h-2-1-1c0 1-1 1-2 1-2 2-6 4-7 6l-1 1c-1 1-1 1-3 2s-3 2-5 3c-2 0-2 0-3-1l2-12-1-4c5-1 11 0 16 0l47-1z" class="B"></path><path d="M525 277v-2h-1c1-1 2-1 3-1 1 2 4 3 5 5-1 1-2 1-3 1-2-1-3-2-4-3z" class="N"></path><path d="M483 286h1l1-1c2 0 2 0 3 2l1 1c-1 1-2 2-4 3-1-1-3-2-3-4l1-1z" class="o"></path><path d="M469 275l1-1c1 1 1 1 1 2l2-2c0 1 0 0 1 1h0 1c2 0 3-1 4-1h0c-1 2-2 4-4 6l-1-3c-2 0-3 0-4 1v2l-3 3c0-1-1-1-1-1 1-2 2-3 4-4 0-1 0-2-1-3z" class="G"></path><path d="M467 283l3-3v-2c1-1 2-1 4-1l1 3c-1 0-2 1-2 2-1 1-1 1-3 2s-3 2-5 3c-1 0-1-1-1-1l3-3z" class="n"></path><path d="M464 274c1 0 2 0 4 1h1c1 1 1 2 1 3-2 1-3 2-4 4 0 0 1 0 1 1l-3 3s0 1 1 1c-2 0-2 0-3-1l2-12z" class="m"></path><path d="M483 286c2-4 6-11 10-12 1 0 1 0 1 2h1c0-1 0-1 1-2l1 1c0-1 0-1 1-1v2c0 1 0 2-1 4h-1l-2 2c-1 2-2 5-4 5l-1 1-1-1c-1-2-1-2-3-2l-1 1h-1z" class="l"></path></svg>