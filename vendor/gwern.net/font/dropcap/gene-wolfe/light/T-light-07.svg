<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="126 108 799 879"><!--oldViewBox="0 0 1024 1023"--><style>.B{fill:#b5b4b3}.C{fill:#c4c3c2}.D{fill:#0e0d0c}.E{fill:#bdbbba}.F{fill:#181817}.G{fill:#868483}.H{fill:#141413}.I{fill:#1d1c1b}.J{fill:#343332}.K{fill:#d4d2d2}.L{fill:#222120}.M{fill:#aeacac}.N{fill:#636260}.O{fill:#7c7b7a}.P{fill:#cac8c7}.Q{fill:#272625}.R{fill:#9a9998}.S{fill:#a4a3a2}.T{fill:#a9a7a7}.U{fill:#92918f}.V{fill:#3a3938}.W{fill:#cfcdcd}.X{fill:#8d8c8a}.Y{fill:#2e2d2b}.Z{fill:#dbdad9}.a{fill:#484745}.b{fill:#2c2a2a}.c{fill:#595857}.d{fill:#504f4e}.e{fill:#403f3e}.f{fill:#959494}.g{fill:#e0dfde}.h{fill:#a09e9d}.i{fill:#757473}.j{fill:#070706}.k{fill:#6c6a6a}.l{fill:#f5f4f3}.m{fill:#71706f}.n{fill:#ecebeb}</style><path d="M550 189c1-1 1-3 3-3 3 2 2 5 7 6h-2l-2 1-2-3h-1-1c-1 0-1 0-2-1z" class="Q"></path><path d="M660 763s3-2 4-2h1l1-1h2l-10 8-1-2 3-3z" class="J"></path><path d="M430 130c4-2 9-3 14-3-1 0-2 1-4 2-2-1-4 2-5 2-2-1-3 0-4 0l-1-1z" class="X"></path><path d="M794 383h1l3 3c1 0 2 0 2 1h1c2 0 1 1 3 1l1 1v3h-1c-5-2-8-4-10-9z" class="Y"></path><path d="M653 218c3 0 7 0 10 1 0 2-1 3-2 4l-3 3h0l-2-1 2-2c1-1 1-1 1-2l-2-1c-2 0-3-1-4-2z" class="b"></path><path d="M581 129c-2-1-3-2-5-3 6 1 11 2 16 5l1 1c-2 0-3-1-5 0-1 0-5-3-6-3h-1z" class="O"></path><path d="M533 184h1 0l3 1v-1c1 1 3 1 4 2l-2 3h-1 0c-2-2-5-2-7-2v-2l2-1z" class="V"></path><path d="M873 256l1 1c-2 6-3 11-4 17-1-2-1-8-1-10 1-3 2-6 4-8z" class="J"></path><path d="M479 178v3l3 3 2 2 2-2h1v1h2c-3 2-4 3-6 4-3-2-4-6-5-8 0-1 0-2 1-3z" class="L"></path><path d="M822 299l1 1c5 1 12 0 17 1l-5 6c0-1 1-2 1-3l1-1h-1l-3 1v-2c-4 0-8 0-10 1l-1-1v-3z" class="I"></path><path d="M217 358l1 1h1c-2 4-1 11-1 16l1 1 1-1v1c0 1 1 1 1 2h1-2c1 1 1 1 1 2h-1c-1 2-1 2-3 3 0-8-1-16 0-25z" class="O"></path><path d="M367 194v-5c0-2 0-8 2-10 1 1 2 3 2 5 2 3 3 4 6 5l1 1 1 1-1 2-2-2c-1-1-3-2-4-3h-1-1c0-2 0-3-1-4v1c-1 2 0 3 0 5h-1l-1 1v3z" class="d"></path><path d="M503 93l1 2c0 3-1 7 0 10-1 1-2 2-2 3s0 2-1 3v5c0 2-1 3 0 5 1 1 1 1 2 1 1 1 1 1 1 2h-2c-2-1-2-1-3-2l4-29z" class="O"></path><path d="M123 237l-4-4c0-1 0-1 1-2 2-1 5 0 8-1 1 2 1 2 3 3-2 0-2 0-3 2l1 1h-3c-1 0-2 0-3 1z" class="b"></path><path d="M583 196h5 0l-8 15c-1 2-3 4-3 6 0-1 0-1-1-2-1 0-2 1-4 2h-1l2-2v-2-1-1l3 3h0c1-2 2-3 3-5l2-3h0c1-1 1-2 1-3h0c1-2 0-1 0-2s1-1 2-2c-1-1-1-1-1-2v-1z" class="V"></path><path d="M890 240l1 2c-7 3-13 8-17 15l-1-1c3-8 10-12 17-16z" class="d"></path><path d="M709 721c0 1 1 3 2 4-4 4-8 9-12 12 0-1 0-2 1-2l1-1c-1 0 0 0-1-1h0c1-1 1-1 2-3h-2 0c1 0 1-1 1-1a30.44 30.44 0 0 1 8-8z" class="e"></path><path d="M230 380l2 2 3 8c-1 1-3 1-4 3 1-1-2-9-2-11-1 2-3 6-5 7l-6 3h0v-2c0-1 1-1 1-2l1 1 1-1c3-2 6-5 9-8z" class="J"></path><path d="M592 131c5 2 9 4 14 7 5 2 9 3 11 8h0c-1 1-1 2-2 2l-2-2c-6-6-13-10-20-14l-1-1z"></path><defs><linearGradient id="A" x1="495.086" y1="214.307" x2="480.154" y2="206.65" xlink:href="#B"><stop offset="0" stop-color="#0f0d0e"></stop><stop offset="1" stop-color="#2d2c2a"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M479 200l2 1c1 0 1 1 1 1 1 4 2 7 5 9 3 1 7 1 10 2h0v2c-3 0-6-1-8 0-2-1-4-2-5-3-3-3-5-7-5-12z"></path><path d="M367 194v-3l1-1 1 3 1 1c1 1 1 3 1 4l1 4v2l-2 2c0 2 0 2 1 3s1 1 2 1h0-2l-1 2h0c-1-1-2-1-3-2v-2-14z" class="Y"></path><path d="M367 194v-3l1-1 1 3 1 1c-2 1-2 1-2 4 0 1 0 1 1 2 0 1-1 2-1 2-1 2 0 4-1 6v-14z" class="e"></path><path d="M474 169c2 0 3 0 5 2v1l2 2v1s1 1 2 1l2 2 3 3v1l-1 2h-1l-2 2-2-2-3-3v-3-1h1c-2-2-5-6-6-8z" class="O"></path><path d="M485 178l3 3v1l-1 2h-1l-2 2-2-2v-3l-2-2v-1c2 1 3 1 5 0z" class="M"></path><path d="M439 196l3-1h1c1-2 0-7 0-8-1-7-1-13 0-19h2v13 1c-1 2-1 5-1 7 1 2 1 3 1 5s0 7 2 9l-2 1c-1-1-1-1-2-1 1-1 0-1 1-1v-1-2c0-1 0-1-1-2h-1c-1 0-2 0-3-1z" class="b"></path><path d="M202 321c-2 1-5 0-6-1s-3-4-4-6c-1-3-3-6-6-9-1-1-3-2-3-4h3 2v1 1l1 1c1 1 1 2 2 4l1 1 4 5c1 2 2 3 3 4s1 1 3 1l1-1 1 1c-1 1-1 1-2 1v1z" class="F"></path><path d="M484 893l2-2 11 25v1c0 2 1 4 1 6l1 1c0 1 0 2-1 3v1h0l-14-35z" class="d"></path><path d="M463 192l5-7 1 1c2 3 4 5 7 8 3 2 9 5 10 9h0c-2 0-2 0-4-1 0 0 0-1-1-1l-2-1-9-9c-1 1-2 2-3 2h-2-1l-1-1z" class="Y"></path><path d="M465 193v-1c1-1 1-3 3-3l2 2c-1 1-2 2-3 2h-2z" class="W"></path><path d="M896 231c2 0 4-1 6 0 1 1 1 1 1 2-1 4-8 7-12 9h0l-1-2 2-2c1-1 1-1 1-2-3-1-5-1-8-1h4c1-1 2-2 2-3h2c1 0 2-1 3-1z" class="I"></path><path d="M891 232h2l1 1v1l1 1c1 0 2-1 4 0-1 1-2 2-4 2-1 0-1-1-2-1-3-1-5-1-8-1h4c1-1 2-2 2-3z" class="c"></path><path d="M443 168c1-4 2-8 3-11 2 9 5 18 9 27 2 2 4 5 6 8h2l1 1-2 3c0-1 0-1-1-1-2-1-3-3-4-5-6-9-9-18-12-28v6h-2z" class="L"></path><path d="M154 265l1 1c0 2 1 3 1 5v1h0c2 1 3 2 4 3 0 1 1 2 0 3v1l2 2 1 1h-1-4v1l-1-1-1 1 1 1 4 1h-8l-1-18c1-1 2-1 2-2z" class="c"></path><path d="M155 266c0 2 1 3 1 5v1h0c2 1 3 2 4 3 0 1 1 2 0 3v1l2 2c-1 1-2 1-4 0-1-2-3-6-3-9v-6z" class="f"></path><path d="M430 130l1 1c-7 3-14 8-20 13l-9 9c-1 0 0 0-1 1h0-2l-1 1-1-1c-1 0-2 0-2-1l1-2c2-1 4-1 6-1 2-1 3-2 4-3h-2c1-2 4-6 5-7 2-1 2 0 4 0 5-4 11-7 17-10z" class="b"></path><path d="M225 377l2-2c1 0 1 0 2 1v2c1 1 1 1 1 2-3 3-6 6-9 8l-1 1-1-1c0 1-1 1-1 2v2c-1-2-1-7-1-9 2-1 2-1 3-3h1c0-1 0-1-1-2h2 1v-1l-1-1 1-1 2 2z" class="k"></path><path d="M225 377l2-2c1 0 1 0 2 1v2c1 1 1 1 1 2-3 3-6 6-9 8h0l-1-1c3-3 5-6 5-10z" class="K"></path><path d="M226 380v-3h1l2 1v2l-1 1c-1-1-1-1-2-1z" class="l"></path><path d="M368 190h1c0-2-1-3 0-5v-1c1 1 1 2 1 4h1 1c1 1 3 2 4 3l2 2v4c0 2 0 4-1 5h-3-2l-1-4c0-1 0-3-1-4l-1-1-1-3z" class="O"></path><path d="M371 198l1-1 1 1h1l-2-2 1-1v1c2 1 2 1 4 1 1 2 0 3 0 5h-3-2l-1-4z" class="G"></path><path d="M794 230h55l-1 1h0l-1 5h0-5 0-1l-2-2 1-1h-2-7c-2-1-5-1-7-1 1 0 2-1 3-1v-1c-11 0-22 1-33 0z" class="I"></path><path d="M840 233c3-1 6-1 8-2l-1 5h0-5 0-1l-2-2 1-1z" class="Z"></path><path d="M558 159c3 0 3-2 6-1-10 5-19 17-23 28-1-1-3-1-4-2 1-3 4-6 5-8 5-7 9-12 16-17z" class="F"></path><path d="M410 192l1 1c3 0 5 3 9 2 3 1 6 1 10 1l-1 1c-1 1-4 2-4 4-1 1-1 4 0 5h-1v4c-1 0-2-1-2-1-1-1-2-2-3-2l1-2-1-2 1-2-4-2c-2-2-4-4-6-7z" class="e"></path><path d="M457 158l-2-1 1-1 10 1c1 2 3 3 5 5h-4l7 7c1 2 4 6 6 8h-1v1c-1 1-1 2-1 3-6-10-12-18-21-23z" class="I"></path><path d="M457 158l-2-1 1-1 10 1c1 2 3 3 5 5h-4c-2-2-6-3-9-4l-1-1v1z" class="E"></path><path d="M777 509l1 1c0-1 0-1 1-1h1v-1c0-1 0-2-1-3v-1h0c-1-2-1-2-1-3v-3c-1-1-1-1-1-2v-3c-1-1-1-1-1-2v-2c-1-2-1-2-1-3l1-1-1-1 1-2c5 16 7 34 7 50-1-4-1-8-2-11h-1c-1-1-1-1-2-1h0v4c-1-3 0-6-1-9v-6z" class="m"></path><path d="M771 481c1-1 2-3 2-4s0-2-1-3c-1-4-1-7-3-9-1-2-1-3-1-4l1-2 7 23-1 2 1 1-1 1c0 1 0 1 1 3v2c0 1 0 1 1 2v3c0 1 0 1 1 2v3c0 1 0 1 1 3h0v1c1 1 1 2 1 3v1h-1c-1 0-1 0-1 1l-1-1c-1-9-4-19-6-28z" class="O"></path><path d="M597 202h2c0 3-1 4-1 7 0 1-1 2-1 3-2 1-4 3-6 5 0 1 0 1 1 1-2 1-3 2-4 3h-1c-1 0-1-1-2-1s-2 0-3 1h0c-1-1-2-1-3-1v-1l1-2v-1h0l7-2c5-3 8-6 10-12z" class="J"></path><defs><linearGradient id="C" x1="721.018" y1="714.219" x2="715.52" y2="710.805" xlink:href="#B"><stop offset="0" stop-color="#4b494a"></stop><stop offset="1" stop-color="#656463"></stop></linearGradient></defs><path fill="url(#C)" d="M730 701c1-2 3-5 5-6-7 11-15 21-24 30-1-1-2-3-2-4l6-8h1c4-2 9-8 10-13l1 2v1l1 1c1-1 1-2 2-3z"></path><path d="M561 190c8-10 12-22 14-34 4 11 4 22 3 34-2-3 0-5-1-7 0-1-1-2-1-2v-3-8l-1-7c-1 3-1 6-2 10-2 6-6 14-10 19v-1l-1 1v-1l-1-1z" class="J"></path><path d="M581 129h1c1 0 5 3 6 3 5 3 9 7 13 12 1 1 3 3 4 5 0 1 1 3 2 4 0 2 1 3 0 5-1 1-2 2-4 3 1 1 5 3 6 4h1c0 1 1 3 2 4l-1 2h-1c-3-5-6-7-10-10h0 1l2-2c1-1 2-3 1-4 0-2-2-5-3-7-5-8-12-13-20-19z" class="D"></path><path d="M574 832v2c-1 1-2 1-2 2l1-1 1 1c-14 16-26 34-35 53l-1-1c3-8 9-16 12-24 0-1 1-2 1-3 1 0 2-1 2-1 2-3 3-6 5-8 5-7 10-14 16-20z"></path><path d="M407 168h0l3-2 1 1-1 2h3c-3 5-4 11-2 17 2 5 5 7 9 9-4 1-6-2-9-2l-1-1-2-4c-3-4-3-9-4-13 1-2 1-4 2-6l1-1z" class="I"></path><path d="M407 168h0l3-2 1 1-1 2c-2 3-4 8-3 12 0 2 0 4 1 7-3-4-3-9-4-13 1-2 1-4 2-6l1-1z" class="M"></path><defs><linearGradient id="D" x1="600.295" y1="178.717" x2="608.18" y2="193.561" xlink:href="#B"><stop offset="0" stop-color="#141110"></stop><stop offset="1" stop-color="#2e2d2d"></stop></linearGradient></defs><path fill="url(#D)" d="M612 169c2 5 3 8 3 13l-3 9-4 3c-3 2-6 4-8 7l-1 1h-2c-1-2-2-4-4-5v-1c2 0 5 0 8-1 4-2 7-4 9-8 2-5 2-11 0-16h1l1-2z"></path><path d="M176 350l5-3c4-3 9-7 11-13 0-2 1-4 1-6 3-1 8-1 12-1 1 0 2 0 3 1h0c-2 2-3 2-6 3-1 0-2 0-4 1v1c-1 1-1 2-2 3l-3 3-1 2c-2 2-8 8-12 9h-1l-1 1-2-1z" class="Q"></path><path d="M196 336l-2-2c0-1 0-2 2-2l2 1c-1 1-1 2-2 3z" class="H"></path><path d="M205 327c1 0 2 0 3 1h0c-2 2-3 2-6 3-1 0-2 0-4 1 0 0 0-1-1-1v-1h-1l-1 1-1-1 1-1h0c1-2 8-1 10-2z" class="F"></path><path d="M186 301l12-1v2h1l1-1c1-1 2-1 3-1l1 1c0 2-1 3-2 4l-2 2c0 1-3 1-4 0h0c0 2 3 4 4 6v1c1 0 1 1 1 2l2 2-1 1c-2 0-2 0-3-1s-2-2-3-4l-4-5-1-1c-1-2-1-3-2-4l-1-1v-1-1h-2zm632 26c3 0 9 0 11 1 1 2 1 5 2 7 3 8 10 12 18 17l-3 1-4-2-5-4-2-1c-4-3-7-8-11-12-1-1-2-2-2-3l-2-1c0-1-1 0-1-1-1 0-1-1-1-2z" class="Q"></path><path d="M818 327c3 0 9 0 11 1v2c-1 1-2-1-3 1l1 2c-1 1-2 0-3 0v-2h-2l-2-1c0-1-1 0-1-1-1 0-1-1-1-2z" class="D"></path><defs><linearGradient id="E" x1="587.81" y1="821.944" x2="585.519" y2="819.272" xlink:href="#B"><stop offset="0" stop-color="#0e0f0e"></stop><stop offset="1" stop-color="#2c2a2b"></stop></linearGradient></defs><path fill="url(#E)" d="M607 802c3-2 6-4 8-6l1 1c-13 9-25 21-36 32-2 2-5 6-6 7l-1-1-1 1c0-1 1-1 2-2v-2c1-3 4-5 6-7 5-6 10-11 16-15 4-3 7-5 11-8z"></path><path d="M253 231c6 0 13-1 20-1h45c9 0 19-1 28 0v1h-1l-1 4v6l-1 1h-1l-1-1v-1-4-4c-14 1-27 0-40 0h-33c-5-1-10 0-15-1z" class="D"></path><path d="M210 305c1 2 1 14 1 17h-1l-8-1v-1c1 0 1 0 2-1l-1-1-2-2c0-1 0-2-1-2v-1c-1-2-4-4-4-6h0c1 1 4 1 4 0l2-2h7 1z"></path><defs><linearGradient id="F" x1="548.69" y1="174.341" x2="544.961" y2="162.153" xlink:href="#B"><stop offset="0" stop-color="#95949a"></stop><stop offset="1" stop-color="#bebcb7"></stop></linearGradient></defs><path fill="url(#F)" d="M544 166c1-1 3-1 4-3l3-3c3-1 5-1 7-1-7 5-11 10-16 17-1 2-4 5-5 8v1l-3-1h0-1-2c0-1-1-2-1-3 1 0 1-1 2-2l2-2c0-1 1-2 2-3h0l5-8c2 1 2 0 3 0z"></path><path d="M541 166c2 1 2 0 3 0l1 1h0v1c-2 1-5 6-8 6h-1l5-8z" class="B"></path><path d="M536 174v2c1 1 2 1 3 1 0 2-2 4-3 5l-2 1v1h0-1-2c0-1-1-2-1-3 1 0 1-1 2-2l2-2c0-1 1-2 2-3z" class="O"></path><path d="M486 176c2 2 5 3 7 5 1 2 2 3 3 5v2c1 3 3 6 4 8 2 5 4 9 5 13v2l-1 1v1c-2 2-4 2-7 2v-2c1 0 2-1 3-1v-1c0-1-1-4-1-5-2-7-5-15-10-21h-2v-1l1-2v-1-1c-1-1-1-2-2-4z" class="j"></path><path d="M486 176c2 2 5 3 7 5 1 2 2 3 3 5v2c1 3 3 6 4 8 2 5 4 9 5 13v2l-1 1c0-6-4-13-7-18-1-3-2-6-3-8l-6-6c-1-1-1-2-2-4z" class="O"></path><path d="M503 93l5-42c1-3 1-9 3-12v1l7 52h0l-1 1-2-12-2-16c-1-6-1-12-3-18l-6 48-1-2z" class="b"></path><path d="M498 127h4c0 1 1 1 1 2l3-1c0 1-1 3 0 5h1l-1 1v4l-3 10c-1 1-2 4-3 5l-1 1h-2l-2-1c-1-3 2-13 2-17l1-7v-2z" class="G"></path><path d="M498 127h4c0 1 1 1 1 2l3-1c0 1-1 3 0 5h1l-1 1-1 1-2 2h-1c-1-2 0-4-1-5-1-2-2-3-3-5z" class="C"></path><path d="M498 129c1 3 1 5 2 8 1 2 1 3 2 5s1 4 1 6c-1 1-2 4-3 5v-2-3h0l1-2-1-1-1-1c0-3 0-5-2-8l1-7z" class="c"></path><defs><linearGradient id="G" x1="495.961" y1="152.001" x2="498.94" y2="142.805" xlink:href="#B"><stop offset="0" stop-color="#151413"></stop><stop offset="1" stop-color="#2e2e2b"></stop></linearGradient></defs><path fill="url(#G)" d="M497 136c2 3 2 5 2 8l1 1 1 1-1 2h0v3 2l-1 1h-2l-2-1c-1-3 2-13 2-17z"></path><path d="M470 191l9 9c0 5 2 9 5 12 1 1 3 2 5 3-2 0-3 0-5-1h-3l-2-2c-1 0-2 0-2-1-2-1-4-1-6-2h0l-1 2h-1l-2-2-2-2 2-1v-5 1h1v-2l2-2-1-1c-1 0-2 0-3-1 0-1 0-1 1-3h0c1 0 2-1 3-2z" class="C"></path><path d="M468 202v-2l2-2-1-1c-1 0-2 0-3-1 0-1 0-1 1-3l7 7-1 1h-2c-1 1-2 1-3 1z" class="g"></path><path d="M467 201v1h1c1 1 1 3 1 4v1h1 1v-2h0 1 1l1-1c2 1 3 3 5 4h0c2 2 3 4 5 6h-3l-2-2c-1 0-2 0-2-1-2-1-4-1-6-2h0l-1 2h-1l-2-2-2-2 2-1v-5z" class="G"></path><path d="M563 192c4-5 8-13 10-19 1-4 1-7 2-10l1 7v8 3s1 1 1 2c1 2-1 4 1 7h0l-2 2v5c-1 0-2 1-3 1v1-2l-1-3c1-1 0-2 0-3s-1-1-2-1h-1c-1 1-2 3-3 4l-3 2c-1 0-2 0-3 1v-1c-2 0-3-2-4-3l2-1h2l1-2 1 1v1l1-1v1z" class="g"></path><path d="M561 190l1 1v1l1-1v1l-3 4c-2 0-3-2-4-3l2-1h2l1-2z" class="F"></path><path d="M576 170v8 3s1 1 1 2c1 2-1 4 1 7h0l-2 2v5c-1 0-2 1-3 1v1-2l-1-3c1-1 0-2 0-3 2-1 2-5 3-8s1-10 1-13z" class="R"></path><path d="M572 191c2-1 2-5 3-8 0 5-1 9-1 14h-1l-1-3c1-1 0-2 0-3z" class="K"></path><defs><linearGradient id="H" x1="635.374" y1="791.187" x2="647.095" y2="761.391" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2929"></stop></linearGradient></defs><path fill="url(#H)" d="M654 764c2 1 4 0 6-1l-3 3 1 2-42 29-1-1c3-3 8-6 10-9l-1-2c3-5 8-6 13-9 4-3 8-6 12-8v-1l5-3z"></path><path d="M513 958c0 1 0 0 1 1 3-14 6-28 11-41 1-4 3-8 5-12 1-3 1-6 3-8 0-1 1-1 1-2l4-8 1 1-8 18c-7 16-11 32-15 49l-3 15c-1 4-1 8-2 12l-7-35h1 0l4 18 1-3c1-1 0-3 1-5l1-1 1 1z" class="F"></path><path d="M892 238l-2 2c-7 4-14 8-17 16-2 2-3 5-4 8l-1 3h0c-1 0-1 1-2 1l-4-3c0-3 0-5 1-8v-1c1-4 6-8 9-10 6-5 12-7 20-8z" class="g"></path><path d="M532 157c3 1 6 1 8 0 3-1 6 0 8-1h17v1l-1 1c-3-1-3 1-6 1-2 0-4 0-7 1l-3 3c-1 2-3 2-4 3-1 0-1 1-3 0h0c-2 1-3 3-4 4h-4v-5l-1-1v2h-2v-2h-1-1c-2-1-2-2-2-3 1-1 2-1 3-2l-1-1c1-1 3 0 4-1z" class="P"></path><path d="M537 158c4 0 8 1 12 0-2 3-5 5-7 7-2-1-2-1-3-3-1-1-2-2-2-4z"></path><path d="M530 163l7-5c0 2 1 3 2 4 1 2 1 2 3 3l-1 1c-2 1-3 3-4 4h-4v-5l-1-1v2h-2v-2-1z" class="M"></path><path d="M535 166h1c0 2 0 2-1 3h-1v-1l1-2z" class="C"></path><path d="M530 163l7-5c0 2 1 3 2 4-1 1-2 2-3 2-2 0 0 0-2-1h-1-3z" class="E"></path><defs><linearGradient id="I" x1="528.801" y1="212.972" x2="519.374" y2="192.127" xlink:href="#B"><stop offset="0" stop-color="#0c0c0b"></stop><stop offset="1" stop-color="#383634"></stop></linearGradient></defs><path fill="url(#I)" d="M537 170c1-1 2-3 4-4h0l-5 8h0c-1 1-2 2-2 3l-2 2c-1 1-1 2-2 2 0 1 1 2 1 3h2l-2 1v2c-3 4-5 10-7 15-1 3-3 7-3 10 2 1 4 1 6 1l-2 2h-1v1l-3 2h-1l-1-1h-1l-3-2v-1-3c0-2-1-3-1-5 1-1 0-1 0-2l2-2 1 1c0 1 0 2-1 4 0 1 0 1 1 2 1-1 1-2 1-3s1-1 0-3h1 0c0-1 0-2 1-3v-1-1l-1-1v-1h0c1-1 2-2 3-4h1c1-2 2-4 3-5l1-1-1-1 2-4c1 0 1-1 2-1l1-1c1-3 3-5 5-7l1-2z"></path><path d="M537 170c1-1 2-3 4-4h0l-5 8h0c-1 1-2 2-2 3l-2 2c-1 1-1 2-2 2 0 1 1 2 1 3h2l-2 1c-7 5-7 11-11 17l3-10c1-2 2-4 3-5l1-1-1-1 2-4c1 0 1-1 2-1l1-1c1-3 3-5 5-7l1-2z" class="m"></path><defs><linearGradient id="J" x1="718.959" y1="716.563" x2="699.963" y2="713.249" xlink:href="#B"><stop offset="0" stop-color="#adaaab"></stop><stop offset="1" stop-color="#cecdcc"></stop></linearGradient></defs><path fill="url(#J)" d="M724 694c2 0 2 0 3 1l1 1-2 2v2c-1 5-6 11-10 13h-1l-6 8a30.44 30.44 0 0 0-8 8s0 1-1 1c-2 0-3-1-4-2 1-1 1-2 1-3l-1-1v-1l3-1 1-1c4-1 7-5 10-8h0c0-1 0-2-1-3h-2-1c1 0 2-1 4-1v-1h0c3-2 4-3 5-5s2-3 3-4h3l3-5z"></path><path d="M726 698v2c-1 5-6 11-10 13h-1c2-5 8-10 11-15z" class="i"></path><path d="M710 708h0c3-2 4-3 5-5s2-3 3-4h3c-3 5-7 10-11 14 0-1 0-2-1-3h-2-1c1 0 2-1 4-1v-1z" class="H"></path><defs><linearGradient id="K" x1="249.127" y1="388.107" x2="242.611" y2="377.675" xlink:href="#B"><stop offset="0" stop-color="#0b0a0a"></stop><stop offset="1" stop-color="#33312e"></stop></linearGradient></defs><path fill="url(#K)" d="M255 366h1c3 4 4 7 3 13 0 3-2 5-5 7-6 5-14 6-21 9-1 0-1-1-2-2 1-2 3-2 4-3l10-2-1-2v-2l-1-1c-2 0-3-1-4-2v-1c-1-1-1-1-2-1l1-1h2v1 2h1c1 0 2 0 2-1l1 1h1c2-2 3-2 4-5h3 0c1-2 0-4 0-5v-1l1-1h1l1-2v-1z"></path><path d="M253 369l2 1s0 1 1 1c1 1 1 1 1 2 0 4 0 7-3 10-2 2-6 4-9 5l-1-2h1v-2h1s1 1 2 1 2-1 3-1c2-2 4-4 4-7l-1 1-2-2h0 0c1-2 0-4 0-5v-1l1-1z" class="G"></path><path d="M253 369l2 1s0 1 1 1c1 1 1 1 1 2l-1 1h-2l-1 1h2v2l-1 1-2-2h0 0c1-2 0-4 0-5v-1l1-1z" class="e"></path><path d="M249 376h3 0l2 2 1-1c0 3-2 5-4 7-1 0-2 1-3 1s-2-1-2-1h-1v2h-1v-2l-1-1c-2 0-3-1-4-2v-1c-1-1-1-1-2-1l1-1h2v1 2h1c1 0 2 0 2-1l1 1h1c2-2 3-2 4-5z" class="a"></path><path d="M249 376h3 0c1 2 1 2 0 4-1 0-2 1-3 1l-1 1c-2 0-1 0-3-1 2-2 3-2 4-5z" class="J"></path><path d="M402 174c1 0 2 0 2 1 1 4 1 9 4 13l2 4c2 3 4 5 6 7l4 2-1 2 1 2-1 2h0-1l-2-2c-1 1-1 2-2 2l-1-1v-1h-1c-4-3-5-4-10-5l1 1c-1 1-2 2-2 4v1h0c-2-2-3-4-3-7v-1c0-1 1-4 2-5 1 1 1 2 2 2v-2c-1-1-1-2-2-4v-2c1 0 2 0 3-1 0-2-1-4-1-6 0-1 0-2-1-3h0c1-1 1-2 1-2v-1z" class="B"></path><path d="M403 186l2 6c-1 1-1 1-3 1-1-1-1-2-2-4v-2c1 0 2 0 3-1z" class="k"></path><path d="M407 195l-1-4 2-1c1 2 0 2 0 3 1 3 6 7 9 7l2 3 1 2-1 2h0-1l-2-2c-4-3-6-5-9-10z" class="C"></path><path d="M402 193c2 0 2 0 3-1 1 1 1 2 2 3 3 5 5 7 9 10-1 1-1 2-2 2l-1-1v-1h-1c-4-3-5-4-10-5l1 1c-1 1-2 2-2 4v1h0c-2-2-3-4-3-7v-1c0-1 1-4 2-5 1 1 1 2 2 2v-2z" class="N"></path><path d="M398 199v-1c0-1 1-4 2-5 1 1 1 2 2 2l1 2c1-1 1-1 2-1h0l-1 2h-3v1l1 1 1 1c-1 1-2 2-2 4v1h0c-2-2-3-4-3-7z" class="F"></path><path d="M517 93l1-1 4 30h0c-2 1-4 1-5 3s0 3 0 5c2-1 3-2 5-2 1 1 1 2 2 4h-1c1 7 3 15 2 21l-3 1-2-2-1-4c-2-3-4-6-4-9l-2-5c0-2 0-4-1-6l3-2c0-2 1-3 2-4l3-3c0-2 0-6-1-8s-1-5-2-7v-1c1-3 1-7 0-10z" class="i"></path><path d="M512 128l3-2v3c1 7 4 13 4 19-2-3-4-6-4-9l-2-5c0-2 0-4-1-6z" class="S"></path><path d="M512 128l3-2v3 10l-2-5c0-2 0-4-1-6z" class="B"></path><path d="M517 130c2-1 3-2 5-2 1 1 1 2 2 4h-1 0c-1 1-1 2 0 4 0 1-1 3-1 5-1-1-1-1-2-3v-1h-1l-1-1v-1c-1-1-1-1-1-2v-3z" class="G"></path><path d="M522 141c0-2 1-4 1-5-1-2-1-3 0-4h0c1 7 3 15 2 21l-3 1-2-2c0-2 1-3 1-4l1-1-2-1v-1l2-1v-3z" class="D"></path><path d="M763 370h0c1 0 1-1 2-1h1 1v1c1 0 1 0 2 1h-2v1h-2c0 4 0 6 3 9 2 2 4 3 6 4h0l2 1c3 1 4-1 6-2l1 1h1v-4l3-2v1l2-1h1 1l2 2v1l-3 14c-5-3-10-4-15-6-5-1-10-4-12-9-2-3-2-8 0-11z" class="L"></path><path d="M789 384l-2 7c-2-1-4-2-6-2-4-2-10-3-13-7-3-2-4-4-6-7 1-2 2-3 3-5l2 1h0v1h-2c0 4 0 6 3 9 2 2 4 3 6 4h0l2 1c3 1 4-1 6-2l1 1h1v-4l3-2v1l2-1h1l-1 5z" class="N"></path><path d="M787 380l2-1h1l-1 5c-1-1-2-3-2-4z" class="C"></path><path d="M243 363h8 5l1 2c-1 1-1 1-2 1v1l-1 2h-1l-1 1v1c0 1 1 3 0 5h0-3c-1 3-2 3-4 5h-1l-1-1c0 1-1 1-2 1h-1v-2-1h-2l-2-2h-1c-1-1-2-2-2-3-1-1 1-3 1-4v-1h1c2-3 3-4 7-5h1z" class="D"></path><path d="M248 369c1-1 2-1 4-2 1-1 1-1 3 0l-1 2h-1l-1 1v1c0 1 1 3 0 5h0-3c-1-1 1-4 1-6l-2-1z" class="L"></path><path d="M243 363h8 5l1 2c-1 1-1 1-2 1v1c-2-1-2-1-3 0-2 1-3 1-4 2h-1c-2 0-4 1-6 3v1c-2 0-2-1-3-2-1 0-2 2-4 1v-3-1h1c2-3 3-4 7-5h1zm192-232c1 0 3-3 5-2-8 5-16 11-21 20 0 2-2 4-2 6s0 2 1 3l3 3c-3 2-6 4-8 8h-3l1-2-1-1-3 2h0l-1 1c-1-1-1-1-1-2 0-2 0-4 1-6v-2c0-2 0-4 1-6l1-1c1-1 2-1 3-2l-1-1 3-3c3-1 5-4 8-5v2c4-4 8-9 14-12z"></path><path d="M409 163l5-7v2l4 3c-3 2-5 3-7 6l-1-1-3 2h0l2-5z" class="K"></path><path d="M409 163l5-7v2c-2 2-3 5-4 8l-3 2h0l2-5z" class="T"></path><path d="M411 150l-1-1 3-3c3-1 5-4 8-5v2c-4 5-5 9-7 13l-5 7-2 5-1 1c-1-1-1-1-1-2 0-2 0-4 1-6v-2c0-2 0-4 1-6l1-1c1-1 2-1 3-2z" class="Z"></path><path d="M411 150l-1-1 3-3c3-1 5-4 8-5v2c-4 5-5 9-7 13l-5 7c-1-5 4-13 7-17-2 1-3 2-5 3v1z" class="C"></path><defs><linearGradient id="L" x1="190.411" y1="360.727" x2="187.089" y2="349.773" xlink:href="#B"><stop offset="0" stop-color="#272026"></stop><stop offset="1" stop-color="#31332d"></stop></linearGradient></defs><path fill="url(#L)" d="M193 339l1 2h0c-1 1-1 3-1 4h1l2-1 1 2c0-1 1-1 1-1l2 2-1 1v1c1 1 2 2 2 4h1v-1h2l1 1-1 2 2 1h0c2 0 3 0 5 1-6 0-42 0-44-1 0-1 0-1-1-1 2-2 7-4 10-5l2 1 1-1h1c4-1 10-7 12-9l1-2z"></path><path d="M181 353l1-1c2 0 5-1 6 0h1l2-2h1l1 2 1-1h6v1l1 1h1v1c-7 1-14 0-21-1z" class="K"></path><path d="M193 339l1 2h0c-1 1-1 3-1 4h1l2-1 1 2c0-1 1-1 1-1l2 2-1 1v1c1 1 2 2 2 4l-1-1v-1h-6l-1 1-1-2h-1l-2 2h-1c-1-1-4 0-6 0l-1 1h-5 0c2-1 3-1 4-2v-1c4-1 10-7 12-9l1-2z" class="G"></path><path d="M193 339l1 2h0c-1 1-1 3-1 4h1l2-1 1 2c0-1 1-1 1-1l2 2-1 1v1c1 1 2 2 2 4l-1-1v-1h-6l-1 1-1-2h1c0-1 1-1 2-2l-1-1c-2 1-3 1-4 3l-2-1v-1c1-1 5-5 5-6l-1-1 1-2z" class="O"></path><path d="M378 196l2-1v1l2 1v1 3c-1 3-1 6-1 9 1 4 2 7 3 11l-3-1v-1h-2l1 4v1h-2v1l-1 1h-1 0c-1-1-2-1-3-1h-1c-1 0-2 0-2 1h-1l-6-3-2 1v-1c-1 0-2-2-2-3 3-3 10 0 15-3-1-3-1-4-4-5l1-2h2 0c-1 0-1 0-2-1s-1-1-1-3l2-2v-2h2 3c1-1 1-3 1-5v-1z" class="e"></path><path d="M373 210l2 1v3c0 1 0 2-1 3h0c-1-3-1-4-4-5l1-2h2z" class="H"></path><path d="M363 223c-1-1-1-1-1-2h1 1c3 1 6 2 9 1h0v1l1-1h3v1h-1 0l1 2-1 1c-1-1-2-1-3-1h-1c-1 0-2 0-2 1h-1l-6-3z" class="f"></path><path d="M379 219v-1h-1v-1c0-1-1-2-1-2 0-1 0-2 1-4 0-1 0-1 1-1 1-1 1-1 2 0 1 4 2 7 3 11l-3-1v-1h-2z" class="Y"></path><path d="M372 202h2 3c1 0 1 2 1 2 0 2-2 3-1 5l-4 1c-1 0-1 0-2-1s-1-1-1-3l2-2v-2z" class="O"></path><path d="M380 180l2-12-4-3 11-1 6-11c0 1 1 1 2 1l1 1 1-1h2 0c1-1 0-1 1-1 0 1-5 9-6 9 0 2-1 4-2 6-1 1-1 2-2 3-1 3-3 5-4 8-3 6-4 13-6 19v-1l-2-1v-1l-2 1v1-4l1-2-1-1 2-10z" class="d"></path><path d="M380 180h2c0 1-1 3-1 4-1 2-1 2 0 4 0 2-1 3-2 5-1 1-1 2-1 3v1-4l1-2-1-1 2-10z" class="N"></path><path d="M397 154l1 1 1-1h2 0c1-1 0-1 1-1 0 1-5 9-6 9-1 1-2 1-2 2v1h-2 0c0-2 1-3 2-5 1-1 2-3 3-5v-1z" class="V"></path><path d="M382 180c1-3 1-6 2-9 0-2 0-3-2-4 2-1 5-1 7-1l-3 9-5 13c-1-2-1-2 0-4 0-1 1-3 1-4z" class="U"></path><path d="M537 208c1-2-2-4-2-6 0-1 1-1 1-2 2-1 4-2 6-4 3-2 5-4 8-7 1 1 1 1 2 1h1 1l2 3c1 1 2 3 4 3v1c1-1 2-1 3-1l3-2-3 4c-2 1-3 3-5 3-1 1-2 2-4 3-1 0-2 1-2 2l-3 3v1c-1-1-1-1-2-1-3 0-3 1-5 3l-2 2-1-1c-1 0-2 2-3 2l-1 1-1-1c1 0 2-1 3-2 0-2 1-3 0-5z" class="B"></path><path d="M558 201l-1-1c-1-1 0-1 0-2 1 0 1 0 3 1 1-1 1-1 3-1-2 1-3 3-5 3z" class="T"></path><path d="M552 190h1 1c0 3-1 4-2 6l-1 1c0 1 0 0-1 1-2 1-3 5-5 6-1-1-1-1-1-2l1-1v-3l7-8z" class="C"></path><path d="M537 208c1-2-2-4-2-6 0-1 1-1 1-2 2-1 4-2 6-4 3-2 5-4 8-7 1 1 1 1 2 1l-7 8c-2 2-4 4-6 5v1c1 1 1 2 2 3-1 2-3 4-4 6 0-2 1-3 0-5z" class="J"></path><path d="M576 197v-5l2-2c0 1 0 5 1 5 1 1 3 1 4 1v1c0 1 0 1 1 2-1 1-2 1-2 2s1 0 0 2h0c0 1 0 2-1 3h0l-2 3c-1 2-2 3-3 5h0l-3-3 1-1h-3v2l-3 1v2h0-1v1c-2 1-3 3-5 4h0c-1 2-4 3-6 4l-1-1v-3h1v-2c-2 1-3 1-4 1h-1c4-4 8-6 12-10 0-1 0-1 1-1l6-8v-1c1-2 1-3 2-5l1 3v2-1c1 0 2-1 3-1z" class="d"></path><path d="M572 194l1 3v2-1c1 0 2-1 3-1v1c0 1-1 2 0 4-1 0-2 1-2 2s-1 1-1 2h0c-1 0-1 0-1-1s-1-2-1-3l-1 1h0c1 2 1 3 0 4-1 0-1 0-2-1-1 2-1 3-2 5 0 1-2 2-2 3-2 2-3 4-6 5l-1-1c1-4 6-5 6-9 0-1 0-1 1-1l6-8v-1c1-2 1-3 2-5z" class="f"></path><path d="M615 148c1 0 1-1 2-2v3c2 1 6 0 8 1 2 2 4 8 6 11 0 1 1 2 2 3 1 0 4 1 5 1h4 0l-3 4 4 21c-1 0-2 1-3 3 1 0 1 1 2 2l-1 1h0-2v3l-3-15-2-5-7-14c-4-6-7-12-12-17z" class="c"></path><path d="M639 166v1l-2 4v1h0c-1-1-1-2-1-3l-1-1-1-1 1-1h4 0z" class="C"></path><path d="M631 168h1c1 1 2 1 2 3h1c1 1 1 2 1 4 1 3 2 6 0 9l-2-5c0-1 1-1 1-2-1-1-1-1-1-2v-2-1c-1-1-1-1-1-2-1 0-1-1-2-1v-1z" class="N"></path><path d="M628 162c1 1 2 2 2 3s1 2 1 3v1c1 0 1 1 2 1 0 1 0 1 1 2v1 2c0 1 0 1 1 2 0 1-1 1-1 2l-7-14v-2l1-1z" class="d"></path><defs><linearGradient id="M" x1="620.934" y1="150.152" x2="625.33" y2="161.937" xlink:href="#B"><stop offset="0" stop-color="#252323"></stop><stop offset="1" stop-color="#3f3e3d"></stop></linearGradient></defs><path fill="url(#M)" d="M615 148c1 0 1-1 2-2v3c2 1 6 0 8 1 2 2 4 8 6 11 0 1 1 2 2 3 1 0 4 1 5 1h1v1h0-5c-4-1-5-7-7-10-2-2-2-3-4-4l-1 1c1 1 2 2 2 3l2 2c1 1 1 2 2 4h0l-1 1v2c-4-6-7-12-12-17z"></path><path d="M439 196h0c1 1 2 1 3 1h1c1 1 1 1 1 2v2 1c-1 0 0 0-1 1 1 0 1 0 2 1l2-1c1 1 2 2 2 3v3c1 0 1 1 2 1v1c1 2 3 3 4 5v1h1l-1 1c-1 0-1-1-2-1h-1c0 2 1 3 2 5 0 1 1 1 1 2-1 1-2 1-3 1l-1 1c-2 0-3 0-4-1-1 0-2 0-2-1-1 0-2 0-3-1h0c-1-1-2-1-3-1-1-1-2-2-4-3l1-1c-2 0-3-1-4-2-1 0-2 0-3-1l-5-5v-4h1 0c5 6 12 10 19 11 0-2-2-5-3-7l-8-13v-1h6z" class="Q"></path><path d="M452 217v-1l-1 1v1 1l-1-1c0-1 0-1-1-1l1-2v-2c1 1 3 2 4 3l1 1h1l-1 1c-1 0-1-1-2-1h-1z" class="F"></path><path d="M442 223c2 0 3 1 4 1h3c-1-2-2-1-2-3h3c0-1 0 0 1-1l2 2h1c0 1 1 1 1 2-1 1-2 1-3 1l-1 1c-2 0-3 0-4-1-1 0-2 0-2-1-1 0-2 0-3-1z" class="H"></path><path d="M441 207l4-3v2c1 1 1 1 1 2 0 2 1 4 1 6 0 1 0 1 1 2h-1c-3-2-5-6-6-9z" class="M"></path><path d="M439 196h0c1 1 2 1 3 1h1c1 1 1 1 1 2v2 1c-1 0 0 0-1 1 1 0 1 0 2 1h0l-4 3-2-5c-1-1-3-2-3-4l3-2z" class="B"></path><path d="M439 202l1-1 2 2v1l1-1c1 0 1 0 2 1h0l-4 3-2-5z" class="E"></path><path d="M824 334c4 4 7 9 11 12l2 1 5 4 4 2 3-1c2 1 5 2 8 3-9 1-18 1-27 2-7 0-15 1-23 1l2-2c2 1 4 0 6 0h1l4-1c-1-1-1-2-2-3h0l1-2c0-2-1-4 0-6h1v-1-1c0-2 1-4 3-5 0-1 0-2 1-3z" class="V"></path><path d="M827 350h1c1-1 1-1 1-2h1 0l1 2 1-1h1c1 1 6 3 7 3 1-1 1-1 2-1l4 2c-8 1-17 2-26 1 1 0 1 0 1-1 1 0 1-1 1-2h1c2-1 3-1 4-1z" class="P"></path><path d="M824 334c4 4 7 9 11 12l2 1 5 4c-1 0-1 0-2 1-1 0-6-2-7-3h-1l-1 1-1-2h0-1c0 1 0 1-1 2h-1c-1 0-2 0-4 1h-1c0 1 0 2-1 2 0 1 0 1-1 1v1c-1-1-1-2-2-3h0l1-2c0-2-1-4 0-6h1v-1-1c0-2 1-4 3-5 0-1 0-2 1-3z" class="d"></path><path d="M820 343v-1c0-2 1-4 3-5 0 1 1 2 1 2 1 2-1 4 2 5l1 3v3c-1 0-2 0-4 1h-1c0 1 0 2-1 2 0 1 0 1-1 1v1c-1-1-1-2-2-3h0l1-2c0-2-1-4 0-6h1v-1z" class="N"></path><defs><linearGradient id="N" x1="556.385" y1="205.707" x2="566.32" y2="207.837" xlink:href="#B"><stop offset="0" stop-color="#bbbeb7"></stop><stop offset="1" stop-color="#ded8dc"></stop></linearGradient></defs><path fill="url(#N)" d="M569 190h1c1 0 2 0 2 1s1 2 0 3c-1 2-1 3-2 5v1l-6 8c-1 0-1 0-1 1-4 4-8 6-12 10-3 1-6 3-10 6l-9 3c0-1 2-2 3-2 0-1 1-2 1-2h1l-1-1 1-1s1-1 2-1h0c1-1 1-1 1-2 1-1 1-1 1-2h-3v-1l-2-1c1 0 2-2 3-2l1 1 2-2c2-2 2-3 5-3 1 0 1 0 2 1v-1l3-3c0-1 1-2 2-2 2-1 3-2 4-3 2 0 3-2 5-3l3-4c1-1 2-3 3-4z"></path><path d="M539 221l3-3h0 2v3c-3 2-6 3-9 5 0-1 1-2 1-2h1l-1-1 1-1s1-1 2-1h0z" class="Q"></path><path d="M553 207c2 0 2-1 3-2 4-1 8-5 11-8h0v1c-2 4-5 8-9 12l-1 1-2 2-2 2c-2-2-2-2-2-4l-1-1 1-1c1-1 1-1 2-1v-1z" class="a"></path><path d="M555 208l1-1h1l1 3-1 1c-1-1-2-1-2-3z" class="b"></path><path d="M555 208c0 2 1 2 2 3l-2 2-2 2c-2-2-2-2-2-4l1-1 1-1 2-1z" class="e"></path><path d="M552 210l3 3-2 2c-2-2-2-2-2-4l1-1z" class="L"></path><path d="M569 190h1c1 0 2 0 2 1s1 2 0 3c-1 2-1 3-2 5v1l-3-2v-1h0c-3 3-7 7-11 8-1 1-1 2-3 2l-1-1c0-1 1-2 2-2 2-1 3-2 4-3 2 0 3-2 5-3l3-4c1-1 2-3 3-4z" class="U"></path><path d="M569 190h1c1 0 2 0 2 1s1 2 0 3c-1 2-1 3-2 5v1l-3-2v-1c2-1 2-4 3-6l-1-1z" class="W"></path><path d="M552 206l1 1v1c-1 0-1 0-2 1l-1 1 1 1c0 2 0 2 2 4l-9 6v-3h-2 0l-3 3c1-1 1-1 1-2 1-1 1-1 1-2h-3v-1l-2-1c1 0 2-2 3-2l1 1 2-2c2-2 2-3 5-3 1 0 1 0 2 1v-1l3-3z" class="J"></path><path d="M538 216h2v-2h1c1 0 1 0 2-1 1 0 1 1 2 1 1-1 1-1 1-2h-1v-2h1c1 1 1 1 1 2v2c1 1 1 0 2 2-1 1-1 1-2 1s-2 0-3 1h-2 0l-3 3c1-1 1-1 1-2 1-1 1-1 1-2h-3v-1z" class="H"></path><path d="M799 359l1-1h3 2v30 1l-1-1c-2 0-1-1-3-1h-1c0-1-1-1-2-1l-3-3h-1s-1 0-1-1v-1l-2-2h-1-1l-2 1v-1c1-2 1-4 1-6v-1h0c0-3-1-4-2-7h0c1-2 3-3 5-4 1 0 4-1 5-1 1 1 1 2 2 2 0-1 0-2 1-2v-1z" class="e"></path><path d="M799 359l1-1h3c0 2 0 7-1 9v4l-2-2h-1 2v-1l-1-1-2 1h0v-3c1-2 1-4 1-6z" class="J"></path><path d="M791 361c1 0 4-1 5-1 1 1 1 2 2 2 0-1 0-2 1-2v-1c0 2 0 4-1 6v3h0l2-1 1 1v1h-2l-1 1s-1-1-1-2h0c-1 0-2-1-3-1l1-1-1-1h-2c0-1-1-1-2-1-1 1-2 1-3 1h-1 0c1-2 3-3 5-4z" class="I"></path><path d="M788 372l1-1-1-1v-2c2-1 3-1 5-1l1 1-1 2c2 0 2-1 3 0 0 0 1 1 1 2v3c2 3 2 4 1 7h0l2-1 1 2c0 1 0 2-1 4 0-1-1-1-2-1l-3-3h-1s-1 0-1-1v-1l-2-2h-1-1l-2 1v-1c1-2 1-4 1-6v-1z" class="k"></path><path d="M788 373c1 1 2 2 2 3s1 1 1 2h2c1 0 0 0 1 1h-3-1-1l-2 1v-1c1-2 1-4 1-6z" class="M"></path><path d="M794 379l1-1h1c-2-2-5-3-6-5 1-1 2-2 3-2v1c2 0 3 2 4 3 2 3 2 4 1 7h0l2-1 1 2c0 1 0 2-1 4 0-1-1-1-2-1l-3-3h-1s-1 0-1-1v-1l-2-2h3z" class="E"></path><path d="M643 190l1-1c2-1 4-2 5-3 1-2 2-6 4-8 4 5 1 23 1 30l1 2c0 1-1 1-2 1-2 1-4 1-5 2v3l-2 2-2-2c1 0 1-1 2-2l-2-1-2 3v2h0l-3-2c0-3 1-5 1-8l-1-6v-3-3h2 0l1-1c-1-1-1-2-2-2 1-2 2-3 3-3z" class="V"></path><path d="M644 205c1 1 1 3 3 4h0 1 1c2 0 2-1 3-3v-1l2 2v1h0l1 2c0 1-1 1-2 1-2 1-4 1-5 2v3l-2 2-2-2c1 0 1-1 2-2l-2-1c-1 0-2-1-2-2 0-2-1-4-1-5l2-2v2l1-1z" class="Q"></path><path d="M644 205c1 1 1 3 3 4h0 1c-1 1-1 2-2 3h-1l-2-6 1-1z" class="d"></path><path d="M643 193c2-2 3-3 5-4h1c0-1 0-1 1-1v-1c0-1 0-1 1-2l1-5 2 7-1 1-1-1h-1v1h2c0 3-1 5-3 8 0 1-1 1-1 2h-2-2-1-1v-5z" class="O"></path><path d="M643 193v5h1 1 2 2c0-1 1-1 1-2h1l1-1c1 1 1 2 0 3 0 1-1 1-1 2l-1 1v1c-1 0-1 1-2 1l1 1h2l1 2c-1 2-1 3-3 3h-1-1 0c-2-1-2-3-3-4-1-4-2-8-1-12z" class="N"></path><path d="M649 204h2l1 2c-1 2-1 3-3 3h-1-1v-2l2-2v-1z" class="G"></path><path d="M528 158l1 1c-1 1-2 1-3 2 0 1 0 2 2 3h1 1v2h2v-2l1 1v5h4l-1 2c-2 2-4 4-5 7l-1 1c-1 0-1 1-2 1l-2 4 1 1-1 1c-1 1-2 3-3 5h-1-3c-1-1-2-1-3 0v1l-1 1v-4h0v-4h0-1l-1-1v-1h2l-1-1c0-1 0-1 1-1 0-2 0-2-1-3l-2-1c2-3 3-6 5-9l3-4 3-6 1 1c1 0 1 0 2-1l2-1z" class="m"></path><path d="M517 169l1 1c-1 2-2 2-2 4 0 0 0 1 1 1 0 1-2 3-1 4 0 0 1 1 1 2v4h-1v2 1l1-1 1 1c-1 1-1 2-3 2h0v-4h0-1l-1-1v-1h2l-1-1c0-1 0-1 1-1 0-2 0-2-1-3l-2-1c2-3 3-6 5-9z" class="Y"></path><path d="M528 158l1 1c-1 1-2 1-3 2 0 1 0 2 2 3h1l-1 1c-1 2-3 3-4 5h-1l-3 3c-1 1 0 1-2 1v-2l1-1c-2-4 5-8 4-12l1 1c1 0 1 0 2-1l2-1z" class="M"></path><path d="M528 158l1 1c-1 1-2 1-3 2 0 1 0 2 2 3-2 0-3 1-4 2h0-1c0-2 1-2 2-4-1-1-1-1-1-2 1 0 1 0 2-1l2-1z" class="B"></path><path d="M530 164v2h2v-2l1 1v5h4l-1 2c-2 2-4 4-5 7l-1 1c-1 0-1 1-2 1l-2 4 1 1-1 1c-1 1-2 3-3 5h-1-3l1-2c0-1 1-1 1-2l-1-1c0-1 1-1 1-2l-2-1v-4h1l-2-2 1-1 2-3h1c2 0 3 0 5-2h0 1c1-1 1-3 2-4v-1l-2-2 1-1h1z" class="U"></path><path d="M530 180l-1-1-1-1 1-1 1-1c1-3 3-4 6-4-2 2-4 4-5 7l-1 1z" class="G"></path><path d="M530 164v2 1 1c1 2-1 5-1 8h-2v2h0-1 0c-1 0-2 0-2 1l-1 1c-1-1 0-2 0-4h-1c-1 0-1 1-2 2h-1v-1l2-3h1c2 0 3 0 5-2h0 1c1-1 1-3 2-4v-1l-2-2 1-1h1z" class="S"></path><path d="M520 178c1-1 1-2 2-2h1c0 2-1 3 0 4h2c0 1 0 2 1 2l2-1-2 4 1 1-1 1c-1 1-2 3-3 5h-1-3l1-2c0-1 1-1 1-2l-1-1c0-1 1-1 1-2l-2-1v-4h1l-2-2 1-1v1h1z" class="X"></path><path d="M526 185l1 1-1 1c-1 1-2 3-3 5h-1-3l1-2c1 0 2 0 2-1l4-4z" class="a"></path><path d="M520 178c1-1 1-2 2-2h1c0 2-1 3 0 4h2c0 1 0 2 1 2-2 1-3 2-5 2-1-2 0-2 0-4 0-1-1-2-1-2z" class="T"></path><path d="M228 360l1-1c1 0 2-1 4-1l6 1h2v1c1 1 2 2 2 3h-1c-4 1-5 2-7 5h-1v1c0 1-2 3-1 4 0 1 1 2 2 3h1l2 2-1 1c1 0 1 0 2 1v1c1 1 2 2 4 2l1 1v2l1 2-10 2-3-8-2-2c0-1 0-1-1-2v-2c-1-1-1-1-2-1l-2 2-2-2-1 1 1 1v1h-1-1c0-1-1-1-1-2v-1l-1 1-1-1c0-5-1-12 1-16h-1c2 0 6-1 8-1l2 2z" class="X"></path><path d="M221 367c2 0 2 0 3 2 0 1 0 0-1 1-1 0-1 1-2 3h1l-1 1-1-1c0-2 0-4 1-6z" class="f"></path><path d="M224 364c1 0 2 0 3 1h1l1-1c1 0 3 0 5 1l1 1v2h-1-4l-2 2c-1 0-1 0-2 1l-1 1c1-3 1-4-1-7v-1z" class="V"></path><path d="M232 382h1c1 1 1 1 2 3 1-1 1-1 3-1 1 1 3 1 4 1l2-1v2l1 2-10 2-3-8z" class="f"></path><path d="M228 360l1-1c1 0 2-1 4-1l6 1h2v1c1 1 2 2 2 3h-1c-4 1-5 2-7 5v-2l-1-1c-2-1-4-1-5-1l-1 1h-1c-1-1-2-1-3-1v-3l-2-2-1 1c-1 0-1-1-2-1h-1c2 0 6-1 8-1l2 2z" class="Y"></path><path d="M242 363c-1 0-1 0-2-1-1 0 0 0 0-1l1-1c1 1 2 2 2 3h-1z" class="V"></path><path d="M227 365c2-2 4-2 6-3h1c1 2 1 3 1 4l-1-1c-2-1-4-1-5-1l-1 1h-1z" class="J"></path><path d="M228 360l1-1c1 0 2-1 4-1l6 1c-2 2-4 2-6 2-1-1-4 0-5 0v-1z" class="I"></path><path d="M218 359c2 0 6-1 8-1l2 2v1c1 0 4-1 5 0-2 1-4 1-5 1l-2 1c-1 0-1-1-2-2l-2-2-1 1c-1 0-1-1-2-1h-1z" class="L"></path><path d="M588 132c2-1 3 0 5 0 7 4 14 8 20 14-1-1-2-1-3-1l2 2c0 2 1 3 2 5v1c1 0 1 1 2 1 1 1 0 0 0 2l1 1 2 2s0 1 1 2l1 7 1 2c0 2 0 5-1 7 0 1 0 3-1 5-1 1-2 3-2 4l-2 2h-1c0 2-1 2-2 3h-1l3-9c0-5-1-8-3-13-1-1-2-3-2-4h-1c-1-1-5-3-6-4 2-1 3-2 4-3 1-2 0-3 0-5-1-1-2-3-2-4-1-2-3-4-4-5-4-5-8-9-13-12z" class="K"></path><path d="M607 153c1 1 2 2 2 4 1-1 0-1 1-1l-1-1v-1l2 2c1 2 2 3 3 4 0 1 0 3 1 4-2-1-4-6-6-7-1 3 3 5 0 8-1-1-5-3-6-4 2-1 3-2 4-3 1-2 0-3 0-5z" class="P"></path><path d="M609 165c3-3-1-5 0-8 2 1 4 6 6 7 2 4 3 6 3 10l1 1c1-1 1-1 1-2l1-5 1 2c0 2 0 5-1 7 0 1 0 3-1 5-1 1-2 3-2 4l-2 2h-1c0 2-1 2-2 3h-1l3-9c0-5-1-8-3-13-1-1-2-3-2-4h-1z" class="B"></path><path d="M610 165c2 1 2 2 3 4 1 1 1 1 2 1 0 1 1 2 1 3-1 2 0 5 0 7l-1 2c0-5-1-8-3-13-1-1-2-3-2-4z" class="T"></path><path d="M616 180h0l2 1c-1 2-3 5-3 7s-1 2-2 3h-1l3-9 1-2z" class="C"></path><path d="M343 753v-2l-6-3-1-1v-1h3c3 1 4 4 7 4h0c4 2 7 5 11 8l12 7c2 2 5 4 7 5 2 0 3 1 5 2 1 1 2 1 3 2l1 1c1 0 2 0 3 1-1-1-2-2-3-2-1-1-1-1-1-2l11 8 1-1 1 1h1 1l-2 2c1 1 3 2 3 3 1 1 2 3 3 4 3 2 6 5 9 8l-2 1 10 7c-2 0-2-1-3-1 0 0 0 1 1 1h0l-1 1-74-53z"></path><path d="M376 770c2 0 3 1 5 2 1 1 2 1 3 2l1 1c1 0 2 0 3 1-1-1-2-2-3-2-1-1-1-1-1-2l11 8 1-1 1 1h1 1l-2 2c1 1 3 2 3 3 1 1 2 3 3 4 3 2 6 5 9 8l-2 1c-4-3-5-6-9-9-1-1-3-2-5-3l1-1h0c-6-6-14-10-21-15z" class="R"></path><path d="M402 200c5 1 6 2 10 5h1v1l1 1c1 0 1-1 2-2l2 2h1 0c1 0 2 1 3 2 0 0 1 1 2 1l5 5c1 1 2 1 3 1 1 1 2 2 4 2l-1 1c2 1 3 2 4 3 1 0 2 0 3 1h-3c-2-1-2-2-4-2h-1c-2-1-2-2-5-2l-1 2c-1 0-1 0-2-1v-2l-1 1v3l2 1-1 1h0c-2 1-4 0-6 1h0c-1 1-2 1-3 1s-2 1-2 2h-3l-2-1c-1-1-2-1-3-2-1 0-2-1-2-1 0-1-1-2-1-4l-4-4c2 0 3 1 4 2l1 1v-1-1c2 1 3 2 4 3h0v-1h1c0-1-1-1-2-2v-2c-3-2-6-5-7-9h0v-1c0-2 1-3 2-4l-1-1z" class="D"></path><path d="M408 215c5 4 10 6 16 8 1 0 2 0 2 1h0c-2 1-4 0-6 1h0c-1 1-2 1-3 1s-2 1-2 2h-3l-2-1c-1-1-2-1-3-2-1 0-2-1-2-1 0-1-1-2-1-4l-4-4c2 0 3 1 4 2l1 1v-1-1c2 1 3 2 4 3h0v-1h1c0-1-1-1-2-2v-2z" class="O"></path><path d="M404 220c3 1 5 3 8 3l1 1h-1c-1 0-1 0-2-1-2 1-2 1-3 2-1 0-2-1-2-1 0-1-1-2-1-4z" class="J"></path><path d="M412 223l1-2c4 1 7 5 11 2 1 0 2 0 2 1h0c-2 1-4 0-6 1h0c-2 0-5-1-7-1l-1-1z" class="c"></path><path d="M407 225c1-1 1-1 3-2 1 1 1 1 2 1h1c2 0 5 1 7 1-1 1-2 1-3 1s-2 1-2 2h-3l-2-1c-1-1-2-1-3-2z" class="Q"></path><path d="M402 200c5 1 6 2 10 5h1v1l1 1c1 0 1-1 2-2l2 2h1 0c1 0 2 1 3 2 0 0 1 1 2 1l5 5c1 1 2 1 3 1 1 1 2 2 4 2l-1 1h-1c-1-1-4-3-5-3s-1 1-1 1c-2 0-3-1-4-1l-1 1v1 1c-2 0-2-1-4-1-2-1-4-3-6-5-2-1-4-3-6-5 0-1-1-1-1-2-1 0-1-1-2-2l-1-3-1-1z" class="a"></path><path d="M418 211l4 2 1 1c0 1 0 1-1 2-2-1-2-2-3-3l-1-2z" class="e"></path><path d="M419 213c1 1 1 2 3 3-1 0-1 0-2 1-1-1-2-1-3-2l2-2z" class="F"></path><path d="M414 213l-5-5c0-1 0-2-1-3h1c1 0 1 0 2 1l-1 1c1 1 2 2 4 2h1c1 1 2 1 3 2l1 2-2 2c-1 0-2-1-3-2z" class="L"></path><path d="M402 200c5 1 6 2 10 5l1 1h-2c-1-1-1-1-2-1h-1c1 1 1 2 1 3l5 5h-1c-2-1-4-3-6-5 0-1-1-1-1-2-1 0-1-1-2-2l-1-3-1-1z" class="Y"></path><defs><linearGradient id="O" x1="412.488" y1="171.808" x2="394.975" y2="164.228" xlink:href="#B"><stop offset="0" stop-color="#babab8"></stop><stop offset="1" stop-color="#ecebea"></stop></linearGradient></defs><path fill="url(#O)" d="M431 131c1 0 2-1 4 0-6 3-10 8-14 12v-2c-3 1-5 4-8 5l-3 3 1 1c-1 1-2 1-3 2l-1 1c-1 2-1 4-1 6v2c-1 2-1 4-1 6 0 1 0 1 1 2-1 2-1 4-2 6 0-1-1-1-2-1v1s0 1-1 2h0c1 1 1 2 1 3 0 2 1 4 1 6-1 1-2 1-3 1v2c-1 1-1 1-2 1-1-1-1-1-1-2h-1c-1-3 0-8 0-12-2 1-3 1-4 3v2h-1v2c-1 4-2 7-2 11-1 1-2 2-2 4-1 2-1 3-2 5l-2 3h0c-1-2-1-3-1-5v-3c2-6 3-13 6-19 1-3 3-5 4-8 1-1 1-2 2-3 1-2 2-4 2-6 1 0 6-8 6-9l9-9c6-5 13-10 20-13z"></path><path d="M405 155l2-2c-1 2-1 4-1 6v2c-1 2-1 4-1 6 0 1 0 1 1 2-1 2-1 4-2 6 0-1-1-1-2-1v1s0 1-1 2h0c1 1 1 2 1 3 0 2 1 4 1 6-1 1-2 1-3 1v2c-1 1-1 1-2 1-1-1-1-1-1-2h-1c-1-3 0-8 0-12 0-5 2-10 5-14 1-3 3-5 4-7z" class="F"></path><path d="M399 176l3 4c0 2 1 4 1 6-1 1-2 1-3 1-1-2-1-8-1-11z" class="i"></path><path d="M401 162c1-3 3-5 4-7v1c0 2-2 3-3 5v4 9 1s0 1-1 2h0c1 1 1 2 1 3l-3-4c0-4 1-8 2-11v-3z" class="S"></path><path d="M405 155l2-2c-1 2-1 4-1 6v2c-1 2-1 4-1 6 0 1 0 1 1 2-1 2-1 4-2 6 0-1-1-1-2-1v-9-4c1-2 3-3 3-5v-1z" class="W"></path><path d="M445 168v-6c3 10 6 19 12 28 1 2 2 4 4 5 1 0 1 0 1 1l2-3h1 2 0c-1 2-1 2-1 3 1 1 2 1 3 1l1 1-2 2v2h-1v-1 5l-2 1 2 2-3 1 8 7-1 1-2-2h-1c-1-1-1-1-3-1-1-1-2-2-4-3 0 1 0 1 1 3h-3l-1-1h-1c-3-1-4-3-5-5 0-2-1-2-3-3 0-1-1-2-2-3-2-2-2-7-2-9s0-3-1-5c0-2 0-5 1-7v-1-13z" class="K"></path><path d="M445 194c0-2 0-3-1-5 0-2 0-5 1-7 0 2 0 3 1 5v3l1 2h2c0 1-1 1-2 2h-2z" class="B"></path><path d="M455 199c3 4 5 8 9 11l8 7-1 1-2-2h-1c-1-1-1-1-3-1-1-1-2-2-4-3h0l-1-2-4-4-1-1c-1-2-3-4-4-6l1 1c3 2 3 5 6 6-1-3-3-4-3-7z" class="C"></path><path d="M455 199l-3-7c2 2 2 3 3 4 2 1 3 1 4 2h1c2 0 5 2 7 3v5l-2 1 2 2-3 1c-4-3-6-7-9-11z" class="E"></path><path d="M455 199l-3-7c2 2 2 3 3 4l4 5 2 2c2 1 3 3 4 4l2 2-3 1c-4-3-6-7-9-11z" class="e"></path><path d="M449 192c1 2 2 4 2 6v1c1 2 3 4 4 6l1 1 4 4 1 2h0c0 1 0 1 1 3h-3l-1-1h-1c-3-1-4-3-5-5 0-2-1-2-3-3 0-1-1-2-2-3-2-2-2-7-2-9h2c1-1 2-1 2-2z" class="U"></path><path d="M455 205l1 1v1l1 2-2 1-2-1c0-2 0-3 2-4z" class="B"></path><path d="M449 192c1 2 2 4 2 6l-2-2h-2 0-1l1-2c1-1 2-1 2-2z" class="W"></path><path d="M455 210l2-1-1-2v-1l4 4 1 2h0c0 1 0 1 1 3h-3l-1-1c-1-1-2-2-3-4z" class="M"></path><defs><linearGradient id="P" x1="312.647" y1="739.912" x2="319.618" y2="712.029" xlink:href="#B"><stop offset="0" stop-color="#151613"></stop><stop offset="1" stop-color="#3c393b"></stop></linearGradient></defs><path fill="url(#P)" d="M278 684c2 2 6 8 9 8l1 1c0-1 1-2 2-3l1 3h1l1-1v-1h1c0 1 4 5 4 6 3 4 5 8 9 11 4 5 8 9 12 13 2 1 4 2 4 3 1 2 2 3 3 5h-1c0 1-1 2-2 2h-1l1 1h1l22 18h0c-3 0-4-3-7-4h-3v1l1 1 6 3v2c-5-3-10-7-14-11-20-16-39-36-51-58z"></path><path d="M288 693c0-1 1-2 2-3l1 3h1l1-1v-1h1c0 1 4 5 4 6-1 1-1 2-1 3l-1 2 8 11c-5-1-12-13-15-17l-1-3z" class="F"></path><path d="M288 693c0-1 1-2 2-3l1 3c-1 1-1 1-2 3l-1-3z" class="D"></path><path d="M292 693l1-1v-1h1c0 1 4 5 4 6-1 1-1 2-1 3l-1 2c-1-3-3-6-4-9z" class="h"></path><defs><linearGradient id="Q" x1="309.828" y1="698.565" x2="309.672" y2="727.943" xlink:href="#B"><stop offset="0" stop-color="#b2aeb0"></stop><stop offset="1" stop-color="#cdcecb"></stop></linearGradient></defs><path fill="url(#Q)" d="M298 697c3 4 5 8 9 11 4 5 8 9 12 13 2 1 4 2 4 3 1 2 2 3 3 5h-1c0 1-1 2-2 2h-1c-2-1-3-3-5-5l-13-13-8-11 1-2c0-1 0-2 1-3z"></path><path d="M323 731c0-1-1-2-1-2v-1c2 0 2 0 3 1 0 1-1 2-2 2z" class="K"></path><path d="M391 183v-2h1v-2c1-2 2-2 4-3 0 4-1 9 0 12h1c0 1 0 1 1 2 1 0 1 0 2-1 1 2 1 3 2 4v2c-1 0-1-1-2-2-1 1-2 4-2 5v1c0 3 1 5 3 7 1 4 4 7 7 9v2c1 1 2 1 2 2h-1v1h0c-1-1-2-2-4-3v1 1l-1-1c-1-1-2-2-4-2l4 4c0 2 1 3 1 4 0 0 1 1 2 1 1 1 2 1 3 2h-5c-2 0-3 0-4-1l-1-1c-1 1-2 1-2 2h-1c-2 0-3 0-4 1-2 0-2 0-3-1-3-1-4-3-6-6-1-4-2-7-3-11 0-3 0-6 1-9 0 2 0 3 1 5h0l2-3c1-2 1-3 2-5 0-2 1-3 2-4 0-4 1-7 2-11z" class="U"></path><path d="M389 200v-1c1-1 1-1 2 0s1 1 1 2l-1 1 1 1h0c1 0 1 0 2 1h1v2c-1 1-1 1-2 1l-2-1v-1c0-2-1-4-2-5z" class="E"></path><path d="M389 194c2-1 2-3 3-2 2 2 2 3 2 5v2l1 5h-1c-1-1-1-1-2-1h0l-1-1 1-1c0-1 0-1-1-2s-1-1-2 0v1 2c-2 2-3 3-3 6h-3v-2l2-3c1-2 1-3 2-5 0-2 1-3 2-4z" class="P"></path><path d="M397 199h1c0 3 1 5 3 7 1 4 4 7 7 9v2c1 1 2 1 2 2h-1v1h0c-1-1-2-2-4-3v1 1l-1-1c-1-1-2-2-4-2h-1l1-1c-2 0-3-1-4-2 0-2 1 0 2-2l-3-1c0-2 1-1 1-3v-2h1c0-1 0-3-1-4 1-1 1-1 1-2z" class="k"></path><path d="M397 199h1c0 3 1 5 3 7 1 4 4 7 7 9v2c-2-1-7-4-7-6-1-1 0 1-1-1s-2-3-3-5c0-1 0-3-1-4 1-1 1-1 1-2z" class="E"></path><path d="M391 183v-2h1v-2c1-2 2-2 4-3 0 4-1 9 0 12h1c0 1 0 1 1 2 1 0 1 0 2-1 1 2 1 3 2 4v2c-1 0-1-1-2-2-1 1-2 4-2 5v1h-1c0 1 0 1-1 2l-1-5c-1 2-1 2-1 3v-2c0-2 0-3-2-5-1-1-1 1-3 2 0-4 1-7 2-11z" class="Z"></path><path d="M396 188h1c0 1 0 1 1 2 1 0 1 0 2-1 1 2 1 3 2 4v2c-1 0-1-1-2-2-1 1-2 4-2 5v1h-1c-1-4-1-7-1-11z" class="Q"></path><path d="M391 183c2 4 4 9 4 13-1 2-1 2-1 3v-2c0-2 0-3-2-5-1-1-1 1-3 2 0-4 1-7 2-11z" class="S"></path><path d="M382 201c0 2 0 3 1 5h0v2h3c0-3 1-4 3-6l-1 6v2l-1 1 1 2h0c1 0 2 0 3 1 0-2-2-2-2-5v1c1-1 0-1 0-2v-4c1 1 1 2 1 3h1c2 2 3 5 5 7v1h-2v1c2 1 2 0 4 0h1 1l4 4c0 2 1 3 1 4 0 0 1 1 2 1 1 1 2 1 3 2h-5c-2 0-3 0-4-1l-1-1c-1 1-2 1-2 2h-1c-2 0-3 0-4 1-2 0-2 0-3-1-3-1-4-3-6-6-1-4-2-7-3-11 0-3 0-6 1-9z" class="I"></path><path d="M382 201c0 2 0 3 1 5h0v2 5h1c0 2 1 4 1 6 1 3 3 5 4 7l1 1h0c-3-1-4-3-6-6-1-4-2-7-3-11 0-3 0-6 1-9z" class="K"></path><path d="M383 208h3c0-3 1-4 3-6l-1 6v2l-1 1 1 2h0s1 1 1 2v2c1 1 1 1 1 2-1 0-1 1-2 0 0-1-1-2-2-3 0-1 0-2-1-3h-1-1v-5z" class="E"></path><path d="M388 213c1 0 2 0 3 1 0-2-2-2-2-5v1c1-1 0-1 0-2v-4c1 1 1 2 1 3h1c2 2 3 5 5 7v1h-2v1c2 1 2 0 4 0h1 1l4 4c0 2 1 3 1 4 0 0 1 1 2 1 1 1 2 1 3 2h-5c-1-1-2-1-2-2-1-1-2 0-4-1h-1l-5-4-1 1c1 1 2 2 4 3 1 0 1 0 1 1-4 1-4-2-7-4-1 0-2 0-2-1v-1c1 1 1 0 2 0 0-1 0-1-1-2v-2c0-1-1-2-1-2z" class="c"></path><path d="M215 325h4 0v1 1h1l2 1v2h1 1l1 4c0 1 0 1 1 2s2 2 4 2l-1 6c-1 0-1 0-2 1h-2c-1 0-1 1-2 2-2 2 0 6-4 7l1 1c1 0 1 1 2 1h2l1-2c2 1 3 2 5 1h1c3 0 4 0 6 1 0 0-1 1-1 2h-3c-2 0-3 1-4 1l-1 1-2-2c-2 0-6 1-8 1l-1-1c-2-1-4-1-6-1-2-1-3-1-5-1h0l-2-1 1-2-1-1h-2v1h-1c0-2-1-3-2-4v-1l1-1-2-2s-1 0-1 1l-1-2-2 1h-1c0-1 0-3 1-4h0l-1-2 3-3c1-1 1-2 2-3v-1c2-1 3-1 4-1 3-1 4-1 6-3h0c1 0 2-1 3-2h3 2l-1-1z" class="i"></path><path d="M199 342c-1-1-2-2-2-3h2c1-1 0-1 0-2-1 0-1 1-2 0l1-1c1-1 1-1 2-3v-1h4l-3 2 1 1v1c-2 1-2 2-2 4h2 0c1 3 2 4 3 5 0 2-1 2-2 3l-1 1c0 1 1 1 2 2v1h-2v1h-1c0-2-1-3-2-4v-1l1-1-2-2-1-1c0-1 1-1 2-2z" class="V"></path><path d="M199 342c1 0 1 0 1-1 1 1 1 2 1 2 0 2 1 4 1 5v1c0 1 1 1 2 2v1h-2v1h-1c0-2-1-3-2-4v-1l1-1-2-2-1-1c0-1 1-1 2-2z" class="N"></path><path d="M204 332h1c2 3 2 5 3 8h0c1 3 1 6-1 8l-1 1c-1 0-2 0-3-1 1-1 2-1 2-3-1-1-2-2-3-5h0-2c0-2 0-3 2-4v-1l-1-1 3-2z"></path><defs><linearGradient id="R" x1="226.168" y1="363.274" x2="215.764" y2="348.629" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#24211e"></stop></linearGradient></defs><path fill="url(#R)" d="M209 328c1 1 2 1 2 2s1 1 1 2h2v1l-1 2c0 1 1 3 0 4v1c-1 1 0 4-1 5 0 2-2 1-1 3 1 0 1 0 2-1 1 1 2 2 2 4h2l1 1 1 1v1l1 1c1 0 1 1 2 1h2l1-2c2 1 3 2 5 1h1c3 0 4 0 6 1 0 0-1 1-1 2h-3c-2 0-3 1-4 1l-1 1-2-2c-2 0-6 1-8 1l-1-1c-2-1-4-1-6-1-2-1-3-1-5-1h0l-2-1 1-2-1-1v-1c-1-1-2-1-2-2l1-1c1 1 2 1 3 1l1-1c2-2 2-5 1-8 1 1 1 1 2 1l1-1c0-1 1-2 0-3h-1l-1 1-1-1c1-1 3-2 3-3v-2l-1-1h-1v-3z"></path><path d="M205 353l2-2v3 1h-3l1-2z" class="F"></path><path d="M217 351l1 1 1 1v1l-2 1h0-1v-1c0-1 0-2-1-2v-1h2z" class="H"></path><path d="M211 350h1c0 1 1 2 1 3l2 1-2 1c-2 0-3 0-5-1 1-1 2-3 3-4z" class="R"></path><path d="M215 325h4 0v1 1h1l2 1v2h1 1l1 4c0 1 0 1 1 2s2 2 4 2l-1 6c-1 0-1 0-2 1h-2c-1 0-1 1-2 2-2 2 0 6-4 7v-1l-1-1-1-1h-2c0-2-1-3-2-4-1 1-1 1-2 1-1-2 1-1 1-3 1-1 0-4 1-5v-1c1-1 0-3 0-4l1-2v-1h-2c0-1-1-1-1-2s-1-1-2-2h-1 0c1 0 2-1 3-2h3 2l-1-1z" class="I"></path><path d="M219 336c0-3-1-8 0-11v1 1h1l2 1v2 6c-1 1-2 1-3 0z" class="E"></path><path d="M219 327h1l2 1v2c-1 0-2 0-3-1v-2z" class="K"></path><path d="M214 332c-1-1-1-1-1-3h1l2 1 1-1 1 1c-1 1-1 1-2 3 1 0 1 0 1 1s0 3-1 4h1c1 3 0 9 0 12v1h-2c0-2-1-3-2-4-1 1-1 1-2 1-1-2 1-1 1-3 1-1 0-4 1-5v-1c1-1 0-3 0-4l1-2v-1z" class="j"></path><path d="M222 330h1 1l1 4c0 1 0 1 1 2s2 2 4 2l-1 6c-1 0-1 0-2 1h-2c-1 0-1 1-2 2-2 2 0 6-4 7v-1-17c1 1 2 1 3 0v-6z" class="B"></path><path d="M849 230l47 1c-1 0-2 1-3 1h-2c0 1-1 2-2 3h-4c3 0 5 0 8 1 0 1 0 1-1 2-8 1-14 3-20 8-3 2-8 6-9 10v1l-4-2-1 1c0 1 0 3 1 4-2 0-3 1-4 1v2h-2c0 1-2 0-3 1-1-1-2-1-3-1l-1-1 1-1c-1-2-3-1-4-2v-1c0-2 1-2 2-2 0-1 0-1-1-1h-1c0-2 1-2 2-3v-2-2c1 0 1-4 1-4 1-2 0-5 1-8h0l1-5h0l1-1z"></path><path d="M854 247c-1-1-1-2-2-2v-1-2h2l1-2h1v2h-1v4c-1 0-1 1-1 1z" class="D"></path><path d="M858 242h5c1-1 1-1 2-1 2 1 6-1 9-1-1 1-3 2-4 3l-1 1c-2 0-9 0-10-1l-1-1z" class="h"></path><path d="M885 235l-18 1h-8-2c-2 0-5 0-6-1v-1-1h18 13c1-1 4-1 6-1v1c1 0 2-1 3-1 0 1-1 2-2 3h-4z" class="T"></path><path d="M856 242h2l1 1c1 1 8 1 10 1l1-1c0 1 1 2 2 3-3 2-8 6-9 10v1l-4-2-1 1c0 1 0 3 1 4-2 0-3 1-4 1v2h-2c0 1-2 0-3 1-1-1-2-1-3-1l-1-1 1-1c-1-2-3-1-4-2v-1c0-2 1-2 2-2l2-1c0 1 0 1 2 1 1-1 1-1 3-1l1-1h1c1-1 1-1 3-1h0 0c-1 0-2-1-2 0h-1-1l1-1v-2l-2 1c-1-1-1-1-2-1 0 0 0-1-1-1l1-1 4-1s0-1 1-1v-4h1z" class="F"></path><path d="M849 260h2c1 0 1 1 2 2h-3l-1-2z" class="D"></path><path d="M504 95l6-48c2 6 2 12 3 18l2 16 2 12c1 3 1 7 0 10v1c1 2 1 5 2 7s1 6 1 8l-3 3c-1 1-2 2-2 4l-3 2c1 2 1 4 1 6l2 5c0 3 2 6 4 9l1 4 2 2 3-1 1 2h1c1 1 3 1 5 2h0c-1 1-3 0-4 1l-2 1c-1 1-1 1-2 1l-1-1-3 6-1-3 1-2-1-1c-1 0-1-1-1-2h0l-1-1v2c-1 0-1-1-1-2-1 1-3 4-5 4v-7c-2-4-1-10-2-14-1 3 0 7-2 11-1 1-1 1-2 1h-1c0-1 1-2 1-3 0-2 1-4 1-6 1-1 1-2 2-4h-2v-4l1-1h-1c-1-2 0-4 0-5-1-2-1-3-2-4 0-1 0-1-1-2-1 0-1 0-2-1-1-2 0-3 0-5v-5c1-1 1-2 1-3s1-2 2-3c-1-3 0-7 0-10z" class="l"></path><path d="M511 104v1l1 1h0c0-1 0-1 1-2v1c-1 1-1 3-1 5h0l-1 3h0v-9z" class="Z"></path><path d="M507 133l1-1 1 2c-1 1-1 3-1 4h-2v-4l1-1z" class="P"></path><path d="M504 105v4 1 3c-1 1-1 1-1 2l-1 2-1-1v-5c1-1 1-2 1-3s1-2 2-3zm11-24l2 12c1 3 1 7 0 10 0-3-1-5-2-7-1-5-1-10 0-15zm2 23c1 2 1 5 2 7s1 6 1 8l-3 3c-1 1-2 2-2 4l-3 2v-9-3c1-2 1-2 2-3l1 1c-1 2-2 3-2 5h1l2-2h2c0-1 0-3-1-4v-9z" class="C"></path><path d="M504 124c0-1 0-1-1-2-1 0-1 0-2-1-1-2 0-3 0-5l1 1c1 0 1 2 2 2 1 1 3 0 4 1 1 0 3 12 3 14-1-1-1-2-1-3-1 1-1 2-1 3l-1-2-1 1h-1c-1-2 0-4 0-5-1-2-1-3-2-4z" class="B"></path><path d="M511 153h0l1 1v-2c0-1 1-1 2-1l1 2v-3h1v2h1v-2c0-1-1-1-1-2v-4h-1c-1-3-3-5-4-7l1-1c0 1 1 2 2 3v-2c0-1-1-1-1-2v-1l2 5c0 3 2 6 4 9l1 4 2 2 3-1 1 2h1c1 1 3 1 5 2h0c-1 1-3 0-4 1l-2 1c-1 1-1 1-2 1l-1-1-3 6-1-3 1-2-1-1c-1 0-1-1-1-2h0l-1-1v2c-1 0-1-1-1-2-1 1-3 4-5 4v-7z" class="Z"></path><path d="M518 157c2 0 4 0 6 1l1 1h1c-1 1-1 1-2 1l-1-1-3 6-1-3 1-2-1-1c-1 0-1-1-1-2z" class="P"></path><path d="M506 138h2c-1 2-1 3-2 4 0 2-1 4-1 6 0 1-1 2-1 3h1c1 0 1 0 2-1 2-4 1-8 2-11 1 4 0 10 2 14v7c2 0 4-3 5-4 0 1 0 2 1 2v-2l1 1h0c0 1 0 2 1 2l1 1-1 2c-1-1-2-3-3-4l-1 1-1 4 3-1c0 3-1 5-2 7-2 3-4 6-4 10h-1l-1 2v3c-1 0-2 0-3 1 0 2 1 5 0 7-2 1-3 2-5 1-1-1-2-2-2-3l-3-2v-2c-1-2-2-3-3-5-2-2-5-3-7-5 1 2 1 3 2 4v1l-3-3-2-2c-1 0-2-1-2-1v-1l-2-2v-1c-2-2-3-2-5-2l-7-7h4c-2-2-4-3-5-5l13 1 6-1c1 0 3-1 4-1s2 1 2 1c2 0 4-1 5-1h1v-2h2l1-1c1-1 2-4 3-5l3-10z" class="K"></path><path d="M485 163l1-1h4c0 1 0 0-1 1 0 1-1 1-1 2-1-1-2-1-3-2zm0-6c1 0 3-1 4-1s2 1 2 1c2 0 4-1 5-1-1 1-1 1-1 3-3 0-4 1-6 0h-1c-1 0-1-1-2-1l-1-1z" class="g"></path><path d="M487 172l-1-1-3-3 1-1c0-1 1-1 1-1v-1l-1-1h0l1-1c1 1 2 1 3 2 0 1 1 2 1 4 0 1-1 1 0 2l-2 1zm8-2v-1-1c1-1 1-2 1-3s-1-2-2-2v-1l1-1c1 1 3 2 3 4h1c0 1 1 2 1 3v1c1 2 1 2 1 4-1 0-2 0-3-1h-1 0v-1-1h-1-1z" class="C"></path><path d="M471 162c2 2 6 4 8 7v1l3 3-1 1-2-2v-1c-2-2-3-2-5-2l-7-7h4z" class="R"></path><path d="M485 157l1 1c-1 2-3 5-5 7l-3-2c-1-1-3-3-3-5h4l6-1z" class="D"></path><path d="M501 162c-1-1-2-1-2-2h2c1-1 1-1 1-2v-2l1-1 1 1v1h1v-2l2-2v-1c1 2 0 4 1 6l1 1 1-1c1 1 1 3 1 4h-1c0 2 0 6 1 7l1 1c0-3 2-5 2-7l3-1c0 3-1 5-2 7-2 3-4 6-4 10h-1c-1-4-4-10-7-13l-2-4z" class="E"></path><path d="M501 162c1 0 2 1 3 2l1 2h1l1-1v3h1l1-1v1c0 2 0 4 1 6h1c1-1 1-2 1-4 0-3 2-5 2-7l3-1c0 3-1 5-2 7-2 3-4 6-4 10h-1c-1-4-4-10-7-13l-2-4z" class="a"></path><path d="M503 166c3 3 6 9 7 13l-1 2v3c-1 0-2 0-3 1 0 2 1 5 0 7-2 1-3 2-5 1-1-1-2-2-2-3l-3-2v-2c-1-2-2-3-3-5-2-2-5-3-7-5 1 2 1 3 2 4v1l-3-3-2-2c-1 0-2-1-2-1v-1l1-1-3-3v-1l2 1c1 0 2 0 2 1 2 1 3 4 6 4 0-2-1-3-2-3l2-1c1 2 2 3 3 4h2 2v-1c-1-1-1-2-1-3v-1h1 1v1 1h0 1c1 1 2 1 3 1 0-2 0-2-1-4v-1c2 0 3 1 4 0-1-1 0-1-1-2h0z" class="T"></path><path d="M501 176v-1c1 0 1-1 2-1l1 1c0 1 0 2-2 2h0l-1-1z" class="C"></path><path d="M481 174l1-1-3-3v-1l2 1c2 2 4 4 5 6s1 3 2 4v1l-3-3-2-2c-1 0-2-1-2-1v-1z" class="m"></path><path d="M503 171c3 3 5 6 6 10v3c-1 0-2 0-3 1h0v-3c0-3 0-6-2-9-1-1-1-1-1-2z" class="J"></path><path d="M503 166c3 3 6 9 7 13l-1 2c-1-4-3-7-6-10l-1-2h-2v-1c2 0 3 1 4 0-1-1 0-1-1-2h0z" class="E"></path><path d="M498 179c1 0 1 0 1-1l2-2 1 1c0 2 1 2 1 3 1 2 0 1 2 3 1 0 1 0 1-1v3h0c0 2 1 5 0 7-2 1-3 2-5 1-1-1-2-2-2-3l-3-2v-2c-1-2-2-3-3-5h2c1-1 2-1 3-2z" class="G"></path><path d="M493 181h2c1-1 2-1 3-2l4 7h-6c-1-2-2-3-3-5z" class="R"></path><path d="M506 185h0c0 2 1 5 0 7-2 1-3 2-5 1-1-1-2-2-2-3h0l2-2c1 1 2 1 2 1 1 1 2 1 2 1h1v-1l-4-2v-1l4 1v-2z" class="i"></path><path d="M778 524v-4h0c1 0 1 0 2 1h1c1 3 1 7 2 11 1 17 0 33-3 49l-3 18c-1 5-3 10-5 15-1 6-3 12-5 18-2 7-5 14-8 21-7 14-14 28-23 41 0 1 0 0-1 1-2 1-4 4-5 6-1 1-1 2-2 3l-1-1v-1l-1-2v-2l2-2-1-1c-1-1-1-1-3-1 2-3 4-5 7-8l-2-1v1c-2 1-4 0-6-1-1 0-2-1-2-2v-1c1 0 2 1 3 1h1c-1-1-1-1-1-3h2c3-2 4-3 5-6l1-1 1-1c1-1 1-2 2-2 2-2 3-2 3-5 1 1 2 1 2 1 2-3 5-7 6-10 0-1 1-3 2-4s3-1 4-1l4-9 12-33 2 4v1c1-4 3-9 3-14 2-6 3-13 4-20 2-18 3-37 1-56z" class="W"></path><path d="M729 694c2 1 2 1 3 0l1-1c-1 2-3 6-3 8-1 1-1 2-2 3l-1-1v-1l-1-2v-2l2-2 1-2z" class="c"></path><defs><linearGradient id="S" x1="753.92" y1="652.015" x2="748.312" y2="649.7" xlink:href="#B"><stop offset="0" stop-color="#030302"></stop><stop offset="1" stop-color="#272724"></stop></linearGradient></defs><path fill="url(#S)" d="M768 609l2 4v1c-6 18-13 36-22 52l-12 22-3 5-1 1c-1 1-1 1-3 0l-1 2-1-1c-1-1-1-1-3-1 2-3 4-5 7-8l-2-1v1c-2 1-4 0-6-1-1 0-2-1-2-2v-1c1 0 2 1 3 1h1c-1-1-1-1-1-3h2c3-2 4-3 5-6l1-1 1-1c1-1 1-2 2-2 2-2 3-2 3-5 1 1 2 1 2 1 2-3 5-7 6-10 0-1 1-3 2-4s3-1 4-1l4-9 12-33z"></path><path d="M731 691c2 0 3-1 5-3l-3 5-1 1c-1 1-1 1-3 0l2-3z" class="J"></path><path d="M731 686l1-1v-1h2l-3 7-2 3-1 2-1-1c-1-1-1-1-3-1 2-3 4-5 7-8z" class="f"></path><path d="M748 659c-3 6-6 11-9 17-1 2-4 7-5 8h-2v1l-1 1-2-1c1-2 4-4 5-6l7-11 3-6c1-1 2-2 4-3z" class="i"></path><path d="M746 656c0-1 1-3 2-4s3-1 4-1l-4 8c-2 1-3 2-4 3l-3 6-7 11c-1 2-4 4-5 6v1c-2 1-4 0-6-1-1 0-2-1-2-2v-1c1 0 2 1 3 1h1c-1-1-1-1-1-3h2c3-2 4-3 5-6l1-1 1-1c1-1 1-2 2-2 2-2 3-2 3-5 1 1 2 1 2 1 2-3 5-7 6-10z" class="X"></path><path d="M738 665c1 1 2 1 2 1-1 3-4 6-5 9v-1h-1l-2-1 1-1c1-1 1-2 2-2 2-2 3-2 3-5z" class="L"></path><path d="M732 673l2 1h1v1c-2 3-3 6-6 8-2 1-3 1-4 0s-1-1-1-3h2c3-2 4-3 5-6l1-1z" class="F"></path><path d="M620 161c-1-1-1-2-1-2l-2-2-1-1c0-2 1-1 0-2-1 0-1-1-2-1v-1c-1-2-2-3-2-5l-2-2c1 0 2 0 3 1l2 2c5 5 8 11 12 17l7 14 2 5 3 15v3l1 6h-2c-2-2-2-2-3-5h-1l-3 2h-1v2c-1 2-3 4-3 7l-3 2h-1l-1-1c-2 0-2 1-3 2l-1-1-1 1h-2l-1 1h0c-1 0-1 0-2 1 0 0-1 1-1 2-1 0-1 0-2 1h-1c-2 0-2 0-3-1-2 2-4 2-7 4l1-2h1l1-2c0-1-1-2-2-3 0 0-1-1-2-1l-1 1-2-1-2 1c-1 0-1 0-1-1 2-2 4-4 6-5 0-1 1-2 1-3 0-3 1-4 1-7l1-1c2-3 5-5 8-7l4-3h1c1-1 2-1 2-3h1l2-2c0-1 1-3 2-4 1-2 1-4 1-5 1-2 1-5 1-7l-1-2-1-7z" class="T"></path><path d="M618 212l3-4c1-1 1-1 1-2l3-6c0 3-1 6-3 8v3l-2 2c-1 0-1 0-2-1z" class="N"></path><path d="M618 212c1 1 1 1 2 1 0 1-1 2-2 3l-1 1h-2l-1 1h0c-1 0-1 0-2 1 0 0-1 1-1 2-1 0-1 0-2 1h-1c-2 0-2 0-3-1 5-3 9-5 13-9z" class="k"></path><path d="M620 200c0-1 1-1 1-2s-1-1-1-2c2-1 3-2 5-3-2 7-3 11-7 17l-1-2c1-1 1-2 1-3l1-1-2-1c2-1 2-1 3-3z" class="L"></path><path d="M633 187c1 1 1 2 0 4h0c0 2 0 4 1 5l1 1c1 2-1 6 2 7l2-2 1 6h-2c-2-2-2-2-3-5h-1l-3 2h-1-2c-1-1 0-2 0-3l-2-1v-1c1-1 1-1 1-2 1-1 1-2 1-3l2-1c0-1-1-2-1-3h0v-1h4v-1h-2l-1-1c1-1 2-1 3-1z" class="P"></path><path d="M628 195l2-1 3 3c0 2 0 2-1 4h-2l-1-1-1-5z" class="K"></path><path d="M614 203h2 1l2 1-1 1c0 1 0 2-1 3l1 2c-6 5-11 9-18 13l1-2c0-1-1-2-2-3 0 0-1-1-2-1l-1 1-2-1-2 1c-1 0-1 0-1-1 2-2 4-4 6-5h1l4-3c1-1 2-2 3-4l2-1h1v2h1c2-1 3-1 5-3z" class="H"></path><path d="M614 203h2c-2 3-5 4-8 5l-1 2v-3l1-1h1c2-1 3-1 5-3z" class="Y"></path><path d="M607 204h1v2l-1 1v3l1 1h-3 0c0 1 1 1 2 1-1 1-2 1-3 1s-2 0-3 1h0l-2-1c1 0 2-1 2-2l1-2c1-1 2-2 3-4l2-1z" class="a"></path><path d="M607 204h1v2l-1 1c-2 2-3 3-6 4l1-2c1-1 2-2 3-4l2-1z" class="c"></path><path d="M602 209l-1 2c0 1-1 2-2 2l2 1h0c1-1 2-1 3-1l1 1c-2 2-3 1-4 2-1 0-1 1-2 2 0 0-1-1-2-1l-1 1-2-1-2 1c-1 0-1 0-1-1 2-2 4-4 6-5h1l4-3z" class="N"></path><path d="M599 213l2 1h0c1-1 2-1 3-1l1 1c-2 2-3 1-4 2-1 0-1 1-2 2 0 0-1-1-2-1l-1 1-2-1 2-2 1 1h1s0-1 1-2v-1z" class="e"></path><path d="M622 170v1 13c0 1-1 3 0 5v2l2 1c1-1 1-1 1-2v3h0c-2 1-3 2-5 3 0 1 1 1 1 2s-1 1-1 2c-1 2-1 2-3 3h-1-2c-2 2-3 2-5 3h-1v-2h-1l-2 1c-1 2-2 3-3 4l-4 3h-1c0-1 1-2 1-3 0-3 1-4 1-7l1-1c2-3 5-5 8-7l4-3h1c1-1 2-1 2-3h1l2-2c0-1 1-3 2-4 1-2 1-4 1-5 1-2 1-5 1-7z" class="d"></path><path d="M614 203v-1c1-1 4-2 6-2-1 2-1 2-3 3h-1-2z" class="V"></path><path d="M607 201l12-5c-1 1-3 3-5 4 0 0-1 0-1 1-2 1-4 2-5 3h-1-1v-1l1-2z" class="f"></path><path d="M615 188h1l2-2c0-1 1-3 2-4 1-2 1-4 1-5 0 6-1 11-5 15-2 2-3 3-4 5-1 1-1 1-2 1l-2 2-3 1-1-1c2-2 4-3 4-6l4-3h1c1-1 2-1 2-3z" class="M"></path><path d="M599 202l1-1c2-3 5-5 8-7 0 3-2 4-4 6l1 1 3-1-1 1-1 2v1h1l-2 1c-1 2-2 3-3 4l-4 3h-1c0-1 1-2 1-3 0-3 1-4 1-7z" class="O"></path><path d="M604 200l1 1 3-1-1 1-1 2v1h1l-2 1c-1 0-2-1-3-2 1-1 1-2 2-3z" class="X"></path><path d="M620 161c-1-1-1-2-1-2l-2-2-1-1c0-2 1-1 0-2-1 0-1-1-2-1v-1c-1-2-2-3-2-5l-2-2c1 0 2 0 3 1l2 2c5 5 8 11 12 17l7 14 2 5 3 15v3l-2 2c-3-1-1-5-2-7l-1-1c-1-1-1-3-1-5h0c1-2 1-3 0-4l-2-2v-1c0-1-1-2-2-3h0c-1 2-1 5-1 8 0 1-1 1-2 2 1 0 1 0 1 1v2c-1 0-1 0-2-1h0v-3c0 1 0 1-1 2l-2-1v-2c-1-2 0-4 0-5v-13-1l-1-2-1-7z" class="Z"></path><path d="M627 173l1-1 1 1v2s-1 0-2 1l-1-1 1-2z" class="n"></path><path d="M620 161c4 7 5 14 5 23v6c0 1 0 1-1 2l-2-1v-2c-1-2 0-4 0-5v-13-1l-1-2-1-7z" class="I"></path><path d="M240 512h1c1-1 3-12 3-15v4c1 1 1 1 2 1l-1 6 3 1 1 1h1c0 4-1 9-1 13-1 15-1 32 1 47v5l2 1c1 1 1 2 2 2 0 2 0 5 1 8 0 0 0 1 1 2-1 3 0 7 1 10l1 1 2-2v2h-2c0 4 3 7 3 11 2 0 3 1 4 2h1c-1 2-1 3 0 5l-1 1h0l-2-2c0 1-1 1 0 2 2 5 6 10 7 15h-2l-1 1c-1 0-1-1-1-1l-1 1c1 1 2 3 2 5 0 1 0 1 1 2l5 10c1 1 2 2 2 4v1h0v2l-1-1h0c0-2-1-2-2-3h-1c0 2 2 4 2 6 2 4 5 9 7 12 1 3 3 6 4 9l6 9c-1 1-2 2-2 3l-1-1c-3 0-7-6-9-8-1-1-2-2-2-3-1-2-2-3-3-5l-4-7v-1c-1-1-1-1-1-2-1-1-2-2-2-3-1-4-4-8-5-11-8-19-13-36-17-56-4-16-6-32-6-48 0-6-1-11 1-17l1-12v-7z" class="W"></path><path d="M284 681l6 9c-1 1-2 2-2 3l-1-1-7-11h4z" class="I"></path><path d="M240 512h1c1-1 3-12 3-15v4c1 1 1 1 2 1l-1 6c0 2-1 5-1 7 1 2-1 3-1 5l1 1v1c0 1 0 2-1 3-1 2 0 6-1 9 0-7 1-15 0-22-1 3-1 5-1 8-1 3-1 7-2 11h0l1-12v-7z" class="X"></path><path d="M244 521v1h2v4c0 12-1 24 0 36 0 4 0 9 1 13v1h-3c0-1-1-2 0-4h0c-1-2 0-5-1-8-1-10-1-20-1-30 1-3 0-7 1-9 1-1 1-2 1-3v-1z" class="H"></path><path d="M247 575c2 12 3 24 7 36 1 6 3 11 5 17-1-1-1-1-1-2-1-1-1-2-1-2l-2-1v3c1 1 1 2 1 3-2-3-3-7-4-11-4-14-6-27-8-42h3v-1z" class="Q"></path><path d="M256 629c0-1 0-2-1-3v-3l2 1s0 1 1 2c0 1 0 1 1 2l4 11 1-1v-1c1 1 2 2 3 2 0 1 0 1 1 2l5 10c1 1 2 2 2 4v1h0v2l-1-1h0c0-2-1-2-2-3h-1c0 2 2 4 2 6 2 4 5 9 7 12 1 3 3 6 4 9h-4c-2-4-5-9-7-13l-11-23c-2-5-5-11-6-16z" class="F"></path><path d="M263 639l1-1v-1c1 1 2 2 3 2 0 1 0 1 1 2l5 10c1 1 2 2 2 4v1h0v2l-1-1h0c0-2-1-2-2-3h-1c0 2 2 4 2 6-4-6-7-14-10-21z" class="T"></path><path d="M245 508l3 1 1 1h1c0 4-1 9-1 13-1 15-1 32 1 47v5l2 1c1 1 1 2 2 2 0 2 0 5 1 8 0 0 0 1 1 2-1 3 0 7 1 10l1 1 2-2v2h-2c0 4 3 7 3 11 2 0 3 1 4 2h1c-1 2-1 3 0 5l-1 1h0l-2-2c0 1-1 1 0 2 2 5 6 10 7 15h-2l-1 1c-1 0-1-1-1-1l-1 1c1 1 2 3 2 5-1 0-2-1-3-2v1l-1 1-4-11c-2-6-4-11-5-17-4-12-5-24-7-36-1-4-1-9-1-13-1-12 0-24 0-36v-4h-2v-1l-1-1c0-2 2-3 1-5 0-2 1-5 1-7z" class="C"></path><path d="M245 508l3 1 1 1-3 16h0v-4h-2v-1l-1-1c0-2 2-3 1-5 0-2 1-5 1-7z" class="L"></path><path d="M250 575l2 1c1 1 1 2 2 2 0 2 0 5 1 8 0 0 0 1 1 2-1 3 0 7 1 10l1 1 2-2v2h-2c0 4 3 7 3 11 2 0 3 1 4 2h1c-1 2-1 3 0 5l-1 1h0l-2-2c0 1-1 1 0 2 2 5 6 10 7 15h-2l-1 1c-1 0-1-1-1-1l-1 1c-2-4-3-9-5-13-5-15-8-31-10-46z" class="H"></path><path d="M261 610c2 0 3 1 4 2h1c-1 2-1 3 0 5l-1 1h0l-2-2 1-1c0-1 0 0-1-1l-1-2c-1 0-1-1-1-2z" class="b"></path><path d="M826 244c1-1 2-1 3-1 1 1 1 0 2 0 2 0 3 0 4 1v3c1 1 2 1 4 1l2-1c2 1 3 1 4 1v2 2c-1 1-2 1-2 3h1c1 0 1 0 1 1-1 0-2 0-2 2v1c1 1 3 0 4 2l-1 1 1 1c1 0 2 0 3 1 1-1 3 0 3-1h2v-2c1 0 2-1 4-1-1-1-1-3-1-4l1-1 4 2c-1 3-1 5-1 8l4 3c1 0 1-1 2-1h0l1-3c0 2 0 8 1 10v9l-1 2h-1c-4-1-9-1-14-1l-25-1c1 4 1 7 3 10l1 2c-3 2-10-1-11 2v2 3l1 1c2-1 6-1 10-1v2l3-1h1l-1 1c0 1-1 2-1 3l-9 14-11 1-3 1v3c-1-1-2-2-2-3-1 0-1-3-1-4l-1-12 1-18v-12c2-10 9-19 14-27l3-6z" class="D"></path><path d="M857 269c1 1 1 2 1 4l-1 1-1-1c0-2 0-2 1-4z" class="L"></path><path d="M856 269l1-1v1c-1 2-1 2-1 4 0-1-1-1-2-2v-1l2-1z" class="F"></path><path d="M830 305c-1 1-1 3-2 4-3 3-3 5-6 6 2-3 3-6 5-8 0-2 2-2 3-2z" class="d"></path><path d="M833 252l1-1h4v1c-1 1-2 2-2 4v2c-2 0-2-1-4-2 2-1 2-1 2-2l-1-2z" class="L"></path><path d="M833 280l-12-3c6 0 12-1 18 0h-6l-1 2h5l-4 1z" class="U"></path><path d="M825 288c-2-1-3-2-4-3v-1h-2c0 1 0 2-1 2l-1-1v-2c3-1 7 0 10 0h-4c1 1 3 2 3 3l-1 2z" class="F"></path><path d="M859 260v1l-1 1c0 2-1 3 0 5 0 1 0 0-1 1l-1 1c-2-2-1-3-1-5l-2-1h2v-2c1 0 2-1 4-1z" class="Y"></path><path d="M827 258c1-2 1-3 3-5h1l1-1h1l1 2c0 1 0 1-2 2-1 0-3 2-4 3l-1-1z" class="M"></path><path d="M862 265l4 3c1 0 1-1 2-1l-1 5c-2 1-3 2-5 3v-10z" class="W"></path><path d="M823 303c2-1 6-1 10-1v2h0c-3 3-4 7-6 10-1 1-2 3-3 4-1 0-5 1-6 1l4-4c3-1 3-3 6-6 1-1 1-3 2-4s1-1 1-2h-8z" class="O"></path><path d="M862 275c2-1 3-2 5-3 0 2-1 4-3 5-1 1-1 1-2 1h0v3h-5-1-5l1-3h-2c2 0 5 0 6-1h1 1c1 1 3 1 4 0v-2z" class="S"></path><path d="M852 278h3c1 1 1 2 1 3h-5l1-3z" class="B"></path><path d="M839 277l11 1h2l-1 3c-5 0-10-1-14-2h-5l1-2h6z" class="P"></path><path d="M827 268l1 1h0c4-1 12-1 16 0 2 0 5 0 6-1h3l1 2v1h-5-15c-2 0-4-1-7-1h-1l1-2z" class="Q"></path><path d="M869 264c0 2 0 8 1 10v9c-1-1-1-1-1-2h-1-11 5v-3h0c1 0 1 0 2-1 2-1 3-3 3-5l1-5h0l1-3z" class="U"></path><path d="M869 264c0 2 0 8 1 10v9c-1-1-1-1-1-2h-1l-3-2 1-1c1 0 1 0 2-1 0-4 1-7 0-10l1-3z" class="a"></path><path d="M837 279c4 1 9 2 14 2h5 1 11 1c0 1 0 1 1 2l-1 2h-1c-4-1-9-1-14-1l-25-1c1 4 1 7 3 10-1 0-1 1-2 1l-2-2c-1 0-2 0-4-1l1-1h4 0l-1-2h-2-1l1-2c0-1-2-2-3-3h4c3-1 6 0 9-1h2 3c3 0 6 1 10 1h2 5 3v-1c-6-1-12 1-18 0-1-1 0-1-1-1h-1-4v1c-1-1-2-1-3-2h-1l4-1z" class="Y"></path><path d="M827 268c0-1 1-2 1-2 1-1 3-3 4-3h3c3-1 7 0 11-1h0l1 1c1 0 2 0 3 1 1-1 3 0 3-1l2 1c0 2-1 3 1 5l-2 1-1-2h-3c-1 1-4 1-6 1-4-1-12-1-16 0h0l-1-1z" class="a"></path><path d="M835 263c3 0 9-1 12 2-1 1-2 1-3 2-2 0-3 0-4-1-1 0-3 1-5 1-1-1-2-1-3-2v-1l3-1z" class="B"></path><path d="M809 289c0-2 1-3 1-6 0 0 0-1 1-1-1-4 1-8 3-11 0 2 0 3-1 5l1 1c-1 5-2 10-1 14v5c0 1 1 3 0 4 1 2 1 4 0 5 0 2-1 3-1 5v5 7h3l-3 1v3c-1-1-2-2-2-3-1 0-1-3-1-4l-1-12 1-18z" class="R"></path><path d="M810 302v-2c1 0 2-1 3 0h0v2h-3 0z" class="B"></path><path d="M813 296s-1 1-1 2h0-2c-1-1-1-3-1-4 2-2 2-2 4-3v5z" class="S"></path><path d="M812 315l-2-2v-3l-1-1c0-2 0-3 1-5 1-1 1 0 3 0v1c0 2-1 3-1 5v5z" class="M"></path><path d="M826 244c1-1 2-1 3-1 1 1 1 0 2 0 2 0 3 0 4 1v3c1 1 2 1 4 1l2-1c2 1 3 1 4 1v2l-1 1h-6-4l-1 1h-1l-1 1h-1c-2 2-2 3-3 5l1 1c-3 3-5 6-6 9l-1 1c-2 0-3 1-3 1l-4 7-1-1c1-2 1-3 1-5-2 3-4 7-3 11-1 0-1 1-1 1 0 3-1 4-1 6v-12c2-10 9-19 14-27l3-6z" class="i"></path><path d="M817 267v-3c0-1 2-2 3-3v3l-3 3z" class="T"></path><path d="M820 264l1 1-3 3v2l-4 7-1-1c1-2 1-3 1-5 1-2 2-3 3-4l3-3z" class="C"></path><path d="M841 247c2 1 3 1 4 1v2l-1 1c-3 0-8-1-11 0h-1v-1l1-2h6l2-1z" class="Z"></path><path d="M826 244c1-1 2-1 3-1 1 1 1 0 2 0 2 0 3 0 4 1v3c1 1 2 1 4 1h-6l-1-2c-1 0-2 0-3 1-2 1-3 2-6 3l3-6z" class="N"></path><path d="M820 261c3-5 6-9 11-11-1 1-2 3-4 4-1 2-2 3-3 5v1l3-2 1 1c-3 3-5 6-6 9l-1 1c-2 0-3 1-3 1v-2l3-3-1-1v-3z" class="E"></path><path d="M137 230l102 1c3-1 11-2 14 0 5 1 10 0 15 1h33c13 0 26 1 40 0v4 4 1l1 1c0 5 1 11 0 16v13c-3 1-6 1-8 2l-1-1h-1l-5 3v4l-5 3c-2-1-3-1-5-1-2 2-4 4-7 5v1c-3 3-7 6-10 9-13 13-21 28-25 46l2 2c0 1-1 2-1 3-6 1-12 1-17 0l-2 1-1-1-1 1c0 1-1 3-2 4l-1-1h-1l-1 2h-4l-1 1-1 1h-3-1-1c-1 1-1 1-2 1-2-1-3-1-6-1h-1c-2 1-3 0-5-1l-1 2h-2c-1 0-1-1-2-1l-1-1c4-1 2-5 4-7 1-1 1-2 2-2h2c1-1 1-1 2-1l1-6c-2 0-3-1-4-2s-1-1-1-2l-1-4h-1-1v-2l-2-1h-1v-1-1h0-4l1 1h-2-3l-1-4h1c0-3 0-15-1-17h-1-7c1-1 2-2 2-4l-1-1c-1 0-2 0-3 1l-1 1h-1v-2l1-1c1-1 1-1 2-3-3-2-8 1-11-1v-4l1-8-30 2-4-1-1-1 1-1 1 1v-1h4 1l-1-1-2-2v-1c1-1 0-2 0-3-1-1-2-2-4-3h0v-1c0-2-1-3-1-5l-1-1c0 1-1 1-2 2-1-4-2-8-4-11-4-7-10-11-17-15-3-1-6-2-8-4 1-1 2-1 3-1h3l-1-1c1-2 1-2 3-2-2-1-2-1-3-3h9z"></path><path d="M161 241c3 1 5 1 7 1h1 2l1 1h-1-1c-2 0-7 0-10 1h-1l2-3z" class="m"></path><path d="M335 236h6v4c-3 1-5 1-8 2l5-5-3-1z" class="W"></path><path d="M312 248c2-1 4-1 6-1-1 1-2 1-2 3-1 1-3 1-4 1l-3 3c-2 0-3 0-4-1h-2c4 0 6-2 9-5z" class="Q"></path><path d="M230 243l17 1c-1 0-2 1-3 1-2 1-5 3-7 3l2-2h0l-1-1c-2-1-4 0-7 0h0l-1-1v-1z" class="C"></path><path d="M235 285l-1-18c0-3-1-7 1-9h3l-2 1c0 1 1 2 2 2l-2 1c0 1 0 2 1 2l-1 1v1h2v1h-2c-2 2-1 12-1 16 0 1 1 2 0 2z" class="P"></path><path d="M160 244c3-1 8-1 10-1v3c1 1 2 0 3 1-2 0-3 0-4 1l-2 2h-1c0-1 0-3-1-3-2-1-3-2-5-3z" class="D"></path><path d="M156 246l-9-6 14 1-2 3c-1 1-2 1-3 2z" class="h"></path><path d="M330 254h0c2 1 3 0 5-1l1 1c-1 3-3 6-1 9h1l-2 2c-1-1-1-1-2-1 0-2 0-6-2-8v-2z" class="M"></path><path d="M231 245h0c3 0 5-1 7 0l1 1h0l-2 2c-2-1-7 2-8 3v-2c0-1 1-1 2-2h1v-1h-3c-1 1-2 1-3 1h-1v-1h1c2-1 3-1 5-1z" class="E"></path><path d="M330 256h0c-1 1 0 1-1 1s-1-1-2-1h-1-3c0 2 3 2 4 3-2 1-2 0-3 0-1-1-2-1-2-1-2-1-3-3-3-4l1-1c2 0 7 0 9 1h1v2z" class="d"></path><path d="M215 294c1 0 0 2 1 4 0 0 1 1 2 1h0v-7l1-1v14c-1 0-2 1-3 1l-1 1c0-1 0-1 1-2-2-2-1-9-1-11z" class="Q"></path><path d="M341 240v1c0 3 1 5 0 7-3 1-3 1-6 0l-3-3c0-2 0-2 1-3 3-1 5-1 8-2z" class="g"></path><path d="M236 328c1 1 1 2 3 2h1v5c5-1 11-1 17 0h-1-9c-1 1-1 2-1 2 1 1 3 1 5 1l-14 1c-1 0-1 0-1-1-1-3-1-6 0-10z" class="W"></path><path d="M320 244c2 1 9 2 11 1h1l3 3c3 1 3 1 6 0v2c-2 0-4 0-5 1l-15-2c-2 0-3 0-5 1 0-2 1-2 2-3l2-3z" class="C"></path><path d="M318 247h6v1c-1 1-2 1-3 1-2 0-3 0-5 1 0-2 1-2 2-3z" class="K"></path><path d="M257 244l21-1c0 1 0 1-1 2s-2 2-3 2c-1 1-5 3-7 3l-2-1c-4-1-7-3-10-4h-1l1-1h2z" class="n"></path><path d="M190 244c1-1 2-3 3-4l2 1c10 3 19 16 24 25-6-5-11-12-17-17l-3 1-2-2h-3c-1-1-2-1-4-1l1-1-1-2z" class="C"></path><path d="M190 244c1-1 2-3 3-4l2 1-1 1c3 2 5 4 8 7l-3 1-2-2h-3c-1-1-2-1-4-1l1-1-1-2z" class="F"></path><path d="M190 244c1-1 2-3 3-4l2 1-1 1v1c0 2 2 3 3 5h-3c-1-1-2-1-4-1l1-1-1-2z" class="k"></path><path d="M229 251l-5 1c-2 0-6 1-8 0-2-4-6-7-8-9h11 11v1l1 1c-2 0-3 0-5 1h-1v1h1c1 0 2 0 3-1h3v1h-1c-1 1-2 1-2 2v2z" class="n"></path><path d="M219 243h11v1l1 1c-2 0-3 0-5 1h-1v1c-1 1-2 1-4 0h0c1 0 1 0 2-1h-4v-2-1z" class="W"></path><path d="M221 256l8 1 1 11-1 6v-3h0-2l-1 2h0l-1 2-1-1v-1l-1-2v-1c-2-1-2-1-4 0v-9h0c0-1-1-3 0-4l2-1z" class="d"></path><path d="M226 268c0-1 0-2 1-3l-1-1s0-1 1-1l-1-1v-1h3v3l-2 1h0 2c0 1 0 2 1 3l-1 6v-3h0-2l-1 2h0v-5z" class="C"></path><path d="M219 261h0c0-1-1-3 0-4l2-1 3 1v3c-1-1-1-1-3-2v1h0c1 0 2 1 3 2h0c-1 1 0 3 0 4-1 1 0 2 0 3h2v5l-1 2-1-1v-1l-1-2v-1c-2-1-2-1-4 0v-9z" class="O"></path><path d="M219 261c2 1 2 2 3 3l1 1c1 1 0 4 0 6v-1c-2-1-2-1-4 0v-9z" class="P"></path><path d="M128 230h9c1 2 1 2 4 2h14 7c3 0 6 1 9 0 1 1 1 2 0 3-2 2-6 1-8 1h-21c-5 0-9-1-13 0l-1-1c1-2 1-2 3-2-2-1-2-1-3-3z" class="E"></path><path d="M128 230h9c1 2 1 2 4 2h14 7l-31 1c-2-1-2-1-3-3z" class="F"></path><path d="M229 344c4-1 8 0 12 0h24c2 0 6 1 8 0 1 0 1-1 2-2l2 2c0 1-1 2-1 3-6 1-12 1-17 0l-2 1-1-1-25 1h-6c-1 0-1-1-2-1 1-1 1-2 2-2h2c1-1 1-1 2-1z" class="K"></path><path d="M159 244h1c2 1 3 2 5 3 1 0 1 2 1 3h1l2-2c1-1 2-1 4-1 1 2 0 3-1 5v1s1 1 2 1h-3l-1 1h0 0c-2-1-3-1-5-1 0 1-1 1-1 2h2v2h3c0 1-1 3-2 3-1 1-2 0-3 2h3l-1 2v1h-1l-2-2c0-2 0-2 1-3h2l2-2h-2 0c-1 1-2 1-3 1v1c-2 2-2 2-2 4-1-6-1-11-6-16 1-2 1-2 1-3 1-1 2-1 3-2z" class="I"></path><path d="M169 248c1-1 2-1 4-1 1 2 0 3-1 5v1s1 1 2 1h-3l-1 1h0 0c-1-1-3-1-5-1h0v-2 1l2-2h1c1 0 2-1 2-2l-1-1z" class="F"></path><path d="M219 270c2-1 2-1 4 0v1l1 2-1 1 1 19c0 6 1 14 0 20-1 1-2 3-4 3 0-2 0-3-1-4v-1-6-14-20-1z" class="K"></path><path d="M219 270c2-1 2-1 4 0v1l1 2-1 1c-1 1-1 1-2 1h-1l1-2c0-1-1-1-2-2v-1z" class="Z"></path><path d="M247 244c2-1 7 0 10 0h-2l-1 1h-1c3 3 11 7 15 7l2-1h16c2-1 4 1 6 1-6 0-11 0-16 1-4 0-9 0-14-1h0c-3 1-12 1-15 0l-1-2-2 1-2 1h-1v-1c-1 0-2 1-3 2v-1h-3-4c-4 1-8 1-12 1 2-1 5 0 7-1 6-1 13-3 18-6v-1c1 0 2-1 3-1z" class="g"></path><path d="M246 250h0c1 0 2-1 3-1 3-2 5 0 8 1h2c1 1 2 1 3 2-3 1-12 1-15 0l-1-2z" class="U"></path><path d="M219 354c4-1 2-5 4-7 1 0 1 1 2 1h6l25-1-1 1c0 1-1 3-2 4l-1-1h-1l-1 2h-4l-1 1-1 1h-3-1-1c-1 1-1 1-2 1-2-1-3-1-6-1h-1c-2 1-3 0-5-1l-1 2h-2c-1 0-1-1-2-1l-1-1z" class="J"></path><path d="M245 349v-1h7l1 1h-2c-1 0-1 0-2 1h-1l1 1h2l-1 2h-4l-1 1-1 1 2-4-1-2z" class="I"></path><path d="M244 350l1-1 1 2-2 4h-3c-1-1-1-2-2-2h-1l-3-3c2-1 7 0 9 0h0z" class="c"></path><path d="M244 350l1-1 1 2-2 4h-3c-1-1-1-2-2-2l2-1c1 0 2 0 3-1v-1z" class="V"></path><path d="M222 356l1-1v-2l1-1v-2l2-1h1 0c0 1 1 1 1 2h2l1-2c1 0 2 1 3 1 0 1 0 2-1 3v1h-1l-1 1h-1c-2 1-3 0-5-1l-1 2h-2z" class="D"></path><path d="M180 250h-2c-1-1-1-2-1-3h-1l1-1 1-1-2-2v-5c1 0 2-1 3 0l2 7 2 2h5l2-3 1 2-1 1v1c2 1 4 0 5 2s3 4 4 5 2 2 2 3c2 2 4 3 4 6-1-1-1-2-3-3l1 1c0 2 1 3 2 4-2 1-2 1-4 1h0l-4-6c-2-2-4-4-7-4-2 0-2 0-3-1l-1-1c1-1 2-1 2-3-2-2-6-2-8-2z" class="G"></path><path d="M180 250c0-2 0-3-1-4l1-1v1l2 2h1c2-1 4 0 6 1h1c0 3 1 3 2 5 1 1 2 2 4 2 2 2 4 4 6 5l1 1c0 2 1 3 2 4-2 1-2 1-4 1h0l-4-6c-2-2-4-4-7-4-2 0-2 0-3-1l-1-1c1-1 2-1 2-3-2-2-6-2-8-2z" class="C"></path><path d="M238 258c4 0 8-1 12-1h36 14c4 0 8 0 12 2l1 3h-1c-4-2-9-1-14-1h-27c-11 0-22-1-33 0-1 0-2-1-2-2l2-1zm103-17l1 1c0 5 1 11 0 16v13c-3 1-6 1-8 2l-1-1h-1c-5-4-12-6-17-9l-1-4c0-2-1-4 1-6 1 1 0 3 1 5 1 1 3 2 5 2l11 4c1 0 1 0 2 1l2-2h0c2-4 0-9 0-12 1-1 3-1 5-1v-2c1-2 0-4 0-7z" class="K"></path><path d="M333 272c1-1 4-2 6-2v-1c-1-1-2-1-2-2 1-1 1-1 3-2 2 0 0-4 1-5 0-1 1-2 1-2v13c-3 1-6 1-8 2l-1-1z" class="E"></path><path d="M278 243l6 1c4 1 6-1 10-1h12c4 0 10-1 14 1l-2 3c-2 0-4 0-6 1-3 3-5 5-9 5h0-4c-5 0-8-2-12-3-2-1-5-2-7-3-1-1-1-1-3-2h0c1-1 1-1 1-2z" class="K"></path><path d="M296 246c2-1 3-2 6-1l6 1c2 0 2-2 3 0v1 1h1c-3 3-5 5-9 5h0-4c-2-2-6-3-9-5 2-1 4-1 6-2h0z" class="C"></path><path d="M296 246h1c2 2 4 3 6 4v-2h1c1 1 1 1 2 1-1 1-2 1-3 2-1-1-2-1-3-2h-4 0c2 2 5 2 7 4h-4c-2-2-6-3-9-5 2-1 4-1 6-2h0z" class="b"></path><path d="M149 254a57.31 57.31 0 0 0-11-11c-2-2-4-3-6-4l1-1c7 0 16 6 22 11 5 5 5 10 6 16l-1 10c-1-1-2-2-4-3h0v-1c0-2-1-3-1-5l-1-1c0 1-1 1-2 2-1-4-2-8-4-11l1-2z" class="l"></path><path d="M149 254c3 4 4 7 5 11 0 1-1 1-2 2-1-4-2-8-4-11l1-2z" class="d"></path><path d="M174 254h1c2-1 4-1 5-1s1 1 1 1l2-1c1 1 2 3 2 4v1h-3l-1 2c2 1 3 0 5 1l2 2c3 0 5 2 8 4v1c-1 0-2 1-4 1h-10 0l-2 1h0c-2 0-5-1-7 0h-3-1-1l-1-1-1 1-1-1c0-2-1-2 0-3h1v-1l1-2h-3c1-2 2-1 3-2 1 0 2-2 2-3h-3v-2h-2c0-1 1-1 1-2 2 0 3 0 5 1h0 0l1-1h3z" class="Y"></path><path d="M169 258h-3v-2h-2c0-1 1-1 1-2 2 0 3 0 5 1h0 0c1 1 1 2 2 2s1 0 2 1h1l1 1v1h-4v-1c-1 0-2 0-3-1z" class="L"></path><path d="M173 266h-2c-2 0-3-3-5-4 5 0 10 1 14 0 2 0 3 1 5 1v1l2 1v1c-2 0-3 0-4-1l-1 1h-2c-1 0 0 0-1 1h-4v-1l1-1h-2l-1 1z" class="G"></path><path d="M185 263h3c3 0 5 2 8 4v1c-1 0-2 1-4 1h-10-1-1-6l-1-1v-2l1-1h2l-1 1v1h4c1-1 0-1 1-1h2l1-1c1 1 2 1 4 1v-1l-2-1v-1z" class="k"></path><path d="M226 273l1-2h2 0v3l1 41v23c-2 0-3-1-4-2s-1-1-1-2l-1-4h-1-1v-2l-2-1h-1v-1h2v-2c0-1 1-1 2-2h0v-3h0c1-2 1-4 1-6 1-6 0-14 0-20l-1-19 1-1v1l1 1 1-2h0z" class="g"></path><path d="M230 315v23c-2 0-3-1-4-2v-6c1 0 1-1 1-2l1-1v1c0 1 0 1-1 2l2 2v-4c0-4 0-8 1-13h0z" class="W"></path><path d="M225 319c1 1 1 3 1 5-1 1-1 1-1 2 1 1 2 1 3 1l-1 1c0 1 0 2-1 2v6c-1-1-1-1-1-2l-1-4h-1-1v-2l-2-1h-1v-1h2v-2c0-1 1-1 2-2h0c1-1 2-2 2-3z" class="C"></path><path d="M223 322v1l-1 2-1 1v-2c0-1 1-1 2-2z" class="Z"></path><path d="M224 273v1l1 1 1-2h0l-1 46h0c0 1-1 2-2 3v-3h0c1-2 1-4 1-6 1-6 0-14 0-20l-1-19 1-1z" class="G"></path><path d="M190 247c2 0 3 0 4 1h3l2 2c3 4 5 8 8 12 2 5 5 8 6 13l1 1v6l1 12c0 2-1 9 1 11-1 1-1 1-1 2l1-1c1 0 2-1 3-1v6 1c1 1 1 2 1 4 2 0 3-2 4-3 0 2 0 4-1 6h0v3h0c-1 1-2 1-2 2v2h-2v-1h0-4l1 1h-2-3l-1-4h1c0-3 0-15-1-17v-1l-1-5 1-2v-20-1c0-1 0-2-1-3v-3-1c-2-2-3-3-4-5 0-3-2-4-4-6 0-1-1-2-2-3s-3-3-4-5-3-1-5-2v-1z" class="c"></path><path d="M210 304h1c1 0 1 0 2-1h0 0l1 2v1c0 1-1 2-1 3h0v1c1 1 1 3 1 4l1 1c-1 1-2 0-2 2 1 1 1 2 1 4 0 1-1 1-1 1 1 1 1 2 2 2v1l1 1h-2-3l-1-4h1c0-3 0-15-1-17v-1z" class="B"></path><defs><linearGradient id="T" x1="214.268" y1="290.17" x2="206.732" y2="292.83" xlink:href="#B"><stop offset="0" stop-color="#9b9797"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#T)" d="M209 270h0c1 3 3 4 3 7v1c1 1 0 3 0 4 1 5 1 9 1 13 0 3 2 7 1 10l-1-2h0 0c-1 1-1 1-2 1h-1l-1-5 1-2v-20-1c0-1 0-2-1-3v-3z"></path><path d="M215 307l1-1c1 0 2-1 3-1v6 1c1 1 1 2 1 4 2 0 3-2 4-3 0 2 0 4-1 6h0v3h0c-1 1-2 1-2 2v2h-2v-1h0-4v-1-1c1-3 0-6 1-9 0-2 0-5-1-7z" class="F"></path><path d="M215 325l1-2c1-1 2 0 3 0v2h-4z" class="D"></path><path d="M219 311v1c1 1 1 2 1 4 2 0 3-2 4-3 0 2 0 4-1 6h0v3h0c-1 1-2 1-2 2v2h-2v-1h0v-2-1-11z" class="P"></path><path d="M219 322c1-1 1-2 1-3l1-1 2 1v3h0c-1 1-2 1-2 2v2h-2v-1h0v-2-1z" class="K"></path><path d="M239 231c3-1 11-2 14 0 5 1 10 0 15 1h33c13 0 26 1 40 0v4h-6c-4 1-9 0-14 0h-36-110l-1-4c1 0 1-1 2-1 16 2 33 1 49 0h14 0z" class="n"></path><path d="M161 265c0-2 0-2 2-4v-1c1 0 2 0 3-1h0 2l-2 2h-2c-1 1-1 1-1 3l2 2c-1 1 0 1 0 3l1 1 1-1 1 1h1 1 3c2-1 5 0 7 0h0l2-1h0 10c2 0 3-1 4-1v-1h5 0c2 0 2 0 4-1-1-1-2-2-2-4l-1-1c2 1 2 2 3 3 1 2 2 3 4 5v1 3c1 1 1 2 1 3v1 20l-1 2 1 5v1h-1-7c1-1 2-2 2-4l-1-1c-1 0-2 0-3 1l-1 1h-1v-2l1-1c1-1 1-1 2-3-3-2-8 1-11-1v-4l1-8-30 2-4-1-1-1 1-1 1 1v-1h4 1l-1-1-2-2v-1c1-1 0-2 0-3l1-10z"></path><path d="M208 283c0 1 0 1-1 2s-1 1-2 1l-1-2h3 0l1-1z" class="Q"></path><path d="M191 276c3 0 6 0 9 1-1 1-2 1-3 1l-5 2-1-4z" class="M"></path><path d="M196 267h5 0c2 1 3 1 4 3-2 1-2 1-4 1-3-2-5-2-9-2 2 0 3-1 4-1v-1z" class="L"></path><path d="M202 261c2 1 2 2 3 3 1 2 2 3 4 5v1 3c1 1 1 2 1 3v1c-2-3-3-5-5-7-1-2-2-2-4-3 2 0 2 0 4-1-1-1-2-2-2-4l-1-1z" class="B"></path><path d="M203 300v-1-1c1-1 0-3 0-5 1-1 1-1 3-2v1 2 2h0 1l1 2h0l2-1-1 2 1 5v1h-1-7c1-1 2-2 2-4l-1-1z" class="H"></path><path d="M209 299l1 5v1h-1l-2-2c-1-1-1-2-1-3 1-1 1-1 3-1z" class="k"></path><path d="M160 278l31-2 1 4-29 2-1-1-2-2v-1z" class="C"></path><path d="M197 278c1 1 2 2 2 3 1 1 2 1 4 1 1-1 2 0 4 0l1 1-1 1h0-3l1 2c-3 0-5 0-7 1l-1 1h3v1c-1 1-2 1-3 1h-1l-3 2h-2l-1-1 1-8-30 2-4-1-1-1 1-1 1 1v-1h4 1l29-2 5-2z" class="I"></path><path d="M204 284c-1 0-1-1-2-2h5l1 1-1 1h0-3z" class="J"></path><path d="M314 259l1 4c5 3 12 5 17 9l-5 3v4l-5 3c-2-1-3-1-5-1-2 2-4 4-7 5 0-1 1-1 2-2l2-2c1 0 1-1 2-1l2-2h-1s-1-1-1 0c-2 0-7 5-8 6h-1c-3 1-7 3-9 6v-2-2c0-2-1-3-2-4-2-4-5-7-7-9-4-4-9-6-14-8s-10-2-15-2c-2 1-4 1-5 2l-4 3c-1 0-2 1-3 2-2 0-4 0-5 2v-5l-4-1-1 2v-2-1h-2v-1l1-1c-1 0-1-1-1-2l2-1c11-1 22 0 33 0h27c5 0 10-1 14 1h1l-1-3h2z" class="D"></path><path d="M236 265h4c3-1 6-1 9-1-2 1-5 2-6 4h0l-4-1-1 2v-2-1h-2v-1z" class="S"></path><path d="M327 275v4l-5 3c-2-1-3-1-5-1 3-2 7-4 10-6z" class="h"></path><path d="M306 281l3 2-2 2h0c-3 1-7 3-9 6v-2-2h0c3 0 6-4 8-6z" class="P"></path><path d="M289 274h1v-4l-1-1 1-2c1 1 1 2 1 3 1 2 3 3 3 5 1 2 4 5 3 6l-1 2c-2-4-5-7-7-9z" class="Y"></path><path d="M321 271l1 1c-3 3-9 8-13 11l-3-2 1-1c1 0 3-1 4-3 3-2 6-4 10-6z" class="B"></path><path d="M249 264h11c-2 1-4 1-5 2l-4 3c-1 0-2 1-3 2-2 0-4 0-5 2v-5h0c1-2 4-3 6-4z" class="C"></path><path d="M243 268l1 1h1c2-1 3-1 5-1l1 1c-1 0-2 1-3 2-2 0-4 0-5 2v-5h0z" class="b"></path><path d="M275 266c1-1 2-1 3-1s3 1 4 1h4 4 1l-1 1-1 2 1 1v4h-1c-4-4-9-6-14-8zm24 6c0-2 2-3 2-5-2 0-2 0-4 1h0v-1c-1 0-1-1-2-1h1l1-1c1-1 7 0 8 0 5 2 9 2 13 4 1 1 2 2 3 2-4 2-7 4-10 6-1 2-3 3-4 3h-1c-1 1-2 1-3 2-1-2-2-3-3-5 0-2-1-3-1-5z" class="I"></path><path d="M307 275v-1c1-1 2-2 2-3h1v1l1 2h1c1-1 1-3 2-3h2v1c-2 2-5 3-5 5-1 2-3 3-4 3h-1c-1 1-2 1-3 2-1-2-2-3-3-5l3 2c2-1 2-1 3-2l1-1-1-1h1z" class="f"></path><path d="M299 272c0-2 2-3 2-5-2 0-2 0-4 1h0v-1c-1 0-1-1-2-1h1l1-1c1-1 7 0 8 0 5 2 9 2 13 4h-1c-2 1-3 0-5-1-2 0-4 0-6-1s-3 0-4 0c1 1 1 2 3 3h1v-2c1 1 2 1 2 2s-1 1-2 1c0 1 0 3 1 4h-1l1 1-1 1c-1 1-1 1-3 2l-3-2c0-2-1-3-1-5z" class="O"></path><path d="M299 272c2 1 2 2 3 5l1-1v-1l1-1h1v1 1l1 1c-1 1-1 1-3 2l-3-2c0-2-1-3-1-5z" class="h"></path><path d="M260 264c5 0 10 0 15 2s10 4 14 8c2 2 5 5 7 9 1 1 2 2 2 4v2 2 1c-1 2-4 4-6 6-3 4-7 8-9 12-6 9-10 18-14 28-6 0-12 1-18 0-2 0-4 0-5-1 0 0 0-1 1-2h9 1c-6-1-12-1-17 0v-5h-1c-2 0-2-1-3-2v-1c-2-4-1-12-1-17v-25c1 0 0-1 0-2 0-4-1-14 1-16h2v2l1-2 4 1v5c1-2 3-2 5-2 1-1 2-2 3-2l4-3c1-1 3-1 5-2z" class="g"></path><path d="M255 266l1 1-3 3h-1c-1 0-1 0-1-1l4-3z" class="n"></path><path d="M238 278v3l2 2 1 1-1 1 1 1-1 2h-1c-2 4 1 9-1 13v-23z" class="b"></path><path d="M235 310c1 0 2 1 2 2 0 3 1 5 0 7v6l3 1v4h-1c-2 0-2-1-3-2v-1c-2-4-1-12-1-17z" class="K"></path><path d="M238 269l1-2 4 1v5c0 3 0 5-2 7 0 1 0 2-1 3l-2-2v-3-9zm2 57v-8c6 5 12 9 20 9 3 0 7-1 10-1l-3 9h-8-2c-6-1-12-1-17 0v-5-4zm1-40l2 1h0c2 5 3 10 9 13 3 1 8 2 11 0 3-1 6-3 7-6l1-1c1 2 3 4 3 6 2 4 3 10 2 14 0 1-1 2-1 2l-3 6c-3 1-6 2-9 2-8 1-14-2-21-7-3-3-3-5-4-9v-6c2-4-1-9 1-13h1l1-2z"></path><path d="M238 301c2-4-1-9 1-13 1 3 2 5 3 8-1 0-1 1-1 1-1 1-1 2-1 3-2 2-1 4-2 7v-6z" class="L"></path><path d="M270 294l1-1c1 2 3 4 3 6 2 4 3 10 2 14 0 1-1 2-1 2-1-3 0-6-1-10-1-2-1-3-3-5 0-2 0-4-1-6z" class="d"></path><path d="M243 302c0-2-1-2 0-3 3-1 3 0 5 2 3 1 5 2 7 4 1 0 1 0 2-1h7l-1 2-1-1c-1-1-2 0-3 0h-1c-2 2-3 3-3 5-1 1-2 1-3 2v1h1v1l-1 1h-1l-2-1h1l-2-2-2 1-1-1c-3-2-2-7-2-10z" class="e"></path><path d="M243 302c0-2-1-2 0-3 3-1 3 0 5 2 3 1 5 2 7 4 1 0 1 0 2-1h7l-1 2-1-1c-1-1-2 0-3 0h-1c-2 2-3 3-3 5-1 1-2 1-3 2v1h1v1l-1 1h-1l-2-1h1l1-2c-1-1-1-2-2-2s-2 1-3 0l2-1c2-1 3-2 3-4h0l1-1c-1-1-2-1-3-2l-1-1-2-1-3 2z" class="F"></path><path d="M253 307c1 0 0 0 1 1 0 1-1 1-2 2h-1c0-1 1-2 2-3z" class="j"></path><path d="M264 304c1 0 2 0 3 1v1 1h1l1 1c0 4 0 6-3 9-1 0-1 1-2 1l-2 1h-1l-2 1c-2-1-3-2-4-2-1-2-1-2-2-3-1 1-1 1-1 2-3-1-4-2-6-4l2-1 2 2h-1l2 1h1l1-1v-1h-1v-1c1-1 2-1 3-2 0-2 1-3 3-5h1c1 0 2-1 3 0l1 1 1-2z" class="J"></path><path d="M261 315h2l-1 3h-2c0-1-1-2 0-3h1z" class="i"></path><path d="M264 304c1 0 2 0 3 1l-3 3v1c1-1 2-1 3-1h1v2h-1v-1c-2 1-1 3-1 4l-1 1h-3c-1-1-1-1-2 0-1-1-2-2-3-2l-2-2c0-2 1-3 3-5h1c1 0 2-1 3 0l1 1 1-2z" class="B"></path><defs><linearGradient id="U" x1="255.564" y1="272.836" x2="260.396" y2="296.106" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#333230"></stop></linearGradient></defs><path fill="url(#U)" d="M256 270c6-3 14-3 20 0 5 1 9 5 13 9 2 3 4 6 6 10-2 2-15 19-15 19l-1-3c0-2-1-4-1-6h-4c0-2-2-4-3-6l-1 1c-1 3-4 5-7 6-3 2-8 1-11 0-6-3-7-8-9-13h0l-2-1-1-1 1-1-1-1c1-1 1-2 1-3 2-2 2-4 2-7 1-2 3-2 5-2 1-1 2-2 3-2 0 1 0 1 1 1h1 3z"></path><path d="M259 275l1 1c0 1 0 1 1 2l-1 1c-4 1-6 3-8 6 0-2 0-3 1-4 0-1 0-1 1-2h0c1-2 3-3 5-4z" class="c"></path><path d="M259 275c2 0 4-1 6 0l3 3s1 1 1 2l1 3c-3-3-5-4-8-4l-1-1c-1-1-1-1-1-2l-1-1z" class="a"></path><path d="M268 272l1 1v-2h1c2 0 4 1 6 2 1 1 1 1 1 2l-1 1c-1 1-2 2-3 2-2 0-3 0-4-1-1-2-1-3-1-5z" class="G"></path><path d="M256 270c6-3 14-3 20 0 5 1 9 5 13 9l-4-1c-2-2-3-2-5-4 0 0 0-1-1-1l-1 1v1h-1c0-1 0-1-1-2-2-1-4-2-6-2h-1v2l-1-1v-1c-2-1-6 0-8 1h-2c-1 0-2 1-3 2h-1l-3 3v-1h-2c2-2 4-4 7-6z" class="H"></path><path d="M261 278l1 1c3 0 5 1 8 4 2 2 3 4 4 6l1 1c-2 0-3 0-4-1v-2c-1-2-2-2-4-3l-1 1c1 1 3 3 2 5 0 3-3 5-5 6-2 0-3 0-5-1l-3-1c-1 0-3-1-4-3 0-2 0-4 1-6 2-3 4-5 8-6l1-1z" class="W"></path><path d="M261 278l1 1 1 2v1h-4l-1-1 2-2 1-1z" class="Z"></path><path d="M258 295c3 0 4-1 6-3 1-1 1-1 1-3h-1l-2 2c-1 2-1 2-4 2l-4-4c0-2 1-3 2-5 1-1 2-1 4-1h1c2-1 4 1 5 2s3 3 2 5c0 3-3 5-5 6-2 0-3 0-5-1z" class="J"></path><path d="M258 285h0l1 1c1 0 1 1 2 1h1c0 2-1 3-2 4h0v1h0-1l-1-2c-1-2-1-3 0-5z" class="I"></path><path d="M248 271c1-1 2-2 3-2 0 1 0 1 1 1h1 3c-3 2-5 4-7 6-2 5-4 9-2 13 0 3 2 6 5 8 3 1 7 1 10 0l1-1c2-1 5-3 5-6 1-2-1-4-2-5l1-1c2 1 3 1 4 3v2c1 1 2 1 4 1l1 4 2 5h-4c0-2-2-4-3-6l-1 1c-1 3-4 5-7 6-3 2-8 1-11 0-6-3-7-8-9-13h0l-2-1-1-1 1-1-1-1c1-1 1-2 1-3 2-2 2-4 2-7 1-2 3-2 5-2z" class="Z"></path><path d="M243 273c1-2 3-2 5-2-3 5-6 10-5 16l-2-1-1-1 1-1-1-1c1-1 1-2 1-3 2-2 2-4 2-7z" class="e"></path><path d="M277 275h1v-1l1-1c1 0 1 1 1 1 2 2 3 2 5 4l4 1c2 3 4 6 6 10-2 2-15 19-15 19l-1-3c0-2-1-4-1-6l-2-5-1-4-1-1 1-1c0-3-1-4-2-6h-1l-1-1v-2c1 0 2 0 2 1h3c1-2 1-3 1-5z"></path><path d="M276 294c2 2 3 2 5 3v1l-1 1v1c0 2 0 3-1 5 0-2-1-4-1-6l-2-5z" class="H"></path><path d="M289 286v5c-2 2-5 5-8 7v-1l1-1-2-4c1 0 2-2 4-2v1l1 1c1-1 2-2 2-3 1-1 0 0 0-1s1-1 2-2z" class="c"></path><path d="M286 284h0l1-1c0 1 1 2 2 3-1 1-2 1-2 2s1 0 0 1c0 1-1 2-2 3l-1-1v-1c-2 0-3 2-4 2-1-1-1-2-2-2 2-1 2-2 4-3 0 0 1 1 2 1v-1-2c0-1 0 0 1-1h1z" class="d"></path><path d="M286 284h0l1-1c0 1 1 2 2 3-1 1-2 1-2 2l-2 1v-1l1-1v-3z" class="k"></path><path d="M278 290c0-1-1-2-1-2v-4l1 1c2-3 3-4 6-5l3 3-1 1h0-1c-1 1-1 0-1 1v2 1c-1 0-2-1-2-1-2 1-2 2-4 3z" class="N"></path><path d="M514 163l1-4 1-1c1 1 2 3 3 4l1 3-3 4c-2 3-3 6-5 9l2 1c1 1 1 1 1 3-1 0-1 0-1 1l1 1h-2v1l1 1h1 0v4h0v4l1-1v-1c1-1 2-1 3 0h3c-1 2-2 3-3 4h0v1l1 1v1 1c-1 1-1 2-1 3h0-1c1 2 0 2 0 3s0 2-1 3c-1-1-1-1-1-2 1-2 1-3 1-4l-1-1-2 2c0 1 1 1 0 2 0 2 1 3 1 5v3 1l3 2h1l1 1h1l3-2v-1h1l2-2c5-1 7-2 10-5 1 2 0 3 0 5-1 1-2 2-3 2l1 1 1-1 2 1v1h3c0 1 0 1-1 2 0 1 0 1-1 2h0c-1 0-2 1-2 1l-1 1 1 1h-1s-1 1-1 2c-1 0-3 1-3 2l9-3c4-3 7-5 10-6h1c1 0 2 0 4-1v2h-1v3l1 1c2-1 5-2 6-4h0c2-1 3-3 5-4v-1h1 0v-2l3-1v-2h3l-1 1v1 1 2l-2 2h1c2-1 3-2 4-2 1 1 1 1 1 2l3-1v1l-1 2v1c1 0 2 0 3 1h0c1-1 2-1 3-1s1 1 2 1h1c1-1 2-2 4-3l2-1 2 1 1-1c1 0 2 1 2 1 1 1 2 2 2 3l-1 2h-1l-1 2c3-2 5-2 7-4 1 1 1 1 3 1h1c1-1 1-1 2-1 0-1 1-2 1-2 1-1 1-1 2-1h0l1-1h2l1-1 1 1c1-1 1-2 3-2l1 1h1l3-2c0-3 2-5 3-7v-2h1l3-2h1c1 3 1 3 3 5h2c0 3-1 5-1 8l3 2h0v-2l2-3 2 1c-1 1-1 2-2 2l2 2 2-2v2h5c1 1 2 2 4 2l2 1c0 1 0 1-1 2l-2 2 2 1-2 3h0l138 1c11 1 22 0 33 0v1c-1 0-2 1-3 1 2 0 5 0 7 1h7 2l-1 1 2 2h1 0 5c-1 3 0 6-1 8 0 0 0 4-1 4s-2 0-4-1l-2 1c-2 0-3 0-4-1v-3c-1-1-2-1-4-1-1 0-1 1-2 0-1 0-2 0-3 1l-3 6c-5 8-12 17-14 27v12l-1 18 1 12c0 1 0 4 1 4 0 1 1 2 2 3l2 2c1-1 2-1 4-1 0 1 0 2 1 2 0 1 1 0 1 1l2 1c0 1 1 2 2 3-1 1-1 2-1 3-2 1-3 3-3 5v1 1h-1c-1 2 0 4 0 6l-1 2h0c1 1 1 2 2 3l-4 1h-1c-2 0-4 1-6 0l-2 2h-2-2-3l-1 1v1c-1 0-1 1-1 2-1 0-1-1-2-2-1 0-4 1-5 1-2 1-4 2-5 4h0c1 3 2 4 2 7h0v1c0 2 0 4-1 6l-3 2v4h-1l-1-1c-2 1-3 3-6 2l-2-1h0c-2-1-4-2-6-4-3-3-3-5-3-9h2v-1h2c-1-1-1-1-2-1v-1h-1-1c-1 0-1 1-2 1h0l1-3c-1-1-2 0-3 0-2 1-3 3-4 5 0 3 1 7 1 10-3-1-5-2-8-2-2 3-2 5-2 8-1 4-1 11 1 14h-1c-6-3-8-10-13-14 3 9 8 16 12 23 4 6 7 12 10 18 2 4 4 8 5 12 1 2 8 16 7 18l-1 2c0 1 0 2 1 4 2 2 2 5 3 9 1 1 1 2 1 3s-1 3-2 4c2 9 5 19 6 28v6c1 3 0 6 1 9 2 19 1 38-1 56-1 7-2 14-4 20 0 5-2 10-3 14v-1l-2-4-12 33-4 9c-1 0-3 0-4 1s-2 3-2 4c-1 3-4 7-6 10 0 0-1 0-2-1 0 3-1 3-3 5-1 0-1 1-2 2l-1 1-1 1c-1 3-2 4-5 6h-2c0 2 0 2 1 3h-1c-1 0-2-1-3-1v1c0 1 1 2 2 2 2 1 4 2 6 1v-1l2 1c-3 3-5 5-7 8l-3 5h-3c-1 1-2 2-3 4s-2 3-5 5h0v1c-2 0-3 1-4 1h1 2c1 1 1 2 1 3h0c-3 3-6 7-10 8l-1 1-3 1v1l1 1c0 1 0 2-1 3 1 1 2 2 4 2h0 2c-1 2-1 2-2 3h0c1 1 0 1 1 1l-1 1c-1 0-1 1-1 2-8 7-17 13-25 19-2 1-4 3-6 4h-2l-1 1h-1c-1 0-4 2-4 2-2 1-4 2-6 1l-5 3v1c-4 2-8 5-12 8-5 3-10 4-13 9l1 2c-2 3-7 6-10 9-2 2-5 4-8 6l-11 8c-6 4-11 9-16 15-2 2-5 4-6 7-6 6-11 13-16 20-2 2-3 5-5 8 0 0-1 1-2 1 0 1-1 2-1 3-3 8-9 16-12 24l-4 8c0 1-1 1-1 2-2 2-2 5-3 8-2 4-4 8-5 12-5 13-8 27-11 41-1-1-1 0-1-1l-1-1-1 1c-1 2 0 4-1 5l-1 3-4-18h0-1c-1-2-2-5-2-7l-4-13h0v-1c1-1 1-2 1-3l-1-1c0-2-1-4-1-6v-1l-11-25-2 2s0-1-1-1c-5-11-10-21-17-32l-5-7-8-11-36-36 1-1h0c-1 0-1-1-1-1 1 0 1 1 3 1l-10-7 2-1c-3-3-6-6-9-8-1-1-2-3-3-4 0-1-2-2-3-3l2-2h-1-1l-1-1-1 1-11-8c0 1 0 1 1 2 1 0 2 1 3 2-1-1-2-1-3-1l-1-1c-1-1-2-1-3-2-2-1-3-2-5-2-2-1-5-3-7-5l-12-7c-4-3-7-6-11-8l-22-18h-1l-1-1h1c1 0 2-1 2-2h1c-1-2-2-3-3-5 0-1-2-2-4-3-4-4-8-8-12-13-4-3-6-7-9-11 0-1-4-5-4-6h-1v1l-1 1h-1l-1-3-6-9c-1-3-3-6-4-9-2-3-5-8-7-12 0-2-2-4-2-6h1c1 1 2 1 2 3h0l1 1v-2h0v-1c0-2-1-3-2-4l-5-10c-1-1-1-1-1-2 0-2-1-4-2-5l1-1s0 1 1 1l1-1h2c-1-5-5-10-7-15-1-1 0-1 0-2l2 2h0l1-1c-1-2-1-3 0-5h-1c-1-1-2-2-4-2 0-4-3-7-3-11h2v-2l-2 2-1-1c-1-3-2-7-1-10-1-1-1-2-1-2-1-3-1-6-1-8-1 0-1-1-2-2l-2-1v-5c-2-15-2-32-1-47 0-4 1-9 1-13h-1l-1-1-3-1 1-6c-1 0-1 0-2-1v-4c0 3-2 14-3 15h-1c1-8 2-15 4-22 3-17 8-33 15-49 3-6 5-12 9-18 0 0 1-3 2-3 4-9 10-17 14-26 1-2 2-5 2-7-4 5-7 11-14 16 0-1 2-4 2-6 0-4 0-13-3-16-3-1-5 0-8 1 0-3 1-6 1-9v-1l-1-3c-2-2-4-2-7-3h-1c1 0 1 0 2-1l-1-2h-5-8c0-1-1-2-2-3v-1h-2l-6-1h3c0-1 1-2 1-2 1 0 1 0 2-1h1 1 3l1-1 1-1h4l1-2h1l1 1c1-1 2-3 2-4l1-1 1 1 2-1c5 1 11 1 17 0 0-1 1-2 1-3l-2-2c4-18 12-33 25-46 3-3 7-6 10-9v-1c3-1 5-3 7-5 2 0 3 0 5 1l5-3v-4l5-3h1l1 1c2-1 5-1 8-2v-13c1-5 0-11 0-16h1l1-1v-6l1-4h1v-1c6 0 14-1 20 0v1l1-1-6-6 2-1 6 3h1c0-1 1-1 2-1h1c1 0 2 0 3 1h0 1l1-1v-1h2v-1l-1-4h2v1l3 1c2 3 3 5 6 6 1 1 1 1 3 1 1-1 2-1 4-1h1c0-1 1-1 2-2l1 1c1 1 2 1 4 1h5l2 1h3c0-1 1-2 2-2s2 0 3-1h0c2-1 4 0 6-1h0l1-1-2-1v-3l1-1v2c1 1 1 1 2 1l1-2c3 0 3 1 5 2h1c2 0 2 1 4 2h3 0c1 1 2 1 3 1 0 1 1 1 2 1 1 1 2 1 4 1l1-1c1 0 2 0 3-1 0-1-1-1-1-2-1-2-2-3-2-5h1c1 0 1 1 2 1l1-1h-1v-1c-1-2-3-3-4-5v-1c-1 0-1-1-2-1v-3c2 1 3 1 3 3 1 2 2 4 5 5h1l1 1h3c-1-2-1-2-1-3 2 1 3 2 4 3 2 0 2 0 3 1h1l2 2 1-1-8-7 3-1 2 2h1l1-2h0c2 1 4 1 6 2 0 1 1 1 2 1l2 2h3c2 1 3 1 5 1 2-1 5 0 8 0s5 0 7-2v-1l1-1v-2c-1-4-3-8-5-13-1-2-3-5-4-8l3 2c0 1 1 2 2 3 2 1 3 0 5-1 1-2 0-5 0-7 1-1 2-1 3-1v-3l1-2h1c0-4 2-7 4-10 1-2 2-4 2-7l-3 1z"></path><path d="M717 400h-3 0-1l3-3 1 3h0z" class="L"></path><path d="M642 403l3 1h-1l1 1-2 2v1c-1-2-1-3-1-5z" class="Q"></path><path d="M624 776c1 2 2 3 2 4l-1 1c-1 0-2-1-2-2s1-2 1-3z" class="G"></path><path d="M511 778c1 0 0 0 1 1 1 0 1 0 1 1l-1 2h-2c0-2 0-2 1-4z" class="Y"></path><path d="M250 570l2 2c1 0 1 1 0 2v2l-2-1v-5zm186-190v-2c1-1 3 1 4 2l-1 2c-1-2-2-2-3-2z" class="D"></path><path d="M300 407c1 0 1 0 2 1 1 0 2 1 3 1v1c-1 1-1 1-3 1-1-1-2-3-2-4z" class="a"></path><path d="M559 431c1 1 2 2 2 3l2 1c-1 1-2 2-2 3l-1-1c-1-2-1-3-1-6z" class="F"></path><path d="M378 749c0-1 0-1 1-2l2 1h3l2 3h0-1c-2-2-4-2-7-2z" class="T"></path><path d="M530 780c1 0 1 0 2 1l-3 4-1 1h0l2-6z" class="D"></path><path d="M380 223c1 1 2 2 2 3-2 1-4 1-5 1l-1-1h1l1-1v-1h2v-1z" class="Y"></path><path d="M300 592l3-1s0 4-1 4c-1 1-2 1-3 2l1-5z" class="S"></path><path d="M445 580c1 1 2 1 3 2 0 1 1 2 0 4h-1c-1-1-1-2-1-3l-2-2 1-1z" class="k"></path><path d="M418 560c1 0 1-1 2 0-1 1-1 0-1 1-1 0-1 1-2 2l-2 1-2-1v-1h2c1-1 1-2 3-2z" class="F"></path><path d="M441 578c2 0 3-1 5 0s2 2 2 4c-1-1-2-1-3-2h-1c-1-1-2-1-3-2z" class="N"></path><path d="M717 400l2 1v1c-2 2-2 1-4 2h-1v-2l1-1c1 0 1 0 2-1h0z" class="H"></path><path d="M639 224l2 1 1 1 5 1h-11l2-3h1z" class="V"></path><path d="M800 260h3l1-1h1c0 2-1 4-2 5 0-1-1-1-1-1 0-2-2-2-2-3z" class="m"></path><path d="M421 780c0-2 0-2 1-3l2 1c1-1 1-1 3-1 0 2 0 2-1 3-2-1-3-1-5 0z" class="c"></path><path d="M772 363l2-2h2v1c2 0 3 0 4 1h-1v1h1c-2 1-7-1-8-1z" class="D"></path><path d="M303 582c2-2 3-3 5-4h0c0 2 0 3-1 5-2 0-2 0-4-1z" class="O"></path><path d="M636 222l2 2-2 3v1c-1-1-2-1-3-1 1-1 1-1 1-2l2-3z" class="D"></path><path d="M458 618h0c0 2 0 4-1 6l-1-1c-1 0-1 0-2-1 2-1 2-3 4-4z" class="F"></path><path d="M746 249l1 1h0 3v1c-1 2-2 2-4 2h-1 0v-2c0-1 1-1 1-2z" class="K"></path><path d="M414 600h1c0 2 1 3 2 4l1 2c-2 0-3-1-5-1l1-5zm19-375c2-1 2-1 4-1l1 1c-1 2-1 2-2 3h0c-1-1-2-1-3-3zm10 367s1-1 2 0c1 0 2-1 3 0l-1 2c-1 0-1 1-2 1s-2-1-3-2l1-1z" class="D"></path><path d="M642 396l2 1c0 1 0 1-1 2 0 2 1 3 2 4v1l-3-1v-1c0-2-1-4 0-6z" class="Y"></path><path d="M724 367h1c1 1 2 2 3 4l-7 1c2-2 3-3 3-5z" class="a"></path><path d="M417 610c3 1 5 1 8 2-2 1-4 2-6 2 0-1 0 0-1-1s-1-2-1-3z" class="f"></path><path d="M786 365l-2-2h-1l1-1h1c2-1 4-1 6-1-2 1-4 2-5 4z" class="H"></path><path d="M451 552l1 1-6 3c-1-1-2-1-3-1l2-2c2 0 4-1 6-1z" class="O"></path><path d="M377 745c1-1 2-1 3-1 2 1 3 2 4 4h-3c-1-1-1-1-2-1v-1l-2-1z" class="R"></path><path d="M472 791l4 3-1 2-4-3c0-1 0-2 1-2z" class="O"></path><path d="M572 532l3-5 1 2c0 1-1 1-1 2l1 1-1 2-3-2z" class="N"></path><path d="M449 393c1-1 2-1 4-1 1 2 1 3 2 5-1-1-2-1-2-2l-1 1v1h0l-1-1c-1-1-2-2-2-3zm1 226c3-1 5-3 8-4-1 3-3 4-5 6l-3-2z" class="L"></path><path d="M725 367l4-1c1 1 1 2 1 4l-2 1c-1-2-2-3-3-4z" class="c"></path><path d="M692 705h0c2-1 2-1 4-1 1 2 1 1 1 3-1 0-3 1-4 1 0-2 0-2-1-3z" class="S"></path><path d="M298 397l1 3c-1 1-2 3-3 4-1 0-1-2-1-3l3-4z" class="c"></path><path d="M298 397c1-2 4-5 6-6-1 1-1 2-2 4-1 1-1 2-1 3l-2 2-1-3z" class="d"></path><path d="M799 258c2-1 3-2 6-2v1 2h-1l-1 1h-3l-1-2z" class="c"></path><path d="M610 517l2 1v6l-1 1v-1l-1-3h0v-3-1z" class="U"></path><path d="M695 279c0-2 0-2 1-3 2 0 3 1 4 2l-1 3-4-2z" class="R"></path><path d="M660 720l3-3c2 1 3 1 5 1l-4 3c-2 0-3 0-4-1z" class="T"></path><path d="M417 721v-11c0 2 0 3 1 4h0l1-2c0 2 0 5 1 8-1 0-2 1-3 1z" class="k"></path><path d="M636 560c1 1 2 1 3 2l-5 4c-1-1-1-3-1-4l1-1c1 0 1 0 2-1zM533 829l-2-5c0-2 1-2 2-3s1-2 2-1c-1 1-2 2-1 4v4l-1 1z" class="C"></path><path d="M394 528c3-1 5-2 8-2v2c1 1 2 1 3 1h-4 0c-1 0-4-1-5 0h0l-2-1z" class="E"></path><path d="M500 787v-3l1-7h0c1 2 1 6 1 8l1 1v2h-2 0l-1-1z" class="I"></path><path d="M666 421l-2-2h1c1 0 3 0 4 1h1c1 1 3 2 4 4l-8-3z" class="V"></path><path d="M314 420l2 1c-1 1-3 6-5 7l-1-2 4-6z" class="Y"></path><path d="M744 371l1-1c1 0 2-1 3-2 0 1 0 0-1 1v3h0c-2 2-4 3-6 5 1-3 2-4 3-6z" class="D"></path><path d="M733 537h0c2-1 2-1 3-2 1 1 1 2 1 3-1 1-3 1-4 1h-1l-2-1-1-1h4z" class="E"></path><path d="M842 242c1 1 2 1 4 2 0 0 0 4-1 4s-2 0-4-1c0-1 1-3 1-5z" class="h"></path><path d="M259 547l1 1c-2 2-6 5-8 5 1-1 1-1 1-2v-1c0-1 0-1 1-2 2 0 4 0 5-1z" class="U"></path><path d="M280 350h1v-1l1-1c0 1 1 1 1 2 0 3-3 6-4 9 0-3 0-7 1-9z" class="Q"></path><path d="M285 333c-1 2-1 4 0 6l1 1-2 4c-1-1-2-2-2-3 0-3 1-5 3-8z" class="B"></path><path d="M420 704c0 1 0 1 1 1 0 2-1 5-2 7l-1 2h0c-1-1-1-2-1-4l3-6z" class="S"></path><path d="M281 373c1 1 1 1 1 2s1 2 2 3h-1l-1 1v2c-1-1-1-2-2-3h0v-2c-1-1-1-1-1-2l2-1z" class="L"></path><path d="M739 343h3v10h0c-2-3-3-7-3-10z" class="c"></path><path d="M640 681v1l1-2h0v3c2 0 3-2 4-1l-2 3h0v-1c-2 0-2 1-3 3 0-3-2-2-3-4 1-1 1-1 3-2z" class="B"></path><path d="M677 678h0c-1-2-1-5 1-7h3c0 2-1 3-1 5-2 0-2 1-3 2z" class="O"></path><path d="M332 272h1l1 1-7 6v-4l5-3z" class="k"></path><path d="M434 266h7v2h0c1 0 1 1 2 2-3-1-6-2-8-2l-2-2h1z" class="Z"></path><path d="M405 557l1 1c2 1 3 1 5 1l2-1c0 1-1 2-2 4-1 0-1-1-2-1l-2 1-1-1c-1-1-1-3-1-4z" class="e"></path><path d="M436 380c1 0 2 0 3 2v1l2 1 1 1c1 0 2 1 2 2h0-1-2l-4-4h0c-1 0-1-1-2-1-1-1-1 0-1-1h1l1-1z" class="F"></path><path d="M700 278l5 3c-1 1-2 2-2 4l-4-4 1-3z" class="E"></path><path d="M370 736c3 1 5 4 7 6-2 1-2 2-3 4v1c-1-2-1-7-2-8s-1-2-2-3z" class="X"></path><path d="M497 880l4 8c0 1-1 2-1 4-2-3-3-5-4-7l1-1c0 1 0 1 1 2v-2c-1-2-1-3-1-4z" class="i"></path><path d="M645 628l3 1h1c1 0 3 2 4 2l2 3v1c-4-2-7-4-10-7z" class="f"></path><path d="M378 402l-1-1-1 1h0v-1-5l-1-2c0-1 0-2 1-3 0 0 0 1 1 2h0v2l1 2 1 1c0 1 0 3-1 4z" class="j"></path><path d="M577 419h3v2l-7 7c1-3 3-5 4-9h0z" class="G"></path><path d="M760 355h-10v-5l2 2h0l2 2c1-1 1-1 1-2l2 2 1-1 2 2z" class="H"></path><path d="M739 335c0 1 1 2 1 3v1c1 1 1 3 2 4h-3l-3-6c1-1 2-1 3-2z" class="R"></path><path d="M564 418c0 1-1 2-2 4-1 1-1 2-2 3 0 1 0 1-1 2l-1 1v-2l1-1h-1c0-1-1-1 0-2 0-2 1-2 2-3h2c0-1 1-2 2-2z" class="H"></path><path d="M282 341c0 1 1 2 2 3 0 2-1 4-1 6 0-1-1-1-1-2l-1 1v1h-1l2-9z" class="f"></path><path d="M357 545c1 2 0 3 0 5-1 1-3 0-4 0 0-1 1-1 1-2-1-1-2-2-3-2l6-1z" class="M"></path><path d="M560 807h3v1l-2 1v1c2 0 3-2 5-1l-1 1-6 3c0-1 0-1-1-2v-1l2-3z" class="f"></path><path d="M759 442l3-1 1 6v1h-3l-3-6h2z" class="E"></path><path d="M688 242c1 1 3 1 5 2 1 0 2 2 3 3-2-1-4 0-6 0-1 0-1-1-2-1v-4z" class="L"></path><path d="M440 415l1-1 1 3 5 10c-1 0-1-1-2-2l-3-3h0c0-1 0-2-1-3s-1-3-1-4z" class="R"></path><path d="M646 745v-3c2-1 3-3 5-4h1c-2 2-2 3-3 5v1c-1 2 0 4-1 6l-2-2v-2-1z" class="M"></path><path d="M369 520c0 2 0 5 1 6 2 1 4 1 5 1v2h0c-3 0-5 1-7-1l1-1-1-1c0-2 0-4 1-6z" class="K"></path><path d="M600 778c1 0 2 1 3 1l1 1v2h1c0-2 0-2-1-3l1-1h1c0 1 1 1 1 2s-1 2 0 2h-2c-1 1-2 1-3 1l-1 1h-1v-1l1-1c-1-2-1-2-1-4z" class="d"></path><path d="M357 427c-1-1-1-1 0-2h2c2 0 0-3 1-5l1 1v1c1 1 1 1 0 2v1l1 3-1 1h0-1c-1 0-2-1-3-2z" class="L"></path><path d="M596 432c1 1 2 1 3 1s2-1 4 0c0 2 0 2-1 3h-2c-3-1-6-1-9-1l1-1 1 1 3-3z" class="K"></path><path d="M721 372c-1 0-2 0-3-1 0-2-1-2 0-4 2-1 4 0 6 0 0 2-1 3-3 5z" class="d"></path><path d="M683 438l6-6c2-1 3 0 4 0h0v1l-10 6v-1z" class="F"></path><path d="M463 785l9 6c-1 0-1 1-1 2l-10-7 2-1z" class="N"></path><path d="M305 409l8 1-1 4c-2-1-4-1-6-2-1-1-2-1-4-1 2 0 2 0 3-1v-1z" class="V"></path><path d="M431 570c2-3 3-4 6-5l-5 9h-1-1-1c0-2 1-3 2-4z" class="N"></path><path d="M614 636c2 1 3 1 5 2 1 1 1 3 1 5h0l-2-1h0c-1-1-3-2-5-2h0c0-1 0-3 1-4z" class="S"></path><path d="M572 532l3 2h0v1c-1 1-2 4-4 4l-1 1h-1l3-8z" class="a"></path><path d="M357 427c1 1 2 2 3 2h1 0l1 3c2 0 2 0 4 2v2h-2c-2-2-7-5-9-6h2l1 1 2 1h0c0-1 1-1 1-2-1 0-2 0-3-1l-1-2z" class="F"></path><path d="M419 614l-2 1c-2 0-3 1-4 0 0-2 0-4 2-6l2 1c0 1 0 2 1 3s1 0 1 1z" class="B"></path><path d="M355 430c2 1 7 4 9 6h-2 0c-2-2-4-1-7-1h0l-1-1h-2c1-1 2-2 3-4z" class="N"></path><path d="M784 243h20l-1 2h0c-2 0-2 1-4 2 0-1 0-1-1-2-1 0-2 0-3-1-4-1-8 1-11-1z" class="l"></path><path d="M571 653v-2c2-2 3-4 5-5 2 0 4-1 6-1 1-1 0-1 1 0v2c-3 1-7 1-10 3l-2 3z" class="H"></path><path d="M263 480h1c0 1 1 1 2 2l1 1v2h1c-1 1-2 2-2 3-1 1-1 2-2 3-1-1-1-1-1-2 1-4 1-5 0-9z" class="E"></path><path d="M737 406l-3-4c-2-3-2-3-1-6h0l7 10h-2-1z" class="X"></path><path d="M685 272h4l5 2 2 2c-1 1-1 1-1 3-3-3-6-5-10-7z" class="G"></path><path d="M534 824c0 2 0 4 1 5l1 1 7-7 2-1v1h-1c-4 3-7 7-11 11v-5l1-1v-4z" class="B"></path><path d="M842 236h5c-1 3 0 6-1 8-2-1-3-1-4-2v-6z" class="T"></path><path d="M713 566c2-2 6-7 6-9 0-1 0-1 1-2l1 1 1 2c-2 2-4 5-6 8-1-1-2-1-3 0z" class="g"></path><path d="M454 460c2-2 4-3 6-4-1 2-3 4-5 6h1c-2 2-8 5-10 6a30.44 30.44 0 0 1 8-8z" class="a"></path><path d="M363 548s0-2-1-2v-1c2 0 3 0 5 1h0c0-1 0-1-1-2l2-1v1 1c1 2 2 2 3 3h1l1 1-1 1h-1c-1-1-2-1-2-1l-1-1c-1 0-1 1-2 1s-2-1-3-1z" class="b"></path><path d="M500 787l1 1h0l2 2v2h-1c0 1 0 1 1 1v1 9l-2-3v-1-1l1 1h0c-1-1-1-2-2-3v-9z" class="L"></path><path d="M304 384c-1 0-2-3-2-4 1-2 5-4 5-7 1-1 0 0 2-1l1 1c-2 2-5 5-6 7 2 1 2 1 3 2v3l-1-1h-2z" class="V"></path><path d="M505 209v-3c1-1 0-2 0-3h1v3l1 1v5l-1-1v2c0 1 0 1-1 2l-1 7h-1l-1-1v-1h1v-1l-1-1c1-1 2-2 2-3v-2-1l1-1v-2z" class="D"></path><path d="M261 458c2 1 2 1 4 3l-3 6c-1-1-2-1-3-2l2-7z" class="i"></path><path d="M588 227c-1 0-2 0-3-1 3-1 3-2 6-2v-1l2 1c2 0 4 0 6-1l-1 2c-2 1-5 1-8 2h-2z" class="c"></path><path d="M264 472c1 1 1 1 1 2-1 1-1 2-1 3v1c1 0 1-1 2-1 1-1 1 0 1-2 1 2 1 3 2 5-1 1-1 1-3 2-1-1-2-1-2-2h-1 0c-1-3 0-5 1-8z" class="X"></path><path d="M776 257c4 1 8 0 12 1l1 3h-3-7c1-1 0-1 0-3l-3-1z" class="P"></path><path d="M302 367c1 0 3 0 5 1v1c0 1 0 3-1 3-1 1-5 0-6 0 0-2 1-3 2-5z" class="X"></path><path d="M393 519l10-10c-1 4-4 9-7 12v-1c-2 0-2-1-3-1z" class="R"></path><path d="M453 628c2-1 3-1 4-2 1 1 2 3 3 5h0-1c-1-1-1-1-1-2l-1 1c0 2 0 3-1 4h-1c-1-1-2-1-3-1l1-2h2v-1l-2-2h0z" class="D"></path><path d="M440 688v-1c3-2 7-3 11-4-1 1-2 2-3 4-2 0-2 1-3 2s-2 0-3 1h-1-2l1-1v-1h0z" class="F"></path><path d="M312 578l1-1 8 7c-2 1-2 1-4 1l-1-1c-1-1-2-1-3-1 0 0 0-1-1-1v-4z" class="C"></path><path d="M414 530c2-1 3 0 5 0h1c1 0 1 0 2 1 1 0 1-1 2-2h1c1 2-1 3 1 3-1 1-2 3-3 3l-3-3-2 1c-1-1-2-2-4-3z" class="D"></path><path d="M352 434h2l1 1h0c3 0 5-1 7 1h0l1 2-2 1v-1c-1-1-1 0-3 0h-4c-2-2-1-2-2-4z" class="F"></path><path d="M551 219h1c1 0 2 0 4-1v2h-1c-2 1-4 1-5 2-3 1-5 3-9 3 4-3 7-5 10-6z" class="a"></path><path d="M295 366l7 1c-1 2-2 3-2 5l-7-1h1l1-2v-3z" class="N"></path><path d="M525 875c0 1 0 2 2 3v-1l1-1-5 10c-2 0-2-1-2-2l4-9z" class="C"></path><path d="M599 636c2 4 2 6 2 10h0c-1 1-4 4-4 5l-2-2 2-2c3-3 2-6 1-10l1-1z" class="M"></path><path d="M582 691s-1 1-1 2l1 1c-1 1-1 1-2 1l-1-1c-1-2-2-5-2-7h2c0-1 1-1 2-2v1 2c1 1 1 2 1 3z" class="I"></path><path d="M804 243h9l-5 5h-2l-1-1c0-1 0 0 1-1v-1h-1l-1 1-1 1s-1 1-2 1c1-1 1-2 2-3h0 0l1-2z" class="Z"></path><path d="M302 539h3 12 2v2h-6-10c1-1 2-1 3-1h2c1 0-1 0 1 0h1c-1-1-3-1-3-1-2 1-3 1-5 0h0z" class="I"></path><path d="M621 637c5 1 7 7 11 10v3c-4-4-8-8-11-13z" class="U"></path><path d="M566 548h1l2-2v1 1h1l2 2v1h1v2c-2-1-4-2-6-2h-2-4c1-1 3-2 5-3z" class="Q"></path><path d="M581 581c-2-1-5 0-7 0h0c-1 1-1 1-1 2s0 1-1 1l1-6c3 0 8-1 11-1-1 2-1 3-2 4h0-1z" class="f"></path><defs><linearGradient id="V" x1="305.946" y1="584.004" x2="300.847" y2="588.807" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#V)" d="M300 592c1-3 2-7 3-10 2 1 2 1 4 1l-4 8-3 1z"></path><path d="M615 438l3 2 3 2-2 2c-1 0-2 0-3 1-2-2-4-3-6-5h3l2-2z" class="K"></path><path d="M654 561v1l-3 3c-3 2-4 4-5 7h-1l-1-1-1 1h-3c4-4 9-8 14-11z" class="V"></path><path d="M738 422l1 2 4 8-2 1v1l2 3-2 2v-3c0-2-2-3-4-4v-3l2 2 1-1v-2c-2-2-2-3-2-5v-1zM412 588l3 6 2 10c-1-1-2-2-2-4h-1c-1-1-1-3-2-5l-1-2 1-1v-4z" class="F"></path><path d="M420 720c1 5 3 10 5 15-4-4-7-8-8-14 1 0 2-1 3-1z" class="d"></path><path d="M310 426l1 2c-3 5-4 11-8 16 0-4 2-6 3-9l-1-1 5-8z" class="e"></path><path d="M264 472c2-1 2-2 4-4 2 0 2 0 3 2h0l-1 1c-1 2-2 3-3 4 0 2 0 1-1 2-1 0-1 1-2 1v-1c0-1 0-2 1-3 0-1 0-1-1-2z" class="N"></path><path d="M647 498c1 1 1 2 1 4l-1 1-1 1-1 1c-1 1-2 0-3 0-2-1-3-3-4-4h4c2 0 3-1 5-3h0z" class="B"></path><path d="M661 559l8-2c-3 2-7 4-10 7-1 1-2 1-3 2l-3 3h-1 0l-1-1c1-2 3-3 5-4l5-5z" class="I"></path><path d="M715 377h1l4 3-7 11-1 1h-1l3-6c1-1 1-3 1-5l1-2-1-2z" class="Q"></path><path d="M450 622c1 1 1 1 2 3l1 3h0l2 2v1h-2l-1 2-4 1h0c-1-2-2-2-4-2 1 0 2-1 3-1 2 0 3-1 5-2l-1-3-2-4h1z" class="e"></path><path d="M510 860h1c1 0 1-1 2-2 3-2 4-5 7-7v3l-9 10-3-3 2-1z" class="B"></path><path d="M520 811h0c1 1 0 2 1 2 1 1 1 1 2 1-2 4-8 8-12 10 1-4 8-7 9-12v-1z" class="h"></path><path d="M399 727l15 15c-5-3-9-7-14-11-1-1-1-2-2-3l1-1z" class="G"></path><path d="M562 540l-3-8v-1-7c1-4 3-7 6-9h0c-1 1-1 1-1 2l-2 4c-1 2-1 3-2 5h0v3c0 2 0 5 2 7h1l1 1 1 1c-1 1-2 1-3 2z" class="H"></path><path d="M288 369c-5-1-9-4-12-8 3 0 6 1 9 2l-1 1c1 2 3 2 4 3h0l2 2h-2z" class="O"></path><path d="M793 259v-1-1h5 1v1l1 2c0 1 2 1 2 3-2 0-3 0-4 1v1h-1l-1-2v-2c-1-1-2-1-2-2v-1l-1 1z" class="N"></path><path d="M771 377v-1c1 0 2 0 2-1 1 1 2 1 2 2h1 3v1 1l2 1-1 2c-2-2-3-2-5-2h-1v1h-1c-2-1-3-2-3-3l1 1 1-1-1-1z" class="D"></path><path d="M643 466l4-1c-3 3-7 7-10 9l-3 2c-1-1-1-1-1-2h1c2 0 0-2 1-3h1c2 0 2-1 3-2h-1l1-1h0c2 0 3-1 4-2z" class="G"></path><path d="M402 657c2 4 4 7 5 10l1 2h0c-2-1-2-1-3-1h-1c-2-3-3-6-4-10h0 1l1-1z" class="S"></path><path d="M710 279v-1c1 0 2 1 3 1l5 5 5 3h1s1 1 0 2v2l-14-12z" class="W"></path><path d="M313 410c1 0 3 1 4 2v1h1v-1c0-2 1-3 1-4l1 1c0 3-2 9-4 12l-2-1c1-2 2-3 2-5-1-1-2-1-4-1l1-4z" class="b"></path><path d="M500 796c1 1 1 2 2 3h0l-1-1v1 1l2 3h-1 0c2 1 1 2 2 4 0 1 0 1-1 2 0-1 0-1-1-1v1 2h1l1 1c-1 0-2 0-2 1h-1l-1-1c-1-5 0-11 0-16z" class="a"></path><path d="M330 558h-26c8-3 21-2 29-1h-4v1h1z" class="S"></path><path d="M286 535h1l-2 1v1h3c1 0 1 0 2 1h0l1-2h5c2 1 3 1 5 1l1 1c1 0 2 0 3 1h-3-13c-2 0-4 0-5-1l1-3h1z" class="U"></path><path d="M574 694c2 0 3 0 5 1 1 1 2 1 2 2 1 1 1 1 1 2 1 1 2 2 3 2 2 1 4 2 5 2 1 1 3 1 3 2h-2-1c-1-1-2-1-3-1 0 1 0 2-1 2v1c-3-1-4-8-6-10s-4-2-6-2v-1z" class="d"></path><path d="M768 499v-1h1l3 3 2 11c-1-1-2-2-2-3v-1c-1 0-1 0-2 1l-2-10z" class="T"></path><path d="M628 742c1 1 1 2 3 2-2 2-5 3-7 4-3 2-7 4-10 5 2-4 10-8 14-11z" class="O"></path><path d="M398 492l1 1 4 10c1 0 1 2 2 3l1 3c-2-1-4-3-4-4l-5-11s0-1 1-1v-1z" class="b"></path><path d="M313 583c1 0 2 0 3 1l1 1c2 0 2 0 4-1 1 1 2 3 3 4-1 1-2 2-4 3 0 0-1-1-2-1-2-2-4-4-5-7z" class="B"></path><path d="M255 571v-9-4c0-1 1-2 2-3v1l-1 3h0c1 1 1 2 1 3l1 1-1 1h1c1 2 2 3 2 5-1 1-2 1-2 2l-1-1c-1 0-1 1-2 1z" class="I"></path><path d="M574 614c0-1 0-1-1-1l-5 1v1c1 0 2 1 2 1v1h-1l-1 1c1 0 2 0 3 1l-1 1c-1 0-1-1-2-1h0c-1-2-2-3-4-4v-1-1c-1 0-2-1-3-2 2-1 5 2 7 2 1 0 3-1 4-1h0c2 0 3 0 4-1h2l-1 2-3 1z" class="V"></path><path d="M301 528l23-1v1 1h-4l-1 1h0-1c-1 0-3-1-5 0v1c-1 0-2-1-2-1l-4-1h0c-2 0-4 0-6-1z" class="C"></path><path d="M285 363l10 3v3l-1 2h-1c-2 0-3-1-5-2h2l-2-2h0c-1-1-3-1-4-3l1-1z" class="X"></path><path d="M288 367l7 2-1 2h-1c-2 0-3-1-5-2h2l-2-2h0z" class="m"></path><path d="M418 783c0-1 1-2 1-3l1-1c0 1 0 0 1 1h0 0c2-1 3-1 5 0-2 4-7 8-10 11l-1-1 5-6h0l-1-1h0-1z" class="i"></path><path d="M499 838h0c1 1 1 0 1 1 3 4 7 8 10 12 0 1 0 1-1 1l-1-1c-3-3-7-6-10-10-1-1-1-2-1-3h1v1l1-1z" class="k"></path><path d="M354 260v-1c-2 0-3 1-5 0v-1c-1-1-1-1-1-2 2 0 3 1 5 1l-1-1h0l-3-1v-4c1-2 3-1 5-2-1 1-2 1-3 2s-1 1-2 3h1c1-1 1-1 3-2 1 1 1 2 2 3v4l1 1h-2z" class="O"></path><path d="M715 439c-3-4-5-9-7-13-1-3-3-5-4-8 3 5 9 10 11 16l-1 1c1 1 1 2 1 4z" class="Y"></path><path d="M402 554l3 3c0 1 0 3 1 4l1 1h0c-1 0-1 0-2-1-1 0 0-1-1-2l-1-1c0 2 1 3 3 5h2v1c1 0 3 0 4 1s1 2 2 3c-3-1-6-1-8-2-1-2-3-4-4-6v-1c0-1-1-2 0-3 0-1-1-1-1-1l1-1z" class="F"></path><path d="M392 702c3 6 7 8 12 11 3 0 5-1 8-1-1 2-4 3-6 4h0c-1-1-3-2-4-2-1-2-3-3-4-3-4-3-6-5-7-8l1-1z" class="M"></path><path d="M346 436c1 0 2-1 4-1v1c0 1 1 2 1 3 2 1 3 1 4 3h1 1v-2c1-1 1-1 2-1l1 1 1-1v2l3 3h-5l-4-2-9-6z" class="H"></path><path d="M441 414l2-1c0-1 0-2-1-4l-1-1v-2c2-1 2-1 2-3h0-2l2-2h1v1l1 1 4 2-5 3c0 1 1 1 1 2 1 1 0 3-1 4 0 1-1 2-2 3l-1-3h0z" class="Y"></path><path d="M349 685h0c0-4-2-11-1-14h2c1 2 1 6 1 9 0 2 1 4 2 6l-1 1h-1v2h0c-1-1-1-2-1-3l-1-1z" class="E"></path><path d="M695 696l2-2c1 1 2 2 4 2l-2 2-1 3v1c-3-1-6 0-10 1 2-2 5-4 7-6v-1z" class="R"></path><path d="M695 696l2-2c1 1 2 2 4 2l-2 2c-2 0-3-1-4-2z" class="h"></path><path d="M567 249c4 0 6 1 9 4l1 1h-7 0c-1-1-2-2-3-2-2 0-3 1-4 2h-2c0-2 4-3 5-5h1z" class="K"></path><path d="M269 366c2-1 3 1 5 2h0l-1-2v-1c1 0 2 1 2 2 1 1 2 3 3 4 0 1 1 2 1 3s0 1 1 2v2l-11-12z" class="e"></path><path d="M602 787c-2-3-6-5-7-8v-1l2 1 1-1 1-1 1 1c0 2 0 2 1 4l-1 1v1h1l1-1c1 0 2 0 3-1h2c-1 1-1 2-1 3-1 0-1 2-1 3-1 0-1 0-2-1v-1l-1 1z" class="N"></path><path d="M577 555c3 2 5 5 8 7 2 1 3 3 5 4l-1 3-6-6-6-6v-2z" class="V"></path><path d="M605 221c1 1 1 1 3 1-2 1-4 3-6 4v2c-4 0-9 0-14-1h2c3-1 6-1 8-2 3-2 5-2 7-4z" class="a"></path><path d="M766 489c0-1 0 0 1-1l3 1 2 12-3-3h-1v1l-1-1-1-9z" class="U"></path><path d="M274 351h1c1 2 0 2 0 4-2 1-5 0-7 0-1 0-3 1-4 0l-2-2c2-1 4-1 6-1h1c1-1 3-1 5-1z" class="Y"></path><path d="M404 422h1c4 2 8 2 13 0l7-3c-4 4-9 6-15 7h-2c-1 1-1 1-2 0h-2c-1 0 0 0-1-1v-2h1v-1z" class="G"></path><path d="M679 529l-2-1h-3l-1 1h-3l2-2h-2c-1 0-2 0-3-1h-1 2 4v-1h-2c-1-1-3-1-3-2 5 0 10 1 15 2l-3 3v1z" class="B"></path><path d="M422 398c1-2 3-3 5-5 0 0 1 0 2-1 1 2 0 3 1 4l8 1c-2 0-5 2-7 2l-1-1-1 2 2 2c-2 1-3-3-4-4h-3-2z" class="N"></path><path d="M699 246h4c1 1 2 1 2 1v2c-2 2-10 1-13 1l1-1v-1-1h-2-1c2 0 4-1 6 0 1 0 1 0 3-1z" class="K"></path><path d="M710 490h1c5 0 9-3 13-7l2-2c-2 4-4 7-8 10-3 2-6 3-9 5 1-2 1-4 1-6z" class="C"></path><path d="M330 527l23-5v1c-1 1-2 1-4 1l-1 1h-1c-4 3-8 1-12 3-1 1-1 2-3 2v-3h-2z" class="S"></path><path d="M368 528c2 2 4 1 7 1h0c1 0 2 0 2 1l1 2c-4 1-9 2-13 2v-1c0-2 2-3 3-5z" class="g"></path><path d="M754 248h3s2 1 3 1l10 4c-3 0-19-1-19-1l-1-1v-1c2 0 3-1 4-2z" class="C"></path><path d="M427 546c0 1-1 8-1 9h-2c2 2 2 1 2 3v2h-1l-1-2c0-1-1-1-2-1 0-2 2-3 2-4l-1 1c-1 0-2 1-4 0v1c-1 1-2 2-3 2h-2c2-2 5-3 7-5 1-1 2-1 2-2l4-4z" class="a"></path><path d="M562 540c1-1 2-1 3-2 0 2 1 4 2 6h1v1l1 1 1-1h2 1l1 1v1l-1 4h-1v-1l-2-2h-1v-1-1l-2 2h-1c-1-3-3-5-4-8z" class="L"></path><path d="M572 545h1l1 1v1l-1 4h-1v-1c1-2 0-3 0-5z" class="j"></path><path d="M627 390h3l1-1h1c0 1 1 2 1 3h0c0 2 1 3 2 4v3l2 2v6l-3-6-1-2h0c0-1 0-1-1-2v-1c-2-2-3-4-5-6z" class="D"></path><path d="M665 432c1 0 4-2 5-2l1 1h0c1 0 2-1 3-1l1 1c-2 2-9 6-12 6h-1c-1-1-1-1-1-2 1-2 2-2 4-3z" class="N"></path><path d="M437 484c1 0 1 0 1 1 1 2 2 4 4 6s5 3 7 3h2l-1 1c-4 0-8 0-11-3-2-1-4-4-4-6 0-1 1-1 2-2z" class="R"></path><path d="M433 691c2 0 3 0 4 1-6 2-10 5-13 9l2 3v1l-3-1c-1 0-2 1-2 1-1 0-1 0-1-1 3-6 8-10 13-13z" class="P"></path><path d="M251 351h1l1 1c1-1 2-3 2-4h1c0 2 0 3-1 5h2 5l2 2c-1 0-2 1-3 1v-2l-2 1c-1 0-2 1-3 0-4-1-7-1-11-1l1-1h4l1-2z" class="Q"></path><path d="M600 393l1-1c1-2 3-3 4-4 4 0 7 0 10 3 0 1-2 1-1 3l2 1 1 1c0 1 0 1 1 2l-1 1h0c-1 0-1-1-1-1-1-3-3-5-5-6-1-1-2-1-3-1h-3c-2 0-3 2-4 3l-1-1z" class="D"></path><path d="M657 550l1 2c-6 2-13 6-19 10-1-1-2-1-3-2l4-3 2 1c5-3 9-5 14-7h1v-1z" class="Z"></path><path d="M414 775c-1-2-1-5-1-7s1-7 0-8c0-1-1-1-1-1 1 0 2 0 3 1s1 3 1 4c0 2 1 2 0 4l-1 1h1v1c1 0 1 0 1 1v2l1 1-1 1c1 2 1 3 0 5h0l-1-1c-1-3 0-3 0-5h-1l1-1c-1-1-1-2-2-2v4z" class="V"></path><path d="M726 247l2-1c4-3 9-3 14-3l-2 2c-2 1-4 3-7 4h0c-3-1-5 0-7-2z" class="n"></path><path d="M530 840h1c2-1 3-2 5-3-1 1-2 2-2 3v1l-14 13v-3l10-11z" class="C"></path><path d="M402 407v1h1c2-1 4-1 7-1-2 2-5 3-6 4-2 2-4 5-6 6l-1-1v-1-2c0-1 1-3 2-4l3-2z" class="i"></path><path d="M648 750c1 0 5 6 5 7-2 0-4-4-7-6l-9-3c0-1 1-1 2-2v1c2-1 2-2 4-1 1 0 2-1 3-1v1 2l2 2z" class="h"></path><path d="M402 407c4-2 9-3 14-5 2-1 3-2 4-5h0v-1h0 1c0 1 0 3-1 4-1 2-2 3-3 4l-7 3c-3 0-5 0-7 1h-1v-1z" class="G"></path><path d="M609 614s0 1-1 2c-3 0-8-1-11-3v-1c1 0 2 0 3-1l5-2 1 1c1 1 1 0 1 1l1 1 1-1h0v3z" class="h"></path><path d="M584 449c1 0 2 0 3 1h1 0 2c0-1 0-1-1-1l-1-1c-1 0-1-1-2-1v-1h-1c0-1 0-2 1-2 2 2 3 4 6 5l1 1c4 1 5 1 7 4-1 0-3-1-4-1v1c-2-1-3-1-4-1l-1 1v3l-1-1c0-1 0-1 1-1v-1c-1-1-2-2-4-3l-3-2z" class="D"></path><path d="M587 451c3 0 6 0 9 2v1c-2-1-3-1-4-1l-1 1v3l-1-1c0-1 0-1 1-1v-1c-1-1-2-2-4-3z" class="I"></path><path d="M621 442l5 5v1l-5 2h-1-3v-1c0-2 0-2-1-4h0c1-1 2-1 3-1l2-2z" class="B"></path><path d="M621 442l5 5v1l-5 2h-1c0-1 1-1 1-2v-1h1c0-1 0-2-1-3 0-1-1 0-2 0l2-2z" class="S"></path><path d="M429 499h3c5 3 11 4 18 5-2 1-4 1-6 1l-1 1h-1c-1-1-2-1-3-1h-1l-1 1c-1-1-1-1-2-1 0-1-1-1-2-1v-2c-2-1-3-1-4-3z" class="C"></path><path d="M456 399c1 2 2 3 2 5l1 1v2c2 2 1 3 2 5 0 1 1 1 2 3v4c0 3 1 6 0 8 0 0-1-1-1-2l-1-1 1-1c-2-3-1-4-1-6 0-1-1-2-2-3 0-1-1-2-1-2l1-1c0-2-1-2-1-3h-1l1-1c-1-3-1-5-2-8z" class="H"></path><path d="M569 661c0 2 1 4 2 6v1c2 4 5 6 9 8h0 1v2h5l-3 1h-2l-5-3c-4-2-6-6-8-9l-1-1v-1c2-1 2-2 2-4z" class="L"></path><path d="M592 440l6 3c5 1 9 1 12 5v1c-1 1-1 1-3 1h0l-2-1-1-1c-4-3-9-5-13-8h1z" class="B"></path><path d="M444 632c2 0 3 0 4 2-4 1-7 3-10 5-2 1-3 2-5 2-1 1-5-1-6-1 6-2 11-4 16-8h1zM289 424h0 2c-1 2-2 3-2 5-1 1-2 2-3 4l-4 4 1 1-1 1c-1 1-1 2-2 3l-1-1h-1l1-1v-1c0-1 1-3 2-5 3-3 5-7 8-10z" class="X"></path><path d="M767 372l1 1h0l1-1h1c0 1-1 2-1 2l-2 2 1 1h3l1 1-1 1-1-1c0 1 1 2 3 3h1v-1h1c0 1 0 2 1 3h-2 0v2c-2-1-4-2-6-4-3-3-3-5-3-9h2z" class="V"></path><path d="M307 382c2 3 3 6 6 8l-1 1c0 1 0 3 2 4h0c1 1 2 2 2 4 1 1 1 1 1 2h0c-3-3-6-6-8-9 0-1 0-1-1-2s-3-4-4-6h2l1 1v-3z" class="a"></path><path d="M601 231h16l3 1h-1 1v1h-2v1h1l1 1h-7-4l-1-1c-1-1-2 0-4 0v-2l-3-1z" class="E"></path><path d="M628 684c-1-1-3-2-5-2 0-1-1-2-1-3-1 0-1 1-2 1s-2-2-3-4l-1-3 6 5c2 1 2 0 3 0s3 2 4 3h0c2 1 2 2 3 4-2 0-2 0-3-1h-1z" class="I"></path><path d="M601 400l14 5 4 2c-1 1-2 1-3 3v-1l-9-4c-2 0-5-2-6-4v-1z" class="G"></path><path d="M615 405l4 2c-1 1-2 1-3 3v-1-1c0-1 0-2-1-3z" class="O"></path><path d="M670 721l1 1c0 1 0 2 1 4l-2-1h-1c-1 1-1 2-2 2-2 1-5 6-5 6l-1-1c-4 4-5 9-9 12 0-2 4-9 6-11 1-1 1 0 1-1l11-11z" class="m"></path><path d="M761 360h6l1-2h0c2 0 5 0 7-1 1 0 2 0 4 1l1-1c0 1 0 2-1 2 0 1-1 1-1 1l-1 1h-3 0c-2 0-2 0-2-1h-1c0 1 0 1 1 1l1 1h0l-1 1c-2 0-4 0-5-1l-1-1c-1 0-1 1-2 0h-3v-1z" class="D"></path><path d="M501 888l8 16-1 3-8-15c0-2 1-3 1-4z" class="N"></path><path d="M414 775v-4c1 0 1 1 2 2l-1 1h1c0 2-1 2 0 5l1 1-1 3h1 0 1 1 0l1 1h0l-5 6 1 1-1 1c-1 0-2-1-3-1l-1-1 1-1h1l2-2c0-4 0-7-1-12z" class="m"></path><path d="M256 363h1c1 0 1 1 3 0 1 0 2 0 3 1h0c1 2 2 3 3 4h1l4 5c1 1 1 1 1 2v1c-1-1-2-1-2-3h0c-2 0-4-2-4-4h-2v3l-1-3c-2-2-4-2-7-3h-1c1 0 1 0 2-1l-1-2z" class="D"></path><path d="M317 472c-4-5-8-9-15-11-1 0-2 0-2-1h0c8 1 14 5 21 10-1 1-2 2-4 2z" class="S"></path><path d="M628 751h1c-1 3-3 7-2 9-1 1-1 1-1 3v5h1c1 2 3 2 4 3l1 1h0c0 1-1 2-2 3l-1-1v-1c-1-2-1-3-2-4l-1 1c0 1 1 2 0 3-1 0-1-2-2-3-2-5 1-14 4-19z" class="Q"></path><path d="M347 525h1l1-1 1 1h2 0l-1 2c1 0 2 1 4 1l-1 1h-1l1 1c1 0 1 0 2 1 1 0 1 1 2 1-1 1 0 1-1 1 0-1 0 0-1-1h0c-1-1-1-1-2-1l-1-1h-3c-2-1-3-1-4 0-2 0-4 0-6 1h-4 1c3-2 7-3 10-4 1 0 0 0 1-1l-1-1zm64 265l-11-6c3-1 4-3 7-5 1 1 2 1 3 3l-1 1 1 1v1h-1l-1-1c0-1-1-3-1-4h-1v1c0 1 1 2 1 3-1 1-1 0 0 1 2 0 4 1 5 2v2l-1 1z" class="X"></path><path d="M322 591c2 1 2 1 5 1-2 1-4 3-5 4-2 2-2 5-4 7-1 2-3 3-4 5h0c1-7 4-13 8-17z" class="B"></path><path d="M571 508v1l1 1c2 0 5-2 6 0l1 1-2 1v1l-1 1c-2 0-7 2-9 3l-1 1c-1-3 7-4 8-6h-2v-1l-2 1h-4-1c-2 1-3 2-5 3v1h-1c2-2 3-3 5-4l1-1c2 0 3-1 5-2h0l1-1z" class="b"></path><path d="M680 606c1 1 2 2 3 2h1l-2 1v4l1 1c1 2 2 3 4 5 1 1 1 1 2 3l-1 1h0l-2-1h0c-1 0-2-1-3-1h0v-1c-1-1-2-3-4-4 0-4 0-5 2-8l-1-1v-1z" class="X"></path><path d="M250 498v3c1 1 1 1 3 2-1 1-1 2-2 3h-1 0l1 1v1c0 3 0 7-1 10 0 2 0 3-1 5 0-4 1-9 1-13h-1l-1-1-3-1 1-6h0l1 1h1c0-1 0-2 1-2l1-1v-2z" class="V"></path><path d="M246 502l1 1h1c0-1 0-2 1-2l1-1c-1 2-1 4-2 6v1c1 1 1 1 0 2l-3-1 1-6h0z" class="H"></path><defs><linearGradient id="W" x1="450.873" y1="466.774" x2="441.613" y2="471.234" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#969594"></stop></linearGradient></defs><path fill="url(#W)" d="M456 462l2-2 1 1c-4 6-12 10-18 13v-2l-2-1 7-3c2-1 8-4 10-6z"></path><path d="M744 371c1-3 3-6 5-8s3-3 6-3h1 5v1h-2-1l-2 2c-4 2-6 6-9 9v-3c1-1 1 0 1-1-1 1-2 2-3 2l-1 1z" class="I"></path><path d="M654 276c2-1 5 0 8 0h11c3 0 6 0 8 1h0c-2 1-4 2-6 1-6-1-14 1-20 0l-1-2z" class="P"></path><path d="M405 506h0c2 2 3 5 4 7h3l-1 1c1 1 1 1 2 1 1 1-1 4 0 6-1 2-2 2-4 3-1-4-2-10-3-15l-1-3z" class="f"></path><path d="M435 601l1-5c1-1 2-3 2-5 0-1 0-1-1-2h1c1-1 1-4 1-6l1 1v2c-1 1-1 2-1 3 1 6-1 12 3 17l-1 1h-1l-1-2c-2-1-2-2-4-2h0v-2z" class="E"></path><path d="M537 809c6-6 13-9 20-14 4-2 7-4 11-6h0c-3 3-7 5-11 7l-13 11c-2 1-4 3-5 4l-2-2z" class="R"></path><path d="M472 824c5 3 9 6 13 10 3 2 6 4 8 7-1 0-3-1-4-2-2-1-3-2-5-2l-7-6c-2-1-4-2-5-4h0c1 0 1 0 2 1l2 1h0v-1c-2-1-3-2-4-3v-1z" class="k"></path><path d="M566 222c1 0 1-1 1-1 1-1 2-2 4-3 0-2 1-4 2-6v1 2l-2 2h1c2-1 3-2 4-2 1 1 1 1 1 2l3-1v1l-1 2v1c-1 1-3 1-4 1h-1l-1-2h-1c-1 1-1 3-3 3-1 1-1 0-1 1l-2 2c-1 0-2 1-2 1h-1c0-1 2-3 3-4z" class="L"></path><path d="M686 256v-4l1 2c1 1 1 1 2 1 3-1 4-1 7-1h1 4l1 1c-1 1-2 2-4 2-1 1-2 1-2 1-1 0-2 1-3 1h0c-3-1-5-1-7-3z" class="d"></path><path d="M427 223c1 1 2 1 3 2l1-1c1 0 1 1 2 1 1 2 2 2 3 3l-2 1c-2-1-5-1-7-1-4-1-8 0-12 0 0-1 1-2 2-2s2 0 3-1h0c2-1 4 0 6-1h0l1-1z" class="J"></path><path d="M623 779c-4-3-9-9-10-13 0-2-1-4 0-5h1c2 6 6 11 10 15 0 1-1 2-1 3z" class="i"></path><path d="M440 380l5 4c2 2 5 4 6 6h-1l-2 2 1 1c0 1 1 2 2 3h-2l-8-9h2 1 0c0-1-1-2-2-2l-1-1-2-1v-1l1-2z" class="I"></path><path d="M781 380h2c1-1 1-2 2-3h0c2-1 2-2 3-4 0 2 0 4-1 6l-3 2v4h-1l-1-1c-2 1-3 3-6 2l-2-1h0v-2h0 2c-1-1-1-2-1-3 2 0 3 0 5 2l1-2z" class="a"></path><path d="M775 380c2 0 3 0 5 2-1 2-1 2-3 2l-1-1c-1-1-1-2-1-3z" class="J"></path><path d="M770 598l1 1c1-1 1-2 1-3v-1c1-2 0-5 1-6l1-1h0v4 1c-1 2 0 4-1 5v3-1c0 5-2 10-3 14v-1l-2-4 2-11z" class="b"></path><path d="M544 249h23-1c-1 2-5 3-5 5h2l-3 3c-1 0-2 0-3 1h-1c0-1 1-2 3-4h0v-1h1v-1c-1-1-3-1-5-1l-4-1h-3c-1-1-3-1-4-1z" class="E"></path><path d="M592 502c0 2 1 3 2 5l-2 1v1h1c0-1 0 0 1 0v1c-1 0-1 1-2 1s-1 0-2-1v3l-1-1v-2c-3-1-10 2-12 3v-1l2-1c2-1 5-2 7-4h-1c1-1 2-2 4-3 1 0 2-1 3-2z" class="G"></path><path d="M592 502c0 2 1 3 2 5l-2 1h0c-2-2-2-1-4-1v-1l1-2c1 0 2-1 3-2z" class="f"></path><path d="M467 209l2 2h1l1-2h0c2 1 4 1 6 2 0 1 1 1 2 1l2 2c-1 1-1 2-2 2s-4-2-5-2c-2 0-1 1-2 0l-1-1h-1-1l3 3 1 1h-1l-8-7 3-1z" class="J"></path><path d="M656 501l1 3h3l1 4c1 1 1 2 1 3-3-2-6-3-8-4-3 0-6-1-8-3l1-1h2c1 1 4 0 6 0 1-1 0-1 1-2z" class="U"></path><path d="M565 406h1c0-2 1-5 2-6l2-2c1-2 2-3 4-5 0-1 0-1 1-2 2-1 3-2 5-4l1 1c-3 3-7 6-10 10-2 2-3 5-4 8-1 1-1 1-1 2v3h0c0 2 0 4-1 6l-1-1h-2v-1l1-1h1l1-1v-1h-2v-2-1c0-2 1-2 2-3z" class="F"></path><path d="M400 658c-1-3 0-6-1-8v-7c0-2 1-4 3-5 0 1 0 2 1 2s1 0 2-1h0v2 1c0 1-1 2-2 3l-1 2c-1 3-1 7 0 10l-1 1h-1 0z" class="X"></path><path d="M241 359c2 0 3 1 4 0 2 0 2 1 4 1l1-1c1 1 0 2 3 1h1c1 0 3 0 5-1h1v1 3c-2 1-2 0-3 0h-1-5-8c0-1-1-2-2-3v-1z" class="Q"></path><path d="M259 359h1v1 3c-2 1-2 0-3 0h0l-3-1v-1h4l1-2z" class="Y"></path><path d="M456 217l1-1v1 1c1 2 2 3 4 3h1l-1-1 1-1c1 1 2 2 3 2h0l-2-3c1-1 0-1 2 0h1 1c1 1 3 2 5 3-2 0-3 0-4-1h-1l-1 1c0 1 2 3 4 5-2-1-4-1-6-1l-2 1h0v-1l-1-1h-1-1v-1c-1-1-1-1-2-1h0c-1-1-2-3-2-4l1-1z" class="I"></path><path d="M544 795c2 1 2 1 3 2l-16 11c-3 2-5 5-8 6 5-8 14-13 21-19z" class="E"></path><path d="M520 251c1-1 1-2 2-2 7-1 15-1 22 0 1 0 3 0 4 1l-21 1h-1c-1 1-2 1-3 1l-3-1z" class="g"></path><path d="M632 405c0-1-1-2-1-3-2-1-3-4-4-6-2-3-4-5-6-8-2-1-5-2-5-3 3 1 7 3 8 6 1 1 2 2 3 2h0c-1-2-4-5-4-7l4 4c2 2 3 4 5 6v1c1 1 1 1 1 2h0c-1 2-1 4-1 6z" class="Q"></path><path d="M654 561c1-1 5-1 7-2l-5 5c-2 1-4 2-5 4l1 1h0 1l-1 1c2 0 3 0 4-1l2-1 2-2 1-1c2-2 2-2 4-2l-10 8h-4l-1-1c-1 0-1 0-1 1-1 1-1 2-2 3l-1-2c1-3 2-5 5-7l3-3v-1z" class="F"></path><path d="M383 473c2 1 6 5 8 7 2 1 2 2 4 2l8 7c-1 2-2 3-3 5v-1h-1l-1-1c-4-7-10-13-15-19z" class="C"></path><path d="M638 688l-9-28c2 2 4 7 5 10l6 11c-2 1-2 1-3 2 1 2 3 1 3 4-1 0-2 1-2 1z" class="R"></path><path d="M396 779l9-12c1-2 3-4 3-6 1 0 1 0 1 1 1 1 1 3 0 4-1 4-5 8-7 11l-3 3h-1-1l-1-1z" class="U"></path><path d="M258 535v-1l1-1 2 1c1-1 1-1 2-1v1c0 1 1 1 1 2l2 2v-1l2-1 1 1-1 1c0 1-1 2-1 3h1v1c-1 0-1 0-2 1h-1c-1 2-2 3-4 3l-2-1c-2-1-4-3-5-5l1-1c1 1 2 1 2 2 2 1 3 2 5 2 1 0 3-1 3-2s-1-2-2-4-2-1-5-2z" class="R"></path><path d="M421 404c1 1 2 1 3 2l-1 1c0 1-1 3-2 4-4 6-9 9-16 11 6-5 13-10 16-17v-1z" class="G"></path><path d="M805 263c4-5 9-12 14-16 3-3 7-6 11-5l-1 1c-1 0-2 0-3 1-3 2-6 5-9 8-4 4-7 8-11 12l-1-1z" class="P"></path><path d="M740 245c3 0 3 1 4 2l3 2h-1c0 1-1 1-1 2v2c-2 0-4 0-6-1l-6-3c3-1 5-3 7-4z" class="C"></path><path d="M689 456h1c3-2 6-4 8-8 0-1 1-2 1-2l1-3c1 1 1 3 1 5l-1 2c-1 3-2 6-3 8v1l-1-1-2 2-2-1c0-1 0-1 1-2l-3 1-1-2z" class="e"></path><path d="M512 178l2 1c1 1 1 1 1 3-1 0-1 0-1 1l1 1h-2v1l1 1h1 0v4h0v4l1-1v-1c1-1 2-1 3 0h3c-1 2-2 3-3 4l-1-2h0 0 2v-1h0c-1 0-2-1-2 0l-2 2h-1c-1-1-1-2-1-3h0v-5l-1-1v1 12c-1 4 0 10-1 13v-28c-1-2 0-4 0-6z" class="L"></path><path d="M670 689c-1 3-3 8-5 9h0c2-6 5-13 6-20 0-3 0-6 1-9 1 3 1 6 1 9 0 1-1 4 0 5 1 2 1 2 1 4-1 0-2 2-2 2h-2z" class="K"></path><path d="M562 422c1 1 2 1 2 3l-1 1 5 9c-2 0-2 1-3 2v-1l-2-1-2-1c0-1-1-2-2-3 0-1-1-2-1-3l1-1c1-1 1-1 1-2 1-1 1-2 2-3z" class="O"></path><path d="M562 422c1 1 2 1 2 3l-1 1h-1c1 2 1 3 1 6-2-2-3-4-3-7 1-1 1-2 2-3z" class="b"></path><path d="M560 425c0 3 1 5 3 7 1 1 1 2 2 4l-2-1-2-1c0-1-1-2-2-3 0-1-1-2-1-3l1-1c1-1 1-1 1-2z" class="I"></path><path d="M379 398v-3c1 1 1 3 1 4 0 3-1 6-2 9 0 1-1 2-1 2h-1c0 1-1 1-1 2l-4 7-1-2c-1 1-2 2-3 2h-1c2-2 4-4 5-6 2-3 3-6 5-8 1-1 2-2 2-3 1-1 1-3 1-4z" class="Q"></path><path d="M667 253c3-1 5-1 7-1v2c-1 1-1 1-1 2v2l-1 1c1 1 1 1 2 1-1 2-2 2-4 3l-1-1c-2 0-2 1-4 0h-3v-1l1-1 1 1c1-1 1-1 2-1h1c1-2 1-3 2-4 0-1-1-2-2-3z" class="U"></path><path d="M669 262c1-1 2-2 2-3l-1-2h0c1 0 2 1 3 1l-1 1c1 1 1 1 2 1-1 2-2 2-4 3l-1-1z" class="c"></path><path d="M461 786c-3-1-7-4-11-7-4-2-10-5-14-8l1-1h3c6 6 16 9 23 15l-2 1z" class="Q"></path><path d="M364 459l1-2c1-1 2-1 3 0 2 2 2 4 5 6 2 2 5 3 8 5 1 2 3 4 5 6 3 2 7 5 9 8-2 0-2-1-4-2-2-2-6-6-8-7-1 0-5-4-7-5-3-4-8-7-12-9z" class="R"></path><path d="M680 435v5c1 0 2-1 3-2v1l-4 3c-5 3-9 9-15 11v-3-1c2 0 2 0 4-1v-2h2c1-1 2-2 2-4l2 1 2-1v-1l2-2c0-2 1-3 2-4z" class="H"></path><path d="M729 366l19-5c-3 2-5 5-8 6-3 2-7 3-10 3 0-2 0-3-1-4z" class="N"></path><path d="M520 811l-1-1c-1-2-1-3-1-4 1-7 0-14 1-21l1-5 1 22c0 3 1 6-1 9h0z" class="C"></path><path d="M529 274c8 0 15 0 23 1 1 1 1 1 1 2-7 1-14 1-21 0-2 0-2 0-3-2v-1z" class="E"></path><path d="M789 261c-1 2 0 4 0 6-2 0-3 1-4 2v2c-1 0-1-1-2-1h0c-1-1-2-1-3-2l1-1c1 0 0-1 0-2l-2-1c-1-1-1-1 0-3h0 7 3z" class="I"></path><path d="M789 261c-1 2 0 4 0 6-2 0-3 1-4 2l-1-4c1-1 1-2 2-3h0v-1h3z" class="T"></path><path d="M643 572l1-1 1 1-2 2-4 5c-1 2-3 4-3 7h2l1 1-2 1-1-1-1 1c0-1 0-1-1-2h-2l2-5c0-1 1-2 2-4l1-2 3-3h3z" class="N"></path><path d="M640 572h3l-3 3h-3l3-3z" class="a"></path><path d="M637 575h3v1c-1 1-1 2-2 3-1 0-1 0-2-1v-1l1-2z" class="d"></path><path d="M634 581c1 1 1 2 2 3v3l-1 1c0-1 0-1-1-2h-2l2-5z" class="U"></path><path d="M611 712c-2-2-3-4-3-7-2-5-1-11 3-15 2-1 4-3 5-5l-1-3h1s1 1 2 1h0v4l1 1 4-1 1 1h-1c-3 2-4 1-7 2-3 0-6 4-7 6-1 4-1 10 1 13l2 2-1 1z" class="O"></path><path d="M556 224c2-1 5-2 6-4h0c2-1 3-3 5-4v-1h1 0v-2l3-1v-2h3l-1 1v1c-1 2-2 4-2 6-2 1-3 2-4 3 0 0 0 1-1 1l-1-1c-2 0-4 3-7 4l-3 3c-1-1-1-1-1-3l2-1z" class="D"></path><path d="M553 231c4-1 10 0 13 0h6v4h-13c-1 0-4 0-5-1s-1-1-1-3z" class="l"></path><path d="M365 518h1l2-2v1c0 1 1 2 1 3-1 2-1 4-1 6l1 1-1 1c-1 2-3 3-3 5v1c-1-1-2-9-3-11l1-3c0-2 2-3 3-3l-1 1z" class="C"></path><path d="M362 523l1-3c0-2 2-3 3-3l-1 1c2 2 1 5 1 7h0-1c-1-1-2-2-3-2z" class="W"></path><path d="M435 603c2 0 2 1 4 2l1 2h1l1-1c3 3 5 5 9 6l-5 1v1h2v1c-2 0-3 1-5 1v-1h-2c1-2 1-2 2-3-1-1-2-2-3-2h-3-1c-1 0-1-1-2-2l1-1 1 2 1-1-1-1-1-2h0v-2z" class="R"></path><path d="M527 870h0l-3 2h-2l1-1h1c1-1 4-4 4-5l1-3c1-2 1-3 3-3v-1c0-1 0-1-1-2h0v-1-1c1-1 3-1 4-1 0-1 1-2 1-3h-1l-1-1h2c1-1 3-2 5-2-1 2-13 22-14 22z" class="H"></path><path d="M261 546c2 0 3-1 4-3h1l1 1c1 1 1 4 1 5v2 7c-1-1-1-2-1-4 0-1 1-2 0-3l-10 5v-1c1-2 2-3 4-4v-4l-1 1-1-1 2-1z" class="M"></path><path d="M261 546c2 0 3-1 4-3h1l1 1-1 1 1 2-6 4v-4l-1 1-1-1 2-1z" class="L"></path><path d="M430 592c0 3 0 4 1 6l1 1 1-1c1 1 1 2 2 3v2h0v2h0l1 2 1 1-1 1-1-2-1 1c1 1 1 2 2 2h1 3c-1 1-1 1-1 3v1 1c-2-2-4-3-6-4s-1-1-2-2l1-1c0-1 0-2-1-3h-2 0c0-1 0 0 1-1 0-1 1-1 1-1v-1c-1 0-1 0-2-1l1-1-1-1c0-2 0-4 1-7z" class="S"></path><path d="M571 508c-1 0-1 0-2-1h0c-2 0-3 0-5 1v-1c2-1 5-2 7-3s4-1 6-1c0 1 0 2 1 2 2 0 4-1 6-1 0 0-1 1-2 1h-2l-1 1v1h1 2c1 1 2 1 3 0h1c-2 2-5 3-7 4l-1-1c-1-2-4 0-6 0l-1-1v-1z" class="J"></path><path d="M717 668l18-33c-2 10-8 20-13 28l-3 5h-2z" class="T"></path><path d="M683 534l1 1v1c-1 0-1 1-3 1l-1 1v1c-3 0-8 1-11 0l-5 1c-1-1-1-1-2-1l1-3c2 0 4 0 6-1h1c1 0 2 0 4 1 1 0 1-1 2 0l1 1 1-1h3 1l1-2z" class="G"></path><path d="M793 259l1-1v1c0 1 1 1 2 2v2l1 2v3 6l-1-1-3 1 1 35c0 5 0 12-1 17-1-6 0-14 0-20v-47z" class="B"></path><path d="M794 268c0-2-1-3 0-5h2l1 2v3h-3z" class="C"></path><path d="M797 268v6l-1-1-3 1 1-6h3z" class="W"></path><path d="M392 682h3v1h1l2-1v2c-3 1-5 4-6 7-1 4-1 8 0 11l-1 1-1-3c-2-6-1-12 2-18z" class="G"></path><path d="M784 310c1 1 1 2 3 1h1l-1 16v5h-1v-5c0-1-2-2-3-3v-6c0-1 0-1 1-2l-2-2c1-1 1-2 2-4z" class="B"></path><path d="M742 243c5 0 11 0 16 1v1 1l-1 1-1-1-2 2c-1 1-2 2-4 2h-3 0l-1-1h1l-3-2c-1-1-1-2-4-2l2-2z" class="W"></path><path d="M747 249l1-1c1 0 2-1 2-2h1 1c2-1 3 0 4 0l-2 2c-1 1-2 2-4 2h-3 0l-1-1h1z" class="Z"></path><defs><linearGradient id="X" x1="357.253" y1="625.576" x2="356.441" y2="623.436" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#92918f"></stop></linearGradient></defs><path fill="url(#X)" d="M367 618v3c-2 2-5 3-8 5l-10 4h-5c-4 1-7 1-10-1 13 0 23-4 33-11z"></path><path d="M392 530c0-1 1-1 2-2l2 1h0c1-1 4 0 5 0h0l-4 2c0 2 2 2 3 3 0 1-1 2-2 2-3 1-7 1-10 2l-2 1v-1c1-1 2-2 3-2s2-1 3-1c-1-1-1-1-1-2-1 0-2 0-4-1 2-1 3-1 5-2z" class="S"></path><path d="M387 532c2-1 3-1 5-2 1 0 3-1 4 0l-1 1v1h1c-1 2-2 2-4 3h0c-1-1-1-1-1-2-1 0-2 0-4-1z" class="G"></path><path d="M511 958c0-3-1-7-1-10v-21c-1-1-1-3-1-4v-12h1c0 1 1 2 1 3l1 43-1 1z" class="T"></path><path d="M581 477v1l1 1c2 3 3 5 5 7-1 3-2 5-4 6-2 2-5 3-8 3v-3c2 0 3-1 5-1 1-2 3-4 3-5l-1-1-4 4c-1 2-2 1-4 1 2 0 4-2 5-4 1-1 2-4 2-5v-4z" class="a"></path><path d="M539 811c-10 8-19 17-30 23 7-9 19-17 28-25l2 2z" class="T"></path><path d="M430 574h1 1l-1 2 1 1 1-1c2-1 6 1 8 2h0c1 1 2 1 3 2h1l-1 1c-4 0-8 1-12 1 0-1 0-1-1-2h-2c-1 0-2-1-3-1 0-1 0-4 1-4 2 0 2 0 3-1z" class="i"></path><path d="M694 586c3-3 8-8 11-10l2 2v1c-2 4-5 7-8 11 0 0-1-1-2-1l-3-3z" class="U"></path><path d="M707 578v1c-2 4-5 7-8 11 0 0-1-1-2-1 2-2 3-4 5-5 0-1 0-1 1-2s2-3 4-4z" class="R"></path><path d="M575 534l3-1 1 1v8c-1 0-2 1-3 1l-1 2-1 1-1-1h-1-2l-1 1-1-1v-1l1-4h1l1-1c2 0 3-3 4-4v-1z" class="V"></path><path d="M568 545c3-1 4-3 6-5v-1h2v1c-1 0-1 1-1 2l1 1-1 2-1 1-1-1h-1-2l-1 1-1-1z" class="H"></path><defs><linearGradient id="Y" x1="576.816" y1="474.297" x2="583.38" y2="471.108" xlink:href="#B"><stop offset="0" stop-color="#696966"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#Y)" d="M591 483c-9-10-21-13-29-23 1 1 2 1 3 2 2 2 5 3 7 5 3 1 7 3 10 4h0l1 1 2 1v1l2 2c1 1 3 3 4 3v4z"></path><path d="M285 529c0-1 0-1 1-3 5 2 10 0 15 2 2 1 4 1 6 1h0l4 1s1 1 2 1v-1c2-1 4 0 5 0h1l2 1v1h-1l1 1c-1 1-1 1-3 1-1 0-2-1-4-2h-4c-2-1-2-2-3-2-5-1-10 0-15 0h-4c-2-1 0-1-2-1h-1z" class="Z"></path><defs><linearGradient id="Z" x1="387.645" y1="517.449" x2="380.848" y2="531.607" xlink:href="#B"><stop offset="0" stop-color="#a5a3a3"></stop><stop offset="1" stop-color="#dbdad9"></stop></linearGradient></defs><path fill="url(#Z)" d="M375 527c8-1 12-4 18-8 1 0 1 1 3 1v1c-5 5-12 8-18 11l-1-2c0-1-1-1-2-1v-2z"></path><path d="M450 504c3 0 6 2 9 4h0-1-3c-3 0-8-1-9 0h1c3 1 6 3 9 4h-1c-4 0-8-1-12-3h0l-1 1c-1 0-4-2-5-2 0-1 2-1 3-1-1-1 0-1-1-2 1 0 2 0 3 1h1l1-1c2 0 4 0 6-1z" class="c"></path><path d="M301 624c-5-12-7-25-10-37-1-5-2-9-2-14h0c1 6 3 12 4 18 1 5 2 10 4 14 1 4 3 7 4 11v2c1 1 1 3 1 5l-1 1z" class="K"></path><path d="M372 486c1 4 1 8 0 12 0 1 0 3 1 5 0 1 1 1 2 2 2 1 6 0 8 1-1 1-2 2-3 2l-4 1-2-1v-1h-1c-1 0-2-1-3-2l-1-2c0-6 0-12 3-17z" class="B"></path><path d="M633 399l1 2 3 6 3 4c0 1 1 2 2 2l-1 2c0 1 1 2 1 2l1 5-1-1h-1c-1-1 0-2 0-3h-3c1-1 1-1 2-1l-3-3h0v-1c-2 0-2 0-3-1v-1c-2 0-2 0-3-1l1-4v-1c0-2 0-4 1-6z" class="O"></path><path d="M633 399l1 2c0 4 1 7 3 10v2c-2 0-2 0-3-1v-1c-2 0-2 0-3-1l1-4v-1c0-2 0-4 1-6z" class="E"></path><path d="M484 837c2 0 3 1 5 2 1 1 3 2 4 2 3 3 6 5 9 8 2 3 4 5 7 7v1l1 3-2 1-24-24z" class="m"></path><path d="M389 747c3 3 7 6 7 10l1 1c0 1 0 1 1 2v1 2c0 4-1 9-4 13-1-1-1-1-2-1h-2c1-1 1-2 2-3 0-1 2-2 2-4 2-8-1-14-5-21z" class="J"></path><path d="M590 431c1 0 4 1 6 1l-3 3-1-1-1 1-2 2 3 3h-1l-3-1c-2 0-2 1-3 1l-2-1v1l-1 1v-1l-1-1-2 2-1-1c1-3 5-5 7-8v-1h5zm-314-84l-3 3 1 1c-2 0-4 0-5 1h-1c-2 0-4 0-6 1h-5-2c1-2 1-3 1-5h-1l1-1 1 1 2-1c5 1 11 1 17 0z" class="L"></path><path d="M257 348l2-1c0 2 0 3 2 4 2 0 4 0 7-1h1v2h-1c-2 0-4 0-6 1h-5-2c1-2 1-3 1-5h-1l1-1 1 1z" class="I"></path><path d="M256 347l1 1c1 0 1 1 1 2s-1 2-1 3h-2c1-2 1-3 1-5h-1l1-1z" class="j"></path><path d="M476 794c5 4 11 8 16 12l19 17c-7-2-13-9-18-14l-18-13 1-2z" class="R"></path><path d="M718 416c-1 0 0 0-1-1 1-1 1-1 1-2v-1h-1c-4-1-7 1-10 0v-1l9-2h1c0-1 0-1 1-1h3c2 1 3 2 4 4s4 5 5 7c-1-1-2-1-2-1l-1-1c-2 0-4 0-6-1v1c-1-1-1-1-3-1z" class="H"></path><path d="M721 416v-1c-1 0-1-1-2-1 0-2 0-2 1-3h0c1 1 1 1 1 2 2 0 2 1 4-1 1 2 4 5 5 7-1-1-2-1-2-1l-1-1c-2 0-4 0-6-1z" class="J"></path><path d="M258 527v-1h-1c-2 1-1 1-2 3 0 0-1 1-1 2l-1-1c-1-1 0-8 1-9 0-1 0-2-1-2l1-1v-5c0 1 1 2 1 3v1h1c1 1 1 1 1 2h1 1c0 1 1 1 2 1 0 1 1 2 1 2 1 2-2 3 1 6 1 0 0 0 1 1h-1 0-1c-2-1-1-1-2-1l-2-1z" class="I"></path><path d="M430 648c2-2 3-2 6-3 1-1 7-1 8 0 3 1 11 7 11 9l-2-2c0 2 0 2 1 3l-1 1h0l-1 1h0c-1-1-2-3-4-4v-1h0c-3-1-4-2-6-3v-1-1h-1c-3 1-8 1-11 1z" class="O"></path><path d="M448 652v-1h1 2c1 1 1 1 2 1 0 2 0 2 1 3l-1 1h0l-1 1h0c-1-1-2-3-4-4v-1z" class="D"></path><defs><linearGradient id="a" x1="356.613" y1="256.494" x2="365.133" y2="264.179" xlink:href="#B"><stop offset="0" stop-color="#838180"></stop><stop offset="1" stop-color="#adacab"></stop></linearGradient></defs><path fill="url(#a)" d="M362 259c1 0 2-1 2-1l1-1 5 1h0v2h4 3 5-1c-2 1-8 1-11 1h1 4c2 0 4 0 6 1h4l-27 1c-2 0-7 0-9-1v-1l5-1h2c2 0 4 0 6-1z"></path><path d="M674 437h0c2-2 4-3 6-4v2c-1 1-2 2-2 4l-2 2v1l-2 1-2-1c0 2-1 3-2 4h-2v2c-2 1-2 1-4 1v1h-1c1-3 2-4 3-5l-2-2 5-4 5-2z" class="c"></path><path d="M674 437v1c-1 1-3 2-4 4-1 1-2 2-4 3l-2-2 5-4 5-2z" class="M"></path><path d="M563 254c1-1 2-2 4-2 1 0 2 1 3 2v1c0 2-1 3-2 5h1l-2 1-1 1c-1 0-3 0-5-1h-6l-1-1 2-2h1c1-1 2-1 3-1l3-3z" class="Y"></path><path d="M556 258h1c1-1 2-1 3-1s2 1 3 2 3 0 4 2l-1 1c-1 0-3 0-5-1h-6l-1-1 2-2z" class="T"></path><path d="M367 618c6-6 6-11 7-19 0 2 0 4 1 5s1 2 2 3l2 2c-1 2-1 3-2 5 0 1-1 3-2 4-1 0-1 1-3 2-1-1-3 0-5 1v-3z" class="R"></path><path d="M530 840l33-25c-1 2-1 2 0 3-6 5-13 9-19 13l-10 10v-1c0-1 1-2 2-3-2 1-3 2-5 3h-1z" class="T"></path><path d="M442 510l1-1h0c4 2 8 3 12 3h1c1 0 2 1 3 1v1c2 1 2 2 2 4v1 1l1 2c1 1 0 3 0 5s0 4-2 7l-1-1c1-2 2-7 1-9h0c-2-1-2-3-3-4s-2-2-2-3v-1l-1-1c-3-2-8-3-12-5z" class="D"></path><path d="M274 477v-1c1-3-1-4-1-7-1-1 0-3 0-4h-3-1c-1-2 1-2 1-3 0 0-1-1-1-2 1 0 2 1 3 2v-1c-1-1 0-1-1-2l1-4v-2-1c-1-1-1-1-1-2l1-1 1 2v-1c1-2 3-4 4-6v-1c0 1 1 2 2 3-1 2-2 3-3 5-1 3-1 4-1 8-1 1-1 3-1 5 0 3 4 12 0 13h0z" class="B"></path><path d="M521 884c0 1 0 2 2 2-4 7-9 15-11 23-1 1-1 1-3 1l-1-3 1-3 12-20z" class="O"></path><defs><linearGradient id="b" x1="378.991" y1="590.964" x2="367.893" y2="589.315" xlink:href="#B"><stop offset="0" stop-color="#b0aeaf"></stop><stop offset="1" stop-color="#d4d3d2"></stop></linearGradient></defs><path fill="url(#b)" d="M368 578c1-1 1 0 3 0h0c5 8 8 15 8 24h-3c0 1-1 1-1 2-1-1-1-3-1-5 1-8-2-14-6-21z"></path><path d="M605 557h-3c-1 0-2-2-3-3h-1v1 1l-2-2c-1-1-1-3-1-5l-3-10h0c0-2 1-4 2-5h1c0-1 0-1 1-2l1 1c0 1 1 2 1 3h3l-2 3-2-2c-1 3-2 4-1 7 0 1 1 1 1 1 0 4 1 6 3 8l5 4z" class="D"></path><path d="M717 668h2l-8 14-3 3-7 11c-2 0-3-1-4-2 5-5 9-10 14-16 2-3 4-7 6-10z" class="G"></path><defs><linearGradient id="c" x1="441.046" y1="505.743" x2="429.191" y2="510.795" xlink:href="#B"><stop offset="0" stop-color="#aaaba8"></stop><stop offset="1" stop-color="#d6d3d4"></stop></linearGradient></defs><path fill="url(#c)" d="M430 503c1 0 2 1 3 1s2 0 2 1c1 0 1 0 2 1l1-1h1c1 1 0 1 1 2-1 0-3 0-3 1 1 0 4 2 5 2 4 2 9 3 12 5l1 1-22-6c-2-1-3-1-5-1v-5h2v-1z"></path><path d="M593 495v1 2l3 2h1c0 1-1 2-1 2v3l-2 2c-1-2-2-3-2-5-1 1-2 2-3 2-2 1-3 2-4 3s-2 1-3 0h-2-1v-1l1-1h2c1 0 2-1 2-1-2 0-4 1-6 1-1 0-1-1-1-2h1c6-1 10-3 15-8z" class="T"></path><path d="M592 502v-1c-1 0-1-1-1-1v-1h1v1c1 1 1 2 3 3l1-1v3l-2 2c-1-2-2-3-2-5z" class="G"></path><path d="M677 678c1-1 1-2 3-2-2 10-8 19-15 27l-9 9c-1 1-2 1-3 2l-1-1 2-2c3-2 5-4 7-7 4-4 9-8 10-14l-1-1h2s1-2 2-2c0-2 0-2-1-4 2-1 3-3 4-5z" class="C"></path><path d="M620 643c3 6 1 15-1 21l-2 3-1-4c1-1 1-1 1-2 1-1 1-2 1-4-2 3-3 6-6 8 0-2 3-6 3-9 2-4 3-9 3-14l2 1h0z" class="R"></path><path d="M260 360c2 0 2 0 4-1h6l2 1c1 0 1 0 2 1 2 4 5 8 7 12l-2 1c0-1-1-2-1-3-1-1-2-3-3-4 0-1-1-2-2-2v1l1 2h0c-2-1-3-3-5-2-1 0-3-2-4-2h-2c-1-1-2-1-3-1v-3z" class="Q"></path><path d="M270 359l2 1c-1 1 0 2-1 3h-1v-4z" class="I"></path><path d="M463 850v-2h1c2 1 3 4 4 6 5 7 9 14 13 21l-1 2-11-18c-1 1-1 1-3 1l-5-7c1-1 1-2 2-3z" class="P"></path><path d="M463 850l6 9c-1 1-1 1-3 1l-5-7c1-1 1-2 2-3z" class="V"></path><path d="M604 234c-8 2-18 0-27 0v1h-1s-1 0-1-1 0-2 1-3h25l3 1v2z" class="g"></path><path d="M582 551c3 6 6 11 12 14l1 1 6 3v3l-1 1-5-2v-1c-2 1-3 1-4 1l-1 1h0l-1-3 1-3c-2-1-3-3-5-4 0-4-3-5-4-8 0-1 0-2 1-3z" class="K"></path><path d="M590 566l5 4c-2 1-3 1-4 1l-1 1h0l-1-3 1-3z" class="e"></path><path d="M451 612h0c3-2 5-4 7-6l-3 5h4c1 0 2 0 3-1h0l-1 1c-1 0-1 1-2 2v1l-1 1c-3 1-5 3-8 4l-2 1v1c-2-1-4-1-6 0h-1l1-2c1 0 2-1 3-1v-1l-2-1c2 0 3-1 5-1v-1h-2v-1l5-1z" class="I"></path><path d="M445 617l1-1c3 0 5-1 8-2 1 0 2-1 4-1l1 1-1 1c-3 1-5 3-8 4l-2 1v1c-2-1-4-1-6 0h-1l1-2c1 0 2-1 3-1v-1z" class="V"></path><path d="M601 536h0c0-1 1-2 1-2 3-5 11-7 16-8s8 1 12 4l-1 1c-3-2-3-2-6-2h-3c-3 0-7 1-9 2-3 1-6 3-7 5-2 2-3 4-3 6v1h-2v-2h-1l1-2 2-3zM398 728c-1 0-1-2-2-3-2-1-4-2-5-3l3 6c-1-1-3-2-4-4-2-2-4-4-4-7 2-1 6-1 8-1 3-1 6-1 8-2 1 0 3 1 4 2-5 1-10 3-15 5h1c3 2 5 4 7 6l-1 1z" class="E"></path><path d="M504 213v2c-2 1-3 1-4 1 1 2 1 2 1 3h-1v-1h-1v1 1c0 1 0 1-2 1s-4-1-6-2c-4-1-9-1-12-3 1 0 1-1 2-2h3c2 1 3 1 5 1 2-1 5 0 8 0s5 0 7-2z" class="Y"></path><path d="M313 512l4 1 1 1c1 0 1 0 2-1l1 1c0 1 0 1 1 1 1 1 2 1 3 1s1 0 2 1c2 1 2 1 4 1v-1h0l1-1 3 1c1 0 2-1 3-1h2v1h2l2 1c-7 1-14 2-21 2-2 0-4 0-5-1-2 0-2 0-2-2-2-1-2-3-3-5z" class="B"></path><path d="M316 517h1v-2l1 1c0 1 1 2 3 2-1 1-2 1-3 1-2 0-2 0-2-2z" class="E"></path><path d="M313 512l4 1 1 1c1 0 1 0 2-1l1 1c0 1 0 1 1 1 1 1 2 1 3 1l1 1-1 1c-2 0-3-1-5-2h-2l-1-1v2h-1c-2-1-2-3-3-5z" class="P"></path><path d="M544 795c10-7 20-13 31-19 3-1 7-4 11-5-2 1-3 2-4 3-8 5-16 9-23 15l-9 6c-1 0-2 2-3 2-1-1-1-1-3-2z" class="S"></path><path d="M481 875c5 9 10 20 14 30 1 4 1 7 3 10l-1 1-11-25c-2-5-4-9-6-14l1-2z" class="E"></path><path d="M654 439l2 1 1-1-2-2 3-3 1 1-2 2c1 1 1 2 2 3h2 1c3 0 5-1 7-1l-5 4c-2 1-5 2-8 2-2 1-4 1-7 0v-2c0-1 1-2 1-4l2-1 2 1h0z" class="B"></path><path d="M652 438l2 1h0c-1 0-1 1-2 2v2h3l-1 1c0 1 2 1 2 1-2 1-4 1-7 0v-2c0-1 1-2 1-4l2-1z" class="L"></path><path d="M659 440h2 1c3 0 5-1 7-1l-5 4c-2 1-5 2-8 2 0 0-2 0-2-1l1-1c2 0 3-1 5-2l-1-1z" class="F"></path><path d="M399 493h1v1l2 1c1 0 2-1 3-1h1c-1 1-1 2-2 2 0 2 1 3 3 5v-1-1h2l2 2c0 1 0 1 1 2 1 2 0 3-1 5 0 1-1 1 0 3l1 1v1h-3c-1-2-2-5-4-7h0c-1-1-1-3-2-3l-4-10z" class="B"></path><path d="M404 496c0 2 1 3 3 5v-1-1h2-1v3h-1l1 2c0 1 0 1-1 2h0c-2-3-3-6-4-9l1-1z" class="E"></path><path d="M272 478l1 1-1 1h1l3-2c2-2 2-5 2-7-1-1-1-4-1-5v-3h1l2 1v3l1 1-1 1h0c1 3 1 6 0 8s-3 3-4 3c-3 2-5 4-8 5h-1v-2l-1-1c2-1 2-1 3-2-1-2-1-3-2-5 1-1 2-2 3-4l2 2c0 1 0 1-1 2l1 1-1 1h-1l1 2c1 0 0 0 1-1z" class="h"></path><path d="M580 412c3-5 6-11 10-15l1-1h0c-2 0-2 0-3-1 2-1 4 0 6 0l1 1c1-3 3-5 6-7-2 2-3 4-4 6v1c2-1 2-2 3-3l1 1c-1 2-1 4 0 6h0v1l-1-1c-2-1-2 0-3-1l-1-1c-1 1-2 1-3 2s-1 2-3 2l-1 1h-1l-2 4c-1 1-1 1-1 2l-3 5h-1l-1-2z" class="k"></path><defs><linearGradient id="d" x1="328.789" y1="582.216" x2="343.118" y2="591.364" xlink:href="#B"><stop offset="0" stop-color="#b8b6b5"></stop><stop offset="1" stop-color="#eae8e7"></stop></linearGradient></defs><path fill="url(#d)" d="M322 591v-1c8-5 18-10 27-8 0 3 0 3-2 5h-2c-2-1-5-1-7-1l-1 1h-2c-1 0-1 1-2 2-1 0-1 0-2 1-2 0-3 1-4 2-3 0-3 0-5-1z"></path><path d="M407 636c2 0 4 0 6-1 1 0 3 1 3 0 1 0 3-3 4-4 1 2 0 4-1 6-2 1-2 1-3 2v4 1h-3v1l-2-1v1-2c-1-1-3-1-5-1h-1v-1-2h0c-1 1-1 1-2 1s-1-1-1-2c2-1 3-1 5-2z" class="C"></path><path d="M409 640c2-1 3-1 5-2v1h0c-1 2-2 3-3 5v1-2c-1-1-3-1-5-1l1-1c1 0 1 0 2-1z" class="R"></path><path d="M402 638c2-1 3-1 5-2v2h1c1 1 1 1 1 2h0c-1 1-1 1-2 1l-1 1h-1v-1-2h0c-1 1-1 1-2 1s-1-1-1-2z" class="E"></path><path d="M450 458c2-3 4-5 7-7l1 1-3 3h1l1 1-3 3v1a30.44 30.44 0 0 0-8 8l-7 3-4 1-10 3-1-1c7-2 14-5 20-9h0c2-1 5-3 6-5 1-1 0-1 0-2z" class="H"></path><path d="M577 613h0l1 1c1 1 1 0 2 1l2 1c0 2 0 3 2 5l2 1c6 3 10 8 13 14l-1 1c-3-6-9-11-16-13h0c-3-1-7 0-9-1 1-2 4 0 6-2-1 0-2-1-3-2h1c1 1 1 1 2 1-1-2-3-3-5-5v-1l3-1z" class="C"></path><path d="M497 916l1-1c5 16 9 32 12 48l-1 3-4-18h0-1c-1-2-2-5-2-7l-4-13h0v-1c1-1 1-2 1-3l-1-1c0-2-1-4-1-6v-1z" class="i"></path><path d="M622 582h2c-4 14-2 29 5 42l6 10h-1c-10-11-13-22-14-37 0-5 0-10 2-15z" class="K"></path><path d="M537 208c1 2 0 3 0 5-1 1-2 2-3 2h-2c-1 1-2 1-2 1-1 0-2 0-3 1l1 1v3c-1 1-1 1-1 2-1 1-3 2-4 2l-1-1c1-1 1-2 1-3h-2c-1 1-1 1-2 1h-1v-2c-2-2-3-3-3-5l3 2h1l1 1h1l3-2v-1h1l2-2c5-1 7-2 10-5z" class="D"></path><path d="M591 479h1c1 1 2 3 4 4h0c2 2 3 3 4 6v3c-1 3-1 6-3 8h-1l-3-2v-2-1c1-2 0-4 0-7l-2-5v-4z" class="V"></path><path d="M593 488l3 2 2 2-2 1c0 1 1 1 0 2v5l-3-2v-2-1c1-2 0-4 0-7z" class="G"></path><path d="M591 479h1c1 1 2 3 4 4h0v3 1c1 1 0 2 0 3l-3-2-2-5v-4z" class="f"></path><path d="M336 531h4c2-1 4-1 6-1 1-1 2-1 4 0h3l1 1c1 0 1 0 2 1h0c1 1 1 0 1 1l-2 1-1 1h-4v1h3c1 0 2 1 3 1-2 0-4-1-5 0s-1 0-2 0c-1-1-3-1-4 0h-1l-1-1-2-1c-1 0-2 0-3 1-1-1-2-1-3-2h-2l1-1 2-2z" class="d"></path><path d="M350 530h3l1 1c1 0 1 0 2 1h0c1 1 1 0 1 1l-2 1h-3-1c-2 0-5-1-8-1l6-3h1z" class="E"></path><path d="M568 435l5 8c-2 0-2 1-3 2l4 4-1 2h-1c-1 0-2-1-3-2v1h-1c-2-2-5-5-6-8 0-2-1-3-1-4h0c0-1 1-2 2-3l2 1v1c1-1 1-2 3-2z" class="Y"></path><path d="M568 435l5 8c-2 0-2 1-3 2-2-1-4-4-4-6 0-1 0-2-1-2 1-1 1-2 3-2zm76 170c0-2 1-3 1-4h1l1-3c1 8 2 14 7 21-2 0-3 0-5-1l-1 1c-1-1-2-3-2-4l-2-1v-1 2h-1c-2-1-2-1-2-3v-4l3-3z" class="W"></path><path d="M645 605c2 1 4 7 4 10v1l-3-3c-1-1-2-3-2-4 1-1 1-2 1-4h0z" class="a"></path><path d="M644 605h1 0c0 2 0 3-1 4 0 1 1 3 2 4v2l-2-1v-1 2h-1c-2-1-2-1-2-3v-4l3-3z" class="M"></path><defs><linearGradient id="e" x1="467.745" y1="874.391" x2="483.255" y2="874.117" xlink:href="#B"><stop offset="0" stop-color="#464848"></stop><stop offset="1" stop-color="#777372"></stop></linearGradient></defs><path fill="url(#e)" d="M469 859l11 18c2 5 4 9 6 14l-2 2s0-1-1-1c-5-11-10-21-17-32 2 0 2 0 3-1z"></path><path d="M565 406v-2c0-2 0-2 1-3v-2h-2c0-2 4-6 6-8 1 0 2-1 2-2 1-1 0-1 1-2s7-7 9-7v1c1 0 0 0 1 1l-3 4h0l5-4v1c-1 2-3 3-4 5l-1-1c-2 2-3 3-5 4-1 1-1 1-1 2-2 2-3 3-4 5l-2 2c-1 1-2 4-2 6h-1z" class="H"></path><path d="M528 221v-3l-1-1c1-1 2-1 3-1 0 0 1 0 2-1h2l1 1 1-1 2 1v1h3c0 1 0 1-1 2 0 1 0 1-1 2h0c-1 0-2 1-2 1l-1 1 1 1h-1s-1 1-1 2c-1 0-3 1-3 2h-2c-1 1-1 1-2 0 0-3 0-4 1-7h0-1z" class="D"></path><path d="M528 221v-3l-1-1c1-1 2-1 3-1 0 0 1 0 2-1h2l1 1 1-1 2 1v1h-1 0v1c-1 1-2 0-3 1 0 0-1 0-1 1h0l1 1h0-4-1-1z" class="L"></path><defs><linearGradient id="f" x1="744.899" y1="425.222" x2="754.579" y2="423.177" xlink:href="#B"><stop offset="0" stop-color="#9fa29c"></stop><stop offset="1" stop-color="#b8b4b9"></stop></linearGradient></defs><path fill="url(#f)" d="M740 406c3 5 7 9 10 14 2 4 4 8 6 11 1 2 3 5 3 7l-1 1 1 3h-2c-2-7-7-15-11-22-3-5-6-10-9-14h1 2z"></path><path d="M715 439c0-2 0-3-1-4l1-1c3 4 7 9 9 14 2 3 4 6 5 9 1 1 1 2 2 3l1 4h-3-1l-13-25z" class="G"></path><path d="M729 464v-1l-1-1v-3h-1v-1c0-1-1-2-1-3l2 3 1-1c1 1 1 2 2 3l1 4h-3z" class="U"></path><path d="M738 422c0-1-1-1-1-2-2-4-5-7-8-11 0-1-1-2-2-3s-1-3-1-3c-1-1-1 0-2-1v-2c-3-2-5-4-6-8l1-1 1 1c2 2 4 5 6 8 6 7 11 13 15 21 0 1-1 2-2 3l-1-2z" class="N"></path><defs><linearGradient id="g" x1="764.637" y1="484.823" x2="762.307" y2="464.183" xlink:href="#B"><stop offset="0" stop-color="#807e7e"></stop><stop offset="1" stop-color="#9e9e9e"></stop></linearGradient></defs><path fill="url(#g)" d="M760 457c4 9 7 19 9 28l1 4-3-1c-1 1-1 0-1 1l-1-2-1-4-2-7c-1-5-3-10-5-15v-1h0c1 2 2 3 4 4v-1-1c-1-2-1-3-1-5z"></path><path d="M764 483c1 1 2 3 4 3l1-1 1 4-3-1c-1 1-1 0-1 1l-1-2-1-4z" class="i"></path><path d="M341 605l4 3c0 2 1 2 2 4h6c-2 1-3 1-5 1 1 2 1 3 1 4-4 1-11 3-14 6h0-1c0-2 0-4 1-6 1-1 3 0 5-2h0c1-3 1-7 1-10z" class="R"></path><path d="M344 613h1l2-1v1c1 0 1 1 1 1l-2 1v1c-1 1-2 1-3 2h-3c1-1 2-2 3-2 0-1 1-2 1-3z" class="X"></path><path d="M341 605l4 3c0 2 1 2 2 4h-2l-1 1c0 1-1 2-1 3-1 0-2 1-3 2l-3 2h-1l1-2h1c2-1 2-2 2-3 1-3 1-7 1-10z"></path><path d="M312 578l-2-1c-3-2-5-6-7-10-1-3-3-6-5-9h0c1-2 3-3 5-3l-1 2c0 2 1 4 3 6 5 9 13 16 21 23v1c-3-1-6-5-8-7l-9-7c1 2 2 3 4 4l-1 1z" class="R"></path><path d="M327 607h6c1-1 3 0 4 0 1 1 1 1 1 3-3-2-4-2-7-1-2 1-5 3-6 6-1 2 0 5 1 7 1 3 3 5 6 6h-1-1l-1 1 1 1h0c-1 0-1 0-2-1h0c-2-1-4-3-5-5l-1-1-1-2v-1c-1-1-1-1-1-3-1-1-1-1 0-2h1l1-2c2-2 4-4 5-6zm-25-105c-3-1-8-3-10-6-2-4-1-10 0-14v4c1 7 2 10 9 12 2 0 3 1 4 2s2 1 3 2h1-1v1h0c2 1 4 2 5 4l-1 4h0-2c-3-1-6-7-8-9z" class="P"></path><path d="M302 502h3v1c2 2 4 1 5 3 1 0 2 3 2 5h0-2c-3-1-6-7-8-9z" class="X"></path><path d="M701 696l7-11 3 3-1 1c-2 0-3 3-4 5h0c0 1-1 3-2 3l-2 2c0 2 0 3 2 4 1 2 3 2 5 2l-1 2 2 1v1c-2 0-3 1-4 1-1-1 0-1-1-1-3-1-5-1-7-2h-1c0-2 0-1-1-3h2v-2-1l1-3 2-2z" class="F"></path><path d="M698 701c3 3 6 6 10 6l2 1v1c-2 0-3 1-4 1-1-1 0-1-1-1-3-1-5-1-7-2h-1c0-2 0-1-1-3h2v-2-1z" class="X"></path><path d="M376 271c-5-1-29 1-31-1 0-2 0-2 1-3h30v4z" class="T"></path><path d="M725 667c1-2 2-2 2-4 2-2 3-5 4-7h1c0 1 0 2 1 3l5 6h0c0 3-1 3-3 5-1 0-1 1-2 2l-1 1-1 1c-1-1-2-3-3-5h0l-1-1c-1 0-1-1-2-1z" class="N"></path><path d="M727 668v-1h2c1-1 3-2 4-2l1 1 4-1h0c0 3-1 3-3 5-1 0-1 1-2 2l-1 1-1 1c-1-1-2-3-3-5h0l-1-1z" class="G"></path><path d="M728 669c2 0 3 2 5 3l-1 1-1 1c-1-1-2-3-3-5h0z" class="c"></path><path d="M527 268c12 0 25-1 37 1-8 1-17 1-24 1l-72-1h10 20l29-1z" class="f"></path><path d="M514 163l1-4 1-1c1 1 2 3 3 4l1 3-3 4c-2 3-3 6-5 9 0 2-1 4 0 6v28c-1 3 0 10-1 12l-1-11v-10l-1-19v-3l1-2h1c0-4 2-7 4-10 1-2 2-4 2-7l-3 1z" class="B"></path><defs><linearGradient id="h" x1="595.14" y1="706.73" x2="600.483" y2="702.246" xlink:href="#B"><stop offset="0" stop-color="#9f9d99"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#h)" d="M581 688c10 3 17 7 21 16 3 5 3 14 1 19 0 1 0 2-1 2-1-3-1-5-1-9-1-12-6-21-19-25 0-1 0-2-1-3z"></path><path d="M612 665c3-2 4-5 6-8 0 2 0 3-1 4 0 1 0 1-1 2l1 4-1 2c-6 6-12 10-20 11-3 0-10 1-13-1l3-1c3 1 8 0 10 0 7-2 13-7 16-13z" class="B"></path><path d="M616 669l-1-1c-1-1 1-4 1-5l1 4-1 2z" class="T"></path><path d="M672 459c3-1 6 0 10-1 2 0 4-1 7-2l1 2 3-1c-1 1-1 1-1 2l-2 5-2 1c-2-1-2-2-3-2s-1 0-2-1h2c1 0 2-1 3-1l-1-1c-3 0-6 0-8 2-1 1-1 1-2 1-1-1 0-1-1-1-1-1-2-1-3-1v2c-2 1-4 2-5 2-2 1-4 1-5 1l-4 4-2-1 7-6h1c2 1 3 0 4-1h3v-1h-2c-2 1-2 1-3 0h0c1-2 3-2 5-2z" class="F"></path><path d="M672 459c3-1 6 0 10-1 2 0 4-1 7-2l1 2c-2 0-4 1-6 1l-1 1c-2 0-8 1-10 0l-1-1z" class="Y"></path><path d="M679 252l1-13h7l1 3v4c1 0 1 1 2 1h1 2v1 1l-1 1-5 1h-1c-1 0-1 0-2-1l-1 1v1c1 0 0 1 0 2-1 1-2 1-3 1l-1-3z" class="g"></path><path d="M762 441c1 2 8 16 7 18l-1 2c0 1 0 2 1 4 2 2 2 5 3 9 1 1 1 2 1 3s-1 3-2 4l-8-26-3-7h3v-1l-1-6h0z" class="S"></path><path d="M763 448l1 1c0 1 0 2 1 3 0 1 1 2 1 3l-1 1v1l-2-2-3-7h3z" class="T"></path><path d="M673 463c0 1-1 2-1 3h1l1 1c-6 2-14 4-17 10l-1 2-1 1-1-1c-1 1-1 1-1 2-1 1 0 1-1 1-2 1-3 3-4 5 1-4 2-7 3-10 0-2 1-3 2-4l4-4 2 1 4-4c1 0 3 0 5-1 1 0 3-1 5-2z" class="Y"></path><path d="M653 473l4-4 2 1-6 3z" class="Q"></path><path d="M672 466h1l1 1c-6 2-14 4-17 10l-1 2-1 1-1-1c-1 1-1 1-1 2-1 1 0 1-1 1-2 1-3 3-4 5 1-4 2-7 3-10h3 0c2-1 4-3 6-5 4-3 7-5 12-6z" class="a"></path><path d="M647 574c1-1 1-2 2-3 0-1 0-1 1-1l1 1h4l-7 8-2 6-3-1c0 2-1 3-2 3h-2l-1-1h-2c0-3 2-5 3-7l4-5 2-2h1l1 2z" class="b"></path><path d="M646 572l1 2h-1c-1 2 0 3-3 5v-5l2-2h1z" class="L"></path><path d="M643 584c1-1 1-1 1-2 0-2 0-2 1-3h3l-2 6-3-1z" class="a"></path><path d="M639 579l1 1 2-1v1s0 1-1 2l1 1v1c-1 0-2 0-3-1l-2 2 1 1h-2c0-3 2-5 3-7z" class="J"></path><path d="M252 489l7-24c1 1 2 1 3 2-3 5-3 11-4 16l-3 12c-1 2-2 5-2 8-2-1-2-1-3-2v-3c0-3 1-6 2-9z" class="N"></path><path d="M252 489l1 1 3-8c0 1 0 2-1 4s-1 5-1 8l1 1c-1 2-2 5-2 8-2-1-2-1-3-2v-3c0-3 1-6 2-9z" class="a"></path><path d="M333 557c5 0 8 0 13 2 6 3 11 5 16 9h0l2 2c2 3 5 5 7 8h0c-2 0-2-1-3 0l-7-7c-8-7-20-12-31-13h-1v-1h4z" class="C"></path><path d="M412 513v-1l-1-1c-1-2 0-2 0-3l2 1 2-1c2 2 2 3 2 6v1l1 1 1-1c1 0 2 2 3 2s0-1 1 0c1 0 0 0 1 1h-1l-1 3-1 1 2 2h2c-2 0-4 0-6 1-4 0-6 1-9 1 0-1 0-1-1-2 2-1 3-1 4-3-1-2 1-5 0-6-1 0-1 0-2-1l1-1z" class="B"></path><path d="M417 518h1l1 1h2v1c-1 1-5 1-7 2v-1c1-2 1-2 3-3z" class="e"></path><path d="M412 513v-1l-1-1c-1-2 0-2 0-3l2 1c1 2 0 1 0 3 0 1 2 5 4 6-2 1-2 1-3 3v1l-3 3h-1v1c0-1 0-1-1-2 2-1 3-1 4-3-1-2 1-5 0-6-1 0-1 0-2-1l1-1z" class="i"></path><path d="M710 689c1 4 9 6 5 12-2 2-3 3-6 4-2 0-4 0-5-2-2-1-2-2-2-4l2-2c1 0 2-2 2-3h0c1-2 2-5 4-5z" class="f"></path><path d="M706 694c2 1 4 3 6 4 0 2 1 2 0 3s-2 2-3 2c-2-1-3-2-4-3s-1-2-1-3c1 0 2-2 2-3z"></path><path d="M271 424l10-15c0 3-4 8-6 11l-12 25c-4 10-8 20-11 30l-3 10h0c-1-1-1-4-1-5 2-4 3-8 4-12l5-13c4-11 9-21 14-31zm76 101l1 1c-1 1 0 1-1 1-3 1-7 2-10 4h-1l-2 2-1 1h2l-2 4h-2c-1-1-2-1-3-2h-2v-1h-3l-1-1-1-1-1-1h1v-1l-2-1h0l1-1h4v-1-1h6 2v3c2 0 2-1 3-2 4-2 8 0 12-3z" class="B"></path><path d="M320 529c2 0 5 0 6 1h2c0 1 0 1-1 2 2 1 5 1 7 1l-1 1h2l-2 4h-2c-1-1-2-1-3-2h-2v-1h-3l-1-1-1-1-1-1h1v-1l-2-1h0l1-1z" class="i"></path><path d="M333 534h2l-2 4h-2c-1-1-2-1-3-2h-2v-1c2-1 5-1 7-1h0z" class="K"></path><path d="M333 534h2l-2 4h-2c-1-1-2-1-3-2 2 0 4 0 5-1v-1h0z" class="T"></path><path d="M660 720c1 1 2 1 4 1l-33 23c-2 0-2-1-3-2l32-22z" class="R"></path><path d="M410 798l2-1 17 14c3 2 6 5 9 7 5 4 8 9 12 13 2 2 4 3 5 5 2 2 3 4 5 6 1 1 2 3 3 4-2 0-3 0-4-1l-2-2v-1h-1c-2-4-6-9-10-12-5-7-12-13-18-19-3-2-5-4-8-6l-10-7z" class="M"></path><path d="M372 547l-1-1c1-2 2-3 3-5 5-1 9-6 13-9 2 1 3 1 4 1 0 1 0 1 1 2-1 0-2 1-3 1s-2 1-3 2v1l-1 1h0l-2 5v4h-1v2l-1-1-1-1c-1 0-2-1-3-1 0-1-1-1-1-1h-2l-1-1-1 1z" class="O"></path><path d="M382 549c-1 0-1-1-1-1 0-1 1-5 1-6l2-2h1l-2 5v4h-1z" class="S"></path><path d="M443 784h1c2 0 5 2 7 3 20 9 36 23 52 38l6 6h-2c0-1-1-1-1-1-4-3-7-7-10-10l-14-11c-12-10-26-17-39-25z" class="O"></path><path d="M648 619l1-1c2 1 3 1 5 1 6 6 16 10 25 10 3 0 6-1 9-2l-1 2h-1c-2 1-3 2-5 3h1l-1 1c-2 0-4 0-6-1-5-1-9-2-13-3-6-2-8-4-12-8l-2-2z" class="g"></path><path d="M605 557l-5-4c-2-2-3-4-3-8 0 0-1 0-1-1-1-3 0-4 1-7l2 2-1 2h1v2h2c0 2 1 3 2 5l1 1c1 1 1 3 2 3 2 1 5 1 7 1v1l-1 2h2l-1 1c2 2 4 3 6 5-5-1-10-2-14-5z" class="U"></path><path d="M599 543h2c0 2 1 3 2 5l-3 1c-1-1-1-2-1-4 0-1-1-1-2-2h2z" class="P"></path><path d="M603 548l1 1c1 1 1 3 2 3 2 1 5 1 7 1v1l-1 2h-1-2 0c-4-1-7-4-9-7l3-1z" class="Z"></path><path d="M449 206c2 1 3 1 3 3 1 2 2 4 5 5h1l1 1h3c-1-2-1-2-1-3 2 1 3 2 4 3 2 0 2 0 3 1h1l2 2 1-1h1v1c4 3 11 6 16 7 1 0 2 1 2 1-5 0-9 0-14-3-2 0-3-1-5-2s-4-2-5-3h-1-1c-2-1-1-1-2 0l2 3h0c-1 0-2-1-3-2l-1 1 1 1h-1c-2 0-3-1-4-3v-1-1l-1 1h-1v-1c-1-2-3-3-4-5v-1c-1 0-1-1-2-1v-3z" class="c"></path><path d="M451 396l1 1h0v-1l1-1c0 1 1 1 2 2 0 1 0 2 1 2 1 3 1 5 2 8l-1 1h1c0 1 1 1 1 3l-1 1s1 1 1 2c1 1 2 2 2 3 0 2-1 3 1 6l-1 1 1 1-1 1c-1 1-1 1-2 1l-1-1c-1-1-1 0-2 0-1 1-1 2-1 4 0 1 0 0-1 1h-1l1-5v-1c1-2 1-3 1-4 1-10-1-17-6-25h2z" class="b"></path><path d="M270 517h1l-1 4v10 4c-2 0-2 0-4 1v1 1l-2-2c0-1-1-1-1-2v-1c-1 0-1 0-2 1l-2-1-1 1v1h-1l1-2v-6l2 1c1 0 0 0 2 1h1 0 1c-1-1 0-1-1-1-3-3 0-4-1-6 0 0-1-1-1-2h6c1-1 2-1 3-3z" class="H"></path><path d="M267 529c1 0 2 1 3 2v4c-2 0-2 0-4 1 0-1-1-1-1-1 0-1-1-3 0-4h1v-1l1-1z" class="M"></path><path d="M266 523s1-1 2-1v-1l1 1 1-1v10c-1-1-2-2-3-2l-1 1c-1-3-1-4 0-7h0z" class="U"></path><path d="M266 523l1 1 1 4-1 1-1 1c-1-3-1-4 0-7z" class="G"></path><path d="M270 517h1l-1 4-1 1-1-1v1c-1 0-2 1-2 1-2 1-2 3-3 5-3-3 0-4-1-6 0 0-1-1-1-2h6c1-1 2-1 3-3z" class="h"></path><path d="M645 767l-1 1c-1 1-1 2-3 3 1 0 2-1 3-1l1-1h0c1-1 3-1 4-2v1c-4 2-8 5-12 8-5 3-10 4-13 9l1 2c-2 3-7 6-10 9-2 2-5 4-8 6-1 0-1 0-2 1-2 0-2 1-4 2 0-1 1-2 1-2 3-4 8-7 11-10l12-12 1-1c2-2 4-3 7-5l12-8z" class="U"></path><path d="M820 343v1h-1c-1 2 0 4 0 6l-1 2h0c1 1 1 2 2 3l-4 1v-3-1h0v2c-2 2-3 2-5 2-1 1-1 0-2 0h-1-1-2-1c-2-1-3-1-4-1l-1 1c-1 0-1 0-2-1 0-2 1-1 1-2-1-1-2-2-2-3h1l1 1h0l1 1h0c0-1 0-2-1-3h-4v1 1c0 1-1 1-2 3h1l1-1v-1h1 1l1 1c0 1 0 2-1 3-1-1-3-1-4-2-1 1-3 1-4 1h-12c4-1 8 0 12-1 0-1 0-1-1-1h-1l2-3v-1l1-2c2 1 4 0 6 1h4 1v1c0 2 1 3 0 5 1 1 2 0 3 0l1-1c1 1 1 1 3 0h0l2 2h3 0c2 0 2-1 3-1v-4-1-2l-1-1c1-1 0-1 1-2l1-1c1 1 2 0 3 0h1z" class="F"></path><path d="M788 350l3 1c1 0 1-1 2-1 0 1 0 1-1 2s-2 1-4 2c0-1 0-1-1-1h-1l2-3z" class="V"></path><path d="M429 622l1 3-1 1v3c-3 3-7 6-8 11 0 4 1 6 3 9h0v1c0 1 0 1 2 2 1 0 2 0 3 1-2 1-3 1-4 1l-7-6-2-5v-4c1-1 1-1 3-2 1-2 2-4 1-6 0-1 1-2 2-2 2-3 4-5 7-7z" class="B"></path><path d="M429 622l1 3-1 1c-3 2-7 7-9 11v1c0 1 0 1-1 2l-1-1 1-2c1-2 2-4 1-6 0-1 1-2 2-2 2-3 4-5 7-7z" class="T"></path><defs><linearGradient id="i" x1="641.983" y1="614.031" x2="636.933" y2="615.657" xlink:href="#B"><stop offset="0" stop-color="#a6a5a5"></stop><stop offset="1" stop-color="#d1d0cf"></stop></linearGradient></defs><path fill="url(#i)" d="M636 599c0 2 1 4 1 6l1 1c1 2 1 5 3 6 0 2 0 2 2 3h1v-2 1l2 1c0 1 1 3 2 4l2 2v1h-2l1 1-1 1c-2-1-2-1-3-1 1 3 3 4 4 6h-1l-3-1c-6-7-9-14-12-23l1-3h1c1-1 1-2 1-3z"></path><path d="M644 614l2 1c0 1 1 3 2 4l2 2v1h-2l1 1-1 1c-2-1-2-1-3-1-1-1-2-2-2-4-1-1-1-1-1-2v-1l2 1 1-1-1-2z" class="f"></path><path d="M643 619c2 0 3 1 5 3l1 1-1 1c-2-1-2-1-3-1-1-1-2-2-2-4z" class="m"></path><path d="M541 848l8-11c3-4 6-9 11-13-3 5-7 9-10 14-1 2-2 5-3 7l-6 8-13 23-1 1v1c-2-1-2-2-2-3l2-5c1 0 13-20 14-22z" class="g"></path><path d="M683 660h4 1c1-1 2-2 3-2l2-1 4-3 2-1s1 0 2-1l1-1c1-1 2-2 4-3v1l2-2h1c-3 4-5 8-8 10-5 4-10 7-16 9 0 5 1 12-2 16l-1 1v-1-2l1-7c1-4 1-9 0-13z" class="M"></path><defs><linearGradient id="j" x1="642.73" y1="713.285" x2="644.197" y2="684.111" xlink:href="#B"><stop offset="0" stop-color="#a3a2a2"></stop><stop offset="1" stop-color="#c5c4c3"></stop></linearGradient></defs><path fill="url(#j)" d="M640 687c1-2 1-3 3-3v1c0 4 0 8 1 12 2 1 2 0 3 0l5-1c-2 3-6 7-5 10 0 2 2 4 3 5v1l-2-1v1l2 1h2l1 1c-2 1-4 1-6 1-5-5-5-12-7-18 0-3-1-6-2-9 0 0 1-1 2-1z"></path><path d="M406 642c2 0 4 0 5 1v2-1l2 1-1 1v1l-2 3v1c-1 1-1 3-2 3 0 2 1 3 1 4h1l1-1 2 2h-5v1c0 1 1 2 2 2 0 2 0 2-1 4h0l-2 1c-1-3-3-6-5-10-1-3-1-7 0-10l1-2c1-1 2-2 2-3h1z" class="H"></path><path d="M407 649c2 0 1 1 3 2-1 1-1 3-2 3-1-1-2-3-2-5h1z" class="P"></path><path d="M402 657c-1-3-1-7 0-10l1-2 1 4c1 6 2 11 5 17l-2 1c-1-3-3-6-5-10z" class="B"></path><path d="M406 642c2 0 4 0 5 1v2-1l2 1-1 1v1l-2 3v1c-2-1-1-2-3-2h-1l-1 1-1-1-1-4c1-1 2-2 2-3h1z"></path><path d="M411 644l2 1-1 1v1l-2 3v1c-2-1-1-2-3-2 1-2 3-2 4-4v-1z" class="B"></path><path d="M411 275h60-1l-4 1h1 1c2 1 4 0 6 0l3 1h0c-6 0-63 1-65 0 0 0-1 0-1-1v-1z" class="l"></path><defs><linearGradient id="k" x1="496.316" y1="279.857" x2="495.248" y2="271.635" xlink:href="#B"><stop offset="0" stop-color="#b1afaf"></stop><stop offset="1" stop-color="#dfdfdf"></stop></linearGradient></defs><path fill="url(#k)" d="M471 275l44-1c3 0 10 0 12 1v2c-5 1-11 1-16 1l-34-1h0 0l-3-1c-2 0-4 1-6 0h-1-1l4-1h1z"></path><path d="M564 416l1 1c1 4 2 7 3 11 1 3 1 5 3 8v2h0c2 2 3 3 4 5 1 0 2 1 3 1 2 1 3 3 4 3l2 2 3 2c2 1 3 2 4 3v1c-1 0-1 0-1 1l-1-1c-3 0-5-2-7-4-1-1-2-1-3-2l-2-1c-1-1-1-1-3-1v2l-4-4c1-1 1-2 3-2l-5-8-5-9 1-1c0-2-1-2-2-3 1-2 2-3 2-4v-2z" class="L"></path><path d="M570 445c1-1 1-2 3-2 5 4 11 8 16 12-3 0-5-2-7-4-1-1-2-1-3-2l-2-1c-1-1-1-1-3-1v2l-4-4z" class="Z"></path><path d="M417 806l1-1h0c-1 0-1-1-1-1 1 0 1 1 3 1 3 2 5 4 8 6 6 6 13 12 18 19 4 3 8 8 10 12h-1l1 1c-1-1-1-1-2-1h-1l-36-36z" class="I"></path><path d="M673 718c5-5 11-11 19-13 1 1 1 1 1 3l-4 2 2 3-2 1c-2 1-4 2-5 3l3 2c0 1 1 2 1 3v3h-4v-3h-2-1-1-1-1l-1-2h0l-1-1h0v-1h-2-1z" class="M"></path><path d="M677 720c4-4 8-7 12-10l2 3-2 1c-2 1-4 2-5 3l3 2c0 1 1 2 1 3v3h-4v-3h-2-1-1-1-1l-1-2z" class="Q"></path><path d="M684 717l3 2c0 1 1 2 1 3v3h-4v-3h-2c0-1-1-1-1-1 1-2 1-3 3-4z" class="D"></path><defs><linearGradient id="l" x1="377.451" y1="266.935" x2="385.224" y2="285.994" xlink:href="#B"><stop offset="0" stop-color="#afafab"></stop><stop offset="1" stop-color="#d0ced2"></stop></linearGradient></defs><path fill="url(#l)" d="M411 276l-1 2h-50c-5 0-12 1-17-1 4-2 59-2 68-2v1z"></path><path d="M351 460c1 1 1 1 2 1 1 1 1 1 2 1s5 3 5 4c4 2 6 4 8 7 0 2 0 2 1 3 1 2 1 3 1 4 1 1 1 2 1 3-1-1-2-2-4-2 0 1-1 1-1 2l-1-3c-2-4-6-8-10-10v-2c-2-1-4-1-7-1l2-2 1-1v-2h1c-1-1 0-1-1-2z" class="J"></path><path d="M361 471h1v-2h2v4l-1 1h-1 0c0-1-1-2-2-2l1-1z" class="F"></path><path d="M365 480l1-3h1l1 2h0c1 1 1 1 2 1 1 1 1 2 1 3-1-1-2-2-4-2 0 1-1 1-1 2l-1-3z" class="d"></path><path d="M351 460c1 1 1 1 2 1 1 1 1 1 2 1s5 3 5 4 1 1 1 2v1c-2-1-3-2-5-3l-2-2-1 1c1 2 3 2 5 3l3 3-1 1c-2-1-3-2-5-4-2-1-4-1-7-1l2-2 1-1v-2h1c-1-1 0-1-1-2z" class="H"></path><defs><linearGradient id="m" x1="355.193" y1="690.903" x2="349.203" y2="693.901" xlink:href="#B"><stop offset="0" stop-color="#b1afaf"></stop><stop offset="1" stop-color="#d0cfcf"></stop></linearGradient></defs><path fill="url(#m)" d="M344 677c1 4 3 6 5 8l1 1c0 1 0 2 1 3h0v-2h1l1-1c3 9 6 17 14 23l-2 1c-12-8-21-20-24-33h2v1l1-1z"></path><path d="M506 185c1-1 2-1 3-1l1 19v10h-2v1l-1-1v-1-5l-1-1v-3h-1c0 1 1 2 0 3v3c-1-4-3-8-5-13-1-2-3-5-4-8l3 2c0 1 1 2 2 3 2 1 3 0 5-1 1-2 0-5 0-7z" class="F"></path><path d="M718 416c2 0 2 0 3 1v-1c2 1 4 1 6 1l1 1s1 0 2 1h0c2 3 4 8 7 10v3-1l-3 1v-1c-1 0-2 0-2-1l-1-1c-1-1-1-2-2-3v-1h-1l1 1c1 1 1 2 1 4-1 0-3 0-4-1s-2-1-3-1h-2l-1 1h0v-2c0-1 0-2-1-3v-2c0-2-1-4-1-6z" class="d"></path><path d="M718 416c2 0 2 0 3 1v1c1 1 1 3 3 4v1c-2 0-2-3-3-1h0c-1 2-1 3-1 5 0-1 0-2-1-3v-2c0-2-1-4-1-6z" class="a"></path><path d="M730 419c2 3 4 8 7 10v3-1c-1-1-2-1-3-2l-2-2c0-1 0-2-1-2-2-1 0-1-1-2 0-1-1-1-2-2l2-2z" class="e"></path><defs><linearGradient id="n" x1="733.312" y1="464.131" x2="736.188" y2="493.869" xlink:href="#B"><stop offset="0" stop-color="#7b7a7b"></stop><stop offset="1" stop-color="#a9a9a7"></stop></linearGradient></defs><path fill="url(#n)" d="M728 464h1 3l9 27 5 24v3c-1-1-1-2-1-3v-1c-1-1-1-2-1-4-1-2-2-8-4-9v1c-3-13-7-26-12-38z"></path><defs><linearGradient id="o" x1="495.73" y1="257.786" x2="493.275" y2="244.743" xlink:href="#B"><stop offset="0" stop-color="#c3c1c3"></stop><stop offset="1" stop-color="#e3e2e0"></stop></linearGradient></defs><path fill="url(#o)" d="M468 249h49l3 2 3 1c1 0 2 0 3-1h1l-2 3h-5c-2 0-6-1-7 0-2 1-3 1-4 1l-1-1 1-2c1 0 1 0 2-1-1 0-3 0-4 1h-7l-3-1h-12c-5-1-10-1-15-1l-2 1v-2z"></path><path d="M637 474c0 1 0 2-1 4h-1c-1 1-2 3-3 4l-4 5c-2 1-2 2-2 3l-1 3 1 1c0 1-1 2-2 4-2 3-4 6-5 10l3 4-1 1c-1 0-2-3-2-4h-1l-2 2v1h-2v-1l-1-1 2-5c1-3 2-5 3-8 1-1 1-2 2-3 0-1 0-1 1-2v-1c1-2 3-4 4-5 3-4 6-7 9-10l3-2z" class="J"></path><path d="M615 505c2-1 3-3 4-4s1-2 2-3c0 4-2 5-3 7s-2 4-3 5l-1 1-1-1 2-5z" class="m"></path><path d="M621 491l2 3c0 1-1 2-2 2-1 1 0 1 0 2-1 1-1 2-2 3s-2 3-4 4l3-8c1-1 1-2 2-3 0-1 0-1 1-2v-1z" class="G"></path><path d="M783 243h1c3 2 7 0 11 1 1 1 2 1 3 1 1 1 1 1 1 2 2-1 2-2 4-2h0c-1 1-1 2-2 3 1 0 2-1 2-1l1-1 1-1h1v1c-1 1-1 0-1 1l1 1h2l-3 3c-2 2-3 2-6 3h-1-1l-1-2-1 1h-2l-1-1c-1 1-2 0-4 0h0-1c-1-1-1-1-3-1l-1-1 1-1 1-1c1-1-1-2-1-3l-1-1v-1z" class="P"></path><path d="M797 254l1-2c2 0 4-1 5-2h1s1 0 1 1c-2 2-3 2-6 3h-1-1z" class="B"></path><path d="M783 243h1c3 2 7 0 11 1 1 1 2 1 3 1 1 1 1 1 1 2s-1 1-2 2c-1-1-3-2-4-2l-1 1c-2-1-2-1-3-2-2 0-3 0-5-1l-1-1v-1z" class="n"></path><path d="M386 413h-1v-1c0-2 0-6 2-8 0-2 1-3 3-4-1 2-1 4-1 6 3 7 7 12 13 15l2 1v1h-1v2c1 1 0 1 1 1h2c1 1 1 1 2 0 2 1 3 1 4 1l2 1c-1 1-2 1-3 1-4 1-11-3-15-5l-3-3c-2-1-4-3-6-5l-1-3z" class="R"></path><path d="M386 413h-1v-1c0-2 0-6 2-8 0-2 1-3 3-4-1 2-1 4-1 6-1 1-1 2-1 3l-1-1c-1 1-1 1-1 2 1 1 1 2 2 3v1l1 1 1-1c-1-2-2-2-2-4l1-1 1 4c3 4 6 7 9 10h0-2c-1 0 0 0-1 1l-3-3c-2-1-4-3-6-5l-1-3z" class="O"></path><path d="M785 269c1-1 2-2 4-2v5l-1 39h-1c-2 1-2 0-3-1l1-35v-4-2z" class="C"></path><path d="M785 269c1-1 2-2 4-2v5h-2v1l-2 2v-4-2z" class="E"></path><path d="M392 682c1-2 3-5 5-7l1 1v3c2 0 3-2 4-2 3-1 8 1 10 2-3 3-6 5-5 9v1c1 1 1 0 1 1 1 2 4 6 5 8h-1c-1 0-1 0-1-1-1 1-2 1-2 0-2 0-4-3-5-5-1-1-3-2-3-4 1-1 2-3 3-3l1-1h0-4c-1-1-2 0-3 0v-2l-2 1h-1v-1h-3z" class="b"></path><path d="M398 682c1 0 2 0 2-1l1-1h4 1l-2 2h1v2h-4c-1-1-2 0-3 0v-2z" class="e"></path><path d="M472 827c-6-3-11-8-16-11l-9-6-23-13c-3-1-6-2-8-4 8 1 15 5 22 9 7 3 14 7 20 12l14 10v1c1 1 2 2 4 3v1h0l-2-1c-1-1-1-1-2-1h0z" class="G"></path><defs><linearGradient id="p" x1="481.94" y1="862.166" x2="487.737" y2="859.761" xlink:href="#B"><stop offset="0" stop-color="#70716c"></stop><stop offset="1" stop-color="#a5a3a5"></stop></linearGradient></defs><path fill="url(#p)" d="M496 885c-2-4-4-8-6-11l-10-20-14-26 3 3c11 15 19 32 28 49 0 1 0 2 1 4v2c-1-1-1-1-1-2l-1 1z"></path><defs><linearGradient id="q" x1="583.626" y1="811.069" x2="574.374" y2="802.931" xlink:href="#B"><stop offset="0" stop-color="#989698"></stop><stop offset="1" stop-color="#b9b7b4"></stop></linearGradient></defs><path fill="url(#q)" d="M605 788l2 2h1v-1c0-1 1-3 2-4h1v1l-1 2c1-1 2-1 3-2h1c-1-1 0-1-1-1l-2-2 1-1c1 1 2 1 3 2 1 0 1-1 2-2 1 1 0 2 0 3l2-1v1c-1 1-1 1-3 1-3 1-8 5-11 7l-23 13c-6 4-13 9-19 12-1-1-1-1 0-3 9-7 20-13 30-18 5-2 9-3 13-6l-4-4 1-1v1c1 1 1 1 2 1z"></path><path d="M438 408l3 6h0l-1 1c0 1 0 3 1 4s1 2 1 3h0l3 3c1 1 1 2 2 2 2 1 2 1 3 2-1 1-1 2-2 2h-3c-1 0 0 0-1-1h-2c-2-1-4-2-5-4-1 1-1 1-2 1v-1l1-1v-1l-2-1h-1v-1-2h0c0-1 1-2 1-2 1-2 1-4 0-6v-2c1-1 2-2 4-2z" class="L"></path><path d="M437 418h2c0-1 0-2-1-3h0l-1 1v-1l1-1c0-1 0-1 1-1v2h1c0 1 0 3 1 4s1 2 1 3h0v1c0 1 1 2 1 3s0 1-1 2c-1-1-1-2-2-3l-2-4h0v-2l-1-1z" class="N"></path><path d="M438 408l3 6h0l-1 1h-1v-2c-1 0-1 0-1 1l-1 1v1l1-1h0c1 1 1 2 1 3h-2c0 1-3 4-3 5h-1v-1-2h0c0-1 1-2 1-2 1-2 1-4 0-6v-2c1-1 2-2 4-2z" class="U"></path><path d="M644 213l2 1c-1 1-1 2-2 2l2 2 2-2v2h5c1 1 2 2 4 2l2 1c0 1 0 1-1 2l-2 2c-2 2-4 4-7 3h0l1-1c1 0 2-1 4-2v-1l-1 1c-1 0-3 1-4 1l-2 1-5-1-1-1-2-1h-1l-2-2 3-6 3 2h0v-2l2-3z" class="i"></path><path d="M648 216v2h5c1 1 2 2 4 2h-4-4c-2 0-1-1-3-2l2-2z" class="J"></path><path d="M646 225c1 0 5-2 7-3h1c-2 2-5 2-6 4h1l-2 1-5-1-1-1c1 0 3-1 5 0z" class="a"></path><path d="M644 213l2 1c-1 1-1 2-2 2l2 2c2 1 1 2 3 2l-2 1c-2-1-1-2-3-1 1 1 1 2 2 3h2c-1 1-1 1-3 1l1 1c-2-1-4 0-5 0l-2-1h-1l-2-2 3-6 3 2h0v-2l2-3z" class="c"></path><path d="M639 216l3 2c-1 1-2 2-2 4h2v1c-2 0-2 0-3 1h-1l-2-2 3-6z" class="L"></path><path d="M335 534c1 1 2 1 3 2 1-1 2-1 3-1l2 1 1 1h1c1-1 3-1 4 0 1 0 1 1 2 0s3 0 5 0c1 0 2 0 3 1-1 1-2 2-3 2l1 2-1 1 1 2-6 1-1-1h-2v-2h0-1-5c-3 0-6-1-8-1l-1-1v-2c-1-1-1-1-2-1h2l2-4z" class="H"></path><path d="M348 543h8l1 2-6 1-1-1h-2v-2h0z" class="a"></path><path d="M334 542l1-2c1-1 3-1 5-1 1 0 2 1 3 1s2 1 3 2c-2 0-3 0-4 1-3 0-6-1-8-1z" class="D"></path><path d="M335 534c1 1 2 1 3 2 1-1 2-1 3-1l2 1 1 1h1c1-1 3-1 4 0 1 0 1 1 2 0s3 0 5 0c1 0 2 0 3 1-1 1-2 2-3 2h-3c-2-1-2-1-4-1h-1-8c-2 0-4 0-5 1l-1 2-1-1v-2c-1-1-1-1-2-1h2l2-4z" class="U"></path><path d="M335 534c1 1 2 1 3 2 1 0 1 0 2 1-1 0-3 0-4 1h-3l2-4z" class="S"></path><path d="M741 421c6 9 11 18 15 28 2 2 3 5 4 8 0 2 0 3 1 5v1 1c-2-1-3-2-4-4h0c-1-1-2-3-3-5-2-5-4-11-6-16-1-2-3-5-5-7l-4-8c1-1 2-2 2-3z" class="B"></path><path d="M383 407c2-4 3-8 4-12 1-2 1-4 3-5l1 1c-1 1-1 2-1 3-1 1-2 2-2 4h1c1-4 4-7 6-10h1c-1 3-2 4-4 6 2 0 2-2 3-3 2-1 3-2 5-3-2 3-6 6-8 9l-2 3c-2 1-3 2-3 4-2 2-2 6-2 8v1h1l1 3c-1 0-2 1-2 2l-1-1c-2 1-3 2-5 3l-2 4v-2-2l-4-1 4-9s1-1 1-2l1 3 2-1 2-3z" class="Q"></path><path d="M383 407l1 4c-1 1-2 3-3 4l-3 2c0-2 2-5 3-7l2-3z" class="G"></path><path d="M386 413l1 3c-1 0-2 1-2 2l-1-1c-2 1-3 2-5 3l-2 4v-2-2c0-1 1-2 1-3l3-2c2 0 3 0 5-2z" class="H"></path><path d="M378 408l1 3 2-1c-1 2-3 5-3 7 0 1-1 2-1 3l-4-1 4-9s1-1 1-2z" class="I"></path><path d="M335 597c0-1 1-1 2-1 4 2 7 11 13 9 1-1 2-1 3-2v2l-3 4c1 0 3 1 5 0 1 0 2-3 4-3h1l1-1c1 1 1 2 1 3h1 0c-1 2-3 6-5 8-3 0-6 0-9 1 0-1 0-2-1-4 2 0 3 0 5-1h-6c-1-2-2-2-2-4l-4-3h-1l-2-1v-2c0-1-1-3-1-3-2-1-2-1-2-2z" class="C"></path><path d="M353 612h1s1-1 2-1h1l3-3v1c0 1-1 1-2 2l-1 1c-1 0-2 0-2 1v1c1 1 1 1 3 2-3 0-6 0-9 1 0-1 0-2-1-4 2 0 3 0 5-1zm-31 38c2 2 5 4 7 5 3 0 6 1 8 3 0 1 0 2-1 3h-4c2 1 5 1 6 3v1h-3c2 5 3 12 4 18h0c-2-2-2-4-3-6 0-4-1-8-3-11-1-1-6-3-7-4-4-2-6-4-9-7 1-1 1 0 1-1l2-2h1l1-2z" class="T"></path><path d="M329 655c3 0 6 1 8 3 0 1 0 2-1 3h-4c-1 0-1 0-2-1-2-1-2-2-4-2l-2 1v-1-3c1 1 2 2 4 2l1-2z" class="f"></path><path d="M446 791h1c4 2 8 5 12 8 6 4 12 7 18 12h-1-2v3c0 1 2 2 4 3v1c-1 0-3-1-4-2-1 0-1-3-1-4h-1c0 2-1 4 1 6 2 0 3 3 6 3h0l1 1c0 1 1 1 2 1 1 1 1 1 2 1 0 1 0 2 1 3h0v-1c0-2 0-4 2-6h0c-1-3-5-3-7-5-1-1-1-1-1-2 2 1 3 1 5 3l5 3h0c1 2-1 9-2 11-2-1-4-4-6-5l-12-9v-3c1 1 0 1 1 2 0 1 0 0 1 1v-4l2-2-1-1c-1-1-3-2-5-3-1-1-2-2-3-2h-1c0-1-1-2-2-2h-1c1 1 1 3 1 4 2 1 1-1 3-2 1 1 2 1 2 3l-3 2h-1v-1h0l-1 1h-1c0-1 0-2-1-3h0l1-2-1-1h-1c-1-1-2-1-2-2h-1c-1 0-1 0-2 1l-1-1 1-2-1-1v-3c-3-1-4-2-6-4z" class="N"></path><path d="M656 445c3 0 6-1 8-2l2 2c-1 1-2 2-3 5h1v3c-3 2-4 4-7 5-4 2-7 5-10 7l-4 1c1-1 1-2 2-2 2-1 3-2 4-3h-3c2-2 4-3 4-5 1-1 2-2 3-4 0-1 1-2 2-3h1v-1c-2 0-3 1-4 2l-1 1h-1l2-2-1-1-3-1v-1l1-1c3 1 5 1 7 0z" class="C"></path><path d="M657 458c-2-1-3-1-3-3v-1c0-2 1-3 2-4 2-1 2-2 4-3v-1c1 0 2 0 3 1v2 1h1v3c-3 2-4 4-7 5z" class="n"></path><path d="M362 720l26 18 20 14c2 1 5 3 7 5-21-9-39-22-57-35 2-1 3-1 4-2zm219-139h1c0 3 0 6 1 9 1 2 0 3 0 5s1 5 1 7c2 0 3 0 4-1 2-2 2-2 2-4l3 2-4 4v1h1v1 1c-1 0-2 0-3 1l-1-2-1 1v1c0 1 1 2 2 1 2 0 5-2 7-2v1h0c-2 0-4 1-5 2l1 1c-3 2-4 4-6 7 2 1 3 2 4 3l-1 1c-2-1-4-3-4-5v-1l-1 1-2-1c-1-1-1 0-2-1l-1-1h0l1-2h-2c-1 1-2 1-4 1h0c2-1 5-3 7-5 1-1 2-3 2-4v-6-16z" class="X"></path><path d="M792 232c3-1 5 0 8 0h15 9c2 0 5 0 7 1h7 2l-1 1 2 2h1l-48 1v-3c1 0 2-1 3-2h-5z" class="B"></path><path d="M838 233h2l-1 1 2 2c-2 0-4 0-5-1 1 0 1-1 2-2z" class="E"></path><path d="M621 513l1-1c5 7 13 13 22 14 3 1 5 1 8 0 1-1 1-2 2-3v-1l1 1-1 3h1l3-3v1c0 1-1 2-2 3 1 1 1 1 2 1v1c-1 2 0 4-1 5h-1l-7-2c-4 0-8-1-12-3-7-3-13-8-16-16z" class="P"></path><path d="M649 532c1-1 0-1 2-2h3c1 1 2 2 2 4l-7-2z" class="g"></path><path d="M354 450c-2 1-10-7-12-8s-5-5-7-6c-2 0-3-1-4-2-2-1-3-2-3-3l1-1 2 2h1 1v-2c-1 0-1-1-1-1l1-1c2 1 6 3 7 5h0l4 2c1 0 1 1 2 1h0l9 6v1c0 1 0 1 1 2h-1c0 3 1 5 2 7v1c-1-2-1-2-3-3z" class="D"></path><path d="M340 433l4 2c1 0 1 1 2 1h0l9 6v1c0 1 0 1 1 2h-1c0 3 1 5 2 7v1c-1-2-1-2-3-3 0-1 0-1-1-3-4-3-8-6-11-11 0-1-1-1-1-1l-1-1v-1z" class="Q"></path><path d="M596 520l4 1h1c0 1 0 1-1 2l1 1c-6 0-10 0-14 4-2 3-3 5-5 8v1c-2 4-1 10 0 14-1 1-1 2-1 3 1 3 4 4 4 8-3-2-5-5-8-7l-2-1-2-1v-2l1-4v-1l1-1 1-2c1 0 2-1 3-1v-8h0c1-1 2-1 2-2 1-1 1-3 2-4 5-3 9-6 13-8z" class="S"></path><path d="M596 520l4 1h1c0 1 0 1-1 2 0 0 0 1-1 1s-2 0-3-1v-3z" class="M"></path><path d="M576 543c1 0 2-1 3-1v10l1 1h0l-1 1-3-1-1 1-2-1v-2l1-4v-1l1-1 1-2z" class="J"></path><path d="M574 546c1 0 2 0 3 1v1l1 1v1c-1 1-1 0-2 0-1-1-1-2-2-3v-1z" class="D"></path><path d="M703 243h15c4 0 7 0 11 1h0c-4 2-8 5-11 6l-1 1h1c2-1 6-4 8-4 2 2 4 1 7 2-3 1-7 2-9 3-2 2-6 2-8 2l-6-5c-2-1-3-1-5-2 0 0-1 0-2-1h-4c1-1 2-2 4-3z" class="W"></path><path d="M717 245h2c0 2-1 3-2 4l-1 1h-1c-1-2-1-2-1-3 1-1 2-1 3-2z" class="l"></path><path d="M699 246c1-1 2-2 4-3h0l6 3h1s1 0 1 1c1 1 1 1 1 2l-1 1-1-1h0c-2-1-3-1-5-2 0 0-1 0-2-1h-4z" class="g"></path><path d="M727 443l1-1 2 2 1 1c3-1 4 0 7 1 2 2 4 4 5 7h-1l1 3c-2 2-4 3-6 3-1 1-2 0-4 0h0c-1 0-2 1-2 1-1-1-1-2-2-3-1-3-3-6-5-9h3c1 0 1-1 1-1l-1-3v-1z"></path><path d="M731 445c3-1 4 0 7 1 2 2 4 4 5 7h-1v1l-2 2-1 1c-1 0-3 1-4 0l1-1c1-1 2-1 3-2h1v-1c-1-1-2-1-3-2s-2-1-3-2c-1-2-1-2-3-3v-1z" class="T"></path><path d="M727 443l1-1 2 2 1 1v1c2 1 2 1 3 3-2 0-2 1-3 2v2l2 2 1 1c-1 1-1 1-1 3h0c-1 0-2 1-2 1-1-1-1-2-2-3-1-3-3-6-5-9h3c1 0 1-1 1-1l-1-3v-1z" class="H"></path><path d="M587 580l1-1-2-2c-3-1-1-5-1-8-1 0-2-1-2-2-1-1-1-2-2-3v-1c3 3 5 6 6 10 1 1 2 1 4 1h0l-1-2 1-1c1 0 2 0 4-1v1l5 2h0c2 0 4 1 5 0v1c-1 0-1 0-2 1l-2 2v1h1c1 0 0 0 1 1 0 1-2 2-2 3-2 1-2 2-3 3h0-1-1c0 1-1 1-2 1l-1-2-1-5h-1l-1 4c-2-2-5-2-8-2 1-1 1-2 2-4l3 3z" class="c"></path><path d="M587 580h1 1l-1-2c1 0 2 0 2-1h1c1 1 1 1 1 2h-1l-1 4c-2-2-5-2-8-2 1-1 1-2 2-4l3 3z" class="X"></path><path d="M595 571l5 2h0c2 0 4 1 5 0v1c-1 0-1 0-2 1l-2 2v1h1c1 0 0 0 1 1 0 1-2 2-2 3-2 1-2 2-3 3h0-1-1c0 1-1 1-2 1l-1-2c1-1 2-1 3-3l-2-1v-1h1l1 1 1-1h0c0-3-2-4-3-6v-1l1-1z" class="a"></path><path d="M595 571l5 2h0v2h-3c0-2-1-2-2-3l-1 1v-1l1-1zM440 688h0v1l-1 1h2 1c1-1 2 0 3-1s1-2 3-2l-4 6h1c4-1 9 1 12 3 1 1 1 1 2 3h0 0c-6-4-11-5-17-5l-5 4 3 1c-2 2-5 6-8 7v-1c1-2 3-3 4-5h-2c-1 2-3 4-5 6h-1l-2-1v-1l-2-3c3-4 7-7 13-9-1-1-2-1-4-1 2-2 4-2 7-3z" class="d"></path><path d="M437 692c1 0 1 1 3 2h0c-4 4-9 6-12 11l-2-1-2-3c3-4 7-7 13-9z"></path><path d="M414 496c3 4 5 9 8 13l1-1c0-2-1-3-2-5v-1l1-1 2-1c1 0 1 1 2 1h1l3 2v1h-2v5 1c1 1 1 1 1 3l-3 1h-1l-1 1h2c0 1-1 2-2 3-1-1 0-1-1-1-1-1 0 0-1 0s-2-2-3-2l-1 1-1-1v-1c0-3 0-4-2-6l-2 1-2-1c1-2 2-3 1-5-1-1-1-1-1-2h0c2-1 2-4 3-5z" class="C"></path><path d="M415 508v-3l1-1 1 1v1c-1 1 0 3 1 4l2 4h-3 0c0-3 0-4-2-6z" class="W"></path><path d="M423 508h1v2h2 1c-1 1-1 3-1 4h-1l-1 1h2c0 1-1 2-2 3-1-1 0-1-1-1-1-1 0 0-1 0s-2-2-3-2l-1 1-1-1v-1h0 3l-2-4 2-1 1 2 1 1h1c-1-1-1-1-1-2v-1l1-1z" class="E"></path><path d="M422 501l2-1c1 0 1 1 2 1h1l3 2v1h-2v5 1c1 1 1 1 1 3l-3 1c0-1 0-3 1-4h-1-2v-2h-1c0-2-1-3-2-5v-1l1-1z" class="P"></path><path d="M424 508l2-2v4h-2v-2z" class="K"></path><path d="M422 501l2-1c1 0 1 1 2 1l1 1c0 1 0 1-1 1h-2c-1-1-1 0-2-1h-1l1-1z" class="Z"></path><path d="M635 634c12 14 26 19 43 21h3 1v3c0 3 0 6-1 9v3h-1c-4-3-9-3-14-4l2-1 1 1h1 1 1l3 1 1-1h4v-1c1-1 1-3 1-4-2-2-12-3-15-4l-5-1c-1 0-1 0-2-1l-2-1c-1 0-2-1-3-2-4-2-7-4-11-7 0-1-2-3-3-4l-6-7h1z" class="P"></path><defs><linearGradient id="r" x1="573.562" y1="838.867" x2="565.714" y2="833.994" xlink:href="#B"><stop offset="0" stop-color="#b9b7ba"></stop><stop offset="1" stop-color="#deded9"></stop></linearGradient></defs><path fill="url(#r)" d="M602 803s-1 1-1 2c2-1 2-2 4-2 1-1 1-1 2-1l-11 8c-6 4-11 9-16 15-2 2-5 4-6 7-6 6-11 13-16 20-2 2-3 5-5 8 0 0-1 1-2 1l-1-3c14-22 32-40 52-55z"></path><path d="M386 565c1 0 3 0 4 1s2 2 4 2c0 1 1 0 1 0 1 0 1-1 1-2h0 1c1 0 2 1 3 2h1c1-1 2-1 3-1l1 1c0 1 1 1 2 2h0v1l1 2c-1-1-3-1-4-2v2c-3-1-5-2-8-1v1c2 4 4 9 5 15 2 7 1 15-1 22-2 10-8 20-16 26 4-5 8-10 10-15 4-8 5-18 5-26 0-13-4-22-13-30z" class="P"></path><path d="M397 566c1 0 2 1 3 2h1c1-1 2-1 3-1l1 1c0 1 1 1 2 2h0v1l1 2c-1-1-3-1-4-2l-8-5h0 1z" class="S"></path><path d="M330 464l-2-3c0-1-1-2-2-3-1-2-2-2-3-5v-1c-1-3-1-6 0-9 1 3 1 7 3 9 4 5 15 7 21 8h0 4c1 1 0 1 1 2h-1v2l-1 1-4-1c-3 1-5 2-8 4h0l-1-1c-3 0-3 0-5-2l-2-2v1z" class="V"></path><path d="M347 460h4c1 1 0 1 1 2h-1v2l-1 1-4-1c1 0 1 0 2-1l-1-1v-2z" class="L"></path><path d="M330 463c1-1 1-2 2-3v1h5c0 2 0 2-1 3 0 1-1 1-2 1h-2l-2-2z"></path><path d="M448 653c2 1 3 3 4 4h0c0 2 1 3 1 5v1c0 3-2 7-4 10-3 2-8 4-12 4l1 1c1 0 1 0 1 1-1 1-2 1-4 1-2 1-8 1-11 0l-3-1c-7-1-13-5-16-11 1 0 1 0 3 1h0l-1-2 2-1h0c2 4 6 7 10 9 1 0 3 1 5 1h1c1-1 5 0 6 0s1-1 2-1l1 1 1-1c3 0 5 0 7-1h1c1-1 2-1 3-2 2-2 4-6 5-9 1-4-1-6-3-9v-1z" class="M"></path><path d="M424 680h3v-1c-1 0-2-1-3-1s-2 0-3-1h3 1 0c3 2 9 1 12 0l1 1c1 0 1 0 1 1-1 1-2 1-4 1-2 1-8 1-11 0z" class="c"></path><path d="M594 420l1 1v1c1 0 1 1 1 2h1c2 2 4 4 6 5l3 2 3 3 6 4-2 2h-3c-2-2-5-3-8-4 1-1 1-1 1-3-2-1-3 0-4 0s-2 0-3-1c-2 0-5-1-6-1h-5c-1 0-1-1-2-2l2-2 1-2c1-1 2-1 3-1v-2l2-1c1 0 2 0 3-1z" class="l"></path><path d="M585 427l1-2 1 1h1l1 1c-1 1-1 2-2 3 1 1 2 1 3 1h-5c-1 0-1-1-2-2l2-2zm10 0c0-1-1-2-2-3h4c2 2 4 4 6 5l-2 2c-2-1-5-2-6-4z" class="C"></path><path d="M594 420l1 1v1c1 0 1 1 1 2h1-4c1 1 2 2 2 3-1 0-2-1-3 0 0 1 1 1 0 2h-1v-4-1h-2v-2l2-1c1 0 2 0 3-1z" class="B"></path><path d="M643 584l3 1-2 5c-1 3-1 11-2 13-1 1-1 1-1 3-1 1-1 1 0 2v4c-2-1-2-4-3-6l-1-1c0-2-1-4-1-6 0 1 0 2-1 3h-1l-1 3c-2-6-2-12-1-18v-1h2c1 1 1 1 1 2l1-1 1 1 2-1h2c1 0 2-1 2-3z" class="R"></path><path d="M639 603c1-2 1-4 2-5 1 1 1 3 1 5-1 1-1 1-1 3-1 1-1 1 0 2v4c-2-1-2-4-3-6l1-3z" class="Z"></path><path d="M643 584l3 1-2 5c-1 0-2 0-2-1l-1-1-6 2v-2l1-1 1 1 2-1h2c1 0 2-1 2-3z" class="O"></path><path d="M639 594v9l-1 3-1-1c0-2-1-4-1-6v-1c1-2 2-3 3-4z" class="C"></path><path d="M632 587h2l1 1h0c-1 2-1 2 0 4 0 1 0 3 1 4v-1c1-1 1-2 2-3h1v2c-1 1-2 2-3 4v1c0 1 0 2-1 3h-1l-1 3c-2-6-2-12-1-18z" class="M"></path><path d="M758 246c7-4 17-3 25-3v1l1 1c0 1 2 2 1 3l-1 1-1 1 1 1c2 0 2 0 3 1h1 0l-6 1c-3 0-9 1-12 0l-2-2c-3-1-7-4-11-4l1-1z" class="l"></path><path d="M783 244l1 1c0 1 2 2 1 3l-1 1-1 1 1 1c2 0 2 0 3 1h1 0l-6 1c-3 0-9 1-12 0l-2-2 1-1v-2c1 0 1 0 3 1h0c2 0 3 0 4-1h1l2-1c1-1 1-1 2-1h0c1-1 1-2 2-2z" class="P"></path><path d="M783 244l1 1c0 1 2 2 1 3l-1 1-1 1 1 1c2 0 2 0 3 1h1 0l-6 1c1-1 0-1 0-1-1-1-1-2-2-1h-3l1-2 2-1 1-1h0v-1h0 0c1-1 1-2 2-2z" class="Z"></path><defs><linearGradient id="s" x1="643.764" y1="242.718" x2="651.092" y2="223.166" xlink:href="#B"><stop offset="0" stop-color="#9c9d9b"></stop><stop offset="1" stop-color="#d3d0d1"></stop></linearGradient></defs><path fill="url(#s)" d="M617 231l60 1c1 1 0 1 0 3-1 0-58 1-64 0h7l-1-1h-1v-1h2v-1h-1 1l-3-1z"></path><path d="M376 267h40c5-1 11-2 17-1l2 2v1c-5 2-11 1-17 1-14 1-28 1-42 1v-4z" class="C"></path><path d="M362 568c2 0 3 0 5 1 1 1 3 2 5 3h1v1c2 2 4 7 7 7 2 1 3 4 5 4l2 3-1 2c-2 0-2 0-3-1h-3c1 0 1 1 1 1 1 2 4 2 4 5-1-1-2-2-3-2h0c1 2 0 6 1 7 0 1 2 1 2 2v4h-2-3-1v-3c0-9-3-16-8-24-2-3-5-5-7-8l-2-2z" class="N"></path><path d="M380 605h0c0-7-1-13-1-19h1v2c1 0 1 1 1 1 1 2 4 2 4 5-1-1-2-2-3-2h0c1 2 0 6 1 7 0 1 2 1 2 2v4h-2-3z" class="E"></path><path d="M362 568c2 0 3 0 5 1 1 1 3 2 5 3h1v1c2 2 4 7 7 7 2 1 3 4 5 4l2 3-1 2c-2 0-2 0-3-1l-2-2c-3-2-4-5-6-7l-7-7c-1-1-2-2-4-2l-2-2z" class="J"></path><path d="M598 591h1l3-3c1-1 1-1 3-1l1 1c0 1-1 1-1 2l1 2-1 1v1 1c1 2 1 4 1 6v1c-5 4-11 6-16 8l-1-1c1-1 3-2 5-2h0v-1c-2 0-5 2-7 2-1 1-2 0-2-1v-1l1-1 1 2c1-1 2-1 3-1v-1-1h-1v-1l4-4 3-3 1-1s0-1-1-1-1 1-2 0v-1 1c2 0 3-2 4-3z" class="E"></path><path d="M597 595l4-4c1 3 1 7 0 10-2 3-7 4-11 4v-1h-1v-1l4-4 3-3 1-1z"></path><path d="M303 541h10l-5 1h1c1 0 2 0 4 1l1 1v2 1l-2 2h-19c-3 0-7 1-9 0v-3-3-1c1-1 3 0 5 0h7c3 0 5-1 7-1z" class="c"></path><path d="M313 543l1 1v2h-9c0-1 0-1 1-1l-1-2h8z" class="b"></path><path d="M284 546l3-1c1 1 2 1 3 2h1c2 1 6 0 9 0 1-1 0-2 1-3 1 1 2 1 4 2h9v1l-2 2h-19c-3 0-7 1-9 0v-3z" class="T"></path><path d="M299 597c1-1 2-1 3-2 0 14 1 24 6 37 2 4 4 9 8 12l6 6-1 2h-1l-2 2c0 1 0 0-1 1-8-8-13-21-16-31l1-1c0-2 0-4-1-5v-2l-2-19z" class="P"></path><path d="M507 252c1-1 3-1 4-1-1 1-1 1-2 1l-1 2 1 1c1 0 2 0 4-1 1-1 5 0 7 0h5l-4 4c1 1 2 1 2 2l-2 2c-2-1-4 0-6 0s-3 1-5 1l1-2h-3-2c-1 0-1 0-3-1l-3 4h0c-3 0-4-2-5-4l1-2-1-1 5-5h7z" class="E"></path><path d="M515 262l6-4c1 1 2 1 2 2l-2 2c-2-1-4 0-6 0z" class="V"></path><path d="M503 260c2-1 3-3 5-4 2 0 3 2 5 3h-1c0 1-1 1-1 2h-3-2c-1 0-1 0-3-1z" class="b"></path><path d="M507 252h1l-1 1c-2 0-3 1-4 3h-2c-1-1-2 0-3 0l-2 2-1-1 5-5h7z" class="U"></path><path d="M750 347h31 8l-1 2v1l-2 3h1c1 0 1 0 1 1-4 1-8 0-12 1h-2l-1-1s-1 0-1 1h-1c-2 0-4 0-6-1-2 0-2 1-2 1-1 0-2 0-2-1l-1 1-2-2-1 1-2-2c0 1 0 1-1 2l-2-2h0l-2-2 1-1c1-1 2-1 3 0h0l-1 1h0 3c1 0 1-1 4-1h3v1 1 1c1-1 0-1 1-1h0l1-1v-1-1h-5c-4 0-7 0-10-1h0z" class="I"></path><path d="M766 350h1c0 1 1 1 2 2l-1 2h-1c0-1-1-1-1-2-1-1 0-1 0-2zm-11 2c1-1 2-2 3-2h2 1c1 1 1 1 1 3l-1 1-1 1-2-2-1 1-2-2z" class="e"></path><path d="M781 347h8l-1 2v1l-2 3h1c1 0 1 0 1 1-4 1-8 0-12 1h-2l-1-1v-1c-1-1-2-1-3-2l1-1 4 1h1c-1 1-1 2-1 3h1c1-1 2-3 2-4l3-3z" class="N"></path><path d="M781 347h8l-1 2c-2 0-3 1-4 1l-2-1c-1 1-1 0-1 1h-3l3-3z" class="J"></path><path d="M385 576c6 11 7 20 3 32-1 3-2 6-4 8-2 6-7 12-11 15h-1c1-2 3-4 5-5 1-2 3-5 4-7l-1-1h0l3-9c1-2 1-3 2-4v-4c0-1-2-1-2-2-1-1 0-5-1-7h0c1 0 2 1 3 2 0-3-3-3-4-5 0 0 0-1-1-1h3c1 1 1 1 3 1l1-2-2-3c0-2-1-3-2-5h-1l1-1h0c1 0 2-1 2-2z" class="W"></path><path d="M383 588c1 1 1 1 3 1l1-2c0 3 0 6 1 9h-1l-2-2c0-3-3-3-4-5 0 0 0-1-1-1h3z" class="S"></path><path d="M385 605v-4c0-1-2-1-2-2-1-1 0-5-1-7h0c1 0 2 1 3 2l2 2h1c-1 2-1 4-1 7-1 1-1 3-1 5-1 2-2 6-2 7v1c-2 6-7 12-11 15h-1c1-2 3-4 5-5 1-2 3-5 4-7l-1-1h0l3-9c1-2 1-3 2-4z" class="h"></path><path d="M383 545c0 4 1 7 3 11v1c2 3 4 5 7 7l3 2h0c0 1 0 2-1 2 0 0-1 1-1 0-2 0-3-1-4-2s-3-1-4-1c-5-4-10-8-15-10-3-1-5-1-7-2-1-2-1-3-1-5 1 0 2 1 3 1s1-1 2-1l1 1s1 0 2 1h1l1-1-1-1v-1l1-1 1 1h2s1 0 1 1c1 0 2 1 3 1l1 1 1 1v-2h1v-4z" class="Z"></path><path d="M373 549l2-1c1 1 1 1 1 2l-1 2c1 1 3 2 5 3v1c-3-1-6-3-9-4-2-1-3-2-5-3 1 0 1-1 2-1l1 1s1 0 2 1h1l1-1z" class="k"></path><path d="M383 545c0 4 1 7 3 11v1c2 3 4 5 7 7h-1c-4 0-9-5-12-8v-1c-2-1-4-2-5-3l1-2c0-1 0-1-1-2l-2 1-1-1v-1l1-1 1 1h2s1 0 1 1c1 0 2 1 3 1l1 1 1 1v-2h1v-4z" class="U"></path><path d="M372 547l1-1 1 1h2s1 0 1 1c1 0 2 1 3 1l-1 1c0 1 1 1 1 1 2 1 3 2 4 4h0l-1 1-3-2v1c-2-1-4-2-5-3l1-2c0-1 0-1-1-2l-2 1-1-1v-1z" class="G"></path><defs><linearGradient id="t" x1="720.94" y1="544.953" x2="718.909" y2="550.997" xlink:href="#B"><stop offset="0" stop-color="#a8a4a6"></stop><stop offset="1" stop-color="#c5c6c3"></stop></linearGradient></defs><path fill="url(#t)" d="M703 541s1-1 2-1v1c1 1 1 1 2 1h7 13l10-1c0 3 0 6-1 8 0 1 0 1-1 1h-3l-11-1h-15l-1-1c0-1-1-1-1-2l2-3c-1-1-2-1-3-2z"></path><path d="M703 541s1-1 2-1v1c1 1 1 1 2 1h7l2 1h1 4v2l-1 1h0c-5 3-7-2-12-2-1 0-2 0-2-1-1-1-2-1-3-2z" class="N"></path><path d="M727 542l10-1c0 3 0 6-1 8h0l-2-1h-1c-2-1-2-1-3-2h-1v1c-2-1-6 0-9-1h0l1-1v-2h-4-1l-2-1h13z" class="X"></path><path d="M733 546l2-2c1 1 1 3 1 5l-2-1h-1c-2-1-2-1-3-2h3z" class="C"></path><path d="M729 546h-5l2-2c1-1 2-1 3-1 2 0 3 1 4 3h-3-1z" class="J"></path><path d="M590 583l1-4h1l1 5 1 2c1 0 2 0 2-1h1 1c0 2-1 3 0 5v1c-1 1-2 3-4 3v-1 1c1 1 1 0 2 0s1 1 1 1l-1 1-3 3-3-2c0 2 0 2-2 4-1 1-2 1-4 1 0-2-1-5-1-7s1-3 0-5c-1-3-1-6-1-9h0c3 0 6 0 8 2z"></path><path d="M590 583l1-4h1l1 5 1 2c1 0 2 0 2-1h1 1c0 2-1 3 0 5v1c-1 1-2 3-4 3v-1 1c1 1 1 0 2 0s1 1 1 1l-1 1-3 3-3-2 1-1c0-5 1-9-1-13z" class="W"></path><path d="M591 596h5l-3 3-3-2 1-1z" class="M"></path><path d="M594 586c1 0 2 0 2-1h1 1c0 2-1 3 0 5v1c-1 1-2 3-4 3v-1c1-2 0-3 0-6v-1z" class="c"></path><path d="M793 274l3-1 1 1 1 31v1c-1 4-1 11 0 14 0 2-1 4-1 5s0 2-1 4v2c0 1 0 2-1 3h-1c0-2 1-5 0-7l-1-1c1-5 1-12 1-17l-1-35z" class="l"></path><path d="M422 398h2 3c1 1 2 5 4 4l-2-2 1-2 1 1c2 1 2 1 3 3 1 1 1 2 2 2 0 2 1 3 2 4-2 0-3 1-4 2v2c1 2 1 4 0 6 0 0-1 1-1 2-2 0-3 1-5 1-1-1-1-2-2-4l-1 1c-1-4 0-8-1-12-1-1-2-1-3-2l1-1h0c-2 1-3 1-5 1h0c1-1 2-2 3-4l1 1 1-3z" class="m"></path><path d="M434 410c0-1-1-3-2-4h-1v-2h3 2c0 2 1 3 2 4-2 0-3 1-4 2z" class="G"></path><path d="M426 417c1-2 1-10 0-12-1-1-1-1-1-2l1 1 2 2c2 1 4 4 6 6 1 2 1 4 0 6 0 0-1 1-1 2-2 0-3 1-5 1-1-1-1-2-2-4z"></path><defs><linearGradient id="u" x1="370.429" y1="680.665" x2="383.176" y2="708.531" xlink:href="#B"><stop offset="0" stop-color="#797874"></stop><stop offset="1" stop-color="#a3a3a4"></stop></linearGradient></defs><path fill="url(#u)" d="M367 709c2 0 4 1 6 1 1 0 1 0 1-1 1-3 1-5 0-8-1-2-3-3-4-5h3c1 1 2 1 3 0 3-2 3-6 3-10-1-2-8-7-8-8 3 1 5 4 8 6v-1c2-4 4-7 6-10s3-6 5-9c-1 2-1 4-2 7-4 10-5 21-8 32 0 2 0 4-1 6 0 1-2 4-3 4-4 1-8-1-11-3h0l2-1z"></path><path d="M422 521h7v1c1 1 2 2 3 2h0c5 3 8 7 10 12l1 3c1 5 0 9-2 13 0 2 0 2 2 3 1 0 2 0 3 1l-5 4h-1-1c-2 1-3 2-5 3-4 4-6 6-11 7v-1h-1c1-1 1-1 0-2 5-2 9-6 12-10 4-5 7-10 6-17h0c0-3-1-5-2-7-3-5-7-7-13-9h-2l-2-2 1-1z" class="C"></path><path d="M441 552c0 2 0 2 2 3 1 0 2 0 3 1l-5 4h-1-1c-2 1-3 2-5 3 2-3 5-7 7-11z" class="G"></path><path d="M381 468c2 0 4 1 5 1 2 1 3 1 4 2h0c1 2 4 3 6 5 4 3 8 6 11 10 2 3 5 6 7 10-1 1-1 4-3 5h0l-2-2h-2v1 1c-2-2-3-3-3-5 1 0 1-1 2-2h-1c-1 0-2 1-3 1l-2-1c1-2 2-3 3-5l-8-7c-2-3-6-6-9-8-2-2-4-4-5-6z" class="K"></path><path d="M723 428c1 0 2 0 3 1s3 1 4 1c0-2 0-3-1-4l-1-1h1v1c1 1 1 2 2 3l1 1c0 1 1 1 2 1v1l3-1v1c2 1 4 2 4 4v3l2 2c-1 1-2 2-2 3h-1c-1 1-1 2-2 2-3-1-4-2-7-1l-1-1-2-2-1 1-1-1c-1-1-1-2-1-4h0c-1-1-2-2-2-3-2-2-2-4-3-6l1-1h2z" class="R"></path><path d="M734 432l3-1v1c2 1 4 2 4 4h-1-1c-1 0-2-1-3-2l1-1c-1-1-2-1-3-1z" class="G"></path><path d="M720 429l1-1h2c0 1 0 1 1 2 1 0 1 1 1 2 1 0 2 0 3-1 1 0 2 1 3 1 0 2 0 2 1 2-3 0-6 0-9 1-2-2-2-4-3-6z" class="X"></path><path d="M723 435c3-1 6-1 9-1l3 2c2 1 2 1 3 3l-1 1h-1c1 2 3 3 4 4-1 1-1 2-2 2-3-1-4-2-7-1l-1-1-2-2-1 1-1-1c-1-1-1-2-1-4h0c-1-1-2-2-2-3z" class="D"></path><path d="M723 435c3-1 6-1 9-1l3 2h0c-1 1 0 1 0 2h-1v2c-1 0-2 0-2-1-2 1-3 2-4 3-1-1-2-2-3-4h0c-1-1-2-2-2-3z" class="N"></path><path d="M614 498c1 0 3 0 4-1l-3 8-2 5 1 1v1c-1 2-1 4-1 5l-1 1-2-1v1 3h0-1l-1-1c-1-1-2-1-3-2v-1h-1c-2-1-3-1-5-2-1 0-2-1-3-1l2-2v-1l2-3 2-3 3-6c1 2 3 2 5 2l1-2h0l3-1z" class="M"></path><path d="M609 515l1-1v3 1 3h0-1l-1-1v-2c0-1-1-2-1-2l2-1z" class="E"></path><path d="M598 511l2-3c1 1 2 1 3 3-1 0-1 0 0 1v2 1h0c-2-1-3-1-4 0-1 0-2-1-3-1l2-2v-1z" class="B"></path><path d="M602 505l3-6c1 2 3 2 5 2-1 1-1 2-1 3l-1 1v1l-2 1c-1 0-1 1-2 2-1-1-2-2-2-4z" class="C"></path><path d="M608 505l-1 1-1-1v-2l1-1c1 0 1 1 2 2l-1 1z" class="W"></path><path d="M614 498c1 0 3 0 4-1l-3 8-2 5 1 1v1c-1 2-1 4-1 5l-1 1-2-1v-3l-1 1-2-2c1-2 1-4 1-7v-1l1-1c0-1 0-2 1-3l1-2h0l3-1z" class="X"></path><path d="M610 514h1c2 1 1 1 2 3l-1 1-2-1v-3z" class="S"></path><path d="M614 498c1 0 3 0 4-1l-3 8-2 5h0v2l-1-2-2 1h-1v-2h3l1-2-1-2c1-2 2-3 2-4h-2l1-1-1-1h-1 0l3-1z" class="E"></path><path d="M295 401c0 1 0 3 1 3-5 7-9 14-13 21-4 5-9 12-10 18-1 2-1 3-2 4l-1 2c-2 4-4 8-5 12-2-2-2-2-4-3l2-5c1-1 2-3 2-4 3-6 5-12 9-17 0-1 1-3 2-4 5-10 12-18 19-27z" class="G"></path><path d="M263 453c1-1 2-3 2-4 3-6 5-12 9-17l-1 3v2c0 2-1 3-2 5h0c-1 1-2 3-2 5v1s-1 1-1 2c-1 2-2 3-2 5l-1 1c-1-1 0-2-2-3z" class="O"></path><path d="M628 684h1c1 1 1 1 3 1 3 5 3 9 1 15-1 3-2 5-4 7-1 1-8 6-8 7 4 2 9 2 13 2h1 2c0 4-6 10-8 13-2-1-1-2-3-2-1 0-1 1-2 2-2 3-8 7-12 9 6-7 15-11 19-18-7-1-15-2-20-8h0l1-1c0 1 1 1 1 2 2 0 4 1 5 1v-1h1c2-1 6-4 8-6 2-4 4-10 3-15 0-2-1-3-2-4-2-1-3 0-4 0l-1-1c2-1 3-2 5-3z" class="M"></path><path d="M548 250h3l4 1c2 0 4 0 5 1v1h-1v1h0c-2 2-3 3-3 4l-2 2 1 1c-3 0-5-1-7-1-5-2-11-4-16-2-1-1-3-1-4-1-1 1-3 2-5 3 0-1-1-1-2-2l4-4 2-3 21-1z" class="C"></path><path d="M554 260c-2-1-4-1-6-3v-1c4-2 6-3 11-3v1h0c-2 2-3 3-3 4l-2 2z" class="Q"></path><path d="M548 250h3c-3 1-6 3-9 3-5 1-9 0-14 4-1 1-3 2-5 3 0-1-1-1-2-2l4-4 2-3 21-1z" class="b"></path><path d="M314 608c1-2 3-3 4-5 2-2 2-5 4-7 0 2 0 3-1 4 0 0-1 1-1 2h1c-2 5-2 7-1 11v2c-1 1-1 1 0 2 0 2 0 2 1 3v1l1 2 1 1c1 2 3 4 5 5h0c1 1 1 1 2 1h0l-1-1 1-1h1 1c1 0 2 1 2 1 3 2 6 2 10 1h5 1l-2 1h1 2l-7 1c-2 1-3 2-4 4v2c-4-1-8-2-11-3h-1 0c-1-2-2-2-4-3-4-3-7-8-8-13h-1c-1-4-1-7-1-11z" class="K"></path><path d="M344 630h5 1l-2 1h1 2l-7 1c-2 1-3 2-4 4v2c-4-1-8-2-11-3h-1 0c-1-2-2-2-4-3 1 0 3-1 4 0 2 1 4 4 6 1l-3-2 1-1 3 2c2-2 6-1 9-2z" class="C"></path><path d="M344 630h5 1l-2 1h1 2l-7 1c-4 0-6 1-9 0 2-2 6-1 9-2z" class="l"></path><path d="M366 483c0-1 1-1 1-2 2 0 3 1 4 2s1 2 1 3c-3 5-3 11-3 17l1 2c1 1 2 2 3 2h1v1l2 1c-2 1-5 2-6 2-3 0-6-2-9-2-2 0-8 5-11 6l-6 3-2-1c3-2 5-3 8-5s12-8 13-13h1 0l2-3c-1-1-1-2-1-3h1c1-2 1-2 1-4-1 1-2 3-4 4h0l-1-1 2-1v-2-1-1c0-1 1-3 2-4z" class="C"></path><path d="M366 483c0-1 1-1 1-2 2 0 3 1 4 2s1 2 1 3c-3 5-3 11-3 17l1 2c1 1 2 2 3 2h1v1h-2-2c-2-1-3-1-5-4l1-1c1 1 1 2 3 3 0-2-3-2-3-4 1-4 2-7 3-11v-9l-2 7c-1 1-2 3-4 4h0l-1-1 2-1v-2-1-1c0-1 1-3 2-4z" class="c"></path><path d="M662 733s3-5 5-6c1 0 1-1 2-2h1l-1 1c0 2 0 2-1 4-2 1-3 3-5 5h1l1 1 1 1 2 1c0 1 1 3 2 4l1 2c2-1 4-3 6-4 1 0 2 0 3-1h2c-7 8-17 14-26 20l2-3v-1c-2-1-3-1-4-3-1-1-2-2-2-3h0l-1-1c0-1 0-1 1-2h0l1 1v2 1h1c0 1 2 2 3 3 1-1 1 0 1-1h1 0v2h1 1v-2-1c1-1 2-1 4-2v-2l-2 1c-2-1-4-2-5-3v-3l-1-1c0-3 1-3 3-5h0c0-1 1-2 2-3z" class="F"></path><path d="M666 737l2 1c0 1 1 3 2 4l1 2h-1c-1 0-2-2-3-2-1-1-3 0-4 0 1-2 2-2 2-2 1-1 1-2 1-2v-1z" class="X"></path><path d="M662 733s3-5 5-6c1 0 1-1 2-2h1l-1 1c0 2 0 2-1 4-2 1-3 3-5 5h1l1 1 1 1v1h-2c-2 2-1 2-1 4l1 1h2v1c-1 1-1 2-2 2l-2-1c-2-2-2-6-2-9h0c0-1 1-2 2-3z" class="G"></path><path d="M650 621c4 4 6 6 12 8 4 1 8 2 13 3 2 1 4 1 6 1l1 1c-1 0-1 0-3 1h1c3 0 6-1 10 0-4 2-9 3-13 3-2 0-5 2-7 1s-5-1-8-1c-3-1-5-2-7-3v-1l-2-3c-1 0-3-2-4-2-1-2-3-3-4-6 1 0 1 0 3 1l1-1-1-1h2v-1z" class="O"></path><path d="M662 638c0-1 0-2 1-3l1-1v1 1h2c1 0 1 0 2 1 2 1 4 1 6 1 1-1 2 0 3 0-2 0-5 2-7 1s-5-1-8-1z" class="k"></path><path d="M650 621c4 4 6 6 12 8 4 1 8 2 13 3-2 1-2 1-3 1-1-1-1-1-3-1 1 1 2 1 2 2 1 0 2 0 2 1-1 1-2 1-3 1-2-1-3-2-3-4l-2 1v-1h-2l-1 1h0c-1 1-2 0-3-1-3 1-4 0-6-1-1 0-3-2-4-2-1-2-3-3-4-6 1 0 1 0 3 1l1-1-1-1h2v-1z" class="G"></path><path d="M645 623c1 0 1 0 3 1l1-1v1c1 0 1 1 2 1 1 3 4 3 6 6h3l2 2h0c-1 1-2 0-3-1-3 1-4 0-6-1-1 0-3-2-4-2-1-2-3-3-4-6z" class="N"></path><defs><linearGradient id="v" x1="308.432" y1="314.798" x2="299.408" y2="308.99" xlink:href="#B"><stop offset="0" stop-color="#92908f"></stop><stop offset="1" stop-color="#e9e7e6"></stop></linearGradient></defs><path fill="url(#v)" d="M285 333c8-21 25-36 43-48 4-2 7-5 11-7h1c1 1 0 1 0 2-2 0-5 3-7 4-9 6-17 13-25 21-10 10-17 23-22 35l-1-1c-1-2-1-4 0-6z"></path><path d="M346 559v-1h0c1-1 2-1 3 0h1v-1h4c5 1 13 4 17 7 5 3 12 7 14 12 0 1-1 2-2 2h0l-1 1h1c1 2 2 3 2 5-2 0-3-3-5-4-3 0-5-5-7-7v-1h-1c-2-1-4-2-5-3-2-1-3-1-5-1h0c-5-4-10-6-16-9z" class="e"></path><path d="M354 557c5 1 13 4 17 7v2h0c2 0 3 1 4 2 2 2 4 3 5 5v1c-2-2-4-4-7-6 0 0-1 0-1-1-2 0-3-1-4-2l-1 1 2 2c-1 0-1 0-2-1s-2-1-3-2-2-3-3-4-5-2-8-3l1-1z" class="I"></path><path d="M346 559v-1h0c1-1 2-1 3 0h1v-1h4l-1 1c3 1 7 2 8 3s2 3 3 4 2 1 3 2 1 1 2 1l4 4h-1c-2-1-4-2-5-3-2-1-3-1-5-1h0c-5-4-10-6-16-9z" class="H"></path><path d="M557 275h58 26c4 0 8 0 12 1l-2 2-1 1c-24-2-48-1-72-1h-14c-3 0-6 0-9-1-1-1-1-1 0-2h2z" class="C"></path><defs><linearGradient id="w" x1="706.127" y1="310.449" x2="716.537" y2="302.372" xlink:href="#B"><stop offset="0" stop-color="#9e9c9a"></stop><stop offset="1" stop-color="#dddcdc"></stop></linearGradient></defs><path fill="url(#w)" d="M736 337c-2-3-3-7-5-11-6-9-13-18-21-25-5-5-12-10-18-14-2-2-4-3-6-4s-4-1-5-3c1-1 1-1 2-1 4 1 8 4 12 6 15 9 29 23 38 38l6 12c-1 1-2 1-3 2z"></path><path d="M441 266h71 21c-1 0-2 1-4 1h0c-3 1-6 0-8 0 1 1 0 1 2 0 1 0 3 0 4 1l-29 1h-20-10l-25 1c-1-1-1-2-2-2h0v-2z" class="l"></path><defs><linearGradient id="x" x1="524.869" y1="891.126" x2="546.079" y2="893.368" xlink:href="#B"><stop offset="0" stop-color="#aaa6a7"></stop><stop offset="1" stop-color="#d4d7d2"></stop></linearGradient></defs><path fill="url(#x)" d="M550 858l1 3c0 1-1 2-1 3-3 8-9 16-12 24l-4 8c0 1-1 1-1 2-2 2-2 5-3 8-2 4-4 8-5 12-5 13-8 27-11 41-1-1-1 0-1-1 4-27 12-53 24-77 4-8 8-16 13-23z"></path><path d="M402 526c5 0 9 1 12 4 2 1 3 2 4 3 1 2 3 4 4 6l-3 3-1 2h0-1l-2-1v-1c-1 0-2-1-4-1-1-1-2-2-4-2-2 1-3 2-4 3l-1 2-1 1h-6l1-1 2-2c1-1 2-3 2-4l-2-2c1 0 2-1 2-2-1-1-3-1-3-3l4-2h4c-1 0-2 0-3-1v-2z" class="W"></path><path d="M416 536c1 2 2 3 3 6l-1 2h0-1l-2-1v-1c0-2 0-3-1-4h1l1 1v-1-2z" class="J"></path><path d="M410 531c3 1 4 3 6 5v2 1l-1-1h-1 0c-4-2-7-3-11-2l1-1h3 2l-1-1c1-1 1-2 2-2v-1z" class="Q"></path><path d="M405 529l5 2v1c-1 0-1 1-2 2l1 1h-2-3l-1 1-3 2-2-2c1 0 2-1 2-2-1-1-3-1-3-3l4-2h4z"></path><path d="M403 536c4-1 7 0 11 2h0c1 1 1 2 1 4-1 0-2-1-4-1-1-1-2-2-4-2-2 1-3 2-4 3l-1 2-1 1h-6l1-1 2-2c1-1 2-3 2-4l3-2z" class="E"></path><path d="M268 423c0 2 0 2-1 4s-3 5-4 7v1l-3 6c1-1 2-1 2-2l3-6h1c1-2 2-5 3-7 0-1 1-1 2-2-5 10-10 20-14 31l-5 13c-1 4-2 8-4 12 0 1 0 4 1 5h0l-1 9-2 8h0c-1 0-1 0-2-1v-4c0 3-2 14-3 15h-1c1-8 2-15 4-22 3-17 8-33 15-49 3-6 5-12 9-18z" class="J"></path><path d="M247 485c0-1 0-3 1-5 0 1 0 4 1 5h0l-1 9-2 8h0c-1 0-1 0-2-1v-4-1c1-1 1 0 1-1v-1c0-3 1-6 2-9z" class="R"></path><path d="M247 485c0-1 0-3 1-5 0 1 0 4 1 5h0l-1 9h-1c-1-2 0-7 0-9z" class="T"></path><path d="M613 640c2 0 4 1 5 2h0c0 5-1 10-3 14 0 3-3 7-3 9-3 6-9 11-16 13-2 0-7 1-10 0h-5v-2c2 0 5 1 8 0 1 0 3-1 4-1s1 0 2-1h2c2 0 5-3 6-4v-1c0-1 0-2 1-3s1-1 1-2l3-9c1-2 1-5 4-7h-1c-1-1-1-2-1-3l1-1 2-4z" class="j"></path><path d="M613 640c2 0 4 1 5 2-1 0-2 0-3 1 0 0-1 1-1 2-1 0-2-1-3-1l2-4z" class="D"></path><path d="M612 648l2 1c0 2-2 5-3 7-2 4-4 9-7 13l-1 1v-1c0-1 0-2 1-3s1-1 1-2l3-9c1-2 1-5 4-7zM285 529h1c2 0 0 0 2 1h4c5 0 10-1 15 0 1 0 1 1 3 2h4c2 1 3 2 4 2 2 0 2 0 3-1l1 1 1 1h3v1h2c1 1 2 1 3 2 1 0 1 0 2 1l-2 1c-2-1-4-1-6-1h-6-2-12c-1-1-2-1-3-1l-1-1c-2 0-3 0-5-1h-5l-1 2h0c-1-1-1-1-2-1h-3v-1l2-1h-1l-1-2v-4z" class="B"></path><path d="M291 536v-2h2c2-1 3-1 5-1l-2 2h1l-1 1h-5z" class="C"></path><path d="M321 533l1 1 1 1h3v1c-1 0-3 0-5 1h0-4v-1l1-2c2 0 2 0 3-1z" class="U"></path><path d="M321 537c2-1 4-1 5-1h2c1 1 2 1 3 2 1 0 1 0 2 1l-2 1c-2-1-4-1-6-1l1-1-5-1z" class="G"></path><path d="M299 532c3-1 8 0 11 1h2v1 1l-1 1h0 2l1 1v1c1 0 2 1 3 1h-12c-1-1-2-1-3-1l-1-1c-2 0-3 0-5-1l1-1h-1l2-2h0l1-1z" class="M"></path><path d="M299 532c3-1 8 0 11 1v1h0v1c-4 0-8 1-11-1h2v-1l-2-1z" class="b"></path><path d="M682 525l2 1 30 2h-12l-1 1h1c-1 2-1 2-3 3h0l1 2c-2 1-2-1-2 3 0 1 1 1 2 2h-9-11v-1l1-1c2 0 2-1 3-1v-1l-1-1c-3-1-7 0-10 0h-2c-3-1-5 0-8-2v-1c4-3 12 2 17-2h-1v-1l3-3z" class="M"></path><path d="M686 531c1-1 1-2 2-2h5v1c-2 1-3 1-5 2l-2-1z" class="W"></path><path d="M682 525l2 1c1 1 2 1 3 3-2 1-2 0-4 0h-2-1-1v-1l3-3z" class="C"></path><path d="M686 531l2 1v1c-1 1-1 1-3 1v1c2 0 4 1 6 1v-1l-1-1c1-1 1-1 2-1l1-1h6l1 2c-2 1-2-1-2 3 0 1 1 1 2 2h-9-11v-1l1-1c2 0 2-1 3-1v-1l-1-1c-3-1-7 0-10 0 1 0 2-2 3-2 3-1 7 1 10-1z" class="S"></path><path d="M718 650v1c0 1 0 2-1 3 1-1 1 0 1-1h3l-1 1c-4 7-9 17-15 23-4 4-8 9-11 13-3 3-5 6-7 9-3 3-7 6-10 9s-6 7-9 10c-2 0-3 0-5-1 5-4 9-8 13-12 11-12 22-23 31-37l11-18z" class="B"></path><path d="M705 677c0-4 2-6 4-9l6-10c1-2 3-3 5-4-4 7-9 17-15 23z" class="f"></path><path d="M597 510l1 1v1l-2 2c1 0 2 1 3 1 2 1 3 1 5 2h1v1c1 1 2 1 3 2l1 1h1l1 3h-10l-1-1c1-1 1-1 1-2h-1l-4-1c-4 2-8 5-13 8-1 1-1 3-2 4 0 1-1 1-2 2h0l-1-1-3 1h0l1-2-1-1c0-1 1-1 1-2l-1-2 6-8c2-2 4-2 7-3 1-1 1-2 2-3h0 3 1c0-1 1-1 2-2l1-1z" class="m"></path><path d="M600 521v-1l-1-1v-1c1 0 1 0 2-1 0 1 0 1-1 2 1 1 1 1 3 1h1 1c0 1 1 1 2 1h2 1l1 3h-10l-1-1c1-1 1-1 1-2h-1z" class="X"></path><path d="M590 513h0 3 1c0-1 1-1 2-2-1 2-1 3-3 4l-3 2 1 1c1 0 3-3 5-2 1 0 2 1 2 1l-1 2c-2 0-2 0-3 1-2 0-2-1-3 0s-3 2-4 3v-1h0c-1-1-2 0-3 0 0-1 1-2 3-3h1v-1c-1 0-2 0-3 1h-2l-1 1-1-1c2-2 4-2 7-3 1-1 1-2 2-3z" class="R"></path><path d="M581 519l1 1 1-1h2c1-1 2-1 3-1v1h-1c-2 1-3 2-3 3l-1 1-2 2 2 1v2c-1 1-1 3-2 4 0 1-1 1-2 2h0l-1-1-3 1h0l1-2-1-1c0-1 1-1 1-2l-1-2 6-8z" class="O"></path><path d="M581 525l2 1v2c-1 1-1 3-2 4 0 1-1 1-2 2h0l-1-1-3 1h0l1-2 2-2 1-3 2-2z" class="e"></path><path d="M581 525l2 1v2c-1 1-1 3-2 4 0 1-1 1-2 2 1-2 2-3 2-5l-2-2 2-2z" class="c"></path><path d="M604 536c1-2 4-4 7-5v4c1 1 4 0 6 1l1 1c-1 1-2 2-2 3l2 1c1 1 1 2 1 4 0 1-1 2-3 3h0l-1 2h0c1 0 2-1 3-1s2-1 3-1l1 2-3 3c-2 2-3 2-5 3h-2l1-2v-1c-2 0-5 0-7-1-1 0-1-2-2-3l-1-1c-1-2-2-3-2-5v-1c0-2 1-4 3-6z" class="I"></path><path d="M618 549c1 0 2-1 3-1l1 2-3 3c-2 2-3 2-5 3h-2l1-2v-1c-2 0-5 0-7-1-1 0-1-2-2-3 1 1 2 2 4 2l1 1c5 0 6-1 9-3z" class="W"></path><path d="M612 539h1c1 0 2 0 3 1l2 1c1 1 1 2 1 4 0 1-1 2-3 3h0-5v-4l-2-3h1c0-1 1-1 2-2z" class="j"></path><g class="H"><path d="M612 539h1c-1 2-1 4-2 5l-2-3h1c0-1 1-1 2-2z"></path><path d="M604 536c1-2 4-4 7-5v4c1 1 4 0 6 1l1 1c-1 1-2 2-2 3-1-1-2-1-3-1h-1c-1 1-2 1-2 2h-1c0 2 0 3-2 4-2-2-1-2-2-5v1h0-2v1l-1-1c1-1 1-3 2-4v-1z"></path></g><path d="M610 536h7l1 1c-1 1-2 2-2 3-1-1-2-1-3-1h-1l1-1c-1-1-2-1-3-2z" class="B"></path><path d="M605 540h1v-1c1-2 2-2 4-3 1 1 2 1 3 2l-1 1c-1 1-2 1-2 2h-1c0 2 0 3-2 4-2-2-1-2-2-5z" class="E"></path><path d="M349 582c6 2 10 5 13 11 2 5 3 10 1 14v1h0-1c0-1 0-2-1-3l-1 1h-1c-2 0-3 3-4 3-2 1-4 0-5 0l3-4v-2c1-3 1-5 0-8l-1-1c-3-3-9-4-14-4v-1c-1-1-3 0-5 0 1-1 1-2 2-2h2l1-1c2 0 5 0 7 1h2c2-2 2-2 2-5z" class="P"></path><path d="M355 593c-2-2-3-3-5-4l-3-1h0c2 0 5 1 7 2 3 3 6 9 5 13v3c-2 0-3 3-4 3-2 1-4 0-5 0l3-4v-2c1-3 1-5 0-8l2-2z"></path><path d="M355 593c1 3 1 5 0 8 0 2-1 3-2 4v-2c1-3 1-5 0-8l2-2z" class="K"></path><path d="M648 487c1-2 2-4 4-5 1 0 0 0 1-1 0-1 0-1 1-2l1 1-1 6c1 5 3 10 7 12 1 1 3 2 5 2-2 0-3 1-5 0 0 1 0 1 1 2-1-1-1 0-1-1-1-2-3-3-4-5l-1 1c1 1 1 2 2 3l3 3v1c1 0 1 0 2 1s0 0 2 1h0c1 1 2 1 3 2 2 2 16 9 19 10v1h-1-1c-8-2-15-5-23-8 0-1 0-2-1-3l-1-4h-3l-1-3c-1 1 0 1-1 2-2 0-5 1-6 0h-2l1-1c0-2 0-3-1-4l1-5c-1-2 0-4 0-6z" class="B"></path><path d="M648 487c1-2 2-4 4-5 1 0 0 0 1-1 0-1 0-1 1-2l1 1-1 6v-2h-1c-1 1-2 2-2 3s1 1 1 2c1 1 1 2 1 3 1 2 2 3 2 5-1 1-1 1-2 1 0 1-1 2-2 3h3 0l-1-1 1-1 1 1 1 1c-1 1 0 1-1 2-2 0-5 1-6 0h-2l1-1c0-2 0-3-1-4l1-5c-1-2 0-4 0-6z" class="O"></path><path d="M648 493v-2h3c0 1 0 1-1 2l1 1 1-1v1h0l-2 2c0 1 1 2 1 3l-2 4h-2l1-1c0-2 0-3-1-4l1-5z" class="h"></path><path d="M589 403l1-1c2 0 2-1 3-2l1 1v1c0 1 1 2 2 2s2 1 3 2h0-3l1 1c-1 5 0 9-1 13v4c0-1 0-2-1-2v-1l-1-1c-1 1-2 1-3 1l-2 1v2c-1 0-2 0-3 1l-1 2c0-2-1-4-2-6l-1 1c0 1-1 1-1 2l-1 2h0l1 2v1c-2 0-2-1-4-2-1-3 3-3 3-6h0v-2h-3l3-7 1 2h1l3-5c0-1 0-1 1-2l2-4h1z" class="O"></path><path d="M589 403l1-1c2 0 2-1 3-2l1 1v1c0 1 1 2 2 2s2 1 3 2h0-3l-1 1c-1-1-2-2-3-1 0 0-1 0-1 1l-1-1h0l-2 2h0l-1-1c1-1 2-2 2-3v-1z" class="N"></path><path d="M591 407c0-1 1-1 1-1 1-1 2 0 3 1l1-1 1 1c-1 5 0 9-1 13v4c0-1 0-2-1-2v-1l-1-1c-1 1-2 1-3 1h-2c-2-1-2-2-3-3 0-5 2-8 5-11z"></path><path d="M595 407l1-1 1 1c-1 5 0 9-1 13v4c0-1 0-2-1-2v-1l-1-1h0c1-2 1-2 1-3l-2-2c1 0 1 0 2-1v-7z" class="G"></path><defs><linearGradient id="y" x1="301.292" y1="309.406" x2="293.652" y2="304.147" xlink:href="#B"><stop offset="0" stop-color="#bbb9b9"></stop><stop offset="1" stop-color="#e6e4e2"></stop></linearGradient></defs><path fill="url(#y)" d="M317 281c2 0 3 0 5 1-13 11-25 22-34 37-3 5-5 10-7 16-1 3-3 6-4 9l-2-2c4-18 12-33 25-46 3-3 7-6 10-9v-1c3-1 5-3 7-5z"></path><path d="M665 563c13-7 27-7 41-7 4 0 8 0 11 1l2 1c-4 1-7 0-10 0h-14c-4 1-7 1-11 2-12 3-22 7-30 17-5 7-7 13-7 21l-1 3h-1c0 1-1 2-1 4l-3 3c-1-1-1-1 0-2 0-2 0-2 1-3 1-2 1-10 2-13l2-5 2-6 7-8 10-8z" class="K"></path><path d="M740 502v-1c2 1 3 7 4 9 0 2 0 3 1 4v1c0 1 0 2 1 3s1 1 2 3c0 2-1 3 0 5l3-3v8 4h-1l1 6h1c0 1 0 2 1 3 0 1 0 2 1 3l-1 1v1c1 1 0 5-1 7l-1-2h-1l-1-2h0v-5c-1-2 0-5 0-7-2 3 0 7-1 10l-1 3v4c-1 0-3-1-4-1 0-9 1-18 0-27 0-9-2-18-3-27z" class="Z"></path><path d="M746 518c1 1 1 1 2 3 0 2-1 3 0 5l3-3v8 4h-1l1 6h1c0 1 0 2 1 3 0 1 0 2 1 3l-1 1v1c1 1 0 5-1 7l-1-2h-1l-1-2h0v-5c-1-2 0-5 0-7-2 3 0 7-1 10l-1 3v-15-8c0-2 0-5-1-7v-5z" class="L"></path><path d="M751 541h1c0 1 0 2 1 3 0 1 0 2 1 3l-1 1v1c1 1 0 5-1 7l-1-2h-1c1-4 0-9 1-13z" class="B"></path><path d="M325 539c2 0 4 0 6 1l2-1v2l1 1c2 0 5 1 8 1h5 1 0v2h2l1 1c1 0 2 1 3 2 0 1-1 1-1 2h-4l-28-1h-9l2-2v-1-2l-1-1c-2-1-3-1-4-1h-1l5-1h6v-2h6z" class="f"></path><path d="M350 545l1 1c1 0 2 1 3 2 0 1-1 1-1 2h-4 0v-2h1l-2-1h-5l1-1 6-1z" class="B"></path><path d="M347 543h1 0v2h2l-6 1-1 1c-4 0-11 1-15 0v-2h-1c1-1 1-1 2-1h0 8c3 1 3 2 6 1 2 0 3-1 4-2z" class="S"></path><path d="M325 539c2 0 4 0 6 1l2-1v2c-3 1-5 0-8 2l1 1c0 2-1 2-2 2h-3c0-1 1-1 1-2v-2h-2l-1-1v-2h6z" class="H"></path><path d="M313 541h6l1 1h2v2c0 1-1 1-1 2h-1v1h2l-2 1h2l-1 1h-9l2-2v-1-2l-1-1c-2-1-3-1-4-1h-1l5-1z" class="h"></path><path d="M313 541h6l1 1-1 5h-2c-1-1 0 0 0-2-1 0-1-1-2-1h-1l-1-1c-2-1-3-1-4-1h-1l5-1z" class="B"></path><path d="M623 554c1 1 2 1 3 2v1h0l1 4-2 4v1c2 0 2-1 4-1v-1c1-1 2-1 2-1l1-1h1c0 1 0 3 1 4-5 5-7 9-10 16h-2l2-6 5-7-11 6c-2-1-5 0-6-1-3 2-4 3-5 7h-1-2 0c-1 1-2 1-2 2l-1-1c0-1 2-2 2-3-1-1 0-1-1-1h-1v-1l2-2c1-1 1-1 2-1v-1c-1 1-3 0-5 0h0l1-1v-3h6l5-1 2-1c4-1 7-4 9-7v-3-2-1z" class="T"></path><path d="M612 568l2-1 1 2c-1 1-1 0-1 2h-1c-1-1-1-2-1-3z" class="M"></path><path d="M609 573c1-1 1-1 2-1 3 0 5-1 7-2 3-1 5-4 8-3h0c-2 2-5 2-7 3-3 1-5 3-7 4-3 2-4 3-5 7h-1-2 0c-1 1-2 1-2 2l-1-1c0-1 2-2 2-3-1-1 0-1-1-1h-1v-1l2-2c1-1 1-1 2-1v-1h4z" class="J"></path><path d="M605 573h4c0 2-2 3-4 5h-1c1-2 1-3 1-4v-1z" class="F"></path><path d="M434 563c2-1 3-2 5-3h1 1l-4 5c-3 1-4 2-6 5-1 1-2 2-2 4h1c-1 1-1 1-3 1-1 0-1 3-1 4 1 0 2 1 3 1h2c1 1 1 1 1 2-2 2-2 6-2 8v2c-1 3-1 5-1 7-1 0-1-1-2-2v-2c-1-1-1-1-2-1s-2-2-2-2v-2l-3-3-4-6-2-1c1-1 2-2 2-3s-1-2-1-2v-1l-4-2h8c2-1 3-1 4-2 5-1 7-3 11-7z" class="m"></path><path d="M426 579c1 0 2 1 3 1l-2 6c-1-1-1-2-2-2v-2c0-1 0-2 1-3z" class="U"></path><path d="M416 581c0-1 1-1 2-2 1 0 1 0 1 1 1 1 1 2 1 2l-1 1c0 1 0 1 1 1h1v-1c1 0 2 0 3 2l-1 4h2v3l-2-2-3-3-4-6z" class="d"></path><path d="M429 580h2c1 1 1 1 1 2-2 2-2 6-2 8v2c-1 3-1 5-1 7-1 0-1-1-2-2v-2c-1-1-1-1-2-1s-2-2-2-2v-2l2 2c1-1 1 0 1-1 0-2 0-3 1-5l2-6z" class="C"></path><path d="M434 563c2-1 3-2 5-3h1 1l-4 5c-3 1-4 2-6 5l-2-1c0 1-1 2-2 2l-6 3h-1c2 2 4 0 5 2 0 1 0 1-1 1h-5v-1c-1-1-2-1-4-2h0l-4-2h8c2-1 3-1 4-2 5-1 7-3 11-7z" class="O"></path><path d="M528 257c1 0 3 0 4 1 5-2 11 0 16 2 2 0 4 1 7 1h6c-1 2-2 3-4 5h-24-21c-2-2-4-1-6-1l3-1-1-1h2c2 0 3-1 5-1s4-1 6 0l2-2c2-1 4-2 5-3z" class="F"></path><path d="M528 257c1 0 3 0 4 1v1c1 0 2 1 4 2l-4 1h-9-2l2-2c2-1 4-2 5-3z" class="M"></path><path d="M532 259c1 0 2 1 4 2l-4 1-3-1v-2h3z" class="P"></path><path d="M532 258c5-2 11 0 16 2-4 2-7 2-11 2l-1-1c-2-1-3-2-4-2v-1z" class="b"></path><path d="M783 318v6c1 1 3 2 3 3v5h1v-5 12h-18c-5 0-10 0-14-1-2-2-1-3-2-5h1v1c2 1 4 1 6 1s3-1 4-1h0l2-2 3-3h3v1c2-2 4-3 6-3v-1l2-5 3-3z" class="G"></path><path d="M783 318v6 8 2c-1 1-2 1-4 1h-11c-3-1-5 0-8 0 2 0 3-1 4-1h0l2-2 3-3h3v1c2-2 4-3 6-3v-1l2-5 3-3z" class="b"></path><path d="M779 332c2 1 2 0 4 0v2c-1 1-2 1-4 1l-3-1v-1c2 0 2-1 3-1z" class="H"></path><path d="M772 329v1c1 0 2 1 3 2h-4c-1 0-1 1-2 1-2 0-2 0-3-1l3-3h3z" class="X"></path><path d="M783 318v6 8c-2 0-2 1-4 0l-1-5v-1l2-5 3-3z"></path><path d="M435 472l4-1 2 1v2l-5 4c-4 3-5 5-7 9-1 3-1 7 0 10 1 1 2 1 3 2h-3c1 2 2 2 4 3v2c-1 0-2-1-3-1l-3-2h-1c-1 0-1-1-2-1 0-1-1-2-1-3h0c-1-2-1-1-1-2-1-2 0-4 0-5v-2c0-2 0-2-1-3h0c-1-1-3-1-4-1v-1c-1-1-2-2-2-3-1-2-2-3-4-4 2 1 3 1 5 1 3 0 6-1 9-2l10-3z" class="P"></path><path d="M435 472l4-1 2 1v2l-5 4h-3l3-3v-2l-1-1z" class="T"></path><path d="M417 483h0c1-1 3-1 3-2 1 0 1-1 1-1 1-1 4-1 6-1v1l-1 2c-2 0-4 0-5 2v1c-1-1-3-1-4-1v-1z" class="K"></path><path d="M433 478h3c-4 3-5 5-7 9-2 1-2 1-3 2v5l-1 1c-1-1 0-4 0-5 1-2 1-5 2-6 1-2 3-4 6-6z" class="f"></path><path d="M422 490l1-3c0-1 0-1 1-2v2c0 1 0 2 1 3 0 1-1 4 0 5l1-1v-5c1-1 1-1 3-2-1 3-1 7 0 10 1 1 2 1 3 2h-3c1 2 2 2 4 3v2c-1 0-2-1-3-1l-3-2h-1c-1 0-1-1-2-1 0-1-1-2-1-3h0c-1-2-1-1-1-2-1-2 0-4 0-5z" class="B"></path><path d="M427 501c-1-1-1-1-1-3h1l2 1c1 2 2 2 4 3v2c-1 0-2-1-3-1l-3-2z" class="K"></path><path d="M462 425c0 1 1 2 1 2-1 6-1 13-5 17-1 2-2 4-4 6-1 1-3 2-4 4v1l-2 2 1 1c-1 1-2 2-4 3l-1 1c-2 0-4 0-5-1l-3 2c-1 0-2-1-4-1 1 0 3-1 4-2l1-1c1 0 1-1 2-2 2-1 3-2 5-4 0-1 2-2 3-3v-2c1-2 3-4 5-6 0-1 0-1 1-2v-2h0c-1 2-2 3-3 4-2 0-2 1-4 2h-1l1-1h0c3-3 6-7 6-10l1-2h1c1-1 1 0 1-1 0-2 0-3 1-4 1 0 1-1 2 0l1 1c1 0 1 0 2-1l1-1z" class="P"></path><path d="M449 454c-1-2 0-2 0-3s-1-1-1-2c2-1 2-1 3-2h1 1l1 3c-1 1-3 2-4 4h-1z" class="d"></path><path d="M454 442l2 2c1 0 1 0 1-1l1 1c-1 2-2 4-4 6l-1-3h-1-1c2-2 2-3 3-5z" class="a"></path><path d="M454 442c2-2 1-1 1-3 1-1 2-3 2-4l1 2h1l1 1c-1 2-2 3-3 5 0 1 0 1-1 1l-2-2z" class="F"></path><path d="M449 454h1v1l-2 2 1 1c-1 1-2 2-4 3l-1 1c-2 0-4 0-5-1 4-2 7-4 10-7z" class="I"></path><path d="M462 425c0 1 1 2 1 2-1 6-1 13-5 17l-1-1c1-2 2-3 3-5l-1-1h-1l-1-2c1-1 1 0 1-1l-1-1c-1 0-1-2-1-3h0l-1 2c-1 1-2 0-3 1l1-2h1c1-1 1 0 1-1 0-2 0-3 1-4 1 0 1-1 2 0l1 1c1 0 1 0 2-1l1-1z" class="L"></path><path d="M444 523c4 5 7 10 9 15 0 1 1 3 1 4l-1 1h1c2-3 4-7 5-10l1 1c-1 2-1 3-2 5l-4 8h-2l-1 1h0c2-1 3 0 4-1h1v1 1h1c1 0 1 1 2 1h1c1 1 2 1 2 2h0c-4-1-7-2-11-1h-1l1 1c-2 0-4 1-6 1l-2 2c-2-1-2-1-2-3 2-4 3-8 2-13l-1-3c-2-5-5-9-10-12 2 0 3 0 5 1 0-1 0 0 1-1 0-1 0-1 1-1h5z" class="d"></path><path d="M442 536l3 1v1l2 2c0 1 1 1 1 2h-1 0c-2 0-2-1-4-3l-1-3z" class="c"></path><path d="M443 539c2 2 2 3 4 3-1 2-1 3-2 5 0 2-2 4-2 6h2l-2 2c-2-1-2-1-2-3 2-4 3-8 2-13z" class="N"></path><defs><linearGradient id="z" x1="448.93" y1="542.541" x2="445.641" y2="523.859" xlink:href="#B"><stop offset="0" stop-color="#5f5d5d"></stop><stop offset="1" stop-color="#8a8a89"></stop></linearGradient></defs><path fill="url(#z)" d="M444 523c4 5 7 10 9 15 0 1 1 3 1 4l-1 1c0 1 0 1-1 1-1-1-2-2-3-4h0c-1-2-2-3-2-5-4-2-6-4-8-8l-2-2c0-1 0 0 1-1 0-1 0-1 1-1h5z"></path><path d="M439 527c2 1 3 1 5 4l3 4c-4-2-6-4-8-8z" class="N"></path><path d="M283 438l1-1c0-1 1-1 2-2v1l-1 2 1 2c-1 1-2 1-2 3 3 1 7-1 9 2 0 1 0 1-1 2v1l-1 1-1 3c-1 2-1 4-4 6h0c-1 1-2 2-4 2h3 1l1 1c-1 0-2 1-3 1h-4v2l-2-1h-1v3c0 1 0 4 1 5 0 2 0 5-2 7l-3 2h-1l1-1-1-1c1 0 1-1 2-1h0c4-1 0-10 0-13 0-2 0-4 1-5 0-4 0-5 1-8 1-2 2-3 3-5-1-1-2-2-2-3l1-2h1l1 1c1-1 1-2 2-3l1-1z" class="I"></path><path d="M283 448l2-2 2-1h4v2c-1 1-2 2-3 2v-1l-1-1h0-1v2c-1 1-1 1-1 2s-1 1-1 1l-1-1v-3z" class="D"></path><path d="M283 438l1-1c0-1 1-1 2-2v1l-1 2 1 2c-1 1-2 1-2 3 3 1 7-1 9 2 0 1 0 1-1 2v1l-1 1v-2-2h-4l-2 1-2 2-2 1-3 3c0 2 0 5 1 6l-1 1h0c-2-2-2-5-2-8 1-2 2-3 3-5-1-1-2-2-2-3l1-2h1l1 1c1-1 1-2 2-3l1-1z" class="Q"></path><path d="M283 438l1-1c0-1 1-1 2-2v1l-1 2-6 8c-1-1-2-2-2-3l1-2h1l1 1c1-1 1-2 2-3l1-1z" class="C"></path><path d="M618 575l11-6-5 7-2 6c-2 5-2 10-2 15-1-2-1-3-3-5v-4l-2-1c-3 3-4 5-6 8h-1-3v-1-1l1-1-1-2c0-1 1-1 1-2l-1-1c-2 0-2 0-3 1l-3 3h-1v-1c-1-2 0-3 0-5h0c1-1 1-2 3-3l1 1c0-1 1-1 2-2h0 2 1c1-4 2-5 5-7 1 1 4 0 6 1z" class="j"></path><path d="M611 581c1 1 1 1 1 2-1 1-2 3-3 4 0 2-2 3-3 4 0-4 3-7 5-10z" class="Y"></path><path d="M606 592v1h3c0-1 0-1 1-2l1-1c0-1 1-2 2-3 0-2 0-2 1-4 2-1 3-3 5-5l1 1 1-1c1-1 2-2 3-2l-2 6c-2 5-2 10-2 15-1-2-1-3-3-5v-4l-2-1c-3 3-4 5-6 8h-1-3v-1-1l1-1h0z" class="I"></path><path d="M606 581h1c1-4 2-5 5-7 1 1 4 0 6 1l-7 6c-2 3-5 6-5 10v1h0l-1-2c0-1 1-1 1-2l-1-1c-2 0-2 0-3 1l-3 3h-1v-1c-1-2 0-3 0-5h0c1-1 1-2 3-3l1 1c0-1 1-1 2-2h0 2z" class="i"></path><path d="M601 582l1 1c0-1 1-1 2-2h0 2c-1 1-2 2-2 3l-6 6c-1-2 0-3 0-5h0c1-1 1-2 3-3z" class="V"></path><path d="M673 718h1 2v1h0l1 1h0l1 2h1 1 1 1 2v3h0c0 2 1 4 1 6v1c2 1 2 1 4 0h1l-3 3-5 4h-2c-1 1-2 1-3 1-2 1-4 3-6 4l-1-2c-1-1-2-3-2-4l-2-1-1-1-1-1h-1c2-2 3-4 5-5 1-2 1-2 1-4l1-1 2 1c-1-2-1-3-1-4l-1-1 3-3z" class="N"></path><path d="M673 718h1 2v1h0l1 1-1 1-5 1-1-1 3-3z" class="O"></path><path d="M672 726v1c2 0 4-2 5-4l1 1-1 2h1c-2 1-4 2-6 2-1 0-3 1-4 2 1-2 1-2 1-4l1-1 2 1z" class="f"></path><path d="M678 726h2 0l-1 1c-1 1-1 2-2 3v1c-1 0-1 0-2 1 0 1 0 0-1 1v-2-1h-2c-2 0-3 1-3 3-1 1-1 3-1 5l-2-1-1-1-1-1h-1c2-2 3-4 5-5 1-1 3-2 4-2 2 0 4-1 6-2z" class="M"></path><path d="M682 731v-2s0-1-1-2h0l1-2h2c0 2 1 4 1 6v1c-1 1-1 2-3 3-1 1-3 1-5 1h0l-1 1c-1 0-2-1-3-1l1-6v1 2c1-1 1 0 1-1 1-1 1-1 2-1v1c1-2 2-2 4-2l1 1z" class="S"></path><path d="M677 732c1-2 2-2 4-2l1 1c-1 2-1 2-3 3h-2v-2zm-9 6c0-2 0-4 1-5 0-2 1-3 3-3h2l-1 6c1 0 2 1 3 1l1-1h0c2 0 4 0 5-1 2-1 2-2 3-3 2 1 2 1 4 0h1l-3 3-5 4h-2c-1 1-2 1-3 1-2 1-4 3-6 4l-1-2c-1-1-2-3-2-4z" class="H"></path><path d="M670 742c1-1 2-4 3-5 1 1 2 2 4 3h0c-2 1-4 3-6 4l-1-2z" class="U"></path><path d="M697 707h1c2 1 4 1 7 2 1 0 0 0 1 1h1 2c1 1 1 2 1 3h0c-3 3-6 7-10 8l-1 1-3 1v1l1 1c0 1 0 2-1 3-1 0-2 1-2 1h-2c-1 1-1 1-1 2h0l-1 1h-1c-2 1-2 1-4 0v-1c0-2-1-4-1-6h0 4v-3c0-1-1-2-1-3l-3-2c1-1 3-2 5-3l2-1-2-3 4-2c1 0 3-1 4-1z" class="j"></path><path d="M695 714c1 0 1 1 2 1s1-1 2 0c0 1 0 1-1 2s-2 1-2 1c-1 0-1 0-1 1l2 2h3l-1 1c-2 0-4 0-6-1 0-1 1-1 1-2l-3-2h0c2-1 3-1 4-2v-1z" class="F"></path><path d="M700 721v-1-3-1c1-1 2-1 3-2 1 1 1 2 3 2v-1c1-1 2-1 4-2-3 3-6 7-10 8z" class="H"></path><path d="M691 713c2-1 4-2 7-1h0l-1 1c-1 0-1 1-2 1v1c-1 1-2 1-4 2h0l3 2c0 1-1 1-1 2 2 1 4 1 6 1l-3 1v1l1 1c0 1 0 2-1 3-1 0-2 1-2 1h-2c-1 1-1 1-1 2h0l-1 1h-1c-2 1-2 1-4 0v-1c0-2-1-4-1-6h0 4v-3c0-1-1-2-1-3l-3-2c1-1 3-2 5-3l2-1z" class="X"></path><path d="M689 714c1 1 1 2 1 3-1 2-2 2-3 2l-3-2c1-1 3-2 5-3z" class="B"></path><path d="M688 722h1 0c-1 1 0 2 0 3l1 2 1-1c1-1 1-2 3-3 0 0 1 1 2 1l1 1c0 1 0 2-1 3-1 0-2 1-2 1h-2c-1 1-1 1-1 2h0l-1 1h-1c-2 1-2 1-4 0v-1c0-2-1-4-1-6h0 4v-3z" class="F"></path><path d="M685 731l1-2c3 1 3 1 5 2l-1 1h-1c-2 1-2 1-4 0v-1z" class="D"></path><defs><linearGradient id="AA" x1="738.694" y1="607.784" x2="728.39" y2="604.39" xlink:href="#B"><stop offset="0" stop-color="#a9a7a7"></stop><stop offset="1" stop-color="#d5d4d3"></stop></linearGradient></defs><path fill="url(#AA)" d="M743 556c1 0 3 1 4 1 1 14-3 29-6 43-2 7-3 15-6 22-3 10-9 21-14 30v1h-3c0 1 0 0-1 1 1-1 1-2 1-3v-1c0-1 1-2 2-3l3-7 7-14c5-13 8-27 10-41 2-10 3-19 3-29z"></path><path d="M354 249l92 1v1h0l1 1c0 1 0 1-1 2v1h-1v-1c-1-1-1-1-2-1s-2 0-2-1c-2-1-5 0-7 0-1 1-3 2-3 4h-1v3c-2 1-7 0-10 0 1-2 1-4 1-6h-7 0-8-18l-23-1c-3 0-7 1-10 0-1 0-2 0-4-1 1-1 2-1 3-2z" class="l"></path><path d="M414 253c5-1 11 0 16-1h4c-1 1-3 2-3 4h-1v3c-2 1-7 0-10 0 1-2 1-4 1-6h-7 0z" class="B"></path><path d="M425 418l1-1c1 2 1 3 2 4 2 0 3-1 5-1h0v2 1h1l2 1v1l-1 1v1c1 2 2 5 3 7s2 3 3 5c-1 0-1 1-2 2v-3c-2 1-2 5-3 6l-1 1h-1c1-2 2-5 2-7-1 0-1 0-2-1h0l-1-1c-1 0-2 0-3-1l-1 1c-6-3-11 0-16 3-3 1-5 2-7 4s-4 4-7 4v-1c0-2 1-4 2-6l5-3-1-1c1 0 2 0 3-1 2-1 5-2 5-5 1-1 2-1 2-2h-1l-2-1c1-1 2-1 4-1 0 1 0 1 1 1s2-1 3-2h0c1-1 2 0 4 0l2-2c-1-1-1-3-1-5z" class="W"></path><path d="M422 427h2c-1 1-1 2-2 3h-3v1c-1 1-1 1-2 1l-1 1c-1 1-3 1-5 1l11-7z" class="C"></path><path d="M430 435l-2-2c1-1 2-1 3-1 1-1 3-1 4 0s2 2 2 3c-1 1-2 1-4 1-1 0-2 0-3-1z"></path><path d="M412 427c1-1 2-1 4-1 0 1 0 1 1 1s2-1 3-2h0c1-1 2 0 4 0l-2 2-11 7-5 3-1-1c1 0 2 0 3-1 2-1 5-2 5-5 1-1 2-1 2-2h-1l-2-1z" class="b"></path><path d="M425 418l1-1c1 2 1 3 2 4 2 0 3-1 5-1h0v2 1h1l2 1v1l-1 1c-1-1-2-1-3-2v-1c-1 0-1 0-2 1s-2 2-4 2l-2 1h-2l2-2 2-2c-1-1-1-3-1-5z" class="B"></path><path d="M582 616l1-1v1c0 2 2 4 4 5l1-1c4 3 8 6 11 10 1 1 5 6 6 6 2 1 6 0 9 0-1 1-1 3-1 4h0l-2 4-1 1c0 1 0 2 1 3h1c-3 2-3 5-4 7l-3 9c0 1 0 1-1 2s-1 2-1 3v1c-1 1-4 4-6 4h-2c-1 1-1 1-2 1s-3 1-4 1v-1c2-1 4-1 5-2 4-2 5-6 8-8-1-1-3 2-4 3h-2-1c1-1 1-2 2-3 4-2 5-4 7-8 1-1 2-4 2-5-1-2-1-1-1-3v-1c-1 1-1 2-1 3-2 2-3 3-5 4-1 1 0 1-1 1h3 0l-4 3-1-1-1 2-1-5v-2h0l3-2c0-1 3-4 4-5h0c0-4 0-6-2-10-3-6-7-11-13-14l-2-1c-2-2-2-3-2-5z" class="a"></path><path d="M601 646v2c1 0 1 0 2-1 1 0 2-1 3-1l-3 3c-1 2-3 5-5 5-1 1-1 0-1 2h0-1l-2-1v-2h0l3-2c0-1 3-4 4-5z"></path><path d="M587 621l1-1c4 3 8 6 11 10 1 1 5 6 6 6 2 1 6 0 9 0-1 1-1 3-1 4h-4c-1 1-1 1-2 1h-1v2c-2-2-1-2-1-3l-1-1c-1 2-1 2-1 4v1h0c-1-1-1-3-1-5h0c-2-7-9-14-15-18z" class="P"></path><path d="M262 494c0-1 0-3 1-5 0 1 0 1 1 2 1 2 0 1 2 3l1 1c0-1 1-2 2-2v-2-1c1 0 2 2 3 2l2 1v2h-2 0l2 1c0 3-2 7-2 10 0 4 0 8-1 11h-1c-1 2-2 2-3 3h-6c-1 0-2 0-2-1h-1-1c0-1 0-1-1-2h-1v-1c0-1-1-2-1-3 1-1 1-3 2-4v-4c1-1 1-2 1-4h0l1-1c1-1 2-3 2-4h-1v-1h0l3-1z" class="h"></path><path d="M267 499l4 1 1 1h0c-2 1-4 2-6 2h0v-1l1-3z" class="L"></path><path d="M256 509v-4c1-1 1-2 1-4h0l1-1c1-1 2-3 2-4h-1v-1h0l3-1v1c0 2 0 3-1 5h1c1-1 1-2 2-3v-1l1 1s1 1 1 2h1l-1 3v1h0l-1 1c-3 0-3 0-5 2v1c-1-1-1 0-2-1 0 1-1 1-1 2l-1 1z" class="J"></path><path d="M266 499h1l-1 3v1h-1c-1-1-2-1-1-3l2-1z"></path><path d="M256 509l1-1c0-1 1-1 1-2 1 1 1 0 2 1v-1c2-2 2-2 5-2h-1c0 1 0 1-1 2h-1v1h4 1 2c1-1 2-1 3-1 0 4 0 8-1 11h-1c-1 2-2 2-3 3h-6c-1 0-2 0-2-1h-1-1c0-1 0-1-1-2h-1v-1c0-1-1-2-1-3 1-1 1-3 2-4z" class="f"></path><path d="M256 509l1-1c0-1 1-1 1-2 1 1 1 0 2 1v-1c2-2 2-2 5-2h-1c0 1 0 1-1 2h-1v1h4v2c-3 0-4-1-6 0l-1 1c1 2 5 3 5 3l-1 1h-1c-1-1-3-2-4-2h-1 0c0 1 0 0-1 1v2l-1 1c0-1-1-2-1-3 1-1 1-3 2-4z" class="a"></path><path d="M255 516l1-1v-2c1-1 1 0 1-1h0 1c1 0 3 1 4 2h1l1-1c2 0 3 1 4 0 1 2 2 2 2 3v1c-1 2-2 2-3 3h-6c-1 0-2 0-2-1h-1-1c0-1 0-1-1-2h-1v-1z" class="D"></path><path d="M263 514l1-1c2 0 3 1 4 0 1 2 2 2 2 3v1c-1 2-2 2-3 3-1-2-2-3-3-4h-1c1-1 1-1 1-2h-1z" class="V"></path><path d="M691 539h9 1v2h0c-1 0-2 0-3 1 2 0 3 0 5-1 1 1 2 1 3 2l-2 3c0 1 1 1 1 2l1 1c-8-1-17-1-26 0-6 0-11 0-17 2l-5 1-1-2v-3h0 1 4v-2l1-1 1-2c0-1 1-1 2-1l1-1h-2-1l5-1c3 1 8 0 11 0h11z" class="B"></path><path d="M677 544c1 0 4 0 5-1 1 0 0-1 2-1-1 1-1 2-1 3h-1c-1 0-1 0-2 1h-6c1-1 1-2 3-2z" class="k"></path><path d="M662 545h1v2c2 1 3 1 5 1h1c2-1 5-1 8-1v1c-4 0-8 0-12 1h-2v2l-5 1-1-2v-3h0 1 4v-2z" class="D"></path><path d="M666 541h3c1 1 4 0 5 1l-1 1h0l4 1c-2 0-2 1-3 2l-5-2-2 1-1-1c-2 0-2 0-3 1h-1l1-1 1-2c0-1 1-1 2-1z" class="b"></path><path d="M691 539h9 1v2h0c-1 0-2 0-3 1v2c-1 0-2 0-3 1h0c-1 0-1 0-2-1h1l1-1h-1c-1-1-2-1-3-1h0v-3z"></path><path d="M680 539h11v3h-7c-2 0-1 1-2 1-1 1-4 1-5 1l-4-1h0l1-1c-1-1-4 0-5-1h-3l1-1h-2-1l5-1c3 1 8 0 11 0z" class="H"></path><path d="M588 251c1 0 2-1 3-1 2-2 17-1 21-1 20 0 41-1 62 1v1 1c-2 0-4 0-7 1 1 1 2 2 2 3-1 1-1 2-2 4h-1c-1 0-1 0-2 1l-1-1-1 1-5-2h-1l1-2-3-3-1-1h-15-1-9l-27-1v1c0 2-1 3-2 5h-1v-1c-2 1-2 1-3 1l-1-1c2-1 3-3 4-4l1-1h0c-3 0-3 2-5 4h-3l1-1c1 0 2-1 3-2l-1-1-1 1h-2v-1c-1 1-2 1-3 1v-2z" class="Z"></path><path d="M653 253h13 1c1 1 2 2 2 3-1 1-1 2-2 4h-1c-1 0-1 0-2 1l-1-1-1 1-5-2h-1l1-2-3-3-1-1z" class="F"></path><path d="M653 253h13l-4 3c-1-1-1-1-1-2h-1l-1 1c0 1 1 2 1 3 2 1 2 0 3 2l-1 1-5-2h-1l1-2-3-3-1-1z" class="Y"></path><path d="M418 533l2-1 3 3c1 0 2-2 3-3 2 1 2 1 3 2 0 2 1 3 0 4l-2 8-4 4c0 1-1 1-2 2-2 2-5 3-7 5l-1 1-2 1c-2 0-3 0-5-1l-1-1-3-3c0-1-1-2-1-2l-2-2h-1c2-2 4-1 6-1l-1-2c0-1-1-1-1-3l1-2c1-1 2-2 4-3 2 0 3 1 4 2 2 0 3 1 4 1v1l2 1h1 0l1-2 3-3c-1-2-3-4-4-6z" class="N"></path><path d="M422 539c0 3 0 5-1 8l-3-3 1-2 3-3z" class="C"></path><path d="M399 550c2 0 3 0 4 1v1h0c2 2 4 3 6 4-1 1-2 1-3 2l-1-1-3-3c0-1-1-2-1-2l-2-2z" class="B"></path><path d="M426 532c2 1 2 1 3 2 0 2 1 3 0 4v-1h-2l-1 1c-1 0-2 0-2-1-1 0-1-1-1-2 1 0 2-2 3-3z" class="J"></path><path d="M409 556c2 0 5-1 8-2h0c2-2 4-4 5-7 1-1 1-2 1-3 1-1 1-1 1-2h1c-1 4-2 6-4 10h0c-2 2-5 3-7 5l-1 1-2 1c-2 0-3 0-5-1 1-1 2-1 3-2z" class="T"></path><path d="M402 544l1-2c1-1 2-2 4-3 2 0 3 1 4 2 2 0 3 1 4 1v1l2 1h1 0l3 3c-2 4-4 5-8 7-2 1-4 1-6 0-1 0-3-1-4-2v-1c-1-1-2-1-4-1h-1c2-2 4-1 6-1l-1-2c0-1-1-1-1-3z" class="E"></path><path d="M409 551h1c2 0 3-1 5-2l1 2c-2 2-5 2-7 2h-1v-2h1z" class="M"></path><path d="M411 541c2 0 3 1 4 1v1l2 1h1c-1 2-2 4-3 5-2 1-3 2-5 2h-1l-1-1 3-3v-3c1-1 0-2 0-3z" class="Y"></path><path d="M415 543l2 1c0 1-1 2-2 2h-2l2-3z" class="Q"></path><path d="M411 541c2 0 3 1 4 1v1l-2 3-2 1v-3c1-1 0-2 0-3z" class="f"></path><path d="M402 544l1-2c1-1 2-2 4-3 2 0 3 1 4 2 0 1 1 2 0 3v3l-3 3 1 1h-1c-2 0-3-1-4-2l-1-2c0-1-1-1-1-3z" class="Q"></path><path d="M410 546h-1c-2-1-2-2-2-4h1 2v1l1 1-1 2z" class="H"></path><path d="M411 544v3l-3 3 1 1h-1c-2 0-3-1-4-2l-1-2c1 0 1-1 2-1s1 0 2 1h2l1-1 1-2h0z" class="N"></path><path d="M631 205l3-2h1c1 3 1 3 3 5h2c0 3-1 5-1 8l-3 6-2 3c0 1 0 1-1 2l-2 1h-1l-1-2c-2 0-1 0-3 1h-3c-1 0-3 1-4 1h-2c-2-1-2-1-3-3-2 1-3 1-5 1-1 0-2 1-2 1-2 0-3 1-5 1v-2c2-1 4-3 6-4h1c1-1 1-1 2-1 0-1 1-2 1-2 1-1 1-1 2-1h0l1-1h2l1-1 1 1c1-1 1-2 3-2l1 1h1l3-2c0-3 2-5 3-7v-2h1z" class="d"></path><path d="M626 221l-3 6c-1 0-3 1-4 1 1-2 2-2 3-3 1-2 2-3 4-4z" class="Y"></path><path d="M614 225c2 0 4-1 6-1h0c-1 1-1 2-3 3 1 0 1 0 2 1h-2c-2-1-2-1-3-3z" class="H"></path><path d="M618 216l1 1c1-1 1-2 3-2l1 1h1v2l-1 1-1-1c-1 0-2 0-3 1h0l-2-1-2 2h-1l3-3 1-1z" class="G"></path><path d="M609 222h2c-1 2-2 2-3 3v1c2 0 4-3 5-4h1c1 0 3 0 4-1l1-1h1v2h1l1-1c0 1-1 2-2 3-2 0-4 1-6 1-2 1-3 1-5 1-1 0-2 1-2 1-2 0-3 1-5 1v-2c2-1 4-3 6-4h1z" class="J"></path><path d="M626 221v-1-1-1l1-1v-1c1 0 1-1 1-2l1-1v1c1-2 0-2 2-3v2 2c-1 1-2 1-2 2l3 4v1 2c0 2-1 2-1 4h-1l-1-2c-2 0-1 0-3 1h-3l3-6z" class="Q"></path><path d="M631 205l3-2h1c1 3 1 3 3 5h2c0 3-1 5-1 8l-3 6-2 3c0-2 0-3 1-4-1-1-1 0-2-1l2-2c0-2 0-3 1-4l-1-2c-1 0-1 1-2 2 0-1 0-2-1-3h0c1-1 1-2 1-3l-3-1v-2h1z" class="B"></path><path d="M631 205l1 1 2-1v1c1 0 1 0 2 1v4c1 1 2 0 1 2l-1 1-1-2c-1 0-1 1-2 2 0-1 0-2-1-3h0c1-1 1-2 1-3l-3-1v-2h1z" class="R"></path><path d="M390 471h1c0-2-1-4 0-6h1l8 3c1 1 2 2 3 2h0c1 1 1 2 2 2v1c1 1 6 2 6 3 2 1 3 2 4 4 0 1 1 2 2 3v1c1 0 3 0 4 1h0c1 1 1 1 1 3v2c0 1-1 3 0 5 0 1 0 0 1 2h0c0 1 1 2 1 3l-2 1-1 1v1c1 2 2 3 2 5l-1 1c-3-4-5-9-8-13-2-4-5-7-7-10-3-4-7-7-11-10-2-2-5-3-6-5h0z" class="g"></path><path d="M392 465l8 3c1 1 2 2 3 2h0c1 1 1 2 2 2v1c-5-2-9-4-13-7v-1z" class="H"></path><path d="M390 471c2 1 4 3 6 4 5 3 11 8 15 13 2-1 3-1 4-1 1-1 2 0 4 0h2v-2c1 1 1 1 1 3v2c0 1-1 3 0 5 0 1 0 0 1 2h0c0 1 1 2 1 3l-2 1-1 1v1c1 2 2 3 2 5l-1 1c-3-4-5-9-8-13-2-4-5-7-7-10-3-4-7-7-11-10-2-2-5-3-6-5z" class="V"></path><path d="M421 485c1 1 1 1 1 3v2c0 1-1 3 0 5 0 1 0 0 1 2h0c0 1 1 2 1 3l-2 1-1 1v1c-1-2-2-4-2-5-3-4-5-7-8-10 2-1 3-1 4-1 1-1 2 0 4 0h2v-2z" class="W"></path><path d="M419 498h2c0 1 1 1 1 3l-1 1v1c-1-2-2-4-2-5z" class="C"></path><path d="M417 490c2-1 3 0 4 0v2c-2 0-2 0-4-1v-1z" class="Z"></path><path d="M624 688c1 0 2-1 4 0 1 1 2 2 2 4 1 5-1 11-3 15-2 2-6 5-8 6h-1v1c-1 0-3-1-5-1 0-1-1-1-1-2l-2-2c-2-3-2-9-1-13 1-2 4-6 7-6 3-1 4 0 7-2h1z"></path><path d="M719 668l3-5c1 2 1 3 1 5l2-1c1 0 1 1 2 1l1 1h0c1 2 2 4 3 5-1 3-2 4-5 6h-2c0 2 0 2 1 3h-1c-1 0-2-1-3-1v1c0 1 1 2 2 2 2 1 4 2 6 1v-1l2 1c-3 3-5 5-7 8l-3 5h-3c-1 1-2 2-3 4s-2 3-5 5h0l-2-1 1-2c3-1 4-2 6-4 4-6-4-8-5-12l1-1-3-3 3-3 8-14z" class="Y"></path><path d="M716 678c1-1 1-2 2-3l1 1v3c-2 0-2 0-3-1z" class="D"></path><path d="M725 667c1 0 1 1 2 1l1 1h0l-1 2h-4l1-2-1-1h0l2-1z" class="I"></path><path d="M719 668l3-5c1 2 1 3 1 5h0l1 1-1 2c-2 1-3 1-3 3v2h-1l-1-1c-1 1-1 2-2 3h-1l-1 2c0 1 0 1-1 2 0 1 0 2-1 3l1 2-2 1-3-3 3-3 8-14z" class="H"></path><path d="M711 682c0 2 0 2 1 3l1 2-2 1-3-3 3-3z" class="j"></path><path d="M723 685c2 1 4 2 6 1v-1l2 1c-3 3-5 5-7 8l-3 5h-3c-1 1-2 2-3 4s-2 3-5 5h0l-2-1 1-2c3-1 4-2 6-4 4-6-4-8-5-12l1-1 2-1h0c1 3 4 3 5 5 1 1 1 2 2 2l1-1v-1l1-1c0-1 0-1 1-2s0-3 0-4z" class="D"></path><path d="M375 604c0-1 1-1 1-2h3v3h1 3 2c-1 1-1 2-2 4l-3 9h0l1 1c-1 2-3 5-4 7-2 1-4 3-5 5h1c-7 7-21 8-30 7h-3v-2c1-2 2-3 4-4l7-1h-2-1l2-1h-1l10-4c3-2 6-3 8-5 2-1 4-2 5-1 2-1 2-2 3-2 1-1 2-3 2-4 1-2 1-3 2-5l-2-2c-1-1-1-2-2-3z" class="U"></path><path d="M353 632l3-1 1-1 2 1v1c-2 0-3 0-5 1 1 1 2 1 3 1v1c-2 0-3 1-5 0v-1l1-2z" class="T"></path><path d="M364 630l1 1c-1 2-2 3-4 3-1 1-3 1-4 1v-1c-1 0-2 0-3-1 2-1 3-1 5-1v1l1-1c1-1 2-1 4-2z" class="R"></path><path d="M372 620c2-1 2-2 3-2 0 1-1 3-2 4s-2 1-2 2-1 2-2 3h-1v1c1 1 1 1 3 1h-4l-1-1-2 1h0 2v2l1-1h0 1l-1 1c-2 0-2-1-3-1-2 1-3 1-4 2l-1 1v-1-1l-2-1s1-1 1-2c1 0 2-1 3-1v2c1 0 2-1 3-2h1c3-2 5-5 7-7z" class="G"></path><path d="M367 621c2-1 4-2 5-1-2 2-4 5-7 7h-1c-1 1-2 2-3 2v-2c-1 0-2 1-3 1 0 1-1 2-1 2l-1 1-3 1-2-1h-2-1l2-1h-1l10-4c3-2 6-3 8-5z" class="Z"></path><path d="M375 604c0-1 1-1 1-2h3v3h1 3 2c-1 1-1 2-2 4l-3 9h0c-3 5-6 8-10 11 1-2 2-5 3-7 1-1 2-3 2-4 1-1 2-3 2-4 1-2 1-3 2-5l-2-2c-1-1-1-2-2-3z" class="P"></path><path d="M351 251c2 1 3 1 4 1 3 1 7 0 10 0l23 1h18 8 0 7c0 2 0 4-1 6h-5c-2 1-5 0-7 0h-9-7-8c0 1-2 1-2 1h-5-3-4v-2h0l-5-1-1 1s-1 1-2 1c-2 1-4 1-6 1l-1-1v-4c-1-1-1-2-2-3-2 1-2 1-3 2h-1c1-2 1-2 2-3z"></path><path d="M370 258h1 2l2-2-1-2 1-1c1 0 2 0 3 1 0 1 0 1-1 2 0 1 0 2-1 3l-2 1h-4v-2z" class="L"></path><path d="M362 259l1-3c-1 0-1-1-1-2h7c1 2 1 2 1 4l-5-1-1 1s-1 1-2 1z" class="i"></path><path d="M392 259v-2-1l1 1v-1l-1-1 1-1h6c1 1 1 3 0 5h-7z" class="B"></path><path d="M378 254c2 0 5 0 6 1 0 1 1 2 0 4h0c0 1-2 1-2 1h-5-3l2-1c1-1 1-2 1-3 1-1 1-1 1-2zm30 5c-1-2-1-3-1-4 2-2 5-2 8-1v5c-2 1-5 0-7 0z" class="E"></path><defs><linearGradient id="AB" x1="331.877" y1="685.389" x2="326.574" y2="689.668" xlink:href="#B"><stop offset="0" stop-color="#bdbcbc"></stop><stop offset="1" stop-color="#e9e8e7"></stop></linearGradient></defs><path fill="url(#AB)" d="M298 643c6 10 11 19 17 28 13 18 29 35 47 49-1 1-2 1-4 2l-2-2-4-3-3 1-4-4-3-3c-3-2-5-3-8-6h0c0-1-1-2-2-2l-5-6c1-1 1-2 3-2l-5-6c-7-8-13-17-19-25-2-4-4-6-6-10h1l1 1 1-3-1-1-1-1c-1-1-1-2-1-3-1-1-2-2-2-4z"></path><path d="M344 709l8 8-3 1-4-4-3-3c1 0 1-1 2-2z" class="Q"></path><path d="M330 695l2 2h0v1h0l12 11c-1 1-1 2-2 2-3-2-5-3-8-6h0c0-1-1-2-2-2l-5-6c1-1 1-2 3-2z" class="F"></path><defs><linearGradient id="AC" x1="681.054" y1="751.406" x2="676.873" y2="744.193" xlink:href="#B"><stop offset="0" stop-color="#131312"></stop><stop offset="1" stop-color="#393637"></stop></linearGradient></defs><path fill="url(#AC)" d="M691 731h0c0-1 0-1 1-2h2s1-1 2-1c1 1 2 2 4 2h0 2c-1 2-1 2-2 3h0c1 1 0 1 1 1l-1 1c-1 0-1 1-1 2-8 7-17 13-25 19-2 1-4 3-6 4h-2l-1 1h-1c-1 0-4 2-4 2-2 1-4 2-6 1l-5 3c-1 1-3 1-4 2h0l-1 1c-1 0-2 1-3 1 2-1 2-2 3-3l1-1 11-8c9-6 19-12 26-20l5-4 3-3 1-1z"></path><path d="M691 731h0c0-1 0-1 1-2h2s1-1 2-1c1 1 2 2 4 2h0c-7 5-12 11-18 15 2-2 4-5 6-7 2-1 2-1 3-2 0-1-1-1-1-2h-1c0 1-1 1-2 1l3-3 1-1z" class="E"></path><defs><linearGradient id="AD" x1="664.889" y1="751.588" x2="667.468" y2="754.865" xlink:href="#B"><stop offset="0" stop-color="#b4b3b2"></stop><stop offset="1" stop-color="#dcdada"></stop></linearGradient></defs><path fill="url(#AD)" d="M687 735c1 0 2 0 2-1h1c0 1 1 1 1 2-1 1-1 1-3 2-2 2-4 5-6 7-8 8-19 12-28 19l-5 3c-1 1-3 1-4 2h0l-1 1c-1 0-2 1-3 1 2-1 2-2 3-3l1-1 11-8c9-6 19-12 26-20l5-4z"></path><path d="M714 528c4 0 9-1 13-1h2 1c1 0 3 0 4 1h0c1 2 2 3 2 5l-1 1h0c0 1-1 2-2 3h-4l1 1 2 1h-4c0 1-1 2-1 2v1h-13-7c-1 0-1 0-2-1v-1c-1 0-2 1-2 1-2 1-3 1-5 1 1-1 2-1 3-1h0v-2h-1c-1-1-2-1-2-2 0-4 0-2 2-3l-1-2h0c2-1 2-1 3-3h-1l1-1h12z" class="R"></path><path d="M721 535c2 0 2-1 3-1 3 1 5 1 7 0l1 1c-1 1-2 1-4 1h-1-2c-2 1-2 0-4-1z" class="W"></path><path d="M711 535c2 0 3-1 6 0h1v1c1 0 2 0 3-1 2 1 2 2 4 1h2 1v1h1l1 1 2 1h-4 0c-5-1-10-1-15-1v-1h-3l1-1v-1z" class="M"></path><path d="M713 538c5 0 10 0 15 1h0c0 1-1 2-1 2v1h-13-7c2 0 3 0 4-1l-2-2 4-1z" class="I"></path><path d="M728 539h0c0 1-1 2-1 2h-8-1c1-2 8-2 10-2z" class="D"></path><defs><linearGradient id="AE" x1="714.407" y1="536.047" x2="720.054" y2="522.725" xlink:href="#B"><stop offset="0" stop-color="#e1dadd"></stop><stop offset="1" stop-color="#f1f3ef"></stop></linearGradient></defs><path fill="url(#AE)" d="M714 528c4 0 9-1 13-1h2 1c1 0 3 0 4 1h0c-1 2-3 2-5 2h-1-8c-4 1-8 0-12 1h-1l-1 1h-4c-1-1-1-2 0-3h-1l1-1h12z"></path><path d="M702 529c-1 1-1 2 0 3h4l1-1h1s0 1 1 1h0c1 1 2 1 2 1v1 1 1l-1 1h3v1l-4 1 2 2c-1 1-2 1-4 1-1 0-1 0-2-1v-1c-1 0-2 1-2 1-2 1-3 1-5 1 1-1 2-1 3-1h0v-2h-1c-1-1-2-1-2-2 0-4 0-2 2-3l-1-2h0c2-1 2-1 3-3z" class="E"></path><path d="M701 539h8l2 2c-1 1-2 1-4 1-1 0-1 0-2-1v-1c-1 0-2 1-2 1-2 1-3 1-5 1 1-1 2-1 3-1h0v-2z" class="H"></path><defs><linearGradient id="AF" x1="745.639" y1="212.581" x2="728.911" y2="254.035" xlink:href="#B"><stop offset="0" stop-color="#d3cfcb"></stop><stop offset="1" stop-color="#f5f6fa"></stop></linearGradient></defs><path fill="url(#AF)" d="M794 237l-74-1h-27c-4 0-10 0-14-1 0-1 0-2 1-3 2-1 6 0 8 0h17 59 12c1 0 4-1 5 0 1 0 2 1 3 1h0l-1-1c3-1 6 0 9 0h5c-1 1-2 2-3 2v3z"></path><path d="M446 250c3 0 7-1 9 0h9c1-1 2-1 4-1v2c-2 3-4 6-7 9-2 1-6 1-9 0l-7-2c-3 2-5 5-9 6-1-1 0-1-1-1v1h-1 0v-1c0-1-2-2-2-2l-47 1h-4c-2-1-4-1-6-1h-4-1c3 0 9 0 11-1h1s2 0 2-1h8 7 9c2 0 5 1 7 0h5c3 0 8 1 10 0v-3h1c0-2 2-3 3-4 2 0 5-1 7 0 0 1 1 1 2 1s1 0 2 1v1h1v-1c1-1 1-1 1-2l-1-1h0v-1z" class="K"></path><path d="M451 254c3 0 4 2 6 2s1 0 2 1l-2 1c-1 1-1 1-3 0-2 0-3-1-4-3l1-1z" class="V"></path><path d="M433 258c1-1 2-2 4-3 2 0 3 1 4 2-1 1-2 4-4 4-1 1-2 0-3 0l-1-3z" class="e"></path><path d="M434 252c2 0 5-1 7 0 0 1 1 1 2 1 0 2 1 2 0 4h-2c-1-1-2-2-4-2-2 1-3 2-4 3 0 1 0 1-1 2l-1-1v-3c0-2 2-3 3-4z" class="n"></path><path d="M642 396c1-2 2-4 2-6l1 1-1 3c1 1 2 1 2 3v2h0c1 2 1 4 2 6 1 3 3 4 6 5 2 1 3 1 5 0 1 2 5 6 5 8 2 0 1-1 3-1l3 3h-1c-1-1-3-1-4-1h-1l2 2c-1 0-2 1-1 3l1 2 1 1c-1 1 0 1-1 2h0l1 1h0c0 1-1 1-2 1v1c-2 1-3 1-4 3-1-1-1-1-2 0l-1-1-3 3 2 2-1 1-2-1h0l-2-1 3-3h-1-3v-3-2l-1-7c0-2-1-3-2-5l-1-1h-5s-1-1-1-2l1-2c-1 0-2-1-2-2l1-1 2-2v-1l2-2-1-1h1v-1c-1-1-2-2-2-4 1-1 1-1 1-2l-2-1z" class="H"></path><path d="M643 407l2-2c3 4 4 5 4 9-2-2-3-5-6-7z" class="J"></path><path d="M653 417v-1h-1 0v-1c0-2-1-3 0-4h1c2 1 4 3 5 6 1 2 1 3 2 5-1 3-3 4-6 5h0c-1 0-1-1-1-1l1-2h0l1-4c-1-1-1-2-2-2v-1z"></path><path d="M660 422v-2c1 0 1 1 2 2h-1v1h1 0c1 2 0 3-1 4l-6 8h-1-3v-3-2l1-3 1-1s0 1 1 1h0c3-1 5-2 6-5z" class="E"></path><path d="M643 407c3 2 4 5 6 7v1l1 1 1 1h2v1c1 0 1 1 2 2l-1 4h0l-1 2-1 1-1 3-1-7c0-2-1-3-2-5l-1-1h-5s-1-1-1-2l1-2c-1 0-2-1-2-2l1-1 2-2v-1z" class="i"></path><path d="M642 413v-1c2 0 4 3 5 5h-5s-1-1-1-2l1-2z" class="I"></path><path d="M649 415l1 1 1 1h2v1c1 0 1 1 2 2l-1 4h0c-1-2-2-1-2-2-1-1-1-3-2-4s-1-2-1-3z" class="L"></path><path d="M333 589c2 0 4-1 5 0v1c5 0 11 1 14 4l1 1c1 3 1 5 0 8-1 1-2 1-3 2-6 2-9-7-13-9-1 0-2 0-2 1s0 1 2 2c0 0 1 2 1 3v2l2 1h1c0 3 0 7-1 10h0c-1-2-1-4-2-5 0-2 0-2-1-3-1 0-3-1-4 0h-6c-1 2-3 4-5 6l-1 2h-1v-2c-1-4-1-6 1-11h-1c0-1 1-2 1-2 1-1 1-2 1-4 1-1 3-3 5-4 1-1 2-2 4-2 1-1 1-1 2-1z"></path><path d="M333 589c2 0 4-1 5 0v1c-7 2-12 6-17 12h-1c0-1 1-2 1-2 1-1 1-2 1-4 1-1 3-3 5-4 1-1 2-2 4-2 1-1 1-1 2-1z" class="Z"></path><path d="M322 613v-4l2-2c1-1 3-4 3-4 1 0 4 0 4 1h6c0-1-1-1-1-2l1-1c-1-1-3-2-3-3l1-1c0 1 0 1 2 2 0 0 1 2 1 3v2l2 1h1c0 3 0 7-1 10h0c-1-2-1-4-2-5 0-2 0-2-1-3-1 0-3-1-4 0h-6c-1 2-3 4-5 6z" class="d"></path><path d="M340 605h1c0 3 0 7-1 10h0c-1-2-1-4-2-5 0-2 0-2-1-3-1 0-3-1-4 0h-6c5-2 9-3 13-2z" class="B"></path><path d="M428 509c2 0 3 0 5 1l22 6v1c0 1 1 2 2 3s1 3 3 4h0c1 2 0 7-1 9-1 3-3 7-5 10h-1l1-1c0-1-1-3-1-4-2-5-5-10-9-15h-5c-1 0-1 0-1 1-1 1-1 0-1 1-2-1-3-1-5-1h0c-1 0-2-1-3-2v-1h-7l1-3h1c1-1 2-2 2-3h-2l1-1h1l3-1c0-2 0-2-1-3v-1z"></path><path d="M447 516l5 3h2c1 0 2 1 2 2 2 2 2 5 1 8h-1v-1c-1-3-2-5-4-7l-5-5z" class="L"></path><path d="M428 509c2 0 3 0 5 1-1 1-1 2-2 3 1 3 5 4 8 5l5 5h-5c-1 0-1 0-1 1-1 1-1 0-1 1-2-1-3-1-5-1h0c-1 0-2-1-3-2v-1h-7l1-3h1c1-1 2-2 2-3h-2l1-1h1l3-1c0-2 0-2-1-3v-1z" class="S"></path><path d="M434 521c2 0 4-1 6 0v1l-1 1h-6v-1l1-1z" class="P"></path><path d="M428 509c2 0 3 0 5 1-1 1-1 2-2 3 1 3 5 4 8 5l-2 1h-5 0c-2-1-3-3-4-5l1-1h0c0-2 0-2-1-3v-1z" class="C"></path><path d="M679 252l1 3c1 0 2 0 3-1 0-1 1-2 0-2v-1l1-1c1 1 1 1 2 1h1l-1 1v4 3 4l14-2c1 0 2 0 3-1 1 0 2-2 3-3v-1l1-1 2 3h13l37-1h17l3 1c0 2 1 2 0 3h0c-10-1-20 0-30 0h-30-11v1c-5 5-13 5-19 10h-4l-5-2c-1-2 0-4-1-6v-12z" class="K"></path><path d="M686 263l14-2c0 1 0 2-1 3v1h-1c-1-1-1-2-2-2l-2 2c0 1-3 1-3 1-2 1-4 2-5 1-2 0-2 0-3-1l1-1h2l-1-1 1-1z" class="C"></path><path d="M386 539l2-1c3-1 7-1 10-2l2 2c0 1-1 3-2 4l-2 2-1 1h6l1-1c0 2 1 2 1 3l1 2c-2 0-4-1-6 1h1l2 2s1 1 1 2l-1 1s1 0 1 1c-1 1 0 2 0 3v1c1 2 3 4 4 6 2 1 5 1 8 2h1c3 0 5-1 7-1 1 1 1 1 0 2h1v1c-1 1-2 1-4 2h-8l-4-1v-1h0c-1-1-2-1-2-2l-1-1c-1 0-2 0-3 1h-1c-1-1-2-2-3-2h-1l-3-2c-3-2-5-4-7-7v-1c-2-4-3-7-3-11l2-5h0l1-1z" class="M"></path><path d="M400 562c0-1 1-2 2-2 1 2 3 4 4 6-3-1-5-3-6-4z" class="b"></path><path d="M398 556c1 1 1 2 3 3h1v1c-1 0-2 1-2 2l-3-3c1-1 1-2 1-3z" class="V"></path><path d="M401 552s1 1 1 2l-1 1s1 0 1 1c-1 1 0 2 0 3h-1c-2-1-2-2-3-3v-1l3-3z" class="G"></path><path d="M388 554l1-1 7 7v2l5 6h-1c-1-1-2-2-3-2-4-4-7-7-9-12z" class="P"></path><path d="M419 572c-2-1-3 0-5-1h-1l-2-1h0v1c-2 0-3 0-3-1-1 0-2-2-3-2v-1c1 0 3 0 4 1h1 1 4 0c3 0 5-1 7-1 1 1 1 1 0 2h1v1c-1 1-2 1-4 2z" class="B"></path><defs><linearGradient id="AG" x1="392.774" y1="536.287" x2="388.96" y2="551.145" xlink:href="#B"><stop offset="0" stop-color="#060604"></stop><stop offset="1" stop-color="#323332"></stop></linearGradient></defs><path fill="url(#AG)" d="M386 539l2-1c3-1 7-1 10-2l2 2c0 1-1 3-2 4-2 0-5-2-7-1-1 0-2 1-3 2 0 1-1 2-2 3h1c1 1 1 1 1 2s0 2 1 3v2l-1 1c2 5 5 8 9 12h-1l-3-2c-3-2-5-4-7-7v-1c-2-4-3-7-3-11l2-5h0l1-1z"></path><path d="M386 546h1c1 1 1 1 1 2s0 2 1 3v2l-1 1h-1c-1-3-1-5-1-8z" class="E"></path><path d="M388 543c1-1 2-2 3-2 2-1 5 1 7 1l-2 2-1 1h6l1-1c0 2 1 2 1 3l1 2c-2 0-4-1-6 1h1l2 2-3 3v1c0 1 0 2-1 3-3-3-5-6-6-9l-1-1h-1 0v-4c0-1 0-2-1-2z" class="J"></path><path d="M393 547h2c1 0 1 1 2 3l-1 1s-1 0-1 1l-2-2v-3z" class="j"></path><path d="M388 543c1-1 2-2 3-2 2-1 5 1 7 1l-2 2-1 1h6l1-1c0 2 1 2 1 3l1 2c-2 0-4-1-6 1v-1c0-1 0-1-1-2-1-2-3-1-5-1-1 1-1 1-1 4l-1-1h-1 0v-4c0-1 0-2-1-2z" class="S"></path><path d="M642 417h5l1 1c1 2 2 3 2 5l1 7v2 3h3 1l-3 3-2 1c0 2-1 3-1 4v2l-1 1v1c-1 1-1 2-2 3l-4-1c-1 1-2 2-3 2-1 1-2 2-4 3l-2-1h-1c-1 1 0 1-1 2h-1c-1 0-1 0-2 1v-1l-1-1c-3 1-6 2-8 1l4-1v-1c0-1-1-2-2-3l5-2v-1l-5-5-3-2 2-1 1 1c2 1 3 0 4-1l8 6c1-1 1-2 1-3 2-2 3-6 6-7 2-1 3-4 4-6v-1l-2-2h2c1-1 0-3-1-4l-1-5z" class="Y"></path><path d="M640 442c-1 1-2 3-3 4 0 0-1 0-1 1l-1-1c1-1 1-2 1-3v-1l1-1 1 1h1 1z" class="F"></path><path d="M626 447c0-3-1-4-3-5v-1c2 0 2 0 4 1 2 2 2 3 5 4l1 1v1h-3v1l-1 1c-1 1-1 2-1 2-2 1-3 1-5 2v-1c0-1-1-2-2-3l5-2v-1z" class="H"></path><path d="M648 441c1 0 2-1 2-2 0 2-1 3-1 4v2l-1 1v1c-1 1-1 2-2 3l-4-1c-1 1-2 2-3 2-1 1-2 2-4 3l-2-1h-1c-1 1 0 1-1 2h-1c-1 0-1 0-2 1v-1l-1-1c-3 1-6 2-8 1l4-1c2-1 3-1 5-2s5-2 8-3c3-2 5-4 8-7l1-1 2-1 1 1z" class="O"></path><path d="M648 441c1 0 2-1 2-2 0 2-1 3-1 4v2l-1 1v1c-1 1-1 2-2 3l-4-1h1l-1-2 1-2h1 1c0-2 1-3 3-4z" class="N"></path><path d="M649 443v2l-1 1v1c-1 1-1 2-2 3l-4-1h1l6-6z" class="S"></path><path d="M642 417h5l1 1c1 2 2 3 2 5l1 7v2 3h3 1l-3 3-2 1c0 1-1 2-2 2l-1-1-2 1-1 1h-4-1-1l-1-1c1-1 1-2 2-2h1c1-1 1-2 2-3h1c1 0 1-5 2-6l1-1-2-1-2-2h2c1-1 0-3-1-4l-1-5z" class="j"></path><path d="M648 418c1 2 2 3 2 5l-2-1c-1-1 0-2 0-4z" class="I"></path><path d="M645 441l-1-1v-1l1-1v-1c0-1 2-3 3-4h1c1 0 2-1 2-1v3h3 1l-3 3-2 1c0 1-1 2-2 2l-1-1-2 1z" class="F"></path><path d="M651 435h3 1l-3 3-2 1c0 1-1 2-2 2l-1-1c1-1 3-3 4-5z" class="h"></path><defs><linearGradient id="AH" x1="588.13" y1="274.652" x2="586.997" y2="261.337" xlink:href="#B"><stop offset="0" stop-color="#bebbb9"></stop><stop offset="1" stop-color="#eee"></stop></linearGradient></defs><path fill="url(#AH)" d="M642 265h8 5l2 2h7 6v1h0c2 0 2-1 4-1-1 1-1 1-2 1v1c1 0 2-1 4-1v1c-1 1-3 1-5 1l-107-1c-12-2-25-1-37-1-1-1-3-1-4-1-2 1-1 1-2 0 2 0 5 1 8 0h0c2 0 3-1 4-1h24 7l70 1c2-1 2-2 4-2h4z"></path><path d="M642 265h8 5l2 2h-23c2-1 2-2 4-2h4z" class="J"></path><path d="M330 463l2 2c2 2 2 2 5 2l1 1h0c3-2 5-3 8-4l4 1-2 2c-4 1-9 2-13 5v2c-6 4-12 13-13 20-1 5-2 13 2 18h0c-1 2-2 2-3 2l-1-1c-1 1-1 1-2 1l-1-1-4-1-1-1h0l1-4c-1-2-3-3-5-4h0v-1h1c1 0 2 0 3-1 2-2 2-5 3-8 0-5 1-9 3-14 0-2 0-4-1-7 2 0 3-1 4-2 2 0 3 0 5-1h0 1c2-2 2-3 3-5v-1z" class="l"></path><path d="M317 501c0 3-1 4 1 7-1 2-1 2-1 5l-4-1-1-1h0l1-4h0l3-3c0-1 1-2 1-3z" class="g"></path><path d="M330 463l2 2c2 2 2 2 5 2l1 1-2 1c-3 0-5 1-7 2-1 1-2 2-3 2h-1c1-1 2-2 2-3l-1-1h0 1c2-2 2-3 3-5v-1z" class="K"></path><path d="M346 464l4 1-2 2c-4 1-9 2-13 5-3 2-6 4-8 6l-2 2c2-4 7-8 11-11l2-1h0c3-2 5-3 8-4z"></path><path d="M318 479c1 2 1 3 1 5h1 1v1h0c-3 4-4 9-4 13 1 1 0 2 0 3s-1 2-1 3l-3 3h0c-1-2-3-3-5-4h0v-1h1c1 0 2 0 3-1 2-2 2-5 3-8 0-5 1-9 3-14z" class="K"></path><path d="M396 572c3-1 5 0 8 1v-2c1 1 3 1 4 2l-1-2 4 1 4 2v1s1 1 1 2-1 2-2 3l2 1 4 6 3 3v2s1 2 2 2 1 0 2 1v2c1 1 1 2 2 2l1 1-1 1c1 1 1 1 2 1v1s-1 0-1 1c-1 1-1 0-1 1h0 2c1 1 1 2 1 3l-1 1c1 1 0 1 2 2s4 2 6 4v-1-1c0-2 0-2 1-3 1 0 2 1 3 2-1 1-1 1-2 3h2v1l2 1v1c-1 0-2 1-3 1l-1 2h1c2-1 4-1 6 0l2 1h-1c-4-1-10 2-14 3l-6 4v-3l1-1-1-3 9-6c-3-3-7-5-10-7l-3-1-7-2-1-2-2-10-3-6c-4-7-9-11-16-16z" class="E"></path><path d="M425 608c1-1 1-1 3-2v1 2l-3-1z" class="C"></path><path d="M429 605c-3-1-7-1-9-2-1-1-1-9-1-11 2 2 4 4 7 5h1c1 1 1 2 2 2l1 1-1 1c1 1 1 1 2 1v1s-1 0-1 1c-1 1-1 0-1 1z" class="j"></path><path d="M396 572c3-1 5 0 8 1v-2c1 1 3 1 4 2l-1-2 4 1 4 2v1s1 1 1 2-1 2-2 3l2 1 4 6v1 1l-1 1c-1 0-1 0-2-1v-2c-1-2-2-3-4-4 0-1-1-2-1-2l-1 1h1c1 2 1 3 2 5h0c1 3 1 4 1 7l-3-6c-4-7-9-11-16-16z" class="S"></path><path d="M404 571c1 1 3 1 4 2l4 5h0c-2 0-6-4-8-5v-2z" class="Z"></path><path d="M407 571l4 1 4 2v1s1 1 1 2-1 2-2 3l-2-2-4-5-1-2z" class="V"></path><defs><linearGradient id="AI" x1="302.774" y1="569.969" x2="274.035" y2="577.811" xlink:href="#B"><stop offset="0" stop-color="#9c9a9a"></stop><stop offset="1" stop-color="#c7c7c6"></stop></linearGradient></defs><path fill="url(#AI)" d="M280 491s1 0 1-1c1 0 2 0 2-1v1c-1 9-4 18-5 27-3 23-1 48 3 70 1 8 2 15 4 23 3 7 5 16 9 23l3 8c1 1 1 2 1 2 0 2 1 3 2 4 0 1 0 2 1 3l1 1 1 1-1 3-1-1h-1l-1-1c-16-29-23-63-25-95-1-23-1-45 6-67z"></path><path d="M387 416c2 2 4 4 6 5l3 3c4 2 11 6 15 5 1 0 2 0 3-1h1c0 1-1 1-2 2 0 3-3 4-5 5-1 1-2 1-3 1l1 1-5 3c-1 2-2 4-2 6v1l-2 2c-1 1-2 1-4 0v-2-1h-2l-1-1v1l-1 1-3-3 1-2-2-3c-2-2-3-3-4-5 0-1 0-1-1-2l1-1h2v-1l-2-2-2 2h-1c0-2 0-4-1-6h0 0l2-4c2-1 3-2 5-3l1 1c0-1 1-2 2-2z" class="O"></path><path d="M394 436c-2 0-2 0-4-2 0-1 0-1-1-2 0-1 0-1-1-2h0l-3-3v-1h2s0 1 1 1l2-1 5 5c-1 1-2 1-4 2v-1l-1 1c1 1 3 2 4 3z" class="i"></path><path d="M379 420c1 1 1 1 2 1s1-1 2-1l1 1v1l-1 1-1-1-2 1 1 1h0c2 0 2 1 3 2-1 0-2 0-3 2h0l-2 2h-1c0-2 0-4-1-6h0 0l2-4z" class="e"></path><path d="M395 431c2 1 5 2 7 3 1 1 0 1 1 1-2 1-5 2-7 2 0 0-1-1-2-1-1-1-3-2-4-3l1-1v1c2-1 3-1 4-2z" class="I"></path><path d="M395 431c2 1 5 2 7 3l-2 1c-3-2-6-1-9-2 2-1 3-1 4-2z" class="k"></path><path d="M387 438c-1-2-2-4-4-5h-1l1-1c2 1 4 4 6 5h1c1 0 2 1 2 2v2c2 0 3-2 5-2 1-1 3-1 4-1s3-1 4-2l1 1-5 3c-1 2-2 4-2 6v1l-2 2c-1 1-2 1-4 0v-2-1h-2l-1-1v1l-1 1-3-3 1-2-2-3 2-1z" class="I"></path><path d="M387 438c2 1 2 1 4 1l-1 1v1l1 1c0 1-1 1-2 2l-2-2-2-3 2-1z" class="X"></path><path d="M393 447c3-3 5-5 8-7-1 2-2 4-2 6v1l-2 2c-1 1-2 1-4 0v-2z" class="h"></path><path d="M387 416c2 2 4 4 6 5l3 3c4 2 11 6 15 5 1 0 2 0 3-1h1c0 1-1 1-2 2-3 2-6 4-10 5-1 0 0 0-1-1-2-1-5-2-7-3l-5-5c-3-2-4-5-5-8 0-1 1-2 2-2z"></path><path d="M599 258c1-2 2-3 2-5v-1l27 1h9 1 15l1 1 3 3-1 2h1l5 2v1h3v4l-1 1h-7l-2-2h-5-8l-1-2-3-1-40-1c0-1 1-2 1-3h0z" class="Z"></path><path d="M621 258c1-1 0-2 1-4 1-1 3 0 5 0v3c-2 2-4 2-6 1z" class="E"></path><path d="M638 262h24 3v4l-1 1h-7l-2-2h-5-8l-1-2-3-1z" class="L"></path><path d="M637 253h1 15l1 1c-1 2-2 3-3 4v1h-9-7c1-1 1-2 1-3 1-1 1-2 1-3z" class="I"></path><path d="M637 253h1 1c2 1 3 1 5 3v1c-1 0-1 1-2 2h-7c1-1 1-2 1-3 1-1 1-2 1-3z" class="C"></path><path d="M599 258c1-2 2-3 2-5v-1l27 1h9c0 1 0 2-1 3 0 1 0 2-1 3-2 0-4 0-6-1 0-1 0-3-1-5l-1 1c-2 0-4-1-5 0-1 2 0 3-1 4-3 1-5 1-7 0-1-2 0-3 0-5h-1c-2 1-3 0-4 0h-1c0 1 0 1-1 2-1 3-1 3-4 5-2 0-2-1-4-2h0z"></path><path d="M578 251c2-1 4-2 6-1 1 0 1 0 3 1v-1l1 1v2c1 0 2 0 3-1v1h2l1-1 1 1c-1 1-2 2-3 2l-1 1h3c2-2 2-4 5-4h0l-1 1c-1 1-2 3-4 4l1 1c1 0 1 0 3-1v1h1 0c0 1-1 2-1 3l40 1 3 1 1 2h-4c-2 0-2 1-4 2l-70-1h-7c2-2 3-3 4-5 2 1 4 1 5 1l1-1 2-1h-1c1-2 2-3 2-5v-1h0 7l-1-1c1-1 1-2 2-2z" class="H"></path><path d="M561 261c2 1 4 1 5 1h0-3c0 1 0 2 1 4h-7c2-2 3-3 4-5z" class="I"></path><path d="M577 254l1-1 1 1c1 0 1 0 2 1v1l-1 1-1-1c0 2 2 3 3 4 0 0-1 0-1 1-2 0-2 0-4-1 0 0-1-1-2-1l2-2c1-1 0-1 0-3h0z" class="E"></path><path d="M570 254h0 7 0 0c0 2 1 2 0 3l-2 2c-1-1-1-1-3 0l-3 1h-1c1-2 2-3 2-5v-1z" class="C"></path><path d="M570 254h0 7 0 0l-6 2-1-1v-1z" class="n"></path><path d="M578 251c2-1 4-2 6-1 1 0 1 0 3 1v-1l1 1v2c1 0 2 0 3-1v1h2l1-1 1 1c-1 1-2 2-3 2l-1 1h-1c-2 1-4 3-5 2h-2-1c0-1-1-1-2-1h0l1-1v-1c-1-1-1-1-2-1l-1-1-1 1h0l-1-1c1-1 1-2 2-2z" class="C"></path><path d="M578 251c2-1 4-2 6-1 1 0 1 0 3 1l-2 1h-6l-1-1z" class="g"></path><path d="M743 432c2 2 4 5 5 7 2 5 4 11 6 16 1 2 2 4 3 5v1c2 5 4 10 5 15l2 7 1 4 1 2 1 9v-1c-1-1-1-2-1-3h-2l-2 3v-2h-1c-1 2-1 2-1 4-1 0-1 0-2-1 0-4-1-9-4-12l-1-1-3-1-1-1v-4-1c0-3-1-6-1-9 0-1 0-1 1-2v-3-3c1-1 1-2 1-3-2 1-2 2-4 2l1-2-1-1v-2l-1-1-2 2-1-3h1c-1-3-3-5-5-7 1 0 1-1 2-2h1c0-1 1-2 2-3l-2-2 2-2-2-3v-1l2-1z"></path><path d="M762 476c-1-1-2-1-2-3l-1 1c-1-3-2-6-4-7s-4-3-5-4v-2h1v1c1 2 4 5 6 6h1v-2c-1-2-1-3-1-5 2 5 4 10 5 15z" class="H"></path><path d="M758 483c2 1 2 1 3 3v6l1 1 1-1c1-1 0-1 0-2l2-3 1 2 1 9v-1c-1-1-1-2-1-3h-2l-2 3v-2c-1-1-2-2-2-3 0-3-1-6-2-9z" class="D"></path><path d="M754 486h2v-2l2-2v1c1 3 2 6 2 9 0 1 1 2 2 3h-1c-1 2-1 2-1 4-1 0-1 0-2-1 0-4-1-9-4-12z" class="M"></path><path d="M743 441c5 5 6 11 7 17-2 1-2 2-4 2l1-2-1-1v-2l-1-1-2 2-1-3h1c-1-3-3-5-5-7 1 0 1-1 2-2h1c0-1 1-2 2-3z" class="T"></path><path d="M740 444h1c3 3 5 6 6 11v3l-1-1v-2l-1-1-2 2-1-3h1c-1-3-3-5-5-7 1 0 1-1 2-2z" class="H"></path><path d="M749 467v1c2 1 2 2 4 3s4 4 5 6v4 1l-2 2v2h-2l-1-1-3-1-1-1v-4-1c0-3-1-6-1-9 0-1 0-1 1-2z" class="h"></path><path d="M753 482l2-1c1-1 2 0 3 0v1l-2 2v2h-2l-1-1-3-1 2-1 1-1z" class="E"></path><path d="M753 482l2 1v1c-1 1-1 1-2 1l-3-1 2-1 1-1z" class="W"></path><path d="M749 479c1-1 1-1 3-1l1-1c-2-1-3-2-3-4 1 1 3 2 4 4 0 1 0 2-1 3 0 1-1 2-1 3l-2 1-1-1v-4z" class="C"></path><path d="M705 281c13 9 27 23 35 38 2 3 3 7 5 11 1 4 2 8 4 11 0 1 1 2 1 2 1 1 39 0 44 1-1-6-1-11 0-17 1 2 0 5 0 7h1c1-1 1-2 1-3v-2c1-2 1-3 1-4v6l1-1c1 0 1 0 2 1 0 2 1 2 2 4v1l-2 1 3 3h0l1 13-1 1c-1 0-2 1-3 0 1-2 0-3 0-5v-1h-1-4c-2-1-4 0-6-1h-8-31-3c0-5-3-9-5-14s-4-11-8-16c-8-13-19-23-31-32 0-2 1-3 2-4z" class="K"></path><path d="M797 331l1-1c1 0 1 0 2 1 0 2 1 2 2 4v1l-2 1 3 3h0l1 13-1 1c-1 0-2 1-3 0 1-2 0-3 0-5v-1h-1-4l3-1c1-1-1-1 0-3h-1l-1-1v-3s1 0 1-1l-1-2c0-1 1-1 1-2v-4z" class="G"></path><path d="M797 335h1l-1-2h1c2 1 1 1 2 3l-1 2 1 1c0 1-1 1-1 1l1 2v6h0-1-4l3-1c1-1-1-1 0-3h-1l-1-1v-3s1 0 1-1l-1-2c0-1 1-1 1-2z" class="e"></path><path d="M359 494v-1c2-1 4-4 5-5v1 2l-2 1 1 1h0c2-1 3-3 4-4 0 2 0 2-1 4h-1c0 1 0 2 1 3l-2 3h0-1c-1 5-10 11-13 13s-5 3-8 5h-2v-1h-2c-1 0-2 1-3 1l-3-1-1 1h0v1c-2 0-2 0-4-1-1-1-1-1-2-1s-2 0-3-1c-1 0-1 0-1-1 1 0 2 0 3-2l1 1c1 0 1 1 3 1s4 0 5-1c0-4-6-6-6-10-1-3 1-7 3-10 1-1 2-2 4-3 1-1 2 0 3 1 4 2 6 6 11 6h5v1l4-1c2-1 2-2 2-3z"></path><path d="M359 494v-1c2-1 4-4 5-5v1 2l-2 1 1 1h0c2-1 3-3 4-4 0 2 0 2-1 4h-1c0 1 0 2 1 3l-2 3h0-1l-2-2c1 0 1-1 2-2l-1-1h-1v2c0 1-1 1-1 1 0-1-1-2-1-3z" class="k"></path><path d="M330 493c1-1 2-2 4-3 1-1 2 0 3 1 4 2 6 6 11 6h5v1c-3 1-6 1-9 1 0 1-3 1-3 1-1-1-2-5-3-6-1-2-2-3-4-4l-4 4v-1z" class="P"></path><path d="M330 493v1c-1 3-3 6-2 9s7 8 10 10c9-3 16-7 22-14h0l1-2 2 2c-1 5-10 11-13 13s-5 3-8 5h-2v-1h-2c-1 0-2 1-3 1l-3-1-1 1h0v1c-2 0-2 0-4-1-1-1-1-1-2-1s-2 0-3-1c-1 0-1 0-1-1 1 0 2 0 3-2l1 1c1 0 1 1 3 1s4 0 5-1c0-4-6-6-6-10-1-3 1-7 3-10z" class="E"></path><path d="M360 499h0l1-2 2 2c-1 5-10 11-13 13s-5 3-8 5h-2v-1l1-1v-1-1c2-1 5-2 7-3 5-3 10-5 13-10l-1-1z" class="N"></path><path d="M470 250c5 0 10 0 15 1h12l3 1-5 5 1 1-1 2c1 2 2 4 5 4h0l3-4c2 1 2 1 3 1h2 3l-1 2h-2l1 1-3 1c2 0 4-1 6 1h-71-7v-2h1v-1c1 0 0 0 1 1 4-1 6-4 9-6l7 2c3 1 7 1 9 0 3-3 5-6 7-9l2-1z" class="I"></path><path d="M489 263l4-3c1 1 1 2 1 4-2 1-2 1-3 0-1 0-2-1-2-1z" class="j"></path><path d="M503 260c2 1 2 1 3 1h2 3l-1 2h-2c-2 1-5 2-8 1l3-4z" class="D"></path><path d="M497 251l3 1-5 5-2 3-4 3h0-2v-1l6-8h1l1-1 2-2zm-20 5h1c1 0 2 0 4-1l3 3c-6 2-11 3-17 3 0-2 2-3 3-5v1c1 1 2 1 3 1s2-1 3-2z" class="C"></path><path d="M470 250c3 2 5 5 8 4 0 1 0 1-1 2s-2 2-3 2-2 0-3-1v-1c-1 2-3 3-3 5h-6l-1-1c3-3 5-6 7-9l2-1z" class="Q"></path><path d="M462 261h1c1-2 5-6 7-7l1 2c-1 2-3 3-3 5h-6z" class="Z"></path><path d="M470 250c5 0 10 0 15 1h12l-2 2-1 1h-1l-2 2c-2 1-4 1-6 2l-3-3c-2 1-3 1-4 1h-1c1-1 1-1 1-2-3 1-5-2-8-4z" class="B"></path><path d="M485 251h12l-2 2c-4 0-8-1-11 1h-1c-1-2-5-2-8-3h10z" class="b"></path><path d="M583 647c4 1 9 4 12 2l2 2-3 2h0v2l1 5 1-2 1 1 4-3h0-3c1 0 0 0 1-1 2-1 3-2 5-4 0-1 0-2 1-3v1c0 2 0 1 1 3 0 1-1 4-2 5-2 4-3 6-7 8-1 1-1 2-2 3h1 2c1-1 3-4 4-3-3 2-4 6-8 8-1 1-3 1-5 2v1c-3 1-6 0-8 0h-1 0c-4-2-7-4-9-8v-1c-1-2-2-4-2-6 0-3 1-6 2-8l2-3c3-2 7-2 10-3z"></path><path d="M600 659c1 0 1-1 2 0l-1 1c-1 1-2 3-4 3v-1l1-1-1-1c1-1 2-1 3-1z" class="e"></path><path d="M594 653v2l1 5v1h-2l-1-1 1-4c0-1 1-2 1-3z" class="S"></path><path d="M592 660l1 1h2c-1 3-2 4-4 5l-2 1-1-2c1-2 3-3 4-5z" class="X"></path><path d="M589 667h-5c-1-1-3-2-4-3-1-2-1-4 0-5 0-2 2-3 4-3s4 1 5 3h0-3c-1 0 0-1-2-1v1l-1 1-1-1h0v1 2c2 1 3 2 5 3h1l1 2z" class="G"></path><path d="M583 647c4 1 9 4 12 2l2 2-3 2h0c0 1-1 2-1 3-2-3-5-5-9-6-3-1-6 0-9 1-2 2-3 4-4 6v1c-1 3-1 6 1 9s5 6 8 7 6 1 9 1v1c-3 1-6 0-8 0h-1 0c-4-2-7-4-9-8v-1c-1-2-2-4-2-6 0-3 1-6 2-8l2-3c3-2 7-2 10-3z" class="B"></path><path d="M743 456l2-2 1 1v2l1 1-1 2c2 0 2-1 4-2 0 1 0 2-1 3v3 3c-1 1-1 1-1 2 0 3 1 6 1 9v1 4l1 1 3 1 1 1c3 3 4 8 4 12 0 1 0 3-1 4l-2-2-2-1c0 1-1 1 0 2v1c1 1 1 2 1 2s0-1-1-1h-2v1h-1s-1 0-1 1c1 6 1 12 2 18l-3 3c-1-2 0-3 0-5-1-2-1-2-2-3v-3l-5-24-9-27-1-4s1-1 2-1h0c2 0 3 1 4 0 2 0 4-1 6-3z" class="j"></path><path d="M743 456l2-2 1 1v2l-5 5c-2 0-5 0-7-2l-1-1h0c2 0 3 1 4 0 2 0 4-1 6-3z" class="M"></path><path d="M745 461c0 3-1 4-2 6v5l3 8v1h0c-1 0-2-1-3-1-3-3-3-9-3-13l5-6z" class="f"></path><path d="M746 460c2 0 2-1 4-2 0 1 0 2-1 3v3 3c-1 1-1 1-1 2 0 3 1 6 1 9v1 4c-1-1-2-1-3-2h0v-1l-3-8v-5c1-2 2-3 2-6l1-1h0z" class="L"></path><path d="M746 460c2 0 2-1 4-2 0 1 0 2-1 3v3c-1 2-2 2-3 4l-1 4h-1c0-2 1-4 1-6 1-2 2-4 1-5v-1h0z" class="h"></path><path d="M741 491l3 2h1v-2c0-1-1-1-1-2 0-2-1-3 1-5l1 2c1 1 3 2 5 3 2 2 2 4 3 6s1 2-1 4c0 1-1 1 0 2v1c1 1 1 2 1 2s0-1-1-1h-2v1h-1s-1 0-1 1c1 6 1 12 2 18l-3 3c-1-2 0-3 0-5-1-2-1-2-2-3v-3l-5-24z" class="H"></path><path d="M746 486c1 1 3 2 5 3 2 2 2 4 3 6s1 2-1 4c0 1-1 1 0 2v1c1 1 1 2 1 2s0-1-1-1h-2v1h-1s-1 0-1 1v-3l1-1h0-2 0c-1-2-1-4-2-6v-3-1c-1-1-1-4 0-5z" class="G"></path><path d="M751 489c2 2 2 4 3 6l-4 4c-1-2-2-4-1-6 1-1 1-2 2-4h0z" class="h"></path><path d="M758 264c4 0 8 1 12 1 0 1 1 2 1 3s1 2 2 2l-1 2c-1 0-2 0-3-1l-5-2c-1-1-2-1-3-1h-6c-4 0-8 1-12 3-8 4-13 9-16 18l9 10c2 2 4 6 7 8 1-1 0-3 0-4l2-6 4 1-1 4c0 2-1 4-1 7v2c1 1 2 1 3 2v2 1c2 2 0 4 5 4 2 0 3 0 5 1h2c1 0 3 1 5 2l6-2c1 0 2 1 3 2l4-2-2 5v1c-2 0-4 1-6 3v-1h-3l-3 3-2 2h0c-1 0-2 1-4 1s-4 0-6-1v-1h-1c-2-3-4-8-5-11-2-3-4-7-6-10-5-8-11-14-18-21v-2c1-1 0-2 0-2 3-10 10-16 19-21 5-1 10-1 15-2z" class="g"></path><path d="M760 321h2c1 0 3 1 5 2h-10l3-1v-1z" class="L"></path><path d="M747 311c1 1 2 1 3 2v2 1c2 2 0 4 5 4 2 0 3 0 5 1v1l-3 1c-2-1-4-1-5-2-3-2-5-7-5-10z" class="F"></path><path d="M776 323l4-2-2 5v1c-2 0-4 1-6 3v-1l-1-2c-2 0-3 0-5-1 3 0 6-1 10-3z" class="H"></path><path d="M758 264c4 0 8 1 12 1 0 1 1 2 1 3s1 2 2 2l-1 2c-1 0-2 0-3-1l-5-2c-1-1-2-1-3-1-2-1-3-1-4-1h1 5v1c1-1 2-1 3-1l1-1-1-1h-8v-1z" class="l"></path><path d="M756 332c0-2-2-4-3-7l5 1c3 0 5 1 8 0 2 1 3 1 5 1l1 2h-3l-3 3-2 2h0c-1 0-2 1-4 1s-4 0-6-1v-1l2-1z" class="D"></path><path d="M764 334c-1-1-1-2-2-3v-1c3-1 5-1 7-1l-3 3-2 2z" class="O"></path><path d="M756 332c0-2-2-4-3-7l5 1-1 1 1 3c2 1 2-1 3 2v1c-1-1-2-2-3-2l-2 1z" class="H"></path><path d="M346 231h0c6 1 11 1 17 0h28 114 29c5 0 11-1 16 0v3c0 1-1 1-1 1-2 1-7 0-9 0l-37-1-159 1 1-4h1z" class="K"></path><path d="M628 471l2-1c-2 3-6 5-9 8h1c2 1 2 1 4 1 0-1 0-1 1-2h0c2-1 3-3 5-4l1 1c0 1 0 1 1 2-3 3-6 6-9 10-1 1-3 3-4 5v1c-1 1-1 1-1 2-1 1-1 2-2 3s-3 1-4 1l-3 1h0l-1 2c-2 0-4 0-5-2l-3 6-2 3-2 3-1-1-1 1c-1 1-2 1-2 2h-1-3 0v-3c1 1 1 1 2 1s1-1 2-1v-1c-1 0-1-1-1 0h-1v-1l2-1 2-2v-3s1-1 1-2c2-2 2-5 3-8v-3c-1-3-2-4-4-6h0c-2-1-3-3-4-4h-1c-1 0-3-2-4-3 1 0 2 1 3 1l2-1 1 1 1 2h2c0-1 0-1-1-2l-1-1h2 3c2 1 3 1 6 1h1l4-1 5-2h1c2 0 4 0 5-2l1 2h1c1-1 1-2 2-2 1-1 2-1 3-1z" class="J"></path><path d="M621 472l1 2h1c1-1 1-2 2-2 1-1 2-1 3-1l-6 5c-3 3-7 6-10 9l-1-1-1-1c-1-1-1 0-1-1l3-3-1-1-1-2 5-2h1c2 0 4 0 5-2z" class="l"></path><path d="M611 484c1-1 1-2 1-3l1-1 1-2c2-2 5-2 8-2-3 3-7 6-10 9l-1-1z" class="W"></path><path d="M600 492l1 1 2 1c1-1 1-2 1-3h1l1 2c-2 3-4 6-6 10l-3 7-1 1c-1 1-2 1-2 2h-1-3 0v-3c1 1 1 1 2 1s1-1 2-1v-1c-1 0-1-1-1 0h-1v-1l2-1 2-2v-3s1-1 1-2c2-2 2-5 3-8z" class="B"></path><path d="M610 476l1 2 1 1-3 3c0 1 0 0 1 1l1 1 1 1-6 8-1-2h-1c0 1 0 2-1 3l-2-1-1-1v-3c-1-3-2-4-4-6h0c-2-1-3-3-4-4h-1c-1 0-3-2-4-3 1 0 2 1 3 1l2-1 1 1 1 2h2c0-1 0-1-1-2l-1-1h2 3c2 1 3 1 6 1h1l4-1z" class="C"></path><path d="M596 476h3c2 1 3 1 6 1h1 0c-1 1-1 3-1 4v1c0 1-3 2-3 3-1-1-1-1-2-1l1-1-1-1-1-1-2-2h-1c0-1 0-1-1-2l-1-1h2z" class="S"></path><path d="M605 477h1 0c-1 1-1 3-1 4v1c0 1-3 2-3 3-1-1-1-1-2-1l1-1-1-1c2 0 2 0 4-1l-1-1h-1-3v-1-1c2 0 4 0 6-1z" class="B"></path><path d="M610 476l1 2 1 1-3 3c0 1 0 0 1 1h-1l-2 2c-1 2-3 3-5 3s-2-1-3-2l1-1h2c0-1 3-2 3-3v-1c0-1 0-3 1-4h0l4-1z" class="K"></path><path d="M609 482l-1-1v-1c1-2 1-2 3-2l1 1-3 3z" class="P"></path><path d="M621 478h1c2 1 2 1 4 1 0-1 0-1 1-2h0c2-1 3-3 5-4l1 1c0 1 0 1 1 2-3 3-6 6-9 10-1 1-3 3-4 5v1c-1 1-1 1-1 2-1 1-1 2-2 3s-3 1-4 1l-3 1h0l-1 2c-2 0-4 0-5-2 1-2 2-4 4-7 1-2 3-4 5-6 1 0 2 0 3 1h0c-1-1-2-2-2-3l6-6z" class="g"></path><path d="M615 494c0-2 1-3 3-5l1 1v3l-2 2-2-1h0z" class="n"></path><path d="M619 493l2-1c-1 1-1 1-1 2-1 1-1 2-2 3s-3 1-4 1l1-1c0-1 0-1-1-2l1-1 2 1 2-2z" class="P"></path><path d="M609 493c2-1 2-1 4-1v1l2 1h0l-1 1c1 1 1 1 1 2l-1 1-3 1h0l-1 2c-2 0-4 0-5-2 1-2 2-4 4-7v1z" class="K"></path><path d="M609 493c2-1 2-1 4-1v1c-1 1-2 3-3 3h-1v-3z" class="n"></path><path d="M623 529c3 0 3 0 6 2l1-1 5 3v1h-1c-1-1-1-1-2-1 1 1 3 3 4 3 2 1 2 0 3 2h-1l1 1c1 1 1 2 2 4h1v-2l2 1 1-1c2 2 2 4 4 7l1-2c0 1 1 1 2 2l2-1v-2h2l1 2h0v3 1h-1c-5 2-9 4-14 7l-2-1-4 3c-1 1-1 1-2 1l-1 1h-1l-1 1s-1 0-2 1v1c-2 0-2 1-4 1v-1l2-4-1-4h0v-1c-1-1-2-1-3-2h0v-1h-4l3-3-1-2c-1 0-2 1-3 1s-2 1-3 1h0l1-2h0c2-1 3-2 3-3 0-2 0-3-1-4l-2-1c0-1 1-2 2-3l-1-1c-2-1-5 0-6-1v-4c2-1 6-2 9-2h3z" class="R"></path><path d="M640 557c2-2 5-4 7-5 2-2 6-2 8-4l1-1h1v3 1h-1c-5 2-9 4-14 7l-2-1z" class="n"></path><path d="M632 538c2 1 3 2 4 4l1 1c1 2 1 8 0 10-1 3-2 4-4 5-1 1-1 1-1 2 0 0 1 1 2 1l-1 1h-1l-1 1s-1 0-2 1v1c-2 0-2 1-4 1v-1l2-4c4-3 7-5 8-11 1-2 0-5-1-6-1-3-3-3-5-4 2 0 2-1 3-2z" class="I"></path><path d="M611 531c2-1 6-2 9-2h3l3 4h-2-5l10 4 3 1c-1 1-1 2-3 2-3 0-5-1-7-1h-1-1c-1 0-1 0-1 1l-1 1-2-1c0-1 1-2 2-3l-1-1c-2-1-5 0-6-1v-4z"></path><path d="M618 537l4 2h-1-1c-1 0-1 0-1 1l-1 1-2-1c0-1 1-2 2-3z" class="E"></path><path d="M622 539c2 0 4 1 7 1 2 1 4 1 5 4 1 1 2 4 1 6-1 6-4 8-8 11l-1-4h0v-1c-1-1-2-1-3-2h0v-1h-4l3-3-1-2c-1 0-2 1-3 1s-2 1-3 1h0l1-2h0c2-1 3-2 3-3 0-2 0-3-1-4l1-1c0-1 0-1 1-1h1 1z" class="U"></path><path d="M626 545h2 2c1 2 1 3 0 5-1 3-3 4-4 6-1-1-2-1-3-2h0v-1h-4l3-3 4-4v-1z" class="F"></path><path d="M626 545h2l2 2c-1 2-2 3-3 4l-2 1-1-1c1-2 2-3 2-5v-1z" class="j"></path><path d="M619 540c1 0 2 1 2 1 2 0 3-1 4-1 2 2 4 2 5 5h-2-2v1l-4 4-1-2c-1 0-2 1-3 1s-2 1-3 1h0l1-2h0c2-1 3-2 3-3 0-2 0-3-1-4l1-1z" class="B"></path><path d="M621 541c2 0 3-1 4-1 2 2 4 2 5 5h-2-2c0-1-1-2-2-3l-1 1c1 0 1 0 0 1l-1 1c0-1 0-3-1-4z" class="N"></path><defs><linearGradient id="AJ" x1="711.727" y1="247.27" x2="757.767" y2="286.653" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232221"></stop></linearGradient></defs><path fill="url(#AJ)" d="M689 272c6-5 14-5 19-10v-1h11 30c10 0 20-1 30 0-1 2-1 2 0 3l2 1c0 1 1 2 0 2l-1 1-2 1v-1h-1v1l-2-1h-4c0-1-1-2-1-3-4 0-8-1-12-1-5 1-10 1-15 2-9 5-16 11-19 21h-1l-5-3-5-5c-1 0-2-1-3-1v1c-3-1-7-4-9-6-2 2-4 1-7 1l-5-2z"></path><path d="M770 265c2 0 4-1 7 0l-2 3h-4c0-1-1-2-1-3z" class="Z"></path><path d="M779 264l2 1c0 1 1 2 0 2l-1 1-2 1v-1h-1v1l-2-1 2-3s1 0 2-1z" class="C"></path><path d="M707 268c4-1 7-2 10-2v2h-1c-1 0-2 1-2 2-2-1-2-1-4 0l-1-1v-1h-2z" class="G"></path><path d="M728 271c-1-2-1-3-2-4v-1h1 8c0 1-1 1-2 2s-2 2-3 4h-1l-1-1z" class="X"></path><path d="M735 266c2-1 4-1 6-1l2 1c-9 5-16 11-19 21h-1l-5-3 2-2c1-2 2-6 2-8v-4c0 1 1 2 1 4h2l1-2 1 1 1-2 1 1h1c1-2 2-3 3-4s2-1 2-2z" class="H"></path><path d="M722 270c0 1 1 2 1 4h2l1-2 1 1c-1 1-1 2-3 3 1 1 1 2 2 3-1 2-3 3-3 6-2-1-2-1-3-3 1-2 2-6 2-8v-4z" class="j"></path><path d="M717 266l1-1c1-1 2 0 3 0 1 2 1 4 1 5v4c0 2-1 6-2 8l-2 2-5-5c-1 0-2-1-3-1v1c-3-1-7-4-9-6l-3-2v-1c3 0 6-1 9-2h2v1l1 1c2-1 2-1 4 0 0-1 1-2 2-2h1v-2z" class="O"></path><path d="M714 270h0 3l1-2h1v1c-1 3 0 4-1 7l-1-2v-1c-2 0-1 0-2 1h-1l-1-1c1-1 1-1 1-2-1 0-2-1-4-1 2-1 2-1 4 0z"></path><path d="M707 268h2v1l1 1c2 0 3 1 4 1 0 1 0 1-1 2l1 1h1c1-1 0-1 2-1v1l1 2c-1 1-2 3-3 4-1-1-2-1-2-2-1-2-1-2-2-2l-1-2-2-1-1 1c0 1 1 2 2 2v1c2 0 3 1 4 2-1 0-2-1-3-1v1c-3-1-7-4-9-6l-3-2v-1c3 0 6-1 9-2z" class="U"></path><defs><linearGradient id="AK" x1="662.302" y1="503.507" x2="708.253" y2="485.156" xlink:href="#B"><stop offset="0" stop-color="#a2a1a0"></stop><stop offset="1" stop-color="#d5d4d3"></stop></linearGradient></defs><path fill="url(#AK)" d="M692 459l2 1 2-2 1 1c-1 1-3 3-3 5 1 2 2 4 4 4 1 1 2 1 3 1 1-1 3-1 4-2v1h0c-2 3-3 5-2 9 1 3 4 11 6 13h1c0 2 0 4-1 6 0 3-1 6 0 9 1 1 2 2 3 2-2 1-4 0-5 2l-1 1-2 8-1 1c-6 1-12 2-18 0h1 1v-1c-3-1-17-8-19-10-1-1-2-1-3-2h0c-2-1-1 0-2-1s-1-1-2-1v-1l-3-3c-1-1-1-2-2-3l1-1c1 2 3 3 4 5 0 1 0 0 1 1-1-1-1-1-1-2 2 1 3 0 5 0h3c1 0 3-1 4-1 7-3 7-8 8-14 0-3 1-5 2-7h0c0 2-1 4-1 6 0 3 2 9 0 12s-8 7-12 8h0c4 3 8 6 13 7 2 1 7 1 10 0 3-3 7-9 8-14 0-7-4-16-8-20-5-5-13-9-19-10l-1-1h-1c0-1 1-2 1-3v-2c1 0 2 0 3 1 1 0 0 0 1 1 1 0 1 0 2-1 2-2 5-2 8-2l1 1c-1 0-2 1-3 1h-2c1 1 1 1 2 1s1 1 3 2l2-1 2-5z"></path><path d="M682 467c5 0 7 1 11 4l1 1h1 1c2 1 4 4 5 7h0c-4 1-8-6-11-7l-1-1c-2-1-5-2-7-4z" class="R"></path><path d="M668 508h1l19 10c2 1 6 0 7-1h2c3 0 4 0 6 1h1l-1 1c-6 1-12 2-18 0h1 1v-1c-3-1-17-8-19-10z" class="X"></path><path d="M697 517h0c2-1 3-1 4-1 0-1-1-1 0-2v-1l-2 1h-3-1v1h0l-1-1 4-2c1-2 2-4 4-5h0v-1l-1-1 3-3h2l1 1h-2c0 2-1 3-1 5 0 0 1 1 1 2h1l-2 8h-1c-2-1-3-1-6-1z" class="M"></path><path d="M673 463v-2c1 0 2 0 3 1 1 0 0 0 1 1 1 0 1 0 2-1 2-2 5-2 8-2l1 1c-1 0-2 1-3 1h-2c1 1 1 1 2 1s1 1 3 2l2-1c0 4 3 6 5 8h-1l-1-1c-4-3-6-4-11-4-1 0-3-1-4-1h-5-1c0-1 1-2 1-3z" class="J"></path><path d="M678 466v-1h-1l1-1c1 0 2 0 3 1l2 1h1 1c1 1 2-1 3-1l2-1c0 4 3 6 5 8h-1l-1-1c-4-3-6-4-11-4-1 0-3-1-4-1z" class="e"></path><path d="M348 467c3 0 5 0 7 1v2c4 2 8 6 10 10l1 3c-1 1-2 3-2 4v1c-1 1-3 4-5 5v1c0 1 0 2-2 3l-4 1v-1h-5c-5 0-7-4-11-6-1-1-2-2-3-1-2 1-3 2-4 3-2 3-4 7-3 10 0 4 6 6 6 10-1 1-3 1-5 1s-2-1-3-1l-1-1h0c-4-5-3-13-2-18 1-7 7-16 13-20v-2c4-3 9-4 13-5z"></path><path d="M364 487v1c-1 1-3 4-5 5v1c0 1 0 2-2 3l-4 1v-1c2-1 3-2 4-4l-1-1c2 0 3-1 4-2l-1-1c2 0 3-1 5-2z" class="M"></path><path d="M348 467c3 0 5 0 7 1v2c-4-2-8-1-12 0l-8 4v-2c4-3 9-4 13-5z" class="i"></path><path d="M356 492c-3-3-7-7-8-11-1 0-1 0 0-1 4 2 6 6 11 8v1l1 1c-1 1-2 2-4 2z" class="E"></path><path d="M416 643l2 5 7 6c1 0 2 0 4-1-1-1-2-1-3-1-2-1-2-1-2-2v-1c3 1 3 1 6-1 3 0 8 0 11-1h1v1 1c2 1 3 2 6 3h0v1 1c2 3 4 5 3 9-1 3-3 7-5 9-1 1-2 1-3 2h-1c-2 1-4 1-7 1l-1 1-1-1c-1 0-1 1-2 1s-5-1-6 0h-1c-2 0-4-1-5-1-4-2-8-5-10-9 1-2 1-2 1-4-1 0-2-1-2-2v-1h5l-2-2-1 1h-1c0-1-1-2-1-4 1 0 1-2 2-3v-1l2-3v-1l1-1v-1h3v-1z" class="U"></path><path d="M418 661h2 4s0 1-1 1c-1 1-3 2-4 1h0l-1 2h0-2v-1-1l1-2h1z" class="R"></path><path d="M413 659c1 1 1 2 2 5v2c2 4 6 6 9 10-2 0-4-1-5-1-4-2-8-5-10-9 1-2 1-2 1-4-1 0-2-1-2-2v-1h5z"></path><path d="M416 643l2 5 7 6v5c0 1 0 1-1 2h-4-2l1-1h1 1c-1-2-4-1-6-2l-3-6c-1-1-2 0-2-2l2-3v-1l1-1v-1h3v-1z" class="c"></path><path d="M416 643l2 5h0c-1 0 0 0-1 1l-2 2h0 2c1 1 1 1 1 2-1 1-1 0-3 0l3 3h1v-1l1-1 1 1v1c-1 0-2 1-2 1-1 1-1 0-2 0-2-1-5-4-5-7 0-1 1-1 0-3v-1l1-1v-1h3v-1z" class="G"></path><path d="M413 645v-1h3c0 2-1 3-2 4l-2-2 1-1z" class="T"></path><path d="M430 648c3 0 8 0 11-1h1v1 1c2 1 3 2 6 3h0v1 1c2 3 4 5 3 9-1 3-3 7-5 9-1 1-2 1-3 2h-1c-2 1-4 1-7 1l-1 1-1-1c-1 0-1 1-2 1s-5-1-6 0c-1-2-3-4-4-6l2-1c0 1 0 1 1 1l1-1-1-1 1-1h2c0-1-1-2-1-2v-1c1 2 4 3 6 5 1-1 1-1 2-1l1 2h1l1-1c2-2 4-3 5-6 0-2 1-3 0-4-1-2-3-3-5-3s-3 1-4 2v2c1 1 2 1 3 1h1v2c0 1-1 2-2 2-2 0-2 0-4-1-3-2-2-3-3-7-1 1-1 1-3 2v-5c1 0 2 0 4-1-1-1-2-1-3-1-2-1-2-1-2-2v-1c3 1 3 1 6-1z"></path><path d="M430 648c3 0 8 0 11-1h1v1 1c2 1 3 2 6 3h0v1 1c-3-3-7-4-11-3h-3v1c-3 1-5 2-6 5h0c-1 1-1 1-3 2v-5c1 0 2 0 4-1-1-1-2-1-3-1-2-1-2-1-2-2v-1c3 1 3 1 6-1z" class="E"></path><path d="M721 556l11 2c1 11-1 22-3 33s-5 22-9 33c-3 8-6 16-11 23h-1l-2 2v-1c-2 1-3 2-4 3l-1 1c-1 1-2 1-2 1l-2 1-4 3-2 1c-1 0-2 1-3 2h-1-4v-1c1-1 1-2 2-2 0-1 3-2 4-2 3-2 6-3 9-5 11-7 18-21 20-34 2-9 4-23-2-31-3-4-6-6-9-9-2-2-4-3-6-5-4-3-11-4-16-5-1-1-2-1-3-1 6 0 14 1 19 3 2 1 5 3 7 3h0l5-5c1-1 2-1 3 0 2-3 4-6 6-8l-1-2z" class="P"></path><path d="M722 558h0c2 0 4 1 5 1 2 1 2 1 3 4 1 6 0 10-1 17 0 4 0 8-1 12-2 8-5 16-8 24 0-6 2-11 2-16 1-3 1-7 1-10-4-3-5-8-8-12-2-1-3-3-5-4-1-1-1-1-1-2l-1-1 5-5c1-1 2-1 3 0 2-3 4-6 6-8z"></path><path d="M713 566c1-1 2-1 3 0-1 2-4 5-4 7v1c-1-1-2-1-3-2l-1-1 5-5z" class="Z"></path><path d="M723 590c-4-3-5-8-8-12-2-1-3-3-5-4-1-1-1-1-1-2 1 1 2 1 3 2v-1c3 1 8 8 9 12 1 1 1 3 2 5z" class="S"></path><path d="M761 268c1 0 2 0 3 1l5 2v1l5 5 1 2c1 3 1 6 0 9l-1 1h-2l-1-1c0 2-1 3-3 5 0 1-1 1-2 1l-2-1 1-2-2-1-1-2c0-1 0-1-1-1l-1 1-1 1h0c-2 0-2 1-4 1h-1l1 2c-1 1-2 2-4 2v-1c-2 2-1 4-2 5l-4-1-2 6c0 1 1 3 0 4-3-2-5-6-7-8l-9-10c3-9 8-14 16-18 4-2 8-3 12-3h6z" class="D"></path><path d="M742 292l-1-1h1l1 2c1-1 3 0 4-1 0 1-1 3-2 5l-2 6v-2l1-1c0-1 0-1-1-1l-1-1c-1 0-2-1-2-1-1-1-1-2-2-3h3 0l1-2z" class="I"></path><path d="M742 292c0 2 0 3-1 5h-1c-1-1-1-2-2-3h3 0l1-2z" class="c"></path><path d="M736 281c3 0 3 0 6 2 0 1 1 2 2 4l-2 1h1l-1 1v2h0-1l1 1-1 2h0-3c-1-1-2-2-3-4-1-1-2-2-2-4 1-2 2-3 3-5z" class="O"></path><path d="M735 290l2-1h5v2h0-1l1 1-1 2h0-3c-1-1-2-2-3-4z" class="N"></path><path d="M737 281c1-3 2-5 5-6 2 1 3 1 5 3h1 2l1 1c-1 2-2 3-3 4v1c0 2-1 4 0 6l-1 2c-1 1-3 0-4 1l-1-2h0v-2l1-1h-1l2-1c-1-2-2-3-2-4-3-2-3-2-6-2h1z" class="F"></path><path d="M742 283v-2l1-1 2 2 1 1-2 4h0c-1-2-2-3-2-4zm-5-2c1-3 2-5 5-6 2 1 3 1 5 3h-5-2c0 1 0 1-1 2s-1 1-2 1z" class="L"></path><path d="M746 283v-1c0-1 0-1-1-2l1-1c2 0 3-1 4-1l1 1c-1 2-2 3-3 4v1c-1 2-2 2-3 3h-1l2-4z" class="D"></path><path d="M748 284c0 2-1 4 0 6l-1 2c-1 1-3 0-4 1l-1-2h0v-2l1-1h-1l2-1h0 1c1-1 2-1 3-3z" class="F"></path><path d="M742 291c2-1 2-2 4-1l1 2c-1 1-3 0-4 1l-1-2h0z" class="D"></path><path d="M743 271c4-2 8-3 12-3h0c0 2-1 3-2 4 1 1 0 1 1 1v2h1l2 3-1 1c-4 3-7 6-8 11-1-2 0-4 0-6v-1c1-1 2-2 3-4l-1-1h-2-1c-2-2-3-2-5-3l2-2c0-1-1-2-1-2z" class="I"></path><path d="M744 273c1-1 1-1 3-1v-1h1c1 0 2 0 3-1v1c1 1 1 2 1 3 1 1 1 0 2 2h-1c-1 0-1 1-2 1l-3 1h-1c-2-2-3-2-5-3l2-2z" class="J"></path><path d="M756 279l5-1c1 2 1 3 3 5v1l1 1 1 1c0 1 1 1 1 2-1 1-1 2-2 3l-2-1-1-2c0-1 0-1-1-1l-1 1-1 1h0c-2 0-2 1-4 1h-1l1 2c-1 1-2 2-4 2v-1c-2 2-1 4-2 5l-4-1c1-2 2-4 2-5l1-2c1-5 4-8 8-11z" class="P"></path><path d="M751 293l-1-3h-1c1-1 1-1 1-3h1l1-3c1-1 2-2 4-3 0 1 1 1 1 2v1c-1 1-3 2-3 4v2l1 2c-1 1-2 2-4 2v-1z" class="K"></path><path d="M757 284v-1c3-1 5 0 7 1l1 1 1 1c0 1 1 1 1 2-1 1-1 2-2 3l-2-1-1-2c0-1 0-1-1-1l-1 1-1 1h0c-2 0-2 1-4 1h-1v-2c0-2 2-3 3-4z" class="Q"></path><path d="M760 288h-1c-1-1-1-1-1-2h2c1-1 2-1 3-1l2 2h1v1h-2s-1 1-1 2l-1-2c0-1 0-1-1-1l-1 1z" class="F"></path><path d="M761 268c1 0 2 0 3 1l5 2v1l5 5 1 2c1 3 1 6 0 9l-1 1h-2l-1-1c0 2-1 3-3 5 0 1-1 1-2 1l-2-1 1-2c1-1 1-2 2-3 0-1-1-1-1-2l-1-1-1-1v-1c-2-2-2-3-3-5l-5 1 1-1-2-3h-1v-2c-1 0 0 0-1-1 1-1 2-2 2-4h0 6z" class="j"></path><path d="M764 269l5 2v1 3c-1-1-3-2-4-3-1 0-3-1-4-1h1l2-2z" class="V"></path><path d="M761 268c1 0 2 0 3 1l-2 2h-1c-2 0-5 0-7 2-1 0 0 0-1-1 1-1 2-2 2-4h0 6z" class="H"></path><path d="M769 272l5 5 1 2c1 3 1 6 0 9l-1 1h-2l-1-1-1-2 1-1 2 2 2-1h0c-1-2-1-4-2-5l-1-1v-1c0-1-1-2-2-3 0 0 0-1-1-1v-3z" class="J"></path><path d="M758 274c2 0 5 0 7 1l4 4c-1 0-2-1-3-2-1 1-2 1-2 2l-3-1-5 1 1-1-2-3 3-1z" class="a"></path><path d="M755 275l3-1v2l-1 2-2-3z" class="d"></path><path d="M764 279c0-1 1-1 2-2 1 1 2 2 3 2l2 2c0 1 1 2 0 3v1l-1 1 1 2c0 2-1 3-3 5 0 1-1 1-2 1l-2-1 1-2c1-1 1-2 2-3 0-1-1-1-1-2l-1-1-1-1v-1c-2-2-2-3-3-5l3 1z" class="E"></path><path d="M771 281c0 1 1 2 0 3v1l-1 1-3-4 4-1z" class="V"></path><path d="M764 279c0-1 1-1 2-2 1 1 2 2 3 2l2 2-4 1-3-3z" class="b"></path><path d="M371 419l4-7c0-1 1-1 1-2h1l-4 9 4 1v2 2h0 0c1 2 1 4 1 6h1l2-2 2 2v1h-2l-1 1c1 1 1 1 1 2 1 2 2 3 4 5l2 3-1 2 3 3 1-1v-1l1 1h2v1 2c2 1 3 1 4 0l2-2c3 0 5-2 7-4s4-3 7-4c5-3 10-6 16-3l1-1c1 1 2 1 3 1l1 1h0c1 1 1 1 2 1 0 2-1 5-2 7h0l-1 1c-1 1-2 2-3 2-2 2-4 3-5 4l-2 1v2c-1 0-4 1-5 2-7 1-13 2-19 0l-9-3v3l-7-4h0l-5-3-2-2h1l-1-1h-1c-2-3-5-7-6-11h-3v-2c-2-2-2-2-4-2l-1-3 1-1-1-3v-1c2 1 3 2 5 3h1v-1c1-1 1-1 1-2 1-1 2-3 3-5z"></path><path d="M378 435c0-2-1-4-2-6 0-2 0-3 1-5h0c1 2 1 4 1 6h1l2-2 2 2v1h-2l-1 1c1 1 1 1 1 2-1 0-2 0-3 1z" class="Q"></path><path d="M378 435c1-1 2-1 3-1 1 2 2 3 4 5l2 3-1 2v-1c-2 0-3 0-4-1v-2-1h-1c-1-1-2-3-3-4z" class="D"></path><path d="M423 453v2c-1 0-4 1-5 2-7 1-13 2-19 0 0-1 1-1 1-2 8 1 16 1 23-2z" class="G"></path><path d="M430 435c1 1 2 1 3 1l1 1h-1v1c-5 5-14 8-20 12l9-1v1c-2 0-8 1-10 0v-1-1c1-2 4-3 6-4 4-1 11-4 14-7l-3-1 1-1z" class="M"></path><path d="M371 419l4-7c0-1 1-1 1-2h1l-4 9c-1 6-4 11-1 17 5 8 16 16 24 18l4 1c0 1-1 1-1 2l-9-3v3l-7-4h0l-5-3-2-2h1l-1-1h-1c-2-3-5-7-6-11h-3v-2c-2-2-2-2-4-2l-1-3 1-1-1-3v-1c2 1 3 2 5 3h1v-1c1-1 1-1 1-2 1-1 2-3 3-5z" class="R"></path><path d="M378 450c3-1 5 2 8 3 1 1 1 0 2 0l2 1v3l-7-4h0l-5-3zm-16-22l4 3c2 2 2 3 3 5h0-3v-2c-2-2-2-2-4-2l-1-3 1-1z" class="J"></path><path d="M609 595c2-3 3-5 6-8l2 1v4c2 2 2 3 3 5 1 15 4 26 14 37l6 7c1 1 3 3 3 4 4 3 7 5 11 7 1 1 2 2 3 2l2 1c1 1 1 1 2 1l5 1c3 1 13 2 15 4 0 1 0 3-1 4v1h-4l-1 1-3-1h-1-1-1l-1-1-2 1c-5 0-12-3-16-5-5-2-10-5-14-8l-4-3v-3c-4-3-6-9-11-10-5-7-9-15-12-23v-3h0l-1-2s-1 0-2-1h0l1-2c0-2-1-3-1-4v-1c0-2 0-4-1-6h3 1z" class="g"></path><path d="M632 647c2 1 5 3 8 4 1 0 2 2 3 2h0c-1-1-1-2-3-3h0 0c2 0 3 0 4 1v3l1 1h-1-1c-2-2-4-3-7-3v1l-4-3v-3z" class="S"></path><path d="M636 653v-1c3 0 5 1 7 3h1 1l2 1 1 1 2 4c-5-2-10-5-14-8z" class="B"></path><path d="M654 652c1 1 2 2 3 2l2 1c1 1 1 1 2 1 1 1 1 2 2 3v2 1h-1c-2 0-5-1-7-3l-2-1c-1 0-2-1-2-2-1-1-1-1 0-2s2-1 2-1l1-1z" class="K"></path><path d="M617 592c2 2 2 3 3 5 1 15 4 26 14 37l6 7c1 1 3 3 3 4-11-10-19-20-24-34-1-1-2-1-3-2v-1h-2v-2c0-1-1-2-1-3 1 0 0-2 1-3v-1h3v-7z" class="D"></path><path d="M617 599l2 12c-1-1-2-1-3-2v-1h-2v-2c0-1-1-2-1-3 1 0 0-2 1-3v-1h3z" class="W"></path><path d="M609 595c2-3 3-5 6-8l2 1v4 7h-3v1c-1 1 0 3-1 3-1-1-1-1-1-2h-1v1c0 1-1 1-1 2s1 1 1 2 0 2-2 3h0v2h0l-1-2s-1 0-2-1h0l1-2c0-2-1-3-1-4v-1c0-2 0-4-1-6h3 1z" class="C"></path><path d="M605 595h3 1c-1 4-1 10-1 14 0 0-1 0-2-1h0l1-2c0-2-1-3-1-4v-1c0-2 0-4-1-6z" class="D"></path><path d="M341 677c0-7-1-14-1-21 9-2 18-3 26-6 14-6 26-15 32-30 1-1 1-2 2-3v1l1 1h1v-1c0-1 0-1-2-2l1-1c2-7 3-14 3-21 0-4-1-7-1-10h1l1 1v1c0 2 1 2 2 3h1c1 1 1 1 1 3h1l1 1 1 2c1 2 1 4 2 5l-1 5c-1 7-2 13-6 20-1 2-1 4-3 6-9 18-25 28-44 34-1 1-2 1-2 1h-1l-14 3 1 8-1 1v-1h-2z" class="Z"></path><path d="M363 655h2l-1 1h1v1h-1s1 0 1 1c-2 1-5 3-8 3-1 0-2 0-3 1l-1 1-2 1h-5c-1-2-1-2-1-4 1-3 2-1 4-2 0 0 1-1 2-1h3v1c1-1 1-1 2-1s2 0 3-1c1 0 2 0 3-1h1z" class="K"></path><path d="M358 666l-1-1c1 0 2-1 4-1h0c1 0 1-1 2-1l5-2 4-2 4-1c4-3 8-5 12-8h0l1-1v-1c-1 0-2 0-3 1-2 1-4 3-6 3l12-9c3-3 4-6 7-8 2-2 3-4 5-4-9 18-25 28-44 34-1 1-2 1-2 1z" class="P"></path><path d="M593 400c1-1 2-1 3-2l1 1c1 1 1 0 3 1l1 1c1 2 4 4 6 4l9 4v1c1-2 2-2 3-3 3 1 4 2 6 5 2 1 2 0 4 0l3-6-1 4c1 1 1 1 3 1v1c1 1 1 1 3 1v1h0l3 3c-1 0-1 0-2 1h3c0 1-1 2 0 3h1l1 1c1 1 2 3 1 4h-2l2 2v1c-1 2-2 5-4 6-3 1-4 5-6 7 0 1 0 2-1 3l-8-6c-1 1-2 2-4 1l-1-1-2 1-3-2-6-4-3-3-3-2c-2-1-4-3-6-5h-1v-4c1-4 0-8 1-13l-1-1h3 0c-1-1-2-2-3-2s-2-1-2-2v-1l-1-1z"></path><path d="M615 427c2-1 3-2 5-3s3-1 5-2v-1l1 1c-1 1-1 1-1 2-3 3-11 6-15 5h-2l-1-1 2-2h4 1l-1 1 1 1h0l1-1h0z" class="G"></path><path d="M626 416c-1 2-1 4-3 5h-1c0 1-1 2-2 2-2 1-3 0-5 3v1h0l-1 1h0l-1-1 1-1h-1-4-2c-4-1-7-3-9-6-1 0 0-1 0-1 2 2 4 5 8 5 4 1 10-1 14-4l3-2c1-1 2-1 3-2z" class="U"></path><path d="M632 406l-1 4c1 1 1 1 3 1v1h-1v1c-2 3-3 5-6 7 0 1-1 1-1 2l-1-1v1c-2 1-3 1-5 2s-3 2-5 3v-1c2-3 3-2 5-3 1 0 2-1 2-2h1c2-1 2-3 3-5 1-1 2-2 3-4l3-6z" class="M"></path><path d="M619 407c3 1 4 2 6 5 2 1 2 0 4 0-1 2-2 3-3 4s-2 1-3 2l-1-2c-2-2-3-4-6-6 1-2 2-2 3-3z" class="G"></path><path d="M622 416l1-1-2-2v-1c0-1-1-2-1-3 3 1 2 2 3 4 1 0 1 0 2-1 2 1 2 0 4 0-1 2-2 3-3 4s-2 1-3 2l-1-2z" class="b"></path><path d="M633 413v-1h1c1 1 1 1 3 1v1h0l3 3c-1 0-1 0-2 1l-1 1-2 3v-3h-1c-2 0-4 3-6 4-1 1-2 1-3 1 0-1 0-1 1-2 0-1 1-1 1-2 3-2 4-4 6-7z" class="J"></path><path d="M633 413v-1h1c1 1 1 1 3 1v1h0l-1 3c-1 1-2 0-3 0v-4z" class="R"></path><path d="M635 422l2-3 3 2c0 1-1 3-1 4-1 1-2 2-2 4l-3 3h-1c-2-1-4-1-5-2 3-2 5-5 7-8z" class="B"></path><path d="M594 401l4 2c1-1 1-1 1-2l1 1h0c0 4 1 6 3 9 4 4 9 6 14 9-1 1-1 0-2 0-5 0-10-4-14-8l-4-5-1-1h3 0c-1-1-2-2-3-2s-2-1-2-2v-1z" class="U"></path><path d="M637 429c0 3-2 5-3 7-1 1-4 4-5 4-1-1-1-1-2-1l-4-2v-1c0-1-1-1-1-1v-1h1l5-4c1 1 3 1 5 2h1l3-3z" class="J"></path><path d="M628 430c1 1 3 1 5 2h-1c-1 0-2 1-3 1h0v1h-6l5-4z" class="S"></path><path d="M637 429c0 3-2 5-3 7-1 1-4 4-5 4-1-1-1-1-2-1 2-1 2-2 3-4l-1-2h0c1 0 2-1 3-1h1 1l3-3z" class="N"></path><path d="M638 418h3c0 1-1 2 0 3h1l1 1c1 1 2 3 1 4h-2l2 2v1c-1 2-2 5-4 6-3 1-4 5-6 7 0 1 0 2-1 3l-8-6c-1 1-2 2-4 1l-1-1-2 1-3-2-6-4-3-3c3 0 5 1 8 3l9 3 4 2c1 0 1 0 2 1 1 0 4-3 5-4 1-2 3-4 3-7 0-2 1-3 2-4 0-1 1-3 1-4l-3-2 1-1z" class="X"></path><path d="M609 434c2 0 3 0 5 1 4 1 7 2 11 4-1 1-2 2-4 1l-1-1-2 1-3-2-6-4z" class="I"></path><path d="M638 418h3c0 1-1 2 0 3h1l1 1c1 1 2 3 1 4h-2l2 2v1l-2-1c-1 1-1 2-1 3l-1 1c-1 0-1 1-1 2-1 1-2 1-3 2s0 1-1 1-1 0-1-1c1-2 3-4 3-7 0-2 1-3 2-4 0-1 1-3 1-4l-3-2 1-1z" class="h"></path><path d="M775 268l2 1v-1h1v1l2-1c1 1 2 1 3 2h0c1 0 1 1 2 1v4l-1 35c-1 2-1 3-2 4l2 2c-1 1-1 1-1 2l-3 3-4 2c-1-1-2-2-3-2l-6 2c-2-1-4-2-5-2h-2c-2-1-3-1-5-1-5 0-3-2-5-4v-1-2c-1-1-2-1-3-2v-2c0-3 1-5 1-7l1-4c1-1 0-3 2-5v1c2 0 3-1 4-2l-1-2h1c2 0 2-1 4-1h0l1-1 1-1c1 0 1 0 1 1l1 2 2 1-1 2 2 1c1 0 2 0 2-1 2-2 3-3 3-5l1 1h2l1-1c1-3 1-6 0-9l-1-2-5-5v-1c1 1 2 1 3 1l1-2c-1 0-2-1-2-2h4z" class="D"></path><path d="M780 274c0-1 0-1 1-1l2 2c-1 1-1 2-1 3l1 1h0l1 2h0c-1-1-2-1-3-1h-1c-1-2 0-4 0-6z" class="j"></path><path d="M773 321c3-2 6-4 9-7l2 2c-1 1-1 1-1 2l-3 3-4 2c-1-1-2-2-3-2z" class="C"></path><path d="M775 268l2 1v-1h1v1c1 2 2 1 3 3l-1 1-2-2h-1c-1 1-1 1 0 2h1c1 0 1 0 2 1 0 2-1 4 0 6h1c1 0 2 0 3 1 0 2 0 2-1 4h-2l-1-2-2-5-3 1-1-2-5-5v-1c1 1 2 1 3 1l1-2c-1 0-2-1-2-2h4z" class="J"></path><path d="M773 270c2 2 4 5 5 8l-3 1-1-2-5-5v-1c1 1 2 1 3 1l1-2z" class="W"></path><path d="M769 303l2 1h-1c2 1 3 2 5 3 1 0 1 0 1 1v1c2 0 2 0 3-1l1 1v1c-1 1-2 2-3 4l-1-1-1 1c-1 0-1 1-2 1l-2 2c-1 0-2 1-3 1l-2 1h-2c-1 0-4 0-6-1-2-2-2-3-3-5v-5c1-2 1-2 3-4h6c2 1 3 0 4 0l1-1z" class="b"></path><path d="M773 311c0 1 1 2 2 3-1 0-1 1-2 1l-2 2-3-1c1-1 0-1 1-2 1 0 1-1 1-2 1 0 2-1 3-1z" class="F"></path><path d="M770 304c2 1 3 2 5 3 1 0 1 0 1 1v1c2 0 2 0 3-1l1 1v1c-1 1-2 2-3 4l-1-1-1 1c-1-1-2-2-2-3-1 0-2 1-3 1v-3c0-2-1-3-1-4l1-1z" class="Q"></path><path d="M770 309c2-1 2-1 3 0l1 1-1 1c-1 0-2 1-3 1v-3z" class="D"></path><path d="M778 278l2 5 1 2h2c-2 2-2 4-3 6l1 1v2 2 1l-1 1v1l-1 1v1c1 2 1 5 0 7-1 1-1 1-3 1v-1c0-1 0-1-1-1-2-1-3-2-5-3h1l-2-1 1-1-1-2c-3 1-6 2-9 1s-6-3-9-7c2 0 3-1 4-2l-1-2h1c2 0 2-1 4-1h0l1-1 1-1c1 0 1 0 1 1l1 2 2 1-1 2 2 1c1 0 2 0 2-1 2-2 3-3 3-5l1 1h2l1-1c1-3 1-6 0-9l3-1z" class="E"></path><path d="M780 283l1 2h2c-2 2-2 4-3 6-2 3-3 5-6 6 3-4 5-8 6-14z" class="Y"></path><path d="M774 297c3-1 4-3 6-6l1 1v2 2 1l-1 1-1-1-3 3c0 1 0 1-1 1-1 1-3 2-4 3l-2-1 1-1-1-2c2-1 3-2 5-3z" class="Q"></path><path d="M771 304c1-1 3-2 4-3 1 0 1 0 1-1l3-3 1 1v1l-1 1v1c1 2 1 5 0 7-1 1-1 1-3 1v-1c0-1 0-1-1-1-2-1-3-2-5-3h1z" class="E"></path><path d="M760 288l1-1c1 0 1 0 1 1l1 2 2 1-1 2 2 1c1 0 2 0 2-1 2-2 3-3 3-5l1 1h2l1-1c-2 5-4 7-8 10-2 0-4 0-6-1-3-1-4-3-6-5l-1-2h1c2 0 2-1 4-1h0l1-1z" class="e"></path><path d="M761 297c1 0 1-1 2-1v-2c2 0 2 0 3 1 0 1 0 2 1 3-2 0-4 0-6-1z" class="V"></path><path d="M826 244l-3 6c-5 8-12 17-14 27v12l-1 18 1 12c0 1 0 4 1 4 0 1 1 2 2 3l2 2c1-1 2-1 4-1 0 1 0 2 1 2 0 1 1 0 1 1l2 1c0 1 1 2 2 3-1 1-1 2-1 3-2 1-3 3-3 5v1h-1c-1 0-2 1-3 0l-1 1c-1 1 0 1-1 2l1 1v2 1 4c-1 0-1 1-3 1h0-3l-2-2h0c-2 1-2 1-3 0l-1-13h0l-3-3 2-1v-1c-1-2-2-2-2-4-1-1-1-1-2-1l-1 1v-6c0-1 1-3 1-5-1-3-1-10 0-14v-1l-1-31v-6-3h1v-1c1-1 2-1 4-1 0 0 1 0 1 1s-1 1 0 2l2-3 1 1c4-4 7-8 11-12 3-3 6-6 9-8z"></path><path d="M810 342c0-3-1-6-2-9h0c1-2 0-4 1-5v-1l-1-1-2-9c2-4 0-7 2-10l1 12c0 1 0 4 1 4 0 1 1 2 2 3l2 2-1 1-1-1c-1 1-1 1-1 3l1 1c0 1-2 1-1 3 0 1 1 4 0 5l-1 2z" class="L"></path><path d="M814 328c1-1 2-1 4-1 0 1 0 2 1 2 0 1 1 0 1 1l2 1c0 1 1 2 2 3-1 1-1 2-1 3-2 1-3 3-3 5v1h-1c-1 0-2 1-3 0l-1 1c-1 1 0 1-1 2l1 1v2 1 4c-1 0-1 1-3 1h0-3l-2-2c1 0 2-1 2-1 1-3 1-6 1-8v-2l1-2c1-1 0-4 0-5-1-2 1-2 1-3l-1-1c0-2 0-2 1-3l1 1 1-1z" class="Y"></path><path d="M814 328c1-1 2-1 4-1 0 1 0 2 1 2 0 1 1 0 1 1v4h-2l-2 2v-6c-1-1-1-1-3-1l1-1z" class="I"></path><path d="M820 330l2 1c0 1 1 2 2 3-1 1-1 2-1 3-2 1-3 3-3 5v1h-1c-1 0-2 1-3 0s-1-1-1-2v-1l1-1v-3l2-2h2v-4z" class="D"></path><path d="M820 330l2 1c0 1 1 2 2 3-1 1-1 2-1 3-2 1-3 3-3 5v1h-1c-1-3 0-4 1-6v-3-4z" class="e"></path><path d="M802 263s1 0 1 1-1 1 0 2l2-3 1 1-2 2c-3 4 0 10-1 14l1 24c0 3 0 6-1 8v28h0l-3-3 2-1v-1c-1-2-2-2-2-4-1-1-1-1-2-1l-1 1v-6c0-1 1-3 1-5-1-3-1-10 0-14v-1l-1-31v-6-3h1v-1c1-1 2-1 4-1z" class="P"></path><path d="M798 306v1c1 2 1 4 1 6v14c1 1 1 1 3 1h0v-2-1h0l1-1v-5-7 28h0l-3-3 2-1v-1c-1-2-2-2-2-4-1-1-1-1-2-1l-1 1v-6c0-1 1-3 1-5-1-3-1-10 0-14z" class="B"></path><path d="M802 263s1 0 1 1-1 1 0 2l2-3 1 1-2 2c-3 4 0 10-1 14h-1v-8c-1 0 0 0 0-1-1-1-1-1-1-2v-1c-1 0-1 0-2 1v7 22c0 2 0 5-1 7l-1-31v-6-3h1v-1c1-1 2-1 4-1z" class="U"></path><path d="M674 467c6 1 14 5 19 10 4 4 8 13 8 20-1 5-5 11-8 14-3 1-8 1-10 0-5-1-9-4-13-7h0c4-1 10-5 12-8s0-9 0-12c0-2 1-4 1-6h0c-1 2-2 4-2 7-1 6-1 11-8 14-1 0-3 1-4 1h-3c-2 0-4-1-5-2-4-2-6-7-7-12l1-6 1-1 1-2c3-6 11-8 17-10z"></path><path d="M470 240h47l125 1 22 1c4 0 9-1 12 0v3H554 412h-48-11-7v-3c6-1 11-1 16-1h27l81-1z" class="l"></path><path d="M574 449v-2c2 0 2 0 3 1l2 1c1 1 2 1 3 2 2 2 4 4 7 4l1 1 1 1v-3l1-1c1 0 2 0 4 1v-1c1 0 3 1 4 1 5 1 9 2 13 2 1-1 5-1 6-1 2 1 5 0 8-1l1 1v1c1-1 1-1 2-1h1c1-1 0-1 1-2h1l2 1c2-1 3-2 4-3 1 0 2-1 3-2l4 1c1-1 1-2 2-3l3 1 1 1-2 2h1l1-1c1-1 2-2 4-2v1h-1c-1 1-2 2-2 3-1 2-2 3-3 4 0 2-2 3-4 5h3c-1 1-2 2-4 3-1 0-1 1-2 2s-2 2-4 2h0l-1 1h1c-1 1-1 2-3 2h-1c-1 1 1 3-1 3h-1l-1-1c-2 1-3 3-5 4h0c-1 1-1 1-1 2-2 0-2 0-4-1h-1c3-3 7-5 9-8l-2 1c-1 0-2 0-3 1-1 0-1 1-2 2h-1l-1-2c-1 2-3 2-5 2h-1l-5 2-4 1h-1c-3 0-4 0-6-1h-3-2l1 1c1 1 1 1 1 2h-2l-1-2-1-1-2 1c-1 0-2-1-3-1l-2-2v-1l-2-1-1-1h0c0-2-1-2-2-3-4-1-7-3-10-6l1-1 1 1h0l-4-5c4 2 7 5 10 6 2 1 3 3 5 3v-1c-1-1-3-2-5-3v-3c-3-2-5-4-7-6l-2-3v-1c1 1 2 2 3 2h1l1-2z" class="n"></path><path d="M642 449l4 1c-2 2-3 4-5 5l-2-4c1 0 2-1 3-2z" class="W"></path><path d="M635 454c2-1 3-2 4-3l2 4h-1c-2 2-3 5-6 5h0c1-1 2-2 2-3l2-2c-2 1-5 1-7 1l4-2z" class="Z"></path><path d="M592 464c2 1 5 2 8 3 2 1 6 1 8 2h5c-6 2-14 0-20-2-1-1 0-1-1-1l-1-1 1-1z" class="P"></path><path d="M635 471l2-3h-1v-1l4-4h3 0c1 0 1-1 2-1 0-1 0-1 1-1h3c-1 1-2 2-4 3-1 0-1 1-2 2s-2 2-4 2h0l-1 1h1c-1 1-1 2-3 2h-1z" class="g"></path><defs><linearGradient id="AL" x1="578.075" y1="459.491" x2="590.755" y2="463.103" xlink:href="#B"><stop offset="0" stop-color="#a09f9d"></stop><stop offset="1" stop-color="#bebcbd"></stop></linearGradient></defs><path fill="url(#AL)" d="M571 453h3c2 1 4 3 6 4 3 3 8 5 12 7l-1 1 1 1c1 0 0 0 1 1-6-2-10-5-15-8-3-2-5-4-7-6z"></path><defs><linearGradient id="AM" x1="591.592" y1="457.141" x2="614.878" y2="455.832" xlink:href="#B"><stop offset="0" stop-color="#0c0b0b"></stop><stop offset="1" stop-color="#3a3939"></stop></linearGradient></defs><path fill="url(#AM)" d="M619 455c2 1 5 0 8-1l1 1v1c1-1 1-1 2-1h1c1-1 0-1 1-2h1l2 1-4 2-8 3c-1 0-1 0-1 1h-3-1c-2 1-5 1-8 1-7 0-13-1-19-4v-3l1-1c1 0 2 0 4 1v-1c1 0 3 1 4 1 5 1 9 2 13 2 1-1 5-1 6-1z"></path><path d="M619 455c2 1 5 0 8-1l1 1v1c1-1 1-1 2-1h1c1-1 0-1 1-2h1l2 1-4 2-8 3c-1 0-1 0-1 1h-3v-3h-3c-2 1-4 0-6 1h-6c-2-2-5-3-8-4v-1c1 0 3 1 4 1 5 1 9 2 13 2 1-1 5-1 6-1z" class="a"></path><path d="M619 457h2l1 3h-3v-3z" class="J"></path><path d="M582 471h0c0-2-1-2-2-3-4-1-7-3-10-6l1-1 1 1h0l-4-5c4 2 7 5 10 6 2 1 3 3 5 3v-1c-1-1-3-2-5-3v-3c5 3 9 6 15 8s14 4 20 2c6 0 13-3 19-5l-5 4c0 1-1 1-1 2-1 0-5 1-5 2-1 2-3 2-5 2h-1l-5 2-4 1h-1c-3 0-4 0-6-1h-3-2l1 1c1 1 1 1 1 2h-2l-1-2-1-1-2 1c-1 0-2-1-3-1l-2-2v-1l-2-1-1-1z" class="Q"></path><path d="M587 468c6 3 12 4 19 5-2 0-3 0-4 1h0l-1 1-5-2c-2 0-8-2-9-4v-1z" class="E"></path><path d="M606 473c4 0 9 0 13-2l8-3c0 1-1 1-1 2-1 0-5 1-5 2-1 2-3 2-5 2h-1l-5 2-4 1h-1c-3 0-4 0-6-1h2v-1l1-1h0c1-1 2-1 4-1z" class="C"></path><path d="M602 474c4 1 9 0 13 0l-5 2-4 1h-1c-3 0-4 0-6-1h2v-1l1-1z" class="V"></path><path d="M583 472l1-1-1-3h-1l1-1c2 0 2 0 3 1h1v1c1 2 7 4 9 4l5 2v1h-2-3-2l1 1c1 1 1 1 1 2h-2l-1-2-1-1-2 1c-1 0-2-1-3-1l-2-2v-1l-2-1z" class="Y"></path><path d="M585 473c3 1 8 1 11 3h-2l1 1c1 1 1 1 1 2h-2l-1-2-1-1-2 1c-1 0-2-1-3-1l-2-2v-1z" class="f"></path><path d="M683 621l-10-3c-3-1-7-1-10-3-2-1-4-5-5-8-2-5 1-13 4-18 2-4 7-7 11-8 6-1 16 1 21 5l3 3c1 0 2 1 2 1 4 4 5 8 6 14v1c1 7 0 16-4 22-3 4-7 6-11 8-4-1-7 0-10 0h-1c2-1 2-1 3-1l-1-1 1-1h-1c2-1 3-2 5-3h1l1-2c2 0 4-2 6-4 2-3 1-6 0-10h0c-3-3-6-4-10-5h-1c-1 0-2-1-3-2v1l1 1c-2 3-2 4-2 8 2 1 3 3 4 4v1z" class="K"></path><path d="M680 607l1 1c-2 3-2 4-2 8 0 0-2-1-3-2s-1-2-1-4c1-2 3-2 5-3z"></path><path d="M680 606c1 0 1-2 2-2h0l1 1-1 1h0 7l1 1h1 0l2-1c2 2 5 5 7 8l-2 1h0v4l-1-1v-2c-1-2-1-2-3-3h0c-3-3-6-4-10-5h-1c-1 0-2-1-3-2z" class="B"></path><defs><linearGradient id="AN" x1="693.54" y1="615.108" x2="686.83" y2="633.614" xlink:href="#B"><stop offset="0" stop-color="#c5c4c5"></stop><stop offset="1" stop-color="#eeeceb"></stop></linearGradient></defs><path fill="url(#AN)" d="M694 613c2 1 2 1 3 3v2l1 1-1 1c1 0 1 0 1 1 1 0 1 0 2 1 0 1-1 2-2 3h-1l-4 4c-2 1-3 1-4 2-3 1-5 1-7 1h-1c2-1 3-2 5-3h1l1-2c2 0 4-2 6-4 2-3 1-6 0-10z"></path><path d="M693 606l-8-3c1-1 2-2 2-3 0-2 0-3-2-4-5 1-8 13-14 8-2-1-3-3-3-5s0-3 2-4c2-3 8-5 12-5 6 1 12 6 16 10 2 4 4 9 3 13v2l-1-1c-2-3-5-6-7-8z"></path><path d="M364 436h2 3c1 4 4 8 6 11h1l1 1h-1l2 2 5 3h0l7 4v-3l9 3c6 2 12 1 19 0 1-1 4-2 5-2v-2l2-1c1-1 3-2 5-4 1 0 2-1 3-2l1-1h0 1l1-1c1-1 1-5 3-6v3c1-1 1-2 2-2h0c1 1 1 2 1 3 2 0 3 1 4 1l-1 1h1c2-1 2-2 4-2 1-1 2-2 3-4h0v2c-1 1-1 1-1 2-2 2-4 4-5 6v2c-1 1-3 2-3 3-2 2-3 3-5 4-1 1-1 2-2 2l-1 1c-1 1-3 2-4 2 2 0 3 1 4 1l3-2c1 1 3 1 5 1l1-1c2-1 3-2 4-3h1c0 1 1 1 0 2-1 2-4 4-6 5h0c-6 4-13 7-20 9l1 1c-3 1-6 2-9 2-2 0-3 0-5-1 0-1-5-2-6-3v-1c-1 0-1-1-2-2h0c-1 0-2-1-3-2l-8-3h-1c-1 2 0 4 0 6h-1c-1-1-2-1-4-2-1 0-3-1-5-1-3-2-6-3-8-5-3-2-3-4-5-6-1-1-2-1-3 0l-1 2c-3-2-5-3-7-6v-1c-1-2-2-4-2-7h1c-1-1-1-1-1-2v-1l4 2h5l-3-3v-2l2-1-1-2h2z" class="n"></path><path d="M409 469c3 0 5 0 8-1h2v-1c2-1 4-2 7-3 2 0 4-1 6-2 2 0 3 1 4 1-6 4-12 6-19 6-3 1-5 1-8 0z" class="K"></path><path d="M390 454l9 3c6 2 12 1 19 0-2 2-3 1-6 2l1 1h-3 0c-6 1-14-1-20-3v-3z" class="e"></path><defs><linearGradient id="AO" x1="437.854" y1="455.047" x2="422.854" y2="447.063" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#373534"></stop></linearGradient></defs><path fill="url(#AO)" d="M436 444c1-1 1-5 3-6v3c1-1 1-2 2-2h0c1 1 1 2 1 3 2 0 3 1 4 1l-1 1v1c-2 1-4 3-6 5-8 6-18 12-28 11l-1-1h0 3l-1-1c3-1 4 0 6-2 1-1 4-2 5-2v-2l2-1c1-1 3-2 5-4 1 0 2-1 3-2l1-1h0 1l1-1z"></path><path d="M436 444h1 2c0-1 1-1 2-1h0c-1 3-3 4-6 6l-5 1 3-2c1-1 1-2 2-3l1-1z" class="D"></path><path d="M450 458c0 1 1 1 0 2-1 2-4 4-6 5h0c-6 4-13 7-20 9l1 1c-3 1-6 2-9 2-2 0-3 0-5-1 0-1-5-2-6-3v-1c-1 0-1-1-2-2h0c-1 0-2-1-3-2 3 0 6 1 9 1 3 1 5 1 8 0 7 0 13-2 19-6l3-2c1 1 3 1 5 1l1-1c2-1 3-2 4-3h1z" class="E"></path><path d="M405 472c6 3 11 3 17 2h2l1 1c-3 1-6 2-9 2-2 0-3 0-5-1 0-1-5-2-6-3v-1zm-5-4c3 0 6 1 9 1 3 1 5 1 8 0l4 1h-1l-2 2h0c-4 1-12 0-15-2h0c-1 0-2-1-3-2z"></path><defs><linearGradient id="AP" x1="425.974" y1="472.993" x2="437.779" y2="459.293" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#2e2d2c"></stop></linearGradient></defs><path fill="url(#AP)" d="M450 458c0 1 1 1 0 2-1 2-4 4-6 5h0l-1-1c-9 4-16 7-25 8l2-2h1l-4-1c7 0 13-2 19-6l3-2c1 1 3 1 5 1l1-1c2-1 3-2 4-3h1z"></path><path d="M364 436h2 3c1 4 4 8 6 11h1l1 1h-1l2 2 5 3h0c-1 1-1 2-1 4h-1l-1 1 1 1v1c-2-1-4-2-5-4 0-1 2 0 4-1-1-1-1-1-1-2-1 1-2 1-3 1s-1-1-2-2c0-1-1-2-2-3h-2v1l2 2v1h0c-1-1-3-2-4-2v1h0c1 1 3 3 4 5h-1c-2-1-4-3-5-4-2-1-3-1-4-2h-1v-1c-1 1-2 1-3 1l-1 1c-1-2-2-4-2-7h1c-1-1-1-1-1-2v-1l4 2h5l-3-3v-2l2-1-1-2h2z" class="g"></path><path d="M355 442l4 2c3 2 5 2 8 2l4-3 2 3c-1 0-2 0-3-1v3c-1 0-2 0-2-1h-4v1h2l1 1-2 1h0c-1-1-2-2-3-2l-1 1 1 1-1 1v-1c-1 1-2 1-3 1l-1 1c-1-2-2-4-2-7h1c-1-1-1-1-1-2v-1z" class="E"></path><path d="M358 451l-1-1c0-1 0-2 1-3h1c1 1 2 2 2 3-1 1-2 1-3 1z" class="g"></path><path d="M364 436h2 3c1 4 4 8 6 11h1l1 1h-1c-1 0-2-1-3-2l-2-3-4 3c-3 0-5 0-8-2h5l-3-3v-2l2-1-1-2h2z" class="I"></path><path d="M325 703h1l2 1h0 2l2 1h2c3 3 5 4 8 6l3 3 4 4 3-1 4 3c-2 0-2 1-4 2l5 4c1 1 2 1 2 2 2 1 3 1 4 2 2 1 2 2 4 3l3 3c1 1 1 2 2 3s1 6 2 8v-1l3-1 2 1v1c1 0 1 0 2 1l-2-1c-1 1-1 1-1 2-1 1-2 1-3 2l1 1h0c0 1 0 1-1 1v1h0 1 0c1 0 2-1 3-1 0-1 1-1 1 0 2 0 2 1 3 3l1 1v1l-2 1c0 2 1 2-1 4h1v1l-1 1h3c1 0 1-1 2-2h0l1-1 1 1c-1 1-2 1-2 2h-1c-1 1-1 1-2 3l-2 2c1 0 3 2 3 2 0 1 0 1 1 2 1 0 2 1 3 2-1-1-2-1-3-1l-1-1c-1-1-2-1-3-2-2-1-3-2-5-2-2-1-5-3-7-5l-12-7c-4-3-7-6-11-8l-22-18h-1l-1-1h1c1 0 2-1 2-2h1c-1-2-2-3-3-5 0-1-2-2-4-3 1-1 2-1 3-1h3v-2h-1c-2-1-2-1-2-2l1-2c1-1 2-2 4-3-1-1-2-1-3-1v-2-1-1l1-3z" class="D"></path><path d="M374 746l3-1 2 1v1c1 0 1 0 2 1l-2-1c-1 1-1 1-1 2-1 1-2 1-3 2-3 1-4 5-7 6l-1-1c3-2 5-5 6-8l1-1v-1z" class="h"></path><path d="M360 740l1 1c1 1 2 1 4 1v-2l1-1h0l1-1c1 1 1 1 2 3h0c1 1 1 4 0 5v1h0c-1 2-2 4-4 5h-3 0c-1-1-1-1-2-1-1-1-2-2-3-2l-3-3h3c1-1 2-2 2-3s0-1 1-2v-1z" class="F"></path><path d="M355 727c0 1 0 2 1 2h0c3 1 5 4 6 6-1 1-2 3-2 5v1c-1 1-1 1-1 2s-1 2-2 3h-3c-2-1-4-3-6-3v-1c-2 0-2-1-3-2l4 1c2 1 3 2 4 3s2 1 3 0c0-1-1-2-2-3h-1c-2-2-4-4-4-7h1c-1-2-1-3-1-4 3 0 3 0 5 2v-1c0-1 0-2-1-2l1-1 1-1z" class="f"></path><path d="M349 730c3 0 3 0 5 2 1 1 2 2 1 4v2 2l3 1v1h-1c-1-1-2-1-3-1h-1c-2-2-4-4-4-7h1c-1-2-1-3-1-4z"></path><defs><linearGradient id="AQ" x1="356.892" y1="751.836" x2="354.779" y2="754.845" xlink:href="#B"><stop offset="0" stop-color="#9b9999"></stop><stop offset="1" stop-color="#c0bfbe"></stop></linearGradient></defs><path fill="url(#AQ)" d="M326 729l7 6 2-2 2-1 1 2 2 2h-1l-2 2 44 32c1 0 3 2 3 2 0 1 0 1 1 2 1 0 2 1 3 2-1-1-2-1-3-1l-1-1c-1-1-2-1-3-2-2-1-3-2-5-2-2-1-5-3-7-5l-12-7c-4-3-7-6-11-8l-22-18h-1l-1-1h1c1 0 2-1 2-2h1z"></path><path d="M337 732l1 2 2 2h-1l-2 2-2-1-2-2 2-2 2-1z" class="D"></path><path d="M338 734l2 2h-1l-2 2-2-1 3-3z" class="L"></path><path d="M325 703h1l2 1h0 2l2 1h2c3 3 5 4 8 6l3 3 4 4 3-1 4 3c-2 0-2 1-4 2l5 4c-1 1-2 1-3 1h1l-1 1-1 1c1 0 1 1 1 2v1c-2-2-2-2-5-2 0 1 0 2 1 4h-1c0 3 2 5 4 7l-1 1c-6-1-4-6-7-8 0-1-1-1-2-1l-1 2c1 0 0 0 2 1l-2 1c-1 0-1-1-3-1h1l-2-2-1-2-2 1-2 2-7-6c-1-2-2-3-3-5 0-1-2-2-4-3 1-1 2-1 3-1h3v-2h-1c-2-1-2-1-2-2l1-2c1-1 2-2 4-3-1-1-2-1-3-1v-2-1-1l1-3z" class="F"></path><path d="M331 720l1-1c1 1 0 2 0 3l2 1c-2 1-3 2-5 2s-4-3-6-4c2 0 3 1 5 0 1 0 2-1 3-1z" class="f"></path><path d="M327 711c1 1 1 1 3 2s6 2 7 5v1l-3 4-2-1c0-1 1-2 0-3l-1 1c-1 0-2 1-3 1-2 1-3 0-5 0h0l-1-1h3v-2h-1c-2-1-2-1-2-2l1-2c1-1 2-2 4-3z" class="U"></path><path d="M323 714l1 1h1l1 1c1 0 1-1 2-2h0c1 0 2 1 3 2 1 2 1 2 0 4-1 0-2 1-3 1-2 1-3 0-5 0h0l-1-1h3v-2h-1c-2-1-2-1-2-2l1-2z" class="Q"></path><path d="M341 723c1 0 1 0 2 1l2 2h1 1v1c1 0 1 1 1 2v1h1c0 1 0 2 1 4h-1c0 3 2 5 4 7l-1 1c-6-1-4-6-7-8 0-1-1-1-2-1l-1 2c1 0 0 0 2 1l-2 1c-1 0-1-1-3-1h1l-2-2-1-2h1 1c0-1 0-1-1-2h-1v1h-1v-2h-1v3l-2-2c2-2 1-2 3-2 2-1 4-4 5-5z" class="G"></path><path d="M346 726h1c0 2 0 4-1 6l-2-1v-2l-2-2h3v-1h1z" class="F"></path><path d="M325 703h1l2 1h0 2l2 1h2c3 3 5 4 8 6l3 3 4 4 3-1 4 3c-2 0-2 1-4 2l5 4c-1 1-2 1-3 1h1l-1 1-1 1c1 0 1 1 1 2v1c-2-2-2-2-5-2h-1v-1c0-1 0-2-1-2v-1h-1-1l-2-2c-1-1-1-1-2-1v-3l-2-1v-2h-2v2-1c-1-3-5-4-7-5s-2-1-3-2-2-1-3-1v-2-1-1l1-3z" class="S"></path><path d="M349 722c-1 0-2 1-3 0l-3-3 1-1 1 2h2 1l1 2z" class="U"></path><path d="M336 710v1c1 1 1 1 2 1 1 1 1 2 1 3v1l-4-4 1-2z" class="M"></path><path d="M330 709c2 1 3 2 5 3l4 4c2 3 5 6 7 10h-1l-2-2c-1-1-1-1-2-1v-3l-2-1v-2h-2v2-1c-1-3-5-4-7-5v-4z" class="b"></path><path d="M345 714l4 4 3-1 4 3c-2 0-2 1-4 2l5 4c-1 1-2 1-3 1-2-1-3-3-5-5l-1-2h-1c-1-2-3-3-4-4l2-2z" class="G"></path><path d="M352 717l4 3c-2 0-2 1-4 2l-3-3v-1l3-1z" class="H"></path><path d="M325 703h1l2 1h0 2l2 1 1 2-1 1c0 1 0 1 1 1l1-1c1 1 1 2 2 2l-1 2c-2-1-3-2-5-3v4c-2-1-2-1-3-2s-2-1-3-1v-2-1-1l1-3z" class="C"></path><path d="M324 707c3 1 4 1 6 2v4c-2-1-2-1-3-2s-2-1-3-1v-2-1z" class="F"></path><path d="M762 497l2-3h2c0 1 0 2 1 3v1l1 1 2 10c1-1 1-1 2-1v1c0 1 1 2 2 3 1 7 1 13 1 20a280.54 280.54 0 0 1 0 41l-5 25-2 11-12 33-4 9c-1 0-3 0-4 1s-2 3-2 4c-2 1-4 2-6 2-3-2-5-4-6-7v-1c-1-4 2-12 3-17 1-7 3-14 5-21 4-17 8-36 8-54 0-1 0-2 1-4l1 2c1-2 2-6 1-7v-1l1-1c-1-1-1-2-1-3-1-1-1-2-1-3h-1l-1-6h1v-4-8c-1-6-1-12-2-18 0-1 1-1 1-1h1v-1h2c1 0 1 1 1 1s0-1-1-2v-1c-1-1 0-1 0-2l2 1 2 2c1-1 1-3 1-4 1 1 1 1 2 1 0-2 0-2 1-4h1v2z"></path><path d="M765 572l1-1 1 2-1 1v3h-1l1 1c-1 1-1 2-1 3s0 1-1 1c0 0-1-1-2-1l-2 1h-1l1-1c1-2 2-3 4-4 1-1 1-1 1-2h0c-2 1-2 1-4 1h-1c1-1 1-1 2-1 1-2 2-2 3-3z" class="X"></path><path d="M756 560c1 0 2 1 2 1l3 2s0 1 1 1v1l-2 2c0 2 0 4 1 5s1 1 1 2h-2l-2 1-3-6h1l3-3c-1-1-2-3-3-4v-2zm1 37l-1-2c-1-2 1-1 2-2 0-1 0-2 1-3 0-1 1-1 1-1 1-2 0-2-1-3 1-1 2-2 2-3l1-1h1c1 1 1 2 2 3l-1 1-2-2v2 1l2 1c0 2 0 4-1 5h0c-1 1-2 1-4 1v2c-1 0-1 0-2 1z" class="T"></path><path d="M751 603v-1c1-2 0-3 1-5 0-1 1-2 1-2 0-4-1-7 0-10 1-1 1-2 1-3v-1h1v1l1 1v-2l1 1c2 4-2 5-2 9v2c0 1 0 1-1 1v1 1l-1 1 1 2 1 1-2 1-1 2h-1z" class="M"></path><path d="M753 624c-1 2-2 3-3 4l-1-1c-2-4-3-9-3-13h1c2 2 3 6 4 9 2-3 4-5 6-8h0l1 1c-1 3-3 6-5 8z" class="W"></path><path d="M737 633l1 2c-1 5-3 11 0 16 1 2 2 3 3 3 2 0 3 0 5-1 3-4 6-10 8-15v5l1-1h1l-4 9c-1 0-3 0-4 1s-2 3-2 4c-2 1-4 2-6 2-3-2-5-4-6-7v-1c-1-4 2-12 3-17z" class="T"></path><path d="M752 628h1v1c0 1-2 3-2 5l-4 10c0 2-1 3-2 3l-2-2-3-4c1-5 1-7 5-11 2 1 4 0 6-1l1-1z" class="U"></path><path d="M745 633c1 0 2 2 3 3 0 1 1 2 0 3 0 2-1 3-3 4-1 0-1 1-2 0-1-2-1-4-1-6 0-1 1-2 3-4z"></path><path d="M751 554l1 2c1 1 1 2 0 4v11 1c-1 1 0 4-1 6 0 4-1 9-2 13-1 8-4 15-5 23-1 4-2 8-3 13-1 2-2 5-3 8l-1-2c1-7 3-14 5-21 4-17 8-36 8-54 0-1 0-2 1-4z" class="R"></path><path d="M765 597l1-1v-2h0c1-2 1 0 1-2 0-1 1-1 1-2l-7 27-1-2-2 1-1-1c0-1 0 0-1-1-1 1-1 1-2 1s-1 1-2 2l-1-1v-3c1-1 1-1 0-2 0-1-1-2-1-3s0-3 1-5h1l1-2 3 3v-3h0c1-1 1-3 2-4h-1c1-1 1-1 2-1v-2c2 0 3 0 4-1h0c0 1 0 2 1 3 0 1 1 1 1 1z" class="F"></path><path d="M755 606h0c2 0 3-1 4-1v-1l1-1 1 1v1c-2 1-2 4-4 5h-3c1-1 2-1 2-3l-1-1z" class="B"></path><path d="M751 603h1c0 1 1 3 2 4l1-1 1 1c0 2-1 2-2 3l-1 1h-2c0-1-1-2-1-3s0-3 1-5z" class="P"></path><path d="M763 593c0 1 0 2 1 3 0 1 1 1 1 1v1l-2 2-1-1v1c-1 0-1 1-2 2-1 0-2 0-4-1 1-1 1-3 2-4h-1c1-1 1-1 2-1v-2c2 0 3 0 4-1h0z" class="B"></path><path d="M763 593c0 1 0 2 1 3 0 1 1 1 1 1v1l-2 2-1-1v-2-1c-1 1-2 1-4 1h0-1c1-1 1-1 2-1v-2c2 0 3 0 4-1h0z" class="D"></path><path d="M764 526l3 3 1-1 1 1v2c1 2 0 3 2 4l1 1c0 6 1 14 0 21v-5l-1 2-1 1-1-3v1 1c-2 0-3-1-5-1h-2-1l-1 1c-2 1-4-1-6-2 0-1 0-1 1-2v-1l-1-1-1 1v-1l1-1c-1-1-1-2-1-3-1-1-1-2-1-3h-1l-1-6h1v-4l1-2c1 1 1 1 2 1l1 1c2-2 5-1 8-3v-1l1-1z" class="j"></path><path d="M754 547c2 0 3 0 4 1s3 2 4 3v-1l1-1c1 1 2 3 4 3l-2-2 1-1c1 0 1 1 2 2l1 3c-2 0-3-1-5-1h-2-1l-1 1c-2 1-4-1-6-2 0-1 0-1 1-2v-1l-1-1-1 1v-1l1-1z" class="T"></path><path d="M751 531l1-2c1 1 1 1 2 1l1 1 1 2h0c1 0 2 1 2 1h3v1l-1 1c1 1 2 0 3 1l2 1h1v1c0 3 0 4-1 6-2 1-3 2-5 2-3 0-6-2-7-3s-1-2-1-3h-1l-1-6h1v-4z" class="S"></path><path d="M757 543c-2-1-2-1-2-2 0-2 0-2 2-2l1 1-1 2v1z" class="F"></path><path d="M757 542l6-1 1 1c-1 2-3 3-5 2-1 0-2 0-2-1v-1z" class="D"></path><path d="M758 540h1c2 0 3-2 4-3l2 1h1v1 1l-2 2-1-1-6 1 1-2z" class="L"></path><path d="M770 509c1-1 1-1 2-1v1c0 1 1 2 2 3 1 7 1 13 1 20a280.54 280.54 0 0 1 0 41l-5 25-2 11-12 33h-1l-1 1v-5l4-11h-1l-1 1c-1 0-2-3-3-4 2-2 4-5 5-8l2-1 1 2 7-27c1-1 1-4 1-5 2-9 3-19 3-28 1-7 0-15 0-21l-1-1c-2-1-1-2-2-4v-2-2l1 1h0v-3-3-3h1c0-4-1-7-1-10z" class="W"></path><path d="M771 519c1 5 1 11 1 17l-1-1c-2-1-1-2-2-4v-2-2l1 1h0v-3-3-3h1z" class="Q"></path><path d="M758 616l2-1 1 2-3 10h-1l-1 1c-1 0-2-3-3-4 2-2 4-5 5-8z" class="D"></path><path d="M762 497l2-3h2c0 1 0 2 1 3v1l1 1 2 10c0 3 1 6 1 10h-1v3 3 3h0l-1-1v2l-1-1-1 1-3-3-1 1v1c-3 2-6 1-8 3l-1-1c-1 0-1 0-2-1l-1 2v-8c-1-6-1-12-2-18 0-1 1-1 1-1h1v-1h2c1 0 1 1 1 1s0-1-1-2v-1c-1-1 0-1 0-2l2 1 2 2c1-1 1-3 1-4 1 1 1 1 2 1 0-2 0-2 1-4h1v2z" class="R"></path><path d="M753 511l-2-3v-1h2l2 1-2 3z" class="P"></path><path d="M755 508c1 1 2 1 4 1h3c1 1 1 0 0 1l-1 1h-7c-1 1-1 1-2 1v-1h1l2-3z" class="B"></path><path d="M756 520c-1 0-2 0-3-1-1 0-1-2-2-3 0-1 0-1 1-2 2 0 4-1 6 0v1h2l1 2c-1 0 0 0-2 1v1l-3 1z" class="Y"></path><path d="M756 520c-1-2-1-2-1-4h2l2 2v1l-3 1z" class="D"></path><path d="M767 518l1 1h1 0c-1 3 0 5 0 8h-1c-2 0-2-1-3-2h-1c0 1-1 1 0 1l-1 1c-1 0-1 0-1 1l-1 1-1-1v-2-1c-1 2-1 3-3 4l-1-1v-4c2-2 4-2 8-4v1h1l1-2 1-1z" class="H"></path><defs><linearGradient id="AR" x1="774.69" y1="501.046" x2="759.995" y2="520.213" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#222221"></stop></linearGradient></defs><path fill="url(#AR)" d="M762 497l2-3h2c0 1 0 2 1 3v1l1 1 2 10c0 3 1 6 1 10h-1v3 3 3h0l-1-1v2l-1-1-1 1-3-3c-1 0 0 0 0-1h1c1 1 1 2 3 2h1c0-3-1-5 0-8h0-1l-1-1-1 1v-1h-3c-1 1-2 1-3 1h-1v-1c2-1 1-1 2-1l-1-2h-2v-1l6-2c0-1-1-1-2-2 1-1 1 0 0-1h-3l-3-3c-1-1-1 0-1-1l-1-1s0-1-1-2v-1c-1-1 0-1 0-2l2 1 2 2c1-1 1-3 1-4 1 1 1 1 2 1 0-2 0-2 1-4h1v2z"></path><path d="M767 518l-1-1 2-1 1 1v2h-1l-1-1z" class="D"></path><path d="M762 495v2c1 2 1 3 1 4l1 1-1 1h0-1v1c1 0 2 0 3 2 0 1 1 4 0 5l-1 1c0-1-1-1-2-2 1-1 1 0 0-1h-3l-3-3c-1-1-1 0-1-1l-1-1s0-1-1-2v-1c-1-1 0-1 0-2l2 1 2 2c1-1 1-3 1-4 1 1 1 1 2 1 0-2 0-2 1-4h1z" class="O"></path><path d="M295 416h-1 0l-1-1c1-1 1-1 2-1l1-1 2-2c1 0 1 0 1-1s0-1-1-2h0v-1h2c0 1 1 3 2 4 2 0 3 0 4 1 2 1 4 1 6 2 2 0 3 0 4 1 0 2-1 3-2 5l-4 6-5 8 1 1c-1 3-3 5-3 9l-8 15-3 3c-1 1-1 2-1 3l1 1h-1l-3 8-1 4-4 11c0 1-1 1-2 1 0 1-1 1-1 1-7 22-7 44-6 67 2 32 9 66 25 95l1 1c2 4 4 6 6 10 6 8 12 17 19 25l5 6c-2 0-2 1-3 2l5 6c1 0 2 1 2 2h0-2l-2-1h-2 0l-2-1h-1l-1 3v1 1 2c1 0 2 0 3 1-2 1-3 2-4 3l-1 2c0 1 0 1 2 2h1v2h-3c-1 0-2 0-3 1-4-4-8-8-12-13-4-3-6-7-9-11 0-1-4-5-4-6h-1v1l-1 1h-1l-1-3-6-9c-1-3-3-6-4-9-2-3-5-8-7-12 0-2-2-4-2-6h1c1 1 2 1 2 3h0l1 1v-2h0v-1c0-2-1-3-2-4l-5-10c-1-1-1-1-1-2 0-2-1-4-2-5l1-1s0 1 1 1l1-1h2c-1-5-5-10-7-15-1-1 0-1 0-2l2 2h0l1-1c-1-2-1-3 0-5h-1c-1-1-2-2-4-2 0-4-3-7-3-11h2v-2l-2 2-1-1c-1-3-2-7-1-10-1-1-1-2-1-2-1-3-1-6-1-8l2-1h1 1l-1-1h0c0-1-1-1-1-1-1-1-1-2-1-4 1 0 1-1 2-1l1 1c0-1 1-1 2-2 0-2-1-3-2-5h-1l1-1-1-1c0-1 0-2-1-3h0l1-3 10-5c1 1 0 2 0 3 0 2 0 3 1 4v-7-2c0-1 0-4-1-5l-1-1c1-1 1-1 2-1v-1h-1c0-1 1-2 1-3l1-1-1-1-2 1v-1c2-1 2-1 4-1v-4-10l1-4c1-3 1-7 1-11 0-3 2-7 2-10l-2-1h0 2v-2l-2-1c-1 0-2-2-3-2v1 2c-1 0-2 1-2 2l-1-1c-2-2-1-1-2-3 1-1 1-2 2-3 0-1 1-2 2-3 3-1 5-3 8-5 1 0 3-1 4-3s1-5 0-8h0l1-1-1-1v-3-2h4c1 0 2-1 3-1l-1-1h-1-3c2 0 3-1 4-2h0c3-2 3-4 4-6l1-3 1-1v-1c1-1 1-1 1-2-2-3-6-1-9-2 0-2 1-2 2-3l-1-2 1-2v-1c-1 1-2 1-2 2l-1 1-1-1 4-4c1-2 2-3 3-4 0-2 1-3 2-5h-2 0c1-1 2-1 3-3 0 0 0-1 1-1 1-1 2-2 2-4z" class="D"></path><path d="M305 670c1 1 2 2 2 3l-2 2-2-4 2-1z" class="B"></path><path d="M287 659l-1-2v-2c2 0 2 0 4 1l-3 3z" class="I"></path><path d="M283 650c1 0 2 1 3 1l-2 4c-1 0-2 1-4 0 2-2 2-3 3-5z" class="M"></path><path d="M283 650v-1c1-2 2-4 2-7l-1-4c2 4 3 9 2 13-1 0-2-1-3-1z" class="E"></path><path d="M269 490l7-5h0l-1 7h-2-1c-1 0-2-2-3-2z" class="G"></path><path d="M280 655c2 1 3 0 4 0-1 2-1 3-3 4-3-1-4-1-6-3h0v-1c2 0 3 1 5 0z" class="h"></path><path d="M325 689l5 6c-2 0-2 1-3 2l-6-7c1 0 2 1 3 2l1 1h2l-3-3 1-1z" class="Q"></path><path d="M287 469h1c0 1 0 2-1 3 0 1-1 2-1 3l1 1v2l-4 11c0 1-1 1-2 1 0 1-1 1-1 1l7-22z" class="T"></path><path d="M296 430h2v1 1h4c-2 2-5 6-7 7h-3c-2-1-3-2-4-4 1-1 1-1 2-1l1-1c1-1 3-2 5-3z" class="f"></path><path d="M296 430h2v1 1c-1 0-2 1-2 2v1c-1 1-3 0-4 1l-2-2h0l1-1c1-1 3-2 5-3z" class="C"></path><path d="M295 416c2-1 3-3 5-4-2 3-5 6-6 9 0 1-1 1-1 2h0c1-1 2-1 3-2 1-3 4-5 5-8h1c0 1 1 1 0 2 0 3 2 3 1 6v-2h-1-1c-2 0-4 2-5 4v1s-1 1-2 1c-1 1-1 3-2 4h-3c0-2 1-3 2-5h-2 0c1-1 2-1 3-3 0 0 0-1 1-1 1-1 2-2 2-4z" class="N"></path><path d="M290 656l3 7c-1 1-2 1-3 1h0l1 1 2-1 1 1v1c-1 0-2 1-3 1l-1-1-2 2v1h-3l-1-1c-1-2-1-4 0-6l3-3 3-3z" class="B"></path><path d="M299 666c-5-7-8-14-11-22-1-3-3-7-4-11h1v1c5 12 12 25 20 36l-2 1-4-5z" class="E"></path><path d="M277 605l5 24c0 3 1 6 0 9-1 2 0 7-4 8 0 1-1 1-1 1-1-1-2-1-2-1-1-1-1-2-3-2h0l1-1 3 2c0-1 0-1 1-1 0 1 0 1 1 1 0-1 1-2 1-3 1-2 1-3 1-5l-2-2 2-2v2h1v-4l-5-21c0-1-1-2-1-3l2-2z" class="R"></path><path d="M289 429h3c1-1 1-3 2-4 1 0 2-1 2-1v-1c1-2 3-4 5-4h1 1v2c0 1-1 3-2 4 1 1 1 1 2 1l2-1v1l-1 2-6 3v-1h-2c-2 1-4 2-5 3l-1 1c-1 0-1 0-2 1l-2 1v-1c-1 1-2 1-2 2l-1 1-1-1 4-4c1-2 2-3 3-4z" class="G"></path><path d="M296 430l1-1c1-1 1-1 1-2v-1l1-1 1 1 3 1 1 1-6 3v-1h-2z" class="M"></path><path d="M305 434l1 1c-1 3-3 5-3 9l-8 15-3 3c-1 1-1 2-1 3l1 1h-1l-3 8-1 4v-2l-1-1c0-1 1-2 1-3 1-1 1-2 1-3h-1l3-8c1-4 3-7 5-10 3-6 6-12 10-17z" class="h"></path><path d="M273 660c0-2-2-4-2-6h1c1 1 2 1 2 3h0l1 1v-2c2 2 3 2 6 3h-2v2c1 0 1 1 3 1h1 1c-1 2-1 4 0 6l1 1h3v-1l2-2 1 1c1 0 2-1 3-1-1 1-1 2-2 4h1v1l-2 2c-1 2-2 3-2 5l1 1-1 1c-1-1-1-2-3-2h0c-2-1-3-4-6-6-2-3-5-8-7-12z" class="I"></path><path d="M285 669h3v-1l2-2 1 1c1 0 2-1 3-1-1 1-1 2-2 4h1v1l-2 2c-1 2-2 3-2 5l-1-1c-1-2-1-3-2-6h1v-1h-1l-1-1z" class="M"></path><path d="M273 660c0-2-2-4-2-6h1c1 1 2 1 2 3h0l1 1c2 1 2 4 3 5 3 5 6 10 8 15h0c-2-1-3-4-6-6-2-3-5-8-7-12z" class="h"></path><defs><linearGradient id="AS" x1="275.888" y1="571.045" x2="267.142" y2="570.944" xlink:href="#B"><stop offset="0" stop-color="#9d9c9b"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#AS)" d="M266 536c2-1 2-1 4-1 0 23 1 47 7 70l-2 2c-4-16-6-32-7-49v-7-2c0-1 0-4-1-5l-1-1c1-1 1-1 2-1v-1h-1c0-1 1-2 1-3l1-1-1-1-2 1v-1z"></path><path d="M271 609c0 2 0 2 2 3h1 1c0-1 1-1 1-2l5 21v4h-1v-2l-2 2 2 2c0 2 0 3-1 5 0 1-1 2-1 3-1 0-1 0-1-1-1 0-1 0-1 1l-3-2-1 1h0c2 0 2 1 3 2 0 0 1 0 2 1h1v1h-2v1l1 1 1-1v1c-1 0-2 0-3 1h-2l-5-10c-1-1-1-1-1-2 0-2-1-4-2-5l1-1s0 1 1 1l1-1h2c-1-5-5-10-7-15-1-1 0-1 0-2l2 2h0l1-1c-1-2-1-3 0-5l1 1c2-1 3-3 4-4z"></path><path d="M270 633c-1-5-5-10-7-15-1-1 0-1 0-2l2 2h0c2 1 2 1 5 1h0 2l-2 5h2 0 1l-1 3c-1 1-1 2-1 3l2 2h0c2-1 3-1 5-1l1 1c0 1 0 1-1 1l-1 1-1 1c-1 1-1 2-2 3-2-1-3-4-4-5z" class="C"></path><path d="M265 618c2 1 2 1 5 1h0 2l-2 5c-2-1-4-4-5-6h0z" class="D"></path><path d="M271 609c0 2 0 2 2 3h1 1c0-1 1-1 1-2l5 21-2-2c-1 0-1 0-1-1-3-2-1-2-1-5h-2c-1 2-2 3-3 4h0l1-3h-1 0-2l2-5h-2 0c-3 0-3 0-5-1l1-1c-1-2-1-3 0-5l1 1c2-1 3-3 4-4z" class="I"></path><path d="M275 612c1 3-1 9-2 12h0-1 0-2l2-5h0c1-1 0-3 0-4l1-1s1-1 1-2h1z" class="E"></path><path d="M271 609c0 2 0 2 2 3h1c0 1-1 2-1 2l-1 1c0 1 1 3 0 4h0-2 0c-3 0-3 0-5-1l1-1c-1-2-1-3 0-5l1 1c2-1 3-3 4-4z" class="j"></path><path d="M271 609c0 2 0 2 2 3h1c0 1-1 2-1 2h-2c-2 0-2 0-4-1 2-1 3-3 4-4z" class="H"></path><path d="M266 617h0c3 0 4-1 6-2 0 1 1 3 0 4h0-2 0c-3 0-3 0-5-1l1-1z" class="T"></path><path d="M257 556l10-5c1 1 0 2 0 3 0 2 0 3 1 4 1 17 3 33 7 49 0 1 1 2 1 3s-1 1-1 2h-1-1c-2-1-2-1-2-3-1 1-2 3-4 4l-1-1h-1c-1-1-2-2-4-2 0-4-3-7-3-11h2v-2l-2 2-1-1c-1-3-2-7-1-10-1-1-1-2-1-2-1-3-1-6-1-8l2-1h1 1l-1-1h0c0-1-1-1-1-1-1-1-1-2-1-4 1 0 1-1 2-1l1 1c0-1 1-1 2-2 0-2-1-3-2-5h-1l1-1-1-1c0-1 0-2-1-3h0l1-3z" class="G"></path><path d="M260 569h3 0l-1 2c1 1 1 1 0 2h-1v1c-2-1-2-2-3-3 0-1 1-1 2-2z"></path><path d="M261 578c3 3 5 6 5 10v2c-2-2-3-5-5-8 1-1 0-3 0-4z" class="Q"></path><path d="M257 576c2 0 3 1 4 2 0 1 1 3 0 4 0 2-1 3-2 4l-2-2v-1c1-2 1-3 2-5l-1-1-1-1zm3 24l3-3 2-2v2h1 1v3l-2 4-1 1c-1-1-1-2-2-3v1 1 3l1 1-1-1c-1-3-3-4-2-7z" class="D"></path><path d="M256 588l1 1c0 1 1 2 1 3l1-1v-1h2l2-1 2 2c1 1 1 2 1 4h-1l-2 2-3 3v-1-2l-2 2-1-1c-1-3-2-7-1-10z" class="S"></path><path d="M258 592l1-1v-1h2l2-1 2 2c-1 2-3 4-4 5-2-1-2-2-3-4z"></path><path d="M257 556l10-5c1 1 0 2 0 3 0 2 0 3 1 4 1 17 3 33 7 49 0 1 1 2 1 3s-1 1-1 2h-1-1c-2-1-2-1-2-3 1-3-1-5-2-8l-1 1v-1-1c1-1 1-2 1-3-1-2-1-3-1-4-1-5-1-9-2-13-1-2 0-2-2-4v1h-1l1-2c-1 0-2 0-2-1h0 3 0v-6l-2 1h0-3c0-2-1-3-2-5h-1l1-1-1-1c0-1 0-2-1-3h0l1-3z" class="D"></path><path d="M265 557c0 2 0 5-1 7h-1c-1 1-1 2-2 4h1l1-1h0v2h-3c0-2-1-3-2-5h-1l1-1 1-1v2h1c0-1 0-1 1-2 0-1 0-2 1-3 1 0 1-1 2-1l1-1z" class="O"></path><path d="M257 556l10-5c1 1 0 2 0 3s-2 2-2 3l-1 1c-1 0-1 1-2 1-1 1-1 2-1 3-1 1-1 1-1 2h-1v-2l-1 1-1-1c0-1 0-2-1-3h0l1-3z"></path><path d="M294 665c1 1 1 2 2 2s2 0 3-1l4 5 2 4 2-2 11 13c0 1 2 3 3 4l6 7 5 6c1 0 2 1 2 2h0-2l-2-1h-2 0l-2-1h-1l-1 3v1 1 2c1 0 2 0 3 1-2 1-3 2-4 3l-1 2c0 1 0 1 2 2h1v2h-3c-1 0-2 0-3 1-4-4-8-8-12-13-4-3-6-7-9-11 0-1-4-5-4-6h-1v1l-1 1h-1l-1-3-6-9c-1-3-3-6-4-9 3 2 4 5 6 6h0c2 0 2 1 3 2l1-1-1-1c0-2 1-3 2-5l2-2v-1h-1c1-2 1-3 2-4v-1z" class="L"></path><path d="M311 689v-1c2 0 2 0 4 1 0 1 0 2-1 3-1-1-1-2-3-3z" class="H"></path><path d="M301 680l1-1c0 2 0 2-1 3s-3 2-5 2h-6c-1-1-1-2-1-3h2c0 1 1 1 2 1 3 1 5 0 8-2z" class="T"></path><path d="M280 672c3 2 4 5 6 6h0c2 0 2 1 3 2l1 1h1 0-2c0 1 0 2 1 3h6 0c-2 0-3 1-4 0l-1 1h1c1 2 0 4 2 6h-1v1l-1 1h-1l-1-3-6-9c-1-3-3-6-4-9z" class="N"></path><path d="M306 678h0c1 1 1 1 1 3h0l-3 6c-1 0-1 1-2 1v1c4-2 4-4 6-8l1 4c-2 1-2 2-3 4v1c-2 2-3 4-3 7h0c-2-1-3-4-4-5l1-1v-1h-1c-1 0-2-1-2-2v-1l4-2c1-2 3-5 5-7z" class="W"></path><path d="M307 673l11 13c0 1 2 3 3 4l6 7 5 6c-2 0-5-3-6-4-1 0-1 0-2 1v1c1-1 2-1 3 0v1c-2 1-4 1-7 1l1-1c0-2 1-3 2-4h0v-1c0-1-3-4-4-5-5-5-10-11-14-17l2-2z" class="T"></path><path d="M293 670l4-2c1 1 3 3 4 5v1c1 1 1 1 1 2l-1 1c-1 1-1 2-2 3h2c-3 2-5 3-8 2-1 0-2 0-2-1h0-1l-1-1 1-1-1-1c0-2 1-3 2-5l2-2v-1z" class="E"></path><path d="M299 680c-2-1-3-1-4-1-2-1-3-2-3-4 0-1 0-2 1-2 0 0 1 0 2 1s3 0 3 0l2 2v1h1c-1 1-1 2-2 3z" class="X"></path><path d="M293 671h2 0c-1 1-2 1-2 2-1 0-1 1-1 2 0 2 1 3 3 4 1 0 2 0 4 1h2c-3 2-5 3-8 2-1 0-2 0-2-1h0-1l-1-1 1-1-1-1c0-2 1-3 2-5l2-2z"></path><path d="M311 689c2 1 2 2 3 3l1 1c2 1 3 2 3 4l2 3c-1 2-3 4-5 6-1 1-2 1-3 1l-7-7c1-1 1-1 1-2 0-3 1-4 3-6 1-1 1-2 2-3z" class="S"></path><path d="M311 689c2 1 2 2 3 3l1 1c1 2 1 3 1 5-1-2-1-3-4-3v-2c-1 0-2-1-3-1 1-1 1-2 2-3z" class="U"></path><path d="M312 695c3 0 3 1 4 3h1c0 2-2 3-3 5-1 1-2 1-3 1-2-1-3-2-4-4 1-1 1-2 2-3 2-1 2-1 3-2zm-5 13c0-1-1-2-2-3l-2-3h1c0-1-1-2-1-2v-1l2 1 7 7v2l6-3c3-2 4-2 7-3l-1 3v1 1 2c1 0 2 0 3 1-2 1-3 2-4 3l-1 2c0 1 0 1 2 2h1v2h-3c-1 0-2 0-3 1-4-4-8-8-12-13z" class="j"></path><path d="M312 709l6-3 2 2c-2 1-4 2-5 2h-2l-1-1z" class="X"></path><path d="M318 706c3-2 4-2 7-3l-1 3v1 1h-4l-2-2z" class="B"></path><path d="M329 297h359l4 124h-15c-1-15-4-38-16-49-5-6-11-9-19-11-17-5-35-4-52-4h-42v70 165 68c0 14-1 29 1 43 1 6 3 11 7 16 9 13 26 17 41 20v12H423v-12c12-1 25-5 35-14 6-6 9-13 10-22 2-13 2-27 2-40v-68l-1-238h-36c-23 0-51-1-70 14-15 12-20 31-22 50h-15l3-124z" class="l"></path></svg>