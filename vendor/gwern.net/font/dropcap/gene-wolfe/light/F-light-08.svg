<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="250 150 515 767"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#c5beab}.C{fill:#a8a18d}.D{fill:#bcb59e}.E{fill:#aaa48f}.F{fill:#beb8a5}.G{fill:#a49d8a}.H{fill:#dbd5c1}.I{fill:#9f9984}.J{fill:#33322b}.K{fill:#43413a}.L{fill:#7f7a68}.M{fill:#c1bba5}.N{fill:#918c7a}.O{fill:#d6d0b8}.P{fill:#706c5e}.Q{fill:#c5bda8}.R{fill:#252420}.S{fill:#7c7766}.T{fill:#59574c}.U{fill:#36352e}.V{fill:#4a483f}.W{fill:#beb9a5}.X{fill:#848070}.Y{fill:#d5d0bb}.Z{fill:#706b5a}.a{fill:#3c3c34}.b{fill:#34332d}.c{fill:#514e41}.d{fill:#958f7a}.e{fill:#8f8772}.f{fill:#cbc5b0}.g{fill:#615e50}.h{fill:#e7e4d5}.i{fill:#b2ac94}.j{fill:#7a7462}.k{fill:#1f1f1c}.l{fill:#131413}.m{fill:#0e0e0c}.n{fill:#1b1a16}.o{fill:#b4af97}.p{fill:#8a8575}.q{fill:#1a1918}.r{fill:#282823}</style><path d="M613 821c1 0 3-1 5 0h-5 0z" class="H"></path><path d="M520 162c1-1 2-1 3-1l1 1-1 2h-3v-2z" class="T"></path><path d="M647 472v-2l3-3v3c0 1-1 1-1 2-1 1 0 5 0 6l-1-5v-1h-1z" class="Y"></path><path d="M369 482h2c0 1 1 1 1 2h-1l-1 1h-1-1c-1-1-3-1-5-1 2-1 4-1 6-2z" class="B"></path><path d="M257 211h0c-3-1-7-1-10-1 3 0 24-1 25 0-4 0-10 0-15 1z" class="O"></path><path d="M647 472h1v1l1 5v3l2 1h0-3l-2-1v-4c1-1 1-4 1-5z" class="h"></path><path d="M584 431l5-1 1 1 1-1 1 1c-2 1-4 4-6 6v-1h-1l1-2-1-2v-1h-1z" class="H"></path><path d="M515 119l1 12-2 3-1-13 2-2z" class="K"></path><path d="M369 482l-1-3c1 0 2 1 2 2 4 1 7 1 11 1v1l-1 1c-1 0-1-1-2 0h-6c0-1-1-1-1-2h-2z" class="D"></path><path d="M383 476c0 2 0 4 1 6l-3 2v1h2v2h1v1l-1 2v1h-1l1-5c-4 0-8 0-13-1l1-1h1 6c1-1 1 0 2 0l1-1v-1c0-1 1-1 2-1v-5z" class="E"></path><path d="M674 489l10 1v1c-3 2-10 1-13 1 0-1 0 0 1-1 0-1 1-1 2-2z" class="O"></path><path d="M576 561l4-1-1 4-1 2c1 4 8 9 11 12v1c-4-4-7-8-12-11 0-1-1-1-1-2l-1 1-1-1v-1h1l1-1c2 0 2 0 3-1l-1-1-2-1z" class="P"></path><path d="M485 851h3 2v1c1 1 0 1 1 1s1 0 2 1l-3 1-1-1h-3v1l-8-2 3-1c1 0 2-1 4-1z" class="L"></path><path d="M585 555l2 2c0 1 0 1-1 2 0 0-1 0-1 1s-1 1-2 2l-4 2 1-4h2 1l-1-2 1-1 2-2z" class="Z"></path><path d="M362 557c1 0 1 0 2 1s2 1 3 1c2 0 3 0 5 1 1 1 1 1 2 3h-1-1-3c-2-2-5-3-8-4 0-1 1-1 1-2z" class="F"></path><path d="M368 485h1v8l-1-1c0 2 0 4-1 5-1 0-3 0-5-1 0-1-1 0 0-2h-1v-1h3c1-1 3-1 4-2v-6z" class="H"></path><path d="M353 527h10c-1 1-1 1-1 3 2 1 2 1 4 1h-11c0-1-1-2-2-4z" class="B"></path><path d="M355 531c-3 0-6 0-9-1h-10l17-3c1 2 2 3 2 4z" class="D"></path><path d="M371 540h-4c0-1-1-1-1-1-2-1-3 0-5-1h5s1-1 2-1c2 0 5 1 7 0 1 0 1 0 2 1 2 0 3-2 5-3 0 2 0 2-1 3l-2 1h0 1l-2 1h-7z" class="B"></path><path d="M383 476v-4h1l1 7c3 1 8-2 11-3l1 1c-3 1-8 2-9 5-2 1-3 3-4 5h-1v-2h-2v-1l3-2c-1-2-1-4-1-6z" class="I"></path><path d="M339 558c-4 0-9-1-13-1-3 1-7 1-11 1 2 0 8-3 10-2h1c2 0 4 0 5-1l-1-1h-2-1c3-1 5-1 7-1l-1 1 1 1h1c1 1 2 1 4 1v2z" class="M"></path><path d="M516 131l2 15c1 1 4 0 5 1 1 0 1 1 1 1v1h-1c-2-1-3 0-5 0-1-1-2-2-3-4 0-3 0-7-1-11l2-3z" class="c"></path><path d="M542 196h0c2 0 2-1 3-2v1l-1 2c1 1 1 1 2 1 3 1 6 1 9 1l1-1v1c-2 1-4 1-6 1h-2c-2 0-2 0-4 1 0 3 1 5 3 8h-1c-1-2-3-5-4-7h-3v-1c1-1 2-1 3-2s0-1 0-2h-1l-1-1h2z" class="g"></path><path d="M515 145c1 2 2 3 3 4v12l2 1v2c-1 0-3-2-5-2 2-4 0-12 0-17z" class="K"></path><path d="M665 493l35 1c-9 1-20 0-29 0l-10 1v1h-1-1-1l1-1c1-1 2-1 3-1 1-1 2 0 3-1z" class="O"></path><path d="M372 560h5 1v2c2 2 7 3 10 3 1 0 0 0 2 1h1c1 0 1 1 2 1h1l1 2c-2 0-4-1-6-2l-10-2c-1 0-4-1-5-1 0 0-2 1-3 2l-1-1 3-2h1c-1-2-1-2-2-3z" class="C"></path><path d="M372 529c3 0 6 0 9 1 2 0 3 0 4 1v1c-1 2-1 2-1 4 1 1 1 1 1 2-1 1-2 1-3 1l-1 1h-3l2-1h-1 0l2-1c1-1 1-1 1-3l-1-1 1-2-1-1h-6-7s0-1 1-1h1l2-1z" class="F"></path><path d="M646 464h1c2 0 4-1 5-2l16-11h0l-18 16-3 3v2c0 1 0 4-1 5 0-2 1-5 0-6l-3 1h0c1-2 3-4 4-6 0 1-1 1-1 1v-3z" class="G"></path><path d="M535 188c0 1 0 3 1 4 2 2 4 2 6 4h-2l1 1h1c0 1 1 1 0 2s-2 1-3 2c-1 0-3 1-4 2l-1-7v-6c0-1 0-1 1-2z" class="D"></path><path d="M534 190l1 4c0 1 1 3 1 5 2 0 3 0 5-1l1 1c-1 1-2 1-3 2-1 0-3 1-4 2l-1-7v-6z" class="E"></path><path d="M354 500c3 0 7 0 11 1l1-1 1 1v5c-4 0-8-1-12-3-2 0-3-1-4-2l3-1z" class="H"></path><path d="M351 501l3-1c1 1 2 1 3 2 2 1 4 1 6 1v1h-4c-1-1-3-1-4-1-2 0-3-1-4-2z" class="Y"></path><path d="M556 826c2 0 4-1 6-1 1 0 2 0 3-1-1 3-4 6-6 8s-4 3-6 6c0 1-2 1-3 1v-2l2-1-1-2c1-1 2-2 3-2 1-1 1-2 2-3l2-1 1-1h-1c-1 0-1-1-2-1z" class="Z"></path><path d="M577 822l-1-2 1-1h4l32 2h0l-33 1h-3z" class="M"></path><path d="M577 822l-1-2 1-1h4c2 1 3 1 4 2h1c-2 0-4 0-6 1h-3zm30-393l1-4c1-6 10-13 14-16-1 3-2 5-4 7 0 1-2 3-2 5h0c-1 1-2 0-2 2-1 1 0 1-1 2 0 1-1 2-2 2l-1 1c-1 0-2 0-3 1z" class="E"></path><path d="M367 512h1c2 1 3 0 5 1 1 1 2 1 4 1-2 0-4 0-5 1v2l-6-1h0-10c-1-1-1 0-2-1h-1c-4 0-7 0-11-1 3-1 7 0 11-1h1c3 1 11 0 13-1z" class="N"></path><path d="M369 485h1c5 1 9 1 13 1l-1 5h-5l-8 2v-8z" class="h"></path><path d="M506 120l2-19 2-31v-7c-1-1-1-1-1-2s0-2 1-2l1 1c-1 8-1 17 0 25 0 5 1 10 0 14l-1 2c-2 2-2 14-2 17v2h-2z" class="G"></path><path d="M579 799c5 4 10 8 16 11-3 1-7 0-9 0-3 0-3 0-5-1-2-3-5-5-8-6h0l1-1c2 1 2 1 4 1h0c0-1 0-1 1-1l1-1-1-2z" class="T"></path><path d="M574 802c2 1 2 1 4 1v1c1 0 2 0 3 1 2 0 3 1 5 2 1 0 2 1 3 2-3 0-5-1-7-2v1c1 1 2 1 4 2-3 0-3 0-5-1-2-3-5-5-8-6h0l1-1z" class="U"></path><path d="M606 437c0 3-1 5-2 8h-1c-1 2-6 7-8 8l-1 1c-1 1-3 2-5 3-6 0-13 3-19 2h1l2-1h0c3 0 6 0 8-1 10-3 20-11 25-20z" class="d"></path><path d="M483 185v-6-1c0-1 1-3 1-5 3-6 8-9 14-12v4h-1l-1 1v1c-4 3-7 4-9 9 0 1 0 3-1 4h0c0 2-1 3-1 4l-1 1h-1z" class="W"></path><path d="M448 812l1 1 2-1v1 1h1l1 1v1c1 2 2 3 2 5-2 0-4 0-6-1-1 1-1 1-2 3-2-1-5-1-8-2h-5c0-1 4 0 5-1h3l1-2 1-1v-2l2-1c0-1 2-2 2-2z" class="C"></path><path d="M448 812l1 1 1 2c-1 0-1 1-2 1-1-1-1-1-2-1v-1c0-1 2-2 2-2z" class="B"></path><path d="M444 815l2-1v1 1l3 2s0 1-1 1c-2 0-2 0-4-1h-1l1-1v-2z" class="M"></path><path d="M368 557l6 1 14 2s2 0 2 1l1 1h0c3 1 3 2 5 4h0v3h-1l-1-2h-1c-1 0-1-1-2-1h-1c-2-1-1-1-2-1-3 0-8-1-10-3v-2h-1-5c-2-1-3-1-5-1l1-2z" class="N"></path><path d="M381 527c2 0 4-1 6-1h0c1 1 1 1 1 2l1 2h0 2c1 0 2 1 3 1l-2 3h0l-2 3c-2 1-2 2-4 2h-1v-1c0-1 0-1-1-2 0-2 0-2 1-4v-1c-1-1-2-1-4-1-3-1-6-1-9-1 0-1 1-1 1-1h1 2l1-1h3 1z" class="G"></path><path d="M381 527c2 0 4-1 6-1h0c1 1 1 1 1 2l1 2h-1-1c-2 0-3 0-4-1h-3v-2h1z" class="j"></path><path d="M389 530h0 2c1 0 2 1 3 1l-2 3h0l-2 3-1-1h0c0-1-1-2-2-3-1 0 0 0-1-1 1 0 2 0 2-1v-1h1z" class="X"></path><path d="M374 523c2 0 4 0 6 1h2v1l-1 2h-1-3l-1 1h-2-1s-1 0-1 1l-2 1h-1c-1 0-1 1-1 1l-1 2c0-1 0-1-1-2-2 0-2 0-4-1 0-2 0-2 1-3s2-1 3-1l2 1h0v-4h6z" class="C"></path><path d="M374 523c2 0 4 0 6 1h2v1l-1 2c-4-1-9 0-13 0v-4h6z" class="h"></path><path d="M347 499c-1 1-3 1-5 1-3 0-8 0-11-1-2-1-2-1-4-1h0-10v-1c4 0 8 0 13-1 7-1 14 0 21-2h1 2 0 0v1c-2 2-4 1-6 1l-1 2v1z" class="M"></path><path d="M661 496v-1l10-1v2c1 2 1 3 1 5 6 0 14 0 20 1l-20 2c-1 1-1 3-1 4h0c0-1 0-2-1-4v-1h-4 0l-1-2h0l-1-1v-2c-1-2-1-2-3-2z" class="o"></path><path d="M661 496v-1l10-1v2 4c-2 1-4 0-6 1l-1-1v-2c-1-2-1-2-3-2z" class="h"></path><path d="M512 162v-40l1-1 1 13c1 4 1 8 1 11 0 5 2 13 0 17-1 1-2 0-3 0z" class="r"></path><path d="M604 555h0c0-4-1-4-3-7l-1-2h0l1-1 8 12-1 1c1 5 6 10 9 15l-21-17 3-1 2 3 2-1 1-1v-1z" class="p"></path><path d="M486 188c1 2 1 4 1 7 0 2-1 6-1 9-2-2-4-3-6-4h-1 0-1v2 1c-1 2 0 4-1 5h-1l1-7c-2 0-5 1-7 0l2-2c1-1 3-1 4-1 0 0 1-1 0-2 0-2-1-3-3-4 0-1-1-1-1-2h1l5 5c1 0 3-1 4-1s2-1 3-2c0-1 0-2 1-4h0z" class="D"></path><path d="M553 355v10l1 1h0c3-1 7-1 10 1 2 1 3 3 4 6 1 0 1 0 1 1h0c2 2 4 4 5 6l-5-4c-3 4-6 7-10 8-2 0-3 0-5-1h-1-1 0v-1c1-2 1-2 0-3l1-1v1l1-1 1 2c-1 0-1-1-2 0l2 1c1 1 2 1 4 0 1 0 2 0 4-1h-1l-3 1c-2-1-2 0-3-2 2-1 3 0 5-1 2 0 3-3 5-4h0 1l-1-1v-1c-1-2-2-4-4-5-1-1-5-2-6-1l-4 1v-6h0c1-1 0-4 1-6z" class="V"></path><path d="M377 514s1 1 2 1v-1l2-1v1h0l-1 2c2 1 2 0 3 1v1 2h-1c-1 0-1 0-2 1-2 1-3 1-4 1l-2 1h-6v4h0l-2-1c0-2 1-4 1-6 0 0 1 0 1-1h-1l-1-3 6 1v-2c1-1 3-1 5-1z" class="M"></path><path d="M378 518h5v2h-1c-1 0-1 0-2 1-2-1-4-1-6-1 2 0 3-1 4-2z" class="Y"></path><path d="M366 516l6 1 6 1c-1 1-2 2-4 2h-7s1 0 1-1h-1l-1-3z" class="O"></path><path d="M377 514s1 1 2 1v-1l2-1v1h0l-1 2c2 1 2 0 3 1v1h-5l-6-1v-2c1-1 3-1 5-1z" class="p"></path><path d="M499 864c1 1 0 3 2 5v-2l2-1v10 3l-1 6 1 8-1 2-4-16c0-3-1-7-3-10l2-2h1l1-3z" class="N"></path><path d="M499 876h4v3l-1 6-1-4c0-2-1-3-2-5z" class="X"></path><path d="M499 864c1 1 0 3 2 5v-2l2-1v10h-4c-1-3 0-6-1-9l1-3z" class="L"></path><path d="M555 732l1 4c0 1 0 0 1 1 1 3 1 7 2 10 1 5 4 10 4 16h1 2 0l-2 2h0l1 1c2 2 2 9 6 10h0l-1 1h-4 0c-1-1-1-1-1-2-1-3-2-6-4-8h0c0-1 1-1 1-2-1 0-1 0-1-1-1-4-3-9-4-14s-1-12-2-18z" class="h"></path><path d="M369 493l8-2c0 2-1 3-1 4h0c-1 1-3 2-4 2 0 1-1 0-2 0s-2 3-3 4h0l-1-1-1 1c-4-1-8-1-11-1l-3 1c-1-1-3-1-4-2v-1l1-2c2 0 4 1 6-1l7-1h1c-1 2 0 1 0 2 2 1 4 1 5 1 1-1 1-3 1-5l1 1z" class="C"></path><path d="M508 120v17c-1 5-1 10-1 15v8l-1 3c-1 0-1 0-2-1h-1v-13c-2 0-6 1-8 0v-1h3v-1h-2v-1h2c1 0 1 0 1 1h1c1 0 2 1 3 0l3-27h2z" class="i"></path><path d="M504 162v-7c1-2 1-4 1-6h0l1-1c0 2 1 3 1 4v8l-1 3c-1 0-1 0-2-1z" class="M"></path><path d="M568 566c1-1 1-1 3-1h0c1 1 1 1 3 1l1 1 1-1c0 1 1 1 1 2l-1 1c-1 4-3 8-4 12-1-1-1-1-2-3l-2-1c0-1-1-2-2-2l-1 1h-1l-1 1-1 1-1-1c-2 1-3 2-4 3l-1-1 1-1c1-1 2-2 2-3 1-1 1-2 2-3h1l1-1c0-1 2-2 3-3 0-1 1-1 2-2z" class="N"></path><path d="M567 573c1-1 2-1 3-2v4c0 1 1 2 0 3l-2-1c0-1-1-2-2-2l2-1-1-1z" class="Z"></path><path d="M575 567l1-1c0 1 1 1 1 2l-1 1-3 1c0 3-1 3-3 5v-4l3-1v-2l2-1z" class="j"></path><path d="M568 566c1-1 1-1 3-1h0c1 1 1 1 3 1l1 1-2 1v2l-3 1c-1 1-2 1-3 2l-1-2 1-1c-1 0-1-1-1-2s1-1 2-2z" class="T"></path><path d="M568 566c1-1 1-1 3-1l-3 3v-2z" class="V"></path><path d="M566 568c0-1 1-1 2-2v2l-1 2c-1 0-1-1-1-2z" class="U"></path><path d="M566 568c0 1 0 2 1 2l-1 1 1 2 1 1-2 1-1 1h-1l-1 1-1 1-1-1c-2 1-3 2-4 3l-1-1 1-1c1-1 2-2 2-3 1-1 1-2 2-3h1l1-1c0-1 2-2 3-3z" class="V"></path><path d="M564 576h-1-2 0c0-2 1-2 2-3s2-2 3-2l1 2 1 1-2 1-1 1h-1z" class="K"></path><path d="M391 518l2-1 1 1c1 0 1-1 2-1l1-1v1 1c-1 2-3 3-3 5 1 1 2 1 3 1h0v2h0l-3 5c-1 0-2-1-3-1h-2 0l-1-2c0-1 0-1-1-2h0c-2 0-4 1-6 1l1-2v-1h-2c-2-1-4-1-6-1l2-1c1 0 2 0 4-1 1-1 1-1 2-1h1v-2-1c2 1 5 1 7 2v-1h1z" class="X"></path><path d="M394 523v1c-1-1-3-1-4-2h-2c1-1 3-1 5-1 1-1 3-2 4-3-1 2-3 3-3 5zm-7 3h5c0-1 1-1 2-1l1 1 2-2v2h0l-3 5c-1 0-2-1-3-1h-2 0l-1-2c0-1 0-1-1-2h0z" class="P"></path><path d="M383 517c2 1 5 1 7 2v-1h1v2h-1l-7 1v1c2 1 2 1 4 3v1c-2 0-4 1-6 1l1-2v-1h-2c-2-1-4-1-6-1l2-1c1 0 2 0 4-1 1-1 1-1 2-1h1v-2-1z" class="I"></path><path d="M391 508l2-1 1 2c1-1 0-1 1-1s2 1 4 1l2 1 1 1c1 1 2 1 3 2v3h-2v1 2l-6 7v-2h0c-1 0-2 0-3-1 0-2 2-3 3-5v-1-1l-1 1c-1 0-1 1-2 1l-1-1-2 1h-1v1c-2-1-5-1-7-2-1-1-1 0-3-1l1-2h3 7l3 1v-1c0-1 0-1-1-1l-1-1c0-1-1-2-3-3-1 0 1 0-1 0l1-2h1l1 1z" class="V"></path><path d="M397 517c1 0 2-1 3-1l1 1c0 2-2 5-4 7-1 0-2 0-3-1 0-2 2-3 3-5v-1z" class="e"></path><path d="M391 508l2-1 1 2c1-1 0-1 1-1s2 1 4 1l2 1 1 1c1 1 2 1 3 2v3h-2c-4-3-8-5-12-8z" class="C"></path><path d="M556 369v-1c-1 0-2 0-3 1h-1l-1-1h0v-3c-1-2-2-5-1-8l2 4v6l4-1c1-1 5 0 6 1 2 1 3 3 4 5v1l1 1h-1 0c-2 1-3 4-5 4-2 1-3 0-5 1 1 2 1 1 3 2l3-1h1c-2 1-3 1-4 1-2 1-3 1-4 0l-2-1c1-1 1 0 2 0l-1-2h0c-2-1-3-2-6-2l-1-1v-2l1-1c1 0 1 0 2-1h0c1 0 2-1 2-1l4-1z" class="D"></path><path d="M556 369h0 2c1 0 3 0 4 1l1 2c0 2-2 4-3 6-1 0-3 0-4-1h0l-1-1c-2-1-3-2-3-3l1-1-1-2 4-1z" class="O"></path><path d="M553 372c1-1 2-1 3-1v1c-1 1-1 1-1 3l1 1h0v1l-1-1c-2-1-3-2-3-3l1-1z" class="Y"></path><path d="M498 853c2 0 3-1 4 0v9l1 4-2 1v2c-2-2-1-4-2-5l-1 3h-1l-2 2h0c-5-4-8-8-9-14v-1h3l1 1 3-1 1-1h4z" class="S"></path><path d="M501 867v-4l1-1 1 4-2 1z" class="e"></path><path d="M486 855v-1h3l1 1c2 0 4 1 6 3v1h0-1l-3-3h-1c2 3 3 4 4 6l-2 2h-1l-1-1 2-2c-3-3-4-4-7-6z" class="V"></path><path d="M486 855c3 2 4 3 7 6l-2 2 1 1h1l2-2h1v1h-1c0 1-1 2-1 2l1 1c1-1 1-2 1-3 1-1 1-1 1-2 1 1 1 2 2 3l-1 3h-1l-2 2h0c-5-4-8-8-9-14z" class="p"></path><path d="M334 553l24-1h8c2 1 5-1 6 1l1 1c1 1 1 0 1 1h0v3l-6-1-1 2c-1 0-2 0-3-1s-1-1-2-1c0 1-1 1-1 2-6-2-15-2-22-1v-2c-2 0-3 0-4-1h-1l-1-1 1-1z" class="E"></path><path d="M374 555h0v3l-6-1h-2 0l1-1c2 0 5-1 7-1z" class="F"></path><path d="M335 555v-1c1 1 2 1 3 1v-2c1 1 4 2 5 1 0 0 1-1 2-1 0 0 0 1 1 1h6l1 1h-4c-1 1-3 1-4 0-1 0-1 1-2 1h-4c-2 0-3 0-4-1z" class="G"></path><path d="M352 554h1 4 1 1 0 2l2 2-1 1c0 1-1 1-1 2-6-2-15-2-22-1v-2h4c1 0 1-1 2-1 1 1 3 1 4 0h4l-1-1z" class="C"></path><path d="M671 508l1 3h4 10c2 0 3 1 5 1h9c4 0 7 3 11 3l16 1h-10c-1 0-3 0-5 1v-1c-3 0-7 0-9 1-9 0-18-1-27 1h-6v-1c0-1 0-2-1-3l-1-1 1-1 1-1 1-1v-2h0z" class="B"></path><path d="M671 508l1 3h4l1 1h1v2h0l1 1-4 1h-1c-1-1-1-1-2-1v1c-2-2 1-3-3-4l1-1 1-1v-2h0z" class="G"></path><path d="M548 376c3 0 4 1 6 2h0l-1 1v-1l-1 1c1 1 1 1 0 3v1h0l1 38h0-1c-1-1-2-4-4-5h0v-2c-2-7-1-15-1-22v-11h1v-3l1-1-1-1z" class="l"></path><path d="M553 421l1 2c3 3 6 6 10 9 1 1 3 2 5 3h1 2c3 0 4-2 7-3 1-1 3-1 5-1h1v1l1 2-1 2c-2 2-3 4-6 4h-11l4-1v-2h-1c-1-1-2-1-3 0h-1c0-1-1-1-2-2l-2 2h-3-3l-1-1c-2 0-3-1-4-2-1-3-1-6-1-9l-1-1 1-1 1-1 1-1h0z" class="B"></path><path d="M584 431h1v1c-1 0-2 1-3 2v1h-2-1c-1 1-1 0-2 1h-1c-2 0-3 0-4-1 3 0 4-2 7-3 1-1 3-1 5-1z" class="O"></path><path d="M553 421l1 2c-1 2-1 2-1 4v1h0c1 1 1 1 2 1v-2l2 2v1h1l1 1c-1 0-1 1-2 1l-1 2c2 0 2-1 4-1h0c-1 2-1 3-3 4l-1-1c-2 0-3-1-4-2-1-3-1-6-1-9l-1-1 1-1 1-1 1-1h0z" class="e"></path><path d="M553 428h0c1 1 1 1 2 1v-2l2 2v1h1l1 1c-1 0-1 1-2 1 0-1 0-1-1-1v1l-2 2-1-1v-4h-1l1-1z" class="E"></path><path d="M581 809c2 1 2 1 5 1 2 0 6 1 9 0h0c7 3 17 1 24 1h53 12s3 0 3 1c1 0 1 0 1 1-1 1-21 0-25 0l-80 1-3-1c0-2 0-2 1-4z" class="m"></path><path d="M607 429c1-1 2-1 3-1l1-1c1 0 2-1 2-2 1-1 0-1 1-2 0-2 1-1 2-2-1 3-2 5-1 8 0 1 1 2 1 4 0 1-1 1-2 2s-2 4-2 5c-3 7-9 13-15 16l2 3h-3 0l-2 1-3 1c-2-1-3-1-6 0v-2l4-2c2-1 4-2 5-3l1-1c2-1 7-6 8-8h1c1-3 2-5 2-8 0 0 1-7 1-8z" class="G"></path><path d="M594 454h3l1 1c-2 1-3 1-4 2h0l3-1 2 3h-3 0l-2 1-3 1c-2-1-3-1-6 0v-2l4-2c2-1 4-2 5-3z" class="B"></path><path d="M594 457l3-1 2 3h-3-2v-2z" class="H"></path><path d="M553 712c0 2 0 5 1 7s0 4 1 5v8c1 6 1 13 2 18s3 10 4 14c0 1 0 1 1 1 0 1-1 1-1 2h0c2 2 3 5 4 8 0 1 0 1 1 2h0 4c-1 3 1 6 3 8l3 4 1 1h4c-1 1-5 2-5 3v2c-2-2-5-3-7-6l-4-7-1-2 1-1h-1c-2-1-4-8-4-11-1-1-1-1-1-2h-2c1-4-1-11-2-16l-2-38z" class="f"></path><path d="M565 782c1 1 2 1 3 2 2 2 2 6 5 7l1-1c1 0 1 0 2-1l1 1h4c-1 1-5 2-5 3v2c-2-2-5-3-7-6l-4-7z" class="C"></path><path d="M676 482l15 1c7 0 12-1 18 3 3 1 6 1 9 1 2 0 14 0 15 1-5 0-11 0-15 1h-2c-3-1-7 0-10 0l-22 1-10-1h-10 0 10v-1h-5v-1h-1v-1l2-2c1 1 3 0 5 0 0 0 0-1 1-2z" class="D"></path><path d="M670 484c1 1 3 0 5 0v2c0 1-1 1-2 1v-1c-2 0-2 0-3-1v1l-1 1h-1v-1l2-2z" class="C"></path><path d="M561 805h3c1-1 1-1 2-1h1l-1-1c1-1 2-1 4-1l3 1h0c3 1 6 3 8 6-1 2-1 2-1 4l3 1h-5c0 2 0 3-1 5l-1 1 1 2h-4-1-4v-2l-3-1h-1-1-2c1-1 1-1 1-2-1-1-2 0-3 0h-1-3v-1c2-1 4-1 6-1 1-1 0-1 1-1h1c0-2 0-2-1-3h2c-1-1-3-2-4-2l-1-1 2-3z" class="X"></path><path d="M568 820h7v1l-2 1h-1-4v-2z" class="T"></path><path d="M573 803c3 1 6 3 8 6-1 2-1 2-1 4l3 1h-5-4l-1-1 2-2h0l-1-1v-2c-1 0-1-1-2-2-1 0-1 0-2-1l3-2z" class="L"></path><path d="M561 805h3c1-1 1-1 2-1h1l-1-1c1-1 2-1 4-1l3 1h0l-3 2c1 1 1 1 2 1 1 1 1 2 2 2v2c-2 0-4 0-5 2h0v1h0v2h5v1h-1c-3 0-5 1-8 3h-1-1-2c1-1 1-1 1-2-1-1-2 0-3 0h-1-3v-1c2-1 4-1 6-1 1-1 0-1 1-1h1c0-2 0-2-1-3h2c-1-1-3-2-4-2l-1-1 2-3z" class="P"></path><path d="M561 805h3c1-1 1-1 2-1h1l-1-1c1-1 2-1 4-1l3 1h0l-3 2-1 1v-1c-1 0-2 0-2 1-1 1-1 2-2 2h-2 0l2 1-1 1v1c-1-1-3-2-4-2l-1-1 2-3z" class="g"></path><path d="M529 846v-2c0-1 2-1 2-1 0-2-1-2 1-4v1h3c-1 2-1 3-3 4h6l1 1h-1v1h4l3 2-3 2v1 2c-2 0-4-1-6 0-1 2-1 6-1 8v2l-1 1-6 5c-8 5-8 20-10 29v-1c0-2 0-3-1-5h0c0-2 1-4 1-6l4-13c-1-2-1-5 0-7v-2h0l1-2v-2l2-4h0l1-2c-1-1-1 0 0-1 1 0 2 0 3-1l-1-1c0-2 1-3 1-4v-1z" class="c"></path><path d="M529 846v-2c0-1 2-1 2-1 0-2-1-2 1-4v1h3c-1 2-1 3-3 4h6l1 1h-1c-2 0-4 0-5 1 1 1 2 1 3 1h0c-2 1-4 1-5 3 0 2 1 1 1 4-1 0-1 0-2-1v1l1 1c0 1 0 1 1 1l-2 3h-1l-1-1h-1c-1-1 0-1 0-2s-1-2-1-2c-1-1-1 0 0-1 1 0 2 0 3-1l-1-1c0-2 1-3 1-4v-1z" class="K"></path><path d="M526 854s1 1 1 2-1 1 0 2h1l1 1h1l2-3 2 2v1l-1 1h-2l-1 1v2c0 1-1 1-1 2-2 0-2 0-4 2h0c0 2-3 4-3 6-1-2-1-5 0-7v-2h0l1-2v-2l2-4h0l1-2z" class="a"></path><path d="M532 856l2 2v1h-3-1l2-3z" class="U"></path><path d="M665 501l1 2h0 4v1c1 2 1 3 1 4v2l-1 1-1 1-1 1 1 1c1 1 1 2 1 3v1h-3v4c1 1 3 1 4 1h15c-4 2-11 1-15 1-1 0-2-1-4 0 0 1 0 1 1 2l2 1h-2v1c-2-2-1-4-4-4-6 0-12 0-17 1 0-1 0-1-1-2v-1l1-1c1-2 3-4 5-5 1-1 0-2 1-2 0-2 2-3 3-5 0-2 3-4 4-6 1 0 1-1 2-1 0-1 2-1 3-1z" class="C"></path><path d="M659 511h4c1 1 1 2 2 3l-1 1c-1 0-1-1-2-2l-1-1-2-1z" class="G"></path><path d="M664 519h2v4h-7-2c0-1 0-1 1-2v-1h1c2 0 3-1 5-1z" class="h"></path><path d="M665 501l1 2h0 4v1c1 2 1 3 1 4v2l-1 1h-2-5-4l2 1-3 2h1s1 0 1 1 0 2-1 2l-1 1c-2 2-5 2-8 3h-3c1-2 3-4 5-5 1-1 0-2 1-2 0-2 2-3 3-5 0-2 3-4 4-6 1 0 1-1 2-1 0-1 2-1 3-1z" class="N"></path><path d="M659 511l-1-1 2-2h1v-3c3 0 6-1 9-1h0c1 2 1 3 1 4v2l-1 1h-2-5-4z" class="O"></path><path d="M670 504h0c1 2 1 3 1 4v2l-1 1h-2v-1c-1 0-2-1-2-1-1-1-2-1-2-3 2 0 4 0 5-1l1-1z" class="h"></path><path d="M510 101l1-2 4 20-2 2-1 1v40 14 1c0 2 0 8-1 9h-3l-1-26v-8c0-5 0-10 1-15v-17-2c0-3 0-15 2-17z" class="H"></path><path d="M510 101l1-2 4 20-2 2-1 1v40 14 1-25-10c0-14 0-28-2-41z" class="J"></path><path d="M508 137c0 7-1 15 0 22v5h2c1-1 1-5 1-6l-1-1 1-1c0-1 0-3 1-4v25c0 2 0 8-1 9h-3l-1-26v-8c0-5 0-10 1-15z" class="B"></path><path d="M382 491h1c0 2 0 4 1 6s3 5 6 7c-1 1 0 1-1 2l1 1h0-1l-1 2h1c2 1 3 2 3 3l1 1c1 0 1 0 1 1v1l-3-1h-7-3 0v-1l-2 1v1c-1 0-2-1-2-1-2 0-3 0-4-1-2-1-3 0-5-1h-1v-2-4-5h0c1-1 2-4 3-4s2 1 2 0c1 0 3-1 4-2h0c0-1 1-2 1-4h5z" class="g"></path><path d="M383 509c1 0 2 0 3 1l-1 1 1 1h-3l-1 1h1c1 1 1 0 1 1h-3 0v-1l-2 1v1c-1 0-2-1-2-1-2 0-3 0-4-1-2-1-3 0-5-1h9 5l1-3z" class="d"></path><path d="M367 510c0-1 0-2 1-3s2 0 3 0h6 1c2 0 4 0 5 2l-1 3h-5-9-1v-2z" class="Y"></path><path d="M368 512v-5l1 1c3 1 6 0 8 2v2h-9z" class="h"></path><path d="M367 501c5 0 9 1 13 0 2 1 3 2 4 3-2 2-5 2-7 2l1 1h-1-6c-1 0-2-1-3 0s-1 2-1 3v-4-5h0z" class="B"></path><path d="M377 491h5v3c1 1 1 2 1 3h-1l-1 1c0 1 0 1 1 2h-1c0 1 0 0-1 1-4 1-8 0-13 0 1-1 2-4 3-4s2 1 2 0c1 0 3-1 4-2h0c0-1 1-2 1-4z" class="N"></path><path d="M382 494c1 1 1 2 1 3h-1l-5 1c-1 0-1 0-2-1 1-1 1-1 1-2h0 2c2 0 2 0 4-1z" class="E"></path><path d="M377 491h5v3c-2 1-2 1-4 1h-2c0-1 1-2 1-4z" class="F"></path><path d="M551 450c2 2 4 5 6 8l1-1h0c3 0 6-1 9 0h1s0-1 1-1l4-5 1 1c-1 0-1 1-2 2h-1l1 1c0-1 1-1 2-2 0-2 2-4 3-5h1c0 3-5 7-6 9l1 1h0l-2 1h-1c6 1 13-2 19-2l-4 2v2c3-1 4-1 6 0l3-1 1 1c0 2 0 2-2 3l-1 1c-1 0-2 1-2 3l-10-1-8-1h-7v2 1l-1 1 2 6v1c0 1-1 1-1 2l1 1-1 2h-1v-4c-1-2-1-2-3-2-1-4-2-8-3-11 0-2-1-3-2-4 0-2 0-2-1-4-2-2-4-4-6-7v-1l2 1z" class="W"></path><path d="M570 459c6 1 13-2 19-2l-4 2v2c3-1 4-1 6 0l3-1 1 1c0 2 0 2-2 3l-1 1c-1 0-2 1-2 3l-10-1-8-1h-7v2 1l-1 1-1-5h6l1-1h1l-1-1 1-1v-1h-3 0l2-2z" class="J"></path><path d="M585 459v2c3-1 4-1 6 0-5 2-13 5-17 4h0c1-1 1-1 3-1v1l1-1-1-2h2c2-1 4-1 6-3z" class="Y"></path><path d="M585 459v2c-1 1-3 1-5 2h-1v-1c2-1 4-1 6-3z" class="H"></path><path d="M570 459c6 1 13-2 19-2l-4 2c-2 2-4 2-6 3h-2l1 2-1 1v-1c-2 0-2 0-3 1h0-5l1-1h1l-1-1 1-1v-1h-3 0l2-2z" class="Q"></path><path d="M646 481h0l2 1h3 0c6 0 13 1 18 0h7c-1 1-1 2-1 2-2 0-4 1-5 0l-2 2v1h1v1h5v1h-10 0 10c-1 1-2 1-2 2-1 1-1 0-1 1-2 0-4-1-5 0l-1 1c-1 1-2 0-3 1-1 0-2 0-3 1l-1 1h1c-1 1-1 2-2 3 1 1 1 1 2 0v1c0 1 0 2-1 3-1-1-1 0-1-1l-3 3v-5l-3 4v-2h1l-1-1c-1 0-1 0-2 1s-1 1-2 1l-1-1 2-2s-1 0-1-1c-2-1-2-3-2-5 0-1-1-1-1-2h-1l1-1-1-2-2-2c1-1 1-1 3-2v-1l2-1v-2z" class="N"></path><path d="M653 494h0-3l1-2h-1v-2c2-1 4-1 7-1-1 1-2 2-4 2v3z" class="E"></path><path d="M657 489h7 0c0 1-1 1-1 2-2 0-2-1-3 0-2 1-3-1-5 1 2 2 3 0 4 3l-1 1-1-1c-2-1-3-1-4-1v-3c2 0 3-1 4-2z" class="M"></path><path d="M664 489h10c-1 1-2 1-2 2-1 1-1 0-1 1-2 0-4-1-5 0l-1 1c-1 1-2 0-3 1-1 0-2 0-3 1-1-3-2-1-4-3 2-2 3 0 5-1 1-1 1 0 3 0 0-1 1-1 1-2z" class="H"></path><path d="M654 484c1 0 1-1 2 0h1 6 1 4v1l2-1-2 2v1h1v1c-3 0-8 1-10 0 0-1 0-2-1-2l-1 1-1-1-2-2z" class="B"></path><path d="M651 482c6 0 13 1 18 0h7c-1 1-1 2-1 2-2 0-4 1-5 0l-2 1v-1h-4-1-6-1c-1-1-1 0-2 0h0c-2 0-3 0-4 1s0 0-1 0l2-3h0z" class="Q"></path><path d="M646 502l2-2s-1 0-1-1c-2-1-2-3-2-5 0-1-1-1-1-2h-1l1-1-1-2-2-2c1-1 1-1 3-2v3l4 1-2 2v1h2v1c-1 1 0 2 1 2l1 1 1 1 2 1h2c0 1 0 2-1 2l-3 4v-2h1l-1-1c-1 0-1 0-2 1s-1 1-2 1l-1-1z" class="b"></path><path d="M504 162c1 1 1 1 2 1l1-3 1 26h-2c-2 1-4 0-6 0l-2 1-1-1c-4 1-9-1-13-1h-1 1l1-1c0-1 1-2 1-4h0c1-1 1-3 1-4 2-5 5-6 9-9v-1l1-1h1v-4c1 0 4 1 5 1h1z" class="C"></path><path d="M502 166h4c0 2 1 11 0 12-1-1-1-1-3-1l-1-1v-3c-1-1-1-1-3-2-2 2-5 3-6 6l3 2h-1c-1 0-1 0-2-1l-1-1h-1c0-2 0-3 1-4 1-2 3-3 4-4 3 0 4 0 6 1l1 1 1-2v-1l-2-2z" class="Q"></path><path d="M494 183h3 1l1-1 2 1-1-1 2-3c1 1 1 1 1 2h1c1-1 1-1 1-2h1v5 2c-2 1-4 0-6 0l-2 1-1-1c-4 1-9-1-13-1h-1 1l1-1c0-1 1-2 1-4h0c1 1 1 2 3 3h0c1 1 2 1 3 1h1l1-1z" class="o"></path><path d="M496 166h6l2 2v1l-1 2-1-1c-2-1-3-1-6-1-1 1-3 2-4 4-1 1-1 2-1 4h1v1c0 1 1 2 2 3h2v1h-3l1 1-1 1h-1c-1 0-2 0-3-1h0c-2-1-2-2-3-3 1-1 1-3 1-4 2-5 5-6 9-9v-1z" class="Y"></path><path d="M563 437l2-2c1 1 2 1 2 2h1c1-1 2-1 3 0h1v2l-4 1h11c3 0 4-2 6-4h1c-1 1-1 2-2 3l-1 2c-2 2-4 5-5 7h-1c-1 1-3 3-3 5-1 1-2 1-2 2l-1-1h1c1-1 1-2 2-2l-1-1-4 5c-1 0-1 1-1 1h-1c-3-1-6 0-9 0h0l-1 1c-2-3-4-6-6-8-1-2-3-3-3-5v-1c1-1 0-3 1-5l1-1v1l1-1v-1l2 1v-1c1 0 2 0 3-1l1 1h3 3z" class="h"></path><path d="M563 437l2-2c1 1 2 1 2 2h1c1-1 2-1 3 0h1v2l-4 1c-2 0-4-1-7 0 0 1 0 2 1 3l-1 1c-2-1-3-2-5-3l-4 4c0 2 0 2-1 3l-3-3v-1c1-1 0-3 1-5l1-1v1l1-1v-1l2 1v-1c1 0 2 0 3-1l1 1h3 3z" class="I"></path><path d="M560 437h3c1 0 2 1 3 2-2 1-5 0-8 0l2-2z" class="L"></path><path d="M556 436l1 1h3l-2 2c-1 2-2 1-4 2v-1c-1-1 0-1-1-1v-1h-2v-1l2 1v-1c1 0 2 0 3-1z" class="Z"></path><path d="M551 438h2v1c1 0 0 0 1 1v1c-1 1-1 1-2 3v1c0 2 0 2-1 3l-3-3v-1c1-1 0-3 1-5l1-1v1l1-1z" class="L"></path><defs><linearGradient id="A" x1="632.319" y1="446.039" x2="616.198" y2="439.112" xlink:href="#B"><stop offset="0" stop-color="#928d7b"></stop><stop offset="1" stop-color="#aba898"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M613 457v-1c6-5 11-10 15-17 4-5 6-11 9-16l6-12 5-16h0l-2 10c0 1 0 0-1 1v1h1c0-1 1-2 1-3 1-4 2-7 3-10 0-2 0-4 2-6-1 6-2 14-5 21-2 2-2 3-2 5v1c-1 1-1 1-1 2s0 2-1 4l1 1c-6 15-16 29-29 40-3 2-6 5-10 7-1 0-1 0-1 1v1l-4-2-10-1c0-2 1-3 2-3l1-1c2-1 2-1 2-3l-1-1 2-1h0 3 3l6 1c2-1 3-2 5-3z"></path><path d="M613 457c-1 4-7 7-10 9-1 1-2 1-3 2 1 1 2 0 3 1l1 1v1l-4-2-10-1c0-2 1-3 2-3l1-1c2-1 2-1 2-3l-1-1 2-1h0 3 3l6 1c2-1 3-2 5-3z" class="P"></path><path d="M602 459l6 1c-2 1-3 2-5 3h-3c1-1 1-2 2-3v-1z" class="S"></path><path d="M594 460l2-1 2 1v3l-2 1 1 1h3l1 1c-2 1-3 0-3 1s1 2 2 2l-10-1c0-2 1-3 2-3l1-1c2-1 2-1 2-3l-1-1z" class="K"></path><path d="M566 575c1 0 2 1 2 2l2 1c-1 1-1 1-1 2-2 6-4 12-5 18-3 8-4 18-7 26-1 2-1 4-1 6l-1 15v4h1c2 1 3 1 4 0 1 1 0 1 1 2 0 2 4 6 5 8-1-1-3-1-4-2h-1l-9 5c0-2 0-3 1-4v-1h-2c0-1-1-2-1-3h1l1 1c2-2 1-3 1-4-1-6 0-11 0-16v-37-6c-1-2-1-4-1-5 1-1 1-2 1-3l-1-1c0-1 1-1 0-2 2-1 3-1 4-2l1 1c1-1 2-2 4-3l1 1 1-1 1-1h1l1-1z" class="I"></path><path d="M564 580h1v2c-1 2-2 5-3 7 0 2-1 4-1 6-1 2-1 4-2 6l-1 4c-1 0-1 0-1-1 0-3 1-5 2-8 0-4 2-7 3-10 0-3 1-4 2-6z" class="F"></path><path d="M553 651l1-1h1 2c-1 2-2 1-2 3v1l-1-1v4c1-1 2-1 3-2h1c1 1 1 1 2 1s1 0 1 1l-9 5c0-2 0-3 1-4v-1h-2c0-1-1-2-1-3h1l1 1c2-2 1-3 1-4z" class="T"></path><path d="M556 579l1 1c1-1 2-2 4-3l1 1 1-1v1c-1 2-3 3-3 6-1 2-2 3-2 5-1 4-2 8-2 11-1 4-1 7-2 10 0 3 1 5 1 8s-1 7-1 11c0 2 0 4-1 6v-37-6c-1-2-1-4-1-5 1-1 1-2 1-3l-1-1c0-1 1-1 0-2 2-1 3-1 4-2z" class="S"></path><path d="M556 579l1 1c1-1 2-2 4-3l1 1-6 5-2 2c-1 1 0 3 0 4-1 3 0 6-1 9v-6c-1-2-1-4-1-5 1-1 1-2 1-3l-1-1c0-1 1-1 0-2 2-1 3-1 4-2z" class="U"></path><path d="M566 575c1 0 2 1 2 2l2 1c-1 1-1 1-1 2-2 6-4 12-5 18-3 8-4 18-7 26-1 2-1 4-1 6l1-14v-1c-1-4-1-7 0-11 0 1 0 1 1 1l1-4c1-2 1-4 2-6 0-2 1-4 1-6 1-2 2-5 3-7v-2h-1c0-2 0-2 1-3v-1l1-1z" class="O"></path><path d="M559 601l1 3-3 12v-1c-1-4-1-7 0-11 0 1 0 1 1 1l1-4z" class="C"></path><path d="M566 575c1 0 2 1 2 2l-8 27-1-3c1-2 1-4 2-6 0-2 1-4 1-6 1-2 2-5 3-7v-2h-1c0-2 0-2 1-3v-1l1-1z" class="E"></path><path d="M495 824c1 1 2 1 3 2l1-1-1-1h2c0 2 1 4 0 6h-2v1h2c1 1 1 1 1 3l-3 1h1 2v5 4c0 2 0 2 1 4-1 1-3 1-4 1l-1 1h0l1 3h-4l-1 1c-1-1-1-1-2-1s0 0-1-1v-1h-2-3l-2-2-1-1v1c-2-1-3 0-6-1l1-1 1-1c1-1 1-1 1-2-1-1-2-4-3-4-3-1-7-2-9-1h-2l3-2c-1-1-3-1-4-2-2-1-3-2-5-4h1 0c1-1 1-2 1-3 1 1 3 1 4 1h5c2 0 3-1 5 0v-2h4l1 1h7v-1h-2l1-1h2c1-1 1-1 3-1 1 1 0 1 1 0v-1h3z" class="j"></path><path d="M487 831h5l1 1-2 1h-2-1c1 1 1 1 2 1h1l-1 1c-1 0-2 0-3-1v-3z" class="P"></path><path d="M465 829h5c2 0 3-1 5 0h4 3c-1 1-2 2-4 2l-1-1h-1-1c-2 1-3 1-5 2h-1v-1-1c-1 1-1 1-2 1l-2-2z" class="S"></path><path d="M482 829c1 0 2 0 3 1v1l1 1v1h-3-1-1c-1 0-3 2-5 2-2-1-6 0-7-1h-3l-1-1h-1l2-1s1 0 2 1c1 0 1 0 2-1 2-1 3-1 5-2h1 1l1 1c2 0 3-1 4-2z" class="Z"></path><path d="M482 829c1 0 2 0 3 1v1c-1 0-2 0-3 1h-2-1l-1 1-3-3h1 1l1 1c2 0 3-1 4-2z" class="P"></path><path d="M500 831c1 1 1 1 1 3l-3 1h1 2v5 4c0 2 0 2 1 4-1 1-3 1-4 1l-1 1h0l1 3h-4l-1 1c-1-1-1-1-2-1s0 0-1-1v-1h-2-3l-2-2-1-1v1c-2-1-3 0-6-1l1-1 1-1c1-1 1-1 1-2l1-1h4v-1h-1c-1-1-1-2-3-2-1 0-2 0-3-1 1-1 2-1 4-2 4-1 10-1 14-1v-1h-1l-1-1h4l-1-1c1-2 3-1 4-2z" class="Z"></path><path d="M497 850l1 3h-4l-1 1c-1-1-1-1-2-1s0 0-1-1v-1l7-1z" class="p"></path><path d="M479 844l1-1h4l1 1c-1 1-2 0-2 1 1 0 2 1 2 2h-5c-1 0-1 0-2-1 1-1 1-1 1-2z" class="E"></path><path d="M501 844c-1-1-2-1-3-1-1-1 1 0-1-1l-1 1h-2c-1 0-1 0-2-1 0 1 0 1-1 2v-1c0-1 0-2 1-2h1 0c2 0 3 0 4-1 2 0 2 1 4 0v4z" class="L"></path><path d="M483 849l2-1 2 1h0c1-1 2-1 3-1 3 0 4-1 6 0v1c-2 0-3 1-4 1-2 0-3 0-4 1h-3l-2-2z" class="X"></path><path d="M397 465v11l6-1 7-1c3-2 5-3 9-4v1l-1 1c-2 0-3 2-4 3-1 2-3 3-4 4-1 2-2 5-4 6v1c0 1-1 1-1 2l1 2v1l2 2c0 1-1 2 0 3 1 2 1 4 3 5l1 2-1 1h-1c0 1 0 1 1 1h-1c-1 0-2 0-3-1v1l2 3v1l-1 1-6-1v1h-1l-2-1c-2 0-3-1-4-1s0 0-1 1l-1-2-2 1-1-1h0l-1-1c1-1 0-1 1-2-3-2-5-5-6-7s-1-4-1-6v-1l1-2v-1c1-2 2-4 4-5 1-3 6-4 9-5l-1-1 1-11z" class="Q"></path><path d="M390 504c4 2 7 3 12 4v1 1h-1l-2-1c-2 0-3-1-4-1s0 0-1 1l-1-2-2 1-1-1h0l-1-1c1-1 0-1 1-2z" class="T"></path><path d="M410 474c3-2 5-3 9-4v1l-1 1c-2 0-3 2-4 3-1 2-3 3-4 4-1 2-2 5-4 6v1c0 1-1 1-1 2l1 2v1 4l-1 1 2 1v1c0 1 0 1 1 2h-1l-1 1h-1v2l-1-2c0-1 0-1-1-2-1 0-1-1-2-2 0 0 1 0 0-1h-2v3c-1-1-1-1-1-3l-1-1v-3c1-2 0-3 0-4h-1v-1c0-1 1-2 1-3 1-1 2-2 4-3 0 0 2-2 3-2l3-2h0l3-3z" class="H"></path><path d="M402 490c0 1 2 2 2 3 0 2 0 2 1 4v1c0 1 1 2 2 2l-1 1h-1v2l-1-2c0-1 0-1-1-2 0-1 0-1-1-2v-2-5z" class="M"></path><path d="M401 481s2-2 3-2l3-2-4 7c0 2-1 4-1 6v5 2c1 1 1 1 1 2-1 0-1-1-2-2 0 0 1 0 0-1h-2v3c-1-1-1-1-1-3l-1-1v-3c1-2 0-3 0-4h-1v-1c0-1 1-2 1-3 1-1 2-2 4-3z" class="i"></path><path d="M397 465v11l6-1 7-1-3 3h0l-3 2c-1 0-3 2-3 2-2 1-3 2-4 3 0 1-1 2-1 3l-1 2h0-1c-1 1-1 1-1 2v3c-1 1-1 1-1 2l-1 1c1 1 2 2 3 2l2 2h1c1 0 2 1 4 2 0 0 0 1 1 2l-1 1h-2c0-1 0-1-2-2v1l-8-4v-1c0-1-1-2-2-3 0-2 0-2-1-3-1 1-1 0-1 1s0 1-1 2c-1-2-1-4-1-6v-1l1-2v-1c1-2 2-4 4-5 1-3 6-4 9-5l-1-1 1-11z" class="B"></path><path d="M393 491h-2l-1-1 1-1c1 0 2-1 3-1l-1-2 1-1c0-1 2-1 3-1 0 1-1 2-1 3l-1 2h0-1c-1 1-1 1-1 2z" class="M"></path><path d="M410 474l-3 3h0l-3 2c-1 0-3 2-3 2h-4l1-1h1c2-1 3-3 4-5l7-1z" class="W"></path><path d="M397 477v1c0 1-1 2-2 3h-2l-1 1 1 1h-1c-2 2-3 4-6 5 0 1 0 2 1 3l1 1h0l-2-1-3-1 1-2v-1c1-2 2-4 4-5 1-3 6-4 9-5z" class="O"></path><path d="M384 488l2 1v2l-3-1 1-2z" class="H"></path><path d="M538 815v-1c2-1 2-2 4-2 2-1 8 0 8-1 1 0 0 0 2-1h0c2 1 2 1 3 0 2 1 5 1 7 1 1 1 1 1 1 3h-1c-1 0 0 0-1 1-2 0-4 0-6 1v1h3 1c1 0 2-1 3 0 0 1 0 1-1 2h2 1 1l3 1v2h4l-4 4c-1 0-1-1-2-1l-1-1c-1 1-2 1-3 1-2 0-4 1-6 1 1 0 1 1 2 1h1l-1 1-2 1c-1 1-1 2-2 3-1 0-2 1-3 2l1 2-2 1v2c-2 1-4 1-5 2s-2 3-3 4v1h0-4v-1h1l-1-1h-6c2-1 2-2 3-4h-3v-1c-2 2-1 2-1 4 0 0-2 0-2 1v2 1h-4v-1h-1c-2-1-2-1-3-1v-2-1c2 0 2-1 3-2 0-1-1-1-3-2 0-1 0-1 1-2h1v-1h2c-1-1-2-1-3-2l1-1-1-1 1-2v-1-2c1-1 2-1 4-2v-3l1 1 1-1c-1-1-2-2-2-3l1-1c0 1 1 2 3 3h1v-2c1 0 3 1 5 1v-2l1-2z" class="r"></path><path d="M529 839h-3c0-1 1-2 2-3 0 1 0 1 1 1h0v2z" class="b"></path><path d="M532 821h3v1l1 1c-1 1-1 1-1 2v1l-3 1-1 1c4 0 7-1 10-1 5-1 9-1 13-2 4 0 7-1 11-1h0c-1 1-2 1-3 1-2 0-4 1-6 1h-4l-2 1h-3c-3 0-5 1-7 1l-5 1c0 2 1 2 0 4-1 1-2 2-3 4h3l-2 1v1c1 0 2 0 3 1h-1-3v-1c-2 2-1 2-1 4 0 0-2 0-2 1v2l-2-1v-1l2-1v-2l1-1-1-1v-2-2s1 0 2-1h-3c-1-1 0-1-1-2 1-2 2-2 4-2v-1h0c-1-1-2 0-4-1v-1c2 0 3-1 4-2l-2-2h1l1-1v-1h1z" class="J"></path><path d="M530 823l1-1v-1h1c1 1 3 2 3 2-2 1-3 1-4 1l-1-1z" class="b"></path><defs><linearGradient id="C" x1="539.486" y1="821.055" x2="565.649" y2="826.27" xlink:href="#B"><stop offset="0" stop-color="#4d4b42"></stop><stop offset="1" stop-color="#7b7764"></stop></linearGradient></defs><path fill="url(#C)" d="M559 817c1 0 2-1 3 0 0 1 0 1-1 2h2 1 1l3 1v2h4l-4 4c-1 0-1-1-2-1l-1-1h0c-4 0-7 1-11 1-4 1-8 1-13 2-3 0-6 1-10 1l1-1 3-1h3c1-1 2-1 3-3h-2l2-1h1 4s1-1 2-1h0l7-1h0c2-1 3-2 4-3z"></path><path d="M559 817c1 0 2-1 3 0 0 1 0 1-1 2h2 1 1l3 1v2c-2-2-5-2-8-1h0-4c-1 1-1 1-2 1v-1h1v-1h0c2-1 3-2 4-3z" class="g"></path><path d="M538 815v-1c2-1 2-2 4-2 2-1 8 0 8-1 1 0 0 0 2-1h0c2 1 2 1 3 0 2 1 5 1 7 1 1 1 1 1 1 3h-1c-1 0 0 0-1 1-2 0-4 0-6 1v1h3 1c-1 1-2 2-4 3h0l-7 1h0c-1 0-2 1-2 1h-4-1l-2 1h2c-1 2-2 2-3 3h-3v-1c0-1 0-1 1-2l-1-1v-1l2-2v-2l1-2z" class="c"></path><path d="M558 817c-3 1-5 2-8 1v-1c0-1 1-1 2-1l2-1 1 1v1h3z" class="g"></path><path d="M538 815l2 1h1 1l-1 3h-1c-2 2-2 1-2 4h0 1 2c-1 2-2 2-3 3h-3v-1c0-1 0-1 1-2l-1-1v-1l2-2v-2l1-2z" class="K"></path><path d="M547 827h3l2-1h4c1 0 1 1 2 1h1l-1 1-2 1c-1 1-1 2-2 3-1 0-2 1-3 2l1 2-2 1v2c-2 1-4 1-5 2s-2 3-3 4v1h0-4v-1h1l-1-1h-6c2-1 2-2 3-4h1c-1-1-2-1-3-1v-1l2-1h-3c1-2 2-3 3-4 1-2 0-2 0-4l5-1c2 0 4-1 7-1z" class="T"></path><path d="M554 832c-1 0-3-1-4 0h0c-1-1-2-1-2-1 1-1 0-1 1-1l1-1h1 5c-1 1-1 2-2 3zm-7-5h3l2-1h4c1 0 1 1 2 1h1l-1 1h-1-2-8v-1z" class="g"></path><path d="M535 833c1-2 0-2 0-4l5-1v1h2l1 3c1 0 0 0 1-1h3l-1 1h0c-2 1-4 1-6 1 2 1 3 1 5 1h1c1-1 1-2 3-2h1c-1 1 0 1-1 1-1 1-2 1-3 2l1 1c-1 1-1 0-3 0h-5v1l-2 2 1 1h-2c-1-1-2-1-3-1v-1l2-1h-3c1-2 2-3 3-4z" class="V"></path><path d="M535 833l1 1-1 1c1 1 3-1 4 1l-3 2-1-1h-3c1-2 2-3 3-4z" class="K"></path><path d="M547 836c1 0 3-1 4-2l1 2-2 1v2c-2 1-4 1-5 2s-2 3-3 4v1h0-4v-1h1l-1-1h-6c2-1 2-2 3-4h1 2l-1-1 2-2v-1h5c2 0 2 1 3 0z" class="g"></path><path d="M536 840h2l-1-1 2-2v-1h5l-2 1c1 1 0 1 1 1v2h-1c-1 0-1 1-2 1h-2v1h2l-1 1h-1v1h-6c2-1 2-2 3-4h1z" class="T"></path><path d="M461 801h0c1 0 2 0 3-1v1c3 1 6 1 10 2 8 2 17 4 25 4v1c-2 1-11-1-14-1h0 0c2 1 3 1 5 1 3 0 5 1 8 1 0 0 1 1 2 1v9 5h-2l1 1-1 1c-1-1-2-1-3-2h-3v1c-1 1 0 1-1 0-2 0-2 0-3 1h-2l-1 1h2v1h-7l-1-1h-4v2c-2-1-3 0-5 0h-5c-1 0-3 0-4-1 0 1 0 2-1 3h0-1l-4-6c-1-1-1-1-2-1v2h-1l-5-3c1-2 1-2 2-3 2 1 4 1 6 1 0-2-1-3-2-5v-1l-1-1h-1v-1-1l-2 1-1-1c1-2 5-5 7-6l4-4 2-1z" class="p"></path><path d="M462 802h1c1 2 1 2 1 3-4 1-6 5-8 8l1 1-1 1c0-1-1-1-1-1 0-1 0-2 1-2 1-2 2-3 3-5 0-1 0-2 1-2 0-1 1-1 2-2v-1z" class="N"></path><path d="M459 826c5 1 11 2 16 1v2c-2-1-3 0-5 0h-5c-1 0-3 0-4-1l-2-2z" class="c"></path><path d="M452 814c2 1 3 2 6 2l1 1h0c1 0 1 0 2 1h-3v1c1 0 5 0 6 1h-1v1 1l-2-1h-1-2-1-2c0-2-1-3-2-5v-1l-1-1z" class="G"></path><path d="M487 819h1 2v2 1h1c0 1 0 1 1 2l-2 1c-2-2-5 0-7-2-1-1-2-1-3-2v-1l1-1c1 0 1 0 3-1h0c1 1 2 1 3 1z" class="L"></path><path d="M487 819h1c1 1 1 2 1 3h-3c0-1 0-1-2-1l1-1v-1c-2 1-1 1-2 2l1 1-1 1c-1-1-2-1-3-2v-1l1-1c1 0 1 0 3-1h0c1 1 2 1 3 1z" class="Z"></path><path d="M447 823c1-2 1-2 2-3 2 1 4 1 6 1h2l5 1h2c1 0 0 0 2 1h0l1 1h-2 0-1c-1 0-1 0-2-1h-1-1c-1-1-4-1-6-1 2 2 4 1 6 2h2l1 1h-2-3l1 1h0l2 2c0 1 0 2-1 3h0-1l-4-6c-1-1-1-1-2-1v2h-1l-5-3zm22-15h3l-1-2 2 1c0-1 0-1 1-2-1 0-2 0-3-1h0c1 0 2 0 4 1v1l7 1 1 1c-1 1-1 2-2 3 1 0 0 0 1 1l-3 3c1 1 2 2 3 2l2 1c-2 1-2 1-3 1l-1 1v1h-2v-1c-2-2-6-1-8-1-1-1-1-1-2-1h-3c-1-1 0-1-1-1h-1c-1-2-3-3-4-4l1-1h1v-3c1-1 1-2 3-3h0 2 1l2 2h0z" class="L"></path><path d="M469 808h3l-1-2 2 1c0-1 0-1 1-2-1 0-2 0-3-1h0c1 0 2 0 4 1v1l7 1 1 1c-1 1-1 2-2 3 1 0 0 0 1 1l-3 3h-1l1-2v-1l-1 1h-1-2c-1 0-1 1-3 2v-1-1-1h-1-1l1-1c-1-1-1-2-2-3z" class="X"></path><path d="M475 805c1-1 1-1 2 0 2 1 6 0 8 2h0 0c2 1 3 1 5 1 3 0 5 1 8 1 0 0 1 1 2 1v9 5h-2l1 1-1 1c-1-1-2-1-3-2h-3 0c-1-1-1-1-1-2h-1v-1-2h-2-1c-1 0-2 0-3-1h0l-2-1c-1 0-2-1-3-2l3-3c-1-1 0-1-1-1 1-1 1-2 2-3l-1-1-7-1v-1z" class="S"></path><path d="M492 815l1 1h1 0l-1 1v1l-1-1c-1 1-2 1-3 1l3-3z" class="Z"></path><path d="M500 810v9l-1 1-3-2v-1c1 1 1 1 3 1v-1h-1v-1-1h-1l-2-2 2-2c0 1 1 1 2 1l1-2z" class="P"></path><path d="M490 821l2-2 1 1h2c1 0 2 1 3 1l1-1 1-1v5h-2l1 1-1 1c-1-1-2-1-3-2h-3 0c-1-1-1-1-1-2h-1v-1z" class="L"></path><path d="M500 819v5h-2c0-1 0-2-2-2-1-1-1-1-1-2 1 0 2 1 3 1l1-1 1-1zm-10 2l2-2 1 1 1 3h0l1 1h-3 0c-1-1-1-1-1-2h-1v-1z" class="Z"></path><path d="M482 812h2l-1 2h1c1 0 1-1 3-2 1 0 1 0 3 1l-1 3c0 1-1 2-2 3-1 0-2 0-3-1h0l-2-1c-1 0-2-1-3-2l3-3z" class="a"></path><path d="M647 409h0c1 0 2-1 2-1h0v1c-1 1-2 2-2 4-1 3-1 8-1 11v24c0 4 1 8 0 12v4 3s1 0 1-1c-1 2-3 4-4 6h0l3-1c1 1 0 4 0 6v4h0c-2 0-4 0-6 1h-5-1l-2-2-2-1-5-1c-3-1-7-3-11-3-3-1-7-3-10-4v-1c0-1 0-1 1-1 4-2 7-5 10-7 13-11 23-25 29-40l-1-1c1-2 1-3 1-4s0-1 1-2v-1c0-2 0-3 2-5z" class="m"></path><path d="M647 409h0c1 0 2-1 2-1h0v1c-1 1-2 2-2 4-1 3-1 8-1 11v24c0 4 1 8 0 12h0v-16l-9 9-13 13c-2 1-4 3-5 3l27-25v-26c-1 1-1 3-2 4l-1-1c1-2 1-3 1-4s0-1 1-2v-1c0-2 0-3 2-5z" class="E"></path><defs><linearGradient id="D" x1="633.685" y1="472.212" x2="644.815" y2="471.288" xlink:href="#B"><stop offset="0" stop-color="#393a33"></stop><stop offset="1" stop-color="#55524a"></stop></linearGradient></defs><path fill="url(#D)" d="M646 460h0v4 3s1 0 1-1c-1 2-3 4-4 6h0l3-1c1 1 0 4 0 6v4h0c-2 0-4 0-6 1h-5-1l-2-2-2-1 16-14c-6 1-13 0-20 1h-1c1-2 4-2 6-2 4 0 11 1 15-1v-3z"></path><path d="M643 472l3-1c1 1 0 4 0 6v4h0c-2 0-4 0-6 1h-5-1l-2-2 11-8z" class="n"></path><path d="M659 496h1 1c2 0 2 0 3 2v2l1 1h0c-1 0-3 0-3 1-1 0-1 1-2 1-1 2-4 4-4 6-1 2-3 3-3 5-1 0 0 1-1 2-2 1-4 3-5 5l-1 1v1c1 1 1 1 1 2l-1 1c0 2-1 7 1 9v1l-1 1v84c-5-18-15-37-27-52-3-5-7-8-10-12l-8-12-1-1c2 0 3-1 5-2 2 0 3-1 5-2l1-1c1-1 2-1 2-2 1 0 2-1 3-1h1l4-5h0c1 0 2 0 3-1 3-2 6-4 10-6 2-2 3-4 5-6 1-1 3-2 5-3l10-10 3-3c0 1 0 0 1 1 1-1 1-2 1-3v-1c-1 1-1 1-2 0 1-1 1-2 2-3z" class="m"></path><path d="M659 496h1 1c2 0 2 0 3 2v2l1 1h0c-1 0-3 0-3 1-1 0-1 1-2 1-1 2-4 4-4 6-4 4-8 7-11 10-10 8-21 15-32 20v1c4 2 7 6 10 9h0c-3-3-6-6-10-8 0-1 0-1-1-1h-2l1-1c1-1 2-1 2-2 1 0 2-1 3-1h1l4-5h0c1 0 2 0 3-1 3-2 6-4 10-6 2-2 3-4 5-6 1-1 3-2 5-3l10-10 3-3c0 1 0 0 1 1 1-1 1-2 1-3v-1c-1 1-1 1-2 0 1-1 1-2 2-3z" class="C"></path><path d="M661 496c2 0 2 0 3 2v2l-1-1h-2l-1-1v-2h1z" class="B"></path><path d="M498 721h1c0 2-1 5 0 6l1 27c0 4-1 9 0 13 1 0 1 0 2 1h2l-2 2 2 2c1 1 2 1 3 2h1v3h3l1 1v-1-4c1 2 1 4 1 7v2 3 8h1v2l1-1h1l1 3 2 2v-1-7c1-2 1-2 1-4-1-1 0-2 1-3 0 2 0 5-1 7v4 9h1v4l-2 24 1 4v-2c1 2 0 5 0 7s0 3 1 5v-1c1 0 1 0 3 1h1v1h4c0 1-1 2-1 4l1 1c-1 1-2 1-3 1-1 1-1 0 0 1l-1 2h0l-2 4v2l-1 2h0v2c-1 2-1 5 0 7l-4 13c0 2-1 4-1 6h0c1 2 1 3 1 5v1l-1 5-3 19v2c-1 6-2 12-4 18-1-1-1-1-1-2l-7-45 1-2-1-8 1-6v-3-10l-1-4v-9c-1-1-2 0-4 0l-1-3h0l1-1c1 0 3 0 4-1-1-2-1-2-1-4v-4-5h-2-1l3-1c0-2 0-2-1-3h-2v-1h2c1-2 0-4 0-6v-5-9c-1 0-2-1-2-1-3 0-5-1-8-1-2 0-3 0-5-1h0 0c3 0 12 2 14 1v-1h2v-2c0-4-1-8-1-13 0-3 0-8-1-11v-2l-1-10c1-1 1-1 0-3h0c1-4 0-10 0-14v-31z" class="k"></path><path d="M508 894c3 3 3 6 3 10 0 2 0 5-2 7h0c-1-3-1-7-1-10 0-2-1-5 0-7z" class="E"></path><path d="M511 837v-3c0 4 1 8 0 12v-4c-1 1-1 2-1 3h0c0 2-1 3-1 4 1 1 0 2 0 2-1 5 0 13 0 18 0 2 0 5-1 7v-32-5-3c1 0 2 1 3 1z" class="G"></path><path d="M508 836c1 0 2 1 3 1v2h-3v-3zm0-41c-1-1 0-3 0-4 0-4-1-10 0-13h1v1c1 1 2 3 3 4l-1 2c1 2 1 9 1 12l-4-2z" class="D"></path><path d="M517 892c1 2 1 3 1 5v1l-1 5-3 19v2h-1v-1c1-2 0-3 0-4 1-1 1-2 1-4v-2-1l1-1v-3h0v-1c-1 1-1 8-2 10h0c0-5 0-10 1-15 0-2 1-4 2-7l1-3z" class="b"></path><path d="M517 892c1 2 1 3 1 5v1l-1 5c-1-3-1-5-1-8l1-3z" class="V"></path><path d="M504 864v-2l2 3c0 1 0 1-1 2h0l2 2v24h-1c-2-3-1-11-2-15s0-10 0-14z" class="L"></path><path d="M503 844l3 1c-1 0-1 0-2 2l2-2 1 24-2-2h0c1-1 1-1 1-2l-2-3v2l-1-20z" class="j"></path><path d="M504 878c1 4 0 12 2 15h1l1 1c-1 2 0 5 0 7 0 3 0 7 1 10l-1 1c-2-3-2-9-2-13l-1 1-2-7-1-8 1-6c1 0 1 0 1-1z" class="Z"></path><path d="M508 876c1-2 1-5 1-7 0-5-1-13 0-18 0 0 1-1 0-2 0-1 1-2 1-4h0c0-1 0-2 1-3v4 28c0 5 0 11-1 16h-1-1v-14z" class="M"></path><path d="M503 893l2 7 1-1c0 4 0 10 2 13l2 2c1 4 0 11 0 15v7c0 2-1 2-1 4l-7-45 1-2z" class="p"></path><path d="M510 914c1 4 0 11 0 15-2-2-1-5-1-8-1-1-1-3-1-4 0-2 0-2 2-3z" class="E"></path><path d="M508 795l4 2c-1 9-1 18-1 27 0 3 1 7 0 10v3c-1 0-2-1-3-1v-41z" class="B"></path><path d="M521 784c0 2 0 5-1 7v4 9h1v4l-2 24c0 4 0 8-1 12s-1 11-1 16c-1 4-2 8-2 12v-3c0-7 1-13 1-20l3-50v-1-7c1-2 1-2 1-4-1-1 0-2 1-3z" class="J"></path><path d="M519 832l1 4v-2c1 2 0 5 0 7s0 3 1 5v-1c1 0 1 0 3 1h1v1h4c0 1-1 2-1 4l1 1c-1 1-2 1-3 1-1 1-1 0 0 1l-1 2h0l-2 4v2l-1 2h0v2c-1 2-1 5 0 7l-4 13c0 2-1 4-1 6h-1v1c-1-1-1-4-1-5v-16c0-4 1-8 2-12 0-5 0-12 1-16s1-8 1-12z" class="l"></path><path d="M521 845c1 0 1 0 3 1h1v1c-1 1-2 3-2 5v1l-1 1 1 2c-1 0-2 0-3 1 1-1 1-1 1-2-1-3-1-7 0-9v-1z" class="k"></path><path d="M525 847h4c0 1-1 2-1 4l1 1c-1 1-2 1-3 1-1 1-1 0 0 1l-1 2h0-2l-1-2 1-1v-1c0-2 1-4 2-5z" class="b"></path><path d="M523 856h2l-2 4v2l-1 2h0v2c-1 2-1 5 0 7l-4 13c-1-10 2-19 2-29 1-1 2-1 3-1z" class="r"></path><path d="M498 721h1c0 2-1 5 0 6l1 27c0 4-1 9 0 13 1 0 1 0 2 1h2l-2 2 2 2c1 1 2 1 3 2h1v3h3l-2 1h-1c-1 3 0 9 0 13 0 1-1 3 0 4v41 3 5c-1 1-1 0-2 1l-2 2c1-2 1-2 2-2l-3-1 1 20c0 4-1 10 0 14 0 1 0 1-1 1v-3-10l-1-4v-9c-1-1-2 0-4 0l-1-3h0l1-1c1 0 3 0 4-1-1-2-1-2-1-4v-4-5h-2-1l3-1c0-2 0-2-1-3h-2v-1h2c1-2 0-4 0-6v-5-9c-1 0-2-1-2-1-3 0-5-1-8-1-2 0-3 0-5-1h0 0c3 0 12 2 14 1v-1h2v-2c0-4-1-8-1-13 0-3 0-8-1-11v-2l-1-10c1-1 1-1 0-3h0c1-4 0-10 0-14v-31z" class="a"></path><path d="M497 850h5v3c-1-1-2 0-4 0l-1-3h0z" class="L"></path><path d="M503 790l3-2v8l-1 1-1-1c0-3 0-4-1-6z" class="P"></path><path d="M503 844l-1-9h4c1 1 1 7 0 9v1l-3-1z" class="X"></path><path d="M501 772l2 2c0 1 1 2 2 3 2 3 1 7 1 11l-3 2c0-4 1-8-1-11 0-1 0-2-1-4v-3z" class="g"></path><path d="M498 769h1c2 1 2 1 2 3v3c1 2 1 3 1 4 2 3 1 7 1 11 1 2 1 3 1 6l1 1 1-1v2h-1c-1 0-1 0-2 1l-1 1v5h-1c0-4-1-8-1-13 0-3 0-8-1-11v-2l-1-10z" class="C"></path><path d="M502 800c-1-4 0-7 0-10 0 2 0 4 2 6l1 1 1-1v2h-1c-1 0-1 0-2 1l-1 1z" class="L"></path><path d="M501 775c1 2 1 3 1 4 2 3 1 7 1 11 1 2 1 3 1 6-2-2-2-4-2-6v-2c0-2-1-4-1-6v-7z" class="S"></path><path d="M502 800l1-1c1 1 2 1 3 2 0 2 0 5-1 7l1 1c0 1 0 2-1 3l1 1h0c0 2 0 2-1 3v1c1 1 1 2 1 3v4 2 1 6c-1 1-2 1-3 1-1-1-1-4-1-5-1-7 0-15-1-22v-2h1v-5z" class="X"></path><path d="M502 805v8c0 1 1 0 1 2v3 1c1 0 1 1 2 1h-1l-1 1v6l2 1-1 1h-2c-1-7 0-15-1-22v-2h1z" class="G"></path><path d="M409 508h0c2 2 4 3 6 4 0 0 3 1 3 2 11 3 21 3 32 3 3 0 7 0 11-1 3 3 7 6 10 9 1 1 2 2 3 2l1 1-1 2 3 3 2 1h1c1 0 2 1 2 2h2c1 1 0 2 2 1h1l1 1c0 1 0 2-1 3l-1 1c0 2 0 4 1 7l-1 1 1 1-2 1v9 21 12c-2-1-3-1-6 0 0 0 0 1 1 2v1l-1 1h3v2 1 1 1 2 7l-1 1v1c-1 0-3 0-4 1h-6c0-2 0-5-1-7v-1-5c-2 0-4 0-6 1-1 0-2 1-2 2 2 0 2-1 3-1 0 2-1 1 0 3 0 1-1 2-1 2l-1 1-1-1c0-1 0-1-1-2s-3-2-5-4c-2-1-4-5-6-6l-1 1v-4-3l-1-5c-1 1-2 1-4 1-1-1-2 0-3 1s-5 1-7 2c0 1 0 2-1 3l-1-2c-1 0-2 1-4 1v-6l3-2 2-1c4-2 12-7 13-11l-13 7-5 3-1-7c-1-3 0-8-1-12h-9l-18-1-9-1c0-1-2-1-2-1l-14-2v-3h0c0-1 0 0-1-1l-1-1c-1-2-4 0-6-1v-4c1-3 0-5 1-8h4 7 3l1-1c1 0 2 0 3-1v1h1c2 0 2-1 4-2l2-3h0l2-3 3-5h0l6-7v-2-1h2v-3c-1-1-2-1-3-2l-1-1h1v-1l6 1 1-1v-1z" class="l"></path><path d="M450 581c0-1 1-2 1-3h2c0 2 0 2-1 3h-2z" class="R"></path><path d="M424 530c0-1 0-1 1-2v-1c1 0 1 0 1-1l1 1-1 2h0v2c1 2 2 3 3 5l-1 1c-1-1-2-2-2-3-1-1-1-1-1-2s0-1-1-2z" class="q"></path><path d="M427 575l1 1c2 0-1-1 1 0h2v-1c0 2 0 3 2 4l-5 3-1-7z" class="b"></path><path d="M454 577h-3v-1c2-1 4-1 6-1l13 1v1h-2-14z" class="U"></path><path d="M429 533l1-1-1-1 1-1c1-2 3-1 5-1 1 1 1 2 1 3v1l-1-1-2 2h-2c-1 0-1-1-2-1z" class="R"></path><path d="M431 534v-1l1-3h1c1 0 1 1 2 2l-2 2h-2z" class="n"></path><path d="M454 577h14l1 1c1 2 1 4 1 6l-3-3v-2c-1 0-2 1-3 2l-1-2h-3-2c-1-1-3-1-4-1v-1z" class="q"></path><path d="M463 579c2-1 4-1 6-1 1 2 1 4 1 6l-3-3v-2c-1 0-2 1-3 2l-1-2z" class="b"></path><path d="M468 589h1l1-1v5 9c-2 0-4 0-6 1l-1-1h1l4-2c0-1 0-1-1-2h2l-1-1v-3h-1l-1-1c-1 0-1 1-2 0h0c1-1 1-1 2-1h1l1-2h-1l1-1z" class="S"></path><path d="M468 589h1l1-1v5l-1 1c0-2-1-2-1-4h-1l1-1z" class="T"></path><path d="M464 581c1-1 2-2 3-2v2l3 3v4l-1 1h-1v-2h-1-2c-1 0-2-1-3-1l1-1c1-2 1-3 1-4z" class="K"></path><path d="M464 581c1-1 2-2 3-2v2h0c-1 2 0 4-1 5-1 0-2-1-3-1 1-2 1-3 1-4z" class="J"></path><path d="M426 531l2-3 1 1h-1v3l1 1c1 0 1 1 2 1h2l2-2 1 1h0l-3 3h-1l1 1c3 0 3-1 6-3-1 2-1 3-2 3l-2 1c1 1 2 1 3 1h-5-2c-1-1-2-1-3-2l1-1c-1-2-2-3-3-5z" class="b"></path><path d="M462 559h12c0 1 1 2 1 3l-49 1h-9 30l18-1h3v-2h-3l-1 1-2-2z" class="K"></path><path d="M449 559h6 5 2l2 2 1-1h3v2h-3l-18 1 1-3 1-1z" class="P"></path><path d="M435 529h1l1 1 2-1h1c0 3 0 5-1 7h1 1v-1h1l6-3 1 1c-1 1-2 1-1 2v1c-1 1-2 1-3 2h0c-2 0-3 0-4-1-2 0-2 1-3 1v1c-1 0-2 0-3-1l2-1c1 0 1-1 2-3-3 2-3 3-6 3l-1-1h1l3-3h0v-1c0-1 0-2-1-3z" class="k"></path><path d="M412 515l1 1v2 1c0 1 0 1 1 1l1 1c1 0 2 1 3 2l1 1v1 1h0c2 1 4 1 5 2h-3-1v1h1 1c-1 0-1 0-2 1h1c1 0 1 0 2 1l1-1v1-1c1 1 1 1 1 2s0 1 1 2h-2c-1 0-1 0-1-1-2-1-3-1-4-2-1-2-3-1-4-2l1-2h0c-1 0-2 1-3 1 0-2-2-4-3-5h0v-1l2-1c0-1-1-1-1-3 1-1 1-2 1-3z" class="U"></path><path d="M410 523h3l1 1s1 1 1 2h1 1v1h-1 0c-1 0-2 1-3 1 0-2-2-4-3-5z" class="J"></path><path d="M479 534h1c1 0 2 1 2 2h2c1 1 0 2 2 1h1l1 1c0 1 0 2-1 3l-1 1c0 2 0 4 1 7l-1 1 1 1-2 1c-1-1-2-1-3-1h0l-1 1-2-1v-16-1z" class="B"></path><path d="M479 534h1c1 0 2 1 2 2h2c1 1 0 2 2 1h1l1 1c0 1 0 2-1 3l-1 1c0 2 0 4 1 7l-1 1 1 1-2 1c-1-1-2-1-3-1h-1l3-2c0-4 1-9-2-12l-2-2h-1v-1z" class="b"></path><defs><linearGradient id="E" x1="436.887" y1="525.038" x2="436.602" y2="511.462" xlink:href="#B"><stop offset="0" stop-color="#39352d"></stop><stop offset="1" stop-color="#615d49"></stop></linearGradient></defs><path fill="url(#E)" d="M409 508h0c2 2 4 3 6 4 0 0 3 1 3 2 1 1 4 3 5 3 1 1 3 2 5 2 6 2 12 3 18 3 3 1 6 1 9 2h9c1 0 2 1 2 1-19 1-37 1-53-9l-1-1c0-1-1-1-2-2v-2c-1-1-1-1-2-1l1-1v-1z"></path><path d="M402 510v-1l6 1c1 0 1 0 2 1v2c1 1 2 1 2 2s0 2-1 3c0 2 1 2 1 3l-2 1v1h0c1 1 3 3 3 5 1 0 2-1 3-1h0l-1 2c1 1 3 0 4 2 1 1 2 1 4 2 0 1 0 1 1 1h2c0 1 1 2 2 3s2 1 3 2l2 2h-3c-1-1-2-1-4-1l-2-1-1-1c-1 0-1 0-1 1l-2-2-2-2c0-2 0-2-1-3h-3-1v-1h-1l-1 1v-4h-1v-1-1h0c-2 0-1 1-3 1l-1-1-1-2 3-2v-1h-1c-1 0-1 0-2-1l1-1-1-1-2-1v-1h2v-3c-1-1-2-1-3-2l-1-1h1z" class="K"></path><path d="M402 510v-1l6 1c1 0 1 0 2 1v2c1 1 2 1 2 2s0 2-1 3c0 2 1 2 1 3l-2 1v-1c-2-1-2-2-3-4l2-1h-4v-3c-1-1-2-1-3-2l-1-1h1z" class="a"></path><path d="M402 510c3 1 6 2 9 5-1 1-1 1-2 1h-4v-3c-1-1-2-1-3-2l-1-1h1z" class="S"></path><path d="M446 572l6-6v1l-3 3 1 1h0c-1 2-2 4-1 6h0c0 3 1 7 0 9h-1c-1 1-2 1-4 1-1-1-2 0-3 1s-5 1-7 2c0 1 0 2-1 3l-1-2c-1 0-2 1-4 1v-6l3-2 2-1c4-2 12-7 13-11z" class="n"></path><path d="M428 586l3-2c1 2 2 4 2 7h-1c-1 0-2 1-4 1v-6z" class="J"></path><path d="M434 587v-1c1-2 4-3 6-4l1-1h1c2-2 4-4 7-4 0 3 1 7 0 9h-1c-1 1-2 1-4 1-1-1-2 0-3 1s-5 1-7 2v-2-1z" class="R"></path><path d="M434 587c3-2 7-4 10-6 2 0 3 1 4 2h-3c-3 2-8 3-11 5v-1z" class="U"></path><path d="M434 588c3-2 8-3 11-5h3c0 1 1 2 1 3h-1c-1 1-2 1-4 1-1-1-2 0-3 1s-5 1-7 2v-2z" class="J"></path><path d="M438 538c1 0 1-1 3-1 1 1 2 1 4 1h0 4l-1 2c1 0 2 0 3-1 1 1 1 2 2 3v1 1h1 2l-1-2h1c1 1 1 2 2 3h0c2 0 3 0 4 1l-1 2h0l-1-1-1-1c0 1 1 2 1 3h1l2 2c1 2 3 3 5 4l1 1v2h2v-2 2h1c1 1 1 0 2 1h-12-2 0l1-1c0-1-1-1-1-1 0-1 0-2 1-2v-1c-1 0-1 1-3 1v-1l-1-1v-2h-1l-1 1 1-1-1-1v-2h-3l-1-1-3-3v1l-1-1-2 1v-2h-1c-1-1-2-1-4-1-1 1-1 1-3 0v-1c-2 1-2 1-4 0l-2-2h2 5v-1z" class="b"></path><path d="M438 539v-1l3 1 1 1 2-1 1 1h2v2h2c0 1 0 1 1 2h0 2l1 1h3v2l-1 1h-3l-1-1-3-3v1l-1-1-2 1v-2h-1c-1-1-2-1-4-1-1 1-1 1-3 0v-1c-2 1-2 1-4 0l-2-2h2 5z" class="a"></path><path d="M431 539h2c1 1 2 2 4 2-2 1-2 1-4 0l-2-2z" class="V"></path><path d="M479 551l2 1 1-1h0c1 0 2 0 3 1v9 21 12c-2-1-3-1-6 0v-43z" class="H"></path><defs><linearGradient id="F" x1="459.623" y1="510.541" x2="429.877" y2="529.459" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#38372c"></stop></linearGradient></defs><path fill="url(#F)" d="M418 514c11 3 21 3 32 3 3 0 7 0 11-1 3 3 7 6 10 9h-5s-1-1-2-1h-9c-3-1-6-1-9-2-6 0-12-1-18-3-2 0-4-1-5-2-1 0-4-2-5-3z"></path><path d="M437 542c2 1 2 1 3 0 2 0 3 0 4 1h1v2l2-1 1 1v-1l3 3 1 1h3v2l1 1-1 1 1-1h1v2l1 1v1c2 0 2-1 3-1v1c-1 0-1 1-1 2 0 0 1 0 1 1l-1 1h0-5-6l-1 1h-2c-4-1-10-1-14-1l1-2v-1c-1 0-1-1-1-1l-1-1c1-1 2-1 3-1s1 1 1 1l1 1c2 0 3-2 4-3l-2-2-1-2h-1l2-2h1-1l-1-2v-2z" class="S"></path><path d="M440 552c1-1 2-2 2-3h1c0 1 0 1-1 1 0 1-1 2-2 2-1 2-2 3-4 4l-1 2c-1-1-2-1-2-1v-1c-1 0-1-1-1-1l-1-1c1-1 2-1 3-1s1 1 1 1l1 1c2 0 3-2 4-3z" class="X"></path><path d="M437 542c2 1 2 1 3 0 2 0 3 0 4 1h1v2l2-1 1 1c1 1 1 2 2 2 0 1-1 2-2 3h2v1h0l-1 1h-2v-2c-1 1-1 1-2 1 0-1 0-1-1-2l1-2c-1 0-1 0-2 1h0v-2c-1 0-1 0-2 1-1-1-1 0-1-1h-1-1l-1-2v-2z" class="V"></path><path d="M448 545v-1l3 3 1 1h3v2l1 1-1 1 1-1h1v2l1 1v1c2 0 2-1 3-1v1c-1 0-1 1-1 2 0 0 1 0 1 1l-1 1h0-5-6c-1 0-1 0-2-1h0l2-3v-2l-1 2h-1l1-2h-2l1-1h2l1-1h0v-1h-2c1-1 2-2 2-3-1 0-1-1-2-2z" class="U"></path><path d="M449 555c2 0 4-1 5 0h1 1 0c-1 1 0 1-2 1 0 2 0 2 1 3h-6c-1 0-1 0-2-1h0l2-3z" class="T"></path><path d="M454 578c1 0 3 0 4 1h2 3l1 2c0 1 0 2-1 4l-1 1c1 0 2 1 3 1h2 1v2l-1 1h1l-1 2h-1c-1 0-1 0-2 1h0c1 1 1 0 2 0l1 1h1v3l1 1h-2c1 1 1 1 1 2l-4 2h-1l1 1c-1 0-2 1-2 2 2 0 2-1 3-1 0 2-1 1 0 3 0 1-1 2-1 2l-1 1-1-1c0-1 0-1-1-2s-3-2-5-4c-2-1-4-5-6-6v-1l1-1-1-2v-12h2c1-1 1-1 1-3h1z" class="J"></path><path d="M453 590c-1-1-1-1-1-2l2-2c0 1 1 1 1 2l2 2c0 1 0 1-1 2l-1-1c-1-1-1-1-2-1z" class="T"></path><path d="M454 578c1 0 3 0 4 1-1 2-1 4 0 7-1-2-1-3-1-4l-1 1-4-2c1-1 1-1 1-3h1z" class="U"></path><path d="M458 579h2 3l1 2c0 1 0 2-1 4l-1 1c-1 1-1 1-3 1h0l-1-1c-1-3-1-5 0-7z" class="m"></path><path d="M450 593l1-2c2 0 5 6 7 8 2 1 4 2 5 3l1 1c-1 0-2 1-2 2 2 0 2-1 3-1 0 2-1 1 0 3 0 1-1 2-1 2l-1 1-1-1c0-1 0-1-1-2s-3-2-5-4c-2-1-4-5-6-6v-1l1-1-1-2z" class="C"></path><path d="M462 586c1 0 2 1 3 1h2 1v2l-1 1h1l-1 2h-1c-1 0-1 0-2 1h0c1 1 1 0 2 0l1 1h1v3l1 1h-2c1 1 1 1 1 2l-4 2c-4-3-6-4-8-7-1-1-3-4-3-5 1 0 1 0 2 1l1 1c1-1 1-1 1-2l-2-2c1 0 2-1 4-1h0c2 0 2 0 3-1z" class="j"></path><path d="M462 586c1 0 2 1 3 1h2 1v2l-1 1c-1 0-2 1-3 1-2 0-4-1-6-1h-1l-2-2c1 0 2-1 4-1h0c2 0 2 0 3-1z" class="c"></path><path d="M456 595c1 0 0-1 1 0h1c1 0 1 0 2-1h0c1-1 2-1 3 0-1 0-1 1-1 2s1 1 2 2v-1l3-3h1v3l1 1h-2c1 1 1 1 1 2l-4 2c-4-3-6-4-8-7z" class="e"></path><path d="M403 517l2 1 1 1-1 1c1 1 1 1 2 1h1v1l-3 2 1 2 1 1c2 0 1-1 3-1h0v1 1h1v4l1-1h1v1h1 3c1 1 1 1 1 3l2 2 2 2c0-1 0-1 1-1l1 1 2 1c2 0 3 0 4 1h3c2 1 2 1 4 0v1 2l1 2h1-1l-2 2h1l1 2 2 2c-1 1-2 3-4 3l-1-1s0-1-1-1-2 0-3 1l1 1s0 1 1 1v1l-1 2c4 0 10 0 14 1h2l-1 3h-30l-18-1-9-1c0-1-2-1-2-1l-14-2v-3h0c0-1 0 0-1-1l-1-1c-1-2-4 0-6-1v-4c1-3 0-5 1-8h4 7 3l1-1c1 0 2 0 3-1v1h1c2 0 2-1 4-2l2-3h0l2-3 3-5h0l6-7v-2z" class="d"></path><path d="M413 543h1v1c-1 0-2 1-3 1h0l-1 1h0 3l-1 1-1 1c-1-1-2-1-3-1 2-2 1-2 1-4 1 1 2 1 3 0h1zm-11-4l1-4h1l1 1-1 2h1c1 1 2 2 2 3-1 1-2 0-3 2h-1-2l-2-2v-1l2 1 1-2h0z" class="I"></path><path d="M418 540v-3l-2-2 1-1 1 1 2 2 2 2c0-1 0-1 1-1l-1 1 3 3h0l2-1 1 1-2 2c-2 0-2-1-4 1l-1 1c-1 0-2 0-3-1l1-1v-1h-1-1c0-1 0-1 1-2h0v-1z" class="j"></path><path d="M428 542l1 1c-1 1-2 1-3 2v3h2c0 1 0 1-1 2s-2 3-3 3-1 0-2 1v1h-3c0-2 0-1-1-2s0-2-1-3v-1l2 1h0l2-1h0c-1-1 0-1-1-1h-1c1-1 1-1 2-1v-1l1-1c2-2 2-1 4-1l2-2z" class="S"></path><path d="M427 550h2c0 1-2 2-2 4h-3l-3 3h-1l-1 2h-5-2-3-2-5l3-3v1l1-1v-2l-1-1c2 0 4 0 6-2-3 0-4 0-6 1h0c-1-1-2-1-2-1l1-1s1 1 2 1l1-1h4 2 0c0 1-1 1-1 2 2 1 2 0 4 0v1c-1 0-2 1-3 2 2 1 1 1 2 2h2v-1h-2l1-1h2 1 3v-1c1-1 1-1 2-1s2-2 3-3z" class="p"></path><path d="M409 553h2c0 1 1 1 1 2l-2 2h-1c-1-1-1-2-2-3 1-1 1-1 2-1z" class="G"></path><path d="M423 538l1 1 2 1c2 0 3 0 4 1h3c2 1 2 1 4 0v1 2l1 2h1-1l-2 2h1l1 2 2 2c-1 1-2 3-4 3l-1-1s0-1-1-1-2 0-3 1l1 1s0 1 1 1v1l-1 2h-13l1-2h1l3-3h3c0-2 2-3 2-4h-2c1-1 1-1 1-2h-2v-3c1-1 2-1 3-2l-1-1-1-1-2 1h0l-3-3 1-1z" class="L"></path><path d="M423 538l1 1 2 1c2 0 3 0 4 1h3c2 1 2 1 4 0v1 2l1 2c-1 0-2 0-3-1h0l-1 1h-1c-1-1-1-1-1-2h-1l-1 1h0v-1l-1-1-1-1-1-1-2 1h0l-3-3 1-1z" class="g"></path><path d="M388 557c5 1 9 1 14 2h5 2 3 2 5 13c4 0 10 0 14 1h2l-1 3h-30l-18-1-9-1c0-1-2-1-2-1h0c1-2 1-2 0-3z" class="N"></path><path d="M388 557c5 1 9 1 14 2h5 2 3 2c-1 1-1 1-2 1h-11l-2 2-9-1c0-1-2-1-2-1h0c1-2 1-2 0-3z" class="i"></path><path d="M403 517l2 1 1 1-1 1c1 1 1 1 2 1h1v1l-3 2 1 2 1 1c2 0 1-1 3-1h0v1 1h1v4l1-1h1v1h1 3c1 1 1 1 1 3l-1-1-1 1 2 2v3 1h-1l-1-1-1 1c1 0 1 1 1 2v1 1h0-1v-2l-1-1v-2h-2c1-1 1-2 1-3h-1c-2 1-3 2-3 4h-1v-1c1-1 2-3 2-4l-1-1v-1h-2c1-1 1-1 1-2l-2-1 1-1-1-1h0l-2 2v1l-2-1c-1 1-1 2-3 2v-1c-1 1-1 0-2 1h0l-1 1h-4l2-3 3-5h0l6-7v-2z" class="P"></path><path d="M397 526l6-7c0 1 0 1-1 3h1v2c0 1 0 1-1 1l-1 1v1-1c-1 0-2 1-2 1l-2-1h0z" class="N"></path><path d="M412 531h1v1h1 3c1 1 1 1 1 3l-1-1-1 1 2 2v3c-1-3-2-4-4-6l-1 1-2 1h0c0-1 0-2 1-3s0-1 0-2z" class="g"></path><path d="M397 526l2 1s1-1 2-1v1h1 0l-3 3s0 1-1 1c0 0-1 1-1 2l-1 1h-4l2-3 3-5z" class="I"></path><path d="M392 534h0 4l-2 2-4 5h0c3 0 4-2 6-3h1v1h0c2-1 3-2 4-3h1l-1 2 1 1v-1 1h0l-1 2-2-1v1l2 2h2 1c-1 1-1 1-1 2l1 1 1-1-2 2h-2v3 1 1h-1c-1-1-1-1-2-1v1l1 1c-1 1-2 1-3 2 0 1-1 1-1 2-1 0-2-1-3-1h-1v-1c0-1 0-1-1-1v1 1c-1 0-1 0-2 1 1 1 1 1 0 3h0l-14-2v-3h0c0-1 0 0-1-1l-1-1c-1-2-4 0-6-1v-4c1-3 0-5 1-8h4 7 3l1-1c1 0 2 0 3-1v1h1c2 0 2-1 4-2l2-3z" class="C"></path><path d="M392 534h0 4l-2 2c-1 0-1 0-2 1h-1c1-1 1-1 1-2v-1z" class="G"></path><path d="M383 543l4-2h0l-3 2v2c-1 1-2 1-3 1h-2l-1-1 5-2z" class="I"></path><path d="M366 552v-4c1 1 1 2 3 2h-1v1c2 0 3-1 5-2h5l1 1c-1 0-2 0-2 1-2 0-3 1-3 2l-1 1-1-1c-1-2-4 0-6-1z" class="F"></path><path d="M374 555l8 1h2c1 1 3 1 4 1 1 1 1 1 0 3h0l-14-2v-3z" class="D"></path><path d="M371 540h7 3c1 1 2 2 2 3l-5 2c-1 0-1 0-2-1-2 3-4 5-7 6-2 0-2-1-3-2 1-3 0-5 1-8h4z" class="h"></path><path d="M512 429h0l12 2 5 2 4 1 3 2c1 1 2 2 3 2l9 6v1c0 2 2 3 3 5l-2-1v1c2 3 4 5 6 7 1 2 1 2 1 4 1 1 2 2 2 4 1 3 2 7 3 11 2 0 2 0 3 2v4h1l1-2-1-1c0-1 1-1 1-2l1 6v-3l1-1 1 2c1 1 2 3 1 4h0v2c0 1-1 3-1 4-1 4-2 9-3 13l-1-1c-3 5-6 9-9 14-3 3-7 7-11 10l-2 1c-2 1-3 2-5 2l-5 2-2 1-4 1c2 1 2 0 3 2 0 2-2 3-3 5v2l-1 1-1 1v2l-7 1c-2 0-4 0-6-1v-2c-1 0-1 0-2-1h-2c-1 1 0 2-1 4-4 0-16 0-19-3-1-1-1-3-1-4 1-1 1-2 1-3l-1-1h-1c-2 1-1 0-2-1h-2c0-1-1-2-2-2h-1l-2-1-3-3 1-2-1-1c-1 0-2-1-3-2-3-3-7-6-10-9-4 1-8 1-11 1-11 0-21 0-32-3 0-1-3-2-3-2-2-1-4-2-6-4h0l-2-3v-1c1 1 2 1 3 1h1c-1 0-1 0-1-1h1l1-1-1-2c-2-1-2-3-3-5-1-1 0-2 0-3l-2-2v-1l-1-2c0-1 1-1 1-2v-1c2-1 3-4 4-6 1-1 3-2 4-4 1-1 2-3 4-3l1-1v-1l5-2h0c1 0 2 0 4-1 3 0 6 0 9-1h6 5 1c2 0 4 0 6-1 1-3 3-5 4-8h1c1-3 2-5 4-7l2-1c3-3 6-6 10-9l7-5h1l2-1h1c3-1 6-2 10-3h0l4-1 11-1z" class="f"></path><path d="M518 457c2-1 2-1 3-2v1h1c1 1 0 1 1 2l-2 1c-1 0-2-1-3-2z" class="H"></path><path d="M537 470l3-1v-1l2-2-1-2v-1c1 1 1 1 2 1 1 1 1 2 2 2-1 2-1 3-2 5h-2v-1h0l-1-1c-1 1-1 1-3 1z" class="i"></path><path d="M526 450c2 1 5 3 7 4-1 1-2 2-4 2-1 0-2 0-2 1l-1-1s-1-1-2-1l1-1c1 0 1-1 2-1-1-1-1-1-1-2v-1z" class="h"></path><path d="M532 469c0-2 0-2-1-3h1l2 3s1 1 2 1h1c2 0 2 0 3-1l1 1h0v1h2 0 1l1 1h-1v1h-1l-3 3h-3l-2-3-3-4z" class="o"></path><path d="M502 460c7-2 11-1 18 1 4 1 7 3 10 6l2 2 3 4 1 5v2c1 2 1 5 1 7h-1v-2c0-3-1-5-3-7-1-2-2-4-4-6-1-2-3-3-4-4l-6-3c-6-3-12-2-18 0h-2c1-1 1-2 3-2h2c-1-1-1-2-2-3z" class="V"></path><path d="M520 461c4 1 7 3 10 6h-1c-1 0-1-1-2-1l-2-2h-1-1-1c-1-1-1-1-2-1h-1v-1l1-1z" class="U"></path><path d="M508 447c3 0 6 0 10 1l5 1c1 0 2 1 3 1v1c0 1 0 1 1 2-1 0-1 1-2 1l-1 1h-2-1c-1 1-1 1-3 2h-2c-2 0-4 0-6-1-1 1-1 1-3 1s-2 0-4 1h-2l-2 2c-1 0-1-1-2-2v1l-1 1v1c-1 0-3-2-4-2 0 2 1 2 1 4h0c-2 1-3 2-4 3l-1-1v-1c1 0 2-1 3-2l-2-2c2-2 5-4 7-6 1 0 2-1 3-2h0l-6 4c0-2 2-3 3-5 0 1 0 0 1 1 1 0 2-1 3-1 1-1 1 1 2 1 0 0 1-2 2-2h2c1-1 1-2 2-3z" class="h"></path><path d="M516 457c-1-1-2-2-2-4 1-1 2-2 4-3l-1 2h1c1 0 1 0 2 1h1 1l-1 1 1 1h-1c-1 1-1 1-3 2h-2z" class="O"></path><path d="M523 449c1 0 2 1 3 1v1c0 1 0 1 1 2-1 0-1 1-2 1l-1 1h-2l-1-1 1-1h-1-1c-1-1-1-1-2-1h-1l1-2h2c1-1 1-1 3-1z" class="Y"></path><path d="M475 467c1-2 3-4 5-6 7-8 17-13 28-14-1 1-1 2-2 3h-2c-1 0-2 2-2 2-1 0-1-2-2-1-1 0-2 1-3 1-1-1-1 0-1-1-1 2-3 3-3 5l6-4h0c-1 1-2 2-3 2-2 2-5 4-7 6l2 2c-1 1-2 2-3 2v1l1 1-1 1 1 2-5 7c0 1-1 1-1 2h0l-1-1c0 1-1 1-1 3l-1-1c0-1 0-2-1-4h0 1v-1c0-1-2-1-3-1l1-1-1-1c0-2-1-2-2-4z" class="B"></path><path d="M493 456l6-4h0c-1 1-2 2-3 2-2 2-5 4-7 6l-2 2c-1 0-1 0-2 1h2c-1 2-2 3-4 4l1 2-3 3c-1 2 1 2-1 3v-1c0-1-2-1-3-1l1-1v-1-1c1-4 7-9 11-11 2-1 3-2 4-3z" class="G"></path><path d="M489 460l2 2c-1 1-2 2-3 2v1l1 1-1 1 1 2-5 7c0 1-1 1-1 2h0l-1-1c0 1-1 1-1 3l-1-1c0-1 0-2-1-4h0 1c2-1 0-1 1-3l3-3-1-2c2-1 3-2 4-4h-2c1-1 1-1 2-1l2-2z" class="Y"></path><path d="M545 466l2 4c1 4 2 7 3 11v5 3c0 3 0 6-1 9l-1 3h-2v1h-2v-1c-1-1-1-1-2-1 0-2-1-3-3-4v-2l-1-1-1-1v-4-1c0-2 0-5-1-7v-2l-1-5 2 3h3l3-3h1v-1h1l-1-1h-1 0c1-2 1-3 2-5z" class="C"></path><path d="M535 473l2 3h3l3-3v3c1 0 2-1 3-1 0 1-3 2-4 3h1c1 1 1 1 2 1v1h-3c-1-1-1-1-2-1-1 1-1 2-1 3l1 1-1 2 1 1h-2l-1 1c0-2 0-5-1-7v-2l-1-5z" class="d"></path><path d="M545 466l2 4c1 4 2 7 3 11h-5v-1-1c-1 0-1 0-2-1h-1c1-1 4-2 4-3-1 0-2 1-3 1v-3h1v-1h1l-1-1h-1 0c1-2 1-3 2-5z" class="F"></path><path d="M545 487v-1h-1 0l1-1c1-1 1-1 3-1v1c0 1 1 1 2 1v3c0 3 0 6-1 9l-1 3h-2v1h-2v-1c-1-1-1-1-2-1 0-2-1-3-3-4v-2l-1-1-1-1v-4h2l1 2 2-2 1 1 2-2z" class="M"></path><path d="M545 487h3v1c0 1 0 2-1 2l-1 1c-1-1-1-2-1-4z" class="W"></path><path d="M546 501l-1-1v-2-1l-1-1h2c1 0 2 1 3 2l-1 3h-2z" class="F"></path><path d="M540 490l2-2 1 1 1 2h-1c0 1 0 1 1 2l-3 1c0 1 0 1 1 2h-1-1v-4l1-1-1-1z" class="E"></path><path d="M489 469c4-4 8-7 13-9 1 1 1 2 2 3h-2c-2 0-2 1-3 2h2l1 2-2 1c1 1 2 1 2 2l1 1-2 2c-3 3-5 6-6 9l-1 2v7h-4c-2 1-2 1-3 2v1 2c1 1 1 1 1 2l-1 1h-2c-2-6-3-11-2-17l1-6 5-7z" class="C"></path><path d="M493 477l2 1v-1c1-2 3-3 5-5l1 1c-3 3-5 6-6 9-1 0-2-1-3-1h-1l2 2-1 1-1-1c-1 0-2 0-3-1 1-1 2-1 3-1 1-1 1-1 2-1v-3z" class="i"></path><path d="M499 465h2l1 2-2 1c1 1 2 1 2 2l1 1-2 2-1-1c-2 2-4 3-5 5v1l-2-1v3c-1 0-1 0-2 1l-1-2h-1l-1-1c2-4 5-8 9-11 1 0 2-1 2-2z" class="D"></path><path d="M500 468c1 1 2 1 2 2l1 1-2 2-1-1c-2 2-4 3-5 5v1l-2-1h-1c2-2 1-4 4-6 1-1 2-2 4-3z" class="B"></path><path d="M500 468c1 1 2 1 2 2-1 1-2 1-3 1s-2 1-2 1l-1-1c1-1 2-2 4-3z" class="Y"></path><path d="M489 469c4-4 8-7 13-9 1 1 1 2 2 3h-2c-2 0-2 1-3 2 0 1-1 2-2 2-4 3-7 7-9 11-1 3-2 6-2 10l2-1 1 1h0l-3 2c0 2 0 3 1 4v2c1 1 1 1 1 2l-1 1h-2c-2-6-3-11-2-17l1-6 5-7z" class="a"></path><path d="M474 470l1-3c1 2 2 2 2 4l1 1-1 1c1 0 3 0 3 1v1h-1 0c1 2 1 3 1 4l1 1c0-2 1-2 1-3l1 1h0c0-1 1-1 1-2l-1 6c-1 6 0 11 2 17v2l1 1c-1 1-2 2-2 3 1 0 2-2 3-2l1 1-4 5v1h1v1h2c0 1-1 1-1 1h-1c-1 1-1 2-1 3 1 1 3 2 4 3h0l-2 1a30.44 30.44 0 0 1-8-8c-7-8-8-15-8-26v-3l1-5 3-7z" class="W"></path><path d="M473 488h2v1c-1 1 0 2 0 3-1 0-1 1-2 1h-1v-4h0l1-1z" class="f"></path><path d="M473 493l1 2h1 0c1-1 1-2 3-2 1 0 1 0 2 2 0 1 0 2-1 4l1 1c-1 0-2 1-3 2l-1-1c1-1 1-1 1-2v-1h-1-3l1-2-2-2v-1h1zm12 8h-1c-2-1-2-1-3-3 1-1 1-2 1-2l-1-2v-2c0-1 0-3-1-5h0v-5h2 1c-1 6 0 11 2 17v2z" class="F"></path><path d="M470 485v-3l1 1h1v3l1 1 3-1h1l1 1c-2 1-4 0-5 1l-1 1h0v4 1l2 2-1 2h3 1v1c0 1 0 1-1 2l1 1v1c2 0 3 0 4-1s0 0 1 0v1l-2 3h1c0-1 1-2 1-2 1-1 1-2 2-2h2c-1 1-2 2-2 3 1 0 2-2 3-2l1 1-4 5v1h1v1h2c0 1-1 1-1 1h-1c-1 1-1 2-1 3 1 1 3 2 4 3h0l-2 1a30.44 30.44 0 0 1-8-8c-7-8-8-15-8-26z" class="B"></path><path d="M478 511l1-1 3-3 1 1h-1c0 1-1 2-1 3 1 1 2 3 3 4s3 2 4 3h0l-2 1a30.44 30.44 0 0 1-8-8z" class="C"></path><path d="M501 465c6-2 12-3 18 0l6 3c1 1 3 2 4 4 2 2 3 4 4 6 2 2 3 4 3 7v2h1v1 4l1 1 1 1v2l-1 6-3 3-1-1-3 1c-4 3-7 5-11 7l-8 2c-1 1-2 2-2 3h-1l-1 1c0 1 0 1 1 2l1 1v3c-1 0-1 1-2 2h0l-2 1h-3l-6-2h1c-1-1-1-1-1-2h0-1l-6-3c-1 0-2-1-2-2h0c-1-1-3-2-4-3 0-1 0-2 1-3h1s1 0 1-1h-2v-1h-1v-1l4-5-1-1c-1 0-2 2-3 2 0-1 1-2 2-3l-1-1v-2h2l1-1c0-1 0-1-1-2v-2-1c1-1 1-1 3-2h4v-7l1-2c1-3 3-6 6-9l2-2-1-1c0-1-1-1-2-2l2-1-1-2z" class="a"></path><path d="M512 473c3 1 7 4 8 7-2 0-3-2-4-4l-1 1c-2-1-3-2-4-2 0-1 1-1 1-2z" class="E"></path><path d="M504 474h1c2-1 4-2 7-1 0 1-1 1-1 2-1 0-2 0-3 1-2 0-3 1-4 2v-1-2-1z" class="F"></path><path d="M515 477l1-1c1 2 2 4 4 4 1 1 2 2 3 2-1 2-1 2-1 4-1 0-1 0-2-1-1-3-4-4-6-6-1 0-2-1-2-1v-1l2 1 1-1z" class="X"></path><path d="M495 485c1-2 1-3 2-5 1-3 4-5 7-6v1 2 1c-3 2-4 4-4 8v1l-2 2 1 2h-1l-1-3h-1-1v-3z" class="G"></path><path d="M522 486l2-1v1 1 1 1c0 1 0 2 1 3h0c-1 1-1 2 0 2l1 1-1 1-1 3h1l-1 1h0c-1 0-2 1-3 2v1l-3 1c-1-1-2-1-3-2 0-2 0-2 1-3v-1l4-4c1-2 2-5 2-8z" class="b"></path><path d="M533 478c2 2 3 4 3 7v2h1v1 4l1 1 1 1v2l-1 6-3 3-1-1-3 1 3-6h-1 0c0-1-1-1-1-2v-1h-2v1c-1 1-2 1-3 0 1-1 2-3 3-5-1-1-3-1-4-2l1-1 1 1h2l-2-2-2-1 1-1h0c-1-1-1-1-1-2h0c-1-1-1-2-1-3l2-3 1 1h0l-1 1 1 1h0l1 1v1h2v-1h1c0-2 1-3 1-4z" class="X"></path><path d="M528 486v-1l1-1h2v3l-1 1-2-2z" class="C"></path><path d="M528 486l2 2 1-1h3l2 1v3 2c-1 0-2 0-2-1-1 0 0-1-1-1l-1-1c-1 0-2 1-2 2-1-1-3-1-4-2l1-1 1 1h2l-2-2v-2z" class="N"></path><path d="M530 492c0-1 1-2 2-2l1 1c1 0 0 1 1 1 0 1 1 1 2 1l-3 6h0c0-1-1-1-1-2v-1h-2v1c-1 1-2 1-3 0 1-1 2-3 3-5z" class="C"></path><path d="M537 492l1 1 1 1v2l-1 6-3 3-1-1-3 1 3-6c2-2 3-5 3-7z" class="B"></path><path d="M501 465c6-2 12-3 18 0l6 3c1 1 3 2 4 4 2 2 3 4 4 6 0 1-1 2-1 4h-1v1h-2v-1l-1-1h0l-1-1 1-1h0l-1-1-2 3c-1-2-3-4-4-6-2-1-3-3-6-4l-1 1c-1-1-2-1-4 0 0-1-1-1-2-1v-2h-1v2l-1 1c-1 1-2 1-4 1h1c1-1 1-2 2-3h0l-2 1-1-1c0-1-1-1-2-2l2-1-1-2z" class="Q"></path><path d="M515 470v-1c0-1-1-2-2-2-1-1-2 0-3-1l1-1c3 0 5 1 8 2 1 0 0 0 1 1h-1c-1 0-2 1-3 0v3c-1 0-1 0-1-1z" class="B"></path><path d="M515 470c0 1 0 1 1 1v-3c1 1 2 0 3 0h1l3 3s0-1 1-1c0-1 0-1 1-2 1 1 3 2 4 4 2 2 3 4 4 6 0 1-1 2-1 4h-1v1h-2v-1l-1-1h0l-1-1 1-1h0l-1-1-2 3c-1-2-3-4-4-6-2-1-3-3-6-4l-1 1c-1-1-2-1-4 0 1-2 1-2 2-3 1 0 2 1 3 1z" class="G"></path><path d="M525 468c1 1 3 2 4 4v2c0 1 0 1-1 1s-3-2-5-3v-1s0-1 1-1c0-1 0-1 1-2z" class="F"></path><path d="M521 475l1-1-2-1 1-1c1 1 2 2 3 4v1h3v-1c3 1 3 4 4 6v1h-2v-1l-1-1h0l-1-1 1-1h0l-1-1-2 3c-1-2-3-4-4-6z" class="L"></path><path d="M511 475c1 0 2 1 4 2l-1 1-2-1v1s1 1 2 1c2 2 5 3 6 6 1 1 1 1 2 1h0c0 3-1 6-2 8l-4 4c-4 2-8 2-12 1 0-2-1-2 0-4l-1-1v-1c-2-1-2-4-3-6v-1c0-4 1-6 4-8 1-1 2-2 4-2 1-1 2-1 3-1z" class="P"></path><path d="M508 476h0v1c-2 1-4 2-5 4v4c0 2 1 5 2 6h1c-1 2-2 2-3 3v-1c-2-1-2-4-3-6v-1c0-4 1-6 4-8 1-1 2-2 4-2z" class="b"></path><path d="M508 490c-2-1-2-2-3-3l-1-3c1-1 3-4 4-4h3c2 1 3 3 4 5-1 2-2 4-3 5h-1-3z" class="R"></path><path d="M508 490v-4l2-1c1 0 1 0 2 2l-1 1v1 1h-3z" class="m"></path><path d="M514 479c2 2 5 3 6 6 1 1 1 1 2 1h0c0 3-1 6-2 8l-4 4c-4 2-8 2-12 1 0-2-1-2 0-4l-1-1c1-1 2-1 3-3 0 1 1 1 2 1 3 0 5-1 7-3 0-1 1-2 1-3 1-2-1-4-2-6v-1z" class="J"></path><path d="M514 479c2 2 5 3 6 6 0 2 1 4-1 6l-2 2c-1-2 1-3 2-5h-1c-1 1-2 2-3 2h0v-1c0-1 1-2 1-3 1-2-1-4-2-6v-1z" class="a"></path><path d="M520 485c1 1 1 1 2 1h0c0 3-1 6-2 8l-4 4c-4 2-8 2-12 1 0-2-1-2 0-4h0l3 1c3 1 6 0 9-2l1-1 2-2c2-2 1-4 1-6z" class="N"></path><path d="M520 485c1 1 1 1 2 1h0c0 3-1 6-2 8h-1c-1 1-2 2-4 3h-3l2-1c1 0 1-1 2-1v-1l1-1 2-2c2-2 1-4 1-6z" class="L"></path><path d="M494 484l1 1v3h1 1l1 3h1l-1-2 2-2c1 2 1 5 3 6v1l1 1c-1 2 0 2 0 4 4 1 8 1 12-1v1c-1 1-1 1-1 3 1 1 2 1 3 2l3-1v-1c1-1 2-2 3-2h0l1-1h-1l1-3 2 1c1 1 2 1 3 0v-1h2v1c0 1 1 1 1 2h0 1l-3 6c-4 3-7 5-11 7l-8 2c-1 1-2 2-2 3h-1l-1 1c0 1 0 1 1 2l1 1v3c-1 0-1 1-2 2h0l-2 1h-3l-6-2h1c-1-1-1-1-1-2h0-1l-6-3c-1 0-2-1-2-2h0c-1-1-3-2-4-3 0-1 0-2 1-3h1s1 0 1-1h-2v-1h-1v-1l4-5-1-1c-1 0-2 2-3 2 0-1 1-2 2-3l-1-1v-2h2l1-1c0-1 0-1-1-2v-2-1c1-1 1-1 3-2h4v-7z" class="X"></path><path d="M491 501l1-1-1-2c-1-1-1-1-1-2h0v-1h2s0 1 1 1v1c0 1 0 2 1 3v4h-1l-2-3z" class="N"></path><path d="M515 502c1 1 2 1 3 2l-2 1h-8l-3 1c-1-1-1-1-2-1h0l-2-2h1c4 1 9 0 13-1z" class="a"></path><path d="M494 491c0 1 1 2 1 4h0v2 1l-2-2c-1 0-1-1-1-1h-2v1h0c0 1 0 1 1 2l1 2-1 1-3-3c0-1 0-1-1-2v-2-1c1-1 1-1 3-2h4z" class="e"></path><path d="M487 493c1-1 1-1 3-2h4v2h-1c-1 0-2 1-4 0h-2z" class="D"></path><path d="M494 484l1 1v3h1 1l1 3h1l-1-2 2-2c1 2 1 5 3 6v1l1 1c-1 2 0 2 0 4 4 1 8 1 12-1v1c-1 1-1 1-1 3-4 1-9 2-13 1h-1-1c-1-2-4-4-4-6v-2h-1c0-2-1-3-1-4v-7z" class="R"></path><path d="M500 487c1 2 1 5 3 6v1l1 1c-1 2 0 2 0 4-2-1-5-2-6-5-1-1-1-1-1-2-2-1-2-2-2-4h1 1l1 3h1l-1-2 2-2z" class="d"></path><path d="M527 497c1 1 2 1 3 0v-1h2v1c0 1 1 1 1 2h0 1l-3 6c-4 3-7 5-11 7l-8 2h-4l-4-1c-5-1-8-3-11-5-2-1-4-3-5-4l-1-1c-1 0-2 2-3 2 0-1 1-2 2-3l-1-1v-2h2l3 3v2c0 1 2 2 3 3 0 1 3 2 4 3 2 0 1-1 3 0 1 2 7 3 9 3 1-1 2-2 2-3v-2c1-2 3-1 4-1 0-1 0-2 1-2l2-1 3-1v-1c1-1 2-2 3-2h0l1-1h-1l1-3 2 1z" class="F"></path><path d="M529 505c0-1-1-1-1-1 0-2 1-3 3-4 0 1 0 0 1 1h0l-3 4z" class="G"></path><path d="M533 499h1l-3 6c-4 3-7 5-11 7l-8 2h-4l-4-1c-5-1-8-3-11-5-2-1-4-3-5-4l-1-1c-1 0-2 2-3 2 0-1 1-2 2-3l-1-1v-2h2l3 3v2c0 1 2 2 3 3 0 1 3 2 4 3 2 0 1-1 3 0 1 2 7 3 9 3 3 0 7-1 10-2h0c5-1 7-3 10-6l3-4 1-2h0z" class="K"></path><path d="M488 504c1 1 3 3 5 4 3 2 6 4 11 5l4 1h4c-1 1-2 2-2 3h-1l-1 1c0 1 0 1 1 2l1 1v3c-1 0-1 1-2 2h0l-2 1h-3l-6-2h1c-1-1-1-1-1-2h0-1l-6-3c-1 0-2-1-2-2h0c-1-1-3-2-4-3 0-1 0-2 1-3h1s1 0 1-1h-2v-1h-1v-1l4-5z" class="i"></path><path d="M508 514h4c-1 1-2 2-2 3h-1l-1 1-1-1v-1l-1 1c-2 0-3-1-4-1 2-2 3-2 6-2z" class="Q"></path><path d="M487 511c1 1 3 2 4 3l1-2c2 0 3 2 3 2v1c0 1-1 1-1 2l1 1v2c-1 0-2 0-3-1l-2 1c-1 0-2-1-2-2h0c-1-1-3-2-4-3 0-1 0-2 1-3h1s1 0 1-1z" class="D"></path><path d="M487 511c1 1 3 2 4 3-1 1-2 1-3 2-1-1-2-3-2-4 0 0 1 0 1-1z" class="G"></path><path d="M501 523c-1-1-1-2-1-2l-1-1 1-2h1 0v2l1 1c0-1 1-1 1-2 1 1 1 1 2 1h0 3 1l1 1v3c-1 0-1 1-2 2h0l-2 1h-3l-6-2h1c-1-1-1-1-1-2h0l3 1 1-1z" class="e"></path><path d="M497 523l3 1 1-1c1 1 2 1 2 2v1h1 0 4l-2 1h-3l-6-2h1c-1-1-1-1-1-2h0z" class="X"></path><path d="M488 504c1 1 3 3 5 4 3 2 6 4 11 5l-4 2v1l-3 1c0-1-1-2-2-3 0 0-1-2-3-2l-1 2c-1-1-3-2-4-3h-2v-1h-1v-1l4-5z" class="F"></path><path d="M529 433l4 1 3 2c1 1 2 2 3 2l9 6v1c0 2 2 3 3 5l-2-1v1c2 3 4 5 6 7 1 2 1 2 1 4 1 1 2 2 2 4 1 3 2 7 3 11 2 0 2 0 3 2v4h1l1-2-1-1c0-1 1-1 1-2l1 6v-3l1-1 1 2c1 1 2 3 1 4h0v2c0 1-1 3-1 4-1 4-2 9-3 13l-1-1c-3 5-6 9-9 14-3 3-7 7-11 10l-2 1c-2 1-3 2-5 2l-5 2-2 1-4 1c2 1 2 0 3 2 0 2-2 3-3 5v2l-1 1-1 1v2l-7 1c-2 0-4 0-6-1v-2c-1 0-1 0-2-1h-2c-1 1 0 2-1 4-4 0-16 0-19-3-1-1-1-3-1-4 1-1 1-2 1-3l-1-1h-1c-2 1-1 0-2-1h-2c0-1-1-2-2-2h-1l-2-1-3-3 1-2-1-1h1 0c1 1 2 1 3 1l2 2h0 2l2 1h1c1 0 1 1 2 1h1l1 1h2c0-2-2-2-2-3h-2-1l1-1c2 0 4 1 6 1 2 1 4 1 7 2h0v-2c2 0 2 0 3-1v-1-1h3l2-1h0c1-1 1-2 2-2v-3l-1-1c-1-1-1-1-1-2l1-1h1c0-1 1-2 2-3l8-2c4-2 7-4 11-7l3-1 1 1 3-3 1-6c2 1 3 2 3 4 1 0 1 0 2 1v1h2v-1h2l1-3c1-3 1-6 1-9v-3-5c-1-4-2-7-3-11l-2-4c-1 0-1-1-2-2-1-3-4-5-6-7l-4-3c-2-1-5-3-7-4-1 0-2-1-3-1l-5-1c0-2-1-2-2-3 1 0 2 0 3-1h1 1c0 1 1 2 2 3 1-1 1-1 1-3v-2l1-1 1-1 1-1 5 2 3 1 4 3h1c-3-2-5-4-7-5-3-2-4-2-6-3l-2-1c-2 0-3 0-4-1 2-1 3-1 6 0l1 1 3-1-1-1-1-1z" class="P"></path><path d="M529 433l4 1 3 2c1 1 2 2 3 2l9 6v1c0 2 2 3 3 5l-2-1c0 1-1-1-1-1-4-3-8-6-12-8-2-2-5-3-8-4l3-1-1-1-1-1z" class="M"></path><path d="M553 482h1c1 0 2 0 3-1 1 1 1 2 1 4l1-1v-1c1 3 1 7 0 11v2c0 3-2 10-4 13l-1-1c-1 2-2 3-3 6 0 1-1 2-2 3v-1c-1-1-2-1-4-2l3-3c0-1 1-2 1-3 1-1 0 0 1-2h0l-3-2 1-3 1-3c1-3 1-6 1-9 1 0 2 0 3-1v-1l2-1-2-1v-3z" class="V"></path><path d="M553 488l1 1v1h0c-1 1-2 2-2 3l3 1v1h-1c-1 0-2 1-2 1l-2 1v1h4v1c0 1-1 2-2 2 0 1 0 1 1 1-1 2-2 3-3 4l-3-2 1-3 1-3c1-3 1-6 1-9 1 0 2 0 3-1z" class="q"></path><path d="M554 499l1-1v-1h2v2l-1 1c0 2-1 6-2 8s-2 3-3 6c0 1-1 2-2 3v-1c-1-1-2-1-4-2l3-3c0-1 1-2 1-3 1-1 0 0 1-2h0c1-1 2-2 3-4-1 0-1 0-1-1 1 0 2-1 2-2z" class="Z"></path><defs><linearGradient id="G" x1="558.861" y1="519.027" x2="545.974" y2="490.968" xlink:href="#B"><stop offset="0" stop-color="#7e7967"></stop><stop offset="1" stop-color="#b0ac9c"></stop></linearGradient></defs><path fill="url(#G)" d="M561 476c2 0 2 0 3 2v4h1l1-2-1-1c0-1 1-1 1-2l1 6v-3l1-1 1 2c1 1 2 3 1 4h0v2c0 1-1 3-1 4-1 4-2 9-3 13l-1-1c-3 5-6 9-9 14-3 3-7 7-11 10l-2 1c-2 1-3 2-5 2l-5 2c0-1-1-2-1-3-3 1-4 1-7 1l4-2-2-1 5-3c5-2 10-6 13-10 2 1 3 1 4 2v1c1-1 2-2 2-3 1-3 2-4 3-6l1 1c-1 1-1 2-1 3l3-3h0c0-1 1-2 1-2l3-9c1-8 1-14 0-22z"></path><path d="M554 508l1 1c-1 1-1 2-1 3-2 4-5 7-8 9 0-1 2-3 3-4s2-2 2-3c1-3 2-4 3-6z" class="T"></path><path d="M567 483v-3l1-1 1 2c1 1 2 3 1 4h0v2c0 1-1 3-1 4-1 4-2 9-3 13l-1-1c2-7 2-13 2-20z" class="r"></path><path d="M532 527c1-1 2-1 4-1 1 0 3-1 5 0v1h1l1 1c-2 1-3 2-5 2l-5 2c0-1-1-2-1-3-3 1-4 1-7 1l4-2 3-1z" class="C"></path><path d="M532 529c1-1 2-2 4-2l1 1c0 1 1 1 1 2l-5 2c0-1-1-2-1-3z" class="F"></path><path d="M527 527l5-3c5-2 10-6 13-10 2 1 3 1 4 2v1c-1 1-3 3-3 4l-2 2v-1l1-1c-1-1-2 0-3 0s-2 1-3 2c-2 0-3 1-4 2-2 0-2 1-3 2l-3 1-2-1z" class="S"></path><path d="M527 439l5 2 3 1 4 3h1c2 1 3 2 5 4 3 2 4 5 7 8 4 4 4 9 6 14 1 3 1 8 1 12v1l-1 1c0-2 0-3-1-4-1 1-2 1-3 1h-1v3l2 1-2 1v1c-1 1-2 1-3 1v-3-5c-1-4-2-7-3-11l-2-4c-1 0-1-1-2-2-1-3-4-5-6-7l-4-3c-2-1-5-3-7-4-1 0-2-1-3-1l-5-1c0-2-1-2-2-3 1 0 2 0 3-1h1 1c0 1 1 2 2 3 1-1 1-1 1-3v-2l1-1 1-1 1-1z" class="O"></path><path d="M532 441l3 1c-1 2-2 1-2 3v1l-1-1-1-1-1-1v-1l2-1z" class="B"></path><path d="M525 441l1-1v5l1 1c1-1 2-1 3-1l-1 1c1 2 2 2 3 3 1-1 2-2 3-2l-1 2v1l1 1c1 0 1 0 2-1v1h2v1c0 1 0 2 1 2l1 1-2 1v-1l-1 1c-1 0 0 0-1 1l-4-3c-2-1-5-3-7-4-1 0-2-1-3-1l-5-1c0-2-1-2-2-3 1 0 2 0 3-1h1 1c0 1 1 2 2 3 1-1 1-1 1-3v-2l1-1z" class="K"></path><path d="M541 455c2-1 2-1 3-3h1c0 1 0 1-1 2s-1 2-2 4c1 0 2 1 3 1 1-1 2-1 3-2h0v1l2 1h-1c-1 1-2 1-4 1v1c2 1 6 1 7 0 1 1 1 2 1 3l1 1c1 1 1 2 1 3h0v2c-1 1-1 1-2 1h-1c1-1 2-2 2-3l-3 1c-2 0-3 0-4 1l-2-4c-1 0-1-1-2-2-1-3-4-5-6-7 1-1 0-1 1-1l1-1v1l2-1z" class="U"></path><path d="M540 445c2 1 3 2 5 4 3 2 4 5 7 8 4 4 4 9 6 14 1 3 1 8 1 12v1l-1 1c0-2 0-3-1-4-1 1-2 1-3 1h-1v3l2 1-2 1v1c-1 1-2 1-3 1v-3-5c-1-4-2-7-3-11 1-1 2-1 4-1l3-1c0 1-1 2-2 3h1c1 0 1 0 2-1v-2h0c0-1 0-2-1-3l-1-1c0-1 0-2-1-3-1-2-2-4-3-5-3-5-6-8-10-11h1z" class="a"></path><path d="M547 470c1-1 2-1 4-1l1 1h-2l1 2-1 1h1c1 2 1 3 1 5l1 1 1-1c0 1 0 1-1 1 0 1 0 1-1 2h1v1 3l2 1-2 1v1c-1 1-2 1-3 1v-3-5c-1-4-2-7-3-11z" class="r"></path><path d="M539 496c2 1 3 2 3 4 1 0 1 0 2 1v1h2v-1h2l-1 3 3 2h0c-1 2 0 1-1 2 0 1-1 2-1 3l-3 3c-3 4-8 8-13 10l-5 3 2 1-4 2h-2c-1 0-2 0-3-1h0l-1-1-2-1c-1 1-2 1-3 1l-1-1h2 2c2-1 3-1 4-2l-2-3c0 2 1 2 0 3h-1c-2 0-3 1-4 1l-5 1-1-1h0c1-1 1-2 2-2v-3l-1-1c-1-1-1-1-1-2l1-1h1c0-1 1-2 2-3l8-2c4-2 7-4 11-7l3-1 1 1 3-3 1-6z" class="E"></path><path d="M531 516c0-1-1-2-2-3 1-1 1-1 2-1h0l1 3v1h-1z" class="C"></path><path d="M516 519v-1h3c0 1 0 1-1 2h0c1 1 1 1 1 2h-1c-1-1-2-2-2-3z" class="F"></path><path d="M534 509c2 1 2 1 4 1l-1 1-1 1-1 1c-1 0-1 0-2-1 0-2 0-2 1-3z" class="C"></path><path d="M508 526l2-1h3c1-1 2-1 2-2s0-2-1-2v-1l2-1c0 1 1 2 2 3h1 0c0 2 1 2 0 3h-1c-2 0-3 1-4 1l-5 1-1-1z" class="N"></path><path d="M512 514l8-2v4c-1 0-3 1-5 1-2 1-4 0-5 0 0-1 1-2 2-3z" class="H"></path><path d="M538 503l1-1v-1h1l2 2v1c-1 1-1 1-1 2l-1 1c0 1-1 2-2 3-2 0-2 0-4-1l3-4c0-1 1-1 1-2z" class="D"></path><path d="M531 505l3-1 1 1c-4 6-9 8-15 11v-4c4-2 7-4 11-7z" class="h"></path><path d="M532 515c2 1 2 2 2 3l1 2-1 1c-5 3-9 5-15 7l-2-1c-1 1-2 1-3 1l-1-1h2 2c2-1 3-1 4-2l1-1h1l1-2c0-1 1-1 1-2h0c1-1 2 0 4 0v1h1c0-1 1-1 1-1l-1-1v-3h1 1v-1z" class="d"></path><path d="M532 515c2 1 2 2 2 3l1 2-1 1c-2-2-2-3-2-5v-1z" class="p"></path><path d="M539 496c2 1 3 2 3 4 1 0 1 0 2 1v1h2v-1h2l-1 3 3 2h0c-1 2 0 1-1 2 0 1-1 2-1 3l-3 3c-3 4-8 8-13 10l-5 3 2 1-4 2h-2c-1 0-2 0-3-1h0l-1-1c6-2 10-4 15-7l1-1-1-2 2-2-1-2v-1l1-1 1-1 1-1c1-1 2-2 2-3l1-1c0-1 0-1 1-2v-1l-2-2h-1v1l-1 1v-1l1-6z" class="r"></path><path d="M520 529l7-2 2 1-4 2h-2c-1 0-2 0-3-1h0z" class="d"></path><path d="M540 507l1 1-1 2h1c1 0 1 0 2-1l2-2c-2 5-6 9-10 13l-1-2 2-2-1-2v-1l1-1 1-1 1-1c1-1 2-2 2-3z" class="G"></path><path d="M536 512l2 1-1 1s-1 1-2 0v-1l1-1z" class="N"></path><path d="M539 496c2 1 3 2 3 4 1 0 1 0 2 1v1h2v-1h2l-1 3-2 3-2 2c-1 1-1 1-2 1h-1l1-2-1-1 1-1c0-1 0-1 1-2v-1l-2-2h-1v1l-1 1v-1l1-6z" class="F"></path><path d="M546 501h2l-1 3-2 3-2 2 1-2c0-3 1-3 2-5v-1z" class="E"></path><path d="M519 522l2 3c-1 1-2 1-4 2h-2-2l1 1c1 0 2 0 3-1l2 1 1 1h0c1 1 2 1 3 1h2c3 0 4 0 7-1 0 1 1 2 1 3l-2 1-4 1c2 1 2 0 3 2 0 2-2 3-3 5v2l-1 1-1 1v2l-7 1c-2 0-4 0-6-1v-2c-1 0-1 0-2-1h-2c-1 1 0 2-1 4-4 0-16 0-19-3-1-1-1-3-1-4 1-1 1-2 1-3l-1-1h-1c-2 1-1 0-2-1h-2c0-1-1-2-2-2h-1l-2-1-3-3 1-2-1-1h1 0c1 1 2 1 3 1l2 2h0 2l2 1h1c1 0 1 1 2 1h1l1 1h2c0-2-2-2-2-3h-2-1l1-1c2 0 4 1 6 1 2 1 4 1 7 2h0v-2c2 0 2 0 3-1v-1-1h3l2-1h0 0l1 1 5-1c1 0 2-1 4-1h1c1-1 0-1 0-3z" class="m"></path><path d="M496 542c1-1 1-1 2 0l1 4h-2c-1-2-1-3-1-4z" class="K"></path><path d="M504 540c1 0 1 1 2 1v2c0 2-1 2-2 3-1 0-1 0-2-1h0c0-3 1-4 2-5zm-16-2h1c1 1 2 3 2 5h2c1 0 1 1 2 1v-1l1-1c0 1 0 2 1 4h2c2 0 4 0 6 1l1-1h0c1-2-1-3 2-5 2 0 3 0 5 2v2h-1c-1 0-1 0-2-1h-2c-1 1 0 2-1 4-4 0-16 0-19-3-1-1-1-3-1-4 1-1 1-2 1-3z" class="R"></path><path d="M519 522l2 3c-1 1-2 1-4 2h-2-2l1 1c1 0 2 0 3-1l2 1 1 1h0c1 1 2 1 3 1h2c3 0 4 0 7-1 0 1 1 2 1 3l-2 1-4 1c-14 4-28 2-41-1h-3-1c2 2 4 3 5 4h-1c-2 1-1 0-2-1h-2c0-1-1-2-2-2h-1l-2-1-3-3 1-2-1-1h1 0c1 1 2 1 3 1l2 2h0 2l2 1h1c1 0 1 1 2 1h1l1 1h2c0-2-2-2-2-3h-2-1l1-1c2 0 4 1 6 1 2 1 4 1 7 2h0v-2c2 0 2 0 3-1v-1-1h3l2-1h0 0l1 1 5-1c1 0 2-1 4-1h1c1-1 0-1 0-3z" class="H"></path><path d="M532 529c0 1 1 2 1 3l-2 1-2-1c-1-1-6-1-8 0s-3 1-5 0l1-1c2 0 4 0 6-1h2c3 0 4 0 7-1z" class="Q"></path><path d="M503 529l9 1c2 1 3-1 5 1l-1 1c-4-1-9 0-13-1 0 0-1 1-3 1v-2c2 0 2 0 3-1z" class="G"></path><path d="M475 528l11 5h-3-1c2 2 4 3 5 4h-1c-2 1-1 0-2-1h-2c0-1-1-2-2-2h-1l-2-1-3-3 1-2z" class="J"></path><path d="M519 522l2 3c-1 1-2 1-4 2h-2-2l1 1c1 0 2 0 3-1l2 1 1 1h0c1 1 2 1 3 1-2 1-4 1-6 1-2-2-3 0-5-1l-9-1v-1-1h3l2-1h0 0l1 1 5-1c1 0 2-1 4-1h1c1-1 0-1 0-3z" class="T"></path><path d="M512 530l8-1c1 1 2 1 3 1-2 1-4 1-6 1-2-2-3 0-5-1z" class="I"></path><path d="M512 429h0l12 2 5 2 1 1 1 1-3 1-1-1c-3-1-4-1-6 0 1 1 2 1 4 1l2 1c2 1 3 1 6 3 2 1 4 3 7 5h-1l-4-3-3-1-5-2-1 1-1 1-1 1v2c0 2 0 2-1 3-1-1-2-2-2-3h-1-1c-1 1-2 1-3 1 1 1 2 1 2 3-4-1-7-1-10-1-11 1-21 6-28 14-2 2-4 4-5 6l-1 3-3 7-1 5v3c0 11 1 18 8 26a30.44 30.44 0 0 0 8 8l2-1c0 1 1 2 2 2l6 3h1 0c0 1 0 1 1 2h-1l6 2v1 1c-1 1-1 1-3 1v2h0c-3-1-5-1-7-2-2 0-4-1-6-1l-1 1h1 2c0 1 2 1 2 3h-2l-1-1h-1c-1 0-1-1-2-1h-1l-2-1h-2 0l-2-2c-1 0-2 0-3-1h0-1c-1 0-2-1-3-2-3-3-7-6-10-9-4 1-8 1-11 1-11 0-21 0-32-3 0-1-3-2-3-2-2-1-4-2-6-4h0l-2-3v-1c1 1 2 1 3 1h1c-1 0-1 0-1-1h1l1-1-1-2c-2-1-2-3-3-5-1-1 0-2 0-3l-2-2v-1l-1-2c0-1 1-1 1-2v-1c2-1 3-4 4-6 1-1 3-2 4-4 1-1 2-3 4-3l1-1v-1l5-2h0c1 0 2 0 4-1 3 0 6 0 9-1h6 5 1c2 0 4 0 6-1 1-3 3-5 4-8h1c1-3 2-5 4-7l2-1c3-3 6-6 10-9l7-5h1l2-1h1c3-1 6-2 10-3h0l4-1 11-1z" class="f"></path><path d="M436 489h-2v-1l1-1h3l2 1v1h-4z" class="Q"></path><path d="M436 489h4v4 1h-1l-1-1-1-1-1-3z" class="o"></path><path d="M443 466h5c-1 1-1 1-1 2l-1 1c-1 0-2 0-2 2-1 0-1 0-1 1-1 1-1 1-2 1-1-1-1 0 0-1v-1h-1l-1 1-1-1c-1 1-1 2-2 1l2-1 2-3c1 1 1 1 2 1l2-1c0-1 0-1-1-2z" class="Y"></path><path d="M434 502l-2-4v-3c-1-2-1-3-1-5l1-1v-1c0-2 1-4 2-5h0l-1 4v2 1c1 1 2 2 4 2l1 1 1 1h1l1 2 3 5v1h-2l-1-2-3-3v4c-1 0-2-1-3 1h-1z" class="C"></path><path d="M433 490c1 1 2 2 4 2l1 1h-3l-2 2v-1-4z" class="W"></path><path d="M438 493l1 1v1 3c1 0 1 0 2 1v1l-3-3h0c-1-2-2-3-3-4h3z" class="M"></path><path d="M439 494h1l1 2 3 5v1h-2l-1-2v-1c-1-1-1-1-2-1v-3-1z" class="C"></path><path d="M435 469h2c1-1 2-1 3-1l-2 3-2 1c-2 2-5 4-5 6v1c-1 1-2 4-2 6l-1 1-3-1c-1-2 1-2 1-4-1 1-1 0-2 1l-3-2 3-2v-1l4-2 1-1c1 0 2-1 3-1v-2c2 0 2-1 3-2z" class="D"></path><path d="M435 469h2c1-1 2-1 3-1l-2 3h-3c-3 1-3 5-7 6-1 0-3 0-4 1v-1l4-2 1-1c1 0 2-1 3-1v-2c2 0 2-1 3-2z" class="O"></path><path d="M464 450v1c1 1 1 2 0 3l-1 1v2h1c0-1 1-1 1-3 1-2 5-5 7-6h0v1 1 1l-2 2h0c-1 0-2 1-2 2l-2 2c0 1-1 1-1 2-1 1-1 2-1 3l-1 1h-2l-1 1c-1 3-2 6-4 8l-1 4h0c-1-1-2-1-2-2h-1v-1c-2 1-3 2-5 3l-2 2h0c-3 3-5 6-5 10l-2-1-1-2 1-1-2-1v-1c-1 0-1 0 0-1v-1c0-1 1-1 1-2l3-3c1 0 1 0 2-1l1-1h1l1-1h0-2c0-1 0-1 1-1 0-2 1-2 2-2l1-1c0-1 0-1 1-2h1c2 0 4 0 6-1 1-3 3-5 4-8h1c1-3 2-5 4-7z" class="H"></path><path d="M447 468l6-1h1c1 1 1 1 0 2 0 1-1 1-2 1-2 0-5 1-7 2h0-2c0-1 0-1 1-1 0-2 1-2 2-2l1-1z" class="B"></path><path d="M438 484c1-2 0-1 1-3 0-1 3-2 4-4h0l2-1c1-1 1-1 1-2 2-2 4-2 7-3v1 2h-1v-1c-2 1-3 2-5 3l-2 2h0c-3 3-5 6-5 10l-2-1-1-2 1-1z" class="M"></path><path d="M440 488c0-4 2-7 5-10h0l2-2c2-1 3-2 5-3v1l-1 7v12c0 1 1 3 1 5 1 3 3 7 3 10l-1-1-2 2h0l1 2-2 2c-1-4-4-8-7-11v-1l-3-5-1-2v-1-4-1z" class="S"></path><path d="M440 493h1v-4h1v4l1-1h0c0-3 0-4 2-6h4v3l1 1c0 2 0 6-1 8v4h-1l-2-4h0v-1c-1-1-2-1-2-2l-1-1-1 1 1 1v1c-1-1-1-1-2-1l-1-2v-1z" class="I"></path><path d="M437 466h6c1 1 1 1 1 2l-2 1c-1 0-1 0-2-1-1 0-2 0-3 1h-2c-1 1-1 2-3 2v2c-1 0-2 1-3 1l-1 1-4 2v1l-3 2 3 2c1-1 1 0 2-1 0 2-2 2-1 4l3 1v3c-1 2 0 4-1 5l-3 2c0 1 0 1 1 2-1 1-1 2-3 3h-3c-1-1-2-1-3-2-1 0-1-1-2-1 1-1 1-1 1-2l-1-1c0 1 0 1-2 2l-1-1v1c-1 0-2-1-3-1-1-1 0-2 0-3l-2-2v-1l-1-2c0-1 1-1 1-2v-1c2-1 3-4 4-6 1-1 3-2 4-4 1-1 2-3 4-3l1-1v-1l5-2h0c1 0 2 0 4-1 3 0 6 0 9-1z" class="D"></path><path d="M406 490c0-2 0-4 2-5h1c-1 3-1 5-1 8l-2-2v-1z" class="O"></path><path d="M437 466h6c1 1 1 1 1 2l-2 1c-1 0-1 0-2-1-1 0-2 0-3 1h-2c-2 1-4 1-6 2h-2c-2-1-3-1-3-3h0c1 0 2 0 4-1 3 0 6 0 9-1z" class="h"></path><path d="M419 470l5-2c0 2 1 2 3 3-3 1-4 2-6 2-1 1-2 1-2 2h-1c-1 0-1 1-2 1-2 2-3 3-4 5h-1c-1 1-1 3-2 4h-1c-2 1-2 3-2 5l-1-2c0-1 1-1 1-2v-1c2-1 3-4 4-6 1-1 3-2 4-4 1-1 2-3 4-3l1-1v-1z" class="Y"></path><path d="M424 477v1l-3 2 3 2c1-1 1 0 2-1 0 2-2 2-1 4l3 1v3c-1 2 0 4-1 5l-3 2c0 1 0 1 1 2-1 1-1 2-3 3h-3c-1-1-2-1-3-2-1 0-1-1-2-1 1-1 1-1 1-2l-1-1v-3l2-2-1 1-2-2v-2l-1-1 2-2h0c1 1 1 2 1 3h2v-2-1c-1-1-2-1-2-2l1-2h2c1-1 2-2 4-2 1-1 1-1 2-1z" class="Q"></path><defs><linearGradient id="H" x1="444.194" y1="503.076" x2="427.919" y2="522.29" xlink:href="#B"><stop offset="0" stop-color="#afa697"></stop><stop offset="1" stop-color="#c2bda2"></stop></linearGradient></defs><path fill="url(#H)" d="M419 501h3c2-1 2-2 3-3-1-1-1-1-1-2l3-2c1 3 2 5 2 7h0 1c1 1 1 2 2 3 2 3 4 5 7 7h0c2 1 3 1 4 1-1-2-10-7-9-10h1c1-2 2-1 3-1v-4l3 3 1 2h2c3 3 6 7 7 11l2-2-1-2h0l2-2 1 1h1c0 2 1 3 2 5l3 3c-4 1-8 1-11 1-11 0-21 0-32-3 0-1-3-2-3-2-2-1-4-2-6-4h0l-2-3v-1c1 1 2 1 3 1h1c-1 0-1 0-1-1h1l1-1-1-2c-2-1-2-3-3-5 1 0 2 1 3 1v-1l1 1c2-1 2-1 2-2l1 1c0 1 0 1-1 2 1 0 1 1 2 1 1 1 2 1 3 2z"></path><path d="M409 508l-2-3v-1c1 1 2 1 3 1h1c1 1 3 2 4 4h0l-1 1-2-2h-1-2 0z" class="O"></path><path d="M419 501h3c2-1 2-2 3-3-1-1-1-1-1-2l3-2c1 3 2 5 2 7h0v1c0 1 1 2 2 4 1 1 3 2 5 4l1 1 1 1c-1 1-3 1-4 1h-1c-2-1-4 0-5-1h-3c1-1 1-2 1-2l2-2c-2 0-3-1-4-2l1-1v-1h-2l-1-1h2c-2-3-3-1-5-2z" class="B"></path><path d="M438 497l3 3 1 2h2c3 3 6 7 7 11l2-2-1-2h0l2-2 1 1h1c0 2 1 3 2 5h-4 0c-2 0-3 0-4 1h-1c-1 0-2-1-2-2-1 0-1-1-2-1-1-1-1-1-2-1l-2-2c-1-1-3-2-4-3-1-2-1-2-2-3 1-2 2-1 3-1v-4z" class="I"></path><path d="M438 501c2 2 5 4 7 6v1h-1c1 1 3 3 5 4h0-2c-1 0-1-1-2-1-1-1-1-1-2-1l-2-2c-1-1-3-2-4-3-1-2-1-2-2-3 1-2 2-1 3-1z" class="D"></path><path d="M414 495l1 1c0 1 0 1-1 2 1 0 1 1 2 1 1 1 2 1 3 2 2 1 3-1 5 2h-2l1 1h2v1l-1 1c1 1 2 2 4 2l-2 2s0 1-1 2l-1-1h-1c-1-1-1-1-2-1-2-1-3-2-4-3-2 0-1 1-2 0l-3-4-1-2c-2-1-2-3-3-5 1 0 2 1 3 1v-1l1 1c2-1 2-1 2-2z" class="E"></path><path d="M453 474c0 1 1 1 2 2h0l1-4c2-2 3-5 4-8 0 2 0 3-1 5v2c-1 1-1 2-2 3s-1 3-1 5c1 0 1 1 1 1 1 1 1 1 2 3h1v2l-1 1 1 1c2 1 1-1 2-1 1-1 3-1 4-1v-3l3 1 1 2c0 11 1 18 8 26a30.44 30.44 0 0 0 8 8l2-1c0 1 1 2 2 2l6 3h1 0c0 1 0 1 1 2h-1l6 2v1 1c-1 1-1 1-3 1v2h0c-3-1-5-1-7-2-2 0-4-1-6-1l-1 1h1 2c0 1 2 1 2 3h-2l-1-1h-1c-1 0-1-1-2-1h-1l-2-1h-2 0l-2-2c-1 0-2 0-3-1h0-1c-1 0-2-1-3-2-3-3-7-6-10-9l-3-3c-1-2-2-3-2-5h-1c0-3-2-7-3-10 0-2-1-4-1-5v-12l1-7h1z" class="F"></path><path d="M452 498l2-1 3 9h-1v-1l-1 1c1 0 1 1 1 2h-1c0-3-2-7-3-10z" class="i"></path><path d="M472 508c1 1 1 1 1 2l3 3c1 2 0 2-1 4 0 0 1 2 1 3v1c-3-1-5-3-6-5 0-2 0-2 1-3h2v-1l-2-1h-1 0l-1-1 1-1h2v-1zm-11-8c2-1 4-1 6-1 1 1 2 3 2 4-1 2-2 1-3 3l-1 1h-1v1l1 1-1 1c-1-1-1-2-2-3v-1c0-1 0-1-1-2v-2h0c-1-1-1-2-1-3l1 1z" class="M"></path><path d="M487 529c0-2-7-3-8-4-2 0-3-1-4-2-2-3-5-5-7-7v-1l2 1c1 2 3 4 6 5v-1l1-1 1 2c1-1 0-1 1-1h0l-1 2c1 1 1 1 2 1l3 1c2 2 2 2 4 2l1 1c1 1 3 0 4 1 2 1 4 1 6 1h1c1 1 0 1 1 1v2h0c-3-1-5-1-7-2-2 0-4-1-6-1z" class="C"></path><path d="M457 480c1 1 1 1 2 3v3 1 4c1-1 1-1 1-2 2-1 2-2 5-2v1 1 1l-1 1h2v1h-1l2 2v3l-2 1c-2 0-3 1-4 2l-1-1v-1c-1-1-1-1-1-2s0-1-1-2v-1-5c-1-1-1-1-1-2l1-1c-1-2-1 0 0-2h0-2v1 1c0 1 0 3 1 4v1c1 1 0 3 0 3 0 1-1 2-1 3l1 1c1 1 0 1 0 3v-1c-1-1-2-2-2-3l-1-4c0-3 1-5 1-8h-1c1-2 2-2 3-4z" class="W"></path><path d="M460 483v2l-1 1 1 1c2 1 1-1 2-1 1-1 3-1 4-1v-3l3 1 1 2c0 11 1 18 8 26a30.44 30.44 0 0 0 8 8l2-1c0 1 1 2 2 2l6 3h1 0c0 1 0 1 1 2h-1l6 2v1 1c-1 1-1 1-3 1-1 0 0 0-1-1h-1c-2 0-4 0-6-1-1-1-3 0-4-1l-1-1c-2 0-2 0-4-2l-3-1c-1 0-1 0-2-1l1-2h0c-1 0 0 0-1 1l-1-2-1 1c0-1-1-3-1-3 1-2 2-2 1-4l-3-3c0-1 0-1-1-2h-3c-1 0-1 0-1-1 1 0 1-1 2-2-2 0-2 0-4 1 1-2 2-1 3-3 0-1-1-3-2-4-2 0-4 0-6 1 1-1 2-2 4-2l2-1v-3l-2-2h1v-1h-2l1-1v-1-1-1c-3 0-3 1-5 2 0 1 0 1-1 2v-4-1-3h1z" class="K"></path><path d="M486 519l2-1c0 1 1 2 2 2l6 3h1 0c0 1 0 1 1 2h-1c-4-1-8-3-11-6z" class="N"></path><path d="M477 519l1-4h0l3 2c1 2 2 3 3 4h-1c1 2 1 2 0 3l-3-1c-1 0-1 0-2-1l1-2h0c-1 0 0 0-1 1l-1-2z" class="E"></path><path d="M481 517c1 2 2 3 3 4h-1c1 2 1 2 0 3l-3-1 1-2v-4z" class="S"></path><path d="M483 524c1-1 1-1 0-3h1c6 3 12 6 19 7v1c-1 1-1 1-3 1-1 0 0 0-1-1h-1c-2 0-4 0-6-1-1-1-3 0-4-1l-1-1c-2 0-2 0-4-2z" class="e"></path><path d="M512 429h0l12 2 5 2 1 1 1 1-3 1-1-1c-3-1-4-1-6 0 1 1 2 1 4 1l2 1c2 1 3 1 6 3 2 1 4 3 7 5h-1l-4-3-3-1-5-2-1 1-1 1-1 1v2c0 2 0 2-1 3-1-1-2-2-2-3h-1-1c-1 1-2 1-3 1 1 1 2 1 2 3-4-1-7-1-10-1-11 1-21 6-28 14-2 2-4 4-5 6l-1 3-3 7-1 5v3l-1-2-3-1v3c-1 0-3 0-4 1-1 0 0 2-2 1l-1-1 1-1v-2h-1c-1-2-1-2-2-3 0 0 0-1-1-1 0-2 0-4 1-5s1-2 2-3v-2c1-2 1-3 1-5l1-1h2l1-1c0-1 0-2 1-3 0-1 1-1 1-2l2-2c0-1 1-2 2-2h0l2-2v-1-1-1h0c-2 1-6 4-7 6 0 2-1 2-1 3h-1v-2l1-1c1-1 1-2 0-3v-1l2-1c3-3 6-6 10-9l7-5h1l2-1h1c3-1 6-2 10-3h0l4-1 11-1z" class="h"></path><path d="M493 438h0l3 2h1c1 1 2 3 3 4l-1 1h1 2l1-1-2-2h1c2 2 6 3 9 2 1 0 1 0 2-1 1 1 2 1 3 2s2 1 2 3c-4-1-7-1-10-1-11 1-21 6-28 14-2 2-4 4-5 6l-1 3h-1v-4h-1v-1l-1-1-3-3h1 1c1 0 2 1 3 1a30.44 30.44 0 0 1 8-8h0c0-2-2-4-4-5 2 0 4 2 5 3 0 1 0 0 1 1 2-2 2-2 2-5h0c1 1 1 0 2 1l1-1-2-2h2c0 1 0 1 1 2 0 1 0 0 1 1l1-1v-1h2v-1l-2-2v-1c0-1 1-2 2-3v-2z" class="U"></path><path d="M493 438h0l3 2h1c1 1 2 3 3 4l-1 1c-1-1-1-2-2-2v2 1h-1l-5-3c0-1 1-2 2-3v-2z" class="H"></path><path d="M460 464l1-1h2l1-1c0-1 0-2 1-3 0-1 1-1 1-2l2-2c0-1 1-2 2-2-1 1-1 2-2 3l-1 2c1 1 3 1 3 2v1h0-1-1l3 3 1 1v1h1v4h1l-3 7-1 5v3l-1-2-3-1v3c-1 0-3 0-4 1-1 0 0 2-2 1l-1-1 1-1v-2h-1c-1-2-1-2-2-3 0 0 0-1-1-1 0-2 0-4 1-5s1-2 2-3v-2c1-2 1-3 1-5z" class="M"></path><path d="M470 471c-1-1-1-1-1-2h1v-1h0c1-1 1-2 2-2h1v4c-1 0-2 1-2 2l-1-1z" class="J"></path><path d="M473 470h1l-3 7-1 1-2-2h-1v-1c0-2 0-2-1-3h3l1-1 1 1c0-1 1-2 2-2z" class="c"></path><path d="M468 476v-2l1-1c1 2 2 2 2 3v1l-1 1-2-2z" class="U"></path><path d="M466 482v-1l-4-4v-1c1 1 1 0 1 1l1 1h3v-2h1l2 2 1-1-1 5v3l-1-2-3-1z" class="V"></path><path d="M460 464l1-1h2l1-1c0-1 0-2 1-3 0-1 1-1 1-2l2-2c0-1 1-2 2-2-1 1-1 2-2 3l-1 2-2 2c0 2-1 3-1 5l-1 1h0c0 1-1 2-1 3v2l-1 1v2c0 1 0 2-1 3v5 1h-1c-1-2-1-2-2-3 0 0 0-1-1-1 0-2 0-4 1-5s1-2 2-3v-2c1-2 1-3 1-5z" class="E"></path><path d="M512 429h0l12 2 5 2 1 1 1 1-3 1-1-1c-3-1-4-1-6 0 1 1 2 1 4 1l2 1c2 1 3 1 6 3 2 1 4 3 7 5h-1l-4-3-3-1-5-2-1 1-1 1-1 1v2c0 2 0 2-1 3-1-1-2-2-2-3h-1-1c-1 1-2 1-3 1-1-1-2-1-3-2-1 1-1 1-2 1-3 1-7 0-9-2h-1l2 2-1 1h-2-1l1-1c-1-1-2-3-3-4h-1l-3-2h0c-4 1-8 2-11 5-1 0-2 1-3 1h-1c4-3 9-5 13-8 1-2 3-3 5-4h0 0c0-1 0 0 1-1h0l4-1 11-1z" class="U"></path><path d="M520 437l7 2-1 1-1 1-3 1-1-1v-1l-1-2v-1z" class="Q"></path><path d="M493 438l10-2c1 2 1 2 1 3l-1 1h-1l-2-1-3 1h-1l-3-2z" class="Y"></path><path d="M503 436h2 12l3 1v1l1 2v1h-1l-1 1h-1l-2-2h0-1l-1 1v1c-1-1-1-1-2 0l-2-1c0-1-1-2-1-3l-1 1c-1 1-1 1-1 2h-2l-3-1h1l1-1c0-1 0-1-1-3z" class="H"></path><path d="M517 436l3 1v1c-1 0-2 1-3 1v-1-2z" class="O"></path><path d="M502 440l3 1h2c0-1 0-1 1-2l1-1c0 1 1 2 1 3l2 1c1-1 1-1 2 0v-1l1-1h1 0l2 2h1l1-1h1l1 1 3-1-1 1v2c0 2 0 2-1 3-1-1-2-2-2-3h-1-1c-1 1-2 1-3 1-1-1-2-1-3-2-1 1-1 1-2 1-3 1-7 0-9-2h-1l2 2-1 1h-2-1l1-1c-1-1-2-3-3-4l3-1 2 1z" class="o"></path><path d="M512 429h0l12 2 5 2 1 1 1 1-3 1-1-1c-3-1-4-1-6 0-10-2-20-1-30 1 1-2 3-3 5-4h0 0c0-1 0 0 1-1h0l4-1 11-1z" class="H"></path><path d="M390 561l9 1 18 1h9c1 4 0 9 1 12l1 7 5-3 13-7c-1 4-9 9-13 11l-2 1-3 2v6c2 0 3-1 4-1l1 2c1-1 1-2 1-3 2-1 6-1 7-2s2-2 3-1c2 0 3 0 4-1l1 5v3 4l1-1c2 1 4 5 6 6 2 2 4 3 5 4s1 1 1 2l1 1 1-1s1-1 1-2c-1-2 0-1 0-3-1 0-1 1-3 1 0-1 1-2 2-2 2-1 4-1 6-1v5 1c1 2 1 5 1 7h6c1-1 3-1 4-1v3l-2 7v5 10 2h-1l-2-1c0 1-1 1-1 2l-1 5 1 5c1 1 2 1 3 2h0v3h0c-1 1-1 1-1 2h-1c0 2-1 3-1 4h-1-2c-1 1-1 3-1 5v1c-1 1-1 2 0 3 0 2-1 5 0 7h0v2c-1 2 0 6 0 8v4 3 11c0 2 0 4 1 6v1c0 2-2 3-3 5h0c0 2 1 3 2 4-1 0-2 1-3 1-1 8 1 16 1 24 3 1 13 0 15 2-4 0-11 0-15-1v4 8c0 1 0 3-1 4h0c-1 4-3 7-4 11 1 1 2 1 3 1s2 0 3 1c1 0 2 0 3 1h1c-2 2-9 1-12 1h-2c-2 2-5 7-6 9l1 1-1 3h1l-2 2c-1 0-3 1-4 1h-2l-2 2 2 1c3 1 4 0 6 1h3 3l2 1-2 1-4 4c-2 1-6 4-7 6 0 0-2 1-2 2l-2 1-97-1h-41c-3 0-10 0-13-1 7-1 15-1 22-3 15-3 30-14 41-25 26-27 38-62 40-99 1-12 1-25 1-37v-64c-1-5 0-11-1-16v-3h0c-2-2-2-3-5-4h0l-1-1z" class="l"></path><defs><linearGradient id="I" x1="427.438" y1="577.725" x2="428.403" y2="584.758" xlink:href="#B"><stop offset="0" stop-color="#bbb094"></stop><stop offset="1" stop-color="#ddd6be"></stop></linearGradient></defs><path fill="url(#I)" d="M396 566c2 3 3 7 6 8 1 0 1-1 2 0 7 4 11 11 20 9 2 0 3-1 4-1l5-3 13-7c-1 4-9 9-13 11l-2 1-3 2c-3 1-7 1-10 0-9-1-16-8-21-15v-1 15c-1-5 0-11-1-16v-3z"></path><path d="M436 809c-5 1-12 1-18 1h-43c3-3 7-6 10-9l9-12c1 1 1 2 3 2l-2 2c1 1 0 0 2 1-2 1-4 3-5 5 1 0 3-2 4-3v1 1c-3 1-4 3-6 6v1c5 1 9 0 14 1h25 1 1l1 1c1 1 2 1 3 1l1 1z" class="e"></path><path d="M396 796v1 1c-3 1-4 3-6 6v1c-2 2-5 1-8 1v-1c3-1 4-3 7-4 1 0 2 0 3-2 1 0 3-2 4-3z" class="G"></path><path d="M390 805c5 1 9 0 14 1h25c-1 0-1 1-2 1h-2-8c-5 0-11 1-16 1h-20l-1-1 2-2v1c3 0 6 1 8-1z" class="E"></path><path d="M390 561l9 1 18 1h9c1 4 0 9 1 12l1 7c-1 0-2 1-4 1-9 2-13-5-20-9-1-1-1 0-2 0-3-1-4-5-6-8h0c-2-2-2-3-5-4h0l-1-1z" class="m"></path><path d="M429 767c1 0 1 0 2-1l1 1c-1 1-2 2-3 2s-1 1-1 1c0 1 1 2 2 2v2c0 2 1 2 2 4l2-4c2 1 4 2 7 2h4c2 0 3 1 4 1 2 0 4-1 5-1l2-1c1 2 5 1 7 1h1c1 1 2 1 3 1s2 0 3 1c1 0 2 0 3 1h1c-2 2-9 1-12 1h-2c-2 2-5 7-6 9l1 1-1 3h1l-2 2c-1 0-3 1-4 1h-2l-2 2 2 1c3 1 4 0 6 1h3 3l2 1-2 1-4 4c-1-2 0-2-2-3h-1-7 0c-3 1-4 2-6 4-1 0-2 2-3 2l-1-1c-1 0-2 0-3-1l-1-1h-1-1-25c-5-1-9 0-14-1v-1c2-3 3-5 6-6v-1-1c1-1 2-2 2-3 2-1 4-2 6-4 4-2 8-5 11-7 5-5 9-10 14-15z" class="P"></path><path d="M433 796v-1c1-1 3-3 5-4l1 1-1 3c-2 1-3 1-5 1zm-2 6l-1-1c-1-1-1-2-1-2-1 1-1 0-2 1l-1-2h1 1l1-1h2 1 1c1 1 2 1 2 2v2l-1 1h1-4z" class="X"></path><path d="M432 778l2-2c3 1 5 1 8 1 0 0 1 1 1 2h3l-6 3h0v-2c-1 0-1 1-1 1-1 0-1-1-2-1 0 0-2 3-3 3l-1-2 1-1-2-2z" class="d"></path><path d="M434 774c2 1 4 2 7 2h4c2 0 3 1 4 1 2 0 4-1 5-1l2-1c1 2 5 1 7 1h1c1 1 2 1 3 1s2 0 3 1c1 0 2 0 3 1h-7c-4 0-10 0-14-1h-5l-5-1c-3 0-5 0-8-1l-2 2c-1 1-1 2-2 2l2-2 2-4z" class="J"></path><path d="M418 795l3 2 1 1v2l1 1-1 1h1c1 1 1 1 3 0h2 0l1 1h-1c0 1-1 1-2 1h-2c-2-1-3 1-5-1v1c-4 0-9 0-11-1 2-1 4-1 6-1v-1-1h2 0c0-1-1-1-1-2h-1v-1h1l1 1h0l2-3z" class="L"></path><path d="M422 800h-1v-1c-1 0-1 0-2 1h0c-1-1-1-1-2-1l2-2 1 1 1-1 1 1v2z" class="X"></path><path d="M439 792l2 1c3 0 3 0 5-2h-2v-1c-1-1-1-1-1-2h1c1-1 1-2 3-2v1l-2 1v1h2l1-1c1 1 1 1 3 1 2-1 2-2 3-4v1c0 1-1 3-2 4h0c1 1 0 1 1 1l1-2 1 1-1 3h1l-2 2c-1 0-3 1-4 1h-2l-2 2c-1-1-1-1-2-1h-1v-2l-1-1-1 1v1c-1-1-2-1-2-1l1-3z" class="S"></path><path d="M442 795l5-1v2l-2 2c-1-1-1-1-2-1h-1v-2z" class="J"></path><path d="M454 793h1l-2 2c-1 0-3 1-4 1h-2v-2l7-1z" class="k"></path><path d="M438 795s1 0 2 1v-1l1-1 1 1v2h1c1 0 1 0 2 1l2 1c3 1 4 0 6 1h3 3l2 1-2 1-4 4c-1-2 0-2-2-3h-1-7 0c-3 1-4 2-6 4-1 0-2 2-3 2l-1-1c-1 0-2 0-3-1l-1-1h-1v-2l1-2h4-1l1-1v-2c0-1-1-1-2-2v-1c2 0 3 0 5-1z" class="J"></path><path d="M453 803l1-1h5l-4 4c-1-2 0-2-2-3z" class="k"></path><path d="M435 801h1l-1 1c2 1 1 1 2 0h4 0 1c-1 1-2 0-3 1-2 1-2 3-4 4-1-1-2-2-2-3h1l1-1v-1h-1l1-1z" class="V"></path><path d="M438 795s1 0 2 1v-1l1-1 1 1v2h1c1 0 1 0 2 1l2 1c3 1 4 0 6 1h3 3l-1 1c-2 1-3 1-5 0h-5l-1 1c-1 0-2 0-3-1l-2 1h-1 0-4c-1 1 0 1-2 0l1-1h-1v-2c0-1-1-1-2-2v-1c2 0 3 0 5-1z" class="Z"></path><path d="M416 788c1 1 2 1 3 2 2-1 4-4 6-5v1l-3 3h1 0c1 1 1 1 2 1h1v1h5v1h-1 0l1 1c-1 1-1 2-2 3-2-1-2-2-3-3v3c-2 0-1 0-2-1h-2-1 0c0 1 1 2 2 2l-1 1-1-1-3-2-2 3h0l-1-1h-1v1h1c0 1 1 1 1 2h0-2v1 1c-2 0-4 0-6 1l-1-1-1 1c-2-1-3-1-4-2l-2 1h-1-3l-1 1h-1l-2 1c0-1 1-2 1-3 3-1 3 1 5-1v-1c-1 1-1 1-2 1l2-3c2 0 3-1 4-2 1 0 1-1 1 0h1l3-1-1-1h-2v-1h3l1 1v-1s0-1-1-1h0l1-1c1 1 1 1 2 1 1-1 2-1 4-1h0l2-2z" class="X"></path><path d="M410 791c1-1 2-1 4-1l-1 2h-3v-1z" class="N"></path><path d="M418 795h2v-1h-1v-1l2-1 1 1v-1c2 0 3 0 4 1v3c-2 0-1 0-2-1h-2-1 0c0 1 1 2 2 2l-1 1-1-1-3-2z" class="S"></path><path d="M429 767c1 0 1 0 2-1l1 1c-1 1-2 2-3 2s-1 1-1 1c0 1 1 2 2 2v2c0 2 1 2 2 4l-2 2c1 0 1-1 2-2l2 2-1 1 1 2-2 2h2c0 1 1 2 2 3v1h-7l-3 1h0-1c-1 0-1 0-2-1h0-1l3-3v-1c-2 1-4 4-6 5-1-1-2-1-3-2l-2 2h0c-2 0-3 0-4 1-1 0-1 0-2-1l-1 1h0c1 0 1 1 1 1v1l-1-1h-3v1h2l1 1-3 1h-1c0-1 0 0-1 0-1 1-2 2-4 2l-2 3c1 0 1 0 2-1v1c-2 2-2 0-5 1 0 1-1 2-1 3h-1-1c2-3 3-5 6-6v-1-1c1-1 2-2 2-3 2-1 4-2 6-4 4-2 8-5 11-7 5-5 9-10 14-15z" class="Z"></path><path d="M423 781v-1h0l2-1 1-1c1 0 1 0 2-1l-1-1h0v-1l-1 1c-1-1-2-1-2-2l1-1c2 1 3 1 4 1h1c0 2 1 2 2 4l-2 2-2 2c-1-1-1-1-2-1h0-1-2z" class="e"></path><path d="M430 780c1 0 1-1 2-2l2 2-1 1 1 2-2 2h2c0 1 1 2 2 3v1h-7l-3 1h0-1c-1 0-1 0-2-1h0-1l3-3v-1c-2 1-4 4-6 5-1-1-2-1-3-2v-1c-1 0-2 0-2 1h-1c-1 0-1 0-2 1v-1l1-2c2-1 4 0 6 1l1-2h0-2c-2-1-2-1-2-2l1-1h1 0 1c1-1 1-1 1-2 1-1 1 0 2 0v1 2c1-1 2-1 2-2h2 1 0c1 0 1 0 2 1l2-2z" class="N"></path><path d="M434 590c2-1 6-1 7-2s2-2 3-1c2 0 3 0 4-1l1 5v3 4l1-1c2 1 4 5 6 6 2 2 4 3 5 4s1 1 1 2l1 1 1-1s1-1 1-2c-1-2 0-1 0-3-1 0-1 1-3 1 0-1 1-2 2-2 2-1 4-1 6-1v5 1c1 2 1 5 1 7h6c1-1 3-1 4-1v3l-2 7v5 10 2h-1l-2-1c0 1-1 1-1 2l-1 5 1 5c1 1 2 1 3 2h0v3h0c-1 1-1 1-1 2h-1c0 2-1 3-1 4h-1-2c-1 1-1 3-1 5v1c-1 1-1 2 0 3 0 2-1 5 0 7h0v2c-1 2 0 6 0 8v4 3 11c0 2 0 4 1 6v1c0 2-2 3-3 5h0c0 2 1 3 2 4-1 0-2 1-3 1-1 8 1 16 1 24 3 1 13 0 15 2-4 0-11 0-15-1v4 8c0 1 0 3-1 4h0c-1 4-3 7-4 11h-1c-2 0-6 1-7-1l-2 1c-1 0-3 1-5 1-1 0-2-1-4-1h-4c-3 0-5-1-7-2l-2 4c-1-2-2-2-2-4v-2c-1 0-2-1-2-2 0 0 0-1 1-1s2-1 3-2l-1-1c-1 1-1 1-2 1-5 5-9 10-14 15-3 2-7 5-11 7-2 2-4 3-6 4 0 1-1 2-2 3s-3 3-4 3c1-2 3-4 5-5-2-1-1 0-2-1l2-2c-2 0-2-1-3-2 12-16 22-35 28-55 5-18 6-37 7-55l-1-51 1-2v-30l-1-4c2 0 3-1 4-1l1 2c1-1 1-2 1-3z" class="c"></path><path d="M434 628h14l-1 1-4 1c-2-1-5-1-7-1-1 0-1 0-2-1z" class="W"></path><path d="M449 712c-1-2 0-7 1-9l2 3h-1v4c0 1-1 2-2 2z" class="S"></path><path d="M444 623l1-1 2 2h2v2c-2 1-5 1-7 0h0v-1c0-1 1-2 2-2z" class="E"></path><path d="M444 623l1-1 2 2c-2 1-3 2-5 2v-1c0-1 1-2 2-2z" class="D"></path><path d="M457 652c2 1 2 3 5 2h0 1 0c2 1 2 1 2 3 0 0 0 1-1 2-3-1-6-4-8-6l1-1z" class="E"></path><path d="M452 706l4 3v1c-1 0-2 1-4 1l1 1h0l-2 2v1c-1-1-1 0-2-1l1-1h-1v-1c1 0 2-1 2-2v-4h1z" class="d"></path><path d="M440 622c2 0 3 0 4 1-1 0-2 1-2 2v1h0c-2 0-6 1-8 0 1-1 2-1 3-2s2-1 3-2z" class="C"></path><path d="M433 680c5 0 10 0 15 1h0v1c-3-1-8 0-11 0-2-1-3-1-4-1v-1z" class="E"></path><path d="M449 714c1 1 1 0 2 1v-1l2-2h0v1c1 1 0 1 0 2 1 0 1 0 1-1 1 1 1 1 1 2 1 0 2 1 3 1 0 1-1 1-1 2l-1-1c-1-1-1-1-2-1l-1-1-2 1h0c1 2 1 4 0 5v1h0-1c-1 0-2-1-3-2 1-2 1-2 2-3h-1v-3l1-1z" class="S"></path><path d="M448 677v2h-14c-1-2-1-6 0-9v2h2c0 1 0 1 1 2 1 0 3 0 4 2v1c2 0 4-1 6 0h1z" class="I"></path><path d="M434 628c1 1 1 1 2 1v1 2 3 5c0 1 0 5-1 6l1 1v2 1 3l-2 2 1 1v2h-1v-5-15-7c0-1-1-1-1-2v-1h1z" class="i"></path><path d="M450 684l1 1h2v1 2c1 1 2 3 3 5-1 0-2 0-3 1 0 1 0 1-1 2-1 0-1 1-2 1v1c1 0 2-1 3-1v2l-3 4v-3-1c-1-5-1-10 0-15z" class="X"></path><path d="M451 685h2v1 2c1 1 2 3 3 5-1 0-2 0-3 1l-1-1v-2h0c-1-1-1-2-1-3v-2-1z" class="I"></path><path d="M433 599l1-6h8l1-1v-1l2 1h1 2l1-1v3l-1 4-1-1-2 1-3-2c-2 1-3 2-5 1h-2l-1 1-1 1z" class="e"></path><path d="M442 596c1-1 2-1 3-1l1 1c0 1 0 1 1 1l-2 1-3-2z" class="d"></path><path d="M453 694c1-1 2-1 3-1 0 1 1 2 2 3l1 2c2 1 3 2 3 4v1l-1-1c-1 0-2 0-3 1h-1c0-1-1-1-1-1-1 1-1 1-2 1h-1l-1 1-2-1 3-4v-2c-1 0-2 1-3 1v-1c1 0 1-1 2-1 1-1 1-1 1-2z" class="G"></path><path d="M453 699v4l-1 1-2-1 3-4z" class="d"></path><path d="M458 696l1 2v2l1 1c-1 0-2 1-2 1-1-1-2-1-2-2 1-1 1-2 2-4zm-1 7h1c1-1 2-1 3-1l1 1v-1c1 1 2 2 2 3l1 1c0 2-2 4-3 5l-2-1c-3-1-6-3-8-6l1-1h1c1 0 1 0 2-1 0 0 1 0 1 1z" class="I"></path><path d="M457 703h1c1-1 2-1 3-1l1 1-2 2c-1 0-1 0-1 1-1-1-2-1-3-1l1-2z" class="i"></path><path d="M456 709c2 1 4 2 6 4l1 1 1 1-1 2v1 1h0c-1 1-1 3-2 3h-1v-1l-1 1h-1 0l1-2-1-1-1 1v-1c0-1 1-1 1-2-1 0-2-1-3-1 0-1 0-1-1-2 0 1 0 1-1 1 0-1 1-1 0-2v-1l-1-1c2 0 3-1 4-1v-1z" class="I"></path><path d="M462 713l1 1 1 1-1 2v1 1h0c-1 1-1 3-2 3h-1v-1l1-1c-1-2 0-2-1-4h1l1-1-1-1 1-1z" class="N"></path><path d="M452 628c0-1 1-2 3-3 2-2 4-3 6-3 1 1 1 2 2 2v1 1l-2 1v1c-2 0-4 2-4 3-1 2 0 4 1 5h-2v-1c-3 0-4-2-6-4 0-1 0-2 2-3z" class="F"></path><path d="M452 628v3h1 0c1 1 1 1 2 1v1 2h1c-3 0-4-2-6-4 0-1 0-2 2-3z" class="i"></path><path d="M429 596h2 1c1 3 0 7 0 10l1 20-1 1c-1 0-2 0-3-1v-30z" class="F"></path><path d="M428 628c2 0 3-1 4 0h1v1c-1 5 0 49-1 50h-2-1l-1-51z" class="Q"></path><path d="M456 623l-6 4v-28l11 9v3 2l1 1v2c0 2 0 3-1 4h0c-1 0-2 2-3 2l-2 1z" class="C"></path><path d="M456 623c-1-2-2-2-4-3l1-1 1-1c1-2-1-2-2-4 0-1 0-1 1-1h2c0 1 0 1 1 2 2 2 3 2 6 1 0 2 0 3-1 4h0c-1 0-2 2-3 2l-2 1z" class="D"></path><path d="M458 622c-1-1-2-1-3-2 0-2 0-2 2-3l4 3c-1 0-2 2-3 2z" class="I"></path><path d="M450 631c2 2 3 4 6 4v1 1c1 1 2 1 2 2h1l1 1c0 1 0 1 1 1 1-1 0 0 2 0h1c2 1 2 1 2 3v1 1l-1 2h1v4l-1 1c-1-1-2-1-3-1h0c-1-1-2-1-3-1l3 3h0c-3 1-3-1-5-2l-1 1c-2-1-5-4-6-7v-1c-1-3 0-8 0-11 1 2 2 7 5 9-1-2-2-4-3-7h0 0c-1-2-2-3-2-5z" class="o"></path><path d="M450 645h1l1-1c1 0 1 0 1 1 1 0 1 1 2 2l-1 1c-1 0-1-1-2-1 0-1-1-1-2-1v-1zm7 7l-1-1c1-1 1-1 1-2h1l1 1v1l3 3h0c-3 1-3-1-5-2z" class="B"></path><path d="M450 631c2 2 3 4 6 4v1 1c0 1 0 2 1 3v1 1l-1-1c-1-1-2-2-2-3s0-2-1-3l-1 1h0c-1-2-2-3-2-5z" class="G"></path><path d="M462 652l-2-3c-1-1-1-1-2-1l-2-3 1-1c0 1 0 1 1 1v-1l1-1 1 1v-1l1 1h0-1c1 2 2 3 3 4h1l-1-2h0 2 1l-1 2h1v4l-1 1c-1-1-2-1-3-1z" class="C"></path><path d="M436 653l1-1c0-1 0-1 1-2v2l2-1h0l1 1c1 1 1 0 2 1l1 1c0 1-1 2-1 4h1l2 2 1 2 1-1v9 5 2h-1c-2-1-4 0-6 0v-1c-1-2-3-2-4-2-1-1-1-1-1-2h-2v-2-1-1-6-9 5h1v-2l-1-1 2-2z" class="G"></path><path d="M446 667l2 3v5l-1-2h-1v-6z" class="I"></path><path d="M447 662l1-1v9l-2-3v-1-3l1-1z" class="C"></path><path d="M434 668l1-1v-1h0l1 1h1l1-1h-1l1-1c1 2 0 2 2 3v1c-1 1-1 2-1 4h-1c-1-2-1-2-1-4l-1-1v2c-1 0-1 0-2-1h0v-1z" class="I"></path><path d="M448 628l1 1c-1 3-1 7-1 11 0 3 1 7 0 10v5 3 3l-1 1-1-2-2-2h-1c0-2 1-3 1-4l-1-1c-1-1-1 0-2-1l-1-1h0l-2 1v-2c-1 1-1 1-1 2l-1 1v-3-1-2l-1-1c1-1 1-5 1-6v-5-3-2-1c2 0 5 0 7 1l4-1 1-1z" class="B"></path><path d="M437 644h1 1v3h-1l-1-3z" class="G"></path><path d="M448 650v5 3 3l-1 1-1-2-2-2 3-4v1l1-1v-4z" class="I"></path><path d="M442 596l3 2 2-1 1 1 1 6-1 10c1 3 0 7 1 10h-2l-2-2-1 1c-1-1-2-1-4-1-1 1-2 1-3 2s-2 1-3 2c-1-2 0-6 0-9l-1-14v-4l1-1 1-1h2c2 1 3 0 5-1z" class="i"></path><path d="M436 607h1l1 1c0 1 0 2-1 3-1-1-1 0-1-1h-1 0v-1l1-1v-1z" class="W"></path><path d="M443 606v-2-1h3v1l-3 2h0z" class="D"></path><path d="M434 617l2 2 1 1h-1l-1 1 1 1v-1h2v1h1 1c-1 1-2 1-3 2s-2 1-3 2c-1-2 0-6 0-9z" class="o"></path><path d="M438 621h2 2 2 1c1-1 2-1 3-2-1-1-1-2-1-4l1-1c1 3 0 7 1 10h-2l-2-2-1 1c-1-1-2-1-4-1h-1-1v-1z" class="G"></path><path d="M442 596l3 2 2-1 1 1 1 6c-1 0-2-1-3-1 0-1-1-3-1-4h-1l-2 2c-1 0-1 0-2-1l-2 2c0-1 0-1-1-2s-1-1-3 0l-1 3v-4l1-1 1-1h2c2 1 3 0 5-1z" class="C"></path><path d="M455 677h1c0 1 0 2-1 3h1 1 1l1 1h1 7v7 6c1 1 3 1 4 2v11c0 2 0 4 1 6v1c0 2-2 3-3 5h0c0 2 1 3 2 4-1 0-2 1-3 1-1-2-1-3-2-4l-2 1-1-2h0v-1-1l1-2-1-1c0-2 0-2-1-3 1-1 3-3 3-5l-1-1c0-1-1-2-2-3 0-2-1-3-3-4l-1-2c-1-1-2-2-2-3-1-2-2-4-3-5v-2-1h-2l-1-1v-2c1-1 1 0 1-1h2c1-2 1-3 2-4z" class="L"></path><path d="M463 719h0v-1-1l1-2c2 2 2 2 2 5l-2 1-1-2z" class="I"></path><path d="M467 694c1 1 3 1 4 2v11h0c-2 1-3 1-4 0 1-1 1-2 0-4h-1c2-3 0-1 0-2l1-1v-6z" class="B"></path><path d="M454 686v-3l1-1c2-1 7 0 10 0l1 1v1 2l-1 1h1c0 2 0 3-1 3h-1 0c1 1 1 1 2 1v5c0 1-1 1-2 2v1l1-1 1 1h0c-1 1-1 1-1 2-2 0-3-1-3-2-1-1-2-2-2-3-2-2-4-4-5-7l-1-3z" class="E"></path><path d="M454 686h1c1 1 1 1 2 1 1-1 1 0 1-1l2 2-1 1h1v2c1 2 3 2 2 3h0l-2 2c-2-2-4-4-5-7l-1-3zm-21-5c1 0 2 0 4 1 3 0 8-1 11 0 1 11 0 23-1 34l-2-3h-1c-1-2-4-1-6-2h1v-1h1v-1h-1c-1 0-2 0-3-1l-1 1h0l-1 1v-2h-1c0 1 0 1-1 2 0-1 0-1-1-3 1-1 0-4 0-5 1-2 1-4 1-7v-2c0-4 0-8 1-12z" class="C"></path><path d="M436 701h1 0c0 2 0 3-1 4h-2c1-3 1-3 2-4z" class="d"></path><path d="M466 652c1 0 1 0 1-1h1 1l-1-2v-1h2v2c0 2-1 3 0 5l1-1h4v-2c1 1 2 1 3 2h0v3h0c-1 1-1 1-1 2h-1c0 2-1 3-1 4h-1-2c-1 1-1 3-1 5v1c-1 1-1 2 0 3 0 2-1 5 0 7h0v2c-1 2 0 6 0 8v4 3c-1-1-3-1-4-2v-6-7h-7-1l-1-1h-1-1-1c1-1 1-2 1-3h-1c-1 1-1 2-2 4h-2l-1-1v-9-2-2-5h0v-3-1-9c2 2 4 4 6 5l9 7v2l1-1c-1-1-1-2-2-3 1-1 1-2 1-2 0-2 0-2-2-3h0-1l-3-3c1 0 2 0 3 1h0c1 0 2 0 3 1l1-1z" class="G"></path><path d="M453 662l1 1v-1h1v1c1 0 1 1 2 2l-1 1h0c1 0 1 0 2-1h1 0l-1 1c-1 1-2 3-4 3-1-1 0-2-1-3v-1-3z" class="E"></path><path d="M467 688h3c0 1 0 1-1 2l2 3v3c-1-1-3-1-4-2v-6z" class="C"></path><path d="M471 679c-2 0-3 0-4-1v-2-1l-1-3 1-1c2-1 2 0 4 1 0 2-1 5 0 7z" class="D"></path><path d="M450 671l2-1 1 1-1 1 1 2h0v1l1 1 1 1c-1 1-1 2-2 4h-2l-1-1v-9z" class="N"></path><path d="M475 652c1 1 2 1 3 2h0v3h0c-1 1-1 1-1 2h-1c0 2-1 3-1 4h-1-2c-1 1-1 3-1 5-1-5 0-9-1-13l1-1h4v-2z" class="J"></path><path d="M475 652c1 1 2 1 3 2h0v3h0c-1 1-1 1-1 2h-1c0 2-1 3-1 4h-1c0-3 0-6 1-9v-2z" class="E"></path><path d="M464 603c2-1 4-1 6-1v5 1c1 2 1 5 1 7h6c1-1 3-1 4-1v3l-2 7v5 10 2h-1l-2-1c0 1-1 1-1 2l-1 5 1 5v2h-4l-1 1c-1-2 0-3 0-5v-2h-2v1l1 2h-1-1c0 1 0 1-1 1v-4h-1l1-2v-1-1c0-2 0-2-2-3h-1c-2 0-1-1-2 0-1 0-1 0-1-1l-1-1h-1c0-1-1-1-2-2v-1h2c-1-1-2-3-1-5 0-1 2-3 4-3v-1l2-1v-1c1-2 0-6 0-8-1-2-1-5 0-7l1-1s1-1 1-2c-1-2 0-1 0-3-1 0-1 1-3 1 0-1 1-2 2-2z" class="B"></path><path d="M470 646l-2-2v-5c0-1-1-2 0-4h2v7 4z" class="C"></path><path d="M461 627l2 1c1 1 1 2 1 4s-1 2-2 4h-2c-1 1-2 0-2 0-1-1-2-3-1-5 0-1 2-3 4-3v-1z" class="R"></path><path d="M461 627l2 1c1 1 1 2 1 4-1 1-2 1-4 2 0 1 0 0-1 0s-1-1-1-2l3-2v-2-1z" class="n"></path><path d="M464 603c2-1 4-1 6-1v5 16c-1 0-3 0-4-1 0-1 0-1-1-2h0-1l1-1 3 2c1-1 1-1 1-2l-1-1v-2l-3 2h-1v-1c1 0 2-1 3-1l-1-1c-1 0-1 0-2-1h-1v3c-1-2-1-5 0-7l1-1s1-1 1-2c-1-2 0-1 0-3-1 0-1 1-3 1 0-1 1-2 2-2z" class="E"></path><path d="M470 607v1c1 2 1 5 1 7h6c1-1 3-1 4-1v3l-2 7v5h-1s-1 0-2-1l-4 1h0l-1-1-1-3v-2-16z" class="l"></path><path d="M477 615c1-1 3-1 4-1v3l-2 7-1-1-2 1-2-2 2-1c1 0 1 0 2-1v-1l-1-3v-1z" class="q"></path><path d="M470 607v1c1 2 1 5 1 7h6v1h-6l1 1c0 2 0 5-1 7v1c2 1 2 1 4 1-2 1-2 0-3 0h-1v1c2 1 3 1 5 1l-4 1h0l-1-1-1-3v-2-16z" class="k"></path><path d="M470 635v-10l1 3 1 1h0l4-1c1 1 2 1 2 1h1v10 2h-1l-2-1c0 1-1 1-1 2l-1 5 1 5v2h-4l-1 1c-1-2 0-3 0-5v-2-2-4-7z" class="n"></path><path d="M478 629h1v10 2h-1l-2-1c0 1-1 1-1 2v-2c0-1 1-1 1-2h1c2-2 1-5 1-7v-2z" class="U"></path><path d="M470 635v-10l1 3 1 1h0l4-1c1 1 2 1 2 1v2c-1 0-3 0-5-1h-2v1 2c1 1 2 2 3 4 0 1-1 1-2 2v4h0l-2-1v-7z" class="r"></path><path d="M470 642l2 1c1 1 1 3 1 4h1l1 5v2h-4l-1 1c-1-2 0-3 0-5v-2-2-4z" class="a"></path><path d="M470 650h4c0 2-2 3-3 4l-1 1c-1-2 0-3 0-5z" class="R"></path><path d="M431 707c1 2 1 2 1 3 1-1 1-1 1-2h1v2l1-1h0l1-1c1 1 2 1 3 1h1v1h-1v1h-1c2 1 5 0 6 2h1l2 3-1 5c-2 8-4 15-6 22l-4 10-1 2c-1 1-2 2-2 4-1 1-1 2-2 2h-1-1l-2-1h0v-1h2l1-1-1-1v1l-2-1c-1-1-1-2-1-3l-1 1h-1v-1h1c0-1-1-1-1-2h-1l-1-1v-1c1-1 1-1 1-2l-1-1c1-5 4-10 5-15v-1c0-1 0-2 1-3v-2l1-5v-2c1-1 0-3 1-4 1-2 1-6 1-8z" class="I"></path><path d="M426 754c0-2 1-4 2-5v-2l1-1v-3-1c1 0 1-1 1-1l1-1v-1c1-1 0-1 1-1 1 1 2 0 3 1h2l1 1h1v-1l1-1v-1l-2-2v-1-2h0l1 1h0l1 2h1v-1l-1-1c0-2 1-1 2-3l-1-1v-1l1-1v-1h-2c1-2 0-1 0-3h1 1l1 1c1-1 1-2 3-3-2 8-4 15-6 22l-4 10-1 2c-1 1-2 2-2 4-1 1-1 2-2 2h-1-1l-2-1h0v-1h2l1-1-1-1v1l-2-1c-1-1-1-2-1-3z" class="d"></path><path d="M434 751l2 2-1 2-2-1c0-1-1-1-1-2l2-1z" class="L"></path><path d="M437 746c-1 1-1 2-1 3l-1 1c-1 0 0 0-1 1l-2 1-2-1v-1l2-2 1 1h1v-1-1l3-1z" class="C"></path><path d="M437 746c0-2 0-3 1-4l2 1-4 10-2-2c1-1 0-1 1-1l1-1c0-1 0-2 1-3z" class="e"></path><path d="M429 679h1l3 1v1c-1 4-1 8-1 12v2c0 3 0 5-1 7 0 1 1 4 0 5 0 2 0 6-1 8-1 1 0 3-1 4v2l-1 5v2c-1 1-1 2-1 3v1c-1 5-4 10-5 15l1 1c0 1 0 1-1 2v1l1 1h1c0 1 1 1 1 2h-1v1h1l1-1c0 1 0 2 1 3l2 1v-1l1 1-1 1h-2v1h0l2 1h1 1l-6 9c-1 2-4 3-4 5l-2 2c-2 1-3 3-5 4-3 2-6 5-10 7-2 1-4 3-6 5 0 1-1 2-2 3s-3 3-4 3c1-2 3-4 5-5-2-1-1 0-2-1l2-2c-2 0-2-1-3-2 12-16 22-35 28-55 5-18 6-37 7-55z" class="B"></path><defs><linearGradient id="J" x1="406.928" y1="787.373" x2="411.798" y2="771.045" xlink:href="#B"><stop offset="0" stop-color="#767364"></stop><stop offset="1" stop-color="#8e8876"></stop></linearGradient></defs><path fill="url(#J)" d="M413 766l1 3-1 2 1 1 1-1 1 1v1h0l3-3v2l-1 1c2 0 2 0 3 2l-2 2c-2 1-3 3-5 4-3 2-6 5-10 7-2 1-4 3-6 5 0 1-1 2-2 3s-3 3-4 3c1-2 3-4 5-5-2-1-1 0-2-1l2-2c5-4 9-12 12-17l2-4 2-4z"></path><path d="M416 773h0l3-3v2l-1 1c2 0 2 0 3 2l-2 2c-2 1-3 3-5 4-2-1-2 0-4 0v1l-1-1 1-1 1-2h1l1-1 3-3v-1z" class="S"></path><g class="d"><path d="M418 773c2 0 2 0 3 2l-2 2c-1-2-1-3-1-4zm-2 1c1 1 1 2 0 3 0 1 0 1-1 1s-2 0-2-1l3-3z"></path><path d="M422 747l1 1c0 1 0 1-1 2v1l1 1h1c0 1 1 1 1 2h-1v1h1l1-1c0 1 0 2 1 3l2 1v-1l1 1-1 1h-2v1h0l2 1h1 1l-6 9c-1 2-4 3-4 5-1-2-1-2-3-2l1-1v-2l-3 3h0v-1l-1-1-1 1-1-1 1-2-1-3c1-1 1-2 2-3 0 0 0-1 1-1v-2h1c0-1 1-2 1-3l3-7c0-2 1-2 1-3z"></path></g><path d="M426 754c0 1 0 2 1 3l2 1v-1l1 1-1 1h-2v1h0l2 1h1 1l-6 9c-1 2-4 3-4 5-1-2-1-2-3-2l1-1v-2l-3 3h0v-1c1-1 2-2 2-3h1l-1-1c1-2 1-2 3-4v-1h1v-1l1-1 1 1 1-1-2-3v-1l2 1h0v-3l1-1z" class="e"></path><path d="M425 761l1 1-2 2v2h0-2c-1 0-1-1-1-2v-1h1v-1l1-1 1 1 1-1z" class="I"></path><path d="M451 723h0v-1c1-1 1-3 0-5h0l2-1 1 1c1 0 1 0 2 1l1 1v1l1-1 1 1-1 2h0 1l1-1v1h1c1 0 1-2 2-3l1 2 2-1c1 1 1 2 2 4-1 8 1 16 1 24 3 1 13 0 15 2-4 0-11 0-15-1v4 8c0 1 0 3-1 4h0c-1 4-3 7-4 11h-1c-2 0-6 1-7-1l-2 1c-1 0-3 1-5 1-1 0-2-1-4-1h-4c-3 0-5-1-7-2l-2 4c-1-2-2-2-2-4v-2c-1 0-2-1-2-2 0 0 0-1 1-1s2-1 3-2l-1-1c-1 1-1 1-2 1 0-1 1-1 1-2l1-1h0c1-1 1-2 2-3l3 2v-1l-2-2c0-1 1-3 2-4h2l1-1-2-1 1-1c0-2 0-2 1-4 1-1 1-2 2-3 0-2 1-3 1-4 0-2 2-4 2-6l3-15c1 1 2 2 3 2h1z" class="N"></path><path d="M462 762c-1 0-2 0-3-1h0c1-2 1-1 3-1v-2h1c0 1-1 3-1 4z" class="G"></path><path d="M434 774l3-3c1 0 1 0 1-1h1c0 1 0 0 1 1s1 2 2 2l1-1 2 2v2h-4c-3 0-5-1-7-2zm5-25c1 0 2 0 3-1h-1l1-1h2 1 1c0 1 1 2 2 3l1-1v2l-1 1c0 1 0 1-1 2v-2h-1c0 1-1 1-1 1h-1-1c-2 1-4 0-5 0 0-2 0-2 1-4z" class="X"></path><path d="M440 749h1c2 1 2 2 3 3-1 0-2 0-4-1v-2z" class="d"></path><path d="M446 754l1 1v1h1v2 1l1 1c1 0 1 0 2-1l1 1-1 2 3 1 2-1v1l-1 1-4-1h0l-1-1c0 2-1 2-2 3l1 1c-1 2-1 3-3 4l-1-1 1-1 1-1-1-1c1-1 0-1 1-2h0c-1-1-1-1-2-1l-1 1 1 2h-1c-1 0-1 1-2 1h0c0 1-1 0 0 1v2c-2 0-2 0-3-1 0-1 1-1 1-2l5-10c0-1 1-2 1-3z" class="X"></path><path d="M466 749c2-1 2 0 3 0v4 8c0 1 0 3-1 4h0c-1 4-3 7-4 11h-1c-2 0-6 1-7-1v-1c0-1 1-1 1-2s0-1-1-1c-1-1-2-1-3 0h0c-1-1-2-1-2-2l1-1h3l1-1 2 2h1c0-1 1-2 1-3 1-1 1-2 2-4 0-1 1-3 1-4 1 0 1-1 1-2v-1c1-1 1-2 1-4-2-1-3 0-5-2h0c2 0 3 0 5 1h2l-1-1z" class="p"></path><path d="M468 765h-1c-1 1-1 0-1 1v1h-1l-2-1 1-1s0-1 1-1v-1-2h1c0 1 1 1 2 2v2h0z" class="N"></path><path d="M451 723h0v-1c1-1 1-3 0-5h0l2-1 1 1c1 0 1 0 2 1l1 1v1l1-1 1 1-1 2h0 1l1-1v1h1c1 0 1-2 2-3l1 2 2-1c1 1 1 2 2 4-1 8 1 16 1 24 3 1 13 0 15 2-4 0-11 0-15-1-1 0-1-1-3 0h-1c0-1-1-1-2-1h0c-1-1-1 0-1-1v-1h1c1 0 1 0 2-1-1 0-1-1-2-1v-2-1h-3v-1l-1 1c-1 1-1 0-3 0h-1-1c-1-2 0-1 0-2v-1l-2 1c0 1 0 2-1 3l-1 1c2 0 3 1 5 2 1 0 1 0 1 1-1 0-1 0-3-1h-2 0c0 2-1 3-2 4l-1 1c-1-1-2-2-2-3h-1-1-2l-1 1h1c-1 1-2 1-3 1 1-1 1-2 2-3 0-2 1-3 1-4 0-2 2-4 2-6l3-15c1 1 2 2 3 2h1z" class="d"></path><path d="M454 717c1 0 1 0 2 1l-1 1v1h-1v-2h-1l1-1zm6 4v1h1v1l1 1v2c-2 1-2 1-4 0v-1c1 0 1-1 1-1l1-1-1-1 1-1z" class="G"></path><path d="M447 721c1 1 2 2 3 2h1v2c-1 0-2-1-3 0l-1 1h1c1 1 0 1 1 2h-1v1c1 1 0 1 0 2l1 1h-2c0 1-1 3-1 4l1 1h-2c0 1 0 1 1 2h-1c-1 0-1 0-1 1v2l1 1v1h-2v1h1c1 0 1 0 2 1l1-1h3v-2c2 0 3 1 5 2 1 0 1 0 1 1-1 0-1 0-3-1h-2 0c0 2-1 3-2 4l-1 1c-1-1-2-2-2-3h-1-1-2l-1 1h1c-1 1-2 1-3 1 1-1 1-2 2-3 0-2 1-3 1-4 0-2 2-4 2-6l3-15z" class="L"></path><path d="M446 747v-1h1 2v3l-1 1c-1-1-2-2-2-3z" class="G"></path><path d="M539 202h3c1 2 3 5 4 7h1 0c4 2 34 1 40 1v3l1-3h138v208l-9-22c-17-40-35-82-79-99-11-5-22-7-34-8-11-1-22-1-33-1h-12c-2 0-4 0-6 1v48 12 6c-1 2 0 5-1 6h0l-2-4c-1 3 0 6 1 8v3h0l1 1h1c1-1 2-1 3-1v1l-4 1s-1 1-2 1h0c-1 1-1 1-2 1l-1 1v2l1 1 1 1-1 1v3h-1v11c0 7-1 15 1 22v2h0c2 1 3 4 4 5h1l-1 1-1 1-1 1 1 1c0 3 0 6 1 9 1 1 2 2 4 2-1 1-2 1-3 1v1l-2-1v1l-1 1v-1l-1 1c-1 2 0 4-1 5l-9-6c-1 0-2-1-3-2-1-3-1-7-1-11v-16-22c0-4 0-8 1-12h-1V262v-39c0-5-1-12 0-17 1-2 2-2 3-3l1-1z" class="m"></path><path d="M547 255c0 1 1 2 2 2h0l1 2v1c-1 0-2 1-3 0v-5z" class="k"></path><path d="M595 251c2 0 6 0 9 1 1 1 3 0 4 1h-14l1-2z" class="Q"></path><path d="M544 230h5v1 2l1 1s0 1 1 2h0 0c-1 1-3 1-4 2v4c1 2 1 2 0 4 0-2-1-3-1-4h-1 1v-2h0l-1 1v-3l1-1v-1c-1 0-2-1-3-2 1-1 0-2 1-4z" class="R"></path><g class="U"><path d="M544 230h5v1 2l1 1s0 1 1 2h-2l-1-1c-2-2-3-3-4-5z"></path><path d="M551 236l1 1h-1l-1 1 1 1c0 1-1 2-1 3l1 2c-1 1 0 1-1 2 1 1 1 2 1 3s-1 2-1 2l-2 1h0c1 2 2 2 1 4v1h0c-1 0-2-1-2-2v-1c0-1 0-2 1-2v-3h-1v-3c1-2 1-2 0-4v-4c1-1 3-1 4-2h0z"></path></g><path d="M547 290h1l-1-1c1-1 1-1 2-1h3v1c-2 3-2 6-2 9-1 7-2 13-2 20v1l-1-29zm1 126c2 1 3 4 4 5h1l-1 1-1 1-1 1 1 1c0 3 0 6 1 9 1 1 2 2 4 2-1 1-2 1-3 1v1l-2-1v1l-1 1v-1l-1 1-1-23z" class="T"></path><path d="M550 371l-1-2-1 1-1-1 1-24c1 1 0 4 1 6 0 2 1 4 1 6-1 3 0 6 1 8v3h0l1 1h1c1-1 2-1 3-1v1l-4 1s-1 1-2 1z" class="b"></path><path d="M646 256c4 1 9 4 13 6-2 1-3 1-4 2l-20-7h2 1c2-1 5 0 8-1z" class="Q"></path><path d="M547 260c1 1 2 0 3 0 0 2 0 2-1 4v1h2c0 1-1 1-1 2s-1 2-1 3l1 1c0 1 0 2-1 3 0 2 1 1 1 3v1 1c0 1-1 2-2 3l1 1-1 1c1 2 1 2 1 4-1 0-1 0-2 1l1 1h-1v-1c-1-2 0-5 0-7s-1-5 0-6v-1c-1-2 0-4 0-6v-9z" class="R"></path><path d="M586 251l2 2 3 3 1 2c-1 1-4 0-6 0-7 0-16-1-22 1h-2v1h-2c-1 0-1 1-2 1h0c1-2 1-2 4-2l1-1c1-1 1-1 2-1v-1l1-1c1 0 0 0 1 1h5 1c1 0 2 0 2-1 1 1 2 1 3 1s2 0 3-1c0-1 1-2 2-3 1 0 2 1 3 1v-2z" class="e"></path><path d="M586 251l2 2 3 3c-1 0-4-1-5 0-2 0-4 0-5-1 0-1 1-2 2-3 1 0 2 1 3 1v-2z" class="M"></path><path d="M556 267h0l4-2c1 0 2 1 3 1h1c-2 2-4 1-5 3-5 6-4 13-7 19h-3c0-2 0-2-1-4l1-1-1-1c1-1 2-2 2-3v-1-1c0-2-1-1-1-3l2-2v-1l1-1 4-2-1-1h1z" class="P"></path><path d="M549 230h4 0c-1 1 0 1-1 1-1 1-1 1-1 2s1 2 2 2l1 1h1l-1 1h-1v1h2c-1 1-1 1-2 1v1 4 1l-1 1c1 1 1 1 1 2l-1 2h2c0 1-1 2-2 2 0 1 0 1 1 2v1l1 1h-1c0 1-1 2-1 3-1 0-1 0-1 1v4h1 2l-3 3 1 1c1 0 2 0 3-1l1 1-4 2-1 1v1l-2 2c1-1 1-2 1-3l-1-1c0-1 1-2 1-3s1-1 1-2h-2v-1c1-2 1-2 1-4v-1l-1-2v-1c1-2 0-2-1-4h0l2-1s1-1 1-2 0-2-1-3c1-1 0-1 1-2l-1-2c0-1 1-2 1-3l-1-1 1-1h1l-1-1h0c-1-1-1-2-1-2l-1-1v-2-1z" class="V"></path><path d="M611 249c5 1 11 1 17 2s12 3 18 5c-3 1-6 0-8 1h-1-2l-2-1-8-1c-6-1-11-2-17-2-1-1-3 0-4-1-3-1-7-1-9-1v-1h2 0c4 0 9-1 14-1z" class="Y"></path><path d="M536 375l1 1v1h2v11 50c-1 0-2-1-3-2-1-3-1-7-1-11v-16-22c0-4 0-8 1-12z" class="G"></path><path d="M537 377h2v11 15 28h-1c-1-4-1-10-1-14v-26c0-3 1-9 0-12-1-1-1-1 0-2z" class="F"></path><path d="M606 244c5 0 11 1 16 2h4l9 2h3v1c4 0 7 1 10 2l1 1c1 0 2 1 3 1l2 1h1c2 1 5 2 7 4l1-1c1-1 2-1 3-2-1 2-2 3-4 4l-6 6-1-1c1-1 2-1 4-2-4-2-9-5-13-6-6-2-12-4-18-5s-12-1-17-2c-5 0-10 1-14 1h0v-1h-3c0-1-2-3-2-4h15l-1-1z" class="M"></path><path d="M606 244c5 0 11 1 16 2h4l9 2h3v1c4 0 7 1 10 2l1 1c1 0 2 1 3 1l2 1h1c2 1 5 2 7 4l1-1c1-1 2-1 3-2-1 2-2 3-4 4l-6 6-1-1c1-1 2-1 4-2-4-2-9-5-13-6-6-2-12-4-18-5s-12-1-17-2c1-1 2-1 4 0 5 0 11 0 15 2l18 4s-1-1-2-1v-1c3 1 6 2 9 4 2 1 4 2 6 2v-1c-2-1-3-1-4-2l-4-2c-1 0-2-1-4-1l-5-2-5-1c-2-1-4-1-6-1-1-1-2-1-3-1l-8-1c-2-1-5 0-8-1-2 0-5 0-7-1l-1-1z" class="N"></path><path d="M556 230h7 18l-2 4h-1c0 1-1 3-1 4l-1 1h0-3l-1 1c0 1 0 0-1 1v2l3 1c4 2 7 3 11 5l3 3v1l-2-2v2c-1 0-2-1-3-1-1 1-2 2-2 3-1 1-2 1-3 1s-2 0-3-1c0 1-1 1-2 1h-1-5c-1-1 0-1-1-1l-1 1v1c-1 0-1 0-2 1l-1 1c-3 0-3 0-4 2h0c1 0 1-1 2-1h2v1l2 5h-1c-1 0-2-1-3-1l-4 2h0-1c-1 1-2 1-3 1l-1-1 3-3h-2-1v-4c0-1 0-1 1-1 0-1 1-2 1-3h1l-1-1v-1c-1-1-1-1-1-2 1 0 2-1 2-2h-2l1-2c0-1 0-1-1-2l1-1v-1-4-1c1 0 1 0 2-1h-2v-1h1l1-1h-1l-1-1c-1 0-2-1-2-2s0-1 1-2c1 0 0 0 1-1h0 3z" class="Z"></path><path d="M556 267c0-1 0-1 1-2h2v-1l-1-1c1-1 2-2 4-2l2 5h-1c-1 0-2-1-3-1l-4 2h0z" class="e"></path><path d="M568 250l-2-1c-1 0-3 0-4 1-1 0-1 1-2 1 0-1 2-2 2-3h-1c-1 0-1-1-2-1h-1v-1l1-1c0-1-1-2-2-3 1-1 3 0 4 0 2 0 4 1 6 1 0 1-1 1-2 2l1 1c1 2 2 3 4 3v1h-2 0z" class="I"></path><path d="M553 230h3l-1 1h0c-1 1-1 1-1 2s1 1 2 2h4l-1 1-3 1v1h5 15v1h0-3l-1 1c0 1 0 0-1 1v2c-2 0-2 0-3-1-2-1-4-1-6-2v-1c-3 0-6 0-8 1l1 1-1 2c1 1 1 1 1 2l-1 1h1v1 1h0 2v1h0c-1 0-1 1-2 2 0 1-2 3-1 4 2 0 1 0 2 1l1 1h0-1-1l-1 1c0 1 1 3 2 3-1 1-1 2-2 2v1h-2-1v-4c0-1 0-1 1-1 0-1 1-2 1-3h1l-1-1v-1c-1-1-1-1-1-2 1 0 2-1 2-2h-2l1-2c0-1 0-1-1-2l1-1v-1-4-1c1 0 1 0 2-1h-2v-1h1l1-1h-1l-1-1c-1 0-2-1-2-2s0-1 1-2c1 0 0 0 1-1h0z" class="T"></path><path d="M562 239h11l-1 1c0 1 0 0-1 1v2c-2 0-2 0-3-1-2-1-4-1-6-2v-1z" class="L"></path><path d="M566 246l-1-1c1-1 2-1 2-2l9 3c4 2 7 3 10 5v2c-1 0-2-1-3-1-1 1-2 2-2 3-1 1-2 1-3 1s-2 0-3-1c0 1-1 1-2 1h-1-5c-1-1 0-1-1-1l-1 1h-2v-1c-1 1-2 1-3 2v-1c1-1 2-3 4-3l1-1c-2 0-2 0-4 1h0v-1c1 0 3-2 4-2s1 1 3 0h0 2v-1c-2 0-3-1-4-3z" class="E"></path><path d="M566 246c2 0 3-1 4-1h0l1 1-1 2h1v1l-1 1v-1c-2 0-3-1-4-3z" class="C"></path><path d="M576 246c4 2 7 3 10 5v2c-1 0-2-1-3-1h0c-2-1-3-1-4 0-1-1-1 0-1-1l-1-1h-3l2-4z" class="B"></path><path d="M556 230h7 18l-2 4h-1c0 1-1 3-1 4l-1 1v-1h-15-5v-1l3-1 1-1h-4c-1-1-2-1-2-2s0-1 1-2h0l1-1z" class="L"></path><path d="M561 238c3-2 11-2 15-2 1 1 1 1 0 2h-15z" class="N"></path><path d="M563 230h18l-2 4h-1c-4 1-8 0-13 0-1 0-3 1-4 0 0 0-1-1-2-1-1-1 0-1 0-3h4 0zm-24-28h3c1 2 3 5 4 7-2 0-4-1-5-1-1 2-1 4-1 6v12l-1 83v35c0 4 1 10 0 14v18 1h-2v-1l-1-1h-1V262v-39c0-5-1-12 0-17 1-2 2-2 3-3l1-1z" class="C"></path><path d="M539 376h-1v-1c-1-1 0-2 0-3 0-3-1-5 0-8v7-5c0-1 1-2 0-2v-2c0-1 0-3 1-4v-1-2 2 1 18z" class="F"></path><path d="M539 202h3c1 2 3 5 4 7-2 0-4-1-5-1-1 2-1 4-1 6v12l-1 83h0v13h0c-2-2-1-7-1-9l-1-33c0-11 2-23 1-34l-1-10c0-3 0-6-1-9v-6c0-5 0-10-1-15 1-2 2-2 3-3l1-1z" class="M"></path><path d="M539 202h3c1 2 3 5 4 7-2 0-4-1-5-1-1-1 0-1-1-1l-1 1v1h-1l-1-1c0-2 1-4 1-5l1-1z" class="h"></path><path d="M586 230h118c2 0 5-1 7 0v35 17 9l-1 24c-9-15-19-27-33-38l-18-11 3-2c1-1 2-1 3-1l1 1c1 0 0 0 1-1l-1-1c1-2 2-3 4-4h0c1-2 2-3 4-4h1v1h1c1 1 1 1 1 2h1 1l1-1h1c0 1 1 1 2 1l2-1 1 1c0-1 0-1 1-2l3 3 2 3 1-1c-1-1-1-1-1-2v-1h1c1-1 2-1 4-1s5-1 7-2v-1h-1l-1-1-2-1c-1 0-1-1-2-1l-1-1h-1c-2-1-4-5-5-7h-1 0v-1h-1v-1-1c1-2 4-4 6-5v-1-1c-1 1-1 1-2 1h0c1-1 2-1 3-2h-3c-4-1-7-1-11-1h-18-56-13-9z" class="I"></path><path d="M707 244v-1-1l1 1 1 1v4l-2-1v-2-1z" class="E"></path><path d="M695 234h1l2-1v1c-1 0 0 0-1 1-2 0-4 2-4 4h0c-1 1-1 2-2 3h-1 0v-1h-1v-1-1c1-2 4-4 6-5z" class="G"></path><path d="M703 294c-1 0-3 0-4-1h-1c-1 0-1-1-2-2l1-1c0-1-1-1-1-2h-1s0-1-1-2c0-1 0-1-1-1l1-2 2 2c1 2 3 3 4 4 0 1 1 2 2 3l1 2z" class="N"></path><path d="M702 236l3 3c0 2 0 4-1 5h-1s-1 1-2 1c-2-1-3-1-3-3-1-1-1-3 0-4s2-1 4-2z" class="a"></path><path d="M698 242h1v1h1l1-1v-1l-1-2h3l1 1-1 1v3s-1 1-2 1c-2-1-3-1-3-3z" class="b"></path><path d="M708 233h1v11l-1-1-1-1v1 1c-1 1-1 1-2 1l-1-1c1-1 1-3 1-5l-3-3h-2l4-2c2 0 2 0 3 2l1-1-1-1 1-1z" class="F"></path><path d="M704 230c2 0 5-1 7 0v35 17c-2-7 0-14-1-21 0-2-1-3-1-5v-8-4-11h-1c-3-1-6 0-9-2 1-1 3 0 5 0v-1z" class="N"></path><path d="M702 252c1-1 3-1 4-2v4 1h0v2 9 29 1l-1 1-2-3-1-2c-1-1-2-2-2-3-1-1-3-2-4-4l-2-2-1-3 1 1c1-2 2-3 4-4v1 1c1 0 1 0 2-1v1c1 0 3-2 5-2v-1l-1-1v-1c0-2 0-3-1-5-1-3-2-4-4-6l1-1s0 1 1 1c0 1 0 0 1 1 1-2 0-2 1-3l-1-1c0-2 0-2 1-4-2 0-2 0-3 1-3 1-5 0-8 1v-1h1c1-1 2-1 4-1s5-1 7-2v-1h-1l-1-1z" class="L"></path><path d="M700 279c1 0 3-2 5-2v9h-1v-2c-1-1-2-1-4-1v1c1 1 0 1 1 1s1 0 2 1c0 2 0 3 1 5v2l-2-2c-1-3-4-4-6-7l-1-2-1-1c1-2 2-3 4-4v1 1c1 0 1 0 2-1v1z" class="N"></path><path d="M698 277v1 1c1 0 1 0 2-1v1c-1 2-3 3-5 3l-1-1c1-2 2-3 4-4z" class="C"></path><path d="M674 254h1v1h1c1 1 1 1 1 2h1 1l1-1h1c0 1 1 1 2 1l2-1 1 1c0-1 0-1 1-2l3 3 2 3 1-1c-1-1-1-1-1-2 3-1 5 0 8-1 1-1 1-1 3-1-1 2-1 2-1 4l1 1c-1 1 0 1-1 3-1-1-1 0-1-1-1 0-1-1-1-1l-1 1c2 2 3 3 4 6 1 2 1 3 1 5v1l1 1v1c-2 0-4 2-5 2v-1c-1 1-1 1-2 1v-1-1c-2 1-3 2-4 4l-1-1c-2-1-3-2-4-4l-1 1v1h-1-1c-1 1-4 0-5 0-1-1-2-2-3-2l-1 1-18-11 3-2c1-1 2-1 3-1l1 1c1 0 0 0 1-1l-1-1c1-2 2-3 4-4h0c1-2 2-3 4-4z" class="I"></path><path d="M697 266l2 2-1 1-1-1v1l2 3c0 1-1 2-1 3h-1v1s1 0 1 1c-2 1-3 2-4 4l-1-1c-2-1-3-2-4-4l-2-2h0c2 0 3 2 4 3 0 1 1 1 2 2l4-4-2-2h1v-1h-1c1-1 1-2 2-3h-1l-2 1v-1l3-3z" class="X"></path><path d="M674 254h1v1h1c1 1 1 1 1 2h1 1c0 1-2 3-2 4 1 1 2 2 3 2h1c1-1 2-2 3-2h0c-1 1-2 2-2 3s0 1-1 2l-3-3h-1-1l-2-2-1-1c-1 0-2-1-3-2h0c1-2 2-3 4-4z" class="C"></path><path d="M697 266c0-1 0 0 1-1v-1c1 0 1 0 1 1l1 2c1 1 1 1 3 2 1 2 1 3 1 5v1l1 1v1c-2 0-4 2-5 2v-1c-1 1-1 1-2 1v-1-1c0-1-1-1-1-1v-1h1c0-1 1-2 1-3l-2-3v-1l1 1 1-1-2-2z" class="e"></path><path d="M670 258c1 1 2 2 3 2l1 1 2 2 2 2 7 6c0 1 1 2 2 3l2 2-1 1v1h-1-1c-1 1-4 0-5 0-1-1-2-2-3-2l-1 1-18-11 3-2c1-1 2-1 3-1l1 1c1 0 0 0 1-1l-1-1c1-2 2-3 4-4z" class="D"></path><path d="M678 276c-2-1-2-1-3-2l-1-1c0-1-1-1-1-2v-1h1v-1l1-1c0 1 1 1 2 2 1 0 1 1 2 1l-1 1h-1v1 1c2-1 4-1 6-1l2-2c0 1 1 2 2 3l2 2-1 1v1h-1-1c-1 1-4 0-5 0-1-1-2-2-3-2z" class="B"></path><path d="M581 230h5 9 13 56 18c4 0 7 0 11 1h3c-1 1-2 1-3 2h0c1 0 1 0 2-1v1 1c-2 1-5 3-6 5v1 1h1v1h0 1c1 2 3 6 5 7h1l1 1c1 0 1 1 2 1l2 1 1 1h1v1c-2 1-5 2-7 2s-3 0-4 1h-1v1c0 1 0 1 1 2l-1 1-2-3-3-3c-1 1-1 1-1 2l-1-1-2 1c-1 0-2 0-2-1h-1l-1 1h-1-1c0-1 0-1-1-2h-1v-1h-1c-2 1-3 2-4 4h0c-2 1-3 2-4 4l1 1c-1 1 0 1-1 1l-1-1c-1 0-2 0-3 1l-3 2-3-1 6-6c2-1 3-2 4-4-1 1-2 1-3 2l-1 1c-2-2-5-3-7-4h-1l-2-1c-1 0-2-1-3-1l-1-1c-3-1-6-2-10-2v-1h-3l-9-2h-4c-5-1-11-2-16-2l1 1h-15c0 1 2 3 2 4h3v1h-2v1l-1 2c-2 0-4 0-6-1l-3-3c-4-2-7-3-11-5l-3-1v-2c1-1 1 0 1-1l1-1h3 0l1-1c0-1 1-3 1-4h1l2-4z" class="E"></path><path d="M573 239h3v3l-2 2-3-1v-2c1-1 1 0 1-1l1-1z" class="I"></path><path d="M671 240l5 1c1 0 2 1 3 2l1-1h2c1 0 1 1 2 0l1 1c-1 0-3 2-4 3 0 0-1 0-2 1h0-1l-1-1c1 0 1-1 2-1h1v-1l-2-1c-1-1-1-1-2-1-2 0-4-1-5-2z" class="N"></path><path d="M644 241l-1-3v-1h3 3c1 1 2 1 2 1h3c1 1 3 1 5 2v1h-1c-1-1-2-1-3-1h-2c-1-1-3-1-4-1v3 1l-2-1c0-2 0-2-1-3-1 0-2 1-2 2z" class="D"></path><defs><linearGradient id="K" x1="628.274" y1="246.688" x2="621.829" y2="230.571" xlink:href="#B"><stop offset="0" stop-color="#acb097"></stop><stop offset="1" stop-color="#d3c0a5"></stop></linearGradient></defs><path fill="url(#K)" d="M626 239h-1c-2-1-6-1-9-1h-4c0-1 0-1-1-1h-24l-1-1h42 10 4c1 1 3 1 4 1h-3v1l1 3-1 1h-4 0-3c-1-1-1-1-2-1h-2c-2-1-3 0-5-1l1-1h-1-1z"></path><path d="M693 233c1 0 1 0 2-1v1 1c-2 1-5 3-6 5v1 1h1v1h-1c-1 1-2 2-4 3 0 1-1 1-1 1l-1 1c-1-1-1-1-2-1 1-1 3-3 4-3l-1-1c-1 1-1 0-2 0h-2l-1 1c-1-1-2-2-3-2l-5-1-1-2 1-1v-1h1c-1-1-2-1-3-1l1-1 4 1c2-1 3-1 5-1 4 0 9 0 14-1z" class="d"></path><path d="M671 240l-1-2 1-1v-1h1c3 0 7 1 9 2 1 1 2 1 3 1v1c-2 0-2 0-3-1-3-1-6-1-8-2l-1 1c2 1 9 1 10 4h-2l-1 1c-1-1-2-2-3-2l-5-1z" class="i"></path><path d="M690 242h1c1 2 3 6 5 7h1l1 1c1 0 1 1 2 1l2 1 1 1h1v1c-2 1-5 2-7 2s-3 0-4 1h-1v1c0 1 0 1 1 2l-1 1-2-3-3-3c-1 1-1 1-1 2l-1-1-2 1c-1 0-2 0-2-1h-1l-1 1h-1c0-1 1-1 1-1l1-1c2-1 1-1 2-4h0c-1-1-1 0-2-1h-1-1l2-2c1 1 1 1 3 0 1 1 2 1 3 2l1-1 1 1c1 0 2 1 3 1 2 0 2 1 3 0l-3-3c-1-1-2-1-2-2-1-2 0 0 1-2v-2z" class="d"></path><path d="M657 243l1-2 2 1 1 1 1 1h0l2 2h0c1 0 2 0 3 1h1c1 0 3 1 4 1 2-1 2-2 4 0l-1 1c-1 0-2 1-3 2h2l-1 2h0 0l1 1c-2 1-3 2-4 4h0c-2 1-3 2-4 4l1 1c-1 1 0 1-1 1l-1-1c-1 0-2 0-3 1l-3 2-3-1 6-6c2-1 3-2 4-4h0c-1-2-6-5-8-6l-2-1h0l1-1s0-1 1-1l1-1v-1l-2-1z" class="G"></path><path d="M664 246c1 0 1 1 2 2h1v1c-1 1-2 1-2 0-2 0-5-1-6-2l1-3h2l2 2h0z" class="D"></path><path d="M581 230h5 9 13 56 18c4 0 7 0 11 1h3c-1 1-2 1-3 2h0c-5 1-10 1-14 1-2 0-3 0-5 1l-4-1h-1-7-11c-2 0-7-1-9 0l1 1h-9-38c-4 0-9-2-13 0-2 0-1-1-2-2l-1 1h-1l2-4z" class="W"></path><path d="M583 240c-1 0-2 0-3-1h32 3 2c2 1 5-1 7 1l2-1h1 1l-1 1c2 1 3 0 5 1h2c1 0 1 0 2 1h3 0l3 1c1 0 2 1 2 1l3 1c1 0 2 0 3 1v-1h0l1-1c0-2-1-3 0-4l4 2v-1l2 2h0l2 1v1l-1 1c-1 0-1 1-1 1l-1 1h0l2 1c2 1 7 4 8 6h0c-1 1-2 1-3 2l-1 1c-2-2-5-3-7-4h-1l-2-1c-1 0-2-1-3-1l-1-1c-3-1-6-2-10-2v-1h-3l-9-2h-4c-5-1-11-2-16-2h-4c-2-1-5 0-8-1h-6c0-1-1-1-2-1-1-1-2-1-3-2z" class="D"></path><path d="M583 240h9v1l-4 1v1c0-1-1-1-2-1-1-1-2-1-3-2z" class="B"></path><path d="M612 239h3l-1 1h-2c-2 0-2 0-3 1 0 0 2 0 3 1l-1 1c-2 0-3 0-5-1h0l1-1-1-1h-1l-1 2c-1 1-1 1-2 1v-1c-1 0-1 0-2-1h1 2v-1h-2l-2 1v-1h-1c1-1 2-1 3-1h10 1z" class="Q"></path><path d="M565 468v-2h7l8 1 10 1 10 1 4 2c3 1 7 3 10 4 4 0 8 2 11 3l5 1 2 1 2 2h1 5c2-1 4-1 6-1v2l-2 1v1c-2 1-2 1-3 2l2 2 1 2-1 1h1c0 1 1 1 1 2 0 2 0 4 2 5 0 1 1 1 1 1l-2 2 1 1c1 0 1 0 2-1s1-1 2-1l1 1h-1v2l3-4v5l-10 10c-2 1-4 2-5 3-2 2-3 4-5 6-4 2-7 4-10 6-1 1-2 1-3 1h0l-4 5h-1c-1 0-2 1-3 1 0 1-1 1-2 2l-1 1c-2 1-3 2-5 2-2 1-3 2-5 2l1 1-1 1h0l1 2c2 3 3 3 3 7h0v1l-1 1-2 1-2-3-3 1-6-4c-2 0-3 0-5-1-2 1-2 1-3 2l2 1 1 1-2 2-1 1 1 2h-1-2l-4 1 2 1 1 1c-1 1-1 1-3 1l-1 1h-1v1c-2 0-2 0-3-1h0c-2 0-2 0-3 1s-2 1-2 2c-1 1-3 2-3 3l-1 1h-1c-1 1-1 2-2 3 0 1-1 2-2 3l-1 1c-1 1-2 1-4 2 1 1 0 1 0 2l1 1c0 1 0 2-1 3 0 1 0 3 1 5v6 37c0 5-1 10 0 16 0 1 1 2-1 4l-1-1h-1c0 1 1 2 1 3h2v1c-1 1-1 2-1 4l1 50 2 38c1 5 3 12 2 16h2c0 1 0 1 1 2 0 3 2 10 4 11h1l-1 1 1 2 4 7c2 3 5 4 7 6 1 1 1 2 3 2l5-3c-2 2-4 3-5 4v1l1 2-1 1c-1 0-1 0-1 1h0c-2 0-2 0-4-1l-1 1-3-1c-2 0-3 0-4 1l1 1h-1c-1 0-1 0-2 1h-3l-2 3 1 1c1 0 3 1 4 2h-2c-2 0-5 0-7-1-1 1-1 1-3 0h0c-2 1-1 1-2 1 0 1-6 0-8 1-2 0-2 1-4 2v1l-1 2v2c-2 0-4-1-5-1v2h-1c-2-1-3-2-3-3l-1 1c0 1 1 2 2 3l-1 1-1-1v3c-2 1-3 1-4 2v2 1l-1 2 1 1-1 1c1 1 2 1 3 2h-2v1h-1c-1 1-1 1-1 2 2 1 3 1 3 2-1 1-1 2-3 2v1 2 1c-1-2-1-3-1-5s1-5 0-7v2l-1-4 2-24v-4h-1v-9-4c1-2 1-5 1-7-1 1-2 2-1 3 0 2 0 2-1 4v7 1l-2-2-1-3h-1l-1 1v-2h-1v-8-3-2c0-3 0-5-1-7v4 1l-1-1h-3v-3h-1c-1-1-2-1-3-2l-2-2 2-2h-2c-1-1-1-1-2-1-1-4 0-9 0-13l-1-27c-1-1 0-4 0-6h-1v31c0 4 1 10 0 14h0c1 2 1 2 0 3l1 10v2c1 3 1 8 1 11 0 5 1 9 1 13v2h-2c-8 0-17-2-25-4-4-1-7-1-10-2v-1c-1 1-2 1-3 1h0l-2-1h-3-3c-2-1-3 0-6-1l-2-1 2-2h2c1 0 3-1 4-1l2-2h-1l1-3-1-1c1-2 4-7 6-9h2c3 0 10 1 12-1h-1c-1-1-2-1-3-1-1-1-2-1-3-1s-2 0-3-1c1-4 3-7 4-11h0c1-1 1-3 1-4v-8-4c4 1 11 1 15 1-2-2-12-1-15-2 0-8-2-16-1-24 1 0 2-1 3-1-1-1-2-2-2-4h0c1-2 3-3 3-5v-1c-1-2-1-4-1-6v-11-3-4c0-2-1-6 0-8v-2h0c-1-2 0-5 0-7-1-1-1-2 0-3v-1c0-2 0-4 1-5h2 1c0-1 1-2 1-4h1c0-1 0-1 1-2h0v-3h0c-1-1-2-1-3-2l-1-5 1-5c0-1 1-1 1-2l2 1h1v-2-10-5l2-7v-3-1l1-1v-7-2-1-1-1-2h-3l1-1v-1c-1-1-1-2-1-2 3-1 4-1 6 0v-12-21-9l2-1-1-1 1-1c-1-3-1-5-1-7l1-1c0 1 0 3 1 4 3 3 15 3 19 3 1-2 0-3 1-4h2c1 1 1 1 2 1v2c2 1 4 1 6 1l7-1v-2l1-1 1-1v-2c1-2 3-3 3-5-1-2-1-1-3-2l4-1 2-1 5-2c2 0 3-1 5-2l2-1c4-3 8-7 11-10 3-5 6-9 9-14l1 1c1-4 2-9 3-13 0-1 1-3 1-4v-2h0c1-1 0-3-1-4l-1-2-1 1v3l-1-6v-1l-2-6 1-1v-1z" class="m"></path><path d="M447 796h2l-2 3-2-1 2-2zm8-3h5l-1 2h-6l2-2zm14-40c2 2 2 2 2 5l-2 3v-8z" class="q"></path><path d="M565 517c0-1 1-1 1-1 3 0 7 0 9-1v1c1 1 4 0 5 1h-15z" class="Z"></path><path d="M522 794h11c1 0 1 0 2-1h7l1 1h-1-3c0 1-17 1-17 0h0z" class="l"></path><path d="M534 654h2c1 0 2 0 3 1l-1 3c-1 1-2 0-3 0-1-2-1-2-1-4z" class="V"></path><path d="M574 530h4c1 1 1 1 1 2l-2 3h-1v-1-1c-2 1-2 1-2 2l1 3h0c-2-1-3-2-4-4l1-2 2 1 1-1h1l1 2 1-2h0c-1-1-2-1-3-2h-1z" class="n"></path><path d="M492 759l1 2v2l2 2c1 1 2 1 3 1h0c-4 1-8 1-12 1v-1c2 0 2 0 3-1h2c1-2 1-4 1-6z" class="G"></path><path d="M538 638c0 2 0 3 1 5v1h0v7h-3 0l-1-1h-1 2l1-1v-7c0-2 1-3 1-4z" class="l"></path><path d="M492 718h-1c-1-1-2-1-3-1h-1v-1c-1-1 0-6 1-8l1 1c1 2 1 3 1 5 1 0 2 1 3 1h-1v3z" class="e"></path><path d="M539 595c-1 1-1 2-1 3h-1l1 1c-1 0-2 1-3 1h-1v-9-13h0l1 17 1 1c1 0 2-1 3-1z" class="k"></path><path d="M455 790l14 1v1c-3 1-7 0-9 1h-5-1l1-3z" class="P"></path><path d="M561 545l1-1h1c1 1 3 1 4 1 2 0 4-1 5 0 1 2 3 1 4 2l-1 1c-2 0-3 0-5 1-3-1-6-3-9-4z" class="c"></path><path d="M471 672c-1-1-1-2 0-3v9l7-1h1c-1 1-1 3-2 3l-6 1v8c0-2-1-6 0-8v-2h0c-1-2 0-5 0-7z" class="K"></path><path d="M547 630h1v16c0 3 1 5 1 8 1 5 0 12 0 18-1-2-1-6-1-8l-1-13v-5c1-4 1-9 1-13-1-1-1-2-1-3z" class="k"></path><path d="M564 800v-2l2 2h1l3 2c-2 0-3 0-4 1l1 1h-1c-1 0-1 0-2 1h-3c-2 0-3 0-5 1v1c-2-1-2-1-4-3 4 0 9-1 13-3l-1-1z" class="c"></path><path d="M555 750c1 5 3 12 2 16-1 1-1 1-2 0 0-2-1-3-1-5s0-4-1-6v-2l1-1c-1 0-2 0-3-1 1-1 3-1 4-1z" class="J"></path><path d="M521 784l1-14v-1 25h0l-1 10h-1v-9-4c1-2 1-5 1-7z" class="K"></path><path d="M539 617c2 7 3 19 0 26-1-2-1-3-1-5v-4c1-2 1-8 1-10v-3-1c-1-1 0-2 0-3z" class="C"></path><path d="M533 705v-1c0-1 0-2 1-3 2 0 4 1 6 1 1 2 4 2 5 2h1l1 2c-3 1-11 0-14-1z" class="c"></path><path d="M500 712c1-1 1-2 2-3 1 0 2 1 3 2l1 4c-1 1-1 1 0 2v4 1l-2-1c-1-2-2-3-3-4l-1-1v-1-3z" class="J"></path><path d="M595 537h2c1-1 1-1 2-1h3v-1h2c1 0 2 0 3-1v-1l11-5-2 3c-3 2-7 4-10 5-1 0-2 1-3 1-1 1-2 1-4 1l-3 1-1-2z" class="k"></path><path d="M539 698l-1-2c0-2 0-6 1-7l7 15h-1c-1 0-4 0-5-2 0-1 0-3-1-4z" class="P"></path><path d="M536 542v-1c1-2 1-3 3-4h1v17h0l-1-1-1 1h-1l-2-1c1-1 3-1 4-2l-3-3v-6z" class="X"></path><path d="M469 791c1-2 0-4 1-4l2 1c0-1 0-1 1-2v1c0 1 0 2-1 4l-1 1-1 2-1 1c-1 1-1 2-2 3l-1-1h0c1-1 1-2 2-3-1 0-2 0-3 1l-2 1c-1 0-3 1-4 1l-1 1v-1l2-1-1-1 1-2c2-1 6 0 9-1v-1z" class="n"></path><path d="M603 511c3-1 5-2 7-2l-2 2c-7 3-14 5-22 5h-1l-1-1h0c3-2 6-2 9-3l1 1c3-1 6-1 9-2z" class="I"></path><path d="M489 709h2l3 1c1 0 2 0 2 1l1 2h1l1-1v1l1-1v3 1l-1 11c-1-1 0-4 0-6h-1v-5h-2c-1 0-2 0-4-1h1c-1 0-2-1-3-1 0-2 0-3-1-5z" class="R"></path><path d="M489 709h2v1c1 1 1 2 1 3 2 1 3 2 4 3-1 0-2 0-4-1h1c-1 0-2-1-3-1 0-2 0-3-1-5z" class="U"></path><path d="M537 554h1l1-1 1 1h0v27c-1-3-2-7-2-10v-1c0-1 0-2-1-3v5-18z" class="p"></path><path d="M499 781h-6c-3-2-10 0-13-1-1 0-1 0-2-1 0-1 1-2 1-3l1-2v-1l1-1v-1l2-2h0c1 1 2 3 1 5v4l3 1c3 1 8 0 12 0v2z" class="l"></path><path d="M516 722c0 2 1 2 1 3 0 2 0 2 1 3 1 2 0 6 0 9v10c-1 3-2 7-2 11v-36z" class="b"></path><path d="M537 572v-5c1 1 1 2 1 3v1c0 3 1 7 2 10 0 3 1 9 0 12l-1-1h0c-1 0-2 0-3-1l1-19z" class="I"></path><path d="M634 482h1 5c2-1 4-1 6-1v2l-2 1v1c-2 1-2 1-3 2l2 2 1 2-1 1h1c0 1 1 1 1 2 0 2 0 4 2 5 0 1 1 1 1 1l-2 2h-1l1-1c0-2-1-2-2-3v-1l-1 3c-1-1-1-1 0-2v-3h-1v-3l-1-1v-3c-1-1-1-2-1-4h0-2c-1 0 0 1-1 2 1 0 1 2 2 3 0 2 0 3-1 4v-1-3c-1-2-2-4-4-6v-1z" class="q"></path><path d="M525 762l1-2 1-1v-1l1 1c1-3 1-6 2-8 1 5 1 10 3 15v2c-1 0-3 0-4-1s0 0-2-1c-1 0-1 0-2-1v-3z" class="T"></path><path d="M533 541l1-1c0-1 0-1 1-2h1v4 6l3 3c-1 1-3 1-4 2 1 1 1 2 0 4v8c-1-2-1-4-1-6v-2-2c0-3 1-5 1-7-3 2-6 5-10 5h0l-2-2c2 0 4 0 5-1h1c3-2 4-5 4-9z" class="R"></path><path d="M481 692v1 4h1c1-1 1-1 2-1l-1 1h0v4c1 1 1 1 1 2h-1-3l1 3h-9c4-3 6-9 9-14z" class="W"></path><path d="M483 697v4l-3 1-1-1v-1c1-1 2-2 4-3z" class="R"></path><path d="M483 701c1 1 1 1 1 2h-1-3v1h-2l-1-1 1-1h2 0l3-1z" class="B"></path><path d="M486 767h-4c-3-2-10 1-13-2 1-1 7 0 9 0 0-1 1-2 1-2-1-1-1-1-2-1h-1l-1 1-1-1 1-1h2v-1c2 1 3 1 5 0 0-2 2-4 3-6s1-2 2-3l1 1h-1c0 2-1 4-1 6v2c-1 1-2 3-3 3h-1v2c1 1 2 1 4 1h0v1z" class="R"></path><path d="M547 581l1 1 1 15c0 3-1 5-1 8v17c0 2 1 5 0 7h-2c0-2 0-3 1-4v-1c-1-2-1-3-1-5 0-5 0-10 1-14v-11c-1-4 0-9 0-13z" class="l"></path><path d="M533 686v1c1 3 0 8 2 11l2 1c1 0 1-1 2-1 1 1 1 3 1 4-2 0-4-1-6-1-1 1-1 2-1 3v1c1 3 0 8 0 11l-4 1c-2 0-4-1-5-2v-1c2 0 3-1 5-1h0c0-1 0-2 1-2 0-2 1-4 2-5s0-3 0-5c1-1 1-1 2-1h1 1c1 1 2 1 3 1v-2l-1 1c-1 0-1 0-2-1-2 0-3 0-3-1v-1c-1-3 0-8 0-11z" class="R"></path><defs><linearGradient id="L" x1="514.469" y1="714.606" x2="522.008" y2="723.665" xlink:href="#B"><stop offset="0" stop-color="#1f1e1b"></stop><stop offset="1" stop-color="#3e3d33"></stop></linearGradient></defs><path fill="url(#L)" d="M516 722c0-2 1-5 0-8v-3c2 0 6 2 6 3l-1 6-1 1-1 6c0 2 1 4 1 6h0-1c0 2 0 3-1 4 0-3 1-7 0-9-1-1-1-1-1-3 0-1-1-1-1-3z"></path><path d="M535 553l2 1v18l-1 19c1 1 2 1 3 1h0l-1 1 1 2c-1 0-2 1-3 1l-1-1-1-17 1-13v-8c1-2 1-3 0-4z" class="L"></path><path d="M578 530c2 0 3 1 5 2v1 4c0 1-2 3-2 5-3-1-6-1-9 0 1 1 2 1 2 2-1 0-2-1-3-1 0-1-1-1-1-2l-1-1c-1 0-1-1-2-1l2-1-4-3c3 0 5 2 7 3 1 1 2 1 4 2h0c1 0 2 0 3 1h1c1-1 1-1 1-2s0 0 1-1v-1h-1s-1 1-1 2c-2 0-3 0-4-1h-1l-1-3c0-1 0-1 2-2v1 1h1l2-3c0-1 0-1-1-2z" class="r"></path><path d="M521 767c1 1 1 2 1 2v1l-1 14c-1 1-2 2-1 3 0 2 0 2-1 4v7 1l-2-2v-2c0-4 0-6-1-9 0-3 1-7 2-10v-4c1-1 2-3 3-4v-1zm53-237h0l-1-1c1-1 2-1 3-1 1 1 2 1 3 1s1 0 1 1h1c-1-2-2-2-3-2-2-1-4-1-6-2-2 0-3 0-5-1h0 10v-1h-3v-1c4-1 5-3 9-2l1 1v1h0c0 2 0 3 1 5 0 1 0 0-1 1s-1 3-1 4v-1c-2-1-3-2-5-2h-4z" class="R"></path><path d="M583 521l1 1v1h0c0 2 0 3 1 5 0 1 0 0-1 1s-1 3-1 4v-1c-1-2-1-3-2-5-2-1-3-2-4-3h2l2 2c1-1 1-1 1-2v-2l1-1z" class="J"></path><path d="M478 654c1 1 0 1 2 1v-2h4c2 1 3 1 5 1v9l-6 1v3 2c0-2-1-4 0-6h4v-1h-4c-1-1-2 0-3-1-1 0-3-1-4-2h1c0-1 0-1 1-2h0v-3z" class="V"></path><defs><linearGradient id="M" x1="477.859" y1="679.967" x2="487.768" y2="684.842" xlink:href="#B"><stop offset="0" stop-color="#aba697"></stop><stop offset="1" stop-color="#cdc9bb"></stop></linearGradient></defs><path fill="url(#M)" d="M483 667h1l2 1v1h0c2 4 1 18 0 23-1 1-1 2-2 3v1c-1 0-1 0-2 1h-1v-4-1-1c3-6 3-15 2-22v-2z"></path><path d="M545 527l1 2c-1 2-2 3-3 4h0c-2 1-3 2-3 4h0-1c-2 1-2 2-3 4v1-4h-1c-1 1-1 1-1 2l-1 1c-2 1-3 3-4 4s-3 2-4 2v-2l1-1 1-1v-2c1-2 3-3 3-5-1-2-1-1-3-2l4-1 2-1 5-2c2 0 3-1 5-2l2-1z" class="q"></path><path d="M526 544h2l1-2c1-2 4-6 7-7l1 1v-1c2-1 3-2 6-2-2 1-3 2-3 4h0-1c-2 1-2 2-3 4v1-4h-1c-1 1-1 1-1 2l-1 1c-2 1-3 3-4 4s-3 2-4 2v-2l1-1z" class="b"></path><path d="M567 558c1-1 1-1 2-1h1c0 2-3 2-2 5v1c1 1 2 1 3 2h0c-2 0-2 0-3 1s-2 1-2 2c-1 1-3 2-3 3l-1 1h-1c-1 1-1 2-2 3 0 1-1 2-2 3l-1 1c-1 1-2 1-4 2v-2h1v-2l2-1-2-1v-2l3-3c1-2 3-3 4-4 0-1-1-2-1-2l-1-1c1-1 2-2 3-4 0-1-1 0 0-1v1c0 2 0 2-1 3v1c1 1 0 1 1 1v1h1l1-1 1 1c0-1 0-1-1-1l1-1-1-2 1-1c0-2 0-2-1-2h1 0 2 1z" class="R"></path><path d="M560 570l1 2c-1 1-1 2-2 3 0 1-1 2-2 3 0-1-1-2-1-3 1-1 3-1 3-3 0-1 1-1 1-2z" class="b"></path><path d="M567 558c1-1 1-1 2-1h1c0 2-3 2-2 5v1c1 1 2 1 3 2h0c-2 0-2 0-3 1s-2 1-2 2c-1 1-3 2-3 3l-1 1h-1l-1-2 1-2 2 1 1-3c0-1 0-2 1-3h0c-1-1-1-2-1-3h0c0-2 0-2-1-2h1 0 2 1z" class="J"></path><path d="M537 666h1 0c-1 8-1 15 1 23-1 1-1 5-1 7l1 2c-1 0-1 1-2 1l-2-1c-2-3-1-8-2-11v-1-14c0-2 0-4 1-5s2-1 3-1z" class="K"></path><path d="M533 686v-14c0-2 0-4 1-5s2-1 3-1l-3 3v5 12l2 8v2l1 2v1l-2-1c-2-3-1-8-2-11v-1z" class="J"></path><path d="M646 502l1 1c1 0 1 0 2-1s1-1 2-1l1 1h-1v2c-3 4-6 7-10 10-3 3-6 4-8 7 0 1-1 1-2 1l-3 3-2 2c-3 2-7 3-10 4l2-3c2-1 4-2 5-3s2-2 4-3l-1 1h1c1 0 1-1 2-2v-1c1 0 1 0 2-1 0 0 1-1 2-1v-1c1-1 1-1 1-2-1 0-4 1-5 3h-1v-1c2-2 5-2 7-4h1c2-1 5-4 7-6v-1-1c1-1 1-2 2-3h1z" class="R"></path><path d="M518 737c1-1 1-2 1-4h1 0c0-2-1-4-1-6l1-6 1-1 1 27c0 6 1 14-1 20v1l-3 1h-2c-1-3 0-8 0-11 0-4 1-8 2-11v-10z" class="c"></path><path d="M516 758c0-4 1-8 2-11 0 2 2 8 1 10 0 1-1 1-1 2 1 2 1 3 0 5v2c-1 0-1 1-1 2l1 1h-2c-1-3 0-8 0-11z" class="K"></path><path d="M483 667v-3l6-1c1 2 1 3 0 4 0 2 0 4 1 5 1 5 0 13 0 18-1 5-1 11-1 16h-2-6l-1-3h3 1c0-1 0-1-1-2v-4h0l1-1v-1c1-1 1-2 2-3 1-5 2-19 0-23h0v-1l-2-1h-1z" class="e"></path><path d="M484 667v-2c2-1 3 0 5 0v3h-3l-2-1z" class="R"></path><path d="M484 696v-1c1-1 1-2 2-3h0c0 1-1 6 0 7v1h1v6h-6l-1-3h3 1c0-1 0-1-1-2v-4h0l1-1z" class="D"></path><path d="M530 602h7v6l2 9c0 1-1 2 0 3v1 3c0 2 0 8-1 10v4c0 1-1 2-1 4v7l-1 1-2-2c-1-2-1-41 0-42l2-2c-1 0-2 0-2-1-1-1-3 0-4 0v-1z" class="T"></path><path d="M537 642c-1-2-1-5-1-8v-17-8c0-1 0 0 1-1h0l2 9c0 1-1 2 0 3v1 3c0 2 0 8-1 10v4c0 1-1 2-1 4z" class="G"></path><path d="M537 807l15-3c2 2 2 2 4 3v-1c2-1 3-1 5-1l-2 3 1 1c1 0 3 1 4 2h-2c-2 0-5 0-7-1-1 1-1 1-3 0h0c-2 1-1 1-2 1 0 1-6 0-8 1-2 0-2 1-4 2v1l-1 2c0-1 0 0-1-1h0l1-1v-1h-5-2c1-2 4 0 5-2h-6-2c-1 0-1 0-1-1 1-1 1-2 3-3h1l-1-1c3 0 5-1 8 0z" class="K"></path><path d="M529 807c3 0 5-1 8 0h1c3 1 8-1 12 0h-1-1-3v1h-6l-4 1h-5v1h1v1h-1l-1 1h-2c-1 0-1 0-1-1 1-1 1-2 3-3h1l-1-1z" class="r"></path><path d="M535 809c2 1 5 0 8 0h7 2 2c1-1 3 0 4 0h2c1 0 3 1 4 2h-2c-2 0-5 0-7-1-1 1-1 1-3 0h0c-2 1-1 1-2 1 0 1-6 0-8 1-2 0-2 1-4 2v1l-1 2c0-1 0 0-1-1h0l1-1v-1h-5-2c1-2 4 0 5-2h-6l1-1h1v-1h-1v-1h5z" class="a"></path><path d="M651 504l3-4v5l-10 10c-2 1-4 2-5 3-2 2-3 4-5 6-4 2-7 4-10 6-1 1-2 1-3 1h0l-4 5h-1c-1 0-2 1-3 1 0 1-1 1-2 2l-1 1c-2 1-3 2-5 2-2 1-3 2-5 2-2-2-3-3-4-5l3-1c2 0 3 0 4-1 1 0 2-1 3-1 3-1 7-3 10-5 3-1 7-2 10-4l2-2 3-3c1 0 2 0 2-1 2-3 5-4 8-7 4-3 7-6 10-10z" class="L"></path><path d="M605 542l-2-2c1-1 1-1 2-1l1-1 1-1h2l4-2c0-1 1-1 2-1s1-1 3-2c1 0 2 0 3-1l-4 5h-1c-1 0-2 1-3 1 0 1-1 1-2 2l-1 1c-2 1-3 2-5 2z" class="e"></path><defs><linearGradient id="N" x1="568.143" y1="547.68" x2="574.947" y2="557.17" xlink:href="#B"><stop offset="0" stop-color="#27251d"></stop><stop offset="1" stop-color="#484438"></stop></linearGradient></defs><path fill="url(#N)" d="M561 545c3 1 6 3 9 4 4 1 7 2 11 3v1h1l2 1 1 1-2 2-1 1 1 2h-1-2l-4 1 2 1 1 1c-1 1-1 1-3 1l-1 1h-1v1c-2 0-2 0-3-1s-2-1-3-2v-1c-1-3 2-3 2-5h-1c-1 0-1 0-2 1v-4c0-1 0-3-1-3 0-2-1-1-1-3l-1-1v2c0-1-1-2-1-2l-1-2h-1z"></path><path d="M572 561c0-2 0-2 1-3h2v3h-3z" class="c"></path><path d="M581 553h1l2 1c-1 1-1 2-2 2h-1c-2 0-3 1-4 2h-2l-1-1v-1l1-1 2 1h1v-1c1-2 2-2 3-2z" class="V"></path><path d="M584 554l1 1-2 2-1 1 1 2h-1-2l-4 1h-1v-3h2c1-1 2-2 4-2h1c1 0 1-1 2-2z" class="T"></path><path d="M568 562c0-1 0-1 2-2l2 1h0 3 1l2 1 1 1c-1 1-1 1-3 1l-1 1h-1v1c-2 0-2 0-3-1s-2-1-3-2v-1z" class="V"></path><path d="M575 561h1l2 1 1 1c-1 1-1 1-3 1l1-1h-1-1l-1 1-1-1h-1v-2h0 3z" class="g"></path><defs><linearGradient id="O" x1="516.99" y1="731.402" x2="536.768" y2="744.448" xlink:href="#B"><stop offset="0" stop-color="#2d2928"></stop><stop offset="1" stop-color="#5f5e4e"></stop></linearGradient></defs><path fill="url(#O)" d="M525 762v-8l-1-27c0-2 0-6 1-8 0-1 0-1 1-1h3c1 2 1 29 1 33-1 2-1 5-2 8l-1-1v1l-1 1-1 2z"></path><path d="M524 807h5l1 1h-1c-2 1-2 2-3 3 0 1 0 1 1 1h2 6c-1 2-4 0-5 2h2 5v1l-1 1h0c1 1 1 0 1 1v2c-2 0-4-1-5-1v2h-1c-2-1-3-2-3-3l-1 1c0 1 1 2 2 3l-1 1-1-1v3c-2 1-3 1-4 2v2 1l-1 2 1 1-1 1c1 1 2 1 3 2h-2v1h-1c-1 1-1 1-1 2 2 1 3 1 3 2-1 1-1 2-3 2v1 2 1c-1-2-1-3-1-5s1-5 0-7v2l-1-4 2-24c0 2-1 5 0 7 0 0 0 1 1 1 0 1-1 4 0 5v-12l1-2h1z" class="n"></path><path d="M524 807h5l1 1h-1c-2 1-2 2-3 3 0 1 0 1 1 1l-1 2v-1h-1v-1c-1-2-1-2-1-5z" class="R"></path><path d="M492 715c2 1 3 1 4 1h2v5 31c0 4 1 10 0 14-1 0-2 0-3-1l-2-2v-2l-1-2c0 2 0 4-1 6h-2l2-14c2-10 1-22 1-32v-1-3z" class="F"></path><path d="M492 715c2 1 3 1 4 1h2v5 31c0 4 1 10 0 14-1 0-2 0-3-1l-2-2v-2l-1-2v-2h2c1 0 1-1 2-2v-2-11c0-6 1-11-1-17 0-1 1-1 1-2 0-3-1-4-3-5l-1 1v-1-3z" class="E"></path><path d="M493 761l1-2 1-1 1 1v5l-1 1-2-2v-2z" class="F"></path><path d="M533 766l17-1v-3h0v-4c-1-2-1-3-1-5l1-1v3h1c0 4 0 7 1 10v2c0 3 1 7 2 10l1 2c0 2 1 4 2 6h1 0v-1l1-1c-1 0-1 0-2-1 1 0 1-1 2-1h3l2-1 1 2 4 7c2 3 5 4 7 6 1 1 1 2 3 2l5-3c-2 2-4 3-5 4v1l1 2-1 1c-1 0-1 0-1 1h0c-2 0-2 0-4-1l-1 1-3-1-3-2h-1l-2-2v2h-1c0-2 0-3-1-4v-1-1l-2-2c0-1 0-1-1-2l-1-2-2-2-1-2c-1-2-1-4-2-6v-2l-1-2-1-8c-4 0-16 0-18 2v-2z" class="q"></path><path d="M564 798v-2h0l1-1 2 2h1c0 2 0 2-1 3h-1l-2-2z" class="m"></path><path d="M568 797v-1c2 1 3 2 4 3l-1 1c1 1 2 1 3 2l-1 1-3-1-3-2c1-1 1-1 1-3z" class="J"></path><path d="M569 789c2 3 5 4 7 6 1 1 1 2 3 2l5-3c-2 2-4 3-5 4v1l1 2-1 1c-1 0-1 0-1 1h0c-2 0-2 0-4-1-1-1-2-1-3-2l1-1c0-1 1 1 1 1h2v-1c0-1 0-1-1-2l-4-2-3-3c-1-1-6-2-7-2h2 7v-1z" class="b"></path><path d="M584 522c2 1 3 2 4 4h0c1 2 2 3 3 5l4 6 1 2c1 2 2 3 4 5l1 1-1 1h0l1 2c2 3 3 3 3 7h0v1l-1 1-2 1-2-3-3 1-6-4c-2 0-3 0-5-1-2 1-2 1-3 2h-1v-1c-4-1-7-2-11-3 2-1 3-1 5-1l1-1c-1-1-3 0-4-2h3l-1-1c0-1-1-1-2-2 3-1 6-1 9 0 0-2 2-4 2-5v-4c0-1 0-3 1-4s1 0 1-1c-1-2-1-3-1-5h0v-1z" class="S"></path><path d="M591 531l4 6 1 2c1 2 2 3 4 5l1 1-1 1h0l1 2c2 3 3 3 3 7h0v1l-1 1c-1-2-3-2-5-3v-1c0-1 0 0-1-1 0-1-1-2-1-3h0l-2-1h1c0-2-1-3-2-4v-1-1c1 2 3 4 5 5h0c-1-1-1-2-1-3l-1-1c0-3-3-8-5-11v-1z" class="L"></path><path d="M598 554l2-1c0-1-1-1 0-2v1h1l3 3v1l-1 1c-1-2-3-2-5-3z" class="j"></path><path d="M591 531l4 6 1 2c1 2 2 3 4 5l1 1-1 1h0l1 2c2 3 3 3 3 7h0l-3-3c0-2 0-3-1-4l-1-1v-1c0-1-1-2-3-3 0-3-3-8-5-11v-1z" class="X"></path><path d="M580 547l-1-2 1-1c0 2 3 2 4 2 3 0 5 2 6 3 2 2 4 3 6 5-1-2-2-5-3-6h1l2 1h0c0 1 1 2 1 3 1 1 1 0 1 1v1c2 1 4 1 5 3l-2 1-2-3-3 1-6-4c-2 0-3 0-5-1-2 1-2 1-3 2h-1v-1c-4-1-7-2-11-3 2-1 3-1 5-1l1-1h4z" class="e"></path><path d="M599 555c-1 0-2-1-3-1v-2l2 1v1c2 1 4 1 5 3l-2 1-2-3z" class="P"></path><path d="M580 547l-1-2 1-1c0 2 3 2 4 2 2 2 6 3 7 5-1 0-2 0-3-1l-2-1c-1-1-1-1-2-1h-1l-3-1z" class="c"></path><path d="M576 547h4l3 1h1c1 0 1 0 2 1-1 0-1 1-3 1v-1l-2 2v1c-4-1-7-2-11-3 2-1 3-1 5-1l1-1z" class="P"></path><path d="M584 522c2 1 3 2 4 4h0c0 2 3 5 2 7 0 2 1 2 1 4-2 0-2 1-3 2l2-1v1 1c-1 1-1 2-1 3h0c-1 1-1 2-1 3 1 2 2 1 2 3-1-1-3-3-6-3-1 0-4 0-4-2l-1 1 1 2h-4c-1-1-3 0-4-2h3l-1-1c0-1-1-1-2-2 3-1 6-1 9 0 0-2 2-4 2-5v-4c0-1 0-3 1-4s1 0 1-1c-1-2-1-3-1-5h0v-1z" class="g"></path><path d="M586 534h-1v-3l2-1c1 1 1 2 2 3v1l-1-1-2 1z" class="c"></path><path d="M584 523c1 1 2 2 2 3 1 1 0 2 1 4l-2 1v3h1l1 3v1h-1c-1 1-1 2-2 3v3h-1 0-3l-1 1 1 2h-4c-1-1-3 0-4-2h3l-1-1c0-1-1-1-2-2 3-1 6-1 9 0 0-2 2-4 2-5v-4c0-1 0-3 1-4s1 0 1-1c-1-2-1-3-1-5h0z" class="K"></path><defs><linearGradient id="P" x1="518.793" y1="577.944" x2="534.415" y2="581.954" xlink:href="#B"><stop offset="0" stop-color="#595546"></stop><stop offset="1" stop-color="#877f6c"></stop></linearGradient></defs><path fill="url(#P)" d="M528 554l4-2c0 4-1 43-1 44l-1 1 1 1-1 1v3 1 8h-5c-2 2-1 2-1 4-1 0-2 1-2 2v-3h0v-4-5c1-3 0-6 0-8s1-3 1-5c-1-1 0-2 0-4l-3 1c1-1 2-2 2-3 1-1 0-3 0-5v-9c0-4 0-7-1-10l2 1h0l1-1c0-2 1-3 2-6 0 0 0-1 1-1v-1h1z"></path><path d="M529 579l1 1v14l-1 1c-2-3 0-8 0-11v-1-4zm-6 9c1 0 1-1 2-1v2h1c-1 1-1 2-1 4-1 1 0 4-1 5v3s1 1 1 2l-1 1c-2 3 0 6-2 10v-4-5c1-3 0-6 0-8s1-3 1-5c-1-1 0-2 0-4z" class="Z"></path><path d="M523 649h7 0l1 57-10-1c-2 0-4-1-6-2v-1c-1-1-1-1-1-2v-8l1-2v-6-4l2-2 2 1 1 1 1 1 2-1h0 1l-1-2c-1-2-2-3-2-6 2 1 3 0 4 0 1-2 0-3-1-5-1-1-1-1-3-2h0l-4-2 1-5c1-3 1-5 1-8l1-1h1 2z" class="j"></path><path d="M525 659h2l1 1v3h2v20c0-1-1-1-1-2v-8c0-1 1-3 1-4-1-1-3-2-3-4-1-1-1-3-2-4l-1-1 1-1zm-2-10h7v1 1h-1c-1 1 0 1-1 1v2c0 2 1 2 2 3v1c-2 1-4 0-5 0v-2c0-3 0-4-2-7z" class="L"></path><path d="M523 678l4 2c0 2 0 5 1 7v1l-2 2c-1 1-1 2-1 2h0l-1-3v-3l-1-1c0 2 0 4 1 6 0 3-1 6-1 10h0-2c0-1 0 0 1-1s-1-1-1-2v-1l1-1c1-3 1-6 0-9v-1c1-2 1-4 1-6h1l-1-2z" class="P"></path><path d="M524 689c1-1 0-2 0-3s1-2 1-2h1v4h2l-2 2c-1 1-1 2-1 2h0l-1-3z" class="Z"></path><path d="M519 650l1-1h1l2 3c0 2 0 6-1 8v3l-1 2h0l-4-2 1-5c1-3 1-5 1-8z" class="c"></path><path d="M519 650c0 1 0 2 1 3 0 2 0 6-1 8v1c2 1 2 1 2 3l-4-2 1-5c1-3 1-5 1-8z" class="U"></path><path d="M517 678l2 1 1 1 1 1 2-1h0c0 2 0 4-1 6v1c1 3 1 6 0 9l-1 1v1c0 1 2 1 1 2s-1 0-1 1v1c1 2 0 0 1 1 0 1 0 2-1 2-2 0-4-1-6-2v-1c-1-1-1-1-1-2v-8l1-2v-6-4l2-2z" class="K"></path><path d="M517 684v-2c1-1 1-1 2-1l2 2c0 1 0 1-1 3-1-1-2-1-3-2zm0 5l1-1c2 1 3 1 4 4v-5c1 3 1 6 0 9l-1 1v-3c-2-2-4-3-4-5z" class="c"></path><path d="M517 678l2 1 1 1-1 1c-1 0-1 0-2 1v2c0 3-1 3 0 5 0 2 2 3 4 5-1 2-1 2-3 3h0v-1l1-1v-1l-1-1c-1 0-2-1-4-1l1-2v-6-4l2-2z" class="J"></path><path d="M514 692c2 0 3 1 4 1l1 1v1l-1 1v1h0c2-1 2-1 3-3v3 1c0 1 2 1 1 2s-1 0-1 1v1c1 2 0 0 1 1 0 1 0 2-1 2-2 0-4-1-6-2v-1c-1-1-1-1-1-2v-8z" class="K"></path><path d="M507 709c1-2 3-2 5-3 1 9 0 18 0 28v39 4 1l-1-1h-3v-3h-1c-1-1-2-1-3-2l-2-2 2-2h-2c-1-1-1-1-2-1-1-4 0-9 0-13l-1-27 1-11 1 1c1 1 2 2 3 4l2 1v-1-4c-1-1-1-1 0-2h1c0-2-1-4 0-6z" class="W"></path><path d="M506 715h1c0-2-1-4 0-6l1 65h-1c-1-1-2-1-3-2l-2-2 2-2 2 1c1-3 0-9 0-12v-24c0-4-1-8 0-11v-1-4c-1-1-1-1 0-2z" class="R"></path><path d="M504 768l2 1v1c0 1-1 2-2 2h0l-2-2 2-2z" class="m"></path><path d="M500 716l1 1c1 1 2 2 3 4l2 1c-1 3 0 7 0 11v24c0 3 1 9 0 12l-2-1h-2c-1-1-1-1-2-1-1-4 0-9 0-13l-1-27 1-11z" class="F"></path><path d="M502 768l1-1-1-1h-1c0-1 0-1 1-2l-1-1c0-1 0-2 1-3h1c0 2 1 4 0 7l-1 1z" class="E"></path><path d="M501 717c1 1 2 2 3 4l2 1c-1 3 0 7 0 11v24c0 3 1 9 0 12l-2-1h-2l1-1c1-3 0-5 0-7v-4c1-3 1-13 0-15h-1-1l1-1h1v-13h-1v-2c-1-3-1-5-1-8z" class="X"></path><path d="M504 721l2 1c-1 3 0 7 0 11l-2-2v-10z" class="L"></path><path d="M494 649h6v3h-1l-1-1h-1v9 8l1 1 1-1c0 1 0 1 1 1v2h3 3 1 4l1 1h2 0v3c-1 2 0 4 1 5v4 6l-1 2v8c0 1 0 1 1 2-1 0-2 0-3-1-2 2-3 2-5 3-4 1-8 2-12 2h-6c0-5 0-11 1-16 0-5 1-13 0-18-1-1-1-3-1-5 1-1 1-2 0-4v-9-5h5 0z" class="F"></path><path d="M498 680v-5l-1-1c-1 0-3 0-4 1v1l-1-1c1-1 1-1 2-1 1-1 0-1 1-1 2-2 3-2 5-2h3c-1 1-1 1-2 1v1 2c-1 1-2 1-2 3 0 1-1 1-1 2z" class="M"></path><path d="M489 649h5v8c-1 1 0 3-1 5 0 2 1 3 0 5h-4c1-1 1-2 0-4v-9-5z" class="H"></path><path d="M490 655h2v4c-1 0-2-1-2-1-1-1-1-2 0-3z" class="O"></path><path d="M501 695h4c0-2 0-5 1-7h1v16c-4 1-8 2-12 2l2-2c-1-2-1-3-1-4 1-2-1-3 0-5h1 1c0-1-1-2-2-3v-2h1l2 2-1 1h1c2 1 0 0 1 2h1z" class="E"></path><path d="M496 700c1-2-1-3 0-5h1 1c0-1-1-2-2-3v-2h1l2 2-1 1h1c2 1 0 0 1 2h1l1 1h1c1 1 1 1 1 3-2 0-4-1-5 0 1 0 1 1 1 1 1 1 3 1 3 3h-1l-2-2h-1c-1 0-1-1-2-1h-1z" class="D"></path><path d="M503 671h3 1c1 2 1 11 0 14v1h1l-2 2c-1 2-1 5-1 7h-4-1c-1-2 1-1-1-2h-1l1-1v-2l1-1-1-3-1-1c0 2 0 3-1 4h0c0-3 1-6 1-9h0c0-1 1-1 1-2 0-2 1-2 2-3v-2-1c1 0 1 0 2-1z" class="i"></path><path d="M511 671l1 1h2 0v3c-1 2 0 4 1 5v4 6l-1 2v8c0 1 0 1 1 2-1 0-2 0-3-1-2 2-3 2-5 3v-16h-1l2-2h-1v-1c1-3 1-12 0-14h4z" class="R"></path><path d="M507 688c3 1 3 1 5 3v10c-2 2-3 2-5 3v-16z" class="D"></path><path d="M511 671l1 1v16c-1 0-3-1-4-2h-1v-1c1-3 1-12 0-14h4z" class="f"></path><defs><linearGradient id="Q" x1="508.84" y1="640.901" x2="518.367" y2="642.007" xlink:href="#B"><stop offset="0" stop-color="#0c0d10"></stop><stop offset="1" stop-color="#29271d"></stop></linearGradient></defs><path fill="url(#Q)" d="M520 589l3-1c0 2-1 3 0 4 0 2-1 3-1 5s1 5 0 8v5 4h0v3c0-1 1-2 2-2 0-2-1-2 1-4h5v1 6 26c1 1 1 3 1 4l-1 1h0-7-2-1l-1 1c0 3 0 5-1 8l-1 5 4 2h0c2 1 2 1 3 2 1 2 2 3 1 5-1 0-2 1-4 0 0 3 1 4 2 6l1 2h-1 0l-2 1-1-1-1-1-2-1-2 2c-1-1-2-3-1-5v-3h0-2l-1-1h-4-1-3-3v-2c-1 0-1 0-1-1l-1 1-1-1v-8-9h1l1 1h1v-3h-6 0-5v-3l1 1c1 0 5 0 7-1 1-1 0-3 0-4l1-4h0v-2c3 0 5 0 8-1l-1-9c1-2 0-4 0-5l1-1h1l2 1c1-1 2-1 3-1v-1h-7c0-1 0-2 1-2h4c1 0 2 0 2-1-1-2-3-3-5-4l-1-1v-1l2 1c1 1 2 1 4 1h1v-1c1-3 0-5 1-8v-2c1-1 1-2 1-3v-2c0-1 0-2 2-4l3-3z"></path><path d="M516 648h1v6l-2-1c0-3 0-3 1-5z" class="U"></path><path d="M517 638h0c1 2 1 3 1 5-1 1-1 3-1 5h0-1c-1-2-1-5-1-7s0-2 2-3z" class="J"></path><path d="M510 621h2c0 3 1 13-1 15l-1 1c0-2 1-4 0-6v-4h1c-1-2-1-4-1-6z" class="E"></path><path d="M521 635l2 1v2c1 2 0 3 0 4v2h7c1 1 1 3 1 4l-1 1h0-7-2-1l-1 1c0 3 0 5-1 8l-1-4v-6h0c0-2 0-4 1-5 0-2 0-3-1-5h0l-1-1h2c1-1 2-1 3-2z" class="K"></path><path d="M521 635l2 1v2c1 2 0 3 0 4v2h-2c0-2 0-3-1-5v-1h0-3 0l-1-1h2c1-1 2-1 3-2z" class="g"></path><path d="M521 635l2 1v2h-3 0-3 0l-1-1h2c1-1 2-1 3-2z" class="q"></path><path d="M515 670h0v-2-3h3c3 0 3 1 5 3l1-1c1 2 2 3 1 5-1 0-2 1-4 0 0 3 1 4 2 6l1 2h-1 0l-2 1-1-1-1-1-2-1-2 2c-1-1-2-3-1-5v-3h0 1l1-1-2-1h1z" class="a"></path><path d="M515 670h2v1 1c-1 1-1 2-2 4h0l2 2-2 2c-1-1-2-3-1-5v-3h0 1l1-1-2-1h1z" class="r"></path><path d="M519 679c-1-1-1-3 0-5 0-1 1-2 2-2h0c0 3 1 4 2 6l1 2h-1 0l-2 1-1-1-1-1z" class="c"></path><path d="M505 626c1-2 0-4 0-5l1-1h1l2 1h1c0 2 0 4 1 6h-1v4c1 2 0 4 0 6l1-1v4-1c1 1 1 3 1 4-1 1-1 1-2 1v1h-2c-1 1-1 1-2 1l-1-8h-7v-2c3 0 5 0 8-1l-1-9z" class="R"></path><path d="M510 637c-1 0-2-1-3-1v-9c0-2-1-5 0-7l2 1h1c0 2 0 4 1 6h-1v4c1 2 0 4 0 6z" class="D"></path><path d="M510 645v2c1 0 1 0 1 1l-1 1h1l1 1v1l-1 1v-1l-1 1c1 1 2 1 2 2s0 1-1 1h-2v1h1l1 2h-1v3c1 1 1 1 2 1 0 1 0 1-1 2l1 1c0 3-1 4-1 6h-4-1-3-3v-2-1c0-2 1-2 1-4h3c0-1 0-1 1-2v-8-4-1c-1 0-1 0-2-1h2l1-2c1 0 1 0 2-1h2z" class="J"></path><path d="M506 666c1 1 2 2 1 3 0 1-1 1-1 2h-3-3v-2-1h1 3c1-1 1-1 2-1v-1z" class="R"></path><path d="M506 666h0l1-1h5c0 3-1 4-1 6h-4-1c0-1 1-1 1-2 1-1 0-2-1-3z" class="a"></path><path d="M520 589l3-1c0 2-1 3 0 4 0 2-1 3-1 5s1 5 0 8v5 4h0v3l-1 1h3v1h-2c-1 4 0 9-1 14l1 1-1 1h0c-1 1-2 1-3 2-1-3 0-8-1-11v-2c1-5 0-8 0-12 0-6 1-14 0-20l3-3z" class="V"></path><path d="M498 638h7l1 8-1 2h-2c1 1 1 1 2 1v1 4 8c-1 1-1 1-1 2h-3c0 2-1 2-1 4v1c-1 0-1 0-1-1l-1 1-1-1v-8-9h1l1 1h1v-3h-6 0-5v-3l1 1c1 0 5 0 7-1 1-1 0-3 0-4l1-4h0z" class="E"></path><path d="M501 664v-1h-1l1-2 3 3h-3zm4-2l-3-5c-2-2-1-3-1-6l2-1h1v1h-2l-1 1 2 1v1h2v8z" class="C"></path><path d="M498 638h5v1h0c-1 1-1 2-2 3 0 2-2 3-3 4-2 2-3 2-6 2 1 1 1 1 2 1h0-5v-3l1 1c1 0 5 0 7-1 1-1 0-3 0-4l1-4z" class="D"></path><path d="M522 617c0-1 1-2 2-2 0-2-1-2 1-4h5v1 6 26h-7v-2c0-1 1-2 0-4v-2l-2-1h0l1-1-1-1c1-5 0-10 1-14h2v-1h-3l1-1z" class="L"></path><path d="M526 624h2l1 1-2 1h-2c0-1 0-1 1-2z" class="p"></path><path d="M530 612v6h-4-1c1-2 1-2 1-4h0c1-2 2-1 4-2z" class="S"></path><path d="M521 635h0l1-1-1-1c1-5 0-10 1-14h2c-1 1-1 2 0 3h0c-1 2 0 2 0 4v3l1 1c0 2-3 1-2 4l2-1c0 2 1 2 0 3h-2 0l-2-1z" class="Z"></path><path d="M565 468v-2h7l8 1 10 1 10 1 4 2c3 1 7 3 10 4 4 0 8 2 11 3l5 1 2 1 2 2v1c2 2 3 4 4 6v3 1l-2 3c-2 6-6 8-12 10l-12 4c-2 0-2 0-4 1l2-2c-2 0-4 1-7 2s-6 1-9 2l-1-1c-3 1-6 1-9 3h0l1 1h1l-6 1c-1-1-4 0-5-1v-1c-2 1-6 1-9 1 0 0-1 0-1 1h-5-4c3-5 6-9 9-14l1 1c1-4 2-9 3-13 0-1 1-3 1-4v-2h0c1-1 0-3-1-4l-1-2-1 1v3l-1-6v-1l-2-6 1-1v-1z" class="i"></path><path d="M604 480h1c0 1 1 1 2 2 0 0 0 1 1 2h2l1 1c0 1 0 2 1 3h0l-1 1c0-1-2-3-3-4-1-2-2-3-4-5z" class="G"></path><path d="M614 505h-1c-1 0 0 0-1-1l1-1v-3-1l2 1c1-1 2-1 3-2-1 3-2 5-4 7z" class="E"></path><path d="M611 480c1 0 3 0 5 1 1 1 2 3 3 5-2 2-1 0-2 1-1 0-1 0-1 1l-3-5c0-1-1-2-2-3z" class="F"></path><path d="M608 502v3c-1 2-6 3-5 6-3 1-6 1-9 2l-1-1c2 0 5-1 7-3 0-1 1-1 2-1v-1c2-1 4-3 6-5z" class="C"></path><path d="M616 481l-1-3 3 3c2 1 4 1 5 3-1 0-2 0-2-1l-1 1 3 3v1l-1 1 1 1-1 2 1 1h-1c0 1-1 2-1 3-1 3-3 6-5 8l-4 4v2c-2 0-2 0-4 1l2-2c2-1 3-3 4-4 2-2 3-4 4-7 1 0 1 0 1-1 1-3 1-7 0-11-1-2-2-4-3-5z" class="V"></path><path d="M616 504c2-2 4-5 5-8 0-1 1-2 1-3h1l-1-1 1-2-1-1 1-1v-1l-3-3 1-1c0 1 1 1 2 1 2 1 2 3 3 5s1 6 0 8l-1 2-5 3c-1 1-2 2-4 2z" class="d"></path><path d="M580 467l10 1 10 1 4 2c3 1 7 3 10 4 4 0 8 2 11 3h0c0 1-1 1-1 2h-1c-2 0-2 0-5 1l-3-3 1 3c-2-1-4-1-5-1-1-2-4-4-6-4-1-1-2-2-3-2s-2-1-3-1c0 0-1 0-1-1l-1 1-2-1c-2-1-6-2-8-2s-3-1-4 0h-2c-1-1-1-2-1-3z" class="B"></path><path d="M584 471l1 1c2 1 2 1 3 2h1c1 0 1 1 2 1h1l1 1c1 1 2 1 4 2 1 1 2 2 4 3 1 0 1 0 2 1v1h1c1 0 1 1 2 1l-2 2v1h-1v-1c-2-1-2-1-4-1l-3 2c-1 2 0 3-1 5l-1 2h-1-2c0-4-1-7-2-11l-4-5c-1-2-2-3-4-4l-1-1h4v-2z" class="D"></path><path d="M584 471l1 1 1 1c0 2 1 3 2 4 0 1-2 1-3 1-1-2-2-3-4-4l-1-1h4v-2z" class="F"></path><path d="M589 483v-1l-1-2c1 0 2 0 2-1l2 2v1l-1-1-1 1c1 1 3 2 4 4l-1 1h0 1 2c-1 2 0 3-1 5l-1 2h-1-2c0-4-1-7-2-11z" class="I"></path><path d="M606 484c1 1 2 2 2 4h0-1s-1 0-1 1c-2-1-2-1-3 0h-1c0 2-1 2-2 3h0c0 1 0 1 1 1 2-1 6 0 8 1h1v-2-1c1 2 1 3 1 5v1c-1 1-1 1-1 2h-2c0 2-2 3-3 5-1-1-1 0-1-1h-2c-1 2-2 3-4 3l1-2s0-1-1-1h-2c-1 0-1-1-2-1v-2c1-1 1-1 1-3v-2h1c-1-1-1-2-1-3 1-2 0-3 1-5l3-2c2 0 2 0 4 1v1h1v-1l2-2z" class="E"></path><path d="M602 503l-1-1h1v-1-1l2-2 1 1v-1c2 0 2 0 3 1 0 2-2 3-3 5-1-1-1 0-1-1h-2z" class="C"></path><path d="M591 494h2 1l1-2c0 1 0 2 1 3h-1v2c0 2 0 2-1 3v2c1 0 1 1 2 1h2c1 0 1 1 1 1l-1 2c2 0 3-1 4-3h2c0 1 0 0 1 1 1-2 3-3 3-5h2c-1 1-1 2-2 3-2 2-4 4-6 5v1c-1 0-2 0-2 1-2 2-5 3-7 3-3 1-6 1-9 3h0l1 1h1l-6 1c-1-1-4 0-5-1v-1l8-2c-1-2 3-4 4-6v-2c1-2 3-5 3-8l1-3z" class="N"></path><path d="M589 508h2l1 1h1c1-1 2-1 3-1h0c-1 1 0 1-1 1s-2 1-3 1l1 1c1 0 1 0 3-1 1 0 2-1 4-1-2 2-5 3-7 3-3 1-6 1-9 3l-1-2c3-1 5-4 6-5z" class="P"></path><path d="M590 497h1c0 1 0 1 1 2 0 1 0 1-1 2s-1 1-1 3c0 1-1 1-1 2 1 0 1-1 2-1v-2l1 1h0c-1 2-2 3-3 4s-3 4-6 5l1 2h0l1 1h1l-6 1c-1-1-4 0-5-1v-1l8-2c-1-2 3-4 4-6v-2c1-2 3-5 3-8z" class="L"></path><path d="M623 480h1c0-1 1-1 1-2h0l5 1 2 1 2 2v1c2 2 3 4 4 6v3 1l-2 3c-2 6-6 8-12 10l-12 4v-2l4-4c2 0 3-1 4-2l5-3 1-2c1-2 1-6 0-8s-1-4-3-5c-1-2-3-2-5-3 3-1 3-1 5-1z" class="F"></path><path d="M633 490v-1c0-1-1-1-1-2 1-1 1-1 3-1l1 1v1l-1 1h-1l-1 1z" class="W"></path><path d="M628 486c0 1 0 1 1 2v1c1 2 0 3-1 5v2c-1 1 0 1-1 1h-1c1-2 1-6 0-8l2-3z" class="D"></path><path d="M623 480h1c0-1 1-1 1-2h0l5 1 2 1 2 2v1h-1v1 1l-1 1-3-4h-2 0l4 4-1 1c-1-1-2-1-3-2l1 1-2 3c-1-2-1-4-3-5-1-2-3-2-5-3 3-1 3-1 5-1z" class="O"></path><path d="M618 481c3-1 3-1 5-1 0 1 0 2 1 2l3 3 1 1-2 3c-1-2-1-4-3-5-1-2-3-2-5-3z" class="M"></path><path d="M633 490l1-1 1 3-1 1v1l2 2c-2 6-6 8-12 10l-12 4v-2l4-4c2 0 3-1 4-2l5-3h1c1-1 2 0 3 0-1 1-1 1-2 1h-1l-2 3c-2 0-3 0-5 1v1c2 0 3-1 5-1 3-1 5-2 7-4h0c1-1 2-2 2-3s-1-2 0-4v-3z" class="D"></path><path d="M565 468v-2h7l8 1c0 1 0 2 1 3h2l1 1v2h-4l1 1c2 1 3 2 4 4l4 5c1 4 2 7 2 11l-1 3c0 3-2 6-3 8v2c-1 2-5 4-4 6l-8 2c-2 1-6 1-9 1 0 0-1 0-1 1h-5-4c3-5 6-9 9-14l1 1c1-4 2-9 3-13 0-1 1-3 1-4v-2h0c1-1 0-3-1-4l-1-2-1 1v3l-1-6v-1l-2-6 1-1v-1z" class="R"></path><path d="M579 496c1 0 2 0 3 1l-1 3 1 1c-1 1-1 2-2 3h0c-1 1-3 2-4 2h0c1-1 2-1 3-2v-2-1-4-1z" class="a"></path><path d="M584 496c0-1 0-3 1-3v2 2l-1 3-4 6c-1 0-2 1-2 2-4 2-7 5-12 6l1-1c2-2 5-3 6-5l1-1v-1h1 1 0c1 0 3-1 4-2h0c1-1 1-2 2-3 1-2 1-3 2-5z" class="J"></path><path d="M575 481l3 3c0 1 1 1 1 2l-1 1 1 2 4 3 1 4c-1 2-1 3-2 5l-1-1 1-3c-1-1-2-1-3-1v1h-1c-1 2 0 4-2 5l-1-1h0c0-2-1-2 0-4v-2-1c1-2-1-5-2-6v-2h0c-1-2-1-3-2-4l1-1 2 1 1-1z" class="b"></path><path d="M575 487c1 0 2 1 2 2 1 2 2 3 2 4 1 0 2 1 3 1v3c-1-1-2-1-3-1h-2v1l-1-2v-1-2-2l-1-1v-2z" class="a"></path><path d="M579 493c1 0 2 1 3 1v3c-1-1-2-1-3-1v-3z" class="K"></path><path d="M575 481l3 3c0 1 1 1 1 2l-1 1 1 2 4 3 1 4c-1 2-1 3-2 5l-1-1 1-3v-3c-1 0-2-1-3-1 0-1-1-2-2-4 0-1-1-2-2-2 0-2-1-3-1-5l1-1z" class="V"></path><path d="M579 489l4 3 1 4c-1 2-1 3-2 5l-1-1 1-3v-3l-1-2c-1-1-1-2-2-3z" class="T"></path><path d="M568 479l2-1 2 2-1 1v1c1 1 1 2 2 4h0c-1 3 1 5 0 7v2c0 4-2 10-5 14h-1c-1 1-2 2-3 2-1 2-1 3-3 3l1-1c2-3 2-6 4-9 1-4 2-9 3-13 0-1 1-3 1-4v-2h0c1-1 0-3-1-4l-1-2z" class="q"></path><path d="M566 504c1-4 2-9 3-13 0-1 1-3 1-4v-2c1 6 1 13-2 19-1 3-2 5-4 7-1 2-1 3-3 3l1-1c2-3 2-6 4-9z" class="U"></path><path d="M581 474c2 1 3 2 4 4l4 5c1 4 2 7 2 11l-1 3c0 3-2 6-3 8v2c-1 2-5 4-4 6l-8 2c-2 1-6 1-9 1 0 0-1 0-1 1h-5v-1h2c1-1 2-2 4-2 5-1 8-4 12-6 0-1 1-2 2-2l4-6 1-3v-2h1c0 1 1 2 1 3h2v-2c0-3 1-5 0-7v-1c-1-6-4-10-9-14h1z" class="T"></path><path d="M580 506l2 3-3 3h-1c0 1-1 1-2 1l-4 2c2-3 4-4 6-7h0c0-1 1-2 2-2z" class="g"></path><path d="M585 495h1c0 1 1 2 1 3h2c0 1-1 3-2 4l-3 5-2 2-2-3 4-6 1-3v-2z" class="L"></path><path d="M584 500l1-3c1 3 0 3-1 5h3l-3 5c-1-1-1-3-1-4l1-3z" class="S"></path><path d="M580 506l4-6-1 3c0 1 0 3 1 4l-2 2-2-3z" class="P"></path><path d="M565 468v-2h7l8 1c0 1 0 2 1 3h2l1 1v2h-4l1 1h-1c5 4 8 8 9 14v1c1 2 0 4 0 7v2h-2c0-1-1-2-1-3h-1v-2c-1 0-1 2-1 3l-1-4-4-3-1-2 1-1c0-1-1-1-1-2l-3-3-1 1-2-1-1 1v-1l1-1-2-2-2 1-1 1v3l-1-6v-1l-2-6 1-1v-1z" class="I"></path><path d="M586 490c1 2 1 3 2 4s0 1 1 2v2h-2c0-1-1-2-1-3v-5z" class="N"></path><path d="M581 480c3 3 4 7 5 10v5h-1v-2c-1 0-1 2-1 3l-1-4c1-1 1-2 1-3-1-1-1-2-2-3v-2c-1-1-2-2-2-3l1-1z" class="V"></path><path d="M576 475l3 2 2 3-1 1c0 1 1 2 2 3v2c1 1 1 2 2 3 0 1 0 2-1 3l-4-3-1-2 1-1c0-1-1-1-1-2l-3-3-1-1 1-2h0l-2-2 2-1h1z" class="g"></path><path d="M576 475l3 2 2 3-1 1-5-3h0l-2-2 2-1h1z" class="K"></path><path d="M565 468v-2h7l8 1c0 1 0 2 1 3h2l1 1v2h-4l1 1h-1l-2-1h-1l1 1c0 1 1 2 1 3l-3-2h-1l-2 1 2 2h0l-1 2 1 1-1 1-2-1-1 1v-1l1-1-2-2-2 1-1 1v3l-1-6v-1l-2-6 1-1v-1z" class="d"></path><path d="M565 468c3 0 4 2 6 3 1 1 3 1 4 2v1h1v1h-1l-3-1c-2-1-3-3-5-4l-2-1v-1z" class="c"></path><path d="M565 469l2 1c2 1 3 3 5 4-1 2-1 2-4 2h-2l-2-6 1-1z" class="R"></path><path d="M572 474l3 1-2 1 2 2h0l-1 2 1 1-1 1-2-1-1 1v-1l1-1-2-2-2 1-1 1v3l-1-6v-1h2c3 0 3 0 4-2z" class="J"></path><path d="M574 480s-1 0-2-1c0-1-1-1-1-2l2-1 2 2h0l-1 2z" class="U"></path><path d="M572 466l8 1c0 1 0 2 1 3h2l1 1v2h-4c-2-1-4-3-6-4h-1c-1 0-2-1-3-2l2-1z" class="O"></path><path d="M525 547c1 0 3-1 4-2s2-3 4-4c0 4-1 7-4 9h-1c-1 1-3 1-5 1l2 2h1 1l1 1h-1v1c-1 0-1 1-1 1-1 3-2 4-2 6l-1 1h0l-2-1c1 3 1 6 1 10v9c0 2 1 4 0 5 0 1-1 2-2 3l-3 3c-2 2-2 3-2 4v2c0 1 0 2-1 3v2c-1 3 0 5-1 8v1h-1c-2 0-3 0-4-1l-2-1v1l1 1c2 1 4 2 5 4 0 1-1 1-2 1h-4c-1 0-1 1-1 2h7v1c-1 0-2 0-3 1l-2-1h-1l-1 1c0 1 1 3 0 5l1 9c-3 1-5 1-8 1v2h0l-1 4c0 1 1 3 0 4-2 1-6 1-7 1l-1-1v3 5c-2 0-3 0-5-1h-4v2c-2 0-1 0-2-1h0c-1-1-2-1-3-2l-1-5 1-5c0-1 1-1 1-2l2 1h1v-2-10-5l2-7v-3-1l1-1v-7-2-1-1-1-2h-3l1-1v-1c-1-1-1-2-1-2 3-1 4-1 6 0v-12-21-9l2-1-1-1 1-1c-1-3-1-5-1-7l1-1c0 1 0 3 1 4 3 3 15 3 19 3 1-2 0-3 1-4h2c1 1 1 1 2 1v2c2 1 4 1 6 1l7-1z" class="B"></path><path d="M496 587l1-3c1 3 2 4 3 6 0 2-1 4-1 6h-1v-1c-1-2-3-6-2-8z" class="D"></path><path d="M495 628v-3c0-1 0-2 1-3h0l1-2c-1-2 0-3-1-4v-2l-1-1c0-1 0-2 1-2 0-1 0-1 1-2l1 1-1 2v1c0 2 1 3 1 5v2c0 1-1 2-1 3l-1 1 1 2-1 1v1h-1z" class="F"></path><defs><linearGradient id="R" x1="504.662" y1="631.381" x2="495.357" y2="630.805" xlink:href="#B"><stop offset="0" stop-color="#a9a188"></stop><stop offset="1" stop-color="#c6bea8"></stop></linearGradient></defs><path fill="url(#R)" d="M498 596h1 0c0 2 1 3 0 4-1 3 0 7 0 10 0 1 1 2 1 2l-1 2 1 1c1 1 1 2 1 3v1c1 2-1 4-1 6 1 1 1 0 1 1s1 2 1 3h2l1 1v-1-3l1 9c-3 1-5 1-8 1v2h0l-1 4v-2c-1-1-1-1 0-2l-1-2h-1c-1-2 0-5 0-8h1v-1l1-1-1-2 1-1c1 1 1 1 1 3l-1 2 1 1 1-1c0-1 0-1 1-1v-1c-1-1-1-1-2-1l2-3h0c0-4 0-1-1-2 0 0 0-2-1-2 1-2 1-2 0-3v-1l1-1v-1h0c-1-1-1-2-1-3v-4h-1v-2h1v-1-2l-1 1h-1l1-2 1-1c1 0 0-1 0-2z"></path><path d="M485 561v-1c1 0 1 0 1-1 1 1 1 2 1 3v7l1 22c0 3 1 8 0 11h-6v-1-1-2h-3l1-1v-1c-1-1-1-2-1-2 3-1 4-1 6 0v-12-21z" class="U"></path><path d="M485 594l1 1c-2 1-4 1-6 1-1-1-1-2-1-2 3-1 4-1 6 0z" class="d"></path><path d="M482 603h0 6l1 1h0c-1 3-1 5-1 7v14 13c0 3 1 5 0 8h-1c0-1-1-1-1-2h-1v-22c-1-5 0-10 0-15-1-1-1-1-3-2v-2z" class="J"></path><path d="M485 607h2c1 2 1 11 0 13v1 1h-2c-1-5 0-10 0-15z" class="V"></path><path d="M485 622h2 0v14h1v-11h0v13c0 3 1 5 0 8h-1c0-1-1-1-1-2h-1v-22z" class="a"></path><path d="M500 590c1 0 3 3 4 3 0 1 1 2 1 2 1 1 1 2 1 3v8 2c0 1 3 2 4 3h0c1 0 2 1 2 1-2 0-3 0-4-1l-2-1v1l1 1c2 1 4 2 5 4 0 1-1 1-2 1h-4c-1 0-1 1-1 2h7v1c-1 0-2 0-3 1l-2-1h-1l-1 1c0 1 1 3 0 5v3 1l-1-1h-2c0-1-1-2-1-3s0 0-1-1c0-2 2-4 1-6v-1c0-1 0-2-1-3l-1-1 1-2s-1-1-1-2c0-3-1-7 0-10 1-1 0-2 0-4h0c0-2 1-4 1-6z" class="G"></path><path d="M504 593c0 1 1 2 1 2 1 1 1 2 1 3v8 2c0 1 3 2 4 3h0c1 0 2 1 2 1-2 0-3 0-4-1l-2-1v1l1 1c0 1 0 1 1 2v1c-1 0-2 0-3-1h0v-2c-1 0-2-1-2-2v-1c-1-1-1-3-1-4s2-2 2-3v-9z" class="C"></path><path d="M482 605c2 1 2 1 3 2 0 5-1 10 0 15v22h1c0 1 1 1 1 2h1 1v3 5c-2 0-3 0-5-1h-4v2c-2 0-1 0-2-1h0c-1-1-2-1-3-2l-1-5 1-5c0-1 1-1 1-2l2 1h1v-2-10-5l2-7v-3-1l1-1v-7z" class="E"></path><path d="M481 613c2 0 2 0 3 2 0 3-1 5-1 8l-2-2v-4h0v-3-1z" class="W"></path><path d="M481 617h0v4l2 2v12 4c0 1-2 2-3 3l-1-3v-10-5l2-7z" class="Q"></path><path d="M479 639l1 3h3v2h1l-1 2h2 2 1 1v3 5c-2 0-3 0-5-1h-4v2c-2 0-1 0-2-1h0c-1-1-2-1-3-2l-1-5 1-5c0-1 1-1 1-2l2 1h1v-2z" class="b"></path><path d="M480 645h0v1c0 1 0 2 1 4 1 0 2-1 3-2h-1l-1-1 1-1h2l-1 1v1l3 3c-2 1-5 0-7 0-1-2 0-4 0-6z" class="Z"></path><path d="M480 642h3v2h1l-1 2-1 1 1 1h1c-1 1-2 2-3 2-1-2-1-3-1-4v-1h0v-3z" class="P"></path><path d="M475 642c0-1 1-1 1-2l2 1v13c-1-1-2-1-3-2l-1-5 1-5z" class="C"></path><path d="M525 547c1 0 3-1 4-2s2-3 4-4c0 4-1 7-4 9h-1c-1 1-3 1-5 1h-11v3c-1-1-3-1-4 0l-1 4c0-1 0-1-1-1l-2 1c-3 2-5 3-7 6-1 6 2 14 0 20l-1 3c0-1 1-4 0-5l-1-1v-5c-1-1 0-2 0-3l-1-1v-4c0-1-1-2-1-3h0c-1-2 0-2 0-3s-2-2-2-2v-3c-2-1-1-1-3-1l-1 12v1-7c0-1 0-2-1-3 0 1 0 1-1 1v1-9l2-1-1-1 1-1c-1-3-1-5-1-7l1-1c0 1 0 3 1 4 3 3 15 3 19 3 1-2 0-3 1-4h2c1 1 1 1 2 1v2c2 1 4 1 6 1l7-1z" class="Q"></path><g class="J"><path d="M487 551v11c0-1 0-2-1-3 0 1 0 1-1 1v1-9l2-1z"></path><path d="M494 555l-6-3v-1h-1v-1l1-1c1 1 3 1 4 2h1 1s-1 0-2 1c1 1 1 2 2 3h0z"></path></g><g class="e"><path d="M494 555c2 0 5 1 6 0l3 1 1 2c-3 2-5 3-7 6v-5c1-1 1-1 0-2h-2-1v-2z"></path><path d="M525 547c1 0 3-1 4-2s2-3 4-4c0 4-1 7-4 9h-1c-1 1-3 1-5 1h-11v3c-1-1-3-1-4 0l-1 4c0-1 0-1-1-1l-2 1-1-2-3-1c-1 1-4 0-6 0h0c-1-1-1-2-2-3 1-1 2-1 2-1h-1v-1c1-1 1-1 1-2l1 1v2h4l1-1c2-1 3 0 4-1 2-2 10-1 14-1l7-1z"></path></g><path d="M494 555h0c-1-1-1-2-2-3 1-1 2-1 2-1l7 1h6c-2 0-2 0-4 1-1 1-2 1-3 0v2c-1 1-4 0-6 0z" class="U"></path><path d="M501 552h6c-2 0-2 0-4 1-1 1-2 1-3 0-1 0-2 0-3-1h4z" class="q"></path><path d="M507 552l5-1v3c-1-1-3-1-4 0l-1 4c0-1 0-1-1-1l-2 1-1-2-3-1v-2c1 1 2 1 3 0 2-1 2-1 4-1z" class="n"></path><path d="M506 557c0-1-1-2-1-3v-1l3 1-1 4c0-1 0-1-1-1z" class="l"></path><path d="M525 547c1 0 3-1 4-2s2-3 4-4c0 4-1 7-4 9h-1c-3-1-6-1-9-1h-15c2-2 10-1 14-1l7-1z" class="B"></path><defs><linearGradient id="S" x1="519.373" y1="551.455" x2="511.338" y2="590.155" xlink:href="#B"><stop offset="0" stop-color="#0c0b0a"></stop><stop offset="1" stop-color="#292923"></stop></linearGradient></defs><path fill="url(#S)" d="M512 551h11l2 2h1 1l1 1h-1v1c-1 0-1 1-1 1-1 3-2 4-2 6l-1 1h0l-2-1c1 3 1 6 1 10v9c0 2 1 4 0 5 0 1-1 2-2 3l-3 3c-2 2-2 3-2 4v2c0 1 0 2-1 3v2c-1 3 0 5-1 8v1h-1s-1-1-2-1h0c-1-1-4-2-4-3v-2-8c0-1 0-2-1-3 0 0-1-1-1-2-1 0-3-3-4-3-1-2-2-3-3-6 2-6-1-14 0-20 2-3 4-4 7-6l2-1c1 0 1 0 1 1l1-4c1-1 3-1 4 0v-3z"></path><path d="M518 559c-1 0-2 0-3-2v-1l10-1c-2 1-2 1-4 3 0 1-2 1-3 1z" class="R"></path><path d="M525 555l2-1v1c-1 0-1 1-1 1-1 3-2 4-2 6l-1 1h0l-2-1-3-3c1 0 3 0 3-1 2-2 2-2 4-3z" class="a"></path><path d="M515 589c0-1 0-1 1-2 1-5 1-10 1-15 0-2 0-6-1-8 0-1 0-1 1-2h1l1 2c1 2 1 4 1 7v9c0 2 0 3-1 5l-4 4z" class="K"></path><path d="M519 585v-2-7h1v4c0 2 0 3-1 5z" class="V"></path><path d="M520 571l2 1v9c0 2 1 4 0 5 0 1-1 2-2 3l-3 3c-2 2-2 3-2 4v2c0 1 0 2-1 3v2c-1 3 0 5-1 8v1h-1s-1-1-2-1l1-1c0-1 0-1 1-2 1-4 0-9 0-14l1-4v1h1c1-1 1-1 1-2l4-4c1-2 1-3 1-5v-9z" class="m"></path><path d="M508 554c1-1 3-1 4 0v17 23c0 5 1 10 0 14h-1c-1 0-3 0-4-1v-28c0-6-1-11 0-17v-3-1l1-4z" class="B"></path><path d="M508 554c1-1 3-1 4 0v17l-1-1c-1-3 0-11-2-13l-2 5v-3-1l1-4z" class="D"></path><path d="M506 557c1 0 1 0 1 1v1 3c-1 6 0 11 0 17v28c1 1 3 1 4 1h1c-1 1-1 1-1 2l-1 1h0c-1-1-4-2-4-3v-2-8c0-1 0-2-1-3 0 0-1-1-1-2-1 0-3-3-4-3-1-2-2-3-3-6 2-6-1-14 0-20 2-3 4-4 7-6l2-1z" class="n"></path><path d="M506 557c1 0 1 0 1 1v1c-1 1-2 2-3 2-1-1-1-2 0-3l2-1z" class="m"></path><path d="M503 588c1-2 1-5 0-6 0-1-1 0-1-1l1-2h3v4 9l-3-4z" class="I"></path><path d="M503 588h-1c-1-1-2-3-3-4v-5-12c1-2 5-4 6-6l1 18h-3l-1 2c0 1 1 0 1 1 1 1 1 4 0 6z" class="E"></path><path d="M524 162c2 2 4 3 6 5 4 3 6 8 7 13 0 2 1 4-1 6v1l-1 1c-1 1-1 1-1 2v6l1 7c1-1 3-2 4-2v1l-1 1c-1 1-2 1-3 3-1 5 0 12 0 17v39 113h1c-1 4-1 8-1 12v22 16c0 4 0 8 1 11l-3-2-4-1-5-2-12-2h0l-11 1-4 1h0c-4 1-7 2-10 3h-1l-2 1h-1l-7 5c-4 3-7 6-10 9l-2 1c-2 2-3 4-4 7h-1c-1 3-3 5-4 8-2 1-4 1-6 1h-1-5-6c-3 1-6 1-9 1-2 1-3 1-4 1h0l-5 2c-4 1-6 2-9 4l-7 1-6 1v-11-57c0-15 0-30-2-45-3-29-14-58-32-82-21-30-51-53-86-65-6-2-13-4-20-5 5-1 11-1 15-1l207-1v-1l-1-1v-4-1-2h1 0 1c2 1 4 2 6 4 0-3 1-7 1-9 0-3 0-5-1-7-1-1-1-2-2-3 4 0 9 2 13 1l1 1 2-1c2 0 4 1 6 0h2 3c1-1 1-7 1-9v-1-14c1 0 2 1 3 0 2 0 4 2 5 2h3l1-2z" class="l"></path><path d="M387 251c-3-3-5-7-8-10 4 1 7 3 10 6v2l-2-1h-1c1 1 1 1 1 3z" class="Q"></path><path d="M429 396h3c2 2 0 44 0 50-1 2-1 3-1 5-1 2-1 3-1 5s0 3-1 5c0 2 0 3-1 5v1c-2 1-3 1-4 1 5-24 5-48 5-72z" class="H"></path><path d="M479 200c1 2 2 2 4 3l1 1 2 2h0c-1 5-1 10-1 14 0 3 0 6 1 8h-1v31h0-3v-1c0-2-1-3-2-5v-11c0-11 1-22-1-33v-1l-1-1v-4-1-2h1z" class="h"></path><path d="M484 204l2 2h0c-1 5-1 10-1 14 0 3 0 6 1 8h-1c-2-3-1-7-1-11 0-1 1-2 1-2l-1-1v-3-7z" class="O"></path><path d="M387 251c0-2 0-2-1-3h1l2 1c3 2 5 5 7 7l11 16 3 6c1 1 1 2 2 2 2 6 5 12 7 18 1 1 1 2 2 3 0 1 0 2 1 3v1l3 8c0 1 0 3 1 4 0 1 0 2 1 3v2c0 1 0 2 1 3l1 4v2l1 2c0 1 1 3 0 4v1l1 1 1 21c1 4 1 8 1 12v22c-1 1-3 0-4 0 0-18 0-37-3-56-5-32-19-61-39-87z" class="H"></path><path d="M431 339c4-1 10 0 14 0 0 2 0 3 2 5l1 1v18c0 1 1 2 1 3l1 1v-1l1 1c4 6 8 8 15 10 2 1 4 0 6 0v1 16c0 6 1 13 0 18 0 4 0 8 1 11v3l1 2h-1-1 0l2 2s0 1 1 2v1l5-3v6l3-1-7 5c-4 3-7 6-10 9l-2 1c-2 2-3 4-4 7h-1c-1 3-3 5-4 8-2 1-4 1-6 1h-1-5-6c-3 1-6 1-9 1v-1c1-2 1-3 1-5 1-2 1-3 1-5s0-3 1-5c0-2 0-3 1-5 0-6 2-48 0-50h-3v-2c1 0 3 1 4 0v-22c0-4 0-8-1-12l-1-21h0z" class="T"></path><path d="M457 448h1v1l1 1c0 1 1 2 2 3l-2 2-1-2h0l-3-1c-1 0-1 0-2-1l2-2h1 1v-1z" class="f"></path><path d="M462 395c2 0 7-1 8 0s1 1 2 1v5h-1l-2-2c-1-1-1-1-3-2h-1-4c1-1 1-1 1-2z" class="D"></path><path d="M449 383c3 0 4-1 5-2h1v2h0l1 1-3 3h1c1 0 1-1 2-1l-5 7h-1 0l-1-10z" class="H"></path><path d="M455 465c-1-1-2-1-4-2 0-2 0-2 1-4l-1-1v-1-1-2-1h5c0 1 2 1 2 2l2 2h-1c-1 3-3 5-4 8z" class="B"></path><path d="M458 455l2 2h-1c-1 0-1 0-2 1v1c0 1-2 3-2 4l-2-1h-1l1-2h0v-3c2-1 3-1 5-2z" class="Q"></path><path d="M452 440c0-1 1-1 1-2s0 0 1-1h0l2 1v1c0 1 1 2 2 2l1 1h0 1l-4 4-1 1c-1 0-2 2-3 4h0-1c-2-3-1-10-1-13l2 2z" class="M"></path><path d="M452 440c0-1 1-1 1-2s0 0 1-1h0l2 1v1c-1 1-1 1-1 2l1 1c-1 1-1 2-2 3h-1c-1-1-1-1-1-2 1-1 1-1 1-2l-1-1z" class="B"></path><path d="M452 396v4 5h0v1s1 1 1 2 0 1 1 2v2 1 2c-1 2-1 4 0 6h-1c-2-2-3-4-4-6 0-3-1-8 0-11h0c0-2 0-5 1-7 0-1 1-1 2-1z" class="D"></path><path d="M449 415l1-3c0-1 1-2 1-2v-1h1c0 1 1 1 1 2v1h1v1 2c-1 2-1 4 0 6h-1c-2-2-3-4-4-6z" class="F"></path><path d="M449 372l1-4h0c3 3 6 6 9 8 2 1 6 2 6 3-1 1-2 1-2 2l-1 2 1 1h4c-2 1-2 1-4 1l-2 2c-1 0-1 0-2 1h0c-1 1-2 2-2 3h0l1 1c-2 0-2 0-3 1v1h0-3c0-2 5-7 6-9h0l-2 1c-1 0-1 1-2 1h-1l3-3-1-1h0v-2h-1c-1 1-2 2-5 2v-11z" class="B"></path><path d="M449 372l1-4h0c3 3 6 6 9 8 2 1 6 2 6 3-1 1-2 1-2 2l-1 2 1 1h4c-2 1-2 1-4 1h-2 0-1l-2-1 3-2-1-2h1v-1c-1 0-1 0-2-1l-1-1c-1 0-2-1-3-2h0l-1 1-1-1v-2c-2 0-2 0-4-1z" class="F"></path><path d="M464 380c3-2 5-2 8-2v16h-3-14 0v-1c1-1 1-1 3-1l-1-1h0c0-1 1-2 2-3h0c1-1 1-1 2-1l2-2c2 0 2 0 4-1h-4l-1-1 1-2 1-1z" class="f"></path><path d="M463 381l1-1c1 0 3 1 4 2l-1 1h0 1 2l1 1h-1c-1 1-2 1-3 1v-1h-4l-1-1 1-2z" class="B"></path><path d="M450 438h0v-8h1l-1-2c1-2 0-7 0-9a30.44 30.44 0 0 0 8 8h1v1c1 1 2 1 3 2 1 2-1 0 1 1 1 0 2 1 2 2 0 2 0 3-2 5-1 1-3 2-4 4h0l-1-1c-1 0-2-1-2-2v-1l-2-1h0c-1 1-1 0-1 1s-1 1-1 2l-2-2z" class="F"></path><path d="M428 467v-1c1-2 1-3 1-5 1-2 1-3 1-5s0-3 1-5v2l1-1h12c2 1 3 0 5 1v1 3c0 3 0 6-1 8 1 1 0 1 1 1h-1-5-6c-3 1-6 1-9 1z" class="D"></path><path d="M432 454h0c1 0 2 1 3 0h1l-1 7c0 1-1 1-2 2l1 2h-1 0c-1 1-2 1-3 1 0-3 1-5 1-7l1-5z" class="B"></path><path d="M438 465c-1-1-1-1-1-2h-1c0-2 1-2 1-3 1-2 0-3 1-5h1c1 0 2 0 3-1 1 0 1 0 3 1 1 0 1 0 2 1l1 1h1c0 3 0 6-1 8 1 1 0 1 1 1h-1-5-6l1-1z" class="F"></path><path d="M438 465l1-1v-2l1-1 1 1 2 2 1 1c1 0 1 0 3-1l1 1c1 1 0 1 1 1h-1-5-6l1-1z" class="W"></path><path d="M456 425l1-1v-1l2 1c1 2 3 3 4 4h1v-2c2-1 2-1 4-3l1 1h1l2 2h1l1 2h-1-1 0l2 2s0 1 1 2v1l5-3v6l3-1-7 5h-1l-9 7c-1 0 0 0-1 1l-1 1c-1 2-2 3-3 4-1-1-2-2-2-3l-1-1v-1h-1v1h-1-1v-2l1-1 4-4h-1c1-2 3-3 4-4 2-2 2-3 2-5 0-1-1-2-2-2-2-1 0 1-1-1-1-1-2-1-3-2v-1c-1-1-2-2-3-2z" class="D"></path><path d="M457 448l1-2h2c1 0 1-1 2-1l3 3-1 1-1-1h-5-1z" class="H"></path><path d="M458 448h5l1 1c-1 2-2 3-3 4-1-1-2-2-2-3l-1-1v-1z" class="E"></path><path d="M469 424h1l2 2h1l1 2h-1-1 0l2 2s0 1 1 2v1l5-3v6l3-1-7 5h-1v-1c-1 0-2-1-3-2v-1c-1-1-1-2-1-2v-1c-1-1-1-1-1-2 0-2 0-3 1-4l-2-2v-1z" class="d"></path><path d="M480 430v6c-1 1-2 1-3 1 0 0-2-3-2-4l5-3z" class="n"></path><path d="M455 395h7c0 1 0 1-1 2h4 1c2 1 2 1 3 2l2 2h1v11c0 4 0 8 1 11v3h-1l-2-2h-1l-1-1c-2 2-2 2-4 3v2h-1c-1-1-3-2-4-4l-2-1v1l-1 1-3-4h1c-1-2-1-4 0-6v-2-1-2c-1-1-1-1-1-2s-1-2-1-2v-1h0v-5-4l3-1z" class="f"></path><path d="M455 395h7c0 1 0 1-1 2h-1-3l-2-2h0z" class="Q"></path><path d="M452 405l1-1v1c1 1 2 1 4 1l1-1h2l-1 1v1h1l1 1c-1 1-1 1-2 1 2 1 1 1 2 1h1c-2 2-3 1-4 2l1 1 1-1 1 1-1 1h0-2l-1 1c-2-1-2-1-3-2v-1-2c-1-1-1-1-1-2s-1-2-1-2v-1h0z" class="O"></path><path d="M454 421c-1-2-1-4 0-6v-2c1 1 1 1 3 2l1-1h2 0v3c1 2 1 2 3 3h1v-1h0c1 0 1 0 1 1v3c1-1 2-2 4-3h0l-2 2 1 1c-2 2-2 2-4 3v2h-1c-1-1-3-2-4-4l-2-1v1l-1 1-3-4h1z" class="o"></path><path d="M454 421h2v-1c2 0 2 1 3 2v1 1l-2-1v1l-1 1-3-4h1z" class="O"></path><path d="M459 422l1-1 3 3h0l1 2v2h-1c-1-1-3-2-4-4v-1-1z" class="M"></path><path d="M431 339c4-1 10 0 14 0 0 2 0 3 2 5l1 1v18 19 12c-1 1-3 0-5 0h0c-3 0-6 1-9 0-1-1-1-2 0-4l-1-23-2-28z" class="Y"></path><path d="M435 341h0l1 1 2 2h-2l-1 2-1-1c0-2 0-3 1-4zm1 38l1 2h0c0 3 1 4 2 5v1h-2l-1-2v-6z" class="H"></path><path d="M448 382v12c-1 1-3 0-5 0h0c1-1 1-2 3-2v-1l1-1c-1-1-2-1-2-2l1-1v-3h0c0-2 1-2 2-2z" class="Q"></path><path d="M433 367c2 3 2 6 2 9 0 1-1 1 0 2h1v1 6 2l-2-1v4l-1-23z" class="O"></path><path d="M440 342h1c1 1 2 3 3 4l-2 2 1 2 1-1h1l-1 1v3 1s0 1-1 1c-1 1-1 1-1 3h0c-1 0-1 1-1 1l-1-1 2-3h1l-1-1h-2v-2h0c0-1 1-2 1-3-1-1 0-2-1-3v-4z" class="Q"></path><path d="M432 446h0c2-17 2-34 1-50 5 0 10-1 15 0 1 5 0 12 0 17v15l1 19v4c-1 1-4 0-5 0h-9-4c0-2 0-3 1-5z" class="f"></path><path d="M447 442c0-1-2-3-2-4s0-2-1-2l1-1 1 1h1c-1-1-1-2-1-3h1c0 1 0 1 1 2v-1-1c-1 0-2-1-2-2l2-3 1 19h-4l1-1c0-2 0-2-1-3v-1l2 1h0v-1z" class="o"></path><path d="M435 451v-3c0-1 0-2-1-3 0-1-1-4 0-5h1 1c1 1 1 1 2 1s1 0 2-1c1 1 0 1 1 2v1c-1 2-1 1-3 1v4c0 1 1 2 2 2h0l1-1c0-1-1-2-2-2l2-2s1 1 2 1c0 0 1 0 1 1l1-2-1-2h0l-1-1-1-1h0c0-2 1-2 2-2l2 2v1h1v1h0l-2-1v1c1 1 1 1 1 3l-1 1h4v4c-1 1-4 0-5 0h-9z" class="Q"></path><path d="M454 230h6c1-1 0-1 1 0h14 2c1 1 1 1 1 2 1 3 0 8 2 10v11c1 2 2 3 2 5v1h3 0l1 1 1-6v20 14l-1 1c0-2 0-2-1-3v8c-1 2-2 4-3 4l-1-1c-1-1 0-3 0-4l-1-1v16 27 12 1l-1 1c-1-1-2-1-3-2v-1l-1 1-1 1c0 1 0 2-1 3-1 2-1 4-1 6v15c0 1 1 4 0 5-2 0-4 1-6 0-7-2-11-4-15-10l-1-1v1l-1-1c0-1-1-2-1-3v-18l-1-1c-2-2-2-3-2-5-4 0-10-1-14 0h0l-1-1v-1c1-1 0-3 0-4l-1-2v-2l-1-4c-1-1-1-2-1-3v-2c-1-1-1-2-1-3-1-1-1-3-1-4l-3-8v-1c-1-1-1-2-1-3-1-1-1-2-2-3-2-6-5-12-7-18-1 0-1-1-2-2l-3-6-11-16c-2-2-4-5-7-7v-2c2 1 11 8 12 8-4-5-11-9-17-13-3-1-7-4-10-6s-6-4-9-7l89 1z" class="K"></path><path d="M445 339h2c1 2 0 4 1 6l-1-1c-2-2-2-3-2-5z" class="Q"></path><path d="M437 301c2 1 2 1 3 2s2 1 3 1l3 1-2 1h0-2c0 1 1 2 1 3h1 1v1h-1-1v1c0 1 0 0-1 1l-5-11z" class="W"></path><path d="M441 335v-4h1c0 1 0 1 1 1 1 1 1 1 3 1h0l1 4-1 1h-4c-1-1-1-1-1-3z" class="Y"></path><path d="M442 312c1-1 1 0 1-1v-1h1c0 2 0 4 1 6 1 1 1 1 1 3v1l1 1 1-1v-1c1-1 1 0 2-1-1-1-1-2-1-3h2s0-1 1-1h0c1-1 1-2 2-3h1c-1 2-2 3-3 5h0c-2 4-2 6-3 10 0 2-1 4-1 6-1-7-4-13-6-20z" class="B"></path><path d="M475 264c2 1 3 2 4 3 0 3 1 23 0 24h-2c-1 1-2 1-3 2l1-2v-1c0-1 0-2 1-2h1v-1l-1-2 1-1c0-1 0-2-1-2v-1l2-1-1-2h-1v-2h1l-1-3v-1h1v-2c0-2 0-2-1-4l-5 1c1-2 2-3 4-3z" class="E"></path><path d="M416 239l14 3c2 1 4 1 5 2l1 1c-2 1-2 2-4 2l-7 4c0-1 0-1-1-1s0 0-1-1h2c-2-1-3-1-3-3h-1v3h-1c-3-2-3-2-3-5h-1v-5z" class="i"></path><path d="M422 243c1 0 2 0 3 1h3v1h3l1-1 1 1c0 1-1 1-1 2h0l-7 4c0-1 0-1-1-1s0 0-1-1h2v-1c1-1 0-2 0-3-1 0-2 0-2-1l-1-1z" class="W"></path><path d="M417 244v-1h2v-1l1-1c0 1 1 1 1 2h1l1 1c0 1 1 1 2 1 0 1 1 2 0 3v1c-2-1-3-1-3-3h-1v3h-1c-3-2-3-2-3-5z" class="M"></path><path d="M461 256c5 0 11 5 14 8-2 0-3 1-4 3v1h-3c-1 1-2 1-3 2l-1-1v-1h-3c-1-2-3-4-5-6l2-1c1 0 3-1 4-3h0c-1-1-1-1-2-1l1-1z" class="D"></path><path d="M462 258h1c0-1 0 0 1-1l1 1v2l1 2c1 0 1 0 2-1l1 1c-1 1-1 2-2 3-1-1-2-2-3-2-1-1-2-1-3-2-1 1-2 1-3 1l2 2 1-1c1 1 2 2 3 2l2 1v1l1 1c1 0 1 0 1-1v1c-1 1-2 1-3 2l-1-1v-1h-3c-1-2-3-4-5-6l2-1c1 0 3-1 4-3z" class="O"></path><path d="M467 300c3-2 8-6 12-7v2 8 4h0c-2 2-4 7-7 8h0-1c-1 0-2-1-3-1v-1c-4 1-4-4-7-3l-1 1c0-1 0-2-1-2l4-5c1 0 1 0 3 1-1-1-1-2-1-3l2-2z" class="B"></path><path d="M472 315v-2l2-1-1-1c0-1 1-2 1-2l2-1c1-1 0-1 1-2l2 1c-2 2-4 7-7 8zm7-20v8l-3 2-1-1 1-1-1-1v-2h1v-2h1v2h1c0-1 0-1-1-2 0-1 1-2 1-3h1z" class="W"></path><path d="M467 300c3-2 8-6 12-7v2h-1c0 1-1 2-1 3 1 1 1 1 1 2h-1v-2h-1l-1 2c-2-1-2-1-2-2-1 0-2 1-2 2v1h-2l-2-1z" class="i"></path><path d="M459 309l4-5c1 0 1 0 3 1h1v1c0 1 1 2 1 3h1v-1c0-1 1-1 2-2l2 1-3 3h0-1c1 1 0 1 1 2 0 0 0 1 1 2v1c-1 0-2-1-3-1v-1c-4 1-4-4-7-3l-1 1c0-1 0-2-1-2z" class="D"></path><path d="M480 253c1 2 2 3 2 5v1h3 0l1 1 1-6v20 14l-1 1c0-2 0-2-1-3v8c-1 2-2 4-3 4l-1-1c-1-1 0-3 0-4l-1-1v-39z" class="O"></path><path d="M482 259h3v2 3l-2 2v1h-1c-1-2 0-6 0-8z" class="H"></path><path d="M487 254v20 14l-1 1c0-2 0-2-1-3v-25-2h0l1 1 1-6z" class="U"></path><path d="M423 279l2 2s0 1 1 1c3 4 5 9 7 13 6 12 11 25 13 38h0c-2 0-2 0-3-1-1 0-1 0-1-1h-1v4h-1v2c-2 1-6 1-9 0v-1s-1-1-1-2h0v-1l-1-2v-2l-1-4c-1-1-1-2-1-3v-2c-1-1-1-2-1-3-1-1-1-3-1-4l-3-8v-1c-1-1-1-2-1-3s0-2 1-3c0 1 0 1 1 1 0-1-1-3-1-4l-1-1c1-1 2-2 2-3h0-1v-1l1-1v-1l-1 1c-1-1 0-1 0-2l-1-1 1-1 1-1-1-1-2 2-1-1 1-1h0-1-1c2-2 1-1 3-2 1 0 2-1 2-2z" class="B"></path><path d="M440 325h1c1 1 2 2 2 3l-1 1c0 1 0 1-1 1l-1-5z" class="H"></path><path d="M454 230h6c1-1 0-1 1 0h14 2c1 1 1 1 1 2-1 6 0 14 0 21v12c-5-5-12-9-19-13-4-2-9-4-13-7h0c-1 0-1-1-1-1l-1-1c-1-2-1-2-2-3s-1-1-1-2v-1-3c4-1 9 1 13 0 1-1 0-1 1-1h1c1-1 2-1 1-2s-2-1-3-1z" class="D"></path><path d="M462 247c1 0 1 0 2 1l-1 1-1 1c-1-1-1-1-1-2l1-1z" class="H"></path><path d="M475 230h2c1 1 1 1 1 2-1 6 0 14 0 21l-2 1h0 0v-2-1-2-2-2l-1-1c1-1 1-2 1-4h-1-1l1-1-1-1c-1-1-2-1-2-2h-1v-1c-1 0-1-1-1-2v-2c2 0 4 0 5-1z" class="B"></path><path d="M475 230h2c0 1 0 1-1 1l-2 2h0c-1 1-1 0-2 1v2l1 1 1 1c-1-1-2-1-2-2h-1v-1c-1 0-1-1-1-2v-2c2 0 4 0 5-1z" class="M"></path><path d="M454 230h6c1-1 0-1 1 0h14c-1 1-3 1-5 1v2c-1 1-3 3-5 3h-1-1 0l-2 2v1 1l2 2h-3l1 2h-1-2-1-2c-1 0-1 0-1 2l3 1c1-1 1-1 2-1l1 1v2h-3c-1 0-2-1-4-2l-2-1c-2-1-3-1-5-1h0c-1 0-1-1-1-1l-1-1c-1-2-1-2-2-3s-1-1-1-2v-1-3c4-1 9 1 13 0 1-1 0-1 1-1h1c1-1 2-1 1-2s-2-1-3-1z" class="Q"></path><path d="M453 238h0c1 0 2 1 3 2l-1 1h-2c-1-1-1-1-1-2l1-1z" class="f"></path><path d="M442 240v-2h3c2 1 3 0 5 1-1 1-1 2-2 3h1c1 1 3 2 4 3v2l-2-1c-2-1-3-1-5-1h0c-1 0-1-1-1-1l-1-1c-1-2-1-2-2-3z" class="Y"></path><path d="M450 339c2-1 5-1 7-1 2 1 7 0 9 1s2 2 4 3h0c1 2 3 5 4 6 0 1 0 2-1 3-1 2-1 4-1 6v15c0 1 1 4 0 5-2 0-4 1-6 0-7-2-11-4-15-10l-1-1c-1-5-1-11-1-17h0c0-3-1-8 1-10z" class="f"></path><path d="M458 368c-1-2-3-3-4-5v-2l1-1 1 2c1 2 1 3 4 3 0 0 1 0 2 1 0 1 1 2 1 3l-1 1-2-3-2 1z" class="H"></path><path d="M457 354c2-2 3-2 5-2 1 1 2 1 2 3 0 1-1 3-2 4s-2 1-4 1l-2-1c0-2 1-3 1-5z" class="R"></path><path d="M449 349c1 1 2 1 3 1v1l-1 1 1 1c2 0 3-2 5-3v4c0 2-1 3-1 5v3l-1-2-1 1v2c1 2 3 3 4 5 0 1 1 2 1 3h1l-1 1h0c-1 0-2 0-3-1v-1h-1c-1-1-2-2-3-2l-1-1-1-1c-1-5-1-11-1-17z" class="Y"></path><path d="M450 339c2-1 5-1 7-1 2 1 7 0 9 1s2 2 4 3h0l-1 2 2 2-1 2c0 1-1 2-2 3 0 1 0 1-1 1l-1-1 1-3v-1c-1 1-2 2-2 3-2 0-6 1-8 0-2 1-3 3-5 3l-1-1 1-1v-1c-1 0-2 0-3-1h0c0-3-1-8 1-10z" class="h"></path><path d="M449 349c0-3-1-8 1-10 0 1 1 1 2 2v1c-1 1-2 1-2 3l2 1v1c-1 1-2 1-3 2z" class="B"></path><path d="M450 339c2-1 5-1 7-1 2 1 7 0 9 1-1 1-1 1-3 1 0 1 0 2-1 3v1l-1-1v-1c-2-1-5-1-7-1-1 0-1 0-2 1v-1c-1-1-2-1-2-2z" class="O"></path><path d="M479 307l1 1v27 12 1l-1 1c-1-1-2-1-3-2v-1l-1 1-1 1c-1-1-3-4-4-6h0c-2-1-2-2-4-3s-7 0-9-1h0-7l-1-1v-4c0-3 1-7 3-9 1-5 4-12 7-15 1 0 1 1 1 2l1-1c3-1 3 4 7 3v1c1 0 2 1 3 1h1 0c3-1 5-6 7-8h0z" class="H"></path><path d="M449 333c0-3 1-7 3-9h0v3 2 1c-1 2-2 3-3 3z" class="f"></path><path d="M450 338c1-1 3 0 5-1h1l2-2 1 1c1 0 2 0 3-1l2 1 1-1 2 1c-3 3-6 2-10 2h-7z" class="O"></path><path d="M465 325h4c1 1 0 2 1 2 1 1 1 1 1 2l-1 2-2 1h-2 1-1l-1-1h-1c0-1 1-2 1-3v-3z" class="B"></path><path d="M459 309c1 0 1 1 1 2l1-1c3-1 3 4 7 3v1c1 0 2 1 3 1h1l-3 2 2 2c2 2 2 2 2 5h0l-1 5h-1c0-1 0-1-1-2-1 0 0-1-1-2h-4-4v-2c1-1 3-2 4-4 0-2 0-3-1-4h-1l1-1-2-2c-2 0-2 1-4 2-1 3-2 4-3 7h0c-1 1-1 2-2 3h-1c1-5 4-12 7-15z" class="f"></path><path d="M472 329v1c-1 2-2 4-3 5l1 1c3 0 4-2 6-3l1 1 3 1v12 1l-1 1c-1-1-2-1-3-2v-1l-1 1-1 1c-1-1-3-4-4-6h0c-2-1-2-2-4-3s-7 0-9-1h0c4 0 7 1 10-2l-2-1c0-1 0 0-1-1v-1-1h2 2l2-1 1-2h1z" class="b"></path><path d="M466 332h2l2-1c-1 1-3 4-3 5l-2-1c0-1 0 0-1-1v-1-1h2z" class="Q"></path><path d="M470 336c3 0 4-2 6-3l1 1c-1 2-2 2-2 4s1 3 2 3l-1 2c-3-2-5-4-7-5v-1l1-1z" class="e"></path><path d="M477 334l3 1v12 1l-4-5 1-2c-1 0-2-1-2-3s1-2 2-4z" class="M"></path><path d="M477 341c1 0 1-1 2-1l1 7v1l-4-5 1-2z" class="I"></path><path d="M479 307l1 1v27l-3-1-1-1c-2 1-3 3-6 3l-1-1c1-1 2-3 3-5v-1l1-5h0c0-3 0-3-2-5l-2-2 3-2h0c3-1 5-6 7-8h0z" class="n"></path><path d="M472 329l1-5c1 1 1 2 2 3h0 2v2l-1 1-2-1-1 2h2l1 1v1c-2 1-3 3-6 3l-1-1c1-1 2-3 3-5v-1z" class="J"></path><path d="M384 242c-3-1-7-4-10-6s-6-4-9-7l89 1c1 0 2 0 3 1s0 1-1 2h-1c-1 0 0 0-1 1-4 1-9-1-13 0v3 1c0 1 0 1 1 2s1 1 2 3l1 1s0 1 1 1c-5 0-10-2-14-3l-7-2h0l-4-1-4-1c-8-1-16 0-24-1h-3c2 1 7 1 9 1l17 1v5l-1 1h-2l-3-1c-1 0-1-1-2-1s-2-1-3-2h-1 0-2c-2 1-3 2-3 3l4 3c-2 0-6 1-8 1-1-1-2-3-3-3h-2c-2-2-3-3-6-3z" class="B"></path><path d="M392 234h17c2 0 7 1 8 0 0-2 0-2 1-3v3l-1 4c-8-1-16 0-24-1 3-1 5 0 7-1v-1h-4c-1 0-2 0-3-1h-1z" class="D"></path><path d="M399 244c-1 0-2-1-3-1v-1l-2-1c-2-1-3-1-4-2h0c-1-1-2-1-3-1h0c-1-1-2 0-3-1l1-3h2 1c1 0 2-1 3 0h1 1c1 1 2 1 3 1h4v1c-2 1-4 0-7 1h-3c2 1 7 1 9 1l17 1v5l-1 1h-2l-3-1c-1 0-1-1-2-1s-2-1-3-2h-1 0-2c-2 1-3 2-3 3z" class="F"></path><path d="M419 235c0-1 0-1 1-1h21v3 1c0 1 0 1 1 2s1 1 2 3l1 1s0 1 1 1c-5 0-10-2-14-3l-7-2h0l-4-1-4-1 1-4v3l1-1v-1z" class="D"></path><path d="M424 236l1 1h0v3l-4-1s2-1 2-2 0-1 1-1z" class="B"></path><path d="M418 234v3l1-1v-1c2 0 3-1 5 0v1c-1 0-1 0-1 1s-2 2-2 2l-4-1 1-4z" class="W"></path><path d="M425 240l1-1c1 0 0-2 2-3v1h2c2 1 2 0 4 0v1h0l1 2h-2l-1 2-7-2z" class="f"></path><path d="M434 238h5l1-1v1h1c0 1 0 1 1 2s1 1 2 3l1 1s0 1 1 1c-5 0-10-2-14-3l1-2h2l-1-2h0z" class="F"></path><path d="M434 238h5l1-1v1h1c0 1 0 1 1 2v2h0l-3-1c-1 0-3-1-4-1l-1-2h0z" class="B"></path><path d="M471 267l5-1c1 2 1 2 1 4v2h-1v1l1 3h-1v2h1l1 2-2 1v1c1 0 1 1 1 2l-1 1 1 2v1h-1c-1 0-1 1-1 2v1l-1 2h0l-2-1c0 2 0 2-1 3l-1-1-1 1v1c-1 0-2 2-4 2-1 1-1 1-1 2l-1 1c-2 1-2 2-2 3-1 2-2 2-3 4l-3 3h-1c-1 1-1 2-2 3h0c-1 0-1 1-1 1h-2c0 1 0 2 1 3-1 1-1 0-2 1v1l-1 1-1-1v-1c0-2 0-2-1-3-1-2-1-4-1-6h1v-1h-1-1c0-1-1-2-1-3h2 0l2-1-3-1c-1 0-2 0-3-1s-1-1-3-2c-2-5-5-10-7-14l3-3-1-1h1 0l1-1c1 0 1-2 3-2h1c1 0 2-1 3-1-1-1-2-1-2-3h3 0v-1c-1-1-1-2-1-3l2-1c1 0 1 1 3 1v-1h1 0 3 1l-1-2h2c2 0 1-1 2-3 1 1 1 2 2 3s2 5 4 6v-1-2c0-1 1-1 1-2v-2h3v1l1 1c1-1 2-1 3-2h3v-1z" class="D"></path><path d="M477 278l1 2-2 1v1l-1 1h-1c1-2 0-2 1-3 0-1 1-2 2-2z" class="F"></path><path d="M454 311c0-2 1-2 1-3l1-1v-2c-1 1-1 1-1 2h-1 0c0-2 0-3 1-4 1 0 0 0 1-1-1-1-2-1-3-1v-1c3-1 5 0 8 0v-3c0-1 1-1 1-2v-1h2c1-1 0-1 1 0l1 1h-1-1c0 1 0 2 1 3h0c-1 1-1 1-1 2l-1 1c-2 1-2 2-2 3-1 2-2 2-3 4l-3 3h-1z" class="o"></path><path d="M441 279h1v-1l2 2h0c1 1 0 4 1 4 1 1 3 1 4 2h3v1h2c0 2 1 2 0 4v2l1 1 1-2c1 0 1 0 2 1-2 2-6 4-8 7l-1-1v1c0 2 1 4 2 6v2c1 0 1 1 2 1-2 2-2 0-3 1s-2 2-3 2c0 1 0 2-1 3l-1 1c-1-2-1-4-1-6h1v-1h-1-1c0-1-1-2-1-3h2 0l2-1h2v-1c-1-1-1-2-3-2h-1c0-2 1-2 2-3l-1-2c0-1 1-2 2-3l-1-1c-1-1-1-1-1-2l-3-6h0l-2-2c-1-1-1-2-2-3 1 0 2-1 3-1z" class="F"></path><path d="M441 279h1v-1l2 2h0c1 1 0 4 1 4 1 1 3 1 4 2v3c0 1 0 1-1 2l2 4h0c-1-1-1 0-2-1h0v2h0c-1 0-1-1-1-2l-1-1c-1-1-1-1-1-2l-3-6h0l-2-2c-1-1-1-2-2-3 1 0 2-1 3-1z" class="D"></path><path d="M471 267l5-1c1 2 1 2 1 4v2h-1v1l1 3h-1l-4-2-1 1 1 1h0-2v3l1 1c-1 1 0 2-1 3 0 1-2 3-3 3l-1 1h0-2v1 1h-1c-1 0-1 0-1 1l-1-1c-1-1-2-2-3-2v-1l2-2c1 0 2 1 2 1v-2-1c-1-1-1-1-1-2v-1c-1-1-1-2-1-3v-1-1-2c0-1 1-1 1-2v-2h3v1l1 1c1-1 2-1 3-2h3v-1z" class="M"></path><path d="M472 269c0 1 0 4-1 4 0 0 0-1-1-1v-1h0c0-2 1-2 2-2z" class="O"></path><path d="M461 268h3v1l1 1-1 1c1 1 0 2 1 3h0c0 1 1 2 1 2 1 0 3-1 4-2h0v1h0c-1 0-2 1-3 2h-1l-1 1-1 1h0c-1-1-1-1-2-1 0-2 2-2 1-3h0l-1-1s-1 1-2 1v-1-2c0-1 1-1 1-2v-2z" class="B"></path><path d="M471 267l5-1c1 2 1 2 1 4v2h-1s-2-2-2-3v-1l-1-1v1l-1 1c-1 0-2 0-2 2h0v1l-1 2h-3-1c-1-1 0-2-1-3l1-1c1-1 2-1 3-2h3v-1z" class="f"></path><path d="M437 280h1c1 1 1 2 2 3l2 2h0l3 6c0 1 0 1 1 2l1 1c-1 1-2 2-2 3l1 2c-1 1-2 1-2 3h1c2 0 2 1 3 2v1h-2l-3-1c-1 0-2 0-3-1s-1-1-3-2c-2-5-5-10-7-14l3-3-1-1h1 0l1-1c1 0 1-2 3-2z" class="B"></path><path d="M454 266c1 1 1 2 2 3s2 5 4 6v1c0 1 0 2 1 3v1c0 1 0 1 1 2v1 2s-1-1-2-1l-2 2v1l1 2c1 1 1 1 1 2s-1 1-2 2c-1-1-1-1-2-1l-1 2-1-1v-2c1-2 0-2 0-4h-2v-1h-3c-1-1-3-1-4-2-1 0 0-3-1-4h0l-2-2v1h-1c-1-1-2-1-2-3h3 0v-1c-1-1-1-2-1-3l2-1c1 0 1 1 3 1v-1h1 0 3 1l-1-2h2c2 0 1-1 2-3z" class="O"></path><path d="M443 271c1 0 1 1 3 1v-1h1c-2 2-3 3-5 4-1-1-1-2-1-3l2-1zm-2 8c-1-1-2-1-2-3h3c1 2 2 2 4 3l2 3h0c-2 0-2-1-4-2h0l-2-2v1h-1z" class="Y"></path><path d="M444 280c2 1 2 2 4 2h0 1c0 1 1 1 2 1v1h1l1 1-1 1h-3c-1-1-3-1-4-2-1 0 0-3-1-4z" class="H"></path><path d="M399 244c0-1 1-2 3-3h2 0 1c1 1 2 2 3 2s1 1 2 1l3 1h2l1-1h1c0 3 0 3 3 5h1v-3h1c0 2 1 2 3 3h-2c1 1 0 1 1 1s1 0 1 1l7-4c2 0 2-1 4-2 3 0 5 1 8 2h0l-1 2h0l3-2c5 2 10 5 15 8h0v1l-1 1c1 0 1 0 2 1h0c-1 2-3 3-4 3l-2 1c2 2 4 4 5 6v2c0 1-1 1-1 2 0 2 0-1 0 2v1c-2-1-3-5-4-6s-1-2-2-3c-1 2 0 3-2 3h-2l1 2h-1-3 0-1v1c-2 0-2-1-3-1l-2 1c0 1 0 2 1 3v1h0-3c0 2 1 2 2 3-1 0-2 1-3 1h-1c-2 0-2 2-3 2l-1 1h0-1l1 1-3 3-1-2c-1-1-2-4-4-4l-2-2c0 1-1 2-2 2-2 1-1 0-3 2h1 1 0l-1 1 1 1 2-2 1 1-1 1-1 1 1 1c0 1-1 1 0 2l1-1v1l-1 1v1h1 0c0 1-1 2-2 3l1 1c0 1 1 3 1 4-1 0-1 0-1-1-1 1-1 2-1 3-1-1-1-2-2-3-2-6-5-12-7-18-1 0-1-1-2-2l-3-6-11-16c-2-2-4-5-7-7v-2c2 1 11 8 12 8-4-5-11-9-17-13 3 0 4 1 6 3h2c1 0 2 2 3 3 2 0 6-1 8-1l-4-3z" class="M"></path><path d="M450 269l-1-1v-1l2-1c0 1 0 1 1 1l1-2 1 1h0c-1 2 0 3-2 3h-2z" class="Y"></path><path d="M429 261c-1 0-1-1-2-2h2 1 1c1 1 1 1 2 1h1c2 0 1 0 3 1-2 1-4 1-5 1l-2-1h-1z" class="H"></path><path d="M410 249h3c1 1 2 1 2 1l1 2h-2v2c-1-1-1-1-2-1-1-1-3-2-4-3 0-1 1-1 2-1zm-6 7l2-1 1 2c1-1 1-1 1-2h1c1 1 1 1 2 1h1 1 0l-2 2v-1c-2 1-1 0-2 1l1 2v3l-3-3v-1l-1-1c-1 0-1-1-2-2z" class="W"></path><path d="M430 251h1c1 1 3 0 5 0 1 1 1 0 1 2h0c-1 1-2 1-2 1h-1-1-1c-1 0-1 0-2-1h-1l-1 1c-1 0-2 0-3-1v-1h1 2c1 0 1 0 2-1h0z" class="B"></path><path d="M422 264h-1c-1-1-1-1-2-1h-3c0-1-1-1-1-2l3-3h2v1l6 6-1 1c-1-1-2-1-2-2h-1z" class="O"></path><path d="M422 264h1c0 1 1 1 2 2l1-1 1 1 4 4-3 3 1 2h0-1c-1 1-2 2-3 2l-2 2c0-2-1-3-2-4v-1l1 1 1-1v-1c1-1 1-2 2-3 0-1 1-1 2-2l-1-1c-1 1-2 1-3 0-1 0-1 0-2 1h0l1-1-1-2 1-1z" class="Y"></path><path d="M423 274c1-1 2-1 2-2 1 2 1 2 0 4h1l2-2v-1l1 2h0-1c-1 1-2 2-3 2l-2 2c0-2-1-3-2-4v-1l1 1 1-1z" class="i"></path><path d="M407 264l-3-3 1-1 1 1 1-1 3 3 6 6c0 1 1 1 1 2l2 1v1c-2 1-4 2-4 3-1 0-1 0-1-1h-1-1 1l-1-1s0-1-1 0l-1-2 1-1 1 1h0v-1c-1-1-1-2-1-3l-4-4z" class="f"></path><path d="M389 247c2 1 11 8 12 8l3 1c1 1 1 2 2 2l1 1v1l-1 1-1-1-1 1 3 3-1 2 2 2h-1l-7-10-3-3-1 1c-2-2-4-5-7-7v-2zm10-3c0-1 1-2 3-3h2 0 1c1 1 2 2 3 2s1 1 2 1l3 1h2l1-1h1c0 3 0 3 3 5h1v-3h1c0 2 1 2 3 3h-2c1 1 0 1 1 1s1 0 1 1h-1c-1 0 0 0-1 1-1 0-2 0-3 1v-1h-4l-1-2s-1 0-2-1h-3c-1 0-2 0-2 1l-5-3-4-3z" class="D"></path><path d="M414 247h1l1 1-1 2s-1 0-2-1l1-2z" class="O"></path><path d="M410 249c0-1-1-2-1-2l1-1c1 0 1 1 2 2l2-1-1 2h-3z" class="f"></path><path d="M446 247c5 2 10 5 15 8h0v1l-1 1c1 0 1 0 2 1h0c-1 2-3 3-4 3l-2 1c2 2 4 4 5 6v2c0 1-1 1-1 2 0 2 0-1 0 2v1c-2-1-3-5-4-6s-1-2-2-3h0c0-1-1-2-1-3-2 1-3 1-4 2h-1v-1c-1-1-1-1-2-1s-4-2-5-3c-3-2-2-1-4-1 0-1 0-1 1-2-1 0-1 0-1-1h2c1 0 2-3 4-4h0l2-2c1 0 1 1 2 2l1-2c-1-1-2-1-2-3z" class="W"></path><path d="M438 257c-1 0-1 0-1-1h2c1 0 2-3 4-4h0l2-2c1 0 1 1 2 2l1-2c0 1 1 2 1 4h1c0 2 1 3 2 5v1c-3 1-6-1-8-2-1 0-1 0-2-1-1 0-1 0-2-1 0 1 0 2 1 2-1 0-2-1-3-1z" class="H"></path><path d="M437 261h1 1c1 1 2 1 3 2v2h0l2-1c1 1 1 1 1 3-1 1-1 2-3 3l1 1-2 1c0 1 0 2 1 3v1h0-3c0 2 1 2 2 3-1 0-2 1-3 1h-1c-2 0-2 2-3 2l-1 1h0-1l1 1-3 3-1-2c-1-1-2-4-4-4l-2-2h0l2-2c1 0 2-1 3-2h1 0l-1-2 3-3-4-4h2v-3h1l-1-2h1l2 1c1 0 3 0 5-1z" class="O"></path><path d="M437 275v-3c0-1 0-1 1-1 1-1 2-1 3 0v1c-1 1-2 2-4 3z" class="H"></path><path d="M429 266l2-2h1l1 2c-1 2 1 2-1 4v1h0l-1-1-4-4h2z" class="W"></path><path d="M437 275c2-1 3-2 4-3 0 1 0 2 1 3v1h0-3c0 2 1 2 2 3-1 0-2 1-3 1h-1c0-1 0-1-1-2 0-1 0-1-1-2 1-1 1-1 2-1z" class="f"></path><path d="M425 277l1 1c1-1 2-1 3-2h0l2 2v-1h3 0l-3 1c-1 0-1 1-2 1v1 1 2 2c-1-1-2-4-4-4l-2-2h0l2-2z" class="o"></path><path d="M524 162c2 2 4 3 6 5 4 3 6 8 7 13 0 2 1 4-1 6v1l-1 1c-1 1-1 1-1 2v6l1 7c1-1 3-2 4-2v1l-1 1c-1 1-2 1-3 3-1 5 0 12 0 17v39 113h1c-1 4-1 8-1 12v22 16c0 4 0 8 1 11l-3-2-4-1-5-2-12-2h0l-11 1-4 1h0c-4 1-7 2-10 3h-1l-2 1h-1l-3 1v-6l-5 3v-1c-1-1-1-2-1-2l-2-2h0 1 1l-1-2v-3c-1-3-1-7-1-11 1-5 0-12 0-18v-16-1c1-1 0-4 0-5v-15c0-2 0-4 1-6 1-1 1-2 1-3l1-1 1-1v1c1 1 2 1 3 2l1-1v-1-12-27-16l1 1c0 1-1 3 0 4l1 1c1 0 2-2 3-4v-8c1 1 1 1 1 3l1-1v-14-20l-1 6-1-1v-31h1c-1-2-1-5-1-8 0-4 0-9 1-14h0l-2-2-1-1c-2-1-3-1-4-3h0 1c2 1 4 2 6 4 0-3 1-7 1-9 0-3 0-5-1-7-1-1-1-2-2-3 4 0 9 2 13 1l1 1 2-1c2 0 4 1 6 0h2 3c1-1 1-7 1-9v-1-14c1 0 2 1 3 0 2 0 4 2 5 2h3l1-2z" class="n"></path><path d="M529 397c-1-5 1-11 0-17v-3h3v35l1 21c-1 0-1-1-2-1v-6l-2-1 1-1c-1-2-1-4-1-6v-10c-1-2 0-4-1-5h0c1-2 1-4 1-6z" class="Z"></path><path d="M531 426v-29c0-2-1-5 1-6h0v21h0l1 21c-1 0-1-1-2-1v-6z" class="j"></path><path d="M526 392c1 1 1 2 1 4 1 1 1 1 2 1 0 2 0 4-1 6h0c1 1 0 3 1 5v10c0 2 0 4 1 6l-1 1 2 1v6c1 0 1 1 2 1v1l-4-1-5-2h1l-2-2v-27c0-2-2-3-3-4h0 1l1-1h0c2 2 2 3 4 3v-2-2l-1-1c-1-1 1-2 1-3z" class="g"></path><path d="M529 425l2 1v6c-1 0-1-1-2-1h0-1c1-2 1-3 1-5v-1z" class="P"></path><path d="M522 397h0c2 2 2 3 4 3v1c0 1 0 1 1 2v1c-1 4 0 7 0 11-1 1 0 2 0 4v1 3 1l-2-1v-2l-1-1 1-1v-2-3c0-1-1-1-1-1 0-3 0-9-1-11 0-2-2-3-3-4h0 1l1-1z" class="Z"></path><path d="M528 307h1 4l-1 69h-3v-3-2-2-1l-1-2c2-2 0-4 1-6h0l-1-2 1-1v-10c0-4 0-9 1-13-1 0-1-1-1-1l-1-1c0-1 0-1 1-2h1 0v-5c-1-2-1-3-1-5v-10l-1-3z" class="S"></path><path d="M532 246l1 1v60h-4-1c1-2 0-4 0-6 1-2 0-5 0-7l1-3c0-1-1-2-1-3l-2 1h0c-2 1-3 1-5 1l-1 1h-1l1-2-1-1c-1-1-1-1-1-2l1-1-1-1-1-1-1-4c-1-1 0-3-1-5v-3-5c0-2 0-2 1-4l2-1c2 0 3-1 5-2s4-3 6-5l3-8z" class="L"></path><path d="M523 259c2-1 4-3 6-5 0 3 0 3-1 5-1 1-3 1-4 2l-1-2z" class="j"></path><path d="M518 261c2 0 3-1 5-2l1 2 2 1h1c0 1 1 2 0 3v-1h-2-1l-3 2-1 1h-1c0-2 1-3 1-4l-2-2z" class="P"></path><path d="M521 266v-1-1c0-1 1-2 2-2l1 2-3 2z" class="S"></path><path d="M516 262l2-1 2 2c0 1-1 2-1 4h1l1-1 3-2h1 2v1 2c1 1 1 1 1 2v2 7s0 1 1 2l-1 3v1 1c-1 0-1 1-1 1-1 1-1 1-2 0h0l1 3c-2 1-3 1-5 1l-1 1h-1l1-2-1-1c-1-1-1-1-1-2l1-1-1-1-1-1-1-4c-1-1 0-3-1-5v-3-5c0-2 0-2 1-4z" class="P"></path><path d="M517 283c0-4-1-8 0-12v5c1 2 1 3 1 5 1 1 2 1 3 1v3l-2 3c-1-1-1-1-1-2l1-1-1-1-1-1z" class="T"></path><path d="M516 262l2-1 2 2c0 1-1 2-1 4h1v1h0v1c-1 1-2 1-3 2-1 4 0 8 0 12l-1-4c-1-1 0-3-1-5v-3-5c0-2 0-2 1-4z" class="K"></path><path d="M516 262l2-1 2 2c0 1-1 2-1 4h1v1l-1 1-1-1-2-6z" class="T"></path><path d="M521 282l2-2 2 1v1h1l2-2h1l-1 3v1 1c-1 0-1 1-1 1-1 1-1 1-2 0h0l1 3c-2 1-3 1-5 1l-1 1h-1l1-2-1-1 2-3v-3z" class="S"></path><path d="M521 285v1 2h1v-1h1c1 0 2 0 2-1l1 3c-2 1-3 1-5 1l-1 1h-1l1-2-1-1 2-3z" class="P"></path><path d="M524 264h1 2v1 2c1 1 1 1 1 2v2 7s0 1 1 2h-1l-2 2h-1v-1c0-1 0-2 1-3v-4c-1-1-1-1-2-1l-3 3v2h0c-1-1-1-4-1-6v-3h0v-1h0v-1l1-1 3-2z" class="j"></path><path d="M524 264h1 2v1 2 3-1c-1-1-2-2-4-2l-3 1h0v-1l1-1 3-2z" class="b"></path><path d="M527 335l3-1c-1 4-1 9-1 13v10l-1 1 1 2h0c-1 2 1 4-1 6l1 2v1 2 2 3h3v1h-3v3c1 6-1 12 0 17-1 0-1 0-2-1 0-2 0-3-1-4 0 1-2 2-1 3l1 1v2 2c-2 0-2-1-4-3h0l-1 1h-1 0l-3-1c-1-1-1-2-2-2v-16c1-2 1-2 2-2l-1-1c-1-3-1-7 0-11 0-2-1-4 0-6 0-1 1-2 1-3l-1-1 4-5 4-6v-2c1-2 2-4 3-7z" class="T"></path><path d="M522 366h4c0 3 1 7 0 10h-4v-10zm0 31v-4h1c-2-3-1-8-1-12v-4c2 0 3 0 4 1v7c0 2 1 4 0 6v1c0 1-2 2-1 3l1 1v2 2c-2 0-2-1-4-3h0z" class="L"></path><path d="M525 343c1 0 1-1 2 0v5c-1 3 0 7-1 10h0l1 3v3c0 1 0 1-1 2h-4v-1c-1-2-1-7 0-9h0l-1-2c1-2 1-2 1-4 0-1 0-2 1-3 0-1 1-2 1-3l1-1z" class="j"></path><path d="M527 361l-2 1v2h0l-1 1h-1v-8h2l1 1 1 3z" class="X"></path><path d="M524 344l1-1-1 1c0 1-1 2-1 3-1 1-1 2-1 3 0 2 0 2-1 4l1 2h0c-1 2-1 7 0 9v1 10c-1 1-1 1-2 1-1 1 0 17-1 18 0 1-1 1-2 2-1-1-1-2-2-2v-16c1-2 1-2 2-2l-1-1c-1-3-1-7 0-11 0-2-1-4 0-6 0-1 1-2 1-3l-1-1 4-5 4-6z" class="U"></path><path d="M524 344l1-1-1 1c0 1-1 2-1 3-1 1-1 2-1 3 0 2 0 2-1 4l1 2h0c-1 2-1 7 0 9v1 10c-1 0-3 0-4-1-1-5-1-12 0-18 0-2 0-3 1-5 1 0 1-1 1-2l4-6z" class="a"></path><path d="M518 284l1 1-1 1c0 1 0 1 1 2l1 1-1 2h1l1-1c2 0 3 0 5-1h0l2-1c0 1 1 2 1 3l-1 3c0 2 1 5 0 7 0 2 1 4 0 6l1 3v10c0 2 0 3 1 5v5h0-1c-1 1-1 1-1 2l1 1s0 1 1 1l-3 1c-1 3-2 5-3 7v2l-4 6-4 5 1 1c0 1-1 2-1 3-1 2 0 4 0 6-1 4-1 8 0 11l-1-1-1-13c1-1 1-2 1-3v-2l-2 2h0v3c0 2-1 6-1 8v-60-5c1-2 0-3 1-5 0-3-1-5 1-7l1-1 1-1 2-7z" class="U"></path><path d="M518 302l-1-1 1-1c1 1 2 2 2 3s1 1 1 2c2 3 2 4 3 7v3h0c1 1 1 2 1 3v3h0l-1 1h-1c-1 0-1 0-3-1l-1-10v-5-1l-1 1v-4z" class="S"></path><path d="M521 305c2 3 2 4 3 7v3h0c1 1 1 2 1 3l-4-2v-1c0-2-1-4-1-6s0-3 1-4z" class="G"></path><path d="M518 302v4l1-1v1 5l1 10c2 1 2 1 3 1-1 1-3 2-3 4l1 1s0 1-1 1h0c-1 1-2 1-2 3v1c0 2-1 3 0 5 1 0 1 1 1 1v3c0 1 1 2 1 3v3h-1l-1-1h-1l2 2c0 2-2 4-3 5l-1-1c0-1 0-4 1-5h0c-1-2-1-9 0-11h0c-1-2-1-4-1-6l1-21v-1c0-2 1-4 2-6z" class="J"></path><path d="M518 306l1-1v1 5l1 10c2 1 2 1 3 1-1 1-3 2-3 4l1 1s0 1-1 1h0l-1-1-1-3c-1-6 0-12-1-18h1z" class="V"></path><path d="M518 284l1 1-1 1c0 1 0 1 1 2l1 1-1 2h1l1-1c2 0 3 0 5-1h0l2-1c0 1 1 2 1 3l-1 3c0 2 1 5 0 7 0 2 1 4 0 6l1 3c-1-1-1-1-2 0v1 1h0v3l-1-1h0c0-4-1-5-3-9-1-1-1-2-3-4-1-2-3-4-4-6 2 0 5 4 6 6h1c-1-2-4-4-5-6 3 0 3 3 5 3v-2c-1-1-2-1-4-2h0c-2-1-3-1-4-2l1-1 2-7z" class="Z"></path><path d="M518 284l1 1-1 1c0 1 0 1 1 2l1 1-1 2h1l1 1h3 0c0 2 1 2 2 4-1-1-2-1-3-2v-1h-2c-1 0-1 1-2 1h0c-2-1-3-1-4-2l1-1 2-7z" class="V"></path><path d="M512 310c0-2 0-3 1-4 1-3 1-8 1-11l1-1v1h0c-1 2 0 4 0 5v1 18 11c0 2 0 4 1 6h0c-1 2-1 9 0 11h0c-1 1-1 4-1 5l1 1-1 2h1l1 1c0 1-1 2-1 3-1 2 0 4 0 6-1 4-1 8 0 11l-1-1-1-13c1-1 1-2 1-3v-2l-2 2h0v3c0 2-1 6-1 8v-60z" class="k"></path><path d="M526 314l1 1v-3h0v-1-1c1-1 1-1 2 0v10c0 2 0 3 1 5v5h0-1c-1 1-1 1-1 2l1 1s0 1 1 1l-3 1c-1 3-2 5-3 7v2l-4 6-4 5h-1l1-2c1-1 3-3 3-5l-2-2h1l1 1h1v-3c0-1-1-2-1-3v-3s0-1-1-1c-1-2 0-3 0-5v-1c0-2 1-2 2-3h0c1 0 1-1 1-1l-1-1c0-2 2-3 3-4h1v1l1 1 1-1v-9z" class="P"></path><path d="M520 328h3v2h-1l-1-1-1 2c1 1 1 1 1 2-1 2 1 4-1 6h2v1c-1 1-2 1-3 1v-3s0-1-1-1c-1-2 0-3 0-5v-1c0-2 1-2 2-3h0z" class="K"></path><path d="M520 347c1-1 0-1 1-1 0-1 1-2 2-3h0v-1c3-5 2-11 2-16l1-1 1 1v9c-1 3-2 5-3 7v2l-4 6-4 5h-1l1-2c1-1 3-3 3-5l-2-2h1l1 1h1z" class="X"></path><path d="M512 370c0-2 1-6 1-8v-3h0l2-2v2c0 1 0 2-1 3l1 13 1 1 1 1c-1 0-1 0-2 2v16c1 0 1 1 2 2l3 1c1 1 3 2 3 4v27l2 2h-1l-12-2h0l-11 1v-1h-1v-2c-1-1-1-2-2-3l-1-4v-16l-2-2 2-3h0l1 1c1 0 2-1 3-1l2-1 2-2s1-1 1-2l1-2c1-4 0-10 0-15 2-1 3-1 5-1h0v-6z" class="B"></path><path d="M506 394l1-2v8 2c-1 0-1-1-1-2h-1l-1 1c0-1 0-2-1-3l2-2s1-1 1-2z" class="l"></path><path d="M507 400c1 4 1 7 1 11v17h3l1 1-11 1v-1h2c2-2 1-7 1-10h0v-3c0-3-1-6 0-8 0-1 0-1 1-1h0c-1-1 0-1-1-2v-1h1v-4h1c0 1 0 2 1 2v-2z" class="e"></path><path d="M504 419h2v2c1 2 0 4 0 6v2h1l1-1h3l1 1-11 1v-1h2c2-2 1-7 1-10h0z" class="p"></path><path d="M503 398c1 1 1 2 1 3l1-1v4h-1v1c1 1 0 1 1 2h0c-1 0-1 0-1 1-1 2 0 5 0 8v3h0c0 3 1 8-1 10h-2-1v-2c-1-1-1-2-2-3l-1-4v-16l-2-2 2-3h0l1 1c1 0 2-1 3-1l2-1z" class="B"></path><path d="M500 406h0v14h3l1-4v3h0c0 3 1 8-1 10h-2-1v-2c0-7-1-15 0-21z" class="C"></path><path d="M503 398c1 1 1 2 1 3-1 1-4 2-4 5h0c-1 6 0 14 0 21-1-1-1-2-2-3l-1-4v-16l-2-2 2-3h0l1 1c1 0 2-1 3-1l2-1z" class="R"></path><path d="M497 399h0l1 1c1 0 2-1 3-1-2 2-3 3-4 5l-2-2 2-3z" class="C"></path><path d="M512 370c0-2 1-6 1-8v-3h0l2-2v2c0 1 0 2-1 3l1 13 1 1 1 1c-1 0-1 0-2 2v16c1 0 1 1 2 2l3 1c1 1 3 2 3 4v27l2 2h-1l-12-2h0l-1-1c2-9 1-20 1-30v-22h0v-6z" class="K"></path><defs><linearGradient id="T" x1="509.309" y1="414.899" x2="516.576" y2="413.129" xlink:href="#B"><stop offset="0" stop-color="#131011"></stop><stop offset="1" stop-color="#212119"></stop></linearGradient></defs><path fill="url(#T)" d="M512 398l1 2c1 0 2 0 3 1l-1 1v3c1 1 1 1 0 2 0 3 1 7 2 10 0 4-2 9-1 12h-4 0l-1-1c2-9 1-20 1-30z"></path><path d="M512 370c0-2 1-6 1-8v-3h0l2-2v2c0 1 0 2-1 3l1 13 1 1 1 1c-1 0-1 0-2 2v16c1 0 1 1 2 2l3 1c1 1 3 2 3 4v27h-1l-1-25c-1-2-3-2-5-3-1-1-2-1-3-1l-1-2v-22h0v-6z" class="l"></path><path d="M487 254c0-2-1-7 0-9 1 4 3 7 6 10l2 3 2 1 2 1c1 1 3 2 5 2h1 2 5l1 1v5-1c0 2 0 2 1 4h1v3c1 2 0 4 1 5l1 4 1 1-2 7-1 1-1 1c-2 2-1 4-1 7-1 2 0 3-1 5v5 60 6h0c-2 0-3 0-5 1 0 5 1 11 0 15l-1 2c-1-5 0-11-1-17h-1v-1h2v-3-2c0-3 0-8-1-11l-1-1c-2-4-4-6-6-9l-5-5c-1-1-1-1-1-3v-3h2l-1-4c0-6 0-12 1-18 0-2 1-4 1-6 0-1-1-1-1-2s-1-1-1-2c-1 0-1 0-2 1v1c-1 0-2 0-2 1-1-1-2-2-2-3v-3-14-2-14-20z" class="K"></path><path d="M494 339l1 3c1 1 2 2 2 4v1c-1 0-2-1-2-1-1 0-1 0-2-1s-1-1-1-3v-3h2z" class="M"></path><path d="M494 339l1 3h-3v-3h2z" class="C"></path><path d="M500 344h4 2v3 2 1 9l-4-7c-1-1-1-1-1-2 0 1 1 1 2 2v-1c-2-3-2-5-3-7z" class="E"></path><path d="M496 327v-5c1-7 3-13 7-19 1-1 1-2 3-3h0v2 1l-2 1 2 1v5c0 1-1 1 0 2v3 3c-1 1-1 2-1 3-1 0-2 0-3-1l-1 1c1 1 2 0 3 1h1v2 1c-2 1-2 1-4 1l-1-2h-1l-2 1h1v2h-2z" class="D"></path><path d="M506 318v9l-1 1h1v7c0 2 1 6 0 7l-2 1v1h-4v-1h0l-2-4c-1-4-2-7-2-12h2v-2h-1l2-1h1l1 2c2 0 2 0 4-1v-1-2h-1c-1-1-2 0-3-1l1-1c1 1 2 1 3 1 0-1 0-2 1-3z" class="B"></path><path d="M498 339l3-2v1l-1 2 2 1h2v-1c0-1-1-2-1-3-1 0-2 0-2-1s0-2 1-2l1 1h3c0 2 1 6 0 7l-2 1v1h-4v-1h0l-2-4z" class="I"></path><path d="M511 288c2 0 2 0 4 1l1 2-1 1-1 1c-2 2-1 4-1 7-1 2 0 3-1 5v5 60 6h-1-4l1-87c0-1 1-1 2-1h1z" class="B"></path><path d="M511 288c2 0 2 0 4 1l1 2-1 1-1 1c-2 2-1 4-1 7-1 2 0 3-1 5v-12-3c0-1-1-1-1-2z" class="b"></path><path d="M487 254c0-2-1-7 0-9 1 4 3 7 6 10l2 3 2 1 2 1c1 1 3 2 5 2h1 2 5l1 1v5-1c0 2 0 2 1 4h1v3c1 2 0 4 1 5l1 4 1 1-2 7-1-2c-2-1-2-1-4-1h-1c-1 0-2 0-2 1h-2c0 1 1 4 1 5-1 1-3 1-3 3h2c-1 1-1 2-2 2-2 1-4 6-6 7-1 1 0 1-1 1l-2 4c0-1-1-1-1-2s-1-1-1-2c-1 0-1 0-2 1v1c-1 0-2 0-2 1-1-1-2-2-2-3v-3-14-2-14-20z" class="F"></path><path d="M492 293c1 0 2 0 3 1-1 1-1 2-2 3l-1 1v-1c-1-1-1-3 0-4zm-3 8h1v1 1c1 0 2 1 3 2v1c-1 0-2 1-3 1h-1l-1-1 1-2 1-1-1-1v-1z" class="B"></path><path d="M487 290c1 3 1 3 3 5-2 2-2 2-2 4 0 0 1 1 1 2v1l1 1-1 1h-2v-14z" class="W"></path><path d="M505 262h2v12 8 4h0c1 2 2 2 3 2-1 0-2 0-2 1h-2c0 1 1 4 1 5-1 1-3 1-3 3h2c-1 1-1 2-2 2-2 1-4 6-6 7-1 1 0 1-1 1 0 0-1-1-1-2 1-1 2-1 3-3l-1-1c0 1-1 1-2 1l5-3c-1-1-1-1-1-2l-1 1c0 1-2 2-3 3v-1c0-1 0-1 1-1 1-1 1-1 1-2h-1l4-4v-2c1 0 2-1 2-2l1-1c0-1 0-2 1-3 0-1 0-2 1-3v-3h-1c1-3 1-6 1-9s-1-5-1-8z" class="S"></path><path d="M512 262l1 1v5-1c0 2 0 2 1 4h1v3c1 2 0 4 1 5l1 4 1 1-2 7-1-2c-2-1-2-1-4-1h-1c-1 0-2 0-3-2h0v-4-8-12h5z" class="Q"></path><path d="M512 262l1 1v5-1c0 2 0 2 1 4h1v3c1 2 0 4 1 5l1 4 1 1-2 7-1-2c-2-1-2-1-4-1h-1c-1 0-2 0-3-2h0 3c1 1 2 0 3 0v-2c-2-7 0-15-1-22z" class="J"></path><path d="M487 254c0-2-1-7 0-9 1 4 3 7 6 10l2 3 2 1 2 1c1 1 3 2 5 2h1c0 3 1 5 1 8s0 6-1 9h1v3c-1 1-1 2-1 3-1 1-1 2-1 3l-1 1c0 1-1 2-2 2v2l-3 1h-1-1c-1-1-1-1-1-3l-1 2c0-1-1-1-1-1 1-1 1-1 1-2 0 1-1 1-2 1l-2-2h-1v-3-1-1l2 1c0-1 0-1 1-1v1 2l1-1c1-1 1-1 1-2l-1-1v-3l-2-1h-2s-1 0-1-1l1-1v-3h-2v-20z" class="f"></path><path d="M504 279c1 1 1 1 2 3-1 1-1 2-1 3-1 1-1 2-1 3l-1 1v-1l-1-1c0-2 0-3 1-4s1-3 1-4z" class="Q"></path><path d="M489 274v-2c1-1 1-1 2-1 0 2 0 3 1 5v2l-1 1h-2s-1 0-1-1l1-1v-3z" class="H"></path><path d="M504 272c0-1 1-1 2-2 0 3 0 6-1 9h1v3c-1-2-1-2-2-3-2-1-2-3-3-3h-1-1c1-1 1-2 2-3v1l2-2h1z" class="W"></path><path d="M503 272h1l1 2-2 2-2-2 2-2zm-10-17l2 3 2 1c-1 2-1 1-3 3l1 1h-1c-1 0-1 0-2-1h-1l-1 1c0 1 0 1-1 2 0-2-1-4 0-5s2-1 2-2 1-2 2-3z" class="H"></path><path d="M495 258l2 1c-1 2-1 1-3 3l-2-2c1-1 2-2 3-2z" class="F"></path><path d="M499 260c1 1 3 2 5 2h1c0 3 1 5 1 8-1 1-2 1-2 2h-1v-4l1-1c-2-1-3 0-4-1-2-2-1-4-1-6z" class="Q"></path><path d="M485 286c1 1 1 1 1 3l1-1v2 14 3c0 1 1 2 2 3 0-1 1-1 2-1v-1c1-1 1-1 2-1 0 1 1 1 1 2s1 1 1 2c0 2-1 4-1 6-1 6-1 12-1 18l1 4h-2v3c0 2 0 2 1 3l5 5c2 3 4 5 6 9l1 1c1 3 1 8 1 11v2 3h-2v1h1c1 6 0 12 1 17 0 1-1 2-1 2l-2 2-2 1c-1 0-2 1-3 1l-1-1h0l-2 3 2 2v16l1 4c1 1 1 2 2 3v2h1v1l-4 1h0c-4 1-7 2-10 3h-1l-2 1h-1l-3 1v-6l-5 3v-1c-1-1-1-2-1-2l-2-2h0 1 1l-1-2v-3c-1-3-1-7-1-11 1-5 0-12 0-18v-16-1c1-1 0-4 0-5v-15c0-2 0-4 1-6 1-1 1-2 1-3l1-1 1-1v1c1 1 2 1 3 2l1-1v-1-12-27-16l1 1c0 1-1 3 0 4l1 1c1 0 2-2 3-4v-8z" class="Y"></path><path d="M480 427l1 2h1c1 1 1 1 1 2h1c0-2 0-2-1-4l2-1c0 2-1 6 1 8l-2 1h-1l-3 1v-6-3z" class="B"></path><path d="M485 398c2 6 0 13 1 19v7-7h1v15 2h-1c-2-2-1-6-1-8v-6-22z" class="a"></path><path d="M485 374c1-1 1-23 1-26v-1l1 10v5 14 1 40h-1v7-7c-1-6 1-13-1-19 0-6 1-13 0-20v-4z" class="R"></path><path d="M485 286c1 1 1 1 1 3l1-1v2 14 3 15 5 10 11 9l-1-10v1c0 3 0 25-1 26v-8-18-54-8z" class="K"></path><path d="M489 310c0-1 1-1 2-1v-1c1-1 1-1 2-1 0 1 1 1 1 2s1 1 1 2c0 2-1 4-1 6-1 6-1 12-1 18 0 0 0 1-1 1-1 1-2 1-2 2h-1v-2c-1 1-1 1-1 2l-1-1v-10-5-15c0 1 1 2 2 3z" class="Q"></path><path d="M487 307c0 1 1 2 2 3 0 2-1 3-1 4 1 1 1 1 1 2h2v1c-1 0-1 1-2 1h0c-1 1-2 2-2 4v-15z" class="M"></path><path d="M487 327l2-2v1 2h2v-1c1-1 0-2 1-4h0c-1-1-1-1-1-2l1-1-1-1 1-1 2-1c-1 6-1 12-1 18 0 0 0 1-1 1-1 1-2 1-2 2h-1v-2c-1 1-1 1-1 2l-1-1v-10z" class="W"></path><path d="M493 335l1 4h-2v3c0 2 0 2 1 3l5 5c2 3 4 5 6 9l1 1c1 3 1 8 1 11v2 3h-2-5l-1 1h-2-7-2v-1-14-5-9-11l1 1c0-1 0-1 1-2v2h1c0-1 1-1 2-2 1 0 1-1 1-1z" class="f"></path><path d="M487 348h3l-1 2v2h0c-3 3-1 6-2 10v-5-9z" class="M"></path><path d="M493 335l1 4h-2c-1 0-2 1-3 2v5l2 2v1l-1-1h-3v-11l1 1c0-1 0-1 1-2v2h1c0-1 1-1 2-2 1 0 1-1 1-1z" class="o"></path><path d="M497 355l-1-2c1-1 1-2 2-3 2 3 4 5 6 9l1 1c1 3 1 8 1 11v2 3h-2-5l-1 1h-2-7-2v-1h10c0-1-1-1-2-1v-1h2 1c-1 0-1-1-1-1v-2-16z" class="C"></path><path d="M499 372c1-1 1-2 3-2 1 1 2 1 4 1v2l-3-1c-2 0-2 1-4 0z" class="G"></path><path d="M497 355c0 1 1 2 1 2l1 3c1 3 1 5 1 8-1 2-1 2-3 3v-16z" class="F"></path><path d="M497 376l1-1c0-1 1-2 1-3 2 1 2 0 4 0l3 1v3h-2-5l-1 1h-2-7-2v-1h10z" class="E"></path><path d="M474 348l1-1 1-1v1c1 1 2 1 3 2l1 78v3l-5 3v-1c-1-1-1-2-1-2l-2-2h0 1 1l-1-2v-3c-1-3-1-7-1-11 1-5 0-12 0-18v-16-1c1-1 0-4 0-5v-15c0-2 0-4 1-6 1-1 1-2 1-3z" class="k"></path><path d="M498 377l1-1h5v1h1c1 6 0 12 1 17 0 1-1 2-1 2l-2 2-2 1c-1 0-2 1-3 1l-1-1h0l-2 3 2 2v16l1 4c1 1 1 2 2 3v2h1v1l-4 1h0c-4 1-7 2-10 3v-2-15-40h2 7 2z" class="Q"></path><path d="M496 415c1 2 1 3 1 5v1h-3l-1-1c1-1 1-2 2-2l1 1v-4z" class="D"></path><path d="M487 432c1 0 0-1 1-2 0-1 1-2 1-3h1v-3-1h0 1c-1 2-1 3 0 5v3c-1 1-2 1-4 1z" class="B"></path><path d="M495 402l2 2v16h0c0-2 0-3-1-5h-1v-1l1-1h0v-7l-1 1c-1-2 0-3 0-5z" class="E"></path><path d="M497 420h0l1 4c1 1 1 2 2 3v2h1v1l-4 1h0c-4 1-7 2-10 3v-2h0c2 0 3 0 4-1v-3c0-2 0-4 2-6l1-1h3v-1z" class="F"></path><path d="M498 424c1 1 1 2 2 3v2h1v1l-4 1c1-2 0-5 1-7z" class="k"></path><path d="M498 377l1-1h5v1h1c1 6 0 12 1 17 0 1-1 2-1 2l-2 2-2 1c-1 0-2 1-3 1l-1-1h0c0-1 0-1-1-1l-1-2v-2-2-1-3h1c-1-2-1-4-1-6 0-1 1-1 1-2h1v-1h-2v-2h-6 7 2z" class="B"></path><path d="M496 377h2 4v2c-1 2-1 4-1 6 0-1-1-2-1-3l-1-1h2l-1-1-2-1h0l-1-2h-1zm6 12h2v1c-1 2 0 3 0 6h1l-2 2-2 1c-1 0-2 1-3 1l-1-1c1-1 4-4 4-6 1-2 0-2 1-4z" class="G"></path><path d="M498 377l1-1h5v1h1c1 6 0 12 1 17 0 1-1 2-1 2h-1c0-3-1-4 0-6v-1h-2c0-1 0 0-1-1l-1 1c-1-1 0-3 1-4 0-2 0-4 1-6v-2h-4z" class="I"></path><path d="M524 162c2 2 4 3 6 5 4 3 6 8 7 13 0 2 1 4-1 6v1l-1 1c-1 1-1 1-1 2v6c-1-2-1-5-1-7h0v6l-1 5 1 27c0 5 0 11-1 15 0 2 0 3-1 4h1l-3 8c-2 2-4 4-6 5s-3 2-5 2l-2 1c-1 2-1 2-1 4v5h-1c-1-2-1-2-1-4v1-5l-1-1h-5-2-1c-2 0-4-1-5-2l-2-1-2-1-2-3c-3-3-5-6-6-10-1 2 0 7 0 9l-1 6-1-1v-31h1c-1-2-1-5-1-8 0-4 0-9 1-14h0l-2-2-1-1c-2-1-3-1-4-3h0 1c2 1 4 2 6 4 0-3 1-7 1-9 0-3 0-5-1-7-1-1-1-2-2-3 4 0 9 2 13 1l1 1 2-1c2 0 4 1 6 0h2 3c1-1 1-7 1-9v-1-14c1 0 2 1 3 0 2 0 4 2 5 2h3l1-2z" class="k"></path><path d="M519 257l-1 1-1-1v-3c-1-1-1-1-1-2l1-1c-1-2-1-5-1-7v-3c1 1 1 3 1 5v3h1l2-1 1 1h2l1 1 1-1v3 1c-1 1-2 2-4 3l-2 1z" class="a"></path><path d="M521 251v5l-2 1c-1-2-1-3 0-5l2-1z" class="c"></path><path d="M518 249l2-1 1 1h2l1 1 1-1v3 1c-1 1-2 2-4 3v-5h-1l-2-2z" class="g"></path><path d="M506 191h1l1 1c0 1 0 2 1 2 1 1 1 1 2 1h0 1v1l-2 1s1 0 1 1c1 1 1 6 1 7v21 11 1c-2-9 0-18-1-26 0-1 0-2-1-3l1-1c0-2 0-2-1-3v-2-2l-1 1v4 3l-1 1-1-1c-1-3 0-9 0-11 0-3-1-4-1-7zm1 51c2 0 4 1 5 2v7c0 2 0 8-1 9-2 0-2 0-3-1l-1-1v-12c0-2-1-3 0-4z" class="Q"></path><path d="M517 240h0c-1-2-2-2-2-4 0-1 1-1 1-2v-18l-1-15c0-2 1-6 0-8v-1-3c0-1 0-1 1-2h1l-1 1c0 1 1 2 1 3-1 3 0 6-1 9l1 2c1 2 0 8 1 11v1c0 1 1 1 2 1v1l-2 1c-1 3 0 7-1 11 1 1 1 1 3 1h3 1l1 1h-1 0-5c0 2 1 3 2 4v1l-1 1-1 1 1 1c-1 1-1 1-2 1l-1 1z" class="J"></path><path d="M520 238c-2-1-2-1-3-1 0-2 1-4 1-6l1-1c0 2 1 3 2 4v1l-1 1-1 1 1 1z" class="c"></path><path d="M508 210l1-1v-3-4l1-1v2 2c1 1 1 1 1 3l-1 1c1 1 1 2 1 3 1 8-1 17 1 26l-1 2c-1 1-2 0-4 0 1-7 0-16 0-23l2-2v-3-1l-1-1z" class="f"></path><path d="M517 187l1 1c0 1 0 2 1 4h0c-1 3 0 5 0 7h0c0 1-1 1-1 2v1c0 1 1 1 1 3v2c1 2 1 5 1 7 1 1 4 0 4 1v3c-1-1-1-1-1-2-1 1-1 2-1 4 0 1 1 1 1 2h1 2c-1 1-1 2-1 3v3h-1v1h-1-3c-2 0-2 0-3-1 1-4 0-8 1-11l2-1v-1c-1 0-2 0-2-1v-1c-1-3 0-9-1-11l-1-2c1-3 0-6 1-9 0-1-1-2-1-3l1-1z" class="K"></path><path d="M520 229c-1-1 0-1 0-2 1-2 0-6 0-8s0-2 1-4l2 1c-1 1-1 2-1 4 0 1 1 1 1 2h1 2c-1 1-1 2-1 3v3h-1v1h-1-3z" class="c"></path><path d="M523 229c0-1 0-3 1-4h1v3h-1v1h-1z" class="P"></path><path d="M518 188c2 0 4-1 6-1 1 0 5 0 6 1 1 0 1 1 2 2l1 5-1 5v11l-1 1h-1-3v1h-3l-1 1h5c-1 1-1 1-2 1h-2c0-1-3 0-4-1 0-2 0-5-1-7v-2c0-2-1-2-1-3v-1c0-1 1-1 1-2h0c0-2-1-4 0-7h0c-1-2-1-3-1-4z" class="N"></path><path d="M523 202h2c1 1 1 1 1 2h2v-2l1-1 1 1c-1 1-1 2-1 4h-1c-1 0-2 0-3 1h-2c-1-1-1-1-1-2l1-1 1 1 1-1-2-2z" class="P"></path><path d="M525 207c1-1 2-1 3-1v1h2c1 1 1 2 1 4h-6v-1-1c0-1 1-1 0-2h0z" class="j"></path><path d="M523 214h-1c0-1 0-1 1-1l1-1h0l-2-1v-2c0-2-1-4 0-6l1 1-1 1c0 1 0 1 1 2h2 0c1 1 0 1 0 2v1 1h6v1h-1-3v1h-3l-1 1z" class="g"></path><path d="M518 188c2 0 4-1 6-1l-1 2 1 2c3 0 3-1 5 2v2c-1 1-2 2-3 2h-3v-2l-1 1 2 3v1l-2-1v1c0 1 0 1 1 2l2 2-1 1-1-1-1-1c-1 2 0 4 0 6v2l2 1h0l-1 1c-1 0-1 0-1 1h1 5c-1 1-1 1-2 1h-2c0-1-3 0-4-1 0-2 0-5-1-7v-2c0-2-1-2-1-3v-1c0-1 1-1 1-2h0c0-2-1-4 0-7h0c-1-2-1-3-1-4z" class="T"></path><path d="M524 162c2 2 4 3 6 5 4 3 6 8 7 13 0 2 1 4-1 6v1l-2-2h0c-4 1-7 1-10 1h-2-6c-1-2-1-5-1-7l1-13h6v-1l-2-1h3l1-2z" class="J"></path><path d="M522 186c-1 0-2-1-3-1l-1-2c1-2 1-2 3-2l1 1v1l2 3h-2z" class="V"></path><path d="M527 179c-1 1-3 2-4 2l-1-1-2-2 1-1-1-1 1-2h-1c1-1 1-1 2-3h-1c-1-1-2-1-2-2 1-1 1-1 1-2h1 3l3 1c1 0 1 1 1 1h0-1c0 1 1 1 1 2 1 2 2 6 1 8v1c0-1-1-1-1-2l-1 1z" class="g"></path><path d="M524 167l3 1c1 0 1 1 1 1h0-1c0 1 1 1 1 2 1 2 2 6 1 8v1c0-1-1-1-1-2l-1-1c-1 0-1 1-2 1v-1c0-1 0-1 1-2l1 1h0c0-2-2-3-3-4h0v-1l1-1c0-1 0-2-1-3h0z" class="S"></path><path d="M524 162c2 2 4 3 6 5 4 3 6 8 7 13 0 2 1 4-1 6v1l-2-2h0c-4 1-7 1-10 1l-2-3v-1c1 0 2 0 3-1l2-2 1-1c0 1 1 1 1 2v-1c1-2 0-6-1-8 0-1-1-1-1-2h1 0s0-1-1-1c0-2-2-3-4-4l1-2z" class="P"></path><path d="M529 180v-1c1-2 0-6-1-8 0-1-1-1-1-2h1 0c4 4 5 7 6 12-1 1-3 4-5 4h0l-1-1h0-1c-2-1-2-1-2-3l2-2 1-1c0 1 1 1 1 2z" class="N"></path><path d="M528 178c0 1 1 1 1 2-1 2-1 2-1 4h0-1c-2-1-2-1-2-3l2-2 1-1z" class="L"></path><path d="M532 200l1 27c0 5 0 11-1 15 0 2 0 3-1 4-1 2-2 6-3 8h-1v-1c0-1 1-2 1-2l-1-1-2 3v-1-3l-1 1-1-1h-2l-1-1-2 1h-1v-3c0-2 0-4-1-5l1-1 1-1c1 0 1 0 2-1l-1-1 1-1 1-1v-1c-1-1-2-2-2-4h5 0 1l-1-1v-1h1v-3c0-1 0-2 1-3h-2-1c0-1-1-1-1-2 0-2 0-3 1-4 0 1 0 1 1 2v-3h2c1 0 1 0 2-1h-5l1-1h3v-1h3 1l1-1v-11z" class="P"></path><path d="M519 230h5c-1 1-2 1-3 2v2c-1-1-2-2-2-4z" class="T"></path><path d="M527 222l-1-1c0-2 0-3 1-5 1 1 2 3 3 4l-3 2z" class="j"></path><path d="M524 244l-1 3h1l1-1c1-2 2-5 3-7l1 1c-1 3-2 6-4 8-2-1-4 0-6-2v-1h2c1 0 2-1 3-1z" class="T"></path><path d="M530 220l1 1 1-1v2l-1 1 1 1-1 1v3c0 1-1 0-1 1-1 0-2 0-3 1h0l2 1v1h-3v1l-2-1v-2h1l-1-1v-1h1v-3c1-1 2-1 2-2v-1l3-2z" class="Z"></path><path d="M529 240h2c0-2 0-3 1-5 1 1 0 5 0 7s0 3-1 4c-1 2-2 6-3 8h-1v-1c0-1 1-2 1-2l-1-1-2 3v-1-3-1c2-2 3-5 4-8z" class="S"></path><path d="M520 238c1-1 2-1 4-1 1-1 3-1 4-1l1 1v1l-1-1c-1 0-2 0-2 1l-1 1-2-1v1c1 2 1 2 1 5-1 0-2 1-3 1h-2v1c2 2 4 1 6 2v1l-1 1-1-1h-2l-1-1-2 1h-1v-3c0-2 0-4-1-5l1-1 1-1c1 0 1 0 2-1z" class="c"></path><path d="M484 185c4 0 9 2 13 1l1 1 2-1 1 1h9l1 1c1 1 1 2 1 3-2 0-3-1-5 0h-1c0 3 1 4 1 7 0 2-1 8 0 11l1 1 1 1v1 3l-2 2c0 7 1 16 0 23v2c-1 1 0 2 0 4v12h-1c-1 1-1 1-2 1v3c-2 0-4-1-5-2l-2-1-2-1-2-3c-3-3-5-6-6-10-1 2 0 7 0 9l-1 6-1-1v-31h1c-1-2-1-5-1-8 0-4 0-9 1-14h0l-2-2-1-1c-2-1-3-1-4-3h0 1c2 1 4 2 6 4 0-3 1-7 1-9 0-3 0-5-1-7-1-1-1-2-2-3z" class="M"></path><path d="M502 225l1 1 1 1v1 3h-2-3c-1 0-3 0-5-1 1-1 3 0 5 0-1-1-1-2 0-3h1l2-2z" class="C"></path><path d="M499 215h1c2-1 3-1 5-1 1 2 1 3 1 5v7 6l-2-1v-3-1l-1-1-1-1c-1-1-3-3-3-4l-1-2c-1-1-2-1-3-1v-1h-1-1 0c2-2 3-2 5-2h1z" class="G"></path><path d="M501 216h2 1v1 3 1h-1c-2-2-2-3-2-5z" class="D"></path><path d="M499 215h1c2-1 3-1 5-1 1 2 1 3 1 5v7l-2-2v-2h1v-1l-1-1v-3-1h-1-2c-1 0-2 0-3-1h1z" class="I"></path><path d="M494 202l2 2h0c1-1 1-1 2-1h1v-1l2-2c2 0 3 0 5 1v5 8h-1 0c-2 0-3 0-5 1h-1c0-1-1-2-1-3 1-2 2-3 3-4-2 0-3 2-5 4v1h-1 0l-2-3v-1h0v-1c1-2 1-3 1-6z" class="i"></path><path d="M501 208c1 1 1 1 2 3v1c0 1 0 1 1 2h1 0c-2 0-3 0-5 1h-1c0-1-1-2-1-3 1-2 2-3 3-4z" class="G"></path><path d="M494 202l2 2h0c1-1 1-1 2-1h1v-1l2-2c2 0 3 0 5 1v5c-1 1-2 1-3 1v1h-1l-2-3-2 1c-2 1-3 2-5 3v-1c1-2 1-3 1-6z" class="D"></path><path d="M487 224l1 3h3v-1h1l1 1h-1c-2 2-3 3-3 5v1 1 4c1 0 1 0 3 1v2c1 1 2 2 3 2l1 1 1-1c-1-1-2-2-2-3v-3-1h0l1-1c0 1 1 2 1 2h1l-1-1 1-1v-2h1v-2h3 2l2 1v6 5 1 6c-1 0-2 0-3-1 1-2 1-3 1-4h-1c-1 0-1 0-2 1h0-1c-2 0-3 1-4 0-2 2-3 2-5 2v-1c-3-2-3-5-4-7v-9-7z" class="B"></path><path d="M487 240c1-1 2-1 3-1v3l1 1v4c-3-2-3-5-4-7z" class="G"></path><path d="M500 246l1-1c-1-1 0-1-1-1-1-1-1-2-2-3l2-1h0v2l1 1c1 0 2-1 3-2v-2l2-1v5 1 6c-1 0-2 0-3-1 1-2 1-3 1-4h-1c-1 0-1 0-2 1h0-1z" class="C"></path><path d="M505 192v-3l1-1v1 2h0c0 3 1 4 1 7 0 2-1 8 0 11l1 1 1 1v1 3l-2 2c0 7 1 16 0 23v2c-1 1 0 2 0 4v12h-1c-1 1-1 1-2 1v3c-2 0-4-1-5-2l-2-1-2-1-2-3c-3-3-5-6-6-10-1 2 0 7 0 9l-1 6-1-1v-31h1v5c0-1 1-1 1-2v9c1 2 1 5 4 7v1c2 0 3 0 5-2 1 1 2 0 4 0h1 0c1-1 1-1 2-1h1c0 1 0 2-1 4 1 1 2 1 3 1v-6-1-5-6-6-7c0-2 0-3-1-5h0 1v-8-5l-1-1c1-3 1-6 0-8z" class="a"></path><path d="M507 209l1 1 1 1v1 3l-2 2v-8z" class="F"></path><path d="M500 246h1 0c1-1 1-1 2-1h1c0 1 0 2-1 4 1 1 2 1 3 1v3c0 2 0 3-1 4v1l-3-2c-5-1-8-4-11-8 2 0 3 0 5-2 1 1 2 0 4 0z" class="E"></path><path d="M484 185c4 0 9 2 13 1l1 1 2-1 1 1h9l1 1c1 1 1 2 1 3-2 0-3-1-5 0h-1 0v-2-1l-1 1v3c1 2 1 5 0 8l1 1c-2-1-3-1-5-1l-2 2v1h-1c-1 0-1 0-2 1h0l-2-2c0 3 0 4-1 6v1h0c-1 0-1 0-1 1s-1 2-1 2c-2 0-3-1-4-2 0 5 1 10 0 14v7c0 1-1 1-1 2v-5c-1-2-1-5-1-8 0-4 0-9 1-14h0l-2-2-1-1c-2-1-3-1-4-3h0 1c2 1 4 2 6 4 0-3 1-7 1-9 0-3 0-5-1-7-1-1-1-2-2-3z" class="Y"></path><path d="M487 205c0-6 0-13 1-18h2v9 1h0v4 1c0 1-1 2-3 3z" class="M"></path><path d="M484 185c4 0 9 2 13 1l1 1h-8-2c-1 5-1 12-1 18v5c0 5 1 10 0 14v7c0 1-1 1-1 2v-5c-1-2-1-5-1-8 0-4 0-9 1-14h0l-2-2-1-1c-2-1-3-1-4-3h0 1c2 1 4 2 6 4 0-3 1-7 1-9 0-3 0-5-1-7-1-1-1-2-2-3z" class="U"></path><path d="M501 187h9l1 1c1 1 1 2 1 3-2 0-3-1-5 0h-1 0v-2-1l-1 1v3c1 2 1 5 0 8l1 1c-2-1-3-1-5-1l-2 2v1h-1c-1 0-1 0-2 1h0l-2-2v-2-1c-1-1-2-2-2-4l1-1c0-1 1-1 1-2 1 0 1 0 2-1 1 0 0 0 1-1h4v-3z" class="f"></path><path d="M505 192c1 2 1 5 0 8v-1c-2 0-2-1-3-2v-3h2 1v-2z" class="D"></path></svg>