<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="138 116 739 764"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#bfbcbd}.C{fill:#222121}.D{fill:#312f31}.E{fill:#161515}.F{fill:#b5b2b4}.G{fill:#aeabac}.H{fill:#030303}.I{fill:#dcdada}.J{fill:#908d8f}.K{fill:#7e7c7e}.L{fill:#777476}.M{fill:#e2e1e1}.N{fill:#ccc9cb}.O{fill:#a09d9f}.P{fill:#232122}.Q{fill:#535151}.R{fill:#474446}.S{fill:#3b393b}.T{fill:#1d1b1c}.U{fill:#5c5a5c}.V{fill:#676566}.W{fill:#363436}.X{fill:#9a9799}.Y{fill:#413f40}.Z{fill:#c6c4c4}.a{fill:#a7a4a6}.b{fill:#f0efef}.c{fill:#e8e6e6}</style><path d="M431 602l2 7-4-3c-1 0-2-1-3-2 3 0 3-1 5-2z" class="Q"></path><path d="M604 489l2 3c0 1-1 2-1 3-1 2-2 3-4 4l3-10z" class="D"></path><path d="M548 833c2 1 3 2 4 4v1h0c-2 0-4-1-5-3v-1-1h1z" class="C"></path><path d="M565 343l1 4c1 1 2 1 4 1h0-2c-1 2-1 2-1 3l-1 1 1 3-2 1v-1-12z" class="E"></path><path d="M502 182l-2 13c-1 0-1 0-2-1 1-3 1-7 2-10l1 1h0c0-1 1-2 1-3z" class="M"></path><path d="M584 177l30 1h-2c-3 0-7-1-10 0h-1-1-1l2 1c-5 0-13 1-17-2z" class="N"></path><path d="M543 808c3 1 4 1 6 1h1s1 0 0 1c0 2-1 3-2 4-1-1-1-2-1-2l-1-1c-1-1-1 0-2-1-1 0-1-1-1-2z" class="T"></path><path d="M379 459c1 0 2-1 3 0 0 1-1 1-1 2v2c-2-1-4-1-5-1-3 0-4 1-7 1 2-1 3-2 5-2l5-2z" class="M"></path><path d="M565 498l2 1c-1 3-2 6-2 9-1-3-2-4-3-6l1-3 2-1z" class="E"></path><path d="M650 628c4 3 8 5 13 5h0c3 1 6-1 8-1-3 2-6 3-8 4-2-1-5-3-7-4s-4-2-6-4z" class="X"></path><path d="M377 457l5-3 2 9h0l-1-1-1 1h-1v-2c0-1 1-1 1-2-1-1-2 0-3 0l-2-2z" class="H"></path><path d="M342 378c4 0 7 0 11-3 0-1 0-1 1-2v1l1 2c-1 1-2 2-3 2-3 1-7 2-10 1h-1l1-1z" class="E"></path><path d="M547 751c2 1 5 2 7 4v4l-1 1c-2-1-4-5-5-7h-1v-2z" class="T"></path><path d="M657 392c0-1 0-2 1-4 1 4 3 9 1 13h-2v-1c-1-2-1-5 0-8z" class="H"></path><path d="M478 503l4 2v-1l3 3-1 1v3h-2l-2 1-2-9z" class="S"></path><path d="M482 504l3 3-1 1v3h-2l-1-1c0-2 0-2 1-3v-2-1z" class="C"></path><path d="M429 190c5-2 14-1 19-1l-11 6 1-1v-1-1c-1-1-2-2-3-2h-5-1z" class="N"></path><path d="M341 379h1c3 1 7 0 10-1 1 2 1 3 1 5h-1c-4 0-8-2-12-4h1z" class="I"></path><path d="M434 803l1-1c3-3 6-3 10-3h-1l1 1c-4 2-8 4-12 7h-1c1-1 2-2 2-3v-1z" class="M"></path><path d="M565 355v1l2-1 4 1h0-3c-1 1 0 2 0 4l2 8v1c-1-1-1-3-2-3l-1 1h0c-1-1-1-2-1-3v-1c-1 1-1 2-1 3v-11z" class="R"></path><path d="M569 790c4 0 7 3 9 5 1 2 1 4 2 6-4-2-7-5-10-8h0c0-1 0-2-1-3z" class="E"></path><path d="M329 653h0-3c-4-1-6-5-8-8l6 3c3 0 5 1 8 2l-3 3z" class="F"></path><defs><linearGradient id="A" x1="365.367" y1="492.411" x2="359.783" y2="503.147" xlink:href="#B"><stop offset="0" stop-color="#757173"></stop><stop offset="1" stop-color="#8c8a8b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M366 491v2h1v2c-2 2-4 5-5 7-2 2-3 4-4 5v-3c2-6 4-9 8-13z"></path><path d="M367 495c2-2 2-4 5-5h0 0c0 1-1 2-2 3v1h0c0 1 0 3-1 3-1 1-1 2-2 3 2 2 1 2 2 5l-2-3c-2-1-4 0-5 0 1-2 3-5 5-7z" class="O"></path><path d="M566 309l-8 20-1-2c0-1 0-2 1-3s1-3 2-5h-1v-2h0c1-2 1-3 2-4 0-2 0-1 1-2 2-1 2-2 4-2z" class="J"></path><path d="M369 463c-1 0-4 1-6 2 2-4 4-9 7-12 2 0 2 0 4 1h-1l-6 6c2 0 4-1 5-2l2 3c-2 0-3 1-5 2z" class="S"></path><path d="M438 789l2 1v1 1c-1 0-2 2-3 3-2 2-4 3-6 4l-1 1-1-1h0c2-5 5-8 9-10z" class="W"></path><path d="M678 650c2-1 3-1 4-2 3-1 6-3 9-5 0 2 0 3-1 5-2 2-6 4-9 4l-1 1c-1-1-1-2-2-3z" class="O"></path><path d="M471 483c2 2 4 3 6 5 2 1 3 1 4 2h1l-7 6-4-13z" class="K"></path><path d="M377 454s1 0 2-1h1v-2l-1-1 1-2c0 2 1 4 2 6l-5 3 2 2-5 2-2-3c-1 1-3 2-5 2l6-6h1v1c1 0 2 0 3-1z" class="B"></path><path d="M372 458l5-1 2 2-5 2-2-3z" class="D"></path><path d="M504 136c2 3 3 5 8 6v1h-1l-3-1v1l1 1v3l-2 1c-1 1-2 2-3 4 0-2-1-5 0-7v-6-3zm17 1v3c-1 3 1 8 1 11l1 1-1 1c-1-2-2-4-5-5-2-3-2-3-2-6 2-1 4-3 6-5z" class="I"></path><path d="M469 475c2 0 3 3 4 5l3 3 8 5s-1 2-2 2h-1c-1-1-2-1-4-2-2-2-4-3-6-5-1-2-2-5-2-8z" class="S"></path><path d="M590 692h2c2 5 3 10 4 16l2 7c-1-1-1-2-2-3v-3c-1-1-1-2-1-3-1-1-1-1-1-2v1h-1 0c0-1 0-2-1-3h-2c-1-1-3 0-4 0l2-1v-6l1-1c1-1 1-1 1-2z" class="I"></path><path d="M555 794c5 1 8 4 13 4h1c3 2 6 3 7 7 0 1 1 2 1 3-4-2-9-6-12-8s-8-3-12-2v-1c1 0 1-1 2-1 1 1 3 1 5 1-2-1-3-2-5-3zM329 465c4 0 8 2 12 3 5 2 10 3 14 7-6-1-12-3-18-5-3-1-5-3-9-2l-1-1c2 0 2-1 2-2z" class="M"></path><path d="M336 323c1 2 2 4 2 6 1 3 3 6 4 10-2 2-3 5-3 8h0v-4h0c-1-2-1-4-2-6-1-4-3-8-4-11v-3h3z" class="Z"></path><path d="M485 524c1 1 1 1 2 1 2 1 2 1 3 2s1 0 2 1l1 4v2l-1 1c0 1 1 2 1 2l1 2c-2 1-2 1-3 3h0l-3-8c-2-3-2-7-3-10z" class="R"></path><path d="M488 534h1v2c1-2 0-1 2-2 1 0 2-2 2-2v2l-1 1c0 1 1 2 1 2l1 2c-2 1-2 1-3 3h0l-3-8z" class="D"></path><path d="M443 710l2-2 1 1c0 2 1 2 0 4-1 0-5 2-5 2-4 2-11 8-15 7-1-2-2-6-1-8l3 5c4-2 9-4 12-7h1l2-2z" class="P"></path><path d="M445 646c2 4 5 11 5 16l-9-3h3 0l-1-1h1 1c0-1-1-2-2-3l-1-2c-1-1-1-2-1-2l-1-1s-1 0-1-1c-2 0-2 0-3-1h0l6 1h2c0-1 0-1 1-2v-1z" class="B"></path><path d="M336 566h1l-1 1h2v1c-2 5-8 8-12 11l-3 1v-1c1-5 9-11 13-13z" class="P"></path><path d="M570 467l5 5c-1 9-6 18-8 27l-2-1h1c1-2 1-3 1-5l1-2v-2-3-1l-3-3v-1c0-2-1-4-2-5h1c1 1 2 2 4 3h0v-2h1s1 1 2 1v1 1h0v3h0v-1h0l1-1v-2-1h1c1-2 0-4 0-6-1-1-2-3-3-5h0zM424 303v1c-1 4-4 8-5 12s4 7 3 11l-1-1c-1-1-1-2-2-3-1 0-1 0-2-1 0-1-1-2-2-3v-1-1-4h0v-1l1-1c3-2 5-5 8-8z" class="B"></path><path d="M564 708c1 2 3 3 5 4 2 2 9 7 11 6l4-4-1 8c-6-1-11-5-16-7l-3-2-1-1c0-2 0-2 1-4z" class="P"></path><path d="M671 567c1-1 2 0 3 0 5 2 9 8 11 13-1 1-2 0-3-1-3-2-8-4-10-8-1-1-2-2-2-3 2 1 2 1 4 1h0c-2-1-2-1-3-2z" class="H"></path><path d="M580 417c2 2 4 7 5 10 2 4-7 27-9 34 0 0 0 1-1 1l-2-6c0-1-1-3-2-4v-1c1 1 1 1 1 2h1c0 1 0 1 1 2 0 1 0 1 1 2h1l1-4v-1l1-1v-2l1-1v-2l1-1v-3l1-1v-2l1-1v-2c1-1 0-1 1-2h0v-2c1-1 1-2 1-3v-1-2h0l-1-1h0v-1c-1-2-1-3-2-4l-1-1h0v-1-1zM398 264h1 0v3l1 1v2c0-1 0-2 1-4 1 1 1 2 1 4 1 2 1 3 1 5h-1c-1-1-1-1-1-2v3h1v1 1 1h1v2 1l1 1h0v1 1l1 1v1 1h0c1 1 1 1 1 2h0v2l1 1v1h1c1 0 0 0 1 1l1 1h0c0-2 1-4 0-5v-2c0-1 0-2 1-3 0 5 1 9 0 13 0 3-1 5-1 7h0l-12-42z" class="B"></path><path d="M345 550h1 0l2 2v3c1 1 1 4 1 5l1 1v1l1 1c0-2 0-3-1-4v-4c-1-1-1-2-1-3v-3-3c2 5 1 11 3 17v1l1 1v3c1 1 1 2 1 4l1 1v1c1 4 4 7 4 11l1 3c-5-5-6-14-9-20-1-3-2-9-4-12 0-2-1-4-2-6zm-1-35c0-1 1-1 1-2h0v2c1 3 1 6 1 9v12c0 2 0 4 1 6 0 1-1 3 0 4v2h0l-1 1h0c-1-2-1-4 0-6l-1 1v2h-1c-1-7-2-16-1-23 0-3 0-6 1-8z" class="M"></path><path d="M601 731l3-1v1c1 2 1 5 2 7h0v-6h1c3 6 4 11 4 18l-3-3c-3-5-7-10-7-16z" class="H"></path><path d="M511 108c0-4 1-8 2-12l5 26h-1 0 0c-1-1-1-2-2-3h-1c-2-1-1-1-1-3l-1 1v3c-1 0-1 1-2 2 0-4 2-9 1-13v-1z" class="U"></path><path d="M471 286c0 3-1 4-1 7h1c1 2 0 3 0 6l-5 5c-1 1-2 1-2 3s2 3 2 4h1v3c-1-1-2-1-3-1-1-2-3-4-4-6l11-21zm-60 0l1-6 1-8c4 8 4 14 2 22l-4 5c1-4 0-8 0-13zm225 420c1 2 1 4 0 5 0 6-6 13-10 16h-1l4-12c1-4 2-7 7-9z" class="H"></path><path d="M338 380c0-1-1-2-1-3 2 0 3 1 5 1l-1 1h-1c4 2 8 4 12 4h1 3 2c1 2 2 4 2 6h-4l-6-1c-4-2-10-4-12-8z" class="C"></path><path d="M405 731l1-1c1 2 1 4 1 6l-3 5c-2 3-4 7-7 10 0-6 1-15 4-19 1 0 1 0 2 1h0c0-1 1-1 1-2h1z" class="P"></path><path d="M415 294h0l1 3c1 1 0 3 0 4l-1 1c-1 1-1 2-1 3h0c1 0 2 0 3-1l1-1c0-1 1-2 2-3 0-1 1-1 1-2l2-2c0-1 1-2 2-3l1 1h0l-1 2c0 1 0 0-1 1v1l-2 2c-1 1 0 1-1 2l-2 2c-1 1 0-1-1 1 0 1-2 2-3 3 0 1-1 1-1 2v1c-1 1-1 2-1 3h0c-1-1-1-1-1-2s0 0-1-1c-1-2-1-3-1-5h0c0-2 1-4 1-7l4-5z" class="M"></path><path d="M339 347c0-3 1-6 3-8 2 4 3 9 5 14 1 3 3 6 3 9-5-3-9-7-11-13v-2z" class="B"></path><path d="M373 706h2c5 2 6 8 7 12l4 10c-5-4-10-9-13-15 0-2-1-5 0-7z" class="P"></path><path d="M350 388l6 1h4l4 11 1 3h-1v-1l-1-1v-1 2l-2-2c-3-3-5-2-9-2 0-3 0-7-1-9l-1-1z" class="F"></path><path d="M350 388l6 1c-1 0-3 0-4 1h1v4 1 1c2 0 5 0 7 1 1 1 3 2 4 3l1 3h-1v-1l-1-1v-1 2l-2-2c-3-3-5-2-9-2 0-3 0-7-1-9l-1-1z" class="Z"></path><path d="M410 539l14 43c-1-1-2-3-2-4l-1-1h-1 0c-1-2-2-4-3-5-2-7-9-26-7-33z" class="O"></path><path d="M640 431l1-1c2-5 5-6 10-7h1v6c-1 1-2 2-3 2-2 3-5 4-8 6l-2 1h-1c0-3 1-5 2-7zm-284-7c3-1 5 0 7 1s3 2 4 3 1 2 2 4c1 1 1 4 1 6-3 0-10-5-12-7s-2-4-2-7z" class="H"></path><path d="M677 467h0c1 0 2 0 3-1 1-2 2-4 4-5 9-7 22-4 33-3-2 1-4 2-6 2-2-1-5-2-8-2-1 0-1 0-1 1-2 0-4 1-6 1l-4 1-2 1h-2-1v1c-1 0-2 0-2 1h-2 0v-1l-2 2 2 2c1 0 3-1 5 0h2c-4 1-8 0-11 1-2 0-4 2-5 3-1 0-1 1-2 1l-1 1v-1c1-1 2-2 3-2l3-3z" class="C"></path><path d="M328 468c4-1 6 1 9 2 6 2 12 4 18 5l25 6c-2 1-3 1-5 1l-3-1-3 2-16-7-6 19h0c0-6 4-13 6-19-8-3-17-5-25-8z" class="L"></path><path d="M690 467h-2c-2-1-4 0-5 0l-2-2 2-2v1h0 2c0-1 1-1 2-1v-1h1 2l2-1 4-1c2 0 4-1 6-1 0-1 0-1 1-1 3 0 6 1 8 2-7 2-14 7-21 7z" class="E"></path><defs><linearGradient id="C" x1="447.118" y1="744.539" x2="438.266" y2="737.331" xlink:href="#B"><stop offset="0" stop-color="#b8b5b6"></stop><stop offset="1" stop-color="#dcd9d9"></stop></linearGradient></defs><path fill="url(#C)" d="M447 719v2c0 1-1 2-1 3s-1 3-1 4l-1 3c0 1 0 2-1 3l1 1 3 1h2c1 0 2 1 3 1h1c1 1 2 1 2 1l1 1h-4v2l1 1h-3 0c-4 0-8 2-12 3l-3-7h5v1c1-1 3-1 4 0h0 1 1c1-1 2-1 3-2h-2c-1 0-1-1-2 0h-1c-3-2-6 0-8-2v-1h1c0-1-2-2-2-4 0-1 1-1 1-2v2 1l3 3h3c1-1 1-1 1-2l1-3 1-5h0l1-1v-1c1-1 1-2 1-3z"></path><path d="M695 480c5 0 10 0 15 1l-2 2c-1 0-2 1-4 1l-5 3h-2c-1 1-1 1-2 1h-5v1l1 1c0 2 0 5-1 6-1-1 0-4-1-6 0-2-1-5-3-7v-1h0 1 1c1 0 2-1 2-1 2 0 3 0 5-1z" class="I"></path><path d="M359 443c2 0 4 0 7 1l1 1h0l-2 4c-1 3-3 5-5 7-1 1-1 2-2 3-2-1-2-2-3-3v-2l-1-1v-6h1v-1c1-2 2-2 4-3z" class="M"></path><path d="M552 739l5-1c1 0 2 2 2 2h7c2 0 3 1 5 1l1 1v1h-5c-3 0-9-1-11 1-1 0-1 1-2 2v2h0c-1 1-2 1-2 2l-1 1v1c1 1 1 1 2 1l1 1v1c-2-2-5-3-7-4l-1-1c2-4 3-8 6-11z" class="F"></path><path d="M538 800c1-2 2-7 4-8 1 0 2 0 3-1-2-2-5-2-8-3l11-1c3-1 6 0 9 0-3 0-6 1-9 1h0c-2 0-3 0-4 1 1 0 2 0 3 1h1c1 1 1 1 2 1s3 2 5 2v1c2 1 3 2 5 3-2 0-4 0-5-1-1 0-1 1-2 1h-3-1l-1-1c-1 0-2-1-3-1h0l-1 1h-1v1 4l-1 1v3 1h2 0l-1-2 1-1h0c1 2 1 5 2 5h3s1 0 1 1h-1c-2 0-3 0-6-1-2-3-4-5-5-8z" class="N"></path><path d="M584 191c-6-2-12-5-16-9 2-1 5 0 7 0 7 0 13 0 19 3h0v1c-2 1-6 3-7 5h-1-2z" class="B"></path><path d="M682 702h0c2 1 5 3 6 4 2 4 4 8 5 12l4 13-5-5c-4-4-10-12-10-18-1-2 0-4 0-6z" class="E"></path><path d="M492 528l4 1c0 1 0 3-1 4 0 1 1 2 1 3l1 1c0 1 0 1-1 2h1c0 1 2 7 2 7 2 2 4 4 7 4h1c0 1-1 2-1 3v1c-2 0-3 0-5 1-1 1-1 2-3 1l2-2-3-4c-1 1-2 1-4 2l-2-10h0c1-2 1-2 3-3l-1-2s-1-1-1-2l1-1v-2l-1-4z" class="F"></path><path d="M499 546c2 2 4 4 7 4h1c0 1-1 2-1 3v1c-2 0-3 0-5 1-1 1-1 2-3 1l2-2h1v-1h3c-1-2-2-3-4-4-1 0-1-2-1-3z" class="O"></path><path d="M494 539c-1 2 0 4 1 6v1l1 1c0 1 0 2 1 3-1 1-2 1-4 2l-2-10h0c1-2 1-2 3-3z" class="V"></path><path d="M599 722c1 2 1 3 2 4v1c1 1 0 3 0 4 0 6 4 11 7 16l-1 1v3 1c1 1 1 8 0 9h-1l-2-2h0c-2-3-3-6-4-10s-1-9-1-13l1-1v-4c1-2 0-6-1-9zm-264-25c3 1 5 5 7 7 3 3 6 4 9 4 3 1 6 0 8-2v-1c-1 0-2-1-3-2l-4-4h0c3-2 7 2 10 3v-3h-1c0-1 1-2 1-2v-3c1-1 1-1 1-2v-2l1-1v3 1h0c1 4-1 7-1 11l1 1h0c0 1 0 1 1 2v2h1l1 2-1 1c-1-1-1-1-2-3 0-1-1-2-2-3 0-1-1-1-1-2l-1-1-1-1-1-1s-1-1-2-1h-1c0-1 0-1-1-1h0c0 1 1 1 2 2v1l5 3v1h1c0 1 1 2 1 3s1 1 1 2v1l1 1c1 1 0 0 1 2h0l1 1v1 1l1 1c1 1 1 2 2 3v1h0v1l2 2c1 1 0 0 1 2l1 1h-1c-3-4-5-9-8-14-1-2-2-5-3-6h-1c-3 2-8 2-12 2-5-1-8-5-11-9l-3-5z" class="I"></path><path d="M326 702c1 2 2 3 2 5 0 7-10 18-15 23h-1c3-7 5-16 8-23 1-1 3-3 5-4h0l1-1z" class="C"></path><path d="M640 441h-1c-2-1-2-3-3-5 0-6 2-9 5-14 2-6 4-12 8-18h1-1v-1-1l1-2 1-1c1-3 3-7 5-8l1 1c-1 3-1 6 0 8v1 1 1c-3 1-6 3-7 6-1 0-2 1-2 2-1 1-2 3-3 4v2l-1 2v2h0c-1 1-1 1-1 2l-1 1c0 1-1 1-1 2-1 1-1 1-1 3h-1c0 1 0 1 1 2-1 2-2 4-2 7h1l2-1c-1 2-1 3-1 4z" class="I"></path><path d="M390 480l-3-8c1 0 1 0 1 1l5 13c-2 0-3 1-4 1-3 1-5 1-8 2v-2h0l-3 1c-2 0-4 1-6 2h0c-3 1-3 3-5 5v-2h-1v-2c3-4 6-5 9-6l-6-2 3-2 3 1c2 0 3 0 5-1 3 0 7 0 10-1z" class="J"></path><defs><linearGradient id="D" x1="381.386" y1="485.062" x2="380.599" y2="481.272" xlink:href="#B"><stop offset="0" stop-color="#4f4b4d"></stop><stop offset="1" stop-color="#656264"></stop></linearGradient></defs><path fill="url(#D)" d="M380 481c3 0 7 0 10-1v1 3c-2 1-5 2-7 2-3 0-5-1-8-1l-6-2 3-2 3 1c2 0 3 0 5-1z"></path><path d="M442 402c4-4 5-11 8-15 2 3 4 5 5 9l-7 12c0 3-1 4-1 7v2c-2-5-4-10-5-15z" class="B"></path><path d="M839 181l18-6c1-1 0-1 1-1 1 1 1 2 1 3l-1 1v2l-1 2c-2 3-5 4-9 5-5 2-11 3-17 4 1-1 2-2 3-2h2c1-1 1-1 3-1 0-1 1-1 2-2v-1c0-1 0-1-1-2v-1c0-1-1-1-1-1z" class="Y"></path><path d="M393 486v1l2 5-1 1c-1-1-2-3-3-4-1 0-2 1-2 2-2 1-3 2-5 2l-1-1h-1c-1 1-1 2-1 3l-1-1c-1 1-1 3-1 5v1c-2 0-3-2-3-3l-1 3c-2 2-2 2-2 5l-1-1v-2l-1-1h-1c-1-1 0-1 0-2 0 0-1-1-1-2 1 0 1-2 1-3h0v-1c1-1 2-2 2-3h0c2-1 4-2 6-2l3-1h0v2c3-1 5-1 8-2 1 0 2-1 4-1z" class="F"></path><path d="M376 497c0-2 1-3 3-5l1 2c-1 1-1 3-1 5v1c-2 0-3-2-3-3z" class="U"></path><path d="M379 492c4-3 10-4 14-5l2 5-1 1c-1-1-2-3-3-4-1 0-2 1-2 2-2 1-3 2-5 2l-1-1h-1c-1 1-1 2-1 3l-1-1-1-2z" class="P"></path><path d="M366 663l3 3c1 2 1 3 2 5v1c1 2 0 8 0 9-1 4-1 7-4 10 0 1 0 1 1 1l2 7-1 1-3-9c0-1-1-2-1-3v-2c-1-1 0-1 0-3l-2-1 1-1s1-1 2-1c-1-1-1-1-2-1h-1-1l4-4c-1-1-3-2-4-3v-2l2-1-1-1c1-1 0-1 1-1h0v-1h-1c0-1-1-1-1-2h1l1-1 1 1 1-1z" class="M"></path><defs><linearGradient id="E" x1="561.408" y1="299.486" x2="545.126" y2="302.71" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#E)" d="M550 286v-1l5 8v-3c3 6 6 12 10 17 1 1 1 1 1 2-2 0-2 1-4 2-1 1-1 0-1 2-1 1-1 2-2 4h0v2l-1 1-1-1 1-3c2-3 2-6 3-9-2-2-2-3-5-4-2-1-7-1-9 0l-2 2-1-1 2-2c3-1 5-1 7-2-1 0-2-1-3-1-2 0-3-1-5-2-1-1-3-2-3-3h12l-4-8z"></path><path d="M467 715l10 34c2 5 4 10 5 15l-1 1v-1l-5-18c-1-1-1-2-2-2l-1 1c-2-2-2-4-5-5l-2-4-3-10 1-1c1-1 0-3 0-4l1-1v-1c1-1 1-1 1-3h0l1-1z" class="T"></path><path d="M466 716h0l1 4c1 3 1 6 1 8l-3-8v-1c1-1 1-1 1-3z" class="V"></path><path d="M464 721l1-1 3 8c0 1 0 2 1 4 2 2 2 5 3 7s2 3 2 5l-1 1c-2-2-2-4-5-5l-2-4-3-10 1-1c1-1 0-3 0-4z" class="E"></path><path d="M549 782h1c2 1 4 3 6 4 4 2 8 3 13 4 1 1 1 2 1 3-3-3-8-4-12-5l-1-1c-3 0-6-1-9 0l-11 1c3 1 6 1 8 3-1 1-2 1-3 1-2 1-3 6-4 8l-4-4v-3c-1-1-1-1-1-3v-1l3-3c1-1 2-3 2-4 3-1 6 0 8 0h3z" class="J"></path><defs><linearGradient id="F" x1="462.971" y1="378.984" x2="452.385" y2="372.243" xlink:href="#B"><stop offset="0" stop-color="#4a484a"></stop><stop offset="1" stop-color="#666465"></stop></linearGradient></defs><path fill="url(#F)" d="M460 357v3h1l1 1c-1 12-3 24-7 35-1-4-3-6-5-9-3 4-4 11-8 15v-1c2-7 6-12 10-18 4-7 6-14 7-21v-2l1-3z"></path><path d="M543 759c1-2 2-3 3-5v-1h1 1c-3 2-3 9-3 12 0 4 0 7 1 11l-2-2c0 1 0 2 1 3 1 2 2 3 4 5h-3c-2 0-5-1-8 0 0 1-1 3-2 4l-1-1c0-3 1-6 2-9v-2c1-1 1-3 1-4v-1c1-3 2-7 4-10h1z" class="K"></path><path d="M536 786l-1-1c0-3 1-6 2-9v-2c1-1 1-3 1-4v-1c1-3 2-7 4-10h1l-3 9c0 1-1 2-1 4v5c0 1-2 2-2 3 1 1 1 1 2 1l1-1h2c1 0 2-1 3 0s1 1 1 2c-2 0-5-1-8 0 0 1-1 3-2 4z" class="Q"></path><path d="M420 690l4 2c-1 1 0 1-1 1-1 1-1 0-2 1 0 2 0 4-1 5l1 1h1c1 0 2 0 3-1h1 3 4 1v1h-1c-1 0-1 0-2 1h0c1 1 2 1 4 1h0c2 1 5 3 6 5h1c0 2 0 2 1 3l-2 2-2-2c-1-1-1-1-2-1h-1c-1 0-2-1-2-1l-3-1h-2l-1-1h1l1-1c-1-1-3 0-5 0l-1 1v-1c0-1 0-1 1-2h0c-1 0-1-1-2 0h-1c2-2 5-2 8-3-1 0-4-1-4 0-2 0-2 0-3 1h-1 0-2c-1-1 0-3 0-4v-1l1-1v-2h-1-1c0 2-1 1-1 3l-1 1h0v1c-1 1-1 1-1 2v1s-1 1-1 2l-1 4c-1 1-1 2-2 3l-1 1c0 2-1 4-2 6v1 1 2l-1 1v2c0 2 0 2-1 4v3h1v2h0v3c-1 2-2 4-3 5h-1l3-5c0-2 0-4-1-6 0-7 3-14 5-21 3-3 5-10 6-15l3-4z" class="I"></path><path d="M565 366c0-1 0-2 1-3v1c0 1 0 2 1 3h0l1-1c1 0 1 2 2 3v-1c3 6 4 12 7 17-2 2-3 4-4 7l-1 1v8c-4-12-7-23-7-35z" class="C"></path><path d="M567 367l1-1c1 0 1 2 2 3v-1c3 6 4 12 7 17-2 2-3 4-4 7l-1 1v-2c0-5-2-9-2-14-1-3-1-7-3-10z" class="U"></path><path d="M550 381v-2-1c-1-4 1-10 1-13 0-4 0-8 1-11 0-1 0-1 1-2-1 10-2 20-1 31 1 24 10 46 19 68v1 1c0 1 0 1 1 2v1l-2-2c-1-4-3-9-5-13-1-2-2-5-4-7 0-1-1-2-1-4l-2-5v-2l-7-32c-1-2-1-6-1-8-1-1 0-1 0-2zm88 236a30.44 30.44 0 0 0 8 8l3 3h1c2 2 4 3 6 4v2l-11 1-6 1h0v1h-2v-2c0-3 1-5 0-7l1-1-3-6h-1l4-4z" class="G"></path><path d="M635 621c3 2 3 5 6 6 2 2 2 3 3 5v1h-3c-1-1-1-3-1-4l-2-2-3-6z" class="B"></path><path d="M638 627l2 2c0 1 0 3 1 4h3c0 1 0 1 1 2l-6 1h0v1h-2v-2c0-3 1-5 0-7l1-1z" class="M"></path><path d="M644 632v-2l2-2v-1c2 0 2 0 3 1h1c2 2 4 3 6 4v2l-11 1c-1-1-1-1-1-2v-1z" class="I"></path><path d="M674 645l4 5c1 1 1 2 2 3 5 6 9 14 8 22 0 2-1 4-2 5v1c-3 8-8 12-15 14-5 2-10 2-14 1h-1c-3-2-8-5-10-8-1-1 0-1-1-2v-1c-1-1-1-1 0-2 0 1 1 2 1 3 3 4 6 7 10 8l1 1c3 1 7 1 10 0h1c5-1 10-4 13-8l1-1c2-2 3-5 4-8v-1c2-5 1-11-2-15v-1c-2-3-4-5-6-8-1 1-4 3-5 3h-1c-4-1-8-1-11-2-2-2-5-3-8-4-1 0-2 0-3-1 4 0 8 2 12 2 4 1 8-1 12 0h3c-1-2-3-3-3-5v-1z" class="L"></path><path d="M557 520c1-1 1-3 2-4v-1h1l1 3-2 9-1 1c0 1 0 2-1 2v3c-1 2-1 2-1 4v1c-1 0-1 0-1 1v1 1l-6 2c-1 1-2 1-3 1h-1-1l-1-1h-1l1-1h2l-1-1 1-2v-1c0-1 0-2 1-3h0c1-3 1-5 1-7 3 0 4-1 6-1h1v-1h2v-1c0-2 0-3 1-5z" class="B"></path><path d="M554 526h2l-1 2h-1-1v1h0l1 1c1 2-1 5-1 7-2 2-3 3-6 4l-2 1-1-1 1-2v-1c0-1 0-2 1-3h0c1-3 1-5 1-7 3 0 4-1 6-1h1v-1z" class="Y"></path><defs><linearGradient id="G" x1="464.772" y1="794.194" x2="443.728" y2="796.806" xlink:href="#B"><stop offset="0" stop-color="#c0bdbf"></stop><stop offset="1" stop-color="#e2e0e0"></stop></linearGradient></defs><path fill="url(#G)" d="M462 786c3 0 5 0 7 1h1c1 0 1 1 2 1v2c1 2 5 3 4 5l-2 1c-1 2-3 4-5 6 1-2 1-3 1-5l-2-2h-1-2-1c-3 0-5 1-7 2l-12 3-1-1h1c-4 0-7 0-10 3l-1 1 1-2c0-1 0-1 1-2 1 0 2-1 3-1v-1h1c1 0 1 0 2-1 1 0 1 1 1 0h1l1 1c0-1 1-1 2-1h1 0c1 0 2 0 3-1l3-1 1-1h2v-1c2-1 0 1 2-1 0-1 1-1 1-1l1-1 1-1c1-1 1 0 1-1l-1-1z"></path><path d="M446 709l1 1v3c1 1 1 2 2 3 1 2 2 5 4 8 2 4 3 8 4 13v1h-2s-1 0-2-1h-1c-1 0-2-1-3-1h-2l-3-1-1-1c1-1 1-2 1-3l1-3c0-1 1-3 1-4s1-2 1-3v-2c0-1-1-1-1-2h-1l-1-1c0 1-1 2-2 3-1 0-3 0-3 1l-5 5h-1c2-2 8-6 8-9v-1s4-2 5-2c1-2 0-2 0-4z" class="G"></path><defs><linearGradient id="H" x1="609.415" y1="196.132" x2="588.173" y2="187.207" xlink:href="#B"><stop offset="0" stop-color="#8b888a"></stop><stop offset="1" stop-color="#bab6b8"></stop></linearGradient></defs><path fill="url(#H)" d="M594 185c6 2 12 5 18 8h0c-1 2 0 3 0 4 0 2 1 4 1 6l-29-12h2 1c1-2 5-4 7-5v-1h0z"></path><path d="M327 467c-12 1-24-5-36-8 6-1 12-2 18-2 8 0 14 1 20 8 0 1 0 2-2 2z" class="H"></path><defs><linearGradient id="I" x1="571.301" y1="698.63" x2="579.741" y2="705.971" xlink:href="#B"><stop offset="0" stop-color="#aaa7a9"></stop><stop offset="1" stop-color="#d0cdce"></stop></linearGradient></defs><path fill="url(#I)" d="M580 695h5c1-1 1-1 2-1l1 1v6l-2 1-1 1h-1c0 1 0 1-1 2h0c-2-1-4 0-6 1l-1 1c-1 1-2 1-3 2s-3 2-4 3c-2-1-4-2-5-4 0-1 0 0 1-1 1-3 2-5 5-7v-1l5-3 5-1z"></path><path d="M580 695h5c1-1 1-1 2-1l1 1v6l-2-1v-1c-3-2-10 0-14 1h-2v-1l5-3 5-1z" class="T"></path><path d="M580 695h5c1-1 1-1 2-1l1 1v6l-2-1v-1-1c0-1 0-1-1-2-2 1-3 1-5 1v-2z" class="E"></path><path d="M561 434c2 2 3 5 4 7 2 4 4 9 5 13l2 2v-1c-1-1-1-1-1-2v-1c1 1 2 3 2 4l2 6 1 4-1 6-5-5c0-1-1-3-2-4-2-3-5-7-7-11-1-1-2-4-3-6l2-2 3-3-1-2c0-1 0-2-1-4v-1z" class="K"></path><path d="M560 444h1l2-2 1 1-3 2 1 1c1 0 1-1 2-1h1 0c-1 1-1 2-2 2-2 1-3-1-3 1 0 1 0 1 1 2l1-1h1 1l-1 1-2 2c-1-1-2-4-3-6l2-2z" class="U"></path><path d="M563 450v1 1l1 1 1-1h1v1c1 1 0 1 1 2v1l1 1c1-1 1-1 1-2l1-1 2 2v-1c-1-1-1-1-1-2v-1c1 1 2 3 2 4v1 2 1 1c-1-2 0-3-2-3-1 2-2 3-3 5-2-3-5-7-7-11l2-2z" class="L"></path><path d="M568 463c1-2 2-3 3-5 2 0 1 1 2 3v-1-1-2-1l2 6 1 4-1 6-5-5c0-1-1-3-2-4z" class="C"></path><path d="M564 713l3 2c0 1-1 1 0 2l1 1 1 1 1 1h0c1 0 1 1 2 2h0c2 3 3 5 2 8 0 2 0 2-1 3h-1l1-1v-2-1c1-2 1-3-1-5-1-1-5-4-7-4v1c1 2 2 4 1 8l-1 1c1 2 1 4 2 6l-10 2-5 1v-1c0-1 2-1 3-2l-1-2c1-3 3-7 4-9 0-2 0-2 1-3 1-2 3-5 3-7l2-2z" class="M"></path><path d="M555 736c2-1 5-1 7-2 1-3 0-9 0-12l3 8c1 2 1 4 2 6l-10 2-5 1v-1c0-1 2-1 3-2z" class="P"></path><path d="M533 798h-1v-2l1-1 1 1 4 4c1 3 3 5 5 8 0 1 0 2 1 2 1 1 1 0 2 1l1 1h-5l-1-1-1 2 2 3c-1-1-2-1-2-3-1 0-1-1-1-2v1c0 1-1 2 0 4 0 1 1 1 1 2v1l1 1c1 1 0 1 1 2 0 1 0 2-1 3v3l1 1c1 0 1 1 2 1-4-1-7-3-10-5l-1-2c0-7 0-13 1-20 0-1 0-2 1-3l-2-2z" class="F"></path><path d="M538 817c1 1 2 3 3 5 0 1 0 1-1 2l-1 1-1-1v-7z" class="W"></path><path d="M533 798h-1v-2l1-1 1 1 4 4c1 3 3 5 5 8 0 1 0 2 1 2 1 1 1 0 2 1h-4c-4-1-5-7-7-11h0l-2-2z" class="H"></path><path d="M352 398c4 0 6-1 9 2l2 2v-2 1l1 1v1h1l15 45h0l-1 2 1 1v2h-1c-1 1-2 1-2 1-1-1-1-2-1-3-1-1-1 0-1-1s1-1 1-2v-2-2h0v-1c0-1 0-2-1-3 0-1 0-1-1-2v-1l-3-3v-2h-1l-2-7c-2-5-3-11-6-16 0-2-2-3-3-5v-1c0-1-1-1-1-2v-1c-2 1-5 1-7 0l1-2z" class="N"></path><path d="M645 657c-1-2-3-3-4-4h0c2 0 4 2 6 3h1 1c0 1 0 2 1 3v2l1 3-1 1c0 2 1 7 0 8v1 3h0l1 1c0 1-1 1-1 1-2 0-2 1-3 2h-2v2l-1-1c-1-2-2-3-2-5-1 0-1-2-1-3h-1v4 1l1 1h0l-1 1c0-1 0-1-1-2v-1-5c-1 2-1 3-1 5 0 1 1 2 0 3l-1-2c0-2 0-5 1-7v-1c0-2 0-3 1-4h1c0-1 1-2 1-2 2-1 4-3 5-4 0-2 0-3-1-4z" class="M"></path><path d="M648 656h1c0 1 0 2 1 3v2l1 3-1 1c0 2 1 7 0 8v1 3h0l1 1c0 1-1 1-1 1-2 0-2 1-3 2h-2v2l-1-1c-1-2-2-3-2-5h0v-2-2c0-1 1-2 1-2l2-2h3v-1c1-1 1-1 1-2-1-2-1-7-1-10z" class="I"></path><defs><linearGradient id="J" x1="443.484" y1="369.315" x2="455.53" y2="389.376" xlink:href="#B"><stop offset="0" stop-color="#9f9c9d"></stop><stop offset="1" stop-color="#cbc8ca"></stop></linearGradient></defs><path fill="url(#J)" d="M444 368l1-1c0 1 1 2 2 3v-1h1c1-3 3-3 4-5 2-1 3-2 5-3l2 1c-1 7-3 14-7 21-4 6-8 11-10 18-1-1-1-2-1-3v-1c1-3 1-6 2-9h1l1-1c1-2 0-5 1-8 1 0 1-1 1-2 0-2 0-5-1-7-1-1-2-1-2-2z"></path><path d="M363 687l1 2-1 1v2c0 1 0 1-1 2v3s-1 1-1 2h1v3c-3-1-7-5-10-3h0l4 4c1 1 2 2 3 2v1c-2 2-5 3-8 2-3 0-6-1-9-4-2-2-4-6-7-7v-3c1 1 3 2 5 2h1c2 1 6 1 8 0h0c2 0 3-1 5-2 1 0 1 0 2-1l1-1c3-1 4-3 6-5z" class="b"></path><path d="M369 619c2 2 1 5 1 8v1l-2 5c0 1 0 1-1 1v1h-1l-1 1h-2 0c-7 1-14 2-20 6-1 0-2 0-3 1 1-2 3-2 4-4-1 0-1-1-2-1 1 0 1-1 2-1-2-2-3-3-4-5h1c1 0 2 0 3 1h6c7-4 14-8 19-14z" class="B"></path><path d="M341 632c1 0 2 0 3 1h6c-3 2-5 1-8 1l-1-2z" class="I"></path><path d="M365 627c0-2 0-2 2-4 1 1 1 1 1 2 0 2 0 8-1 9l-1-2c-1-1-1-3-1-5z" class="G"></path><path d="M366 635h-11v-1c2-3 6-5 10-7 0 2 0 4 1 5 0 1 0 2 1 2v1h-1z" class="I"></path><path d="M463 781h1c1 0 3-2 5-2 1 0 2 2 3 3 3 1 4 5 6 7l3 14c1 4 1 8 2 12 0 2 1 5 1 7l-3-6c0-3 0-6-1-9-1-2-2-4-2-6l-1-4h0l-1 1-1 1-1 1h-1c-1 3-2 6-4 8-2 3-6 3-9 6-1-2-2-3-2-5v-1c2 0 2 0 3 1v1h0 2l3-3c1-2 2-3 3-5 2-2 4-4 5-6l2-1c1-2-3-3-4-5v-2c-1 0-1-1-2-1h-1c-2-1-4-1-7-1h-7c-3 1-7 3-9 3l6-3c2-2 5-3 8-4l3-1z" class="Q"></path><path d="M463 781h1c1 0 3-2 5-2 1 0 2 2 3 3 1 2 3 4 2 7v-1c-2-2-6-3-8-3-5 0-9 1-14 1 2-2 5-3 8-4l3-1z" class="O"></path><path d="M474 828c3-1 6-1 9-1v3c1 0 2 1 3 1v3h-1s-2 0-2 1v3l1 1c1 0 1 0 2-1 2 3 1 7 3 10 1 3 3 5 5 8 1 2 2 4 3 7l-1 1c-1 0-1-1-2-2l-1-1c-1 0-3-1-4-3 0-1 0 0-1-1-2-3-3-5-4-8-1-1-1-1-1-2h0l-1-3-1-1c-1-2-1-2-1-4-1 0-1-1-1-1v-1c-1-1-1-2-2-3-1 0 0 0-1 1h-2l-1 1h0c-2 1-1 0-2 1-1 0-2 1-3 1-2 2-5 3-8 4h0c2-2 4-3 5-5 1-1 1-2 2-2v1c0 1-1 2-1 2l-1 1c1 0 1 0 2-1h0 1l1-1h1v-1h1l1-1c1 0 1 0 2-1v-1h-2-1l1-1h1c1-1 1-2 1-3h-1v1h-1c-1 0-2 0-3 1h-3 0v-1h3c2-1 3-1 5-2z" class="B"></path><path d="M454 678c3 3 4 7 5 11l4 12 2 6-6 1v1c2 1 3 0 5 0h1c1 1 1 4 2 6l-1 1h0c0 2 0 2-1 3v1l-1 1c0 1 1 3 0 4l-1 1-2-6-4-9-5-10c0-1 1-1 2-2l1 2c0 1 0 2 2 3v-1c-1-1-1-1-1-2h0l-1-1 1-1c1 0 1 1 2 1l1 2v-1-1-1-2h1l-1-2h-5v1l-1 1c0-1 0-1-1-2h0v-1-1c0-2 1-3 1-5l-2 1v-1c1-1 2-2 3-2l1-1v-3c-1-2-1-3-1-4z" class="D"></path><path d="M463 712v-1c1 0 1 0 2-1 1 2 1 4 1 6s0 2-1 3c-1-2-3-5-2-7z" class="L"></path><path d="M463 701l2 6-6 1s0-1-1-2c1-1 2-1 2-2h2c-1-2-1-1-3-1 1-1 2-2 4-2z" class="H"></path><path d="M460 714v-3c1-1 1-1 2-1l1 2c-1 2 1 5 2 7v1l-1 1c0 1 1 3 0 4l-1 1-2-6-1-6z" class="C"></path><path d="M460 714c1 2 2 4 2 6l2 1c0 1 1 3 0 4l-1 1-2-6-1-6z" class="T"></path><path d="M399 658c2 0 2 1 4 2v1a30.44 30.44 0 0 1 8 8h1v2c-1 1-1 4-1 6-1 2-1 4-1 7l-1 8-1-3v-3l-1-1 1-1v-3l-1-1c0-1 1-2 0-3h0 1c-2-2-2-2-4-3h-1-2l-1 1c-2 1-2 3-4 5l1-1v-1-1l-1-1c0 1-1 2-1 2 0 1 0 1-1 2v-3h-1l-3 3-6 6c1-3 3-5 5-8 2-2 3-4 4-6 3-4 6-9 6-14z" class="B"></path><path d="M399 658c2 0 2 1 4 2v1a30.44 30.44 0 0 1 8 8v2l-2-1c-4-1-7-3-10-1l-3-1v1l-6 11-6 6c1-3 3-5 5-8 2-2 3-4 4-6 3-4 6-9 6-14z" class="U"></path><path d="M409 670l-1-2c-3 0-6-1-8-4 0-1 0-2 1-3h2a30.44 30.44 0 0 1 8 8v2l-2-1z" class="R"></path><path d="M663 337h0 2c1 0 1 1 2 2 2 3 2 6 1 10h0v1 1c-1 1-1 2-1 3-1 2-3 3-6 4l-2 1c-1 0-1 2-1 2v1 1 2c-1-1-2-1-3-2l-1 1c2 2 2 1 4 2-1 2-3 5-5 7 0 0-4-1-5-2l15-34z" class="U"></path><path d="M642 445h1c1-1 1-1 2-1h1l2-1c2 0 4 1 5 2l1 1h0l2 1v1 1c1 2 1 5-1 8h0c-1 2-2 3-4 5v-1l-1 1 1 1 1 1h0c-1 0-1-1-2-1-2-2-6 1-8-1h0l-2-1-1-1-2-2v-3c1-2 1-1 1-3 1 0 1-1 2-2-1-2-1-4 0-6l2 1z" class="M"></path><path d="M640 444l2 1-1 1c1 5 5 12 8 15l5-7c1-2 1-5 1-8h-1l2 1v1 1c1 2 1 5-1 8h0c-1 2-2 3-4 5v-1l-1 1 1 1 1 1h0c-1 0-1-1-2-1-2-2-6 1-8-1h0l-2-1-1-1-2-2v-3c1-2 1-1 1-3 1 0 1-1 2-2-1-2-1-4 0-6z" class="I"></path><path d="M511 108v1c1 4-1 9-1 13 1-1 1-2 2-2v-3l1-1c0 2-1 2 1 3h1c1 1 1 2 2 3h0 0v2l1 1h0 1c2 1 2 3 3 5 0 2 0 4-1 7-2 2-4 4-6 5h-3c-5-1-6-3-8-6v-7c1-3 3-4 5-5 0-6 1-10 2-16z" class="E"></path><path d="M510 125h1c1 0 2 1 2 2 1 2 2 5 0 7v1h1l-1 1h-3c-1-1-2-2-2-4-1-3 0-5 2-7z" class="K"></path><defs><linearGradient id="K" x1="156.533" y1="175.818" x2="147.459" y2="175.681" xlink:href="#B"><stop offset="0" stop-color="#959193"></stop><stop offset="1" stop-color="#bebbbc"></stop></linearGradient></defs><path fill="url(#K)" d="M174 189c-6-1-12-3-18-5h0l-3-1c-4-2-8-3-10-8h0c-1-1-1-2-1-3v-1l2-2c1-1 0-1 1-1 9 9 23 11 35 14l-1 1c-1 2-4 4-5 6z"></path><path d="M648 371c1 1 5 2 5 2 4 1 10 7 14 5 1 0 3-1 4-2l1 1c0 2-1 4-3 6-3 2-7 3-10 4l-11 3c-2-1-3-1-5-1-1 1-2 2-2 3s-1 2-2 3l9-24z" class="H"></path><path d="M652 376c3-1 4 1 6 2 4 2 7 2 10 1 1-1 1-1 2 0-2 0-3 1-4 2h-1c-4 2-11 3-15 1l-1-1c1-2 1-3 3-5z" class="b"></path><defs><linearGradient id="L" x1="474.601" y1="237.721" x2="498.403" y2="237.778" xlink:href="#B"><stop offset="0" stop-color="#aba7a8"></stop><stop offset="1" stop-color="#cdcbcb"></stop></linearGradient></defs><path fill="url(#L)" d="M498 194c1 1 1 1 2 1l-8 36v2 1c0 1-1 2-1 3v2c-1 1-1 2-1 3 0 0-1 1-1 2l-3 9c0 1-1 2-1 3-1 2-1 4-1 6v1c0 1 0 1-1 2v2l-1 1v1 2c0 1-1 1-1 1 0 1 0 1-1 2 0 2-1 4-2 6 0 2-1 3-2 4 0 2 0 3-1 4l-1 2v1l-2 2h-1-1c0-3 1-4 1-7 0 0 1-3 2-3l4-12c6-15 10-30 13-46 3-10 6-21 8-31z"></path><defs><linearGradient id="M" x1="529.247" y1="710.132" x2="546.619" y2="729.925" xlink:href="#B"><stop offset="0" stop-color="#454145"></stop><stop offset="1" stop-color="#5d5e5c"></stop></linearGradient></defs><path fill="url(#M)" d="M542 697l2-1v3c-1 1 0 3-1 5v3c1 0 2 1 3 1s3 0 4 1l-2 2v3c0 2-2 4-2 6l-8 23h-4v1h-3v-1h-1l12-46z"></path><path d="M548 711v3l-5 7-2-2c0-2 1-4 1-6h0c2-2 4-2 6-2z" class="J"></path><path d="M541 719l2 2-6 17-1-1c2-6 2-12 5-18z" class="L"></path><path d="M548 714c0 2-2 4-2 6l-8 23h-4-1l2-1c1-1 1-2 2-4l6-17 5-7z" class="E"></path><path d="M474 800l1-1 1-1 1-1h0l1 4c0 2 1 4 2 6 1 3 1 6 1 9l3 6c0 1 0 2 1 3h0c-2 0-6 0-7 1-2 0-3 0-4 1v1c-2 1-3 1-5 2h-3v1l-1 1c-1 0-1 0-2 1h-1c-2 1-3 2-4 3l1 1 3-3h0v2c-1 1-2 1-3 2h0-1l-1-1 1-1c0-1 0-1 1-2h0l2-1 2-2h1c1-1 2-1 2-2 1 0 0-1 1-2l1-1c-1-1-2-1-2-2 0-3 1-3 2-5h0 0c-1-2-1-2-1-4 0 0 1-1 2-1l-1-2 2-1 1 1c-1 2 0 3 0 5h0v2h0v3l2 2v-1-2c-1-1 0-5 0-7-1-1-1-3-1-4 1-1 1-1 1-2v-1-5c0 1-1 2-1 2v1l-1 1v2h-1l1 1-1 1v1l-1-1-1 1c-1 0-2 0-3 1h-1-1c0 1-1 1-1 1-1 1-1 1-2 3v-2c3-3 7-3 9-6 2-2 3-5 4-8h1z" class="I"></path><path d="M474 800l1-1 1-1 1-1h0l1 4c0 2 1 4 2 6 1 3 1 6 1 9l3 6c0 1 0 2 1 3h0c-2 0-6 0-7 1-2 0-3 0-4 1v1c-2 1-3 1-5 2h-3c4-2 7-4 10-7 0-1 1-3 0-4-1-7 1-13-2-19z" class="J"></path><defs><linearGradient id="N" x1="498.566" y1="173.976" x2="508.934" y2="185.024" xlink:href="#B"><stop offset="0" stop-color="#5b575b"></stop><stop offset="1" stop-color="#7d7b7a"></stop></linearGradient></defs><path fill="url(#N)" d="M505 161l1 2h0l-1 1c1 1 1 2 2 3h1 1c2 1 4 1 6 1h2v1c1 3 2 5 2 8 2 5 3 11 4 16v-1c-4-4-3-17-10-19-2 0-3 0-4 1s-2 2-2 3c-1 5-2 11-3 17s-3 13-4 19l-2 11c0 3 0 5-2 7v1l-1 1c0 1-1 3-2 3l-2 1c0-1 1-2 1-3v-1-2l8-36 2-13c1-5 3-12 3-18v-3z"></path><defs><linearGradient id="O" x1="633.425" y1="206.133" x2="619.766" y2="211.83" xlink:href="#B"><stop offset="0" stop-color="#2f2c2d"></stop><stop offset="1" stop-color="#575759"></stop></linearGradient></defs><path fill="url(#O)" d="M613 203c0-2-1-4-1-6 0-1-1-2 0-4 13 7 26 18 31 33l1 4c1 4 1 10 0 15l-1 3c0-4 0-8-1-11s-2-6-4-9c-6-10-15-18-25-25z"></path><path d="M512 142h3c0 3 0 3 2 6 3 1 4 3 5 5v5c-1 3-1 5-1 8s1 6 1 10c-3-2-3-4-5-7v-1h-2c-2 0-4 0-6-1h-1-1c-1-1-1-2-2-3l1-1h0l-1-2-1-1c-1-2-1-6 0-8s2-3 3-4l2-1v-3l-1-1v-1l3 1h1v-1z" class="H"></path><path d="M512 142h3c0 3 0 3 2 6h-3c0 1 0 2 1 3h-1c0 1 0 1 1 2h-1l-2-2v-2h0l1-1c-1 0-1 0-2-1-1 0-1 0-1-1l-1-1v-1l-1-1v-1l3 1h1v-1z" class="D"></path><path d="M515 151h0c1 1 2 3 2 5 0 1-1 2-2 3h-4c-2-1-3-1-4-3 1-2 1-3 2-5 1-1 1-2 3-2h0v2l2 2h1c-1-1-1-1-1-2h1z" class="K"></path><path d="M522 158c-1 3-1 5-1 8s1 6 1 10c-3-2-3-4-5-7v-1h-2c-2 0-4 0-6-1h-1-1c-1-1-1-2-2-3l1-1h0c1 1 2 2 3 2 1 1 4 1 6 1 4-1 5-5 7-8z" class="Q"></path><path d="M594 669v-1l13-9c2 3 3 5 4 8 1 2 3 4 4 6 2 2 3 5 5 8l5 5c-3-2-6-4-8-7l-2-2-1-2c-1 0-1 0-1 1 0 2 2 2 1 4 0-1-1-2-2-2h-1l-1-1c-1 0-1 1-2 0h-1-1l-1 1c-1 0-1 0-2 1-3 2-3 5-3 9h0c0 1 0 3 1 4 0 2-1 4 0 6 0 1 0 2 1 4 0 3 1 6 1 10 1 2 1 5 1 8h0c-1-2-2-7-2-9v-2l-1-4-1-6-1-7c-1-1-1-3-1-4h0v-1h-2c0-3-1-6-2-10h0c1-2 1-2 0-4v-1c1-1 0-1 0-2h-1l1-1z" class="B"></path><path d="M594 669v-1l13-9c2 3 3 5 4 8 1 2 3 4 4 6 2 2 3 5 5 8l5 5c-3-2-6-4-8-7-1-3-4-9-6-11h-4-1c-2 0-5 1-7 3-1 1-1 3-1 5h-1v-3c0-2-1-3-3-4z" class="Q"></path><path d="M309 273c-1-1-2-4-3-6s-5-8-5-11h0 2l1 1 3 2c7 9 11 21 16 31 4 11 9 22 13 33h-3c-2 1-4 2-5 3h-1c-2 2-5 4-6 6l-1 1c0 1-1 1-1 2l-3 6v1c0 2-1 7 0 9s1 4 2 6 5 6 7 7h1c1 1 2 1 3 2h1 1c2 1 2 0 3 1h0v1c-2-1-3-1-5-1v-1c-2 0-4-1-5-2h-1c-2-1-4-4-5-6h-1c0-1 0-1-1-2 0-2-1-4-1-5-1-2-1-4 0-6v-2c0-2 1-3 1-5h1c0-2 1-3 2-5 1-1 1-2 2-3 2-2 4-4 7-6 1-1 4-1 5-2l-1-1-1-1c0-1 0-1-1-2-2-6-5-12-7-17s-4-10-6-14l-2-2-6-12z" class="B"></path><defs><linearGradient id="P" x1="408.808" y1="205.288" x2="423.344" y2="214.98" xlink:href="#B"><stop offset="0" stop-color="#8f8c8e"></stop><stop offset="1" stop-color="#cbc9c9"></stop></linearGradient></defs><path fill="url(#P)" d="M429 190h1 5c1 0 2 1 3 2v1 1l-1 1c-15 8-32 18-37 36l-1 1v1 2h-1l1 1c0 1-1 1-1 2v1h0c-1 2-1 3-1 5l1 1c0 1 0 2-1 3v1h-1l1 1c0 1-1 4 0 5 0 1 0 2 1 3v2 2 1c1 0 1 0 1 1h-1c-1-2-1-5-2-8-1-10-2-22 2-32 5-15 17-27 31-34z"></path><path d="M565 343c0-4 1-7 2-11 1-10 4-20 9-29 0 2-1 3-1 5v3c0 1-1 3-1 4s-1 3-1 3c0 1 1 3 1 4 2 6 6 11 8 16h0l-1-1-1 1 1 2c1 1 1 1 1 3l-1-1v1 2l3 4c0 1 1 1 1 2-5 1-11 1-17 1h-2l1-1c0-1 0-1 1-3h2 0c-2 0-3 0-4-1l-1-4z" class="H"></path><path d="M570 348c1 0 3-1 5-2-1-2-2-4-3-5l-1-2-1-1v-1-6l1-1c1-1-1-2 1-3h0c1 2 2 4 2 6l7 12 3 4c0 1 1 1 1 2-5 1-11 1-17 1h-2l1-1c0-1 0-1 1-3h2 0z" class="P"></path><path d="M570 348h12c-2-3-8-10-8-15h0l7 12 3 4c0 1 1 1 1 2-5 1-11 1-17 1h-2l1-1c0-1 0-1 1-3h2 0z" class="X"></path><path d="M566 352l1-1c0-1 0-1 1-3h2 0l1 1c1 1 1 1 3 1h-1c-2 1-3 1-5 2h-2z" class="J"></path><path d="M577 385l1 1v-1c0-1 0-1 1-2 0 3 0 5 1 7v1c2 5 5 9 8 13 0 1 0 1 1 2 1 2 2 3 3 4l-11 40c-2 5-2 12-5 16l-1-4c1 0 1-1 1-1 2-7 11-30 9-34-1-3-3-8-5-10-3-5-6-11-8-16v-8l1-1c1-3 2-5 4-7z" class="F"></path><path d="M552 662v2h3c-1 2-3 7-2 10 1 1 0 1 1 1l3 1-2 1c1 1 2 1 3 2h3 2l2 2 2-2c0 1-1 2-1 3h-1c-1 0-1 1-2 1v3c-1 3-3 5-5 8-2 4-4 9-8 14h-4c-1 0-2-1-3-1v-3c1-2 0-4 1-5v-3l-2 1 5-19c2-5 3-11 5-16z" class="V"></path><path d="M555 677c1 1 2 1 3 2-2 3-2 4-2 8l-4-3v-2c0-2 2-4 3-5z" class="H"></path><path d="M558 679h3 2l2 2 2-2c0 1-1 2-1 3h-1c-1 0-1 1-2 1v3c-1 3-3 5-5 8-2 4-4 9-8 14h-4c-1 0-2-1-3-1v-3c1-2 0-4 1-5v-3l1-1c0-1 1-2 3-2l2-1s0 1 1 1c2 1 3 3 3 5h0 1c1-2 4-7 3-9l-2-2c0-4 0-5 2-8z" class="C"></path><path d="M544 696l1-1c0-1 1-2 3-2l1 2h3v1s-1 0-1 1l1 1h-1v-1l-2-1v1h-2c-1 1-1 2-3 2v-3z" class="H"></path><path d="M544 699c2 0 2-1 3-2h2v-1l2 1-3 1-1 1v1h0c1-1 1 0 1 0h1l-2 2 1 1c1 0 1-1 2-1l1-2v-1l2 2c-1 1-2 3-3 4-3 0-2-2-4-2 0 0-2 0-2 1h-1c1-2 0-4 1-5z" class="P"></path><path d="M455 738h2v-1c2 1 2 2 3 3s1 1 2 1v-1l-1-2h1c1 4 3 7 4 10 2 6 4 12 5 18 1 4 3 7 3 10 1 5 3 9 4 13-2-2-3-6-6-7-1-1-2-3-3-3-2 0-4 2-5 2h-1l-3 1-1-1 1-1s0 1 1 1c0-1 1-1 2-2h0c1-2 0-4 0-6 0 0-1-4-1-5s0-2-1-3c0-1-1-2-1-3s0-1-1-2v3l-1-1h-1v-1l-2-1-1-2c0-2 1-3 2-4v-3l-1-1c1-1 1-1 1-2h0c-1-2-1-2-2-3 0 1 0 1-1 2l-1-1c-1-1-1-2-2-4h0 3l-1-1v-2h4l-1-1z" class="F"></path><path d="M456 754c1-2 2-3 4-3h1l-4 8-2 1-1-2c0-2 1-3 2-4z" class="P"></path><path d="M455 738h2c0 1 1 1 2 2h0l3 6-9-4-1-1v-2h4l-1-1z" class="O"></path><path d="M462 768l1-3v-1 2h1c0 1 1 8 2 9h0c-1 2 0 3-1 5l-2 1-3 1-1-1 1-1s0 1 1 1c0-1 1-1 2-2h0c1-2 0-4 0-6l-1-5z" class="B"></path><path d="M459 740c2 2 3 5 4 8 3 7 4 13 6 20 2 3 2 6 5 8 1 5 3 9 4 13-2-2-3-6-6-7-1-1-2-3-3-3-2 0-4 2-5 2h-1l2-1 1-1c2-1 3-1 5 0h0c1-2-8-27-9-32v-1l-3-6z" class="V"></path><path d="M457 737c2 1 2 2 3 3s1 1 2 1v-1l-1-2h1c1 4 3 7 4 10 2 6 4 12 5 18 1 4 3 7 3 10-3-2-3-5-5-8-2-7-3-13-6-20-1-3-2-6-4-8h0c-1-1-2-1-2-2v-1z" class="K"></path><defs><linearGradient id="Q" x1="381.655" y1="663.931" x2="367.193" y2="698.1" xlink:href="#B"><stop offset="0" stop-color="#d0cece"></stop><stop offset="1" stop-color="#fefefd"></stop></linearGradient></defs><path fill="url(#Q)" d="M369 666c-1-1-1-2-1-3 1-1 7 0 9 0 3 1 6 1 9 3 2 2 5 3 7 6-1 2-2 4-4 6-2 3-4 5-5 8-2 2-3 4-5 6 0 0 0 1-1 2l-1-1c-2 2-5 5-6 7-1-1-1-2-2-4 0-1 0 1 0-1v-1c-1 0-1-1-1-2-1 0-1 0-1-1 3-3 3-6 4-10 0-1 1-7 0-9v-1c-1-2-1-3-2-5z"></path><path d="M427 656c5 1 9 3 14 3l9 3 3 9c1 2 1 4 1 5v2c0 1 0 2 1 4v3l-1 1c-1 0-2 1-3 2v1l2-1c0 2-1 3-1 5v1 1h0c1 1 1 1 1 2l1-1v-1h5l1 2h-1v2 1 1 1l-1-2c-1 0-1-1-2-1l-1 1 1 1h0c0 1 0 1 1 2v1c-2-1-2-2-2-3l-1-2c-1 1-2 1-2 2-5-9-10-19-17-27 0-1-1-3-1-5h0c0-3-3-6-5-7h-1l-3-4c-1 0-1-1 0-1 0-1 0-1 1-1h1z" class="T"></path><path d="M444 678c1-1 2-1 4-2h0 1c1-1 4 0 5 0v2c0 1 0 2 1 4v3l-1 1c-1 0-2 1-3 2-1 0-2 1-2 2h-1 0c-1-2-2-3-3-5-1 0-1 0-1-1-1-2-2-3-3-5v-1-1h1l1 1h1z" class="C"></path><path d="M427 656c5 1 9 3 14 3l9 3 3 9c1 2 1 4 1 5-1 0-4-1-5 0h-1 0c-2 1-3 1-4 2v-1c1-1 1-1 2-1l1-1h2c1-1 1-1 2-1h1c-1-1-1-2-1-3l-1-2-2-4h-1-1c-2 0-3 1-4 2l-2 1c0 1 1 2 0 3h-1c0-2-1-4 0-5 0-1 1-2 1-3-1 0-1-1-2-1h-1c0 1-1 1 0 3h0-1-1c-1-1-2-1-2-2-1-1-3-2-4-3v2h-1l-3-4c-1 0-1-1 0-1 0-1 0-1 1-1h1z" class="D"></path><defs><linearGradient id="R" x1="267.039" y1="209.854" x2="264.461" y2="215.146" xlink:href="#B"><stop offset="0" stop-color="#444143"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#R)" d="M253 202c18 9 35 27 46 43 3 4 6 9 8 14l-3-2-1-1h-2 0c0 3 4 9 5 11s2 5 3 6c-2 0-4-1-4-2-1-2-2-3-2-5-1 0-1 0-1-1-1-1-1-1-1-2l-4-5c0-1 0-2-1-2v-1h0c-1-1-1-2-2-3 0 0 0-1-1-2h0l-1-2c-1-1-1-2-2-3 0-1-1-1-1-2s0 0-1-1l-5-7c-11-14-28-24-44-31h6v1c3 0 5 1 7 2l-1-2v-2h0 1l1-1z"></path><path d="M364 651c5-1 9 0 13 0 6 1 13 2 18 5 1 0 2 1 3 1l1 1c0 5-3 10-6 14-2-3-5-4-7-6-3-2-6-2-9-3-2 0-8-1-9 0 0 1 0 2 1 3l-3-3-1 1-1-1-1 1h-1c0 1 1 1 1 2h1v1h0c-1 0 0 0-1 1l1 1-2 1v2c1 1 3 2 4 3l-4 4v1l-1-1v-1l-1-1v4h0l-2-1-1 1h-1c2-2 3-2 3-4h-1c-1 0-2 1-2 1l-2-1v-2l2-2c1 0 2-1 3-1l1-3-1-1c0-2-1-2-2-4 0-5-1-1-4-3 1-1 1-1 1-2h1 0 3l1-3c1-2 4-3 6-4h2 0c2 0 2 0 3-1h-6z" class="B"></path><path d="M362 664v-4h1l3 3-1 1-1-1-1 1h-1z" class="I"></path><path d="M364 651c5-1 9 0 13 0 6 1 13 2 18 5 1 0 2 1 3 1 0 3-1 6-2 7h-1-1c-7-6-17-6-26-6-2 0-4-1-5 0h-1 0l2-3 2-2-1-1h2 0c2 0 2 0 3-1h-6z" class="S"></path><path d="M364 651c5-1 9 0 13 0 6 1 13 2 18 5-3 1-6-1-9-1-2 0-4-1-5-1-4 0-7 0-11 1-2 0-4 1-6 0l2-2-1-1h2 0c2 0 2 0 3-1h-6z" class="E"></path><path d="M590 672c2 0 2 0 3 2l1-1c1 2 1 2 0 4h0c1 4 2 7 2 10h2v1h0c0 1 0 3 1 4l1 7 1 6 1 4v2c0 2 1 7 2 9h0c1 3 4 9 3 12h-1v6h0c-1-2-1-5-2-7v-1l-3 1c0-1 1-3 0-4v-1c-1-1-1-2-2-4l-1-7-2-7c-1-6-2-11-4-16h-2c0 1 0 1-1 2l-1 1-1-1c-1 0-1 0-2 1h-5l-5 1-5 3v-1l1-1h-1-1c-1 1-3 2-5 3l2-3c4-2 9-7 12-11 2-6 7-10 12-14z" class="L"></path><path d="M596 708v-1c1-3-1-7-1-9l-1-3h1c0 2 1 3 1 5 2 10 6 19 6 29h1l1 1-3 1c0-1 1-3 0-4v-1c-1-1-1-2-2-4l-1-7-2-7z" class="B"></path><defs><linearGradient id="S" x1="587.05" y1="688.44" x2="570.943" y2="688.556" xlink:href="#B"><stop offset="0" stop-color="#3e3e40"></stop><stop offset="1" stop-color="#6d6869"></stop></linearGradient></defs><path fill="url(#S)" d="M578 686l4-4c1-1 2-2 4-2h1c1 1 1 2 1 3h-3c-1 0-2 1-3 2-2 3-6 7-7 11l-5 3v-1l1-1h-1-1c-1 1-3 2-5 3l2-3c4-2 9-7 12-11z"></path><path d="M582 685c2 0 5-1 7 0 1 2 2 4 3 7h-2c0 1 0 1-1 2l-1 1-1-1c-1 0-1 0-2 1h-5l-5 1c1-4 5-8 7-11z" class="M"></path><path d="M587 694c0-1 0-1-1-2l-3 1v-1l1-1c1-1 2-1 4-1 1 0 1 0 2 2 0 1 0 1-1 2l-1 1-1-1z" class="c"></path><path d="M594 673c1 2 1 2 0 4h0c1 4 2 7 2 10h2v1h0c0 1 0 3 1 4l1 7 1 6 1 4v2c0 2 1 7 2 9h0c1 3 4 9 3 12h-1v6h0c-1-2-1-5-2-7v-1l-1-1-2-14c-2-12-6-24-12-35 0-1-1-3-1-3 0-1 1-2 1-3h4l1-1z" class="N"></path><path d="M372 584c-1 1-1 0-1 0l-1-1v-1h0c-1-2-1-4-1-5l-1-1v-3l-1-1v-1s0-1-1-1l-1-2c0-2-1-4-2-5v-1c-1-1-2-1-3-3 0 0 0-1-1-1v-2c-1-1 0-2-1-3 0-1-1-1-1-3h-1v1c1 2 0 3 1 4v3 1c1 1 0 2 1 3v2 1h0l1 2c0 3 0-1 0 1v1l1 2v2 1l1 1v3c-2-8-4-16-5-24-2-12-1-25 0-37 0-6 0-10 1-15 0-2 1-4 2-5v1 1c-1 2-1 3-1 5v3c1-1 2-3 4-5 1 0 3-1 5 0l2 3c0 2 0 4-1 7-2 4-3 7-3 12v10l-1 3c-2 14 3 30 7 43 0 1 1 4 1 4z" class="Z"></path><defs><linearGradient id="T" x1="215.8" y1="188.236" x2="214.199" y2="197.406" xlink:href="#B"><stop offset="0" stop-color="#282628"></stop><stop offset="1" stop-color="#4b494a"></stop></linearGradient></defs><path fill="url(#T)" d="M180 182c2 0 5 0 6 1 13 1 26 4 39 8 9 2 19 6 28 11l-1 1h-1 0v2l1 2c-2-1-4-2-7-2v-1h-6c-4-2-9-4-14-5-16-5-34-7-51-10 1-2 4-4 5-6l1-1z"></path><path d="M180 182c2 0 5 0 6 1v1h1-3c-1 0-1 0-2-1h-3l1-1z" class="D"></path><path d="M511 608l1-1c9-1 17-4 26-6l-8 29-1 4-3 14c0 1 0 2-1 3l-14-43z" class="S"></path><path d="M519 627v-1-2c-1-1-1-4-1-5 1 0 1 0 1 1 1 2 3 4 5 5h0c2 1 3 2 4 3v1l1 1 1-1v1l-1 4-3 14v-1c0-1 0-2-1-3 0-2 0-3-1-5 0-1-1-2-1-3l-2-5c0-1 0-1-1-2l-1-2z" class="H"></path><path d="M519 627l2-2c0 1 0 2 1 3 0 2 3 4 2 7h1v2l1 1 1-1-1-1h0l-1-2c1-1 3 0 4 0l-3 14v-1c0-1 0-2-1-3 0-2 0-3-1-5 0-1-1-2-1-3l-2-5c0-1 0-1-1-2l-1-2z" class="W"></path><path d="M559 319h1c-1 2-1 4-2 5s-1 2-1 3l1 2c-3 6-4 14-5 21v2c-1 1-1 1-1 2-1 3-1 7-1 11 0 3-2 9-1 13v1 2c0 1-1 1 0 2 0 2 0 6 1 8l7 32v2l-1-1c0 1-1 1-2 1l-1 1c1 1 1 1 3 1v1h-3-1v1h0 0l-1-1v-1h-1c-4-11-3-23-8-34h1c0 1 1 1 1 2v-1l-1-14-1-4 1-2v-6l1-1v2h0v-9c1-2 1-5 1-6v-2c1-2 1-2 1-3v-3c1-1 1-1 2-1 1-2 2-6 2-9 0-1 0-2 1-3l4-11c0-1 1-2 1-3l1 1 1-1z" class="T"></path><path d="M547 377c-1-11 0-21 2-31l1 2h1c0 3-1 6-1 9-1 3 0 8-1 10-1 3-1 7-1 10h-1 0z" class="G"></path><path d="M547 377h0 1c0-3 0-7 1-10l1 14c0 1-1 1 0 2 0 2 0 6 1 8l7 32v2l-1-1c-5-15-9-31-10-47z" class="O"></path><defs><linearGradient id="U" x1="549.729" y1="329.749" x2="556.203" y2="346.362" xlink:href="#B"><stop offset="0" stop-color="#908c8e"></stop><stop offset="1" stop-color="#b4b2b2"></stop></linearGradient></defs><path fill="url(#U)" d="M559 319h1c-1 2-1 4-2 5s-1 2-1 3l1 2c-3 6-4 14-5 21v2c-1 1-1 1-1 2-1 3-1 7-1 11 0 3-2 9-1 13v1 2l-1-14c1-2 0-7 1-10 0-3 1-6 1-9h-1l-1-2v-1c1-2 2-6 2-9 0-1 0-2 1-3l4-11c0-1 1-2 1-3l1 1 1-1z"></path><path d="M612 657c1 0 1-1 2-1 1-1 1-1 2-1s1 0 2-1c8-3 19-5 27-4 0 2 0 2 1 3h1c1 0 2 0 3 1h1l1 1c-1 0-2 0-3 1h-1-1c-2-1-4-3-6-3h0c1 1 3 2 4 4 1 1 1 2 1 4-1 1-3 3-5 4l3-3c1-2 1-2 1-3l-1-1c0 1-1 1-1 2-1 1-1 1-2 1h-1c-2 1-5 0-6 1h-2c-2 0-2 0-3 1-2 0-4 1-6 1v1l-1 1c-2 1-4 2-5 4l-1 1s-1 1-1 2c-1-2-3-4-4-6-1-3-2-5-4-8l-13 9v1l-1 1h1c0 1 1 1 0 2v1l-1 1c-1-2-1-2-3-2-5 4-10 8-12 14-3 4-8 9-12 11l1-4 1-1c4-8 11-15 18-22 1 0 2-1 3-1l2-2c5-3 9-6 14-9 1-1 3-2 4-2l4-2-1 3z" class="a"></path><path d="M568 692c8-5 11-18 22-20-5 4-10 8-12 14-3 4-8 9-12 11l1-4 1-1z" class="K"></path><path d="M612 657c1 0 1-1 2-1 1-1 1-1 2-1s1 0 2-1c8-3 19-5 27-4 0 2 0 2 1 3h1c1 0 2 0 3 1h1l1 1c-1 0-2 0-3 1h-1-1c-2-1-4-3-6-3h0c1 1 3 2 4 4h0c-11-2-25 2-34 7v-1c0-1 0-4 1-6z" class="C"></path><path d="M517 169c2 3 2 5 5 7l2 8c3 10 4 20 7 29 5 19 9 39 16 58 2 6 5 10 7 16l1 1v2 3c-2-3-3-5-5-8v1l4 8h-12c0 1 2 2 3 3h-2c-2-1-2-2-3-3l1-1c-2-4-7-7-9-12l1-1 7 11c1 1 2 2 3 2s1 0 2-1l-2-2 1-1c1 0 1 1 2 0-1-1-1-2-1-3l-1-1h0l-1-3v-2l-1-2v-1-2c-1-1-1-2-1-3l-1-2c0-2-1-3-1-5l-1-1v-1-2l-4-9v-3c-1-2-1-1-1-2v-2c-1-1-1-1-1-2v-1c0-5-3-10-2-15l-7-34c-1-5-2-11-4-16 0-3-1-5-2-8z" class="B"></path><path d="M517 169c2 3 2 5 5 7l2 8c-2 4 0 10 1 14 4 26 11 53 21 77l4 10v1c-3-4-4-9-6-13-5-15-10-30-14-46l-7-34c-1-5-2-11-4-16 0-3-1-5-2-8z" class="V"></path><path d="M530 227c4 16 9 31 14 46 2 4 3 9 6 13l4 8h-12c0 1 2 2 3 3h-2c-2-1-2-2-3-3l1-1c-2-4-7-7-9-12l1-1 7 11c1 1 2 2 3 2s1 0 2-1l-2-2 1-1c1 0 1 1 2 0-1-1-1-2-1-3l-1-1h0l-1-3v-2l-1-2v-1-2c-1-1-1-2-1-3l-1-2c0-2-1-3-1-5l-1-1v-1-2l-4-9v-3c-1-2-1-1-1-2v-2c-1-1-1-1-1-2v-1c0-5-3-10-2-15z" class="O"></path><path d="M338 380c2 4 8 6 12 8l1 1c1 2 1 6 1 9l-1 2c2 1 5 1 7 0v1c0 1 1 1 1 2v1c1 2 3 3 3 5 3 5 4 11 6 16l-1 3c-1-1-2-2-4-3s-4-2-7-1v-1l-1 1v-1l-1 1-2-1-1-1c-2-1-4-4-6-5 0-1-1-2-2-3v-1c-1-1-1-2-1-2-1-1-2-2-2-3-1-3-2-5-2-8-1-1-1-2-1-3-1-2 0-4 0-6-1-1-1-2-1-3l1-1c-1-3-1-5 1-7z" class="b"></path><path d="M351 389c1 2 1 6 1 9l-1 2h-1c-1 0-1 0-1-1-1-4 0-7 2-10zm20 229c-19-26-30-60-29-92l2-17c0-4 1-7 1-10 1-2 0-4 0-6l1 1v2c1 2-1 7-1 10-1 3-1 6-1 9-1 2-1 5-1 8-1 7 0 16 1 23h1v4c1 2 2 4 2 6 2 3 3 9 4 12 3 6 4 15 9 20 1 3 2 5 4 7l2 4h-1v-1h-1v-1h-3v2h0c1 1 1 0 1 1 1 2 2 3 3 5l1 1-1 1c13 23 37 39 62 49h-1c-1 0-1 0-1 1-1 0-1 1 0 1l3 4h1c2 1 5 4 5 7h0c0 2 1 4 1 5l-10-10c-8-8-17-14-27-20-4-1-8-3-12-4-3-1-6-2-10-3-4 0-9 0-13-1h0 2l1-1h1v-1c1 0 1 0 1-1l2-5v-1c0-3 1-6-1-8l2-1z" class="P"></path><path d="M345 546v4c1 2 2 4 2 6 2 3 3 9 4 12l2 8 1 2-1 1c-1-1 0-1-1-2l-1-2h0-1c0-1-1-3-1-4-3-8-4-17-5-25h1z" class="F"></path><path d="M351 568c3 6 4 15 9 20 1 3 2 5 4 7l2 4h-1v-1h-1v-1h-3v2h0c1 1 1 0 1 1 1 2 2 3 3 5l1 1-1 1c-7-9-11-21-15-32h1 0l1 2c1 1 0 1 1 2l1-1-1-2-2-8z" class="B"></path><path d="M371 618c1 0 5 5 5 6l14 13c3 2 7 5 8 7-4-1-8-3-12-4-3-1-6-2-10-3-4 0-9 0-13-1h0 2l1-1h1v-1c1 0 1 0 1-1l2-5v-1c0-3 1-6-1-8l2-1z" class="N"></path><path d="M375 633c0 1 0 2-1 3l-1-1c-2-1-1-6-1-8 2 1 4 3 5 5l-2-1v1 1z" class="b"></path><defs><linearGradient id="V" x1="392.229" y1="637.284" x2="376.276" y2="636.145" xlink:href="#B"><stop offset="0" stop-color="#9e9b9d"></stop><stop offset="1" stop-color="#c4c1c2"></stop></linearGradient></defs><path fill="url(#V)" d="M376 624l14 13c3 2 7 5 8 7-4-1-8-3-12-4-2-2-4-4-6-4s-4-2-5-3v-1-1l2 1h1c1 0 3 1 4 2l1-1c-1-2-3-2-3-4-1-2-3-3-4-5h0z"></path><path d="M714 241h1l1-1 7-7s1-2 2-2c3-1 5-5 8-7l2-2h1l1-1 2-1h1 1c1 0 2 0 3-1 0 0 0-1 1-1s1 0 2-1l3-1v-1h1c1-2 3-2 5-3-9 6-17 12-25 20-26 24-41 58-56 89 8 4 14 9 17 18 3 5 3 11 0 16a19.81 19.81 0 0 1-11 11c-7 3-16 2-23 0-2-1-2 0-4-2l1-1c1 1 2 1 3 2h0c8 2 16 3 24-1 5-2 8-6 10-11 2-6 1-11-2-16-3-7-8-12-16-14l-6 14h0c1 2 2 4 2 5 1 4-1 9-3 12 0-1 0-2 1-3v-1-1h0c1-4 1-7-1-10-1-1-1-2-2-2h-2 0c1-4 4-9 6-13 9-23 20-45 33-66 4-6 7-12 12-17z" class="S"></path><path d="M667 354c2-3 4-8 3-12 0-1-1-3-2-5h0l6-14c8 2 13 7 16 14 3 5 4 10 2 16-2 5-5 9-10 11-8 4-16 3-24 1h0v-2-1-1s0-2 1-2l2-1c3-1 5-2 6-4z" class="M"></path><path d="M714 241h0c3-5 8-10 12-14 20-20 45-33 72-39 13-3 27-4 41-7 0 0 1 0 1 1v1c1 1 1 1 1 2v1c-1 1-2 1-2 2-2 0-2 0-3 1h-2c-1 0-2 1-3 2l-25 4c-10 1-19 4-28 7-7 3-14 7-22 10-2 1-4 1-5 3h-1v1l-3 1c-1 1-1 1-2 1s-1 1-1 1c-1 1-2 1-3 1h-1-1l-2 1-1 1h-1l-2 2c-3 2-5 6-8 7-1 0-2 2-2 2l-7 7-1 1h-1z" class="D"></path><path d="M484 488c5 1 11 2 15 5h0c3 2 5 4 7 6 2 3 3 6 4 9 0 4 0 7-1 10h-1c-1 2-2 3-4 5l4-1h2c-2 2-4 3-6 4-1 1-3 2-4 3h-4l-4-1c-1-1-1 0-2-1s-1-1-3-2c-1 0-1 0-2-1-1-4-3-8-5-12l2-1h2v-3l1-1-3-3v1l-4-2c-1-1-1-2-2-4l-1-3 7-6c1 0 2-2 2-2z" class="I"></path><path d="M486 511c0-1 1-2 2-3 2-2 4-4 7-4l2 2c-1 1-1 0-2 1-3 1-5 5-7 6l-2-2z" class="c"></path><path d="M493 501c2 0 3 1 5 1 0 1 1 1 2 2l1 1c0 1-1 1-1 2l-1-1c-1-1-2-2-4-2-3 0-5 2-7 4-1 1-2 2-2 3l-1 2 1 1-2 1c1 1 2-1 2 1l-2 2c2 0 2-1 3 1-1 0-1 1-1 2h1 0l-1 2c1 0 1 0 2 1l-1 1c-1 0-1 0-2-1-1-4-3-8-5-12l2-1h2v-3l1-1c0-1 0 0 1-1h0v-1l-1-2v-1c1 1 2 1 2 2 1 1 1 0 2 1v-1c1 0 1-1 2-1v-1l2-1z" class="D"></path><path d="M500 507l3 3 1-2c0 1 1 1 1 1 1 0 1 0 2 1h1l2-2c0 4 0 7-1 10h-1c-1 2-2 3-4 5-2 1-5 2-8 1l-1-1c-1-1-3-2-3-4 0-1-1-5-1-6 1-2 2-3 4-4 2 1 2 3 4 4l1-1c0-2 0-2-1-4v-1h1z" class="U"></path><path d="M496 524l1-1h2v-1h-1-1c-1-1-2-2-3-2 2-3 0-6 1-7h2v3l1 1h2c-1 2-2 3-3 4h1l3-3c1-1 1-2 2-3l1 1h0c-1 2-1 3-1 4h1l3-2h1c-1 2-2 3-4 5-2 1-5 2-8 1z" class="K"></path><path d="M500 507l3 3-1 3c-2 1-2 2-4 4l-1-1v-3h-2c-1 1 1 4-1 7 1 0 2 1 3 2h1 1v1h-2l-1 1-1-1c-1-1-3-2-3-4 0-1-1-5-1-6 1-2 2-3 4-4 2 1 2 3 4 4l1-1c0-2 0-2-1-4v-1h1z" class="J"></path><path d="M484 488c5 1 11 2 15 5h0c3 2 5 4 7 6 2 3 3 6 4 9l-2 2h-1c-1-1-1-1-2-1 0 0-1 0-1-1l-1 2-3-3c0-1 1-1 1-2l-1-1c-1-1-2-1-2-2-2 0-3-1-5-1l-2 1v1c-1 0-1 1-2 1v1c-1-1-1 0-2-1 0-1-1-1-2-2v1l1 2v1h0c-1 1-1 0-1 1l-3-3v1l-4-2c-1-1-1-2-2-4l-1-3 7-6c1 0 2-2 2-2z" class="C"></path><path d="M499 493c3 2 5 4 7 6 2 3 3 6 4 9l-2 2v-2c-1-2-1-4-2-5l-1-2c-1-1-1-2-2-3v-1c-1-1-3-2-4-3v-1h0z" class="E"></path><path d="M476 499h4 0c2-2 3-4 5-5h6c1 1 1 2 2 4h2l5 4c2 2 5 6 8 6v2h-1c-1-1-1-1-2-1 0 0-1 0-1-1l-1 2-3-3c0-1 1-1 1-2l-1-1c-1-1-2-1-2-2-2 0-3-1-5-1l-2 1v1c-1 0-1 1-2 1v1c-1-1-1 0-2-1 0-1-1-1-2-2v1l1 2v1h0c-1 1-1 0-1 1l-3-3v1l-4-2c-1-1-1-2-2-4z" class="P"></path><path d="M482 504c-1-1-1-1-1-2-1-1-1-2 0-3l2 1 3-3h0 1v1 1h1 3v2h2l-2 1v1c-1 0-1 1-2 1v1c-1-1-1 0-2-1 0-1-1-1-2-2v1l1 2v1h0c-1 1-1 0-1 1l-3-3z" class="W"></path><defs><linearGradient id="W" x1="371.446" y1="511.564" x2="391.382" y2="522.521" xlink:href="#B"><stop offset="0" stop-color="#b9b7b8"></stop><stop offset="1" stop-color="#dbd9da"></stop></linearGradient></defs><path fill="url(#W)" d="M389 491c0-1 1-2 2-2 1 1 2 3 3 4v1h1c0 3 0 4-1 7l-4 11c-2 4-4 8-5 13 0 2-1 5-1 8l-1 5-1 3h-1l-1 2c-1-1-2-1-4-2h-5c-2 1-4 1-5 2-1-1-1-2-1-3s-1-2-1-3l1-3v-10c0-5 1-8 3-12 1-3 1-5 1-7-1-3 0-3-2-5 1-1 1-2 2-3 0 1 1 2 1 2 0 1-1 1 0 2h1l1 1v2l1 1c0-3 0-3 2-5l1-3c0 1 1 3 3 3v-1c0-2 0-4 1-5l1 1c0-1 0-2 1-3h1l1 1c2 0 3-1 5-2z"></path><path d="M389 491c0-1 1-2 2-2 1 1 2 3 3 4v1c-2 3-3 5-5 7v-1h-1l-2 2c0-1 0-1-1-1-1-2-2-3-4-5v-1c0-1 0-2 1-3h1l1 1c2 0 3-1 5-2z" class="H"></path><path d="M381 496l4-2c2 3-2 4 3 6l-2 2c0-1 0-1-1-1-1-2-2-3-4-5z" class="W"></path><path d="M389 491c0-1 1-2 2-2 1 1 2 3 3 4v1c-2 3-3 5-5 7v-1-2l-2-2c-1-3 1-3 2-5z" class="T"></path><path d="M377 528c0-1 1-1 1-1 2-1 3-4 3-7 1 1 1 3 1 3 1 2-1 6-2 7v1c-1 2-2 4-1 6v1l-1 1 1 1h2v1l-1 2c-1-1-2-1-4-2h-5l-1-1h1c1-2 2-3 3-4l-1-1c2-2 1-5 4-7z" class="B"></path><path d="M374 536h3v1h0l-2 1c0 1 1 1 1 2v1h-5l-1-1h1c1-2 2-3 3-4z" class="G"></path><path d="M365 524c2 2 3 3 6 3h0l6 1c-3 2-2 5-4 7l1 1c-1 1-2 2-3 4h-1l1 1c-2 1-4 1-5 2-1-1-1-2-1-3s-1-2-1-3l1-3v-10z" class="L"></path><path d="M365 524c2 2 3 3 6 3h0v1c-1 1-1 2-1 3s-1 1-1 2c1 1 2 2 2 3-2 1-3 1-4 1-2-1-2-2-2-3v-10z" class="D"></path><path d="M368 512c1 2 2 3 3 5h1c1 1 1 1 1 3h1v-2h3l1 1 2-1c1 1 1 1 1 2 0 3-1 6-3 7 0 0-1 0-1 1l-6-1h0c-3 0-4-1-6-3 0-5 1-8 3-12z" class="H"></path><path d="M376 497c0 1 1 3 3 3v-1l2 11c1 2 2 5 1 7h-2v1l-2 1-1-1h-3v2h-1c0-2 0-2-1-3h-1c-1-2-2-3-3-5 1-3 1-5 1-7-1-3 0-3-2-5 1-1 1-2 2-3 0 1 1 2 1 2 0 1-1 1 0 2h1l1 1v2l1 1c0-3 0-3 2-5l1-3z" class="J"></path><path d="M372 508c1-1 1-1 1-2h1c1 1 1 2 3 2v3 1h1 1l1 1h-1 0-1c-1 1-1 2-2 2s-1-1-2-2l-1 1-1-2v-1c0-1-1-2 0-3z" class="L"></path><path d="M369 505c-1-3 0-3-2-5 1-1 1-2 2-3 0 1 1 2 1 2 0 1-1 1 0 2 0 2 1 4 2 7-1 1 0 2 0 3v1l1 2c0 2 0 1-2 3-1-2-2-3-3-5 1-3 1-5 1-7z" class="K"></path><path d="M376 497c0 1 1 3 3 3v-1l2 11v1l-3-3c-1 0-1-1-2-1l-1-1v-6l1-3z" class="L"></path><path d="M648 390l11-3-1 1c-1 2-1 3-1 4l-1-1c-2 1-4 5-5 8l-1 1-1 2v1 1h1-1c-4 6-6 12-8 18-3 5-5 8-5 14 1 2 1 4 3 5h1v3c-1 2-1 4 0 6-1 1-1 2-2 2 0 2 0 1-1 3v3l2 2 1 1h0c-2 0-4-1-5-1 1 2 5 8 4 10l-4-7c0-1-1-2-1-3-1-1-4-1-6-2l-7-1c-1 0-2-1-3-2v-2l-1-1c0-1 1-3 1-4l3-8 18-45c1-1 2-2 2-3s1-2 2-3c2 0 3 0 5 1z" class="N"></path><path d="M639 395c1-1 2-2 2-3s1-2 2-3c2 0 3 0 5 1-1 5-6 9-8 14-3 5-5 11-7 16-1 3-1 5-1 8 0 1 0 4-1 6-2 2-2 3-3 6-1 2-1 2 1 5l5 10c0 1 1 3 2 4 1 0 2 1 3 1l1 1h0c-2 0-4-1-5-1 1 2 5 8 4 10l-4-7c0-1-1-2-1-3-1-1-4-1-6-2l-7-1c-1 0-2-1-3-2v-2l-1-1c0-1 1-3 1-4l3-8 18-45z" class="E"></path><defs><linearGradient id="X" x1="618.474" y1="446.454" x2="628.026" y2="453.003" xlink:href="#B"><stop offset="0" stop-color="#323031"></stop><stop offset="1" stop-color="#565455"></stop></linearGradient></defs><path fill="url(#X)" d="M617 452c0-1 1-3 1-4l3-8c1 2 1 3 1 5 1 4 5 7 8 10 1 2 3 3 5 3v1c-2 0-4-1-5-1-4-2-8-3-12-5l-1-1z"></path><path d="M510 521h2c2-3 3-6 4-10l1 1c0 1 0 2-1 3 0 1-1 3-1 5l1-1h2v1l2-1v1c2 1 2 1 4 1h0l3-3v1c0 1 1 2 1 3l1-1 1-1 1-1v1c1 0 2 1 3 2v1c1 0 1 0 2-1v1c1 0 1 0 2-1v2h3 0c1 1 2 1 3 1l-2 2 2 1c1 0 1-1 2-1l1 1h0c0 2 0 4-1 7h0c-1 1-1 2-1 3v1l-1 2 1 1h-2-5l-4-1c-1-1-3-2-4-3v-2c-1 1-1 1 0 2h-1l-2-1h-2c0 1 0 2 1 3-2-1-3-2-5-3l1 2-1 1c-1 1-2 3-3 4l1 2c-1 1-3 2-4 3-1 2-3 3-5 4h-1l-3 1v-1c0-1 1-2 1-3h-1c-3 0-5-2-7-4 0 0-2-6-2-7h-1c1-1 1-1 1-2l-1-1c0-1-1-2-1-3 1-1 1-3 1-4h4c1-1 3-2 4-3 2-1 4-2 6-4v-1z" class="K"></path><path d="M520 534c2 0 2 0 3 2h-2v1l1 2-1 1c-1 1-2 3-3 4l1 2c-1 1-3 2-4 3-1 2-3 3-5 4h-1l-3 1v-1c0-1 1-2 1-3h-1v-1c1 0 1-1 1-2l1-1v2 1l2-2v1l1 1 1-1v-1l1-1 1-1 1-1c0-1 0-1 1-2s2-3 4-4l-1-1h0c1-1 1-2 1-3z" class="J"></path><path d="M507 550h1l1 1 1 2h0-1l-3 1v-1c0-1 1-2 1-3z" class="G"></path><path d="M500 530h0c2-2 7-3 10-5h0 1l-1 1v2l-1 1h2l-1 2h0c-1 0-1 0-1 1l-1-1-1 1 2 2h0-1c-1-1-1-1-2-1l-1-1c-1 1-2 2-2 4s1 3 3 5l1-1v1c-1 1-2 2-2 3h1v-1c1-1 2-1 3-1h1c0 1-1 1-1 3 1 0 1 0 2-1 0 2 0 2-1 3l-2 2v-1-2l-1 1c0 1 0 2-1 2v1c-3 0-5-2-7-4 0 0-2-6-2-7h-1c1-1 1-1 1-2l-1-1c1-2 1-3 2-5 1 0 1 0 2-1z" class="S"></path><path d="M496 536c1-2 1-3 2-5 1 0 1 0 2-1 0 2 0 4-1 5 0 1-2 1-1 2h1v1c1 1 0 1 1 1l3 3v1c-1 0-1 1-2 1v1c1 0 2 0 3-1 1 1 1 2 1 3 1 1 1 2 1 2v1c-3 0-5-2-7-4l-2-7h-1c1-1 1-1 1-2l-1-1z" class="R"></path><path d="M510 521h2c2-3 3-6 4-10l1 1c0 1 0 2-1 3 0 1-1 3-1 5l1-1h2v1l2-1v1c2 1 2 1 4 1h0l3-3v1c0 1 1 2 1 3l-1 1c1 0 1 0 1 1l-1 1h0 0-3 0l-1 1v2 1c1 1 1 2 1 2h2v1c0 1 0 0-1 1v1l1 1 1 2h-2c0 1 0 2 1 3-2-1-3-2-5-3v-1h2c-1-2-1-2-3-2l-1-1h-2v-1l-1 1-2 1-1-1v-3c-1-1-1-1-2-1h-2l1-1v-2l1-1h-1 0c-3 2-8 3-10 5h0c-1 1-1 1-2 1-1 2-1 3-2 5 0-1-1-2-1-3 1-1 1-3 1-4h4c1-1 3-2 4-3 2-1 4-2 6-4v-1z" class="a"></path><path d="M514 524h1c0 2 0 3-1 5h-1l-1-2c0-1 1-2 2-3zm5 0h0 1v2c1 1 2 2 2 3h0-2-1c-1 0-1 0-2-1 1-2 1-3 2-4z" class="F"></path><path d="M528 522l1-1 1-1 1-1v1c1 0 2 1 3 2v1c1 0 1 0 2-1v1c1 0 1 0 2-1v2h3 0c1 1 2 1 3 1l-2 2 2 1c1 0 1-1 2-1l1 1h0c0 2 0 4-1 7h0c-1 1-1 2-1 3v1l-1 2 1 1h-2-5l-4-1c-1-1-3-2-4-3v-2c-1 1-1 1 0 2h-1l-2-1-1-2-1-1v-1c1-1 1 0 1-1v-1h-2s0-1-1-2v-1-2l1-1h0 3 0 0l1-1c0-1 0-1-1-1l1-1z" class="J"></path><path d="M528 522l1-1 1-1 1-1v1c1 0 2 1 3 2v1c1 0 1 0 2-1v1c1 0 1 0 2-1v2h3 0c1 1 2 1 3 1l-2 2 2 1c1 0 1-1 2-1l1 1h0c0 2 0 4-1 7h0c-1 1-1 2-1 3v1l-1 2 1 1h-2v-1-1l-2 1v-3c0-1-1-1-1-2l1-2c0-1 1-4 1-5v-1-1c-2 0-2 0-4 1 0 0-1-2-2-2s-1 1-2 0c-1 0-1 1-2 0-1 0-3 0-5-1h0l1-1c0-1 0-1-1-1l1-1z" class="G"></path><path d="M544 528c1 0 1-1 2-1l1 1h0c0 2 0 4-1 7h0c-1 0-2-1-3-2 1-2 1-3 1-5z" class="F"></path><path d="M479 465c2 0 3-2 5-2s3 1 4 1l7 5c2 2 6 7 8 8 3 0 4 0 5 1 2 1 3 2 5 2l1 2-1 1-2 2 1 1h0l2 2 4 9 1 2c1 2 1 4 1 6v4l-1 1c-1 2-1 4-2 6 0 1-1 2-1 3l-1 1c0-2 1-4 1-5 1-1 1-2 1-3l-1-1c-1 4-2 7-4 10h-2v1h-2l-4 1c2-2 3-3 4-5h1c1-3 1-6 1-10-1-3-2-6-4-9-2-2-4-4-7-6h0c-4-3-10-4-15-5-2-1-5-3-8-5l-3-3c-1-2-2-5-4-5-1-1-1-2-1-3v-1h1l1 1 1-2c1 1 1 2 2 3v-3c1-2 4-4 6-5z" class="c"></path><path d="M503 477c3 0 4 0 5 1 2 1 3 2 5 2l1 2-1 1-2 2-8-8z" class="Y"></path><path d="M512 486h0l2 2 4 9-1 3c-1 1-1 2-1 3 0-3-2-6-3-8 0-3-1-6-1-9z" class="N"></path><path d="M518 497l1 2c1 2 1 4 1 6v4l-1 1c-1 2-1 4-2 6 0 1-1 2-1 3l-1 1c0-2 1-4 1-5 1-1 1-2 1-3l-1-1c-1 4-2 7-4 10h-2l3-4s0-1 1-1c0-2 0-3 1-4 1-2 1-6 1-9 0-1 0-2 1-3l1-3z" class="F"></path><path d="M519 499c1 2 1 4 1 6v4l-1 1-1-3v-1c0-2 1-4 1-7z" class="G"></path><path d="M484 482c5 1 12 2 17 5 2 1 4 3 6 5l3 6c-2-2-4-4-6-5-2-2-4-3-6-3v-1l-8-3h0c-1-1-1-1-2-1-2-1-3-2-4-3z" class="F"></path><path d="M498 489v1c2 0 4 1 6 3 2 1 4 3 6 5l-3-6c3 1 3 2 5 5v1c2 4 4 10 3 14-1 0-1 1-1 1-1 1-1 2-1 2-1 2-2 4-3 5l-2 1v1l-4 1c2-2 3-3 4-5h1c1-3 1-6 1-10-1-3-2-6-4-9-2-2-4-4-7-6 2 0 2 1 4 2 1 0 1-1 2-1l-7-5z" class="B"></path><path d="M506 499h0c1-1 1-1 1-2l3 3c1 2 2 9 2 11 1 3 0 5-2 7h0-1c1-3 1-6 1-10-1-3-2-6-4-9z" class="a"></path><path d="M470 472l1-2c1 1 1 2 2 3 3 4 6 8 11 9 1 1 2 2 4 3 1 0 1 0 2 1h0l8 3 7 5c-1 0-1 1-2 1-2-1-2-2-4-2h0c-4-3-10-4-15-5-2-1-5-3-8-5l-3-3c-1-2-2-5-4-5-1-1-1-2-1-3v-1h1l1 1z" class="N"></path><path d="M470 472l1-2c1 1 1 2 2 3 3 4 6 8 11 9 1 1 2 2 4 3 1 0 1 0 2 1h0c-3 0-5-1-7-2-2 0-5-1-7-2-1-2-3-5-3-7v-1l-3-2z" class="G"></path><defs><linearGradient id="Y" x1="665.963" y1="541.296" x2="636.872" y2="544.255" xlink:href="#B"><stop offset="0" stop-color="#8b898b"></stop><stop offset="1" stop-color="#a7a3a5"></stop></linearGradient></defs><path fill="url(#Y)" d="M652 498c0-5-2-11-3-16-1 0-3 0-4-1h0 1c1 0 1-1 2-1h3l8-3h2l1-1 5-2 4-2v1c-4 3-10 5-15 7l5 14c1 3 2 8 2 12 4 19 4 36 1 55-2 6-4 13-7 19-2 5-3 11-6 16l-1 1-3 3c-1 3-3 6-5 8l1-2c0-3 1-5 1-8v-4c2-3 3-7 5-10 7-21 9-44 7-66-1-7-3-13-4-20z"></path><path d="M656 480l5 14c1 3 2 8 2 12v3 1 4h0 0v-2c-1-1-1-2-1-3v-1-3l-1-1v-2-2h0l-1-3c-1-1-1-1 0-2l-1-1h-1c0-3-2-7-3-9-1-1-2-3-3-4 2-1 2-1 4-1z" class="O"></path><defs><linearGradient id="Z" x1="383.757" y1="555.673" x2="364.091" y2="585.557" xlink:href="#B"><stop offset="0" stop-color="#7f7b7d"></stop><stop offset="1" stop-color="#9d9b9d"></stop></linearGradient></defs><path fill="url(#Z)" d="M364 537c0 1 1 2 1 3s0 2 1 3c1-1 3-1 5-2h5c2 1 3 1 4 2 1 2 1 5 0 7 0 0-1 1-1 2l1 1v1c0 4 1 7 2 10v3l1 2 3 3c0 2 0 3 1 4 0 2 2 6 3 6v5 1c1 1 2 1 2 3 0 0 0 1-1 1v1c2 1 3 1 4 2 2 1 4 3 4 5 1 1 0 2 0 2-1 4-3 4-5 6-2 1-3 1-4 1v1h-1 0c-1-1-2-1-3-2l-4-4c0-1 0-2-1-3-4-3-7-12-9-17 0 0-1-3-1-4-4-13-9-29-7-43z"></path><path d="M378 572c-1 1 0 2-1 3v1h0c-1 0-2 0-3-1l-1 1c-1-1-1-2-1-3 2 0 4-1 6-1z" class="J"></path><path d="M376 554v2h0c0 1 0 1-1 1h-2v1h-4v-1-2h2 4l1-1zm-4 8c2-1 2-1 4-1h1c1 1 1 0 0 1s-3 3-5 4v-1h-2l1-1h0 1v-2z" class="K"></path><path d="M377 562v4l-1 1h2l-6 2v-1-2c2-1 4-3 5-4z" class="X"></path><path d="M385 587c1 0 2-1 4 0h1v1c-1 1-2 2-4 3l-2 1c-2 1-2 1-3 1-1-1-2-2-2-4 1 0 2-1 3-1l1 1 2-2z" class="P"></path><path d="M378 567h4 0l1 2-2 2c-1 0-2 0-3 1-2 0-4 1-6 1-1-2-1-3 0-4l6-2z" class="H"></path><path d="M373 553l6-1 1 1v1c0 4 1 7 2 10v3h0-4-2l1-1v-4c1-1 1 0 0-1h-1c-2 0-2 0-4 1l1-2v-1c-1 1-2 1-3 1v-1h2 1c0-1 1-1 2-1v-1c1 0 1 0 1-1h0v-2c-1 0-2 0-3-1z" class="O"></path><path d="M382 567c-1-1-1-1-2-1h0c-1-2-1-3-1-5 0-1-1-1-2-2l2-2c-1 0-1 0-2-1 1-1 2-1 3-2 0 4 1 7 2 10v3h0zm-11 13c3 3 4 6 5 10 1 2 2 2 3 4h0c1 1 2 2 3 2 0 1 1 2 2 2h0c-1-1-1-1-1-2h2l1-1c0-2-1-2-2-2v-1l2-1c2-1 3-2 4-3 1 1 2 1 2 3 0 0 0 1-1 1v1c0 1-1 1-2 1-2 2-4 3-5 6 0 1 1 4 2 5 2 3 4 3 7 3h1c-2 1-3 1-4 1v1h-1 0c-1-1-2-1-3-2l-4-4c0-1 0-2-1-3-4-3-7-12-9-17 0 0-1-3-1-4z" class="G"></path><path d="M390 588c1 1 2 1 2 3 0 0 0 1-1 1v1c0 1-1 1-2 1h0c1-1 1-1 1-2-1 0-2-1-4-1 2-1 3-2 4-3zm-7-19l3 3c0 2 0 3 1 4 0 2 2 6 3 6v5h-1c-2-1-3 0-4 0-2 0-3-2-5-3l1-1c-1-1-1-1-2-1v-1l1-1c1 0 1-1 1-2h1c-1 0-1-1-2-1l-3-2c1-1 0-2 1-3s2-1 3-1l2-2z" class="F"></path><path d="M380 576l1 1h4v1h-2v1h2c0 1 0 1-1 1-2 1-2 1-3 3h0c-1-1-1-1-2-1v-1l1-1c1 0 1-1 1-2h1c-1 0-1-1-2-1v-1z" class="X"></path><path d="M378 572c1-1 2-1 3-1h2c0 2 0 2-1 3v1c-1 0-1 1-2 1v1l-3-2c1-1 0-2 1-3z" class="O"></path><path d="M371 541h5c2 1 3 1 4 2 1 2 1 5 0 7 0 0-1 1-1 2l-6 1h0c-2-1-4-1-5-2-2-2-2-6-2-8 1-1 3-1 5-2zm20 52c2 1 3 1 4 2 2 1 4 3 4 5 1 1 0 2 0 2-1 4-3 4-5 6h-1c-3 0-5 0-7-3-1-1-2-4-2-5 1-3 3-4 5-6 1 0 2 0 2-1z" class="H"></path><path d="M395 595c2 1 4 3 4 5 1 1 0 2 0 2 0-3-2-1-4-3l1-1c-1-2-1-2-1-3z" class="C"></path><path d="M403 660h4c3 2 5 3 7 4l4 4c3 1 7 5 9 7 1 2 4 4 5 6 5 6 9 13 13 19 3 4 4 8 7 12 1 2 2 5 3 8 2 5 4 11 7 16v2h-1l1 2v1c-1 0-1 0-2-1s-1-2-3-3c-1-5-2-9-4-13-2-3-3-6-4-8-1-1-1-2-2-3v-3l-1-1-1-1-2 2c-1-1-1-1-1-3h-1c-1-2-4-4-6-5h0c-2 0-3 0-4-1h0c1-1 1-1 2-1h1v-1h-1-4-3-1c-1 1-2 1-3 1h-1l-1-1c1-1 1-3 1-5 1-1 1 0 2-1 1 0 0 0 1-1l-4-2-3 4c-1 5-3 12-6 15-2 7-5 14-5 21l-1 1h-1c0 1-1 1-1 2h0c-1-1-1-1-2-1l2-8 6-32 1-8c0-3 0-5 1-7 0-2 0-5 1-6v-2h-1a30.44 30.44 0 0 0-8-8v-1z" class="J"></path><path d="M420 675l12 15h0c-2-1-6-5-6-7l-2-1-4-1v-1c-1-1-1 0-2-1l2-4z" class="H"></path><path d="M447 710h0c1 0 1 0 1 1l1 3c0 1 1 1 1 2 4 7 7 15 11 22l1 2v1c-1 0-1 0-2-1s-1-2-3-3c-1-5-2-9-4-13-2-3-3-6-4-8-1-1-1-2-2-3v-3z" class="L"></path><path d="M417 694c0-2 1-3 2-5v-1c1-2 2-3 4-3l1-1c1 1 1 1 1 2 1 2 5 5 6 6 1 0 1 1 2 1 1 2 1 2 3 3v1h-1-1 0-4c-1 1-1 1-1 2h-3-1c-1 1-2 1-3 1h-1l-1-1c1-1 1-3 1-5 1-1 1 0 2-1 1 0 0 0 1-1l-4-2-3 4z" class="a"></path><path d="M420 690c1 0 2-1 3 0 3 0 6 3 9 5h-9l-2-1c1-1 1 0 2-1 1 0 0 0 1-1l-4-2z" class="N"></path><defs><linearGradient id="a" x1="402.893" y1="698.053" x2="414.351" y2="702.868" xlink:href="#B"><stop offset="0" stop-color="#bcbabb"></stop><stop offset="1" stop-color="#dfdcdd"></stop></linearGradient></defs><path fill="url(#a)" d="M403 724l1-1 6-32 3-18v-2h0c2 0 6 3 7 4l-2 4c1 1 1 0 2 1v1l4 1 2 1c-2 0-4-1-5 0-1 0-2 1-2 2l-3 4c-3 4-3 12-5 17v3c-2 7-5 14-5 21l-1 1h-1c0 1-1 1-1 2h0c-1-1-1-1-2-1l2-8z"></path><path d="M418 679c1 1 1 0 2 1v1l4 1 2 1c-2 0-4-1-5 0-1 0-2 1-2 2l-3 4c-3 4-3 12-5 17v3c-2 7-5 14-5 21l-1 1 7-31c1-7 3-15 6-21z" class="L"></path><path d="M581 345v-2-1l1 1c0-2 0-2-1-3l-1-2 1-1 1 1h0c4 5 6 11 10 17 2 4 6 9 9 14 1 2 1 4 1 7l-4 14c-2 6-5 13-6 20-1-1-2-2-3-4-1-1-1-1-1-2-3-4-6-8-8-13v-1c-1-2-1-4-1-7-1 1-1 1-1 2v1l-1-1c-3-5-4-11-7-17l-2-8c0-2-1-3 0-4h3 0l-4-1-1-3h2c6 0 12 0 17-1 0-1-1-1-1-2l-3-4z" class="T"></path><path d="M583 360v-1h1 2v-1h-1v-1c2 0 2 0 3 1 1 2 3 4 4 7l-1 1c-3-1-3-4-5-5l-1 1c-1 1-2 1-4 1v-1c1 0 1 0 2-2z" class="V"></path><path d="M568 360c0-2-1-3 0-4h3 2 1 1v1 1c1 0 6 0 7 1l1 1c-1 2-1 2-2 2h-6c-1-1-2-3-3-4-2 0-2 0-3 1l-1 1z" class="U"></path><path d="M585 351c2 0 3 0 4 2h0l1 2v1l-2-2h0l-1 1h-1-2c-2 0-5 1-7 1-1-1-2-1-3 0h-1-2 0l-4-1-1-3h2c6 0 12 0 17-1z" class="C"></path><defs><linearGradient id="b" x1="579.595" y1="362.778" x2="570.269" y2="379.816" xlink:href="#B"><stop offset="0" stop-color="#a19d9f"></stop><stop offset="1" stop-color="#c0bdbe"></stop></linearGradient></defs><path fill="url(#b)" d="M568 360l1-1c1-1 1-1 3-1 1 1 2 3 3 4h6v1c2 0 3 0 4-1l1-1c2 1 2 4 5 5h-2l1 1h-1v-1h-3c-1 0-1 0-2 1-1 0-1 0-2 1h0v1c-1 2-2 3-1 5h0c0 1-2 9-2 9-1 1-1 1-1 2v1l-1-1c-3-5-4-11-7-17l-2-8z"></path><path d="M581 363c2 0 3 0 4-1l1-1c2 1 2 4 5 5h-2l1 1h-1v-1h-3 1v-1h-2c-1 1-2 1-3 2h-1v-4z" class="L"></path><path d="M591 366l1-1c2 2 4 4 5 7s1 7 2 10l-3 2c-1 0-2 1-3 2v1l1 2 4 1c-2 6-5 13-6 20-1-1-2-2-3-4-1-1-1-1-1-2-3-4-6-8-8-13v-1c-1-2-1-4-1-7 0 0 2-8 2-9h0c-1-2 0-3 1-5v-1h0c1-1 1-1 2-1 1-1 1-1 2-1h3v1h1l-1-1h2z" class="G"></path><path d="M594 375c0 1 1 3 0 4l-1 7v1c-1 0-1-1-1-1h-1c1-2 1-4 0-6 0-1 0 0 1-1h1 1v-4z" class="a"></path><path d="M586 386h2l1 3c1 1 1 3 2 4 1 4 3 7 2 11h-2c0-1-1-2-2-3 0-1 0-2-1-3s0-3 0-4l-1-3c-1-2-1-3-1-5z" class="B"></path><path d="M581 374h1 0c0 1 1 2 1 2v3c-1 2-1 7 0 9 0 1 0 3 1 4v1c1 4 4 10 7 14h0l-2-1c-1-1-1-1-1-2-3-4-6-8-8-13v-1c-1-2-1-4-1-7 0 0 2-8 2-9z" class="I"></path><path d="M591 366l1-1c2 2 4 4 5 7s1 7 2 10l-3 2c-1 0-2 1-3 2l1-7c1-1 0-3 0-4 0-2-1-4-3-5h-3l-1 1c-1 0-2 1-2 3l-2 2s-1-1-1-2h0-1 0c-1-2 0-3 1-5v-1h0c1-1 1-1 2-1 1-1 1-1 2-1h3v1h1l-1-1h2z" class="K"></path><path d="M581 374c1-3 3-6 5-6 2-1 4 0 5 2h-3l-1 1c-1 0-2 1-2 3l-2 2s-1-1-1-2h0-1 0z" class="B"></path><path d="M445 332c0-1 2-2 2-3 2-3 5-8 5-10 0-4-1-9-2-13l4 11c4 9 6 20 7 30l1 5v3 6l-1-1h-1v-3l-1 3v2l-2-1c-2 1-3 2-5 3-1 2-3 2-4 5h-1v1c-1-1-2-2-2-3l-1 1c0 1 1 1 2 2 1 2 1 5 1 7 0 1 0 2-1 2-1 3 0 6-1 8l-1 1h-1c-1 3-1 6-2 9v1l-5-12c-2-4-4-11-5-15-1-2-2-5-2-8-1-1-1-2 0-3 1-5 5-9 7-12l9-16z" class="R"></path><path d="M431 360v3h1v-2c1-2 4-7 7-7 0 0 1 1 2 1 0 0 1 0 1-1 2 0 5 1 6 1h-3v2h-1v-1c-1 1-1 2-1 3h-1v-2c-1 0-1 1-1 2h-3l-2 2c-2 1-3 3-4 5h-1c-1-2-1-3-1-5l1-1z" class="P"></path><path d="M431 371c3-2 4-4 6-7 2-1 2-2 4-2v-1h2v1c1-1 1-1 2-1h1v-1h-2v-1h6c2-2 3-2 5-2 1 0 2-1 3-1h1l1 1-1 3v2l-2-1c-2 1-3 2-5 3-1 2-3 2-4 5h-1v1c-1-1-2-2-2-3l-1 1c0 1 1 1 2 2 1 2 1 5 1 7 0 1 0 2-1 2 0-2 0-5-1-8-1-1-2-3-4-4h-4c-1 2-3 4-4 7l1 1 3 10-1 1c-2-4-4-11-5-15z" class="L"></path><path d="M444 368c-1-1-3-2-3-4 2 0 2 1 3 2h1l-2-3h1l1 1c1-1 2-1 2-1 3-1 5-3 7-4h1c2 0 3 0 4 1v2l-2-1c-2 1-3 2-5 3-1 2-3 2-4 5h-1v1c-1-1-2-2-2-3l-1 1z" class="X"></path><path d="M434 375l-1-1c1-3 3-5 4-7h4c2 1 3 3 4 4 1 3 1 6 1 8-1 3 0 6-1 8l-1 1h-1c-1 3-1 6-2 9v1l-5-12 1-1-3-10z" class="O"></path><path d="M434 375l-1-1c1-3 3-5 4-7h4c2 1 3 3 4 4 1 3 1 6 1 8-1 3 0 6-1 8l-1 1h-1 0c1-4 2-11 0-15-2 1-4 1-6 1 0 2 1 4 0 5-1-1-1-2-2-4h-1 0z" class="K"></path><path d="M443 373h-1c0-2-1-2-3-3l-2 2-1-1c1 0 1-1 1-1 3-2 4 0 6 1h2c1 3 1 6 1 8-1 3 0 6-1 8l-1 1h-1 0c1-4 2-11 0-15z" class="Z"></path><path d="M445 332c0-1 2-2 2-3 2-3 5-8 5-10 0-4-1-9-2-13l4 11v6 1l-1 1v1l-13 24c1 1 2 1 3 1h0l2-1c3-2 6-1 10-2h0 6v-1l1 5v3c-5-1-9 0-14 0-1 0-4-1-6-1 0 1-1 1-1 1-1 0-2-1-2-1-3 0-6 5-7 7v2h-1v-3c3-6 7-12 10-17 2-4 4-6 4-11z" class="H"></path><path d="M461 347l1 5c-6 1-13 0-19-1l2-1c3-2 6-1 10-2h0 6v-1z" class="J"></path><path d="M454 317c4 9 6 20 7 30v1h-6 0c-4 1-7 0-10 2l-2 1h0c-1 0-2 0-3-1l13-24v-1l1-1v-1-6z" class="D"></path><path d="M454 323c1 2 1 4 2 6h0c0 2 0 3 1 4 1 2 1 4 1 5s-1 2-1 2c-1 1-1 2-1 3h-1l-1-1c0-1 1-2 2-3 0-2-1-2 0-3v-1l-1-1c0-2-1-3-2-5 1-1 0-2 0-3v-1l1-1v-1z" class="T"></path><path d="M453 326c0 1 1 2 0 3v1l-3 8 3-5 1-1c0 4-2 6-3 9s-4 5-6 7l2-5c1-1 1-3 2-4-2 0-5 8-6 9h12 0c-4 1-7 0-10 2l-2 1h0c-1 0-2 0-3-1l13-24z" class="U"></path><path d="M645 650c6 1 10 2 16 4 3 1 7 1 11 2h1c1 0 4-2 5-3 2 3 4 5 6 8v1c3 4 4 10 2 15v1c-1 3-2 6-4 8l-1 1c-3 4-8 7-13 8h-1c-3 1-7 1-10 0l-1-1c-4-1-7-4-10-8 0-1-1-2-1-3v-2h2c1-1 1-2 3-2 0 0 1 0 1-1l-1-1h0v-3-1c1-1 0-6 0-8l1-1-1-3v-2c-1-1-1-2-1-3 1-1 2-1 3-1l-1-1h-1c-1-1-2-1-3-1h-1c-1-1-1-1-1-3z" class="b"></path><path d="M650 674l4 2 3 6c-1 0-6 1-7 1 0 1-1 2-1 2l-1 1h0v-1c1-2 1-4 2-6 0 0 1 0 1-1l-1-1h0v-3z" class="Y"></path><path d="M645 650c6 1 10 2 16 4 3 1 7 1 11 2h-1l-3 1h0c2 3 6 4 9 6-1 0-1 1-1 0-2-1-3-2-5-2 1 2 2 2 2 4-2 0-5 1-7 0l-1-1c1 2 2 3 3 6h0l-1 1-3-6c-1 0-1-1-2-2v2c-1 2-2 5-2 8h-1 0l-1-2h-1l-1 3c-1 0-1-1-2-1h-1v1h0c1 0 1 0 1 1v1h0l-4-2v-1c1-1 0-6 0-8l1-1-1-3v-2c-1-1-1-2-1-3 1-1 2-1 3-1l-1-1h-1c-1-1-2-1-3-1h-1c-1-1-1-1-1-3z" class="N"></path><path d="M645 650c6 1 10 2 16 4 3 1 7 1 11 2h-1l-10-1-1 1c-3 2-7 2-10 5v-2c-1-1-1-2-1-3 1-1 2-1 3-1l-1-1h-1c-1-1-2-1-3-1h-1c-1-1-1-1-1-3z" class="E"></path><defs><linearGradient id="c" x1="394.852" y1="723.233" x2="414.208" y2="647.354" xlink:href="#B"><stop offset="0" stop-color="#c0bdbf"></stop><stop offset="1" stop-color="#faf9f9"></stop></linearGradient></defs><path fill="url(#c)" d="M363 636c4 1 9 1 13 1 4 1 7 2 10 3 4 1 8 3 12 4 10 6 19 12 27 20l10 10c7 8 12 18 17 27l5 10 4 9 2 6 3 10h0v3c1 1 1 1 1 2h-2c-1 0 0 0-1-1l-2-4c-3-5-5-11-7-16-1-3-2-6-3-8-3-4-4-8-7-12-4-6-8-13-13-19-1-2-4-4-5-6-2-2-6-6-9-7l-4-4c-2-1-4-2-7-4h-4c-2-1-2-2-4-2l-1-1c-1 0-2-1-3-1-5-3-12-4-18-5l-2-1-3-1c-3 0-10 1-13-1-5 0-10 2-15 2-2 0-5-1-8-1h-1 0c1-1 1-2 1-2v-1-1h-1v-1h2l1 1 2-2h0c1-1 2-1 3-1 6-4 13-5 20-6z"></path><path d="M457 711l4 9 2 6 3 10h0c-1-1-1-2-2-4-1-1-2-3-2-5v-1h-1c-2-3-3-7-4-11-1-1-1-3 0-4z" class="B"></path><path d="M359 648c11-1 21 0 32 3 2 0 6 1 8 3 1 0 1 1 2 2 2 1 5 2 6 4h-4c-2-1-2-2-4-2l-1-1c-1 0-2-1-3-1-5-3-12-4-18-5l-2-1-3-1c-3 0-10 1-13-1z" class="a"></path><path d="M372 649c5 0 10 1 14 2 2 1 3 1 5 1l2 1 4 1 1 1c1 0 2 1 3 1 2 1 5 2 6 4h-4c-2-1-2-2-4-2l-1-1c-1 0-2-1-3-1-5-3-12-4-18-5l-2-1-3-1z" class="X"></path><path d="M517 479l2 1v3h3c1 0 2 0 3-2l1-1h3 1c2 1 4 3 5 5 3 0 5 1 8 2v1c4 1 7 4 11 5l8 9c1 2 2 3 3 6l-2 3c0 2-1 5-2 7l-1-3h-1v1c-1 1-1 3-2 4-1 2-1 3-1 5v1h-2v1h-1c-2 0-3 1-6 1h0l-1-1c-1 0-1 1-2 1l-2-1 2-2c-1 0-2 0-3-1h0-3v-2c-1 1-1 1-2 1v-1c-1 1-1 1-2 1v-1c-1-1-2-2-3-2v-1l-1 1-1 1-1 1c0-1-1-2-1-3v-1l-3 3h0c-2 0-2 0-4-1v-1l-2 1v-1h-2c0-1 1-2 1-3 1-2 1-4 2-6l1-1v-4c0-2 0-4-1-6l-1-2-4-9-2-2h0l-1-1 2-2 1-1-1-2h0c2 0 3-1 4-1z" class="L"></path><path d="M520 494c2 0 5-1 7-1 1 0 1 1 2 1l-2 2c1 1 2 1 3 1 2 1 4 2 5 4-3 0-3 0-5-2h-2 0c-1 0-2-1-3-1l1-1v-1l-5-1-1-1z" class="K"></path><path d="M534 491l1 1 1-1 1 2h0c-1 1-2 1-3 1 0 1 1 1 1 2h-1v1h1c1 0 2 1 2 2v1l-1 1 1 1v1l-1-1-1-1c-1-2-3-3-5-4-1 0-2 0-3-1l2-2c-1 0-1-1-2-1h5 0 2v-2z" class="J"></path><path d="M537 500v-1c0-1-1-2-2-2h-1v-1h1c0-1-1-1-1-2 1 0 2 0 3-1h0l2 2h1c1 1 1 2 2 3v1l1 3-3 3-3-5z" class="Z"></path><path d="M519 510l1-1v-4c2 2 4 4 6 5s3 4 5 5v1l2 3c0 1 0 2 1 3-1-1-2-2-3-2v-1l-1 1-1 1-1 1c0-1-1-2-1-3v-1l-3 3h0c-2 0-2 0-4-1v-1l-2 1v-1h-2c0-1 1-2 1-3 1-2 1-4 2-6z" class="X"></path><path d="M517 479l2 1v3h3c1 0 2 0 3-2l1-1h3 1c2 1 4 3 5 5l3 3c1 0 1 0 1 1h0-2l1 2c-1 0 0 0-1-1l-1 1-1 1-1-1v2h-2 0-5c-2 0-5 1-7 1-1 0-1-1-2-2l2-2c-1 0-2 0-3-1-1 0-2-1-3-1l-2-2h0l-1-1 2-2 1-1-1-2h0c2 0 3-1 4-1z" class="L"></path><path d="M532 492l-1-1-1-1 3-1 1 2v2h-2v-1z" class="R"></path><path d="M517 479l2 1v3h-6l1-1-1-2h0c2 0 3-1 4-1z" class="Q"></path><path d="M512 486c2-1 3-1 5-1 1 0 2 0 3 1v1c-1 0-1 0-1 1l1 1 3-3h0l1 2-4 2h0c-1 0-2 0-3-1-1 0-2-1-3-1l-2-2z" class="C"></path><path d="M524 488h0c-1 1-2 2-2 3 1 0 2 0 3-1s4-1 5 0h0c-1 1-2 1-2 2h0 4v1h0-5c-2 0-5 1-7 1-1 0-1-1-2-2l2-2h0l4-2z" class="Q"></path><path d="M530 480c2 1 4 3 5 5l3 3c1 0 1 0 1 1h0-2l1 2c-1 0 0 0-1-1l-1 1-1 1-1-1-1-2c-1-3-2-5-4-8v-1h1z" class="F"></path><path d="M535 485c3 0 5 1 8 2v1c4 1 7 4 11 5l8 9c1 2 2 3 3 6l-2 3c0 2-1 5-2 7l-1-3h-1v1c-1 1-1 3-2 4-1 2-1 3-1 5v1h-2v1h-1c-2 0-3 1-6 1h0l-1-1c-1 0-1 1-2 1l-2-1 2-2c0-6-2-13-4-18v-2l3-3-1-3v-1c-1-1-1-2-2-3h-1l-2-2-1-2 1-1c1 1 0 1 1 1l-1-2h2 0c0-1 0-1-1-1l-3-3z" class="Q"></path><path d="M540 495l1-1 2 1v1h-2l3 3c1 1 1 0 2 1s2 1 4 2c0 1 1 1 1 2 1 0 1 1 2 1h1 1 1v-1c2 1 3 3 5 4 1 1 2 2 2 3 0 2-1 5-2 7l-1-3h-1v1c-1 1-1 3-2 4v-4c0-2 0-6-2-7v-1l-2-1-2-2h-2c0-2-2-2-3-3l-1 1-1-1c0-1-1-2-1-3v-1l-1 1v-1c-1-1-1-2-2-3z" class="U"></path><path d="M556 505v-1c2 1 3 3 5 4 1 1 2 2 2 3 0 2-1 5-2 7l-1-3c0-2 0-2-1-3v1c-1-1 0-1-1-2-1-2-2-2-3-4l1-1c1 1 1 1 2 1 0-1-1-2-2-2z" class="D"></path><path d="M535 485c3 0 5 1 8 2v1c4 1 7 4 11 5l8 9c1 2 2 3 3 6l-2 3c0-1-1-2-2-3l1-1c-2-4-6-7-9-10-2-1-3-3-4-3-2-1-3-1-4-2s-4-2-6-3h0 0c0-1 0-1-1-1l-3-3z" class="O"></path><path d="M542 499l1-1v1c0 1 1 2 1 3l1 1 1-1c1 1 3 1 3 3h2l2 2 2 1v1c2 1 2 5 2 7v4c-1 2-1 3-1 5v1h-2v1h-1c-2 0-3 1-6 1h0l-1-1c-1 0-1 1-2 1l-2-1 2-2c0-6-2-13-4-18v-2l3-3-1-3z" class="S"></path><path d="M540 505l3-3 1 5h-4v-2z" class="M"></path><path d="M547 513c1 0 2 1 3 1h1c0 1 0 1-1 2 1 1 0 2 1 4l-1 1v-1c-1-1-2-2-3-2l-1-1 1-3v-1z" class="F"></path><path d="M540 507h4c1 2 1 4 3 6v1l-1 3 1 1v5 1c1 1 1 1 2 1l1 1h2c-1-2-1-2-1-3 1 0 2 2 3 3v1h-1c-2 0-3 1-6 1h0l-1-1c-1 0-1 1-2 1l-2-1 2-2c0-6-2-13-4-18z" class="N"></path><defs><linearGradient id="d" x1="354.093" y1="662.39" x2="328.057" y2="686.437" xlink:href="#B"><stop offset="0" stop-color="#d3d0d1"></stop><stop offset="1" stop-color="#fdfcfc"></stop></linearGradient></defs><path fill="url(#d)" d="M332 650l4-4v1s0 1-1 2h0 1c3 0 6 1 8 1 5 0 10-2 15-2 3 2 10 1 13 1l3 1 2 1c-4 0-8-1-13 0h6c-1 1-1 1-3 1h0-2c-2 1-5 2-6 4l-1 3h-3 0-1c0 1 0 1-1 2 3 2 4-2 4 3 1 2 2 2 2 4l1 1-1 3c-1 0-2 1-3 1l-2 2v2l2 1s1-1 2-1h1c0 2-1 2-3 4h1l1-1 2 1-1 1h1 0c2 1 2 3 3 5-2 2-3 4-6 5l-1 1c-1 1-1 1-2 1-2 1-3 2-5 2h0c-2 1-6 1-8 0h-1c-2 0-4-1-5-2-2 0-2-1-3-1-3-2-5-4-6-6-3-4-5-8-5-13-2-9 3-15 8-21l3-3z"></path><path d="M359 668c0 1-1 1-2 2h-2c-2 0-3-2-4-3v-1h1 1c0-2 0-2 2-3h0v1 1c-1 1-1 1 0 2 1 0 1-2 2-3 1 2 2 2 2 4z" class="E"></path><path d="M332 650l4-4v1s0 1-1 2h0 1c3 0 6 1 8 1 5 0 10-2 15-2 3 2 10 1 13 1l3 1 2 1c-4 0-8-1-13 0h6c-1 1-1 1-3 1h0-2c-2 1-5 2-6 4l-1 3h-3 0l-2-1c-2-1-2-1-3-3h-1c-4 1-7 1-11 1h0c-3 0-5-2-7-3-6 6-10 12-9 20v2c1 4 2 6 4 10a30.44 30.44 0 0 0 8 8h1c1 1 3 1 5 2h0c5 1 9-1 14-1-2 1-3 2-5 2h0c-2 1-6 1-8 0h-1c-2 0-4-1-5-2-2 0-2-1-3-1-3-2-5-4-6-6-3-4-5-8-5-13-2-9 3-15 8-21l3-3z" class="J"></path><path d="M338 656c3-1 6-1 8-1 7-2 12-4 18-4h6c-1 1-1 1-3 1h0-2c-2 1-5 2-6 4l-1 3h-3 0l-2-1c-2-1-2-1-3-3h-1c-4 1-7 1-11 1z" class="H"></path><path d="M460 307c1 2 3 4 4 6 1 0 2 0 3 1 0 1 1 1 2 2-1 1-1 1 0 3v1l1 3c1 1 1 2 1 4l1 1v1c1 1 1 1 1 2v1l1 1v3c0 1 0 2 1 2h1c2 1 2 1 3 3l2 10 1 4v6 8 4c0 6-1 12-2 19-1 1-1 2-1 4l1 1-4 17c-2 10-4 19-8 27-1 0-1 0-2-1-2 0-3-2-5-3-1 2-2 4-2 5l-1 3c-3-5-5-11-6-16 1 0 1 1 2 1v1c15-31 18-66 10-99l-1-3c-2-6-3-14-6-19v-1c1 0 1-1 2-1l1-1z" class="B"></path><path d="M461 437c12-28 19-61 13-92-1-6-4-12-5-18l5 9c0 1 0 2 1 2-1 2 0 3 0 6l2 10c1 3 0 7 1 10 1 4 0 10 0 13-1 5-1 9-2 14s-1 11-3 16c-2 8-6 16-7 25l-1 1 1 1-1 1-1 1c1 1 1 2 3 3l-1 1c-2 0-3-2-5-3z" class="W"></path><path d="M475 338h1c2 1 2 1 3 3l2 10 1 4v6 8 4c0 6-1 12-2 19-1 1-1 2-1 4l1 1-4 17c-2 10-4 19-8 27-1 0-1 0-2-1l1-1c-2-1-2-2-3-3l1-1 1-1-1-1 1-1c1-9 5-17 7-25 2-5 2-11 3-16s1-9 2-14c0-3 1-9 0-13-1-3 0-7-1-10l-2-10c0-3-1-4 0-6z" class="E"></path><defs><linearGradient id="e" x1="466.823" y1="428.463" x2="472.677" y2="434.037" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#3d3b3c"></stop></linearGradient></defs><path fill="url(#e)" d="M466 432h1l3-3c1-3 2-6 2-8l1-3c1 0 0 1 0 2h1v-1-1l2-4c-2 10-4 19-8 27-1 0-1 0-2-1l1-1c-2-1-2-2-3-3l1-1 1-1-1-1 1-1z"></path><path d="M514 444c0-1 1-2 2-3 0 1 0 1 1 2h1c2 1 4 2 5 3h1c9 6 17 14 25 22 2 3 5 5 8 7 1 1 5 3 5 5l6 6v3 2l-1 2c0 2 0 3-1 5h-1l-2 1-1 3-8-9c-4-1-7-4-11-5v-1c-3-1-5-2-8-2-1-2-3-4-5-5h-1-3l-1 1c-1 2-2 2-3 2h-3v-3l-2-1c-1 0-2 1-4 1h0c-2 0-3-1-5-2-1-1-2-1-5-1-2-1-6-6-8-8l-7-5c5-1 10-2 15-4l4-1c2-1 4-2 7-2 0-2 0-2 1-4s0-2-1-4c0-1 0-2 1-3-1-1-1-1-1-2z" class="b"></path><path d="M514 444c0-1 1-2 2-3 0 1 0 1 1 2h1c2 1 4 2 5 3h-2v1c1 2 3 2 4 4h0c1 0 3 1 4 2v2l-14-9c-1-1-1-1-1-2z" class="D"></path><defs><linearGradient id="f" x1="535.622" y1="449.612" x2="535.392" y2="466.354" xlink:href="#B"><stop offset="0" stop-color="#211e1f"></stop><stop offset="1" stop-color="#3c3c3b"></stop></linearGradient></defs><path fill="url(#f)" d="M523 446h1c9 6 17 14 25 22 2 3 5 5 8 7 1 1 5 3 5 5l-33-25v-2c-1-1-3-2-4-2h0c-1-2-3-2-4-4v-1h2z"></path><path d="M530 466c10 6 20 14 27 22l9 10h-1l-2 1-1 3-8-9c-4-1-7-4-11-5v-1c-3-1-5-2-8-2-1-2-3-4-5-5h-1-3-1c0-1 1-2 2-2-1-2-1-2-2-3l1-1c1-1 2-2 3-4h-1l1-2 1-2z" class="Q"></path><path d="M532 478h-3v-3-1c1 0 2-1 3-2 2 1 5 2 6 4-1 0-1 1-2 1h-3l-1 1z" class="Y"></path><path d="M526 480h-1c0-1 1-2 2-2-1-2-1-2-2-3l1-1c1-1 2-2 3-4h-1l1-2 1 2h3 1l-1 1s-1 0-1 1h0c-1 1-2 2-3 2v1 3h3v1l-1-1-1 2h-1-3z" class="R"></path><path d="M554 493l-1-1c-2-1-2-4-4-5l-3-2-1 1v-1c0-1 0-1 1-2 2 1 5 3 8 5 1 1 2 0 3 0h0l9 10h-1l-2 1-1 3-8-9z" class="S"></path><path d="M507 459c2-1 4-2 7-2 2 1 5 2 7 3l9 6-1 2-1 2h1c-1 2-2 3-3 4l-1 1c1 1 1 1 2 3-1 0-2 1-2 2h1l-1 1c-1 2-2 2-3 2h-3v-3l-2-1c-1 0-2 1-4 1h0c-2 0-3-1-5-2-1-1-2-1-5-1-2-1-6-6-8-8l-7-5c5-1 10-2 15-4l4-1z" class="D"></path><path d="M509 467c-1 1-1 1-3 1-2 1-3 1-4 1l-1-1c1-1 3-2 5-2h2c1 0 0-1 2-2h0v2h-1v1z" class="S"></path><path d="M510 467c3 0 5 4 7 6 0 1-1 1-1 2s0 2 1 3v1c-1 0-2 1-4 1l1-1c-2-1-3-2-4-3l-1-1v-2l2-1h-1c1-2 1-2 1-4l-1-1z" class="W"></path><path d="M510 476h2v-1l-1-1 1-1v1h3v4c-1 0-1 1-1 1-2-1-3-2-4-3z" class="Y"></path><path d="M507 459h4c-1 1-2 1-2 3h-1l-4 3c-1 0-2 1-3 1l-6 3-7-5c5-1 10-2 15-4l4-1z" class="E"></path><path d="M514 457c2 1 5 2 7 3l9 6-1 2-1 2h1c-1 2-2 3-3 4l-1 1c1 1 1 1 2 3-1 0-2 1-2 2h1l-1 1c-1 2-2 2-3 2h-3v-3l-2-1v-1c-1-1-1-2-1-3s1-1 1-2c-2-2-4-6-7-6h-1v-1h1v-2h0c1-1 2-1 3-1l1-1h1c0-2 0-3-1-5z" class="Y"></path><path d="M517 478l2-1c1 0 1 1 2 1l-1 1-1-1-1 1c1 0 1 1 1 1 2 1 4 1 6 1-1 2-2 2-3 2h-3v-3l-2-1v-1z" class="U"></path><path d="M514 457c2 1 5 2 7 3l-1 1s0 1 1 2h-3l-2-1h-1c0-2 0-3-1-5z" class="C"></path><path d="M621 457l7 1c2 1 5 1 6 2 0 1 1 2 1 3l4 7c1-2-3-8-4-10 1 0 3 1 5 1h0l2 1h0c2 2 6-1 8 1 1 0 1 1 2 1l3 1h1v1c2 1 3 1 4 1h1c1 0 2 0 4 1 2 0 6 0 9-1h3l-3 3c-1 0-2 1-3 2l-4 2-5 2-1 1h-2l-8 3h-3c-1 0-1 1-2 1h-1 0c1 1 3 1 4 1 1 5 3 11 3 16-3 1-7 3-10 2 0 0-1-1-2-1l-1 1v3l2 13 1 3c-1 3-1 5-3 6-2 2-4 3-7 3l-3-1-2-2c0-2-1-5-2-7v-2l-1-1c-1 0 0 0-1-1l-1-2-1 1c0 1 0 2 1 3h0v1c-2-1-2-3-2-4l-1 1-6-13c-1-2-5-8-4-10h3l-2-3-2-2 1-1h1c1-4 2-8 4-13s4-10 7-15z" class="G"></path><path d="M632 492c2-1 4 0 5 0v1c-1 1-3 1-4 1-1-1-1-1-1-2z" class="B"></path><path d="M637 515c0-1-1-1-1-2s2-4 2-6v-5c1-1 1-3 1-5 0-1 0-1 1-2h1l1 1v-1l-2-2h0l2-1c1 1 1 0 1 1h1c0 2-1 3-2 4h-2c0 1-1 2-1 3v3l2 13h-3-1c1-1 2-2 2-3h0c-1 0-2 1-2 2z" class="P"></path><path d="M641 482h4c0 1 0 2-1 3v1l1 1c1 1 1 2 1 3h-1s-1 0-2-1h-2c-1 0-1 1-3 0-1 1-2 0-4-1-1 0-1 0-2-1h2c0-1 1-1 1-2h0l1-1c1 0 2-1 3-1s1 0 2-1z" class="B"></path><path d="M638 489l-1-1c1-1 2-2 4-2l1 1-1 2c-1 0-1 1-3 0z" class="N"></path><path d="M637 515c0-1 1-2 2-2h0c0 1-1 2-2 3h1 3l1 3c-1 3-1 5-3 6-2 2-4 3-7 3l-3-1-2-2c0-2-1-5-2-7 2 0 4-1 5 0v2h2c2-1 3-3 5-5z" class="H"></path><defs><linearGradient id="g" x1="648.841" y1="477.302" x2="658.425" y2="461.278" xlink:href="#B"><stop offset="0" stop-color="#d8d6d7"></stop><stop offset="1" stop-color="#fdfcfb"></stop></linearGradient></defs><path fill="url(#g)" d="M674 470c-3-1-4 1-7 2-6 2-13 5-20 6-3-5-7-10-8-15l1-1h2c2 2 6-1 8 1 1 0 1 1 2 1l3 1h1v1c2 1 3 1 4 1h1c1 0 2 0 4 1 2 0 6 0 9-1h3l-3 3z"></path><path d="M612 491l-2-3-2-2 1-1h1c2 2 5 3 8 3h0c3 1 8 3 10 6h-1v2 1h1c0-1 0 0 1-1 0 2-2 3-2 4v4c1-1 1-1 2-1l2-1c1 1 1 1 1 3l2-3c1 1 1 2 1 3-1 1-2 1-3 2h2c0 2-2 3-1 4l1 1c0 1-1 1-2 3h1v2c-1 1-1 1-3 1-1-1-3 0-5 0v-2l-1-1c-1 0 0 0-1-1l-1-2-1 1c0 1 0 2 1 3h0v1c-2-1-2-3-2-4l-1 1-6-13c-1-2-5-8-4-10h3z" class="B"></path><path d="M627 504c1-1 1-1 2-1l2-1c1 1 1 1 1 3h0l-1-1c-1 0-1 1-2 0h0l-2 2v1l-1-1 1-2z" class="F"></path><path d="M620 500c1 0 2 1 3 1l2-1h0v4l-2-1h0c0 2 1 3 1 5 1 1 1 1 0 2v1h0c-2-1-3-2-3-4l-1-2h0c0-1 0 0-1-1 0-1 0-2-1-3l2-1z" class="N"></path><path d="M612 491h0c1 1 1 2 2 3 2 2 3 5 6 6h0l-2 1c1 1 1 2 1 3 1 1 1 0 1 1-3-1-3-5-5-7-1 1-1 2-2 3-1-2-5-8-4-10h3z" class="G"></path><path d="M612 491l-2-3-2-2 1-1h1c2 2 5 3 8 3h0c3 1 8 3 10 6h-1v2 1c-2 0-3 1-4 2h-3v1c-3-1-4-4-6-6-1-1-1-2-2-3h0z" class="D"></path><path d="M623 499v-4-1l4 2v1c-2 0-3 1-4 2z" class="R"></path><path d="M612 491h2l1 1h1l1-1c2 1 3 2 4 4-1 1-1 1-2 1v2l1 1v1c-3-1-4-4-6-6-1-1-1-2-2-3z" class="C"></path><defs><linearGradient id="h" x1="620.93" y1="471.867" x2="641.778" y2="475.326" xlink:href="#B"><stop offset="0" stop-color="#b9b6b7"></stop><stop offset="1" stop-color="#e3e1e1"></stop></linearGradient></defs><path fill="url(#h)" d="M621 457l7 1c2 1 5 1 6 2 0 1 1 2 1 3l4 7c0 2 1 2 1 3 1 2 2 3 4 4-1 1-1 1-2 1l2 2-1 1-2 1c-1 1-1 1-2 1s-2 1-3 1l-1 1h0c0 1-1 1-1 2h-2c1 1 1 1 2 1 0 1-1 2-2 2-2 0-4-1-6-1-2 1-3 0-4-1h-4 0c-3 0-6-1-8-3 1-4 2-8 4-13s4-10 7-15z"></path><path d="M626 483c6-2 11-4 16-5l2 2-1 1-2 1c-1 1-1 1-2 1s-2 1-3 1l-1 1v-2h-2-7z" class="F"></path><path d="M621 457l7 1c-2 2-5 4-6 7-4 5-6 14-9 20 2 1 3 1 5 1h0c2-2 6-2 8-3h7 2v2h0c0 1-1 1-1 2h-2c1 1 1 1 2 1 0 1-1 2-2 2-2 0-4-1-6-1-2 1-3 0-4-1h-4 0c-3 0-6-1-8-3 1-4 2-8 4-13s4-10 7-15z" class="V"></path><path d="M626 483h7l-4 2c-3 1-6 1-9 1h-2c2-2 6-2 8-3z" class="a"></path><path d="M633 483h2v2h0c0 1-1 1-1 2h-2c1 1 1 1 2 1 0 1-1 2-2 2-2 0-4-1-6-1-2 1-3 0-4-1 1 0 3 1 5 0 0 0 2-1 2-2v-1l4-2z" class="L"></path><path d="M642 608c2-2 4-5 5-8l3-3c-2 5-5 10-8 14-2 2-3 4-4 6l-4 4h1l3 6-1 1c1 2 0 4 0 7v2h2v-1h0l6-1 11-1v-2c2 1 5 3 7 4 1 3 5 4 7 6l1 1h1c1 0 1 0 2-1h1c0-1 1-1 1-2l1 1-3 3c-1 1 0 0 0 1v1c0 2 2 3 3 5h-3c-4-1-8 1-12 0-4 0-8-2-12-2 1 1 2 1 3 1 3 1 6 2 8 4-6-2-10-3-16-4-8-1-19 1-27 4-1 1-1 1-2 1s-1 0-2 1c-1 0-1 1-2 1l1-3-4 2c-1 0-3 1-4 2-5 3-9 6-14 9l-2 2c-1 0-2 1-3 1-7 7-14 14-18 22l-1 1v1c-1 0-1 1-2 2l-1 1-1-1v-1l1-1c1-3 3-6 3-9 0-1 2-4 3-5-3 1-5 3-7 6v-3c1 0 1-1 2-1h1c0-1 1-2 1-3l-2 2-2-2h-2-3c-1-1-2-1-3-2l2-1-3-1c-1 0 0 0-1-1-1-3 1-8 2-10h-3v-2h4l2-1c2 0 4 0 5-1h2c15-4 31-10 44-20 2 0 4-2 5-3 11-8 21-18 28-29z" class="b"></path><path d="M598 660v-2-1l1-2c2-1 3-2 6-2l1 1 1 1-9 5z" class="M"></path><path d="M589 655c2 0 3-1 5-1l-18 18c1-6 7-9 10-13 1-2 2-3 3-4z" class="W"></path><path d="M656 632c2 1 5 3 7 4 1 3 5 4 7 6-3-1-6-3-8-4-8-2-15-2-23-1v-1h0l6-1 11-1v-2z" class="B"></path><path d="M568 663h0c2-1 2-1 4 0h0 1c1 0 1 0 2-1l1-1c1-1 2-2 3-2l2-1h1c1-1 1-1 2 0v1c1 0 1-1 2-2v-1c1 0 2-1 3-1-1 1-2 2-3 4-3 4-9 7-10 13l-6 8c-3 1-5 3-7 6v-3c1 0 1-1 2-1h1c0-1 1-2 1-3l-2 2-2-2h-2-3c-1-1-2-1-3-2l2-1c3 0 6 1 9 3l1-2c1-2 3-4 3-6 1-3 0-5-2-8h0z" class="T"></path><path d="M563 660h2c-1 1-1 2 0 3s2 0 3 0h0c2 3 3 5 2 8 0 2-2 4-3 6l-1 2c-3-2-6-3-9-3l-3-1c-1 0 0 0-1-1-1-3 1-8 2-10h-3v-2h4l2-1c2 0 4 0 5-1z" class="L"></path><path d="M563 660h2c-1 1-1 2 0 3s2 0 3 0h0l-1 1c1 2 1 2 1 4v-1c-1-1-4-4-6-4-1 0-5 1-7 1h-3v-2h4l2-1c2 0 4 0 5-1z" class="D"></path><path d="M561 664c1 1 1 1 2 1h0v2h2c1 2 2 3 2 4-1 2-1 2-3 3-3 0-5-1-7-3 1-3 2-5 4-7z" class="E"></path><path d="M642 608c2-2 4-5 5-8l3-3c-2 5-5 10-8 14-2 2-3 4-4 6l-4 4-12 12c-2 1-5 3-7 5v3l-1-2c-2 1-5 4-6 6l-14 9c-2 0-3 1-5 1-1 0-2 1-3 1v1c-1 1-1 2-2 2v-1c-1-1-1-1-2 0h-1l-2 1c-1 0-2 1-3 2l-1 1c-1 1-1 1-2 1h-1 0c-2-1-2-1-4 0h0c-1 0-2 1-3 0s-1-2 0-3c15-4 31-10 44-20 2 0 4-2 5-3 11-8 21-18 28-29z" class="C"></path><defs><linearGradient id="i" x1="622.604" y1="629.152" x2="632.643" y2="635.559" xlink:href="#B"><stop offset="0" stop-color="#a8a4a5"></stop><stop offset="1" stop-color="#d6d4d5"></stop></linearGradient></defs><path fill="url(#i)" d="M634 621h1l3 6-1 1c1 2 0 4 0 7v2h-1c-2 0-4 0-6 1-4 1-7 2-11 3-3 1-6 3-9 4h-2c1-2 4-5 6-6l1 2v-3c2-2 5-4 7-5l12-12z"></path><path d="M636 637c-1-1-1-2-1-3h-1c0-1 0-2-1-3l-1-1c1-2 1-2 2-3 1 0 2 1 3 1 1 2 0 4 0 7v2h-1z" class="c"></path><path d="M671 643h1c1 0 1 0 2-1h1c0-1 1-1 1-2l1 1-3 3c-1 1 0 0 0 1v1c0 2 2 3 3 5h-3c-4-1-8 1-12 0-4 0-8-2-12-2 1 1 2 1 3 1 3 1 6 2 8 4-6-2-10-3-16-4-8-1-19 1-27 4-1 1-1 1-2 1s-1 0-2 1c-1 0-1 1-2 1l1-3-4 2c-1 0-3 1-4 2-5 3-9 6-14 9l-2 2c-1 0-2 1-3 1 3-4 7-7 12-10l9-5c10-5 21-8 32-8 4-1 7-1 11 0 6 1 14 4 20 1 1-1 2-1 2-3v-1l-1-1z" class="G"></path><path d="M613 654c1 1 4-1 5-1 5-3 11-3 17-4 5-1 11-2 15 0 1 1 2 1 3 1 3 1 6 2 8 4-6-2-10-3-16-4-8-1-19 1-27 4-1 1-1 1-2 1s-1 0-2 1c-1 0-1 1-2 1l1-3z" class="J"></path><path d="M527 537l2 1h1c-1-1-1-1 0-2v2c1 1 3 2 4 3l4 1h5l-1 1h1l1 1h1 1c1 0 2 0 3-1l6-2-4 14-3 8c-1 4-2 10-3 14l-3 9c-1 3-1 8-3 10l-2 2s-1 1-1 2h0l1 1h1c-9 2-17 5-26 6l-1 1c0-2 1-1 1-3 0-1-1-1-1-1-2-2-3-7-4-9v-2l-2-6c-2-4-3-9-4-12l-2-6-1-4-5-13c2-1 3-1 4-2l3 4-2 2c2 1 2 0 3-1 2-1 3-1 5-1l3-1h1c2-1 4-2 5-4 1-1 3-2 4-3l-1-2c1-1 2-3 3-4l1-1-1-2c2 1 3 2 5 3-1-1-1-2-1-3h2z" class="Q"></path><path d="M535 566l-1 3 1 1-1 1h2c1-1 0-1 1-1-1 2-2 3-3 4-2-1-2-1-3-1-1-1-1-1-1-3 2-2 3-2 5-4z" class="F"></path><path d="M537 570h0c2-1 3-2 3-4h-1c0 1 0 1-1 2 1-3 1-4 3-6v-1l1-2c1 0 2 2 3 3-1 2-1 4-2 6-2 6-8 12-14 15-1 0-1 0-2 1h0c-1-1-1-2-1-3l3-3-1-1c-1 0-1 0-1 1v1h-2v-2l-1-1c0-2 0-2 1-3l1 1c1 0 1-1 2 0h1l2-1c1 0 1 0 3 1 1-1 2-2 3-4z" class="D"></path><path d="M545 562l1 2c0 1-1 3-1 4 0 4-3 3-2 7h1v1h0c-2 2-3 4-5 5-2 3-5 5-8 6l-1 1-10 4c-2 1-3 1-5 1-1-1-1-1-3-1h0c6-3 11-5 17-9 6-3 12-9 14-15 1-2 1-4 2-6z" class="X"></path><path d="M501 575l1-1c1 0 2 0 3-1h3c2-1 3-1 5-1h4l-1 1h-3l-2 2v1c2 0 3-1 4 0v1c1 1 1 1 2 1 2 0 3 2 4 3l-4 4h0c-1 1-2 2-2 3h-2v1c-2 0-3 0-4-1-2 0-3-1-4-1-2-4-3-9-4-12z" class="Y"></path><path d="M506 584l2-1h0c1 1 2 1 4 1h1c1 0 3 0 4 1-1 1-2 2-2 3h-2c-3-1-5-2-7-4z" class="E"></path><path d="M501 575l1-1c1 0 2 0 3-1h3c2-1 3-1 5-1h4l-1 1h-3l-2 2v1c-2 0-4 1-6 3 0 1-1 1-1 2l2 2v1c2 2 4 3 7 4v1c-2 0-3 0-4-1-2 0-3-1-4-1-2-4-3-9-4-12z" class="D"></path><path d="M534 541l4 1h5l-1 1h1l1 1h1 1c1 0 2 0 3-1l6-2-4 14-3 8c-1 4-2 10-3 14l-1-1v-1h-1c-1-4 2-3 2-7 0-1 1-3 1-4l-1-2c-1-1-2-3-3-3l-1 2v1c-2 2-2 3-3 6 1-1 1-1 1-2h1c0 2-1 3-3 4h0c-1 0 0 0-1 1h-2l1-1-1-1 1-3 3-2v-1l-3 2h-6-1 0c1-2 1-2 1-4 1 1 1 1 2 1h1l1 1c1-1 1-1 2-1l4-3-1-1h0c-1-1 0-2 0-3-1-2-4-3-5-5h2c0-1-1-2-2-2h-1v-2h-1c0-1 0-2 1-3h1c0-1 1-1 1-2z" class="G"></path><path d="M549 543l6-2-4 14-3 8c-1-1 0-2 0-3h-1c-2-2-5-4-7-6 0-2 1-1 2-3-4 0-2-2-4-4v1c-1-1-2-1-2-2v-1h3 6l1-1c1 0 2 0 3-1z" class="U"></path><path d="M549 543l6-2-4 14c0-2 0-3-1-5-1-1-1 1-2 1l-1-1c1 0 1-1 1-2h0c1-1 1-2 1-4v-1z" class="D"></path><path d="M515 593c2 0 3 0 5-1l10-4 1-1c3-1 6-3 8-6 2-1 3-3 5-5h0l1 1-3 9c-1 3-1 8-3 10l-2 2s-1 1-1 2h0l1 1h1c-9 2-17 5-26 6l-1 1c0-2 1-1 1-3 0-1-1-1-1-1-2-2-3-7-4-9v-2h2c1-1 1-1 3-1h0c2 0 2 0 3 1z" class="I"></path><path d="M527 594c1 1 2 1 3 2 0-1-1-2-1-3h0 0l2 1c1 2 2 3 2 4l-18 5-1-1c0-1 1-1 2-2l1 1c0-1 1-1 2-2v1h1l1-3h0l-1-1v-1l1-1 4-1 2 1z" class="U"></path><path d="M507 593h2c1-1 1-1 3-1h0c2 0 2 0 3 1v1 1c0 1-1 1-1 2h1c1 0 1 0 2-1h1l1-2 1 1v1l1 1h0l-1 3h-1v-1c-1 1-2 1-2 2l-1-1c-1 1-2 1-2 2l1 1-4 1c-2-2-3-7-4-9v-2z" class="D"></path><path d="M507 593h2c1-1 1-1 3-1h0c2 0 2 0 3 1v1c-3 0-5 1-8 1v-2z" class="J"></path><path d="M515 593c2 0 3 0 5-1l10-4 1-1c3-1 6-3 8-6 2-1 3-3 5-5h0l1 1-3 9c-1 3-1 8-3 10l-6 2c0-1-1-2-2-4l-2-1h0 0c0 1 1 2 1 3-1-1-2-1-3-2l-2-1-4 1-1 1-1-1-1 2h-1c-1 1-1 1-2 1h-1c0-1 1-1 1-2v-1-1z" class="R"></path><path d="M525 593v-2h4c-1 1-1 2-2 3l-2-1z" class="Y"></path><path d="M529 591l1-1 1 1h0 0c1 0 1-1 2-2v1h1v-1h2c1-2 3-3 5-4l1 1c-1 3-1 8-3 10l-6 2c0-1-1-2-2-4l-2-1h0 0c0 1 1 2 1 3-1-1-2-1-3-2 1-1 1-2 2-3z" class="Q"></path><path d="M527 537l2 1h1c-1-1-1-1 0-2v2c1 1 3 2 4 3 0 1-1 1-1 2h-1c-1 1-1 2-1 3h1v2h1c1 0 2 1 2 2h-2c1 2 4 3 5 5 0 1-1 2 0 3h0l1 1-4 3c-1 0-1 0-2 1l-1-1h-1c-1 0-1 0-2-1 0 2 0 2-1 4h0-1v2l-1 1c-1-1-1-2-2-2-2 1-1 2-2 3l-1-1c-1 1-2 2-3 2 0 1-1 1-1 2h-4c-2 0-3 0-5 1h-3c-1 1-2 1-3 1l-1 1-2-6-1-4-5-13c2-1 3-1 4-2l3 4-2 2c2 1 2 0 3-1 2-1 3-1 5-1l3-1h1c2-1 4-2 5-4 1-1 3-2 4-3l-1-2c1-1 2-3 3-4l1-1-1-2c2 1 3 2 5 3-1-1-1-2-1-3h2z" class="X"></path><path d="M525 564c0-3-1-2-2-4 2-2 3-6 6-8v2c1 0 0 0 1 1l-2 2c0 1 0 2 1 4h0c0 2 0 2-1 4-1 0-2-1-3-1z" class="B"></path><path d="M512 560l-1 2v1c1-1 2-2 3-2v1l1 2-3 2h1c0 1 0 1-1 2v1h2 1c1-1 1-1 2-1 3-1 5-2 8-4h0c1 0 2 1 3 1h0-1v2l-1 1c-1-1-1-2-2-2-2 1-1 2-2 3l-1-1c-1 1-2 2-3 2 0 1-1 1-1 2h-4c-2 0-3 0-5 1h-3c-1 1-2 1-3 1l-1 1-2-6-1-4h1c2-1 2-1 4-1l2-1c1-1 2 0 3-1 2 0 3-1 4-2z" class="V"></path><path d="M498 565h1c2-1 2-1 4-1v1l2-1v2h1 3c-4 2-7 1-10 3l-1-4z" class="D"></path><path d="M512 560l-1 2v1c1-1 2-2 3-2v1c-2 1-4 2-5 4h-3-1v-2l-2 1v-1l2-1c1-1 2 0 3-1 2 0 3-1 4-2z" class="R"></path><path d="M527 537l2 1h1c-1-1-1-1 0-2v2c1 1 3 2 4 3 0 1-1 1-1 2h-1c-1 1-1 2-1 3s1 3-1 4h0c-2-1-2-1-3-1-1 2-3 6-5 7h0l-7 8-1-2v-1c-1 0-2 1-3 2v-1l1-2c-1 1-2 2-4 2-1 1-2 0-3 1l-2 1c-2 0-2 0-4 1h-1l-5-13c2-1 3-1 4-2l3 4-2 2c2 1 2 0 3-1 2-1 3-1 5-1l3-1h1c2-1 4-2 5-4 1-1 3-2 4-3l-1-2c1-1 2-3 3-4l1-1-1-2c2 1 3 2 5 3-1-1-1-2-1-3h2z" class="S"></path><path d="M506 554l3-1c1 1 1 2 2 3-1 0-1 1-2 1h-1l-3 1c-1-1-2-1-3-1l-1-2c2-1 3-1 5-1z" class="R"></path><path d="M523 548c0 2 1 3 0 4s-1 2-2 3l-2 2h0c1 0 2-1 3-1l-7 8-1-2v-1c-1 0-2 1-3 2v-1l1-2c-1 1-2 2-4 2-1 1-2 0-3 1l-1-1 6-3h1c5-2 9-7 12-11z" class="V"></path><path d="M512 560c1-1 3-2 5-3 1-1 2-1 4-2l-2 2h0c1 0 2-1 3-1l-7 8-1-2v-1c-1 0-2 1-3 2v-1l1-2zm-19-8c2-1 3-1 4-2l3 4-2 2c2 1 2 0 3-1l1 2c1 0 2 0 3 1-1 0-2 1-4 2-1 1-2 2-3 2h0c1 1 1 1 2 1 1-1 3-2 4-1l1 1-2 1c-2 0-2 0-4 1h-1l-5-13z" class="Q"></path><path d="M527 537l2 1h1c-1-1-1-1 0-2v2c1 1 3 2 4 3 0 1-1 1-1 2h-1c-1 1-1 2-1 3s1 3-1 4h0c-2-1-2-1-3-1-1 2-3 6-5 7h0c-1 0-2 1-3 1h0l2-2c1-1 1-2 2-3s0-2 0-4l2-1v-1c-1 1-2 1-3 2 0-1 0-1 1-2l2-2h0c0-1 0-2-1-3h-1c0 1-1 2-2 3v1c1 0 1 0 1 1l-3 2h-1l1-1v-1l-1-2c1-1 2-3 3-4l1-1-1-2c2 1 3 2 5 3-1-1-1-2-1-3h2z" class="K"></path><path d="M527 537l2 1h1c-1-1-1-1 0-2v2c1 1 3 2 4 3 0 1-1 1-1 2h-1c-1 1-1 2-1 3s1 3-1 4h0c-2-1-2-1-3-1 1-2 1-3 1-5h0l-2 3v-1c1-2 1-3 1-6h-1c-1-1-1-2-1-3h2z" class="F"></path><path d="M395 492c1 3 2 6 3 10l12 37c-2 7 5 26 7 33 1 1 2 3 3 5h0l2 4c4 6 6 13 9 21-2 1-2 2-5 2 1 1 2 2 3 2l4 3 3 11 6 15c1 4 2 8 3 11v1c-1 1-1 1-1 2h-2l-6-1h-2c-1 0-2-1-3-2h1v-2h0c0-1 0-2 1-3v-1c0-1 0-2-1-2-3-3-5-5-8-5l-1 1-2 2h-1v-1l-9-3c-3-2-6-5-9-8-6-4-12-9-16-16 1 1 2 1 3 2h0 1v-1c1 0 2 0 4-1 2-2 4-2 5-6 0 0 1-1 0-2 0-2-2-4-4-5-1-1-2-1-4-2v-1c1 0 1-1 1-1 0-2-1-2-2-3v-1-5c-1 0-3-4-3-6-1-1-1-2-1-4l-3-3-1-2v-3c-1-3-2-6-2-10v-1l-1-1c0-1 1-2 1-2 1-2 1-5 0-7l1-2h1l1-3 1-5c0-3 1-6 1-8 1-5 3-9 5-13l4-11c1-3 1-4 1-7h-1v-1l1-1z" class="B"></path><path d="M402 607h2v1c-1 1-2 2-4 3-1 0-2 0-3-1h0c1-2 3-2 5-3h0z" class="G"></path><path d="M420 624h0c2 0 2 0 4 1 1 1 1 2 1 4 0 1-1 2-3 3-1 0-2 0-4-1-1-1-1-2-1-3 1-2 2-3 3-4z" class="C"></path><path d="M413 607c2 0 3 1 4 3l1-1c1 0 2 1 3 2s1 1 2 1h1l3 2 9 7v2c-1-2-3-3-4-4l-2 1c-2 0-5-3-6-4-4-3-7-6-11-9z" class="J"></path><path d="M406 611h3c1 0 3 2 3 4v2c-1 2-3 4-5 5-1 0-2-1-3-1l-3-3c0-2 0-2 1-4s2-3 4-3z" class="H"></path><path d="M430 620l2-1c1 1 3 2 4 4v-2-1l6 15c1 4 2 8 3 11v1c-1 1-1 1-1 2h-2l-6-1h-2l-1-2c0-2 1-4 2-6 1-3 1-7 3-10h1c0-2-1-3-2-4l-7-6z" class="L"></path><path d="M437 643c1-1 1-1 2-1v-1h0c0-1 0-2 1-3 1 1 1 2 2 3v2h-2v1l-2 1c1-1 1-1 1-2h-2z" class="V"></path><path d="M434 648l-1-2c0-2 1-4 2-6 1-3 1-7 3-10 0 2 1 6 0 8l1 1h-2c0 1 0 1 1 2h-1-1c1 1 0 1 1 2h2c0 1 0 1-1 2l1 1h1c0 1 1 2 1 2l1 1-6-1h-2z" class="K"></path><path d="M389 556l5 1c-1 0-1 1-2 2h1l-1 2c1 1 1 2 1 3 1 0 2-1 1 1v2c1 1 2 1 3 4v1h-1c0 2 0 2 1 3l1 3 1 2 1 1c1 1 1 2 2 4 2 2 3 3 4 6h-1v1c2 0 2 1 3 1l1 1c0 2 4 4 5 7v1l3 1c1-1 1-2 1-3h1c1 1 2 2 4 2h0l1 2h2c1 1 2 2 3 2l4 3 3 11v1l-9-7-3-2h-1c-1 0-1 0-2-1s-2-2-3-2l-1 1c-1-2-2-3-4-3-2-2-4-4-5-6-6-7-11-13-14-21l-2-8v-3l-2-3c0-3-2-6-1-9v-1z" class="X"></path><path d="M389 556l5 1c-1 0-1 1-2 2h1l-1 2c1 1 1 2 1 3 1 0 2-1 1 1v2c1 1 2 1 3 4v1h-1c0 2 0 2 1 3l1 3 1 2 1 1c1 1 1 2 2 4 2 2 3 3 4 6h-1c-8-9-13-22-16-34v-1z" class="Y"></path><path d="M405 592c2 0 2 1 3 1l1 1c0 2 4 4 5 7v1l3 1c1-1 1-2 1-3h1c1 1 2 2 4 2h0l1 2h2c1 1 2 2 3 2l4 3 3 11v1l-9-7-3-2c-7-6-14-12-19-20z" class="Q"></path><path d="M417 603c1-1 1-2 1-3h1c1 1 2 2 4 2h0l1 2h2c1 1 2 2 3 2h-1l-2 2v1l-1 1h0c-3-1-3-4-4-6l-2 1-2-2z" class="E"></path><path d="M424 604h2c1 1 2 2 3 2h-1l-2 2v1c-1-1-1-1-1-2h-1v-1c1-1 0-1 0-2z" class="Y"></path><path d="M429 606l4 3 3 11v1l-9-7 2-1v-1h-1l-2-1c1 0 2-1 3-1v-1h-2l1-3h1z" class="W"></path><path d="M395 492c1 3 2 6 3 10-1 0-2 1-2 3-6 10-9 24-9 35 0 5 1 11 2 16v1c-1 3 1 6 1 9l2 3v3l2 8c-1-2-1-3-2-4h0l-1 1c1 3 2 6 3 8s3 6 3 8h-1c-1-1-2-2-2-3l-2-3-1-1c-1-1-1-2-1-3v-1c-1 0-3-4-3-6-1-1-1-2-1-4l-3-3-1-2v-3c-1-3-2-6-2-10v-1l-1-1c0-1 1-2 1-2 1-2 1-5 0-7l1-2h1l1-3 1-5c0-3 1-6 1-8 1-5 3-9 5-13l4-11c1-3 1-4 1-7h-1v-1l1-1z" class="K"></path><path d="M383 538l1-5c2 4 0 10 1 14 2 9 3 18 7 25l2 8c-1-2-1-3-2-4h0l-1 1c-1-2-2-5-3-7-2-7-3-13-4-21-1 1-2 0-3 1l-1 3-1-1c0-1 1-2 1-2 1-2 1-5 0-7l1-2h1l1-3z" class="G"></path><path d="M383 538c0 2 0 4 1 5v6c-1 1-2 0-3 1l-1 3-1-1c0-1 1-2 1-2 1-2 1-5 0-7l1-2h1l1-3z" class="N"></path><path d="M380 553l1-3c1-1 2 0 3-1 1 8 2 14 4 21 1 2 2 5 3 7 1 3 2 6 3 8s3 6 3 8h-1c-1-1-2-2-2-3l-2-3-1-1c-1-1-1-2-1-3v-1c-1 0-3-4-3-6-1-1-1-2-1-4l-3-3-1-2v-3c-1-3-2-6-2-10v-1z" class="I"></path><path d="M382 564c2 2 3 2 3 5h0c0 1 0 2 1 3l-3-3-1-2v-3z" class="M"></path><path d="M402 566c0-2 1-2 1-3l-1-1 1-1s1-1 2-1c1-1 2-3 3-2l1 3c1 1 1 1 1 2 0 2 1 4 2 6v-2h0c1 1 1 2 2 3 2 0 1 1 2 2h1c1 1 2 3 3 5h0l2 4c4 6 6 13 9 21-2 1-2 2-5 2h-2l-1-2h0c-2 0-3-1-4-2h-1c0 1 0 2-1 3l-3-1v-1c-1-3-5-5-5-7l-1-1c-1 0-1-1-3-1v-1h1c-1-3-2-4-4-6-1-2-1-3-2-4l-1-1-1-2-1-3c-1-1-1-1-1-3h1v-1l1-1-2-2c1-1 2-2 3-2h3z" class="W"></path><path d="M397 575l1-1h3c-1 1-1 1-1 2s0 2-1 4l-1-2-1-3z" class="D"></path><path d="M402 566c1 0 1 0 2 1s1 1 1 2h-1c-1 1-2 0-3 0l-1-1c1-1 1-1 2-1v-1z" class="E"></path><path d="M420 594h2 0c1 0 1 1 2 1 0 2 1 5 2 6-1 1-1 1-2 1h-1 0c-2 0-3-1-4-2h-1c0 1 0 2-1 3l-3-1v-1l2 1v-1l1-1c-1-1-1-1-1-2h2l-1-1 2-2c1 2 1 4 2 5 1-2 0-3-1-6z" class="R"></path><path d="M406 572c3 0 4 2 6 3-1 1-1 2-1 4l1 1c-1 1-1 2-1 3 0 2-1 1-1 2h-1c-1 0-1-1-2-1v-1c-1 0-1 0-2-1v-3-1c-1-1-1 0-1-1h-1c2-2 2-3 3-5z" class="E"></path><defs><linearGradient id="j" x1="424.558" y1="581.132" x2="413.112" y2="591.256" xlink:href="#B"><stop offset="0" stop-color="#272525"></stop><stop offset="1" stop-color="#403e42"></stop></linearGradient></defs><path fill="url(#j)" d="M412 569v-2h0c1 1 1 2 2 3 2 0 1 1 2 2h1c1 1 2 3 3 5h0l2 4c4 6 6 13 9 21-2 1-2 2-5 2h-2l-1-2h1c1 0 1 0 2-1-1-1-2-4-2-6-1 0-1-1-2-1h0-2v-1h-2l-1-1 1-1h2v-1h-1 1v-1l-1-2h-1v-3c-1-1-1-3-2-4s-1-1-1-2v-1l-1 2h0c0-2-1-3-1-5s-1-3-1-5z"></path><path d="M414 570c2 0 1 1 2 2h1c1 1 2 3 3 5h0l2 4c-5-2-6-7-8-11z" class="R"></path><path d="M389 556c-1-5-2-11-2-16 0-11 3-25 9-35 0-2 1-3 2-3l12 37c-2 7 5 26 7 33h-1c-1-1 0-2-2-2-1-1-1-2-2-3h0v2c-1-2-2-4-2-6 0-1 0-1-1-2l-1-3c-1-1-2 1-3 2-1 0-2 1-2 1l-1 1 1 1c0 1-1 1-1 3h-3c-1 0-2 1-3 2l2 2-1 1c-1-3-2-3-3-4v-2c1-2 0-1-1-1 0-1 0-2-1-3l1-2h-1c1-1 1-2 2-2l-5-1z" class="C"></path><path d="M399 544c-1 1-2 2-4 2h0l-1 1h2c-1 1-1 2-3 3h-1l1-2-1-1 1-2c1-1 1-1 0-3 1-1 1-2 2-2v-1c0-1 0-2 1-3 0 2 0 6 1 7l2 1z" class="W"></path><path d="M396 536c1-2 0-2 2-2s2 0 3 1 2 3 1 4c0 2-2 3-3 5l-2-1c-1-1-1-5-1-7z" class="K"></path><path d="M394 557l5-2v1l1-1h2 1l-1 1c1 1 1 1 1 2l-2 2-2-2c-1 1-1 2-2 3 2 1 3 0 4 0v1 1h-1c-1 0-1 0-1 1-2-1-3-1-4-2h-1l1-1c0-1-1-1-2-2h-1c1-1 1-2 2-2z" class="S"></path><path d="M500 213c1-6 3-13 4-19l3-17c0-1 1-2 2-3s2-1 4-1c7 2 6 15 10 19v1l7 34c-1 5 2 10 2 15v1c0 1 0 1 1 2v2c0 1 0 0 1 2v3l4 9v2 1l1 1c0 2 1 3 1 5l1 2c0 1 0 2 1 3v2 1l1 2v2l1 3h0l1 1c0 1 0 2 1 3-1 1-1 0-2 0l-1 1 2 2c-1 1-1 1-2 1s-2-1-3-2l-7-11-1 1c2 5 7 8 9 12l-1 1c1 1 1 2 3 3h2c2 1 3 2 5 2 1 0 2 1 3 1-2 1-4 1-7 2h-3c0-2-1-3-2-5s-3-3-5-4l-2-1-10-17-2 3c0-2 0-2-1-3l-1 2c-2 2-5 6-5 8-1 1-1 4-3 4-1 0-1-1-2-2l-7-13v8c-2 0-4 0-6 1-1 2-5 8-5 10-1 1-1 1-2 1h-2 0l-1 1c-2 2-3 4-4 7h-4c-2-2-5-2-8-2 3-1 6-3 8-3h2l3-3-1-1h-1-10l2-2v-1l1-2c1-1 1-2 1-4 1-1 2-2 2-4 1-2 2-4 2-6 1-1 1-1 1-2 0 0 1 0 1-1v-2-1l1-1v-2c1-1 1-1 1-2v-1c0-2 0-4 1-6 0-1 1-2 1-3l3-9c0-1 1-2 1-2 0-1 0-2 1-3v-2l2-1c1 0 2-2 2-3l1-1v-1c2-2 2-4 2-7l2-11z" class="c"></path><path d="M506 251c1 2 1 2 1 4l1 1h0c-1 1-2 2-2 3l-2 1c0-3 1-6 2-9z" class="C"></path><path d="M483 293h2v-1c1-1 1-2 2-2v-1c1-1 1-2 2-2 0-1 0-1 1-1v-1c1-1 1-2 2-3l1-1h0c1 0 1 0 0 1l-3 6-1 1c-1 2-2 2-2 4 0 0 1 0 1 1h0l-1 1c-2 2-3 4-4 7h-4c-2-2-5-2-8-2 3-1 6-3 8-3h2l3-3-1-1z" class="Z"></path><path d="M510 233l1-21c1-6 1-12 2-18l1 27 2 15c0 4 1 8 1 12 0 3 3 6 3 9v4l-1-1v1c-2-1-2-2-4-3l-1 1-1-4v1h-1-1l-1 1c0 2 0 2-1 3h-1v-4h0l-1-1c0-2 0-2-1-4l1-2c0-1 1-3 1-4l2-12z" class="H"></path><path d="M513 236h0l1-1h0v3c-1 1 0 1 0 1v3h-1c-1-1-1-2-1-3-1 0-1-1-1-1l2-2z" class="E"></path><path d="M510 233c0 1 1 1 2 2v1h1l-2 2s0 1 1 1c0 1 0 2 1 3-1 0-1 1-2 1l-2 1c1 1 1 1 2 1l-1 2-2-1v-1l2-12z" class="P"></path><path d="M507 249c0-1 1-3 1-4v1l2 1 1-2h1c2 2 1 7 1 10v1h-1-1l-1 1c0 2 0 2-1 3h-1v-4h0l-1-1c0-2 0-2-1-4l1-2z" class="S"></path><path d="M507 249l3 1 1 2-3 4-1-1c0-2 0-2-1-4l1-2z" class="T"></path><path d="M500 213c1-6 3-13 4-19l3-17c0-1 1-2 2-3s2-1 4-1c7 2 6 15 10 19v1l7 34c-1 5 2 10 2 15v1c0 1 0 1 1 2v2c0 1 0 0 1 2v3l4 9v2 1l1 1c0 2 1 3 1 5l1 2c0 1 0 2 1 3v2 1l1 2v2c-1-1-2-3-3-5-1-3-1-8-2-11l-9-30-7-31c0-3-1-5-2-7-1-3-2-5-3-8h0v-1c0-1 0-2-1-3v-3-1c-2-2-2-2-4-2-3 1-2 4-3 6l-1 1s-1 1-1 2c-1 1 0 1-1 2v3c0 1 0 0-1 2v1c0 1 0 1-1 3s-1 5-2 7c0 2-1 4-2 6z" class="G"></path><path d="M500 213c1-2 2-4 2-6 1-2 1-5 2-7s1-2 1-3v-1c1-2 1-1 1-2v-3c1-1 0-1 1-2 0-1 1-2 1-2 0 5-2 8-3 13-1 8-2 16-4 24l-7 24c-1 5-3 9-5 13l-3 15c-1 4-2 9-4 11l-1 1v1c0 1-1 1-1 2l2 2h-10l2-2v-1l1-2c1-1 1-2 1-4 1-1 2-2 2-4 1-2 2-4 2-6 1-1 1-1 1-2 0 0 1 0 1-1v-2-1l1-1v-2c1-1 1-1 1-2v-1c0-2 0-4 1-6 0-1 1-2 1-3l3-9c0-1 1-2 1-2 0-1 0-2 1-3v-2l2-1c1 0 2-2 2-3l1-1v-1c2-2 2-4 2-7l2-11z" class="F"></path><path d="M513 255l1 4 1-1c2 1 2 2 4 3v-1l1 1v-4l2 11c0 2 2 5 2 7l-2 3c0-2 0-2-1-3l-1 2c-2 2-5 6-5 8-1 1-1 4-3 4-1 0-1-1-2-2l-7-13v8c-2 0-4 0-6 1 3-6 6-14 7-20v-3l2-1c0-1 1-2 2-3v4h1c1-1 1-1 1-3l1-1h1 1v-1z" class="E"></path><path d="M514 277v-2h1c0 2 0 3-1 4 0 1-1 1-1 1v2h-2l-1-2c1-2 2-1 4-3z" class="Y"></path><path d="M513 282h1c1-1 1-3 3-5 0-1 1-2 2-2l1 2c-2 2-5 6-5 8-1 1-1 4-3 4-1 0-1-1-2-2 2-2 1-3 1-5h2z" class="R"></path><path d="M508 260h1c1-1 1-1 1-3l1-1h1 1c0 1 0 2-1 4l1 1v-1c2 2 2 5 2 7l-1 4v1c0 2 0 2-1 4l1 1c-2 2-3 1-4 3v-2h0l-1-1c-3-3-2-8-2-12 0-2 0-4 1-5z" class="C"></path><path d="M507 265h1 0c0 3 0 4 1 6v1 3 2c-3-3-2-8-2-12z" class="S"></path><path d="M513 276c-1-1-2-2-2-3-2-4-1-9 0-14l1 1 1 1v-1c2 2 2 5 2 7l-1 4v1c0 2 0 2-1 4z" class="X"></path><path d="M514 272c-1 1-1 1-2 1 0-2 0-3-1-4l2-2h1v4 1z" class="a"></path><path d="M508 256v4c-1 1-1 3-1 5 0 4-1 9 2 12l1 1h0v2l1 2c0 2 1 3-1 5l-7-13v8c-2 0-4 0-6 1 3-6 6-14 7-20v-3l2-1c0-1 1-2 2-3z" class="P"></path><defs><linearGradient id="k" x1="479.535" y1="767.365" x2="552.41" y2="786.291" xlink:href="#B"><stop offset="0" stop-color="#727071"></stop><stop offset="1" stop-color="#bbb8b9"></stop></linearGradient></defs><path fill="url(#k)" d="M563 686c2-3 4-5 7-6-1 1-3 4-3 5 0 3-2 6-3 9l-1 1v1l1 1 1-1c1-1 1-2 2-2v-1l-1 4-2 3c2-1 4-2 5-3h1 1l-1 1v1 1c-3 2-4 4-5 7-1 1-1 0-1 1-1 2-1 2-1 4l1 1-2 2c0 2-2 5-3 7-1 1-1 1-1 3l-4 9 1 2c-1 1-3 1-3 2v1c-3 3-4 7-6 11l1 1v2h-1v1c-1 2-2 3-3 5h-1c-2 3-3 7-4 10v1c0 1 0 3-1 4v2c-1 3-2 6-2 9l1 1-3 3v1c0 2 0 2 1 3v3l-1-1-1 1v2h1l2 2c-1 1-1 2-1 3-1 7-1 13-1 20l1 2c3 2 6 4 10 5l4 3h-1v1c-1 0-2 1-2 0h-1c-1 0-2-1-3-2h-2l-2-1v1 1h1c1 0 3 3 4 3 1 1 1 1 3 2v1l6 4c-3-1-7-3-10-4l-1-1h-1l-2-2c-2 2-2 6-3 9-1 1-1 2-1 3l-4 8-2 2v1c-2 2-4 5-7 6v1c-1 0-2 1-3 1l-1 1c-2 0-3 1-5 1h-2c-1 1-3 1-5 1-1-1-1-2-2-3h-1l-2-1c-1 0-2-1-3-2l1-1c-1-3-2-5-3-7-2-3-4-5-5-8-2-3-1-7-3-10-1 1-1 1-2 1l-1-1v-3c0-1 2-1 2-1h1v-3c-1 0-2-1-3-1v-3c-3 0-6 0-9 1v-1c1-1 2-1 4-1 1-1 5-1 7-1h0c-1-1-1-2-1-3 0-2-1-5-1-7-1-4-1-8-2-12l-3-14c-1-4-3-8-4-13 0-3-2-6-3-10-1-6-3-12-5-18-1-3-3-6-4-10v-2l2 4c1 1 0 1 1 1h2c0-1 0-1-1-2v-3h0l2 4c3 1 3 3 5 5l1-1c1 0 1 1 2 2l5 18v1l1-1c4 9 6 19 8 27 4 14 9 28 12 43 0 3 1 10 3 12 1-1 3-11 3-11l15-64c2-9 4-18 7-26v-2h1v1h3v-1h4l8-23c0-2 2-4 2-6v-3l2-2c-1-1-3-1-4-1h4c4-5 6-10 8-14 2-3 4-5 5-8z"></path><path d="M526 827h-1-1v-1c2-1 5 0 7 0s2 0 3 1c1 2 4 2 5 4l-13-4z" class="E"></path><path d="M521 826c0-3 0-6 1-8 1-1 0-1 1-1v-3c1-3 2-5 4-8l-2 9c0 2-2 5-2 7s-1 3-2 4z" class="K"></path><path d="M535 772l1 1v1c-1 0-1 1-1 1l-2 14v1l-1 1c-1 2-1 5-3 6l6-25z" class="C"></path><path d="M490 833c0-4-1-7 0-11l4 12c-1 3 1 5 1 8v3l1 1c0 2 0 3-1 4 1 2 1 3 1 5-1-2-2-5-2-7v-1c-1-1-1-2-1-4h-1v-2l-1-2c0-2 0-4-1-5v-1z" class="J"></path><path d="M555 721v1c-2 5-4 11-5 16l1 1 1-1v1c-3 3-4 7-6 11-1 1-1 1-3 2v1h-1c3-11 9-21 13-32zm-87 19c3 1 3 3 5 5l1-1c1 6 4 13 5 20-1-1-1-3-2-4-1-4-1-9-3-12v4c2 4 2 9 4 13v4 1c-1 0-1 0-2-1-1-9-5-20-8-29z" class="D"></path><path d="M546 750l1 1v2h-1v1c-1 2-2 3-3 5h-1c-2 3-3 7-4 10v1c0 1 0 3-1 4v2c-1 3-2 6-2 9l1 1-3 3 2-14s0-1 1-1v-1l-1-1c0-5 4-14 7-19h1v-1c2-1 2-1 3-2z" class="V"></path><defs><linearGradient id="l" x1="483.828" y1="771.084" x2="480.287" y2="803.373" xlink:href="#B"><stop offset="0" stop-color="#454343"></stop><stop offset="1" stop-color="#5b5a5c"></stop></linearGradient></defs><path fill="url(#l)" d="M478 765c3 8 4 16 6 24l5 24c0 3 2 6 1 8-3-6-4-14-5-20l-9-32c1 1 1 1 2 1v-1-4z"></path><path d="M521 826c1-1 2-2 2-4s2-5 2-7v6c1 1 2 1 2 1 1 0 1 1 2 2h1c1 0 1 1 2 1l-1 1c-2 0-5-1-7 0v1h1 1v2c0 1 0 2-1 3v1c1 2 2 2 2 5l-1 1c-1 0-1-1-2-1h-1 0v-3-1l1-1h0l1-1v-1l1-3c-1 3-3 5-4 7 0 2-1 4-2 6l-1 1-6 13c1-3 2-7 3-10l5-19zm43-126c2-1 4-2 5-3h1 1l-1 1v1 1c-3 2-4 4-5 7-1 1-1 0-1 1-1 2-1 2-1 4l1 1-2 2c0 2-2 5-3 7-1 1-1 1-1 3l-4 9 1 2c-1 1-3 1-3 2l-1 1-1-1c1-5 3-11 5-16v-1c1-4 3-7 4-10l5-11z" class="U"></path><path d="M529 797c2-1 2-4 3-6l1-1c0 2 0 2 1 3v3l-1-1-1 1v2h1l2 2c-1 1-1 2-1 3-1 7-1 13-1 20l1 2c3 2 6 4 10 5l4 3h-1c-1 1-2 0-4 0l-4-2c-1-2-4-2-5-4-1-1-1-1-3-1l1-1c-1 0-1-1-2-1h-1c-1-1-1-2-2-2 0 0-1 0-2-1v-6l2-9c0-3 1-6 2-9z" class="R"></path><path d="M530 805c1 4 0 13 2 17l1 1 1 2c-3-1-4-3-5-4-1-6 1-11 1-16z" class="L"></path><defs><linearGradient id="m" x1="529.391" y1="801.965" x2="534.893" y2="813.636" xlink:href="#B"><stop offset="0" stop-color="#787477"></stop><stop offset="1" stop-color="#989595"></stop></linearGradient></defs><path fill="url(#m)" d="M533 798l2 2c-1 1-1 2-1 3-1 7-1 13-1 20l-1-1c-2-4-1-13-2-17 1-1 1-2 1-3l2-3v-1z"></path><path d="M563 686c2-3 4-5 7-6-1 1-3 4-3 5 0 3-2 6-3 9l-1 1-1-1-2 3c1 2 0 2 0 4l-4 5v1h-1l-1 3v1c-1 4-3 8-4 12-2 3-3 6-4 9h-1c-2 4-3 9-5 13h0l-1-1-1-1 8-23c0-2 2-4 2-6v-3l2-2c-1-1-3-1-4-1h4c4-5 6-10 8-14 2-3 4-5 5-8z" class="G"></path><path d="M563 686c2-3 4-5 7-6-1 1-3 4-3 5-7 8-12 19-16 29 0-2 0-4-1-5s-3-1-4-1h4c4-5 6-10 8-14 2-3 4-5 5-8z" class="E"></path><path d="M550 709c1 1 1 3 1 5l-12 30-1-1 8-23c0-2 2-4 2-6v-3l2-2z" class="D"></path><defs><linearGradient id="n" x1="462.04" y1="795.056" x2="490.955" y2="761.876" xlink:href="#B"><stop offset="0" stop-color="#989192"></stop><stop offset="1" stop-color="#b1b3b6"></stop></linearGradient></defs><path fill="url(#n)" d="M466 736l2 4c3 9 7 20 8 29l9 32c1 6 2 14 5 20v1c-1 4 0 7 0 11v-1h0c-1-1-1-2-2-3h0v1 3h-1c0-3-1-5-2-8h0c-1-1-1-2-1-3 0-2-1-5-1-7-1-4-1-8-2-12l-3-14c-1-4-3-8-4-13 0-3-2-6-3-10-1-6-3-12-5-18-1-3-3-6-4-10v-2l2 4c1 1 0 1 1 1h2c0-1 0-1-1-2v-3h0z"></path><path d="M534 743h4l1 1 1 1c-3 3-4 9-5 13l-13 48v4c-3 7-3 16-6 23v-1-1c1-1 0-1 1-2v-1-1-1-1c0-1 0-1 1-2l-2-2c-1 0-2 1-3 2-1 2-1 4-2 5s-1 2-1 3-1 3-1 4h-1l15-64c2-9 4-18 7-26v-2h1v1h3v-1z" class="V"></path><defs><linearGradient id="o" x1="527.336" y1="763.001" x2="535.447" y2="766.912" xlink:href="#B"><stop offset="0" stop-color="#2c2a2a"></stop><stop offset="1" stop-color="#464648"></stop></linearGradient></defs><path fill="url(#o)" d="M534 743h4l1 1 1 1c-3 3-4 9-5 13l-13 48c-1-2 0-3 0-4l8-36-2-1c0-5 1-11 2-16v-4h0v-2h1v1h3v-1z"></path><path d="M530 745c2 0 4 1 6 0 0 1 0 2-1 3-2 5-3 12-5 18l-2-1c0-5 1-11 2-16v-4z" class="K"></path><defs><linearGradient id="p" x1="596.359" y1="576.878" x2="638.193" y2="596.08" xlink:href="#B"><stop offset="0" stop-color="#d0cecf"></stop><stop offset="1" stop-color="#f7f7f6"></stop></linearGradient></defs><path fill="url(#p)" d="M639 500l1-1c1 0 2 1 2 1 3 1 7-1 10-2 1 7 3 13 4 20 2 22 0 45-7 66-2 3-3 7-5 10v4c0 3-1 5-1 8l-1 2c-7 11-17 21-28 29-1 1-3 3-5 3-13 10-29 16-44 20h-2c-1 1-3 1-5 1l-2 1h-4c0-4 2-8 3-11l6-23 2 1 4-1 8-5c2-1 3-3 6-4 2-1 4-3 7-5 4-3 9-8 12-12h0c1-1 2-1 2-2 1-2 3-3 4-5 0-1 1-1 2-2 2-1 3-4 4-5v-1l1 1c1-2 2-4 3-7 2-3 3-6 5-9h1c1 1 0 2 0 4-1 1-1 1-1 2v1c1-1 2-2 2-3s0-1 1-2c0 0-1-2 0-2 0-1 1-1 1-2 3 2 8 2 11 3l2-3c0-1-1 0 0-1v-1-2c0-3 1-4 3-6l1-6v-7-2-1c0-1 0-1-1-2 0-1 0-2-1-3h-2l-1-2-1-1h-2v-1l1 1v-3c-1-2-1-3-1-4v-1h-2c3 0 5-1 7-3 2-1 2-3 3-6l-1-3-2-13v-3z"></path><path d="M583 630c-1-1-1-2 0-4 0-1 1-1 2-2h3c2 1 2 3 3 5-1 1-3 3-5 3-1 0-3-1-3-2z" class="C"></path><path d="M638 570c0-1-1 0 0-1v-1-2c0-3 1-4 3-6 0 5-1 9-3 14l-6 14c0 2-1 3-3 4v-1c1-1 1-2 2-3 0-2 0-2-1-3 0-2 0-1 1-2h1c2-2 2-7 4-10l2-3z" class="F"></path><path d="M642 519c0 6 1 12 1 18 0 2 0 5-1 7 0-1 0-1-1-2 0-1 0-2-1-3h-2l-1-2-1-1h-2v-1l1 1v-3c-1-2-1-3-1-4v-1h-2c3 0 5-1 7-3 2-1 2-3 3-6z" class="K"></path><path d="M635 536c1-1 3-1 4-1v-1h1l2 2-1 1h-4l-1-1h-2v-1l1 1z" class="V"></path><path d="M618 608l3-2-2 3c-3 7-10 13-17 16h0c-1 2-4 3-5 4l-1 1-1-1h0c-1 3-2 3-4 4-2 0 0 0-2 1h-2-1c-1-1-1-1-2-1-1-1-1-2-1-3 0 1 2 2 3 2 2 0 4-2 5-3s1-1 1-2c1-2 2-3 4-4 1 0 4-1 6-2l3-1c1-2 2-3 2-4 1-1 1-2 1-3 1-2 1-2 0-4l1 1c2-1 3-2 6-2h3z" class="I"></path><path d="M618 608l3-2-2 3c-2 2-4 4-6 5-3 3-4 5-8 6 1-2 2-3 2-4 1-1 1-2 1-3 1-2 1-2 0-4l1 1c2-1 3-2 6-2h3z" class="Z"></path><path d="M615 608h3c-1 2-2 2-4 3-2 0-4-1-5-1 2-1 3-2 6-2z" class="B"></path><path d="M581 619l-1 1c-1 2-3 3-5 4l-1 7c0 1 0 3 1 4 1 2 0 4 1 5 0 2 0 5-1 6-1 2-2 2-3 3h0-1 0c-1 3-8 8-8 11-1 1-3 1-5 1l-2 1h-4c0-4 2-8 3-11l6-23 2 1 4-1 8-5c2-1 3-3 6-4z" class="V"></path><path d="M567 630l1 1c-1 0-1 1-2 2l1 1-1 2h-1 0c-1-1-1-3 0-4l2-2z" class="U"></path><path d="M563 644c0-2 1-4 2-5l1 1h1 0c-1 1-1 1-1 2s0 2-1 3l-2-1z" class="L"></path><path d="M563 644l2 1 1 3c-3 3-6 8-8 11v2h0l-2 1c0-3 1-5 2-7 3-4 4-7 5-11z" class="K"></path><path d="M581 619l-1 1c-1 2-3 3-5 4l-1 7c0 1 0 3 1 4 1 2 0 4 1 5 0 2 0 5-1 6-1 2-2 2-3 3h0-1 0c-1 3-8 8-8 11-1 1-3 1-5 1h0v-2c2-3 5-8 8-11h2c2-1 5-1 6-3-1-1-2 1-4 1l2-2v-1h0v-5h0l-1-1v-2c1-1 0-2-1-3h0c1-1 1 0 1-2-1-1-3-1-4-2l8-5c2-1 3-3 6-4z" class="F"></path><path d="M625 570c3 2 8 2 11 3-2 3-2 8-4 10h-1c-1 1-1 0-1 2 1 1 1 1 1 3-1 1-1 2-2 3v1l-1 2h0c0 2-1 5-3 6h-2c0 3-1 4-2 6l-3 2h-3c-3 0-4 1-6 2l-1-1c1 2 1 2 0 4 0 1 0 2-1 3 0 1-1 2-2 4l-3 1c-2 1-5 2-6 2l-4-4h0l-1-1c0-2 1-3 1-4v-1c-2 1-2 1-4 1h0c4-3 9-8 12-12h0c1-1 2-1 2-2 1-2 3-3 4-5 0-1 1-1 2-2 2-1 3-4 4-5v-1l1 1c1-2 2-4 3-7 2-3 3-6 5-9h1c1 1 0 2 0 4-1 1-1 1-1 2v1c1-1 2-2 2-3s0-1 1-2c0 0-1-2 0-2 0-1 1-1 1-2z" class="M"></path><path d="M608 609c-1-1-1-3-1-5h1c0 1 1 1 2 2h0c2 1 3 1 5 2-3 0-4 1-6 2l-1-1z" class="G"></path><path d="M596 615c1-2 2-3 3-4h3c2 1 3 1 4 3l1 2c0 1-1 2-2 4l-3 1c-2 1-5 2-6 2l-4-4 1-2h2 1v-2z" class="H"></path><path d="M596 615c1-2 2-3 3-4h3-1v1c-1 2-3 5-4 7-1-2-1-3-1-4z" class="C"></path><path d="M592 619l1-2h2 1v-2c0 1 0 2 1 4 2 1 3 2 5 2-2 1-5 2-6 2l-4-4z" class="B"></path><path d="M611 595h0l1-1h6c3 2 4 3 5 6 0 3-1 4-2 6l-3 2h-3c-2-1-3-1-5-2-1-2-1-4-2-6l3-5z" class="H"></path><path d="M625 570c3 2 8 2 11 3-2 3-2 8-4 10h-1c-1 1-1 0-1 2 1 1 1 1 1 3-1 1-1 2-2 3v1l-1 2h0c0 2-1 5-3 6h-2c-1-3-2-4-5-6h-6l-1 1h0c1-2 2-4 4-5l1-1 5-10c1-1 2-2 2-3s0-1 1-2c0 0-1-2 0-2 0-1 1-1 1-2z" class="G"></path><path d="M619 588c1-1 1 0 2-1s1 0 3 0h0c-1 1-1 2-2 3h-1l-2-2z" class="W"></path><path d="M624 587v-2l1-1v-1h1c1 1 2 1 3 1l1 1c1 1 1 1 1 3-1 1-1 2-2 3-1-2-3-3-5-4h0z" class="a"></path><path d="M624 587c2 1 4 2 5 4v1l-1 2h0c0 2-1 5-3 6h-2c-1-3-2-4-5-6h-6l-1 1h0c1-2 2-4 4-5l1-1c2 0 2 0 3-1l2 2h1c1-1 1-2 2-3z" class="B"></path><path d="M621 590h1c2 1 4 3 6 4h0-4v1l-2-2c-1 0-1 0-2-1 0-1 1-1 1-2z" class="G"></path><path d="M611 595c1-2 2-4 4-5l3 1c0 1 0 1-1 2h1v1h-6l-1 1h0z" class="N"></path><path d="M624 587c2 1 4 2 5 4v1l-1 2c-2-1-4-3-6-4 1-1 1-2 2-3z" class="D"></path><defs><linearGradient id="q" x1="658.319" y1="560.668" x2="614.542" y2="564.474" xlink:href="#B"><stop offset="0" stop-color="#bebcbd"></stop><stop offset="1" stop-color="#e3e1e1"></stop></linearGradient></defs><path fill="url(#q)" d="M639 500l1-1c1 0 2 1 2 1 3 1 7-1 10-2 1 7 3 13 4 20 2 22 0 45-7 66-2 3-3 7-5 10v4c0 3-1 5-1 8l-1 2c-7 11-17 21-28 29-1 1-3 3-5 3h0l1-1c3-1 6-5 8-6l1-1c2-1 4-3 5-5 0-1-4-4-5-6l8-8c9-12 15-27 18-41 5-19 6-40 1-59 0-3-1-7-2-9-1-1-2-2-3-2l-2 1v-3z"></path><defs><linearGradient id="r" x1="576.47" y1="553.558" x2="613.024" y2="571.521" xlink:href="#B"><stop offset="0" stop-color="#1b191a"></stop><stop offset="1" stop-color="#3b393a"></stop></linearGradient></defs><path fill="url(#r)" d="M617 452l1 1v2c1 1 2 2 3 2-3 5-5 10-7 15s-3 9-4 13h-1l-1 1 2 2 2 3h-3c-1 2 3 8 4 10l6 13 1-1c0 1 0 3 2 4v-1h0c-1-1-1-2-1-3l1-1 1 2c1 1 0 1 1 1l1 1v2c1 2 2 5 2 7l2 2 3 1h2v1c0 1 0 2 1 4v3l-1-1v1h2l1 1 1 2h2c1 1 1 2 1 3 1 1 1 1 1 2v1 2 7l-1 6c-2 2-3 3-3 6v2 1c-1 1 0 0 0 1l-2 3c-3-1-8-1-11-3 0 1-1 1-1 2-1 0 0 2 0 2-1 1-1 1-1 2s-1 2-2 3v-1c0-1 0-1 1-2 0-2 1-3 0-4h-1c-2 3-3 6-5 9-1 3-2 5-3 7l-1-1v1c-1 1-2 4-4 5-1 1-2 1-2 2-1 2-3 3-4 5 0 1-1 1-2 2h0c-3 4-8 9-12 12-3 2-5 4-7 5-3 1-4 3-6 4l-8 5-4 1-2-1 2-4 8-27c4-17 10-34 15-50l7-24c2-5 3-9 5-13 0-3 2-7 3-11 2-1 3-2 4-4 0-1 1-2 1-3l-2-3 13-37z"></path><path d="M604 530l-1 5c0 1 0 1-1 1h-1c0-3 1-4 2-7l1 1z" class="C"></path><path d="M608 495v1l1 1-1 2c0 1-1 2-1 3h-1v-1c0-2 1-4 2-6z" class="P"></path><path d="M618 529h0c0 3 1 8 0 10h0v-4l-1-1h-1v1h0-1v-1c1-2 0-2 2-3 1-1 1-1 1-2z" class="C"></path><path d="M612 503c1 3 3 6 4 9-1 0-2-1-3-1v-2l-2-2c0-2 0-3 1-4z" class="D"></path><path d="M609 534c1 0 2 0 2 1 1 1 1 2 1 3 0 3-1 4-3 5-1 0-2-1-3-2 0-1-1-2 0-4 0-1 2-2 3-3z" class="O"></path><path d="M608 536h2v2 1l-1 1v1l-1-1v-4z" class="a"></path><path d="M605 529v-1c-1-1-1-2-1-3 1-2 2-4 2-6v-1c-2-3-2-4-5-5v-1h1l1-1c0 1 1 1 1 2h1l1-1c0 1 2 3 2 4v4 1c0 1 0 1 1 2 1 0 2 1 2 2 0 2-1 3-2 4 0 1-1 1-1 2 0 0-1-1-1-2h-2z" class="W"></path><path d="M605 495c-1 3-2 5-4 8-1 2-1 6-2 9 2-2 4-3 6-3 0 1 1 2 1 3h0l-1 1h-1c0-1-1-1-1-2l-1 1h-1v1c3 1 3 2 5 5v1c0 2-1 4-2 6 0 1 0 2 1 3v1h-1v-1 2l-1-1-1-3h0v2h-4c0-2 0-3-1-5 0-3 0-6 1-9v-4h0c0-3 2-7 3-11 2-1 3-2 4-4z" class="T"></path><path d="M584 591c2-2 4-4 6-4l1 1-1 1s-1 1-1 2l-1 1h0c-1 1 0 1-1 2s-1 1-1 2 0 1 1 2c-1 1-2 0-3 1-1-1-1-1-1-2-1 1-1 1-1 3h1l1 3h-3c1 1 1 2 2 3h1l1 2c-1 1-2 2-3 2-1 1-1 2-2 3v1 1l-4 2 1-3v-1c0-1 1-2 2-3v-2h-3v-1c1 0 2 0 3-1h-4v1l-1-1 1-1h1l-1-1 1-1c-2 0-3 1-4 1 2-3 6-6 9-9 2-2 3-1 3-4z" class="S"></path><path d="M617 452l1 1v2c1 1 2 2 3 2-3 5-5 10-7 15s-3 9-4 13h-1l-1 1 2 2 2 3h-3c-1 2 3 8 4 10l6 13 3 14 1 8c1 0 2 1 3 2l-1 3c0 4 1 8-1 12-1 1-1 1-1 3v1c0 1 0 1-1 2v2c0 1 0 1-1 1v2 1c0 1 0 1-1 2v2l-1 1c0 1-1 3-1 4l-3 6v-1-1c1-2 1-2 1-3h0c-1 1-2 2-3 4h-1-1l4-9c8-19 7-39 1-58-1-3-3-6-4-9l-3-6-1-1v-1l-2-3-2-3 13-37z" class="F"></path><path d="M617 452l1 1v2c1 1 2 2 3 2-3 5-5 10-7 15s-3 9-4 13h-1l-1 1 2 2 2 3h-3c-1 2 3 8 4 10l6 13 3 14 1 8h-1l-1-8v-4l-2-6c-2-8-6-15-10-22h-1v-1l-2-3-2-3 13-37z" class="W"></path><defs><linearGradient id="s" x1="570.115" y1="558.69" x2="598.018" y2="556.693" xlink:href="#B"><stop offset="0" stop-color="#3a3638"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#s)" d="M598 510h0v4c-1 3-1 6-1 9 1 2 1 3 1 5h0c3 19-2 39-14 54-3 4-7 8-11 12 0 1-1 2-2 3 4-17 10-34 15-50l7-24c2-5 3-9 5-13z"></path><path d="M595 549c1 2 0 2 0 4l-1 2c-1 5-3 10-4 14v1l-4 6c0 1-1 2-1 3l-1 1-2 2-1-1c2-4 5-9 7-13 1-3 1-6 2-9v-2c1-1 1-1 1-2 0 1 0 2 1 3 1-1 2-7 3-9z" class="G"></path><path d="M595 549c1-7 2-13 2-21h1c3 19-2 39-14 54-3 4-7 8-11 12 1-2 3-4 4-5 2-3 3-5 4-8l1 1 2-2 1-1c0-1 1-2 1-3l4-6v-1c1-4 3-9 4-14l1-2c0-2 1-2 0-4z" class="O"></path><path d="M619 514l1-1c0 1 0 3 2 4v-1h0c-1-1-1-2-1-3l1-1 1 2c1 1 0 1 1 1l1 1v2c1 2 2 5 2 7l2 2 3 1h2v1c0 1 0 2 1 4v3l-1-1v1h2l1 1 1 2h2c1 1 1 2 1 3 1 1 1 1 1 2v1 2 7l-1 6c-2 2-3 3-3 6v2 1c-1 1 0 0 0 1l-2 3c-3-1-8-1-11-3 0 1-1 1-1 2-1 0 0 2 0 2-1 1-1 1-1 2s-1 2-2 3v-1c0-1 0-1 1-2 0-2 1-3 0-4h-1c-2 3-3 6-5 9-1 3-2 5-3 7l-1-1v1c-1 1-2 4-4 5 0-1 1-2 2-3 0-1 1-2 1-2v-1l2-2c1-2 2-4 2-5l3-6c0-1 1-3 1-4l1-1v-2c1-1 1-1 1-2v-1-2c1 0 1 0 1-1v-2c1-1 1-1 1-2v-1c0-2 0-2 1-3 2-4 1-8 1-12l1-3c-1-1-2-2-3-2l-1-8-3-14z" class="N"></path><path d="M627 525l2 2 3 1h2v1c0 1 0 2 1 4v3l-1-1v1h2l1 1 1 2h2c1 1 1 2 1 3-4-1-7-2-11 0l-1-1c1 0 2 0 3-1l1-1h1l-1-2h-3c0-1 1-1 1-1 0-1-1-2-1-2-1-3-3-6-3-9z" class="B"></path><path d="M629 527l3 1h2v1c0 1 0 2 1 4-3-2-5-3-6-6z" class="a"></path><path d="M630 542c4-2 7-1 11 0 1 1 1 1 1 2v1 2c0 1-1 2-1 4-2 2-5 2-8 2l-2-1-3-1c-1-3-1-4 0-7 0-1 1-2 2-2z" class="E"></path><path d="M630 542c4-2 7-1 11 0 1 1 1 1 1 2v1c-1-1-2-1-3-1s-2 1-4 1h0c-1 0-1 1-2 0-2 0-3-1-4-1h-1c0-1 1-2 2-2z" class="D"></path><path d="M642 547v7l-1 6c-2 2-3 3-3 6v2 1c-1 1 0 0 0 1l-2 3c-3-1-8-1-11-3l1-3h1v-4c2-4 2-8 1-12l3 1 2 1c3 0 6 0 8-2 0-2 1-3 1-4z" class="G"></path><path d="M627 563h3v1 1l-2 3-2-1h1v-4z" class="F"></path><path d="M638 559v1c-2 2-2 1-4 1 0 2 0 2-1 3-1 0-1 0-2-1v-1h1 1v-2l-1-1h6z" class="O"></path><path d="M626 567l2 1c3 0 7 1 10 2l-2 3c-3-1-8-1-11-3l1-3z" class="D"></path><path d="M641 551c0 1 0 1-1 2s-1 2-1 4c0 0 0 1-1 2h-6c-1 0-1 0-2-1h1l4-1c-1-1-2-3-4-3h-1l1-2 2 1c3 0 6 0 8-2z" class="X"></path><path d="M641 551c0 1 0 1-1 2s-1 2-1 4h-2l-1-2v-2h-3c3 0 6 0 8-2z" class="K"></path><path d="M602 566l-1-1h1v-3h1c1 0 1 2 3 3l1-1c1 0 1 1 2 2h1l1 1h0l-1 1 1 1h1c0-1 1-1 2-1v-3h0c1 0 1 1 1 2v3l-4 9h1 1c1-2 2-3 3-4h0c0 1 0 1-1 3v1 1c0 1-1 3-2 5l-2 2v1s-1 1-1 2c-1 1-2 2-2 3-1 1-2 1-2 2-1 2-3 3-4 5 0 1-1 1-2 2h0c-3 4-8 9-12 12-3 2-5 4-7 5-3 1-4 3-6 4l-8 5-4 1-2-1 2-4 2-1c4-1 7-4 11-6l4-2v-1-1c1-1 1-2 2-3 1 0 2-1 3-2l-1-2h-1c-1-1-1-2-2-3h3l-1-3h-1c0-2 0-2 1-3 0 1 0 1 1 2 1-1 2 0 3-1-1-1-1-1-1-2s0-1 1-2 0-1 1-2h0l1-1c0-1 1-2 1-2l1-1-1-1c-2 0-4 2-6 4l2-4c2-2 4-3 6-6v-2c1-1 1-2 2-2l1-4c2 0 3 1 4 1 0-2 0-2-1-3l-2 1v-2c2-2 2-1 3-2 1 0 2-1 3-2z" class="C"></path><path d="M602 566c0 1 0 2 1 4h-1v2c0 1 0 1-1 2l-1 1-1-1c-1 2-2 3-1 5-1 0-2 1-2 2-1 1 0 1-1 2v1h-1l-1-1c0-1 1-1 1-2v-4l1-4c2 0 3 1 4 1 0-2 0-2-1-3l-2 1v-2c2-2 2-1 3-2 1 0 2-1 3-2z" class="Y"></path><path d="M611 579h1 1c1-2 2-3 3-4h0c0 1 0 1-1 3v1 1c0 1-1 3-2 5l-2 2v1s-1 1-1 2c-1 1-2 2-2 3-1 1-2 1-2 2-1 2-3 3-4 5 0 1-1 1-2 2h0c-3 4-8 9-12 12-3 2-5 4-7 5-3 1-4 3-6 4l-8 5-4 1-2-1 2-4 2-1c4-1 7-4 11-6l4-2v-1-1c1-1 1-2 2-3 1 0 2-1 3-2l-1-2h2 0c2 0 2 0 3-2v-1c0-1 0-2 2-2h1c2-1 3-1 5-2 6-6 10-13 14-20z" class="B"></path><path d="M584 606h2 0c2 0 2 0 3-2v-1c0-1 0-2 2-2h1c2-1 3-1 5-2-5 6-11 11-17 15v-1c1-1 1-2 2-3 1 0 2-1 3-2l-1-2z" class="R"></path><path d="M580 615h1c2-1 3-2 5-3 2-2 6-7 9-8-1 2-4 3-5 5l1 1 1-2c3-2 5-5 8-6-3 4-8 9-12 12-3 2-5 4-7 5-3 1-4 3-6 4l-8 5-4 1-2-1 2-4 2-1c4-1 7-4 11-6l4-2z" class="O"></path><path d="M565 623s0 1 1 1 2-1 3-1c2-2 5-3 7-4-2 3-5 4-8 6h0c2 0 4-2 7-2l-8 5-4 1-2-1 2-4 2-1z" class="Y"></path><defs><linearGradient id="t" x1="439.158" y1="389.743" x2="538.508" y2="355.292" xlink:href="#B"><stop offset="0" stop-color="#dbd8d9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#t)" d="M503 282v-8l7 13c1 1 1 2 2 2 2 0 2-3 3-4l1 2c-1 6-1 12-1 18l-1 47h0c-1 4 0 10-1 13l-1 1-2 3c-1 1-1 3-1 4l-1 2v1l-1 1c0 2-2 5-3 6-1 0-1-1-1-1l-1 1c1 0 1 1 1 2l-1 2c-2 0-2 0-3 1s-1 1-1 2v1c1 1 0 1 2 1 0 1 2 1 2 2v1l-2-1c1 1 1 1 1 2-1 0 0 0-1 1h-1c1 1 1 1 2 0 0 1 0 2-1 3h0l-2-1-1 1c2 2 4 3 6 5h0v-1l1-1c1 0 1-1 2-2 1-2 4-5 4-7s0-1 1-2l1-1h0v1 5 1h0c0 2 0 2 1 3v1l-1 4c-1 0-1 0-1-1-1 2-1 4 0 5v1l-2 8c-1 3-2 4-4 6l-6 6c-2 2-3 4-3 6h1l3-4 4 2 1 1-1 1c-1 1-2 2-4 2l1 1h2c0 2-2 2-1 4l5-2h2v-1c1 0 1 1 1 2l-1 2c2 0 4-1 5-1 0 1 0 1 1 2-1 1-1 2-1 3 1 2 2 2 1 4s-1 2-1 4c-3 0-5 1-7 2l-4 1c-5 2-10 3-15 4-1 0-2-1-4-1s-3 2-5 2c-2 1-5 3-6 5v3c-1-1-1-2-2-3l-1 2-1-1h-1v1c-3-6-5-12-7-18l-3-9 1-3c0-1 1-3 2-5 2 1 3 3 5 3 1 1 1 1 2 1 4-8 6-17 8-27l4-17-1-1c0-2 0-3 1-4 1-7 2-13 2-19v-4-8-6l-1-4-2-10c-1-2-1-2-3-3h-1c-1 0-1-1-1-2v-3l-1-1v-1c0-1 0-1-1-2v-1l-1-1c0-2 0-3-1-4l-1-3v-1c-1-2-1-2 0-3-1-1-2-1-2-2v-3h-1c0-1-2-2-2-4s1-2 2-3l5-5c0-3 1-4 0-6h1 10 1l1 1-3 3h-2c-2 0-5 2-8 3 3 0 6 0 8 2h4c1-3 2-5 4-7l1-1h0 2c1 0 1 0 2-1 0-2 4-8 5-10 2-1 4-1 6-1z"></path><path d="M504 361c0-2 0-4 1-6l1-22v-1 18c0 2 0 7-1 9v2h-1z" class="b"></path><path d="M499 301h3l1-3-1 15-3-12z" class="S"></path><path d="M504 361h1c-1 6 0 13-2 19-1-2-1-4-2-6 0-2 2-4 2-6 1-2 1-4 1-7z" class="I"></path><path d="M461 437c2 1 3 3 5 3 1 1 1 1 2 1v1c-2 2-3 5-4 8-1 1-2 2-3 4l-3-9 1-3c0-1 1-3 2-5z" class="D"></path><path d="M461 437c2 1 3 3 5 3 1 1 1 1 2 1v1c-1 0-2 0-3 1l-1 1c-1 1-1 3-2 4-1-1-1-3-2-4h0l-1-2c0-1 1-3 2-5z" class="R"></path><path d="M460 444c0-1 0-1 1-2l1 1h1l-2-2 1-1 3 3-1 1c-1 1-1 3-2 4-1-1-1-3-2-4z" class="S"></path><path d="M501 374c1 2 1 4 2 6v2l-1 1c1 0 1 1 1 2l-1 2c-2 0-2 0-3 1s-1 1-1 2v1c1 1 0 1 2 1 0 1 2 1 2 2v1l-2-1c1 1 1 1 1 2-1 0 0 0-1 1h-1c1 1 1 1 2 0 0 1 0 2-1 3h0l-2-1-1 1c-1-2 0-2 0-3l2-2c-1-1-1-1-2-1l-1-1c-1-6 3-14 5-19z" class="Z"></path><path d="M497 283c2-1 4-1 6-1l-1 1 1 15-1 3h-3l-2-4c-2-1-3-3-5-4 0-2 4-8 5-10z" class="C"></path><path d="M497 297s1-1 2-1c0 1 0 1 1 2v-5l-1-1v-4c0-3 2-4 3-5l1 15-1 3h-3l-2-4z" class="E"></path><path d="M496 369h0c-5 10-12 19-16 28l-1-1c0-2 0-3 1-4 1-7 2-13 2-19l9-2c2 0 3-1 5-2z" class="T"></path><path d="M482 373l9-2-1 1c0 1-1 3-2 4-2 2-3 4-4 6l-3 10h-1c1-7 2-13 2-19z" class="R"></path><path d="M490 372c0 1-1 3-2 4-2 2-3 4-4 6h0v-3c0-1 0-2 1-3 0-1 2-3 3-3l2-1z" class="Q"></path><path d="M491 358l8-2 1 1c-1 2-1 4-2 7 0 1-1 4-2 5h0c-2 1-3 2-5 2l-9 2v-4-8 1h1c3-1 6-3 8-4z" class="C"></path><path d="M498 364c0 1-1 4-2 5h0c-2 1-3 2-5 2l-9 2v-4l7-2c1-1 2-1 3-1 2-1 4-2 6-2z" class="X"></path><path d="M498 364c0 1-1 4-2 5h0c0-1 0-1-1-2-2 1-3 1-5 2h-1v-1l1-1h-1c1-1 2-1 3-1 2-1 4-2 6-2z" class="J"></path><path d="M495 342l6-2-1 9v8l-1-1-8 2c-2 1-5 3-8 4h-1v-1-6l-1-4 2-2c0-1 0-2-1-3l1-1 12-3z" class="V"></path><path d="M482 362c1-1 2-1 3-2v-2c0-1 1-2 2-2h1c1-1 2-2 4-2 0 1-1 2-1 4-2 1-5 3-8 4h-1z" class="K"></path><path d="M492 354l5-4h0v1c1-1 2-1 3-2v8l-1-1-8 2c0-2 1-3 1-4z" class="J"></path><path d="M481 351l2-2c0-1 0-2-1-3l1-1 12-3h-1c-2 1-4 2-6 2v1 1 3c-1 1-2 2-4 2l1 1h2v1 1l-1 1h-1l1-1v-1l-2 2v2h-1v-1c-1-1 0-1-1-1l-1-4z" class="Q"></path><path d="M471 293h1 10 1l1 1-3 3h-2c-2 0-5 2-8 3 3 0 6 0 8 2h4c1-3 2-5 4-7l1-1h0 2c1 0 1 0 2-1 2 1 3 3 5 4l2 4 3 12v15c0 2 0 4-1 6v6l-6 2-12 3-1 1c1 1 1 2 1 3l-2 2-2-10c-1-2-1-2-3-3h-1c-1 0-1-1-1-2v-3l-1-1v-1c0-1 0-1-1-2v-1l-1-1c0-2 0-3-1-4l-1-3v-1c-1-2-1-2 0-3-1-1-2-1-2-2v-3h-1c0-1-2-2-2-4s1-2 2-3l5-5c0-3 1-4 0-6z" class="I"></path><path d="M493 314h1v1l2 2v3c0 1-1 1-1 2v1c0 1 0 1-1 2l-1 1v-2l-1-1v-2c0-2-1-3-1-4 1-1 2-2 2-3z" class="Z"></path><path d="M488 294h2c5 5 6 9 6 16v7l-2-2v-1h-1c0 1-1 2-2 3h-1v-5c-1-5-4-7-7-10 1-3 2-5 4-7l1-1h0z" class="C"></path><path d="M483 302c1-3 2-5 4-7v3c-1 2-2 1-1 3h0c2 1 2 2 2 4l4 4-2 3c-1-5-4-7-7-10z" class="T"></path><path d="M488 294h2c5 5 6 9 6 16v7l-2-2v-1h-1c0-2 0-2 1-3 0-3 0-4-2-6l-2-1v-1h1s0-1 1 0h1v-1c-1-1-1-2-2-3h-2v-2c0-1-1-2-1-3h0z" class="P"></path><defs><linearGradient id="u" x1="488.704" y1="334.32" x2="491.112" y2="341.876" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#3a3838"></stop></linearGradient></defs><path fill="url(#u)" d="M476 338c8-3 16-4 24-8v-1l1 1v4 6l-6 2-12 3-1 1c1 1 1 2 1 3l-2 2-2-10c-1-2-1-2-3-3z"></path><defs><linearGradient id="v" x1="475.285" y1="308.619" x2="483.723" y2="322.584" xlink:href="#B"><stop offset="0" stop-color="#a09c9e"></stop><stop offset="1" stop-color="#c6c3c5"></stop></linearGradient></defs><path fill="url(#v)" d="M471 293h1 10 1l1 1-3 3h-2c-2 0-5 2-8 3 3 0 6 0 8 2h4c3 3 6 5 7 10v5h1c0 1 1 2 1 4-2 1-3 1-5 1h-1c-1 1-2 1-3 0h0l-5-1h0l-6-3h0c-2-1-2-1-3-2s-2-1-2-2v-3h-1c0-1-2-2-2-4s1-2 2-3l5-5c0-3 1-4 0-6z"></path><path d="M479 302h4c3 3 6 5 7 10v5h-1c-1 0-2-1-3-3l-1-1v-1-1c-1-2-1-2-2-3s0-2-1-2c0-1-1-1-1-2l-2-2z" class="G"></path><path d="M471 293h1 10 1l1 1-3 3h-2c-2 0-5 2-8 3 3 0 6 0 8 2l2 2h-1 0c-2-2-4-2-6-2-1 0-2 1-3 1-2 0-2 0-3 1v4s0 1 1 0h5c1-1 3-1 4 0h1c1 1 2 1 3 2h0c-5-1-9-1-14 0l-1 1h-1c0-1-2-2-2-4s1-2 2-3l5-5c0-3 1-4 0-6z" class="C"></path><path d="M511 392l1-1h0v1 5 1h0c0 2 0 2 1 3v1l-1 4c-1 0-1 0-1-1-1 2-1 4 0 5v1l-2 8c-1 3-2 4-4 6l-6 6c-2 2-3 4-3 6h1l3-4 4 2 1 1-1 1c-1 1-2 2-4 2l1 1h2c0 2-2 2-1 4l5-2h2v-1c1 0 1 1 1 2l-1 2c2 0 4-1 5-1 0 1 0 1 1 2-1 1-1 2-1 3 1 2 2 2 1 4s-1 2-1 4c-3 0-5 1-7 2l-4 1c-5 2-10 3-15 4-1 0-2-1-4-1s-3 2-5 2c-2 1-5 3-6 5v3c-1-1-1-2-2-3l-1 2-1-1c0-1 0-1 1-2l2-5-1-2h1 1l2-1c0-1 1-2 2-3 0 0 1-1 2-1l3-6-1-1-2 3h0c1-3 0-4 2-7 1-2 1-7 3-9 1-1 0-2 1-3h1 0c1-2 0-2 1-4l1 1v-1-1c-1-1-1-1-1-3l3-1-3-3v-3c0-1 1-2 2-2l-2-1-1-1c2-3 3-7 4-9 1-3 2-5 3-7v-1c1 4-3 6-3 10l3-3c0-1 0-2 1-3h1c1-1 0-4 1-5h1c0 1-1 1 0 3 2 2 4 3 6 5h0v-1l1-1c1 0 1-1 2-2 1-2 4-5 4-7s0-1 1-2z" class="M"></path><path d="M493 406c0-1 1-2 2-3 1 0 1 0 1 1 1 1 0 1 1 2l-3 1s-1 0-1-1z" class="c"></path><path d="M493 411c-2-1-2-1-3-2l3-3c0 1 1 1 1 1-1 1-1 2-1 4z" class="I"></path><path d="M487 416l1-2 1-1c1 0 1 1 2 2v2h-2l-2-1z" class="c"></path><path d="M490 425l1 1c0 1-1 2-1 4l-2-1c-1-1-1-1-1-3l3-1z" class="b"></path><path d="M503 411c0 2 1 6 0 8l-1 1v1c0 1-1 2-2 3v1l-1 1-1-1h-2c0-2 0-5 1-6 1 0 2 0 4-1l2-7z" class="F"></path><path d="M490 439h-1c0-1-1-1-1-1v-2l1-2h-1v-3h0c1 0 1 0 2-1l1 1c1 3 0 8 2 10-1 4-1 6-3 9l-1 1c-1 1-2 2-3 4l-1-1 3-4h0 1l1-2c1-2 2-5 1-7l-1-2z" class="N"></path><path d="M481 450c0-1 3-7 4-7 1-1 1-2 1-3v-1h4l1 2c1 2 0 5-1 7l-1 2h-1 0l-2 1h-1-3l-1-1z" class="I"></path><path d="M491 441c1 2 0 5-1 7l-1 2v-4c-1 0-2 0-3-1s0-2 0-3h3l2-1z" class="B"></path><path d="M511 392l1-1h0v1 5 1h0c0 2 0 2 1 3v1l-1 4c-1 0-1 0-1-1-1 2-1 4 0 5v1l-2 8c-1 3-2 4-4 6l-6 6-2-2h0l-1-1c2 0 2-1 3-2h0l1-1v-1c1-1 2-2 2-3v-1l1-1c1-2 0-6 0-8-1-1-2-3-2-5h0v-1l3 3v-2l2 1c0-1 0-1 1-2 0-1 0-1-1-1v-3c1-2 4-5 4-7s0-1 1-2z" class="O"></path><path d="M511 392c0 1 0 3-1 4 0 2-2 3-2 5v3 2c0 2 0 2-1 3s-1 3-1 4c-1 5-3 8-6 12v-1c1-1 2-2 2-3v-1l1-1c1-2 0-6 0-8-1-1-2-3-2-5h0v-1l3 3v-2l2 1c0-1 0-1 1-2 0-1 0-1-1-1v-3c1-2 4-5 4-7s0-1 1-2z" class="B"></path><path d="M497 406c1 0 2 1 3 2v1c1 1 1 5 0 6 0 1-1 2-1 3-2 0-3-1-3-2-1-1-1-3-2-4 1 1 1 3 1 5 1 4 0 10 0 14 0 3-1 7-1 10 1-1 1-2 2-4h1l3-4 4 2 1 1-1 1c-1 1-2 2-4 2l1 1h2c0 2-2 2-1 4-4 2-6 4-9 7s-5 6-8 8l-2 3-1-1c-1-1-1-2-2-2v-1l-1-1 3-6h3 1l2-1-3 4 1 1c1-2 2-3 3-4l1-1c2-3 2-5 3-9 0-10 1-20 0-30 0-2 0-3 1-4l3-1z" class="G"></path><path d="M489 451c0 4-5 5-4 8l-2 3-1-1c-1-1-1-2-2-2v-1c1-1 1-2 3-2 0 0 1 1 2 1l1-2c1-2 2-3 3-4z" class="J"></path><path d="M482 451h3 1l2-1-3 4 1 1-1 2c-1 0-2-1-2-1-2 0-2 1-3 2l-1-1 3-6z" class="G"></path><path d="M502 444l5-2h2v-1c1 0 1 1 1 2l-1 2c2 0 4-1 5-1 0 1 0 1 1 2-1 1-1 2-1 3 1 2 2 2 1 4s-1 2-1 4c-3 0-5 1-7 2l-4 1c-5 2-10 3-15 4-1 0-2-1-4-1s-3 2-5 2c-2 1-5 3-6 5v3c-1-1-1-2-2-3l-1 2-1-1c0-1 0-1 1-2l2-5-1-2h1 1l2-1c0-1 1-2 2-3 0 0 1-1 2-1l1 1v1c1 0 1 1 2 2l1 1 2-3c3-2 5-5 8-8s5-5 9-7z" class="N"></path><path d="M477 458v1l-1 1h1c1 0 2 0 2-1l1 2h-4l-2 2v1h1c0-1 1-1 2-1l-1 1-2 2v1c2-1 3-2 5-2-2 1-5 3-6 5v3c-1-1-1-2-2-3l-1 2-1-1c0-1 0-1 1-2l2-5-1-2h1 1l2-1c0-1 1-2 2-3z" class="F"></path><path d="M514 444c0 1 0 1 1 2-1 1-1 2-1 3 1 2 2 2 1 4s-1 2-1 4c-3 0-5 1-7 2l-4 1 1-2h3c1-1 1-1 3-1h1c-1-2-1-3-3-4 1-1 1-2 1-4 0 0 0-1-1-1l-3 1 4-4c2 0 4-1 5-1z" class="B"></path><path d="M502 444l5-2h2v-1c1 0 1 1 1 2l-1 2-4 4h-1l-13 10c-2 1-4 3-6 4-1-1-1-1-2-1l2-3c3-2 5-5 8-8s5-5 9-7z" class="E"></path><defs><linearGradient id="w" x1="520.929" y1="388.894" x2="550.02" y2="381.469" xlink:href="#B"><stop offset="0" stop-color="#a8a5a6"></stop><stop offset="1" stop-color="#c9c7c8"></stop></linearGradient></defs><path fill="url(#w)" d="M520 277l1-2c1 1 1 1 1 3l2-3 10 17 2 1c2 1 4 2 5 4s2 3 2 5h3l-2 2 1 1 2-2c2-1 7-1 9 0 3 1 3 2 5 4-1 3-1 6-3 9l-1 3c0 1-1 2-1 3l-4 11c-1 1-1 2-1 3 0 3-1 7-2 9-1 0-1 0-2 1v3c0 1 0 1-1 3v2c0 1 0 4-1 6v9h0v-2l-1 1v6l-1 2 1 4 1 14v1c0-1-1-1-1-2h-1c5 11 4 23 8 34h1v1l1 1h0 0v-1h1 3v-1c-2 0-2 0-3-1l1-1c1 0 2 0 2-1l1 1 2 5c0 2 1 3 1 4v1c1 2 1 3 1 4l1 2-3 3-2 2c1 2 2 5 3 6 2 4 5 8 7 11 1 1 2 3 2 4h0c1 2 2 4 3 5 0 2 1 4 0 6h-1v1 2l-1 1h0v1h0v-3h0v-1-1c-1 0-2-1-2-1h-1v2h0c-2-1-3-2-4-3h-1c1 1 2 3 2 5v1l3 3v1l-6-6c0-2-4-4-5-5-3-2-6-4-8-7-8-8-16-16-25-22h-1c-1-1-3-2-5-3h-1c-1-1-1-1-1-2-1 1-2 2-2 3-1 0-3 1-5 1l1-2c0-1 0-2-1-2v1h-2l-5 2c-1-2 1-2 1-4h-2l-1-1c2 0 3-1 4-2l1-1-1-1-4-2-3 4h-1c0-2 1-4 3-6l6-6c2-2 3-3 4-6l2-8v-1c-1-1-1-3 0-5 0 1 0 1 1 1l1-4v-1c-1-1-1-1-1-3h0v-1-5-1h0l-1 1c-1 1-1 0-1 2s-3 5-4 7c-1 1-1 2-2 2l-1 1v1h0c-2-2-4-3-6-5l1-1 2 1h0c1-1 1-2 1-3-1 1-1 1-2 0h1c1-1 0-1 1-1 0-1 0-1-1-2l2 1v-1c0-1-2-1-2-2-2 0-1 0-2-1v-1c0-1 0-1 1-2s1-1 3-1l1-2c0-1 0-2-1-2l1-1s0 1 1 1c1-1 3-4 3-6l1-1v-1l1-2c0-1 0-3 1-4l2-3 1-1c1-3 0-9 1-13h0l1-47c0-6 0-12 1-18l-1-2c0-2 3-6 5-8z"></path><path d="M520 389v-1c1 0 2-1 3-1v-1l2 1c0 1 1 1 1 3l-1-1h-2-2-1z" class="V"></path><path d="M525 400l1-1h1c-1 3-5 4-5 7l3-3c1-1 1-1 2-1h0c-2 2-4 4-5 6h0c-1 2-1 4-1 5-1-1-2-1-3-2 0-1 0-4 1-5h1c0-2 4-4 5-6z" class="I"></path><path d="M520 277l1-2c1 1 1 1 1 3v18c-2-3 0-7-1-10v-1c0-1 0-1-1-2h-2 0v1c-1 1-1 1-1 2l-1 1-1-2c0-2 3-6 5-8z" class="B"></path><path d="M517 375l1-1 1 2h0v-2c1 0 1 1 2 2v1c1 1 1 2 1 3 1 2 2 3 2 5 1-1 1-2 0-3v-1c0-1-1-2 0-3 1 2 1 3 1 5 1 2 0 3 0 4l-2-1v1c-1 0-2 1-3 1v1c1 1 2 2 3 2s1 0 2 1 1 0 1 1l-1 1-1-1c-2 0-3-2-5-3l-1-10c0-2 0-3-1-5z" class="O"></path><path d="M516 389h1v-6c-1-1-1-1-1-2s0-1-1-2v-4l-1-1 1-1c1 2 1 5 3 7h0l1 10c2 1 3 3 5 3l1 1h-1l-1-1v1c0 1 0 2 2 2v1 2l-1 1h-1c-1-1-2-1-3-2 0-1-1-2-1-3l-2-2v-1c-1-2-1-1-1-3z" class="N"></path><path d="M557 424l1 1 2 5c0 2 1 3 1 4v1c1 2 1 3 1 4l1 2-3 3-2 2-4-11c-2-2-3-6-3-8h1v1l1 1h0 0v-1h1 3v-1c-2 0-2 0-3-1l1-1c1 0 2 0 2-1z" class="D"></path><path d="M560 430c0 2 1 3 1 4v1c1 2 1 3 1 4l1 2-3 3-2 2-4-11c2 1 2 1 3 3h2 1v-1h-2c1-1 2-1 2-2v-5z" class="Q"></path><path d="M526 365h1c1 1 1 3 2 4l6 2c3 1 6 3 8 3h1l-1 2 1 4 1 14v1c0-1-1-1-1-2h-1c-1 0-2-2-2-3l-8-12c-1-2-3-5-4-7l-3-6z" class="D"></path><defs><linearGradient id="x" x1="534.729" y1="375.144" x2="547.118" y2="385.788" xlink:href="#B"><stop offset="0" stop-color="#3b3839"></stop><stop offset="1" stop-color="#565456"></stop></linearGradient></defs><path fill="url(#x)" d="M535 371c3 1 6 3 8 3h1l-1 2 1 4 1 14v1c0-1-1-1-1-2l-9-22z"></path><path d="M544 380l-1 1h0l-1-2c0-1-1-2 0-3h1l1 4z" class="Q"></path><defs><linearGradient id="y" x1="531.72" y1="286.223" x2="515.28" y2="299.277" xlink:href="#B"><stop offset="0" stop-color="#040302"></stop><stop offset="1" stop-color="#2c2b2b"></stop></linearGradient></defs><path fill="url(#y)" d="M524 275l10 17c-7 4-8 8-9 15-1 4-1 8-1 12-2-7-2-15-2-23v-18l2-3z"></path><path d="M547 451l14 18c2 2 4 7 7 8v2h0c-2-1-3-2-4-3h-1c1 1 2 3 2 5v1l3 3v1l-6-6c0-2-4-4-5-5-3-2-6-4-8-7v-2h0c-2-1-3-2-3-4l1-1 2-2c-1-2-3-3-3-5l1-3z" class="I"></path><path d="M547 461l2-2 6 8c-1 0-2-1-2-1-2-1-1 0-3 0h-1 0c-2-1-3-2-3-4l1-1z" class="a"></path><path d="M549 466h1c2 0 1-1 3 0 0 0 1 1 2 1l2 2 6 7c1 1 2 3 2 5v1l3 3v1l-6-6c0-2-4-4-5-5-3-2-6-4-8-7v-2z" class="J"></path><path d="M550 466c2 0 1-1 3 0 0 0 1 1 2 1l2 2c-1 0-1 0-1 1v1h0l-2-1-4-4z" class="O"></path><path d="M532 440c1-1 2-3 2-4h1c0 1 1 2 1 3 1 2 6 6 6 8l-1 1c0 1-1 1-1 2 1 1 0 1 2 1v-1c1 0 1-1 2-1 1 1 2 1 3 2l-1 3c0 2 2 3 3 5l-2 2-1 1c0 2 1 3 3 4h0v2c-8-8-16-16-25-22h0l2-1c1 1 1 1 2 1v-2c-1-1-1-2-1-3h0c1 0 2 0 3 1 0 1 0 0 1 1v1l1-1v-3z" class="F"></path><path d="M543 455c1-1 1-2 2-3h0l1 2c0 2 2 3 3 5l-2 2c-1-3-2-4-4-6z" class="Z"></path><path d="M532 440c1-1 2-3 2-4h1c0 1 1 2 1 3 1 2 6 6 6 8l-1 1c0 1-1 1-1 2 1 1 0 1 2 1v-1c1 0 1-1 2-1 1 1 2 1 3 2l-1 3-1-2h0c-1 1-1 2-2 3l-4-4c-1-2 1-4 0-5-1 1-1 0-1 1v3c-2-1-4-2-5-2l-1-1-1 1c-1 0-1-1-1-1v-5c0 1 0 0 1 1v1l1-1v-3z" class="M"></path><path d="M521 413c0 1 0 1 1 2v-2c0-3 0-4 2-6l3-3c2 1 3 1 4 2l1 1 3 6-1 8v3 1l1-1h1c1 0 2-1 3-1v3c0 1-1 1-2 1l-3 1c-1 0-1 0-1-1-2 3 1 6 0 9 0 1-1 2-2 2v1c-1-1-1-3-1-5l-1 1c0-2-2-4-3-6-1-4-4-9-7-12-1-1-1 0-1-2 1 0 1 0 2 1h0c0-1-1-2-2-2v-3c1 1 2 1 3 2z" class="B"></path><path d="M521 413c0 1 0 1 1 2v-2c0-3 0-4 2-6l3-3c2 1 3 1 4 2l1 1 3 6-1 8-1-1v-5c-1 0-1 0-2-1v-1l-1 1v-1h1c1-1 1-2 1-3s0-1-1-1h-2l1-2c-1-1-1-1-3-2-1 1-1 2-2 2l-1 2c-1 1-1 4-1 5s1 1 1 1l1 3 3 8 1 1v1c1 2 1 4 1 6l-1 1c0-2-2-4-3-6-1-4-4-9-7-12-1-1-1 0-1-2 1 0 1 0 2 1h0c0-1-1-2-2-2v-3c1 1 2 1 3 2z" class="N"></path><path d="M514 352c1 2 1 5 1 8 0 4 0 7 1 11v1c0 1 1 2 1 3 1 2 1 3 1 5h0c-2-2-2-5-3-7l-1 1 1 1v4c1 1 1 1 1 2s0 1 1 2v6h-1c0 2 0 1 1 3v1l2 2c0 1 1 2 1 3 1 1 2 1 3 2h1 1c-1 2-5 4-5 6h-1c-1 1-1 4-1 5v3c1 0 2 1 2 2h0c-1-1-1-1-2-1 0 2 0 1 1 2 1 4 2 5 4 8 1 1 1 2 1 3l1 1c-2-1-3-1-4-2l-1-1h-1v-1-1l-1-1c0-1-1-1-1-2v-1c-2-5 0-11-2-16 0-1 0-1 1-2 0 1 0 2 1 3 0-3-1-5 0-8h0c0-1-1-2-1-2v-1l-1 2h-1v-1h-1c-1 3 0 5 1 8s1 5 1 7l-1 1-1-9v-1c-1-1-1-1-1-3h0v-1-5-1h0l-1 1c-1 1-1 0-1 2s-3 5-4 7c-1 1-1 2-2 2l-1 1v1h0c-2-2-4-3-6-5l1-1 2 1h0c1-1 1-2 1-3-1 1-1 1-2 0h1c1-1 0-1 1-1 0-1 0-1-1-2l2 1v-1c0-1-2-1-2-2-2 0-1 0-2-1v-1c0-1 0-1 1-2s1-1 3-1l1-2c0-1 0-2-1-2l1-1s0 1 1 1c1-1 3-4 3-6l1-1v-1l1-2c0-1 0-3 1-4l2-3 1-1c1-3 0-9 1-13h0z" class="F"></path><path d="M514 352c1 2 1 5 1 8 0 4 0 7 1 11v1c0 1 1 2 1 3 1 2 1 3 1 5h0c-2-2-2-5-3-7l-1 1 1 1v4c1 1 1 1 1 2s0 1 1 2v6h-1v-5c0-1 0-2-1-3s-2-1-3-2l-1 1h0 1l2 2v1l-1-1-1 1v3s0 1 1 1l-3 3c-1 0-2 1-3 2v1c-2 2-3 6-5 8h-1c1-2 1-5 3-6v-3l-1-1h-1c0 1 0 1 1 1l-1 1v-1c-2 0-3-1-4-2 0-1 0-1 1-2s1-1 3-1l1-2c0-1 0-2-1-2l1-1s0 1 1 1c1-1 3-4 3-6l1-1v-1l1-2c0-1 0-3 1-4l2-3 1-1c1-3 0-9 1-13h0z" class="M"></path><path d="M513 402l1 9 1-1c0-2 0-4-1-7s-2-5-1-8h1v1h1l1-2v1s1 1 1 2h0c-1 3 0 5 0 8-1-1-1-2-1-3-1 1-1 1-1 2 2 5 0 11 2 16v1c0 1 1 1 1 2l1 1v1 1h1l1 1c1 1 2 1 4 2l-1-1c0-1 0-2-1-3-2-3-3-4-4-8 3 3 6 8 7 12 1 2 3 4 3 6l1-1c0 2 0 4 1 5l1 1v3l-1 1v-1c-1-1-1 0-1-1-1-1-2-1-3-1h0c0 1 0 2 1 3v2c-1 0-1 0-2-1l-2 1h0-1c-1-1-3-2-5-3h-1c-1-1-1-1-1-2-1 1-2 2-2 3-1 0-3 1-5 1l1-2c0-1 0-2-1-2v1h-2l-5 2c-1-2 1-2 1-4h-2l-1-1c2 0 3-1 4-2l1-1-1-1-4-2-3 4h-1c0-2 1-4 3-6l6-6c2-2 3-3 4-6l2-8v-1c-1-1-1-3 0-5 0 1 0 1 1 1l1-4z" class="J"></path><path d="M524 446v-1c-2-3-5-4-8-7l-3-3 1-2h1l1 1c1 1 0 0 1-1 2 0 3 1 4 1h2l3 3h0l-3-1h0l1 2v1c0 1 0 1 1 2v1l-1 1c0 1 1 1 2 2l-2 1z" class="G"></path><path d="M523 434c-2-1-3-2-5-3l-1-1c1-1 2-1 3-1 2-1 4 0 5 2 2 2 3 3 4 6v-2l1-1c0 2 0 4 1 5l1 1v3l-1 1v-1c-1-1-1 0-1-1-1-1-2-1-3-1h0c0 1 0 2 1 3v2c-1 0-1 0-2-1s-2-1-2-2l1-1v-1c-1-1-1-1-1-2v-1l-1-2h0l3 1h0l-3-3z" class="Z"></path><path d="M511 411c1 3 0 5 0 7 0 1 1 1 2 2v2c-1 1-2 3-2 3 1 3 2 3 2 6l-2 1v-1l-1 1-1 1-2-1v2c-1 1-2 1-2 2l-1-1-4-2-3 4h-1c0-2 1-4 3-6l6-6c2-2 3-3 4-6l2-8z" class="L"></path><path d="M500 433c0-1 0-1 1-2 0-1 3-4 4-4 1 1 2 1 2 3h-1l1 2v2c-1 1-2 1-2 2l-1-1-4-2z" class="B"></path><path d="M524 328c2 1 4 3 6 4l20 6c0-1 1-1 1-2 0 3-1 7-2 9-1 0-1 0-2 1v3c0 1 0 1-1 3v2c0 1 0 4-1 6v9h0v-2l-1 1v6h-1c-2 0-5-2-8-3l-6-2c-1-1-1-3-2-4h-1c-1-3-1-6-1-9h0l-1-7v-4-5-12z" class="C"></path><path d="M544 354c-1 3-1 5-1 8l-7-3h2 2v-1l1-1v-1l-1-2h4z" class="J"></path><path d="M527 365c6 0 11 2 16 3v6c-2 0-5-2-8-3l-6-2c-1-1-1-3-2-4z" class="X"></path><path d="M524 349l5 1 2 1 4 1h1c1 1 2 1 3 1l1 1 1 2v1l-1 1v1h-2-2c-3-1-7-3-10-4l-1 1h0l-1-7z" class="L"></path><path d="M531 351l4 1h1c1 1 2 1 3 1l1 1 1 2v1l-1 1c-4-1-9-3-12-5v-1l3-1z" class="K"></path><path d="M524 328c2 1 4 3 6 4l20 6c-3 1-3 1-4 3l-2 13h-4l-1-1c-1 0-2 0-3-1h-1l-4-1-2-1-5-1v-4-5-12z" class="Q"></path><path d="M524 340h4c1-1 1-1 2 0h4v1h-1-5c-1 1-1 1-1 2l1 1s1 1 1 2l1 1v1c-1 1-1 0-1 1v1l-5-1v-4-5z" class="W"></path><path d="M524 345l2 1 2-1 1 1 1 1v1c-1 1-1 0-1 1v1l-5-1v-4z" class="D"></path><path d="M538 342h5c2 1 2 0 3-1l-2 13h-4l-1-1c-1 0-2 0-3-1h-1v-1c-1 0-2-1-3-2h0 1c1 0 2 1 3 1l2 2v-3c-1-1-1-1 0-1v-3c-1-1 0-2 0-3z" class="U"></path><g class="P"><path d="M524 328c2 1 4 3 6 4l20 6c-3 1-3 1-4 3-1 1-1 2-3 1h-5c-2 0-3-1-4-1v-1h-4c-1-1-1-1-2 0h-4v-12z"></path><path d="M534 292l2 1c2 1 4 2 5 4s2 3 2 5h3l-2 2 1 1 2-2c2-1 7-1 9 0 3 1 3 2 5 4-1 3-1 6-3 9l-1 3c0 1-1 2-1 3l-4 11c-1 1-1 2-1 3s-1 1-1 2l-20-6c-2-1-4-3-6-4v-3l1-2c-1-1-1-2-1-3v-1c0-4 0-8 1-12 1-7 2-11 9-15z"></path></g><path d="M533 303h2 0v2h0c-1 1-1 3 0 4v2c0 1-1 1-1 2-2 0-2-1-3-2s-1-1-1-2h1l1 1 1-1v-2h-2v-1c1 0 1 0 2-1l-1-1v1l-1-1 2-1z" class="E"></path><path d="M536 293c2 1 4 2 5 4s2 3 2 5c-1 2-1 2-3 3l-1-1v-1c-1-1-2-1-2-2s0-1 1-2l-2-1c-1-1-1-2-2-3l2-2z" class="C"></path><path d="M541 297c1 2 2 3 2 5-1 2-1 2-3 3l-1-1v-1h1c1-2-1-2-1-4 0-1 1-1 2-2z" class="T"></path><path d="M543 302h3l-2 2c-1 1-3 1-3 3v1c-2 2-3 1-3 2 1 1 1 1 2 0v1l-2 2c-1 1-1 2-2 2v2h2l-1 2c1 1 2 1 4 1h6 0c2-1 3-2 5-3v1c-1 2-4 3-6 3h-1c-2 1-6 1-8 0-1 0-2 0-3 1h1v1l-1 1-1-2c-1 0-1 0-2-1v-1h-1c-1-1-2-3-2-5h0v-1c-1-3-1-4 1-6l1 1c0 1-1 2 0 3 1 2 2 4 4 5 3-2 2-6 3-9l2-3v-1l1 1c2-1 2-1 3-3z" class="G"></path><path d="M534 292l2 1-2 2c-1 2-2 3-3 6-1 1-2 3-2 5v2c-2 2-2 3-1 6v1h0c0 2 1 4 2 5h1v1c1 1 1 1 2 1l1 2c-1 0-2 1-3 2-1-1-2-2-2-3l-1-1v-1h-1c0 1 0 3 1 4v1c1 2 2 3 2 6-2-1-4-3-6-4v-3l1-2c-1-1-1-2-1-3v-1c0-4 0-8 1-12 1-7 2-11 9-15z" class="Z"></path><path d="M545 305l2-2c2-1 7-1 9 0 3 1 3 2 5 4-1 3-1 6-3 9v-3c-2 0-4 4-6 5v-1c-2 1-3 2-5 3h0-6c-2 0-3 0-4-1l1-2h-2v-2c1 0 1-1 2-2l2-2v-1c-1 1-1 1-2 0 0-1 1 0 3-2v-1c0-2 2-2 3-3l1 1z" class="L"></path><path d="M545 305l2-2c2-1 7-1 9 0l2 2-2 2v1c-1 1-1 1-2 1l-1-1c-1 0-2 1-3 0h-4c-1-1-1-2-2-2v-1h1z" class="J"></path><path d="M538 317c2-2 3-5 6-6 3-2 5-1 8-1 1 0 3 2 4 3-2 1-3 3-4 4-2 1-3 2-5 3h0-6c-2 0-3 0-4-1l1-2z" class="Z"></path><path d="M552 318c2-1 4-5 6-5v3l-1 3c0 1-1 2-1 3l-4 11c-1 1-1 2-1 3s-1 1-1 2l-20-6c0-3-1-4-2-6v-1c-1-1-1-3-1-4h1v1l1 1c0 1 1 2 2 3 1-1 2-2 3-2l1-1v-1h-1c1-1 2-1 3-1 2 1 6 1 8 0h1c2 0 5-1 6-3z" class="I"></path><path d="M163 172c2 0 4 1 6 2l11 3c13 2 26 3 39 3l58 1h114 48c-16 7-33 18-41 34-7 13-7 28-5 42 4 20 10 40 16 60l51 144 66 205c4-23 11-45 17-68l27-95 20-71 21-73 19-54c5-16 12-32 16-49 2-9 4-20 1-29-5-19-21-31-37-39-4-3-9-4-14-7 3-1 6 0 9 0h16 60 66c20 0 39 0 59-1 10-1 20-2 30-4 5-1 9-3 13-3-1 1-6 3-9 3-14 4-29 5-44 8-19 5-36 12-52 23-17 12-31 28-43 46-19 30-33 64-47 97l-31 75-34 101-48 161-21 81-15 64c-4-18-10-36-15-54l-34-111-49-150-48-142-25-66c-8-21-16-41-28-60-17-27-40-48-70-59-10-3-22-6-33-8-13-3-28-4-40-10z" class="H"></path></svg>