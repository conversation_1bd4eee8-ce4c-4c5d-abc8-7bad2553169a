<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="68 8 564 664"><!--oldViewBox="0 0 684 752"--><style>.B{fill:#363536}.C{fill:#c5c3c4}.D{fill:#969595}.E{fill:#adacad}.F{fill:#525151}.G{fill:#1b1a1b}.H{fill:#838283}.I{fill:#646363}.J{fill:#b5b4b4}.K{fill:#a6a4a5}.L{fill:#9e9d9d}.M{fill:#2e2e2e}.N{fill:#6c6b6c}.O{fill:#3f3e3f}.P{fill:#8e8d8d}.Q{fill:#212121}.R{fill:#767575}.S{fill:#4e4d4e}.T{fill:#bcbbbb}.U{fill:#545354}.V{fill:#2b2a2b}.W{fill:#888787}.X{fill:#434243}.Y{fill:#d5d4d4}.Z{fill:#5a5959}.a{fill:#cccbcb}.b{fill:#5e5e5e}.c{fill:#7c7b7c}.d{fill:#151415}.e{fill:#3b3b3b}.f{fill:#edecec}.g{fill:#272727}.h{fill:#070707}.i{fill:#dddbdc}.j{fill:#0f0f0f}.k{fill:#717070}.l{fill:#d1d0d1}.m{fill:#e2e1e1}</style><path d="M544 149h1 3 1l-6 2 1-2z" class="X"></path><path d="M447 160v-1l1 1v3c1 1 2 0 3 0l-7 1h1c1-2 2-2 2-4z" class="J"></path><path d="M166 84v-2l1-1c1 1 1 3 2 4l1 1c-1 1-1 1-2 1s-1-2-2-3z" class="G"></path><path d="M109 375c2 0 2 0 3 1v1c-2 1-3 1-5 2v-2c0-1 2-1 2-2z" class="d"></path><path d="M526 288h-1l-1-1c-1-1-3 1-4-1h0c2 1 6-1 8 1 1 0 1 1 2 1h-4z" class="i"></path><path d="M378 227h1c0 1 1 1 1 2h0c0 1 0 2 1 3v3l-1-1h0v5h-1c0-4 0-8-1-12z" class="S"></path><path d="M583 178h1c1 1 3 2 3 4v1l-6-5h2z" class="G"></path><path d="M299 41c1-4 3-7 5-11 0 3 0 6-1 8v-1h-1 0c-1 2-1 2-2 3l-1 1z" class="O"></path><path d="M379 239h1v13 1h0 1 1c-1 1-1 3-1 4l-1 2h0c-1-7-1-14-1-20z" class="m"></path><path d="M537 150l2 2-10 2 2-2c2-1 4-1 6-2z" class="F"></path><path d="M551 364l1-1v2c-1 3-2 5-4 7l-1-1c2-2 3-4 4-7z" class="d"></path><path d="M380 239v-5h0l1 1v11c0 2 0 4-1 6h0v-13z" class="X"></path><path d="M563 131l9 11v3c-1-1-1-1-1-2l-1-1c0-1-2-3-3-4h-1 0 1v-1c-1-1-4-4-4-6z" class="M"></path><path d="M239 229h0 14c-2 1-5 0-8 0h-1-1v1c1 1 2 0 4 0 0 1 0 1-1 1-1 1-2 1-3 2-1 0-1 0-2-1v-1c0-1-1-2-2-2z" class="i"></path><path d="M583 152c4 2 7 3 11 3l-3 1c-1 1-1 1-3 1-1 0-2-1-3-1h0v-1c-1-1-2-1-3-2l1-1z" class="X"></path><path d="M175 81l5 3 3 1-1 2h-1l-1-1-3-1-2-2 1-1c-1 0-1-1-1-1z" class="G"></path><path d="M180 86c0-1-1-1-2-2h0 2l3 1-1 2h-1l-1-1z" class="d"></path><path d="M572 142c4 4 7 7 11 10l-1 1c-3-3-7-5-10-8v-3z" class="V"></path><path d="M429 47c3-4 6-9 10-11h0v1l-1 1c-2 1-4 6-6 8-1 0-2 0-3 1z" class="e"></path><path d="M165 131c0 2-1 2-1 3s0 1-1 1l-4 5-2 2-2-1 10-10z" class="h"></path><path d="M472 83h11v1h3l-6 1-10-1h4c-1-1-2-1-2-1z" class="g"></path><path d="M183 85c4 1 7 2 11 3l-1 1h-3-4l-4-2 1-2z" class="h"></path><path d="M199 161l1-1h2c2-1 2-1 4 0 0 0 1 1 2 1v-1c0 1 0 1 1 1v-1l1 3-12-2h1z" class="H"></path><path d="M452 160l1 1c1 0 1 0 2 1 0-1 0-2 1-2v1l1 1 1-1c1 0 1 1 2 1-3 1-6 1-9 1-1 0-2 1-3 0v-3 1h1 1 0l1 1h0l1-2z" class="P"></path><path d="M560 134l1 1 2 2h4v1h-1 0 1c1 1 3 3 3 4l1 1c-4-1-5-3-8-5l-3-4zm20 158v2c0 1 1 1 1 2-1 1-1 3-1 4 0 3-1 5-2 7 0 1 0 2-1 2l-1-1c1-2 2-4 2-6v-2c1-3 1-5 2-8z" class="B"></path><path d="M95 176c2-3 4-4 7-4 1 0 2 1 3 1v1c-3 0-4 0-6 2l-1 1-1-1-3 4v-1l1-1v-2h0z" class="G"></path><path d="M428 161h1c1 0 2 1 4 2v-1h0v-1c1 0 1 0 2 1h2l1 1 1 1h4c-2 1-4 1-6 0h-3l-1 1c-1 1-3 0-5 0h4l-1-1-3-3z" class="C"></path><path d="M335 34c3-3 5-5 9-7 1 0 3-1 4-2-1 2-3 2-4 3-2 2-4 4-5 6-2 1-3 1-4 2v-2z" class="B"></path><path d="M464 84c-2-1-5-1-6-2h2-3l-1-1h3l13 2s1 0 2 1h-4-6z" class="h"></path><path d="M299 41l1-1c1-1 1-1 2-3h0 1v1c1 3 2 5 3 7 0 0 0 1-1 1h0l-2-1c0-1 0-1-1-1 0-1 0-2-1-2-1 1-1 1-2 1v-2z" class="M"></path><path d="M275 264c1 3 1 7 1 11v19c-1 0-1 0-1-1s0-2-1-3h0c-1-2-1-5 0-7v-1h1c1-2 0-4 0-5v-13z" class="b"></path><path d="M210 87c5-1 11-2 16-5 0 1 1 1 1 2-5 2-10 4-15 4-1 0-1 0-2-1z" class="d"></path><path d="M381 307v6 2c-1 1-1 2-1 3v2c0 1 0 1 1 2v2 1h1c-1 1-1 3-2 3 0 3 0 7-1 10v-28c1-1 1-2 2-3z" class="P"></path><path d="M380 318v2c0 1 0 1 1 2v2 1h1c-1 1-1 3-2 3 0-3-1-7 0-10z" class="X"></path><path d="M351 35s5-2 6-3l-2 2v1l6-2c-1 1-2 2-4 3s-4 2-6 4l-3 2c0-1 1-2 2-3h0c1-1 2-2 2-3l-1-1z" class="O"></path><path d="M198 88l12-1c1 1 1 1 2 1h0l-8 2h-9c-2 0-3-1-5-1h3l1-1h4z" class="G"></path><path d="M194 88h4 0c1 1 2 1 3 1-2 1-4 0-6 1-2 0-3-1-5-1h3l1-1z" class="d"></path><path d="M593 184c4 4 8 8 13 9h2 0c-4 1-8 1-11-1-2-2-5-3-6-5 1 0 2 1 3 2 1 0 1 0 2 1l1-1-1-1c-1-2-2-2-2-4h-1z" class="M"></path><path d="M236 77h0 5v1c-1 0-2 1-3 1-1 1-1 1-2 1h0c-3 2-6 3-9 4 0-1-1-1-1-2l10-5z" class="Q"></path><path d="M380 259h0v2h1v2h1s0-1 1-2l1 1c-1 2-3 4-3 7h0v2h0v2 2c-1 3-1 6-2 9 0-8 0-17 1-25z" class="C"></path><path d="M158 458h0-1l-1-1c-4 0-5-3-8-6l3 2c1 1 3 1 5 0 3 0 6-3 7-6h2c-2 3-4 5-7 7h-1c1 1 2 0 2 1 0 2 0 2-1 3z" class="G"></path><path d="M514 377l2-2c2-1 4-2 6-2v1h-1c-1 1-1 1-1 3h6c1 1 3 1 4 1h2c1 1 2 1 3 1h-3c-5 1-13 0-18-2z" class="S"></path><path d="M530 378h2c1 1 2 1 3 1h-3-2c-1 0-2 0-3-1h3z" class="e"></path><path d="M115 373c1 0 3 2 4 3l1-1c1 2 2 3 2 6l-3 3h-2c1-1 2-2 2-4 0-1-1-1-2-2s-4-1-5-1v-1c-1-1-1-1-3-1h1 1c1-1 2-1 4-2z" class="M"></path><path d="M115 373c1 0 3 2 4 3v1c0 1 0 0-1 1 0-1-1-1-1-2h-1c-2-2-3-1-5-1 1-1 2-1 4-2z" class="e"></path><path d="M164 452c1 0 2 1 3 1s4-3 6-3h1c-4 4-10 8-15 8h-1c1-1 1-1 1-3 0-1-1 0-2-1h1c2 0 4-1 6-2z" class="B"></path><defs><linearGradient id="A" x1="222.244" y1="88.235" x2="219.945" y2="84.733" xlink:href="#B"><stop offset="0" stop-color="#7b7878"></stop><stop offset="1" stop-color="#8f9193"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M236 80l1 1c-4 3-8 3-11 6h1c-2 1-3 1-5 1l-6 2h-1c-2 0-2 0-3-2h0c5 0 10-2 15-4 3-1 6-2 9-4z"></path><path d="M212 420c1 0 1 0 1 1 1 0 1 1 1 1l-2 2 1 1c1 0 2 0 2-1l1 1-3 3v-1c-1 0-4 2-5 3h0l-1-1v-1-2h-5c2-1 5-2 6-3 2-1 3-2 4-3z" class="K"></path><path d="M212 424l1 1c1 0 2 0 2-1l1 1-3 3v-1c-1 0-4 2-5 3 1-2 3-4 4-6z" class="g"></path><path d="M299 43c1 0 1 0 2-1 1 0 1 1 1 2 1 0 1 0 1 1v1c-1 2-2 4-4 6h-3c-1 1-2 1-3 1v-1h0l3-1v-3h1l-3-1c2-1 4-2 5-4z" class="X"></path><path d="M299 43c1 0 1 0 2-1 1 0 1 1 1 2s-1 2-2 4h-3l-3-1c2-1 4-2 5-4z" class="G"></path><path d="M280 169c1 0 3 2 3 1 3 2 4 3 6 5 1 1 3 2 4 2-2 1-4 1-6 1h-43c4-1 8 0 12 0h30c-1-2-2-4-4-5h0 1c-1-1-1-2-1-2l-2-2z" class="f"></path><path d="M614 300h2v1c2 1 1 3 4 3h0c1 0 3 3 3 3 2 4 4 7 8 9-8-3-13-8-18-15 1 0 1 0 1-1z" class="G"></path><path d="M135 321l13 1h1c1 0 1 0 2 1h0c0 1 0 3-1 4h0-2c-1-1-1-1-2-1h0-2c-1-1-3-1-4-2-1 0-2-1-4-1h-1v-2z" class="Y"></path><path d="M159 140l-1 3c0 1-2 2-2 3h-1l-3 5-1 1c-1 1-2 1-4 1v-1l2-3h0l-3 2c3-4 6-8 9-10l2 1 2-2z" class="B"></path><path d="M159 140l-1 3c0 1-2 2-2 3h-1l-1-1h0l3-3 2-2z" class="U"></path><path d="M369 32c5-2 11-4 16-4-6 3-15 7-18 13v1h-1-1v-1h0c0-1 2-3 3-4v-1l3-3c-2 1-4 2-6 2h-1l5-3z" class="X"></path><path d="M604 272l1-1c4 11 9 22 15 33h0c-3 0-2-2-4-3v-1c-5-7-7-16-10-24 0-2-1-3-2-4z"></path><path d="M116 232c1 3 0 6 1 9 0 6 1 12 0 18v1l-3-5v-12c1-1 1-3 1-4v-3l1-2v-2z" class="m"></path><path d="M241 74l1-1h1v2c-1 1-1 2-1 3v1h0v1h1l-2 2c-1 1-3 2-5 2-1 1-3 2-4 3l-1-1h-1l-3 1h-1c3-3 7-3 11-6l-1-1h0c1 0 1 0 2-1 1 0 2-1 3-1v-1h-5 0l5-3z" class="B"></path><path d="M242 80h1l-2 2c-1 1-3 2-5 2-1 1-3 2-4 3l-1-1h-1c4-2 8-3 12-6zm364 196c3 8 5 17 10 24h-2c0-2-2-4-3-5-2-4-6-9-7-13 1-1 0-2 0-4h0c1-1 1-2 2-2z" class="H"></path><path d="M361 33h1l7-1-5 3c-2 2-4 3-6 5l-3 1-5 4c0-1 1-2 2-2h-1-1c0-1 1-2 1-3 2-2 4-3 6-4s3-2 4-3z" class="i"></path><path d="M351 43c2-2 3-3 5-4h-1l1 1 1-1h0l-2 2-5 4c0-1 1-2 2-2h-1z" class="f"></path><path d="M190 362c1 0 1 0 1 1h1c0 2-1 4-2 5-3 4-6 9-10 12-3 2-5 3-8 5-2 1-4 3-6 3h-1c7-4 14-9 19-15 2-3 4-5 5-8 1-1 1-2 1-3z" class="G"></path><path d="M94 180l3-4 1 1c-1 3-2 5 0 8 1 3 3 5 6 5 2 1 5 0 7-1 4-2 7-6 10-10-1 3-2 5-4 7-3 2-5 4-8 6-3 1-6 0-9-1l-3-3-2-2c-1-3-1-4-1-6z" class="h"></path><path d="M545 342c4 2 7 5 8 10 1 4 1 9-1 13v-2l-1 1h-1c0-1 1-2 1-3h-1l-1-1c1-3 1-5 1-8-1-4-3-6-6-8 1-1 1-1 1-2z" class="Q"></path><path d="M550 364h1c-1 3-2 5-4 7l1 1c-4 4-8 6-13 7-1 0-2 0-3-1h-2c-1 0-3 0-4-1 5 0 9-1 13-3 5-2 8-5 11-10z" class="i"></path><path d="M532 378c6-1 10-3 15-7l1 1c-4 4-8 6-13 7-1 0-2 0-3-1z" class="B"></path><path d="M107 394l-2-2c-2-3-3-6-2-9 0-3 1-5 4-6v2c-2 3-2 4-2 7 1 4 2 6 5 8 2 2 5 3 7 4l-1 1h-1 0c-1 1-1 1-2 1l1 1h-1l-1-1h0c-1-1-2-2-3-2-1-1-2-2-2-3v-1z"></path><path d="M107 394c3 2 5 4 9 5h-1 0c-1 1-1 1-2 1l1 1h-1l-1-1h0c-1-1-2-2-3-2-1-1-2-2-2-3v-1z" class="a"></path><path d="M246 39c5 7 8 13 16 16 1 1 3 2 5 2h1 0 1c-1 1-2 1-3 1v1c-1 0-6-2-7-3h-2-1l-9-9v-2-1-1c-1-1-1-2-1-4z" class="c"></path><path d="M137 313l1 1c3 0 5 0 7 1 1 1 3 1 4 2h0v1h0-2c-1 1-1 1-2 1v-1h-2c-1 0-1 0-1-1-1 0-1 1-2 0h-1c1 0 2 0 3 1h0 1c1 0 1 0 1 1 2 0 2 0 3 1h0 1c2 0 4-1 5 0h1 0c-1 0-2 1-2 0-1 0-1 0-2 1h1v1h-2l-1-1c-1 0 0 1-1 0h-1 0-2v-1c-1 0-1 1-2 0h-1c-1 0-1 0-2-1h-1-1-1v1c1 0 2 0 3 1 1 0 4-1 5 0h3v1h1l-13-1 2-8z" class="f"></path><path d="M248 155c1 0 1 1 2 1 0 1 0 2-1 3l-1 1c0 1 0 1 1 1 0 1 0 1 1 1v1l2 2-25-1h2c2 0 4-1 6-1h1l2-2h0c1 0 1-1 2-1l1 1c1-1 1-3 2-5h0v3c1 1 1 2 2 2h1c-1-2 0-3 0-5l2-1z" class="l"></path><path d="M117 186h1c1 0 1-1 2-1 0 1-3 3-4 4v1h0l-3 3-1 1h-1l1 1v-1h1l2-1 1-1h1l3-3h0c0 1 0 2-1 2-1 1-1 2-2 3-1 0-2 1-2 1h-1c-1 1-2 1-2 1h-1c-4 1-7 0-10-1l-3-5c-1-1-1-1-1-2l3 3c3 1 6 2 9 1 3-2 5-4 8-6z" class="C"></path><path d="M100 191c3 1 6 2 9 1h0v1h0 3c-2 1-4 2-6 2-3-1-5-3-6-4z" class="K"></path><path d="M117 186h1c1 0 1-1 2-1 0 1-3 3-4 4v1c-1 0-3 3-4 3h-3 0v-1h0c3-2 5-4 8-6z" class="E"></path><path d="M165 388h1c2 0 4-2 6-3l8-5-1 3-3 3c-1 2-2 2-3 4-1 0-4 2-5 2h0c-1 0-2 1-3 1-3 1-7 4-10 4l-1 1c1-2 2-3 4-4h0v-1c-4 2-8 3-13 4-1 1-3 1-5 1h1c9-2 16-6 24-10z" class="I"></path><path d="M165 392h0c4-2 7-5 11-6-1 2-2 2-3 4-1 0-4 2-5 2h0c-1 0-2 1-3 1v-1z" class="H"></path><path d="M158 393h0c2-1 6-4 8-3l-6 3v1c2-1 3-2 5-2v1c-3 1-7 4-10 4l-1 1c1-2 2-3 4-4h0v-1z" class="K"></path><defs><linearGradient id="C" x1="171.181" y1="63.038" x2="169.823" y2="81.52" xlink:href="#B"><stop offset="0" stop-color="#090706"></stop><stop offset="1" stop-color="#272729"></stop></linearGradient></defs><path fill="url(#C)" d="M166 84c-2-5-2-10-1-15s4-10 9-13l-4 4c-1 2-1 5-1 8 0 5 3 9 6 13 0 0 0 1 1 1l-1 1 2 2c-2-1-4-2-5-3s-3-1-4-2h-1v1l-1 1v2z"></path><defs><linearGradient id="D" x1="265.377" y1="371.421" x2="277.285" y2="368.091" xlink:href="#B"><stop offset="0" stop-color="#141514"></stop><stop offset="1" stop-color="#49484a"></stop></linearGradient></defs><path fill="url(#D)" d="M272 359v-8c1 1 1 3 2 4h1c0 6 2 13 1 20h0c-1 5-4 10-6 14v-2c1-1 0-1 0-1l-1-1v1c-1 1-1 1-1 2h-1c4-9 6-15 5-25v-4z"></path><path d="M249 154l6-3c1 0 3 1 4 2l1 1 1 1h3l-1 1h-1c0-1-1-1-1-1v1c0 1 0 1-1 2h0-1v2h-1-3 1l1 1c0 1 0 2 1 4h-6l-2-2v-1c-1 0-1 0-1-1-1 0-1 0-1-1l1-1c1-1 1-2 1-3-1 0-1-1-2-1l1-1h0z" class="L"></path><path d="M255 158c1 0 3-1 4 0h0v2h-1-3s0-1-1-1l1-1z" class="h"></path><path d="M249 159c1 0 1 0 2-1 1 0 2 0 3-1 0 1 1 1 1 1l-1 1c-1 0-3 2-4 3h0c-1 0-1 0-1-1-1 0-1 0-1-1l1-1z" class="E"></path><path d="M249 154l6-3c1 0 3 1 4 2l1 1 1 1-4-1h-1-1c-1 0-1 0-2 1-2 0-2 0-4-1h0z" class="U"></path><path d="M254 159c1 0 1 1 1 1h1l1 1c0 1 0 2 1 4h-6l-2-2v-1h0c1-1 3-3 4-3z" class="W"></path><path d="M254 159c1 0 1 1 1 1h1c-2 1-4 3-6 3v-1h0c1-1 3-3 4-3z" class="Q"></path><path d="M383 50c9-3 20-5 30-1-5 0-10 0-15 1-3 1-5 2-8 3 1 0 2 0 3 1-2 0-4-1-5 1h0 1l-3 1c-1 0-3 0-5 1-1-1-2-1-2-1l2-1-1-1c0-1 1-2 1-3l2-1z"></path><path d="M381 55c3-1 6-2 9-2 1 0 2 0 3 1-2 0-4-1-5 1h0 1l-3 1c-1 0-3 0-5 1-1-1-2-1-2-1l2-1z" class="B"></path><path d="M141 398h-1c2 0 4 0 5-1 5-1 9-2 13-4v1h0c-2 1-3 2-4 4-6 2-11 3-17 4-5 0-9 1-14 0-2 0-5-1-8-2v-1h0 1l1-1c1 0 3 1 4 1h6c5 0 9-1 14-1z" class="C"></path><defs><linearGradient id="E" x1="384.336" y1="233.5" x2="379.986" y2="251.25" xlink:href="#B"><stop offset="0" stop-color="#9c9a9a"></stop><stop offset="1" stop-color="#d3d2d4"></stop></linearGradient></defs><path fill="url(#E)" d="M408 226h3 0c-3 3-22 2-26 2h-4l1 1c3 3 3 7 3 12v2 3 1l-1 2c-1 1-2 3-2 5v-1h-1-1 0v-1h0c1-2 1-4 1-6v-11-3c-1-1-1-2-1-3h0c0-1-1-1-1-2h-1 0l30-1z"></path><path d="M385 243c-1-1-1-2-1-4-1-2-1-8-3-9v-1h1c3 3 3 7 3 12v2z" class="e"></path><path d="M213 234v-1h1v1c1 3 1 5 1 8h0c0 2-1 3 0 5v2c0 1 0 2 1 2v1c0 1 0 2 1 4l1 2v-1h0c0-2-1-4-1-6v-2l-1-1c0-1 0-2 1-3v-1-1h0v-2c1-2 2-6 4-7 1-1 2-2 3-2 2-2 5-1 7-1 1 0 3 0 4-1 1 0 2 0 3-1l-1 2h-3s0 1-1 1c-1 1-3 0-5 0-1 0-3 2-4 3s-2 1-3 1l-2 2c-1 6-2 12 0 17 0 1 0 2 1 3h0v1c1 2 2 3 3 5l-2 1c-2-2-4-5-5-7-1-3-2-5-3-8-1-2-1-5-1-7v-3l-1-1v-3l2-2z" class="a"></path><path d="M213 234h0l-1 6-1-1v-3l2-2z" class="O"></path><path d="M85 258h1 0 1c1-1 1-2 2-2h0v2 1h1v-1c1-1 0-1 1-2 0 0 0-1 1-1-1 3-2 6-2 8-1 0-1 0-2-1l1-1h-1c-1 1-2 3-3 4l-2 2 1 1c-1 2-9 6-10 7l-2 1h-1 1v-2c1-1 2-1 3-1l1-1c-2 0-3 1-5 1l-1 1-5 2c-5 1-9 2-13 2-3 0-5 0-7 1-2-1-2-1-3-2 6 1 11 0 16-1 11-2 21-9 27-18z" class="d"></path><path d="M70 274c5-3 9-6 13-10 2-2 3-5 6-6-2 4-5 7-8 11h0l2-2 1 1c-1 2-9 6-10 7l-2 1h-1 1v-2c1-1 2-1 3-1l1-1c-2 0-3 1-5 1l-1 1z" class="C"></path><path d="M264 155l2 1 2 5h1l1 1 1 1v-1c1-1 1-2 2-2l2 1 1 1c1 1 2 2 3 2l4 3h2c1 1 1 1 1 2-1 1-2 0-3 1 0 1-2-1-3-1-1-1-2-1-3-2-2-1-4-1-5-1-4 0-8 0-11-1h-3c-1-2-1-3-1-4l-1-1h-1 3 1v-2h1 0c1-1 1-1 1-2v-1s1 0 1 1h1l1-1z" class="l"></path><path d="M264 155l2 1 2 5c-1-1-3-2-4-3h-1l-1 2-2-2h0c1-1 1-1 1-2v-1s1 0 1 1h1l1-1z" class="K"></path><path d="M271 162c1-1 1-2 2-2l2 1 1 1c1 1 2 2 3 2l4 3-6-2c-2-1-6-1-7-3l1 1v-1z" class="P"></path><path d="M271 162c1-1 1-2 2-2l2 1 1 1c0 1-1 1-1 1-2 0-3 0-4-1z" class="L"></path><path d="M277 165l6 2h2c1 1 1 1 1 2-1 1-2 0-3 1 0 1-2-1-3-1-1-1-2-1-3-2-2-1-4-1-5-1 1-1 3 0 5-1z" class="m"></path><path d="M259 158h1l2 2 3 2c-1 1-3 1-4 2v1h-3c-1-2-1-3-1-4l-1-1h-1 3 1v-2z" class="j"></path><path d="M258 160s1 0 1 1v2c1 0 2 1 2 1v1h-3c-1-2-1-3-1-4l-1-1h-1 3z" class="U"></path><path d="M558 235c1 3 0 7 1 10v2h0l2 1c-1 1-1 4-1 5v4 7 1h0c0-1 0 0 1-1h0l1-2h1c0 1 0 2 1 2v4l-3 4h0c1 1 1 1 1 2 1-2 3-3 4-5l1 1-2 2 3-1-1 3c-1 0-1 0-2 1 0 1-4 7-5 7h-1l-2 3v-4l1-46z" class="J"></path><path d="M557 281v-1c2 0 2-2 4-2 1 0 1 0 0 1 0 1-1 2-2 3l-2 3v-4z" class="i"></path><path d="M566 269l1 1-2 2c-2 3-4 5-6 6 1-1 2-3 3-4 1-2 3-3 4-5z" class="P"></path><path d="M559 245v2h0l2 1c-1 1-1 4-1 5v4 7 1h0c0-1 0 0 1-1h0c0 1-1 2-2 3v-6-3-13z" class="K"></path><path d="M559 247h0l2 1c-1 1-1 4-1 5 0-1 0-1-1-2v-4z" class="D"></path><defs><linearGradient id="F" x1="192.541" y1="428.498" x2="200.151" y2="439.032" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#38373a"></stop></linearGradient></defs><path fill="url(#F)" d="M211 417h1l-3 3c0 1 0 2-1 3s-4 2-6 3c-7 3-13 7-16 15-2 3-2 6-2 10 0 2 1 3 0 5-1-2-1-4-2-6 0-1 0-5-1-6h-1-1l3-5h-1l-2-1c1-1 2-3 3-4l1 1c1 0 2-2 3-2h1s1-1 1-2c1-1 3-2 4-2 6-5 13-7 19-12z"></path><path d="M182 434l1 1c1 0 2-2 3-2h1c-2 2-3 4-5 6h-1l-2-1c1-1 2-3 3-4z" class="N"></path><path d="M496 277h-3c-1-1-3 0-4-1h0s1-2 2-2h4c2-1 5-1 7-1 2-1 4-1 6-1l7-2h0l-2 2v-1l-1 1h-1-1-2v1c-1 0-2-1-3 0h-3-1l-1 1h0-3-1-1c-1 0-2 0-3 1h0c-1 0-2 1-2 1h1c1-1 2 0 4-1h2v1h-1c2 1 5 0 7 0l1-1h2 0s1 0 1-1h1 3 0c0-1 1-1 2-1h2v-1h2 2c1 0 2 1 3 2v2h1c0 1 0 0 1 1h-1c0 1-1 2-2 2-2 2-7 2-9 2-3-1-5-1-7-1h-1v-2h-2v-1h-6z" class="Y"></path><path d="M511 274c0-1 1-1 2-1h2v-1h2 2c1 0 2 1 3 2v2h1c0 1 0 0 1 1h-1-6c-2 0-7 0-8-1h0c2-1 3-1 5-2 2 0 3 0 4-1h0c0-1-2 0-3 0h-2c-1 1-1 1-2 1h0z" class="f"></path><path d="M517 277h6c0 1-1 2-2 2-2 2-7 2-9 2-3-1-5-1-7-1h-1v-2h-2v-1h15z" class="g"></path><path d="M432 46c2-2 4-7 6-8-2 5-4 9-7 13-2 3-5 6-8 9-1 1-3 2-4 3-2-1-4 1-6 1h-1c-1-1-2-1-3-1-3 1-4 0-7 0l11-4v-1c6-1 12-7 16-11 1-1 2-1 3-1z" class="T"></path><path d="M429 47c1-1 2-1 3-1-2 4-5 6-9 9-2 1-3 3-6 3l-4 1v-1c6-1 12-7 16-11z" class="X"></path><defs><linearGradient id="G" x1="87.483" y1="270.504" x2="64.251" y2="279.797" xlink:href="#B"><stop offset="0" stop-color="#979596"></stop><stop offset="1" stop-color="#c7c6c7"></stop></linearGradient></defs><path fill="url(#G)" d="M85 265c1-1 2-3 3-4h1l-1 1c1 1 1 1 2 1v1c0 1 0 2-1 3l1 1c-1 3-3 4-5 6l-3 2s1 0 2-1v1c-1 0-2 1-3 1l1 1c-5 2-10 3-16 3-1-1-1-1-2-1l-1-3 2-1 5-2 1-1c2 0 3-1 5-1l-1 1c-1 0-2 0-3 1v2h-1 1l2-1c1-1 9-5 10-7l-1-1 2-2z"></path><path d="M85 265c1-1 2-3 3-4h1l-1 1c1 1 1 1 2 1v1c0 1 0 2-1 3l-2 2h-1 0c-1-1-1-1 0-2v-1l-1-1z" class="D"></path><path d="M86 269v-2c1 0 2-1 3-2-1-1-1-2-1-3 1 1 1 1 2 1v1c0 1 0 2-1 3l-2 2h-1z" class="W"></path><path d="M271 259c-1-5-1-10-1-14 0-7 1-12 6-16h1c0 1-1 1-1 2 0 6-1 11-1 17v11c0 1 1 3 0 5v13c0 1 1 3 0 5h-1v1l-2-7h1 0v-2l-2-15z" class="h"></path><path d="M273 274l1 8v1l-2-7h1 0v-2z" class="D"></path><path d="M275 264v-4c-2-2-2-5-2-7 0-5 0-11 1-16 0-2 1-4 2-6 0 6-1 11-1 17v11c0 1 1 3 0 5z" class="S"></path><path d="M220 258h0c-1-1-1-2-1-3-2-5-1-11 0-17l2-2c1 0 2 0 3-1s3-3 4-3c2 0 4 1 5 0 1 0 1-1 1-1h3l-2 2-1 1-2 6-2 3h-1l-1 1-4 5-2 2v1l1 1-2 2c0 1 0 3-1 4v-1z" class="E"></path><path d="M220 258v-6-3-1h0 0l-1-1c1 0 1 0 1-1s1-2 2-3c0-2 0-3 1-4l1-1v-1c1-1 4-4 6-4h2c1 1 2 0 3 0h0l-1 1h-3c-4 1-6 4-8 7l1 1v2h-1v1 1c-1 1-1 1-1 2l1 1h1l-2 2v1l1 1-2 2c0 1 0 3-1 4v-1z" class="D"></path><path d="M224 249h-1l-1-1c0-1 0-1 1-2v-1-1h1v-2l-1-1c2-3 4-6 8-7h3l-2 6-2 3h-1l-1 1-4 5z" class="P"></path><path d="M228 243h-1-1c0-1 1-2 1-3 1-1 2-3 2-4h0 1c1 1 1 1 1 3h-1l-1 1 2-1 1 1-2 3h-1l-1 1h0v-1z" class="R"></path><path d="M229 240l2-1 1 1-2 3h-1l-1 1h0v-1c1-1 1-2 1-3z" class="L"></path><path d="M576 308l1 1c-5 13-13 24-27 30l-5 2v1c0 1 0 1-1 2-4-2-8-1-12 0h-1l2-2c2-2 9-4 12-4h0v1c2-1 2-4 3-5v-2h2l7-3c3-1 6-4 8-6 5-4 9-9 11-15z"></path><path d="M565 323c0 3-1 4-3 5-3 2-7 4-10 5l-2 1h-2v-2h2l7-3c3-1 6-4 8-6z" class="M"></path><path d="M450 226c2-1 6 0 8 1h3 0l-1 1h-2l-4 7c-2 3-3 6-6 9v-1h-1 0v-2l-1-1-4 5-5 5h0c-2 2-3 3-3 5-1 1-1 2-2 2h0-3c1-2 1-2 3-4 1-1 2-2 2-4 5-7 9-14 12-22 1 1 1 2 1 3h-1v1h0c-1 1-1 2-1 3h-1v1c0 1-1 2-2 3h0c-1 1-1 2-2 3v1c-1 2-2 2-2 4 1 0 4-5 5-6v-1-1l1-1v-1c1-1 2-5 4-6h0l2-4z" class="a"></path><path d="M450 226c1 0 2 0 2 1 1 4-3 8-4 11-1 0-2 2-2 2l-4 5-5 5h0c3-5 6-9 8-14 1-2 3-4 3-6l2-4z" class="S"></path><path d="M450 226c2-1 6 0 8 1h3 0l-1 1h-2l-4 7c-2 3-3 6-6 9v-1h-1 0v-2l-1-1s1-2 2-2c1-3 5-7 4-11 0-1-1-1-2-1z" class="E"></path><path d="M458 227h3 0l-1 1h-2l-4 7c-2 3-3 6-6 9v-1l6-9c0-2 0-5 1-6 1 0 2-1 3-1z" class="Y"></path><path d="M183 430c2-1 3-1 5-1l-6 5c-1 1-2 3-3 4l2 1h1l-3 5h1c-2 2-4 5-6 6h-1c-2 0-5 3-6 3s-2-1-3-1c-2 1-4 2-6 2 3-2 5-4 7-7 2-5 4-8 4-14v3 2h0c2-1 3-2 4-4 0 0 0-1 1-2h0l1-1h0c0 1 0 2-1 3v1l1 1c1-1 1-2 2-3h0c1-1 2-1 3-1v1h1c1-1 2-2 2-3z" class="U"></path><path d="M179 438l2 1h1l-3 5-5 4 3-5-1-1c0-1 2-3 3-3v-1z" class="c"></path><path d="M183 430c2-1 3-1 5-1l-6 5c-1 1-2 3-3 4v1c-1 0-3 2-3 3-1 3-3 5-6 7l5-7c1-2 2-3 3-5h0l-2 2-4 4c0-2 1-3 2-4l1-3c1-1 1-2 2-3h0c1-1 2-1 3-1v1h1c1-1 2-2 2-3z" class="W"></path><path d="M176 439l-1-1c1-2 1-2 3-3h0v2l-2 2z" class="P"></path><path d="M174 432l1-1h0c0 1 0 2-1 3v1l1 1-1 3c-1 1-2 2-2 4-2 4-5 6-8 9-2 1-4 2-6 2 3-2 5-4 7-7 2-5 4-8 4-14v3 2h0c2-1 3-2 4-4 0 0 0-1 1-2h0z" class="a"></path><path d="M174 432l1-1h0c0 1 0 2-1 3v1l1 1-1 3c-1-1-1-2-2-3l1-1c1-1 1-1 1-2v-1z" class="T"></path><path d="M198 242v2 1h0 0c2-2 3-4 5-7 2-2 6-4 10-4l-2 2v3l1 1v3 2c-1 1-2 2-2 4v1c-1 1-1 2 0 3 0 0-1 1-1 2v4 1l-2-1 1-4s0-1-1-1c0-1-2 0-2-1l-1 1-2 4h0v-2h-3 2v-1s-1 0-1 1c-2-1-2-3-3-4-1 0-1-2-1-2l1-4 1-4z" class="a"></path><path d="M197 246c0 1 0 2 1 3 0 1-1 1-1 2 1 1 2 2 2 3h0 0l2-6c0-1 1-3 1-4h1c-1 4-1 8-2 11 0 0-1 0-1 1-2-1-2-3-3-4-1 0-1-2-1-2l1-4z" class="T"></path><path d="M203 244h0c1 0 1 1 1 1 0 2 0 3 2 4 1 1 0 1 1 3-1 0-2 1-2 1l-1 1-2 4h0v-2h-3 2v-1c1-3 1-7 2-11z" class="b"></path><path d="M203 244c0-1 0-2 1-3 0-1 1-3 2-3l1 1s2-1 2-2l2-1v3l-1 3-2-2h-2c0 1 2 2 2 3 0 2-1 3-2 5v1c-2-1-2-2-2-4 0 0 0-1-1-1h0z" class="D"></path><path d="M208 243c0-1-2-2-2-3h2l2 2 1-3 1 1v3 2c-1 1-2 2-2 4v1c-1 1-1 2 0 3 0 0-1 1-1 2v4 1l-2-1 1-4s0-1-1-1c0-1-2 0-2-1 0 0 1-1 2-1-1-2 0-2-1-3v-1c1-2 2-3 2-5z" class="Q"></path><path d="M211 239l1 1v3 2c-1-1-2-1-2-3l1-3z" class="B"></path><path d="M208 243c1 1 1 2 1 3v1c-1 2-1 4-1 6v2s0-1-1-1c0-1-2 0-2-1 0 0 1-1 2-1-1-2 0-2-1-3v-1c1-2 2-3 2-5z" class="N"></path><path d="M367 42c2-1 4-1 6-2-3 2-5 3-7 5h0v1c4-1 9-2 14-2h13l-9 4h-1c-3 0-9 2-11 4h0-1c-3 1-6 2-8 5l-5 4v-1l-1 1c0-1-1-1-2-2 0-1 1-3 1-4l1-1c0-2 1-4 2-6l6-7h0v1h1 1z" class="h"></path><path d="M357 54c0 1 0 1 1 2 2-1 3-3 5-5 1-1 3-1 4-2 1 0 3-1 4-1h0c0 1-2 2-3 2l-2 1c-2 1-4 3-6 5-1 1-1 2-2 4l-1 1c0-1-1-1-2-2 0-1 1-3 1-4l1-1z" class="g"></path><path d="M579 276l2-1c-1 2-1 4 0 6l1 1v1c0 1-1 2-1 4s1 4 2 6c0 2 2 6 3 7l1 3h1c1-5 2-8 5-11 0 0 1-2 2-2l6-6v-1c1-1 1-2 1-3v-1l2 3c1 4 5 9 7 13 1 1 3 3 3 5 0 1 0 1-1 1l-10-14c-4 3-10 8-12 14s0 11 3 16c-6-5-10-14-13-21 0-1-1-1-1-2v-2c-1 3-1 5-2 8v-4-3-5h-1v-1-3c0-1 1-3 1-4 1-1 1-3 1-4z" class="h"></path><path d="M579 276l2-1c-1 2-1 4 0 6l1 1v1c0 1-1 2-1 4l-2-5c0 3 0 7 1 10-1 3-1 5-2 8v-4-3-5h-1v-1-3c0-1 1-3 1-4 1-1 1-3 1-4z" class="D"></path><path d="M104 274l1 1v-1-3h1c0 1 1 3 1 4s1 2 2 3v2l1 1h1c-1-1-1-3-2-4l1-1c1 2 1 3 2 4h1c1 1 2 2 3 4 0-1 0-2 1-3h1 0v4c0 1 0 2 1 2v2l-1 2c-1 1-1 2-2 3 0 0 0 1-1 1v1h1l3 3v1l-1-1c-1 0-1-1-3-1h0c1 1 3 3 4 3v3c1 0 1 0 1 1-2 0-4-2-5-3v1 1h1c1 1 3 2 4 3l1 1v4l2 5h-1l-1-1c-1 0-1-1-2-2-3-2-6-4-8-8v-1-3-2l1-2c0-1 1-2 1-2 0-2-2-5-3-7l-3-5c-3-4-4-8-5-13 1 1 1 2 2 3z" class="Z"></path><path d="M113 305c2 2 6 4 8 7l2 5h-1l-1-1c-1 0-1-1-2-2h1c-1-1-3-2-4-3-2-2-3-3-3-6z" class="C"></path><path d="M113 305c-1-1-1-2 0-3h2v1 1h1c1 1 3 2 4 3l1 1v4c-2-3-6-5-8-7z" class="f"></path><path d="M109 280l1 1h1c-1-1-1-3-2-4l1-1c1 2 1 3 2 4h1c1 1 2 2 3 4 0-1 0-2 1-3h1 0v4c0 1 0 2 1 2v2l-1 2-1-1-1 1h0l-2 2h-1l-1-2-2-5c-1-2-2-2-2-3l-1-1c-1-1-1-1-1-2l1-1c0 1 1 1 1 1h1z" class="k"></path><path d="M110 286h0 2s-1-1 0-2c1 1 1 1 1 2-1 2 0 2 0 3s0 2-1 2l-2-5z" class="L"></path><path d="M112 291c1 0 1-1 1-2s-1-1 0-3c1 2 2 4 3 5l-2 2h-1l-1-2z" class="J"></path><path d="M116 284c0-1 0-2 1-3h1 0v4c0 1 0 2 1 2v2c-1 0 0 0-1-1l-1 2h-1l-4-8c2 1 3 3 4 4 0 1 1 1 1 1h1l-2-3z" class="E"></path><defs><linearGradient id="H" x1="459.544" y1="82.007" x2="453.64" y2="65.469" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#585657"></stop></linearGradient></defs><path fill="url(#H)" d="M439 69c7 3 16 6 23 4 4-1 7-3 9-5 1-1 2-2 4-3-3 6-6 12-13 15l-2 1h-1-3l1 1h3-2c1 1 4 1 6 2h-8-2 0l-2 1v1c-1-1-2-1-3-1h-3c-1-1-1-1-2-1s-1 0-2-1c-1 0-3-1-4-1h0-2-2l-1-1 1-2c0-1 1-1 1-2h3 0c0-1-1-1-1-2 1-1 1 0 3-1h-1v-1c1-1 1-2 0-3h0v-1z"></path><path d="M442 73c1 0 2 1 3 1v1h0-2c-1-1-1-1-1-2z" class="S"></path><path d="M446 77c5 1 10 1 15 0-3 1-5 3-8 3h-2c-2 0-3 0-5-2v-1z" class="K"></path><path d="M440 74l6 3v1c2 2 3 2 5 2h2l-2 1-1 1c2 1 5 1 6 2h-2 0l-2 1v1c-1-1-2-1-3-1h-3c-1-1-1-1-2-1s-1 0-2-1c-1 0-3-1-4-1h0-2-2l-1-1 1-2c0-1 1-1 1-2h3 0c0-1-1-1-1-2 1-1 1 0 3-1z" class="P"></path><path d="M447 80c1 1 1 1 1 2 0 2 3 1 5 2h1 0l-2 1v1c-1-1-2-1-3-1h-3c-1-1-1-1-2-1s-1 0-2-1c2-1 2 0 4-1v-1l1-1z" class="K"></path><path d="M438 82c-1 0-1 0-1-1s0-1-1-1v-2h1 0c1 0 2 0 2-1h1c1 0 1 1 2 1s2 1 2 2h3l-1 1v1c-2 1-2 0-4 1-1 0-3-1-4-1z" class="L"></path><path d="M408 226c2-1 4-1 6-1v1c-2 2-5 4-8 4v4c-1 1-1 3-2 5-1 1-2 1-3 1-2 0-7 3-8 5-1 1 0 1 0 2h-1c-1 0-1 0-2 1h-1c-1 1-2 2-3 2s-1-1-2-1l1-2v-1-3-2c0-5 0-9-3-12l-1-1h4c4 0 23 1 26-2h0-3z" class="Y"></path><path d="M389 243h-1 0c-1-1-1-2 0-3 5-2 9-5 15-5h0v-2l3-3v4c-1 1-1 3-2 5h0-2c-4-1-8 1-11 3l-2 1z" class="C"></path><path d="M389 243l2-1c3-2 7-4 11-3h2 0c-1 1-2 1-3 1-2 0-7 3-8 5-1 1 0 1 0 2h-1c-1 0-1 0-2 1h-1c-1 1-2 2-3 2s-1-1-2-1l1-2 4-4z" class="S"></path><path d="M253 229l5-1h10c3 0 6-1 9 0v1h-1c-5 4-6 9-6 16 0 4 0 9 1 14h-1c-1-3-2-9-5-11-1 0-2-1-3-1h0l-1-1h-1 0l-2-1c-1-1-1-2-2-3 0 0 1-1 1-2-1-1-1-1-2-1 0-2 0-4-2-5h0c-2-2-4-2-7-3 1 0 1 0 1-1-2 0-3 1-4 0v-1h1 1c3 0 6 1 8 0z" class="C"></path><path d="M262 244l1-1 1 1v1l-1 1-1-2z" class="f"></path><path d="M246 231c1 0 1 0 1-1-2 0-3 1-4 0v-1h1 1l1 1h23c0 1-1 2-1 3h-1v2h0c0 1 0 1-1 2v2-3h-2c0-1 1-1 1-2v-1c-1-1-4 0-5 0h-1v-1h-1 0l-1 1h-1c-1 0-1 0-3 1h0c-2-2-4-2-7-3z" class="l"></path><path d="M257 233l1-1h0 1v1h1c1 0 4-1 5 0v1c0 1-1 1-1 2h2v3c-1 1-2 3-2 4v1l-1-1-1 1h-1l-1 1v1l-2-1c-1-1-1-2-2-3 0 0 1-1 1-2-1-1-1-1-2-1 0-2 0-4-2-5 2-1 2-1 3-1h1z" class="Y"></path><path d="M257 233h2s-1 1-1 2h0-1c-1 0-1-1-2-1l1-1h1zm3 0c1 0 4-1 5 0v1c0 1-1 1-1 2s1 2 0 3c-1 0-1 0-2-1v-2h1c-1-2-2-2-3-3zm0 12h-1v-2h-1c-1-2 0-6 0-7l1-1 1 1c0 2 1 4 1 5v3h0l-1 1z" class="m"></path><path d="M383 48h1l-2 1 1 1-2 1c0 1-1 2-1 3l1 1-2 1s1 0 2 1c2-1 4-1 5-1h3 0c-2 1-3 2-5 2l-6 3c-1 0-2 1-3 2 0 1-1 1-2 2l-2 1c-2 1-4 3-6 5-1 1-3 2-3 3-1 2-2 3-2 4-1 0-2 1-2 2l-2 4-1-1v-3l1-1c-1-1-1-2-1-3h0 0l1-5c1-2 1-4 3-7 0-3 2-4 4-7s5-4 8-5h1 0c2-2 8-4 11-4z" class="J"></path><path d="M375 54l6-3c0 1-1 2-1 3l1 1-2 1-3 1c-1-1-1-2-1-3z" class="d"></path><path d="M366 56h2v1c-1 0-1 1-1 2l-2 3h-1 0l-2 2h-1l1-2h-1c0-2 4-4 5-6z" class="C"></path><path d="M368 61c2-3 5-5 7-7 0 1 0 2 1 3l3-1s1 0 2 1l-3 1c-2-1-2-1-4 0-1 1-2 2-3 2s-1 0-2 1h-1z" class="Z"></path><path d="M372 52h0c2-2 8-4 11-4-4 3-9 5-13 8l-1 1v-1c1-1 3-2 3-3v-1z" class="H"></path><path d="M363 57c2-3 5-4 8-5l-5 4c-1 2-5 4-5 6l-2 2c0-3 2-4 4-7z" class="B"></path><path d="M361 62h1l-1 2h1l2-2h0 1c-1 2-2 3-3 4-2 2-3 6-5 8h-1v-3c1-2 1-4 3-7l2-2z" class="Y"></path><path d="M362 66h1c2-2 3-4 5-6 0 1-1 1-1 1l-1 1v1c-1 1-1 1-1 3l-1 2c-2 1-3 5-4 8-1 1-2 3-2 4l-2 4-1-1v-3l1-1c-1-1-1-2-1-3h0 0l1-5v3h1c2-2 3-6 5-8z" class="C"></path><defs><linearGradient id="I" x1="374.534" y1="66.534" x2="372.179" y2="60.946" xlink:href="#B"><stop offset="0" stop-color="#a4a2a4"></stop><stop offset="1" stop-color="#c2c0c0"></stop></linearGradient></defs><path fill="url(#I)" d="M386 56h3 0c-2 1-3 2-5 2l-6 3c-1 0-2 1-3 2 0 1-1 1-2 2l-2 1c-2 1-4 3-6 5-1 1-3 2-3 3-1 2-2 3-2 4-1 0-2 1-2 2 0-1 1-3 2-4 1-3 2-7 4-8l1-2c1-2 2-4 3-5h1c1-1 1-1 2-1s2-1 3-2c2-1 2-1 4 0l3-1c2-1 4-1 5-1z"></path><path d="M368 61h1c1-1 1-1 2-1s2-1 3-2c2-1 2-1 4 0-2 1-4 3-6 4s-6 5-8 6h0l1-2c1-2 2-4 3-5z" class="P"></path><path d="M483 83l3-1c6-1 13-3 17-9 2-4 3-7 2-11-1-3-4-8-7-9-3-2-7-3-9-2s-5 3-6 5c0 1 0 2 1 3h-1-1c-1-2-1-4 0-6 1-3 3-5 5-6 5-3 11-2 16 0 4 1 9 5 12 10h-1c0-1-1-1-1-2-1-1-3-2-4-4-1-1-3-2-4-2-2-1-3-2-5-2h1c3 1 9 6 11 8v2c2 4 3 7 2 12l-1-1-1 1c-1-2 0-2-1-4-2 6-4 10-10 14-4 2-10 5-15 5h-3v-1z"></path><defs><linearGradient id="J" x1="509.166" y1="53.525" x2="496.534" y2="59.564" xlink:href="#B"><stop offset="0" stop-color="#b7b7b7"></stop><stop offset="1" stop-color="#e6e3e8"></stop></linearGradient></defs><path fill="url(#J)" d="M511 65c-1 0-1-4-2-5-1-2-3-4-4-5-5-3-9-7-15-6-1 0-3 0-5 1 4-3 7-3 12-3 6 1 11 6 15 10 2 4 3 7 2 12l-1-1-1 1c-1-2 0-2-1-4z"></path><path d="M219 141c1-1 1-2 2-3 1 1 2 2 2 3l2 1v1h-1l-1 1h1v2l1 1c0 2 3 5 4 6s1 2 2 3h1c0 1 1 1 1 2h2l2-2h0c1 1 3 0 4 0 1-1 1-1 2-1h0 1l-1 1h0c-1 2-1 4-2 5l-1-1c-1 0-1 1-2 1h0l-2 2h-1c-2 0-4 1-6 1h-2l-13-1h-4l-1-3 1-1c1-1 1-3 1-4 1-4 2-8 3-11 0-2 1-4 2-5 1 1 1 2 2 3l1-1z" class="Q"></path><path d="M227 159h2v1h-3 1v-1z" class="V"></path><path d="M217 152v1l3 6c-1-1-3-2-4-3 0-2 0-3 1-4z" class="S"></path><path d="M215 151c0-1 1-2 2-3v4c-1 1-1 2-1 4h-1v-5z" class="X"></path><path d="M219 141c1-1 1-2 2-3 1 1 2 2 2 3-1 0-1 1-2 2v2l-1-1v-3h-1 0z" class="Z"></path><path d="M221 153h1l3 3h0c1 2 1 2 2 3v1h-1c-1 0-2-1-3-2 0-1-2-3-2-5z" class="B"></path><path d="M220 144l1 1c1 1 0 3 1 4 1 2 2 5 3 7h0l-3-3h-1c-1-2-1-5-1-7l-1-1 1-1z" class="I"></path><path d="M219 145l1 1c0 2 0 5 1 7 0 2 2 4 2 5-1 0-3-1-4-3-1-3-1-7 0-10z" class="k"></path><path d="M216 139c1 1 1 2 2 3l1-1h0c-1 2-2 3-2 5v1 1c-1 1-2 2-2 3h0-1c0-2 0-4 1-6h0l-1-1c0-2 1-4 2-5z" class="b"></path><defs><linearGradient id="K" x1="209.363" y1="153.475" x2="215.149" y2="157.518" xlink:href="#B"><stop offset="0" stop-color="#4e4e4e"></stop><stop offset="1" stop-color="#7c7a7c"></stop></linearGradient></defs><path fill="url(#K)" d="M214 144l1 1h0c-1 2-1 4-1 6h1 0v5c0 2 0 2 2 3 1 2 3 2 5 3 1 0 2 0 3 1 1 0 3 0 4 1h-2l-13-1h-4l-1-3 1-1c1-1 1-3 1-4 1-4 2-8 3-11z"></path><path d="M214 163h0c0-1 0-2-1-3v-1l6 3c1 1 2 0 3 0s2 0 3 1c1 0 3 0 4 1h-2l-13-1zm361 123h1l1-2v3 1h1v5 3 4 2c0 2-1 4-2 6-2 6-6 11-11 15-2 2-5 5-8 6l-7 3h-2c0-1 1-3 1-4 1-3 1-5 2-8 0-1 0-1 1-1l2-2c-1 0-1 0-1-1s0 0-1-1c0 0 0-1 1-1l1-2s1-1 2-1c0 0 1 0 1-1v2c1 0 2-1 3-2 2-2 5-6 8-6 0-2 2-3 3-4v-1l3-7v-1s0-1 1-2v-3z" class="E"></path><path d="M552 322c6-4 10-8 15-13l-1 2c0 1-2 3-2 4-3 2-6 6-9 8h-3v-1z" class="D"></path><path d="M575 286h1l1-2v3 1h1v5c0 2-1 4-3 6 0 2-2 3-2 5h-1l-1-1h0l-3 3h-1 0c0-1 1-1 1-2h0c0-2 2-3 3-4v-1l3-7v-1s0-1 1-2v-3z" class="J"></path><path d="M577 288h1v5c0 2-1 4-3 6h0-1c1-2 2-4 1-6 1-2 2-3 2-5z" class="C"></path><path d="M575 286h1l1-2v3 1c0 2-1 3-2 5l-4 7v-1l3-7v-1s0-1 1-2v-3z" class="W"></path><path d="M566 309l2-2h0v2h-1c-5 5-9 9-15 13v1h-1 0v-3c0-1 0-1 1-1l2-2c-1 0-1 0-1-1s0 0-1-1c0 0 0-1 1-1l1-2s1-1 2-1c0 0 1 0 1-1v2c1 0 2-1 3-2h1c2-1 2-1 5-1z" class="N"></path><path d="M561 310c2-1 2-1 5-1-2 2-4 4-7 4l2-3z" class="H"></path><path d="M552 319h1c1-1 2-1 3-2h1l-5 4v1 1h-1 0v-3c0-1 0-1 1-1z" class="W"></path><path d="M560 310h1l-2 3c-2 1-3 3-5 4-1 0-1 0-1-1s0 0-1-1c0 0 0-1 1-1l1-2s1-1 2-1c0 0 1 0 1-1v2c1 0 2-1 3-2z" class="a"></path><path d="M556 311s1 0 1-1v2c-1 1-3 2-4 2h0l1-2s1-1 2-1z" class="W"></path><path d="M575 307c1-2 1-4 3-5 0 2-1 4-2 6-2 6-6 11-11 15-2 2-5 5-8 6l-7 3h-2c0-1 1-3 1-4 1-3 1-5 2-8v3h0l1 1c2 0 3-1 5-1 1-1 2-1 3-2 4-3 7-7 10-11 1-1 2-3 4-4l1 1z" class="C"></path><path d="M551 328c2-1 4-3 6-4l1 1c4-1 7-7 10-10l1 1c-2 3-5 5-7 8-2 1-4 3-5 5l-7 3h-2c0-1 1-3 1-4h2z" class="l"></path><path d="M548 332c0-1 1-3 1-4h2 1l-1 1c0 1-1 2-1 3h-2z" class="m"></path><path d="M575 307c1-2 1-4 3-5 0 2-1 4-2 6-2 6-6 11-11 15-2 2-5 5-8 6 1-2 3-4 5-5 2-3 5-5 7-8 0-1 1-1 1-2 2-2 3-5 5-7z" class="L"></path><path d="M217 123c0 1 1 3 2 4l2 5v3l1 1c-1 0-1 1-1 1s1 1 0 1c-1 1-1 2-2 3l-1 1c-1-1-1-2-2-3-1 1-2 3-2 5-1 3-2 7-3 11 0 1 0 3-1 4l-1 1v1c-1 0-1 0-1-1v1c-1 0-2-1-2-1-2-1-2-1-4 0h-2l-1 1h-1l-9-1c1-2 2-3 2-4l2-3c2-3 4-6 7-10s7-9 11-13l3-3c0-1 1-2 1-2v-1c1 0 1 0 2-1z"></path><path d="M191 156l1 1 1 2h1l2 1v-1h2s1 1 1 2h-1l-9-1c1-2 2-3 2-4z" class="j"></path><defs><linearGradient id="L" x1="207.876" y1="132.215" x2="218.548" y2="154.342" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#929091"></stop></linearGradient></defs><path fill="url(#L)" d="M217 123c0 1 1 3 2 4l2 5v3l1 1c-1 0-1 1-1 1s1 1 0 1c-1 1-1 2-2 3l-1 1c-1-1-1-2-2-3-1 1-2 3-2 5-1 3-2 7-3 11 0 1 0 3-1 4l-1 1v1c-1 0-1 0-1-1v1c-1 0-2-1-2-1-2-9 2-19 6-27l2-4v-2c0-1 1-2 1-2v-1c1 0 1 0 2-1z"></path><path d="M217 123c0 1 1 3 2 4-2 2-4 2-5 2v-2c0-1 1-2 1-2v-1c1 0 1 0 2-1z" class="F"></path><path d="M221 135l1 1c-1 0-1 1-1 1s1 1 0 1c-1 1-1 2-2 3l-1 1c-1-1-1-2-2-3h0v-1h1 1v-1l1-1c0-1 1-1 2-1z" class="H"></path><defs><linearGradient id="M" x1="214.959" y1="151.96" x2="204.142" y2="141.959" xlink:href="#B"><stop offset="0" stop-color="#777574"></stop><stop offset="1" stop-color="#9f9ea1"></stop></linearGradient></defs><path fill="url(#M)" d="M212 133h1c0 1-1 3-1 5 0 1-2 3-2 5 1 2 0 5-1 7s-1 4-1 6v4 1c-1 0-2-1-2-1-2-9 2-19 6-27z"></path><path d="M210 143c1 2 0 5-1 7s-1 4-1 6h-1c0-3 0-6 1-8l2-5z" class="I"></path><path d="M335 36c1-1 2-1 4-2l-3 6c0 1 1 1 1 1l6-2c-1 2-3 4-4 6s-2 4-2 6h0l-1 4c0 1-1 2 0 3 0 1 0 5-1 5h0-1v3 1 2l-1 2c1 1 3 4 3 6h-1l-2-3c-2-3-3-7-6-9 0 0-1 0-1-1 0 0-2-1-2-2h0c-1-2-2-3-3-4s-5-3-6-3l-1-1c-2-2-5-4-7-6l-2-2h0c1 0 1-1 1-1l1 1h2c1 0 3-1 4-1l6-3h2l6-2s1 1 2 1c1-3 4-5 6-7v2z" class="W"></path><path d="M332 52v-1h2v2h-2 0v-1z" class="D"></path><path d="M331 59v-6h1 0l2 10v3 1l-2-4-1-4z" class="S"></path><defs><linearGradient id="N" x1="337.415" y1="41.322" x2="337.845" y2="46.128" xlink:href="#B"><stop offset="0" stop-color="#575657"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#N)" d="M337 41l6-2c-1 2-3 4-4 6s-2 4-2 6h-2v1h-1v-6l1-1v-2l2-2z"></path><path d="M335 36c1-1 2-1 4-2l-3 6c-2 2-3 5-4 8v4 1h-1v6-3c-1 0-1 0-2-1l-1-1 1 1c1-2 1-4 1-6 1-5 2-9 5-13z" class="N"></path><path d="M335 34v2c-3 4-4 8-5 13 0 2 0 4-1 6l-1-1c-1-1-1-3-2-5l1-1h-2c1-2 2-4 2-6v-2s1 1 2 1c1-3 4-5 6-7z" class="j"></path><path d="M329 41h0c-1 4-2 6-1 9v2h1v-1s1-1 1-2c0 2 0 4-1 6l-1-1c-1-1-1-3-2-5l1-1h-2c1-2 2-4 2-6v-2s1 1 2 1z" class="Q"></path><path d="M326 55c-1-2-1-3-1-5l1-1c1 2 1 4 2 5l1 1c1 1 1 1 2 1v3l1 4 2 4v2l-1 2c1 1 3 4 3 6h-1l-2-3c-2-3-3-7-6-9 0 0-1 0-1-1 0 0-2-1-2-2h2s1-1 1-2v-3l-1-2z" class="b"></path><path d="M327 57h0c0 1 1 2 1 3 1 1 2 3 4 4v-1l2 4v2l-1 2-6-11v-3z" class="K"></path><path d="M326 55c-1-2-1-3-1-5l1-1c1 2 1 4 2 5l1 1c1 1 1 1 2 1v3l1 4v1c-2-1-3-3-4-4 0-1-1-2-1-3h0l-1-2z" class="T"></path><path d="M321 42l6-2v2c0 2-1 4-2 6h2l-1 1-1 1c0 2 0 3 1 5l1 2v3c0 1-1 2-1 2h-2 0c-1-2-2-3-3-4s-5-3-6-3l-1-1c-2-2-5-4-7-6l-2-2h0c1 0 1-1 1-1l1 1h2c1 0 3-1 4-1l6-3h2z" class="h"></path><path d="M321 42l6-2v2h-6z" class="G"></path><path d="M314 54h2l1-2h0v1l1 1h2c1-1 0-2 0-3 1 1 2 1 2 2h2l1-1v3 1h-1-1c0-1-1-1-2-2v1l-2 1c-1-1-2-1-2-2-1 0-2 0-2 1l-1-1z" class="d"></path><path d="M306 45l1 1h2c1 0 3-1 4-1l6-3-1 1 1 1c-3 1-5 2-6 3h-4-1l-1 1-2-2h0c1 0 1-1 1-1z" class="G"></path><path d="M325 48h2l-1 1-1 1c0 2 0 3 1 5l1 2v3c0 1-1 2-1 2h-2 0c-1-2-2-3-3-4s-5-3-6-3c0-1 1-1 2-1 0 1 1 1 2 2l2-1v-1c1 1 2 1 2 2h1 1v-1-3-4z" class="M"></path><path d="M326 55l1 2v3c0 1-1 2-1 2h-2 0c-1-2-2-3-3-4h2s1 1 2 1h0c1-1 0-1 0-2s1-2 1-2z" class="S"></path><path d="M583 235c1 0 1 1 2 2v6c0 2 1 5 2 7l1 3-1-2h-1-1c1 1 1 2 1 3-1 1 0 1-1 1v3c0 1-1 1-2 1l-1-4v5c0 4-2 9-3 13h0v3c0 1 0 3-1 4 0 1-1 3-1 4l-1 2h-1v3c-1 1-1 2-1 2v1l-3 7v1c-1 1-3 2-3 4-3 0-6 4-8 6-1 1-2 2-3 2v-2c0 1-1 1-1 1-1 0-2 1-2 1-1-2 0-3 0-5v-5c0-1 1-2 1-4s0-5 1-7h0 1c3-3 7-7 10-11l1-2 1 1v1c2-3 4-5 5-7l2-2c1-2 2-5 2-7 1-4 2-10 3-14 0-5 0-9 1-13l1-2z" class="d"></path><path d="M585 245c0 1 2 5 2 6h-1-1 0l-1-1c-1-1 0-3 0-5h1z" class="B"></path><path d="M582 255h0c1-2 1-3 1-5s0-5 1-6l1 1h-1c0 2-1 4 0 5l1 1h0c1 1 1 2 1 3-1 1 0 1-1 1v3c0 1-1 1-2 1l-1-4z" class="g"></path><path d="M565 293c2-2 5-4 7-7 1-1 2-3 3-5s2-5 4-8v3c0 1 0 3-1 4 0 1-1 3-1 4l-1 2h-1v3c-1 1-1 2-1 2l-1 1v-1l-5 5c1-2 2-4 4-6-2 1-3 2-4 3h-3z" class="R"></path><path d="M575 289l-2-1h0c0-1 1-2 2-3v1 3z" class="b"></path><defs><linearGradient id="O" x1="567.361" y1="286.594" x2="561.013" y2="283.334" xlink:href="#B"><stop offset="0" stop-color="#c2c3c5"></stop><stop offset="1" stop-color="#f1efed"></stop></linearGradient></defs><path fill="url(#O)" d="M568 278l1 1v1c2-3 4-5 5-7l2-2c-2 6-7 13-11 18l-10 9c0-2 0-5 1-7h0 1c3-3 7-7 10-11l1-2z"></path><path d="M568 293c1-1 2-2 4-3-2 2-3 4-4 6l5-5v1l1-1v1l-3 7v1c-1 1-3 2-3 4-3 0-6 4-8 6-1 1-2 2-3 2v-2c0 1-1 1-1 1-1 0-2 1-2 1-1-2 0-3 0-5v-5c2 0 3-1 4-2l7-7h3z" class="F"></path><path d="M574 291v1l-3 7v1c-1 1-3 2-3 4-3 0-6 4-8 6-1 1-2 2-3 2v-2h1c1-1 2-2 3-4 2-2 5-4 7-6l5-8 1-1z" class="D"></path><defs><linearGradient id="P" x1="562.911" y1="294.326" x2="558.848" y2="305.7" xlink:href="#B"><stop offset="0" stop-color="#7c7b7a"></stop><stop offset="1" stop-color="#9a989a"></stop></linearGradient></defs><path fill="url(#P)" d="M565 293h3c-2 3-4 7-7 9 0 1 1 2 0 3h0v1c-1 2-2 3-3 4h-1c0 1-1 1-1 1-1 0-2 1-2 1-1-2 0-3 0-5v-5c2 0 3-1 4-2l7-7z"></path><path d="M558 300v1l-3 4-1 2v-5c2 0 3-1 4-2z" class="Y"></path><path d="M558 305c1 0 2-2 3-3 0 1 1 2 0 3h0v1c-1 2-2 3-3 4h-1c0 1-1 1-1 1-1 0-2 1-2 1-1-2 0-3 0-5l1-2 1 1c1 0 1-1 2-1z" class="E"></path><path d="M558 306h0c0 1-1 2-1 2h0l1 1h0v1h-1c0 1-1 1-1 1v-2-1l2-2z" class="H"></path><path d="M558 305c1 0 2-2 3-3 0 1 1 2 0 3h0v1c-1 2-2 3-3 4v-1h0l-1-1h0s1-1 1-2h0v-1z" class="c"></path><path d="M303 45l2 1 2 2c2 2 5 4 7 6l1 1c1 0 5 2 6 3s2 2 3 4h0c0 1 2 2 2 2 0 1 1 1 1 1 3 2 4 6 6 9l2 3c0 1 1 2 2 3h-1v2 1l-2 2h0l-1 1h0v3h-1v-1l-3-3c-1-2-1-4-3-6h0l1 4v1c-1 0-1 0-1 1v2c1 0 1 0 1 1s1 1 1 2l-5-4h0v1l-1-1h-1c-1 0-3-4-4-5l1-2c-1-1-2-2-2-3l-2-1c-1-1-2-2-2-4-4-3-8-5-11-8h-1l-2-1h-1c1-1 1-1 2-1v-1l-1-1h0 2v-1l-1-1h0v-1l1-1c-1 0-1 0-1-1h0-1l-2-2h3c2-2 3-4 4-6v-1z" class="F"></path><path d="M320 73l2-1 2 3v1l-1 1c0 1 2 1 1 3-2-2-3-5-4-7z" class="b"></path><path d="M324 76l2 3h0l1 4v1c-1 0-1 0-1 1v2l-1-2 1-1c-1-2-2-3-2-4 1-2-1-2-1-3l1-1z" class="U"></path><path d="M312 71c1 1 2 1 3 2l1-1 2 1c0 2 1 3 2 5 0 2 2 2 2 4h0l-2-3c-1-1-2-3-3-4-1 0-1 0-1 1l-2-1c-1-1-2-2-2-4z" class="O"></path><path d="M300 63l1-1 6 3c2 2 5 4 7 6h1l-1-2v-1c2 1 3 3 4 4v1h0l-2-1-1 1c-1-1-2-1-3-2-4-3-8-5-11-8h-1z" class="S"></path><path d="M309 60c4 2 7 4 10 8l3 4-2 1c-3-3-5-6-8-9-1-1-1-2-2-2h0c-1-1-1-2-1-2z" class="N"></path><path d="M299 54l2 1h1l4 2c0 1 3 2 3 3 0 0 0 1 1 2h0c1 0 1 1 2 2-1-1-2-1-3-2h-1-1c-1-1-2-1-3-1-1-1-1 0-1 0-2-1-2-1-3-2 0 2 1 1 0 2-1 0-1 0-2 1h-1c1-1 1-1 2-1v-1l-1-1h0 2v-1l-1-1h0v-1l1-1c-1 0-1 0-1-1h0 0z" class="O"></path><path d="M301 55h1l4 2c0 1 3 2 3 3 0 0 0 1 1 2h0c1 0 1 1 2 2-1-1-2-1-3-2l-3-3c-2 0-3-1-4-1 0-1-1-1-2-1h0l1-2z" class="S"></path><path d="M306 57v-2h1l2 1 3-1 6 6c1 1 2 3 3 4s2 2 2 3 0 1 1 2l1 3c1 1 1 1 1 2h-2l-2-3-3-4c-3-4-6-6-10-8 0-1-3-2-3-3z" class="C"></path><path d="M306 57v-2h1l2 1c4 3 7 5 10 8 1 1 1 2 1 3s1 1 0 1h-1c-3-4-6-6-10-8 0-1-3-2-3-3z" class="i"></path><path d="M303 45l2 1 2 2c2 2 5 4 7 6l1 1c1 0 5 2 6 3s2 2 3 4h-1c-2-2-4-4-7-5 3 4 6 7 8 11v1l-1-1c0-1-1-2-2-3s-2-3-3-4l-6-6-3 1-2-1h-1v2l-4-2h-1l-2-1h0-1l-2-2h3c2-2 3-4 4-6v-1z" class="R"></path><path d="M307 52v-1h1c1 0 2 1 2 2h0c-1 0-2 0-3-1h0z" class="I"></path><path d="M307 52c1 1 2 1 3 1l2 2-3 1-2-1v-1h0v-2z" class="L"></path><path d="M303 46c1 2 4 4 5 5h-1v1h0v2h0v1h-1v2l-4-2h-1l-2-1h0-1l-2-2h3c2-2 3-4 4-6z" class="N"></path><path d="M298 54l1-2c2 0 3-1 5-1h1l-1 1v1c-1 1-4-1-5 1h0-1z" class="R"></path><path d="M299 54c1-2 4 0 5-1v-1l1-1 2 1h0v2h0v1h-1v2l-4-2h-1l-2-1z" class="D"></path><path d="M302 55l1-1h4v1h-1v2l-4-2z" class="T"></path><path d="M324 68c-2-4-5-7-8-11 3 1 5 3 7 5h1 0c0 1 2 2 2 2 0 1 1 1 1 1 3 2 4 6 6 9l2 3c0 1 1 2 2 3h-1v2 1l-2 2h0l-1 1h0v3h-1v-1l-3-3c-1-2-1-4-3-6h0 0l-2-3v-1h2c0-1 0-1-1-2l-1-3c-1-1-1-1-1-2l1 1v-1z" class="a"></path><path d="M326 64c0 1 1 1 1 1 3 2 4 6 6 9 0 1 0 5-1 6h-1c-1-4-2-9-4-13-1-1-1-2-1-3z" class="J"></path><path d="M324 68c2 2 3 5 4 7s2 4 2 6l2 2h0l-1-3h1c1-1 1-5 1-6l2 3c0 1 1 2 2 3h-1v2 1l-2 2h0l-1 1h0v3h-1v-1l-3-3c-1-2-1-4-3-6h0 0l-2-3v-1h2c0-1 0-1-1-2l-1-3c-1-1-1-1-1-2l1 1v-1z" class="E"></path><path d="M330 81l2 2h0c1 1 1 3 0 4v1c-1-2-1-5-2-7z" class="T"></path><path d="M167 81v-1h1c1 1 3 1 4 2s3 2 5 3l3 1 1 1h1l4 2h4c2 0 3 1 5 1h9l8-2c1 2 1 2 3 2h1l6-2c2 0 3 0 5-1l3-1h1l1 1c1-1 3-2 4-3 2 0 4-1 5-2v2h2 0l1 1h1l1 1 1 1 1-1v1c0 1 1 1 2 2l-1 1v-1h-3l1 1h0-1-1 0c-2 0-7 1-8 2v1h-1c-1 0-2 0-3 1s-2 1-4 2l-3 1-5 1-10 2c-6 1-11 1-17 1-9-1-17-6-23-13 0 0-1-1-1-2l-1-1c-1-1-1-3-2-4z" class="i"></path><path d="M182 93h0c1-1 3-1 4-1l4 1c1 1 2 0 3 1-1 0-2 0-3 1h0c-3 0-5-1-8-2z" class="R"></path><path d="M237 92v1h-1-2l-2 1h-2c-4 1-7 0-11 2-1 0-1 0-2-1h-3l8-2h2c1 0 0 1 1 1h2v-1c2 1 3 0 4 0 2-1 4-1 6-1z" class="E"></path><path d="M195 95h6 13 3c1 1 1 1 2 1-3 1-7 1-10 1s-5-1-8-1h-4-1-1v-1z" class="C"></path><path d="M212 92l1 1h2c2 0 4-1 5-1l2 1-8 2h-13-6-5c1-1 2-1 3-1 6 0 13 0 19-2z" class="I"></path><path d="M167 81v-1h1c1 1 3 1 4 2s3 2 5 3l3 1 1 1h1l4 2c-1 1-1 0-2 2 1 1 1 1 2 1-1 0-3 0-4 1h0c-3-1-7-3-9-5h-1-1s-1-1-1-2l-1-1c-1-1-1-3-2-4z" class="H"></path><path d="M167 81v-1h1c1 1 3 1 4 2s3 2 5 3l3 1 1 1c-1 0-1 0-2 1l1 1h0c-3 0-5-2-7-4-1 0-3-1-4 0-1-1-1-3-2-4z" class="Z"></path><path d="M216 90l6-2c0 1 1 1 1 2l2-1-2 3h-3c-1 0-3 1-5 1h-2l-1-1c-6 2-13 2-19 2-1-1-2 0-3-1l-4-1c-1 0-1 0-2-1 1-2 1-1 2-2h4c2 0 3 1 5 1h9l8-2c1 2 1 2 3 2h1z" class="J"></path><path d="M204 90l8-2c1 2 1 2 3 2h1c-2 0-6 1-8 0h-4z" class="K"></path><path d="M223 90l2-1-2 3h-3c-1 0-3 1-5 1h-2l-1-1 11-2z" class="W"></path><path d="M236 84c2 0 4-1 5-2v2h2 0l1 1h1l1 1 1 1 1-1v1c0 1 1 1 2 2l-1 1v-1h-3l1 1h0-1-1 0c-2 0-7 1-8 2-2 0-4 0-6 1-1 0-2 1-4 0v1h-2c-1 0 0-1-1-1h-2l-2-1h3l2-3-2 1c0-1-1-1-1-2 2 0 3 0 5-1l3-1h1l1 1c1-1 3-2 4-3z" class="O"></path><path d="M232 87c1-1 3-2 4-3l1 2-6 3c0-1 1-2 1-2z" class="k"></path><path d="M236 84c2 0 4-1 5-2v2c-1 1-3 2-4 2l-1-2z" class="b"></path><path d="M230 86h1l1 1s-1 1-1 2c-2 1-5 2-8 3l2-3-2 1c0-1-1-1-1-2 2 0 3 0 5-1l3-1z" class="c"></path><path d="M230 86h1l-6 3-2 1c0-1-1-1-1-2 2 0 3 0 5-1l3-1z" class="E"></path><defs><linearGradient id="Q" x1="244.707" y1="88.197" x2="236.662" y2="90.041" xlink:href="#B"><stop offset="0" stop-color="#7c7a7c"></stop><stop offset="1" stop-color="#90908f"></stop></linearGradient></defs><path fill="url(#Q)" d="M246 86l1 1 1-1v1c0 1 1 1 2 2l-1 1v-1h-3l1 1h0-1-1 0c-2 0-7 1-8 2-2 0-4 0-6 1-1 0-2 1-4 0v1h-2c-1 0 0-1-1-1 5-2 10-3 15-5 3 0 4-1 7-2z"></path><path d="M247 87l1-1v1c0 1 1 1 2 2l-1 1v-1h-3l-1 1-1-1c1 0 1-1 1-1 1 0 1 0 2-1h0z" class="I"></path><defs><linearGradient id="R" x1="390.041" y1="303.139" x2="378.666" y2="274.922" xlink:href="#B"><stop offset="0" stop-color="#a4a1a2"></stop><stop offset="1" stop-color="#cfcfd0"></stop></linearGradient></defs><path fill="url(#R)" d="M393 247v1c1 2 4 4 4 5 1 0 2-1 3-2h1v2l-2 2c0 1-1 2-1 2-1 1-1 1-2 1 0 2 1 3 1 4l-1 1v2h-1v1 1c1 1 1 3 1 4 1 2 1 5 1 7l-2 6c-1 1-2 1-3 2l-3 7h0l-1 3-1 1-1 2h0c-1 0-2 1-3 2l-2 6c-1 1-1 2-2 3 0-6-1-14 0-20v-6c1-3 1-6 2-9v-2-2h0v-2h0c0-3 2-5 3-7l-1-1c-1 1-1 2-1 2h-1v-2h-1v-2l1-2c0-1 0-3 1-4v1c0-2 1-4 2-5 1 0 1 1 2 1s2-1 3-2h1c1-1 1-1 2-1h1z"></path><path d="M385 297h2l-1 2h0c-1 0-2 1-3 2l2-4z" class="P"></path><path d="M385 295l3-18 1 1c0 2-1 3-1 5v3c-1 2-2 5-2 8h1c1 0 1-1 2-1h0l-1 3-1 1h-2v-2z" class="K"></path><path d="M385 295l3 1-1 1h-2v-2z" class="D"></path><path d="M381 275v1c0 2 0 5-1 7v4c-1 1 0 3 0 5h0v-1-1c0-1 0-2 1-3 0-1-1-1 0-2v-1-1-1h1c0-1 0-2 1-3l-4 23v-12-6c1-3 1-6 2-9z" class="V"></path><defs><linearGradient id="S" x1="379.75" y1="288.508" x2="398.607" y2="266.679" xlink:href="#B"><stop offset="0" stop-color="#b5b3b4"></stop><stop offset="1" stop-color="#d8d8d8"></stop></linearGradient></defs><path fill="url(#S)" d="M388 265h0c1-2 3-5 5-6l1 3 2 1v2h-1v1 1c1 1 1 3 1 4 1 2 1 5 1 7l-2 6c-1 1-2 1-3 2l-3 7c-1 0-1 1-2 1h-1c0-3 1-6 2-8v-3c0-2 1-3 1-5l-1-1c1-1 1-1 1-2v-1l1-2c1-2 1-3 1-5h-1v3 1l-4 1 2-6v-1z"></path><path d="M394 262l2 1v2h-1v1 1c0-2-1-3-1-5z" class="M"></path><path d="M389 278c1-1 1-1 1-3h1v4c0 2-2 5-3 7v-3c0-2 1-3 1-5z" class="J"></path><path d="M396 271c1 2 1 5 1 7l-2 6c-1 1-2 1-3 2 1-3 2-6 4-9v-6z" class="E"></path><path d="M393 247v1c1 2 4 4 4 5 1 0 2-1 3-2h1v2l-2 2c0 1-1 2-1 2-1 1-1 1-2 1 0 2 1 3 1 4l-1 1-2-1-1-3c-2 1-4 4-5 6h0c-2 5-4 9-5 14-1 1-1 2-1 3h-1v1 1 1c-1 1 0 1 0 2-1 1-1 2-1 3v1 1h0c0-2-1-4 0-5v-4c1-2 1-5 1-7v-1-2-2h0v-2h0c0-3 2-5 3-7l-1-1c-1 1-1 2-1 2h-1v-2h-1v-2l1-2c0-1 0-3 1-4v1c0-2 1-4 2-5 1 0 1 1 2 1s2-1 3-2h1c1-1 1-1 2-1h1z" class="G"></path><path d="M400 251h1v2l-2 2c0 1-1 2-1 2-1 1-1 1-2 1 0 2 1 3 1 4l-1 1-2-1-1-3v-1c1 0 0 1 1 2 0 1 1 1 2 2h1l-1-1v-1c0-2 0-2 1-3 1 0 1-1 2-2v-1h-1c-1 1-4 3-5 2 0 0 1 0 2-1v-1h0l2-1c1 0 2-1 3-2z" class="d"></path><path d="M388 256h4c-6 4-9 11-11 17v-2h0v-2h0c0-3 2-5 3-7s3-4 4-6z" class="T"></path><path d="M393 247v1c1 2 4 4 4 5l-2 1h0c-1 0-2 1-3 2h-4c-1 2-3 4-4 6l-1-1 2-3v-2c2-1 2-3 4-4 1-2 2-2 2-4l1-1h1z" class="a"></path><path d="M385 258c3-3 4-7 8-9h0c1 1 1 1 0 2-2 1-3 3-5 5-1 2-3 4-4 6l-1-1 2-3z" class="m"></path><path d="M384 249c1 0 1 1 2 1s2-1 3-2h1c1-1 1-1 2-1l-1 1c0 2-1 2-2 4-2 1-2 3-4 4v2l-2 3c-1 1-1 2-1 2h-1v-2h-1v-2l1-2c0-1 0-3 1-4v1c0-2 1-4 2-5z" class="E"></path><path d="M382 254v1h0 2c0-1 1-2 2-2h1l-2 3c-1 1-2 2-4 3v-2c0-1 0-3 1-4v1z" class="W"></path><path d="M384 249c1 0 1 1 2 1s2-1 3-2h1c1-1 1-1 2-1l-1 1-4 5h-1c-1 0-2 1-2 2h-2 0v-1c0-2 1-4 2-5z" class="Z"></path><path d="M140 308l3-8 2 1-1 1h-2c1 1 2 2 3 2v1c0 1 0 2 1 3s3 1 5 2c1 0 1 1 1 1h1l1 1 17 7s1 1 2 1h0c6 3 12 7 15 11l5 5h0c0-2-2-3-1-4 0-1 0-1-1-2 1 0 1 0 2 1 0 1 1 2 2 4l3 6c3 12 0 22-6 32-5 8-12 15-19 20l-1 2c-1 0-1 0-1 1h1 0c1 0 1 0 2-1h2l-4 3-2 1c-2 1-3 1-5 1-1 0-2 1-3 1l-1-1c-2 1-5 2-7 4-2-1-3-1-5-2l1-1c-4 1-7 2-11 2 0 0-1 1-2 0v-1c6-1 11-2 17-4l1-1c3 0 7-3 10-4 1 0 2-1 3-1h0c1 0 4-2 5-2 1-2 2-2 3-4l3-3 1-3c4-3 7-8 10-12 1-1 2-3 2-5 1-1 1-2 1-3 1-5 0-11-4-16-1-3-4-5-7-7-5-3-10-5-15-6-8-1-16-1-24-3h-1 5c1 1 2 1 4 0v1h2 1 0c1 0 2 1 3 0h1 1 0 1c1 1 2 1 3 1h3 0c2 0 2 0 3-1 0-1 0-1-1-1h-2l-3-1h0l-1-1h-1c-1 0-2 0-3-1h-1c-1 0-1 0-2-1v-1c1-1 8 0 10 0l-4-1-3-1-4-2h-1-1c0-1-1-1-1-1h-1l-1-1c-1-1-3-1-4-2-2-1-4-1-7-1l-1-1 2-3 1-2z" class="l"></path><path d="M192 340l2 2h1 0c2 3 2 6 2 9l-4-8-1-3z" class="D"></path><defs><linearGradient id="T" x1="191.249" y1="335.464" x2="193.683" y2="340.988" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#T)" d="M188 331l5 5 2 6h0-1l-2-2-4-6c1-1 1-2 0-3z"></path><path d="M173 393l-1 2c-1 0-1 0-1 1h1 0c1 0 1 0 2-1h2l-4 3-2 1c-2 1-3 1-5 1-1 0-2 1-3 1l-1-1 12-7z" class="B"></path><path d="M180 380c4-3 7-8 10-12 0 2 0 3-1 5-3 4-6 7-10 10l1-3z" class="D"></path><path d="M179 327c4 2 7 4 9 7l4 6 1 3-7-8h0c-1-2-3-3-4-5-1-1-2-1-3-3z" class="E"></path><path d="M165 393c1 0 2-1 3-1h0c-5 4-12 8-18 9-4 1-7 2-11 2 0 0-1 1-2 0v-1c6-1 11-2 17-4l1-1c3 0 7-3 10-4z" class="O"></path><path d="M160 318h0c1 0 3 0 4 1h4c2 1 3 2 5 1 6 3 12 7 15 11 1 1 1 2 0 3-2-3-5-5-9-7 1 2 2 2 3 3 1 2 3 3 4 5h0l-12-6h-1l-9-3s-1 0-2-1h1c3 1 6 1 10 3h2 0c-1-2-3-2-5-4-2-1-4-1-6-2l-6-2v-1h1c0 1 1 1 1 1h1c1 1 1 1 2 1h1l2 1h2c0-1-7-2-8-3h0v-1z" class="J"></path><path d="M160 318h0c1 0 3 0 4 1h4c2 1 3 2 5 1 6 3 12 7 15 11 1 1 1 2 0 3-2-3-5-5-9-7-1-1-3-2-4-2-5-3-10-5-15-7z" class="S"></path><path d="M140 308l3-8 2 1-1 1h-2c1 1 2 2 3 2v1c0 1 0 2 1 3s3 1 5 2c1 0 1 1 1 1h1l1 1 17 7s1 1 2 1h0c-2 1-3 0-5-1h-4c-1-1-3-1-4-1h0v1h0c1 1 8 2 8 3h-2l-2-1h-1c-1 0-1 0-2-1h-1s-1 0-1-1h-1v1c-1 0-2-1-2-1h-1c-1-1-3-2-5-1l-1-1c-1-1-3-1-4-2-2-1-4-1-7-1l-1-1 2-3 1-2z" class="I"></path><path d="M150 313c3 1 5 1 6 4h0c-2 0-4-1-6-2v-2z" class="R"></path><path d="M139 310l1-2 1 1 5 2h-3c-2 0-3 0-4-1z" class="C"></path><path d="M146 311c1 0 3 1 4 2v2l-6-3-1-1h3z" class="W"></path><path d="M140 308l3-8 2 1-1 1h-2c1 1 2 2 3 2v1c0 1 0 2 1 3s3 1 5 2c1 0 1 1 1 1h1l1 1-12-4-1 1-1-1z" class="J"></path><path d="M139 310c1 1 2 1 4 1l1 1 6 3c2 1 4 2 6 2 1 1 3 1 4 2h0c1 1 8 2 8 3h-2l-2-1h-1c-1 0-1 0-2-1h-1s-1 0-1-1h-1v1c-1 0-2-1-2-1h-1c-1-1-3-2-5-1l-1-1c-1-1-3-1-4-2-2-1-4-1-7-1l-1-1 2-3z" class="C"></path><path d="M139 310c1 1 2 1 4 1l1 1v1c-1-1-2-1-2-1h-1v2l-2-1-1 1-1-1 2-3z" class="i"></path><defs><linearGradient id="U" x1="480.643" y1="53.389" x2="500.743" y2="108.977" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262526"></stop></linearGradient></defs><path fill="url(#U)" d="M512 57v-2c-2-2-8-7-11-8h-1c2 0 3 1 5 2 1 0 3 1 4 2 1 2 3 3 4 4 0 1 1 1 1 2h1c2 3 3 9 3 13 0 7-3 12-9 17-2 2-5 4-8 4 2 1 4 1 6 2 7 1 13 1 19 2 5-1 8-3 12-6-1 4-3 7-6 10l-1-1h-1c-1 1-1 1-2 1h0c-1 0-8 2-8 3v1c-1 0-2-1-3-1l-2-1c-1-1-2-2-3-2h-3s0 1-2 1h-2-2l-3-1c-1 0-1-1-2-1h0v-1h3-5 0-4-2c-3-1-6-1-10-1h-4l-7-1-12-2v-1c-1 0-5-1-6-1 0-1-1-1-1-2l-2-1-6-1v-1h3l1 1v-2h3c1 0 2 0 3 1v-1l2-1h0 2 8 6l10 1 6-1c5 0 11-3 15-5 6-4 8-8 10-14 1 2 0 2 1 4l1-1 1 1c1-5 0-8-2-12z"></path><path d="M480 94c3 0 6 1 8 0 1 0 1 1 2 1l4 1c-5 0-9 0-14-2z" class="g"></path><defs><linearGradient id="V" x1="499.498" y1="98.864" x2="506.17" y2="97.889" xlink:href="#B"><stop offset="0" stop-color="#656264"></stop><stop offset="1" stop-color="#7c7c7c"></stop></linearGradient></defs><path fill="url(#V)" d="M500 99c-1 0-1-1-2-1h0v-1h3l8 2s0 1-2 1h-2-2l-3-1z"></path><path d="M512 99h5c3 0 9-1 11 0-1 0-8 2-8 3v1c-1 0-2-1-3-1l-2-1c-1-1-2-2-3-2z" class="k"></path><defs><linearGradient id="W" x1="495.035" y1="95.721" x2="511.714" y2="91.269" xlink:href="#B"><stop offset="0" stop-color="#131111"></stop><stop offset="1" stop-color="#474648"></stop></linearGradient></defs><path fill="url(#W)" d="M526 95h0-6-3-1l-1 1h0 3 0l1-1c0 1 1 1 1 2h-3l-14-3-4-1c-1 1-2 1-3 1l-3-1h1v-1h0 0v-1l1 1c1-1 4-1 5-1h1c2 1 4 1 6 2 7 1 13 1 19 2z"></path><path d="M454 84l3 1-1 1v2l1 1 10 1h6c-3 1-8 1-11 0h-3c-2-1-5-1-6-1l3 1h1c2 0 3 1 4 1h2 1c1 0 2 1 3 1-6 0-11-2-17-3l-2-1-6-1v-1h3l1 1v-2h3c1 0 2 0 3 1v-1l2-1z" class="V"></path><path d="M446 85h3v2h-3v-2z" class="H"></path><path d="M454 84l3 1-1 1v2l1 1-3-1-5-1v-2c1 0 2 0 3 1v-1l2-1z" class="L"></path><path d="M454 84l3 1-1 1v2l1 1-3-1v-2h-2v-1l2-1z" class="J"></path><path d="M450 89c6 1 11 3 17 3l4 1c3 0 6 0 9 1 5 2 9 2 14 2l2 1h0-4-2c-3-1-6-1-10-1h-4l-7-1-12-2v-1c-1 0-5-1-6-1 0-1-1-1-1-2z" class="O"></path><path d="M450 89c6 1 11 3 17 3l4 1h2v1c-2 0-4 0-6-1l-10-1c-1 0-5-1-6-1 0-1-1-1-1-2z" class="N"></path><defs><linearGradient id="X" x1="459.097" y1="81.691" x2="475.38" y2="93.615" xlink:href="#B"><stop offset="0" stop-color="#bdbbbb"></stop><stop offset="1" stop-color="#eae9eb"></stop></linearGradient></defs><path fill="url(#X)" d="M456 84h8 6l10 1-4 1-1 1v1l8-1h-1c-1 1-1 2-2 2h-1 0c1 1 1 1 2 1h-8-6l-10-1-1-1v-2l1-1-3-1h0 2z"></path><path d="M456 84h8 6l10 1-4 1c-6 1-13 0-19-1l-3-1h0 2z" class="D"></path><path d="M511 65c1 2 0 2 1 4l1-1 1 1c-1 5-3 9-7 13l-6 3c-6 4-13 5-20 5-1 0-1 0-2-1h0 1c1 0 1-1 2-2h1l-8 1v-1l1-1 4-1 6-1c5 0 11-3 15-5 6-4 8-8 10-14z" class="f"></path><defs><linearGradient id="Y" x1="498.07" y1="82.747" x2="495.081" y2="78.605" xlink:href="#B"><stop offset="0" stop-color="#919192"></stop><stop offset="1" stop-color="#b8b7ba"></stop></linearGradient></defs><path fill="url(#Y)" d="M511 65c1 2 0 2 1 4-3 6-7 11-14 14-4 2-10 3-15 4l-8 1v-1l1-1 4-1 6-1c5 0 11-3 15-5 6-4 8-8 10-14z"></path><defs><linearGradient id="Z" x1="581.616" y1="224.088" x2="602.075" y2="224.251" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#383737"></stop></linearGradient></defs><path fill="url(#Z)" d="M582 187v-2h0 1l1 1c7 12 8 27 6 41-1 1-2 6-2 7 1 3 4 6 5 9 3 5 5 11 7 16 2 3 5 8 5 12l-1 1c1 1 2 2 2 4-1 0-1 1-2 2h0c0 2 1 3 0 4l-2-3v1c0 1 0 2-1 3v1l-6 6c-1 0-2 2-2 2-3 3-4 6-5 11h-1l-1-3c-1-1-3-5-3-7-1-2-2-4-2-6s1-3 1-4v-1l-1-1c-1-2-1-4 0-6l-2 1v-3h0c1-4 3-9 3-13v-5l1 4c1 0 2 0 2-1v-3c1 0 0 0 1-1 0-1 0-2-1-3h1 1l1 2-1-3c-1-2-2-5-2-7v-6c-1-1-1-2-2-2 3-9 5-18 5-27-1-7-3-14-6-21z"></path><path d="M592 259c1 1 1 3 2 4h1v-1c-1-1-1-1-1-2v-3c-1-1-1-2-1-2v-1h1s1 0 1 1v1 1 1c1 5 2 10 4 15h-1c-1-2-2-4-3-5l-1-1v-1l-1-2c0-2-1-3-1-5z" class="B"></path><path d="M588 253v1-3h1c1 1 2 6 2 8h1c0 2 1 3 1 5 0 0-1-1-1-2-1 4 0 7 1 10v1l-3-8h-1 0c-1-1-1-2-1-3s0-1 1-2c0-1-2-5-2-6v5l-2-1v-3c1 0 0 0 1-1 0-1 0-2-1-3h1 1l1 2z" class="X"></path><path d="M589 260c0 1 1 3 2 5h0v-1c0-1-1-5 0-6v1h1c0 2 1 3 1 5 0 0-1-1-1-2-1 4 0 7 1 10v1l-3-8h-1 0c-1-1-1-2-1-3s0-1 1-2z" class="N"></path><defs><linearGradient id="a" x1="602.548" y1="267.329" x2="597.298" y2="275.007" xlink:href="#B"><stop offset="0" stop-color="#8d8d90"></stop><stop offset="1" stop-color="#c5c2c1"></stop></linearGradient></defs><path fill="url(#a)" d="M599 273c-2-5-3-10-4-15v-1c1 1 1 3 1 3 1 0 1 1 2 2 0 1 2 2 3 2v1c0 1 2 2 2 3l1 4c1 1 2 2 2 4-1 0-1 1-2 2h0c0 2 1 3 0 4l-2-3c-1-1-2-5-3-6z"></path><path d="M602 272c1-1 0-2 1-4l1 4c1 1 2 2 2 4-1 0-1 1-2 2h0l-2-6z" class="N"></path><path d="M596 260c1 0 1 1 2 2 0 1 2 2 3 2v1c0 1 2 2 2 3-1 2 0 3-1 4l-6-12z" class="O"></path><path d="M593 273v-1c-1-3-2-6-1-10 0 1 1 2 1 2l1 2v1l1 1c1 1 2 3 3 5h1c1 1 2 5 3 6v1c0 1 0 2-1 3v1l-6 6c-1 0-2 2-2 2v-1l1-2h0-1v-3c-1-1-1-1-1-2v-1-2h0l-3-16h1l3 8z" class="D"></path><path d="M598 282l-3-8h0c2 2 3 5 4 8h0v2l-1-1v-1z" class="C"></path><path d="M599 282h0c1-3-1-5-1-7h1c1 2 2 3 3 5 0 1 0 2-1 3v1l-2-2h0z" class="E"></path><defs><linearGradient id="b" x1="590.404" y1="271.291" x2="597.362" y2="287.331" xlink:href="#B"><stop offset="0" stop-color="#a6a4a5"></stop><stop offset="1" stop-color="#c5c4c5"></stop></linearGradient></defs><path fill="url(#b)" d="M589 265h1l3 8c0 1 1 2 2 4l1 1v1c1 1 0 3 1 4l1-1v1l1 1v-2l2 2-6 6c-1 0-2 2-2 2v-1l1-2h0-1v-3c-1-1-1-1-1-2v-1-2h0l-3-16z"></path><path d="M595 277l1 1c-2 2-1 1-1 3h-1v-2h1v-2z" class="J"></path><path d="M587 259v-5c0 1 2 5 2 6-1 1-1 1-1 2s0 2 1 3h0l3 16h0v2 1c0 1 0 1 1 2v3h1 0l-1 2v1c-3 3-4 6-5 11h-1l-1-3c-1-1-3-5-3-7-1-2-2-4-2-6s1-3 1-4v-1l-1-1c-1-2-1-4 0-6l-2 1v-3h0c1-4 3-9 3-13v-5l1 4c1 0 2 0 2-1l2 1z" class="I"></path><path d="M582 255l1 4c1 0 2 0 2-1l2 1c1 3 0 5 0 8-1-1-1-2-1-2l-1-1v1h-1v-1h0c0-1-1-3-1-4l-1-1v1-5z" class="e"></path><path d="M586 276v1h1l-1-7c1 2 3 5 3 8h0v-1c0-1-1-8-1-9 2 4 3 8 3 12v-1c-1 2 0 3-1 4-1 0-1 0-1-1v-1h0 0c-1 1-1 1-1 2h0c1 2 1 4 1 5h-1c0-1-1-6-2-7-1 1-1 3-1 5v-4l-1-1 1-4v-1h1z" class="J"></path><defs><linearGradient id="c" x1="578.896" y1="272.308" x2="583.291" y2="278.732" xlink:href="#B"><stop offset="0" stop-color="#6d6d6c"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#c)" d="M582 260v-1l1 1c0 1 1 3 1 4h0c0 3 1 5 1 8 0 1 1 3 1 4h-1v1l-1-1h-1c0 2-1 4-1 6l-1-1c-1-2-1-4 0-6l-2 1v-3h0c1-4 3-9 3-13z"></path><path d="M579 273h1c0 1 1 1 1 2l-2 1v-3h0z" class="c"></path><path d="M583 276c0-4-1-8 0-12h1 0c0 3 1 5 1 8 0 1 1 3 1 4h-1v1l-1-1h-1z" class="D"></path><path d="M584 276v-4h1c0 1 1 3 1 4h-1v1l-1-1z" class="K"></path><path d="M584 276l1 1-1 4 1 1v4c0-2 0-4 1-5 1 1 2 6 2 7h1c0-1 0-3-1-5h0c0-1 0-1 1-2h0 0v1c0 1 0 1 1 1 1-1 0-2 1-4v1l1 1v2 1c0 1 0 1 1 2v3h1 0l-1 2v1c-3 3-4 6-5 11h-1l-1-3c-1-1-3-5-3-7-1-2-2-4-2-6s1-3 1-4v-1c0-2 1-4 1-6h1z" class="a"></path><path d="M588 283h0c0-1 0-1 1-2h0 0v1c0 1 0 1 1 1 1-1 0-2 1-4v1l1 1v2 1l-1-1v2 1 1l1 1h-1c-1-1 0-1-1-2v-1s0-1-1-1l-1-1z" class="C"></path><path d="M584 290v1c1 3 3 6 2 9h0c-1-1-3-5-3-7v-2l1-1z" class="E"></path><defs><linearGradient id="d" x1="580.71" y1="279.734" x2="588.099" y2="286.653" xlink:href="#B"><stop offset="0" stop-color="#9f9d9f"></stop><stop offset="1" stop-color="#bdbeba"></stop></linearGradient></defs><path fill="url(#d)" d="M584 276l1 1-1 4 1 1v4l1 6h0c-1-1-1-1-2-1v-1l-1 1v2c-1-2-2-4-2-6s1-3 1-4v-1c0-2 1-4 1-6h1z"></path><path d="M582 283l1 5c0 1 0 2 1 2l-1 1v2c-1-2-2-4-2-6s1-3 1-4z" class="K"></path><defs><linearGradient id="e" x1="576.231" y1="196.174" x2="552.16" y2="237.767" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#e)" d="M554 182l2 1 8 8c6 9 11 18 12 28 2 8 1 16 0 23v2l-1 6c-1 1-3 2-4 2l-1 7c-1 1-1 2 0 3-1 1-1 2-2 3 0 1-1 1-1 2s-1 1-1 2h0c-1 2-3 3-4 5 0-1 0-1-1-2h0l3-4v-4c-1 0-1-1-1-2h-1l-1 2h0c-1 1-1 0-1 1h0v-1-7-4c0-1 0-4 1-5l-2-1h0v-2c-1-3 0-7-1-10v-11l-4-42z"></path><path d="M572 242l1-3c0 3 0 5-1 7v3l1 1 2-2c-1-2-1-2 1-4l-1 6c-1 1-3 2-4 2l1-10z" class="U"></path><defs><linearGradient id="f" x1="560.834" y1="211.214" x2="564.697" y2="219.395" xlink:href="#B"><stop offset="0" stop-color="#201f1f"></stop><stop offset="1" stop-color="#3f3c3f"></stop></linearGradient></defs><path fill="url(#f)" d="M561 222c0-2 0-4-1-6 0-1-1-3-1-4l4-2c1 2 1 4 2 7l1 4c0 2 1 5 1 7l-2 1 1 3-1 2h-1l-1-8h-1v2h0l-1-6z"></path><path d="M561 222c1-1 1-3 1-4v1l1 2v5h-1v2h0l-1-6z" class="X"></path><defs><linearGradient id="g" x1="564.575" y1="218.411" x2="562.575" y2="229.925" xlink:href="#B"><stop offset="0" stop-color="#1e1e1c"></stop><stop offset="1" stop-color="#3b3b3c"></stop></linearGradient></defs><path fill="url(#g)" d="M563 221c-1-2-1-4 0-6v1l1 6h0c1 0 1 0 2-1 0 2 1 5 1 7l-2 1 1 3-1 2h-1l-1-8v-5z"></path><path d="M564 222c1 0 1 0 2-1 0 2 1 5 1 7l-2 1-1-7z" class="I"></path><defs><linearGradient id="h" x1="561.324" y1="238.318" x2="561.726" y2="250.398" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#777776"></stop></linearGradient></defs><path fill="url(#h)" d="M558 224c0 1 0 2 1 3s1 2 1 3c2 1 1 1 2 3v5c0-1 1-3 0-4v-1-2-1-2h0v-2h1l1 8h1l1-2c-1 2-1 5-1 8h0c0 1 0 2-1 3h-1c-1 2-1 6-1 8-1 2-1 3-1 5l-1 1v-4c0-1 0-4 1-5l-2-1h0v-2c-1-3 0-7-1-10v-11z"></path><path d="M562 228v-2h1l1 8h1l1-2c-1 2-1 5-1 8h0c0 1 0 2-1 3h-1v-2-5c0-3-1-5-1-8z" class="Z"></path><path d="M563 236v-1c1 0 1 0 1 1s0 3 1 4c0 1 0 2-1 3h-1v-2-5z" class="R"></path><defs><linearGradient id="i" x1="562.083" y1="233.168" x2="556.438" y2="238.14" xlink:href="#B"><stop offset="0" stop-color="#5e5c5c"></stop><stop offset="1" stop-color="#808082"></stop></linearGradient></defs><path fill="url(#i)" d="M558 224c0 1 0 2 1 3s1 2 1 3c2 6 0 13 1 18l-2-1h0v-2c-1-3 0-7-1-10v-11z"></path><defs><linearGradient id="j" x1="569.006" y1="219.485" x2="569.796" y2="243.801" xlink:href="#B"><stop offset="0" stop-color="#2e2d2e"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#j)" d="M565 217l1 1c1 1 1 1 1 2 1 2 2 3 2 5h0l2-1c0 1 1 2 1 3 1 4 2 7 1 10v2l-1 3-1 10-1 7c-1 1-1 2 0 3-1-1-1-1-1-2h0v-2h-1c0-1 0-2 1-3v-4-4-4c0-1-1-2-2-2 1-4 0-9 0-13 0-2-1-5-1-7l-1-4z"></path><defs><linearGradient id="k" x1="567.916" y1="240.881" x2="571.738" y2="256.376" xlink:href="#B"><stop offset="0" stop-color="#5e5d5f"></stop><stop offset="1" stop-color="#8a878a"></stop></linearGradient></defs><path fill="url(#k)" d="M569 251c2-1 0-9 2-11h1v2l-1 10-1 7c-1 1-1 2 0 3-1-1-1-1-1-2h0v-2h-1c0-1 0-2 1-3v-4z"></path><path d="M567 228c0 4 1 9 0 13 1 0 2 1 2 2v4 4 4c-1 1-1 2-1 3h1v2h0c0 1 0 1 1 2-1 1-1 2-2 3 0 1-1 1-1 2s-1 1-1 2h0c-1 2-3 3-4 5 0-1 0-1-1-2h0l3-4v-4c-1 0-1-1-1-2h-1l-1 2h0c-1 1-1 0-1 1h0v-1-7l1-1c0-2 0-3 1-5 0-2 0-6 1-8h1c1-1 1-2 1-3h0c0-3 0-6 1-8l-1-3 2-1z" class="D"></path><path d="M566 258v-1-7c1 0 1 1 2 0-1 3-1 5-2 8z" class="k"></path><path d="M566 250l1-9c1 0 2 1 2 2v4l-1 1v2c-1 1-1 0-2 0z" class="N"></path><defs><linearGradient id="l" x1="569.958" y1="252.765" x2="564.535" y2="255.055" xlink:href="#B"><stop offset="0" stop-color="#807d7d"></stop><stop offset="1" stop-color="#9b9a9d"></stop></linearGradient></defs><path fill="url(#l)" d="M569 247v4 4c-1 1-1 2-1 3s0 1-1 1h-1 0v-1c1-3 1-5 2-8v-2l1-1z"></path><path d="M565 240c0 3 0 9-1 12h-1 0c0-1-1-1-1-1 0-2 0-6 1-8h1c1-1 1-2 1-3h0z" class="H"></path><path d="M245 45h1l1 2 9 9h1 2c1 1 6 3 7 3v-1c1 0 2 0 3-1l17-7c1-1 4-2 5-2l3-1 3 1h-1v3l-3 1h0v1c1 0 2 0 3-1l2 2h1 0c0 1 0 1 1 1l-1 1v1h0l1 1v1h-2 0l1 1v1c-1 0-1 0-2 1h1l2 1h1c3 3 7 5 11 8 0 2 1 3 2 4l2 1c0 1 1 2 2 3l-1 2c1 1 3 5 4 5h1l1 1c0 1 1 2 1 3 1 1 1 2 1 2v1 1 2c-2-2-4-3-6-5l-2-1c-2-1-4-2-7-3v1c1 0 3 1 4 2-1 0-2-1-3-1l-1-1h-3l-18-3h-2c-2-2-4-4-5-7-3-3-7-6-10-9-3-2-7-4-10-6-7-4-14-10-17-18z" class="l"></path><path d="M282 64c6 2 11 4 16 7 1 1 1 2 1 3l-14-8c-1 0-2-1-3-2z" class="T"></path><path d="M298 83h-4c-3-1-7-4-8-6 0-1 1-1 2-1h1c1 0 1 0 2 1h0 2v1h2v1h0l-1 1c1 0 3 1 5 1l1 1 2 1v1c-1 0-3-1-4-1z" class="E"></path><path d="M298 83c-3-1-5-2-7-4v-1l3 2c1 0 3 1 5 1l1 1 2 1v1c-1 0-3-1-4-1z" class="N"></path><path d="M298 71l7 4 4 3c1 0 2 2 4 2v1l-1 2c1 1 2 1 2 2h2c0 1 1 1 2 1l2 3 1 1-1 1h0-1l-2-1c-2-1-4-2-7-3-1-1-3-1-5-2l-3-1v-1l-2-1-1-1c-2 0-4-1-5-1l1-1h0c1 0 1 0 2 1h0c1 0 2 0 3-1v-1l-3-2c2 0 5 2 7 2h0c0-2-3-3-5-4 0-1 0-2-1-3z" class="L"></path><path d="M299 81c1 0 1-1 2-1v-1l1-1c1 1 2 1 4 2l-2 1h-2c-1 0-1 1-2 1l-1-1z" class="P"></path><path d="M306 80l2 1 3 3c-2-1-3-1-5-2l-1 1v2l-3-1v-1l-2-1c1 0 1-1 2-1h2l2-1z" class="H"></path><path d="M302 83h1l-1-1c1-1 3 0 4 0l-1 1v2l-3-1v-1z" class="R"></path><path d="M305 75l4 3c1 0 2 2 4 2v1l-1 2c1 1 2 1 2 2h-1c-1 0-1-1-2-1l-3-3c0-2-2-3-3-5v-1z" class="W"></path><path d="M309 78c1 0 2 2 4 2v1l-1 2c-1-2-2-2-3-4v-1z" class="R"></path><defs><linearGradient id="m" x1="314.471" y1="91.605" x2="309.009" y2="81.151" xlink:href="#B"><stop offset="0" stop-color="#434445"></stop><stop offset="1" stop-color="#676566"></stop></linearGradient></defs><path fill="url(#m)" d="M306 82c2 1 3 1 5 2 1 0 1 1 2 1h1 2c0 1 1 1 2 1l2 3 1 1-1 1h0-1l-2-1c-2-1-4-2-7-3-1-1-3-1-5-2v-2l1-1z"></path><path d="M314 85h2c0 1 1 1 2 1l2 3h-1s-1-1-2-1c-2 0-2-1-4-3h1z" class="N"></path><defs><linearGradient id="n" x1="270.354" y1="70.011" x2="313.191" y2="62.762" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#n)" d="M291 48l3-1 3 1h-1v3l-3 1h0v1c1 0 2 0 3-1l2 2h1 0c0 1 0 1 1 1l-1 1v1h0l1 1v1h-2 0l1 1v1c-1 0-1 0-2 1h1l2 1h1c3 3 7 5 11 8 0 2 1 3 2 4l2 1c0 1 1 2 2 3l-1 2c1 1 3 5 4 5h1l1 1c0 1 1 2 1 3 1 1 1 2 1 2v1 1 2c-2-2-4-3-6-5h1 0l1-1-1-1-2-3c-1 0-2 0-2-1h-2c0-1-1-1-2-2l1-2v-1c-2 0-3-2-4-2l-4-3-7-4c-5-3-10-5-16-7l-16-5v-1c1 0 2 0 3-1l17-7c1-1 4-2 5-2z"></path><path d="M291 48l3-1 3 1h-1v3l-3 1h0 0v-1l1-1-1-1s-1-1-2-1z" class="M"></path><path d="M293 53c1 0 2 0 3-1l2 2h1 0c0 1 0 1 1 1l-1 1v1h0l1 1v1h-2 0l1 1v1c-1 0-1 0-2 1l-2-2c-1-1-2-2-2-3h3c-1-1-1-1 0-2-1-2-1-2-3-2zm8 10c3 3 7 5 11 8 0 2 1 3 2 4l2 1c0 1 1 2 2 3l-1 2c1 1 3 5 4 5h1l1 1c0 1 1 2 1 3 1 1 1 2 1 2v1 1 2c-2-2-4-3-6-5h1 0l1-1-1-1-2-3c-1 0-2 0-2-1h-2c0-1-1-1-2-2l1-2v-1c1 0 3 1 4 2v-1c0-2-1-2-2-3-3-1-7-5-9-7l-4-2h0 1 1c-1-1-2-1-2-2 3 2 6 4 9 7v-1l-6-5c-1 0-2-1-2-2 0 0 0-1-1-1s-1-1-1-2z" class="B"></path><path d="M313 81c1 2 4 3 5 5-1 0-2 0-2-1h-2c0-1-1-1-2-2l1-2z" class="I"></path><defs><linearGradient id="o" x1="355.05" y1="84.147" x2="340.25" y2="83.532" xlink:href="#B"><stop offset="0" stop-color="#b0aeaf"></stop><stop offset="1" stop-color="#ececec"></stop></linearGradient></defs><path fill="url(#o)" d="M364 35h1c2 0 4-1 6-2l-3 3v1c-1 1-3 3-3 4l-6 7c-1 2-2 4-2 6l-1 1c0 1-1 3-1 4 1 1 2 1 2 2l1-1v1l5-4c-2 3-4 4-4 7-2 3-2 5-3 7l-1 5h0 0c0 1 0 2 1 3l-1 1v3l1 1-1 3-3 6h0c0 1-1 1-2 2v-1c-1-2-1-7-2-8h-1l-1-1c0 1 0 2-1 2v2 4h0c2 1 2 3 2 5v6c0 5-1 9-2 13v6c-1 2-2 5-2 7 0 1-1 3-1 4 0 2 0 3-1 4s-1 1-3 2v-2h-1-1c0-3 0-6-1-9 1-2 1-3 1-4v-2-1-1c-1-1 0-2 0-4v-3l-1-7V97 86h0l-1-1 2-2v-1-2h1c-1-1-2-2-2-3h1c0-2-2-5-3-6l1-2v-2-1-3h1 0c1 0 1-4 1-5-1-1 0-2 0-3l1-4h0c0-2 1-4 2-6s3-4 4-6c2-2 5-3 8-4l1 1c0 1-1 2-2 3h0c-1 1-2 2-2 3l3-2c0 1-1 2-1 3h1 1c-1 0-2 1-2 2l5-4 3-1c2-2 4-3 6-5z"></path><path d="M347 62l1 1v-1h0 0 1 0l-2 8c0 1 0 2-1 2h0v-1l1-9z" class="U"></path><path d="M351 43h1c-1 0-2 1-2 2-1 1-2 2-3 4l-1 1s0 1-1 2h0-1-1c2-4 4-6 7-9h1z" class="m"></path><path d="M348 42l3-2c0 1-1 2-1 3-3 3-5 5-7 9 0 1-1 2-1 4-1-1-1-2-1-3l-1-1c1-4 5-7 8-10z" class="K"></path><path d="M341 125v3c2-1 3-10 3-12 1-3 1-9 3-12 0 5-1 9-2 13v6c-1 2-2 5-2 7-1 1-2 2-2 3l-1-1h-1c-1-1-1-2-1-3 3-2 1-7 2-9 1 1 1 3 1 4v1z" class="a"></path><path d="M340 52l1 1c0 1 0 2 1 3 0 1-1 3-1 4 0 2 0 4-1 6 0 5 1 9 1 14-1-3-2-6-3-8-1-4 0-8-1-11 0-1 1-3 1-4 1-2 1-3 2-5z" class="l"></path><path d="M343 39c2-2 5-3 8-4l1 1c0 1-1 2-2 3h0c-1 1-2 2-2 3-3 3-7 6-8 10-1 2-1 3-2 5h-1c0-1-1-1-1-2l1-4h0c0-2 1-4 2-6s3-4 4-6z" class="h"></path><path d="M337 51v1c3 0 4-4 5-5 2-2 3-3 4-5 1-1 2-2 4-3-1 1-2 2-2 3-3 3-7 6-8 10-1 2-1 3-2 5h-1c0-1-1-1-1-2l1-4z" class="G"></path><defs><linearGradient id="p" x1="353.939" y1="45.218" x2="358.978" y2="50.466" xlink:href="#B"><stop offset="0" stop-color="#0f0f10"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#p)" d="M364 35h1c2 0 4-1 6-2l-3 3v1c-1 1-3 3-3 4l-6 7h0c-2 0-3 1-4 2-1 2-2 3-3 5-1 1-1 3-2 4l-1 3h0-1 0 0v1l-1-1c2-9 6-15 11-22 2-2 4-3 6-5z"></path><path d="M368 36v1c-1 1-3 3-3 4l-6 7h0c-2 0-3 1-4 2 2-5 8-10 13-14z" class="R"></path><path d="M336 58c-1-1 0-2 0-3 0 1 1 1 1 2h1c0 1-1 3-1 4 1 3 0 7 1 11 1 2 2 5 3 8 1 2 1 6 3 8h0c-1 1-1 1-1 2v1 6c0 1 0 1-1 2v2h-1s-1-2 0-3l-2-2v-2c-1-3-2-6-3-10l-1 13V86h0l-1-1 2-2v-1-2h1c-1-1-2-2-2-3h1c0-2-2-5-3-6l1-2v-2-1-3h1 0c1 0 1-4 1-5z" class="C"></path><path d="M339 94c1 0 2 0 3-1v5h-1l-2-2v-2z" class="H"></path><path d="M336 58c-1-1 0-2 0-3 0 1 1 1 1 2h1c0 1-1 3-1 4v2c0 4 0 9 1 13v2c-1-1-2-4-3-6l-1-3v-2-1-3h1 0c1 0 1-4 1-5z" class="F"></path><path d="M334 66c2 2 2 3 2 5l-1 1-1-3v-2-1z" class="Z"></path><path d="M336 58c-1-1 0-2 0-3 0 1 1 1 1 2h1c0 1-1 3-1 4v2h-1v-5z" class="O"></path><defs><linearGradient id="q" x1="335.501" y1="89.489" x2="341.89" y2="81.62" xlink:href="#B"><stop offset="0" stop-color="#777577"></stop><stop offset="1" stop-color="#8f8f8e"></stop></linearGradient></defs><path fill="url(#q)" d="M334 69l1 3c1 2 2 5 3 6v-2l3 9c1 3 1 5 1 8-1 1-2 1-3 1-1-3-2-6-3-10l-1 13V86h0l-1-1 2-2v-1-2h1c-1-1-2-2-2-3h1c0-2-2-5-3-6l1-2z"></path><path d="M337 80c1 1 2 6 2 8l1 1c-2-2-3-4-4-6v-1-2h1z" class="J"></path><path d="M335 97l1-13c1 4 2 7 3 10v2l2 2c-1 1 0 3 0 3h1c0 3 0 7-1 10v10 4-1c0-1 0-3-1-4-1 2 1 7-2 9 0 1 0 2 1 3h1l1 1c0-1 1-2 2-3 0 1-1 3-1 4 0 2 0 3-1 4s-1 1-3 2v-2h-1-1c0-3 0-6-1-9 1-2 1-3 1-4v-2-1-1c-1-1 0-2 0-4v-3l-1-7V97z" class="i"></path><path d="M336 117l2 6v6c0 1 0 2 1 3h1l1 1c0-1 1-2 2-3 0 1-1 3-1 4 0 2 0 3-1 4s-1 1-3 2v-2h-1-1c0-3 0-6-1-9 1-2 1-3 1-4v-2-1-1c-1-1 0-2 0-4z" class="T"></path><path d="M339 132h1l1 1v1h-1c-1 0-1-1-1-2z" class="i"></path><path d="M336 117l2 6c-2 3-1 6-1 9 0-2-1-5-1-7v-2-1-1c-1-1 0-2 0-4z" class="E"></path><path d="M336 125c0 2 1 5 1 7s1 4 1 6h-1-1c0-3 0-6-1-9 1-2 1-3 1-4z" class="D"></path><path d="M335 97l1-13c1 4 2 7 3 10v2c-2 7 1 16 0 24 0 1 1 2 0 3v4c0-1 0-1-1-2v-4c0-1-1-3-1-5l-1-2-1-7V97z" class="f"></path><path d="M355 50c1-1 2-2 4-2h0c-1 2-2 4-2 6l-1 1c0 1-1 3-1 4 1 1 2 1 2 2l1-1v1l5-4c-2 3-4 4-4 7-2 3-2 5-3 7l-1 5h0 0c0 1 0 2 1 3l-1 1v3l1 1-1 3-3 6h0c0 1-1 1-2 2v-1c-1-2-1-7-2-8h-1l-1-1c0 1 0 2-1 2l1-16v1h0c1 0 1-1 1-2l2-8 1-3c1-1 1-3 2-4 1-2 2-3 3-5z" class="K"></path><path d="M349 88l1 1c2 1 3-1 5-2l-3 6c0-1-1-1-1-1-1-1-2-2-2-4z" class="W"></path><path d="M353 65l2-6c1 1 2 1 2 2-1 2-2 5-3 9h1l-1 1c0 1 1 1 0 1v2 1l1 1h0l-2 6V65z" class="O"></path><path d="M358 60v1l5-4c-2 3-4 4-4 7-2 3-2 5-3 7l-1 5-1-1v-1-2c1 0 0 0 0-1l1-1h-1c1-4 2-7 3-9l1-1z" class="Q"></path><path d="M346 71v1h0c1 0 1-1 1-2v5c0 2 0 4 1 6 0 1 1 2 1 4v3c0 2 1 3 2 4 0 0 1 0 1 1h0c0 1-1 1-2 2v-1c-1-2-1-7-2-8h-1l-1-1c0 1 0 2-1 2l1-16z" class="R"></path><path d="M347 75c0 2 0 4 1 6 0 1 1 2 1 4h-2V75z" class="H"></path><path d="M355 50c1-1 2-2 4-2h0c-1 2-2 4-2 6l-1 1c0 1-1 3-1 4l-2 6c0 2 0 3-1 5v12c0 1 1 3 0 4-1 0-2-1-3-2V66c0-3 1-5 1-7 1-1 1-3 2-4 1-2 2-3 3-5z" class="Y"></path><path d="M355 50c1-1 2-2 4-2h0c-1 2-2 4-2 6l-1 1v-2h0v-1c-1 2-3 2-4 3 1-2 2-3 3-5z" class="E"></path><path d="M393 54c7 0 13 2 20 4v1l-11 4c3 0 4 1 7 0 1 0 2 0 3 1h1c2 0 4-2 6-1-6 5-12 8-18 13-1 1-2 3-4 4-1 2-2 4-3 5-2 1-4 1-6 2-1 0-2 0-3 1h0c-1 1-1 1-3 1h-2c1 1 2 1 4 1h-1v1 1c1 1 5 0 7 0l-4 1c-1 0-3-1-5 0-2 0-4 1-6 1h-2c-4 2-9 4-12 7-2 0-3 0-4-1l-2 2v-1c-2 1-2 3-3 4 0 1-1 3-2 4v-1-2c-1 0-1 0-1-1l-1-2c-1-1-1-4-1-5 0-2 0-4-2-5h0v-4-2c1 0 1-1 1-2l1 1h1c1 1 1 6 2 8v1c1-1 2-1 2-2h0l3-6 1-3 2-4c0-1 1-2 2-2 0-1 1-2 2-4 0-1 2-2 3-3 2-2 4-4 6-5l2-1c1-1 2-1 2-2 1-1 2-2 3-2l6-3c2 0 3-1 5-2h0-3l3-1h-1 0c1-2 3-1 5-1z" class="i"></path><path d="M373 91c4-1 8-3 12-3h0c-1 1-1 1-3 1h-2c1 1 2 1 4 1h-1c-2 0-8 2-9 1h-1z" class="B"></path><path d="M384 71l-2 3v1l-4 2c2 0 5-1 7-1-2 1-3 1-5 2h-1c-1 0-1 0-2 1-1 0-2 1-3 1v-1l1-2h0l9-6z" class="J"></path><path d="M383 90v1 1c1 1 5 0 7 0l-4 1c-1 0-3-1-5 0-2 0-4 1-6 1h-2c-4 2-9 4-12 7-2 0-3 0-4-1 4-4 10-7 16-9h1c1 1 7-1 9-1z" class="F"></path><path d="M380 78l1 3h1c1-1 2-1 4-1 0-1 0-1 1-1h0c1 1 1 1 2 0h0l3-1h2 0c1 0 1 0 2-1v1c-1 1-2 2-3 2h0c-2 0-3 1-5 2-1 1-3 1-5 2l-7 1h0c-1-1-2 0-3-1v-1l1-1h0v-2h-1v-1-1l2-1-1 2v1c1 0 2-1 3-1 1-1 1-1 2-1h1z" class="C"></path><path d="M373 78l2-1-1 2v1c1 0 2-1 3-1 1-1 1-1 2-1-2 2-4 3-5 5 1 1 3 1 4 1v-1l1 1h4l-7 1h0c-1-1-2 0-3-1v-1l1-1h0v-2h-1v-1-1z" class="K"></path><path d="M372 71c1 0 2-1 2-1l5-2c1 0 3 0 4 1l-7 4c-1 2-1 2-2 3 0 1 1 1 1 1h0l-2 1v1 1h1v2h0l-1 1v1c1 1 2 0 3 1h0l7-1c2-1 4-1 5-2 2-1 3-2 5-2-4 3-8 4-12 6-8 2-16 4-22 9v-2c1-1 3-2 3-4-1 0-2 1-3 1l-1-1 3-5 2-3c1-2 3-4 5-6l4-4z" class="c"></path><path d="M374 76c0 1 1 1 1 1h0l-2 1h-1 0l2-2z" class="k"></path><path d="M361 84l2-3v1l2 1c-1 1-2 3-4 3v-2z" class="E"></path><path d="M371 81l2-2v1h1v2h0l-1 1v1c1 1 2 0 3 1h0l-5 2-3 2c-1-2 0-4 0-5v-1l3-2z" class="L"></path><path d="M371 81h1l1 1c-1 1-3 3-5 4v1c1 1 2 0 3 0l-3 2c-1-2 0-4 0-5v-1l3-2z" class="P"></path><path d="M372 71c1 0 2-1 2-1l5-2c1 0 3 0 4 1l-7 4c-4 2-8 6-11 10l-2-1v-1c1-2 3-4 5-6l4-4z" class="i"></path><path d="M371 66h1c1 0 3-1 5-1v1l-7 4v1h1 1l-4 4c-2 2-4 4-5 6l-2 3-3 5 1 1c1 0 2-1 3-1 0 2-2 3-3 4v2l-4 6c-2 1-2 3-3 4 0 1-1 3-2 4v-1-2c-1 0-1 0-1-1l-1-2c-1-1-1-4-1-5 0-2 0-4-2-5h0v-4-2c1 0 1-1 1-2l1 1h1c1 1 1 6 2 8v1c1-1 2-1 2-2h0l3-6 1-3 2-4c0-1 1-2 2-2 0-1 1-2 2-4 0-1 2-2 3-3 2-2 4-4 6-5z" class="I"></path><path d="M365 72l2-2 4-2c-1 1-1 2-2 3l-2 2v1h0v-2h-2z" class="e"></path><path d="M345 89h1c1 0 1 1 1 2 1 0 1 1 1 1 1 2 1 4 2 5 1-1 2-2 2-3 1-2 2-3 3-4l1-2-4 10c0 1-1 2-1 3s1 3 1 4-1 3-2 4v-1-2c-1 0-1 0-1-1l-1-2c-1-1-1-4-1-5 0-2 0-4-2-5h0v-4z" class="F"></path><defs><linearGradient id="r" x1="355.885" y1="86.577" x2="361.194" y2="88.109" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#313232"></stop></linearGradient></defs><path fill="url(#r)" d="M365 72h2v2h0v-1l1 2c-2 2-4 4-5 6l-2 3-3 5 1 1c1 0 2-1 3-1 0 2-2 3-3 4v2l-4 6c-2 1-2 3-3 4 0-1-1-3-1-4s1-2 1-3l4-10c3-5 5-12 9-16z"></path><path d="M358 89l1 1c1 0 2-1 3-1 0 2-2 3-3 4v2l-4 6v-2s3-9 3-10z" class="Z"></path><defs><linearGradient id="s" x1="408.905" y1="51.729" x2="379.463" y2="65.955" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2b2c"></stop></linearGradient></defs><path fill="url(#s)" d="M393 54c7 0 13 2 20 4v1l-11 4c-5 2-9 4-14 6l-4 2-9 6s-1 0-1-1c1-1 1-1 2-3l7-4c-1-1-3-1-4-1l-5 2s-1 1-2 1h-1-1v-1l7-4v-1c-2 0-4 1-5 1h-1l2-1c1-1 2-1 2-2 1-1 2-2 3-2l6-3c2 0 3-1 5-2h0-3l3-1h-1 0c1-2 3-1 5-1z"></path><defs><linearGradient id="t" x1="381.215" y1="73.821" x2="376.379" y2="72.468" xlink:href="#B"><stop offset="0" stop-color="#4a4a4b"></stop><stop offset="1" stop-color="#676565"></stop></linearGradient></defs><path fill="url(#t)" d="M383 69l3-2-1 1c0 1-2 2-3 3h0 1 0 1v-1h2v-1h0 2l-4 2-9 6s-1 0-1-1c1-1 1-1 2-3l7-4z"></path><path d="M389 55h1c1 1 3 1 5 1l-6 1v4c-3 2-7 4-10 6 3 0 5-1 8-1h0l-1 1-3 2c-1-1-3-1-4-1l-5 2s-1 1-2 1h-1-1v-1l7-4v-1c-2 0-4 1-5 1h-1l2-1c1-1 2-1 2-2 1-1 2-2 3-2l6-3c2 0 3-1 5-2h0-3l3-1z" class="c"></path><path d="M373 65c1-1 2-1 2-2 1-1 2-2 3-2l6-3c-1 1-1 3-2 4-2 1-4 1-6 3h-3z" class="W"></path><path d="M550 149h2c2 0 5 3 7 5v-1h1c0 1 0 2-1 2l-2 1c0 1 0 2 1 3h0c1 1 1 2 1 2v-2h2v1c1 1 0 1 1 1v1h2c0-1-1-2-2-4h1 1l1-1h0 1v1h-1c2 4 6 8 9 12 1 0 1 0 1 1l1 1c1 1 2 2 4 3l3 3h-2-1c0 2 2 5 3 7h-1 0v2c3 7 5 14 6 21 0 9-2 18-5 27l-1 2c-1 4-1 8-1 13-1 4-2 10-3 14 0 2-1 5-2 7l-2 2c-1 2-3 4-5 7v-1l-1-1-1 2c-3 4-7 8-10 11h-1 0l2-2-2-1c0-1 1-2 1-3l2-3h1c1 0 5-6 5-7 1-1 1-1 2-1l1-3-3 1 2-2-1-1h0c0-1 1-1 1-2s1-1 1-2c1-1 1-2 2-3-1-1-1-2 0-3l1-7c1 0 3-1 4-2l1-6v-2c1-7 2-15 0-23-1-10-6-19-12-28l-8-8-2-1v-4c-1-3-1-8-2-12l-2-17z" class="Y"></path><path d="M567 270l5-9v3c-1 2-3 5-4 7l-3 1 2-2z" class="K"></path><path d="M556 183c1 0 2 0 3 1 2 1 4 3 4 5h1v2l-8-8z" class="T"></path><path d="M570 190v-1l1 1c0 1 0 1 1 2h0v1s0 1 1 1h0l-1-2v-2l-2-2h1l2 3 2 7 2 4c0 3 2 7 1 9l-3-9c-1-4-3-7-4-10l-1-2z" class="B"></path><path d="M577 201c1 0 1 1 1 2v-1s0-1 1-1v1c0 3 1 6 1 10v2 1l1 1v3c0 1 1 4 0 5 0 0 0 1-1 2 0-4 0-7-1-10l-1-3v-2c1-2-1-6-1-9v-1z" class="T"></path><defs><linearGradient id="u" x1="571.512" y1="251.651" x2="572.16" y2="261.677" xlink:href="#B"><stop offset="0" stop-color="#696a69"></stop><stop offset="1" stop-color="#7e7c7e"></stop></linearGradient></defs><path fill="url(#u)" d="M571 252c1 0 3-1 4-2-1 4-2 8-3 11l-5 9-1-1h0c0-1 1-1 1-2s1-1 1-2c1-1 1-2 2-3-1-1-1-2 0-3l1-7z"></path><path d="M567 274h-1v1c0 1 0 1 1 1h0c2-1 3-3 4-5l3-3c-2 4-3 7-6 10l-1 2c-3 4-7 8-10 11h-1 0l2-2-2-1c0-1 1-2 1-3l2-3h1c1 0 5-6 5-7 1-1 1-1 2-1z" class="T"></path><path d="M550 149h2c2 0 5 3 7 5v-1h1c0 1 0 2-1 2l-2 1c0 1 0 2 1 3h0c1 1 1 2 1 2v1l-1 1v-2 1 1c3 5 4 11 8 16l5 9h-1l2 2v2l1 2h0c-1 0-1-1-1-1v-1h0c-1-1-1-1-1-2l-1-1v1l1 2h0l-1 1h0l-1-1c-1 0-1 0-1-1l-2-2c-2-3-5-5-7-7-1-1-2-1-3-2-1 0-1-1-2-2-1-3-1-8-2-12l-2-17z" class="F"></path><path d="M552 166c3 6 9 10 12 15 2 2 4 6 6 9l1 2h0l-1 1h0l-1-1c-1 0-1 0-1-1l-2-2c-2-3-5-5-7-7-1-1-2-1-3-2-1 0-1-1-2-2-1-3-1-8-2-12z" class="C"></path><path d="M565 157h0 1v1h-1c2 4 6 8 9 12 1 0 1 0 1 1l1 1c1 1 2 2 4 3l3 3h-2-1c0 2 2 5 3 7h-1 0v2c3 7 5 14 6 21 0 9-2 18-5 27l-1 2v-4-3c-1-1-1-4-1-5 1-1 1-4 1-5h1v-2c-1-1-1 0-1-1 0-2-1-3-2-5 0-4-1-7-1-10v-1c-1 0-1 1-1 1v1c0-1 0-2-1-2v1l-2-4-2-7-2-3-5-9c-4-5-5-11-8-16v-1-1 2l1-1v-1-2h2v1c1 1 0 1 1 1v1h2c0-1-1-2-2-4h1 1l1-1z" class="E"></path><path d="M575 171l1 1c1 1 2 2 4 3l3 3h-2-1c0 2 2 5 3 7h-1 0v2c-1-1-1-3-2-5-2-4-3-7-5-11h0z" class="j"></path><defs><linearGradient id="v" x1="571.44" y1="188.521" x2="581.837" y2="192.353" xlink:href="#B"><stop offset="0" stop-color="#9a999a"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#v)" d="M571 179c2 2 4 6 5 8h1v-1c1 1 1 2 2 3l3 9-3-1h0v2s0-1-1-1v-1c-1 1-1 2-1 4v1l-2-4-2-7c-1-1 0-3 0-5-1-1-1-3-2-5v-2z"></path><path d="M573 186c1 2 3 6 3 9-1 1-1 2-1 3l-2-7c-1-1 0-3 0-5z" class="D"></path><defs><linearGradient id="w" x1="563.132" y1="167.971" x2="572.045" y2="166.865" xlink:href="#B"><stop offset="0" stop-color="#6a6a6a"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#w)" d="M565 157h0 1v1h-1c2 4 6 8 9 12 1 0 1 0 1 1h0c-3-2-5-5-8-7l2 2c1 3 3 4 5 6 1 2 1 3 2 5 1 1 2 3 2 4s0 1 1 2h0v2c-1-1-1-1-1-2l-1-1v-1c-1-1-2-2-2-3-1-1 0-1-1-1 0-1-1-1-1-1-1-2-2-4-4-5-1-2-3-3-4-4 0-1-2-3-3-4 0-1-1-2-1-3 1 1 0 1 1 1v1h2c0-1-1-2-2-4h1 1l1-1z"></path><path d="M559 159h2v1c0 1 1 2 1 3 1 1 3 3 3 4 1 1 3 2 4 4-1 1-1 1-1 2v1c1 1 2 3 3 5v2c1 2 1 4 2 5 0 2-1 4 0 5l-2-3-5-9c-4-5-5-11-8-16v-1-1 2l1-1v-1-2z" class="R"></path><path d="M565 167c1 1 3 2 4 4-1 1-1 1-1 2v1h0c-1-2-3-3-3-5h0v-2z" class="D"></path><path d="M559 159h2v1h-1c0 1 1 3 1 4-1 1-1 2 0 4 0 0 1 0 1 1 1 1 2 3 3 5h-1l-1-1c1 1 2 4 3 6-4-5-5-11-8-16v-1-1 2l1-1v-1-2z" class="I"></path><path d="M577 201c0-2 0-3 1-4v1c1 0 1 1 1 1v-2h0l3 1c0 1 1 2 1 2l1 3c1 3 3 9 2 12-1 2 0 4-1 5l-1 8c0 1 0 0-1 1v1l-1 3v-3c-1-1-1-4-1-5 1-1 1-4 1-5h1v-2c-1-1-1 0-1-1 0-2-1-3-2-5 0-4-1-7-1-10v-1c-1 0-1 1-1 1v1c0-1 0-2-1-2z" class="C"></path><defs><linearGradient id="x" x1="433.872" y1="234.139" x2="440.576" y2="244.876" xlink:href="#B"><stop offset="0" stop-color="#2c2b2b"></stop><stop offset="1" stop-color="#525253"></stop></linearGradient></defs><path fill="url(#x)" d="M415 225h1 3 9 4l1 1h2 0c1 0 1 0 2 1 1 0 0-1 1-1h2c2-1 4-1 6-1l1 1-1 1h0c-3 8-7 15-12 22 0 2-1 3-2 4-2 2-2 2-3 4h3 0c-1 3-3 5-5 7-1 1-3 2-3 3l-6 5c0-1 1-2 0-3l-1-1-2 3h-1l-1 1h-1v1c-1 1-1 2-2 3h0l-1 2c0 2-1 2-1 4 0 1-1 2-1 3-1 1-1 2-1 3l-2 2c-1 0-1 1-1 2-1 1-1 2-2 3v-2h-1c0-1 0-2-1-2v1l-1 1-1-1-1 1h0v-1-2h0l2-3c1-2 1-4 2-6 0-1 0-2 1-3v-1c-1 0-1-1-2-2h-2v1 2c0-2 0-5-1-7 0-1 0-3-1-4v-1-1h1v-2l1-1c0-1-1-2-1-4 1 0 1 0 2-1 0 0 1-1 1-2l2-2v-2h-1c-1 1-2 2-3 2 0-1-3-3-4-5v-1c0-1-1-1 0-2 1-2 6-5 8-5 1 0 2 0 3-1 1-2 1-4 2-5v-4c3 0 6-2 8-4v-1h1z"></path><path d="M419 261c1-1 1-1 2-1l1 1-4 5c-1 0 0 0-1-1 1-1 2-2 2-4z" class="H"></path><path d="M428 249c2-2 3-4 4-7l-1 7c-2 2-3 5-5 7v-3l2-4z" class="L"></path><path d="M418 260l1 1c0 2-1 3-2 4v2 1h0l-2 3h-1l1-5-1-1c2 0 2-1 3-2l1-3z" class="O"></path><path d="M424 261l10-12c0 2-1 3-2 4-2 2-2 2-3 4s-3 3-3 4h-2z" class="J"></path><path d="M442 227h2s-1 0-1 1c-3 2-4 8-6 11-2 4-4 7-6 10l1-7 1-1c2-2 2-4 3-6l1-1c0-1 1-1 1-2l1-1c0-1 1-2 1-3 1 0 2-1 2-1z" class="P"></path><path d="M429 257h3 0c-1 3-3 5-5 7-1 1-3 2-3 3l-6 5c0-1 1-2 0-3l-1-1h0l4-4s2-3 3-3h2c0-1 2-2 3-4z" class="l"></path><path d="M421 264s2-3 3-3h2c-1 2-2 4-4 5h0c-1 0-1-1 0-2h-1z" class="C"></path><path d="M425 240c1 0 1 1 2 1l-2 3 3-1c0 1-1 2-1 4 1 0 1-1 2-1l-1 3-2 4v3l-3 3-1 2-1-1c-1 0-1 0-2 1l-1-1c0-1 1-3 1-4l2-7c0-1 0-1 1-1v-4h1c1 0 1-1 1-2l1-2z" class="T"></path><path d="M423 253h0l3-6h0v3c-1 1-1 1-1 3h1v3l-3 3c0-1 0-1-1-2 1-2 1-3 1-4z" class="K"></path><path d="M425 240c1 0 1 1 2 1l-2 3c-1 4-3 8-5 12 2-1 2-2 3-3 0 1 0 2-1 4 1 1 1 1 1 2l-1 2-1-1c-1 0-1 0-2 1l-1-1c0-1 1-3 1-4l2-7c0-1 0-1 1-1v-4h1c1 0 1-1 1-2l1-2z" class="N"></path><path d="M422 257c1 1 1 1 1 2l-1 2-1-1c0-1 1-2 1-3z" class="L"></path><path d="M417 235l2 2h1v-1c1 1 1 2 1 3l1 1c1 0 1 0 2-1v3c0 1 0 2-1 2h-1v4c-1 0-1 0-1 1l-2 7c0 1-1 3-1 4l-1 3c-1 1-1 2-3 2l1 1-1 5-1 1h-1v1l-1-5h-1c0-2 1-8 0-10h-1v-2s1-2 1-3c-1 0-1 0-2-1l1-1h1v-3-2h1 1c0-2 0-4 1-5 0-1 0-1 1-2 0-1 0-2 1-2l2-2z" class="R"></path><path d="M411 258v-10h1l1 1v2h0c0 1-1 1-1 2-1 2 0 3 0 5h-1z" class="D"></path><g class="J"><path d="M416 256l1 1 2-1c0 1-1 3-1 4l-1 3c-1-1-1-2-1-2v-5z"></path><path d="M413 251c1 1 1 2 1 3-1 2 0 5 1 7 0-1 0 0 1 0 0 0 0 1 1 2-1 1-1 2-3 2l1 1-1 5-1 1h-1v1l-1-5h-1c0-2 1-8 0-10h-1v-2l2 2h0 1c0-2-1-3 0-5 0-1 1-1 1-2z"></path></g><path d="M415 261c0-1 0 0 1 0 0 0 0 1 1 2-1 1-1 2-3 2l1-4z" class="I"></path><path d="M409 256l2 2h0 1v8c-1 0 0 1 0 2h0c1 0 1-1 1-2h2l-1 5-1 1h-1v1l-1-5h-1c0-2 1-8 0-10h-1v-2z" class="c"></path><path d="M420 236c1 1 1 2 1 3l1 1c1 0 1 0 2-1v3c0 1 0 2-1 2h-1v4c-1 0-1 0-1 1l-2 7-2 1-1-1c0-2-1-5-2-8v-1c0-1 0-4 1-5h0c1-2 2-2 4-3v-2h1v-1z" class="C"></path><path d="M418 244h1 0c0 3 0 6-1 9-1 0-1-1-2-1 0-4 0-5 2-8z" class="i"></path><path d="M420 236c1 1 1 2 1 3l1 1c1 0 1 0 2-1v3c0 1 0 2-1 2h-1v4c-1 0-1 0-1 1 0-4-1-7-2-10v-2h1v-1z" class="O"></path><path d="M422 240c1 0 1 0 2-1v3c0 1 0 2-1 2h-1v-4z" class="E"></path><path d="M415 225h1 3 9 4l1 1h2 0c1 0 1 0 2 1 1 0 0-1 1-1h2c2-1 4-1 6-1l1 1-1 1v-1l-2 1h-2s-1 1-2 1c0 1-1 2-1 3l-1 1c0 1-1 1-1 2l-1 1c-1 2-1 4-3 6l-1 1c-1 3-2 5-4 7l1-3c-1 0-1 1-2 1 0-2 1-3 1-4l-3 1 2-3c-1 0-1-1-2-1l-1 2v-3c-1 1-1 1-2 1l-1-1c0-1 0-2-1-3v-1s-1-3-1-4v-2h-1 0c0-1 0-1-1-2h-1c0-1 0-1-1-2z" class="K"></path><path d="M433 241c1-4 2-9 4-12 2-2 3-2 5-2 0 0-1 1-2 1 0 1-1 2-1 3l-1 1c0 1-1 1-1 2l-1 1c-1 2-1 4-3 6z" class="D"></path><path d="M432 225l1 1h2c-2 3-2 6-2 9l-3 7c0 1-1 2-1 4-1 0-1 1-2 1 0-2 1-3 1-4 1-5 5-10 5-16-1-1-1-1-1-2h0z" class="C"></path><path d="M428 225h4 0c0 1 0 1 1 2 0 6-4 11-5 16l-3 1 2-3c-1 0-1-1-2-1l-1 2v-3s1-2 1-3l1-4v-2c1 0 1-1 1-1 0-2 0-2 1-3v-1z" class="K"></path><path d="M431 226h0c1 5-3 10-4 15-1 0-1-1-2-1 2-4 3-9 6-14z" class="X"></path><path d="M428 226h3c-3 5-4 10-6 14l-1 2v-3s1-2 1-3l1-4v-2c1 0 1-1 1-1 0-2 0-2 1-3z" class="L"></path><path d="M415 225h1 3 9v1c-1 1-1 1-1 3 0 0 0 1-1 1v2l-1 4c0 1-1 3-1 3-1 1-1 1-2 1l-1-1c0-1 0-2-1-3v-1s-1-3-1-4v-2h-1 0c0-1 0-1-1-2h-1c0-1 0-1-1-2z" class="E"></path><path d="M421 233v2h0 2v-1-1 2h1c1-1 0-3 1-4l1 1-1 4c0 1-1 3-1 3-1 1-1 1-2 1l-1-1c0-1 0-2-1-3v-1s0-1 1-2z" class="J"></path><path d="M421 239l3-3v-1h1v1c0 1-1 3-1 3-1 1-1 1-2 1l-1-1z" class="C"></path><path d="M415 225h1 3v1c1 0 2-1 3 0h1v2c0 1 0 2-1 2 0 1-1 2-1 3-1 1-1 2-1 2s-1-3-1-4v-2h-1 0c0-1 0-1-1-2h-1c0-1 0-1-1-2z" class="L"></path><path d="M415 225c1 1 1 1 1 2h1c1 1 1 1 1 2h0 1v2c0 1 1 4 1 4v1 1h-1l-2-2-2 2c-1 0-1 1-1 2-1 1-1 1-1 2-1 1-1 3-1 5h-1-1v2 3h-1l-1 1h-1l-1-2h-1c-1 1-2 2-2 3-1 0 0 0-1 1l-1-1v-2h-1c-1 1-2 2-3 2 0-1-3-3-4-5v-1c0-1-1-1 0-2 1-2 6-5 8-5 1 0 2 0 3-1 1-2 1-4 2-5v-4c3 0 6-2 8-4v-1h1z" class="h"></path><path d="M399 250c0-2-1-3 0-5 0 0 1 0 2-1l-1 2v2 1l-1 1z" class="G"></path><path d="M393 248c1 0 3 0 3 1 1 0 1 1 2 1h1l1-1c1 1 1 1 0 2s-2 2-3 2c0-1-3-3-4-5z" class="g"></path><path d="M414 226l-1 3c-2 2-5 2-7 5v-4c3 0 6-2 8-4z" class="b"></path><path d="M407 241l1 3h1l-1 1h0-3c-1 2-2 4-4 6h-1c1-1 1-1 0-2v-1c2-1 3-3 4-5 1 0 2-1 3-1v-1z" class="Q"></path><path d="M411 236l3-3v-2h1l2 2c-1 1-2 1-2 2-1 1-2 1-3 3h0c-1 2-1 5-3 6h0-1l-1-3h0c1-1 2-1 2-2 0 0 0-1 1-1 0-1 1-1 1-2z" class="M"></path><path d="M407 241h0c1-1 2-1 2-2 0 0 0-1 1-1 0-1 1-1 1-2v3c-1 1-2 1-2 2v1 2h0-1l-1-3z" class="d"></path><path d="M409 244c2-1 2-4 3-6h0c1-2 2-2 3-3 0-1 1-1 2-2v2l-2 2c-1 0-1 1-1 2-1 1-1 1-1 2-1 1-1 3-1 5h-1-1v2 3h-1l-1 1h-1l-1-2h-1c-1 1-2 2-2 3-1 0 0 0-1 1l-1-1v-2c2-2 3-4 4-6h3 0l1-1h0z" class="B"></path><path d="M405 250l1-2c1 0 2 1 3 2v1l-1 1h-1l-1-2h-1z" class="Q"></path><path d="M402 254c1-1 0-1 1-1 0-1 1-2 2-3h1l1 2h1c1 1 1 1 2 1 0 1-1 3-1 3v2h1c1 2 0 8 0 10h1l1 5c-1 1-1 2-2 3h0l-1 2c0 2-1 2-1 4 0 1-1 2-1 3-1 1-1 2-1 3l-2 2c-1 0-1 1-1 2-1 1-1 2-2 3v-2h-1c0-1 0-2-1-2v1l-1 1-1-1-1 1h0v-1-2h0l2-3c1-2 1-4 2-6 0-1 0-2 1-3v-1c-1 0-1-1-2-2h-2v1 2c0-2 0-5-1-7 0-1 0-3-1-4v-1-1h1v-2l1-1c0-1-1-2-1-4 1 0 1 0 2-1 0 0 1-1 1-2l2-2 1 1z" class="O"></path><path d="M407 275l2-2h1l-1 2c-2 3-5 5-8 7v-1c1-3 4-4 6-6z" class="N"></path><path d="M410 268h1l1 5c-1 1-1 2-2 3 0-1 0-1-1-1l1-2h-1l-2 2h-1c0-1 1-2 1-2 2-2 3-3 3-5z" class="b"></path><path d="M399 272c1 0 1 0 1 1s0 2 1 2h0 3v-1c0 1 1 1 1 1h1 1c-2 2-5 3-6 6h-1c0-1 0-2 1-3v-1c-1 0-1-1-2-2h-1 0v-1c0-1 1-1 1-2h0z" class="V"></path><path d="M404 283l5-5c0 2-1 2-1 4 0 1-1 2-1 3-1 1-1 2-1 3l-2 2c-1 0-1 1-1 2-1 1-1 2-2 3v-2h-1c0-1 0-2-1-2v1l-1 1-1-1-1 1h0v-1-2h0l2-3 6-4z" class="l"></path><path d="M404 283l1 1v1l-6 6v1l-1 1-1-1-1 1h0v-1-2h0l2-3 6-4z" class="i"></path><path d="M409 258h1c1 2 0 8 0 10s-1 3-3 5c0 0-1 1-1 2h-1s-1 0-1-1v1h-3l1-1h0c-1-1 0-3-1-5l1-1c1 0 2-1 2-1h1l2-3c0-1 0-1 1-2s1-2 1-4z" class="P"></path><path d="M407 264l1 1v1c-1 1-1 2-3 3v-2l2-3z" class="K"></path><path d="M401 269l1-1v5l1 1c1-1 2-2 2-3 1 0 1 0 1 1l1 1s-1 1-1 2h-1s-1 0-1-1v1h-3l1-1h0c-1-1 0-3-1-5z" class="H"></path><path d="M409 258h1c1 2 0 8 0 10s-1 3-3 5l-1-1c0-2 1-2 2-3 1 0 1-1 1-2l-1-1v-1l-1-1c0-1 0-1 1-2s1-2 1-4z" class="D"></path><path d="M396 263l1-1c0-1-1-2-1-4 1 0 1 0 2-1 0 1 1 1 2 1 0 1-1 1-2 2 1 2 2 3 3 5h1c2 1 2 1 2 2 0 0-1 1-2 1l-1 1c1 2 0 4 1 5h0l-1 1h0c-1 0-1-1-1-2s0-1-1-1h0c0 1-1 1-1 2v1h0 1-2v1 2c0-2 0-5-1-7 0-1 0-3-1-4v-1-1h1v-2z" class="Q"></path><path d="M401 265h1c2 1 2 1 2 2 0 0-1 1-2 1l-1 1-1 1-1-2h2c1-1 0-2 0-3z" class="B"></path><path d="M399 272v-1c-1-1-1-3 0-4v1l1 2 1-1c1 2 0 4 1 5h0l-1 1h0c-1 0-1-1-1-2s0-1-1-1z" class="c"></path><path d="M402 254c1-1 0-1 1-1 0-1 1-2 2-3h1l1 2h1c1 1 1 1 2 1 0 1-1 3-1 3v2c0 2 0 3-1 4s-1 1-1 2l-2 3h-1c0-1 0-1-2-2h-1c-1-2-2-3-3-5 1-1 2-1 2-2-1 0-2 0-2-1 0 0 1-1 1-2l2-2 1 1z" class="D"></path><path d="M402 261c0-1 1-1 1-2l1 2c0 1-1 1-2 2v-2z" class="k"></path><path d="M403 254l1 1 1 1 1-2c-1 2-1 4-3 5 0 1-1 1-1 2h0l-1-1v-3l2-3z" class="F"></path><path d="M401 257v3l1 1h0v2 1c1 1 1 1 2 1h-2-1c-1-2-2-3-3-5 1-1 2-1 2-2l1-1z" class="U"></path><path d="M407 252h1c1 1 1 1 2 1 0 1-1 3-1 3v2c0 2 0 3-1 4s-1 1-1 2l-2 3h-1c0-1 0-1-2-2h2l3-3c0-1 1-2 1-3 0-2-1-4-1-6v-1z" class="e"></path><path d="M402 254c1-1 0-1 1-1 0-1 1-2 2-3h1l1 2v1c-1 0-1 0-1 1l-1 2-1-1-1-1-2 3-1 1c-1 0-2 0-2-1 0 0 1-1 1-2l2-2 1 1z" class="M"></path><path d="M401 253l1 1-2 2-1-1 2-2z" class="O"></path><path d="M403 254c1-1 2-1 2-3 0 0 1 0 1-1l1 2v1c-1 0-1 0-1 1l-1 2-1-1-1-1z" class="b"></path><path d="M95 176h0v2l-1 1v1c0 2 0 3 1 6l2 2c0 1 0 1 1 2l3 5c3 1 6 2 10 1h1s1 0 2-1h1s1-1 2-1c1-1 1-2 2-3v5h-1l-1 3h0 1c1 2 0 6 0 8v6c0 4-1 7 0 11v3c-1 2-1 4-2 5v2l-1 2v3c0 1 0 3-1 4v12l3 5c1 3 1 7 1 10v11h0 0-1c-1 1-1 2-1 3-1-2-2-3-3-4h-1c-1-1-1-2-2-4l-1 1c1 1 1 3 2 4h-1l-1-1v-2c-1-1-2-2-2-3s-1-3-1-4h-1v3 1l-1-1c-1-1-1-2-2-3 1 5 2 9 5 13l3 5c1 2 3 5 3 7 0 0-1 1-1 2l-1 2v2 3c0-2 0-3-1-4-5-6-8-11-11-18-1-1-1-1-1-2h0c-1-1-1-2-2-3 0-2 0-3-1-5h0l-1-4v-1c0-1 0-7 1-7v-2h-2c0 1 0 2-1 4l-2 1v-1c0-2 1-5 2-8-1 0-1 1-1 1-1 1 0 1-1 2v1h-1v-1-2h0c-1 0-1 1-2 2h-1 0-1c1-2 3-5 4-7 2-4 3-8 4-12l8-22c2-4 3-8 6-12-3-1-6-2-9-4-4-3-6-7-7-12s1-9 4-13z" class="h"></path><path d="M105 263c0-1 0-3 1-4 1 1 1 2 1 3l-2 1z" class="j"></path><path d="M105 263l2-1c0 3 1 5 1 7v-2-1c1 0 1-1 1-2h0c3 2 4 7 5 10h0c-1-1-1-2-1-3-1-1-2-4-3-5-2 2 0 5-1 6-1-1-1-4-3-5-1-1-1-2-2-3l1-1z" class="G"></path><path d="M94 179v1c0 2 0 3 1 6l2 2c0 1 0 1 1 2l3 5c-2-1-3-2-4-3-1-2-2-3-3-4l-1 1c-1-3 0-7 1-10z" class="a"></path><path d="M109 272c1-1-1-4 1-6 1 1 2 4 3 5 0 1 0 2 1 3v1l-2 3 1 2h-1c-1-1-1-2-2-4h0c-1-1-1-2-1-4z" class="V"></path><path d="M112 278c-1-2-2-4-2-7h3c0 1 0 2 1 3v1l-2 3z" class="O"></path><path d="M114 274l1 1v-2h-1c0-1 0-2 1-3h2 1v11h0 0-1c-1 1-1 2-1 3-1-2-2-3-3-4l-1-2 2-3v-1h0z" class="e"></path><path d="M112 278l2-3c1 2 2 5 4 6h0 0-1c-1 1-1 2-1 3-1-2-2-3-3-4l-1-2z" class="H"></path><defs><linearGradient id="y" x1="107.175" y1="257.516" x2="102.953" y2="275.214" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#y)" d="M100 253c0-2 0-3 1-4h1v1c0 1 1 2 1 3v1 2c0 3 1 6 1 8 1 1 1 2 2 3 2 1 2 4 3 5 0 2 0 3 1 4h0l-1 1c1 1 1 3 2 4h-1l-1-1v-2c-1-1-2-2-2-3s-1-3-1-4h-1v3 1l-1-1c0-1 0-2-1-3v-3c0-1-1-1-1-2h0c-1-2-1-4-1-6v-5h-1v-2z"></path><path d="M100 253c0-2 0-3 1-4h1v1 8l-1 2v-5h-1v-2z" class="G"></path><path d="M93 189l1-1c1 1 2 2 3 4 1 1 2 2 4 3 3 1 6 2 10 1h1s1 0 2-1h1s1-1 2-1c1-1 1-2 2-3v5h-1c-1 0-2 1-3 2-2 1-4 1-6 2-3 1-6 0-10-2-3-2-5-4-6-9z" class="f"></path><defs><linearGradient id="z" x1="90.965" y1="239.628" x2="97.418" y2="259.488" xlink:href="#B"><stop offset="0" stop-color="#656568"></stop><stop offset="1" stop-color="#8b8888"></stop></linearGradient></defs><path fill="url(#z)" d="M92 255c2-6 2-12 5-18 1-1 1-4 3-5l-2 10v10c-1 3-1 6-1 8h0c-1 0-1 0-2-1h0-2c0 1 0 2-1 4l-2 1v-1c0-2 1-5 2-8z"></path><path d="M93 259h0c0-2 1-5 2-6h1l-1 6h0-2z" class="P"></path><path d="M96 253c0-1 1-7 1-8 1-1 1-2 1-3v10c-1 3-1 6-1 8h0c-1 0-1 0-2-1l1-6z" class="b"></path><defs><linearGradient id="AA" x1="96.211" y1="277.132" x2="109.393" y2="292.376" xlink:href="#B"><stop offset="0" stop-color="#333335"></stop><stop offset="1" stop-color="#5e5d5a"></stop></linearGradient></defs><path fill="url(#AA)" d="M98 252v4c1 6-1 13 1 19 0 1-1 2 0 3 0-1 1-5 1-6h-1c0-1 1-2 0-3 0-2 1-7 0-8 0-2-1-3 0-5v-1-1s0-1 1-1v2h1v5c0 2 0 4 1 6h0c0 1 1 1 1 2v3c1 1 1 2 1 3-1-1-1-2-2-3 1 5 2 9 5 13l3 5c1 2 3 5 3 7 0 0-1 1-1 2l-1 2v2 3c0-2 0-3-1-4-5-6-8-11-11-18-1-1-1-1-1-2h0c-1-1-1-2-2-3 0-2 0-3-1-5h0l-1-4v-1c0-1 0-7 1-7v-2h0c1 1 1 1 2 1h0c0-2 0-5 1-8z"></path><path d="M101 277c1 1 1 3 2 5v1 3c-1-1-1-2-2-3v-6z" class="b"></path><path d="M95 259c1 1 1 1 2 1h0v14-1c-1-5-2-8-2-12v-2h0z" class="c"></path><path d="M94 269v-1c0-1 0-7 1-7 0 4 1 7 2 12v1l2 9c-1-1-1-1-1-2h0c-1-1-1-2-2-3 0-2 0-3-1-5h0l-1-4z" class="K"></path><defs><linearGradient id="AB" x1="97.917" y1="272.83" x2="107.489" y2="278.644" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#8e8d8e"></stop></linearGradient></defs><path fill="url(#AB)" d="M100 255h1v5c0 2 0 4 1 6h0c0 1 1 1 1 2v3c1 1 1 2 1 3-1-1-1-2-2-3 1 5 2 9 5 13-1 0-2 0-2 1l-2-2v-1c-1-2-1-4-2-5-1-3-1-5-1-7-1-2-1-4-1-6 0-3 0-6 1-9z"></path><path d="M100 255h1v5c0 2 0 4 1 6h0c0 1 1 1 1 2v3c1 1 1 2 1 3-1-1-1-2-2-3-1-2-1-5-1-7h0c0 1-1 2 0 3h0-1v-3 6c-1-2-1-4-1-6 0-3 0-6 1-9z" class="F"></path><path d="M103 283l2 2c0-1 1-1 2-1l3 5c1 2 3 5 3 7 0 0-1 1-1 2l-1 2c-3-2-3-5-5-7-1-3-2-5-3-7v-3z" class="R"></path><path d="M106 288c1 1 1 2 2 3l2 3s1 1 1 2c-1 0-1-1-2-1h0c0-1-1-1-1-1-2-2-2-4-2-6z" class="H"></path><path d="M105 285c0-1 1-1 2-1l3 5-2 2c-1-1-1-2-2-3 0-1-1-2-1-3z" class="K"></path><path d="M110 289c1 2 3 5 3 7 0 0-1 1-1 2l-1-2c0-1-1-2-1-2l-2-3 2-2z" class="T"></path><defs><linearGradient id="AC" x1="100.918" y1="240.511" x2="129.646" y2="210.756" xlink:href="#B"><stop offset="0" stop-color="#c4c3c4"></stop><stop offset="1" stop-color="#e8e7e8"></stop></linearGradient></defs><path fill="url(#AC)" d="M117 199h0 1c1 2 0 6 0 8v6c0 4-1 7 0 11v3c-1 2-1 4-2 5v2l-1 2v3c0 1 0 3-1 4v12c-3-5-5-10-6-15-1-6-2-11-1-17 0-3 1-7 3-10 2-5 4-10 7-14z"></path><path d="M163 447c5-8 3-17 1-26l3-3c-5 0-12-1-16-3-1-1-2-2-3-2s-8 1-9 1c-4 0-8 1-12 0-8-1-16-6-22-12-4-4-8-10-8-16 0-5 2-9 5-12 2-2 5-4 9-3 3 0 6 1 9 4l-1 1c-1-1-3-3-4-3-2 1-3 1-4 2h-1-1c0 1-2 1-2 2-3 1-4 3-4 6-1 3 0 6 2 9l2 2v1c0 1 1 2 2 3 1 0 2 1 3 2h0l1 1h1l-1-1c1 0 1 0 2-1v1c3 1 6 2 8 2 5 1 9 0 14 0v1c1 1 2 0 2 0 4 0 7-1 11-2l-1 1c2 1 3 1 5 2 2-2 5-3 7-4l1 1c1 0 2-1 3-1 2 0 3 0 5-1l2-1c1-1 2-1 3-2 1 1 1 1 0 2l1 1 2-1h1l-1 3 1-1 1-1-1 2c1 0 2 0 3-1h1l1 2-4 2h-1l-4 3-2 1h1 3v1c0 1-2 2-3 2v1 1c1 0 1 0 2-1h0c1 0 1 0 2-1l3-1 6-4h0l2-1c0-1 3-2 4-3 2-1 5-3 7-5l2-1c1-1 1-1 3-1v-1c1-1 2-3 3-4l3-1v1l-1 2h1 0c2-1 2-2 4-2 0 1-1 1-1 2-1 0-1 1-2 2h1c-1 1-2 2-2 3 1-1 3-2 4-2v1c-2 3-4 5-7 8h1v1h0l2-1-4 4c-1 1-3 1-4 2 0 1 0 1-1 2l-1 2c0 1 0 2 1 3h0v1l1 1c-2 2-7 5-10 6l-5 4c-2 0-3 0-5 1 0 1-1 2-2 3h-1v-1c-1 0-2 0-3 1h0c-1 1-1 2-2 3l-1-1v-1c1-1 1-2 1-3h0l-1 1h0c-1 1-1 2-1 2-1 2-2 3-4 4h0v-2-3c0 6-2 9-4 14h-2z"></path><path d="M108 372c3 0 4-1 7 1-2 1-3 1-4 2h-1v-1h-1l-1-2zm79 34h3c0 1-1 2-2 2h1v2h0c-1 1 0 1-1 1v2c-1 1-2 2-4 2h-1c-3 1-6 4-9 5h-1-3c2-2 5-3 8-4 2-1 4-3 6-5l-7 2-1-1c1 0 1 0 2-1l3-1 6-4z" class="L"></path><path d="M187 406h3c0 1-1 2-2 2-2 2-4 3-6 3l-1-1 6-4z" class="E"></path><defs><linearGradient id="AD" x1="108.436" y1="383.891" x2="100.184" y2="383.376" xlink:href="#B"><stop offset="0" stop-color="#a9a8aa"></stop><stop offset="1" stop-color="#cccccd"></stop></linearGradient></defs><path fill="url(#AD)" d="M101 394c-1-5-2-9-1-14 2-4 4-6 8-8l1 2h1v1h-1c0 1-2 1-2 2-3 1-4 3-4 6-1 3 0 6 2 9l2 2v1c0 1 1 2 2 3-1 0-2-2-3-2h-1v2h0v-1c-1-1-2-3-4-3z"></path><path d="M163 408c1 0 3-1 4-1 2 0 3 0 4 1h2 0 1 3v1c0 1-2 2-3 2v1 1c1 0 1 0 2-1h0l1 1c-6 3-14 3-20 0-2 0-5-1-7-2 1-1 4-2 5-2 2-1 5-1 7-1h0 1z" class="D"></path><path d="M163 408c1 0 3-1 4-1 2 0 3 0 4 1-2 0-4 1-6 1v-1h-2zm-7 3c2 0 5-1 7-1h0 4v1c-2 1-4 1-6 1h-1c-2-1-3-1-4-1z" class="E"></path><path d="M162 408h1 2v1l-2 1c-2 0-5 1-7 1h-3c2-2 7-2 9-3z" class="C"></path><path d="M173 408h0 1 3v1c0 1-2 2-3 2h-1c-1 1-1 1-2 1h0c-1 0-1 1-2 0h0l4-4z" class="H"></path><path d="M155 409c2-1 5-1 7-1h0c-2 1-7 1-9 3h3c1 0 2 0 4 1h1c-1 0-2 1-4 1h0c-2 0-5-1-7-2 1-1 4-2 5-2z" class="L"></path><path d="M170 420h3 1c3-1 6-4 9-5h1c-2 2-5 5-8 6 1 1 2 1 3 0 1 0 1 0 2 1 3-1 6-4 9-6l2-1v1l-1 1c0 1-1 1-2 3l-2 1c-1 0-2 1-3 2-1 0-2 1-2 2-2 2-4 3-5 4l-2 2-1 1h0c-1 1-1 2-1 2-1 2-2 3-4 4h0v-2-3l-2-11 3-2z" class="Y"></path><path d="M175 425c-1 1-3 1-4 2h-2c0-1 2-2 2-3 2 1 3 1 4 1z" class="l"></path><path d="M171 424c2-1 4-2 5-3 1 1 2 1 3 0 1 0 1 0 2 1h0l-6 3c-1 0-2 0-4-1z" class="J"></path><path d="M190 416l2-1v1l-1 1c0 1-1 1-2 3l-2 1c-1 0-2 1-3 2-1 0-2 1-2 2-2 2-4 3-5 4l-1-1c-1 0-1 0-2 1h-1 0c2-2 4-3 6-4l3-2-1-1h0c3-1 6-4 9-6z" class="C"></path><path d="M172 398c1-1 2-1 3-2 1 1 1 1 0 2l1 1 2-1h1l-1 3 1-1 1-1-1 2c1 0 2 0 3-1h1l1 2-4 2h-1l-4 3-2 1h0-2c-1-1-2-1-4-1-1 0-3 1-4 1h-1 0c-2 0-5 0-7 1-1 0-4 1-5 2l-1-1-1-1h0l-1-1h0c0-1 0-1 1-1h0v-1c2-1 4-1 5-2h1c2-2 5-3 7-4l1 1c1 0 2-1 3-1 2 0 3 0 5-1l2-1z" class="F"></path><path d="M174 399h0l1-1 1 1h-2z" class="I"></path><path d="M149 410v-1c2-1 5-1 7-2v1l-1 1c-1 0-4 1-5 2l-1-1z" class="l"></path><path d="M148 407c3-1 7-2 10-3h4l-14 5-1-1h0c0-1 0-1 1-1h0z" class="a"></path><defs><linearGradient id="AE" x1="165.83" y1="401.585" x2="160.199" y2="409.899" xlink:href="#B"><stop offset="0" stop-color="#a0a2a2"></stop><stop offset="1" stop-color="#c5c2c3"></stop></linearGradient></defs><path fill="url(#AE)" d="M168 403l2-1c1 0 2 0 3 1l-2 1c-3 2-6 3-9 4-2 0-5 0-7 1l1-1v-1s3-1 4-1c2-1 5-1 8-3z"></path><path d="M168 403l2-1c1 0 2 0 3 1l-2 1-1-1h-2z" class="P"></path><path d="M176 399l2-1h1l-1 3 1-1 1-1-1 2c-3 2-6 3-9 5l-3 1c-1 0-3 1-4 1h-1 0c3-1 6-2 9-4l2-1c-1-1-2-1-3-1l2-2 2-1h2z" class="U"></path><defs><linearGradient id="AF" x1="175.25" y1="398.249" x2="174.134" y2="401.956" xlink:href="#B"><stop offset="0" stop-color="#6d6d6d"></stop><stop offset="1" stop-color="#838182"></stop></linearGradient></defs><path fill="url(#AF)" d="M176 399l2-1h1c-2 2-4 4-6 5-1-1-2-1-3-1l2-2 2-1h2z"></path><path d="M182 400h1l1 2-4 2h-1l-4 3-2 1h0-2c-1-1-2-1-4-1l3-1c3-2 6-3 9-5 1 0 2 0 3-1z" class="H"></path><path d="M170 406h2c3 0 5-3 7-3v1l-4 3-2 1h0-2c-1-1-2-1-4-1l3-1z" class="K"></path><path d="M161 400l1 1c1 0 2-1 3-1 2 0 3 0 5-1l1 1c-1 1-6 4-8 4h-1-4c-3 1-7 2-10 3v-1c2-1 4-1 5-2h1c2-2 5-3 7-4z" class="H"></path><defs><linearGradient id="AG" x1="197.716" y1="413.156" x2="185.37" y2="406.85" xlink:href="#B"><stop offset="0" stop-color="#757575"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#AG)" d="M208 390l3-1v1l-1 2h1 0c2-1 2-2 4-2 0 1-1 1-1 2-1 0-1 1-2 2l-2 2-3 3c-1 1-5 3-5 5v1c-1 3-10 9-12 11-3 2-6 5-9 6-1-1-1-1-2-1-1 1-2 1-3 0 3-1 6-4 8-6 2 0 3-1 4-2v-2c1 0 0 0 1-1h0v-2h-1c1 0 2-1 2-2h-3 0l2-1c0-1 3-2 4-3 2-1 5-3 7-5l2-1c1-1 1-1 3-1v-1c1-1 2-3 3-4z"></path><defs><linearGradient id="AH" x1="207.96" y1="398.297" x2="197.01" y2="395.258" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#AH)" d="M208 390l3-1v1l-1 2h1 0c2-1 2-2 4-2 0 1-1 1-1 2-1 0-1 1-2 2l-2 2-3 3-3 1c-3 2-5 4-8 5-1 1-2 1-3 1h0-1-1 0c-1-1-2 0-2-1s3-2 4-3c2-1 5-3 7-5l2-1c1-1 1-1 3-1v-1c1-1 2-3 3-4z"></path><path d="M207 396v-1l3-3h1c-1 2-2 3-4 4z" class="U"></path><path d="M207 396h3l-3 3-3 1c1-2 2-3 3-4z" class="N"></path><path d="M211 392h0c2-1 2-2 4-2 0 1-1 1-1 2-1 0-1 1-2 2l-2 2h-3 0c2-1 3-2 4-4z" class="b"></path><path d="M193 402l2 1h1l-3 3h0-1-1 0c-1-1-2 0-2-1s3-2 4-3z" class="P"></path><path d="M101 394c2 0 3 2 4 3v1h0v-2h1c1 0 2 2 3 2s2 1 3 2h0l1 1h1l-1-1c1 0 1 0 2-1v1c3 1 6 2 8 2 5 1 9 0 14 0v1c1 1 2 0 2 0 4 0 7-1 11-2l-1 1c2 1 3 1 5 2h-1c-1 1-3 1-5 2v1h0c-1 0-1 0-1 1h0l1 1h0c-11 3-23 3-34-2-5-3-11-7-13-13z" class="J"></path><path d="M115 400c3 1 6 2 8 2 5 1 9 0 14 0v1c1 1 2 0 2 0h0c-6 1-14 3-19 0-2-1-4-2-5-3z" class="S"></path><defs><linearGradient id="AI" x1="197.893" y1="424.343" x2="184.193" y2="418.069" xlink:href="#B"><stop offset="0" stop-color="#666667"></stop><stop offset="1" stop-color="#9d9b9b"></stop></linearGradient></defs><path fill="url(#AI)" d="M212 394h1c-1 1-2 2-2 3 1-1 3-2 4-2v1c-2 3-4 5-7 8h1v1h0l2-1-4 4c-1 1-3 1-4 2 0 1 0 1-1 2l-1 2c0 1 0 2 1 3h0v1l1 1c-2 2-7 5-10 6l-5 4c-2 0-3 0-5 1 0 1-1 2-2 3h-1v-1c-1 0-2 0-3 1h0c-1 1-1 2-2 3l-1-1v-1c1-1 1-2 1-3h0l2-2c1-1 3-2 5-4 0-1 1-2 2-2 1-1 2-2 3-2l2-1c1-2 2-2 2-3l1-1v-1l-2 1c2-2 11-8 12-11v-1c0-2 4-4 5-5l3-3 2-2z"></path><path d="M187 421h1 1-1c0 1-1 1-1 2h0c-1 1-3 2-4 2-2 2-6 6-6 8h0c-1 1-1 2-2 3l-1-1v-1c1-1 1-2 1-3h0l2-2c1-1 3-2 5-4 0-1 1-2 2-2 1-1 2-2 3-2z" class="E"></path><defs><linearGradient id="AJ" x1="202.935" y1="419.984" x2="184.828" y2="426.528" xlink:href="#B"><stop offset="0" stop-color="#777578"></stop><stop offset="1" stop-color="#a9a9a8"></stop></linearGradient></defs><path fill="url(#AJ)" d="M202 418l1 1c-2 2-7 5-10 6l-5 4c-2 0-3 0-5 1 2-4 15-10 19-12z"></path><defs><linearGradient id="AK" x1="206.234" y1="410.776" x2="197.657" y2="407.694" xlink:href="#B"><stop offset="0" stop-color="#656564"></stop><stop offset="1" stop-color="#908e90"></stop></linearGradient></defs><path fill="url(#AK)" d="M208 404h1v1h0l2-1-4 4c-1 1-3 1-4 2l-9 7c-2 2-4 5-7 6 0-1 1-1 1-2h1-1-1l2-1 3-3c4-5 11-9 16-13z"></path><defs><linearGradient id="AL" x1="201.444" y1="402.707" x2="204.503" y2="408.111" xlink:href="#B"><stop offset="0" stop-color="#838382"></stop><stop offset="1" stop-color="#b0aeb0"></stop></linearGradient></defs><path fill="url(#AL)" d="M212 394h1c-1 1-2 2-2 3 1-1 3-2 4-2v1c-2 3-4 5-7 8-5 4-12 8-16 13l-3 3c1-2 2-2 2-3l1-1v-1l-2 1c2-2 11-8 12-11v-1c0-2 4-4 5-5l3-3 2-2z"></path><path d="M212 394h1c-1 1-2 2-2 3 0 2-5 5-6 6l-1 1c-1 0-2 1-2 1v-1c0-2 4-4 5-5l3-3 2-2z" class="H"></path><defs><linearGradient id="AM" x1="188.833" y1="86.395" x2="192.284" y2="154.299" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#AM)" d="M168 87c1 0 1 0 2-1 0 1 1 2 1 2 6 7 14 12 23 13 6 0 11 0 17-1l10-2v2h1l1 1c-1 2-2 4-3 5 0 1-1 2-1 3-1 0-2 2-2 3v2l-1 2h0v3l1 4c-1 1-1 1-2 1v1s-1 1-1 2l-3 3c-4 4-8 9-11 13s-5 7-7 10l-2 3c0 1-1 2-2 4h0c-2-1-3-1-5-1h-3-1c-2-1-5-1-7-1l-1-1h-2l-1-1h0v-1h-2v1c-2-1-4 0-6-1h-1-1c-1 0-1-1-2-1-2 0-3 0-4-1-1 0-1-1-1-2l3-5h1c0-1 2-2 2-3l1-3 4-5c1 0 1 0 1-1s1-1 1-3l3-3 15-14c1-2 3-4 5-5 2-2 5-3 5-5-2 0-5-1-8-1-7-2-13-9-17-16z"></path><path d="M184 135h0c1-2 2-3 3-3h1c-1 1-4 5-5 6l-1-1-1 1 3-3z" class="F"></path><path d="M162 146c1-1 2-1 3-1l-3 5c-1 1-2 2-2 3l-1-1c0-2 1-2 1-4l2-2z" class="D"></path><path d="M211 100l10-2v2s0 1-1 1c-1 1-4 2-6 1h1 2l-1-1 1-1h1-4c-1 1-2 0-3 0z" class="M"></path><path d="M177 151l8-11 1 1h0v1l-3 5h0c-2 1-4 2-6 4z" class="R"></path><path d="M193 104c2-1 8 0 10 0-5 2-9 5-13 7-1 1-3 3-5 3h-2c1-2 3-4 5-5 2-2 5-3 5-5z" class="Q"></path><defs><linearGradient id="AN" x1="197.829" y1="127.718" x2="185.636" y2="140.127" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#757474"></stop></linearGradient></defs><path fill="url(#AN)" d="M185 140c4-4 8-8 12-13 1-1 3-5 5-6 0 3-2 3-3 5l-1 1h-1l1 1 2-2c1-1 0 1 1-1 1 0 1-1 2-1l1-1h0s0-1 1-1h0c1 0 1-1 2-1h0l1-1s0 1 1 1l1-1h0c0 1-2 2-2 3-2 2-5 1-7 4-1 0-3 1-3 2-4 4-8 8-12 13v-1h0l-1-1z"></path><path d="M181 138l1-1 1 1-2 2c-1 1-2 3-3 4-1 2-3 4-4 6-1 1-4 4-4 6v1l-1-1h0v-1h-2v1c-2-1-4 0-6-1l3-3c1-2 2-2 3-3s1-1 2-1l7-7v-1c2 0 3-1 5-2z" class="k"></path><path d="M164 152c2-1 4-1 5-3l1-1c2-1 2-2 4-3h0c0 2-3 3-3 5 0 0 1-1 2-1l1 1c-1 1-4 4-4 6v1l-1-1h0v-1h-2v1c-2-1-4 0-6-1l3-3z" class="H"></path><defs><linearGradient id="AO" x1="190.868" y1="118.587" x2="195.283" y2="126.891" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#464445"></stop></linearGradient></defs><path fill="url(#AO)" d="M167 149c0-2 2-3 3-4 1-2 3-5 5-6 2-2 4-5 6-6 1 0 2-1 3-2l6-6c1-2 3-4 4-6h1v-1h1-1l1-1h0 0 0c1 0 1-1 2-1h1c0-1 1-2 2-2v-2c1-1 2 0 3 0l1-1 1-1h1l1-1h0l-1 2h1l-3 3h0l-10 11c-3 2-5 5-7 7h0-1c-1 0-2 1-3 3h0l-3 3c-2 1-3 2-5 2v1l-7 7c-1 0-1 0-2 1z"></path><path d="M176 141c1-2 3-5 5-6h3l-3 3c-2 1-3 2-5 2v1z" class="I"></path><path d="M195 125l-1-1h-1c3-3 7-7 11-10h1l-10 11z" class="M"></path><defs><linearGradient id="AP" x1="196.361" y1="144.459" x2="180.335" y2="141.948" xlink:href="#B"><stop offset="0" stop-color="#716e71"></stop><stop offset="1" stop-color="#bebebc"></stop></linearGradient></defs><path fill="url(#AP)" d="M198 129l2 1c-2 2-11 12-11 14l1 1-1 1v2c-2 3-5 8-8 11h-1c-2-1-5-1-7-1l-1-1c1 0 1-1 2-2l3-4c2-2 4-3 6-4h0l3-5c4-5 8-9 12-13z"></path><path d="M183 147h1s0 1-1 2l-2 2v-1l-2 2 4-5h0z" class="T"></path><path d="M189 144l1 1-1 1v2c-2 3-5 8-8 11h-1v-2l9-13z" class="F"></path><path d="M177 151c2-2 4-3 6-4l-4 5 2-2v1l-3 6c1 0 1-1 2 0v2c-2-1-5-1-7-1l-1-1c1 0 1-1 2-2l3-4z" class="C"></path><path d="M174 155c2 0 3-1 4-2-1 1-2 3-4 4l-1 1-1-1c1 0 1-1 2-2z" class="D"></path><path d="M177 151c2-2 4-3 6-4l-4 5-1 1c-1 1-2 2-4 2l3-4z" class="P"></path><path d="M214 117v-1h2v3l1 4c-1 1-1 1-2 1v1s-1 1-1 2l-3 3v-1-1c-1 0-2-1-3-1l-6 6-1 2c-2 2-3 3-5 4l-4 4c0 1-1 2-1 3s-1 1-1 2h-1v-2l1-1-1-1c0-2 9-12 11-14l-2-1c0-1 2-2 3-2 2-3 5-2 7-4 0-1 2-2 2-3l4-3z" class="S"></path><path d="M201 127v2l-1 1-2-1c0-1 2-2 3-2z" class="b"></path><path d="M214 117v-1h2v3c-1 1-3 2-4 3 2-2 2-3 2-5z" class="I"></path><path d="M202 130c1-1 1-2 2-3 2-1 3-1 5-1h0c-3 1-5 3-7 4z" class="Z"></path><path d="M192 143c1-1 1-2 2-2 0-1 0-2 1-3s2-3 3-3c0 1 0 2-1 3l-1 1-4 4z" class="I"></path><path d="M212 122c1-1 3-2 4-3l1 4c-1 1-1 1-2 1v1c-1-1-2-1-3-2l-2 2v-1c0-1 1-1 2-2z" class="R"></path><path d="M210 125l2-2c1 1 2 1 3 2 0 0-1 1-1 2l-3 3v-1-1c-1 0-2-1-3-1l1-1h0l1-1z" class="W"></path><path d="M202 130c2-1 4-3 7-4l-1 1-6 6-1 2c-2 2-3 3-5 4l1-1c1-1 1-2 1-3 1-2 3-3 4-5z" class="R"></path><defs><linearGradient id="AQ" x1="193.89" y1="139.761" x2="199.511" y2="146.498" xlink:href="#B"><stop offset="0" stop-color="#aaa8a8"></stop><stop offset="1" stop-color="#d2d2d3"></stop></linearGradient></defs><path fill="url(#AQ)" d="M208 127c1 0 2 1 3 1v1 1c-4 4-8 9-11 13s-5 7-7 10l-2 3c0 1-1 2-2 4h0c-2-1-3-1-5-1h-3c3-3 6-8 8-11h1c0-1 1-1 1-2s1-2 1-3l4-4c2-1 3-2 5-4l1-2 6-6z"></path><path d="M202 133l1 1c0 1-1 2-1 3 0-1-1-1-1-2l1-2z" class="T"></path><path d="M184 159c1 0 1 0 2-1h0c1-1 1-2 2-3l1-1h0c1-2 1-2 3-3v2h1l-2 3c0 1-1 2-2 4h0c-2-1-3-1-5-1z" class="l"></path><path d="M201 135c0 1 1 1 1 2-1 1-2 2-3 4l-1-1h1v-1l-1 1-3 3c-1 2-3 4-5 5h0c0-1 1-1 1-2s1-2 1-3l4-4c2-1 3-2 5-4z" class="C"></path><path d="M163 135c2-1 5-4 6-6 0 0 0-1 1-1s1 0 2 1c0-1 1-2 2-2l3-3c1 0 0 1 1 0l1-1c1 0 2-1 2-1 1-1 4 0 4 0h2c-1 2-10 10-12 12-3 3-7 7-10 11-1 0-2 0-3 1l-2 2c0 2-1 2-1 4l1 1v2h-1c-1 0-1-1-2-1-2 0-3 0-4-1-1 0-1-1-1-2l3-5h1c0-1 2-2 2-3l1-3 4-5z" class="J"></path><path d="M156 146c1 0 2 0 4-1 1 0 2-1 3-2h0l-1 2h0c0-1 1-1 1-2l1 1-2 2-2 2c0 2-1 2-1 4l1 1v2h-1c-1 0-1-1-2-1-2 0-3 0-4-1-1 0-1-1-1-2l3-5h1z" class="a"></path><path d="M160 148c0 2-1 2-1 4-1 1-1 2-3 2v-1l4-5z" class="P"></path><defs><linearGradient id="AR" x1="165.229" y1="132.023" x2="164.86" y2="138.595" xlink:href="#B"><stop offset="0" stop-color="#454345"></stop><stop offset="1" stop-color="#626362"></stop></linearGradient></defs><path fill="url(#AR)" d="M163 135c2-1 5-4 6-6 0 0 0-1 1-1s1 0 2 1c0-1 1-2 2-2l3-3c1 0 0 1 1 0l1-1c1 0 2-1 2-1 1-1 4 0 4 0-2 3-5 4-8 7-2 1-4 3-6 5-4 3-8 6-13 9l1-3 4-5z"></path><path d="M246 89h3v1h0v1c0 1 1 1 1 1h1c-1 1-2 1-3 2 0 1 1 1 1 2 1 0 1 0 2-1h1l-1 1c-2 1-5 2-7 3 1 1 1 1 2 1l1-1h0 1c0-1 1-1 1-1 0-1 0 0 1-1h0v-1h1c1 1 2 1 3 1 2 1 3 2 5 4-1 0-1 1-1 1h-1c0 1 1 1 1 2h0v1l1 1c1 0 1 1 1 2h2 0l1 1 1 1v1 1c-1 1-1 1-2 1l-1-1-2 2 1 1h1 0c1 0 1-1 2-1s1 0 2-1h0v1c1 0 1 1 2 2v1l3 4 1 1c0 1-1 1-2 2h3s1 0 2 1 3 1 5 2v2h1 0c1 1 2 1 3 1h-1v1 4l2 2c0 2-1 1 0 3h0c1 0 1 1 2 2v4h0c1 0 0 0 1 1h0-1c0 1 0 1 1 2h-1 0c-1-1-1-2-1-3-1-1-1-1-1-2s-1-3-2-4v-1c0-1-1-2-1-4l-2 2 1 1c1 1 1 2 1 4h1v1c-1 0-1-1-2-1-1-1-1-1-2-1h-1l-3-1h-1 0l-3 3v1c-2 0-3-1-4-2-2 0-5-1-6 1l-2 2c-1 0-2 1-2 1v1c-3 2-5 3-7 6v1h0l-1 1-2 1c0 2-1 3 0 5h-1c-1 0-1-1-2-2v-3l1-1h-1 0c-1 0-1 0-2 1-1 0-3 1-4 0h0l-2 2h-2c0-1-1-1-1-2h-1c-1-1-1-2-2-3s-4-4-4-6l-1-1v-2h-1l1-1h1v-1l-2-1c0-1-1-2-2-3 1 0 0-1 0-1s0-1 1-1l-1-1v-3l-2-5c-1-1-2-3-2-4l-1-4v-3h0l1-2v-2c0-1 1-3 2-3 0-1 1-2 1-3 1-1 2-3 3-5l-1-1h-1v-2l5-1 3-1c2-1 3-1 4-2s2-1 3-1h1v-1c1-1 6-2 8-2h0 1 1 0l-1-1z" class="h"></path><path d="M229 145c1-1 1-1 2-1 3 2 5 2 8 2h1s1 0 1 1h0c-5 1-8 1-12-2z" class="J"></path><path d="M255 144c3-3 7-4 12-5l-1 3c-2 0-5-1-6 1l-4 1h-1z" class="b"></path><path d="M267 139l6 1h0l-3 3v1c-2 0-3-1-4-2l1-3z" class="N"></path><path d="M259 131h0c1-1 3-1 4 0h0l1 1v2l-1 1-12-1h5 0 1c1-1 2-2 2-3h0z" class="L"></path><path d="M263 131l1 1c0 1 0 2-1 2-1-1-1-1-2-3h1 1z" class="K"></path><path d="M225 142v-1h2v1c3 0 4 1 7 1h0l5 3c-3 0-5 0-8-2-1 0-1 0-2 1l-4-2v-1z" class="D"></path><path d="M225 142v-1h2v1c2 1 3 1 4 2-1 0-1 0-2 1l-4-2v-1z" class="K"></path><path d="M255 144h1l4-1-2 2c-1 0-2 1-2 1v1c-3 2-5 3-7 6v1h0l-1 1-2 1v-1c1-3 2-4 4-6s2-4 5-5z" class="X"></path><path d="M238 143h1c4 1 9 1 13 0 0 0 1-1 1 0-2 1-3 2-4 4-1 1-1 2-2 2h0 0v-2h-6 0c0-1-1-1-1-1h-1l-5-3h4z" class="I"></path><path d="M240 146l7-1 1 1h0l-1 1h-6 0c0-1-1-1-1-1z" class="N"></path><path d="M233 158c0-2-1-3-1-5 0 1 2 3 2 4v1l2-2c-1-2-2-3-3-5h1c1 0 2 2 3 3h1c1 0 1 0 2-1 0 0 1 0 2-1h1c1-1 2-1 2-1v-1l1 1c0 1 0 1-1 1 0 1 0 2 1 3v1c0 2-1 3 0 5h-1c-1 0-1-1-2-2v-3l1-1h-1 0c-1 0-1 0-2 1-1 0-3 1-4 0h0l-2 2h-2z" class="j"></path><path d="M246 131h6 7 0c0 1-1 2-2 3h-1 0-5c-1 1-3 1-4 1-4 0-7 0-11-1 0-1-1-1-1-2 3 1 9 0 11-1z" class="K"></path><path d="M263 131c1 0 3 0 4 1 0 0 0 1 1 1 1 1 2 1 3 1l2-1h0c2 0 4 0 6 1l2 1-2 2 1 1c1 1 1 2 1 4h1v1c-1 0-1-1-2-1-1-1-1-1-2-1l-1-1c-5-2-9-4-14-5l1-1v-2l-1-1h0z" class="D"></path><path d="M273 133c2 0 4 0 6 1l2 1-2 2 1 1c1 1 1 2 1 4h1v1c-1 0-1-1-2-1-1-1-1-1-2-1l-1-1h1c0-1 0-2-1-2s-2-1-3-1c0-1 0 0-1-1 0 0-1 0-1-1h0 2v-1l-1-1z" class="H"></path><path d="M273 133c2 0 4 0 6 1l2 1-2 2 1 1c1 1 1 2 1 4-1-2-3-4-3-5s0-2-1-3h-3l-1-1z" class="N"></path><path d="M216 116h0l1-2c0 1-1 3 0 5v1c1 2 1 4 3 5l1-1 1 1 1 1c1 1 2 1 2 2 1 1 2 2 3 2v2l5 2h-1c-1 0-2 0-3-1v1 1h1c1-1 1 0 2 0h1l1 1c1 1 2 1 3 2v2c-2 0-3 0-5-1h0-1l-1-1h-2v-1s-1-1-2-1h0l-1-1-1-1h0c1 1 2 2 2 4l5 2c0 1 0 0 1 0s5 3 6 3h-4 0c-3 0-4-1-7-1v-1h-2v1l-2-1c0-1-1-2-2-3 1 0 0-1 0-1s0-1 1-1l-1-1v-3l-2-5c-1-1-2-3-2-4l-1-4v-3z" class="g"></path><path d="M221 124l1 1 1 1c1 1 2 1 2 2 1 1 2 2 3 2v2c-4-2-6-3-8-7l1-1z" class="J"></path><path d="M221 132c2 2 4 5 6 7 2 1 4 3 7 4-3 0-4-1-7-1v-1h-2v1l-2-1c0-1-1-2-2-3 1 0 0-1 0-1s0-1 1-1l-1-1v-3z" class="N"></path><path d="M222 136c1 1 2 3 3 4 1 0 1 0 2 1h-2v1l-2-1c0-1-1-2-2-3 1 0 0-1 0-1s0-1 1-1z" class="L"></path><path d="M272 124s1 0 2 1 3 1 5 2v2h1 0c1 1 2 1 3 1h-1v1 4l2 2c0 2-1 1 0 3h0c1 0 1 1 2 2v4h0c1 0 0 0 1 1h0-1c0 1 0 1 1 2h-1 0c-1-1-1-2-1-3-1-1-1-1-1-2s-1-3-2-4v-1c0-1-1-2-1-4l-2-1c-2-1-4-1-6-1h0l-2 1c-1 0-2 0-3-1-1 0-1-1-1-1-1-1-3-1-4-1-1-1-3-1-4 0h0-7c2-1 6-1 8-1l3-1h-2-3c1-1 0-1 1-1l5-1c1 0 2 0 2-1h0c1-1 2-1 3-2h2 1z" class="S"></path><path d="M272 130l1-1 1 1h4c-1 1-2 0-3 0h-1v1l-1 1-1-1h1l-1-1z" class="F"></path><path d="M278 130c1 1 2 1 4 1v4l-1-1h-1c-1-1-2-2-2-4zm-11-1l2-1c1-1 0 0 1 0h2 2v2l-1-1-1 1c-1 0-3 0-5-1z" class="e"></path><path d="M260 130c5 0 6 0 10 3h3 0l-2 1c-1 0-2 0-3-1-1 0-1-1-1-1-1-1-3-1-4-1-1-1-3-1-4 0h0-7c2-1 6-1 8-1z" class="H"></path><path d="M272 128c1-1 4-1 5 0h2v1h1 0c1 1 2 1 3 1h-1v1c-2 0-3 0-4-1h-4v-2h-2z" class="G"></path><defs><linearGradient id="AS" x1="272.893" y1="130.914" x2="261.373" y2="123.812" xlink:href="#B"><stop offset="0" stop-color="#110f12"></stop><stop offset="1" stop-color="#373736"></stop></linearGradient></defs><path fill="url(#AS)" d="M272 124s1 0 2 1 3 1 5 2v2-1h-2c-1-1-4-1-5 0h-2c-1 0 0-1-1 0l-2 1h-4-2-3c1-1 0-1 1-1l5-1c1 0 2 0 2-1h0c1-1 2-1 3-2h2 1z"></path><defs><linearGradient id="AT" x1="263.999" y1="131.228" x2="229.994" y2="123.731" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#AT)" d="M217 119v-1-2h0c1 1 1 1 1 2h1c1 0 1 1 2 1v-1l3 1 1 1h2 0c1-1 1-1 2-1h1l1-1v1l1 1 1-1 1 1h0c1 1 3 2 5 2 1 0 2 1 3 1h2 0c0 2 0 1-1 2h3 1l3-1c0 2 0 2 1 3h1c2 0 4-1 5-1s2 0 3-1c2 2 3 1 6 1h0c0 1-1 1-2 1l-5 1c-1 0 0 0-1 1h3 2l-3 1c-2 0-6 0-8 1h-6c-2 1-8 2-11 1 0 1 1 1 1 2h-3l-5-2v-2c-1 0-2-1-3-2 0-1-1-1-2-2l-1-1-1-1-1 1c-2-1-2-3-3-5v-1z"></path><path d="M225 128l4 1 3 1c5 1 9 2 14 1-2 1-8 2-11 1 0 1 1 1 1 2h-3l-5-2v-2c-1 0-2-1-3-2z" class="D"></path><path d="M225 128l4 1 3 1c-1 0-1 1-2 1s-2 0-2-1c-1 0-2-1-3-2z" class="P"></path><path d="M247 125l3-1c0 2 0 2 1 3h1c-5 3-12 3-19 2v-3l2-1c4 2 8 1 12 0z" class="E"></path><path d="M217 119v-1-2h0c1 1 1 1 1 2h1c1 0 1 1 2 1v-1l3 1 1 1h2 0c1-1 1-1 2-1h1l1-1v1l1 1 1-1 1 1h0c1 1 3 2 5 2 1 0 2 1 3 1h2 0c0 2 0 1-1 2h3 1c-4 1-8 2-12 0l-2 1v3c-1-1-2-1-3-2h-2-1l2 2-4-1c0-1-1-1-2-2l-1-1-1-1-1 1c-2-1-2-3-3-5v-1z" class="k"></path><path d="M222 125c1-1 1-2 2-2l1 1v1c-1 0-2 0-2 1l-1-1z" class="R"></path><path d="M226 125h1l2-1c1 0 3 0 5 1h-4v2h-2c0-1-1-2-2-2z" class="W"></path><path d="M230 127v-2h4 1l-2 1v3c-1-1-2-1-3-2z" class="L"></path><path d="M217 120h1c1 0 3 3 3 4l-1 1c-2-1-2-3-3-5z" class="K"></path><path d="M225 124l1 1c1 0 2 1 2 2h-1l2 2-4-1c0-1-1-1-2-2 0-1 1-1 2-1v-1z" class="N"></path><path d="M225 124l1 1c1 0 2 1 2 2h-1c-1-1-2-1-2-2v-1z" class="I"></path><path d="M234 120c1 1 3 2 5 2 1 0 2 1 3 1h2 0c0 2 0 1-1 2-4-1-7-1-11-3 1 0 1-1 2-2z" class="V"></path><path d="M225 120h2 0c1-1 1-1 2-1h1l1-1v1l1 1 1-1 1 1h0c-1 1-1 2-2 2h-2s0-1-1-1c-2 0-2 1-2 2h-1l-1-1-1-1h-2c-1 0-1-1-1-2v-1l3 1 1 1z" class="O"></path><path d="M224 121l1-1c1 0 1 0 2 1-1 0-1 0-2 1l-1-1zm9-2l1 1h0c-1 1-1 2-2 2h-2v-1l2-1 1-1z" class="B"></path><path d="M255 105l3-1v1l1 1c1 0 1 1 1 2h2 0l1 1 1 1v1 1c-1 1-1 1-2 1l-1-1-2 2 1 1h1 0c1 0 1-1 2-1s1 0 2-1h0v1c1 0 1 1 2 2v1l3 4 1 1c0 1-1 1-2 2h3-1-2c-1 1-2 1-3 2-3 0-4 1-6-1-1 1-2 1-3 1s-3 1-5 1h-1c-1-1-1-1-1-3l-3 1h-1-3c1-1 1 0 1-2h0-2c-1 0-2-1-3-1-2 0-4-1-5-2h0l-1-1 6-4 1-1c4-2 6-5 9-7h3 1l2-2z" class="O"></path><path d="M249 112h2 1c-1 1-3 2-4 3h-1 0v-1l2-2z" class="Z"></path><path d="M255 105l3-1v1l1 1c1 0 1 1 1 2h2v1h-1 0c-1-1-2 0-3 1-1-1-1-1-2-1l-1-4z" class="g"></path><path d="M248 115c1 0 2-1 3 0v2c-1 0 0 0-1-1h-1c-1 0-1 1-1 2 1 0 1 0 2 1h-2v1h0c0 1 0 1 1 1 1 1 2 1 3 1h-5-3v1h-2c0-1-1-2-1-3h1v-2c1 0 1-1 2-1h1l2-2h1z" class="F"></path><path d="M248 120h0c0 1 0 1 1 1 1 1 2 1 3 1h-5c-2 0-3 0-5-1l6-1z" class="B"></path><path d="M262 108h0l1 1 1 1v1 1c-1 1-1 1-2 1l-1-1-2 2 1 1h1 0c1 0 1-1 2-1v1c-3 1-4 2-6 3h0-3l2-2h-1-2c0 1-1 1-1 0s0-1 1-2c1 0 2-1 3-2 2-1 3-2 5-3h1v-1z" class="Q"></path><path d="M263 109l1 1v2h-1c-1 0-1-1-2-1v-1l2-1z" class="B"></path><path d="M256 112c1 1 1 1 2 0l2-1h0v1c-1 1-2 1-2 2l-2 2h-1-2c0 1-1 1-1 0s0-1 1-2c1 0 2-1 3-2z" class="M"></path><path d="M240 114c4-2 6-5 9-7h3 1l-5 4h1 1v1h-1 0l-2 2v1h0l-2 2h-1c-1 0-1 1-2 1v2h-1c0 1 1 2 1 3-1 0-2-1-3-1-2 0-4-1-5-2h0l-1-1 6-4 1-1z" class="U"></path><path d="M240 114c4-2 6-5 9-7h3 1l-5 4c-2 1-5 3-7 5 0 0-1 1-1 2-2 0-2 1-3 2h-1v-1l2-2 1-1v-1l1-1z" class="R"></path><path d="M263 114c1 0 1 0 2-1h0v1c1 0 1 1 2 2v1l3 4 1 1c0 1-1 1-2 2h3-1-2c-1 1-2 1-3 2-3 0-4 1-6-1-1 1-2 1-3 1s-3 1-5 1h-1c-1-1-1-1-1-3l-3 1h-1-3c1-1 1 0 1-2h0v-1h3 5c-1 0-2 0-3-1-1 0-1 0-1-1 2-1 4-1 6-2h3 0c2-1 3-2 6-3v-1z" class="V"></path><path d="M263 115c-1 1-1 1-1 2h-2c-1 1-1 1-2 1h-1c2-1 3-2 6-3z" class="B"></path><path d="M267 117l3 4v1c-1 1-1 1-2 1-1-2-1-4-1-6z" class="G"></path><defs><linearGradient id="AU" x1="267.396" y1="125.473" x2="262.259" y2="123.046" xlink:href="#B"><stop offset="0" stop-color="#120f13"></stop><stop offset="1" stop-color="#282a27"></stop></linearGradient></defs><path fill="url(#AU)" d="M270 121l1 1c0 1-1 1-2 2h3-1-2c-1 1-2 1-3 2-3 0-4 1-6-1l1-1c3 1 5 0 7-1 1 0 1 0 2-1v-1z"></path><path d="M252 122c3-1 4-1 6-2 3 0 3 2 6 1h0c0 1-1 3-3 3l-1 1c-1 1-2 1-3 1l2-2h1c-1-2-2-2-3-2h-5z" class="X"></path><path d="M252 122h5c1 0 2 0 3 2h-1l-2 2c-1 0-3 1-5 1h-1c-1-1-1-1-1-3l-3 1h-1-3c1-1 1 0 1-2h0v-1h3 5 0z" class="U"></path><path d="M244 123l3 1h0l-1 1h-3c1-1 1 0 1-2z" class="F"></path><path d="M251 124h2c1-1 2-1 3-1s2 1 3 1l-2 2c-1 0-3 1-5 1h-1c-1-1-1-1-1-3h1z" class="E"></path><path d="M251 124s1 0 2 1v1l-1 1h-1c-1-1-1-1-1-3h1z" class="T"></path><path d="M246 89h3v1h0v1c0 1 1 1 1 1h1c-1 1-2 1-3 2 0 1 1 1 1 2 1 0 1 0 2-1h1l-1 1c-2 1-5 2-7 3 1 1 1 1 2 1l1-1h0 1c0-1 1-1 1-1 0-1 0 0 1-1h0v-1h1c1 1 2 1 3 1 2 1 3 2 5 4-1 0-1 1-1 1h-1c0 1 1 1 1 2h0l-3 1-2 2h-1-3c-3 2-5 5-9 7l-1 1-6 4-1 1-1-1v-1l-1 1h-1c-1 0-1 0-2 1h0-2l-1-1-3-1v1c-1 0-1-1-2-1h-1c0-1 0-1-1-2h0v2 1c-1-2 0-4 0-5v-2c0-1 1-3 2-3 0-1 1-2 1-3 1-1 2-3 3-5l-1-1h-1v-2l5-1 3-1c2-1 3-1 4-2s2-1 3-1h1v-1c1-1 6-2 8-2h0 1 1 0l-1-1z" class="C"></path><path d="M225 102c1-1 4-3 5-3h0c-1 2-2 3-3 4h-1v-1h-1z" class="Y"></path><path d="M226 97h1l-4 4-1-1h-1v-2l5-1z" class="Q"></path><path d="M229 108c0 1 0 1 1 2h0c-2 1-3 3-6 3l-3 3h-1c2-3 5-5 8-7l1-1z" class="R"></path><path d="M220 106c0 3-2 8-1 10h0c1 1 1 2 2 2v1c-1 0-1-1-2-1h-1c0-1 0-1-1-2h0v2 1c-1-2 0-4 0-5v-2c0-1 1-3 2-3 0-1 1-2 1-3z" class="b"></path><path d="M224 113c1 0 1 1 2 1v1c-1 0-1 1-1 2h0v2h-1l-3-1c-1 0-1-1-2-2h1 1l3-3z" class="E"></path><path d="M225 102h1v1s-1 1-1 2c-1 0-1 1-2 2 0 1 0 1 1 2h0l-4 4v-1c0-2 1-2 1-4 1-2 2-4 4-6z" class="f"></path><path d="M245 90v1c-1 0-2 1-2 2v1l-4 3h0c-1 1-3 2-5 3h0c-1 1-2 1-3 2h0c1-3 3-4 6-5 1-1 2-3 4-4l-3 1h-2c-1 0-2 2-4 2h-2c-1 0-2 1-3 1h-1l3-1c2-1 3-1 4-2s2-1 3-1h1v-1c1-1 6-2 8-2z" class="L"></path><path d="M239 97l1 1c-1 2-3 3-4 4v1c-1 1-2 1-3 2s-3 2-4 3l-1 1 2-4c-2 2-4 4-6 4h0c-1-1-1-1-1-2 1-1 1-2 2-2s2-2 3-2h0 1v1l2-2c1-1 2-1 3-2h0c2-1 4-2 5-3z" class="a"></path><path d="M239 97l1 1c-1 2-3 3-4 4v1c-1 1-2 1-3 2s-3 2-4 3l-1 1 2-4c1-1 4-3 5-5h-1 0c2-1 4-2 5-3z" class="E"></path><path d="M246 89h3v1h0v1c0 1 1 1 1 1h1c-1 1-2 1-3 2 0 1 1 1 1 2-2 1-4 3-6 3l-3 2c-1 0-2 1-4 1 1-1 3-2 4-4l-1-1h0l4-3v-1c0-1 1-2 2-2v-1h0 1 1 0l-1-1z" class="N"></path><path d="M246 95v-2c1-1 1 0 1-1v-1l2-1v1c0 1 1 1 1 1h1c-1 1-2 1-3 2l-2 1z" class="S"></path><path d="M246 95l2-1c0 1 1 1 1 2-2 1-4 3-6 3v-2l3-2z" class="X"></path><path d="M245 90h0 1v1c-1 0-1 1-1 2l-2 2v2c-1 1-3 1-3 3v1c-1 0-2 1-4 1 1-1 3-2 4-4l-1-1h0l4-3v-1c0-1 1-2 2-2v-1z" class="H"></path><path d="M251 95h1l-1 1c-2 1-5 2-7 3 1 1 1 1 2 1l1-1h0 1c0-1 1-1 1-1 0-1 0 0 1-1h0v-1h1c1 1 2 1 3 1 2 1 3 2 5 4-1 0-1 1-1 1h-1c0 1 1 1 1 2h0l-3 1-2 2h-1-3c-3 2-5 5-9 7l-1 1-6 4-1 1-1-1v-1l-1 1h-1c-1 0-1 0-2 1h0-2l-1-1h1v-2h0c0-1 0-2 1-2v-1c-1 0-1-1-2-1 3 0 4-2 6-3h0c-1-1-1-1-1-2 1-1 3-2 4-3s2-1 3-2v-1c2 0 3-1 4-1l3-2c2 0 4-2 6-3 1 0 1 0 2-1z" class="W"></path><path d="M229 108c1-1 3-2 4-3v1h3l-1 1h-1c-1 0-2 2-4 3-1-1-1-1-1-2z" class="I"></path><path d="M231 119c1 0 1-1 2-2 1 0 0 1 1 0 0-1 1-1 1-1 2-1 3-2 4-3l1 1-1 1-6 4-1 1-1-1z" class="P"></path><path d="M251 104c0-1 1-2 2-2h1c1 0 1-1 2-1s1 0 2 1h-1c0 1 1 1 1 2h0l-3 1-2 2h-1-3c1-1 2-1 2-2 1 0 2-1 3-1v-1c-1 0-2 0-3 1z" class="U"></path><path d="M255 105l-1-1h0c1-1 2-1 3-2 0 1 1 1 1 2h0l-3 1z" class="B"></path><path d="M248 99l2 1 2-1c1 0 2 1 2 1 0 1-1 1-2 2 0 0-1 1-2 1l1-1-1-1c-2 0-2 1-3 2-2 0-3 1-4 1-1 1-2 2-3 1l1-1v-1c2-1 5-2 7-4z" class="I"></path><path d="M254 97c2 1 3 2 5 4-1 0-1 1-1 1-1-1-1-1-2-1s-1 1-2 1h-1c-1 0-2 1-2 2-3 2-6 4-9 5 1-1 2-2 4-3l1-1c1-1 2-2 3-2s2-1 2-1c1-1 2-1 2-2 0 0-1-1-2-1l-2 1-2-1c1 0 2-1 4-2h2z" class="B"></path><path d="M230 110h1c1 1 1 0 2 0l1 1h1v1c-1 1-3 2-4 3l-3 3h-1l-2 2-1-1h1v-2h0c0-1 0-2 1-2v-1c-1 0-1-1-2-1 3 0 4-2 6-3z" class="D"></path><defs><linearGradient id="AV" x1="243.156" y1="99.446" x2="237.443" y2="103.976" xlink:href="#B"><stop offset="0" stop-color="#2d2b2c"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#AV)" d="M251 95h1l-1 1c-2 1-5 2-7 3 1 1 1 1 2 1l1-1h0 1c0-1 1-1 1-1 0-1 0 0 1-1h0v-1h1c1 1 2 1 3 1h-2c-2 1-3 2-4 2-2 2-5 3-7 4l-2 1c-1 1-2 2-3 2h-3v-1c1-1 2-1 3-2v-1c2 0 3-1 4-1l3-2c2 0 4-2 6-3 1 0 1 0 2-1z"></path><path d="M236 103v1h3c-1 1-2 2-3 2h-3v-1c1-1 2-1 3-2z" class="U"></path><path d="M439 37c2 7 2 14 1 21v4c-1 2-1 4-2 6l1 1v1h0c1 1 1 2 0 3v1h1c-2 1-2 0-3 1 0 1 1 1 1 2h0-3c0 1-1 1-1 2l-1 2-1 1v2h0c-1 2-2 3-3 4l2 1c0 1 1 1 2 1v1 2h-1l-1 1v-1 1h-5c1 1 1 1 2 1v1c-1 0-3-1-3 0-1 0-2 1-2 1-1 1-2 1-2 1-1 1-2 1-2 1h-1c-1 0-2 1-3 1-1 1-2 2-4 2h0l-2 2c-1 0-2 0-2 1-2 1-3 1-5 1-1-1-1-1-1-2h-3l-2 1h-1v1 3c-1 0-1 1-2 1h-1l-5 1c-1 0-1 0-2 1-1 0-3 1-4 2h-2l2 1h0c-3 0-6 0-8 1l-1 1h0c-3 2-7 4-9 6-4 3-6 6-9 10v-1c-3 2-5 8-6 11 0 1-1 3-1 3-1 1-2 2-3 2l-1-1h-2 0v2c-1 0-1 0-1-1h0l-2-6c1 0 0-1 0-2 2-1 2-1 3-2s1-2 1-4c0-1 1-3 1-4 0-2 1-5 2-7v-6c1-4 2-8 2-13v-6c0 1 0 4 1 5l1 2c0 1 0 1 1 1v2 1c1-1 2-3 2-4 1-1 1-3 3-4v1l2-2c1 1 2 1 4 1 3-3 8-5 12-7h2c2 0 4-1 6-1 2-1 4 0 5 0l4-1c-2 0-6 1-7 0v-1-1h1c-2 0-3 0-4-1h2c2 0 2 0 3-1h0c1-1 2-1 3-1 2-1 4-1 6-2 1-1 2-3 3-5 2-1 3-3 4-4 6-5 12-8 18-13 1-1 3-2 4-3 3-3 6-6 8-9 3-4 5-8 7-13l1-1z"></path><path d="M419 72c2 0 4-1 7-2v-1l1 1c-3 3-9 5-13 6h-1v-1h0l6-3z" class="D"></path><path d="M426 66l4-4c-1 2-2 7-3 8l-1-1v1c-3 1-5 2-7 2l2-2c2-1 4-3 5-4z" class="I"></path><path d="M421 78h2 0c1 0 2 0 3-1h0l1-1c1-1 2-1 3-2 2-3 1-5 4-7 0 3-1 6-2 8l-3 3v-1c-2 1-4 2-5 3h-2 0-3-1c1-1 2-1 3-2z" class="G"></path><path d="M408 78c2-1 3-2 5-3v1h1v1l7 1c-1 1-2 1-3 2-3 0-6 1-9 2l-1-1v-1-2z" class="c"></path><path d="M408 78c2-1 3-2 5-3v1h1v1s-1 0-1 1c-1 1-3 1-5 2v-2z" class="F"></path><path d="M414 73l-7 2 9-6c3-2 5-3 7-5 3-2 5-4 8-6-2 3-3 4-6 7l1 1c-1 1-3 3-5 4l-2 2-6 3 1-1v-1z" class="e"></path><path d="M420 69c1-2 3-3 5-4l1 1c-1 1-3 3-5 4-1 0-1 0-1-1z" class="g"></path><path d="M420 69c0 1 0 1 1 1l-2 2-6 3 1-1v-1c1-1 3-1 4-2l2-2z" class="M"></path><path d="M434 67l4-12v9c-1 2-2 4-2 6v-1c1 0 1 0 1-1s1-1 1-2v-1c0-2 1-2 2-3-1 2-1 4-2 6l1 1v1h0c1 1 1 2 0 3v1h1c-2 1-2 0-3 1 0 1 1 1 1 2h0-3c0 1-1 1-1 2l-1 2-1 1v2h0c-1 2-2 3-3 4 0 1 0 1-1 2v1h-1c0-1-1-1-2-2 2-1 3-2 4-4-1 0-2-1-3-1l1-2 1-2 1-2 3-3c1-2 2-5 2-8z" class="B"></path><path d="M428 80c1 0 1 0 1 1 1 0 0 0 1 1 1-2 1-3 1-4l1-1 1-1v1 1c-1 2-2 4-4 7h0c-1 0-2-1-3-1l1-2 1-2z" class="O"></path><path d="M427 82c1 0 1 0 2 1v2h0c-1 0-2-1-3-1l1-2z" class="g"></path><path d="M438 68l1 1v1h0c1 1 1 2 0 3v1h1c-2 1-2 0-3 1 0 1 1 1 1 2h0-3c0-1 0-2 1-3 0-2 1-4 2-6z" class="R"></path><path d="M433 78c1 0 1 0 1 1l-1 2-1 1v2h0c-1 2-2 3-3 4 0 1 0 1-1 2v1h-1c0-1-1-1-2-2 2-1 3-2 4-4h0c2-3 3-5 4-7z" class="d"></path><defs><linearGradient id="AW" x1="414.273" y1="86.73" x2="380.522" y2="85.374" xlink:href="#B"><stop offset="0" stop-color="#0e0e10"></stop><stop offset="1" stop-color="#3c3a3a"></stop></linearGradient></defs><path fill="url(#AW)" d="M406 80l2-2v2 1l1 1c3-1 6-2 9-2h1 3l-8 5c-3 2-7 5-10 6-1 0-5 1-6 1-2 1-3 1-5 1-1 0-2 1-3 1l-1-1h-3l4-1c-2 0-6 1-7 0v-1-1h1c-2 0-3 0-4-1h2c2 0 2 0 3-1h0c1-1 2-1 3-1v1h3c5 0 10-2 13-5 1-1 1-2 2-3h0z"></path><path d="M406 80l2-2v2 1l1 1h0l-4 2h0l1-2v-2z" class="B"></path><path d="M394 92c0-1 1-1 2-2h1c2-1 4-2 7-2l-2 1c-3 2-5 2-8 3z" class="V"></path><path d="M394 92c3-1 5-1 8-3l-1 2h3c-1 0-5 1-6 1-2 1-3 1-5 1-1 0-2 1-3 1l-1-1h-3l4-1h4z" class="N"></path><defs><linearGradient id="AX" x1="407.081" y1="92.801" x2="411.663" y2="79.41" xlink:href="#B"><stop offset="0" stop-color="#818081"></stop><stop offset="1" stop-color="#9b9b9b"></stop></linearGradient></defs><path fill="url(#AX)" d="M419 80h3l-8 5c-3 2-7 5-10 6h-3l1-2 2-1c1 0 2-1 3-2l12-6z"></path><path d="M422 80h0 2c1-1 3-2 5-3v1l-1 2-1 2-1 2c1 0 2 1 3 1-1 2-2 3-4 4 1 1 2 1 2 2h1v-1c1-1 1-1 1-2l2 1c0 1 1 1 2 1v1 2h-1l-1 1v-1 1h-5c1 1 1 1 2 1v1c-1 0-3-1-3 0-1 0-2 1-2 1-1 1-2 1-2 1-1 1-2 1-2 1h-1c-1 0-2 1-3 1-1 1-2 2-4 2h0l-2 2c-1 0-2 0-2 1-2 1-3 1-5 1-1-1-1-1-1-2 4-1 6-3 9-5l2-2 1-1v-1c-3 0-7 1-10 0h0l-5 1h0c-3 1-5 1-7 2l-6 1-1 1-2-2c-1 1-2 1-3 1h-3s-1 0-1-1h-1 0c-1-1-1-1-2-1 0 0 1 0 1-1h0v-1-1h2c2 0 4-1 6-1 2-1 4 0 5 0h3l1 1c1 0 2-1 3-1 2 0 3 0 5-1 1 0 5-1 6-1 3-1 7-4 10-6l8-5z" class="h"></path><path d="M421 89l3-2c0 1 1 1 1 2l-2 2v-1c-1 0-2 0-2-1z" class="S"></path><path d="M426 84c1 0 2 1 3 1-1 2-2 3-4 4h0c0-1-1-1-1-2 1-1 2-2 2-3z" class="O"></path><path d="M403 95c2-2 2-1 3-2 1 0 2 0 3 1 1 0 2 0 3-1v1c1 0 1 1 1 1-3 0-7 1-10 0z" class="j"></path><path d="M420 92l-3 3c0-2 0-4 1-5l3-1c0 1 1 1 2 1v1 1h-3z" class="N"></path><path d="M427 91h1v-1c1-1 1-1 1-2l2 1c0 1 1 1 2 1v1 2h-1l-1 1v-1 1h-5c1 1 1 1 2 1v1c-1 0-3-1-3 0-1 0-2 1-2 1-1 1-2 1-2 1-1 1-2 1-2 1h-1c-1 0-2 1-3 1-1 1-2 2-4 2h0c2-1 3-2 5-4 1-1 5-2 6-3l1-2h6c-2-1-4-1-6-1h-3 3v-1l2-2h0c1 1 2 1 2 2z" class="Q"></path><path d="M425 89h0c1 1 2 1 2 2-1 1-3 1-4 1v-1l2-2z" class="e"></path><path d="M431 89c0 1 1 1 2 1v1 2h-1l-1 1v-1c-1 0-2 0-2-1h2v-1h-2v-1l2-1z" class="U"></path><defs><linearGradient id="AY" x1="381.539" y1="95.259" x2="377.36" y2="99.717" xlink:href="#B"><stop offset="0" stop-color="#444442"></stop><stop offset="1" stop-color="#5e5b5f"></stop></linearGradient></defs><path fill="url(#AY)" d="M373 94h2c2 0 4-1 6-1 2-1 4 0 5 0h3l1 1c1 0 2-1 3-1 2 0 3 0 5-1v1c-1 0-2 0-3 1l-4 1c4 0 8-1 12 0l-5 1h0c-3 1-5 1-7 2l-6 1-1 1-2-2c-1 1-2 1-3 1h-3s-1 0-1-1h-1 0c-1-1-1-1-2-1 0 0 1 0 1-1h0v-1-1z"></path><path d="M382 98l3-1c2-1 7-2 9-1h3 1c-3 1-5 1-7 2l-6 1-1 1-2-2z" class="S"></path><path d="M373 94h2c2 0 4-1 6-1 2-1 4 0 5 0h3l1 1c-1 0-7 2-8 2-2 0-5 1-7 2h-1 0c-1-1-1-1-2-1 0 0 1 0 1-1h0v-1-1z" class="R"></path><path d="M373 96h3c1 0 5-2 6-1v1c-2 0-5 1-7 2h-1 0c-1-1-1-1-2-1 0 0 1 0 1-1z" class="C"></path><defs><linearGradient id="AZ" x1="362.187" y1="117.12" x2="354.167" y2="112.347" xlink:href="#B"><stop offset="0" stop-color="#cdcbcc"></stop><stop offset="1" stop-color="#f0eff0"></stop></linearGradient></defs><path fill="url(#AZ)" d="M361 101c3-3 8-5 12-7v1 1h0c0 1-1 1-1 1 1 0 1 0 2 1h0 1c0 1 1 1 1 1h3c1 0 2 0 3-1l2 2 1-1 6-1c2-1 4-1 7-2h0l5-1h0c3 1 7 0 10 0v1l-1 1-2 2c-3 2-5 4-9 5h-3l-2 1h-1v1 3c-1 0-1 1-2 1h-1l-5 1c-1 0-1 0-2 1-1 0-3 1-4 2h-2l2 1h0c-3 0-6 0-8 1l-1 1h0c-3 2-7 4-9 6-4 3-6 6-9 10v-1c-3 2-5 8-6 11 0 1-1 3-1 3-1 1-2 2-3 2l-1-1h-2 0v2c-1 0-1 0-1-1h0l-2-6c1 0 0-1 0-2 2-1 2-1 3-2s1-2 1-4c0-1 1-3 1-4 0-2 1-5 2-7v-6c1-4 2-8 2-13v-6c0 1 0 4 1 5l1 2c0 1 0 1 1 1v2 1c1-1 2-3 2-4 1-1 1-3 3-4v1l2-2c1 1 2 1 4 1z"></path><path d="M357 105l1-1c1-1 2-1 4-1-2 2-5 3-6 6l-3 1 1-2 3-3z" class="P"></path><path d="M353 122c2-1 2-3 4-3-1 1-2 2-3 4 0 0 0 2-1 3s-1 2-1 4h0c-1 1-1 2-2 2v1l-1 1c0-3 1-5 2-7s1-4 2-5z" class="T"></path><path d="M361 101c3-3 8-5 12-7v1 1h0c0 1-1 1-1 1-4 2-7 4-10 6-2 0-3 0-4 1l-1 1c0-1 0-1 1-1l3-3z" class="H"></path><path d="M356 109c-3 4-4 8-6 13-1 2-2 6-2 9-1 0-1 2-1 2v2h1l1-1-2 7c0 1-1 1-1 2h-1v-2c-1-1-1-2-1-4l1-5c1-3 2-7 3-10 0-2 1-4 2-6h0l1-1v-2l2-3 3-1z" class="C"></path><path d="M347 141h-1l-1-1s0-1 1-2 0-3 1-5v2h1l1-1-2 7z" class="l"></path><defs><linearGradient id="Aa" x1="350.826" y1="116.245" x2="341.251" y2="128.669" xlink:href="#B"><stop offset="0" stop-color="#757373"></stop><stop offset="1" stop-color="#909191"></stop></linearGradient></defs><path fill="url(#Aa)" d="M347 98c0 1 0 4 1 5l1 2c0 1 0 1 1 1v2 1c1-1 2-3 2-4 1-1 1-3 3-4v1l2-2c1 1 2 1 4 1l-3 3c-1 0-1 0-1 1l-3 3-1 2-2 3v2l-1 1h0c-1 2-2 4-2 6-1 3-2 7-3 10l-1-1h0c-1 1-1 2-2 3 0-1 1-3 1-4 0-2 1-5 2-7v-6c1-4 2-8 2-13v-6z"></path><path d="M348 107c0 2 1 4 2 5v2h0c1 0 0 0 1 1l-1 1s-1 0-1-1h0c-1-3-1-5-1-8z" class="I"></path><path d="M355 101v1l2-2c1 1 2 1 4 1l-3 3c-1 0-1 0-1 1l-3 3-1 2-2 3v2c-1-1 0-1-1-1h0v-2c-1-1-2-3-2-5v-4l1 2c0 1 0 1 1 1v2 1c1-1 2-3 2-4 1-1 1-3 3-4z" class="Z"></path><path d="M351 113h0c-1-2 1-6 2-8l1-1 1-1v1c0 1-1 2-1 4h0l-1 2-2 3z" class="S"></path><path d="M368 118l1-1c1 0 2-1 3 0-3 2-7 4-9 6-4 3-6 6-9 10v-1c-3 2-5 8-6 11 0 1-1 3-1 3-1 1-2 2-3 2l-1-1h-2 0v2c-1 0-1 0-1-1h0l-2-6c1 0 0-1 0-2 2-1 2-1 3-2s1-2 1-4c1-1 1-2 2-3h0l1 1-1 5c0 2 0 3 1 4v2h1c0-1 1-1 1-2l2-7h0l1-1v-1c1 0 1-1 2-2h0c0-2 0-3 1-4l2-1 1-1h0v1l-1 4c2-2 4-5 7-7 1-2 4-3 6-4z" class="E"></path><path d="M352 130h0c0-2 0-3 1-4l2-1c-1 2-2 4-3 5z" class="C"></path><path d="M342 134c1-1 1-2 2-3h0l1 1-1 5c0 2-1 3-1 4v6h0-2 0v2c-1 0-1 0-1-1h0l-2-6c1 0 0-1 0-2 2-1 2-1 3-2s1-2 1-4z" class="D"></path><path d="M341 140l1-1c1 1 1 3 1 5v3h0-2 0v2c-1 0-1 0-1-1h0c1-2 1-6 1-8z" class="O"></path><path d="M341 138v2c0 2 0 6-1 8l-2-6c1 0 0-1 0-2 2-1 2-1 3-2z" class="K"></path><path d="M374 98h1c0 1 1 1 1 1h3c1 0 2 0 3-1l2 2h-2c-1 1-3 1-4 2h0l-1 1 2 1c-1 0-3 0-4 1v1c-1 1-3 1-4 2-3 1-5 3-8 5l-3 2c0 1-1 2-2 3l-1 1c-2 0-2 2-4 3v-2c2-4 2-7 5-10l7-7 6-3 2-1 1-1z" class="C"></path><path d="M378 102h0l-1 1 2 1c-1 0-3 0-4 1-3 1-7 4-10 5h0c4-4 8-6 13-8z" class="f"></path><path d="M365 110h0c3-1 7-4 10-5v1c-1 1-3 1-4 2-3 1-5 3-8 5l-3 2c0-1 0-1 1-2 0-1 2-3 4-3z" class="Y"></path><defs><linearGradient id="Ab" x1="367.778" y1="99.914" x2="363.962" y2="109.532" xlink:href="#B"><stop offset="0" stop-color="#777575"></stop><stop offset="1" stop-color="#9d9b9d"></stop></linearGradient></defs><path fill="url(#Ab)" d="M374 98h1c0 1 1 1 1 1h3v1c-2 0-3 1-4 1-2 1-4 2-7 4-3 1-6 4-9 6l-1-1 7-7 6-3 2-1 1-1z"></path><path d="M374 98h1c0 1 1 1 1 1h3v1c-2 0-3 1-4 1s-3 0-4-1l2-1 1-1z" class="N"></path><path d="M374 98h1c0 1 1 1 1 1h3v1c-1-1-1-1-1 0-2 0-3 0-5-1l1-1z" class="I"></path><defs><linearGradient id="Ac" x1="377.046" y1="121.943" x2="367.191" y2="112.392" xlink:href="#B"><stop offset="0" stop-color="#d7d5d7"></stop><stop offset="1" stop-color="#fbfafa"></stop></linearGradient></defs><path fill="url(#Ac)" d="M372 112c6-3 14-4 20-3v1l-5 1c-1 0-1 0-2 1-1 0-3 1-4 2h-2l2 1h0c-3 0-6 0-8 1l-1 1h0c-1-1-2 0-3 0l-1 1c-2 1-5 2-6 4-3 2-5 5-7 7l1-4v-1h0l-1 1-2 1c1-1 1-3 1-3 1-2 2-3 3-4l1-1c1-1 2-2 2-3l3-2v1h1c2-2 4-2 6-3h2v1z"></path><path d="M368 118v-2c2-2 3-2 6-2v1s0 1-1 1l-1 1h0c-1-1-2 0-3 0l-1 1z" class="f"></path><path d="M370 111h2v1c-2 1-5 1-7 3 0 1-2 2-3 2-2 2-5 5-6 8h0v-1h0l-1 1-2 1c1-1 1-3 1-3 1-2 2-3 3-4l1-1c1-1 2-2 2-3l3-2v1h1c2-2 4-2 6-3z" class="Y"></path><path d="M370 111h2v1c-2 1-5 1-7 3 0 1-2 2-3 2h1c0-1 1-2 2-3-1 0-3 2-5 3l-1 1h-1c1-1 2-2 2-3l3-2v1h1c2-2 4-2 6-3z" class="i"></path><path d="M403 95c3 1 7 0 10 0v1l-1 1-2 2c-3 2-5 4-9 5h-3l-2 1h-1v1 3c-1 0-1 1-2 1h-1v-1c-6-1-14 0-20 3v-1h-2c-2 1-4 1-6 3h-1v-1c3-2 5-4 8-5 1-1 3-1 4-2v-1c1-1 3-1 4-1l-2-1 1-1h0c1-1 3-1 4-2h2l1-1 6-1c2-1 4-1 7-2h0l5-1h0z" class="m"></path><path d="M371 108v1c-1 0-3 1-4 2l-1 1c1 0 2 0 2-1h2 0c-2 1-4 1-6 3h-1v-1c3-2 5-4 8-5z" class="f"></path><path d="M391 98c1 0 2-1 3 0-1 1-3 2-5 3 2 0 4 0 5-1 1 0 2 0 2 1v1h0c-1 0-3 1-4 1v-1h1 0-1-2-2c-2 1-4 1-6 2-2 0-5 1-7 2v-1c1-1 3-1 4-1l-2-1 1-1h0c1-1 3-1 4-2h2l1-1 6-1z" class="T"></path><path d="M384 100l1-1c1 0 3 0 3 1-1 1-7 3-9 4l-2-1 1-1h0c1-1 3-1 4-2h2z" class="m"></path><defs><linearGradient id="Ad" x1="392.954" y1="108.864" x2="385.036" y2="103.003" xlink:href="#B"><stop offset="0" stop-color="#a3a1a3"></stop><stop offset="1" stop-color="#c5c5c4"></stop></linearGradient></defs><path fill="url(#Ad)" d="M396 102v1c2 0 3-1 5-1 4-1 5-2 8-4l1 1c-3 2-5 4-9 5h-3l-2 1h-1v1 3c-1 0-1 1-2 1h-1v-1c-6-1-14 0-20 3v-1c4-2 9-3 13-4 3-1 4-3 7-4 1 0 3-1 4-1z"></path><path d="M403 95c3 1 7 0 10 0v1l-1 1-2 2-1-1c-3 2-4 3-8 4-2 0-3 1-5 1v-1h0v-1c0-1-1-1-2-1-1 1-3 1-5 1 2-1 4-2 5-3-1-1-2 0-3 0 2-1 4-1 7-2h0l5-1h0z" class="P"></path><path d="M394 100h0c1-1 2-1 3-1h1c2-1 3-1 5-1l1 1-8 3v-1c0-1-1-1-2-1z" class="L"></path><path d="M404 99c1 0 2-1 4-1 1-1 2-1 4-1l-2 2-1-1c-3 2-4 3-8 4-2 0-3 1-5 1v-1h0l8-3z" class="Q"></path><path d="M239 229c1 0 2 1 2 2v1c1 1 1 1 2 1 1-1 2-1 3-2 3 1 5 1 7 3h0c2 1 2 3 2 5 1 0 1 0 2 1 0 1-1 2-1 2 1 1 1 2 2 3l2 1h0 1l1 1h0c1 0 2 1 3 1 3 2 4 8 5 11h1l2 15v2h0-1l2 7c-1 2-1 5 0 7h0c1 1 1 2 1 3s0 1 1 1v11 11c-1 4-1 7-1 10h0-1l-1-1-6-6c-2-3-3-5-5-7-1 0-2-1-2-2h0 1c0-1 0-1-1-1h-2l-3-3-4-1-6-4-5-2c-2-1-4-2-5-3l-2-2c-1-2-2-3-5-4v-1h0 6l-2-1-2-1-1-1h-1c1 0 2 0 4-1l-2-1c-4-3-9-5-12-8l-4-4c-1-1-2-3-2-4-1-2-2-4-2-5-1-1-1-2-1-3v-1-4c0-1 1-2 1-2-1-1-1-2 0-3v-1c0-2 1-3 2-4v-2c0 2 0 5 1 7 1 3 2 5 3 8 1 2 3 5 5 7l2-1c-1-2-2-3-3-5 1-1 1-3 1-4l2-2-1-1v-1l2-2 4-5 1-1h1l2-3 2-6 1-1 2-2 1-2h1z" class="h"></path><path d="M225 260c-1-2-1-3 0-4v-1 1c1 1 2 3 3 5-1 0-1 1-2 1h0c0-1-1-1-1-2z" class="e"></path><path d="M240 247l4 3c1 1 4 2 5 1h1v2h-1c-2 1-4 0-6-1s-3-3-3-5z" class="Z"></path><path d="M228 244l1-1h1c-2 4-3 7-7 10l-1-1v-1l2-2 4-5z" class="E"></path><path d="M255 246l-1-1c1-1 1-2 1-3h1c1 1 1 2 2 3l2 1h0 1l1 1h-2c-1 0-1 0-2 1-2 0-6 3-8 5v-2c0-1 0-1 1-2l1-1c1 0 2-1 3-2z" class="U"></path><path d="M255 246l-1-1c1-1 1-2 1-3h1c1 1 1 2 2 3l2 1h0 1l1 1h-2c-2-1-3-1-5-1z" class="D"></path><defs><linearGradient id="Ae" x1="238.465" y1="242.772" x2="243.57" y2="239.595" xlink:href="#B"><stop offset="0" stop-color="#a19fa2"></stop><stop offset="1" stop-color="#bebcba"></stop></linearGradient></defs><path fill="url(#Ae)" d="M240 247c-2-6-1-10 1-15 1 1 1 1 2 1l-2 4v5 3c1 2 4 4 3 5l-4-3z"></path><path d="M270 271l2 5 2 7c-1 2-1 5 0 7h0c1 1 1 2 1 3s0 1 1 1v11c-1 0-1-5-1-6l-1-1v-3l-4-24z" class="Z"></path><path d="M220 259c1-1 1-3 1-4 0 2 0 3 1 5 1 1 1 1 2 1h1v-1c0 1 1 1 1 2h0c1 0 1-1 2-1l2 5h-2v2l1 1-1 2-7-6 2-1c-1-2-2-3-3-5z" class="M"></path><path d="M226 262c1 0 1-1 2-1l2 5h-2v2l1 1-1 2-7-6 2-1 5 5v-2c-1-2-1-3-2-5z" class="N"></path><path d="M239 258v1c0 1 1 2 1 3 1-1 0-2 1-4h1c1 2 3 3 5 3h1l-1 3h-2 0-2l-2-2c-1 2 1 6 0 7h-1-1v1l-2-5-1-2c0-2 0-3 1-5l2 1v-1z" class="Q"></path><path d="M237 265c0-1 0-1 1-2h1v6 1l-2-5z" class="B"></path><path d="M239 258v1c0 1 1 2 1 3 1-1 0-2 1-4h1c1 2 3 3 5 3h1l-1 3h-2 0l-1-2v-1h0-2 0c0-2 0-2-1-3-1 2 0 3-1 5h-1v-4-1z" class="j"></path><path d="M256 256c-1-1-1-1-2-1 0-1 1-2 2-2 2-1 7-2 9 0 2 0 3 4 4 6h1 0 1l2 15v2h0-1l-2-5h0c-4-7-7-12-14-15z" class="E"></path><path d="M256 256c0-1 1-1 1-1 3-1 5 0 8 1v1l3 3c1 3 2 4 3 7 0 1 0 2-1 4h0c-4-7-7-12-14-15z" class="f"></path><path d="M246 231c3 1 5 1 7 3h0c2 1 2 3 2 5 1 0 1 0 2 1 0 1-1 2-1 2h-1c0 1 0 2-1 3l1 1c-1 1-2 2-3 2l-1 1c-1 1-1 1-1 2h-1c-1 1-4 0-5-1 1-1-2-3-3-5v-3-5l2-4c1-1 2-1 3-2z" class="H"></path><path d="M251 243l1 1c-1 1-1 2-2 2h-2-2 0v-1-2c2 0 2 1 3 1 2 0 2 0 2-1z" class="L"></path><path d="M253 244l1 1c0 1 0 1-1 2h-1v1l-1 1c-1 0-2 0-3-1 1-1 2-2 3-2 0-1 1-1 2-2z" class="J"></path><path d="M255 239c1 0 1 0 2 1 0 1-1 2-1 2h-1c0 1 0 2-1 3l1 1c-1 1-2 2-3 2v-1h1c1-1 1-1 1-2l-1-1h0-1l-1-1c1-1 2-1 2-1h1v-1-1l1-1z" class="E"></path><path d="M241 237h1v-1l1 1v2l1 1h0v2h2v1h0v2 1h0v1h0c-1 0-2-2-3-3h0c-2 0-1-1-2-2v-5z" class="c"></path><path d="M242 236l1 1v2l1 1h0l-1 2c-1-2-1-3-1-5v-1z" class="D"></path><path d="M243 242l1-2v2h2v1h0v2 1l-3-4z" class="J"></path><path d="M250 238h2c0 1 1 2 1 3h0 0 1v1h-1s-1 0-2 1c0 1 0 1-2 1-1 0-1-1-3-1h0v-1h0v-2c1 0 2-1 2-1h1c1 0 1 0 1-1z" class="H"></path><path d="M246 240c1 0 2-1 2-1h1v1h1l2 1c-1 0-1 1-1 1h-2c-1 0-1 1-1 1h-2v-1h0v-2z" class="R"></path><path d="M246 240c1 0 2-1 2-1h1v1c-1 1-2 1-3 2v-2z" class="K"></path><path d="M246 231c3 1 5 1 7 3h0c2 1 2 3 2 5l-1 1v1h-1 0 0c0-1-1-2-1-3h-2c0 1 0 1-1 1h-1s-1 1-2 1v2h0-2v-2h0l-1-1v-2l-1-1v1h-1l2-4c1-1 2-1 3-2z" class="b"></path><path d="M242 236c1 0 1-1 1-2 1 0 2-1 3-1 2 0 3 0 4 2-1-1-2-1-3-1l-1 1-2 5-1-1v-2l-1-1z" class="W"></path><path d="M246 235l1-1c1 0 2 0 3 1l1 1c0 1-1 1-1 2s0 1-1 1h-1s-1 1-2 1v2h0-2v-2h0l2-5z" class="a"></path><path d="M246 235l1-1c1 0 2 0 3 1l1 1c0 1-1 1-1 2s0 1-1 1h-1s-1 1-2 1c0-1 2-2 3-3v-1c-1 0-2-1-3-1z" class="L"></path><path d="M243 279c2 0 2 0 3 1l1 1c2 0 4 2 6 3h0 1c1 0 2 0 2 1 0-1-1-2-2-2v-1c-2-1-5-2-7-3-1-1-3-2-4-3s-2-1-3-1c-2-1-4-4-5-6h0c0-2-1-2-1-4 3-3 2-5 3-8 0-1 1-2 2-2h0v3 1l-2-1c-1 2-1 3-1 5l1 2 2 5c0 1 2 4 3 5s3 2 4 2c2 1 5 3 7 4 1 1 3 2 4 3 2 2 2 3 4 4l2 2 3 4s0 1 1 2 1 2 2 3c1-1 0-9 0-11 1-1 1-2 0-3l2 9c2 3 1 4 1 7v1 3 3c1 2 1 3 0 5-1-1-2-2-2-3-2-3-4-6-6-8l-2-2h0l-6-5v-1c1 0 2 0 2 1 1 0 3 2 4 2h1c-1-1-1-3-1-4-2-3-4-5-6-7-3-2-8-4-11-6l-2-1z" class="d"></path><path d="M269 285l2 9c0 4 0 7 1 11l-3-6c1-1 0-9 0-11 1-1 1-2 0-3zm-7 8c1 1 4 4 4 5v3c-1 0-2-2-3-2l-1 1h0l-6-5v-1c1 0 2 0 2 1 1 0 3 2 4 2h1c-1-1-1-3-1-4z" class="B"></path><defs><linearGradient id="Af" x1="250.13" y1="300.066" x2="252.776" y2="293.336" xlink:href="#B"><stop offset="0" stop-color="#3d3d3d"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#Af)" d="M245 280c3 2 8 4 11 6 2 2 4 4 6 7 0 1 0 3 1 4h-1c-1 0-3-2-4-2 0-1-1-1-2-1v1l6 5h0l2 2c1 1 3 3 3 5h-1-1l-3-4c-5-5-11-7-18-10-1 0-2-1-4-1l-6-3-2-1-2-1-1-1h-1c1 0 2 0 4-1l2 1 3 1 5 2c1 0 2 1 3 1l1-1-1-1h0l4 1c0-1-1-1-1-2l-3-2-2-2 1-1c1 0 2 1 3 1-1 0-1-1-2-2h-1l1-1z"></path><path d="M232 285l2 1 3 1-1 1h-2c-1-1-2-1-4-1l-1-1h-1c1 0 2 0 4-1z" class="U"></path><path d="M232 285l2 1-1 1-4-1h-1c1 0 2 0 4-1z" class="F"></path><path d="M256 286c2 2 4 4 6 7 0 1 0 3 1 4h-1c-1 0-3-2-4-2 0-1-1-1-2-1v1c-2 0-3 0-4-1l-4-2h1c-1 0-1 0 0 0l5 1c-2-2-5-3-8-4l-1-1h0l4 1c2 1 4 1 5 2 2 1 3 2 5 2h0c-1-2-3-3-4-5l1-2z" class="b"></path><path d="M245 280c3 2 8 4 11 6l-1 2c1 2 3 3 4 5h0c-2 0-3-1-5-2-1-1-3-1-5-2 0-1-1-1-1-2l-3-2-2-2 1-1c1 0 2 1 3 1-1 0-1-1-2-2h-1l1-1z" class="D"></path><path d="M245 285l-2-2 1-1c1 0 2 1 3 1 2 2 5 4 7 6-2-1-4-2-6-2l-3-2z" class="T"></path><path d="M228 290v-1h0 6l6 3c2 0 3 1 4 1 7 3 13 5 18 10l3 4h1 1c0-2-2-4-3-5 2 2 4 5 6 8 0 1 1 2 2 3h1c1 1 1 1 2 1v-9c-1-3-1-7-1-10v3l1 1c0 1 0 6 1 6v11c-1 4-1 7-1 10h0-1l-1-1-6-6c-2-3-3-5-5-7-1 0-2-1-2-2h0 1c0-1 0-1-1-1h-2l-3-3-4-1-6-4-5-2c-2-1-4-2-5-3l-2-2c-1-2-2-3-5-4z" class="S"></path><path d="M262 303l3 4h0v2 1l-4-4h1v-3z" class="k"></path><path d="M255 306h0c0-1-2-2-2-3 1 1 3 2 5 3v-1c2 2 4 4 6 5v1c-1 0-2-1-3-1 0-1 0-1-1-1h-2l-3-3z" class="N"></path><path d="M245 301c1-1 1-1 2-1 2 0 4 3 6 2 2 0 4 2 5 3v1c-2-1-4-2-5-3 0 1 2 2 2 3h0l-4-1-6-4z" class="D"></path><defs><linearGradient id="Ag" x1="255.862" y1="307.284" x2="251.663" y2="293.362" xlink:href="#B"><stop offset="0" stop-color="#858383"></stop><stop offset="1" stop-color="#b2b2b2"></stop></linearGradient></defs><path fill="url(#Ag)" d="M244 293c7 3 13 5 18 10v3h-1l-2-1-9-6c-1 0-2-1-3-1h0c-2-1-3-1-4-2v-1c1 1 1 1 2 1v-2c-1 0 0 0-1-1z"></path><path d="M274 295v3l1 1c0 1 0 6 1 6v11c-1 1-2 1-2 1-1 1-1 3-1 5h-1c-1-1-1-2-2-3l-6-9c2 1 3 2 4 3l1 1h0c-1-2-3-5-3-7h1c0-2-2-4-3-5 2 2 4 5 6 8 0 1 1 2 2 3h1c1 1 1 1 2 1v-9c-1-3-1-7-1-10z" class="B"></path><path d="M228 290v-1h0 6l6 3c2 0 3 1 4 1 1 1 0 1 1 1v2c-1 0-1 0-2-1v1c1 1 2 1 4 2h0c2 1 4 2 6 4-2 1-4-2-6-2-1 0-1 0-2 1l-5-2c-2-1-4-2-5-3l-2-2c-1-2-2-3-5-4z" class="T"></path><path d="M235 296c2 0 5 0 6 2 1 0 1 0 1 1h0 1c1 0 1 1 2 0h2 0v-1h0c2 1 4 2 6 4-2 1-4-2-6-2-1 0-1 0-2 1l-5-2c-2-1-4-2-5-3z" class="E"></path><path d="M212 245v-2c0 2 0 5 1 7l3 8c1 2 3 5 5 7l7 6 1-2-1-1v-2h2l3 4c2 3 6 8 10 9h0l2 1-1 1h1c1 1 1 2 2 2-1 0-2-1-3-1l-1 1 2 2 3 2c0 1 1 1 1 2l-4-1h0l1 1-1 1c-1 0-2-1-3-1l-5-2-3-1-2-1-2-1c-4-3-9-5-12-8l-4-4c-1-1-2-3-2-4-1-2-2-4-2-5-1-1-1-2-1-3v-1-4c0-1 1-2 1-2-1-1-1-2 0-3v-1c0-2 1-3 2-4z" class="W"></path><path d="M209 259h1c0 1 1 1 1 2 1 1 1 1 2 1 1 2 2 3 3 4h0 1 0l1 1h-2c-1-1-1-1-3-1-1-1-1-2-2-2v-1h-1c-1-1-1-2-1-3v-1z" class="Z"></path><path d="M217 266c-1-1-1-1 0-2 3 4 7 7 11 9l1-1 2 2v1h0l-1 1-1-1c-2-2-5-2-7-3-1-1-2-1-3-2-1 0-2-2-3-3h2l-1-1h0z" class="I"></path><path d="M215 270c1 0 2 0 4 1 2 2 4 2 6 4 2 1 3 2 5 3s5 3 7 4c-1 1-1 1-2 1h-1 0c-1 0-3-2-4-2l-9-6c0-1-1-1-2-1-2-1-3-3-4-4z" class="D"></path><path d="M214 259h1s0-1 1-1h0c1 2 3 5 5 7l7 6 1 1-1 1c-4-2-8-5-11-9-1 1-1 1 0 2h-1 0c-1-1-2-2-3-4l-1-2c1 0 2 1 3 1s1 0 2-1l-3-1z" class="Q"></path><path d="M213 262c2 0 2-1 4 0v2h0c-1 1-1 1 0 2h-1 0c-1-1-2-2-3-4z" class="B"></path><path d="M212 245v-2c0 2 0 5 1 7l3 8h0c-1 0-1 1-1 1h-1l3 1c-1 1-1 1-2 1s-2-1-3-1l1 2c-1 0-1 0-2-1 0-1-1-1-1-2h-1v-4c0-1 1-2 1-2-1-1-1-2 0-3v-1c0-2 1-3 2-4z" class="e"></path><path d="M211 252h1c0 2 0 5 2 7l3 1c-1 1-1 1-2 1s-2-1-3-1v-1c-1 0-1-1-1-2s-1-4 0-5z" class="G"></path><path d="M211 252v-2h2l3 8h0c-1 0-1 1-1 1h-1c-2-2-2-5-2-7h-1z" class="B"></path><path d="M212 268l1-1c0 1 1 2 2 3s2 3 4 4c1 0 2 0 2 1l9 6c1 0 3 2 4 2l8 5v1l-5-2-3-1-2-1-2-1c-4-3-9-5-12-8l-4-4c-1-1-2-3-2-4z" class="C"></path><path d="M228 271l1-2-1-1v-2h2l3 4c2 3 6 8 10 9h0l2 1-1 1h1c1 1 1 2 2 2-1 0-2-1-3-1l-1 1 2 2c-3 0-9-4-12-6h0 4l-6-4v-1l-2-2-1-1z" class="E"></path><path d="M228 271l1-2-1-1v-2h2l3 4c-1 0-2 0-2-1-1 2-1 2 0 4v2-1l-2-2-1-1z" class="P"></path><path d="M248 261c1 0 1 0 2 1 2 0 3 0 5-1h3c0 1 1 1 1 2 1 0 2 1 2 1 0 1 1 2 2 3s2 3 3 5 2 4 2 6v2l1 5c1 1 1 2 0 3 0 2 1 10 0 11-1-1-1-2-2-3s-1-2-1-2l-3-4-2-2c-2-1-2-2-4-4-1-1-3-2-4-3-2-1-5-3-7-4-1 0-3-1-4-2s-3-4-3-5v-1h1 1c1-1-1-5 0-7l2 2h2 0 2l1-3z" class="k"></path><path d="M262 279l2 5h-4v-1l-1-2v-2c1 0 2 0 2 1l1-1z" class="D"></path><path d="M259 281h1c1 0 2 0 2 1v1h-2l-1-2zm-3-9h4 0l2 7-1 1c0-1-1-1-2-1h0c-1-1-1-1-1-2 0-2-1-3-2-5z" class="E"></path><path d="M260 272l2 7-1 1c0-1-1-1-2-1h0l1-1v-3-3zm-8 0h1c1 1 1 1 2 1v1c1 1 1 2 2 3s1 2 1 3c0 2 1 3 1 4l-3-3-1-1-4-2c-1-1-2-2-3-2h-1v-1c0-1 0-1-1-1v-1h1l2-1h3z" class="L"></path><path d="M251 278l1-1c2 0 2 0 3 1v2l-4-2zm1-6h1c1 1 1 1 2 1v1c0 1-1 1-1 2l-2-2-1-1 1-1z" class="E"></path><path d="M249 272h3l-1 1 1 1-1 1-3-1h0v2h-1v-1c0-1 0-1-1-1v-1h1l2-1z" class="P"></path><path d="M239 269h1 1c1-1-1-5 0-7l2 2h2 0c0 1 1 2 1 2 0 1 1 2 2 3l2 1-1 2h0l-2 1h-1v1c1 0 1 0 1 1v1h1c1 0 2 1 3 2l4 2 1 1h-3c-2-1-5-3-7-4-1 0-3-1-4-2s-3-4-3-5v-1z" class="c"></path><path d="M244 272c2 2 2 3 2 5-1 0-3-1-4-2 0-2 1-1 2-3z" class="I"></path><path d="M245 264h0c0 1 1 2 1 2 0 1 1 2 2 3l2 1-1 2h-2l-3-4c0-1-1-2-1-3v-1h2z" class="B"></path><path d="M246 266c0 1 1 2 2 3l2 1-1 2h-2v-1-1c-1-1-2-2-1-4z" class="k"></path><path d="M239 269h1 1c1-1-1-5 0-7l2 2v1c0 1 1 2 1 3-1 0-1 1-2 2l2 2c-1 2-2 1-2 3-1-1-3-4-3-5v-1z" class="O"></path><path d="M242 270c-1-1-1-4 0-5h1c0 1 1 2 1 3-1 0-1 1-2 2z" class="Z"></path><path d="M248 261c1 0 1 0 2 1 2 0 3 0 5-1h3c0 1 1 1 1 2 1 0 2 1 2 1-1 1-1 1-1 3h-2l2 5h-4c0 1 0 1-1 1s-1 0-2-1h-1-3 0l1-2-2-1c-1-1-2-2-2-3 0 0-1-1-1-2h2l1-3z" class="R"></path><path d="M250 262c2 0 3 0 5-1h3c0 1 1 1 1 2v2l-1 1-2-2-1-1h-1l1 1-1 1-3-3h-1z" class="G"></path><path d="M248 261c1 0 1 0 2 1h1c-1 1-2 1-3 1 1 1 3 4 3 4v2 1c-1 0-2-1-2-2l-1 1c-1-1-2-2-2-3 0 0-1-1-1-2h2l1-3z" class="V"></path><path d="M259 263c1 0 2 1 2 1-1 1-1 1-1 3h-2l2 5h-4c0 1 0 1-1 1s-1 0-2-1h-1-3 0l1-2-2-1 1-1c0 1 1 2 2 2v-1-2l1 1c1 1 2 1 3 0l1-4 2 2 1-1v-2z" class="B"></path><path d="M250 270h3c1 1 1 1 2 1-1 1-1 1-2 1h-1-3 0l1-2z" class="O"></path><path d="M255 271c2-1 2-3 3-4l2 5h-4c0 1 0 1-1 1s-1 0-2-1c1 0 1 0 2-1z" class="D"></path><path d="M260 267c0-2 0-2 1-3 0 1 1 2 2 3s2 3 3 5 2 4 2 6v2l1 5c1 1 1 2 0 3 0 2 1 10 0 11-1-1-1-2-2-3s-1-2-1-2l-3-4-2-2c1-1 0-3-1-4h4l-2-5-2-7h0l-2-5h2z" class="O"></path><path d="M265 289c1 2 1 3 1 5l-3-4c1 0 1 0 2-1z" class="k"></path><path d="M268 278v2l1 11c0 1 0 3-1 4-1-1-1-5-2-7 1 1 1 1 1 2h0l1-12z" class="Z"></path><path d="M264 284l1 5c-1 1-1 1-2 1l-2-2c1-1 0-3-1-4h4z" class="W"></path><path d="M260 267c0-2 0-2 1-3 0 1 1 2 2 3s2 3 3 5 2 4 2 6l-1 12h0c0-1 0-1-1-2v-1l-1-6c-1-4-3-7-4-11l-1-2c0-1 1-1 0-1z" class="N"></path><path d="M263 267c1 1 2 3 3 5h-1s0-1-1-1l1 1c-2-1-3-3-3-5h1z" class="H"></path><path d="M189 243l10-5-1 4-1 4-1 4s0 2 1 2c1 1 1 3 3 4 0-1 1-1 1-1v1h-2 3v2h0l2-4 1-1c0 1 2 0 2 1 1 0 1 1 1 1l-1 4 2 1c0 1 0 2 1 3 0 1 1 3 2 5 0 1 1 3 2 4l4 4c3 3 8 5 12 8l2 1c-2 1-3 1-4 1h1l1 1 2 1 2 1h-6 0v1c3 1 4 2 5 4l2 2c1 1 3 2 5 3l5 2 6 4 4 1 3 3h2c1 0 1 0 1 1h-1 0c0 1 1 2 2 2 2 2 3 4 5 7l6 6 1 1h1 0v11 2c1 5 0 11 0 16h-1c-1-1-1-3-2-4v8l-1-1c-1-3-1-5-2-7s-2-5-4-6l-1 1-2-3c-4-3-7-6-10-10-3-2-7-4-10-6l-2-4-7-4c-1-1-3-1-4-2h-2c0-1 0-1-1-1h-3s0-1-1-1c-2-1-4-1-5-1-2-1-3-1-4-1h0l-2-2c-2 1-2 1-3 0h-1l-1 1-5-5c-2-1-4-3-6-5l-6-6-1 1c-1-1-2-1-3-1l-4-7s0-1 1-2c0 0-1-1-1-2l-2 1-3-6v-2l-1 1-2-3c-2-5-3-10-3-15v-1l1 1c1-1 2-3 3-3l1-2c0-1 1-2 2-3l3-3h0c1-1 2-2 4-2 1-2 3-3 5-5z" class="h"></path><path d="M187 286c1 0 1 0 2 1v2l-1-1c-1 0-1-1-1-2z" class="j"></path><path d="M182 281h1c0 2 1 3 2 4 0 1 0 2-1 3l-1-1c1-2-1-3-1-6z" class="G"></path><path d="M181 272l2 4c2 2 3 5 5 7h-2l-1-2c-1-1-2-1-3-1-1-1 0-3-1-4v-4z" class="d"></path><path d="M199 282c2 2 3 4 4 7l1 1h1 0l1 2 2 4h-2c-2-3-4-7-6-10h-4l1-1h2c1-1 0-2 0-3z" class="e"></path><path d="M206 296h2c2 4 6 6 10 7 2 0 5 1 6 0 1 1 1 0 0 1h-5c-1 0-2 1-2 1-2 0-6-2-8-3-1-2-2-3-3-6z" class="G"></path><defs><linearGradient id="Ah" x1="253.968" y1="307.43" x2="266.095" y2="323.904" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#525251"></stop></linearGradient></defs><path fill="url(#Ah)" d="M248 306c7 2 13 7 17 12 1 1 3 3 4 5v2 1c-4-6-11-12-17-16-2-2-5-3-8-4h2 2z"></path><path d="M181 289s0-1 1-2c0 2 0 2 1 4h1v-1s0-1 1-1v-1 1c0 1 1 1 2 2h1 1c1 1 5 7 6 7 2 0 5 3 7 4h0v1l3 3h-2c-3-2-5-3-7-5s-4-3-6-6l-1-1h-1v1l1 1-1 1c-1-1-2-1-3-1l-4-7z" class="G"></path><path d="M247 304c2 0 2 1 4 1l4 1 3 3h2c1 0 1 0 1 1h-1 0c0 1 1 2 2 2 2 2 3 4 5 7l6 6 1 1h1 0v11c-1-1-1-3-2-4-1-3-2-5-4-7v-1-2c-1-2-3-4-4-5-4-5-10-10-17-12l-1-2z" class="b"></path><path d="M224 312l-1-1c1-1 7 0 8 0h0 1l2 1h1c1 0 1-1 2 0h0 1c1 1 2 1 2 1h1c1 1 1 1 2 1l1 1c1 1 3 2 4 2s2 1 2 1v1l-4-2h-1v2c-2-1-3-2-5-1l-1-1c-1 0-3-1-4 0-1 0-6-3-7-4-1 0-3-1-4-1z" class="V"></path><path d="M228 313h1s2 1 3 1h4c1 0 2 1 4 1 1 1 2 0 3 0l3 2h-1v2c-2-1-3-2-5-1l-1-1c-1 0-3-1-4 0-1 0-6-3-7-4z" class="X"></path><path d="M206 292v-1c1 0 0-1 0-1v-2c1 1 2 1 2 2v1h1 0 1c2 1 4 3 6 4l2 2v1c1 1 1 0 2 1 0 0 1 0 1 1l3 3c-1 1-4 0-6 0-4-1-8-3-10-7l-2-4z" class="Y"></path><defs><linearGradient id="Ai" x1="176.322" y1="275.108" x2="170.232" y2="261.368" xlink:href="#B"><stop offset="0" stop-color="#acacab"></stop><stop offset="1" stop-color="#d8d6d8"></stop></linearGradient></defs><path fill="url(#Ai)" d="M174 258h1c1 1 1 3 2 4-1 2-1 3-1 5 1 3 2 6 2 10l-2 3v-2l-1 1-2-3c-2-5-3-10-3-15v-1l1 1c1-1 2-3 3-3z"></path><path d="M174 258h1c1 1 1 3 2 4-1 2-1 3-1 5h-1v-3h-1v6c-1-3-1-9 0-12z" class="B"></path><path d="M174 270v-6h1v3h1c1 3 2 6 2 10l-2 3v-2l-2-8z" class="V"></path><path d="M175 267h1c1 3 2 6 2 10h-1c-1-1-2-8-2-10z" class="R"></path><defs><linearGradient id="Aj" x1="193.529" y1="298.855" x2="204.645" y2="310.123" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#575556"></stop></linearGradient></defs><path fill="url(#Aj)" d="M189 296l-1-1v-1h1l1 1c2 3 4 4 6 6s4 3 7 5h2l-3-3v-1c2 2 5 3 8 5 1 1 3 1 5 2 3 1 5 2 7 3 2 2 4 2 5 4l2 1h-2c0-1 0-1-1-1h-3s0-1-1-1c-2-1-4-1-5-1-2-1-3-1-4-1h0l-2-2c-2 1-2 1-3 0h-1l-1 1-5-5c-2-1-4-3-6-5l-6-6z"></path><path d="M203 306h2l-3-3v-1c2 2 5 3 8 5 1 1 3 1 5 2 3 1 5 2 7 3 2 2 4 2 5 4-1-1-2-1-3-1-1-1-2-1-3-1-2-1-4-3-7-4-1 1-2 1-3 1s-5-2-5-2l-3-3z" class="X"></path><path d="M203 306h2l-3-3v-1c2 2 5 3 8 5-1 0-2 0-3-1h-1l2 2h1v1h-2c-1-1-1-1-1 0l-3-3z" class="M"></path><defs><linearGradient id="Ak" x1="244.418" y1="331.023" x2="254.021" y2="326.646" xlink:href="#B"><stop offset="0" stop-color="#3b3b3c"></stop><stop offset="1" stop-color="#636161"></stop></linearGradient></defs><path fill="url(#Ak)" d="M224 312c1 0 3 1 4 1 1 1 6 4 7 4 5 2 9 5 14 8 2 2 4 3 6 5 2 3 4 6 6 10h0v1l1 2c-4-3-7-6-10-10-3-2-7-4-10-6l-2-4-7-4c-1-1-3-1-4-2l-2-1c-1-2-3-2-5-4 1 0 2 1 3 1 2 1 4 2 6 4h1c0 1 1 1 1 1 1 1 3 2 4 2 1 1 2 1 2 2h1c1 0 1 0 1-1l-6-3c-1-1-2-1-3-2l-4-2h-1c-1 0-2-1-2-2h-1z"></path><path d="M240 323c8 4 15 10 21 17v1l1 2c-4-3-7-6-10-10-3-2-7-4-10-6l-2-4z" class="E"></path><defs><linearGradient id="Al" x1="247.474" y1="331.976" x2="278.585" y2="340.326" xlink:href="#B"><stop offset="0" stop-color="#2d2c2e"></stop><stop offset="1" stop-color="#696968"></stop></linearGradient></defs><path fill="url(#Al)" d="M250 318h1v-1c-3-2-6-4-9-5-1 0-2-1-3-1l1-1c1 1 1 1 2 1h0c1 0 3 2 4 2h1c2 1 5 3 6 4h1c1 1 3 2 4 3 7 6 14 15 17 23v1 1-6c1 5 0 11 0 16h-1c-1-1-1-3-2-4v8l-1-1v-5c0-2-1-3-1-5 1 0 1 2 2 2h1c-1-1-1-2-1-2l-3-9c-2-8-10-14-17-19h0l-2-1v-1z"></path><path d="M235 317c1-1 3 0 4 0l1 1c2-1 3 0 5 1v-2h1l4 2 2 1h0c7 5 15 11 17 19l3 9s0 1 1 2h-1c-1 0-1-2-2-2 0 2 1 3 1 5v5c-1-3-1-5-2-7s-2-5-4-6l-1 1-2-3-1-2v-1h0c-2-4-4-7-6-10-2-2-4-3-6-5-5-3-9-6-14-8z" class="R"></path><path d="M265 338l-1-3v-1c2 2 3 4 5 5l3 9h-1v-1l-6-9z" class="E"></path><path d="M261 341h2l-1-1h1c1 0 2 1 3 2 1 2 2 4 3 7 1 1 1 3 2 4v5c-1-3-1-5-2-7s-2-5-4-6l-1 1-2-3-1-2z" class="P"></path><path d="M246 317l4 2 2 1h0c7 5 15 11 17 19-2-1-3-3-5-5v1l1 3c-3-3-5-6-8-9-2-2-6-4-8-7v-1l-4-2v-2h1z" class="D"></path><path d="M246 317l4 2 2 1h0l-1 1c2 2 4 3 7 6h-1c-3-2-5-4-8-6l-4-2v-2h1z" class="b"></path><path d="M204 281l1 1c2 1 3 2 5 3 2 0 3 1 5 0 2 1 6 2 8 4l5 1c3 1 4 2 5 4l2 2c1 1 3 2 5 3l5 2 6 4c-2 0-2-1-4-1l1 2h-2-2c-1 0-3 0-4-1l-11-2c-3 0-5-2-7-4l-1 1c0-1-1-1-1-1-1-1-1 0-2-1v-1l-2-2c-2-1-4-3-6-4h-1 0-1v-1c0-1-1-1-2-2v2s1 1 0 1v1l-1-2v-2h0l-1-2h0l-2-3c0-1 1-2 2-2z" class="E"></path><path d="M233 294l2 2c1 1 3 2 5 3l5 2 6 4c-2 0-2-1-4-1l1 2h-2c-2-2-7-4-10-5-2-2-4-3-6-5 2 0 2 0 3-2z" class="B"></path><path d="M240 299l5 2 6 4c-2 0-2-1-4-1-1 0-2-1-3-1l-1-1c-2-1-2-2-3-3z" class="F"></path><path d="M204 281l1 1c2 1 3 2 5 3 2 0 3 1 5 0 2 1 6 2 8 4l5 1c3 1 4 2 5 4-1 2-1 2-3 2-2-2-4-3-6-5-4-1-8-3-12-4-2-1-3-1-5-2h0c-1 0-2 1-3 1h0l-2-3c0-1 1-2 2-2z" class="H"></path><path d="M204 281l1 1h-1c0 1 0 2 1 2l-1 2-2-3c0-1 1-2 2-2z" class="k"></path><path d="M210 285c2 0 3 1 5 0 2 1 6 2 8 4h-3 1c1 0 1 1 2 1-5-1-9-3-13-5z" class="O"></path><path d="M223 290c-1 0-1-1-2-1h-1 3l5 1c3 1 4 2 5 4-1 2-1 2-3 2-2-2-4-3-6-5l-1-1z" class="Q"></path><path d="M205 288h3 0c1 1 2 1 3 1l2 1h1 1c1 0 2 0 3 1 1 0 2 0 2 1 3 0 5 3 7 4s5 4 6 6l1 1h-1c-1 1-3 0-4 0-3 0-5-2-7-4l-1 1c0-1-1-1-1-1-1-1-1 0-2-1v-1l-2-2c-2-1-4-3-6-4h-1 0-1v-1c0-1-1-1-2-2v2s1 1 0 1v1l-1-2v-2z" class="C"></path><path d="M221 295l-1-1h1c2 0 3 3 6 3v-1c2 1 5 4 6 6l1 1h-1c-1 1-3 0-4 0-3 0-5-2-7-4-1-1-2-2-3-2h-1v-1c1-1 2-1 3-1z" class="l"></path><path d="M221 295l-1-1h1c2 0 3 3 6 3v-1c2 1 5 4 6 6-3-1-5-2-7-4-2 0-3-2-5-3z" class="J"></path><path d="M189 243l10-5-1 4-1 4-1 4s0 2 1 2c1 1 1 3 3 4 0-1 1-1 1-1v1h-2 3v2h0l2-4 1-1c0 1 2 0 2 1 1 0 1 1 1 1l-1 4 2 1c0 1 0 2 1 3 0 1 1 3 2 5 0 1 1 3 2 4l4 4c3 3 8 5 12 8l2 1c-2 1-3 1-4 1h1l1 1 2 1 2 1h-6 0v1l-5-1c-2-2-6-3-8-4-2 1-3 0-5 0-2-1-3-2-5-3l-1-1c-1 0-2 1-2 2l2 3h0l1 2h0v2h0-1l-1-1c-1-3-2-5-4-7 0 1 1 2 0 3h-2l-1 1-2-1c-2 0-4-1-6-2-2-2-3-5-5-7l-2-4c-2-3-3-6-4-10-1-1-1-3-2-4h-1l1-2c0-1 1-2 2-3l3-3h0c1-1 2-2 4-2 1-2 3-3 5-5z" class="F"></path><path d="M185 260l2 6c-2 0-2-1-3-2v-1c0-1 0-1 1-2v-1z" class="P"></path><path d="M187 258l2 4c0 3 2 5 3 8l-1-1-1 1c0-1 0-2-1-2-1-3-1-5-2-7-1-1-1-1 0-1v-2z" class="I"></path><path d="M190 275c1 1 3 5 5 6h0c1 1 2 2 2 4l-1 1-2-1h1v-1c-1-2-3-3-4-4v-1l-1-2v-2z" class="c"></path><defs><linearGradient id="Am" x1="192.077" y1="272.628" x2="198.526" y2="284.054" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#838383"></stop></linearGradient></defs><path fill="url(#Am)" d="M190 270l1-1 1 1 6 9s0 3 1 3c0 1 1 2 0 3h-2c0-2-1-3-2-4l-3-8c-1-1-2-2-2-3z"></path><path d="M182 264h2c1 1 1 2 3 2l1 3 2 6v2l1 2v1c1 1 3 2 4 4v1h-1c-2 0-4-1-6-2-2-2-3-5-5-7 0-2 0-3-1-4 1-1 1-2 1-2l-1-5v-1z" class="c"></path><path d="M188 276c1 0 2 0 2 1l1 2h-1v2c-1-2-1-4-2-5z" class="L"></path><path d="M188 269l2 6v2c0-1-1-1-2-1v-1c0-1 0-2-1-4 0-1 1-1 1-2z" class="P"></path><path d="M183 270c2 4 4 8 7 11h0v-2h1v1c1 1 3 2 4 4v1h-1c-2 0-4-1-6-2-2-2-3-5-5-7 0-2 0-3-1-4 1-1 1-2 1-2z" class="J"></path><defs><linearGradient id="An" x1="176.959" y1="265.416" x2="187.242" y2="254.511" xlink:href="#B"><stop offset="0" stop-color="#b7b5b6"></stop><stop offset="1" stop-color="#dddbdc"></stop></linearGradient></defs><path fill="url(#An)" d="M184 248c1-2 3-3 5-5l-1 2c-1 1-1 2-1 2v11 2l-1-3h-1v2 1 1c-1 1-1 1-1 2v1h-2v1l1 5s0 1-1 2c1 1 1 2 1 4l-2-4c-2-3-3-6-4-10-1-1-1-3-2-4h-1l1-2c0-1 1-2 2-3l3-3h0c1-1 2-2 4-2z"></path><path d="M180 250l1 1c-1 1-1 2-1 3h0l-1-1h-2l3-3z" class="l"></path><path d="M177 253h2l1 1c-1 1-1 2-1 3l-1-1c-1-1-2 0-3 0 0-1 1-2 2-3z" class="T"></path><defs><linearGradient id="Ao" x1="176.507" y1="263.654" x2="177.906" y2="256.035" xlink:href="#B"><stop offset="0" stop-color="#908e90"></stop><stop offset="1" stop-color="#a8a7a6"></stop></linearGradient></defs><path fill="url(#Ao)" d="M175 256c1 0 2-1 3 0l1 1c0 5 1 10 3 15 1 1 1 2 1 4l-2-4c-2-3-3-6-4-10-1-1-1-3-2-4h-1l1-2z"></path><path d="M184 248c1-2 3-3 5-5l-1 2c-1 1-1 2-1 2v11 2l-1-3h-1v2 1 1c-1 1-1 1-1 2v1h-2v1c-1-5 0-12 2-17z" class="K"></path><path d="M182 264v-4h1l2 1c-1 1-1 1-1 2v1h-2z" class="H"></path><path d="M185 259c-1-2-1-5-1-6l3-6v11 2l-1-3h-1v2z" class="P"></path><defs><linearGradient id="Ap" x1="184.866" y1="255.765" x2="219.436" y2="269.307" xlink:href="#B"><stop offset="0" stop-color="#070708"></stop><stop offset="1" stop-color="#323130"></stop></linearGradient></defs><path fill="url(#Ap)" d="M189 243l10-5-1 4-1 4-1 4s0 2 1 2c1 1 1 3 3 4 0-1 1-1 1-1v1h-2c-1 1-1 2-2 3 0 2 0 5 1 6 1 2 1 3 2 4 0 1 0 1 1 2h0c0 1 1 2 2 3h0l-1 1c4 4 8 7 13 10-2 1-3 0-5 0-2-1-3-2-5-3l-1-1c-1 0-2 1-2 2l2 3h0l1 2h0v2h0-1l-1-1c-1-3-2-5-4-7-1 0-1-3-1-3l-6-9c-1-3-3-5-3-8l-2-4v-11s0-1 1-2l1-2z"></path><path d="M196 260c-1-3-1-7 0-10 0 0 0 2 1 2 1 1 1 3 3 4 0-1 1-1 1-1v1h-2c-1 1-1 2-2 3 0 1 0 1-1 1z" class="D"></path><path d="M196 260c1 0 1 0 1-1 0 2 0 5 1 6 1 2 1 3 2 4 0 1 0 1 1 2h0c0 1 1 2 2 3h0l-1 1c-1-1-3-4-4-5v-1c-1-2-2-6-2-9z" class="k"></path><path d="M189 262c1 3 2 3 4 4s4 7 6 9v1c1 1 4 5 5 5-1 0-2 1-2 2l2 3h0l1 2h0v2h0-1l-1-1c-1-3-2-5-4-7-1 0-1-3-1-3l-6-9c-1-3-3-5-3-8z" class="M"></path><path d="M202 283c-1-2-3-3-3-5-1-1-1-1 0-2 1 1 4 5 5 5-1 0-2 1-2 2z" class="F"></path><defs><linearGradient id="Aq" x1="210.195" y1="274.452" x2="216.121" y2="269.292" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#333132"></stop></linearGradient></defs><path fill="url(#Aq)" d="M205 253c0 1 2 0 2 1 1 0 1 1 1 1l-1 4 2 1c0 1 0 2 1 3 0 1 1 3 2 5 0 1 1 3 2 4l4 4c3 3 8 5 12 8l2 1c-2 1-3 1-4 1h1l1 1 2 1 2 1h-6 0v1l-5-1c-2-2-6-3-8-4-5-3-9-6-13-10l1-1h0c-1-1-2-2-2-3h0c-1-1-1-1-1-2-1-1-1-2-2-4-1-1-1-4-1-6 1-1 1-2 2-3h3v2h0l2-4 1-1z"></path><path d="M214 272l4 4c-2 0-2 0-3-1 0-1-1-1-1-2l-2-1h2z" class="g"></path><path d="M216 281s1 0 2-1v1h1 1c1 0 1 0 1 1h1c2 0 6 3 7 2h1l2 1c-2 1-3 1-4 1l-6-3h-2l-4-2z" class="O"></path><path d="M205 253c0 1 2 0 2 1 1 0 1 1 1 1l-1 4 2 1c0 1 0 2 1 3 0 1 1 3 2 5 0 1 1 3 2 4h-2 0c-4-3-8-7-10-11v-3h0l2-4 1-1z" class="E"></path><path d="M204 254c1 1 1 2 1 4h0l-1 2c-1 1-1 1-2 1v-3h0l2-4z" class="C"></path><path d="M202 261c0-2 0-2 1-3h2l-1 2c-1 1-1 1-2 1z" class="a"></path><path d="M205 253c0 1 2 0 2 1 1 0 1 1 1 1l-1 4v4c-1-1-1-2-1-4 0 0-1 0-1-1h0c0-2 0-3-1-4l1-1z" class="D"></path><path d="M207 259l2 1c0 1 0 2 1 3 0 1 1 3 2 5 0 1 1 3 2 4h-2 0v-1c-1-2-2-3-3-5-1-1-1-2-2-3v-4z" class="O"></path><path d="M197 259c1-1 1-2 2-3v7a30.44 30.44 0 0 0 8 8c1 1 3 2 4 3s2 3 3 4l6 3h-1-1v-1c-1 1-2 1-2 1l4 2h2l6 3h1l1 1 2 1 2 1h-6 0v1l-5-1c-2-2-6-3-8-4-5-3-9-6-13-10l1-1h0c-1-1-2-2-2-3h0c-1-1-1-1-1-2-1-1-1-2-2-4-1-1-1-4-1-6z" class="B"></path><path d="M197 259c1-1 1-2 2-3v7c1 2 2 5 4 7-2-1-3-3-5-5-1-1-1-4-1-6z" class="U"></path><defs><linearGradient id="Ar" x1="202.009" y1="267.435" x2="208.613" y2="280.333" xlink:href="#B"><stop offset="0" stop-color="#605f60"></stop><stop offset="1" stop-color="#7e7e7f"></stop></linearGradient></defs><path fill="url(#Ar)" d="M198 265c2 2 3 4 5 5 3 4 8 9 13 11l4 2v1c-1 1-2-1-3 0-5-2-10-5-14-10h0c-1-1-2-2-2-3h0c-1-1-1-1-1-2-1-1-1-2-2-4z"></path><path d="M202 275l1-1c4 5 9 8 14 10 1-1 2 1 3 0v-1h2l6 3h1l1 1 2 1 2 1h-6 0v1l-5-1c-2-2-6-3-8-4-5-3-9-6-13-10z" class="L"></path><path d="M220 283h2l6 3h1l1 1 2 1h-5l-1-1h-3c-2 0-4-1-6-3 1-1 2 1 3 0v-1z" class="P"></path><path d="M532 99c-2 2-3 3-5 3l-1 1c11 6 24 9 36 4 5-1 12-5 15-10 0-2 1-4 1-6 1 5 1 11-2 16-4 6-12 12-20 14-1 1-2 1-3 1 3 2 6 5 8 7l2 2c0 2 3 5 4 6h-4l-2-2-1-1 3 4c3 2 4 4 8 5 0 1 0 1 1 2 3 3 7 5 10 8 1 1 2 1 3 2v1h0c1 0 2 1 3 1 2 0 2 0 3-1l3-1 9-3c5-4 8-9 9-15 0-4-1-8-3-11-2-2-4-3-7-3-2 0-3 0-5 1h-2c2-2 4-3 7-3s6 1 8 3c3 4 5 10 5 15-1 7-5 14-10 19-4 3-12 5-17 5-3 0-6-2-9-2l14 23h1c0 2 1 2 2 4l1 1-1 1c-1-1-1-1-2-1-1-1-2-2-3-2l-4-4v-1c0-2-2-3-3-4h-1l-3-3c-2-1-3-2-4-3l-1-1c0-1 0-1-1-1-3-4-7-8-9-12h1v-1h-1 0l-1 1h-1-1c1 2 2 3 2 4h-2v-1c-1 0 0 0-1-1v-1h-2v2s0-1-1-2h0c-1-1-1-2-1-3l2-1c1 0 1-1 1-2h-1v1c-2-2-5-5-7-5h-2-1-1-3-1l-1 2-4 1-2-2c-2 1-4 1-6 2l-2 2c-3 1-5 1-8 1l-1 1h-4c-1 1-2 1-3 1h-2c0-2-1-4-2-5-1-5-2-10-5-13-2-3-3-4-6-6 1-1 2-1 2-2l1-1v-2-3-1c0-3-1-5-3-7-1 0-3-1-4-1l-1-1c-1-1-2-1-2-3v-1l-1-2v-1c-1-2-2-3-4-4-1-1-3-1-4-3h-1c0-1-1-2-2-3l-1-1h0l-2-1h4c4 0 7 0 10 1h2 4 0 5-3v1h0c1 0 1 1 2 1l3 1h2 2c2 0 2-1 2-1h3c1 0 2 1 3 2l2 1c1 0 2 1 3 1v-1c0-1 7-3 8-3h0c1 0 1 0 2-1h1l1 1z" class="h"></path><path d="M566 149h1l4 4-1 1h0c-1-1-1-1-2-1h-1 0l-1-1-3-3h2 0 1zm-45 6v-3h0v-3h1v2h0c0 1 1 3 2 3s1-1 2-1c0-1 0-1 1-1 0-1 1-1 1-1 1 1 2 1 3 1l-2 2c-3 1-5 1-8 1z" class="B"></path><path d="M560 139h-1c-1-1-3-3-4-5h0 2 0l1-1-1-1h1l2 2 3 4c0 1-1 1-1 1h-2z" class="e"></path><path d="M567 153h1c1 0 1 0 2 1h0l1-1 5 6h0-2 0c1 1 2 2 3 4 0 2 1 3 2 5-1-1-1-2-2-3s-4-3-4-5c-1-3-4-5-6-7z" class="G"></path><path d="M594 155l9-3c-2 2-5 3-7 4h-1c-1 2-2 2-4 2l1 1c1 0 1 0 2-1h3c1 0 2-1 3-1-3 2-8 2-11 2-2 0-5-1-6-2h0l-4-4 6 3h0c1 0 2 1 3 1 2 0 2 0 3-1l3-1z" class="M"></path><path d="M526 153c0-1-1-2-1-3-1-1 0-1 0-1 2 0 2 0 3 2h1 0c1 0 2 0 3-1h5l-5-5c2 1 4 3 6 4 0 1 0 1 1 0h2c1 0 1-1 2-1 1-1 2 0 3-1 1 0 1 0 1-1l-2-2h0l1 1 1-1c-1 0-1-1-2-1s0 0-1-1c2 1 3 2 5 3 1 0 2 1 3 2l-4-2h0v2c-1 1-2 1-3 2h-1l-1 2-4 1-2-2c-2 1-4 1-6 2-1 0-2 0-3-1 0 0-1 0-1 1-1 0-1 0-1 1z" class="G"></path><path d="M537 150h0c2 1 2 0 4 0 1-1 2-1 3-1l-1 2-4 1-2-2z" class="M"></path><path d="M554 148c-1-1-2-1-2-2-1-1-1-2-3-3l-1-2 1-1v-2h0c1 1 1 1 3 1h0 1c1-1 1-1 0-2 5 2 7 5 10 8 2 1 4 3 4 4h-1-1 0-2v-1c-1-1-2-1-3-1v-1-1s-1 0-2-1l-1-1s1 0 2 1v-1h2c-2-1-3-3-5-4-2 1-4 1-5 2 0 2 1 4 3 7z" class="Q"></path><path d="M557 143s1 0 2 1v-1h2l5 6h-1 0-2v-1c-1-1-2-1-3-1v-1-1s-1 0-2-1l-1-1z" class="O"></path><path d="M566 152l1 1h0c2 2 5 4 6 7 0 2 3 4 4 5s1 2 2 3c0 2 1 3 2 5 1 1 2 3 3 5h-1l-3-3v-1l-1-2c-2-5-7-9-10-13-1-2-2-3-3-4v-3z" class="e"></path><path d="M554 148c-2-3-3-5-3-7 1-1 3-1 5-2 2 1 3 3 5 4h-2v1c-1-1-2-1-2-1l1 1c1 1 2 1 2 1v1 1l-1 1h-1c0 1 2 2 2 3l-1 1h0v1 1c-2-2-5-5-7-5h-2-1-1-3c1-1 2-1 3-2v-2h0l4 2h0c2 1 4 3 5 5v-1l-2-2-1-1z" class="M"></path><path d="M559 152c-1 0-1-2-2-2 0-3-2-4-3-5 0-1 1-1 1-2v-1h1l1 1 1 1c1 1 2 1 2 1v1 1l-1 1h-1c0 1 2 2 2 3l-1 1h0z" class="F"></path><defs><linearGradient id="As" x1="571.807" y1="139.72" x2="570.816" y2="155.04" xlink:href="#B"><stop offset="0" stop-color="#5b595c"></stop><stop offset="1" stop-color="#767775"></stop></linearGradient></defs><path fill="url(#As)" d="M563 138c3 2 4 4 8 5 0 1 0 1 1 2 3 3 7 5 10 8 1 1 2 1 3 2v1l-6-3 4 4h0c-9-3-17-11-23-18h2s1 0 1-1z"></path><path d="M560 147c1 0 2 0 3 1v1l3 3v3c1 1 2 2 3 4 3 4 8 8 10 13l1 2v1c-2-1-3-2-4-3l-1-1c0-1 0-1-1-1-3-4-7-8-9-12h1v-1h-1 0l-1 1h-1-1c1 2 2 3 2 4h-2v-1c-1 0 0 0-1-1v-1h-2v2s0-1-1-2h0c-1-1-1-2-1-3l2-1c1 0 1-1 1-2h-1v-1h0l1-1c0-1-2-2-2-3h1l1-1z" class="R"></path><path d="M560 147c1 0 2 0 3 1v1l3 3v3c1 1 2 2 3 4l-7-7h0c-1-1-2-2-2-3l-1-1 1-1z" class="F"></path><path d="M560 147c1 0 2 0 3 1l-1 1h-2l-1-1 1-1z" class="U"></path><path d="M575 171c0-1 0-1-1-1-3-4-7-8-9-12h1c2 2 11 11 11 14h-1l-1-1z" class="D"></path><path d="M559 152h0l1-1c0-1-2-2-2-3h1l1 1c0 1 1 2 2 3h0l-1 1c0 1 2 3 3 4h1l-1 1h-1-1c1 2 2 3 2 4h-2v-1c-1 0 0 0-1-1v-1h-2v2s0-1-1-2h0c-1-1-1-2-1-3l2-1c1 0 1-1 1-2h-1v-1z" class="Z"></path><path d="M559 159c1-1 1-2 2-3 0 1 1 1 1 2l-1 1h-2z" class="S"></path><defs><linearGradient id="At" x1="558.452" y1="118.127" x2="544.282" y2="103.814" xlink:href="#B"><stop offset="0" stop-color="#8c8c8e"></stop><stop offset="1" stop-color="#cac7c8"></stop></linearGradient></defs><path fill="url(#At)" d="M538 110c8 2 17 2 26-1 2 0 4-1 6-2 2-2 4-4 6-5-4 6-11 11-19 13-6 2-15 3-21 1 2 0 4 1 5 0h-3c-1-2-1-4 0-6z"></path><path d="M476 96h4c4 0 7 0 10 1h2 4 0 5-3v1h0c1 0 1 1 2 1l3 1h2 2c2 0 2-1 2-1h3c1 0 2 1 3 2l2 1c1 0 2 1 3 1 6 1 11 5 18 7-1 2-1 4 0 6h3c-1 1-3 0-5 0-7 0-14-1-20-3-4-1-7-2-10-2 1 1 3 2 4 3 2 1 4 2 5 4 4 4 5 12 6 18-1 2-2 3-4 4v1h0c1 1 1 2 1 3 0 3 1 6 1 9 0 1 0 2 1 3h-4c-1 1-2 1-3 1h-2c0-2-1-4-2-5-1-5-2-10-5-13-2-3-3-4-6-6 1-1 2-1 2-2l1-1v-2-3-1c0-3-1-5-3-7-1 0-3-1-4-1l-1-1c-1-1-2-1-2-3v-1l-1-2v-1c-1-2-2-3-4-4-1-1-3-1-4-3h-1c0-1-1-2-2-3l-1-1h0l-2-1z" class="Y"></path><path d="M498 112l3 4v2l-3-3v-1h0c1-1 0-1 0-2z" class="a"></path><path d="M509 99h3c1 0 2 1 3 2l2 1h-3-2-2l3 1h0-1-2c-2 1-3 1-4 0h-3v-1h2c-1-1-2-1-3-1l-2-1h3 2 2c2 0 2-1 2-1z" class="K"></path><path d="M503 100h2 0-1v1s1 0 2 1h2v1c-1 0-2 0-3-1h0c-1-1-2-1-3-1l-2-1h3z" class="P"></path><path d="M509 99h3c1 0 2 1 3 2l-10-1h0 2c2 0 2-1 2-1z" class="H"></path><path d="M493 111c2 1 4 3 5 4l3 3 3 8v2h-1c0 1 0 2-1 2h-1v1-1-2-3-1c0-3-1-5-3-7-1 0-3-1-4-1l-1-1c1 0 0-1 0-2v-2z" class="g"></path><path d="M493 113c3 0 4 2 5 4-1 0-3-1-4-1l-1-1c1 0 0-1 0-2z" class="a"></path><path d="M476 96h4c4 0 7 0 10 1h2 4 0 5-3v1h0c1 0 1 1 2 1l3 1h-3l2 1c1 0 2 0 3 1h-2v1c-2-1-4-1-6-1-1-1-1 0-1 0v2 1h-2v1 1l-1-1c-2 0-3-3-6-3h0c2 1 3 4 5 5 3 1 5 2 6 4 0 1 1 1 0 2h0v1c-1-1-3-3-5-4v2c0 1 1 2 0 2-1-1-2-1-2-3v-1l-1-2v-1c-1-2-2-3-4-4-1-1-3-1-4-3h-1c0-1-1-2-2-3l-1-1h0l-2-1z" class="T"></path><path d="M490 108c1 0 3 3 3 3v2c0 1 1 2 0 2-1-1-2-1-2-3v-1l-1-2v-1z" class="G"></path><path d="M491 98l9 1 3 1h-3l2 1c1 0 2 0 3 1h-2l-13-2 1-2z" class="W"></path><path d="M476 96h4c4 0 7 0 10 1h2 4 0 5-3v1h0c1 0 1 1 2 1l-9-1-1 2c-2 0-6-1-8 0v1h-1c0-1-1-2-2-3l-1-1h0l-2-1z" class="U"></path><path d="M479 98l1-1 11 1-1 2c-2 0-6-1-8 0v1h-1c0-1-1-2-2-3z" class="P"></path><path d="M504 128h1l2 2h1c1 1 3 3 4 5l5 5v1h0c1 1 1 2 1 3 0 3 1 6 1 9 0 1 0 2 1 3h-4c-1 1-2 1-3 1h-2c0-2-1-4-2-5-1-5-2-10-5-13-2-3-3-4-6-6 1-1 2-1 2-2l1-1v1-1h1c1 0 1-1 1-2h1z" class="a"></path><path d="M504 133c2 0 3 2 5 4l2 3h2c1 2 3 4 3 7l-1 1c0 2 1 4 1 5v3h0c-1 1-2 1-3 1v-1c1-1 0-3 0-4-1-8-5-13-9-19h0z" class="J"></path><defs><linearGradient id="Au" x1="513.399" y1="144.149" x2="498.861" y2="142.994" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2f2d30"></stop></linearGradient></defs><path fill="url(#Au)" d="M501 130v1-1h1c0 2 0 2 2 3h0 0c4 6 8 11 9 19 0 1 1 3 0 4v1h-2c0-2-1-4-2-5-1-5-2-10-5-13-2-3-3-4-6-6 1-1 2-1 2-2l1-1z"></path><path d="M93 259h2v2c-1 0-1 6-1 7v1l1 4h0c1 2 1 3 1 5 1 1 1 2 2 3h0c0 1 0 1 1 2 3 7 6 12 11 18 1 1 1 2 1 4v1c2 4 5 6 8 8 1 1 1 2 2 2h-2c-2-1-5-2-6-4h1 0c0 1 1 1 1 1l1 1c1 0 2 1 3 1 0 0-1 0-1-1h-1c0-1-1-1-1-1-1-1-3-2-3-3l-2-2c0-1 0-1-1-1 0-1 0-1-1-2 0-1-2-3-3-4 0 0-1 1-1 0-1 0-2-1-3-2 0-1 0-1-1-1h0v2 1c-1 0 0 0-1 1l2 2h0c0 1 1 2 2 2 1 2 3 4 4 5 2 1 4 3 5 5 3 2 7 4 10 6 1 1 4 2 4 2 0 1 1 10 2 12l3-10h1c2 1 6 2 9 2h1c8 2 16 2 24 3 5 1 10 3 15 6 3 2 6 4 7 7 4 5 5 11 4 16 0 1 0 2-1 3h-1c0-1 0-1-1-1-8 1-15 0-23-2l-1 4c-5 9-25 17-35 20 4-3 9-5 12-8-1-3-2-5-3-8-2-2-5-4-8-5l-9-6c-2-1-5-1-7-2-11-8-18-22-20-36h0c-7 8-15 16-26 20 2-2 4-3 6-5 6-7 9-16 9-25 0-7-3-13-4-20-1-1-1-3-1-4h-7c-15 0-29-2-40-12 3 1 6 3 9 4h0c1 1 1 1 3 2 2-1 4-1 7-1 4 0 8-1 13-2l-2 1 1 3c1 0 1 0 2 1 6 0 11-1 16-3l-1-1c1 0 2-1 3-1v-1c-1 1-2 1-2 1l3-2c2-2 4-3 5-6l-1-1c1-1 1-2 1-3l2-1c1-2 1-3 1-4z"></path><path d="M153 355c1 0 3 1 4 1l1 1v2c-1 0-2-1-4-1v-1h-2c-1 0-2 0-3-1h4v-1z" class="g"></path><path d="M124 337l6 3 1-1s2 1 3 1c3 2 6 3 9 4h0c-2 0-3-1-5-1l-2-1h-3l1 1 1 1c-1 0-2-1-3-2h-1s0-1-1-1c0 0-1 0-1-1-1 0-3-1-4-1 0 0-1-1-1-2z" class="M"></path><path d="M105 328c0-1 0-1-1-2v-1l-2-3c0-3-1-5-2-7l3 2c2 1 4 3 5 6v1c1 0 1 1 2 2h-1l-2-2h0l-1-1v1h-1c-1 2 0 3 1 4h-1z" class="j"></path><path d="M124 337c-4-4-9-8-12-13v-1l9 9c3 2 6 5 10 7l-1 1-6-3z" class="S"></path><path d="M139 347c1 0 2 1 3 2 0 0 1 0 2 1h0 2 0c1-1 1-1 1 0 1 0 1 0 2 1h1c0-1 1 0 2 1 2 1 3 0 4 2v1c-2 0-2 0-3-1h0 0c0-1-1-1-1-1s-1 0 0 1l1 1v1h-4-2c2 1 5 2 5 3-1 0-3-1-4-2h-1l-1-1c1-2 2 0 4-1-1 0-1 0-2-1h1l-1-1h2v1l1-1v-1c-2-1-2 0-3 0s-2 0-3-1l-1-1h-2v-1c-1 0-1 0-2-1-1 0-1 0-1-1z" class="V"></path><path d="M143 360c1 1 2 2 3 2 1 1 2 0 3 0v-1c-2 0-3-2-4-2 0-1 0-1-1-1 1-1 2-1 3 0 2 0 2 1 3 2h1 0c1 0 1 0 1 1h1 0c2 1 3 0 5 1-3 0-4 0-6 1-1 1-2 1-3 1-1-1-1 0-2-1-2 0-2-2-4-1h1c1 1 1 2 1 3l2 1h2 0c-1 0-2-1-2-2h0c1 0 2 1 3 2v1c-2 0-4-1-5-1l-1-1c1-2-1-2-1-3v-2z" class="G"></path><path d="M45 279c2-1 4-1 7-1 4 0 8-1 13-2l-2 1 1 3c1 0 1 0 2 1-4-1-8 0-12 0-3-1-6-1-9-2z" class="C"></path><defs><linearGradient id="Av" x1="118.387" y1="334.945" x2="117.69" y2="356.848" xlink:href="#B"><stop offset="0" stop-color="#716f71"></stop><stop offset="1" stop-color="#8a8a8b"></stop></linearGradient></defs><path fill="url(#Av)" d="M134 360l-18-8c-1-1-3-3-4-5-6-6-11-16-13-25l2 4 2 4c2 1 3 2 5 3v1h-1l2 3v1c2 3 4 6 7 8 5 3 10 6 15 10 1 1 3 2 3 3v1z"></path><path d="M103 330c2 1 3 2 5 3v1h-1l2 3v1c-3-2-5-5-6-8z" class="e"></path><path d="M109 338v-1l2 2h1l2 1 1-1c2 2 4 4 6 5s3 2 5 3l1 1h1c1 1 4 4 4 5v1l1 1h0v1c1 0 1 1 2 1 1 1 3 1 4 2 1 0 1 0 2 1h2v2c0 1 2 1 1 3l-6-3c-1-1-3-1-4-2v-1c0-1-2-2-3-3-5-4-10-7-15-10-3-2-5-5-7-8z" class="M"></path><path d="M111 339h1l2 1 1-1c2 2 4 4 6 5s3 2 5 3l1 1h1c1 1 4 4 4 5v1l1 1h0c-8-4-15-9-22-16z" class="D"></path><path d="M124 339c1 1 1 0 2 1s2 1 3 2c1 0 3 1 4 2 0 0 1 0 1 1 1 0 2 1 3 1 0 0 1 1 2 1 0 1 0 1 1 1 1 1 1 1 2 1v1h2l1 1c1 1 2 1 3 1s1-1 3 0v1l-1 1v-1h-2l1 1h-1c1 1 1 1 2 1-2 1-3-1-4 1l1 1h0v1c-1-1-2-1-3 0 1 0 1 0 1 1 1 0 2 2 4 2v1c-1 0-2 1-3 0-1 0-2-1-3-2h-2c-1-1-1-1-2-1-1-1-3-1-4-2-1 0-1-1-2-1v-1h0l-1-1v-1c0-1-3-4-4-5h-1c2 0 4 0 5 1s2 2 3 2c1 1 3 0 5 1v-1l-2-2h0l-3-2c-1-1-1-2-2-2l-3-2c-2-1-4-2-6-4z" class="B"></path><path d="M138 349c2 1 3 2 5 3 1 1 2 1 2 2h1c-1 1-3 0-3 0h-1-1v1l1-1v1c-1 0-1 0-2-1 0 0 1-1 2-1h0v-1h-2v-1l-2-2z" class="X"></path><defs><linearGradient id="Aw" x1="139.799" y1="350.181" x2="130.106" y2="351.812" xlink:href="#B"><stop offset="0" stop-color="#525552"></stop><stop offset="1" stop-color="#787479"></stop></linearGradient></defs><path fill="url(#Aw)" d="M127 348c2 0 4 0 5 1s2 2 3 2c1 1 3 0 5 1h2v1h0c-1 0-2 1-2 1 1 1 1 1 2 1 1 1 1 1 1 2-2 0-3-1-5-1v-1 1l2 2c-1 0 0 0-1 1-1-1-3-1-4-2-1 0-1-1-2-1v-1h0l-1-1v-1c0-1-3-4-4-5h-1z"></path><path d="M133 356c1 0 1 0 1-1h0v-1l1-1h0l4 1v1h-1 0v1l2 2c-1 0 0 0-1 1-1-1-3-1-4-2-1 0-1-1-2-1z" class="F"></path><path d="M135 357h1c0-1-1-1-1-2h0 3 0v1l2 2c-1 0 0 0-1 1-1-1-3-1-4-2z" class="b"></path><defs><linearGradient id="Ax" x1="126.077" y1="335.154" x2="122.037" y2="344.846" xlink:href="#B"><stop offset="0" stop-color="#1f1f1f"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#Ax)" d="M106 328c-1-1-2-2-1-4h1v-1l1 1h0l2 2h1c0 1 1 1 1 2 1 1 2 1 2 2 1 1 2 1 2 2 1 1 2 2 4 3l5 4c2 2 4 3 6 4l3 2c1 0 1 1 2 2l3 2h0l2 2v1c-2-1-4 0-5-1-1 0-2-1-3-2s-3-1-5-1l-1-1c-2-1-3-2-5-3s-4-3-6-5l-1 1-2-1h-1l-2-2-2-3h1v-1c-2-1-3-2-5-3l-2-4c2 1 2 1 3 1l1 1h1z"></path><path d="M130 343l3 2c1 0 1 1 2 2l3 2c-2 0-4-1-6-3h0c-1 0-1-1-2-1v-1-1h0z" class="X"></path><path d="M101 326c2 1 2 1 3 1l1 1h1c1 1 2 2 2 4h0c2 0 5 4 7 5v1 1h0l-1 1-2-1h-1l-2-2-2-3h1v-1c-2-1-3-2-5-3l-2-4z" class="I"></path><path d="M101 326c2 1 2 1 3 1l1 1h1c1 1 2 2 2 4h0l3 5-3-3v-1c-2-1-3-2-5-3l-2-4z" class="G"></path><path d="M93 259h2v2c-1 0-1 6-1 7v1l1 4h0c1 2 1 3 1 5 1 1 1 2 2 3h0c0 1 0 1 1 2 3 7 6 12 11 18 1 1 1 2 1 4v1c2 4 5 6 8 8 1 1 1 2 2 2h-2c-2-1-5-2-6-4h1 0c0 1 1 1 1 1l1 1c1 0 2 1 3 1 0 0-1 0-1-1h-1c0-1-1-1-1-1-1-1-3-2-3-3l-2-2c0-1 0-1-1-1 0-1 0-1-1-2 0-1-2-3-3-4 0 0-1 1-1 0-1 0-2-1-3-2 0-1 0-1-1-1h0v2 1c-1 0 0 0-1 1l2 2h0c0 1 1 2 2 2 1 2 3 4 4 5 2 1 4 3 5 5-2-1-3-2-5-4-3-3-6-6-9-10-3 12-11 23-21 30 1-2 3-3 5-5 3-6 4-13 4-19-1-4-2-8-3-11 0-3-3-11-1-13l5-9h0l-1-1c-1 1-3 3-5 4l-1-1c1 0 2-1 3-1v-1c-1 1-2 1-2 1l3-2c2-2 4-3 5-6l-1-1c1-1 1-2 1-3l2-1c1-2 1-3 1-4z" class="l"></path><path d="M105 299c2 1 3 1 5 2 1 1 1 2 1 4v1c-2-2-4-5-6-7z" class="T"></path><path d="M95 273c1 2 1 3 1 5 1 1 1 2 2 3h0c0 1 0 1 1 2 3 7 6 12 11 18-2-1-3-1-5-2-1-3-9-16-9-18v-1c-1-2-1-4-1-7z" class="E"></path><path d="M93 259h2v2c-1 0-1 6-1 7v1l-1 2h0c-1-1 0-1 0-2h0v-1-2-1h0 0c0 1 0 2-1 3-1 0-1 0-2 1 0 1-1 2-1 3h0 1c1 1 1 1 2 1v4c0-1 0-1-1-2 0-1 0-1-1-2-1 0-2 0-2-1-1 1-1 0-1 1h0c1 0 1 1 1 1v1l-1-1c-1 1-3 3-5 4l-1-1c1 0 2-1 3-1v-1c-1 1-2 1-2 1l3-2c2-2 4-3 5-6l-1-1c1-1 1-2 1-3l2-1c1-2 1-3 1-4z" class="J"></path><path d="M92 263v4c-1 0-2 0-2 1l-1-1c1-1 1-2 1-3l2-1z" class="L"></path><path d="M242 37l1-1c1 0 2 1 2 2l1 1c0 2 0 3 1 4v1 1 2l-1-2h-1c3 8 10 14 17 18 3 2 7 4 10 6 3 3 7 6 10 9 1 3 3 5 5 7h2l18 3h3l1 1c1 0 2 1 3 1-1-1-3-2-4-2v-1c3 1 5 2 7 3l2 1c2 2 4 3 6 5v-2-1-1s0-1-1-2c0-1-1-2-1-3v-1h0l5 4c0-1-1-1-1-2s0-1-1-1v-2c0-1 0-1 1-1v-1l-1-4h0c2 2 2 4 3 6l3 3v1h1v-3h0l1-1h0l1 1h0v11 10l1 7v3c0 2-1 3 0 4v1 1 2c0 1 0 2-1 4 1 3 1 6 1 9h1 1v2c0 1 1 2 0 2l2 6h0v3h1l-1 1h0c-1-1-1-1-2-1v5h-1-1v-1c0-1-1-2-1-2v-2h-2c0 1 0 3-1 4-2 1-4 1-6 0h-2c1 2 4 3 7 3 1-2 2-3 3-5h0v2h0 0v3l-2 2c-1 1-2 1-4 1h-4s-1-1-1-2l-1 1c0 1 1 3 2 3 1 2 1 3 2 5h-1c-2-1-4-5-5-7-3-4-6-6-11-7-2-1-5-2-7-3-1-1-2-3-2-4v-1c-1 0-1-1-1-2-1 0-2-1-3-2 0 0-1-2-2-2-1-1-2-1-3-2l-1-1c-1 2-1 4-1 5 0-1 0-3-1-4h-1c-1-1 0-1 0-3 0-1-1-2-1-3v-1h-1v2l1 1v1 2h-2v-5c0-1 0-1-1-2s-2-1-3-1h-1v-2c-2-1-4-1-5-2s-2-1-2-1h-3c1-1 2-1 2-2l-1-1-3-4v-1c-1-1-1-2-2-2v-1h0c-1 1-1 1-2 1s-1 1-2 1h0-1l-1-1 2-2 1 1c1 0 1 0 2-1v-1-1l-1-1-1-1h0-2c0-1 0-2-1-2l-1-1v-1h0c0-1-1-1-1-2h1s0-1 1-1c-2-2-3-3-5-4-1 0-2 0-3-1h-1v1h0c-1 1-1 0-1 1 0 0-1 0-1 1h-1 0l-1 1c-1 0-1 0-2-1 2-1 5-2 7-3l1-1h-1c-1 1-1 1-2 1 0-1-1-1-1-2 1-1 2-1 3-2h-1s-1 0-1-1v-1h0l1-1c-1-1-2-1-2-2v-1l-1 1-1-1-1-1h-1l-1-1h0-2v-2l2-2h-1v-1h0v-1c0-1 0-2 1-3v-2h-1l-1 1c-4-11-4-24 0-35l1-2z" class="h"></path><path d="M275 88c1 1 2 1 4 2 0 0 2 0 2 1s0 1-1 2v-1c-1-1-2-1-3-1-2-1-2-1-2-3z" class="G"></path><path d="M309 148h1c1-1-1-2 1-2 1 0 1 1 1 2 1 0 0 1 0 1 0 1-1 1-2 1h-3c1-1 1-1 2-1v-1z" class="T"></path><path d="M268 104c-1 0-2 0-2-1h-2l-1-1v-1l1-1h0c1 1 2 2 4 3h0v1z" class="G"></path><path d="M254 88v1c2 1 3 2 5 2-2 1-3 2-5 1-1-1-1-2-2-3l2-1z" class="R"></path><path d="M276 102l2 1-1 2c-1 0-1 0-3 1-2 0-4-1-6-2v-1c2 0 3 1 4 1h5c-1 0-1-1-1-2z" class="j"></path><path d="M282 78c1 3 3 5 5 7-2 0-4-1-6-2-1-1-1-1-1-2 1-1 1-1 1-2l1-1z" class="G"></path><path d="M259 101h0c2 2 2 3 2 5l1 2h0 0-2c0-1 0-2-1-2l-1-1v-1h0c0-1-1-1-1-2h1s0-1 1-1z" class="d"></path><path d="M259 101c2 2 2 3 2 5l-2-1 1-1-1-3z" class="j"></path><path d="M307 150c-1-1-3-2-3-4-1-1-1-4 0-6 1 1 1 4 2 6 0 1 1 2 2 2h1v1c-1 0-1 0-2 1z" class="C"></path><path d="M259 93h1c1 0 2 0 3 1s2 2 2 3l-1 1-3-2c-2-1-3-2-3-3h1z" class="I"></path><path d="M242 37l1-1c1 0 2 1 2 2l1 1c0 2 0 3 1 4v1 1 2l-1-2h-1c-1-2-2-5-2-7l-1-1z" class="H"></path><path d="M250 92v-4s-1-1-1-2h0l2 2 1 1c1 1 1 2 2 3l-1 1c0 1 0 1-1 2h-1c-1 1-1 1-2 1 0-1-1-1-1-2 1-1 2-1 3-2h-1z" class="G"></path><path d="M251 92l1 2h0l-1 1c-1 1-1 1-2 1 0-1-1-1-1-2 1-1 2-1 3-2z" class="V"></path><path d="M248 76c1 2 2 5 4 7 0 2 1 4 2 5l-2 1-1-1-2-2v-1-1c0-1 1-2 1-3l-1-1c-1-2-1-2-1-4z" class="M"></path><path d="M252 83c0 2 1 4 2 5l-2 1-1-1v-2c-1 0-1-1-1-2l2-1z" class="g"></path><path d="M275 88c-4 0-8-2-12-3h-2c2-2 9 1 11 0h1 0c1 0 2 1 3 1s1 0 0-1l1 1c0 1 0 1-1 2h-1z" class="F"></path><path d="M258 74c-1-1-4-3-5-4 0-1 0-3-1-4v-3c4 1 6 5 9 6l1 1h0-1-3l-1-1-1 1c1 1 3 1 3 3h-1v1z" class="Z"></path><path d="M278 103l3 2c4 1 7 3 12 2 1 0 2 0 3 1l-7 2v-1c-1-1-4-1-5-1l-8-2h-2c2-1 2-1 3-1l1-2z" class="O"></path><path d="M278 103l3 2v1h-4-1 0-2c2-1 2-1 3-1l1-2z" class="G"></path><path d="M286 132l9 6c1 1 3 2 3 3 1 1 1 2 1 3 1 1 1 2 1 2-1 0-1-1-1-2-1 0-2-1-3-2 0 0-1-2-2-2-1-1-2-1-3-2l-1-1c-1 2-1 4-1 5 0-1 0-3-1-4h-1c-1-1 0-1 0-3 0-1-1-2-1-3z" class="X"></path><path d="M273 98h-2 0c-2-1-3-3-5-5h0 3c1 1 1 0 2 0s2 0 3 1h3 0-2v1s0 1 1 1c1 1 3 2 4 2s2 1 2 2c-1 1-1 0-3 0s-4-2-6-2h0z" class="Q"></path><path d="M261 75l3 2c0 1 0 2 1 3h2l1 1h4c-1 0-4-2-4-3h1l-1-1 4 1v3c2 1 3 3 4 4h0c1 1 1 1 0 1s-2-1-3-1h0l-12-6-2-2 2-2z" class="W"></path><path d="M258 74v-1h1c0-2-2-2-3-3l1-1 1 1h3 1c1 1 1 2 2 3l2 2c1 1 2 1 2 2l1 1h-1c0 1 3 3 4 3h-4l-1-1h-2c-1-1-1-2-1-3l-3-2-3-1z" class="K"></path><path d="M258 74v-1h1c0-2-2-2-3-3l1-1 1 1 2 2c1 0 1 1 1 1l3 2 1 1v1h-1l-3-2-3-1z" class="T"></path><path d="M286 126l5 3c3 1 5 3 8 4 1-1 0-1 1-1 1-1 1 0 2 0h2c0 2 1 4 2 5h1c1 1 2 2 2 3l-6-3s0-1-1-1h-1c-4-2-8-4-12-7-2 0-2-2-3-3z" class="g"></path><path d="M266 111v-1c3 3 7 7 12 8 2 1 5 1 7 3l-2-1-1 1c1 1 2 1 3 2l3 3c1 0 1 1 2 1 0 1 0 1 1 1h0v1l-5-3c-2-1-4-1-5-3-2-1-3-2-5-3-5-2-8-5-10-9z" class="I"></path><defs><linearGradient id="Ay" x1="248.973" y1="60.572" x2="241.172" y2="78.96" xlink:href="#B"><stop offset="0" stop-color="#070506"></stop><stop offset="1" stop-color="#404241"></stop></linearGradient></defs><path fill="url(#Ay)" d="M240 56c2 2 3 3 3 5l5 15c0 2 0 2 1 4l1 1c0 1-1 2-1 3-3-3-5-9-6-13-2-4-3-10-3-15z"></path><defs><linearGradient id="Az" x1="277.778" y1="116.244" x2="288.367" y2="113.248" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#8c8c8e"></stop></linearGradient></defs><path fill="url(#Az)" d="M266 111l-1-2h0c1 0 2 0 3 1 4 1 7 1 10 2h-3c1 1 4 2 5 2 4 1 9 1 13 1-2 2-6 0-8 2h0-1c-2 2-4 1-6 1-5-1-9-5-12-8v1z"></path><path d="M266 111l-1-2h0c1 0 2 0 3 1 0 1 1 1 3 2 4 2 8 4 13 5-2 2-4 1-6 1-5-1-9-5-12-8v1z" class="j"></path><path d="M285 123c-1-1-2-1-3-2l1-1 2 1c4 0 7 1 11 3v1l1 1 2 2v3 2c-3-1-5-3-8-4v-1h0c-1 0-1 0-1-1-1 0-1-1-2-1l-3-3z" class="W"></path><path d="M290 126v-2h2 3 1v1l1 1 2 2h0c-2 1-3 0-4 0-2-1-4-1-5-2z" class="m"></path><path d="M285 123c-1-1-2-1-3-2l1-1 2 1c4 0 7 1 11 3h-1-3-2v2c-1-1-3-2-5-3z" class="a"></path><path d="M262 70h1c1 1 2 1 3 1 2 1 7 2 8 4h0l-7-1c2 1 5 2 6 3l3 3c1 1 0 1 1 3l2 2h0c0 1 1 2 1 3l3 2c1 0 0-1 1 0 1 0 2 1 3 1 0 0 1-1 2-1h1c1-1 1-1 2-1l1-1 1-1v1c-1 1-2 2-4 3-3 1-4 1-7 1l-1 1h1-1c-1 0-1 0-2 1h-2 0 2v-1c1-1 1-1 1-2s-2-1-2-1c-2-1-3-1-4-2h0 1c1-1 1-1 1-2l-1-1h0c-1-1-2-3-4-4v-3l-4-1c0-1-1-1-2-2l-2-2c-1-1-1-2-2-3h0z" class="Q"></path><path d="M272 78s1 1 2 1c0 1 1 3 1 4 1 0 1 0 1 1 2 1 3 3 3 4v1 1c-2-1-3-1-4-2h0 1c1-1 1-1 1-2l-1-1h0c-1-1-2-3-4-4v-3z" class="S"></path><defs><linearGradient id="BA" x1="289.384" y1="109.308" x2="292.06" y2="117.7" xlink:href="#B"><stop offset="0" stop-color="#b7b6b7"></stop><stop offset="1" stop-color="#d8d6d6"></stop></linearGradient></defs><path fill="url(#BA)" d="M296 108h5l1 1h-1 0c-3 1-7 1-11 3h8l1-1c1 0 3 1 5 1-1 2-1 2-2 2l-1 1h1l-1 1 1 1c2 0 2 0 3 1l-1 1-1-1c-3 0-4-2-6-3h-4c-4 0-9 0-13-1-1 0-4-1-5-2h3 1 0c4 0 6-1 10-2l7-2z"></path><defs><linearGradient id="BB" x1="247.666" y1="49.64" x2="234.169" y2="66.786" xlink:href="#B"><stop offset="0" stop-color="#191816"></stop><stop offset="1" stop-color="#504e52"></stop></linearGradient></defs><path fill="url(#BB)" d="M241 74c-4-11-4-24 0-35 1 3 0 4 0 7l-1 10c0 5 1 11 3 15 1 4 3 10 6 13v1 1h0c0 1 1 2 1 2v4s-1 0-1-1v-1h0l1-1c-1-1-2-1-2-2v-1l-1 1-1-1-1-1h-1l-1-1h0-2v-2l2-2h-1v-1h0v-1c0-1 0-2 1-3v-2h-1l-1 1z"></path><path d="M242 79h0v-1c0-1 0-2 1-3 1 3 3 7 6 10v1h0c0 1 1 2 1 2v4s-1 0-1-1v-1h0l1-1c-1-1-2-1-2-2v-1l-1 1-1-1-1-1h-1l-1-1h0-2v-2l2-2h-1v-1z" class="Q"></path><path d="M245 83l2 2 1 1-1 1-1-1-1-1h-1c0-1 1-1 1-2z" class="S"></path><path d="M242 79l1 1h1c0 2 0 2 1 3 0 1-1 1-1 2l-1-1h0-2v-2l2-2h-1v-1z" class="U"></path><path d="M290 91h3 1c1 1 2 1 2 2l-3 1h0c1 0 2 1 3 1v1h-1v2h1v1 1l2 1h-6-1-1-1c-3 0-7 0-10-1 2 0 2 1 3 0 0-1-1-2-2-2s-3-1-4-2c-1 0-1-1-1-1v-1h2 0 1 0 2c1-1 1-1 2-1h1-1l1-1c3 0 4 0 7-1z" class="H"></path><path d="M296 98l-1 1c-1 0-3 1-4 0l1-1h3 1zm-3-4c1 0 2 1 3 1v1h-1v1l-2-1c-1 0-1-1-1-1l1-1z" class="L"></path><path d="M293 91h1c1 1 2 1 2 2l-3 1c-3 0-3 0-5 2v2h-3c1 0 1-1 2-2v-2l1-1c1 0 2-1 3-1 1-1 2-1 2-1z" class="F"></path><path d="M280 98v-1h2 1 2v-1l1-1-1-1c1-1 2-1 3-1l-1 1v2c-1 1-1 2-2 2h-1v1c1 1 2 0 3 1h3 0v1h-1c-3 0-7 0-10-1 2 0 2 1 3 0 0-1-1-2-2-2z" class="S"></path><path d="M290 91h3s-1 0-2 1c-1 0-2 1-3 1s-2 0-3 1l1 1-1 1v1h-2-1-2v1c-1 0-3-1-4-2-1 0-1-1-1-1v-1h2 0 1 0 2c1-1 1-1 2-1h1-1l1-1c3 0 4 0 7-1z" class="O"></path><path d="M278 94h0 2c1-1 1-1 2-1h1-1l1-1 1 1c-1 2-2 2-3 3-2 0-4-1-5 0-1 0-1-1-1-1v-1h2 0 1z" class="M"></path><path d="M273 98h0c2 0 4 2 6 2 3 1 7 1 10 1h1 1 1 6 2 1l1 2c-1 0-1 0-1 1h1 2v1s-1 1-1 2 0 1 1 2h-2l-1-1h-5c-1-1-2-1-3-1-5 1-8-1-12-2l-3-2-2-1c-1-1-2-2-3-4z" class="K"></path><path d="M302 104h2v1s-1 1-1 2h0-1c-1 0-1 0-2-1h1v-1l1-1z" class="E"></path><path d="M291 101h1 6 2 1l1 2c-1 0-1 0-1 1h1l-1 1v1h-1c-4-1-8-1-12-2-1 0-2 0-3-1h-1l-1-1h0c1 0 1 0 2-1h4 1 1z" class="C"></path><path d="M291 101h1v1c1 1 2 1 3 2 2 0 3-1 5-1l1 1h1l-1 1h0c-4 0-7 0-10-2h0v-2z" class="a"></path><path d="M298 101h2 1l1 2c-1 0-1 0-1 1l-1-1c-2 0-3 1-5 1-1-1-2-1-3-2v-1h6z" class="i"></path><path d="M300 101h1l1 2c-1 0-1 0-1 1l-1-1h-1-1c-2 0-2 0-3-1v-1h5 0z" class="f"></path><path d="M293 115h4c1 1 1 2 2 2 1 1 2 1 2 2h1v3h1c1 1 1 1 1 2 1 1 3 2 4 3h4c2 1 6 2 8 4h-3s-1-1-2-1c-1-1-4-2-6-2v1h-3c-1 1-1 1-2 1v2h-2c-1 0-1-1-2 0-1 0 0 0-1 1v-2-3l-2-2-1-1v-1c-4-2-7-3-11-3-2-2-5-2-7-3 2 0 4 1 6-1h1 0c2-2 6 0 8-2z" class="N"></path><path d="M299 124l2 1c1 1 3 1 3 1 1 1 1 2 2 2l1 1h-1c-1 1-1 1-2 1v2h-2c-1 0-1-1-2 0-1 0 0 0-1 1v-2-3l-2-2-1-1c1-1 2-1 3-1z" class="M"></path><path d="M299 124l2 1 1 3h0c-1 0-1-1-2-1-1-1-2-1-3-1l-1-1c1-1 2-1 3-1z" class="Q"></path><path d="M284 117h1 1c4 2 10 4 13 7-1 0-2 0-3 1v-1c-4-2-7-3-11-3-2-2-5-2-7-3 2 0 4 1 6-1z" class="V"></path><defs><linearGradient id="BC" x1="286.941" y1="117.386" x2="296.192" y2="115.54" xlink:href="#B"><stop offset="0" stop-color="#9a9898"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#BC)" d="M293 115h4c1 1 1 2 2 2 1 1 2 1 2 2h1v3l-7-3h-1c-2-1-6-1-8-2h-1 0c2-2 6 0 8-2z"></path><path d="M304 105l1 1v2c2 1 3 1 5 2h0c1 0 2 1 3 1h2c1 1 3 2 4 3h1v-2c3 4 6 8 8 12 1 1 1 1 1 2l1 1c0 2 1 3 1 4l-2-2c0 2 1 3 1 5v1s2 3 2 4c-1 1 0 2-1 3l-1-1-1 2c-1-1-1-2-2-3-2-3-6-6-10-9h3c-2-2-6-3-8-4h-4c-1-1-3-2-4-3 0-1 0-1-1-2h-1v-3h-1c0-1-1-1-2-2-1 0-1-1-2-2 2 1 3 3 6 3l1 1 1-1c-1-1-1-1-3-1l-1-1 1-1h-1l1-1c1 0 1 0 2-2-2 0-4-1-5-1l-1 1h-8c4-2 8-2 11-3h0 1 2c-1-1-1-1-1-2s1-2 1-2z" class="f"></path><path d="M297 115c2 1 3 3 6 3l1 1c1 0 2 1 2 1v2c0 2 4 4 6 5h-4c-1-1-3-2-4-3 0-1 0-1-1-2h-1v-3h-1c0-1-1-1-2-2-1 0-1-1-2-2z" class="C"></path><path d="M321 131c2 0 5 1 6 3l1 1c0-1 0-1-1-1v-2c-1-2-5-5-8-6-1 0-2 0-3-1l-3-1c-1 0-2-1-3-2 1 0 2 0 3 1h1l5 2 4 3c2 1 4 3 6 5l1 2s2 3 2 4c-1 1 0 2-1 3l-1-1-1 2c-1-1-1-2-2-3-2-3-6-6-10-9h3 1z" class="E"></path><defs><linearGradient id="BD" x1="325.857" y1="134.935" x2="321.949" y2="137.067" xlink:href="#B"><stop offset="0" stop-color="#3a3939"></stop><stop offset="1" stop-color="#5f5d5f"></stop></linearGradient></defs><path fill="url(#BD)" d="M317 131h3 1c3 3 7 6 9 10l-1 2c-1-1-1-2-2-3-2-3-6-6-10-9z"></path><path d="M304 105l1 1v2c2 1 3 1 5 2h0c1 0 2 1 3 1h2c1 1 3 2 4 3h1v-2c3 4 6 8 8 12 1 1 1 1 1 2l1 1c0 2 1 3 1 4l-2-2c0 2 1 3 1 5v1l-1-2c-2-2-4-4-6-5 1-2 1-2 0-4-4-3-8-5-13-7-3 0-6-1-8-2h-1l1-1c1 0 1 0 2-2-2 0-4-1-5-1l-1 1h-8c4-2 8-2 11-3h0 1 2c-1-1-1-1-1-2s1-2 1-2z" class="m"></path><path d="M302 114c0 1 1 1 1 1h1c0 1 1 1 1 1l-1-2c1 0 1 1 2 1s2 1 3 1l8 3c3 1 4 2 6 4v1c-4-3-8-5-13-7-3 0-6-1-8-2h-1l1-1z" class="Y"></path><path d="M304 105l1 1v2c2 1 3 1 5 2l-1 1c2 1 6 3 8 5-3 0-6-2-9-3-1-1-3-2-5-3-1 0-1-1-2-1h1 2c-1-1-1-1-1-2s1-2 1-2z" class="C"></path><path d="M304 105l1 1v2c2 1 3 1 5 2l-1 1c-2-1-4-2-5-2-1-1-1-1-1-2s1-2 1-2z" class="O"></path><defs><linearGradient id="BE" x1="316.274" y1="121.632" x2="327.111" y2="119.101" xlink:href="#B"><stop offset="0" stop-color="#8c8a8a"></stop><stop offset="1" stop-color="#aaa"></stop></linearGradient></defs><path fill="url(#BE)" d="M310 110h0c1 0 2 1 3 1h2c1 1 3 2 4 3h1v-2c3 4 6 8 8 12 1 1 1 1 1 2l1 1c0 2 1 3 1 4l-2-2c0-1-1-2-2-3-3-4-6-7-10-10-2-2-6-4-8-5l1-1z"></path><path d="M331 121l4 5v3c1 3 1 6 1 9h1 1v2c0 1 1 2 0 2l2 6h0v3h1l-1 1h0c-1-1-1-1-2-1v5h-1-1v-1c0-1-1-2-1-2v-2h-2c0 1 0 3-1 4-2 1-4 1-6 0h-2-1c-3-3-6-6-8-10l-2-2c-1-1-2-2-4-3 0-1-1-2-2-3h-1c-1-1-2-3-2-5v-2c1 0 1 0 2-1h3v-1c2 0 5 1 6 2 1 0 2 1 2 1 4 3 8 6 10 9 1 1 1 2 2 3l1-2 1 1c1-1 0-2 1-3 0-1-2-4-2-4v-1c0-2-1-3-1-5l2 2c0-1-1-2-1-4l-1-1c0-1 0-1-1-2l1-1c1 1 1 2 2 4 0-1 1-1 1-1h0c-1-1-1-1-1-2v-3z" class="f"></path><path d="M306 129h3v-1c2 0 5 1 6 2 1 0 2 1 2 1 4 3 8 6 10 9h-2c-1-2-2-3-4-4-1-1-3-2-4-2-3-1-6-4-9-4-1 0-1 0-2 1 0 1 0 3 1 4 1 0 1 0 1 1 1 1 1 2 3 3l1 1c1 1 1 2 1 3-1-1-2-2-4-3 0-1-1-2-2-3h-1c-1-1-2-3-2-5v-2c1 0 1 0 2-1z" class="T"></path><path d="M325 140h2c1 1 1 2 2 3 1 2 3 6 3 8l-2 2c-2 1-5 0-7-1s-3-3-5-5c-1-1-2-1-3-2l-2-2c0-1 0-2-1-3 2 1 3 2 4 3 3 2 6 5 9 6v-2h0 0v-1h1v-2c1 1 1 2 2 3v1l1 1v-2s0-1-1-1l-1-3c0-1-1-2-2-3z" class="a"></path><path d="M331 121l4 5v3c1 3 1 6 1 9h1 1v2c0 1 1 2 0 2l2 6h0v3h1l-1 1h0c-1-1-1-1-2-1v5h-1-1v-1c0-1-1-2-1-2v-2h-2c0 1 0 3-1 4-2 1-4 1-6 0h-2-1c-3-3-6-6-8-10 1 1 2 1 3 2 2 2 3 4 5 5s5 2 7 1l2-2c0-2-2-6-3-8l1-2 1 1c1-1 0-2 1-3 0-1-2-4-2-4v-1c0-2-1-3-1-5l2 2c0-1-1-2-1-4l-1-1c0-1 0-1-1-2l1-1c1 1 1 2 2 4 0-1 1-1 1-1h0c-1-1-1-1-1-2v-3z" class="F"></path><path d="M338 151v-8-1l2 6h0v3h1l-1 1h0c-1-1-1-1-2-1v5h-1-1v-1c0-1-1-2-1-2v-2h-2c0-1 0-1 1-2 1 0 1 1 2 2 0 1 0 2 1 3h0c1-1 1-2 1-3z" class="M"></path><path d="M337 138h1v2c0 1 1 2 0 2v1 8h-1c-1-1-1-1-1-2-1-2-2-2-4-4 0-1-1-2-1-3 1-1 0-2 1-3l1 3c1 2 2 3 3 4h1v-8z" class="H"></path><path d="M331 121l4 5v3c1 3 1 6 1 9h1v8h-1c-1-1-2-2-3-4l-1-3c0-1-2-4-2-4v-1c0-2-1-3-1-5l2 2c0-1-1-2-1-4l-1-1c0-1 0-1-1-2l1-1c1 1 1 2 2 4 0-1 1-1 1-1h0c-1-1-1-1-1-2v-3z" class="K"></path><path d="M329 129l2 2c2 3 3 6 4 11h0-2l-1-3c0-1-2-4-2-4v-1c0-2-1-3-1-5z" class="l"></path><defs><linearGradient id="BF" x1="331.109" y1="130.639" x2="335.301" y2="127.099" xlink:href="#B"><stop offset="0" stop-color="#c8c8c6"></stop><stop offset="1" stop-color="#eae9eb"></stop></linearGradient></defs><path fill="url(#BF)" d="M331 121l4 5v3c1 3 1 6 1 9h-1c0-1 0-2-1-3 0-3-2-6-3-8 0-1 1-1 1-1h0c-1-1-1-1-1-2v-3z"></path><path d="M326 79h0c2 2 2 4 3 6l3 3v1h1v-3h0l1-1h0l1 1h0v11 10l1 7v3c0 2-1 3 0 4v1 1 2c0 1 0 2-1 4v-3l-4-5v3c0 1 0 1 1 2h0s-1 0-1 1c-1-2-1-3-2-4l-1 1c-2-4-5-8-8-12v2h-1c-1-1-3-2-4-3h-2c-1 0-2-1-3-1h0c-2-1-3-1-5-2v-2l-1-1v-1h-2-1c0-1 0-1 1-1l-1-2h-1-2l-2-1v-1-1h-1v-2h1v-1c-1 0-2-1-3-1h0l3-1c0-1-1-1-2-2h-1-3c2-1 3-2 4-3l1-1c-3 0-4 0-6-2l18 3h3l1 1c1 0 2 1 3 1-1-1-3-2-4-2v-1c3 1 5 2 7 3l2 1c2 2 4 3 6 5v-2-1-1s0-1-1-2c0-1-1-2-1-3v-1h0l5 4c0-1-1-1-1-2s0-1-1-1v-2c0-1 0-1 1-1v-1l-1-4z" class="N"></path><path d="M296 96h0 1c0 1 0 1 1 2v1h-2v-1h-1v-2h1z" class="B"></path><path d="M296 95c1-2 1-2 3-2v1l-1 1-1 1 1 2c-1-1-1-1-1-2h-1 0v-1z" class="O"></path><path d="M301 101c2 0 3 0 5 1l-2 2h-2-1c0-1 0-1 1-1l-1-2z" class="i"></path><path d="M306 102l1 1v1l-2 4v-2l-1-1v-1l2-2z" class="S"></path><path d="M315 103h0c1 1 2 1 3 1h1-2c-1 2 0 2 0 3l-6-3h3l1-1z" class="a"></path><path d="M324 115c1 0 2 1 3 1h2l2 5v3c0 1 0 1 1 2h0s-1 0-1 1c-1-2-1-3-2-4l-5-8z" class="l"></path><path d="M329 116l2 5v3c-1-3-3-5-4-8h2z" class="f"></path><path d="M319 104l2 2c2 4 5 6 8 10h-2c-1 0-2-1-3-1-2-3-5-6-7-8 0-1-1-1 0-3h2zm-12 0c4 0 10 5 12 7l1 1v2h-1c-1-1-3-2-4-3h-2c-1 0-2-1-3-1h0c-2-1-3-1-5-2l2-4z" class="i"></path><path d="M307 104c0 1 0 2 1 2 0 1 2 2 3 2 2 1 3 2 4 3h-2c-1 0-2-1-3-1h0c-2-1-3-1-5-2l2-4z" class="T"></path><path d="M314 101c0-1 0-2-1-3 2 1 3 1 5 2s2 3 5 3l4 4c0 2 2 4 3 5h1 0 0c1 2 2 5 2 7h1c1 1 1 2 2 4v2c0 1 0 2-1 4v-3l-4-5-2-5c-3-4-6-6-8-10l-2-2h-1c-1 0-2 0-3-1 1 0 1 0 2-1l-3-1z" class="Y"></path><path d="M317 102c2 1 3 2 4 4h0l-2-2h-1c-1 0-2 0-3-1 1 0 1 0 2-1z" class="C"></path><path d="M334 119c1 1 1 2 2 4v2c0 1 0 2-1 4v-3l-2-7h1z" class="W"></path><path d="M299 94c2 1 3 1 5 1v-1l-1-1h0l9 1 2 2c1 1 2 1 3 1 0 0 1 0 1 1 1 1 4 3 5 5-3 0-3-2-5-3s-3-1-5-2c1 1 1 2 1 3l3 1c-1 1-1 1-2 1h0l-1 1h-3c-3-2-6-4-10-5-1 0-2 0-2-1s-1-1-1-3l1-1z" class="J"></path><path d="M309 97l-1-1 1-1c1 0 2 1 2 1 1 1 2 1 4 2 1 0 2 1 3 2-2-1-3-1-5-2 1 1 1 2 1 3l3 1c-1 1-1 1-2 1h0l-3-2c1 0 1-1 1-1l-1-1h-1c0-1 0-1-1-1h-1v-1z" class="C"></path><path d="M309 97c2 1 4 1 5 3v1h0l3 1c-1 1-1 1-2 1h0l-3-2c1 0 1-1 1-1l-1-1h-1c0-1 0-1-1-1h-1v-1z" class="T"></path><path d="M301 99c-1-1-1-1 0-2s2 0 3 0c3 1 6 3 8 4l3 2-1 1h-3c-3-2-6-4-10-5z" class="i"></path><path d="M289 85l18 3 4 2c3 0 5 2 7 3s3 2 4 4h-1l-2-1h0v1l-1 1c0-1-1-1-1-1-1 0-2 0-3-1l-2-2-9-1h0l1 1v1c-2 0-3 0-5-1v-1c-2 0-2 0-3 2-1 0-2-1-3-1h0l3-1c0-1-1-1-2-2h-1-3c2-1 3-2 4-3l1-1c-3 0-4 0-6-2z" class="I"></path><path d="M311 90c3 0 5 2 7 3v1c-1 0-3-1-3-1-4-2-7-2-11-3 4-1 6 1 9 1-1 0-1-1-2-1h0z" class="X"></path><path d="M294 91h1l7 1h4 1c1 0 2 1 3 1h1l1 1-9-1h0l1 1v1c-2 0-3 0-5-1v-1c-2 0-2 0-3 2-1 0-2-1-3-1h0l3-1c0-1-1-1-2-2z" class="R"></path><path d="M289 85l18 3 4 2h0c1 0 1 1 2 1-3 0-5-2-9-1-3 0-5 0-9 1h-1-1-3c2-1 3-2 4-3l1-1c-3 0-4 0-6-2z" class="B"></path><path d="M326 79h0c2 2 2 4 3 6l3 3v1h1v-3h0l1-1h0l1 1h0v11 10l1 7v3c0 2-1 3 0 4v1 1c-1-2-1-3-2-4h-1c0-2-1-5-2-7h0 0-1c-1-1-3-3-3-5l-4-4c-1-2-4-4-5-5l1-1v-1h0l2 1h1c-1-2-2-3-4-4s-4-3-7-3l-4-2h3l1 1c1 0 2 1 3 1-1-1-3-2-4-2v-1c3 1 5 2 7 3l2 1c2 2 4 3 6 5v-2-1-1s0-1-1-2c0-1-1-2-1-3v-1h0l5 4c0-1-1-1-1-2s0-1-1-1v-2c0-1 0-1 1-1v-1l-1-4z" class="L"></path><path d="M335 86h0v11 10 2l-1 1c-2-8-1-17 1-24z" class="F"></path><path d="M327 83c3 6 3 13 4 20-2-4-3-8-6-11 0 0 0-1-1-2 0-1-1-2-1-3v-1h0l5 4c0-1-1-1-1-2s0-1-1-1v-2c0-1 0-1 1-1v-1z" class="Z"></path><path d="M310 87c3 1 5 2 7 3l2 1c2 2 4 3 6 5v-2-1-1c3 3 4 7 6 11l3 16h-1c0-2-1-5-2-7h0 0-1c-1-1-3-3-3-5l-4-4c-1-2-4-4-5-5l1-1v-1h0l2 1h1c-1-2-2-3-4-4s-4-3-7-3l-4-2h3l1 1c1 0 2 1 3 1-1-1-3-2-4-2v-1z" class="C"></path><defs><linearGradient id="BG" x1="320.452" y1="97.072" x2="325.293" y2="103.261" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#BG)" d="M318 98l1-1v-1h0l2 1h1c2 2 3 4 4 7 1 2 1 2 1 3l-4-4c-1-2-4-4-5-5z"></path><path d="M325 92c3 3 4 7 6 11l3 16h-1c0-2-1-5-2-7-1-5-3-11-6-16v-2-1-1z" class="M"></path><path d="M433 81l1 1h2 2 0c1 0 3 1 4 1 1 1 1 1 2 1s1 0 2 1v2l-1-1h-3v1l6 1 2 1c0 1 1 1 1 2 1 0 5 1 6 1v1l12 2 7 1 2 1h0l1 1c1 1 2 2 2 3h1c1 2 3 2 4 3 2 1 3 2 4 4v1l1 2v1c0 2 1 2 2 3l1 1c1 0 3 1 4 1 2 2 3 4 3 7v1 3 2l-1 1c0 1-1 1-2 2 3 2 4 3 6 6 3 3 4 8 5 13 1 1 2 3 2 5-1 1-2 1-3 1h-3-1l-1-1v1h0l-1 1h-8l-3 1c-2 0-4 1-6 0-2 1-3 1-5 1h-1c-1 0-2 0-4 1h-1-2c-2 0-7 1-8 1v-1h1-2-1-1-1c-1 0-1-1-2-1l-1 1-1-1v-1c-1 0-1 1-1 2-1-1-1-1-2-1l-1-1-1 2h0l-1-1h0-1-1v-1l-1-1v1c0 2-1 2-2 4h-1-1-4l-1-1c1-1 1-1 2-1v-2l-3-9c-2-3-4-5-7-7-1-2-2-3-4-4l-3-3c-1 0-1 0-2-1-4-1-6-1-9-1l-2-2-2 1h-2v-1h-1c-1 0-2-1-3-1h-2 0l-1-1c-1 1-2 2-2 3s0 2-1 2v-1-1h0c1-1 1-1 1-2h1c0-1 0-1 1-2-1 0-1-1-1-1l-1-1-1 1c-1-1-1-1-1-2 0 0 0-1 1-2l1-1c1 0 2-1 3-2l-1-1-2 1c0 1 0 2-1 2-1 1-2 1-3 2-1 0-1 1-2 1h-1c0 1-1 1-1 1h-1c-2 2-4 3-6 5h0c-2-1-3-1-4-1l3-4v-1h0l-2 2v-1-1c1-1 1-1 1-3h-1-1l2-3h0-1c-1 0-2-1-3-1h-1 0c0-1 1-2 1-2 2-2 4-1 6-3h-1 0l-2-1h2c1-1 3-2 4-2 1-1 1-1 2-1l5-1h1c1 0 1-1 2-1v-3-1h1l2-1h3c0 1 0 1 1 2 2 0 3 0 5-1 0-1 1-1 2-1l2-2h0c2 0 3-1 4-2 1 0 2-1 3-1h1s1 0 2-1c0 0 1 0 2-1 0 0 1-1 2-1 0-1 2 0 3 0v-1c-1 0-1 0-2-1h5v-1 1l1-1h1v-2-1c-1 0-2 0-2-1l-2-1c1-1 2-2 3-4h0v-2l1-1z"></path><path d="M452 112l2-1 2 1c0 1 1 1 2 1-1 1-2 1-3 1h0c-1 0-3 0-4-1l1-1z" class="F"></path><path d="M467 126c1 0 2 1 3 2h0l3 3 1 3c-3-1-4-4-6-5-1 0-2-1-2-1v-1l-1-1h0 0 1 1z" class="B"></path><path d="M467 126c1 0 2 1 3 2h0c-1 0-1 0-1 1-1-1-2-2-3-2l-1-1h0 0 1 1z" class="G"></path><defs><linearGradient id="BH" x1="457.083" y1="113.073" x2="467.119" y2="117.702" xlink:href="#B"><stop offset="0" stop-color="#515151"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#BH)" d="M458 113l10 4s0 1-1 2l-12-5h0c1 0 2 0 3-1z"></path><path d="M450 107c1 1 1 2 1 3-2 2-5 3-7 4l-1-1c0-1-1-2-1-3 3 0 6-2 8-3z" class="J"></path><path d="M473 131c2 1 2 1 3 2h0c0 2 2 2 3 3l3 3 3 3c0 2 1 3 1 5l-1-3c-2 0-3 0-4-1l-3-3-4-6-1-3z" class="G"></path><path d="M478 140l1-1h1c0 1 0 0 1 1 1 0 3 2 4 4h0c-2 0-3 0-4-1l-3-3z" class="V"></path><defs><linearGradient id="BI" x1="469.565" y1="115.499" x2="480.006" y2="127.103" xlink:href="#B"><stop offset="0" stop-color="#89898a"></stop><stop offset="1" stop-color="#b8b7b5"></stop></linearGradient></defs><path fill="url(#BI)" d="M468 117l11 5c1 0 3 1 3 1 1 1 1 2 2 2-1 1-1 1-1 3-5-4-10-7-16-9 1-1 1-2 1-2z"></path><defs><linearGradient id="BJ" x1="426.3" y1="110.725" x2="434.182" y2="114.151" xlink:href="#B"><stop offset="0" stop-color="#525352"></stop><stop offset="1" stop-color="#6c696d"></stop></linearGradient></defs><path fill="url(#BJ)" d="M432 110h1c1-1 3-1 4-1v1c1 0 3-1 4 1l1-1c0 1 1 2 1 3l1 1-4 1-4 1h-2l-3 1-3 1-1-2h-1c-1 0-2-1-2-2 2-1 5-3 8-4z"></path><path d="M429 115h-1v-1c2 0 3-1 5-1h1 0 0l1 1v-1h3c-1 1-3 1-4 2h1c0 1 0 1 1 1h-2l-3 1-3 1-1-2 2-1z" class="N"></path><path d="M431 115c1 0 1-1 2-1v1l1 1-3 1-3 1-1-2 2-1h2z" class="I"></path><path d="M429 115h2c-1 1-1 1 0 2l-3 1-1-2 2-1z" class="S"></path><path d="M441 111l1-1c0 1 1 2 1 3l1 1-4 1-4 1c-1 0-1 0-1-1h-1c1-1 3-1 4-2h-3 0c1-1 4-2 6-2z" class="L"></path><path d="M441 111l1-1c0 1 1 2 1 3l1 1-4 1-1-1h0c1-1 2-1 3-1v-1c-1 0-1-1-1-1z" class="K"></path><defs><linearGradient id="BK" x1="476.621" y1="148.143" x2="483.623" y2="146.677" xlink:href="#B"><stop offset="0" stop-color="#a0a09f"></stop><stop offset="1" stop-color="#e3e2e4"></stop></linearGradient></defs><path fill="url(#BK)" d="M468 129c2 1 3 4 6 5l4 6 3 3c1 1 2 1 4 1l1 3v1c1 1 2 2 2 4 1 1 2 6 3 7h3l-3 1c-2 0-4 1-6 0v-1c-3-11-11-21-17-30z"></path><path d="M481 143c1 1 2 1 4 1l1 3v1c1 1 2 2 2 4h-3l-3-5-1-4z" class="Z"></path><path d="M481 143c1 1 2 1 4 1l1 3v1c-1-1-2-1-4-1l-1-4z" class="X"></path><path d="M485 152h3c1 1 2 6 3 7h3l-3 1c-2 0-4 1-6 0v-1h0l1-1v-3l-1-3z" class="c"></path><path d="M486 155l1 5h4c-2 0-4 1-6 0v-1h0l1-1v-3z" class="i"></path><path d="M488 130l-1-1h1c3 1 6 4 8 5 1 1 2 1 3 2 2 1 2 1 3 2l2 1c3 3 4 8 5 13 1 1 2 3 2 5-1 1-2 1-3 1h-3-1l-1-1v1h0l-1 1v-1c0-3-1-6-2-8-3-8-8-14-14-20h2z" class="Y"></path><path d="M488 130l-1-1h1c3 1 6 4 8 5 1 1 2 1 3 2 3 4 5 7 6 11h-1v1c-1-2-1-4-3-5h-1c-3-5-8-9-12-13z" class="f"></path><path d="M486 130h2c4 4 9 8 12 13l4 10v1l1 4h0-1l-1-1v1h0l-1 1v-1c0-3-1-6-2-8-3-8-8-14-14-20z" class="T"></path><defs><linearGradient id="BL" x1="464.888" y1="127.937" x2="491.688" y2="115.464" xlink:href="#B"><stop offset="0" stop-color="#262628"></stop><stop offset="1" stop-color="#41403f"></stop></linearGradient></defs><path fill="url(#BL)" d="M456 112c1-1 3-1 5 0 2 0 6 2 8 1h0 1l1-1c1 0 6 1 7 2 9 3 16 5 23 11v3 2l-1 1c0 1-1 1-2 2 3 2 4 3 6 6l-2-1c-1-1-1-1-3-2-1-1-2-1-3-2-2-1-5-4-8-5h-1l1 1h-2l-3-2c0-2 0-2 1-3-1 0-1-1-2-2 0 0-2-1-3-1l-11-5-10-4c-1 0-2 0-2-1z"></path><path d="M482 123c6 3 11 7 16 10 3 2 4 3 6 6l-2-1c-1-1-1-1-3-2-1-1-2-1-3-2-2-1-5-4-8-5h-1l1 1h-2l-3-2c0-2 0-2 1-3-1 0-1-1-2-2z" class="J"></path><path d="M484 125c2 1 3 2 4 4h0-1l1 1h-2l-3-2c0-2 0-2 1-3z" class="a"></path><path d="M470 113l1-1c1 0 6 1 7 2l-1 1c1 1 4 2 6 2l15 9-1 3c-3-3-8-6-12-8-2-2-5-3-8-4-2-1-5-3-7-4z" class="W"></path><path d="M478 114c9 3 16 5 23 11v3 2l-1 1c-1-1-2-1-3-2l1-3-15-9c-2 0-5-1-6-2l1-1z" class="a"></path><path d="M498 126l2 2h1v2l-1 1c-1-1-2-1-3-2l1-3z" class="c"></path><defs><linearGradient id="BM" x1="452.441" y1="120.42" x2="451.811" y2="126.326" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#4b494b"></stop></linearGradient></defs><path fill="url(#BM)" d="M441 118c3-1 4-1 7-1l1 1h4 0c1 1 2 2 3 2 1 1 2 2 3 2l6 3s1 0 2 1h-1-1 0 0l1 1v1s1 1 2 1c6 9 14 19 17 30v1c-2 1-3 1-5 1h-1c-1 0-2 0-4 1h-1-2c-2 0-7 1-8 1v-1h1-2-1c-1-3-3-6-4-9 1 1 1 1 1 0v-3c0-1-1-2-2-3 1 0 2 0 3-1l-2-2h1c-1-2-1-2-1-4h-1l1-1v-1h-1l1-1-1-1c-1-1-2-1-3-3l-3-2-3-3h-2v-1h0c-2-2-5-4-8-5l-3-1h-2 0c1-1 1-2 3-2 1-1 3 0 5-1z"></path><path d="M444 121h1l-1-1v-1h1v1c1 1 2 1 2 2h0l-3-1z" class="V"></path><defs><linearGradient id="BN" x1="459.154" y1="127.828" x2="462.747" y2="134.717" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#989696"></stop></linearGradient></defs><path fill="url(#BN)" d="M458 131v-2h-1l1-1-1-1h0c3 2 6 5 8 7v1h0v3c-2-3-4-5-7-7z"></path><path d="M444 121l3 1c1 1 2 2 4 3h0c2-1 5 1 6 2h0 0l1 1-1 1h1v2c0-1-1-1-2-1-1-1-3-1-4-2-2-2-4-3-7-4 0-1-1-1-2-2l1-1z" class="N"></path><defs><linearGradient id="BO" x1="462.428" y1="137.711" x2="478.6" y2="154.916" xlink:href="#B"><stop offset="0" stop-color="#a6a4a3"></stop><stop offset="1" stop-color="#dcdcdd"></stop></linearGradient></defs><path fill="url(#BO)" d="M465 138v-3h0v-1c2 3 5 5 6 8l1 1 1 2 1 2s1 1 1 2h0c1 1 2 2 2 4l1 4v1c0 1 1 1 1 2v1c-1 0-2 0-4 1l1-1-1-5c-3-7-5-13-10-18z"></path><path d="M476 161c0-2 0-3 1-4l1 1c0 1 1 1 1 2v1c-1 0-2 0-4 1l1-1z" class="f"></path><defs><linearGradient id="BP" x1="471.998" y1="147.847" x2="478.703" y2="145.608" xlink:href="#B"><stop offset="0" stop-color="#b0b0af"></stop><stop offset="1" stop-color="#d3d2d5"></stop></linearGradient></defs><path fill="url(#BP)" d="M478 157h1l1-1c-1 0-1-1-1-2v-1c-1-1-1-1-1-2l-2-3c0-1-1-2-1-2 0-1-1-1-1-1 0-1-1-3-2-3-3-5-6-8-9-12h1c2 1 4 3 6 5 4 4 6 8 9 12 1 2 2 3 3 5 0 2 1 5 1 7 1 0 1 1 1 1h1c-2 1-3 1-5 1h-1v-1c0-1-1-1-1-2v-1z"></path><defs><linearGradient id="BQ" x1="458.565" y1="119.822" x2="456.481" y2="130.416" xlink:href="#B"><stop offset="0" stop-color="#070605"></stop><stop offset="1" stop-color="#38393b"></stop></linearGradient></defs><path fill="url(#BQ)" d="M441 118c3-1 4-1 7-1l1 1h4 0c1 1 2 2 3 2 1 1 2 2 3 2l6 3s1 0 2 1h-1-1 0 0l1 1v1s1 1 2 1c6 9 14 19 17 30v1h-1s0-1-1-1c0-2-1-5-1-7-1-2-2-3-3-5-3-4-5-8-9-12-2-2-4-4-6-5v-1c-3-2-8-6-11-7l-7-3c-2-1-3-1-5-1z"></path><defs><linearGradient id="BR" x1="474.623" y1="159.001" x2="438.683" y2="116.297" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242324"></stop></linearGradient></defs><path fill="url(#BR)" d="M441 118c2 0 3 0 5 1h-1-1v1l1 1h-1l-1 1c1 1 2 1 2 2 3 1 5 2 7 4 1 1 3 1 4 2 1 0 2 0 2 1 3 2 5 4 7 7 5 5 7 11 10 18l1 5-1 1h-1-2c-2 0-7 1-8 1v-1h1-2-1c-1-3-3-6-4-9 1 1 1 1 1 0v-3c0-1-1-2-2-3 1 0 2 0 3-1l-2-2h1c-1-2-1-2-1-4h-1l1-1v-1h-1l1-1-1-1c-1-1-2-1-3-3l-3-2-3-3h-2v-1h0c-2-2-5-4-8-5l-3-1h-2 0c1-1 1-2 3-2 1-1 3 0 5-1z"></path><path d="M475 156l1 5-1 1h-1l-2-2c1 1 1 1 1 0s1-2 1-4h1z" class="h"></path><path d="M454 133c1 0 1 0 2 1h1c1 1 1 1 2 1l2 2 1 1c3 3 5 7 6 11 1 3 3 6 3 10l-2-4h-1c-1-2-1-4-3-5-1-1-1-2-2-3h-1 0v2h0l-2-3-2-2h1c-1-2-1-2-1-4h-1l1-1v-1h-1l1-1-1-1c-1-1-2-1-3-3z" class="d"></path><path d="M458 137c1 2 3 3 4 5l6 9c0 1 1 3 1 4h-1c-1-2-1-4-3-5-1-1-1-2-2-3h-1 0v2h0l-2-3-2-2h1c-1-2-1-2-1-4h-1l1-1v-1h-1l1-1z" class="O"></path><path d="M458 140c2 2 4 4 5 7h-1 0v2h0l-2-3-2-2h1c-1-2-1-2-1-4z" class="N"></path><path d="M459 144l3 3v2h0l-2-3-2-2h1z" class="I"></path><path d="M460 146l2 3h0v-2h0 1c1 1 1 2 2 3 2 1 2 3 3 5h1l2 4s0 1 1 1l2 2h-2c-2 0-7 1-8 1v-1h1-2-1c-1-3-3-6-4-9 1 1 1 1 1 0v-3c0-1-1-2-2-3 1 0 2 0 3-1z" class="D"></path><path d="M460 151l1-1 2 1c0 1 0 1-1 2h-1l-1-2z" class="L"></path><path d="M460 146l2 3h0 1v2h0l-2-1-1 1-1-1c0-1-1-2-2-3 1 0 2 0 3-1z" class="H"></path><path d="M459 150l1 1 1 2c1 3 3 6 2 8v1h-1c-1-3-3-6-4-9 1 1 1 1 1 0v-3z" class="V"></path><path d="M465 150c2 1 2 3 3 5h1l2 4s0 1 1 1l2 2h-2c-2 0-7 1-8 1v-1h1-2v-1c1 0 2-1 3-2l-1-2c-1-1-1-3 0-4l1 1c0-1-1-3-1-4z" class="J"></path><path d="M465 150c2 1 2 3 3 5h1l2 4s0 1 1 1l2 2h-2c-1 0-2 0-2-1h-1c-1-2-2-5-3-7 0-1-1-3-1-4z" class="F"></path><path d="M433 121h2l3 1c3 1 6 3 8 5h0v1h2l3 3 3 2c1 2 2 2 3 3l1 1-1 1h1v1l-1 1h1c0 2 0 2 1 4h-1l2 2c-1 1-2 1-3 1 1 1 2 2 2 3v3c0 1 0 1-1 0 1 3 3 6 4 9h-1-1c-1 0-1-1-2-1l-1 1-1-1v-1c-1 0-1 1-1 2-1-1-1-1-2-1l-1-1-1 2h0l-1-1h0-1-1v-1l-1-1v1c0 2-1 2-2 4h-1-1-4l-1-1c1-1 1-1 2-1v-2l-3-9c-2-3-4-5-7-7-1-2-2-3-4-4l-3-3c-1 0-1 0-2-1-4-1-6-1-9-1l-2-2c1 0 1-1 2-1l-1-1c2-1 5-1 7-2l1 1 1-1h1l1-1 4-1 2-1h0c0-1 2-1 2-3h-1v-2h2 2z" class="h"></path><path d="M451 153h0l-4-6c1 1 3 2 4 3h0v-1h1l3 7-4-3h0z" class="d"></path><path d="M428 126c2 0 3 0 4 1h1l-5 2h-3c-1 1-2 1-2 1l-1-1h-1l1-1 4-1 2-1z" class="Q"></path><defs><linearGradient id="BS" x1="413.706" y1="130.954" x2="426.052" y2="140.283" xlink:href="#B"><stop offset="0" stop-color="#8d8c8d"></stop><stop offset="1" stop-color="#a9a8a9"></stop></linearGradient></defs><path fill="url(#BS)" d="M418 129l1 1c-2 1-5 1-7 2 8 1 15 3 20 8-1 0-4-1-5-1 0 1 0 1-1 1l-3-3c-1 0-1 0-2-1-4-1-6-1-9-1l-2-2c1 0 1-1 2-1l-1-1c2-1 5-1 7-2z"></path><path d="M433 121h2l3 1c3 1 6 3 8 5h0-2-11-1c-1-1-2-1-4-1h0c0-1 2-1 2-3h-1v-2h2 2z" class="E"></path><path d="M433 121h2l3 1-1 1v2s1 1 2 1c-1 0-2 0-3-1-1 0-2 0-3-1 0 0-1-1-2-1h0v-2h2z" class="T"></path><path d="M435 121l3 1-1 1v2h-1c-1 0-1-2-2-3l1-1z" class="l"></path><path d="M438 122c3 1 6 3 8 5h0-2c-2-1-4-1-5-1s-2-1-2-1v-2l1-1z" class="m"></path><path d="M452 149v-1c0-1 0-3-1-4v-1c1 0 3 3 4 5v1l3 4c1 3 3 6 4 9h-1-1c-1 0-1-1-2-1l-1 1-1-1v-1c-1 0-1 1-1 2-1-1-1-1-2-1l-1-1-1 2h0l-1-1h0 1v-2c1-3 1-3 0-6h0l4 3-3-7z" class="O"></path><path d="M452 160c1-1 1-2 3-3 0 1 1 1 1 3-1 0-1 1-1 2-1-1-1-1-2-1l-1-1z" class="S"></path><path d="M455 149l3 4c1 3 3 6 4 9h-1-1c-1 0-1-1-2-1l-1 1-1-1h1v-1-1c-1-2-2-3-1-5v-1c-1-2-1-3-1-4z" class="b"></path><path d="M456 153c1 1 1 2 2 4 0 0 1 0 1 1h0v1c1 1 1 2 1 3h1-1c-1 0-1-1-2-1l-1 1-1-1h1v-1-1c-1-2-2-3-1-5v-1z" class="U"></path><defs><linearGradient id="BT" x1="431.27" y1="160.47" x2="437.331" y2="141.954" xlink:href="#B"><stop offset="0" stop-color="#918f90"></stop><stop offset="1" stop-color="#b7b7b7"></stop></linearGradient></defs><path fill="url(#BT)" d="M426 140c1 0 1 0 1-1 1 0 4 1 5 1 5 4 9 10 10 16 0 3 0 6 1 8 1-1 2-2 3-4h1c0 2-1 2-2 4h-1-1-4l-1-1c1-1 1-1 2-1v-2l-3-9c-2-3-4-5-7-7-1-2-2-3-4-4z"></path><defs><linearGradient id="BU" x1="453.491" y1="136.714" x2="448.118" y2="148.247" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#BU)" d="M451 149c0-2-3-6-4-8-1-1-2-2-3-4l-2-2h0l3-1v-1h1c1 0 1 0 2 1h1s0-1-1-1l2-1c0 1 0 1 1 1h1l1 1c-1 1-1 0-3 0l1 1h-1c0 1 1 1 1 1v-1h2l3 3h0c0-1 1-2 1-2l1 1-1 1h1v1l-1 1h1c0 2 0 2 1 4h-1l2 2c-1 1-2 1-3 1 1 1 2 2 2 3v3c0 1 0 1-1 0l-3-4v-1c-1-2-3-5-4-5v1c1 1 1 3 1 4v1h-1z"></path><path d="M455 144v-1c1 0 2 0 3 1l2 2c-1 1-2 1-3 1s-2-2-2-3z" class="N"></path><path d="M454 143c-1 0-1-1-2-1-1-1-1-1-2-1-1-1-3-3-3-4h2l-1-1v-1h1l2 2h1v-1l1-1 3 3h0c0-1 1-2 1-2l1 1-1 1h1v1l-1 1h1c0 2 0 2 1 4h-1c-1-1-2-1-3-1v1l-1-1z" class="V"></path><path d="M454 143c-1-2-3-3-3-5h0l2 1c1 1 2 2 4 3-1-2-2-3-3-5l3 3h1c0 2 0 2 1 4h-1c-1-1-2-1-3-1v1l-1-1z" class="F"></path><path d="M433 81l1 1h2 2 0c1 0 3 1 4 1 1 1 1 1 2 1s1 0 2 1v2l-1-1h-3v1l6 1 2 1c0 1 1 1 1 2 1 0 5 1 6 1v1l12 2 7 1 2 1h0l1 1c1 1 2 2 2 3h1c1 2 3 2 4 3 2 1 3 2 4 4v1l1 2v1c0 2 1 2 2 3l1 1c1 0 3 1 4 1 2 2 3 4 3 7v1c-7-6-14-8-23-11-1-1-6-2-7-2l-1 1h-1 0c-2 1-6-1-8-1-2-1-4-1-5 0l-2-1-2 1v-1c1 0 1 0 2-1h-2-1c0-1 0-2-1-3-2 1-5 3-8 3l-1 1c-1-2-3-1-4-1v-1c-1 0-3 0-4 1h-1c-1-1-2 0-3 0 0-2 3-2 4-3-2 0-3 0-4 1l-1 1h-1v-2c2-2 5-2 8-3l-1-1h-3c3-1 7-2 10-4h0c-2-1-5 0-7-2h0l-6-2c-1 0-1 0-2-1h5v-1 1l1-1h1v-2-1c-1 0-2 0-2-1l-2-1c1-1 2-2 3-4h0v-2l1-1z" class="R"></path><path d="M454 99l-2-1v-1c2-1 3-1 5 0 2 0 4 1 5 2l-1 1-3-1h-4z" class="B"></path><path d="M454 99h4c-2 0-4 0-5 1h-2v1l1 1s0 1 1 1h-1-2-6v-1c1-1 2-1 3-2h2c2-1 3-1 5-1z" class="K"></path><path d="M444 103v-1c1-1 2-1 3-2l-1 2c2 1 4 0 6 1h-2-6z" class="H"></path><path d="M472 98c2 1 5 1 6 3v1l-4-1h-4c-1-1-5-1-6-1 3-1 5-1 8-2z" class="L"></path><path d="M444 98h1l-1 1c0 1-1 1-1 3-1 1-3 2-5 2h-1 0-2l-1-1h-3c3-1 7-2 10-4 0 0 1 0 1-1h2z" class="I"></path><path d="M472 98h-1c-4-1-11-1-15-3 3 0 6 0 9 1 2 0 3 0 4-1l7 1 2 1h0l1 1c1 1 2 2 2 3v1h-2l-1-1c-1-2-4-2-6-3z" class="Q"></path><path d="M469 95l7 1 2 1h0c-3 1-9-1-13-1 2 0 3 0 4-1z" class="I"></path><path d="M458 99l3 1 8 2c1 0 2 0 3 1l-1 1-4 2h0-1 0c-3 0-5 1-8 0l1-1 1-1h-3l-2-1h-2c-1 0-1-1-1-1l-1-1v-1h2c1-1 3-1 5-1z" class="E"></path><path d="M452 102v-1c1-1 1 0 2 0 0 1 1 1 1 2h-2c-1 0-1-1-1-1z" class="T"></path><path d="M467 106h-1c-2-1-3-1-5-1l-1-2h0v-1h0c2 0 3 1 4 1 2 0 6-1 7 1l-4 2h0z" class="a"></path><path d="M450 103h2 1 2l2 1h3l-1 1-1 1c3 1 5 0 8 0h0 1 0 1c1 1 1 1 1 2-1 1-3 1-4 1v1h-2l2 2c1 0 3 0 4 1h0c-2 1-6-1-8-1-2-1-4-1-5 0l-2-1-2 1v-1c1 0 1 0 2-1h-2-1c0-1 0-2-1-3h2c1-1 1 0 1-1h0l1-2h-2-3 1v-1z" class="b"></path><path d="M467 106h1c1 1 1 1 1 2-1 1-3 1-4 1v1h-2l-7-1v-1h5v-1h1 0c2 0 3 0 4-1h1 0z" class="E"></path><path d="M467 106h1c1 1 1 1 1 2h-8v-1h1 0c2 0 3 0 4-1h1 0z" class="g"></path><path d="M456 106c0-1 1-1 2-2v1c0 1 0 2-2 3v1l7 1 2 2c1 0 3 0 4 1h0c-2 1-6-1-8-1-2-1-4-1-5 0l-2-1-2 1v-1c1 0 1 0 2-1h-2-1c0-1 0-2-1-3h2c1-1 1 0 1-1h3z" class="G"></path><path d="M453 106h3c-2 1-3 3-4 4h-1c0-1 0-2-1-3h2c1-1 1 0 1-1z" class="K"></path><path d="M444 103h6v1h-1 3 2l-1 2h0c0 1 0 0-1 1h-2c-2 1-5 3-8 3l-1 1c-1-2-3-1-4-1v-1c-1 0-3 0-4 1h-1c-1-1-2 0-3 0 0-2 3-2 4-3-2 0-3 0-4 1l-1 1h-1v-2c2-2 5-2 8-3h2 0l-3 1c-1 0-2 0-2 1 1 0 2 0 4-1 2 0 6 0 8-2z" class="S"></path><path d="M439 108h0c-1-1-4 0-6 0 2-1 3-1 5-2 3-1 7-2 11-2l-1 1v1h0c-2 2-6 2-9 2h0z" class="c"></path><path d="M452 104h2l-1 2h0c0 1 0 0-1 1h-2c-2 1-5 3-8 3l-1 1c-1-2-3-1-4-1v-1l2-1h0c3 0 7 0 9-2h0v-1l1-1h0 3z" class="W"></path><path d="M452 104h2l-1 2h-4 0v-1h3v-1z" class="D"></path><path d="M478 101l1 1h2v-1h1c1 2 3 2 4 3 2 1 3 2 4 4v1l1 2v1c-5-1-10-3-15-3-2 0-5-1-7-1 0-1 0-1-1-2h-1l4-2 1-1c-1-1-2-1-3-1l1-1h4l4 1v-1z" class="X"></path><path d="M470 101h4c1 2 1 2 0 3h0l-2-1c-1-1-2-1-3-1l1-1z" class="B"></path><path d="M491 111l-20-4h0c1-1 3-1 4-2v-1c2 1 4 1 6 1s4 1 6 1l3 3 1 2zm-22-3c2 0 5 1 7 1 5 0 10 2 15 3 0 2 1 2 2 3l1 1c1 0 3 1 4 1 2 2 3 4 3 7v1c-7-6-14-8-23-11-1-1-6-2-7-2l-1 1h-1c-1-1-3-1-4-1l-2-2h2v-1c1 0 3 0 4-1z" class="i"></path><path d="M469 108c2 0 5 1 7 1l-8 1h3c0 1 0 1 1 1l-7-1v-1c1 0 3 0 4-1z" class="C"></path><path d="M463 110h2l7 1c6 1 12 3 18 6 3 1 5 2 7 4 1 1 3 3 4 3v1c-7-6-14-8-23-11-1-1-6-2-7-2l-1 1h-1c-1-1-3-1-4-1l-2-2z" class="e"></path><path d="M433 81l1 1h2 2 0c1 0 3 1 4 1 1 1 1 1 2 1s1 0 2 1v2l-1-1h-3v1l6 1 2 1c0 1 1 1 1 2 1 0 5 1 6 1v1h-5-1l-1 1h-1v1h3l-1 1h0c-1 1-2 1-4 1-1 0-1 1-2 1h-1-2c0 1-1 1-1 1h0c-2-1-5 0-7-2h0l-6-2c-1 0-1 0-2-1h5v-1 1l1-1h1v-2-1c-1 0-2 0-2-1l-2-1c1-1 2-2 3-4h0v-2l1-1z" class="b"></path><path d="M442 98c0-1 0-1 2-2 0 1 0 1 1 1l-1 1h-2z" class="S"></path><g class="F"><path d="M431 93v1c3 1 4 0 6 1v1c-1 0-2 0-3 1l-6-2c-1 0-1 0-2-1h5v-1z"></path><path d="M441 91h-2l1-1h7v1h4c1 0 5 1 6 1v1h-5-1l-1 1h-1v1l-3 1c-1 1-1 0-3-1l-1 1c-1-1-2-1-4-1h-1v-1h3v-1s-1 0-1-1h2v-1z"></path></g><path d="M438 95h-1v-1h3l2 1h-1-3z" class="k"></path><path d="M441 91h-2l1-1h7v1c-1 0-3 0-4 1l1 1 1-1h3 0v1c-3 1-4 2-7 0h-1s-1 0-1-1h2v-1z" class="W"></path><path d="M433 81l1 1h2 2 0c1 0 3 1 4 1 1 1 1 1 2 1s1 0 2 1v2l-1-1h-3v1l6 1 2 1c0 1 1 1 1 2h-4v-1h-7l-1 1h2-3c-1 0-1 1-2 1s-2 0-3-1v-1c-1 0-2 0-2-1l-2-1c1-1 2-2 3-4h0v-2l1-1z" class="R"></path><path d="M442 87l6 1-1 1c-2 1-4 1-7 0 1 0 1-1 2-2h0 0z" class="F"></path><path d="M432 84h3c1 1 2 1 3 2h2l1 1h0-4-2c-1 0-1 1-2 2v1c-1 0-2 0-2-1l-2-1c1-1 2-2 3-4h0z" class="I"></path><path d="M432 84h3c1 1 2 1 3 2h2c-1 1-3 0-4 0h-2c-1 0-2-1-2-2h0z" class="U"></path><path d="M433 81l1 1h2 2 0c1 0 3 1 4 1 1 1 1 1 2 1s1 0 2 1v2l-1-1h-3v1h0-1l-1-1h-2c-1-1-2-1-3-2h-3v-2l1-1z" class="R"></path><path d="M432 84v-2h4c0 1 1 1 1 2h2 0-4-3z" class="F"></path><path d="M435 84h4l6 2h-3v1h0-1l-1-1h-2c-1-1-2-1-3-2z" class="B"></path><path d="M428 95l6 2h0c2 2 5 1 7 2h0c-3 2-7 3-10 4h3l1 1c-3 1-6 1-8 3v2h1l1-1c1-1 2-1 4-1-1 1-4 1-4 3 1 0 2-1 3 0-3 1-6 3-8 4 0 1 1 2 2 2h1l1 2c-1 0-2 0-4 1v1c1 1 3 1 4 1h5 0-2-2v2h1c0 2-2 2-2 3h0l-2 1-4 1-1 1h-1l-1 1-1-1c-2 1-5 1-7 2l1 1c-1 0-1 1-2 1l-2 1h-2v-1h-1c-1 0-2-1-3-1h-2 0l-1-1c-1 1-2 2-2 3s0 2-1 2v-1-1h0c1-1 1-1 1-2h1c0-1 0-1 1-2-1 0-1-1-1-1l-1-1-1 1c-1-1-1-1-1-2 0 0 0-1 1-2l1-1c1 0 2-1 3-2l-1-1-2 1c0 1 0 2-1 2-1 1-2 1-3 2-1 0-1 1-2 1h-1c0 1-1 1-1 1h-1c-2 2-4 3-6 5h0c-2-1-3-1-4-1l3-4v-1h0l-2 2v-1-1c1-1 1-1 1-3h-1-1l2-3h0-1c-1 0-2-1-3-1h-1 0c0-1 1-2 1-2 2-2 4-1 6-3h-1 0l-2-1h2c1-1 3-2 4-2 1-1 1-1 2-1l5-1h1c1 0 1-1 2-1v-3-1h1l2-1h3c0 1 0 1 1 2 2 0 3 0 5-1 0-1 1-1 2-1l2-2h0c2 0 3-1 4-2 1 0 2-1 3-1h1s1 0 2-1c0 0 1 0 2-1 0 0 1-1 2-1 0-1 2 0 3 0v-1z" class="G"></path><path d="M411 112l1-1 1-1c0-1 0 0 1-1v-1l1-1c1-2 1-3 3-4 1-1 2 0 3 0h0c0 1-1 1-2 1-1 1-2 3-3 5l-1 1c-1 0-1 1-2 2h-1-1zm0-10h0c2 0 3-1 4-2 1 0 2-1 3-1h1c-1 1-2 2-3 2l-2 2h0c-2 2-3 4-5 5-1-1-2-1-2-2l2-2h0l2-2z" class="d"></path><path d="M403 113c2-1 5-3 7-5 0 1-1 2-2 3-3 3-7 5-11 6l1-1 1-1h-2 0c-1-1-1-1-2-1 1-1 3-2 4-2h2 0c0 1 1 1 2 1z" class="C"></path><path d="M399 112h2 0c0 1 1 1 2 1-2 1-4 1-6 2-1-1-1-1-2-1 1-1 3-2 4-2z" class="M"></path><path d="M428 95l6 2h0c-1 1-1 1-2 1h-1-4c0 1-1 2-2 2v-1h-3-1c-1 0-3 2-4 2-1 1-1 2-2 2h-1l2-2c1 0 2-1 3-2 0 0 1 0 2-1 0 0 1 0 2-1 0 0 1-1 2-1 0-1 2 0 3 0v-1z" class="V"></path><path d="M398 104h3c0 1 0 1 1 2 2 0 3 0 5-1 0-1 1-1 2-1h0l-2 2-1 1c-1 1-2 3-3 3l-2 2h-2l1-2h0-2l1-1c-1-1-2-2-2-3-1 0-1 0-1-1h0l2-1z" class="O"></path><path d="M400 110h1 2l-2 2h-2l1-2z" class="F"></path><path d="M396 105l2-1v1c1 0 2 1 2 1l1 1h2v1c0 1-1 1-2 2h-1 0-2l1-1c-1-1-2-2-2-3-1 0-1 0-1-1h0z" class="I"></path><path d="M434 97c2 2 5 1 7 2h0c-3 2-7 3-10 4h3l1 1c-3 1-6 1-8 3l-8 2c-1 0-2 1-2 1h-2l1-1c1-2 2-4 3-5 1 0 2 0 2-1h0 0l-1-1c2-1 3-2 5-2 1 0 2-1 2-2h4 1c1 0 1 0 2-1z" class="O"></path><path d="M429 100c3-1 6-2 9-1v1h-5c-1 0-3 1-4 1l-1-1h1z" class="V"></path><path d="M425 100c1 0 2-1 2-2 1 2 1 1 2 2h-1l1 1-1 1-1-1c-1 0-1 0-2 1 1 1 0 1 1 1s1 1 2 1l-3 1v-1-1c0-1 0-1-1-1s-2 1-3 1l-1-1c2-1 3-2 5-2z" class="B"></path><path d="M421 103c1 0 2-1 3-1s1 0 1 1v1 1l3-1 3-1h3l1 1c-3 1-6 1-8 3l-8 2c-1 0-2 1-2 1h-2l1-1c1-2 2-4 3-5 1 0 2 0 2-1h0 0z" class="W"></path><path d="M421 103c1 0 2-1 3-1s1 0 1 1v1 1h-2c-2 1-4 2-7 4 1-2 2-4 3-5 1 0 2 0 2-1h0 0z" class="V"></path><path d="M423 105v-1h-1l1-1h2v1 1h-2z" class="e"></path><path d="M392 110h1c1 0 1-1 2-1v-3-1h1 0c0 1 0 1 1 1 0 1 1 2 2 3l-1 1h2 0l-1 2c-1 0-3 1-4 2 1 0 1 0 2 1h0 2l-1 1-1 1-17 4h-1c-1 0-2-1-3-1h-1 0c0-1 1-2 1-2 2-2 4-1 6-3h-1 0l-2-1h2c1-1 3-2 4-2 1-1 1-1 2-1l5-1z" class="P"></path><path d="M385 112c1-1 1-1 2-1 0 1 1 1 2 1l-1 1c-1 0-2 0-2 1h-2l1-2z" class="C"></path><path d="M392 113h-1c0-1 0 0 1-1 1 0 2 0 3-1s2-1 3-1h2 0l-1 2c-1 0-3 1-4 2 1 0 1 0 2 1h0l-7 1c1-2 3-2 5-3h-3z" class="R"></path><path d="M381 114c1-1 3-2 4-2l-1 2h2c2 0 4 0 6-1h3c-2 1-4 1-5 3h0c-3 1-5 1-7 1h-1l1-2h0-2l-2-1h2z" class="Y"></path><path d="M381 114c1-1 3-2 4-2l-1 2h2c2 0 4 0 6-1h3c-2 1-4 1-5 3h0l-1-1h0v-1h-8 0z" class="a"></path><defs><linearGradient id="BV" x1="392.699" y1="110.219" x2="380.705" y2="124.63" xlink:href="#B"><stop offset="0" stop-color="#cecdce"></stop><stop offset="1" stop-color="#f2f1f3"></stop></linearGradient></defs><path fill="url(#BV)" d="M381 115h2 0l-1 2h1c2 0 4 0 7-1h0l7-1h2l-1 1-1 1-17 4h-1c-1 0-2-1-3-1h-1 0c0-1 1-2 1-2 2-2 4-1 6-3h-1 0z"></path><path d="M408 111l3 1h1c-3 3-7 5-11 6 0 1-1 1-1 1s-1 0-1 1h0l-7 4c-1 1-3 2-4 4-2 2-4 3-6 5h0c-2-1-3-1-4-1l3-4v-1h0l-2 2v-1-1c1-1 1-1 1-3h-1-1l2-3h0l17-4c4-1 8-3 11-6z" class="j"></path><path d="M399 120h0l-7 4-1-2h-2c3-1 7-2 10-2z" class="C"></path><path d="M386 124l3-2h2l1 2c-1 1-3 2-4 4-2 2-4 3-6 5h0c-2-1-3-1-4-1l3-4v-1l5-3z" class="Y"></path><path d="M386 124h1 2v1c0 1-1 1-2 1-1 2-4 4-6 4v-2-1l5-3z" class="f"></path><path d="M415 110h2s1-1 2-1l8-2v2h1l1-1c1-1 2-1 4-1-1 1-4 1-4 3 1 0 2-1 3 0-3 1-6 3-8 4 0 1 1 2 2 2h1l1 2c-1 0-2 0-4 1v1c1 1 3 1 4 1h5 0-2-2v2h1c0 2-2 2-2 3h0l-2 1-4 1-1 1h-1l-1 1-1-1c-2 1-5 1-7 2l1 1c-1 0-1 1-2 1l-2 1h-2v-1h-1c-1 0-2-1-3-1h-2 0l-1-1c-1 1-2 2-2 3s0 2-1 2v-1-1h0c1-1 1-1 1-2h1c0-1 0-1 1-2-1 0-1-1-1-1l-1-1-1 1c-1-1-1-1-1-2 0 0 0-1 1-2l1-1c1 0 2-1 3-2l-1-1-2 1c0 1 0 2-1 2-1 1-2 1-3 2-1 0-1 1-2 1h-1c0 1-1 1-1 1h-1c1-2 3-3 4-4l7-4h0c0-1 1-1 1-1s1 0 1-1c4-1 8-3 11-6h1c1-1 1-2 2-2z" class="g"></path><path d="M428 121h5 0-2-2v2h-3c-1-1-1 0-2 0l-1-1h0c1-1 3-1 5-1z" class="K"></path><path d="M415 125l-1-1v-1h0c2-1 4-2 6-2l1 1-2 2-4 1z" class="G"></path><path d="M405 119l1 1c-1 3-2 4-4 5-1 0-1 1-2 1v-3l1-1c1-1 3-2 4-3z" class="P"></path><path d="M401 130l-1-1h-1v-2h-2c1-2 1-3 3-4v3c1 0 1-1 2-1 1 1 3 0 4 1h6-1c-3 0-5 3-9 2v1h0 1c-1 1-2 1-2 1z" class="S"></path><path d="M401 130s1 0 2-1h-1 0v-1c4 1 6-2 9-2-1 1-2 2-2 3v1h0c-1 1-2 1-3 2h1l-1 1h-1c-1 0-2-1-3-1h-2 0l-1-1 2-1z" class="R"></path><path d="M421 112c1-1 2-1 3-1h0l-1 1h-1s-1 0-1 1h-1c-3 3-7 5-10 7-1 1-2 1-3 0l-1-1v-1s1-1 2-1c2-1 3-2 5-3h1 2c2-1 3-1 5-2z" class="h"></path><path d="M415 110h2s1-1 2-1l8-2v2h1l1-1c1-1 2-1 4-1-1 1-4 1-4 3 1 0 2-1 3 0-3 1-6 3-8 4 0 1 1 2 2 2h1l1 2c-1 0-2 0-4 1h0l1-1c-2-1-4 0-7 1v-1c1-1 1-2 2-3 1 0 1 0 1-1 1 0 1-1 1-1l7-4c-3 1-6 1-8 2-1 1-1 1-2 1-1-1-1-1-2-1h-1c-1 0-1 1-3 1 1-1 1-2 2-2z" class="e"></path><path d="M413 112c2 0 2-1 3-1h1c1 0 1 0 2 1 1 0 1 0 2-1v1c-2 1-3 1-5 2h-2-1c-2 1-3 2-5 3-1 0-2 1-2 1v1h-1c-1 1-3 2-4 3l-1 1c-2 1-2 2-3 4h2v2h1l1 1-2 1c-1 1-2 2-2 3s0 2-1 2v-1-1h0c1-1 1-1 1-2h1c0-1 0-1 1-2-1 0-1-1-1-1l-1-1-1 1c-1-1-1-1-1-2 0 0 0-1 1-2l1-1c1 0 2-1 3-2l-1-1-2 1c0 1 0 2-1 2-1 1-2 1-3 2-1 0-1 1-2 1h-1c0 1-1 1-1 1h-1c1-2 3-3 4-4l7-4h0c0-1 1-1 1-1s1 0 1-1c4-1 8-3 11-6h1z" class="G"></path><path d="M424 123c1 0 1-1 2 0h3 1c0 2-2 2-2 3h0l-2 1-4 1-1 1h-1l-1 1-1-1c-2 1-5 1-7 2l1 1c-1 0-1 1-2 1l-2 1h-2v-1l1-1h-1c1-1 2-1 3-2h0v-1c0-1 1-2 2-3h1l3-1 4-1c1 0 4 0 5-1h0z" class="P"></path><path d="M420 127c1-1 3-1 4-1 1 1 1 1 2 1l-4 1h-1c0-1-1-1-1-1h0z" class="k"></path><path d="M415 127l2-1c1 0 2 0 3 1h0s1 0 1 1h1l-1 1h-1l-1 1-1-1-1-1-2 1v-2z" class="R"></path><path d="M420 127s1 0 1 1h1l-1 1h-1-1c0-1 0 0-1-1l2-1z" class="F"></path><path d="M412 128c1-1 2-1 3-1v2l2-1 1 1c-2 1-5 1-7 2l1 1c-1 0-1 1-2 1l-2 1h-2v-1l1-1h-1c1-1 2-1 3-2h0l3-2z" class="N"></path><path d="M412 128c1-1 2-1 3-1v2h0c-1 0-2 0-3-1z" class="H"></path><path d="M411 131l1 1c-1 0-1 1-2 1l-2 1h-2v-1l1-1 4-1z" class="L"></path><path d="M163 268c1-4 3-6 6-9h1v1 1c0 5 1 10 3 15l2 3 1-1v2l3 6 2-1c0 1 1 2 1 2-1 1-1 2-1 2l4 7c1 0 2 0 3 1l1-1 6 6c2 2 4 4 6 5l5 5 1-1h1c1 1 1 1 3 0l2 2h0c1 0 2 0 4 1 1 0 3 0 5 1 1 0 1 1 1 1h3c1 0 1 0 1 1h2c1 1 3 1 4 2l7 4 2 4c3 2 7 4 10 6 3 4 6 7 10 10l2 3 1-1c2 1 3 4 4 6s1 4 2 7l1 1v4c1 10-1 16-5 25h1c0-1 0-1 1-2v-1l1 1s1 0 0 1v2c-7 12-15 25-10 40v2l-3-3c-1-1-2-2-2-3-2-3-3-7-3-10v-6c-5 5-11 9-16 13-7 5-15 11-18 19-2 3-3 8-3 11 0 4 1 11 4 14 1 1 2 2 3 2 2 0 3-1 4-3 1-3 1-7 0-11h0c2 3 4 6 3 10 0 4-3 8-5 10-2 1-4 2-7 2-4-1-6-3-9-7-5-8-6-21-4-31 1-3 2-5 4-8h0c1-1 4-3 5-3v1l3-3-1-1c0 1-1 1-2 1l-1-1 2-2s0-1-1-1c0-1 0-1-1-1-1 1-2 2-4 3 1-1 1-2 1-3l3-3h-1c-6 5-13 7-19 12-1 0-3 1-4 2 0 1-1 2-1 2h-1c-1 0-2 2-3 2l-1-1 6-5 5-4c3-1 8-4 10-6l-1-1v-1h0c-1-1-1-2-1-3l1-2c1-1 1-1 1-2 1-1 3-1 4-2l4-4-2 1h0v-1h-1c3-3 5-5 7-8v-1c-1 0-3 1-4 2 0-1 1-2 2-3h-1c1-1 1-2 2-2 0-1 1-1 1-2-2 0-2 1-4 2h0-1l1-2v-1l-3 1c-1 1-2 3-3 4v1c-2 0-2 0-3 1l-2 1c-2 2-5 4-7 5-1 1-4 2-4 3l-2 1h0l-6 4-3 1c-1 1-1 1-2 1h0c-1 1-1 1-2 1v-1-1c1 0 3-1 3-2v-1h-3-1l2-1 4-3h1l4-2-1-2h-1c-1 1-2 1-3 1l1-2-1 1-1 1 1-3h-1l-2 1-1-1c1-1 1-1 0-2-1 1-2 1-3 2l4-3h-2c-1 1-1 1-2 1h0-1c0-1 0-1 1-1l1-2c7-5 14-12 19-20 6-10 9-20 6-32l-3-6c-1-2-2-3-2-4-1-1-1-1-2-1 1 1 1 1 1 2-1 1 1 2 1 4h0l-5-5c-3-4-9-8-15-11h0c-1 0-2-1-2-1l-17-7-1-1h-1s0-1-1-1c-2-1-4-1-5-2s-1-2-1-3v-1c-1 0-2-1-3-2h2l1-1-2-1c1-3 2-6 4-9 1 3 3 5 5 7 0-6 0-11 1-17v-1c1 0 1-1 2-1l1-3c1-2 2-3 3-4s1-2 3-2h0l1-2z" class="h"></path><path d="M182 303c1 1 2 2 2 3v1l-2-1h0v-3z" class="F"></path><path d="M211 377c0-1 1-2 2-3 0 1 1 1 0 2v1 2h-1v-1l-1-1z" class="M"></path><path d="M200 376l2-4-1 2h1c1 1 0 2-1 2v3h0c-1-1-1-1-1-2h-1l1-1z" class="Q"></path><path d="M157 308l8 3c0 1 1 2 1 3l-5-2c0-1-1-1-1-2-1 0-2-1-3-2z" class="H"></path><path d="M180 317c1 1 3 2 4 3l-1 1v1l-1-1-4-2h1c0-1 0-1 1-2z" class="P"></path><path d="M213 428h1l-1 3h0v1c-2 2-4 5-5 8 0-1-1-1-1-2 0-3 4-8 6-10z" class="S"></path><path d="M191 382c0-1 1-1 1-2 1-1 1-1 1-2 2-2 3-4 6-6-1 3-2 5-4 7v1l-3 3-1-1z" class="Q"></path><path d="M263 377h1l-3 8c0 1-2 5-3 5v-1l1-1 1-1c0-1 0-2 1-3s0-1 0-1v1l-2 4h-1v1c0 1-1 2-2 3v1l-2 2v1l-2 2h-1l2-2h0c0-1 1-1 1-1l1-2c1-2 2-3 2-5 1-1 2-3 3-4v-2c2-1 2-3 3-5z" class="j"></path><path d="M173 290l3 3c2 3 3 7 6 10v3l-3-3c-1-2-2-4-4-6l-1-4-1-3z" class="S"></path><path d="M247 393l2-3c1-1 1-2 2-2v1l-1 1-1 2-1 1v1h0c-1 0-2 2-2 3h0c1 1 1 1 1 2-1 0-1 1-2 0h0l-1 1-1 1c0 1-1 1-1 2v1h0v1c-1 1-1 0-1 1l-1 1c0 2-3 2-3 3h-1l4-5c3-3 5-8 7-12z" class="M"></path><path d="M165 311c3 1 6 2 10 4 1 0 3 1 5 2-1 1-1 1-1 2h-1l-4-2c-1-1-6-3-8-3 0-1-1-2-1-3z" class="R"></path><path d="M145 301c4 3 8 5 12 7 1 1 2 2 3 2 0 1 1 1 1 2-5-2-11-5-16-8-1 0-2-1-3-2h2l1-1z" class="D"></path><path d="M168 273c1 4 2 7 4 11 1 3 2 6 4 9l-3-3 1 3c-2-2-4-5-5-7l-2-7v-2l1 2v-6z" class="B"></path><path d="M167 277l1 2c1 2 2 5 3 7 0 1 1 3 2 4l1 3c-2-2-4-5-5-7l-2-7v-2z" class="b"></path><defs><linearGradient id="BW" x1="248.921" y1="379.664" x2="252.013" y2="390.069" xlink:href="#B"><stop offset="0" stop-color="#222021"></stop><stop offset="1" stop-color="#454746"></stop></linearGradient></defs><path fill="url(#BW)" d="M251 381v-2l1-2v-1s1-1 1-2v-1l1-1v-1h0v3h0c0 1-1 2-1 3v1h0c1 0 1 0 1 1h1 0 1c-1 3-4 6-5 9-1 0-1 1-2 2l-2 3h-1l1-2-1-1h1c-1-1 4-7 4-9z"></path><defs><linearGradient id="BX" x1="192.275" y1="392.426" x2="177.431" y2="392.949" xlink:href="#B"><stop offset="0" stop-color="#202121"></stop><stop offset="1" stop-color="#4a4748"></stop></linearGradient></defs><path fill="url(#BX)" d="M195 380c0 2-2 2-3 4h1c1 0 2-2 2-3l1-1c1 0 1-1 1-2 1-1 2-2 3-2l-1 1-11 15-8 7-1 1-1 1 1-3c1-2 3-4 5-5l3-3c0-2 4-5 5-7l3-3z"></path><path d="M221 376v-2h0c0-1 1-2 1-2v-1-1h0c1-2 1-4 3-6h1c1 1 2 1 4 0h1v4h0v1 1c-1 1-1 2-2 2 1-1 1-3 1-4-1 0-2 1-2 1-1 1-3 4-4 4-1 1-1 1-2 1h0 0c0 1 0 1-1 2z" class="G"></path><path d="M167 289c1 0 2 1 3 2-1-2-1-3-2-5h1c1 2 3 5 5 7l1 4c2 2 3 4 4 6h0c-1 0-2-1-3-1 0-1-1 0-1-1-1 0-2-1-3-1 0 0 1 1 0 2h0l-2-2 1-1c0-2-1-3-2-5h0c-1-1-1-3-2-5h0z" class="D"></path><path d="M169 294h1c1 1 2 2 3 4h-1v2s1 1 0 2h0l-2-2 1-1c0-2-1-3-2-5zm-2-5c1 0 2 1 3 2-1-2-1-3-2-5h1c1 2 3 5 5 7l1 4c-3-2-5-5-8-8h0z" class="P"></path><path d="M157 296l1 1c1 1 2 2 3 2s1 0 1-1h1c1 2 3 4 4 5h-2l-1 1 3 2-1 1 1 1-1 1c-2-2-4-2-6-5-2-1-3-2-5-4v-1c0-1 1-1 2-3z" class="M"></path><path d="M155 300h2c1 0 2 1 2 2 1 1 1 2 1 2-2-1-3-2-5-4z" class="F"></path><path d="M158 297c1 1 2 2 3 2s1 0 1-1h1c1 2 3 4 4 5h-2l-1 1c-2-2-4-4-6-7z" class="T"></path><path d="M221 376c1-1 1-1 1-2h0 0c1 0 1 0 2-1 1 0 3-3 4-4 0 0 1-1 2-1 0 1 0 3-1 4v2h2s1-1 2-1h0c0 3-2 5-3 8h0-1-3c1-3 3-7 2-10-2 3-4 5-5 8l-2-1c-1 0-1 2-2 3h0c0 1-1 1-1 2h-1v-1l3-9v2h0v2l-2 3v1c1-1 2-3 3-5h0z" class="V"></path><path d="M262 398c0 1-2 4-3 6s-2 5-2 8l-1 1v8c1 2 2 5 2 6l-1 1v-2c-1-1-2-3-2-5v-1c-1-2-1-5 0-7 0-2 1-4 0-5v-3c2-3 3-6 5-10 2-3 2-5 6-7-1 2-4 7-4 10z" class="M"></path><defs><linearGradient id="BY" x1="190.914" y1="388.416" x2="174.576" y2="392.552" xlink:href="#B"><stop offset="0" stop-color="#2b2b2c"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#BY)" d="M176 395c5-4 11-9 13-15l1-1v1 1c-1 1-1 2-2 3h0l1-1c1 0 1 0 2-1l1 1c-1 2-5 5-5 7l-3 3c-2 1-4 3-5 5h-1l-2 1-1-1c1-1 1-1 0-2-1 1-2 1-3 2l4-3z"></path><defs><linearGradient id="BZ" x1="234.845" y1="417.261" x2="229.272" y2="414.92" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#535252"></stop></linearGradient></defs><path fill="url(#BZ)" d="M226 420c1-1 3-3 4-5h0c2-1 4-3 5-5h1 1l1 1h-1l1 1h-1v2h0c-1 1-1 1-1 2h-1v2-1h1c1-1 1-2 2-2 2-1 4-3 5-4h0c-1 2-2 3-4 4-3 3-7 5-10 8-2 1-3 3-6 3 1 0 2-1 3-2-1-1-1-2 0-3v-1z"></path><path d="M226 420l1 1h1c-1 1-2 2-2 3-1-1-1-2 0-3v-1z" class="I"></path><path d="M235 391c1-1 0-1 1-2 0 0 0-1 1-1l1-1v-1h0v-1c-1-1-1-1-1-2h1v1 1c1-1 1-1 1-2h1c1 0 1 1 2 1h0 0 1 0l-1 1c0 1 0 2-1 2v1h1c1 0 1-1 2-2 3-2 4-6 5-9h0 0v2l-2 3c0 2-1 3-2 5h0s0 1 1 1c0 2-1 2-2 3v1-2c-1 1-1 2-1 2-1 0-2 1-3 0v1h-1c-1 0-1-1-1-1-1 1-1 1-1 2-1 1-2 2-3 4v-2h-2c0 1-1 3-3 3h0l1-2c2 0 3-4 5-5h-2l1-1h1z" class="g"></path><path d="M235 392c1 0 1-1 1-2h0c1-1 1-2 2-2h1c0 2-1 3-1 4-1 1-1 1-1 2-1 1-2 2-3 4v-2h-2c0 1-1 3-3 3h0l1-2c2 0 3-4 5-5z" class="B"></path><path d="M232 396l4-4h0l1 2c-1 1-2 2-3 4v-2h-2z" class="F"></path><path d="M225 420l1 1c-1 1-1 2 0 3-1 1-2 2-3 2l-2 3c-3 4-6 8-9 13v-2c1-2 2-4 3-7h0-2v-1-1h0l1-3h-1 0l3-3h2l3-3h2c1 0 2-1 2-2z" class="D"></path><path d="M221 422h2v1c-2 2-4 4-7 5l2-3 3-3z" class="F"></path><path d="M216 425h2l-2 3-1 2-2 1h0l1-3h-1 0l3-3z" class="X"></path><path d="M215 430c1-1 2-1 3-2 0 2-1 4-3 5h0-2v-1-1l2-1z" class="E"></path><path d="M225 420l1 1c-1 1-1 2 0 3-1 1-2 2-3 2l-2 3v-3c1-2 2-2 2-3v-1c1 0 2-1 2-2z" class="k"></path><path d="M154 312l-1-1h-1s0-1-1-1c-2-1-4-1-5-2s-1-2-1-3v-1c5 3 11 6 16 8l5 2c2 0 7 2 8 3l1 1 1 1h-1c-1-1-3-1-4 0h0l-17-7z" class="C"></path><defs><linearGradient id="Ba" x1="202.364" y1="379.738" x2="190.825" y2="390.979" xlink:href="#B"><stop offset="0" stop-color="#242424"></stop><stop offset="1" stop-color="#444343"></stop></linearGradient></defs><path fill="url(#Ba)" d="M199 377h1c0 1 0 1 1 2-1 1-1 1-1 2h-1l-1 1 1 1 1-1c0-1 1-1 1-2s0-1 1-2h1c0 1 0 1 1 2l-1 1h0c0 1-1 2-2 3v1c0 2-3 6-5 7h0s-1 0-1-1l-1 1-1-1c-2 0-3 0-5 1l11-15z"></path><path d="M163 268c1-4 3-6 6-9h1v1 1h-2c0 2-1 4-1 5l1 7v6l-1-2v2l2 7h-1c1 2 1 3 2 5-1-1-2-2-3-2v-2c-2-4-3-8-4-12v-7z" class="R"></path><path d="M167 279l2 7h-1c1 2 1 3 2 5-1-1-2-2-3-2v-2l1-1c-1-1-1-2-1-3-1-1-1-2-1-3v-1h1z" class="H"></path><path d="M167 277c-2-4-3-7-2-11v-1-1h1v2h1l1 7v6l-1-2z" class="F"></path><defs><linearGradient id="Bb" x1="249.28" y1="395.029" x2="226.097" y2="410.636" xlink:href="#B"><stop offset="0" stop-color="#434344"></stop><stop offset="1" stop-color="#616060"></stop></linearGradient></defs><path fill="url(#Bb)" d="M246 390l1 1-1 2h1c-2 4-4 9-7 12l-4 5h-1c-1 2-3 4-5 5h0c-1 2-3 4-4 5v1l-1-1c1-2 3-3 4-5h0v-1c1-1 2-3 3-5v-1h0c1-1 2-3 3-4 2-3 4-6 6-8h1c0-1 0-2 1-2l3-4z"></path><path d="M246 393h1c-2 4-4 9-7 12-1 0-1-1-1-1-1-2 4-6 5-8 0-1 1-2 2-3z" class="U"></path><path d="M264 346l1-1c2 1 3 4 4 6s1 4 2 7l1 1v4c1 10-1 16-5 25l-5 10c0-3 3-8 4-10l3-9c2-10 0-21-5-29l1-2-1-2z" class="C"></path><path d="M264 346l1-1c2 1 3 4 4 6s1 4 2 7l1 1v4c-1-2-1-3-2-5-1-3-3-7-5-10l-1-2z" class="E"></path><defs><linearGradient id="Bc" x1="227.478" y1="382.707" x2="215.281" y2="382.512" xlink:href="#B"><stop offset="0" stop-color="#313031"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#Bc)" d="M223 379c1-3 3-5 5-8 1 3-1 7-2 10 0 0-1 1-1 2l-1 2c-1 1-2 3-3 4s-2 2-2 3l-1 1-1 1c0 1-1 1-2 2h0v-1c-1 0-3 1-4 2 0-1 1-2 2-3h-1c1-1 1-2 2-2 0-1 1-1 1-2-2 0-2 1-4 2h0-1l1-2 2-2c1-2 2-4 4-6v1h1c0-1 1-1 1-2h0c1-1 1-3 2-3l2 1z"></path><path d="M222 382v1c0 1-1 2-2 3v-1h-1l3-3z" class="U"></path><path d="M217 382v1h1c0-1 1-1 1-2h0c1-1 1-3 2-3l2 1c0 1-1 2-1 3l-3 3-2 2c-1 1-1 1-1 2l-1 1c-2 0-2 1-4 2h0-1l1-2 2-2c1-2 2-4 4-6z" class="O"></path><path d="M174 317l4 2 4 2 1 1v-1l1-1c6 3 12 9 14 16v5l-3-6c-1-2-2-3-2-4-1-1-1-1-2-1 1 1 1 1 1 2-1 1 1 2 1 4h0l-5-5c-3-4-9-8-15-11h0c-1 0-2-1-2-1h0c1-1 3-1 4 0h1l-1-1-1-1z" class="L"></path><path d="M174 317l4 2 4 2v1c-1 0-4-1-5-2h-1-3c-1 0-2-1-2-1h0c1-1 3-1 4 0h1l-1-1-1-1z" class="J"></path><defs><linearGradient id="Bd" x1="181.563" y1="296.764" x2="188.221" y2="293.808" xlink:href="#B"><stop offset="0" stop-color="#706f70"></stop><stop offset="1" stop-color="#b1afaf"></stop></linearGradient></defs><path fill="url(#Bd)" d="M173 276l2 3 1-1v2l3 6 2-1c0 1 1 2 1 2-1 1-1 2-1 2l4 7 2 3c1 3 4 5 6 8l10 9c-1 0-1 0-3-1-2-2-3-3-5-4l4 5h1c1 0 3 2 4 3h0l1 2h-2c-6-5-10-11-15-17-3-3-6-6-8-10-2-3-4-7-6-11v-2c0-1 0-3-1-5z"></path><path d="M179 286l2-1c0 1 1 2 1 2-1 1-1 2-1 2-1-1-1-2-2-3z" class="V"></path><defs><linearGradient id="Be" x1="212.474" y1="372.91" x2="217.244" y2="386.883" xlink:href="#B"><stop offset="0" stop-color="#2f2f2f"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#Be)" d="M217 369v3c1 0 1-1 2-1l1-1v1-1l1-1h0v-1-2c-1 0-1 1-2 1 0-1 0-1 1-2h1v-1c1 2 0 6-1 9l-3 9c-2 2-3 4-4 6l-2 2v-1l-3 1c-1 1-2 3-3 4v1c-2 0-2 0-3 1l-2 1c2-2 4-5 6-8l3-6c1-2 1-4 2-6l1 1v1h1l2-3c0-1 0-2 1-3v-1c1-1 1-3 0-4h0l1-1c1 0 1 0 2 1 0 0-1 0-2 1z"></path><defs><linearGradient id="Bf" x1="213.69" y1="380.511" x2="201.007" y2="392.19" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#Bf)" d="M213 379l2-3c0-1 0-2 1-3v-1c1-1 1-3 0-4h0l1-1c1 0 1 0 2 1 0 0-1 0-2 1h0v2c0 1 0 1-1 2v1c0 1-1 2-1 3-2 3-3 6-4 9-1 1-2 3-3 4s-2 3-3 4v1c-2 0-2 0-3 1l-2 1c2-2 4-5 6-8l3-6c1-2 1-4 2-6l1 1v1h1z"></path><defs><linearGradient id="Bg" x1="206.726" y1="435.177" x2="217.205" y2="467.54" xlink:href="#B"><stop offset="0" stop-color="#b5b3b5"></stop><stop offset="1" stop-color="#f3f1f2"></stop></linearGradient></defs><path fill="url(#Bg)" d="M208 440c1-3 3-6 5-8v1h2 0c-1 3-2 5-3 7v2c-1 1-1 3-2 4-2 10-1 21 4 29-2-3-4-5-5-8-5-9-5-18-1-27z"></path><path d="M168 300h2l2 2h0c1-1 0-2 0-2 1 0 2 1 3 1 0 1 1 0 1 1 1 0 2 1 3 1h0l3 3h0l2 1v-1c3 1 5 4 7 6h-1l-2-2c0 1 0 1-1 2h3c-1 1 0 1-1 1l-4-2c0-1-1-1-1-1h-1 0l-1-1v1c1 0 1 0 1 1 1 1 3 2 3 3h0c-2-1-4-2-5-3-2-1-4-1-6-2v-1h-1l1 1c2 1 3 3 5 4h0c-2 0-7-3-9-4l-4-3-3-2 1-1h2l2 1c0-1-1-2-1-3h0v-1h0z" class="M"></path><path d="M168 300h2l2 2h0l3 4h-2l-4-5-1-1h0z" class="S"></path><path d="M168 300h2l2 2-3-1-1-1h0z" class="b"></path><path d="M176 306h0v-1h0 1c1 0 2 1 3 1l1 1 1-1 2 1c1 1 1 2 1 3h0c-3-1-4-3-7-3 1 1 1 1 0 1s-2-1-2-2z" class="U"></path><path d="M167 303l2 1c1 1 2 3 4 4v1h-2l-4-3-3-2 1-1h2z" class="F"></path><path d="M172 300c1 0 2 1 3 1 0 1 1 0 1 1 1 0 2 1 3 1h0l3 3h0l-1 1-1-1c-1 0-2-1-3-1h-1 0v1h0-1l-3-4c1-1 0-2 0-2z" class="I"></path><path d="M234 326l-2-2-1-2h-1c2 0 4 1 6 1 2 1 5 3 6 4 3 2 7 4 10 6 3 4 6 7 10 10l2 3 1 2-1 2c-3-4-7-7-11-9-2 0-4 0-6-1h-4c-1-1-5-8-5-10-1-1-3-2-4-4z" class="i"></path><path d="M163 268v7c1 4 2 8 4 12v2h0c1 2 1 4 2 5h0c1 2 2 3 2 5l-1 1h-2 0v1h0c0 1 1 2 1 3l-2-1c-1-1-3-3-4-5h-1c0 1 0 1-1 1s-2-1-3-2l-1-1c-1-2-2-4-2-7-1-2-1-7-2-8v-1c1 0 1-1 2-1l1-3c1-2 2-3 3-4s1-2 3-2h0l1-2z" class="C"></path><path d="M153 281v-1c1 0 1-1 2-1l1-3v3 9 1h-1c-1-2-1-7-2-8z" class="f"></path><path d="M162 298h0 0c-2-2-3-4-3-6l-3-8c0-2 0-6 2-8h0c1 1 2 1 2 1h1l1 6v3c0-2-1-3-2-4v3h-1v1c1 1 0 2 1 3v2c0 1 1 2 1 3 1 1 2 2 2 4h-1z" class="K"></path><path d="M160 291c-1-1-1-1-1-2-1-2-1-5-1-7 0-1-1-3 0-3v-1h1l1 4v3h-1v1c1 1 0 2 1 3v2z" class="N"></path><path d="M160 282c1 1 2 2 2 4 1 2 2 3 3 5l2 5 3 4h-2 0v1h0c0 1 1 2 1 3l-2-1c-1-1-3-3-4-5 0-2-1-3-2-4 0-1-1-2-1-3v-2c-1-1 0-2-1-3v-1h1v-3z" class="c"></path><path d="M160 291v-2c-1-1 0-2-1-3v-1h1c1 2 1 4 3 6v3l-1-1-1 1c0-1-1-2-1-3z" class="Z"></path><path d="M163 291l2 3c0 1 1 2 2 2l3 4h-2c-2-2-4-4-5-6v-3z" class="F"></path><path d="M161 294l1-1 1 1c1 2 3 4 5 6h0v1h0c0 1 1 2 1 3l-2-1c-1-1-3-3-4-5 0-2-1-3-2-4z" class="O"></path><path d="M163 268v7c1 4 2 8 4 12v2h0c1 2 1 4 2 5h0c1 2 2 3 2 5l-1 1-3-4-2-5c-1-2-2-3-3-5v-3l-1-6c-1-1 0-3-1-5h-1c1-1 1-2 3-2h0l1-2z" class="J"></path><path d="M165 291v1c2 1 3 3 4 4v-2h0c1 2 2 3 2 5l-1 1-3-4-2-5z" class="L"></path><path d="M163 268v7h0c0 2 1 8 1 9l-2-1-1-6c-1-1 0-3-1-5h-1c1-1 1-2 3-2h0l1-2z" class="Y"></path><defs><linearGradient id="Bh" x1="197.686" y1="395.21" x2="182.089" y2="401.223" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#504e4f"></stop></linearGradient></defs><path fill="url(#Bh)" d="M208 380c0-1 2-2 2-3 2-3 3-5 5-7v-1l1 1-2 2c0 1-1 1-1 2-1 1-2 2-2 3-1 2-1 4-2 6l-3 6c-2 3-4 6-6 8s-5 4-7 5c-1 1-4 2-4 3l-2 1h0l-6 4-3 1c-1 1-1 1-2 1h0c-1 1-1 1-2 1v-1-1c1 0 3-1 3-2v-1h-3-1l2-1 4-3h1l4-2-1-2h-1c-1 1-2 1-3 1l1-2 8-7c2-1 3-1 5-1l1 1 1-1c0 1 1 1 1 1h0c2-1 5-5 5-7l2-1 2-3v-1c1 0 1-1 1-1h1c0-1 0-1-1-2v1l-1-1 2-2h0l-1 1 1 1c0-1 0-1 1-2v1c0 1 0 1-1 2 1 1-1 1-1 3 0 0-1 1-1 2 0 0-1 0-1 1l-1 1v1h-1c-1 1-2 3-3 5-1 0-2 1-2 2h0c2-1 2-3 3-4 2-1 3-3 4-5 2-1 2-2 4-4z"></path><path d="M194 397v1c-1 2-3 3-4 5-1 0-3 2-3 2v1h0 0l-6 4-3 1c-1 1-1 1-2 1h0c-1 1-1 1-2 1v-1-1c1 0 3-1 3-2v-1h-3-1l2-1 4-3h1v1l1-1v1c2 0 3-1 4-1 1-1 3-1 4-2 2-1 3-3 5-5z" class="N"></path><path d="M180 404v1l1-1v1c2 0 3-1 4-1l-2 2h0v-1c-2 1-6 3-8 2h0l4-3h1z" class="I"></path><path d="M177 409l5-1c-1 1-2 2-3 2l-1 1c-1 1-1 1-2 1h0c-1 1-1 1-2 1v-1-1c1 0 3-1 3-2z" class="W"></path><defs><linearGradient id="Bi" x1="191.083" y1="389.979" x2="185.762" y2="400.286" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#656364"></stop></linearGradient></defs><path fill="url(#Bi)" d="M188 392c2-1 3-1 5-1l1 1 1-1c0 1 1 1 1 1-4 4-7 7-11 9l-1 1-1-2h-1c-1 1-2 1-3 1l1-2 8-7z"></path><path d="M182 400c1-2 5-3 7-4-2 2-3 3-4 5l-1 1-1-2h-1z" class="R"></path><defs><linearGradient id="Bj" x1="198.266" y1="382.229" x2="202.685" y2="398.87" xlink:href="#B"><stop offset="0" stop-color="#21211f"></stop><stop offset="1" stop-color="#4e4d4f"></stop></linearGradient></defs><path fill="url(#Bj)" d="M208 380c0-1 2-2 2-3 2-3 3-5 5-7v-1l1 1-2 2c0 1-1 1-1 2-1 1-2 2-2 3-1 2-1 4-2 6l-3 6c-2 3-4 6-6 8s-5 4-7 5c-1 1-4 2-4 3l-2 1h0v-1s2-2 3-2c1-2 3-3 4-5v-1s2-1 2-2l6-6c2-2 4-6 6-9z"></path><defs><linearGradient id="Bk" x1="225.021" y1="401.614" x2="232.382" y2="412.185" xlink:href="#B"><stop offset="0" stop-color="#2d2c2c"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#Bk)" d="M246 388l1-1c1-1 1-2 2-3 0-1 1-2 2-3 0 2-5 8-4 9h-1l-3 4c-1 0-1 1-1 2h-1c-2 2-4 5-6 8-1 1-2 3-3 4h0v1c-1 2-2 4-3 5v1h0c-1 2-3 3-4 5 0 1-1 2-2 2h-2l-3 3h-2l-1-1c0 1-1 1-2 1l-1-1 2-2s0-1-1-1c0-1 0-1-1-1-1 1-2 2-4 3 1-1 1-2 1-3l3-3h-1c0-1 1-2 2-2 1-2 3-3 5-5v-1h0c0-1 1-1 1-1l3-3h2c1-2 4-4 5-6 2 0 3-2 3-3h2v2c1-2 2-3 3-4 0-1 0-1 1-2 0 0 0 1 1 1h1v-1c1 1 2 0 3 0 0 0 0-1 1-2v2-1c1-1 2-1 2-3z"></path><path d="M240 393h0l-2 2v-1l1-1h1z" class="B"></path><path d="M232 408v1c-1 2-2 4-3 5s-2 3-4 3c2-3 4-6 7-9z" class="I"></path><path d="M229 414v1h0c-1 2-3 3-4 5 0 1-1 2-2 2h-2l2-2 2-3c2 0 3-2 4-3z" class="Z"></path><path d="M232 402c1-2 3-4 4-5-2 4-5 8-8 11 0 1-1 2-2 3-1-1-1 0-2-1h-1l1-2h1c2 0 6-5 7-6z" class="F"></path><path d="M215 422c3 0 4-2 6-3l2 1h0l-2 2-3 3h-2l-1-1c0 1-1 1-2 1l-1-1 2-2h1z" class="O"></path><path d="M232 396h2v2l-3 3 1 1c-1 1-5 6-7 6h-1v-1l-2 1h0c0-2 1-2 2-3 1-2 4-4 5-6 2 0 3-2 3-3z" class="I"></path><path d="M222 405h2c-1 1-2 1-2 3h0l2-1v1l-1 2h1c1 1 1 0 2 1l-3 3-8 8h-1s0-1-1-1c0-1 0-1-1-1-1 1-2 2-4 3 1-1 1-2 1-3l3-3h-1c0-1 1-2 2-2 1-2 3-3 5-5v-1h0c0-1 1-1 1-1l3-3z" class="D"></path><path d="M220 412v2 1c-1 1-1 2-3 3 0-1-1-1-1-2l2-2 2-2z" class="H"></path><path d="M223 410h1c1 1 1 0 2 1l-3 3v-1l-3 2v-1-2s2-2 3-2z" class="R"></path><path d="M212 417h1v-1c2-1 3-2 5-2l-2 2-4 4c-1 1-2 2-4 3 1-1 1-2 1-3l3-3z" class="F"></path><path d="M222 405h2c-1 1-2 1-2 3h0l2-1v1l-1 2c-1 0-3 2-3 2l-2 2c-2 0-3 1-5 2v1h-1-1c0-1 1-2 2-2 1-2 3-3 5-5v-1h0c0-1 1-1 1-1l3-3z" class="X"></path><defs><linearGradient id="Bl" x1="235.566" y1="391.292" x2="211.326" y2="403.079" xlink:href="#B"><stop offset="0" stop-color="#222"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#Bl)" d="M233 385h0c1 0 1 0 1-1l2-1v-2l4-7v1c0 1 0 1-1 2v1h0c-1 2-2 3-2 4s-1 1-1 2l-1 1s-1 1-1 2c-1 0-1 1-1 2l-2 2h0v1l-1 1c-1 1-1 1-1 2h0l1-1c2-1 3-3 4-3h1-1l-1 1h2c-2 1-3 5-5 5l-1 2h0c-1 2-4 4-5 6h-2l-3 3s-1 0-1 1h0v1c-2 2-4 3-5 5-1 0-2 1-2 2-6 5-13 7-19 12-1 0-3 1-4 2 0 1-1 2-1 2h-1c-1 0-2 2-3 2l-1-1 6-5 5-4c3-1 8-4 10-6l-1-1v-1h0c-1-1-1-2-1-3l1-2c1-1 1-1 1-2 1-1 3-1 4-2l4-4-2 1h0v-1h-1c3-3 5-5 7-8h0c1-1 2-1 2-2l1-1 1-1c0-1 1-2 2-3s2-3 3-4l1-2c0-1 1-2 1-2h3 1c0 1-1 1-1 2h0v1 1c1-1 2-2 2-3h2c-1 1-1 1-2 1l1 1c1 0 1 0 2-1h0l-1 2z"></path><path d="M218 409l-1-1c1-2 2-3 4-4h1v1l-3 3s-1 0-1 1z" class="B"></path><path d="M226 391v-1l1-1 2-2h0c1-1 2-2 3-2h0l-1 1c0 1-1 2-1 3h-1c-1 1-1 3-2 4-1-1 0-1 0-1-1 0-1 0-2 1v-1l1-1z" class="V"></path><path d="M224 385l1-2c0-1 1-2 1-2h3 1c0 1-1 1-1 2h0v1 1c1-1 2-2 2-3h2c-1 1-1 1-2 1l1 1c1 0 1 0 2-1h0l-1 2-2 1 1-1h0c-1 0-2 1-3 2h0l-2 2-1 1v1l-1 1h-1v-1c0-1 1-1 1-2v-1s1-2 2-3h-1 0-1-1z" class="g"></path><path d="M224 385l1-2c0-1 1-2 1-2h3l-2 4h-1 0-1-1z" class="B"></path><defs><linearGradient id="Bm" x1="218.486" y1="384.35" x2="215.202" y2="406.018" xlink:href="#B"><stop offset="0" stop-color="#3c3a39"></stop><stop offset="1" stop-color="#6f7072"></stop></linearGradient></defs><path fill="url(#Bm)" d="M215 396h0c1-1 2-1 2-2l1-1 1-1c0-1 1-2 2-3s2-3 3-4h1 1 0 1c-1 1-2 3-2 3l-7 8s0 1-1 2c-1 2-4 4-5 5l-1 1-2 1h0v-1h-1c3-3 5-5 7-8z"></path><path d="M212 403c1 1 0 2 1 3l5-3c-2 2-3 3-4 5-1 1-3 2-3 3h1c1 0 1 0 2-1l1 1c-1 0-1 1-2 1h0c-2 2-5 4-7 7l4-4c3-1 5-4 8-6v1c-2 2-4 3-5 5-1 0-2 1-2 2-6 5-13 7-19 12-1 0-3 1-4 2 0 1-1 2-1 2h-1c-1 0-2 2-3 2l-1-1 6-5 5-4c3-1 8-4 10-6l-1-1v-1h0c-1-1-1-2-1-3l1-2c1-1 1-1 1-2 1-1 3-1 4-2l4-4 1-1z" class="I"></path><path d="M212 403c1 1 0 2 1 3l5-3c-2 2-3 3-4 5-1 1-3 2-3 3h1c1 0 1 0 2-1l1 1c-1 0-1 1-2 1h0 0-2c-2 2-5 3-8 4 3-2 5-4 7-7-2 1-3 2-4 3l-1-1 2-3 4-4 1-1z" class="S"></path><path d="M189 296l6 6c2 2 4 4 6 5l5 5 1-1h1c1 1 1 1 3 0l2 2h0c1 0 2 0 4 1 1 0 3 0 5 1 1 0 1 1 1 1h3c1 0 1 0 1 1h2c1 1 3 1 4 2l7 4 2 4c-1-1-4-3-6-4-2 0-4-1-6-1h1l1 2 2 2v1c1 2 2 3 2 6 2 10-3 18-8 26-1-1-1-2-1-3-1-6-4-11-7-17-1-3-3-6-5-8-3-4-7-7-10-10l-1-2h0c-1-1-3-3-4-3h-1l-4-5c2 1 3 2 5 4 2 1 2 1 3 1l-10-9c-2-3-5-5-6-8l-2-3c1 0 2 0 3 1l1-1z" class="Y"></path><defs><linearGradient id="Bn" x1="210.794" y1="311.102" x2="215.099" y2="322.297" xlink:href="#B"><stop offset="0" stop-color="#777778"></stop><stop offset="1" stop-color="#969494"></stop></linearGradient></defs><path fill="url(#Bn)" d="M206 312l1-1h1c1 1 1 1 3 0l2 2h0c1 0 2 0 4 1 1 0 3 0 5 1 1 0 1 1 1 1h-3c-2 0-4-2-7-2v1c0 1 0 1 1 1l3 3c1 2 3 4 5 6 0 0 1 1 1 2 2 2 5 6 6 9h-1 0c-2-5-5-8-9-12 2 3 4 6 5 8h0l-1 1v1c-3-4-6-7-9-10-2-1-4-3-6-4-1-1-3-2-4-3h2 1 1 1l3 3c2 1 3 2 5 3h0c-1-2-4-6-6-8l-5-3z"></path><path d="M189 296l6 6c2 2 4 4 6 5l5 5 5 3c2 2 5 6 6 8h0c-2-1-3-2-5-3l-3-3h-1-1-1-2l-1-1-10-9c-2-3-5-5-6-8l-2-3c1 0 2 0 3 1l1-1z" class="c"></path><path d="M195 306l5 2h1v-1l5 5 5 3v1c-3 0-6-4-8-5s-3-1-5-2l-3-3z" class="X"></path><defs><linearGradient id="Bo" x1="187.775" y1="298.807" x2="202.056" y2="306.471" xlink:href="#B"><stop offset="0" stop-color="#212021"></stop><stop offset="1" stop-color="#3b3b3a"></stop></linearGradient></defs><path fill="url(#Bo)" d="M189 296l6 6c2 2 4 4 6 5v1h-1l-5-2 3 3c-2-1-3-2-5-2-2-3-5-5-6-8l-2-3c1 0 2 0 3 1l1-1z"></path><path d="M187 299c3 1 6 5 8 7l3 3c-2-1-3-2-5-2-2-3-5-5-6-8z" class="U"></path><defs><linearGradient id="Bp" x1="220.016" y1="313.908" x2="226.689" y2="329.942" xlink:href="#B"><stop offset="0" stop-color="#918f90"></stop><stop offset="1" stop-color="#c2c3c3"></stop></linearGradient></defs><path fill="url(#Bp)" d="M214 316c-1 0-1 0-1-1v-1c3 0 5 2 7 2h3 3c1 0 1 0 1 1h2c1 1 3 1 4 2l7 4 2 4c-1-1-4-3-6-4-2 0-4-1-6-1h1l1 2 2 2v1c-2-1-3-3-5-3l-1-1h0c0 2 3 5 4 7 0 2 1 3 1 5-1-2-2-3-3-5-2-2-4-5-6-7-1 0-2 0-3-1v1h0c0 1 1 1 1 2-2-2-4-4-5-6l-3-3z"></path><path d="M214 316v-1l1 1c1 0 2 1 2 1v1 1l-3-3z" class="D"></path><path d="M217 319h1c1 0 1 0 2 1s3 2 4 3c-1 0-2 0-3-1v1h0c0 1 1 1 1 2-2-2-4-4-5-6z" class="E"></path><path d="M214 316c-1 0-1 0-1-1v-1c3 0 5 2 7 2h3 3c1 0 1 0 1 1h2c1 1 3 1 4 2h-2-1-1c-2-1-3-1-4-2s-2 0-4 0c-2-1-4-1-6-1l-1-1v1z" class="P"></path><path d="M461 227h1c1 1 3 1 5 1 1 1 4 1 5 2h0 1l6 3h2c0 1 1 1 2 1l2 1c2 1 3 2 5 3h0c1 1 3 2 4 4 1 0 3 1 4 1l1 2c1 0 2 2 3 2l1 2 3 3 2 2 3 4c1 1 1 2 2 2v1 1c2 0 2 1 2 2v1h1s0 1 1 1 1 2 1 2l1 1c-1 1-3 1-4 1h0l-7 2c-2 0-4 0-6 1-2 0-5 0-7 1h-4c-1 0-2 2-2 2h0c1 1 3 0 4 1h3 6v1h2v2h1c2 0 4 0 7 1 2 0 7 0 9-2 1 0 2-1 2-2h1v3l1 1h0c1 1 2 2 2 3-1 0-2 0-3 1h-3l-1 1h0c1 2 3 0 4 1l1 1h1 4c0 1 1 3 1 4l-1 1 1 1h0l2 2v2c0 1 1 0 1 2h0c-1 0-1 0-1 1 1 0 1 0 2 1 0 1 1 2 1 3 1 2 1 3 2 5s1 5 2 8h0v1c0 1 0 2 1 3h0l1 5v1l-1 1h2v2c0 1-1 1 0 2v1l1 1c0 1 0 2 1 3-3 0-10 2-12 4l-2 2h1c4-1 8-2 12 0 3 2 5 4 6 8 0 3 0 5-1 8l1 1h1c0 1-1 2-1 3-3 5-6 8-11 10-4 2-8 3-13 3h-6c0-2 0-2 1-3h1v-1c-2 0-4 1-6 2l-2 2c-2-1-6 0-8 0h-3-1c3 3 6 5 9 8 3 5 5 11 8 16 2 3 5 5 6 8 2 2 2 4 3 6 4 22-25 40-36 57-6 9-9 17-6 27l-2-1c-2-1-4-3-6-5-2-3-3-8-4-12-2 3-6 6-8 9-4 3-7 8-9 11-4 8-8 16-6 25 1 3 3 7 7 9 3 2 9 2 12 1s6-3 8-6c1-2 2-4 1-7 0-1-2-3-3-4-1 0-2 0-3 1v-1l1-1h3c2 1 3 2 4 4 1 3 1 6 0 9-2 4-4 6-8 8-5 2-12 1-17-1s-10-7-12-12c-1-4-1-7-1-11 0-5 1-10 3-14 0-2 3-7 2-8-5 3-11 4-17 5-7 1-17 0-24-5-4-3-7-10-8-15 0-3 0-7 3-10 2-3 5-5 9-5 2 0 5 0 7 2 2 1 4 4 4 6s0 2-1 3h0v-2s0-1-1-1c-2-2-4-2-6-2-3 0-5 1-7 3-1 2-1 5-1 7 0 3 3 6 5 7 4 3 10 3 15 2 17-3 33-16 43-30 6-10 11-21 11-32 0-6-1-12-2-17-2 0-4 1-5 1-2 1-4 1-6 1 1 4 1 8 1 11 0 8-5 17-10 22-3 2-6 4-9 7 2-4 4-8 5-12 0-4-1-9-2-12-4-15-13-28-21-41-6-8-12-16-15-25-3-7-5-14-5-22-2 2-4 3-6 5-6 5-12 12-12 20-1 6 1 13 5 17 3 3 6 4 9 4s6-1 9-3c1-2 1-3 1-4v-1h0 1c1 1 1 2 1 4-1 3-2 5-4 7-4 3-9 3-13 3-7-1-13-4-17-9-2-2-4-5-4-7-1-3-1-6-1-9-1-7-1-14 0-21-1-3-1-5-1-7 1-3 1-7 1-10 1 0 1-2 2-3h-1v-1-2c-1-1-1-1-1-2v-2c0-1 0-2 1-3v-2-6l2-6c1-1 2-2 3-2h0l1-2 1-1 1-3h0l3-7c1-1 2-1 3-2l2-6v-2-1h2c1 1 1 2 2 2v1c-1 1-1 2-1 3-1 2-1 4-2 6l-2 3h0v2 1h0l1-1 1 1 1-1v-1c1 0 1 1 1 2h1v2c1-1 1-2 2-3 0-1 0-2 1-2l2-2c0-1 0-2 1-3 0-1 1-2 1-3 0-2 1-2 1-4l1-2h0c1-1 1-2 2-3v-1h1l1-1h1l2-3 1 1c1 1 0 2 0 3l6-5c0-1 2-2 3-3 2-2 4-4 5-7 1 0 1-1 2-2 0-2 1-3 3-5h0l5-5 4-5 1 1v2h0 1v1c3-3 4-6 6-9l4-7h2l1-1z" class="h"></path><path d="M491 338l-1 1h0v-2c0-1 1-2 2-3v2l-1 2z" class="G"></path><path d="M481 444c1 0 1 0 1 1l-1 4c-1-1-1-1-2-1l2-4z" class="H"></path><path d="M425 312c0-3 0-4 1-7l1 1v3-1l2-2v1l-1 2-3 3z" class="d"></path><path d="M504 288h7c1 1 3 1 4 0l2 1h-13v-1z" class="E"></path><path d="M390 346c0-2 2-3 2-5v-1c1 0 1-1 1-1 0-1 0-1 1-2v1l1-1v1l-1 2-1 3h-1l-2 4v-1z" class="G"></path><path d="M468 320h1l1 8c0 2 1 3 1 5-2-4-3-9-3-13z" class="d"></path><path d="M452 331h0l-1-2c1-1 1-2 2-3v-1h0c1 2 1 3 1 4v2l-1 1c0-1 0-1-1-1h0zm-4-82l1-1c0 2-1 3-2 4 0 1-1 1-1 2 0 0 0 1-1 1h0v1h-1v1l2-2h1c-2 1-3 3-4 4h-2l7-10z" class="Q"></path><path d="M477 331c1 1 0 5 1 7 0 2 1 3 2 5h0l-1 1c0-1-1-1-1-2-2-4-2-8-1-11z" class="d"></path><path d="M500 340h0l3-2 2 1h4c-1 1-1 1-1 2h0-5l-3-1z" class="F"></path><path d="M505 339h4c-1 1-1 1-1 2-2 0-4 0-5-1 1 0 1-1 2-1z" class="Z"></path><path d="M512 342v-1c4 0 7 0 11-1l-3 1v1c1 1 3 0 5 1-4 0-9 0-13-1z" class="D"></path><path d="M426 313l1-1 1 1v2 2l1 1v-1l1-1v3h1-2c-1 0-3-2-3-2-1-2-1-3 0-4z" class="b"></path><path d="M439 490c1 0 2 1 3 1-4 1-7 2-11 2-1 0-2 1-3 0h0 1c4 0 7-2 10-3z" class="R"></path><path d="M454 235l4-7h2l-6 12c0-2 1-3 0-5z" class="D"></path><path d="M454 493h0c1 0 2-1 3-1 2-2 4-3 6-4l-5 6h-1c-1 1-3 2-4 3-1 0-1 0-1-1s1-2 2-3z" class="I"></path><path d="M448 351c-1-1-2-3-3-5l4 3-1-5c2 2 3 5 5 7l-1 1v1h-1l-2-2h-1z" class="Q"></path><path d="M509 339l11 1h5-2c-4 1-7 1-11 1v1c-1 0-3-1-4-1h0c0-1 0-1 1-2z" class="I"></path><path d="M462 227c1 1 3 1 5 1-1 1-1 2-1 3v-1h-2c-1 1-1 2-1 3l-3 6h0l-3 3c0-1 1-3 2-4 2-3 3-6 3-10v-1z" class="M"></path><path d="M469 359h0c5 4 10 8 16 11l1 1c-3 0-5-2-7-3-1-1-2-1-3-1-2-1-5-3-6-4l1-1v-1c-1 0-2-1-2-2z" class="Q"></path><path d="M499 331c1 0 2 0 2 1v-1c2 0 2 0 3 1 1 0 3-1 4-1h0l1 1-3 2h-1v-1h1l1-1h1-2l-1 1h0-1c-2 0-3 1-5 1h-1 0 0c-1 1-2 1-3 2l-1-1c2-1 3-3 4-3l1-1z" class="G"></path><path d="M499 331c1 0 2 0 2 1h-2-1l1-1z" class="V"></path><path d="M406 289c1 1 1 1 1 2-1 2-1 5-1 8-1 2-1 3-2 5l-1 2v-1-1-1l1-2c1 0 0 0 1-1s0-5 0-6v1h0l-1 1c0 1 0 2-1 3h0v1c-1 2-1 3-2 4h0l5-15z" class="g"></path><path d="M415 281l2-1 1 1h0c2 0 3-1 4-1-2 3-6 5-8 8v-3c0-1 0-2 1-3v-1z" class="T"></path><path d="M415 281l2-1 1 1h0c0 1-1 2-2 3l-1-1v-1-1z" class="a"></path><path d="M452 496c0 1 0 1 1 1 1-1 3-2 4-3h1l-7 10h-2v-3c1-2 2-3 3-5h0z" class="X"></path><path d="M429 358h1s0 1 1 1c2 0 4-1 6-3h1l1-1 1-1s1 0 1-1c1 1 1 1 1 3h0c-1 0 0 0-1 1-1 0-2 0-3 1v1c-2 0-2 2-3 3-1-1-1-1-1-2-1-1-2-1-2-1h-1 0-2l-1-1h1z" class="d"></path><path d="M474 355c2-1 4 0 5 1 1-1 1-1 0-1v-1h3l-1-1-5-4c3 1 5 3 8 5h0l-1 1 1 1-1 1c0-1-1-1-2-1h1c0 1 0 1-1 1h-1l-1 1 1 1h-1l-1-1c-1-1-3-1-4-2v-1z" class="V"></path><path d="M462 304c2-4 2-8 4-12v1c0 1 0 2-1 3s-1 2-1 3l1-1s0-1 1-2v1c-3 6-4 12-4 19-1-1 0-2-1-4-1-3 0-6 1-8z" class="C"></path><path d="M441 385c-1-2-2-3-2-6h1c2 5 7 8 12 10l-1 1c-1 0-2 0-4-1s-4-3-6-4z" class="K"></path><path d="M462 475l3-3v1h0c-4 5-8 10-13 13l1-4 9-7z" class="c"></path><path d="M500 452h0c2-2 4-5 8-6l-3 4-9 10v-2l4-6z" class="K"></path><path d="M451 363c-3-4-5-8-8-12-1-1-2-2-2-3s0-1 1-1l8 10v1l1 1c-1 0-1 0-2-1 1 2 2 3 2 5z" class="M"></path><path d="M457 362c1 1 2 3 3 3 1 1 3 4 5 5l2 2 2 2c-2 0-3-1-4-3v1 2h1l1 1v1l-3-3c-2-1-3-3-5-5-1-2-3-4-4-6v1c1 0 1 0 2 1h0 1c0 1 1 1 1 2h1 0c0-1-2-2-3-3v-1z" class="B"></path><path d="M464 373l3 3 4 5c0 1-1 1-1 2 0 2 1 3 1 4l-1 1c-1-3-2-5-4-7v-1c0-1-1-3-2-4v-3z" class="e"></path><path d="M442 356c0 3-1 6-2 9v-4h-1l-1 1c-1 1-2 3-3 3l-2-1-1-5h-1 0 1s1 0 2 1c0 1 0 1 1 2 1-1 1-3 3-3v-1c1-1 2-1 3-1 1-1 0-1 1-1h0z" class="Z"></path><defs><linearGradient id="Bq" x1="478.605" y1="305.751" x2="483.652" y2="293.399" xlink:href="#B"><stop offset="0" stop-color="#373537"></stop><stop offset="1" stop-color="#4f4f4e"></stop></linearGradient></defs><path fill="url(#Bq)" d="M487 295c2 0 2-1 4-1-4 3-8 5-12 9-2 2-3 4-5 6 0-1 0-1 1-2 0-1 1-2 0-3v-1c1-1 1-2 2-3l1 1c3-2 5-5 9-6z"></path><path d="M506 334h1v1h2l1 1-2 1v-1c-2 1-3 2-5 2l-3 2h0-1c-2 0-4 0-6-1 3-1 6-3 9-4l3-1h1z" class="d"></path><path d="M506 334h1v1h2l1 1-2 1v-1h-5s-1 0-1-1l3-1h1z" class="e"></path><path d="M403 306l1-2c1-2 1-3 2-5v12c1 3 2 5 2 7v2l-1-1c-2-3-5-8-4-13z" class="M"></path><path d="M477 289v1l1-1h0l-3 4c-1 1-2 2-2 3-1 1-1 2-2 3s-1 2-1 2c-2 3-2 6-3 9v2l-1 1v-2-1l1-2v-1c0-1 1-3 1-4h0c-1 1-1 2-1 2v2c-1 1-1 2-1 2 0 1 0 2-1 3v4-3c0-9 5-18 12-24z" class="Q"></path><path d="M455 490c3 0 4-2 6-4 1-1 3-2 4-3 2-2 4-5 7-6 0 1 1 1 1 1-2 1-3 3-4 4l-6 6c-2 1-4 2-6 4-1 0-2 1-3 1h0c0-1 1-2 1-3z" class="k"></path><path d="M494 458v1l2-1v2c-6 6-12 15-14 24v1 1-1h0v-4c1-3 2-5 3-7 2-6 5-11 9-16z" class="T"></path><defs><linearGradient id="Br" x1="441.348" y1="389.393" x2="439.472" y2="395.923" xlink:href="#B"><stop offset="0" stop-color="#7e7d7e"></stop><stop offset="1" stop-color="#969896"></stop></linearGradient></defs><path fill="url(#Br)" d="M435 388l1-1 1 1v-1c-1-1-1-1-1-2h0 1l1 1 2 3c1 2 2 2 3 4l-1 1c3 2 5 4 8 6l2 3c-7-3-13-9-17-15z"></path><path d="M442 394c-2-2-3-4-5-7l1-1 2 3c1 2 2 2 3 4l-1 1zm-41-99c1-1 1-2 2-3 0-1 0-2 1-2l2-2v1l-5 15-3 5h-2l1-1c1-1 2-3 1-4h-1v-1c1-2 1-4 2-5s2-2 2-3z" class="K"></path><path d="M458 474c0 2-1 3-3 5-1 0-2 1-3 1s-3 1-4 2l-8 5c-5 2-9 4-15 4v-1c12-2 23-10 33-16z" class="W"></path><path d="M513 305l7-2h0l-1 1-4 2c-3 1-6 3-9 3-1 0-1 1-2 1-5 1-8 4-12 5 1-1 2-3 3-4 3-1 5-2 8-3l6-3 3-1 1 1z" class="Q"></path><path d="M509 305l3-1 1 1c-1 0-3 1-4 1v-1z" class="B"></path><path d="M512 285c4 1 8 0 12 0h-3l-1 1h0c1 2 3 0 4 1l1 1h1l-9 1-2-1c-1 1-3 1-4 0h-7v-2l5-1h2 1z" class="a"></path><path d="M504 286l5-1c-1 1-2 1-3 2 2 1 4 0 6 1h3c-1 1-3 1-4 0h-7v-2z" class="J"></path><defs><linearGradient id="Bs" x1="494.863" y1="288.745" x2="503.436" y2="286.72" xlink:href="#B"><stop offset="0" stop-color="#8c8b8c"></stop><stop offset="1" stop-color="#acabac"></stop></linearGradient></defs><path fill="url(#Bs)" d="M500 287l4-1v2 1c-1 1-4 1-5 2l-5 1c-1 1-2 2-3 2-2 0-2 1-4 1v-1-1c1-1 3-3 5-3l8-3z"></path><path d="M490 292l2-1c2-1 4-1 7 0l-5 1c-1 1-2 2-3 2-2 0-2 1-4 1v-1l3-2z" class="b"></path><path d="M490 292h4c-1 1-2 2-3 2-2 0-2 1-4 1v-1l3-2z" class="U"></path><path d="M432 383c1 1 1 0 1 1h1v-3l2 1h1c1 0 1 1 2 2l1 1h1c2 1 4 3 6 4 0 1 0 1-1 1h1c-1 1-1 1-1 2h-2-1c1 1 2 1 2 2v1l-2-2c-1-2-2-2-3-4l-2-3-1-1h-1 0c0 1 0 1 1 2v1l-1-1-1 1s-2-4-3-5z" class="N"></path><path d="M440 389v-1-1h0c1 0 1 0 2 1s3 1 4 2h1c-1 1-1 1-1 2h-2-1c1 1 2 1 2 2v1l-2-2c-1-2-2-2-3-4z" class="L"></path><defs><linearGradient id="Bt" x1="449.672" y1="367.144" x2="464.535" y2="368.663" xlink:href="#B"><stop offset="0" stop-color="#424341"></stop><stop offset="1" stop-color="#7a777a"></stop></linearGradient></defs><path fill="url(#Bt)" d="M451 363c0-2-1-3-2-5 1 1 1 1 2 1l-1-1v-1l5 5c1 2 3 4 4 6 2 2 3 4 5 5v3c1 1 2 3 2 4v1l-3-4-12-14z"></path><path d="M482 469c0-1 1-3 2-3h1c-1 2-2 4-2 6l-3 8v1c-1 3-1 6-1 9-1-4-2-9-2-13v-1l-1 2h-1v-1c1-1 1-2 2-2h0v-1h-1c1-2 4-4 5-6l1 1z" class="H"></path><path d="M482 469c0-1 1-3 2-3h1c-1 2-2 4-2 6l-3 8c-1 0-1 0-1-1v-4c0-1 1-2 1-3 1-1 1-2 2-3z" class="K"></path><path d="M454 329c1 1 2 3 2 4h0v2h-1-1c2 5 5 10 9 14 0 2 1 3 2 4l1 2h-1c-7-7-11-14-13-24h0c1 0 1 0 1 1l1-1v-2z" class="D"></path><path d="M454 329c1 1 2 3 2 4h0v2h-1-1c0-1-1-2-1-3l1-1v-2z" class="B"></path><path d="M430 380h0l-7-12c-1-1-3-4-3-6 1 1 1 2 2 3l6 9c1 1 2 3 4 3h1 0c1 0 3 3 4 4h1c0-2 1-4 1-6 0 1 1 2 1 4h-1c0 3 1 4 2 6h-1l-1-1c-1-1-1-2-2-2h-1l-2-1v3h-1c0-1 0 0-1-1-1 0-1-2-2-3z" class="X"></path><path d="M430 380h1l1-1c1 1 1 1 2 1v1 3h-1c0-1 0 0-1-1-1 0-1-2-2-3z" class="S"></path><path d="M425 490v1c6 0 10-2 15-4 3-2 5-3 8-5 1-1 3-2 4-2-5 4-11 7-17 10-2 1-4 1-6 2-1 0-3 1-4 1l-1 1c-4 0-11-2-13-5l14 1zm23-139h1l2 2h1v-1l1-1 3 4c1 1 1 1 3 2s3 3 5 4l5 5h-1c-5-1-8-6-12-6v1c-3-2-6-7-8-10z" class="B"></path><path d="M463 377l3 4c2 2 3 4 4 7l1 4-2 1h-3-1v-1c1 0 2-1 3-1v-1c-1 0-1 0-1-1h-1l-1 1c-1 1-2 1-3 1v1 1c-1 0-2 0-3-1l-2-1h2c2 0 3-1 4-3 0-2 2-5 1-7h-1 0v-4z" class="G"></path><path d="M463 388l2-2h0v-2h2c1 1 0 3 0 4s-1 1-1 1l-1 1c-1 1-2 1-3 1v1 1c-1 0-2 0-3-1l-2-1h2c2 0 3-1 4-3z" class="M"></path><defs><linearGradient id="Bu" x1="466.494" y1="353.762" x2="476.425" y2="363.274" xlink:href="#B"><stop offset="0" stop-color="#959495"></stop><stop offset="1" stop-color="#b0b0b0"></stop></linearGradient></defs><path fill="url(#Bu)" d="M465 353h3c1 0 2 1 3 2 1 0 2 1 3 1 1 1 3 1 4 2-1 1-2 1-3 1l-1-1-1 1c1 2 5 5 6 6s4 2 5 3c0 1 0 1 1 2-6-3-11-7-16-11h0c0-1-4-3-4-4h1l-1-2z"></path><path d="M471 355c1 0 2 1 3 1 1 1 3 1 4 2-1 1-2 1-3 1l-1-1c0-1-3-3-3-3z" class="c"></path><path d="M479 448c1 0 1 0 2 1-1 1-2 3-3 4-2 4-4 8-7 12-2 2-4 5-6 7l-3 3 1-4v-1c2-3 5-6 7-8 2-4 4-7 7-10 0-2 1-3 2-4z" class="K"></path><path d="M479 448c1 0 1 0 2 1-1 1-2 3-3 4v-2c1 0 1 0 1-1l-2 2h0c0-2 1-3 2-4z" class="W"></path><path d="M483 292h0c3-3 11-7 16-8 1 1 1 1 2 1 0 0-1 0-1 1v1l-8 3c-2 0-4 2-5 3v1 1c-4 1-6 4-9 6l-1-1c1-2 3-5 4-7l2-1z" class="I"></path><path d="M483 292h0c3-3 11-7 16-8 1 1 1 1 2 1 0 0-1 0-1 1-4 1-8 2-11 4-2 1-5 4-7 4l-1-1 2-1z" class="H"></path><defs><linearGradient id="Bv" x1="416.296" y1="347.797" x2="427.969" y2="352.721" xlink:href="#B"><stop offset="0" stop-color="#757676"></stop><stop offset="1" stop-color="#a6a3a5"></stop></linearGradient></defs><path fill="url(#Bv)" d="M413 334l1-1c1 1 1 1 1 2l1 2c1 1 1 2 1 3 1 0 1 1 1 2l1-1h0 1l1 1c0 1 1 2 2 3 0 2 1 4 2 6v1l1 3 1 2h0 1 1v1h-1 0v1l-1 2-1-1-13-26z"></path><path d="M426 360v-2h1 1v1l-1 2-1-1z" class="K"></path><path d="M419 341h1l1 1c0 1 1 2 2 3 0 2 1 4 2 6v1l-1-1h0c-2-2-4-7-5-10z" class="D"></path><path d="M484 377l-2-2 1-1c0 1 1 2 2 2s1 0 2 1l1-1c1 1 1 1 3 1h0c-1-1-1 0-1-1h1c4 2 8 2 12 6l-1 1c2 2 4 4 5 6l2 4h0c-1-1-1-2-2-3s-2-2-3-4h-1-1c0-1 0-1-1-1-1-1-1-1-3-1-2 1-5-2-7-3s-4-3-6-4h-1z" class="Q"></path><defs><linearGradient id="Bw" x1="459.035" y1="333.759" x2="468.812" y2="357.142" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#727272"></stop></linearGradient></defs><path fill="url(#Bw)" d="M454 335h1 1v-2h0c3 4 5 9 8 13 3 3 7 6 10 9v1c-1 0-2-1-3-1-1-1-2-2-3-2h-3c-1-1-2-2-2-4-4-4-7-9-9-14z"></path><path d="M463 349c2 1 3 1 4 3l1 1h-3c-1-1-2-2-2-4z" class="H"></path><path d="M471 381l3 4c1 2 2 3 2 5l1 1v5 1h0c-1 2-3 5-4 7 0-1-1-1-1-1 0-2 1-4 2-6l1-1h-2l-1 1v-1l-1-1v-3l-1-4 1-1c0-1-1-2-1-4 0-1 1-1 1-2z" class="j"></path><path d="M473 391h0l1-1h0c0 1 0 2 1 3-1 1-2 1-2 1 0 1 0 1-1 2h1l-1 1v-1l1-5z" class="G"></path><path d="M470 388l1-1 2 4-1 5-1-1v-3l-1-4z" class="M"></path><path d="M471 381l3 4c-1 1-1 2-1 3s0 1 1 1v1l-1 1h0l-2-4c0-1-1-2-1-4 0-1 1-1 1-2z" class="Q"></path><path d="M382 325v-2c1 1 1 1 2 1h1 3l2-1-6 12c-1 3-2 7-4 10-1-3-1-5-1-7 1-3 1-7 1-10 1 0 1-2 2-3z" class="U"></path><path d="M428 358h0l1 1h2 1l1 5 2 1c1 0 2-2 3-3l1-1h1v4l-2 5-1 2v4h-1v-1l-9-14 1-2v-1z" class="T"></path><path d="M434 368c2 0 2-1 2-2h1c0 1-1 3 0 4v2 4h-1v-1-1c0-2-1-4-2-6z" class="D"></path><path d="M428 358h0l1 1h2 1l1 5 2 1v1l2-2v1 1h0-1c0 1 0 2-2 2-2-3-2-7-6-9v-1z" class="H"></path><path d="M438 362l1-1h1v4l-2 5-1 2v-2c-1-1 0-3 0-4h0v-1-1l-2 2v-1c1 0 2-2 3-3z" class="R"></path><path d="M438 362l1-1h1v4l-2 5v-2-4c1-1 1-1 1-2h-1z" class="N"></path><path d="M523 277h1v3l1 1h0c1 1 2 2 2 3-1 0-2 0-3 1-4 0-8 1-12 0h-1-2l-5 1-4 1v-1c0-1 1-1 1-1-1 0-1 0-2-1h1l12-3c2 0 7 0 9-2 1 0 2-1 2-2z" class="L"></path><path d="M525 281h0c-1 1-3 1-5 1-1 1-3 1-5 2-1 0-2 1-3 1h-1c0-1-1-1-1-1s1-1 2-1c3 0 5-1 8-1 1-1 3-1 5-1z" class="H"></path><path d="M501 285c3-1 6-1 9-1 0 0 1 0 1 1h-2l-5 1-4 1v-1c0-1 1-1 1-1z" class="c"></path><path d="M525 281c1 1 2 2 2 3-1 0-2 0-3 1-4 0-8 1-12 0 1 0 2-1 3-1 2-1 4-1 5-2 2 0 4 0 5-1z" class="E"></path><defs><linearGradient id="Bx" x1="513.298" y1="300.357" x2="527.645" y2="301.877" xlink:href="#B"><stop offset="0" stop-color="#353635"></stop><stop offset="1" stop-color="#605e60"></stop></linearGradient></defs><path fill="url(#Bx)" d="M512 304l10-6c2 0 3 0 5-1v-1c-2 0-4 1-5 1-1 1-2 1-2 1h-1-4l15-5 1 1h0l2 2v2c0 1 1 0 1 2h0c-1 0-1 0-1 1l-1-1-4 2h-1l-4 1-4 1 1-1h0l-7 2-1-1z"></path><path d="M527 300c2-1 3-1 5-1h1l-1 1-4 2h-1l1-1-1-1z" class="X"></path><path d="M527 300l1 1-1 1-4 1-4 1 1-1h0 0l7-3z" class="g"></path><path d="M458 474c2-1 3-3 5-4v1l-1 4-9 7-1 4-10 5c-1 0-2-1-3-1-3 1-6 3-10 3h-1 0-3c1 0 3-1 4-1 2-1 4-1 6-2 6-3 12-6 17-10 1 0 2-1 3-1 2-2 3-3 3-5z" class="J"></path><path d="M439 490c5-3 10-4 14-8l-1 4-10 5c-1 0-2-1-3-1z" class="L"></path><path d="M446 249h1 1l-7 10c0 1-1 2-2 2-5 7-11 13-17 19-1 0-2 1-4 1h0l12-14c2-1 3-3 5-4 1-2 3-4 4-5 2-2 4-4 5-6l2-3z" class="Y"></path><path d="M446 249h1 1l-7 10c0 1-1 2-2 2h0c1-2 1-2 2-3v-1l3-3-6 6-3 3c1-2 3-4 4-5 2-2 4-4 5-6l2-3z" class="l"></path><path d="M486 324c0 1 0 1 1 1 2-2 5-5 8-7 1 0 3-1 5-1h1 0s-2 1-2 2h1 0c-7 6-13 11-15 19 0 1-1 2-1 3l-1-1v-2s-1-1-1-2c-1-3 0-7 2-9 1-1 1-2 2-3z" class="G"></path><path d="M499 319h1 0c-7 6-13 11-15 19h-1v-1c1-8 8-14 15-18z" class="U"></path><path d="M478 466l1-1v1c0 1-1 2-2 3v1c0 1-1 2-1 2v2h1v1h0c-1 0-1 1-2 2h-1s0 1-1 1c0 0-1 0-1-1-3 1-5 4-7 6-1 1-3 2-4 3-2 2-3 4-6 4 2-3 5-7 8-11 4-3 8-6 11-11l3-3s0 1 1 1z" class="P"></path><path d="M478 466l1-1v1c0 1-1 2-2 3v1c0 1-1 2-1 2v2h1v1h0c-1 0-1 1-2 2h-1s0 1-1 1c0 0-1 0-1-1v-1h-1-1 0v-1l1-1c3-2 4-5 7-8z" class="I"></path><path d="M470 475h1 2c1 0 2-2 2-3h1v2h1v1h0c-1 0-1 1-2 2h-1s0 1-1 1c0 0-1 0-1-1v-1h-1-1 0v-1z" class="c"></path><defs><linearGradient id="By" x1="509.199" y1="398.303" x2="517.223" y2="408.23" xlink:href="#B"><stop offset="0" stop-color="#6f7070"></stop><stop offset="1" stop-color="#a9a7a7"></stop></linearGradient></defs><path fill="url(#By)" d="M503 382c4 3 6 8 9 13 2 3 4 6 5 10v9h-1v-1-1-1c1 0 1-1 0-1h-1v8c-1 1-1 2-2 2-1-1 0-11-1-13 0-2-1-4-1-6v-3c-2-5-8-11-13-14 2 0 2 0 3 1 1 0 1 0 1 1h1 1c1 2 2 3 3 4s1 2 2 3h0l-2-4c-1-2-3-4-5-6l1-1z"></path><path d="M462 287c1 0 1 0 1 1l-1 2 3-1-4 11c-1 4-3 7-3 11v2-1h-1 0v-1h0c0 2 0 4-1 6v2c-1-3 0-7 0-11v-1h-1 0c0 1 0 1-1 1 0 1 0 2-1 3v-1-2c1 0 1-1 1-1v-3s0-1 1-2v-1-1h0v-1c0 1 0 1-1 2 0 1 0 1-1 2l1-2h0l2-5c2-3 4-7 6-9z" class="C"></path><path d="M454 304c1-1 2-3 2-4v-1c2-1 3-3 5-5-2 5-4 9-5 14v-1h-1 0c0 1 0 1-1 1 0 1 0 2-1 3v-1-2c1 0 1-1 1-1v-3z" class="M"></path><path d="M462 287c1 0 1 0 1 1l-1 2-1 4c-2 2-3 4-5 5v1c0 1-1 3-2 4 0 0 0-1 1-2v-1-1h0v-1c0 1 0 1-1 2 0 1 0 1-1 2l1-2h0l2-5c2-3 4-7 6-9z" class="Q"></path><path d="M492 315c4-1 7-4 12-5 1 0 1-1 2-1v1s0 1 1 0h2-1v1h1c-1 1-3 1-4 3-1 1-2 2-4 3h0-1c-2 0-4 1-5 1-3 2-6 5-8 7-1 0-1 0-1-1l3-4c-3 1-5 4-7 6 0 1-2 4-3 4 0 0 0-1 1-2 1-4 4-9 8-10 1-1 2-2 4-3z" class="V"></path><path d="M484 377h1c2 1 4 3 6 4s5 4 7 3c5 3 11 9 13 14v3c-1-2-3-5-5-7 1 3 3 7 3 10h-1c-1-3-3-7-6-9-1-1-1-2-3-2 0 0 0-1-1-1-1-1-1-2-2-2-2-2-4-3-5-5v-1l-7-7z" class="P"></path><path d="M492 334v-1c-1 0-3 2-4 3l3-6 2-2-1-1c1 0 2-1 3-2l1-1 2-2h1c1 0 3-1 5-2h0 1l1-1h3c0-1 1-1 2-1v1c-3 1-7 2-10 3l-1 1-1 1 12-3c2-1 4-1 6-1l-11 4-6 2c0 1-1 1 0 2l1 1c1-1 3-1 5-1h2v1 2h0 0c-1 0-3 1-4 1-1-1-1-1-3-1v1c0-1-1-1-2-1l-1 1c-1 0-2 2-4 3l1 1-4 2 1-2v-2z" class="Q"></path><path d="M501 329c-1 1-3 1-4 1-1-1-1-1-2-1h0l5-3c0 1-1 1 0 2l1 1z" class="B"></path><path d="M492 336c1-2 5-5 6-6 1 0 1 1 1 1l-1 1c-1 0-2 2-4 3l1 1-4 2 1-2z" class="j"></path><defs><linearGradient id="Bz" x1="468.267" y1="390.761" x2="464.52" y2="401.41" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#363535"></stop></linearGradient></defs><path fill="url(#Bz)" d="M466 389h1c0 1 0 1 1 1v1c-1 0-2 1-3 1v1h1 3l2-1v3l1 1v1l1-1h2l-1 1c-1 2-2 4-2 6 0 0 1 0 1 1-1 0-2 1-3 2-2 1-3 1-5 2l1-1-3-3v-1l2-1h0l-1-1h-2-1c0-1 0-1 1-2l-1-1c-1-1-1-1-2-1v1l-2-2c-1-2-2-3-4-4 0-1 0-1-1-1 1 0 2 0 3-1h1l1 1 2 1c1 1 2 1 3 1v-1-1c1 0 2 0 3-1l1-1z"></path><path d="M473 396h2l-1 1c-1 2-2 4-2 6 0 0 1 0 1 1-1 0-2 1-3 2-1-1-1-1-2-1v-1c1-1 2-1 3-1l1-2c-1 0-2 1-3 0 1-1 2-3 3-4h0 0l1-1z" class="g"></path><path d="M463 397h1v3c1 0 1 0 2-1l1-1h1l-1 1c0 1 0 3 1 4h0v1 1c1 0 1 0 2 1-2 1-3 1-5 2l1-1-3-3v-1l2-1h1 0l-2-2h0c0-1-1-2-1-3z" class="F"></path><path d="M455 390h1l1 1 2 1c1 1 2 1 3 1 1 1 1 1 1 2h2v1c1 2-1 2 1 3-1 1-1 1-2 1v-3h-1c0 1 1 2 1 3h0l2 2h0-1 0l-1-1h-2-1c0-1 0-1 1-2l-1-1c-1-1-1-1-2-1v1l-2-2c-1-2-2-3-4-4 0-1 0-1-1-1 1 0 2 0 3-1z" class="Z"></path><path d="M456 390l1 1 2 1c1 1 2 1 3 1 1 1 1 1 1 2h2v1c1 2-1 2 1 3-1 1-1 1-2 1v-3h-1v-1c-1 1-2 1-3 1 0-2 1-2 0-3l-3-1c-1-1-1-2-1-3z" class="B"></path><path d="M447 389c2 1 3 1 4 1l1-1 3 1c-1 1-2 1-3 1 1 0 1 0 1 1 2 1 3 2 4 4l2 2v-1c1 0 1 0 2 1l1 1c-1 1-1 1-1 2h1 2l1 1h0l-2 1v1l3 3-1 1c-1 0-3 0-5-1-1 0-3-1-4-2l-4-2-2-3c-3-2-5-4-8-6l1-1 2 2v-1c0-1-1-1-2-2h1 2c0-1 0-1 1-2h-1c1 0 1 0 1-1z" class="L"></path><path d="M456 405l1-2h4-2l2 2c0 1 0 1-1 2-1 0-3-1-4-2z" class="W"></path><path d="M462 401h2l1 1h0l-2 1v1l3 3-1 1c-1 0-3 0-5-1 1-1 1-1 1-2l-2-2h2c1 0 1-1 1-1v-1z" class="N"></path><path d="M443 393l2 2c2 1 5 3 7 3 1 1 2 2 4 3v1s0 1-1 1l-5-3c-3-2-5-4-8-6l1-1z" class="T"></path><path d="M447 389c2 1 3 1 4 1l1-1 3 1c-1 1-2 1-3 1 1 0 1 0 1 1 2 1 3 2 4 4l2 2v-1c1 0 1 0 2 1l1 1c-1 1-1 1-1 2-1 0-1 0-2-1 0 0 0-1-1-2h-6c-2 0-5-2-7-3v-1c0-1-1-1-2-2h1 2c0-1 0-1 1-2h-1c1 0 1 0 1-1z" class="c"></path><path d="M445 394c0-1-1-1-2-2h1 2c0-1 0-1 1-2 2 2 7 4 8 6v1c-4 0-6-1-10-3z" class="E"></path><defs><linearGradient id="CA" x1="385.444" y1="348.553" x2="397.78" y2="379.468" xlink:href="#B"><stop offset="0" stop-color="#1b1b1a"></stop><stop offset="1" stop-color="#5c5c60"></stop></linearGradient></defs><path fill="url(#CA)" d="M393 343l1-3v3c-1 1 0 1-1 1v1 1c1 0 0 0 1-1l1 1v2c-2 3-3 6-5 9-1 5-1 13 0 18 1 3 3 7 4 9-5-3-8-7-10-13-1-5 2-10 1-14-1-1 0-2 0-4 0-3 1-8 3-12l1-1v1 1c-1 0-1 0-1 1v2c-1 0-1 1-1 1v1c0 1-1 1-1 2v1l-1 4v3c2-1 1-3 2-5 0-1 0-1 1-2h0v-2l2-2v1l2-4h1z"></path><path d="M390 347l2-4h1l-4 10c0 2-1 4-2 4h0c0-4 1-7 3-10z" class="O"></path><defs><linearGradient id="CB" x1="424.689" y1="330.829" x2="419.778" y2="350.216" xlink:href="#B"><stop offset="0" stop-color="#292728"></stop><stop offset="1" stop-color="#525454"></stop></linearGradient></defs><path fill="url(#CB)" d="M413 334l-2-8 2 2h2c1 0 2 0 3 1h3c2-1 5 0 7-1h1c0 2 0 5 1 6h0c-1 1-1 1-2 1-1 1-2 1-2 2l-1 1 2 5v4c-1 0-1 1-1 1 0 1 1 2 0 4h0-1v-1c-1-2-2-4-2-6-1-1-2-2-2-3l-1-1h-1 0l-1 1c0-1 0-2-1-2 0-1 0-2-1-3l-1-2c0-1 0-1-1-2l-1 1z"></path><path d="M425 342c0-2-1-3 0-4l2 5v4c-1 0-1 1-1 1-1-2-1-4-1-6z" class="I"></path><path d="M422 330s0-1 1-1l1 2 2 6-1 1c-1 1 0 2 0 4l-2-1c0-2 0-2 1-3v-2c-1-2-1-4-2-6z" class="O"></path><path d="M428 328h1c0 2 0 5 1 6h0c-1 1-1 1-2 1-1 1-2 1-2 2l-2-6-1-2c-1 0-1 1-1 1l-1-1h0c2-1 5 0 7-1z" class="G"></path><path d="M428 328h1c0 2 0 5 1 6h0c-1 1-1 1-2 1v-7z" class="e"></path><path d="M421 329h5c1 1 1 1 1 2l-1 1v-1h-2l-1-2c-1 0-1 1-1 1l-1-1z" class="M"></path><path d="M413 334l-2-8 2 2h2l1 1c1 0 1 0 2 1 2 3 2 6 3 9 1 2 2 4 2 6-1-1-2-2-2-3l-1-1h-1 0l-1 1c0-1 0-2-1-2 0-1 0-2-1-3l-1-2c0-1 0-1-1-2l-1 1z" class="Z"></path><path d="M419 341l-3-10c2 3 3 5 4 9 0 0 1 1 1 2l-1-1h-1 0z" class="W"></path><defs><linearGradient id="CC" x1="459.042" y1="331.878" x2="472.7" y2="313.384" xlink:href="#B"><stop offset="0" stop-color="#b8b8b8"></stop><stop offset="1" stop-color="#dcdadb"></stop></linearGradient></defs><path fill="url(#CC)" d="M461 300v1l-1 3h1v-1l1 1c-1 2-2 5-1 8 1 2 0 3 1 4 1 3 1 6 2 8 1 6 5 12 9 17 1 2 3 3 4 4-2 0-4-2-7-4-2-1-5-3-7-5s-3-5-4-7c-2-3-3-7-3-10v-2c1-2 1-4 1-6h0v1h0 1v1-2c0-4 2-7 3-11z"></path><path d="M504 280h1c2 0 4 0 7 1l-12 3h-1c-5 1-13 5-16 8h0l-2 1c-1 2-3 5-4 7-1 1-1 2-2 3v1c1 1 0 2 0 3-1 1-1 1-1 2-1 3-2 6-4 8l-1 1v2h-1c0-3 0-6 1-9 3-12 9-20 19-27 3-2 6-3 10-3l4-1h2z" class="I"></path><path d="M488 284c3-2 6-3 10-3-2 1-6 4-8 4-1 0 0 0-1-1h-1z" class="U"></path><path d="M504 280h1c2 0 4 0 7 1l-12 3v-1l8-3h-6 2z" class="X"></path><path d="M474 302c2-4 5-7 8-10h1l-2 1c-1 2-3 5-4 7-1 1-1 2-2 3v1c1 1 0 2 0 3 0-1-1-3 0-4v-1h-1z" class="Q"></path><path d="M474 302h1v1c-1 1 0 3 0 4-1 1-1 1-1 2-1 3-2 6-4 8l-1 1c1-5 3-11 5-16z" class="G"></path><defs><linearGradient id="CD" x1="430.798" y1="282.413" x2="446.325" y2="281.247" xlink:href="#B"><stop offset="0" stop-color="#5b565a"></stop><stop offset="1" stop-color="#6a706c"></stop></linearGradient></defs><path fill="url(#CD)" d="M440 273h1l2-2 2-2c3-1 5-2 7-4v-1 1l2-2c1 0 1-1 2-2h0v1l-1 1-5 5v3l-9 9c0 1 1 2 0 2v1h0v1c1 1 1 2 2 3-1 2-5 5-5 7-1 0-1 1-2 1h-1 0l-1-1-1 1-3 5-3 6-1-1 2-7c0-4 3-10 6-13 0-1 0-1-1-2 0 0 2-2 2-3 2-2 3-5 5-7z"></path><path d="M440 273h1l2-2 2-2c3-1 5-2 7-4v-1 1l2-2c1 0 1-1 2-2h0v1l-1 1-5 5-16 17c0-1 0-1-1-2 0 0 2-2 2-3 2-2 3-5 5-7z" class="G"></path><path d="M430 300c-1 0-1-1-1-1 1-3 2-5 4-8 1-2 1-3 3-5 1-2 3-4 5-6 0 1 1 2 0 2v1h0v1c1 1 1 2 2 3-1 2-5 5-5 7-1 0-1 1-2 1h-1 0l-1-1-1 1-3 5z" class="J"></path><path d="M441 284c1 1 1 2 2 3-1 2-5 5-5 7-1 0-1 1-2 1h-1 0l-1-1-1 1c2-4 5-8 8-11z" class="d"></path><path d="M509 404c0-3-2-7-3-10 2 2 4 5 5 7 0 2 1 4 1 6 1 2 0 12 1 13 1 0 1-1 2-2v-8h1c1 0 1 1 0 1v1 1 1 1 1c0 1 0 3-1 3v1c0 1 1 2 0 3v1h0v1 3h0c1 1 1 1 1 2l-7 14c-1 0-1 1-1 1v1c-4 1-6 4-8 6h0-1 0s1-1 1-2h-1c0-1 5-9 6-11 5-8 6-15 5-24 0-4 0-7-1-11z" class="C"></path><defs><linearGradient id="CE" x1="484.329" y1="378.103" x2="480.592" y2="384.557" xlink:href="#B"><stop offset="0" stop-color="#969597"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#CE)" d="M456 361v-1c4 0 7 5 12 6h1l5 5c1 1 3 2 4 3 4 3 9 7 13 11 1 2 3 3 5 5 1 0 1 1 2 2 1 0 1 1 1 1-13-2-22-10-30-19l-2-2-2-2c-2-1-4-4-5-5-1 0-2-2-3-3l-1-1z"></path><path d="M465 370c1 0 2 1 3 1 0 1-1 1-1 1l-2-2z" class="E"></path><path d="M471 371h3c1 1 3 2 4 3-2 0-2 0-3-1h0-1c0 1 1 1 2 2h0l-5-4z" class="H"></path><path d="M456 361v-1c4 0 7 5 12 6h1l5 5h-3c-1-1-3-3-5-3s-4-3-5-4l-1 1c-1 0-2-2-3-3l-1-1z" class="R"></path><path d="M517 405c1 1 3 3 3 4l1 2c1 2 1 3 2 4 0 1 1 1 1 2h0v1 1s-1 1-1 2l1-1h1c1 2-1 7-1 9-2 4-3 6-6 9-1 2-3 3-5 4-1 3-3 5-5 6 0 1-1 2-2 2h-1l3-4v-1s0-1 1-1l7-14c0-1 0-1-1-2h0v-3-1h0v-1c1-1 0-2 0-3v-1c1 0 1-2 1-3v-1-1h1v-9z" class="M"></path><path d="M516 430c1-2 1-3 2-5 0-1 0-3 1-4 1 0 2 1 3 1h0v1c-1 2-2 6-3 9-2 5-6 10-11 13 0 0 0-1 1-1l7-14z" class="L"></path><path d="M517 405c1 1 3 3 3 4l1 2c1 2 1 3 2 4v1 4c-1 0-1 1-1 2h0c-1 0-2-1-3-1-1 1-1 3-1 4-1 2-1 3-2 5 0-1 0-1-1-2h0v-3-1h0v-1c1-1 0-2 0-3v-1c1 0 1-2 1-3v-1-1h1v-9z" class="J"></path><path d="M521 411c1 2 1 3 2 4v1h-3c0-2 0-3 1-5z" class="L"></path><path d="M517 405c1 1 3 3 3 4h-1c-1 2-2 12-3 12 0-2 1-4 1-7v-9z" class="N"></path><defs><linearGradient id="CF" x1="526.396" y1="331.817" x2="535.218" y2="340.339" xlink:href="#B"><stop offset="0" stop-color="#555454"></stop><stop offset="1" stop-color="#6d6d6e"></stop></linearGradient></defs><path fill="url(#CF)" d="M535 331c3-1 5-1 7-1l1 1c0 1-1 1 0 2v1l1 1c0 1 0 2 1 3-3 0-10 2-12 4l-2 2-1 1 1-2-1-1-5 1c-2-1-4 0-5-1v-1l3-1h2-5l-11-1h-4l-2-1c2 0 3-1 5-2v1l2-1 1-1h3l8-2c3 0 5-1 8-2 1 1 1 1 2 1l3-1z"></path><defs><linearGradient id="CG" x1="531.52" y1="331.225" x2="540.989" y2="332.556" xlink:href="#B"><stop offset="0" stop-color="#6d6c6c"></stop><stop offset="1" stop-color="#828182"></stop></linearGradient></defs><path fill="url(#CG)" d="M535 331c3-1 5-1 7-1l1 1c0 1-1 1 0 2h-4c-3 0-6 2-9 1l2-2 3-1z"></path><path d="M543 334l1 1c0 1 0 2 1 3-3 0-10 2-12 4l-2 2-1 1 1-2-1-1-5 1c-2-1-4 0-5-1v-1l3-1h2c6 0 12-3 18-5v-1z" class="J"></path><path d="M522 333c3 0 5-1 8-2 1 1 1 1 2 1l-2 2-4 1h1c-2 2-17 5-20 4l-1-1c1 0 2 0 3-1h-1l2-1 1-1h3l8-2z" class="V"></path><path d="M511 335h3v1l-5 1h-1l2-1 1-1z" class="b"></path><defs><linearGradient id="CH" x1="514.703" y1="331.446" x2="528.927" y2="335.42" xlink:href="#B"><stop offset="0" stop-color="#6a6b6a"></stop><stop offset="1" stop-color="#878586"></stop></linearGradient></defs><path fill="url(#CH)" d="M522 333c3 0 5-1 8-2 1 1 1 1 2 1l-2 2-4 1-12 1v-1l8-2z"></path><path d="M528 302l4-2 1 1c1 0 1 0 2 1 0 1 1 2 1 3 1 2 1 3 2 5h-1 0-1l-2 2v-2c-1 0-3 0-4 1h-1c-2 1-5 2-7 3-2 0-4 1-6 1l-1-1v1h-2c-2 0-3 1-5 1h-1l-7 3h0 0-1c0-1 2-2 2-2 2-1 3-2 4-3 1-2 3-2 4-3h-1v-1h1-2c-1 1-1 0-1 0v-1c3 0 6-2 9-3l4-2 4-1 4-1h1z" class="B"></path><path d="M519 304l4-1v1h0v1c-1 1-3 1-4 1-2 1-3 1-5 2h-2 0-1 0c1-1 2-1 4-2l4-2z" class="O"></path><path d="M527 302h1 0 4 1l-3 3-1 1 1 1s1 1 2 1h0l1 1c-1 0-1-1-1-1h-3c-2 1-4 1-5 0h-1l1-1c2 0 3-1 4-2l1-1h2v-1h-2c-2 0-4 1-6 2v-1h0v-1l4-1z" class="F"></path><path d="M528 302l4-2 1 1c1 0 1 0 2 1 0 1 1 2 1 3 1 2 1 3 2 5h-1 0l-1-1h-1-2l-1-1h0c-1 0-2-1-2-1l-1-1 1-1 3-3h-1-4 0z" class="N"></path><path d="M533 309l-1-1h0c-1 0-2-1-2-1l-1-1 1-1 1 1h3c0 1 0 1 1 2v1h-2z" class="U"></path><path d="M507 316c3-2 7-5 11-5h2 0c3-1 6-2 9-2 2 1 3 1 4 1h3l-2 2v-2c-1 0-3 0-4 1h-1c-2 1-5 2-7 3-2 0-4 1-6 1l-1-1v1h-2c-2 0-3 1-5 1h-1z" class="V"></path><path d="M508 404h1c1 4 1 7 1 11 1 9 0 16-5 24-1 2-6 10-6 11h1c0 1-1 2-1 2h0 1l-4 6-2 1v-1c-4 5-7 10-9 16v-3h0v-1c1 0 1 0 1-1h0l1-2s1-1 1-2l1-1v-1l1-1 1-1v-1c1-1 2-2 2-3l1-1v-1c-1 1 0 1-1 1-1 2-2 3-3 4l-1 2s0 1-1 1v1s-1 1-1 2c-1 0 0 0-1 1 0 0 0 1-1 1v1c-1 1-1 2-2 3 0-2 1-4 2-6 1 0 6-9 7-10h0c0-2 3-6 4-9l1-3c3-5 5-10 6-16l3-12 1 5c0-2 1-6-1-8v-2h-1 1c1-2 1-3 2-5v-2z" class="G"></path><path d="M494 458l5-8h1c0 1-1 2-1 2h0 1l-4 6-2 1v-1z" class="Y"></path><path d="M497 444c3-5 5-10 6-16l3-12 1 5v7c-2 10-9 21-15 28h0c0-2 3-6 4-9l1-3z" class="E"></path><path d="M448 244c3-3 4-6 6-9 1 2 0 3 0 5 0 1-2 3-2 4-1 1-2 2-3 4l-1 1h-1-1l-2 3c-1 2-3 4-5 6-1 1-3 3-4 5-2 1-3 3-5 4l-12 14-1-1-2 1-1-1 1-1-1-1c-1-2 0-4 0-6l1-1 2-3 1 1c1 1 0 2 0 3l6-5c0-1 2-2 3-3 2-2 4-4 5-7 1 0 1-1 2-2 0-2 1-3 3-5h0l5-5 4-5 1 1v2h0 1v1z" class="L"></path><path d="M433 260c0-1 3-3 4-3h0c1-1 2-1 3-2-2 2-3 4-5 6l-1-1c1 0 1-1 2-1l-1-1-2 3v-1z" class="J"></path><path d="M446 249c1-2 3-5 5-6l1 1c-1 1-2 2-3 4l-1 1h-1-1z" class="C"></path><path d="M417 268l1 1c1 1 0 2 0 3l-3 5h-1v-5l1-1 2-3z" class="i"></path><path d="M446 240l1 1v2h0 1v1l-2 3c-1 2-3 4-4 5s-2 2-2 3c-1 1-2 1-3 2h0c-1 0-4 2-4 3l-3 3c-1 1-6 7-7 7 0-1 1-2 1-3s2-2 3-3c2-2 4-4 5-7 1 0 1-1 2-2 0-2 1-3 3-5h0l5-5 4-5z" class="C"></path><path d="M448 243v1l-2 3c-1 2-3 4-4 5h-1c2-3 3-8 6-9h0 1zm-6 2c0 4-3 6-5 9-2 2-4 4-5 6-1 1-2 2-2 3h0c-1 1-6 7-7 7 0-1 1-2 1-3s2-2 3-3c2-2 4-4 5-7 1 0 1-1 2-2 0-2 1-3 3-5h0l5-5z" class="J"></path><path d="M467 228c1 1 4 1 5 2h0 1l6 3h2l-1 2-4 9-2 3v-2c-1-1-1-1-1-2v1c-1 0 0 0-1 1h0l-2-1c-1 3-3 6-5 9l-3 6c-2 2-4 5-7 7v-3l1-1v-1h0c-1 1-1 2-2 2l-2 2v-1 1c-2 2-4 3-7 4l-2 2-2 2h-1l4-6c1-1 1-2 2-2 0-1 1-1 1-2 2-2 4-5 6-7 6-8 10-16 13-25 0-1 0-2 1-3z" class="g"></path><path d="M472 230h0 0c0 1-1 1-1 2v1c-2 4-4 10-8 11l7-13c0-1 1-1 2-1z" class="X"></path><path d="M472 230h1 0c0 1 1 2 1 3s0 2-1 4v1l-3 6c-1 3-3 6-5 9l-3 6c-2 2-4 5-7 7v-3l1-1 4-4c4-6 7-13 10-19 1-3 3-5 3-8l-1-1h0z" class="J"></path><path d="M473 230l6 3h2l-1 2-4 9-2 3v-2c-1-1-1-1-1-2v1c-1 0 0 0-1 1h0l-2-1 3-6v-1c1-2 1-3 1-4s-1-2-1-3h0z" class="f"></path><path d="M479 233h2l-1 2-4 9-2 3v-2c1-2 2-4 3-7 1-2 1-3 2-5z" class="C"></path><path d="M467 228c1 1 4 1 5 2-1 0-2 0-2 1l-7 13c-4 7-9 14-15 20h0l-1-1c2-2 4-5 6-7 6-8 10-16 13-25 0-1 0-2 1-3z" class="Y"></path><path d="M413 272l1-1h1l-1 1c0 2-1 4 0 6l1 1-1 1 1 1v1c-1 1-1 2-1 3v3 1c0 1-1 2-2 3v2 6c1 7 3 14 9 18 4 3 7 4 11 5-2 2-5 3-8 3h-1c-6 0-9 0-13-4l-2-2v-2c0-2-1-4-2-7v-12c0-3 0-6 1-8 0-1 0-1-1-2v-1c0-1 0-2 1-3 0-1 1-2 1-3 0-2 1-2 1-4l1-2h0c1-1 1-2 2-3v-1h1z" class="a"></path><path d="M413 272l1-1h1l-1 1c0 2-1 4 0 6l1 1-1 1 1 1v1c-1 1-1 2-1 3v3 1c0 1-1 2-2 3v2 6c-1-4 0-9 0-13 0-5 0-10 1-15z" class="Z"></path><path d="M410 276v1 3c-1 3 0 8-1 12v4c1 1 0 3 0 4 0 3 2 6 3 9 0 2-2 6-1 8s5 4 6 5c-2 0-4-1-6-2 3 3 5 4 8 5 1 0 1 0 2 1h3-1c-6 0-9 0-13-4l-2-2v-2c0-2-1-4-2-7v-12c0-3 0-6 1-8 0-1 0-1-1-2v-1c0-1 0-2 1-3 0-1 1-2 1-3 0-2 1-2 1-4l1-2z" class="L"></path><path d="M408 282h1c0 2-1 4-1 6l-1 3c0-1 0-1-1-2v-1c0-1 0-2 1-3 0-1 1-2 1-3z" class="B"></path><path d="M407 291l1-3c0 5-1 8 0 12 1 2 2 4 2 6-1 1-2 0-2 1s0 5 1 6c0 1 1 2 1 3v6l-2-2v-2c0-2-1-4-2-7v-12c0-3 0-6 1-8z" class="c"></path><path d="M479 359h1l-1-1 1-1h1c1 0 1 0 1-1h-1c1 0 2 0 2 1l1-1-1-1 1-1s3 2 3 3c1 1 6 3 6 4 4 2 7 3 11 4l4 1 4 1h8v2c1 0 1 0 2 1 1-1 1-1 2-1v1l-2 2h1l-1 1c-2 0-4 1-6 2l-2 2c-2-1-6 0-8 0h-3-1c-2-2-5-2-8-3s-5-2-8-3l-1-1c-1-1-1-1-1-2-1-1-4-2-5-3s-5-4-6-6l1-1 1 1c1 0 2 0 3-1l1 1z" class="C"></path><path d="M522 370c1-1 1-1 2-1v1l-2 2c-1 0-1 0-2 1v-2c0-1 1-1 2-1z" class="f"></path><path d="M478 358l1 1c1 1 4 3 5 4h0c-1 0-1 1-2 1-2 0-5-3-7-5 1 0 2 0 3-1z" class="E"></path><path d="M493 361c4 2 7 3 11 4l4 1 4 1h-2c-4 1-8 0-11 0-2 0-4 1-6 0-2 0-3-1-5-2h1 0c2-1 6 0 8 0-1-1-3-2-4-3v-1z" class="K"></path><path d="M479 359h1l-1-1 1-1h1c1 0 1 0 1-1h-1c1 0 2 0 2 1l1-1-1-1 1-1s3 2 3 3c1 1 6 3 6 4v1c1 1 3 2 4 3-2 0-6-1-8 0h0-1l-4-2h0c-1-1-4-3-5-4z" class="D"></path><path d="M479 359h1l-1-1 1-1h1c1 0 1 0 1-1h-1c1 0 2 0 2 1l1-1-1-1 1-1s3 2 3 3 2 2 2 3h-1c-1 0-1 0-2 1l-3-1h0l2 2-1 1c-1-1-4-3-5-4z" class="B"></path><path d="M399 275c1 1 1 2 2 2v1c-1 1-1 2-1 3-1 2-1 4-2 6l-2 3h0v2 1h0l1-1 1 1 1-1v-1c1 0 1 1 1 2h1v2c0 1-1 2-2 3s-1 3-2 5v1h1c1 1 0 3-1 4l-1 1h2l-3 6-5 8-2 1h-3-1c-1 0-1 0-2-1v2h-1v-1-2c-1-1-1-1-1-2v-2c0-1 0-2 1-3v-2-6l2-6c1-1 2-2 3-2h0l1-2 1-1 1-3h0l3-7c1-1 2-1 3-2l2-6v-2-1h2z" class="D"></path><path d="M397 304h1c1 1 0 3-1 4l-1 1c-1 2-1 4-3 5 1-3 3-7 4-10z" class="J"></path><path d="M385 318c1 2 0 4 0 6 1-2 2-4 4-6h1 1c1-1 3-1 4-3l-5 8-2 1h-3-1c-1 0-1 0-2-1l3-5z" class="F"></path><path d="M386 310h1s0 1 1 2l-3 6-3 5v2h-1v-1-2c-1-1-1-1-1-2v-2c0-1 0-2 1-3v3h1c1-2 1-4 2-6h0 1c0-1 1-1 1-2z" class="h"></path><path d="M381 315v3h1c1-2 1-4 2-6h0 1c-1 4-4 9-4 13v-1-2c-1-1-1-1-1-2v-2c0-1 0-2 1-3z" class="B"></path><path d="M399 291c1 0 1 1 1 2h1v2c0 1-1 2-2 3s-1 3-2 5c-1 1-1 3-2 4l-4 6h0c0 1-1 1-1 2-1 0-1 0-1-1-1-1-1-1-1-2h0c-1-1-1-2-1-2h-1l1-2 2-2c2-5 6-9 9-13l1-1v-1z" class="E"></path><path d="M393 306l2 1-4 6-1-1c0-2 2-5 3-6z" class="C"></path><path d="M389 306l1 1-2 4v1h0c-1-1-1-2-1-2h-1l1-2 2-2z" class="d"></path><path d="M387 308l1 1v2 1h0c-1-1-1-2-1-2h-1l1-2z" class="j"></path><path d="M399 291c1 0 1 1 1 2h1v2c0 1-1 2-2 3l-1-1s0-1-1-1l1-1h1v-1-1-1-1z" class="C"></path><path d="M398 297l1 1c-1 1-1 3-2 5-1 1-1 3-2 4l-2-1 1-2c1-1 1-2 2-3 0-1 0-1 1-2s1-1 1-2z" class="T"></path><path d="M399 292v1c-2 2-3 4-4 6s-2 4-4 7l-1 1-1-1c2-5 6-9 9-13l1-1z" class="V"></path><path d="M399 275c1 1 1 2 2 2v1c-1 1-1 2-1 3-1 2-1 4-2 6l-2 3h0v2 1h0l1-1 1 1c-3 4-7 8-9 13l-2 2-1 2c0 1-1 1-1 2h-1 0c-1 2-1 4-2 6h-1v-3-2-6l2-6c1-1 2-2 3-2h0l1-2 1-1 1-3h0l3-7c1-1 2-1 3-2l2-6v-2-1h2z" class="G"></path><path d="M392 286c1-1 2-1 3-2-1 3-3 8-6 9h0l3-7z" class="P"></path><path d="M383 301c1-1 2-2 3-2l-5 14v-6l2-6z" class="H"></path><defs><linearGradient id="CI" x1="385.374" y1="305.027" x2="396.835" y2="293.296" xlink:href="#B"><stop offset="0" stop-color="#9c9b9d"></stop><stop offset="1" stop-color="#d6d4d4"></stop></linearGradient></defs><path fill="url(#CI)" d="M396 290h0v2 1h0l1-1 1 1c-3 4-7 8-9 13l-2 2-1 2c0 1-1 1-1 2h-1c1-3 2-5 3-7 2-6 5-11 9-15z"></path><path d="M444 318c1-1 2-1 2-2s0-2 1-3l3 3v6c-1 5-3 10-5 15s-5 10-9 14c-2 2-4 5-6 6h-1-1-1 0l-1-2-1-3h1 0c1-2 0-3 0-4 0 0 0-1 1-1v-4l-2-5 1-1c0-1 1-1 2-2 1 0 1 0 2-1h0c-1-1-1-4-1-6l1-1 2-1c1 0 3-1 4-2l4-1h0l3-2c1-1 1-2 1-3z" class="E"></path><path d="M441 336l2-4v-1c1 1 1 2 2 3h-2v3c-1 0-1 0-2 1 0 1-1 2-1 3l-1-1c1-1 1-2 2-4z" class="L"></path><path d="M439 340l1 1-5 7c-2 3-3 5-5 7l-1-2h1l1-2v1l1-1c1-2 2-3 2-4 2-2 3-5 5-7z" class="R"></path><path d="M432 326c1 0 3-1 4-2l4-1c-2 3-3 6-4 10v1l-1 3v-2h0v-1h0c-1-1 0-2 0-3l-1-1 1-3h0-1l-1 1-1-2z" class="e"></path><path d="M432 326l1 2 1-1h1 0l-1 3c-1 2-1 4-3 5l-1-1h0c-1-1-1-4-1-6l1-1 2-1z" class="F"></path><path d="M430 327v7c-1-1-1-4-1-6l1-1z" class="M"></path><path d="M434 330l1 1c0 1-1 2 0 3h0v1h0v2c-1 2-1 4-2 6 0 1 0 3 1 4h0c0 1-1 2-2 4l-1 1v-1l-1 2h-1v-1c1-1 1-3 1-5l2-5-1-1 1-2v-2h1l-2-2c2-1 2-3 3-5z" class="B"></path><path d="M433 343c0 1 0 3 1 4h0c0 1-1 2-2 4l-1 1v-1l2-8z" class="W"></path><path d="M440 331h1v-1c0-1 1-2 2-4l2-4c0-1 0-1 1-2v2c-1 2-2 5-3 7-1 1-3 6-3 7h1c-1 2-1 3-2 4-2 2-3 5-5 7h0c-1-1-1-3-1-4 1-2 1-4 2-6l1-3v-1h0 2c1-1 1-1 1-2h0v2c1-1 0-1 1-1v-1h0z" class="K"></path><path d="M436 333h0 2c1-1 1-1 1-2h0v2c1-1 0-1 1-1v-1h0c-1 3-1 5-2 8h-1c0-1-1-2-1-3v-2-1z" class="J"></path><path d="M428 335c1 0 1 0 2-1l1 1 2 2h-1v2l-1 2 1 1-2 5c0 2 0 4-1 5v1l1 2-2 2h-1 0l-1-2-1-3h1 0c1-2 0-3 0-4 0 0 0-1 1-1v-4l-2-5 1-1c0-1 1-1 2-2z" class="N"></path><path d="M426 355c1-1 1-1 1-2h0c1-1 1-1 2-1v1l1 2-2 2h-1 0l-1-2z" class="H"></path><path d="M428 340c1 0 1-1 2-1h2l-1 2 1 1-2 5c0-1 1-2 0-4h-1c-1 1 0 3-1 4h-1v-4l1-1v-2z" class="S"></path><path d="M430 339h2l-1 2-1-1v-1z" class="F"></path><path d="M428 335c1 0 1 0 2-1l1 1 2 2h-1v2h-2c-1 0-1 1-2 1v2l-1 1-2-5 1-1c0-1 1-1 2-2z" class="B"></path><path d="M428 335c1 0 1 0 2-1v2 3c-1 0-1 1-2 1v-5z" class="F"></path><path d="M430 334l1 1 2 2h-1v2h-2v-3-2z" class="U"></path><path d="M511 258c1 1 1 2 2 2v1 1c2 0 2 1 2 2v1h1s0 1 1 1 1 2 1 2l1 1c-1 1-3 1-4 1h0l-7 2c-2 0-4 0-6 1-2 0-5 0-7 1h-4c-1 0-2 2-2 2h0c1 1 3 0 4 1h3c-6 1-11 2-16 5-4 3-7 7-10 10-1 1-4 5-4 5v-1c-1 1-1 2-1 2l-1 1c0-1 0-2 1-3s1-2 1-3v-1c-2 4-2 8-4 12l-1-1v1h-1l1-3v-1l4-11c1-4 6-8 9-11l2-2 4-3c1-1 3-1 4-1 3-2 5-4 7-5l3-3c4-2 8-3 12-5h1l4-1z" class="C"></path><path d="M491 267l1-1h3c1-1 2-1 4-1 1-1 2 0 3 0-3 0-7 1-10 3-1 2-5 4-7 4l-1 1-6 3h0-2 0l4-3c1-1 3-1 4-1 3-2 5-4 7-5z" class="J"></path><path d="M511 258c1 1 1 2 2 2v1l-2 1c-3 0-6 2-9 3-1 0-2-1-3 0-2 0-3 0-4 1h-3l-1 1 3-3c4-2 8-3 12-5h1l4-1zm-37 25c5-3 10-9 15-11v1c-4 2-9 5-11 9l-1 2c0-1 1-1 1-1l2-1c-4 3-7 7-10 10-1 1-4 5-4 5v-1c-1 1-1 2-1 2l-1 1c0-1 0-2 1-3s1-2 1-3v-1l8-9z" class="E"></path><path d="M474 278h0c4 0 7-3 10-4-1 1-2 1-3 2s-3 3-5 4h0c-1 1-2 1-2 3l-8 9c-2 4-2 8-4 12l-1-1v1h-1l1-3v-1l4-11c1-4 6-8 9-11z" class="Y"></path><path d="M513 261v1c2 0 2 1 2 2v1h1s0 1 1 1 1 2 1 2l-14 3c-4 1-7 2-11 2v-1h-1 0-1c1-1 6-2 7-3-1-1-2 0-3-1h1c1-1 3-1 4-1 4-1 9-2 12-4h-1v-1l2-1z" class="f"></path><path d="M500 268v1c4 0 8-3 12-3-3 2-6 2-9 3s-7 3-10 3h-1 0-1c1-1 6-2 7-3-1-1-2 0-3-1h1c2 1 2 0 4 0z" class="T"></path><path d="M513 261v1c2 0 2 1 2 2v1c-1 0-2 1-3 1-4 0-8 3-12 3v-1c-2 0-2 1-4 0 1-1 3-1 4-1 4-1 9-2 12-4h-1v-1l2-1z" class="Y"></path><path d="M512 263l2-1v1c-1 1-3 2-4 2-4 1-7 2-10 3-2 0-2 1-4 0 1-1 3-1 4-1 4-1 9-2 12-4z" class="J"></path><path d="M538 310c1 2 1 5 2 8h0v1c0 1 0 2 1 3h0l1 5v1l-1 1h2v2l-1-1c-2 0-4 0-7 1l-3 1c-1 0-1 0-2-1-3 1-5 2-8 2l-8 2h-3l-1 1-1-1h-2v-1h-1l3-2-1-1h0v-2-1h-2c-2 0-4 0-5 1l-1-1c-1-1 0-1 0-2l6-2 11-4c-2 0-4 0-6 1l-12 3 1-1 1-1c3-1 7-2 10-3v-1l1-1h2 1c1 0 2 0 2-1h1c1 0 2-1 3-1 0-1 1 0 1 0v-1c2-1 5-2 7-3h1c1-1 3-1 4-1v2l2-2h1 0 1z" class="F"></path><path d="M537 310h0v2c-1 1-4 2-6 3 1 0 1 0 1 1-1 0-2 0-3 1-3 0-6 1-9 1l4-2c1 0 1 0 2-1 2-1 5-2 8-3l2-2h1z" class="H"></path><path d="M522 314c2-1 5-2 7-3h1c1-1 3-1 4-1v2l-8 3c-1 1-1 1-2 1h-2c-1 0-2 0-3 1l-8 2v-1l1-1h2 1c1 0 2 0 2-1h1c1 0 2-1 3-1 0-1 1 0 1 0v-1z" class="j"></path><path d="M538 310c1 2 1 5 2 8l-2 2h-1v-1h-4 0c1-1 2-1 2-2-1 1-3 1-5 1-1 1-1 0-1 0 3-1 6-2 8-4l-5 2c0-1 0-1-1-1 2-1 5-2 6-3v-2h1z" class="a"></path><path d="M529 318s0 1 1 0c2 0 4 0 5-1 0 1-1 1-2 2-4 1-9 3-13 4l-1-1h-1c-4-1-9 4-12 2l11-4c3-1 7-1 10-2h2z" class="R"></path><path d="M540 318h0v1c0 1 0 2 1 3h0 0c-1 0-2 0-3 1v-1h0c-2 0-4 0-6 1h0-3c-4 1-7 1-11 2h-1c1-1 2-1 3-2 4-1 9-3 13-4h0 4v1h1l2-2z" class="E"></path><path d="M540 318h0v1c0 1 0 2 1 3h0 0c-1 0-2 0-3 1v-1h0c-2 0-4 0-6 1h0-3 0c1-2 5-2 7-3h1 1l2-2z" class="Y"></path><path d="M540 318h0v1c0 1 0 2 1 3-1 0-1 0-2-1h-3v-1h1 1l2-2z" class="f"></path><path d="M506 324c3 2 8-3 12-2h1l1 1c-1 1-2 1-3 2h1c-1 1-3 1-5 2h-1-4-2v1h0c-2 0-4 0-5 1l-1-1c-1-1 0-1 0-2l6-2z" class="O"></path><path d="M506 328l-2-1h0c2-1 4-2 5-2v1c1 0 2 0 3 1h-4-2v1z" class="F"></path><path d="M518 322h1l1 1c-1 1-2 1-3 2h1c-1 1-3 1-5 2h-1c-1-1-2-1-3-1v-1l9-3z" class="H"></path><defs><linearGradient id="CJ" x1="510.295" y1="329.412" x2="532.407" y2="328.513" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#302f2f"></stop></linearGradient></defs><path fill="url(#CJ)" d="M532 323h0c2-1 4-1 6-1h0v1c1-1 2-1 3-1h0l1 5v1l-1 1h2v2l-1-1c-2 0-4 0-7 1l-3 1c-1 0-1 0-2-1-3 1-5 2-8 2l-8 2h-3l-1 1-1-1h-2v-1h-1l3-2-1-1h0v-2-1h-2 0v-1h2 4 1c2-1 4-1 5-2 4-1 7-1 11-2h3z"></path><path d="M512 327h1c1 1 2 1 4 1l-4 1-5 2v-2-1h-2 0v-1h2 4z" class="B"></path><path d="M506 327h2c2 0 3 0 4 1v1c-2-1-3-1-6-2z" class="S"></path><path d="M512 327h1c1 1 2 1 4 1l-4 1h-1v-1c-1-1-2-1-4-1h4z" class="U"></path><defs><linearGradient id="CK" x1="515.618" y1="324.394" x2="524.785" y2="327.729" xlink:href="#B"><stop offset="0" stop-color="#6b696b"></stop><stop offset="1" stop-color="#828382"></stop></linearGradient></defs><path fill="url(#CK)" d="M529 323h3 1c-1 1-2 1-4 2h-2l-1 1h2v1l-11 1c-2 0-3 0-4-1 2-1 4-1 5-2 4-1 7-1 11-2z"></path><path d="M519 331c2-1 5-1 7-1 0 1-1 2-2 2s-1 0-2 1l-8 2h-3l-1 1-1-1h-2v-1h-1l3-2c2-1 6-2 8-1h2z" class="X"></path><path d="M509 332c2-1 6-2 8-1-1 1-3 1-5 1-2 1-3 2-5 2h-1l3-2z" class="M"></path><path d="M519 331c2-1 5-1 7-1 0 1-1 2-2 2s-1 0-2 1l-8 2h-3c3-1 5-3 8-4z" class="S"></path><path d="M532 323h0c2-1 4-1 6-1h0v1c1-1 2-1 3-1h0l1 5v1l-1 1h2v2l-1-1c-2 0-4 0-7 1l-3 1c-1 0-1 0-2-1-3 1-5 2-8 2 1-1 1-1 2-1s2-1 2-2c2-1 4-1 7-2 2 0 3 0 5-2h0c-2 0-4 1-6 1h-4v-1h-2l1-1h2c2-1 3-1 4-2h-1z" class="Z"></path><path d="M530 331c2-1 3-1 5-1v1l-3 1c-1 0-1 0-2-1z" class="D"></path><path d="M541 327l1 1-1 1h2v2l-1-1c-2 0-4 0-7 1v-1l6-3z" class="J"></path><path d="M532 323h0c2-1 4-1 6-1h0v1c1-1 2-1 3-1h0l1 5v1l-1-1h0c0-1 0-1-1-1v-1h-2c-1 0-1-1-2-1-2 1-5 2-8 2h-2l1-1h2c2-1 3-1 4-2h-1z" class="L"></path><path d="M482 438c0-3 1-7 1-11 1-4 0-8 0-12-1-1 0-2-1-4 0-1-1-3-1-4l-1-1 3-3c1-2 2-4 3-5s0-2 1-3l3 1 1 3 1 2h1c1 1 1 2 1 3l1 1v1c0 1 0 1 1 2v1c0 1 1 3 2 4v1 1h0c1 1 0 2 0 3 1-1 1-1 0-2 0-1 1-3 0-3v-2-1c0-1-1-2-1-3s-1-2-1-3h1v1h1c1 0 1 0 1 1 1 1 1 1 2 1h1c-1-1-2-2-2-3 1 0 5 5 5 7h1v2c2 2 1 6 1 8l-1-5-3 12c-1 6-3 11-6 16l-1 3c-1 3-4 7-4 9h0c-1 1-6 10-7 10h-1c-1 0-2 2-2 3l-1-1c-1 2-4 4-5 6v-2s1-1 1-2v-1c1-1 2-2 2-3v-1l-1 1c-1 0-1-1-1-1l-3 3 1-2h0c0-1 1-1 1-2h0l-3 2c-2 2-5 6-8 7v-1c2-2 4-5 6-7 3-4 5-8 7-12 1-1 2-3 3-4l1-4c0-1 0-1-1-1l1-6z" class="Q"></path><path d="M485 401h0c1 1 1 2 1 3h0l-1-1 1 1v2c-1-1-1-3-1-5z" class="g"></path><path d="M494 412c-1-4-1-8-3-12v-1h0l1 2 3 7c0 1 1 2 0 3l-1 1z" class="V"></path><path d="M482 438h3c-1 3-2 5-3 7 0-1 0-1-1-1l1-6z" class="Z"></path><path d="M471 465h1c1-1 3-3 4-5l2-2c1-2 1-3 2-5h1v-1-1c1-1 1-2 2-3h0c0-1 0-2 1-2h1c-1 4-3 8-5 12l-7 8c-2 2-5 6-8 7v-1c2-2 4-5 6-7z" class="B"></path><path d="M485 446c0-1 1-1 1-2v-1l1-1c1-5 3-12 3-18 0-2-1-5-1-7l-2-12c1 1 2 3 2 5s1 3 2 5l1 11v4h1l1 3c0 3 0 5-1 8h0v-1c-1 1-2 1-2 2l-2 2c-2 5-4 10-7 14l-5 7-3 3 1-2h0c0-1 1-1 1-2h0l-3 2 7-8c2-4 4-8 5-12z" class="j"></path><defs><linearGradient id="CL" x1="494.202" y1="432.289" x2="490.343" y2="438.199" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#858385"></stop></linearGradient></defs><path fill="url(#CL)" d="M492 430h1l1 3c0 3 0 5-1 8h0v-1c-1 1-2 1-2 2l-2 2 3-14z"></path><defs><linearGradient id="CM" x1="497.241" y1="409.29" x2="489.299" y2="432.194" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#CM)" d="M492 401h1c1 1 1 2 1 3l1 1v1c0 1 0 1 1 2v1c0 1 1 3 2 4v1 1h0c1 1 0 2 0 3 1-1 1-1 0-2 0-1 1-3 0-3v-2-1c0-1-1-2-1-3s-1-2-1-3h1v1h1c1 0 1 0 1 1h-1v3c1 3 1 5 1 7 1 1 0 7 0 9l-1 6c0 2-1 3-2 4 0 2 0 3-1 5h0c-1-2 0-4-1-7h0l-1-3h-1v-4l-1-11c0-3-1-7 0-11 1 4 2 6 2 10h0l1-2 1-1c1-1 0-2 0-3l-3-7z"></path><path d="M498 432v-1c0 2-1 3-2 4h0v-3h2z" class="S"></path><path d="M499 416c1 1 0 7 0 9l-1 6v1l-1-1c1-4 0-7 1-10 0-1 0-1 1-1v-4z" class="B"></path><path d="M492 426h1c1 1 1 4 1 6 1 1 1 2 1 3v1l1-1h0c0 2 0 3-1 5h0c-1-2 0-4-1-7h0l-1-3h-1v-4z" class="Z"></path><path d="M489 444l2-2c0-1 1-1 2-2v1 2c0 1 0 1 1 2-1 2-2 3-3 5h1c2-2 3-4 5-6l-1 3c-1 3-4 7-4 9h0c-1 1-6 10-7 10h-1c-1 0-2 2-2 3l-1-1c-1 2-4 4-5 6v-2s1-1 1-2v-1c1-1 2-2 2-3v-1l-1 1c-1 0-1-1-1-1l5-7c3-4 5-9 7-14z" class="E"></path><path d="M493 440v1 2c0 1 0 1 1 2-1 2-2 3-3 5h0c-1-2 0-3 0-5v-1h0v-1c1 0 1 0 1-1l1-2z" class="L"></path><path d="M482 458c1 3-1 5-2 8h-1v-1l-1 1c-1 0-1-1-1-1l5-7z" class="N"></path><path d="M497 444l-1 3c-1 3-4 7-4 9h0c-1 1-6 10-7 10h-1c-1 0-2 2-2 3l-1-1c1-3 4-6 6-9 1-1 2-2 2-3 0-2 1-4 2-6h0 1c2-2 3-4 5-6z" class="P"></path><defs><linearGradient id="CN" x1="509.078" y1="417.643" x2="484.835" y2="434.405" xlink:href="#B"><stop offset="0" stop-color="#333332"></stop><stop offset="1" stop-color="#727073"></stop></linearGradient></defs><path fill="url(#CN)" d="M499 406c1 1 1 1 2 1h1c-1-1-2-2-2-3 1 0 5 5 5 7h1v2c2 2 1 6 1 8l-1-5-3 12c-1 6-3 11-6 16-2 2-3 4-5 6h-1c1-2 2-3 3-5-1-1-1-1-1-2v-2h0c1-3 1-5 1-8h0c1 3 0 5 1 7h0c1-2 1-3 1-5 1-1 2-2 2-4l1-6c0-2 1-8 0-9 0-2 0-4-1-7v-3h1z"></path><path d="M493 441h2c0 2-1 3-1 4-1-1-1-1-1-2v-2h0z" class="R"></path><path fill="#fff" d="M531 344h1c4-1 8-2 12 0 3 2 5 4 6 8 0 3 0 5-1 8l1 1h1c0 1-1 2-1 3-3 5-6 8-11 10-4 2-8 3-13 3h-6c0-2 0-2 1-3h1v-1l1-1h-1l2-2v-1c-1 0-1 0-2 1-1-1-1-1-2-1v-2h-8l-4-1-4-1c-4-1-7-2-11-4 0-1-5-3-6-4 0-1-3-3-3-3h0l1-1h1v-2l5-3c2-1 4-3 7-3 3-1 7 0 10 1h11 6c2-1 3-1 5-1l1-1z"></path><path d="M504 363l6 2c-1 1-1 1-2 1l-4-1v-2z" class="G"></path><path d="M510 365c4 1 9 1 13 0l1 1c-2 0-3 0-4 1h-8l-4-1c1 0 1 0 2-1z" class="M"></path><path d="M549 360l1 1h1c0 1-1 2-1 3-3 5-6 8-11 10h-3l4-2c5-2 8-7 9-12z" class="h"></path><path d="M523 365c2-1 5-1 6-1-2 3-3 6-6 8h-1l2-2v-1c-1 0-1 0-2 1-1-1-1-1-2-1v-2c1-1 2-1 4-1l-1-1z" class="Y"></path><path d="M522 374c5 1 10 1 14 0h3c-4 2-8 3-13 3h-6c0-2 0-2 1-3h1z" class="B"></path><defs><linearGradient id="CO" x1="493.26" y1="352.016" x2="494.008" y2="364.776" xlink:href="#B"><stop offset="0" stop-color="#0f0c0b"></stop><stop offset="1" stop-color="#3f4041"></stop></linearGradient></defs><path fill="url(#CO)" d="M484 354l1-1h1v-2c1 2 3 3 4 4 5 3 9 6 14 8v2c-4-1-7-2-11-4 0-1-5-3-6-4 0-1-3-3-3-3h0z"></path><defs><linearGradient id="CP" x1="455.356" y1="260.351" x2="470.817" y2="286.037" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#CP)" d="M481 233c0 1 1 1 2 1l2 1c2 1 3 2 5 3h0c1 1 3 2 4 4 1 0 3 1 4 1l1 2c1 0 2 2 3 2l1 2 3 3 2 2 3 4-4 1h-1c-4 2-8 3-12 5l-3 3c-2 1-4 3-7 5-1 0-3 0-4 1l-4 3-2 2c-3 3-8 7-9 11l-3 1 1-2c0-1 0-1-1-1-2 2-4 6-6 9l-2 5h0l-1 2-1 2c-2 5-2 10-2 16 0 0 1 1 0 1v-6l-3-3c-1 1-1 2-1 3s-1 1-2 2c0 1 0 2-1 3v-5h1-1v1h-1c-1 2-2 4-4 4-2 1-4 1-5 0-2 0-3-1-4-2h2-1v-3l-1 1v1l-1-1v-2-2l-1-1-1 1-1-1 3-3 1-2v-1l-2 2v1-3l3-6 3-5 1-1 1 1h0 1c1 0 1-1 2-1 0-2 4-5 5-7-1-1-1-2-2-3v-1h0v-1c1 0 0-1 0-2l9-9v-3l5-5v3c3-2 5-5 7-7l3-6c2-3 4-6 5-9l2 1h0c1-1 0-1 1-1v-1c0 1 0 1 1 2v2l2-3 4-9 1-2z"></path><path d="M490 253h1v2c0 1-1 1-1 1h-2c1-1 1-2 2-3z" class="G"></path><path d="M476 259l1-2c1 1 2 1 2 2l-2 2-1-2z" class="D"></path><path d="M484 250h0c0 1-1 2-2 4 1-1 1-2 2-2v-1c1-1 1-2 2-2l1-1c-2 4-5 8-8 11 0-1-1-1-2-2 2-1 4-2 5-4 1-1 1-2 2-3z" class="L"></path><path d="M484 250c1-2 2-4 2-6 1-1 1-3 2-4h1 0c0 3-1 6-2 8l-1 1c-1 0-1 1-2 2v1c-1 0-1 1-2 2 1-2 2-3 2-4h0z" class="E"></path><path d="M455 279v-3l1-1 3-2s1-1 2-1h1 1c-1 1-1 1-1 2-2 2-5 4-7 5z" class="j"></path><path d="M462 274h2l-2 2c-2 2-4 4-6 5-1 2-2 3-4 5v-2l-1-1c1-1 2-3 4-4s5-3 7-5z" class="M"></path><path d="M470 269c3-3 7-4 9-6l2-2c1-1 3-2 5-3v1c0-1 0-2 1-3h1c-1 1-1 1-1 3l1-1c0 1 1 1 2 1l1-1h0c0 1-1 1-1 2l-4 5-5 3c0-1 0-1 1-2v-2l-10 6h-1l-1 1h-1c1-1 1-1 1-2z" class="G"></path><path d="M482 264l5-5c2 0 2 0 3 1l-4 5-5 3c0-1 0-1 1-2v-2z" class="M"></path><path d="M481 233c0 1 1 1 2 1l2 1c2 1 3 2 5 3h0c-1 1-1 1-1 2h0-1c-1 1-1 3-2 4 0 2-1 4-2 6-1 1-1 2-2 3-1 2-3 3-5 4l-1 2c-1 0-1 1-2 1 0-2 1-4 2-6v-3h0 0c-1-2 0-3 1-5-1-1-1 0-1-1v-1l4-9 1-2z" class="J"></path><path d="M485 235c2 1 3 2 5 3-2 0-2 1-3 2h-1c0-2-1-3-1-5z" class="Y"></path><path d="M478 252l-1 1-2 5h1v-1h1l1-1h0c0-2 2-4 3-5h0v1l1 1c-1 2-3 3-5 4l-1 2c-1 0-1 1-2 1 0-2 1-4 2-6l2-2z" class="E"></path><path d="M481 233c0 1 1 1 2 1 1 3 1 4 1 6h-1c0 2-1 3-1 5l-1-4c1-1 1-2 1-4 0-1-1-2-2-2l1-2z" class="L"></path><path d="M476 251c2-3 3-7 5-10l1 4-4 7-2 2v-3h0z" class="P"></path><defs><linearGradient id="CQ" x1="478.654" y1="248.057" x2="477.938" y2="238.792" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#4a4849"></stop></linearGradient></defs><path fill="url(#CQ)" d="M480 235c1 0 2 1 2 2 0 2 0 3-1 4-2 3-3 7-5 10h0c-1-2 0-3 1-5-1-1-1 0-1-1v-1l4-9z"></path><path d="M443 287c2-3 4-5 6-6-2 2-4 5-6 8-1 2-3 4-4 6s-1 4-2 6-2 4-2 7c1 0 1 0 2-1l-2 7c0 1-1 2-1 3-1 0-2 1-2 1v1h-1-1v-3l-1 1v1l-1-1v-2-2l-1-1-1 1-1-1 3-3 1-2v-1l-2 2v1-3l3-6 3-5 1-1 1 1h0 1c1 0 1-1 2-1 0-2 4-5 5-7z" class="B"></path><path d="M432 318c0-2 0-3 1-5 0-1 1-2 1-3s0-1 1-1c0 2-1 3 0 5 0 1-1 2-1 3-1 0-2 1-2 1z" class="S"></path><path d="M432 308v1c0 2-1 4-1 6-1 1-1 2-1 4v-3l-1 1v1l-1-1v-2h1c0-1 1-2 1-2l2-5z" class="F"></path><path d="M433 295l1-1 1 1h0 1c1 0 1-1 2-1l-2 3c0 1-1 2-1 4 0 1-2 4-3 6v1l-2 5s-1 1-1 2h-1v-2l-1-1-1 1-1-1 3-3 1-2v-1l-2 2v1-3l3-6 3-5z" class="Q"></path><path d="M429 307h1c-1 1-1 3-2 4v-2l1-2z" class="M"></path><path d="M428 309v2 4-2l-1-1-1 1-1-1 3-3z" class="F"></path><path d="M438 294l-2 3h-1l-5 10h-1v-1c2-2 2-4 3-6s2-3 3-5h0 1c1 0 1-1 2-1z" class="g"></path><path d="M433 295l1-1 1 1c-1 2-2 3-3 5s-1 4-3 6l-2 2v1-3l3-6 3-5z" class="j"></path><defs><linearGradient id="CR" x1="488.197" y1="254.815" x2="499.974" y2="255.612" xlink:href="#B"><stop offset="0" stop-color="#b0aeaf"></stop><stop offset="1" stop-color="#cfcecf"></stop></linearGradient></defs><path fill="url(#CR)" d="M494 242c1 0 3 1 4 1l1 2c1 0 2 2 3 2l1 2 3 3 2 2 3 4-4 1h-1c-4 2-8 3-12 5-1 0-3 1-5 2l-1-1c-1 0-1 1-2 0l4-5c0-1 1-1 1-2 2-1 2-3 3-5 1-4 2-7 0-11z"></path><path d="M498 243l1 2c0 3-1 8-3 10v1-1h-1c2-4 3-7 3-12z" class="J"></path><path d="M494 242c1 0 3 1 4 1 0 5-1 8-3 12h-1v-2c1-4 2-7 0-11z" class="Y"></path><path d="M488 265c5-3 11-6 13-13 0-1 0-3 1-5l1 2 3 3 2 2 3 4-4 1h-1c-4 2-8 3-12 5-1 0-3 1-5 2l-1-1z" class="V"></path><path d="M503 249l3 3 2 2c-1 0-2 1-3 1l-5 2 3-8z" class="l"></path><path d="M506 252l2 2c-1 0-2 1-3 1h-1v-1l2-2z" class="i"></path><path d="M508 254l3 4-4 1h-1-6 0 0v-2l5-2c1 0 2-1 3-1z" class="G"></path><defs><linearGradient id="CS" x1="474.039" y1="263.509" x2="470.018" y2="287.292" xlink:href="#B"><stop offset="0" stop-color="#252325"></stop><stop offset="1" stop-color="#3c3c3b"></stop></linearGradient></defs><path fill="url(#CS)" d="M482 264v2c-1 1-1 1-1 2l5-3c1 1 1 0 2 0l1 1c2-1 4-2 5-2l-3 3c-2 1-4 3-7 5-1 0-3 0-4 1l-4 3-2 2c-3 3-8 7-9 11l-3 1 1-2c0-1 0-1-1-1v-3l-3 3-3 3-1-1-1 1-1 1v-2h-1l1-2-3 3c1-2 2-3 2-4 2-2 3-3 4-5 2-1 4-3 6-5l2-2v-1c3-1 4-2 6-4 0 1 0 1-1 2h1l1-1h1l10-6z"></path><path d="M464 273h1c1 0 2 0 3-1h1c2 0 3-1 4-2h0c0 1-1 2-2 2-1 1-1 2-1 3v1c-1 1-1 2-2 2v-1c-1 0-2 1-3 2h0l1-3h0c2-1 2-2 3-3-2 0-3 1-4 3v-1c-1 0-2 1-3 1l2-2v-1z" class="X"></path><path d="M475 273v1h1c1 0 2-1 3-2l1 1-4 3-2 2c-3 3-8 7-9 11l-3 1 1-2s1-2 2-2c2-5 6-9 10-13z" class="D"></path><path d="M488 265l1 1c2-1 4-2 5-2l-3 3c-2 1-4 3-7 5-1 0-3 0-4 1l-1-1c-1 1-2 2-3 2h-1v-1l6-5 5-3c1 1 1 0 2 0z" class="L"></path><path d="M462 276c1 0 2-1 3-1v1c1-2 2-3 4-3-1 1-1 2-3 3h0l-1 3h0c1-1 2-2 3-2v1l-6 6-3 3-3 3-1-1-1 1-1 1v-2h-1l1-2-3 3c1-2 2-3 2-4 2-2 3-3 4-5 2-1 4-3 6-5z" class="I"></path><path d="M459 282h1c-1 1-2 2-2 3-1 2-2 3-3 4l-1 1-1 1v-2h-1l1-2c2-2 4-4 6-5z" class="c"></path><path d="M462 276c1 0 2-1 3-1v1c1-2 2-3 4-3-1 1-1 2-3 3h0 0c-3 2-4 4-7 6h0c-2 1-4 3-6 5l-3 3c1-2 2-3 2-4 2-2 3-3 4-5 2-1 4-3 6-5z" class="U"></path><defs><linearGradient id="CT" x1="447.971" y1="284.471" x2="467.349" y2="241.515" xlink:href="#B"><stop offset="0" stop-color="#b3b3b4"></stop><stop offset="1" stop-color="#edebea"></stop></linearGradient></defs><path fill="url(#CT)" d="M470 244l2 1h0c1-1 0-1 1-1v-1c0 1 0 1 1 2v2l2-3v1c0 1 0 0 1 1-1 2-2 3-1 5h0 0v3c-1 2-2 4-2 6 1 0 1-1 2-1l1 2c-1 0-2 1-3 0h-1 0c-1 1-1 2-2 3h-2s-1 1-2 1l-4 4-1 2-1-1h-1c-2 1-2 2-4 2-5 3-10 7-14 11h-1 0v-1c1 0 0-1 0-2l9-9v-3l5-5v3c3-2 5-5 7-7l3-6c2-3 4-6 5-9z"></path><path d="M450 268l5-5v3c-1 2-3 3-5 5v-3z" class="P"></path><defs><linearGradient id="CU" x1="455.677" y1="268.906" x2="477.59" y2="250.016" xlink:href="#B"><stop offset="0" stop-color="#181618"></stop><stop offset="1" stop-color="#393939"></stop></linearGradient></defs><path fill="url(#CU)" d="M474 247l2-3v1c0 1 0 0 1 1-1 2-2 3-1 5h0 0v3c-1 2-2 4-2 6 1 0 1-1 2-1l1 2c-1 0-2 1-3 0h-1 0c-1 1-1 2-2 3h-2s-1 1-2 1l-4 4-1 2-1-1h-1c-2 1-2 2-4 2 7-7 14-15 18-25z"></path><path d="M463 269h0c0-2 3-4 5-6 3-3 5-8 8-12v3c-1 2-2 4-2 6 1 0 1-1 2-1l1 2c-1 0-2 1-3 0h-1 0c-1 1-1 2-2 3h-2s-1 1-2 1l-4 4z" class="k"></path><path d="M451 283l1 1v2c0 1-1 2-2 4l3-3-1 2h1v2l1-1 1-1 1 1 3-3 3-3v3c-2 2-4 6-6 9l-2 5h0l-1 2-1 2c-2 5-2 10-2 16 0 0 1 1 0 1v-6l-3-3c-1 1-1 2-1 3s-1 1-2 2c0 1 0 2-1 3v-5h1-1v1h-1c-1 2-2 4-4 4-2 1-4 1-5 0-2 0-3-1-4-2h2 1v-1s1-1 2-1c0-1 1-2 1-3l2-7c0-3 3-7 4-9 1-1 2-3 2-4 1-2 2-3 3-5 1-1 4-4 5-6z" class="N"></path><path d="M441 313c1-1 1-3 3-3l1 1-2 2h-2z" class="W"></path><path d="M445 304h0l2-1s0 1-1 2-2 3-3 4c0-1 1-3 2-5z" class="F"></path><path d="M448 301c1 0 2 0 3 1-1 0-1 1-1 1l-2 4v-1-1h-2c1-1 1-2 1-2v-1l1-1z" class="I"></path><path d="M453 289v2l-2 2-1 1-1 2-1 1v-1c0-1 1-2 2-3s0-2 1-2c0-1 1-1 2-2z" class="W"></path><path d="M445 311l1-2h0v5l-2 4c0 1 0 2-1 3v-5h1-1v1h-1c0-1 1-3 1-4l2-2z" class="B"></path><path d="M450 294l2 2-2 3h0l-2 2-1 1v1l-2 1h0c0-2 2-4 3-7l1-1 1-2z" class="O"></path><path d="M450 294l2 2-2 3h0 0v-1h0c0-1 0-2-1-2l1-2z" class="F"></path><path d="M454 295l1-3c1 1 1 2 1 3v1l-2 5h0l-1-1c-1 1-2 1-2 2-1-1-2-1-3-1l2-2h0l2-3 2-1z" class="U"></path><path d="M462 284v3c-2 2-4 6-6 9v-1c0-1 0-2-1-3l-1 3-2 1-2-2 1-1 2-2 1-1 1-1 1 1 3-3 3-3z" class="F"></path><path d="M454 290v3h-3l2-2 1-1z" class="S"></path><path d="M451 293h3v2l-2 1-2-2 1-1z" class="e"></path><path d="M451 302c0-1 1-1 2-2l1 1-1 2-1 2c-2 5-2 10-2 16 0 0 1 1 0 1v-6l-3-3c-1 1-1 2-1 3s-1 1-2 2l2-4c1-1 1-3 2-5v-2l2-4s0-1 1-1z" class="H"></path><path d="M451 302c0-1 1-1 2-2l1 1-1 2-1 2c0-1-1-1-1-2h-1s0-1 1-1z" class="N"></path><path d="M448 309s1-1 2 0v7l-3-3c-1 1-1 2-1 3s-1 1-2 2l2-4c1-1 1-3 2-5z" class="L"></path><path d="M451 283l1 1v2c0 1-1 2-2 4-2 2-4 5-5 8-1 1-2 4-3 6 0 2 0 4-1 6 0 1-1 2-1 3h1 2c0 1-1 3-1 4-1 2-2 4-4 4-2 1-4 1-5 0-2 0-3-1-4-2h2 1v-1s1-1 2-1c0-1 1-2 1-3l2-7c0-3 3-7 4-9 1-1 2-3 2-4 1-2 2-3 3-5 1-1 4-4 5-6z" class="I"></path><path d="M433 319c1-1 1-1 1-2v-1c1 0 1-1 2-2l2-2c0 3-1 5-3 7h0-2z" class="H"></path><path d="M440 305l1 1c0-1 1-1 1-2 0 2 0 4-1 6 0 1-1 2-1 3-1 2-3 4-4 7h-1v-1c2-2 3-4 3-7h1v-1c0-2 1-4 1-6z" class="Z"></path><path d="M446 289l1 1c0 3-4 5-3 8h1c-1 1-2 4-3 6 0 1-1 1-1 2l-1-1c1-1 1-4 2-5 0-1 1-1 1-2v-1c-1 0-1 0-2 1 1-1 2-3 2-4 1-2 2-3 3-5z" class="S"></path><path d="M451 283l1 1v2c0 1-1 2-2 4-2 2-4 5-5 8h-1c-1-3 3-5 3-8l-1-1c1-1 4-4 5-6z" class="e"></path><path d="M441 313h2c0 1-1 3-1 4-1 2-2 4-4 4-2 1-4 1-5 0v-2h2 0v1h1c1-3 3-5 4-7h1z" class="P"></path><path d="M381 115h1c-2 2-4 1-6 3 0 0-1 1-1 2h0 1c1 0 2 1 3 1h1 0l-2 3h1 1c0 2 0 2-1 3v1 1l2-2h0v1l-3 4c1 0 2 0 4 1h0c2-2 4-3 6-5h1s1 0 1-1h1c1 0 1-1 2-1 1-1 2-1 3-2 1 0 1-1 1-2l2-1 1 1c-1 1-2 2-3 2l-1 1c-1 1-1 2-1 2 0 1 0 1 1 2l1-1 1 1s0 1 1 1c-1 1-1 1-1 2h-1c0 1 0 1-1 2h0v1 1c1 0 1-1 1-2s1-2 2-3l1 1h0 2c1 0 2 1 3 1h1v1h2l2-1 2 2c3 0 5 0 9 1 1 1 1 1 2 1l3 3c2 1 3 2 4 4 3 2 5 4 7 7l3 9v2c-1 0-1 0-2 1l-1-1h-2c-1-1-1-1-2-1v1h0v1c-2-1-3-2-4-2h-1l3 3 1 1h-4-3c-5 0-11 0-16 1-2 0-3 0-5 1s-5 2-7 3h-1v1h0c0 1 1 1 0 2h0c0 1 0 1-1 2 0 0-1 1-1 2v1h16 9 3 7c20-1 39-3 57-5l56-9c1 3 1 7 1 10l2 28 2 32v74h0c-1-2-2-5-2-7-1-2-2-5-3-8-8-24-21-47-41-64-11-9-25-15-39-17-8-2-16-2-24-2l-34 1c-11 1-23 0-35 2l-3 27v52 86 100c0 20-1 39 0 59 1 9 2 18 5 26 10 27 38 45 63 57 5 2 11 5 17 7l19 7c5 1 10 2 14 4h-13-25-91-120-37c-6 0-13 0-20-1l13-4c14-5 29-11 42-18 9-6 17-12 24-20 14-14 26-31 30-50 5-20 4-41 4-61v-62l-1-123v-66c7-8 15-14 23-20h0c-5-1-9 0-13 0h-19c-18 0-37-1-55 1-24 3-47 13-64 31-10 11-18 24-24 38-2 4-4 8-5 12-1 3-2 8-4 10h0c-2-10-2-20-2-29l2-53 4-61c24 6 47 11 72 14 13 1 26 2 39 2h43c2 0 4 0 6-1-1 0-3-1-4-2-2-2-3-3-6-5 1-1 2 0 3-1 0-1 0-1-1-2h-2l-4-3c-1 0-2-1-3-2l-1-1-2-1c-1 0-1 1-2 2v1l-1-1-1-1h-1l-2-5-2-1h-3l-1-1-1-1c-1-1-3-2-4-2l-6 3v-1c2-3 4-4 7-6v-1s1-1 2-1l2-2c1-2 4-1 6-1 1 1 2 2 4 2v-1l3-3h0 1l3 1h1c1 0 1 0 2 1 1 0 1 1 2 1v-1h-1c0-2 0-3-1-4l-1-1 2-2c0 2 1 3 1 4v1c1 1 2 3 2 4s0 1 1 2c0 1 0 2 1 3h0 1c-1-1-1-1-1-2h1 0c-1-1 0-1-1-1h0v-4c-1-1-1-2-2-2h0c-1-2 0-1 0-3l-2-2v-4-1h1c-1 0-2 0-3-1h0c1 0 2 0 3 1s1 1 1 2v5h2v-2-1l-1-1v-2h1v1c0 1 1 2 1 3 0 2-1 2 0 3h1c1 1 1 3 1 4 0-1 0-3 1-5l1 1c1 1 2 1 3 2 1 0 2 2 2 2 1 1 2 2 3 2 0 1 0 2 1 2v1c0 1 1 3 2 4 2 1 5 2 7 3 5 1 8 3 11 7 1 2 3 6 5 7h1c-1-2-1-3-2-5-1 0-2-2-2-3l1-1c0 1 1 2 1 2h4c2 0 3 0 4-1l2-2v-3h0 0v-2h0c-1 2-2 3-3 5-3 0-6-1-7-3h2c2 1 4 1 6 0 1-1 1-3 1-4h2v2s1 1 1 2v1h1 1v-5c1 0 1 0 2 1h0l1-1h-1v-3c0 1 0 1 1 1v-2h0 2l1 1c1 0 2-1 3-2 0 0 1-2 1-3 1-3 3-9 6-11v1c3-4 5-7 9-10 2-2 6-4 9-6h0l1-1c2-1 5-1 8-1z"></path><path d="M331 229l6 3-1 1c0 1 0 1-1 1-2-2-3-3-4-5z" class="e"></path><path d="M344 250c0-1 1-2 1-3 1 1 2 1 2 2v1c-1 1-2 2-3 2h-1l1-2z" class="m"></path><path d="M266 156c0-1 0-1 1-3 0 2 1 3 2 4v1 3h-1l-2-5z" class="B"></path><path d="M335 229c2-1 3 0 4 1l1-1h1 2 1l-1 1c-1 0-3 2-4 2l-1-1c-1 0-2-1-3-1v-1z" class="j"></path><path d="M328 250v-2c-1-2-2-7 0-9h0c1 3 1 7 1 10h1l-2 1z" class="U"></path><path d="M387 156v3l-2 1c-2 2-3 4-6 5 1-4 6-7 8-9z" class="e"></path><path d="M333 248c-2-3-2-7-2-11 2 3 2 6 3 10 0 0 0 1-1 1z" class="f"></path><path d="M318 210c1 2 1 4 1 6 0 4 1 7 1 10 1 1 2 3 2 4-1 0-1-1-2-2-2-4-2-9-3-13 1-2 1-3 1-5z" class="I"></path><path d="M350 238l1 1v2c0 3 0 6-2 8-1 0-1 0-1-1-1-2 1-7 2-10z" class="f"></path><path d="M269 158v2c2 0 2 0 3-1 0-2 0-2 2-4v1c1 1 0 2 0 3 2 1 4 0 6 1v1c-1 0-1 1-1 1h-1l-1-2c-1 0-1 1-2 1l-2-1c-1 0-1 1-2 2v1l-1-1-1-1v-3z" class="d"></path><path d="M260 143c1-2 4-1 6-1 1 1 2 2 4 2h0c-2 0-6 0-8 1-2 0-4 1-6 2v-1s1-1 2-1l2-2z" class="F"></path><g class="Q"><path d="M390 167h0l3-3c2-1 3-2 6-3l2-2h3v1c-3 2-5 3-9 5-2 1-3 2-5 2z"></path><path d="M383 178c0 1-1 1-2 0v-1c2-4 5-8 9-10-1 2-1 2-3 3-1 3-2 5-4 8z"></path></g><path d="M334 247c1 1 3 2 4 2l-3-4c2 1 2 2 3 3l1 1h0 1 1v-2h1v2l2 1-1 2c-1 0-3 0-4-1-1 0-1 1-2 0-2 0-3-1-4-3 1 0 1-1 1-1z" class="C"></path><path d="M338 227c-3-1-8-3-11-6v-2c2 0 5 1 7 2 2 0 3 0 5 1h-3-1l-1 1h1c1 0 1 1 2 2l1 2z" class="T"></path><path d="M276 149c1 0 1-1 2-1h0 1l1 1h1l1 1c-1 1-1 3-1 4l-1 4h0-6c2-2 4-3 4-5h-2-1c0-1 0-2 1-3v-1z" class="h"></path><path d="M276 150h1l1 1-2 2h-1c0-1 0-2 1-3z" class="d"></path><path d="M277 156c1 0 1-1 2-1 0-1 1-1 2-1l-1 4c-1 0-2-1-4-1h0l1-1z" class="Q"></path><path d="M279 148l1 1h1l1 1c-1 1-1 3-1 4-1 0-2 0-2 1-1 0-1 1-2 1 0 0 1-2 1-3l1-5z" class="G"></path><path d="M258 149c2 0 3-1 5 0 2 0 2 2 3 2 0 1 1 2 1 2-1 2-1 2-1 3l-2-1h-3l-1-1-1-1c-1-1-3-2-4-2l3-2z" class="O"></path><path d="M258 149c2 0 3-1 5 0 2 0 2 2 3 2l-1 2h-1l-2-2h0-1v1h-1v-1l-2-2z" class="Q"></path><path d="M318 188c2 4 4 9 5 14 1 6 0 11-1 17h0c-2-2-1-7-1-10s-1-7-1-11c-1-3-3-8-2-10z" class="K"></path><path d="M360 193h1v1 5 9 9c0 3 0 5-1 9-1 3-3 7-7 9h-2c5-3 8-6 9-12 0-2 1-4 0-6s-2-5-2-7c0-7 0-11 2-17z" class="D"></path><path d="M404 160c1 2-1 2-1 3l1 1 1 1h0c-5 0-8 1-13 4v1l-1 1v1l-3 6h1-6c2-3 3-5 4-8 2-1 2-1 3-3 2 0 3-1 5-2 4-2 6-3 9-5z" class="N"></path><path d="M394 141c2 0 2 0 3 1v1l-1 1 1 1c-1 1-1 2-2 3 0 1-1 2-2 2h0l-2 2-1-1c-3 1-5 4-7 6l-6 3-1-1c4-2 7-5 9-8 1-1 2-3 3-4l2-1c1 0 1-1 2-1l-1-1h1c1-1 2-2 2-3z" class="Q"></path><path d="M392 145l1-1 1 1s-1 1-1 2v1c0 1-1 1-2 1h-2 0c0-1 1-2 1-3 1 0 1-1 2-1z" class="j"></path><path d="M339 222c3 0 6 0 9-1s6-1 9-2c-3 5-8 7-13 8h-6l-1-2c-1-1-1-2-2-2h-1l1-1h1 3z" class="Y"></path><path d="M365 176c2-2 3-4 4-6 1 3-2 7-3 11 0 2-1 5-2 8s-1 6-1 9c-1 2-1 5-2 7v3-9-5-1h-1v-2h0l-1-2 1-1c1-5 3-8 5-12z" class="U"></path><path d="M389 178h-1l3-6v-1l1-1v-1c5-3 8-4 13-4h1c1 0 2 1 3 1-2 0-3 0-5 1s-5 2-7 3h-1v1h0c0 1 1 1 0 2h0c0 1 0 1-1 2 0 0-1 1-1 2v1h16 9-30z" class="f"></path><path d="M348 256c0-1 3-3 4-3v-1c3-3 3-7 3-12 0-1-1-2 0-4 0 0 0-1 1-1h1c-1 0-1 1-1 1-1 1 0 3 0 4 0 3 0 8-2 11-2 5-6 9-11 10-1 1-3 1-4 0-6-2-9-5-11-11l2-1c0 2 1 3 3 5l1 1h0c4 2 8 2 12 1h2z" class="B"></path><path d="M334 255c4 2 8 2 12 1h2 0c-2 2-5 3-8 3-3-1-4-2-6-4h0z" class="C"></path><path d="M281 135c0 2 1 3 1 4v1c1 1 2 3 2 4s0 1 1 2c0 1 0 2 1 3h0l1 3c-1-1-2-1-4-2h0l1 2h0c-1 1-1 1-1 2-1 1-1 1-1 2h2v2h0-4 0l1-4c0-1 0-3 1-4l-1-1h-1l-1-1h-1 0c-1 0-1 1-2 1-1-1-1-2-2-3l-1-1-3-1h0v-1l3-3h0 1l3 1h1c1 0 1 0 2 1 1 0 1 1 2 1v-1h-1c0-2 0-3-1-4l-1-1 2-2z" class="I"></path><path d="M283 149l-1-2v-1c1 0 1 0 2 1v1l-1 1z" class="R"></path><path d="M274 146h2v-1h1c1 0 1 0 2 1v1h1v2l-1-1h-1 0c-1 0-1 1-2 1-1-1-1-2-2-3z" class="Q"></path><path d="M273 140h1c0 2 0 3 1 4-1 1-1 1-2 1l-3-1h0v-1l3-3h0z" class="c"></path><path d="M284 144c0 1 0 1 1 2 0 1 0 2 1 3h0l1 3c-1-1-2-1-4-2h0l1 2h0c-1 1-1 1-1 2-1 1-1 1-1 2h2v2h0-4 0l1-4c0-1 0-3 1-4l-1-1h2l1-1v-1-1c-1 0-2 0-2-1l-1-1h0l2 1h1v-1z" class="b"></path><path d="M275 161c1 0 1-1 2-1l1 2h1s0-1 1-1v-1c6 3 14 6 18 12l3 6h-5-4-5c2 0 4 0 6-1-1 0-3-1-4-2-2-2-3-3-6-5 1-1 2 0 3-1 0-1 0-1-1-2h-2l-4-3c-1 0-2-1-3-2l-1-1z" class="M"></path><path d="M279 164c3 0 11 2 13 5h0c2 2 6 5 6 8v1h-2-4l4-1c-1-1-2-3-3-4 0-1-1-2-2-3s-4-2-6-3h-2l-4-3z" class="k"></path><path d="M285 167c2 1 5 2 6 3s2 2 2 3c1 1 2 3 3 4l-4 1h-5c2 0 4 0 6-1-1 0-3-1-4-2-2-2-3-3-6-5 1-1 2 0 3-1 0-1 0-1-1-2z" class="J"></path><defs><linearGradient id="CV" x1="379.295" y1="195.675" x2="364.826" y2="184.752" xlink:href="#B"><stop offset="0" stop-color="#3e3f3f"></stop><stop offset="1" stop-color="#717070"></stop></linearGradient></defs><path fill="url(#CV)" d="M394 156c1 0 1-1 2-1h2 0c-1 2-2 2-3 2l1 1c-2 1-4 2-5 3l-1 1c-3 2-6 6-9 8-5 9-9 17-12 27-1 4-2 9-3 13-1 6-1 11-3 16v1l1-10c0-5 0-10 1-15 0-3 1-7 2-10 2-10 6-20 12-27 3-1 4-3 6-5 2 0 3-1 5-2h1s1 0 1-1c1 0 2-1 2-1z"></path><path d="M395 157l1 1c-2 1-4 2-5 3l-1 1c-3 2-6 6-9 8h0c0-2 1-3 2-5 3-4 8-6 12-8z" class="c"></path><defs><linearGradient id="CW" x1="311.62" y1="172.005" x2="299.224" y2="178.662" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2a2b"></stop></linearGradient></defs><path fill="url(#CW)" d="M280 129c1 0 2 0 3 1s1 1 1 2v5h2v-2-1l-1-1v-2h1v1c0 1 1 2 1 3 0 2-1 2 0 3h1c1 1 1 3 1 4 3 6 7 11 11 16h0l2 2c3 1 7 5 9 8 1 3 1 8 3 11v2h1 0c0 1 0 1 1 2v1c1 1 2 2 2 4-1 2 1 7 2 10 0 4 1 8 1 11s-1 8 1 10h0c0 2 1 3 1 5 1 2 2 5 3 6l2 2-1 1h0c-2-2-4-5-4-8-1-1-1-3-2-5s0-4-1-6v2c0-1 0-1-1-2v2c0-2 0-4-1-6 0 2 0 3-1 5-1-9-4-16-6-25-4-11-10-23-21-30l-6-2h0v-2h-2c0-1 0-1 1-2 0-1 0-1 1-2h0l-1-2h0c2 1 3 1 4 2l-1-3h1c-1-1-1-1-1-2h1 0c-1-1 0-1-1-1h0v-4c-1-1-1-2-2-2h0c-1-2 0-1 0-3l-2-2v-4-1h1c-1 0-2 0-3-1h0z"></path><path d="M315 193c2 2 2 4 2 7-1 1-1 2-1 2l-1-5v-4z" class="V"></path><path d="M312 183c2 3 3 7 3 10v4c-1-2-2-5-2-8v-1c-1-1-1-3-1-5z" class="e"></path><path d="M286 149h1c-1-1-1-1-1-2h1l-1 1c1 1 3 2 4 3 3 2 4 6 7 8 1 1 3 2 4 4-2-1-3-3-5-4l-2-2c-3-1-4-4-7-5l-1-3z" class="X"></path><path d="M283 154c0-1 0-1 1-2h0l-1-2h0c2 1 3 1 4 2 3 1 4 4 7 5l2 2c2 1 3 3 5 4 5 6 9 12 11 19v1c0 2 0 4 1 5v1c0 3 1 6 2 8l1 5c1 3 2 5 2 8 0 2 0 3-1 5-1-9-4-16-6-25h1c0-4-3-9-5-13 0-1 0-2-1-4-1-4-4-7-7-10-2-2-3-3-5-4-3-3-5-4-9-5h-2z" class="F"></path><path d="M283 154h2c4 1 6 2 9 5 2 1 3 2 5 4 3 3 6 6 7 10 1 2 1 3 1 4 2 4 5 9 5 13h-1c-4-11-10-23-21-30l-6-2h0v-2h-2c0-1 0-1 1-2z" class="D"></path><path d="M283 154h2c4 1 6 2 9 5l2 2h-1c-1-1-2-2-3-2l-4-1 2 2-6-2h0v-2h-2c0-1 0-1 1-2z" class="J"></path><path d="M283 154h2l-1 1v1l4 2 2 2-6-2h0v-2h-2c0-1 0-1 1-2z" class="c"></path><path d="M397 134c0-1 1-2 2-3l1 1h0 2c1 0 2 1 3 1h1v1h2l2-1 2 2c3 0 5 0 9 1 1 1 1 1 2 1l3 3c2 1 3 2 4 4 3 2 5 4 7 7l3 9v2c-1 0-1 0-2 1l-1-1h-2c-1-1-1-1-2-1v1h0v1c-2-1-3-2-4-2h-1l3 3 1 1h-4-3c-5 0-11 0-16 1-1 0-2-1-3-1h-1 0l-1-1-1-1c0-1 2-1 1-3v-1h1c-1-1-1-1-1-2h-4l-4 1-1-1c1 0 2 0 3-2h0-2c-1 0-1 1-2 1 0 0-1 1-2 1 0 1-1 1-1 1h-1c-2 1-3 2-5 2l2-1v-3l4-4 2-2h0c1 0 2-1 2-2 1-1 1-2 2-3l-1-1 1-1 2-1c0-1-1-1 0-2l-1-1h-1v-3-2z" class="h"></path><path d="M411 153l6-3v2h-2l-1 1v2l-2-1v-1h-1z" class="g"></path><path d="M425 150c1-1 2-1 3-1 0-1-1-1-2-1l-1-1h-1v-1c2 0 6 2 7 3 2 1 4 3 4 5l2 2v-3-2l3 9h-1c-4-2-8-6-12-8l-2-2z" class="Q"></path><path d="M425 153v-1c-1-1-4-2-4-3v-1l4 2 2 2c4 2 8 6 12 8h1v2c-1 0-1 0-2 1l-1-1h-2c-1-1-1-1-2-1v1c-1 0-1 0-1-1l-6-4v-3l-1-1z" class="F"></path><path d="M426 154c4 2 8 5 11 8h-2c-1-1-1-1-2-1v1c-1 0-1 0-1-1l-6-4v-3z" class="K"></path><path d="M417 150c2 0 4 0 6 1l2 2 1 1v3l6 4c0 1 0 1 1 1h0v1c-2-1-3-2-4-2h-1-1c-1-1-2-3-3-3-2 0-3-1-5-2-1 0-1 1-2 1h-2c0-1-1-1-1-2h0 0v-2l1-1h2v-2z" class="Z"></path><path d="M423 151l2 2 1 1v3c-2-2-5-3-7-5h0c2 0 2 0 3-1h1z" class="D"></path><path d="M414 153c1 0 2 1 3 1 2 1 5 2 7 4-2 0-3-1-5-2-1 0-1 1-2 1h-2c0-1-1-1-1-2h0 0v-2z" class="d"></path><path d="M414 153c1 0 2 1 3 1-1 1-1 1-3 1h0v-2z" class="V"></path><path d="M410 133l2 2c3 0 5 0 9 1 1 1 1 1 2 1l3 3c2 1 3 2 4 4l-5-3h-2c-3 0-5 0-8 2-3 1-7 2-10 3v-2c1 0 1 0 1-1v-1c1-1 3-1 4-2h0c-1-1-3-1-4-1h-1v-1l1-1h0-2s1-1 1-2v-1h1 2l2-1z" class="Z"></path><path d="M410 140h2 1c-1 1-1 1-2 1v1c1 0 3 0 4 1-3 1-7 2-10 3v-2c1 0 1 0 1-1v-1c1-1 3-1 4-2z" class="O"></path><path d="M421 136c1 1 1 1 2 1l3 3c2 1 3 2 4 4l-5-3h-2-5l-1-1h-2v-1c1 0 1-1 2-1 2-1 3 0 4-2z" class="D"></path><path d="M421 136c1 1 1 1 2 1v1c-2 1-4-1-5 0v1c2 0 3 0 5 1h0l1 1h1-2-5l-1-1h-2v-1c1 0 1-1 2-1 2-1 3 0 4-2z" class="N"></path><path d="M410 133l2 2c3 0 5 0 9 1-1 2-2 1-4 2-1-1-1-1-2 0-3 0-7 1-9 1v-2h0-2s1-1 1-2v-1h1 2l2-1z" class="O"></path><path d="M410 133l2 2c-1 0-2 1-3 1l-3 1h-2s1-1 1-2v-1h1 2l2-1z" class="c"></path><path d="M410 133l2 2c-1 0-2 1-3 1 0-1 0-1-1-1 0 0-1 0-1-1h1l2-1z" class="N"></path><path d="M411 153h1v1l2 1h0 0c0 1 1 1 1 2h2c1 0 1-1 2-1 2 1 3 2 5 2 1 0 2 2 3 3h1l3 3 1 1h-4-3c-5 0-11 0-16 1-1 0-2-1-3-1h-1 0l-1-1-1-1c0-1 2-1 1-3v-1h1c-1-1-1-1-1-2h-4c2-1 3 0 4-1v-1h-1v-1h1c0 1 0 1 1 2 1-1 1-2 2-2h1 0c2 0 2-1 3-1z" class="k"></path><path d="M408 163h4 1c1 1 2 1 4 1s6 0 8 1c-5 0-11 0-16 1-1 0-2-1-3-1 1 0 3 1 4 0-1-1-2-1-3-1l1-1z" class="D"></path><path d="M417 157c1 0 1-1 2-1 2 1 3 2 5 2 1 0 2 2 3 3-2-1-4-2-6-2s-3 0-4 1h-1c-1 0-3 1-4 2v-1c0-1 2-2 2-2 1 0 1 0 1-1-1 0-2 1-3 1h0c2-1 3-2 5-2z" class="S"></path><path d="M414 155c0 1 1 1 1 2h2c-2 0-3 1-5 2h0l-3 3s0 1-1 1l-1 1c1 0 2 0 3 1-1 1-3 0-4 0h-1 0l1-1v-4l2-2h0l1-1c1-1 1-1 2-1v1l3-2z" class="I"></path><path d="M414 155c0 1 1 1 1 2h2c-2 0-3 1-5 2h0l-3 3v-2c1-1 2-2 2-3l3-2z" class="G"></path><path d="M411 153h1v1l2 1h0 0l-3 2v-1c-1 0-1 0-2 1l-1 1h0l-2 2v4l-1 1-1-1-1-1c0-1 2-1 1-3v-1h1c-1-1-1-1-1-2h-4c2-1 3 0 4-1v-1h-1v-1h1c0 1 0 1 1 2 1-1 1-2 2-2h1 0c2 0 2-1 3-1z" class="B"></path><path d="M417 160c1-1 2-1 4-1s4 1 6 2h1l3 3h0c-2 1-5-1-7-1h-11l4-3z" class="d"></path><path d="M397 134c0-1 1-2 2-3l1 1h0 2c1 0 2 1 3 1h1v1h-1v1c0 1-1 2-1 2h2 0l-1 1v1h1c1 0 3 0 4 1h0c-1 1-3 1-4 2v1c0 1 0 1-1 1v2l-2 1-1 1 1 1 2-1c1 1 0 2 0 3l-2 1c1 1 1 1 1 2h-1v1h1v1c-1 1-2 0-4 1l-4 1-1-1c1 0 2 0 3-2h0-2c-1 0-1 1-2 1 0 0-1 1-2 1 0 1-1 1-1 1h-1c-2 1-3 2-5 2l2-1v-3l4-4 2-2h0c1 0 2-1 2-2 1-1 1-2 2-3l-1-1 1-1 2-1c0-1-1-1 0-2l-1-1h-1v-3-2z" class="N"></path><path d="M401 141h1l-1 3-2 2v-1c0-2 0-3 2-4z" class="P"></path><path d="M401 141v-2c1 0 2 0 3 1h0c1 0 1 0 2-1 1 0 3 0 4 1h0c-1 1-3 1-4 2v-1c-1-1-2-1-3 0h-1-1z" class="R"></path><path d="M391 152l2-2v2c0 1-1 1-1 2-2 1-2 2-4 3l-1 2v-3l4-4z" class="X"></path><path d="M397 134c0-1 1-2 2-3l1 1c0 1 0 1-1 2h2c-1 1-2 1-2 1v2c0 1-1 1-1 2h0-1v-3-2z" class="M"></path><path d="M400 132h2c1 0 2 1 3 1h1v1h-1v1c0 1-1 2-1 2l-3 1h-1c0-1 1-3 1-3 0-1 1-1 2-1l-1-1h-2v-1z" class="P"></path><path d="M403 141c1-1 2-1 3 0v1 1c0 1 0 1-1 1v2l-2 1-1 1h0v-1c0-1-1-1-1-1 1-1 1-2 2-2l-1-1-1 1 1-3h1z" class="D"></path><path d="M403 141c1-1 2-1 3 0v1 1c0 1 0 1-1 1v2l-2 1v-1l1-1v-2-1c0-1 0-1-1-1z" class="W"></path><path d="M401 144l1-1 1 1c-1 0-1 1-2 2 0 0 1 0 1 1v1l-10 6c1-1 1-2 2-2 0-1 1-1 1-2l4-4 2-2z" class="K"></path><path d="M403 149l2-1c1 1 0 2 0 3l-2 1c1 1 1 1 1 2h-1v1h1v1c-1 1-2 0-4 1l-4 1-1-1c1 0 2 0 3-2h0-2c-1 0-1 1-2 1h-1c1-2 3-3 4-4l6-3z" class="H"></path><path d="M403 149l2-1c1 1 0 2 0 3l-2 1c-1 1-1 2-3 2h0l1-2c-1-1-1 1-2 1v-1h-2l6-3z" class="P"></path><path d="M381 115h1c-2 2-4 1-6 3 0 0-1 1-1 2h0 1c1 0 2 1 3 1h1 0l-2 3h1 1c0 2 0 2-1 3v1 1l2-2h0v1l-3 4c1 0 2 0 4 1h0c2-2 4-3 6-5h1s1 0 1-1h1c1 0 1-1 2-1 1-1 2-1 3-2 1 0 1-1 1-2l2-1 1 1c-1 1-2 2-3 2l-1 1c-1 1-1 2-1 2 0 1 0 1 1 2l1-1 1 1s0 1 1 1c-1 1-1 1-1 2h-1c0 1 0 1-1 2h0v1 1c1 0 1-1 1-2v2 3h1l1 1c-1 1 0 1 0 2l-2 1v-1c-1-1-1-1-3-1 0 1-1 2-2 3h-1l1 1c-1 0-1 1-2 1l-2 1c-1 1-2 3-3 4-2 3-5 6-9 8l1 1c-2 1-3 3-5 5h0s-1 2-2 2l-1 3c-1 2-2 4-4 6-2 4-4 7-5 12l-1 1 1 2h0v2c-2 6-2 10-2 17l-1-1h0v4l1 3c-1 1-2 1-3 1l-7 2c-4 1-8 1-12 0-4 0-7-2-10-2h-1c1-8 1-15-2-22l-2-7-3-6c-1-1-2-2-4-3-2-3-2-8-3-11-2-3-6-7-9-8l-2-2h0c-4-5-8-10-11-16 0-1 0-3 1-5l1 1c1 1 2 1 3 2 1 0 2 2 2 2 1 1 2 2 3 2 0 1 0 2 1 2v1c0 1 1 3 2 4 2 1 5 2 7 3 5 1 8 3 11 7 1 2 3 6 5 7h1c-1-2-1-3-2-5-1 0-2-2-2-3l1-1c0 1 1 2 1 2h4c2 0 3 0 4-1l2-2v-3h0 0v-2h0c-1 2-2 3-3 5-3 0-6-1-7-3h2c2 1 4 1 6 0 1-1 1-3 1-4h2v2s1 1 1 2v1h1 1v-5c1 0 1 0 2 1h0l1-1h-1v-3c0 1 0 1 1 1v-2h0 2l1 1c1 0 2-1 3-2 0 0 1-2 1-3 1-3 3-9 6-11v1c3-4 5-7 9-10 2-2 6-4 9-6h0l1-1c2-1 5-1 8-1z" class="i"></path><path d="M355 212v5l-7 2c1-1 3-2 3-3v-1c1 0 2 1 3 1v-1c1-1 1-2 1-3z" class="C"></path><path d="M356 205c0 2 0 5 1 8l1 3c-1 1-2 1-3 1v-5c0-3 1-5 1-7z" class="L"></path><path d="M352 192l1 1v2c0 2-1 4-1 7 0 2 1 9 0 10h-4c0-1 2-1 3-1 1-3 0-9 0-13 0-2 0-4 1-6z" class="T"></path><path d="M359 189l1 2h0v2c-2 6-2 10-2 17l-1-1h0v4c-1-3-1-6-1-8 1-6 2-10 3-16z" class="B"></path><path d="M296 150l-1-1c0-1 0-2-1-2 0-1-1-1-1-1v-1h4 0l1 1c1 1 1 1 2 1 0 1 1 3 2 4 2 1 5 2 7 3s3 2 4 3l-6-2h-2c-1-1-2-3-3-4-2 0-3-2-5-2l-1 1z" class="C"></path><path d="M353 193c1-7 4-11 7-16 0-1 1-3 2-4h0c0-1 2-4 3-4 2-1 3-2 5-2h0l-1 3c-1 2-2 4-4 6 0-1 1-1 1-2s0-1 1-1v-1c0-1 0-1 1-2h0c-2 0-5 4-6 5 0 1 0 1-1 2-1 0 0 1-1 1v1c-1 0-1 2-2 3l-2 7-3 6v-2z" class="l"></path><path d="M352 169c1-2 1-3 2-4 1 1-1 3-1 5l2-1v1c0 1 0 2 1 2v1h0c0 1-3 6-4 6h-1v2-6h0 0c0-1 0-2 1-3v-1c-1 0-1 1-1 2v2c-1 1-2 1-3 1 1-1 1-1 2-1v-2h0c-1 0-1-1-2-1h0-1l2 2c-1 1-1 1-1 2-1-1 0-1-1-2h0l-2 1c-1-1-1-3-1-4 0 1-1 4-2 5h0v-3h0c1 0 1 0 1-1h-1l-1-1h2 0l1-1c1 0 1 1 2 2 0 0 1-1 2-1s1 1 2 1c1-1 2-2 2-3z" class="a"></path><path d="M356 173l-4 4c0-3 0-5 1-7l2-1v1c0 1 0 2 1 2v1z" class="E"></path><path d="M309 154c5 1 8 3 11 7 1 2 3 6 5 7h1c1 1 1 3 2 4l3 9v3l1 6v6h0l-1-10c-2-6-3-11-7-15-1-2-3-4-4-6-3-3-4-6-7-8-1-1-2-2-4-3z" class="H"></path><path d="M289 142c0-1 0-3 1-5l1 1c1 1 2 1 3 2 1 0 2 2 2 2 1 1 2 2 3 2 0 1 0 2 1 2v1c-1 0-1 0-2-1l-1-1h0-4v1s1 0 1 1c1 0 1 1 1 2l1 1 4 3c1 2 2 3 4 4v1c3 1 10 2 11 4l2 2c1 2 2 3 3 4s1 2 1 2c-2-1-3-4-5-5-3-3-6-2-9-4-1-1-2-1-3-2l-4-1c-4-5-8-10-11-16zm35 21h1c1 1 2 2 4 3 0 1 0 2 1 3v1h0v2h1c1-2 0-3 0-5 2 0 4 1 5 0 1 0 1 1 2 2 1-1 2-1 4-1h1c0-1 0-1 1-2 0 1 1 1 1 2 1 0 1-1 2 0h1 1c1 1 1 1 2 1h0 1c0 1-1 2-2 3-1 0-1-1-2-1s-2 1-2 1c-1-1-1-2-2-2l-1 1h0-2c0-1-1 0-2 0v1h-4v-1h1 0l-1-1h0 1c-1-1-2-1-3 0h-1v2c1 1 0 1 0 2 0 2 0 5-1 6 0 1 1 4 0 4v-3l-3-9c-1-1-1-3-2-4-1-2-1-3-2-5z" class="J"></path><defs><linearGradient id="CX" x1="361.525" y1="166.274" x2="356.873" y2="183.457" xlink:href="#B"><stop offset="0" stop-color="#504f50"></stop><stop offset="1" stop-color="#797a79"></stop></linearGradient></defs><path fill="url(#CX)" d="M372 161l4-2 1 1c-2 1-3 3-5 5h0s-1 2-2 2h0c-2 0-3 1-5 2-1 0-3 3-3 4h0c-1 1-2 3-2 4-3 5-6 9-7 16l-1-1c1-5 2-10 4-14 2-5 5-10 9-14 1-2 5-3 7-3z"></path><path d="M365 166h1c0 1 0 1-1 2h-1v-1l1-1z" class="X"></path><path d="M372 161l4-2 1 1c-2 1-3 3-5 5h0l-1-1-1 1h-3c1-2 3-2 4-3l1-1z" class="V"></path><path d="M300 158l4 1c1 1 2 1 3 2 3 2 6 1 9 4 2 1 3 4 5 5 2 4 4 8 5 11l2 7h1c0 1 1 3 0 5l-1-1c0-1-1-1-1-2v-1l-1-1c0-1 0-1-1-1 0 1 1 2 1 3h0c0 1 1 1 0 1 0-3-3-7-5-10h0c-1 2 0 4 1 5 1 3 2 6 1 9l-2-7-3-6c-1-1-2-2-4-3-2-3-2-8-3-11-2-3-6-7-9-8l-2-2h0z" class="E"></path><path d="M326 181l2 7h0l-3-6s1 0 1-1z" class="F"></path><path d="M321 188v-3c-1-1-1-2-1-3l-1-1v-1l-1-2c-1-1-1-2-1-3 1 1 1 1 2 1 1 2 2 4 2 5h0c-1 2 0 4 1 5 1 3 2 6 1 9l-2-7z" class="C"></path><defs><linearGradient id="CY" x1="311.63" y1="171.917" x2="326.485" y2="174.284" xlink:href="#B"><stop offset="0" stop-color="#212121"></stop><stop offset="1" stop-color="#403e3f"></stop></linearGradient></defs><path fill="url(#CY)" d="M315 169c0-2-1-2-2-4v-1h1l2 1c2 1 3 4 5 5 2 4 4 8 5 11 0 1-1 1-1 1l-6-9c-1-2-2-3-3-3l-1-1z"></path><path d="M300 158l4 1c1 1 2 1 3 2 3 2 6 1 9 4l-2-1h-1v1c1 2 2 2 2 4l-1-1-1 1c0 5 4 9 5 13-1-1-2-2-4-3-2-3-2-8-3-11-2-3-6-7-9-8l-2-2h0z" class="V"></path><path d="M333 151h2v2s1 1 1 2v1h1 1v3c1 1 1 1 2 0h1 1c1 1 0 2 1 2s1-1 2-1h1s0 1 1 1h1v1c2 0 1-1 2 0v1c2 0 2-1 3-1h2 1l1-1h0c1 0 1 0 2-1l1 1-1 1 1 1h2l-5 7c0 1-1 1-1 2-1 0-1-1-1-2v-1l-2 1c0-2 2-4 1-5-1 1-1 2-2 4h-1 0c-1 0-1 0-2-1h-1-1c-1-1-1 0-2 0 0-1-1-1-1-2-1 1-1 1-1 2h-1c-2 0-3 0-4 1-1-1-1-2-2-2-1 1-3 0-5 0 0 2 1 3 0 5h-1v-2h0v-1c-1-1-1-2-1-3-2-1-3-2-4-3h-1c-1 0-2-2-2-3l1-1c0 1 1 2 1 2h4c2 0 3 0 4-1l2-2v-3h0 0v-2h0c-1 2-2 3-3 5-3 0-6-1-7-3h2c2 1 4 1 6 0 1-1 1-3 1-4z" class="D"></path><path d="M357 161h0c1 0 1 0 2-1l1 1-1 1 1 1h2l-5 7h0c-1-2 1-6 1-7v-1l-1 1c-1 0-1-1-1-1l1-1z" class="e"></path><path d="M332 160h0c1 0 2-1 2-1l2 1h1 0c1 0 1 1 1 2h0 2 1v1h-1 0c-1 0-1 0-1-1l-2 1v-2h-1v2h-1v-1c-2 0-3 1-4 1 0 1 0 5-1 6h0c-1-1-1-2-1-3-2-1-3-2-4-3h-1c-1 0-2-2-2-3l1-1c0 1 1 2 1 2h4c2 0 3 0 4-1z" class="g"></path><path d="M333 151h2v2s1 1 1 2v1h1 1v3c1 1 1 1 2 0h1 1c1 1 0 2 1 2s1-1 2-1h1s0 1 1 1h1v1c2 0 1-1 2 0v1c2 0 2-1 3-1h2c-1 1-3 2-5 2-1-1-3-1-4-1-1-1-1-1-2-1l-1 1v1c-1-1-1-2-1-4h-1v2h-1-2 0c0-1 0-2-1-2h0-1l-2-1s-1 1-2 1h0l2-2v-3h0 0v-2h0c-1 2-2 3-3 5-3 0-6-1-7-3h2c2 1 4 1 6 0 1-1 1-3 1-4z" class="G"></path><path d="M381 115h1c-2 2-4 1-6 3 0 0-1 1-1 2h0 1c1 0 2 1 3 1h1 0l-2 3h1 1c0 2 0 2-1 3v1 1l2-2h0v1l-3 4c1 0 2 0 4 1h0c2-2 4-3 6-5h1s1 0 1-1h1c1 0 1-1 2-1 1-1 2-1 3-2 1 0 1-1 1-2l2-1 1 1c-1 1-2 2-3 2l-1 1c-1 1-1 2-1 2 0 1 0 1 1 2l1-1 1 1s0 1 1 1c-1 1-1 1-1 2h-1c0 1 0 1-1 2h0v1 1c1 0 1-1 1-2v2 3h1l1 1c-1 1 0 1 0 2l-2 1v-1c-1-1-1-1-3-1 0 1-1 2-2 3h-1l1 1c-1 0-1 1-2 1l-2 1c-1 1-2 3-3 4-2 3-5 6-9 8l-4 2c-2 0-6 1-7 3-4 4-7 9-9 14v-2h0c1-3 2-4 3-6l1-1 1-1 2-3h-1c-2 2-4 5-6 8h0v-1c0-1 1-1 1-2l5-7h-2l-1-1 1-1-1-1c-1 1-1 1-2 1h0l-1 1h-1-2c-1 0-1 1-3 1v-1c-1-1 0 0-2 0v-1h-1c-1 0-1-1-1-1h-1c-1 0-1 1-2 1s0-1-1-2h-1-1c-1 1-1 1-2 0v-3-5c1 0 1 0 2 1h0l1-1h-1v-3c0 1 0 1 1 1v-2h0 2l1 1c1 0 2-1 3-2 0 0 1-2 1-3 1-3 3-9 6-11v1c3-4 5-7 9-10 2-2 6-4 9-6h0l1-1c2-1 5-1 8-1z" class="h"></path><path d="M357 161s1-1 2-1 1-1 2-2h2 0c1 0 2 0 3-1l-4 6h-2l-1-1 1-1-1-1c-1 1-1 1-2 1h0z" class="G"></path><path d="M377 144v-4h1c1 1 1 2 1 3-1 2-1 4-3 5s-4 2-5 1h-2v-4c1 1 1 2 3 2 0 1 1 1 2 0h0c2 0 3-2 3-3z" class="i"></path><path d="M369 145c1-1 2-3 3-3v-1c1-1 2-1 3-1h0c0 1 0 1-1 1l-2 2h0 2 0l2 1h1c0 1-1 3-3 3h0c-1 1-2 1-2 0-2 0-2-1-3-2z" class="G"></path><path d="M376 144h1c0 1-1 3-3 3h0-1v-2l1-1 1 1h1v-1z" class="j"></path><path d="M340 148c0 1 0 1 1 1v-2h0 2v4h4v1l-1 3-1-1-2-1v1c-1 1-2 1-3 1v-3h0l1-1h-1v-3z" class="B"></path><path d="M341 151c0-1 0-2 1-3v-1c0 2 0 4 1 5h1c1 1 1 2 1 2l-2-1v1c-1 1-2 1-3 1v-3h0l1-1z" class="Q"></path><path d="M377 133l1-1c1 0 2 0 4 1-1 0-1 1-2 1-2 1-4 2-6 4-1 1-2 1-3 1-2 1-3 2-4 3 1-3 3-5 5-7h1l1-3h2l1 1z" class="M"></path><path d="M374 132h2l1 1c-1 1-2 3-3 4h-1c0-1-1-1 0-2l1-3z" class="j"></path><path d="M347 151c0-2 0-3 1-5h0c1 1 0 3 1 3h1 1v4c2 2 3 2 5 2h1c1-2 2-3 4-4 1-1 1-2 2-2l-1 2s0 1-1 1c-2 2-2 5-5 5-1 0-1 1-2 1l-1-1c-1-1-2-2-3-2-1-1-1-2-1-3v-1h-2v1-1z" class="Q"></path><path d="M387 136c1 0 2-1 3-2 2-1 4-4 7-5v2c-1 2-3 4-3 7h0l2-2c1 0 1-1 1-2v2 3h1l1 1c-1 1 0 1 0 2l-2 1v-1c-1-1-1-1-3-1 0 1-1 2-2 3h-1l1 1c-1 0-1 1-2 1l-2 1 2-4c1-2 3-5 3-7v-2c-2 1-4 1-6 2z" class="g"></path><path d="M391 144s0-1 1-2c0-1 1-1 2-1 0 1-1 2-2 3h-1z" class="d"></path><path d="M343 147l1 1c1 0 2-1 3-2 0 0 1-2 1-3 1-3 3-9 6-11v1l-2 4v4l1 1 2-4h3l-1 2c-1 1-5 7-5 9s0 3 2 3l2 2h1v1h-1c-2 0-3 0-5-2v-4h-1-1c-1 0 0-2-1-3h0c-1 2-1 3-1 5h-4v-4z" class="b"></path><path d="M352 137v4l1 1c-1 2-2 4-4 6 1-4 1-7 3-11z" class="T"></path><path d="M363 136l2-3c2-2 4-4 6-4h3c0 1-1 3-1 4-1 1-1 1-1 2-2 2-4 4-5 7l-2 2c-1 1-2 4-2 5-1 0-1 1-2 2-2 1-3 2-4 4v-1h-1l-2-2c-2 0-2-1-2-3s4-8 5-9l1-2v1l3-5 2 2z" class="f"></path><path d="M361 134l2 2c-2 2-3 3-4 5h-1l-1-1 1-2v1l3-5z" class="E"></path><path d="M357 140l1 1h1c-1 2-3 6-2 8 0 1 0 1 1 2 2 0 2 0 2-1l1-1v-1c1 0 0 0 1-1l1-1 1-1 1-1c-1 1-2 4-2 5-1 0-1 1-2 2-2 1-3 2-4 4v-1h-1l-2-2c-2 0-2-1-2-3s4-8 5-9z" class="C"></path><path d="M387 136c2-1 4-1 6-2v2c0 2-2 5-3 7l-2 4c-1 1-2 3-3 4-2 3-5 6-9 8l-4 2c-2 0-6 1-7 3-4 4-7 9-9 14v-2h0c1-3 2-4 3-6l1-1 1-1 2-3h-1c-2 2-4 5-6 8h0v-1c0-1 1-1 1-2l5-7 4-6h1c3-3 8-3 12-7 2-3 3-6 4-9 1-2 3-3 4-5z" class="T"></path><path d="M385 151c-4 1-7 6-11 8h-3v-1c0-1 1-1 1-1 5-3 9-5 11-10 1-2 3-5 5-6 1 0 2 1 2 2l-2 4c-1 1-2 3-3 4z" class="i"></path><defs><linearGradient id="CZ" x1="369.115" y1="131.85" x2="363.692" y2="121.119" xlink:href="#B"><stop offset="0" stop-color="#c5c3c6"></stop><stop offset="1" stop-color="#fbfafa"></stop></linearGradient></defs><path fill="url(#CZ)" d="M381 115h1c-2 2-4 1-6 3 0 0-1 1-1 2h0 1c1 0 2 1 3 1h1 0l-2 3h1 1c0 2 0 2-1 3v1 1l2-2h0v1l-3 4-1 1-1-1h-2l-1 3h-1c0-1 0-1 1-2 0-1 1-3 1-4h-3c-2 0-4 2-6 4l-2 3-2-2-3 5v-1h-3l-2 4-1-1v-4l2-4c3-4 5-7 9-10 2-2 6-4 9-6h0l1-1c2-1 5-1 8-1z"></path><path d="M363 128v1l-3 3c0 1 0 1-1 1l1 1c1 0 0-1 1 0h0l-3 5v-1h-3c1-2 2-4 3-5 1-2 3-4 5-5z" class="k"></path><path d="M363 128c0-1 3-3 4-4 2-1 4-3 7-4l1 1c1 1 3 0 4 0h1 0l-5 2-4 2c-2 1-4 2-5 4-2 1-4 3-5 4l-1-1 3-3v-1z" class="Y"></path><path d="M375 123l5-2-2 3h1 1c0 2 0 2-1 3v1 1l2-2h0v1l-3 4-1 1-1-1h-2l-1 3h-1c0-1 0-1 1-2 0-1 1-3 1-4h-3c-2 0-4 2-6 4l-2 3-2-2h0c-1-1 0 0-1 0l-1-1c1 0 1 0 1-1l1 1c1-1 3-3 5-4 1-2 3-3 5-4l4-2z" class="W"></path><path d="M366 129c1-2 3-3 5-4v1h2c-1 1-3 1-4 2-2 1-2 1-3 1z" class="K"></path><path d="M375 123l5-2-2 3h1 1c0 2 0 2-1 3v1 1l2-2h0v1l-3 4-1 1-1-1h-2c2-2 5-5 5-7-2 1-3 1-4 1v-3z" class="G"></path></svg>