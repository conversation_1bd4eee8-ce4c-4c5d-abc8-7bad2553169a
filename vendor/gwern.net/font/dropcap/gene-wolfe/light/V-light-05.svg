<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="72 36 484 632"><!--oldViewBox="0 0 612 752"--><style>.B{fill:#fbfbfb}.C{fill:#e5e4e5}.D{fill:#404041}.E{fill:#afafaf}.F{fill:#d9d8d9}.G{fill:#8d8d8e}.H{fill:#cdcbcd}.I{fill:#121313}.J{fill:#a2a1a2}.K{fill:#7e7d7e}.L{fill:#cbcacb}.M{fill:#f0eff0}.N{fill:#3c3c3d}.O{fill:#161616}.P{fill:#5e5d5e}.Q{fill:#1b1a1b}.R{fill:#636263}.S{fill:#f6f5f6}.T{fill:#9d9c9d}.U{fill:#6f6e6f}.V{fill:#bcbbbb}.W{fill:#212122}.X{fill:#767576}.Y{fill:#272628}</style><path d="M157 222l2 4-2-1h-1v-2l1-1z" class="T"></path><path d="M423 373l1 1v1l-1 1c-1 0-1-1-1-2h-1l2-1z" class="C"></path><path d="M429 471l5-2-2 2h-3z" class="F"></path><path d="M414 364v-2c0-1 1-1 2-2l1 2-3 2z" class="M"></path><path d="M364 499l4-1c0 2-1 3-3 4 0-1-1-2-1-3z" class="C"></path><path d="M345 415v3l-1 4-1-4 2-3z" class="N"></path><path d="M300 52h1c1 0 2 1 2 2l-1 1h-2v-1-2z"></path><path d="M362 500l2-1c0 1 1 2 1 3l-3 2v-4z" class="H"></path><path d="M345 415c1-2 1-4 3-5v2c-1 2-2 5-3 6v-3z" class="D"></path><path d="M368 346c0-3 1-5 1-7h0v-1c1 1 2 2 2 3l-1 3-2 2h0z" class="I"></path><path d="M323 52l1-1c1 1 1 2 2 3v1h-2 0c-1 0-2 0-2-1s0-2 1-2z"></path><path d="M334 571l1 1-1 5-1 1h-1l2-7z" class="I"></path><path d="M315 467l3 10-3-3-1-4c1-1 1-2 1-3z" class="L"></path><path d="M190 214c2 0 4 1 6 2h-6-6l6-2z"></path><path d="M187 360c3 0 5 2 7 3v1 1h-1l-3-2c-2-1-2-2-3-3z" class="J"></path><path d="M247 457c2 0 5 0 7 1 2 0 2 2 3 3l-9-3h-1v-1zm187-243c2 0 5 1 7 2h0-4-8l5-2z"></path><path d="M533 324c3-1 5-3 6-6 1-1 1-2 2-3 0 3-2 7-4 10-1 0-1 0-2 1 0-1-1-1-2-2z" class="O"></path><path d="M452 352h0c-2 2-3 3-5 4-1 0-1 0-2 1s-2 1-4 2h0-1l-1-1c3-1 5-2 8-4 1-1 3-2 5-2z" class="J"></path><path d="M235 445c2 1 4 2 5 4 0 1 0 2-1 3h0 0c-1-2-2-4-4-5h-1 0 0v-1l1-1z" class="P"></path><path d="M389 174c2-1 5-2 7-3 1 0 2 0 3 1h-2c1 1 2 1 2 2-2-1-5 1-8 0h-2z" class="J"></path><path d="M229 445c1-1 4-1 6 0l-1 1v1h0c-3 0-5 0-7 2h0-1 0c0-2 1-2 3-3v-1z" class="G"></path><path d="M289 559l2 7h-1l-2-1c0-1-1-2-2-3v-2c2 0 2 0 3-1z" class="Q"></path><path d="M168 232c1 1 2 1 3 1l1-1c0 2 1 2 2 3s3 1 4 2c-1 0-3-1-4-1l2 2h0 0l-6-3c-1-1-1-2-2-3z" class="R"></path><path d="M260 162c1 0 2 1 2 1 0 2 0 3-1 4-2 1-3 2-5 3l-1-1v-1l2-1c2-1 3-3 3-5h0z" class="I"></path><path d="M428 472l-1 1-1-1h-3c1-1 3 0 4-1h2 3l1 1h1c2 1 3 1 3 2l1 1c-3-1-7-3-10-3z" class="C"></path><path d="M303 408v-1c0 1 1 1 1 1 0 1 0 1 1 2l1-1 1 13c-2-4-3-9-4-14z" class="J"></path><path d="M513 143c2 0 2 0 4 1 0 1 0 3 1 5h0v1h3c-1 0-2 1-3 0s-3-3-4-5h-1v-1l-1-1h1z" class="V"></path><path d="M418 200h0c1 1 1 1 1 2h2s0 1 1 1h0c0 1-1 2-1 3v3h-1c-2-3-2-6-2-9z" class="I"></path><path d="M312 606l1 1c1 0 1 0 1-1 1 1 1 2 1 3l-2 7c-1-2-1-6-2-8 1 0 1-1 1-2z" class="W"></path><path d="M279 223c4 0 8-1 12 0l-1 1c-1 0-2 0-3 1h-6l-2-2z" class="D"></path><path d="M374 455h2c0 1 1 2 1 2 1 1 1 0 2 1 0 1-1 0-3 1h0-2l-2 1v1l-1-1c1 0 1-1 1-2h0c0-1 1-2 2-3z" class="S"></path><path d="M386 452l-1-2c0-2 1-2 2-3 3-2 7-3 10-2h-3 1c-1 1-4 1-5 2h1l-1 1c-2 1-3 1-4 4z" class="N"></path><path d="M215 464l5 1c1 1 1 1 2 3h0c1 0 2 0 2 1s-1 1-2 2h-1c0-1 1-1 2-2l-1-1c-2 0-5-1-6-2s-1-1-1-2z" class="C"></path><path d="M221 180c2-1 4-3 7-3l-12 9-2-2 7-4z" class="W"></path><path d="M162 239h0v1h3c0 1-1 1-1 1-4 1-7 4-11 6 0-1 1-1 1-2h0c1-2 6-5 8-6z" class="G"></path><path d="M368 346h0l2-2c0 2-1 4-2 6s-2 3-2 5h-2c1-1 1-2 0-3l4-6z" class="O"></path><path d="M439 245l5-4c2-2 4-5 7-4-4 3-8 7-12 11v-3zm-96-11h0v5h2c3 0 6 2 9 3 0 1 1 2 1 3-4-2-8-4-12-5h0c-1-2 0-4 0-6z" class="K"></path><path d="M215 550c-2 0-4-2-6-2h0c-1-1-1-1-2-1h-1 0v-1c1 0 1 0 2 1h2c1 0 2 1 3 1v-2c2 2 5 3 7 5h-1 0v1 1c-1-1-3-3-4-3z" class="C"></path><path d="M208 460h2c1 1 2 2 2 3-5-1-10-1-15 1v-1c2-1 4-1 7-2l4-1z" class="N"></path><path d="M315 474l3 3 4 15-3-3c-1-5-3-10-4-15z" class="J"></path><path d="M356 505c0-1 1-3 1-4 1 0 1-1 2-1 1-1 2-1 4-1 3-2 7-3 10-4l-5 3-4 1-2 1c-2 2-4 3-6 5h0z" class="N"></path><path d="M349 154c1 2 1 4 1 5h-1c-1 3-3 7-6 8l-2 1 2-3c3-3 5-7 6-11z" class="L"></path><path d="M400 384c-1-1-2 0-2 0h-1 0l1-2c0-1 4-2 5-2h4l2 2c-2 0-4 0-6 1-1 0-2 1-3 1z" class="P"></path><path d="M371 487c2-2 4-3 6-4 2-2 5-4 8-5h1c-3 3-6 5-9 6-2 2-4 3-6 3z" class="E"></path><defs><linearGradient id="A" x1="203.825" y1="371.704" x2="198.949" y2="363.982" xlink:href="#B"><stop offset="0" stop-color="#605e60"></stop><stop offset="1" stop-color="#757674"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M194 363l15 7c-2 1-3 1-5 1h0c-2-1-3-2-4-2-2-1-5-3-7-4h1v-1-1z"></path><path d="M294 125v2 1c-1 0-1 1-1 2s1 3 2 4 2 2 3 1h3c-1 1-1 1 0 2h-1c-1 1-3 1-5 0-1 0-2-1-2-3-2-3-1-6 1-9z" class="W"></path><path d="M310 451l5 16c0 1 0 2-1 3l-5-16 1-3z" class="V"></path><path d="M234 473h2c2 2 4 4 6 5 2 2 6 4 7 6l1 1h-1c-1-1-3-2-4-3-4-2-9-5-11-9z" class="E"></path><path d="M245 143h1 1l1 1h1v-1l3 2h0l2 1v1h-1v2l2 2h-1c-3-2-5-5-9-6l-1-1h0-1v-1c1 1 1 1 2 1v-1z" class="C"></path><path d="M391 447h5s0 1 1 1h-1c-2 0-3 0-4 1s-1 1-1 2c-1 2 1 6 1 8-1-1-1-3-2-4s-3-2-4-3c1-3 2-3 4-4l1-1z" class="I"></path><path d="M323 170c1-1 2-1 3-1 3-2 9-2 12-3 1 0 3-1 5-1l-2 3c-1 0-3 0-5 1l-13 1z" class="U"></path><path d="M226 449h1 0c2-2 4-2 7-2h0 1c2 1 3 3 4 5h0l-1 1h-1c-1 1-2 3-3 5h0c0-2 2-6 1-8-1-1-2-2-3-2-3 0-4 1-6 3v-2zm134-89c2-4 1-11 0-15 1 1 3 2 4 4v3c1 1 1 2 0 3-1 2-2 3-3 5h-1z"></path><path d="M297 352c1 1 1 2 1 3v1 6h1 0v-1h0c0-1-1-6 0-7 0 5 1 10 1 15 0 1 0 1 1 1l-4 3v-21z" class="H"></path><path d="M340 227l-3-4c2-1 4 0 5 0 5 0 10 0 14 1h-4 0c1 0 1 0 1 1h-1-6-5l-1 2z" class="R"></path><path d="M231 178c2-1 4 0 6 0s3 1 6 0h2 1 0 2l-2 2h-2c-2 0-3 1-3 2-1 0-1 0-1-1h-1c-2-2-8-2-10-2h0l2-1z"></path><path d="M347 395c3-5 7-11 11-15 0 3-2 5-4 8l-4 7-3 4v-2c1-1 0-1 0-2z" class="Q"></path><path d="M505 160c4 4 5 8 7 13-1 2-1 4-2 5v1c-1-1 0-1-1-2v-2h-1v-1h0 0 1c0-2-1-4-1-6 0-1 0-1-1-1l-1-3c-1-2-1-2-1-4z" class="L"></path><path d="M395 445c3 3 7 7 8 11l-1 5s-1 0-1 1c-1-3 0-6-2-9-1-2-2-3-3-4v-1h1c-1 0-1-1-1-1h-5-1c1-1 4-1 5-2z" class="E"></path><path d="M413 187c-1-1-2-1-3-1-5-2-8-5-11-9 2 1 3 2 5 3 2 2 5 3 8 4 3 2 5 1 8 2 0 1 1 1 1 2h-1c-2-1-4 0-7-1zm-169-39l7 5c3 1 4 2 7 3h0l-1-4c2 2 5 5 5 8v3s-1-1-2-1h0c-1-3-2-5-5-6-1 0-2 0-3-1l-1-1c-1-1-2-2-4-3l-3-3z" class="W"></path><path d="M397 445c3 1 5 2 6 5 2 3 1 6 0 9v1l-1 1 1-5c-1-4-5-8-8-11h-1 3z" class="Q"></path><path d="M317 454c1-1 4 0 5 0h11 1l-1 2h-16v-2z"></path><path d="M287 225c1-1 2-1 3-1-1 2-2 4-3 7 0 2-1 6-2 8h-1v-1h0v-1c-1-3 1-9 3-12z" class="X"></path><path d="M423 368l1 1c2 0 3-2 5-2-2 2-4 3-6 4l-12 3c-4 1-8 3-11 5h0l1-1c4-3 9-4 13-6l9-4z" class="R"></path><path d="M299 418l8 25c-1-1-1-2-2-3l-7-20 1-1v2-2-1z" class="H"></path><path d="M264 502h-1l-1-1h1c1 0 6 4 7 5v1l1 1c1 0 1 1 1 1l1 1v3c-2-1-5-3-7-5l-3-1c0-1 1 0 1 0 0-1 0-1 1-1-1-1-1-2-1-3v-1z" class="V"></path><path d="M264 502c1 1 1 2 2 2l2 2v1h-1v1h-1l-3-1c0-1 1 0 1 0 0-1 0-1 1-1-1-1-1-2-1-3v-1z" class="C"></path><path d="M463 309v1c0 2 0 2 1 4l-5 9c-1 2-2 3-4 5v-1-2l1-1h-1c1-1 1-2 2-3 0 0 1-1 1-2 1-1 2-3 3-5 0-1 1-2 1-2v-1c1-1 1-1 1-2zm-64-137c2 0 4 2 7 3h0l1 1c1 1 1 2 2 3 1 0 1 0 2 1h1c1 1 1 1 3 2 1 0 1 0 2 1h-2-1s-1-1-1 0c-4-1-7-5-9-6s-3-2-5-3c0-1-1-1-2-2h2z" class="M"></path><path d="M362 500v4c-1 1-2 2-2 3l1 1c-1 0-2 1-3 1-1 2-3 2-5 3l1-3c0-1 1-3 2-4h0c2-2 4-3 6-5z" class="E"></path><path d="M354 509h0c2-1 3-1 4-2v1 1c-1 2-3 2-5 3l1-3z" class="G"></path><path d="M362 507h0c8-5 15-7 24-8 2-1 5-1 6-1h1 0c-2 0-5 1-7 1-3 1-7 2-10 4l-9 3-5 3c0-1 1-1 0-2z" class="N"></path><path d="M340 227l1-2h5 6-1c-2 1-5 0-7 0 1 4 3 10 1 13v1h-2v-5h0c-1-3-1-5-3-7z" class="D"></path><path d="M424 116l5 2 1 1c3 4 6 5 9 7 3 1 5 2 7 4h0l-2 1 1 1s0 1 1 2c-2-2-4-3-5-5-7-5-13-8-20-12 2 0 3 1 5 1h0c-1 0-1 0-1-1h-1v-1z" class="X"></path><path d="M276 152l1 1c-1 2 0 5 1 7 2 3 4 5 8 6h0l4 2c1 0 1 0 2 1h-2c-2 0-4 0-6-1-4-1-7-6-9-10 0-1 0-2-1-3h-1l3-3z" class="F"></path><path d="M229 445v1c-2 1-3 1-3 3h0v2c-1 3-2 5-2 8v2l-2-1v-1-1l-1-1c-1-2 0-6 1-8 2-2 4-3 7-4z" class="D"></path><path d="M238 159c0 1 0 2 1 3v3c1 1 3 2 4 2s1 0 2-1v-1c1 1 2 1 3 2 0 1 1 1 1 2v1h-1c-3 0-8-1-10-3v-1h0-1l-1-1c0-1-1-1-2-2 0-2 1-2 2-3 0 1 1 1 2 1v-2z" class="Y"></path><path d="M322 113l1 1h1c1 1 3 2 3 4 1 0 0 2 0 3l1 1c1-1 2-2 3-2h0c0 2 2 6 2 8h-1c0-1-1-3-2-4h-1l1 1c-1 1-1 2-2 3v-2c-1-3-3-6-6-8l-1-1 1-1h-1c0-1 1-2 1-3z" class="I"></path><path d="M322 113l1 1h1c0 1 0 2-1 3l-1-1h-1c0-1 1-2 1-3z" class="S"></path><path d="M284 101c1-1 2-3 4-3v-1h1c2-1 6-1 7 0h0c2 1 4 2 5 3l-1 1c-1-1-2-1-3-2l-1 1 2 2h0 0-1c-1 0-1-1-2-1s-5-1-6-1h0l-1 1h-2 0c-1 1-2 1-2 0z" class="Y"></path><path d="M234 143l6 5c1 1 2 2 3 2h1c1 0 2 1 3 1h0c2 1 3 2 4 3 0 1 0 0-1 1h0c-2 0-5-2-6-4 0 2 1 4 2 5l-11-8-2-2h1v-3z" class="D"></path><path d="M528 320h2c0 1-1 3-1 5 1-1 3-2 4-4h0l-2 3 1 1 1-1c1 1 2 1 2 2-2 2-6 2-10 3-1 0-3 1-4 2 1-2 3-4 4-6 1-1 2-4 3-5zM414 546h-1c-1 1-3 2-5 3-4 2-7 6-8 10 2-1 4-5 7-6-2 2-4 4-5 6-2 1-3 3-4 5 0 1-2 2-2 3s-1 1-2 1c1-4 4-7 4-11v-2h0s1 0 1-1c1 0 1 0 1-1l1 1 3-3c3-3 6-4 10-5z" class="I"></path><path d="M330 125l-1-1h1c1 1 2 3 2 4h1c1 2 1 5 0 7-1 1-2 2-4 3-2 0-4-1-5-1h0l1-1c-1-1 0-1 0-2 2 0 4-2 5-4 1-1 0-3 0-5z" class="O"></path><path d="M330 125l-1-1h1c1 1 2 3 2 4 0 3-1 5-3 6-1 1-2 1-4 1v-1c2 0 4-2 5-4 1-1 0-3 0-5z" class="S"></path><path d="M234 163c1 1 2 1 2 2l1 1c1 2 1 4 2 5 3 1 6 3 8 5h0-1v1 1h0-1c-2-3-7-3-10-4-1 0-2 0-3-1h3c1 0 1 0 1-1 1-1 0-1 1-1-1 0-2-1-2-1 0-1 0-2-1-3v-1h0 1l-1-3z" class="I"></path><path d="M323 236c1 1 3 1 4 1v-13c0-1-1-3-1-4 1 1 2 2 3 4v5 11c-2 0-5 0-8-2h1l-1-2h2z" class="W"></path><defs><linearGradient id="C" x1="222.47" y1="459.78" x2="215.018" y2="465.258" xlink:href="#B"><stop offset="0" stop-color="#030302"></stop><stop offset="1" stop-color="#333234"></stop></linearGradient></defs><path fill="url(#C)" d="M222 460l2 1s1 1 1 2v1h1v2c-2-1-4-1-6-1l-5-1-3-1c0-1-1-2-2-3h11 1z"></path><path d="M471 249c2 1 3 2 4 3h1v-1c-1 0-1-1-1-1l-2-2v1c-1-1-1-2-2-2l-1-1c-2-2-6-4-8-5-1-1-2 0-3 0l-1-1c1-1 3-1 4-1v-1c1 2 4 3 6 4 2 2 5 4 7 7 2 2 4 5 6 8l2 3-1 1c-1-1-2-2-2-3h-1c-3-3-5-6-8-9z" class="H"></path><path d="M342 418c1-4 4-8 6-11 1-2 1-3 3-5l-3 8h0c-2 1-2 3-3 5l-2 3 1 4c-1 2-1 5-2 7l-2 4-1-1c0-2 0-2-1-4 1 0 1-1 1-2 1-3 3-5 3-8z" class="Y"></path><path d="M342 429v-3-1-1c1 0 1 0 1-1-1 1 0 1-1 1l1-6 1 4c-1 2-1 5-2 7z" class="D"></path><path d="M430 119c3 0 7 3 9 4 1 1 2 1 3 1l1-2 4 4c2 1 3 2 4 3l1 3c-2-1-3-3-5-4l-1 2c-2-2-4-3-7-4-3-2-6-3-9-7z" class="E"></path><path d="M447 128c-2 0-5-2-7-4h1l1 1c2 0 4 1 5 1 2 1 3 2 4 3l1 3c-2-1-3-3-5-4z" class="K"></path><path d="M188 363c1 0 1 0 1 1l1-1 3 2c2 1 5 3 7 4 1 0 2 1 4 2h0c2 0 3 0 5-1l18 9h0v1c-4-2-9-5-13-6s-7-1-10-2c-2-1-5-2-7-3v1c-1-1-1-1-2-1-1-1-2-2-4-2l-3-4z" class="P"></path><path d="M188 363c1 0 1 0 1 1 2 1 8 4 8 5v1c-1-1-1-1-2-1-1-1-2-2-4-2l-3-4z" class="L"></path><path d="M318 459h14l-2 6c-3 0-6-1-9 0v1c-2-2-2-4-3-7z"></path><path d="M284 101c0 1 1 1 2 0h0 2l1-1h0c1 0 5 1 6 1h-2v1 1c-1-1-3-1-4-1l-3 3c0 1-1 2 0 3 0 1 1 2 2 3 1 0 2 0 3 2-1 0-1 1-2 1s-3 0-4-1-3-3-3-5c-1-2 0-4 2-7z" class="Q"></path><path d="M168 232c-1-3-2-7-4-10l21 1h-2c-1 1-1 2-3 2h-12c2 2 3 5 4 7l-1 1c-1 0-2 0-3-1z" class="P"></path><path d="M323 465c1 1 4 1 6 1h0 1l-3 9c0 1-1 5-2 6l-1-2c0-2-4-9-3-12 1 1 3 0 4 0 1 1 2 1 3 1v-1h-3 0c-1 0-2 0-2-2zM312 30v1c0 3-5 25-5 25 1 2 2 3 3 5 1 3 1 6 1 10v1c-1-3-1-6-2-8 0-2-1-5-2-5-3-2-5-1-8 0l-1-1c1-4 4-2 7-3 0 0 1-2 1-3 1-3 2-8 3-11s1-9 3-11zm68 429h1v2 3s-1 1-2 1c-5 1-8 4-12 8v1h0v-2c1-2 2-7 4-9 1-1 3-2 4-2 2-1 3-1 5-2h0z"></path><path d="M327 183c1 1 1 3 1 4l1 8c1-2 1-3 1-5v12 10 4h-1-2c1-3 0-15 0-19v-9-5z" class="Q"></path><path d="M327 183c1 1 1 3 1 4l1 8c1-2 1-3 1-5v12c-1 2 0 4 0 6-1-1-1-5-1-6 0-2 0-4-1-6 0-3 0-5-1-8v-5z" class="I"></path><defs><linearGradient id="D" x1="409.235" y1="310.483" x2="415.442" y2="314.169" xlink:href="#B"><stop offset="0" stop-color="#626065"></stop><stop offset="1" stop-color="#717471"></stop></linearGradient></defs><path fill="url(#D)" d="M417 296l2-2-10 34c-2 0-2 1-3 2l11-34z"></path><path d="M441 342h0v2h0l-1 2-15 12c-2 1-5 3-8 4l-1-2 7-4c1-1 2-1 3-2h0c2-1 4-4 6-4 0-1 1-1 1-2 1 0 1-1 2-1h1l1-2h1s0-1 1-1h0l1-1c0-1 0-1 1-1z" class="F"></path><path d="M414 546c1-1 8 0 9 0l8 3c-4 0-8-1-13-1-1 1-3 0-4 1-1 0-3 1-4 2s-2 2-3 2c-3 1-5 5-7 6 1-4 4-8 8-10 2-1 4-2 5-3h1z" class="H"></path><path d="M229 139h3 0c1 1 2 1 3 1h2l1 1c1 0 2 0 3 1h2 0c1 0 2 1 2 1v1c-1 0-1 0-2-1v1h1 0v2h1v1h0-1v1h0l3 3h0c-1 0-2-1-3-1h-1c-1 0-2-1-3-2l-6-5-5-4z" class="M"></path><path d="M312 30v-1h1c3 6 3 13 5 20 0 1 0 4 1 5s2 2 3 2l4 1v1c-1 1-2 1-3 0-1 0-3-1-4 0-4 4-4 10-5 15 0 1-1 2-1 3v-1c1-3 1-7 1-10 1-3 1-7 3-9v-1c-1-2-1-7-1-9l-3-13c0-1 0-2-1-3z"></path><path d="M455 324h1l-1 1v2 1c-2 4-5 8-8 11l-6 5h0v-2h0l1-2 1-1-1-1-3 3v-1-1c1 0 3-1 4-2v-1c1-1 2-1 3-2l1-1h0l1-1s1-1 1-2h1c0-2 2-2 3-2 1-1 2-3 2-4z" class="C"></path><path d="M455 324h1l-1 1v2 1c-2 4-5 8-8 11l-1-2c2-2 4-4 6-7l1-2c1-1 2-3 2-4z" class="H"></path><defs><linearGradient id="E" x1="487.06" y1="273.462" x2="481.357" y2="276.078" xlink:href="#B"><stop offset="0" stop-color="#818485"></stop><stop offset="1" stop-color="#a29a9f"></stop></linearGradient></defs><path fill="url(#E)" d="M475 255c-1-2-3-4-5-6-3-2-6-3-10-5-1 0-3-1-4-2h1c5 2 10 4 14 7 3 3 5 6 8 9h1c0 1 1 2 2 3l1-1 2 5c0 2 1 4 1 6 0 7-1 14-3 22l-2 6-1-1s1-1 1-2c1-1 1-3 1-4 2-6 2-12 2-17v-3c-1 0-1-1-1-1v-2h1l-1-1v-5c-1-1-1-2-2-2-2-1-3-3-4-4s-1-2-2-2z"></path><path d="M272 550c-2-2-5-3-7-4 5-1 9-2 13-7 1 0 2-2 3-2l1 1v2c1 0 1 0 1 1v1c-2 2-4 3-5 5-1 0-3 4-4 4 0-1-1-1-2-1z"></path><path d="M282 538v2c1 0 1 0 1 1h-1c-2 2-6 5-7 8v1c-1-1-3-2-3-3 3-1 7-6 10-9z" class="T"></path><path d="M417 223l43-1c-1 1-2 3-3 4-1-1-5-1-7-1-6 0-13 1-20 0v-2h-13z" class="N"></path><path d="M439 358l1 1h1 0c2-1 3-1 4-2s1-1 2-1l-5 4c-1 1-2 1-3 2l-1 1c-1 0-1 1-1 1l-2 1c-1 0-1 0-1 1-1 0-2 0-2 1v1c-1 0-1 0-1 1h-1c-3 2-5 3-7 4l-2 1h-2-1 1l3-2h1v-1c2-1 4-2 6-4-2 0-3 2-5 2l-1-1 6-4c3-3 7-4 10-6z" class="E"></path><path d="M429 364h1c1 0 3-1 5-1-2 1-4 3-6 4-2 0-3 2-5 2l-1-1 6-4z" class="G"></path><path d="M325 204v-2c1-1 0-4 0-5h2c0 4 1 16 0 19h-1v2c1 1 1 1 0 2 0 1 1 3 1 4v13c-1 0-3 0-4-1 2-3 1-8 1-12 0-1 0-2 1-3v-2-3-12z" class="F"></path><path d="M350 395h1l3-4h0v2c-1 2-3 4-4 6-2 3-4 8-6 11h0c-1 2-2 5-2 7v1c0 3-2 5-3 8 0 1 0 2-1 2-2-1-1-2-1-5 0-1 0-1 1-2 1-8 6-15 9-22l3-4z" class="K"></path><path d="M338 421h0c1 0 1-2 2-3 1-3 2-5 4-8h0c-1 2-2 5-2 7v1c0 3-2 5-3 8 0 1 0 2-1 2-2-1-1-2-1-5 0-1 0-1 1-2zm-172-99c4 6 8 12 14 16 3 3 6 6 9 8 3 3 7 5 10 8-2-1-3-2-5-3-1-1-3-2-4-2l-6-6c-2-1-3-3-5-4 2 4 6 7 9 9 1 1 3 2 4 3 2 1 3 2 5 3v1c-2-1-4-4-7-4v1h-2c-2-1-5-5-7-6-1-1-2-2-2-3-1-2-3-4-4-6l-8-11c0-1-1-1-1-2v-2z" class="D"></path><defs><linearGradient id="F" x1="264.402" y1="251.741" x2="273.811" y2="256.746" xlink:href="#B"><stop offset="0" stop-color="#6a6a6b"></stop><stop offset="1" stop-color="#828385"></stop></linearGradient></defs><path fill="url(#F)" d="M257 285c-1-2 0-5-1-7v-1-3c0-5 1-9 3-14 3-7 10-14 17-19 2-1 5-2 8-3h0v1h1-1c-2 2-5 3-7 4-9 6-16 16-18 26-2 5-2 10-2 16z"></path><defs><linearGradient id="G" x1="425.061" y1="269.005" x2="431.849" y2="271.029" xlink:href="#B"><stop offset="0" stop-color="#4e4f50"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#G)" d="M417 296c1-6 3-11 5-16 4-13 8-25 17-35v3c-3 5-6 9-8 14-2 2-2 5-3 8l-9 24-2 2z"></path><path d="M199 346l14 10v-1-1h1v-1s0-1-1-2h0v-1c3 2 5 4 7 6l1-1c1 2 1 5 2 7h1v1c0 1 1 1 0 2-3-1-8-5-11-7l-15-9c-1 0-1-1-2-2 0 0 1 0 2 1h0c1 0 1 0 2 1h1c-1 0-2-1-2-2v-1z" class="C"></path><path d="M213 350c3 2 5 4 7 6l1-1c1 2 1 5 2 7h0c-4-2-7-4-10-6v-1-1h1v-1s0-1-1-2h0v-1z" class="O"></path><path d="M213 350c3 2 5 4 7 6 0 1 1 1 1 3l-3-3c-2 0-3-1-4-2v-1s0-1-1-2h0v-1z" class="J"></path><defs><linearGradient id="H" x1="319.731" y1="595.722" x2="333.443" y2="594.748" xlink:href="#B"><stop offset="0" stop-color="#10060e"></stop><stop offset="1" stop-color="#263129"></stop></linearGradient></defs><path fill="url(#H)" d="M333 578l1-1c0 1 1 2 2 3l-1 1v2l-1 3v2h0-1l-1-1-1-1-7 22c0 3-2 5-3 8v-2h0c-1 0-1 1-2 1l13-37h1z"></path><path d="M333 578l1-1c0 1 1 2 2 3l-1 1v2l-1 3v2h0-1l-1-1-1-1 2-5v-3z" class="U"></path><path d="M333 578l1-1c0 1 1 2 2 3l-1 1v2s0-2-1-3l-1 1v-3z" class="D"></path><path d="M335 73l-2-3c2 1 3 3 5 4 0 4 1 9-1 12s-4 4-7 5c-2 0-4 1-5 0h-1 0l1-1 1-1c1-2 2-3 3-4l3-2c1-2 1-3 1-5-1-2-2-3-4-4h1c2-1 3 0 5-1z" class="Q"></path><path d="M332 83l1 1c-2 3-5 5-8 6l1-1c1-2 2-3 3-4l3-2z" class="B"></path><path d="M335 73c1 1 1 3 1 4-1 3-2 5-3 7l-1-1c1-2 1-3 1-5-1-2-2-3-4-4h1c2-1 3 0 5-1zm-63 267c2 2 3 6 4 8l7 19c0 2 0 3 1 4 1 2 1 4 2 6l10 31c1 3 3 7 3 10v1 2-2l-1 1c0-2-1-3-1-5l-4-13-9-25c-1-3-1-5-2-8l-8-23c-1-2-1-4-2-6z" class="L"></path><defs><linearGradient id="I" x1="332.946" y1="338.051" x2="324.211" y2="340.144" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2b2d"></stop></linearGradient></defs><path fill="url(#I)" d="M327 321c-2 1-5 2-7 2v-1h0c4-1 7-1 10-4v41h-2v4-4h-1c-1-2-1-4-3-7 1-1 1-2 1-3 1-3 1-6 2-9v-14c0-1 1-4 0-5z"></path><path d="M327 321h0c1 2 1 20 1 24v14h-1c-1-2-1-4-3-7 1-1 1-2 1-3 1-3 1-6 2-9v-14c0-1 1-4 0-5z" class="X"></path><path d="M302 114c1 1 2 2 3 2s1-1 1-1h2c-1 1-2 2-3 2l-1 2v2c-1 1-1 1-1 2-1 0-2 0-2 1-1 2-1 4-2 5 1 1 2 2 2 4v2h-3c-1 1-2 0-3-1s-2-3-2-4 0-2 1-2v-1-2-1-3h1c1 0 1 0 1-1 1-1 1-1 1-2 1 0 1-1 2-2 1 0 1 0 1-1h0 2v-1z"></path><path d="M295 132c1 1 2 2 3 2s2-1 3-1h0v2h-3c-1 1-2 0-3-1v-2z" class="M"></path><path d="M294 124h2c0 2-1 3-1 4v4 2c-1-1-2-3-2-4s0-2 1-2v-1-2-1zm7 9c-1-1-1-1-2-1-1-1-2-2-2-3v-4h2v-1c1-2 3-4 5-5v2c-1 1-1 1-1 2-1 0-2 0-2 1-1 2-1 4-2 5 1 1 2 2 2 4h0z" class="F"></path><path d="M302 114c1 1 2 2 3 2s1-1 1-1h2c-1 1-2 2-3 2-2 0-4 2-5 3l-1 2c0-1 0-1-1-2-1 2-1 2-2 3-1-1-1-1-1-2 1 0 1 0 1-1 1-1 1-1 1-2 1 0 1-1 2-2 1 0 1 0 1-1h0 2v-1z" class="C"></path><defs><linearGradient id="J" x1="323.056" y1="171.787" x2="318.952" y2="179.046" xlink:href="#B"><stop offset="0" stop-color="#6e6c6e"></stop><stop offset="1" stop-color="#9a9b9a"></stop></linearGradient></defs><path fill="url(#J)" d="M317 171l14 1h-5v9l1 2v5 9h-2c0 1 1 4 0 5v2l-1-2c-1 0 0-5 0-6 0-2 0-4-1-6l-1-1c-1 0-1 0-2 1v4c-1-4 1-20-1-22l-2-1z"></path><path d="M320 181h2c1 1 2 1 2 3-1 0-1-1-2-1l-1-1h-1v-1z" class="J"></path><path d="M127 162h0l3-3c1-1 2 0 3 0-3 5-5 10-7 16l-1 2v3 1h0l1-1c-1 2-1 4-2 5-1 4-2 7-3 11h-1v-3h-1c-1 2-1 6-3 8v-2c0-1 0-2 1-4 0-1 1-2 1-3 1-1 1-3 1-4 0-4 1-8 2-13 1-1 1-3 2-4 1-3 3-6 4-9z" class="L"></path><path d="M120 193c0-2 1-3 1-5 1-3 2-7 4-11v3 1h0l1-1c-1 2-1 4-2 5-1 4-2 7-3 11h-1v-3zm7-31c0 4-3 8-4 12h1c-1 3-2 5-3 8-1 4-1 8-2 11-1 2-1 6-3 8v-2c0-1 0-2 1-4 0-1 1-2 1-3 1-1 1-3 1-4 0-4 1-8 2-13 1-1 1-3 2-4 1-3 3-6 4-9z" class="I"></path><path d="M446 313c2-7 3-15-1-21-2-1-4-3-6-3-1 0-2 0-3 1v2c0 1-1 1-1 2-2 1-4 3-5 5 1-2 1-4 2-7h-1-1c1-2 3-5 5-6 1-1 4-1 6 0 3 1 8 5 9 8 2 5 1 14-1 19-1 2-3 5-4 7-1-1-1-1-1-2-1 0 0-1 0-2 1 0 1-1 1-1v-1c1 0 1-1 1-1z"></path><path d="M470 300l1-2c0-2 1-8 1-9-1-1-4-1-5-3h0c2-1 3 0 5 0 0-2 0-3-1-5-2-13-13-25-23-32h-1l1-1c2 0 3 1 4 2h1c2 3 6 5 9 7 4 4 6 9 9 13 0-2-1-4-2-6h0c1 1 3 3 3 4s-1 1-1 2c1 1 1 2 2 3 1 2 1 4 1 7 0 4 0 9-1 13v3l-1 3-1 1h-1z" class="O"></path><path d="M164 255h0c2-1 3-3 5-4 1-1 3-1 4-2 1 0 2-1 3-2l3 3c-1 1-4 2-5 3-4 3-7 6-10 10-4 4-6 9-8 14-1-1-2-2-2-4 0 0 1 0 0-1v-1c1-2 0-4 1-6h0c1-1 1-2 2-4 2-2 4-5 7-6z" class="D"></path><path d="M157 261l1 1 1-1c1-1 2-2 2-3l1 1c-1 1-2 2-3 4h1c0-1 1-1 2-2 0-1 1-2 1-2h1c-2 2-4 5-5 7h1l3-3h1c-4 4-6 9-8 14-1-1-2-2-2-4 0 0 1 0 0-1v-1c1-2 0-4 1-6h0c1-1 1-2 2-4z" class="N"></path><path d="M312 30c1 1 1 2 1 3l3 13c0 2 0 7 1 9v1c-2 2-2 6-3 9l-1-1c-1-1-1-1-1-2s-1-4-2-5v1 1 1 1c-1-2-2-3-3-5 0 0 5-22 5-25v-1z" class="B"></path><defs><linearGradient id="K" x1="344.016" y1="383.096" x2="348.719" y2="387.6" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2c2c"></stop></linearGradient></defs><path fill="url(#K)" d="M364 355h2c-1 2-1 5-2 7-2 5-6 9-10 13l-9 12c-2 3-5 7-6 10l-3 6-1-1-1 1h-1c1-2 3-4 4-6 2-6 5-11 9-16 0-1 1-1 1-2v-1c0-1 1-1 1-1 3-4 7-8 9-12 1-1 2-3 3-5h0 1c1-2 2-3 3-5z"></path><path d="M364 355h2c-1 2-1 5-2 7v-1c-2 3-4 5-6 6 0-1 2-5 3-7s2-3 3-5z" class="V"></path><path d="M347 378c0-1 1-1 1-1 3-4 7-8 9-12 1-1 2-3 3-5h0 1c-1 2-3 6-3 7-2 3-4 5-6 7s-4 5-6 7c0-1 1-1 1-2v-1z" class="K"></path><path d="M115 173c1-8 3-14 8-20l7-10c-4 3-7 4-12 5 4-3 8-4 12-7 1-1 2-4 2-6s0-3-2-4h-4-3c-3 1-6 6-7 8-1 1-2 2-2 3h-3 0c2-5 4-10 9-13 2-2 5-3 7-3s4 2 5 3c1 3 3 8 3 10v1c-1 4-5 5-7 8-3 3-6 8-8 12l-3 6c-1 2-1 5-2 7z"></path><path d="M196 347c-2 0-3-1-5-2v-1c-1 0-2-1-2-1-1-1-1-2-2-2s-1-1-2-1l-2-2c-1 0-1-1-2-2h-1v-1h-1c-1-1 0-1-1-1 0-1-1-2-2-2v-1c-1-1-2-2-2-3-1-1-2-2-2-3-1-1-1-1-1-2l-2-2c0-3-2-5-3-7s-1-3-2-5h0v-2-1-1h1l1 1v1c0 2 1 3 2 5 2 5 5 10 9 14l22 20v1c0 1 1 2 2 2h-1c-1-1-1-1-2-1h0c-1-1-2-1-2-1z" class="M"></path><defs><linearGradient id="L" x1="391.327" y1="360.261" x2="400.707" y2="365.011" xlink:href="#B"><stop offset="0" stop-color="#808285"></stop><stop offset="1" stop-color="#aca9a7"></stop></linearGradient></defs><path fill="url(#L)" d="M406 330c1-1 1-2 3-2l-25 76c-1 0-1 0-1-1 1 0 1 0 1-1h0 0v-2c1 0 1 0 1-1h0v-2c1-1 1-1 1-2h-1c-1 1-1 2-1 3-1 2-1 4-3 6l25-74z"></path><path d="M283 542v1l6 16c-1 1-1 1-3 1v2c1 1 2 2 2 3 1 2 1 4 1 5h0c0-1 0-1-1-1 0-2-2-3-3-3l-1 1-1-2c-2-4-4-8-8-10h-1c0-1 0-1 1-1v-1c-1-1-2-2-4-3h1c1 0 2 0 2 1 1 0 3-4 4-4 1-2 3-3 5-5z" class="X"></path><path d="M279 554h3v2c0 1 1 2 1 2 0 1 0 1-1 1-1-1-2-2-4-2v-1-1l1-1z" class="K"></path><path d="M278 555s-1 0-1 1v-1l1-2c2-2 4-5 5-8h-1c1-1 0-1 1-2l6 16c-1 1-1 1-3 1v2c-1-1-2-1-3-2v-2s-1-1-1-2v-2h-3l-1 1z"></path><path d="M279 554c1-1 3-3 4-5l3 11v2c-1-1-2-1-3-2v-2s-1-1-1-2v-2h-3z" class="R"></path><path d="M471 254h1v-1l3 2h0c1 0 1 1 2 2s2 3 4 4c1 0 1 1 2 2v5l1 1h-1v2s0 1 1 1v3c0 5 0 11-2 17 0 1 0 3-1 4 0 1-1 2-1 2l-2 5h-2c2-5 3-11 3-16v-8c2-6-1-11-2-16v-1c-2-3-4-5-6-8z" class="X"></path><path d="M482 271h0v-3c-1 0-1-1-1-2v-1h0c1 0 1 1 1 1 0 1 1 2 1 3v2s0 1 1 1v3c-1 1-1 3-2 5v1c0-2 1-7 0-8s0-1 0-2z" class="G"></path><path d="M471 254h1v-1l3 2h0c1 0 1 1 2 2s2 3 4 4c1 0 1 1 2 2v5l1 1h-1c0-1-1-2-1-3 0 0 0-1-1-1h0v1c0 1 0 2 1 2v3h0l-2-3v-2c-1-2-2-3-3-4-2-3-4-5-6-8z" class="J"></path><path d="M180 319c-3-4-4-9-5-14-1-4-1-9 2-13 3-3 7-6 11-7 1 0 2 0 3 1 2 2 5 5 5 6h-1-1c0 3 1 6 2 9v-1c-2-2-3-5-6-6h0c-1-1 0-2 0-3l-1-2h-3c-3 0-5 4-7 6 0 2 0 5 1 7 0 2 2 2 2 4 0 3 1 4 1 7h0v2c-1 1-1 1-1 2h0l-2 2z"></path><path d="M178 305c0-1-1-1-1-2 0-3 0-5 2-8 0 2 0 5 1 7 0 2 2 2 2 4 0 3 1 4 1 7h0v2c-1 1-1 1-1 2h0c-2-4-3-8-4-12z" class="T"></path><path d="M178 305c0-1-1-1-1-2 0-3 0-5 2-8 0 2 0 5 1 7v4l1 3v1h-1c-1-2-1-4-1-6h-1v1z" class="K"></path><defs><linearGradient id="M" x1="300.583" y1="397.867" x2="306.641" y2="395.734" xlink:href="#B"><stop offset="0" stop-color="#b3b2b2"></stop><stop offset="1" stop-color="#d2d2d1"></stop></linearGradient></defs><path fill="url(#M)" d="M301 371c1 1 1 2 2 3v1l-1-1v-1c0-1-1-1-1-1v6h0v3 1 1c1 0 1 0 1 1 0 2 2 3 3 5h0v-7c-1-3 0-5 0-8 0-2-1-10 0-11l1 46-1 1c-1-1-1-1-1-2 0 0-1 0-1-1v1c-2-3-4-7-4-11l-3-9 1-2v-4c0-2-1-5 0-7l2-2 2-2z"></path><path d="M297 382c0-2-1-5 0-7l2-2v10c0 3 1 5 0 7-1 1-1 2-1 3l1 3v1l-3-9 1-2v-4z" class="T"></path><path d="M297 382l1 11 1 3v1l-3-9 1-2v-4z" class="D"></path><path d="M320 194v-4c1-1 1-1 2-1l1 1c1 2 1 4 1 6 0 1-1 6 0 6l1 2v12 3 2c-1 1-1 2-1 3 0 4 1 9-1 12h-2s-1 0-1-1c-1-5-1-11-1-16l1-25z" class="B"></path><path d="M452 313l1-5c3-10 2-19 0-30-1-4-3-9-5-14-1-2-2-3-3-5s-3-3-4-5c1 0 2 1 2 1 11 8 15 22 16 35v8c1 0 2-1 3-1 0 1-3 6-4 7h0l-4 10v-1h-1 0-1z"></path><defs><linearGradient id="N" x1="322.108" y1="583.704" x2="281.367" y2="562.073" xlink:href="#B"><stop offset="0" stop-color="#41433b"></stop><stop offset="1" stop-color="#96949c"></stop></linearGradient></defs><path fill="url(#N)" d="M285 530h1l3 9c0 1 1 2 1 3l1 2v1c1 0 1-2 1-3v-2-1c-1-1-1-1-1-2v-1c-1-1-1-2-2-4h1c2 5 3 11 5 16l8 24 3 11c2 6 5 12 6 18 0 1 0 3-1 4h-1l-25-75z"></path><path d="M344 540c0-1 0-2 1-3h1c1 2 5 7 7 7h0l7 3h-1c-3 1-5 2-7 4v1l-5 5h-1s-1 0-1 1l-2-1-1 3c0 2-1 4-3 5h0-2l-2 7-1-1 10-31z" class="O"></path><path d="M337 565l4-10c0-1 1-2 1-3 1 1 2 2 2 3v1l-1 1-1 3c0 2-1 4-3 5h0-2z" class="P"></path><path d="M344 555c1 0 2 1 3 1l-4-4v-2c1 2 3 3 5 4 0 1 1 1 1 0 0-3-6-4-6-8h1l1 1c1 2 4 4 5 5l2-1v1l-5 5h-1s-1 0-1 1l-2-1 1-1v-1z" class="K"></path><path d="M344 540c0-1 0-2 1-3h1c1 2 5 7 7 7h0c0 1 0 1-1 2l-1-1c-1 1 0 2-1 3l-1-1-1-1h0l-2-2c0-1 1 0 0-1s-2-1-2-3z" class="Q"></path><path d="M331 172l11-1-2 1c-2 1-6 6-7 9-1 2 0 6 1 8 0 1 2 2 4 2 2 1 4 1 5 0 1 0 1-1 2-2h0c-2 1-4 3-6 2-1 0-2-1-2-1 0-2 1-5 2-6s2-1 3-1h1c-1 2-2 3-2 5h2c1 0 2 0 2-1 1-1 1-2 1-3s-1-1-1-2c1 0 4 1 5 2 0 0 1 1 1 2 0 3-2 6-2 9h-1v-1l-1-1c-1 1 0 1-1 2 0-1 0-1-1-2-5-1-10 0-14-4v-2c-1 1-1 2-1 3 0 2 0 3-1 5l-1-8c0-1 0-3-1-4l-1-2v-9h5zM133 318c-5-6-11-12-19-14-4-2-10-2-14 0-2 1-4 4-5 6-1 4 2 7 4 11 1 3 3 6 5 9-1 0-2-1-4-1-3-1-6-1-9-3s-5-6-5-10c1 4 3 7 7 9 1 0 3 1 4 0-3-3-6-5-7-9 0-3-1-9 1-11 3-3 7-5 10-5 9-1 16 2 23 7l10 9h0l-2-1v1c1 1 1 1 1 2z"></path><path d="M94 308v3c0 2 1 5 1 8 1 1 1 3 1 4l-1-1c-2-2-3-3-4-5 0-3 1-6 3-9z" class="M"></path><path d="M107 169c-3-5-7-11-13-12-5 0-10 1-14 2h5c-1 1 0 1-1 1s-1 0-2 1h0l1 1h-2c0 1 0 1 1 2s3 2 5 2 4 0 6-1h7c-2 1-3 1-5 1-2 1-6 3-8 3 0 0-1-1-2-1-3-1-6-3-7-6-1-1-1-3 0-5 0-2 2-3 4-4 3-1 15 2 18 4 5 3 8 7 11 12l-1-15c-3-1-8-1-9-4-1 0-1-1-1-2 0 1 1 1 1 1 1 1 3 1 5 1 1 0 2-1 3-1v-2h1v2h1c1-1 0-3 1-4 0-1 0-2 1-2 1 6-1 11-1 17-1 3 0 7-1 10 0 2 1 3 1 5v3 3l-1 1c0-3-1-6-2-9 0-2-1-3-2-4z"></path><defs><linearGradient id="O" x1="115.085" y1="223.241" x2="115.666" y2="210.784" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#5d5d5e"></stop></linearGradient></defs><path fill="url(#O)" d="M115 173c1-2 1-5 2-7l3-6v2h0c-2 4-2 8-3 12l2-2 1-1h1v2c-1 4-4 11-2 15 0 1 0 3-1 4 0 1-1 2-1 3-1 2-1 3-1 4v2c2-2 2-6 3-8h1v3h1c-2 5-3 9-4 14h1c0 6-2 11-2 16h0-1l-3-24c1 0 1-1 1-1 1-1 0-7 0-8v-1c-1-3-2-7-2-10l1-1v-3-3h1l2-2z"></path><path d="M112 178l2 7v6c1 1 1 2 1 3-1 0-1 0-1 1 0-1 0-2-1-2v-1c-1-3-2-7-2-10l1-1v-3z" class="N"></path><path d="M115 173c1-2 1-5 2-7l3-6v2h0c-2 4-2 8-3 12l-1 1-1-1c-1 1 0 3-1 4 0 3 1 6 1 10-1-1-1-2-1-3l-2-7v-3h1l2-2z" class="F"></path><path d="M120 193v3h1c-2 5-3 9-4 14v4c-2-2 0-4-1-5-1 1-1 2-2 4v-1-9-5c0-1 1-2 1-3 0 2 0 3 1 4v2c2-2 2-6 3-8h1z" class="D"></path><path d="M117 174l2-2 1-1h1v2c-1 4-4 11-2 15 0 1 0 3-1 4 0 1-1 2-1 3-1 2-1 3-1 4-1-1-1-2-1-4 1-1 2-5 2-6-1-1-1-3-1-5s0-4-2-6c1-1 0-3 1-4l1 1 1-1z" class="W"></path><path d="M117 174l2-2 1-1h1v2l-1-1h0c0 3-1 6-2 9-1-2-2-4-2-6l1-1z" class="C"></path><path d="M114 178c1-1 0-3 1-4l1 1c0 2 1 4 2 6l-1 8c-1-1-1-3-1-5s0-4-2-6z" class="H"></path><path d="M296 388c-1-2-1-4-1-6v-5h-1c-1 1-1 1-1 2h-1v-2c0-2 3-2 3-4v-1-25-58c0-8-1-60 1-64h1v127 21l4-3v1l-2 2-2 2c-1 2 0 5 0 7v4l-1 2z"></path><path d="M250 485c4 3 9 5 13 7-1-6-1-14-4-19-4-5-9-8-16-9 1-2 1-3 2-5 3 1 6 1 9 3 2 1 3 2 4 3 1 3 1 5 2 8l8 22-10-3c-2-1-3-1-5 0-1-1-4-1-5-2l-3-3c-1-2-4-4-6-6-3-1-5-2-8-3l-16-7h5 1 1c1-1 2-1 2-2s-1-1-2-1h0c-1-2-1-2-2-3 2 0 4 0 6 1v-2h-1v-1l9 10c2 4 7 7 11 9 1 1 3 2 4 3h1z" class="O"></path><path d="M220 465c2 0 4 0 6 1 3 2 4 4 6 7-3 0-6 0-9-1-1 0-2 0-3-1h1 1c1-1 2-1 2-2s-1-1-2-1h0c-1-2-1-2-2-3z" class="J"></path><path d="M445 305l1-1v-5h0c1 2 1 5 1 7-1 2-2 5-1 7 0 0 0 1-1 1v1s0 1-1 1c0 1-1 2 0 2 0 1 0 1 1 2l-4 5c-1 1-6 5-6 5-3 2-5 5-8 7l-1-1 1-1h-2 0l-1-1-3 3-3 3 1 1-2 1c-2 1-3 2-6 3 0 1-1 2-2 3v-1c0-1 1-3 2-4 2-4 3-8 6-11 1-1 1-3 2-4 2-1 5-2 7-3s5-2 8-3c4-3 9-10 10-15v-1c0-1 1-1 1-1z"></path><path d="M411 343h0l4-7c4-5 8-9 15-11l-12 13 1 1 2-2-3 3 1 1-2 1c-2 1-3 2-6 3 0 1-1 2-2 3v-1c0-1 1-3 2-4z" class="E"></path><path d="M418 338l1 1 2-2-3 3 1 1-2 1c-2 1-3 2-6 3 2-3 5-5 7-7z" class="P"></path><defs><linearGradient id="P" x1="430.182" y1="324.652" x2="436.027" y2="327.75" xlink:href="#B"><stop offset="0" stop-color="#cacccb"></stop><stop offset="1" stop-color="#f1eef0"></stop></linearGradient></defs><path fill="url(#P)" d="M445 305l1-1v-5h0c1 2 1 5 1 7-1 2-2 5-1 7 0 0 0 1-1 1v1s0 1-1 1c0 1-1 2 0 2 0 1 0 1 1 2l-4 5c-1 1-6 5-6 5-3 2-5 5-8 7l-1-1 1-1h-2 0l-1-1c3-2 6-4 8-6 6-6 9-13 12-20v-1-1c0-1 1-1 1-1z"></path><path d="M445 305l1-1v-5h0c1 2 1 5 1 7-1 2-2 5-1 7 0 0 0 1-1 1v1s0 1-1 1c0 1-1 2 0 2 0 1 0 1 1 2l-4 5c-1 1-6 5-6 5-3 2-5 5-8 7l-1-1 1-1h1c5-4 9-8 12-13 1-1 2-3 2-4 0-2 1-4 2-6 1-3 1-5 1-7z" class="O"></path><defs><linearGradient id="Q" x1="284.663" y1="565.615" x2="266.751" y2="581.496" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242325"></stop></linearGradient></defs><path fill="url(#Q)" d="M259 547c4 0 9 1 12 3 2 1 3 2 4 3v1c-1 0-1 0-1 1h1c4 2 6 6 8 10l1 2 1-1c1 0 3 1 3 3 1 0 1 0 1 1h0c0-1 0-3-1-5l2 1h1c1 2 2 5 3 8 0 1 1 3 1 4v2h-1c-1 2 0 3 0 5v3 7l-1 1h0v-1l-1 1v-2c0 3-1 6 0 9h-3l-7-2c1-1 1-3 2-4 1-2 1-4 2-6l3 2v-2-2c-1-2-2-4-2-6-3-10-6-20-15-26-3-3-7-4-11-6h0v-1l-2-1s-1 0-1-1l1-1z"></path><path d="M284 597c1-2 1-4 2-6l3 2c1 1 1 4 1 5-2 0-5 0-6-1z" class="B"></path><path d="M284 567l1-1c1 0 3 1 3 3 1 0 1 0 1 1h0c1 4 2 9 3 13l-1 1c-2-6-4-11-7-17z" class="F"></path><path d="M288 565l2 1h1c1 2 2 5 3 8 0 1 1 3 1 4v2h-1c-1 2 0 3 0 5v3 7l-1 1h0v-1l-1 1v-2l-1-10 1-1c-1-4-2-9-3-13 0-1 0-3-1-5z" class="D"></path><path d="M292 583l1 13h0v-1l-1 1v-2l-1-10 1-1z" class="U"></path><path d="M513 145h1c1 2 3 4 4 5s2 0 3 0 2 0 3-1c0-1 0-1 1-1 0 1 0 2-1 3s-3 2-4 2h-1c-1 1-3 0-4 1v16c2-6 6-12 12-14 3-1 13-4 15-3s4 3 5 5c0 2 0 4-1 6-2 2-5 4-7 5-3 0-5-1-8-2-1-1-3-1-5-1l1-1h6c3 0 7 2 9-1 1 0 1-1 1-2-1-1-2-2-4-2-1 0-5-1-6-2 0-1-1-1-1-1h-1c-6 2-10 6-13 12-2 3-3 8-3 11v4h1 1l-3 14-1-1-1 5h0c-1 0-1 0-1 1v-1c0-6-1-11-2-17h0v-4h0v-1h0c-1-1-1-1-1-2v-3h1v2c1 1 0 1 1 2v-1c1-1 1-3 2-5h0 0 0l1 1h1c0-4 0-11-1-16 0-3-1-6-1-8s0-4 1-5z"></path><path d="M538 157l6 3-5-1h-1-1l-1-1c1 0 1-1 2-1z" class="C"></path><path d="M531 157h7c-1 0-1 1-2 1l1 1h1 1v1c-1 0-5-1-6-2 0-1-1-1-1-1h-1z" class="L"></path><path d="M515 180v4h1 1l-3 14-1-1c1-2 1-4 1-6l1-11z" class="C"></path><path d="M512 173h0l1 1h1l-2 13c1 5 1 10 0 15-1 0-1 0-1 1v-1c0-6-1-11-2-17h0v-4h0v-1h0c-1-1-1-1-1-2v-3h1v2c1 1 0 1 1 2v-1c1-1 1-3 2-5h0 0z" class="F"></path><path d="M509 180h1 0v2 2c1 0 0 2 1 3v4h1v-4c1 5 1 10 0 15-1 0-1 0-1 1v-1c0-6-1-11-2-17h0v-4h0v-1z" class="L"></path><defs><linearGradient id="R" x1="208.302" y1="281.732" x2="196.418" y2="288.051" xlink:href="#B"><stop offset="0" stop-color="#525253"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#R)" d="M176 238h0 0l-2-2c1 0 3 1 4 1 6 4 11 11 15 17 2 2 4 5 6 8l2 5 36 105s-1-1-1-2-1-3-2-4l1-1h-1c0-1-1-3-1-5-1-1-1-2-2-4 0-1-1-3-1-5-1-1-1-2-2-4 0 0 0-2-1-2v-2l-2-6h-2c0 2 0 2 1 4l10 31h0c-4-9-7-20-11-30l-14-42c-4-13-8-26-14-38-5-9-11-17-19-24z"></path><path d="M199 262l2 5h-1v1l-1-1c-1-1-1-2-1-3v-1l1-1z" class="P"></path><path d="M182 306c2 6 6 14 12 16 3 2 6 3 9 5 6 3 11 9 13 15v2c1 1 2 2 2 3l3 8-1 1c-2-2-4-4-7-6 0-1-14-12-15-14l-13-11-5-6 2-2h0c0-1 0-1 1-2v-2h0c0-3-1-4-1-7z"></path><path d="M207 339l6 6-5-2c-1-1-1-2-2-3l1-1z" class="K"></path><path d="M202 334l5 5-1 1c1 1 1 2 2 3-1-1-2-2-3-2l-4-3v-2-1l1-1z" class="G"></path><path d="M205 341c0-2-1-1-1-3l2 2c1 1 1 2 2 3-1-1-2-2-3-2z" class="X"></path><path d="M194 328l8 6-1 1v1 2l-3-2-4-4v-1h1c-1-1-1-2-1-3z" class="E"></path><path d="M218 347c-9-6-16-14-23-23 6 2 10 7 14 11 2 2 5 5 6 8 1 0 1 0 1 1 1 1 2 2 2 3z" class="J"></path><path d="M182 317c0-1 0-1 1-2v-2h0c3 5 6 11 11 15 0 1 0 2 1 3h-1v1l4 4h0l-13-11-5-6 2-2h0z" class="L"></path><path d="M182 317h0c3 6 7 11 12 15l4 4h0l-13-11-5-6 2-2z" class="I"></path><defs><linearGradient id="S" x1="444.809" y1="340.292" x2="415.11" y2="330.595" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#363537"></stop></linearGradient></defs><path fill="url(#S)" d="M458 304l1 1c1-1 1-2 2-2 0 2-2 4-2 6h1c0 1-1 2-1 2h0c-2 3-3 6-5 9a30.44 30.44 0 0 1-8 8c-3 3-5 6-8 9-6 5-12 10-18 14-5 3-11 6-16 11l2-5 1-5c1-2 1-3 2-4s2-2 2-3c3-1 4-2 6-3l2-1-1-1 3-3 3-3 1 1h0 2l-1 1 1 1c3-2 5-5 8-7 0 0 5-4 6-5v1l-2 2h1 0c1 0 1-1 2-1h0 0c4-4 8-9 10-14h1 0 1v1l4-10z"></path><path d="M458 304l1 1c1-1 1-2 2-2 0 2-2 4-2 6h1c0 1-1 2-1 2h0-1c-2 2-4 5-5 8-1 1-1 3-2 4l3-9 4-10z" class="F"></path><path d="M435 330l6-5v1l-2 2h1 0c1 0 1-1 2-1h0c-1 2-3 4-5 5-2 3-4 5-7 7-3 3-7 6-11 9-3 3-7 5-11 8-1 0-1 1-2 1l1-5c1-2 1-3 2-4s2-2 2-3c3-1 4-2 6-3l2-1-1-1 3-3 3-3 1 1h0 2l-1 1 1 1c3-2 5-5 8-7z" class="E"></path><path d="M435 330l6-5v1l-2 2h1 0c1 0 1-1 2-1h0c-1 2-3 4-5 5v-1c1 0 1 0 1-1v-1h0c-1 1-2 1-2 2l-1-1z" class="F"></path><path d="M421 337l3-3 1 1h0 2l-1 1 1 1-7 6-2 1-1-2 2-1-1-1 3-3z" class="D"></path><path d="M421 337l3-3 1 1h0 2l-1 1c-2 1-5 3-7 5l-1-1 3-3z" class="V"></path><path d="M417 342l1 2 2-1c-2 3-5 6-8 9-1 0-3 1-4 2s0 1 0 1v1c-1 0-1 1-2 1l1-5c1-2 1-3 2-4s2-2 2-3c3-1 4-2 6-3z" class="R"></path><path d="M417 342l1 2-7 5-3 3h-1c1-2 1-3 2-4s2-2 2-3c3-1 4-2 6-3z" class="O"></path><defs><linearGradient id="T" x1="166.521" y1="306.505" x2="214.267" y2="307.865" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242324"></stop></linearGradient></defs><path fill="url(#T)" d="M166 307c2 3 3 7 6 10-3-7-6-14-8-20 1 0 2 2 3 2-2-12 1-28 10-38l5-6h1v1c-4 5-7 11-9 18-3 9-4 17-4 27 1 10 7 19 14 27h0c1 0 2 1 3 2s1 2 2 2 1 0 1-1l-1-1-1-1-2-2-1-2 13 11c1 2 15 13 15 14v1h0c1 1 1 2 1 2v1h-1v1 1l-14-10-22-20c-4-4-7-9-9-14-1-2-2-3-2-5z"></path><defs><linearGradient id="U" x1="208.207" y1="353.938" x2="193.418" y2="329.257" xlink:href="#B"><stop offset="0" stop-color="#9f9e9e"></stop><stop offset="1" stop-color="#cbcacb"></stop></linearGradient></defs><path fill="url(#U)" d="M185 325l13 11c1 2 15 13 15 14v1h0c1 1 1 2 1 2v1h-1l-9-6-12-12c-3-3-6-5-8-8 1 0 2 1 3 2s1 2 2 2 1 0 1-1l-1-1-1-1-2-2-1-2z"></path><path d="M297 216h-2c-1-2 0-6 0-8v-21l-1 1c0 1-1 2-2 2-3 3-7 3-11 3-1 0-2 0-3 1v4h0v1c-1-1-1-2-1-3-1-4-4-7-4-11 1 0 1-1 2-1 1-1 3-2 5-2-1 0-1 1-2 1 0 1 1 1 1 2 1 1 2 3 4 4l1-1c0-1-1-3-1-4l-1-1h2c2 1 3 4 5 6-1 1-3 2-4 3h1c2 0 4-1 5-3 1-1 2-4 1-5-1-6-4-10-9-13h0 14 8v13 20c-1-2 0-5 0-7l-1-8h0-2c0 2 1 4 0 6 0 1 0 2-1 3v1 3c0 1 0 1-1 2h0c0 3 0 10-2 12h-1z"></path><path d="M298 193c1 0 2-1 3 0l-1 11h0c0 3 0 10-2 12h-1 0v-6-14l1-3z" class="F"></path><defs><linearGradient id="V" x1="301.658" y1="171.908" x2="303.817" y2="188.194" xlink:href="#B"><stop offset="0" stop-color="#959495"></stop><stop offset="1" stop-color="#ccc"></stop></linearGradient></defs><path fill="url(#V)" d="M297 171h8v13 20c-1-2 0-5 0-7l-1-8h0-2c0 2 1 4 0 6 0 1 0 2-1 3v1 3c0 1 0 1-1 2l1-11c-1-1-2 0-3 0l-1 3c0-3 0-7 1-10 1-4 2-10 1-14l-2-1z"></path><path d="M297 171h8v13h0c-1-3 0-6 0-9 0-1 0-2-1-3h-2c-2 3-1 8-1 12l-3 6v3l-1 3c0-3 0-7 1-10 1-4 2-10 1-14l-2-1z" class="J"></path><path d="M505 160c0-1-1-2-1-3-2-3-5-6-7-9-2-2-5-4-6-6s-1-5 0-7c1-3 3-7 6-9 2 0 4 0 6 1 5 2 7 7 9 12l1 4h-1l1 1v1c-1 1-1 3-1 5s1 5 1 8c1 5 1 12 1 16h-1l-1-1h0 0 0c-2-5-3-9-7-13z" class="B"></path><path d="M505 160c0-1-1-2-1-3-2-3-5-6-7-9-2-2-5-4-6-6s-1-5 0-7c1-3 3-7 6-9 2 0 4 0 6 1 5 2 7 7 9 12l1 4h-1 0c-6-5-5-14-15-12-1 0-2 0-3 1-1 2-1 4-1 5v1c1 2 2 3 3 4 3 1 7 2 10 4v1c-4 1-10-3-13-5 2 3 4 5 6 7 6 6 12 15 13 24h0 0c-2-5-3-9-7-13z"></path><defs><linearGradient id="W" x1="490.233" y1="272.071" x2="475.016" y2="273.08" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242425"></stop></linearGradient></defs><path fill="url(#W)" d="M464 228c2 1 7 3 7 5 2 1 3 2 4 3l7 7c3 5 7 14 8 20 1 5 1 10 1 15 0 2-1 5-1 8 0 1-1 3-1 4l1 1c-1 3-1 7-2 10v2h-1c0 3-2 5-5 7h0c-1 1-2 3-3 5v-1-1c1-1 0-2-1-3l-1 3c0-1 1-3 0-3v-1-2c0-1 1-3 1-4l2-5 1 1 2-6c2-8 3-15 3-22 0-2-1-4-1-6l-2-5-2-3c-2-3-4-6-6-8-2-3-5-5-7-7-2-1-5-2-6-4l1-1-1-2c0-1 0-2 1-3l1-1h-1l1-3z"></path><path d="M483 293h1l1-3 1-1c-1 5-2 11-5 15h0-1v1l-1-1 2-5 2-6z" class="K"></path><path d="M480 298l1 1-2 5 1 1v-1h1l-3 6-1 3c0-1 1-3 0-3v-1-2c0-1 1-3 1-4l2-5z" class="F"></path><path d="M485 265v-1c0-1 0-2-1-4l1-1 1 2v3c1 4 2 7 2 10-1 1 0 3-1 4 0-1-1-1-1-1v-6h0c0-2-1-4-1-6z" class="N"></path><path d="M486 271h0v6s1 0 1 1c0 3 0 6-1 9v2l-1 1-1 3h-1c2-8 3-15 3-22z" class="R"></path><path d="M464 228c2 1 7 3 7 5 1 2 2 3 3 5h-1l-9-6v-1h-1l1-3z"></path><path d="M489 290l1 1c-1 3-1 7-2 10v2h-1c0 3-2 5-5 7h0c2-7 6-13 7-20z" class="C"></path><path d="M463 232l5 4c1 1 2 2 4 3 0 0 1 1 2 1v1c1 0 2 1 3 2 0 1 1 2 1 3 1 1 2 1 3 3h0l2 4c1 1 2 4 3 6h-1 0l-1 1c1 2 1 3 1 4v1l-2-5-2-3c-2-3-4-6-6-8-2-3-5-5-7-7-2-1-5-2-6-4l1-1-1-2c0-1 0-2 1-3z"></path><path d="M468 242h1c-1-1-2-3-3-3h0l-2-2h1c1 0 2 1 3 2l1 1c2 1 5 3 6 5 0 2 2 3 3 5 0 1 0 1 1 2 1 2 2 1 2 5-2-3-4-6-6-8-2-3-5-5-7-7z" class="I"></path><path d="M234 372h0l-10-31c-1-2-1-2-1-4h2l2 6v2c1 0 1 2 1 2 1 2 1 3 2 4 0 2 1 4 1 5 1 2 1 3 2 4 0 2 1 4 1 5h1l-1 1c1 1 2 3 2 4s1 2 1 2c5 11 8 23 12 34l24 74 12 36c1 5 4 11 5 16h-1c1 2 1 3 2 4v1c0 1 0 1 1 2v1 2c0 1 0 3-1 3v-1l-1-2c0-1-1-2-1-3l-3-9h-1c-2-4-2-9-4-13l-7-22-5-16-21-61-3-11-11-35z" class="E"></path><path d="M198 188l13-2h1c0 1 0 1 1 1 3 2 5 4 8 6h-4c-4 1-6 6-10 6-1 1 0 2 0 3 0 3 0 6-3 8v-1c1-3 0-5-1-8-1-1-1-2-2-2-3 0-7 0-9 1l-3 3c-3 2-6 5-7 8-1 1-2 5-3 5h-3-7c-4 1-9 0-13 0l-2 2 1 1h1c-2 0-3 1-4 2h-1l1-1c0-1 0-1-1-2h0v-1c1-1 2-1 3-2 1 0 2-1 3-1s2-1 4-1c1-1 3-1 4-1 1-1 3-2 4-2 2-1 5-1 6-3h-1l3-2h-1c1-2 2-1 3-3 0 0 2-2 3-2h-6-1-1-1l-4 1c-1 0-1 0-2-1l1-1h-1c-2 0-3 1-5 0h0l-2-1 6-3 5-2h3c2-1 5-1 8-2l16-3z"></path><path d="M168 199l3-1v1l2 1-4 1c-1 0-1 0-2-1l1-1z" class="E"></path><path d="M203 191c0-1 3-1 4-1 0 1-1 1-1 2h-1c0 1 0 1-1 2l-2 1c-2 0-2-1-3-1l3-2c1 0 2 0 2-1h-1z" class="X"></path><path d="M207 190h4 0 2l-6 3c0 1-1 2-1 2-1-1-1-1-2-1 1-1 1-1 1-2h1c0-1 1-1 1-2z" class="D"></path><path d="M171 193h3l-12 6h0l-2-1 6-3 5-2z" class="N"></path><path d="M206 195s1-1 1-2l6-3 3 2c-2 2-4 4-7 5h0c-3 1-7 0-10 0 3-1 5-1 7-2z" class="B"></path><defs><linearGradient id="X" x1="198.819" y1="193.248" x2="179.762" y2="200.678" xlink:href="#B"><stop offset="0" stop-color="#7f7f80"></stop><stop offset="1" stop-color="#a8a7a8"></stop></linearGradient></defs><path fill="url(#X)" d="M174 200c3-2 7-2 10-3l15-3c1 0 1 1 3 1l-12 3c-5 2-9 5-13 7h-1c1-2 2-1 3-3 0 0 2-2 3-2h-6-1-1z"></path><path d="M171 198c5-2 9-4 14-5 6-1 12-2 18-2h1c0 1-1 1-2 1l-3 2-15 3c-3 1-7 1-10 3h-1l-2-1v-1z" class="L"></path><path d="M175 207c1 0 3-1 5-2h3c1-1 2-1 3-2-2 3-4 6-7 7-3 2-5 2-8 3-2 1-4 2-7 3h0 5c-4 1-9 0-13 0l-2 2 1 1h1c-2 0-3 1-4 2h-1l1-1c0-1 0-1-1-2h0v-1c1-1 2-1 3-2 1 0 2-1 3-1s2-1 4-1c1-1 3-1 4-1 1-1 3-2 4-2 2-1 5-1 6-3z" class="E"></path><path d="M164 216s-1 0-1-1h0l1-1c2-1 5-2 7-3 1-1 3-2 5-2 1 0 2 0 3 1-3 2-5 2-8 3-2 1-4 2-7 3z" class="L"></path><path d="M157 214c1 0 2-1 4-1 1-1 3-1 4-1-2 2-3 2-6 3-1 0 0 0-1 1h-2l-2 2 1 1h1c-2 0-3 1-4 2h-1l1-1c0-1 0-1-1-2h0v-1c1-1 2-1 3-2 1 0 2-1 3-1z" class="T"></path><path d="M469 219c1 0 1 0 1 1 0 0 1 1 2 1h0c0-1 0-1-1-2h1l9 9c3 3 5 6 7 10 1 2 2 3 3 5 0 1 4 9 4 10v-1l1 1h0v-1c1 1 1 2 1 3v-3c0-1 0-1 1-2 1 3 1 6 1 9 0 1 0 6 1 6v-1c1 3 1 7 1 10 1 4-1 9-2 12l1 1c-1 3-2 6-3 8-1 1-2 2-3 4-2 2-3 5-5 7l-1-1v-1c1-1 1-2 1-2v-1h-1c1-3 1-7 2-10l-1-1c0-1 1-3 1-4 0-3 1-6 1-8 0-5 0-10-1-15-1-6-5-15-8-20l-7-7c-1-1-2-2-4-3 0-2-5-4-7-5l1-2c0-1 1-2 1-3l1-2 2-2z" class="K"></path><path d="M467 221c1 1 1 1 2 1 1 4 4 6 7 9 1 2 3 4 4 6 1 1 2 3 2 4h0c-2-2-4-5-7-7v2c-1-1-2-2-4-3 0-2-5-4-7-5l1-2c0-1 1-2 1-3l1-2z" class="J"></path><path d="M466 223c1 0 2 0 3 2l-1 1s1 1 1 2h0c-2-1-3-2-4-2 0-1 1-2 1-3z" class="E"></path><path d="M465 226c1 0 2 1 4 2l6 6v2c-1-1-2-2-4-3 0-2-5-4-7-5l1-2z" class="X"></path><path d="M486 244c3 1 4 3 5 6h1c0 2 1 4 2 5v11 12c0 2-1 5-1 7l-4 16h-1c1-3 1-7 2-10h0c3-9 3-18 2-27-1-2-1-5-1-8-1-4-3-8-5-12z" class="D"></path><path d="M469 219c1 0 1 0 1 1 0 0 1 1 2 1h0c0-1 0-1-1-2h1l9 9c3 3 5 6 7 10 0 3 1 6 2 8h0l2 4h-1c-1-3-2-5-5-6-1-3-2-6-4-9l-1-1c-3-4-8-7-11-11l-1-1c-1 0-1 0-2-1l2-2z" class="O"></path><path d="M470 223c3 1 4 2 6 4 3 2 5 4 6 7v1l-1-1c-3-4-8-7-11-11z" class="P"></path><path d="M488 238c1 2 2 3 3 5 0 1 4 9 4 10v-1l1 1h0v-1c1 1 1 2 1 3v-3c0-1 0-1 1-2 1 3 1 6 1 9 0 1 0 6 1 6v-1c1 3 1 7 1 10 1 4-1 9-2 12l1 1c-1 3-2 6-3 8-1 1-2 2-3 4-2 2-3 5-5 7l-1-1v-1c1-1 1-2 1-2v-1l4-16c0-2 1-5 1-7v-12-11c-1-1-2-3-2-5l-2-4h0c-1-2-2-5-2-8z" class="M"></path><path d="M498 250c1 3 1 6 1 9 0 1 0 6 1 6v-1c1 3 1 7 1 10 1 4-1 9-2 12-1-2 1-7 1-9 1-3 0-7 0-10-1 2-1 6-2 9h0 0c-1 0-1-19-1-21v-3c0-1 0-1 1-2z" class="K"></path><path d="M488 238c1 2 2 3 3 5 0 1 4 9 4 10v-1l1 1h0v-1c1 1 1 2 1 3 0 2 0 21 1 21h0 0l-2 12c0-2 1-7 0-9-1 1-1 3-1 5s0 7-2 9c0-3 1-5 1-7l-1-1c0-2 1-5 1-7v-12-11c-1-1-2-3-2-5l-2-4h0c-1-2-2-5-2-8z" class="U"></path><path d="M488 238c1 2 2 3 3 5 0 1 4 9 4 10 1 5 1 12 1 18h0c0 1 0 2-1 2v-2-5-6-4-1c0-1 0-1-1-2v-1-1c-1-2-2-4-4-5h0c-1-2-2-5-2-8z" class="P"></path><path d="M496 252c1 1 1 2 1 3 0 2 0 21 1 21h0 0l-2 12c0-2 1-7 0-9v-8c0-6 0-13-1-18v-1l1 1h0v-1z" class="T"></path><path d="M277 153c2 2 4 5 7 7h7 16c10 0 21 1 31 0 1 0 3 0 5-1 2-2 4-4 5-7 0-1-1-1-1-1h-1c1 0 2 0 3-1v-1-8h-1c-2-1-4-1-5-3-1-1-1-3 0-4 2-3 5 1 8 1v-1l-2-1h0c2-3 4-5 5-8 4-5 9-9 15-12 13-5 28-6 41-1v1c1 0 2 0 3-1 2 1 5 3 5 5-17-4-35-9-52 2-7 5-12 14-14 22-1 4-1 7 1 10 1 2 2 3 2 6-2 0-4-3-5-3h-1c-1 4-3 8-6 11-2 0-4 1-5 1-3 1-9 1-12 3-1 0-2 0-3 1h-8-15l-8-1c-1-1-1-1-2-1l-4-2h0c-4-1-6-3-8-6-1-2-2-5-1-7z"></path><path d="M286 166c4 0 8 1 13 2h0c0 1 0 1 1 2l-8-1c-1-1-1-1-2-1l-4-2z" class="I"></path><path d="M308 115c3 0 7 0 11 1l2 1 1 1c3 2 5 5 6 8v2c1-1 1-2 2-3 0 2 1 4 0 5-1 2-3 4-5 4 0 1-1 1 0 2l-1 1c-3-1-3-2-6 0 0 0-1 0-1 1 0 4 2 7 5 10 3 2 7 4 10 3 0 0 1 0 1-1 1-1 1-2 1-2h1l1 2h-1s0 1-1 1c-1 2-2 3-4 3l1 1c2 0 3-1 4-1 1-1 1-2 1-4 1 0 1 2 2 3h1c-1 1-2 2-4 2-2 1-5 2-7 2h-20c-5 0-11 0-16-1-2 0-3-1-5-2-1-1-3-2-5-2l1-1h3c1 0 3-1 3-3h0 1v1c1 1 2 2 3 2 2 0 5-1 7-2 5-2 7-7 8-11-1-2-2-3-3-3s-3 1-4 2c-1-1-1-1 0-2v-2c0-2-1-3-2-4 1-1 1-3 2-5 0-1 1-1 2-1 0-1 0-1 1-2v-2l1-2c1 0 2-1 3-2z"></path><path d="M311 150h0v1c1 1 3 1 4 1s2 1 3 1h-1l-4 1h-8l6-4z" class="V"></path><path d="M331 155h0c-3 1-7 0-9-2-4-3-7-9-7-14-1-1-1-1 0-2l1 1v1c0 5 3 10 6 13 2 2 6 2 8 2h0l1 1zm-9-37c3 2 5 5 6 8v2h0c-1 2-2 4-4 5 0-1 2-3 2-4 1-2 0-5-1-7h-1l1 3h-1-1c0-2-1-4-2-6h0l1-1z" class="C"></path><path d="M309 138h1c0 4-3 9-5 12-3 3-5 5-9 5-3 0-6 0-8-2v-1l1-2h1v2c1 1 2 2 3 2s5 0 7-1c5-3 8-9 9-15z" class="B"></path><path d="M316 124v1 2h1v-1l1-1v2l1-1 1-1c0 3 0 4-2 6s-3 3-6 3-4-1-6-3c-1 0-1-1-2-2 1-1 1-1 1-2v1l1 1h0v1c1 0 2 1 3 2h0 1 2 1c1 0 1 0 2-1h0c1-2 1-5 1-7z" class="M"></path><path d="M308 115c3 0 7 0 11 1l2 1 1 1-1 1h-1c1 2 1 4 2 6h-1c-1-2-2-4-3-5h0c1 2 2 3 2 5l-1 1-1 1v-2l-1 1v1h-1v-2-1c0 2 0 5-1 7h0c-1 1-1 1-2 1h-1-2-1 0c-1-1-2-2-3-2v-1h0l-1-1v-1c0 1 0 1-1 2-1-2-1-4-1-6 0-1 0-1 1-2v-2l1-2c1 0 2-1 3-2z" class="B"></path><path d="M319 116l2 1 1 1-1 1h-1-1v-3z" class="S"></path><path d="M305 127c0-1-1-2 0-3h0c1-1 1-2 1-2 1-1 1-2 2-2h1c-1 1-1 1-1 2l1 1c-1 1-1 2-2 3 1 0 2 1 3 0 2 0 3-1 3-3v-2l3 3c0 2 0 5-1 7h0c-1 1-1 1-2 1h-1-2-1 0c-1-1-2-2-3-2v-1h0l-1-1v-1z" class="F"></path><path d="M310 61v-1-1-1-1c1 1 2 4 2 5s0 1 1 2l1 1c0 3 0 7-1 10v1c0-1 1-2 1-3l1 1c0-5 1-9 5-12 1-1 3-1 5-2l1 1c-1 2-7 5-6 8l1-1c1 4-1 9 0 13 1 0 1 0 1-1 3-2 1-6 6-6h1c2 1 3 2 4 4 0 2 0 3-1 5l-3 2c-1 1-2 2-3 4l-1 1-1 1c-2 2-3 4-4 6 0 2 1 3 2 4h0l2-1c1-1 3-2 4-3 2-1 5-1 7-1 2 1 5 3 6 5 1 1 2 2 2 4 0 3 0 5-2 7-1 1-2 1-3 2-1 0-3-1-4-1 1-1 0-1 1-1 2 0 3-1 3-3 1-1 1-3 0-5 0-1-1-1-2-2h-5c0 1 0 1 1 1-2 2-3 3-5 4-2 2-3 4-5 6 0 1-1 2-1 3h1l-1 1-2-1c-4-1-8-1-11-1h-2s0 1-1 1-2-1-3-2l-1-2c-1-2-2-3-3-5-2-1-4-2-5-4v-1-1h2c1 0 1 1 2 1h1 0 0l-2-2 1-1c1 1 2 1 3 2l1-1c2 1 4 4 4 5l2 3v-1c0-4-1-10-4-13-1-2-3-3-5-3-3 0-7-1-9-4-2-2-3-6-3-9 0-4 3-6 6-8l-2 2c-1 3-1 5-1 8 2 5 7 9 11 11-1-1-4-3-4-5v-1h2l3 3c4 5 6 10 7 17 0 2 0 5 1 7 0-1-1-2 0-3v-1c1-3 0-6-1-9v-3l-4-8c-2-2-5-4-7-6l-2-2c-1 2 0 4-1 5h-1c-1-1-2-3-2-5s1-4 3-6h3c2 0 3 1 3 2 1 1 2 1 1 2h0l-1 1 3 3v-1h1c1-4 0-8-1-11h0l2 2c0-2 0-4-1-5-1-2-2-2-3-3s-2-3-3-4c4 1 6 2 9 5 1 0 2-1 2-1 1 2 1 5 2 8v-1c0-4 0-7-1-10z"></path><path d="M317 109c1 2 1 3 1 5h-1l-1-1 1-4h0z" class="B"></path><path d="M317 109c0-2 1-5 2-8 0 1 0 2 1 3-1 2-2 3-3 5h0z" class="C"></path><path d="M309 64c1 2 1 5 2 8h-1c-1-3-2-5-3-7 1 0 2-1 2-1z" class="S"></path><path d="M320 97c0 2 1 3 2 4h0l-2 3c-1-1-1-2-1-3v-1l1-3z" class="M"></path><path d="M301 100c2 1 4 4 4 5v2c-2-2-3-4-5-5v-1l1-1z" class="Q"></path><path d="M293 102c2 0 2 1 3 2s3 1 3 1c1 0 2 2 2 2v5c-1-2-2-3-3-5-2-1-4-2-5-4v-1z" class="W"></path><path d="M307 76c0 1 0 3 1 4 0 2 0 4 1 6v-7h0l2 13v5l-3-8c-1-3-1-5-1-7s-1-4 0-6z" class="V"></path><path d="M310 61v-1-1-1-1c1 1 2 4 2 5s0 1 1 2l1 1c0 3 0 7-1 10v1c0-1 1-2 1-3l1 1-2 8c-1-1-2-9-3-10h1v-1c0-4 0-7-1-10z" class="M"></path><path d="M324 100c1-1 3-2 4-3 2-1 5-1 7-1 2 1 5 3 6 5 1 1 2 2 2 4-2-1-3-3-5-5l-2-1c-1 2-2 0-4 1-1 0-2 1-3 1h-1 0v-1h1v-1c-2 0-2 1-3 1h-2z" class="Y"></path><defs><linearGradient id="Y" x1="321.903" y1="86.826" x2="322.403" y2="96.749" xlink:href="#B"><stop offset="0" stop-color="#040804"></stop><stop offset="1" stop-color="#1e181b"></stop></linearGradient></defs><path fill="url(#Y)" d="M319 100v-1h-2v1c-1 1 0 1 0 2-1 2-1 5-2 7 0 1 1 3 0 4 0 0-1 0-1 1l-1-1c2-2 0-5 1-7 0-1 0-1 1-2v-1-1-1l1-2v-1-1s1-1 1-2c0 0 0-1 1-1v-1c0-1 1-2 1-3l4-4h0 1l1-1v-1h2v1h2c-1 1-2 2-3 4l-1 1-1 1c-2 2-3 4-4 6l-1 3z"></path><path d="M154 343c2-1 3 0 5 1l3 2c0 1 1 2 3 3l6 4 9 7c2 1 3 2 4 3h4l3 4c2 0 3 1 4 2 1 0 1 0 2 1v-1c2 1 5 2 7 3 3 1 6 1 10 2s9 4 13 6v-1l3 2 3 11c-1 2-5 3-7 5l3-6c-1-2-3-4-5-5s-5-1-7-1c-4-1-8-1-12-2-7-1-14-5-21-8-9-5-21-6-31-6-5 0-10-1-14 1-5 2-9 4-13 6-2 1-5 2-8 3-2 1-4 3-7 4 2-2 5-4 7-6 3-2 5-3 8-5 6-7 12-18 21-22 4-2 8-3 12-4l-5-3z"></path><path d="M184 363h4l3 4 1 2c1 1 1 2 2 2v1c-2-1-3-2-4-3l-6-6z" class="U"></path><path d="M178 362c3 2 6 5 8 7 1 0 2 2 2 2l4 4-6-3c-3-1-10-3-11-6h1l-2-2h1 1l2-2z" class="P"></path><path d="M178 362c3 2 6 5 8 7l-1 1-2-2h-1c-2-1-5-2-7-4h1l2-2z" class="F"></path><path d="M151 360h5c3 1 4 3 6 5h0l-17 1c2-1 4-2 6-4l-4 1c0-1 2-2 2-3h2z" class="B"></path><path d="M137 367c1-4 5-5 8-6v-1c1 0 2-1 3-2 3-1 7-1 10 0h1c1 0 2 1 3 2l2 1c2 2 3 4 4 6-4-2-6-6-11-7-1 0-4-1-6 0h-2c0 1-2 2-2 3-2 1-3 1-5 1-2 1-4 2-5 3z" class="S"></path><path d="M191 367c2 0 3 1 4 2 1 0 1 0 2 1s3 2 4 3 2 2 3 2v1c3 2 10 5 14 3 2 1 6 2 9 4 1 0 1 0 2 1h0c1 2 2 3 3 5-1 0-2-1-2-1-2-4-9-6-13-7-3 0-6 0-9-1-6-1-10-4-14-8v-1c-1 0-1-1-2-2l-1-2z" class="R"></path><path d="M191 367c2 0 3 1 4 2 1 0 1 0 2 1s3 2 4 3 2 2 3 2v1c-4-1-8-4-12-7l-1-2z" class="V"></path><path d="M197 369c2 1 5 2 7 3 3 1 6 1 10 2s9 4 13 6c1 1 2 2 2 4-1-1-1-1-2-1-3-2-7-3-9-4-4 2-11-1-14-3v-1c-1 0-2-1-3-2s-3-2-4-3v-1z" class="S"></path><path d="M204 375c2 0 4 1 5 2 3 1 6 1 9 2-4 2-11-1-14-3v-1z" class="M"></path><path d="M137 367c-2 1-4 2-5 4l-6 3 6-6c4-4 7-9 13-12s15-4 20-2l1 1c4 1 8 5 12 7l-2 2h-1-1l2 2h-1s-1-1-2-1h0c0 1 1 1 1 2-1 0-2 1-3 0-1 0-3-3-4-4-1-2-3-3-5-3-1-1-2-2-3-2h-1c-3-1-7-1-10 0-1 1-2 2-3 2v1c-3 1-7 2-8 6z" class="B"></path><path d="M362 168h0c-4-3-5-7-6-12-2-7 0-18 4-24 4-7 11-12 19-14 7-2 13-1 20-1 6 1 13 1 19 3s10 5 15 8l6 3h1l1-2c1 2 3 3 5 5-1-1-1-2-1-2l-1-1 2-1h0l1-2c2 1 3 3 5 4 0 1 1 3 2 4l-1 1h0l3 3c1 2 2 4 4 5l-1 2c1 1 3 2 3 3 1 1 0 2 0 2l-6-5v1c-1 0-1 0-2 1h0l-5-3c-1 0-4-2-6-2l-16-8c-6-2-11-2-17-1-1 0 0 0-1-1l-3 1h-1c-2-1-4-2-6-2v1h1v1c-1 0-1 0-2-1-3-1-7 1-9 2l-5 2c-3 1-5 1-8 2-2 1-5 3-7 3-2 2-4 5-5 8 0 2 0 4-3 6h0v-1c1-1 1-2 1-3h0c-1 2-3 4-3 7 1 3 2 6 4 8h-1z"></path><path d="M419 131c2 0 5 1 8 1 1 0 2 1 3 1l2 1h0 1c-2-1-3-2-5-2 0 0-1 0-1-1h-2 0c-1 0-3-2-4-2-1-1-3-2-5-3-1-1-4-1-5-3-1 0-1-1-2-1-5-1-9-2-14-4 2 0 5 1 7 1 7 0 15 2 22 5 3 1 6 3 9 5v-1l6 3h1l1-2c1 2 3 3 5 5-1-1-1-2-1-2l-1-1 2-1h0l1-2c2 1 3 3 5 4 0 1 1 3 2 4l-1 1h0l3 3c1 2 2 4 4 5l-1 2c1 1 3 2 3 3 1 1 0 2 0 2l-6-5v1c-1 0-1 0-2 1h0l-5-3c-1 0-4-2-6-2l-16-8h2c1-1 1 0 2-1-4-2-8-3-12-3v-1z" class="C"></path><path d="M456 147c-6-6-14-11-22-15 2-1 18 10 20 12l1-1c1 1 3 2 4 4 1 1 3 2 3 3 1 1 0 2 0 2l-6-5z" class="O"></path><path d="M431 135c6 2 12 7 18 11-1 0-4-2-6-2l-16-8h2c1-1 1 0 2-1z" class="Q"></path><path d="M433 128l6 3h1l1-2c1 2 3 3 5 5-1-1-1-2-1-2l-1-1 2-1h0l1-2c2 1 3 3 5 4 0 1 1 3 2 4l-1 1h0l3 3c1 2 2 4 4 5l-1 2c-1-2-3-3-4-4l-22-14v-1z" class="W"></path><path d="M441 129c1 2 3 3 5 5l2 2v1c-3-2-6-3-9-6h1l1-2z" class="C"></path><path d="M447 128c2 1 3 3 5 4 0 1 1 3 2 4l-1 1h0-1c-1 0-2-1-3-1h0l3 3h-1c-1 0-1-1-2-1h-1v-1-1l-2-2c-1-1-1-2-1-2l-1-1 2-1h0l1-2z" class="N"></path><path d="M447 128c2 1 3 3 5 4 0 1 1 3 2 4l-1 1-7-7h0l1-2z" class="J"></path><path d="M383 137c-2 1-7 1-9 1v-2c0-1-1-3-1-3-2 1-1 4-1 6l-3 3c-4 1-6 4-7 8-1 2-1 3-3 5v-1c0-5 1-10 2-16 3-6 7-11 13-13 14-7 32 1 45 6v1c4 0 8 1 12 3-1 1-1 0-2 1h-2c-6-2-11-2-17-1-1 0 0 0-1-1l-3 1h-1c-2-1-4-2-6-2v1h1v1c-1 0-1 0-2-1-3-1-7 1-9 2l-5 2-1-1z" class="B"></path><path d="M388 132l1-1c5-3 24 0 30 1 4 0 8 1 12 3-1 1-1 0-2 1h-2c-6-2-11-2-17-1-1 0 0 0-1-1l-3 1h-1c-2-1-4-2-6-2v1h1v1c-1 0-1 0-2-1-3-1-7 1-9 2l-5 2-1-1 8-2v-1c-1 0-2-1-3-2z"></path><path d="M407 133c1 0 4 0 5 1h-3l-3 1 1-2z" class="K"></path><path d="M395 131c5 0 8 1 12 2l-1 2h-1c-2-1-4-2-6-2v1h1v1c-1 0-1 0-2-1-3-1-7 1-9 2l-5 2-1-1 8-2v-1c-1 0-2-1-3-2h1c2 0 4 0 6-1z" class="G"></path><path d="M389 132c2 0 4 0 6-1l3 1h0c-1 1-3 1-5 2l-2 1v-1c-1 0-2-1-3-2h1z" class="F"></path><path d="M388 132h1c1 0 3 0 3 1l1 1-2 1v-1c-1 0-2-1-3-2z" class="L"></path><path d="M496 312c5-5 10-9 16-11 4-1 9-2 13-1 3 0 7 2 9 4 2 3 2 8 2 11 0 2-1 4-3 6h0c-1 2-3 3-4 4 0-2 1-4 1-5h-2c2-3 4-7 3-10-1-2-3-5-5-6-5-2-11-2-16 0-8 3-15 11-20 18-3 4-5 7-7 11-5 5-11 9-16 13 4 1 8 2 12 4 6 3 10 9 14 14 2 3 4 6 7 8 4 3 10 6 13 11-3-1-5-3-7-5l-7-2c-5-3-10-6-16-7-7-1-14 0-21 1-6 0-14 1-20 4s-12 6-18 8c-3 1-6 2-9 2-5 1-12 0-16 4-1 0-2 2-3 3 1 2 2 4 4 6-3-1-4-2-6-4 1-2 1-4 2-6 1-1 3-3 4-3s2-1 3-1c2-1 4-1 6-1l-2-2c4-1 9-1 13-3l3-1 1-1v-1l-1-1c2-1 4-2 7-4h1c0-1 0-1 1-1v-1c0-1 1-1 2-1 0-1 0-1 1-1l2-1s0-1 1-1l1-1c1-1 2-1 3-2l5-4c2-1 3-2 5-4h0c0-1 1-2 2-3l5-3c0-1 1-1 1-1l-1-1h0-1l1-1c2 0 3-2 4-3 4-2 8-5 11-8l13-14 8-7 1 1z"></path><path d="M424 375c2-1 3-1 5-2-2 3-5 5-8 6 0-1 0-1-1-2l3-1 1-1z" class="N"></path><path d="M407 380c4-1 9-1 13-3 1 1 1 1 1 2-4 2-8 2-12 3l-2-2z" class="R"></path><path d="M530 320c1-3 2-6 2-8l1-3c1 3 2 6 1 10l-1 2c-1 2-3 3-4 4 0-2 1-4 1-5z" class="C"></path><defs><linearGradient id="Z" x1="432.629" y1="367.997" x2="424.498" y2="373.933" xlink:href="#B"><stop offset="0" stop-color="#b1b1b2"></stop><stop offset="1" stop-color="#dcdadc"></stop></linearGradient></defs><path fill="url(#Z)" d="M442 360h1l2-1 1-1c1-1 1-1 3-1-3 2-5 4-8 6-4 3-8 7-12 10-2 1-3 1-5 2v-1l-1-1c2-1 4-2 7-4h1c0-1 0-1 1-1v-1c0-1 1-1 2-1 0-1 0-1 1-1l2-1s0-1 1-1l1-1c1-1 2-1 3-2z"></path><defs><linearGradient id="a" x1="444.207" y1="372.355" x2="447.791" y2="362.71" xlink:href="#B"><stop offset="0" stop-color="#585b5c"></stop><stop offset="1" stop-color="#817c7c"></stop></linearGradient></defs><path fill="url(#a)" d="M441 368c2 0 8-4 10-4 1-1 3-1 4-1v3h0 1c-1 1-4 1-5 1-4 2-7 4-11 5-2 1-5 1-8 2 3-1 5-3 7-5 0 0 1-1 2-1z"></path><path d="M461 346c2-2 5-4 8-6-2 5-15 14-20 17h0c-2 0-2 0-3 1l-1 1-2 1h-1l5-4c2-1 3-2 5-4h0c0-1 1-2 2-3l5-3h2z" class="P"></path><path d="M459 346h2c-3 3-5 5-9 6h0c0-1 1-2 2-3l5-3z" class="T"></path><defs><linearGradient id="b" x1="470.867" y1="344.743" x2="472.281" y2="323.171" xlink:href="#B"><stop offset="0" stop-color="#c8c4cc"></stop><stop offset="1" stop-color="#fdfffa"></stop></linearGradient></defs><path fill="url(#b)" d="M459 343c2 0 3-2 4-3 4-2 8-5 11-8l13-14 8-7 1 1c-2 2-5 4-8 7-2 3-4 6-7 9-4 5-8 8-12 12-3 2-6 4-8 6h-2c0-1 1-1 1-1l-1-1h0-1l1-1z"></path><path d="M441 368c3-3 6-5 9-7 6-4 10-7 17-8 7 0 14 2 19 7 3 3 5 6 7 8l6 6c-6-3-10-8-15-11v1h0v1c-2-1-4-3-6-3v1h1c-2 0-4-1-5-1 2 2 3 3 6 4h-16l5-6c-2 1-3 2-5 3-1 1-3 3-4 3 0-2 0-3 1-4s3-2 4-3c-2 1-5 2-6 3-1 2-2 3-3 4h-1 0v-3c-1 0-3 0-4 1-2 0-8 4-10 4z" class="B"></path><path d="M474 362l-1-1-1-1h-2-1c1-1 2-1 3-1h1c1-1 1 0 2 0h2c2 0 3 1 4 2s2 1 3 2h0v1h0v1c-2-1-4-3-6-3v1h1c-2 0-4-1-5-1z" class="C"></path><path d="M477 181c2 2 3 4 5 5 1 2 2 5 2 6v1c1 3 3 5 5 7 0 2 3 5 4 6 2 8 5 17 5 25 1 8 3 17 3 26-1 2-1 5-1 7v1c-1 0-1-5-1-6 0-3 0-6-1-9-1 1-1 1-1 2v3c0-1 0-2-1-3v1h0l-1-1v1c0-1-4-9-4-10-1-2-2-3-3-5-2-4-4-7-7-10l-9-9h-1c1 1 1 1 1 2h0c-1 0-2-1-2-1 0-1 0-1-1-1 0 0 2-2 2-3h-13-12c-1-2-2-5-4-7v-1h0 0l1-1c-2-1-3-2-5-3-1-1-1-3-3-4-4-1-7-1-10-1-1 1-2 3-3 4h0c-1 0-1-1-1-1h-2c0-1 0-1-1-2h0c-3-1-5-4-8-5-1-2-3-2-6-2 3-2 6-5 9-6 3 1 5 0 7 1h1c5 0 9 1 13 1l19 4h3 3s0-1 1-1c4 2 7 4 11 6 0 1 1 1 1 1 3 1 5 3 7 5 1 0 3 3 4 3 0-2-3-2-3-4h0l-3-3 1-1c1 1 3 1 5 3 0 1 1 2 2 3h0 1l1-1c-2-1-3-3-4-5h1c0-1-1-2-1-3v-1c-1-3-6-8-6-11h0l-1-2 1-1z"></path><path d="M487 224c2 3 5 8 6 11v1l-3-3v-1h-1c-1-2-2-5-2-8z" class="U"></path><defs><linearGradient id="c" x1="465.735" y1="206.965" x2="455.913" y2="205.219" xlink:href="#B"><stop offset="0" stop-color="#818181"></stop><stop offset="1" stop-color="#a19fa1"></stop></linearGradient></defs><path fill="url(#c)" d="M443 200c1-1 2 0 4 0h1l2 1h1 2c6 3 11 5 17 10-7-2-13-4-19-7l-6-3h0l-2-1z"></path><path d="M443 200c1-1 2 0 4 0h1l2 1h1 2c1 2 2 2 3 3v1c-2-1-3-1-5-1l-6-3h0l-2-1z" class="E"></path><path d="M438 196h0c5 1 9 4 14 4l1-1 1-1-1-1 7 3c6 2 11 5 15 9 1 1 3 2 3 3h0c-6-4-11-9-18-11l4 3c-4 0-8-4-13-3h-1l-2-1h-1c-2 0-3-1-4 0l-1-1c-2 0-3-1-4-3z" class="J"></path><defs><linearGradient id="d" x1="464.507" y1="205.217" x2="474.755" y2="200.885" xlink:href="#B"><stop offset="0" stop-color="#1b1b1d"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#d)" d="M460 192c4 2 7 4 11 6 0 1 1 1 1 1 1 1 3 3 4 5 0 1 2 2 2 3h0c2 3 4 5 6 8-2-1-4-4-5-4 2 3 5 5 7 9-2-1-8-7-8-8h0c0-1-2-2-3-3-4-4-9-7-15-9l1-1c0-1-1-3-2-4l-6-2h3 3s0-1 1-1z"></path><path d="M460 192c4 2 7 4 11 6 0 1 1 1 1 1 1 1 3 3 4 5-5-4-11-7-17-9l-6-2h3 3s0-1 1-1z" class="H"></path><defs><linearGradient id="e" x1="496.974" y1="208.165" x2="485.661" y2="220.148" xlink:href="#B"><stop offset="0" stop-color="#050403"></stop><stop offset="1" stop-color="#292a2c"></stop></linearGradient></defs><path fill="url(#e)" d="M477 181c2 2 3 4 5 5 1 2 2 5 2 6v1c1 3 3 5 5 7 0 2 3 5 4 6 2 8 5 17 5 25h-1c-2-1-9-10-10-13-2-4-5-8-9-11 0-1-2-2-2-3-1-2-3-4-4-5 3 1 5 3 7 5 1 0 3 3 4 3 0-2-3-2-3-4h0l-3-3 1-1c1 1 3 1 5 3 0 1 1 2 2 3h0 1l1-1c-2-1-3-3-4-5h1c0-1-1-2-1-3v-1c-1-3-6-8-6-11h0l-1-2 1-1z"></path><path d="M483 195c2 2 3 6 5 8v1h-1c-2-1-3-3-4-5h1c0-1-1-2-1-3v-1z" class="D"></path><path d="M477 181c2 2 3 4 5 5 1 2 2 5 2 6v1l-7-9h0l-1-2 1-1z" class="J"></path><defs><linearGradient id="f" x1="487.324" y1="220.946" x2="485.673" y2="206" xlink:href="#B"><stop offset="0" stop-color="#303131"></stop><stop offset="1" stop-color="#545354"></stop></linearGradient></defs><path fill="url(#f)" d="M478 199c1 1 3 1 5 3 0 1 1 2 2 3h0 1l1-1h1l3 6c0 1 1 2 1 4l3 5c-1 1-2 2-3 2h-1l-1-1h0c-1-1-1-2-3-2-2-4-5-8-9-11 0-1-2-2-2-3-1-2-3-4-4-5 3 1 5 3 7 5 1 0 3 3 4 3 0-2-3-2-3-4h0l-3-3 1-1z"></path><path d="M478 199c1 1 3 1 5 3 0 1 1 2 2 3 2 2 3 6 3 9l-3-6c-1-2-3-4-5-5h0l-3-3 1-1z" class="D"></path><defs><linearGradient id="g" x1="490.32" y1="224.465" x2="450.159" y2="232.558" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#a4a3a4"></stop></linearGradient></defs><path fill="url(#g)" d="M442 201c2 0 2 0 3 1 6 3 11 6 17 8 3 1 7 2 11 3 5 3 10 7 14 11 0 3 1 6 2 8h1v1l3 3v-1c2 4 4 10 5 15-1 1-1 1-1 2v3c0-1 0-2-1-3v1h0l-1-1v1c0-1-4-9-4-10-1-2-2-3-3-5-2-4-4-7-7-10l-9-9h-1c1 1 1 1 1 2h0c-1 0-2-1-2-1 0-1 0-1-1-1 0 0 2-2 2-3h-13-12c-1-2-2-5-4-7v-1h0 0l1-1 6 1c-2-2-4-5-7-7z"></path><defs><linearGradient id="h" x1="490.898" y1="238.148" x2="499.192" y2="248.781" xlink:href="#B"><stop offset="0" stop-color="#5e5c5b"></stop><stop offset="1" stop-color="#7f8182"></stop></linearGradient></defs><path fill="url(#h)" d="M490 232v1l3 3v-1c2 4 4 10 5 15-1 1-1 1-1 2v3c0-1 0-2-1-3 1-1 1-1 0-2 0-2-1-4-2-5 0-2 0-3-1-4l-3-9z"></path><path d="M481 228v-1h0v-1c-2-2-4-3-5-7l6 6c3 4 7 9 9 14v3 1h0c-1-2-2-3-3-5-2-4-4-7-7-10z" class="J"></path><path d="M442 209c2-1 3 1 5 1 3 2 7 3 10 4 1 1 2 1 3 2h-2-12c-1-2-2-5-4-7zm-24-9c-3-1-5-4-8-5-1-2-3-2-6-2 3-2 6-5 9-6 3 1 5 0 7 1h1c5 0 9 1 13 1l19 4 6 2c1 1 2 3 2 4l-1 1-7-3 1 1-1 1-1 1c-5 0-9-3-14-4h0c1 2 2 3 4 3l1 1 2 1h0v1c-1-1-1-1-3-1 3 2 5 5 7 7l-6-1c-2-1-3-2-5-3-1-1-1-3-3-4-4-1-7-1-10-1-1 1-2 3-3 4h0c-1 0-1-1-1-1h-2c0-1 0-1-1-2h0z"></path><path d="M436 195l2 1c1 2 2 3 4 3l1 1 2 1h0v1c-1-1-1-1-3-1-2-2-5-3-8-4l2-2z" class="K"></path><path d="M418 198c-3-1-6-4-9-6 1-1 2-1 4-2 0 0 1 0 1 1 1 2 5 4 5 6l-1 1z" class="B"></path><path d="M413 190h1 7 0v1s1 0 2 1l-1 2 1 2h1 0v1c-2 0-4 0-6 1l1-1c0-2-4-4-5-6 0-1-1-1-1-1z" class="C"></path><path d="M414 190h7 0v1s1 0 2 1l-1 2 1 2-9-6z" class="N"></path><path d="M421 190a126.62 126.62 0 0 1 28 6c1 0 3 0 4 1l1 1-1 1-1 1c-5 0-9-3-14-4h0l-2-1-2 2c-4-1-7 0-10 0v-1h0-1l-1-2 1-2c-1-1-2-1-2-1v-1z" class="H"></path><defs><linearGradient id="i" x1="421.94" y1="194.643" x2="433.761" y2="193.481" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#686769"></stop></linearGradient></defs><path fill="url(#i)" d="M423 192l13 3-2 2c-4-1-7 0-10 0v-1h0-1l-1-2 1-2z"></path><path d="M295 578l18 55 6-18c1 0 1-1 2-1h0v2c1-3 3-5 3-8l7-22 1 1 1 1h1 0c0 1 0 2-1 4 2 2 0 6 0 9 0 1-1 3 0 4 0 1-1 3-1 4-2 4-3 9-4 14 0 1 0 2 1 3 1 2 2 2 3 3h2c1-1 2-4 4-5 1 2 4 3 4 5s0 3-1 4c-2 3-5 5-8 5h-4c0 3 2 7 4 8l1 1 2 1c-1 1-2 2-1 3l1 8c-5 0-8 0-12-2-1 2-3 4-4 6-2 2-5 4-7 6-4-4-8-7-11-12-1 0-3 1-4 2h-8v-1c1-2 1-5 1-8-1 0-1-1-1-1 0-1 1-1 2-2 3-2 3-5 4-9h0c-4 0-8 0-11-3-1-1-2-2-2-4s0-4 2-5c0-1 2-1 3-1l3 4c2 0 2 1 3-1 1-1 2-2 2-3v-7c-1-4-3-8-4-13v-1c-1-1-2-1-3-1h3c-1-3 0-6 0-9v2l1-1v1h0l1-1v-7-3c0-2-1-3 0-5h1v-2z"></path><path d="M324 619c0-2 0-4 1-6 0 0 0-1 1-1 1 1 0 4 0 5h0c-1 1-1 2-1 3h0-1v-1z" class="U"></path><path d="M299 639c1-2 1-3 3-4h0c1 1 0 4 0 5h-2c0-1-1-1-1-1z" class="R"></path><path d="M299 639s1 0 1 1h2l-1 4h0-3l1-5z" class="G"></path><path d="M326 653h6l1 4c-3 0-5-1-7-4z" class="F"></path><path d="M325 648h1c0 1 1 3 2 3s1-1 2-2l2 4h-6c-1-1-1-3-1-5z" class="H"></path><path d="M331 598c1-2 2-4 2-6 2 2 0 6 0 9 0 1-1 3 0 4 0 1-1 3-1 4v-1h-1c-1-3 2-7 0-10z" class="D"></path><path d="M324 644v-1c2 1 3 1 4 2l2 4c-1 1-1 2-2 2s-2-2-2-3h-1l-1-1v-3z" class="T"></path><path d="M294 651c1 0 3 0 4-1 1 1 1 1 1 2v1s0 1-1 2-3 2-5 2v-1c0-2 1-3 1-5z" class="F"></path><path d="M298 644h3 0c0 3-1 6-2 9v-1c0-1 0-1-1-2-1 1-3 1-4 1 2-2 3-5 4-7z" class="E"></path><path d="M313 647c1-2 1-4 2-6 0-2 3-3 4-3h1c0 3-1 6-2 8h0-1s0 1-1 1h0-3z" class="G"></path><path d="M305 644v-3h2c2 3 2 6 5 8l1-1v-1h3 0c1 0 1-1 1-1h1 0v1c-1 1-3 4-5 4-1 0-2 0-3-1h-1c-2-1-3-3-4-6z" class="T"></path><path d="M301 627h2 1c-2 3-3 7-6 8-3 2-7 2-9 1-2 0-4-2-4-4l-1-1h0 1c0 1 2 3 4 3h3l4-1c3-1 4-3 5-6z" class="S"></path><path d="M298 622v-3l1 3h0v4c1-2 1-3 2-4v5h0c-1 3-2 5-5 6l-4 1-1-1h-1 0c-2 0-2-1-3-2v-1l2-2v1 2c3 0 5 0 7-1 2-2 2-5 2-8z" class="M"></path><path d="M299 622h0v4c1-2 1-3 2-4v5h0c-1 3-2 5-5 6l-4 1-1-1h1c2 0 3-1 5-2 2-2 2-7 2-9z" class="I"></path><path d="M305 644c0-2-1-4 0-6 0-1 1-2 2-2 2 0 4 1 6 2 0 3-1 6-1 10h1l-1 1c-3-2-3-5-5-8h-2v3z" class="P"></path><path d="M300 611v-6l1-1 1 8c1 2 2 5 2 8v7h-1-2 0v-5-3c-1-2-1-5-1-8z" class="G"></path><path d="M301 627c1-2 0-4 1-6 1-1 1-1 2-1v7h-1-2 0z" class="H"></path><path d="M300 611v-6l1-1 1 8-1 1v6c-1-2-1-5-1-8z" class="X"></path><path d="M320 622h1l3 8c1 2 4 3 6 4h2c3 0 5 0 8-2v-1 1c-1 1-1 2-2 3-2 1-4 2-6 2-4 0-7-2-9-4-3-3-3-7-3-11z" class="B"></path><path d="M326 617h1 0c0-1 0-2 1-3 0 1 0 4-1 5v1c0 2 0 7 1 9s2 3 4 3c1 0 3-1 4-2 0-1 0-1 1-2v-1h1v3c-2 2-3 2-5 3l-1 1h-2c-2-1-5-2-6-4 1-2-1-5-1-7 0-1 0-3 1-4v1h1 0c0-1 0-2 1-3h0z" class="R"></path><path d="M309 653c2 0 3 1 4 0h0c1 0 1 0 2-1 1 0 2-1 3-2 2-2 4-7 3-10v-2h0v-1c0-1 0-1 1-2 0 0 1 1 2 1 1 1 2 2 3 4l1 5c-1-1-2-1-4-2v1 3h0l-1 3v-4h-1v1c-1 2-2 5-4 7-1-1-1-1-2 0-2 0-3 1-5 1 0-1-1-1-2-2z" class="N"></path><path d="M324 636c1 1 2 2 3 4h-3l-1 1h0v-4l1-1z" class="P"></path><path d="M323 641l1-1h3l1 5c-1-1-2-1-4-2v1h0l-1-3z" class="U"></path><path d="M331 586l1 1 1 1h1 0c0 1 0 2-1 4 0 2-1 4-2 6-1 4-2 8-4 11h-2c-3 3-3 9-4 13h-1l1-6c1-3 3-5 3-8l7-22z" class="K"></path><path d="M323 650c-2 7-5 11-10 16-6-5-11-10-11-18l1-6c0 2 1 4 2 6s3 3 4 5c1 1 2 1 2 2 2 0 3-1 5-1 1-1 1-1 2 0 2-2 3-5 4-7v-1h1v4z" class="B"></path><path d="M293 596l1-1v-7-3c0-2-1-3 0-5h1c2 6 3 12 5 18 0 2 1 4 1 6l-1 1v6c0 3 0 6 1 8v3c-1 1-1 2-2 4v-4h0l-1-3v3c-1 1-2 2-2 3v-7c-1-4-3-8-4-13v-1c-1-1-2-1-3-1h3c-1-3 0-6 0-9v2l1-1v1h0z" class="P"></path><path d="M292 605c2 2 4 4 5 6 0-2-2-3-2-5-1-3 0-8 0-11h1c0 6 1 11 4 16 0 3 0 6 1 8v3c-1 1-1 2-2 4v-4h0l-1-3v3c-1 1-2 2-2 3v-7c-1-4-3-8-4-13z"></path><path d="M296 618h1v-1-1c1 1 1 2 1 3v3c-1 1-2 2-2 3v-7z" class="I"></path><path d="M517 184c1-4 1-7 2-11 1-1 1-1 1-2h1c-1 0-1 1-1 2-1 0 0 2-1 2v5c-1 1-1 3-1 5 1-1 1-3 2-5 0-1 1-3 2-5-1 2-1 3-1 5 0 1 0 2-1 3v5c2-6 4-11 10-13 4-1 8 0 12 2 6 3 10 10 13 16-2 2-4 3-5 5-1 1-2 3-2 5-1 1-2 2-3 4-1 0-1 1-2 1l-1-1c2-2 3-4 4-6-2-3-3-8-7-9-2-1-4-1-6 0-7 3-10 10-12 17l-2 5h1c1 2 1 5 2 7 4 7 9 14 14 20-1 2-3 4-4 6-1 1-3 5-4 6l2-9c0-1 1-2 1-3-1-1-4-2-4-2-2 1-5 4-6 6-3 4-6 9-8 14-2 7-3 14-5 20-1 4-2 8-4 12 2-2 4-4 7-5 2-1 5-2 7-3 0-1 0-1 1-2-1-1-1-1-2-1h-5c1-2 2-4 4-5 2 1 3 1 5 3 0 1 0 3 1 5h1v-1c0-3 2-6 3-9 2 1 5 1 5 3l1 1-1 1h-3c0 1-1 3-2 4 6 1 11 1 14 6 2 2 3 4 3 7-1 1-2 2-2 3 0 2 1 4 1 6 0 1-1 2-1 3l-2-2h1c0-2 0-6-1-8-1-3-5-5-8-6-3 0-8-1-10 1l-26 19-8 7-13 14c-3 3-7 6-11 8-1 1-2 3-4 3l-1 1h1 0l1 1s-1 0-1 1l-5 3c-1 1-2 2-2 3-2 0-4 1-5 2-1 0-2-1-3-1l-1 1h-2v1h-1-1l-18 10-14 6c-2 2-5 2-7 4l2-6 12-5 3-2c3-1 6-3 8-4l15-12 1-2 6-5c3-3 6-7 8-11 2-2 3-3 4-5l5-9c-1-2-1-2-1-4v-1h0c1-1 2-1 3-1l4-8h1l1-1 1-3v-3c1-4 1-9 1-13 0-3 0-5-1-7-1-1-1-2-2-3 0-1 1-1 1-2s-2-3-3-4h0c1 2 2 4 2 6-3-4-5-9-9-13-3-2-7-4-9-7h-1l1-1v-1c-1 0-2-1-3-2h0c7-1 13 2 19 6l2 2c2 3 4 5 6 8v1c1 5 4 10 2 16v8c0 5-1 11-3 16h2c0 1-1 3-1 4v2 1c1 0 0 2 0 3l1-3c1 1 2 2 1 3v1 1c1-2 2-4 3-5h0c3-2 5-4 5-7h1v-2h1v1s0 1-1 2v1l1 1c2-2 3-5 5-7 1-2 2-3 3-4 1-2 2-5 3-8s2-6 2-10c1-2 0-4 0-7 1-12 2-23 1-35l-2-10v-1-2c1 4 2 8 2 11 1 10 2 20 1 29 0 3 0 6-1 9v9c-1 4-3 9-4 13 7-13 8-29 9-43v-1-5h1v6c1-3 1-6 1-9 0-1-1-3 0-4 1-3 0-6 0-9v2h-1v-6l-1-10 1 2h0 0v-2l-1-5 2-2v-1c0-1 0-3 1-4v1c0-1 0-1 1-1h0l1-5 1 1 3-14z"></path><path d="M476 303h2c0 1-1 3-1 4-2 3-3 6-5 8h-1c3-4 4-8 5-12z" class="G"></path><path d="M513 197l1 1-2 12c-1 1-1 2-1 3h0c-1-1-1-5-1-7 0-1 0-3 1-4v1c0-1 0-1 1-1h0l1-5z" class="K"></path><path d="M482 310h0c3-2 5-4 5-7h1v-2h1v1s0 1-1 2v1l1 1-11 12c0-1 1-2 1-3 1-2 2-4 3-5z" class="H"></path><path d="M477 313l1-3c1 1 2 2 1 3v1 1c0 1-1 2-1 3-2 2-4 4-5 6-3 1-4 7-8 8 4-5 10-12 12-19z" class="O"></path><path d="M459 338v-3c0-1 1-1 1-2 2-5 5-9 8-13 1-2 2-4 3-5h1l-9 15c-1 3-2 5-4 8z" class="D"></path><path d="M510 206c0 2 0 6 1 7h0c0-1 0-2 1-3 0 4-1 8-1 11 0 2 0 4-1 6v1 2h-1v-6l-1-10 1 2h0 0v-2l-1-5 2-2v-1z" class="N"></path><path d="M508 209l2-2v7h-1l-1-5z" class="Y"></path><path d="M508 214l1 2h0 0c2 2 1 8 1 11v1 2h-1v-6l-1-10z" class="U"></path><path d="M454 345l-1 1h2 0c-1 1-1 2-1 3-1 1-2 2-2 3-2 0-4 1-5 2-1 0-2-1-3-1l-1 1h-2v1h-1-1l4-3c4-3 7-4 11-7z" class="C"></path><path d="M454 345l-1 1v1h0c0 1-1 2-3 3-1 0-1 0-2 1h-1l-1 1h-2-1c4-3 7-4 11-7z" class="H"></path><path d="M459 323h0c1 0 2-2 3-2h0l-8 12c-1 1-2 3-3 4-3 3-6 6-10 9h-1l1-2 6-5c3-3 6-7 8-11 2-2 3-3 4-5z" class="D"></path><path d="M518 218c1 2 2 5 4 8 2 4 6 8 9 12h-5 0-1v-1l-2 1v-1-1h1c0-1-1-2-2-3h-1-1c1-1 1-1 2-1l-1-1v-2c0-1-1-2-1-3s-1-2-1-3c-1-1-1-3-1-5z" class="S"></path><path d="M523 197h1s-2 6-3 7l-3 10-3 6c-1 4-1 8-1 13h0c-1-3-1-5-1-8 1-8 2-16 4-23 1-3 4-4 6-5z" class="B"></path><path d="M477 307v2 1c1 0 0 2 0 3-2 7-8 14-12 19-3 4-6 7-10 10 1-1 2-3 4-4 2-3 3-5 4-8l9-15c2-2 3-5 5-8z" class="M"></path><defs><linearGradient id="j" x1="468.574" y1="311.663" x2="462.872" y2="310.783" xlink:href="#B"><stop offset="0" stop-color="#212522"></stop><stop offset="1" stop-color="#363037"></stop></linearGradient></defs><path fill="url(#j)" d="M470 300h1l1-1c0 1 0 2-1 3v1 1h-1c0 3-3 7-2 9v1l-1 2c-1 3-3 6-4 9-1 0-1 1-1 2l-3 3c-2 3-4 6-7 9s-5 6-9 9c1-2 2-3 3-4l7-7c2-3 5-6 7-10 1-1 1-2 2-4 1-1 2-2 1-3l-1 1h0c-1 0-2 2-3 2h0l5-9c-1-2-1-2-1-4v-1h0c1-1 2-1 3-1l4-8z"></path><path d="M463 309h0c1-1 2-1 3-1l-2 6c-1-2-1-2-1-4v-1z" class="S"></path><path d="M453 248c2 1 4 2 5 3 7 4 11 8 14 15 2 5 3 10 3 15v10 3c-1 1-1 2-1 3-1 4-3 8-5 13 0 1-1 2-1 3-1-2 2-6 2-9h1v-1-1c1-1 1-2 1-3l1-3v-3c1-4 1-9 1-13 0-3 0-5-1-7-1-1-1-2-2-3 0-1 1-1 1-2s-2-3-3-4h0c1 2 2 4 2 6-3-4-5-9-9-13-3-2-7-4-9-7h-1l1-1v-1z" class="P"></path><path d="M532 177c3-1 5 0 7 0 6 3 8 10 11 16-2 1-3 3-4 4-1-3-3-6-5-8-5 0-8 0-12 2 2-2 5-3 8-3v-2-1h-3 0c1-1 1-1 2-1v-1h-5c-1 1-2 1-3 2 0-1 0-1 1-1 1-2 4-2 7-2v-1c-2-1-3-1-5 0-5 1-8 4-10 8 0-1 0-2 1-4 3-4 5-7 10-8zm-69 160l5-5 6-6 13-13c8-9 22-26 34-28 5-1 11-2 16 1 2 2 3 5 4 8-9-4-16-6-25-2-10 3-18 13-24 20l-19 20c-2 2-5 5-8 6l-6 5-1 1h1 0l1 1s-1 0-1 1l-5 3c0-1 0-2 1-3h0-2l1-1 9-8z" class="B"></path><path d="M454 345l9-8h0c-1 2-6 5-6 7l2-2 4-3c1-1 1-1 2-1l-6 5-1 1h1 0l1 1s-1 0-1 1l-5 3c0-1 0-2 1-3h0-2l1-1z" class="F"></path><path d="M389 91c-1-2-2-4-2-6-1-1 0-1 0-2h0v-2c1-2 0-4 0-6l-3-3v-1c2 1 4 2 5 3 0 1 1 3 1 3 0 1 1 1 2 2l5 3c2 1 5 2 7 4l-5-8 17 19c10 9 20 17 31 25l-3-10c-1-3-2-5-2-8 1-1 2-3 2-5 2-4 5-12 9-13 5-2 12-2 17-2 1 0 5 0 6-1 0 0 1 0 1-1s1-2 1-3v-2c0 2 1 5 0 7 0 3-3 6-4 9-2 4-2 8-3 13 3 3 7 5 11 7-3 0-11-2-13-1-1 4 1 10 3 14s5 9 8 13l6 6c1 3 2 6 3 8l5 8h1v-1h1l1 1h0c1 2 2 3 2 5 1-1 1-2 1-2 1 1 1 2 2 2 1 2 3 3 3 5v1l4 13c1 6 2 11 2 17-1 1-1 3-1 4v1l-2 2 1 5v2h0 0l-1-2 1 10v6h1v-2c0 3 1 6 0 9-1 1 0 3 0 4 0 3 0 6-1 9v-6h-1v5 1c-1 14-2 30-9 43 1-4 3-9 4-13v-9c1-3 1-6 1-9 1-9 0-19-1-29 0-3-1-7-2-11v2 1l2 10c1 12 0 23-1 35 0 3 1 5 0 7 0 4-1 7-2 10l-1-1c1-3 3-8 2-12 0-3 0-7-1-10 0-2 0-5 1-7 0-9-2-18-3-26 0-8-3-17-5-25-1-1-4-4-4-6s-2-4-2-6c-1-1-1-3-1-4-1-2-3-5-3-7-1-1-1-3-2-4h-1c-2-2-3-6-5-8-1-2-2-3-3-4l-1-2c0-1-3-6-4-7l-1-2-4-4s1-1 0-2c0-1-2-2-3-3l1-2c-2-1-3-3-4-5l-3-3h0l1-1c-1-1-2-3-2-4l-1-3c-1-1-2-2-4-3l-4-4-1 2c-1 0-2 0-3-1-2-1-6-4-9-4l-1-1-5-2v1h1c0 1 0 1 1 1h0c-2 0-3-1-5-1h-1s-1 0-1 1l-1-1c0-2-3-4-5-5-1 1-2 1-3 1v-1l-2-2h0c-5-2-8-6-11-10-3-3-6-6-8-9z"></path><path d="M438 119l5 3-1 2c-1 0-2 0-3-1 0-1 0-1-1-2h0l1-1c-1 0-1-1-1-1z" class="V"></path><path d="M494 161h1v-1h1l1 1h0c0 2 1 5 2 7-1 0-1 0-2 1l-3-8z" class="M"></path><path d="M451 129c4 4 7 8 10 12l-1 1h0l-3-3-1 1-3-3h0l1-1c-1-1-2-3-2-4l-1-3z" class="R"></path><path d="M499 168c1 3 2 6 3 10 2 6 5 14 5 21h-1c1-2 0-5-1-7-2-8-5-16-8-23 1-1 1-1 2-1z" class="F"></path><path d="M467 158h2c5 7 9 13 12 21h-1c-2-2-3-6-5-8-1-2-2-3-3-4l-1-2c0-1-3-6-4-7z" class="K"></path><path d="M456 140l1-1 3 3h0l1-1 3 6c1 4 4 7 5 11h-2l-1-2-4-4s1-1 0-2c0-1-2-2-3-3l1-2c-2-1-3-3-4-5z" class="N"></path><path d="M459 147l1-2c1 2 3 3 4 4 1 2 2 5 3 6l-1 1-4-4s1-1 0-2c0-1-2-2-3-3z" class="Q"></path><path d="M497 161c1 2 2 3 2 5 1-1 1-2 1-2 1 1 1 2 2 2 1 2 3 3 3 5v1l4 13c1 6 2 11 2 17-1 1-1 3-1 4v1l-2 2-1-4-1-5v-1h1c0-7-3-15-5-21-1-4-2-7-3-10-1-2-2-5-2-7z" class="O"></path><path d="M499 166c1-1 1-2 1-2 1 1 1 2 2 2 1 2 3 3 3 5v1 1 1l1 6c1 4 2 9 3 13v3h-1v-1c0-6-2-13-4-18-1-4-3-7-5-11z" class="C"></path><path d="M508 196h1v-3c-1-4-2-9-3-13l-1-6v-1-1l4 13c1 6 2 11 2 17-1 1-1 3-1 4v1l-2 2-1-4 1-1v-1-3-4z" class="W"></path><path d="M391 88l-1-2 9 10c4 5 10 9 15 13 3 3 7 6 10 8h1c0 1 0 1 1 1h0c-2 0-3-1-5-1h-1s-1 0-1 1l-1-1c0-2-3-4-5-5-1 1-2 1-3 1v-1l-2-2h0c-5-2-8-6-11-10-3-3-6-6-8-9 1-1 0-1 1-1 0-1 1-1 1-2z" class="C"></path><path d="M409 108l4 4c-1 1-2 1-3 1v-1l-2-2h1 0c1 0 1-1 0-2z" class="Q"></path><path d="M391 88l8 10-2 1h0v1c-3-3-6-6-8-9 1-1 0-1 1-1 0-1 1-1 1-2z" class="W"></path><path d="M399 98c1 1 3 3 4 5l5 4c1 0 1 1 1 1 1 1 1 2 0 2h0-1 0c-5-2-8-6-11-10v-1h0l2-1z" class="N"></path><path d="M475 86l1 1c-2 3-8 6-12 7-3 1-5 1-7 2-3 4-5 8-6 12-1 1-2 3-2 5s3 9 4 11c1 1 2 2 2 3l-1-1c-2-3-3-6-5-10-1-2-2-4-2-6-1-1-1-2-1-4 1-2 2-5 3-8 1-2 2-6 4-8 4-5 16-2 22-3v-1zm-58 25c-8-6-15-12-22-20-2-3-4-5-5-7s-1-3-1-4c2 0 4 1 5 2l5 4c2 1 5 3 7 5 9 8 18 17 28 25l4 3h0s0 1 1 1l-1 1h0c1 1 1 1 1 2-2-1-6-4-9-4l-1-1-5-2-7-5z" class="B"></path><path d="M417 111l1-1v-1c0 1 2 2 2 2l7 4h0c1 1 1 2 2 2l1 1h-1l-5-2-7-5z" class="F"></path><path d="M429 118h1l-1-1c-1 0-1-1-2-2h0c2 1 4 2 6 2l1-1 4 3h0s0 1 1 1l-1 1h0c1 1 1 1 1 2-2-1-6-4-9-4l-1-1z" class="L"></path><path d="M489 192c-3-13-9-26-16-37l-8-11c-3-6-7-14-9-21-1-3-4-9-2-13 1-1 1-2 2-3 0 2 1 4 2 7 0-1 0-2-1-3v-6-1c0-1 1-3 2-4h0l-1 4h0c1-2 2-3 3-5-1 10 2 20 6 29h1c-2-6-5-13-6-19v-1c0-2-1-4 0-6 0-1 0-1 1-2 2-2 5-4 8-6l-1 2c-2 3-2 7-3 10 0 2-2 4-2 6 0 3 1 6 1 8 4 12 11 20 17 30 6 11 12 21 17 32 1 3 2 6 3 8 1 4 2 7 3 10l1 5 1 4 1 5v2h0 0l-1-2 1 10v6h1v-2c0 3 1 6 0 9-1 1 0 3 0 4 0 3 0 6-1 9v-6h-1v5 1c-1 14-2 30-9 43 1-4 3-9 4-13v-9c1-3 1-6 1-9 1-9 0-19-1-29 0-3-1-7-2-11v2 1l2 10c1 12 0 23-1 35 0 3 1 5 0 7 0 4-1 7-2 10l-1-1c1-3 3-8 2-12 0-3 0-7-1-10 0-2 0-5 1-7 0-9-2-18-3-26 0-8-3-17-5-25-1-1-4-4-4-6s-2-4-2-6c-1-1-1-3-1-4 1 1 1 2 2 2h1z" class="B"></path><path d="M486 190c1 1 1 2 2 2h1c2 4 3 9 4 14-1-1-4-4-4-6s-2-4-2-6c-1-1-1-3-1-4z" class="Q"></path><path d="M487 173l-6-12c-1-1-2-2-2-4-1-1-2-1-2-2v-1c0-1 1-2 1-2h0c1 1 2 3 3 4 0 1 0 1 1 2l7 15h-2z" class="S"></path><path d="M487 173h2c2 5 3 9 5 14 3 7 6 15 7 22l-1-1v-1 1c-1 2 0 4 0 6 1 1 0 3 1 5v2l-1-1c0-3-1-5-1-8-1 0 0-1-1-2v-2-1-1l-1-2v-1-1l-1-2h0v-2l-1-1v-1c0-1-1-3-1-4v-1l-1-1v-2l-3-6v-2h-1v-1c0-1-1-1-1-2v-1l-1-1v-2z" class="M"></path><path d="M487 165c-2-5-5-9-8-13l-9-16h0c4 5 7 10 10 15l6 11c2 3 3 6 5 9 2 4 5 9 7 13l2 5v1c1 0 1 1 2 3l2 4c0-1 0-2-1-3h0v-2-2c1 4 2 7 3 10l1 5 1 4 1 5v2h0 0l-1-2c0-3-1-6-2-8-1 0-1 1-2 1l-1-2h0c-2-5-3-11-5-16-3-8-7-16-11-24z" class="D"></path><path d="M500 189v1c1 0 1 1 2 3l2 4c0-1 0-2-1-3h0v-2-2c1 4 2 7 3 10l1 5 1 4 1 5v2h0 0l-1-2c0-3-1-6-2-8-2-6-5-11-6-17z" class="M"></path><path d="M482 158c2 2 3 5 5 8v-1c4 8 8 16 11 24 2 5 3 11 5 16h0l1 2c1 0 1-1 2-1 1 2 2 5 2 8l1 10v6h1v-2c0 3 1 6 0 9-1 1 0 3 0 4 0 3 0 6-1 9v-6h-1v5 1c-1-1 0-3-1-4v-11c-1-9-3-17-6-26-1-7-4-15-7-22-2-5-3-9-5-14l-7-15z" class="D"></path><path d="M507 225l1-3h0l1 2v6 3h0c0-1-1-3-1-3v-1l-1-4z"></path><path d="M487 165c4 8 8 16 11 24 2 5 3 11 5 16h0l1 2c1 0 1-1 2-1 1 2 2 5 2 8l1 10-1-2h0l-1 3 1 4v1h-1v-5c-1-8-4-16-7-24-2-7-4-15-8-22l-5-13v-1z" class="H"></path><path d="M503 205h0l1 2c1 0 1-1 2-1 1 2 2 5 2 8l1 10-1-2h0l-1 3c-1-7-2-14-4-20z" class="O"></path><path d="M389 136c2-1 6-3 9-2 1 1 1 1 2 1v-1h-1v-1c2 0 4 1 6 2h1l3-1c1 1 0 1 1 1 6-1 11-1 17 1l16 8c2 0 5 2 6 2l5 3h0c1-1 1-1 2-1v-1l6 5 4 4 1 2c1 1 4 6 4 7l1 2c1 1 2 2 3 4 2 2 3 6 5 8h1c1 1 1 3 2 4 0 2 2 5 3 7 0 1 0 3 1 4 0 2 2 4 2 6-2-2-4-4-5-7v-1c0-1-1-4-2-6-2-1-3-3-5-5l-1 1 1 2h0c0 3 5 8 6 11v1c0 1 1 2 1 3h-1c1 2 2 4 4 5l-1 1h-1 0c-1-1-2-2-2-3-2-2-4-2-5-3l-1 1 3 3h0c0 2 3 2 3 4-1 0-3-3-4-3-2-2-4-4-7-5 0 0-1 0-1-1-4-2-7-4-11-6-1 0-1 1-1 1h-3-3l-19-4c-4 0-8-1-13-1 0-1-1-1-1-2-3-1-5 0-8-2h1v-1c0-1 1 0 1 0h1 2c-1-1-1-1-2-1-2-1-2-1-3-2h-1c-1-1-1-1-2-1-1-1-1-2-2-3l-1-1h0c-3-1-5-3-7-3-1-1-2-1-3-1-2 1-5 2-7 3s-5 1-7 2v1c1 2 9 0 11 0 1 0 2 1 3 2h0c-3 0-6 0-9 1-1 0-1 1-2 1v1c-1 0-1-1-2-2-1 0-3 0-5-1h0l1-1s1 0 2-1v-1h-2c-2 0-2-1-3-2h-1c0 1 0 1-1 2-1-1-1-1-1-2l-1-2c-3-1-6-2-10-4h1c-2-2-3-5-4-8 0-3 2-5 3-7h0c0 1 0 2-1 3v1h0c3-2 3-4 3-6 1-3 3-6 5-8 2 0 5-2 7-3 3-1 5-1 8-2l5-2z" class="B"></path><path d="M365 165l3 3h-3l-2-2h1 1v-1z" class="I"></path><path d="M404 154h1c0-1-1-1-2-2h1c1 0 2 1 3 1h1v2l-1 1v1h0v1l-2-2c0-1-1-1-1-2z" class="M"></path><path d="M404 154c0 1 1 1 1 2l2 2c2 1 4 1 5 2h-2 2c-1 2-1 1-3 1-1 0-3-1-4-2l-1-1v1h-3 1c1-2 1-3 2-5z" class="I"></path><path d="M405 159l1-1v1c1 0 3 1 4 1h2c-1 2-1 1-3 1-1 0-3-1-4-2z" class="V"></path><path d="M368 168c1 1 2 2 4 2l-4-5c2 1 5 3 5 4v5l-1-2c-3-1-6-2-10-4h1 2 3z" class="Q"></path><path d="M435 152v-1c-1 0-1-1-2-1v-1l1 1h2c-1-1-2-1-3-2-2 0-4-1-6-2v-1c1 1 3 1 4 2 3 1 7 2 9 4 1 1 3 2 4 3h1c1 1 2 2 4 3l1 1v1c-1-1-4-2-6-4h0c-3 0-5-1-8-2l-1-1z" class="J"></path><defs><linearGradient id="k" x1="392.925" y1="138.425" x2="395.295" y2="147.246" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#373939"></stop></linearGradient></defs><path fill="url(#k)" d="M406 135l3-1c1 1 0 1 1 1-8 3-13 7-19 12-2 1-5 3-6 4-2 2-4 5-6 7 0-2 3-6 3-7v-1h-1c5-3 10-7 15-10 0-1-1-1-2-2l11-3h1z"></path><path d="M429 155c-4-1-9-1-14-2-4-1-8-3-12-4-1-1-3-2-5-2h-1c5-1 13 1 17 2 7 1 14 1 21 3l1 1h0c1 1 2 2 4 2h-2c-2-1-7-1-9 0z" class="O"></path><path d="M436 153c3 1 5 2 8 2h0c2 2 5 3 6 4 3 2 7 3 9 5l-1 1c5 5 11 10 15 15 1 1 2 2 2 3h0c-5-5-10-10-16-14h-1c-2-1-3-1-4-2-2 0-2-1-2-2l-8-4v-1h-1c1-1 1-1 1-2h0l-6-2-9-1c2-1 7-1 9 0h2c-2 0-3-1-4-2h0z" class="F"></path><path d="M452 165c3 0 5 2 7 4h-1c-2-1-3-1-4-2-2 0-2-1-2-2z" class="X"></path><path d="M436 153c3 1 5 2 8 2h0c2 2 5 3 6 4 3 2 7 3 9 5l-1 1c-4-3-9-5-14-7l-6-2-9-1c2-1 7-1 9 0h2c-2 0-3-1-4-2h0z" class="Q"></path><path d="M408 155c8 1 17 4 26 4 2 0 3 1 4-1 1-1 0-1 0-2l6 2h0c0 1 0 1-1 2h1v1l8 4c0 1 0 2 2 2 1 1 2 1 4 2v1h-1c-1-1-2-1-3-2v1h0-1-2 0c-1 0-2-1-3-1h-1c-1 0-4-1-5-2l-7-1-6-2v-1c-1-2-9-2-11-3l-6 1c-1-1-3-1-5-2v-1h0v-1l1-1z" class="N"></path><path d="M438 156l6 2h0c0 1 0 1-1 2h1v1l-10-2c2 0 3 1 4-1 1-1 0-1 0-2z" class="C"></path><path d="M444 164l4 2s1 1 2 1h1c1 0 2 1 3 1v1h0-1-2 0c-1 0-2-1-3-1h-1c-1 0-4-1-5-2 1 0 1-1 2-2z" class="E"></path><path d="M407 156l10 2h1c0 1 1 1 1 1h2-3 0l-6 1c-1-1-3-1-5-2v-1h0v-1z" class="C"></path><path d="M417 158c6 1 13 1 19 3 3 1 5 2 8 3-1 1-1 2-2 2l-7-1-6-2v-1c-1-2-9-2-11-3h0 3-2s-1 0-1-1h-1z" class="H"></path><path d="M385 166c1-2 1-4 3-6h0v-1c1-3 3-9 5-10 1-1 1-1 2 0l1 1h1c1 1 1 1 1 2-1 1-1 2-1 4v3c-1 0-1 1-1 2h0c-1 1-1 1-1 2 0 2-1 4-2 6-5 3-10 3-14 6-2-1-3-2-4-4l1-1c1-1 1-1 1-2l1-1c0-1 1-1 1-2 1 1 2 1 3 2 1 0 2 0 3-1z" class="S"></path><path d="M395 152l-4 10c0 1-1 2-1 3h1c0 1 0 2-1 3h-1c-1 1-1 1-2 1-1 1-3 1-5 1-1-1-2-1-3-2 1 0 2-1 2-1h1c1 0 2 0 3-1h1c3-1 4-3 5-6 0 0 0-1 1-1 1-3 1-6 3-7z" class="I"></path><path d="M387 169h-3c1-1 1-1 2-1h3c-1 1-1 1-2 1zm-2-3c1-2 1-4 3-6h0v-1c1-3 3-9 5-10 1-1 1-1 2 0l1 1h1c1 1 1 1 1 2-1 1-1 2-1 4 0-1 0-3-1-4h-1c-2 1-2 4-3 7-1 0-1 1-1 1-1 3-2 5-5 6h-1z" class="O"></path><path d="M430 143c8 2 14 6 20 10 4 2 7 4 9 6 4 3 8 6 11 10l4 5c0 3 2 5 3 7l-1 1 1 2h0c0 3 5 8 6 11v1c-2-5-6-8-8-13h0c0-1-1-2-2-3-4-5-10-10-15-15l1-1c-2-2-6-3-9-5v-1l-1-1c-2-1-3-2-4-3h-1c-1-1-3-2-4-3-2-2-6-3-9-4h3 0 0l-4-4h0z" class="T"></path><path d="M430 143c8 2 14 6 20 10 4 2 7 4 9 6v1l-3-3c-2-1-5-2-7-3-1-1-2-2-3-2l-2-2-3-1h-1c1 1 3 2 4 2 0 1 1 1 1 2h1l1 1c1 0 2 1 3 1 2 1 4 4 7 5h-1l-11-7c-1-1-3-1-5-2-2-2-6-3-9-4h3 0 0l-4-4h0z" class="S"></path><path d="M440 151c2 1 4 1 5 2l11 7 1 1c3 3 7 6 11 10 3 3 6 7 8 11l1 2h0c0 3 5 8 6 11v1c-2-5-6-8-8-13h0c0-1-1-2-2-3-4-5-10-10-15-15l1-1c-2-2-6-3-9-5v-1l-1-1c-2-1-3-2-4-3h-1c-1-1-3-2-4-3z" class="U"></path><path d="M430 143h0c-8-3-16 0-24 1 3-2 6-2 9-3 2 0 3-1 5-1 6 1 11 2 16 4 2 1 4 2 5 3 5 2 10 6 16 9v-1c-1-1-3-2-4-2-4-3-7-7-12-8l-1-1h1 1 1c2 0 5 2 6 2l5 3h0c1-1 1-1 2-1v-1l6 5 4 4 1 2c1 1 4 6 4 7l1 2c1 1 2 2 3 4 2 2 3 6 5 8h1c1 1 1 3 2 4 0 2 2 5 3 7 0 1 0 3 1 4 0 2 2 4 2 6-2-2-4-4-5-7v-1c0-1-1-4-2-6-2-1-3-3-5-5-1-2-3-4-3-7l-4-5c-3-4-7-7-11-10-2-2-5-4-9-6-6-4-12-8-20-10z" class="P"></path><path d="M474 174l3 3s1 1 1 2c1 0 1 1 1 2l3 5c-2-1-3-3-5-5-1-2-3-4-3-7z" class="G"></path><path d="M456 147l6 5 4 4 1 2c1 1 4 6 4 7-3-3-5-6-8-9s-6-5-9-7h0c1-1 1-1 2-1v-1z" class="L"></path><path d="M389 136c2-1 6-3 9-2 1 1 1 1 2 1v-1h-1v-1c2 0 4 1 6 2l-11 3c1 1 2 1 2 2-5 3-10 7-15 10h1v1c-2 1-3 2-5 3-1 0-2 0-4 1h0l-1-1h0-1c-1 2-6 5-6 8v3 1h-1-1l2 2h-2c-2-2-3-5-4-8 0-3 2-5 3-7h0c0 1 0 2-1 3v1h0c3-2 3-4 3-6 1-3 3-6 5-8 2 0 5-2 7-3 3-1 5-1 8-2l5-2z" class="B"></path><path d="M371 154c1-2 2-3 3-4 2 0 3-1 6-1l1 1h-3c-2 1-3 2-5 3v2l-1-1h0-1 0z" class="C"></path><path d="M381 150h0 1v1c-2 1-3 2-5 3-1 0-2 0-4 1h0v-2c2-1 3-2 5-3h3z" class="O"></path><path d="M363 166v-2c-1-3 0-7 2-10l1-1c0 2-1 4-2 6l7-5h0c-1 2-6 5-6 8v3 1h-1-1z"></path><path d="M384 138l5-2c0 1 0 3-1 3 0 0-2 1-3 1h0c-1 0-1 0-1 1h-2-1l-1 1h-1-1l-3 1c-2 1-3 2-5 3h0v-1-1l-1-1c2 0 5-2 7-3 3-1 5-1 8-2z" class="C"></path><path d="M389 136c2-1 6-3 9-2 1 1 1 1 2 1v-1h-1v-1c2 0 4 1 6 2l-11 3c1 1 2 1 2 2-5 3-10 7-15 10h0l-1-1c3-2 6-5 9-6v-1c0-1 1-1 0-2-1 1-1 1-1 2h-3c-1 0-3 1-4 1h-1l-2 1h-2 0l1-1h2l1-1h1 1c1 0 1-1 2-1h0c0-1 0-1 1-1h0c1 0 3-1 3-1 1 0 1-2 1-3z" class="F"></path><path d="M435 165l7 1c1 1 4 2 5 2h1c1 0 2 1 3 1h0 2 1 0v-1c1 1 2 1 3 2h1v-1h1c6 4 11 9 16 14 2 5 6 8 8 13 0 1 1 2 1 3h-1c1 2 2 4 4 5l-1 1h-1 0c-1-1-2-2-2-3-2-2-4-2-5-3-4-2-9-7-11-10-4-4-9-6-15-9h1l-2-2h0c0-1-4-2-4-3 1 0 2 1 2 1h1 0v-2c-1 0-1-1-2-1v-1h0l-16-5h7c2 1 4 2 6 2h0c-3-2-6-3-10-4h0z" class="D"></path><path d="M448 172c5 2 8 6 13 8l-1 1h-1c-3-1-5-2-8-3 0-1-4-2-4-3 1 0 2 1 2 1h1 0v-2c-1 0-1-1-2-1v-1z" class="F"></path><path d="M435 165l7 1c1 1 4 2 5 2h1c1 0 2 1 3 1l5 4c-4 1-7-3-11-4-3-2-6-3-10-4h0z" class="Q"></path><path d="M463 181c0-1 0-1-1-2-2-1-4-2-5-4h0c1 0 2 1 2 2h3c3 0 5 5 8 7h1 0c5 4 8 10 12 15 1 2 2 4 4 5l-1 1h-1l-6-8c-2-3-4-5-7-8s-5-6-9-8z" class="E"></path><path d="M452 180h1l-2-2h0l8 3h1l1-1c0 1 1 1 2 2s3 2 4 4c0 0 1 0 0 1 1 1 3 2 4 3 2 2 4 6 6 6 1 0 2 1 2 1l6 8h0c-1-1-2-2-2-3-2-2-4-2-5-3-4-2-9-7-11-10-4-4-9-6-15-9z" class="N"></path><path d="M452 180h1l-2-2h0l8 3h1l1-1c0 1 1 1 2 2s3 2 4 4c0 0 1 0 0 1-2-2-4-4-7-6v1c3 2 5 4 7 7h0c-4-4-9-6-15-9z" class="G"></path><defs><linearGradient id="l" x1="468.384" y1="193.664" x2="472.662" y2="172.661" xlink:href="#B"><stop offset="0" stop-color="#1b1723"></stop><stop offset="1" stop-color="#4d5048"></stop></linearGradient></defs><path fill="url(#l)" d="M454 168c1 1 2 1 3 2h1v-1h1c6 4 11 9 16 14 2 5 6 8 8 13 0 1 1 2 1 3h-1c-4-5-7-11-12-15h0-1c-3-2-5-7-8-7l-6-4h0l-5-4h0 2 1 0v-1z"></path><path d="M454 168c1 1 2 1 3 2 2 1 4 3 5 4 3 3 7 7 9 10h0-1c-3-2-5-7-8-7l-6-4h0l-5-4h0 2 1 0v-1z" class="H"></path><path d="M454 168c1 1 2 1 3 2 2 1 4 3 5 4h-2c-1-1-1-2-3-2l-1 1h0l-5-4h0 2 1 0v-1z" class="V"></path><path d="M395 149c2 0 3 0 4 1v1c0 2-1 4 1 7 0 0 1 0 1 1h3v-1l1 1c1 1 3 2 4 2 2 0 2 1 3-1h-2 2l6-1c2 1 10 1 11 3v1l6 2h0c4 1 7 2 10 4h0c-2 0-4-1-6-2h-7l16 5h0v1c1 0 1 1 2 1v2h0-1s-1-1-2-1c0 1 4 2 4 3h0l2 2h-1l-18-5c-1-1-4-1-6-1l12 5c-3 0-6-1-8-1-4-1-9-1-13-3-2-1-5-2-8-4-1-1-4-3-5-2 0 0 0 1-1 2-2 0-6-2-9-3v-7c0-1 0-2 1-2v-3c0-2 0-3 1-4 0-1 0-1-1-2h-1l-1-1z" class="B"></path><path d="M434 175c2 0 5 0 6-1 0-1-1-1-2-1l-10-2c7-1 13 2 19 4 0 1 4 2 4 3h0l2 2h-1l-18-5z" class="V"></path><path d="M395 149c2 0 3 0 4 1v1c0 2-1 4 1 7 0 0 1 0 1 1h3v-1l1 1c1 1 3 2 4 2 2 0 2 1 3-1h-2 2l6-1c2 1 10 1 11 3v1l6 2h0-9-17c-1 0-4 1-6 0s-5-4-6-6v-3c0-2 0-3 1-4 0-1 0-1-1-2h-1l-1-1z"></path><defs><linearGradient id="m" x1="421.001" y1="159.047" x2="422.547" y2="163.747" xlink:href="#B"><stop offset="0" stop-color="#989898"></stop><stop offset="1" stop-color="#b5b6b8"></stop></linearGradient></defs><path fill="url(#m)" d="M412 160l6-1c2 1 10 1 11 3v1c-5-1-11 0-17-1l-3-1c2 0 2 1 3-1h-2 2z"></path><path d="M412 160c1 0 2 1 3 1v1h-3l-3-1c2 0 2 1 3-1z" class="E"></path><path d="M396 161v7c3 1 7 3 9 3 1-1 1-2 1-2 1-1 4 1 5 2 3 2 6 3 8 4 4 2 9 2 13 3 2 0 5 1 8 1l-12-5c2 0 5 0 6 1l18 5c6 3 11 5 15 9 2 3 7 8 11 10l-1 1 3 3h0c0 2 3 2 3 4-1 0-3-3-4-3-2-2-4-4-7-5 0 0-1 0-1-1-4-2-7-4-11-6-1 0-1 1-1 1h-3-3l-19-4c-4 0-8-1-13-1 0-1-1-1-1-2-3-1-5 0-8-2h1v-1c0-1 1 0 1 0h1 2c-1-1-1-1-2-1-2-1-2-1-3-2h-1c-1-1-1-1-2-1-1-1-1-2-2-3l-1-1h0c-3-1-5-3-7-3-1-1-2-1-3-1-2 1-5 2-7 3s-5 1-7 2v1c1 2 9 0 11 0 1 0 2 1 3 2h0c-3 0-6 0-9 1-1 0-1 1-2 1v1c-1 0-1-1-2-2-1 0-3 0-5-1h0l1-1s1 0 2-1v-1h-2c-2 0-2-1-3-2h-1c0 1 0 1-1 2-1-1-1-1-1-2v-5l2 2c1 2 2 3 4 4 4-3 9-3 14-6 1-2 2-4 2-6 0-1 0-1 1-2h0z"></path><path d="M435 182c9 1 19 3 26 7h0-3c-4-2-9-4-14-4h-3c-2-1-4-1-6-3z" class="T"></path><path d="M406 175v-1l-4-2c2 0 3 0 4 1 3 1 5 3 7 4l12 3c-1 1-2 1-3 1-2-1-5-1-7-2-2 0-4-2-6-2v1 1c-1-1-1-2-2-3l-1-1h0z" class="G"></path><path d="M444 185c5 0 10 2 14 4h3 0c3 1 5 2 7 3 2 2 3 4 6 5l3 3 3 3h0c0 2 3 2 3 4-1 0-3-3-4-3-2-2-4-4-7-5 0 0-1 0-1-1-4-2-7-4-11-6l-8-3c-2-2-5-3-8-4z" class="N"></path><path d="M409 179v-1-1c2 0 4 2 6 2 2 1 5 1 7 2 1 0 2 0 3-1l10 2c2 2 4 2 6 3h3c3 1 6 2 8 4l8 3c-1 0-1 1-1 1h-3-3l-19-4c-4 0-8-1-13-1 0-1-1-1-1-2-3-1-5 0-8-2h1v-1c0-1 1 0 1 0h1 2c-1-1-1-1-2-1-2-1-2-1-3-2h-1c-1-1-1-1-2-1z" class="F"></path><path d="M425 180l10 2c2 2 4 2 6 3h-3-1c-1 0-2-1-4-1-3-1-8 0-11-3 1 0 2 0 3-1z" class="H"></path><path d="M434 189l-1-1c-2 0-3-1-5-1h0 6 0 2c2 2 7 0 10 1 2 1 4 1 6 1l8 3c-1 0-1 1-1 1h-3-3l-19-4z" class="C"></path><path d="M305 171h12l2 1c2 2 0 18 1 22l-1 25c0 5 0 11 1 16 0 1 1 1 1 1l1 2h-1c-3-1-6-3-7-7s-1-8 0-11l-3 3c-1 3-1 8-1 11 1 2 1 5 3 7l6 3v31 16c1 1 1 2 2 3s2 3 2 5c0 5-4 9-4 13 0 1 0 1 1 2s6 1 8 2v-70h2l-1 45v16c0 3 0 6 1 9h2l2-1v1c-1 1-2 2-3 2l-1-1c-3 0-13 1-14-1-2-1-2-2-2-4-1-4 1-9 2-13h-4c-1 4-4 11-3 16h0c1 3 2 5 4 6s4 2 6 2c1 5 0 11 0 16 0 3 0 5 1 7 1 3 3 4 4 6 2 3 2 5 3 7h1v4-4h2v16c1 1 1 1 2 1-3 2-8 3-12 3-2-1-4-1-5-3-2-2-2-5-1-8 0-3 4-10 1-13 0-1-1-2-2-2-2 8-5 16 0 25 1 2 3 5 6 6h0l7-1c2 0 4 0 6-1 4-2 6-5 10-5l1 1c0 1-2 2-3 4 0 1-1 0-1 1h0c-1 1-1 2-1 3l4-4c0-1 1-1 1-2 1-1 1-1 2-1l2-1v1c0 1-1 1-1 2-4 5-7 10-9 16-1 2-3 4-4 6h1l1-1 1 1-2 7v1c1 1 0 4 0 5-1 3-2 7-2 11h1c3-12 7-22 14-32 0 1 1 1 0 2v2c-3 7-8 14-9 22-1 1-1 1-1 2 0 3-1 4 1 5 1 2 1 2 1 4l1 1-1 3c-1 4-2 8-3 11-2 1-4 0-5 0h-17l-7-25-1-13-1-46c1-8 0-16 0-24v-31c0-5 1-10 0-15 0-1 0-2-1-3-1-2-2-4-2-6 0-6 3-10 3-15 1-11 0-23 0-34v-31-20-13z"></path><path d="M326 395c0-1 0-2 1-2 0 1 0 1 1 2l-2 7v-6-1z" class="U"></path><path d="M316 425c1-1 1-3 1-4v16c-1-3-1-5-1-8v-4zm23 11h0c-2-2-1-4-1-6 0 0-2-2-2-3h0v-2-1l1-1c0 3-1 4 1 5 1 2 1 2 1 4l1 1-1 3z" class="I"></path><path d="M327 434h0c0 2-1 3 1 5 1 2-1 4-1 6v1h-1v-6c0-2 0-4 1-6z" class="J"></path><path d="M326 395v-6-3s0-1 1-1c0 3 2 7 1 10-1-1-1-1-1-2-1 0-1 1-1 2z" class="K"></path><path d="M310 398l-1 15h0l-1-14 2-1z" class="U"></path><path d="M317 360h1v11 6c-2-6-2-10-1-17z" class="J"></path><path d="M327 359h1v4c0 3 1 10 0 13l-1-1v-16z" class="K"></path><path d="M317 406v15c0 1 0 3-1 4v-18c0 1 0 2 1 2v-3z" class="D"></path><path d="M342 377l1 1c0 1-2 2-3 4 0 1-1 0-1 1h0c-1 1-1 2-1 3l4-4c0-1 1-1 1-2 1-1 1-1 2-1l-10 12h-1v-2c0-1 2-2 2-4 2-2 4-5 6-7v-1zm-30-50c1-1 1-3 3-4 0 6 0 12-1 17l-1-13-1 1v-1z" class="S"></path><path d="M312 411h1 1v20c-1 0-1-1-1-2-1-4 0-8-1-11 0-2-1-5 0-7z" class="I"></path><path d="M315 216c0 1 1 3 1 4v-2c0-2 0-4 1-6h0v15c0 3 1 5 0 8 0-2-1-3-1-5s0-9-1-10v-4z" class="G"></path><defs><linearGradient id="n" x1="322.156" y1="437.971" x2="325.496" y2="428.477" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#97989a"></stop></linearGradient></defs><path fill="url(#n)" d="M326 428l-2 12v6h-1c-1-1 0-17 1-19v1h2z"></path><path d="M313 194c2 4 2 9 1 14 1-1 0-8 1-8 0 5-1 11 0 16v4l-2-2h0v-24z" class="W"></path><path d="M311 378l1 2c1 3 2 12 1 14v1c1 2 0 7 0 10h0 0c-1-2-2-8 0-10-1-1-2-4-1-6v-1c-1-1-1-3-1-4v-6z" class="F"></path><path d="M316 407c1-7-1-15 0-22v-1l1 1v4 17 3c-1 0-1-1-1-2z" class="X"></path><defs><linearGradient id="o" x1="323.964" y1="426.457" x2="326.996" y2="418.321" xlink:href="#B"><stop offset="0" stop-color="#a5a5a6"></stop><stop offset="1" stop-color="#c6c4c6"></stop></linearGradient></defs><path fill="url(#o)" d="M328 408v1c0 1-1 5-1 6v2h1l-2 11h-2v-1c0-6 2-13 4-19z"></path><path d="M308 294h0l1 1c1 1 1 2 1 4 0 5-2 10-2 15 0 2 1 3 2 4 0 2 0 4 1 5l-1 3c-1 0-1 1-2 2 0-4 1-8 0-12-1-7 1-14 0-22z" class="H"></path><path d="M315 200l1-14 1 1v6 13 6h0c-1 2-1 4-1 6v2c0-1-1-3-1-4-1-5 0-11 0-16z" class="J"></path><path d="M308 232l1-13c0-9-1-17 0-26 0-2 0-4 1-6l-1 45h-1zm5-14c-1 1-1 1-1 2h0-1c1-4 1-10 1-15v-14c0-2 0-5 1-7v10 24h0z" class="C"></path><path d="M308 399v-29-23h1c0 1 1 1 1 2 0 5-1 10-1 14v6l1 12v17l-2 1z" class="X"></path><path d="M322 366h0c2-2 2-4 4-6v17h-6v-5-1c1-1 2-2 2-3l-1-1 1-1z" class="J"></path><path d="M308 232h1l1 11v31c0 3 1 8 0 11h-1c0-3-1-6-1-9v-14-30z" class="S"></path><defs><linearGradient id="p" x1="332.326" y1="431.107" x2="325.932" y2="426.643" xlink:href="#B"><stop offset="0" stop-color="#aeaeac"></stop><stop offset="1" stop-color="#cdcbcf"></stop></linearGradient></defs><path fill="url(#p)" d="M331 418c1-3 1-6 3-8v1c1 1 0 4 0 5-1 3-2 7-2 11h1l-4 16v-8 1 1c-1 0-1 1-1 2-2-2-1-3-1-5h0c0-5 2-11 4-16z"></path><path d="M314 287h-1l-2-1v-18-25l3 3v41z" class="B"></path><path d="M314 246l2 2 1 42h-1s-1 0-1-2v-3h-1v2-41z" class="R"></path><path d="M304 279c0 2 1 4 2 5 3 5 11 6 14 12 3 5-1 11-2 16l-1 1c-1-2 3-8 2-10 0-1 0-2-1-2-1-3-4-4-6-6-3-1-8-4-8-7l-1-1c0-2 0-5 1-8z" class="B"></path><defs><linearGradient id="q" x1="336.183" y1="406.759" x2="328.848" y2="400.101" xlink:href="#B"><stop offset="0" stop-color="#dbd9dc"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#q)" d="M347 379c0 1-1 1-1 2-4 5-7 10-9 16-1 2-3 4-4 6h1l1-1 1 1-2 7c-2 2-2 5-3 8v-2l-2-2-1 3h-1v-2c0-1 1-5 1-6v-1c0-1 1-3 1-4 4-10 11-18 18-25z"></path><defs><linearGradient id="r" x1="328.769" y1="411.706" x2="337.027" y2="404.058" xlink:href="#B"><stop offset="0" stop-color="#1e1c1e"></stop><stop offset="1" stop-color="#343635"></stop></linearGradient></defs><path fill="url(#r)" d="M333 403h1l1-1 1 1-2 7c-2 2-2 5-3 8v-2l-2-2c1-3 2-8 4-11z"></path><path d="M312 327v1c-1 4-2 10 1 15 2 3 6 5 8 8 3 2 3 6 3 9l-2 6-1 1 1 1c0 1-1 2-2 3 0-3 1-6 1-9-1-3-2-6-5-8-3-3-7-6-8-10-1-6 0-12 4-17z" class="B"></path><defs><linearGradient id="s" x1="329.036" y1="407.056" x2="312.877" y2="394.586" xlink:href="#B"><stop offset="0" stop-color="#838082"></stop><stop offset="1" stop-color="#c6c6c6"></stop></linearGradient></defs><path fill="url(#s)" d="M319 384l7-1 1 2c-1 0-1 1-1 1v3 6 1 6l-4 14-2 11h0c-1-5 0-10 0-15 0-10 0-19-1-28z"></path><path d="M322 416c0-1 0-3-1-3v-1c0-4 2-6 4-9 0-2 0-5 1-7v6l-4 14z" class="G"></path><path d="M107 169c1 1 2 2 2 4 1 3 2 6 2 9s1 7 2 10v1c0 1 1 7 0 8 0 0 0 1-1 1l3 24v20c0 2 0 6 1 8h0l2 2c1 10 2 20 5 30l2 2c2 3 3 8 6 10 2 2 3 5 5 7 1 0 0 0 1-1l-1-1v-2h0-1v-2c-1-1-1-3-2-4l-1-4 1-1c0-1-1-2-1-2 0-1 0-1-1-2v-1-3-1-5-3-15c1-2 1-5 2-7 1-7 3-13 7-18 0-2 3-5 4-6h2l5-5 1-1c1-1 2-2 4-2l1 3-1 1v2h1l2 1c1 1 1 3 2 4v1 1l-2 2h1l1-1c1 1 1 1 1 2v4c-2 1-7 4-8 6h0c0 1-1 1-1 2-5 3-8 8-10 13l1 1c1-1 1-3 2-4 1-2 2-3 3-4-1 3-3 5-4 8 2-1 2-2 4-3 1 0 1-1 2-2 0 1 0 2-1 2-1 1-1 2-2 3h1c0-1 1-2 2-2v-1l3-3c4-5 10-8 16-9 2 0 4-1 5 0v1l-2 1c-4 1-7 4-9 7-3 1-5 4-7 6-1 2-1 3-2 4h0c-1 2 0 4-1 6v1c1 1 0 1 0 1 0 2 1 3 2 4-2 3-2 6-2 10 1-1 2-2 4-1 1 0 1 0 1 1l-1 1c-2 0-3 0-4 2-1 6 3 14 5 19l7 13v2c0 1 1 1 1 2l8 11c1 2 3 4 4 6 0 1 1 2 2 3 2 1 5 5 7 6h2v-1c3 0 5 3 7 4v-1l4 2 6 3v1 2l17 8c3 1 4 5 5 8-7-5-14-8-21-11l-16-9h-2c-1 0-1 0-1-1-1 0-2 0-2-1-1 0-1 0-2-1-1 0-2 0-2-1-2-1-5-4-6-4v1 1 1l6 4c1 1 3 2 4 3s1 2 3 3l-1 1c0-1 0-1-1-1h-4c-1-1-2-2-4-3l-9-7-6-4c-2-1-3-2-3-3l-3-2c-2-1-3-2-5-1-1-1-3-2-4-3-7-6-12-14-17-22 0-1 0-1-1-2v-1l2 1h0l-10-9v-1l-2-1-11-8c-2-2-4-4-7-5-1-1-3-1-4-1-3 0-6 0-9 1-2 1-4 3-5 5h1l1-1c2-2 5-2 7-2-3 1-6 2-9 4 0 2-1 4 0 5 0 1 1 1 1 1 0 1-1 2-1 2-3-1-1-6-1-8 0-1-1-3-2-4 1-2 1-4 2-6 3-4 8-5 13-6l-1-4-4-1 1-2c2-1 3-1 4-1s1 0 2 1c2 2 2 6 1 9 1-1 2-3 3-4 1-2 2-4 4-6l2 1c1 1 3 2 3 4v1c-1 0-1-1-1-1h-3c-1 1-2 2-3 2 1 1 2 2 3 2l6 3c3 2 5 4 8 6-3-5-4-9-6-14-2-9-4-20-9-28-1-3-6-10-10-11-1 1-2 1-3 2 1 4 3 8 4 12-1-1-2-3-3-5l-6-7c2-2 3-4 5-7 4-5 7-11 10-16 0-1 1-2 1-3 0-2-2-6-3-8v-1c-3-4-3-10-8-13-3-1-5-3-8-2s-6 7-7 10c0 2 2 4 3 5 0 1-1 2-1 2h-1c-1-2-3-4-4-7 0 0 0-2-1-3 0 0-5-4-6-5 2-4 5-9 8-13s8-5 13-5c1-1 4-1 6-1 0-1 1-1 2-1h1l4 4h0v-2c0-1 1 0 2-1l-1-3s1 1 1 2l1-1v-3z"></path><path d="M107 232h0 1l-1-3c-1-1 0-2 0-3h1l1 10c-1-1-2-2-2-4z" class="D"></path><path d="M113 192v1c0 1 1 7 0 8 0 0 0 1-1 1l-1-10c0 1 1 1 1 2h1v-2z" class="J"></path><path d="M146 275c0-3 1-6 2-9 0 1 1 2 1 2 0 3-2 6-2 8l-1 9c0-2-1-4-1-7 0-1 1-2 1-3z" class="Y"></path><path d="M92 188v-1-1c1 0 2 0 3 1h2l3 3-1 1s-1 0-2-1v1l1 1h-1c-1 0-1-1-2-1v-1c-1 0-2-1-3-2z" class="I"></path><path d="M103 202c2 4 3 9 6 13-1 2-2 3-2 4h0v-1-2-2l-2 4c0-1 1-2 1-3 0-2-2-6-3-8v-1h1l1 4h0v-1-1c-1-1-1-2-1-3-1-1-1-2-1-3h0z" class="O"></path><path d="M157 323c0 1 1 2 2 3 0 1 0 2 1 3s2 3 3 4c0 1 1 1 1 2l4 4c-5-2-10-9-13-14 1 0 1-1 2-2z" class="F"></path><path d="M190 352v-1c3 0 5 3 7 4v-1l4 2 6 3v1 2c-2-1-4-2-5-3-4-3-8-5-12-7z" class="J"></path><path d="M136 301c0 1 1 1 1 2 1 2 2 3 3 4l1 1h1 0 0l5 10c-2-2-4-4-5-6-2-2-5-4-6-7 1 0 0 0 1-1l-1-1v-2h0z" class="H"></path><path d="M134 267h1v6h0c1 7 2 14 4 21 0 3 2 7 4 10l1 2c-2 0-5-7-6-9v-1c0-3-2-5-2-8-2-7-2-14-2-21z" class="G"></path><path d="M92 181c1 1 2 1 3 1h0 1l-1 1c2 2 5 4 7 6l-1 1c-1-2-4-5-6-5 0 1 1 1 2 2h-2c-1-1-2-1-3-1v1 1c-2-1-4 0-6-1v-1c2-1 3-1 5-2h1 1l-1-1c-1 0 0 0-1 1h-2v-1h3v-2z" class="D"></path><path d="M107 219c1 2-1 6-2 9l-2 2-2 4c-1 2-3 3-5 4h-1c0-1 0-1 1-1 4-6 9-12 11-18h0z" class="S"></path><path d="M103 230l2 3c1 1 3 3 3 4 1 2 1 3 1 4-1-1-1-2-2-2-2-1-3-3-4-3 1 2 2 3 3 5-1-1-3-2-4-4h-1c0 1 0 1 1 2h-1 0c-2-1-4-1-5-1h-1 1c2-1 4-2 5-4l2-4z" class="K"></path><path d="M109 215c1 3 1 6 2 9v2h0l1-1v4h0c-1 0-1-1-1-2h0c0-1 0-1-1-1h0l-1-1c0 1 0 1-1 1h-1c0 1-1 2 0 3l1 3h-1 0l-2 1-2-3 2-2c1-3 3-7 2-9 0-1 1-2 2-4z" class="Q"></path><path d="M142 296l1-1v1l2 2 3 8s1 2 1 3h-1c1 2 2 5 3 7v1c0 1 0 1-1 2-2-4-4-9-5-13s-2-7-3-10zm-35-127c1 1 2 2 2 4 1 3 2 6 2 9s1 7 2 10v2h-1c0-1-1-1-1-2l-2-10-3-9 1-1v-3z" class="H"></path><path d="M107 169c1 1 2 2 2 4v3s1 0 1 1v3 1c-1 0-1 1-1 1l-3-9 1-1v-3z" class="F"></path><defs><linearGradient id="t" x1="139.847" y1="293.617" x2="134.142" y2="302.158" xlink:href="#B"><stop offset="0" stop-color="#d7dad6"></stop><stop offset="1" stop-color="#f2eef3"></stop></linearGradient></defs><path fill="url(#t)" d="M132 291l1-1c0-1-1-2-1-2 0-1 0-1-1-2v-1-3c1 1 1 3 2 4h1l1 3 3 9 4 10h0-1l-1-1c-1-1-2-2-3-4 0-1-1-1-1-2h-1v-2c-1-1-1-3-2-4l-1-4z"></path><path d="M132 291l1-1c0-1-1-2-1-2 0-1 0-1-1-2v-1-3c1 1 1 3 2 4h1l1 3c-1 0-1 0-2 1v2c1 0 1 1 1 1l1 3v3c-1-1-1-3-2-4l-1-4z" class="L"></path><path d="M141 244h1c1-1 1-2 2-2h1v-1c0-1 3-3 4-3-9 10-14 21-14 35h0v-6h-1c0-7 2-14 4-21 1 0 2-1 3-2z" class="K"></path><path d="M91 175c1-1 4-1 6-1 0-1 1-1 2-1h1l4 4h0v-2c0-1 1 0 2-1l3 14c-1-1-2-4-3-5-2-1-4-4-5-5 1 3 3 5 5 7 0 2 1 3 1 4h0l-6-8c-1-1-1-3-3-4-1-1-2-1-3-2h-4z" class="C"></path><path d="M103 202h0l-3-6 8 4c2 8 3 16 4 25l-1 1h0v-2c-1-3-1-6-2-9-3-4-4-9-6-13z" class="B"></path><path d="M148 306h2c2 5 5 11 8 15l3 6c1 1 2 3 3 4v2c2 0 3 2 4 3l1 1c-2-1-3-3-5-2 0-1-1-1-1-2-1-1-2-3-3-4s-1-2-1-3c-1-1-2-2-2-3-1 1-1 2-2 2l-5-6c1-1 1-1 1-2v-1c-1-2-2-5-3-7h1c0-1-1-3-1-3z" class="G"></path><path d="M150 319c1-1 1-1 1-2v-1c-1-2-2-5-3-7h1l8 14c-1 1-1 2-2 2l-5-6z" class="M"></path><path d="M167 326s-1 0-1 1l1 1-1 1c-1-1-3-5-4-6 0-1 0-1-1-2l-1-2-4-8v-1h0l-1-2v-1s0-1-1-1v-2c-1-1-1-1-1-2v-1c-1-1-1-2-1-3l-1-6c-1-1 0-2-1-2v-8-3h1v1 7c1 2 1 5 1 7 1 1 0 1 1 1v3h0c0 1 0 1 1 2v2l1 1v1 1l1 1v1l1 1c1 1 1 1 2 1l7 13v2c0 1 1 1 1 2z" class="Q"></path><path d="M95 191c-3-2-7-3-10-2s-5 6-6 8l-4-4c3-6 4-11 10-15 2-2 7-2 10-1s5 5 7 8c-3-3-6-5-10-5-1 0-2 1-3 2 1 0 2 0 3-1v2h-3v1h2c1-1 0-1 1-1l1 1h-1-1c-2 1-3 1-5 2v1c2 1 4 0 6 1 1 1 2 2 3 2v1z" class="B"></path><path d="M140 266h0-1v4l-1-1v-4c1-1 1-4 1-5 1-2 1-3 2-4 0-2 2-5 3-7 0-1 0-2 1-2v-1c1-1 2-1 2-2 1-2 3-4 5-5l1-1 1-1c1-1 2-2 3-2 1-1 1-1 2-1h1l1-1c1 1 1 1 1 2v4c-2 1-7 4-8 6h0c0 1-1 1-1 2-5 3-8 8-10 13l-3 6z" class="I"></path><defs><linearGradient id="u" x1="143.978" y1="290.234" x2="147.689" y2="289.964" xlink:href="#B"><stop offset="0" stop-color="#565757"></stop><stop offset="1" stop-color="#79787a"></stop></linearGradient></defs><path fill="url(#u)" d="M143 260l1 1c1-1 1-3 2-4 1-2 2-3 3-4-1 3-3 5-4 8 2-1 2-2 4-3 1 0 1-1 2-2 0 1 0 2-1 2-1 1-1 2-2 3h1c0-1 1-2 2-2v-1 1 1l-3 6c-1 3-2 6-2 9 0 1-1 2-1 3 0 3 1 5 1 7l2 11c0 3 1 7 2 10h-2l-3-8-2-2v-1l-1 1c-1-1-1-4-1-5l-1-4c-1-8-1-14 0-21l3-6z"></path><path d="M143 270h0 0v4h1v-3h1v2c0 1 0 1 1 2 0 1-1 2-1 3v-2h0 0l-1 2v-1-1 3 1h0c0-1-1-1-1-2h0c-1 2 0 3 0 5-1-3-1-7-1-10 0-1 1-2 1-3z" class="R"></path><defs><linearGradient id="v" x1="139.964" y1="273.4" x2="143.326" y2="295.253" xlink:href="#B"><stop offset="0" stop-color="#827f81"></stop><stop offset="1" stop-color="#b6b6b6"></stop></linearGradient></defs><path fill="url(#v)" d="M141 278v-8c1 0 1-1 1-2h0c0-1 0-1 1-2h1v1l-1 1v1 1c0 1-1 2-1 3 0 3 0 7 1 10 0 5 2 10 2 15l-2-2v-1l-1 1c-1-1-1-4-1-5l-1-4c2-2 1-6 1-9z"></path><path d="M141 278c1 3 0 3 0 6 0 2 0 4 1 5l-1 2-1-4c2-2 1-6 1-9z" class="K"></path><path d="M143 260l1 1c1-1 1-3 2-4 1-2 2-3 3-4-1 3-3 5-4 8 2-1 2-2 4-3 1 0 1-1 2-2 0 1 0 2-1 2-1 1-1 2-2 3h1c0-1 1-2 2-2v-1 1 1l-3 6c-1 3-2 6-2 9-1-1-1-1-1-2v-2h-1v3h-1v-4h0 0v-1-1l1-1v-1h-1c-1 1-1 1-1 2h0c0 1 0 2-1 2v8c0 3 1 7-1 9-1-8-1-14 0-21l3-6z" class="U"></path><path d="M152 221c1-1 2-2 4-2l1 3-1 1v2h1l2 1c1 1 1 3 2 4v1c-2 0-4 0-6 1l-6 6c-1 0-4 2-4 3v1h-1c-1 0-1 1-2 2h-1c-1 1-2 2-3 2-2 7-4 14-4 21s0 14 2 21c0 3 2 5 2 8v1 1l-3-9-1-3h-1c-1-1-1-3-2-4v-1-5-3-15c1-2 1-5 2-7 1-7 3-13 7-18 0-2 3-5 4-6h2l5-5 1-1z" class="Q"></path><path d="M151 226v2l-1 1h-2l3-3z" class="N"></path><path d="M131 276c1 1 2 2 2 3l1 7h-1c-1-1-1-3-2-4v-1-5z" class="G"></path><path d="M140 233c0-2 3-5 4-6h2c-3 4-6 9-8 14h-1c1-2 1-3 2-5 0-1 1-2 1-3z" class="U"></path><path d="M152 221c1-1 2-2 4-2l1 3-1 1-5 5v-2c0-1 0-2 1-3l-1-1 1-1z" class="D"></path><defs><linearGradient id="w" x1="150.337" y1="233.882" x2="137.49" y2="239.79" xlink:href="#B"><stop offset="0" stop-color="#585652"></stop><stop offset="1" stop-color="#7d7e82"></stop></linearGradient></defs><path fill="url(#w)" d="M148 229h2l-1 2c-3 3-4 7-7 11 0 0-1 1-1 2-1 1-2 2-3 2 2-6 6-13 10-17z"></path><defs><linearGradient id="x" x1="134.777" y1="260.728" x2="131.306" y2="260.094" xlink:href="#B"><stop offset="0" stop-color="#363638"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#x)" d="M140 233c0 1-1 2-1 3-1 2-1 3-2 5h1c-1 5-3 10-4 15-2 7-1 16-1 23 0-1-1-2-2-3v-3-15c1-2 1-5 2-7 1-7 3-13 7-18z"></path><path d="M156 223v2h1l2 1c1 1 1 3 2 4v1c-2 0-4 0-6 1l-6 6c-1 0-4 2-4 3v1h-1c-1 0-1 1-2 2h-1c0-1 1-2 1-2 3-4 4-8 7-11l1-2 1-1 5-5z" class="G"></path><path d="M142 242c3-4 4-8 7-11 1 1 0 2 0 3l-3 3c-1 2-2 4-3 5h-1z" class="T"></path><path d="M156 225h1l2 1c1 1 1 3 2 4v1c-2 0-4 0-6 1h-2c-1 1-1 0-1 0 1-2 3-3 4-4v-3z" class="E"></path><path d="M159 226c1 1 1 3 2 4h-2c-1-2-2-2-2-5l2 1z" class="L"></path><path d="M100 290c-5 0-10 1-14 4h-1c0-2 1-4 2-6 3-3 5-3 9-4 9 0 17 3 23 9 7 6 13 13 20 20l11 12 8 7c0 1 2 2 2 2 0 1 0 1 1 2 0 1 3 2 4 3l3 3 3 3c-3-2-5-4-7-5-1-1-2-2-4-3-1-1-2-2-3-2 0 0-3-3-4-3-8-8-15-16-23-24l-6-5-2 2-11-8c-2-2-4-4-7-5-1-1-3-1-4-1v-1z" class="B"></path><path d="M100 290c10 2 17 5 24 13l-2 2-11-8c-2-2-4-4-7-5-1-1-3-1-4-1v-1z" class="I"></path><path d="M124 303l6 5c8 8 15 16 23 24 1 0 4 3 4 3 1 0 2 1 3 2 2 1 3 2 4 3 2 1 4 3 7 5l-3-3-3-3c-1-1-4-2-4-3-1-1-1-1-1-2 3 2 6 4 8 7 3 2 5 5 8 7s6 5 10 7l6 3h-2c-1 0-1 0-1-1-1 0-2 0-2-1-1 0-1 0-2-1-1 0-2 0-2-1-2-1-5-4-6-4v1 1 1l6 4c1 1 3 2 4 3s1 2 3 3l-1 1c0-1 0-1-1-1h-4c-1-1-2-2-4-3l-9-7-6-4c-2-1-3-2-3-3l-3-2c-2-1-3-2-5-1-1-1-3-2-4-3-7-6-12-14-17-22 0-1 0-1-1-2v-1l2 1h0l-10-9v-1l-2-1 2-2z" class="C"></path><path d="M162 344l6 5c-1 0-2 0-3-1v1c-2-1-3-2-3-3v-2z" class="R"></path><path d="M155 339l7 5v2l-3-2c-2-2-4-3-4-5z" class="P"></path><path d="M165 349v-1c1 1 2 1 3 1l12 8v1c-3-1-6-4-8-5h-1l-6-4z" class="U"></path><path d="M180 357l5 3h1c-1-1-3-2-4-3l-6-3c-4-3-8-6-11-9v-1l11 9h1l6 4c1 1 3 2 4 3s1 2 3 3l-1 1c0-1 0-1-1-1h-4c-1-1-2-2-4-3l-9-7h1c2 1 5 4 8 5v-1z" class="K"></path><path d="M124 303l6 5c8 8 15 16 23 24 1 0 4 3 4 3 1 2 3 3 4 4l7 5h0c-2-1-4-2-5-3-3-2-6-5-9-7l-17-17c-2-1-4-3-6-4-2-2-5-4-7-7l-2-1 2-2z"></path><path d="M133 318c0-1 0-1-1-2v-1l2 1h0c5 3 8 9 12 13 2 4 6 7 9 10 0 2 2 3 4 5-2-1-3-2-5-1-1-1-3-2-4-3-7-6-12-14-17-22z" class="I"></path><path d="M213 546h0c4 0 9 5 12 8 0-4 1-7 2-10 1-4 4-8 5-13l-3 3c-3 4-4 8-5 12 0 2 0 4-1 6-1-8 2-16 5-22-3-1-9-2-10-5-1-2-2-6-2-7 1-1 2-2 2-3l-1-1c-1-2-1-5-1-7 1-3 3-8 6-10 5-5 10-6 17-5-2-2-4-6-6-7-10-5-22-7-32-7-5 1-11 1-14 5-2 3 7 14 9 18 0 1 1 2 1 3 0 2 0 3-1 4 0-2 0-4-1-6s-3-5-5-6l1 8c0 2 0 4-1 6-2 2-5 3-8 3h0c3-1 5-2 6-5 1-2 1-4 0-6 0-4-1-7-3-10l-6-12c-3-8-4-15 0-23 1-4 5-7 9-9 7-2 12-1 18 2s11 6 16 9v1h-1-11-2l-4 1c-3 1-5 1-7 2v1c-3 1-6 3-8 5 1 1 3 1 5 1 2 1 3 1 4 2h3c1 1 3 1 3 1 1 0 1 2 2 2 3 0 7 1 11 2 9 2 18 6 26 10 1 1 3 2 4 3h0c-1 1-3 1-5 1h1c0 1 1 1 1 2h0-3v1h1 1c1 1 3 2 4 3-6-1-16-3-21 0l-1 1h4c3 0 7-1 10 0h0c2 1 5 1 7 2 6 1 11 4 17 7l3 1c2 2 5 4 7 5h0l3 6 2 10c-3 3-7 7-11 9-2 2-5 3-7 4s-4 2-5 3c1 1 2 2 3 2h1l-1 1c0 1 1 1 1 1l2 1v1h0c4 2 8 3 11 6 9 6 12 16 15 26 0 2 1 4 2 6v2 2l-3-2c-1 2-1 4-2 6-1 1-1 3-2 4l-2 2c-5 5-11 6-17 6-15-1-32-13-38-27-1-2-5-15-5-15-1-1-2-1-3-2-3-1-6-3-9-6 2-2 2-3 2-5v-1h0c2 2 3 4 4 6 3 5 9 5 14 7-1-2-3-5-4-7l-9-9c1 0 3 2 4 3v-1-1h0 1c-2-2-5-3-7-5z"></path><path d="M249 564l1 1c1 0 1 1 3 1v1c-1 0-4 0-5-1h0c0-1 1-1 1-2z" class="E"></path><path d="M245 560c1 0 2 1 3 2 0 1 0 2 1 2 0 1-1 1-1 2h0c-2-1-3-5-3-6z" class="G"></path><path d="M258 512c1 1 4 2 6 3v1c1 0 3 1 3 2h0l-2-1h-2v1c-2-1-3-2-5-3v-1-1-1z" class="Q"></path><path d="M220 551c1 1 3 2 4 4s2 6 4 6l1 1s-1 0-1 1c-1-2-2-4-4-5-1-2-3-3-5-5v-1-1h0 1z" class="H"></path><path d="M251 561c2 2 4 4 5 7h0l-3-1v-1c-2 0-2-1-3-1l-1-1c-1 0-1-1-1-2 1-1 1 0 2 0l1-1z" class="F"></path><path d="M246 570c2-1 4 0 5 0l-3 6v2h-1l-1-1v-3c1-1 0-3 0-4z" class="K"></path><path d="M179 472h1l1-4h1c-1 1-1 3-1 4 1-1 3-4 3-4 1 0 2 1 2 1l-5 7c-1 0-2-2-2-3v-1zm66 88l-5-7c4 2 8 4 11 8l-1 1c-1 0-1-1-2 0-1-1-2-2-3-2z" class="L"></path><path d="M252 504c5 2 9 3 14 6 0 0 1 1 2 1-2 0-5-2-7-2 0-1-1-1-2-1h0-2l-1-1-1 1c0 1 0 1-2 1h-1 0c-2 0-4-1-5-1l1-1h1 1l-1-1s-1 0-2-1h1s2 0 3 1h2c0-1-1-2-1-2z" class="C"></path><path d="M252 509h1c2 0 2 0 2-1l1-1 1 1h2 0c1 0 2 0 2 1h1c0 1 2 2 3 3 2 0 3 1 4 3-2-1-6-4-9-4h0c3 2 6 3 9 6-2-1-4-2-5-2-2-1-5-2-6-3h-3l1-1c-1 0-2-1-2-1l-2-1z" class="S"></path><path d="M244 517h2l-1-1c-1 0-1 0-1-1-2 0-3 0-4-1h0 1 3c1 1 2 1 3 1h1 0l1 1c1 1 3 1 4 1 2 1 6 2 7 3-2 0-9-2-10-2 3 2 7 2 9 4-1 1 0 1-1 1s-2-1-3-1h0l-2-1h-2-1c-1-1-1-2-2-2l-4-1v-1z" class="C"></path><path d="M248 595c-2-1-5-2-6-4s-4-4-6-5c-2-2-4-5-6-8v-1c2 1 2 2 3 4s4 4 7 6l2 2h4c2 1 5 1 7 2h-2c0 1 0 1 1 2h1-2c-1-1-1-1-2-1v1 1h0s-1 0-1 1z" class="P"></path><path d="M206 475h-11c2-1 4-1 5-2h-6c-5 1-8 3-11 7 1-5 2-7 6-11 1 1 3 1 5 1 2 1 3 1 4 2h3c1 1 3 1 3 1 1 0 1 2 2 2z" class="H"></path><path d="M237 581l-2-2h-1c2 4 8 6 10 10-3-2-6-4-8-6l-2-2c-2-4-5-7-8-11 0 0-1-1-1-2h2s1 0 2 1h-1c2 2 4 3 6 5 2 1 3 3 5 5h-1l2 2v1c-1-1-1-1-2-1h-1z" class="P"></path><path d="M250 511c3 1 6 2 8 4 2 1 3 2 5 3l2 1c1 0 3 1 4 1h0c1 1 1 1 2 1 1 1 1 2 1 4-1 0-5-2-6-3h-2 0c-1 0-1-1-2-1h-1c-1-1-1-1-1-2s-1-1-1-1c0-1-2-1-2-2-3-1-6-1-8-3-1 0-1 0-2-1h2l1-1z" class="I"></path><path d="M250 511c3 1 6 2 8 4 2 1 3 2 5 3l2 1h-2c-1 0-2-2-3-2-4-2-8-3-11-4-1 0-1 0-2-1h2l1-1z" class="C"></path><path d="M228 561l-1-6c4 3 8 9 9 14l1 3h2v1h2 1c0 1 1 3 2 4-2-1-3-2-5-3-1 0-2-1-3-1-3-2-6-7-8-10 0-1 1-1 1-1l-1-1z" class="F"></path><path d="M221 506c10-2 20 2 29 5l-1 1h-2c1 1 1 1 2 1 2 2 5 2 8 3 0 1 2 1 2 2 0 0 1 0 1 1l-12-4h0l-1-1v-1c-1-1-4-2-6-2-4-1-9-2-13-3-3 0-5 0-7-1h-1 0l1-1z" class="M"></path><path d="M271 597h0l1-1c2 0 3-1 4-3l-1 3c-1 2-4 4-6 4-6 2-13 1-19-1l-1 3-6-4c-2-1-3-3-4-5l11 4c2 1 6 2 8 2h1c3 1 9 0 12-2z" class="E"></path><path d="M243 598l1-1h0 1v1c1 0 2-1 2 0h1c1 0 2 1 2 1l-1 3-6-4z" class="L"></path><path d="M223 517h1c1 1 2 1 3 1s2 1 3 2h0 2c0-1 0-2 1-3 2-1 2 0 4-1h4c2 0 2 0 3 1v1l4 1c1 0 1 1 2 2h1 2l1 1v1h-2 0l1 1h-1s-1-1-2-1h0c-1 0-3-1-4-1-3-1-6-1-8-2h-2c1 1 2 1 3 1 2 0 5 1 7 2 5 1 9 3 14 5h-3c-4-1-8-3-12-5-2 0-4 0-6-1-1-1-9-1-11-2-2 0-3 0-5-1h-2c0-1 1-1 2-2z" class="M"></path><path d="M274 587c1-1 3-3 3-5 1-3 0-6-1-9 2 1 4 4 6 6l-1 1c2 4 3 8 2 12v-3l-5 2-2 2c-1 2-2 3-4 3l-1 1h0c-3 2-9 3-12 2h-1c2 0 3 0 4-1h-2c3 0 6 0 9-1 3-2 5-6 6-9h-1v-1z" class="I"></path><path d="M271 597c1-2 3-3 4-5s1-4 2-5 2-2 3-4c0 2-1 4-1 5s-1 2-1 3l-2 2c-1 2-2 3-4 3l-1 1h0z" class="P"></path><path d="M280 583v-4h0l1 1c2 4 3 8 2 12v-3l-5 2c0-1 1-2 1-3s1-3 1-5z" class="G"></path><path d="M221 519h2c2 1 3 1 5 1 2 1 10 1 11 2v1c2 1 4 1 5 1 2 0 4 2 5 2l8 3c1 1 3 1 4 2-1 0-2-1-4-1-3-1-6-2-8-3-2 0-3-1-4-1-1-1-3-1-4-1l-6-2c3 3 7 3 10 4s6 3 9 4h0c-3 0-6-3-10-3h-3c-1 0-3-1-5-1l-16-3h1 2v-1c-1 0-1 0-2-1v-2h-1l1-1z" class="V"></path><path d="M221 519h2v1h-1v1h1c1 0 2 1 3 2 2 0 6 0 8 1 2 2 6 3 8 4h2 0-3c-1 0-3-1-5-1l-16-3h1 2v-1c-1 0-1 0-2-1v-2h-1l1-1z" class="O"></path><path d="M179 472c0-4 0-8 2-12s6-7 10-9c4-1 9-1 13 1l-1 1c-7 1-15 5-20 11-1 1-1 3-2 4l-1 4h-1zm41 52c6 1 11 2 16 3 2 0 4 1 5 1 2 1 3 1 5 2s5 2 7 3h1c-1 1-4-1-6-1h0c1 1 2 1 3 2h0c-4-1-6-2-10-3 2 1 3 2 5 3s3 3 5 4h-4c-6-1-12-6-18-9-1-1-3-2-5-3-1 0-3 0-4-2zm3-7h-2v-1h2c-1-1-2-1-3-2 0-1-1-1 0-2v-1c1-1 3-1 4-1l1 2h0l2-1c4 0 8 0 12 1 3 1 5 1 8 2l1 1h-1c-1 0-2 0-3-1h-3-1 0c1 1 2 1 4 1 0 1 0 1 1 1l1 1h-2c-1-1-1-1-3-1h-4c-2 1-2 0-4 1-1 1-1 2-1 3h-2 0c-1-1-2-2-3-2s-2 0-3-1h-1z" class="B"></path><defs><linearGradient id="y" x1="272.671" y1="591.428" x2="254.188" y2="596.998" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#888788"></stop></linearGradient></defs><path fill="url(#y)" d="M274 588h1c-1 3-3 7-6 9-3 1-6 1-9 1h0l-5-1-7-2c0-1 1-1 1-1h0v-1-1c1 0 1 0 2 1h2-1c-1-1-1-1-1-2h2s1 0 1 1l1-1c3 0 5 0 7-1 1 0 4-1 5-1h1c0 1 0 1 1 1l5-2z"></path><path d="M255 595c1 1 2 1 3 2h-1c1 0 2 0 3 1l-5-1v-2z" class="U"></path><path d="M253 591s1 0 1 1h3l-1 2-3-1h-1c-1-1-1-1-1-2h2zm-5 4c0-1 1-1 1-1h0v-1-1c1 0 1 0 2 1h-1c1 1 4 2 5 2h0v2l-7-2z" class="R"></path><path d="M267 589h1c0 1 0 1 1 1-4 1-8 3-12 2h-3l1-1c3 0 5 0 7-1 1 0 4-1 5-1z" class="D"></path><path d="M240 581l-2-2h1c-2-2-3-4-5-5-2-2-4-3-6-5h1c2 1 3 3 5 4s5 2 7 3c3 2 5 5 9 6l17 7c-1 0-4 1-5 1-2 1-4 1-7 1-3-1-8-2-10-4-3-1-6-4-8-6h1c1 0 1 0 2 1v-1z" class="E"></path><path d="M237 581h1c1 0 1 0 2 1v-1c2 2 4 4 7 5 2 1 5 2 8 3 2 1 4 1 7 1-2 1-4 1-7 1-3-1-8-2-10-4-3-1-6-4-8-6z" class="R"></path><defs><linearGradient id="z" x1="261.737" y1="592.384" x2="269.968" y2="605.731" xlink:href="#B"><stop offset="0" stop-color="#cfcdce"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#z)" d="M278 591l5-2v3c-1 4-2 8-6 11-5 3-14 3-20 2-2-1-5-2-8-3l1-3c6 2 13 3 19 1 2 0 5-2 6-4l1-3 2-2z"></path><path d="M220 504c1-2 3-4 5-6h4c3 0 7-1 10 0h0l-1 1c5 1 10 3 14 5 0 0 1 1 1 2h-2c-1-1-3-1-3-1h-1c1 1 2 1 2 1l1 1h-1-1l-1 1c1 0 3 1 5 1h0l2 1s1 1 2 1l-1 1h3v1 1 1c-2-2-5-3-8-4-9-3-19-7-29-5h0l-1-1v-1z" class="B"></path><path d="M220 504c1-2 3-4 5-6h4c3 0 7-1 10 0h0l-1 1c-4-1-9-1-13 1 0 1 0 2-1 3 0 1 1 0 2 1h-6 0z" class="E"></path><path d="M226 504c10 1 20 5 29 8h3v1 1 1c-2-2-5-3-8-4-9-3-19-7-29-5h0l-1-1v-1h0 6z" class="O"></path><path d="M206 450c6 3 11 6 16 9v1h-1-11-2l-4 1c-3 1-5 1-7 2-4 1-8 3-11 6 0 0-1-1-2-1 0 0-2 3-3 4 0-1 0-3 1-4h-1c1-1 1-3 2-4 5-6 13-10 20-11l1-1s1-1 2-1v-1z" class="B"></path><path d="M209 457c3 0 10 1 12 3h-11-2c1-1 4-1 5-1h1 1c-1 0-2-1-3-1s-2 0-3-1h0z" class="H"></path><defs><linearGradient id="AA" x1="210.098" y1="452.406" x2="206.084" y2="459.297" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#3d3c3c"></stop></linearGradient></defs><path fill="url(#AA)" d="M206 450c6 3 11 6 16 9v1h-1c-2-2-9-3-12-3-7-1-14-2-20 3-3 2-5 5-7 8h-1c1-1 1-3 2-4 5-6 13-10 20-11l1-1s1-1 2-1v-1z"></path><g class="B"><path d="M239 572c-1-7-3-11-7-15l-3-3v-2c1-7 3-12 5-18 2 2 4 3 6 4s7 3 8 4v1c1 4 1 8 2 11 1 4 5 8 7 11h0c1 2 1 3 1 5v1h-1v-3-1c-1-2-2-3-4-5-1-2-3-6-6-7l-9-4c1 5 1 11 2 16 1 2 1 4 2 6h-1-2v-1z"></path><path d="M274 587c-8 1-14-3-21-5l4-8c1-2 2-4 2-6 1-3-3-6-4-9-3-3-2-8-1-12l7 4h0c4 2 8 3 11 6 9 6 12 16 15 26 0 2 1 4 2 6v2c-1-1-3-2-3-3-1-1-1-4-2-5 0-1-1-3-2-4-2-2-4-5-6-6 1 3 2 6 1 9 0 2-2 4-3 5z"></path></g><path d="M404 459c5-3 9-6 15-8 5-3 9-5 15-4 4 1 8 3 11 7 4 5 5 12 4 18-1 4-2 7-4 11-3 7-10 15-8 24h0c0 2 2 3 4 5l2 1h0c-3 0-5-1-7-3-1-1-2-3-2-5v-9c-3 4-5 6-4 12v2c-2-2-2-4-2-6 1-6 6-11 9-15 0-1 2-3 2-4s-1-2-1-2c-4-4-12-5-16-5-9 0-17 1-24 4-3 1-5 2-7 4s-4 4-5 6c6-1 13 0 18 4 3 3 5 7 5 11 0 2 1 5 0 6l-2 2c1 2 3 3 2 5 0 2-1 5-3 7-3 1-7 1-10 3 3 4 5 9 6 14 1 2 1 4 1 6l-1 1-1 1v-1c1-4-3-13-5-17-1-1-1-1-2-1h-1 0v1c3 4 5 9 6 14 0 1 1 4 1 5s0 1-1 1c0 1-1 1-1 1h0v2c0 4-3 7-4 11 1 0 2 0 2-1 6-3 11-3 15-9 1-1 2-4 4-5v1c0 1 0 4 1 5h1c-1 3-5 5-7 6-1 0-3 1-4 1 0 1-1 4-2 5-1 3-2 7-3 10-4 9-12 18-21 22-9 5-19 8-29 4-1 0-3-1-4-2-1 0-3-3-4-3 0 0-1 1-2 1l-8 2c-1-1 0-3 0-4 0-3 2-7 0-9 1-2 1-3 1-4v-2l1-3v-2l1-1c-1-1-2-2-2-3l1-5 2-7h2 0c2-1 3-3 3-5l1-3 2 1c0-1 1-1 1-1h1l5-5c6-5 12-4 19-7-3-2-7-3-10-5-5-3-9-7-14-11l1-1c1-2 1-3 2-5l3-11c2-1 4-1 5-3 1 0 2-1 3-1l1-1c1 1 0 1 0 2l5-3v1c1 1 3 1 4 1 2-1 5-3 7-2h-1l-6 3c0 1 0 1 1 2 8-3 17-5 25-7h4 3v-2c-1-2-3-4-5-5-5-3-12-2-17-1-2 0-3 1-5 2l7-5c-2-1-5-1-6-3h0c3-1 6-4 9-5 3-2 7-4 10-5 7-3 13-4 21-5 2 0 4 0 7-1h-2c2-1 7 0 10 1h1l-6-3c3 0 7 2 10 3l-1-1c0-1-1-1-3-2h-1l-1-1 2-2c0-1 1-1 1-1-4-4-9-5-13-5-2 0-5-1-7 0v1h0c-1 0-2 1-3 1-2 1-6 2-8 4h3v2h1 1 0l-14 6c-3 2-6 3-9 4-3 3-6 6-9 8-2 1-5 2-7 2l-11 3 3-6v3c2 0 7-2 9-4h0c2 0 4-1 6-3 3-1 6-3 9-6 6-5 10-10 15-16 0-1 1-1 1-1l1-1v-1h1z"></path><path d="M379 560c1 1 1 2 1 3s-1 1-1 2l-2-1 2-4z" class="O"></path><path d="M377 564l2 1c-1 1-3 3-5 4v-1h0v-1c2-1 2-2 3-3z" class="N"></path><path d="M337 585h2v1c0 2-1 4-2 5-1-1 0-4 0-6z" class="L"></path><path d="M341 596v2 1l-5 2c-1-1 0-2 0-3l4-1 1-1z" class="E"></path><path d="M358 515l3-3v1c1 0 1-1 2-1h0v1 1c-1 2-6 3-8 4l3-3z" class="P"></path><path d="M337 593l2-2v1l2 4-1 1-4 1 1-5z" class="B"></path><path d="M378 580l9-6 1 1c0 1 0 2-1 2-1 1-3 2-4 3h-1c-2 1-2 1-4 0z" class="V"></path><path d="M370 515c-2 0-5 4-8 3h0c4-2 7-5 10-6 2-1 5-2 7-2-1 2-3 2-5 3-1 1-2 2-3 2h-1z" class="E"></path><path d="M387 574c3-1 5-3 7-4 2 0 4-1 5-1-1 1-2 3-3 4-1 0-2 0-2 1-1 0-1 0-2 1l-1 1h-1-1c-1 1-1 1-2 1 1 0 1-1 1-2l-1-1z" class="G"></path><path d="M378 519c1 0 2 0 3-1h0 1 2l1-1h5c0 1-1 1-2 1-3 1-7 1-10 3h-1c-1 0-1 0-2 1-2 1-4 1-6 1-2 1-4 2-5 3 2-3 6-4 9-5 2 0 3-1 5-2z" class="C"></path><path d="M410 458h4c-1 0-1 0-2 1h7l4 1v1c-2 0-4-1-5-1v1c-3 0-5 0-8-1-2 0-5 1-7 0v-1h1l1 1c1-1 4-2 5-2z" class="G"></path><path d="M410 458h4c-1 0-1 0-2 1-1 0-1 0-2 1h-5c1-1 4-2 5-2z" class="T"></path><path d="M375 597l11-4c-2 4-5 6-9 8v-1c-1-1-1-1-3-1l1-2z" class="E"></path><path d="M435 468c3 2 6 5 7 8v3h-1v-1l-3-3-1-1c0-1-1-1-3-2h-1l-1-1 2-2c0-1 1-1 1-1z" class="L"></path><path d="M334 586l1 1h0c1 0 1 0 1-1l1-1c0 2-1 5 0 6v2l-1 5c0 1-1 2 0 3-1-1-1-1-2 0h-1 0c0-3 2-7 0-9 1-2 1-3 1-4v-2z" class="Q"></path><path d="M387 588c2-2 4-3 5-5 0 3-5 6-8 8-1 1-3 2-5 3l-6 3c-2 0-4 0-6 1 1-1 1-1 2-1 1-1 5-2 6-4 1-1 3-1 5-1 2-1 5-2 7-4z" class="P"></path><path d="M387 577c1 0 1 0 2-1h1 1l1-1c1-1 1-1 2-1 0-1 1-1 2-1-1 1-1 2-1 2-3 2-4 5-6 7v-1l1-1h-1c-1 0-3 3-4 3-1-1-1 0-2 0l2-2v-1c-1 1-2 1-3 0h1c1-1 3-2 4-3z" class="K"></path><path d="M383 532c-1 0-3 0-4 1l-7 1c-2 1-4 2-5 2v-1c2-1 4-2 7-3 5-2 9-4 14-5v1 1 1c-1 0-2 1-3 1l-1 1h-1 0z" class="C"></path><path d="M398 555v2c0 4-3 7-4 11-2 2-3 4-6 5 1-2 2-6 3-8 2-3 5-6 6-9l1-1z" class="B"></path><path d="M375 515h2c5-2 11-4 17-4-1 0-2 1-3 1-1 1-2 1-3 2-2 0-3 1-5 2-1 0-2 0-4 1h0-2 0-1c-4 2-8 3-12 4 2-1 5-3 6-4 1 0 1-1 2-1h1c1-1 1-1 2-1z" class="E"></path><path d="M389 582c2-2 3-5 6-7v1c-1 3-2 4-3 6-2 2-4 3-6 5l1 1c-2 2-5 3-7 4-2 0-4 0-5 1h-1c0-1-2-1-2-1l2-1h0c4 0 7-3 10-5 1-1 4-2 5-4z" class="Y"></path><path d="M343 557l2 1c0-1 1-1 1-1h1c-4 5-7 10-9 16 0 2-1 5-2 7-1-1-2-2-2-3l1-5 2-7h2 0c2-1 3-3 3-5l1-3z" class="G"></path><path d="M367 506v1c1 1 3 1 4 1 2-1 5-3 7-2h-1l-6 3c0 1 0 1 1 2-1 0-2 0-3 1-2 0-4 1-6 2v-1-1h0c-1 0-1 1-2 1v-1l-3 3c-1-1-2 0-3 0h-1l3-3c0-1 1-2 2-2l3-1 5-3z" class="L"></path><path d="M359 510c1 1 1 1 2 1l6-2c-2 1-5 2-6 3l-3 3c-1-1-2 0-3 0h-1l3-3c0-1 1-2 2-2z" class="D"></path><path d="M385 583c1 0 3-3 4-3h1l-1 1v1c-1 2-4 3-5 4-3 2-6 5-10 5h0l-2 1h-7v1c-2-1-3-1-4-2h0l-1-1h6l12-3c3-2 5-2 7-4z" class="R"></path><path d="M374 591c2-1 4-3 6-4 1-1 2-1 4-1-3 2-6 5-10 5z" class="D"></path><path d="M365 592c-1 0-2 0-3-1h0 7c1-1 3-1 5-1v1l-2 1h-7z" class="G"></path><path d="M379 557c2-2 4-3 6-4-2 2-4 4-6 7l-2 4c-1 1-1 2-3 3-2 0-3 1-5 2h-2c0-2 4-6 5-9 1-1 1-2 0-3l1-1 1 1h0l2-1v1h3z" class="L"></path><path d="M367 569c0-2 4-6 5-9 1-1 1-2 0-3l1-1 1 1h0l2-1v1h3c-3 3-7 7-10 11v1h-2z" class="R"></path><path d="M404 471h0c-3 1-7 2-10 1l1-1c1-2 3-5 5-6l8-1c4-1 9-3 14-1-2 0-5-1-7 0v1h0c-1 0-2 1-3 1-2 1-6 2-8 4h3v2h1-4z" class="J"></path><path d="M404 471h-1c-1-2 0-3 1-5 2 0 5-1 8-1-2 1-6 2-8 4h3v2h1-4z" class="M"></path><path d="M388 527l16-3 1-1c-3 3-7 4-10 6-6 3-11 8-18 9l-1-1v-1c2-2 4-3 7-4h0 1l1-1c1 0 2-1 3-1v-1-1-1z" class="B"></path><path d="M379 510c7-2 14-5 21-4h5v1c-2 1-4 1-5 1-3 0-5 1-8 1-3 1-7 2-10 3-2 1-4 2-7 3-1 0-1 0-2 1h-1c-1 0-1 1-2 1l-6 3c2-2 4-3 6-5h1c1 0 2-1 3-2 2-1 4-1 5-3z" class="C"></path><path d="M382 580c1 1 2 1 3 0v1l-2 2c1 0 1-1 2 0-2 2-4 2-7 4l-12 3h-6s-1 0-1-1l13-5 6-4c2 1 2 1 4 0z" class="V"></path><path d="M382 580c1 1 2 1 3 0v1l-2 2c1 0 1-1 2 0-2 2-4 2-7 4l5-6c-3 1-7 3-11 3h0l6-4c2 1 2 1 4 0z" class="T"></path><path d="M429 454c-3-1-6-1-10-1 7-2 12-4 19 0 3 2 6 6 7 10 1 2 1 4 1 6v1c-1-1-1-1-1-2v-1c-3-5-10-11-16-13z" class="B"></path><path d="M342 591c0-5 2-10 5-14-1 7 1 14 6 19 1 1 6 3 8 3 5 1 10-1 14-2l-1 2-6 2c-3 0-6 1-8 0-6-1-11-4-14-9 0-1-1-1-1-2h-2l-1 1z" class="J"></path><defs><linearGradient id="AB" x1="357.634" y1="584.649" x2="364.407" y2="597.931" xlink:href="#B"><stop offset="0" stop-color="#666465"></stop><stop offset="1" stop-color="#878687"></stop></linearGradient></defs><path fill="url(#AB)" d="M367 598c-4 0-9 0-12-2-4-2-5-6-6-9 4 2 8 3 12 4 1 1 2 1 4 2v-1h7s2 0 2 1h1c-1 2-5 3-6 4-1 0-1 0-2 1z"></path><path d="M365 592h7s2 0 2 1h-9v-1z" class="W"></path><path d="M394 511c2 0 4-1 5 0 1 0 1 1 1 1l1 1c0-1 0-1 1-2s2 0 3 0h1c0 1 0 1-1 2h-1l1 1v2h-2c-2 1-5 0-6 1l-2 1h-5c0 1-1 1-2 1v-1c1 0 2 0 2-1h-5l-1 1h-2-1 0c-1 1-2 1-3 1v-1c-4 2-7 3-11 4 3-2 7-3 10-5h0 2 0c2-1 3-1 4-1 2-1 3-2 5-2 1-1 2-1 3-2 1 0 2-1 3-1z" class="B"></path><path d="M378 518h2v-1c2 0 3 0 4-1 4-1 9 0 13 1l-2 1h-5c0 1-1 1-2 1v-1c1 0 2 0 2-1h-5l-1 1h-2-1 0c-1 1-2 1-3 1v-1z" class="F"></path><path d="M342 591l1-1h2c0 1 1 1 1 2 3 5 8 8 14 9 2 1 5 0 8 0l6-2c2 0 2 0 3 1v1l-6 3-5 1c-6 1-13 1-18-2-4-3-5-8-6-12z" class="B"></path><path d="M374 599c2 0 2 0 3 1v1l-6 3c1-1 1-2 3-2h0v-1h-1-1-4l6-2z" class="H"></path><path d="M397 517c1-1 4 0 6-1h2l-1 4h-1c0 1 0 1 1 2-2 1-4 1-5 2-4 1-7 1-11 2-2 0-5 1-7 2-3 0-5 0-8 1-3 0-6 2-9 2 5-2 9-4 14-4 2-1 3-1 5-1 5-1 9-3 14-4h2v-1c-3 1-7 1-10 3-2 1-7 2-9 2-3 0-7 1-10 1-1 1-2 1-4 1 6-2 12-3 18-5h-1c-2 0-4 1-6 1-4 1-7 1-11 2 1 0 2-1 4-2 1 0 3 0 5-1l1-1h-1c1-1 1-1 2-1h1c3-2 7-2 10-3v1c1 0 2 0 2-1h5l2-1z" class="J"></path><path d="M397 517c1-1 4 0 6-1h2l-1 4h-1-2c-2 1-5 0-7 0h0c-3 1-10 2-12 1l1-1h3v-1h2c1 0 2 0 2-1h5l2-1z" class="S"></path><path d="M397 517c1-1 4 0 6-1h2l-1 4h-1-2c-2 1-5 0-7 0 2 0 4 0 6-2-1-1-4 0-5 0l2-1z" class="C"></path><path d="M429 454c6 2 13 8 16 13v1c0 1 0 1 1 2v-1c0 3 1 5-1 7l-1 1-3-6-4-4c-6-5-13-5-19-6v-1c1 0 3 1 5 1v-1l-4-1h-7c1-1 1-1 2-1h-4c2-1 3-2 5-2 3-1 9-2 13-1 1 0 1 0 1-1z" class="B"></path><path d="M441 471h0c0-1-1-2-1-3h0v-1h1v2c1 1 2 3 3 3h0v-4h1c0 1 0 1 1 2v-1c0 3 1 5-1 7l-1 1-3-6z" class="S"></path><path d="M429 454c6 2 13 8 16 13v1h-1v-1c-2-2-4-5-7-7-6-5-15-3-23-2h-4c2-1 3-2 5-2 3-1 9-2 13-1 1 0 1 0 1-1z" class="G"></path><path d="M374 557c1-5 2-10 2-15 5-2 10-5 14-8 3 5 5 10 6 16v3c0 2-3 4-4 6-1 1-3 5-3 6 2-2 3-5 5-7v1c-1 1-1 2-2 3s-1 2-2 3c0 1-1 2-2 2-1 2-1 5-3 7l-4 3 4-11c2-5 0-10 1-14v-1l-10 5-2 1h0zm-7 12l1 3h0-1 0c1 3 3 7 5 10-4 2-7 3-11 4h0c-3 1-8 1-10 0-1-1-2-2-2-4-2-5 0-8 3-12a30.44 30.44 0 0 0-8 8c-2 2-3 4-4 6l-1-1v-2c0-2 1-4 2-6 2-6 6-13 11-17 3-2 6-3 9-5l10-6c0 4 1 8-1 12-2 3-4 6-5 9 3-4 6-8 7-12v-1c1-3 0-9 2-12h0c0 2 0 4-1 6v3 1 3l-1 1c1 1 1 2 0 3-1 3-5 7-5 9z" class="B"></path><path d="M133 159l4-7c1-3 1-5 3-7 1-2 2-3 3-4 8-8 14-19 13-30 0 0-2 1-3 1l-9 1c1 0 2-1 3-2 3-1 5-3 7-5 0-5-1-10-3-15-1-2-4-5-4-8v-6l2 6c2 1 4 1 6 1l11 1h6c3 1 4 4 5 6 2 3 3 6 4 9 1 1 2 3 2 4 0 2-1 4-1 5l-4 12c1 0 4-2 5-3 13-9 24-20 35-31l8-9-4 8c2-1 3-3 5-4l6-4c1 0 2-1 3-1v-2c2-3 3-3 6-4-2 2-3 3-4 6-1 2 0 4 1 6-2 7-5 11-10 16a34.47 34.47 0 0 1-12 12c2 0 4 0 6-1 4-1 7-1 10-1 13 0 27 3 35 13 2 3 4 7 7 10l1 1c0 1-1 1-2 2h0c2 0 3-1 4-1 1-1 2-1 2-1 1 0 2 1 2 2 1 1 1 2 1 4-2 1-5 2-7 2 0 3 0 6 1 9v1l-1 1-3 3c-1 0-2 1-3 3-2 2-2 5-4 8-3 3-6 5-10 6h-3c-1 1-2 4-2 5-1-1-2-1-3-2 0 0 0-1 1-1v-1-1l6-5c-1 1-2 3-3 4 5-1 9-1 12-4 1-2 2-5 2-8 0-2-4-6-6-7v-1h1c0-2-1-4-2-5-1-2-2-3-4-3v1l-2-1c0 1 0 1-1 2h0l-3-2v1h-1l-1-1h-1-1s-1-1-2-1h0-2c-1-1-2-1-3-1l-1-1h-2c-1 0-2 0-3-1h0-3l5 4v3h-1l2 2c-1 0-1 0-1 2 2 3 3 6 4 9v2c-1 0-2 0-2-1-1 1-2 1-2 3l1 3h-1 0v1c1 1 1 2 1 3 0 0 1 1 2 1-1 0 0 0-1 1 0 1 0 1-1 1h-3c1 1 2 1 3 1 3 1 8 1 10 4h-2c-3 1-4 0-6 0s-4-1-6 0c-1-1-1 0-2 0s-1-1-1-1c-3 0-5 2-7 3l-7 4 2 2c-1 0-2 1-3 1s-1 0-1-1h-1l-13 2-16 3c-3 1-6 1-8 2h-3l-5 2-6 3 2 1h0c2 1 3 0 5 0h1l-1 1c1 1 1 1 2 1l4-1h1 1 1 6c-1 0-3 2-3 2-1 2-2 1-3 3h1l-3 2h1c-1 2-4 2-6 3-1 0-3 1-4 2-1 0-3 0-4 1-2 0-3 1-4 1s-2 1-3 1c-1 1-2 1-3 2v1h0c1 1 1 1 1 2l-1 1h1l-1 1-5 5h-2c-1 1-4 4-4 6-4 5-6 11-7 18-1 2-1 5-2 7v15 3 5 1 3 1c1 1 1 1 1 2 0 0 1 1 1 2l-1 1 1 4c1 1 1 3 2 4v2h1 0v2l1 1c-1 1 0 1-1 1-2-2-3-5-5-7-3-2-4-7-6-10l-2-2c-3-10-4-20-5-30l-2-2h0c-1-2-1-6-1-8v-20h1 0c0-5 2-10 2-16h-1c1-5 2-9 4-14 1-4 2-7 3-11 1-1 1-3 2-5l-1 1h0v-1-3l1-2c2-6 4-11 7-16z"></path><path d="M181 124c1-1 2-2 4-3-1 1 0 2-1 3h-1-2z" class="T"></path><path d="M143 184h1v2c-1 2-2 3-3 4h-1l3-6z" class="K"></path><path d="M174 130c2-2 4-5 7-6h2l-5 5-2 1h-2z" class="G"></path><path d="M163 145l1 1c-1 1-1 3-2 5 0 0-2 2-2 3l1 1-3 2-2 1c0-1 1-2 2-4l5-9z" class="N"></path><path d="M144 186v3h0c-2 4-5 7-6 11l-1 1h-1c2-4 2-8 4-11h1c1-1 2-2 3-4z" class="U"></path><defs><linearGradient id="AC" x1="167.784" y1="141.915" x2="174.628" y2="130.869" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#AC)" d="M174 130h2v1h1l1 1a30.44 30.44 0 0 0-8 8c-3 2-5 3-6 6l-1-1c3-5 7-10 11-15z"></path><path d="M204 116c6-5 13-10 19-16 4-4 8-9 12-13-5 10-15 18-23 25l-6 6-1 1-1-1 2-2c1-1 2-1 2-2-2 1-2 2-4 2z" class="M"></path><path d="M148 186c1 0 1-1 2-1 1-1 1-1 2-1 0 1-2 2-2 3 1 0 1-1 2-1-6 7-10 14-16 21 0 1-1 2-1 2-2 2-4 6-4 8h-1c0-2 1-5 2-7v3h0c0-1 1-1 1-2s0-1 1-2 0-2 1-3v1c1 0 1-1 1-1 2-2 3-5 5-6v-1c0-1 1-1 1-1v-1h1l-1-1-1 1h0c0-1 0-1 1-2l-5 7s-1 1-2 1c0-1 1-2 1-2h1l1-1c1-4 4-7 6-11 1-1 3-2 4-3z" class="O"></path><path d="M157 97h1c3 1 5 3 6 6v4c0 7-4 13-7 19l-1 1c0-1 2-4 2-5 1-3 2-6 2-9 0-4-2-8-3-12-1-1-1-3-1-4h1z" class="B"></path><path d="M196 117l8-5c2-1 4-3 5-3v1c-1 2-3 3-5 5l-12 7c-3 3-6 5-9 7h-1c-1 1-3 2-4 3l-1-1h-1v-1l2-1 5-5h1c1-1 0-2 1-3l1-1 5-4 1 1c2-1 2-1 4 0z" class="H"></path><path d="M176 130l2-1h0 2 2c-1 1-3 2-4 3l-1-1h-1v-1z" class="E"></path><path d="M191 116l1 1c2-1 2-1 4 0l-1 1c-1 0-2 1-3 2s-2 1-3 2l-1 1h-1c-1 0-2 1-3 2v-1c1-1 0-2 1-3l1-1 5-4z" class="F"></path><path d="M185 121l1-1h1c1 0 3-1 4-1-2 1-3 2-4 4-1 0-2 1-3 2v-1c1-1 0-2 1-3z" class="J"></path><path d="M182 129h1c3-2 6-4 9-7v1l-5 5c-1 1-3 2-4 3l-1 4h1l8-5h0l-22 17-6 6h-1l-1 2-1-1c0-1 2-3 2-3 1-2 1-4 2-5 1-3 3-4 6-6a30.44 30.44 0 0 1 8-8c1-1 3-2 4-3z" class="Y"></path><path d="M164 146c1-3 3-4 6-6l-2 2c1 0 1 0 2-1h2l-1 1 1 1c-1 1-3 1-4 2 0 1-2 2-3 3s-2 3-3 4l1 1h-1l-1 2-1-1c0-1 2-3 2-3 1-2 1-4 2-5z" class="O"></path><path d="M168 145v-1h0c1-2 2-2 3-2l1 1c-1 1-3 1-4 2z" class="R"></path><defs><linearGradient id="AD" x1="173.367" y1="138.611" x2="178.69" y2="137.437" xlink:href="#B"><stop offset="0" stop-color="#7d7c7d"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#AD)" d="M172 141l11-10-1 4h1c-2 2-4 4-7 5-2 1-3 2-4 3l-1-1 1-1z"></path><defs><linearGradient id="AE" x1="170.857" y1="135.052" x2="177.674" y2="137.82" xlink:href="#B"><stop offset="0" stop-color="#22201c"></stop><stop offset="1" stop-color="#303136"></stop></linearGradient></defs><path fill="url(#AE)" d="M182 129h1c3-2 6-4 9-7v1l-5 5c-1 1-3 2-4 3l-11 10h-2c-1 1-1 1-2 1l2-2a30.44 30.44 0 0 1 8-8c1-1 3-2 4-3z"></path><path d="M149 86c5 1 19 0 22 2 3 3 5 9 6 13 1 2 1 4 2 6 0 2-1 4-1 6-2 5-5 10-8 14l-1 1h0c2-6 6-10 8-16-1-2-7-15-8-16-2-1-4-1-6-1-5-2-10-5-14-9z" class="B"></path><path d="M191 130c9-6 19-9 29-11 2-1 5-1 7-1l1 1c-3 0-8 0-10 2h3c-3 2-6 2-9 3 0 0-1 0-1 1l-1-1-6 3-10 5 1 1c3-2 7-3 10-5h0l-2 2v1h3l1 1c-2 0-3 1-5 2 1 0 1 1 2 1l-5 1h-3c-1 0-3 1-5 0l-20 14h-2c0 1 0 1-1 1v-1c0-1 0-2 1-3l22-17z" class="C"></path><path d="M191 136l12-6v1h3l1 1c-2 0-3 1-5 2 1 0 1 1 2 1l-5 1h-3c-1 0-3 1-5 0z" class="W"></path><path d="M191 116l11-9c9-9 18-18 29-25 1 0 3-2 5-2v2c-1 3-3 5-5 8-6 8-13 14-22 20v-1c-1 0-3 2-5 3l-8 5c-2-1-2-1-4 0l-1-1zm-65 64c3-8 8-16 13-23 2-5 4-9 7-13l10-16c3-3 5-6 6-10 2-5 4-12 2-18h0 0s1 0 1 1c2 2 2 4 1 6l1-1v-2c0-1 0-3-1-4v-2h1c0 1 0 1 1 2 0 4 1 7 0 11h1c-1 3-1 6-3 9-2 5-6 10-9 15l-9 14c-4 5-7 11-10 16-3 4-4 7-6 11-2 6-5 11-7 16-1 3-1 6-2 9v1l-2-1-2 5c0 2-1 3-1 4h-1c1-5 2-9 4-14 1-4 2-7 3-11 1-1 1-3 2-5z" class="B"></path><path d="M124 185v1 3c-1 2-1 4-2 5v1c1-1 0-1 1-1v-1-1l1-1v-1c0-1 1-1 1-2h1l-5 13-2 5c0 2-1 3-1 4h-1c1-5 2-9 4-14 1-4 2-7 3-11z" class="C"></path><path d="M168 111h1c-1 3-1 6-3 9-2 5-6 10-9 15l-9 14c-4 5-7 11-10 16-3 4-4 7-6 11-2 6-5 11-7 16-1 3-1 6-2 9v1l-2-1 5-13c4-9 8-17 13-26l13-21 5-8c2-4 6-7 8-11s2-7 3-11z" class="N"></path><path d="M206 118c15-5 32-9 47-2 9 4 15 11 19 20 1 4 2 10 2 14l-2 2c-1 2-2 3-2 5h-1l1-1c1-10-1-20-7-28-6-7-15-10-23-11h-3v1c-2 0-6 0-8 1h-1l-1-1c-2 0-5 0-7 1-10 2-20 5-29 11h0l-8 5h-1l1-4c1-1 3-2 4-3l5-5 12-7c2 0 2-1 4-2 0 1-1 1-2 2l-2 2 1 1 1-1z" class="B"></path><path d="M191 130l4-4c10-6 25-9 37-9h8-3v1c-2 0-6 0-8 1h-1l-1-1c-2 0-5 0-7 1-10 2-20 5-29 11h0z" class="I"></path><path d="M191 136c2 1 4 0 5 0h3l-15 7c-5 2-8 6-13 9v1 1l10-4c2-1 4-2 6-4l1 1c-2 1-3 2-5 3-1 0-3 1-3 2 2-1 4-2 6-2v1h0v1c2-1 5-2 7-2 0 1 0 1 1 1v1c-5 1-12 3-17 6l1 1-12 6c-1 1-1 2-1 3v1l-6 4-1 1-7 8-3 4c-1 1-3 2-4 3h0v-3-2h-1l1-3c0-1 0-2 1-3 1-4 4-9 6-12s4-5 5-8l2-1 3-2 1-2h1l6-6c-1 1-1 2-1 3v1c1 0 1 0 1-1h2l20-14z" class="E"></path><path d="M161 163h0c1 0 2-1 3-2l4-4h2 0 1c1-1 1-1 2-1l1-1v1l-2 1c-2 2-5 3-7 5s-5 3-7 6l-1-1c0-1 0-1 1-2 1 0 2-1 3-2z" class="J"></path><defs><linearGradient id="AF" x1="167.057" y1="165.862" x2="171.989" y2="157.829" xlink:href="#B"><stop offset="0" stop-color="#3b3d3c"></stop><stop offset="1" stop-color="#59585a"></stop></linearGradient></defs><path fill="url(#AF)" d="M186 152c2-1 5-2 7-2 0 1 0 1 1 1v1c-5 1-12 3-17 6l1 1-12 6c-1 1-1 2-1 3v1l-6 4h-1c-1 0-1 0-2 1v-2l3-3 7-6c3-3 8-6 12-8 3-1 5-2 8-3z"></path><path d="M156 172c4-2 6-5 10-7-1 1-1 2-1 3v1l-6 4h-1c-1 0-1 0-2 1v-2z" class="J"></path><path d="M165 162v1h1l-7 6-3 3v2c1-1 1-1 2-1h1l-1 1-7 8-3 4c-1 1-3 2-4 3h0v-3-2h-1l1-3c1 0 2-1 3-2l1-2v-1c1-2 2-3 3-5v1h1l2-2h2 0l2-2c2-3 5-4 7-6z" class="T"></path><path d="M151 171v1h1l2-2h2l-6 6-2 1v-1c1-2 2-3 3-5z" class="R"></path><path d="M148 182l3-3c1-1 1-2 2-2 2-2 3-3 5-3l-7 8c-1 0-3 1-4 1 1 0 1 0 1-1z" class="K"></path><path d="M150 176v3c-1 1-2 1-2 3 0 1 0 1-1 1 1 0 3-1 4-1l-3 4c-1 1-3 2-4 3h0v-3-2h-1l1-3c1 0 2-1 3-2l1-2 2-1z" class="X"></path><path d="M150 176v3c-1 1-2 1-2 3 0 1 0 1-1 1 0 1-1 2-2 3v-1c0-1 1-2 2-3v-1-2l1-2 2-1z" class="G"></path><path d="M191 136c2 1 4 0 5 0h3l-15 7c-5 2-8 6-13 9-2 2-5 3-7 6-1 2-2 3-3 5-1 1-2 2-3 2-1 1-1 1-1 2l1 1-2 2h0-2l-2 2h-1v-1c-1 2-2 3-3 5v1l-1 2c-1 1-2 2-3 2 0-1 0-2 1-3 1-4 4-9 6-12s4-5 5-8l2-1 3-2 1-2h1l6-6c-1 1-1 2-1 3v1c1 0 1 0 1-1h2l20-14z" class="D"></path><path d="M161 155l1-2c0 1-1 3-1 4h1 1l-6 6c-1 0-1 0-1-1 1-1 2-3 2-5l3-2z" class="T"></path><path d="M151 171l6-6c3-3 4-6 7-7-1 2-2 3-3 5-1 1-2 2-3 2-1 1-1 1-1 2l1 1-2 2h0-2l-2 2h-1v-1z" class="K"></path><path d="M169 147c-1 1-1 2-1 3v1c1 0 1 0 1-1h2l-8 7h-1-1c0-1 1-3 1-4h1l6-6z" class="E"></path><path d="M156 158l2-1c0 2-1 4-2 5 0 1 0 1 1 1-1 2-3 4-4 5-3 3-6 7-7 10h-1c1-4 4-9 6-12s4-5 5-8z" class="K"></path><path d="M208 134c1 0 2 0 3-1h2l1 1c2 0 4 1 6 1 3 1 5 2 8 4h1l5 4v3h-1l2 2c-1 0-1 0-1 2-1-2-3-2-4-2-5-1-9-2-13-1s-8 1-11 2l-12 3v-1c-1 0-1 0-1-1-2 0-5 1-7 2v-1h0v-1c-2 0-4 1-6 2 0-1 2-2 3-2 2-1 3-2 5-3l-1-1c-2 2-4 3-6 4l-10 4v-1-1c5-3 8-7 13-9l15-7 5-1 4-1z" class="B"></path><path d="M230 144l3 2 2 2c-1 0-1 0-1 2-1-2-3-2-4-2l1-1c-1-1-1-2-1-3z" class="M"></path><path d="M207 140l10 3c-6 0-11-2-17 0h-1c2-1 3-1 4-2v-1h4z" class="N"></path><path d="M188 147c1 0 2-1 3-1 0-1 1 0 1 0 0 1-1 1-1 2 0 0 1 0 2 1l1 1c2-1 4-1 7-1 1 0 1 0 2-1 0 0 0 1 1 1h2l-12 3v-1c-1 0-1 0-1-1-2 0-5 1-7 2v-1h0v-1c-2 0-4 1-6 2 0-1 2-2 3-2 2-1 3-2 5-3z" class="F"></path><path d="M208 134c1 0 2 0 3-1h2l1 1c2 0 4 1 6 1 3 1 5 2 8 4h1l5 4v3h-1l-3-2c-6-6-14-9-22-10z" class="I"></path><path d="M184 143c0 1-1 1-2 2l-4 3h0l1 1v-1h1v-1c1 0 2 0 3-1h0 1c2-1 3-2 5-2 1-1 2-1 3-2 1 0 2-1 3-1 2 0 4 0 6-1h6-4v1c-1 1-2 1-4 2h1l-8 3s-1-1-1 0c-1 0-2 1-3 1l-1-1c-2 2-4 3-6 4l-10 4v-1-1c5-3 8-7 13-9z" class="H"></path><path d="M187 146c4-2 6-3 11-3h1 1l-8 3s-1-1-1 0c-1 0-2 1-3 1l-1-1z" class="L"></path><path d="M169 111v-5l3 6c0 3-1 9-3 12 0 1 0 2-1 4 0 2-2 5-3 6l-7 13c-4 7-9 14-13 22-4 9-8 19-11 30l-1 7c-1 1-1 3-1 4-1 2-2 5-2 7-2 6-3 12-4 18l-2 17v1c-1 2-1 6-1 8v3l-1-21c0-3 1-7 1-10 1-17 6-33 12-49 1-5 4-10 7-14 0-2 2-4 2-6 1 0 0-1 0-1l3-6-9 13c0-2 1-3 2-5l4-6-1-1-3 5h0c0 1-1 2-2 3v-1c3-5 6-11 10-16l9-14c3-5 7-10 9-15 2-3 2-6 3-9zm37 20c6-2 11-4 17-6 9-2 19-5 27-1 7 3 12 8 14 15 3 7 2 12 3 19v-1c-1-2-3-4-4-7-2-3-3-5-6-8-1 0-3-1-3-2-1-2 0-6-1-7h-1v5l-14-1h0c3 1 13 4 15 6 0 1 0 1-1 2h0l-3-2v1h-1l-1-1h-1-1s-1-1-2-1h0-2c-1-1-2-1-3-1l-1-1h-2c-1 0-2 0-3-1h0-3-1c-3-2-5-3-8-4-2 0-4-1-6-1l-1-1h-2c-1 1-2 1-3 1l-4 1c-1 0-1-1-2-1 2-1 3-2 5-2l-1-1z" class="B"></path><path d="M207 132c2-1 5-1 8-2 5 0 11-1 16 0 1 0 3 0 5 1h0 0l-1 1c-3-1-7-1-10 0l-12 1h-2c-1 1-2 1-3 1l-4 1c-1 0-1-1-2-1 2-1 3-2 5-2z" class="O"></path><path d="M225 132c3-1 7-1 10 0v1h1v3c1 1 1 1 2 1h0c3 1 13 4 15 6 0 1 0 1-1 2h0l-3-2v1h-1l-1-1h-1-1s-1-1-2-1h0-2c-1-1-2-1-3-1l-1-1h-2c-1 0-2 0-3-1h0-3-1c-3-2-5-3-8-4-2 0-4-1-6-1l-1-1 12-1z" class="H"></path><path d="M231 134h1l1 1v1h1c-1 1-1 1-2 1v1h-1c-2 0-3-1-4-1v-1h2c1 0 2 0 2-1v-1z" class="F"></path><path d="M234 136c0 1 1 1 2 1h2c3 1 13 4 15 6 0 1 0 1-1 2h0l-3-2h0c-1 0-2-1-2-1h-1l-3-1c-1 0-3-1-4-1l-4-1c-1 0-2-1-3-1v-1c1 0 1 0 2-1z" class="M"></path><path d="M225 132c3-1 7-1 10 0v1h1v3c1 1 1 1 2 1h0-2c-1 0-2 0-2-1h-1v-1l-1-1h-1v1c0 1-1 1-2 1-2-1-3-1-4-2h-5v1c-2 0-4-1-6-1l-1-1 12-1z" class="G"></path><path d="M225 134h6v1c0 1-1 1-2 1-2-1-3-1-4-2z" class="L"></path><path d="M225 132c3-1 7-1 10 0v1h1v3c1 1 1 1 2 1h0-2c-1 0-2 0-2-1h-1v-1l-1-1h1l1-1h-8l-1-1z" class="H"></path><path d="M138 170l9-13-3 6s1 1 0 1c0 2-2 4-2 6-3 4-6 9-7 14-6 16-11 32-12 49 0 3-1 7-1 10l1 21v-3c0-2 0-6 1-8v-1c0 4 0 8 1 12 1 3 1 6 1 9 0 2 0 4 1 5 0 0 1 0 1 1 0 0 1 0 1 1s0 2 1 3v3c1 2 1 4 2 5l1 4c1 1 1 3 2 4v2h1 0v2l1 1c-1 1 0 1-1 1-2-2-3-5-5-7-3-2-4-7-6-10l-2-2c-3-10-4-20-5-30l-2-2h0c-1-2-1-6-1-8v-20h1 0c0-5 2-10 2-16 0-1 1-2 1-4l2-5 2 1v-1c1-3 1-6 2-9 2-5 5-10 7-16 2-4 3-7 6-11v1c1-1 2-2 2-3h0l3-5 1 1-4 6c-1 2-2 3-2 5z" class="D"></path><path d="M126 202c3-8 5-17 9-24 1-1 2-3 3-5 1-3 3-8 7-10-4 6-8 12-10 18-1 3-2 5-2 7l-5 15c0-1 0-1-1-2l-1 1z" class="C"></path><path d="M138 165v1c1-1 2-2 2-3h0l3-5 1 1-4 6c-1 2-2 3-2 5 0 3-3 6-4 9l-5 10v1c0 1-1 4-2 5 0-2 1-4 2-6v-2c1-2 2-3 2-5-2 2-2 5-3 7-1 3-3 7-4 10v2h-1c1-3 1-6 2-9 2-5 5-10 7-16 2-4 3-7 6-11z" class="E"></path><path d="M122 243l1 21v-3c0-2 0-6 1-8v-1c0 4 0 8 1 12 1 3 1 6 1 9 0 2 0 4 1 5 0 0 1 0 1 1 0 0 1 0 1 1s0 2 1 3v3c1 2 1 4 2 5l1 4c1 1 1 3 2 4v2h1 0v2l1 1c-1 1 0 1-1 1-2-2-3-5-5-7-3-2-4-7-6-10h0 0c0-4-1-7-2-11-1-7-1-15-1-23v-11z" class="H"></path><path d="M125 264c1 3 1 6 1 9 0 2 0 4 1 5 0 0 1 0 1 1 0 0 1 0 1 1s0 2 1 3v3c1 2 1 4 2 5l1 4c1 1 1 3 2 4v2h1 0v2l1 1c-1 1 0 1-1 1-2-2-3-5-5-7v-2l-1-2-1-3-1-1-1-4v-1-1c0-1 0-1-1-1 1-1 1-1 0-2v-1-1c-1-1-1-1-1-2v-1-3-9zm-1-65v4c0 3-3 7-1 9l3-10 1-1c1 1 1 1 1 2-2 7-4 14-5 21-2 14-3 29-2 42 1 4 1 9 2 13 0 2 1 4 1 6l-1 1c-3-10-4-20-5-30l-2-2h0c-1-2-1-6-1-8v-20h1 0c0-5 2-10 2-16 0-1 1-2 1-4l2-5 2 1v-1h1v-2z" class="M"></path><path d="M124 199v4c0 3-3 7-1 9l-4 20c-1 7-2 16-1 24l-2-2h0c-1-2-1-6-1-8v-20h1 0c0-5 2-10 2-16 0-1 1-2 1-4l2-5 2 1v-1h1v-2z" class="W"></path><path d="M115 226h1 0c1 2 0 5 0 7 1 3 0 7 0 10h0l-1 3v-20z" class="D"></path><path d="M124 199v4c0 3-3 7-1 9l-4 20c-2-4 0-12 1-16-1-2-1-4 0-6 0-2 0-2-1-4l2-5 2 1v-1h1v-2z" class="P"></path><path d="M121 201l2 1c0 1 0 4-1 5s-1 2-2 3v6c-1-2-1-4 0-6 0-2 0-2-1-4l2-5z" class="D"></path><path d="M206 149c3-1 7-1 11-2 1 0 2 1 3 0 1 0 3 0 4 1h-2c-1 0-2 0-2 1h-1c4 0 6-1 9 0h1c-1 1-2 1-2 2-1 2-2 5-2 7 1 0 1 1 1 1h1v-1l1 1c-2 3-4 6-8 7h-4l-14-1c-4 0-8 0-12 1v1l-10 4-3 2h0l-6 3c0 1 1 1 1 2v1l-2 2c-4 2-8 4-11 6h-1c-2 1-5 4-6 6l-1 1c-2 1-2 2-4 2v-1l-9 12h-2c6-7 10-14 16-21-1 0-1 1-2 1 0-1 2-2 2-3-1 0-1 0-2 1-1 0-1 1-2 1l3-4 7-8 1-1 6-4v-1c0-1 0-2 1-3l12-6-1-1c5-3 12-5 17-6l12-3z" class="S"></path><path d="M213 155h1c1-1 3-1 4-2v1h-2c0 1 0 1 1 2h0c-1 1-3 0-4 1-3 2-7 1-10 2h0 3 0 2c1-1 1-1 1 0h4c-3 0-6 0-9 1h0-3-4l-1-1c5 0 10-1 14-2h0c1-1 2-1 3-2z" class="L"></path><defs><linearGradient id="AG" x1="166.767" y1="173.075" x2="164.464" y2="169.817" xlink:href="#B"><stop offset="0" stop-color="#272626"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#AG)" d="M213 155c-1 1-2 1-3 2h0c-4 1-9 2-14 2-6 1-11 2-17 5-5 3-10 5-15 9-1 1-12 11-12 11-1 0-1 0-2 1-1 0-1 1-2 1l3-4 7-8 1-1 6-4c5-3 10-6 15-8 6-2 13-2 18-3s10-1 15-3z"></path><path d="M206 149c3-1 7-1 11-2 1 0 2 1 3 0 1 0 3 0 4 1h-2c-1 0-2 0-2 1h-1c-4 2-8 4-13 5s-10 0-14 1c-5 0-10 2-14 4l-1-1c5-3 12-5 17-6l12-3zm7 10h4s2-1 3-1c0 0 1-3 2-4 0 1 0 2 1 3 0 0 1 1 1 2l1-1c1 0 1 1 1 1h1v-1l1 1c-2 3-4 6-8 7h-4l-14-1h0s-1 0-2-1h-5 0c2-1 5 0 7-1h0c-6 0-12 1-18 3h0c-2-1-3-1-4 0l-1-2c6-3 11-4 17-5l1 1h4 3 0c3-1 6-1 9-1z" class="O"></path><path d="M204 160h0c3 1 8 0 11 0 2 0 4-1 6-1v1l-1 1v-1c-6 2-12 2-18 3-6 0-12 1-18 3h0c-2-1-3-1-4 0l-1-2c6-3 11-4 17-5l1 1h4 3 0z" class="F"></path><path d="M184 166c6-2 12-3 18-3h0c-2 1-5 0-7 1h0 5c1 1 2 1 2 1h0c-4 0-8 0-12 1v1l-10 4-3 2h0l-6 3c0 1 1 1 1 2v1l-2 2c-4 2-8 4-11 6h-1c-2 1-5 4-6 6l-1 1c-2 1-2 2-4 2v-1l-9 12h-2c6-7 10-14 16-21-1 0-1 1-2 1 0-1 2-2 2-3l12-11c5-4 10-6 15-9l1 2c1-1 2-1 4 0h0z" class="N"></path><path d="M171 176v-1-1c1-1 2-2 4-2 2-1 3-1 5-1l-3 2h0l-6 3z" class="P"></path><path d="M171 176c0 1 1 1 1 2v1l-2 2c-4 2-8 4-11 6h-1c2-1 3-3 5-5s5-4 8-6z" class="V"></path><path d="M163 177c1 0 2-2 2-2 1 0 2-1 3-2h2c-9 7-16 14-23 22l-9 12h-2c6-7 10-14 16-21 1 0 2-2 3-2 2-1 3-3 5-4l3-3z" class="F"></path><path d="M152 184l12-11c5-4 10-6 15-9l1 2c1-1 2-1 4 0h0l-6 3-6 3c-1 1-2 1-2 1h-2c-1 1-2 2-3 2 0 0-1 2-2 2l-3 3c-2 1-3 3-5 4-1 0-2 2-3 2s-1 1-2 1c0-1 2-2 2-3z" class="L"></path><path d="M180 166c1-1 2-1 4 0h0l-6 3-6 3c-1 1-2 1-2 1h-2c-1 1-2 2-3 2 0 0-1 2-2 2v-1l2-2h1c1-1 1-2 3-2l4-3c1-1 2-1 3-1v-1h2c1 0 1-1 2-1h0z" class="E"></path><path d="M147 195v1c2 0 2-1 4-2l1-1c1-2 4-5 6-6h1l-5 5 1 1h0l-4 4-1 1h0v1l6-4 2 1c2-2 4-3 6-4l2 3-6 3 2 1h0c2 1 3 0 5 0h1l-1 1c1 1 1 1 2 1l4-1h1 1 1 6c-1 0-3 2-3 2-1 2-2 1-3 3h1l-3 2h1c-1 2-4 2-6 3-1 0-3 1-4 2-1 0-3 0-4 1-2 0-3 1-4 1s-2 1-3 1c-1 1-2 1-3 2v1h0c1 1 1 1 1 2l-1 1h1l-1 1-5 5h-2c-1 1-4 4-4 6-4 5-6 11-7 18-1 2-1 5-2 7v15 3 5 1 3 1c1 1 1 1 1 2 0 0 1 1 1 2l-1 1c-1-1-1-3-2-5v-3c-1-1-1-2-1-3s-1-1-1-1c0-1-1-1-1-1-1-1-1-3-1-5 0-3 0-6-1-9-1-4-1-8-1-12l2-17c1-6 2-12 4-18h1c0-2 2-6 4-8 0 0 1-1 1-2h2l9-12z"></path><path d="M163 208l-7 2c1-2 2-3 4-4 1 1 2 1 3 2h0z" class="G"></path><path d="M162 210c-1 2-4 3-5 4-1 0-2 1-3 1h-3c4-2 7-4 11-5z" class="U"></path><path d="M131 238c1 3-2 7-1 10h0l-1 4v1h-1v-2l-1-1c0-3 3-8 4-12z" class="X"></path><path d="M131 238c1-2 2-5 3-7 1-3 4-7 6-8-1 3-3 6-4 9-1 2-2 4-3 7l-3 9h0c-1-3 2-7 1-10z" class="R"></path><path d="M179 202c-1 2-2 1-3 3h1l-3 2h1c-1 2-4 2-6 3-1 0-3 1-4 2-1 0-3 0-4 1-2 0-3 1-4 1 1-1 4-2 5-4 4-2 9-3 12-5v-1h0c1-1 3-1 5-2z" class="K"></path><defs><linearGradient id="AH" x1="161.765" y1="207.548" x2="179.222" y2="198.783" xlink:href="#B"><stop offset="0" stop-color="#898989"></stop><stop offset="1" stop-color="#bbb9ba"></stop></linearGradient></defs><path fill="url(#AH)" d="M174 200h1 1 6c-1 0-3 2-3 2-2 1-4 1-5 2h0l-11 4h0c-1-1-2-1-3-2 3-2 6-3 10-4 1-1 3-1 4-2z"></path><path d="M162 199h0c2 1 3 0 5 0h1l-1 1c1 1 1 1 2 1-2 1-4 2-7 2-1 1-3 1-5 2s-4 2-5 4c-3 3-7 5-10 9 2-6 10-11 14-14l-1-1h0c2-2 4-3 7-4z" class="G"></path><path d="M162 199h0c2 1 3 0 5 0l-11 5-1-1h0c2-2 4-3 7-4z" class="I"></path><defs><linearGradient id="AI" x1="147.97" y1="213.722" x2="144.228" y2="208.291" xlink:href="#B"><stop offset="0" stop-color="#0d0d0e"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#AI)" d="M160 198l2 1c-3 1-5 2-7 4h0l1 1c-4 3-12 8-14 14 0 1-1 2-3 3 0 0-1 1-1 2h0c-1-1 0-1 0-2h-2c2-5 6-8 9-12 4-5 9-9 15-11z"></path><path d="M151 215h3c-1 1-2 1-3 2v1h0c1 1 1 1 1 2l-1 1h1l-1 1-5 5h-2c-1 1-4 4-4 6-4 5-6 11-7 18-1 2-1 5-2 7v15 3 5 1 3 1c1 1 1 1 1 2 0 0 1 1 1 2l-1 1c-1-1-1-3-2-5v-3c-1-1-1-2-1-3s-1-1-1-1c0-1-1-1-1-1-1-1-1-3-1-5h1v-3-5c-1-4-1-10 0-15l1 1v2h1v-1l1-4 3-9c1-3 2-5 3-7 1-3 3-6 4-9 4-4 7-6 11-8z" class="G"></path><path d="M130 283c1-3-1-7 0-10h1v3 5 1 3 1c1 1 1 1 1 2 0 0 1 1 1 2l-1 1c-1-1-1-3-2-5v-3z" class="J"></path><defs><linearGradient id="AJ" x1="129.594" y1="253.451" x2="125.487" y2="259.245" xlink:href="#B"><stop offset="0" stop-color="#7c7b7e"></stop><stop offset="1" stop-color="#929393"></stop></linearGradient></defs><path fill="url(#AJ)" d="M129 252v2l1-1v-2c1 0 1 0 1-1l1 1h0l-1 2v1 1l-1 1v1 1c-1 2 0 3 0 4-1 1-1 1-1 2h-1v7l1 9c0-1-1-1-1-1 0-1-1-1-1-1-1-1-1-3-1-5h1v-3-5c-1-4-1-10 0-15l1 1v2h1v-1z"></path><path d="M127 265v-3h1v3l-1 1v4-5z" class="E"></path><path d="M127 270v-4l1-1v6l1 9c0-1-1-1-1-1 0-1-1-1-1-1-1-1-1-3-1-5h1v-3z" class="H"></path><defs><linearGradient id="AK" x1="124.895" y1="225.381" x2="137.6" y2="221.486" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#454545"></stop></linearGradient></defs><path fill="url(#AK)" d="M147 195v1c2 0 2-1 4-2l1-1c1-2 4-5 6-6h1l-5 5 1 1h0l-4 4-1 1h0v1l6-4 2 1c2-2 4-3 6-4l2 3-6 3c-6 2-11 6-15 11-3 4-7 7-9 12-1 2-3 3-4 6-1 2-2 5-4 7 0 1-1 1-2 1 1-6 2-12 4-18h1c0-2 2-6 4-8 0 0 1-1 1-2h2l9-12z"></path><path d="M145 201l9-9 1 1h0l-4 4-1 1h0v1c-3 2-5 5-8 8-1 1-2 3-3 5l-2 2h-1c1-2 3-4 4-6l5-7z" class="W"></path><path d="M147 195v1c2 0 2-1 4-2l1-1c1-2 4-5 6-6h1l-5 5-9 9h-1c-2 2-3 4-5 6h-1l9-12z" class="D"></path><path d="M156 195l2 1-8 7c-2 1-4 3-6 5v-1l-2 2h0c-1 1-1 1-1 2l-2 2v-1c1-2 2-4 3-5 3-3 5-6 8-8l6-4z" class="U"></path><path d="M164 192l2 3-6 3c-6 2-11 6-15 11l-1-1c2-2 4-4 6-5l8-7c2-2 4-3 6-4z" class="H"></path><path d="M217 147c4-1 8 0 13 1 1 0 3 0 4 2 2 3 3 6 4 9v2c-1 0-2 0-2-1-1 1-2 1-2 3l1 3h-1 0v1c1 1 1 2 1 3 0 0 1 1 2 1-1 0 0 0-1 1 0 1 0 1-1 1h-3c1 1 2 1 3 1 3 1 8 1 10 4h-2c-3 1-4 0-6 0s-4-1-6 0c-1-1-1 0-2 0s-1-1-1-1c-3 0-5 2-7 3l-7 4 2 2c-1 0-2 1-3 1s-1 0-1-1h-1l-13 2-16 3c-3 1-6 1-8 2h-3l-5 2-2-3c-2 1-4 2-6 4l-2-1-6 4v-1h0l1-1 4-4h0l-1-1 5-5c3-2 7-4 11-6l2-2v-1c0-1-1-1-1-2l6-3h0l3-2 10-4v-1c4-1 8-1 12-1l14 1h4c4-1 6-4 8-7l-1-1v1h-1s0-1-1-1c0-2 1-5 2-7 0-1 1-1 2-2h-1c-3-1-5 0-9 0h1c0-1 1-1 2-1h2c-1-1-3-1-4-1-1 1-2 0-3 0z" class="S"></path><g class="C"><path d="M171 176l6-3-1 1s-1 1-2 1v2c1 1 2 0 3 0l-5 2v-1c0-1-1-1-1-2z"></path><path d="M190 167c-1 1-2 1-3 2h0 5l-1 1c-4 0-9 2-11 3h-1-2l3-2 10-4z"></path></g><path d="M227 151c1 1 2 1 2 1h1c-1 2 0 3-1 4 0 1-1 2-1 3l-1-1v1h-1s0-1-1-1c0-2 1-5 2-7z" class="Y"></path><path d="M229 152h1c-1 2 0 3-1 4 0 1-1 2-1 3l-1-1c1-1 1-1 1-3l1-3z" class="W"></path><path d="M177 177l7-1c3-1 6-2 9-2l-7 3v1c-1 0-2 0-3 1h-3-1 0c-1 1-4 1-6 2h-3l2-2 5-2z" class="D"></path><path d="M217 147c4-1 8 0 13 1 1 0 3 0 4 2 2 3 3 6 4 9v2c-1 0-2 0-2-1l-3-3c-1-1-2-3-3-4 1 0 2 1 3 2 0 1 0 1 1 1h0c0-1-1-2-1-3-1 0-1-1-2-1h-1-1s-1 0-2-1c0-1 1-1 2-2h-1c-3-1-5 0-9 0h1c0-1 1-1 2-1h2c-1-1-3-1-4-1-1 1-2 0-3 0z" class="Q"></path><path d="M186 178c7-1 13-1 20-3 2-1 5-2 8-4 1 0 3-2 5-2v2c2 0 5-1 7-2 1 0 2-1 4-1 0-2-1-8 0-10 1 4 0 8 2 11 1 1 2 2 3 2l1 1c0 1 0 1-1 1h-3-1c-2-1-2-2-5-2-3 1-6 3-9 4l-3 4c-2 0-4 1-6 1-1 0-1 0-2-1-1 0-2 1-3 1l-4 1c-1 1-2 1-3 1s-2 0-3 1c-2 0-5 0-7 1h-3c-3 1-7 2-10 3-1 1-2 2-4 2v1l-5 2c-2 1-4 2-6 4l-2-1-6 4v-1h0l1-1 4-4h0l-1-1 5-5c3-2 7-4 11-6h3c2-1 5-1 6-2h0 1 3c1-1 2-1 3-1z"></path><path d="M206 179l11-4-3 4c-2 0-4 1-6 1-1 0-1 0-2-1z" class="T"></path><defs><linearGradient id="AL" x1="182.842" y1="182.62" x2="190.22" y2="181.861" xlink:href="#B"><stop offset="0" stop-color="#5c5b5d"></stop><stop offset="1" stop-color="#838182"></stop></linearGradient></defs><path fill="url(#AL)" d="M180 183l10-2c5-1 11-3 16-3-3 1-6 2-8 3h1c-1 1-2 1-3 1s-2 0-3 1c-2 0-5 0-7 1h-3 0c-2-1-2-1-3-1z"></path><path d="M155 193l7-4c6-3 12-5 18-6 1 0 1 0 3 1h0c-3 1-7 2-10 3-1 1-2 2-4 2v1l-5 2c-2 1-4 2-6 4l-2-1-6 4v-1h0l1-1 4-4h0z" class="D"></path><path d="M166 189c1 1 2 0 3 0v1l-5 2c-2 1-4 2-6 4l-2-1-6 4v-1h0l1-1 4-4c1 1 7-3 9-4h2z" class="O"></path><path d="M166 189c1 1 2 0 3 0v1l-5 2c-2 1-4 2-6 4l-2-1c1-1 3-2 5-3s3-2 5-3z" class="P"></path><path d="M217 175c3-1 6-3 9-4 3 0 3 1 5 2h1c1 1 2 1 3 1 3 1 8 1 10 4h-2c-3 1-4 0-6 0s-4-1-6 0c-1-1-1 0-2 0s-1-1-1-1c-3 0-5 2-7 3l-7 4 2 2c-1 0-2 1-3 1s-1 0-1-1h-1l-13 2-16 3c-3 1-6 1-8 2h-3l-5 2-2-3 5-2v-1c2 0 3-1 4-2 3-1 7-2 10-3h3c2-1 5-1 7-1 1-1 2-1 3-1s2 0 3-1l4-1c1 0 2-1 3-1 1 1 1 1 2 1 2 0 4-1 6-1l3-4z" class="M"></path><path d="M203 180c1 0 2-1 3-1 1 1 1 1 2 1-1 1-2 1-3 2l-2-2z" class="E"></path><path d="M199 181l4-1 2 2c-2 0-4 1-7 0h-2c1 0 2 0 3-1z" class="V"></path><path d="M186 184c2-1 5-1 7-1-1 0-1 1-2 1h-1 1c0 1-1 1-2 1-1 1-3 1-4 1h-2c1-1 2-1 3-2zm-5 5v2h1c-3 1-6 1-8 2h-3v-1c1 0 3 0 4-1l6-2z" class="F"></path><path d="M183 184h3c-1 1-2 1-3 2-4 1-10 4-14 4v-1c2 0 3-1 4-2 3-1 7-2 10-3z" class="E"></path><path d="M217 175c3-1 6-3 9-4 3 0 3 1 5 2h1c1 1 2 1 3 1 0 1-1 1-1 1h-2c-2 0-4-2-5-2l-2 1-11 5 3-4z" class="H"></path><path d="M198 185c1 0 3-1 4-1h3c1-1 3 0 4 0l2-1c1 0 3 0 4-1l2-1c1-1 2-1 4-1l-7 4 2 2c-1 0-2 1-3 1s-1 0-1-1h-1l-13 2 1-1c1 0 1 0 2-1h1 0-4c0-1-1 0 0-1z" class="L"></path><path d="M211 186l3-2 2 2c-1 0-2 1-3 1s-1 0-1-1h-1z" class="I"></path><path d="M181 189c1-1 2-2 3-2h4v-1h2 0 1 1l2-1h0 1 1 2c-1 1 0 0 0 1h4 0-1c-1 1-1 1-2 1l-1 1-16 3h-1v-2z" class="C"></path><path d="M356 224l61-1h13v2c7 1 14 0 20 0 2 0 6 0 7 1-1 3-1 6-2 9h-1c-1 0-2 1-3 2-3-1-5 2-7 4l-5 4c-9 10-13 22-17 35-2 5-4 10-5 16l-11 34-25 74c2-2 2-4 3-6 0-1 0-2 1-3h1c0 1 0 1-1 2v2h0c0 1 0 1-1 1v2h0 0c0 1 0 1-1 1 0 1 0 1 1 1l-36 107-11 32-8 22c-2 6-3 12-5 17l-4 12c-2 5-4 10-5 15 0-1 0-2-1-3 0 1 0 1-1 1l-1-1c0 1 0 2-1 2l-1-3h1c1-1 1-3 1-4-1-6-4-12-6-18l-3-11-8-24c-2-5-3-11-5-16-1-5-4-11-5-16l-12-36-24-74c-4-11-7-23-12-34l-36-105-2-5c-2-3-4-6-6-8-4-6-9-13-15-17-1-1-3-1-4-2s-2-1-2-3c-1-2-2-5-4-7h12c2 0 2-1 3-2h2 94l2 2h6c-2 3-4 9-3 12v1c-3 1-6 2-8 3-7 5-14 12-17 19-2 5-3 9-3 14v3 1c1 2 0 5 1 7 0 4 0 8 1 11l3 9v2c0 1 2 3 2 4s1 3 1 4v1c0 1 0 2 1 2l2 6 5 16c1 2 1 4 2 6l8 23c1 3 1 5 2 8l9 25 4 13c0 2 1 3 1 5l7 20c1 1 1 2 2 3l3 8-1 3 5 16 1 4c1 5 3 10 4 15l3 3 3 8c1-2 2-7 3-9l8-24 3-11 3-7 9-28 6-18 5-16 6-17 5-14c1-7 4-13 6-19 5-15 8-30 6-46-3-19-14-34-29-45l-1-1c0-1-1-2-1-3-3-1-6-3-9-3v-1c2-3 0-9-1-13 2 0 5 1 7 0h1 1c0-1 0-1-1-1h0 4z"></path><path d="M354 242l3 3-1 1-1-1c0-1-1-2-1-3z" class="X"></path><path d="M312 601v5c0 1 0 2-1 2l-1-3h1c1-1 1-3 1-4z" class="D"></path><path d="M305 440c1 1 1 2 2 3l3 8-1 3c-2-5-3-9-4-14z" class="E"></path><path d="M319 489l3 3 3 8v2h-1c1 2 1 3 1 4h0c0-1 1-1 1-2s1-1 1-1l-2 7-6-21z" class="T"></path><defs><linearGradient id="AM" x1="312.218" y1="591.274" x2="327.684" y2="586.296" xlink:href="#B"><stop offset="0" stop-color="#414243"></stop><stop offset="1" stop-color="#6d6e6d"></stop></linearGradient></defs><path fill="url(#AM)" d="M314 606c3-11 6-22 10-33 0 2 1 4 0 6v3l-4 12c-2 5-4 10-5 15 0-1 0-2-1-3z"></path><defs><linearGradient id="AN" x1="315.186" y1="567.49" x2="349.723" y2="536.604" xlink:href="#B"><stop offset="0" stop-color="#6e6f72"></stop><stop offset="1" stop-color="#a4a2a1"></stop></linearGradient></defs><path fill="url(#AN)" d="M345 510v1 2c-1 1-1 3-1 4l-3 9 1 1v-2l1-1v-1-1c1-1 1-3 1-4 1 0 1 0 1-1v-1l1-2c1-1 0-1 1-1 0-1 0-2 1-3v1l-11 32-8 22c-2 6-3 12-5 17v-3c1-2 0-4 0-6 1-2 2-5 2-7l11-31 5-16 3-9z"></path><path d="M381 404c2-2 2-4 3-6 0-1 0-2 1-3h1c0 1 0 1-1 2v2h0c0 1 0 1-1 1v2h0 0c0 1 0 1-1 1 0 1 0 1 1 1l-36 107v-1c-1 1-1 2-1 3-1 0 0 0-1 1l-1 2v1c0 1 0 1-1 1 0 1 0 3-1 4v1 1l-1 1v2l-1-1 3-9c0-1 0-3 1-4v-2-1c0-1 1-2 1-2l3-8 26-79 6-17z" class="V"></path><defs><linearGradient id="AO" x1="387.209" y1="282.101" x2="355.315" y2="301.367" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#888887"></stop></linearGradient></defs><path fill="url(#AO)" d="M357 245c14 8 26 25 30 41 6 23-3 46-10 68l-6 18c0-1-1-1 0-2v-1-1c1-1 0-1 1-1v-1-1h0c0-1 0-1 1-2v-1s0-1 1-1v-1-1-1h1v-3c1-2 2-3 2-5v-1l1-1v-1l1-2v-1-1h1v-1l-1-2c-3 5-4 11-6 16h0c1-7 4-13 6-19 5-15 8-30 6-46-3-19-14-34-29-45l1-1z"></path><path d="M373 356h0c2-5 3-11 6-16l1 2v1h-1v1 1l-1 2v1l-1 1v1c0 2-1 3-2 5v3h-1v1 1 1c-1 0-1 1-1 1v1c-1 1-1 1-1 2h0v1 1c-1 0 0 0-1 1v1 1c-1 1 0 1 0 2l-44 131s-1 0-1 1-1 1-1 2h0c0-1 0-2-1-4h1v-2c1-2 2-7 3-9l8-24 3-11 3-7 9-28 6-18 5-16 6-17 5-14z" class="E"></path></svg>