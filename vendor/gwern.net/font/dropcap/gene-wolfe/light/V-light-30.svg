<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="174 78 694 816"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#c5c1bf}.C{fill:#ada8a7}.D{fill:#272526}.E{fill:#363435}.F{fill:#131313}.G{fill:#353334}.H{fill:#171717}.I{fill:#d5d0cb}.J{fill:#edecea}.K{fill:#e5e3e2}.L{fill:#020202}.M{fill:#1e1c1d}.N{fill:#878384}.O{fill:#d9d5d3}.P{fill:#454244}.Q{fill:#868384}.R{fill:#706c6d}.S{fill:#4c4a4b}.T{fill:#5c585a}.U{fill:#534f52}</style><path d="M797 298c0-1 1-1 2-2 0 4 0 6-2 9v-3-4z" class="L"></path><path d="M528 557c1 4 0 6-2 10l-2 3v-3c1-1 2-7 2-8s1-1 2-2z" class="E"></path><path d="M437 624l1 1v1h-1c-1 1-1 0-1 1h0l-2 2-10-4s1 0 1-1h1l8 2c1 0 2-1 3-2z" class="I"></path><path d="M538 144c-2 0-5-4-6-6h5c2 2 3 3 3 5l1 1h-3z" class="D"></path><path d="M471 172l1 1h1l1 1h3c0 1-1 2-1 2-2 1-2 2-4 2-2-1-2-1-3-2-1-2 0-2 0-4h2zm275 8h1v1 5c0 3-4 8-5 10 0-6 2-11 4-16z" class="G"></path><path d="M559 461c1 1 1 1 1 2v2l-5 17v-5l1-5c1-4 3-7 3-11z" class="N"></path><path d="M586 624h1l18-5h0-1c-5 2-10 4-15 7-2 1-4 3-5 5v1l-2 1c1-2 1-5 2-7 1-1 2-1 2-2z" class="G"></path><path d="M553 328c-1-2-2-4-2-6l1-1c2 1 4 4 6 6l12 9-1 1c-5-3-9-7-14-10h-1c-1 0-1 0-1 1z" class="D"></path><path d="M615 621c6-3 14-6 20-6l-13 8v-1c0-1 1-1 2-2l5-3-8 2 1 1v1h-2c-1 1-3 2-4 3l-8 5-1-1h1l3-2 4-4v-1z" class="E"></path><path d="M619 240l2 2-1 6c0 2 0 3-1 5l-1 1-1-1c-2-1-2-1-3-3v-2c1-4 3-6 5-8z" class="D"></path><path d="M614 250c1-2 2-2 3-3 2 0 2 0 3 1 0 2 0 3-1 5l-1 1-1-1c-2-1-2-1-3-3z" class="F"></path><path d="M537 138h8c1 0 2 0 3 1s3 4 4 5h-11l-1-1c0-2-1-3-3-5z" class="E"></path><path d="M329 270c1 3 3 6 4 9 1 2 2 4 2 7 0 1 1 3 1 4 1 0 2 1 3 1 0 1-1 3-2 4v1h-1c0-1 0-3-1-4-2-3-2-7-4-10l-2-7c1-1 0-4 0-5z" class="K"></path><path d="M463 144c0-1 2-2 2-2 1-2 1-2 2-3h-2-2l15-1v1c-1 1-2 3-2 4l-13 1z" class="C"></path><path d="M618 254l1-1c0 3-1 5-2 7 0 4-1 8-3 12h0c-1 2-2 5-3 8l-1 2h0c-1-1-1-3-2-5h1 1v-3c1-1 1-2 2-3 1-5 6-12 6-17z" class="G"></path><path d="M240 286l1 1 1-2h1l-1 2v1c-1 3-3 6-4 9-2 4-2 8-2 12-3-3-4-4-5-8l1-2c1 1 1 2 2 2h0v-4c1-1 1-3 2-4v-1l2-2v-1c1-1 1-2 2-3z" class="F"></path><path d="M275 203c7 4 13 8 19 13l4 4c3 2 5 4 7 6 1 2 2 3 3 4-3-1-4-4-6-6-2-1-3-2-4-3s-2-3-3-3l-2-2-1 1c-7-6-13-10-21-13h2 2v-1z" class="C"></path><path d="M531 586l2 2v1 1c0 1 1 2 2 4 0-1 0-2 1-2h0 0c-1 2 0 5-1 6 0 1 1 2 0 3 0 1-1 7 0 8v2h1c0 1 1 3 0 4v5-2c0-1 0 0 1-1v-2 1l-2 11c0-1-1-1-1-2s-1-4 0-5v-2c-1-4 0-9 0-14l-3-18z" class="O"></path><path d="M543 523c1 5-2 8-2 13l-1-2c0 1-1 2-1 3-1 0-1 1-2 2v1c-1 1-1 1-1 2-1 3-5 6-7 8h-2l16-27z" class="B"></path><path d="M437 272c3 1 6 2 7 5 1 2 1 5 1 7-1 1-1 2-2 3-1-1-2 0-3-1h1c0-2-1-4-1-6h0c-1 0-2-1-2-1h-1v-1h1 1l-3-2c-1-1-2-1-4-1l2-2c1 0 2 0 3-1z" class="P"></path><path d="M562 171c-1 2 0 3-1 5-1 1-2 1-3 1-2 1-3 0-5-1-1-2-1-4-1-6 0-5 4-8 7-11l2 1c-3 3-6 7-5 12 1 1 1 1 2 1l3-3 1 1z" class="G"></path><path d="M545 138l22 1h-6v1 2c2 2 6 0 8 1 2 0 5 0 7 1h-24c-1-1-3-4-4-5s-2-1-3-1z" class="B"></path><path d="M661 516h0 1c1 0 3 0 5-1l11-2c1-1 4-1 5-1s2 1 3 1c-11 3-20 6-30 10 1-2 2-2 4-3h1v-1l-2 1h-1-3c1 0 2-1 3-2l3-2z" class="G"></path><path d="M409 238c4 3 6 6 7 11 0 1 0 2-1 3l-3 2c1 2 2 4 2 6l1 4c-1-2-1-3-2-5h0c0 2 1 4 0 6l-1-5c-1-5-2-12-3-17 0-2-1-3 0-5z" class="D"></path><defs><linearGradient id="A" x1="739.898" y1="209.171" x2="748.517" y2="211.192" xlink:href="#B"><stop offset="0" stop-color="#6c6867"></stop><stop offset="1" stop-color="#848082"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M752 204l3 2c-3 2-7 3-10 6l-6 4-2 2-1-1c-2 1-4 3-5 4-3 3-6 7-9 10-2 2-3 5-5 6 7-14 22-26 35-33z"></path><path d="M786 276h5v-2l1-2v3c0 1 2 2 2 3s0 3-1 4c2 4 6 9 6 14-1 1-2 1-2 2-1-7-5-12-10-17v-1-3l-1-1z" class="F"></path><path d="M787 277h5v1c-1 2-3 2-5 3v-1-3z" class="B"></path><path d="M548 746h0c1-1 1-2 1-3l2-3 4-8 1-1c0 2 0 4-1 6 0 0 0 1-1 2l-1 3c0 4-1 9-3 13h-1l-2 2h0-3v-1-2c1-2 2-3 2-5l-1 1v-1c1-1 1-2 3-3z" class="N"></path><path d="M544 756l3-3c1 0 1-1 1-2s1-2 1-2v-1c1-3 3-6 4-9v-1h1c-1 1-1 2-1 3v1c0 4-1 9-3 13h-1l-2 2h0-3v-1z" class="Q"></path><path d="M264 158h1c2 2 5 4 7 6 1 1 0 2 1 3s3 2 4 4c1 1 2 1 3 0 5 8 8 17 8 26-3-7-7-14-12-20 1-1 1-3 1-4-1-1-3-3-3-4-4-4-5-7-9-10-1-1-1 0-1-1z" class="G"></path><defs><linearGradient id="C" x1="496.696" y1="141.422" x2="483.753" y2="140.073" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#3c3a3b"></stop></linearGradient></defs><path fill="url(#C)" d="M478 138h22c-3 2-5 4-8 7 0-1 0-1-1-1-1-1-5 0-7 0h-26 5l13-1c0-1 1-3 2-4v-1z"></path><path d="M254 213c9-3 19 2 27 7 9 5 18 13 24 22 9 12 14 24 17 38 0 4 1 8 1 11v1h-1v-3c-1-2-1-4-1-5v-3l-1-1v-2-4l-1-2h0c0-1-1-2-1-2l-1-4c0-1-1-2-1-3l-1-2h0c0-1 0-2-1-2l-1-3-4-6-3-5s-1-1-1-2l-1-1-1-1-1-1c-1-1-2-2-3-4h0l-4-4h-1l-3-3h-1c-1 0-2-1-2-2v-1s-1 0-2-1h-1c-1-2-2-2-3-3s-3-2-4-3h-1l-1-1s-1 0-2-1h-1 0c-1-1-1-1-2-1s0 0-1-1h-2l-4-1c-3-1-6 0-9-1h-1z" class="B"></path><path d="M573 372c1 5 1 12 1 17 0 3-1 5 0 7 1 1 1 1 1 2v2c-1 3-2 5-2 8-1 2-3 3-2 6h0v2c-1 1-1 2-1 4 2 0 2-2 3-4 0 0 1-1 2-1l-5 17-1-1c1-2 1-4 2-6 1-1 0-1 1-2h-1l-1 1h-1l-1-1c-1 1-2 2-3 2v1c0 1 0 2-1 3-1-6-5-11-7-16 2 1 3 2 4 4l1 1c1 2 1 4 3 6v-2h1c3-6 6-13 7-19v-10-21z" class="S"></path><path d="M582 767c6-4 14-6 21-5 6 2 13 6 16 12 5 6 5 14 4 22-2 7-6 15-12 19l-1 1c0-1 1-2 2-3h0c-2 1-3 2-5 2l5-4c6-6 10-13 10-21 0-5-1-10-4-15-3-6-9-10-16-12-6-1-14 1-20 5v-1z" class="M"></path><path d="M779 280l1-1h2c1 0 2 1 4 1 1 1 0 1 1 1 5 5 9 10 10 17v4 3c0 2-1 3-3 5 0-9-1-16-7-22a30.44 30.44 0 0 0-8-8z" class="F"></path><path d="M782 279c1 0 2 1 4 1 1 1 0 1 1 1 5 5 9 10 10 17v4h-1c-1-11-8-15-14-23z" class="I"></path><path d="M235 210v-1c0-1 2-2 2-2 4-3 7-6 12-7 7-2 19-1 26 3v1h-2-2c-8-2-17-3-25 1l-1 1c-2 0-2-1-4 0l-3 3v1l-1 1-1 2c-1 2-1 5-1 8l-1 1c-1-4-1-8 1-12z" class="B"></path><path d="M701 270c1-1 1-1 1-2 1-2 2-4 4-5-2 6-4 13-4 20-1 1-1 1-1 2s0 1-1 2c-1 2-1 5-2 8-1 1-1 3-1 5h0c-2-1-3-3-4-4l-2-3c0-1-2-4-3-5 2 0 3 0 4 2l2 2h1c0-3 1-4 2-6 2-5 3-11 4-16z" class="C"></path><path d="M717 237c2-1 3-4 5-6 3-3 6-7 9-10 1-1 3-3 5-4l1 1c-10 9-19 20-25 32l-4 9c-1 1-1 3-2 4-2 1-3 3-4 5 0 1 0 1-1 2-1-7 9-22 12-29l1-1 1-1c0-1 1-1 2-2z" class="I"></path><path d="M237 281l1 1c0-1 1-1 1-2 1 0 1-1 2-1v-1l3-3h3c0 1 1 1 1 2l3-2 1-1v1l-6 6c-1 1-2 3-4 3l-2 2c-1 1-1 2-2 3v1l-2 2v1c-1 1-1 3-2 4v4h0c-1 0-1-1-2-2l-1 2c-1-5 0-9 2-13 2-2 3-4 4-7z" class="M"></path><path d="M726 144c3-1 7 0 10-1h4 9 0v-3h-7c-1-1 0 0-2-1h0 33c2 0 5 0 7-1 2 0 4-1 7 0-14 5-28 5-42 6h-19zm-483-6c5 0 10 1 15 1h21c5 0 10 0 14 1h-5-1 0-3v1 2h5 5c3 1 6 0 8 1h-19c-13 0-28 0-40-6z" class="C"></path><path d="M580 368c2 4 3 7 4 11v1l-3 12s0 2-1 3l-5 20c-1 0-2 1-2 1-1 2-1 4-3 4 0-2 0-3 1-4v-2h0c-1-3 1-4 2-6 0-3 1-5 2-8s3-6 3-9c3-5 3-11 2-17 0-2-1-4 0-6z" class="T"></path><path d="M571 414l3-7 5-12h1l-5 20c-1 0-2 1-2 1-1 2-1 4-3 4 0-2 0-3 1-4v-2h0z" class="E"></path><path d="M592 608c2-7 6-14 9-20 4-11 9-22 15-32l9 3 1 2v3c-3 0-8-2-10 0-7 4-21 41-25 50-1-1 0-3 1-5v-1z" class="H"></path><defs><linearGradient id="D" x1="595.74" y1="639.206" x2="599.666" y2="619.275" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2e2c2c"></stop></linearGradient></defs><path fill="url(#D)" d="M584 632l31-11v1l-4 4-3 2h-1c-4 2-8 4-13 6-3 1-10 2-13 5 0-2 1-4 1-6l2-1z"></path><path d="M570 336c9 7 15 15 19 26l-4 16c-2-2-1-3-1-5 0-5-2-9-3-13 1-1 1-2 1-4-3-8-7-14-13-19l1-1z" class="G"></path><path d="M582 356c2 6 3 12 3 17h-1 0c0-5-2-9-3-13 1-1 1-2 1-4zM473 812c7-7 12-15 16-25l4 19c1 2 2 6 2 9h-1v-1-1-1c0-1 0 0-1-1v-2-1c0-1-1-1-1-2l-1-2v-2-2c-1 0-1-1-1-2h0l-4 3-2 1v1h-1l-1 1c-2 1-2 2-3 2 0 1-1 2-1 2v1c-1 1-2 2-3 4 0 2-4 5-6 5v1c-1 0-1 1-2 1h-1c-2 1-4 2-6 2-1 1-2 1-2 1h-1-2v1h-2-1c-2 0-4 0-5 1h-10l-1-1c-1 0-1 1-2 0-1 0-1 0-2-1h-1l-2-1c8 2 15 2 22 1 9-1 16-5 22-11z" class="B"></path><path d="M786 235h-1 0c-3 2-5 2-8 1h-2c1 1 3 1 4 1 4 1 9-2 12-4 5-5 6-11 7-18 1 6 0 13-4 18-2 4-6 7-11 7-3 1-8 1-10-2-3-2-4-4-4-7 0-2 0-4 2-6 2-1 3-2 5-1h1l-1 1c-1 3-2 4-1 7 0 2 1 3 3 3 3 1 5 0 7-1l1 1z" class="F"></path><path d="M716 256c1-4 4-9 7-13 8-11 19-21 32-27 6-3 16-5 23-3-4 0-9 0-13 1h0c-2 0-3 1-4 2s0 0-2 1l-1 1c-1 1-2 1-4 2l-1 1h-1 0c-1 1-1 1-2 1h0-1l-1 1c-1 1-1 1-2 1-2 1-3 2-4 3-1 0-3 1-4 2s-2 2-4 3l-6 6c0 2-1 2-2 3s-1 2-2 3l-1 2-2 2c0 1-1 2-2 3l-3 7c0 1-1 2-1 3l-3 7v2l-1 1v2 1h0v1c-1 1-1 2-1 3v1c0 1 0 2-1 3 0 4 1 8 0 12 1 1 0 2 1 4 0 1 0 1 1 3v3c-6-16-1-33 5-48z" class="B"></path><path d="M423 818c-2-1-4-2-6-4-7-6-10-15-11-24 0-7 2-15 7-20 4-5 10-8 16-8v1c-7 1-13 4-17 11-5 6-5 13-4 20 2 7 8 17 14 20l1 1 2 2h1c1 1 2 1 3 1l3 1c2 1 3 1 5 1 4 1 8 1 12 1l-1 1h1 1c1 0 1 0 1 1-7 1-14 1-22-1-2-1-4-2-6-4z" class="F"></path><path d="M423 818c3 0 7 2 11 3 3 0 6 1 8 1s4-1 6 0h1 1c1 0 1 0 1 1-7 1-14 1-22-1-2-1-4-2-6-4z" class="G"></path><path d="M432 275c2 0 3 0 4 1l3 2h-1-1v1h1s1 1 2 1h0c0 2 1 4 1 6h-1c-1-1-2-2-4-3-4 2-6 3-8 8v2h0c-2 7-1 15 0 22h-1l-2-6-2-10v-3c1-2 0-3 0-5h1c0-2 0-3 1-4 1-4 1-7 4-10 1-1 2-2 3-2z" class="D"></path><path d="M436 283l-1-1c-2 0-2 1-3 2h-1v-3c2-1 3-1 5-2 1 1 2 1 4 1 0 2 1 4 1 6h-1c-1-1-2-2-4-3z" class="E"></path><path d="M432 275c2 0 3 0 4 1l3 2h-1-1v1h1s1 1 2 1h0c-2 0-3 0-4-1-2 0-4 0-6 2-2 1-3 3-3 4-1 1-1 2-2 2 1-4 1-7 4-10 1-1 2-2 3-2z" class="B"></path><path d="M432 275c2 0 3 0 4 1 0 0 0 1-2 1h-1c-1 1-2 1-3 1l-1-1c1-1 2-2 3-2z" class="I"></path><path d="M752 204c6-3 11-4 17-5s14 1 19 4l2 2c3 2 5 4 6 7h1c0 1 1 2 1 3-1 7-2 13-7 18-3 2-8 5-12 4-1 0-3 0-4-1h2c3 1 5 1 8-1h0 1c3-3 6-7 7-11s0-10-3-14-7-7-12-7l-1-1c-8-1-15 1-22 4l-3-2z" class="B"></path><path d="M342 287l1-1c2 0 3 3 4 5 0 2 1 3 0 5s-2 2-3 3c-1 3 0 7 0 10 3 11 7 20 12 29l6 13c-1-1-2-3-3-4-2-3-4-8-6-11h-1l-6-8h0v2c-2-3-4-6-5-10-1-6-1-12-1-18 1-4 2-7 2-10-1-1-1-2-1-3v-1l1-1z" class="M"></path><path d="M341 288l1-1c0 2 0 4 1 6 0 3-1 7-1 10 0 12 5 23 11 33h-1l-6-8h0v2c-2-3-4-6-5-10-1-6-1-12-1-18 1-4 2-7 2-10-1-1-1-2-1-3v-1z" class="C"></path><path d="M539 794c2-1 1-5 3-6 3 12 12 24 23 31 5 2 11 4 16 5 8 0 15-1 23-4-2 2-2 2-4 3h-1c-1 1-3 1-4 1h-2l-1 1c-3 0-10 1-12-1h-3c-1 0-1 0-2-1h-2c-1 0-2 0-2-1h-3-1c-1-1-2-1-3-2h0c-1 0-1 0-2-1h-1c-1-1-1-2-2-2l-2-2-2-1c-1-1-2-1-2-2-3-1-5-3-7-4h-1-2l-1-1h-1c-1 0-2-1-3-2l-1 1v1 1c0 1-1 2-1 3v1c0 1-1 2-1 3v1c-1 0-1 1-1 2v1 2l-1 1h0v1 1l-2 2 8-32z" class="B"></path><path d="M234 222l1-1c0-3 0-6 1-8l1-2 1-1v-1l3-3c2-1 2 0 4 0-5 4-8 8-8 14 0 4 1 7 4 10h0c1 1 1 2 2 3 2 1 5 3 8 2 2 0 3-1 4-2 1-3-1-6-1-9h0 3c2 2 3 4 3 6 1 2 0 4-1 6s-4 4-7 4c-5 1-10 0-13-3-5-4-7-8-8-14 0-4 1-9 4-13-2 4-2 8-1 12z" class="F"></path><path d="M234 222l1-1c0-3 0-6 1-8l1-2 1-1v-1l3-3c2-1 2 0 4 0-5 4-8 8-8 14 0 4 1 7 4 10 1 3 3 5 6 6 3 0 6 0 9-1v-1 1c-1 1-2 1-3 2-4 1-8 0-12-3s-6-7-7-12z" class="C"></path><path d="M608 282h0v-2l-1-1c0-3-2-5-3-8-2-10-3-20-9-29-1-3-4-5-6-8l2 1c4 1 8 3 11 6 9 7 7 22 8 33h0v3h-1-1c1 2 1 4 2 5h0l1-2c1-3 2-6 3-8h0l-3 12c-1 0-1 0-2 1h0-1v-3z" class="D"></path><path d="M608 277c-1-11-3-25-10-34-1-3-4-4-6-6 7 3 12 6 14 13 2 5 2 11 2 16l1 11h-1z" class="K"></path><path d="M542 782c1 1 1 2 1 3h1c4 13 12 25 24 31 2 1 5 2 7 3h3c1 0 7 1 8 1 1-1 4-1 5-1l5-1h2c1 0 1-1 2-1s2-1 3-1h0 0c2 0 3-1 4-1 2 0 3-1 5-2h0c-1 1-2 2-2 3-2 2-4 3-6 4-8 3-15 4-23 4-5-1-11-3-16-5-11-7-20-19-23-31v-2c-1-1 0-2 0-4z" class="D"></path><path d="M543 785h1c4 13 12 25 24 31 2 1 5 2 7 3h0c-3 0-10-2-12-4-11-6-17-18-20-30z" class="B"></path><path d="M607 299c-1 4-2 8-4 11 1-8 2-17-4-23-1-2-4-4-6-3-2 0-3 2-5 2-2-1-3-2-3-4s0-4 1-6c2-2 5-3 8-3 4-1 6 0 8 3h1c0-1 0 0-1-1-1-3-3-4-6-5h-1c2 0 3-1 5 0 3 1 5 5 6 8l1 2 1 2v3c0 3 0 4-1 6v1l-1 1c0 1 0 3 1 5v1z" class="D"></path><path d="M607 280l1 2v3c0 3 0 4-1 6v1l-1 1v-2-2c-1-2-1-2-1-4 1-2 2-4 2-5z" class="F"></path><path d="M592 277l2-1c2-1 3 0 5 0 3 2 5 5 5 9v1h0l-1-1c0-2-3-4-5-5-1-1-5 0-7 1h-1v1h-1-1c1-1 1-2 2-2 0-1 1-1 1-2l1-1z" class="O"></path><path d="M592 277l2-1c2-1 3 0 5 0-2 2-2 2-5 2-1 0-1 0-2-1z" class="K"></path><path d="M525 592c1-2 1-5 2-7l3-12v-1l1 1c-1 1-1 1-1 2v1 1h-1v2h0v2h-1v2l1 2h1l1-1v2l3 18c0 5-1 10 0 14v2c-1 1 0 4 0 5s1 1 1 2c-2 12-4 26-4 38l-1 1h0v-28c1-1 0-4 0-5l1-1c0-2 0-6-1-8l-1-2v-1l-1-2-1-2v-2-1c1-2 1-9 0-10 1-1 2-2 3-4 0-1 0-3-1-4-1-2-2-3-4-4z" class="B"></path><path d="M320 309v1l-6 16 1 2-2 3-2 4c-1 1-2 3-3 4-1 0-2-1-3-1l-2 3-5 6-5 5-9 9c-11 13-21 30-23 47-1 22 6 40 17 58h-1 0 0v1c1 1 3 3 3 5v1c-4-5-8-12-11-19-8-16-12-36-8-54 3-15 11-28 21-39 7-8 15-15 22-23 7-9 12-19 16-29z" class="L"></path><g class="C"><path d="M312 329l2-3 1 2-2 3-1-2z"></path><path d="M312 329l1 2-2 4-2-1 3-5z"></path></g><path d="M309 334l2 1c-1 1-2 3-3 4-1 0-2-1-3-1l4-4z" class="B"></path><path d="M420 271c1-7 0-14 2-21h0c2-6 5-10 10-13 2-1 4-2 7-2h2c-3 3-6 6-9 10-4 10-4 21-7 31v-1c2-3 4-5 8-6v1c-2 1-4 3-5 5 3-2 5-3 9-3-1 1-2 1-3 1l-2 2c-1 0-2 1-3 2-3 3-3 6-4 10-1 1-1 2-1 4h-1c0 2 1 3 0 5v3l-3-10v-3c0-7-2-16-5-22l-1-4c4 3 3 7 6 11z" class="M"></path><path d="M422 257c1-6 3-13 9-17 1-2 3-3 6-4l-6 6c-3 6-5 13-6 20h-1c-1-1-1-3-2-5z" class="K"></path><path d="M422 257c1 2 1 4 2 5h1c-1 8-1 17-4 24h-1c0-7-2-16-5-22l-1-4c4 3 3 7 6 11v5l2-19z" class="C"></path><defs><linearGradient id="E" x1="556.509" y1="328.762" x2="516.085" y2="363.588" xlink:href="#B"><stop offset="0" stop-color="#1c1a1a"></stop><stop offset="1" stop-color="#3c3b3b"></stop></linearGradient></defs><path fill="url(#E)" d="M513 287l2-13c5 38 16 75 32 110l10 22c3 5 6 11 8 16v2c-2-2-2-4-3-6l-1-1c-1-2-2-3-4-4l-11-21-6-12c-4-13-10-27-14-40l-8-33-2-10c-1-2-1-4-2-6s0-2-1-4z"></path><defs><linearGradient id="F" x1="515.61" y1="345.522" x2="471.892" y2="347.524" xlink:href="#B"><stop offset="0" stop-color="#1c1a16"></stop><stop offset="1" stop-color="#413e40"></stop></linearGradient></defs><path fill="url(#F)" d="M513 287c1 2 0 2 1 4 0 10-3 19-6 29-7 28-16 55-29 82 0 4-2 8-4 12 0 1-2 3-1 6l-7 15h-1v-3-3l-1-1h0l-1-1c-1-2-1-3-3-3l-1-2h1c1 0 1 0 2 1v-1l1 1c5-8 9-17 13-25 11-23 20-46 27-71 4-13 6-27 9-40z"></path><path d="M465 428c0-5 5-11 8-16 2-3 4-7 6-10 0 4-2 8-4 12 0 1-2 3-1 6l-7 15h-1v-3-3l-1-1z" class="I"></path><path d="M608 629l8-5c1-1 3-2 4-3h2v-1l-1-1 8-2-5 3c-1 1-2 1-2 2v1l-19 16c-8 7-14 15-21 23-3 3-7 7-9 11h0l-2 2h0c1-3 2-7 3-10l4-16c1-3 1-7 3-10h1-1c3-3 10-4 13-5 5-2 9-4 13-6l1 1z" class="F"></path><path d="M581 639c3-3 10-4 13-5 5-2 9-4 13-6l1 1c-5 3-10 7-14 11-6 8-11 17-19 24l12-18c3-3 4-7 7-9-4 1-9 3-12 2h-1z" class="J"></path><path d="M553 328c0-1 0-1 1-1h1c5 3 9 7 14 10 6 5 10 11 13 19 0 2 0 3-1 4 1 4 3 8 3 13 0 2-1 3 1 5l-1 2v-1c-1-4-2-7-4-11-1 2 0 4 0 6 1 6 1 12-2 17 0 3-2 6-3 9v-2c0-1 0-1-1-2-1-2 0-4 0-7 0-5 0-12-1-17-1-13-4-23-12-33-3-3-6-7-8-11h0z" class="H"></path><path d="M578 391c-1-11 3-22-2-33h-1l1-1v1c2 2 4 7 4 10-1 2 0 4 0 6 1 6 1 12-2 17z" class="E"></path><defs><linearGradient id="G" x1="570.867" y1="354.881" x2="575.939" y2="339.218" xlink:href="#B"><stop offset="0" stop-color="#bfc1be"></stop><stop offset="1" stop-color="#faf1ed"></stop></linearGradient></defs><path fill="url(#G)" d="M555 327c5 3 9 7 14 10 6 5 10 11 13 19 0 2 0 3-1 4-4-7-9-15-15-21-3-4-7-7-10-11l-1-1z"></path><path d="M819 155c12 6 21 17 26 30 5 17 4 35-4 51-8 15-22 28-38 33-3 1-9 2-11 3l-1 2v2h-5l1 1v3 1c-1 0 0 0-1-1-2 0-3-1-4-1h-2l-1 1-10-9-8-5c-9-7-17-17-19-28l6 9c2 3 5 5 7 7l-1 1h-1-1v1l1 1c10 8 18 12 32 11 18-1 34-7 46-21 12-13 17-29 15-47-1-13-8-29-18-38-3-3-6-4-9-7h0z" class="L"></path><path d="M769 271h6 0c3 1 8 3 11 5l1 1v3 1c-1 0 0 0-1-1-2 0-3-1-4-1h-2l-1 1-10-9z" class="H"></path><path d="M775 271c3 1 8 3 11 5l1 1v3c-4-2-8-6-12-9z" class="N"></path><path d="M205 157v1c-1 1-3 2-4 3-1 2-3 3-4 5-12 15-17 35-13 53 3 11 7 20 15 29 2 1 4 3 5 4l1 1 2 2 6 3c0 1 1 1 3 2l5 3c1 0 3 1 5 1l4 2h1c1 0 3 1 4 1h4c5 1 10 1 14 1l1-1c1 0 3 1 5 0 1 0 1 0 2-1h1c1 0 2-1 3-1l2-1 4-3 3-1h0c3-2 5-5 7-7h0-2l1-1v-1l-3 2h0l12-15c-4 13-11 23-23 29-3 2-5 3-8 5l-16 16v-1l1-2h-1l-1 2-1-1 2-2c2 0 3-2 4-3l6-6v-1l-1 1-3 2c0-1-1-1-1-2h-3l-3 3v1c-1 0-1 1-2 1 0 1-1 1-1 2l-1-1c-1-1-2-2-1-3h2v-4-1c-1-1-2-1-3-1-3-2-7-3-10-4-5-2-10-5-15-8-13-9-24-24-27-40-2-9-2-19 0-29 3-14 10-26 22-34zm44-4c2 1 3 1 4 1s2 0 3 1h1c1 0 2 1 4 1v1c1 0 2 1 3 1 0 1 0 0 1 1 4 3 5 6 9 10 0 1 2 3 3 4 0 1 0 3-1 4-8-8-18-15-30-15s-24 3-33 12-15 23-14 36c0 15 6 29 16 39 6 5 12 9 19 12h0c-1 1-2 0-3 0h-1c-2-1-3-2-5-2 0-1 0-1-1-1-1-1-1-1-2-1-12-9-20-19-23-34-4-15-2-32 7-45 8-12 17-20 32-23h10l2 1h2l3 1h1c1 1 1 1 2 1s1 1 2 1l1-1c-1 0-3-1-4-2h-2c-1-1-2-1-3-1s-2 0-3-1v-1z" class="L"></path><path d="M431 176h0c5-3 10-6 16-6-17 9-27 28-33 46-2 7-3 15-5 21-2-2-2-4-3-7l-1-3h0v4h-1c-1-5-4-9-4-15l1-4c5-13 17-30 30-36z" class="M"></path><g class="C"><path d="M413 199c0 1 1 2 1 3l-3 6c0-2 0-3-1-5 0-1 2-4 3-4z"></path><path d="M410 203c1 2 1 3 1 5l-2 6c0-3 1-5-1-8 0 0 2-2 2-3z"></path></g><path d="M413 199l6-9c2-3 7-9 10-9-7 6-11 13-15 21 0-1-1-2-1-3z" class="Q"></path><defs><linearGradient id="H" x1="408.395" y1="199.448" x2="417.404" y2="205.192" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#H)" d="M431 176l1 1 1-1 1 1c-1 2-3 3-5 4h0c-3 0-8 6-10 9l-6 9c-1 0-3 3-3 4s-2 3-2 3c2 3 1 5 1 8l-4 13h0v4h-1c-1-5-4-9-4-15l1-4c5-13 17-30 30-36z"></path><path d="M408 206c2 3 1 5 1 8l-4 13h0c-1-2-2-6-2-9h0c2-4 3-8 5-12z" class="C"></path><defs><linearGradient id="I" x1="629.904" y1="216.027" x2="638.971" y2="232.518" xlink:href="#B"><stop offset="0" stop-color="#3f3b3f"></stop><stop offset="1" stop-color="#726e6f"></stop></linearGradient></defs><path fill="url(#I)" d="M631 216h1 0c1-1 2-2 3-2h1s1 0 1-1c3 0 5 0 7 1h2 1c-1 2-1 3-1 5l-3 13c-1 2-1 5-2 7-5 3-9 8-12 13-3 4-4 8-7 12v1h1c-1 0-1 0-2 1 0 0-1 1-1 2h0v1l-1 1c0 1-2 2-3 4h-1 0c0 1 0 2-1 2l-1 4v2l-2 5v2h-1c0 2 0 3-1 4v1c0 1-1 1 0 2l-1 1v1c0 1 0 1-1 1h0v-1c-1-2-1-4-1-5l1-1v-1c1-2 1-3 1-6h1 0c1-1 1-1 2-1l3-12c2-4 3-8 3-12 1-2 2-4 2-7 1-2 1-3 1-5l1-6-2-2 2-3c3-2 4-6 4-9 1-1 1-1 2-3 2-2 2-6 4-9z"></path><path d="M608 285h1 0c1-1 1-1 2-1-1 5-2 10-4 14-1-2-1-4-1-5l1-1v-1c1-2 1-3 1-6z" class="H"></path><defs><linearGradient id="J" x1="546.998" y1="371.295" x2="493.404" y2="350.449" xlink:href="#B"><stop offset="0" stop-color="#b8b3b4"></stop><stop offset="1" stop-color="#f3efec"></stop></linearGradient></defs><path fill="url(#J)" d="M514 291c1 2 1 4 2 6l2 10 8 33c4 13 10 27 14 40 0 3 1 6 1 8h-1c0-1-1-1-1-2-7-15-13-31-17-47l-8-28c-4 21-9 42-17 62-4 11-10 21-15 31-3 5-5 11-8 16-1-3 1-5 1-6 2-4 4-8 4-12 13-27 22-54 29-82 3-10 6-19 6-29z"></path><defs><linearGradient id="K" x1="456.408" y1="328.716" x2="475.747" y2="390.892" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292729"></stop></linearGradient></defs><path fill="url(#K)" d="M453 343c5-6 12-10 17-14 3-3 6-5 8-8h1c1 1 0 3 0 5-3 7-10 13-14 19-8 14-8 30-9 45v12c1 4 3 9 5 14 1 2 2 4 2 6v1c-1-1-1-1-2-1h-1l1 2c1 1 1 2 1 3-3-3-6-10-7-14s-3-8-4-12c-2-5-3-12-3-18 0-4 1-11 2-15h1v-1l1-1v3h1 0v-3c1-6 4-11 6-16 1-2 1-3 0-5-4 7-7 12-10 19-1 3-3 9-5 11v2l-2-6-2-6c1-3 2-5 4-8 2-5 6-10 9-14z"></path><defs><linearGradient id="L" x1="459.082" y1="351.634" x2="453.447" y2="345.886" xlink:href="#B"><stop offset="0" stop-color="#dcd3cd"></stop><stop offset="1" stop-color="#faf8f7"></stop></linearGradient></defs><path fill="url(#L)" d="M456 343c6-7 13-11 20-17-5 7-12 12-17 19-4 7-7 12-10 19-1-1-2-2-2-3 2-7 4-13 9-18z"></path><path d="M444 357c2-5 6-10 9-14h3c-5 5-7 11-9 18 0 1 1 2 2 3-1 3-3 9-5 11v2l-2-6-2-6c1-3 2-5 4-8z" class="D"></path><path d="M447 361c0 1 1 2 2 3-1 3-3 9-5 11l3-14z" class="B"></path><path d="M444 357c1 2-1 5 0 7 0 2 0 3-1 4l-1 3-2-6c1-3 2-5 4-8z" class="M"></path><path d="M450 368h1v-1l1-1v3h1c-1 9-3 23 1 31 1 1 2 2 2 4l2 7c0 1 1 2 1 2l1 3c0 1 1 2 1 4v2h-1l1 2c1 1 1 2 1 3-3-3-6-10-7-14s-3-8-4-12c-2-5-3-12-3-18 0-4 1-11 2-15z" class="G"></path><path d="M656 517c0-1 1-1 1-1h2 2l-3 2c-1 1-2 2-3 2h3 1l2-1v1h-1c-2 1-3 1-4 3-8 5-15 10-23 17-3 3-8 6-10 10 1 1 3 0 4 2 1 1 1 3 2 4v1 4h-3l-1-2-9-3c-6 10-11 21-15 32-3 6-7 13-9 20h-1l-3 6h-1c1-5 3-10 4-15 2-8 4-17 8-25-1-1-1-3-1-4 1-1 1-3 2-4v-1c0-5 2-10 3-15l4-16c1 1 2 3 3 3 0-3 0-3 2-5 5-6 18-9 25-11l19-4z" class="F"></path><path d="M614 546c9-10 20-18 32-23 4-2 8-4 12-5-1 1-2 2-3 2-16 7-30 18-42 31v-2l1-3z" class="K"></path><path d="M612 532l-1 2 2 7 1 5-1 3v2l-14 23c-1-1-1-3-1-4 1-1 1-3 2-4v-1c3-6 7-12 12-18 0-3-1-7-2-10 0-3 0-3 2-5z" class="B"></path><path d="M607 534c1 1 2 3 3 3 1 3 2 7 2 10-5 6-9 12-12 18 0-5 2-10 3-15l4-16z" class="H"></path><path d="M591 608l5-12c4-10 8-20 13-30 3-5 5-11 10-14 2-1 6 1 8 1v-1c1 1 1 3 2 4v1 4h-3l-1-2-9-3c-6 10-11 21-15 32-3 6-7 13-9 20h-1z" class="B"></path><path d="M627 552c1 1 1 3 2 4v1 4h-3l-1-2 1-1-1-1-1-1v-1c1 1 2 1 3 1v-1-2-1z" class="D"></path><path d="M656 517c0-1 1-1 1-1h2 2l-3 2c-4 1-8 3-12 5-12 5-23 13-32 23l-1-5-2-7 1-2c5-6 18-9 25-11l19-4z" class="M"></path><path d="M628 527l-3 3h-1c-5 3-8 5-11 11l-2-7 17-7z" class="G"></path><path d="M656 517c-3 2-8 3-12 4-6 2-11 4-16 6l-17 7 1-2c5-6 18-9 25-11l19-4z" class="J"></path><defs><linearGradient id="M" x1="455.031" y1="420.821" x2="448.601" y2="422.151" xlink:href="#B"><stop offset="0" stop-color="#7a7677"></stop><stop offset="1" stop-color="#918d8d"></stop></linearGradient></defs><path fill="url(#M)" d="M419 308l1-1c-1-1-1-1-1-2v-3h1c-1-1-1-2-2-3l1-1h0l-1-2 1-2 1 1v2l1 1c0 2 1 4 2 6h0c0 2 0 3 1 4l1 1 2 6h1c1 3 2 9 2 13l7 26c1 3 2 8 3 11l2 6 2 6v-2c2-2 4-8 5-11 3-7 6-12 10-19 1 2 1 3 0 5-2 5-5 10-6 16v3h0-1v-3l-1 1v1h-1c-1 4-2 11-2 15 0 6 1 13 3 18 1 4 3 8 4 12-1 1-1 2-1 3 1 3 1 5 1 8 1 3-1 5 0 7 1 5 1 10 2 15 1 2 1 2 0 4v3c1 1 0 3 1 5 0 1 1 2 0 3 0 1 0 1 1 2h0c1 2 1 4 1 6v2c1 1 1 2 1 3 0 2 0 4-1 6h1v1h0l-1 1v3-1-2l-1-4v-1h0c-2-2-3-9-3-12 0-2 0-5-1-7l-1 1h0c-1-1-1-2-1-3-1-2-2-5-2-7h0c-1-2-1-4-1-6-1-3-2-7-3-10l1-1s0-1-1-1v-1l-1-1h1v-2h-1c0-1-1-2-1-2-1-1-1-3-1-4-1-3-2-6-2-9v-1c-2-5-3-12-4-18 0-3-1-6-2-9s-1-6-2-9l-1-1v-3c-1-2-2-6-3-9-1-5-3-10-4-15 0-2 1-5 0-7-1-1-1-2 0-3-1 0-2-1-2-2-1-1-1-2-2-3l1-1c-1-2-3-4-3-6v-1c-1-1-1-1-1-2s0-2-1-4v-3c0-3-2-6-1-8h0l1 3 1-2z"></path><path d="M449 364c3-7 6-12 10-19 1 2 1 3 0 5-2 5-5 10-6 16v3h0-1v-3l-1 1v1h-1c1-1 1-2 1-4h0c-2 3-3 7-4 10v1c-1 3-1 6-1 10v-1c-1-1-1-3-2-4v-3-2c2-2 4-8 5-11z" class="E"></path><path d="M419 308l1-1c-1-1-1-1-1-2v-3h1c-1-1-1-2-2-3l1-1h0l-1-2 1-2 1 1v2l1 1c0 2 1 4 2 6h0c0 2 0 3 1 4l1 1 2 6h1c1 3 2 9 2 13l7 26c1 3 2 8 3 11l2 6 2 6v3 4l1 1v4l-1 1 1 1v1 3h0c0 1-1 2-1 3v-1l-1-2c0-1 0-1-1-2v-1-2l-1-1v-1-2c-1-1-1-2-1-3v-1l-2-5v-1-2l-1-1v-2l-1-2v-1-2c-1-1-1-2-1-3v-1l-1-2c0-2 0-3-1-4 0-1 0-1-1-2v-2-1s-1-1-1-2 0-2-1-2v-2-2s-1-1-1-2v-1c0-1-1-2-1-2v-2s-1-1-1-2l-2-7-1-2v-1-2c-1-1-1-2-1-2v-1c0-1-1-2-1-2l-3-9z" class="Q"></path><g class="C"><path d="M418 315c0-3-2-6-1-8h0l1 3 1-2 3 9s1 1 1 2v1s0 1 1 2v2 1l1 2 2 7c0 1 1 2 1 2v2s1 1 1 2v1c0 1 1 2 1 2v2 2c1 0 1 1 1 2s1 2 1 2v1 2c1 1 1 1 1 2 0 5 2 10 3 15l2 9 4 14 2 13c1 4 2 8 3 11l6 32c0 1 1 2 1 4 0 1 1 3 1 4l-1 1h0c-1-1-1-2-1-3-1-2-2-5-2-7h0c-1-2-1-4-1-6-1-3-2-7-3-10l1-1s0-1-1-1v-1l-1-1h1v-2h-1c0-1-1-2-1-2-1-1-1-3-1-4-1-3-2-6-2-9v-1c-2-5-3-12-4-18 0-3-1-6-2-9s-1-6-2-9l-1-1v-3c-1-2-2-6-3-9-1-5-3-10-4-15 0-2 1-5 0-7-1-1-1-2 0-3-1 0-2-1-2-2-1-1-1-2-2-3l1-1c-1-2-3-4-3-6v-1c-1-1-1-1-1-2s0-2-1-4v-3z"></path><path d="M418 315c0-3-2-6-1-8h0l1 3 4 16c1 3 3 7 4 11-1 0-2-1-2-2-1-1-1-2-2-3l1-1c-1-2-3-4-3-6v-1c-1-1-1-1-1-2s0-2-1-4v-3z"></path></g><path d="M582 768c6-4 14-6 20-5 7 2 13 6 16 12 3 5 4 10 4 15 0 8-4 15-10 21l-5 4c-1 0-2 1-4 1h0 0c-1 0-2 1-3 1s-1 1-2 1h-2l-5 1c-1 0-4 0-5 1-1 0-7-1-8-1h-3c-2-1-5-2-7-3-12-6-20-18-24-31v-3c-1-6 2-12 3-18v8h1c0 7 2 14 5 21 4 8 10 16 19 19 7 2 16 2 23-1 4-2 8-6 10-11 1-5 0-8-2-12l-1 3c-1 2-2 4-3 5-3 2-6 3-9 3-5 0-9-2-12-5-2-4-4-9-3-14 0-5 2-9 7-13v1z" class="L"></path><path d="M619 793c-1-1-1-2-2-3v-5c0-1 0-2-1-3l-1-3c1-2 1-2 3-2l2 6c-1 3-1 7-1 10z" class="E"></path><path d="M620 783c0 2 1 4 1 7h1c0 8-4 15-10 21l-5 4c-1 0-2 1-4 1h0 0l2-1c9-5 12-12 14-22 0-3 0-7 1-10z" class="C"></path><path d="M582 768c6-4 14-6 20-5 7 2 13 6 16 12 3 5 4 10 4 15h-1c0-3-1-5-1-7l-2-6c-4-5-6-9-12-12-5-2-12-1-17 1-4 2-8 6-10 10-1 3-2 10 0 13s4 5 8 6h0v1l-1-1c-2 0-4-2-6-3-3-3-4-8-4-12 0-6 3-9 6-12z" class="I"></path><path d="M595 773c4-1 7 0 10 1 4 1 8 5 9 9 2 6 2 11 0 16-3 7-9 10-16 13 3-4 6-6 8-11 0-1 1-3 1-4 0-4-2-9-5-12-1-1-2-2-4-2l-1 1c1 2 0 4 0 6-1 2-2 3-4 4s-5 0-7-1-4-3-4-6c-1-2 0-5 1-7 3-4 7-6 12-7z" class="J"></path><path d="M229 151l2-1h0l-3-2c-5-4-10-12-11-18 0-5 0-10 3-14l-2 6c-1 5 1 11 3 16 3 4 9 11 15 12l1 1h0c1 0 2 1 3 1h1c1 1 2 1 4 1h1 3v1c1 1 2 1 3 1s2 0 3 1h2c1 1 3 2 4 2l-1 1c-1 0-1-1-2-1s-1 0-2-1h-1l-3-1h-2l-2-1h-10c-15 3-24 11-32 23-9 13-11 30-7 45 3 15 11 25 23 34 1 0 1 0 2 1 1 0 1 0 1 1 2 0 3 1 5 2h1c1 0 2 1 3 0h0l6 1c9 1 20 0 29-4 3-2 5-4 8-5h0l3-2v1l-1 1h2 0c-2 2-4 5-7 7h0l-3 1-4 3-2 1c-1 0-2 1-3 1h-1c-1 1-1 1-2 1-2 1-4 0-5 0l-1 1c-4 0-9 0-14-1h-4c-1 0-3-1-4-1h-1l-4-2c-2 0-4-1-5-1l-5-3c-2-1-3-1-3-2l-6-3-2-2-1-1c-1-1-3-3-5-4-8-9-12-18-15-29-4-18 1-38 13-53 1-2 3-3 4-5 1-1 3-2 4-3v-1c4-4 11-6 17-7 2 0 4 0 7 1z" class="F"></path><path d="M192 197v8c1 5 2 11 0 16l-1-4c-1-6-1-13 1-20z" class="D"></path><path d="M191 217l1 4c2 7 4 14 7 21h0 0c-1-1-2-3-3-4-3-6-6-13-6-20l1-1z" class="R"></path><path d="M190 218l-1-1c-2-13 2-32 9-43h1c-3 8-6 15-7 23-2 7-2 14-1 20l-1 1z" class="N"></path><path d="M205 157c4-4 11-6 17-7 2 0 4 0 7 1-4 0-9 1-12 3-9 3-17 10-22 18-9 14-12 33-9 49 2 9 6 18 12 24 3 3 6 5 7 8l-1-1c-1-1-3-3-5-4-8-9-12-18-15-29-4-18 1-38 13-53 1-2 3-3 4-5 1-1 3-2 4-3v-1z" class="C"></path><defs><linearGradient id="N" x1="220.293" y1="190.861" x2="198.719" y2="185.709" xlink:href="#B"><stop offset="0" stop-color="#b5aead"></stop><stop offset="1" stop-color="#d9d5d0"></stop></linearGradient></defs><path fill="url(#N)" d="M222 257c-4-1-9-6-12-9-10-11-16-27-15-41 0-16 6-31 18-42 4-4 13-11 19-10 2 0 4-1 6 0-15 3-24 11-32 23-9 13-11 30-7 45 3 15 11 25 23 34z"></path><path d="M474 420c3-5 5-11 8-16 5-10 11-20 15-31 8-20 13-41 17-62l8 28c4 16 10 32 17 47 0 1 1 1 1 2h1c0-2-1-5-1-8l6 12 11 21c2 5 6 10 7 16 1-1 1-2 1-3v-1c1 0 2-1 3-2l1 1h1l1-1h1c-1 1 0 1-1 2-1 2-1 4-2 6l1 1-8 26-2 7v-2c0-1 0-1-1-2-1-3 1-5 1-8-1-2 0-6-2-8v4h0l-1-5c0-3 0-5-1-8s-2-7-3-10c-2-2-2-3-2-6 0-2-2-5-3-7l-11-18c-4-6-7-12-11-18-1 0-1-1-2-1 0-1-1-2-2-3-2-2-3-4-4-6v-1c-1-4-1-8-2-13h0c0-2-1-6-1-8h-1c-1 3-1 7-1 11s0 8-1 12l-8 10c-10 15-20 31-28 47 0 1-2 6-2 6-5 11-5 22-6 34-2-7-4-13-5-20-2-5-4-10-4-15 0-1-1-3-1-3l-4-11c0-1 0-2 1-3 1 4 4 11 7 14 0-1 0-2-1-3 2 0 2 1 3 3l1 1h0l1 1v3 3h1l7-15z" class="H"></path><path d="M560 453c0-2 0-4 1-5 0 3 0 8 1 10l-2 7v-2c0-1 0-1-1-2-1-3 1-5 1-8z" class="R"></path><path d="M540 380l6 12c0 3 1 7 1 10-3-5-6-10-8-16 0 1 1 1 1 2h1c0-2-1-5-1-8z" class="C"></path><path d="M551 420c3 7 11 21 10 28-1 1-1 3-1 5-1-2 0-6-2-8v4h0l-1-5c0-3 0-5-1-8s-2-7-3-10c-2-2-2-3-2-6z" class="U"></path><path d="M514 345h-1c0-3 1-5 2-7l1 1 2 15c0 3 0 10 2 12 2 3 5 8 6 11-1 0-1-1-2-1 0-1-1-2-2-3-2-2-3-4-4-6v-1c-1-4-1-8-2-13h0c0-2-1-6-1-8h-1z" class="S"></path><defs><linearGradient id="O" x1="456.613" y1="430.593" x2="465.264" y2="430.942" xlink:href="#B"><stop offset="0" stop-color="#a6a5a3"></stop><stop offset="1" stop-color="#c7bfbe"></stop></linearGradient></defs><path fill="url(#O)" d="M455 413c1 4 4 11 7 14 0-1 0-2-1-3 2 0 2 1 3 3l1 1h0l1 1v3 3h1c0 3-2 7-3 9-1-4-2-9-4-14h-1c0-1-1-3-1-3l-4-11c0-1 0-2 1-3z"></path><path d="M504 378c0-3 3-6 4-10 1-2 1-5 2-8 0-5 0-12 2-17 1-2 1-4 1-6l2-9 1 11-1-1c-1 2-2 4-2 7h1c-1 3-1 7-1 11s0 8-1 12l-8 10z" class="D"></path><path d="M546 392l11 21c2 5 6 10 7 16 1-1 1-2 1-3v-1c1 0 2-1 3-2l1 1h1l1-1h1c-1 1 0 1-1 2-1 2-1 4-2 6l-3 7c0 1 0 2-1 2h0c-4-13-13-25-18-38 0-3-1-7-1-10z" class="Q"></path><path d="M570 424l1-1h1c-1 1 0 1-1 2-1 2-1 4-2 6l-3 7v-1c0-2-1-4 0-6 0-2 3-5 4-7z" class="C"></path><path d="M805 151c2-1 2-1 5 0 2 0 3 1 5 1l1 1c1 0 2 1 3 2h0c3 3 6 4 9 7 10 9 17 25 18 38 2 18-3 34-15 47-12 14-28 20-46 21-14 1-22-3-32-11l-1-1v-1h1 1l1-1c2 1 3 2 5 3 10 6 24 6 35 4 13-4 23-15 30-27 3-8 6-16 5-25 0-16-6-28-18-39-10-7-18-8-29-8-12 1-22 6-30 16l-6 8v-5-1c0-1 1-3 2-4 6-10 18-18 29-21h5c6-1 12-1 18 0h1c-1-1-2-1-4-1v-1l-3-1c2-1 5-1 7-1h3z" class="F"></path><path d="M838 225c0 4-3 10-7 13l6-17c0 1 1 3 1 4z" class="U"></path><path d="M838 204v1c1 1 1 2 1 3 0 3 0 4 2 6-1 3-2 7-3 11 0-1-1-3-1-4 1-6 1-11 1-17z" class="R"></path><defs><linearGradient id="P" x1="834.253" y1="197.942" x2="841.656" y2="199.124" xlink:href="#B"><stop offset="0" stop-color="#6b6868"></stop><stop offset="1" stop-color="#878384"></stop></linearGradient></defs><path fill="url(#P)" d="M838 204c-1-12-4-23-11-34-1 0-1-1-1-2 3 3 5 7 7 10 4 9 8 17 8 27v9c-2-2-2-3-2-6 0-1 0-2-1-3v-1z"></path><defs><linearGradient id="Q" x1="806.88" y1="189.294" x2="837.783" y2="196.626" xlink:href="#B"><stop offset="0" stop-color="#beb6b1"></stop><stop offset="1" stop-color="#d7d7d7"></stop></linearGradient></defs><path fill="url(#Q)" d="M747 180c0-1 1-3 2-4 6-10 18-18 29-21h5c6-1 12-1 18 0 13 6 23 15 28 28 7 15 7 32 2 47-4 9-11 19-19 26-2 1-5 3-6 3h-1c0-1 3-2 5-3 7-5 13-13 16-21 2-4 4-8 5-12 3-16 1-32-8-46-6-10-16-19-27-22-4 0-10-1-14 0h-3l-1 1h-2c-13 5-23 14-29 25v-1z"></path><path d="M805 151c2-1 2-1 5 0 2 0 3 1 5 1l1 1c1 0 2 1 3 2h0c3 3 6 4 9 7 10 9 17 25 18 38 2 18-3 34-15 47-12 14-28 20-46 21-14 1-22-3-32-11 1-1 3 1 4 2 4 3 9 5 14 6 16 4 34 0 48-8 13-9 21-22 24-37 4-16 0-35-9-49-7-10-17-17-29-20z" class="C"></path><path d="M747 181c6-11 16-20 29-25h2l1-1h3c4-1 10 0 14 0 11 3 21 12 27 22 9 14 11 30 8 46-1 4-3 8-5 12-1 0-1 0-1-1 3-8 6-16 5-25 0-16-6-28-18-39-10-7-18-8-29-8-12 1-22 6-30 16l-6 8v-5z" class="L"></path><path d="M783 162l4-1c1-1 3-1 4-1 6 1 11 2 16 5 1 1 4 3 5 4v1c-10-7-18-8-29-8z" class="F"></path><path d="M245 206l1-1c8-4 17-3 25-1 8 3 14 7 21 13l1-1 2 2c1 0 2 2 3 3s2 2 4 3c2 2 3 5 6 6 1 1 1 1 1 2l1 1 2 2s0 1 1 1c0 1 1 2 1 2 5 7 8 13 11 21 1 2 2 4 2 6l1 1c0 1 1 2 1 4 0 1 1 4 0 5l2 7c2 3 2 7 4 10 1 1 1 3 1 4h1v-1c1-1 2-3 2-4l2-3v1c0 1 0 2 1 3 0 3-1 6-2 10 0 6 0 12 1 18 1 4 3 7 5 10 0 3 4 6 3 9v4h0c-4-3-7-3-11-4-3 0-6 0-8 2-1 1-1 1-1 2-1 0-1 1-1 1l-1-1h-3c-4 4-11 8-16 11l-1 1c0 1-1 1-2 2v1c1 0 2 0 4-1l1 2 7 3h1v1l-1 2c1 1 1 2 1 2 2 1 5 0 7 1h4l-6 2c-7 1-14 4-21 6l-5 4c-7 5-14 11-18 19l-4 10c-6 17-5 32 3 48 10 20 27 35 48 41 13 5 28 7 42 8 1 0 4-1 5 0h2 9c4 1 10 1 14 1l1-1v1h1v2h1l-1 1v-1c-1 0-1 0-1-1-2 1-3 2-4 4 0 1 1 2 2 3h1l-2 1-6-1-1-2h-1c-3 1-6 0-10 0v2h-6-3c-2-1-2-1-3-1h-1c2 2 4 2 6 2-1 0 0 0-1 1-3-1-22-5-24-5-1 1-2 1-4 1l-1-1c-20-5-37-13-51-28-4-3-7-7-10-11v-1c0-2-2-4-3-5v-1h0 0 1c-11-18-18-36-17-58 2-17 12-34 23-47l9-9 5-5 5-6 2-3c1 0 2 1 3 1 1-1 2-3 3-4l2-4 2-3-1-2 6-16v-1c0-2 1-4 1-6 1-4 2-7 2-11v-1c0-3-1-7-1-11-3-14-8-26-17-38-6-9-15-17-24-22-8-5-18-10-27-7-5 0-9 3-12 7-2 2-2 5-1 8v2h0c-3-3-4-6-4-10 0-6 3-10 8-14z" class="H"></path><path d="M305 338c1 0 2 1 3 1l-3 3-2-1 2-3z" class="I"></path><path d="M303 341l2 1-5 6 1-2h0l-3 1 5-6z" class="O"></path><path d="M298 347l3-1h0l-1 2c-1 1-3 4-4 4h0-3l5-5z" class="K"></path><path d="M302 369h3 0c-6 4-13 8-18 13l-2 1c5-7 10-10 17-14z" class="R"></path><path d="M320 310l3-9v-1c0-1 1-1 1-2v-2h1c-1 3-1 6-2 9 0 2 0 3-1 4v1c-1 6-4 13-7 18l-1-2 6-16z" class="C"></path><path d="M287 382l1 1c3-2 5-4 9-3-7 5-14 11-18 19l-4 10c-1 0-1-1-1-1 1-3 2-6 2-8 1-2 1-4 2-6 1-4 4-8 7-11l2-1z" class="E"></path><path d="M309 367c1 1 1 1 2 1l6-3c1 1 1 2 1 2 2 1 5 0 7 1h4l-6 2c-7 1-14 4-21 6l-5 4c-4-1-6 1-9 3l-1-1c5-5 12-9 18-13h1c1-1 2-1 3-2z" class="D"></path><defs><linearGradient id="R" x1="307.184" y1="369.089" x2="321.093" y2="372.359" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#535052"></stop></linearGradient></defs><path fill="url(#R)" d="M325 368h4l-6 2c-7 1-14 4-21 6 1-1 2-2 4-2l-1-1v-1c3-1 5-1 8-2 4-2 7-2 12-2z"></path><path d="M292 217l1-1 2 2c1 0 2 2 3 3s2 2 4 3c2 2 3 5 6 6 1 1 1 1 1 2l1 1 2 2s0 1 1 1c0 1 1 2 1 2 5 7 8 13 11 21 1 2 2 4 2 6l1 1c0 1 1 2 1 4 0 1 1 4 0 5v4c1 3 2 8 1 11h0v-1c-2-28-17-54-38-72z" class="I"></path><path d="M305 242h1c-7-9-14-15-23-22h1c18 10 33 32 38 52 3 7 4 17 3 24h0-1v2c0 1-1 1-1 2v1l-3 9v-1c0-2 1-4 1-6 1-4 2-7 2-11v-1c0-3-1-7-1-11-3-14-8-26-17-38z" class="R"></path><path d="M306 354h2l-1 1c0 1-1 1-2 2v1c1 0 2 0 4-1l1 2 7 3h1v1l-1 2-6 3c-1 0-1 0-2-1-1 1-2 1-3 2h-1 0-3c-3 0-9 6-12 5v-3-2c1-3 5-7 8-8 3-2 6-4 8-7z" class="F"></path><path d="M295 368c1-1 2-2 4-3s6-1 8 0l-12 3z" class="D"></path><path d="M306 354h2l-1 1c0 1-1 1-2 2v1c1 0 2 0 4-1l1 2c-4 0-8 2-11 3l-1-1c3-2 6-4 8-7z" class="G"></path><path d="M307 365l7-1-5 3c-1 1-2 1-3 2h-1 0-3c-3 0-9 6-12 5v-3l5-3 12-3z" class="T"></path><path d="M329 275l2 7c2 3 2 7 4 10 1 1 1 3 1 4h1v-1l1 1v2c-2 10-4 20-8 29-2 5-4 10-8 14-1 2-3 4-5 6-3 2-8 4-9 7h-2l3-3 2-1 3-3h1c2-1 3-3 5-5h-2l1-1v-1l2-3h0c1-2 2-3 2-4v-1c0-1 1-1 1-2v-2c1-1 1-1 1-2l-2 3c-1 4-4 7-7 10v-1l1-1c1-2 0-1 1-2 2-1 3-3 4-5 1-3 3-6 3-9l1-1v-2-2c-1 2-2 4-3 7l-6 10c-1 2-2 5-4 6-1 1-1 1-2 1l6-9c2-4 5-7 6-10l4-11v-1c1-1 1-1 1-2v-1c0-1 0-1 1-2v-2-1l1-11h0c1-3 0-8-1-11v-4z" class="D"></path><path d="M331 307c0 6 0 10-1 16-1 1 0 3 0 4-2 5-4 10-8 14h0c-1-4 1-6 3-9 0-1 1-3 2-4v-3c0-3 1-5 2-7 1-4 1-8 2-11z" class="T"></path><path d="M329 275l2 7c2 3 2 7 4 10 1 1 1 3 1 4h1v-1l1 1v2c-2 10-4 20-8 29 0-1-1-3 0-4 1-6 1-10 1-16 0-4 1-8 0-12-1 2 0 4-1 5l-1 1 1-11h0c1-3 0-8-1-11v-4z" class="R"></path><defs><linearGradient id="S" x1="336.357" y1="306.019" x2="331.112" y2="300.992" xlink:href="#B"><stop offset="0" stop-color="#7b7b7d"></stop><stop offset="1" stop-color="#988d8e"></stop></linearGradient></defs><path fill="url(#S)" d="M331 282c2 3 2 7 4 10 1 1 1 3 1 4h1v-1l1 1v2l-1-1c-1 2-1 4-1 6l-1 5-1 1c0 1 0 2-1 4l-2-31z"></path><path d="M341 288v1c0 1 0 2 1 3 0 3-1 6-2 10 0 6 0 12 1 18 1 4 3 7 5 10 0 3 4 6 3 9v4h0c-4-3-7-3-11-4-3 0-6 0-8 2-1 1-1 1-1 2-1 0-1 1-1 1l-1-1h-3c-4 4-11 8-16 11 1-3 6-5 9-7 2-2 4-4 5-6 4-4 6-9 8-14 4-9 6-19 8-29v-2l-1-1c1-1 2-3 2-4l2-3z" class="H"></path><path d="M334 336l4-9c0 2 1 5 0 6-1 2-3 3-5 4l1-1z" class="C"></path><path d="M335 327c0 3-2 6-2 8l1 1-1 1v1c1 1 3 1 5 1-3 0-6 0-8 2-1 1-1 1-1 2-1 0-1 1-1 1l-1-1h-3l2-2h1c2-2 5-5 6-8 0-2 1-3 2-5v-1zm6-7c1 4 3 7 5 10 0 3 4 6 3 9l-2-2c-3-2-5-5-6-9 0-3-2-5 0-8z" class="D"></path><path d="M336 318c1 2 1 3 0 5l-1 4v1c-1 2-2 3-2 5-1 3-4 6-6 8h-1c4-7 7-15 10-23z" class="E"></path><path d="M338 296c1-1 2-1 2-3h1c0 2 0 3-1 5-2 7-2 14-4 20-3 8-6 16-10 23l-2 2c-4 4-11 8-16 11 1-3 6-5 9-7 2-2 4-4 5-6 4-4 6-9 8-14 4-9 6-19 8-29v-2z" class="B"></path><path d="M293 352h3 0c-4 5-9 10-14 15-11 14-21 33-19 51 2 26 14 49 34 66s45 22 71 22h0c1 0 4-1 5 0h2 9c4 1 10 1 14 1l1-1v1h1v2h1l-1 1v-1c-1 0-1 0-1-1-2 1-3 2-4 4 0 1 1 2 2 3h1l-2 1-6-1-1-2h-1c-3 1-6 0-10 0v2h-6-3c-2-1-2-1-3-1h-1c2 2 4 2 6 2-1 0 0 0-1 1-3-1-22-5-24-5-1 1-2 1-4 1l-1-1c-20-5-37-13-51-28-4-3-7-7-10-11v-1c0-2-2-4-3-5v-1h0 0 1c-11-18-18-36-17-58 2-17 12-34 23-47l9-9z" class="J"></path><path d="M366 511l21 1 2 1h-1c-3 1-6 0-10 0v2h-6c1-1 0-1 1-2h0c-1-2-4 0-6-1l-1-1z" class="G"></path><path d="M368 506c1 0 4-1 5 0h2 9c4 1 10 1 14 1l1-1v1h1v2h1l-1 1v-1c-1 0-1 0-1-1-2 1-3 2-4 4 0 1 1 2 2 3h1l-2 1-6-1-1-2-2-1c4-1 7-2 10-4-6 0-14 0-20-1-2 0-5 1-7 0-1 0-1 0-2-1h0z" class="O"></path><path d="M280 473v-1c0-2-2-4-3-5v-1h0 0 1c7 8 13 16 22 23 14 12 34 18 52 21l14 1 1 1c2 1 5-1 6 1h0c-1 1 0 1-1 2h-3c-2-1-2-1-3-1h-1c2 2 4 2 6 2-1 0 0 0-1 1-3-1-22-5-24-5-1 1-2 1-4 1l-1-1c-20-5-37-13-51-28-4-3-7-7-10-11z" class="F"></path><path d="M755 206c7-3 14-5 22-4l1 1c5 0 9 3 12 7s4 10 3 14-4 8-7 11l-1-1c1-1 2-2 3-4s1-6 0-8c-2-5-6-7-10-9-7-2-17 0-23 3-13 6-24 16-32 27-3 4-6 9-7 13-6 15-11 32-5 48 2 10 7 19 12 28 6 8 13 15 20 22 16 19 30 39 28 65-1 16-6 31-15 44-9 16-22 30-39 39-10 5-20 8-31 11-1 0-2-1-3-1s-4 0-5 1l-11 2c-2 1-4 1-5 1h-1 0-2-2s-1 0-1 1l-19 4c-7 2-20 5-25 11-2 2-2 2-2 5-1 0-2-2-3-3l3-8v1c1-1 3-2 4-3 4-1 8-3 12-4h0c1 0 1-1 2-2l-2-1c3 0 5-1 7-1h2c1-1 3-1 4-1l1-2 3-1c-2 0-5 0-6-1s-1-1-1-3h5c3 0 7 0 10-1 2 0 4 1 6 0 1 0 2 0 3-1h0c14-1 28-3 42-7 21-6 39-21 49-40 9-16 11-31 5-48-1-2-2-3-2-5-1-2-2-5-4-7-2-1-3-4-3-5v-1c-5-10-16-16-26-20l-2-3c2-1 3-1 4-2-1 0-3-1-4-2 2-2 4-3 7-4-2-2-2-2-4-3v-1c-3-3-6-5-10-7-11-4-21-5-33 0-2-1-5 0-8 1l-3 2h-1c-1 1-2 1-3 1 5-6 8-12 12-20 3-6 6-13 8-21 2-4 4-14 2-18l-2 1s-1 0-1-1c-1-1-1-3-1-4 1-2 2-5 4-7 1 1 1 1 2 3 1 1 3 4 3 5l2 3c1 1 2 3 4 4h0c0-2 0-4 1-5 1-3 1-6 2-8 1-1 1-1 1-2s0-1 1-2c0-7 2-14 4-20 1-1 1-3 2-4l4-9c6-12 15-23 25-32l2-2 6-4c3-3 7-4 10-6z" class="H"></path><defs><linearGradient id="T" x1="704.06" y1="305.591" x2="695.94" y2="300.909" xlink:href="#B"><stop offset="0" stop-color="#393638"></stop><stop offset="1" stop-color="#5b5759"></stop></linearGradient></defs><path fill="url(#T)" d="M702 283c0 10 1 19 4 28 1 3 3 7 3 10h0c-1-2-2-4-2-5l-1-1v-1l-2-6-1-1c-1 3-3 5-3 8l2 7-2-3h-1c-1-3-2-7-3-11l-1 1c-2-3-2-6-3-9l-1-2v-5l2 3c1 1 2 3 4 4h0c0-2 0-4 1-5 1-3 1-6 2-8 1-1 1-1 1-2s0-1 1-2z"></path><path d="M691 293l2 3c1 1 2 3 4 4h0c0 3 0 6 1 9 0 3 1 7 2 10h-1c-1-3-2-7-3-11l-1 1c-2-3-2-6-3-9l-1-2v-5z" class="N"></path><path d="M691 293l2 3c0 3 1 6 2 9 0 1 1 3 1 3l-1 1c-2-3-2-6-3-9l-1-2v-5z" class="T"></path><defs><linearGradient id="U" x1="707.185" y1="336.196" x2="692.563" y2="319.586" xlink:href="#B"><stop offset="0" stop-color="#898686"></stop><stop offset="1" stop-color="#a7a2a2"></stop></linearGradient></defs><path fill="url(#U)" d="M692 300c1 3 1 6 3 9l1-1c1 4 2 8 3 11h1l2 3c3 5 4 10 7 15h-1l-1-1h-1c2 4 3 6 6 9l2 2c6 4 11 8 16 13-3-1-7-6-10-8-5-3-11-5-15-9-1-1-2-4-3-5-6-11-11-25-10-38z"></path><path d="M695 309l1-1c1 4 2 8 3 11h1l2 3c3 5 4 10 7 15h-1l-1-1h-1c2 4 3 6 6 9l2 2h-1c-8-4-15-29-18-38zm31 53c1 0 1 0 2 1 1 0 2 0 4 1 4 3 7 6 10 12l-1 1c5 7 17 25 15 34-1-2-2-3-2-5-1-2-2-5-4-7-2-1-3-4-3-5v-1c-5-10-16-16-26-20l-2-3c2-1 3-1 4-2-1 0-3-1-4-2 2-2 4-3 7-4z" class="P"></path><path d="M728 363c1 0 2 0 4 1 4 3 7 6 10 12l-1 1c-1-1-2-2-2-4l-2-2c-3-3-7-5-9-8z" class="E"></path><path d="M723 368c4 1 9 4 12 7l3 2 3 3h0c1 3 4 4 4 6l-1 1c1 1 2 3 3 4l6 8c0 1 0 2 1 3 0 2 1 2 0 4-1-2-2-5-4-7-2-1-3-4-3-5v-1c-5-10-16-16-26-20l-2-3c2-1 3-1 4-2z" class="D"></path><path d="M738 377l3 3h0c1 3 4 4 4 6l-1 1h0c-2-3-4-5-7-7 0 0-1-1-1-2 1 0 1 0 2-1h0z" class="E"></path><path d="M664 355c5-6 8-12 12-20 3-6 6-13 8-21 2-4 4-14 2-18l-2 1s-1 0-1-1c-1-1-1-3-1-4 1-2 2-5 4-7 1 1 1 1 2 3 1 1 3 4 3 5v5l1 2c-1 13 4 27 10 38 1 1 2 4 3 5 4 4 10 6 15 9 3 2 7 7 10 8 2 1 3 2 4 3v1l-2-1v1c-2-1-3-1-4-1-1-1-1-1-2-1-2-2-2-2-4-3v-1c-3-3-6-5-10-7-11-4-21-5-33 0-2-1-5 0-8 1l-3 2h-1c-1 1-2 1-3 1z" class="F"></path><path d="M690 313v4c1 2 0 4 0 6-1 2-2 5-4 6h0c-1 2-2 2-3 3l4-9c2-3 2-6 3-10z" class="D"></path><path d="M683 325l1 1c1-1 2-2 3-2v-1l-4 9c-3 2-5 10-8 11 0-3 2-5 3-7l5-11z" class="C"></path><path d="M698 338h0c1 2 2 3 4 4-9 1-16 2-25 4 2-2 5-4 7-5 2 0 4-1 7-1 2-1 5 0 7-2z" class="O"></path><path d="M684 341c-1-3 5-7 6-11l2-2c1 0 1 0 1 1 1 1 0 1 1 2l1 2 1 2c0 1 1 2 2 3-2 2-5 1-7 2-3 0-5 1-7 1z" class="D"></path><path d="M683 325l1-5c3-7 7-21 3-28l-1-1 1-1h0l3 6c1 2 0 5 0 7v10c-1 4-1 7-3 10v1c-1 0-2 1-3 2l-1-1z" class="B"></path><defs><linearGradient id="V" x1="704.197" y1="523.956" x2="653.438" y2="479.725" xlink:href="#B"><stop offset="0" stop-color="#050504"></stop><stop offset="1" stop-color="#2d2a2c"></stop></linearGradient></defs><path fill="url(#V)" d="M706 292v-4c0-22 11-43 26-57 2-3 11-12 14-11l-1 1c-8 5-15 12-20 19-3 3-6 7-8 10 0 1 0 3-1 4v2c-6 15-11 32-5 48 2 10 7 19 12 28 6 8 13 15 20 22 16 19 30 39 28 65-1 16-6 31-15 44-9 16-22 30-39 39-10 5-20 8-31 11-1 0-2-1-3-1s-4 0-5 1l-11 2c-2 1-4 1-5 1h-1 0-2-2s-1 0-1 1l-19 4c-7 2-20 5-25 11-2 2-2 2-2 5-1 0-2-2-3-3l3-8v1c1-1 3-2 4-3 4-1 8-3 12-4h0c1 0 1-1 2-2l-2-1c3 0 5-1 7-1h2c1-1 3-1 4-1l1-2 3-1c-2 0-5 0-6-1s-1-1-1-3h5c3 0 7 0 10-1 2 0 4 1 6 0 1 0 2 0 3-1 12 0 25-1 37-4 5-2 11-3 16-5 17-8 33-22 42-39 4-6 7-13 9-20 4-10 5-23 3-33-2-8-5-16-9-24l-5-8c-7-10-16-18-24-27-8-10-14-21-18-34-3-6-4-13-5-20z"></path><path d="M755 372l5 9-1-1-1-1c0 1 0 1 1 2h-1l-5-8 2-1z" class="B"></path><path d="M633 516h2c1-1 3-1 4-1-2 2-4 3-7 5h-1c-1 0-2 0-3 1-5 1-10 2-14 4-1 1-3 2-4 2 1-1 3-2 4-3 4-1 8-3 12-4h0c1 0 1-1 2-2l-2-1c3 0 5-1 7-1z" class="O"></path><path d="M758 381h1c-1-1-1-1-1-2l1 1 1 1c2 3 4 6 5 10 4 10 6 20 4 31-1 11-4 21-9 31-1 2-2 4-4 6h0l-1-1c4-6 7-13 9-20 4-10 5-23 3-33-2-8-5-16-9-24z" class="C"></path><path d="M706 292l1-4c0 2 0 6 1 9 1 4 2 8 3 13 2 4 4 9 6 14 9 18 26 31 38 48l-2 1c-7-10-16-18-24-27-8-10-14-21-18-34-3-6-4-13-5-20z" class="I"></path><path d="M755 458l1 1c-9 16-22 30-38 39-7 3-15 6-22 8-14 4-28 5-41 5-4 1-8 1-12 1-2 0-5 0-6-1s-1-1-1-3h5c3 0 7 0 10-1 2 0 4 1 6 0 1 0 2 0 3-1 12 0 25-1 37-4 5-2 11-3 16-5 17-8 33-22 42-39z" class="J"></path><path d="M514 345h1c0 2 1 6 1 8h0c1 5 1 9 2 13v1c1 2 2 4 4 6 1 1 2 2 2 3 1 0 1 1 2 1 4 6 7 12 11 18l11 18c1 2 3 5 3 7 0 3 0 4 2 6 1 3 2 7 3 10s1 5 1 8l1 5h0v-4c2 2 1 6 2 8 0 3-2 5-1 8 0 4-2 7-3 11l-1 5v5c-3 14-7 28-12 41l-16 27c-1 2-1 3-1 5 0 0 1 2 2 2-1 1-2 1-2 2s-1 7-2 8c-2 0-3 0-5 1h0l-9-1c-2-2-2-6-2-9h0-2l-3-2c1 0 1-2 1-2 0-3-6-9-7-12-12-19-18-40-22-62v-2c-2-8-2-16-2-24 0-3 1-6 1-8v-1h-1c0-2 0-5 1-7v-2-5s2-5 2-6c8-16 18-32 28-47l8-10c1-4 1-8 1-12s0-8 1-11z" class="H"></path><path d="M507 460c0-3 0-7 1-10l4-2v1c-3 2-4 8-5 11z" class="D"></path><path d="M480 483h2c0 1 0 2 1 4l1 5-2 2c-1-3-2-8-2-11z" class="Q"></path><path d="M496 480c0-2 0-3 1-4h1v-1l1-1 1-1h0l1-1 1 7c-2 0-3-1-4 0s-1 1-2 1z" class="P"></path><path d="M529 528c1 1 2 3 4 4-2 4-4 9-7 13 1-3 1-5 2-7 0-4 1-7 1-10z" class="Q"></path><path d="M527 435l-1-1c-3-7-2-18-2-25 1 1 2 3 3 4-2 5-2 12-1 17 0 2 1 4 1 5z" class="E"></path><defs><linearGradient id="W" x1="482.714" y1="495.694" x2="487.005" y2="500.554" xlink:href="#B"><stop offset="0" stop-color="#9f9d9b"></stop><stop offset="1" stop-color="#bdb8b8"></stop></linearGradient></defs><path fill="url(#W)" d="M482 494l2-2c2 3 3 8 5 10l1 3h-3c0 1 1 2 0 3-2-4-4-10-5-14z"></path><path d="M495 487c-1-3-1-9 0-12l1 1h-1v5l1-1c1 0 1 0 2-1s2 0 4 0c0 3 2 8 2 12-1-3-3-6-4-8l-1-1-1-1c-1 1-1 1-1 3h-1v2c0 1-1 1-1 1z" class="S"></path><path d="M544 435c1 1 3 1 4 3v1c1 3 3 7 3 10v1c1 3 1 7 1 10-1 1 0 3-1 5 0-4 0-7-1-10-1-8-4-13-6-20z" class="D"></path><path d="M553 436l1-1c1 3 2 6 2 9 1 4 1 8 2 12 0 1-1 2 0 3 1-3 0-7 0-10v-4c2 2 1 6 2 8 0 3-2 5-1 8 0 4-2 7-3 11v-14c0-8-1-15-3-22z" class="T"></path><path d="M527 435c0-1-1-3-1-5-1-5-1-12 1-17 1 1 2 3 2 4-1 2-1 5 0 7 0 1 0 1 1 1-1 1-1 2-1 4s1 3 1 5c-1 1-1 2-3 1z" class="P"></path><defs><linearGradient id="X" x1="547.81" y1="497.713" x2="539.69" y2="500.287" xlink:href="#B"><stop offset="0" stop-color="#837c7e"></stop><stop offset="1" stop-color="#a09d9b"></stop></linearGradient></defs><path fill="url(#X)" d="M549 481l1 2c-3 10-5 21-10 31-2-3-1-4-1-6 0 1-1 1-2 2h-1c4-6 7-12 10-19 1-3 1-6 2-9l1-1z"></path><path d="M549 455h1 0c1 3 1 6 1 10 1 6 0 12-1 18l-1-2-1 1c0-1 0-1-1-2v-3c1-3 0-6 0-9 1-4 2-7 2-10v-3z" class="P"></path><path d="M549 455h1 0c1 3 1 6 1 10 1 6 0 12-1 18l-1-2c1-8 1-15 0-23v-3z" class="R"></path><path d="M532 475c1 0 1 0 2 1v9 1c-1 4-3 8-7 11l-1 1v-1c0-1 1-3 1-3l2-9c1-3 1-5 1-7s1-2 2-3z" class="B"></path><path d="M516 436c2 0 3 1 4 1v7h-8c-2 0-3 0-4-1v-6l8-1z" class="J"></path><defs><linearGradient id="Y" x1="526.893" y1="502.501" x2="537.63" y2="504.893" xlink:href="#B"><stop offset="0" stop-color="#333033"></stop><stop offset="1" stop-color="#4c4a4b"></stop></linearGradient></defs><path fill="url(#Y)" d="M543 485l1 1c-1 2-1 4-2 6l-1 2 2-1c0 2-4 9-6 11v1c-1 0-1 1-2 1-1 3-4 6-7 7h-4v-3h0c10-4 16-15 19-25z"></path><path d="M495 487s1 0 1-1v-2h1c0-2 0-2 1-3l1 1 1 1c1 2 3 5 4 8 0 2 0 4 1 5l1 2c0 1 0 2-1 3-5-3-8-8-10-14z" class="U"></path><path d="M505 496v1l-1-1c-3-2-6-5-6-9 0-2 0-3 1-4h1c1 2 3 5 4 8 0 2 0 4 1 5z" class="B"></path><path d="M483 440l-2 12c0 2-1 3 0 5 1 1 1 2 0 3v9c-1 3-1 7 0 10l1 4h-2 0c-1-6-2-12-2-19 0-4-1-8 0-12s3-8 5-12z" class="R"></path><path d="M529 417c5 5 11 11 15 18 2 7 5 12 6 20h0-1c-1-4-2-8-3-11-3-7-9-16-16-19-1 0-1 0-1-1-1-2-1-5 0-7z" class="B"></path><defs><linearGradient id="Z" x1="539.24" y1="522.903" x2="531.463" y2="514.368" xlink:href="#B"><stop offset="0" stop-color="#9b9595"></stop><stop offset="1" stop-color="#bbb6b5"></stop></linearGradient></defs><path fill="url(#Z)" d="M536 510h1c1-1 2-1 2-2 0 2-1 3 1 6-2 6-5 12-7 18-2-1-3-3-4-4 1-3 0-6 1-8v-1c-1 1-2 1-3 1 2-3 6-7 9-10z"></path><path d="M483 440c3-10 12-17 19-24 1 2 0 5 0 7s-1 2-2 2h-1c-3 3-7 6-9 9-5 7-7 15-9 23-1-2 0-3 0-5l2-12z" class="B"></path><path d="M521 558c1 0 2 0 3 1l1 1 1-1c0 1-1 7-2 8-2 0-3 0-5 1h0l-9-1c-2-2-2-6-2-9 3 1 10 2 13 0z" class="J"></path><path d="M521 558c1 0 2 0 3 1l1 1 1-1c0 1-1 7-2 8-2 0-3 0-5 1 1-3 0-7 2-10z" class="D"></path><defs><linearGradient id="a" x1="500.75" y1="522.45" x2="498.31" y2="502.543" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#2c2a2b"></stop></linearGradient></defs><path fill="url(#a)" d="M506 498c1 1 1 2 2 3v6c-1 3 0 6 0 8l1 1c-1 1-1 2-1 4 0 1 0 1 1 1v1 1l-1 3c0-3 0-3-2-4h0-2l-1-1h-1 2v-1c-1-1 0-1-1-1-5-3-10-9-13-14l-1-3c2 0 4 2 5 4l1-1 2 1v-1l9 6c1 0 0 0 1-1v-8l-2-1c1-1 1-2 1-3z"></path><path d="M494 506l1-1 2 1v-1l9 6c1 0 0 0 1-1v2l-1 1 1 1v1c-5-1-9-6-13-9z" class="S"></path><defs><linearGradient id="b" x1="545.611" y1="441.061" x2="532.722" y2="452.733" xlink:href="#B"><stop offset="0" stop-color="#1a1817"></stop><stop offset="1" stop-color="#383637"></stop></linearGradient></defs><path fill="url(#b)" d="M530 425c7 3 13 12 16 19 1 3 2 7 3 11v3c0 3-1 6-2 10 0 3 1 6 0 9v3c1 1 1 1 1 2-1 3-1 6-2 9 0-1-1-2 0-3v-1h0c1-2 0-8 0-10 1-1 1-5 1-7-1 6-1 10-3 16l-1-1c3-11 4-22 1-34-2-9-7-18-15-22 0-2 0-3 1-4z"></path><path d="M487 508c1-1 0-2 0-3h3c3 5 8 11 13 14-1 0-3 0-4 1 1 8 2 15 4 24-7-12-13-23-16-36z" class="O"></path><defs><linearGradient id="c" x1="504.574" y1="429.94" x2="475.896" y2="466.872" xlink:href="#B"><stop offset="0" stop-color="#0a0908"></stop><stop offset="1" stop-color="#373537"></stop></linearGradient></defs><path fill="url(#c)" d="M481 457c2-8 4-16 9-23 2-3 6-6 9-9h1l2 2v1c-8 4-13 13-16 22-3 11-3 23 0 35h-1v2l-1 1-1-1h0c-1-2-1-3-1-4l-1-4c-1-3-1-7 0-10v-9c1-1 1-2 0-3z"></path><defs><linearGradient id="d" x1="486.716" y1="483.394" x2="478.284" y2="474.606" xlink:href="#B"><stop offset="0" stop-color="#474346"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#d)" d="M481 469c2 5 2 11 4 16v2l-1 1-1-1h0c-1-2-1-3-1-4l-1-4c-1-3-1-7 0-10z"></path><path d="M512 449l-1 36 1 25v31c0 5-1 10 0 15-1 0-1-1-2-1l1-27c-2-3 0-9-2-12 1-4 0-10 0-15 0-1 1-4 0-6-1-1 0-4-1-5-1-10-1-20-1-30 1-3 2-9 5-11z" class="E"></path><defs><linearGradient id="e" x1="551.976" y1="404.333" x2="527.976" y2="402.207" xlink:href="#B"><stop offset="0" stop-color="#474649"></stop><stop offset="1" stop-color="#6c6766"></stop></linearGradient></defs><path fill="url(#e)" d="M522 373c1 1 2 2 2 3 1 0 1 1 2 1 4 6 7 12 11 18l11 18c1 2 3 5 3 7 0 3 0 4 2 6 1 3 2 7 3 10s1 5 1 8l1 5h0c0 3 1 7 0 10-1-1 0-2 0-3-1-4-1-8-2-12 0-3-1-6-2-9l-1 1c-7-19-17-37-29-54l1-1c0-3-2-5-3-8z"></path><path d="M504 378l8-10v3 1c-3 7-8 12-12 18-11 17-21 35-26 55h-1c0-2 0-5 1-7v-2-5s2-5 2-6c8-16 18-32 28-47z" class="B"></path><path d="M514 345h1c0 2 1 6 1 8h0c1 5 1 9 2 13v1c1 2 2 4 4 6 1 3 3 5 3 8l-1 1-7-10h0c3 8 2 17 2 25v13l-1 23c-1 1 0 1-1 1-2 0-4 1-6 0-1-2-1-8-1-10 0-14 1-28 2-42 0-3 1-7 1-10v-1l-1 1v-1-3c1-4 1-8 1-12s0-8 1-11z" class="C"></path><defs><linearGradient id="f" x1="512.417" y1="353.352" x2="515.083" y2="369.148" xlink:href="#B"><stop offset="0" stop-color="#9a9393"></stop><stop offset="1" stop-color="#ccc8c7"></stop></linearGradient></defs><path fill="url(#f)" d="M514 345h1c0 2 1 6 1 8v7c1 3 0 5 1 9 1 1 1 1 0 2-2 0-2-2-4 0h0l-1 1v-1-3c1-4 1-8 1-12s0-8 1-11z"></path><path d="M517 434c-2 0-4 0-5-1 0-13-1-27 1-40v-6c1 0 1-1 2-1l1 1c3 7 1 15 2 22l1 1-1 23c-1 1 0 1-1 1z" class="K"></path><path d="M512 448c2 0 5 0 6 2 1 3 1 7 1 10v19l-1 76c-1 1-4 1-5 1h-1c-1-5 0-10 0-15v-31l-1-25 1-36v-1zm17-19c8 4 13 13 15 22 3 12 2 23-1 34-3 10-9 21-19 25h0l-1-9c2-1 4-2 5-3 8-8 9-22 4-32-1-2-4-5-5-8-1-1 0-3 0-4v-9c0-3 1-7 0-10 2 1 2 0 3-1 0-2-1-3-1-5z" class="J"></path><path d="M530 434c0 3 1 6 0 8l-3 3c0-3 1-7 0-10 2 1 2 0 3-1z" class="G"></path><path d="M502 428c0 6-7 11 0 16l1 1c0 4 1 7 1 11v2l-3 3c-3 4-6 9-6 14-1 3-1 9 0 12 2 6 5 11 10 14l2 1v8c-1 1 0 1-1 1l-9-6v1l-2-1-1 1c-1-2-3-4-5-4-2-2-3-7-5-10l-1-5h0l1 1 1-1v-2h1c-3-12-3-24 0-35 3-9 8-18 16-22z" class="J"></path><path d="M486 485c3 8 6 14 11 20v1l-2-1-1 1c-1-2-3-4-5-4-2-2-3-7-5-10l-1-5h0l1 1 1-1v-2h1z" class="P"></path><path d="M679 351c12-5 22-4 33 0 4 2 7 4 10 7v1c2 1 2 1 4 3-3 1-5 2-7 4 1 1 3 2 4 2-1 1-2 1-4 2l2 3c10 4 21 10 26 20v1c0 1 1 4 3 5 2 2 3 5 4 7 0 2 1 3 2 5 6 17 4 32-5 48-10 19-28 34-49 40-14 4-28 6-42 7h0c-1 1-2 1-3 1-2 1-4 0-6 0-3 1-7 1-10 1h-5c0 2 0 2 1 3s4 1 6 1l-3 1-1 2c-1 0-3 0-4 1h-2l-1-1-2-2c-2 1-5 0-7 1l-1-1h0-4c-1 1-3 0-4 0v-2c1-2 1-4 1-5 1-1 1-1 1-2v-1l1-2v-1-1c1-8 5-17 7-25l8-2 2-2c2-1 3-1 5-1v-1l1 1c1-1 2-1 3-1v-1c-1 0-2 0-3-1 1-1 3 0 5-1l4 1v-2c-2 0-6-1-8-1l-8-2h-1-2 0l5-3h1-1-3l-1 1h-2l4-19c1-2 1-5 3-7h0c0-4 2-9 3-13l2-7 4-12c1-4 2-7 4-10v-1l4-17c0-4 3-9 5-12l6-6c1 0 2 0 3-1h1l3-2c3-1 6-2 8-1z" class="J"></path><path d="M657 448c1-2 1-3 2-4l2 1h-2c1 2 2 2 2 4v1 1l2-2v1l-3 3c2 0 2 0 3-1 1 0 1-1 1-1 1-2 1-3 0-4 1 0 1 1 2 2 0 1 0 2-1 3-2 2-3 2-6 3l-5 1v-1l2-2c0-2 1-3 1-5z" class="L"></path><path d="M626 510c1 0 1 0 2-1h1v-2l26-1h5 0c-1 1-2 1-3 1-2 1-4 0-6 0-3 1-7 1-10 1h-5c0 2 0 2 1 3s4 1 6 1l-3 1-1 2c-1 0-3 0-4 1h-2l-1-1-2-2c0-1 1-3 1-4-1 1-3 1-5 1z" class="K"></path><path d="M631 509h1c1 1 2 2 2 3v1l-2 2-2-2c0-1 1-3 1-4z" class="M"></path><path d="M689 378c-2 3-2 4-2 8 1 0 1 1 2 2l1 3c-2-2-3-2-3-4v-2h0c-1-1-1-2-1-3v-1c-1 0-2 1-3 1-1 1-3 2-4 3-1 0-2 1-3 1s-1 1-2 1c-5 5-11 10-16 15-3 5-7 10-10 16-2 3-4 8-7 10 6-16 18-29 30-40 6-4 11-7 18-10z" class="B"></path><path d="M693 431c-1-2-2-4-5-6-1-1-4-1-6-1-2 1-3 2-4 3-1 3 0 6 0 8v1h-1c-2 0-2-1-3-2-1-2-2-5-1-7h1c0 2 0 4 1 5 0 1 1 2 2 2 0-1-1-3 0-4 0-3 2-6 4-7v-1h0l1-1h4l2 1h1c-2-2-4-2-7-3 2 0 4 0 6 1 5 2 6 6 7 10l-1 1v4c-1-1-1-2-1-4z" class="L"></path><path d="M636 458h-1-3l-1 1h-2l4-19c1-2 1-5 3-7h0c1 2 1 4 1 6h1c0 1-1 2-1 3h0c-1-2 0-4-1-5-1 1-2 2-2 4v4c0 1 0 6 1 7 0 0 1 1 2 1h2l1-1h1c1 0 6 1 8 1 2-1 5-2 7-4 0-1 1-1 1-1 0 2-1 3-1 5l-1-1c-2 0-4 1-6 2l-9 3c-1 1-3 1-4 1z" class="B"></path><path d="M694 377l1-1h2 1 0v1c-1 1-2 1-3 2v1 1c1-1 1 0 1-1l3-3v1 2l1 1c-1 2-2 3-2 5v1 1c1 1 1 2 2 3-2 1-4 2-6 1-2 0-3-1-4-1l-1-3c-1-1-1-2-2-2 0-4 0-5 2-8 2 0 4-1 5-1z" class="F"></path><path d="M694 377c0 4-2 9 0 13l1 1v1c-3-1-4-2-6-4-1-1-1-2-2-2 0-4 0-5 2-8 2 0 4-1 5-1z" class="C"></path><defs><linearGradient id="g" x1="675.477" y1="402.42" x2="631.373" y2="417.13" xlink:href="#B"><stop offset="0" stop-color="#050705"></stop><stop offset="1" stop-color="#353233"></stop></linearGradient></defs><path fill="url(#g)" d="M657 397l12-13c1 2 1 3 2 4-12 11-24 24-30 40l-4 11c0-2 0-4-1-6 0-4 2-9 3-13l2-7 4-12 3-1c-1 2-1 4-2 6-1 1-1 2-1 2l1 1c0 1 0 1-1 2v1h1 0l1-1 10-14z"></path><path d="M645 401l3-1c-1 2-1 4-2 6-1 1-1 2-1 2l1 1c0 1 0 1-1 2v1h1c-1 4-3 7-5 10h0l-2-2 2-7 4-12z" class="E"></path><path d="M700 381c1-1 3-4 5-5h5c13 1 26 8 34 18 3 3 6 7 7 11-2-2-3-5-5-6-2-3-6-5-9-7h-1l-1-1c-1-2-9-4-11-5v1c2 1 4 1 5 3l-1 1c-6-3-13-5-19-5-3 0-7 0-10 1h-1v-1c0-2 1-3 2-5z" class="B"></path><path d="M715 369l4 1 2 3c10 4 21 10 26 20v1c0 1 1 4 3 5l4 9h-1s-1-3-2-3c-1-4-4-8-7-11-8-10-21-17-34-18h-5c-2 1-4 4-5 5l-1-1v-2-1l-3 3c0 1 0 0-1 1v-1-1c1-1 2-1 3-2v-1h0-1-2l-1 1c-1 0-3 1-5 1-7 3-12 6-18 10-1-1-1-2-2-4l-12 13-1-1c-1-3 7-9 8-12l3-3c8-6 17-11 28-11 6-1 12 0 17 1l1-1 2-1z" class="L"></path><defs><linearGradient id="h" x1="683.091" y1="383.037" x2="678.569" y2="374.773" xlink:href="#B"><stop offset="0" stop-color="#ccc6c3"></stop><stop offset="1" stop-color="#eeedec"></stop></linearGradient></defs><path fill="url(#h)" d="M715 369l4 1 2 3c10 4 21 10 26 20v1c-1-1-3-3-3-4h-1c-6-10-20-16-30-18-17-2-30 2-44 12l-12 13-1-1c-1-3 7-9 8-12l3-3c8-6 17-11 28-11 6-1 12 0 17 1l1-1 2-1z"></path><path d="M715 369l4 1 2 3-9-2 1-1 2-1z" class="E"></path><defs><linearGradient id="i" x1="699.706" y1="393.133" x2="669.719" y2="367.726" xlink:href="#B"><stop offset="0" stop-color="#0b0b0a"></stop><stop offset="1" stop-color="#3b393a"></stop></linearGradient></defs><path fill="url(#i)" d="M649 391l4-5c12-13 26-22 43-22 6-1 12 0 18 0-1-1-1-2-1-3 3 0 5-1 8-1l1-1c2 1 2 1 4 3-3 1-5 2-7 4 1 1 3 2 4 2-1 1-2 1-4 2l-4-1-2 1-1 1c-5-1-11-2-17-1-11 0-20 5-28 11l-3 3c-1 3-9 9-8 12l1 1-10 14-1 1h0-1v-1c1-1 1-1 1-2l-1-1s0-1 1-2c1-2 1-4 2-6l-3 1c1-4 2-7 4-10z"></path><path d="M648 400c2-4 5-7 8-11 3-5 7-7 11-11 2-2 3-3 5-4 4-3 10-5 15-6h1c9-2 18-1 27 1l-2 1-1 1c-5-1-11-2-17-1-11 0-20 5-28 11l-3 3c-1 3-9 9-8 12l1 1-10 14-1 1h0-1v-1c1-1 1-1 1-2l-1-1s0-1 1-2c1-2 1-4 2-6z" class="S"></path><path d="M647 411c-1-3 1-4 2-7 2-7 9-16 15-20-1 3-9 9-8 12l1 1-10 14z" class="I"></path><path d="M660 470h1c13-1 23-10 31-20-1 2-1 4-3 5v1c-1 2-5 5-7 6s-4 3-5 4l-14 6v1h2 3l2-1h1l1-1h1v-1c1 0 2-1 3-1h1 1c-7 4-14 6-22 8-2-1-3 1-5 0 0-1 0-1 1-1-5 0-13 4-17 8v3h-1v-2l-1 1v2h0l-1-1c-3 1-8 6-9 10-1 1-2 3-2 4l-1 2c-1 3-1 4 0 7 0 1 1 1 2 2h2v-2h2c2 0 4 0 5-1 0 1-1 3-1 4-2 1-5 0-7 1l-1-1h0-4c-1 1-3 0-4 0v-2c1-2 1-4 1-5 1-1 1-1 1-2v-1l1-2v-1-1c1-8 5-17 7-25l8-2 2-2c2-1 3-1 5-1v-1l1 1h2l1 1c4-1 15-1 17 0z" class="B"></path><path d="M639 468l1 1h2l1 1c4-1 15-1 17 0l-9 3c-8 2-14 5-21 11l-13 15c1-8 5-17 7-25l8-2 2-2c2-1 3-1 5-1v-1z" class="M"></path><path d="M639 468l1 1h2l1 1c-3 2-6 2-9 4-1 1-1 1-2 1l-1 1-2 1v-1c1-1 2-3 3-4l2-2c2-1 3-1 5-1v-1z" class="H"></path><path d="M630 484v-1c3-5 8-9 13-10 3-1 5-2 8 0-8 2-14 5-21 11z" class="P"></path><path d="M695 430c2 4 1 9 0 14l-3 6c-8 10-18 19-31 20h-1c-2-1-13-1-17 0l-1-1h-2c1-1 2-1 3-1v-1c-1 0-2 0-3-1 1-1 3 0 5-1l4 1v-2c-2 0-6-1-8-1l-8-2h-1-2 0l5-3h1c1 0 3 0 4-1l9-3c2-1 4-2 6-2l1 1-2 2v1l5-1c3-1 4-1 6-3 1 1 1 1 2 1h2c4 0 7-2 11-2 2-1 4-2 6-4 5-4 6-9 7-16 0 2 0 3 1 4v-4l1-1z" class="H"></path><path d="M667 460c4-3 10-5 15-4-2 1-4 3-6 4l-1-1c-3 1-5 1-8 1z" class="D"></path><path d="M636 458c1 0 3 0 4-1l9-3c2-1 4-2 6-2l1 1-2 2c-7 3-14 5-22 6h-2 0l5-3h1z" class="K"></path><path d="M665 452c1 1 1 1 2 1h2c4 0 7-2 11-2-8 5-17 5-26 6h-1l1-1 5-1c3-1 4-1 6-3z" class="B"></path><path d="M667 460c3 0 5 0 8-1l1 1c-3 1-6 2-10 3s-9 2-13 2l-4-1c-2 0-6-1-8-1 4-2 8-1 12-1l14-2z" class="G"></path><path d="M653 465v-1c2-2 7-2 9-2 1 1 2 1 4 1h0c-4 1-9 2-13 2z" class="S"></path><path d="M691 448c0-1 2-4 3-5h0l1 1-3 6c-8 10-18 19-31 20h-1c-2-1-13-1-17 0l-1-1h-2c1-1 2-1 3-1v-1c-1 0-2 0-3-1 1-1 3 0 5-1l4 1v-2l4 1c4 0 9-1 13-2s7-2 10-3c2-1 4-3 6-4 3-2 6-5 9-8z" class="P"></path><path d="M645 465l4 1c4 0 9 1 13 1h-6v2h-14-2c1-1 2-1 3-1v-1c-1 0-2 0-3-1 1-1 3 0 5-1z" class="U"></path><path d="M691 448c-1 4-8 9-11 12-5 3-12 6-18 7-4 0-9-1-13-1v-2l4 1c4 0 9-1 13-2s7-2 10-3c2-1 4-3 6-4 3-2 6-5 9-8zm-12-97c12-5 22-4 33 0 4 2 7 4 10 7v1l-1 1c-3 0-5 1-8 1 0 1 0 2 1 3-6 0-12-1-18 0-17 0-31 9-43 22l-4 5v-1l4-17c0-4 3-9 5-12l6-6c1 0 2 0 3-1h1l3-2c3-1 6-2 8-1z" class="K"></path><defs><linearGradient id="j" x1="653.852" y1="368.23" x2="674.769" y2="350.631" xlink:href="#B"><stop offset="0" stop-color="#1b1918"></stop><stop offset="1" stop-color="#343233"></stop></linearGradient></defs><path fill="url(#j)" d="M664 355c1 0 2 0 3-1h1l3-2c3-1 6-2 8-1-10 5-18 14-26 22 0-4 3-9 5-12l6-6z"></path><path d="M795 108c1 0 2 0 4 1s4 1 7 2v1c2 1 3 3 5 5h0c1 2 2 3 2 5v1c1 2 0 5 0 8-1 1-1 2-1 3v1c-1 1-2 3-3 4l-1 1c-1 3-4 7-7 8l-3 2c1 1 2 1 4 1-2 0-5 0-7 1l3 1v1c2 0 3 0 4 1h-1c-6-1-12-1-18 0h-5c-11 3-23 11-29 21-1 1-2 3-2 4h-1c3-9 9-16 16-23-11 3-19 9-29 15-5 3-9 7-13 11-4 5-7 10-10 16-10 19-16 41-23 61l-24 78-5 16c0 2-1 5-1 6s1 1 1 1c-2 3-5 8-5 12l-4 17v1c-2 3-3 6-4 10l-4 12-2 7c-1 4-3 9-3 13h0c-2 2-2 5-3 7l-4 19h2l1-1h3 1-1l-5 3h0 2 1l8 2c2 0 6 1 8 1v2l-4-1c-2 1-4 0-5 1 1 1 2 1 3 1v1c-1 0-2 0-3 1l-1-1v1c-2 0-3 0-5 1l-2 2-8 2c-2 8-6 17-7 25v1 1l-1 2v1c0 1 0 1-1 2 0 1 0 3-1 5v2c1 0 3 1 4 0h4 0l1 1c2-1 5 0 7-1l2 2 1 1c-2 0-4 1-7 1l2 1c-1 1-1 2-2 2h0c-4 1-8 3-12 4-1 1-3 2-4 3v-1l-3 8-4 16c-1 5-3 10-3 15v1c-1 1-1 3-2 4 0 1 0 3 1 4-4 8-6 17-8 25-1 5-3 10-4 15h1l3-6h1v1c-1 2-2 4-1 5l-5 10c0 1-1 1-2 2-1 2-1 5-2 7 0 2-1 4-1 6h1-1c-2 3-2 7-3 10l-4 16c-1 3-2 7-3 10h0l2-2h0v1 1l-1 2c-1 1-1 2-1 3l-1 4h-1v2l-1 2v2l-1 2v2l-1 2v2c0 2 0 2-1 4v2l-1 1v1 1 1h-1c0 2 0 2-1 4h0l-4 15-1 1c0 1 0 3-1 3l-1 1-4 8-2 3c0 1 0 2-1 3h0c-2 1-2 2-3 3v1l1-1c0 2-1 3-2 5l-15 21c-3 4-7 7-10 11l-1-3h0c-1 1-2 2-2 4v2c0 1-1 1-1 2v-17c-1-3 0-6-1-9s0-8 0-11v-31-3-49 2c0 1 0 5 1 6 1-4 0-14 1-16v17h0c1-2 1-4 1-6-1-9 0-19 0-27l-1-21c0-3 1-6-1-9v-1l2-2 1-3h0c0 1 1 3 1 4v-5l-2-1h1c2-2 4-3 6-4 1-1 2-1 3-1 1 1 1 8 0 10v1 2l1 2 1 2v1l1 2c1 2 1 6 1 8l-1 1c0 1 1 4 0 5v28h0l1-1c0-12 2-26 4-38l2-11 104-377c1-2 1-5 2-7l3-13c0-2 0-3 1-5h-1-2c-2-1-4-1-7-1 0 1-1 1-1 1h-1c-1 0-2 1-3 2h0-1c-2 3-2 7-4 9-1 2-1 2-2 3 0 3-1 7-4 9-3-20-11-43-26-58-4-4-9-6-14-10 2 0 5 1 7 2h3 0c-2 0-5-2-7-2h-1-1c-6-2-14-2-20 2l-1-1-3 3c-1 0-1 0-2-1-1-5 2-9 5-12l-2-1 2-2c-3-1-5-2-8-3-5-3-10-6-15-10h3 11 24 150 19c14-1 28-1 42-6 5-2 12-5 14-10 2-3 2-6 1-8s-2-4-4-5h-4c-2 1-2 2-2 4 0 1 0 2 1 4h1l-1 1h0c-2 1-3 0-4 0-2-1-3-4-3-6-1-2 0-4 1-6 2-3 4-3 8-4z" class="F"></path><path d="M621 169l3 3c-1 1-2 1-3 3l-3-3c1-2 1-2 3-3z" class="T"></path><path d="M604 171l-9-6h0c2 0 3 1 5 2l1 1h0l1 1c1-1 1-2 1-3h-1s-1-1-2-1l-4-3h0c3 1 6 3 10 5l-2 1v3z" class="E"></path><path d="M618 172h0c-1 0-4-3-5-3 0-1-1-2-1-3l1-1v1c1 1 2 3 3 4l1-2 1-1h-1v-1l-1-1c-1 0-2-1-3-2h0c3 2 5 4 8 6-2 1-2 1-3 3z" class="G"></path><path d="M629 189l3 7c-1 0-3 0-4-1v2l-3-5c1-1 2-2 4-3z" class="C"></path><path d="M646 170h1v-1c2-3 3-6 5-9 0 2-1 4-1 6h1l-2 2c-1 2-3 7-6 8l2-6z" class="D"></path><defs><linearGradient id="k" x1="622.458" y1="174.056" x2="628.158" y2="178.288" xlink:href="#B"><stop offset="0" stop-color="#636061"></stop><stop offset="1" stop-color="#7c797b"></stop></linearGradient></defs><path fill="url(#k)" d="M624 172c2 2 5 5 6 7v2 3l-9-9c1-2 2-2 3-3z"></path><path d="M633 182v-1c-1-2-3-5-4-8 0 0-2-2-2-3l1-1 4-1v1h0c1 2 1 4 1 5l2 11c-1-1-2-1-2-3z" class="R"></path><path d="M628 197v-2c1 1 3 1 4 1l5 9c0 2-2 4-3 6h-1c-1-5-3-10-5-14z" class="B"></path><path d="M652 166v-1c3-3 6-7 11-7v1h1c0 1 0 1 1 1v1h1l1-1v1c-2 1-3 1-5 1-6 1-9 4-12 9-2 3-4 7-5 10-1 1-1 2-1 3v1c-1 1-1 2-1 3h-2l3-12c3-1 5-6 6-8l2-2z" class="E"></path><path d="M561 160c3 1 6 4 9 5-3 2-6 3-9 5l-3 3c-1 0-1 0-2-1-1-5 2-9 5-12z" class="I"></path><path d="M639 187l-2-28c1-2 1-5 3-6 2-2 7-3 9-2 1 1 0 3 0 4-2 0-4 1-6 2h-1c-2 10-2 20-3 30z" class="N"></path><path d="M639 187c1-10 1-20 3-30h1c2-1 4-2 6-2l-3 15-2 6-3 12v3h-2v-4z" class="K"></path><defs><linearGradient id="l" x1="605.966" y1="166.064" x2="624.618" y2="191.605" xlink:href="#B"><stop offset="0" stop-color="#353334"></stop><stop offset="1" stop-color="#898686"></stop></linearGradient></defs><path fill="url(#l)" d="M606 167c8 6 18 13 23 22-2 1-3 2-4 3-7-7-13-15-21-21v-3l2-1z"></path><path d="M662 162c2 0 5 0 6 1h2c1 0 2 0 3 1h2c1 1 2 1 3 1 2 0 2-1 4-2v1l-10 6c-2 2-4 4-6 5-2-2-3-2-5-3s-3-1-5 0c-5 2-9 10-11 14-1 2-2 4-4 6v-1-3h2c0-1 0-2 1-3v-1c0-1 0-2 1-3 1-3 3-7 5-10 3-5 6-8 12-9z" class="P"></path><defs><linearGradient id="m" x1="647.443" y1="182.955" x2="641.557" y2="215.546" xlink:href="#B"><stop offset="0" stop-color="#20201f"></stop><stop offset="1" stop-color="#494649"></stop></linearGradient></defs><path fill="url(#m)" d="M639 202l1 1c2-3 4-7 7-10 3-4 6-7 9-10 0 1 1 2 1 3s-2 5-3 7l-5 16c-1 3-1 7-3 10 0-2 0-3 1-5h-1-2c-2-1-4-1-7-1 0 1-1 1-1 1h-1c-1 0-2 1-3 2h0-1l2-5h1c1-2 3-4 3-6 1-1 1-1 2-3z"></path><defs><linearGradient id="n" x1="633.415" y1="179.102" x2="649.938" y2="190.844" xlink:href="#B"><stop offset="0" stop-color="#9d9a9b"></stop><stop offset="1" stop-color="#d7d1cd"></stop></linearGradient></defs><path fill="url(#n)" d="M641 191v1c2-2 3-4 4-6 2-4 6-12 11-14 2-1 3-1 5 0s3 1 5 3l-10 8c-3 3-6 6-9 10-3 3-5 7-7 10l-1-1c-1-6-5-13-9-18v-3-2l3 3c0 2 1 2 2 3l4 7v-1h2z"></path><path d="M621 237c-3-20-11-43-26-58-4-4-9-6-14-10 2 0 5 1 7 2h3 0c1 1 3 1 4 2h1l3 2 3 2h1c1 1 2 2 3 2 0 1 1 1 1 2 2 1 0 0 1 1 1 0 2 1 3 2 2 1 5 5 6 7 1 0 2 1 2 2l1 1 2 2v1l3 5 3 6c1 1 1 2 1 4l1 1h-1s-1 0-1-1 0-1-1-2v-2l-1-1-1-1-1-3-2-3-1-1c0-2-1-3-2-4l-2-2c-2-3-6-8-9-10-2 0-2-1-3-2-1 0-2-1-3-2s-1-1-3-2v1h0l-1-1-1 1c2 1 5 4 6 5l1 1c8 7 23 27 23 38v3c-1 2-1 2-2 3 0 3-1 7-4 9z" class="M"></path><path d="M625 228c-2-13-8-26-15-36-2-3-5-6-6-8 8 7 23 27 23 38v3c-1 2-1 2-2 3z" class="B"></path><path d="M795 108c1 0 2 0 4 1s4 1 7 2v1c2 1 3 3 5 5h0c1 2 2 3 2 5v1c1 2 0 5 0 8-1 1-1 2-1 3v1c-1 1-2 3-3 4l-1 1c-1 3-4 7-7 8l-3 2c1 1 2 1 4 1-2 0-5 0-7 1l3 1v1c2 0 3 0 4 1h-1c-6-1-12-1-18 0h-5c-11 3-23 11-29 21-1 1-2 3-2 4h-1c3-9 9-16 16-23-11 3-19 9-29 15h-1l6-4c1-1 2-1 3-2l1-1c2-1 3-2 4-2 4-2 8-4 12-5 2-1 2-1 4-1v1c2 0 3-2 4-2 3-1 5-2 7-3h3c1-1 2-1 3-1h4c1-1 3-1 4-1s2 0 3-1c2 0 4-1 6-2l1-1 1-1 1-1c2-1 3-2 5-4v-1c0-1 1-1 1-2 1-2 2-4 3-7 0-1 0-2 1-3v-3-1c-1-1 0-2-1-3h0c-1-1-1-2-1-3l-1-1-4-4h-1c-1-1-2-1-3-2h0-2-1c-1 0-1 0-1 1h-1l-1 1c-3 2-2 5-2 8h-1l-1 1 4 2c0-1 0-1 1-1l-2-2v-4l2-2c1-1 3-1 4-1l1 1c1 0 2 0 3 1l1 1c1 1 0 2 1 4v1 1l1 1-1 1c0 1 0 2-1 4s-5 5-7 6l-7 4-7 2c-1 0-2 0-3 1h-3c-1 0-1 0-2 1h-3c-2 1-4 1-5 1-2 1-7 0-9 0-2 1-34 1-39 1-2 1-6 0-8 0h-25-106c-2-1-7-1-10-1l-28 1c1 0 1 1 2 1l1 1s1 0 2 1l2 1c1 1 2 2 4 2l8 5h2 1l9 1h-10c-3-1-5-2-8-3-5-3-10-6-15-10h3 11 24 150 19c14-1 28-1 42-6 5-2 12-5 14-10 2-3 2-6 1-8s-2-4-4-5h-4c-2 1-2 2-2 4 0 1 0 2 1 4h1l-1 1h0c-2 1-3 0-4 0-2-1-3-4-3-6-1-2 0-4 1-6 2-3 4-3 8-4z" class="L"></path><defs><linearGradient id="o" x1="802.081" y1="131.061" x2="813.012" y2="121.861" xlink:href="#B"><stop offset="0" stop-color="#a8a09c"></stop><stop offset="1" stop-color="#bcbbbc"></stop></linearGradient></defs><path fill="url(#o)" d="M795 108c1 0 2 0 4 1s4 1 7 2v1c2 1 3 3 5 5h0c1 2 2 3 2 5v1c1 2 0 5 0 8-1 1-1 2-1 3v1c-1 1-2 3-3 4l-1 1c-1 3-4 7-7 8l-3 2c1 1 2 1 4 1-2 0-5 0-7 1h-2l1-1h-1c-3 2-7 2-11 2-4 1-9 1-14 4-4 2-7 6-11 8h-1c5-4 9-9 15-11 4-1 8-1 12-2 3 0 7 0 9-2h1c3-1 6-3 8-5 5-5 8-11 9-18 0-5-2-9-5-13-2-2-5-4-8-4s-5 1-7 3c-1 2-1 2-1 4 0 1 0 2-1 3v-1c-1-2-1-4 0-6s2-3 4-3c1-1 2-1 3-2z"></path><defs><linearGradient id="p" x1="676.584" y1="299.731" x2="654.456" y2="297.726" xlink:href="#B"><stop offset="0" stop-color="#373538"></stop><stop offset="1" stop-color="#918c8c"></stop></linearGradient></defs><path fill="url(#p)" d="M641 336l38-131h1c-1 0-1 1-1 2v1 1h-1c1 1 1 1 1 2v1h1v4c1 1 1 1 1 3 1 1 0 2 0 4 1 1 1 8 1 10 1 2 0 7 0 9-1 2 0 7 0 9-1 1-1 3-1 5-1 1 0 2 0 4-1 1-1 1-1 3v3c-1 1-1 2-1 3-1 1 0 5 0 7-1 0-1 4-1 5-1 1 0 3 0 4-1 1-1 1-1 2v2c-1 1-1 2-1 3 1-1 2-4 2-5l1-2v-1l3-9v-1c1-3 1-5 2-7l1-1c0-2-1 0 0-2l2-4h0l-24 78-5 16c0 2-1 5-1 6s1 1 1 1c-2 3-5 8-5 12l-4 17-2 3c-1-1 0-8 0-10v-1-4-1c-1 2-1 6-2 9h-1v-4h0l-3 6h0v-2-1c0-1-1-1 0-3h1c0-2 1-2 2-3v-1c0-1 1-2 1-3h1l-1-1c1-1 0-1 1-2h-1c1-1 1-2 2-3v-1l-1-1v-2h-2l1-1c0-3 1-4 2-6v-2-3h-1l1-1v-1c0-1 0-1-1-2h1l1 1h0v-4h0c0-2-1-3 0-4l1-1v-1-1-3 1c-1-1-1-1-1-2h2v-2c0-2 1-2 2-3v-1-3h0c0-3 2-3 2-5l-1-1c1-1 1-3 1-4v-1-2h0l1-3h1 1c-1-1-1-2-1-3s1-1 1-2v-1l-1 1v-1-1-1l1-1c0-1 0-2-1-3l-1 2 1 1c0 1-1 3-1 3h-1v-1-2-1c0-1 1-3 1-4 1-1 1-1 1-2v-3c1-1 1-1 1-2v-1c0-1 0-1 1-2v-2l1-1v-1-1l-1-3c-2 3-2 8-3 11-2 6-12 45-14 47z"></path><path d="M648 371h0v5c-1-2-1-3 0-5zm11-58c0 1 0 1 1 2v1c-1 1 0 1 0 2-1 3-3 8-2 10v1 1 1 1l-1 1c-1 2-1 5-1 8l-1 1h0c0 2 0 3-1 4v3l-1 1c0-2 0-3 1-5v-7-1h1c0-2 0-2-1-3l1-1h1c-1-2 0-6 1-8 1-1 1-2 1-4h0c0-3 0-5 1-8z" class="N"></path><path d="M657 304v-1l1 2 1-1h0v-2l1 1c0 1 0 0-1 1 1 1 1 1 1 2 0 2 0 5-2 6l1 1h0c-1 3-1 5-1 8h0c0 2 0 3-1 4-1 2-2 6-1 8h-1l1-16c1-1 0-3 0-4 1-1 1-2 1-3-1-1-1-2-1-3s1-1 1-2v-1z" class="Q"></path><defs><linearGradient id="q" x1="607.151" y1="539.412" x2="552.957" y2="524.731" xlink:href="#B"><stop offset="0" stop-color="#bfbab6"></stop><stop offset="1" stop-color="#e9e3d7"></stop></linearGradient></defs><path fill="url(#q)" d="M641 336c2-2 12-41 14-47 1-3 1-8 3-11l1 3v1 1l-1 1v2c-1 1-1 1-1 2v1c0 1 0 1-1 2v3c0 1 0 1-1 2 0 1-1 3-1 4v1 2 1h1s1-2 1-3l-1-1 1-2c1 1 1 2 1 3l-1 1v1 1 1l1-1v1c0 1-1 1-1 2s0 2 1 3h-1-1l-1 3h0v2 1c0 1 0 3-1 4l1 1c0 2-2 2-2 5h0v3 1c-1 1-2 1-2 3v2h-2c0 1 0 1 1 2v-1 3 1 1l-1 1c-1 1 0 2 0 4h0v4h0l-1-1h-1c1 1 1 1 1 2v1l-1 1h1v3 2c-1 2-2 3-2 6l-1 1h2v2l1 1v1c-1 1-1 2-2 3h1c-1 1 0 1-1 2l1 1h-1c0 1-1 2-1 3v1c-1 1-2 1-2 3h-1c-1 2 0 2 0 3v1 2h0l3-6h0v4h1c1-3 1-7 2-9v1 4 1c0 2-1 9 0 10l2-3v1c-2 3-3 6-4 10l-4 12-2 7c-1 4-3 9-3 13h0c-2 2-2 5-3 7l-4 19h2l1-1h3 1-1l-5 3h0 2 1l8 2c2 0 6 1 8 1v2l-4-1c-2 1-4 0-5 1 1 1 2 1 3 1v1c-1 0-2 0-3 1l-1-1v1c-2 0-3 0-5 1l-2 2-8 2c-2 8-6 17-7 25v1 1l-1 2v1c0 1 0 1-1 2 0 1 0 3-1 5v2c1 0 3 1 4 0h4 0l1 1c2-1 5 0 7-1l2 2 1 1c-2 0-4 1-7 1l2 1c-1 1-1 2-2 2h0c-4 1-8 3-12 4-1 1-3 2-4 3v-1l-3 8-4 16c-1 5-3 10-3 15v1c-1 1-1 3-2 4 0 1 0 3 1 4-4 8-6 17-8 25-1 5-3 10-4 15h1l3-6h1v1c-1 2-2 4-1 5l-5 10c0 1-1 1-2 2-1 2-1 5-2 7 0 2-1 4-1 6h1-1c-2 3-2 7-3 10l-4 16c-1 3-2 7-3 10h0l2-2h0v1 1l-1 2c-1 1-1 2-1 3l-1 4h-1v2l-1 2v2l-1 2v2l-1 2v2c0 2 0 2-1 4v2l-1 1v1 1 1h-1c0 2 0 2-1 4h0l-4 15-1 1c0 1 0 3-1 3l-1 1-4 8-2 3c0 1 0 2-1 3h0c-2 1-2 2-3 3v1l1-1c0 2-1 3-2 5l-15 21c-3 4-7 7-10 11l-1-3h0c-1 1-2 2-2 4v2c0 1-1 1-1 2v-17c-1-3 0-6-1-9s0-8 0-11v-31-3-49 2c0 1 0 5 1 6 1-4 0-14 1-16v17h0c1-2 1-4 1-6-1-9 0-19 0-27l-1-21c0-3 1-6-1-9v-1l2-2 1-3h0c0 1 1 3 1 4v-5l-2-1h1c2-2 4-3 6-4 1-1 2-1 3-1 1 1 1 8 0 10v1 2l1 2 1 2v1l1 2c1 2 1 6 1 8l-1 1c0 1 1 4 0 5v28h0l1-1-2 22v16c0 4-1 7-1 11 0 3-2 10-1 12l114-390z"></path><path d="M659 283l-1-2h1v1 1z" class="N"></path><path d="M587 614h1l3-6h1v1c-1 2-2 4-1 5l-5 10c0 1-1 1-2 2l3-12z" class="D"></path><path d="M611 520l1-2c2 1 5 1 8 1 2 0 4-1 6 1-4 1-8 3-12 4-1 1-3 2-4 3v-1l1-6z" class="G"></path><path d="M611 520c2 0 2 0 3 2v2c-1 1-3 2-4 3v-1l1-6z" class="D"></path><path d="M647 382v1c0 2-1 9 0 10l2-3v1c-2 3-3 6-4 10l-4 12-1-1 1-1v-2h0c1-1 1-1 1-2l-1-1c0-1 0-3 1-4v-1c-1-2 2-5 2-6s0-1 1-2h0c0-1 0-2 1-2 1-3-1-7 1-9z" class="N"></path><path d="M630 513l2 2 1 1c-2 0-4 1-7 1l-13 1c0-1 0-2 1-2v-1c2-1 3 0 5-1h4c2-1 5 0 7-1z" class="J"></path><path d="M548 746s-1-1 0-1v-1l-1 1h-1c0-1 1-1 1-2 1 0 0 0 1-1h-1l1-1v-1c1-1 1-2 1-3h-1l1-2h0l1-1v1h1l1-2v-1c0-1 0-2 1-3v-1l1-2c0-1 0-1 1-1l1-1v-2l-1-1h0c1-4 2-7 4-10 0-1 1-2 1-3 1 4-1 8-2 12 0 1-1 2 0 3v-1-1-1l1-1v-1-1-1l1-1v-1-1h0c1-2 0-2 1-3l1 2-4 15-1 1c0 1 0 3-1 3l-1 1-4 8-2 3c0 1 0 2-1 3h0zm77-276v-5c0-4 2-8 3-12 1-5 1-11 4-16h0c0-1 0-1 1-2v-1-1-2c1-2 0-3 2-5l-1-1c0-2 1-1 2-3 0-1-1-1 0-2 1-2 1-2 1-3h1v1h1v-1-1c1-2 1-3 1-4l1 1-2 7c-1 4-3 9-3 13h0c-2 2-2 5-3 7l-4 19h2l1-1h3 1-1l-5 3h0 2 1l8 2c2 0 6 1 8 1v2l-4-1c-2 1-4 0-5 1 1 1 2 1 3 1v1c-1 0-2 0-3 1l-1-1v1c-2 0-3 0-5 1l-2 2-8 2 1-4z" class="C"></path><path d="M630 463c5 0 10 2 15 2-2 1-4 0-5 1 1 1 2 1 3 1v1c-1 0-2 0-3 1l-1-1c-2-3-6-2-9-5z" class="P"></path><path d="M625 470c1-2 1-6 3-8 0 0 1 1 2 1h0c3 3 7 2 9 5v1c-2 0-3 0-5 1l-2 2-8 2 1-4z" class="D"></path><path d="M633 470c-1 0-2 1-3 1s-1 0-2-1l1-1 1-2s-1 0-2-1h1c2 0 4 0 6 1 2 0 2 0 4 2-2 0-3 0-5 1h-1z" class="E"></path><path d="M635 467c2 0 2 0 4 2-2 0-3 0-5 1h-1c0-1-1-1-1-2 1 0 2-1 3-1z" class="G"></path><defs><linearGradient id="r" x1="512.349" y1="722.06" x2="531.903" y2="712.92" xlink:href="#B"><stop offset="0" stop-color="#222024"></stop><stop offset="1" stop-color="#5f5c5c"></stop></linearGradient></defs><path fill="url(#r)" d="M524 605c1-1 2-1 3-1 1 1 1 8 0 10v1 2l1 2 1 2v1l1 2c1 2 1 6 1 8l-1 1c0 1 1 4 0 5v28h0l1-1-2 22v16c0 4-1 7-1 11 0 3-2 10-1 12-2 6-4 12-7 17 0 1 0 1-1 2-1-1-1-3-1-4l1-13-1-27c-1-4 0-8 0-11v-22-2-4c-1 3-1 9-1 12-1-9 0-19 0-27l-1-21c0-3 1-6-1-9v-1l2-2 1-3h0c0 1 1 3 1 4v-5l-2-1h1c2-2 4-3 6-4z"></path><path d="M519 728v-8h1v16c0 2-1 5 0 7 0 1 0 1-1 2-1-1-1-3-1-4l1-13z" class="M"></path><path d="M529 703h-1c-2-5-2-9-2-15 0-2 0-4 1-5 0 0 0 1 1 1 0 2 1 2 1 3v16z" class="R"></path><path d="M524 605c1-1 2-1 3-1 1 1 1 8 0 10v1 2l-7 76c-1 7 0 15 0 21v6h-1v8l-1-27c-1-4 0-8 0-11v-22-2-4c-1 3-1 9-1 12-1-9 0-19 0-27l-1-21c0-3 1-6-1-9v-1l2-2 1-3h0c0 1 1 3 1 4v-5l-2-1h1c2-2 4-3 6-4z" class="E"></path><path d="M517 614l1-3h0c0 1 1 3 1 4v59c0 9 0 18-1 27-1-4 0-8 0-11v-22-2-4c-1 3-1 9-1 12-1-9 0-19 0-27l-1-21c0-3 1-6-1-9v-1l2-2z" class="C"></path><path d="M515 617v-1l2-2c1 11 1 22 1 32 0 4 1 12-1 15 0-4 1-10 0-14l-1-21c0-3 1-6-1-9z" class="Q"></path><path d="M220 116h0c3-4 6-5 11-7h0c2-1 5-1 7 0s4 2 5 4 1 5 0 7c-1 1-2 3-3 4s-2 0-3 0c0-3 2-5 0-8-1-1-3-1-4-1-2 1-4 2-5 3-1 3 0 6 1 9 2 5 7 8 12 10 1 1 2 1 2 1 12 6 27 6 40 6h19 156 26c2 0 6-1 7 0 1 0 1 0 1 1h0c-4 3-10 6-14 8-3 2-6 3-8 5 3 3 8 7 8 13 0 1-1 2-1 3h-3l-1-1h-1l-1-1h-2 0c-4-4-10-5-15-5-1 1-2 2-3 2-1 1-3 1-4 1-6 0-11 3-16 6h0c-13 6-25 23-30 36l-1 4c0 6 3 10 4 15h1v-4h0l1 3c1 3 1 5 3 7v1c-1 2 0 3 0 5 1 5 2 12 3 17l1 5c1-2 0-4 0-6h0c1 2 1 3 2 5 3 6 5 15 5 22v3l3 10 2 10-1-1c-1-1-1-2-1-4h0c-1-2-2-4-2-6l-1-1v-2l-1-1-1 2 1 2h0l-1 1c1 1 1 2 2 3h-1v3c0 1 0 1 1 2l-1 1-1 2-1-3h0c-1 2 1 5 1 8v3c1 2 1 3 1 4s0 1 1 2v1c0 2 2 4 3 6l-1 1c1 1 1 2 2 3 0 1 1 2 2 2-1 1-1 2 0 3 1 2 0 5 0 7 1 5 3 10 4 15 1 3 2 7 3 9v3l1 1c1 3 1 6 2 9s2 6 2 9c1 6 2 13 4 18v1c0 3 1 6 2 9 0 1 0 3 1 4 0 0 1 1 1 2h1v2h-1l1 1v1c1 0 1 1 1 1l-1 1c1 3 2 7 3 10 0 2 0 4 1 6h0c0 2 1 5 2 7 0 1 0 2 1 3h0l1-1c1 2 1 5 1 7 0 3 1 10 3 12h0v1l1 4v2 1-3l1-1h0v-1h-1c1-2 1-4 1-6 0-1 0-2-1-3v-2c0-2 0-4-1-6h0c-1-1-1-1-1-2 1-1 0-2 0-3-1-2 0-4-1-5v-3c1-2 1-2 0-4-1-5-1-10-2-15-1-2 1-4 0-7 0-3 0-5-1-8l4 11s1 2 1 3c0 5 2 10 4 15 1 7 3 13 5 20 1-12 1-23 6-34v5 2c-1 2-1 5-1 7h1v1c0 2-1 5-1 8 0 8 0 16 2 24v2c4 22 10 43 22 62 1 3 7 9 7 12 0 0 0 2-1 2l3 2h2 0c0 3 0 7 2 9l9 1h0c2-1 3-1 5-1v3c0 7-1 14 0 21l1 1c2 1 3 2 4 4 1 1 1 3 1 4-1 2-2 3-3 4-1 0-2 0-3 1-2 1-4 2-6 4h-1l2 1v5c0-1-1-3-1-4h0l-1 3-2 2v1c2 3 1 6 1 9l1 21c0 8-1 18 0 27 0 2 0 4-1 6h0v-17c-1 2 0 12-1 16-1-1-1-5-1-6v-2 49 3 31c0 3-1 8 0 11s0 6 1 9v17c0-1 1-1 1-2v-2c0-2 1-3 2-4h0l1 3c3-4 7-7 10-11l15-21v2 1h3 0l2-2h1c-1 3-2 6-2 9v8h-1v-8c-1 6-4 12-3 18v3h-1c0-1 0-2-1-3 0 2-1 3 0 4v2c-2 1-1 5-3 6l-8 32-16 60-20-71c0-3-1-7-2-9l-4-19c-4 10-9 18-16 25-6 6-13 10-22 11 0-1 0-1-1-1h-1-1l1-1c-4 0-8 0-12-1-2 0-3 0-5-1l-3-1c-1 0-2 0-3-1h-1l-2-2-1-1c-6-3-12-13-14-20-1-7-1-14 4-20 4-7 10-10 17-11v-1c8 0 15 1 21 6 3 4 5 8 5 14 0 5-1 10-4 13s-8 4-12 4c-3 0-7-1-9-4v-1h-1c-1-3-1-5-1-7-3 2-3 6-3 10l1 4c2 4 5 7 9 9 7 4 15 4 22 2 8-2 15-10 19-17 4-8 7-19 6-28l-3-12-6-20-11-38-6-22c-1-2-4-6-5-8-14-17-30-33-46-46-4-2-7-4-11-6 4 0 9 2 12 3l18 7 10 4 2-2h0c0-1 0 0 1-1h1v-1l-1-1-9-3v-1l15 6-8-20-4-11-13-26c-2-3-3-5-6-6h-1c-3 0-5 1-8 1v-2h0c-1-1-2-1-3-2 0-2 0-3 1-5s2-3 4-5h1c-12-11-23-19-36-28-4-1-7-3-11-4-5-2-11-4-17-5 2 0 3 0 4-1 2 0 21 4 24 5 1-1 0-1 1-1-2 0-4 0-6-2h1c1 0 1 0 3 1h3 6v-2c4 0 7 1 10 0h1l1 2 6 1 2-1h-1c-1-1-2-2-2-3 1-2 2-3 4-4 0 1 0 1 1 1v1l1-1h-1v-2h-1v-1l-1 1c-4 0-10 0-14-1h-9-2c-1-1-4 0-5 0-14-1-29-3-42-8-21-6-38-21-48-41-8-16-9-31-3-48l4-10c4-8 11-14 18-19l5-4c7-2 14-5 21-6l6-2h-4c-2-1-5 0-7-1 0 0 0-1-1-2l1-2v-1h-1l-7-3-1-2c-2 1-3 1-4 1v-1c1-1 2-1 2-2l1-1c5-3 12-7 16-11h3l1 1s0-1 1-1c0-1 0-1 1-2 2-2 5-2 8-2 4 1 7 1 11 4h0v-4c1-3-3-6-3-9v-2h0l6 8h1c2 3 4 8 6 11 1 1 2 3 3 4 4 4 7 8 11 11l-12-43v-1s-1-3-1-4l-24-75c-5-14-10-28-17-41-9-15-21-26-37-34-4-3-8-5-13-6 4 4 8 8 11 13-1 1-2 1-3 0-1-2-3-3-4-4s0-2-1-3c-2-2-5-4-7-6h-1c-1 0-2-1-3-1v-1c-2 0-3-1-4-1h-1c-1-1-2-1-3-1s-2 0-4-1h-3-1c-2 0-3 0-4-1h-1c-1 0-2-1-3-1h0l-1-1c-6-1-12-8-15-12-2-5-4-11-3-16l2-6z" class="H"></path><path d="M402 186c2 0 3 1 5 2l-4 5c-1-2-2-2-3-3 0-1 1-3 2-4z" class="C"></path><path d="M407 181h2v1c-1 1 0 2 0 3l-2 3c-2-1-3-2-5-2 1-2 3-4 5-5z" class="N"></path><path d="M415 517l1 2c-1 1-1 0-1 2-3 0-7-2-10 0-1 0-2-1-3-1 2-1 1-1 2-1h2l3-1c2-1 4-1 6-1z" class="R"></path><path d="M504 604c1 0 2 0 3 1 0 0 1 0 1 1v5c0 3 1 6 1 9-2-2-5-13-5-16z" class="S"></path><path d="M472 808c1 0 1 1 2 1l-2 2c-5 4-8 5-13 7h-4c7-2 12-6 17-10z" class="C"></path><path d="M518 606l2-1h4c-2 1-4 2-6 4h-1c-1 1 0 1-1 0-1 0-2 0-4 1h-1c-1-2-1-3-1-5 1 1 3 1 4 1h4z" class="L"></path><path d="M428 583c1 1 1 2 2 4 1 4 3 8 5 12 0 1 1 4 1 5s0 2-1 2l-4-11v-1c0-2 0-3-1-4l-1-3c-1-1-1-2-1-3v-1z" class="G"></path><path d="M390 515l6 1c1 0 2 0 3 1l3 3c1 0 2 1 3 1 4 2 9 4 12 6-2 1-6-3-7-3-5-3-12-4-17-6l-3-3z" class="B"></path><path d="M438 625c2 1 6 2 7 5v3c-4-1-8-3-11-4l2-2h0c0-1 0 0 1-1h1v-1zm-5 138c6 0 12 2 16 7 3 3 5 6 5 11 0 4-1 9-4 11-2 2-5 3-8 3-1 0-1 1-2 1 1-1 2-1 4-1v-1c3 0 5-2 7-5s1-9 0-13-5-8-9-10c-3-1-6-2-9-2v-1z" class="O"></path><path d="M472 808c8-9 12-17 14-28 1 1 2 4 2 6l-1 3h-1c-2 7-7 15-12 20-1 0-1-1-2-1z" class="B"></path><path d="M401 513c2 1 13 0 14 3v1c-2 0-4 0-6 1l-3 1h-2c-1 0 0 0-2 1l-3-3c-1-1-2-1-3-1l2-1c1-1 2-1 3-2z" class="K"></path><path d="M399 517c3 0 7 1 10 1l-3 1h-2c-1 0 0 0-2 1l-3-3z" class="Q"></path><path d="M471 172c-3-3-5-4-9-6h-1l7-6c4 4 6 7 5 13h-1l-1-1z" class="I"></path><path d="M400 190c1 1 2 1 3 3-3 6-4 11-6 17-2-1-3-3-4-5 2-5 4-10 7-15z" class="B"></path><path d="M455 818h4c5-2 8-3 13-7l1 1c-6 6-13 10-22 11 0-1 0-1-1-1h-1-1l1-1c-4 0-8 0-12-1-2 0-3 0-5-1l-3-1c-1 0-2 0-3-1h-1l-2-2h1c2 0 3 2 5 2 4 2 10 3 15 2h6c1 0 4-1 5-1z" class="E"></path><path d="M379 522c12 5 26 12 35 22 0 1 1 2 1 3v1l1 1h-2c-2-2-5-5-8-7-8-8-17-14-27-19v-1zm40 106l29 12c-3 0-7 0-9-1l-1-1c-1 1-1 1-1 3l11 15c1 2 4 4 4 7-6-7-11-15-17-22-5-5-10-9-16-13z" class="O"></path><path d="M397 170h0c0-2 1-6 2-7 1 0 1 0 2 1v3h1l2-1h1c-1 1-2 1-2 2v1h2c-2 1-3 3-4 5h-1v1 1h0c1-1 2-2 4-3l2-2c2-3 6-5 10-6l-1 1h0c-1 1-1 1-2 1l-1 1h-1l-2 2c1 0 0 0 1-1h2 1l2-1v-1c2-2-1 1 1-1h1 0l1-1 1 1c-2 1-7 6-9 6h0c-4 1-10 8-12 11 0-3 0-6 1-8l-1-1v-3l-1-1z" class="G"></path><path d="M400 216c-1-9 6-19 11-25 11-13 26-22 43-24-1 1-2 2-3 2-1 1-3 1-4 1-6 0-11 3-16 6h0c-13 6-25 23-30 36l-1 4h0z" class="C"></path><defs><linearGradient id="s" x1="418.194" y1="168.927" x2="413.237" y2="182.5" xlink:href="#B"><stop offset="0" stop-color="#403d3e"></stop><stop offset="1" stop-color="#747172"></stop></linearGradient></defs><path fill="url(#s)" d="M407 181c5-7 13-15 22-18h0l-1 1-4 2c-1 0-1 1-2 1l-1 1-1 1h-1l-1 1c1 0 1 1 2 1v-1h1 1v-1h1c1-1 2-1 2-1h1 0l-2 1c-1 0-1 1-1 1l1 1v-1h1 0c1-1 2-1 3-1v-1h2v-1h1l2-1h1c1 0 1-1 2-1v1c-1 0-1 0-2 1-10 4-18 10-25 18 0-1-1-2 0-3v-1h-2z"></path><path d="M400 465l1-1 1 1s1 1 1 2 1 2 1 3v1c0 1 1 1 1 2v1l1 1c0 2 1 3 1 5 1 1 1 1 1 2l1 1v3h-1c0 1 0 0 1 1v1c0 1 1 3 1 5h0c1 1 2 2 2 4h1l-1 1-2-1v-1c0-1 0-1-1-2 0-2 0-4-1-6l-2-4c-2-4-9-12-14-14 1 0 3 1 5 0v-1c2-1 4-1 5-1v-2l-2-1z" class="G"></path><path d="M429 763h4v1c-7 0-12 1-17 7-4 4-6 11-5 17 0 8 2 15 7 21 2 2 4 4 5 6l-1-1c-6-3-12-13-14-20-1-7-1-14 4-20 4-7 10-10 17-11z" class="B"></path><path d="M510 605c-1 0-2-1-3-2s-2-4-3-6h22c-3 3-6 5-8 9h0-4c-1 0-3 0-4-1z" class="J"></path><path d="M388 185l-6-27c0-1 0-1 1-2 2 0 4 0 6 2 1 0 2 1 2 2 0 2-1 5-1 7l-1 9c0 3 1 9 0 11l-1-2z" class="I"></path><path d="M220 116h0c3-4 6-5 11-7h0c2-1 5-1 7 0h0l1 1 3 3v5l-1-2c-1-2-3-4-5-5s-4-1-6 0c-4 2-6 4-8 8-2 5-1 11 0 16v1c2 4 6 8 10 11 2 1 4 2 5 4h0l-1-1c-6-1-12-8-15-12-2-5-4-11-3-16l2-6z" class="C"></path><path d="M389 513l1 2 3 3c5 2 12 3 17 6h-3l-2-1h0-1l-1-1c-1 0-3 0-4-1h0-2l-1-1h-1l-2-1h-2-2-5-1 3l1 1h2 0c1 0 2 0 3 1h2 1c1 0 1 1 2 1s1 0 2 1c3 0 7 1 9 4-4-2-8-3-12-4-9-2-17-4-25-6h-1c1-1 0-1 1-1-2 0-4 0-6-2h1c1 0 1 0 3 1h3 6v-2c4 0 7 1 10 0h1z" class="D"></path><path d="M386 516c1 0 1-1 2 0 1 0 2 1 3 2-3 1-6 0-8 0v-1c1 0 1 0 2-1h1z" class="G"></path><path d="M389 513l1 2 3 3h-2c-1-1-2-2-3-2-1-1-1 0-2 0l-8-1v-2c4 0 7 1 10 0h1z" class="U"></path><path d="M513 571h0c2-1 4-1 5 0 0 4 1 21 0 24h-2c-1 1-2 1-3 1-1-8-1-17 0-25z" class="K"></path><path d="M415 531l4 2c0 4-2 8-1 12 1 1 1 1 3 1 2 4 3 8 4 12 2 5 4 10 5 16l-8-14-4-7-4-4h2l-1-1v-1c0-1-1-2-1-3l1-4c0-1 1-5 1-7l-2-1c0-1 1-1 1-1z" class="C"></path><path d="M415 540l2 2v5l1 1v1 1h0l-1 1 1 1v1l-4-4h2l-1-1v-1c0-1-1-2-1-3l1-4z" class="B"></path><path d="M418 548h2c0 2 1 3 1 4 1 4 4 4 1 8l-4-7v-1l-1-1 1-1h0v-1-1z" class="C"></path><path d="M514 804l1 1h1c3-2 6-5 9-8 2-2 5-4 6-7l10-11 1 1v1 1c0 2-1 3 0 4v2c-2 1-1 5-3 6v1c-2 1-3 4-4 6 0-1-1-1 0-2v-1c0-1 0-1 1-1v-1-1-2h1v-4l-3 3-2 2-2 2c0 1-2 3-3 3-2 3-5 6-8 7-1 1-1 3-3 3l-1 1h0v8h-1v-14zM364 175c-3-2-8-5-10-7h1l1 1h0l1 1h1 0c1 0 1 1 2 1l1-1v-1h1 0 1c0-1 1-2 1-2h0c1-1 1-1 2-1l-1-1h1v-1h1l1-1c5 1 7 3 10 6 2 3 7 16 9 16h1l1 2c-1 1-1 3-1 4h0c-2-1-2-2-3-3-2-4-3-8-6-12-1-2-4-5-7-6-2 0-7 3-8 5z" class="T"></path><path d="M419 166c2-2 6-5 9-5h0l-5 3v1l1-1c3-2 9-4 13-4h-1c-3 1-5 2-7 3-9 3-17 11-22 18-2 1-4 3-5 5-1 1-2 3-2 4-3 5-5 10-7 15h-1c0-1 0-2-1-3h0c-1 0-3-3-4-4 1-1 2-1 2-2l1-1c2-2 3-4 4-7h2c4-6 9-11 14-16 2 0 7-5 9-6z" class="F"></path><path d="M389 196l1-1c2-2 3-4 4-7h2c-2 4-4 9-5 14h0c-1 0-3-3-4-4 1-1 2-1 2-2z" class="C"></path><path d="M402 555c2-2 3-3 7-4 10 6 15 22 19 32v1c0 1 0 2 1 3l1 3c1 1 1 2 1 4v1l-13-26c-2-3-3-5-6-6h-1c-3 0-5 1-8 1v-2h0c-1-1-2-1-3-2 0-2 0-3 1-5h1z" class="L"></path><path d="M402 555c2-2 3-3 7-4 10 6 15 22 19 32v1c-4-9-7-17-13-24-1-2-2-4-3-5l-7 5h0c0-2 4-5 5-6h1c-2-1-6 4-9 4h-1l1-3z" class="O"></path><path d="M512 610c2-1 3-1 4-1 1 1 0 1 1 0l2 1v5c0-1-1-3-1-4h0l-1 3-2 2v1c2 3 1 6 1 9l1 21c0 8-1 18 0 27 0 2 0 4-1 6h0v-17c-1 2 0 12-1 16-1-1-1-5-1-6v-2 49 3l-1-108c-1-1 0-4-1-5z" class="C"></path><path d="M512 610c2-1 3-1 4-1 1 1 0 1 1 0l2 1v5c0-1-1-3-1-4h0l-1 3-2 2v1 42l-1-22v-16c0-2 0-4-1-6-1-1 0-4-1-5z" class="R"></path><path d="M364 175c1-2 6-5 8-5 3 1 6 4 7 6 3 4 4 8 6 12 1 1 1 2 3 3h0c0-1 0-3 1-4 1-2 0-8 0-11l1 17c2-4 5-8 5-12l2-11 1 1v3l1 1c-1 2-1 5-1 8 2-3 8-10 12-11h0c-5 5-10 10-14 16h-2c-1 3-2 5-4 7l-1 1c0 1-1 1-2 2-5-7-10-12-16-17l-2-2c-2-1-4-3-5-4z" class="Q"></path><defs><linearGradient id="t" x1="383.347" y1="186.2" x2="376.754" y2="189.499" xlink:href="#B"><stop offset="0" stop-color="#aaa2a4"></stop><stop offset="1" stop-color="#bbb8b1"></stop></linearGradient></defs><path fill="url(#t)" d="M369 179c1-2 4-3 6-4 4 4 5 9 8 13 2 3 5 5 6 8 0 1-1 1-2 2-5-7-10-12-16-17l-2-2z"></path><path d="M387 421h1l2 2h1c0 1 1 2 1 3 1 1 1 2 1 3s1 1 1 2 1 3 1 5l1 2v1c1 2 1 4 2 6v2c1 1 1 1 1 2l1 2c0 2 1 6 2 8l-1 1 1 1c0 1-1 2 1 3 0 1 0 1 1 2h0v1l-1-1v1c0-1-1-2-1-2l-1-1-1 1 2 1v2c-1 0-3 0-5 1v1c-2 1-4 0-5 0l-4-1h1l-1-1 2-1v-1-1c3-1 6-1 9-2l-2-1v-1c-1 0-3 0-4-1l-12-3h-1-1l-1-1h1c-2-2-4-3-5-5 0 0 1 1 2 1h1c2 0 8-1 10-1 1 1 2 1 3 1l1 1c2 0 1 0 2 1h5v-1h0c0-1 0-2-1-3v-2l-1-1v-2c-1-1-1-2-1-2v-1c0-1 0-2-1-3 0-1 0-2-1-4l-1 1v2h-1v-1-1c1-5-2-11-4-15z" class="B"></path><path d="M391 423c0 1 1 2 1 3 1 1 1 2 1 3s1 1 1 2 1 3 1 5l1 2v1c1 2 1 4 2 6v2c1 1 1 1 1 2l1 2c0 2 1 6 2 8l-1 1 1 1c0 1-1 2 1 3 0 1 0 1 1 2h0v1l-1-1v1c0-1-1-2-1-2l-1-1-1 1 2 1v2c-1 0-3 0-5 1v1c-2 1-4 0-5 0l-4-1h1l-1-1 2-1v-1-1c3-1 6-1 9-2l-2-1v-1l3 1 1-1c-5-3-13-2-17-5 1 0 3 0 5 1l12 2c-3-12-6-25-11-36h1z" class="E"></path><path d="M400 465l2 1v2c-1 0-3 0-5 1-3 0-6 0-9-1l12-3h0z" class="F"></path><path d="M342 513c2 0 3 0 4-1 2 0 21 4 24 5h1l25 6c4 1 8 2 12 4l7 4s-1 0-1 1l2 1c0 2-1 6-1 7l-1 4c-9-10-23-17-35-22v1c-1 0-1 0-2-1-2-1-4-1-6-1l-1 1c-4-1-7-3-11-4-5-2-11-4-17-5z" class="M"></path><path d="M342 513c2 0 3 0 4-1 2 0 21 4 24 5h1v1h-1c-3-1-6-2-8-2h-2c5 2 10 3 15 4 1 0 3 1 4 2v1c-1 0-1 0-2-1-2-1-4-1-6-1l-1 1c-4-1-7-3-11-4-5-2-11-4-17-5z" class="E"></path><path d="M371 517l25 6c4 1 8 2 12 4l7 4s-1 0-1 1c-14-6-29-10-43-14v-1z" class="O"></path><defs><linearGradient id="u" x1="535.39" y1="785.131" x2="524.997" y2="776.38" xlink:href="#B"><stop offset="0" stop-color="#5d595b"></stop><stop offset="1" stop-color="#949090"></stop></linearGradient></defs><path fill="url(#u)" d="M514 765c1 3 0 6 1 9v17c0-1 1-1 1-2v-2c0-2 1-3 2-4h0l1 3c3-4 7-7 10-11l15-21v2 1h3 0l2-2h1c-1 3-2 6-2 9v8h-1v-8c-1 6-4 12-3 18v3h-1c0-1 0-2-1-3v-1-1l-1-1-10 11c-1 3-4 5-6 7-3 3-6 6-9 8h-1l-1-1v-39z"></path><path d="M424 800c2 4 3 6 6 9v1c-2-1-3-2-4-2l-2 1c-4-3-7-6-8-11-2-4-3-10-1-15 2-4 6-8 11-9 4-2 9-2 13 0s8 5 9 10c1 2 0 4-1 6-2 2-4 3-7 4h-3c-1 1-1 1-3 1v-1c-1 0-2 0-3-1v1h-1-1c-1-3-1-5-1-7-3 2-3 6-3 10l1 4-2-1z" class="J"></path><path d="M424 800c-1-3-1-7 0-10s3-6 6-7h3c0 2-1 5 0 7s2 3 4 4h0c-1 1-1 1-3 1v-1c-1 0-2 0-3-1v1h-1-1c-1-3-1-5-1-7-3 2-3 6-3 10l1 4-2-1z" class="F"></path><defs><linearGradient id="v" x1="405.116" y1="205.072" x2="378.577" y2="244.89" xlink:href="#B"><stop offset="0" stop-color="#1f1e20"></stop><stop offset="1" stop-color="#6f6a6d"></stop></linearGradient></defs><path fill="url(#v)" d="M371 181c6 5 11 10 16 17 1 1 3 4 4 4h0c1 1 1 2 1 3h1c1 2 2 4 4 5v1c1 2 1 5 2 6l1-1h0c0 6 3 10 4 15h1v-4h0l1 3c1 3 1 5 3 7v1c-1 2 0 3 0 5 1 5 2 12 3 17l-1 1c1 1 1 4 1 6h0c0 2 1 4 2 6v5h0v-1l-1-1v-1-1c0-1 0-1-1-2v-1l-3-7c-1-3-1-7-3-11-2-6-7-11-12-15-2-3-5-6-8-7l-9-31c-2-7-5-13-6-19z"></path><path d="M409 243c-1 1-1 2-1 3h0c0-1 0-2-1-3h0c1-2-1-10-1-13 1 3 1 5 3 7v1c-1 2 0 3 0 5z" class="U"></path><path d="M361 318l1 1v1c0 1 1 1 1 2 0 2 1 4 1 6 1 1 1 2 2 3v1c0 1 0 1 1 2v1 1c1 2 1 4 2 7v1c1 1 1 2 1 4l2 5 1 5c0 1 1 3 1 4s-1 1 0 2l1 1h0c1 1 0 1 1 2l1 2c0 1 1 2 1 3s0 2 1 3v1h-1v1c1 1 1 2 1 3v1l1 2v2c1 2 0-1 1 2 1 2 1 4 2 6v4c0 1 0 1 1 3 1 1 1 1 1 3 1 0 1 1 1 2l1 2h0c0 1 0 1 1 2v2c0 2 1 3 2 5v3c1 1 1 2 1 3v1h-1l-2-2h-1 0c-1-1-1-2-2-3-4-10-10-19-17-27v-1l-1-1h1c-3-3-6-5-7-8-5-4-10-8-16-10-3 0-6 0-9-1-4-1-9 0-13 0l6-2h-4c-2-1-5 0-7-1 0 0 0-1-1-2l1-2v-1h-1l-7-3-1-2c-2 1-3 1-4 1v-1c1-1 2-1 2-2l1-1c5-3 12-7 16-11h3l1 1s0-1 1-1c0-1 0-1 1-2 2-2 5-2 8-2 4 1 7 1 11 4h0v-4c1-3-3-6-3-9v-2h0l6 8h1c2 3 4 8 6 11 1 1 2 3 3 4 4 4 7 8 11 11l-12-43v-1z" class="K"></path><path d="M338 339c4 1 7 1 11 4h0l3 2c1 2 4 4 5 5l1 1 1 1h1l4 6v-1c2 2 5 5 7 6s4 4 5 7v2l-1 1-4-5c-7-9-18-19-30-20-11-1-24 2-32 9-2 1-3 1-4 1v-1c1-1 2-1 2-2l1-1c5-3 12-7 16-11h3l1 1s0-1 1-1c0-1 0-1 1-2 2-2 5-2 8-2z" class="F"></path><path d="M338 339c4 1 7 1 11 4h0l3 2c-7-2-17-3-23-2 0-1 0-1 1-2 2-2 5-2 8-2z" class="B"></path><defs><linearGradient id="w" x1="342.868" y1="398.105" x2="367.36" y2="378.131" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#353334"></stop></linearGradient></defs><path fill="url(#w)" d="M361 318l1 1v1c0 1 1 1 1 2 0 2 1 4 1 6 1 1 1 2 2 3v1c0 1 0 1 1 2v1 1c1 2 1 4 2 7v1c1 1 1 2 1 4l2 5 1 5c0 1 1 3 1 4s-1 1 0 2l1 1h0c1 1 0 1 1 2l1 2c0 1 1 2 1 3s0 2 1 3v1h-1v1c1 1 1 2 1 3v1l1 2v2c1 2 0-1 1 2 1 2 1 4 2 6v4c0 1 0 1 1 3 1 1 1 1 1 3 1 0 1 1 1 2l1 2h0c0 1 0 1 1 2v2c0 2 1 3 2 5v3c1 1 1 2 1 3v1h-1l-2-2h-1 0c-1-1-1-2-2-3-4-10-10-19-17-27v-1l-1-1h1c-3-3-6-5-7-8-5-4-10-8-16-10-3 0-6 0-9-1-4-1-9 0-13 0l6-2h-4c-2-1-5 0-7-1 0 0 0-1-1-2l1-2c2 1 9 0 11 0 7 0 15 2 21 4 13 5 25 17 32 28-1-4-2-7-3-11-1-3-1-7-3-10l-1-1 1-1v-2c-1-3-3-6-5-7s-5-4-7-6v1l-4-6h-1l-1-1-1-1c-1-1-4-3-5-5l-3-2v-4c1-3-3-6-3-9v-2h0l6 8h1c2 3 4 8 6 11 1 1 2 3 3 4 4 4 7 8 11 11l-12-43v-1z"></path><path d="M329 368c12 0 19 2 28 7 6 4 10 8 12 13-2-3-5-5-8-7-5-4-10-8-16-10-3 0-6 0-9-1-4-1-9 0-13 0l6-2z" class="N"></path><defs><linearGradient id="x" x1="359.887" y1="344.831" x2="347.543" y2="339.843" xlink:href="#B"><stop offset="0" stop-color="#121211"></stop><stop offset="1" stop-color="#2f2b2d"></stop></linearGradient></defs><path fill="url(#x)" d="M346 330v-2h0l6 8c3 8 8 14 12 21v1l-4-6h-1l-1-1-1-1c-1-1-4-3-5-5l-3-2v-4c1-3-3-6-3-9z"></path><path d="M361 381c3 2 6 4 8 7 5 5 9 10 12 16l1 1v3c1 3 4 6 3 10-4-10-10-19-17-27v-1l-1-1h1c-3-3-6-5-7-8z" class="C"></path><path d="M386 231c3 1 6 4 8 7 5 4 10 9 12 15 2 4 2 8 3 11l3 7v1c1 1 1 1 1 2v1 1l1 1v1h0v-5c-1-2-2-4-2-6h0c0-2 0-5-1-6l1-1 1 5c1-2 0-4 0-6h0c1 2 1 3 2 5 3 6 5 15 5 22v3l3 10 2 10-1-1c-1-1-1-2-1-4h0c-1-2-2-4-2-6l-1-1v-2l-1-1-1 2 1 2h0l-1 1c1 1 1 2 2 3h-1v3c0 1 0 1 1 2l-1 1-1 2-1-3h0c-1 2 1 5 1 8v3c1 2 1 3 1 4s0 1 1 2v1c0 2 2 4 3 6l-1 1c1 1 1 2 2 3 0 1 1 2 2 2-1 1-1 2 0 3 1 2 0 5 0 7 1 5 3 10 4 15 1 3 2 7 3 9v3l1 1c1 3 1 6 2 9s2 6 2 9c1 6 2 13 4 18v1c0 3 1 6 2 9 0 1 0 3 1 4 0 0 1 1 1 2h1v2h-1l1 1v1c1 0 1 1 1 1l-1 1h0v6l4 24c0 1 0 3 1 5 0 1 0 3-1 4 0-1 0-1-1-2h0l-61-227-3-12z" class="B"></path><path d="M391 241l3 3c1 3 3 5 5 8 2 5 3 10 5 16l5 13c1 2 2 4 2 6 1 1 1 1 0 3h0l-1 1-1-2h1v-3h0l-2-4c0-1-1-2-2-4h0l-1-1h0c0-3-1-3-1-5h-1c-1-1-1-3-2-4-2-3-2-6-4-9 0-2-1-3-2-5l-1-4c0-1-1-3-2-4l-1-5z" class="C"></path><defs><linearGradient id="y" x1="417.93" y1="286.353" x2="414.073" y2="287.998" xlink:href="#B"><stop offset="0" stop-color="#777475"></stop><stop offset="1" stop-color="#8e8a8b"></stop></linearGradient></defs><path fill="url(#y)" d="M386 231c3 1 6 4 8 7 5 4 10 9 12 15 2 4 2 8 3 11l3 7v1c1 1 1 1 1 2v1 1l1 1v1h0v-5c-1-2-2-4-2-6h0c0-2 0-5-1-6l1-1 1 5c1-2 0-4 0-6h0c1 2 1 3 2 5 3 6 5 15 5 22v3l3 10 2 10-1-1c-1-1-1-2-1-4h0c-1-2-2-4-2-6l-1-1v-2l-1-1-1 2 1 2h0l-1 1c1 1 1 2 2 3h-1v3c0 1 0 1 1 2l-1 1-1 2-1-3h0c-1 2 1 5 1 8-1 0-1-1-1-2s-1-2-1-3l-1-1c0-1-1-4 0-5 0-1 0-1-1-2v-1c0-2-1-3-1-4-1-1 0-1-1-2v-1-1h-1c0-1-1-1-1-2l1-1h0c1-2 1-2 0-3 0-2-1-4-2-6l-5-13c-2-6-3-11-5-16-2-3-4-5-5-8l-3-3-1-2h-1 0v4l-3-12z"></path><path d="M413 265c1-2 0-4 0-6h0c1 2 1 3 2 5 3 6 5 15 5 22v3l-7-24z" class="H"></path><path d="M386 231c3 1 6 4 8 7h0c1 2 1 3 2 5 2 3 4 5 6 9 1 2 2 5 3 8 4 8 8 17 10 26h0l-1-2c-1-2 0-1-1-2l-1-1v-1c-1-1-1-1-1-2s-1-2-1-3l-1-2-3-6v-1c-1-1-1-2-2-3l-1-2-1-4v-1c-1-1-1-2-2-3l-3-6-1-1c0-1-1-2-2-3 0-1-1-1-1-2-1 0-1 0-1-1-1-1-1-1-2-1h-1 0v4l-3-12z" class="Q"></path><defs><linearGradient id="z" x1="388.471" y1="389.618" x2="400.609" y2="386.263" xlink:href="#B"><stop offset="0" stop-color="#524e51"></stop><stop offset="1" stop-color="#6e6a6b"></stop></linearGradient></defs><path fill="url(#z)" d="M364 250c2 4 2 10 4 14l13 47 40 151 17 61c1 7 4 14 5 21v-1c-1-1-1-2-1-2v-2c-2-3-2-6-3-9 0-1-1-1-1-2v-2c-1-1-1-2-1-3-1-2-1-4-2-6h0c-3-2-4-7-6-10v-2c-1-1-1-1-1-2v-1l-2-2c-1-3-3-7-4-10h0v-1c0-1 0-1-1-2v-2l-3-6-1-4c-1-2-2-3-2-6-1-1-1-1-2-3h0v-3h-1v-1c0-1-1-1-1-2h0c-1-1-1-2-1-3h0l-1-1v-2l-1-1c0-2-1-4-1-5v-2l-1-2-1-5c0-1-1-2-2-4h0c-1-2-1-3-2-4v-1h0c0-1-1-2-1-2l-2-7v-1l-1-1v-2l-2-5c0-1-1-2-1-3v-1l-1-1v-1-2l-1-2v-2l-1-2s0-2-1-2v-2c-1-2-1-2-1-3v-3c-1-1-1-2-1-3s0-1-1-2c-1-2 0-2 0-3l-2-4v-2c-1-1-1-1-1-2v-1c-1 0-1-1-1-1v-3c-1-1-1-1-1-2-1-2 0-3 0-5l-1-4-1-1v-2l-1-1v-3c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-2c-1-2-1-2-1-3-1-1 0-3 0-4-1-1-1-2-1-3v-2c-1-1-1-1-1-2v-2c-1-2-1-2-1-3v-1-1c-1-2-1-2-1-3v-2c-1-1-1-1-1-2-1-1 0-2 0-3-1-2-1-3-1-4-1-1 0-2 0-3-1-2-1-3-1-4-1-1 0-1 0-2-1-1-1-1-1-2v-3c-1-1-1-1-1-2-1-2 0-3 0-4-1-1-1-3-1-4-1-1 0-2 0-3-1-2-1-5-1-6v-3-1c-1-1 0-3 0-5-1-1-1-4-1-5 1-2 1-2 1-3h-1v-2-4z"></path><defs><linearGradient id="AA" x1="480.463" y1="512.348" x2="463.875" y2="514.174" xlink:href="#B"><stop offset="0" stop-color="#4f4c4f"></stop><stop offset="1" stop-color="#858181"></stop></linearGradient></defs><path fill="url(#AA)" d="M454 416l4 11s1 2 1 3c0 5 2 10 4 15 1 7 3 13 5 20 1-12 1-23 6-34v5 2c-1 2-1 5-1 7h1v1c0 2-1 5-1 8 0 8 0 16 2 24v2c4 22 10 43 22 62 1 3 7 9 7 12 0 0 0 2-1 2l3 2h2 0c0 3 0 7 2 9l-3 1c-1 1-1 1-1 2l1 23c-1 1-1 0-2 0s-2 0-3 1c-1-1-1-1-1-2v-1l-2-6c0-1 0-2-1-3v-1-2l-1-2v-1c0-1 0-2-1-2v-3l-1-1v-2c-1-1-1-3-1-4l-2-7c0-1 0-2-1-2v-3l-1-3h-1v-2l-3-14-1-1v-2c-1-2-1-3-1-5l-1-4-1-1v-1c-1-2 0 0 0-2h0c-1-2-1-3-1-4-1 0 0 0-1-1v-1l-1-3v5c1 1 1 4 1 6 1 1 0 2 0 3 1 1 1 2 1 3v3c1 1 1 3 1 4 1 1 0 4 0 6 1 1 1 2 1 4 1 1 0 3 0 5 1 1 1 4 1 5 1 1 0 2 0 4 1 1 1 1 1 3v3c1 1 1 4 1 6 1 1 0 2 0 3 1 1 1 3 1 5 1 1 0 4 0 5 2 2 1 10 1 13-1 2-1 6-1 8h0c0 1 0 2-1 3v4h0v2c0 1 1 2 1 3s0 2 1 3c0 1 0 3 1 4v2c0 1 0 2 1 3v2 1 2c-1-9-4-18-6-27l-16-64-12-44c-2-8-5-17-6-26h0c1 1 1 1 1 2 1-1 1-3 1-4-1-2-1-4-1-5l-4-24v-6h0c1 3 2 7 3 10 0 2 0 4 1 6h0c0 2 1 5 2 7 0 1 0 2 1 3h0l1-1c1 2 1 5 1 7 0 3 1 10 3 12h0v1l1 4v2 1-3l1-1h0v-1h-1c1-2 1-4 1-6 0-1 0-2-1-3v-2c0-2 0-4-1-6h0c-1-1-1-1-1-2 1-1 0-2 0-3-1-2 0-4-1-5v-3c1-2 1-2 0-4-1-5-1-10-2-15-1-2 1-4 0-7 0-3 0-5-1-8z"></path><path d="M458 458v-5c1 3 3 11 3 14h0v2c1 2 1 3 1 5l1 1v1 2c0 1 1 3 1 4v1h0c0 2 0 2 1 3v2l1 4v2 1c1 1 1 3 1 4h0v5c-1-1-2-6-2-7 0-2-1-3-1-5l-1 1v-2-1-1h-1c-1-2-1-3-2-4v-3l1-1h0v-1h-1c1-2 1-4 1-6 0-1 0-2-1-3v-2c0-2 0-4-1-6h0c-1-1-1-1-1-2 1-1 0-2 0-3z" class="Q"></path><path d="M461 474l3 18-1 1v-2-1-1h-1c-1-2-1-3-2-4v-3l1-1h0v-1h-1c1-2 1-4 1-6z" class="N"></path><defs><linearGradient id="AB" x1="466.758" y1="488.749" x2="451.639" y2="490.083" xlink:href="#B"><stop offset="0" stop-color="#807d7e"></stop><stop offset="1" stop-color="#b7b1af"></stop></linearGradient></defs><path fill="url(#AB)" d="M450 470h0c1 1 1 1 1 2 1-1 1-3 1-4-1-2-1-4-1-5l-4-24v-6h0c1 3 2 7 3 10 0 2 0 4 1 6h0c0 2 1 5 2 7 0 1 0 2 1 3h0l1-1c1 2 1 5 1 7 0 3 1 10 3 12h0v1l1 4v2 1c1 1 1 2 2 4h1v1 1 2l1-1c0 2 1 3 1 5 0 1 1 6 2 7v-5c1 2 1 3 1 5v2h1l1 5 1 2v1c1 1 1 3 1 5v3l1 1v1c0 1 0 2 1 3h-1v-1c-1-2-1-3-1-4l-1-1c-1-2 0-3-1-5v-2l-1-1v1c0 1 0 3 1 4v6c1 3 0 6 1 9 1 1 0 3 1 5 0 2 0 4-1 5 0 1-1 1-1 1l-1-3c0-1 0-1-1-2v1l-12-44c-2-8-5-17-6-26z"></path><path d="M475 480c4 22 10 43 22 62 1 3 7 9 7 12 0 0 0 2-1 2l3 2h2 0c0 3 0 7 2 9l-3 1c-1 1-1 1-1 2l1 23c-1 1-1 0-2 0s-2 0-3 1c-1-3-1-5-2-8l-4-16-18-72-4-17 1-1z" class="J"></path><path d="M503 556l3 2h2 0c0 3 0 7 2 9l-3 1c-1 1-1 1-1 2 0-2-3-3-3-5-1-3-1-6 0-9z" class="D"></path><path d="M323 370c4 0 9-1 13 0 3 1 6 1 9 1 6 2 11 6 16 10 1 3 4 5 7 8h-1l1 1v1c7 8 13 17 17 27 1 1 1 2 2 3h0c2 4 5 10 4 15v1 1h1v-2l1-1c1 2 1 3 1 4 1 1 1 2 1 3v1s0 1 1 2v2l1 1v2c1 1 1 2 1 3h0v1h-5c-1-1 0-1-2-1l-1-1c-1 0-2 0-3-1-2 0-8 1-10 1h-1c-1 0-2-1-2-1 1 2 3 3 5 5h-1l1 1h1 1l12 3c1 1 3 1 4 1v1l2 1c-3 1-6 1-9 2v1 1l-2 1 1 1h-1l4 1c5 2 12 10 14 14l2 4c1 2 1 4 1 6s0 2 1 4c2 4 4 10 4 15-3 0-11-1-13 0-1 1-2 1-3 2h-1c-1-1-2-2-2-3 1-2 2-3 4-4 0 1 0 1 1 1v1l1-1h-1v-2h-1v-1l-1 1c-4 0-10 0-14-1h-9-2c-1-1-4 0-5 0-14-1-29-3-42-8-21-6-38-21-48-41-8-16-9-31-3-48l4-10c4-8 11-14 18-19l5-4c7-2 14-5 21-6z" class="J"></path><path d="M369 451c-1-1-1-4-1-5h0v-1c1-1 2-1 4-1 1 2 0 3 0 5h-2l-1 2z" class="M"></path><path d="M369 451l1-2h2c1 0 1 2 2 2 1 2 3 3 5 5h-1c-1 0-2-1-4-1-2-1-4-2-5-4z" class="H"></path><path d="M281 401c4-3 7-6 11-9 5-4 12-7 18-9 1 1 1 1 2 1h1v-1h1 1v-1h1l1 1v-1h1c2 1 4 1 6 1 2-2 4-1 7-1l1 3 1 1h-16c-7 1-14 3-20 7-4 2-7 5-10 7-1 2-4 5-6 6 0 0-1 0-1-1l-1 1c0 1-1 1-2 2v-1l1-2v-1l3-3z" class="O"></path><path d="M342 455c6 6 12 10 20 13 9 3 19 5 27 10 6 3 10 8 14 12 2 2 4 5 6 7l1 1c2 4 4 10 4 15-3 0-11-1-13 0-1 1-2 1-3 2h-1c-1-1-2-2-2-3 1-2 2-3 4-4 0 1 0 1 1 1v1l1-1 1 1 1-1c2-1 4-2 4-4 1-4-2-8-4-11v-1c-4-5-8-7-13-10-3-2-6-5-9-6-6-2-11-3-16-6-2 0-3-2-4-2-4-3-9-4-13-7-2-2-5-5-6-7z" class="B"></path><path d="M336 425c0 2 0 2-1 4 0 3 0 7 1 10v1c0 1 1 3 1 4 1 2 2 4 4 6 0 3 5 7 8 8 6 5 14 8 22 9h9v1c1 1 1 1 3 1v1l5-1 4 1c5 2 12 10 14 14l2 4c1 2 1 4 1 6s0 2 1 4l-1-1c-2-2-4-5-6-7-4-4-8-9-14-12-8-5-18-7-27-10-8-3-14-7-20-13-3-5-6-9-8-14h0c-1-3-1-8-1-10l1-1c0-2 1-3 2-5z" class="E"></path><path d="M408 488c1 2 1 4 1 6s0 2 1 4l-1-1c0-2-1-4-2-5-1-2-1-3-1-4h1 1z" class="D"></path><path d="M371 467h9v1c1 1 1 1 3 1v1l5 2-2 1-2-1c-2-1-4-1-7-1-3-1-3-1-5-4h-1z" class="G"></path><path d="M371 467h9v1c1 1 1 1 3 1-3 1-5 0-8 0l-2-2h-1-1z" class="P"></path><path d="M388 469l4 1c5 2 12 10 14 14l2 4h-1-1c-5-7-11-13-18-16l-5-2 5-1z" class="L"></path><path d="M336 425c1-2 3-4 5-5 3 0 8 0 10 2l2 2c-1 0-1 0-2-1-1 0-2-1-3-1v1h1c2 1 3 3 5 6v1 4c1 0 2-3 2-4 1-2 0-3 0-5 2 2 2 4 1 7 0 1-1 3-2 4-1 0-2 0-3 1l-1-1 2-5c-1-3-2-6-5-7h-5c-3 2-5 4-5 6-2 4-1 8 1 11 4 7 11 11 19 13l12 3h2c-1-1-1-1-2-1-3-1-5-2-6-4-1-1-1-2-1-4l1-1c1 1 1 0 1 1 0 2 0 2 1 3v-1h1c1 1 2 4 4 5 1 1 2 1 4 1 1 1 2 1 4 1h1 1l12 3c1 1 3 1 4 1v1l2 1c-3 1-6 1-9 2v1 1l-2 1 1 1h-1l-5 1v-1c-2 0-2 0-3-1v-1h-9c-8-1-16-4-22-9-3-1-8-5-8-8-2-2-3-4-4-6 0-1-1-3-1-4v-1c-1-3-1-7-1-10 1-2 1-2 1-4z" class="F"></path><path d="M371 467c-8-1-16-4-22-9-3-1-8-5-8-8 4 4 9 8 15 10 4-2 11 1 15 2 5 0 13-1 17 1 1 0 2 1 3 1l6-2 2 1c-3 1-6 1-9 2v1 1l-2 1 1 1h-1l-5 1v-1c-2 0-2 0-3-1v-1h-9z" class="B"></path><path d="M380 467l10-2v1 1l-2 1 1 1h-1l-5 1v-1c-2 0-2 0-3-1v-1z" class="G"></path><path d="M380 468h8l1 1h-1l-5 1v-1c-2 0-2 0-3-1z" class="U"></path><path d="M356 460c4-2 11 1 15 2l6 3c-7 0-14-2-21-5z" class="P"></path><defs><linearGradient id="AC" x1="384.357" y1="460.97" x2="377.143" y2="465.03" xlink:href="#B"><stop offset="0" stop-color="#222321"></stop><stop offset="1" stop-color="#3b3435"></stop></linearGradient></defs><path fill="url(#AC)" d="M371 462c5 0 13-1 17 1 1 0 2 1 3 1-4 0-9 1-14 1l-6-3z"></path><path d="M323 370c4 0 9-1 13 0 3 1 6 1 9 1 6 2 11 6 16 10 1 3 4 5 7 8h-1l1 1v1c7 8 13 17 17 27 1 1 1 2 2 3h0c2 4 5 10 4 15-1-1-1-3-2-4l-2-4c-1-2-1-3-2-5 0-1 0 0-1-1l-3-6c-2-3-4-6-7-10-2-3-5-5-7-8 0-1-1-2-3-3l-4-4-8-6c-1 0-2-1-3-1l-1-1h-4c-1 1-1 3-2 4-1 3-2 4-4 5h-4c-1 0-2 0-3-1 1-2 2-3 2-5l-1-1-1-3c-3 0-5-1-7 1-2 0-4 0-6-1h-1v1l-1-1h-1v1h-1-1v1h-1c-1 0-1 0-2-1-6 2-13 5-18 9-4 3-7 6-11 9l-3 3c1-1 1-3 2-4l-1-1c4-8 11-14 18-19l5-4c7-2 14-5 21-6z" class="H"></path><path d="M337 384l1-2c0-1 0-2 1-3 1 0 1 0 3 1 0 2-1 5-2 7s-2 3-3 4h-1v-2c1-1 1-4 1-5z" class="I"></path><path d="M325 377c1-1 2-2 4-2 1 0 2 1 3 2v1l1 1s1 1 1 3h1l1 2-1 1-1-1c-1 0-1 0-2 1l-1-3c-2-2-4-4-6-5z" class="F"></path><path d="M323 370c4 0 9-1 13 0-2 1-5 0-7 1-12 1-22 3-33 10-6 4-11 11-14 17l-2 2-1-1c4-8 11-14 18-19l5-4c7-2 14-5 21-6z" class="B"></path><path d="M336 370c3 1 6 1 9 1 6 2 11 6 16 10 1 3 4 5 7 8h-1l1 1v1c-9-8-18-15-30-18-2-1-6-1-9-2 2-1 5 0 7-1z" class="I"></path><path d="M337 384c-1-3-1-5-1-7 19 3 35 21 45 36 3 6 7 12 8 19l-2-4c-1-2-1-3-2-5 0-1 0 0-1-1l-3-6c-2-3-4-6-7-10-2-3-5-5-7-8 0-1-1-2-3-3l-4-4-8-6c-1 0-2-1-3-1l-1-1h-4c-1 1-1 3-2 4-1 3-2 4-4 5 3-4 5-7 5-12h-1c-2-1-2-1-3-1-1 1-1 2-1 3l-1 2zm-56 17a46.21 46.21 0 0 1 17-17c7-5 19-9 27-7 2 1 4 3 6 5-3 0-5-1-7 1-2 0-4 0-6-1h-1v1l-1-1h-1v1h-1-1v1h-1c-1 0-1 0-2-1-6 2-13 5-18 9-4 3-7 6-11 9z" class="B"></path></svg>