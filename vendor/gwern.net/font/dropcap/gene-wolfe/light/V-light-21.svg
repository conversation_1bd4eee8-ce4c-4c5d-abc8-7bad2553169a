<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="119 97 799 812"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#dedddd}.C{fill:#787778}.D{fill:#747374}.E{fill:#d5d4d2}.F{fill:#4e4e4e}.G{fill:#a1a0a0}.H{fill:#e1e1e0}.I{fill:#c6c5c5}.J{fill:#bab9b9}.K{fill:#0a0a0a}.L{fill:#5d5c5d}.M{fill:#161616}.N{fill:#282828}.O{fill:#3f3f3f}.P{fill:#343435}.Q{fill:#151515}.R{fill:#f3f2f2}</style><path d="M706 450l18-1-14 6c1-1 2-2 3-2 1-1 1-1 2-1l4-2h-4l-1 1c-1 1-2 1-3 1s-2 1-3 1c-2 0-2 0-3-1 0-1 1-1 1-2zm26-57c0-3 1-6 3-9l16 1c-7 2-13 3-19 8z" class="M"></path><path d="M639 602l2 2c1 3-1 8-2 11-1 1-1 3-1 4 0 0 1 0 1 1 2 0 2-2 4-3v3l-1 2c-1 1-2 1-2 1-2 0-3-1-4-3v-2l3-16z" class="C"></path><path d="M336 500v-1c-1-1-2-2-4-2l-4-2c-6-2-12-1-19 0 6-2 11-4 17-4 2 0 4 0 6 1h1l1 1 2 7z" class="I"></path><defs><linearGradient id="A" x1="359.056" y1="617.065" x2="348.923" y2="611.36" xlink:href="#B"><stop offset="0" stop-color="#d3d2d1"></stop><stop offset="1" stop-color="#fefdfd"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M356 618c-2 0-3-1-4-1l-1-1h-2l-2-2 1-2h4 0c5 0 14 3 19 6h-1c-5 0-10-3-14 0z"></path><path d="M667 542l1 1c1-1 1-2 2-3h2v-1c1 1 1 1 1 2l-1 1 1 1c1 0 6-1 8 0 1 0 2 0 3 1h1c1 0 1 0 2 1v-1c1 0 3 1 4 1h1 1l1 2h0l-1 1h1l2 2-1 1c0-2-4-2-4-4h2l-1-1c-9-2-17 1-26 2l-1-1 2-5z" class="B"></path><path d="M853 208h24 0c-2 1-8 1-11 1l-21 1h-10c-2 0-4-1-5 0-1 0-1 0-1 1s1 2 2 3c2 1 6-1 8 1h3c1 0 3-1 4 0h4c1 0 3 0 5 1h3c-8 1-16 0-24-1-3 0-8 1-10-1l5-5c4-1 8-1 12-1h17-5z" class="I"></path><path d="M386 609l2 5c1 3 0 5 1 7s3 3 4 5h-1l-1-1c-1-1-3-2-4-3-5-4-9-9-14-13h9 4z" class="G"></path><path d="M816 223c2-3 5-6 8-9 2 2 7 1 10 1 8 1 16 2 24 1l16 1-28 1c-6 0-12 0-18 1-4 1-8 3-12 4z" class="D"></path><path d="M384 626c3 1 5 3 8 1h2c4 6 7 14 8 20-3-3-7-7-11-10-4-4-8-6-13-9 2-2 3-2 6-2z" class="P"></path><defs><linearGradient id="C" x1="383.295" y1="622.808" x2="370.705" y2="619.692" xlink:href="#B"><stop offset="0" stop-color="#7b7a7b"></stop><stop offset="1" stop-color="#a19f9f"></stop></linearGradient></defs><path fill="url(#C)" d="M356 618c4-3 9 0 14 0h1c8 2 14 7 21 9-3 2-5 0-8-1-3 0-4 0-6 2-1 0-2-1-2-1l-10-5-10-4z"></path><path d="M366 622c3-1 4-2 7-1l4 1c1 1 2 1 3 2h0c2 0 3 1 4 2-3 0-4 0-6 2-1 0-2-1-2-1l-10-5z" class="D"></path><defs><linearGradient id="D" x1="652.742" y1="633.966" x2="655.811" y2="626.119" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#5f5d5e"></stop></linearGradient></defs><path fill="url(#D)" d="M643 617l1 1c6 10 18 17 28 23-2 0-4-1-6-1-9-2-22-8-28-15-2-2-3-4-2-7v2c1 2 2 3 4 3 0 0 1 0 2-1l1-2v-3z"></path><defs><linearGradient id="E" x1="826.961" y1="244.678" x2="829.93" y2="231.797" xlink:href="#B"><stop offset="0" stop-color="#4d4b4c"></stop><stop offset="1" stop-color="#727071"></stop></linearGradient></defs><path fill="url(#E)" d="M810 231c2 1 4 3 7 4h7l13 1 41 1c-5 2-14 1-20 1l-33 1c-7 0-16 0-23 3 2-4 5-8 8-11z"></path><path d="M665 547l1 1c9-1 17-4 26-2l1 1h-2c0 2 4 2 4 4-6 1-11 2-16 2-7 1-12 2-18 3 1-2 2-7 4-9z" class="J"></path><path d="M803 340c-2 0-2 0-3-1-9-5-19-10-26-17-2-2-4-4-5-6-2-4 2-15 4-19v1c1 5-4 11-2 16 2 0 2 0 4-1l2-1 1 1c4 8 10 14 17 20 2 2 5 4 8 7z" class="F"></path><path d="M286 439c2 1 7-1 10-1h8c6 2 8 4 10 9l1 2h-1c-4-2-9-2-13-1 8 4 19 9 22 19v1c-2-2-1-2-2-4l-1-1-6-6c-2-2-4-3-5-4v-1l-1 1c-3 1-7 0-10 1l-5 1h-2-1 0l8-8h-1c-3-1-7-1-11-1l-6 1c1-3 3-6 6-8z" class="H"></path><defs><linearGradient id="F" x1="778.416" y1="287.396" x2="808.364" y2="263.064" xlink:href="#B"><stop offset="0" stop-color="#a8a7a7"></stop><stop offset="1" stop-color="#d4d4d3"></stop></linearGradient></defs><path fill="url(#F)" d="M825 239v1h-3-4-1-5 0c-1 1-1 0-2 1h-3-1c-3 1-6 5-7 8l-1 1-1 2c-1 1-1 1-1 2h-1v1l-3 6c-1 1-1 0-1 2-1 0-2 2-2 3v1h2c2 0 5 0 6 1 2 0 4-1 5 0 1 0 3 0 4 1 1 0 3-1 4 0h5v1c1 0 1 0 2 1l-1 1h-2c-1 0-1 0-2 1h-2c-1 0-1 0-2 1h-2-1l-4 2c-3 1-6 2-8 4h-1c-1 1-1 0-1 1-2 1-4 1-5 2-3 2-5 4-7 6-1 1-2 1-2 2 1-4 2-7 4-11 4-10 9-19 15-28l6-10c7-3 16-3 23-3z"></path><path d="M600 410l1-1c2 0 2-1 3-3 1 1 1 2 0 3 0 1-1 2-2 4h0c-1 1-1 1-1 2l-29 41c-2 1-2 1-3 3l-1 1c-2 2 0 0-1 2l-2 2c0 1-1 2-2 3s-2 1-2 3l-3 2 8-18c0-1 1-3 1-5l3-3h0 0v-1l9-21c3-5 5-10 6-14h1c0 2-1 3-2 5 2 1 4 3 6 4v1l1-1h2c0-1 1-1 1-2 1-1 1-1 2-3 0-1 1-1 2-2h0c1-1 2-1 2-2z" class="M"></path><path d="M355 568c3 1 12 1 15 4 3 4 5 11 7 16l9 21h-4-9-1c-1 0-1-1-2-1h-1-5 0c-1 0-3 1-4 0h0c-1 1-2 1-3 1h-2c0-1 1-3 2-4 0-1 1-2 1-4v-1c1-1 1-2 2-3v-1c0-1 1-2 1-3 1-1 1-2 1-3h0l1-1h-1c-1-1-3-1-4-1-8 0-14 1-21 2 1-2 3-4 5-6 4-3 6-6 9-9 1-2 3-4 4-6v-1z" class="B"></path><path d="M371 159h18 6c1 0 1 1 2 1h0v-1c2 0 17-1 18 0s1 1 1 2v-2h4 11 30 17c-5 3-10 5-16 8-16 9-32 20-43 36l-8 12c-2 0-2-1-3-2 0-1 0 0-1-1v-1l-1-1-2-3-5-7c-1-1-1-2-2-2 0-1 0-1-1-2s-1-2-2-3l-2-3c-1-1-2-3-3-4l-2-3-1-1c-1-2 0-1-1-2 0 0-1-1-1-2l-2-3c-1 0-1-1-2-2 0-1-1-1-2-2-1-3-2-5-4-7 0 0-1-1-1-2-1-1-2-2-2-3zm248 41c-15-19-36-30-57-41h67l-9 18v1l10-19h29v1h1s0-1 1-1c1-1 4 0 5 0h15c-1 1-2 2-3 4-2 1 0-1-1 1-1 1-1 1-2 3l-17 21-1 1c-1 1-1 2-1 3l-2 2c-1 0 0 0-1 1 0 1-1 2-2 3l-1 2c-1 1-1 0-1 1l-2 2c-2 2 1-1-1 1 0 1-1 1-1 2s-1 1-2 2c0 1-1 1-1 2l-1 1c-3 1-6 7-8 9l-1 1-7-12c2-4 5-7 7-11l12-17v-1l-20 27-5-7z" class="K"></path><path d="M408 213c1 1 1 2 3 2l8-12c2 0 3-1 6 1 4 3 9 7 15 9 10 4 23 3 33 3 0 1-1 1-1 2-25 15-45 40-51 69-1 6-2 13-2 19-1 3 0 7 0 10v5c0 2 1 4 0 6h0c-3-1-3-3-5-5-1-3-3-5-5-7 2 0 2 1 3 1 0-1 0-3-1-4-1-4-4-8-3-12 0 0-1-2-1-3l-3-8c-5-16-7-34-3-50h1l-1-2h0 1c2-7 4-14 8-20l-2-4z" class="E"></path><path d="M408 300l11 27c-3-1-3-3-5-5-1-3-3-5-5-7 2 0 2 1 3 1 0-1 0-3-1-4-1-4-4-8-3-12z" class="M"></path><path d="M619 200l5 7 20-27v1l-12 17c-2 4-5 7-7 11l7 12 1-1c2-2 5-8 8-9h0l-8 11 7 19h1s-1 1-1 2c0 2 1 5 1 7 0 5 1 9 1 14h0 0c1 3 0 5 0 8 0 4-3 8-3 12h0c1-1 2-2 3-2 3-2 5-5 7-7 1-1 2-3 3-4l2-2v1c-1 2-3 4-3 6 3-3 5-6 7-9l-2 8c-1 3-1 7-1 9v3-1c1 0 2-1 3-1l-13 14v1c-1 0-1 1-1 2-2 2-3 3-6 5-1 0-1 1-1 2-1 2-3 3-4 5-1 1-2 1-3 3l-4 4c0 1-1 1-1 2-1 0-1 0-1 1h-1c0 1-1 1-1 3v-1c-1-2 0-4 1-7 1-2 0-6 0-9-1-1 0-3 0-5 0-3-1-6-1-8-2-27-18-52-38-70-5-4-11-7-16-12 12 1 25 1 36-4 5-3 10-7 15-11z" class="E"></path><defs><linearGradient id="G" x1="632.825" y1="290.345" x2="652.396" y2="294.704" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#343334"></stop></linearGradient></defs><path fill="url(#G)" d="M642 264h0c1 3 0 5 0 8 0 4-3 8-3 12h0c1-1 2-2 3-2 3-2 5-5 7-7 1-1 2-3 3-4l2-2v1c-1 2-3 4-3 6 3-3 5-6 7-9l-2 8c-1 3-1 7-1 9v3-1c1 0 2-1 3-1l-13 14v1c-1 0-1 1-1 2-2 2-3 3-6 5-1 0-1 1-1 2-1 2-3 3-4 5-1 1-2 1-3 3l-4 4c0 1-1 1-1 2-1 0-1 0-1 1h-1c8-20 17-39 19-60z"></path><defs><linearGradient id="H" x1="407.508" y1="214.778" x2="330.921" y2="245.581" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#3d3c3c"></stop></linearGradient></defs><path fill="url(#H)" d="M294 159h77c0 1 1 2 2 3 0 1 1 2 1 2 2 2 3 4 4 7 1 1 2 1 2 2 1 1 1 2 2 2l2 3c0 1 1 2 1 2 1 1 0 0 1 2l1 1 2 3c1 1 2 3 3 4l2 3c1 1 1 2 2 3s1 1 1 2c1 0 1 1 2 2l5 7 2 3 1 1v1c1 1 1 0 1 1l2 4c-4 6-6 13-8 20h-1 0l1 2h-1c-4 16-2 34 3 50l3 8c0 1 1 3 1 3-1 4 2 8 3 12 1 1 1 3 1 4-1 0-1-1-3-1h0c-1-1-3-3-4-5l-12-16c-1-1-5-5-5-7l-4-4c-1-2-2-3-4-5l-6-8v7l-1-1-1 1c-1-3-4-6-6-8 2 1 3 2 6 2l2-1 2 2c0-1-1-2-1-3h0 0c0-2-2-8-4-10v-5l-5-22h0c2 1 3 4 5 6h1c-1-1-3-4-4-6 0 0 1 0 1-1-1-1-2-3-3-4s-3-3-4-5c0-1-1-2-2-3h0c-1-1-1-2-2-3l-1 1-27-39h-1c-1-2-2-3-2-4-1 0-1-1-2-1-2-2-11-2-14-2-1 1-5 1-6 1l-2-2h0-1l-1-1c0-1-2-2-2-4l-5-6z"></path><path d="M366 269c2 1 3 2 6 2l2-1 2 2 9 11c1 1 2 2 3 4l-4-4c-1-2-2-3-4-5l-6-8v7l-1-1-1 1c-1-3-4-6-6-8z" class="L"></path><path d="M327 174l2-1h0c2 2 8 2 10 3v-1h1c1 1 2 1 3 3l1 1 14 21h0c-1-1-2-3-3-4 0 3 0 5 1 7 0 2 0 3 1 5v1c1 4 3 6 3 10h0c-1-1-1-2-2-3l-1 1-27-39h-1c-1-2-2-3-2-4z" class="F"></path><defs><linearGradient id="I" x1="644.342" y1="202.056" x2="688.281" y2="225.502" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#I)" d="M681 159h24c7 0 14-1 22 0h4 11l-7 9c-1 2-2 3-4 4h-1-1-2l-1 1h-2c-2 0-2 0-3 1h-2l-2 1h0c-1 2-3 3-4 4 0 1 1 1 1 2h2c3-3 6-2 10-2h1v-1c1 0 2-1 3-1s2 0 3 1h-1c1 1 2 1 2 1 2 1 3 2 4 3 2 4 5 7 4 11s-4 8-6 10c-2 4-4 7-7 10l-5 4c-2 1-4 3-6 3h0c-2 2-5 3-7 2l-2-1c-2-1-5-3-6-5-1-1-2-2-2-4v-2-5l-13 19c-3 3-6 7-8 11l-2 2-2 2c-2 3-5 6-6 8-3 5-5 10-8 14l-1 1c0 1-1 2-2 3l-1 1h-1l-3 3h0l-2 2c-1 1-2 3-3 4-2 2-4 5-7 7-1 0-2 1-3 2h0c0-4 3-8 3-12 0-3 1-5 0-8h0 0c0-5-1-9-1-14 0-2-1-5-1-7 0-1 1-2 1-2h-1l-7-19 8-11h0l1-1c0-1 1-1 1-2 1-1 2-1 2-2s1-1 1-2c2-2-1 1 1-1l2-2c0-1 0 0 1-1l1-2c1-1 2-2 2-3 1-1 0-1 1-1l2-2c0-1 0-2 1-3l1-1 17-21c1-2 1-2 2-3 1-2-1 0 1-1 1-2 2-3 3-4z"></path><path d="M727 159h4 11l-7 9c-4 1-12 2-16 2l8-11z" class="Q"></path><defs><linearGradient id="J" x1="685.697" y1="201.947" x2="704.675" y2="212.049" xlink:href="#B"><stop offset="0" stop-color="#424141"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#J)" d="M676 239l-4 2h-1c1-2 0-4 1-6v-4c-2 3-4 6-6 8v1l8-13v-1l1-1 5-7 1-1v-1l5-15h-1l5-6c1-2 3-3 4-5 2-3 5-5 7-8 0-1 0-1 1-2 2-3 7-6 11-6h0l-1 2h0l2-2v2l3-3h0 1c-1 0 0 0-1 1v1h0c-1 2-3 3-4 4 0 1 1 1 1 2h2c3-3 6-2 10-2h1v-1c1 0 2-1 3-1s2 0 3 1h-1c1 1 2 1 2 1 2 1 3 2 4 3 2 4 5 7 4 11s-4 8-6 10c-2 4-4 7-7 10l-5 4c-2 1-4 3-6 3h0c-2 2-5 3-7 2l-2-1c-2-1-5-3-6-5-1-1-2-2-2-4v-2-5l-13 19c-3 3-6 7-8 11l-2 2-2 2z"></path><path d="M703 216v-5c-1-2 0-3 0-4v-2c1-1 1 0 1-2s1-3 2-5c0 3-1 5 0 8h0c0 2 0 3 1 4v1c-1 2 1 5 2 7 2 1 5 3 7 2h1 1 0 0c-2 2-5 3-7 2l-2-1c-2-1-5-3-6-5z" class="C"></path><defs><linearGradient id="K" x1="703.844" y1="196.55" x2="727.584" y2="186.774" xlink:href="#B"><stop offset="0" stop-color="#b0afad"></stop><stop offset="1" stop-color="#d3d1d3"></stop></linearGradient></defs><path fill="url(#K)" d="M727 179c0 2-3 4-4 6l-9 12c-1 0-2 2-3 3l-5 6h0c-1-3 0-5 0-8 5-9 11-15 20-19h1z"></path><path d="M730 177c1 0 2 0 3 1h-1c1 1 2 1 2 1 2 1 3 2 4 3 2 4 5 7 4 11s-4 8-6 10c-2 4-4 7-7 10l-5 4c-2 1-4 3-6 3h0-1-1c-2 1-5-1-7-2-1-2-3-5-2-7v-1c-1-1-1-2-1-4l5-6c1-1 2-3 3-3l9-12c1-2 4-4 4-6v-1c1 0 2-1 3-1z" class="K"></path><path d="M711 200h1c1-1 1-2 2-2l1-1s0-1 1-1 2-1 3-2c2-2 4-5 7-6l-6 8-10 11c-1 2-2 3-3 5v-1-1c-1-1-1-2-1-4l5-6z" class="L"></path><path d="M730 177c1 0 2 0 3 1h-1c1 1 2 1 2 1 2 1 3 2 4 3-2 0-4 0-5 1-3 0-5 3-7 5-3 1-5 4-7 6-1 1-2 2-3 2s-1 1-1 1l-1 1c-1 0-1 1-2 2h-1c1-1 2-3 3-3l9-12c1-2 4-4 4-6v-1c1 0 2-1 3-1z" class="D"></path><path d="M733 183c1-1 3-1 5-1 2 4 5 7 4 11s-4 8-6 10c-2 4-4 7-7 10l-5 4c-2 1-4 3-6 3h0-1-1c-2 1-5-1-7-2-1-2-3-5-2-7v1c1 2 2 3 4 4h3 1c4-3 23-26 24-29-2-3-3-3-6-4z" class="F"></path><defs><linearGradient id="L" x1="734.925" y1="204.664" x2="711.418" y2="219.714" xlink:href="#B"><stop offset="0" stop-color="#515051"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#L)" d="M714 216c2 0 3 0 5-2 1 0 1-1 2-1 4-2 7-6 10-9h5v-1c-2 4-4 7-7 10l-5 4c-2 1-4 3-6 3h0-1-1c-2 1-5-1-7-2-1-2-3-5-2-7v1c1 2 2 3 4 4h3z"></path><defs><linearGradient id="M" x1="580.298" y1="382.637" x2="653.444" y2="372.991" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#4e4e4e"></stop></linearGradient></defs><path fill="url(#M)" d="M678 237l2-2c2-4 5-8 8-11l13-19v5 2c0 2 1 3 2 4 1 2 4 4 6 5l2 1c2 1 5 0 7-2h0c2 0 4-2 6-3l5-4c3 0 5-3 8-5-2 2-4 5-5 7-3 4-8 7-11 11s-6 10-9 14l-1 1-5 7c0 1 0 1-1 2s-2 2-2 4c-2 2-4 5-4 7-1 2-2 3-2 5h0l1-1h1l-5 8-8 11c0 4-3 8-2 12v1c-1 1-2 2-2 3-1 1-2 1-2 2-1 1-2 3-3 3-1 1-2 2-3 4s-3 4-4 6c1 0 2-2 4-3v1c-3 3-7 6-9 10l-1 1-3 6c-2 2-4 4-6 5 0 4-3 7-5 9v1c-2 1-3 3-5 4-1 1-2 2-3 5l-5 5-2 3c0 1-1 1-1 2l-3 3c-1 1-1 2-2 3l-10 14-9 11c0 1-1 2-1 2l-3 4-1 2-2 2-1 2c-1 1-2 1-2 3 0 1-1 1-2 2h0c-1 1-2 1-2 2-1 2-1 2-2 3 0 1-1 1-1 2h-2l-1 1v-1c-2-1-4-3-6-4 1-2 2-3 2-5h-1c1-3 4-5 4-8l1-1v-1l18-40c3-8 6-15 9-22 2-4 3-8 5-11 0-2 1-2 1-3h1c0-1 0-1 1-1 0-1 1-1 1-2l4-4c1-2 2-2 3-3 1-2 3-3 4-5 0-1 0-2 1-2 3-2 4-3 6-5 0-1 0-2 1-2v-1l13-14c-1 0-2 1-3 1v1-3c0-2 0-6 1-9l2-8c-2 3-4 6-7 9 0-2 2-4 3-6v-1h0l3-3h1l1-1c1-1 2-2 2-3l1-1c3-4 5-9 8-14 1-2 4-5 6-8l2-2z"></path><path d="M631 367h0v-1c0-1 0-1 1-2 0-1 1-1 1-2 1-2 3-4 4-6v3l-2 3c0 1-1 1-1 2l-3 3z" class="F"></path><defs><linearGradient id="N" x1="604.836" y1="356.998" x2="631.025" y2="364.462" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#N)" d="M635 335c-1 1-2 3-2 5l7-9c0 2-1 4-2 6 0 3 0 6-1 9-1 0-2 0-2 1l-1 1v2l-4 4c-3 3-6 7-9 11-1 1-2 2-2 3l-3 3c-2 2-5 5-7 8-1 1-2 2-3 4-2 2-5 4-6 7-1 1-3 1-3 3-1 2-3 3-4 5-1 1-2 2-3 2l18-40 7-7c1-2 4-4 5-6l3-3 2-2c1 0 1-1 2-1v-1l3-3 3-3v-1l1 1h1v1z"></path><path d="M633 334c1 2-3 8-4 10 0 0 0 1-1 1h0c0-1 0-1 1-3v-1l1-1 1-2c-2 1-2 2-3 3s-1 1-3 1c1 0 1-1 2-1v-1l3-3 3-3z" class="Q"></path><defs><linearGradient id="O" x1="657.072" y1="305.498" x2="679.438" y2="311.262" xlink:href="#B"><stop offset="0" stop-color="#6d6e6d"></stop><stop offset="1" stop-color="#b4b2b2"></stop></linearGradient></defs><path fill="url(#O)" d="M686 284c0 4-3 8-2 12v1c-1 1-2 2-2 3-1 1-2 1-2 2-1 1-2 3-3 3-1 1-2 2-3 4s-3 4-4 6c1 0 2-2 4-3v1c-3 3-7 6-9 10l-1 1-3 6c-2 2-4 4-6 5 0 4-3 7-5 9v1c-2 1-3 3-5 4-1 1-2 2-3 5l-5 5v-3c2-2 4-4 5-6 1-3 4-5 6-8 0-2 0-4 1-6h0c-1 1-2 2-4 3h-1c-1 1-1 2-2 3-1 2-2 3-3 4-2 2-6 7-9 8l4-4v-2l1-1c0-1 1-1 2-1 1-3 1-6 1-9 1-2 2-4 2-6l-7 9c0-2 1-4 2-5v-1h-1l-1-1c0-2 1-2 2-3h1l2-2-1-1 1-1 2-2h1 1c2-2 6-7 9-9h1c0-1 0-1 1-1v-1c2-5 9-9 13-13 3-2 5-5 7-7h0c-1 2-1 2-2 3-2 2-3 4-4 6-1 1-1 1-1 2l-2 5v1l1-1 1-1 1-1c0-1 1-1 2-2h0l1-1 1-1 2-2c0-1 0 0 1-1 0-1 0-1 1-2 2-3 5-6 7-9 1-2 2-3 4-5z"></path><defs><linearGradient id="P" x1="650.805" y1="326.6" x2="664.849" y2="329.713" xlink:href="#B"><stop offset="0" stop-color="#5e5d5d"></stop><stop offset="1" stop-color="#818081"></stop></linearGradient></defs><path fill="url(#P)" d="M644 339c2-3 6-6 9-9 7-7 13-14 21-21-1 2-3 4-4 6 1 0 2-2 4-3v1c-3 3-7 6-9 10l-1 1-3 6c-2 2-4 4-6 5 0 4-3 7-5 9v1c-2 1-3 3-5 4-1 1-2 2-3 5l-5 5v-3c2-2 4-4 5-6 1-3 4-5 6-8 0-2 0-4 1-6h0c-1 1-2 2-4 3h-1z"></path><path d="M645 349v-1c1-3 2-4 4-5l1 2c-2 1-3 3-5 4z" class="D"></path><path d="M649 343c2-2 4-5 6-8 0 4-3 7-5 9v1l-1-2z" class="C"></path><defs><linearGradient id="Q" x1="641.785" y1="321.627" x2="659.299" y2="326.137" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#Q)" d="M673 293h0c-1 2-1 2-2 3-2 2-3 4-4 6-1 1-1 1-1 2l-2 5v1l1-1 1-1 1-1c0-1 1-1 2-2-1 2-2 3-3 5l-17 23c-4 6-10 12-15 17v-2l1-1c0-1 1-1 2-1 1-3 1-6 1-9 1-2 2-4 2-6l-7 9c0-2 1-4 2-5v-1h-1l-1-1c0-2 1-2 2-3h1l2-2-1-1 1-1 2-2h1 1c2-2 6-7 9-9h1c0-1 0-1 1-1v-1c2-5 9-9 13-13 3-2 5-5 7-7z"></path><path d="M638 328h0c0 3-1 5-3 7v-1h-1l-1-1c0-2 1-2 2-3h1l2-2z" class="M"></path><defs><linearGradient id="R" x1="649.541" y1="291.37" x2="671.881" y2="293.124" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#6e6e6e"></stop></linearGradient></defs><path fill="url(#R)" d="M658 285v-1c3-1 5-5 8-7h1c0-1 1-2 2-3l1 1 3-3 1 1c0 1-2 2-2 3h1c0 1 0 1-1 2h-1l-2 6c1-1 1-2 3-2 1-1 1-1 2-1 1-1 3-4 4-5 1 2 0 2 0 4 0 1 0 1 1 2-1 2-1 4-1 5-1 2-4 4-5 6-2 2-4 5-7 7-4 4-11 8-13 13v1c-1 0-1 0-1 1h-1c-3 2-7 7-9 9h-1-1l-2 2-1 1 1 1-2 2h-1c-1 1-2 1-2 3v1l-3 3-3 3v1c-1 0-1 1-2 1l-2 2-3 3c-1 2-4 4-5 6l-7 7c3-8 6-15 9-22 2-4 3-8 5-11 0-2 1-2 1-3h1c0-1 0-1 1-1 0-1 1-1 1-2l4-4c1-2 2-2 3-3 1-2 3-3 4-5 0-1 0-2 1-2 3-2 4-3 6-5 0-1 0-2 1-2v-1l13-14z"></path><defs><linearGradient id="S" x1="655.982" y1="291.505" x2="675.581" y2="295.195" xlink:href="#B"><stop offset="0" stop-color="#4e4d4d"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#S)" d="M678 276c1 2 0 2 0 4 0 1 0 1 1 2-1 2-1 4-1 5-1 2-4 4-5 6-2 2-4 5-7 7-4 4-11 8-13 13v1c-1 0-1 0-1 1h-1c-3 2-7 7-9 9h-1-1c1-2 3-3 4-5 6-6 11-15 17-22l13-16c1-1 3-4 4-5z"></path><defs><linearGradient id="T" x1="622.872" y1="321.517" x2="641.542" y2="328.37" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#313030"></stop></linearGradient></defs><path fill="url(#T)" d="M644 302c1-2 2-3 3-4 2-1 3-2 4-3v2c-3 2-5 6-7 9v1c2-2 4-5 6-7 0 2 0 3-1 4v2c0 2-1 6-2 8s-3 2-3 5c-1 2-3 3-4 5l-2 2-1 1 1 1-2 2h-1c-1 1-2 1-2 3v1l-3 3-3 3v1c-1 0-1 1-2 1l-2 2-3 3c-1 2-4 4-5 6l-7 7c3-8 6-15 9-22 2-4 3-8 5-11 0-2 1-2 1-3h1c0-1 0-1 1-1 0-1 1-1 1-2l4-4c1-2 2-2 3-3 1-2 3-3 4-5 0-1 0-2 1-2 3-2 4-3 6-5z"></path><defs><linearGradient id="U" x1="671.487" y1="250.366" x2="707.536" y2="266.574" xlink:href="#B"><stop offset="0" stop-color="#575757"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#U)" d="M678 237l2-2c2-4 5-8 8-11l13-19v5 2c0 2 1 3 2 4 1 2 4 4 6 5l2 1c2 1 5 0 7-2h0c2 0 4-2 6-3l5-4c3 0 5-3 8-5-2 2-4 5-5 7-3 4-8 7-11 11s-6 10-9 14l-1 1-5 7c0 1 0 1-1 2s-2 2-2 4c-2 2-4 5-4 7-1 2-2 3-2 5h0l1-1h1l-5 8-8 11c-2 2-3 3-4 5-2 3-5 6-7 9-1 1-1 1-1 2-1 1-1 0-1 1l-2 2-1 1-1 1h0c-1 1-2 1-2 2l-1 1-1 1-1 1v-1l2-5c0-1 0-1 1-2 1-2 2-4 4-6 1-1 1-1 2-3h0c1-2 4-4 5-6 0-1 0-3 1-5-1-1-1-1-1-2 0-2 1-2 0-4-1 1-3 4-4 5-1 0-1 0-2 1-2 0-2 1-3 2l2-6h1c1-1 1-1 1-2h-1c0-1 2-2 2-3l-1-1-3 3-1-1c-1 1-2 2-2 3h-1c-3 2-5 6-8 7v1c-1 0-2 1-3 1v1-3c0-2 0-6 1-9l2-8c-2 3-4 6-7 9 0-2 2-4 3-6v-1h0l3-3h1l1-1c1-1 2-2 2-3l1-1c3-4 5-9 8-14 1-2 4-5 6-8l2-2z"></path><path d="M729 213c3 0 5-3 8-5-2 2-4 5-5 7-3 4-8 7-11 11s-6 10-9 14c0-1 0-2-1-3v-1l3-6h1c1-2 2-4 4-5 1 0 1 0 2-1h1c2-2 5-6 8-8h1v-1l-2 1h0v-2c-1 1-3 3-5 3h0l5-4z" class="E"></path><path d="M682 266v-1l6-6 1 1h0c1-2 1-2 3-4-3 5-6 8-8 12-2 3-4 6-6 8-1 1-3 4-4 5-1 0-1 0-2 1-2 0-2 1-3 2l2-6h1c1-1 1-1 1-2 1-2 2-2 3-4 2-2 4-4 6-5v-1z" class="C"></path><path d="M673 251c3-3 6-8 10-10 1-1 3-1 4-1l1 1s1 0 2 1h0 1c1 1 4 4 4 6h0c-2 0-3-1-4-1-1-1-2-2-3-2-3 0-4 0-5 2l-3 3-5 3-3 3h-1v-1l2-4z" class="G"></path><path d="M675 253c1-2 2-3 4-5 1-1 1-3 3-4 0 1 0 2 1 3l-3 3-5 3z" class="C"></path><path d="M683 247c1-2 2-2 5-2 1 0 2 1 3 2 1 0 2 1 4 1v3c0 2-2 4-3 5-2 2-2 2-3 4h0l-1-1-6 6v1h-1-2l-1 1c-2 0-3-1-4-1v-1c-1-1-3-2-3-3-1-2-1-5 0-7v1h1l3-3 5-3 3-3z" class="K"></path><path d="M675 253l5-3-7 9c0 2 0 2 1 3 1 0 2 1 3 1h2c0 1 0 1-1 2s-2 1-4 0c-1-1-3-2-3-3-1-2-1-5 0-7v1h1l3-3z" class="D"></path><path d="M691 247c1 0 2 1 4 1v3c0 2-2 4-3 5-2 2-2 2-3 4h0l-1-1-6 6v1h-1-2l-1 1c-2 0-3-1-4-1v-1c2 1 3 1 4 0s1-1 1-2h-2c1 0 2-1 3-1 3-3 5-7 8-10 1 0 2-2 2-3 1 0 1-2 1-2z" class="L"></path><defs><linearGradient id="V" x1="657.039" y1="270.067" x2="679.498" y2="275.041" xlink:href="#B"><stop offset="0" stop-color="#353435"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#V)" d="M676 239l2-2c-1 5-6 9-5 14l-2 4c-1 2-1 5 0 7 0 1 2 2 3 3v1c1 0 2 1 4 1l1-1h2 1v1c-2 1-4 3-6 5-1 2-2 2-3 4h-1c0-1 2-2 2-3l-1-1-3 3-1-1c-1 1-2 2-2 3h-1c-3 2-5 6-8 7v1c-1 0-2 1-3 1v1-3c0-2 0-6 1-9l2-8c-2 3-4 6-7 9 0-2 2-4 3-6v-1h0l3-3h1l1-1c1-1 2-2 2-3l1-1c3-4 5-9 8-14 1-2 4-5 6-8z"></path><path d="M676 239l2-2c-1 5-6 9-5 14l-2 4c-1 2-1 5 0 7 0 1 2 2 3 3v1h-1c-1-1-3-2-3-3-1-1 0 0-1-2h0v-2c0-1 0-3-1-3v-1c0-1 1-2 2-3v-5c1-2 4-5 6-8z" class="F"></path><defs><linearGradient id="W" x1="675.558" y1="277.013" x2="753.442" y2="258.487" xlink:href="#B"><stop offset="0" stop-color="#c5c2c0"></stop><stop offset="1" stop-color="#f2f3f6"></stop></linearGradient></defs><path fill="url(#W)" d="M743 159h1c3-1 9 0 13 0 2 0 4-1 6 0v1l1-1c2-1 12 0 15 0h2c-2 3-4 5-5 9h1c1-1 1-1 2-1h1c1 0 5-1 6 0h5c3-3 5-6 7-8-2 5-6 10-9 15l1-1s1 0 2-1h0c1 0 1-1 1-1 1-2 3-3 4-4 1-2 2-4 4-5l1 1-55 75-2-1-16 23 1 1c-2 4-6 7-6 10l-11 16-59 79-11 13-1-2-2 2v-1c0-2-1-2-2-3v-1l1-1h-1c1-2 2-3 3-4h1c1-1 2-1 2-3-1 0-1 0-1-1 1-2 2-4 3-5 0-1-1-2 0-4-2 4-5 7-7 10h0-1c-1 0-1 1-1 1-1 2-3 3-4 5h-2v-1l-3 3c0-1 1-2 1-4 1-1 1-2 2-3l3-3c0-1 1-1 1-2l2-3 5-5c1-3 2-4 3-5 2-1 3-3 5-4v-1c2-2 5-5 5-9 2-1 4-3 6-5l3-6 1-1c2-4 6-7 9-10v-1c-2 1-3 3-4 3 1-2 3-4 4-6s2-3 3-4c1 0 2-2 3-3 0-1 1-1 2-2 0-1 1-2 2-3v-1c-1-4 2-8 2-12l8-11 5-8h-1l-1 1h0c0-2 1-3 2-5 0-2 2-5 4-7 0-2 1-3 2-4s1-1 1-2l5-7 1-1c3-4 6-10 9-14s8-7 11-11c1-2 3-5 5-7-3 2-5 5-8 5 3-3 5-6 7-10 2-2 5-6 6-10s-2-7-4-11c-1-1-2-2-4-3 0 0-1 0-2-1h1c-1-1-2-1-3-1s-2 1-3 1v1h-1c-4 0-7-1-10 2h-2c0-1-1-1-1-2 1-1 3-2 4-4h0l2-1h2c1-1 1-1 3-1h2l1-1h2 1 1c2-1 3-2 4-4l7-9h1z"></path><path d="M729 260l1 1c-2 4-6 7-6 10l-11 16v-1c-2 3-4 5-6 8l-1-1c1-2 3-3 3-6l20-27zm60-86l1-1s1 0 2-1h0c1 0 1-1 1-1 1-2 3-3 4-4 1-2 2-4 4-5l1 1-55 75-2-1c1-2 3-4 4-6 2-2 3-5 5-7 2-4 5-6 8-10l19-27c1-1 2-2 2-3h0l-28 36c1-2 4-5 6-8l19-27c3-4 6-8 9-11z" class="P"></path><path d="M692 307l19-26h1l-18 25 1 1 14-20c0 3-2 4-3 6l1 1c2-3 4-5 6-8v1l-59 79c-1-3 3-4 4-7l-1-1c0 1-1 2-2 3h0v-1l8-10 25-36v-1l4-6z" class="N"></path><defs><linearGradient id="X" x1="677.664" y1="283.113" x2="714.843" y2="283.38" xlink:href="#B"><stop offset="0" stop-color="#acaaa7"></stop><stop offset="1" stop-color="#d5d5d7"></stop></linearGradient></defs><path fill="url(#X)" d="M722 248c-1 2-3 4-4 7l-3 3v2l1 1h0l3-4c1-1 1-2 3-2-1 2-1 2-2 3l-1 1c0 1-1 1-1 2l-1 2c-1 0-1 1-2 2-1 2-3 3-4 5v1l-1 1c-1 1-1 2-2 3s-1 1-1 2c-1 0-1 1-2 2l-3 4-2 2-1 2c-1 1-1 2-2 2l-10 15c-1 1-1 2-1 2l-1 1-2-1-1 2h0c-3 1-4 3-6 5 0-2 2-4 2-6l-2 1c0-1 1-2 1-3 1 0 2-2 3-3 0-1 1-1 2-2 0-1 1-2 2-3s1-2 2-2l1-2c1-1 1-2 2-2l1-1h0c1-2 2-3 3-4l8-12 1-1c2-2 4-4 5-6l3-3c1-2 2-3 3-4 0-1 1-2 2-3 1-2 2-3 3-4 0 0 1-1 1-2h1c0-1 1-2 2-3z"></path><path d="M791 167c3-3 5-6 7-8-2 5-6 10-9 15-3 3-6 7-9 11l-19 27c-2 3-5 6-6 8 0 1-1 2-1 3-1 0-1 1-2 2 0 1-1 1-1 2s-1 1-1 2l-1-1h-1v-1l-2 2c-1 2-3 3-4 5-1 1-1 0-1 1l-1 1c0-1 1-1 1-3h0c1-5 5-7 5-12l2-4 4-5c1-1 1-2 2-3l1-1 3-5c1-1 1-2 2-3l1-1v-1l1-1 2-2 3-5 2-3c2-2 3-4 5-6 0-1 0-1 1-1l2-2c1-1 2-2 4-2l2-1c1 0 2 0 2-1h1c2-2 4-4 5-7z" class="R"></path><defs><linearGradient id="Y" x1="632.75" y1="354.922" x2="676.209" y2="341.941" xlink:href="#B"><stop offset="0" stop-color="#575956"></stop><stop offset="1" stop-color="#a09ca0"></stop></linearGradient></defs><path fill="url(#Y)" d="M674 309c1-2 2-3 3-4 0 1-1 2-1 3l2-1c0 2-2 4-2 6 2-2 3-4 6-5h0l1-2 2 1 1-1v1c0 2-2 5-3 6s-1 2-2 3c3-2 5-4 7-6l3-3h1 0l-4 6v1l-25 36-8 10v1h0c1-1 2-2 2-3l1 1c-1 3-5 4-4 7l-11 13-1-2-2 2v-1c0-2-1-2-2-3v-1l1-1h-1c1-2 2-3 3-4h1c1-1 2-1 2-3-1 0-1 0-1-1 1-2 2-4 3-5 0-1-1-2 0-4-2 4-5 7-7 10h0-1c-1 0-1 1-1 1-1 2-3 3-4 5h-2v-1l-3 3c0-1 1-2 1-4 1-1 1-2 2-3l3-3c0-1 1-1 1-2l2-3 5-5c1-3 2-4 3-5 2-1 3-3 5-4v-1c2-2 5-5 5-9 2-1 4-3 6-5l3-6 1-1c2-4 6-7 9-10v-1c-2 1-3 3-4 3 1-2 3-4 4-6z"></path><path d="M639 373c0-1 1-2 1-2l1 1v-1c1-1 1 0 2 0l5-5h0l1-2c0 3-5 7-4 9-1 1-2 3-3 4l-2 2v-1c0-2-1-2-2-3v-1l1-1z" class="L"></path><path d="M655 360v1h0c1-1 2-2 2-3l1 1c-1 3-5 4-4 7l-11 13-1-2c1-1 2-3 3-4s8-11 10-13z" class="P"></path><path d="M674 309c1-2 2-3 3-4 0 1-1 2-1 3l2-1c0 2-2 4-2 6 2-2 3-4 6-5-4 4-7 8-9 12-1 2-1 3-3 4-1 1-2 2-2 3l-2 2-2 2c-1 1-2 2-2 4l-1 1v-2l-2 1c1 0 1-1 1-1h-1c1-1 1-2 2-3v-1l3-6 1-1c2-4 6-7 9-10v-1c-2 1-3 3-4 3 1-2 3-4 4-6z" class="G"></path><defs><linearGradient id="Z" x1="665.676" y1="327.415" x2="685.414" y2="320.335" xlink:href="#B"><stop offset="0" stop-color="#999897"></stop><stop offset="1" stop-color="#b8b6b8"></stop></linearGradient></defs><path fill="url(#Z)" d="M686 306v1c0 2-2 5-3 6s-1 2-2 3c3-2 5-4 7-6l3-3h1 0l-4 6v1l-25 36v-1c-1-2 2-5 3-6l4-5 3-4v-1h0l-1-1-2 2h-2l-1-2c1-3 3-5 5-7s3-5 5-7l1-2s1-1 1-2l1-2c1-1 2-1 2-2l3-3 1-1z"></path><defs><linearGradient id="a" x1="637.65" y1="349.362" x2="656.373" y2="353.532" xlink:href="#B"><stop offset="0" stop-color="#575656"></stop><stop offset="1" stop-color="#929192"></stop></linearGradient></defs><path fill="url(#a)" d="M661 330v1c-1 1-1 2-2 3h1s0 1-1 1l2-1v2c-1 2-3 4-4 7l-1 1c-3 4-6 8-8 12h-1c1-4 4-6 6-9h0-1c-2 3-4 4-5 7l-1 2c-2 4-5 7-7 10h0-1c-1 0-1 1-1 1-1 2-3 3-4 5h-2v-1l-3 3c0-1 1-2 1-4 1-1 1-2 2-3l3-3c0-1 1-1 1-2l2-3 5-5c1-3 2-4 3-5 2-1 3-3 5-4v-1c2-2 5-5 5-9 2-1 4-3 6-5z"></path><defs><linearGradient id="b" x1="704.021" y1="230.239" x2="743.79" y2="243.69" xlink:href="#B"><stop offset="0" stop-color="#b1b0b0"></stop><stop offset="1" stop-color="#edeceb"></stop></linearGradient></defs><path fill="url(#b)" d="M743 159h1c3-1 9 0 13 0 2 0 4-1 6 0v1l1-1c2-1 12 0 15 0h2c-2 3-4 5-5 9h1c1-1 1-1 2-1h1c1 0 5-1 6 0h5c-1 3-3 5-5 7h-1c0 1-1 1-2 1l-2 1c-2 0-3 1-4 2l-2 2c-1 0-1 0-1 1-2 2-3 4-5 6l-2 3-3 5-2 2-1 1v1l-1 1c-1 1-1 2-2 3l-3 5-1 1c-1 1-1 2-2 3l-4 5-2 4-1 1-1 1c0 1-1 2-1 2-1 1-1 1-1 2-1 0-1 1-2 2v1h-1c0 1-1 2-1 3l-1 1-10 14-2 4c-1 1-3 2-3 3-2 0-2 1-3 2l-3 4h0l-1-1v-2l3-3c1-3 3-5 4-7-1 1-2 2-2 3h-1c0 1-1 2-1 2-1 1-2 2-3 4-1 1-2 2-2 3-1 1-2 2-3 4l-3 3c-1 2-3 4-5 6l-1 1-8 12c-1 1-2 2-3 4h0l-1 1c-1 0-1 1-2 2l-1 2c-1 0-1 1-2 2v-1c-1-4 2-8 2-12l8-11 5-8h-1l-1 1h0c0-2 1-3 2-5 0-2 2-5 4-7 0-2 1-3 2-4s1-1 1-2l5-7 1-1c3-4 6-10 9-14s8-7 11-11c1-2 3-5 5-7-3 2-5 5-8 5 3-3 5-6 7-10 2-2 5-6 6-10s-2-7-4-11c-1-1-2-2-4-3 0 0-1 0-2-1h1c-1-1-2-1-3-1s-2 1-3 1v1h-1c-4 0-7-1-10 2h-2c0-1-1-1-1-2 1-1 3-2 4-4h0l2-1h2c1-1 1-1 3-1h2l1-1h2 1 1c2-1 3-2 4-4l7-9h1z"></path><path d="M721 226h1c1-1 3-2 4-3h0c-1 1-2 3-3 3l1 1 4-6 1 1-8 12-12 17c-1 1-1 2-2 3l-1 1v1l-3 3-1 2-3 4h-1l-1 1h0c0-2 1-3 2-5 0-2 2-5 4-7 0-2 1-3 2-4s1-1 1-2l5-7 1-1c3-4 6-10 9-14z" class="J"></path><defs><linearGradient id="c" x1="730.195" y1="190.711" x2="742.499" y2="199.103" xlink:href="#B"><stop offset="0" stop-color="#6f6f6f"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#c)" d="M754 173c1-1 1-2 2-3-1 1-1 2-2 3 1 1 6 1 8 1 1 0 1-1 2-1 3 0 6 1 8-2 0 2-1 3-2 4v1l-2 2c-3 0-5 1-8 3l-1-1c-3 2-5 5-7 6 0-1 0-2 1-2l-1-1c-2 2-2 3-2 5 0 1-1 1-1 2-1 1-1 1-2 1v3c-1 3-3 6-5 9-2 4-6 9-10 12 1-2 3-5 5-7-3 2-5 5-8 5 3-3 5-6 7-10 2-2 5-6 6-10s-2-7-4-11c-1-1-2-2-4-3 0 0-1 0-2-1h1c-1-1-2-1-3-1h3c5-1 11-1 16-2 3 0 3 0 5-2z"></path><path d="M747 194c0-2 0-4-1-6h0l-1-2c0-2-1-2-1-3l1-1c1-1 4-2 6-2v2c1 0 1-1 2-2s2-1 4-1c2-1 3-1 5-2s4-1 6-2c1 0 1 0 2 1l-2 2c-3 0-5 1-8 3l-1-1c-3 2-5 5-7 6 0-1 0-2 1-2l-1-1c-2 2-2 3-2 5 0 1-1 1-1 2-1 1-1 1-2 1v3z" class="J"></path><path d="M763 186c1-1 2-2 3-4l7-8c2 0 4 1 6 1s4-1 7-1h-1c0 1-1 1-2 1l-2 1c-2 0-3 1-4 2l-2 2c-1 0-1 0-1 1-2 2-3 4-5 6l-2 3-3 5-2 2-1 1v1l-1 1c-1 1-1 2-2 3l-3 5-1 1c-1 1-1 2-2 3l-4 5-2 4-1 1-1 1c0 1-1 2-1 2-1 1-1 1-1 2-1 0-1 1-2 2v1h-1c0 1-1 2-1 3l-1 1-10 14-2 4c-1 1-3 2-3 3-2 0-2 1-3 2l-3 4h0l-1-1v-2l3-3c1-3 3-5 4-7 0-1 0-1 1-2l1-1c1-1 1-3 2-4l3-4c1-2 2-3 3-4h0l1-1c1-2 1-3 2-4h1v-1c1-1 1-2 2-2 0-1 0-1 1-2s1-2 2-3l3-4c1-1 1-2 2-3l3-5h0v-1l2-3c1-1 1-2 2-3l7-11 1-1c0-2 1-2 2-3z" class="H"></path><path d="M743 159h1c3-1 9 0 13 0 2 0 4-1 6 0v1l1-1c2-1 12 0 15 0h2c-2 3-4 5-5 9h1c1-1 1-1 2-1h1c1 0 5-1 6 0h5c-1 3-3 5-5 7-3 0-5 1-7 1s-4-1-6-1l-7 8c-1 2-2 3-3 4 1-3 3-5 5-8l2-2v-1c1-1 2-2 2-4-2 3-5 2-8 2-1 0-1 1-2 1-2 0-7 0-8-1 1-1 1-2 2-3-1 1-1 2-2 3-2 2-2 2-5 2-5 1-11 1-16 2h-3c-1 0-2 1-3 1v1h-1c-4 0-7-1-10 2h-2c0-1-1-1-1-2 1-1 3-2 4-4h0l2-1h2c1-1 1-1 3-1h2l1-1h2 1 1c2-1 3-2 4-4l7-9h1z" class="F"></path><path d="M714 181c1-1 1-2 2-2 3-2 11-5 15-4 2 0 5 0 8-1l15-1c-2 2-2 2-5 2-5 1-11 1-16 2h-3c-1 0-2 1-3 1v1h-1c-4 0-7-1-10 2h-2z" class="L"></path><path d="M743 159h1c3-1 9 0 13 0 2 0 4-1 6 0v1l1-1c2-1 12 0 15 0v1c-1 2-4 6-6 8-1 0-1 0-2 1-3 0-8-1-9 0-2 0-2 0-3 1h-2c1-2 3-5 4-7h-1c-1 1-2 4-4 5 0 1-4 1-5 1l-16 1 8-11z" class="Q"></path><path d="M234 343h-1c-1-1-1-2-2-2l-2-5c0-2-1-3-1-4-1-2-2-3-2-5-1-2-2-5-4-7v-1l-10-15c-16-22-38-39-58-57l-6-6c-1-1-3-3-4-3h-1-1l-1-1h2v-1c-4-3-7-6-10-9l-19-20h13 26 18c3 0 6 0 9 1 4 2 10-1 13 1 2 1 4 4 5 6 3 2 5 5 7 8 3 3 6 7 9 11l3 6c4 6 8 11 11 17 2 3 3 6 5 8 1 4 3 7 4 11 2 2 3 4 4 7h0l5 11 5 12 2 7 4 9c1 1 1 2 1 4-1 0-1 0-1 1l-1 1c-1 2-4 3-7 5-1 1-2 1-4 1-3 3-9 5-12 8l1 1z" class="E"></path><path d="M206 264c-1 0-4-1-4-1v-1-1l-1-1 5-1v5z" class="I"></path><path d="M241 283h0c-2 2-4 5-5 8h0l-6-1 1-1c2-1 3-3 4-4 2-1 4-1 6-2z" class="C"></path><path d="M230 290c-3 0-10 0-12-1l17-4c-1 1-2 3-4 4l-1 1zm-24-31h11c-2 0-3 1-4 1h0l1 5c-3 0-5 0-8-1v-5z" class="J"></path><defs><linearGradient id="d" x1="244.385" y1="289.889" x2="238.621" y2="288.839" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#d)" d="M241 283l5 11c-3-1-6-2-10-3h0c1-3 3-6 5-8z"></path><path d="M251 306l2 7-2-1c-6-1-12-2-18-2l-1-1c5-1 12-4 17-3h2z" class="J"></path><path d="M251 306l2 7-2-1-2-2c-1-1-1-2-1-4h1 2zm-6 28c1-1 3-2 4-3 2-1 4-3 6-5l-19-1 6-2c5-1 10 0 15-1h0c1 1 1 2 1 4-1 0-1 0-1 1l-1 1c-1 2-4 3-7 5-1 1-2 1-4 1z" class="G"></path><path d="M198 215c3 2 5 5 7 8-10-4-19-5-29-5l-23-1 45-2z" class="C"></path><path d="M214 234l3 6c-9-1-18-1-27-2l-33-1c7-1 14 0 20-1l37-2z" class="L"></path><defs><linearGradient id="e" x1="236.256" y1="266.624" x2="218.502" y2="262.828" xlink:href="#B"><stop offset="0" stop-color="#343334"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#e)" d="M228 257c2 3 3 6 5 8 1 4 3 7 4 11-7-6-15-9-23-11l-1-5h0c1 0 2-1 4-1 3 0 8-1 11-2z"></path><path d="M781 159h128c-8 3-15 5-23 9-20 9-39 23-55 40h17 5 5-17c-4 0-8 0-12 1l-5 5c-3 3-6 6-8 9l-6 8c-3 3-6 7-8 11l-6 10c-6 9-11 18-15 28-2 4-3 7-4 11-1 2-2 4-4 6h0c-2 4-6 15-4 19 1 2 3 4 5 6 7 7 17 12 26 17 1 1 1 1 3 1l7 5c-15-5-27-8-42-6-4 1-8 2-11 4-2 0-4 1-5 3-2 3-3 7-5 11-4 9-7 19-12 27-2 3-3 6-3 9-1 2-1 3-1 5 2 2 6 4 9 5l26 12c-13-2-29-4-41 5-2 1-3 3-4 4 7 5 13 10 20 15 2 2 6 5 8 8-9-4-23-12-34-8-5 3-6 7-9 11 0 1-1 1-1 2 1 1 1 1 3 1 1 0 2-1 3-1s2 0 3-1l1-1h4l-4 2c-1 0-1 0-2 1-1 0-2 1-3 2-3 2-5 3-7 6-3 3-5 10-7 15 6 3 12 4 18 6-6 0-15-1-21 1l-5 9v1c-8 3-12 10-20 11-3-1-6-3-9-4-1-1-1-1-3-2h0c-1-1-1-2-1-4 0-3 1-7 0-10v-1c-1-3-2-6-2-8-1-4 25-56 29-65l72-158-1-2 13-27c1-2 1-4 2-7 1-2 3-4 3-6h0l-4 6h-1 0v-1c1-2 2-3 3-5l10-12c3-4 6-9 10-13 1-2 3-3 4-5 2-2 4-5 6-8 1-1 2-1 3-2 1-2 5-6 5-8-2-1-2-1-3-1l-2 2-1-1c-2 1-3 3-4 5-1 1-3 2-4 4 0 0 0 1-1 1h0c-1 1-2 1-2 1l-1 1c3-5 7-10 9-15-2 2-4 5-7 8h-5c-1-1-5 0-6 0h-1c-1 0-1 0-2 1h-1c1-4 3-6 5-9z" class="K"></path><path d="M802 191h1c1 0 2 0 3 2 0 0 1 1 0 2 0 2 0 2-2 3h-3c-2-2-2-2-2-4 1-2 1-2 3-3z" class="B"></path><path d="M717 368h1c2 0 2 0 2 2l-13 13h0 1l-1 1c-2 1-2 0-3 0v-2c4-6 8-10 13-14z" class="E"></path><path d="M820 174c2 0 3 0 4 1s1 3 1 5-1 3-2 4c-2 1-4 2-6 1h-1c-1 0-2-1-3-2h2 2c1-2 1-4 1-7 1 0 2-1 2-2h0z" class="B"></path><path d="M697 414h1c0 5 1 9 3 13l-6 4-2 1-1-1c0-1-1-4-1-5s3-4 4-6 1-4 2-6z" class="D"></path><path d="M761 274c1-1 9 4 11 5-4 2-8 3-12 5l-8 3 1-2c1-6 2-8 8-11z" class="F"></path><path d="M782 239l-13 14c1-4 4-8 6-12l13-26h1v6l2 10h0c-3 2-6 5-9 8z" class="I"></path><path d="M782 239c-1-4 4-8 5-12 1-2 1-4 1-6l-1-1c1-1 0-1 1-2l1 3 2 10h0c-3 2-6 5-9 8z" class="B"></path><path d="M674 437c3 0 4 3 6 4l2 2c0 1 0 1-1 2l-14 28-3 5 6-23c1-6 2-13 4-18z" class="P"></path><path d="M766 216h0v-1c1-2 2-3 3-5l10-12c3-4 6-9 10-13 1-2 3-3 4-5 2-2 4-5 6-8 1-1 2-1 3-2-9 13-19 25-27 38-4 6-6 13-9 19l-12 25-1-2 13-27c1-2 1-4 2-7 1-2 3-4 3-6h0l-4 6h-1z" class="G"></path><path d="M180 208h12v-1c-16-16-36-31-57-40-7-3-14-5-21-8h133l7 10v1c-4-1-7-1-10 1-2-4-5-8-9-10h-1c-1 0-1 1-2 1h-1-1l1 2c1 1 1 1 1 2-1-2-3-5-5-6-2 1-2 1-3 2 1 4 6 11 10 14 2 1 18 25 20 29l21 43 35 76 41 89 21 45c1 1 3 5 3 6l25 55 7 17h0l26 57 13 28 4 9c1 1 2 3 2 4l29 63 8 18c0 1 2 4 2 6 2 3 3 7 5 11l9 20 7 19-1 2 1 1v7c1 1 0 2 1 3v-1h3 4c1-1 2-1 2-2s1-2 1-3l1 1 3-28h0v-9c1-3 3-5 4-8l1 1c3 6 9 13 11 19 1 3 3 6 5 8 0 2 1 2 2 3h0v-46c0-6 1-14 0-20 0-4 1-7 3-11 1-3 5-14 7-15l1-1v-1c1-1 1-1 1-2 1-2 2-4 3-7 2-2 3-7 4-9l9-19c0 7 0 15 1 22v1c1-5 1-10 1-15 0-4-2-9 0-13h1c1-1 1-4 2-5l5-12 8-18v8c-1 5-1 10 1 14v-10c0-2 0-3 1-4 0-1 3-1 4-2 2 0 5 0 7 1 4 1 7 3 10 4l2-1c1-1 2-1 3-2v-1c1-4-1-9-2-13v-12c0-4 0-8 1-12v-1c0-3 2-6 3-10l3-6c6-8 14-15 23-21l3-2 3-2h-2c0-2-1-5 0-7 2-5 0-11 3-15 3 1 6 3 9 4 8-1 12-8 20-11v-1c0 4 0 6 2 9h2l8-1-13 7c-2 1-5 2-6 4-2 1-4 7-4 9l-1 1h1c7-1 14 0 21 0-7 2-15 4-22 8-2 1-4 2-5 4l-4 9-2 5c-2 2-3 7-4 9l-13 29c-3 5-5 12-9 17-1 6-2 11-3 16-1 3 0 5 2 7 6 7 19 13 28 15 2 0 4 1 6 1l2 1h0c-15-1-31-1-45 5-4 1-8 3-11 5-1 2-2 3-2 5l-12 25-34 81-59 133-32-76-43-100-20-45c-3-7-6-16-10-23-1-2-2-3-4-5-1-6-4-14-8-20l-1-1c-1-2-3-3-4-5s0-4-1-7l-2-5-9-21c-2-5-4-12-7-16-3-3-12-3-15-4-13 0-25 3-38 7 1-1 4-3 6-4 6-3 23-14 25-20 0-1 1-2 2-4v-1l1-1v-4c-3-3-6-5-10-6-6-3-12-6-19-8 6 0 14 0 20-3 1-1 2-1 2-3 0-1 0-2-1-3-4-8-18-9-26-11h6c3 0 13 0 15-2 0-2-1-4-2-5l-2-7c-2-4-3-10-6-13-5-4-17-2-22-2 1 0 3-1 5-2l9-4c1 0 3-1 4-2l-1-2v-1c-3-10-14-15-22-19 4-1 9-1 13 1h1l-1-2c-2-5-4-7-10-9h-8c-3 0-8 2-10 1 0-1 1-3 2-4 3-5 3-9 3-15-11-3-21-3-32-2 3-2 5-2 8-3 6-3 14-7 19-12l3-3v-1l1-1c-1-4-2-7-5-10-2-4-7-6-11-8l9 3-2-7c-2-3-4-4-6-5-7-4-13-6-20-8 5-1 11 0 16-2h1 2c-1-6-4-12-6-18-11-4-24-3-34 0l-1-1c3-3 9-5 12-8 2 0 3 0 4-1 3-2 6-3 7-5l1-1c0-1 0-1 1-1 0-2 0-3-1-4l-4-9-2-7-5-12-5-11h0c-1-3-2-5-4-7-1-4-3-7-4-11-2-2-3-5-5-8-3-6-7-11-11-17l-3-6c-3-4-6-8-9-11-2-3-4-6-7-8-1-2-3-5-5-6-3-2-9 1-13-1z" class="K"></path><path d="M629 586l9 1v1c-1 0-2 0-2 1-3-1-5-2-7-3z" class="M"></path><path d="M337 439v-2c2 1 4 5 5 7-1 0-1 0-1-1l-2 1c-1-2-1-4-2-5z" class="D"></path><path d="M624 602v5c-2 0-3-1-5-2l2-1c1-1 2-1 3-2z" class="E"></path><path d="M225 217h0c2 3 4 7 4 11l-1-1c-1-3-3-5-5-7l-1-1v-1l3-1z" class="H"></path><path d="M339 444l2-1c0 1 0 1 1 1 3 5 9 12 9 18-5-5-9-13-12-18z" class="C"></path><path d="M198 169c2 1 3 8 4 10l1 1c-1 0-2-1-3-2h-7l5-9zm9 32l-1-2c-1-3-1-6 0-9 2-3 6-5 10-6l1 3c0 1 0 0-1 1-3 0-5 1-7 3-1 1-2 2-3 4 0 1 0 2 1 2v1c0 1 1 2 0 3z" class="B"></path><path d="M476 728c-1 5-4 9-6 13-1 3-2 6-4 7l-1-1c-1-1-2-1-2-2l13-17z" class="H"></path><path d="M337 439c1 1 1 3 2 5 3 5 7 13 12 18l3 4h0l-20-21 3-6zm-27-58v1c0 1-1 1-1 2 0 2 2 4 3 5l6 10 6 9c1 3 3 5 3 7l-22-31 5-3z" class="B"></path><path d="M251 239c0-1 0-3 1-4l18 36-3-2-11-19c-2-4-3-8-5-11z" class="G"></path><path d="M260 272c2 0 4 1 6 3l1 2h2c1 1 2 2 3 2l1-1h0l9 17-22-23z" class="I"></path><path d="M301 342c4 3 7 11 10 15 1 1 0 1 1 2v1c1 1 1 1 1 2v1l-14-13c-1-2-3-3-4-5 2 0 4-1 5-2l1-1z" class="N"></path><path d="M248 257c2 1 3 2 6 4s6 5 9 8c2 3 4 5 7 7l-3-7 3 2 3 7-1 1c-1 0-2-1-3-2h-2l-1-2c-2-2-4-3-6-3l-4-4c-2-2-7-5-9-8 0-1 0-2 1-3zm241 482h1c0 2 1 3 1 5 1 6 1 13 2 20 0 3 1 6 1 9 1 6 1 12 3 17l-3 12-5-63z" class="B"></path><path d="M310 381l1-1 2 2c1 1 2 3 3 5 5 10 9 21 15 32 2 1 3 4 4 6h-1l-5-6c0-1-2-3-2-4 0-2-2-4-3-7l-6-9-6-10c-1-1-3-3-3-5 0-1 1-1 1-2v-1z" class="O"></path><path d="M286 331h0c2-1 4-4 6-4v1c0 1 2 3 3 4 2 3 3 6 6 9v1l-1 1c-1 1-3 2-5 2-4-4-8-8-11-12l2-2z" class="I"></path><path d="M286 331l6 6c2 1 5 1 6 4h0v1l2 1c-1 1-3 2-5 2-4-4-8-8-11-12l2-2zm-35-92c2 3 3 7 5 11l11 19 3 7c-3-2-5-4-7-7-3-3-6-6-9-8s-4-3-6-4c3-5 3-12 3-18z" class="R"></path><path d="M456 660c0-2 0-9 1-11l20 46c-2 2-3 4-5 6h0c-3-11-8-21-11-31-1-2-3-4-3-6s-1-2-2-4z" class="C"></path><path d="M291 420c6 1 12 4 15 10 1 1 2 3 2 4l1 2c-2 1-4 0-5 2h-8c-3 0-8 2-10 1 0-1 1-3 2-4 3-5 3-9 3-15z" class="E"></path><path d="M308 434l1 2c-2 1-4 0-5 2h-8c-3 0-8 2-10 1 3-1 6-3 9-4 4 0 11 1 13-1z" class="C"></path><defs><linearGradient id="f" x1="225.149" y1="190.157" x2="211.842" y2="203.311" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#f)" d="M216 184c4 1 6 2 9 5 2 3 3 6 2 9 0 3-2 7-5 8-1 1-5 2-8 2-3-1-6-3-7-6v-1c1-1 0-2 0-3v-1c-1 0-1-1-1-2 1-2 2-3 3-4 2-2 4-3 7-3 1-1 1 0 1-1l-1-3z"></path><path d="M207 198v-1c-1 0-1-1-1-2 1-2 2-3 3-4h2c-1 2-2 3-2 5 0 1 1 3 3 4s5 1 7 1v1c-2 2-3 3-6 3-1 0-2-1-3-2s-2-2-2-3v-1l-1-1z" class="J"></path><path d="M211 191l7-2c2 1 3 3 3 4 1 2 0 3 0 4s-2 3-2 4c-2 0-5 0-7-1s-3-3-3-4c0-2 1-3 2-5z" class="K"></path><defs><linearGradient id="g" x1="655.148" y1="510.656" x2="684.663" y2="509.741" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#5b5a5a"></stop></linearGradient></defs><path fill="url(#g)" d="M659 500c3 1 6 3 9 4 8-1 12-8 20-11v-1c0 4 0 6 2 9h2l-1 1-2 2c-3 2-6 3-9 4-8 4-15 9-22 14h-2c0-2-1-5 0-7 2-5 0-11 3-15z"></path><defs><linearGradient id="h" x1="522.919" y1="779.119" x2="508.201" y2="831.153" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#dedcdd"></stop></linearGradient></defs><path fill="url(#h)" d="M524 779l-5 44c-1 6-1 13-3 19l-6-75c1 2 1 4 1 6l1 1v7c1 1 0 2 1 3v-1h3 4c1-1 2-1 2-2s1-2 1-3l1 1z"></path><path d="M400 519l7 17h0c0 6 5 13 8 19 1 2 10 24 10 25 0 0-1 1-2 1 0 0-1 1-2 1-3-14-9-27-14-40l-3-9c-1-1-1-3-2-4-1 0-1-1-1-1l-2-6 1 13c1 2 1 24 1 28 0 1 1 1 2 2 2 2 5 7 6 10 0 2 2 4 3 6l1 2 1 1v1c-4 2-9 4-13 7-1 1-1 1-2 1v-13-1c-1-3 0-8 0-12v-28c0-5-1-11 0-17 0-1 0-2 1-3z" class="N"></path><path d="M400 535c1 2 1 24 1 28v19c0 3-1 7 0 10-1 1-1 1-2 1v-13c1-4 0-9 0-14l1-31z" class="I"></path><defs><linearGradient id="i" x1="408.913" y1="572.415" x2="395.781" y2="582.695" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#c3c1c2"></stop></linearGradient></defs><path fill="url(#i)" d="M401 563c0 1 1 1 2 2 2 2 5 7 6 10 0 2 2 4 3 6l1 2 1 1v1c-4 2-9 4-13 7-1-3 0-7 0-10v-19z"></path><defs><linearGradient id="j" x1="468.585" y1="685.489" x2="445.915" y2="703.011" xlink:href="#B"><stop offset="0" stop-color="#b9b8b8"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#j)" d="M456 660c1 2 2 2 2 4s2 4 3 6c3 10 8 20 11 31h0c-5 7-11 14-15 21l-1-62z"></path><defs><linearGradient id="k" x1="584.468" y1="622.526" x2="601.715" y2="627.769" xlink:href="#B"><stop offset="0" stop-color="#d1cfcf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#k)" d="M588 612l8-18v8c-1 5-1 10 1 14v45l-15-29-1-3c1-1 1-4 2-5l5-12z"></path><path d="M588 612c1 5-1 10-3 14-1 2-3 3-3 6l-1-3c1-1 1-4 2-5l5-12z" class="B"></path><defs><linearGradient id="l" x1="411.718" y1="547.249" x2="397.151" y2="557.009" xlink:href="#B"><stop offset="0" stop-color="#515050"></stop><stop offset="1" stop-color="#a2a1a1"></stop></linearGradient></defs><path fill="url(#l)" d="M400 535l-1-13 2 6s0 1 1 1c1 1 1 3 2 4l3 9c5 13 11 26 14 40-2 0-5 2-7 3v-1l-1-1-1-2c-1-2-3-4-3-6-1-3-4-8-6-10-1-1-2-1-2-2 0-4 0-26-1-28z"></path><defs><linearGradient id="m" x1="498.637" y1="756.573" x2="490.863" y2="784.427" xlink:href="#B"><stop offset="0" stop-color="#939292"></stop><stop offset="1" stop-color="#bfbdbe"></stop></linearGradient></defs><path fill="url(#m)" d="M488 723h-1c0-2 0-3 1-4 1 1 1 2 2 3v1c1 0 1 1 2 2l-1-2v-2h0c2 3 3 7 5 11l9 20c-1 2 0 4-1 6-2 4-2 9-3 13l-4 19c-2-5-2-11-3-17 0-3-1-6-1-9-1-7-1-14-2-20 0-2-1-3-1-5h-1v-4l-1-12z"></path><path d="M488 723h-1c0-2 0-3 1-4 1 1 1 2 2 3v1c1 0 1 1 2 2l-1-2v-2h0c2 3 3 7 5 11l9 20c-1 2 0 4-1 6-2-4-5-8-7-12-2-5-3-9-5-14-1-2-2-4-3-7 0-1 0-1-1-2z" class="Q"></path><defs><linearGradient id="n" x1="563.475" y1="665.132" x2="585.68" y2="672.02" xlink:href="#B"><stop offset="0" stop-color="#d9d7d7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#n)" d="M565 662c2-2 3-7 4-9l9-19c0 7 0 15 1 22v1 42c0 3 1 6 0 9l-19-35 1-1v-1c1-1 1-1 1-2 1-2 2-4 3-7z"></path><defs><linearGradient id="o" x1="378.258" y1="491.837" x2="356.398" y2="500.077" xlink:href="#B"><stop offset="0" stop-color="#686868"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#o)" d="M372 458c1 1 3 5 3 6-1 1-1 1-1 2-1 4 10 23 12 28 1 5 1 11 1 16h0l-20-4c-4-1-8-2-12-2h-1c6-6 11-13 14-22 1-2 2-5 2-8 1-2 1-3 1-4 1-4 1-8 1-12z"></path><defs><linearGradient id="p" x1="444.024" y1="647.219" x2="425.777" y2="657.164" xlink:href="#B"><stop offset="0" stop-color="#b7b6b5"></stop><stop offset="1" stop-color="#e1e0e0"></stop></linearGradient></defs><path fill="url(#p)" d="M433 593l13 28 4 9c1 1 2 3 2 4h0v4c0 3-1 5-2 7h-1c-2 2-3 6-5 8l-12 21v-71-1c0-2-1-7 1-9z"></path><path d="M433 593l13 28 4 9c1 1 2 3 2 4h0v4c0 3-1 5-2 7h-1c-3-10-6-19-11-29-1-4-4-8-5-13h0-1v-1c0-2-1-7 1-9z" class="N"></path><path d="M247 159c4-1 8 0 13 0 1 0 4-1 5 0h1 6 12 10l5 6c0 2 2 3 2 4l1 1h1 0l2 2c1 0 5 0 6-1 3 0 12 0 14 2 1 0 1 1 2 1 0 1 1 2 2 4h1l27 39 1-1c1 1 1 2 2 3h0c1 1 2 2 2 3 1 2 3 4 4 5s2 3 3 4c0 1-1 1-1 1 1 2 3 5 4 6h-1c-2-2-3-5-5-6h0l5 22v5c2 2 4 8 4 10h0 0c0 1 1 2 1 3l-2-2-2 1c-3 0-4-1-6-2 2 2 5 5 6 8l1-1 1 1v-7l6 8c2 2 3 3 4 5l4 4c0 2 4 6 5 7l12 16c1 2 3 4 4 5h0c2 2 4 4 5 7 2 2 2 4 5 5h0c5 13 11 26 17 39 0 2 2 5 2 7 4 6 6 13 8 19l24 54c3 6 5 13 8 19 2 5 5 9 6 14l8 19 6 12 12 30c3 6 7 12 8 18 2-1 5-9 6-12l6-10 7-18c2-4 4-8 5-12l4-6c1-5 4-9 6-14 1-2 2-4 2-6 0-3 2-5 4-7v-1l3-2c0-2 1-2 2-3s2-2 2-3l2-2c1-2-1 0 1-2l1-1c1-2 1-2 3-3l29-41c0-1 0-1 1-2h0c1-2 2-3 2-4 1-1 1-2 0-3-1 2-1 3-3 3l-1 1c0-2 1-2 2-3l1-2 2-2 1-2 3-4s1-1 1-2l9-11 10-14c0 2-1 3-1 4l3-3v1h2c1-2 3-3 4-5 0 0 0-1 1-1h1 0c2-3 5-6 7-10-1 2 0 3 0 4-1 1-2 3-3 5 0 1 0 1 1 1 0 2-1 2-2 3h-1c-1 1-2 2-3 4h1l-1 1v1c1 1 2 1 2 3v1l2-2 1 2 11-13 59-79 11-16c0-3 4-6 6-10l-1-1 16-23 2 1 55-75 2-2c1 0 1 0 3 1 0 2-4 6-5 8-1 1-2 1-3 2-2 3-4 6-6 8-1 2-3 3-4 5-4 4-7 9-10 13l-10 12c-1 2-2 3-3 5v1h0 1l4-6h0c0 2-2 4-3 6-1 3-1 5-2 7l-13 27 1 2-72 158c-4 9-30 61-29 65 0 2 1 5 2 8v1c1 3 0 7 0 10 0 2 0 3 1 4h0c2 1 2 1 3 2-3 4-1 10-3 15-1 2 0 5 0 7h2l-3 2-3 2c-9 6-17 13-23 21l-3 6c-1 4-3 7-3 10v1c-1 4-1 8-1 12v12c1 4 3 9 2 13v1c-1 1-2 1-3 2l-2 1c-3-1-6-3-10-4-2-1-5-1-7-1-1 1-4 1-4 2-1 1-1 2-1 4v10c-2-4-2-9-1-14v-8l-8 18-5 12c-1 1-1 4-2 5h-1c-2 4 0 9 0 13 0 5 0 10-1 15v-1c-1-7-1-15-1-22l-9 19c-1 2-2 7-4 9-1 3-2 5-3 7 0 1 0 1-1 2v1l-1 1c-2 1-6 12-7 15-2 4-3 7-3 11 1 6 0 14 0 20v46h0c-1-1-2-1-2-3-2-2-4-5-5-8-2-6-8-13-11-19l-1-1c-1 3-3 5-4 8v9h0l-3 28-1-1c0 1-1 2-1 3s-1 1-2 2h-4-3v1c-1-1 0-2-1-3v-7l-1-1 1-2-7-19-9-20c-2-4-3-8-5-11 0-2-2-5-2-6l-8-18-29-63c0-1-1-3-2-4l-4-9-13-28-26-57h0l-7-17-25-55c0-1-2-5-3-6l-21-45-41-89-35-76-21-43c-2-4-18-28-20-29-4-3-9-10-10-14 1-1 1-1 3-2 2 1 4 4 5 6 0-1 0-1-1-2l-1-2h1 1c1 0 1-1 2-1h1c4 2 7 6 9 10 3-2 6-2 10-1v-1l-7-10z" class="R"></path><path d="M410 488c2 1 3 2 3 4l-3 3v-1h0l-2-2 2-4z" class="J"></path><path d="M494 616c2-3 3-6 4-10v2 1s0 1-1 1v2l-1 1v1l1 1c-1 2 0 3-2 4h0v-1c-1 0-2 0-2-1l1-1z" class="B"></path><path d="M410 494c-2-2-4-3-6-5l6-3v2l-2 4 2 2h0zm109 222c2 1 2 1 3 3 0 2-2 4-3 6h-1v-1c1-2 0-5 0-7l1-1z" class="F"></path><path d="M448 583l-3-6h7v2c-1 2-3 3-4 4z" class="G"></path><path d="M518 724c-1 0-2-1-2-2l-1-1-1-1c0-2 0-4 2-5l3 1-1 1c0 2 1 5 0 7z" class="H"></path><path d="M500 615h0c1 0 1 0 2 1h0 1 0c0 2 0 5-1 6v2 1l-1 1c-1-1-2-2-2-3 1-2 1-5 1-8z" class="B"></path><path d="M588 496c1-1 1-1 3-2v1 1c-1 0-2 1-2 1l-1 1h1c-1 1-1 3-2 4h-1 0-1c-2 0-1 0-2 1-2 0-2-1-3 1v1h-2v-1l2-2h2c1 0 1-1 2-1l-1-1v-1l1-1c2 0 3-1 4-2zm-104 50c1 0 1 0 2 1v1l-1 1-3 1c0-1-1-1-1 0h-2-1-2c-1 1 0 1-1 2h0-1l-1-1c-1 0-2 1-2 1h-1-2 0c1-1 2-1 3-2h1 1 0 1c-1-1-1-1-1-2v-1l1 1h1c1-1 1-1 2-1h3 0c2-1 3-1 4-1z" class="H"></path><path d="M473 543v1h1l4-1c3 0 5 0 8 1h1c-2 0-2 0-3 1v1c-1 0-2 0-4 1h0-3c-1 0-1 0-2 1h-1l-1-1-1-1v-1l-2 1c0-1-1 0-2-1 1-2 2-2 5-2z" class="E"></path><path d="M499 558h0c2-1 3-1 4-3l1 1c-1 1-1 1-1 2h1c-1 2-2 3-4 4l-3 2v1l-3 2v-1h-1l-4 2h-1c2-1 4-3 5-4l-1-1h0c1-1 2-2 3-2 1-1 2-2 4-3z" class="B"></path><path d="M530 559h0l1-2c0-1 1-1 2-1h1l-1-1v-1l2-1c0 1 0 1 1 2h1s1 0 1 1h2 1l1 1h2l1 1c1 0 1 0 2 1h-2c0 1-1 1-1 1-1 1-1 1-1 2-1-1-2-1-2-1l-1-1c-1 0-2-1-4-2h0c-1-1-2-1-3-1h-1c1 1 1 1 2 1l2 2h0-2c-1-1-2-1-4-1z" class="E"></path><path d="M673 398c0 3 0 4-1 6-3 4-10 12-15 12-1 1-3 0-4 0h4c1-1 2-2 2-3a30.44 30.44 0 0 0 8-8c2-3 3-5 6-7z" class="C"></path><path d="M581 489l2-2c2-3 5-5 7-8 1 0 2-2 3-2l2-2c1-1 2-2 3-2h0c1-1 2-2 3-2l-1 2h1c1-1 0-1 1 0 0 1 0 1-1 1l-1-1-6 5c-2 1-5 4-5 6h0v1l-1 1-5 6c3-2 5-3 6-6l1 1-1 1v1l-3 3c-2 0-3 0-4 1 0-1-1-1 0-1l1-2 1-1c-1 0-1 0-2 1l-1-1zm-50 71c1 0 3 1 4 2l1 1c1 1 2 1 3 2l2 1 2 2 4 3 2 1h0c-1 1-1 1-2 1l-1-1c-1-1-2-2-3-2-1 1-1 0-1 1l2 2c1 0 1 1 2 1v1 1h0c-3-2-6-6-9-8-1 0-1-1-2-1-2-1-3-3-5-4l-1-1v-1-1h2z" class="E"></path><defs><linearGradient id="q" x1="461.169" y1="581.783" x2="449.865" y2="582.181" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#4c4b4b"></stop></linearGradient></defs><path fill="url(#q)" d="M452 577l13-1c-6 4-10 9-15 13l-2-6c1-1 3-2 4-4v-2z"></path><path d="M486 616c2-2 4-6 7-6h0c1 1 1 3 1 4v1 1l-1 1c-5 5-8 7-14 9l7-10z" class="G"></path><path d="M707 318c1 3 0 10-2 13v2h-1c-1 3-3 6-6 7h-2l-1-1c4-4 6-9 8-13l4-8z" class="L"></path><path d="M492 593c1 0 2 0 2-1 2-2 3-5 5-7 0-1 1-1 1-2l2-2v-2c1 1 2 2 2 3v2l-1 1c-1 4-3 7-4 10v1 1l-1 1v3c0 1 0 2-1 3l-1-4c0-2-2-5-4-6v-1z" class="B"></path><path d="M525 744l2-2v9h0c-1 1-2 1-2 3-1 3-3 8-5 11-2 2-3 3-4 6l-3 3h0-1l-1-1 1-2h1c3-5 5-10 8-16 1-3 3-7 4-11zm-61-126c0-2-1-3-2-5-1-5 0-10 3-14 4-6 9-8 16-9l5 1h0v1c0 1-1 2-2 3h0c-1 1-2 2-4 2-1-1-1-3-3-3-2-1-5-1-6 0-4 3-5 7-6 10 0 3 0 13-1 14z" class="D"></path><path d="M481 590l5 1h0v1c0 1-1 2-2 3h0c-1-2-2-4-3-5z" class="F"></path><defs><linearGradient id="r" x1="492.199" y1="597.961" x2="497.385" y2="604.976" xlink:href="#B"><stop offset="0" stop-color="#292828"></stop><stop offset="1" stop-color="#454545"></stop></linearGradient></defs><path fill="url(#r)" d="M492 594c2 1 4 4 4 6l1 4 1 2c-1 4-2 7-4 10v-1-1c0-1 0-3-1-4h0c-3 0-5 4-7 6v-2c0-6 6-9 6-13l1-3v-2h0l-1-1v-1z"></path><path d="M695 339v-2c-2-4 1-10 3-14l1-1h0c0-2 3-4 5-5 1 0 1 0 3 1l-4 8c-2 4-4 9-8 13z" class="B"></path><path d="M670 394h0c1 2 2 3 3 4-3 2-4 4-6 7a30.44 30.44 0 0 1-8 8c0 1-1 2-2 3h-4-1v-1c-2-1-4-2-5-5 0-2 0-4 1-6h1c-1 2-1 3-1 5s2 4 4 5c1 0 2 0 2-1 6-6 11-13 16-19z" class="P"></path><path d="M535 553c2 0 4 1 6 1 1 0 2 0 3 1 2 1 5 3 8 4l2 1c3 1 6 2 8 4-1 1-1-1-2 1v3 1h-2c-1-1-2-1-4-2 0-1-1-1-1-1h-1c-1-1-2-1-3-2l-2-1-4-1c0-1 0-1 1-2 0 0 1 0 1-1h2c-1-1-1-1-2-1l-1-1h-2l-1-1h-1-2c0-1-1-1-1-1h-1c-1-1-1-1-1-2z" class="H"></path><path d="M590 512h1l3 6 5 12c0 2 5 12 5 14h-2l-1-1c-3-1-6-11-7-15-2-4-5-9-7-14 1-1 2-1 3-2z" class="D"></path><path d="M417 429l-1-1h-1l-1 1v-2c-1-2-3-3-5-5h1c0-3-5-6-7-8-1-2-2-3-3-4l-2-4v-1l1 1c0-1 0-1 1-2v1c3 1 6 4 8 7l1 1 1 1c0 1 2 1 2 3 1 1 0-1 1 1 1 0 1 1 2 1 0 1 1 2 2 3 0 1 0 2 1 2v1c0 1 0 1 1 2h0-1-1l1 1c1 1 1 1 2 3h-1 0l-2-2z" class="B"></path><path d="M540 549l1-1h-2c1-1 1-1 1-2h1c0-1 0-1 1-2h0l1-1c1-1 0-1 1-3l1 1h-1l1 1c1-1 1-1 2-1l-1-1-1-1v-1h2c0-1 0-1 1-2h2l1-1c1 0 2-1 3-2h3c2-1 3 0 4-1h4 2v1h-1 1c1 1 1 1 1 2-1 1-2 1-3 1h0c2 1 1 1 2 1h1c1 0 1 0 1 1-1 1-1 0-2 1h-2c-1-1-3 0-4 0-2-1-12-1-13 0 1 0 1 0 2 1h1 4l2 1h0 1l1 1c1 0 2 0 2 1h0-3 0c-2 0-2 0-2-1-2 1-2 1-3 1h-1-3-1l2 2c1 0 1 0 2 1-1 1-2 0-3 1h-1c-1 0-2 0-3 1h1 2l-1 1c0 1-1 1-1 0h-6z" class="E"></path><path d="M599 511c3 0 6 0 9 1h0c1 0 2 0 2 1 3 1 5 5 6 8 2 5 0 9-2 13-1 2-2 3-3 4-2-10-5-19-12-27z" class="C"></path><defs><linearGradient id="s" x1="515.127" y1="768.56" x2="520.514" y2="782.611" xlink:href="#B"><stop offset="0" stop-color="#868485"></stop><stop offset="1" stop-color="#a3a3a2"></stop></linearGradient></defs><path fill="url(#s)" d="M512 774h1 0l3-3c1-3 2-4 4-6 2-3 4-8 5-11 0-2 1-2 2-3l-3 28-1-1c0 1-1 2-1 3s-1 1-2 2h-4-3v1c-1-1 0-2-1-3v-7z"></path><path d="M494 547l2-1 1 2c-2 0-4 1-6 1h4c1-1 2-1 3-1v1l2 2c0 1-2 2-2 3l3-2v2h1c0 2-2 3-3 4-2 1-3 2-4 3-1 0-2 1-3 2h0c0 1 0 1-1 1-1 1-2 2-3 2h-1l2-2v-1c0-1 1-1 0-2h-1c0 1 0 1-1 2-1-1-1-1-1-2-1 1-3 2-4 2v-1c1 0 2-1 3-2h0l1-1h-1v-1-1h1-1c0 1 0 1-1 2h-2v-1c-1 0-1 0-2-1 1 0 1 0 2-1l1-1h1v-2h0l-1-2h-2-1c-1 1-3 1-5 1 1-1 0-1 1-2h2 1 2c0-1 1-1 1 0 1 0 2 0 3-1h3l2-1c2 0 3-1 4-1z" class="E"></path><path d="M656 393l2-2c3-1 7 0 10 1l2 2h0c-5 6-10 13-16 19 0 1-1 1-2 1-2-1-4-3-4-5s0-3 1-5h-1v-1h2v-2c0-1 0 0 1-1l5-7z" class="O"></path><path d="M656 393l2-2c3-1 7 0 10 1h-1c-1 1-2 1-4 1-1 0-2 1-4 2l-4 5c0-2 1-4 1-7z" class="J"></path><path d="M655 400c-2 2-3 5-5 6v1l-1 2c1 2 1 2 2 3 1 0 2 0 3-1s2-3 4-5c3-4 7-9 11-13l1 1c-5 6-10 13-16 19 0 1-1 1-2 1-2-1-4-3-4-5s0-3 1-5h-1v-1h2v-2c0-1 0 0 1-1l5-7c0 3-1 5-1 7z" class="I"></path><path d="M522 575c1 1 1 1 2 3h0v1 3h1v2l2 5s0 1 1 2h0v1 2c1 1 1 2 1 3h0c-1 0-1-1-1-2h-1c-2 3 0 6 1 9v2s-1 0-1-1v-1c-1 1-1 1-1 2h0l-1-1-2-3c0-1 0 0-1-1 0 2 0 3-1 5h-2v-4c-1 0-1-1-1-1v-1c-1-2 0-9 0-12h-1c0 1 0 2-1 3-1-1 0-6 0-7l-1-1v3c-1 1 0 1-1 2h-1c1-1 1-9 1-10-1-1-1-1-1-2h1 1c1 1 1 4 2 5v-2h1c0 1 0 2 1 2 0-2 0-3 2-4l1-2z" class="B"></path><defs><linearGradient id="t" x1="479.636" y1="606.971" x2="486.745" y2="614.634" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#3a3a3a"></stop></linearGradient></defs><path fill="url(#t)" d="M492 594v1l1 1h0v2l-1 3c0 4-6 7-6 13v2l-7 10c-4 0-6-1-9-2h-1v-1l-1-1 2-3 5-7c1-1 3-3 4-5 4-4 8-8 11-12 1-1 1-1 2-1z"></path><path d="M470 619c1 0 2-2 3-3h1c1 0 2-1 3-1-2 2-7 6-7 9h-1v-1l-1-1 2-3z" class="B"></path><path d="M492 594v1c-4 5-8 11-12 17-1 0-2 2-3 3-1 0-2 1-3 1h-1c-1 1-2 3-3 3l5-7c1-1 3-3 4-5 4-4 8-8 11-12 1-1 1-1 2-1z" class="H"></path><path d="M615 551l1 1c-1 2-1 3-2 4l-4 11-9 17c0 1-1 2-1 3 0 2-1 2 0 3 2 4 2 6 2 10-1 1-4 1-4 2-1 1-1 2-1 4v10c-2-4-2-9-1-14v-8l-8 18-5 12c-1 1-1 4-2 5h-1c-2 4 0 9 0 13 0 5 0 10-1 15v-1c-1-7-1-15-1-22l-9 19c-1 2-2 7-4 9h0c0-1 0-2 1-3l9-22c1-2 3-4 3-6 0-3 1-5 3-8h1v-1l1-1c0-1 2-3 2-5h-1l1-1h1v-2c0-1 0-2 1-2 2-2 3-5 3-7l2-2 6-17c0-1 1-1 1-2 1-1 2-1 2-2l1-2c1-1 1-1 1-3s1-3 1-4l7-14c2-2 3-5 4-7z" class="D"></path><path d="M525 744c1-4 3-7 5-11 0-1 0-1 1-2v-1c1-1 1-2 1-3l25-52 9-20c2-5 4-11 7-15 0-1 1-2 2-3l-9 22c-1 1-1 2-1 3h0c-1 3-2 5-3 7 0 1 0 1-1 2v1l-1 1c-2 1-6 12-7 15-2 4-3 7-3 11 1 6 0 14 0 20v46h0c-1-1-2-1-2-3 0-1 1-1 0-2 0-6 1-11 1-17 0-15-2-31 0-46l-13 29-3 5-1 2v2l-1-1c-1 3-3 5-4 8l-2 2zm-39-153c3 0 4 1 6 2v1c-1 0-1 0-2 1-3 4-7 8-11 12-1 2-3 4-4 5l-5 7-2 3-1-1-3-3c1-1 1-11 1-14 1-3 2-7 6-10 1-1 4-1 6 0 2 0 2 2 3 3 2 0 3-1 4-2h0c1-1 2-2 2-3v-1h0z" class="G"></path><path d="M469 613c-1-1-2-3-2-4 0-4 1-7 3-10 2-1 3-2 6-2 1 1 1 2 2 4-3 4-7 8-9 12z" class="H"></path><defs><linearGradient id="u" x1="490.67" y1="595.032" x2="468.184" y2="607.977" xlink:href="#B"><stop offset="0" stop-color="#474546"></stop><stop offset="1" stop-color="#818080"></stop></linearGradient></defs><path fill="url(#u)" d="M486 591c3 0 4 1 6 2v1c-1 0-1 0-2 1-3 4-7 8-11 12-1 2-3 4-4 5-3 2-4 4-6 5-1-1-1-2 0-4 2-4 6-8 9-12l2-4c2 0 3-1 4-2h0c1-1 2-2 2-3v-1h0z"></path><path d="M552 524h2v-1l2-2 1-2 5-6c1-2-1 0 1-1 1-1 1-2 2-2l2-3c0-1 1-2 1-2l3-3 1-1 3-5c1-2 3-3 4-4 0-1 1-2 2-3l1 1v1l-2 2-3 3h0c1-1 2-1 3-1l-1 1v1c-1 0-1 1-2 2l1 1 1-1h1l1-1c1 0 1-1 2-1 2-1 3-1 5-1-1 1-2 2-4 2l-1 1v1l1 1c-1 0-1 1-2 1h-2l-2 2c-1 0-2 1-3 2h-1c0 1 0 1-1 2h1l1 1h-3c-1 1 0 1-1 2h1c1 0 2-1 3-2l1 1 2-2h1c-1 1-1 2-3 3h-1l-1 1c-1 0-1 0-2 1l-1-1h-1v1l-3 3v1h2l1-1h2v-1h3c1-1 1-1 3-1v1c-2 1-3 1-5 2h0c-1 0-2 1-2 1h-1c-1 0-2 0-3 1h-2c-1 0-1 1-1 1-1 0-2 1-3 1-2 1-2 1-2 2 1 0 2-1 3-1 1-1 1-1 2-1 1-1 2-1 3-1v1h-2c-2 1-3 2-6 3 0 0-1 0-1 1l-1-1-1 1h1c0 1-3 3-2 4 1-1 2-1 4-1l2-1h1c1-1 4-1 5-1h0v1c-1 0-2 1-2 1h-1c-1 1-1 1-1 2h0s1 1 2 1c1-1 0-1 1-1 2 0 3 1 4 0s3 0 4-1h2l2-1h1v3h0c-1-1-1-1-1-2-2 1-3 1-4 1-1 1-3 1-4 1-1 1-2 1-3 1h-2-4c-1 1-2 0-4 1h-3c-1 1-2 2-3 2l-1 1h-2c-1 1-1 1-1 2h-2v1l1 1 1 1c-1 0-1 0-2 1l-1-1h1l-1-1c-1 2 0 2-1 3l-1 1h0c-1 1-1 1-1 2h-1c0 1 0 1-1 2h2l-1 1v1c1 1 2 1 3 2v1l-1-1-2 1v-1h-3l-2-1 13-19c1-3 2-6 4-8zm-111-59c3 0 3 1 5 3h0c0 2 2 3 3 5 1 1 2 3 3 4l2 3v1h1l3 5 1 1c0 1 0 0 1 1 0 1 1 2 1 3 1 0 1 1 2 2s3 2 4 5l1 1h0c1 2 3 3 4 5l1 1c1 1 2 2 2 3l1 1c0 1 1 2 3 3l-1 1v1c2 1 2 3 4 5 1 2 2 3 3 4h0c-3-2-5-6-8-8l-4-4v1c1 1 2 2 3 4h-1-1c0-1 0-1-1-2-2-2-4-5-7-7h0v-2l-8-7-1-1-2-2c-1 0-2-1-3-2h0c-2-1-3-2-4-2h-1s-1-1-2-1v-1l-2-1-3-2c-1-1-1-1-1-2l2-2v-2l-3-2h-1v-2l2-1 1 1v-1c-2-2-3-2-6-4l1-1h1l1 1c2 1 3 2 5 3v2c0 1 0 0 1 1h0 2c1 1 3 4 4 4v-1-1l-3-2-3-4c-1-1-2-1-2-2-1-1-2-2-3-2l-1-1c0-1-1-1-2-2h0 0l1-1c4 3 8 8 12 11h0c-1-2-3-4-5-6-1 0 0 0-1-1l-3-3 1-1c1 1 2 3 3 4h1c0-1-2-3-3-4z" class="B"></path><path d="M532 735v-2l1-2 3-5 13-29c-2 15 0 31 0 46 0 6-1 11-1 17 1 1 0 1 0 2-2-2-4-5-5-8-2-6-8-13-11-19z" class="R"></path><path d="M595 463c0 1 0 1-1 2s-5 7-5 9v-1l1 1 5-5h1c1-1 3-3 5-4l2-2c1-1 3-1 4-2l2-1v-1c1 2 0 3 1 4 0 1 1 1 1 2v1h-3c0 1-1 1-1 2h-2c-2 2-2 3-4 3-1 0-2 1-3 2h0c-1 0-2 1-3 2l-2 2c-1 0-2 2-3 2-2 3-5 5-7 8l-2 2c-1 1-2 2-2 3-1 1-3 2-4 4l-3 5-1 1-3 3s-1 1-1 2l-2 3c-1 0-1 1-2 2-2 1 0-1-1 1l-5 6-1 2-2 2v1h-2l9-13c-2 1-7 8-8 10s-3 3-3 5h-1c-1 2-2 3-3 4 1-2 2-3 2-5 2-2 4-5 5-7-1 1-2 3-4 5-1 1-3 2-5 4-2 3-3 6-5 9s-5 6-7 9c0-3 3-6 4-8l12-18c2-2 3-3 5-6l8-12c1 0 4-3 4-4h1l1-2c1-1 1-2 3-3l2-2v-1c1 0 1 0 1-1l2-2h1c0-1 1-2 1-2 1-2 1-1 2-1l3-3c1-2 2-5 3-6s2-1 3-2l7-9z" class="H"></path><path d="M561 501c1 0 4-3 4-4h1l1-2c1-1 1-2 3-3l2-2v-1c1 0 1 0 1-1l2-2h1c0-1 1-2 1-2 1-2 1-1 2-1l3-3c1-2 2-5 3-6s2-1 3-2c-1 3-3 5-4 8-2 3-4 5-6 7l-4 4c-2 1-4 2-5 4 0 1-1 1-2 2 0 2-3 4-4 5l-2 2-1 2c-1 1 0 1-1 2l-1 1-1 1c0 1-1 1-1 2-1 1-1 2-1 3-1 1-1 1-1 2l-1 1c-1 1-2 3-4 5-1 1-3 2-5 4-2 3-3 6-5 9s-5 6-7 9c0-3 3-6 4-8l12-18c2-2 3-3 5-6l8-12z" class="B"></path><path d="M448 491c1 0 2 1 4 2h0c1 1 2 2 3 2l2 2 1 1 8 7v2h0c3 2 5 5 7 7 1 1 1 1 1 2h1 1 0l1-1c2 2 5 7 7 10h0 0c-2-1-5-6-6-7-1 2 3 5 4 7 1 1 1 2 2 4v1l1 1c1-1 1 0 1-1 2 1 4 3 5 5v1l-4-4v1c1 1 3 2 4 4 0 2 1 2 1 4h0c-2 0-1 0-2 1-2-1-2 0-3-1l-1 1c2 1 2 0 4 0h2 1c1 0 2 2 3 3h-1l-1 2c-1 0-2 1-4 1l-2 1h-3c-1 1-2 1-3 1l3-1 1-1v-1c-1-1-1-1-2-1v-1c1-1 1-1 3-1h-1c-3-1-5-1-8-1l-4 1h-1v-1h0 1c1-1 2-1 3-1s2-1 4-1l-1-1v1l-2-2h-2l-1-1-1 1c-1 0-3 0-4-1v-1l-1 1v1l-1-1c-2 1-4 0-6-1v-1h4v1h0l1-1h2 0l1 1 3-2-1-2h-1-1l-1-1h-5v-2h2 1 2 0l1 1 1-1h1c1 1 1 0 2 1h1 1c1 0 4 1 6 1h0c-2-1-2-2-3-2h-1-1c-1 0-1-1-2-1h-2v-1c-2 0-2 0-3-1 0-1 0-1 1-2v1c2 0 3 0 4 1h1v-2c-1-1-2-1-2-1-1 0-1 0-2-1h0-2l-2-1h-1-2-1 0 0c-1 0-1 0-1-1v-1c-1 0-3-1-4-2h2 2v1h1 1 1v1h2 1c1 0 1 0 2 1h1 1 0l-1-1c-1-1-2-2-3-2h-1c-1-1-1-1-2-1h0c-3-2-6-2-9-3v3l-2-1c0-2 0-3 1-4s2-1 3 0h2v1l4 1h1 1l1 1h0c1 0 2 1 3 1v-1l-1-2h0c-2-2-4-4-6-5v-1h-1c-3-3-7-5-11-6l-1-1h-1c-1-1-2 0-4-1v-2c1 0 1 0 2-1l2 1h1v1l7 3h1l-1-1-3-3c-2-1-1 0-2-1 0-1-1-1-2-2-2-1-2-1-3-3z" class="I"></path><path d="M491 522l1-1-2-3v-1c2 1 2 3 4 4 0-1 0-2-1-3h-1l-3-4 1-1c1 0 2 1 2 2h1 0 1c0 1 0 2 1 3h1l1 3h2 1 0c0-3-2-5-4-7-1-2-3-5-4-7l1-1c1 2 2 6 5 7v-3l12 30c3 6 7 12 8 18 0 1 0 2 1 2h1c1-1 1-2 3-2 1-1 2-2 2-3 2-2 3-5 5-7 1-1 2-2 2-3 2-3 5-6 7-9s3-6 5-9c2-2 4-3 5-4 2-2 3-4 4-5-1 2-3 5-5 7 0 2-1 3-2 5 1-1 2-2 3-4h1c0-2 2-3 3-5s6-9 8-10l-9 13c-2 2-3 5-4 8l-13 19v1c2 1 4 2 6 2-2 0-4-1-6-1l-2 1v1l1 1h-1c-1 0-2 0-2 1l-1 2h0l1 1h-2c-2 2-5 4-6 7h-1c-1 1 0 0 0 1l-1 1h-1c0 1-1 1-2 1-1-1-2-1-3-1v-2l1-2c0 1 1 1 2 2l3-3v-2c-1 1-2 1-2 2v1h-1v-2h1l-3-5-3-3-2-3c-1-1-1-3-2-4l-5-6-2-4h-1v-1c-1-1-2-3-3-4h0l-2-3-2-3-1-2c-1-1-1-2-2-3z" class="M"></path><path d="M548 525c0 2-1 3-2 5 1-1 2-2 3-4h1c0-2 2-3 3-5s6-9 8-10l-9 13c-2 2-3 5-4 8l-13 19v1c2 1 4 2 6 2-2 0-4-1-6-1l-2 1v1l1 1h-1c-1 0-2 0-2 1l-1 2h0l1 1h-2c-2 2-5 4-6 7h-1c-1 1 0 0 0 1l-1 1h-1c0 1-1 1-2 1-1-1-2-1-3-1v-2l1-2c0 1 1 1 2 2l3-3v-2c0-2 4-5 5-6v-1l2-3 1-1c0-1 1-1 1-2l3-3 2-4 1-1 1-1c1-2 4-4 4-6 0-1 0-1 1-2h0c1-2 1-2 2-3v-1c1-2 3-2 4-3z" class="N"></path><defs><linearGradient id="v" x1="530.392" y1="532.449" x2="534.77" y2="538.785" xlink:href="#B"><stop offset="0" stop-color="#121011"></stop><stop offset="1" stop-color="#2b2c2c"></stop></linearGradient></defs><path fill="url(#v)" d="M599 453v1h1 0c1-2 2-3 4-5h1v1h-1c-2 2-3 4-4 6s-2 3-3 4c0 1-1 2-2 3l-7 9c-1 1-2 1-3 2s-2 4-3 6l-3 3c-1 0-1-1-2 1 0 0-1 1-1 2h-1l-2 2c0 1 0 1-1 1v1l-2 2c-2 1-2 2-3 3l-1 2h-1c0 1-3 4-4 4l-8 12c-2 3-3 4-5 6l-12 18c-1 2-4 5-4 8 0 1-1 2-2 3-2 2-3 5-5 7 0 1-1 2-2 3-2 0-2 1-3 2h-1c-1 0-1-1-1-2 2-1 5-9 6-12l6-10 7-18c1 3-1 7-1 10 2-2 4-4 5-7l3-3v-1c4-3 7-9 10-13l10-13c3-4 6-7 8-11 1-2 3-4 4-5 2-2 7-6 7-9h1c3 0 6-7 9-9h1l3-3c1-1 0-1 2-1z"></path><path d="M537 518c1 3-1 7-1 10 2-2 4-4 5-7l3-3v-1l1 1-2 3-1 1c0 2-2 3-3 4-3 3-5 5-7 8l-2 2 7-18z" class="N"></path><path d="M599 453v1h1 0c1-2 2-3 4-5h1v1h-1c-2 2-3 4-4 6s-2 3-3 4c0 1-1 2-2 3l-7 9c-1 1-2 1-3 2s-2 4-3 6l-3 3c-1 0-1-1-2 1 0 0-1 1-1 2h-1l-2 2c0 1 0 1-1 1v1l-2 2c-2 1-2 2-3 3l-1 2h-1c0 1-3 4-4 4-2 2-4 3-5 5-1 3-3 6-5 8-1 2-3 4-5 6l-5 8c-1 1-2 3-3 4h-1l19-27c2-2 4-4 5-6 4-6 9-10 13-15 1-2 2-3 3-5 2-2 5-4 6-6-3 2-4 4-7 6 3-5 7-8 10-12h-1c-1 1-2 3-4 5-8 8-15 18-22 28l-14 18-1-1c4-3 7-9 10-13l10-13c3-4 6-7 8-11 1-2 3-4 4-5 2-2 7-6 7-9h1c3 0 6-7 9-9h1l3-3c1-1 0-1 2-1z" class="J"></path><path d="M620 538l133-288 1 2-72 158c-4 9-30 61-29 65 0 2 1 5 2 8v1c1 3 0 7 0 10 0 2 0 3 1 4h0c2 1 2 1 3 2-3 4-1 10-3 15-1 2 0 5 0 7h2l-3 2-3 2v-37-6-7c-1 2-2 3-2 4-1 2-1 3-2 5-1 1-1 2-2 3l-3 6v1c-1 3-3 6-4 9-1 1-1 0-1 1-1 4-4 8-6 12l-3 8-1 1c0 1 0 0-1 1s-1 2-2 3l-1 2v2c-1 1-2 2-2 4h-2z" class="E"></path><path d="M655 484c1 3 0 7 0 10 0 2 0 3 1 4h0c2 1 2 1 3 2-3 4-1 10-3 15-1 2 0 5 0 7h2l-3 2v-40z" class="M"></path><path d="M419 423c1 0 1 0 2 1 0 1 1 1 1 2 1 1 1 2 2 2 1 1 0 0 0 2l1 1h1c0 1 1 1 1 2l1 1 1 1 1 2c1 1 1 1 1 2 1 2 3 4 5 6 1 1 0 0 0 2 1 0 2 1 2 2l1 1c1 1 2 3 3 4l2 3c1 2 2 3 4 5 2 3 5 6 6 10 2 2 4 5 6 7 4 5 8 11 11 16l2 2c1 3 3 3 4 6 1 2 3 5 5 7l8 14c0 1 1 1 1 2l2 2 1 2h0c1 1 1 2 2 4l3 3c1 1 1 3 2 4l2 3c1 1 1 2 2 3v1l-2-2v2c1 1 1 2 1 4-1-2-2-4-3-5 0-2-1-2-3-3h0l-1 1 1 2c-1 0-1 0-1 1l-1-2-2 1 1-2h1c-1-1-2-3-3-3h-1-2c-2 0-2 1-4 0l1-1c1 1 1 0 3 1 1-1 0-1 2-1h0c0-2-1-2-1-4-1-2-3-3-4-4v-1l4 4v-1c-1-2-3-4-5-5 0 1 0 0-1 1l-1-1v-1c-1-2-1-3-2-4-1-2-5-5-4-7 1 1 4 6 6 7h0 0c-2-3-5-8-7-10l-1 1h0c-1-2-2-3-3-4v-1l4 4c3 2 5 6 8 8h0c-1-1-2-2-3-4-2-2-2-4-4-5v-1l1-1c-2-1-3-2-3-3l-1-1c0-1-1-2-2-3l-1-1c-1-2-3-3-4-5h0l-1-1c-1-3-3-4-4-5s-1-2-2-2c0-1-1-2-1-3-1-1-1 0-1-1l-1-1-3-5h-1v-1l-2-3c-1-1-2-3-3-4-1-2-3-3-3-5h0c-2-2-2-3-5-3-1 0-2-2-2-3v-1c1 1 1 1 2 1-1-1-1-2-2-3-2-1-2-2-2-4 0-1-3-3-3-4-1-1-3-2-4-4l-8-10c-1-2-3-4-5-6v-2l2 2h0 1c-1-2-1-2-2-3l-1-1h1 1 0c-1-1-1-1-1-2v-1c1 0 1-1 1-1z" class="H"></path><path d="M475 508c2 1 4 3 4 5 1 3 4 7 7 9h0l-7-11c-2-3-6-7-7-10-1-4-3-7-5-9l-13-17h1v-1c3 4 6 8 10 12 2 3 3 6 6 9h0l2 2c1 3 3 3 4 6 1 2 3 5 5 7l8 14c0 1 1 1 1 2l2 2 1 2h0c1 1 1 2 2 4l3 3c1 1 1 3 2 4l2 3c1 1 1 2 2 3v1l-2-2v2c1 1 1 2 1 4-1-2-2-4-3-5 0-2-1-2-3-3h0l-1 1 1 2c-1 0-1 0-1 1l-1-2-2 1 1-2h1c-1-1-2-3-3-3h-1-2c-2 0-2 1-4 0l1-1c1 1 1 0 3 1 1-1 0-1 2-1h0c0-2-1-2-1-4-1-2-3-3-4-4v-1l4 4v-1c-1-2-3-4-5-5 0 1 0 0-1 1l-1-1v-1c-1-2-1-3-2-4-1-2-5-5-4-7 1 1 4 6 6 7h0 0c-2-3-5-8-7-10l-1 1h0c-1-2-2-3-3-4v-1l4 4c3 2 5 6 8 8h0c-1-1-2-2-3-4-2-2-2-4-4-5v-1l1-1c-2-1-3-2-3-3l-1-1z" class="F"></path><defs><linearGradient id="w" x1="636.534" y1="518.585" x2="651.591" y2="522.971" xlink:href="#B"><stop offset="0" stop-color="#d9d9d8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#w)" d="M648 485c1-2 1-3 2-5 0-1 1-2 2-4v7 6 37c-9 6-17 13-23 21l-3 6c-1-1 0-3 0-5v-7-6-2c1-2 1-4 1-6 1-1 1 0 1-1l1-1 3-8c2-4 5-8 6-12 0-1 0 0 1-1 1-3 3-6 4-9v-1l3-6c1-1 1-2 2-3z"></path><path d="M652 489c-1 2 0 5-1 7 0-2 0-4-1-6 1-3 1-6 1-9h0l1 2v6z" class="B"></path><path d="M648 485c-1 7-1 13-5 19-3 4-5 10-7 14 0 2-2 5-2 7l-3 12-3 9 1 1-3 6c-1-1 0-3 0-5v-7-6-2c1-2 1-4 1-6 1-1 1 0 1-1l1-1 3-8c2-4 5-8 6-12 0-1 0 0 1-1 1-3 3-6 4-9v-1l3-6c1-1 1-2 2-3z" class="I"></path><defs><linearGradient id="x" x1="608.556" y1="570.161" x2="616.064" y2="573.402" xlink:href="#B"><stop offset="0" stop-color="#a9a7a7"></stop><stop offset="1" stop-color="#d0d0cf"></stop></linearGradient></defs><path fill="url(#x)" d="M622 538c0-2 1-3 2-4v-2l1-2c1-1 1-2 2-3 0 2 0 4-1 6v2 6 7c0 2-1 4 0 5-1 4-3 7-3 10v1c-1 4-1 8-1 12v12c1 4 3 9 2 13v1c-1 1-2 1-3 2l-2 1c-3-1-6-3-10-4-2-1-5-1-7-1 0-4 0-6-2-10-1-1 0-1 0-3 0-1 1-2 1-3l9-17 4-11c1-1 1-2 2-4l-1-1c-1-2 3-9 4-11l1-2h2z"></path><path d="M601 584c0 1 0 4 1 6 2 1 3 2 4 4s3 4 4 6c0-3-1-7 0-9l2-2c-1 2-1 3 0 6h1v-2c1 3 2 8 6 10 0 0 1 0 2 1l-2 1c-3-1-6-3-10-4-2-1-5-1-7-1 0-4 0-6-2-10-1-1 0-1 0-3 0-1 1-2 1-3z" class="C"></path><path d="M622 538c0-2 1-3 2-4v-2l1-2c1-1 1-2 2-3 0 2 0 4-1 6v2 6 7c0 2-1 4 0 5-1 4-3 7-3 10v1c-1 4-1 8-1 12v12c1 4 3 9 2 13v1c-1 1-2 1-3 2-1-1-2-1-2-1-4-2-5-7-6-10v2h-1c-1-3-1-4 0-6h0c1-1 2-2 3-2v-1c0-1 1-2 1-2v-1c1-1 1-3 2-5h0c-1-4-1-9-1-13 0-2 1-6 0-9 0-2 0-5 1-6v-2c0-1 0-1 1-2l1-4h0l-1-2 1-2h2z" class="G"></path><path d="M618 578s1-1 1-2 0-1 1-2v2 9c-1 1-1 0-2 1l-3 1v-1c0-1 1-2 1-2v-1c1-1 1-3 2-5z" class="B"></path><path d="M615 587l3-1h2c0 1-1 2-2 3 1 1 1 1 1 2 2 2 3 3 3 6l1 1v1l-4-4c-2-2-3-4-6-4v2 2h-1c-1-3-1-4 0-6h0c1-1 2-2 3-2z" class="E"></path><path d="M613 593v-2c3 0 4 2 6 4l4 4 1 2v1c-1 1-2 1-3 2-1-1-2-1-2-1-4-2-5-7-6-10z" class="M"></path><defs><linearGradient id="y" x1="613.135" y1="554.117" x2="625.865" y2="564.883" xlink:href="#B"><stop offset="0" stop-color="#b5b4b2"></stop><stop offset="1" stop-color="#e8e7ea"></stop></linearGradient></defs><path fill="url(#y)" d="M620 538h2c-1 2-1 3-1 5v1l-1 32v-2c-1 1-1 1-1 2s-1 2-1 2h0c-1-4-1-9-1-13 0-2 1-6 0-9 0-2 0-5 1-6v-2c0-1 0-1 1-2l1-4h0l-1-2 1-2z"></path><path d="M372 356c1 1 3 3 4 5 0 1 1 2 2 3 0 1 4 5 4 5l7 9c1 1 2 1 3 2v1l6 5v1c1 2 3 4 4 6l2 1c0 1 1 2 1 3 1 0 1 1 1 2l1 1 1 1c0 1 1 2 2 2 1 1 0 0 0 2h1c0 1 1 1 1 2l1 2 1 1c0 1 1 1 1 1 1 1 2 1 2 3l2 1 14 20c3 5 8 11 11 17l2 2c1 2 2 3 3 4 1 3 2 5 4 8l10 13c1 1 2 3 4 5s3 6 6 9c1 1 3 3 4 5s2 4 3 5c1 2 2 4 3 5l7 11 1 3c1 1 1 2 2 3l1 2 2 3 2 3h0c1 1 2 3 3 4v1h1l2 4 5 6c1 1 1 3 2 4l2 3 3 3 3 5h-1v2h1v-1c0-1 1-1 2-2v2l-3 3c-1-1-2-1-2-2l-1 2v2c1 0 2 0 3 1 1 0 2 0 2-1h1l1-1c0-1-1 0 0-1h1c1-3 4-5 6-7v1 1l-1 1 1 2h1c1 0 2 2 3 3h0s-1 0-1-1c-2-2-3-2-5-3h-1c1 3 6 8 6 9-2-2-4-3-5-5l-1-2h-1c0 2 2 5 4 7h-1c-1-2-3-4-4-5h-1l3 4h-1l-3-4c0 1 1 3 1 4 1 1 1 2 1 3h0c-1 0-1-2-2-2v2l-1 2c-2 1-2 2-2 4-1 0-1-1-1-2h-1v2c-1-1-1-4-2-5h-1v-4l-1 1c0 1 0 2-1 3h-1c1-2 1-2 1-4 1-1 1-2 1-3h-1c0 1-1 3-2 4 0 2 0 2-1 4h-1c1-1 2-2 2-3s-1-1 0-2h0c0-2 1-3 2-5l-2-2c-2 0-3 3-5 3 1 0 0 0 1-1l3-3v-1h-1c-2 3-5 5-8 8h0c3-4 6-7 8-11-1 1-2 2-4 3 1-1 1-2 2-3h0c-2 1-3 3-5 4h0l4-5v-1h-1-1c0-1 0-1 1-2l-1-1c-1 2-2 2-4 3h0c1-1 3-2 3-4h-1v-2l-3 2c0-1 2-2 2-3l-2-2v-1c-1 0-2 0-3 1h-4c2 0 4-1 6-1 0-1 0-1 1-1l-1-2 1-1h0c2 1 3 1 3 3 1 1 2 3 3 5 0-2 0-3-1-4v-2l2 2v-1c-1-1-1-2-2-3l-2-3c-1-1-1-3-2-4l-3-3c-1-2-1-3-2-4h0l-1-2-2-2c0-1-1-1-1-2l-8-14c-2-2-4-5-5-7-1-3-3-3-4-6l-2-2c-3-5-7-11-11-16-2-2-4-5-6-7-1-4-4-7-6-10-2-2-3-3-4-5l-2-3c-1-1-2-3-3-4l-1-1c0-1-1-2-2-2 0-2 1-1 0-2-2-2-4-4-5-6 0-1 0-1-1-2l-1-2-1-1-1-1c0-1-1-1-1-2h-1l-1-1c0-2 1-1 0-2-1 0-1-1-2-2 0-1-1-1-1-2-1-1-1-1-2-1 0-1-2-2-2-3l-1-1-1-2c-1-2-3-2-4-4l-1-1c-1-1-1-2-2-3s-2-2-2-3-1-1-2-2l-1-2c-1-1-2-2-2-3l-2-2c0-1 0-1-1-2h0c-1-2-2-3-2-4l-2-3-6-6c0-1-1-2-1-2-3-2-6-5-7-8-1 0-2-2-2-2v-1c0-3-4-6-5-8 0-1-1-4-1-5z" class="O"></path><path d="M372 356c1 1 3 3 4 5 0 1 1 2 2 3 0 1 4 5 4 5l7 9c2 3 4 6 6 8l6 10 10 14c2 2 4 4 5 6l15 20c1 2 2 4 3 5 1 2 3 4 4 6 2 2 3 5 5 7 2 3 4 5 5 8-2-2-3-3-4-5l-2-3c-1-1-2-3-3-4l-1-1c0-1-1-2-2-2 0-2 1-1 0-2-2-2-4-4-5-6 0-1 0-1-1-2l-1-2-1-1-1-1c0-1-1-1-1-2h-1l-1-1c0-2 1-1 0-2-1 0-1-1-2-2 0-1-1-1-1-2-1-1-1-1-2-1 0-1-2-2-2-3l-1-1-1-2c-1-2-3-2-4-4l-1-1c-1-1-1-2-2-3s-2-2-2-3-1-1-2-2l-1-2c-1-1-2-2-2-3l-2-2c0-1 0-1-1-2h0c-1-2-2-3-2-4l-2-3-6-6c0-1-1-2-1-2-3-2-6-5-7-8-1 0-2-2-2-2v-1c0-3-4-6-5-8 0-1-1-4-1-5zm430-193l2-2c1 0 1 0 3 1 0 2-4 6-5 8-1 1-2 1-3 2-2 3-4 6-6 8-1 2-3 3-4 5-4 4-7 9-10 13l-10 12c-1 2-2 3-3 5v1h0c-1 1-1 2-2 3v1l-3 3c0 1 0 1-1 2 0 0-1 1-1 2l-2 2s-1 1-1 2l-1 1h0c-1 1-1 2-2 2l-1 1v2c-2 1-3 3-4 4-1 3-3 5-5 7l-2 3-2 4-5 7-2 2-1 1v1l-3 4c-1 1-1 2-2 2l-2 4c-1 0-1 1-2 2l-1 1-1 2s-1 1-1 2c-1 2-3 3-4 5l-1 1-7 9-1 1c0 2-1 3-2 4 0 1-1 1-2 2l-44 61 1 1 2-2v1l-3 3h-1c0 2-2 3-3 5l-5 7c-1 1-1 3-2 4l-8 12-2 3-4 6-2 2-1 1c0 1-1 1-1 2-2 2 0-1-1 1l-2 2-1 3c-1 0-1 1-1 2h-1v1c0 2-1 3-2 5l-2 3-1 1c-1 1-2 2-3 4-2 4-5 8-8 12l-1 1c0 1-1 1-2 2-2 2-3 4-5 5-2 0-1 0-2 1l-3 3h-1c-3 2-6 9-9 9h-1l-6 6c0-1 4-5 5-6 6-6 10-14 15-20l-1-1c-1 0-1 0-2 1l-2 3c-1 1-1 2-2 2 0 1-1 2-2 3 0 2-3 5-5 5 1-2 4-4 5-6 2-4 6-9 9-13h-1l1-1c0-1 1-1 1-1 0-1 1-2 1-2 1-2 0-1 1-2h1v-1h-1c1-1 2-2 2-3 1-1 3-4 3-5v-1h-1l3-3c1-2 1-4 4-5 0-1 1-1 1-2l3-3 1 1c1-2 1-3 2-4 0-1 1-1 1-2 1-1 1-2 3-3 1 0 1-1 2-2l4-6c1-1 1-1 1-2 2-3 4-6 7-8l4-6 2-2 1 2 11-13 59-79 11-16c0-3 4-6 6-10l-1-1 16-23 2 1 55-75z" class="B"></path><path d="M745 237l2 1-23 33c0-3 4-6 6-10l-1-1 16-23z" class="N"></path><path d="M613 426c1 0 2-1 4-2-4 7-9 13-13 19-4 4-7 10-11 14-3 2-6 9-9 9l20-28 9-12z" class="O"></path><path d="M628 402c3-5 8-11 11-16l15-19c2-2 5-4 7-7l15-22 17-21c3-4 6-9 9-12l-44 61c-3 2-6 8-9 11l-11 17c-1 0-3 3-3 3l-3 1v1l-1 1h0l-2 2h-1z" class="P"></path><path d="M642 377l1 2c-6 7-11 15-16 23s-11 16-16 24c-1 2-3 5-5 7-3 4-6 9-9 13l-1-1c-1 0-1 0-2 1l-2 3c-1 1-1 2-2 2 0 1-1 2-2 3 0 2-3 5-5 5 1-2 4-4 5-6 2-4 6-9 9-13h-1l1-1c0-1 1-1 1-1 0-1 1-2 1-2 1-2 0-1 1-2h1v-1h-1c1-1 2-2 2-3 1-1 3-4 3-5v-1h-1l3-3c1-2 1-4 4-5 0-1 1-1 1-2l3-3 1 1c1-2 1-3 2-4 0-1 1-1 1-2 1-1 1-2 3-3 1 0 1-1 2-2l4-6c1-1 1-1 1-2 2-3 4-6 7-8l4-6 2-2z" class="O"></path><path d="M611 416c0-1 1-1 1-2l3-3 1 1-6 8 1 1h-1c-1 1-1 2-2 4 0 1-1 1-1 2-2 2-2 3-3 5s-3 3-4 5l-3 3h-1l1-1c0-1 1-1 1-1 0-1 1-2 1-2 1-2 0-1 1-2h1v-1h-1c1-1 2-2 2-3 1-1 3-4 3-5v-1h-1l3-3c1-2 1-4 4-5z" class="Q"></path><path d="M658 366l1 1 2-2v1l-3 3h-1c0 2-2 3-3 5l-5 7c-1 1-1 3-2 4l-8 12-2 3-4 6-2 2-1 1c0 1-1 1-1 2-2 2 0-1-1 1l-2 2-1 3c-1 0-1 1-1 2h-1v1c0 2-1 3-2 5l-2 3-1 1c-1 1-2 2-3 4-2 4-5 8-8 12l-1 1c0 1-1 1-2 2-2 2-3 4-5 5-2 0-1 0-2 1l-3 3h-1c4-4 7-10 11-14 4-6 9-12 13-19-2 1-3 2-4 2 1-2 2-5 4-7 3-5 7-12 11-17h1l2-2h0l1-1v-1l3-1s2-3 3-3l11-17c3-3 6-9 9-11z" class="B"></path><path d="M617 419c2 0 4-2 5-3-1 2-3 7-5 8s-3 2-4 2c1-2 2-5 4-7z" class="F"></path><path d="M628 402h1l2-2h0l1-1v-1l3-1-9 13c-1 2-2 5-4 6-1 1-3 3-5 3 3-5 7-12 11-17z" class="O"></path><path d="M646 356c-1 2 0 3 0 4-1 1-2 3-3 5 0 1 0 1 1 1 0 2-1 2-2 3h-1c-1 1-2 2-3 4h1l-1 1v1c1 1 2 1 2 3v1l-4 6c-3 2-5 5-7 8 0 1 0 1-1 2l-4 6c-1 1-1 2-2 2-2 1-2 2-3 3 0 1-1 1-1 2-1 1-1 2-2 4l-1-1-3 3c0 1-1 1-1 2-3 1-3 3-4 5l-3 3h1v1c0 1-2 4-3 5 0 1-1 2-2 3h1v1h-1c-1 1 0 0-1 2 0 0-1 1-1 2 0 0-1 0-1 1l-1 1h1c-3 4-7 9-9 13-1 2-4 4-5 6 2 0 5-3 5-5 1-1 2-2 2-3 1 0 1-1 2-2l2-3c1-1 1-1 2-1l1 1c-5 6-9 14-15 20-1 1-5 5-5 6l6-6c0 3-5 7-7 9-1 1-3 3-4 5-2 4-5 7-8 11l-10 13c-3 4-6 10-10 13v1l-3 3c-1 3-3 5-5 7 0-3 2-7 1-10 2-4 4-8 5-12l4-6c1-5 4-9 6-14 1-2 2-4 2-6 0-3 2-5 4-7v-1l3-2c0-2 1-2 2-3s2-2 2-3l2-2c1-2-1 0 1-2l1-1c1-2 1-2 3-3l29-41c0-1 0-1 1-2h0c1-2 2-3 2-4 1-1 1-2 0-3-1 2-1 3-3 3l-1 1c0-2 1-2 2-3l1-2 2-2 1-2 3-4s1-1 1-2l9-11 10-14c0 2-1 3-1 4l3-3v1h2c1-2 3-3 4-5 0 0 0-1 1-1h1 0c2-3 5-6 7-10z" class="K"></path><path d="M583 459c2 0 5-3 5-5 1-1 2-2 2-3 1 0 1-1 2-2l2-3c1-1 1-1 2-1l1 1c-5 6-9 14-15 20-1 1-5 5-5 6l6-6c0 3-5 7-7 9-1 1-3 3-4 5-2 4-5 7-8 11l-10 13c-3 4-6 10-10 13v1l-3 3c-1 3-3 5-5 7 0-3 2-7 1-10 2-4 4-8 5-12l1 1c0 5-2 7-4 11 4-2 8-7 10-11 2-2 4-4 6-7 1-1 1-2 2-3 1-2 2-3 3-5 0-1 1-1 1-2 1-1 2-2 2-3l1-1 6-9c1-2 3-4 5-6l1-1c1-2 3-3 3-5h1c0-1 1-2 1-3h1c0-2 0-2 1-3zm8-28v1l-2 4c-1 1-1 3-2 5 1-2 2-3 3-4 1-2 3-3 4-4h0v2c-1 2-2 2-2 3l-1 1-1 2-1 1c0 1-1 1-1 2-2 2 0-1-1 1 0 1-1 1-2 2 0 1-1 1-1 2s-1 1-2 2c0 1-1 1-1 2l-2 2-1 2s-1 1-1 2l-2 2-1 1c0 1-1 1-1 2-2 2 0-1-1 1 0 1-1 1-2 2 0 1-1 1-1 2s-1 1-2 2c0 1-1 1-1 2-1 1-2 3-3 4l-2 2c0 1-1 2-1 2l-3 5-4 6-3 3-1 2-2 2-1 1c1-5 4-9 6-14 1-2 2-4 2-6 0-3 2-5 4-7v-1l3-2c0-2 1-2 2-3s2-2 2-3l2-2c1-2-1 0 1-2l1-1c1-2 1-2 3-3 3-2 4-6 7-9l12-16z" class="Q"></path><path d="M646 356c-1 2 0 3 0 4-1 1-2 3-3 5 0 1 0 1 1 1 0 2-1 2-2 3h-1c-1 1-2 2-3 4h1l-1 1v1c1 1 2 1 2 3v1l-4 6c-3 2-5 5-7 8 0 1 0 1-1 2l-4 6c-1 1-1 2-2 2-2 1-2 2-3 3 0 1-1 1-1 2-1 1-1 2-2 4l-1-1-3 3c0 1-1 1-1 2-3 1-3 3-4 5l-3 3-1 1-2 5c0-1 1-3 0-3-2 2-4 7-7 8h0v-2h0c-1 1-3 2-4 4-1 1-2 2-3 4 1-2 1-4 2-5l2-4v-1l-12 16c-3 3-4 7-7 9l29-41c0-1 0-1 1-2h0c1-2 2-3 2-4 1-1 1-2 0-3-1 2-1 3-3 3l-1 1c0-2 1-2 2-3l1-2 2-2 1-2 3-4s1-1 1-2l9-11 10-14c0 2-1 3-1 4l3-3v1h2c1-2 3-3 4-5 0 0 0-1 1-1h1 0c2-3 5-6 7-10z" class="D"></path><defs><linearGradient id="z" x1="614.022" y1="413.866" x2="618.904" y2="381.623" xlink:href="#B"><stop offset="0" stop-color="#15171a"></stop><stop offset="1" stop-color="#43413e"></stop></linearGradient></defs><path fill="url(#z)" d="M631 376v1c-1 2-1 3-2 4l1 1c-1 2-4 6-5 8l-8 11c-2 3-5 7-6 10-2 1-3 0-5 1l-1 1v-1l26-36z"></path><path d="M615 407c-1 1-1 1-1 3v1l-4 4 1 1c-3 1-3 3-4 5l-3 3-1 1-2 5c0-1 1-3 0-3-2 2-4 7-7 8h0v-2h0c-1 1-3 2-4 4-1 1-2 2-3 4 1-2 1-4 2-5l2-4v-1l14-19v1l1-1c2-1 3 0 5-1 1-1 2-3 4-4z" class="M"></path><defs><linearGradient id="AA" x1="600.797" y1="399.215" x2="632.6" y2="383.083" xlink:href="#B"><stop offset="0" stop-color="#171313"></stop><stop offset="1" stop-color="#484c4d"></stop></linearGradient></defs><path fill="url(#AA)" d="M629 370c0 2-1 3-1 4l3-3v1h2l-20 26-7 10c-1 2-3 5-5 7 0-1 0-1 1-2h0c1-2 2-3 2-4 1-1 1-2 0-3-1 2-1 3-3 3l-1 1c0-2 1-2 2-3l1-2 2-2 1-2 3-4s1-1 1-2l9-11 10-14z"></path><path d="M631 376c1-2 3-4 4-5 0 1-1 3-2 4h1l3-2h0c-1 1-2 2-2 3l3-2v1c1 1 2 1 2 3v1l-4 6c-3 2-5 5-7 8 0 1 0 1-1 2l-4 6c-1 1-1 2-2 2-2 1-2 2-3 3 0 1-1 1-1 2-1 1-1 2-2 4l-1-1-3 3c0 1-1 1-1 2l-1-1 4-4v-1c0-2 0-2 1-3-2 1-3 3-4 4 1-3 4-7 6-10l8-11c1-2 4-6 5-8l-1-1c1-1 1-2 2-4v-1z" class="F"></path><path d="M622 397c1-2 2-5 4-5l1 1v-1c1-1 1-2 3-2 0 1-1 1-1 3-1 1-2 3-3 4-2 0-2 1-3 2h-1v-2z" class="O"></path><path d="M622 397v2h1c1-1 1-2 3-2-2 2-3 4-4 6-2 1-2 2-3 3 0 1-1 1-1 2-1 1-1 2-2 4l-1-1-3 3c0 1-1 1-1 2l-1-1 4-4v-1c0-2 0-2 1-3 0-2 5-9 7-10z" class="P"></path><path d="M312 273v-1c-1-2-2-4-4-5l-6-9 1-1c0 1 1 2 2 2l14 20c3 4 12 18 15 19 1 1 2 2 2 3h1c1 0 2 1 3 3 1 1 2 1 2 3l1 1h1l2 4 2 2 1-1 1 1h2l2 2 20 29h0c2 1 3 2 4 3h0c0-2-2-3-3-5v-2l14 18s1 1 1 2l4 6 3 3c1 2 2 3 4 5 1 2 2 4 4 6l2 3c0 1 1 2 2 3l3 3h0 1l2 3 1 1 1 2c1 1 1 2 2 3 0 1 1 2 2 3 0 1 1 2 2 2 0 1 1 2 1 2l1 2c1 1 3 4 4 6l1 1h0l2 2 1 2c1 1 2 2 3 4l3 5c1 0 1 1 2 2l1 2c1 0 1 1 2 2l1 2c1 0 1 1 2 2l1 2c1 0 1 1 2 2 0 1 0 0 1 1 0 1 1 2 1 2l3 5 3 3c0 1 0 1 1 2 0 0 1 1 1 2l3 3c0 1 1 2 1 2 2 2 0-1 1 1 1 1 1 2 2 2v2h1l2 4c2 2 3 3 4 5 1 1 1 1 1 2 1 0 1 1 2 2l8 12c0 1 1 2 2 3l1 2h1c1-1 1-1 2 0l1 1 6 12v3c-3-1-4-5-5-7l-1 1c1 2 3 5 4 7 2 2 4 4 4 7h0-1-2l-1-3h-1c-1-1-1-2-1-3h-1 0-1c0-1-1-2-2-2l-1 1 3 4h1c1 1 1 2 1 3-2-1-2-3-4-4v1l2 3-1 1-1-3-7-11c-1-1-2-3-3-5-1-1-2-3-3-5s-3-4-4-5c-3-3-4-7-6-9s-3-4-4-5c-4-5-7-9-10-13-2-3-3-5-4-8-1-1-2-2-3-4l-2-2c-3-6-8-12-11-17l-14-20-2-1c0-2-1-2-2-3 0 0-1 0-1-1l-1-1-1-2c0-1-1-1-1-2h-1c0-2 1-1 0-2-1 0-2-1-2-2l-1-1-1-1c0-1 0-2-1-2 0-1-1-2-1-3l-2-1c-1-2-3-4-4-6v-1l-6-5v-1c-1-1-2-1-3-2l-7-9s-4-4-4-5c-1-1-2-2-2-3-1-2-3-4-4-5l-7-10c-3-5-7-9-10-14l-29-40-10-14c-1-1-3-3-4-5z" class="K"></path><path d="M312 273v-1c-1-2-2-4-4-5l-6-9 1-1c0 1 1 2 2 2l14 20c3 4 12 18 15 19 1 1 2 2 2 3h1c1 0 2 1 3 3 1 1 2 1 2 3l1 1h1l2 4 2 2 1-1 1 1h2l2 2 20 29h0c2 1 3 2 4 3h0c0-2-2-3-3-5v-2l14 18s1 1 1 2l4 6 3 3c1 2 2 3 4 5 1 2 2 4 4 6l2 3c0 1 1 2 2 3l3 3h0 1l2 3 1 1 1 2c1 1 1 2 2 3l-4-2 1 1c0 1 0 2 1 3 0 2 2 3 3 5h-1c-1-1-2-2-3-2l52 79h0c-4-5-9-10-12-15-4-5-6-10-10-15-4-7-9-13-13-20-1-2-2-3-2-5-3-5-23-36-26-37 4 6 25 34 25 38-3-1-4-6-6-8-2-3-5-6-7-10-2-3-5-6-7-9l-6-9c-1-1-2-2-3-4-2-2-4-7-7-8l-2-2c-4-3-6-7-9-11l-1 1s-4-4-4-5c-1-1-2-2-2-3-1-2-3-4-4-5l-7-10c-3-5-7-9-10-14l-29-40-10-14c-1-1-3-3-4-5z" class="E"></path><path d="M375 341l14 18s1 1 1 2c-1 0-1 0-1 1 1 1 1 2 2 3 0 1 1 2 1 2h-2c-1 0-1-1-1-1-3-3-5-6-7-9 0-2-1-4-3-5s-3-4-5-7c2 1 3 2 4 3h0c0-2-2-3-3-5v-2z" class="F"></path><path d="M390 367h2s-1-1-1-2c-1-1-1-2-2-3 0-1 0-1 1-1l4 6 3 3c1 2 2 3 4 5 1 2 2 4 4 6l2 3c0 1 1 2 2 3l3 3h0 1l2 3 1 1 1 2c1 1 1 2 2 3l-4-2 1 1c0 1 0 2 1 3 0 2 2 3 3 5h-1c-1-1-2-2-3-2-4-4-6-10-10-14-3-3-5-6-7-10-2-3-5-6-6-10h-1c0 1 1 1 0 2-1-2-1-3-2-5h0z" class="N"></path><path d="M247 159c4-1 8 0 13 0 1 0 4-1 5 0h1 6 12 10l5 6c0 2 2 3 2 4l1 1h1 0l2 2c1 0 5 0 6-1 3 0 12 0 14 2 1 0 1 1 2 1 0 1 1 2 2 4h1c-3-1-5-2-7-1h-1c-1 1-2 1-4 2-2 0-4-1-6-1s-3-1-4-2h0l-3 1c-1 0-2 0-2 1-2 0-9 0-10 1h-5l-2-1v1c1 1 2 2 2 3-1-1-2-3-4-3-1-1-6 0-7 0v1l1 3h-5l1 1c0 2 0 3-1 5h0c1 1 1 2 0 3h0l3 4 2 3 5 7 1 1 1 1 7 10c1 1 1 2 2 2 3 5 7 9 9 13l3 4s1 1 1 2l1 1 1 1 6 8-1 1 4 5 2 2 1 1 2 4 1 1 1 2c0 1-2 3-3 4h-1c-1-1-1-2-3-3 1 1 1 2 2 3l2 2 1 2c1 1 2 2 2 3 1 3 3 5 5 7l8 11 11 15c2 2 4 4 5 7l-2-2h-2l-1-1-1 1-2-2-2-4h-1l-1-1c0-2-1-2-2-3-1-2-2-3-3-3h-1c0-1-1-2-2-3-3-1-12-15-15-19l-14-20c-1 0-2-1-2-2l-1 1 6 9c2 1 3 3 4 5v1c-4-4-7-9-10-13l-31-42c-13-17-27-33-39-52 0-1 0-1-1-2l-1-2h1 1c1 0 1-1 2-1h1c4 2 7 6 9 10 3-2 6-2 10-1v-1l-7-10z" class="H"></path><path d="M250 182c-2-2-4-5-6-8-3-4-6-9-10-12l1-1c4 2 7 6 9 10l21 29c0-2-2-5-3-6v-1c1 0 1 1 2 1l1 1c2 2 4 5 6 8l11 15c2 3 6 7 7 10 3 2 5 6 6 9l6 7c3 4 8 9 9 13-2-1-4-4-6-6h-1v1 1c-6-6-11-14-16-20l-2-3c-2-4-7-7-9-12l-1-1c-1-1-2-1-2-3v-1l-18-23c-2-3-3-5-5-8z" class="G"></path><path d="M265 200c0-2-2-5-3-6v-1c1 0 1 1 2 1l1 1c2 2 4 5 6 8l11 15c2 3 6 7 7 10-2-1-4-4-5-6-2-2-3-4-4-6l-1 1 1 2-1 1-12-16c-1-2-2-3-2-4z" class="C"></path><path d="M231 164l-1-2h1 1c1 0 1-1 2-1h1l-1 1c4 3 7 8 10 12 2 3 4 6 6 8 2 3 3 5 5 8l18 23v1c0 2 1 2 2 3l1 1c2 5 7 8 9 12l2 3c5 6 10 14 16 20v-1-1h1c2 2 4 5 6 6 2 0 2 1 2 2l5 6s1 0 1 1h0c1 1 1 2 2 3l2 2 1 2c1 1 2 2 2 3 1 3 3 5 5 7l8 11 11 15c2 2 4 4 5 7l-2-2h-2l-1-1-1 1-2-2-2-4h-1l-1-1c0-2-1-2-2-3-1-2-2-3-3-3h-1c0-1-1-2-2-3-3-1-12-15-15-19l-14-20c-1 0-2-1-2-2l-1 1 6 9c2 1 3 3 4 5v1c-4-4-7-9-10-13l-31-42c-13-17-27-33-39-52 0-1 0-1-1-2z" class="D"></path><path d="M231 164l-1-2h1 1c1 0 1-1 2-1h1l-1 1c4 3 7 8 10 12 2 3 4 6 6 8-3-1-4-6-7-8l-1 1 5 6h-1l-1-2-9-12c-1-2-2-3-4-4l-1 1z" class="C"></path><path d="M334 298c0-1-1-2-2-3l-3-4c0-1-1-1-1-2-1-2-3-4-4-5l-13-18h0l-2-2c-3-5-7-10-11-15l-15-19-11-15-3-3v-1c1 0 1 1 2 2l1 1h1c0 2 1 2 2 3l1 1c2 5 7 8 9 12l2 3c5 6 10 14 16 20v-1-1h1c2 2 4 5 6 6 2 0 2 1 2 2l5 6s1 0 1 1h0c1 1 1 2 2 3l2 2 1 2c1 1 2 2 2 3 1 3 3 5 5 7l8 11 11 15c2 2 4 4 5 7l-2-2h-2l-1-1-1 1-2-2-2-4h-1l-1-1c0-2-1-2-2-3-1-2-2-3-3-3h-1c0-1-1-2-2-3z" class="J"></path><path d="M349 313l-3-4 1-1 1 2 1-1c2 2 4 4 5 7l-2-2h-2l-1-1z" class="G"></path><path d="M247 159c4-1 8 0 13 0 1 0 4-1 5 0h1 6 12 10l5 6c0 2 2 3 2 4l1 1h1 0l2 2c1 0 5 0 6-1 3 0 12 0 14 2 1 0 1 1 2 1 0 1 1 2 2 4h1c-3-1-5-2-7-1h-1c-1 1-2 1-4 2-2 0-4-1-6-1s-3-1-4-2h0l-3 1c-1 0-2 0-2 1-2 0-9 0-10 1h-5l-2-1v1c1 1 2 2 2 3-1-1-2-3-4-3-1-1-6 0-7 0v1l-2-1h-11v1c-1-2-2-3-4-4v2c-2 1-5 0-8 0 5 5 9 11 13 17l-1-1c-1 0-1-1-2-1v1c1 1 3 4 3 6l-21-29c3-2 6-2 10-1v-1l-7-10z" class="D"></path><path d="M265 159h1c0 1 2 2 2 3v1l5 8c-5-1-10 0-16 0-3-3-6-8-9-12h17z" class="Q"></path><defs><linearGradient id="AB" x1="276.512" y1="159.449" x2="276.434" y2="169.499" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#AB)" d="M266 159h6 12l1 1c0 1 1 1 1 2l1 1 1 1c2 2 3 4 5 6 1 1 1 2 1 3h-1c0-1-1-1-2-1h-1-3c-2 1-4 1-5 1v1 1c-2-1-4-2-5-3-4-3-6-6-9-9h0v-1c0-1-2-2-2-3z"></path><defs><linearGradient id="AC" x1="291.85" y1="158.359" x2="294.245" y2="169.588" xlink:href="#B"><stop offset="0" stop-color="#0f0e0f"></stop><stop offset="1" stop-color="#363536"></stop></linearGradient></defs><path fill="url(#AC)" d="M284 159h10l5 6c0 2 2 3 2 4l1 1h1 0l2 2c1 0 5 0 6-1 3 0 12 0 14 2 1 0 1 1 2 1 0 1 1 2 2 4h1c-3-1-5-2-7-1h-1c-1 1-2 1-4 2-2 0-4-1-6-1s-3-1-4-2h0l-3 1h-3c-1-1-3-1-3-1-1-1-1-1-2-1h0c-1 0-2-1-3-1h-1-2v-1c-2 0-3 0-4-1h3 1c1 0 2 0 2 1h1c0-1 0-2-1-3-2-2-3-4-5-6l-1-1-1-1c0-1-1-1-1-2l-1-1z"></path><defs><linearGradient id="AD" x1="464.286" y1="425.123" x2="425.877" y2="439.317" xlink:href="#B"><stop offset="0" stop-color="#0a080b"></stop><stop offset="1" stop-color="#2f2f2e"></stop></linearGradient></defs><path fill="url(#AD)" d="M305 177l3-1h0c1 1 2 2 4 2s4 1 6 1c2-1 3-1 4-2h1c2-1 4 0 7 1l27 39 1-1c1 1 1 2 2 3h0c1 1 2 2 2 3 1 2 3 4 4 5s2 3 3 4c0 1-1 1-1 1 1 2 3 5 4 6h-1c-2-2-3-5-5-6h0l5 22v5c2 2 4 8 4 10h0 0c0 1 1 2 1 3l-2-2-2 1c-3 0-4-1-6-2 2 2 5 5 6 8l1-1 1 1v-7l6 8c2 2 3 3 4 5l4 4c0 2 4 6 5 7l12 16c1 2 3 4 4 5h0c2 2 4 4 5 7 2 2 2 4 5 5h0c5 13 11 26 17 39 0 2 2 5 2 7 4 6 6 13 8 19l24 54c3 6 5 13 8 19 2 5 5 9 6 14l8 19-1-1c-1-1-1-1-2 0h-1l-1-2c-1-1-2-2-2-3l-8-12c-1-1-1-2-2-2 0-1 0-1-1-2-1-2-2-3-4-5l-2-4h-1v-2c-1 0-1-1-2-2-1-2 1 1-1-1 0 0-1-1-1-2l-3-3c0-1-1-2-1-2-1-1-1-1-1-2l-3-3-3-5s-1-1-1-2c-1-1-1 0-1-1-1-1-1-2-2-2l-1-2c-1-1-1-2-2-2l-1-2c-1-1-1-2-2-2l-1-2c-1-1-1-2-2-2l-3-5c-1-2-2-3-3-4l-1-2-2-2h0l-1-1c-1-2-3-5-4-6l-1-2s-1-1-1-2c-1 0-2-1-2-2-1-1-2-2-2-3-1-1-1-2-2-3l-1-2-1-1-2-3h-1 0l-3-3c-1-1-2-2-2-3l-2-3c-2-2-3-4-4-6-2-2-3-3-4-5l-3-3-4-6c0-1-1-2-1-2l-14-18v2c1 2 3 3 3 5h0c-1-1-2-2-4-3h0l-20-29c-1-3-3-5-5-7l-11-15-8-11c-2-2-4-4-5-7 0-1-1-2-2-3l-1-2-2-2c-1-1-1-2-2-3 2 1 2 2 3 3h1c1-1 3-3 3-4l-1-2-1-1-2-4-1-1-2-2-4-5 1-1-6-8-1-1-1-1c0-1-1-2-1-2l-3-4c-2-4-6-8-9-13-1 0-1-1-2-2l-7-10-1-1-1-1-5-7-2-3-3-4h0c1-1 1-2 0-3h0c1-2 1-3 1-5l-1-1h5l-1-3v-1c1 0 6-1 7 0 2 0 3 2 4 3 0-1-1-2-2-3v-1l2 1h5c1-1 8-1 10-1 0-1 1-1 2-1z"></path><path d="M397 370c0-2 0-2-1-3 0-1-1-2-1-3 0-2-2-5-2-7h1c0 1 0 1 1 2s1 1 3 1c1 1 1 1 1 2 3 4 8 8 9 13-1-1-2-2-2-3l-2-1-1 1h-1c0 2 1 4 2 6h0c1 1 2 3 3 4s2 3 2 5c-1-1-2-2-2-3l-2-3c-2-2-3-4-4-6-2-2-3-3-4-5z" class="O"></path><path d="M444 417c2 1 2 2 3 3l1 2c1 1 1 2 2 2v1l1-1 2 4 2 2 3 4 3 3c0 1 1 2 2 2 1 1 0 0 0 2 1 1 3 1 3 3 1 1 2 2 4 2 3 6 5 13 8 19 2 5 5 9 6 14v-1c-1-1-2-1-3-3l-3-3c0-1 0 0-1-1l-1-2c-1-1-2-2-2-3s-1-2-1-2l-1-1-1-2-2-2-1-2-1-1-4-5c-1-2-1-3-2-4l-1-1-4-6-4-4c0-1 1 0 0-1l-2-2v-1l-2-1c0-2 1-1 0-2-1 0-1-1-2-1 0-1 0-1-1-2 0-1-1-1-2-3h1l1 2c1-2 0-3 1-5-1-1-1-1-1-2l-1-1z" class="K"></path><defs><linearGradient id="AE" x1="456.092" y1="394.441" x2="369.908" y2="363.559" xlink:href="#B"><stop offset="0" stop-color="#090809"></stop><stop offset="1" stop-color="#4e4e4f"></stop></linearGradient></defs><path fill="url(#AE)" d="M388 347c-1-2-2-4-4-7-1-1-3-3-3-4 0-2-1-3-1-4-1-1-1-2-1-2 1 1 1 0 1 1l3 3 1 1c0 1 1 2 2 3 0 1 1 1 2 2l5 7c1 1 1 2 2 3l1-1c1 1 1 2 2 3 1 2 2 3 3 4l2 2c0 1 1 2 1 2l2 2 2 4 4 5 1 2 4 4-1 1 1 1v-1l1 1-1 1 2 1h0v1l1 1c0 1 1 2 2 3l1 1 3 4c1 2 3 4 4 6l5 7c1 1 2 3 3 5 1 0 1 1 2 1l1 1 1 2c0 1 1 2 1 2l1 1v1l1 1c0 1 0 1 1 2-1 2 0 3-1 5l-1-2h-1l-2-2-4-6-4-6s-1-1-1-2l-2-2s-1-1-1-2l-2-2s-1-1-1-2h-1v-1l-3-4c-1-1-1-2-2-2v-1l-2-2c-1 0-2-1-2-2l-2-3c-1-1-2-3-3-4l-2-3c-1-1-1-1-1-2-1-5-6-9-9-13 0-1 0-1-1-2l-4-5c0-1-1-1-1-2l-3-4c0-1 0-1-2-2z"></path><defs><linearGradient id="AF" x1="440.467" y1="377.378" x2="406.917" y2="391.199" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#454445"></stop></linearGradient></defs><path fill="url(#AF)" d="M396 349c-1-1-1-2-2-3h0l-7-9v-1l-3-4c-1 0-2-1-2-2 0-2 0-4 1-6l4 5 2 1 3 3c0 2 1 2 2 3l1 1c1 2 3 3 4 4 0 2 1 2 2 3 1 2 3 3 4 4 0 2 1 2 2 3 1 2 3 4 5 5 0 1 1 2 1 2 1 2 2 2 3 4s3 2 4 4v1c2 1 5 5 6 5l7 8c1 2 4 4 5 6s2 3 4 5c1 1 0 0 0 1l2 1h0v-3l-1-3-3-7c0-1 0-1-1-2-1-2-1-3-1-5 4 6 6 13 8 19l24 54c-2 0-3-1-4-2 0-2-2-2-3-3 0-2 1-1 0-2-1 0-2-1-2-2l-3-3-3-4-2-2-2-4-1 1v-1c-1 0-1-1-2-2l-1-2c-1-1-1-2-3-3v-1l-1-1s-1-1-1-2l-1-2-1-1c-1 0-1-1-2-1-1-2-2-4-3-5l-5-7c-1-2-3-4-4-6l-3-4-1-1c-1-1-2-2-2-3-1-1 1 1-1-1v-1h0l-2-1 1-1-1-1v1l-1-1 1-1-4-4-1-2-4-5-2-4-2-2s-1-1-1-2l-2-2c-1-1-2-2-3-4-1-1-1-2-2-3z"></path><defs><linearGradient id="AG" x1="356.125" y1="291.071" x2="327.249" y2="296.739" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#d0d0d0"></stop></linearGradient></defs><path fill="url(#AG)" d="M318 266c2 1 2 2 3 3h1c1-1 3-3 3-4l-1-2-1-1-2-4-1-1-2-2-4-5 1-1 16 21c0 1 0 1 1 2l3 6c4 4 8 9 11 14 2 2 5 6 7 8 1 0 2 0 3 1h0 1c-1-2-1-3-1-5-1-1-1-1-1-3 0-3-5-6-7-8v-2c1 0 2 1 3 3 2 1 7 8 9 8h0c2 1 2 2 3 4l5 5c1 1 0 0 1 2h1c1 1 1 1 1 2l4 5c3 4 7 8 10 12 0 1 1 1 2 2s2 2 2 3c1 1 2 2 3 4l-3-3-2-1-4-5c-1 2-1 4-1 6 0 1 1 2 2 2l3 4v1l7 9h0c1 1 1 2 2 3l-1 1c-1-1-1-2-2-3l-5-7c-1-1-2-1-2-2-1-1-2-2-2-3l-1-1-3-3c0-1 0 0-1-1 0 0 0 1 1 2 0 1 1 2 1 4 0 1 2 3 3 4 2 3 3 5 4 7 2 1 2 1 2 2l3 4c0 1 1 1 1 2l4 5c-2 0-2 0-3-1s-1-1-1-2h-1c0 2 2 5 2 7 0 1 1 2 1 3 1 1 1 1 1 3l-3-3-4-6c0-1-1-2-1-2l-14-18v2c1 2 3 3 3 5h0c-1-1-2-2-4-3h0l-20-29c-1-3-3-5-5-7l-11-15-8-11c-2-2-4-4-5-7 0-1-1-2-2-3l-1-2-2-2c-1-1-1-2-2-3z"></path><path d="M325 276c5 4 8 10 12 15l28 36c3 5 7 9 10 14v2c1 2 3 3 3 5h0c-1-1-2-2-4-3h0l-20-29c-1-3-3-5-5-7l-11-15-8-11c-2-2-4-4-5-7z" class="L"></path><defs><linearGradient id="AH" x1="367.297" y1="317.949" x2="362.305" y2="320.009" xlink:href="#B"><stop offset="0" stop-color="#797879"></stop><stop offset="1" stop-color="#8f8f8f"></stop></linearGradient></defs><path fill="url(#AH)" d="M365 327h1l-1-2-1-1v-3l-7-10v-1l1 1h0 1l1-1c1 2 2 4 4 5 0-1 0-1-1-2 2 1 2 1 3 3l1 2c0 1 0 1 1 1l3 6 1 4v1c0 2 2 3 3 5s1 3 2 5 2 4 4 6v1l3 4h1c0 1 0 1 1 2 1 2 3 3 3 5v1l-14-18c-3-5-7-9-10-14z"></path><defs><linearGradient id="AI" x1="389.14" y1="341.084" x2="375.558" y2="343.876" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#6e6e6e"></stop></linearGradient></defs><path fill="url(#AI)" d="M367 318c3 2 6 5 7 8l2 2c1 1 2 3 3 4l-1 1-1-3c-1 1-1 1-1 2l7 9 2 3c1 1 2 3 3 3 2 1 2 1 2 2l3 4c0 1 1 1 1 2l4 5c-2 0-2 0-3-1s-1-1-1-2h-1c0 2 2 5 2 7 0 1 1 2 1 3 1 1 1 1 1 3l-3-3-4-6c0-1-1-2-1-2v-1c0-2-2-3-3-5-1-1-1-1-1-2h-1l-3-4v-1c-2-2-3-4-4-6s-1-3-2-5-3-3-3-5v-1l-1-4-3-6c-1 0-1 0-1-1z"></path><defs><linearGradient id="AJ" x1="377.371" y1="308.09" x2="360.768" y2="313.246" xlink:href="#B"><stop offset="0" stop-color="#555"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#AJ)" d="M348 283c1 0 2 1 3 3 2 1 7 8 9 8h0c2 1 2 2 3 4l5 5c1 1 0 0 1 2h1c1 1 1 1 1 2l4 5c3 4 7 8 10 12 0 1 1 1 2 2s2 2 2 3c1 1 2 2 3 4l-3-3-2-1-4-5c-1 2-1 4-1 6 0 1 1 2 2 2l3 4v1l7 9h0c1 1 1 2 2 3l-1 1c-1-1-1-2-2-3l-5-7c-1-1-2-1-2-2-1-1-2-2-2-3l-1-1-3-3c0-1 0 0-1-1 0 0 0 1 1 2 0 1 1 2 1 4 0 1 2 3 3 4 2 3 3 5 4 7-1 0-2-2-3-3l-2-3-7-9c0-1 0-1 1-2l1 3 1-1c-1-1-2-3-3-4l-2-2c-1-3-4-6-7-8l-1-2c-1-2-1-2-3-3h0l-5-6-1-2-4-5c1 0 2 0 3 1h0 1c-1-2-1-3-1-5-1-1-1-1-1-3 0-3-5-6-7-8v-2z"></path><defs><linearGradient id="AK" x1="421.869" y1="331.46" x2="394.104" y2="341.548" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3d3c3d"></stop></linearGradient></defs><path fill="url(#AK)" d="M374 277v-7l6 8c2 2 3 3 4 5l4 4c0 2 4 6 5 7l12 16c1 2 3 4 4 5h0c2 2 4 4 5 7 2 2 2 4 5 5h0c5 13 11 26 17 39 0 2 2 5 2 7s0 3 1 5c1 1 1 1 1 2l3 7 1 3v3h0l-2-1c0-1 1 0 0-1-2-2-3-3-4-5s-4-4-5-6l-7-8c-1 0-4-4-6-5v-1c-1-2-3-2-4-4s-2-2-3-4c0 0-1-1-1-2-2-1-4-3-5-5-1-1-2-1-2-3-1-1-3-2-4-4-1-1-2-1-2-3-1-1-3-2-4-4l-1-1c-1-1-2-1-2-3-1-2-2-3-3-4 0-1-1-2-2-3s-2-1-2-2c-3-4-7-8-10-12l-4-5c0-1 0-1-1-2h-1c-1-2 0-1-1-2l-5-5c-1-2-1-3-3-4-2-3-5-5-6-9l1-1c0-3-1-6-2-8s-1-2-1-4l1 2 11 9h0c2 0 2 0 4 1 1-1 2-1 3-2 2-2 2-3 3-5z"></path><defs><linearGradient id="AL" x1="379.756" y1="290.267" x2="368.696" y2="292.827" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#AL)" d="M374 277v-7l6 8h-1v1c1 3 3 6 6 9h-1l-2-2c0-1-4-7-5-8 1 3 1 6 2 9 1 1 1 2 1 3v2c1 3 2 6 2 8v1c0 2 0 4 1 6h0l-5-6-2-2c-3-6-8-10-12-16 2 0 2 0 4 1 1-1 2-1 3-2 2-2 2-3 3-5z"></path><path d="M374 277v-7l6 8h-1l-3-3-2 4c-1 3-3 4-5 5h-1c1-1 2-1 3-2 2-2 2-3 3-5z" class="N"></path><defs><linearGradient id="AM" x1="383.496" y1="300.044" x2="358.617" y2="305.033" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#AM)" d="M352 272l1 2 11 9h0c4 6 9 10 12 16l2 2 5 6 7 10h-1l-2-2h-1v4c1 2 3 3 3 6v1 3c0-1-1-2-2-3s-2-1-2-2c-3-4-7-8-10-12l-4-5c0-1 0-1-1-2h-1c-1-2 0-1-1-2l-5-5c-1-2-1-3-3-4-2-3-5-5-6-9l1-1c0-3-1-6-2-8s-1-2-1-4z"></path><path d="M353 274l11 9h0c4 6 9 10 12 16l2 2h-1c-4-4-7-10-12-14l-1-1h0 0l-1 1-1-1c-2-2-3-4-5-6-1-2-3-3-4-6z" class="C"></path><defs><linearGradient id="AN" x1="359.548" y1="213.036" x2="332.717" y2="222.181" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#AN)" d="M305 177l3-1h0c1 1 2 2 4 2s4 1 6 1c2-1 3-1 4-2h1c2-1 4 0 7 1l27 39 1-1c1 1 1 2 2 3h0c1 1 2 2 2 3 1 2 3 4 4 5s2 3 3 4c0 1-1 1-1 1 1 2 3 5 4 6h-1c-2-2-3-5-5-6h0l5 22v5c2 2 4 8 4 10h0 0c0 1 1 2 1 3l-2-2-2 1c-3 0-4-1-6-2 2 2 5 5 6 8l1-1 1 1c-1 2-1 3-3 5-1 1-2 1-3 2-2-1-2-1-4-1h0l-11-9-1-2c0 2 0 2 1 4s2 5 2 8l-1 1c1 4 4 6 6 9h0c-2 0-7-7-9-8-1-2-2-3-3-3v2c2 2 7 5 7 8 0 2 0 2 1 3 0 2 0 3 1 5h-1 0c-1-1-2-1-3-1-2-2-5-6-7-8-3-5-7-10-11-14l-3-6c-1-1-1-1-1-2l-16-21-6-8-1-1-1-1c0-1-1-2-1-2l-3-4c-2-4-6-8-9-13-1 0-1-1-2-2l-7-10-1-1-1-1-5-7-2-3-3-4h0c1-1 1-2 0-3h0c1-2 1-3 1-5l-1-1h5l-1-3v-1c1 0 6-1 7 0 2 0 3 2 4 3 0-1-1-2-2-3v-1l2 1h5c1-1 8-1 10-1 0-1 1-1 2-1z"></path><path d="M343 233c1 1 2 2 2 4h0v2h1v1l-1 1h1 0l1 1h0-1c0 1 1 2 1 3l-7-8 3-4z" class="D"></path><path d="M368 260c-1-3-7-9-7-12l4 5h1c-1-1-2-2-2-3v-1l7 10c2 2 4 8 4 10h0 0c0 1 1 2 1 3l-2-2-4-6c-1-2-2-2-2-4z" class="F"></path><path d="M354 259l3-1h2l2 2 1-2 1 1c1 1 3 1 4 2l1-1c0 2 1 2 2 4l4 6-2 1c-3 0-4-1-6-2-2-2-5-5-8-6-2 0-3 1-4 2l-1 1c-1 2-1 2-1 3-1 0-1 0-2-1v1l-1-4 4-5 1-1z" class="D"></path><path d="M349 265l4-5h1 1c0 1 0 1-1 2 0 0-1 1-1 2h0l1 1-1 1c-1 2-1 2-1 3-1 0-1 0-2-1v1l-1-4z" class="C"></path><path d="M354 265c1-1 2-2 4-2 3 1 6 4 8 6s5 5 6 8l1-1 1 1c-1 2-1 3-3 5-1 1-2 1-3 2-2-1-2-1-4-1h0l-11-9-1-2c-1-1-2-2-2-3v-1c1 1 1 1 2 1 0-1 0-1 1-3l1-1z" class="K"></path><path d="M372 277l1-1 1 1c-1 2-1 3-3 5-1 0-3 0-4-1v-1c2 0 3-1 5-3z" class="D"></path><path d="M350 269v-1c1 1 1 1 2 1 0-1 0-1 1-3l9 9c1 2 4 3 5 5v1c1 1 3 1 4 1-1 1-2 1-3 2-2-1-2-1-4-1h0l-11-9-1-2c-1-1-2-2-2-3z" class="L"></path><defs><linearGradient id="AO" x1="328.919" y1="187.956" x2="310.973" y2="197.935" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#AO)" d="M305 177l3-1h0c1 1 2 2 4 2s4 1 6 1h1v1h0c6 2 9 6 12 11 1 2 2 5 2 8 1 3 4 7 4 10-1-1-2-1-3-2h-1l-1-1c0 1-1 1-2 1-5-2-11-7-16-11-2-1-4-2-7-2-1 1-2 1-3 1-2 1-3 2-5 3-3-6-7-11-11-16 0-1-1-2-2-3v-1l2 1h5c1-1 8-1 10-1 0-1 1-1 2-1z"></path><path d="M288 182c0-1-1-2-2-3v-1l2 1c1 1 2 1 4 1 1 0 2 0 4 1 1 0 2-1 4 0 1 0 1 0 2 1 0 1 0 1 1 2 0 1 2 2 2 3 2 2 4 5 6 7h-4c-1 1-2 1-3 1-2 1-3 2-5 3-3-6-7-11-11-16z" class="J"></path><path d="M307 194c3 0 5 1 7 2 5 4 11 9 16 11 1 0 2 0 2-1l1 1c6 6 12 12 11 21v4h-1v1l-3 4c-2 2-5 3-8 3-2-1-5-1-7-2l-9-3c-9-7-18-15-21-26 0-5 1-8 4-11 2-1 3-2 5-3 1 0 2 0 3-1z" class="L"></path><path d="M337 229l-1-2h3c0 2 0 4-1 7l-6 3c-1 0-3 0-5-2h1l-1-1h2 3l5-5z" class="D"></path><path d="M332 234c-2-1-3-2-5-3-4-4-8-9-12-13-4-3-10-7-12-12 0-2 0-2 1-3 2-2 3-3 6-2s5 4 7 6l11 12c3 3 7 6 9 10l-5 5z" class="K"></path><defs><linearGradient id="AP" x1="336.001" y1="208.508" x2="310.422" y2="222.059" xlink:href="#B"><stop offset="0" stop-color="#343334"></stop><stop offset="1" stop-color="#696869"></stop></linearGradient></defs><path fill="url(#AP)" d="M307 194c3 0 5 1 7 2 5 4 11 9 16 11 1 0 2 0 2-1l1 1c6 6 12 12 11 21v4h-1v1l-3 4c-2 2-5 3-8 3-2-1-5-1-7-2h4c1 0 2 0 3-1l6-3c1-3 1-5 1-7s0-3-1-4-2-1-3-2c-2-2-4-4-5-6-2-1-3-2-4-4-2-3-6-5-8-8h-2l-2-2c-2-1-2 0-3-1s-1-1-2-1c-1-1-1-1-2-1l-1 1c-1 0-2 1-3 1-2 0-2 0-4 1v-1c1-2 3-3 4-4l1-1c1 0 2 0 3-1z"></path><defs><linearGradient id="AQ" x1="366.115" y1="262.873" x2="260.657" y2="204.335" xlink:href="#B"><stop offset="0" stop-color="#999695"></stop><stop offset="1" stop-color="#e2e2e4"></stop></linearGradient></defs><path fill="url(#AQ)" d="M277 180v-1c1 0 6-1 7 0 2 0 3 2 4 3 4 5 8 10 11 16-3 3-4 6-4 11 3 11 12 19 21 26l9 3c2 1 5 1 7 2 3 0 6-1 8-3l7 8 5 7 1 2c1 1 2 3 1 5l-1 1-4 5 1 4c0 1 1 2 2 3 0 2 0 2 1 4s2 5 2 8l-1 1c1 4 4 6 6 9h0c-2 0-7-7-9-8-1-2-2-3-3-3v2c2 2 7 5 7 8 0 2 0 2 1 3 0 2 0 3 1 5h-1 0c-1-1-2-1-3-1-2-2-5-6-7-8-3-5-7-10-11-14l-3-6c-1-1-1-1-1-2l-16-21-6-8-1-1-1-1c0-1-1-2-1-2l-3-4c-2-4-6-8-9-13-1 0-1-1-2-2l-7-10-1-1-1-1-5-7-2-3-3-4h0c1-1 1-2 0-3h0c1-2 1-3 1-5l-1-1h5l-1-3z"></path><path d="M277 180v-1c1 0 6-1 7 0 2 0 3 2 4 3 4 5 8 10 11 16-3 3-4 6-4 11-2-3-3-6-5-9l-10-14c0-1-1-2-2-3l-1-3z" class="I"></path><path d="M332 272l1-2h-1c-1-1-1-3-2-4-1-2-3-3-4-5l-1-1c-1-1-1-3-2-4-2-3-3-6-3-9l1 3c4 6 9 11 13 16 3 4 5 7 9 10 1 0 2 1 2 3l1 1c1 1 1 2 2 3v2c2 2 7 5 7 8 0 2 0 2 1 3 0 2 0 3 1 5h-1 0c-1-1-2-1-3-1-2-2-5-6-7-8-3-5-7-10-11-14l-3-6z" class="G"></path><defs><linearGradient id="AR" x1="357.322" y1="263.253" x2="319.262" y2="257.672" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#a6a6a6"></stop></linearGradient></defs><path fill="url(#AR)" d="M316 235l9 3c2 1 5 1 7 2 3 0 6-1 8-3l7 8 5 7 1 2c1 1 2 3 1 5l-1 1-4 5 1 4c0 1 1 2 2 3 0 2 0 2 1 4s2 5 2 8l-1 1-31-42c-1-2-2-3-2-4-1-1-1 0-2-1l-3-3z"></path><defs><linearGradient id="AS" x1="352.879" y1="250.156" x2="332.639" y2="252.136" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#969595"></stop></linearGradient></defs><path fill="url(#AS)" d="M316 235l9 3c2 1 5 1 7 2 3 0 6-1 8-3l7 8 5 7 1 2c1 1 2 3 1 5l-1 1-4 5c-1-3-2-5-4-8-3-3-5-6-8-9-1-2-4-7-7-7l-1-1c-1 0-2-1-4-1v1c-2-1-3-1-4-1-1-1-1 0-2-1l-3-3z"></path></svg>