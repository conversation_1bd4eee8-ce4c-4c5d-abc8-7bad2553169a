<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="172 114 694 800"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#010101}.C{fill:#2d2c2d}.D{fill:#4a4949}.E{fill:#5e5d5e}.F{fill:#343334}.G{fill:#252424}.H{fill:#100f10}</style><path d="M538 843h0c2 0 4 1 5 2l-1 1c-1 1-1 1-2 1h-1c-1-2-1-2-1-4zm-43 0h1v1c0 1 0 2-1 3h-2c-1 0-1-1-2-2 1-1 3-1 4-2zm-22-45h1 2c2 2 2 3 2 5h-1s-1-1-2-1c-1-1-2 0-3-1 0-1 1-2 1-3zm81 0h0c2 0 3 1 4 3h-1-3l-1 1-1 2-1-1v-2c1-2 1-3 3-3zm76-255h1l-9 12s-1-1-1-2c2-4 6-7 9-10z" class="B"></path><path d="M614 564c0 2-1 3 0 5l-8 17c-1-2 0-4 1-6s1-4 2-7c2-3 3-6 5-9z" class="C"></path><path d="M421 725h0l1 1c3 7 10 8 16 10h-3c-4 0-9 0-12-4-2-2-2-4-2-7z" class="B"></path><path d="M621 553c0 1 1 2 1 2l-8 14c-1-2 0-3 0-5 2-4 5-8 7-11z" class="G"></path><path d="M572 822c1 0 2 1 3 2v3c-1 2-3 3-5 4l-2 1h-1c2-2 3-4 4-7 0-1 0-2 1-3zm-108 0h2c1 2 1 5 1 6 1 2 3 2 3 4h-2l-2-1c-2-1-3-3-4-5l2-4zm-8-49h3c1 0 3 1 4 2s2 2 2 3c-1-1-3-2-4-2h-5v1c-1 0-1 1-2 1-1 2 0 3 0 5v1h0c-2-2-2-4-2-6 1-3 2-4 4-5zm111 0h2c2 1 4 2 4 4 1 2 2 3 1 5 0 1-1 2-2 3 1-2 1-3 1-5-1 0-1 0-1-1-1-1-3-3-4-3-3 0-4 0-6 2h-1c0-1 0-1 1-2 1-2 2-3 5-3zm122-135c1 0 2 0 3 1h0c-1 0-1 1-2 1-8 4-11 14-14 22 0 0 0 1-1 2 1-5 2-11 4-15 2-6 5-9 10-11zm-11-144l1-1c1 0 2 1 3 2h1 0c0-1-1-1-2-2h-1 1 1c4 2 7 7 8 11 2 5 1 11-2 16-2 2-5 4-7 5h-2 1c3-1 5-4 7-6 2-4 3-11 1-15s-5-8-9-10h0-1zM440 740c4-1 6 1 10 3-4-1-7-2-11-1-3 1-5 3-7 6-1 2-1 5 0 8 1 2 3 4 6 5 2 0 3 0 5-1h1 0c-1 1-2 2-4 3-2 0-4 0-6-1-3-1-4-4-5-7s0-7 2-10 5-5 9-5zm141 0h3c4 0 7 2 10 5 2 3 2 6 2 10-1 2-2 5-5 7-2 1-5 1-7 0-1 0-3-1-3-2h1c1 1 3 1 5 1s3-2 4-3c2-3 3-6 2-8-1-3-3-6-5-7-5-2-9-1-13 0 2-2 4-2 6-3z" class="B"></path><defs><linearGradient id="A" x1="731.418" y1="372.221" x2="727.018" y2="366.19" xlink:href="#B"><stop offset="0" stop-color="#4c484c"></stop><stop offset="1" stop-color="#656566"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M724 369l8-3 1-1 13-2c2-1 4-1 5-1l2 1c-8 2-17 4-24 7s-12 6-18 9c0-1 1-1 1-2l3-3 9-5z"></path><path d="M312 582c2-1 4-1 5 0 5 1 6 4 9 7l-6-3c-3-1-8-2-11 0-5 2-8 7-10 12-1 5-1 10 0 16l-1-3-1-1c-1-7 0-14 3-19s7-8 12-9z" class="B"></path><defs><linearGradient id="C" x1="601.363" y1="630.302" x2="586.472" y2="632.648" xlink:href="#B"><stop offset="0" stop-color="#1c1c1d"></stop><stop offset="1" stop-color="#3d3c3d"></stop></linearGradient></defs><path fill="url(#C)" d="M586 638l3-11c4 1 9 1 13 0h1c-1 2-2 4-4 6-2 1-5 3-8 4-1 0-3 0-4 1l2 1h-3v-1z"></path><defs><linearGradient id="D" x1="591.803" y1="599.795" x2="608.297" y2="587.661" xlink:href="#B"><stop offset="0" stop-color="#141416"></stop><stop offset="1" stop-color="#353534"></stop></linearGradient></defs><path fill="url(#D)" d="M605 579v3l2-2c-1 2-2 4-1 6l-1 2-3 8c-4 8-7 17-12 25l12-38 1 1 2-5z"></path><path d="M706 582c4-1 7 0 10 2 5 2 8 8 9 13s1 12-2 17c1-4 2-7 2-11-1-4-2-8-4-11s-6-6-10-7c-6-1-10 2-14 5 2-5 4-6 9-8z" class="H"></path><path d="M397 558c1 10-1 21-7 29-4 4-8 7-13 7-4 1-9 0-13-3-2-2-4-6-4-9h0 1c1 5 3 8 8 10 3 1 7 1 11 0 6-3 11-9 13-15 1-4 2-7 2-12h1v-2l-1-4 2-1z" class="B"></path><path d="M423 628h1c5 0 9 0 14-1 1 3 3 6 3 9v1h-2c-3-1-4-1-6-1-4-1-9-3-11-7l1-1z" class="C"></path><path d="M582 646c3 1 6 3 8 5 7 4 13 9 21 10 3 1 6 1 10 1-9 2-18 1-25-2-6-3-12-7-14-13v-1z" class="G"></path><path d="M443 644c1 0 0 0 1 1v2c-3 4-6 7-11 10-9 6-19 7-29 5l-2-1h3c14 1 28-8 38-17zm194-106l3-2c0 1 1 1 1 1-7 6-12 13-13 23l-1 4c0 2 0 2 1 3 0 4 1 8 2 11 3 6 7 12 13 14 4 2 8 1 11 0 4-2 6-5 8-9v-1 1c0 2-1 5-3 7-3 2-7 4-11 5-4 0-9-2-12-5-7-6-10-17-10-26-1-7 1-14 6-20 1-2 3-4 5-6z" class="B"></path><defs><linearGradient id="E" x1="712.061" y1="390.18" x2="710.276" y2="385.628" xlink:href="#B"><stop offset="0" stop-color="#2b2a2c"></stop><stop offset="1" stop-color="#434344"></stop></linearGradient></defs><path fill="url(#E)" d="M690 394c8-6 18-8 28-9 5-1 10 0 16 1-4 0-8 0-11 1-7 1-12 3-18 7h-2c0-1 1-1 1-2h0c-3 0-6 2-8 3v1l-6 5h-2l-1 1c0-4 7-6 8-9-2-1-2 1-3 1h-2z"></path><path d="M321 397c-6-5-14-9-21-10-4-1-8-1-11-1 4-1 8-1 12-1 8-1 18 2 25 6 2 1 5 3 7 4h1c0 1 0 1 1 1v-1c1 1 1 2 2 3v2c-1 0 0 0-1-1s-3-2-5-3c-1 1-2 1-4 1l-3-3c-1-1-2-1-3-1l2 2 5 3c1 0 1 1 2 2l-9-6v3z" class="C"></path><path d="M665 495h2c5 0 11 1 15 5 3 3 4 6 4 10 1 4 0 7-3 9 0 1-1 1-2 2v-1h0c2-3 3-5 3-8s-2-5-4-7c-5-4-12-5-17-4h-3c-3 0-5 1-7 2h-1-2c4-2 8-5 12-6 1 0 1 0 2-1l1-1zM518 875c0 5 0 11 1 16 1 6 4 13 4 19-1 4-3 8-5 10-1 0-1-1-2-1-7-9-1-19 0-28 1-5 1-11 2-16z" class="B"></path><defs><linearGradient id="F" x1="811.816" y1="196.958" x2="828.023" y2="213.52" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#333"></stop></linearGradient></defs><path fill="url(#F)" d="M831 189v3l1-1c4 5-1 11 3 15-2 6-7 11-12 13s-10 2-15 0c-7-3-13-12-16-19 4 5 7 10 13 12 5 1 10 1 14-1 5-2 9-7 10-12h1c1-3 1-6 1-10z"></path><path d="M660 501c-14 5-23 15-32 26-3 4-6 8-8 12l-9 14h0c5-12 11-24 19-34 9-13 21-26 37-29 5-1 10 0 14 3h-1 1c1 1 2 1 2 2h0-1c-1-1-2-2-3-2l-1 1c-8-2-14-1-21 3l1 1c2-1 4-2 7-3l-1 1c-1 1-1 1-2 1-4 1-8 4-12 6h2 1c2-1 4-2 7-2z" class="H"></path><path d="M603 627v-1c3-2 4-4 6-8 3 3 6 11 7 15 0 4 0 7-3 10s-8 6-12 6c-5 0-12 0-16-4h-1v-2l1-5h1v1h3l-2-1c1-1 3-1 4-1 3-1 6-3 8-4 2-2 3-4 4-6z" class="B"></path><defs><linearGradient id="G" x1="606.201" y1="641.349" x2="587.849" y2="634.232" xlink:href="#B"><stop offset="0" stop-color="#1d1c1d"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#G)" d="M589 639c5-1 10-1 14-3 2-2 4-4 6-5 0 3 1 8-3 11-1 1-3 2-5 3h-1c-4 2-9 1-13 0h-2 0-1v-2l1-5h1v1h3z"></path><path d="M584 643l1-5c1 1 1 3 2 3 0 1 0 2-1 3l-2-1z" class="E"></path><path d="M443 643c-2 2-6 4-9 5-6 2-13 2-19-1-3-2-7-5-8-9-2-7 4-14 7-20l1 3c2 3 4 5 8 6v1l-1 1c2 4 7 6 11 7 2 0 3 0 6 1h2v-1c1 2 1 3 2 5v2z" class="B"></path><defs><linearGradient id="H" x1="415.391" y1="639.745" x2="437.81" y2="635.938" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#H)" d="M443 643l-1-1c-5-1-8 5-12 5-4 1-8-1-11-3-4-4-5-8-6-13l3 2c6 5 15 4 23 4h2v-1c1 2 1 3 2 5v2z"></path><path d="M441 636c1 2 1 3 2 5-3 0-3-1-5-2v-1c1 0 2 0 2-1h1v-1z" class="E"></path><defs><linearGradient id="I" x1="624.247" y1="535.589" x2="633.722" y2="568.617" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242426"></stop></linearGradient></defs><path fill="url(#I)" d="M602 583c1-6 3-12 6-17 1-4 4-7 6-11 3-5 6-11 11-15 4-5 11-9 18-10 4 0 9 2 12 5s4 7 4 10c0 4-1 7-4 9-3 3-8 4-12 3 4-2 8-3 10-8 1-2 1-4 0-5-1-2-3-3-5-4-3 0-6 1-8 3-8 5-11 16-12 24-1-1-1-1-1-3l1-4c1-10 6-17 13-23 0 0-1 0-1-1l-3 2c-2 1-4 3-6 5h-1c-3 3-7 6-9 10-2 3-5 7-7 11-2 3-3 6-5 9-1 3-1 5-2 7l-2 2v-3l-2 5-1-1z"></path><path fill="#f4f3f4" d="M641 537c4-1 6-1 9 1 2 1 3 2 3 4h0 0c-2-2-4-3-7-3-4 0-7 2-10 5-3 4-6 8-7 13-1 2-1 5-2 7h0l1-4c1-10 6-17 13-23z"></path><path d="M605 579c2-6 5-11 9-17 3-5 5-10 9-14 2-3 4-5 6-7l1 2c-3 3-7 6-9 10-2 3-5 7-7 11-2 3-3 6-5 9-1 3-1 5-2 7l-2 2v-3z" class="D"></path><path d="M347 490c7-1 14 1 20 4 5 3 10 6 14 10 10 10 18 23 25 35 3 6 7 13 9 20-3-4-5-9-8-14-6-10-12-19-20-28-9-9-19-16-32-16-5 0-10 1-14 5-2 1-3 3-3 6 0 4 1 6 3 9h1-1c-2-1-3-2-4-4-2-4-2-8-1-12s4-6 8-8c7-3 14-2 22 1-1-2-3-2-5-3-5-2-12-3-17-1s-8 5-10 9c-2 5-1 12 1 16 2 3 4 5 7 6 1 0 0 0 2 1-2 0-3 0-4-1-4-2-7-5-8-9-2-5-1-12 2-16 2-6 7-8 13-10z" class="B"></path><path d="M395 565c-2-8-6-19-13-23-3-2-5-2-8-1-2 0-4 1-5 3 0 1 0 3 1 5 2 5 6 7 10 8-4 1-9 0-13-3-2-3-4-6-3-9 0-4 1-8 4-11 4-2 8-4 13-3 9 1 15 7 21 14 8 11 16 23 21 36 6 14 9 30 14 44-4-5-7-11-10-17 0-1-1-3-1-4l-23-44-9-12c2 3 3 6 3 10l-2 1 1 4v2h-1z" class="H"></path><path d="M381 538l1-1c-1 0-2-1-3-1l1-1c4 1 7 2 10 6 1 1 1 2 2 3-1 0-2-1-3-2l-2 1-6-5z" class="F"></path><path d="M404 556c-2-3-5-6-5-9 9 13 19 27 24 42v1h-1c-2-2-3-5-4-8l-9-17c-1-3-3-6-5-9z" class="D"></path><path d="M404 556c2 3 4 6 5 9l9 17c1 3 2 6 4 8h1v-1c2 3 2 5 3 8l-1-1v-1l-2-1c0 4 3 6 3 10l-23-44h1c2 3 5 7 6 10 1 1 1 2 2 3h0c0-2-1-3-2-5l-4-7-1-1c0-1-1-2-1-3v-1z" class="C"></path><path fill="#f4f3f4" d="M396 563h-1c0-1 0-2-1-3-1-5-3-9-6-14-2-3-5-6-10-7-2 0-5 1-8 2h0-1l3-3c3-2 6-1 9 0l6 5 2-1c1 1 2 2 3 2 1 1 1 2 1 3 1 0 1 0 1 1 2 3 3 6 3 10l-2 1 1 4z"></path><path d="M387 543l2-1c1 1 2 2 3 2 1 1 1 2 1 3 1 0 1 0 1 1 2 3 3 6 3 10l-2 1c-1-3-2-8-4-11-1-2-2-4-4-5z" class="G"></path><path d="M271 243c4 0 9 0 13 1 16 5 29 22 37 36 3 6 6 13 9 20 6 15 11 31 17 46 1 4 5 13 4 16l-13-34c-8-22-18-46-37-60-6-4-12-7-20-6-4 1-9 3-12 7-1 2-1 4-1 6 1 3 3 6 6 8 2 1 5 1 9 1v-1l1 1c-2 1-4 2-6 2-4 0-7-1-11-4-3-2-5-6-5-10-1-4 0-8 3-11 3-4 8-7 14-7 11-1 19 4 27 11-2-5-11-11-15-14-7-4-13-5-20-5-3 0-5-1-8 0-2 1-4 3-5 5-5 9-1 15 1 24-2-3-5-8-5-12-1-5 0-11 4-15 3-3 7-4 12-5h1zm475 0h5c5 0 10 1 14 5 3 4 5 8 5 13-1 7-4 13-9 17 4-5 6-10 7-16 0-5-2-11-5-14-2-2-5-2-7-2-18-1-28 7-41 19 6-3 10-8 17-10s15-2 21 2c4 2 7 5 8 9 1 5 0 9-2 13s-7 6-11 7c-4 0-6 0-8-2v-1c0 1 1 1 2 1 3 0 7-1 9-3s4-5 4-8c0-2-2-4-3-6-5-4-11-5-17-5-2 1-4 1-6 2-8 2-13 7-18 13-20 22-28 53-39 79v-1c0-2 1-5 2-7l4-12c9-25 16-51 33-72 9-11 20-19 35-21zm-17 188c9-1 18-1 27 0 13 2 28 5 36 16 2 3 4 8 4 12 0 1-1 2-1 3 0-4 0-7-3-11-5-8-14-12-23-14-25-6-52 0-74 11-7 3-12 7-18 10h0c7-3 14-6 22-7 5-1 10-2 14 1h0 0c-10-3-21 4-29 8-19 9-33 22-46 39-4 5-8 11-11 17l-7 12c-1 3-2 5-4 7l7-21c1-4 2-9 3-13 5-11 13-23 21-32 13-16 32-28 53-34 9-2 19-4 29-4z" class="B"></path><path d="M677 449l2-1c7-4 16-6 24-8 2-1 8-3 10-3-1 0-2 1-3 1-2 1-3 3-5 3-3 0-7 2-10 3-3-1-7 1-10 2-2 1-5 3-8 3z" class="D"></path><defs><linearGradient id="J" x1="674.401" y1="462.322" x2="670.43" y2="451.203" xlink:href="#B"><stop offset="0" stop-color="#27282a"></stop><stop offset="1" stop-color="#525050"></stop></linearGradient></defs><path fill="url(#J)" d="M677 449c3 0 6-2 8-3 3-1 7-3 10-2-7 4-14 7-21 11-5 4-10 8-16 12-5 4-10 7-14 12v-1c2-3 4-4 6-7 1-1 2-2 3-4 5-5 11-9 17-13 2-2 5-4 7-5h0z"></path><path d="M281 435c-14 0-30 0-43 7-5 3-11 8-12 14 0 3 0 5 1 7h-1c-1-4-1-8 0-12 5-8 13-13 22-16 11-4 22-5 33-5 20 0 41 3 59 13 26 12 45 33 57 59 3 6 5 13 7 20l4 11c0 1 2 4 2 6-7-12-13-24-21-35-13-18-29-33-49-43-6-4-13-7-20-9-4-1-8-1-12 0h0c2-1 3-1 4-2h1c11-2 22 4 31 8-5-4-12-6-17-10-15-6-30-11-46-13z" class="B"></path><defs><linearGradient id="K" x1="322.229" y1="451.924" x2="327.692" y2="441.36" xlink:href="#B"><stop offset="0" stop-color="#212122"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#K)" d="M281 435h5c4-2 11 0 14 1l17 3c7 2 14 4 21 7 6 3 12 6 17 10 3 2 5 3 8 5h-1l-2-1h-1v3c1 1 2 1 2 2 2 0 3 2 4 2 1 1 3 3 3 4-2-1-5-4-7-6-7-5-14-10-21-14-4-2-8-4-12-5h-2c0 1 0 1 1 2-15-6-30-11-46-13z"></path><path d="M831 189c-2-6-4-10-7-15 9 3 15 10 19 19 3 9 4 19 0 28-3 8-10 14-18 17-8 4-18 4-26 0-9-4-13-11-18-19h-1l1 2c0 2 1 3 0 4l-1 1c-2 0-4-2-5-3l-10-5c-7-2-13-3-20-2-14 2-24 9-33 21 3-9 12-19 19-24 12-10 29-14 44-11 4 1 8 3 11 5 9 6 12 19 24 20 7 1 12-1 17-5 6-4 10-11 10-18 1-2 1-9-1-11 0 5 0 9-1 13-4-4 1-10-3-15l-1 1v-3z" class="B"></path><path d="M799 228l6 3h1c3 1 6 1 9 1-1 1-2 1-4 1-1 0-4-1-5-1l-3-1v1 1c1 2 2 1 4 2h1 7c1-1 2-1 3-1h1c-5 2-13 2-18 0-1-1-2-1-3-2 1-1 1-2 1-4z" class="C"></path><path d="M777 212c3 0 7 2 10 4 4 3 7 8 12 12 0 2 0 3-1 4-4-2-7-4-10-8l-7-7v2h-1v-1-1l-1-1c0-1-1-2-2-3v-1z" class="G"></path><path d="M781 217l-1-1 1-1c3 2 6 4 8 7-1 1-1 1-1 2l-7-7z" class="D"></path><path d="M765 218c2 0 3 1 5 1l2 2h1c2 1 4 3 7 4l1-1-1-3c0 1-1 2-2 2-2 0-3-2-5-3-3-2-7-4-11-5-1-1-2-1-3-2h3 0s1 0 1-1c0 0 1-2 0-2l-11-1c8-1 17 1 25 3v1c1 1 2 2 2 3l1 1v1 1l1 2c0 2 1 3 0 4l-1 1c-2 0-4-2-5-3l-10-5z" class="F"></path><path d="M255 328c-5 1-9 2-13 4-3 1-6 4-7 7-1 2-1 6 1 8 1 1 2 2 3 2l2 1c-5 1-11-1-14-3-5-3-10-8-11-13-1-4 0-9 2-12 3-6 10-10 16-11 7-2 14-1 21 0 18 3 35 11 50 20 17 11 31 26 43 42 4 5 9 11 12 17 3 7 6 15 9 23 1 4 3 8 4 13h0c-4-7-7-15-10-22-6-11-14-21-22-31-4-5-9-10-14-15-6-5-13-9-19-13-7-4-13-7-20-10-11-5-22-6-33-7z" class="B"></path><path d="M255 328c-1 0-1-1-2-1h-2c-5 0-9 1-13 3s-7 6-9 10l-1 1c-2-1-4-2-5-4 0-1 0-4 1-6 1-4 7-7 12-8 20-6 47 5 64 16 3 1 7 3 8 6-7-4-13-7-20-10-11-5-22-6-33-7z" class="C"></path><path d="M714 345c-6 4-11 7-17 12-15 13-29 32-38 49-3 4-5 9-6 13l-1 1v-1c0-2 2-5 2-7 2-7 4-14 8-20 2-5 5-10 8-15 22-32 56-59 95-66 11-2 24-2 34 4 4 3 7 7 8 12s-1 10-4 14c-5 7-14 9-22 10 1-1 2-1 3-2 1 0 2-1 3-2 1-2 1-5 1-7-2-4-5-7-8-8-5-3-11-3-17-4-12 1-23 4-34 9l-15 8z" class="B"></path><path d="M714 345l1-2c8-6 17-10 26-14 12-5 24-8 37-7 7 0 15 2 20 7 1 2 2 4 2 6l-1 1c0 3-2 4-4 6l-2-4-2-3c-4-5-10-7-16-8-3-1-10-1-13 0l1 1c-12 1-23 4-34 9l-15 8z" class="C"></path><path d="M196 174l1 1c-4 7-6 13-6 21 1 5 4 10 8 13s9 5 14 4c8-1 13-8 18-14-3 6-7 14-12 18-4 3-9 4-14 4h-1c-4-1-8-3-12-7-5-6-6-12-6-20h0c-2 2-2 7-1 10 0 7 4 14 10 19 5 4 12 5 19 4 8-1 13-9 17-15 5-6 11-9 18-10h1c9-1 17 0 26 3l5 2c11 6 23 17 27 29-7-9-15-17-27-19-9-3-19-2-28 3-3 1-5 3-7 4s-3 2-4 1c-1-3 1-6 2-9-3 3-5 7-8 11-3 5-8 9-14 12-9 3-18 3-26-1s-14-10-17-19c-4-9-3-19 1-28 3-7 9-14 16-17z" class="H"></path><path d="M789 283c-38 2-70 30-93 58-13 14-24 29-33 46 3-14 9-25 17-37 16-24 36-43 61-57 18-11 43-22 64-16 9 2 16 8 20 15 5 10 5 21 2 31-5 13-14 23-27 29-10 5-23 7-35 9l-12 2-2-1c-1 0-3 0-5 1l-13 2-1 1-8 3-9 5-3 3c0 1-1 1-1 2l-6 3-11 8c-3 2-5 3-7 6l3-2h2c1 0 1-2 3-1-1 3-8 5-8 9l1-1h2l6-5v-1c2-1 5-3 8-3h0c0 1-1 1-1 2h2l-2 2-16 14-9 9c-5 4-9 9-13 14-9 10-16 21-22 33l-12 21c1-4 2-8 4-12l9-27c4-13 9-25 15-36 14-25 35-43 63-50 16-5 33-6 49-7 13 0 28-1 38-10 8-8 11-17 11-28-1-10-4-19-10-26-3-3-6-4-9-6-4-1-8-2-12-2z" class="B"></path><path d="M789 283c1-1 3-1 4-1 2-1 8 1 10 1l1 1-3 1c-4-1-8-2-12-2z" class="F"></path><path d="M801 285l3-1c4 2 7 3 10 6-1 1 0 1-2 1-1-1-1-1-2 0-3-3-6-4-9-6z" class="E"></path><defs><linearGradient id="L" x1="823.383" y1="304.147" x2="809.973" y2="298.176" xlink:href="#B"><stop offset="0" stop-color="#171716"></stop><stop offset="1" stop-color="#3c3b3d"></stop></linearGradient></defs><path fill="url(#L)" d="M814 290c5 4 9 12 10 17 0 2-1 5-2 6l-1 1c0 1-1 2-1 3-1-10-4-19-10-26 1-1 1-1 2 0 2 0 1 0 2-1z"></path><path d="M696 396v-1c2-1 5-3 8-3h0c0 1-1 1-1 2h2l-2 2-16 14-9 9v-1c0-2 4-5 6-6v-3-1l-5 5c-1 2 0 1-1 2 0-3 5-7 7-9l3-3c1-1 2-1 3-2l5-3 1-1 3-2v-1h0l-4 2z" class="C"></path><defs><linearGradient id="M" x1="692.285" y1="405.658" x2="691.215" y2="400.842" xlink:href="#B"><stop offset="0" stop-color="#4b4e4b"></stop><stop offset="1" stop-color="#615d60"></stop></linearGradient></defs><path fill="url(#M)" d="M687 410c0-2 1-4 2-5 3-3 7-8 11-9h3l-16 14z"></path><path d="M687 396c0 1-1 1-2 2v-1c1-2 3-4 5-6 2-1 4-2 6-4v-2c-2 1-3 2-5 3 0-2 6-5 8-6l1-1 1-1c2-2 5-5 8-6 4-2 7-3 11-4 1-1 3-1 4-1l-9 5-3 3c0 1-1 1-1 2l-6 3-11 8c-3 2-5 3-7 6z" class="F"></path><path d="M715 374l-3 3c0 1-1 1-1 2l-6 3c2-4 6-6 10-8z" class="E"></path><path d="M272 364c-16-5-35-4-51-12-12-6-21-16-25-29-4-10-3-21 2-31 4-7 11-13 19-15 20-6 45 5 62 14 30 17 55 42 70 72 4 8 8 15 10 23-2-2-3-4-4-6l-9-14c-13-19-29-38-47-52-18-16-43-33-69-31-6 1-12 3-17 8-6 7-9 13-10 22-1 12 0 21 8 30 12 12 29 12 46 13 29 2 58 6 81 25 8 7 16 16 21 25 9 13 14 26 19 40l12 32c1 4 3 9 4 13-12-23-24-45-41-64-1 0-2-2-2-2l-30-28v-3l9 6c-1-1-1-2-2-2l-5-3-2-2c1 0 2 0 3 1l3 3c2 0 3 0 4-1 2 1 4 2 5 3s0 1 1 1v-2c-1-1-1-2-2-3v1c-1 0-1 0-1-1l-3-3c-4-3-8-5-11-8l-16-9c-10-5-21-9-32-11z" class="B"></path><path d="M208 290l4-3c3-2 13-6 16-5l1 1h1c-6 1-12 3-17 8h-2c-2 0-2 0-3-1z" class="E"></path><path d="M205 293l3-3c1 1 1 1 3 1h2c-6 7-9 13-10 22-1-1-1-2-1-3h0l1-3v-1l1-1h0l1-2v-1-1c0-2-1-3-2-4-1 3-2 6-2 10l-1 1v-2c0-4 3-10 5-13z" class="C"></path><path d="M205 293l3-3c1 1 1 1 3 1-1 2-3 3-4 4l-2-2z" class="D"></path><defs><linearGradient id="N" x1="333.78" y1="413.648" x2="328.22" y2="394.352" xlink:href="#B"><stop offset="0" stop-color="#2c2c2d"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#N)" d="M321 397v-3l9 6 16 16h-1c0-1-1-1-2-2-1 0-2-1-2-2l-1 1c2 3 7 6 10 10 1 0 1 1 1 2l-30-28z"></path><path d="M299 368c7 2 15 6 21 10l8 6c-1 1-2-1-3-1v4c2 1 3 2 5 3l3 3c1 0 2 1 2 2v1c-1 0-1 0-1-1l-3-3c-4-3-8-5-11-8-1-1-2-2-3-2v-1-1c-5-5-13-7-18-12z" class="F"></path><path d="M272 364c1-1 2-1 2-1 2-1 4 0 6 1 6 1 12 1 18 3l1 1c5 5 13 7 18 12v1 1c1 0 2 1 3 2l-16-9c-10-5-21-9-32-11z" class="D"></path><path d="M696 583h2c-4 3-8 5-11 9-21 23-10 56-17 83-5 17-16 30-31 39h1c16-6 31-26 37-42 4-7 6-16 9-23l-1 10c-4 22-18 43-36 54-11 8-26 12-39 9-11-3-21-10-27-20-5-7-8-18-7-27 0-6 2-13 4-19 3 8 8 15 15 19s15 4 23 2c10-4 20-13 25-23 3-6 5-14 3-21 0-4-2-8-4-12 5 5 8 9 8 15 1 19-11 42-24 55-4 6-10 10-18 10-11 1-17-3-25-11 1 4 4 7 8 10 7 5 15 6 24 4 7-1 14-6 19-12 13-15 18-36 23-54 4-13 7-26 14-37 6-9 14-16 25-18z" class="B"></path><defs><linearGradient id="O" x1="645.71" y1="651.98" x2="678.359" y2="639.688" xlink:href="#B"><stop offset="0" stop-color="#2f2f2f"></stop><stop offset="1" stop-color="#4c4c4d"></stop></linearGradient></defs><path fill="url(#O)" d="M670 608l1 1c-3 14-4 28-5 42-2 13-3 26-10 37h0l2-3v-1c1-3 1-5 2-7-1-1-4-2-6-2l-1 1c-1 3-5 12-8 13 1-3 3-5 5-8 3-6 6-12 8-19 5-18 4-37 12-54z"></path><defs><linearGradient id="P" x1="613.598" y1="694.037" x2="593.355" y2="664.7" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#P)" d="M583 670c6 7 8 13 18 16 11 3 20-4 30-9l-3 6c-1 2-2 3-4 4-6 6-12 8-20 8-6 0-12-2-16-6-6-6-5-12-5-19z"></path><path d="M447 651c2 20 5 40-9 56-7 9-18 15-29 15-16 2-31-4-43-15-17-15-27-37-29-60 3 9 6 18 10 27 5 11 11 20 20 28 4 4 9 8 14 11l2 1c-16-12-27-25-31-45-5-27 4-64-22-82-2-2-4-3-6-5l6 1c13 5 22 16 27 28 6 13 9 28 13 41 3 10 6 19 11 28 5 10 13 19 24 23 7 3 17 3 24-1 6-2 10-6 12-12-7 7-15 12-25 11-8 0-14-4-19-10-12-13-24-36-23-54 0-7 3-12 8-16-2 4-3 7-4 11-2 7 0 14 3 21 4 10 13 19 23 23 8 3 16 3 23 0 8-4 15-12 18-20l1-3c0-1 0-1 1-2z" class="B"></path><defs><linearGradient id="Q" x1="379.515" y1="662.714" x2="342.709" y2="634.97" xlink:href="#B"><stop offset="0" stop-color="#2e2f2e"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient></defs><path fill="url(#Q)" d="M352 608c2 0 4 7 4 9 3 9 4 20 6 29 2 15 7 29 15 41v1c-1-1-1-1-1-2-2-2-4-8-6-9-2 0-3 1-4 1s-2-1-2-1l-1 1c0 1 1 2 1 4 1 2 4 5 4 8-6-12-9-23-11-36l-2-30c0-6-2-11-3-16z"></path><defs><linearGradient id="R" x1="409.839" y1="693.318" x2="426.444" y2="663.859" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#444443"></stop></linearGradient></defs><path fill="url(#R)" d="M441 670v5c0 6-1 11-5 15-5 4-13 5-19 5-9 0-14-5-20-11-2-2-3-4-4-7 9 6 17 11 28 9 10-2 14-9 20-16z"></path><path d="M447 651c-1-8-5-16-8-24l-27-85-6-17-18-56-48-143-14-42c-5-16-9-31-16-46-11-23-28-41-53-50-12-4-24-5-35 1l1-2c7-6 15-5 23-5-2-1-5-1-7-2-10-2-19-3-29-3l-2-7h297v10c-13 1-25 2-36 7-17 8-30 21-37 38-5 14-7 28-4 42 2 9 5 17 8 25l15 46 42 124 40 120 13 39c2 6 4 14 7 20-1-12-4-23-3-35 1-8 3-16 5-23l10-36 42-150 13-46c7-23 14-47 14-71 1-26-4-59-23-78-18-18-41-21-65-21v-11h268l-2 6c-10 1-19 2-28 4-3 1-6 1-9 2 6 0 12-1 17 1 3 1 6 3 9 5-3 0-5-2-8-2-14-4-32 3-44 10-21 11-33 29-43 50-6 14-9 30-14 44l-22 70-40 127-48 159v1l-64 223-57-177-10-31c-1-3-2-8-4-11z" class="B"></path></svg>