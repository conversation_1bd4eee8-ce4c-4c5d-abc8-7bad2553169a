<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="174 146 679 736"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#cfcecd}.C{fill:#e0e0df}.D{fill:#b0aead}.E{fill:#e8e8e8}.F{fill:#fcfcfc}.G{fill:#848281}.H{fill:#535151}.I{fill:#f6f5f6}.J{fill:#212020}.K{fill:#30302f}.L{fill:#393837}.M{fill:#b6b4b3}.N{fill:#c9c8c8}.O{fill:#2a2a2a}.P{fill:#a5a3a2}.Q{fill:#999796}.R{fill:#6f6d6d}.S{fill:#595858}.T{fill:#171717}.U{fill:#424141}.V{fill:#c4c2c1}.W{fill:#c0bfbe}.X{fill:#8d8b8a}</style><path d="M359 714c1 2 2 4 2 5l-1 1-1-1v-5z" class="F"></path><path d="M481 375c2 1 5 1 7 0l4-1c-2 2-4 2-6 2v1 1h-6l-7-2c1-1 2-1 3-1h5zm89-152c5-2 12-2 17-4l1-3v4 6-1c0-1 0-2-1-3-4 1-8 2-11 3-1-1-1-1-2-1h0v-1h-4z" class="H"></path><path d="M456 222c3 0 7 0 9 1-15 3-28 6-39 17h-1v-1c2-3 6-5 9-7l8-4c2-1 10-3 10-4 2-1 3-1 4-1h1v-1h-1z" class="L"></path><path d="M661 724c3-2 7-3 10-5 0-2 1-3 1-4l2-2v1h0c0 2-1 2-1 3l-1 1h1v1l-1 1v2c-2 2-4 3-6 4h-1-2 0-3-1l-1-1c1-1 2 0 3-1z" class="P"></path><path d="M655 726l-1-1c2-3-1-9 1-13 1 1 1 3 1 4l4-1h0l1 1c-1 0-1 1-1 2h0c-1 1-1 3-1 5 1 0 2 0 2 1-1 1-2 0-3 1h-1c0 1 0 1-1 2h0 0l-1-1h0z" class="F"></path><path d="M421 226c2-2 6-3 9-3 9-2 17-2 26-1h1v1h-1c-1 0-2 0-4 1-8-2-22 0-30 3l-1-1z" class="J"></path><path d="M655 726h0l1 1h0 0c1-1 1-1 1-2h1l1 1h1 3 0 2 1l-14 12v-3c0-1 1-2 1-2v-1c1-1 1-1 1-2v-1c1-1 1-1 1-3z" class="I"></path><path d="M655 787c3-1 6-1 8-1 1 0 2 0 3-1h1c1 0 3-1 4-1h0l-2 2v1l-1 1v1h-1v2l-1 1c-2 0-3 1-5 1h-1v-2l2-1-3-1v-1c-1 0-2 0-4-1h0z" class="D"></path><path d="M640 712c0-1 0-2 1-3v-1l1-1v-1c0-1 1-2 1-3s0-1 1-1v-1c-1-1 0 0 0-1h0l2 1v-2l1-1c1 1 0 2 1 3l1 1c-1 0-3 1-3 1-2 5-3 9-3 15v1 2c0 1 0 1-1 2 0-1 0-2-1-2v-1c0-2-1-5 0-6v-1l-1-1z" class="V"></path><path d="M596 285c1 2 2 3 2 5h1v4h0v5c-1 0-2 1-2 2s0 1-1 2v1c0 3-2 5-4 7h-1c3-8 5-17 5-26z" class="O"></path><path d="M623 617h0c1 1 1 2 1 4h0c-1 11-1 21-4 31-1 2-1 3-2 4 0-2-1-3 0-4 1-2 1-4 2-6 2-9 2-17 1-25l2-4z" class="B"></path><path d="M577 254c5 0 9 9 13 13 2 3 5 7 7 11 3 6 4 13 5 19l-2-1-1-2h0v-4h-1c0-2-1-3-2-5-1-12-11-23-19-31z"></path><defs><linearGradient id="A" x1="434.418" y1="230.473" x2="437.589" y2="220.506" xlink:href="#B"><stop offset="0" stop-color="#d0ced1"></stop><stop offset="1" stop-color="#f4f4f5"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M422 227c8-3 22-5 30-3 0 1-8 3-10 4v-1l-1-1-19 3v-2z"></path><defs><linearGradient id="C" x1="557.221" y1="234.286" x2="564.483" y2="218.778" xlink:href="#B"><stop offset="0" stop-color="#313230"></stop><stop offset="1" stop-color="#4a4748"></stop></linearGradient></defs><path fill="url(#C)" d="M542 231c2-1 4-2 6-2 7-3 15-5 22-6h4v1h0c1 0 1 0 2 1-2 0-4 0-5 1l1 1c-1 1 0 1 0 2-7-3-19 0-26 2h0-4z"></path><path d="M426 279c6-5 13-9 21-11-12 7-20 14-23 28-2 5-2 11-1 16l-2 2c-2-10 0-20 5-29 2-2 4-4 5-7l-3 2-2-1z" class="O"></path><path d="M643 719c1-2 1-3 2-5h0c0-1 1-2 1-3h0c0 2 0 4-1 6v2l-1 3v1c-1 2-1 3-1 5s1 4 0 6v2h0l-1 1v2h0v2h-1v3c-1 1 0 2-1 3-1-1 0-5 0-7v-2c-1-3 1-6-1-8 0 2 1 7 0 8-1-1-1-7-1-8 2-3 0-7 1-10v-2c0-1 0-2 1-3v-1-2l1 1v1c-1 1 0 4 0 6v1c1 0 1 1 1 2 1-1 1-1 1-2v-2z" class="D"></path><path d="M457 357l21 9c3 1 7 2 10 3 1 1 2 1 3 3l-1 1c-2 2-5 1-8 0-5-1-11-2-16-4l1-1c4 1 9 3 13 3h9c-13-4-25-8-36-14h4z" class="H"></path><path d="M362 781h1c1 1 3 3 5 3 2 1 4 2 7 3h4c2 0 4 0 6-1 1 0 2 0 3 1v3c-2 0-4 0-6-1-1 2-1 2-1 5h-1c-4-1-7-4-11-6-1-1-3-2-4-3-1 0-3 0-3 1-4 1-7 0-10-1h-2c-2 0-7-3-9-4 5 2 15 5 20 3h3v-1l-2-2z" class="K"></path><path d="M385 786c1 0 2 0 3 1v3c-2 0-4 0-6-1-1 2-1 2-1 5-1-1-2-2-3-2-3-2-6-3-9-5 3 1 6 2 9 2l1-1v-1c2 0 4 0 6-1z" class="X"></path><path d="M633 752v8c0 4-6 11-9 15-7 8-15 11-24 13-7 1-15-3-21-6 2-1 5 1 8 2 4 1 10 3 14 2h1c3-1 7-3 11-5 8-4 15-12 18-21 1-2 0-5 2-8h0z" class="K"></path><path d="M673 718c1 0 1-1 2-1 2-1 3-1 4-3l1-1 2-4c1-1 1-1 3-2h0v1c1 1 1 2 1 3l1 1h1l2-1h2 2c1-1 0-1 2-1 4-1 10-1 15-3 0 0 1 0 1 1h0l-7 2-4 1h0c-2 0-3 0-5 1-8 3-16 6-24 10v-2l1-1v-1z" class="B"></path><path d="M578 250c11 6 20 23 24 35 0 1 1 8 2 9-1 3-1 6-1 10l-1-7c-1-6-2-13-5-19-2-4-5-8-7-11-1-5-5-9-8-12-1-1-3-3-4-5z" class="P"></path><path d="M577 254c-8-6-16-11-26-12-3-1-6-1-9-1 0-1 2-1 3-1 12-2 24 3 33 10 1 2 3 4 4 5 3 3 7 7 8 12-4-4-8-13-13-13z" class="R"></path><path d="M328 670h4l3 1c1 0 2 1 2 1h1c1 0 3 1 4 2l3 1c1 0 2 1 3 1l2 1 2 1c2 1 3 4 5 4h1v-1h0c1 1 1 2 1 4 1 1 1 3 2 4 0 2 1 4 2 6v2c1 1 1 0 1 2 1 1 0 4 0 5v2c-1-1 0-1-1-2h0l-1-2c0-2-1-3-2-5 0-1 0-1-1-2v-2c-1-1-2-3-3-4h0c-1-2-4-6-6-7-1 0 0 0-1-1-2-1-3-2-5-3l-2-1c-1-1-2-1-3-2h-1l-2-1c-1-1-3-1-4-2s-2-1-3-1h0l-1-1z" class="I"></path><path d="M603 304c0-4 0-7 1-10l1 4c0 2-1 6 0 8 0 1 0 3 1 4 0 3 0 5-1 7v4 1l-1 3c-4 7-7 15-12 21-1 2-3 5-5 6h-1c1-2 3-5 4-7 3-4 4-7 5-12 5-9 8-19 8-29z" class="B"></path><path d="M672 722c8-4 16-7 24-10 2-1 3-1 5-1-6 3-12 5-17 7-6 3-12 7-18 11-5 3-10 6-14 11s-7 10-10 15h6c-2 0-5 1-6 2-2 1-5 5-5 7l-1 1c-2 2-3 4-4 6-2 4-5 7-8 9 3 2 6 5 8 7l-1 1h-1l-2-2-5-5c0-1 4-4 5-5 3-5 6-10 9-14 5-9 9-17 15-24l14-12c2-1 4-2 6-4z" class="O"></path><path d="M628 690h0c0-2 0-2-1-3l-1-3v-1l-4-8 4-7c4-6 7-14 9-21 0-2 1-5 1-7h1c0 4-1 7-2 11v1 1c0 1 0 1-1 2v2h-1v1 1h-1l-1 3-1 2h0v2l-1 1v1l1 1v1 2 1c1-2 1-2 1-4l1-1 1 1 1 1c0-1 1-1 1-2 1-2 0-3 1-3 1 1 0 3 0 4h0l1-2 1 1c-1 3-2 6-3 10-1 1 0 3 0 5 0 1 0 2 1 3h-1 0c-1-3-2-6-1-9l1-5h0c-4 6-5 12-5 19l-2-1z" class="M"></path><path d="M576 225c3-1 7-2 11-3 1 1 1 2 1 3v1 5 4l-1 8v-1c-2-3-7-6-10-7-8-4-19-6-27-4l-3 1-1-1c7-2 19-5 26-2 0-1-1-1 0-2l-1-1c1-1 3-1 5-1z" class="F"></path><path d="M572 227c2 0 5 1 7 1l9 3v4c-6-3-11-5-16-6 0-1-1-1 0-2z" class="L"></path><path d="M576 225c3-1 7-2 11-3 1 1 1 2 1 3v1 5l-9-3c-2 0-5-1-7-1l-1-1c1-1 3-1 5-1z" class="I"></path><path d="M579 228l9-3v1 5l-9-3z" class="E"></path><path d="M822 217h1v3 3c1 0 1 0 2 1 1 2 2 4 3 7v2 1l1 2v2l1 1c0 2 0 14-1 15v4c0 1 0 2-1 3v3c-1 1-1 2-2 3v1c0 1 0 1-1 2v1l-3-2c0-3-3-6-4-8v-1c3 2 4 3 6 6v1c1-5 0-10-1-15 0-12-1-24-1-35z" class="D"></path><defs><linearGradient id="D" x1="414.301" y1="260.742" x2="417.867" y2="270.364" xlink:href="#B"><stop offset="0" stop-color="#8e8c8a"></stop><stop offset="1" stop-color="#bdbcbb"></stop></linearGradient></defs><path fill="url(#D)" d="M412 260c0-1 1-2 1-4 0-1 1-1 1-2 3-1 11 4 15 6l2 1c0 1-1 2-1 3h0 0l-1 1v1l-2 2v-1c0 1-1 2-2 3-1 0 0 0-1-1v1c0 1-1 1-1 2l-1-1h0c0 1 0 1-1 2v-1-1h-1l-1-1v1h-1v1l-1-1c-2 1-4 2-7 2l-1 1c0 1 0 0-1 2v1 2-3c0-5 2-11 4-16z"></path><defs><linearGradient id="E" x1="413.462" y1="256.127" x2="416.385" y2="260.965" xlink:href="#B"><stop offset="0" stop-color="#767474"></stop><stop offset="1" stop-color="#8f8d8c"></stop></linearGradient></defs><path fill="url(#E)" d="M412 260c0-1 1-2 1-4 0-1 1-1 1-2 3-1 11 4 15 6l2 1c0 1-1 2-1 3h0 0l-1 1v1l-2 2v-1c-1 0-1-1-2-1h0l-1-1v-2h0v-2-1h-1l-1-1h-2 0c-1 0-1 1-2 1-2 2-2 2-5 2 0 0-1-1-1-2z"></path><path d="M613 787c6-1 10-1 15-1l2 2h1l1-1v3h-1c-1 0-1 0-1 1h0c-1 0-2 0-2 1h-1c0 1-1 1-1 2h0c1 0 1 0 3 1h0 0l-1 1-4-1-1 3-2-2h-4 0c1 1 2 1 3 1h1c0 1-1 1-2 2h-1c-1 0-1 0-2 1l-5-1h0c-1 0-1 0-2-1h-1l-2-1-2-1h0c-2-1-5-3-6-3-3-1-5-2-7-2l1-1h4c1-1 2 0 4 0v-1h4c3 0 6-1 9-2z" class="W"></path><path d="M613 787c6-1 10-1 15-1l2 2h1l1-1v3h-1c-1 0-1 0-1 1h0c-1 0-2 0-2 1h-5c-5-1-10-1-16-1 3-1 6 0 8-1 4-1 8 1 12-1 1 0 1 0 2-1h-3l-1-1h-6c-1 1-4 1-5 1l-1-1z" class="Q"></path><path d="M601 790c2 0 4 1 6 1 6 0 11 0 16 1h5-1c0 1-1 1-1 2h0c1 0 1 0 3 1h0 0l-1 1-4-1c-5 0-11 0-16-1-3-1-5-3-7-4z" class="G"></path><path d="M591 791l1-1h4c1-1 2 0 4 0h1c2 1 4 3 7 4 5 1 11 1 16 1l-1 3-2-2h-4 0c1 1 2 1 3 1h1c0 1-1 1-2 2h-1c-1 0-1 0-2 1l-5-1h0c-1 0-1 0-2-1h-1l-2-1-2-1h0c-2-1-5-3-6-3-3-1-5-2-7-2z" class="E"></path><path d="M569 780l-1-1 1-1c0 1 1 1 2 1l2 2 1-1c1 0 4 2 5 2 6 3 14 7 21 6v1 1c-2 0-3-1-4 0h-4l-1 1c2 0 4 1 7 2 1 0 4 2 6 3h0l2 1 2 1h1c1 1 1 1 2 1h0c1 2 1 2 2 2 3 1 8 1 11 2v1h-2v1 1 2l-1-1v1h-1l-4-4-1 1c3 3 6 5 8 9l1 1v1c-6-7-13-12-21-17-5-1-9-4-15-4h0c-1-1-3-1-4-1-2 0-3 1-4 0v-1h4v-1h-1c-2-1-4-2-5-3s-2-2-4-2v1l-5-8z" class="Q"></path><path d="M569 780l-1-1 1-1c0 1 1 1 2 1l2 2 1-1c1 0 4 2 5 2 6 3 14 7 21 6v1 1c-2 0-3-1-4 0h-4l-1 1c-1-1-2-1-3-1s-2 1-2 1h-1c1-1 0-1 1-2h-5-1 0c-2-1-2-1-3-3l-3-3h-1c-1-1-2-1-3-2v-1h-1z" class="D"></path><path d="M574 780c1 0 4 2 5 2 6 3 14 7 21 6v1 1c-2 0-3-1-4 0h-4l-1 1c-1-1-2-1-3-1l1-1h5c-4-1-10-1-13-3 0-1-1-1-1-2-1-1-3-2-5-2h-1l-1-1 1-1z" class="F"></path><path d="M603 799c-1-2-6-5-9-5-1-1-3-1-4-2h1l5 1c2 0 4 2 7 3h1l2 1 2 1h1c1 1 1 1 2 1h0c1 2 1 2 2 2 3 1 8 1 11 2v1h-2v1 1 2l-1-1v1h-1l-4-4-1 1c3 3 6 5 8 9l1 1v1c-6-7-13-12-21-17z" class="B"></path><defs><linearGradient id="F" x1="382.25" y1="787.42" x2="375.085" y2="769.7" xlink:href="#B"><stop offset="0" stop-color="#a19e9d"></stop><stop offset="1" stop-color="#c1bfbe"></stop></linearGradient></defs><path fill="url(#F)" d="M362 781l-1-1 1-1h1l1 1h3l3-1c0-1 1-1 2-2h0c1-1 2-1 2-2l2-1c1-1 1-1 2-1l1-2h0 1v-1l1-1c0-1 1-1 2-2v-1l-1-1-1-1-1-2c-1-2-3-4-5-5h0c-1-1-2-1-2-2l1-1v1c1 1 1 1 2 1 2 1 4 3 5 5l10 15c0 1 1 2 1 2 1 1 1 2 1 3s-1 1-2 2-2 1-2 2l-1 1v1c-1-1-2-1-3-1-2 1-4 1-6 1h-4c-3-1-5-2-7-3-2 0-4-2-5-3h-1z"></path><path d="M368 784h5c3 1 9 0 12 2-2 1-4 1-6 1h-4c-3-1-5-2-7-3z" class="Q"></path><defs><linearGradient id="G" x1="638.425" y1="790.863" x2="640.026" y2="778.944" xlink:href="#B"><stop offset="0" stop-color="#9d9998"></stop><stop offset="1" stop-color="#bbbab9"></stop></linearGradient></defs><path fill="url(#G)" d="M632 787c-2-2-5-5-8-7 3-2 6-5 8-9 1-2 2-4 4-6v1c0 1-1 2-2 3v1 1l-2 2c-1 1-1 2-2 2v1l-2 2c1 0 1 0 2-1h1l1 1h2c1 0 1 0 2 1h2c2 0 1 0 3 1 1 0 3 0 4 1 1 0 2 0 3-1h2 0c1-1 1-1 2-1l1 1h3c0 1 1 1 0 2l-2 5s-2 0-2 1v1c-1 0-3 1-4 2-4 1-8 4-12 5-1 1-6 0-8 0l1-1h0 0c-2-1-2-1-3-1h0c0-1 1-1 1-2h1c0-1 1-1 2-1h0c0-1 0-1 1-1h1v-3z"></path><path d="M636 796v-1-1-1c4-4 7-6 14-7 3-1 3-2 5-5l1 1-2 5s-2 0-2 1v1c-1 0-3 1-4 2-4 1-8 4-12 5z" class="R"></path><path d="M378 755c-2-2-3-5-5-8s-4-6-6-8c-5-5-10-8-16-12-9-5-18-10-27-14l-20-6c3-1 9 2 12 2 7 2 14 2 22 4h0c-1-2-2-4-4-6v-1h1c3 2 4 5 5 8 1 2 7 4 9 5l4 4 3 1c2 1 3 1 5 0 1-1 0-2 0-4v-1c0-1-1-3-2-5s-2-4-2-6h0c3 5 7 9 6 16l1 1h1c0 1 0 4 1 6l1 1 1 3h0c1 1 2 2 2 3l3 3 1 2-1 1c2 4 4 7 6 11h-1z" class="J"></path><path d="M356 728c-11-8-22-12-34-17 2 0 5 1 7 1 4 1 7 2 11 4 6 3 11 7 17 10h-1v1 1z" class="C"></path><path d="M363 724l1 1h1c0 1 0 4 1 6l1 1 1 3h0c1 1 2 2 2 3l3 3 1 2-1 1c-5-6-10-11-17-16v-1-1h1l3 1c2-1 2-2 3-3z" class="I"></path><path d="M373 741h0c1 0 2 2 3 3l3 3v-5l-2-1v-3c-1-2 0-10 0-12-1-2-1-2-1-3v-1-2l1 1h0v-2-5c-1-1-1-1-1-2v-5-1-3c-1 0-2-1-2-2v-1l2 2v-1-1c1 0 1 1 2 2 1-1 1-1 1-2s0-1-1-3v-1-8c0-1-1-2-1-3h1c0-3-1-7 0-10v-2c0-1 0 0 1-1 1 1 1 1 1 3v2c1 1 1 2 1 4 1 1 0 6 0 8-1 1 0 3 0 5l1-1h0v3h-1c0 1 0 2 1 3v1 3c1 3-1 7 0 11v3c1 1 1 1 3 1v1c0 4 1 22 0 25-1 0-2 1-2 1h-1c-1 1-1 1-1 2v1c-1 1 0 2-1 3-1-1-2-3-3-4l-3-4-1-2z" class="B"></path><path d="M388 787v-1l1-1c0 1 1 2 1 3h0c-1 1-1 2-1 3h3l2 2c-2 1-4 1-6 1h-1l1-1v-1l-1 1c-1 0-1 0-2 1 0 0-1 0-1 1h3l1 1c1 0 3 1 4 1h-14l2 3-1 1c-4 0-8 0-12-1-8-1-14-1-21 3h0c-2 1-2 1-3 1l-1 1h-1l-4 1-1-1-1 1c-2 0-4 0-6-1 2 0 5 0 7-1h1c1-1 1-1 2-1l1-1c1 0 1-1 2-1l1-1c2-1 5-4 6-6v-1c1-3 0-5 1-8h0 2c3 1 6 2 10 1 0-1 2-1 3-1 1 1 3 2 4 3 4 2 7 5 11 6h1c0-3 0-3 1-5 2 1 4 1 6 1v-3z" class="Q"></path><path d="M352 785c3 1 6 2 10 1 0-1 2-1 3-1 1 1 3 2 4 3 4 2 7 5 11 6l1 1c-2 0-3 1-5 0h-5c-1-1-2-1-4-1-3-1-6-2-9-4l-6-5z" class="C"></path><path d="M336 805c2 0 3-1 5-2h1l2-2c3-2 6-5 7-8v-1l1 2 1 1h5c3 0 6 1 9 1 4 1 8 0 11 1l2 3-1 1c-4 0-8 0-12-1-8-1-14-1-21 3h0c-2 1-2 1-3 1l-1 1h-1l-4 1-1-1z" class="I"></path><defs><linearGradient id="H" x1="623.745" y1="815.154" x2="651.905" y2="822.092" xlink:href="#B"><stop offset="0" stop-color="#9e9c9b"></stop><stop offset="1" stop-color="#bcbbba"></stop></linearGradient></defs><path fill="url(#H)" d="M622 805l1 1v-1c2 1 0 2 2 3l-1 2h1s1 0 1 1v-1c-1-2-1-4-1-6 2 0 4 0 6 1l13 1c5 0 10 0 15 1 4 0 7 2 11 3 3 1 6 1 9 1h0-3c-2 0-3 0-4 1v-1h-3c-1-1-2-1-3-1h-1l-1-1h-2v-1c-1 0-5-1-6 0-1 0-1 1-2 2-1 2-2 3-3 4l-3 7v1s0 1-1 1v1 1l-1 1v2h0c0 1 0 2-1 3 0 1 1 1 0 2 0 1 0 2-1 3v1c1 1 2 1 2 3 1 0 1 0 1 1l1 2c1 1 1 2 2 3v1c1 1 1 2 1 2l-1 1c-1-3-2-6-4-9-1-3-4-5-6-8-2-2-3-4-4-6l1-2-2-1v-1h-1l-1-1c-2-2-4-4-6-7h0c-1 1 0 1 0 3h0l-3-3-1-1c-2-4-5-6-8-9l1-1 4 4h1v-1l1 1v-2-1z"></path><path d="M374 743l3 4c1 1 2 3 3 4 1-1 0-2 1-3v-1c0-1 0-1 1-2h1s1-1 2-1c-1 3-1 7-3 10 1 0 2-1 3-3 1 3 1 6 1 9 2 6 7 12 11 16l10 8c1 1 4 2 5 2 3 2 7 2 10 4h1c1 0 1 0 2 2l1-1c2 0 4-1 6-2h1 4c-2 1-3 1-4 2l-1 1c-2-1-1-1-3 0-1 0-1 1-2 1-3 0-5 2-7 2-1 1-2 2-3 2l-1 1c-1 0-2 0-3 1h-4l-2 1h-4l-15 1h-3-1c-1-1-2-1-2-1-2 1-1 1-3 1l1-1-2-3h14c-1 0-3-1-4-1l-1-1h-3c0-1 1-1 1-1 1-1 1-1 2-1l1-1v1l-1 1h1c2 0 4 0 6-1l-2-2h-3c0-1 0-2 1-3h0c0-1-1-2-1-3s1-1 2-2 2-1 2-2 0-2-1-3c0 0-1-1-1-2l-10-15c-1-2-3-4-5-5-1 0-1 0-2-1v-1c1 0 1 0 2 1 1 0 1 1 2 1v-1h1c-2-4-4-7-6-11l1-1z" class="E"></path><path d="M397 776l10 8h-2c-2 0-4 0-5-2s-3-3-3-6z" class="F"></path><path d="M385 801c2-2 5-2 7-2l2-1c4 0 10 1 13-1 1-1 2-1 3-1h0v1l2-1h0l-3 3-2 1h-4l-15 1h-3z" class="N"></path><path d="M374 754c1 0 1 0 2 1 1 0 1 1 2 1 3 3 5 6 8 10l10 15c-2 1-3 2-5 4h1c2 1 5 1 8 1 4 1 8 3 11 4 2 1 5 1 7 2-4 3-9 2-13 2-6 1-12 1-18 1h-3c0-1 1-1 1-1 1-1 1-1 2-1l1-1v1l-1 1h1c2 0 4 0 6-1l-2-2h-3c0-1 0-2 1-3h0c0-1-1-2-1-3s1-1 2-2 2-1 2-2 0-2-1-3c0 0-1-1-1-2l-10-15c-1-2-3-4-5-5-1 0-1 0-2-1v-1z" class="G"></path><path d="M393 790c3-1 5-1 8-1l1 1v1c-3 0-6 0-9-1z" class="Q"></path><path d="M393 790h-2l-1-1 1-2h8 1c4 1 7 2 11 4 1 0 1 0 2 1-4 0-9-2-12-3-3 0-5 0-8 1z" class="D"></path><path d="M654 787h1 0c2 1 3 1 4 1v1l3 1-2 1v2h1c2 0 3-1 5-1l1-1v-2h1v1c2 6 6 11 12 14s11 1 17-1c-5 5-11 7-18 8-3 0-6 0-9-1-4-1-7-3-11-3-5-1-10-1-15-1l-13-1c-2-1-4-1-6-1 0 2 0 4 1 6v1c0-1-1-1-1-1h-1l1-2c-2-1 0-2-2-3v1l-1-1v-1h2v-1c-3-1-8-1-11-2-1 0-1 0-2-2l5 1c1-1 1-1 2-1h1c1-1 2-1 2-2h-1c-1 0-2 0-3-1h0 4l2 2 1-3 4 1c2 0 7 1 8 0 4-1 8-4 12-5 1-1 3-2 4-2v-1c0-1 2-1 2-1z" class="J"></path><path d="M663 805h2c1-1 2-1 3-1h1 0 2c1 1 1 1 2 1h1s1 1 2 1h2l1 1c1 0 4-1 5 0s1 1 2 1c-3 1-5 1-8 1-1 0-4-1-5-1-4 1-7-1-10-3z" class="C"></path><path d="M616 800c1-1 1-1 2-1h1c1-1 2-1 2-2h-1c-1 0-2 0-3-1h0 4l2 2c4 1 9 1 13 2 1 1 2 1 3 1 2 1 3 0 5 1l1-1h7 0 5c3-1 9-1 12 0l2 2 1 1 2 1h-1c-1 0-1 0-2-1h-2 0-1c-1 0-2 0-3 1h-2c-7-1-13 0-19-1-3 0-6-1-9-2l-19-2z" class="N"></path><path d="M654 787h1 0c2 1 3 1 4 1v1l3 1-2 1v2h1c2 0 3-1 5-1l1-1v-2h1v1c0 1-1 2 0 3 3 6 7 10 13 13h3 0c3 1 5 0 8 0-2 1-4 2-6 2-1 0-1 0-2-1s-4 0-5 0l-1-1h-2c-1 0-2-1-2-1l-2-1-1-1-2-2c-3-1-9-1-12 0h-5 0-7l-1 1c-2-1-3 0-5-1-1 0-2 0-3-1-4-1-9-1-13-2l1-3 4 1c2 0 7 1 8 0 4-1 8-4 12-5 1-1 3-2 4-2v-1c0-1 2-1 2-1z" class="I"></path><path d="M636 800c1-1 2-1 4-1s3 1 5 2h7 0-7l-1 1c-2-1-3 0-5-1-1 0-2 0-3-1z" class="C"></path><path d="M660 791v2h1c2 0 3-1 5-1l1-1v-2h1v1c0 1-1 2 0 3-2 1-3 2-4 3-3 1-5 1-8 1-3 1-9 2-12 1v-1c4-3 11-4 16-6z" class="P"></path><path d="M644 798l2-1 1-1c1 0 2 0 3-1 1 0 2-1 4-1s2 0 3 2l-1 1h0c-3 1-9 2-12 1z" class="D"></path><path d="M634 555v-1l1-1c0-1 1-2 1-3h0l1-2v-2c1-1 1-1 1-2l1-1v-2c1-1 1 0 1-1l1-1c0-1 0-2 1-3h0v-2c1-1 1-2 1-2l1-1v-3h1c0-1 0-1 1-2v-1-1c1-2 1-3 2-4l1-1c0-1 0-2 1-3s1-2 1-3c1-1 1-1 1-2s1-2 1-3l3-5c1-2 2-4 2-6 1-1 2-3 2-5l1-3s0-1 1-1c1-2 2-4 2-6h0l1-1c0-2 1-4 2-6 0-1 1-2 1-3v-1l1-1c0-1 0-1 1-1v-4l1 1c0-1 1-3 2-5v-1h0c1-2 1-3 1-5h0l1-1c1-1 1-2 1-4h0v1c1-1 1-2 1-3s0 0 1-1c0-1 0-2 1-3s1-2 1-4v-1 2l3-6c0 1 1 2 1 3h0v2-1c1 1 1 2 1 3l-1 1 1 1-1 2h0c-1-1 0-1-1-2h0l-1-1v4l-1 2-1-1v1c0 1-1 2-1 3l-1 1v1 2l-1 1v2l1-1c0-1 0 0 1-1v1l-1 1v1c0 1 0 1-1 2v2l-1 1c0 1 0 2-1 3h0v-1l-1-1v-1c0 1-1 1-1 2s0 1-1 2v1l1-1 1-1v-1h0v4c-1 1-1 1-1 2v2l-1-2v-1h-1v4 1c0 1 0 2-1 3h0v-3l-1 1c0 1 0 2 1 3 0 1 0 2-1 4l-1 2v-1c-1 1 0 3-1 5l-1-1v1 1l-1-1v-2h0 0c-1 1 0 1-1 2v2l-1 1v1h-1v2c-1 1-1 1-1 2h0v1h1 0c0 3-1 6-2 8h0v-1c0 1-1 1 0 3l-1 2-1-1v1h0-1c-1 0-1 0-2 1l-1 3v2l1 1s-1 1-1 2h-2v2c-1 1-1 1 0 3h0v-1l1 1c0 1 0 2-1 3v-1l-1-1v-2c-1 2 0 6-1 7v2l-1 1c0 1 0 2-1 3v-4l1-1v-2c-1 1-1 2-2 3v1c0 1 0 1-1 2v1 1h1c1 2 0 4 0 6-3 5-5 11-7 17 0 2-1 5-2 7-1 1 0 3-2 5l1 1c-1 0-2 1-2 2h1l-1 1v2h0l-1 1v2h0v1h0l-2 2h0c-1 1 0 2-1 3 0 1 0 1-1 2v1l-1 1v1c-1-2-1-5-1-7l6-32z" class="M"></path><path d="M346 803h0c7-4 13-4 21-3 4 1 8 1 12 1 2 0 1 0 3-1 0 0 1 0 2 1h1 3l15-1h4l-3 2-4 2c-1 0-2 0-2 1v1l-1 1-1 1v2h-1c-1 2-2 3-3 4h0c-1 1-2 2-2 3-1 2-5 5-7 6v1h-2-2c-1-1-1 0-1-1l-1 1h0 0l-1-1-4-4-3-2-5-4h-1l-1-1c-1-1-3-2-4-3h-1-3l-1 1h-1 0-1l-1 1c-2-1-5 0-8 0-1 0-2 0-3-1h-4-1c-1 0-2-1-3-1h0c-3-1-4-2-6-3l-2-2 1-1c1 1 2 1 2 1 1 0 2 1 3 1 2 1 4 1 6 1l1-1 1 1 4-1h1l1-1c1 0 1 0 3-1z" class="D"></path><path d="M346 803h0c7-4 13-4 21-3 4 1 8 1 12 1 2 0 1 0 3-1 0 0 1 0 2 1h1 3l15-1h4l-3 2-4 2c-1 0-2 0-2 1-2 0-5 1-6 0-8 0-16-1-23 1-4 1-7 0-11 1h-1c-1 1-2 1-3 1-2 1-4 2-7 2-1 0-3 0-5 1-1 0-2 0-3-1h-4-1c-1 0-2-1-3-1h0c-3-1-4-2-6-3l-2-2 1-1c1 1 2 1 2 1 1 0 2 1 3 1 2 1 4 1 6 1l1-1 1 1 4-1h1l1-1c1 0 1 0 3-1z" class="B"></path><path d="M346 803h0c7-4 13-4 21-3l1 2c-2 0-4-1-6 0 0 0-1 1-2 1-1 1-1 1-2 1v-1l2-1-1-1-3 3-1-1h0c1-1 1-2 2-2h0-3c-3 0-5 2-8 3v-1z" class="D"></path><path d="M379 801c2 0 1 0 3-1 0 0 1 0 2 1h1 3c-2 2-4 1-5 2-2 0-4-1-5 0-2 0-3 0-4 1-2 0-3 0-4-1h0-10c1 0 2-1 2-1 2-1 4 0 6 0l-1-2c4 1 8 1 12 1z" class="P"></path><path d="M388 801l15-1h4l-3 2-4 2c-1 0-2 0-2 1-2 0-5 1-6 0-8 0-16-1-23 1-4 1-7 0-11 1h-1c-1 1-2 1-3 1-2 1-4 2-7 2-1 0-3 0-5 1-1 0-2 0-3-1h-4-1c-1 0-2-1-3-1h0c-3-1-4-2-6-3l4 1c1 0 1 1 2 1h1 3c4 1 8 1 12 0 3 0 5-1 7-1l6-2c2 0 4-1 5-1 2 0 3 0 5-1 1 1 2 1 4 1 1-1 2-1 4-1 1-1 3 0 5 0 1-1 3 0 5-2z" class="Q"></path><path d="M408 276v3 3l-1 1c0 2 1 4 0 6 0 1 0 4-1 5v3 5c0 1 1 3 1 4 1 0 1 1 1 1 1 0 2 1 3 1v-1h1v2c0-1 1-3 0-4l1-1v-2c0-2 1-3 2-5 0-1 1-2 1-3 3-6 6-10 10-15l2 1 3-2c-1 3-3 5-5 7-5 9-7 19-5 29l2-2c2 10 5 19 11 27 3 3 5 6 8 8 5 4 9 7 15 10h-4c11 6 23 10 36 14h-9c-4 0-9-2-13-3l-1 1-13-4h0c-1 0-1 0-2-1l-3-2c-1 0-2 0-3-1l-2-1c-1-1-2-1-2-2-2 0-2-1-3-2-2-1-3-2-5-4 0 1 0 1 1 2h-1c-1-1-2-1-3-3 0-1-2-4-4-5l-3-3-1-1c-2-1-4-3-5-6 1-2-2-5-2-7-2-3-2-7-4-10-1-5-4-9-5-14-1-4-1-8 0-12 0-6 1-12 2-17z" class="F"></path><defs><linearGradient id="I" x1="451.109" y1="350.603" x2="444.193" y2="353.994" xlink:href="#B"><stop offset="0" stop-color="#544e4e"></stop><stop offset="1" stop-color="#616361"></stop></linearGradient></defs><path fill="url(#I)" d="M434 343c3 0 5 5 6 5s1-1 2-1c5 4 9 7 15 10h-4c-7-3-14-8-19-14z"></path><path d="M416 294c3-6 6-10 10-15l2 1c-3 4-6 7-8 12 0 1-1 4-2 5l-1 1h0c0 2-1 3-2 5 0-3 1-6 1-9z" class="L"></path><path d="M423 312c2 10 5 19 11 27 3 3 5 6 8 8-1 0-1 1-2 1s-3-5-6-5c-7-6-12-19-13-29l2-2z" class="R"></path><path d="M421 336c1 1 3 2 4 2h0c3 3 5 6 7 9 10 10 21 17 35 21l-1 1-13-4h0c-1 0-1 0-2-1l-3-2c-1 0-2 0-3-1l-2-1c-1-1-2-1-2-2-2 0-2-1-3-2-2-1-3-2-5-4 0 1 0 1 1 2h-1c-1-1-2-1-3-3 0-1-2-4-4-5-2-3-3-6-5-10z" class="S"></path><path d="M408 276v3 3l-1 1c0 2 1 4 0 6 0 1 0 4-1 5v3 5c0 1 1 3 1 4 1 0 1 1 1 1 1 0 2 1 3 1v-1h1v2c0-1 1-3 0-4l1-1v-2c0-2 1-3 2-5 0-1 1-2 1-3 0 3-1 6-1 9 1-2 2-3 2-5h0l1-1-1 8c0 5 0 9 1 14 1 7 4 13 7 19h0c-1 0-3-1-4-2 2 4 3 7 5 10l-3-3-1-1c-2-1-4-3-5-6 1-2-2-5-2-7-2-3-2-7-4-10-1-5-4-9-5-14-1-4-1-8 0-12 0-6 1-12 2-17z" class="W"></path><path d="M415 303c1-2 2-3 2-5h0l1-1-1 8c0 3 0 8-1 9v-1l-1-10z" class="J"></path><path d="M419 331l-2-2c-1-1-1-2-2-3h-1c-1-5-1-8 0-13h2c0 7 1 12 3 18z" class="F"></path><path d="M417 305c0 5 0 9 1 14 1 7 4 13 7 19h0c-1 0-3-1-4-2l-2-5c-2-6-3-11-3-18v1c1-1 1-6 1-9z" class="K"></path><path d="M550 231c8-2 19 0 27 4 3 1 8 4 10 7v1 2l-1 1 1 3v1h1c1-1 3-2 5-3 7-1 16-1 23 1h1c16 3 29 18 37 31 3 4 4 8 6 13l3 6-1 2c0-1-3-8-3-9-6-13-15-26-28-34v-1c-2-1-3-2-5-2l-2-2-1 6v4 1c-1 9-1 17-4 26l-1 1h0-1 0c-1 1-1 2-1 2v2h-1l-1-12-1-1-1 1v1c2 9 2 17 1 26-3 26-19 50-39 65l-10 7c-4 2-8 5-13 7-15 5-28 7-43 5-4-1-9-1-12-3h-1c2-1 5-2 8-3h8 10 1c22-2 45-12 59-28 0-1 2-1 2-1 1-1 2-2 4-3h0c-2 2-4 4-6 5-3 2-5 5-8 7l-8 6v1c-2 0-4 2-5 2-4 2-7 4-10 5l-8 3c-1 0-2 0-3 1h-3c-1 0-2 1-3 1s-2 0-3 1h-3c-2 1-4 1-5 1-3 1-15-1-19 0h-1c-1 0-1 1-2 1v1l1 1c1 0 2 0 3 1h3c2 0 3 0 5 1 1 0 14 1 16 0h4c1-1 1-1 2-1l3-1h1l2-1h3l1-1c1 0 3-1 4-1s1-1 2-1h1l2-1s1-1 2-1c3-1 7-3 9-5l5-3s1-1 2-1c0-1 1-1 1-2 2 0 2-1 3-2 2-1 3-2 4-4h1c1 0 5-5 6-6v-1l5-4-1-1 2-2 1-1c0-2 1-3 3-4h0c1 0 1-1 2-1 0-1 1-2 1-3h0l1-1v-1c3-4 5-9 7-13l1-1c1-3 1-5 2-8 1-1 1-2 1-3v-2c1-1 1-2 1-4h0c1-2 1-4 1-5v-5c0-1 1-3 1-4s-1-3-1-4v-1-4c-1-1-1-2-1-3v-1-1l-1-1h0v-2l-1-1v-2l-1-1v-2h0c-1-1-1-2-1-3-1-1-1-2-2-3h0l1 2v1 1l1 1c0 1 0 2 1 3s0 1 1 3v1c0 2 0-1 0 1v3c1 0 1 0 1 1s-1 2 0 3v4c1 2 1 4 1 6v6c-1 1-1 2-1 4-1 2 0 0 0 1v1l-1 1h-1v-1-4c1-2 1-9 1-11-1-1-1-2-1-3v-3c-1-1-1-1-1-2l-1-3v-1c-1-1-1-2-1-3l-1-1v-1-1l-1-1-3-6c-1-2-2-4-3-5v-1l-1-1-2-2c0-1-1-2-2-3-1-2-4-5-6-6s-3-3-5-5c-1 0-2-1-3-2l-1-1c-2-1-4-3-6-3l-7-3c-1-1-1-1-2-1l-1-1h-2c-1-1-2-1-3-1h-2-1c-2-2-4 0-6-1-1-1-2 0-3 0v-1l-4-2 1-1h4 0l1 1 3-1z" class="E"></path><path d="M598 253l-3-4c1-1 2-1 3-1 2 2 3 3 4 5h-4z" class="T"></path><path d="M576 243c1-1 3-1 4-1 2 2 5 4 6 4l1 3v1l2 2c2 1 4 3 5 6v1c-5-6-12-12-18-16z" class="L"></path><path d="M598 253h4 1c3 5 5 10 6 15 2 4 4 9 4 13l-1 1v1c-3-11-7-21-14-30z" class="K"></path><defs><linearGradient id="J" x1="608.688" y1="280.356" x2="592.803" y2="251.064" xlink:href="#B"><stop offset="0" stop-color="#292826"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#J)" d="M588 250h0c2 0 4-1 5-1s2 2 2 2c2 2 3 5 5 8 5 9 9 18 10 29 0 2 1 5 0 7v1c-1-4-1-8-2-12-3-9-8-18-14-25v-1c-1-3-3-5-5-6l-2-2h1z"></path><path d="M550 231c8-2 19 0 27 4 3 1 8 4 10 7v1 2l-1 1c-1 0-4-2-6-4-1 0-3 0-4 1-8-4-14-7-23-9h-8l-4-2 1-1h4 0l1 1 3-1z" class="X"></path><path d="M577 235c3 1 8 4 10 7v1 2l-5-4c-1-1-2-2-3-2-3-1-4-2-6-3 1-1 2 0 4-1z" class="P"></path><path d="M542 231h4 0l1 1 3-1c5 0 12 2 16 4l-12-2-1 1h-8l-4-2 1-1z" class="H"></path><path d="M553 234l1-1 12 2c5 2 10 4 14 7-1 0-3 0-4 1-8-4-14-7-23-9z" class="U"></path><defs><linearGradient id="K" x1="606.281" y1="260.457" x2="626.121" y2="275.343" xlink:href="#B"><stop offset="0" stop-color="#7a7675"></stop><stop offset="1" stop-color="#b9b8b7"></stop></linearGradient></defs><path fill="url(#K)" d="M598 248c8 0 20 0 26 4h0l-1 6v4 1c-1 9-1 17-4 26l-1 1h0-1 0c-1 1-1 2-1 2v2h-1l-1-12-1-1c0-4-2-9-4-13-1-5-3-10-6-15h-1c-1-2-2-3-4-5z"></path><path d="M604 253l-1-3c1 0 0 0 1 1 2 2 3 5 5 7 3 5 5 12 7 18 2 3 2 10 1 14h0c-1 1-1 2-1 2v2h-1l-1-12-1-1c0-4-2-9-4-13-1-5-3-10-6-15h1z" class="I"></path><path d="M604 253l-1-3c1 0 0 0 1 1 2 2 3 5 5 7 3 5 5 12 7 18 2 3 2 10 1 14h0c-1 1-1 2-1 2 0-4 0-9-1-14-1-9-6-17-11-25z" class="K"></path><path d="M598 248c8 0 20 0 26 4h0l-1 6c-1 0-1 0-1-2h-1c-4 0-9-2-12 0l1 1c-1 0-1 1-1 1-2-2-3-5-5-7-1-1 0-1-1-1l1 3h-1-1c-1-2-2-3-4-5z" class="R"></path><defs><linearGradient id="L" x1="529.87" y1="811.926" x2="552.524" y2="858.51" xlink:href="#B"><stop offset="0" stop-color="#9a9997"></stop><stop offset="1" stop-color="#c3c2c1"></stop></linearGradient></defs><path fill="url(#L)" d="M548 782c9 8 16 17 24 27 3 3 6 7 9 10 2 3 4 5 6 8h-1l-1-2c-2 1-1 0-2 1h-2v1c-2 0-3-1-4 0-2 0-7 1-8 0-2 0-2 0-3-1h-3c-1 0-1 0-2-1h-1c-1 0-3-1-4-1l-2-1c-1 0-2 0-3 1h-1 0-1c-1 1-3 2-3 3h-1l-1 1-1 1-1 1-1 1v2l-2 2-3 4-1 3-2 3v2c-1 0-1 0-1 1l-1 2-2 5c-1 1-1 2-2 3l-1 2c0 1-1 2-1 4h-1c0 1-1 2-2 3l-1 3h0c0 1 0 1-1 2h0c-1 2-4 4-5 6h-1-1v3c-1 0 0 1 0 1l-1 1v2h-2v-1l-1-1v-2-1-2l-4-12-4-13-5-11-2-7c0-1-1-1-1-2-1-2-2-4-2-7-1-1-2-2-2-4 0-1-1-2-1-4-1-3-3-5-4-9-1-2-2-4-2-6-1-1-2-3-2-4s-1-2-1-3l-1-3h2l15 42c6 15 12 30 16 46 1-1 1-1 1-2 1-2 2-5 2-7l4-11 16-45c3-9 6-18 10-26h1l3-8z"></path><defs><linearGradient id="M" x1="551.619" y1="783.701" x2="553.557" y2="807.192" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#8d8b89"></stop></linearGradient></defs><path fill="url(#M)" d="M548 782c9 8 16 17 24 27 3 3 6 7 9 10-2 0-4-3-6-4l-7-9c-3 0-6 1-9 1s-6-1-8 0h-3-7c-1 0-2 1-2 1h0v-2l2-5 2-6c1-2 1-3 2-5l3-8z"></path><defs><linearGradient id="N" x1="404.346" y1="803.555" x2="412.678" y2="834.692" xlink:href="#B"><stop offset="0" stop-color="#969492"></stop><stop offset="1" stop-color="#c3c2c0"></stop></linearGradient></defs><path fill="url(#N)" d="M440 787h2c3-1 4-4 7-1v1h1l-2 4-1 3c0 2-1 3-2 4l-2 5-1-1c-1 0-1 0-1-1-2 7-5 15-8 21l-1-1c0 2-1 4-1 6h0l2-2 1 1-2 2-1 1-1-1v-3h0l1-2h0c0-1 0-1-1-2v1l-1-1c-1 1-2 1-3 1h-1c-1 0-2 0-3 1h-3c-1 0-1 0-2 1h-2c-1 0-2 1-3 1-1 1-2 1-2 2h-1c-2 1-3 2-4 3l-2 1-2 2-2 1-1 1c-1 0-2 1-3 2l-10 5-2 2v1l-1 1c-1 4-5 6-8 7l-1 2c-1 1-2 1-3 2v2c0 2-1 3-1 4l-1-4c-2-8 3-15 7-21 1-2 1-3 2-4s1-2 2-2v-1c0-1 1-1 1-3l-3-4h0l1-1c0 1 0 0 1 1h2 2v-1c2-1 6-4 7-6 0-1 1-2 2-3h0c1-1 2-2 3-4h1v-2l1-1 1-1v-1c0-1 1-1 2-1l4-2 3-2 2-1h4c1-1 2-1 3-1l1-1c1 0 2-1 3-2 2 0 4-2 7-2 1 0 1-1 2-1 2-1 1-1 3 0l1-1c1-1 2-1 4-2 1-1 1-2 3-2z"></path><path d="M440 787h2c3-1 4-4 7-1v1h1l-2 4-1 3c0 2-1 3-2 4l-2 5-1-1c-1 0-1 0-1-1-2 7-5 15-8 21l-1-1 4-11v-2h0c0-1 1-2 1-3-3-3-7-3-10-4-2 0-3-1-5-2-1 1-3 1-4 1-4 1-7 3-11 6-8 7-14 15-19 25-2 3-4 7-5 11l-3 6c-3 4-8 5-10 9v2c0 2-1 3-1 4l-1-4c-2-8 3-15 7-21 1-2 1-3 2-4s1-2 2-2v-1c0-1 1-1 1-3l-3-4h0l1-1c0 1 0 0 1 1h2 2v-1c2-1 6-4 7-6 0-1 1-2 2-3h0c1-1 2-2 3-4h1v-2l1-1 1-1v-1c0-1 1-1 2-1l4-2 3-2 2-1h4c1-1 2-1 3-1l1-1c1 0 2-1 3-2 2 0 4-2 7-2 1 0 1-1 2-1 2-1 1-1 3 0l1-1c1-1 2-1 4-2 1-1 1-2 3-2z" class="K"></path><path d="M409 799h4c1-1 2-1 3-1l-9 6c-1 0-2-1-3-2l3-2 2-1z" class="C"></path><path d="M418 800c4-2 11-4 15-3 3 1 4 3 5 5 0 3-1 6-2 8v-2h0c0-1 1-2 1-3-3-3-7-3-10-4-2 0-3-1-5-2-1 1-3 1-4 1z" class="X"></path><path d="M379 835c1 1 1 1 1 2 2-2 2-4 4-6 0-1 2-2 3-2-2 3-3 6-4 9-1 2-2 6-4 8-2 3-4 4-6 6-2 1-3 3-4 5l-1 1c1-2 1-7 3-9l-1-1c2-5 6-9 9-13z" class="C"></path><path d="M404 802c1 1 2 2 3 2-4 4-8 7-11 12l-7 10c-1 1-2 3-2 3-1 0-3 1-3 2-2 2-2 4-4 6 0-1 0-1-1-2 3-6 6-11 11-15-3 1-5 3-6 5-4 4-5 9-9 13 1-2 1-3 2-4s1-2 2-2v-1c0-1 1-1 1-3l-3-4h0l1-1c0 1 0 0 1 1h2 2v-1c2-1 6-4 7-6 0-1 1-2 2-3h0c1-1 2-2 3-4h1v-2l1-1 1-1v-1c0-1 1-1 2-1l4-2z" class="B"></path><path d="M392 814l1 1c1-2 3-3 4-5 1-1 2-1 2-2l1-1 1 1-2 2-3 3-1 1c0 1-5 6-5 6-3 1-5 3-6 5-4 4-5 9-9 13 1-2 1-3 2-4s1-2 2-2v-1c0-1 1-1 1-3l-3-4h0l1-1c0 1 0 0 1 1h2 2v-1c2-1 6-4 7-6 0-1 1-2 2-3h0z" class="M"></path><path d="M440 787h2c3-1 4-4 7-1v1h1l-2 4-1 3c0 2-1 3-2 4l-2 5-1-1c-1 0-1 0-1-1-1-1-2-2-2-3l-1-1-2-1c-6-3-13 0-18 2l-1-1c1 0 2-1 3-2 2 0 4-2 7-2 1 0 1-1 2-1 2-1 1-1 3 0l1-1c1-1 2-1 4-2 1-1 1-2 3-2z" class="N"></path><path d="M439 798c2-1 3-1 5-2l1 1s-1 1-1 2c-1 0-1 1-1 2l-1 1c-1 0-1 0-1-1-1-1-2-2-2-3z" class="E"></path><path d="M449 787h1l-2 4-1 3-1-1v-1c0-1 0-1-1-2l-1 1h-1c0 2-1 2-1 2l-2-1c1-2 3-5 6-5h2 1z" class="W"></path><path d="M191 259h0c4-3 7-5 11-6l-3 2c0 4 3 8 6 10 1 2 4 3 6 3 3 1 6 1 9 1 17 4 34 17 43 32 5 7 9 15 12 23l12 26 50 121 37 90 10 26c1 4 4 11 3 15v1l1-1v1c-1 3-3 6-3 9v1l-1 1c0 6-1 14 1 19l3 14v1c4 10 9 19 10 30 0 4-3 9-5 12l-4-12v-1c1-4-1-5-1-9h0l1 2h1c1 1 1 1 1 3 1 1 1 2 1 4v1 2l-1 1 1 1h-1l1 2v-3l1-1c0-1-1-2 0-3v-3c1-1 1-2 1-3h-1l1-1-1-3c-1-1-1-1-1-2v-1l-2-3v-1h-1v-2l-1-1c0-4-4-9-3-13h1c0-1-1-2-1-2v-2l-2-6c0-7-1-16 1-23v-2-3c1-1 1-5 1-6l-1-1v-1c-1 0-1-2-1-2l-1-1h1c-1-1-1-2-1-3s0-1-1-2v-2h0l-1-1v-1c0-1 0-1-1-2h-1v-1-3l-1 1c-1-1 0-1 0-2l-1-1v-1c-1-1-1-1-1-3v2c-1-1-1-2-1-3v-1h-1v-2c-2-1-6-13-6-15l-1-1c-2-4-4-9-5-13-3-7-7-15-9-22-1-1-1-1-1-2-3-4-4-10-6-15l-14-34c-4-10-9-20-13-31v-1c-3-4-4-9-6-14-3-6-6-13-8-19v-1c-3-5-5-10-6-15v-2h-1v-1c-1-1-1-2-1-2l-1-1c-1-3-2-5-3-7 0-1 0-1-1-2v-1-1-1h-1v-2h0l-1-2c0-1-1-2-2-4h0l-1-1v-1-2l-2-2v-1c-1-1 0-1-1-3l-1-1v-1c0-2-1-1-1-3v-1h-1v-1c0-1-1-3-1-4l-1 1c-1 0-1-1-1-2h0c-1-2-1-2-1-4l-1-2h0-1l1-1h-1 0-1l1-1-1-1v-1l-1-1 1-1c-1-1-1-1-1-2-1-1-2-1-2-2 1-1-1-2-1-4-1-1-1-2-2-3h0-1l1-1c0-1-1-2-1-3-1-1-1-1-1-2l-1-2c0-1-1-1-1-2s0-1-1-2c-1-2-2-4-4-6h-1l-1-1h1l-2-1-2-3v-1c-1 0-2-1-3-2v-1l-1 1v-1c-1-1-1-1-2-1v-1l-1-1v1c-1-1-1-1-1-2h-1v-1c-1 0-1-1-1-2h-1c-1-1-1 0-2-1v-1h-1l-1-1c-2 0-1-1-2-1h-2c-1-1 0-1-1-2-2 0-4-1-6-2-1 0-1 0-2-1h-1 0l-2-1v1c-1-1-3-1-4-1-2-1-6 0-8-1l-6-2c-3-1-5-4-6-7h-1c0-1 0-2-1-3v-1c0-1 0 0-1-1l-1 1c0 1-1 1-1 1h-1-1z" class="M"></path><path d="M552 768h1l1 1 2-1c1 1 2 2 3 1 3 1 6 3 9 4 0 0-1 0-1 1l5 1c-1 0-2 0-4 1h-1l7 4-1 1-2-2c-1 0-2 0-2-1l-1 1 1 1 5 8v-1c2 0 3 1 4 2s3 2 5 3h1v1h-4v1c1 1 2 0 4 0 1 0 3 0 4 1h0c6 0 10 3 15 4 8 5 15 10 21 17v-1l3 3h0c0-2-1-2 0-3h0c2 3 4 5 6 7l1 1h1v1l2 1-1 2c1 2 2 4 4 6 2 3 5 5 6 8 2 3 3 6 4 9 1 4 2 7 1 12h0c-1-1-1-1-1-2 0-3-2-5-4-6-1-1-1-1-2-1h-1c-2-1-5-4-7-7l-2-5c-1-2-4-2-6-4l-4-3h-1l-4-3c-2-1-4-2-5-3l-2-1c-1-1-2-1-3-1v-1h-2-1c-1-1-1-1-2-1-1-1-3 0-4 0-1-1-3-1-4-1-1 1-4 1-5 1h-1l-1 1c1 1 1 1 1 3h-1-1c-1 0-1 0-2-1h1c-2-3-4-5-6-8-3-3-6-7-9-10-8-10-15-19-24-27l-3 8h-1l8-22z" class="O"></path><path d="M635 831v-1l1-1c4 7 10 13 13 21l-3-3h0l-1-1c-1-2-3-3-4-5l-1-2-3-3-2-5z" class="E"></path><path d="M584 794c1 0 3 0 4 1h0c-3 0-6 1-9 3v1 1c1 2 1 5 2 8l3 9c-1 0-2-2-2-3h-1v-1-1c0-1 0-2-1-3v-1c0-1 0-2-1-3l-1-1v-1l-1-1v-2c0-1-1-1-1-1 0-2 0-2 1-4 2-1 4 0 7-1z" class="C"></path><path d="M624 815l3 3h0c0-2-1-2 0-3h0c2 3 4 5 6 7l1 1h1v1h0c-1 0-2 0-2-1 0 2 2 4 3 6l-1 1v1l-1 2c-3-6-6-12-10-17v-1z" class="F"></path><path d="M634 833l1-2 2 5 3 3 1 2c1 2 3 3 4 5l1 1h0l3 3 2 8c-3-4-6-6-9-9-4-5-6-11-8-16z" class="C"></path><defs><linearGradient id="O" x1="588.267" y1="795.548" x2="593.786" y2="805.696" xlink:href="#B"><stop offset="0" stop-color="#7b7978"></stop><stop offset="1" stop-color="#918f8e"></stop></linearGradient></defs><path fill="url(#O)" d="M585 814c-3-5-3-10-5-15 2-1 5-2 8-3 5-1 13 4 17 6h0-2-1-3l-3 1c-4 1-9 3-12 6l1 3v2z"></path><path d="M552 768h1l1 1 2-1c1 1 2 2 3 1 3 1 6 3 9 4 0 0-1 0-1 1l5 1c-1 0-2 0-4 1h-1l7 4-1 1-2-2c-1 0-2 0-2-1l-1 1 1 1 5 8v-1c2 0 3 1 4 2s3 2 5 3h1v1h-4v1c1 1 2 0 4 0-3 1-5 0-7 1-1 2-1 2-1 4 0 0 1 0 1 1v2l1 1v1l1 1c1 1 1 2 1 3v1c1 1 1 2 1 3v1 1h1c0 1 1 3 2 3l3 8-13-17c-8-10-15-20-25-28h-1v2l-3 8h-1l8-22z" class="B"></path><path d="M554 769l2-1c1 1 2 2 3 1 3 1 6 3 9 4 0 0-1 0-1 1l5 1c-1 0-2 0-4 1h-1l-13-7z" class="H"></path><path d="M559 779l1-1c1 0 2 1 3 2 2 2 4 5 5 7 1 1 1 1 1 2l1 2c-2 1-5-5-7-6l-4-6z" class="E"></path><path d="M563 785c0 2 1 4 2 5 2 2 5 4 5 6-4-3-7-8-11-12-3-2-6-4-10-6l2-5 8 6 4 6z" class="S"></path><path d="M574 788v-1c2 0 3 1 4 2s3 2 5 3h1v1h-4v1c1 1 2 0 4 0-3 1-5 0-7 1-1 2-1 2-1 4 0 0 1 0 1 1v2l1 1v1l1 1c1 1 1 2 1 3l-1 1c0-2-1-4-2-5l-1-1-3-4v-1l2 2v-3c1-1 1-1 1-3-1 0-1-1-2-1l-1 1h0l-3-3-1-2c1-1 0-1 1-1l1 1h3v-1z" class="D"></path><defs><linearGradient id="P" x1="610.828" y1="802.498" x2="606.821" y2="835.922" xlink:href="#B"><stop offset="0" stop-color="#959391"></stop><stop offset="1" stop-color="#c7c6c4"></stop></linearGradient></defs><path fill="url(#P)" d="M585 814v-2l-1-3c3-3 8-5 12-6l3-1h3 1 2 0c4 2 6 4 9 7 3 2 6 5 9 8 3 5 7 12 9 18 2 4 4 9 7 13 1 2 3 3 5 5h-1c-2-1-5-4-7-7l-2-5c-1-2-4-2-6-4l-4-3h-1l-4-3c-2-1-4-2-5-3l-2-1c-1-1-2-1-3-1v-1h-2-1c-1-1-1-1-2-1-1-1-3 0-4 0-1-1-3-1-4-1-1 1-4 1-5 1h-1l-1 1c1 1 1 1 1 3h-1-1c-1 0-1 0-2-1h1 1 1c-1-5-3-9-4-13z"></path><defs><linearGradient id="Q" x1="495.083" y1="811.417" x2="473.754" y2="834.978" xlink:href="#B"><stop offset="0" stop-color="#616060"></stop><stop offset="1" stop-color="#9e9c9a"></stop></linearGradient></defs><path fill="url(#Q)" d="M463 770h-1-1c-1 1-2 1-4 1h0l8-4v-1l1-1v-2c1 1 1 1 1 2l2 5 1 1 1 4 1 3c1 0 1 1 1 2l5 13 1 3c0 1 1 2 1 3s1 3 2 4c0 2 1 4 2 6 1 4 3 6 4 9 0 2 1 3 1 4 0 2 1 3 2 4 0 3 1 5 2 7 0 1 1 1 1 2l2 7 5 11 4 13 4 12v2 1c-1 0-3 1-4 0l-2-2c-2-1-5-3-6-6h-1c-1-2-2-4-3-5l-9-19c-2-5-5-10-7-16-1 0-1-1-2-2l-1-2-1-1v-1c-1-1-4-3-6-4h0c-2-1-4 0-6 0-1 1-3 0-4 0-1 1-2 1-3 1-1 1-3 0-4 0l-1 1c-4 0-8 0-12-1-1 0-2 1-3 2l-1-1-2 2h0c0-2 1-4 1-6l1 1c3-6 6-14 8-21 0 1 0 1 1 1l1 1 2-5c1-1 2-2 2-4l1-3 2-4c1-2 2-3 3-5v-1c2-2 5-3 7-4v-1-1l6-4-1-1 2-1h-1l-3 1z"></path><path d="M473 780l5 13 1 3c-1 0-3-1-4-1l-1-1c-3-2-5-4-8-6-1 0-2 1-3 1 1-2 3-3 5-5 2-1 4-2 5-4z" class="S"></path><path d="M463 770h-1-1c-1 1-2 1-4 1h0l8-4v-1l1-1v-2c1 1 1 1 1 2l2 5 1 1 1 4 1 3c1 0 1 1 1 2-1 2-3 3-5 4-2 2-4 3-5 5-8 7-12 15-18 23-3 3-6 6-8 9-1 1-2 3-4 4l-2 2h0c0-2 1-4 1-6l1 1c3-6 6-14 8-21 0 1 0 1 1 1l1 1 2-5c1-1 2-2 2-4l1-3 2-4c1-2 2-3 3-5v-1c2-2 5-3 7-4v-1-1l6-4-1-1 2-1h-1l-3 1z" class="F"></path><path d="M472 780h1c-1 2-3 3-5 4h-4c3-2 5-3 8-4z" class="T"></path><path d="M464 784h4c-2 2-4 3-5 5-8 7-12 15-18 23-3 3-6 6-8 9-1 1-2 3-4 4l-2 2h0c0-2 1-4 1-6l1 1v2l13-15c6-9 10-18 18-25z" class="J"></path><defs><linearGradient id="R" x1="467.66" y1="776.787" x2="459.92" y2="785.333" xlink:href="#B"><stop offset="0" stop-color="#545353"></stop><stop offset="1" stop-color="#706f6f"></stop></linearGradient></defs><path fill="url(#R)" d="M459 781c0-1 1-1 2-2h1 0l2-1c1-1 2-2 4-2l3-1 1 3c1 0 1 1 1 2h-1l-1-1c-5 3-11 6-15 11-2 2-3 3-4 6-1 2-4 7-6 9l-3 3v-1l5-9c1-1 2-2 2-4l2-2c1-1 2-2 3-4 2-1 3-4 5-6h-2l1-1z"></path><path d="M463 770h-1-1c-1 1-2 1-4 1h0l8-4v-1l1-1v-2c1 1 1 1 1 2l2 5 1 1 1 4-3 1c-2 0-3 1-4 2l-2 1h0-1c-1 1-2 1-2 2l-1 1h2c-2 2-3 5-5 6-1 2-2 3-3 4l-2 2c-1-1-1-1-2-3l2-4c1-2 2-3 3-5v-1c2-2 5-3 7-4v-1-1l6-4-1-1 2-1h-1l-3 1z" class="H"></path><path d="M460 776l6-2c-3 2-7 3-7 7l-1 1h2c-2 2-3 5-5 6v-3h0c0-1 0-1-1-2v-1h-1v-1c2-2 5-3 7-4v-1z" class="V"></path><path d="M453 782h1v1c1 1 1 1 1 2h0v3c-1 2-2 3-3 4l-2 2c-1-1-1-1-2-3l2-4c1-2 2-3 3-5z" class="E"></path><path d="M452 792v-3-1c0-1 1-3 2-5 1 1 1 1 1 2h0v3c-1 2-2 3-3 4z" class="N"></path><defs><linearGradient id="S" x1="492.215" y1="831.93" x2="464.581" y2="856.59" xlink:href="#B"><stop offset="0" stop-color="#999896"></stop><stop offset="1" stop-color="#c0bebd"></stop></linearGradient></defs><path fill="url(#S)" d="M437 821h1c4-2 8-9 11-12 0-1 1-3 2-3 0 0 4 1 5 1 4 1 9 1 12 2 4 1 7 3 9 5h1v1c0 1 1 2 2 3l3 6 13 26 4 9c2 2 3 3 4 6l1 1v1h0v-1l4 12v2 1c-1 0-3 1-4 0l-2-2c-2-1-5-3-6-6h-1c-1-2-2-4-3-5l-9-19c-2-5-5-10-7-16-1 0-1-1-2-2l-1-2-1-1v-1c-1-1-4-3-6-4h0c-2-1-4 0-6 0-1 1-3 0-4 0-1 1-2 1-3 1-1 1-3 0-4 0l-1 1c-4 0-8 0-12-1-1 0-2 1-3 2l-1-1c2-1 3-3 4-4z"></path><path d="M599 299v-5l1 2 2 1 1 7c0 10-3 20-8 29-1 5-2 8-5 12-1 2-3 5-4 7l-5 7c-14 16-37 26-59 28h-1l-1-2h-2c5-1 11-1 16-3 2 0 4 0 5-1-18-4-44 0-61 10l-6 4c-4 3-7 7-10 11 0 1-1 2-1 3v-2l1-1c1-3 4-6 4-9-2 1-3 3-5 4 3-4 5-7 9-11h1l1-1c3-1 5-4 6-6 1-1 3-2 4-2-1-1-2-1-3-1l1-2h6v-1-1c2 0 4 0 6-2 1-1 7-2 8-2l27-8c21-7 41-18 54-36 4-5 7-11 10-17h1c2-2 4-4 4-7v-1c1-1 1-1 1-2s1-2 2-2z" class="F"></path><path d="M486 378h0c1 0 1 0 2-1 1 1 1 0 2 0h3l-11 4c-1-1-2-1-3-1l1-2h6z" class="S"></path><defs><linearGradient id="T" x1="595.908" y1="314.594" x2="601.013" y2="315.188" xlink:href="#B"><stop offset="0" stop-color="#171414"></stop><stop offset="1" stop-color="#313130"></stop></linearGradient></defs><path fill="url(#T)" d="M600 296l2 1 1 7c0 10-3 20-8 29-3 2-4 4-6 6v1l-8 8v-2c11-9 17-21 19-35v-15z"></path><path d="M591 311h1c2-2 4-4 4-7v-1c1-1 1-1 1-2s1-2 2-2c-1 17-15 33-27 44-18 16-41 23-64 30-5 1-10 3-15 4h-3c-1 0-1 1-2 0-1 1-1 1-2 1h0v-1-1c2 0 4 0 6-2 1-1 7-2 8-2l27-8c21-7 41-18 54-36 4-5 7-11 10-17z" class="U"></path><path d="M589 340v-1c2-2 3-4 6-6-1 5-2 8-5 12-1 2-3 5-4 7l-5 7c-14 16-37 26-59 28h-1l-1-2h-2c5-1 11-1 16-3 2 0 4 0 5-1-18-4-44 0-61 10l-6 4c-4 3-7 7-10 11 0 1-1 2-1 3v-2l1-1c1-3 4-6 4-9 17-16 43-17 64-23 19-6 36-15 51-28v2l8-8z" class="L"></path><path d="M566 366l-1 1c-6 6-15 4-22 4l10-5 1 2c2 0 5 0 7-1 1 0 3-1 5-1z" class="M"></path><path d="M537 373c6 0 12 1 18 1-3 2-6 4-9 5h-8l-14-1v-1h2c3 0 6-1 10-1l-4-1c2-1 4-1 5-2z" class="I"></path><path d="M589 340c0 5-12 17-16 20l-7 6c-2 0-4 1-5 1-2 1-5 1-7 1l-1-2c10-5 20-11 28-18l8-8z" class="E"></path><path d="M357 271c6-9 14-17 24-21 12-5 23-4 35 1l9 5c2 1 3 2 4 3h0v1c-4-2-12-7-15-6 0 1-1 1-1 2 0 2-1 3-1 4-2 5-4 11-4 16-1 5-2 11-2 17-1 4-1 8 0 12 1 5 4 9 5 14 2 3 2 7 4 10 0 2 3 5 2 7 1 3 3 5 5 6l1 1 3 3c2 1 4 4 4 5 1 2 2 2 3 3h1c-1-1-1-1-1-2 2 2 3 3 5 4 1 1 1 2 3 2 0 1 1 1 2 2l2 1c1 1 2 1 3 1l3 2c1 1 1 1 2 1h0c9 4 19 8 28 10h-5c-1 0-2 0-3 1l7 2-1 2c1 0 2 0 3 1-1 0-3 1-4 2l-7 3c-1-1-2-1-4-1l-2-1-11-3c-4-2-8-3-12-4-8-3-15-8-22-13l-9-8c-7-5-11-12-16-18l-6-9c0-1-1-2-2-3l-1-1c1 0 1-1 1-2h0l1-1-1-2c1-2 0-4 0-6 0-1-2-4-3-5l-7-14-4-8-2-2c-1 0-3-1-5-1-5 0-9-1-13 0v-1l-1-2v-1c2-3 3-6 5-9z" class="H"></path><path d="M394 248h2c1 1 1 2 2 3 1 2 1 5 1 7l-1 1c-1-1-3-1-5 0-1-4 0-8 1-11z" class="S"></path><path d="M393 259c2-1 4-1 5 0v17c-1-1-1-2-2-3 0-2 0-2-1-3-1 1-2 3-3 5l1-16z" class="B"></path><path d="M409 257h1c-2 8-4 15-5 23-1 6 0 12-2 17v-2h0c-1-3-1-7-2-10-1-8-3-17 2-24 1-2 3-3 6-4z" class="C"></path><path d="M389 248h5c-1 3-2 7-1 11l-1 16 2 16c-2 0-2-3-3-5-1-3 0-6-1-9h0c-1-1-1-2-2-3 0-1-1-1-1-2v-2h0l-3-13c0-2-1-5 0-7l5-2z" class="J"></path><path d="M384 250l5-2 1 13c0 4 1 7 0 11-1-5 0-10-1-15 0-1-1-1-2-2-1 1-2 1-3 2 0-2-1-5 0-7z" class="H"></path><defs><linearGradient id="U" x1="385.986" y1="256.502" x2="388.117" y2="274.107" xlink:href="#B"><stop offset="0" stop-color="#828080"></stop><stop offset="1" stop-color="#abaaa8"></stop></linearGradient></defs><path fill="url(#U)" d="M384 257c1-1 2-1 3-2 1 1 2 1 2 2 1 5 0 10 1 15v5h0c-1-1-1-2-2-3 0-1-1-1-1-2v-2h0l-3-13z"></path><path d="M392 275c1-2 2-4 3-5 1 1 1 1 1 3 1 1 1 2 2 3 3 21 8 42 19 60 1 3 3 5 5 6l1 1 3 3c2 1 4 4 4 5-6-5-13-10-18-17l-1 1-9-13c-6-10-10-20-12-32 1-1 1-3 1-4 1 2 1 5 3 5l-2-16z" class="F"></path><path d="M391 286c1 2 1 5 3 5 1 6 2 12 4 18 3 8 8 18 14 25h0l-1 1-9-13c-6-10-10-20-12-32 1-1 1-3 1-4z" class="U"></path><path d="M357 271c6-9 14-17 24-21 12-5 23-4 35 1l9 5c-2 1-5-2-7-3s-4-1-5-2c-6-2-11-3-17-3h-2-5l-5 2c-1 2 0 5 0 7l3 13h0v2c0 1 1 1 1 2 1 1 1 2 2 3h0c1 3 0 6 1 9 0 1 0 3-1 4 2 12 6 22 12 32-2-1-3-3-4-6h-1v2l-2-2-3-4v5c1 2 5 7 5 8s0 2 1 3c1 3 4 7 4 10l-2-3c-2-1-3-2-5-3 0 0-1-2-2-2l-5-8-1-2c1-2 0-4 0-6 0-1-2-4-3-5l-7-14-4-8-2-2c-1 0-3-1-5-1-5 0-9-1-13 0v-1l-1-2v-1c2-3 3-6 5-9z" class="L"></path><path d="M378 262l-1-8 2-2c2 1 4 9 5 11 0 1 1 3 1 4-1-1-2-1-2-2l-1-3h-4z" class="G"></path><path d="M388 311c-1-6-4-11-7-17-3-10-6-22-9-33l2-2v2l7 23c3 10 7 20 11 28v5c-1-1-2-3-3-4 0-1 0-1-1-2z" class="E"></path><path d="M378 262h4l1 3c0 1 1 1 2 2v3h2 0v2c0 1 1 1 1 2 1 1 1 2 2 3h0c1 3 0 6 1 9 0 1 0 3-1 4 2 12 6 22 12 32-2-1-3-3-4-6h-1v2l-2-2-3-4c-4-8-8-18-11-28l2-2-5-20z" class="I"></path><path d="M387 270h0v2c0 1 1 1 1 2 1 1 1 2 2 3h0c1 3 0 6 1 9 0 1 0 3-1 4-3-7-4-13-5-20h2z" class="X"></path><path d="M383 282l6 18c2 5 5 10 6 16l-3-4c-4-8-8-18-11-28l2-2z" class="K"></path><path d="M357 271c6-9 14-17 24-21 12-5 23-4 35 1l9 5c-2 1-5-2-7-3s-4-1-5-2c-6-2-11-3-17-3h-2-5l-5 2c-6 2-11 5-16 9 3 3 3 6 3 9l-2 2 9 22c1 2 2 5 3 7 2 3 4 6 5 10 0 1 1 2 1 2l1 1v-1c1 1 1 1 1 2 1 1 2 3 3 4 1 2 5 7 5 8s0 2 1 3c1 3 4 7 4 10l-2-3c-2-1-3-2-5-3 0 0-1-2-2-2l-5-8-1-2c1-2 0-4 0-6 0-1-2-4-3-5l-7-14-4-8-2-2c-1 0-3-1-5-1-5 0-9-1-13 0v-1l-1-2v-1c2-3 3-6 5-9z" class="M"></path><path d="M360 269l5-6 2 7-2-1c-2-1-3 0-5 0z" class="H"></path><path d="M360 269c2 0 3-1 5 0l2 1 1 2h-1c-3 1-6 0-8 0l-1-1 2-2z" class="J"></path><path d="M368 259c3 3 3 6 3 9l-2 2c-1-2-2-5-3-7v-1l2-3z" class="O"></path><path d="M387 314c5 6 9 14 13 21-2-1-3-2-5-3 0 0-1-2-2-2l-5-8-1-2c1-2 0-4 0-6z" class="U"></path><defs><linearGradient id="V" x1="360.166" y1="273.615" x2="363.131" y2="285.899" xlink:href="#B"><stop offset="0" stop-color="#747272"></stop><stop offset="1" stop-color="#969392"></stop></linearGradient></defs><path fill="url(#V)" d="M357 271h1l1 1c2 0 5 1 8 0h1c0 1 1 2 1 3l4 12-2-2c-1 0-3-1-5-1-5 0-9-1-13 0v-1l-1-2v-1c2-3 3-6 5-9z"></path><path d="M392 317v-5l3 4 2 2v-2h1c1 3 2 5 4 6l9 13 1-1c5 7 12 12 18 17 1 2 2 2 3 3h1c-1-1-1-1-1-2 2 2 3 3 5 4 1 1 1 2 3 2 0 1 1 1 2 2l2 1c1 1 2 1 3 1l3 2c1 1 1 1 2 1h0c9 4 19 8 28 10h-5c-1 0-2 0-3 1l7 2-1 2c1 0 2 0 3 1-1 0-3 1-4 2l-7 3c-1-1-2-1-4-1l-2-1-11-3c-4-2-8-3-12-4-8-3-15-8-22-13l-9-8c-7-5-11-12-16-18l-6-9c0-1-1-2-2-3l-1-1c1 0 1-1 1-2h0l1-1 5 8c1 0 2 2 2 2 2 1 3 2 5 3l2 3c0-3-3-7-4-10-1-1-1-2-1-3s-4-6-5-8z" class="F"></path><path d="M405 341c3 1 3 3 6 5l8 7v1c-2-1-4-3-6-3-1-1-4-5-5-5l-3-5z" class="M"></path><path d="M397 325c4 4 6 9 9 13 1 3 4 5 5 8-3-2-3-4-6-5-1 0-2-2-3-3 0-3-3-7-4-10-1-1-1-2-1-3z" class="V"></path><path d="M413 351c2 0 4 2 6 3v-1c6 4 10 8 15 12-1 0-2 0-3-1l-4-2c-5-2-10-7-14-11z" class="N"></path><defs><linearGradient id="W" x1="451.912" y1="371.395" x2="451.587" y2="379.126" xlink:href="#B"><stop offset="0" stop-color="#a7a4a7"></stop><stop offset="1" stop-color="#bfc0bb"></stop></linearGradient></defs><path fill="url(#W)" d="M427 362l4 2c1 1 2 1 3 1 4 3 8 5 12 7l16 7c3 1 6 2 10 3 2 0 4-2 7-2 1 0 2 0 3 1-1 0-3 1-4 2l-7 3c-1-1-2-1-4-1-2-3-8-4-12-5 0 0-1-1-2-1 0-2-3-2-5-3-6-3-12-7-18-10-2-1-2-2-3-4z"></path><path d="M455 380c5-1 12 5 17 2 2 0 4-2 7-2 1 0 2 0 3 1-1 0-3 1-4 2l-7 3c-1-1-2-1-4-1-2-3-8-4-12-5z" class="H"></path><defs><linearGradient id="X" x1="460.598" y1="373.444" x2="465.902" y2="367.556" xlink:href="#B"><stop offset="0" stop-color="#424340"></stop><stop offset="1" stop-color="#5e5b5d"></stop></linearGradient></defs><path fill="url(#X)" d="M412 334c5 7 12 12 18 17 1 2 2 2 3 3h1c-1-1-1-1-1-2 2 2 3 3 5 4 1 1 1 2 3 2 0 1 1 1 2 2l2 1c1 1 2 1 3 1l3 2c1 1 1 1 2 1h0c9 4 19 8 28 10h-5c-1 0-2 0-3 1h0c-10-3-20-7-29-12-13-8-23-18-33-29l1-1z"></path><path d="M389 329c0-1-1-2-2-3l-1-1c1 0 1-1 1-2h0l1-1 5 8c1 0 2 2 2 2 2 1 3 2 5 3l2 3c1 1 2 3 3 3l3 5c1 0 4 4 5 5 4 4 9 9 14 11 1 2 1 3 3 4 6 3 12 7 18 10 2 1 5 1 5 3 1 0 2 1 2 1 4 1 10 2 12 5l-2-1-11-3c-4-2-8-3-12-4-8-3-15-8-22-13l-9-8c-7-5-11-12-16-18l-6-9z" class="E"></path><path d="M395 332c2 1 3 2 5 3l2 3c1 1 2 3 3 3l3 5v4c-5-5-9-12-13-18z" class="L"></path><path d="M408 346c1 0 4 4 5 5 4 4 9 9 14 11 1 2 1 3 3 4 6 3 12 7 18 10 2 1 5 1 5 3-17-7-32-16-45-29v-4z" class="H"></path><path d="M342 297c2-2 3-5 3-8h1v-2c1-2 2-5 3-7 1 1 0 2-1 4-1 4-4 9-4 13h1 0v-1-1c1-1 0-1 1-1v-1-1c1-1 1-1 1-2h0l1-1v-1l1-1v-1-1c0-1 0-1 1-2h0v-1-1c1 0 1 0 1-1l1-1v1 1l1 2v1c4-1 8 0 13 0 2 0 4 1 5 1l2 2 4 8 7 14c1 1 3 4 3 5 0 2 1 4 0 6l1 2-1 1h0c0 1 0 2-1 2l1 1c1 1 2 2 2 3l6 9c5 6 9 13 16 18l9 8c7 5 14 10 22 13 4 1 8 2 12 4l-3 1v1h-2c-5-2-8-1-12 1-15 5-27 13-39 23l-2 2-1-1-3 4c-1-1-2-4-3-6l-5-13c-1 0-2-1-3-1l-39-95z" class="T"></path><path d="M365 311l1-1c1 2 2 4 3 7l-5-2v-1l1-1v-2z" class="B"></path><path d="M352 281l1 2v1l-3 9h-1-1v-1h0c0-3 2-8 4-11z" class="Q"></path><path d="M361 305h1c0 2 0 2 1 3h2l1 2-1 1v2l-1 1v1h-12l-1-2c-1-1-1-3-2-4h0c4-3 8-3 12-4z" class="M"></path><path d="M364 314l-1-1c-1-1-4-3-4-4 2 0 4 1 6 2v2l-1 1z" class="F"></path><path d="M349 309h0c4-3 8-3 12-4v2c-1 0-1 1-2 1-3 0-5 1-8 1v4h0c-1-1-1-3-2-4z" class="D"></path><path d="M377 377l-11-28c6 5 14 6 22 7 4 1 9 3 14 3h1c-2 1-4 1-4 3-2-1-3-2-4-1-9 2-13 9-18 16z" class="M"></path><path d="M403 359h0c2 1 4 0 5 2l-1 1 1 1 2 1v2h-1l-2 1c-1 1-2 3-3 4l-3 3v1c-1 1-2 3-3 4 1 1 1 2 2 3-1 0-1 0-2 1h3 0 1c-3 1-6 2-9 1v1h-2l-5 4c-1 0-2 1-3 1-1-2-2-3-2-5h0c-1-1-1-3-2-4 0-1 0-1-1-2l-1-2c5-7 9-14 18-16 1-1 2 0 4 1 0-2 2-2 4-3z" class="I"></path><path d="M407 367c-1-1-3-1-4-2 1-1 3-2 4-3l1 1 2 1v2h-1l-2 1zm-7 0h0c1 1 1 1 1 3-2 1-4 3-6 4v1c-1 0-1 0-1 1-3 2-6 5-9 8h-2-2l4-4c2-1 4-4 6-5 2-2 3-4 5-5l1-1 3-2zm-14 22v-2h0l3-3s1 0 1-1c1-2 3-4 5-6 1 0 1 0 2-1 1 0 2-1 4-1-1 1-2 3-3 4 1 1 1 2 2 3-1 0-1 0-2 1h3 0 1c-3 1-6 2-9 1v1h-2l-5 4z" class="E"></path><path d="M377 377c5-7 9-14 18-16 1-1 2 0 4 1h-1c-3 2-6 3-9 4l-4 4v1h1l-2 1 1 1v1c-1 0-1 0-2 1-1 2-3 3-5 4l-1-2z" class="H"></path><path d="M353 284c4-1 8 0 13 0 2 0 4 1 5 1l2 2 4 8 7 14c1 1 3 4 3 5 0 2 1 4 0 6l-2-4c-1 0-2 1-3 1v2c1 1 3 2 3 3l1 1h0l-1 1-1-1c-2-1-5-1-7-2-1 0-1 0-2-1 0-1-3-2-5-3l-5-10v1h-2c-1-1-1-1-1-3h-1c-4 1-8 1-12 4h0l-2-6-1-2c-1-4 0-6 2-9h0v1h1 1l3-9z" class="O"></path><path d="M370 297c3 1 5 1 7 4v1l-1 2c-1 0-1-1-2-1h-2l-1-1v-1l1-2h0c-1 0-2-1-2-2z" class="B"></path><path d="M371 302v-1l1-2 3 3-1 1h-2l-1-1z" class="F"></path><path d="M377 302l8 14c-1 0-2 1-3 1v2h0l-1-3c-1-1-1-2-2-3v-1h-1l-1-3c-1-1-1-2-2-3l-3-3h2c1 0 1 1 2 1l1-2z" class="V"></path><defs><linearGradient id="Y" x1="356.073" y1="306.405" x2="362.115" y2="295.334" xlink:href="#B"><stop offset="0" stop-color="#a8a4a3"></stop><stop offset="1" stop-color="#cfcece"></stop></linearGradient></defs><path fill="url(#Y)" d="M346 301h2c6-5 14-4 22-4 0 1 1 2 2 2h0l-1 2v1c-4-1-9-1-13 0-3 0-5 0-8 1h-3l-1-2h0z"></path><path d="M347 303h3c3-1 5-1 8-1 4-1 9-1 13 0l1 1 3 3c1 1 1 2 2 3l1 3h1v1c1 1 1 2 2 3l1 3h0c1 1 3 2 3 3l1 1h0l-1 1-1-1c-2-1-5-1-7-2-1 0-1 0-2-1 0-1-3-2-5-3l-5-10v1h-2c-1-1-1-1-1-3h-1c-4 1-8 1-12 4h0l-2-6z" class="Q"></path><path d="M373 314l-1-1c0-2-1-2-2-3-1-3-2-5-4-6l1-1 1 1c1 2 5 4 7 4l1 3 2 3h-5 0z" class="N"></path><path d="M362 305c1 0 2-1 3-1 1 1 2 1 2 3v1c2 2 4 5 6 6h0 5c1 1 2 2 2 5h2 0c1 1 3 2 3 3l1 1h0l-1 1-1-1c-2-1-5-1-7-2-1 0-1 0-2-1 0-1-3-2-5-3l-5-10v1h-2c-1-1-1-1-1-3z" class="W"></path><path d="M378 314c1 1 2 2 2 5h0c-2-1-3-2-4-3-1 0-1 0-1-1-1 0-1-1-2-1h5z" class="B"></path><path d="M353 284c4-1 8 0 13 0 2 0 4 1 5 1l2 2 4 8c-1 1-1 1-1 2h-2l-1-1c-9-2-17 0-26 3l-1 2h0c-1-4 0-6 2-9h0v1h1 1l3-9z" class="M"></path><path d="M366 284c2 0 4 1 5 1 0 1 0 2-1 3-2-1-4-2-6-2l1-1 1-1z" class="J"></path><path d="M371 285l2 2 4 8c-1 1-1 1-1 2h-2l1-1-5-8c1-1 1-2 1-3z" class="L"></path><path d="M353 284c4-1 8 0 13 0l-1 1-1 1h-9c-1 2-1 4-1 6h1c-1 1-3 2-5 2v-1l3-9z" class="T"></path><path d="M355 292c1-1 2-1 3-2h1 5 2v1c-1 1 0 1-1 1h-2c-2 0-4 0-5 1h-1c-1 1-1 1-2 1l-1 1c-1 0-2 1-4 1 0 0-2 1-3 2v1l-1 2h0c-1-4 0-6 2-9h0v1h1 1v1c2 0 4-1 5-2z" class="D"></path><path d="M347 299v-1c1-1 3-2 3-2 2 0 3-1 4-1l1-1c1 0 1 0 2-1h1c1-1 3-1 5-1 3 1 8 0 10 3v1c-9-2-17 0-26 3z" class="P"></path><path d="M354 316c4 0 8 0 11 1h1c3 1 6 3 9 3 1 1 1 1 2 1 2 1 5 1 7 2l1 1 1-1h0l-1-1c0-1-2-2-3-3v-2c1 0 2-1 3-1l2 4 1 2-1 1h0c0 1 0 2-1 2l1 1c1 1 2 2 2 3l6 9c5 6 9 13 16 18l-2 1c-2 1-4-2-6-3h-3l-14-2c0-3 0-4 2-6-1-1-3-2-4-3-1 0-2-1-3-1v-1h-1c-1 2-2 2-4 1 1-1 2-1 4-2h0v-1c-2 1-4 2-6 4h0l1-3c-3 3-7 4-11 4l-8-19c-1-2-3-5-3-8l1-1z" class="W"></path><path d="M396 347h1l2 2-1 1-1 1h-1v-4z" class="I"></path><path d="M388 337c1 0 2 1 3 2 0 2 0 2-1 4h-2v-1c0-1 1-2 1-3l-1-1v-1z" class="B"></path><path d="M388 346c1 1 1 2 2 3h1c1 1 2 2 3 2 2 1 4 1 6 3l-14-2c0-3 0-4 2-6z" class="E"></path><defs><linearGradient id="Z" x1="380.214" y1="330.792" x2="387.98" y2="327.117" xlink:href="#B"><stop offset="0" stop-color="#2e302d"></stop><stop offset="1" stop-color="#514f4f"></stop></linearGradient></defs><path fill="url(#Z)" d="M354 316c4 0 8 0 11 1h1c3 1 6 3 9 3 1 1 1 1 2 1 2 1 5 1 7 2l1 1 1-1h0l-1-1c0-1-2-2-3-3v-2c1 0 2-1 3-1l2 4 1 2-1 1h0c0 1 0 2-1 2l1 1c1 1 2 2 2 3l6 9c5 6 9 13 16 18l-2 1c0-1 0-1-1-1-1-1-2-1-3-2h0c-1-1-2-3-3-4-1-2-2-3-3-4s-1-2-2-3c-4-5-8-10-13-14-3-2-6-4-9-5-5-1-9-2-14-4-2 0-5-1-6-2l-1-2z"></path><path d="M354 316c4 0 8 0 11 1h1c3 1 6 3 9 3 1 1 1 1 2 1 2 1 5 1 7 2l1 1 1-1h0l-1-1c0-1-2-2-3-3v-2c1 0 2-1 3-1l2 4 1 2-1 1h0c0 1 0 2-1 2l1 1c1 1 2 2 2 3-6-5-12-6-20-8l-14-3-1-2zm22 26c2 1 3 1 4-1h1v1c1 0 2 1 3 1 1 1 3 2 4 3-2 2-2 3-2 6l14 2h3c2 1 4 4 6 3l2-1 9 8c7 5 14 10 22 13 4 1 8 2 12 4l-3 1v1h-2c-5-2-8-1-12 1-15 5-27 13-39 23l-2 2-1-1-3 4c-1-1-2-4-3-6l-5-13v-2h1c2-2 5-4 8-5 3-2 7 0 10-2l3-3c-1 1-2 1-3 2h-1-1 0-3c1-1 1-1 2-1-1-1-1-2-2-3 1-1 2-3 3-4v-1l3-3c1-1 2-3 3-4l2-1h1v-2l-2-1-1-1 1-1c-1-2-3-1-5-2h0c-2-2-11-3-14-4l-16-3c-3-1-6-2-8-4v-2-1c3 0 9 0 11-2v-1z" class="C"></path><path d="M384 393v-2h1c1 1 3 1 3 2v1h-1c0 1 0 1 1 2 2 2 2 5 3 8 0 2 0 4 1 6 1-1 2-2 2-3 1-1 1-1 2-1h0l-1 2-3 4c-1-1-2-4-3-6l-5-13z" class="D"></path><path d="M376 342c2 1 3 1 4-1h1v1c1 0 2 1 3 1 1 1 3 2 4 3-2 2-2 3-2 6l14 2h3c2 1 4 4 6 3l2-1 9 8c7 5 14 10 22 13 4 1 8 2 12 4l-3 1v1h-2c-5-2-8-1-12 1-15 5-27 13-39 23l-2 2-1-1 1-2c13-12 28-21 45-26-8-4-15-8-22-13-1 4-2 7-4 10h-1c2-2 3-4 3-7 0 0 0-1 1-2h0l-2-2h-1-1v-3-1c-2-1-3-3-4-4l-1 1c-4-2-8-4-11-5-3 0-5 0-8-1l-17-3c-2-1-5-3-8-2v-2-1c3 0 9 0 11-2v-1z" class="H"></path><path d="M376 342c2 1 3 1 4-1h1v1c1 0 2 1 3 1 1 1 3 2 4 3-2 2-2 3-2 6-3-1-6-2-9-2-2-1-3-1-4-1-3 0-5-2-8-3v-1c3 0 9 0 11-2v-1z" class="C"></path><path d="M382 344h3v4c-1 0-1 0-3-1-1 0-1-1-2-1l2-2z" class="I"></path><path d="M376 342c2 1 3 1 4-1h1v1l1 2-2 2-2-2-2-1v-1z" class="E"></path><defs><linearGradient id="a" x1="446.653" y1="753.846" x2="387.055" y2="754.823" xlink:href="#B"><stop offset="0" stop-color="#666463"></stop><stop offset="1" stop-color="#8f8c8a"></stop></linearGradient></defs><path fill="url(#a)" d="M388 602h1v2h0c2 2 1 5 1 8 1 1 1 2 1 4 1 5 1 11 1 17 1 2 2 3 3 5v1c3 7 7 14 9 22 0-2 1-4 1-6-1-1-1-1-1-2l-1-3-1-4c0-3-1-10 1-12 1-1 1-2 1-4 2-2 3-6 4-9h1l1 2s1 1 1 2l9 22 1 2 2 4v2c1 2 2 3 2 5l1 2c1 5-4 10-6 15 0 1-1 2-1 3-4 13-11 25-11 39 0 3 1 6 1 9 0 2 1 5 1 7l1 1c1 1 2 4 3 5 5 10 12 15 23 17 0-1-1-1-1-2s0-1 1-1l6 2c3 2 6 3 10 3h4l-1 1c3 2 3 0 5 1-3 1-8 1-11 1 4 1 7 1 10 3 0 1 0 1-1 1l-3 1h-2c-1 1-3 1-4 1h0c2 1 3 2 5 2h0c-2 1-4 2-6 1-1 0-2 0-3 1h4 4 1 0 1c2 0 5-2 7-3l3-1h1l-2 1 1 1-6 4v1 1c-2 1-5 2-7 4v1c-1 2-2 3-3 5h-1v-1c-3-3-4 0-7 1h-2c-2 0-2 1-3 2h-4-1c-2 1-4 2-6 2l-1 1c-1-2-1-2-2-2h-1c-3-2-7-2-10-4-1 0-4-1-5-2l-10-8c-4-4-9-10-11-16 0-3 0-6-1-9-1 2-2 3-3 3 2-3 2-7 3-10s0-21 0-25c1-5 1-9 2-14s3-10 5-14c-2-8-6-15-9-22v-1l6 10 4 12c2-3 5-8 5-12-1-11-6-20-10-30v-1l-3-14c-2-5-1-13-1-19l1-1v-1c0-3 2-6 3-9v-1z"></path><path d="M412 786h0c0-1 0-1-1-1h0 0 3 1c4 1 8 3 12 1l2 2c-2 1-5 1-7 2-3-2-7-2-10-4zm-6-115v-1-1l1-1c1 5 2 13 0 18l-3 6c-2 3-4 6-5 9-2 4-4 10-5 15 0 5 1 11 2 16 4 14 16 27 28 34 6 3 15 6 22 7h4v2c-15-1-29-7-39-18-7-7-15-18-18-28-1-4-1-8-1-12h0l2-8c1-3 2-6 4-9 3-5 6-9 7-14 2-5 1-10 1-15z" class="J"></path><path d="M463 770l3-1h1l-2 1 1 1-6 4v1 1c-2 1-5 2-7 4v1c-1 2-2 3-3 5h-1v-1c-3-3-4 0-7 1h-2c-2 0-2 1-3 2h-4-1c-2 1-4 2-6 2l-1 1c-1-2-1-2-2-2h-1c2-1 5-1 7-2l-2-2 9-3c3-1 5-3 8-4s7-2 9-4h-2-1v-2h4 1 0 1c2 0 5-2 7-3z" class="C"></path><path d="M440 787l20-12v1 1c-2 1-5 2-7 4v1c-1 2-2 3-3 5h-1v-1c-3-3-4 0-7 1h-2z" class="P"></path><path d="M463 770l3-1h1l-2 1-36 18-2-2 9-3c3-1 5-3 8-4s7-2 9-4h-2-1v-2h4 1 0 1c2 0 5-2 7-3zm-74-92l4 12c2-3 5-8 5-12-1-11-6-20-10-30v-1c3 7 6 13 9 20 1 4 1 7 2 11 0 7-6 13-9 19h3 0l2 1-1 1c-1 3-2 7-1 10h1l-2 8h0 0c-1 2-1 5-1 7h0-1-2l-1 1c0 8-1 15-1 22 1 10 4 20 11 27 5 5 10 8 16 11h2-1-3 0 0c1 0 1 0 1 1h0c-1 0-4-1-5-2l-10-8c-4-4-9-10-11-16 0-3 0-6-1-9-1 2-2 3-3 3 2-3 2-7 3-10s0-21 0-25c1-5 1-9 2-14s3-10 5-14c-2-8-6-15-9-22v-1l6 10z" class="K"></path><defs><linearGradient id="b" x1="396.002" y1="717.739" x2="383.998" y2="701.761" xlink:href="#B"><stop offset="0" stop-color="#a6a09e"></stop><stop offset="1" stop-color="#ced0d0"></stop></linearGradient></defs><path fill="url(#b)" d="M390 697h3 0l2 1-1 1c-1 3-2 7-1 10h1l-2 8h0 0c-1 2-1 5-1 7h0-1-2l-1 1c0-10 0-18 3-28z"></path><path d="M393 697l2 1-1 1c-1 3-2 7-1 10h1l-2 8h-1c0-3-1-7 0-9l2-11z" class="D"></path><path d="M388 602h1v2h0c2 2 1 5 1 8 1 1 1 2 1 4 1 5 1 11 1 17 1 2 2 3 3 5v1c3 7 7 14 9 22l2 10c0 5 1 10-1 15-1 5-4 9-7 14-2 3-3 6-4 9h-1c-1-3 0-7 1-10l1-1-2-1h0-3c3-6 9-12 9-19-1-4-1-7-2-11-3-7-6-13-9-20l-3-14c-2-5-1-13-1-19l1-1v-1c0-3 2-6 3-9v-1z" class="B"></path><path d="M392 633c1 2 2 3 3 5v1c3 7 7 14 9 22l2 10c0 5 1 10-1 15-1 5-4 9-7 14-2 3-3 6-4 9h-1c-1-3 0-7 1-10l1-1-2-1h0-3c3-6 9-12 9-19 1 2 2 4 1 6-2 3-2 5-3 9 0 1-1 3-2 5 0 3-1 6-1 9 0-1 0-2 1-3s1-4 1-5c2-3 4-6 5-9 0-2 1-3 2-4v-1c2-5 2-12 0-17-1-2-1-3-1-4v-2c0-1 0-2-1-3 0-2-1-4-2-5-3-6-6-14-7-21z" class="W"></path><path d="M399 701c1 1 1 1 1 2-1 5-1 15 1 20h1v1-2l1-1v1l2 5v-1 1h1 1v-1l3 12h0v-3l1 1c1 1 2 4 3 5 5 10 12 15 23 17 0-1-1-1-1-2s0-1 1-1l6 2c3 2 6 3 10 3h4l-1 1c3 2 3 0 5 1-3 1-8 1-11 1 4 1 7 1 10 3 0 1 0 1-1 1l-3 1h-2c-1 1-3 1-4 1h0c2 1 3 2 5 2h0c-2 1-4 2-6 1-1 0-2 0-3 1-7-1-16-4-22-7-12-7-24-20-28-34-1-5-2-11-2-16 1-5 3-11 5-15z" class="F"></path><defs><linearGradient id="c" x1="408.911" y1="733.662" x2="403.109" y2="727.36" xlink:href="#B"><stop offset="0" stop-color="#484847"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#c)" d="M401 723h1v1-2l1-1v1l2 5v-1 1h1 1v-1l3 12h0l2 3c0 1 1 3 2 4v1c-6-6-11-15-13-23z"></path><defs><linearGradient id="d" x1="455.521" y1="759.674" x2="430.471" y2="759.27" xlink:href="#B"><stop offset="0" stop-color="#0d0c0c"></stop><stop offset="1" stop-color="#2c2a2b"></stop></linearGradient></defs><path fill="url(#d)" d="M410 735l1 1c1 1 2 4 3 5 5 10 12 15 23 17 0-1-1-1-1-2s0-1 1-1l6 2c3 2 6 3 10 3h4l-1 1c3 2 3 0 5 1-3 1-8 1-11 1h-3c-13-1-25-6-33-17v-1c-1-1-2-3-2-4l-2-3v-3z"></path><path d="M396 732l1 1c1 1 1 1 1 2 7 14 20 25 34 30 7 3 15 4 22 3-1 1-3 1-4 1h0c2 1 3 2 5 2h0c-2 1-4 2-6 1-1 0-2 0-3 1-7-1-16-4-22-7-12-7-24-20-28-34z" class="X"></path><path d="M404 630c2-2 3-6 4-9h1l1 2s1 1 1 2l9 22 1 2 2 4v2c1 2 2 3 2 5l1 2c1 5-4 10-6 15 0 1-1 2-1 3-4 13-11 25-11 39 0 3 1 6 1 9 0 2 1 5 1 7v3h0l-3-12v1h-1-1v-1 1l-2-5v-1l-1 1v2-1h-1c-2-5-2-15-1-20 0-1 0-1-1-2 1-3 3-6 5-9l3-6c2-5 1-13 0-18l-1 1v1 1l-2-10c0-2 1-4 1-6-1-1-1-1-1-2l-1-3-1-4c0-3-1-10 1-12 1-1 1-2 1-4z"></path><defs><linearGradient id="e" x1="408.987" y1="622.292" x2="405.511" y2="634.06" xlink:href="#B"><stop offset="0" stop-color="#868283"></stop><stop offset="1" stop-color="#9b9a99"></stop></linearGradient></defs><path fill="url(#e)" d="M404 630c2-2 3-6 4-9h1l1 2c0 3-1 6-2 9 0 5 0 9 1 14-4-4-2-7-3-11-1-1-1 0-1-1h0l-2 1v-1c1-1 1-2 1-4z"></path><path d="M412 692h2c-3 6-5 12-7 18-1 5 0 11 0 16v1h-1-1v-1 1l-2-5v-1c-1-5 0-10 2-15 0-1 0-3 1-4l1-2 1-1 1 1c1-3 2-6 3-8z" class="G"></path><path d="M403 721c-1-5 0-10 2-15 0-1 0-3 1-4l1-2 1-1 1 1c-2 5-5 13-4 19 0 3 1 5 1 8h-1v-1 1l-2-5v-1z" class="S"></path><path d="M411 625l9 22 1 2 2 4v2c1 2 2 3 2 5 0 5-2 10-5 14 0-4-1-8-3-11-3-8-7-16-7-24 0-4 0-9 1-14z" class="H"></path><path d="M403 650l-1-4c0-3-1-10 1-12v1l2-1h0c0 1 0 0 1 1 1 4-1 7 3 11 3 9 10 20 9 30 0 6-2 11-4 16h-2c-1 2-2 5-3 8l-1-1-1 1-1 2c-1 1-1 3-1 4-2 5-3 10-2 15l-1 1v2-1h-1c-2-5-2-15-1-20 0-1 0-1-1-2 1-3 3-6 5-9l3-6c2-5 1-13 0-18l-1 1v1 1l-2-10c0-2 1-4 1-6-1-1-1-1-1-2l-1-3z" class="D"></path><path d="M403 650l-1-4c0-3-1-10 1-12v1c0 4 0 9 1 13 2 9 8 17 10 27 0 5 0 11-2 17-1 2-2 5-3 8l-1-1 1-4c3-7 4-14 3-21h-1c0-4-1-7-2-10l-2-5c-1 3 1 6 0 9h0l-1 1v1 1l-2-10c0-2 1-4 1-6-1-1-1-1-1-2l-1-3z" class="U"></path><path d="M407 668h0c1-3-1-6 0-9l2 5c1 3 2 6 2 10h1c1 7 0 14-3 21l-1 4-1 1-1 2c-1 1-1 3-1 4-2 5-3 10-2 15l-1 1v2-1h-1c-2-5-2-15-1-20 0-1 0-1-1-2 1-3 3-6 5-9l3-6c2-5 1-13 0-18z" class="G"></path><path d="M407 690h1c0 2 0 3-1 5h-1c0-2 0-3 1-5z" class="X"></path><defs><linearGradient id="f" x1="606.397" y1="737.217" x2="632.389" y2="735.538" xlink:href="#B"><stop offset="0" stop-color="#8a8888"></stop><stop offset="1" stop-color="#b0afac"></stop></linearGradient></defs><path fill="url(#f)" d="M613 687l7-9 1 1h-1v2l1 1c1 3 3 6 4 9 1 1 2 1 4 1l-1-2 2 1v6c1 1 1 3 1 4v2c1 2 0 4 1 6 0 1 0 1 1 3v2c0 1 0 1 1 2v3 12 5l1 4v3c1 1 1 2 1 3v8h0c-1-1-1-2-2-3v-1l-1 2h0c-2 3-1 6-2 8-3 9-10 17-18 21-4 2-8 4-11 5h-1c-4 1-10-1-14-2-3-1-6-3-8-2-1 0-4-2-5-2l-7-4h1c2-1 3-1 4-1l-5-1c0-1 1-1 1-1-3-1-6-3-9-4-1 1-2 0-3-1l-2 1-1-1h-1l6-15c1-5 3-9 5-14 1-3 2-7 4-11 2-1 2-2 3-4l3-9c3-6 7-4 12-6 7-3 13-7 19-12v-1c4-2 6-6 9-9z"></path><path d="M625 691c1 1 2 1 4 1v4c1 1 0 2 1 3v4 2c0-1-1-1-1-2v-2c-1-1-1-2-1-3v-1c-1-1-1-2-2-3l-1-3z" class="B"></path><path d="M623 698c1 1 1 3 2 4l2 12c1 4 0 9-1 14h0c-2 9-9 18-14 25-8 9-18 16-30 20-2 1-7 2-10 2l-5-1c0-1 1-1 1-1h10c5-1 10-3 15-6 9-5 18-13 24-22v-1c7-10 10-20 9-32l-3-14z" class="U"></path><defs><linearGradient id="g" x1="571.758" y1="772.943" x2="611.135" y2="769.823" xlink:href="#B"><stop offset="0" stop-color="#676463"></stop><stop offset="1" stop-color="#82807e"></stop></linearGradient></defs><path fill="url(#g)" d="M612 753c-1 1-1 2-1 3l-2 11c-1 3-1 11 1 14h3c-4 2-8 4-11 5h-1c-4 1-10-1-14-2-3-1-6-3-8-2-1 0-4-2-5-2l-7-4h1c2-1 3-1 4-1 3 0 8-1 10-2 12-4 22-11 30-20z"></path><path d="M613 687l7-9 1 1h-1v2l1 1c1 3 3 6 4 9l1 3h-1l-1-2-1 2-1-2v2 1 1c1 1 1 1 1 2h0l3 14c1 12-2 22-9 32v1c-6 9-15 17-24 22-5 3-10 5-15 6h-10c-3-1-6-3-9-4-1 1-2 0-3-1l-2 1-1-1h-1l6-15v2c4 2 8 3 12 3 9 1 15-4 21-9h0v1c-1 1-1 3-2 5h0 1c1-1 0-1 1-1s2-1 3-1v-1c4-2 8-6 10-10 2-2 3-4 5-7s3-7 3-11c-1-1-1-5-1-6 0-5-2-19-4-22l-3 1h0v-1c4-2 6-6 9-9z" class="D"></path><path d="M553 768l2-5 6 2c-2 2-3 2-5 3h0l-2 1-1-1z" class="S"></path><path d="M612 724l1-1c1 11-7 17-11 25-3 3-6 6-10 8-9 6-20 9-31 8-2 0-4-1-5-2v-1c2-2 7 1 10-1-2 0-7-1-9-2 0-1 1-2 1-3 4 2 8 3 12 3 9 1 15-4 21-9h0v1c-1 1-1 3-2 5h0 1c1-1 0-1 1-1s2-1 3-1v-1c4-2 8-6 10-10 2-2 3-4 5-7s3-7 3-11z" class="O"></path><path d="M613 687l7-9 1 1h-1v2l1 1c1 3 3 6 4 9l1 3h-1l-1-2c0-1-1-2-1-3s-1-1-1-2l-2-3h-1c-1 2-1 4-2 6-1 3 0 9 0 13v6c0 6 0 10-2 16 0 8-5 17-11 22-1 0-1 1-2 1 4-8 12-14 11-25l-1 1c-1-1-1-5-1-6 0-5-2-19-4-22l-3 1h0v-1c4-2 6-6 9-9z" class="G"></path><path d="M615 725h-1c0-2 1-5 1-7 1-5 0-10 0-15-1-4-2-7-2-11 0-2 0-2 1-3h1c0 2 0 5 1 8 0 4 0 8 1 12 0 6 0 10-2 16z" class="H"></path><path d="M613 687v1c0 1-2 3-3 4 0 1-1 1 0 2l3 29-1 1c-1-1-1-5-1-6 0-5-2-19-4-22l-3 1h0v-1c4-2 6-6 9-9z" class="L"></path><defs><linearGradient id="h" x1="604.42" y1="696.709" x2="630.832" y2="729.595" xlink:href="#B"><stop offset="0" stop-color="#9a9797"></stop><stop offset="1" stop-color="#e7e7e6"></stop></linearGradient></defs><path fill="url(#h)" d="M617 703c0-4-1-10 0-13 1-2 1-4 2-6h1l2 3c0 1 1 1 1 2s1 2 1 3l-1 2-1-2v2 1 1c1 1 1 1 1 2h0l3 14c1 12-2 22-9 32v1l-1-1c0-1 0-1 1-2v-2c2-1 2-3 3-5-5 3-8 5-10 10v1c-1 1-2 2-3 2 1-3 3-5 5-8 0-1 1-3 2-4l1-2c0-2 1-3 1-5 1-3 2-6 2-9 2-5 1-13-1-17z"></path><path d="M607 748c1 0 2-1 3-2v-1c2-5 5-7 10-10-1 2-1 4-3 5v2c-1 1-1 1-1 2l1 1c-6 9-15 17-24 22-5 3-10 5-15 6h-10c-3-1-6-3-9-4-1 1-2 0-3-1h0c2-1 3-1 5-3 2 0 4 1 6 1s5 0 7-1c2 0 7 1 9 1 9-2 18-11 24-18z" class="E"></path><path d="M561 765c2 0 4 1 6 1s5 0 7-1c2 0 7 1 9 1l-15 4c4 1 8 1 12 0s9-3 13-3c-5 3-10 5-15 6h-10c-3-1-6-3-9-4-1 1-2 0-3-1h0c2-1 3-1 5-3z" class="Q"></path><path d="M561 765c2 0 4 1 6 1h2c-2 1-3 2-5 2-1 0-4-1-5 0v1c-1 1-2 0-3-1h0c2-1 3-1 5-3z" class="R"></path><defs><linearGradient id="i" x1="569.8" y1="724.645" x2="598.558" y2="735.942" xlink:href="#B"><stop offset="0" stop-color="#8b8a88"></stop><stop offset="1" stop-color="#b6b4b1"></stop></linearGradient></defs><path fill="url(#i)" d="M604 697l3-1c2 3 4 17 4 22 0 1 0 5 1 6 0 4-1 8-3 11s-3 5-5 7c-2 4-6 8-10 10v1c-1 0-2 1-3 1s0 0-1 1h-1 0c1-2 1-4 2-5v-1h0c-6 5-12 10-21 9-4 0-8-1-12-3v-2c1-5 3-9 5-14 1-3 2-7 4-11 2-1 2-2 3-4l3-9c3-6 7-4 12-6 7-3 13-7 19-12h0z"></path><path d="M604 697h0l-1 2v1c5 12 4 25-2 36 0 2-2 4-2 6-1 4-5 5-5 10v1c-1 0-2 1-3 1s0 0-1 1h-1 0c1-2 1-4 2-5v-1h0c3-4 6-8 8-12 6-12 6-23 2-36-6 5-13 8-21 12-2 1-4 2-6 2h-1c3-6 7-4 12-6 7-3 13-7 19-12z" class="K"></path><defs><linearGradient id="j" x1="594.698" y1="714.208" x2="613.181" y2="726.71" xlink:href="#B"><stop offset="0" stop-color="#bebdbd"></stop><stop offset="1" stop-color="#f5f4f3"></stop></linearGradient></defs><path fill="url(#j)" d="M604 697l3-1c2 3 4 17 4 22 0 1 0 5 1 6 0 4-1 8-3 11s-3 5-5 7c-2 4-6 8-10 10 0-5 4-6 5-10 0-2 2-4 2-6 6-11 7-24 2-36v-1l1-2z"></path><path d="M604 742v-1c0-1 1-3 1-4 1-1 2-2 4-2-2 3-3 5-5 7z" class="I"></path><defs><linearGradient id="k" x1="334.269" y1="409.026" x2="290.876" y2="431.853" xlink:href="#B"><stop offset="0" stop-color="#6d6b6b"></stop><stop offset="1" stop-color="#9a9897"></stop></linearGradient></defs><path fill="url(#k)" d="M202 253c7-5 18-5 26-3 12 2 20 10 30 18 3 2 5 4 7 7 3 3 6 7 8 11v1l130 314c0 2 1 3 0 4v1l1 1c1 2 2 5 3 7v1c1 2-1 6-3 8h0v4 3c0 2 0 3-1 4-2 2-1 9-1 12l1 4 1 3c0 1 0 1 1 2 0 2-1 4-1 6-2-8-6-15-9-22v-1c-1-2-2-3-3-5 0-6 0-12-1-17 0-2 0-3-1-4 0-3 1-6-1-8h0v-2h-1l-1 1v-1c1-4-2-11-3-15l-10-26-37-90-50-121-12-26c-3-8-7-16-12-23-9-15-26-28-43-32-3 0-6 0-9-1-2 0-5-1-6-3-3-2-6-6-6-10l3-2z"></path><defs><linearGradient id="l" x1="406.911" y1="623.094" x2="394.613" y2="625.312" xlink:href="#B"><stop offset="0" stop-color="#5c5b59"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#l)" d="M398 603c-1-1-1-3-1-5h-1c0-1 0-2-1-3-1-2 0-3-1-5h1l1 1c0-2-2-3-2-5-1-2-2-3-2-4s-1-2-1-3l-1-1c0-1-1-2-1-3l-1-1-1-3h1c1 1 1 1 1 2l1 2 2 4v1l1 3 7 16c0 1 1 2 1 3l2 3v1l1 1c1 2 2 5 3 7v1c1 2-1 6-3 8h0v4 3c0 2 0 3-1 4-2 2-1 9-1 12l1 4 1 3c0 1 0 1 1 2 0 2-1 4-1 6-2-8-6-15-9-22-2-12 0-23 2-35l1-1z"></path><g class="H"><path d="M404 623h-1v-2-1c0-2-1-5 0-8h0l1 2c1 2 1 2 1 4v1c-1 1-1 1-1 2v2h0z"></path><path d="M398 603c1 3-1 8-2 11-1 13-1 22 4 34 1 1 1 2 2 3 1-1 1 0 1-1l1 3c0 1 0 1 1 2 0 2-1 4-1 6-2-8-6-15-9-22-2-12 0-23 2-35l1-1z"></path></g><path d="M220 269v-1l1-1h1c1-1 1-1 2-1 21 6 37 21 48 40 2 4 4 9 7 13l15 36 46 115 36 89 10 24c1 4 2 9 3 12s3 5 4 7c1 4 0 7 0 11v-1l1 1c0-3 1-6 2-8l1-1c-2 12-4 23-2 35v-1c-1-2-2-3-3-5 0-6 0-12-1-17 0-2 0-3-1-4 0-3 1-6-1-8h0v-2h-1l-1 1v-1c1-4-2-11-3-15l-10-26-37-90-50-121-12-26c-3-8-7-16-12-23-9-15-26-28-43-32z" class="P"></path><path d="M191 259c-3 2-5 6-7 9l2-74h217c3-14 10-26 19-37-2 5-5 10-6 15-2 6-2 11-3 17 0 1-1 3 0 4 1 0 2 0 3 1 0 11 2 21 5 32l1 1v2l19-3 1 1v1l-8 4c-3 2-7 4-9 7v1c3 7 7 14 11 19l6 6c-3 0-5-1-7-3l-4-1-2-1v-1h0c-1-1-2-2-4-3l-9-5v-1c0-2-3-4-4-6s-2-4-4-6c-2-4-6-8-9-12-2-3-5-5-5-8l-2-2h0-2c-4 1-8 1-11 1h-24-103-21c-3 0-8-1-12 0h-3l1 1c27 2 52 22 70 42 4 4 7 8 9 13 4 7 7 15 10 23l14 34 40 96 69 169 54 132 17 42 7 18c1 3 2 6 4 9l1 1c1-2 1-4 2-5 2-5 3-10 6-14v3c1 0 0 1 0 2l1 1-1 1c0-2 0-3-1-4-1 3-4 8-4 11l1 1c1 1 1 2 2 3l1 2c1 1 1 2 1 3 1 2 2 3 2 5 2 5 4 11 5 16v-1c0-1 1-1 2-2h0v-3l1-1c0-2 1-3 2-5 0-1 1-3 1-4 1-1 1-1 1-2h0c-1 4-3 8-3 11s-2 6-2 9c-1 1-1 2-1 2v1h-1v2l-2 5c0 1-1 2-1 2v1h0c-1 1-1 2-1 3 0 0 0 1-1 2v2l-2 5c0 1-1 1-1 2v1h0c-1 1-1 1-1 2l-3 8v1 2c-1 0-1 1-1 1 0 2 0-1 0 2-1 1-1 1-1 2v3l-1 1h0-1v1 1 1l1 1v1c0 1 0 1-1 2-4-16-10-31-16-46l-15-42h-2l-5-13c0-1 0-2-1-2l-1-3-1-4-1-1-2-5c0-1 0-1-1-2v2l-1 1v1l-8 4h0c2 0 3 0 4-1h1 1c-2 1-5 3-7 3h-1 0-1-4-4c1-1 2-1 3-1 2 1 4 0 6-1h0c-2 0-3-1-5-2h0c1 0 3 0 4-1h2l3-1c1 0 1 0 1-1-3-2-6-2-10-3 3 0 8 0 11-1-2-1-2 1-5-1l1-1h-4c-4 0-7-1-10-3l-6-2c-1 0-1 0-1 1s1 1 1 2c-11-2-18-7-23-17-1-1-2-4-3-5l-1-1c0-2-1-5-1-7 0-3-1-6-1-9 0-14 7-26 11-39 0-1 1-2 1-3 2-5 7-10 6-15l-1-2c0-2-1-3-2-5v-2l-2-4-1-2-9-22c0-1-1-2-1-2l-1-2h-1c-1 3-2 7-4 9v-3-4h0c2-2 4-6 3-8v-1c-1-2-2-5-3-7l-1-1v-1c1-1 0-2 0-4L273 287v-1c-2-4-5-8-8-11-2-3-4-5-7-7-10-8-18-16-30-18-8-2-19-2-26 3-4 1-7 3-11 6h0z"></path><path d="M395 197l14-1-8 9c0-2 0-3-1-5h-1c0 2-1 2-2 3-1-1-1-4-2-6h0 0z" class="N"></path><path d="M415 169c-1 9-3 16-3 24h-6-1c0-2 1-4 1-5 3-7 6-13 9-19z" class="E"></path><path d="M441 226l1 1v1l-8 4c-3 2-7 4-9 7l-3-8v-2c2 0 5 0 7-1 4 0 8-1 12-2z" class="C"></path><path d="M422 231h2c1 1 2 2 4 2 2-1 4-1 6-1-3 2-7 4-9 7l-3-8z" class="N"></path><path d="M403 601l25 60-2 1-1-2c0-2-1-3-2-5v-2l-2-4-1-2-9-22c0-1-1-2-1-2l-1-2h-1c-1 3-2 7-4 9v-3-4h0c2-2 4-6 3-8v-1c-1-2-2-5-3-7l-1-1v-1c1-1 0-2 0-4zm62 155l15 37h-2l-5-13c0-1 0-2-1-2l-1-3-1-4-1-1-2-5c0-1 0-1-1-2v2l-1 1v1l-8 4h0c2 0 3 0 4-1h1 1c-2 1-5 3-7 3h-1 0-1-4-4c1-1 2-1 3-1 2 1 4 0 6-1h0c-2 0-3-1-5-2h0c1 0 3 0 4-1h2l3-1c1 0 1 0 1-1-3-2-6-2-10-3 3 0 8 0 11-1l4-1v-1c0-1-1-2-1-4h1z" class="G"></path><path d="M511 796l1 1c0 1 1 2 1 3 1 7 0 14 0 21v31c0 4-2 12 0 15 0 2 0 3-1 5l-1-1c0-4-1-7-1-11v-19-28c0-5-1-12 1-17z" class="J"></path><defs><linearGradient id="m" x1="428.921" y1="668.105" x2="421.003" y2="675.465" xlink:href="#B"><stop offset="0" stop-color="#7b7775"></stop><stop offset="1" stop-color="#908e8e"></stop></linearGradient></defs><path fill="url(#m)" d="M428 661l37 95h-1l-13-33-1-2c0-1-1-1-1-2l-4-9-2-7-1-2-6-14v-1l-1-1c0-2-1-2-1-4-1-4-3-8-5-11h-1l-6 15v-4l-3-1c0-1 1-2 1-3 2-5 7-10 6-15l2-1z"></path><defs><linearGradient id="n" x1="455.375" y1="737.714" x2="443.677" y2="742.273" xlink:href="#B"><stop offset="0" stop-color="#555454"></stop><stop offset="1" stop-color="#8e8b8a"></stop></linearGradient></defs><path fill="url(#n)" d="M440 716v-4l1-4h0v-1h0c1-2 1-3 2-4l2 7 4 9c0 1 1 1 1 2l1 2 13 33c0 2 1 3 1 4v1l-4 1c-2-1-2 1-5-1l1-1h-4c-1-1-1-1-3-1 1-1 1-1 1-2v-5c-1-1-1-2-2-3 0-2-1-4 0-5l-1-3c-1-1-1-2-1-4-1 0-1-1-1-1-1-2-2-4-3-5v-1c-1-2-2-3-2-5-1-3-1-6-1-9z"></path><path d="M449 744c1 1 2 5 2 6v2c-1-1-1-2-2-3 0-2-1-4 0-5z" class="G"></path><path d="M457 760h8v1l-4 1c-2-1-2 1-5-1l1-1z"></path><path d="M440 716v-4l1-4h0v-1h0c1-2 1-3 2-4l2 7c-1 2-1 2-1 4l1 1c0 2 0 1 1 3v1c1 2 0 4 1 6 0 2-1 8 0 10h0l-1 1c-1-2-2-4-3-5v-1c-1-2-2-3-2-5-1-3-1-6-1-9z" class="S"></path><defs><linearGradient id="o" x1="189.671" y1="226.458" x2="196.844" y2="226.564" xlink:href="#B"><stop offset="0" stop-color="#828080"></stop><stop offset="1" stop-color="#a1a09f"></stop></linearGradient></defs><path fill="url(#o)" d="M189 200v-4c2 1 3 2 5 4l5 6v1h0c1 2 2 2 2 4 0 1 0 1-1 1 2 2 3 3 4 5 1 1 2 1 2 3v4c-1 1-1 2-1 2l1 1 2-1-20 29 1-14v-41z"></path><defs><linearGradient id="p" x1="189.553" y1="200.996" x2="197.939" y2="202.067" xlink:href="#B"><stop offset="0" stop-color="#9c9b9c"></stop><stop offset="1" stop-color="#bbb9b9"></stop></linearGradient></defs><path fill="url(#p)" d="M189 200v-4c2 1 3 2 5 4l5 6v1h0c1 2 2 2 2 4 0 1 0 1-1 1l-2-2h0l-1-1-2-2-3-3c-1-2-2-3-3-4z"></path><defs><linearGradient id="q" x1="191.682" y1="231.864" x2="205.278" y2="232.421" xlink:href="#B"><stop offset="0" stop-color="#8a8887"></stop><stop offset="1" stop-color="#cecdcd"></stop></linearGradient></defs><path fill="url(#q)" d="M198 210l2 2c2 2 3 3 4 5 1 1 2 1 2 3v4c-1 1-1 2-1 2l1 1 2-1-20 29 1-14c0 2 1 3 2 4l1-1v-1h1c0-1 0 0 1-1v-1h-1l1-1c0-1 0-2 1-2v-1-1s1-1 1-2c0-2 0-3 1-5v1c1-3 0-3 0-5v-1h-2v-2c-1-1 0-2 0-3h0l1-1h0c1-2 1-2 1-4 1-1 1-2 1-4z"></path><defs><linearGradient id="r" x1="528.854" y1="820.194" x2="500.511" y2="832.703" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#aaa6a6"></stop></linearGradient></defs><path fill="url(#r)" d="M512 797c1-2 1-4 2-5 2-5 3-10 6-14v3c1 0 0 1 0 2l1 1-1 1c0-2 0-3-1-4-1 3-4 8-4 11l1 1c1 1 1 2 2 3l1 2c1 1 1 2 1 3 1 2 2 3 2 5 2 5 4 11 5 16v-1c0-1 1-1 2-2h0v-3l1-1c0-2 1-3 2-5 0-1 1-3 1-4 1-1 1-1 1-2h0c-1 4-3 8-3 11l-18 52c-2-3 0-11 0-15v-31c0-7 1-14 0-21 0-1-1-2-1-3z"></path><defs><linearGradient id="s" x1="424.498" y1="209.415" x2="405.889" y2="235.105" xlink:href="#B"><stop offset="0" stop-color="#5d5d5b"></stop><stop offset="1" stop-color="#858383"></stop></linearGradient></defs><path fill="url(#s)" d="M413 196v4c0 3 0 6 1 9 2 15 6 31 14 44 3 3 5 6 7 8v1l-4-1-2-1v-1h0c-1-1-2-2-4-3l-9-5v-1c0-2-3-4-4-6s-2-4-4-6c-2-4-6-8-9-12-2-3-5-5-5-8l-2-2h0c1-1 2-1 3-2 2-1 3-1 5-3l9-9c1-2 3-4 4-6z"></path><path d="M394 218l9 9 18 20c1 1 5 6 7 6 3 3 5 6 7 8v1l-4-1-2-1v-1h0c-1-1-2-2-4-3l-9-5v-1c0-2-3-4-4-6s-2-4-4-6c-2-4-6-8-9-12-2-3-5-5-5-8z" class="O"></path><defs><linearGradient id="t" x1="442.275" y1="720.786" x2="416.529" y2="727.61" xlink:href="#B"><stop offset="0" stop-color="#949290"></stop><stop offset="1" stop-color="#cbc9c7"></stop></linearGradient></defs><path fill="url(#t)" d="M422 685l6-15h1c2 3 4 7 5 11 0 2 1 2 1 4l1 1v1l6 14 1 2c-1 1-1 2-2 4h0v1h0l-1 4v4c0 3 0 6 1 9 0 2 1 3 2 5v1c1 1 2 3 3 5 0 0 0 1 1 1 0 2 0 3 1 4l1 3c-1 1 0 3 0 5 1 1 1 2 2 3v5c0 1 0 1-1 2 2 0 2 0 3 1-4 0-7-1-10-3l-6-2c-1 0-1 0-1 1s1 1 1 2c-11-2-18-7-23-17-1-1-2-4-3-5l-1-1c0-2-1-5-1-7 0-3-1-6-1-9 0-14 7-26 11-39l3 1v4z"></path><path d="M451 752v5c0 1 0 1-1 2 2 0 2 0 3 1-4 0-7-1-10-3h1 2l1 1h3c0-2 0-4 1-6z" class="G"></path><defs><linearGradient id="u" x1="438.537" y1="686.96" x2="429.083" y2="693.08" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#898785"></stop></linearGradient></defs><path fill="url(#u)" d="M429 670c2 3 4 7 5 11 0 2 1 2 1 4l1 1v1l6 14 1 2c-1 1-1 2-2 4h0v1h0l-1 4v4l-3-2c0-1 1-2 1-2h0v-2c-2-2-2-2-2-5l-2-2 1-1-1-1-1 1-1-1 1-2-2-2c-1-1-1-1-1-2h0l-1-1 1-1c0 1 1 1 2 1-1 0-1 0-1-1l-1-1v-1c-1-2-3-4-5-4-3 2-2 9-5 12h-1l10-26v-1-2z"></path><defs><linearGradient id="v" x1="439.525" y1="708.955" x2="407.667" y2="731.03" xlink:href="#B"><stop offset="0" stop-color="#cac8c8"></stop><stop offset="1" stop-color="#f4f4f4"></stop></linearGradient></defs><path fill="url(#v)" d="M422 685l6-15h1v2 1l-10 26c-4 11-6 22-2 33 3 7 8 13 14 18l6 5c-1 0-1 0-1 1s1 1 1 2c-11-2-18-7-23-17-1-1-2-4-3-5l-1-1c0-2-1-5-1-7 0-3-1-6-1-9 0-14 7-26 11-39l3 1v4z"></path><path d="M431 750l6 5c-1 0-1 0-1 1-2 0-6-4-8-5l3-1z" class="O"></path><path d="M422 685l6-15h1v2 1l-10 26c-4 11-6 22-2 33 3 7 8 13 14 18l-3 1c-2-1-4-4-6-6-6-9-10-20-9-31v-1c1-6 4-11 5-16l4-12z" class="K"></path><path d="M401 205v1c3-3 8-9 12-10-1 2-3 4-4 6l-9 9c-2 2-3 2-5 3-1 1-2 1-3 2h-2c-4 1-8 1-11 1h-24-103-21c-3 0-8-1-12 0h-3l1 1h-3l-6 8-2 1-1-1s0-1 1-2v-4c0-2-1-2-2-3-1-2-2-3-4-5 1 0 1 0 1-1 0-2-1-2-2-4h0v-1l-5-6 1-2v-1h200 0 0c1 2 1 5 2 6 1-1 2-1 2-3h1c1 2 1 3 1 5h0z" class="E"></path><path d="M391 201l1-3 1-1c0 1 1 2 1 2 1 2 1 4 0 6h0-3v-4h0z" class="C"></path><path d="M195 198l3 3h0l15 15c-4-2-10-7-14-10l-5-6 1-2z" class="H"></path><defs><linearGradient id="w" x1="200.819" y1="217.228" x2="212.32" y2="215.669" xlink:href="#B"><stop offset="0" stop-color="#c7c5c5"></stop><stop offset="1" stop-color="#f1f1f0"></stop></linearGradient></defs><path fill="url(#w)" d="M199 206c4 3 10 8 14 10 1 1 2 1 3 1l1 1h-3l-6 8-2 1-1-1s0-1 1-2v-4c0-2-1-2-2-3-1-2-2-3-4-5 1 0 1 0 1-1 0-2-1-2-2-4h0v-1z"></path><path d="M401 205v1c3-3 8-9 12-10-1 2-3 4-4 6l-9 9c-2 2-3 2-5 3-1 1-2 1-3 2h-2c-4 1-8 1-11 1h-24-103-21c-3 0-8-1-12 0l-2-1 174-1c3-3 6-7 9-9l1-1z" class="G"></path><path d="M195 198v-1h200 0l-1 2s-1-1-1-2l-1 1-1 3v-3l-1-1c-1 2-1 4-1 6-1-2-1-4-2-7l-1 7v-1c-1-2-1-3-2-5l-1 7c-1-2 0-4-1-5h-1c-1 1-1 2-1 4l-1-3-1-1-1 1-1 1h0l-1-2h-1l-1 2-1-1-1-1-1 1s-1 0-1-1h-7-18-72-31l-34 1h-6c-1 0-2 1-3 1h0l-3-3z" class="D"></path><defs><linearGradient id="x" x1="745.872" y1="556.785" x2="509.791" y2="494.806" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#323231"></stop></linearGradient></defs><path fill="url(#x)" d="M588 216v-7-15h161 73v1 22c0 11 1 23 1 35 1 5 2 10 1 15v-1c-2-3-3-4-6-6v1c1 2 4 5 4 8l3 2c-2 1-2 3-4 3v1h-2l-3 1c-2 0-5 1-6 0-2 0-2 0-3-1h-2c-2 0-2 0-3-1h-2c-2 0-2 0-3-1h-5 0-5c-1 0-1 0-2 1-19 4-34 16-45 31-7 10-11 22-16 33-1 1-1 2-2 3l-1 2v1c-1 2-1 4-2 6h0 0c-1 1-1 2-2 3v1s0 1-1 1c-1 6-4 11-6 17 0 1-1 2-1 3v1c0 1-1 3-2 5-1 0-1 1-1 2h1c-1 2-2 7-4 8v1h-1l1 1-1 2v-2c0 1 0 1-1 2v1 1 2l-1-1v1h0c-1 4-4 9-6 13 0 2-1 3-1 5-1 1-1 0-1 2h1c-1 1 0 1-1 2l-1-1v1 1c-1 1-1 1 0 2h-1v2h0c0 1 0 2-1 3v1h-1v1l-1-1v2c-1 1-1 2-1 2-1 2-1 3-2 4 0-1-1-2-1-3l-3 6v-2 1c0 2 0 3-1 4s-1 2-1 3c-1 1-1 0-1 1s0 2-1 3v-1h0c0 2 0 3-1 4l-1 1h0c0 2 0 3-1 5h0v1c-1 2-2 4-2 5l-1-1v4c-1 0-1 0-1 1l-1 1v1c0 1-1 2-1 3-1 2-2 4-2 6l-1 1h0c0 2-1 4-2 6-1 0-1 1-1 1l-1 3c0 2-1 4-2 5 0 2-1 4-2 6l-3 5c0 1-1 2-1 3s0 1-1 2c0 1 0 2-1 3s-1 2-1 3l-1 1c-1 1-1 2-2 4v1 1c-1 1-1 1-1 2h-1v3l-1 1s0 1-1 2v2h0c-1 1-1 2-1 3l-1 1c0 1 0 0-1 1v2l-1 1c0 1 0 1-1 2v2l-1 2h0c0 1-1 2-1 3l-1 1v1l-6 32c0 2 0 5 1 7v-1c1 2 1 2 1 4h0v1l1 1v1h0c0 1 0 1 1 2 0 1-1 2 0 3 0 1 0 1 1 2 0 1-1 1 0 2 0 1 1 5 0 6l-1-3v-1-3l-1-1v-1-3l-1-1v-2l-1-2c-1 2 1-1-1 1 0 1-1 2-2 2-2 1-3 2-4 4l1 1-1 1c0 1 0 1 1 2 0 1-1 3 0 4v4h0l-2 4c1 8 1 16-1 25-1 2-1 4-2 6-1 1 0 2 0 4-1 2-2 4-3 7 0 2 0 4-1 6-3 6-6 11-10 16-2 2-4 5-7 7l-19 13h1c2 0 4-1 7-2 2 0 5 0 7-1 4-1 7-5 11-6v1c-6 5-12 9-19 12-5 2-9 0-12 6l-3 9c-1 2-1 3-3 4-2 4-3 8-4 11-2 5-4 9-5 14l-6 15-8 22c-4 8-7 17-10 26l-16 45-4 11c0 2-1 5-2 7v-1l-1-1v-1-1-1h1 0l1-1v-3c0-1 0-1 1-2v-2s0-1 1-1v-2-1l3-8c0-1 0-1 1-2h0v-1c0-1 1-1 1-2l2-5v-2c1-1 1-2 1-2 0-1 0-2 1-3h0v-1s1-1 1-2l2-5v-2h1v-1s0-1 1-2c0-3 2-6 2-9s2-7 3-11h0c0 1 0 1-1 2 0 1-1 3-1 4-1 2-2 3-2 5l-1 1v3h0c-1 1-2 1-2 2v1c-1-5-3-11-5-16 0-2-1-3-2-5 0-1 0-2-1-3l-1-2c-1-1-1-2-2-3l-1-1c0-3 3-8 4-11 1 1 1 2 1 4l1-1-1-1c0-1 1-2 0-2v-3l57-150c1-4 2-7 3-10l7-19 17-45c1-4 3-9 5-13l9-24 17-49 8-21c3-10 8-21 12-30 3-10 6-20 10-29 1-3 3-7 4-10l7-18 8-23c2-4 3-8 5-12 1-6 4-12 7-18 4-12 8-25 14-36 1-3 3-5 5-8 18-21 47-42 76-45h2 0l-1-1H618v1h-1c-3 1-3 4-5 6-4 5-13 18-19 21-1 0-2 1-3 2l-1 1h0l1-1h3c-2 1-4 2-5 3h-1v-1l-1-3 1-1v-2l1-8v-4-5-6-4z"></path><path d="M791 218h2c1 0 2 0 3 1s3 4 4 6l12 20 3 6c-4-2-5-5-8-8l-6-9-2-4-2-3c-1-3-5-5-5-8l-1-1z"></path><path d="M588 216v-7-15h161c-2 1-91 0-102 0-3 0-20 0-22 1h-12-4c-5-1-12-1-17 0h-2-1v1c0 2-1 8 0 9 0 2 1 5 0 7v4c-1 2-1 4 0 6 0 3 0 12-1 14v1 1c0 1 1 3 0 4 0 2-1 1 0 3 0-1 0-2 1-2v2h1c2-1 3-3 5-5l13-15 6-6c1-1 1-1 3-1-3 1-3 4-5 6-4 5-13 18-19 21-1 0-2 1-3 2l-1 1h0l1-1h3c-2 1-4 2-5 3h-1v-1l-1-3 1-1v-2l1-8v-4-5-6-4z" class="T"></path><defs><linearGradient id="y" x1="618.473" y1="578.437" x2="634.277" y2="589.66" xlink:href="#B"><stop offset="0" stop-color="#888585"></stop><stop offset="1" stop-color="#bebcba"></stop></linearGradient></defs><path fill="url(#y)" d="M625 573v1c1-3 5-16 7-17v2 1c0-1 1-1 1-2s1-2 1-3l-6 32c0 2 0 5 1 7v-1c1 2 1 2 1 4h0v1l1 1v1h0c0 1 0 1 1 2 0 1-1 2 0 3 0 1 0 1 1 2 0 1-1 1 0 2 0 1 1 5 0 6l-1-3v-1-3l-1-1v-1-3l-1-1v-2l-1-2c-1 2 1-1-1 1 0 1-1 2-2 2-2 1-3 2-4 4l1 1-1 1c0 1 0 1 1 2 0 1-1 3 0 4v4h0c-1-2-1-10-2-11l-2 1-1-7c0-1 0-1-1-2l-1 1v2l-2 2v1c0 1 0 0-1 2-1 1-1 3-2 5 0 2-2 5-2 7l-13 36c-2 4-3 8-5 13-1 2-3 6-3 9h-1l25-69 7-20c2-5 4-10 6-14z"></path><defs><linearGradient id="z" x1="590.744" y1="223.305" x2="605.122" y2="224.903" xlink:href="#B"><stop offset="0" stop-color="#817f7e"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#z)" d="M591 199v-2c1 0 2 1 3 1l1 2c3 4 8 8 13 12 1 1 3 3 6 4 1 1 2 1 3 1h1v1h-1c-2 0-2 0-3 1l-6 6-13 15c-2 2-3 4-5 5h-1c2-4 1-12 1-15l1-31z"></path><defs><linearGradient id="AA" x1="598.251" y1="212.343" x2="613.823" y2="214.048" xlink:href="#B"><stop offset="0" stop-color="#b2b1b0"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#AA)" d="M591 199v-2c1 0 2 1 3 1l1 2c3 4 8 8 13 12 1 1 3 3 6 4 1 1 2 1 3 1h1v1h-1c-2 0-2 0-3 1l-6 6-2-1c0-1 0-4-1-5v-1c-1-2-2-4-4-6-3-2-6-5-8-7 0-1-1-3-2-4v-2z"></path><defs><linearGradient id="AB" x1="682.391" y1="393.052" x2="669.033" y2="396.573" xlink:href="#B"><stop offset="0" stop-color="#4c4b4b"></stop><stop offset="1" stop-color="#656463"></stop></linearGradient></defs><path fill="url(#AB)" d="M665 388c1-3 3-7 4-10l7-18 8-23c2-4 3-8 5-12v1c0 1 0 1-1 2v2s0 1-1 1v1c-1 1-1 3-2 5l-1 3c0 1 0-1 0 1l-1 1-1 2v2s0 1-1 1v1 2c-1 0-1 1-1 2h-1v2 1l-1 1h0l-1 2v2h-1c0 2-1 4-1 6h-1c1 1 0 1 1 1 1 1 1 2 1 3 1 1 1 1 1 2 0 0 1 1 1 2v1c0 1 0 1 1 3v2c0 1 0 1 1 2v2c1 0 1 1 1 2v1c1 1 1 2 1 3s1 2 1 4v2h1v2 1 2l2-2v-1c1-2 1-4 2-5v-2-1c1-1 1-2 2-3 0-1 1-3 1-4l3-6c0-3 2-5 2-7l1-4c1-2 2-4 3-5 0-1 0-2 1-3v1l-16 43c-3 8-5 17-9 24v-5l-1-5c-1-5-3-10-4-15-1-3-1-7-2-10l-1-1c0-1 0-2-1-3l-1 1h-1z"></path><path d="M818 197c0 4-1 8 0 12v5c1 15 1 31 3 46l-6-9-3-6-12-20c-1-2-3-5-4-6s-2-1-3-1h0c3-2 7-5 9-7l16-14z" class="J"></path><defs><linearGradient id="AC" x1="675.756" y1="420.189" x2="653.176" y2="425.705" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#949392"></stop></linearGradient></defs><path fill="url(#AC)" d="M665 388h1l1-1c1 1 1 2 1 3l1 1c1 3 1 7 2 10 1 5 3 10 4 15l1 5v5l-13 35-5 13c-1 3-2 6-3 8v-3h0l1-3h0l1-1v-2s1-1 1-2v-1c1-1 1-1 1-2l1-3 1-2 1-1v-1-1-2c-2-3-3-7-4-11l-2-13v-1l-3-9c-1 3-7 21-9 22l-1 1c3-10 8-21 12-30 3-10 6-20 10-29z"></path><defs><linearGradient id="AD" x1="582.381" y1="615.53" x2="612.825" y2="588.671" xlink:href="#B"><stop offset="0" stop-color="#484847"></stop><stop offset="1" stop-color="#949292"></stop></linearGradient></defs><path fill="url(#AD)" d="M577 628c1-4 2-7 3-10l7-19 17-45h0c0 2 0 3-1 4-1 2-1 4-2 5l-3 9 1 1c2 1 3 3 5 6l3 3 6 8c1-1 2-3 2-4v-1l3-6c0-1 1-2 1-2l-27 74c0-2 1-3 1-5l3-6 1-5c1-1 1-1 1-2l1-1h-1c-1-1-2-1-3-2-2-2-4-5-6-7l-6-8c-2 3-3 10-6 12v1z"></path><defs><linearGradient id="AE" x1="592.798" y1="641.748" x2="620.219" y2="640.693" xlink:href="#B"><stop offset="0" stop-color="#7d7979"></stop><stop offset="1" stop-color="#abaaa8"></stop></linearGradient></defs><path fill="url(#AE)" d="M616 601v-2l1-1c1 1 1 1 1 2l1 7 2-1c1 1 1 9 2 11l-2 4c1 8 1 16-1 25-1 2-1 4-2 6-1 1 0 2 0 4-1 2-2 4-3 7 0 2 0 4-1 6-3 6-6 11-10 16-2 2-4 5-7 7l-19 13h1c2 0 4-1 7-2 2 0 5 0 7-1 4-1 7-5 11-6v1c-6 5-12 9-19 12-5 2-9 0-12 6l-3 9c-1 2-1 3-3 4l20-52h1c0-3 2-7 3-9 2-5 3-9 5-13l13-36c0-2 2-5 2-7 1-2 1-4 2-5 1-2 1-1 1-2v-1l2-2z"></path><defs><linearGradient id="AF" x1="598.263" y1="635.304" x2="608.946" y2="634.684" xlink:href="#B"><stop offset="0" stop-color="#6c6a6a"></stop><stop offset="1" stop-color="#848281"></stop></linearGradient></defs><path fill="url(#AF)" d="M609 618l1 1h0 0v2c-1 4 0 8-1 12 0 4 0 8-2 11l-9 9-2 1 13-36z"></path><path d="M607 644l-1-1h0c0-3 2-7 3-10 0 4 0 8-2 11z" class="G"></path><path d="M616 601v-2l1-1c1 1 1 1 1 2l1 7 2-1c1 1 1 9 2 11l-2 4c1 8 1 16-1 25-1 2-1 4-2 6-1 1 0 2 0 4-1 2-2 4-3 7h-1c-1 2-1 4-2 6s-2 4-4 6c-1 1-1 3-3 4h0l1-2v-1l-1 1-1-1c1-1 1-2 2-3l1-2c1-2 2-5 4-7v-1l1-1c0-1 1-2 1-3l1-1c0-1 1-2 1-3l2-4h0l2-6 1-7v-5c1-2 1-5 1-7-1-3-1-7-1-11l-4-14z" class="R"></path><path d="M619 607l2-1c1 1 1 9 2 11l-2 4-2-14z" class="M"></path><path d="M596 654l2-1c-1 2-2 4-2 6-1 2-2 5-2 7-1 3 0 7 0 10 0 1 0 2 1 4l1 4c1 0 2-1 3-2h-1c-1-1-1-3-1-4v-3c-1-3-2-7-1-10 0-1 2-2 2-3 1-1 1-2 2-3 1 1 1 3 1 4v2 1 1c0 2 1 5 1 7l-1 1 1 1 1-2h1l1-1h1c-1 1-1 2-2 3l1 1 1-1v1l-1 2h0c2-1 2-3 3-4 2-2 3-4 4-6s1-4 2-6h1c0 2 0 4-1 6-3 6-6 11-10 16-2 2-4 5-7 7l-19 13h1c2 0 4-1 7-2 2 0 5 0 7-1 4-1 7-5 11-6v1c-6 5-12 9-19 12-5 2-9 0-12 6l-3 9c-1 2-1 3-3 4l20-52h1c0-3 2-7 3-9 2-5 3-9 5-13z" class="R"></path><path d="M606 673c-1 1-1 2-2 3l1 1 1-1v1l-1 2h0c2-1 2-3 3-4 2-2 3-4 4-6s1-4 2-6h1c0 2 0 4-1 6-3 6-6 11-10 16-2 2-4 5-7 7-2 0-2 0-3 1h-1 0l2-2c-1-1-1-1-1-2 0 0 2-1 2-2 1 0 2-1 2-2l5-6v-1l-3 3c-1-1-1-2-2-3v-1l-1-4v-8h1c0 3 1 8 3 10h0l1 1 1-2h1l1-1h1z" class="G"></path><path d="M567 728l20-52h1c-3 6-5 11-7 17 0 1 0 2-1 2l1 1c1-1 2-1 3-2l3-2c4-2 7-4 11-7 0 1-1 2-2 2 0 1-2 2-2 2 0 1 0 1 1 2l-2 2h0 1c1-1 1-1 3-1l-19 13h1c2 0 4-1 7-2 2 0 5 0 7-1 4-1 7-5 11-6v1c-6 5-12 9-19 12-5 2-9 0-12 6l-3 9c-1 2-1 3-3 4z" class="Q"></path><defs><linearGradient id="AG" x1="586.901" y1="699.821" x2="588.599" y2="689.679" xlink:href="#B"><stop offset="0" stop-color="#555558"></stop><stop offset="1" stop-color="#74706f"></stop></linearGradient></defs><path fill="url(#AG)" d="M594 689c0 1 0 1 1 2l-2 2h0 1c1-1 1-1 3-1l-19 13h-1c1-2 1-4 2-5l3-3c4-3 8-5 12-8z"></path><path d="M643 447l1-1c2-1 8-19 9-22l3 9v1l2 13c1 4 2 8 4 11v2 1 1l-1 1-1 2-1 3c0 1 0 1-1 2v1c0 1-1 2-1 2v2l-1 1h0l-1 3h0v3l-36 95h0s-1 1-1 2l-3 6v1c0 1-1 3-2 4l-6-8-3-3c-2-3-3-5-5-6l-1-1 3-9c1-1 1-3 2-5 1-1 1-2 1-4h0c1-4 3-9 5-13l9-24 17-49 8-21z" class="D"></path><path d="M802 207c1 0 2-1 3-2 3-2 5-5 8-7 1 0 1 0 2-1h3l-16 14c-2 2-6 5-9 7l-1-1H618h-1c-1 0-2 0-3-1-3-1-5-3-6-4-5-4-10-8-13-12 0-1 0-2-1-2v-1h6 13 45 155l-3 3-8 7z" class="E"></path><path d="M802 207c1 0 2-1 3-2 3-2 5-5 8-7 1 0 1 0 2-1h3l-16 14c-2 2-6 5-9 7l-1-1H618h-1c1-1 2-1 3-1h49 89l22-1c4 0 9 1 12 0s8-6 10-8z" class="G"></path><path d="M658 197h155l-3 3c-1-1-1-1-2-1-1 1-1 0-2 0h-5v5h0v-1c-1-1-1-4-3-5h-1v8c-1-3 0-7-2-8l-1 1h-1c-2 1-1 5-1 7l-1-5c0-1-1-1-1-2h-2c-1 3-1 5-1 7 0-2 0-6-1-7h-3c0 2-1 3-1 5l-1-4c-1-1-1-1-2-1v7l-2-6-3-1c-1 2 0 4 0 5-1-1-1 0-1-1s0-3-1-4h-1c-1 1-1 2-1 3l-1-2-1-1c0 1-1 2-1 3l-1-2H601c-1 0-2 0-2 1l9 9 6 6c-3-1-5-3-6-4-5-4-10-8-13-12 0-1 0-2-1-2v-1h6 13 45z" class="Q"></path><path d="M595 200c0-1 0-2-1-2v-1h6 13 45-63c0 1 3 3 4 4l9 9 6 6c-3-1-5-3-6-4-5-4-10-8-13-12z" class="U"></path><defs><linearGradient id="AH" x1="665.242" y1="346.475" x2="755.929" y2="445.189" xlink:href="#B"><stop offset="0" stop-color="#908e8e"></stop><stop offset="1" stop-color="#bab9b7"></stop></linearGradient></defs><path fill="url(#AH)" d="M769 254h0c8-4 19-7 28-4 8 1 15 5 21 10v1c1 2 4 5 4 8l3 2c-2 1-2 3-4 3v1h-2l-3 1c-2 0-5 1-6 0-2 0-2 0-3-1h-2c-2 0-2 0-3-1h-2c-2 0-2 0-3-1h-5 0-5c-1 0-1 0-2 1-19 4-34 16-45 31-7 10-11 22-16 33-1 1-1 2-2 3l-1 2v1c-1 2-1 4-2 6h0 0c-1 1-1 2-2 3v1s0 1-1 1c-1 6-4 11-6 17 0 1-1 2-1 3v1c0 1-1 3-2 5-1 0-1 1-1 2h1c-1 2-2 7-4 8v1h-1l1 1-1 2v-2c0 1 0 1-1 2v1 1 2l-1-1v1h0c-1 4-4 9-6 13 0 2-1 3-1 5-1 1-1 0-1 2h1c-1 1 0 1-1 2l-1-1v1 1c-1 1-1 1 0 2h-1v2h0c0 1 0 2-1 3v1h-1v1l-1-1v2c-1 1-1 2-1 2-1 2-1 3-2 4 0-1-1-2-1-3l-3 6v-2 1c0 2 0 3-1 4s-1 2-1 3c-1 1-1 0-1 1s0 2-1 3v-1h0c0 2 0 3-1 4l-1 1h0c0 2 0 3-1 5h0v1c-1 2-2 4-2 5l-1-1v4c-1 0-1 0-1 1l-1 1v1c0 1-1 2-1 3-1 2-2 4-2 6l-1 1h0c0 2-1 4-2 6-1 0-1 1-1 1l-1 3c0 2-1 4-2 5 0 2-1 4-2 6l-3 5c0 1-1 2-1 3s0 1-1 2c0 1 0 2-1 3s-1 2-1 3l-1 1c-1 1-1 2-2 4v1 1c-1 1-1 1-1 2h-1v3l-1 1s0 1-1 2v2h0c-1 1-1 2-1 3l-1 1c0 1 0 0-1 1v2l-1 1c0 1 0 1-1 2v2l-1 2h0c0 1-1 2-1 3l-1 1v1c0 1-1 2-1 3s-1 1-1 2v-1-2c-2 1-6 14-7 17v-1c0-2 1-4 2-6l5-14 20-54 63-168 11-30c2-5 5-10 7-16 1-3 4-5 5-8 9-9 19-18 31-23z"></path><path d="M721 330l1 1v1h1 0l-1 1-1 1c0-1-1-1-1-2l1-2z" class="P"></path><path d="M692 406h1l-1 4-1 4h0v-1c-1 1-1 2-1 4h-2l4-11zm40-96c1 0 1 0 2 1v1h0c0 3-3 8-5 9h0l-1-1c0 1 0 1-1 2l-1 1-1-1 1-1 1-1h0l1-2 1-1c-1-2 1-4 2-5h1v-2z" class="D"></path><path d="M792 273c-2-1-3-1-4-2h-2 2l-1 1h-2 0c-3 2-6 1-8 3h-2v-1h2-4c1-1 3 0 5-1h0-1-2v-1h2c1 0 1-1 2-1h1l-1-1h0l1-1 1 1h1v-1c1-1 4-1 6-2 2 0 6 0 8 1h0 1 1v1l3 2c0 1 0 0-1 1h0c-1 0-3-1-4 0v-1l-4 2h0z" class="P"></path><path d="M769 254h0c8-4 19-7 28-4l1 1 3 1h1l2 1 1 1c2 0 3 1 4 2h2v1h-2c-1-1-2-2-3-2h-3c-4 2-7 1-11 2-2 1-4 0-6 1h-2l-3 1c-8 1-14 4-21 7-3 1-6 3-8 5s-6 4-9 5l-6 6v1c-1 1-2 2-3 4v-1c2-5 7-10 11-14 3-3 7-7 10-9l2-1c2-1 4-3 6-4l6-3v-1z" class="R"></path><path d="M577 628v-1c3-2 4-9 6-12l6 8c2 2 4 5 6 7 1 1 2 1 3 2h1l-1 1c0 1 0 1-1 2l-1 5-3 6c0 2-1 3-1 5l-2 7-2 5c0 1-1 2-1 4-1 1-2 2-2 4 0 1-1 2-1 4-1 1-2 2-2 4-1 2-2 4-2 6-1 1-2 3-2 4s-1 3-1 4l-1 2h0c-1 1-1 2-1 4l-1 1v1l-3 7-4 12c-1 1-1 2-1 3-1 1-2 3-2 4-1 2-2 4-2 7-1 3-3 5-4 9s-3 8-5 12l-1 2v1c-1 1-2 3-2 4v2h-1c0 1-1 2-1 4v1l-1 1c0 1-1 2-1 3v2h-1c0 1-1 2-1 4-2 5-4 11-6 17-1 2-2 6-4 8h0c0 1 0 1-1 2 0 1-1 3-1 4-1 2-2 3-2 5l-1 1v3h0c-1 1-2 1-2 2v1c-1-5-3-11-5-16 0-2-1-3-2-5 0-1 0-2-1-3l-1-2c-1-1-1-2-2-3l-1-1c0-3 3-8 4-11 1 1 1 2 1 4l1-1-1-1c0-1 1-2 0-2v-3l57-150z" class="O"></path><path d="M558 734l1 1v1l-1-1v-1zm-1 2s0 1 1 1v1h-2l1-2z" class="T"></path><path d="M219 217c4-1 9 0 12 0h21 103 24c3 0 7 0 11-1h2 0l2 2c0 3 3 5 5 8 3 4 7 8 9 12 2 2 3 4 4 6s4 4 4 6v1c-12-5-23-6-35-1-10 4-18 12-24 21-2 3-3 6-5 9v-1l-1 1c0 1 0 1-1 1v1 1h0c-1 1-1 1-1 2v1 1l-1 1v1l-1 1h0c0 1 0 1-1 2v1 1c-1 0 0 0-1 1v1 1h0-1c0-4 3-9 4-13 1-2 2-3 1-4-1 2-2 5-3 7v2h-1c0 3-1 6-3 8 2 6 39 95 39 95 1 0 2 1 3 1l5 13c1 2 2 5 3 6l3-4 1 1 2-2c12-10 24-18 39-23 4-2 7-3 12-1h2v-1l3-1 11 3 2 1c2 0 3 0 4 1l7-3c-1 2-3 5-6 6l-1 1h-1c-4 4-6 7-9 11 2-1 3-3 5-4 0 3-3 6-4 9l-1 1v2c0-1 1-2 1-3 3-4 6-8 10-11l6-4c17-10 43-14 61-10-1 1-3 1-5 1-5 2-11 2-16 3h2l1 2h-10-8c-3 1-6 2-8 3h1c3 2 8 2 12 3 15 2 28 0 43-5 5-2 9-5 13-7l10-7c20-15 36-39 39-65 1-9 1-17-1-26v-1l1-1 1 1 1 12h1v-2s0-1 1-2h0 1 0l1-1c3-9 3-17 4-26v-1-4l1-6 2 2c2 0 3 1 5 2v1c13 8 22 21 28 34 0 1 3 8 3 9l1-2-3-6c-2-5-3-9-6-13-8-13-21-28-37-31h-1c-7-2-16-2-23-1h-3l-1 1h0l1-1c1-1 2-2 3-2 6-3 15-16 19-21 2-2 2-5 5-6h1v-1h174l1 1h0-2c-29 3-58 24-76 45-2 3-4 5-5 8-6 11-10 24-14 36-3 6-6 12-7 18-2 4-3 8-5 12l-8 23-7 18c-1 3-3 7-4 10-4 9-7 19-10 29-4 9-9 20-12 30l-8 21-17 49-9 24c-2 4-4 9-5 13l-17 45-7 19c-1 3-2 6-3 10l-57 150c-3 4-4 9-6 14-1 1-1 3-2 5l-1-1c-2-3-3-6-4-9l-7-18-17-42-54-132-69-169-40-96-14-34c-3-8-6-16-10-23-2-5-5-9-9-13-18-20-43-40-70-42l-1-1h3z" class="F"></path><path d="M561 586l4-10h2l-4 11c-1 1-1 2-1 2h-1v-1h0l1-1v-1h0-1zm99-277l2 2-12 33c0-1-1-2-1-3l7-20c0-2 1-4 2-6l2-6z" class="J"></path><path d="M541 645c0 2-1 3-1 6l-4 10-15 38-5 13c-1 3-2 5-2 9-1 1-1 1-1 2v8c0 2-1 4 0 7 0-1-1-3 0-4 0-1 0-1 1-2v-3c0-2 0-4 1-5 0-1-1-3 0-4 0-2 0-2 1-3 0-1-1-2 0-3v-1l1-1v-1l-4 37h0l-1 1-1-1 2-32 3-5 1-5 12-30 3-7c-1 0-2 0-2-1s0-2-1-3c2-1 2-2 3-3l2-7v5 1-1l1 1c2-5 4-11 6-16z" class="C"></path><path d="M534 655v5 1-1l1 1-3 8c-1 0-2 0-2-1s0-2-1-3c2-1 2-2 3-3l2-7z" class="K"></path><path d="M543 587h0c0-1 1-2 0-3h0l1-1v1l1 1 3 4v1c3 1 4 3 6 5 0-2 1-2 1-4 2-1 3-3 4-5 1-3 3-7 4-10h0l1-1v-2l1-1 2-6h1 0c0 1 1 2 1 3l-2 7h-2l-4 10h1 0v1l-1 1h0v1h1l-13 33v-1c0-1 0-2 1-3 0-1 0-1-1-2v-2c0-1 0 0 1-1 0-8-2-14-5-21 0-1 0-3-2-5z" class="G"></path><path d="M565 576l3-10c0 1 1 2 1 3l-2 7h-2z" class="T"></path><path d="M561 586h1 0v1l-1 1h0v1h1l-13 33v-1c0-1 0-2 1-3 0-1 0-1-1-2l6-17c-1-2-3-4-4-5v-1l5 5 5-12z" class="K"></path><path d="M645 351l4-10c0 1 1 2 1 3l-22 61c-3 7-5 15-8 21h-1c0-3 2-6 2-9 1-1 1-1 0-1v-2c1-1 1-1 2-1-2-2-4-6-6-7-3-4-7-7-11-9h6c4-1 8-1 12-2v-1c-3 0-4-1-6-2l1-1c0 1 1 1 2 1h0c-4-3-8-5-12-9h-1v2h-1c-1 0-2-1-3-2 1 1 2 1 3 1 0-2-2-3-1-5v-1c0 1 1 3 2 4h0l6 4c1 1 3 2 5 3 3 1 6 5 10 5l16-41v-1-1z" class="O"></path><path d="M606 397h6c4-1 8-1 12-2v-1c-3 0-4-1-6-2l1-1c0 1 1 1 2 1h0c2 1 7 3 8 4-1 1-1 2-2 2-2 0-5 0-7 1h1c2 1 4 1 7 1h0c-3 4-3 9-5 13-2-2-4-6-6-7-3-4-7-7-11-9z" class="D"></path><defs><linearGradient id="AI" x1="535.142" y1="625.9" x2="548.171" y2="615.858" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#938f8f"></stop></linearGradient></defs><path fill="url(#AI)" d="M541 578h0l1 11h0c0 2 0 10 1 11 1-3 0-6 1-9 0-1 0-2-1-4 2 2 2 4 2 5 3 7 5 13 5 21-1 1-1 0-1 1v2c1 1 1 1 1 2-1 1-1 2-1 3v1c-1 3-6 20-8 22l-4-2 2-7c0-3 2-16 1-19-1-2-3-4-4-6 0-3-2-6-3-9 1 1 2 2 4 2 1 2 1 3 2 5v-25h1v7c2-3 1-9 1-12z"></path><path d="M542 589h0c0 2 0 10 1 11 1-3 0-6 1-9l2 8h0c-2 2-1 9-1 12h-1v-8c-1 1-1 3-1 4l-1 1v-19z" class="D"></path><path d="M541 578h0l1 11v19c-1 9 0 18-2 27h-1c0-3 2-16 1-19-1-2-3-4-4-6 0-3-2-6-3-9 1 1 2 2 4 2 1 2 1 3 2 5v-25h1v7c2-3 1-9 1-12z" class="T"></path><path d="M543 587c2 2 2 4 2 5 3 7 5 13 5 21-1 1-1 0-1 1v2c1 1 1 1 1 2-1 1-1 2-1 3v1c-1 3-6 20-8 22l-4-2 2-7h1v4h0c2-6 5-11 6-17v-10c0-3-1-7 0-11v-2h0l-2-8c0-1 0-2-1-4z" class="L"></path><path d="M546 599c1 2 2 4 2 6v1c1 5 0 10-2 16v-10c0-3-1-7 0-11v-2h0z" class="G"></path><path d="M517 711l1 1 1-2h2c3 0 6-3 8-5 1-1 2-3 3-4l-1-1 1-1c1-1 0-2 1-3 1-2 1-4 2-6-1-3 2-7 3-10 0-2 1-3 3-4v-1c0-1 0-1 1-2l1-2v-1-2h1c0-1 0-1 1-2 0-1 1-3 1-4s1-3 2-4v-1-1c1-2 1-3 2-4v-2l1-1 1-2v-1-2h1v-2h0v-2-1l1-1 1-2h-3-3 0c1-2 2-1 3-2 1 0 2 0 3-1h1l1-1h0 1v-1c1-2 2-5 3-7v-1-1c1-3 2-4 3-6 1-1 1-2 1-3 1-1 1-2 2-3v-1l2-7c1-1 1-2 1-2l1-3c1 0 1-1 1-1l1-4 3-6c0-1 1-3 1-4l1-2c0-1 1-2 1-3l1-3c0-1 1-2 1-3s1-3 2-4l1-3c1-2 1-6 3-7v1c0 1-1 2-1 4l-9 23v1l-2 6-1 1-8 23c-1 1-1 2-1 2l-2 4-1 5h-1l-2 8-3 6v2l-1 1c-1 2-2 5-2 7-1 1-1 1-1 2-1 1-1 2-2 3 0 2-1 3-1 4l-1 3c0 1-1 2-1 3s-1 3-2 4c0 2-1 4-2 6 0 2-1 3-2 4v3c-1 1-1 2-2 3l-1 3-1 3c0 2-1 4-2 5 0 2-1 3-2 4v3l-1 1c0 1 0 2-1 2 0 3-1 5-2 7l-1 2v1l-1 2-6 12c0 2-1 4-2 6-2 3-3 8-6 11l4-37zm111-355h0l17-4v1l-16 41c-4 0-7-4-10-5-2-1-4-2-5-3l-6-4h0c-1-1-2-3-2-4s-1-2-1-3c-1-1-1-1-1-2l-1-1c0-1-1-1-2-2-2-2-8-7-9-10l2-2s0-1 1-1c3 1 7 1 10 1 8 0 16 0 23-2z" class="B"></path><path d="M620 369c0-1-1-1-1-2-1-1-2-1-3-2 0-1 0 0-1-1h0l-1-1v-1h0l1-1h1c1-2 5-1 7-1 0 1 1 1 1 3-2 1-3 1-5 1 1 1 2 1 2 3l-1 2z" class="C"></path><path d="M616 377c0-1-2-2-3-3-3-4-6-7-10-10v-1l-2-2c-1 0 0 0-1-1h1 5c1 0 1 2 2 3 1 0 2 1 2 1 2 0 3 1 5 2v3 2h4c1 2 0 4 2 6 0 1 1 1 1 2s0 1-1 2h-1l-2-2c0-1-1-1-2-2z" class="F"></path><path d="M601 370c-2-2-8-7-9-10l2-2c0 1 1 2 2 2 4 2 6 7 10 8v1c2 1 4 3 6 5 1 1 3 3 4 3 1 1 2 1 2 2l2 2h1c1-1 1-1 1-2s-1-1-1-2c-2-2-1-4-2-6h-4v-2-3c2 1 4 3 6 4v1c1 2 2 4 2 6 0 1 1 2 1 2 0 3 2 5 3 7v1 2c-1-1-1 0-1-1-1-2-3-3-4-4l-1 1c2 1 3 2 4 4l1 1c-2 0-7-2-8-4h-1l-1-1c-1-1-1-2-2-2l-2-2h0v1h0c1 1 1 1 1 2l1 2-6-4h0c-1-1-2-3-2-4s-1-2-1-3c-1-1-1-1-1-2l-1-1c0-1-1-1-2-2z" class="I"></path><path d="M606 369c2 1 4 3 6 5-1 1-2 1-4 1 0-2-1-4-2-6z" class="E"></path><defs><linearGradient id="AJ" x1="637.103" y1="372.942" x2="617.988" y2="367.67" xlink:href="#B"><stop offset="0" stop-color="#a3a1a0"></stop><stop offset="1" stop-color="#e4e2e1"></stop></linearGradient></defs><path fill="url(#AJ)" d="M628 356h0l17-4v1l-16 41c-4 0-7-4-10-5-2-1-4-2-5-3l-1-2c0-1 0-1-1-2h0v-1h0l2 2c1 0 1 1 2 2l1 1h1c1 2 6 4 8 4l-1-1c-1-2-2-3-4-4l1-1c1 1 3 2 4 4 0 1 0 0 1 1v-2-1c-1-2-3-4-3-7 0 0-1-1-1-2 0-2-1-4-2-6v-1l-1-1 1-2c0-2-1-2-2-3 2 0 3 0 5-1 0-2-1-2-1-3l2-1h2c0-1 1-2 1-3z"></path><path d="M565 382c2-1 4-3 5-2 18 5 34 16 47 29 1 1 3 3 4 5v2c1 0 1 0 0 1 0 3-2 6-2 9h1c-1 2-5 16-6 17l-45 126c0-1-1-2-1-3h0v-2c2-4 3-8 5-12l11-31 17-47c1-4 4-8 4-12 0-2 0-5-1-7l-4-13 1-1c-5-14-11-32-23-42l-3-2c0-1 1-2 1-3h0l-5-2h0l-9-5-1-1 2-1c0-1 0-1 1-2l1-1z"></path><path d="M599 408l1-1c3 2 6 3 9 6 2 2 5 5 8 5 2 0 3 0 4-1v-1c1 0 1 0 0 1 0 3-2 6-2 9l-1 1h0c-3-6-9-11-14-15-2-2-4-3-5-4z" class="G"></path><path d="M561 386c4 0 9 3 12 5 1 0 3 1 4 1 4 3 10 5 14 9 2 2 4 5 7 7 8 6 17 15 19 25-4-4-7-9-10-13l-1 1-5-5c-4-4-10-10-15-13l-4-2c-2-2-4-4-7-4 0-1 1-2 1-3h0l-5-2h0l-9-5-1-1z" class="M"></path><path d="M576 394c5 3 11 6 16 10 6 5 10 10 15 16l-1 1-5-5c-4-4-10-10-15-13l-4-2c-2-2-4-4-7-4 0-1 1-2 1-3h0z" class="L"></path><defs><linearGradient id="AK" x1="590.039" y1="446.73" x2="607.461" y2="418.27" xlink:href="#B"><stop offset="0" stop-color="#7e7c7b"></stop><stop offset="1" stop-color="#c4c2c1"></stop></linearGradient></defs><path fill="url(#AK)" d="M575 397c3 0 5 2 7 4l4 2c5 3 11 9 15 13l5 5c3 4 8 8 9 13l-3 9v2h-1c-2 5-4 12-4 17l-3-12-2-7-1-2c-5-14-11-32-23-42l-3-2z"></path><path d="M604 440c1 3 2 7 0 10l-2-7c1-1 1-2 2-3z" class="B"></path><path d="M601 416l5 5c3 4 8 8 9 13l-3 9c-2-2-2-2-2-4h0c0-4 0-10-3-13 0-1 0-1-1-2-2-2-5-5-5-8z" class="Q"></path><path d="M575 397c3 0 5 2 7 4 1 1 1 2 2 3h1l4 4 4 7h1c5 8 7 16 10 25-1 1-1 2-2 3l-1-2c-5-14-11-32-23-42l-3-2z" class="I"></path><path d="M565 382c2-1 4-3 5-2 18 5 34 16 47 29 1 1 3 3 4 5v2 1c-1 1-2 1-4 1-3 0-6-3-8-5-3-3-6-4-9-6l-1 1h-1c-3-2-5-5-7-7-4-4-10-6-14-9-1 0-3-1-4-1-3-2-8-5-12-5l2-1c0-1 0-1 1-2l1-1z" class="F"></path><defs><linearGradient id="AL" x1="565.717" y1="387.209" x2="575.283" y2="388.791" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#8b8887"></stop></linearGradient></defs><path fill="url(#AL)" d="M561 386l2-1c0-1 0-1 1-2 1 2 3 2 5 3 5 2 10 4 15 7 1 1 3 2 4 3-1 0-1 1 0 1-4-1-8-5-11-5-1 0-3-1-4-1-3-2-8-5-12-5z"></path><defs><linearGradient id="AM" x1="593.94" y1="404.675" x2="599.251" y2="401.334" xlink:href="#B"><stop offset="0" stop-color="#747473"></stop><stop offset="1" stop-color="#8f8b8a"></stop></linearGradient></defs><path fill="url(#AM)" d="M577 392c3 0 7 4 11 5-1 0-1-1 0-1l27 18c1 0 1 0 2-1v-4c1 1 3 3 4 5v2 1c-1 1-2 1-4 1-3 0-6-3-8-5-3-3-6-4-9-6l-1 1h-1c-3-2-5-5-7-7-4-4-10-6-14-9z"></path><defs><linearGradient id="AN" x1="399.869" y1="229.118" x2="328.292" y2="284.997" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2b2b"></stop></linearGradient></defs><path fill="url(#AN)" d="M392 216l2 2c0 3 3 5 5 8 3 4 7 8 9 12 2 2 3 4 4 6s4 4 4 6v1c-12-5-23-6-35-1-10 4-18 12-24 21-2 3-3 6-5 9v-1l-1 1c0 1 0 1-1 1v1 1h0c-1 1-1 1-1 2v1 1l-1 1v1l-1 1h0c0 1 0 1-1 2v1 1c-1 0 0 0-1 1v1 1h0-1c0-4 3-9 4-13 1-2 2-3 1-4-1 2-2 5-3 7v2h-1c0 3-1 6-3 8v-1c-3-6-16-12-21-15 4-8 8-15 12-23 5-9 12-17 20-24 9-9 23-16 36-15l3-3z"></path><path d="M400 231c3 3 5 6 8 7 2 2 3 4 4 6-4-2-8-6-10-9l-2-4z"></path><path d="M392 216l2 2c0 3 3 5 5 8 3 4 7 8 9 12-3-1-5-4-8-7-4-4-6-9-11-12l3-3z" class="T"></path><path d="M618 218c18 2 35 12 47 26 6 8 11 16 16 24 2 4 5 9 7 13-7 3-18 8-22 15-1 2-1 4-1 5l-3 10-2-2c1-3 4-8 3-11l-3-6c-2-5-3-9-6-13-8-13-21-28-37-31h-1c-7-2-16-2-23-1h-3l-1 1h0l1-1c1-1 2-2 3-2 6-3 15-16 19-21 2-2 2-5 5-6h1z"></path><path d="M624 252l2 2c2 0 3 1 5 2v1c13 8 22 21 28 34 0 1 3 8 3 9l1-2c1 3-2 8-3 11l-2 6c-1 2-2 4-2 6l-7 20-4 10v1l-17 4h0c-7 2-15 2-23 2-3 0-7 0-10-1-1 0-1 1-1 1l-2 2c1 3 7 8 9 10 1 1 2 1 2 2l1 1c0 1 0 1 1 2 0 1 1 2 1 3v1c-1 2 1 3 1 5-1 0-2 0-3-1 1 1 2 2 3 2h1v-2h1c4 4 8 6 12 9h0c-1 0-2 0-2-1l-1 1c2 1 3 2 6 2v1c-4 1-8 1-12 2h-6c4 2 8 5 11 9 2 1 4 5 6 7-1 0-1 0-2 1-1-2-3-4-4-5-13-13-29-24-47-29-1-1-3 1-5 2l-1-1 10-7c20-15 36-39 39-65 1-9 1-17-1-26v-1l1-1 1 1 1 12h1v-2s0-1 1-2h0 1 0l1-1c3-9 3-17 4-26v-1-4l1-6z" class="K"></path><path d="M624 252l2 2c2 0 3 1 5 2-1 2 0 7-1 10l-4-2-1-2-1 1-1-1v-4l1-6z" class="G"></path><path d="M624 252l2 2-1 8-1 1-1-1v-4l1-6z"></path><path d="M611 331c1-1 1-2 2-3l2-2 2-2c1 0 1-1 2-1h0l2-1h1 1 2c1-1 1-1 3-1h0c2-1 7 0 10 0h0c2 0 3 0 4 1 1 0 3 0 5-1h5v1c-4 1-8 1-12 1-3-1-6 0-9 0-5 1-12 2-16 5-2 1-3 3-5 4l1-1z" class="C"></path><path d="M599 379l-2-2h-1v-1l1-1c-1-4-2-5-4-9-1-1-2-3-3-4 2 1 5 3 7 5l3 3h1c1 1 2 1 2 2l1 1c0 1 0 1 1 2 0 1 1 2 1 3v1c-1 2 1 3 1 5-1 0-2 0-3-1l-3-2c-1 0-1-1-2-2z" class="V"></path><path d="M623 262l1 1 1-1 1 2 4 2c-1 10-3 20-8 30-2 5-5 10-7 15h-1v-2h-1c1-9 1-17-1-26v-1l1-1 1 1 1 12h1v-2s0-1 1-2h0 1 0l1-1c3-9 3-17 4-26v-1z" class="I"></path><path d="M623 262l1 1 1-1 1 2-12 45h-1c1-9 1-17-1-26v-1l1-1 1 1 1 12h1v-2s0-1 1-2h0 1 0l1-1c3-9 3-17 4-26v-1z" class="T"></path><path d="M585 367l2-2h1l-1 1c-1 3-1 5-1 8l1 1c1-2 1-5 3-6 1 0 1 0 1 1 2 5 3 7 8 9 1 1 1 2 2 2l3 2c1 1 2 2 3 2h1v-2h1c4 4 8 6 12 9h0c-1 0-2 0-2-1l-1 1c2 1 3 2 6 2v1c-4 1-8 1-12 2h-6l-3-3c-10-7-20-12-31-16 4-3 9-7 13-11z" class="C"></path><path d="M585 367l2-2h1l-1 1c-1 3-1 5-1 8l1 1v1l-2 2c-3-3 1-7 0-11z" class="F"></path><path d="M607 385h1v-2h1c4 4 8 6 12 9h0c-1 0-2 0-2-1l-1 1c2 1 3 2 6 2v1c-4 1-8 1-12 2h-6l-3-3h9c1 0 2 1 3 1h7c-1-1-1-1-2-1h-3-1l-2-1c-2 0-6-2-6-4s0-2-1-4z" class="V"></path><path d="M652 322c1-1 2-1 4-1l-7 20-4 10v1l-17 4h0c-7 2-15 2-23 2-3 0-7 0-10-1 1-1 1-2 3-3 3-2 10 1 14-1h-12v-1-2l8-14c3-4 9-8 14-9h0c4-2 9-2 13-2 2-1 4-1 7-1 1-1 3 0 5-1 1 0 3 0 5-1z" class="H"></path><path d="M622 327h0c4-2 9-2 13-2-1 1-2 1-4 2h0c-4 1-8 3-12 5l-3-1c2-2 4-2 6-4h0z" class="N"></path><path d="M600 350l8-14c3-4 9-8 14-9h0c-2 2-4 2-6 4l3 1c-7 4-13 9-16 17-1 1-1 1-2 1h-1z" class="B"></path><path d="M619 344c-2 1-4 3-6 4 0 0-1 0-1-1-3 2-6 2-9 2l4-4v-1c2-2 3-4 5-6 3-3 7-5 10-7 0 2 1 2 0 4-3 1-5 2-7 3l2 2c1 0 1 0 2-1h2l-2 2h0 0v3z" class="W"></path><path d="M615 338l2 2c1 0 1 0 2-1h2l-2 2h0 0c-1 1-2 1-3 2-2 2-4 3-6 5 1-2 2-4 4-5 1 0 2-1 2-1-1-1-2 0-3-1v-1l2-2z" class="F"></path><path d="M600 352c2 0 4-2 7-2 2 0 5 0 7-1s4-4 7-5c4 0 7 1 11 3-1 1-2 1-4 1h0l-1 1s-1 0-1 1h-4l-1 1c1 1 1 1 2 1-1 1-2 1-3 1h-2c-1 0-5-1-6 0h-12v-1z" class="I"></path><path d="M632 347c2 1 5 1 7 3h-1c-1 0-2 1-3 1 3 0 5-1 7 0 0 1 0 1-1 1-8 2-15 3-24 3-2 0-5 1-7 0h-3c-2 0-8 1-9-1 3-2 10 1 14-1 1-1 5 0 6 0h2c1 0 2 0 3-1-1 0-1 0-2-1l1-1h4c0-1 1-1 1-1l1-1h0c2 0 3 0 4-1z" class="E"></path><path d="M632 347c2 1 5 1 7 3h-1c-1 0-2 1-3 1s-1 1-2 1l-1-1c-2 0-3 0-5 1h0-1v-1c-2 0-2 0-4-1h4c0-1 1-1 1-1l1-1h0c2 0 3 0 4-1z" class="C"></path><defs><linearGradient id="AO" x1="650.684" y1="338.701" x2="627.388" y2="324" xlink:href="#B"><stop offset="0" stop-color="#bbb9b8"></stop><stop offset="1" stop-color="#e7e5e4"></stop></linearGradient></defs><path fill="url(#AO)" d="M652 322c1-1 2-1 4-1l-7 20-4 10c-4-1-8-4-12-5-2-1-4-2-6-2-2-1-4-1-6-2 0 1-1 1-2 2v-3h0 0l2-2h-2c-1 1-1 1-2 1l-2-2c2-1 4-2 7-3 1-2 0-2 0-4 3-1 6-3 9-4h0c2-1 3-1 4-2 2-1 4-1 7-1 1-1 3 0 5-1 1 0 3 0 5-1z"></path><path d="M622 335l-2 2h0 1 0 1l2-2 1 1c-2 1-3 2-4 3h-2c-1 1-1 1-2 1l-2-2c2-1 4-2 7-3z" class="C"></path><path d="M621 339c1-1 2-2 4-3 1 1 1 2 1 4-1 0-1 0-1 1 1 0 1 1 2 1 3 0 4 2 6 4-2-1-4-2-6-2-2-1-4-1-6-2 0 1-1 1-2 2v-3h0 0l2-2z" class="D"></path><defs><linearGradient id="AP" x1="660.543" y1="303.265" x2="614.281" y2="300.564" xlink:href="#B"><stop offset="0" stop-color="#c5c4c3"></stop><stop offset="1" stop-color="#f9f8f8"></stop></linearGradient></defs><path fill="url(#AP)" d="M631 257c13 8 22 21 28 34 0 1 3 8 3 9l1-2c1 3-2 8-3 11l-2 6c-1 2-2 4-2 6-2 0-3 0-4 1v-1h-5c-2 1-4 1-5 1-1-1-2-1-4-1h0c-3 0-8-1-10 0h0c-2 0-2 0-3 1h-2-1-1l-2 1h0c-1 0-1 1-2 1l-2 2-2 2c-1 1-1 2-2 3 0-4 1-7 2-10 1-4 2-9 5-13 0-1 1-2 1-3l4-9c3-6 5-12 6-19 2-7 3-13 2-20z"></path><path d="M618 308v1c-1 3-3 7-4 11 0 1-1 3 0 4 1-1 1-2 2-4h0c0-2 2-4 3-5l-1-1 1-1 1-1h2c2 1 2 1 4 1h6c1 1 1 1 1 2v1s0 1-1 1c0 1 0 1 1 2s2 1 4 1l1 1h0c-3 0-8-1-10 0h0c-2 0-2 0-3 1h-2-1-1l-2 1h0c-1 0-1 1-2 1l-2 2-2 2c-1 1-1 2-2 3 0-4 1-7 2-10 1-4 2-9 5-13z" class="N"></path><defs><linearGradient id="AQ" x1="651.038" y1="277.12" x2="643.64" y2="283.814" xlink:href="#B"><stop offset="0" stop-color="#7f7c7c"></stop><stop offset="1" stop-color="#a1a09e"></stop></linearGradient></defs><path fill="url(#AQ)" d="M631 257c13 8 22 21 28 34 0 1 3 8 3 9l1-2c1 3-2 8-3 11l-2 6h-1c0-1 0-1 1-2h0v-2h1v-1-1-1h1v-1-1-1h1c0-2-1-3-2-4l-2-4c0 3 0 5-1 7h-2l-1 1v-1l-1 1v1l-3-8c-2-5-8-14-12-18-3-1-5-2-8-3 2-7 3-13 2-20z"></path><defs><linearGradient id="AR" x1="587.707" y1="639.798" x2="244.499" y2="385.078" xlink:href="#B"><stop offset="0" stop-color="#9d9e9e"></stop><stop offset="1" stop-color="#e0dddc"></stop></linearGradient></defs><path fill="url(#AR)" d="M511 748c-14-28-25-57-37-85l-72-177-57-139-15-36c-3-8-7-16-9-23-1-3-2-5 0-7 5 3 18 9 21 15v1c2 6 39 95 39 95l125 306v-4 1l1-1 2 6c2-1 4-1 5-1 1 1 2 1 2 2 1 1 1 2 1 4v1l-1 5-3 5-2 32z"></path><path d="M507 694l2 6 2 3 2 6c1 1 2 2 3 2l-3 5-3-9c-2-3-3-6-4-9v-4 1l1-1z" class="T"></path><path d="M509 700c2-1 4-1 5-1 1 1 2 1 2 2 1 1 1 2 1 4v1l-1 5c-1 0-2-1-3-2l-2-6-2-3z" class="U"></path><path d="M511 703c0 1 1 1 2 1l2-1c1 0 1 1 1 1-1 2-1 4-2 5h-1l-2-6z" class="R"></path><path d="M461 407v2c0-1 1-2 1-3 3-4 6-8 10-11l6-4c17-10 43-14 61-10-1 1-3 1-5 1-5 2-11 2-16 3h2l1 2h-10-8c-3 1-6 2-8 3h1c3 2 8 2 12 3 15 2 28 0 43-5 5-2 9-5 13-7l1 1-1 1c-1 1-1 1-1 2l-2 1 1 1 9 5h0l5 2h0c0 1-1 2-1 3l3 2c12 10 18 28 23 42l-1 1 4 13c1 2 1 5 1 7 0 4-3 8-4 12l-17 47-11 31c-2 4-3 8-5 12v2h-1l-2 6-1 1v2l-1 1h0c-1 3-3 7-4 10-1 2-2 4-4 5 0 2-1 2-1 4-2-2-3-4-6-5v-1l-3-4-1-1v-1l-1 1h0c1 1 0 2 0 3h0c1 2 1 3 1 4-1 3 0 6-1 9-1-1-1-9-1-11h0l-1-11h0c0 3 1 9-1 12v-7h-1v25c-1-2-1-3-2-5-2 0-3-1-4-2 1 3 3 6 3 9 1 2 3 4 4 6 1 3-1 16-1 19l-2 7 4 2v1c-2 5-4 11-6 16l-1-1v1-1-5l-2 7c-1 1-1 2-3 3 1 1 1 2 1 3s1 1 2 1l-3 7-12 30v-1c0-2 0-3-1-4 0-1-1-1-2-2-1 0-3 0-5 1l-2-6-1 1v-1 4L381 392c1 0 2 1 3 1l5 13c1 2 2 5 3 6l3-4 1 1 2-2c12-10 24-18 39-23 4-2 7-3 12-1h2v-1l3-1 11 3 2 1c2 0 3 0 4 1l7-3c-1 2-3 5-6 6l-1 1h-1c-4 4-6 7-9 11 2-1 3-3 5-4 0 3-3 6-4 9l-1 1z" class="I"></path><path d="M476 450h7c1 1 0 2 1 3h0c0 2-2 4-3 5l-1 1v1c-2 1-3 2-5 2 0-4 4-5 5-9h0c0-1 0-1 1-2h-1 0-1c-1 0-2 0-3-1z" class="E"></path><path d="M449 531c1 2 1 2 1 4l2 2v1h0c1 1 1 1 1 2v1h1c0 1 0 1 1 2h-1c0 1 1 2 1 3v1c1 1 1 2 2 3l1 1v1-1c1 2 2 5 4 6v1h0c-1 1-1 0 0 1 0 1 0 1 1 2h0c1 0 2 1 3 2v1s1 1 1 2h1 0l-3 3c-2-2-2-4-4-6-3-8-7-14-10-22 0-2-1-5-2-7h0v-3z" class="B"></path><path d="M475 450c-1 1-1 0-1 1-1 1-1 2-2 3-1-1-1-3-2-5l-7-4c-6-3-11-11-13-17-2-10 0-20 5-28h1c-2 3-3 6-4 10-3 5-1 14 1 19 1 4 3 9 6 11 1 1 3 2 5 3 1 1 3 3 5 4h2c2 1 4 2 7 2-1 1-3 1-3 1z" class="J"></path><path d="M523 397c4 0 8-1 12-1 4-1 8-3 11-3s8 4 10 5 8 5 8 7c-2 1-3-3-5-3h0-1-1c-1-1-2 0-3 0l-1-1h-2c-1-1-2-1-3-1l-3-2c-1-1-2-1-3-2s-1-1-2-1c-1 1-1 1-2 1h-2v1h-3-2l-1 1c-1 0-5 0-7-1z" class="B"></path><path d="M539 520s-1-1-1-2h1v-8-18l-3 6c-1-1 4-11 5-13v42-7l-1 1c-1 3 0 7 0 11v19c0 3 0 6-1 9v-40z" class="L"></path><path d="M540 532c0-4-1-8 0-11l1-1v7l1 23c0 3-1 7 0 9l-1 19h0c0 3 1 9-1 12v-7h-1v-23c1-3 1-6 1-9v-19z"></path><path d="M540 532c0-4-1-8 0-11l1-1v7l1 23c0 3-1 7 0 9l-1 19h0v-32c0-4 1-12-1-16v2z" class="J"></path><path d="M536 601h1v1l1-1v-1l-1-1c0-6-1-11 0-17 1-2 1-4 1-6 0-4 1-31 0-33h0v-1-2c0 4 0 8-1 12h0v-15c0-2-1-4 0-6v-2c0-1 0-1 1-1-2-4 0-3 0-6l-1-1h1v-1h1v40 23 25c-1-2-1-3-2-5l-1-2z" class="C"></path><path d="M465 384l2 1c2 0 3 0 4 1-4 2-7 4-9 6s-4 5-5 6c0-2 1-3 2-4v-1h0c-1 1-2 1-3 1-2 1-5 2-7 3-8 5-16 12-23 20-2 2-3 5-5 7 1-3 3-6 5-8 6-7 12-13 20-18 4-3 9-5 13-9l-4 2c-3 1-6 2-8 3l-1-1c3-2 7-4 11-5 1-1 1-1 2-1l-2-1h0 4 1c2 0 2 0 3-2z" class="U"></path><path d="M466 406c3-4 7-7 11-9 3-1 6-4 9-4 5-1 12 2 17 3 3 1 6 1 9 1l-1 1h-1-1c-1 0-2 0-3-1-2 0-4 0-5 1h-2c-1 0-1 0-2 1h-2c-2 0-2 0-2 1h-2c-2 0-3 0-4 1h-1 0c-2 0-3 0-3 1-2 0-4-1-5 0h-3l-1 1h-1v-1l-1 1h-1c-1 1-2 1-3 2l-2 1z" class="N"></path><path d="M494 528c-1-4-3-8-4-12-3-7-8-14-7-21l26 54v3l-4-5c-1-3-3-6-4-9-2-4-5-7-7-10z" class="S"></path><defs><linearGradient id="AS" x1="491.417" y1="499.546" x2="476.599" y2="524.262" xlink:href="#B"><stop offset="0" stop-color="#575555"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#AS)" d="M479 510c-1-8-1-17 0-25l4 10c-1 7 4 14 7 21 1 4 3 8 4 12-1-1-2-1-3-2h-1c-1-1-1-1-2-1 0 1 1 2 1 3h0l1 3-1 2c-1-2-2-4-4-4-1-1-2-1-3-1v7l-3-43v18z"></path><path d="M437 499c-2-2-4-6-5-9-2-3-4-5-5-8-5-8-8-17-10-26h1c1 1 1 1 1 3h0c1 1 1 2 1 3l2 4v2c1 1 2 2 2 4l1 1v1h0c1 1 2 2 2 4h0c1 0 2 1 2 2h1v1c1 1 1 0 1 1v1c0 1 0 1 1 2v-1l1 1-1 1 1 1h0c0 1 0 1 1 2s1 2 1 3c1 0 1 1 2 2h1v-1 2c2-2 4-4 3-8v-1-2c-2-5-5-11-6-17v-1c-1-1-1-2-1-3h1c1 1 0 2 1 3v1l2 5c0 1 1 2 1 4l1 3c0 1 1 2 1 2l1 4c1 2 1 4 0 6-1 4-1 8-1 12l1 1h1v1l-1 2v2 1h0c1 2 2 3 3 4s0 0 0 1l2 2h-1c1 1 1 1 1 2v1c-1 1 0 3 0 4l1 1v3l-1 1c-1-2-1-4-2-6-1-4-2-5-3-7s-2-3-4-4c-1 0-1 1-2 1h-2c0-2 0-4 1-5 0-1 0-1 1-2v-1c1-1 0 0 1-2h0v-2-2z" class="B"></path><path d="M437 499l2-2h0c0 3 0 6-1 9v2l1 1h1 0l-2 3c-1 0-1 1-2 1h-2c0-2 0-4 1-5 0-1 0-1 1-2v-1c1-1 0 0 1-2h0v-2-2z" class="M"></path><path d="M466 406c-1 0-1 1-2 1 0-1 0 0 1-1 9-10 25-18 38-20 5-1 10-1 15-1h2l1 2h-10-8c-3 1-6 2-8 3h1c3 2 8 2 12 3 15 2 28 0 43-5v1c1 1 2 1 3 0l3 2h0l-3-1v2c2 2 5 3 7 6l-5-3c0 1 1 2 0 3-2-1-7-5-10-5s-7 2-11 3c-4 0-8 1-12 1h-11c-3 0-6 0-9-1-5-1-12-4-17-3-3 0-6 3-9 4-4 2-8 5-11 9z" class="J"></path><path d="M556 395l-8-3h-1c2-1 5-2 7-2v2c2 2 5 3 7 6l-5-3z" class="B"></path><path d="M495 390h1c3 2 8 2 12 3 15 2 28 0 43-5v1l-6 3c-12 3-23 5-35 4l-21-4 6-2z" class="G"></path><path d="M456 403c1-4 10-11 14-13-4 4-6 7-9 11 2-1 3-3 5-4 0 3-3 6-4 9l-1 1-1-1v1h-1 0c-4 9-4 16-1 25 2 5 6 10 11 12 6 3 11 2 17 4 8-2 14-6 18-14 1-2 2-4 2-6 1-3 0-6 0-9l-2-5h0c2 2 3 5 3 7v1c1 6-1 12-5 16-4 7-12 10-19 12h-7-1s2 0 3-1c-3 0-5-1-7-2h-2c-2-1-4-3-5-4-2-1-4-2-5-3-3-2-5-7-6-11-2-5-4-14-1-19 2-2 3-5 4-7z" class="X"></path><path d="M456 403c1-4 10-11 14-13-4 4-6 7-9 11 2-1 3-3 5-4 0 3-3 6-4 9l-1 1-1-1v1h-1 0-2c-2 3-2 7-3 10v5h-1c0-5 0-10 2-14 1-2 1-3 1-5h0z" class="D"></path><path d="M572 397c0 1 1 1 2 1s2 3 3 2l1-1c12 10 18 28 23 42l-1 1c0-2-1-3-1-4h-1c-1-1-1-2-2-4v-1l-1-2v-1c-1-1-1-2-1-2l-1-1-1-2c0 3 0 7-1 10v1c-1 2-2 5-2 8l-1 1v1l-1 2-1-1c-1 0-1-1 0-2h0c-1-2 0-9 0-12-1-1-1-3-1-4-1-2 0-3 0-4-1-2-1-2-1-4v-2h-1v-2-1l-2-2h-1-1c0 1 0 2-1 3 0-3 0-6-1-9v-1c0-1-1-3-2-4h0l-3-6z" class="B"></path><defs><linearGradient id="AT" x1="590.181" y1="410.242" x2="577.875" y2="410.717" xlink:href="#B"><stop offset="0" stop-color="#1b1716"></stop><stop offset="1" stop-color="#393e3e"></stop></linearGradient></defs><path fill="url(#AT)" d="M572 397c0 1 1 1 2 1s2 3 3 2l1-1c12 10 18 28 23 42l-1 1c0-2-1-3-1-4-2-6-5-12-8-18 0 6 0 13-2 18v-14c0-8-7-17-13-23l-1 2-3-6z"></path><defs><linearGradient id="AU" x1="431.948" y1="419.393" x2="424.082" y2="411.89" xlink:href="#B"><stop offset="0" stop-color="#d1cfcf"></stop><stop offset="1" stop-color="#fdfcfb"></stop></linearGradient></defs><path fill="url(#AU)" d="M447 394c2-1 5-2 8-3l4-2c-4 4-9 6-13 9-8 5-14 11-20 18-2 2-4 5-5 8-1 2-2 3-3 5l-5 7-1 3-1 3c0-1 0-1-1-1 0 1-1 2-1 4 0 0 0 1-1 1-4-6 4-13 4-19l4-6-1-1 2-3h0c-2 1-4 3-5 4l-1-1 17-16 18-11 1 1z"></path><path d="M446 393l1 1c-7 3-13 8-19 13-3 3-5 5-8 9-1 1-3 4-4 5l-1-1 2-3h0c-2 1-4 3-5 4l-1-1 17-16 18-11z" class="L"></path><path d="M564 381l1 1-1 1c-1 1-1 1-1 2l-2 1 1 1 9 5h0l5 2h0c0 1-1 2-1 3l3 2-1 1c-1 1-2-2-3-2s-2 0-2-1l3 6h0v2l1 1h0-1-1l1 1v3c-1-1 0-2-1-2v-1l-1-1c1 4 2 7 1 12h0c0 2-1 4-2 6-4 8-12 13-21 16h-1c3-1 5-2 7-4 5-3 11-8 12-15 0-5-1-11-5-15v-1c0-2-6-6-8-7 1-1 0-2 0-3l5 3c-2-3-5-4-7-6v-2l3 1h0l-3-2c-1 1-2 1-3 0v-1c5-2 9-5 13-7z" class="K"></path><path d="M554 390l3 1c4 2 8 5 10 9h-1c-1 0-2-1-4-1l-1-1c-2-3-5-4-7-6v-2z" class="C"></path><path d="M562 399c2 0 3 1 4 1h1 1c4 5 5 10 5 16 0 3-1 8-4 11 0-4 3-6 3-10-1-8-4-13-10-18z" class="N"></path><path d="M554 389c1-1 2-1 4-1 4 1 8 5 11 7 1 1 2 2 3 2l3 6h0v2l1 1h0-1-1l1 1v3c-1-1 0-2-1-2v-1l-1-1c-4-7-8-12-16-15l-3-2z" class="C"></path><path d="M564 381l1 1-1 1c-1 1-1 1-1 2l-2 1 1 1 9 5h0l5 2h0c0 1-1 2-1 3l3 2-1 1c-1 1-2-2-3-2s-2 0-2-1c-1 0-2-1-3-2-3-2-7-6-11-7-2 0-3 0-4 1s-2 1-3 0v-1c5-2 9-5 13-7z" class="L"></path><path d="M571 392l5 2h0c0 1-1 2-1 3l3 2-1 1c-1 1-2-2-3-2s-2 0-2-1c-1 0-2-1-3-2 2 0 2 0 3 1h1l-2-3v-1z" class="K"></path><path d="M454 381l11 3c-1 2-1 2-3 2h-1-4 0l2 1c-1 0-1 0-2 1-4 1-8 3-11 5l-18 11-17 16c-4 4-7 8-10 12v1c-2-3-4-7-5-10l3-3v-2l-3 4-4-9 4-4 2-2c12-10 24-18 39-23 4-2 7-3 12-1h2v-1l3-1z" class="V"></path><path d="M431 394l1 1-11 7c-5 3-10 7-15 11-2 2-4 5-7 7v-2c1-2 3-3 4-4 1-2 2-3 4-4 7-7 16-12 24-16z" class="H"></path><path d="M454 381l11 3c-1 2-1 2-3 2h-1-4 0c-10 1-16 4-25 9l-1-1c4-2 8-4 11-6-4 0-10 4-14 6-9 5-18 9-26 16-2 1-4 3-6 5l-1 1h0c0-1 2-3 3-4 9-7 18-14 29-19 7-4 14-7 22-10h2v-1l3-1z" class="K"></path><path d="M454 381l11 3c-1 2-1 2-3 2h-1-4c-2 0-3 0-4-1h3c-1-1-3-2-4-2l-1-1 3-1z" class="O"></path><path d="M398 407c12-10 24-18 39-23 4-2 7-3 12-1-8 3-15 6-22 10-11 5-20 12-29 19v-5z" class="F"></path><defs><linearGradient id="AV" x1="404.449" y1="432.505" x2="425.138" y2="391.762" xlink:href="#B"><stop offset="0" stop-color="#cccbcb"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AV)" d="M457 386l2 1c-1 0-1 0-2 1-4 1-8 3-11 5l-18 11-17 16c-4 4-7 8-10 12v1c-2-3-4-7-5-10l3-3c3-2 5-5 7-7 5-4 10-8 15-11l11-7c9-5 15-8 25-9z"></path><path d="M457 386l2 1c-1 0-1 0-2 1-4 1-8 3-11 5l-18 11v-1c2-1 5-3 6-5h-1l-1-1 1-1c-2 1-3 2-5 3-1 0-2 1-3 2l1 2c-2 1-1-1-3-1h-2l11-7c9-5 15-8 25-9z" class="I"></path><defs><linearGradient id="AW" x1="587.06" y1="515.05" x2="560.409" y2="504.53" xlink:href="#B"><stop offset="0" stop-color="#93918f"></stop><stop offset="1" stop-color="#efeeec"></stop></linearGradient></defs><path fill="url(#AW)" d="M589 444c0-3 1-6 2-8v-1c1-3 1-7 1-10l1 2 1 1s0 1 1 2v1l1 2v1c1 2 1 3 2 4h1c0 1 1 2 1 4l4 13c1 2 1 5 1 7 0 4-3 8-4 12l-17 47-11 31c-2 4-3 8-5 12v2h-1l-2 6-1 1v2l-1 1h0c-1 3-3 7-4 10-1 2-2 4-4 5 0 2-1 2-1 4-2-2-3-4-6-5v-1l-3-4-1-1v-1l-1 1h0c1 1 0 2 0 3h0c1 2 1 3 1 4-1 3 0 6-1 9-1-1-1-9-1-11h0l-1-11 1-19v3 17 1c1-1 1-2 1-2l3-5 2-4v-2c1-1 1-2 2-3l2-5 4-8 2-5 3-6 2-4 1-3 2-4v-2l5-10v-2l1-1v-2c1-1 1-2 2-3 0-1 1-3 1-4s1-3 2-4c0-1 1-3 1-4s1-2 1-3l1-1v-1c0-1 0-2 1-2v-2s1-1 1-2l4-12v-2c1-1 1-2 1-3 1-1 1-2 1-3v-2c1-1 1-2 1-3 1-1 0-4 0-6 1-1 1-1 1-2-1-2 0-4-1-6v-1h0v-2z"></path><path d="M381 392c1 0 2 1 3 1l5 13c1 2 2 5 3 6l3-4 1 1-4 4 4 9 3-4v2l-3 3c1 3 3 7 5 10v-1c3-4 6-8 10-12l1 1c1-1 3-3 5-4h0l-2 3 1 1-4 6c0 6-8 13-4 19 1 0 1-1 1-1 0-2 1-3 1-4 1 0 1 0 1 1v1c1 0 0 1 0 2v4l1 2 2-2-1-1c1-1 1-2 2-3h0c2-5 5-9 8-14h0l-3 7h0c0 1 0 2-1 3v1h0l-1 2h0v1l-1 1v1h0v1c1 1 1 0 1 2l1 2v2 1l-1 1h-1c2 9 5 18 10 26 1 3 3 5 5 8 1 3 3 7 5 9v2 2h0c-1 2 0 1-1 2v1c-1 1-1 1-1 2-1 1-1 3-1 5h2c1 0 1-1 2-1 2 1 3 2 4 4s2 3 3 7c1 2 1 4 2 6l1-1c1 1 1 2 1 3h0v3h0c1 2 2 5 2 7 3 8 7 14 10 22 2 2 2 4 4 6l3-3h0c2-1 3-2 4-2 2 0 2 0 2 2 0 1 0 1-1 3h0l-1 2c0 3-1 3-2 6v4 3 1h1 0c1-1 0-1 1-1 2-4 5-8 6-12 1-6 1-13 1-19v-43-18l3 43v-7c1 0 2 0 3 1 2 0 3 2 4 4l1-2-1-3h0c0-1-1-2-1-3 1 0 1 0 2 1h1c1 1 2 1 3 2 2 3 5 6 7 10 1 3 3 6 4 9l4 5v-3l27 52 1 2c-2 0-3-1-4-2 1 3 3 6 3 9 1 2 3 4 4 6 1 3-1 16-1 19l-2 7 4 2v1c-2 5-4 11-6 16l-1-1v1-1-5l-2 7c-1 1-1 2-3 3 1 1 1 2 1 3s1 1 2 1l-3 7-12 30v-1c0-2 0-3-1-4 0-1-1-1-2-2-1 0-3 0-5 1l-2-6-1 1v-1 4L381 392z"></path><path d="M476 579l3-6v13l-1-2-1-1c-1-1-1-3-1-4z" class="C"></path><path d="M410 441c1 0 1 0 1 1v1c1 0 0 1 0 2v4l1 2 2-2-1-1c1-1 1-2 2-3 0 2-2 15-3 16l-1-2h0v-3-1-1c-1-1-1-1-1-2-1-3 1-7 0-10v-1z" class="N"></path><path d="M411 459l-7-17c2-5 5-11 8-15 0 6-8 13-4 19 1 0 1-1 1-1 0-2 1-3 1-4v1c1 3-1 7 0 10 0 1 0 1 1 2v1 1 3z" class="W"></path><path d="M412 421c1-1 3-3 5-4h0l-2 3c-4 6-9 13-11 20-1-1-2-2-2-3-1-2-1-3 1-5 2-4 5-8 9-11z" class="N"></path><path d="M468 566c2-1 3-2 4-2 2 0 2 0 2 2 0 1 0 1-1 3h0c-4 3-8 9-11 13l-3-6 9-9v-1h0z" class="D"></path><path d="M472 571c0 3-1 3-2 6v4 3 1h1 0c1-1 0-1 1-1l-5 11c-2-4-4-8-5-12l10-12z" class="B"></path><defs><linearGradient id="AX" x1="470.427" y1="602.718" x2="482.573" y2="598.457" xlink:href="#B"><stop offset="0" stop-color="#a19e9d"></stop><stop offset="1" stop-color="#e2e2e2"></stop></linearGradient></defs><path fill="url(#AX)" d="M476 579c0 1 0 3 1 4l1 1 1 2v30l8-13c0 2 0 3 1 4l-1 1c0 1-1 2-2 4h-1 0v-2c-2 1-2 4-3 6v2c1 3-1 3-1 6-1 1 1 5 2 6h1 4v-1h1c-1-2 0-3-1-4s-3 0-4-2c0-1 0-1 1-2 1 1 1 1 1 2 1 1 1 1 2 1l1 2v4 1c-2 0-4-1-6 1l-14-35-1-1c0-2 3-5 4-7l5-10z"></path><defs><linearGradient id="AY" x1="419.519" y1="481.499" x2="427.174" y2="477.911" xlink:href="#B"><stop offset="0" stop-color="#a6a4a4"></stop><stop offset="1" stop-color="#c9c7c5"></stop></linearGradient></defs><path fill="url(#AY)" d="M420 438c0 1 0 2-1 3v1h0l-1 2h0v1l-1 1v1h0v1c1 1 1 0 1 2l1 2v2 1l-1 1h-1c2 9 5 18 10 26 1 3 3 5 5 8 1 3 3 7 5 9v2c-1 1-1 1-2 3-1 1-2 1-2 4h1l-2 2-12-28c-2-6-5-12-7-18 0-2 0-4 1-6 0-7 2-14 6-20z"></path><path d="M437 501v2h0c-1 2 0 1-1 2v1c-1 1-1 1-1 2-1 1-1 3-1 5h2c1 0 1-1 2-1 2 1 3 2 4 4s2 3 3 7c1 2 1 4 2 6l1-1c1 1 1 2 1 3h0v3h0c1 2 2 5 2 7 3 8 7 14 10 22 2 2 2 4 4 6l-2 2-4 4-27-65 2-2h-1c0-3 1-3 2-4 1-2 1-2 2-3z" class="D"></path><path d="M438 512c2 1 3 2 4 4s2 3 3 7c1 2 1 4 2 6l1-1c1 1 1 2 1 3h0v3h0c1 2 2 5 2 7 3 8 7 14 10 22 2 2 2 4 4 6l-2 2-3-3v-1l-1-2c0-1 0-1-1-2-1-2-3-5-4-8l-1-1c0-1 0-1-1-2h0l-1-2v-2c-1-1-1-2-2-2 0-2-1-4-2-6h0v-2c-1-1-1-1-1-2-1-1-1-1-1-2l-1-3s-1-1-1-2h0v-1c-1-2-1-1-1-2v-3c-1-1-1-1-1-2v-2-1c-1-1-2 0-4-1v-1c0-1-1-1-1-3 1 0 1-1 2-1z" class="V"></path><defs><linearGradient id="AZ" x1="501.936" y1="549.726" x2="467.46" y2="574.217" xlink:href="#B"><stop offset="0" stop-color="#bebebe"></stop><stop offset="1" stop-color="#fefdfc"></stop></linearGradient></defs><path fill="url(#AZ)" d="M490 531l-1-3h0c0-1-1-2-1-3 1 0 1 0 2 1h1c1 1 2 1 3 2 2 3 5 6 7 10 1 3 3 6 4 9l4 5v1l-5 9-10 20c0 1-2 4-2 5-2 3-3 5-4 8-3 4-5 9-7 14l1-74v-7c1 0 2 0 3 1 2 0 3 2 4 4l1-2z"></path><path d="M490 531l1 3c1 1 1 1 1 2v1l3 4h0c0 1 1 1 1 2 1 1 0 0 1 2l1 2 1 1 1 1c-1 1-1 1-2 1-1-2-1-2-2-3l-1-1c0-1 0-2-1-3l-1-1c-1-1-2-2-3-4 0-1 0-3-1-5h0l1-2z" class="M"></path><path d="M490 531l-1-3h0c0-1-1-2-1-3 1 0 1 0 2 1h1c1 1 2 1 3 2 2 3 5 6 7 10 1 3 3 6 4 9l4 5v1l-5 9v-1c-1-1-1-2-1-3s0-2-1-3h0c0-1 0-1-1-2h0 1c0-2-1-3-2-5v-1l-1 1-1-1-1-2c-1-2 0-1-1-2 0-1-1-1-1-2h0l-3-4v-1c0-1 0-1-1-2l-1-3z" class="P"></path><defs><linearGradient id="Aa" x1="522.364" y1="685.433" x2="506.024" y2="677.656" xlink:href="#B"><stop offset="0" stop-color="#686665"></stop><stop offset="1" stop-color="#a09e9c"></stop></linearGradient></defs><path fill="url(#Aa)" d="M487 640h1l-3-2c-1 0-1-1-2-1l1-1s0-1 1 0c0 0 1 1 2 1 1 1 2 1 3 2l2 1c2 1 4 2 6 1 13 6 24 16 32 27 0 1 1 1 2 1l-3 7-12 30v-1c0-2 0-3-1-4 0-1-1-1-2-2-1 0-3 0-5 1l-2-6-4-10c-1-1-9-21-10-23v-1c0-2-3-6-3-7l-2-5c0-1-3-7-3-8v-1l2 1z"></path><defs><linearGradient id="Ab" x1="510.798" y1="675.038" x2="492.102" y2="669.258" xlink:href="#B"><stop offset="0" stop-color="#9a9896"></stop><stop offset="1" stop-color="#b7b5b3"></stop></linearGradient></defs><path fill="url(#Ab)" d="M493 661h13 7c1 0 2 1 3 2-1 0-2 0-3 1 0 1 0 3-1 5v1c-1 5-2 10-5 14v1l-2 2c-1-1-1-2-2-3s-9-21-10-23z"></path><defs><linearGradient id="Ac" x1="511.65" y1="665.577" x2="518.363" y2="652.9" xlink:href="#B"><stop offset="0" stop-color="#222223"></stop><stop offset="1" stop-color="#403f3e"></stop></linearGradient></defs><path fill="url(#Ac)" d="M487 640h1l-3-2c-1 0-1-1-2-1l1-1s0-1 1 0c0 0 1 1 2 1 1 1 2 1 3 2l2 1c2 1 4 2 6 1 13 6 24 16 32 27 0 1 1 1 2 1l-3 7-12 30v-1c0-2 0-3-1-4 0-1-1-1-2-2h3c3-3 8-17 9-22-2-6-5-10-10-14-1-1-2-2-3-2h-7-13v-1c0-2-3-6-3-7l-2-5c0-1-3-7-3-8v-1l2 1z"></path><path d="M504 648l6 4v1h-5s-2-1-3-1l2-1v-3z" class="B"></path><defs><linearGradient id="Ad" x1="517.15" y1="669" x2="515.968" y2="653.991" xlink:href="#B"><stop offset="0" stop-color="#83807f"></stop><stop offset="1" stop-color="#b3b1b1"></stop></linearGradient></defs><path fill="url(#Ad)" d="M510 652c6 4 10 8 15 14 1 2 2 3 2 5 1 2 0 3 0 5l-6-9c-4-6-10-10-16-14h5v-1z"></path><path d="M488 648c0-1-3-7-3-8v-1l2 1c3 2 6 3 9 4s6 3 8 4v3l-2 1c-4-2-9-3-14-4z" class="C"></path><path d="M488 648c5 1 10 2 14 4 1 0 3 1 3 1 6 4 12 8 16 14l6 9h2l-12 30v-1c0-2 0-3-1-4 0-1-1-1-2-2h3c3-3 8-17 9-22-2-6-5-10-10-14-1-1-2-2-3-2h-7-13v-1c0-2-3-6-3-7l-2-5z" class="J"></path><path d="M490 653h5c6 1 12 4 18 7h-20c0-2-3-6-3-7z" class="P"></path><defs><linearGradient id="Ae" x1="524.863" y1="639.62" x2="494.834" y2="599.731" xlink:href="#B"><stop offset="0" stop-color="#989694"></stop><stop offset="1" stop-color="#f4f4f3"></stop></linearGradient></defs><path fill="url(#Ae)" d="M509 549l27 52 1 2c-2 0-3-1-4-2 1 3 3 6 3 9 1 2 3 4 4 6 1 3-1 16-1 19l-2 7 4 2v1c-2 5-4 11-6 16l-1-1v1-1-5l-2 7c-1 1-1 2-3 3 1 1 1 2 1 3-8-11-19-21-32-27-5-3-12-5-16-9 2-2 4-1 6-1v-1-4l-1-2c-1 0-1 0-2-1 0-1 0-1-1-2-1 1-1 1-1 2 1 2 3 1 4 2s0 2 1 4h-1v1h-4-1c-1-1-3-5-2-6 0-3 2-3 1-6v-2c1-2 1-5 3-6v2h0 1c1-2 2-3 2-4l1-1c-1-1-1-2-1-4l3-5c0-3 1-5 2-7h-1c1-1 1-2 1-3v-1c0-1 2-4 2-5l10-20 5-9v-1-3z"></path><path d="M534 608l2 2c1 2 3 4 4 6 1 3-1 16-1 19l-2 7c-3-2-8-4-12-6h4 0 1c3-2 5-4 6-7h0c1-4 1-7 2-11 0-1 0 0-1-1v-3l-1-1h0c-1-2-2-3-2-5z" class="G"></path><path d="M487 624c4 0 7 1 10 2 10 2 19 6 28 10 4 2 9 4 12 6l4 2v1c-2 5-4 11-6 16l-1-1v1-1-5l-2 7c-1 1-1 2-3 3 1 1 1 2 1 3-8-11-19-21-32-27-5-3-12-5-16-9 2-2 4-1 6-1v-1-4l-1-2z" class="O"></path><path d="M504 631l-11-4c-1 0-3-1-3-2 2 0 4 1 6 1 6 2 11 4 17 6l-2 1c-1-1-4-1-5-1-1-1-1-1-2-1h0z" class="C"></path><path d="M504 631h0c1 0 1 0 2 1 1 0 4 0 5 1l2-1 19 9h0c-1 1-1 0-2 0l-2-1c-2-1-9-2-11-1h0c-2 0-6-2-7-3-2-1-2-2-4-3-1 0-2-1-2-1v-1z" class="V"></path><path d="M517 639c2-1 9 0 11 1l2 1c1 0 1 1 2 0h0c2 1 5 2 5 4h0-1 0l1 1c-1 1 0 1-1 1 1 2 0 2 0 4h-1c-3-1-6-2-9-4h2c1 0 3 0 5-1-5-3-10-5-16-7z" class="G"></path><path d="M488 626c5 2 10 4 16 6 0 0 1 1 2 1 2 1 2 2 4 3 1 1 5 3 7 3h0c6 2 11 4 16 7-2 1-4 1-5 1h-2l-6-2-1 1-4-1c-2-1-3-1-5-1l-22-13v-1-4z" class="B"></path><path d="M488 630c5 2 32 15 32 15l-1 1-4-1c-2-1-3-1-5-1l-22-13v-1z" class="L"></path><defs><linearGradient id="Af" x1="501.15" y1="649.339" x2="505.296" y2="638.493" xlink:href="#B"><stop offset="0" stop-color="#c1c0c0"></stop><stop offset="1" stop-color="#efedec"></stop></linearGradient></defs><path fill="url(#Af)" d="M482 632c2-2 4-1 6-1l22 13c2 0 3 0 5 1l4 1 15 6 1 1v1c-1 1-1 0-1 1l-2 7c-1 1-1 2-3 3 1 1 1 2 1 3-8-11-19-21-32-27-5-3-12-5-16-9z"></path><path d="M510 644c2 0 3 0 5 1l4 1 15 6 1 1v1c-1 1-1 0-1 1l-2 7c-1 1-1 2-3 3-1 0-5-7-6-9-4-4-8-8-13-12z" class="R"></path><path d="M524 652l-1-1c2 0 4 1 5 1 2 0 4 0 5 1l1-1 1 1v1c-1 1-1 0-1 1l-2 7v-6c-3-2-5-3-8-4z" class="S"></path><path d="M510 644c2 0 3 0 5 1l4 1 15 6-1 1c-1-1-3-1-5-1-1 0-3-1-5-1l1 1h-2c1 1 1 2 2 3l-1 1c-4-4-8-8-13-12z" class="H"></path><path d="M509 549l27 52 1 2c-2 0-3-1-4-2 1 3 3 6 3 9l-2-2-12-22v3l-1 1c-1 1-1 2-2 3l-1 1-1 1h-1l-2 2-3 3s0 1-1 1c-1 2-2 3-3 3v1 1c-1 0-1 0-2 1h0c-2 0-2 1-4 2-1 0-3 1-4 1-1 1-2 1-3 1-2-2-4-1-5-5v-1l-1 2c-1-1-1-2-1-4l3-5c0-3 1-5 2-7h-1c1-1 1-2 1-3v-1c0-1 2-4 2-5l10-20 5-9v-1-3z" class="E"></path><path d="M508 562c2 0 2 0 4-1 1 2 1 3 2 5l-2 1-2-4h-1c-3 8-8 16-12 24-2 3-4 8-7 11 0-3 1-5 2-7l16-29z" class="S"></path><defs><linearGradient id="Ag" x1="499.92" y1="599.683" x2="528.445" y2="571.975" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242323"></stop></linearGradient></defs><path fill="url(#Ag)" d="M509 549l27 52 1 2c-2 0-3-1-4-2 1 3 3 6 3 9l-2-2-12-22c-3-7-7-13-10-19l2-1c-1-2-1-3-2-5-2 1-2 1-4 1l-16 29h-1c1-1 1-2 1-3v-1c0-1 2-4 2-5l10-20 5-9v-1-3z"></path><path d="M508 562c1-2 1-2 2-3 1 0 1 1 2 2-2 1-2 1-4 1z" class="U"></path><path d="M512 567l2-1 19 35h0c1 3 3 6 3 9l-2-2-12-22c-3-7-7-13-10-19z" class="R"></path></svg>