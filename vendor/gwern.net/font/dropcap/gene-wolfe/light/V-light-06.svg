<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="102 112 834 872"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#cecdcc}.C{fill:#aeadaa}.D{fill:#a9a8a6}.E{fill:#c2c1bf}.F{fill:#656563}.G{fill:#8f8e8b}.H{fill:#c8c7c6}.I{fill:#b4b3b3}.J{fill:#a3a2a0}.K{fill:#383837}.L{fill:#5e5e5b}.M{fill:#838380}.N{fill:#989795}.O{fill:#535351}.P{fill:#71706e}.Q{fill:#494947}.R{fill:#bbbab8}.S{fill:#d3d2d1}.T{fill:#d8d7d5}.U{fill:#1a1a19}.V{fill:#3f3f3e}.W{fill:#939390}.X{fill:#7a7a77}.Y{fill:#c1c0be}.Z{fill:#dedddb}.a{fill:#767571}.b{fill:#323232}.c{fill:#272726}.d{fill:#9e9e99}.e{fill:#575756}.f{fill:#212121}.g{fill:#b7b6b2}.h{fill:#83827f}.i{fill:#6b6b6a}.j{fill:#858582}.k{fill:#e3e2e1}.l{fill:#9c9c9b}.m{fill:#2c2c2c}.n{fill:#444544}.o{fill:#ecebea}.p{fill:#080808}.q{fill:#4e4e4c}.r{fill:#131312}.s{fill:#e8e7e5}.t{fill:#2f302d}.u{fill:#252523}.v{fill:#f4f3f2}.w{fill:#fffffe}</style><path d="M551 353l1-7c2 2 3 3 4 5l-1 2h-1v-2c-1 0-2 2-3 2z" class="P"></path><path d="M412 804c1 1 1 2 2 2h1v1h1c1 0 2 0 3-1 2-1 3-1 4-2-1 2-2 2-3 4 0 1 0 2-1 3h-2c-1 0-3-1-4-3-1-1-1-3-1-4z" class="L"></path><path d="M354 618c1-1 2-1 2-1 2 1 3 2 5 2l2 2-1 1h-1l-3 3h-2c-1 0-2-1-2-1 0-2-1-3 0-4v-2z" class="b"></path><path d="M664 238c1 0 2 0 3-1s2 0 3 0l3 5c1 2 1 6 1 9-1 1-1 1-2 1s-1-1-2-2c1 0 1-1 2-1 0-1 1-2 0-3-1-3-5-6-8-8z" class="P"></path><path d="M744 419l7-5h0 1 1c-1 4-3 8-3 12h0c-2-2-4-5-6-7z" class="u"></path><path d="M748 419h1l1 1-2 2-1-1c0-1 0-1 1-2z" class="U"></path><path d="M551 353c1 0 2-2 3-2v2h1l1-2c4 4 9 5 13 8 3 1 5 3 7 6-1 0-3-2-4-3-3-3-6-4-11-5-4-1-8-1-12 0 1-1 1-2 2-3v-1z" class="Q"></path><path d="M503 278h1l1-4h1c-1 3 0 8-2 11v2c-2 1-4 1-6 2-3 1-6 2-9 4 0 0-1 1-1 2l-5 2v-1c1-2 3-3 5-4 3-1 8-3 10-5 3-2 4-6 5-9z" class="Y"></path><path d="M534 366v-1c1-1 1-2 2-3h1c-1 9 0 19 0 28l-1-3c-2-2 0-5-2-8-1-2 0-6 0-9-1-1-1-2 0-4z" class="d"></path><path d="M523 239l4 27c1 6 2 12 5 18 1 0 1 0 1 1-1-1-2-1-2-2v-1c-2-3-3-6-3-9-1-3-1-5-2-8l-2-5c-1-6-3-13-3-19l2-2z" class="Y"></path><path d="M562 423c3-1 4-4 7-5-3 4-6 8-9 11-2 4-6 7-7 12h0c-1 2-1 3 0 4l-2-1v-2-1c1-7 6-14 11-18z" class="V"></path><path d="M580 445h5c1 1 3 1 4 2l6 3-1 1c-2 0-2 0-4-1l-6-2c-1 0-3 0-5 1h-1c-1 0-2 1-3 1l-1 1s-1 0-1 1l-3 1c-1 1 0 1-1 1-4 2-7 6-11 8h0c1-3 4-6 6-8h1c3-2 5-4 8-6h1l1-1h2c1-1 2-1 3-2h0z" class="o"></path><path d="M638 364h0c-1-3-3-4-5-5v-1h2v-1l-1-1-1 1v-1h1 1c0-1 0-1-1-2s-3-2-4-3l1-1 1 1c0-1-1-2-1-2-3-4-4-7-4-11l1-1c1 5 1 7 4 11 2 3 5 5 7 7 3 3 5 9 6 12h-1l-1-1c-1 0-1-1-2-1s-2 0-3-1z" class="W"></path><path d="M423 753h12v3c-2 3-3 5-3 8h0c-1 2-1 4-1 6-3-5-6-11-8-16v-1z" class="p"></path><path d="M652 232h1l-1 1c-1 0-2 1-2 2-6 2-11 6-14 12h0c-2 5-2 10-1 15h1c0 1 0 2 1 2 0 1 0 2-1 2s-1 0-1-1v-1c-2-1-2-2-2-4-1-2-1-5-1-7 1-3 1-5 2-7v-1c1-2 2-5 4-7 2 0 4-2 6-3 1-1 2-1 4-2 1 0 3-1 4-1z" class="Z"></path><path d="M625 378c-1 0-6-2-6-3-1-2-2-4-1-5v-1l1-3c1-1 2-2 4-3 2 0 3 1 5 2v3c-1 1-1 2-3 2l-2-2c-1 0-2 0-3 1h0c1 4 4 5 7 7 4 1 7 2 10 2 0 1-1 1-1 2-1 0-1 0-2 1h-1l-8-3z" class="j"></path><path d="M620 369v-2l3-3c2 0 3 1 4 2v1c-1 1-1 2-2 2l-1-2-1 1c-1 0-2 0-3 1h0z" class="B"></path><defs><linearGradient id="A" x1="649.905" y1="234.615" x2="656.007" y2="243.527" xlink:href="#B"><stop offset="0" stop-color="#474646"></stop><stop offset="1" stop-color="#6e6f6a"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M650 235c5-1 13-3 18 0 0 1 1 1 2 2-1 0-2-1-3 0s-2 1-3 1c-2 0-5-1-6 0-1 0-2 1-2 1-2-1-2-1-4-1v1c0 1-1 1-1 2h-4c-2 0-3 1-5 3-1 1-1 1-3 1l-2 2h-1 0c3-6 8-10 14-12z"></path><path d="M269 460l1 1-2 10v1c-2 6-3 13-2 19 0 8-1 16 1 23 0 5 2 9 3 13h-1l-2-7h-1c0 2 1 6 1 8 1 1 0 2 0 3-1-3-1-6-2-9-3-13-2-26 0-40 0-7 2-15 4-22z" class="G"></path><path d="M274 550c3 2 4 7 6 10 3 3 5 6 8 9 8 9 17 16 25 24 1-1 1 0 1-1v-1c1-1 2-1 3-3l1 1-3 3c2 0 2-1 4-2v1l-2 2-1 1h1c0 1 1 1 1 2l1 1h0l5-5h0c2-1 4-2 4-4 1-1 1-2 2-3v-1c0 2-1 3 0 4h0c-1 3-6 10-8 12-1 1-2 1-3 1-15-16-36-30-45-51z" class="e"></path><path d="M109 227l-14 8c5-9 13-18 21-24h0c-4 5-9 8-12 13-1 1-2 2-3 4 5-3 7-9 12-11 2 0 3-3 5-4l1 1h-1l1 2c1 0 2 1 3 1v-5 1c1 1 2 3 3 3h1 1l1 1c-2 1-4 1-5 2v1s-1 0-2 1c-1 0-2 0-2 1h-2l-5 2c-1 1-2 1-2 2l-1 1h0z" class="M"></path><path d="M118 214l1 2c1 0 2 1 3 1v-5 1c1 1 2 3 3 3h1 1l1 1c-2 1-4 1-5 2-4 1-7 4-10 3-1-1-2-2-1-3h0l1 1h1v-1h1v2c1-1 1-1 3-1v-1c-1-1-1-2-2-2l-1 1c1-2 2-3 3-4z" class="H"></path><path d="M334 580l1 4a54.5 54.5 0 0 0 12 24l7 7s2 1 2 2c0 0-1 0-2 1h0c-12-6-18-18-23-30h-1 0c-1-1 0-2 0-4h1v-1l3-3z" class="q"></path><path d="M334 580l1 4-1 1c-1 1-2 1-3 2v-3-1l3-3z" class="K"></path><path d="M566 445c1 0 2-1 4-1h0c2 0 8 1 10 1h0c-1 1-2 1-3 2h-2l-1 1h-1c-3 2-5 4-8 6h-1c-2 2-5 5-6 8h0l-5 6c0 1-1 3-2 4-1 0-1 1-2 2 0 1 0 1-1 1 0-3 1-6 2-9l1 1c3-6 5-12 10-17 1-2 3-3 5-5z" class="v"></path><path d="M388 207h3l1 1c1 0 3 1 4 2 6 2 10 9 13 14l5 7h1c1 2 2 5 3 7v1c1 2 1 5 1 8-1-1-1-1-1-2-1-1-1-2-1-3-1-3-4-6-5-8 0-1-1-2-1-3h-1c-1-2-2-3-2-4l-10-10-1-1c-1-1-2-2-4-3h0l-6-3h-2c-1-1-1-1-1-2h1c1-1 2-1 3-1z" class="T"></path><path d="M388 207h3l1 1c1 0 3 1 4 2l-1 2c-3-1-5-2-8-2h-2c-1-1-1-1-1-2h1c1-1 2-1 3-1z" class="Z"></path><path d="M469 405c0-2 0-4-1-7 0-1-1-2 0-4l1 5v1h3c5 0 18-1 21 1v8c-5-1-11 0-16-1h-7c0-1 0-1-1-2v-1z" class="d"></path><path d="M470 401h7c4 0 9 0 13 1l-1 1c-4 2-14 1-19 1v-3z" class="k"></path><path d="M393 253c1 2 2 5 3 6 2 1 3 4 5 5v1l-1 2 2 1h1 2 3l1 1c-1 2-2 2-3 3-2 3-3 6-5 8-1 2-2 3-2 5v1h2l2-2c-2 4-6 6-10 8 2-9 3-18 2-27 0-4-2-8-2-12z" class="D"></path><path d="M401 280l-1-2c0-3 2-6 5-8h1v2c-2 3-3 6-5 8z" class="g"></path><path d="M605 753h2c2 0 5-1 7-1v1h-4-4v1c-3 5-5 10-8 15 0 1-2 4-2 5l1 2 1 2h-1c-2-4-5-8-8-12l-2-2c0-2-1-2-2-3l4-4 3-2 4 1 2-2 2 1c1-1 3-1 5-2z" class="p"></path><path d="M592 755l4 1 2-2 2 1c-3 2-5 7-8 9-1 1-2 2-3 2l-2-2c0-2-1-2-2-3l4-4 3-2z" class="O"></path><path d="M589 757l1 1c1 0 1-1 2-1v3h-1c-1 1-2 1-2 3h1 0c0 1 0 0 1 1l1-1v1c-1 1-2 2-3 2l-2-2c0-2-1-2-2-3l4-4z" class="P"></path><path d="M463 848c2 0 2 1 4 1v-1c2 2 4 4 5 6l3 3c0 1 0 3 1 4l1 1h1 0v1h1c2-1 3-2 5-3l-2 3c1 0 2 1 2 2l-1-1-1 1h0v2c0 1 0 2 1 2l-1 1c-1-1-2-2-4-2v2l2 1-1 2-1 1c0 3 0 5 1 8 1 1 1 3 1 4h-1c-4-6-6-11-7-17l-9-21z" class="a"></path><path d="M463 848c2 0 2 1 4 1 2 3 3 5 4 8 0 2 1 3 1 4-1-1-2-2-2-3l-1 1c1 2 2 5 3 7v1 2l-9-21z" class="Q"></path><path d="M484 860l-2 3c1 0 2 1 2 2l-1-1-1 1h0v2c0 1 0 2 1 2l-1 1c-1-1-2-2-4-2v2l2 1-1 2-1 1c0 3 0 5 1 8l-1 1v-2c-1-2-2-4-2-6-1-3-1-7-1-10 1-1 1-2 2-3h0 1 0v1h1c2-1 3-2 5-3z" class="c"></path><path d="M385 210h2l6 3h0c2 1 3 2 4 3l1 1 10 10c0 1 1 2 2 4h1c0 1 1 2 1 3 1 2 4 5 5 8 0 1 0 2 1 3 0 1 0 1 1 2 1 5 2 11-2 16v1c0-2 1-4 1-5 1-2 1-3 1-5v-1l-2-2c0-1 0 0-1-1-2-2-4-4-7-5-1 0 0 0-1-1 1 0 1 0 1-1h1l-1-1v-1c0-2-1-3-3-4v-1c1 0 1 0 2-1l-1-2c-1-2-3-5-5-6h0c-3-1-7-1-10-1-1-1-2 0-4 0l-1 1c-2 1-2 1-4 1l4-4c1 0 2-1 3-1s1 0 2-1h1l1 1c0-1 0-2 1-3v2h0c2 0 2 0 3 1h1l-1-2c1 0 1 2 3 2v-1l-7-7h-1l-2-2-6-3h0z" class="J"></path><path d="M407 233v-2c1 0 2 1 2 2l5 8 1 2 2-1c0 1 0 2 1 3 0 1 0 1 1 2 1 5 2 11-2 16v1c0-2 1-4 1-5 1-2 1-3 1-5v-1l-2-2c0-1 0 0-1-1-2-2-4-4-7-5-1 0 0 0-1-1 1 0 1 0 1-1h1l-1-1v-1c0-2-1-3-3-4v-1c1 0 1 0 2-1l-1-2z" class="d"></path><path d="M407 233v-2c1 0 2 1 2 2l5 8 1 2c1 1 1 2 1 3-2-3-3-6-5-9l-1 1h1c1 2 1 4 1 6-1 0-1 0-2-1l-1-1v-1c0-2-1-3-3-4v-1c1 0 1 0 2-1l-1-2z" class="H"></path><path d="M636 247h1l2-2c2 0 2 0 3-1 2-2 3-3 5-3h4c0-1 1-1 1-2v-1c2 0 2 0 4 1l-2 4c-1 2-3 4-4 6 0 1 0 4-1 6-1 0-2 1-3 2-4 3-7 5-9 10h0l-1-1c1 0 1-1 1-2-1 0-1-1-1-2h-1c-1-5-1-10 1-15z" class="M"></path><path d="M636 247h1l2-2c2 0 2 0 3-1 2-2 3-3 5-3h4c0-1 1-1 1-2v-1c2 0 2 0 4 1l-2 4c-2-1-2-1-4 0-2 2-3 3-3 5l-1-2c-2-1-2-1-4 0-1 1-4 2-5 3-1 3-2 6-2 9l1 1v3c0 2 0 0 1 2h0c-1 0-1-1-1-2h-1c-1-5-1-10 1-15z" class="L"></path><path d="M759 395v1-1l13 13c-4 1-7 2-11 3-2 1-5 3-8 3h-1-1 0l-7 5-2-2-2-4-2-2h1v-2c1 1 1 0 1 1v-2-1c0-1 0-1-1-2 2 0 4 3 5 4s2 1 3 1l1 1h0v-1l-2-2c1-2 3 0 4-1 1 0 1 0 1-1l1-3h0 2v1h1l2-2c1 1 1 1 2 1v-1c0-2-2-4-3-6h0 2l1-1z" class="X"></path><path d="M740 413v-2h1v-2c2 2 3 2 4 5-1 1-1 2-3 3l-2-4z" class="q"></path><path d="M759 395v1h0c3 4 6 6 8 10-3 0-5 0-8 1-4 1-7 2-11 4v-1l-2-2c1-2 3 0 4-1 1 0 1 0 1-1l1-3h0 2v1h1l2-2c1 1 1 1 2 1v-1c0-2-2-4-3-6h0 2l1-1z" class="B"></path><path d="M565 464l4-3h1 0v1c0 1 0 1 1 1-1 1-3 2-4 4 0 1-1 2-1 2l-1 3h-1c0 1 0 1-1 2l-1 2c-1 1-1 2-2 4l-1 1h0c-2 1-3 3-4 5l-6 10c-1 2-1 4-2 6h-1l-2-5c1-3 1-7 2-11s3-8 5-11 3-6 5-8c1-2 3-3 4-5v4c-2 1-3 3-4 5h1c2-2 4-5 6-6l2-1z" class="v"></path><path d="M554 484l5-9v1l-3 6c-1 2-2 3-1 4l-6 10c-1 2-1 4-2 6h-1l-2-5c1-3 1-7 2-11 0 3 1 5 1 8h1l1-2 5-8z" class="I"></path><path d="M560 462v4c-2 1-3 3-4 5h1c2-2 4-5 6-6l2-1c-2 3-5 5-7 8l-1 1c-2 2-3 5-5 7v4h2l-5 8-1 2h-1c0-3-1-5-1-8 1-4 3-8 5-11s3-6 5-8c1-2 3-3 4-5z" class="H"></path><path d="M549 492c-1-2-1-2-1-4 0-3 2-5 4-8v4h2l-5 8z" class="T"></path><path d="M625 378l8 3c2 1 5 2 7 2 2 1 3 1 5 2h2l2 2c-1 0-2 0-3 1h1 5l-1 1c-1 0-6 0-7 2-1 0 0 0-1 1 0-1 0-2-1-2l-2 1c-2 1-5 1-7 3l-9-1c-2-1-7-2-9 0l1 3h0c-2 0-2-1-4-2v-2c1-2 4-3 6-3 1-1 1-1 1-2v-4c1-2 4-3 5-4l1-1z" class="G"></path><path d="M625 378l8 3c2 1 5 2 7 2l1 3-2 1h0c-3-1-5-2-7-3v-1l-1 1c-2-1-3-3-5-4l-2-1 1-1z" class="T"></path><path d="M631 384v1c-1 2 0 3-2 4-3-1-6 0-9 0 1-2 2-4 3-5 2-1 2-2 3-4 2 1 3 3 5 4z" class="k"></path><path d="M640 383c2 1 3 1 5 2h2l2 2c-1 0-2 0-3 1h1 5l-1 1c-1 0-6 0-7 2-1 0 0 0-1 1 0-1 0-2-1-2l-2 1c-2-2-3-1-5-1l-1-1 2-1 1-1 1 1 1-1h0l2-1-1-3z" class="a"></path><path d="M640 383c2 1 3 1 5 2h2l2 2c-1 0-2 0-3 1-2 0-4 0-7-1l2-1-1-3z" class="Y"></path><path d="M572 482h0c1 0 2 0 3-1h4c-1 0-1 0-2 1-2 0-3 1-5 2h-1c-5 4-10 8-13 13h-1c0 1-1 2-1 3v2l-1 1 1 1-3 7v1c0 3-1 7-2 10 0 1 0 3 1 5v2l1 1-1 1c-1-1-1-1-1-2h-1c-2-2-3-5-4-7-1-5 0-9 1-13v-7c1-2 1-4 2-6l6-10c1-2 2-4 4-5-1 1-4 5-4 6v1 3 2l2-3c1-1 2-1 3-1 2-2 4-4 6-5h0c1-1 1-1 2-1l1-1c1 0 1-1 3 0z" class="O"></path><path d="M551 499l1-3 3-9v1 3 2l2-3c1-1 2-1 3-1-4 4-7 8-9 14v2c-1-1 0-4 0-6z" class="Z"></path><path d="M555 486c1-2 2-4 4-5-1 1-4 5-4 6l-3 9-1 3h-1c-1 3-1 6-3 10v-7c1-2 1-4 2-6l6-10z" class="a"></path><path d="M572 482h0c1 0 2 0 3-1h4c-1 0-1 0-2 1-2 0-3 1-5 2h-1c-5 4-10 8-13 13h-1c0 1-1 2-1 3v2l-1 1 1 1-3 7v1c0 3-1 7-2 10 0 1 0 3 1 5v2c-1-2-2-3-2-5h0c-1-4-2-9 0-11 0-3 1-5 2-8 2-5 4-10 8-15 2-2 4-3 5-5l1-1h0c1-1 1-1 2-1l1-1c1 0 1-1 3 0z" class="T"></path><path d="M550 513h0 1v-3c0-1 0-1 1-2 0-1 1-2 2-3h0l-1 3c-1 2 0 2 0 3v1c0 3-1 7-2 10 0 1 0 3 1 5v2c-1-2-2-3-2-5h0c-1-4-2-9 0-11z" class="M"></path><path d="M549 357c4-1 8-1 12 0 5 1 8 2 11 5 1 1 3 3 4 3 2 3 4 7 5 11v1c1 4 1 10 0 14-2 8-5 14-9 21l-3 6c-3 1-4 4-7 5-3 0-6-2-9-2h0l-1-1c-2 0-4 0-5 1v1l-1 1v-1-2-1c1 0 2-1 2-2 2-1 6 1 8 0h1c1 0 2 0 2-1 2 0 3-1 4-2v1s-1 1-1 2h1l4-5h-2l1-2-1-1c0-2 1-1 2-2 1 0 1-1 1-1 1-1 1-3 2-4 2-2 4-3 5-5h0 1 0 1s0-1 1-2v-3c1-1 1-2 1-3v-4c-1-3 0-7 0-9l-1-1c0-2-1-4-2-6-4-6-9-9-15-11h-1-1c-3-1-8 0-11 1v1l-1-1 2-2z" class="C"></path><path d="M568 406c1-1 1-3 2-4 2-2 4-3 5-5h0 1c-2 6-5 12-9 15h-2l1-2-1-1c0-2 1-1 2-2 1 0 1-1 1-1z" class="o"></path><path d="M392 226c3 0 7 0 10 1h0c2 1 4 4 5 6l1 2c-1 1-1 1-2 1v1c2 1 3 2 3 4v1h-1-3l-4 1-3 2h1l1-1h1c0 1 0 1-1 2s-1 1-2 1l-1-1-1 1c-1 0-2 0-3-1v1c-2 0-2 0-3-1s-2-1-3-1l-2-2c-1 0-2 0-3 1l-3-7-1 1v-3c1-1 1-1 2-1l1-1c2-2 5-4 8-6l3-1z" class="d"></path><path d="M379 237l1-1h1l4 7c-1 0-2 0-3 1l-3-7z" class="Z"></path><path d="M391 232c1 0 2 0 3-1l2 1c-1 1-3 1-4 1-1 1-2 3-3 4v3 1c-1-1-1-2-2-4 0 0 0-1-1-2v-1l-1-1h2 1c1 0 1-1 2-1h1z" class="g"></path><path d="M381 233c2-2 5-4 8-6 1 2 1 3 3 3l-1 2h-1c-1 0-1 1-2 1h-1-2-1-3z" class="H"></path><path d="M397 232l1-1 1 1c1 1 2 2 3 2 2 1 2 1 3 3-1 3-2 4-4 6l-3 2h-2l-1-1v-1c2-1 4-2 5-4 1-1 1-2 1-2 0-2-2-4-4-5z" class="B"></path><path d="M392 230c3-1 5-1 8 0 2 1 5 3 6 6v1c2 1 3 2 3 4v1h-1-3l-4 1c2-2 3-3 4-6-1-2-1-2-3-3-1 0-2-1-3-2l-1-1-1 1h-1l-2-1c-1 1-2 1-3 1l1-2z" class="E"></path><path d="M392 226c3 0 7 0 10 1h0c2 1 4 4 5 6l1 2c-1 1-1 1-2 1-1-3-4-5-6-6-3-1-5-1-8 0-2 0-2-1-3-3l3-1z" class="Z"></path><path d="M551 411c2-2 4-4 6-5l6-9c0 1 0 2-1 4l-1 1v1h0c0 1 0 1-1 2v1l-2 2c1 0 1 0 2-1l3-3h0l4 3 1-1s0 1-1 1c-1 1-2 0-2 2l1 1-1 2h2l-4 5h-1c0-1 1-2 1-2v-1c-1 1-2 2-4 2 0 1-1 1-2 1h-1c-2 1-6-1-8 0 0 1-1 2-2 2v1 2 1l1-1v-1c1-1 3-1 5-1l1 1h0c3 0 6 2 9 2-5 4-10 11-11 18v1 2c-4-2-9-2-14-2v-1h1l1-1c1-2 1-7 1-9l1-4 1-3c0-1 1-2 1-3 3-4 5-7 8-10z" class="H"></path><path d="M559 416c-1 1-2 0-3 0-2-1-2-2-2-3s1-2 2-3c2 0 3 0 4 1l1 2-2 1v2z" class="o"></path><path d="M544 425c0-1 0-1 1-1 0 1 0 1 1 2l1-2c0-1 1-1 1-1h1c-1 1-2 3-2 4v1l2-2c1-2 3-2 6-2-1 1-3 5-4 6-2 1-2 0-3 2-1-1-1-2-2-2v-1 1h-1l-1-2c0-2 1-2 0-3z" class="I"></path><path d="M560 407l3-3h0l4 3 1-1s0 1-1 1c-1 1-2 0-2 2l1 1-1 2h2l-4 5h-1c0-1 1-2 1-2v-1c-1 1-2 2-4 2v-2l2-1-1-2h1c2 0 3-1 4-2l-1-1-1 1c-1-1-2-1-3-2z" class="s"></path><path d="M551 411c0 2-2 3-3 5-2 2-5 7-5 10h0l1-1c1 1 0 1 0 3l1 2h1v-1 1c1 0 1 1 2 2 1-2 1-1 3-2 1-1 3-5 4-6s2-1 3-1l-1 1c0 1-1 2-1 2l-3 6c-1 1-1 3-2 4h0c-1 0-3 1-4 2 0-1-1-2-1-2-2-2-1-3-3-5 0-1-1-3-1-4v-3c0-1 1-2 1-3 3-4 5-7 8-10z" class="G"></path><path d="M542 424v3c0 1 1 3 1 4 2 2 1 3 3 5 0 0 1 1 1 2 1-1 3-2 4-2h0c0 2-1 4-1 6h1v2c-4-2-9-2-14-2v-1h1l1-1c1-2 1-7 1-9l1-4 1-3z" class="O"></path><path d="M543 431c2 2 1 3 3 5 0 0 1 1 1 2 1-1 3-2 4-2h0c0 2-1 4-1 6-1 0-3 0-3-2l-2-2-1-1h-1c1-2 1-3 0-5v-1z" class="P"></path><path d="M638 364c1 1 2 1 3 1s1 1 2 1l1 1h1 0v-1l6 4c3 2 5 4 8 5l7 3 5 1 2 1c2 0 3 1 4 1l-1 2-1 2c-1 0-1 1-2 1-2 0-4 1-5 2h0l-1 1-7 1-7 2-2-3 1-1h-5-1c1-1 2-1 3-1l-2-2h-2c-2-1-3-1-5-2-2 0-5-1-7-2h1c1-1 1-1 2-1 0-1 1-1 1-2 3-1 5-3 7-5-2-1-5 0-7-1-1-1 1-6 1-8z" class="Y"></path><path d="M648 383l1-1h0c1-2 2-4 3-4l4 1c0 2-1 3-2 5v2l-1-1c-2-1-3-1-5-2z" class="c"></path><path d="M638 364c1 1 2 1 3 1s1 1 2 1l1 1h1 0l1 1c1 1 7 5 7 6-1 0-2-1-2-1h-2c-1 0-2-1-3-2l-1 1-1 1c-2-1-5 0-7-1-1-1 1-6 1-8z" class="l"></path><path d="M637 372l1-1v-2c1-1 1-1 2-1 2 0 4 1 6 3h0l-1 1-1 1c-2-1-5 0-7-1z" class="I"></path><path d="M644 373l1-1c1 1 4 2 4 4h0c-1 1-2 3-3 5 0 1 1 1 2 2h0c2 1 3 1 5 2h-6-2c-2-1-3-1-5-2-2 0-5-1-7-2h1c1-1 1-1 2-1 0-1 1-1 1-2 3-1 5-3 7-5z" class="a"></path><path d="M645 385c0-2 0-2-1-4l-1 1-2-1v-2c2-1 3-3 5-4 1 0 2 0 3 1-1 1-2 3-3 5 0 1 1 1 2 2h0c2 1 3 1 5 2h-6-2z" class="K"></path><path d="M659 375l7 3 5 1 2 1c2 0 3 1 4 1l-1 2-1 2c-1 0-1 1-2 1-2 0-4 1-5 2h0l-1 1-7 1-7 2-2-3 1-1h-5-1c1-1 2-1 3-1l-2-2h6l1 1v-2c1-2 2-3 2-5 1 0 1 0 3 1h1 3c-1-1-3-3-4-5z" class="N"></path><path d="M673 380c2 0 3 1 4 1l-1 2-1 2-3-1h0v-1-1l1-2z" class="X"></path><path d="M659 375l7 3 5 1c-1 1 0 1-1 1-1 1-1 1-2 1-2 0-3 0-5-1-1-1-3-3-4-5z" class="D"></path><path d="M401 243l4-1h3 1l1 1h-1c0 1 0 1-1 1 1 1 0 1 1 1 3 1 5 3 7 5 1 1 1 0 1 1l2 2v1c0 2 0 3-1 5 0 1-1 3-1 5-4 7-8 14-14 20h0l-2 2h-2v-1c0-2 1-3 2-5 2-2 3-5 5-8 1-1 2-1 3-3l-1-1h-3-2-1l-2-1 1-2v-1c-2-1-3-4-5-5-1-1-2-4-3-6l2-3c-4-1-6-3-8-5 1 0 2 0 3 1s1 1 3 1v-1c1 1 2 1 3 1l1-1 1 1c1 0 1 0 2-1s1-1 1-2h-1l-1 1h-1l3-2z" class="H"></path><path d="M401 243l4-1h3 1l1 1h-1c0 1 0 1-1 1 1 1 0 1 1 1 3 1 5 3 7 5 1 1 1 0 1 1l1 3c1 4-3 9-5 12l-1 1h0c2-3 6-9 5-13l-1-2h-1 0l-1 1c-2-1-2-1-3 0l-3-3c-3-3-5 0-8-2h-1c-1 1-2 1-2 2h-1-1 0c-4-1-6-3-8-5 1 0 2 0 3 1s1 1 3 1v-1c1 1 2 1 3 1l1-1 1 1c1 0 1 0 2-1s1-1 1-2h-1l-1 1h-1l3-2z" class="M"></path><path d="M400 248c3-1 5-2 8-2 3 1 6 2 8 5v1h-1 0l-1 1c-2-1-2-1-3 0l-3-3c-3-3-5 0-8-2z" class="T"></path><path d="M395 250h0 1 1c0-1 1-1 2-2h1c3 2 5-1 8 2l3 3c0 2 1 4-1 5-1 2-1 2-3 3l-3 1c1 1 1 1 2 1 0 1-1 1-2 2l-1-1-2 1v-1c-2-1-3-4-5-5-1-1-2-4-3-6l2-3z" class="L"></path><path d="M403 260h0c0-1-1-2-2-2 1-1 2-1 2-2 1 0 0 0 1-1h0l2 2c2-1 1-1 3-1l1 1c-1 2-2 3-4 3h-1l2 1-3 1-1-2z" class="d"></path><path d="M395 250h0 1 1c0-1 1-1 2-2h1c3 2 5-1 8 2-3 0-5 0-7 2-1 1-2 2-2 4 1 2 2 3 4 4l1 2c1 1 1 1 2 1 0 1-1 1-2 2l-1-1-2 1v-1c-2-1-3-4-5-5-1-1-2-4-3-6l2-3z" class="H"></path><path d="M399 256c1 2 2 3 4 4l1 2c1 1 1 1 2 1 0 1-1 1-2 2l-1-1-2 1v-1c-1 0 0 0 0-1v-1l-1-1c-2-1-2-2-3-3l2-2z" class="T"></path><path d="M565 363c-4-2-7-3-11-3-3 1-7 3-9 5-2 4-3 8-3 13h0c-1-2-2-5-2-7-1-9 4-11 9-17h0 2c-1 1-1 2-2 3l-2 2 1 1v-1c3-1 8-2 11-1h1 1c6 2 11 5 15 11 1 2 2 4 2 6l1 1c0 2-1 6 0 9v4c0 1 0 2-1 3v3c-1 1-1 2-1 2h-1 0-1 0c-1 2-3 3-5 5-1 1-1 3-2 4l-1 1-4-3h0l-3 3c-1 1-1 1-2 1l2-2v-1c1-1 1-1 1-2h0v-1l1-1c1-2 1-3 1-4s0-1 1-2c0 0 0-1 1-2v-2c1 0 1-1 1-2v-2c1-1 1-1 1-2v-1c1-1 1-5 0-6v-1c0-2-1-3-1-4v-2-1c-1-1-2-2-2-4 1 0 1 0 2-1h1l-2-2z" class="a"></path><path d="M565 363c1 0 2 0 3 1h0c2 1 3 2 4 4s2 3 3 5v3c-1 1-1 2-2 3s-1 3-2 5v-5h0c-1-5-2-10-4-14l-2-2z" class="q"></path><path d="M563 404c3-3 9-8 10-13v-2c1 0 2-1 3-1h1l1-2c1 3-1 8-2 11h0-1 0c-1 2-3 3-5 5-1 1-1 3-2 4l-1 1-4-3h0z" class="Z"></path><path d="M567 365c2 4 3 9 4 14 0 3 0 6-1 9l-1 1c-1 5-4 9-8 14h0v-1l1-1c1-2 1-3 1-4s0-1 1-2c0 0 0-1 1-2v-2c1 0 1-1 1-2v-2c1-1 1-1 1-2v-1c1-1 1-5 0-6v-1c0-2-1-3-1-4v-2-1c-1-1-2-2-2-4 1 0 1 0 2-1h1z" class="o"></path><path d="M558 449c1-2 2-3 3-4l1 1c-1 0-1 1-2 2h1c1-1 3-2 5-3-2 2-4 3-5 5-5 5-7 11-10 17l-1-1c-1 3-2 6-2 9 1 0 1 0 1-1 1-1 1-2 2-2l-5 11c-1 3-1 7-2 10-1 4-2 8-2 12v2 1l-1 1h0-1v-6c0-2 0-2-1-4v-3l-3 2h0l-1-3v-2-1c-1-1-1-4-2-5-1-3 0-5-1-8h0v-4c1 0 1-1 1-1v-1h1v-2-2l1-1c1-1 0-3 0-4s0-1 1-2c0 2 0 3 1 4 0-2 0-4 1-6v1l1 1c2 0 2 0 3-1l-1-1v-1h5c1-2 2-4 2-5h0l5-5c0-1 0-1 1-2 1 2 1 2 4 2h0z" class="O"></path><path d="M553 449c0-1 0-1 1-2 1 2 1 2 4 2-6 7-9 15-13 23v-9c1-1 1-3 1-4 1-2 2-4 2-5h0l5-5z" class="Z"></path><path d="M558 449c1-2 2-3 3-4l1 1c-1 0-1 1-2 2h1c1-1 3-2 5-3-2 2-4 3-5 5-5 5-7 11-10 17l-1-1c-1 3-2 6-2 9 1 0 1 0 1-1 1-1 1-2 2-2l-5 11c-1 3-1 7-2 10h-1c2-11 3-21 8-30 2-5 5-9 7-14h0z" class="B"></path><path d="M535 464c0-1 0-1 1-2 0 2 0 3 1 4 0-2 0-4 1-6v1c0 2 1 4 1 6h1c0-1 0-2 1-3 0 2 0 2 1 4v2 1l1 1h0-1c-1 1 0 1 0 2h1v1l-2-1 1 4h1v4 2h0-1l1-2-1-1v1s-1 2-1 3c0 0 1 1 1 2s-1 1 0 3v7c0-1 0-2 1-3v-1h1c-1 4-2 8-2 12v2 1l-1 1h0-1v-6c0-2 0-2-1-4v-3l-3 2h0l-1-3v-2-1c-1-1-1-4-2-5-1-3 0-5-1-8h0v-4c1 0 1-1 1-1v-1h1v-2-2l1-1c1-1 0-3 0-4z" class="g"></path><path d="M537 489h1c0-6-2-14 0-18h1v1 11c0 2 1 3 1 5 0 3-1 6-1 8l-3 2h0l-1-3v-2l2 2v-1-5z" class="H"></path><path d="M535 464c0-1 0-1 1-2 0 2 0 3 1 4 0-2 0-4 1-6v1c0 2 1 4 1 6h1c0-1 0-2 1-3 0 2 0 2 1 4v2 1l1 1h0-1c-1 1 0 1 0 2h1v1l-2-1v-1-1h-1-1v-1h-1c-2 4 0 12 0 18h-1v5 1l-2-2v-1c-1-1-1-4-2-5-1-3 0-5-1-8h0v-4c1 0 1-1 1-1v-1h1v-2-2l1-1c1-1 0-3 0-4z" class="d"></path><path d="M532 479v-4c1 0 1-1 1-1v-1h1v2l2-2c1 3 1 10 1 13v3 5 1l-2-2v-1c-1-1-1-4-2-5-1-3 0-5-1-8h0z" class="T"></path><path d="M534 475l2-2c1 3 1 10 1 13-1 1-1 2-1 4h-1v-4-1c-1-2-1-4-1-6v-2c1-1 0-1 0-2z" class="H"></path><path d="M513 463v-1h1v4h1l1-3c1-1 0-1 2 0v3h1c0-2 0-4 1-5h1 1c1 1 1 1 1 2 2 0 3-1 4-2l1 1 1-1s0 1 1 2l1-1v1c1 1 0 1 1 1 0 0 1-1 1-2h0l1 2h1c0 1 1 3 0 4l-1 1v2 2h-1v1s0 1-1 1v4h0c1 3 0 5 1 8 1 1 1 4 2 5v1 2l1 3h0l3-2v3c1 2 1 2 1 4v6h1 0l1-1c0 1 1 3 0 4h-1c0 1 0 1 1 2h0-1s-1 0-1-1l-1 2c-2 0-2 0-3-1v-8l-1 2c-1-2-1-4-1-6-1-3-2-6-4-8v-1l-1-1c-4-5-9-8-15-9l-2-1-1-3-2 2-1-1v-10-1c0-1 1-1 1-2s0-1 1-2v1c0 2 0 3 2 4l1-1v-5-1z" class="j"></path><path d="M525 472c0-2 0-6 2-7h1c0 1 0 1-1 1v1h0 1l1-1v5c1-2 1-3 1-4l1-1v5 1l1 1c1-1 1-6 1-8h1v4 2 2h-1v1s0 1-1 1v4h-1v-3c-1-1-2-3-3-3l-3-1z" class="Y"></path><path d="M525 472l3 1c1 0 2 2 3 3v3h-3c-1 1-1 1-1 2-1 0-1-1-2-1-1 1-2 2-2 3l-1-1c-1 0-1 1-2 1-1-1-2-1-3-2 2 0 3-1 4-1v-1c2-2 3-5 4-7z" class="H"></path><path d="M513 463v-1h1v4h1l1-3c1-1 0-1 2 0v3h1c0-2 0-4 1-5h1v1 5l2 1v1c-1 1-1 1-1 3l1 1c0 2-1 3-2 4s-2 1-4 1c-1-1-2-2-2-3l-1 1v1 1 5l-2-1-1-3-2 2-1-1v-10-1c0-1 1-1 1-2s0-1 1-2v1c0 2 0 3 2 4l1-1v-5-1z" class="W"></path><path d="M508 470c1 0 2 1 3 1v1 7l-2 2-1-1v-10z" class="D"></path><path d="M532 479h0c1 3 0 5 1 8 1 1 1 4 2 5v1 2l1 3h0l3-2v3c1 2 1 2 1 4v6h1 0l1-1c0 1 1 3 0 4h-1c0 1 0 1 1 2h0-1s-1 0-1-1l-1 2c-2 0-2 0-3-1v-8l-2-6c-2-9-7-13-14-17 1 0 1-1 2-1l1 1c0-1 1-2 2-3 1 0 1 1 2 1 0-1 0-1 1-2h3 1z" class="R"></path><path d="M539 496v3 5h-1l-1-1c0-2-1-3-1-5h0l3-2z" class="S"></path><path d="M532 479h0l-1 1v2 1c-1 3 0 5 2 7l-1 1-2-2c-1-1-2-3-3-3-2-1-3-1-4-3 0-1 1-2 2-3 1 0 1 1 2 1 0-1 0-1 1-2h3 1z" class="B"></path><path d="M435 753h12l4 1c-1 1-2 1-2 3v1l-1 1c-1 0-2 2-2 3l-1 2c-1 4-4 9-3 13l-2-2c0 4-2 7-2 10l1 2-1 2c-1 1 0 1-1 2 0 1 0 1-1 2v1c-2 2-5 5-8 7-1 1-4 3-5 3-1 1-2 1-4 2-1 1-2 1-3 1h-1v-1h-1c-1 0-1-1-2-2 2-3 5-3 7-6 2-2 2-9 3-12 1-4 2-8 5-11 1-1 3-3 4-5 0-2 0-4 1-6h0c0-3 1-5 3-8v-3z" class="D"></path><path d="M438 780l6-16h0l-4 11c0 4-2 7-2 10l1 2-1 2c-1 1 0 1-1 2 0 1 0 1-1 2v1c-2 2-5 5-8 7-1 1-4 3-5 3-1 1-2 1-4 2-1 1-2 1-3 1h-1c6-5 13-7 17-14 0-1 1-1 1-2 1-1 1-2 2-3v-3c2-1 2-3 3-5z" class="Z"></path><path d="M435 753h12l4 1c-1 1-2 1-2 3v1l-1 1c-1 0-2 2-2 3l-1 2c-1 4-4 9-3 13l-2-2 4-11h0l-6 16v-2-2l-1 1-1-1 2-1c1 0 1-1 1-2 1-2 2-4 2-5 0-2 1-3 0-5l-1 2h-1v-1-1l-1-1-3 3-2 2v1h-1l1-2c0-1 0-1-1-2h0c0-3 1-5 3-8v-3z" class="O"></path><path d="M435 753h12l4 1c-1 1-2 1-2 3v1l-1 1c-1 0-2 2-2 3 0-1 0-2 1-3l-3-1c-1 1-2 1-3 2h0-2l-2-2 1-1c-2 0-1 1-3 2v-3-3z" class="U"></path><path d="M437 758c4-2 7-2 10-1v2l-3-1c-1 1-2 1-3 2h0-2l-2-2z" class="Q"></path><defs><linearGradient id="C" x1="150.411" y1="220.127" x2="152.819" y2="203.262" xlink:href="#B"><stop offset="0" stop-color="#8f9090"></stop><stop offset="1" stop-color="#afaba6"></stop></linearGradient></defs><path fill="url(#C)" d="M157 195l4-1c3 0 4 0 6 3 1 1 2 1 4 2l1 2v3l1 1h1l-1 1 1 1h0c2 1 2 1 4 1 1 0 1 1 1 1l-8 1h0c-4 2-8 2-12 3l-23 5-18 6c-2 1-6 3-9 3h0l1-1c0-1 1-1 2-2l5-2h2c0-1 1-1 2-1 1-1 2-1 2-1v-1c1-1 3-1 5-2l-1-1h-1-1c-1 0-2-2-3-3v-1 5c-1 0-2-1-3-1l-1-2h1l-1-1 4-4c3 0 5-1 8-1 0 0 1-1 2-1l6-3 12-6c2-1 5-1 7-3z"></path><path d="M119 214c2-2 3-3 6-4h0l-3 2v5c-1 0-2-1-3-1l-1-2h1zm47-14c2 0 3 0 4 1h2v3l-1 1c-2 1-3 1-5 1l1-2c0-1 0-2-1-4h0z" class="B"></path><path d="M157 195l4-1c3 0 4 0 6 3 1 1 2 1 4 2l1 2h-2c-1-1-2-1-4-1h0c1 2 1 3 1 4l-1 2c-1 0-2 0-3 1-2 0-3 0-4-1s-1-2-2-3c-2-1-4-1-6-1h0c-1-1-1-3-1-4 2-1 5-1 7-3z" class="Z"></path><path d="M157 203v-4c2-1 3-2 5-2s3 2 4 3h0c1 2 1 3 1 4l-1 2c-1 0-2 0-3 1-2 0-3 0-4-1s-1-2-2-3z" class="a"></path><path d="M166 200c1 2 1 3 1 4l-1 2c-1 0-2 0-3 1v-2c0-1-1-2 0-3 2 0 0 0 1-1 1 0 2 0 2-1z" class="i"></path><path d="M150 198c0 1 0 3 1 4h0c2 0 4 0 6 1 1 1 1 2 2 3-1 2-3 3-4 4h-6c-7 2-14 3-20 6-1-1-2-3-3-5 1-2 2-2 4-3 0 0 1-1 2-1l6-3 12-6z" class="S"></path><path d="M152 206c1-1 2-1 3-1h2v1c-1 2-2 2-4 2h-1v-2z" class="k"></path><path d="M151 202h0c1 2 1 3 1 4v2c-1 1-3 2-5 2-1-1-2-2-3-4 0-1 0-2 1-3 2-1 3-1 6-1z" class="i"></path><path d="M661 221c1-1 4-1 5-1 1 1 2 1 3 1 1 1 2 1 3 1l2-1 1 1v1l-1 2h0 4v1l-1 1c-1 1-2 1-2 3l-1 1 4 6h1c1 2 1 3 1 4 1 1 1 2 1 3v1c1 2 1 6 0 8v2c0 1 0 1-1 1v4l-2 2c0 2 1 5 0 6 0 2-1 7 0 9 0 1 0 1 1 3v3c1 0 1 1 1 2v1 1 1 3l1 1-1-1c-2-1-4-3-5-5-3-6-2-16-3-23 0-3 0-6-2-8v1 16c0 4 1 8 1 11-1 2 0 6 1 8-2-4-3-7-6-10 2 7 7 12 7 19h0c-1-3-2-5-3-8s-3-5-4-8c-1-2-1-3-2-4h-1l1-1c2 2 4 3 6 3l-1-16c0-3 0-8-1-11v-3l2-2c1 1 1 2 2 2s1 0 2-1c0-3 0-7-1-9l-3-5c-1-1-2-1-2-2-5-3-13-1-18 0 0-1 1-2 2-2l1-1h-1c-1 0-3 1-4 1-2 1-3 1-4 2-2 1-4 3-6 3 4-6 9-12 16-16v1c2-1 4-2 7-2z" class="N"></path><g class="E"><path d="M652 232c3-1 7-1 10 0h1c2 0 3 1 4 1l2 1v1c2 0 4 3 5 4 1 2 2 4 2 6v1 2 1c0 1-1 2-1 2h-1c0-3 0-7-1-9l-3-5c-1-1-2-1-2-2-5-3-13-1-18 0 0-1 1-2 2-2l1-1h-1z"></path><path d="M674 237h0c0-1 0-2-1-3 0-1 0-1-1-2h0l-1-1 1-1 2 1 4 6h1c1 2 1 3 1 4 1 1 1 2 1 3v1c1 2 1 6 0 8v2c0 1 0 1-1 1v4l-2 2c0 2 1 5 0 6 0 2-1 7 0 9 0 1 0 1 1 3v3c1 0 1 1 1 2v1 1 1 3c-1-1-2-2-2-3v-4c-2-7-3-16-2-22 1-1 1-2 1-3h0c0-2 1-3 2-3h1v-1c-1 0-1 0-2-1h0c-1 0-2 1-3 1-1-1 0-1 1-1 1-1 2-3 2-4 1-2 0-6-1-8v-1c-1-1-2-2-3-4z"></path></g><path d="M661 221c1-1 4-1 5-1 1 1 2 1 3 1 1 1 2 1 3 1l2-1 1 1v1l-1 2h0 4v1l-1 1c-1 1-2 1-2 3l-1 1-2-1-1 1 1 1h0c1 1 1 1 1 2 1 1 1 2 1 3h0c-1-1-3-3-5-4-4-2-11-4-16-2h-2c-2 1-3 1-3 2-2 1-3 1-4 2-2 1-4 3-6 3 4-6 9-12 16-16v1c2-1 4-2 7-2z" class="B"></path><path d="M661 221v2l-1 1c-1 1-3 1-5 2h-2c0-2 0-2 1-3 2-1 4-2 7-2z" class="V"></path><path d="M182 181l1 1c1 1 3 1 4 1l5 2v-1l1 1s2 2 3 2l2 3c0 1-1 3-2 4h0l-1 2v4c0 1 0 3-1 4l-3 1c-1 0-3 0-4-1h-1c-1 1-3 1-5 0-2 1-4 1-7 1h-1l-1-1v-3l-1-2c-2-1-3-1-4-2-2-3-3-3-6-3l-4 1c-2 2-5 2-7 3l-12 6-6 3c-1 0-2 1-2 1-3 0-5 1-8 1l-4 4c-2 1-3 4-5 4-5 2-7 8-12 11 1-2 2-3 3-4 3-5 8-8 12-13h0 0c1-2 3-3 5-4 4-1 8-5 12-7 10-5 20-9 31-12 4-1 9-2 13-3h0v-1l1-1c0-1 3-1 4-2z" class="H"></path><path d="M134 202l1 1 5-2h1c-1 1-3 2-3 3l-6 3c-1 0-2 1-2 1-3 0-5 1-8 1l3-2c2-2 6-5 9-5z" class="R"></path><path d="M134 202c4-1 7-4 11-5 3-2 6-3 9-4 1 0 3-1 4-1v1h-1c-1 0-2 1-3 1v1l-1 1c1 0 1 0 2-1h0 2c-2 2-5 2-7 3l-12 6c0-1 2-2 3-3h-1l-5 2-1-1z" class="J"></path><path d="M179 187c4-1 6-1 10 0h1l1 2v1h-1c-3 0-7-1-10 0v1h-1-3-1-1l-5 1-8 2-4 1h-2 0c-1 1-1 1-2 1l1-1v-1c1 0 2-1 3-1h1v-1l10-2 11-3z" class="h"></path><path d="M168 190h1c3 0 5-1 8-1 1 0 1 0 2-1h1c-1 1-4 1-5 2-1 0-4 1-5 2h-2 1l-8 2-4 1h-2 0c-1 1-1 1-2 1l1-1v-1c1 0 2-1 3-1h1v-1l10-2z" class="M"></path><path d="M182 181l1 1c1 1 3 1 4 1l5 2v-1l1 1s2 2 3 2l2 3c0 1-1 3-2 4h0c-2 0-1 0-2 1h-1-3l1-1-2-1v-2h-10 1v-1c3-1 7 0 10 0h1v-1l-1-2h-1c-4-1-6-1-10 0-1 0-2-1-2-2h0v-1l1-1c0-1 3-1 4-2z" class="Z"></path><path d="M182 181l1 1-1 3h-4-1v-1l1-1c0-1 3-1 4-2z" class="v"></path><path d="M192 184l1 1s2 2 3 2l2 3c0 1-1 3-2 4h0c-2 0-1 0-2 1h-1-3l1-1-2-1v-2h-10 1v-1c3-1 7 0 10 0h1v-1l-1-2c4 1 5 3 8 5-1-2-3-4-4-5l-2-2v-1z" class="G"></path><path d="M179 191h10v2l2 1-1 1h3 1c1-1 0-1 2-1l-1 2v4c0 1 0 3-1 4l-3 1c-1 0-3 0-4-1h-1c-1 1-3 1-5 0-2 1-4 1-7 1h-1l-1-1v-3l-1-2c-2-1-3-1-4-2-2-3-3-3-6-3l8-2 5-1h1 1 3z" class="B"></path><path d="M186 202l-1 1h-2l-1-1c1-1 2-1 4-1v1z" class="T"></path><path d="M171 199l1-2c2-1 4-2 6-1 1 0 2 0 2 1 1 2 0 4 1 6v1c-2 1-4 1-7 1h-1l-1-1v-3l-1-2z" class="F"></path><path d="M173 205l1-1c-1-1-1-1 0-2h1c0-1 0 0 1-1 1 1 2 2 4 2h1v1c-2 1-4 1-7 1h-1z" class="j"></path><path d="M185 198l1-2c1-2 2-2 3-3l2 1-1 1h3 1c1-1 0-1 2-1l-1 2v4c0 1 0 3-1 4l-3 1c-1 0-3 0-4-1v-1l-1-1v-1c-1-1-1-2-1-3z" class="h"></path><path d="M185 198c1 1 2 1 4 2h3l1 1h-3l2 2h-3l-1-1c-1 0-1 0-1 1l-1-1v-1c-1-1-1-2-1-3z" class="F"></path><path d="M185 198l1-2c1-2 2-2 3-3l2 1-1 1h3 1c1-1 0-1 2-1l-1 2-2 1v1l1 1h0-2 0v1h-3c-2-1-3-1-4-2z" class="P"></path><path d="M457 378v-2c-1-3-1-8-1-11l1-2v-1c1-1 2-2 2-4 0-1 1-1 2-2h1c3-2 5-4 9-3l1 1h3l-1-1 1-1v1c5 3 8 8 10 13 1 6 1 11-1 17h0c-1 4-4 7-7 9-3 0-6 1-8 0-2 0-3-2-3-3l-1 1 2 6c-1 3 0 10-2 12h0l-2-7c0-5-3-12-5-17l1-1c0-2-1-3-2-5z" class="C"></path><path d="M463 381l-2-6c0-3-1-6-1-9 1-2 2-7 4-9 2-1 5 0 7 0 1 1 2 2 2 4-2-1-4-2-7-1-2 1-3 3-4 5-2 5 1 12 3 16h-2z" class="O"></path><defs><linearGradient id="D" x1="479.121" y1="362.168" x2="474.379" y2="369.832" xlink:href="#B"><stop offset="0" stop-color="#605f5f"></stop><stop offset="1" stop-color="#7d7c7a"></stop></linearGradient></defs><path fill="url(#D)" d="M471 357c3 1 6 3 8 6l3 6h0c1 3 0 6 0 8l1-1c0 1 1 1 0 2v1c-1 0-1 0-1 1s-1 2-2 2h0v-1-2c-1 0-1 0-1 1h-1l1-5c0-4-1-8-4-12l-2-2c0-2-1-3-2-4z"></path><path d="M475 353c5 3 8 8 10 13 1 6 1 11-1 17h0c-1 4-4 7-7 9-3 0-6 1-8 0-2 0-3-2-3-3l-2-3c0-2 0-3-1-5h2l2 2c1 2 3 2 6 2 2-1 4-3 5-5h1c-1 1-1 2-2 3-2 3-5 3-8 3h0l-1 1c1 0 1 2 2 2 3 1 6 0 8-2 1 0 2 1 3 0 2-2 3-6 4-9 0-4 0-7-1-10l-1-3v-1-1h-1c-1-3-2-5-4-7l-3-2v-1z" class="X"></path><defs><linearGradient id="E" x1="464.338" y1="381.592" x2="458.958" y2="394.036" xlink:href="#B"><stop offset="0" stop-color="#5a5958"></stop><stop offset="1" stop-color="#747571"></stop></linearGradient></defs><path fill="url(#E)" d="M457 378l1-1 1-1-1-2c0-4 0-8 1-12l1-3c1 2 0 5-1 7-1 5 1 10 3 15l3 9 2 6c-1 3 0 10-2 12h0l-2-7c0-5-3-12-5-17l1-1c0-2-1-3-2-5z"></path><path d="M465 381c-2-4-5-11-3-16 1-2 2-4 4-5 3-1 5 0 7 1l2 2c3 4 4 8 4 12l-1 5c-1 2-3 4-5 5-3 0-5 0-6-2l-2-2z" class="w"></path><path d="M520 283c1 0 2 0 3 1h5l1 1v1 1l3 1v-2h2l3 3h0c1 0 5 2 5 3 2 1 5 2 7 3 0 2-1 3-2 4-1 0-2 1-3 1 0 1-1 2-1 3v1 2l-1 1-15-1h-14l-6 1c-2-1-3 0-5 0h-11c2 2 5 6 6 9-2 2-1 4-2 6v-1c0-1 1-5 1-6-1-4-6-6-7-10h1c0-6-4-5-7-8l5-2c0-1 1-2 1-2 3-2 6-3 9-4 2-1 4-1 6-2l1-1h1 3 8l-2-2 1-1h4z" class="g"></path><path d="M515 299c1 0 4 0 6-1h0c3 2 7 0 11 1h-6v1 1c1 0 1 0 1 1l1 1h-12l-1-1-1-2 1-1z" class="B"></path><path d="M532 299h5c2 1 4 1 6 1h1c0 1-1 2-1 3v1c-5-1-10-1-15-1l-1-1c0-1 0-1-1-1v-1-1h6z" class="T"></path><path d="M490 294h1c3-2 7-2 11-2 7 0 13 1 20 1 5 0 11-1 17 0-2 0-3 1-4 0-2 0-3-1-4 0-2 0-4 0-6 1-6 1-12-1-18-1-1 0-3 0-4 1h9c2 0 3 0 4 1-2 1-10-1-12 1h1c3 0 5 0 7 1l1 1-1 1h0-1l-20 1-1 1 1 1s1 1 1 2l-2 1c0-6-4-5-7-8l5-2 2-1z" class="J"></path><path d="M490 294v1c-1 0-1 0-2 1 3 0 4 0 6 1h3v1h3c1-1 1-1 2 0 2 0 3-1 5 0h1 3l1 1h-1l-20 1-1 1 1 1s1 1 1 2l-2 1c0-6-4-5-7-8l5-2 2-1z" class="n"></path><path d="M512 299h0l1-1 2 1-1 1 1 2 1 1h12c5 0 10 0 15 1v2l-1 1-15-1h-14l-6 1c-2-1-3 0-5 0h-11c2 2 5 6 6 9-2 2-1 4-2 6v-1c0-1 1-5 1-6-1-4-6-6-7-10h1l2-1c0-1-1-2-1-2l-1-1 1-1 20-1h1z" class="U"></path><path d="M512 299h0l1-1 2 1-1 1 1 2-2 1v-1c-1 0-1 0-1 1l-1-1h-1v1c-1-1-1 0-2-1l-1 1h-2l-1-1c-2 1-2 1-4 1v-1l-1 1-1-1-2 1h0-2v-1c-2 1 0-1-1 1l-1 1c0-1-1-2-1-2l-1-1 1-1 20-1h1z" class="E"></path><path d="M520 283c1 0 2 0 3 1h5l1 1v1 1l3 1v-2h2l3 3h0c1 0 5 2 5 3h0c2 2 5 3 7 4-4-1-7-2-10-3-6-1-12 0-17 0-7 0-13-1-20-1-4 0-8 0-11 2h-1l-2 1c0-1 1-2 1-2 3-2 6-3 9-4 2-1 4-1 6-2l1-1h1 3 8l-2-2 1-1h4z" class="k"></path><path d="M520 283c1 0 2 0 3 1h5l1 1v1 1l-12-1-2-2 1-1h4z" class="F"></path><path d="M510 288h7 7c1 0 3 1 5 1h1v1h2v1h-2c-7-2-15-1-23-2h0l3-1z" class="w"></path><path d="M441 381c1-1 2-3 3-4 2-3 5-4 9-4h1c1-3 0-7 1-10h0v-2c1-4 4-7 7-9h1c1 0 1-1 2-1 3-1 7 0 10 1l-1 1 1 1h-3l-1-1c-4-1-6 1-9 3h-1c-1 1-2 1-2 2 0 2-1 3-2 4v1l-1 2c0 3 0 8 1 11v2c1 2 2 3 2 5l-1 1c2 5 5 12 5 17v1 8 5c-2 2 0 5-1 7v-6l-2 1-1-2-1-1 1-1v-2c-2-1-3 0-5 0h-2c0-1-1 0-1 0-1-1-1-1-2-1h-1-7l-23 1h-5-5c-2-1-2-2-3-3v-4l3-1c-1-2-1-2 0-3h13c3 0 6-1 8 0l15 1c-2-3-3-6-4-9 0-4 0-7 1-11z" class="v"></path><path d="M442 401h9c2 0 5 0 7 2h-1l-4 1c-1-1-3-1-4-1-2-1-5-1-7-2z" class="s"></path><path d="M446 383c2-2 3-2 5-2 1 0 2 0 3 1v3c-1-2-2-3-5-2-1 0-2 0-3 2s-1 7 0 10c1 1 2 4 3 5h1l1 1h-9-5-11l3-1 15 1c-2-3-3-6-4-9 1 1 1 2 2 3 0 1 1 2 2 2s1 1 2 1h0l-2-7v-2-1c0-2 1-4 2-5z" class="e"></path><path d="M443 381h1 2 0v2c-1 1-2 3-2 5v1 2l2 7h0c-1 0-1-1-2-1s-2-1-2-2c-1-1-1-2-2-3 0-4 0-7 1-11v1 3 1h1l1-5z" class="P"></path><path d="M450 400h-1c-1-1-2-4-3-5-1-3-1-8 0-10s2-2 3-2c3-1 4 0 5 2h1v2c2 4 3 9 4 14l-9-1z" class="w"></path><path d="M405 404l3-1c1 1 5 1 7 1 9 0 18-1 27 0h11l4-1v1 1c1 0 0 0 1 1l-1 1h0c1 1 2 2 2 4-2-1-3 0-5 0h-2c0-1-1 0-1 0-1-1-1-1-2-1h-1-7l-23 1h-5-5c-2-1-2-2-3-3v-4z" class="T"></path><path d="M451 405h2l1 1c-2 1-4 1-6 1v1h-14c-1-1-3-1-4-1-4 0-9 0-12-1h33v-1z" class="J"></path><path d="M405 404l3-1c1 1 5 1 7 1 9 0 18-1 27 0-4 0-8 0-12 1h-1c-5 1-11 0-16 0-1 0-4-1-4 0h-1v2 1h11 5 0c-2 0-4 0-5 1h3l3 1h-2-5v1h-5-5c-2-1-2-2-3-3v-4z" class="R"></path><path d="M453 404l4-1v1 1c1 0 0 0 1 1l-1 1h0c1 1 2 2 2 4-2-1-3 0-5 0h-2c0-1-1 0-1 0-1-1-1-1-2-1h-1-7l-23 1v-1h5 2l-3-1h-3c1-1 3-1 5-1h0 10 14v-1c2 0 4 0 6-1l-1-1h-2-21c4-1 8-1 12-1h11z" class="G"></path><path d="M422 409h25l1 1h-7l-23 1v-1h5 2l-3-1z" class="D"></path><path d="M441 381c1-1 2-3 3-4 2-3 5-4 9-4h1c1-3 0-7 1-10h0v-2c1-4 4-7 7-9h1c1 0 1-1 2-1 3-1 7 0 10 1l-1 1 1 1h-3l-1-1c-4-1-6 1-9 3h-1c-1 1-2 1-2 2 0 2-1 3-2 4v1l-1 2c0 3 0 8 1 11v2c1 2 2 3 2 5l-1 1c2 5 5 12 5 17v1 8 5c-2 2 0 5-1 7v-6l-2 1-1-2-1-1 1-1v-2c0-2-1-3-2-4h0l1-1c-1-1 0-1-1-1v-1-1h1c-2-2-5-2-7-2l-1-1 9 1c-1-5-2-10-4-14v-2h-1v-3c-1-1-2-1-3-1-2 0-3 0-5 2v-2h0-2-1l-1 5h-1v-1-3-1z" class="R"></path><path d="M443 381c1-1 2-3 4-4h8c0 2 2 7 1 9h0l3 9h0c0 1 1 2 2 2v5c1 2 1 5 1 7l1 1v5c-2 2 0 5-1 7v-6l-2 1-1-2-1-1 1-1v-2c0-2-1-3-2-4h0l1-1c-1-1 0-1-1-1v-1-1h1c-2-2-5-2-7-2l-1-1 9 1c-1-5-2-10-4-14v-2h-1v-3c-1-1-2-1-3-1-2 0-3 0-5 2v-2h0-2-1z" class="a"></path><path d="M459 395c0 1 1 2 2 2v5c1 2 1 5 1 7l1 1v5c-2 2 0 5-1 7v-6l-3-21z" class="E"></path><path d="M503 278c2-11 5-22 7-33l4-41 1-13c1-3 1-7 2-10 1 4 0 9 1 13l2 18c1 9 1 18 3 27l-2 2c0 6 2 13 3 19l2 5c1 3 1 5 2 8 0 3 1 6 3 9v1c0 1 1 1 2 2l1 1h-2v2l-3-1v-1-1l-1-1h-5c-1-1-2-1-3-1h-4l-1 1 2 2h-8-3-1l-1 1v-2c2-3 1-8 2-11h-1l-1 4h-1z" class="I"></path><path d="M513 246h0c-1-1 0-2 0-3v-2-2c1-5 1-10 1-14l2-16v-7l1 1v19l-1-3v7 4c-1 2-1 4-1 6l-2 10z" class="G"></path><path d="M516 230v-4-7l1 3 2 21c0 3 0 6 1 8l-3 3v-1l-1-2v1l-2 9h-1c-1 6-2 13-4 19l-3 6h-1c5-9 5-20 6-30 1-4 1-7 2-10l2-10c0-2 0-4 1-6z" class="Z"></path><path d="M516 230v-4-7l1 3 2 21c0 3 0 6 1 8l-3 3v-1l-1-2v1l-2 9h-1c0-3 1-7 2-10 0-7 1-14 1-21z" class="L"></path><path d="M517 203c2 2 0 5 1 7v1s0 1 1 2v5l1 6v4l1 6v7c0 6 2 13 3 19l2 5c1 3 1 5 2 8 0 3 1 6 3 9v1c0 1 1 1 2 2l1 1h-2v2l-3-1v-1-1l-1-1-2-3-1-3v1l-3-13c-1-1-1-3-2-4h-4c0-1 0-3 1-4h4l-1-7c-1-2-1-5-1-8l-2-21v-19z" class="o"></path><path d="M516 262c0-1 0-3 1-4h4l4 20v1l-3-13c-1-1-1-3-2-4h-4z" class="I"></path><path d="M524 260l2 5c1 3 1 5 2 8 0 3 1 6 3 9v1c0 1 1 1 2 2l1 1h-2v2l-3-1v-1-1h0v-1l-3-10v-1-2l-2-11z" class="T"></path><path d="M514 261l2-9v-1l1 2v1l3-3 1 7h-4c-1 1-1 3-1 4h4c1 1 1 3 2 4l3 13v-1l1 3 2 3h-5c-1-1-2-1-3-1h-4l-1 1 2 2h-8-3l3-6c2-6 3-13 4-19h1z" class="l"></path><path d="M516 262h4c1 1 1 3 2 4-2 1-4 1-5 1-1 1-1 1-1 3l1 6c2 1 5 3 8 3v-1l1 3c-4 0-8-2-12-3 0-6 1-10 2-16z" class="B"></path><path d="M522 266l3 13c-3 0-6-2-8-3l-1-6c0-2 0-2 1-3 1 0 3 0 5-1z" class="Z"></path><path d="M513 261h1c0 2-1 5-1 7v12h1c1 1 1 2 2 2l4 1h-4l-1 1 2 2h-8-3l3-6c2-6 3-13 4-19z" class="i"></path><path d="M509 280l3 2c0 2-2 3-3 4h-3l3-6z" class="Q"></path><path d="M537 442c5 0 10 0 14 2l2 1c0 1 1 1 1 2-1 1-1 1-1 2l-5 5h0c0 1-1 3-2 5h-5v1l1 1c-1 1-1 1-3 1l-1-1v-1c-1 2-1 4-1 6-1-1-1-2-1-4-1 1-1 1-1 2h-1l-1-2h0c0 1-1 2-1 2-1 0 0 0-1-1v-1l-1 1c-1-1-1-2-1-2l-1 1-1-1c-1 1-2 2-4 2 0-1 0-1-1-2h-1-1c-1 1-1 3-1 5h-1v-3c-2-1-1-1-2 0l-1 3h-1v-4h-1v1c-1 1-1 1-2 1l-1-3c-1-1-1-2-2-2h-3l-1-1h-1-7l-5 1h-7c-1-1-2-3-3-5v1c-2-2-2-3-5-4v-1c-1-1-1 0-1-1h15c-3-1-6-1-9-1-2 0-3-1-5-1h0c0-1 0-2 1-3 1-2 5-1 7-1h4l6-1h18 25z" class="o"></path><path d="M476 450h11v2h0-3v1h-1l-1-1c0 1 0 2-1 2v1c-2-2-2-3-5-4v-1z" class="V"></path><path d="M539 450c3 0 8 1 11 1 1-1 2-1 3-2l-5 5h0l-1-1-1 1c0-1 0-1-1-1h-1-2v-1h-8-1v-1c2-1 4 0 6-1z" class="P"></path><path d="M499 450c6-1 14 0 21 0 6 0 12-1 19 0-2 1-4 0-6 1v1h1-7c-4-1-12-1-17 0h-4v1l-1-1c-2 0-3 0-4 1v-1h0c1 0 1-1 2-1l-4-1z" class="M"></path><path d="M484 443h4c3 1 7 0 10 0-2 1-7 0-7 1h5c-4 1-10 2-14 1v1h13 4l-1 1h-4-13v1c-2 0-3-1-5-1h0c0-1 0-2 1-3 1-2 5-1 7-1z" class="T"></path><path d="M494 442h18v1l21 1c5 0 9-1 14 0h1c1 1 1 1 2 1-1 1-7 0-9 0-4-1-8 0-12 0l-16-1h-17-5c0-1 5 0 7-1-3 0-7 1-10 0l6-1z" class="H"></path><path d="M487 450h12l4 1c-1 0-1 1-2 1h0v1c1-1 2-1 4-1l1 1v-1h4c5-1 13-1 17 0h7 8v1h2 1c1 0 1 0 1 1l1-1 1 1c0 1-1 3-2 5h-5v1l1 1c-1 1-1 1-3 1l-1-1v-1c-1 2-1 4-1 6-1-1-1-2-1-4-1 1-1 1-1 2h-1l-1-2h0c0 1-1 2-1 2-1 0 0 0-1-1v-1l-1 1c-1-1-1-2-1-2l-1 1-1-1c-1 1-2 2-4 2 0-1 0-1-1-2h-1-1c-1 1-1 3-1 5h-1v-3c-2-1-1-1-2 0l-1 3h-1v-4h-1v1c-1 1-1 1-2 1l-1-3c-1-1-1-2-2-2h-3l-1-1h-1-7l-5 1h-7c-1-1-2-3-3-5 1 0 1-1 1-2l1 1h1v-1h3 0v-2z" class="D"></path><path d="M487 450h12l4 1c-1 0-1 1-2 1h0c-3 0-7-1-10 0-1 1-2 1-3 1l-1-1v-2z" class="O"></path><path d="M542 452v1h2 1c1 0 1 0 1 1l1-1 1 1c0 1-1 3-2 5h-5-1c-3 0-5-1-7-1l-1-3c1 0 1-1 2-1h0l1-1h1l1 1v-1c2 0 2 0 4 1l1-2z" class="T"></path><path d="M535 453h1v4h1c0-1 0-2 1-3h0c1 1 1 1 1 2s1 2 1 3c-3 0-5-1-7-1l-1-3c1 0 1-1 2-1h0l1-1z" class="s"></path><path d="M503 458c-4-1-8 1-11 0l-2-2c0-1 0-1 1-2h1c0 2 0 2 1 3h3v-2h1l1 1-1 1h1 5v-2l1-1 2 1 1-1c1 1 4 1 6 0 1 0 1 0 2 1 1-1 2-1 4-1h7v2 1h1v-2c0-1 0-1 2-2h2 4l-1 1h0c-1 0-1 1-2 1l1 3c2 0 4 1 7 1h1v1l1 1c-1 1-1 1-3 1l-1-1v-1c-1 2-1 4-1 6-1-1-1-2-1-4-1 1-1 1-1 2h-1l-1-2h0c0 1-1 2-1 2-1 0 0 0-1-1v-1l-1 1c-1-1-1-2-1-2l-1 1-1-1c-1 1-2 2-4 2 0-1 0-1-1-2h-1-1c-1 1-1 3-1 5h-1v-3c-2-1-1-1-2 0l-1 3h-1v-4h-1v1c-1 1-1 1-2 1l-1-3c-1-1-1-2-2-2h-3l-1-1h-1z" class="S"></path><path d="M504 458c9-1 19 1 29 0 2 0 4 1 7 1h1v1l1 1c-1 1-1 1-3 1l-1-1v-1c-1 2-1 4-1 6-1-1-1-2-1-4-1 1-1 1-1 2h-1l-1-2h0c0 1-1 2-1 2-1 0 0 0-1-1v-1l-1 1c-1-1-1-2-1-2l-1 1-1-1c-1 1-2 2-4 2 0-1 0-1-1-2h-1-1c-1 1-1 3-1 5h-1v-3c-2-1-1-1-2 0l-1 3h-1v-4h-1v1c-1 1-1 1-2 1l-1-3c-1-1-1-2-2-2h-3l-1-1z" class="K"></path><path d="M673 300c0-7-5-12-7-19 3 3 4 6 6 10-1-2-2-6-1-8 0-3-1-7-1-11v-16-1c2 2 2 5 2 8 1 7 0 17 3 23 1 2 3 4 5 5l1 1-1-1v-3-1-1-1c0-1 0-2-1-2v-3c-1-2-1-2-1-3-1-2 0-7 0-9 1-1 0-4 0-6l2-2c1 6 0 12 1 17v1c0 2 1 3 1 5 1 2 1 5 2 7h0v4 1l2 3 3 1h-3l-1 2h1c0 3 0 6 1 8 0 2 1 3 2 5 1 3 2 7 5 10v3h-1v1 1c1 2 3 5 4 7 1 1 1 1 1 2l2 4v1l5 9 1 1c0 1 1 2 1 3l1 2c1 1 1 1 1 2 1 3 3 5 4 7l1 2 2 2c1 1 0 0 1 2l1 1 1 2 1 2c2 1 3 2 4 4l5 6c1 2 3 5 5 7 2 1 4 2 5 4 1 0 2-1 3-2l1-1c1 1 1 0 1 1h0l2-1c3 0 3 1 4 3 1 1 2 2 2 4h0l-1 3c0 1 0 1-1 1-1 1-3-1-4 1l2 2v1h0l-1-1c-1 0-2 0-3-1s-3-4-5-4c1 1 1 1 1 2v1 2c0-1 0 0-1-1v2h-1l-1-1c-1-2-4-5-4-7-1-2-2-3-4-5v2l1 1v1-1c-1-1-2-2-4-3h0c-1-2-2-4-4-5l-7-10-12-19-13-23c-2-5-4-10-7-14-1-1-1-2-2-2-1-1-2-1-4-2l3-1c-1-7-5-15-7-22z" class="J"></path><path d="M725 392h2l1 2 1 1c1 1 3 3 3 4 1 2 3 3 4 5 1 1 2 3 3 4h1v2c0-1 0 0-1-1v2h-1l-1-1c-1-2-4-5-4-7-1-2-2-3-4-5l-4-6z" class="N"></path><path d="M719 378h1c2 1 3 2 4 4l5 6c1 2 3 5 5 7 2 1 4 2 5 4 1 0 2-1 3-2l1-1c1 1 1 0 1 1h0l2-1c3 0 3 1 4 3 1 1 2 2 2 4h0l-1 3c0 1 0 1-1 1-1 1-3-1-4 1l2 2v1h0l-1-1c-1 0-2 0-3-1v-1c0-2-1-3-3-3-2-1-3-4-4-5l-3-3c-5-6-11-12-15-19z" class="E"></path><path d="M746 396c3 0 3 1 4 3 1 1 2 2 2 4h0c-2 1-4 2-6 2-2-1-3-2-4-4 0-2 1-2 2-4l2-1z" class="n"></path><path d="M680 260c1 6 0 12 1 17v1c0 2 1 3 1 5 1 2 1 5 2 7h0v4 1l2 3 3 1h-3l-1 2h1c0 3 0 6 1 8 0 2 1 3 2 5 1 3 2 7 5 10v3h-1v1 1c1 2 3 5 4 7 1 1 1 1 1 2l2 4v1l5 9 1 1c0 1 1 2 1 3l1 2c1 1 1 1 1 2 1 3 3 5 4 7l1 2 2 2c1 1 0 0 1 2l1 1 1 2 1 2h-1c-3-5-7-10-11-15-1-3-2-6-4-9-4-7-8-15-12-23-1-2-3-5-3-7-1-3-3-6-4-9 0-4 0-8-1-12 0-1-1-1-1-1 0-2 0-3-1-5v-2l-4-2c1 0 1 0 2-2l1 1-1-1v-3-1-1-1c0-1 0-2-1-2v-3c-1-2-1-2-1-3-1-2 0-7 0-9 1-1 0-4 0-6l2-2z" class="B"></path><path d="M673 300c0-7-5-12-7-19 3 3 4 6 6 10-1-2-2-6-1-8 0-3-1-7-1-11v-16-1c2 2 2 5 2 8 1 7 0 17 3 23 1 2 3 4 5 5-1 2-1 2-2 2l4 2v2l-2 2c1 1 1 2 2 4h0c-1 2 0 3 0 5 1 3 1 5 1 9h1v2l2 4c0 1 1 2 1 3 1 1 1 1 1 2l1 1c1 1 1 2 0 4 9 19 19 35 31 52 1 1 4 6 5 7l4 6v2l1 1v1-1c-1-1-2-2-4-3h0c-1-2-2-4-4-5l-7-10-12-19-13-23c-2-5-4-10-7-14-1-1-1-2-2-2-1-1-2-1-4-2l3-1c-1-7-5-15-7-22z" class="k"></path><path d="M689 333l-5-11c-2-4-9-26-8-29h2l4 2v2l-2 2c1 1 1 2 2 4h0c-1 2 0 3 0 5 1 3 1 5 1 9h1v2l2 4c0 1 1 2 1 3 1 1 1 1 1 2l1 1c1 1 1 2 0 4z" class="N"></path><defs><linearGradient id="F" x1="428.35" y1="195.129" x2="327.95" y2="163.905" xlink:href="#B"><stop offset="0" stop-color="#060605"></stop><stop offset="1" stop-color="#444242"></stop></linearGradient></defs><path fill="url(#F)" d="M248 174h189 2c-1 3-5 9-6 11l-11 20h-31c-1-1-1-1-1-3 2-1 4-3 5-4l-2-3c0-1 0-2-1-3l-2-1c-4-4-7-4-12-5-1-1-4-1-5-1l-2-2h-3-1l-2-1h-1c-6-1-11-2-17-1h0c-2-1-3-1-5-1h0 5v-1h-9v-1h9l-7-1v-1h-4c-2-2-8-1-11-1h12l-89-1z"></path><path d="M437 174h2c-1 3-5 9-6 11h-1c0-3 0-3-1-5-1 0-2 0-3 1h-1-1v-1h1c2 0 4-1 4-3 0-1-1-1-2-2 2 0 5 0 7-1h1z" class="p"></path><path d="M383 178c3 0 6 0 9 1l4 2h-3 0l1 1v1s-1 1-2 1-3 0-5-1c-1-1-2-1-3-1v-1c-3-1-6-1-9-1l-1-1c3 0 7 1 9-1z" class="f"></path><path d="M384 181c2 0 4 0 7 1h3v1s-1 1-2 1-3 0-5-1c-1-1-2-1-3-1v-1zm-37-2h6c4 1 6 1 10 0v-1l1 1c1-1 2-1 3-1h16c-2 2-6 1-9 1l1 1c-3 0-8 0-10 2h-1c-6-1-11-2-17-1h0c-2-1-3-1-5-1h0 5v-1z" class="m"></path><path d="M375 180c3 0 6 0 9 1v1c1 0 2 0 3 1 2 1 4 1 5 1h-2 0c1 1 1 0 2 1h2v1c-2 0-2 1-3 2 0 1 0 1 1 2v2l-2-1c-4-4-7-4-12-5-1-1-4-1-5-1l-2-2h-3-1l-2-1c2-2 7-2 10-2z" class="f"></path><path d="M375 180c3 0 6 0 9 1v1h-8-8l-1 1-2-1c2-2 7-2 10-2z" class="K"></path><path d="M413 195c-2 0-5 0-7-1s-4-4-4-6c0-3 1-5 2-7 2-2 4-3 7-3 2 0 4 0 5 2 2 2 3 4 3 7 1 2 0 4-2 5-1 2-3 2-4 3z" class="G"></path><path d="M413 195v-2l2-2c2-2 1-4 1-7 0-1-1-1-1-2v-1c-2 0-3-1-4-1-2 0-3 1-5 2l-1 1h-1c1-2 3-3 5-4 2 0 4 0 6 1l1 1v-1c2 2 3 4 3 7 1 2 0 4-2 5-1 2-3 2-4 3z" class="a"></path><path d="M408 184c2 0 4-1 5 0 2 2 2 2 2 5-1 1-1 2-3 3h-2c-2 0-3-1-4-2 0-1-1-3-1-4 1-2 2-2 3-2z" class="b"></path><path d="M409 186h0c1 0 1 1 2 1l1-1c1 1 1 1 1 3h-1c-2 0-2 1-4 0 0-2 0-2 1-3z" class="U"></path><path d="M848 174h46 19l-9 15c-3 4-6 9-8 14v1h1l5 5 1 2c-3-1-6-3-9-4h-10c0 1-1 1-1 2h0-1c-1 0-1 1-2 1-2 1 0 3-1 4-2-2-5-4-7-5-7-6-14-10-23-13l-8-3h2 1c1-1 2-1 3-2h-3c-1-1-2-1-4-1v-1h3c1-1 2-1 2-2 2 0 3 1 5 0l2 1h0 0c2-2 4-3 6-5-3 0-7 0-10-1-1 0-3 0-4 1l2-6h0v-1l2-2z" class="r"></path><path d="M863 187c13 3 24 8 33 16v1c-2-1-4-3-6-4-3 0-6-2-10-4-3-2-7-3-11-4-1-1-2-1-4-2-1 0-3-1-4-2h0l2-1z" class="T"></path><path d="M848 174h46c-5 1-11 1-16 1-3 0-9 1-12 0h-2l1 1h9v1l-3 1 1 1h9 0-17c-1 1-1 1-1 2l-1 3c-1-1-3-1-4-1-3 0-7 0-10-1-1 0-3 0-4 1l2-6h0v-1l2-2z" class="c"></path><path d="M848 182c5-1 10-1 15-1l-1 3c-1-1-3-1-4-1-3 0-7 0-10-1z" class="C"></path><path d="M858 183c1 0 3 0 4 1l-1 3h2l-2 1h0c1 1 3 2 4 2 2 1 3 1 4 2 4 1 8 2 11 4 4 2 7 4 10 4 2 1 4 3 6 4h1l5 5 1 2c-3-1-6-3-9-4h-10c0 1-1 1-1 2h0-1c-1 0-1 1-2 1-2 1 0 3-1 4-2-2-5-4-7-5-7-6-14-10-23-13l-8-3h2 1c1-1 2-1 3-2h-3c-1-1-2-1-4-1v-1h3c1-1 2-1 2-2 2 0 3 1 5 0l2 1h0 0c2-2 4-3 6-5z" class="E"></path><path d="M858 183c1 0 3 0 4 1l-1 3c-1 0-2-1-3 0-1 0-2 1-3 2s-1 1-2 1l-1-2c2-2 4-3 6-5z" class="H"></path><path d="M845 187c2 0 3 1 5 0l2 1h0v1l-2 2-1-1-2 1h0-3c-1-1-2-1-4-1v-1h3c1-1 2-1 2-2z" class="C"></path><path d="M847 191l3 2h0 4l1 2h0c-2 0-3 0-4 1h-2l-8-3h2 1c1-1 2-1 3-2h0z" class="B"></path><path d="M869 192c4 1 8 2 11 4 4 2 7 4 10 4 2 1 4 3 6 4h1l5 5 1 2c-3-1-6-3-9-4h-10c0 1-1 1-1 2h0l-3-2c0-1 0-1 1-1 0-1 0-2 1-2-1-1-1-1-2 0h0-1l-1-1v-1c-1 0-2-1-3-2l1-1 1 1 1-2c0-1-2-2-3-2-2-2-5-2-7-3l1-1z" class="g"></path><path d="M881 206l2-2c0-1 0-1-1-2v-1c2 0 4 2 5 3 0 0 1 1 2 1h2v1c-2 0-5 0-7 1h0c0 1-1 1-1 2h0l-3-2c0-1 0-1 1-1z" class="T"></path><path d="M654 222c0-1 2-2 3-2l1-1c1 0 1 0 2-1h1c3 0 8-1 11 0h3 0l1 1 2 1c0 2 1 3 3 4h4v1h0l1 1c1 1 1 1 1 2 1-1 2-1 3-1v-1l4 1c2 0 3 1 6 1v-1l4-3 1 1c1-1 2-1 2-2v-2l1-1v1c1 1 1 3 0 4v2c-1 2-1 3-2 5l-3 6c-1 4-2 10-5 14v12 3c-1 2-1 4-2 7 1 2 0 4 1 6 0 2 2 4 3 6s1 5 2 7l-1 1v2l-1 2 1 2-1 2c0 1 0 1 1 2v1 1l-1-1c-1 0-1-1-2-1h-1c-1 1-2 1-3 1 3 1 5 1 6 4 1 2 1 3 0 5 0 0 1 0 1 1l1 4c-1 0-1 1-2 1s-1 1-2 1l-1 1c-1 2-2 2-3 2-3-3-4-7-5-10-1-2-2-3-2-5-1-2-1-5-1-8h-1l1-2h3l-3-1-2-3v-1-4h0c-1-2-1-5-2-7 0-2-1-3-1-5v-1c-1-5 0-11-1-17v-4c1 0 1 0 1-1v-2c1-2 1-6 0-8v-1c0-1 0-2-1-3 0-1 0-2-1-4h-1l-4-6 1-1c0-2 1-2 2-3l1-1v-1h-4 0l1-2v-1l-1-1-2 1c-1 0-2 0-3-1-1 0-2 0-3-1-1 0-4 0-5 1-3 0-5 1-7 2v-1z" class="s"></path><path d="M675 230c0-2 1-2 2-3 1 0 1 1 1 2s-1 1-1 2c-1-1-2-1-2-1zm26 85l1 4c-1 0-1 1-2 1-1-1 0-2-1-3 1-2 1-1 2-2z" class="o"></path><path d="M684 275v-1c0-2 1-2 2-3 2-1 3-1 5 0l-1 2h1c-1 1-2 1-3 1-1 1-2 2-4 2v-1z" class="a"></path><path d="M691 271c1 0 2 1 3 2v3c0 1 0 1 1 2-2 1-3 3-5 4-1 0-2 0-2-1h0c1-1 3-1 4-2h-1v-2h-1l1-1v-1c-2 0-2 0-3-1 1 0 2 0 3-1h-1l1-2z" class="X"></path><path d="M688 274c1 1 1 1 3 1v1l-1 1h1v2h1c-1 1-3 1-4 2h0-2c-2-2-2-4-2-6v1c2 0 3-1 4-2z" class="j"></path><path d="M694 305c3 1 5 1 6 4 1 2 1 3 0 5-2 0-3 1-4 1s-2 0-3-1-2-4-2-5c1-2 2-3 3-4z" class="O"></path><path d="M684 255c1-1 1-2 3-3 1 0 3 0 4 1 2 1 3 3 3 5 1 1 0 3-1 5-1 0-2 1-2 1-2 0-3 0-4-1-2-1-3-1-3-3-1-2-1-3 0-5z" class="G"></path><path d="M684 255c1 0 2 1 2 2h3v-2l-1-1h1l4 4c-1 1-2 1-3 2-2-1-2-1-3-1-1 1-1 1-2 1 0 2 1 2 2 3-2-1-3-1-3-3-1-2-1-3 0-5z" class="i"></path><path d="M684 290c2-2 4-2 6-3 3 0 4 0 6 2 1 1 2 3 2 5s-2 3-3 4c-3 1-4 2-6 1l-3-1-2-3v-1-4z" class="d"></path><path d="M684 290c2-2 4-2 6-3 1 1 0 1 1 2 0 1-1 1-1 2h-2c-1 0-1 0-1 1 1 1 1 2 2 2v1h-3l-1 1c1 1 1 1 1 2l-2-3v-1-4z" class="j"></path><path d="M690 287c3 0 4 0 6 2 1 1 2 3 2 5s-2 3-3 4c0-1 0-3-1-4l-3 1h0c-1-1 2-2 2-4l-2-2c-1-1 0-1-1-2z" class="h"></path><path d="M704 224l1 1c1-1 2-1 2-2v-2l1-1v1c1 1 1 3 0 4v2c-1 2-1 3-2 5l-3 6c-1 4-2 10-5 14v12 3c-1 2-1 4-2 7v-2c0-1 0-2-1-2v-1l1-4v-3c1-1 1-2 1-3v-1c0-2 1-3-1-4l1-1v-1c0-1 1-3 1-4 1-1 1-1 1-2l1-1c0-2-1-4-1-6 0 0 0-1-1-1 0-1 0-4 1-5l3-3 3-3-1-1c-1 1-2 1-3 1h-1l4-3z" class="T"></path><path d="M267 531c0-1 1-2 0-3 0-2-1-6-1-8h1l2 7h1c3 3 3 8 5 11v1l-1 1h1 1l1 1c1 1 0 1 1 2s1 2 1 4c1 0 1 0 1 1s1 1 1 2c1 1 2 3 3 4h1c-1-3-3-4-4-7h1c2 2 3 6 5 7h1l2 2c2 1 3 3 5 4 0-1-1-2-2-2-1-1-2-2-3-4h1v-1c0-1 0-2 1-3h0l2-2c1 0 2-1 2-2h2c0-2 0-1 1-2l-1-1 1-1 2 2h1l-1-1v-1l1 1 1 1h1c1-1 1-2 1-4v3h1c3 0 5 1 7 2s5 3 7 5c0 2 4 5 6 7l2 1v2s2 1 2 2c1 2 1 3 1 5h0c-1 2 0 4 0 6v10 1h-1v1c-1 1-1 2-2 3 0 2-2 3-4 4h0l-5 5h0l-1-1c0-1-1-1-1-2h-1l1-1 2-2v-1c-2 1-2 2-4 2l3-3-1-1c-1 2-2 2-3 3v1c0 1 0 0-1 1-8-8-17-15-25-24-3-3-5-6-8-9-2-3-3-8-6-10h0c-3-6-6-13-7-19z" class="C"></path><path d="M325 578l2 3-1 2-1-1-2 1h0v-1c1-1 1-2 2-4h0z" class="D"></path><path d="M321 574c3 0 4 1 6 3v2 2l-2-3c-1-1-1-1-1-2h-3c-2 2-4 3-5 6h-1 0c1-2 2-3 3-5l-1-1c2-1 3-1 4-2z" class="l"></path><path d="M300 564v1c1 1 1 2 2 2l1 1-1 1h0c-1 1-1 0-1 1-1-1-1-1-2-1l-1-2 1-2h0-1l-3 3-2 2h-1c0-1 1-2 1-2l-1-1h0c0-3 2-4 4-5 2 0 3 1 4 2z" class="E"></path><path d="M326 557l2 1v2s2 1 2 2c1 2 1 3 1 5h0c-1 2 0 4 0 6v10 1h-1v1c-1 1-1 2-2 3 0 2-2 3-4 4h0l-5 5h0l-1-1c0-1-1-1-1-2h-1l1-1h0l1 1c4-4 6-6 8-11l1-2v-2-2c-2-2-3-3-6-3-1 1-2 1-4 2l-1-2c-1-1-2-2-2-3-1-1-1-2-1-4l4-4c3-1 4 0 6-2l1-1 1-1c0-1 1-2 1-2z" class="P"></path><path d="M321 574c2-1 4-3 4-4 1 0 1 0 1 1v3l1 1c2 2 2 5 1 7l-1 3c-1 1-1 2-1 3l-1 3-1 1-5 5h0l-1-1c0-1-1-1-1-2h-1l1-1h0l1 1c4-4 6-6 8-11l1-2v-2-2c-2-2-3-3-6-3z" class="h"></path><path d="M325 591c1-1 2-3 3-4h0v-2c1-1 1-1 1-2 0-2 0-3 1-4v-1l-1-1c0-1 0-2-1-3h-1v-2c1-2 1-4 1-6l-1-1c0-2 0-2 1-3h2c1 2 1 3 1 5h0c-1 2 0 4 0 6v10 1h-1v1c-1 1-1 2-2 3 0 2-2 3-4 4h0l1-1z" class="F"></path><path d="M323 561l3 4c-2 3-4 8-7 9h-2-1c-1-1-2-2-2-3-1-1-1-2-1-4l4-4c3-1 4 0 6-2z" class="W"></path><path d="M314 571v-2l1-1c0-1 0-1 1-2h4v1l-1 1s-1 0-1 1c-1 2-2 3-1 5h-1c-1-1-2-2-2-3z" class="g"></path><path d="M305 540v3h1c3 0 5 1 7 2s5 3 7 5c0 2 4 5 6 7 0 0-1 1-1 2l-1 1-1 1c-2 2-3 1-6 2l-4 4c-1-1 0 0 0-2l-2 2c1-2 2-3 3-4h-2c-1 0-1 1-1 2h-1v3s-1 1-2 1c0-1-1-1-1-2h-3c0-1 0-1 1-1-1 0-1 0-2-1l-3-1c-1-1-2-2-4-2h0l-1-2c0-1-1-2-2-2-1-1-2-2-3-4h1v-1c0-1 0-2 1-3h0l2-2c1 0 2-1 2-2h2c0-2 0-1 1-2l-1-1 1-1 2 2h1l-1-1v-1l1 1 1 1h1c1-1 1-2 1-4z" class="D"></path><path d="M305 566c2-1 3-1 4-2l1-1c1-1 1-2 1-3-1-2-2-4-4-4-1-1-2 0-3 0l-1 1v-1l-1 1-1-1 2-2c3 0 4 0 6 1h1c2-1 3 1 5 0l1 2h0v1 1c1 0 1 1 1 2-1 0-2 2-3 2h-2c-1 0-1 1-1 2h-1v3s-1 1-2 1c0-1-1-1-1-2h-3c0-1 0-1 1-1z" class="Y"></path><path d="M305 540v3h1c3 0 5 1 7 2-1 1-2 2-2 3v3c2 1 3 2 4 4-2 1-3-1-5 0-1-2-3-3-5-4-1 0-2-2-2-2l-1 1v1l-1-1v-2h-3v-1c-1 1-2 1-2 2-2 1-2 1-4 1l2-2c1 0 2-1 2-2h2c0-2 0-1 1-2l-1-1 1-1 2 2h1l-1-1v-1l1 1 1 1h1c1-1 1-2 1-4z" class="B"></path><path d="M305 551c0-1 1-2 2-2l1-1c1 1 1 2 2 3h1c2 1 3 2 4 4-2 1-3-1-5 0-1-2-3-3-5-4z" class="Z"></path><path d="M313 545c2 1 5 3 7 5 0 2 4 5 6 7 0 0-1 1-1 2l-1 1-1 1c-2 2-3 1-6 2l-4 4c-1-1 0 0 0-2l-2 2c1-2 2-3 3-4 1 0 2-2 3-2 0-1 0-2-1-2v-1-1h0l-1-2c-1-2-2-3-4-4v-3c0-1 1-2 2-3z" class="D"></path><path d="M121 207c1-1 2-1 2-3-2 0-4 1-6 0-1 0-2 0-2-1-2-3-4-7-5-10l-11-19h137v1c-1 1-6 0-8 0-1 0-1 0-1 1h1l-1 1h0c0 3 2 6 4 8 1 0 2 0 3 1l-1 1-23-2c-3 0-6 1-9 0-2 0-4-1-6-1-1 0-1 0-2 1l-1-1v1l-5-2c-1 0-3 0-4-1l-1-1c-1 1-4 1-4 2l-1 1v1h0c-4 1-9 2-13 3-11 3-21 7-31 12-4 2-8 6-12 7z" class="p"></path><path d="M192 184c-1-1-3-2-4-2h-1 0c7 0 16-1 23 0-4 1-8 1-12 1 2 1 2 1 4 1v1h8c-3 0-6 1-9 0-2 0-4-1-6-1-1 0-1 0-2 1l-1-1z" class="H"></path><path d="M168 185v-4c0-2 2-2 3-3 5 0 7 1 11 3-1 1-4 1-4 2l-1 1c0-2-1-2-2-4-1 1-2 1-3 1l-1 1c-1-1-1-1-1-2l-1 1c0 1 0 2-1 3v1z" class="Z"></path><path d="M168 185v-1c1-1 1-2 1-3l1-1c0 1 0 1 1 2l1-1c1 0 2 0 3-1 1 2 2 2 2 4v1h0c-4 1-9 2-13 3 1-1 2-1 3-2h1v-1h0z" class="b"></path><path d="M210 182c4 1 9 1 13 2 2 0 6 1 8 1 1 0 2 0 3 1l-1 1-23-2h-8v-1c-2 0-2 0-4-1 4 0 8 0 12-1z" class="S"></path><path d="M345 198c1-1 1-3 2-4 4-4 11-8 16-9v2h1 0c0 1-1 2-2 2l1 1c2-2 5-3 8-4h6 1c5 1 8 1 12 5l2 1c1 1 1 2 1 3l2 3c-1 1-3 3-5 4 0 2 0 2 1 3-1 1-2 1-3 2-1 0-2 0-3 1h-1c0 1 0 1 1 2h0l6 3 2 2h1l7 7v1c-2 0-2-2-3-2l1 2h-1c-1-1-1-1-3-1h0v-2c-1 1-1 2-1 3l-1-1h-1c-1 1-1 1-2 1s-2 1-3 1l-4 4c2 0 2 0 4-1l1-1c2 0 3-1 4 0l-3 1c-3 2-6 4-8 6l-1 1c-1 0-1 0-2 1-2 1-4 1-6 1v1h-13c-1 2-1 4-1 5-1-1-1-3-2-5l-1-1c-2 0-2 0-3-1 0-2-3-4-4-6v-1c-1-1-2-3-2-4v-1h-1v1h-1l-1-3c-1-3-2-6-2-10 1-1 0-2 1-3v-1c1-2 2-7 3-9z" class="Y"></path><path d="M355 224l2-1c1 0 1 1 2 2v1c-2 0-3-1-5 0l-1 1c0-1 1-2 2-3z" class="R"></path><path d="M365 227c-1 0-1 1-2 1-1 1-1 2-1 4-1 1-1 1-2 1-2-1-4-1-6-1-1-1-3-2-3-3-2-3-1-6-1-8 2 1 4 1 5 3-1 1-2 2-2 3l1-1c2-1 3 0 5 0l1 1 1-1h4v1z" class="B"></path><path d="M353 227l1-1c2-1 3 0 5 0l1 1c-1 2-1 3-2 4-2 0-2 0-4-1-1-1-1-1-1-3z" class="L"></path><path d="M378 213l3-2 1-1h3l6 3c-1 1-1 1-2 1 0-1 0-1-1-1s-1 0-2-1h-1l-1 1c0 2 5 5 4 6l-1 1c-1-2-4-5-5-6 1 4 5 5 5 9h-3v1l-4-2v2h-1c-2-1-3-1-5-1h-1c-2 0-5-1-7-1l12-9z" class="T"></path><path d="M365 226v-1h2c0-1 1-1 2-1 3-2 4 2 7 3 2 1 4 0 6-2l1 1 2-2h2l-4 4c2 0 2 0 4-1l1-1c2 0 3-1 4 0l-3 1c-3 2-6 4-8 6l-1 1c-1 0-1 0-2 1-2 1-4 1-6 1v1h-13c-1 2-1 4-1 5-1-1-1-3-2-5l-1-1c-2 0-2 0-3-1h10s1 0 1-1v-2c2-1 2-3 2-5v-1z" class="C"></path><path d="M383 228c2 0 2 0 4-1l1-1c2 0 3-1 4 0l-3 1c-3 2-6 4-8 6l-1 1c-1 0-1 0-2 1-2 1-4 1-6 1v1h-13c-1 2-1 4-1 5-1-1-1-3-2-5l-1-1c-2 0-2 0-3-1h10 3 4c1-1 0-1 2-2v1 1h3c2-1 1-2 2-3h2c2-1 3-4 5-4z" class="M"></path><path d="M345 198c1-1 1-3 2-4 4-4 11-8 16-9v2h1 0c0 1-1 2-2 2l1 1c-3 3-5 5-6 8s-1 4 0 6v1c1-1 2-2 2-3h2v2 1l2 3h0l-2 5c0 1 0 2-1 3 1 1 2 1 3 2 3-1 4-2 6-5l3 1c0-1 1-1 1-1 2-1 3-1 5 0l-12 9c-3 0-6 0-8-1-1 0-2 0-3-1l-2-1c-1 0-1 0-2-1v-1c-1-1-1-1-1-2h-1c0-2-1-2-1-4 0-1-1-1-1-2-2 2 1 9 0 11l-1 1c0-1 0-2-1-3 0-1 0-2-1-4l-2-6v-1c1-2 2-7 3-9z" class="D"></path><path d="M351 200h1l1-3c1 1 1 1 1 2l-1 2c-3 2-2 4-4 6 0-2 1-5 2-7z" class="E"></path><path d="M363 187h1 0c0 1-1 2-2 2-3 3-4 5-6 8l-2 2c0-1 0-1-1-2l-1 3h-1-1c-1 1-1 2-2 3v1 2h-1v-3c1-1 1-3 1-4 2-3 6-8 9-10 2 0 4-1 6-2z" class="g"></path><path d="M349 207c2-2 1-4 4-6l-1 5c0 3 0 6 2 8 2 3 4 4 9 4 3-1 4-2 6-5l3 1c0-1 1-1 1-1 2-1 3-1 5 0l-12 9c-3 0-6 0-8-1h0l-1-1h-1l-1-1c-1-1-3-2-4-4h0c-1-2-1-4-2-5v-3z" class="I"></path><path d="M354 199l2-2c2-3 3-5 6-8l1 1c-3 3-5 5-6 8s-1 4 0 6v1c1-1 2-2 2-3h2v2 1l2 3h0l-2 5c0 1 0 2-1 3 1 1 2 1 3 2-5 0-7-1-9-4-2-2-2-5-2-8l1-5 1-2z" class="G"></path><path d="M352 206l3-1c1 3 0 5 2 8l-3 1c-2-2-2-5-2-8z" class="F"></path><path d="M357 213h1v-2c-1-1-1-2 0-3l1-1c0 3-1 6 1 9 1 1 2 1 3 2-5 0-7-1-9-4l3-1z" class="Q"></path><path d="M371 186h6 1c5 1 8 1 12 5l2 1c1 1 1 2 1 3l2 3c-1 1-3 3-5 4 0 2 0 2 1 3-1 1-2 1-3 2-1 0-2 0-3 1h-1c0 1 0 1 1 2h0-3l-1 1-3 2c-2-1-3-1-5 0 0 0-1 0-1 1l-3-1c-2 3-3 4-6 5-1-1-2-1-3-2 1-1 1-2 1-3l2-5h0l-2-3v-1-2h-2c0 1-1 2-2 3v-1c-1-2-1-3 0-6s3-5 6-8c2-2 5-3 8-4z" class="k"></path><path d="M378 190c3 0 7-1 9-1 1 1 2 1 3 2l2 1c1 1 1 2 1 3-3-2-5-4-9-3-8 1-14 6-18 13-1 1-2 2-2 4h0c-1-1-1 0-1-1h0l-2-3v-1c1 0 1 0 2-1l6-7c3-3 5-4 9-6z" class="L"></path><path d="M369 196c3-3 5-4 9-6-2 3-4 4-6 6-4 3-7 7-9 12l-2-3v-1c1 0 1 0 2-1l6-7z" class="d"></path><path d="M371 186h6 1c5 1 8 1 12 5-1-1-2-1-3-2-2 0-6 1-9 1-4 2-6 3-9 6l-6 7c-1 1-1 1-2 1v-2h-2c0 1-1 2-2 3v-1c-1-2-1-3 0-6s3-5 6-8c2-2 5-3 8-4z" class="R"></path><path d="M361 198l3-1v1l-2 4 1 1c-1 1-1 1-2 1v-2h-2 0l2-4z" class="J"></path><path d="M378 186c5 1 8 1 12 5-1-1-2-1-3-2-2 0-6 1-9 1-4 2-6 3-9 6l-3 2c-1-1 2-3 3-5 1-1 2-2 4-3s3-1 4-2v-2h1z" class="B"></path><path d="M371 186c1 0 1 0 2 1-1 1-2 2-4 3a30.44 30.44 0 0 0-8 8l-2 4h0c0 1-1 2-2 3v-1c-1-2-1-3 0-6s3-5 6-8c2-2 5-3 8-4z" class="Z"></path><path d="M364 209l1 1c2-1 2-3 3-4 3-4 7-6 11-8 1-1 2-1 4-1 3 0 5-1 7 1v1 1 2c0 2 0 2 1 3-1 1-2 1-3 2-1 0-2 0-3 1h-1c0 1 0 1 1 2h0-3l-1 1-3 2c-2-1-3-1-5 0 0 0-1 0-1 1l-3-1c-2 3-3 4-6 5-1-1-2-1-3-2 1-1 1-2 1-3l2-5c0 1 0 0 1 1h0z" class="V"></path><path d="M369 213c2-2 4-4 7-5h1c3-1 5-1 8 0h-1c0 1 0 1 1 2h0-3l-1 1-3 2c-2-1-3-1-5 0 0 0-1 0-1 1l-3-1z" class="H"></path><path d="M364 209l1 1c2-1 2-3 3-4 3-4 7-6 11-8 1-1 2-1 4-1h0 0c2 0 4 1 5 1v1h-1c-1 1-1 2-1 3h-1-1c-3 3-5 4-9 5-1 1-2 2-4 2v1h-2c-2 0-2 1-3 2s-1 1-2 1v1h-1l1-5h0z" class="g"></path><path d="M558 462c4-2 7-6 11-8l-9 8c-1 2-3 3-4 5-2 2-3 5-5 8s-4 7-5 11-1 8-2 11l2 5h1v7c-1 4-2 8-1 13 1 2 2 5 4 7 2 4 4 7 7 10l11 10v3l-1 1c-1 3-2 6-3 8l2 2h0 0c1 1 3 1 4 2 2 1 3 2 5 2 1-1 2-1 3-2 1 0 1-1 2-2l2-2c1 1 1 0 2 1 1 5 2 11 5 15h0c2 2 4 3 6 5l4 2 3 3h-2l-1 1h-2-2v1h-1c-1 0-2 1-3 2l1 1h-1l-1 1h-3c0 1 1 1 0 2h-1l1 1 1 1h-1-4c-2 0-2 0-3 1-1 0-1 0-2 1h-1c-1 0-1 0-2-2-1-1-1-2-3-2-1 0-2 0-2-1l-1-1h-1c-1 0-1-1-2-2l-1 2h0c-2 1-2 3-4 3h0c-1 0-2-1-2-1l-8-4c-1 0-2-1-4-1l-4-2v-3l-2-2v-11c-1-3 0-8 0-11-1-8 0-17 0-25 0-1 1-4 0-5-1-4 2-12-1-15v-1h2v-1h0c-1-1-1-1-1-2h1c1-1 0-3 0-4v-1-2c0-4 1-8 2-12 1-3 1-7 2-10l5-11c1-1 2-3 2-4l5-6z" class="Q"></path><path d="M550 542c-3 0-5 1-8 0v-3h0c0-2 0 0 1-2l1 1h1l1 1v-1h1 1v1c1 0 1 1 2 2v1z" class="G"></path><path d="M544 523v-1h2c1 2 2 5 4 7 2 4 4 7 7 10l-1 1c-1-1-3-4-5-6l-1 1-3-3c0-1-1-2-2-3 0-1-1-1-1-2h-1l-1-1 1-1c0 1 1 1 2 2 0-2-1-3-1-4z" class="d"></path><path d="M544 523h-1-1v-2-2-1l1-1v3l1 1v-1-2c1-2 0-4 0-6 0-5-1-10 0-15l2 5h1v7c-1 4-2 8-1 13h-2v1z" class="M"></path><path d="M544 553c-1 0-1 0-2-1v-2c1 0 1-1 2-2-1 0-1-1-2-1 0-2 0-2 1-3 2-1 4-1 7-1l2 1v4 4c-2 1-6 2-8 1z" class="C"></path><path d="M550 543l2 1v4l-2-1v-1h-1l-1 2-1-1c1-1 2-2 2-3l1-1z" class="d"></path><path d="M544 553c-1 0-1 0-2-1v-2c1 0 1-1 2-2-1 0-1-1-2-1 0-2 0-2 1-3 2-1 4-1 7-1l-1 1c0 1-1 2-2 3l1 1c1 0 2 1 3 2v1l-1-1c-2-1-3-2-5-2v1 1c-1 1-1 1-1 3z" class="W"></path><path d="M547 532l3 3 1-1c2 2 4 5 5 6s2 2 2 3c3 1 5 3 7 5 1 0 1 1 2 1v1h-1c0-1-1-1-1-2-3-1-8-3-11-2l-1-2h3c-2-1-3-2-5-1l-1-1v-1c-1-1-1-2-2-2v-1h-1-1v1l-1-1c-1-1-1-2-2-2h-1v-1-1-1l1-1 2 1 2-1z" class="M"></path><path d="M550 535l1-1c2 2 4 5 5 6s2 2 2 3h0c-1-1-1-1-2-1-3-1-5-4-6-7z" class="W"></path><path d="M550 555v-1c5 0 9 3 12 6-1 1-1 1-1 3l1 1 1-1h0c1 1 2 0 3 0h0 0l-2 2-1 1 1 1-2-1v1c-2-1-1 0-2-1h-3v-1l-1-1c0 1-1 2-1 3l-1 1c-1-1-2-2-2-4-2-2-3-5-4-8v-1l1-1c0 1 0 1 1 1z" class="S"></path><path d="M548 556v-1l1-1c0 1 0 1 1 1 1 2 2 3 3 4l2 2 1 2h2 0c1 1 1 2 2 3h-3v-1l-1-1c0 1-1 2-1 3l-1 1c-1-1-2-2-2-4-2-2-3-5-4-8z" class="I"></path><path d="M557 539l11 10v3l-1 1-3 8 2 2c-1 0-2 1-3 0h0l-1 1-1-1c0-2 0-2 1-3 1 0 1 0 1-1-2-3-6-4-9-6-1-1-1-2-1-4 0-1 0-2 1-3 3-1 8 1 11 2 0 1 1 1 1 2h1v-1c-1 0-1-1-2-1-2-2-4-4-7-5 0-1-1-2-2-3l1-1z" class="J"></path><path d="M567 550l-1 2-2 1 1 1c-1 0-3-2-5-2l-2-2c1-1 1-2 2-2 1 1 1 2 3 2h0 3 1z" class="E"></path><path d="M544 562l1-1c-1-1-1-1-1-2h0v-1l-1 1c-1-1-1-1-1-2l1-1c1 0 1 1 2 2v1c1 0 1 0 2 1l-1-1 2-3c1 3 2 6 4 8 0 2 1 3 2 4l3 6 1-3h1 1l1 1c0 2 1 3 2 5l-1 1v4c2 4 4 7 7 10l3 3c-1 0-2 0-2-1l-1-1h-1c-1 0-1-1-2-2l-1 2h0c-2 1-2 3-4 3h0c-1 0-2-1-2-1l-8-4c-1 0-2-1-4-1l-4-2v-3l-2-2v-11c0 1 1 1 1 2 1-1 1-2 3-3-1 0-2-1-3-2l1-1h0c2-2 1 0 1-2h0c-2-1-1-2-2-4l1-1 1 1z" class="F"></path><path d="M541 572c0 1 1 1 1 2 3 4 7 8 9 13l2 2c-1 1 0 1-1 1l-1 1c-1 0-2-1-4-1l-4-2v-3l-2-2v-11z" class="b"></path><path d="M543 585v-8c2 2 3 5 4 7 1 1 0 1 0 3 0 1 0 1 1 2l1-1h1 0l1 1h1v1l-1 1c-1 0-2-1-4-1l-4-2v-3z" class="u"></path><path d="M545 571c-1 0-2-1-3-2l1-1h0c2-2 1 0 1-2h0c-2-1-1-2-2-4l1-1 1 1c2 0 3 0 4 1v2h2c1 4 3 8 6 12-1 1-2 1-3 2h0c-1 0-1 0-1-1h-1v1h-1v-1l1-1-2-2h1v-4l-2-2c0 2 0 2 1 3l-1 1v1h0l-3-3z" class="N"></path><path d="M544 562l1-1c-1-1-1-1-1-2h0v-1l-1 1c-1-1-1-1-1-2l1-1c1 0 1 1 2 2v1c1 0 1 0 2 1l-1-1 2-3c1 3 2 6 4 8 0 2 1 3 2 4l3 6 1-3h1 1l1 1c0 2 1 3 2 5l-1 1v4c2 4 4 7 7 10l3 3c-1 0-2 0-2-1l-1-1h-1c-1 0-1-1-2-2l-1 2h0c-2 1-2 3-4 3h0c-1 0-2-1-2-1 0-2-1-2-2-4v-4h1l-1-1v-1c-1-2 0-4-1-6h0v-2c-3-4-5-8-6-12h-2v-2c-1-1-2-1-4-1z" class="O"></path><path d="M560 571l1 1c0 2 1 3 2 5l-1 1v4c2 4 4 7 7 10l3 3c-1 0-2 0-2-1l-1-1h-1c-1 0-1-1-2-2l-1 2h0c-2 1-2 3-4 3-1-1-1-1 0-2h0 2c1-1 1-3 1-3-1-1-2-2-2-3l-1 1h0l-3-6c-1-2-1-2 0-3l-1-1c0-1 0-2-1-3l1-1v-1l1-3h1 1z" class="M"></path><path d="M560 571l1 1c0 2 1 3 2 5l-1 1v4l-5-7v-1l1-3h1 1z" class="I"></path><path d="M580 563l2-2c1 1 1 0 2 1 1 5 2 11 5 15h0c2 2 4 3 6 5l4 2 3 3h-2l-1 1h-2-2v1h-1c-1 0-2 1-3 2l1 1h-1l-1 1h-3c0 1 1 1 0 2h-1l1 1 1 1h-1-4c-2 0-2 0-3 1-1 0-1 0-2 1h-1c-1 0-1 0-2-2-1-1-1-2-3-2l-3-3c-3-3-5-6-7-10v-4l1-1c-1-2-2-3-2-5l-1-1h-1-1l-1 3-3-6 1-1c0-1 1-2 1-3l1 1v1h3c1 1 0 0 2 1v-1l2 1-1-1 1-1 2-2c1 1 3 1 4 2 2 1 3 2 5 2 1-1 2-1 3-2 1 0 1-1 2-2z" class="B"></path><path d="M554 568l1-1c0-1 1-2 1-3l1 1v1 1c1 0 2 0 3 1l-1 2 1 1h-1-1l-1 3-3-6zm26-5l2-2c1 1 1 0 2 1 1 5 2 11 5 15h0l-3-1c-1 0-3 0-4-1h-2c1 1 1 1 1 2h0l-2-1-2-1c-1 0-2 0-3-1l1-1c1 0 1-1 2 0h1 0v-4c0-1-2-1-3-2 1-1 2-1 3-2 1 0 1-1 2-2z" class="D"></path><path d="M575 567c1-1 2-1 3-2 1 0 1-1 2-2l1 1c0 1 1 2 2 2l2 8c-3-1-5-1-6-4v-1h-1c0-1-2-1-3-2z" class="L"></path><path d="M581 577h0c0-1 0-1-1-2h2c1 1 3 1 4 1l3 1c2 2 4 3 6 5l4 2 3 3h-2l-1 1h-2-2-1c-1-1-1-1-3-1-2-1-5-3-7-3l-1 1h-3l1-1c1 0 0 0 1-1 1 0 1-1 1-2-2-1-3-1-5-2l1-3 2 1z" class="B"></path><path d="M579 576l2 1 1 1h2c1 0 2 0 3 1l1 1-1 2-4-1c-2-1-3-1-5-2l1-3z" class="R"></path><path d="M583 581l4 1 7 2c1 0 1-1 1-2l4 2 3 3h-2l-1 1h-2-2-1c-1-1-1-1-3-1-2-1-5-3-7-3l-1 1h-3l1-1c1 0 0 0 1-1 1 0 1-1 1-2z" class="q"></path><path d="M595 582l4 2 3 3h-2l-6-3c1 0 1-1 1-2z" class="R"></path><path d="M561 572c6 2 11 5 17 7 2 1 3 1 5 2 0 1 0 2-1 2-1 1 0 1-1 1l-1 1h3l1-1c2 0 5 2 7 3 2 0 2 0 3 1h1v1h-1c-1 0-2 1-3 2l1 1h-1l-1 1h-3c0 1 1 1 0 2h-1l1 1 1 1h-1-4c-2 0-2 0-3 1-1 0-1 0-2 1h-1c-1 0-1 0-2-2-1-1-1-2-3-2l-3-3c-3-3-5-6-7-10v-4l1-1c-1-2-2-3-2-5z" class="Y"></path><path d="M571 579c1 1 1 2 1 2v2c1 1 1 1 2 1h0c1-1 2-1 3 0l1 1c1 0 1-1 2-2h0l1 1-1 1v1c-2 0-3 1-5 1l-1 1v1c-1 0-2 1-3 1l-3-5 2-1h1v-1l-1-1c1-1 1-2 1-3z" class="a"></path><path d="M561 572c6 2 11 5 17 7 2 1 3 1 5 2 0 1 0 2-1 2-1 1 0 1-1 1l-1-1h0c-1 1-1 2-2 2l-1-1c-1-1-2-1-3 0h0c-1 0-1 0-2-1v-2s0-1-1-2c-2 0-3 2-4 2v-2c-1 1-1 0-2 1h0l-1-1 2-1-1-2h-1v2l-1-1c-1-2-2-3-2-5z" class="V"></path><path d="M580 585h3l1-1c2 0 5 2 7 3 2 0 2 0 3 1h1v1h-1c-1 0-2 1-3 2l1 1h-1l-1 1h-3c0 1 1 1 0 2h-1l1 1 1 1h-1-4c-2 0-2 0-3 1-1 0-1 0-2 1h-1c-1 0-1 0-2-2-1-1-1-2-3-2l-3-3 1-1h3s0-1 1-1h1l-1-1v-1l1-1c2 0 3-1 5-1v-1z" class="L"></path><path d="M580 585h3l1-1c2 0 5 2 7 3h0-2c-1 1-2 1-3 2l1-2-1 1c-5 1-9 3-14 5 2 1 4 2 5 2 0 1 1 1 0 2 1 1 1 1 1 2h-1c-1 0-1 0-2-2-1-1-1-2-3-2l-3-3 1-1h3s0-1 1-1h1l-1-1v-1l1-1c2 0 3-1 5-1v-1z" class="R"></path><path d="M513 306h14l15 1-1 2c-3 4-5 8-4 13 1 3 2 6 4 9 1 1 3 3 3 4s0 2-1 4c-1 1-1 1-3 2l-1 1c1 0 7 3 7 4-1 2-2 5-4 6h-2c-1 1-1 3-2 4v3c0 1-1 2-1 2-2-1-7 0-9-1h-34v-1l-1-4c0-1-1-2-1-3-2-1-5-1-6-3s-1-3-1-4c1-1 2-2 4-2l2-1-1-1c-1-1-1-3-2-4-1-4 3-7 5-10 1-1 1-4 2-6v1c1-2 0-4 2-6-1-3-4-7-6-9h11c2 0 3-1 5 0l6-1z" class="i"></path><path d="M516 307h2v2 4l-1 1c-1-2-1-3-1-5v-2z" class="B"></path><path d="M513 306l3 1v2 4c0 1 0 0-1 1 0-1-1-2-2-2 0 0 0 1-1 1 0 1 0 1-1 1v-2c0-1 1-1 0-2v2h-1c-2-1 0-1-1-3l-2-2 6-1z" class="R"></path><path d="M507 336h-1v-2c0-4 1-5 2-8v-1h1v3c1-1 1-3 2-4h1v2h0l1-1c0-1-1-2-1-2v-6-1c0 1 0 1 1 2v2h1c0 3 0 4-1 7-1 1-2 1-3 2v6l-1 1h-1v-2h0s-1 1-1 2z" class="I"></path><path d="M488 337c1 0 1 0 2 1 0-1 0-1 1-1h1c1 0 2-1 4-1 0 0 1 0 1 1l1-1v-1c0 1 0 1 2 2h1 3v2c-1 1-2 1-2 2-4 0-9-1-12 0-1-1-1-3-2-4z" class="U"></path><path d="M513 306h14l15 1-1 2c-3 4-5 8-4 13 1 3 2 6 4 9 1 1 3 3 3 4s0 2-1 4v-1h-1c-1-1-1-1-3-1-1 0-4 0-5-1h-1-1 0c-1 0-1 0-1-1-1 1-1 1-1 2l-1-1c-1 1-1 0-2 0 0 0 0-1-1-1v1c-1 0-1 0-2 1l-1-1h-1c-1 0-2 0-3 1 0-2 0-3-1-4h0v3h0c-2-1-1-1-2-3h0c0 1 0 2-1 3h-1v-4h1l-1-1c0-1 1-2 1-3 1 1 0 2 1 3h0c1-4 1-7 0-10l-1-1c0-1 0-2 1-3 3 3 1 9 3 13h0c1-3 0-9-1-12v-1h1l1-1c1 0 1 0 1-1v-3l-1 1c-2-2-1-3-1-5l-1 1v-2h-2l-3-1z" class="X"></path><path d="M519 337c0-2 0-3-1-4h0v3h0c-2-1-1-1-2-3h0c0 1 0 2-1 3h-1v-4h1l-1-1c0-1 1-2 1-3 1 1 0 2 1 3h0c1-4 1-7 0-10l-1-1c0-1 0-2 1-3 3 3 1 9 3 13h0c1-3 0-9-1-12v-1h1c1 1 2 2 2 3l-1 1v1c1 2 0 5 2 7 1-1 0-1 1-1 0-3 0-8-1-10h1v5c0 2 1 4 2 6v1l1 1c1-2 1-1 0-3l-1-1v-3c0-2-1-4-1-5h1c1 1 0 2 1 4 0 2 0 3 1 5 0 1 0 2 1 3v1l1-1c0-1 0-1-1-2-1-2-2-8-2-11h1c1 1 1 4 1 6 0 3 1 6 3 7h1c-1-1-1-1-1-3 1 1 2 3 3 4h0v-2c0-1-1-2-1-4l1 1c1 1 1 2 0 4 1 1 2 1 3 1h1v1 1h1v-1h1v1l1-1h0c1 0 2 1 3 2l-1 3h-1c-1-1-1-1-3-1-1 0-4 0-5-1h-1-1 0c-1 0-1 0-1-1-1 1-1 1-1 2l-1-1c-1 1-1 0-2 0 0 0 0-1-1-1v1c-1 0-1 0-2 1l-1-1h-1c-1 0-2 0-3 1z" class="B"></path><path d="M507 336c0-1 1-2 1-2h0v2h1l1-1c1 0 2 1 2 2h7c1-1 2-1 3-1h1l1 1c1-1 1-1 2-1v-1c1 0 1 1 1 1 1 0 1 1 2 0l1 1c0-1 0-1 1-2 0 1 0 1 1 1h0 1 1c1 1 4 1 5 1 2 0 2 0 3 1h1v1c-1 1-1 1-3 2l-1 1c1 0 7 3 7 4-1 2-2 5-4 6h-2c-1 1-1 3-2 4v3c0 1-1 2-1 2-2-1-7 0-9-1h-34v-1l-1-4c0-1-1-2-1-3-2-1-5-1-6-3s-1-3-1-4c1-1 2-2 4-2l2-1-1-1c3-1 8 0 12 0 0-1 1-1 2-2v-2h-3c2 0 4 0 6-1z" class="k"></path><path d="M514 342h22c0 2 2 3 2 4l1 1v1c-4 0-12 1-15-1h-1-1c-2-1-4 0-6-1h-1l-2-1c0-1 0-2 1-3z" class="v"></path><path d="M512 341h15c4 0 8 0 12 1 1 0 7 3 7 4-1 2-2 5-4 6h-2c-1 1-1 3-2 4v-3l-1-1-1 2h0c0-1 0-1-1-2h-2l-1 2c-2 0-2 0-4-2h-1-2c-1 0-2 1-2 1-1 0-1-1-2-1-1 1-2 1-2 1l-1-1h0c0-1 1-1 2-2 6 0 14-1 20 1h1l1-1v-1c-1-1-1-1-1-2h0c0-1 0-1-1-1h0v-1-1l-1-1-3-1h-22l-2-1z" class="a"></path><path d="M505 350h7 8c-1 1-2 1-2 2h0l1 1s1 0 2-1c1 0 1 1 2 1 0 0 1-1 2-1h2 1c2 2 2 2 4 2l1-2h2c1 1 1 1 1 2h0l1-2 1 1v3 3c0 1-1 2-1 2-2-1-7 0-9-1h-34v-1l-1-4 1-1c1-1 4-1 5-1 2-1 3 0 5 0l1-3z" class="D"></path><path d="M512 350h8c-1 1-2 1-2 2h0c-1 1-1 1-2 1l-1-1c-2 1-3 0-3 2l-1 1h0v-3h0l1-2z" class="L"></path><path d="M505 350h7l-1 2c-1 1-1 2-2 2 0-1 0-1-1-1s-1 0-1 1l-1-1h-1l-1 1h0v-1l1-3z" class="q"></path><path d="M493 355l1-1c1-1 4-1 5-1 2-1 3 0 5 0v1c-2 1-2 1-2 3h-1v-1-3 1l-1 3h-1v-1l-1 1 1 2h-5l-1-4z" class="P"></path><path d="M499 359h9c1 0 1-1 2-2 0 1 1 1 1 2h2l-1-2c2 0 1 1 3 0v2h1l1-3c1 1 1 1 2 1s2-1 3-1v1h1c0-1 1-1 1-2 2 0 2-1 4 0 0 1 1 1 1 2l2 2c2 0 5-1 7 0 0 1-1 2-1 2-2-1-7 0-9-1h-34v-1h5z" class="o"></path><path d="M507 336c0-1 1-2 1-2h0v2h1l1-1c1 0 2 1 2 2h7c1-1 2-1 3-1h1l1 1c1-1 1-1 2-1v-1c1 0 1 1 1 1 1 0 1 1 2 0l1 1c0-1 0-1 1-2 0 1 0 1 1 1h0 1 1c1 1 4 1 5 1 2 0 2 0 3 1h1v1c-1 1-1 1-3 2l-1 1c-4-1-8-1-12-1h-15c-2 1-2 3-3 4-1 0-1-2-2 0h-2v1c2 0 4 1 6 0l1 2h1c-6 2-14 1-19 1h-6c2 1 4 1 6 1h11l-1 3c-2 0-3-1-5 0-1 0-4 0-5 1l-1 1c0-1-1-2-1-3-2-1-5-1-6-3s-1-3-1-4c1-1 2-2 4-2l2-1-1-1c3-1 8 0 12 0 0-1 1-1 2-2v-2h-3c2 0 4 0 6-1z" class="E"></path><path d="M489 343c2 0 5-1 7-1-1 1-2 1-4 1-1 0-3 0-5 1v1h4v1l-2 1c3 2 19 1 23 1h1c-6 2-14 1-19 1h-6c2 1 4 1 6 1h11l-1 3c-2 0-3-1-5 0-1 0-4 0-5 1l-1 1c0-1-1-2-1-3-2-1-5-1-6-3s-1-3-1-4c1-1 2-2 4-2z" class="V"></path><path d="M507 336c0-1 1-2 1-2h0v2h1l1-1c1 0 2 1 2 2h7c1-1 2-1 3-1h1l1 1c1-1 1-1 2-1v-1c1 0 1 1 1 1 1 0 1 1 2 0l1 1c0-1 0-1 1-2 0 1 0 1 1 1h0 1 1c1 1 4 1 5 1 2 0 2 0 3 1h1v1c-1 1-1 1-3 2-12-2-26-1-38 0 0-1 1-1 2-2v-2h-3c2 0 4 0 6-1z" class="b"></path><defs><linearGradient id="G" x1="704.194" y1="508.939" x2="768.4" y2="502.862" xlink:href="#B"><stop offset="0" stop-color="#afaeac"></stop><stop offset="1" stop-color="#e6e5e3"></stop></linearGradient></defs><path fill="url(#G)" d="M752 436l1-1v-1l2 1c2 3 4 6 5 10 0 0 1 1 1 2s1 3 1 4l1 1 1 3v2c0 1 1 2 1 3v1 2c1 1 1 3 1 5 1 1 1 4 1 6 1 1 0 3 1 4v11 4c-1 4 0 8-1 12-1 3-1 5-2 8v2c0 1-1 1-1 2l-2 7v1c-3 8-8 15-12 22-1 1-1 2-2 3-5 7-11 14-18 20-2 2-5 4-7 5-2 0-4 1-5 2h-1v-2c-1 0-1-1-2 0-1 0-2 0-3 1l-1-1h1c1-1 2-3 2-4l-2-1-1 1h-1l1-2c0-1-1-2-2-3 0-1-1-3-1-4 1-1 2-1 4-1v-1l-1-4h-2c1-2 1-1 2-2l1 1h1c0-1 0-1-1-2 1-1 1-2 1-3l1-1 1-1 5-4 4-5 2-1c0-1 1-2 2-3l4-5-3-3c0-1 0-2-1-3h1c4 0 4-3 7-6h0 0c1-2 2-3 2-5 1-1 2-2 2-3 1-1 1-2 1-4l2-3c0-1 1-1 1-2l3-13v-21l-2-12c0-2-1-4-1-6h0l1-2c1 1 1 1 2 1h1 0c1 1 3 2 4 3h2 1 1 0c1-1 0-1 1-1-1-1-1-3-2-4l-2-7-1-3z"></path><path d="M727 563l2 1 1-2c0-1 0-2-1-3v-1-1c0-1 1-2 2-2h0c0 1-1 2 0 3 0 1 2 2 2 4l-1 1h-1c-2 1-1 2-3 1l-1-1z" class="g"></path><path d="M758 503l3 3-5 18h-1v-1c0-1 1-1 1-2v-2c0-1 0-1-1-2 1-3 1-7 3-10h0v-4z" class="C"></path><path d="M758 454l5 22c1 1 1 1 0 2h-5c-1 0-1-3-2-5h2l2-2v-1c1-5-1-11-3-15l1-1z" class="a"></path><path d="M758 473h0c3 2 2 3 5 3 1 1 1 1 0 2h-5c-1 0-1-3-2-5h2z" class="O"></path><path d="M755 517c1 1 1 1 1 2v2c0 1-1 1-1 2v1h1c-1 5-4 9-7 13-2 4-3 7-6 10h-3c3-3 5-7 7-11 2-1 2-3 3-4h-1c0-2 1-2 1-3l1-1h-1l1-1c1-1 2-1 2-2s-1-2-1-3v-1h1l2-4z" class="H"></path><path d="M755 517c1 1 1 1 1 2v2c0 1-1 1-1 2v1c0 3-1 3-3 5l-1 2s-1 0-1 1h-1c0-2 1-2 1-3l1-1h-1l1-1c1-1 2-1 2-2s-1-2-1-3v-1h1l2-4z" class="J"></path><path d="M753 499l5 4v4h0c-2 3-2 7-3 10l-2 4h-1l-2-1-1-1c-1-1 0-2 0-4h-1v1l-1-1-1-3h3c0-1 0-2 1-3v-1-2-1c0-1 1-2 1-3h1c0-1 0-2 1-3z" class="g"></path><path d="M749 512c0 1-1 2-1 3v1l-1-1-1-3h3z" class="B"></path><path d="M749 515l3-12h1c1 2 2 3 1 5v1l-1 10c-1 1-1 1-3 1l-1-1c-1-1 0-2 0-4z" class="H"></path><path d="M758 478h5c0 8 2 17 0 25-1 1-1 2-2 3l-3-3-5-4 1-2h2c1 0 1 1 2 3l1-3-1-1v-6h1c-2-3-1-9-1-12z" class="E"></path><path d="M753 499l1-2h2c1 0 1 1 2 3l1 1h0c1 1 1 1 2 1 1 1 1 1 2 1-1 1-1 2-2 3l-3-3-5-4z" class="n"></path><path d="M724 539l2-1c1 1 1 2 2 3l1 1h0s-1 0-1 1h-1c1 2 2 1 4 1h0c0 1 0 1 1 3v2h1v1l2 2c-2 1-2 1-3 2l-1 1c-1 0-2 1-2 2v1 1c1 1 1 2 1 3l-1 2-2-1h-1-1l-1-1h-2 0c-1 0-2 1-3 1h-1c-2 1-2 1-4 1l1-1-1-1-2-1v-1l-1-4h-2c1-2 1-1 2-2l1 1h1c0-1 0-1-1-2 1-1 1-2 1-3l1-1 1-1 5-4 4-5z" class="D"></path><path d="M724 539l2-1c1 1 1 2 2 3l1 1h0s-1 0-1 1h-1c1 2 2 1 4 1v3h-1l-2 2c-1 1-3 2-4 3v-1c-1 0-1 0-2 1h-1c0-1-1-1-1-2h0v-1c-1-1-1-2 0-3v-2l4-5z" class="I"></path><path d="M724 539l2-1c1 1 1 2 2 3l1 1h0s-1 0-1 1h-1c1 2 2 1 4 1v3h-1c-2-1-3-2-4-3h0c-1 0-1 0-1 1l-2 2-2-1v-1h2c0-1 0-2 1-3 0 0 1 1 1 0s0-2-1-3z" class="Y"></path><path d="M720 544v2c-1 1-1 2 0 3v1h0c0 1 1 1 1 2s-1 2-1 3v1c-2 2-5 3-6 6l-2-1v-1l-1-4h-2c1-2 1-1 2-2l1 1h1c0-1 0-1-1-2 1-1 1-2 1-3l1-1 1-1 5-4z" class="E"></path><path d="M719 551l1 2c-1 1-1 1-2 1h-2l-1-1c2-1 3-2 4-2z" class="T"></path><path d="M715 553c0-1 0-2 1-3 0-1 0-2 1-2 1 1 1 2 2 3-1 0-2 1-4 2z" class="C"></path><path d="M744 449l1-2c1 1 1 1 2 1h1 0c1 1 3 2 4 3h2 1 1 0c1-1 0-1 1-1 1 1 1 2 1 3v1l-1 1c2 4 4 10 3 15v1l-2 2h-2c1 2 1 5 2 5 0 3-1 9 1 12h-1v6l1 1-1 3c-1-2-1-3-2-3h-2l-1 2c-1 1-1 2-1 3h-1l-1-1c0-1-1-1-2-1l-1-1h-1v2c-1 1-1 1-2 1v-1l3-13v-21l-2-12c0-2-1-4-1-6h0z" class="R"></path><path d="M749 493c-1 0-2-1-3-1l1-1c1-1 1-1 3-1l-1 3z" class="H"></path><path d="M750 490c0-1-1-1-1-2l-1-1h2l1 1v4c-1 1 0 2 0 4h3l1-1 1 2h-2l-1 2c-1 1-1 2-1 3h-1l-1-1c0-1-1-1-2-1l1-1v-2-2-2l1-3z" class="d"></path><path d="M744 449l1-2c1 1 1 1 2 1h1 0c1 1 3 2 4 3h2 1 1 0c1-1 0-1 1-1 1 1 1 2 1 3-1 0-3-1-4 0h-2c-1 1-1 2-1 4 0 1 1 2 1 3 1 2 1 3 1 5h-2c0 2-1 2-1 3h-1l-2-1-2-12c0-2-1-4-1-6h0z" class="C"></path><path d="M744 449h1c1 1 1 2 3 3v1 1h0c1 1 1 3 1 5 1 2 1 4 2 6 0 2-1 2-1 3h-1l-2-1-2-12c0-2-1-4-1-6h0z" class="B"></path><path d="M750 487h1c-1-1-2-1-3-2h0c2 0 3 1 5 1 1 1 1 1 2 1 0-2 0-2-1-3s-2-1-3-2-1-1 0-2c0-1 1-1 1-1l-2-2c1-1 1 0 3 0l-1-1-1-1c0-1 0-2-1-3h-1c1-1 1-1 2-1 1 2 2 0 4 1l1 1c1 2 1 5 2 5 0 3-1 9 1 12h-1v6l1 1-1 3c-1-2-1-3-2-3l-1-2-1 1h-3c0-2-1-3 0-4v-4l-1-1z" class="D"></path><path d="M754 479c1 0 2-1 3 0v3h-1-2c-1-2-1-2 0-3z" class="E"></path><path d="M753 488h2l1 1c0 2 1 2 0 4-2-1-3-2-3-4v-1zm-9 14c1 0 1 0 2-1v-2h1l1 1c1 0 2 0 2 1l1 1c0 1-1 2-1 3v1 2 1c-1 1-1 2-1 3h-3l1 3 1 1v-1h1c0 2-1 3 0 4l1 1 2 1v1c0 1 1 2 1 3s-1 1-2 2l-1 1h1l-1 1c0 1-1 1-1 3h1c-1 1-1 3-3 4-2 4-4 8-7 11l-1 1c-1 2-1 3-2 5-1 0-1 0-2-1l-2-2v-1h-1v-2c-1-2-1-2-1-3h0c-2 0-3 1-4-1h1c0-1 1-1 1-1h0l-1-1c-1-1-1-2-2-3 0-1 1-2 2-3l4-5-3-3c0-1 0-2-1-3h1c4 0 4-3 7-6h0 0c1-2 2-3 2-5 1-1 2-2 2-3 1-1 1-2 1-4l2-3c0-1 1-1 1-2v1z" class="g"></path><path d="M738 527c0 2 1 3 0 4h-1s-1 0-2 1v-1h1l-1-1 3-3zm-7 8c1-1 2-1 3-1v2l2 1v1c-1 1-2 2-3 2l-2-4v-1z" class="E"></path><path d="M748 515h1c0 2-1 3 0 4l1 1 2 1v1c0 1 1 2 1 3s-1 1-2 2c-3-5-8-5-12-9h0c2 0 5 2 7 3h0 1v-2c0-1 1-2 1-3v-1z" class="e"></path><path d="M734 541c2-1 3-2 4-4l-1-1c1 0 1-1 2-2l-1-1 1-1c1 1 2 2 3 2 1 1 2 2 2 3v1c-1 0-1 0-2 1s-3 1-4 3c-1 0-1 1-2 1-1 1-2 1-3 1v-1l1-1h1l-1-1z" class="D"></path><path d="M744 537c0 1 0 1 1 1l2-2h0c-2 4-4 8-7 11l-1 1c-1 2-1 3-2 5-1 0-1 0-2-1l-2-2v-1h-1v-2c-1-2-1-2-1-3s0 0 1-1v-1c1-1 1-1 2-1l1 1h-1l-1 1v1c1 0 2 0 3-1 1 0 1-1 2-1 1-2 3-2 4-3s1-1 2-1v-1z" class="d"></path><path d="M744 502c1 0 1 0 2-1v-2h1l1 1c1 0 2 0 2 1l1 1c0 1-1 2-1 3v1 2 1c-1 1-1 2-1 3h-3l1 3 1 1c0 1-1 2-1 3v2h-1 0c-2-1-5-3-7-3h0l-1 1c0 2 0 3 1 4l1 1c0 1-1 2-2 3l-3 3 1 1h-1c-1 1-1 2-1 3-1 0-2 0-3 1h-1c0 1-1 1-1 2l1 1-1 1h-1c0-1-1-1-2-1 0-1 1-2 2-3l4-5-3-3c0-1 0-2-1-3h1c4 0 4-3 7-6h0 0c1-2 2-3 2-5 1-1 2-2 2-3 1-1 1-2 1-4l2-3c0-1 1-1 1-2v1z" class="D"></path><path d="M747 515c-2 0-3 0-5-1h-2v-1c1-2 3-2 5-2l1 1 1 3z" class="g"></path><path d="M729 524c4 0 4-3 7-6h0c0 2-1 5-2 7s-1 3-2 5l-3-3c0-1 0-2-1-3h1z" class="n"></path><path d="M744 502c1 0 1 0 2-1v-2h1l1 1c1 0 2 0 2 1l1 1c0 1-1 2-1 3v1 2 1c-1 1-1 2-1 3h-3l-1-1h0c-2-1-2-1-3-1l-1-1h4l-2-2v-1c2 1 1 0 3 0 0 0 2 1 3 1v-2l-1-1-1 1h-3v-3z" class="H"></path><path d="M633 429c3-3 9-10 14-10 1 1 2 2 2 3l-1 1c2 2 4 3 5 6 1 0 1 1 1 2v4l1 1v-1h1l-1 2c-1 2-2 2-4 3s-5 2-8 3c-2 0-3 0-4 1-2 1-2 1-2 3-1 2-2 2-4 3v-1h0-4v-1l-2 1h-1c-1 1-1 1-2 1v2h-1v-2h-1c-1 2-2 3-2 5l-1 1-1 1v1c1 1 2 2 2 4l-2 2c-1 0-1 1-2 2l-3 3c-1 1-1 3-2 4-1 2-3 5-3 7-1 2-1 2-2 3l-1 1 1 1-2 1h-2l1 1h2c0 2-2 9-2 11-1 6-1 11-1 16h0v-6c-1-2-3-2-4-3 0 1 0 1-1 2s-1 2-2 2c-2 0-3-1-4-1-2 0-2 1-3 1-1 3-2 5-3 8-2 2-3 3-4 5v1h-2c-1 5-4 11-6 16-1 3-2 6-4 8 0 0-1 1-1 2l-11-10c-3-3-5-6-7-10h1c0 1 0 1 1 2l1-1-1-1v-2c-1-2-1-4-1-5 1-3 2-7 2-10v-1l3-7-1-1 1-1v-2c0-1 1-2 1-3h1c3-5 8-9 13-13h1c2-1 3-2 5-2 1-1 1-1 2-1h-4c-1 1-2 1-3 1h0c3-2 6-3 9-3 1 0 6 0 6-1 1 0 3-5 4-6 3-7 7-13 12-20 2-2 3-4 5-6 2-1 3-1 5-2h0c2-1 3-3 5-4s3-1 4-2c1-3 4-4 5-6s4-4 6-6v3z" class="g"></path><path d="M627 440l-2-2 1-1c2 0 3 0 5 1-1 1-1 2-2 2h-2z" class="T"></path><path d="M611 453c2-6 6-10 10-13 0 1 0 1 1 2-2 2-5 5-6 8l-1 2s-1 2-2 2l-2-1z" class="B"></path><path d="M631 438h0c1 3 2 7 2 11h-4v-1l-2 1c0-1 2-2 2-3 1-2-1-3-2-5v-1h2c1 0 1-1 2-2z" class="Y"></path><path d="M618 458c1 1 2 2 2 4l-2 2c-1 0-1 1-2 2l-3 3c-1 1-1 3-2 4v-2c0-2 1-5 3-6 1-1 1-1 2-3-1 0-2-1-2-2 0 0 0-1 1-2h3z" class="D"></path><path d="M639 434c0 1 0 1-1 2 0 1 1 1 1 2h0l-1 1c-1 0-2 0-2 1v2l1 1c0 1-1 1 0 2l2-1c-2 1-2 1-2 3-1 2-2 2-4 3v-1h0c0-4-1-8-2-11 2 0 3-1 4-2l4-2z" class="W"></path><path d="M636 440c-1-1-1-1-1-2 1-1 2-1 3-2 0 1 1 1 1 2h0l-1 1c-1 0-2 0-2 1zm-53 52h1v1c-1 6-3 12-3 18 0 2-1 4 0 5 1-1 2-2 2-3h2l3-6v2c-1 3-2 5-3 8-2 2-3 3-4 5v1h-2c0-11 1-21 4-31z" class="J"></path><path d="M588 507v-1c0-2-1-4 0-6 0-3 2-6 3-9 0-2 2-3 2-5v-2h1l-1-1 1-1v1l1 1c1 0 2 0 3 1-1 1-2 1-2 2-1 3-3 6-4 9 0 2 0 2 1 3h2c1 1 1 1 1 2s0 1 1 2v4c-1 1-1 2-2 2-2 0-3-1-4-1-2 0-2 1-3 1v-2z" class="D"></path><path d="M633 429c3-3 9-10 14-10 1 1 2 2 2 3l-1 1c-2 2-4 4-4 7 0 1 0 1 1 2l-3 1c-1 0-2 0-3 1l-4 2c-2 0-3 1-5 0h0c0-1 1-1 1-2l-1-1c-1 0-1 1-2 0h0l5-4z" class="T"></path><path d="M608 446c2-1 3-1 5-2l-5 6c-1 0-1 1-1 2l-1 2h-1l-12 21h1v3h0c-2-1-1-3-2-4-1 2-2 4-4 5v1c-1 2-1 4-2 6l-1 1v3c-1 1-1 1-1 2h-1l3-11c-1-1-7 1-8 2-6 2-10 7-15 10-3 2-4 5-7 7 0-1 1-2 1-3h1c3-5 8-9 13-13h1c2-1 3-2 5-2 1-1 1-1 2-1h-4c-1 1-2 1-3 1h0c3-2 6-3 9-3 1 0 6 0 6-1 1 0 3-5 4-6 3-7 7-13 12-20 2-2 3-4 5-6zm40-23c2 2 4 3 5 6 1 0 1 1 1 2v4l1 1v-1h1l-1 2c-1 2-2 2-4 3s-5 2-8 3c-2 0-3 0-4 1l-2 1c-1-1 0-1 0-2l-1-1v-2c0-1 1-1 2-1l1-1h0c0-1-1-1-1-2 1-1 1-1 1-2 1-1 2-1 3-1l3-1c-1-1-1-1-1-2 0-3 2-5 4-7z" class="D"></path><path d="M638 439c2 0 3 0 4 1v1l-3 1c-1 0-3-1-3-2s1-1 2-1z" class="Y"></path><path d="M646 429c1 1 2 2 4 2h2c-1 1-1 2-3 3 0 0-1-1-2-1-1-1-1-2-1-4z" class="E"></path><path d="M654 431v4l1 1v-1h1l-1 2h-1v-1c-3 2-6 4-9 5 1-2 5-3 7-6v-2l2-2z" class="I"></path><path d="M646 429c0-1 0-2 1-3l1-1c1 0 3 1 3 2l1 3v1h-2c-2 0-3-1-4-2z" class="B"></path><path d="M645 432c1 1 3 2 4 3v1c-2 2-4 3-7 5h0v-1c-1-1-2-1-4-1l1-1h0c0-1-1-1-1-2 1-1 1-1 1-2 1-1 2-1 3-1l3-1z" class="I"></path><path d="M611 453l2 1c0 2 0 4-1 6 0 1 0 2-1 2v5c-2 2-1 3-2 5-1 1-1 1-1 2l-1 1v1c-1 1-1 1-1 2v2c-1 0-2 1-2 1l-1 1c1 1 2 1 2 2l1 1-2 1h-2l1 1h2c0 2-2 9-2 11-1 6-1 11-1 16h0v-6c-1-2-3-2-4-3 0 1 0 1-1 2v-4c-1-1-1-1-1-2s0-1-1-2h-2c-1-1-1-1-1-3 1-3 3-6 4-9 0-1 1-1 2-2 1 0 1-1 2-2v-1h-2v-1h1c1 0 1-1 2-1l-1-2-1 1h-1l1-1s0-1 1-1h1l-1-1-1-2c-1-1-1-1-1-2l2-2c2-2 3-4 5-7 0-2 0-2 2-3l-1-2c1-2 2-3 4-4l1-1z" class="E"></path><path d="M611 453l2 1c0 2 0 4-1 6-1 0-2-1-3-2h-1c1-2 2-3 2-4l1-1z" class="k"></path><path d="M605 463c1 1 1 2 3 2h1c0 1-1 2-1 3l-2 2h-1c0 1-1 3-1 5-1 1-2 1-4 1l-1-2c-1-1-1-1-1-2l2-2c2-2 3-4 5-7z" class="B"></path><path d="M605 470h-2c1-1 0-1 1-1l1-2h1l2 1-2 2h-1z" class="R"></path><path d="M600 470c1 1 1 1 2 3-1 0-2 1-3 1-1-1-1-1-1-2l2-2z" class="T"></path><path d="M556 500c3-2 4-5 7-7 5-3 9-8 15-10 1-1 7-3 8-2l-3 11c-3 10-4 20-4 31-1 5-4 11-6 16-1 3-2 6-4 8 0 0-1 1-1 2l-11-10c-3-3-5-6-7-10h1c0 1 0 1 1 2l1-1-1-1v-2c-1-2-1-4-1-5 1-3 2-7 2-10v-1l3-7-1-1 1-1v-2z" class="w"></path><path d="M556 504l2-4h1l-2 3c1 1 2 1 3 2l3-4h1c-5 7-9 15-8 23l2 7c1 7 4 12 11 16h0s-1 1-1 2l-11-10c-3-3-5-6-7-10h1c0 1 0 1 1 2l1-1-1-1v-2c-1-2-1-4-1-5 1-3 2-7 2-10v-1l3-7z" class="n"></path><path d="M560 505c-1 3-3 5-3 8-2 7-2 11-1 17h0c-1 0-1 0-1-1-1-1-1-3-1-4-1-8 1-15 3-22 1 1 2 1 3 2z" class="k"></path><path d="M494 360h34c2 1 7 0 9 1v1h-1c-1 1-1 2-2 3v1c-1 2-1 3 0 4 0 3-1 7 0 9 2 3 0 6 2 8l1 3v28c0 2-1 6 0 7v1c2 1 3 1 4 1l-1 4c0 2 0 7-1 9l-1 1h-1v1h-25-18c0-1-1-1-2-1l-1-2v-9h0v-9l-1-1c0-1 0-1-1-2s-1-3-1-5l4 1v-4l1-1v-8h1 1v-15l-1-23v-3z" class="c"></path><path d="M502 401v-28c0-3 0-8 1-10l1 1v11 26h-2z" class="W"></path><path d="M508 375v-11h1v6h1c0-2 0-5-1-7h1c2 2 1 6 1 9v31c-1 4-1 10-1 14 0 2 0 4-1 6-1-2 0-7 0-9l-1-26c0-4 1-9 0-13z" class="J"></path><defs><linearGradient id="H" x1="518.902" y1="386.996" x2="509.098" y2="389.004" xlink:href="#B"><stop offset="0" stop-color="#acaba7"></stop><stop offset="1" stop-color="#d4d3d5"></stop></linearGradient></defs><path fill="url(#H)" d="M512 396l1-19c0-5 0-9-1-13l1-1v3c1-1 0-3 2-3 1 2 1 6 1 8v30c-1 1-1 3-1 5l-1 16h-1l-1-26z"></path><path d="M504 364h1 1v-1c2 2 1 5 1 8h0c-2 2-1 5-1 8v28c0 5-1 11 0 16l-6 1 1-17c1-1 0-4 1-6h0 2v-26-11z" class="e"></path><path d="M502 401h2c1 6 0 14 0 21h-1c-1-7 0-14-1-21h0z" class="G"></path><path d="M504 364h1 1v-1c2 2 1 5 1 8h0c-2 2-1 5-1 8-1 3 0 9-1 12l-1-15v-1-11z" class="K"></path><path d="M494 363h3c1 2 0 4 0 6v8l1 43-2 6h-1c-1 0-1 1-2 1 0-4 0-9-1-12v-1-4l1-1v-8h1 1v-15l-1-23z" class="P"></path><path d="M495 401l1 24-1 1c-1 0-1 1-2 1 0-4 0-9-1-12v-1-4l1-1v-8h1 1z" class="o"></path><path d="M494 363h3c1 2 0 4 0 6v8 24 1c-1-1-1-3-1-5v-12c0-2 0-4-1-5v6h0l-1-23z" class="X"></path><path d="M520 367c1-2 0-3 2-5 1 1 1 2 1 4v3 39c-1 4-1 10-1 14h-1 0l-1 1h-14c-1-5 0-11 0-16v-28c0-3-1-6 1-8l1 15v-11c1 4 0 9 0 13l1 26c0 2-1 7 0 9 1-2 1-4 1-6 0-4 0-10 1-14h1v-7l1 26h1l1-16c0-2 0-4 1-5v-1c0-2 1-4 1-6v-23c0-2-1-6 1-8h1v3l1 1z" class="F"></path><path d="M517 394v-23c0-2-1-6 1-8h1c0 6 1 57 0 60l-1-1-1-28zm3-27c1-2 0-3 2-5 1 1 1 2 1 4v3 39c-1 4-1 10-1 14h-1 0l-1-55z" class="S"></path><path d="M532 364v-1l1-1c0 2 1 3 1 4-1 2-1 3 0 4 0 3-1 7 0 9 2 3 0 6 2 8l1 3v28c0 2-1 6 0 7v1c2 1 3 1 4 1l-1 4c0 2 0 7-1 9l-1 1h-1v1h-25-18c0-1-1-1-2-1l-1-2v-9h0v-9l-1-1c0-1 0-1-1-2s-1-3-1-5l4 1v1c1 3 1 8 1 12 1 0 1-1 2-1h1l2-6h0v-1c1-3 0-5 0-7 1-8 0-16 1-24l1-24v-1c2 3 1 9 1 12v32l-1 17 6-1h14l1-1h0 1c0-4 0-10 1-14v-39c1 4 1 9 1 13v16-31-1c0-1 0-2 1-3h1c1 2 1 6 1 8 1 18-1 35 0 52l1 1v-1-20-40h1 1c1 1 1 1 2 1z" class="B"></path><path d="M530 363c1 1 1 1 2 1 1 10 0 19 0 28v2c-2-1-1-8-1-10l-1-21z" class="K"></path><path d="M531 384c0 2-1 9 1 10v-2c1 4 0 9 1 14 0 6 1 12-1 18h-1l-1-1 1-39z" class="Q"></path><path d="M524 366c1 5 0 9 1 14v25 13c0 1 1 4 0 5h-1-4l1-1h0 1c0-4 0-10 1-14v-39c1 4 1 9 1 13v16-31-1z" class="F"></path><path d="M521 422h0 1c0-4 0-10 1-14v4h0c2 2 1 9 1 11h-4l1-1z" class="M"></path><path d="M499 388v27c0 2 1 8 0 10h1c2 1 2 1 5 2 4 0 10-1 14 0v1h-4-8-14c-1 1-1 2-2 2h0v-9l-1-1c0-1 0-1-1-2s-1-3-1-5l4 1v1c1 3 1 8 1 12 1 0 1-1 2-1h1l2-6h0v-1c1-3 0-5 0-7 1-8 0-16 1-24z" class="F"></path><path d="M534 379c2 3 0 6 2 8l1 3v28c0 2-1 6 0 7v1c2 1 3 1 4 1l-1 4c0-1 0-2-1-3h-1-10-8-1v-1c-4-1-10 0-14 0-3-1-3-1-5-2h32 0c1 0 2-1 2-1 1-2 0-7 0-10v-35z" class="l"></path><path d="M507 428h8 4 1 8 10 1c1 1 1 2 1 3 0 2 0 7-1 9l-1 1h-1v1h-25-18c0-1-1-1-2-1l-1-2v-9c1 0 1-1 2-2h14z" class="B"></path><path d="M538 428h1c1 1 1 2 1 3 0 2 0 7-1 9l-1 1c0-2-1-3-2-4h0c1-1 2-2 2-3 0-2 0-2-1-4l-1 1c1 2 1 2 0 4h0l-1-6c1 0 1 0 3-1z" class="C"></path><path d="M519 428h1 8 10c-2 1-2 1-3 1h-2c-2 1-2 1-2 3v1h-1c-1 1-1 2-1 3l-1 1-2 1c-2 0-2-3-5-1-1-2-1-4-3-6-1-1-1-2-3-3h4z" class="Z"></path><path d="M520 428h8c1 0 2 1 3 1v1h-1c0 1-1 2-2 3s-2 0-3-1l-2 1-4-2 1-3z" class="s"></path><path d="M507 428h8c2 1 2 2 3 3 0 3 0 6 1 8 3 0 7 1 10 0l1-3c1 0 1 0 2 1v2l1 1c2-1 2-2 3-3 1 1 2 2 2 4h-1v1h-25l2-1h0c-1-1-3-1-4-1h-2-1v-1l1-2c1-1 0-2 0-3h-1 0l-1-2 1-1v-3z" class="R"></path><path d="M509 433l1-1c1 0 2 1 3 1s1-1 2-2l1 1v2c-1 2-1 4-1 5l-1 1-1-1c-1-1-1-1-2-1l-1 1h-2l2-2-1-4z" class="g"></path><path d="M508 434v-2l1 1 1 4-2 2h2l1-1c1 0 1 0 2 1l1 1 1-1h1l1 1c3 1 7 1 10 0 1 0 1 0 2-1l1-3c1 0 1 0 2 1v2l1 1c2-1 2-2 3-3 1 1 2 2 2 4h-1v1h-25l2-1h0c-1-1-3-1-4-1h-2-1v-1l1-2c1-1 0-2 0-3z" class="M"></path><path d="M491 430c1 0 1-1 2-2h14v3l-1 1 1 2h0 1c0 1 1 2 0 3l-1 2v1h1 2c1 0 3 0 4 1h0l-2 1h-18c0-1-1-1-2-1l-1-2v-9z" class="C"></path><path d="M491 439c2-1 1-3 2-4 0-2 0-2 1-3 1 1-1 3-1 4v3h1 4v-1c1 1 2 1 3 2l2-1 1 1h0c1-1 2-1 3 0h1 2c1 0 3 0 4 1h0l-2 1h-18c0-1-1-1-2-1l-1-2z" class="L"></path><path d="M498 438c-1-2 0-4 0-5 0-2 1-2 3-2l1 1c-1 2-1 4-1 6h1l1-1v-1c1-1 1 0 1-1v-3h2l1 2h0 1c0 1 1 2 0 3l-1 2v1c-1-1-2-1-3 0h0l-1-1-2 1c-1-1-2-1-3-2z" class="N"></path><path d="M507 434h0 1c0 1 1 2 0 3l-1 2c-1-1-1-1-1-2s0-2 1-3z" class="C"></path><path d="M555 757c2 0 3 0 4 2 1 1 2 2 2 4l1 1 1-1 1 1h2c1 1 3 1 4 1l1-1c0 1 1 1 2 1s2 1 3 1l-1-2h0l-1-1v-3l2-1 4-1c1 1 5 3 5 4l2 2 2 2c3 4 6 8 8 12h1c1 1 1 3 2 4v1c1 6 1 12 0 18-2 8-6 14-11 19-3 4-8 8-13 9h-1c-6 2-11 3-16 2h-1l-4-1c-10-3-17-10-21-18l-2-5c-1 0-1 0-2 1v-1c0-1-2-1-2-2v-6-1l1-1v-5l-1-1 2-1 1-1v-3-2l1-1 2-8 1-1 1-3 2-1v-1l2-1 2 2v-1c-1-1-1-1-1-2 1-2 3-4 5-6l9-4h1z" class="D"></path><path d="M574 777l-2-2 2-1c2 1 5 3 7 5l1 1c1 0 2 1 3 2l-1 1c-4-3-7-4-12-3-2 1-5 1-7 2h5c-2 2-3 3-5 4h-1c-2 1-3 2-4 4 0 1-1 2-1 3l-2 1c0-3 1-6 3-9 1-1 2-1 3-2 2-2 4-3 7-5 1 1 3 0 4 0v-1z" class="F"></path><path d="M560 790c1-2 2-3 4-4h1c-2 4-3 7-1 12 0 3 2 5 5 6s7 1 10 0c4-2 6-3 8-6-1 3-3 6-6 8v1c-5 1-10 2-14 0-2-1-4-2-5-4-1 0-1 0-2-1s-1-2-3-4v-4l2-1c0-1 1-2 1-3z" class="K"></path><path d="M560 790c0 3-1 6 0 9v1c1 1 1 2 2 3-1 0-1 0-2-1s-1-2-3-4v-4l2-1c0-1 1-2 1-3z" class="L"></path><path d="M576 801c-2 1-4 0-6-1s-3-3-3-6c0-2 0-4 2-6s4-3 6-3c4 0 7 1 10 4h1c0-1-1-2 0-3l1 1c1 3 1 5 0 8v3c-2 3-4 4-8 6l-3-3z" class="V"></path><path d="M585 789h1c0-1-1-2 0-3l1 1c1 3 1 5 0 8v3c-2 3-4 4-8 6l-3-3c1 0 3 0 5-1 1-1 3-4 4-5s0-4 0-6h0z" class="W"></path><path d="M557 779c1-1 2-1 3-2 1 0 1 0 1-1 1-1 2-1 3-2 1 0 1 0 3 1l1 1c1-1 1-1 2-1l2 2h2v1c-1 0-3 1-4 0-3 2-5 3-7 5-1 1-2 1-3 2-2 3-3 6-3 9v4c2 2 2 3 3 4s1 1 2 1c1 2 3 3 5 4 4 2 9 1 14 0l-3 3c-1 0-3 0-5 1 0 0-2-1-2 0h3c0 1 1 2 0 3h1l-2 1c-6 0-10-3-14-7l-1 1-1 1-1 1c-3-4-5-7-5-11v-1l-2-1c1-1 0-2 0-3v-2h-1v-3c0-2 1-3 2-5 1-3 2-5 4-7 1 1 2 1 3 1z" class="J"></path><path d="M557 798c2 2 2 3 3 4s1 1 2 1c1 2 3 3 5 4h-5c-2-3-4-5-5-9z" class="F"></path><path d="M567 775l1 1c1-1 1-1 2-1l2 2h2v1c-1 0-3 1-4 0h-2c-1 0-1 1-2 1l-3 1-1-1c1-2 3-3 5-4z" class="E"></path><path d="M554 778c1 1 2 1 3 1-1 1-3 3-4 5l-1 1c-1 0-1 1-1 2l-1 1c-1 2-1 3-1 5h-1v-3c0-2 1-3 2-5 1-3 2-5 4-7z" class="I"></path><path d="M562 807h5c4 2 9 1 14 0l-3 3c-1 0-3 0-5 1 0 0-2-1-2 0-4-2-6-2-9-4z" class="i"></path><path d="M552 795h-1c-1-5 1-5 3-8v-1c1-2 3-4 5-5h0c-3 4-4 8-5 13l-1 1v3l-1-1v-1-1z" class="L"></path><path d="M552 795v1 1l1 1v-3l1-1c0 6 1 9 5 14l-1 1-1 1-1 1c-3-4-5-7-5-11v-1l-2-1c1-1 0-2 0-3h3z" class="q"></path><path d="M580 758c1 1 5 3 5 4l2 2 2 2c3 4 6 8 8 12h1c1 1 1 3 2 4v1c1 6 1 12 0 18-2 8-6 14-11 19 0-2 3-3 3-5v-1c-2 2-4 4-6 5v1l-1-1c-3 1-4 2-7 3-2 1-3 1-5 2v-2c1 0 1 0 2-1h-1c-4 1-7 1-10-1v-4c-3-1-6-3-8-5l1-1 1-1 1-1c4 4 8 7 14 7l2-1h-1c1-1 0-2 0-3h-3c0-1 2 0 2 0 2-1 4-1 5-1l3-3v-1c3-2 5-5 6-8v-3c1-3 1-5 0-8 0-2-2-3-3-4l1-1 1 1v-1c1-1 0-2 0-3h0l-1-1 1-1 1 1c2 0 3 1 4 1-1-2-2-4-4-5l1-1c-1-2-5-5-7-6l-6-3h0l-1-1v-3l2-1 4-1z" class="H"></path><path d="M581 767l1-1c1 1 2 2 3 2 8 7 11 15 12 25 0 3 0 5-1 8-1 2-2 6-5 7l3-6c0-1 0 0-1-1v-1h0v-1c1-2 2-5 2-8 0-7-2-13-7-18-1-2-5-5-7-6z" class="e"></path><path d="M580 758c1 1 5 3 5 4 1 3 4 5 6 8 1 2 2 3 3 5v1c-3-3-5-6-8-8h-1c-1 0-2-1-3-2l-1 1-6-3h0l-1-1v-3l2-1 4-1z" class="a"></path><path d="M574 763v-3l2-1 1 2h0l2 2-1 1h1 1l2 2-1 1-6-3h0l-1-1zm24 15c1 1 1 3 2 4v1c1 6 1 12 0 18-2 8-6 14-11 19 0-2 3-3 3-5v-1c-2 2-4 4-6 5v1l-1-1c-3 1-4 2-7 3-2 1-3 1-5 2v-2c1 0 1 0 2-1l1-1c1-1 1-2 2-3h1c2 0 5-2 7-3 0-1 1-2 1-2 2-1 3-2 4-4 3-1 4-5 5-7l1 1c0 2-2 8-3 9 0 1-1 1-1 2l2-2v-1l1-2 1-1v-1c4-9 3-19 0-28h1z" class="P"></path><defs><linearGradient id="I" x1="573.962" y1="816.505" x2="572.911" y2="808.75" xlink:href="#B"><stop offset="0" stop-color="#32312f"></stop><stop offset="1" stop-color="#494a47"></stop></linearGradient></defs><path fill="url(#I)" d="M575 814h4c7-3 11-8 14-14v1c1 1 1 0 1 1l-3 6c-1 2-2 3-4 4 0 0-1 1-1 2-2 1-5 3-7 3h-1c-1 1-1 2-2 3l-1 1h-1c-4 1-7 1-10-1v-4c-3-1-6-3-8-5l1-1 1-1 1-1c4 4 8 7 14 7l2-1z"></path><path d="M558 809l2 1v2h-1l-1-1-1-1 1-1z" class="n"></path><path d="M564 816c3 1 8 3 11 2h1 0c1-1 1-1 2-1-1 1-1 2-2 3l-1 1h-1c-4 1-7 1-10-1v-4z" class="h"></path><defs><linearGradient id="J" x1="579.773" y1="796.127" x2="593.438" y2="796.377" xlink:href="#B"><stop offset="0" stop-color="#afb0ac"></stop><stop offset="1" stop-color="#d4d2d1"></stop></linearGradient></defs><path fill="url(#J)" d="M591 779c-1-2-2-4-4-5l1-1c5 5 7 11 7 18 0 3-1 6-2 8v1h0c-3 6-7 11-14 14h-4-1c1-1 0-2 0-3h-3c0-1 2 0 2 0 2-1 4-1 5-1l3-3v-1c3-2 5-5 6-8v-3c1-3 1-5 0-8 0-2-2-3-3-4l1-1 1 1v-1c1-1 0-2 0-3h0l-1-1 1-1 1 1c2 0 3 1 4 1z"></path><path d="M586 779h0l-1-1 1-1 1 1c2 0 3 1 4 1 1 1 1 2 1 3l-1 1-1 1c1 4 1 8 0 12v1c0-6-1-10-4-14v-1c1-1 0-2 0-3z" class="d"></path><path d="M587 778c2 0 3 1 4 1 1 1 1 2 1 3l-1 1-1 1v-1h0c-1-2-1-4-3-5z" class="E"></path><g class="P"><path d="M585 782l1 1c3 4 4 8 4 14-2 4-5 10-10 12 0 0-1 0-2 1l3-3v-1c3-2 5-5 6-8v-3c1-3 1-5 0-8 0-2-2-3-3-4l1-1z"></path><path d="M555 757c2 0 3 0 4 2 1 1 2 2 2 4l1 1 1-1 1 1h2c1 1 3 1 4 1l1-1c0 1 1 1 2 1s2 1 3 1l-1-2 6 3c2 1 6 4 7 6l-1 1c2 1 3 3 4 5-1 0-2-1-4-1l-1-1-1 1 1 1h0c0 1 1 2 0 3v1l-1-1c-1-1-2-2-3-2l-1-1c-2-2-5-4-7-5l-2 1 2 2h-2l-2-2c-1 0-1 0-2 1l-1-1c-2-1-2-1-3-1-1 1-2 1-3 2 0 1 0 1-1 1-1 1-2 1-3 2-1 0-2 0-3-1-2 2-3 4-4 7-1 2-2 3-2 5l-2 3h0c-1 0-2 0-2-1l1-1v-1h-2c-2 0-3-1-5-1l-1-1s-1-1-2-1c-2-1-3-1-5-1v-2l1-1 2-8 1-1 1-3 2-1v-1l2-1 2 2v-1c-1-1-1-1-1-2 1-2 3-4 5-6l9-4h1z"></path></g><path d="M547 772v-2h2 1l1 1c2 1 4 0 6 1l-1 1c-1-1-1 0-1-1-1 1-3 3-5 3l-1-1h-1l-1-2z" class="G"></path><path d="M554 778c3-3 6-6 10-6l4-1c2 2 3 3 4 6l-2-2c-1 0-1 0-2 1l-1-1c-2-1-2-1-3-1-1 1-2 1-3 2 0 1 0 1-1 1-1 1-2 1-3 2-1 0-2 0-3-1h0z" class="D"></path><path d="M537 770c1 0 1 0 2 1l1 1c1 0 2 0 3 1s2 2 3 2c1 1 2 1 2 2v1c-1 1-1 1-2 1-1-1-1-1-2-1-2 0-4-1-5-2v-1h-2c-1 0-2-1-3-1l1-3 2-1z" class="W"></path><path d="M555 757c2 0 3 0 4 2 1 1 2 2 2 4h0l-1 2h3v1c-2 0-3 0-5 1h-1l-1 1h-1l-7-7v1l9 9v1c-2-1-4 0-6-1l-1-1h-1-2v2c-2-2-3-3-6-3-1-1-1-1-1-2 1-2 3-4 5-6l9-4h1z" class="N"></path><path d="M555 757c2 0 3 0 4 2 1 1 2 2 2 4h0-1c-2-1-1-1-2-1-1 2-1 3-2 4-1-1-2-2-2-4v-4-1h1z" class="d"></path><path d="M546 783h2c0-2 1-2 1-4l1-1v-2h0c1-1 1 0 3 0 0 0 1 1 1 2h0c-2 2-3 4-4 7-1 2-2 3-2 5l-2 3h0c-1 0-2 0-2-1l1-1v-1h-2c-2 0-3-1-5-1l-1-1s-1-1-2-1c-2-1-3-1-5-1v-2l1-1 2-8 2 1 7 4h1c1 1 2 1 3 2v1z" class="F"></path><path d="M543 783l3-1v1l-2 3h1v1c-1 0-1 0-1-1l-1 1v1l-1 1s-1 0-1-1l1-2c-1-1-1-1-1-2l2-1z" class="M"></path><path d="M533 775l2 1 7 4h1c1 1 2 1 3 2l-3 1-2 1c0 1 0 1 1 2l-1 2c-1 0-1-1-3-1-1 0-3-1-5-2l-1-2v1l-1-1 2-8z" class="j"></path><path d="M535 776l7 4h1c1 1 2 1 3 2l-3 1h-2c-3-1-5-3-7-4l1-3z" class="C"></path><path d="M561 763l1 1 1-1 1 1h2c1 1 3 1 4 1l1-1c0 1 1 1 2 1s2 1 3 1l-1-2 6 3c2 1 6 4 7 6l-1 1c2 1 3 3 4 5-1 0-2-1-4-1l-1-1-1 1 1 1h0c0 1 1 2 0 3v1l-1-1c-1-1-2-2-3-2l-1-1c-2-2-5-4-7-5h0l-2-1-1-1c-2-1-3-2-5-2-1 0-1-1-2-2-2 0-3 0-4 1-2 0-3 0-4-1l1-1h1c2-1 3-1 5-1v-1h-3l1-2h0z" class="h"></path><path d="M575 764l6 3c2 1 6 4 7 6l-1 1c2 1 3 3 4 5-1 0-2-1-4-1l-1-1c0-1 0-2-1-3 1 0 1 0 1-1h1c-1-1-2-1-3-2s-3-3-4-3c-1-1-1 0-2-2h-2 0l-1-2z" class="g"></path><defs><linearGradient id="K" x1="571.852" y1="771.826" x2="578.956" y2="767.142" xlink:href="#B"><stop offset="0" stop-color="#8f8d8a"></stop><stop offset="1" stop-color="#a8a89f"></stop></linearGradient></defs><path fill="url(#K)" d="M586 779c-2-1-5-3-6-5h-1c-2-5-8-5-13-6 2-1 4-2 6-2 3 0 6 4 10 4l2 1c1 1 2 1 3 2h-1c0 1 0 1-1 1 1 1 1 2 1 3l-1 1 1 1h0z"></path><path d="M530 786c2 0 3 0 5 1 1 0 2 1 2 1l1 1c2 0 3 1 5 1h2v1l-1 1c0 1 1 1 2 1h0l2-3v3h1v2c0 1 1 2 0 3l2 1v1c0 4 2 7 5 11 2 2 5 4 8 5v4c3 2 6 2 10 1h1c-1 1-1 1-2 1v2c2-1 3-1 5-2 3-1 4-2 7-3l1 1v-1c2-1 4-3 6-5v1c0 2-3 3-3 5-3 4-8 8-13 9h-1c-6 2-11 3-16 2h-1l-4-1c-10-3-17-10-21-18l-2-5c-1 0-1 0-2 1v-1c0-1-2-1-2-2v-6-1l1-1v-5l-1-1 2-1 1-1v-3z" class="j"></path><path d="M539 800c2 2 4 2 5 5l-1 2c-1-1-2-3-3-4 0-1 0-1-1-2-1 1-1 1-1 2h0l-1-1v-1c1-1 1-1 2-1z" class="G"></path><path d="M534 799h5v1c-1 0-1 0-2 1v1h-3c0 1-1 2-2 2v-2l-1-1 3-2z" class="D"></path><path d="M542 818c2-2 5-3 7-4 1 1 0 1 0 2 0 0 8 5 9 6-2 0-4-2-6-3l-1-1c-4-2-3 0-6 2h-2l-1-1v-1z" class="n"></path><path d="M529 790c0 6 1 11 2 17-1 0-1 0-2 1v-1c0-1-2-1-2-2v-6-1l1-1v-5l-1-1 2-1z" class="K"></path><path d="M530 786c2 0 3 0 5 1 1 0 2 1 2 1-1 1-1 1-2 3 0 1-1 2 0 3 0 1 1 1 1 3 1 0 1 0 2 1-1 1-2 1-4 1l-3 2v-1l2-2c-1-3 0-4 0-6 0-1 0-2 1-2-1-2-1-3-1-4-2 1-2 2-3 3v-3z" class="X"></path><path d="M537 788l1 1c2 0 3 1 5 1-2 2-2 2-2 4-1 0-1-1-1-2l-1 1v2c1 1 2 1 2 1v2c0 1 0 1-1 1h-1-5c2 0 3 0 4-1-1-1-1-1-2-1 0-2-1-2-1-3-1-1 0-2 0-3 1-2 1-2 2-3z" class="G"></path><path d="M537 788l1 1c0 2-1 3-2 5-1-1-1-2-1-3 1-2 1-2 2-3z" class="d"></path><path d="M534 806h1c1 1 2 1 2 3l1-1h0 1v1 1h1v1h-1v1h2c1-1 2-1 2-1l1 1c-1 1-1 3-2 4h-4c-2-2-1-3-3-4l-2-2-1-3c1-1 1-1 2-1z" class="F"></path><path d="M573 824c2-1 3-1 5-2 3-1 4-2 7-3l1 1c-3 1-6 3-8 3-5 2-7 5-13 5l-2 1c-1-1-2-1-4-1 0-1 1-2 2-3 4 1 8 0 12-1z" class="n"></path><path d="M543 790h2v1l-1 1c0 1 1 1 2 1h0c1 2 2 5 2 7l-1 1v2h-2 0c-1 1-1 1-1 2-1-3-3-3-5-5v-1h1c1 0 1 0 1-1v-2s-1 0-2-1v-2l1-1c0 1 0 2 1 2 0-2 0-2 2-4z" class="a"></path><path d="M543 790h2l-3 3v2l3 2v1c-1 0-1 1-1 1l-1 1v1l1 1h1c1-1 0-1 1 0l-1 1h0c-1 1-1 1-1 2-1-3-3-3-5-5v-1h1c1 0 1 0 1-1v-2s-1 0-2-1v-2l1-1c0 1 0 2 1 2 0-2 0-2 2-4z" class="h"></path><path d="M542 818v1l1 1h2c3-2 2-4 6-2l1 1-1 1v2h1 1l1 1h1l6 2c-1 1-2 2-2 3 2 0 3 0 4 1h5v-1h4c1-1 2-1 2-1h1c-1 1-2 1-3 2-5 2-12 2-17 0l-4-2-2-1c-2 0-3-2-5-3-1-1-2-1-3-2v-2h0l1-1z" class="F"></path><path d="M552 822h1l1 1h1l6 2c-1 1-2 2-2 3l-5-2c-1 0-1-1-1-1l-1-3z" class="b"></path><path d="M546 793l2-3v3h1v2c0 1 1 2 0 3l2 1v1c0 4 2 7 5 11 2 2 5 4 8 5v4c3 2 6 2 10 1h1c-1 1-1 1-2 1-2 1-4 1-6 1h-2c-1 0-2 0-3-1h-1c-2-2-5-3-7-6-1-2-2-4-4-5v-1h-1c-2 0-3-1-5-2l-1-1 1-2c0-1 0-1 1-2h0 2v-2l1-1c0-2-1-5-2-7z" class="O"></path><path d="M544 805c0-1 0-1 1-2l1 2s1 0 2-1c1 2 2 3 2 5v1h-1c-2 0-3-1-5-2l-1-1 1-2z" class="X"></path><path d="M546 793l2-3v3h1v2c0 1 1 2 0 3l2 1v1c0 4 2 7 5 11 2 2 5 4 8 5v4c-6-3-10-6-13-12-2-3-2-5-3-8 0-2-1-5-2-7z" class="N"></path><path d="M546 793l2-3v3h1v2c0 1 1 2 0 3v5c1 1 1 1 1 2 1 1 1 2 1 3-2-3-2-5-3-8 0-2-1-5-2-7z" class="D"></path><defs><linearGradient id="L" x1="652.458" y1="439.681" x2="661.042" y2="447.319" xlink:href="#B"><stop offset="0" stop-color="#4d4848"></stop><stop offset="1" stop-color="#5a5f5c"></stop></linearGradient></defs><path fill="url(#L)" d="M676 383c3 2 7 3 10 4l3 1c2 0 3 2 6 2v1 1c1 0 1 1 2 1v1h-1c-1-1-2-1-4-1l1 2c7 2 13 8 17 14l-1 1c-2 0-3-2-4-3l-7-7-1 1c1 1 2 1 2 3l2 1c13 12 18 26 22 42h0-3v-1-2h0l-1-2h0c-1 0-1 0-1-1-1 1-1 2-1 3v1h0v2s0 1-1 1h-3l1 1h3l-1 1h-2v1h3v1c-2 0-3 0-4 2-2-1-4-1-5-3-4-3-7-5-11-6v-1l-1-1h-1-13c-2 2-2 2-5 2h-1-1c-3 0-5 0-7 1l-9 3c-2 0-5 1-7 2h-7l1-2c-2 0-2 3-4 4l-1-1 2-3c1-1 3-3 4-3-1-1-2-1-2-2h-1-1v-1c3-1 6-2 8-3s3-1 4-3l1-2h-1v1l-1-1v-4c0-1 0-2-1-2-1-3-3-4-5-6l1-1c0-1-1-2-2-3-5 0-11 7-14 10v-3c-2 2-5 4-6 6s-4 3-5 6c-1 1-2 1-4 2s-3 3-5 4h0c-2 1-3 1-5 2l3-3 13-28c2-6 5-15 11-20v-1h-2c2-2 5-2 7-3l2-1c1 0 1 1 1 2 1-1 0-1 1-1 1-2 6-2 7-2l2 3 7-2 7-1 1-1h0c1-1 3-2 5-2 1 0 1-1 2-1l1-2z"></path><path d="M651 441l12-3h4v1l-15 4s-1-1-1-2z" class="E"></path><path d="M691 438h-1l4-3h0c0-1 1-3 1-4h1v-2l-1-1c0-2 0-2-2-4h0c2-1 3 0 4 1s2 1 3 2c-1 3-2 7-5 9-1 1-2 1-3 2h-1z" class="n"></path><path d="M691 438h1v1c1 1 2 1 4 1l3 2c-1 0-2 0-3 1h0-1-13c-2 2-2 2-5 2h-1-1c-3 0-5 0-7 1l-9 3c-2 0-5 1-7 2h-7l1-2c2 0 4-2 6-3 7-3 16-6 24-7 4 0 12 1 15-1z" class="i"></path><path d="M676 441c2 1 3 1 5 1l-6 1c-1-1-2-1-4-1h0l5-1z" class="M"></path><path d="M691 438h1v1c1 1 2 1 4 1l3 2c-1 0-2 0-3 1h0-1l-1-1c-5 1-9 1-13 0-2 0-3 0-5-1l-5 1c-3 1-7 1-10 2s-6 2-9 2c7-3 16-6 24-7 4 0 12 1 15-1z" class="C"></path><path d="M676 441c3 0 7 0 10-1 1 0 2 0 3 1h2l3 1c-5 1-9 1-13 0-2 0-3 0-5-1z" class="h"></path><path d="M681 412l1-1c1 0 1 0 2 1v-1-1-1l3 3c1 0 2 0 4-1 1-1 2-2 4-2 1 0 1 0 2 1 0 1 1 2 2 3 3 4 5 8 4 14v2c-2 4-4 7-7 9-2 1-3 1-4 1v-1c1-1 2-1 3-2 3-2 4-6 5-9-1-1-2-1-3-2l1-1c-4-2-5-2-9-2h-2c1-2 1-2 0-3v-1c-2-1-4-2-5-4h-1l1-2h-1z" class="E"></path><path d="M694 418l3-2c2 2 2 7 3 9v2c-1-1-2-1-3-2l1-1c-4-2-5-2-9-2h-2c1-2 1-2 0-3 3 0 5 0 7-1z" class="Q"></path><path d="M681 412l1-1c1 0 1 0 2 1v-1-1-1l3 3c1 0 2 0 4-1 1-1 2-2 4-2 1 0 1 0 2 1 0 1 1 2 2 3l-2 1c-1 1-2 2-4 3l1 1c-2 1-4 1-7 1v-1c-2-1-4-2-5-4h-1l1-2h-1z" class="I"></path><path d="M693 417c-1 0-2 1-3 0 1-1 1-1 2-1 1-1 2-2 3-2 1-1 1 0 2 0-1 1-2 2-4 3z" class="J"></path><path d="M682 412h1l1 1c0 1 2 2 3 3v2c-2-1-4-2-5-4h-1l1-2z" class="Y"></path><path d="M691 411h-1c0-2 0-2 1-3v-1l-1-1-1 1c-1 1-1 1 0 3v1l-1-1c0-1 0-1-1-2h-1-1v-2-1c2-2 5-2 7-3 2 0 5 3 7 4l7 6c-1-3-4-4-5-6v-1c13 12 18 26 22 42h0-3v-1-2h0l-1-2h0c-1 0-1 0-1-1-1 1-1 2-1 3v1h0v2s0 1-1 1h-3l1 1h3l-1 1h-2v1h3v1c-2 0-3 0-4 2-2-1-4-1-5-3-4-3-7-5-11-6v-1l-1-1h0c1-1 2-1 3-1l-3-2c-2 0-3 0-4-1 1 0 2 0 4-1 3-2 5-5 7-9v-2c1-6-1-10-4-14-1-1-2-2-2-3-1-1-1-1-2-1-2 0-3 1-4 2z" class="m"></path><g class="n"><path d="M691 411h-1c0-2 0-2 1-3v-1l-1-1-1 1c-1 1-1 1 0 3v1l-1-1c0-1 0-1-1-2h-1-1v-2-1c2-2 5-2 7-3 2 0 5 3 7 4h-4c-2-1-2-1-3 0l1 1h2 0c1 1 1 2 2 2 2 1 5 4 8 5v2 1h3v1l-1 1h2v1h-3v1c2 0 3 0 5 1l-1 1c-2-1-3 0-5 0l1 1h0c2-1 4 0 5 0v1h-3-2v1l1 1v-1h5v1h-4v1l2 1h0-1-4 0-1-1v-2c1-6-1-10-4-14-1-1-2-2-2-3-1-1-1-1-2-1-2 0-3 1-4 2z"></path><path d="M703 429h1 1 0c2 1 4 2 6 2l1 1h-4 0 0v1c2 0 3 0 4 1v1l-2-1-1 1c1 1 2 1 4 2h-3v1l2 1h-2l2 2h-2c1 1 2 2 2 3h-1c0 1 1 1 2 2l1-1v1h0l-1 1c1 1 2 0 3 1h-3l1 1h3l-1 1h-2v1h3v1c-2 0-3 0-4 2-2-1-4-1-5-3-4-3-7-5-11-6v-1l-1-1h0c1-1 2-1 3-1l-3-2c-2 0-3 0-4-1 1 0 2 0 4-1 3-2 5-5 7-9z"></path></g><path d="M703 429h1 1 0c2 1 4 2 6 2l1 1h-4 0c-2-1-3-1-4-1v1 1c1 0 2 1 2 2h0l-2-1-1 1h-2c-1 0-3 4-5 3 3-2 5-5 7-9z" class="O"></path><path d="M699 442c3 1 5 2 7 3l3 2c0 2 1 3 1 5h-1c-3-4-7-6-12-8l-1-1h0c1-1 2-1 3-1z" class="N"></path><path d="M696 438c2 1 4-3 5-3h2 1l3 3v1h-2v1h1l3 3v1c0 1 0 1 1 2h-1-1c-1-1-1-1-2-1-2-1-4-2-7-3l-3-2c-2 0-3 0-4-1 1 0 2 0 4-1z" class="e"></path><path d="M696 438c2 1 4-3 5-3h2 1c-1 1-1 1-2 1l-1 1 2 2h-1-3 0c1 1 2 1 4 2 1 0 2 1 3 2v1l-8-3-1-1h-1c-2 0-3 0-4-1 1 0 2 0 4-1z" class="P"></path><path d="M649 412c1 0 1 0 1-1l-1-1v-2-1h3l-1-1v-1l2 1c1 0 2 1 3 1v1c2 1 5 3 7 3l1 1h0c1 1 1 1 2 1l2 2c1 0 3 1 4 1h0l-2-2h1l11 5v-1c1 1 1 1 2 1-1-1-1-1-1-2l-2-1 1-2c1 2 3 3 5 4v1c1 1 1 1 0 3h2v1c1 1 1 1 2 1h1v1h-4l-1 2 1 1c-1 0-1 0-2 1 1 0 1 0 1 1h0c-1 0-1 1-1 2l1 2h0-1l1 1 1 1-1 1h1v1c-3 1-8 0-11 0h-1l-9 1v-1h-4l-12 3c0 1 1 2 1 2l-5 3c-1-1-2-1-2-2h-1-1v-1c3-1 6-2 8-3s3-1 4-3l1-2h-1v1l-1-1v-4c0-1 0-2-1-2-1-3-3-4-5-6l1-1c0-1-1-2-2-3-5 0-11 7-14 10v-3c2-1 5-4 6-7l10-7z" class="B"></path><path d="M672 435l2-1 1 1c1 1 3 1 5 1h3c-2 2-4 2-6 2h-1c0-1-1-1-1-2l-3-1z" class="C"></path><path d="M682 419v-1c1 1 1 1 2 1-1-1-1-1-1-2l-2-1 1-2c1 2 3 3 5 4v1c1 1 1 1 0 3h2v1c1 1 1 1 2 1h1v1h-4l-1 2 1 1c-1 0-1 0-2 1 1 0 1 0 1 1h0c-1 0-1 1-1 2l1 2h0-1l1 1 1 1-1 1h1v1c-3 1-8 0-11 0 2 0 4 0 6-2l2-1-1-1c-1 0-2 0-3-1h1l-1-1c1-2 2-1 3-1l-1-2h2v-1h-1c-1 0-2-2-2-3 1 1 1 2 2 2l1-1-2-2h3l-2-2h1v-1l-2-2h-1zm-25 10l-1-1h2l-2-2h2v-1h-2v-1h2c0-1-1-1-1-2h1s1 1 2 1l-2-2h0c1-1 1 0 3 1-1-2-1-1-1-2h2l-1-1h0l1-1c0 1 1 1 2 1h1c1 1 2 1 3 2h0v1l-4-2v1l9 5h-1c-3-1-6-3-9-4h0c3 2 7 3 9 6-2 0-5-3-8-3l-1-1h-1l1 1h0c2 0 3 1 4 2l1 1h0c-1 0-1 0-2-1h-1l-1-1h-1v1h1c1 1 2 1 2 2-1 0-2-1-4-1 1 1 0 1 1 1 1 1 1 0 2 1h1l1 1h-2 0l2 2h1c1 0 1 1 2 1 0 0 1 0 2 1h0l3 1c0 1 1 1 1 2l-9 1v-1h-4l3-1h1l-2-1c0-1-1-1-2-1v-1l-1-1c-1-2-3-3-5-4z" class="D"></path><path d="M649 412c1 0 1 0 1-1l-1-1v-2-1h3l-1-1v-1l2 1c1 0 2 1 3 1v1c2 1 5 3 7 3l1 1h0-2l1 1 6 3c-2 0-6-2-8-1 2 1 4 2 6 4-2-1-5-3-7-3 1 1 2 1 3 2 0 0 1 1 2 1h-1c-1 0-2 0-2-1l-1 1h0l1 1h-2c0 1 0 0 1 2-2-1-2-2-3-1h0l2 2c-1 0-2-1-2-1h-1c0 1 1 1 1 2h-2v1h2v1h-2l2 2h-2l1 1c2 1 4 2 5 4l1 1v1c1 0 2 0 2 1l2 1h-1l-3 1-12 3c0 1 1 2 1 2l-5 3c-1-1-2-1-2-2h-1-1v-1l8-3s3-1 4-3l1-2h-1v1l-1-1v-4c0-1 0-2-1-2-1-3-3-4-5-6l1-1c0-1-1-2-2-3-5 0-11 7-14 10v-3c2-1 5-4 6-7l10-7z" class="P"></path><path d="M645 444l6-3c0 1 1 2 1 2l-5 3c-1-1-2-1-2-2z" class="I"></path><path d="M657 429c2 1 4 2 5 4l1 1v1c1 0 2 0 2 1h-3l-1-1-1 1c-1-1-1-1-2-1h-1c0-2-1-4 0-6z" class="J"></path><path d="M649 412c1 0 1 0 1-1l-1-1v-2-1h3l-1-1v-1l2 1c1 0 2 1 3 1v1c2 1 5 3 7 3l1 1h0-2c-1-2-4-2-6-3-1 0-2 0-3-1l7 4c-3 0-6-2-9-2l6 3h-3 0c1 1 3 2 3 3-1 0-2 0-3-1h0l3 3h-3l1 1c1 0 1 0 1 1h-2v1h2v1l-2 1v2c-2-4-3-9-5-13z" class="n"></path><path d="M649 412c2 4 3 9 5 13 1 3 2 6 2 10h-1v1l-1-1v-4c0-1 0-2-1-2-1-3-3-4-5-6l1-1c0-1-1-2-2-3-5 0-11 7-14 10v-3c2-1 5-4 6-7l10-7z" class="E"></path><defs><linearGradient id="M" x1="626.052" y1="409.587" x2="645.576" y2="420.132" xlink:href="#B"><stop offset="0" stop-color="#0b0c0b"></stop><stop offset="1" stop-color="#2f2f2f"></stop></linearGradient></defs><path fill="url(#M)" d="M676 383c3 2 7 3 10 4l3 1c2 0 3 2 6 2v1 1c1 0 1 1 2 1v1h-1c-1-1-2-1-4-1l1 2c7 2 13 8 17 14l-1 1c-2 0-3-2-4-3l-7-7-1 1c1 1 2 1 2 3l2 1v1c1 2 4 3 5 6l-7-6c-2-1-5-4-7-4-2 1-5 1-7 3v1 2h1 1c1 1 1 1 1 2l1 1v-1c-1-2-1-2 0-3l1-1 1 1v1c-1 1-1 1-1 3h1c-2 1-3 1-4 1l-3-3v1 1 1c-1-1-1-1-2-1l-1 1h1l-1 2h1l-1 2 2 1c0 1 0 1 1 2-1 0-1 0-2-1v1l-11-5h-1l2 2h0c-1 0-3-1-4-1l-2-2c-1 0-1 0-2-1h0l-1-1c-2 0-5-2-7-3v-1c-1 0-2-1-3-1l-2-1v1l1 1h-3v1 2l1 1c0 1 0 1-1 1l-10 7c-1 3-4 6-6 7-2 2-5 4-6 6s-4 3-5 6c-1 1-2 1-4 2s-3 3-5 4h0c-2 1-3 1-5 2l3-3 13-28c2-6 5-15 11-20v-1h-2c2-2 5-2 7-3l2-1c1 0 1 1 1 2 1-1 0-1 1-1 1-2 6-2 7-2l2 3 7-2 7-1 1-1h0c1-1 3-2 5-2 1 0 1-1 2-1l1-2z"></path><path d="M643 392c1-1 0-1 1-1 1-2 6-2 7-2l2 3-10 3c1-2 1-2 0-3z" class="G"></path><path d="M640 391l2-1c1 0 1 1 1 2 1 1 1 1 0 3h-1-1l-1 1c-2-1-4-1-5-1v-1h-2c2-2 5-2 7-3z" class="d"></path><path d="M660 390l7-1c1 0 2 1 3 2-2 1-3 1-5 2-1 0 0 0-1 1l-1 1c-1 1-1 1-1 2-2-1-3-2-3-3h0 1c1 0 1 0 2-1h0l-1-2h-1 1l-1-1zm-5 13h0-1c-2 0-2-1-3-2h2c0-1 0 0-1-1h-1l2-2h-1l-1-1h7v-1c-2 0-3 0-4-1l1-1c2 1 3 1 5 3v1l3 3v1h-1v-1c-1 0-1 0-2-1v1c1 1 2 1 2 2-1 1-3 1-4 0h-3z" class="b"></path><path d="M611 443c2 0 16-14 19-16 3-3 6-6 9-8-1 3-4 6-6 7-2 2-5 4-6 6s-4 3-5 6c-1 1-2 1-4 2s-3 3-5 4h0c-2 1-3 1-5 2l3-3z" class="N"></path><path d="M676 383c3 2 7 3 10 4l3 1c2 0 3 2 6 2v1 1c1 0 1 1 2 1v1h-1c-1-1-2-1-4-1l1 2c7 2 13 8 17 14l-1 1c-2 0-3-2-4-3l-7-7-1 1c1 1 2 1 2 3l2 1v1c1 2 4 3 5 6l-7-6c-2-1-5-4-7-4-2 1-5 1-7 3v1 2h1 1c1 1 1 1 1 2l1 1v-1c-1-2-1-2 0-3l1-1 1 1v1c-1 1-1 1-1 3h1c-2 1-3 1-4 1l-3-3v1 1 1c-1-1-1-1-2-1l-1 1h1l-1 2h1l-1 2 2 1c0 1 0 1 1 2-1 0-1 0-2-1v1l-11-5h-1l2 2h0c-1 0-3-1-4-1l-2-2c-1 0-1 0-2-1h0l-1-1c-2 0-5-2-7-3v-1c2 0 5 1 7 2-3-2-8-3-10-5h1c3 1 5 2 8 2h0c-3-1-5-2-7-3h3c1 1 3 1 4 0 0-1-1-1-2-2v-1c1 1 1 1 2 1v1h1v-1l-3-3 3 1c0-1 0-1 1-1s0 0 1-1l-1-1h0l1-1-1-1c1-1 0-1 1-1 2-1 3-1 5-2-1-1-2-2-3-2l1-1h0c1-1 3-2 5-2 1 0 1-1 2-1l1-2z" class="X"></path><path d="M670 405c-2-1-4-2-5-4 0-1 1-1 1-2h1c1 1 2 2 3 2h1c0 1 1 1 1 2l-2 2z" class="B"></path><path d="M672 403c2 0 4 1 5 2l2 1h0c1-1 1 0 1-1l1 1h0v2l-1 1c-1-1-2 0-4-1-1-1-3-2-5-2l-1-1 2-2z" class="H"></path><path d="M672 403c2 0 4 1 5 2h-5l-1 1-1-1 2-2z" class="R"></path><path d="M663 411v-1c2 0 3 1 5 2h0l-2-2c-1-1-2-1-3-2h1 3 1c1 1 1 1 2 1h1l-2-1 1-1c2 1 4 2 6 2 1 0 1 0 2 1s1 2 2 2h1 1l-1 2h1l-1 2 2 1c0 1 0 1 1 2-1 0-1 0-2-1v1l-11-5h-1l2 2h0c-1 0-3-1-4-1l-2-2c-1 0-1 0-2-1h0l-1-1z" class="g"></path><path d="M693 395c7 2 13 8 17 14l-1 1c-2 0-3-2-4-3l-7-7-1 1c1 1 2 1 2 3-2-2-4-4-7-4s-6 1-8 3h0-1l1-2 1-1 1-2c2-2 4-3 7-3z" class="T"></path><path d="M676 383c3 2 7 3 10 4l3 1c2 0 3 2 6 2v1 1c1 0 1 1 2 1v1h-1c-1-1-2-1-4-1l-8-3c-4-1-8 0-13 1h-1c-1-1-2-2-3-2l1-1h0c1-1 3-2 5-2 1 0 1-1 2-1l1-2z" class="V"></path><path d="M676 383c3 2 7 3 10 4l3 1c2 0 3 2 6 2v1h-2c-3-2-7-4-10-4h-1c-5-1-10 0-14 1h0c1-1 3-2 5-2 1 0 1-1 2-1l1-2z" class="d"></path><path d="M681 392h4c0 1 0 1 1 1v5l-1 2-1 1c-1-1-1-2-2-2 0 1 0 2 1 3-1 0 0 0-1 1v1h0-3l1 1c0 1 0 0-1 1h0l-2-1c-1-1-3-2-5-2 0-1-1-1-1-2 0 0-3-3-4-3l1-1 3 3h1l-1-1-1-1c-1-1-1-2 0-3 1 0 3 0 4-1 2-1 5-1 7-2z" class="C"></path><path d="M681 392h4c0 1 0 1 1 1v5l-1 2c-1-1-2-2-3-2h-1l-1-1c-1 1-1 1-2 1-1-1-2-2-3-2h0l-1-1h2l2 1h1c1 0 2 0 3 1 1 0 1 0 2 1v-1c-1-1-1-2-2-2h-1c1-1 1-2 0-3z" class="M"></path><defs><linearGradient id="N" x1="606.239" y1="567.554" x2="631.985" y2="525.288" xlink:href="#B"><stop offset="0" stop-color="#b2b1b0"></stop><stop offset="1" stop-color="#dfdedc"></stop></linearGradient></defs><path fill="url(#N)" d="M597 507c1-1 1-1 1-2 1 1 3 1 4 3v6h0l1 4v1l3 11v2h-1l-3 1c3 1 6-1 8 2 1 1 1 3 2 4h-1l-2-2h0c1 1 1 2 2 4h1l1 1c1 0 2 1 3 2l7 7c7 5 15 9 23 12 3 1 7 1 9 3l3-2h3 1c2 1 5 0 6-1h8l8-1c2 0 4-1 5-1 4-1 8-3 11-4l6-3c2-1 5-3 8-5l-1 1c0 1 0 2-1 3 1 1 1 1 1 2h-1l-1-1c-1 1-1 0-2 2h2l1 4v1c-2 0-3 0-4 1 0 1 1 3 1 4 1 1 2 2 2 3l-1 2h1l1-1 2 1c0 1-1 3-2 4h-1l1 1c1-1 2-1 3-1 1-1 1 0 2 0v2h1c1-1 3-2 5-2-7 5-16 10-25 12-6 3-15 4-22 5l1-2c0-1 1-1 1-1v-1h-2v-1h2v-2c-1 1-3 2-5 2-1 1-1 1-2 1h-2l-1 1c-3 1-11 0-13-1-1-1-4-1-5-1h-4-4c-6-1-11-3-17-5-1 0-3 0-5-1l-2 1c-2-1-5-2-7-4-2-1-5-3-7-5-5-4-10-6-14-10l-1 1-1 1h0c-1-1-2-2-4-2v-1c-1-1-1 0-2-1l-2 2c-1 1-1 2-2 2-1 1-2 1-3 2-2 0-3-1-5-2-1-1-3-1-4-2h0 0l-2-2c1-2 2-5 3-8l1-1v-3c0-1 1-2 1-2 2-2 3-5 4-8 2-5 5-11 6-16h2v-1c1-2 2-3 4-5 1-3 2-5 3-8 1 0 1-1 3-1 1 0 2 1 4 1 1 0 1-1 2-2z"></path><path d="M631 565h3c1 0 2 1 2 2-1 2-2 3-3 4h-2l-2-2c1-2 1-3 2-4z" class="X"></path><path d="M619 557c1 0 1 0 2 1 1 0 1 1 2 2h-2v1h2c-1 1-1 2-2 3s-1 1-2 1c-2-2-3-3-3-5 1-2 2-2 3-3z" class="i"></path><path d="M649 581c-1-1 0-1-1-2l-1-1v-1l1 1c1 0 2 0 3-1 0-2 2-3 3-4h2v-1c1-2 1-3 3-4l1 1v1h-1l2 2c0-2 2-2 3-3 0 1 1 1 2 1h1c-1 1-2 2-3 2-1 1-4 2-5 4h1l1 1c-1 1-2 1-3 1h-1l-3 1h-1c-1 1-2 1-4 2z" class="C"></path><path d="M659 570l2 2c0 1 0 1-1 2l-1-1-1-1 1-2z" class="E"></path><path d="M668 563h8v6h-1c-1 0-1-1-2-2-2 0-3 1-5-1 0 1 0 1-1 2v2h-1c-1 0-2 0-2-1-1 1-3 1-3 3l-2-2h1v-1l-1-1v-1c-2-1-2-1-4-1l3-2h3 1c2 1 5 0 6-1z" class="I"></path><path d="M667 570v-2c1-1 1-1 1-2 2 2 3 1 5 1 1 1 1 2 2 2l1 1c0 1 0 1 1 2h0 4 2c0 1 0 2-2 3h0c-2 1-2 2-3 1l-1 1h-1v-1l-1 1h-1l1-2h0c-2-1-5-2-8-1l-2 1c-1 1-3 0-5 1h-1c1-2 4-3 5-4 1 0 2-1 3-2z" class="H"></path><path d="M660 576c2-1 4 0 5-1l2-1c3-1 6 0 8 1h0c-3 0-6 3-9 4h-1v1c-2 0-5 1-6 2 1 2 2 2 4 3l4 1v1l-9-3c0-1-1-2-2-2-2 1-5 0-8 0l1-1c2-1 3-1 4-2h1l3-1h1c1 0 2 0 3-1l-1-1z" class="R"></path><path d="M665 579l-2-1v-1c2-1 2-1 4-1l-1 1v2h-1z" class="E"></path><path d="M595 518c-1-1-4-1-6-1v-1c1 0 2-1 3-1 1-1 2-1 3-1l1 2c1 0 2 0 3 1 1 0 2 0 4 1v1l3 11v2h-1-2-3l1-1v-1c-1 0-2-2-3-3h-2c-1 0-2-1-4-1 0 1-1 0-1 1v-1c2 0 3 0 4-1h0c-1-1-2-3-2-5l2-2z" class="H"></path><path d="M595 518l1 1 2 2c0 2-1 3-3 4h0c-1-1-2-3-2-5l2-2z" class="F"></path><path d="M606 530h-3c-1-2-2-3-2-5-1-2-1-3-2-5h0c1 0 3-1 4-1l3 11z" class="T"></path><path d="M601 543h0v-1-3h2c0 2 0 2 1 3h2 1 0v-1h-1v-1c2 0 3 0 5 1h1l1 1c1 0 2 1 3 2l7 7v1l-2-2h-3c1 1 1 1 1 2l-1-1-1 1v-1l-1 3h-3v-1-1c-1 0-2-1-2-1l-1-1-1-2h0 0l-1 1c1 1 1 2 1 4h-1c-2-2-3-4-4-6l-2-2c0-1 0-1-1-2z" class="H"></path><path d="M591 552c2 1 4 1 5 3 1 1 1 3 3 4 2 2 5 4 7 7 1 2 2 2 4 3l2 2c2 2 5 3 8 4 2 2 4 1 7 2s7 2 11 2c1 0 3 1 4 2h4c1 1 2 1 2 1-2 0-6 0-8 1-2 0-1 1-2 0l-1-1h-5s-4-1-5-2c-4-1-8-3-11-5-2-1-2-1-3 0-9-6-17-14-22-23z" class="J"></path><path d="M613 575c1-1 1-1 3 0 3 2 7 4 11 5 1 1 5 2 5 2h5l1 1c1 1 0 0 2 0 2-1 6-1 8-1h0c3 0 6 1 8 0 1 0 2 1 2 2l9 3c1 1 2 1 4 1h-2l-1 1c-3 1-11 0-13-1-1-1-4-1-5-1h-4-4c-6-1-11-3-17-5-4-2-8-4-12-7z" class="i"></path><path d="M648 582c3 0 6 1 8 0 1 0 2 1 2 2h-6c-4 0-8 1-13 1-2-1-5-1-7-3h5l1 1c1 1 0 0 2 0 2-1 6-1 8-1h0z" class="a"></path><path d="M592 526c2 0 3 1 4 1h2c1 1 2 3 3 3v1l-1 1h3 2l-3 1c3 1 6-1 8 2 1 1 1 3 2 4h-1l-2-2h0c1 1 1 2 2 4-2-1-3-1-5-1v1h1v1h0-1-2c-1-1-1-1-1-3h-2v3 1h0c-1 0-4-3-4-3-2-1-2-1-4 0h0l-2-2-1-1c-1-1-1-2-1-3l-1-1c0-3 1-3 2-4 1 1 2 1 2 3v1l1-1v-4s-1-1-1-2z" class="R"></path><defs><linearGradient id="O" x1="607.29" y1="540.346" x2="593.045" y2="567.941" xlink:href="#B"><stop offset="0" stop-color="#030200"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#O)" d="M579 523h2c-1 3 0 5 1 8 2 7 4 15 9 21h0c5 9 13 17 22 23 4 3 8 5 12 7-1 0-3 0-5-1l-2 1c-2-1-5-2-7-4-2-1-5-3-7-5-5-4-10-6-14-10l-1 1-1 1h0c-1-1-2-2-4-2v-1c-1-1-1 0-2-1l-2 2c-1 1-1 2-2 2-1 1-2 1-3 2-2 0-3-1-5-2-1-1-3-1-4-2h0 0l-2-2c1-2 2-5 3-8l1-1v-3c0-1 1-2 1-2 2-2 3-5 4-8 2-5 5-11 6-16z"></path><path d="M575 543c3 0 4 3 7 3 2 1 3 3 5 5l-1 1v-1c-2 0-1 0-2-1h-1-1c-1-1-1-1-2-1l-1-1v1c-1-1-3-3-5-4h1v-1l5 4v-1c-1-2-3-2-5-4z" class="c"></path><path d="M576 551c3 2 9 4 11 8l-1 1-1-1c-1 0-1 0-2-1-2-2-3-3-7-3l-2-2v-1l2 2 1-1c-1-1-1-1-1-2h0z" class="K"></path><path d="M571 551c0-1 1-2 2-2s2 1 3 1v1h0c0 1 0 1 1 2l-1 1-2-2v1l2 2c4 0 5 1 7 3 1 1 1 1 2 1l1 1 1-1 3 4-1 1-1 1h0c-1-1-2-2-4-2v-1c-1-1-1 0-2-1l-2 2c-1 1-1 2-2 2-1 1-2 1-3 2-2 0-3-1-5-2-1-1-3-1-4-2h0 0l-2-2c1-2 2-5 3-8l1-1h0 1c1 0 1 0 2-1z" class="m"></path><path d="M568 552h0 1c1 0 1 0 2-1v1c0 1 0 1 1 2-2 1-2 1-4 1 0-1-1-1-1-2l1-1zm0 4h1c0 2 0 4-1 5h-2v-1-1c1-1 1-2 2-3z" class="U"></path><path d="M566 563c1-1 1-1 2-1l1 1h3c0-2 0-2-1-3v-1l2 1v1s1 0 1 1h2v1h1c-1 1-1 1-3 2l-2-1-2 1c-1-1-3-1-4-2h0z" class="b"></path><path d="M576 555c4 0 5 1 7 3 1 1 1 1 2 1l1 1 1-1 3 4-1 1-1 1h0c-1-1-2-2-4-2v-1c-1-1-1 0-2-1l-2 2c-1 1-1 2-2 2-1 1-2 1-3 2-2 0-3-1-5-2l2-1 2 1c2-1 2-1 3-2h-1v-1h1v-1c-1-1-2-1-3-2 1-1 2 1 4 1-1-2-3-3-4-5 1 0 0 0 1 1 1 0 2 1 4 1v-1l-3-1z" class="q"></path><path d="M714 549l-1 1c0 1 0 2-1 3 1 1 1 1 1 2h-1l-1-1c-1 1-1 0-2 2h2l1 4v1c-2 0-3 0-4 1 0 1 1 3 1 4 1 1 2 2 2 3l-1 2h1l1-1 2 1c0 1-1 3-2 4h-1l1 1c1-1 2-1 3-1 1-1 1 0 2 0v2h1c1-1 3-2 5-2-7 5-16 10-25 12-6 3-15 4-22 5l1-2c0-1 1-1 1-1v-1h-2v-1h2v-2c-1 1-3 2-5 2-1 1-1 1-2 1-2 0-3 0-4-1v-1l-4-1c-2-1-3-1-4-3 1-1 4-2 6-2v-1h1c3-1 6-4 9-4l-1 2h1l1-1v1h1l1-1c1 1 1 0 3-1h0c2-1 2-2 2-3h-2-4 0c-1-1-1-1-1-2l-1-1h1v-6l8-1c2 0 4-1 5-1 4-1 8-3 11-4l6-3c2-1 5-3 8-5z" class="D"></path><path d="M708 562c0-1 0-3 1-4 1 0 2 1 3 2v1c-2 0-3 0-4 1zm1 17l-1-1c-1-1-1-3 0-4l1-1c0 1 1 1 2 2l1 1-3 3z" class="I"></path><path d="M674 577h1l1-1v1l2 2v1 2 1c0 2-1 1-2 1s-1 1-1 1h-1v-3-1l-1-1 1-3z" class="S"></path><path d="M686 575h7c0 2 0 3-1 5h-4c-1-1-2-2-2-4v-1z" class="a"></path><path d="M692 571s1-1 2 0c-1 1-1 2-1 4h0-7l-1 2h1c-3 2-6 0-7 3 0 1 0 2-1 2v-2-1l-2-2h1l1-1c1 1 1 0 3-1h0c2-1 2-2 2-3h1l1 1h1 1 4s0-2 1-2z" class="Y"></path><path d="M703 582l1 1c-3 1-7 3-10 3l-3 1v1c3 0 5-1 7-1h0c-6 3-15 4-22 5l1-2c0-1 1-1 1-1v-1h-2v-1h2v-2c1 1 1 0 1 1v1h1c9 0 15-1 23-5z" class="G"></path><path d="M666 579c3-1 6-4 9-4l-1 2-1 3 1 1-1 1v-1h-6l1 1h3v1c-3 0-4 0-6-1l-2-1v1c2 2 6 4 9 4l1 1c-1 1-1 1-2 1-2 0-3 0-4-1v-1l-4-1c-2-1-3-1-4-3 1-1 4-2 6-2v-1h1z" class="X"></path><path d="M712 576c1-1 2-1 3-1 1-1 1 0 2 0v2h1c1-1 3-2 5-2-7 5-16 10-25 12h0c-2 0-4 1-7 1v-1l3-1c3 0 7-2 10-3l-1-1c2-2 4-3 6-3l3-3z" class="l"></path><path d="M689 561c4-1 8-3 11-4l1 2h1 0c2 0 2-1 3-2l1 1-1 1 1 1c-1 0-1 0-1 1v2l1 2h0-2v1h1c0 1-2 2-3 3h-1c-1 0-1 0-1 1h-1v1 1h-1l-4-1c-1-1-2 0-2 0-1 0-1 2-1 2h-4-1-1l-1-1h-1-2-4 0c-1-1-1-1-1-2l-1-1h1v-6l8-1c2 0 4-1 5-1z" class="H"></path><path d="M689 561c4-1 8-3 11-4l1 2h1v1c-2 1-3 2-5 3h-1 0v-2c-3 0-3 0-4 2v8c-1 0-1 2-1 2h-4-1-1l-1-1 1-1 5-1v-1h-1v-1h1l-1-2v-1l2-1c0-2-1-2-2-3z" class="C"></path><path d="M701 559h1v1c-2 1-3 2-5 3v-2c1-1 3-2 4-2zm-17 3c2 0 4-1 5-1 1 1 2 1 2 3l-2 1v1l1 2h-1v1h1v1l-5 1-1 1h-1-2-4 0c-1-1-1-1-1-2l-1-1h1v-6l8-1z" class="S"></path><path d="M676 569l2-1 1-1 4-1c1 1 1 2 2 3v1 1l-1 1h-1-2-4 0c-1-1-1-1-1-2l-1-1h1z" class="Z"></path><defs><linearGradient id="P" x1="281.262" y1="215.146" x2="312.979" y2="157.929" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#2c2b2b"></stop></linearGradient></defs><path fill="url(#P)" d="M236 174h12l89 1h-12c3 0 9-1 11 1h4v1l7 1h-9v1h9v1h-5 0c2 0 3 0 5 1h0c6-1 11 0 17 1h1l2 1h1 3l2 2c1 0 4 0 5 1h-1-6c-3 1-6 2-8 4l-1-1c1 0 2-1 2-2h0-1v-2c-5 1-12 5-16 9-1 1-1 3-2 4-1 2-2 7-3 9v1c-1 1 0 2-1 3 0 4 1 7 2 10l1 3h1v-1h1v1c0 1 1 3 2 4v1c1 2 4 4 4 6 1 1 1 1 3 1l1 1c1 2 1 4 2 5v6 2l-1-1v3 6h-1c0-2 1-2 0-3-2 3-1 5-2 8v2h0c0 1-1 3 0 4-1 3-2 6-3 8-1 3-2 5-3 8l-3 3h-1 0-1c-2-8-7-16-10-24l-6-16v5l-1 1-2-11-1-1h-1c-3 0-5-6-7-7-1-1-1-1-2-1l-4-4c-4-5-9-9-14-13 1 1 0 1 0 2-1 0-2-2-3-2l-12-9h1c-1-1-1-1-1-2h0c-9-6-20-11-29-14l-17-6c-1-1-2-1-3-1-2-2-4-5-4-8h0l1-1h-1c0-1 0-1 1-1 2 0 7 1 8 0v-1z"></path><path d="M284 176h2v1 1l1 1h0-4-2l1-1c-1 0-1-1-2-1 1-1 3-1 4-1z" class="U"></path><path d="M284 192h1v3h-1c-2 0-6 1-8 0-1 0-1-1-2-1h-1-1 2c1-1 1-1 2-1h1c2 0 3 0 5 1h1l1-2z" class="f"></path><path d="M320 216c-2 0-4 0-6-1h-7v-1c2 0 6 1 7 0l-1-1c-3 0-7 0-10-1 3 0 7 0 10-1h5l-3 1c0 1 2 1 3 2h-2l1 1 3 1z" class="U"></path><path d="M331 201v1c-1 1-2 1-4 2h0 0v1l-2 1-1-1v-1l-1 1v1l-1 2h0-1-1c0-1-1 0-1 0v1c-1 0-1-1-1-1-1-1 0-1 1-2h-1c1-2 4-2 6-3h-1c1-2 1-2 1-3h-1l1-2c1 0 1 0 2-1h0-1c1-1 1-1 3-1 0 1 0 1 1 2h1c0 1 0 1-1 2v1h2z" class="f"></path><path d="M347 181c6-1 11 0 17 1h1l2 1h1l-1 1c-1 0-1-1-2-1-1 1-3 0-4 0s-2 0-3 1h-19c-3 0-7 0-9-1-2 1-5 1-7 1v-1h5 2 1c4-1 8 0 11-1h3s1-1 2-1l1 1c1 1 6 0 8 0h0c-3 0-5 0-8-1h-1z" class="n"></path><path d="M318 211h5v-1h-5 0l4-1h1c1 0 2 0 3 1v-1c1 0 2 0 3 1l1-1v1c-1 1 0 1-1 1 1 0 3 0 5-1 0 0 1 1 2 1v1h-1l-2 1h-1l-1 1c1 1 2 1 3 1h0-7-2c-1 1-3 0-5 1l-3-1-1-1h2c-1-1-3-1-3-2l3-1z" class="f"></path><path d="M290 210c8 5 16 12 23 20 2 3 5 7 8 10l2-4c1 1 0 4 0 6h-1c-3 0-5-6-7-7-1-1-1-1-2-1l-4-4c-4-5-9-9-14-13 0-1-1-2-2-3 0-1-1-2-2-3-1 0-1-1-1-1z" class="H"></path><path d="M319 226c-1 0-1-1-2-1h-1c4-3 10-2 15-2h5 2c-1 1-1 2-1 3v1c0 1 0 2-1 2l2 2-1 1h-3l-1-1c-1 0-2 1-3 1s-2-2-3-3h0c-2 0-4 0-6-1-1 0-4 0-5-1l-1-1h3 1z" class="f"></path><path d="M319 226l4-1c3 0 11 0 13 1v1h-8-1c-2-1-4 0-6 1-1 0-4 0-5-1l-1-1h3 1zm21-31h0 1v3h4c-1 2-2 7-3 9v1c-1 1 0 2-1 3l-2 2v-4h-2c-4-2-11 1-13-1 1-2 6-1 9-1v-1c2 1 3 1 4 2l1-1c-1-1-1-1 0-2-1-1-4-1-6-1v-2-1h-1-2v-1c1-1 2-1 2-2h2c2-1 3-1 6-1l1-2z" class="c"></path><path d="M340 195h0 1v3h4c-1 2-2 7-3 9v1c-1 1 0 2-1 3l-2 2v-4c-1-4-1-8 0-12l1-2z" class="R"></path><path d="M340 195h0 1v3h4c-1 2-2 7-3 9-1-4-2-7-2-11v-1z" class="q"></path><path d="M358 184c1-1 2-1 3-1s3 1 4 0c1 0 1 1 2 1l1-1h3l2 2c1 0 4 0 5 1h-1-6c-3 1-6 2-8 4l-1-1c1 0 2-1 2-2h0-1v-2c-5 1-12 5-16 9-1 1-1 3-2 4h-4v-3l2 2 1-1c1-1 1-5 1-7h-1v-1h-3l-2-1h0c-2 0-4-1-5-1-3 0-10 1-12 0v-1c1-1 11 0 13 0l1-1c1 1 3 1 4 1h14 0c2 0 2 0 4-1z" class="t"></path><path d="M344 189h1c1 0 2 0 4-1l1 1-1 1-1 1c-1 0-2-1-3-2h-1z" class="f"></path><defs><linearGradient id="Q" x1="261.114" y1="192.027" x2="256.486" y2="199.409" xlink:href="#B"><stop offset="0" stop-color="#bfbfbe"></stop><stop offset="1" stop-color="#eaeae8"></stop></linearGradient></defs><path fill="url(#Q)" d="M231 185c-2-2-4-5-4-8h0c6 8 15 10 24 13 4 1 8 3 12 4 7 3 14 7 21 12 2 0 5 2 6 4 0 0 0 1 1 1 1 1 2 2 2 3 1 1 2 2 2 3 1 1 0 1 0 2-1 0-2-2-3-2l-12-9h1c-1-1-1-1-1-2h0c-9-6-20-11-29-14l-17-6c-1-1-2-1-3-1z"></path><path d="M280 206l13 8c1 1 2 2 2 3 1 1 0 1 0 2-1 0-2-2-3-2l-12-9h1c-1-1-1-1-1-2h0z" class="a"></path><path d="M308 177c2 0 4 0 6 1 3 2 5 5 6 8v1c1 1 1 3 1 5h0c-1 1-1 2-2 3-1 2-5 3-7 4-3 0-5 0-7-1s-5-3-6-5c-1-3-1-7 1-10s4-5 8-6z" class="P"></path><path d="M305 185l-1-1 2-2 2-2c2 0 2 0 3 1h3l1 1v1h1c2 2 2 4 3 6 0 2 0 3-2 5-3 3-5 3-9 3l-1-1-2-1c-2-2-3-3-4-5l1-2v1l1 1v-3c0-1 1-2 2-2z" class="G"></path><path d="M305 185c3-1 6-2 9 0 0 0 1 1 1 2 0 2 0 3-2 5h-1l1 2c-3 0-5 1-7 0s-3-2-3-4v-3c0-1 1-2 2-2z" class="c"></path><path d="M309 186h1c1 0 3 0 5 1l-1 1c-2 1-3 3-4 4h-3c-1-1 0-1-1-1h-1c-1-1 0-2 0-3l1-1 1 1 2-2z" class="U"></path><path d="M339 213l2-2c0 4 1 7 2 10l1 3h1v-1h1v1c0 1 1 3 2 4v1c1 2 4 4 4 6 1 1 1 1 3 1l1 1c1 2 1 4 2 5v6 2l-1-1v3 6h-1c0-2 1-2 0-3-2 3-1 5-2 8v2h0c0 1-1 3 0 4-1 3-2 6-3 8-1 3-2 5-3 8l-3 3h-1 0-1c-2-8-7-16-10-24l-6-16v5l-1 1-2-11-1-1c0-2 1-5 0-6l2-4v-1h1 1v10h2 1c0-1 1-1 2-1s3 1 4 0h0l1 1h8v-1c-2-2-2-3-4-3h-1 1v-2l-2-2 2-1c-1-1-1-3-1-4h-1l1-1-1-14z" class="J"></path><path d="M357 252l-1-1v-4h-2v-1c0-2 0-4 1-6v1c0 1 1 3 2 4v4 3z" class="C"></path><path d="M355 236l1 1c1 2 1 4 2 5v6 2l-1-1v-4c-1-1-2-3-2-4v-1-4z" class="d"></path><path d="M339 213l2-2c0 4 1 7 2 10l1 3h1v-1h1v1c0 1 1 3 2 4v1c0 5 5 7 4 11-1 1-1 1 0 1v5h0c-1-1-1-2-1-3-1-1-1-1-1-2h-1 0c-5-5-7-7-9-14l-1-14z" class="l"></path><path d="M343 221l1 3h1v-1h1v1c0 1 1 3 2 4v1c0 5 5 7 4 11-3-4-7-8-8-12v-1c-1-2-1-3-1-6z" class="I"></path><path d="M345 260c1-1 4-1 5 0l2 1c-1 10-3 19-8 27h0-1c-2-8-7-16-10-24 1 0 2 1 3 0h1 0c0-1-1-1-1-2h-1c2-1 3-1 5-2h1 4z" class="c"></path><path d="M346 268c1 1 2 1 3 2v1h0l-1 2h-1l-1-1v-1c1-1 0-2 0-3z" class="U"></path><path d="M345 275h2v3h-3 0c-1-1-1-2-1-3h2z" class="f"></path><path d="M339 271s1 0 2 1 2 0 3 1v1c-1 0-2 0-2-1h-1v1l2 1c0 1 0 2 1 3h0-1v1c1 0 2 1 2 1 0 2 0 2-1 3l-1-1c-1-1-1-2-1-4l-2-1c0-2-1-3-2-5l1-1z" class="p"></path><path d="M339 271v-1h1v-1c-1 0-2 0-2-1v-1l2 1c1-1 1-1 2-1l-1-1v-1l1-1c1 1 2 1 2 2h0v1h1l1 1c0 1 1 2 0 3v1l1 1c-1 1-1 2-2 2h-2l-2-1v-1h1c0 1 1 1 2 1v-1c-1-1-2 0-3-1s-2-1-2-1z" class="m"></path><path d="M325 232v-1h1 1v10h2 1c0-1 1-1 2-1s3 1 4 0h0l1 1h8v-1c-2-2-2-3-4-3h-1 1v-2l-2-2 2-1c-1-1-1-3-1-4h-1l1-1c2 7 4 9 9 14h0l1 5 1 6c0 3 1 6 1 9l-2-1c-1-1-4-1-5 0h-4-1c-2 1-3 1-5 2h1c0 1 1 1 1 2h0-1c-1 1-2 0-3 0l-6-16v5l-1 1-2-11-1-1c0-2 1-5 0-6l2-4z" class="U"></path><path d="M350 246l1 6-1 1v-1-1h0c-1-1-3-2-4-2 0-1 1-1 2-1s2-1 2-2z" class="u"></path><path d="M350 251h0v1 1c-3 1-8 1-12 2h-5 0v-2c4 0 8-1 11-2h6z" class="m"></path><path d="M325 232h1v1l1 15v5l-1 1-2-11-1-1c0-2 1-5 0-6l2-4z" class="d"></path><path d="M325 232h1v1 7c-1 1-2 0-2 2v1l-1-1c0-2 1-5 0-6l2-4z" class="C"></path><path d="M350 253l1-1c0 3 1 6 1 9l-2-1c-1-1-4-1-5 0h-4-1-3c-1 0-1-1-3-1v-1l1-1h0-1l-1-2h5c4-1 9-1 12-2z" class="V"></path><path d="M350 253l1-1c0 3 1 6 1 9l-2-1c-1-1-4-1-5 0 0-1 2-2 2-3h-1c1-1 1-1 1-2-1 0-2 0-4 1h0c-1-1-2-1-3 0l-2-1c4-1 9-1 12-2z" class="n"></path><path d="M511 751h15c4 0 9-1 12 2h5c0 1-1 1 0 2v2h2v4c-2 2-4 4-5 6 0 1 0 1 1 2v1l-2-2-2 1v1l-2 1-1 3-1 1-2 8-1 1v2 3l-1 1-2 1 1 1v5l-1 1v1 6c0 1 2 1 2 2v1c-1 2-3 3-5 4-1 1-2 1-3 2-4 1-8 3-12 4l-8-2-1 2c-1-1-2-2-4-3 0 1 1 3 1 4l-1 2h0v1l-2 3-7 7-2 3-3 6h-1l-1-4c-1 1-2 3-2 4v1 1h-1l1 2v1c1 2 4 4 5 6 1 1 1 2 3 2l2-1h0l1 1 1 1c-2 1-5 3-6 5-2 1-3 2-5 3h-1v-1h0-1l-1-1c-1-1-1-3-1-4l-3-3c-1-2-3-4-5-6v1c-2 0-2-1-4-1-1-3-3-7-5-10-1-1-3-2-3-3v-4-8l-16-36-1-2c0-3 2-6 2-10l2 2c-1-4 2-9 3-13l1-2c0-1 1-3 2-3l1-1v-1c0-2 1-2 2-3l-4-1h33 11v-1h6c2 0 4-1 6-1h8z" class="W"></path><path d="M458 838c3 1 7 8 9 10v1c-2 0-2-1-4-1-1-3-3-7-5-10z" class="e"></path><path d="M455 761l2-1 1 2-1 2c0 2 1 6 0 9 0 1 0 2 1 3-1 0-1 0-1 1l1 7-2 2v-8-3h-1v3h0v-5-1-2-6-3z" class="L"></path><path d="M455 761l2-1 1 2-1 2v3h-1v-2l-1-1v-3z" class="e"></path><path d="M451 771l4 2v5h0v17c0-2 0-5-1-7-1-1-2-2-4-2 0-3 1-7 0-10h0c1-2 1-4 1-5z" class="r"></path><path d="M457 773l2 2c0 3 2 7 1 10l-1 12c0 7 0 14 1 21v2h-1c-3-11-3-23-3-34l2-2-1-7c0-1 0-1 1-1-1-1-1-2-1-3z" class="P"></path><path d="M457 773l2 2c0 3 2 7 1 10h-1l-1 8c-1-3 0-6 0-8v-1l-1-7c0-1 0-1 1-1-1-1-1-2-1-3z" class="h"></path><path d="M451 754h4v6 1 3 6 2 1l-4-2c0 1 0 3-1 5h0c1 3 0 7 0 10l-2-1h0c-2-3-4-5-6-8-1-4 2-9 3-13l1-2c0-1 1-3 2-3l1-1v-1c0-2 1-2 2-3z" class="G"></path><path d="M446 762c0-1 1-3 2-3v3c1 1 0 3 0 4l1 6h0l-2 2-1-1c0-1-1-4-1-5s1-2 1-3l-1-1 1-2z" class="F"></path><path d="M449 772c1 0 1 0 2-1v-1h1l-1 1c0 1 0 3-1 5h0c1 3 0 7 0 10l-2-1h0c-2-3-4-5-6-8l1-1c2 2 3 5 6 7v-1c-1-2-1-2-1-4h0c-1-2-2-2-2-4v-1l1 1 2-2h0z" class="P"></path><path d="M451 754h4v6 1 3 6 2 1l-4-2 1-1h-1v1c-1 1-1 1-2 1l-1-6c0-1 1-3 0-4v-3l1-1v-1c0-2 1-2 2-3z" class="r"></path><path d="M455 760v1 3 6 2 1l-4-2 1-1h-1v1c-1 1-1 1-2 1l-1-6 1 1c2 0 1-1 2-2h2c1-2 1-3 2-5z" class="b"></path><path d="M460 818l1-1c2 6 5 16 10 20 1 2 2 3 4 4l3 5c1 2 4 4 5 6 1 1 1 2 3 2l2-1h0l1 1 1 1c-2 1-5 3-6 5-2 1-3 2-5 3h-1v-1h0-1l-1-1c-1-1-1-3-1-4l5 5c-1-2-1-4-2-5s-1-1-1-2l-1-1v-1h-1c-2-2-15-16-15-18l1-1v-5h0c1 1 0 1 2 1-1-1-1-3-2-5 0-2 0-4-1-5v-2z" class="S"></path><path d="M480 853l-9-10c2 0 5 3 6 5l7 7h2l1-1h-1l2-1h0l1 1 1 1c-2 1-5 3-6 5-2 1-3 2-5 3h-1v-1h0-1l-1-1c-1-1-1-3-1-4l5 5c-1-2-1-4-2-5s-1-1-1-2l-1-1 5 5 2-1v-1h-2l2-2s-2-2-3-2z" class="h"></path><path d="M476 854v-1h-1c-2-2-15-16-15-18l1-1v-5h0c1 1 0 1 2 1 1 4 3 6 5 9-1 0-3-4-4-3 1 2 4 5 5 7 3 4 7 7 10 10h1c1 0 3 2 3 2l-2 2h2v1l-2 1-5-5z" class="D"></path><path d="M440 775l2 2c2 3 4 5 6 8h0l2 1c2 0 3 1 4 2 1 2 1 5 1 7v19 9l-16-36-1-2c0-3 2-6 2-10z" class="p"></path><path d="M447 753h33c-1 0-2 1-3 1l3 3h1 0v-1l2-2c1 1 1 3 1 5h-1c-1 4-2 8-2 13l-1 10v-4c-5 2-7 5-11 9l-3 8c-2 4-3 8-4 12-1 3-1 7-1 10l-1 1c-1-7-1-14-1-21l1-12c1-3-1-7-1-10l-2-2c1-3 0-7 0-9l1-2-1-2-2 1v-1-6h-4l-4-1z" class="F"></path><path d="M458 762l2-2c2 0 3 0 5 1l1-1c0 1 0 1 1 1h1v1h1 1v3l1 1 1-2c1 0 0 0 1 1 1-1 1-2 1-3 1 0 2 1 3 1 1 3 2 7 1 10l-1 1v-2l-2-4c0-1 0 0-1-1-1 2-1 3-1 6h-1v-5l-1-1v2l-1-1v-2h-1c-1 0-1-1-1-1-2 0-3 0-4 1-2-1-2-1-4-1-1 1-1 5-1 6l1 2v2h-1l-2-2c1-3 0-7 0-9l1-2z" class="N"></path><defs><linearGradient id="R" x1="467.322" y1="750.735" x2="463.441" y2="761.647" xlink:href="#B"><stop offset="0" stop-color="#2d2c2c"></stop><stop offset="1" stop-color="#454644"></stop></linearGradient></defs><path fill="url(#R)" d="M447 753h33c-1 0-2 1-3 1l3 3h1 0v-1 1 9c-2-3-2-6-4-9h-3c-1 0-1 1-1 2l-1-2h-1v2c-1 0-1-1-1-2l-1 1h-1v-1c-1-1-3-1-4-1h-4-1-1l-1 4-2 1v-1-6h-4l-4-1z"></path><path d="M470 768l1 1v-2l1 1v5h1c0-3 0-4 1-6 1 1 1 0 1 1l2 4v2c-1 1-4 2-4 3 2 0 3-1 5-2l2 1v2c-5 2-7 5-11 9v-1c-2 1-3 4-4 6v-1c0-2 2-4 2-6 0-1 0-1 1-2l2-4h1l-1-1h0l-1-1v-1-2c-1-2-1-3-1-5h0 2v-1z" class="I"></path><path d="M470 769v3c0 1 1 1 1 2 1 1-1 2-1 4l-1-1v-1-2c-1-2-1-3-1-5h0 2z" class="Z"></path><path d="M460 775v-2l-1-2c0-1 0-5 1-6 2 0 2 0 4 1 1-1 2-1 4-1 0 0 0 1 1 1h1v2 1h-2 0c0 2 0 3 1 5v2 1l1 1h0l1 1h-1l-2 4c-1 1-1 1-1 2 0 2-2 4-2 6v1c1-2 2-5 4-6v1l-3 8c-2 4-3 8-4 12-1 3-1 7-1 10l-1 1c-1-7-1-14-1-21l1-12c1-3-1-7-1-10h1z" class="E"></path><path d="M460 775v1l1 1h1c-1 1-1 1-1 2 0 0 1 0 1 1s-1 1 0 2c1 2 0 10-1 12l-1 1-1 2 1-12c1-3-1-7-1-10h1z" class="J"></path><path d="M469 777c-1 1-2 1-3 1s-2-1-3-2v-1h1v-6h2l1-1 1 1h0c0 2 0 3 1 5v2 1z" class="k"></path><path d="M468 769c0 2 0 3 1 5v2c-1 0-1 0-2 1-1-2-1-2-1-3 1-1 1-2 1-3s0-1 1-2z" class="o"></path><path d="M480 778v4 4c0 6 2 12 4 17 0 1-1 2 0 3v1c0 1-1 2-1 3h1l-4 6c2 0 2-1 3-1l1-1 3 1 1-1h0 2v1l1-1v-1l2 2h-1l1 2 2-2h1c0 1 1 3 1 4l-1 2h0v1l-2 3-7 7-2 3-3 6h-1l-1-4c-1 1-2 3-2 4v1 1h-1l1 2v1l-3-5c-2-1-3-2-4-4-5-4-8-14-10-20 0-3 0-7 1-10 1-4 2-8 4-12l3-8c4-4 6-7 11-9z" class="W"></path><path d="M462 815c2 3 3 8 6 10l1 1v6c1 1 2 2 2 4-4-6-8-14-9-21z" class="S"></path><path d="M470 787l1 2v1l-1 2h0l1-1h1l-1 1c0 1-1 3-1 5v5 15c0 3 1 7-1 9l-1-1c0-6-1-14 0-20 0-2 2-8 1-9h-1c0-1 0-1-1-2 1-3 2-5 3-7z" class="I"></path><path d="M473 784c2-1 2-2 4-3l1 1c0 1 0 1-1 2 0 1-1 2-2 3v10c-1 2 0 4 0 6 1 1 0 3 0 4 0 3 1 6 0 9-1 2-2 4-3 7 0-4 1-7 1-10v-16c0-3 0-7 1-9v-2c1-1 0-1-1-2z" class="a"></path><path d="M467 794c1 1 1 1 1 2h1c1 1-1 7-1 9-1 6 0 14 0 20-3-2-4-7-6-10 1-7 2-13 5-20v-1z" class="k"></path><path d="M480 778v4 4c0 6 2 12 4 17 0 1-1 2 0 3v1c0 1-1 2-1 3h1l-4 6c-1 1-1 2-2 3 0 2 0 2-1 4v2c0 3-2 4-3 7h2c-1 1-3 4-2 6 0 1 2 2 3 3v1c-1 0-2-1-2-1-2-1-3-2-4-4 1-1 0-3 0-5 0-3 1-6 1-9 1-3 2-5 3-7 1-3 0-6 0-9 0-1 1-3 0-4 0-2-1-4 0-6v-10c1-1 2-2 2-3 1-1 1-1 1-2l-1-1c-2 1-2 2-4 3l-3 3c-1 2-2 4-3 7v1h-1l3-8c4-4 6-7 11-9z" class="L"></path><path d="M474 822l1 1h2v2c0 3-2 4-3 7h-1c0-4 1-7 1-10z" class="G"></path><path d="M484 806v1c0 1-1 2-1 3h1l-4 6c-1 1-1 2-2 3 0 2 0 2-1 4h-2l-1-1c1-4 3-7 5-11l3-3 2-2z" class="C"></path><path d="M480 778v4 4c0 6 2 12 4 17 0 1-1 2 0 3l-2 2-3 3v-1s1-1 1-2c-1-2-2-4-2-6-1-6-1-12-1-18 1-1 1-1 1-2l-1-1c-2 1-2 2-4 3l-3 3c-1 2-2 4-3 7v1h-1l3-8c4-4 6-7 11-9z" class="K"></path><path d="M482 808c-1-1-1-3-2-4v-3l-1-1c0-3 0-5-1-8 0-3 0-4 2-6 0 6 2 12 4 17 0 1-1 2 0 3l-2 2z" class="m"></path><path d="M490 814v1l1-1v-1l2 2h-1l1 2 2-2h1c0 1 1 3 1 4l-1 2h0v1l-2 3-7 7-2 3-3 6h-1l-1-4c-1 1-2 3-2 4v1 1h-1l1 2v1l-3-5s1 1 2 1v-1c-1-1-3-2-3-3-1-2 1-5 2-6h-2c1-3 3-4 3-7v-2c1-2 1-2 1-4 1-1 1-2 2-3 2 0 2-1 3-1l1-1 3 1 1-1h0 2z" class="Q"></path><path d="M484 814l3 1 1-1h0c-1 1-2 3-4 4-1 1-2 1-3 2h0c0-1 1-1 1-2v-1h1v-2l1-1z" class="W"></path><path d="M480 816c2 0 2-1 3-1v2h-1v1c0 1-1 1-1 2h0c-1 2-2 4-4 5v-2c1-2 1-2 1-4 1-1 1-2 2-3z" class="X"></path><path d="M476 832h0l2-2c0 1-1 2-2 4l1 2v1 3l1 1v1 1h-1l1 2v1l-3-5s1 1 2 1v-1c-1-1-3-2-3-3-1-2 1-5 2-6z" class="K"></path><path d="M492 815l1 2 2-2h1c0 1 1 3 1 4l-1 2h0v1l-2 3-7 7-2 3-3 6h-1l-1-4c0-7 3-12 7-17 2-1 4-2 5-4v-1z" class="C"></path><path d="M487 832l-2-1c0-2 0-2 1-3 1-3 4-7 8-8 1 0 0 0 1 1l-1 1v3l-7 7z" class="E"></path><path d="M511 751h15c4 0 9-1 12 2h5c0 1-1 1 0 2v2h2v4c-2 2-4 4-5 6 0 1 0 1 1 2v1l-2-2-2 1v1l-2 1-1 3-1 1-2 8-1 1v2 3l-1 1-2 1 1 1v5l-1 1v1 6c0 1 2 1 2 2v1c-1 2-3 3-5 4-1 1-2 1-3 2-4 1-8 3-12 4l-8-2-1 2c-1-1-2-2-4-3h-1l-2 2-1-2h1l-2-2v1l-1 1v-1h-2 0l-1 1-3-1-1 1c-1 0-1 1-3 1l4-6h-1c0-1 1-2 1-3v-1c-1-1 0-2 0-3-2-5-4-11-4-17v-4l1-10c0-5 1-9 2-13h1c0-2 0-4-1-5l-2 2v1h0-1l-3-3c1 0 2-1 3-1h11v-1h6c2 0 4-1 6-1h8z" class="D"></path><path d="M509 802l2 1v2l-1 1h-6l5-3v-1z" class="S"></path><path d="M496 801v-1c2-1 5-3 8-3 2 1 2 2 3 3-1 0-2-1-3-2l-1 1 1 1-2 1c-1 1-2 2-3 2l-1-1h-1l-1-1z" class="B"></path><path d="M504 791l2-1v-2h1c1 1 1 0 0 1v9h1v-1c1 0 2 1 2 2 0 2 0 0-1 1v1l-2-1c-1-1-1-2-3-3-3 0-6 2-8 3v1h-1v-3-1h1 2c0-1 1-1 2-1h1c1-2 1-4 1-6 1 1 0 3 1 4 0 1 0 1 1 1v-4z" class="K"></path><path d="M484 759h1c3 0 2-2 3-3l1 1 1 1h0v10h-1c0-2 0-6-1-8 0 2 0 7-1 8h-1c0 11-2 27 5 37l2 2h-1-1c-2-2-4-5-4-8-3-8-2-17-2-25l-1-15z" class="V"></path><path d="M481 772c0-5 1-9 2-13h1l1 15c0 8-1 17 2 25 0 3 2 6 4 8h-1c-2-2-3-4-4-6-2-4-3-7-4-11-1-6 0-12-1-18z" class="I"></path><path d="M491 805l1-1-1-2c-1-1-2-2-2-4h0l-2-6h1c2 2 1 5 3 8l1 2 1 2h1l1-1h0c1 1 2 1 3 1 1 1 1 2 2 3v1c2 1 2 0 3 2h0v1l1-1c1-1 2-2 4-3h0l2-1 1-1h1 1l-1 2h1l-3 1c1 1 2 1 1 2l-1 1c4 2 7-2 10 1h1c-1 1-2 1-3 2-4 2-7 3-10 3-8-1-11-5-16-10h1l-2-2z" class="j"></path><path d="M511 805h1 1l-1 2h1l-3 1c1 1 2 1 1 2l-1 1h-5v-1c1 0 2-1 3-2v-1h0l2-1 1-1z" class="J"></path><path d="M493 807h1 0v-1s1 1 2 1c1 2 3 4 6 4 1 0 2 1 3 1 1 1 2 1 4 1h2c-1 1-1 1-2 1v1c1 0 2 0 2-1h2c2 0 3-1 4-1s2-1 3-1h1c-1 1-2 1-3 2-4 2-7 3-10 3-8-1-11-5-16-10h1z" class="O"></path><path d="M480 782l1-10c1 6 0 12 1 18 1 4 2 7 4 11 1 2 2 4 4 6h1 1c5 5 8 9 16 10 3 0 6-1 10-3 1-1 2-1 3-2h-1c-3-3-6 1-10-1l1-1c1-1 0-1-1-2l3-1h-1l1-2h-1-1v-2l1-1c-1-2 0-2 0-4l2 1c-1 1-1 2-1 4 2 2 4 3 6 4h5l1-2c1 0 1 0 1-1 0-2 0-3 1-5v6c0 1 2 1 2 2v1c-1 2-3 3-5 4-1 1-2 1-3 2-4 1-8 3-12 4l-8-2-1 2c-1-1-2-2-4-3h-1l-2 2-1-2h1l-2-2v1l-1 1v-1h-2 0l-1 1-3-1-1 1c-1 0-1 1-3 1l4-6h-1c0-1 1-2 1-3v-1c-1-1 0-2 0-3-2-5-4-11-4-17v-4z" class="N"></path><path d="M512 798l2 1c-1 1-1 2-1 4 2 2 4 3 6 4h5l1-2c1 0 1 0 1-1 0-2 0-3 1-5v6c0 1 2 1 2 2-2 0-3 1-5 2h0l-1 1c-2 0-3 0-4-1h-1l-1 1h-2c0-1-1-1-2-2v-1h-1l1-2h-1-1v-2l1-1c-1-2 0-2 0-4z" class="V"></path><path d="M511 803c4 3 8 6 13 6l-1 1c-2 0-3 0-4-1h-1l-1 1h-2c0-1-1-1-2-2v-1h-1l1-2h-1-1v-2z" class="I"></path><path d="M484 803c1 2 3 4 5 6 1 1 2 1 3 2 3 2 6 4 9 5l-1 2c-1-1-2-2-4-3h-1l-2 2-1-2h1l-2-2v1l-1 1v-1h-2 0l-1 1-3-1-1 1c-1 0-1 1-3 1l4-6h-1c0-1 1-2 1-3v-1c-1-1 0-2 0-3z" class="L"></path><path d="M484 814c2-1 3-2 5-3 1 1 1 1 1 3h-2 0l-1 1-3-1z" class="M"></path><path d="M531 769c2-1 4-1 5-1l1 1v1l-2 1-1 3-1 1-2 8-1 1v2 3l-1 1-2 1 1 1v5l-1 1v1c-1 2-1 3-1 5 0 1 0 1-1 1l-1 2h-5c-2-1-4-2-6-4 0-2 0-3 1-4l-2-1c0 2-1 2 0 4l-1 1-2-1v-1-1c1-1 1 1 1-1 0-1-1-2-2-2v1h-1v-9c1-1 1 0 0-1 0-1 0-3 1-4h2v2c1 1 1 2 2 3h0v-3-4c1-1 1-1 1-3h0 2l1-2c2-2 4-3 6-4h0l4-2 2-1 3-1z" class="F"></path><path d="M518 785l2-1c0-1-1-2-1-3l2-2c0 3 1 4 0 7l-1 1-2-2z" class="t"></path><path d="M512 798h0l-1-1c1 0 2-3 3-3s1 0 2-1v-3h1l1 1c0 1-1 2-1 3l-2 3-1 2h0l-2-1z" class="O"></path><path d="M518 791l2-2 1-3c2 0 2 0 3 1l1 2v1 1c0 1-1 2-1 3h-3v1h1v1c-2 0-3 0-4-2h-1c0-1 1-2 1-3z" class="W"></path><path d="M526 771l2-1c1 1 1 1 1 3h0c-1 1-2 2-2 3-1 0-1 0-1-1l-2 1h0c-1 0-2 0-3 1h0v2l-2 2c0 1 1 2 1 3l-2 1c-1-1-4-2-5-3 0 0 1-2 2-3l1-2c2-2 4-3 6-4h0l4-2z" class="c"></path><path d="M526 771l2-1c1 1 1 1 1 3h0c-1 1-2 2-2 3-1 0-1 0-1-1l-2 1h0c-1 0-2 0-3 1h0c-1 2-2 2-3 3-1-1-1-1-1-2 1 0 1-1 2-1l1-1c2 0 2-2 2-3h0l4-2z" class="U"></path><path d="M531 769c2-1 4-1 5-1l1 1v1l-2 1-1 3-1 1-2 8-1 1v2 3l-1 1-2 1 1 1v5l-1 1v1c-1 2-1 3-1 5 0 1 0 1-1 1l-1 2h-5c-2-1-4-2-6-4 0-2 0-3 1-4h0l1-2 2-3h1c1 2 2 2 4 2v-1h-1v-1h3c0-1 1-2 1-3v-1-8-3c-1-1-1-2-1-3h0 0l2-1c0 1 0 1 1 1 0-1 1-2 2-3h0c0-2 0-2-1-3l3-1z" class="Q"></path><path d="M525 791c1 2 1 3 1 5-1 1-1 1-1 2-1 0-1 0-2-1h1v-3c0-1 1-2 1-3z" class="O"></path><path d="M531 769c2-1 4-1 5-1l1 1v1l-2 1-1 3-1 1-2 8-1 1s-2-2-3-2c1-3 2-1 3-3l-1-1 1-4-1-1c0-2 0-2-1-3l3-1z" class="m"></path><path d="M531 769c2-1 4-1 5-1l1 1v1l-2 1c-1 0-2 1-3 1l-1-3z" class="U"></path><path d="M517 794h1c1 2 2 2 4 2v-1h-1v-1h3v3h-1c1 1 1 1 2 1-1 1-1 1-2 1 0 1 0 1 1 1l1 1h-1l-1 2v1 2h1v-1h1l-1 2h-5c-2-1-4-2-6-4 0-2 0-3 1-4h0l1-2 2-3z" class="X"></path><path d="M517 801c1-1 1-2 2-3 2 0 2 0 4 1 0 1 0 1 1 1l-4 1v1h1v2l-4-3z" class="e"></path><path d="M517 794h1c1 2 2 2 4 2v-1h-1v-1h3v3h-1c1 1 1 1 2 1-1 1-1 1-2 1-2-1-2-1-4-1-1 1-1 2-2 3v-1c-1-1-1-1 0-1l-2-2 2-3z" class="i"></path><path d="M511 751h15c4 0 9-1 12 2h5c0 1-1 1 0 2v2h2v4c-2 2-4 4-5 6 0 1 0 1 1 2v1l-2-2-2 1-1-1c-1 0-3 0-5 1l-3 1-2 1-4 2h0c-2 1-4 2-6 4l-1 2h-2 0c0 2 0 2-1 3v4 3h0c-1-1-1-2-2-3v-2h-2c-1 1-1 3-1 4h-1v2l-2 1v4c-1 0-1 0-1-1-1-1 0-3-1-4 0 2 0 4-1 6h-1c-1 0-2 0-2 1h-2v-3c-1-3-1-6-2-8v-6-13c-1-3-1-7-1-10l-1 11c-2-3 0-8-1-11l-1 1h0l-1-1-1-1c-1 1 0 3-3 3h-1c0-2 0-4-1-5l-2 2v1h0-1l-3-3c1 0 2-1 3-1h11v-1h6c2 0 4-1 6-1h8z" class="c"></path><path d="M511 751h15c4 0 9-1 12 2h-47v-1h6c2 0 4-1 6-1h8z" class="T"></path><path d="M524 757l1 1 1-2v1 6 5h1v-12h1 1c0 2 1 5 0 7v1c1-1 0-3 1-4v-3l1-1c1 2 0 4 0 6v1 5h1c0-1 0-1-1-2 0-1 0-2 1-3 0-2-1-5 0-7l1 1c1 1 1 1 1 3 1-2 0-3 1-4 2 1 1 2 1 4 1-2 0-2 1-3h1 1 0 4 2v4c-2 2-4 4-5 6 0 1 0 1 1 2v1l-2-2-2 1-1-1c-1 0-3 0-5 1l-3 1-2 1-2-1v-2-8-3z" class="F"></path><path d="M538 757h1 0 4 2v4c-2 2-4 4-5 6 0 1 0 1 1 2v1l-2-2-2 1-1-1c1-1 2-2 2-3v-8z" class="m"></path><path d="M508 755h0l2 1h1c1 1 1 2 1 3l2-2c0-1 2 0 3 0h1c1 2 1 4 1 7 1-3 1-6 2-8h1 2v1 3 8 2l2 1-4 2h0c-2 1-4 2-6 4l-1 2h-2v-1c-1-3-1-5-1-7v-2-3-5l-5 1c0-2 1-5 1-7z" class="i"></path><path d="M524 757v3c-2 1-2 1-4 3 0-1 1-3 1-5 1 0 1 0 3-1h0z" class="M"></path><path d="M520 763c2-2 2-2 4-3v8h-3v1c-1-2 0-4-1-6z" class="D"></path><path d="M512 766c1-1 1-2 1-4 2 0 3-1 4-2h0 1v3c-1 0-1 0-2 1l-1 4c-1 1-2 1-3 1v-3z" class="M"></path><path d="M512 769c1 0 2 0 3-1l1-4c1-1 1-1 2-1 0 2 0 5 1 7v2 1h0l1-1 1-3v-1h3v2l2 1-4 2h0c-2 1-4 2-6 4l-1 2h-2v-1c-1-3-1-5-1-7v-2z" class="N"></path><path d="M521 769v-1h3v2l2 1-4 2h0c-2 1-4 2-6 4l-1-1 1-1c0-1-1-1-1-2s1-3 2-4l1 1 1 2v1h0l1-1 1-3z" class="Y"></path><path d="M504 758v-4h3l1 1c0 2-1 5-1 7l5-1v5 3 2c0 2 0 4 1 7v1h0c0 2 0 2-1 3v4 3h0c-1-1-1-2-2-3v-2h-2c-1 1-1 3-1 4h-1v2l-2 1v4c-1 0-1 0-1-1-1-1 0-3-1-4 0 2 0 4-1 6h-1c-1 0-2 0-2 1h-2v-3c-1-3-1-6-2-8v-6-13c0-3 0-5 1-7h0l1 4c0-1 0-2 1-4h0c1 1 3 2 4 2v1c1 1 1 1 1 2v-1l1-6h1z" class="E"></path><path d="M504 758v-4h3l1 1c0 2-1 5-1 7l5-1v5 3 2c0 2 0 4 1 7v1h0c0 2 0 2-1 3v4 3h0c-1-1-1-2-2-3v-2h-2c-1 1-1 3-1 4h-1v2l-2 1v-24-9z" class="V"></path><path d="M504 758v-4h3l1 1c0 2-1 5-1 7v3 1 2c-1-1-1-3-1-4-1 0-1 1-1 1 0 1 0 2-1 2v-9z" class="U"></path><path d="M507 762l5-1v5 3 2c0 2 0 4 1 7v1h0l-6 2v-4-12-3z" class="J"></path><path d="M510 779h-1c-1-2-1-5-1-7l2-2c0 2 1 4 0 5v4z" class="C"></path><path d="M510 770l1-1 1 2c0 2 0 4 1 7l-1 1h-2v-4c1-1 0-3 0-5z" class="E"></path><path d="M529 808c1-1 1-1 2-1l2 5c4 8 11 15 21 18l4 1h1c5 1 10 0 16-2h1c-3 4-5 10-7 15l-7 16-31 75c-4 10-8 20-13 30v-1l-6-14-10-22c-2-4-4-9-6-14-6-14-11-28-17-41l1-2-2-1v-2c2 0 3 1 4 2l1-1c-1 0-1-1-1-2v-2h0l1-1 1 1c0-1-1-2-2-2l2-3c1-2 4-4 6-5l-1-1-1-1h0l-2 1c-2 0-2-1-3-2-1-2-4-4-5-6v-1l-1-2h1v-1-1c0-1 1-3 2-4l1 4h1l3-6 2-3 7-7 2-3v-1h0l1-2c0-1-1-3-1-4 2 1 3 2 4 3l1-2 8 2c4-1 8-3 12-4 1-1 2-1 3-2 2-1 4-2 5-4z" class="r"></path><path d="M525 882c0 2-1 4 1 6l2 2h-3c-1 1-1 1-1 3h0c2 0 2 1 3 1l-2 1-1-1c-2-2-2-2-2-5 1-2 1-5 3-7z" class="c"></path><g class="b"><path d="M530 914l1 1v3l-1 2-2 1-1 6c0 1 0 2-1 3l-1-1 1-1v-2-4h0 1v-4-2c1-1 2-1 3-2zm12-43c1 0 2 1 3 1v-1l-1-1h0 2c0-1 0-1 1-1h0c0-1 0-1-1-2h-1l1-1 1 1 1-1 2 1v1h-1l1 1-1 2h0c-1 0-2 1-3 1l-2 2h1 0v1l-2-1-1 2-1-1 1-1-1-1-1-1c1-1 1-1 2-1z"></path><path d="M527 894c3 1 4 3 7 4h1l3 1-1 2v1 2l-1 1 1 1-1 1v1c0 2-2 2-3 3-1 0-1 1-2 1l1-1c-1-1-1-1-1-2l2-1v-1h-1l1-1-1-2c-1-1-1 0-1-1h1 0l-1-2h0 2 0c-1-1-1-2-2-2l-1-1-5-3 2-1z"></path></g><path d="M527 918h0c-3-10-6-19-5-29 0 3 0 3 2 5l1 1 5 3 1 1c1 0 1 1 2 2h0-2 0l1 2h0-1c0 1 0 0 1 1l1 2-1 1h1v1l-2 1c0 1 0 1 1 2l-1 1h-1c-1 1-1 2 0 2-1 1-2 1-3 2v2z" class="V"></path><path d="M533 843c0-1 0-1 1-2h0l-1-1v-1c0-1 0-1 1-2v-1-1-1h1c0-1 0-1 1-2 0 0-1 0-1-1h1l-1-1h1c1 0 1 0 2 1h1l1-1 1 1 1 2c2 0 1 0 3 1h3 1v1c3 0 3 1 5 3h-1c-2 0-3 0-5 1l-2 1c-2 2-2 4-2 7v1l2 4c-1-1-2-1-2-2l-1-3v-1-2h0c1-1 1-2 1-2l-1-1-1 2c-1 2-1 5 0 7 0 1 1 2 1 4-2-2-2-5-4-6h0l1-1-1-1h-1v-2-2l-2-1-3 2z" class="m"></path><path d="M536 841l-1-1h-1l2-2 1 1h1c1 0 1 1 2 2h2c-1 1 0 1-1 2 0 0 0 1-1 1 0 1 0 2-1 2h-1v-2-2l-2-1z" class="K"></path><path d="M548 834h1v1c3 0 3 1 5 3h-1c-2 0-3 0-5 1l-2 1c-1 0-1-1-2 0h0l-1-1s1-1 1-2l4-2v-1z" class="c"></path><path d="M553 838h1c2 1 5 2 6 5 1 2 0 5-1 7-2 2-4 3-6 3h-2c-2 0-4-1-5-1l-2-4v-1c0-3 0-5 2-7l2-1c2-1 3-1 5-1z" class="G"></path><path d="M544 848c1-1 1-2 1-4h0 1v1c0 2 0 2 1 3v-1s1 0 1 1c1 1 3 1 4 1v1h1c1-1 1 0 1 0l1 1c-2 0-3 1-5 1l1 1c-2 0-4-1-5-1l-2-4z" class="i"></path><path d="M547 847c0-2 0-3 1-4s2-2 4-1c2 0 3 0 4 2 0 2-1 3-2 4l-2 1c-1 0-3 0-4-1 0-1-1-1-1-1z" class="c"></path><path d="M551 844h2v2l-1 1c-2 0-2 0-3-1v-1l2-1z" class="p"></path><path d="M553 838h1c2 1 5 2 6 5 1 2 0 5-1 7-2 2-4 3-6 3h-2l-1-1c2 0 3-1 5-1l-1-1s0-1-1 0h-1v-1l2-1h1c1-1 2-2 2-4v-1c-1-2-2-2-3-3h-5l-1 1v-1h0l1-1h3 1v-1z" class="a"></path><path d="M530 869c1-1 1-2 2-3v-2h4 2v1h-2c1 1 1 1 1 2h0c-1 1-1 0 0 1v1 1l1 1c1 0 1 1 1 2h0c3 3 3 4 3 8 0 2 2 1 0 2h0c-2 1-3 2-4 2-1 2-2 4-5 5h-5l-2-2c-2-2-1-4-1-6s1-3 2-4l1-4c1 0 2-2 2-3v-2z" class="M"></path><path d="M539 880l-2 2v-1c0-2-1-3-2-4h-1l2-2 1 1v-1c1 2 2 3 2 4v1z" class="P"></path><path d="M531 878h1c2 0 3 1 3 2 1 1 1 2 0 4-1 1-2 2-4 3h0c-2 0-4-1-5-2v-3c1-2 3-3 5-4z" class="U"></path><path d="M530 869c1-1 1-2 2-3v-2h4 2v1h-2c1 1 1 1 1 2h0c-1 1-1 0 0 1v1 1l1 1c1 0 1 1 1 2h0c3 3 3 4 3 8 0 2 2 1 0 2h0c-2 1-3 2-4 2 1-1 1-3 1-5v-1c0-1-1-2-2-4-1-1-2-2-3-2-2 0-3 1-5 2l-2 3h0l1-4c1 0 2-2 2-3v-2z" class="K"></path><path d="M530 869c1-1 1-2 2-3v-2h4l-1 1h-2c1 1 1 2 2 2l-1 4c-1 0-2 1-3 1l-1-1v-2z" class="c"></path><path d="M531 861c1-8 0-17 1-26 1 4 1 6 0 10v1c1-1 1-2 1-2v-1l3-2 2 1v2 2h1l1 1-1 1h0c2 1 2 4 4 6 1 2 5 3 7 4 2 0 2 1 4 1l-1 1h-1l2 1c-1 1-1 1-1 2h1v3c-1 1-1 1-2 1 0 2 1 1 0 2s-2 1-3 2h0l1-2-1-1h1v-1l-2-1-1 1-1-1-1 1h1c1 1 1 1 1 2h0c-1 0-1 0-1 1h-2 0l1 1v1c-1 0-2-1-3-1-3-1-3-1-5-2v-1c-1-1-1 0 0-1h0c0-1 0-1-1-2h2v-1h-2-4v2c-1 1-1 2-2 3v-1c-1-2 0-5 1-7z" class="Q"></path><path d="M550 858c2 0 2 1 4 1l-1 1h-1l2 1c-1 1-1 1-1 2h1v3c-1 1-1 1-2 1 0 2 1 1 0 2s-2 1-3 2h0l1-2-1-1h1v-1l-2-1s-1 0-1-1c1 0 2 1 3 1v-1l-3-2h-1 0 4 0l-5-3h0c2 1 3 1 5 1v-1h0c-1 0-2-1-3-2 2 1 2 1 3 0z" class="c"></path><defs><linearGradient id="S" x1="492.042" y1="904.748" x2="512.308" y2="896.866" xlink:href="#B"><stop offset="0" stop-color="#0c0c0b"></stop><stop offset="1" stop-color="#3f3f3e"></stop></linearGradient></defs><path fill="url(#S)" d="M481 872l1-1 5 4c1 0 2-1 3-1l7 5 6 5 3 2 6 6h1v-4h1c0 6 1 8 5 13 0 2 2 3 3 5l1 3c-2-1-3-4-4-5 0 1 1 3 1 4v1l3 16c0 2 0 4-1 6h-1 0c-1 1-1 1-1 2h1v2h-1c0 2 0 5-1 7l-1 20-3-13c-1 0-2 1-3 1l-10-22c-2-4-4-9-6-14-6-14-11-28-17-41l1-2 1 1z"></path><path d="M504 913v-2h-3s-1-1-2-1h0 3v-1h-1c1-1 1-1 2-1v-1c-2-1-3-2-4-3l1-1v1c1 0 1 0 2 1h0l-1-1 1-1c0-1-1-1-1-1v-1-2l2-1h0l-1 1v1c0 1 0 2 1 3v1h2l-1 1v1c2 0 4 1 6 1v1c1 0 2 1 3 2l1 1h-1l-2 1c-3-1-4-1-7 1z" class="c"></path><path d="M503 904v-4h1c1-2-1 0 1-1l1-2c-1-1-3-2-4-3v-1c2 1 3 2 5 4l1 1c2 2 2 2 3 4h0c1 2 2 4 2 6v2c-1-1-2-2-3-2v-1c-2 0-4-1-6-1v-1l1-1h-2z" class="n"></path><path d="M506 898h1c1 1 2 2 3 4v1l-2-1-1-1h-2-1l2-3z" class="O"></path><path d="M481 872l1-1 5 4c1 0 2-1 3-1l7 5 6 5 3 2 6 6h1v-4h1c0 6 1 8 5 13 0 2 2 3 3 5l1 3c-2-1-3-4-4-5 0 1 1 3 1 4v1l3 16c0 2 0 4-1 6-1-3 0-8-1-11-4-23-24-34-40-48z" class="B"></path><path d="M490 874l7 5c-1 0-2 1-3 2l-7-6c1 0 2-1 3-1z" class="J"></path><path d="M512 892h1v-4h1c0 6 1 8 5 13 0 2 2 3 3 5l1 3c-2-1-3-4-4-5 0 1 1 3 1 4v1c-2-2-2-5-3-7-2-3-5-6-8-9 1-1 1-1 3-1z" class="O"></path><defs><linearGradient id="T" x1="508.362" y1="887.579" x2="494.409" y2="881.318" xlink:href="#B"><stop offset="0" stop-color="#6d6d6c"></stop><stop offset="1" stop-color="#a3a3a2"></stop></linearGradient></defs><path fill="url(#T)" d="M497 879l6 5 3 2 6 6c-2 0-2 0-3 1l-15-12c1-1 2-2 3-2z"></path><path d="M504 913c3-2 4-2 7-1 2 1 4 1 5 3 1 5 2 11 3 17v10l-1 20-3-13c-1 0-2 1-3 1l-10-22h1 1v-2c-1-1 0-1 0-2-1-2-2-3-2-5-1-2 1-4 2-6z" class="X"></path><path d="M507 915h4c1 1 2 3 3 4v1c-2 1-3 2-4 3h-1c-2 0-3 0-5-1-1-1-1-2-1-3 1-2 2-3 4-4z" class="U"></path><path d="M504 924h1c2 1 3 2 5 2 2-1 4-2 5-4l1-1c-3 9-2 19-1 28-1 0-2 1-3 1l-10-22h1 1v-2c-1-1 0-1 0-2z" class="p"></path><path d="M529 808c1-1 1-1 2-1l2 5-2 2c1 2 1 4 1 6v15c-1 9 0 18-1 26-1 2-2 5-1 7v1 2c0 1-1 3-2 3l-1 4c-1 1-2 2-2 4-2 2-2 5-3 7-1 10 2 19 5 29h0v4h-1 0c0-5-2-9-3-13l-1-3c-1-2-3-3-3-5-4-5-5-7-5-13h-1v4h-1l-6-6-3-2-6-5-7-5c-1 0-2 1-3 1l-5-4-1 1-1-1-2-1v-2c2 0 3 1 4 2l1-1c-1 0-1-1-1-2v-2h0l1-1 1 1c0-1-1-2-2-2l2-3c1-2 4-4 6-5l-1-1-1-1h0l-2 1c-2 0-2-1-3-2-1-2-4-4-5-6v-1l-1-2h1v-1-1c0-1 1-3 2-4l1 4h1l3-6 2-3 7-7 2-3v-1h0l1-2c0-1-1-3-1-4 2 1 3 2 4 3l1-2 8 2c4-1 8-3 12-4 1-1 2-1 3-2 2-1 4-2 5-4z" class="X"></path><path d="M520 892c0-1 0-3-1-4 1-2 1-5 1-7 1-1 1 0 1-1v-1-1c0-1-1-1 0-2v-5c1 3 1 3 3 5-2 3-1 6-2 9 0 1-1 3-1 4s0 2-1 3z" class="d"></path><path d="M509 818c4-1 8-3 12-4 0 1 1 1 0 3v2 1h-1v-2c-1 1-1 4-1 6-1-1-1-3-1-5-1 1-1 1-1 2-1-1-1-2-1-3l-1 1v1c-1-1-1-1-1-2-1 1-1 1-1 2v2c-2 0-3 0-5-1 0-1 0-2 1-3z" class="c"></path><path d="M516 831v-7h1c1 4 0 11 0 15v36 11c0 1 0 0-1 1v-56z" class="Z"></path><path d="M524 871l2-2c0 1 1 1 1 2l-1 4h1v-1h1l-1 4c-1 1-2 2-2 4-2 2-2 5-3 7-1 10 2 19 5 29h0v4h-1 0c0-5-2-9-3-13l-1-3c-1-5-2-9-2-14 1-1 1-2 1-3s1-3 1-4c1-3 0-6 2-9v-5z" class="Y"></path><path d="M514 823c2 4 1 11 1 14l1-6v56c1-1 1 0 1-1 1 4 1 9 2 13v2c-4-5-5-7-5-13v-18-47z" class="R"></path><path d="M524 824c2 2 1 9 1 12v3h1v18 7h0v5l-2 2v5c-2-2-2-2-3-5v-11-27c0-3 0-6 1-8h1v3h1v-4z" class="g"></path><path d="M521 860h0c1 3 1 6 1 9h1v-4h1v6 5c-2-2-2-2-3-5v-11z" class="J"></path><path d="M524 824c2 2 1 9 1 12v3h1v18 7h0v5l-2 2v-6-6-1c-1-3 0-6 0-9v-21-4z" class="H"></path><defs><linearGradient id="U" x1="513.479" y1="860.472" x2="510.768" y2="860.575" xlink:href="#B"><stop offset="0" stop-color="#585856"></stop><stop offset="1" stop-color="#6f6f6d"></stop></linearGradient></defs><path fill="url(#U)" d="M513 820l1 3v47 18h-1v4h-1l-6-6 1-1 2-1v-5c0-3 1-6 1-9 1-6 1-15 0-21l-1-8-1-1c0-2-1-4-2-6-1 0-1 0-2-1h0c-1 0-2 1-3 1h-2l2-3c1-1 2-2 2-3 1-1 1-2 2-3s2-2 3-4c2 1 3 1 5 1v-2z"></path><path d="M508 821c2 1 3 1 5 1v9h-1v-1c0-2 0-4-2-6-1 0-3 1-4 1 0 1-1 2-1 2l-1 2-1-1c1-1 1-2 2-3s2-2 3-4z" class="b"></path><path d="M503 828l1 1 1-2c0 2 1 2 1 3 1 1 2 0 3 0v-2h1c1 3 0 5 1 8 0 5 0 48-1 50-1 1-2 0-3-1l2-1v-5c0-3 1-6 1-9 1-6 1-15 0-21l-1-8-1-1c0-2-1-4-2-6-1 0-1 0-2-1h0c-1 0-2 1-3 1h-2l2-3c1-1 2-2 2-3z" class="W"></path><path d="M529 808c1-1 1-1 2-1l2 5-2 2c1 2 1 4 1 6v15c-1 9 0 18-1 26-1 2-2 5-1 7v1 2c0 1-1 3-2 3h-1v1h-1l1-4c0-1-1-1-1-2v-5h0v-7-18h-1v-3c0-3 1-10-1-12v-1-1l2-2c2 1 1 2 2 4h0c0-1 0-1 1-2v-3h-2-1v-1l-1-1v2c-1-1-1 0-2-1h-1v1h-1v-2c1-2 0-2 0-3 1-1 2-1 3-2 2-1 4-2 5-4z" class="N"></path><path d="M527 831l1-1c0-2 1-3 1-5h0l1 12h-2c-1-2-1-4-1-6z" class="g"></path><path d="M527 815h0c1-1 2-2 4-3v2c1 2 1 4 1 6h-1l-1 6v-7h-1-2-1v-1l-1-1v2c-1-1-1 0-2-1h-1l2-2c1-1 2-1 3-1z" class="Q"></path><path d="M529 808c1-1 1-1 2-1l2 5-2 2v-2c-2 1-3 2-4 3h0c-1 0-2 0-3 1l-2 2v1h-1v-2c1-2 0-2 0-3 1-1 2-1 3-2 2-1 4-2 5-4z" class="t"></path><path d="M521 814c1-1 2-1 3-2v3h3c-1 0-2 0-3 1l-2 2v1h-1v-2c1-2 0-2 0-3z" class="K"></path><path d="M526 839v-14h1v6c0 2 0 4 1 6h2c-1 4 0 8-1 12v8l2 4c-1 2-2 5-1 7v1 2c0 1-1 3-2 3h-1v1h-1l1-4c0-1-1-1-1-2v-5h0v-7-18z" class="B"></path><path d="M527 825v6c0 2 0 4 1 6h2c-1 4 0 8-1 12v-7l-1-1c-2-1-1-13-1-16z" class="E"></path><path d="M529 857l2 4c-1 2-2 5-1 7v1 2c0 1-1 3-2 3h-1v1h-1l1-4 1-1c-1-1-1-1-1-2h0c0-2 1-4 1-5l1-1-1-1c1-2 1-3 1-4z" class="I"></path><path d="M496 815c2 1 3 2 4 3l1-2 8 2c-1 1-1 2-1 3-1 2-2 3-3 4s-1 2-2 3c0 1-1 2-2 3l-2 3h2c1 0 2-1 3-1h0c1 1 1 1 2 1 1 2 2 4 2 6l1 1 1 8c1 6 1 15 0 21 0 3-1 6-1 9v5l-2 1-1 1-3-2-6-5-7-5c-1 0-2 1-3 1l-5-4-1 1-1-1-2-1v-2c2 0 3 1 4 2l1-1c-1 0-1-1-1-2v-2h0l1-1 1 1c0-1-1-2-2-2l2-3c1-2 4-4 6-5l-1-1-1-1h0l-2 1c-2 0-2-1-3-2-1-2-4-4-5-6v-1l-1-2h1v-1-1c0-1 1-3 2-4l1 4h1l3-6 2-3 7-7 2-3v-1h0l1-2c0-1-1-3-1-4z" class="C"></path><path d="M490 874c-2-1-4-2-5-4l1-1c1 2 3 3 5 4h1c4 0 8 6 11 9v1 1l-6-5-7-5z" class="Y"></path><g class="H"><path d="M505 880h-2c-2-1-4-3-6-5-1-2-3-4-4-5h-1v-2l-2-2v-1l1 1 1-1-2-1 1-1 1 1v-1l-2-2c0-1 0-2 1-3 1 1 2 1 3 1h0 1l-2 2c1 1 0 2 1 3v2l3 3c-2 0-3-2-5-2l8 8c1 1 4 3 5 5z"></path><path d="M505 838c1 1 2 3 2 4 1 2 0 5 0 8l1 24c0 3-2 5-1 8-1 0-2-1-2-2v-1c-1-2-2-4-4-6h1l2 2c1 0 1 0 1-1-1-2-1-2-1-4h1c1-1 1-2 1-4 0-1 0-2 1-3-1-1-1-2-1-4-1-3 0-5-1-8v-13z"></path></g><path d="M507 863c0 4 0 8-2 11l1 2v1c-1-1-2-1-2-2 1 0 1 0 1-1-1-2-1-2-1-4h1c1-1 1-2 1-4 0-1 0-2 1-3z" class="S"></path><path d="M495 859l1 1c2 2 4 5 7 7l1-1 1 2v1c-1 1-1 0-1 1 0 2 0 2 1 4 0 1 0 1-1 1l-2-2h-1c2 2 3 4 4 6v1c-1-2-4-4-5-5l-8-8c2 0 3 2 5 2l-3-3v-2c-1-1 0-2-1-3l2-2z" class="k"></path><path d="M485 835l1 1c-1 2-4 7-3 10 0 1 0 1 1 2 2 1 4 2 6 1l3-1c1-1 2-2 3-4l1-1h1c-1 2-1 4-1 6-1 2-2 3-3 4-3 2-5 4-7 6v1c1 1 1 1 0 2h1l-1 1-1 1c0 1 2 3 4 4 0 1 0 1 1 1l-1 1c-1-1-2-1-3-2h-1l1 1h-1l-1 1c1 2 3 3 5 4-1 0-2 1-3 1l-5-4-1 1-1-1-2-1v-2c2 0 3 1 4 2l1-1c-1 0-1-1-1-2v-2h0l1-1 1 1c0-1-1-2-2-2l2-3c1-2 4-4 6-5l-1-1-1-1h0l-2 1c-2 0-2-1-3-2-1-2-4-4-5-6v-1l-1-2h1v-1-1c0-1 1-3 2-4l1 4h1l3-6z" class="X"></path><path d="M485 835l1 1c-1 2-4 7-3 10 0 1 0 1 1 2 2 1 4 2 6 1l3-1c0 2 0 2-1 3-2 0-3 0-4 1-2-1-2 0-3 0h-2c-1-2-4-4-5-6v-1l-1-2h1v-1-1c0-1 1-3 2-4l1 4h1l3-6z" class="O"></path><path d="M499 837h1 2l1-2c1 1 1 2 2 3v13c1 3 0 5 1 8 0 2 0 3 1 4-1 1-1 2-1 3 0 2 0 3-1 4h-1c0-1 0 0 1-1v-1l-1-2-1 1c-3-2-5-5-7-7l-1-1h-1l-2-2v-1c1 0 1-1 2-3 1-1 2-2 3-4 0-2 0-4 1-6l1-6z" class="o"></path><path d="M497 849l1 1v1c-1 1-1 3-1 4l-1 1h-2c-1 0-1 0-2 1v-1c1 0 1-1 2-3 1-1 2-2 3-4z" class="S"></path><path d="M499 837h1 2l1-2c1 1 1 2 2 3v13c1 3 0 5 1 8 0 2 0 3 1 4-1 1-1 2-1 3 0 2 0 3-1 4h-1c0-1 0 0 1-1v-1l-1-2c-1-2-1-4 0-6v-2c-2-4-1-10-1-14 0-2 0-4-1-5h-2c0 2 0 4-1 6 0 2 0 3-1 5h0l-1-1c0-2 0-4 1-6 0 0 1-5 1-6z" class="Z"></path><path d="M496 815c2 1 3 2 4 3l1-2 8 2c-1 1-1 2-1 3-1 2-2 3-3 4s-1 2-2 3c0 1-1 2-2 3l-2 3h2c-1 1-1 2-2 3 0 1-1 6-1 6h-1l-1 1c-1 2-2 3-3 4l-3 1c-2 1-4 0-6-1-1-1-1-1-1-2-1-3 2-8 3-10l-1-1 2-3 7-7 2-3v-1h0l1-2c0-1-1-3-1-4z" class="f"></path><path d="M493 828h4 0c-3 4-7 7-10 11-1 2-2 5-4 7h0c-1-3 2-8 3-10l7-8z" class="C"></path><path d="M499 834h2c-1 1-1 2-2 3 0 1-1 6-1 6h-1l-1 1c-1 2-2 3-3 4l-3 1c-2 1-4 0-6-1-1-1-1-1-1-2h0 2c4-3 9-8 14-12z" class="l"></path><path d="M484 848v-1h4s1-1 1-2c2 0 6-1 7-1-1 2-2 3-3 4l-3 1c-2 1-4 0-6-1z" class="R"></path><path d="M496 815c2 1 3 2 4 3l1-2 8 2c-1 1-1 2-1 3-1 2-2 3-3 4s-1 2-2 3c0 1-1 2-2 3h-2 0-1l1-2c1-1 1-1 1-2l-3 1h0-4l-7 8-1-1 2-3 7-7 2-3v-1h0l1-2c0-1-1-3-1-4z" class="X"></path><path d="M502 820h1l1 1c-1 2-3 4-4 6l-3 1h0-4c3-2 6-5 9-8z" class="J"></path><path d="M501 816l8 2c-1 1-1 2-1 3-1 2-2 3-3 4s-1 2-2 3c0 1-1 2-2 3h-2 0-1l1-2c1-1 1-1 1-2 1-2 3-4 4-6l-1-1h-1-2v-2l1-2z" class="U"></path><defs><linearGradient id="V" x1="652.097" y1="327.505" x2="735.922" y2="395.904" xlink:href="#B"><stop offset="0" stop-color="#010201"></stop><stop offset="1" stop-color="#353533"></stop></linearGradient></defs><path fill="url(#V)" d="M673 300h0c2 7 6 15 7 22l-3 1c2 1 3 1 4 2 1 0 1 1 2 2 3 4 5 9 7 14l13 23 12 19 7 10c2 1 3 3 4 5h0c2 1 3 2 4 3v1-1l-1-1v-2c2 2 3 3 4 5 0 2 3 5 4 7l1 1 2 2 2 4 2 2c2 2 4 5 6 7h0c1 1 1 2 2 3l3 6-2-1v1l-1 1 1 3 2 7c1 1 1 3 2 4-1 0 0 0-1 1h0-1-1-2c-1-1-3-2-4-3h0-1c-1 0-1 0-2-1l-1 2h0c0 2 1 4 1 6l2 12v21l-3 13c0 1-1 1-1 2l-2 3c0 2 0 3-1 4 0 1-1 2-2 3 0 2-1 3-2 5h0 0c-3 3-3 6-7 6h-1c1 1 1 2 1 3l3 3-4 5c-1 1-2 2-2 3l-2 1-4 5-5 4-1 1c-3 2-6 4-8 5l-6 3c-3 1-7 3-11 4-1 0-3 1-5 1l-8 1h-8-4c-4 0-9-1-13-2v-1c-1-1-1-1-1-2h-2c-1-1-1-1-1-2 2-1 5-2 7-3l1-1c4 0 7-1 10-2 0 0 0-1 1-1h0c0-1-1-1-1-2h3l2-1c3-1 7-1 11-1v-4c2-1 5-2 7-4h1c7-6 10-12 12-21 1-1 1-3 1-5l1 1c0 1 1 2 2 2l1-1 1-1h-2v-1c3 0 6-2 9-3 2-1 3-3 5-5l2-5-1-1c1-1 1-2 2-3 5-15 5-31 1-47s-9-30-22-42l-2-1c0-2-1-2-2-3l1-1 7 7c1 1 2 3 4 3l1-1c-4-6-10-12-17-14l-1-2c2 0 3 0 4 1h1v-1c-1 0-1-1-2-1v-1-1c-3 0-4-2-6-2l-3-1c-3-1-7-2-10-4l1-2c-1 0-2-1-4-1l-2-1-5-1-7-3c-3-1-5-3-8-5l-6-4c0-4 4-11 6-15l12-27 6-15c1-3 3-6 4-9z"></path><path d="M726 398h0l8 11h0c-1-1-2-1-4-1h0c-2-2-5-4-6-7v-1l1 1c0 1 3 4 4 4 0-1 0-2-1-3l-1-1c-1-1 0-1-1-2h-1l1-1z" class="b"></path><path d="M652 369c-1-3 1-6 2-9 1 2 1 4 2 6l2 5c-2 0-2-1-4-2 1 1 1 1 1 2-1 0-2-1-3-2z" class="G"></path><path d="M656 366l4 2 1-2v1 1 1c1-3 0-4 0-7 0-1 1-2 2-2-1 4-2 7 0 11l1 1-2 2c-1-1-3-2-4-3l-2-5z" class="U"></path><path d="M652 369c1 1 2 2 3 2 0-1 0-1-1-2 2 1 2 2 4 2 1 1 3 2 4 3l1 1 2-1 1 1v1 2l-7-3c-3-1-5-3-8-5l1-1z" class="F"></path><path d="M678 337l1-1-1-1c-2 0-3-1-4-1l-1-1h3c2 1 4 2 7 3l-1-1h0c-3-1-4-3-7-3-1 0-1 0-2-1l-4-2c5 1 10 2 14 4 1 1 0 2 0 3h1c2 3 3 4 4 7 2 2 3 6 4 8-2-2-2-4-3-6s-3-4-5-5-3-1-5-3h-1z" class="f"></path><path d="M676 346c-2-1-4-2-5-3h-1c-1 0-1 0-2-1h1c4 1 7 2 11 4-2-2-4-3-7-4-1-1-3 0-4-2 0 0 1 1 2 1 2 0 4 0 6 1h1 0l-1-1c-1 0-3-1-4-2-2 0-3 0-4-1h4l2 1 2-1c1 2 4 3 6 3-2-1-3-1-5-3-1-1-2-1-4-2h0c2-1 3 0 4 1h1c2 2 3 2 5 3s4 3 5 5 1 4 3 6l1 3c0 1 2 3 2 4 1 1 1 2 2 3l-1 1h1v1h-5c1 1 2 1 3 1s1 1 2 1c0 1 1 1 1 1 2 1 3 2 4 4l1 2v2 1l-1 1c0-2-2-4-3-5-2-1-3-2-4-3l-4-3-1-1-3-3-5-4h0 2 0l-3-3c1 0 2 1 3 1v-1c-2-1-4-2-5-4-1 0-1-1-2-2h-1 0c1-1 2 0 3 0h0c-1-1-2-2-3-2z" class="n"></path><path d="M669 357c1 0 2-1 2-2s-1 0-2-2h0l2-1-1-1v-1h2 0c-1-1-2-1-3-2 1-1 2-1 3-1-2-2-3-1-5-2h0l9 1c1 0 2 1 3 2h0c-1 0-2-1-3 0h0 1c1 1 1 2 2 2 1 2 3 3 5 4v1c-1 0-2-1-3-1l3 3h0-2 0l5 4 3 3 1 1 4 3c1 1 2 2 4 3 1 1 3 3 3 5l1-1v-1l17 22-1 1-6-9c-2 0-3-2-4-3l-1 1c-3-5-7-9-11-13l-10-9c-1-1-2-3-4-4h-1c-1 2 0 2 0 4s0 5-1 6-1 2-2 2c-1 1-1 1-2 1l-1-1c-3 0-4 0-6-2 1-1 1-1 1-2v-1h-1c1-2 2-3 3-4h-1-1s1-1 0-2c0-2-1-3-2-4z" class="O"></path><path d="M709 385l-2-3c-3-3-6-7-8-9-3-3-7-5-10-8l1-1 1 1 4 3c1 1 2 2 4 3 1 1 3 3 3 5l1-1v-1l17 22-1 1-6-9c-2 0-3-2-4-3z" class="P"></path><path d="M669 357c1 0 2-1 2-2s-1 0-2-2h0l2-1-1-1v-1h2 0c-1-1-2-1-3-2 1-1 2-1 3-1-2-2-3-1-5-2h0l9 1c1 0 2 1 3 2h0c-1 0-2-1-3 0h0 1c1 1 1 2 2 2-2 0-4-1-6-1l1 1h0c2 0 3 1 4 2h-2c0 1 1 1 2 2-1 0-2 0-3-1 0 2 1 2 2 3h0c-1 0-2-1-3-2l-1 1 4 3h0l-2-1v1 1h-2v1l1 1c1 0 2 1 3 2v1h1v3 1l-1 1c1 0 0 0 1 1l1 2c-1 1-1 1-2 1l-1-1c-3 0-4 0-6-2 1-1 1-1 1-2v-1h-1c1-2 2-3 3-4h-1-1s1-1 0-2c0-2-1-3-2-4z" class="b"></path><path d="M709 385c1 1 2 3 4 3l6 9 1-1c1 2 2 4 4 5 1 3 4 5 6 7h0c2 0 3 0 4 1h0l-8-11c2 1 3 2 4 3v1-1l-1-1v-2c2 2 3 3 4 5 0 2 3 5 4 7l1 1 2 2 2 4 2 2c2 2 4 5 6 7h0c1 1 1 2 2 3l3 6-2-1v1l-1 1 1 3 2 7c1 1 1 3 2 4-1 0 0 0-1 1h0-1-1-2c-1-1-3-2-4-3h0-1c-1 0-1 0-2-1l-1 2h0c-2-3-3-6-4-10 0-3-3-4-3-7h1l1 1h0c-1-2-1-2-2-2l-2 1c-1 0 0 0-1 1v3c0 1 1 1 1 2h0l-1 1v3 1c-1-2-1-4-2-6-2-10-5-19-9-27l-3-6c-3-7-7-13-12-18l1-1z" class="N"></path><path d="M726 409c1 0 1 0 2 1 0 1 1 2 2 3h0c1-1 1-1 3-1l2 2 1 1v1h0c1 1 1 1 1 2-1 1-1 1-3 1l-1-1-1 1 1 1c1 1 1 2 2 3v1 1 1c1 1 1 2 1 3 0 0-1 1-2 1 0-1-2-4-2-5-2-6-4-11-6-16z" class="C"></path><path d="M745 439c-2-1-3-2-4-4 1-1 0-1 1-2-1-2-2-2-3-3v-1l1-1v-2c1 0 2-2 3-2l5 8c0 1 2 2 2 3v2c-2 0-4 1-5 2z" class="B"></path><path d="M729 398c2 2 3 3 4 5 0 2 3 5 4 7l1 1 2 2 2 4 2 2c2 2 4 5 6 7h0c1 1 1 2 2 3l3 6-2-1v1l-1 1-1-1c-1-3-5-8-6-11 0-1 0-2-1-3h-2l-3 3c0 1 0 1-1 1v-1l4-4 1 1v-1l-1-1h-3v-1h2v-1c-2-3-4-6-7-8l-8-11c2 1 3 2 4 3v1-1l-1-1v-2zm16 41c1-1 3-2 5-2v1h1l2 1 2 7c1 1 1 3 2 4-1 0 0 0-1 1h0-1-1-2c-1-1-3-2-4-3h0-1c-1 0-1 0-2-1l-1 2h0c-2-3-3-6-4-10h0 1l1 1 1-1 1 1 1-1z" class="E"></path><defs><linearGradient id="W" x1="723.971" y1="414.868" x2="727.031" y2="413.572" xlink:href="#B"><stop offset="0" stop-color="#31312c"></stop><stop offset="1" stop-color="#545350"></stop></linearGradient></defs><path fill="url(#W)" d="M709 385c1 1 2 3 4 3l6 9 7 12c2 5 4 10 6 16 0 1 2 4 2 5l1 2c-1 0 0 0-1 1v3c0 1 1 1 1 2h0l-1 1v3 1c-1-2-1-4-2-6-2-10-5-19-9-27l-3-6c-3-7-7-13-12-18l1-1z"></path><path d="M663 360c2-2 3-3 6-3 1 1 2 2 2 4 1 1 0 2 0 2h1 1c-1 1-2 2-3 4h1v1c0 1 0 1-1 2 2 2 3 2 6 2l1 1c1 0 1 0 2-1 1 0 1-1 2-2s1-4 1-6-1-2 0-4h1c2 1 3 3 4 4l10 9c4 4 8 8 11 13 5 5 9 11 12 18l3 6-1 1c0 1 0 1 1 2 0 0 0 1 1 2 0 1 0 3-1 4-2-4-3-8-5-11l-3-6c-1 0-1-1-2-2l-1-2-2-2c-2 0-4-3-5-4-3-3-7-7-10-9 0 3 4 4 5 7h-1c-2-1-2-1-4-1-2-1-6-3-7-4h-1l2 2v1h0l-3-1c-3-1-7-2-10-4l1-2c-1 0-2-1-4-1l-2-1-5-1v-2-1l-1-1-2 1-1-1 2-2-1-1c-2-4-1-7 0-11z" class="C"></path><path d="M670 370l-4-2c-2-2-1-3-2-4 1-2 1-2 3-3-1 1-1 2-1 3h1c1-1 1-1 2-1v1l2-1h1 1c-1 1-2 2-3 4h1v1c0 1 0 1-1 2z" class="V"></path><path d="M664 372c2 3 5 4 9 6h0c1-1 2-2 4-2 0 0 1 0 2 1v-1h4l1 1c2 0 5 2 7 3l1 1c1 0 2 0 3 2 0 3 4 4 5 7h-1c-2-1-2-1-4-1-2-1-6-3-7-4h-1l2 2v1h0l-3-1c-3-1-7-2-10-4l1-2c-1 0-2-1-4-1l-2-1-5-1v-2-1l-1-1-2 1-1-1 2-2z" class="s"></path><path d="M664 372c2 3 5 4 9 6h0c4 2 8 3 12 5 0 1 2 1 3 2h-1l2 2v1h0l-3-1c-3-1-7-2-10-4l1-2c-1 0-2-1-4-1l-2-1-5-1v-2-1l-1-1-2 1-1-1 2-2z" class="e"></path><path d="M677 381c2 1 3 1 5 2 1 1 3 2 5 3-1 0-1 0-1 1-3-1-7-2-10-4l1-2z" class="M"></path><path d="M687 364l10 9c4 4 8 8 11 13 5 5 9 11 12 18l3 6-1 1c0 1 0 1 1 2 0 0 0 1 1 2 0 1 0 3-1 4-2-4-3-8-5-11l-3-6c-1 0-1-1-2-2l-1-2-2-2c-1-2-4-5-6-7v-1c-5-7-12-12-17-19v-3-2z" class="o"></path><path d="M695 383c3 2 7 6 10 9 1 1 3 4 5 4l2 2 1 2c1 1 1 2 2 2l3 6c2 3 3 7 5 11 1-1 1-3 1-4-1-1-1-2-1-2-1-1-1-1-1-2l1-1c4 8 7 17 9 27 1 2 1 4 2 6l2 9v14l-2 10c-1 7-4 14-8 20-1 0-2 1-3 1h-1l-1 1-1-1c1-1 1-2 2-3 5-15 5-31 1-47s-9-30-22-42l-2-1c0-2-1-2-2-3l1-1 7 7c1 1 2 3 4 3l1-1c-4-6-10-12-17-14l-1-2c2 0 3 0 4 1h1v-1c-1 0-1-1-2-1v-1-1c-3 0-4-2-6-2h0v-1l-2-2h1c1 1 5 3 7 4 2 0 2 0 4 1h1c-1-3-5-4-5-7z" class="Q"></path><path d="M695 389c2 0 2 0 4 1v1c1 0 2 1 2 1l1 1h2c5 2 7 9 10 13v1l4 6 2 4v1h0c-7-12-15-21-25-29zm4 15c0-2-1-2-2-3l1-1 7 7c1 1 2 3 4 3l1-1c11 14 15 31 17 49 0 7 0 14-1 21v4c-1 2-2 10-4 11 5-15 5-31 1-47s-9-30-22-42l-2-1z" class="B"></path><path d="M695 383c3 2 7 6 10 9 1 1 3 4 5 4l2 2 1 2c1 1 1 2 2 2l3 6c2 3 3 7 5 11 1-1 1-3 1-4-1-1-1-2-1-2-1-1-1-1-1-2l1-1c4 8 7 17 9 27 1 2 1 4 2 6l2 9v14l-2 10c-1 7-4 14-8 20-1 0-2 1-3 1 1-5 3-9 4-14 5-22 1-45-7-65v-1l-2-4-4-6v-1c-3-4-5-11-10-13h-2l-1-1s-1-1-2-1v-1h1c-1-3-5-4-5-7z" class="Z"></path><path d="M732 437c1 2 1 4 2 6l2 9v14l-2 10-1-1c0-2 1-3 1-4v-6c0-7 1-16-3-23 0-2 1-4 1-5z" class="H"></path><path d="M735 432l2-1c1 0 1 0 2 2h0l-1-1h-1c0 3 3 4 3 7 1 4 2 7 4 10 0 2 1 4 1 6l2 12v21l-3 13c0 1-1 1-1 2l-2 3c0 2 0 3-1 4 0 1-1 2-2 3 0 2-1 3-2 5h0 0c-3 3-3 6-7 6l1-1v-3c1-1 2-2 2-4 1-1 1-1 1-2 1-1 1-2 1-2v-1h-2-1 0l-2-1-1 1-1-1-2 1-1 1c-2 1-3 2-4 4-1 0-1 0-2 1h-1l-1 3h-1c0-2 0-3-1-4l-1 1h0v1l-1 1c-1 0-1 1-2 2s-3 0-4 1c0 0-1 0-1 1h-1v-1c1-1 1-2 2-2-1-1-1-1-1-2-1-2-2-4-2-6 0 1 1 2 2 2l1-1 1-1h-2v-1c3 0 6-2 9-3 2-1 3-3 5-5l2-5 1-1h1c1 0 2-1 3-1 4-6 7-13 8-20l2-10v-14l-2-9v-1-3l1-1h0c0-1-1-1-1-2v-3c1-1 0-1 1-1z" class="L"></path><path d="M735 479l2-8c1-4 1-8 1-12 0-2-1-5 0-7h1v8c1 2 1 4 1 6-1 4-2 10-4 13h-1z" class="G"></path><path d="M735 479h1l-1 4-3 7c-3 5-6 10-11 14l1-7h1c1 0 2-1 3-1h1c3-5 6-12 8-17z" class="B"></path><path d="M745 455l2 12v21h-2c-2-2-1-3-1-6 0-4 2-9 1-13-1-1-1-3-1-4 0-2-1-2-1-4 1-1 1-2 1-3l1-1v-2z" class="Q"></path><path d="M735 432l2-1c1 0 1 0 2 2h0l-1-1h-1c0 3 3 4 3 7 1 4 2 7 4 10 0 2 1 4 1 6v2l-1 1c0 1 0 2-1 3v-3h-1l2-2h-1-1c-1-2-1-2-1-3v-3c-1-1-2-2-2-4h0l-2 2v3l-1 1-2-9v-1-3l1-1h0c0-1-1-1-1-2v-3c1-1 0-1 1-1z" class="K"></path><path d="M734 442h1l1-1c0-1 1-1 1-3l1 1c0 1 0 2-1 3 1 1 2 1 2 3v1l-2 2v3l-1 1-2-9v-1z" class="r"></path><path d="M740 466l2-3 1 1c0 4 0 9-2 12l2 1c0 2 0 3-1 5v1l-1 2c-1 3-3 6-4 9-1-1-2-1-2-2l-1-1v1l-2-2 3-7 1-4c2-3 3-9 4-13z" class="t"></path><path d="M739 482h1v-1l-1-1h0 1 1v-3-1l2 1c0 2 0 3-1 5v1l-1 2v-1l-2 1h0 0v-1-2z" class="O"></path><path d="M735 483l3 1h0v-2h1v2 1h0 0l2-1v1c-1 3-3 6-4 9-1-1-2-1-2-2l-1-1v1l-2-2 3-7z" class="V"></path><path d="M742 483c1 2 1 5 0 7v6 2c-1 3-2 5-1 8 0 2 0 3-1 4 0 1-1 2-2 3 0 2-1 3-2 5h0 0c-3 3-3 6-7 6l1-1v-3c1-1 2-2 2-4 1-1 1-1 1-2 1-1 1-2 1-2v-1h-2-1 0l1-2v-1l1-1h0v-1-1c-1-1-1-1-2-1l-1-1 3-3h-2l5-6h1c1-3 3-6 4-9l1-2z" class="d"></path><path d="M731 511l1-2v-1l1-1c1 2 1 2 3 3l-1 1h0l2 1c-1 2-4 3-4 5v2c0 1-3 3-3 4v-3c1-1 2-2 2-4 1-1 1-1 1-2 1-1 1-2 1-2v-1h-2-1 0z" class="I"></path><path d="M733 500h0c1 0 1-1 2-1 1-2 3-4 4-5-1 2-2 6-4 7h-1v1 1c2 1 3 2 3 4s-1 2-1 3c-2-1-2-1-3-3h0v-1-1c-1-1-1-1-2-1l-1-1 3-3z" class="E"></path><path d="M721 504c5-4 8-9 11-14l2 2v-1l1 1c0 1 1 1 2 2h-1l-5 6h2l-3 3 1 1c1 0 1 0 2 1v1 1h0l-1 1v1l-1 2-2-1-1 1-1-1-2 1-1 1c-2 1-3 2-4 4-1 0-1 0-2 1h-1l-1 3h-1c0-2 0-3-1-4l-1 1h0v1l-1 1c-1 0-1 1-2 2s-3 0-4 1c0 0-1 0-1 1h-1v-1c1-1 1-2 2-2-1-1-1-1-1-2-1-2-2-4-2-6 0 1 1 2 2 2l1-1 1-1h-2v-1c3 0 6-2 9-3 2-1 3-3 5-5l2-5 1-1-1 7z" class="C"></path><path d="M723 510l3-3s1-1 2-1c1-1 3 0 5 1h0l-1 1v1l-1 2-2-1-1 1-1-1-2 1-1 1-1-2z" class="L"></path><path d="M721 504c5-4 8-9 11-14l2 2v-1l1 1c0 1 1 1 2 2h-1l-5 6c-4 3-11 8-16 10l-3 1h-1 0l5-3c2 0 5-2 5-4z" class="Q"></path><path d="M734 492v-1l1 1c0 1 1 1 2 2h-1l-3 1c-1 1-1 2-3 3 0-2 2-5 4-6z" class="K"></path><path d="M716 513c2-1 5-2 7-3l1 2c-2 1-3 2-4 4-1 0-1 0-2 1h-1l-1 3h-1c0-2 0-3-1-4l-1 1h0v1l-1 1c-1 0-1 1-2 2s-3 0-4 1c0 0-1 0-1 1h-1v-1c1-1 1-2 2-2-1-1-1-1-1-2-1-2-2-4-2-6 0 1 1 2 2 2s2 0 2 1h0l-1 1-1 1h1c4 0 7-2 10-4z" class="Q"></path><path d="M716 513c2-1 5-2 7-3l1 2c-2 1-3 2-4 4-1 0-1 0-2 1h-1l-1-1c1-2 2-1 3-1l-3-2z" class="O"></path><path d="M725 511l2-1 1 1 1-1 2 1h0 1 2v1s0 1-1 2c0 1 0 1-1 2 0 2-1 3-2 4v3l-1 1h-1c1 1 1 2 1 3l3 3-4 5c-1 1-2 2-2 3l-2 1-4 5-5 4-1 1-8 5-6 3c-3 1-7 3-11 4-1 0-3 1-5 1l-8 1h-8-4c-4 0-9-1-13-2v-1c-1-1-1-1-1-2h-2c-1-1-1-1-1-2 2-1 5-2 7-3l1-1c4 0 7-1 10-2 0 0 0-1 1-1h0c0-1-1-1-1-2h3l2-1c3-1 7-1 11-1v-4c2-1 5-2 7-4h1c7-6 10-12 12-21 1-1 1-3 1-5l1 1c0 2 1 4 2 6 0 1 0 1 1 2-1 0-1 1-2 2v1h1c0-1 1-1 1-1 1-1 3 0 4-1s1-2 2-2l1-1v-1h0l1-1c1 1 1 2 1 4h1l1-3h1c1-1 1-1 2-1 1-2 2-3 4-4l1-1z" class="X"></path><path d="M670 556c4-1 8-2 12-2 1 1 0 1 0 2v1l-1-1c-2 0-4 0-7 1h-1c-1-1-1-1-3-1z" class="f"></path><path d="M694 545h2c1-1 2-2 3-4l1 1h2c1 1 1 2 1 3-1 1-3 2-4 2-2 1-3 1-4 0l-1-1v-1z" class="d"></path><path d="M703 533h1v1c1 1 2 3 3 4h-1-2s0 1-1 1c-1 2-1 2-4 1v-3c1-2 2-3 4-4z" class="H"></path><path d="M698 537h1v3l-1 1h1c-1 2-2 3-3 4h-2v-1c-1 0-2 0-3 1l1-3-2-1h1c1-1 3-3 5-3l1 1h1v-2z" class="P"></path><path d="M691 541c1 0 1 1 2 1 0 1 1 1 2 1 1-1 1-2 2-2h1 1c-1 2-2 3-3 4h-2v-1c-1 0-2 0-3 1l1-3-2-1h1z" class="M"></path><path d="M691 545c1-1 2-1 3-1v1 1c-1 2-1 2-3 3h-1v-1l-1-1h0c-2 1-3 0-4 1-3 0-5 1-7 2h-4v-1h-3c7-2 14-2 20-4z" class="L"></path><path d="M681 545l9-4 2 1-1 3c-6 2-13 2-20 4-2 1-4 1-6 1 0 0 0-1 1-1h0c0-1-1-1-1-2h3l2-1c3-1 7-1 11-1z" class="W"></path><path d="M708 546c1 1 1 0 3 0 0 1-1 1-2 2-2 1-3 1-4 3l1 3-6 3c-3 1-7 3-11 4-1 0-3 1-5 1 0-1 0-1-1-1h-2c1-1 1-1 2-1v-1h-2l1-1c2 1 2 0 4-1 0-3 2-5 5-7 1-1 2 1 4 1l1-1c2 0 3 0 5-1s4-2 7-3z" class="q"></path><path d="M692 552h1c1 1 2 1 3 1 0 1 0 1-1 1l-3 3h-2c1-2 1-3 2-5z" class="u"></path><path d="M692 557c1 0 1 0 2-1h3l2-1h1c-2 2-4 2-6 3-4 1-5 2-9 2 0-1 1-1 1-2l4-1h2z" class="L"></path><path d="M691 550l1 2c-1 2-1 3-2 5l-4 1v-1c0-3 2-5 5-7z" class="U"></path><path d="M701 516c1-1 1-3 1-5l1 1c0 2 1 4 2 6 0 1 0 1 1 2-1 0-1 1-2 2v1c-1 1-2 3-2 5 0 0 0 1 1 1l1 3-1 1c-2 1-3 2-4 4h-1v2h-1l-1-1c-2 0-4 2-5 3h-1l-9 4v-4c2-1 5-2 7-4h1c7-6 10-12 12-21z" class="u"></path><path d="M694 535l1-1c1-1 2-3 3-4l1-1 1 4c-2 1-3 2-4 3h-1l-1-1z" class="U"></path><path d="M694 535l1 1h1c1 0 1 1 2 1v2h-1l-1-1c-2 0-4 2-5 3h-1l-9 4v-4c2-1 5-2 7-4h1c3 0 2 0 5-2h0z" class="f"></path><path d="M671 549h3v1c0 1-2 2-2 1h-2c-1 0-1 1-2 2h-1v2l1 1h2c2 0 2 0 3 1h1c3-1 5-1 7-1l1 1v1l-1 1h2v1c-1 0-1 0-2 1h2c1 0 1 0 1 1l-8 1h-8-4c-4 0-9-1-13-2v-1c-1-1-1-1-1-2h-2c-1-1-1-1-1-2 2-1 5-2 7-3l1-1c4 0 7-1 10-2 2 0 4 0 6-1z" class="p"></path><path d="M668 556h2c2 0 2 0 3 1h1 0v1h-5-1 0v-2z" class="U"></path><path d="M681 559h2v1c-1 0-1 0-2 1h2c1 0 1 0 1 1l-8 1h-8-4c2-1 6 0 8-1 3-2 7-1 9-3z" class="b"></path><path d="M671 549h3v1c0 1-2 2-2 1h-2c-1 0-1 1-2 2h-1v-1h-2c-1 1-2 1-3 1h-2 0c-2 1-3 0-5-1 4 0 7-1 10-2 2 0 4 0 6-1z" class="r"></path><path d="M725 511l2-1 1 1 1-1 2 1h0 1 2v1s0 1-1 2c0 1 0 1-1 2 0 2-1 3-2 4v3l-1 1h-1c1 1 1 2 1 3l3 3-4 5c-1 1-2 2-2 3l-2 1-4 5-5 4-1 1-8 5-1-3c1-2 2-2 4-3 1-1 2-1 2-2-2 0-2 1-3 0-3 1-5 2-7 3 0-1 0-2 1-2 0-1 2-1 3-2s1-3 2-4c1-2 3-3 4-4-1 0-3 0-4 1-1-1-2-3-3-4v-1h-1l1-1-1-3c-1 0-1-1-1-1 0-2 1-4 2-5h1c0-1 1-1 1-1 1-1 3 0 4-1s1-2 2-2l1-1v-1h0l1-1c1 1 1 2 1 4h1l1-3h1c1-1 1-1 2-1 1-2 2-3 4-4l1-1z" class="O"></path><path d="M704 533c1 0 2 0 3 1h2c1 0 3 1 4 2h0l-2 1c-1 0-3 0-4 1-1-1-2-3-3-4v-1z" class="I"></path><path d="M717 532c1-1 2-3 2-4l3-3v2c1-1 2-2 2-3h3-1c-3 4-6 8-10 11v-1h0c0-1 1-1 1-2h0z" class="j"></path><path d="M713 518h1l-1 1c0 1-1 1-1 3l-2-1v1c0 1 0 1-1 2v1l2-1c0 2-2 4-4 6-2 0-2 0-4-1-1 0-1-1-1-1 0-2 1-4 2-5h1c0-1 1-1 1-1 1-1 3 0 4-1s1-2 2-2l1-1z" class="H"></path><path d="M727 524h1c1 1 1 2 1 3l3 3-4 5c-1 1-2 2-2 3l-2 1-4 5-5 4-1 1-8 5-1-3c1-2 2-2 4-3 1-1 2-1 2-2-2 0-2 1-3 0l6-5 1-2 6-6 6-7-1-2h1z" class="t"></path><path d="M728 535c0-1-1-2-1-2l-3 1-1-1c1-1 1-2 2-3 2 0 3-1 4-3l3 3-4 5z" class="K"></path><defs><linearGradient id="X" x1="720.587" y1="545.397" x2="704.913" y2="547.103" xlink:href="#B"><stop offset="0" stop-color="#232423"></stop><stop offset="1" stop-color="#424240"></stop></linearGradient></defs><path fill="url(#X)" d="M717 538l2-1c1 2 1 2 2 3-1 4-5 4-6 8l-1 1-8 5-1-3c1-2 2-2 4-3 1-1 2-1 2-2-2 0-2 1-3 0l6-5 2 1v-1h1l-1-1c0-1 1-1 1-2z"></path><path d="M717 538l2 1v1c-1 0-2 1-2 1l-1-1c0-1 1-1 1-2z" class="f"></path><path d="M725 511l2-1 1 1 1-1 2 1h0 1 2v1s0 1-1 2c0 1 0 1-1 2 0 2-1 3-2 4v3l-1 1h-1-1-3c0 1-1 2-2 3v-2l-3 3c0 1-1 3-2 4-1 0-3 2-3 3-2-1-5-2-5-3-1-3 2-6 3-8s1-3 3-4h1l1-3h1c1-1 1-1 2-1 1-2 2-3 4-4l1-1z" class="R"></path><path d="M725 511l2-1 1 1 1-1 2 1h0c-2 6-4 9-8 13 0-2 0-4 1-6l1-2c1-1 0-1 0-2v-1l1-1-1-1z" class="h"></path><defs><linearGradient id="Y" x1="663.074" y1="666.85" x2="603.529" y2="658.5" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#282828"></stop></linearGradient></defs><path fill="url(#Y)" d="M584 562v1c2 0 3 1 4 2h0l1-1 1-1c4 4 9 6 14 10 2 2 5 4 7 5 2 2 5 3 7 4l2-1c2 1 4 1 5 1 6 2 11 4 17 5h4 4c1 0 4 0 5 1 2 1 10 2 13 1l1-1h2c1 0 1 0 2-1 2 0 4-1 5-2v2h-2v1h2v1s-1 0-1 1l-1 2h-1l-42 100-15 35c-2 4-4 8-5 12h-1l-15-35-4-11-18-41h0l-8-19c-1-1-2-4-1-5 0-3 4-5 5-8h1v-3-1-2l1-1 1-1v-3h3 0c1-1 1-1 2-1h1l2-1c0-1 0-1-1-2h2l-2-2 1-1c0 1 0 1 1 1v-1c-1-1-2-1-3-2v-2c1-1 1-1 3-1h4 1l-1-1-1-1h1c1-1 0-1 0-2h3l1-1h1l-1-1c1-1 2-2 3-2h1v-1h2 2l1-1h2l-3-3-4-2c-2-2-4-3-6-5h0c-3-4-4-10-5-15z"></path><path d="M631 651h1v2l1 1h-2c-1-1-2-1-2-2 1 0 1 0 2-1h0zm10-26v-2h3v-1-1h3l-2 1v1l1-1 1 1c1 0 1 0 2-1v1c-1 0-5 0-5 1v2 2l-3-3z" class="U"></path><path d="M629 645l2 1-2 3 2 2h0c-1 1-1 1-2 1 0 1 1 1 2 2-1 0-1 0-1 1-1 0-2-1-3-1l-1-1 2-1-2-1 2-2c-1-1-1-1-1-2l2-2z" class="f"></path><path d="M638 608c1 3 2 5 5 6l1 1-1 1c0 1 0 1 1 2 2 0 2 1 3 2v1h-3v1 1h-3v2c-2-1-4-2-7-2v-2h0c2-2 3-1 5-1v-1h-1l1-1-1-1v-1l1-1-2-1v-3-1h0l1-2z" class="m"></path><path d="M673 587c2 0 4-1 5-2v2h-2v1h2v1s-1 0-1 1l-1 2h-1c-2-2-10-1-14-1-3 0-6 0-9-1-1 1-2 1-3 2l-3 1v-1-1l-2-2h2v-2h4c1 0 4 0 5 1 2 1 10 2 13 1l1-1h2c1 0 1 0 2-1z" class="F"></path><path d="M646 589c2 0 4 0 6 1-1 1-2 1-3 2l-3 1v-1-1l-2-2h2z" class="f"></path><path d="M593 693c0-1 0-2 2-3 1 1 0 1 1 2h2c1 2 3 2 5 3l9 6-1 1c-1-1-1-1-2-1v3h-4-1-3 0l-2-2-1-1-1 3-4-11z" class="b"></path><path d="M649 592c3 2 7 4 9 7h0c1 2 1 4 1 7-1 3-3 6-5 7-3 2-7 2-10 2l-1-1c-3-1-4-3-5-6l-1-1c0-6 1-8 5-12 1-1 3-2 4-2l3-1z" class="N"></path><path d="M642 607v-1c-1-2-1-5 1-6h0l1 1v-1h2c2 2 5 1 8 1 0 2 1 5-1 7-1 1-2 2-4 2-3 0-5-1-7-3h0z" class="p"></path><path d="M642 607l-2-1c0-3-1-5 1-8 0 0 1-1 1-2 3-2 5-2 9-2 2 1 4 3 5 5l1 1 1-1c1 2 1 4 1 7-1 3-3 6-5 7-3 2-7 2-10 2l-1-1 1-1h2c3 0 4 0 6-2 3-2 3-4 5-6v-1c0-1-1-1-1-2h-1l1-1h0c0-2-1-3-2-4l-1 1c1 1 1 1 1 2l-1-1v1l-1-1-1 1c-1 0-2 0-2-1-2 0-2 0-3-1 0 1-1 1 0 2h-2v1l-1-1h0c-2 1-2 4-1 6v1z" class="a"></path><path d="M646 598c1-1 1-2 4-2l3 3v1l-1-1-1 1c-1 0-2 0-2-1-2 0-2 0-3-1z" class="o"></path><path d="M634 623c3 0 5 1 7 2l3 3c2 3 3 7 2 10s-3 6-6 7c-3 2-6 2-9 1l-2-1c-1-1-2-2-3-2-2-2-3-4-3-6 0-4 1-7 4-10 2-2 4-4 7-4z" class="I"></path><path d="M634 637c1 1 2 1 3 1 1 1 2 1 4 1-3 2-4 3-7 3h-1c0-2 1-3 1-5z" class="f"></path><path d="M627 633l1-1-1-1c2-2 2-2 4-3h1v3h-3v5c1 1 2 1 3 2l1-1h1c0 2-1 3-1 5-2-1-4-2-5-4-1-1-1-3-1-5z" class="t"></path><path d="M627 633c-1 0-1 0-2-1l1-1v-1c0-2 1-3 2-3 1-1 3-2 4-2 2 1 3 1 5 0l1 1c0 1 1 2 2 2 1 1 1 1 2 3h0c-2-1-4-2-5-3s-1-1-2-1l-2 2h1c0 1 0 1 1 2h0-3v-3h-1c-2 1-2 1-4 3l1 1-1 1z" class="d"></path><path d="M634 637h-1l-1 1c-1-1-2-1-3-2v-5h3 3c3 1 4 1 6 4v4c-2 0-3 0-4-1-1 0-2 0-3-1z" class="r"></path><path d="M593 622l2-2 1-1c1-1 1-2 3-3l4 1c1 1 1 1 2 1 1 1 2 2 4 2 1 0 1 1 2 1 1 1 2 1 2 2h0l-1-1-1 1h0l2 2h0l6 3c1 1 2 2 2 3h1v-1l-2-2h1 2v-1h2l1-1 1 1c-3 3-4 6-4 10 0 2 1 4 3 6 1 0 2 1 3 2l-2 2c0 1 0 1 1 2l-2 2 2 1-2 1 1 1c1 0 2 1 3 1v1h-1c1 1 1 1 2 1l-1 1h-2v1c1 0 1 2 1 2s1 0 1 1h-1l1 2-1 1c2 1 3 2 5 4h0c-1 1-1 1-2 1v1h-1v1 1h-2-1l2-1-1-1-2 1h0c1 1 1 2 1 2v1h-3v3c1 1 1 3 0 4h1 0 1l-1 1-2 1c-2 3-4 5-8 7h-1-1c-3 1-5 0-8-2h-3c-1-2-2-3-4-4h0l2 3v1l-2-2h-1l1 2-2 1c1 0 1 0 2 1h0l-1 1h-2c-1-1 0-1-1-2-2 1-2 2-2 3l-18-41v-1h2c2-1 4-1 5-1-1-2-3-1-4-2l1-1c0-2 2-5 3-7h1c2-4 5-7 6-11l1-1v-1l1-1h-1v-1h1c2 0 2-2 2-3z" class="M"></path><path d="M604 669l-1-1v-1h2c1 0 0 0 1-1-1 0-2 0-3-1 2 0 2 0 4 1h0l1-1c-1-1-3-1-3-3 1 1 3 3 4 3h1c-1 2-3 3-5 5l-1-1z" class="e"></path><path d="M594 660c1 1 0 1 1 0l-2-2h1 0c1 1 2 1 3 1l-3-2h1c1 0 2 1 3 1s2 1 2 1h-1c2 1 4 2 6 4l-5-2h-1c1 1 2 2 3 2l-1 1v-1c-1 0-2-1-3-1v1h1l3 3c-3-1-5-3-7-2-1-1-2-1-2-2h1v-2z" class="D"></path><path d="M589 665v-1l-1-1h1c2 1 3-1 5 2h0c1 1 3 2 4 3h0c-2 0-3-1-5-2h0l4 4-1 1c-1-1-2-2-4-3 1 1 1 1 1 2h-2c-2-2-4-4-7-5l1-1c2 0 3 2 5 2l-1-1z" class="C"></path><path d="M621 653c-1-3-5-3-6-5 0 0 1 0 1 1h4l-4-3 4 1h0c-1-1-2-2-2-3l2 1h0l-2-3v-1c1 0 1 1 2 1h1c1 0 1 1 2 2v1h-2c1 2 3 3 4 4 0 1 0 1-1 2v1l2 1 1 1h-3l2 2h0c-1 0-2 0-3-1h0c-1-1-2-1-2-2z" class="V"></path><path d="M626 626l1 1c-3 3-4 6-4 10 0 2 1 4 3 6 1 0 2 1 3 2l-2 2c0 1 0 1 1 2l-2 2 2 1-2 1-2-1v-1c1-1 1-1 1-2-1-1-3-2-4-4h2v-1c-1-1-1-2-2-2l-3-3h2v-1l-1-2h1v-3h1l1-1-1-1h0 1v-1l-2-2h1 2v-1h2l1-1z" class="b"></path><path d="M580 655c2-1 2-1 4-1l3 2c1 1 2 1 3 1 2 1 3 2 4 3v2h-1c0 1 1 1 2 2l6 3-7-2h0c-2-3-3-1-5-2h-1l1 1v1l-2-1c-2-1-2-1-3-3h-1-1l-1-1 1-1h-2c1-1 0-1 1-2-1 0-1 1-2 0 0-1 0-1 1-2z" class="R"></path><path d="M610 665v-1c-1-1-2-2-4-3h0c2 1 3 2 5 2h1c-1-2-4-2-5-4h0l4 2 2 1h0 1c0 1 0 1 1 2h-1v1l1 1h-2l1 1h0c-2 0-4 1-6 2-1 1-2 2-3 4s-2 3-2 5c-1 0-1-1-1-1l-1 1v4h-1l-1-1h0v1h0-2c-3 0-6 0-8-2 1-1 2 0 3 0v-1c-1-1-2-1-3-1l1-1h3c1 2 3 3 5 3h0v-1c-3-1-5-3-8-5h1c2 2 5 4 8 4v-1h-1c-1 0-2-1-2-2 1 1 2 1 4 1l2-2-1-2h1 1l-1-2 2-1 1 1c2-2 4-3 5-5z" class="q"></path><path d="M613 662c2 0 3 1 4 1-2-2-6-3-8-5 2 0 2 1 4 1 1 1 4 2 5 2v-1l-4-2c-1 0-3-1-3-2 1 0 1 1 2 1 2 1 4 2 6 2-1-1-7-4-7-5 1 0 2 1 3 1 1 1 1 1 3 0 1 0 1 0 2 1s2 1 3 1v1h-2l1 1h2c2 1 3 3 5 4v1c-1 0-2-1-3-2v1c0 2 2 1 3 3h-1 0c-2-1-2-1-4-1 1 1 1 1 2 1 0 1 1 1 1 2h0-1c-1-1-1-1-2-1s-2-1-2-1h-2l-1-1-1 1h1l6 6 1 1h0c-1 1-2 0-3 0l-1-1c-3-3-4-4-8-5h0l-1-1h2l-1-1v-1h1c-1-1-1-1-1-2h-1z" class="V"></path><path d="M618 655c-2-1-3-2-5-3l3 1c1 1 3 1 5 2-1-2-4-3-6-4v-1l6 3c0 1 1 1 2 2h0c1 1 2 1 3 1h0l-2-2h3c1 0 2 1 3 1v1h-1c1 1 1 1 2 1l-1 1h-2v1c1 0 1 2 1 2s1 0 1 1h-1l1 2-1 1c2 1 3 2 5 4h0c-1 1-1 1-2 1v1h-1v1 1h-2-1l2-1-1-1-2 1h0c1 1 1 2 1 2v1h-3v3c1 1 1 3 0 4h1 0 1l-1 1-2 1c1-5 0-7-1-11 1 0 2 1 3 0h0l-1-1-6-6h-1l1-1 1 1h2s1 1 2 1 1 0 2 1h1 0c0-1-1-1-1-2-1 0-1 0-2-1 2 0 2 0 4 1h0 1c-1-2-3-1-3-3v-1c1 1 2 2 3 2v-1c-2-1-3-3-5-4h-2l-1-1h2v-1c-1 0-2 0-3-1s-1-1-2-1z" class="t"></path><path d="M582 650c0 1-1 1-3 2h-1s-1 0-1 1h4l-3 1c0 1 1 1 2 1-1 1-1 1-1 2 1 1 1 0 2 0-1 1 0 1-1 2h2l-1 1 1 1-1 1 1 1v2c1 1 1 2 3 2l-1 1 1 2v1h1 1v1l1 2h-1c0 1 0 1 1 1s3 1 5 2h-3l-1 1c1 0 2 0 3 1v1c-1 0-2-1-3 0 2 2 5 2 8 2h2 0v-1h0l1 1h1v-4l1-1s0 1 1 1c-1 4 0 7 3 10v1h-3c-1-2-2-3-4-4h0l2 3v1l-2-2h-1l1 2-2 1c1 0 1 0 2 1h0l-1 1h-2c-1-1 0-1-1-2-2 1-2 2-2 3l-18-41v-1h2c2-1 4-1 5-1z" class="V"></path><path d="M582 640h1 1l-2 3h1c3 2 3 5 7 6h0c0-2-2-2-4-3l1-1c1 1 3 2 4 3h0l2-1c0-1 0-1-1-1l1-1c1 1 5 2 5 4h1l2 2c2 1 4 2 6 4l-1 1c-2-2-4-4-6-4l-2-1h-2c3 2 5 1 8 4v1l-1-1c-1 0-1-1-2-1l1 2-1 1c-2 0-4-2-6-2l1 1c3 1 4 2 6 3h-2s-1-1-2-1-2-1-3-1h-1l3 2c-1 0-2 0-3-1h0-1l2 2c-1 1 0 1-1 0s-2-2-4-3c-1 0-2 0-3-1l-3-2c-2 0-2 0-4 1-1 0-2 0-2-1l3-1h-4c0-1 1-1 1-1h1c2-1 3-1 3-2-1-2-3-1-4-2l1-1c0-2 2-5 3-7z" class="H"></path><path d="M614 667c4 1 5 2 8 5l1 1c1 4 2 6 1 11-2 3-4 5-8 7h-1-1c-3 1-5 0-8-2v-1c-3-3-4-6-3-10 0-2 1-3 2-5s2-3 3-4c2-1 4-2 6-2z" class="j"></path><path d="M615 672h1c1 1 2 2 3 4l-1-1h-1-3l-1-2 2-1z" class="B"></path><path d="M614 667c4 1 5 2 8 5-2 0-3-1-4-2l-1 1c-4 0-6-1-9-2 2-1 4-2 6-2z" class="Y"></path><path d="M610 677h5 3c2 1 2 3 1 5 0 1-1 3-2 4h-4c-1-1-4-3-5-5 0-1 0-3 1-4h1z" class="r"></path><path d="M605 673l1 1h5v1c0 1 0 1-1 2h-1c-1 1-1 3-1 4 1 2 4 4 5 5h4c-1 1-2 2-4 3l2 2h-1c-3 1-5 0-8-2v-1c-3-3-4-6-3-10 0-2 1-3 2-5z" class="l"></path><path d="M606 674h5v1c0 1 0 1-1 2h-1c-1 1-1 3-1 4 1 2 4 4 5 5h-2c-2-1-4-2-5-4-1-3-1-5 0-8z" class="m"></path><path d="M593 622l2-2 1-1c1-1 1-2 3-3l4 1c1 1 1 1 2 1 1 1 2 2 4 2 1 0 1 1 2 1 1 1 2 1 2 2h0l-1-1-1 1h0l2 2h0c2 1 3 2 5 4h-1c-1-1-2-1-3-2s-3-2-5-3h0l1 1c1 1 2 1 3 2l3 3h0-1c-2-1-4-1-6-2l4 3v1c-1 0-2-1-3-1h-2c2 1 3 1 5 3l-1 1v-1c-2-1-5-2-7-2l10 6c-4-1-7-4-11-4h1c2 1 7 4 8 5l-1 1v-1c-2-1-4-1-6-2l5 4h0-1c-2-1-4-1-6-2h0c2 2 7 3 8 6-1 0-1 0-1-1-2-1-4-2-6-2h0c1 2 4 2 5 4h-1 0c-2-1-2-1-4-1-1 0-1-1-2-1 1 1 3 3 4 5-1-1-3-2-4-3l-1 1c2 1 3 2 5 4l-6-3h0c2 2 4 3 5 4-1 1 0 1-1 0-2-1-4-2-7-3 0-2-4-3-5-4l-1 1c1 0 1 0 1 1l-2 1h0c-1-1-3-2-4-3l-1 1c2 1 4 1 4 3h0c-4-1-4-4-7-6h-1l2-3h-1c2-4 5-7 6-11l1-1v-1l1-1h-1v-1h1c2 0 2-2 2-3z" class="T"></path><path d="M608 631c-1 0-2 0-3-1l-2-1-2-1c-1 0-3-1-3-2 1 0 2 1 4 1h0l-2-2h0l3 1h1c-1-1-2-2-3-2l1-1c2 1 4 2 6 4l1 1 4 3v1c-1 0-2-1-3-1h-2z" class="B"></path><path d="M602 647c-2 0-4-2-5-4h0 1 0l-2-2h3 1c-1-1-2-1-2-2 2 0 5 1 7 3h0c1 2 4 2 5 4h-1 0c-2-1-2-1-4-1-1 0-1-1-2-1 1 1 3 3 4 5-1-1-3-2-4-3l-1 1z" class="H"></path><path d="M584 562v1c2 0 3 1 4 2h0l1-1 1-1c4 4 9 6 14 10 2 2 5 4 7 5 2 2 5 3 7 4l2-1c2 1 4 1 5 1 6 2 11 4 17 5h4v2h-2l2 2v1 1c-1 0-3 1-4 2-4 4-5 6-5 12l1 1-1 2h0v1 3l2 1-1 1v1l1 1-1 1h1v1c-2 0-3-1-5 1h0v2c-3 0-5 2-7 4l-1-1-1 1h-2v1h-2-1l2 2v1h-1c0-1-1-2-2-3l-6-3h0l-2-2h0l1-1 1 1h0c0-1-1-1-2-2-1 0-1-1-2-1-2 0-3-1-4-2-1 0-1 0-2-1l-4-1c-2 1-2 2-3 3l-1 1-2 2c0 1 0 3-2 3h-1v1h1l-1 1v1l-1 1c-1 4-4 7-6 11h-1c-1 2-3 5-3 7l-1 1c1 1 3 0 4 2-1 0-3 0-5 1h-2v1h0l-8-19c-1-1-2-4-1-5 0-3 4-5 5-8h1v-3-1-2l1-1 1-1v-3h3 0c1-1 1-1 2-1h1l2-1c0-1 0-1-1-2h2l-2-2 1-1c0 1 0 1 1 1v-1c-1-1-2-1-3-2v-2c1-1 1-1 3-1h4 1l-1-1-1-1h1c1-1 0-1 0-2h3l1-1h1l-1-1c1-1 2-2 3-2h1v-1h2 2l1-1h2l-3-3-4-2c-2-2-4-3-6-5h0c-3-4-4-10-5-15z" class="N"></path><path d="M600 576c1-1 0-1 0-2 1 1 2 1 4 1 1 2 3 2 4 3 2 1 3 2 4 3l-5-1c-1 0-1-1-2-1-2-1-3-1-5-3zm19 9c2 1 5 2 6 4h2c1 1 2 1 3 2s2 2 3 4h-1c-1-1-3-2-4-2s-4-3-5-3l-1-1h-2c-1-1-3-2-3-3 1 0 1 0 2-1z" class="G"></path><path d="M591 617c2 1 2 0 4 0l1-1c2-3 3-6 6-8-1 3-1 5-3 7v1c-2 1-2 2-3 3l-1 1-2 2-4 1c1-1 2-1 3-3-2 0-2 0-4-1 1-1 2-1 3-2z" class="J"></path><path d="M604 573c2 2 5 4 7 5 2 2 5 3 7 4l6 3 3 4h-2c-1-2-4-3-6-4l-5-2c-1 0-1-1-2-2s-2-2-4-3c-1-1-3-1-4-3l-2-2h2z" class="P"></path><path d="M609 598v-1l1-2h-1c1-1 2-1 3-1v-1l1-1v1h2c1 0 2 1 3 1 0 0 1 1 2 1s2 1 3 2v1c-2-1-3-2-5-2 0 1 1 1 2 1 2 1 3 2 5 4l-4-2h-1l6 5c-1 0-2-1-3-1l-2-1c-1-1-1-1-2-1v1c-4-1-7-3-10-4z" class="R"></path><path d="M625 601v1h1l6 3c-2-3-5-4-7-7l5 3c-1-2-4-3-4-5 1 1 3 2 4 2l-4-4c1 0 2 1 3 1s2 1 3 2v1l2 2h0-2c0 2 2 1 1 3h-2l2 2h-2c1 1 2 1 3 3-2 0-3-1-4-1l3 3 1 1 2 1-1 1h-1l1 1h-1-1-1c-1 1-1 2-2 2l-1-1v-1l-2-2c0-2-7-4-9-6h1c1 1 2 2 4 2 1 1 2 1 3 2h1v-1c-3-2-5-3-8-5l7 2h0c-1-1-2-1-3-2-1 0 0 0-1-1h1c1 0 2 1 3 1l-6-5h1l4 2z" class="j"></path><path d="M629 614l1-1c-1 0 0 0-1-1h-1 1 1l-1-1 1-2c1 0 1 1 2 1h1l1 1 2 1-1 1h-1l1 1h-1-1-1c-1 1-1 2-2 2l-1-1v-1z" class="P"></path><path d="M584 562v1c2 0 3 1 4 2h0l1-1 1-1c4 4 9 6 14 10h-2l2 2c-2 0-3 0-4-1 0 1 1 1 0 2-2-1-4-3-6-3l2 2c2 0 2 1 3 2v1h-1-1v1l1 1c-1 0-2 0-3-1h-1c1 2 4 3 5 5l-4-2c-2-2-4-3-6-5h0c-3-4-4-10-5-15z" class="e"></path><g class="X"><path d="M594 573c-1 0-1-1-2-2l1-1c1 1 1 2 2 2l-1-1 1-1c1 1 3 1 4 2l3 1 2 2c-2 0-3 0-4-1 0 1 1 1 0 2-2-1-4-3-6-3z"></path><path d="M594 579l-1-1c-2-1-3-2-4-3l1-1 1 1h1l-1-1v-1h0-2l-2-2v-3c2 1 3 2 4 3 2 2 3 3 5 4h0c2 0 2 1 3 2v1h-1-1v1l1 1c-1 0-2 0-3-1h-1z"></path></g><path d="M618 582l2-1c2 1 4 1 5 1 6 2 11 4 17 5h4v2h-2l2 2v1 1c-1 0-3 1-4 2-4 4-5 6-5 12l1 1-1 2h0v1 3l2 1-1 1v1c-1 0-2-2-3-3h0l-1-1h1l1-1-2-1-1-1-3-3c1 0 2 1 4 1-1-2-2-2-3-3h2l-2-2h2c1-2-1-1-1-3h2 0l-2-2v-1c1 1 1 1 2 1-1-2-4-4-6-5 1 0 3 1 4 2h1c-1-2-2-3-3-4s-2-1-3-2l-3-4-6-3z" class="F"></path><g class="V"><path d="M634 611c2-3 1-5 1-8 0-1 1-1 0-2h0l1-2h0c0-1 0-1 1-1l1-1c1-1 1-2 3-2h1c-4 4-5 6-5 12l1 1-1 2h0v1 3l2 1-1 1v1c-1 0-2-2-3-3h0l-1-1h1l1-1-2-1z"></path><path d="M618 582l2-1c2 1 4 1 5 1 6 2 11 4 17 5h4v2h-2l2 2v1 1c-1 0-3 1-4 2h-1l2-2-1-1c-1 0-2 0-3-1-1 0-1-1-2-1s-1 1-1 1l-3-2c-3-2-6-3-9-4l-6-3z"></path></g><path d="M642 592v-1c-1-1 0-1-1-1v-1h3l2 2v1 1c-1 0-3 1-4 2h-1l2-2-1-1z" class="b"></path><defs><linearGradient id="Z" x1="617.545" y1="611.856" x2="605.652" y2="608.17" xlink:href="#B"><stop offset="0" stop-color="#b4b3b2"></stop><stop offset="1" stop-color="#d5d5d3"></stop></linearGradient></defs><path fill="url(#Z)" d="M609 598c3 1 6 3 10 4v-1c1 0 1 0 2 1l2 1h-1c1 1 0 1 1 1 1 1 2 1 3 2h0l-7-2c3 2 5 3 8 5v1h-1c-1-1-2-1-3-2-2 0-3-1-4-2h-1c2 2 9 4 9 6l2 2v1l1 1c1 0 1-1 2-2h1 1 1 0c1 1 2 3 3 3l1 1-1 1h1v1c-2 0-3-1-5 1h0v2c-3 0-5 2-7 4l-1-1-1 1h-2v1h-2-1l2 2v1h-1c0-1-1-2-2-3l-6-3h0l-2-2h0l1-1 1 1h0c0-1-1-1-2-2-1 0-1-1-2-1-2 0-3-1-4-2-1 0-1 0-2-1l-4-1v-1c2-2 2-4 3-7l1-2 3-3v-1l2-2c0-1 1-1 1-2z"></path><path d="M602 608l1-2 1 2h1l-1 2h1v1h-2l1 1h1v1h-2v1h1l-1 1h-2v1h2v1l-4-1v-1c2-2 2-4 3-7z" class="S"></path><path d="M626 621c-3-1-5-2-7-4h1c2 1 3 2 4 2-1-1-4-3-5-5l4 3 1-1-1-1h0l1 1 1-1-2-1v-1h1c1 1 2 1 3 1v-1l1 2h0-1c1 1 1 1 1 2v2h-1c0 1 2 1 1 2 0 0-1 0-2-1v1z" class="j"></path><path d="M627 613c-1 0-1 0-2-1-1 0-1 0-2-1 1 0 3 1 4 1l2 2v1l1 1c1 0 1-1 2-2h1 1 1 0c1 1 2 3 3 3l1 1-1 1h1v1c-2 0-3-1-5 1h0v2c-3 0-5 2-7 4l-1-1h0v-1l-1 1c-1 0-2-1-2-1l-3-3v-1c2 1 4 3 6 3v-1l-5-3h0c1 1 5 3 7 2-1 0-1-1-2-1v-1c1 1 2 1 2 1 1-1-1-1-1-2h1v-2c0-1 0-1-1-2h1 0l-1-2z" class="a"></path><path d="M630 616c1 0 1-1 2-2h1 1 1 0c1 1 2 3 3 3l1 1-1 1h1v1c-2 0-3-1-5 1h0v2c-3 0-5 2-7 4l-1-1h0c2-1 4-3 6-4l-1-1c0 1-1 1-2 1 1-1 2-2 3-2l-1-1 1-1h0l-2-2h0z" class="e"></path><path d="M630 616c1 0 1-1 2-2h1 1 1 0l-1 1 1 2-3 1h0l-2-2h0z" class="F"></path><path d="M587 595l3 2h1l-2-1 1-1 4 2h1l-1-1h0 2l1 1h1c1 0 1 1 2 1v-1c-2 0-4-1-5-3v-1l2 1-1-1v-1h2c1-1 2-2 4-3 1 0 2 2 3 2s1 0 2 1l-1 1 1 1h1c-1 3-4 6-5 8l-12 15c-1 1-2 1-3 2 2 1 2 1 4 1-1 2-2 2-3 3l4-1c0 1 0 3-2 3h-1v1h1l-1 1v1l-1 1c-1 4-4 7-6 11h-1c-1 2-3 5-3 7l-1 1c1 1 3 0 4 2-1 0-3 0-5 1h-2v1h0l-8-19c-1-1-2-4-1-5 0-3 4-5 5-8h1v-3-1-2l1-1 1-1v-3h3 0c1-1 1-1 2-1h1l2-1c0-1 0-1-1-2h2l-2-2 1-1c0 1 0 1 1 1v-1c-1-1-2-1-3-2v-2c1-1 1-1 3-1h4 1l-1-1-1-1h1z" class="D"></path><path d="M577 616h1c2 0 4 1 7 1h0l-3 4v1c-2 1-3 3-5 4v-1c1-3 1-4 2-6l-2-2h0v-1z" class="F"></path><path d="M607 594h1c-1 3-4 6-5 8l-12 15c-1 1-2 1-3 2l-2 2-1-1 21-24c0-1 0-1 1-2z" class="B"></path><path d="M588 619c2 1 2 1 4 1-1 2-2 2-3 3l-2 1c-2 0-3 1-4 2h-1c-1 1-2 3-4 4l-1-1c0-1 0-1-1-1l-1-1c1-1 1-1 2-1 2-1 3-3 5-4 1-1 2-2 3-2l1 1 2-2z" class="G"></path><path d="M585 620l1 1-7 6c-1 0-1 0-2-1h0c2-1 3-3 5-4 1-1 2-2 3-2z" class="I"></path><path d="M587 606l3-2v1c1 0 1 0 2 1-2 0-2-1-4 0v1c1 0 3 0 4 1l1 1c-2 2-5 5-7 6l-1-1h0-2c-1-1-3-1-4-1v-2h0l1-1s1 1 2 1c1-1 3-1 4-1v-1c-1 0-2 0-3-1h3c-1-1 0-1-1-1l-1-1 1-1 2 2v-1z" class="h"></path><path d="M580 598c1-1 1-1 3-1h4c0 2 2 2 3 4h0-1l-2-2c0 1 1 2 2 3-2 0-2 0-3-1v1c1 1 1 1 3 1l-1 1c-1 0-1 0-2-1l-1 1c1 0 1 1 2 2v1l-2-2-1 1 1 1c1 0 0 0 1 1h-3c1 1 2 1 3 1v1c-1 0-3 0-4 1-1 0-2-1-2-1l-1 1h0v2l-1-1-1-1c-1 0-2 0-3 1v-3h3 0c1-1 1-1 2-1h1l2-1c0-1 0-1-1-2h2l-2-2 1-1c0 1 0 1 1 1v-1c-1-1-2-1-3-2v-2z" class="X"></path><path d="M580 598c1-1 1-1 3-1 0 0 1 1 1 2h-1c0 1 0 1 1 2-1 0-1 0-1 1-1-1-2-1-3-2v-2z" class="i"></path><path d="M573 613c1 1 3 2 4 3v1h0l2 2c-1 2-1 3-2 6v1c-1 0-1 0-2 1l1 1c-1 1-3 1-4 1h-1v1h1v1h-1c-2 0-3 0-4 2-1-1-2-4-1-5 0-3 4-5 5-8h1v-3-1-2l1-1z" class="n"></path><path d="M572 614c1 0 1 0 2 1v1c1 0 1 1 1 2h-1-1c0-1 0-1-1-1v-1-2z" class="V"></path><path d="M577 617l2 2c-1 2-1 3-2 6v1c-1 0-1 0-2 1h-1-4c1-2 2-3 3-4 1 0 2-1 3-1 0-2 0-3 1-5z" class="L"></path><path d="M589 623l4-1c0 1 0 3-2 3h-1v1h1l-1 1v1l-1 1c-1 4-4 7-6 11h-1c-1 2-3 5-3 7l-1 1c1 1 3 0 4 2-1 0-3 0-5 1h-2v1h0l-8-19c1-2 2-2 4-2h1v-1h-1v-1h1c1 0 3 0 4-1 1 0 1 0 1 1l1 1c2-1 3-3 4-4h1c1-1 2-2 4-2l2-1z" class="D"></path><path d="M589 623l4-1c0 1 0 3-2 3h-1v1h1l-1 1v1h-2v1c-1-1-2-1-2-1-2 0-2 1-3 0 2-1 3-1 4-4l2-1z" class="I"></path><path d="M574 640c0-1 0-2 1-2 1-3 6-5 9-7l-5 6c-1 1-3 2-4 3 0 2 0 4-1 6l1 1h1v1l-1 1c-1-1-1-2-2-4 2-2 1-3 1-5z" class="l"></path><path d="M582 640c-1 0-1 1-2 1h-1c2-2 3-4 4-6s1-2 2-3c1 0 1-1 1-1 1-1 1-2 2-2h1c-1 4-4 7-6 11h-1z" class="g"></path><path d="M576 628c1 0 1 0 1 1l1 1c-2 2-4 4-5 6l-2 2c1 1 2 1 3 2 0 2 1 3-1 5 1 2 1 3 2 4v1l2 1h-2v1h0l-8-19c1-2 2-2 4-2h1v-1h-1v-1h1c1 0 3 0 4-1z" class="L"></path><path d="M573 645l-2-6h-1l1-1c1 1 2 1 3 2 0 2 1 3-1 5z" class="G"></path><path d="M467 396l1 4c0 2 0 3 1 5v1c1 1 1 1 1 2h7c5 1 11 0 16 1l-1 1v4l-4-1c0 2 0 4 1 5s1 1 1 2l1 1v9h0v9l1 2c1 0 2 0 2 1l-6 1h-4c-2 0-6-1-7 1-1 1-1 2-1 3h0c2 0 3 1 5 1 3 0 6 0 9 1h-15c0 1 0 0 1 1v1c3 1 3 2 5 4v-1c1 2 2 4 3 5h7l5-1h7 1l1 1h3c1 0 1 1 2 2l1 3c1 0 1 0 2-1v1 5l-1 1c-2-1-2-2-2-4v-1c-1 1-1 1-1 2s-1 1-1 2v1 10l1 1v2h-2-4c-4 1-10 0-14 1h0c-1 1-3 1-5 1-2 1-3 1-5 1v-1l-4-6-2-2v-1c-2-2-5-6-8-6h0l-1 1c2 2 4 4 5 7l1 1-1 1-1 1 6 5v1l-2-1h0c0 1 1 2 2 2h1c1 1 2 1 3 1l1 1h7l3 3 2 3h0l-1 2 1 1v4l2 1-1 1-1 1-2 49-1-1v7 4h0-1v2h0c-1 1-2 1-3 1h-1-3c-2 0-3-1-5-2-1 0-2-1-3-2l-1-1-1 1-1-1-1-1c-1 0-2 1-3 2 0 1 0 3-1 4h-1c0-1-1-1-1-2-1-1-2-2-3-4-2 0-2 0-3 1-2-1-3-1-5-1h0c-1 0-2 0-3 1h-1l-2-3 1-1c-3-1-4-4-6-6l-1-2c0-1 0-1-1-2h0-1c-1-1-1 0-2-1-1 0-2 0-2-1-2 2-5 4-6 7v1s-1 0-1 1c0-1 1-2 1-2l-2-2 2-2 6-9c3-7 4-13 4-21l-1-8c-1-3-2-6-3-8-1-3-3-5-5-8-1-2-3-3-5-5-1-1-2-2-4-3 3-3 4-8 5-12h1l-1-3 1-2h2 1c3 1 7 2 10 3l-5-13c0-1-1-2-2-2v1h1c0 1-2 2-3 4h1l-1 1c0 1-1 1-1 2h-1c0-2 1-3 1-4v-1l2-4c0-3-4-7-6-9v-3h0c0-2-1-4-2-5-5-9-7-20-16-26-1 0-1 0-1-1s-1-5 0-6c1 1 1 2 3 3h5 5l23-1h7 1c1 0 1 0 2 1 0 0 1-1 1 0h2c2 0 3-1 5 0v2l-1 1 1 1 1 2 2-1v6c1-2-1-5 1-7v-5-8-1l2 7h0c2-2 1-9 2-12z" class="w"></path><path d="M418 411l23-1v2c-1 0-2 0-3 1l-25-1v-1h5z" class="V"></path><path d="M448 410h1c1 0 1 0 2 1-4 2-7 4-9 8-1 1-2 1-2 3-1 1-1 1-1 2 0 2 1 4 0 5v1h-1-1c-1-5 0-11 3-15v-2h-2c1-1 2-1 3-1v-2h7z" class="s"></path><path d="M448 410h1c-4 2-8 3-10 7 0 1 0 1-1 2h0c0 3 0 8 1 10v1h-1-1c-1-5 0-11 3-15v-2h-2c1-1 2-1 3-1v-2h7z" class="Q"></path><path d="M482 476c-2-6-5-10-9-14l-1-1v-1l5 4c-1-2-3-3-4-5s-1-4-3-5c-5-2-6-7-11-8l-1-1h0 1c6 2 15 10 19 15l6 13h1l1 2h-2c-1 0-1 0-2 1z" class="a"></path><defs><linearGradient id="a" x1="409.855" y1="410.522" x2="411.969" y2="427.562" xlink:href="#B"><stop offset="0" stop-color="#010100"></stop><stop offset="1" stop-color="#262523"></stop></linearGradient></defs><path fill="url(#a)" d="M405 408c1 1 1 2 3 3h5v1c1 9 7 19 11 28-1 0-1 0-2 1-5-9-7-20-16-26-1 0-1 0-1-1s-1-5 0-6z"></path><path d="M441 475c3 1 5 1 7 2 6 2 10 6 14 9 2 1 4 4 6 4 0 0 2 0 2-1h1c1 1 0 1 2 1-1-1-1-1-1-2l-6-6c-4-5-9-8-14-10-4-1-11-2-11-3 10 0 20 6 27 12l6 5v1l-2-1h0c0 1 1 2 2 2h1c1 1 2 1 3 1l1 1c-1 0-3 0-4 1-2 2-4 0-6 1 1 1 1 2 1 2-1 0-2 0-3 1h-2-1v-1l-3-1c-1 0-3-1-4-2l10 3-2-2c-4-6-12-10-18-13-1 0-3-1-4-1-1-1-1-2-2-3z" class="a"></path><defs><linearGradient id="b" x1="447.207" y1="443.021" x2="464.86" y2="430.124" xlink:href="#B"><stop offset="0" stop-color="#5e5d5b"></stop><stop offset="1" stop-color="#777"></stop></linearGradient></defs><path fill="url(#b)" d="M467 396l1 4c0 2 0 3 1 5v1c1 1 1 1 1 2h7 0c-2 1-4 1-6 0l-1 1c-1 6-1 11-2 16l1 2c-1 6-4 12-9 16-4 2-7 2-10 1-5-1-8-4-10-7-1-2-2-4-2-7h1v-1c1-1 0-3 0-5l1 1 2 7c0 1 1 1 1 2 1 0 1-1 2-1l3 3c1 0 2 0 3 1h0 0-2l-1 1c2 1 3 1 5 0 3-1 4-2 6-5 1-4 2-7 3-11 1-2-1-5 1-7v-5-8-1l2 7h0c2-2 1-9 2-12z"></path><path d="M439 424l1 1 2 7c0 1 1 1 1 2 1 0 1-1 2-1l3 3c1 0 2 0 3 1h0 0-2l-1 1c2 1 3 1 5 0 3-1 4-2 6-5v1l-3 5-2 1c-4 1-7-1-10-3h0c-3-2-4-4-5-7v-1c1-1 0-3 0-5z" class="g"></path><path d="M467 396l1 4c0 2 0 3 1 5v1c1 1 1 1 1 2h7 0c-2 1-4 1-6 0l-1 1c-1 6-1 11-2 16-2 5-5 11-10 13l-2 1 3-5v-1c1-4 2-7 3-11 1-2-1-5 1-7v-5-8-1l2 7h0c2-2 1-9 2-12z" class="D"></path><path d="M463 410v-8-1l2 7c0 6 0 12-1 18h-1c0 3-2 6-4 8v-1c1-4 2-7 3-11 1-2-1-5 1-7v-5z" class="S"></path><path d="M451 411s1-1 1 0h2c2 0 3-1 5 0v2l-1 1 1 1 1 2 2-1v6c-1 4-2 7-3 11-2 3-3 4-6 5-2 1-3 1-5 0l1-1h2 0 0c-1-1-2-1-3-1l-3-3c-1 0-1 1-2 1 0-1-1-1-1-2l-2-7-1-1c0-1 0-1 1-2 0-2 1-2 2-3 2-4 5-6 9-8z" class="w"></path><path d="M462 416v6c-1 4-2 7-3 11-2 3-3 4-6 5-2 1-3 1-5 0l1-1h2 0 0c-1-1-2-1-3-1l1-1c2 0 4 0 5-1 4-3 5-12 6-17l2-1z" class="G"></path><path d="M451 411s1-1 1 0h2c2 0 3-1 5 0v2l-1 1c-3-1-7-2-10 0s-4 4-5 7 0 8 2 11c1 1 3 2 4 3l-1 1-3-3c-1 0-1 1-2 1 0-1-1-1-1-2l-2-7-1-1c0-1 0-1 1-2 0-2 1-2 2-3 2-4 5-6 9-8z" class="K"></path><path d="M439 424c0-1 0-1 1-2 0-2 1-2 2-3-1 6 0 9 3 14-1 0-1 1-2 1 0-1-1-1-1-2l-2-7-1-1z" class="B"></path><defs><linearGradient id="c" x1="491.153" y1="424.142" x2="471.237" y2="418.316" xlink:href="#B"><stop offset="0" stop-color="#666665"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#c)" d="M477 408c5 1 11 0 16 1l-1 1v4l-4-1c0 2 0 4 1 5s1 1 1 2l1 1v9h0v9l1 2c1 0 2 0 2 1l-6 1h-4c-2 0-6-1-7 1-1 1-1 2-1 3h0c2 0 3 1 5 1 3 0 6 0 9 1h-15c0 1 0 0 1 1v1l-2-1v-1c1-4 3-6 4-9 0-3-1-5-1-8l-4-7-2-3h-1c-1 1-1 3-1 5l-1-2c1-5 1-10 2-16l1-1c2 1 4 1 6 0h0z"></path><path d="M486 439v-3c0-1 1-1 1-1 1-1 1-2 1-3h1v3 1c-1 2-1 3-1 5h-1v-2h-1z" class="F"></path><path d="M491 430h0v9l1 2c1 0 2 0 2 1l-6 1h-4v-1c2-1 2-2 2-3h1v2h1c0-2 0-3 1-5v-1c0-2 1-3 2-5z" class="n"></path><path d="M482 417c2 1 2 2 3 4 0 5 1 13-1 18l-1 1c-1-1-1-2-1-3 1-3 0-4 0-7h2v-4c-1 0-1 0 0-1-1-1-1-2-1-3 0-2-1-2-1-5z" class="J"></path><path d="M471 416l1 1 4 8c1 1 2 3 2 5h0c0 2 2 4 2 5 1 2 1 4 1 6l-3-6c0-1 0-2-1-3h0l-4-7c0-2-1-4-2-5-1-2 0-3 0-4z" class="G"></path><path d="M472 417h1l2 3h1c0-2-1-2-2-3 1-1 1-1 2-1v1l1 1v-2h4l1 1c0 3 1 3 1 5 0 1 0 2 1 3-1 1-1 1 0 1v4h-2c0 3 1 4 0 7l-4-7c0-2-1-4-2-5l-4-8z" class="H"></path><path d="M477 408c5 1 11 0 16 1l-1 1v4l-4-1c-6 0-11 1-16 0-1 1-1 2-1 3s-1 2 0 4c1 1 2 3 2 5l-2-3h-1c-1 1-1 3-1 5l-1-2c1-5 1-10 2-16l1-1c2 1 4 1 6 0h0z" class="K"></path><path d="M477 408c5 1 11 0 16 1l-1 1h-15c-2 0-5 0-7-1l1-1c2 1 4 1 6 0h0z" class="S"></path><path d="M481 454c1 2 2 4 3 5h7l5-1h7 1l1 1h3c1 0 1 1 2 2l1 3c1 0 1 0 2-1v1 5l-1 1c-2-1-2-2-2-4v-1c-1 1-1 1-1 2s-1 1-1 2v1 10l1 1v2h-2-4c-4 1-10 0-14 1h0c-1 1-3 1-5 1-2 1-3 1-5 1v-1l-4-6-2-2v-1c-2-2-5-6-8-6h0l-2-3h0l1-1h0c-2-2-3-4-6-6 0-1 0-2-1-3h-1v-1c1 0 2 1 3 1l2 2c6 3 11 9 14 14l6 8 1 2h1c1-2 1-3 0-5l-1-2c1-1 1-1 2-1h2l-1-2v-5c-1-5-2-9-4-13v-1z" class="L"></path><path d="M485 468v-1h2 1 1v4h2v5c-1 2 0 3-2 6-1-1 0-1-1-2 0-1-1-2-1-3-1-1-1-1-1-2l-1-2v-5z" class="q"></path><path d="M495 476c2 0 3 1 5 2 1 1 1 3 2 4h1c1 0 2 1 4 1h0-4c-4 1-10 0-14 1l-1-1h3c2-2 2-4 2-6l2-1z" class="E"></path><path d="M488 483h3c2-2 2-4 2-6l2-1c-1 2-1 3-1 5 1 1 5 1 7 1h1 1c1 0 2 1 4 1h0-4c-4 1-10 0-14 1l-1-1z" class="R"></path><path d="M465 470l-2-3h0l1-1h0c-2-2-3-4-6-6 0-1 0-2-1-3h-1v-1c1 0 2 1 3 1l2 2c4 5 9 8 13 13 2 2 3 5 4 8 1 1 1 3 2 5l8-2 1 1h0c-1 1-3 1-5 1-2 1-3 1-5 1v-1l-4-6-2-2v-1c-2-2-5-6-8-6h0z" class="k"></path><path d="M481 454c1 2 2 4 3 5h7l5-1 4 1 1 2h0v2l-2-1-1-1-1 1c-1 2-1 4-1 6l-1 1h-1c-1 1-2 1-3 1v1h-2v-4h-1-1-2v1c-1-5-2-9-4-13v-1z" class="c"></path><path d="M481 454c1 2 2 4 3 5h7-6-1l3 5c1 1 1 2 1 3h-1-2v1c-1-5-2-9-4-13v-1z" class="e"></path><path d="M489 465v-2c1 0 2-1 3-1h1l1-1 1 1h2c-1 2-1 4-1 6l-1 1h-1c-1 1-2 1-3 1v1h-2v-4-2z" class="K"></path><path d="M489 465h3l1 1 1-1c0 1 0 3 1 4h-1c-1 1-2 1-3 1v1h-2v-4-2z" class="e"></path><path d="M503 458h1l1 1h3c1 0 1 1 2 2l1 3c1 0 1 0 2-1v1 5l-1 1c-2-1-2-2-2-4v-1c-1 1-1 1-1 2s-1 1-1 2v1 10l1 1v2h-2 0c-2 0-3-1-4-1v-2l-2-3-1-1c1 0 1 1 2 0-1-1-1-1-1-2h-3l-1 1h-1v-1c1-2 0-3 0-5 0-1 0-1 1-2h2c1 1 1 3 2 4 0-1 1-2 0-3l-1-1v-4l-1-1 2 1v-2h0l-1-2-4-1h7z" class="h"></path><path d="M503 458h1l1 1h3c1 0 1 1 2 2l-2 3c-1 1-1 2-1 3h-1l-1-5h0l-1 1c-1-1-1-1-1-2-1 2-2 5-2 7l-1-1v-4l-1-1 2 1v-2h0l-1-2-4-1h7z" class="m"></path><path d="M501 474c0-1 0-1 1-2 1 0 1 0 2-1h1 1c1 2-1 8 2 9l1 1v2h-2 0c-2 0-3-1-4-1v-2l-2-3-1-1c1 0 1 1 2 0-1-1-1-1-1-2z" class="d"></path><path d="M422 441c1-1 1-1 2-1 3 7 6 14 10 21 3 4 5 9 7 14 1 1 1 2 2 3 1 0 3 1 4 1 6 3 14 7 18 13l2 2-10-3c1 1 3 2 4 2l3 1v1h1 2c1-1 2-1 3-1 0 0 0-1-1-2 2-1 4 1 6-1 1-1 3-1 4-1h7l3 3 2 3h0l-1 2 1 1v4l2 1-1 1-1 1-2 49-1-1v7 4h0-1v2h0c-1 1-2 1-3 1h-1-3c-2 0-3-1-5-2-1 0-2-1-3-2l-1-1-1 1-1-1-1-1c-1 0-2 1-3 2 0 1 0 3-1 4h-1c0-1-1-1-1-2-1-1-2-2-3-4-2 0-2 0-3 1-2-1-3-1-5-1h0c-1 0-2 0-3 1h-1l-2-3 1-1c-3-1-4-4-6-6l-1-2c0-1 0-1-1-2h0-1c-1-1-1 0-2-1-1 0-2 0-2-1-2 2-5 4-6 7v1s-1 0-1 1c0-1 1-2 1-2l-2-2 2-2 6-9c3-7 4-13 4-21l-1-8c-1-3-2-6-3-8-1-3-3-5-5-8-1-2-3-3-5-5-1-1-2-2-4-3 3-3 4-8 5-12h1l-1-3 1-2h2 1c3 1 7 2 10 3l-5-13c0-1-1-2-2-2v1h1c0 1-2 2-3 4h1l-1 1c0 1-1 1-1 2h-1c0-2 1-3 1-4v-1l2-4c0-3-4-7-6-9v-3h0c0-2-1-4-2-5z" class="U"></path><path d="M446 518c1-1 3-2 5-2-1 2-4 2-6 4 1 0 2-1 3-1 0-1 2-1 3-1h0c0 1-1 1-2 1 0 1-1 1-2 2 1 0 2 0 2-1h4 0l-3 2h1 1c1-1 2-1 3-1v1h-1c-1 1-3 2-4 3s-1 1-2 1c-1 1-3 2-4 2h-3-1c1-1 1-1 1-2v-1l-1-4c2-2 4-2 6-3z" class="K"></path><defs><linearGradient id="d" x1="442.56" y1="532.227" x2="422.725" y2="536.216" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#3e3d3a"></stop></linearGradient></defs><path fill="url(#d)" d="M444 509v2l1-1c1 0 1 0 1 1v1c-1 0-1 1-2 1l-2 1 1 1c1-1 3-2 4-2h0l-1 1v1h1l1 1c-1 0-1 1-2 1v1c-2 1-4 1-6 3l1 4v1c0 1 0 1-1 2h1 3c-1 0-2 1-3 2l-1 1c0 1 0 1-1 2v2h0l-3 6v1c-1 2-2 4-3 5-2 2-5 4-6 7v1s-1 0-1 1c0-1 1-2 1-2l-2-2 2-2 6-9c3-7 4-13 4-21 0-1 0-3 1-4 1-2 4-4 6-7h0z"></path><path d="M422 441c1-1 1-1 2-1 3 7 6 14 10 21 3 4 5 9 7 14 1 1 1 2 2 3l1 3h-1c-1-4-4-8-5-11l-1-3c0 3 2 6 2 9 0 0 0 1 1 1 0 2 1 6 1 7 1 1 0 1 1 1l3 6v2c1 1 1 2 2 2v2l-1 2c2 0 1-1 3 0v1c0 1 0 1 1 2v1 1l-6 5h0c-2 3-5 5-6 7-1 1-1 3-1 4l-1-8h1v-1l3-11c1-8 1-17-2-24-1-1-2 0-3 0-1-1-2-1-3-1h-2v-1c-1-1-2-2-2-3 3 1 7 2 10 3l-5-13c0-1-1-2-2-2v1h1c0 1-2 2-3 4h1l-1 1c0 1-1 1-1 2h-1c0-2 1-3 1-4v-1l2-4c0-3-4-7-6-9v-3h0c0-2-1-4-2-5z" class="b"></path><path d="M437 512c0 1 0 1 2 1 0 0 0-1 1-2 0-3 2-2 4-4 1-1 2-3 3-4h3v1l-6 5h0c-2 3-5 5-6 7-1 1-1 3-1 4l-1-8h1z" class="Q"></path><path d="M424 473l1-2h2 1c0 1 1 2 2 3v1h2c1 0 2 0 3 1 1 0 2-1 3 0 3 7 3 16 2 24l-3 11v1h-1c-1-3-2-6-3-8-1-3-3-5-5-8-1-2-3-3-5-5-1-1-2-2-4-3 3-3 4-8 5-12h1l-1-3z" class="F"></path><path d="M437 511l-1-1c1-4 0-8 0-12 1 2 2 2 4 2l-3 11z" class="I"></path><path d="M430 475h2 0c-1 1-1 0-1 1v1c0 1 0 1-1 2-1 2-2 5-2 7v2 1l-1 1v4c1 1 1 1 1 2-1-2-3-3-5-5l2-2c0-4 3-10 5-14z" class="O"></path><path d="M436 498c1-4 1-8 1-12-1-2-1-3 0-5v-4h0l1-1c3 7 3 16 2 24-2 0-3 0-4-2z" class="H"></path><path d="M424 473l1-2h2 1c0 1 1 2 2 3v1c-2 4-5 10-5 14l-2 2c-1-1-2-2-4-3 3-3 4-8 5-12h1l-1-3z" class="r"></path><path d="M430 483c0-2 1-2 2-3h1c2 1 1 6 1 8v17l-3-6c-3-5-3-10-1-16z" class="G"></path><path d="M430 483c0-2 1-2 2-3h1c2 1 1 6 1 8-1 2-1 5-1 7h-1v-7-1-4h-2z" class="M"></path><path d="M443 478c1 0 3 1 4 1 6 3 14 7 18 13l2 2-10-3c1 1 3 2 4 2l3 1v1h1 2c4 5 7 11 7 17-1 5 0 11-1 16v3c-1 2-1 5-3 8l-26-58-1-3z" class="w"></path><path d="M450 525c2-1 3-1 5-1v1c-2 0-3 1-4 2h-1c-1 0-1 1-2 1l9-3c-2 2-5 3-7 5v1c2-1 4-3 6-3 0 1-4 4-6 4l1 1c1 0 5-4 6-3-1 2-4 3-6 4 2 0 4 0 5-1s2-1 3-1h0c-2 2-4 2-5 4 2 0 4-2 6-2l-3 3h1l-1 2c2 0 4-2 6-3v1l-6 4c3 0 6-2 9-3-3 2-6 4-9 5h1c2 0 4-2 7-2l-8 5 8-3v1c-1 1-2 1-4 2l-1 1h0 2 0l-2 2c1 0 3-1 4-3h0c1-1 2-1 2-2l1 1 2-1-1-1h1 2v2l1 1-3 3c-1 0-1 0-1 1h-1c-1 2-2 3-1 5l1 1v2l1 1 3 4-1 1-1-1-1-1c-1 0-2 1-3 2 0 1 0 3-1 4h-1c0-1-1-1-1-2-1-1-2-2-3-4-2 0-2 0-3 1-2-1-3-1-5-1h0c-1 0-2 0-3 1h-1l-2-3 1-1c-3-1-4-4-6-6l-1-2c0-1 0-1-1-2h0-1c-1-1-1 0-2-1-1 0-2 0-2-1 1-1 2-3 3-5v-1l3-6h0v-2c1-1 1-1 1-2l1-1c1-1 2-2 3-2s3-1 4-2c1 0 1 0 2-1z" class="e"></path><path d="M462 557c2 1 2 2 4 1h1l1 1 3 4-1 1-1-1-1-1c-1 0-2 1-3 2 0 1 0 3-1 4h-1c0-1-1-1-1-2v-2l-1-1 1-1v-1c-1-1-2-1-2-1l-2 1v-1h0c2-1 3-2 4-3h0z" class="t"></path><path d="M471 543v2l1 1-3 3c-1 0-1 0-1 1h-1c-1 2-2 3-1 5l1 1v2h-1c-2 1-2 0-4-1 0-1 0-1-1-1h-1l1-1 2 1v-3c-1 0-2 1-3 1v-1h2c0-1 1-2 1-3l-5 2 3-1 1-1c1-2 2-2 2-4 1-1 2-1 2-2l1 1 2-1-1-1h1 2z" class="c"></path><path d="M469 544l1 2c-1 1-1 1-3 1h-1c0-1 0-1 1-2l2-1z" class="r"></path><path d="M438 549l1-1 1-1h1v-1c-2 1-3 1-4 1v-1c2 0 3-1 5-2l12-6-7 6 9-4-7 5 8-3c-2 1-4 2-6 4v1s0 1-1 1l-3 3c-2 0-5 1-6 1l-1 1-1-2c0-1 0-1-1-2h0z" class="N"></path><path d="M451 547l3-1h0 0l-3 3h0c1 0 3-1 4-2l-4 4 1 1c1-1 2-2 3-2 0 2-4 4-5 5l4-2c1-1 2-1 3-1-2 2-5 3-7 5h0l5-2c0 1-3 3-5 3l1 1v-1h2v1l-2 1h1 0 3v1h-1c-1 0-2 0-3 1-1 0-2 0-3 1h-1l-2-3 1-1c-3-1-4-4-6-6l1-1c1 0 4-1 6-1l3-3c1 0 1-1 1-1z" class="D"></path><path d="M479 490h7l3 3 2 3h0l-1 2 1 1v4l2 1-1 1-1 1-2 49-1-1v7 4h0-1v2h0c-1 1-2 1-3 1h-1-3c-2 0-3-1-5-2-1 0-2-1-3-2l-1-1-3-4-1-1v-2l-1-1c-1-2 0-3 1-5h1c0-1 0-1 1-1l3-3-1-1v-2c0-2 0-3-1-4 2-3 2-6 3-8v-3c1-5 0-11 1-16 0-6-3-12-7-17 1-1 2-1 3-1 0 0 0-1-1-2 2-1 4 1 6-1 1-1 3-1 4-1z" class="Q"></path><path d="M473 531v-3c1-5 0-11 1-16l2 1c1 8-1 14-1 21 0 3-1 5-2 7l-1 3v-1c-1-4 1-8 2-12h-1z" class="I"></path><path d="M481 554c0-1-2-2-3-2l-3-3c1-3 3-5 5-7 1 3 3 3 5 6h-1-1c2 1 3 1 5 0 0 2 0 5-1 7v1h0l-6-2z" class="J"></path><path d="M467 495c1-1 2-1 3-1 7 9 9 19 9 29h0 0c-1-1 0-2 0-3h-1l-1 6c0 1 0 3-1 5v2l-1 1c0-7 2-13 1-21l-2-1c0-6-3-12-7-17z" class="X"></path><defs><linearGradient id="e" x1="466.323" y1="519.295" x2="483.791" y2="507.247" xlink:href="#B"><stop offset="0" stop-color="#c1bebd"></stop><stop offset="1" stop-color="#e9e9e9"></stop></linearGradient></defs><path fill="url(#e)" d="M469 492c2 0 5-1 6 1 2 2 2 4 3 6 3 8 4 15 4 24v-1-3c1 2 1 4 2 5 0 3 0 4-1 6v1c-1 2-1 2-3 3-1 1-1 2-1 3l-4 7h-1c0-1 0 0 1-1 2-6 3-13 4-19v-1h0c0-10-2-20-9-29 0 0 0-1-1-2z"></path><path d="M482 523v-1-3c1 2 1 4 2 5 0 3 0 4-1 6v1c-1 2-1 2-3 3 1-4 1-7 2-11z" class="F"></path><path d="M473 555l1-3-1-1s-1 0-1-1h2 1c1 1 4 5 6 5 2 1 2 1 3 4l3 3v1l-1 1h0l2 1h-1v2h0c-1 1-2 1-3 1h-1-3c-2 0-3-1-5-2-1 0-2-1-3-2l-1-1-3-4c1 0 3-1 4-2-1-1-1-1-1-3h1v1h1z" class="G"></path><path d="M468 559c1 0 3-1 4-2-1-1-1-1-1-3h1v1h1 1v2 1c3 2 4 4 7 6v2 1l-1 1c-2 0-3-1-5-2-1 0-2-1-3-2l-1-1-3-4z" class="I"></path><path d="M472 564l2-3h1c0 2 1 2 1 4l-1 1c-1 0-2-1-3-2z" class="H"></path><path d="M479 490h7l3 3 2 3h0l-1 2 1 1v4l-1-3h0v6h-1v-4h-1c-1 1-1 2 0 4h-1v-2l-1 2v-1l-1 3-1 16c-1-1-1-3-2-5v3 1c0-9-1-16-4-24-1-2-1-4-3-6-1-2-4-1-6-1 2-1 4 1 6-1 1-1 3-1 4-1z" class="h"></path><path d="M482 492c2 1 4 2 4 4v1l3 5h-1c-1 1-1 2 0 4h-1v-2l-1 2v-1l-1 3c-2-3-1-6-2-9-1-1 0-2 0-2l-1-2h1v-1l-1-2z" class="F"></path><path d="M486 505v-3c0-1-1-1-1-1 0-2-1-3 0-4l1 1v-1l3 5h-1c-1 1-1 2 0 4h-1v-2l-1 2v-1z" class="O"></path><path d="M479 490h7l3 3 2 3h0l-1 2 1 1v4l-1-3h0v6h-1v-4l-3-5v-1c0-2-2-3-4-4s-5-1-7-1c1-1 3-1 4-1z" class="t"></path><path d="M489 506h1v-6h0l1 3 2 1-1 1-1 1-2 49-1-1v7 4h0l-2-1h0l1-1v-1l-3-3c-1-3-1-3-3-4v-1l6 2h0v-1c1-2 1-5 1-7-1-1-1-1-1-2v-1c0-1-1-1-1-2h1l1-1-1-1c-1 1-1 1-2 1h-1v1l1 2h-1l-1-2c0 1 0 1-1 0 0-1-1-2-1-3 1-1 2-1 2-2 1-1 1-1 1-2-2 0-4 0-5 1 0-1 0-2 1-3 2-1 2-1 3-3v-1c1-2 1-3 1-6l1-16 1-3v1l1-2v2h1c-1-2-1-3 0-4h1v4z" class="K"></path><path d="M483 531v1c1 1 0 2 0 3 1 1 1 1 3 2l1 1v1h0v1c-1-1-2-2-3-2 0 2 3 2 1 3h-1c-1-1-1 0-1-1v1 2c0 1 0 1-1 0 0-1-1-2-1-3 1-1 2-1 2-2 1-1 1-1 1-2-2 0-4 0-5 1 0-1 0-2 1-3 2-1 2-1 3-3z" class="e"></path><path d="M486 505v1l1-2v2h1c-1-2-1-3 0-4h1v4c0 9-1 18-3 27l1 2h0v2 1l-1-1c-2-1-2-1-3-2 0-1 1-2 0-3v-1-1c1-2 1-3 1-6l1-16 1-3z" class="L"></path><path d="M716 448c1 0 1-1 1-1v-2h0v-1c0-1 0-2 1-3 0 1 0 1 1 1h0l1 2h0v2 1h3 0c4 16 4 32-1 47-1 1-1 2-2 3l1 1-2 5c-2 2-3 4-5 5-3 1-6 3-9 3v1h2l-1 1-1 1c-1 0-2-1-2-2l-1-1c0 2 0 4-1 5-2 9-5 15-12 21h-1c-2 2-5 3-7 4v4c-4 0-8 0-11 1l-2 1h-3c0 1 1 1 1 2h0c-1 0-1 1-1 1-3 1-6 2-10 2l-1 1c-2 1-5 2-7 3 0 1 0 1 1 2h2c0 1 0 1 1 2v1c4 1 9 2 13 2h4c-1 1-4 2-6 1h-1-3l-3 2c-2-2-6-2-9-3-8-3-16-7-23-12l-7-7c-1-1-2-2-3-2l-1-1h-1c-1-2-1-3-2-4h0l2 2h1c-1-1-1-3-2-4-2-3-5-1-8-2l3-1h1v-2l-3-11v-1l-1-4c0-5 0-10 1-16 0-2 2-9 2-11h-2l-1-1h2l2-1-1-1 1-1c1-1 1-1 2-3 0-2 2-5 3-7 1-1 1-3 2-4l3-3c1-1 1-2 2-2l2-2c0-2-1-3-2-4v-1l1-1 1-1c0-2 1-3 2-5h1v2h1v-2c1 0 1 0 2-1h1l2-1v1h4 0v1c2-1 3-1 4-3 0-2 0-2 2-3 1-1 2-1 4-1v1h1 1c0 1 1 1 2 2-1 0-3 2-4 3l-2 3 1 1c2-1 2-4 4-4l-1 2h7c2-1 5-2 7-2l9-3c2-1 4-1 7-1h1 1c3 0 3 0 5-2h13 1l1 1v1c4 1 7 3 11 6 1 2 3 2 5 3 1-2 2-2 4-2v-1h-3v-1h2l1-1h-3l-1-1h3z" class="W"></path><path d="M620 487c2 1 5 4 6 7-1-1-2-1-3-2h-1l1 1c-3 0-2-2-4-3-2 0-3-1-4-2h3c1 0 1 0 2-1z" class="h"></path><path d="M645 517c4 2 5 4 8 6l3 1s1 0 2 1h2l-1 2c1 0 2 1 3 1l-1 2h1l2-1c1 2 3 3 4 4-2 0-2 0-4-1-1 1-1 4-1 6l-3-1-1-1v-3-3l-2-1 2-2h-1c-4-1-8-4-10-7l-3-3z" class="H"></path><path d="M615 494h1c1 2 2 4 4 5 0-1-1-1-1-2v-1c2 1 2 3 4 4 0-1-1-2-1-4 1 1 5 3 5 4l-1 1v3c1 1 1 1 0 2 0 0-1 0-1-1h-2-1c1 4 4 8 4 12-1-1-3-3-3-5-1-1-1-1-1-2l-1-1c-2-5-3-10-6-15z" class="B"></path><path d="M620 487c-2-1-3-2-4-2l-1-1c3 0 5 2 8 3 1 2 3 3 4 4l16 20 1-1c2 2 2 3 5 3 2 1 3 2 4 4 0 1 2 2 3 2v1h-1v1h-3l6 3c1 0 2 0 2 1h0-2c-1-1-2-1-2-1l-3-1c-3-2-4-4-8-6-5-5-9-11-13-16-2-3-3-5-6-7-1-3-4-6-6-7z" class="Z"></path><path d="M644 510c2 2 2 3 5 3 2 1 3 2 4 4 0 1 2 2 3 2v1h-1v1h-3c-4-3-6-7-9-10l1-1z" class="G"></path><path d="M626 517c0-4-3-8-4-12h1 2c0 1 1 1 1 1 1-1 1-1 0-2v-3l1-1c6 6 11 14 17 20 2 2 4 5 7 7h0c-1 1-1 0-1 2 0 1-1 1-2 2v1 3c-1 0-2-1-3-2l-1-1h-2c1 1 1 3 1 4h-1-1c0-1 0-2-1-3h-1c-1-1-2-1-3-1l-1-1-1-3c-2-4-5-7-8-11z" class="o"></path><path d="M644 520c2 2 4 5 7 7h0c-1 1-1 0-1 2 0 1-1 1-2 2v1 3c-1 0-2-1-3-2l-1-1c2 0 1 0 2 1h1v-1c-1-1-1-2 0-3v-1-1l1 1 1-1c0-1-2-1-3-2s-2-3-2-5h0z" class="k"></path><path d="M657 529l2 1v3 3l1 1c1 2 1 4 1 6l-1 1h2l1 1h-2l-1 1v1l-6 1c2 1 2 1 4 1-1 0-3 1-3 2l-1 2c-2 1-5 2-7 3-1 0-1-1-2-2v-1h-1c-2 0-4 0-6-1h-1l-1-1c-2 0-2 0-3-1l-1-1c-3-2-5-4-8-7 0-1-1-2-1-3s0-1 1-2v1h1v-1l1-1h3c2-1 4-2 5-3l1-2 1 1c1 0 2 0 3 1h1c1 1 1 2 1 3h1 1c0-1 0-3-1-4h2l1 1c1 1 2 2 3 2v-3h0c4 0 6-1 9-3z" class="N"></path><path d="M643 539c1 1 3 3 4 3l-1 2h-2c0-1-1-3-1-5z" class="g"></path><path d="M649 537h0c0 2 0 4-1 6v1h1l1 1 1-1 2 2c-3 1-5 1-7 1l-2-2v-1h2l1-2 2-5z" class="X"></path><path d="M648 532h0c1 1 1 2 2 3-1 1-1 1-1 2l-2 5c-1 0-3-2-4-3l-1-1v-1h2v-2-1l1-1c1 1 2 2 3 2v-3z" class="Y"></path><path d="M657 529l2 1v3 3l1 1c1 2 1 4 1 6-1-1-2-1-2-1h-2c-1 1-2 2-3 1h0l-3 1-1 1-1-1h-1v-1c1-2 1-4 1-6h0c0-1 0-1 1-2-1-1-1-2-2-3 4 0 6-1 9-3z" class="F"></path><path d="M657 534c1 0 1-1 2-1v3c0 3-1 4-2 5v-1-6z" class="C"></path><path d="M659 536l1 1c1 2 1 4 1 6-1-1-2-1-2-1h-2v-1c1-1 2-2 2-5z" class="I"></path><path d="M657 529l2 1v3c-1 0-1 1-2 1-3 1-6 1-8 3h0c0-1 0-1 1-2-1-1-1-2-2-3 4 0 6-1 9-3z" class="R"></path><path d="M624 542c0-1-1-2-1-3s0-1 1-2v1h1v1c1 0 1 1 1 1 0 1 1 2 2 2l1 1 1 1 1-1v1c1 1 2 1 3 2l9 3c1 1 4 0 6-1 3 0 7-1 11-2v1l-6 1c2 1 2 1 4 1-1 0-3 1-3 2l-1 2c-2 1-5 2-7 3-1 0-1-1-2-2v-1h-1c-2 0-4 0-6-1h-1l-1-1c-2 0-2 0-3-1l-1-1c-3-2-5-4-8-7z" class="F"></path><path d="M637 552v-1c5-1 12-2 17-3 2 1 2 1 4 1-1 0-3 1-3 2l-1 2c-2 1-5 2-7 3-1 0-1-1-2-2v-1h-1c-2 0-4 0-6-1h-1z" class="J"></path><path d="M655 551l-1 2c-2 1-5 2-7 3-1 0-1-1-2-2v-1h-1 0c3-1 7-1 11-2z" class="c"></path><path d="M635 531l1 1c1 0 2 0 3 1h1c1 1 1 2 1 3h1 1c0-1 0-3-1-4h2l1 1-1 1v1 2h-2v1h-1v2 1l-2-1-1 1c1 1 3 2 3 4v1h-7c-1-1-2-1-3-2v-1l-1 1-1-1-1-1c-1 0-2-1-2-2 0 0 0-1-1-1v-1-1l1-1h3c2-1 4-2 5-3l1-2z" class="E"></path><path d="M626 536h3 1c0 2-1 2-1 3h-1l-2-3z" class="B"></path><path d="M635 531l1 1c1 0 2 0 3 1h1c1 1 1 2 1 3h1 1c0-1 0-3-1-4h2l1 1-1 1v1 2h-2l-2-1-1 2c-2-1-4 1-6 1l-1-1v-1c1-1 1-1 2-1h0-1c1-1 1-1 1-2v-1l1-2z" class="H"></path><path d="M660 509l1 1h1 0c0 2 0 4 1 6h3l6 3c3 2 5 3 7 5h1l2 2 2 1 2 3c1 1 2 1 2 2 1-2 2-3 3-5l1 1-2 3 1 1 2-3h1c-1 3-5 5-6 8-2 2-5 3-7 4v4c-4 0-8 0-11 1l-2 1h-3c0 1 1 1 1 2h0c-1 0-1 1-1 1-3 1-6 2-10 2l-1 1 1-2c0-1 2-2 3-2-2 0-2 0-4-1l6-1v-1l1-1h2l-1-1h-2l1-1c0-2 0-4-1-6l3 1c0-2 0-5 1-6 2 1 2 1 4 1-1-1-3-2-4-4l-2 1h-1l1-2c-1 0-2-1-3-1l1-2h0c0-1-1-1-2-1l-6-3h3v-1h1v-1c1-2 1-2 3-3v-2c0-2 0-3 1-5z" class="W"></path><path d="M660 537l3 1v3l-1 1-1 1c0-2 0-4-1-6z" class="D"></path><path d="M665 519c1 1 2 1 3 1 1 1 1 2 2 2l1 1c2 1 4 1 5 4v2l-3-3c-2-2-4-3-7-3-1 0-2 0-3 1-1 0-2 1-3 1 0-1-1-1-2-1 2-1 3-1 4-2v-2c1 0 2 0 3-1z" class="I"></path><path d="M680 524l2 2h0-2v2c0 1 2 1 2 2 0 2-1 4-2 5l1 2c-5 3-10 5-16 7l1-2c2 0 6-1 8-3l3-3c4-4 1-7 2-11v-1h1z" class="X"></path><path d="M681 537l-1-2c1-1 2-3 2-5 0-1-2-1-2-2v-2h2 0l2 1 2 3c1 1 2 1 2 2-2 2-5 4-7 5z" class="i"></path><path d="M660 509l1 1h1 0c0 2 0 4 1 6 0 1 1 2 2 3-1 1-2 1-3 1v2c-1 1-2 1-4 2l-6-3h3v-1h1v-1c1-2 1-2 3-3v-2c0-2 0-3 1-5z" class="O"></path><path d="M661 510h1 0c0 2 0 4 1 6 0 1 1 2 2 3-1 1-2 1-3 1h-1c-2-4-1-7 0-10z" class="Y"></path><path d="M666 523c3 0 5 1 7 3l3 3c0 2-2 5-3 7-1 1-1 1-3 1l-2-4c-1-1-3-2-4-4l-2 1h-1l1-2c-1 0-2-1-3-1l1-2h0c1 0 2-1 3-1 1-1 2-1 3-1z" class="Z"></path><path d="M660 525c1 0 2-1 3-1 1-1 2-1 3-1 1 1 1 1 2 1 1 1 2 1 2 2 1 1 1 2 1 3v3c-2-2-5-4-8-4h-1c-1 0-2-1-3-1l1-2h0z" class="o"></path><path d="M691 527l1 1-2 3 1 1 2-3h1c-1 3-5 5-6 8-2 2-5 3-7 4v4c-4 0-8 0-11 1l-2 1h-3c0 1 1 1 1 2h0c-1 0-1 1-1 1-3 1-6 2-10 2l-1 1 1-2c0-1 2-2 3-2-2 0-2 0-4-1l6-1c2-1 4-2 5-3 6-2 11-4 16-7 2-1 5-3 7-5 1-2 2-3 3-5z" class="S"></path><path d="M681 541v4c-4 0-8 0-11 1l-2 1v-2c5-1 9-2 13-4z" class="t"></path><path d="M658 549c3-2 7-3 10-4v2h-3c0 1 1 1 1 2h0c-1 0-1 1-1 1-3 1-6 2-10 2l-1 1 1-2c0-1 2-2 3-2z" class="M"></path><path d="M611 490c2 1 3 2 4 4h0c3 5 4 10 6 15l1 1c0 1 0 1 1 2 0 2 2 4 3 5 3 4 6 7 8 11l1 3-1 2c-1 1-3 2-5 3h-3l-1 1v1h-1v-1c-1 1-1 1-1 2s1 2 1 3h-1l-4-2c-4-5-7-10-9-15l-2-5c-1-8-2-14-1-22v-1l2 2 1-1 1-2v-1l-1-2h0c0-2 0-2 1-3z" class="D"></path><path d="M621 509l1 1c0 1 0 1 1 2 0 2 2 4 3 5 3 4 6 7 8 11h0-1c-1-6-10-10-13-16-1-1-1-2-1-3h2z" class="M"></path><path d="M614 519l1 1v2c1 2 1 3 2 4h1v-1c-1-1-1-1-1-3 1 0 1 2 3 3l-1 1 1 1h0-1 0c1 1 1 2 3 2l-1 1h-1 0c1 1 5 1 6 1h1l1-1h1 0l-1-2 1-1c1 1 0 1 1 2l1-1c0-2-1-3-2-4h0c2 2 3 3 3 5 1 0 1 0 1-1h1c0 2 0 2-1 3-3 2-8 3-12 3l-4-1c0-3-3-8-4-11l1-3z" class="W"></path><path d="M611 490c2 1 3 2 4 4h0c3 5 4 10 6 15h-2c0 1 0 2 1 3h-1v1c1 2 4 4 5 6l-8-4c-2-1-2-1-4-1 0 1 1 1 1 2 1 0 1 3 1 3l-1 3c-1-3-1-5-2-7s-2-4-2-6h1c-2-2 0-5 0-8h-1v-1h2l-1-1h-1l1-1 1-2v-1l-1-2h0c0-2 0-2 1-3z" class="N"></path><path d="M611 490c2 1 3 2 4 4h0c3 5 4 10 6 15h-2v-2c-1 0-1-1-2-2v-2c-2-3-3-7-6-9 0-1 0-1-1-1h0c0-2 0-2 1-3z" class="P"></path><path d="M610 499h1v1 2h1v4c0 2 0 2 1 3h0-2v1c1 1 0 1 1 2l3 1h2c1 0 1 0 1-1h1v1c1 2 4 4 5 6l-8-4c-2-1-2-1-4-1 0 1 1 1 1 2 1 0 1 3 1 3l-1 3c-1-3-1-5-2-7s-2-4-2-6h1c-2-2 0-5 0-8h-1v-1h2l-1-1z" class="h"></path><path d="M607 497l2 2h1l1 1h-2v1h1c0 3-2 6 0 8h-1c0 2 1 4 2 6s1 4 2 7 4 8 4 11l4 1c4 0 9-1 12-3 1-1 1-1 1-3h0l1 3-1 2c-1 1-3 2-5 3h-3l-1 1v1h-1v-1c-1 1-1 1-1 2s1 2 1 3h-1l-4-2c-4-5-7-10-9-15l-2-5c-1-8-2-14-1-22v-1z" class="V"></path><path d="M617 533l4 1v4l1 1-1 1c-1-1-2-3-3-4v-1c-1-1-1 0-1-2z" class="u"></path><path d="M639 444c1-1 2-1 4-1v1h1 1c0 1 1 1 2 2-1 0-3 2-4 3l-2 3 1 1c2-1 2-4 4-4l-1 2h7c2-1 5-2 7-2l9-3c2-1 4-1 7-1-1 1-1 2-1 3l-1 2-1 4c1 1 1 1 3 1h0c1 0 2 0 3 1v1 1h-1c0 1 0 1 1 2h-1 0c-1 1-2 1-3 1v1l-1 1-4 2-1 2h0 1 2 2c0 1-1 2-2 4-1 0-2-1-3-1l-4 1v1l-4 1-3 3c-2 1-4 1-6 1h0-1l-9 3c-1 1-2 1-3 1v3c-3-1-1-2-3-4v1c-2 2-5 2-5 4l-1 1h0v1 2l-2 2c-1-1-3-2-4-4-3-1-5-3-8-3l1 1c1 0 2 1 4 2-1 1-1 1-2 1h-3l-1-1c-1 1-2 1-2 2l-2 1h1c-1 1-1 1-1 3h0l1 2v1l-1 2-1 1-2-2v1c-1 8 0 14 1 22l2 5c2 5 5 10 9 15l4 2h1c3 3 5 5 8 7l1 1c1 1 1 1 3 1l1 1h1c2 1 4 1 6 1h1v1c1 1 1 2 2 2 0 1 0 1 1 2h2c0 1 0 1 1 2v1c4 1 9 2 13 2h4c-1 1-4 2-6 1h-1-3l-3 2c-2-2-6-2-9-3-8-3-16-7-23-12l-7-7c-1-1-2-2-3-2l-1-1h-1c-1-2-1-3-2-4h0l2 2h1c-1-1-1-3-2-4-2-3-5-1-8-2l3-1h1v-2l-3-11v-1l-1-4c0-5 0-10 1-16 0-2 2-9 2-11h-2l-1-1h2l2-1-1-1 1-1c1-1 1-1 2-3 0-2 2-5 3-7 1-1 1-3 2-4l3-3c1-1 1-2 2-2l2-2c0-2-1-3-2-4v-1l1-1 1-1c0-2 1-3 2-5h1v2h1v-2c1 0 1 0 2-1h1l2-1v1h4 0v1c2-1 3-1 4-3 0-2 0-2 2-3z" class="F"></path><path d="M639 444c1-1 2-1 4-1v1l-6 3c0-2 0-2 2-3z" class="d"></path><path d="M608 520l2 5-1 3v1l-3-8 2-1z" class="R"></path><path d="M619 456l1-1c0-2 1-3 2-5h1v2h1 4c1 0 3-2 4-1-1 1-3 1-4 2l-9 3z" class="J"></path><path d="M617 469l1 2-1 2-5 7-1-1c0-3 4-7 6-10z" class="D"></path><path d="M619 456l9-3c-3 3-6 6-8 9 0-2-1-3-2-4v-1l1-1z" class="W"></path><path d="M605 497c0 1 0 1 1 2l1-1c-1 8 0 14 1 22l-2 1c-2-8-2-15-1-24z" class="E"></path><path d="M611 479l1 1c-1 4-2 7-3 10h1 1c-1 1-1 1-1 3h0l1 2v1l-1 2-1 1-2-2v1l-1 1c-1-1-1-1-1-2 1-6 3-12 6-18z" class="I"></path><path d="M609 490h1 1c-1 1-1 1-1 3h0l1 2v1l-1 2-1 1-2-2 2-7z" class="q"></path><path d="M644 444h1c0 1 1 1 2 2-1 0-3 2-4 3-2 0-3 0-4 2-2 1-4 2-6 4-6 4-10 10-15 16l-1-2c3-4 8-10 12-13 3-2 5-4 8-6 2-2 5-4 7-6z" class="l"></path><path d="M617 473c0 3-2 4-2 8l-1 1h1l1-1 3-1h1c1 1 2 1 3 1 1 2 1 3 1 4l-1 2c-3-1-5-3-8-3l1 1c1 0 2 1 4 2-1 1-1 1-2 1h-3l-1-1c-1 1-2 1-2 2l-2 1h-1c1-3 2-6 3-10l5-7z" class="L"></path><path d="M618 471c5-6 9-12 15-16 2-2 4-3 6-4 1-2 2-2 4-2l-2 3 1 1c-1 1-2 1-2 2-1 1-2 1-3 2-1 0-2 1-3 2l-2-1h-2v3c-2 1-3 2-5 4l-1 1c-1 1-1 2-2 3l-1 3c-2 2-5 6-5 9l-1 1h-1l1-1c0-4 2-5 2-8l1-2z" class="F"></path><path d="M626 552l1-1 1 1c1 1 1 1 2 1l1 1h0l2 1c3 2 4 2 6 2l-1-1c0-1 0-1 1-1l-2-2 1-1c2 1 4 1 6 1h1v1c1 1 1 2 2 2 0 1 0 1 1 2h2c0 1 0 1 1 2v1c4 1 9 2 13 2h4c-1 1-4 2-6 1h-1-3c-2 0-5 0-8-1h0c-8-2-18-6-24-11z" class="E"></path><path d="M638 552c2 1 4 1 6 1h1v1c1 1 1 2 2 2 0 1 0 1 1 2h2c0 1 0 1 1 2v1l-12-4-1-1c0-1 0-1 1-1l-2-2 1-1z" class="r"></path><path d="M610 525c2 5 5 10 9 15l4 2h1c3 3 5 5 8 7l1 1c1 1 1 1 3 1l1 1h1l-1 1 2 2c-1 0-1 0-1 1l1 1c-2 0-3 0-6-2l-2-1h0l-1-1c-1 0-1 0-2-1l-1-1-1 1h0c-7-4-14-15-17-23v-1l1-3z" class="C"></path><path d="M619 540l4 2h1c3 3 5 5 8 7l1 1c1 1 1 1 3 1l1 1h1l-1 1 2 2c-1 0-1 0-1 1-2-1-5-2-7-4a34.47 34.47 0 0 1-12-12z" class="O"></path><path d="M621 472c3-2 3-2 6-2l2 2c0 2 1 2 0 4l1 2h1c1 0 3 1 4 2v1c-2 2-5 2-5 4l-1 1h0v1 2l-2 2c-1-1-3-2-4-4l1-2c0-1 0-2-1-4-1 0-2 0-3-1h-1l-3 1c0-3 3-7 5-9z" class="i"></path><path d="M621 472c3-2 3-2 6-2l2 2c0 2 1 2 0 4h-1c-2 1-3 2-5 3h0-2c-1 0-1 0-2 1l-3 1c0-3 3-7 5-9z" class="I"></path><path d="M659 449l9-3c2-1 4-1 7-1-1 1-1 2-1 3l-1 2-1 4c1 1 1 1 3 1h0c1 0 2 0 3 1v1 1h-1c0 1 0 1 1 2h-1 0c-1 1-2 1-3 1v1l-1 1-4 2-1 2h0 1 2 2c0 1-1 2-2 4-1 0-2-1-3-1l-4 1v1l-4 1-3 3c-2 1-4 1-6 1h0-1l-9 3c-1 1-2 1-3 1v3c-3-1-1-2-3-4-1-1-3-2-4-2h-1l-1-2c1-2 0-2 0-4l-2-2c-3 0-3 0-6 2l1-3c1-1 1-2 2-3l1-1c2-2 3-3 5-4v-3h2l2 1c1-1 2-2 3-2 1-1 2-1 3-2 0-1 1-1 2-2 2-1 2-4 4-4l-1 2h7c2-1 5-2 7-2z" class="M"></path><path d="M667 451h5l1-1-1 4h-1c-2 0-3 0-5-1h-2c-1 1-1 1-2 1l1-1 1-1h-1c1-1 1-1 2-1l1 1 1-1z" class="N"></path><path d="M642 453c2-1 2-4 4-4l-1 2c0 1 0 3 1 3h1v1c-1 0-2 0-2 1-1 0-2 1-2 2l-1-1s0-1-1-1l-1-1c0-1 1-1 2-2z" class="V"></path><path d="M668 464v-1l3-1h0l3-2 1-1h-2l1-1h0c-1-1-3-1-4-1l-1-1 2-1h4c1 0 2 0 3 1v1 1h-1c0 1 0 1 1 2h-1 0c-1 1-2 1-3 1v1l-1 1-4 2-1-1z" class="N"></path><path d="M661 457l2 2h4v4c-1 1-2 2-3 2-2 1-3 3-4 4-2 0-3 0-4 1v-3c2 0 4 0 6-1-1-2-2-3-2-5v-1l1-3z" class="X"></path><path d="M644 460h0c4 0 7-1 10-2 2-1 5-2 7-3l1-1c0 1-1 2-1 3l-1 3c-1 1-3 1-4 1l-5 1-2 1-1-1-1 1-2-1c0-1-1-2-1-2z" class="N"></path><path d="M649 463l2-1 5-1c1 0 3 0 4-1v1c0 2 1 3 2 5-2 1-4 1-6 1v3h-1v-2l-1-1h-4c-2 1-3 1-4 1l-1-1h4c1 0 1-1 2-1h1c-1-1-1-2-3-3z" class="G"></path><path d="M668 464l1 1-1 2h0 1 2 2c0 1-1 2-2 4-1 0-2-1-3-1l-4 1v1l-4 1-3 3c-2 1-4 1-6 1 1-2 1-2 3-2l-1-2 3-1c2 0 4-1 5-2 2-2 5-4 7-6z" class="Y"></path><path d="M659 449l9-3c2-1 4-1 7-1-1 1-1 2-1 3l-1 2-1 1h-5l-1 1-1-1c-1 0-1 0-2 1h0c-2 2-3 2-5 3h-2v1c-2 0-3 1-5 1l-1-2 1-1h0-4-1c-1 0-1-2-1-3h7c2-1 5-2 7-2z" class="N"></path><path d="M659 449l9-3c2-1 4-1 7-1-1 1-1 2-1 3l-1 2-1 1h-5l-1 1-1-1c-1 0-1 0-2 1h0-3l-4 1-1-1c1 0 2-1 2-1 2 0 3 0 4-1l-2-1z" class="J"></path><path d="M667 451c0-1 1-2 1-3h3c1-1 1-1 2-1l1 1-1 2-1 1h-5z" class="C"></path><path d="M640 455l1 1c0 1 0 1 1 2l-1 1c1 0 2 1 3 1 0 0 1 1 1 2l2 1 1-1 1 1c2 1 2 2 3 3h-1c-1 0-1 1-2 1h-4l1 1c1 0 2 0 4-1h4l1 1v2h1c1-1 2-1 4-1-2 1-8 3-9 6l-1 2c-3 1-7 2-9 3-1 1-2 1-3 1v3c-3-1-1-2-3-4-1-1-3-2-4-2h-1l-1-2c1-2 0-2 0-4l-2-2c-3 0-3 0-6 2l1-3c1-1 1-2 2-3l1-1c2-2 3-3 5-4v-3h2l2 1c1-1 2-2 3-2 1-1 2-1 3-2z" class="a"></path><path d="M624 466c0 2 0 2 2 2h0 1v2h0c-3 0-3 0-6 2l1-3c1-1 1-2 2-3z" class="P"></path><path d="M631 466h4 1l-1 2h-1v2h-3-1c0-1-1-2-1-3h2v-1z" class="D"></path><path d="M636 466c0-1 1-1 2-2v1l1 1c1-1 3-1 5-1h2 1v1h-1c-5 0-8 3-12 4v-2h1l1-2z" class="N"></path><path d="M655 468v2c-5 3-11 5-16 7-1 1-2 1-3 1v-2c0-1 1-1 2-2 2-2 8-4 11-5h2l1-1c1 1 1 1 2 1l1-1z" class="M"></path><path d="M640 455l1 1c0 1 0 1 1 2l-1 1c1 0 2 1 3 1 0 0 1 1 1 2l-1 3c-2 0-4 0-5 1l-1-1v-1c-1 1-2 1-2 2h-1-4c0-2 1-2 2-3-1 0-1-1-1-1-1-1 0-2 0-2h2v-1c1-1 2-2 3-2 1-1 2-1 3-2z" class="C"></path><path d="M635 461c1-1 2-2 4-2l1 2c-1 1-2 1-3 2h-1v-1l-1-1z" class="R"></path><path d="M640 455l1 1c0 1 0 1 1 2l-1 1h-2c-2 0-3 1-4 2l-2 2c-1 0-1-1-1-1-1-1 0-2 0-2h2v-1c1-1 2-2 3-2 1-1 2-1 3-2zm16 15c1-1 2-1 4-1-2 1-8 3-9 6l-1 2c-3 1-7 2-9 3-1 1-2 1-3 1v3c-3-1-1-2-3-4-1-1-3-2-4-2h-1l-1-2c1-2 0-2 0-4h3 0l-1 2v1h1l1 1h3 0v2c1 0 2 0 3-1 5-2 11-4 16-7h1z" class="L"></path><path d="M638 481v-2c5-1 9-4 13-4l-1 2c-3 1-7 2-9 3-1 1-2 1-3 1z" class="P"></path><path d="M716 448c1 0 1-1 1-1v-2h0v-1c0-1 0-2 1-3 0 1 0 1 1 1h0l1 2h0v2 1h3 0c4 16 4 32-1 47-1 1-1 2-2 3l1 1-2 5c-2 2-3 4-5 5-3 1-6 3-9 3v1h2l-1 1-1 1c-1 0-2-1-2-2l-1-1c0 2 0 4-1 5-2 9-5 15-12 21h-1c1-3 5-5 6-8h-1l-2 3-1-1 2-3-1-1c-1 2-2 3-3 5 0-1-1-1-2-2l-2-3-2-1-2-2h-1c-2-2-4-3-7-5l-6-3h-3c-1-2-1-4-1-6h0-1l-1-1c-1 2-1 3-1 5v2c-2 1-2 1-3 3-1 0-3-1-3-2-1-2-2-3-4-4-3 0-3-1-5-3l-1 1-16-20 2-2v-2-1h0l1-1c0-2 3-2 5-4v-1c2 2 0 3 3 4v-3c1 0 2 0 3-1 2-1 6-2 9-3h1 0c2 0 4 0 6-1l3-3 4-1v-1l4-1c1 0 2 1 3 1 1-2 2-3 2-4h-2-2-1 0l1-2 4-2 1-1v-1c1 0 2 0 3-1h0 1c-1-1-1-1-1-2h1v-1-1c-1-1-2-1-3-1h0c-2 0-2 0-3-1l1-4 1-2c0-1 0-2 1-3h1 1c3 0 3 0 5-2h13 1l1 1v1c4 1 7 3 11 6 1 2 3 2 5 3 1-2 2-2 4-2v-1h-3v-1h2l1-1h-3l-1-1h3z" class="n"></path><path d="M701 468c2 5 5 11 4 16-1 1-1 1 0 2l-2-2h0c0-1-1 0 0-1v-1l-1-1 1-2c-1-2-1-2-2-3 1-1 1-2 1-3l-2-2c0-2 0-2 1-3z" class="E"></path><path d="M696 462c-1-1-1-2-1-4h1c2 3 4 7 5 10-1 1-1 1-1 3l2 2c0 1 0 2-1 3l-3-2v-1-1-8l-2-2z" class="l"></path><path d="M694 480c2 1 3 1 5 0l3 1 1 1v1c-1 1 0 0 0 1h0l2 2c-1 1-1 2-2 3l-1-1c-2-1-3 0-5-2v-1h2l2 2 1-1c-1-1-2-2-3-2-1-1-2 0-3 1l-1-2v-1h-4c1-1 1-1 3-2z" class="S"></path><path d="M709 496h1v2h0c2 1 2 2 4 3 1 1 1 2 2 3 2-2 1-3 2-4 0-1 2-2 2-3l1 1-2 5c-2 2-3 4-5 5h-1l-2-1v-3l1-1h-1c-1-1-1-2-2-3l-1-1v-1h1v-2z" class="m"></path><path d="M711 504h3v2l-1 2-2-1v-3z" class="U"></path><path d="M696 462l2 2v8 1 1l3 2c1 1 1 1 2 3l-1 2-3-1c-2 1-3 1-5 0-2 1-2 1-3 2l-2 1c0 1 1 1 1 2-1 1-1 1-2 1-1-2-4-3-6-5 0-1-2-1-3-2h1c1 0 2 0 4 1h-1l1 1h0 1 1l2-1-2-1h-1 0l1-1c3-1 5-2 7-4 0-1 0-1 1-1 2-3 3-7 2-11z" class="W"></path><path d="M694 480c2-1 3-2 5-2 1 0 1 0 2 1h0 2l-1 2-3-1c-2 1-3 1-5 0z" class="J"></path><path d="M696 462l2 2v8c-2 2-4 5-7 6l-3 1v1l-2-1h-1 0l1-1c3-1 5-2 7-4 0-1 0-1 1-1 2-3 3-7 2-11z" class="e"></path><path d="M691 482h4v1l1 2 1 3c2 1 3 1 4 1v1h-2v1h1l-2 2h-1v1l7-2c2 4 3 7 4 11l-3 1v1l1 1-1 1v2h-2-1v-2h0v-2l-1 1h-1l-3-8c-1-5-5-9-7-13 0-1-1-1-1-2l2-1z" class="T"></path><path d="M701 506v-2l2-2-1-1v-1c0-2-1-3-2-4 1-2 1-2 2-2 1 1 1 2 2 4h0v1h-1l3 3-1 2v1l1 1-1 1v2h-2-1v-2h0v-2l-1 1z" class="o"></path><path d="M673 467c1 0 2-1 3 0l-1 1 1 1-1 1c-1 1-1 2-1 3 0 2 0 2 2 3l3 1c2 2 5 1 7 1l-1 1h0 1l2 1-2 1h-1-1 0l-1-1h1c-2-1-3-1-4-1h-1c1 1 3 1 3 2 2 2 5 3 6 5 1 0 1 0 2-1 2 4 6 8 7 13l3 8h1l1-1v2h0v2h1 2v-2l1-1-1-1v-1l3-1 1 1h-2c1 1 2 1 1 2s0 1-1 2c0 1-1 2-2 2v1 1h2l-1 1-1 1c-1 0-2-1-2-2l-1-1c0 2 0 4-1 5h-1c-1 4-2 7-4 10h-1v-1l2-4h-1l-2 2h0l3-6v-5-1l1 1c1-3-1-6-2-8-1-1 0-2-1-4s-3-3-6-4h-7c-1 0-2 1-4 2h-3l3-3h0c-1-1-1-1-1-2h2c0-1 0-1-1-1l-1-1h2v-1c-2-1-4-2-6-4l5 2s1 1 2 1l1-1-5-2-2-1h-1c-1 0-1-1-2-1-1-1-2-1-3-2 1-1 1 0 1-1l-2-3v-1l1 1 1-1c-3-2-3-2-6-2h0 1l1-1h1c2-1 3-1 5-1v-2c1-2 2-3 2-4z" class="j"></path><path d="M678 495c2-1 4-1 7-1 2 0 2 0 4 1h-1-7c1 1 0 1 1 1-1 0-2 1-4 2h-3l3-3z" class="J"></path><path d="M674 485h-1c-1 0-1-1-2-1-1-1-2-1-3-2 1-1 1 0 1-1l-2-3v-1l1 1 1-1c-3-2-3-2-6-2h0 1l1-1h1c2-1 3-1 5-1 0 3 1 5 4 7l4 3c-1 1-2-1-4 0 2 1 5 2 7 4 0 1 1 1 2 2-1 0-2-1-3-2-2-1-5-3-7-2z" class="B"></path><path d="M682 496h7c3 1 5 2 6 4s0 3 1 4c1 2 3 5 2 8l-1-1v1 5l-3 6h0l2-2h1l-2 4v1h1c2-3 3-6 4-10h1c-2 9-5 15-12 21h-1c1-3 5-5 6-8h-1l-2 3-1-1 2-3-1-1c-1 2-2 3-3 5 0-1-1-1-2-2l-2-3-2-1-2-2h-1c-2-2-4-3-7-5h1l1 1 1-1h0c1-1 1-1 1-2-1 0-1 0-1 1-2-1-3-1-4-2 0-1 0-1 1-1l-1-2c0-1 0-2-1-3 0-1-1-2-1-2l1-1h1l-1-1h2v-3c0-1 0-2 1-3h1l1-2h3c2-1 3-2 4-2z" class="C"></path><path d="M688 498h2l1 1c1 0 2 0 3 1v1h-2l-2-1-1 1h-1v-1h1l-1-2z" class="D"></path><path d="M688 505v-1h1c1 1 2 1 3 3l3 3h0-2v1l1 4c0 2-1 3-2 4l-1-5c-1-3-3-5-6-7l-1-1h-3l1-1h1c3 1 5 2 7 5h2 1l-5-5z" class="B"></path><path d="M674 500l2-1c1 1 1 1 0 2h1v2h1 1l-1 1h1c1-1 1-1 2 0h1c1 0 3 0 4 1h2 0l5 5h-1-2c-2-3-4-4-7-5h-1l-1 1h3c-4 1-5 1-8 3l-1 1c0 1-2 3-3 3 1-1 0-1 1-1l1-2c0-1-1-2-1-2-1 0-2 0-3-1h1l-1-1h2v-3c0-1 0-2 1-3h1z" class="d"></path><path d="M672 503h1c2 1 2 1 4 1v1c1 0 2-1 3 0-1 1-2 1-2 2-1 1-2 1-2 2l-1 1c0 1-2 3-3 3 1-1 0-1 1-1l1-2c0-1-1-2-1-2-1 0-2 0-3-1h1l-1-1h2v-3z" class="M"></path><path d="M676 509c3-2 4-2 8-3l1 1c3 2 5 4 6 7l-2 1c-1 0-1 0-2-1l-1-1h0l1-2-1-1-2 1c-1 0-2 0-3 1l1 2c-1 0-3 1-4 2l-1 3v-1-1h-1c-1 0-1 0-1 1-2-1-3-1-4-2 0-1 0-1 1-1l-1-2c0-1 0-2-1-3 0-1-1-2-1-2l1-1c1 1 2 1 3 1 0 0 1 1 1 2l-1 2c-1 0 0 0-1 1 1 0 3-2 3-3l1-1z" class="P"></path><path d="M676 509c3-2 4-2 8-3l1 1c-1 1-2 2-3 2l-1-1c-1 1-2 1-3 2h-3l1-1z" class="Q"></path><path d="M682 514l-1-2c1-1 2-1 3-1l2-1 1 1-1 2h0l1 1c1 1 1 1 2 1l2-1 1 5v4l-1 4c-1 2-2 3-3 5 0-1-1-1-2-2l-2-3-2-1-2-2h-1c-2-2-4-3-7-5h1l1 1 1-1h0c1-1 1-1 1-2h1v1 1l1-3c1-1 3-2 4-2z" class="e"></path><path d="M682 514c1-1 1-1 2-1v1l1 2c0 2 0 2-2 3h2v1c-1 0-1 0-2-1l-2 1h-1c-1-1-1-2-2-4 1-1 3-2 4-2z" class="F"></path><path d="M682 514c1-1 1-1 2-1v1c-1 1-3 2-4 3 0 1 1 2 1 2v1h-1c-1-1-1-2-2-4 1-1 3-2 4-2z" class="i"></path><path d="M676 517h1v1 1l1-3c1 2 1 3 2 4h1l2-1c1 1 1 1 2 1l2-1h1v1h-1c-1 0-2 1-3 2h0v1c0 2 0 2 1 3l-1 1-2-1-2-2h-1c-2-2-4-3-7-5h1l1 1 1-1h0c1-1 1-1 1-2z" class="O"></path><path d="M680 524c1-1 0-1 1-1h1v-1h2v1c0 2 0 2 1 3l-1 1-2-1-2-2z" class="b"></path><path d="M684 523l1 1c2-1 1 0 2-1h2v-3h1v1c0 1 1 2 2 2h0l-1 4c-1 2-2 3-3 5 0-1-1-1-2-2l-2-3 1-1c-1-1-1-1-1-3z" class="K"></path><path d="M685 526c1 0 2 0 4-1l1 2c-1 1-2 2-4 3l-2-3 1-1z" class="U"></path><path d="M675 445h1 1c3 0 3 0 5-2h13 1l1 1v1c4 1 7 3 11 6l-2 1h0-2c-2-1-4-1-6-2 1 1 2 2 2 3l-2-1c2 5 5 9 7 14h0v2c1 1 1 2 1 3l1 4 1 1v4c1 1 1 4 0 5v1c0 2 0 3-1 4 0 2 1 3-1 5v-4l1-4v-10c-1-1-1-3-2-5l-2-6c-1-4-4-10-7-12v4h-1c0 2 0 3 1 4 1 4 0 8-2 11-1 0-1 0-1 1-2 2-4 3-7 4-2 0-5 1-7-1l-3-1c-2-1-2-1-2-3 0-1 0-2 1-3l1-1-1-1 1-1c-1-1-2 0-3 0h-2-2-1 0l1-2 4-2 1-1v-1c1 0 2 0 3-1h0 1c-1-1-1-1-1-2h1v-1-1c-1-1-2-1-3-1h0c-2 0-2 0-3-1l1-4 1-2c0-1 0-2 1-3z" class="D"></path><path d="M675 455c3-1 4-1 8 0h1c2 1 4 3 6 4 1 2 2 6 2 8h-1-1c-2-1-4-2-7-4h0c0-1-1-2-2-2s0 0-1-1c0-1-1-2-2-2v-1-1c-1-1-2-1-3-1h0z" class="F"></path><path d="M678 456l6 2c2 1 4 3 5 4l-4-1c-2 0-2-1-3-1h-2c0-1-1-2-2-2v-1-1z" class="J"></path><path d="M680 460h2c1 0 1 1 3 1l4 1c1 2 2 3 2 5h-1c-2-1-4-2-7-4h0c0-1-1-2-2-2s0 0-1-1z" class="Y"></path><path d="M673 463l1-1v-1c1 0 2 0 3-1h0 1c-1-1-1-1-1-2h1c1 0 2 1 2 2 1 1 0 1 1 1s2 1 2 2h0c1 1 2 4 4 4 1 0 3 1 4 2v1c-2 1-2 1-3 1s-2-1-3-1h0l1 1v1h-2c-1-1-2-1-3-1l1 1h0c1 0 2 1 3 1v1l-3-1v1c1 1 1 1 2 1h1v1h-1v1h3c1 0 4-3 5-4l1 1c-2 2-4 3-7 4-2 0-5 1-7-1l-3-1c-2-1-2-1-2-3 0-1 0-2 1-3l1-1-1-1 1-1c-1-1-2 0-3 0h-2-2-1 0l1-2 4-2z" class="I"></path><path d="M669 465l4-2c1 0 2 1 2 2l2 1c1 0 0 0 1 1h-2c-1-1-2 0-3 0h-2-2-1 0l1-2z" class="H"></path><path d="M675 445h1 1c3 0 3 0 5-2h13 1l1 1v1c4 1 7 3 11 6l-2 1h0-2c-2-1-4-1-6-2 1 1 2 2 2 3l-2-1c2 5 5 9 7 14h0v2c1 1 1 2 1 3l1 4 1 1v4c1 1 1 4 0 5v1c0 2 0 3-1 4 0 2 1 3-1 5v-4l1-4v-10c-1-1-1-3-2-5l-2-6c-1-4-4-10-7-12-2-2-3-4-5-6-1 0-3 0-5 1 4 3 7 7 8 12 0 3 1 6 0 8 0-4-1-9-3-13v2l-1 1c-2-1-4-3-6-4h-1c-4-1-5-1-8 0-2 0-2 0-3-1l1-4 1-2c0-1 0-2 1-3z" class="e"></path><path d="M684 455h0c-3-2-6-3-8-3v-1c1-2 2-2 5-2s5 1 8 3c0 1 2 3 2 4v2l-1 1c-2-1-4-3-6-4z" class="N"></path><path d="M697 445c4 1 7 3 11 6l-2 1h0-2c-2-1-4-1-6-2 1 1 2 2 2 3l-2-1c2 5 5 9 7 14h0v2c1 1 1 2 1 3l1 4 1 1v4c1 1 1 4 0 5v1c0 2 0 3-1 4 0 2 1 3-1 5v-4l1-4v-10c-1-1-1-3-2-5l-2-6c-1-4-4-10-7-12-2-2-3-4-5-6-1 0-3 0-5 1-3-2-5-2-8-2h-1 0c2-2 4 0 7-2h0c1 0 3 1 4 0 4-1 10 2 15 4h0l-7-4h1z" class="X"></path><path d="M716 448c1 0 1-1 1-1v-2h0v-1c0-1 0-2 1-3 0 1 0 1 1 1h0l1 2h0v2 1h3 0c4 16 4 32-1 47-1 1-1 2-2 3 0 1-2 2-2 3-1 1 0 2-2 4-1-1-1-2-2-3-2-1-2-2-4-3h0v-2h-1-1v1h0c-1 0-2-1-2-2 2-2 1-3 1-5 1-1 1-2 1-4v-1c1-1 1-4 0-5v-4l-1-1-1-4c0-1 0-2-1-3v-2h0c-2-5-5-9-7-14l2 1c0-1-1-2-2-3 2 1 4 1 6 2h2 0l2-1c1 2 3 2 5 3 1-2 2-2 4-2v-1h-3v-1h2l1-1h-3l-1-1h3z" class="U"></path><path d="M700 453c0-1-1-2-2-3 2 1 4 1 6 2h2 0l2-1c1 2 3 2 5 3 1-2 2-2 4-2v1c1 0 2 0 3 1-1 1 0 1-1 1l2 2h-2l2 2h-1c-1 0-2-1-4-1h0l6 3-1 1c-3-1-4-3-7-2h-1l-3-1h-2c-2 0-1 0-2-1s-2-1-3-1c-2-1-3-2-3-4z" class="P"></path><path d="M713 454c1-2 2-2 4-2v1c1 0 2 0 3 1-1 1 0 1-1 1l2 2h-2l2 2h-1c-1 0-2-1-4-1h0l6 3-1 1c-3-1-4-3-7-2h-1l-3-1h-2c-2 0-1 0-2-1 1 0 2 1 3 1s0-1 1 0h5l-1-1c-2 0-4-1-6-1-1 0-1 0-2-1 2 1 4 0 5 1h3v-1l-4-1v-1c1 1 3 1 4 1l-1-1z" class="K"></path><path d="M708 476h0l1 1h2c0-1-1-1-1-2 2 0 4 3 6 2 2 1 3 2 4 3v1l-2-1h-1l2 2h0 0c-1 0-1 1-1 1l-1 1 3 3c-1 0-2-1-3-1h0l1 2-1 1 1 1c0 1 0 1-1 1h-3c1 1 3 2 4 4v1h-1v-1c-1-1-3-2-4-3h-1c1 1 3 3 4 3v1h-3v1c2 0 3 0 4 1h0-2-2c0 2 1 1 1 2v1c-2-1-2-2-4-3h0v-2h-1-1v1h0c-1 0-2-1-2-2 2-2 1-3 1-5 1-1 1-2 1-4v-1c1-1 1-4 0-5v-4z" class="n"></path><path d="M708 486c1 1 2 1 3 2l-1 2c-1 2-1 3-1 4v1c1 1 1 0 1 1h-1-1v1h0c-1 0-2-1-2-2 2-2 1-3 1-5 1-1 1-2 1-4z" class="q"></path><path d="M714 491c-1-1-2-1-2-2l1-1 1-1h-2 1c2-2-2 0 1-1h0c-1-1-1-2-2-3h0c1 0 2 1 3 1 0-1-1-2-2-2l1-1 3 2v-1l-1-1v-1c1 1 2 1 3 2-1 0-1 1-1 1l-1 1 3 3c-1 0-2-1-3-1h0l1 2-1 1 1 1c0 1 0 1-1 1h-3z" class="K"></path><path d="M708 476l-1-1-1-4c0-1 0-2-1-3v-2h0c-2-5-5-9-7-14l2 1c0 2 1 3 3 4 1 0 2 0 3 1s0 1 2 1h2l3 1h1c2 1 5 2 7 3v1l-1-1c-2-1-4-2-6-2v1h1c2 1 5 3 6 4-2 0-2-1-3-2h-1-2 0c0 1 0 1 1 1 1 1 1 1 1 2-1 0-1-1-2-1h-1c2 2 3 2 5 3 1 0 1 1 2 1v1c-1 0-2-1-2-1-1 0-1-1-2-1-2 0-3-1-5-2h0c3 2 6 4 8 6-2-1-5-2-7-4l-1 1c3 1 7 3 9 6-3-1-7-4-9-4 2 2 7 4 10 7l-8-4h0l2 2c-2 1-4-2-6-2 0 1 1 1 1 2h-2l-1-1h0z" class="L"></path><path d="M651 477c2 0 4 0 6-1l3-3 4-1v-1l4-1c1 0 2 1 3 1v2c-2 0-3 0-5 1h-1l-1 1h-1 0c3 0 3 0 6 2l-1 1-1-1v1l2 3c0 1 0 0-1 1 1 1 2 1 3 2 1 0 1 1 2 1h1l2 1 5 2-1 1c-1 0-2-1-2-1l-5-2c2 2 4 3 6 4v1h-2l1 1c1 0 1 0 1 1h-2c0 1 0 1 1 2h0l-3 3-1 2h-1c-1 1-1 2-1 3v3h-2l1 1h-1l-1 1s1 1 1 2c1 1 1 2 1 3l1 2c-1 0-1 0-1 1 1 1 2 1 4 2 0-1 0-1 1-1 0 1 0 1-1 2h0l-1 1-1-1h-1l-6-3h-3c-1-2-1-4-1-6h0-1l-1-1c-1 2-1 3-1 5v2c-2 1-2 1-3 3-1 0-3-1-3-2-1-2-2-3-4-4-3 0-3-1-5-3l-1 1-16-20 2-2v-2-1h0l1-1c0-2 3-2 5-4v-1c2 2 0 3 3 4v-3c1 0 2 0 3-1 2-1 6-2 9-3h1 0z" class="T"></path><path d="M665 490c-2 0-4-2-5-3 0 0 1 1 2 1h1l-2-2h4 0c-1-1 0-1-1-2h2c1 0 2 1 3 2s2 2 2 3h-3v1 1c-1 0-2-1-3-1z" class="B"></path><path d="M638 491h2l1-1-2-1v-1l4 1 2 1h-2-1l1 1 1 1h-2l-1 1c1 0 1 0 2 1-1 0-2-1-3 0h0c-1 1-1 1-2 1 0 1 1 1 1 2l-1 2h0 0c-1-1-2-2-2-3l-2-1v-1c2-1 3-1 4-2h-1l1-1z" class="C"></path><path d="M642 487h1l-2-2c1 0 2 0 2-1h-4l1-1h1c1 0 2 0 3-1-1 0-1 0-1-1 1 0 2 0 3-1s3-1 4-1h0l2 2h-1c-1 0-1 0-2 1-1 0-2 1-3 1 0 1 0 1 1 2l-1 1 3 1h-4 0c1 1 3 2 3 3l-6-3z" class="E"></path><path d="M638 499h0l1-2c0-1-1-1-1-2 1 0 1 0 2-1h0c1-1 2 0 3 0l1 1h-1-1l1 3-1-1v2l-1 1 3 2-1 2c2 1 3 3 4 4l1 2c1 1 1 1 1 2v1c-2-2-6-6-7-8h0c0-1-1-2-2-3 0-1-1-2-2-3z" class="I"></path><path d="M650 477h1v2h-1c-1 0-3 0-4 1s-2 1-3 1c0 1 0 1 1 1-1 1-2 1-3 1h-1l-1 1h4c0 1-1 1-2 1l2 2h-1-1l2 2-4-1v1l2 1-1 1h-2l-1-1h-4 0c0-1-1-1 0-2 0-1 0-2 1-2h1c-1 1-1 1 0 1h2l1-3v-3c1 0 2 0 3-1 2-1 6-2 9-3z" class="W"></path><path d="M642 499c3 1 3 2 6 5 2 2 3 3 6 4h0v-1h3 1l1 1h0l1 1c-1 2-1 3-1 5v2c-2 1-2 1-3 3-1 0-3-1-3-2-1-2-2-3-4-4h0v-1c0-1 0-1-1-2l-1-2c-1-1-2-3-4-4l1-2-3-2 1-1z" class="S"></path><path d="M644 502v1c1 1 2 1 3 3 1 1 6 5 8 6l2-2h-1c1-1 1-1 2-1-1 2 0 1 0 3 0 1-4 2-4 2l-6-4-1-2c-1-1-2-3-4-4l1-2z" class="E"></path><path d="M659 508h0l1 1c-1 2-1 3-1 5v2c-2 1-2 1-3 3-1 0-3-1-3-2-1-2-2-3-4-4h0v-1c0-1 0-1-1-2l6 4s4-1 4-2c0-2-1-1 0-3l1-1z" class="l"></path><path d="M635 480c2 2 0 3 3 4l-1 3h-2c-1 0-1 0 0-1h-1c-1 0-1 1-1 2-1 1 0 1 0 2h0 4l1 1-1 1h1c-1 1-2 1-4 2v1l2 1c0 1 1 2 2 3h0c1 1 2 2 2 3 1 1 2 2 2 3h0c1 2 5 6 7 8h0c-3 0-3-1-5-3l-1 1-16-20 2-2v-2-1h0l1-1c0-2 3-2 5-4v-1z" class="a"></path><path d="M633 490h0l1 2-2 2c-1-1-2-2-2-3l3-1z" class="O"></path><path d="M634 495l2 1c0 1 1 2 2 3h0c1 1 2 2 2 3 1 1 2 2 2 3h0c1 2 5 6 7 8h0c-3 0-3-1-5-3-3-3-7-7-9-11l-1-3v-1z" class="W"></path><path d="M676 486l5 2-1 1c-1 0-2-1-2-1l-5-2c2 2 4 3 6 4v1h-2l1 1c1 0 1 0 1 1h-2c0 1 0 1 1 2h0l-3 3-1 2h-1c-1 1-1 2-1 3v3h-2l1 1h-1l-1 1s1 1 1 2c1 1 1 2 1 3l1 2c-1 0-1 0-1 1 1 1 2 1 4 2 0-1 0-1 1-1 0 1 0 1-1 2h0l-1 1-1-1h-1l-6-3h-3c-1-2-1-4-1-6h0-1l-1-1-1-1h0l-1-1 1-1h0 1v-2h1v-1c0-1 1-2 1-3s-1-2-1-2h2l1-1c-2 0-4-1-5-2 2 0 4 0 5 1l1-1c-1 0-2-1-3-1 0 0-1 0-1-1l2 1c2 0 4 1 6 0l-8-3c2 0 4 0 6 1h0l-2-2c1 0 2 1 3 1v-1-1h3c0-1-1-2-2-3 2 1 3 1 4 1l-1-1c1-1 3 0 4 0z" class="Y"></path><path d="M670 496v-1l-1-1h1 2 0l-1-1 1-1h1 1l-2-2h2l-2-2h1 1l1 1c1 0 2 1 2 2l1 1c1 0 1 0 1 1h-2c0 1 0 1 1 2h0-1l-1-1-3 1h-2v1h-1z" class="I"></path><path d="M670 496h1v-1h2l3-1 1 1h1l-3 3-1 2h-1c-1 1-1 2-1 3v3h-2c-1 0-1-1-2-2l-1-1h2l-1-1-1-1h-1l1-1c0-1 1-1 2-1l2-2h-1v-1z" class="h"></path><path d="M668 504c1-1 1-1 3-1v-1l-1-1v-1c1-1 2-1 3-2v2c-1 1-1 2-1 3v3h-2c-1 0-1-1-2-2z" class="a"></path><defs><linearGradient id="f" x1="672.129" y1="512.541" x2="662.961" y2="505.945" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#9c9c9a"></stop></linearGradient></defs><path fill="url(#f)" d="M667 501l1 1 1 1h-2l1 1c1 1 1 2 2 2l1 1h-1l-1 1s1 1 1 2c1 1 1 2 1 3l1 2c-1 0-1 0-1 1 1 1 2 1 4 2 0-1 0-1 1-1 0 1 0 1-1 2h0l-1 1-1-1h-1l-6-3h-3c-1-2-1-4-1-6h0-1l-1-1-1-1 1-1h2v-1-1h1l-1-2h3v-2l1 1 1-1z"></path><defs><linearGradient id="g" x1="713.778" y1="239.787" x2="721.204" y2="162.466" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262625"></stop></linearGradient></defs><path fill="url(#g)" d="M674 207h0c-4-2-8-2-12-2h-16-38l-16-31h256l-2 2v1h0l-2 6c1-1 3-1 4-1 3 1 7 1 10 1-2 2-4 3-6 5h0 0l-2-1c-2 1-3 0-5 0 0 1-1 1-2 2h-3v1c2 0 3 0 4 1h3c-1 1-2 1-3 2h-1-2l8 3c9 3 16 7 23 13 2 1 5 3 7 5 1-1-1-3 1-4 1 0 1-1 2-1h1 0c0 2-1 5-2 7v1c-1 4-4 9-8 11h0v-1c2-2 5-4 5-7v-3c-1 1-2 1-3 1-1 1-4 2-6 2h-3c-1 1-5 1-7 1s-4 1-5 1c-2-1-2 0-3-2l2-1c0-2 2-4 2-6 0-1 0-1-1-1l-1-1c-2-1-5 0-7-1h1c-2-1-4-1-6-1-10-1-21 0-30 3l-7 2-23 11-10 8 1-4 3-3-2-1-2-1c-3-1-5-3-7-5h0-4c-1 0-5 2-6 3-2 0-4 1-6 2l-4 3c-1 2-2 4-3 5h-2c0-1 0-2-1-3-1 2-3 4-5 6l3 1c-1 1-2 2-3 2-1 1-3 2-3 3l-2 2h0c0 1-1 2-2 3-4 5-10 16-10 23l-3 11c-2 2-2 5-3 7 0 3 0 7 2 9h0l-2 1-1-1h-1l1 5h-1c0 2 3 7 2 10h0c0 2 0 3 1 5l-1 1h-1c0-1 0-2-1-3-1 0-2-1-2-2l-1-4c-1-2-1-5-2-8-1-2-1-5-1-7-1-2-1-5-2-7s-3-4-3-6c-1-2 0-4-1-6 1-3 1-5 2-7v-3-12c3-4 4-10 5-14l3-6c1-2 1-3 2-5v-2c1-1 1-3 0-4v-1l-1 1v2c0 1-1 1-2 2l-1-1-4 3v1c-3 0-4-1-6-1l-4-1v1c-1 0-2 0-3 1 0-1 0-1-1-2l-1-1h0v-1h-4c-2-1-3-2-3-4l-2-1c2 0 3 1 5 0-1-4-4-8-6-11l-1-1z"></path><path d="M719 212l-1 6 1-1 1-1 1 1c-2 1-3 1-4 3v2h0 1c1-2 2-2 3-3 2 1 1 2 2 3h1l-1 1h0c-3 0-6 0-8 1l-1-1c1-3 3-7 5-11z" class="U"></path><path d="M691 189c2-2 5-2 7-2 4 1 9 3 11 6l1 1h-1c-2-2-3-2-5-3l-2-1h-1-1l1 1-1 1-2-1h0v1c1 1 1 0 1 1v1c-3-3-4-4-8-5z" class="B"></path><path d="M755 209c1 1 2 1 3 2h0l1 1c-2 0-3 1-4 2-7 4-11 8-17 15-1 2-3 4-5 6l-1 1h-1c1-5 4-7 6-11 2-2 4-4 6-7s3-6 5-8c2 0 1 2 2 4l5-5z" class="E"></path><defs><linearGradient id="h" x1="748.004" y1="216.716" x2="750.629" y2="228.181" xlink:href="#B"><stop offset="0" stop-color="#61605d"></stop><stop offset="1" stop-color="#7d7d77"></stop></linearGradient></defs><path fill="url(#h)" d="M755 214c5 0 7 0 11 2l3 3 1 1c1 1 1 2 2 3h1 3v1l-3 1-2-1c-3-1-5-3-7-5h0-4c-1 0-5 2-6 3-2 0-4 1-6 2l-4 3c-1 2-2 4-3 5h-2c0-1 0-2-1-3 6-7 10-11 17-15z"></path><path d="M744 227c1-1 1-2 2-3 3-3 9-7 14-7 2 0 3 1 4 2h0-4c-1 0-5 2-6 3-2 0-4 1-6 2l-4 3z" class="h"></path><path d="M755 209c5-5 11-8 16-11h0c-1 2-3 2-4 4 1 0 0 0 1-1 1 0 3-1 4-1h0v1c-2 0-4 1-5 2 2 1 3 2 5 4h0v1c1 0 1 1 2 1v-2l1 1 1 5h1c0 1 1 2 2 3-1 0-3 0-4-1-2-1-1-3-4-3v1h0c0-1 0-2-1-3l-2 1-1 2v2h-1v1c-4-2-6-2-11-2 1-1 2-2 4-2l-1-1h0c-1-1-2-1-3-2z" class="D"></path><path d="M759 212h1c1 0 2-1 2-2-1 0-1 0-2 1l-1-1h1l1-1c1-1 1-1 3-1l1 1c2 1 2 1 3 2l-1 2v2h-1v1c-4-2-6-2-11-2 1-1 2-2 4-2z" class="W"></path><path d="M765 209c2 1 2 1 3 2l-1 2v2h-1c0-2 0-2 1-3h-3v-2l1-1z" class="g"></path><path d="M664 192c2-2 4-3 7-4 7-2 14-2 20 2v-1c4 1 5 2 8 5v1c0 1 0 1 1 2v2c0 3 0 6-1 9 0 3 0 6-1 10-1 2-1 2-3 4h-1v1h-1c2-6 4-12 2-19l-2-5c-1-3-3-5-5-7l-3-2c-4-2-7-2-12-1h-1c-2 0-3 1-5 2-1 1-1 1-3 1z" class="T"></path><path d="M691 189c4 1 5 2 8 5v1c0 1 0 1 1 2v2c0 3 0 6-1 9-1-7-4-13-8-18v-1z" class="G"></path><path d="M664 192c2 0 2 0 3-1 2-1 3-2 5-2h1c5-1 8-1 12 1l3 2c2 2 4 4 5 7-1 3 2 8 1 11-2-1-2-6-3-9-2-2-5-5-8-6-4-2-10 1-14 3-2 1-4 2-6 2v-1-2-1h0c-2 1-2 2-3 3h0-1 0c1-3 3-5 5-7z" class="C"></path><path d="M664 192c2 0 2 0 3-1 2-1 3-2 5-2h1c5-1 8-1 12 1l3 2h-1c-1 1-2 0-3 0-1-1-4-1-5-1l-1 1c-3 1-6 0-10 2-1 1 0 1-1 1-2 0-2 1-3 1l-1 1v-1h0c-2 1-2 2-3 3h0-1 0c1-3 3-5 5-7zm-52 2c-1-1-2-2-3-4s0-4 1-6c2-4 5-5 8-6 5 1 7 3 8 6 2 2 2 3 3 5v1 2h1v1l-1 2-3 1h0c-3 1-5 1-7 1h0l-1-1c-2 0-4 0-6-2z" class="G"></path><path d="M612 194h1 1 0 1c3 0 5 0 8-2v-1c0 1-1 2-1 2l-2 1 1 1-3 1h0c-2 0-4 0-6-2z" class="M"></path><path d="M614 183l2-2c2-1 4-1 5 0 2 1 3 2 3 4 0 1 1 4-1 5h0 0c0-2 0-4-2-5-2-2-4-2-7-2z" class="D"></path><path d="M626 184c2 2 2 3 3 5v1 2h1v1l-1 2-3 1h0c-3 1-5 1-7 1h0l-1-1h0l3-1c1 0 3-2 4-3 2-2 2-5 1-8z" class="V"></path><path d="M614 183c3 0 5 0 7 2 2 1 2 3 2 5-2 1-3 2-5 2s-3-1-4-2-1-2-1-4c0-1 1-1 1-2v-1z" class="m"></path><path d="M617 185h1c1 1 2 2 2 3l-1 1h-2l-1-1c0-1 0-1 1-2v-1z" class="U"></path><path d="M734 177c3 0 5 0 8 2 2 2 4 5 4 8v1c0 2 0 3-1 4-2 3-5 4-8 5-2 0-4 0-5-1-3-1-5-3-6-5s-1-5 0-7c2-4 4-5 8-7z" class="J"></path><path d="M739 190c0 1 0 1-1 1-1 1-3 1-5 1-2-1-2-2-3-4 0-1 0-2 1-3s1-2 4-2c-2 2-3 3-3 5 1 1 0 2 1 2 1 1 1 0 2 1l4-1z" class="f"></path><path d="M735 183c1 0 3 0 4 2 1 1 2 2 2 3-1 1-1 2-2 2l-4 1c-1-1-1 0-2-1-1 0 0-1-1-2 0-2 1-3 3-5z" class="p"></path><defs><linearGradient id="i" x1="632.992" y1="180.332" x2="646.051" y2="196.791" xlink:href="#B"><stop offset="0" stop-color="#171716"></stop><stop offset="1" stop-color="#353532"></stop></linearGradient></defs><path fill="url(#i)" d="M626 184h1c1 0 1-1 2-1 2 0 0 0 2-1h3v1c1 1 2 1 3 0h0l1-1c2 0 2 0 4-1 1-1 2-1 4-1 1-1 1-1 3-2h0c1-1 1-1 2-1v-1h1v2c-1 0-1 1-2 1-2 0-3 1-4 2 1 1 2 0 3 0l11 1c-2 1-7 0-10 0 3 1 9 0 12 1-4 1-7 1-11 1 0-1-1 0-2-1h-1c-1-1-3 0-4 0v1c1 0 3 0 4 1 2 0 3 0 4-1 2 0 4 0 5 1-1 1-2 1-4 1v1h0 0l-1 1h2c1-1 1-1 2-1l2-1h0c1 1 2 0 3 0h1c-1 1-2 1-4 2h0c-5 4-13 4-18 6-2 1-4 1-6 1-4 1-5 2-8 1h0l3-1 1-2v-1h-1v-2-1c-1-2-1-3-3-5z"></path><path d="M700 192l1-1-1-1h1 1l2 1c2 2 6 6 7 9 0 1 0 1 1 3v4l1 1h0c-2 2-3 6-4 8-1 3-3 5-5 8l-4 3v1c-3 0-4-1-6-1l-4-1v-1c1 0 2 0 3-2h1v-1h1c2-2 2-2 3-4 1-4 1-7 1-10 1-3 1-6 1-9v-2c-1-1-1-1-1-2v-1-1c0-1 0 0-1-1v-1h0l2 1z" class="G"></path><path d="M693 223h1l2 1c-1 2-1 2-2 3l-4-1v-1c1 0 2 0 3-2z" class="h"></path><path d="M705 209v5 2 1c-1 2-2 4-4 6l-1-1c0-3 3-9 5-13z" class="J"></path><path d="M700 192l1-1-1-1h1 1v1c1 1 3 3 3 5 1 0 1 1 1 1v7h-1l-1-4c-1-3-2-6-4-8z" class="E"></path><path d="M699 193c2 3 3 5 4 8h-1l-1-1c-1 6 3 14-3 18 1-4 1-7 1-10 1-3 1-6 1-9v-2c-1-1-1-1-1-2v-1-1z" class="d"></path><path d="M706 197c3 4 2 9 2 13-1 3-2 4-3 6v-2-5-5h1v-7z" class="C"></path><path d="M683 195c3 1 6 4 8 6 1 3 1 8 3 9 1-3-2-8-1-11l2 5c2 7 0 13-2 19-1 2-2 2-3 2v1 1c-1 0-2 0-3 1 0-1 0-1-1-2l-1-1h0v-1h-4c-2-1-3-2-3-4l-2-1c2 0 3 1 5 0-1-4-4-8-6-11l-1-1v-1c-2-4 2-7 6-8 0 0 2-1 3-1v-2z" class="m"></path><path d="M680 202h3l5 4v1c-1 0-1 0-2 1h0l-1 1h-2l-2 3-3-2c-1 0-1-1-2-2 0-1-1-1-1-2 1-2 2-3 5-4zm1 17h0l2-2v-2l1-1c1-1 2-2 4-2 1 1 1 2 1 3l-1 2 2 2c0 1-1 1 0 2v1c-1 1-2 2-4 2 0 1 0 1-1 1h0v-1h-4c-2-1-3-2-3-4l-2-1c2 0 3 1 5 0z" class="r"></path><path d="M678 220c2 1 7 4 9 3 1-1 2-1 3-2v1c-1 1-2 2-4 2 0 1 0 1-1 1h0v-1h-4c-2-1-3-2-3-4z" class="T"></path><path d="M683 195c3 1 6 4 8 6 1 3 1 8 3 9 1-3-2-8-1-11l2 5c2 7 0 13-2 19-1 2-2 2-3 2v1 1c-1 0-2 0-3 1 0-1 0-1-1-2l-1-1c1 0 1 0 1-1 2 0 3-1 4-2v-1c-1-1 0-1 0-2 1-3 1-6 1-9s-1-4-1-6h0-1l-2-2c-3-1-5-1-7 0-3 1-4 2-5 4v2l-1-1v-1c-2-4 2-7 6-8 0 0 2-1 3-1v-2z" class="M"></path><path d="M704 191c2 1 3 1 5 3h1 0c3 4 4 7 4 11 3-1 6-3 9-3l1 1c0 2-2 4-3 5-1 2-2 3-2 4-2 4-4 8-5 11l1 1c2-1 5-1 8-1-8 3-12 9-15 16-1 3-2 5-2 8-1 6-3 11-4 17 0 7 1 12 2 18l1 2c0 2 0 3 1 5 1 1 1 2 1 3s0 3 1 4l1 5h-1c0 2 3 7 2 10h0c0 2 0 3 1 5l-1 1h-1c0-1 0-2-1-3-1 0-2-1-2-2l-1-4c-1-2-1-5-2-8-1-2-1-5-1-7-1-2-1-5-2-7s-3-4-3-6c-1-2 0-4-1-6 1-3 1-5 2-7v-3-12c3-4 4-10 5-14l3-6c1-2 1-3 2-5v-2c1-1 1-3 0-4v-1l-1 1v2c0 1-1 1-2 2l-1-1c2-3 4-5 5-8 1-2 2-6 4-8h0l-1-1v-4c-1-2-1-2-1-3-1-3-5-7-7-9z" class="j"></path><path d="M715 224c2-1 5-1 8-1-8 3-12 9-15 16-1 3-2 5-2 8-1 6-3 11-4 17 0 7 1 12 2 18-1 1-1 1-1 2l-2-6v-3-1c0-1-1-2 0-3v-8l3-16c1-5 3-10 5-15l3-6v-1c1-1 1-1 1-2 0 1 0 1 1 2l1-1z" class="D"></path><path d="M698 267c0 2 0 6 1 8 0 1 0 2-1 4 1 1 2 1 2 2 1 1 2 3 2 4v3l1 1v1h2c1 2 1 2 1 4 0 1 1 2 1 3 0 2 0 3 1 4 0 2 3 7 2 10h0c0 2 0 3 1 5l-1 1h-1c0-1 0-2-1-3-1 0-2-1-2-2l-1-4c-1-2-1-5-2-8-1-2-1-5-1-7-1-2-1-5-2-7s-3-4-3-6c-1-2 0-4-1-6 1-3 1-5 2-7z" class="G"></path><path d="M703 290h2c1 2 1 2 1 4-1 1-1 2-1 3v7-2l-1-1 1-1c0-1 0-1-1-2v-3c0-2-1-3-1-5z" class="h"></path><defs><linearGradient id="j" x1="711.573" y1="302.424" x2="703.927" y2="311.076" xlink:href="#B"><stop offset="0" stop-color="#6d6b68"></stop><stop offset="1" stop-color="#7e7f76"></stop></linearGradient></defs><path fill="url(#j)" d="M705 304v-7c0-1 0-2 1-3 0 1 1 2 1 3 0 2 0 3 1 4 0 2 3 7 2 10h0c0 2 0 3 1 5l-1 1h-1c0-1 0-2-1-3 0-1-1-3-1-4-1-2-2-4-2-6z"></path><path d="M704 191c2 1 3 1 5 3h1 0c3 4 4 7 4 11 3-1 6-3 9-3l1 1c0 2-2 4-3 5h-1l-3 3c-1 1-2 2-3 4v1l-1 1v1l-1-1-1-1c2 9-5 17-7 25 0 1-1 2-1 3-1 2-2 6-3 8-1 4-1 8-2 12v-12c3-4 4-10 5-14l3-6c1-2 1-3 2-5v-2c1-1 1-3 0-4v-1l-1 1v2c0 1-1 1-2 2l-1-1c2-3 4-5 5-8 1-2 2-6 4-8h0l-1-1v-4c-1-2-1-2-1-3-1-3-5-7-7-9z" class="E"></path><path d="M717 207c1 0 2 1 3 1l-3 3c-1-1-2-1-4-1l4-3z" class="t"></path><path d="M724 203c0 2-2 4-3 5h-1c-1 0-2-1-3-1 2-2 4-3 7-4z" class="c"></path><path d="M713 210c2 0 3 0 4 1-1 1-2 2-3 4v1l-1 1v1l-1-1-1-1c0-2 1-4 2-6z" class="m"></path><path d="M846 176v1h0l-2 6c1-1 3-1 4-1 3 1 7 1 10 1-2 2-4 3-6 5h0 0l-2-1c-2 1-3 0-5 0 0 1-1 1-2 2h-3v1c2 0 3 0 4 1h3c-1 1-2 1-3 2h-1-2-3c-4 0-7-1-10-1-2 0-4 1-6 1s-4 0-5 1h-3 0c-1 0-2-1-3-1l-13 3-1 1c-1-1-2-2-4-2 0-1-1-1-1-1l1-2h-1-1c-7 2-13 5-19 8h0c-1 0-3 1-4 1-1 1 0 1-1 1 1-2 3-2 4-4h0c5-3 10-5 14-7 19-9 41-13 61-15z" class="S"></path><path d="M833 185v2l7 1c1 0 2 0 3 1h-3-16c-4 1-9 2-13 4l-13 3c1-2 4-2 6-3 5-1 9-3 13-4 6-1 12-1 16-4z" class="X"></path><path d="M811 193c4-2 9-3 13-4h16v1c2 0 3 0 4 1h3c-1 1-2 1-3 2h-1-2-3c-4 0-7-1-10-1-2 0-4 1-6 1s-4 0-5 1h-3 0c-1 0-2-1-3-1z" class="R"></path><path d="M838 193c-1-1-2-1-3-2h0c-2-1-5 0-6 0-1-1-1-1-2-1h0c2 0 5-1 8 0h5c2 0 3 0 4 1h3c-1 1-2 1-3 2h-1-2-3z" class="E"></path><path d="M845 178l1-1-2 6c1-1 3-1 4-1 3 1 7 1 10 1-2 2-4 3-6 5h0 0l-2-1c-2 1-3 0-5 0 0 1-1 1-2 2-1-1-2-1-3-1l-7-1v-2l-1-1c0-1 0-1 1-1v-1h0-6c0 1-1 1-2 1l-5 1-1 1h-2l-1 1v-1h0l2-1h1l10-3c2-1 4-1 6-1 4-1 6-2 10-2z" class="g"></path><path d="M845 178l1-1-2 6h-1c-3 0-6 1-7-1 1-2 3-1 5-2h0l4-2z" class="B"></path><path d="M844 183c1-1 3-1 4-1 3 1 7 1 10 1-2 2-4 3-6 5h0 0l-2-1c-2 1-3 0-5 0 0 0-3-1-4-2h0l1-1h-1c-2 0-3 1-4 1l-2-1 1-2c1 2 4 1 7 1h1z" class="Y"></path><path d="M723 223v2l-2 2h-1l-1-1-3 3v1h-1l-3 6c-1 1-1 1-1 2 1 0 1 0 2 1h1 1c0 1 1 1 2 1s1-1 2-2c0-1 2-2 4-3h2 0v-1-1c1-1 2-1 3-1h0l1-1v-1c1-2 1-2 2-3l1-1c2 0 3 0 4-1h1c-2 4-5 6-6 11h1l1-1 3 1c-1 1-2 2-3 2-1 1-3 2-3 3l-2 2h0c0 1-1 2-2 3-4 5-10 16-10 23l-3 11c-2 2-2 5-3 7 0 3 0 7 2 9h0l-2 1-1-1h-1c-1-1-1-3-1-4s0-2-1-3c-1-2-1-3-1-5l-1-2c-1-6-2-11-2-18 1-6 3-11 4-17 0-3 1-5 2-8 3-7 7-13 15-16h0z" class="r"></path><path d="M706 247l2 2h1v-1l2-2c1 0 1 0 1-1l2-2v1c0 2-1 3-3 4l-2 2v1 1l-1 1-1-1-1 1c1 2 1 3 1 4-1 1-2 2-2 3s0 1-1 2h1c0 3-2 7-1 10v5l1 1h0l1-1v-2h0v-1h0v-1c1-3 1-6 3-8 1-2 1-3 3-5 1-2-1 0 1-2l1-1c-4 9-7 18-9 27l-1-2c-1-6-2-11-2-18 1-6 3-11 4-17z" class="f"></path><path d="M714 257c1-3 2-6 2-9 1-2 1-5 1-6 1-2 3-3 4-5h1v3c1 0 2 1 3 1h0l-2 1v1c0 1 0 0-1 1 0 1 0 1-1 2l-11 30-4 13c-1-2-1-3-1-5 2-9 5-18 9-27z" class="H"></path><path d="M737 225c-2 4-5 6-6 11h1l1-1 3 1c-1 1-2 2-3 2-1 1-3 2-3 3l-2 2h0c0 1-1 2-2 3-4 5-10 16-10 23l-3 11c-2 2-2 5-3 7 0 3 0 7 2 9h0l-2 1-1-1h-1c-1-1-1-3-1-4s0-2-1-3l4-13 11-30c1-1 1-1 1-2 1-1 1 0 1-1v-1l2-1h1c1-1 2-3 3-5l2-9 1-1c2 0 3 0 4-1h1z" class="M"></path><path d="M710 276c0 5-2 10-1 16v4h-1c-1-1-1-3-1-4s0-2-1-3l4-13z" class="h"></path><path d="M791 192h1 1l-1 2s1 0 1 1c2 0 3 1 4 2l1-1 13-3c1 0 2 1 3 1h0 3c1-1 3-1 5-1s4-1 6-1c3 0 6 1 10 1h3l8 3c9 3 16 7 23 13 2 1 5 3 7 5 1-1-1-3 1-4 1 0 1-1 2-1h1 0c0 2-1 5-2 7v1c-1 4-4 9-8 11h0v-1c2-2 5-4 5-7v-3c-1 1-2 1-3 1-1 1-4 2-6 2h-3c-1 1-5 1-7 1s-4 1-5 1c-2-1-2 0-3-2l2-1c0-2 2-4 2-6 0-1 0-1-1-1l-1-1c-2-1-5 0-7-1h1c-2-1-4-1-6-1-10-1-21 0-30 3l-7 2-23 11-10 8 1-4 3-3-2-1 3-1v-1h-3-1c-1-1-1-2-2-3l-1-1-3-3v-1h1v-2l1-2 2-1c1 1 1 2 1 3h0v-1c3 0 2 2 4 3 1 1 3 1 4 1-1-1-2-2-2-3h-1l-1-5-1-1v2c-1 0-1-1-2-1v-1h0c-2-2-3-3-5-4 1-1 3-2 5-2v-1c6-3 12-6 19-8z" class="g"></path><path d="M799 199l1-1c1-1 2-1 3 0 1 0 1 0 1 1s0 1-1 2c-1 0-2-1-3-1l-1-1z" class="B"></path><path d="M801 205l2 2h1v-1c1 0 1 0 2 1v1c-2 1-4 1-6 1v-1-1l-1 1h-1v-1c0-1 1-2 2-2v1l1-1z" class="D"></path><path d="M827 197h2c1 1 1 1 2 3-1 1-1 1-3 2h-2c-1-1-1-1-1-3 0-1 1-2 2-2z" class="O"></path><path d="M816 199c1 0 3 1 3 1 1 1 1 2 1 3-1 1-1 2-3 2h0c-3 1-6 1-9 1l1-1 5-1c2-2 2-2 2-5z" class="B"></path><path d="M796 214c6-1 12-5 17-4-2 0-7 1-9 3v1l-23 11c1-2 1-3 3-4l3-2c3-2 6-4 9-5z" class="G"></path><path d="M809 205c0-1-2-3-2-4-1-1-1-2 0-3s3-2 5-2c1 1 2 1 3 2 0 1 0 0 1 1h0c0 3 0 3-2 5l-5 1z" class="P"></path><path d="M793 195c2 0 3 1 4 2l2 2h0l1 1c0 1 0 2 1 2v3l-1 1v-1c-1 0-2 1-2 2v1h1l1-1v1 1l-2 1c-1 1-2 1-4 1l-1-1-1-1h-4c-1 1-2 1-3 2h-1l4-4h-2v-2c1 0 1-1 2-1l-1-3h1c1-2 3-3 4-3v-2l1-1z" class="l"></path><path d="M793 195c2 0 3 1 4 2l2 2-1 1c-1 0-1 0-2 1h-1l-3-3v-2l1-1z" class="X"></path><path d="M788 204l-1-3h1c1-2 3-3 4-3l3 3-4 3v-1-1h-1v2h-1-1z" class="F"></path><path d="M853 219l1-1 3-3h0v1c-1 1-1 2-2 3h0v1c1-1 2-1 3-2l3-2c0-1 0-3-1-5-1 0-2-1-3-2h1c2 0 4 0 6 1 2 0 3 1 4 2h3 0c2 0 3 0 5 1v1 3l-1 1h0c-1 1-4 2-6 2h-3c-1 1-5 1-7 1s-4 1-5 1c-2-1-2 0-3-2l2-1z" class="T"></path><path d="M869 217l-1-1h1l-1-1-1 1c-2 1-3 1-4 1l-1-1 2-1 7-1c0 2-1 2-2 3z" class="v"></path><path d="M871 212h0c2 0 3 0 5 1v1 3l-1 1h0c-1 1-4 2-6 2h-3 0l4-1-1-1v-1c1-1 2-1 2-3l-3-2h3z" class="d"></path><path d="M871 212h0c1 1 3 2 3 4l-1 1-3 2-1-1v-1c1-1 2-1 2-3l-3-2h3z" class="E"></path><path d="M831 195c3 0 4 0 7 1 1 0 2 0 3 1s1 1 2 1c2-1 2 1 3 1h2l2 1c1 0 2 1 4 1v1c2 0 3 2 4 3 1 0 3 1 5 1 2 1 4 2 6 4-1-1-2-1-3-1-3-1-5-1-8-2l-2-1h-2c-6-3-34 2-37 0 3 0 6 0 8-1 1-1 2-2 4-2h2c2-1 3-3 5-5l-1-1h-3c-1 0 0 0-1-1v-1z" class="J"></path><path d="M843 198c2-1 2 1 3 1h2l2 1c1 0 2 1 4 1v1c2 0 3 2 4 3h-1l-1-1v1c-2 0-3 0-4-1s-2-1-3-1c-2 0-2 0-3 1-2 0-2 0-3-1l1-1 1 1v-1c-1-1-2-1-2-1v-1h1c0-1 0-1-1-2h0z" class="d"></path><path d="M817 194c1-1 3-1 5-1s4-1 6-1c3 0 6 1 10 1h3l8 3c9 3 16 7 23 13 2 1 5 3 7 5 1-1-1-3 1-4 1 0 1-1 2-1h1 0c0 2-1 5-2 7v1c-1 4-4 9-8 11h0v-1c2-2 5-4 5-7v-3c-1 1-2 1-3 1h0l1-1v-3-1c-2-1-3-1-5-1h0l-2-2c-2-2-4-3-6-4-2 0-4-1-5-1-1-1-2-3-4-3v-1c-2 0-3-1-4-1l-2-1h-2c-1 0-1-2-3-1-1 0-1 0-2-1s-2-1-3-1c-3-1-4-1-7-1-1-1 0-1-1-1-1 1-2 1-3 0-2 0-3 2-5 3l-2-1c-2-1-2-1-3-2z" class="h"></path><path d="M784 211h1c1-1 2-1 3-2h4l1 1 1 1c2 0 3 0 4-1h4c-1 1-1 1-1 2-1 0-2 0-3 1l-2 1c-3 1-6 3-9 5l-3 2c-2 1-2 2-3 4l-10 8 1-4 3-3-2-1 3-1v-1h-3-1c-1-1-1-2-2-3l-1-1-3-3v-1h1v-2l1-2 2-1c1 1 1 2 1 3h0v-1c3 0 2 2 4 3 1 1 3 1 4 1h4c2-1 2-1 2-3 0-1-1-1-1-1v-1z" class="E"></path><path d="M784 211h1c1-1 2-1 3-2h4l1 1 1 1c-1 1-3 1-3 1-1 1-3 4-4 3v-1c0-1-1-1-1-2l-1 1c0-1-1-1-1-1v-1z" class="I"></path><path d="M768 211l2-1c1 1 1 2 1 3h0c0 2 1 4 1 5 1 1 1 2 3 2v1c2 1 3 1 5 0 1 0 1 0 2-1 2 0 3-1 5-1l-3 2c-2 1-2 2-3 4l-10 8 1-4 3-3-2-1 3-1v-1h-3-1c-1-1-1-2-2-3l-1-1-3-3v-1h1v-2l1-2z" class="J"></path><path d="M791 192h1 1l-1 2s1 0 1 1l-1 1v2c-1 0-3 1-4 3h-1l1 3c-1 0-1 1-2 1v2h2l-4 4v1s1 0 1 1c0 2 0 2-2 3h-4c-1-1-2-2-2-3h-1l-1-5-1-1v2c-1 0-1-1-2-1v-1h0c-2-2-3-3-5-4 1-1 3-2 5-2v-1c6-3 12-6 19-8z" class="C"></path><path d="M791 192v2h0c-1 0 0 1-1 0-1 0-2 0-3 1h-1c-1 0-2 1-4 2-1 0-1 0-2 1l-1 1h0c0 2-1 3-1 5h1l-1 1c-1 1-1 1-2 1 0 1-1 1-1 2l-1-1v2c-1 0-1-1-2-1v-1h0c-2-2-3-3-5-4 1-1 3-2 5-2v-1c6-3 12-6 19-8z" class="d"></path><path d="M779 204h3c1-1 1-1 2 0h1l1 1v2h2l-4 4v1s1 0 1 1c0 2 0 2-2 3h-4c-1-1-2-2-2-3h-1l-1-5c0-1 1-1 1-2 1 0 1 0 2-1l1-1z" class="W"></path><defs><linearGradient id="k" x1="803.996" y1="302.467" x2="723.929" y2="268.074" xlink:href="#B"><stop offset="0" stop-color="#020302"></stop><stop offset="1" stop-color="#2f2f2e"></stop></linearGradient></defs><path fill="url(#k)" d="M804 214l7-2c9-3 20-4 30-3 2 0 4 0 6 1h-1c-8 9-12 21-17 31l-32 69-31 65-5 12c0 2-2 4-2 7v1 1-1l-1 1h-2 0c1 2 3 4 3 6v1c-1 0-1 0-2-1l-2 2h-1v-1h-2c0-2-1-3-2-4-1-2-1-3-4-3l-2 1h0c0-1 0 0-1-1l-1 1c-1 1-2 2-3 2-1-2-3-3-5-4-2-2-4-5-5-7l-5-6c-1-2-2-3-4-4l-1-2-1-2-1-1c-1-2 0-1-1-2l-2-2-1-2c-1-2-3-4-4-7 0-1 0-1-1-2l-1-2c0-1-1-2-1-3l-1-1-5-9v-1l-2-4c0-1 0-1-1-2-1-2-3-5-4-7v-1-1h1v-3c1 0 2 0 3-2l1-1c1 0 1-1 2-1s1-1 2-1l-1-4c0-1-1-1-1-1 1-2 1-3 0-5-1-3-3-3-6-4 1 0 2 0 3-1h1c1 0 1 1 2 1l1 1v-1-1c-1-1-1-1-1-2l1-2-1-2 1-2v-2l1-1c0 2 0 5 1 7 1 3 1 6 2 8l1 4c0 1 1 2 2 2 1 1 1 2 1 3h1l1-1c-1-2-1-3-1-5h0c1-3-2-8-2-10h1l-1-5h1l1 1 2-1h0c-2-2-2-6-2-9 1-2 1-5 3-7l3-11c0-7 6-18 10-23 1-1 2-2 2-3h0l2-2c0-1 2-2 3-3 1 0 2-1 3-2l-3-1c2-2 4-4 5-6 1 1 1 2 1 3h2c1-1 2-3 3-5l4-3c2-1 4-2 6-2 1-1 5-3 6-3h4 0c2 2 4 4 7 5l2 1 2 1-3 3-1 4 10-8 23-11z"></path><path d="M743 361c3 2 6 5 7 8 0 1 1 1 2 2l-1 1c-2-1-3-1-4-2h0c0-1 0-2-1-3h-1c0-2-1-4-2-6z" class="U"></path><path d="M763 236c3 0 7-5 9-7l-1 4-9 8-1-1c0-2 0-2 2-4z" class="C"></path><path d="M807 242s1 1 1 2c1 0 1 1 2 1l1 2h-1l-2-1-1 1 1 1v1l-2-2h-1c0 1 1 1 1 2 1 2 2 3 4 4h-1l-2-1-1-2c-2-1-2-2-2-3l2-1c-1-1-2-2-2-4h3z" class="U"></path><path d="M743 355c1-3 0-5 2-8h1c-1 5-1 6 1 10l1 1h-1c-1-1-1-2-2-3 0-1-1-2-1-3 0 2 0 4 1 6-1 1-2 1-2 1l-1 1h0l1 1c1 2 2 4 2 6h1c-1 0-3-2-4-3v-2c0-1-1-2-2-3v-1l1 1 1-1c-1 0-1 0-1-1h2v-2zm26-32c1-2 0-3-1-5l2-2 1 1-1 3h0l1 2c1 2 4 5 6 6h-1c0 1-1 1 0 1 1 1 2 2 2 4-1-1-1-1-2-1l1 1v1l-1-1c0-1 0-1-1-1-2-1-3-2-4-3l-1-1 1-1-2-4z" class="c"></path><path d="M734 354c1-1 2-1 3-1 1 1 2 1 4 1 1 1 1 1 2 1v2h-2c0 1 0 1 1 1l-1 1-1-1v1c1 1 2 2 2 3v2c1 1 3 3 4 3 1 1 1 2 1 3-6-5-10-10-13-16z" class="K"></path><path d="M802 233l1 4v1l1 1h-1v2h0 2v-1h0l2 2h-3c0 2 1 3 2 4l-2 1c0 1 0 2 2 3l1 2v2c-1-1-2-2-3-2l-3-4v-1h1c-1 0-1-1-1-1v-1l-2-2h1 1c-1-1-1-1-1-2v-1c-1-1-1-1-1-2l1 1v-1l1-1c1-1 1-2 1-4z" class="m"></path><path d="M724 314l1-1v-2c1-6 2-12 4-18 1-2 1-2 2-3l1 2c-2 6-3 13-4 20-1 2-2 6-2 8v5c-1 0-1 0-2-1 1-2-1-4-2-6 1 0 1 0 2-1v-3h0z" class="D"></path><path d="M752 251c1-2 2-3 4-3-10 14-18 27-24 44l-1-2 1-2c0-1 0-1 1-2l1-2v-2c0-1 0-1 1-2 2-6 5-13 9-18l3-6c1-2 3-4 5-5z" class="g"></path><path d="M778 231c2-2 5-4 7-6 1 0 2-1 3-1 2-1 4-2 6-2 2-1 5-3 7-3 3-1 6 0 9-1h2 3c1 1 2 1 4 1-3 0-5 0-7 2l-2 1h-3-1c-1 0-1 1-2 1l-1 1c-2-1-4-2-5-2l-1 1c-1 1-5 1-5 2h0c-2 1-3 1-4 2h-1c-2 0-3 1-5 1-1 1-2 2-4 3z" class="c"></path><path d="M782 228l5-2c2-2 5-3 8-4 1 0 1 0 2-1h1 0 6l1-1 2 2h-1c-1 0-1 1-2 1l-1 1c-2-1-4-2-5-2l-1 1c-1 1-5 1-5 2h0c-2 1-3 1-4 2h-1c-2 0-3 1-5 1z" class="m"></path><path d="M783 278l2 4-1 1 3 3-1 1-1-1c2 2 3 4 5 5 1 0 2 1 3 1v1l1 1h1v-1l1 1c0 1-1 1-1 2v2l1 1c-1 1-2 1-3 1l-4-3c-1-1-2-1-3-1h1c1 2 2 3 3 6l-1 2-3-5h0l-1-1c-2-2-4-2-6-3v-1h2 1c-1-1-2-1-2-2 1 0 1 0 2-1l-1-1v-1h1c-1-1-1-2-1-3l2 1-1-1c1-1 1 0 1-1l-1-2c0-2 0-3 1-5z" class="V"></path><path d="M786 299v-2l-1-2 1-1c2 1 4 3 5 4h1l-6-6h1l2 2c0-2-2-3-3-5 2 2 4 4 6 5l-2-3c1 0 2 1 3 1v1l1 1h1v-1l1 1c0 1-1 1-1 2v2l1 1c-1 1-2 1-3 1l-4-3c-1-1-2-1-3-1h1c1 2 2 3 3 6l-1 2-3-5z" class="U"></path><path d="M788 227c1-1 2-1 4-2h0c0-1 4-1 5-2l1-1c1 0 3 1 5 2l1-1c1 0 1-1 2-1h1 3c-2 2-3 3-4 5v2l-2-1-2 4v1c0 2 0 3-1 4l-1 1-1-3c-1 0-1 0-1-1l2 1h0l-1-2h-1l-2-2c-2 0-3-1-4-2-1 0-1-1-3 0-1 0-3 1-5 1-2 1-3 1-4 2l-8 5c1-3 3-4 6-6 2-1 3-2 4-3 2 0 3-1 5-1h1z" class="K"></path><defs><linearGradient id="l" x1="785.436" y1="225.748" x2="796.678" y2="231.617" xlink:href="#B"><stop offset="0" stop-color="#343534"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#l)" d="M788 227h5l1-1 2 1 1-1c1 1 4 2 4 4-1 0-1 0-2-1h-1l2 3s0 1-1 1h-1l-2-2c-2 0-3-1-4-2-1 0-1-1-3 0-1 0-3 1-5 1-2 1-3 1-4 2l-8 5c1-3 3-4 6-6 2-1 3-2 4-3 2 0 3-1 5-1h1z"></path><path d="M788 262l4-3h1c2-2 3-1 6-1-2 0-4 1-5 1-2 1-4 2-5 4h0v4c-1 2-1 3 0 5s1 3 2 4l1 1v3 1c-2 2-2 2-2 4l5 7v1h0v1h-1l-1-1v-1c-1 0-2-1-3-1-2-1-3-3-5-5l1 1 1-1-3-3 1-1-2-4h-2l-1-2h1 1v-4h-1c2-1 2-2 2-4l3-6h2z" class="f"></path><path d="M787 274h0c1 1 0 1 1 1 1 1 2 1 3 1l1 1v3c-3-1-4-3-5-6z" class="I"></path><path d="M789 263v4c-1 2-1 3 0 5s1 3 2 4c-1 0-2 0-3-1-1 0 0 0-1-1h0c-1-1-1-3-1-4v-1c1-2 1-4 3-6z" class="D"></path><path d="M781 278l-1-2h1 1c4 3 5 7 8 10h-1l-1 1c1 0 1 1 2 1 1 1 2 3 3 4-1 0-2-1-3-1-2-1-3-3-5-5l1 1 1-1-3-3 1-1-2-4h-2z" class="b"></path><defs><linearGradient id="m" x1="736.312" y1="338.839" x2="728.772" y2="348.554" xlink:href="#B"><stop offset="0" stop-color="#373635"></stop><stop offset="1" stop-color="#525352"></stop></linearGradient></defs><path fill="url(#m)" d="M726 320l1 1v4l2-2v1c0 1 0 1-1 3 1 1 1 1 1 3l2 2v1h-1c1 1 1 1 1 2h-1l2 3c2 1 3 3 4 5 1 1 3 2 4 3h1 3l1-2h0c2-1 2-1 4-1l-3 4h-1c-2 3-1 5-2 8-1 0-1 0-2-1-2 0-3 0-4-1-1 0-2 0-3 1-2-5-6-9-7-13l-1-1c0-2-1-4-1-6h1c0-2 1-2 0-3v-1c1-2 0-1 1-2v-1c-1 0-2 0-3-1v-2c1 1 1 1 2 1v-5z"></path><path d="M732 338c2 1 3 3 4 5 1 1 3 2 4 3h1 3l1-2h0c2-1 2-1 4-1l-3 4h-1c-2 3-1 5-2 8-1 0-1 0-2-1v-1l-2-1c-1-2-4-4-6-6h1c-1-1-2-1-3-2l1-1h1l-3-2 1-1c-1-1-1-2-1-3l1 1h1z" class="P"></path><path d="M745 344h0c2-1 2-1 4-1l-3 4h-1c-2 3-1 5-2 8-1 0-1 0-2-1v-1l-2-1h2l-1-2h2v-1c1-1 2-2 2-3l1-2z" class="Q"></path><path d="M757 241c0 3-4 7-5 10-2 1-4 3-5 5l-3 6c-4 5-7 12-9 18-1 1-1 1-1 2v2l-1 2c-1 1-1 1-1 2l-1 2c-1 1-1 1-2 3-2 6-3 12-4 18v2l-1 1c-1-2-2-3-2-5-1-1 1-5 2-6l2-11c0-1 1-4 1-5l3-6c1-5 4-7 5-12 3-3 4-6 6-9 1-2 3-4 4-7l12-12z" class="N"></path><path d="M772 237l8-5c1-1 2-1 4-2 2 0 4-1 5-1 2-1 2 0 3 0v1c0 1 0 1 1 2h-1v-1h-1c-2 0-4 0-6 1l-6 3-4 3c-2 1-3 2-4 3-1 0-1 1-3 1v2l-2 1 1 1h-2v2h-2v1c-1 1-1 1-2 1 0 1 0 1 1 2l-1 1v1h-1v1h0v1l-2-1 1 2-1 1c-1 1-3 4-4 4v1l-2 2c0 1 0 1-1 2s-1 1-1 2h-2c0 1 0 1 1 2h-2c1 1 1 1 1 2h-2l1 1v1h-2c0 1 1 2 1 2-1 1-1 1-2 1l1 1h-2l1 1v1h-2c1 1 0 1 1 1v1c-1 0-1 0-2 1v2 1l-2 1c0 1 0 1 1 2l-2 1 1 1-2 1 1 1c0 1 0 1-1 1v2h-1c0 1 0 1 1 2l-1 1v1l-1 1 1 1h-1v1 1l-1 1h0c0 1 0 2-1 2v3 1c0 3-1 9 0 11l-1 1 1 1h-1l1 2h-2c1 1 1 2 2 3h-1c-1 1-1 1-1 2l-2-2c0-2 0-2-1-3 1-2 1-2 1-3v-1-3c0-3 1-7 2-10 0-3 1-5 2-7l3-12c1-2 2-3 2-5 1-3 3-6 4-10 1-3 3-5 4-7 2-4 4-8 7-10 2-2 3-7 5-8h2v-2l2-1v-2s2-2 3-2c0-1 1-1 1-2h1l1-1 4-4z" class="Q"></path><path d="M708 301h1c2 7 4 15 7 22 4 11 10 23 16 34 8 13 17 26 27 38l-1 1h-2 0l-31-46c-2-2-4-6-5-8h-1v-2c0-2-2-2-3-3l-5-12h0c-1-4-3-7-3-11 1 1 1 2 1 3h1l1-1c-1-2-1-3-1-5h0c1-3-2-8-2-10z" class="k"></path><path d="M710 311c2 4 3 9 4 13 3 9 7 17 11 26-2-2-4-6-5-8h-1v-2c0-2-2-2-3-3l-5-12h0c-1-4-3-7-3-11 1 1 1 2 1 3h1l1-1c-1-2-1-3-1-5h0z" class="j"></path><path d="M799 258h0c4 3 7 4 9 8 1 2 1 4 1 6-1 3-3 6-6 8h-1c-4 2-6 2-10 1h0v-1-3l-1-1c-1-1-1-2-2-4s-1-3 0-5v-4h0c1-2 3-3 5-4 1 0 3-1 5-1z" class="G"></path><path d="M789 272v-4c1-1 2-1 3-2-1 3-1 4-1 7l2 2h-1l1 2h0-1l-1-1c-1-1-1-2-2-4z" class="a"></path><path d="M792 277h1c3 3 6 2 9 3-4 2-6 2-10 1h0v-1-3z" class="C"></path><path d="M799 258h0c4 3 7 4 9 8l-1 1c-1-1-1-2-2-2-2 0-2-1-4-1v-1c-1 1-1 1-2 1l-1-1h2l-1-1c-2-1-4-1-5-3 1 0 3-1 5-1z" class="R"></path><path d="M792 266h3l-1 1h1c1 0 2 1 3 1v2c1-1 1 0 2-1h3c1 1 1 1 1 2 0 2-1 3-2 4-2 2-4 2-5 1-2 0-3-1-4-1l-2-2c0-3 0-4 1-7z" class="p"></path><path d="M810 222l2-1c2-2 4-2 7-2 3 2 6 4 8 8 0 3 0 5-1 7s-2 4-4 5c-3 2-5 2-9 2l-2 1c-2 0-3-1-4-2s-1-1-2 0c0-1-1-2-1-2l-1-1-1-4v-1l2-4 2 1v-2c1-2 2-3 4-5z" class="W"></path><path d="M814 237c-1 0-2 1-3 1l-1-1 1-3 3 3z" class="l"></path><path d="M810 230h-3c0-2 1-4 2-6 2-1 4-1 6-1v2c1 0 1 0 2 1h-2v1h-2l-1-1c-2 1-2 2-2 4z" class="i"></path><path d="M822 231l3-3c1 2 1 5 1 6-1 2-2 4-4 5-2 0-4 1-5 0 0-1 2-1 3-2s1-1 1-2l1-1c-1-1 0-2 0-3z" class="X"></path><path d="M802 233v-1l2-4 2 1c-1 3-1 6 2 9 1 1 3 2 5 3l-2 1c-2 0-3-1-4-2s-1-1-2 0c0-1-1-2-1-2l-1-1-1-4z" class="c"></path><path d="M810 230c0-2 0-3 2-4l1 1h2c3 1 5 2 7 4 0 1-1 2 0 3-2 2-3 2-5 3h-3l-3-3c-1-1-1-3-1-4z" class="r"></path><path d="M810 230c0-2 0-3 2-4l1 1c-1 2-1 4 0 6v1l3 2c1 0 1 0 1 1h-3l-3-3c-1-1-1-3-1-4z" class="f"></path><path d="M749 343l2-2c3-2 5-3 9-2 1 0 3 1 4 2l3 4 1 2c1 2 1 4 0 6-1 3-3 6-6 7-3 2-7 2-10 1l-1-1c-1 0-3-2-3-2l-1-1c-2-4-2-5-1-10l3-4z" class="G"></path><path d="M762 347c0 1 1 2 1 2 0 2 1 3 0 5-1 1-3 2-4 2-3 1-5 0-7-1-1-1-2-3-2-5v-1h-1v-1c1-1 2-2 4-2h2v1c2 1 3 1 5 0h2 0z" class="r"></path><path d="M749 343l2-2c3-2 5-3 9-2 1 0 3 1 4 2l3 4 1 2c-2-1-2-2-3-3l-1 1c-1 0-3-2-4-2l-1 1h1c1 1 2 2 2 3h0-2c-2 1-3 1-5 0v-1-1c0-1-1-1-2-1h0l-1 1h-1l-1 1-2 2c0 1 0 1-1 2l1 1c0 1-1 3 0 5l4 4h-1c-1 0-3-2-3-2l-1-1c-2-4-2-5-1-10l3-4z" class="J"></path><path d="M779 295c2 1 4 1 6 3l1 1h0l3 5s1 1 1 2c0 3 0 7-3 9-2 3-5 5-8 5-1 0-3 0-4-1-1 0-3-1-4-2l-1-1-3-3c-1-3-1-6 0-9h0c1-2 2-3 3-4 3-3 5-5 9-5z" class="N"></path><path d="M777 300v-1c2 0 3 0 5 1 1 1 2 2 2 3v1l-1-1c-1-1-1-1-2-1l-1 1s-2 1-2 0c-1 0 0 0-1-1h-2l2-1v-1z" class="Z"></path><path d="M779 295c2 1 4 1 6 3l1 1h0l3 5s1 1 1 2l-1 3c-1-5-2-7-5-10l-1-2-1 1v2c-2-1-3-1-5-1v1h-3-1v-1c1 0 1 0 2-1v-1l-2 1-3 2c3-3 5-5 9-5z" class="D"></path><path d="M784 299c3 3 4 5 5 10l1-3c0 3 0 7-3 9-2 3-5 5-8 5-1 0-3 0-4-1-1 0-1-1-2-2-1 0-1-1-2-2 1 1 2 1 4 2v-1-1c1 1 2 1 4 1l1-1h-2c4-1 5-2 7-4l1-3 1 1v-1c1-2 0-5-1-6s-1-2-2-3z" class="a"></path><path d="M785 311c0 2-2 4-3 6-1 0-1 1-2 1s-4 0-5-1v-1-1c1 1 2 1 4 1l1-1h-2c4-1 5-2 7-4z" class="j"></path><path d="M778 315c-3-1-5-2-6-5-1-1-1-3-1-5l1-1 1-1h2c0 1 0 1 1 2 0 1 1 1 2 1s2-1 3-2c0 0 2 1 3 1 1 1 1 2 2 3l-1 3c-2 2-3 3-7 4z" class="U"></path><path d="M716 337c1 1 3 1 3 3v2h1c1 2 3 6 5 8l31 46c1 2 3 4 3 6v1c-1 0-1 0-2-1l-2 2h-1v-1h-2c0-2-1-3-2-4-1-2-1-3-4-3l-2 1h0c0-1 0 0-1-1l-1 1c-1 1-2 2-3 2-1-2-3-3-5-4-2-2-4-5-5-7 1 0 2 1 3 1 0-1 0-2 1-3 0-1 1-2 2-3l3-2c0-3-2-5-2-8l-9-14-11-22z" class="G"></path><path d="M744 385c4 4 7 9 10 14l-5-5 5 7h1c0 1 0 1-1 2h-2c0-2-1-3-2-4-1-2-1-3-4-3l1-1v-1c0-1-1-2-2-3l-1-6z" class="I"></path><path d="M736 373c3 4 5 8 8 12l1 6c1 1 2 2 2 3v1l-1 1-2 1h0c0-1 0 0-1-1l-1 1c-1 1-2 2-3 2-1-2-3-3-5-4-2-2-4-5-5-7 1 0 2 1 3 1 0-1 0-2 1-3 0-1 1-2 2-3l3-2c0-3-2-5-2-8z" class="S"></path><defs><linearGradient id="n" x1="751.133" y1="223.84" x2="755.511" y2="252.807" xlink:href="#B"><stop offset="0" stop-color="#c1c1bf"></stop><stop offset="1" stop-color="#e4e2e1"></stop></linearGradient></defs><path fill="url(#n)" d="M754 222c1-1 5-3 6-3h4 0c2 2 4 4 7 5l2 1 2 1-3 3c-2 2-6 7-9 7-2 2-2 2-2 4l1 1c-1 2-5 7-6 7-2 0-3 1-4 3 1-3 5-7 5-10l-12 12-4 2h0-1l1-4 1-1 1-2-1-2v-1c1 0 1 1 2 1 0-2 0-1 1-2v-1c-2-1-4-3-6-5l-2 1h-2c-1-1-1-1-2-1 1 0 2-1 3-2l-3-1c2-2 4-4 5-6 1 1 1 2 1 3h2c1-1 2-3 3-5l4-3c2-1 4-2 6-2z"></path><path d="M744 227l4-3c2-1 4-2 6-2-1 2-1 3-3 4s-13 8-14 10l1 1c-1 0-1 0-2 1l-1 1c-1-1-1-1-2-1 1 0 2-1 3-2l-3-1c2-2 4-4 5-6 1 1 1 2 1 3h2c1-1 2-3 3-5z" class="j"></path><path d="M739 238c1-2 3-4 5-5 3 0 5 2 6 4l1 2-4 4h-2c-2-1-4-3-6-5z" class="F"></path><path d="M754 222c1-1 5-3 6-3h4 0c2 2 4 4 7 5l2 1 2 1-3 3c-2 2-6 7-9 7-2 2-2 2-2 4l1 1c-1 2-5 7-6 7-2 0-3 1-4 3 1-3 5-7 5-10 1-3 5-6 7-9 2-2 3-5 4-8-1-1-3-2-4-3l-1 1h-2c-1-1-1 0-2-1 0 1 0 2 1 3v1l1 1v1c0 4 0 5-3 8l-6-3c-1-1-2-2-2-4l2-1-1-1c2-1 2-2 3-4z" class="D"></path><path d="M771 224l2 1 2 1-3 3c-2 2-6 7-9 7 1-2 3-4 5-6 0-1 2-3 2-4 1 0 0-2 1-2z" class="g"></path><path d="M792 229c1 1 2 2 4 2l2 2h1l1 2h0l-2-1c0 1 0 1 1 1l1 3v1l-1-1c0 1 0 1 1 2v1c0 1 0 1 1 2h-1-1l2 2v1s0 1 1 1h-1v1l3 4c1 0 2 1 3 2s2 1 2 2h-1c0 2 1 2 1 4l-1-1c-1-1-3-2-4-2l-2-1c-1 0-2 0-3-1v1h-2c-3 1-6 2-8 4l-1 2h-2l-3 6h-3 0c-2-2-3-3-4-5l-1-1c-2 0-3-2-4-2h-1l2 2h0c-2-2-7-3-10-5h0c0-1-1-1-2-2h0v-1h1v-1l1-1c-1-1-1-1-1-2 1 0 1 0 2-1v-1h2v-2h2l-1-1 2-1v-2c2 0 2-1 3-1 1-1 2-2 4-3l4-3 6-3c2-1 4-1 6-1h1v1h1c-1-1-1-1-1-2v-1z" class="e"></path><path d="M793 255l2 1h1l-1-2h0c1 0 2 1 3 1v-1l-3-3 8 5c-2-2-4-3-5-5 2 1 3 2 5 3h0c-1-3-5-6-7-8 3 2 5 4 7 6h1c1 0 2 1 3 2s2 1 2 2h-1c0 2 1 2 1 4l-1-1c-1-1-3-2-4-2l-2-1c-1 0-2 0-3-1v1h-2c-3 1-6 2-8 4l-1 2h-2l1-1c1-1-1-1-1-2h2 0v-2s1 0 2 1v-1-1h3c0-1-4-3-5-4l5 3z" class="b"></path><path d="M762 252h1c1-1 1-1 1-2v-1l2-1v-1h2c0-1 0-1 1-1s1-1 2-1c0-1 1-1 1-1 1 0 2-2 3-2l4-4c1 1 2 2 4 3l-3-3 1-1c1 2 3 3 5 4l4 3c-1-1-3-4-5-4-1-1-2-2-3-2l1-1c2 1 4 2 6 4 1 2 3 3 4 4 1 0 2 1 3 2h-1c-1 0-2-1-4-1l2 1 4 4c-2-1-3-2-5-3h-2c-2-1-3-1-5-2l-2-1v1c1 1 3 2 4 3v1c-2-1-4-2-5-4-1 0-1-1-2-1l-1-1h-1l2 2v1l-2-1v1h0-1l-1-1c-1 1-1 1-1 2l-2-1v-1c-2 1-3 2-4 2-1 1-2 1-3 1v1h0l-1 1-1 1-1 1v2h-3v-1h1v-1l1-1z" class="M"></path><path d="M763 255v-2l1-1 1-1 1-1h0v-1c1 0 2 0 3-1 1 0 2-1 4-2v1l2 1c0-1 0-1 1-2l1 1h1 0v-1l2 1v-1l-2-2h1l1 1c1 0 1 1 2 1 1 2 3 3 5 4v-1c-1-1-3-2-4-3v-1l2 1c2 1 3 2 5 3 1 0 3 1 4 2l-1 1v-1c-1 0-2-1-4-1h0l5 4-1 1-5-3c1 1 5 3 5 4h-3v1 1c-1-1-2-1-2-1v2h0-2c0 1 2 1 1 2l-1 1-3 6h-3 0c-2-2-3-3-4-5l-1-1c-2 0-3-2-4-2h-1l2 2h0c-2-2-7-3-10-5h0c0-1-1-1-2-2h0 3z" class="G"></path><path d="M763 255l1 1h0l1-2h0l1-1c1-1 3-2 4-2h3c-1 0-2 0-2 1 1 1 0 1 2 1l1 1h2c1 0 3 1 3 2 1 1 0 1 0 1 2 2 3 3 4 3h1l-1-1 1-1 3 3-1 1-3 6h-3 0c-2-2-3-3-4-5l-1-1c-2 0-3-2-4-2h-1l2 2h0c-2-2-7-3-10-5h0c0-1-1-1-2-2h0 3z" class="N"></path><path d="M776 263c1 2 3 3 5 4-1-1-4-4-4-5l2 1h1c-1-2-2-3-3-4h0s1 0 2 1c0 0 1 0 2 1v-1c-1-1-3-2-5-4 1 0 2 1 3 1 2 2 3 3 4 3h1l-1-1 1-1 3 3-1 1-3 6h-3 0c-2-2-3-3-4-5z" class="M"></path><path d="M733 238c1 0 1 0 2 1h2l2-1c2 2 4 4 6 5v1c-1 1-1 0-1 2-1 0-1-1-2-1v1l1 2-1 2-1 1-1 4h1 0l4-2c-1 3-3 5-4 7-2 3-3 6-6 9-1 5-4 7-5 12l-3 6c0 1-1 4-1 5l-2 11c-1 1-3 5-2 6 0 2 1 3 2 5h0v3c-1 1-1 1-2 1l-1-2c-5-5-8-13-11-19l2-1h0c-2-2-2-6-2-9 1-2 1-5 3-7l3-11c0-7 6-18 10-23 1-1 2-2 2-3h0l2-2c0-1 2-2 3-3z" class="T"></path><path d="M736 245h2l-1-2c1 0 2 0 3-1l2 2v1 1l1 2-1 2c-1-2-4-4-6-5z" class="B"></path><path d="M733 238c1 0 1 0 2 1l-3 3c-1 1 0 1-1 1-1 2-2 3-3 4l-2-1c1-1 2-2 2-3h0l2-2c0-1 2-2 3-3z" class="N"></path><path d="M726 246l2 1-12 22c0-7 6-18 10-23z" class="d"></path><path d="M717 286c2 0 3 0 5 1 1 1 1 1 1 3s-1 2-2 3c-2 1-2 1-4 0-1 0-2-1-2-2 0-2 0-4 2-5z" class="F"></path><path d="M718 288h1l2 2-1 1c-1 0-2 0-3-1 0-1 0-1 1-2z" class="e"></path><path d="M731 249h1l1-3c1-1 2-1 3-1 2 1 5 3 6 5l-1 1-1 4h-2c-2 0-4-1-5-3-1-1-1-1-2-3z" class="h"></path><path d="M737 251c-1-1-1-1-2 0h-3c1-2 1-2 3-3 0 1 0 1 1 1v1l1-1c1-1 2 0 3 0l-1 3c-1 0-1 0-2-1z" class="L"></path><path d="M740 249l1 2-1 4h-2c-2 0-4-1-5-3 1 0 0 0 1 1h2l1-2c1 1 1 1 2 1l1-3z" class="i"></path><path d="M712 296h0c-2-2-2-6-2-9 1-2 1-5 3-7v3h0v1c-1 2 0 6 1 8l1 1v2h2c1 0 2 0 4 1l1 1h1v-1l1-1h0c0-2 1-2 2-3l-2 11c-1 1-3 5-2 6 0 2 1 3 2 5h0v3c-1 1-1 1-2 1l-1-2c-5-5-8-13-11-19l2-1z" class="R"></path><path d="M721 296l1 1h1v-1l1-1h0c0-2 1-2 2-3l-2 11c-4 0-2-2-4-3h-1c-1-1-2-1-2-2 1-1 2-1 3-2h1z" class="H"></path><path d="M712 296c2 3 3 6 5 9v1l3 6c1 2 1 3 1 4-5-5-8-13-11-19l2-1z" class="N"></path><path d="M735 269c-1 1-3 5-5 5-1 1-3 0-4 1h0v1c-1-1-1-1-2-1 0-1-1-2-2-2h-2c1-1 1-1 2-1s1 0 2-1c0-1 1 0 2 0v-1l-1-1h1v-1l-2-2h-1 1v-1-1-1l1-2v-1c1-1 0-1 1-2-1 0-2 0-2-1l3-5v-2c1-1 2-3 4-4v3c1 2 1 2 2 3 1 2 3 3 5 3h2 1 0l4-2c-1 3-3 5-4 7-2 3-3 6-6 9z" class="k"></path><path d="M726 259c1 0 1-1 2-1 2 0 4 1 5 3 1 0 1 1 0 2v1-1c-1-1-1-1-2-1s-2-1-3-1h0v-1l-2-1z" class="h"></path><path d="M726 259l2 1v1c-1 1-1 2 0 3l1 2 1 1c-1 0-2-1-3-1-1-1-2-1-2-2 0-2 1-3 1-5z" class="a"></path><path d="M728 261h0c1 0 2 1 3 1s1 0 2 1v1c-1 1-1 2-2 3h-1l-1-1-1-2c-1-1-1-2 0-3z" class="L"></path><path d="M702 293c0 2 0 5 1 7 1 3 1 6 2 8l1 4c0 1 1 2 2 2 0 4 2 7 3 11h0l5 12 11 22 9 14c0 3 2 5 2 8l-3 2c-1 1-2 2-2 3-1 1-1 2-1 3-1 0-2-1-3-1l-5-6c-1-2-2-3-4-4l-1-2-1-2-1-1c-1-2 0-1-1-2l-2-2-1-2c-1-2-3-4-4-7 0-1 0-1-1-2l-1-2c0-1-1-2-1-3l-1-1-5-9v-1l-2-4c0-1 0-1-1-2-1-2-3-5-4-7v-1-1h1v-3c1 0 2 0 3-2l1-1c1 0 1-1 2-1s1-1 2-1l-1-4c0-1-1-1-1-1 1-2 1-3 0-5-1-3-3-3-6-4 1 0 2 0 3-1h1c1 0 1 1 2 1l1 1v-1-1c-1-1-1-1-1-2l1-2-1-2 1-2v-2l1-1z" class="o"></path><path d="M702 337c1-1 2-1 3 0v1l-1 1c-1 1-1 1-2 1v-3z" class="v"></path><path d="M711 325l5 12 11 22-2-2c-1-2-3-2-4-5 0-1-1-1-2-2v-3c-1-3-3-5-4-8-1 0-2-4-2-4-1-1-1 0-2-1v-2-7z" class="Z"></path><path d="M706 346c-1-1-1-2-2-3 0-1 1-2 2-3s3-2 5-2c1 1 1 3 2 4 0 1 1 2 0 3 0 2-2 3-3 3h-3l-1-2z" class="i"></path><path d="M706 346c-1-1-1-2-2-3 0-1 1-2 2-3s3-2 5-2c1 1 1 3 2 4l-1 2c-1-1-1-2-2-3 0-1 0-1-1-2l-1 1h0c-1 1-1 2-2 2l1 3s-1 0-1 1z" class="a"></path><path d="M702 293c0 2 0 5 1 7 1 3 1 6 2 8l1 4c0 1 1 2 2 2 0 4 2 7 3 11l-1-1c-1-1-1-3-3-3v-2h-1v2c0 1 1 2 1 3v3l1 1c0 1-1 1-1 2h-1v1c-1 0-2 1-3 1h0c-1 1-2 1-3 1l-1-1h-2c0 2 1 2 1 4l1 2c1 1 1 0 1 1 1 1 1 3 2 4 0 1 0 2 1 3h0l7 13 3 6c2 2 3 4 4 6 3 5 6 9 10 13l3 3h1v-1c0-2 2-2 1-4 0-1 0-1-1-1s-2 0-2-1h-1c-1 0-2-1-3-2 0-1-1-2-2-3s0 0 0-1h-1c0-1 0-2-1-2l1-4h2c2 1 3 0 4 1l-1 2 1 1-1 1h1v2l2 2-1 1c1 1 2 1 4 1 2 1 2 3 5 2l-3 2c-1 1-2 2-2 3-1 1-1 2-1 3-1 0-2-1-3-1l-5-6c-1-2-2-3-4-4l-1-2-1-2-1-1c-1-2 0-1-1-2l-2-2-1-2c-1-2-3-4-4-7 0-1 0-1-1-2l-1-2c0-1-1-2-1-3l-1-1-5-9v-1l-2-4c0-1 0-1-1-2-1-2-3-5-4-7v-1-1h1v-3c1 0 2 0 3-2l1-1c1 0 1-1 2-1s1-1 2-1l-1-4c0-1-1-1-1-1 1-2 1-3 0-5-1-3-3-3-6-4 1 0 2 0 3-1h1c1 0 1 1 2 1l1 1v-1-1c-1-1-1-1-1-2l1-2-1-2 1-2v-2l1-1z" class="Z"></path><path d="M731 332c0-1 0-1 1-2h1c-1-1-1-2-2-3h2l-1-2h1l-1-1 1-1c-1-2 0-8 0-11v-1-3c1 0 1-1 1-2h0l1-1v-1-1h1l-1-1 1-1v-1l1-1c-1-1-1-1-1-2h1v-2c1 0 1 0 1-1l-1-1 2-1-1-1 2-1c-1-1-1-1-1-2l2-1v-1-2c1-1 1-1 2-1v-1c-1 0 0 0-1-1h2v-1l-1-1h2l-1-1c1 0 1 0 2-1 0 0-1-1-1-2h2v-1l-1-1h2c0-1 0-1-1-2h2c-1-1-1-1-1-2h2c0-1 0-1 1-2s1-1 1-2l2-2v-1c1 0 3-3 4-4l1-1-1-2 2 1v-1c1 1 2 1 2 2h0c3 2 8 3 10 5h0l-2-2h1c1 0 2 2 4 2l1 1c1 2 2 3 4 5h0 3c0 2 0 3-2 4h1v4h-1-1l1 2h2c-1 2-1 3-1 5l1 2c0 1 0 0-1 1l1 1-2-1c0 1 0 2 1 3h-1v1l1 1c-1 1-1 1-2 1 0 1 1 1 2 2h-1-2v1c-4 0-6 2-9 5-1 1-2 2-3 4h0c-1 3-1 6 0 9l3 3-2 2c1 2 2 3 1 5l2 4-1 1 1 1-1 1c1 0 1 1 1 1v3c0 1 2 2 2 3h0c1 0 1 0 1 1s0 1-1 2-1 1-2 1l-1 1c-1 0-1-1-2-2-1 2-1 3-1 5l-3-4c-1-1-3-2-4-2-4-1-6 0-9 2l-2 2c-2 0-2 0-4 1h0l-1 2h-3-1c-1-1-3-2-4-3-1-2-2-4-4-5l-2-3h1c0-1 0-1-1-2h1v-1z" class="H"></path><path d="M753 314c-1 0-1-1-2-1l1-1 2 1c0-2-2-2-2-3h2 0c-1-1-2-2-2-3l2 1v-1l-1-1c3 0 3-2 4-4h2 1 0l1-2c-1 0-1 0 0-1h1l-1-1v-1l1-1-2-1v-1-1h1l1 1c0 2 2 3 3 4-1 1-1 0-2 0 0 1 1 1 2 2v1h-1 0l1 1-1 1-1-1v1c-1 1-2 1-2 1v2l-1 1c0 1 1 1 2 2l1 1c-1 0-2 0-3-1 1 1 1 2 2 3h0l-3-1h0l1 1-1 1c-1-1-2-1-3-1l3 3v1l-2-1c-1-1-2-1-3-1h-1 0z" class="I"></path><path d="M745 330v-1c-1 0-2-1-3-2 0 1 0 2-1 2s-2-1-3-2v-4h0c-1-1 0-1-1-1v-1h-1l2-2c-1-1-1-1 0-2l-1-1v-4-2h0l1-1v-1-1l1-1v-2l1-1v-2l1-1c0-2 2-3 2-5v-1-1l2-2-1-1h2l-1-1 1-1-1-1 1-1 3-4c-1-1 0-1-1-2h2l1-1h1l-1-1 2-2h1l-1-1c1-2 1 0 2 0 0-1-1-2-1-3l3 1c1 1 2 2 2 3l1 1 1 2h0l-2-1-1 1-1-2-1 1c1 1 2 1 3 2h1l1 2h-1c0 2 2 2 3 3h-2 0l1 2v1l-2 1-3-2 1-1h0c0-2-3-3-5-4l-1-1h2c1 0 2 2 4 2h0c-1-2-4-3-5-5l-1 2c-1 1-1 0-1 1h-1l-2 5v1 2l-1 1 1 1c-1 1-3 2-3 3s-1 1-1 2 1 1 1 2h-1v2c-1 0-2 2-3 2v4c-1 2-3 5-2 7l1 2c1 2 3 3 5 4v-1l-3-2h1c2 0 4 1 6 2v1c2 2 4 3 6 4h1c1 0 2 1 3 2h0c-1 0-2 0-3-1h-1c-2-1-5-4-7-3 2 1 3 3 5 4s4 1 6 2h-2l-1-1c-2 1-3 0-5-1l1 1c0 1 2 1 3 2h0c-2 0-3-1-4-1 0 1 2 2 3 3-1 0-2 0-3-1l-1-1h-1l4 4c-2-1-5-2-8-4 1 2 4 3 5 4h-1l-1-1c-1 0-2-1-3-1z" class="Y"></path><path d="M749 328c-1 0-4-1-4-2-1-1-2-2-2-3h0l1 1 1-1-1-1h1 1c2 0 4 1 5 3l1 1c0 1 2 1 3 2h0c-2 0-3-1-4-1 0 1 2 2 3 3-1 0-2 0-3-1l-1-1h-1z" class="C"></path><path d="M751 325c2 1 3 2 5 1l1 1h2c1 0 3 2 4 2l4 3c2 1 3 2 4 3-1 2-1 2-1 3l-2-1v2l-2-1-1 2c-1 1-1 0-1 1-1-1-3-2-4-2-4-1-6 0-9 2l-2 2c-2 0-2 0-4 1h0l-1 2h-3-1c-1-1-3-2-4-3-1-2-2-4-4-5l-2-3h1c2 1 4 4 5 4h1 1 0c0-1-1-2-2-2l1-2 1-1c1 2 2 2 4 2 0-2 0-3-2-4h0c2 1 3 2 5 4v-2h1v-1h-1l1-2h0l-1-1c1 0 2 1 3 1l1 1h1c-1-1-4-2-5-4 3 2 6 3 8 4l-4-4h1l1 1c1 1 2 1 3 1-1-1-3-2-3-3 1 0 2 1 4 1h0c-1-1-3-1-3-2l-1-1z" class="j"></path><path d="M745 330c1 0 2 1 3 1l1 1h1c1 0 2 1 2 1-1 1-1 1-2 0 0 1 0 2 1 2v1l-2-1-2 2h-2l1-2c1 0 2-1 3-2l-3-2h0l-1-1z" class="W"></path><path d="M740 332c2 1 3 2 5 4v-2h1v-1h-1l1-2 3 2c-1 1-2 2-3 2l-1 2h0l1 1v1c-1 0-2-1-3-1l-1-2c0-2 0-3-2-4h0z" class="J"></path><path d="M736 339h1 1 0c0-1-1-2-2-2l1-2 1-1c1 2 2 2 4 2l1 2h0l2 2v1h-1l1 2c-2 0-2 0-4-1-1-1-3-2-5-3z" class="N"></path><path d="M745 344c0-2 2-4 3-5 2-1 5-3 7-3 1 1 1 1 2 1-1-1-1-2-1-3 0 0 1 1 2 1v-1l-3-3c2 1 4 2 6 4l-1 1c1 1 1 1 1 2l-1 1c-4-1-6 0-9 2l-2 2c-2 0-2 0-4 1h0z" class="e"></path><path d="M761 335c1 0 2 1 3 0l-2-1c-1 0-2-1-2-3 2 2 4 3 6 4l1-1-2-1c-3-1-4-2-6-5 2 2 5 4 7 4h1c2 1 3 2 4 3-1 2-1 2-1 3l-2-1v2l-2-1-1 2c-1 1-1 0-1 1-1-1-3-2-4-2l1-1c0-1 0-1-1-2l1-1z" class="q"></path><defs><linearGradient id="o" x1="773.59" y1="296.424" x2="769.043" y2="295.114" xlink:href="#B"><stop offset="0" stop-color="#5e5e5f"></stop><stop offset="1" stop-color="#727371"></stop></linearGradient></defs><path fill="url(#o)" d="M781 290l1 1c-1 1-1 1-2 1 0 1 1 1 2 2h-1-2v1c-4 0-6 2-9 5-1 1-2 2-3 4h0c-1 3-1 6 0 9l3 3-2 2c1 2 2 3 1 5l2 4-1 1 1 1-1 1c1 0 1 1 1 1v3c0 1 2 2 2 3h0c1 0 1 0 1 1s0 1-1 2-1 1-2 1l-1 1c-1 0-1-1-2-2-1 2-1 3-1 5l-3-4c0-1 0 0 1-1l1-2 2 1v-2l2 1c0-1 0-1 1-3-1-1-2-2-4-3l-4-3c-1 0-3-2-4-2-2-1-4-1-6-2s-3-3-5-4c2-1 5 2 7 3h1c1 1 2 1 3 1h0c-1-1-2-2-3-2h-1c-2-1-4-2-6-4l10 4-1-1c-1-1-2-2-4-2 0-1 0 0-1-1l-4-2v-1c2 1 3 2 5 2 2 1 3 2 4 2-1-3-7-4-9-6 1-1 7 4 10 4-2-2-4-3-6-4h0 0 1c1 0 2 0 3 1l2 1v-1l-3-3c1 0 2 0 3 1l1-1-1-1h0l3 1h0c-1-1-1-2-2-3 1 1 2 1 3 1l-1-1c-1-1-2-1-2-2l1-1v-2s1 0 2-1v-1l1 1 1-1-1-1h0 1v-1l1-1v-1c1 0 1 0 2-1l3-3c2 0 4-1 5-2s1 0 3 0l-1-1 1-1c0 1 1 1 2 1v-1z"></path><path d="M768 326l-1-2-2-2-1-1c1 1 3 1 4 2h1l2 4-1 1-2-2z" class="K"></path><path d="M768 323l-3-4h0l1-1v-1-1c0-1-1-2-1-3v-1-2h0c0-2 1-4 1-6v-1l1 1c-1 3-1 6 0 9l3 3-2 2c1 2 2 3 1 5h-1z" class="b"></path><path d="M763 329c1 0 2 1 3 1h1 0c-1-1-1-2-2-3 1-1 1 1 2 1v-1l-5-4c2 0 4 2 6 3h0l2 2 1 1-1 1c1 0 1 1 1 1v3c0 1 2 2 2 3h0c1 0 1 0 1 1s0 1-1 2-1 1-2 1l-1 1c-1 0-1-1-2-2-1 2-1 3-1 5l-3-4c0-1 0 0 1-1l1-2 2 1v-2l2 1c0-1 0-1 1-3-1-1-2-2-4-3l-4-3z" class="V"></path><path d="M731 332c0-1 0-1 1-2h1c-1-1-1-2-2-3h2l-1-2h1l-1-1 1-1c-1-2 0-8 0-11v-1-3c1 0 1-1 1-2h0l1-1v-1-1h1l-1-1 1-1v-1l1-1c-1-1-1-1-1-2h1v-2c1 0 1 0 1-1l-1-1 2-1-1-1 2-1c-1-1-1-1-1-2l2-1v-1-2c1-1 1-1 2-1v-1c-1 0 0 0-1-1h2v-1l-1-1h2l-1-1c1 0 1 0 2-1 0 0-1-1-1-2h2v-1l-1-1h2c0-1 0-1-1-2h2c-1-1-1-1-1-2h2c0-1 0-1 1-2s1-1 1-2l2-2v-1c1 0 3-3 4-4l1-1-1-2 2 1v-1c1 1 2 1 2 2h0c3 2 8 3 10 5h-3-2c-1-1-1-1-2-1s-2 0-2-1l-1 1h1c1 1 0 1 1 2h0l-2-1v1c1 1 2 1 3 2v1h-1c-1-1-2-2-4-2 1 1 2 1 2 2 1 0 1 0 1 1h0c-1 0-2-1-3-1v1c1 0 2 1 2 1-1 0-3 0-4-1l-1 1 2 2h0-2v1l1 1h-1l-1-1-1 1 2 1-3-1c0 1 1 2 1 3-1 0-1-2-2 0l1 1h-1l-2 2 1 1h-1l-1 1h-2c1 1 0 1 1 2l-3 4-1 1 1 1-1 1 1 1h-2l1 1-2 2v1 1c0 2-2 3-2 5l-1 1v2l-1 1v2l-1 1v1 1l-1 1h0v2 4l1 1c-1 1-1 1 0 2l-2 2h1v1c1 0 0 0 1 1h0v4c1 1 2 2 3 2s1-1 1-2c1 1 2 2 3 2v1l1 1h0l-1 2h1v1h-1v2c-2-2-3-3-5-4h0c2 1 2 2 2 4-2 0-3 0-4-2l-1 1-1 2c1 0 2 1 2 2h0-1-1c-1 0-3-3-5-4 0-1 0-1-1-2h1v-1z" class="G"></path><g class="D"><path d="M738 334c-1 0-1-1-1-2l2 1-1-1c-1-1-2-2-2-3 1 0 3 2 4 3 2 1 2 2 2 4-2 0-3 0-4-2z"></path><path d="M740 332c-1-1-4-5-5-6v-6c0-1 1-3 0-4 0-1 1-1 0-2l1-1v-1-2-1l1-1c0-1-1-1-1-2h2l-1-1 1-1-1-1c1-1 2-3 2-5h0c1-1 1-1 1-2l1-2c0-1 0-2 1-2v-1c0-1 1-2 2-2l-1-1c1-1 1-2 1-3l2-2h0c0-1 0-2 1-2h0v-1c1-1 1-1 1-2h1v-1l1-1h0v-2h2v-2l1 1c0-2-1-2-1-3h1v-1-1h1l-1-1 1-1v1l2-2v-1h2c-1-1-1-1-1-2h2l-1-1c1 0 1 0 2-1 0 0-1 0-1-1h1 2c-1-1-1-1-1-2h1c3 2 8 3 10 5h-3-2c-1-1-1-1-2-1s-2 0-2-1l-1 1h1c1 1 0 1 1 2h0l-2-1v1c1 1 2 1 3 2v1h-1c-1-1-2-2-4-2 1 1 2 1 2 2 1 0 1 0 1 1h0c-1 0-2-1-3-1v1c1 0 2 1 2 1-1 0-3 0-4-1l-1 1 2 2h0-2v1l1 1h-1l-1-1-1 1 2 1-3-1c0 1 1 2 1 3-1 0-1-2-2 0l1 1h-1l-2 2 1 1h-1l-1 1h-2c1 1 0 1 1 2l-3 4-1 1 1 1-1 1 1 1h-2l1 1-2 2v1 1c0 2-2 3-2 5l-1 1v2l-1 1v2l-1 1v1 1l-1 1h0v2 4l1 1c-1 1-1 1 0 2l-2 2h1v1c1 0 0 0 1 1h0v4c1 1 2 2 3 2s1-1 1-2c1 1 2 2 3 2v1l1 1h0l-1 2h1v1h-1v2c-2-2-3-3-5-4z"></path></g><defs><linearGradient id="p" x1="772.339" y1="279.673" x2="756.876" y2="278.461" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#c4c4c2"></stop></linearGradient></defs><path fill="url(#p)" d="M772 262h0l-2-2h1c1 0 2 2 4 2l1 1c1 2 2 3 4 5h0 3c0 2 0 3-2 4h1v4h-1-1l1 2h2c-1 2-1 3-1 5l1 2c0 1 0 0-1 1l1 1-2-1c0 1 0 2 1 3h-1v1 1c-1 0-2 0-2-1l-1 1 1 1c-2 0-2-1-3 0s-3 2-5 2l-3 3c-1 1-1 1-2 1v1l-1 1c-1-1-2-1-2-2 1 0 1 1 2 0-1-1-3-2-3-4l2 2h1l-2-2h0c-2-1-3-2-4-4l2 1v-1l-1-1 2-1v-1l-1-2h0 2c-1-1-3-1-3-3h1l-1-2h-1c-1-1-2-1-3-2l1-1 1 2 1-1 2 1h0l-1-2-1-1c0-1-1-2-2-3l-2-1 1-1 1 1h1l-1-1v-1h2 0l-2-2 1-1c1 1 3 1 4 1 0 0-1-1-2-1v-1c1 0 2 1 3 1h0c0-1 0-1-1-1 0-1-1-1-2-2 2 0 3 1 4 2h1v-1c-1-1-2-1-3-2v-1l2 1h0c-1-1 0-1-1-2h-1l1-1c0 1 1 1 2 1s1 0 2 1h2 3z"></path><path d="M763 285c1 0 2 0 2 2v2h-1v2h0l-1 1h1l1 3c0-1-1-1-2-1h0 0c-2-1-3-2-4-4l2 1v-1l-1-1 2-1v-1l-1-2h0 2z" class="I"></path><path d="M764 292c2 0 3-1 5-2 0-3 2-4 4-6-1 2-2 4-2 6-1 2-2 3-3 5-1 0 0 0-1 1l1 1c-1 1-1 1-2 1v1l-1 1c-1-1-2-1-2-2 1 0 1 1 2 0-1-1-3-2-3-4l2 2h1l-2-2h0c1 0 2 0 2 1l-1-3z" class="W"></path><path d="M764 271h0 1c2 1 4 3 4 5l1 1 1-1s0-1 1-1c0 0 0 1 1 2l1 1-2 3h-1 0-1l-1 1v-1h-1v1c-1 0-2-1-2-2l-1 1-1-1v-1l1-1-1-1v-1c0-1-1-1 0-2h1-1c-1-1 0-2 0-3z" class="I"></path><path d="M771 281c0-1-1-1-1-2h-2s-1-1-1-2c1 1 1 1 2 0l-2-2h1l1 1 1 1 1-1s0-1 1-1c0 0 0 1 1 2l1 1-2 3h-1 0z" class="l"></path><path d="M773 277h3l1 1 1 2c-1 1 0 1 0 2s-1 0-1 2v1h0l-3 3 1 2-1 1c-2 1-2 2-3 3l-3 3-1-1c1-1 0-1 1-1 1-2 2-3 3-5 0-2 1-4 2-6 1 0 1 0 1-1l-1-1-1-1 2-3-1-1z" class="j"></path><path d="M773 277h3l1 1-1 1 1 2-1 1c0 1 0 1-1 3l-1 1c-1 0-1 1-2 2 0 1-1 2-1 2 0-2 1-4 2-6 1 0 1 0 1-1l-1-1-1-1 2-3-1-1z" class="h"></path><path d="M774 278l1 1c0 1-1 2-2 3l-1-1 2-3z" class="W"></path><defs><linearGradient id="q" x1="774.995" y1="269.512" x2="763.996" y2="269.488" xlink:href="#B"><stop offset="0" stop-color="#999898"></stop><stop offset="1" stop-color="#b5b5b4"></stop></linearGradient></defs><path fill="url(#q)" d="M772 262h0l-2-2h1c1 0 2 2 4 2l1 1c1 2 2 3 4 5h0 3c0 2 0 3-2 4h1v4h-1-1l1 2h2c-1 2-1 3-1 5l1 2c0 1 0 0-1 1l1 1-2-1c0 1 0 2 1 3h-1v1 1c-1 0-2 0-2-1l-1 1 1 1c-2 0-2-1-3 0s-3 2-5 2c1-1 1-2 3-3l1-1-1-2 3-3h0v-1c0-2 1-1 1-2s-1-1 0-2l-1-2-1-1h-3c-1-1-1-2-1-2-1 0-1 1-1 1l-1 1-1-1c0-2-2-4-4-5h-1 0c-2 0-2 0-3-1l1-1v-1s-1-1-2-1v-1c1 0 2 1 3 1h0c0-1 0-1-1-1 0-1-1-1-2-2 2 0 3 1 4 2h1v-1c-1-1-2-1-3-2v-1l2 1h0c-1-1 0-1-1-2h-1l1-1c0 1 1 1 2 1s1 0 2 1h2 3z"></path><path d="M776 277c-2-1-3-2-3-4l1-1-1-1 1-1c1 0 2 0 3 1v2l2 3h0l-2-1h-1l1 2h-1z" class="N"></path><path d="M780 268h3c0 2 0 3-2 4h1v4h-1-1l1 2h2c-1 2-1 3-1 5l1 2c0 1 0 0-1 1l1 1-2-1c0 1 0 2 1 3h-1v1 1c-1 0-2 0-2-1l-1 1 1 1c-2 0-2-1-3 0s-3 2-5 2c1-1 1-2 3-3l1-1-1-2 3-3h0v-1c0-2 1-1 1-2s-1-1 0-2l-1-2-1-1h0 1l-1-2h1l2 1h0l-2-3c1 0 2 1 4 2l-1-1c-1-1-1-2-2-2l1-1h1c0-1 0-1-1-2h0c1 0 2 1 3 1v-1l-2-1z" class="P"></path><path d="M774 291l1-1-1-2 3-3 1 2c-1 1-2 1-2 2l1 1-1 1v-1l-2 1z" class="X"></path><path d="M782 286c0-1-1-1-2-2 0-1-1-2-1-3l1-1c0-1-1-2-1-3l1 1h1 2c-1 2-1 3-1 5l1 2c0 1 0 0-1 1z" class="F"></path><path d="M407 468l1 1c5-1 9 0 14 0l-1 3c1 0 2 0 3 1l1 3h-1c-1 4-2 9-5 12 2 1 3 2 4 3 2 2 4 3 5 5 2 3 4 5 5 8 1 2 2 5 3 8l1 8c0 8-1 14-4 21l-6 9-2 2 2 2s-1 1-1 2c0-1 1-1 1-1v-1c1-3 4-5 6-7 0 1 1 1 2 1 1 1 1 0 2 1h1 0c1 1 1 1 1 2l1 2c2 2 3 5 6 6l-1 1 2 3h1c1-1 2-1 3-1h0c2 0 3 0 5 1 1-1 1-1 3-1 1 2 2 3 3 4 0 1 1 1 1 2h1c1-1 1-3 1-4 1-1 2-2 3-2l1 1 1 1 1-1 1 1c1 1 2 2 3 2 2 1 3 2 5 2h3 1c1 0 2 0 3-1h0v-2h1 0v-4-7l1 1v21l1 15h-1v4c0 2 0 5 1 8-1 0-2 0-3 1-1 2-2 3-4 5-2 1-4 3-6 5 0 1-1 2-2 3l-6 6c-2 2-4 3-7 3-1 2-3 2-5 4 0 0-1-1-2-1l-1 1 1-2v-1l-1 1c-2-2-3-2-6-2v2h-1-1c-2 1-3 1-5 1l-1 1h-2v1l-1-2c-1-1-2 0-4 0h-1-4c-2 1-5 0-7 0h-3v-1h-2 0c-3 0-7-1-9-3h-1-1c-1-1-2-2-2-3v-1l-23 1c-3 0-6-2-9-3l-1 1h-2c-1 0-1-1-2-1-3-1-5-2-7-3-1-1-2-2-4-2 2 1 5 3 6 5-2 0-3-1-5-2 0-1-2-2-2-2l-7-7a54.5 54.5 0 0 1-12-24l-1-4v-6-6c0-2 0-5-1-7 1-1 2-2 2-3 1-2 0-4 1-6 1-3 2-4 4-6h1c1-2 3-3 4-4l2-2c0-12 0-22 3-33l1-2c1-5 4-8 7-12l3-2 3-3 2-2 5-4 8-6 6-3c2-1 4-1 6-2l16-3z" class="m"></path><path d="M382 605l3 5c-1 1-1 1-3 1h-1 0c-1-1-1-1-2-1-1-2-2-2-4-3l1-1c1-1 2-1 3-1 1 1 2 0 3 0z" class="c"></path><path d="M373 572c1 0 0 0 1 1h1c0-1 1-2 2-3 0 2-1 3-1 4-3 4-2 10-2 15v7c-3-7-2-17-1-24z" class="G"></path><path d="M354 595c0-1 1-2 1-3 3 3 5 5 8 6 2 1 3 2 4 2 2 1 3 2 5 3h-3-2-2 0c1 1 2 2 3 2 2 1 3 1 4 2v1c1 0 1 1 2 1h1v-2c2 1 3 1 4 3 1 0 1 0 2 1h0-4l-5-2c-7-2-14-7-18-14z" class="K"></path><path d="M367 596h1v-2l-1-2v-3c0-1-1-2-1-3s0-1-1-2l1-2c-1 0-1-1-1-2h1l-1-1v-1c-1-1-1-1 0-2l-1-2 1-1c2-2 2-3 4-3 0 0 1 1 1 2h-3v1l2 2h-1c0 1 0 1-1 2 1 1 1 1 1 3l-1 1c1 1 1 1 1 2 1 0 0 2 0 2 0 2 1 3 1 5 0 3 2 10 4 11l2 2v1l-1-1h-2c-2-1-3-2-5-3 0-1-1-2-2-3h2v-1z" class="L"></path><path d="M354 595h0c4 7 11 12 18 14l5 2c-2 1-3-1-5 0 0 1 1 2 1 3h-2 0c0 1 1 2 1 3v1h-2c-2-1-3-2-4-3-2-2-4-5-5-8l-7-11-1-1h1z" class="H"></path><path d="M361 607c3 1 7 4 10 6v1h0c0 1 1 2 1 3v1h-2c-2-1-3-2-4-3-2-2-4-5-5-8z" class="C"></path><path d="M374 589c0-5-1-11 2-15v1 2c0 5 1 10 1 14l1 1v-1c1-1 2-1 4-1h1c1 0 1 0 3 1h2c1 1 2 2 2 4 0 1-1 2-3 2h-1l-1 2v1c1 4 4 8 7 12h-1c-2 1-2 0-4-1-1 0-1-1-2-1l-3-5c-1-1 0-1-1-1-3-3-5-5-7-8v-7z" class="R"></path><path d="M374 589h1v4c0 1 0 1 1 2h0c1 2 3 5 4 6 1-1 2-2 2-3v-4l1-1c0 1 1 2 1 3v1s0 1 1 2v1c1 4 4 8 7 12h-1c-2 1-2 0-4-1-1 0-1-1-2-1l-3-5c-1-1 0-1-1-1-3-3-5-5-7-8v-7z" class="C"></path><path d="M384 597s0 1 1 2v1c1 4 4 8 7 12h-1c-2 1-2 0-4-1 0-3-2-7-3-10-1-2-1-3 0-4z" class="Q"></path><path d="M353 567c1-1 3-5 4-6 1 1 1 2 2 2l1 1c1 2 2 3 4 4 1 0 0 0 1-1h2 1 2v-1-1h1v2c0 1-1 2-2 3-2 0-2 1-4 3l-1 1 1 2c-1 1-1 1 0 2v1l1 1h-1c0 1 0 2 1 2l-1 2c1 1 1 1 1 2s1 2 1 3v3l1 2v2h-1v1h-2c1 1 2 2 2 3-1 0-2-1-4-2-3-1-5-3-8-6 0 1-1 2-1 3h0c-1-1-1-2-1-3-1-2-2-4-4-5 1-2 0-6 0-8 0-3 1-5 2-8l1-3 1-1z" class="j"></path><path d="M355 574h2l1 2h1l-1-2c2 0 4 4 6 5h-2l1 3v1h-3l-1-4c-1 0-3-4-4-5z" class="C"></path><path d="M353 567c1-1 3-5 4-6 1 1 1 2 2 2l1 1-1 1 1 1c-1 0-2 0-2-1l-1 1c0 1 1 3 2 4-2 0-2 1-3 0l-3-3z" class="V"></path><path d="M352 568l3 5v1c1 1 3 5 4 5l-1 3c-1 0-3-3-4-4v-1c-1-1-2-2-2-3s-1-2-1-3l1-3z" class="K"></path><path d="M349 587c1-2 0-6 0-8 0-3 1-5 2-8 0 1 1 2 1 3s1 2 2 3v1c1 1 3 4 4 4l1-3 1 4h3v-1s0 1 1 2c0 1 0 1 1 2v1 2 2 1h0c0 2 1 3 2 4v1h-2c1 1 2 2 2 3-1 0-2-1-4-2-3-1-5-3-8-6 0 1-1 2-1 3h0c-1-1-1-2-1-3-1-2-2-4-4-5z" class="X"></path><path d="M362 591c2 0 2 0 3 1 0 2 1 3 2 4v1h-2c1 1 2 2 2 3-1 0-2-1-4-2 1-1 0-1 0-1l-1-1c1-2 0-2-1-4 1 0 0 0 1-1h0z" class="P"></path><path d="M363 582s0 1 1 2c0 1 0 1 1 2v1 2 2 1h0c-1-1-1-1-3-1-1 0-3-1-3-2-1-1-1-3 0-4v-1l3 4c1-1-1-4-2-5h3v-1z" class="J"></path><path d="M349 587c1-2 0-6 0-8 0-3 1-5 2-8 0 1 1 2 1 3s1 2 2 3v1l-1 1 1 1c1 1 3 5 2 6v1 1l-1 1-1-1-1 1c1 1 1 2 2 3 0 1-1 2-1 3h0c-1-1-1-2-1-3-1-2-2-4-4-5z" class="q"></path><path d="M375 568c1-2 2-3 3-4 2 0 4 0 6 1h2c2 1 5 1 7 2 1 0 2 1 3 1l1-1 1 1c1 0 2-1 3-1-4 5-6 10-5 16 0 3 1 5 2 8h-1v-1l-1 1c-2-2-3-4-4-7v2l-2 1c1 3 0 5 1 8h-1c0-2-1-3-2-4h-2c-2-1-2-1-3-1h-1c-2 0-3 0-4 1v1l-1-1c0-4-1-9-1-14v-2-1c0-1 1-2 1-4-1 1-2 2-2 3h-1c-1-1 0-1-1-1l2-4z" class="H"></path><path d="M377 570c2-1 4-1 7-2 1 0 2 1 3 1 1 2 1 1 3 0h2c0 1-2 1-2 2-1 1 0 5-3 5 0-2 0-4-1-5-4 0-7 1-10 3 0-1 1-2 1-4z" class="g"></path><path d="M388 591v-2c-1-1-4-1-6-1-1-1-3-2-3-3-2-2 0-5-2-7v-1c1-1 1-2 3-2 1-1 2-1 4 0 1 0 2 0 3 1v4h0 2v-1c1 1 1 2 1 3v1c-1 1 0 3 0 4h0c1 3 0 5 1 8h-1c0-2-1-3-2-4z" class="T"></path><path d="M384 575c1 0 2 0 3 1v4c-2 1-3 1-4 1-1-1-1-1-2-3 1-2 1-2 3-3z" class="K"></path><defs><linearGradient id="r" x1="401.475" y1="575" x2="390.831" y2="581.165" xlink:href="#B"><stop offset="0" stop-color="#3b3b39"></stop><stop offset="1" stop-color="#5a5953"></stop></linearGradient></defs><path fill="url(#r)" d="M375 568c1-2 2-3 3-4 2 0 4 0 6 1h2c2 1 5 1 7 2 1 0 2 1 3 1l1-1 1 1c1 0 2-1 3-1-4 5-6 10-5 16 0 3 1 5 2 8h-1v-1l-1 1c-2-2-3-4-4-7-1-4 0-10 1-15h-1-2c-2 1-2 2-3 0-1 0-2-1-3-1-3 1-5 1-7 2-1 1-2 2-2 3h-1c-1-1 0-1-1-1l2-4z"></path><path d="M375 568h2c0-1 1-1 2-1l1-1h2v1l2-1c0 1 0 0 1 1h0l1-1 1 2h3 1 1l1 1h-1-2c-2 1-2 2-3 0-1 0-2-1-3-1-3 1-5 1-7 2-1 1-2 2-2 3h-1c-1-1 0-1-1-1l2-4z" class="M"></path><path d="M390 587l2-1 1 3c0 4 1 6 2 9 1 2 2 3 3 5l-1 1c3 2 5 6 9 6l1 1 6-2 7 1c0 2 0 2-1 4l-3 2h5l1-1v3c1 1 2 2 1 3 0 1-3 3-4 4-1 0-2 1-2 1h-2l1 2h0c-3 0-7-1-9-3h-1-1c-1-1-2-2-2-3v-1l-23 1c-3 0-6-2-9-3l-1-1h2v-1c0-1-1-2-1-3h0 2c0-1-1-2-1-3 2-1 3 1 5 0h4 1c2 0 2 0 3-1 1 0 1 1 2 1 2 1 2 2 4 1h1c-3-4-6-8-7-12v-1l1-2h1c2 0 3-1 3-2h1c-1-3 0-5-1-8z" class="L"></path><path d="M385 600c1 0 2 1 3 1 0 2 0 2 2 3h0c2-1 2-1 4-1l-3 2c2 2 4 4 7 4v1h2v1 1h-1c-2 0-3 1-4 2-2 0-3-1-3-2-3-4-6-8-7-12z" class="D"></path><path d="M377 611h4 1l4 1v1c-3 0-5 0-8-1h-1c2 1 3 0 4 2h-2v1h0l3 2h-2c-1-1-2 0-3 0 1 0 1 1 1 1h-3c1 2 3 3 5 4-3 0-6-2-9-3l-1-1h2v-1c0-1-1-2-1-3h0 2c0-1-1-2-1-3 2-1 3 1 5 0z" class="l"></path><path d="M390 587l2-1 1 3c0 4 1 6 2 9 1 2 2 3 3 5l-1 1c0-1-1-2-2-2l-1 1c-2 0-2 0-4 1h0c-2-1-2-1-2-3-1 0-2-1-3-1v-1l1-2h1c2 0 3-1 3-2h1c-1-3 0-5-1-8z" class="B"></path><path d="M413 609l7 1c0 2 0 2-1 4l-3 2h5l1-1v3c1 1 2 2 1 3 0 1-3 3-4 4-1 0-2 1-2 1h-2l1 2h0c-3 0-7-1-9-3h-1-1c-1-1-2-2-2-3v-1l-23 1c-2-1-4-2-5-4h3c2 0 4 1 5 1 5 0 9-1 14-2 2-1 3-2 4-3l2-1c1-1 3-2 4-2l6-2z" class="Y"></path><path d="M401 614l2 2v1c-2 1-4 1-6 0 2-1 3-2 4-3z" class="T"></path><path d="M406 625c-1-2-2-3-2-4 0-2 1-4 2-5 2-1 3-1 5-1 1 0 1 0 2 1l-2 1c-2 1-3 1-4 3s-1 3 0 5h0-1z" class="q"></path><path d="M413 609l7 1c0 2 0 2-1 4h-4c0-1-1-1-1-1-2-1-3 0-5 0-3 1-3 1-6 0 1-1 3-2 4-2l6-2z" class="Z"></path><path d="M416 616h5l1-1v3c1 1 2 2 1 3 0 1-3 3-4 4-1 0-2 1-2 1h-2l1 2h0c-3 0-7-1-9-3h0c-1-2-1-3 0-5s2-2 4-3c-1 2-2 4-2 6l1-1h1c0 1 0 1 1 2h1v-2l1-1c-1-1-1-2-2-3l1-1 1 1v-1h1v1l1-2z" class="i"></path><path d="M416 616h5l1-1v3l-2 2-3 3c-2 0-2 0-2-1l-1-1c-1-1-1-2-2-3l1-1 1 1v-1h1v1l1-2z" class="L"></path><path d="M350 539c1 0 1 0 2-1h2l1-1c1-1 1-1 2-1l1 2h2c2 4 5 8 8 12l-1 1h-1l-1 1c-3 3-6 6-8 9-1 1-3 5-4 6l-1 1-1 3c-1 3-2 5-2 8 0 2 1 6 0 8 2 1 3 3 4 5 0 1 0 2 1 3h-1l1 1 7 11c1 3 3 6 5 8 1 1 2 2 4 3l1 1-1 1h-2c-1 0-1-1-2-1-3-1-5-2-7-3-1-1-2-2-4-2 2 1 5 3 6 5-2 0-3-1-5-2 0-1-2-2-2-2l-7-7a54.5 54.5 0 0 1-12-24l-1-4v-6-6c0-2 0-5-1-7 1-1 2-2 2-3 1-2 0-4 1-6 1-3 2-4 4-6h1c1-2 3-3 4-4l2-2v1c2-1 2-1 3-2z" class="j"></path><path d="M343 590l1-1 1 4 2 5-1-1c-2-1-3-3-3-4v-3z" class="G"></path><path d="M334 574v1c1 1 1 2 1 3v3 1h1v-3-1c-1-1 1-2 1-3 0 4 0 10 1 14 1 3 2 6 4 9 1 2 5 8 5 10a54.5 54.5 0 0 1-12-24l-1-4v-6z" class="B"></path><path d="M347 586c2 2 1 7 3 9 0 0 2 1 2 2 0-3-2-5-2-7l-1-3c2 1 3 3 4 5 0 1 0 2 1 3h-1l1 1 7 11c1 3 3 6 5 8-3 0-5-3-8-3h0l-1-2-2-2c-3-1-6-7-8-10l-2-5c0-2 1-3 1-4 1-1 1-1 1-3z" class="R"></path><path d="M349 587c2 1 3 3 4 5 0 1 0 2 1 3h-1l1 1 7 11c1 3 3 6 5 8-3 0-5-3-8-3h0l-1-2h1c0-2-2-4-3-6 2 2 3 3 5 4h0c0-1-2-3-3-4-2-2-3-4-4-7h-1c0-3-2-5-2-7l-1-3z" class="J"></path><path d="M347 554c0-1 0-1 1-1 1-3 6-7 9-8l-3 3c-3 3-5 6-7 9 1 1 1 1 2 1-3 6-6 12-6 19 1 3 1 5 2 8 1 1 1 1 2 1h0c0 2 0 2-1 3 0 1-1 2-1 4l-1-4-1 1v3c-1-2-2-4-2-6-1-2 0-3-2-4v-1c-1-2-1-4-1-5 0-7 2-12 6-18 1-1 2-3 3-5h0z" class="N"></path><path d="M347 557c1 1 1 1 2 1-3 6-6 12-6 19 1 3 1 5 2 8 1 1 1 1 2 1h0c0 2 0 2-1 3 0 1-1 2-1 4l-1-4-1 1v-3l-1-1v-1-7c0-4 0-9 2-14 1-2 1-5 3-7z" class="M"></path><path d="M344 589c-1-4-1-8-1-12 1 3 1 5 2 8 1 1 1 1 2 1h0c0 2 0 2-1 3 0 1-1 2-1 4l-1-4z" class="B"></path><path d="M358 549c1-1 2-1 4-1v1l1 1c1 1 2 1 2 2-3 3-6 6-8 9-1 1-3 5-4 6l-1 1-1 3c-1 3-2 5-2 8 0 2 1 6 0 8l1 3c0 2 2 4 2 7 0-1-2-2-2-2-2-2-1-7-3-9h0c-1 0-1 0-2-1-1-3-1-5-2-8 0-7 3-13 6-19-1 0-1 0-2-1 2-3 4-6 7-9 1 1 1 1 2 1h2z" class="Z"></path><path d="M347 557c2-3 4-6 7-9 1 1 1 1 2 1h2l-2 1c-3 3-5 5-7 8-1 0-1 0-2-1z" class="F"></path><path d="M347 574l-1-1v-4c3-5 5-13 10-16 1-1 1-1 2 0-1 1-2 3-3 5l-3 3c-3 4-4 9-5 13z" class="s"></path><path d="M358 549c1-1 2-1 4-1v1l1 1c1 1 2 1 2 2-3 3-6 6-8 9-1 1-3 5-4 6l-1 1-1 3c-1 3-2 5-2 8 0 2 1 6 0 8l1 3c0 2 2 4 2 7 0-1-2-2-2-2-2-2-1-7-3-9h0v-12c1-4 2-9 5-13l3-3c1-2 2-4 3-5v-2l-2-1 2-1z" class="I"></path><path d="M350 539c1 0 1 0 2-1h2l1-1c1-1 1-1 2-1l1 2h2c2 4 5 8 8 12l-1 1h-1l-1 1c0-1-1-1-2-2l-1-1v-1c-2 0-3 0-4 1h-2c-1 0-1 0-2-1l3-3c-3 1-8 5-9 8-1 0-1 0-1 1l-3 1c-4 6-7 13-7 20 0 1-2 2-1 3v1 3h-1v-1-3c0-1 0-2-1-3v-1-6c0-2 0-5-1-7 1-1 2-2 2-3 1-2 0-4 1-6 1-3 2-4 4-6h1c1-2 3-3 4-4l2-2v1c2-1 2-1 3-2z" class="T"></path><path d="M333 561c1-1 2-2 2-3 1-2 0-4 1-6 1-3 2-4 4-6l-3 7 1 1v-1c1-1 2-2 4-3l-3 5c-2 4-3 9-5 13 0-2 0-5-1-7z" class="M"></path><path d="M350 539c1 0 1 0 2-1h2l1-1c1-1 1-1 2-1l1 2h0c-2 0-3 0-5 1-5 3-8 7-11 11-2 1-3 2-4 3v1l-1-1 3-7h1c1-2 3-3 4-4l2-2v1c2-1 2-1 3-2z" class="e"></path><path d="M360 538c2 4 5 8 8 12l-1 1h-1l-1 1c0-1-1-1-2-2l-1-1v-1c-2 0-3 0-4 1h-2c-1 0-1 0-2-1l3-3c-3 1-8 5-9 8-1 0-1 0-1 1l-3 1c3-4 8-11 13-14l1-3h0 2z" class="O"></path><path d="M360 538c2 4 5 8 8 12l-1 1h-1l-1-1c-1-1-2-3-3-4h-4c0-1 1-2 1-3l-2-2 1-3h0 2z" class="t"></path><path d="M426 556c0-1 1-1 1-1v-1c1-3 4-5 6-7 0 1 1 1 2 1 1 1 1 0 2 1h1 0c1 1 1 1 1 2l1 2c2 2 3 5 6 6l-1 1 2 3h1c1-1 2-1 3-1h0c2 0 3 0 5 1 3 2 5 3 6 7v2c0 4-1 9-4 12l-1 1c-4 2-9 4-12 7-1 1-1 2-2 2-3 4-6 7-11 10h0c-2 1-4 2-7 2h0c-2 1-3 1-4 1l-1 1 1 2h-1l-7-1-6 2-1-1c-4 0-6-4-9-6l1-1c-1-2-2-3-3-5-1-3-2-5-2-9l-1-3v-2c1 3 2 5 4 7l1-1v1h1c-1-3-2-5-2-8-1-6 1-11 5-16h1l1-1h1l1 1h3c2-3 6-2 9-3 2-1 3-3 5-5h0l-1-3 3-3 1-1 2 2s-1 1-1 2z" class="E"></path><path d="M440 590l2-1v2c-2 4-5 8-9 10 0-1 2-4 3-4 1-2 3-3 3-5l1-2z" class="k"></path><path d="M426 556c0-1 1-1 1-1v-1c1-3 4-5 6-7 0 1 1 1 2 1 1 1 1 0 2 1h1 0c1 1 1 1 1 2l1 2c2 2 3 5 6 6l-1 1 2 3h1c1-1 2-1 3-1h0l-3 3 1 1h1v1l-1 2c2 2 1 5 1 7h2c1-1 2-1 3-1v1c-2 1-3 2-5 2-2 3-3 6-4 10l-1 4c-1 1-1 2-2 2 0-2 1-5 2-7 1-6 2-12 1-17-1-8-5-15-11-20v1l1 2h0-4-1l2 2h-1-1v1h0c-1 1-2 1-3 1 0 1 0 0 1 2-1 0-1-1-2 0 1 1 2 1 2 3l-3-3h-1l-1-1c-1 0-1 0-2 1l-1-3 3-3 1-1 2 2s-1 1-1 2z" class="Q"></path><path d="M425 552l2 2s-1 1-1 2v1h1c-1 1-2 1-3 1s-1 0-2 1l-1-3 3-3 1-1z" class="t"></path><defs><linearGradient id="s" x1="415.915" y1="610.966" x2="398.124" y2="588.703" xlink:href="#B"><stop offset="0" stop-color="#3a3937"></stop><stop offset="1" stop-color="#60605b"></stop></linearGradient></defs><path fill="url(#s)" d="M392 584c1 3 2 5 4 7l1-1v1h1c4 7 9 12 17 14 3 1 7 1 10 1h0c-2 1-3 1-4 1l-1 1 1 2h-1l-7-1-6 2-1-1c-4 0-6-4-9-6l1-1c-1-2-2-3-3-5-1-3-2-5-2-9l-1-3v-2z"></path><path d="M393 589c4 9 10 17 20 20h0l-6 2-1-1c-4 0-6-4-9-6l1-1c-1-2-2-3-3-5-1-3-2-5-2-9z" class="C"></path><path d="M398 603c1 1 3 3 4 3 2 1 4 1 6 2 0 1-1 2-2 2-4 0-6-4-9-6l1-1z" class="Y"></path><path d="M451 562c2 0 3 0 5 1 3 2 5 3 6 7v2c0 4-1 9-4 12l-1 1c-4 2-9 4-12 7l1-4c1-4 2-7 4-10 2 0 3-1 5-2v-1c-1 0-2 0-3 1h-2c0-2 1-5-1-7l1-2v-1h-1l-1-1 3-3z" class="R"></path><path d="M450 566c1 0 1 0 2-1 1 1 2 1 3 1 1 1 2 1 2 3 0 1 1 3 0 4 0 1-1 2-2 2s-2 0-3 1h-2c0-2 1-5-1-7l1-2v-1z" class="K"></path><path d="M452 565c1 1 2 1 3 1v1 1c1 2 0 4-1 5l-1 1c0-1-1-1-1-2v-7z" class="f"></path><path d="M455 579c2-1 3-2 4-3 0-1 1-1 1-2l1-1v-1h1c0 4-1 9-4 12l-1 1c-4 2-9 4-12 7l1-4c2-2 2-2 3-4l1-1c2-1 4-2 5-4z" class="T"></path><path d="M455 579c1 1 2 2 2 3-1 1-3 2-5 3h-2l-1-1 1-1c2-1 4-2 5-4z" class="k"></path><defs><linearGradient id="t" x1="416.04" y1="579.834" x2="403.295" y2="592.249" xlink:href="#B"><stop offset="0" stop-color="#3f3e3a"></stop><stop offset="1" stop-color="#62625f"></stop></linearGradient></defs><path fill="url(#t)" d="M402 567l1-1h1l1 1h3l2 1c-4 3-5 4-6 9v1c-1 3 0 8 2 11 4 6 8 8 13 10 1-1 1 0 2-1s1 0 3-1l3-3c-3 0-6 1-8 0l-1-1c2 0 4 1 5 0 2-1 4-1 5-2l3-1 2 1 1-1c1 1 1 2 2 2h1 1a30.44 30.44 0 0 1-8 8c-1 1-2 1-3 1h-1c-3 2-9 1-13 0-5-2-9-6-12-11-3-7-2-14 1-20v-2-1z"></path><path d="M428 591l3-1 2 1 1-1c1 1 1 2 2 2h1 1a30.44 30.44 0 0 1-8 8c-1 1-2 1-3 1l5-6c-4 2-8 4-12 4h-1c1-1 1 0 2-1s1 0 3-1l3-3c-3 0-6 1-8 0l-1-1c2 0 4 1 5 0 2-1 4-1 5-2z" class="l"></path><path d="M429 562c0-2-1-2-2-3 1-1 1 0 2 0-1-2-1-1-1-2 1 0 2 0 3-1h0v-1h1 1l-2-2h1 4l-1 1 1 1c-1 1-1 0-2 1-1 0-2 0-2 2 0 1 0 1 1 2h2l1 1c0-1 0-2 1-3 2 0 3 1 4 3h0v1c1 4 3 7 3 12h0c1 3 0 6 0 9-1 2-1 3-1 5v1c-1 1-1 1-1 2v-2l-2 1-1 2h-1-1-1c-1 0-1-1-2-2l-1 1-2-1-3 1v-1c2-1 3-2 4-3 0-1 0-3-1-4h0c1-3 2-4 2-7v-1l1-1c0-1 0-2-1-3h0v-1c0-2-2-3-3-5h0c-2-1-2-2-3-3h1l1 1v-1z" class="S"></path><path d="M444 583l-3 3h-1v-2c1-2 2-4 2-6 0-3 0-8-1-10-1-1-1-1-1-2s-1-3-1-5h0 2v1c1 4 3 7 3 12h0c1 3 0 6 0 9z" class="k"></path><path d="M434 574l1 5h2l1 1 1-3c2 5-2 9-2 14h0c1-1 2-2 3-2v1l-1 2h-1-1-1c-1 0-1-1-2-2l-1 1-2-1-3 1v-1c2-1 3-2 4-3 0-1 0-3-1-4h0c1-3 2-4 2-7v-1l1-1z" class="D"></path><path d="M435 579h2l1 1-2 6-2-2c1-2 1-3 1-5z" class="M"></path><path d="M434 584l2 2-2 4-1 1-2-1-3 1v-1c2-1 3-2 4-3s2-2 2-3z" class="G"></path><path d="M434 574l1 5c0 2 0 3-1 5 0 1-1 2-2 3 0-1 0-3-1-4h0c1-3 2-4 2-7v-1l1-1z" class="O"></path><path d="M429 562c0-2-1-2-2-3 1-1 1 0 2 0-1-2-1-1-1-2 1 0 2 0 3-1h0v-1h1 1l-2-2h1 4l-1 1 1 1c-1 1-1 0-2 1-1 0-2 0-2 2 0 1 0 1 1 2h2l1 1 2 2h-2c1 1 2 2 2 3s-1 1 0 3c2 2 1 5 1 8l-1 3-1-1h-2l-1-5c0-1 0-2-1-3h0v-1c0-2-2-3-3-5h0c-2-1-2-2-3-3h1l1 1v-1z" class="P"></path><path d="M433 560h2l1 1 2 2h-2c1 1 2 2 2 3s-1 1 0 3c2 2 1 5 1 8l-1 3-1-1c0-2 0-6-1-8-1-1-1-3-1-4v-1l1 1h1l-5-5v-1h1v-1z" class="N"></path><path d="M422 559c1-1 1-1 2-1l1 1h1l3 3v1l-1-1h-1c1 1 1 2 3 3h0c1 2 3 3 3 5v1h0c1 1 1 2 1 3l-1 1v1c-1 1-2 3-3 3-1-4-2-5-5-8h-1v-1c-2-1-8-1-10 0-3 2-5 5-6 8 0 3 0 7 2 10s5 4 8 5l1 1c2 1 5 0 8 0l-3 3c-2 1-2 0-3 1s-1 0-2 1c-5-2-9-4-13-10-2-3-3-8-2-11v-1c1-5 2-6 6-9l-2-1c2-3 6-2 9-3 2-1 3-3 5-5h0z" class="Y"></path><path d="M422 559c1-1 1-1 2-1l1 1h1l3 3v1l-1-1h-1c1 1 1 2 3 3h0c1 2 3 3 3 5v1h0c1 1 1 2 1 3l-1 1c-2-4-3-7-7-9-3-2-7-2-10-1h-1c-2 1-3 1-5 3l-2-1c2-3 6-2 9-3 2-1 3-3 5-5h0z" class="K"></path><path d="M418 593c-3-1-6-2-8-5s-2-7-2-10c1-3 3-6 6-8 2-1 8-1 10 0v1h1c3 3 4 4 5 8 1 0 2-2 3-3 0 3-1 4-2 7h0c1 1 1 3 1 4-1 1-2 2-4 3v1c-1 1-3 1-5 2-1 1-3 0-5 0z" class="O"></path><path d="M422 576c0 1 0 1-1 2l1 1c1-1 1-1 2-1v-1h1l1 1h0l1 1-2 2c-2 0-2 0-4-2v-1l1-2z" class="c"></path><path d="M417 590c3-1 7-2 9-1l-2 2c-2 0-4 1-6 0l-1-1z" class="n"></path><path d="M415 588h-1l-2-2h0c-1-2-2-3-2-4 0-2 0-5 1-7h1c1-2 2-2 4-3v1c-1 1-2 3-3 4-1 2-1 3 0 5v1 2l2 3z" class="K"></path><path d="M413 583h0c1-2 0-3 1-5 1 3 1 5 4 7 3 3 6 2 9 1l4-3c1 1 1 3 1 4-1 1-2 2-4 3l-2-1c-2-1-6 0-9 1l-2-2-2-3v-2z" class="Q"></path><path d="M414 578l1-1h0c2-2 2-3 5-4h4v1c-3 0-3 1-6 2v6c1 1 3 2 4 3 3 0 4 0 5-2 2-1 2-3 3-4 1 0 2-2 3-3 0 3-1 4-2 7h0l-4 3c-3 1-6 2-9-1-3-2-3-4-4-7z" class="D"></path><path d="M488 565h0v-4-7l1 1v21l1 15h-1v4c0 2 0 5 1 8-1 0-2 0-3 1-1 2-2 3-4 5-2 1-4 3-6 5 0 1-1 2-2 3l-6 6c-2 2-4 3-7 3-1 2-3 2-5 4 0 0-1-1-2-1l-1 1 1-2v-1l-1 1c-2-2-3-2-6-2v2h-1-1c-2 1-3 1-5 1l-1 1h-2v1l-1-2c-1-1-2 0-4 0h-1-4c-2 1-5 0-7 0h-3v-1h-2l-1-2h2s1-1 2-1c1-1 4-3 4-4 1-1 0-2-1-3v-3l-1 1h-5l3-2c1-2 1-2 1-4h1l-1-2 1-1c1 0 2 0 4-1h0c3 0 5-1 7-2h0c5-3 8-6 11-10 1 0 1-1 2-2 3-3 8-5 12-7l1-1c3-3 4-8 4-12v-2c-1-4-3-5-6-7 1-1 1-1 3-1 1 2 2 3 3 4 0 1 1 1 1 2h1c1-1 1-3 1-4 1-1 2-2 3-2l1 1 1 1 1-1 1 1c1 1 2 2 3 2 2 1 3 2 5 2h3 1c1 0 2 0 3-1h0v-2h1z" class="l"></path><path d="M484 576h0c0 2 0 3-1 5h-1c-1-2-1-2 0-3s0-1 1-1l1-1z" class="J"></path><path d="M420 610h1v1h1 2l-1 2 1 1v1h-2 0l-1 1h-5l3-2c1-2 1-2 1-4z" class="X"></path><path d="M458 599l2-2c0 2-1 3-2 4s-1 1-2 1l-5 5h-1c-1 1-2 1-3 2s-1 1-3 1l1-1c1-1 2-1 3-2h-1l2-2 3-3h2c1 0 3-2 4-3z" class="D"></path><path d="M433 629c1-1 2-1 3-1h1l1-1v-1l1 1c0-1 0-1-1-2l1-1h0l4 2 1-1v1h1l2 2h-1c-2 1-3 1-5 1l-1 1h-2v1l-1-2c-1-1-2 0-4 0z" class="b"></path><path d="M442 612l3 3c-3 0-6 2-9 2-2 1-3 2-5 3l-2 1-1-1c1-1 3-2 5-3 3-2 6-3 9-5z" class="R"></path><path d="M430 609c1 0 2-1 3-1 2-1 3-2 5-2l-3 3c-2 2-3 4-5 6 0 1-1 1-2 2l-1-1c1-1 1-3 2-4v-2h-1l2-1z" class="b"></path><path d="M432 621c4 0 6-1 10-2l-1 2h-2v1c1 0 2 0 3-1l1 1s0 1 1 1c1 1 1 2 1 3h-1v-1l-1 1-4-2h0 0c-1 0-2-1-3-1-1 1-2 0-3 0 0 0-1 0-1 1-1-1-1-1-2-1h-1l-1-1 4-1z" class="Y"></path><path d="M441 603v-1c1-1 2-4 3-5v2h0l1 2-1 1c-1 4 1 6-3 9-1-1-2-2-1-3v-2h-1c0 1 0 2-1 3h-3l3-3c1-1 2-3 3-3z" class="a"></path><path d="M419 625h3l2-2h2c0 1 1 1 0 1-1 1-1 1-1 2v1h2l2-1h0c1 1 2 1 2 2l1 1h-4c-2 1-5 0-7 0h-3v-1h-2l-1-2h2s1-1 2-1z" class="C"></path><path d="M445 615l3-2 4 5c-4-1-6-1-10 1-4 1-6 2-10 2l-1-1c2-1 3-2 5-3 3 0 6-2 9-2z" class="G"></path><path d="M466 570c-1-1 1-3 1-5 0-1 1-1 2-2l1 1c1 1 2 3 4 3 0 2 1 2 0 3l-2 1h0c0 2-1 3-2 3v1c-1 2-1 2-1 4l-1 1-1-1c0-1 0-2 1-3h0v-2h-2c0-2 1-1 1-3l-1-1z" class="J"></path><path d="M432 604l4-1c1-1 2-2 3-2l2 2c-1 0-2 2-3 3-2 0-3 1-5 2-1 0-2 1-3 1l-2 1h1l-5 1h-2-1v-1l-1-2 1-1c1 0 2 0 4-1h0c3 0 5-1 7-2h0z" class="U"></path><path d="M425 606c3 0 5-1 7-2h0c-1 2-2 3-4 3h-1c-1 0-1 1-2 1 1 1 1 1 2 1h3l-2 1h1l-5 1h-2-1v-1l-1-2 1-1c1 0 2 0 4-1h0z" class="c"></path><path d="M464 599v1c-2 2-2 2-3 4l-6 4v1h2l5 1c-1 1-1 2-1 2l-2 1-4-1c-2 0-4 0-7 1l-3 2-3-3c3-2 7-3 10-5l12-8z" class="E"></path><path d="M457 609l5 1c-1 1-1 2-1 2l-2 1-4-1h2l1-1h-2c0-1 0-1 1-2z" class="D"></path><path d="M471 563l1 1c1 1 2 2 3 2 2 1 3 2 5 2h3 1c1 0 2 0 3-1h0v-2h1v5l-3 1h0l1-2h-2v1c-1 1-2 2-4 2v1c1 1 1 2 3 2h-1c-2 1-5 3-6 3h-3-1-1v-2l-1-1v-1c1 0 2-1 2-3h0l2-1c1-1 0-1 0-3-2 0-3-2-4-3l1-1zm-9 3c0 1 1 1 1 2h1c1-1 1-3 1-4 1-1 2-2 3-2l1 1c-1 1-2 1-2 2 0 2-2 4-1 5l-3 11c0 1 1 2 2 2 1 1 1 2 2 4 0 2-1 3-2 5v-3c-1-1-2-2-4-2l-1 1c-1 0-1 0-2-1 0-1 0-1 1-2h-2l1-1c3-3 4-8 4-12v-2c-1-4-3-5-6-7 1-1 1-1 3-1 1 2 2 3 3 4z" class="K"></path><path d="M463 584h0c1 1 2 1 3 3 0 0-1 1-1 2-1-1-2-2-4-2 1-1 1-2 2-3z" class="f"></path><path d="M459 585c1-1 1-2 3-3l1 2c-1 1-1 2-2 3l-1 1c-1 0-1 0-2-1 0-1 0-1 1-2z" class="r"></path><path d="M457 585h2c-1 1-1 1-1 2-2 1-2 1-3 3v2l-1-1c-2 2-5 5-6 8 0 0 0 1 1 2-1 2-1 3-2 4-1-1-1-2-2-4l-1-2h0v-2c-1 1-2 4-3 5v1l-2-2c-1 0-2 1-3 2l-4 1c5-3 8-6 11-10 1 0 1-1 2-2 3-3 8-5 12-7z" class="u"></path><path d="M444 599c1-1 1-1 0-2 1-2 2-4 4-5l3-3h2c1 1 1 1 1 2-2 2-5 5-6 8 0 0 0 1 1 2-1 2-1 3-2 4-1-1-1-2-2-4l-1-2z" class="p"></path><path d="M448 613c3-1 5-1 7-1l4 1h3v2 1l-3 1c0 1-2 2-2 3-2 1-3 1-5 3h-1l-1 1c0 1-1 1-2 2v2h-1l-2-2c0-1 0-2-1-3-1 0-1-1-1-1l-1-1c-1 1-2 1-3 1v-1h2l1-2c4-2 6-2 10-1l-4-5z" class="O"></path><path d="M442 619c4-2 6-2 10-1h1c-2 2-4 2-6 3l-4 1-1-1c-1 1-2 1-3 1v-1h2l1-2z" class="B"></path><path d="M448 613c3-1 5-1 7-1l4 1h3v2 1l-3 1c-1 0-2 0-3 1l-1-1 1-1h0c2 0 3 0 5-1h-1c-1-1-3 0-5-1-1 2-1 3-1 5-1 0-1 0-1-1h-1l-4-5z" class="Q"></path><path d="M458 587c1 1 1 1 2 1l1-1c2 0 3 1 4 2v3c-1 2-2 3-4 4l-1 1-2 2c-1 1-3 3-4 3h-2l-3 3-2 2v-2c1-1 1-2 2-4-1-1-1-2-1-2 1-3 4-6 6-8l1 1v-2c1-2 1-2 3-3z" class="M"></path><path d="M458 587c1 1 1 1 2 1l1 1c-1 2-1 3-1 5v1h0l1 1-1 1-2 2h0c0-3 1-8 0-10h-1c1-1 1-1 1-2z" class="u"></path><path d="M461 587c2 0 3 1 4 2v3c-1 2-2 3-4 4l-1-1h0v-1c0-2 0-3 1-5l-1-1 1-1z" class="c"></path><path d="M461 589c1 1 2 2 2 3l-3 3h0v-1c0-2 0-3 1-5z" class="p"></path><path d="M454 591l1 1c0 2 0 6-2 8-1 1-1 1-2 1v-3c-1 1-2 2-2 3-1-1-1-2-1-2 1-3 4-6 6-8z" class="t"></path><path d="M488 565h0v-4-7l1 1v21l1 15h-1-2v1 2c-2 2-3 3-5 4s-3 1-4 1l-1-1c-1-1-2-2-2-3-1-2-2-4-2-6h1c1-1 2-1 3-2 0-1 1-1 2-2l1-1c2-1 2-1 3-2h3v-2l1-2c0-1 0-2-1-2h-2 0l-1-1c-2 0-2-1-3-2v-1c2 0 3-1 4-2v-1h2l-1 2h0l3-1v-5z" class="Q"></path><path d="M489 576l1 15h-1-2v1 2c-2 2-3 3-5 4h0c0-2 0-3 1-4h1c0-1-1-1-1-2-1 0 0 0-1-1h2l1-1 1 1 1-1v-1l2-1v-11-1z" class="K"></path><path d="M473 589h1c1-1 2-1 3-2 0-1 1-1 2-2l1-1c2-1 2-1 3-2h3c-1 3-3 5-5 6-1 1-3 2-4 3s0 4 0 6l1 1h-1c-1-1-2-2-2-3-1-2-2-4-2-6z" class="D"></path><path d="M488 565h0v-4-7l1 1v21 1 11l-2 1c0-3 0-6-1-9l1-2c0-1 0-2-1-2h-2 0l-1-1c-2 0-2-1-3-2v-1c2 0 3-1 4-2v-1h2l-1 2h0l3-1v-5z" class="b"></path><path d="M474 596l-5-2v-1h1v-1c1-2 1-2 3-3 0 2 1 4 2 6 0 1 1 2 2 3l1 1c1 0 2 0 4-1s3-2 5-4v-2-1h2v4c0 2 0 5 1 8-1 0-2 0-3 1-1 2-2 3-4 5-2 1-4 3-6 5 0 1-1 2-2 3l-6 6c-2 2-4 3-7 3-1 2-3 2-5 4 0 0-1-1-2-1l-1 1 1-2v-1l-1 1c-2-2-3-2-6-2 1-1 2-1 2-2l1-1h1c2-2 3-2 5-3 0-1 2-2 2-3l3-1v-1-2h-3l2-1s0-1 1-2l-5-1h-2v-1l6-4c1-2 1-2 3-4v-1c1-1 2-2 4-2 2-1 4-1 6-1z" class="f"></path><path d="M474 596l1 2c-1 1-2 1-3 1-2 0-5 2-7 3-1 1-3 1-4 2 1-2 1-2 3-4v-1c1-1 2-2 4-2 2-1 4-1 6-1z" class="Y"></path><path d="M475 605l1-1c-1-1-3-1-4-1v-1c1-1 3 0 4-1 1 1 2 1 3 2 4 0 4 0 7-2 0 1 1 1 2 1h0l-1-1c0-2 1-4 2-5v-1c0 2 0 5 1 8-1 0-2 0-3 1-1 2-2 3-4 5l-1-2c-2-1-4-1-6-2h-1z" class="m"></path><path d="M461 604c1-1 3-1 4-2-1 2-2 3-4 4-1 0-1 0-2 1l1 1h5c3-2 6-1 8-2 1 2 2 2 2 4h-1c-1 0-2 0-3-1-1 1-2 3-3 4l-1-1c-1 1-1 1-3 1h-2 0-3l2-1s0-1 1-2l-5-1h-2v-1l6-4z" class="Q"></path><path d="M462 610c3-1 7-1 9-1-1 1-2 3-3 4l-1-1c-1 1-1 1-3 1h-2 0-3l2-1s0-1 1-2z" class="l"></path><path d="M475 605h1c2 1 4 1 6 2l1 2c-2 1-4 3-6 5 0 1-1 2-2 3l-6 6h-4l-1-1v-1h3v-2c2 0 2-1 3-1v-2h0l-2-1v-2c1-1 2-3 3-4 1 1 2 1 3 1h1c0-2-1-2-2-4l2-1z" class="V"></path><path d="M471 609c1 1 2 1 3 1-1 2 0 3-2 4l-2 2-2-1v-2c1-1 2-3 3-4z" class="D"></path><path d="M467 621c2 0 3-2 5-3 1-1 2-1 3-1l-6 6h-4l-1-1v-1h3z" class="L"></path><path d="M475 605h1c2 1 4 1 6 2-2 2-3 3-4 5h-2l-1-2c0-2-1-2-2-4l2-1z" class="e"></path><path d="M462 613h0 2c2 0 2 0 3-1l1 1v2l2 1h0v2c-1 0-1 1-3 1v2h-3v1l1 1h4c-2 2-4 3-7 3-1 2-3 2-5 4 0 0-1-1-2-1l-1 1 1-2v-1l-1 1c-2-2-3-2-6-2 1-1 2-1 2-2l1-1h1c2-2 3-2 5-3 0-1 2-2 2-3l3-1v-1-2z" class="P"></path><path d="M467 619l-1-2c2-1 2-1 4-1v2c-1 0-1 1-3 1z" class="b"></path><path d="M455 629c1-1 1-2 2-3h2 0 3c-1 2-3 2-5 4 0 0-1-1-2-1z" class="h"></path><path d="M457 620c0 1 0 1-1 2h-2c0 1 0 1 1 1h2v1l-1 1c0 1-1 1-1 2l-1 1c-2-2-3-2-6-2 1-1 2-1 2-2l1-1h1c2-2 3-2 5-3z" class="X"></path><path d="M407 468l1 1c5-1 9 0 14 0l-1 3c1 0 2 0 3 1l1 3h-1c-1 4-2 9-5 12 2 1 3 2 4 3 2 2 4 3 5 5 2 3 4 5 5 8 1 2 2 5 3 8l1 8c0 8-1 14-4 21l-6 9-2 2-1 1-3 3 1 3h0c-2 2-3 4-5 5-3 1-7 0-9 3h-3l-1-1h-1l-1 1h-1c-1 0-2 1-3 1l-1-1-1 1c-1 0-2-1-3-1-2-1-5-1-7-2h-2c-2-1-4-1-6-1-1 1-2 2-3 4h-1l3-5-1-1h-2v2c0 1 0 2-2 3 1-3 2-3 1-6h-1-2c-1 0-1 0-2 1v1h-2c-2 1-1 1-2 2h-1v-1l-2-2v1l-1 1-1-1c-1 0-1-1-2-2 2-3 5-6 8-9l1-1h1l1-1c-3-4-6-8-8-12h-2l-1-2c-1 0-1 0-2 1l-1 1h-2c-1 1-1 1-2 1-1 1-1 1-3 2v-1c0-12 0-22 3-33l1-2c1-5 4-8 7-12l3-2 3-3 2-2 5-4 8-6 6-3c2-1 4-1 6-2l16-3z" class="k"></path><path d="M421 500h2c5 6 6 11 8 18 0 4 0 8-1 12v-2c-1-3 0-6-1-8-1-4-1-9-4-13 0-2-2-5-4-7z" class="E"></path><path d="M362 502l2-3c-1 3-1 6-2 9v4c-1 0-2 1-3 2v2c-1 4 0 8 0 11 1 1 2 1 4 0h2v-1-3l1 6c-2 0-4 0-6 1l-2 1c-2-9 0-21 4-29z" class="B"></path><path d="M382 560h0c3 1 5 1 8 2h3 0l-1-1h9 0c2 0 4 0 6-1 3 0 5-2 8-3s6-4 9-4l-3 3c-10 6-20 9-32 7-2-1-5-2-7-2v-1z" class="T"></path><path d="M368 510h1c1-2 2-5 4-7 1 0 1-1 2 0l-2 2-1 1v1l1 1-2 5c-1 2-2 4-2 6s-1 3-1 5v1 1 7l-1 1c0-2-1-3-1-5l-1-6c1-4 1-9 3-13z" class="K"></path><path d="M421 543c0 3-4 6-6 7l-1 1c-1 1-3 2-5 3v1c-1 1-2 1-4 1l-1 1c-2 0-8 1-10 0v-1h-2l-1-1c-2 0-3 0-4-1l-3-2c2 0 4 1 6 1h6l1 1c1 1 2 1 3 1l6-1c6-2 11-6 15-11z" class="Y"></path><path d="M384 552c2 0 4 1 6 1h6l1 1c1 1 2 1 3 1-5 0-9 0-13-1l-3-2z" class="O"></path><path d="M371 504l1-2c1-5 6-7 11-10 2-2 5-3 8-3h1c4-1 8-1 11 0 8 1 16 5 20 11h-2 0-2c-1 0-1 0-2-1h-1 0c-1-1-1-1-2-1h-2v-2-1l-2-3c-9-3-18-3-27 2-4 2-8 6-12 10z" class="T"></path><path d="M410 492c5 2 8 5 11 8h-2c-1 0-1 0-2-1h-1 0c-1-1-1-1-2-1h-2v-2-1l-2-3z" class="Q"></path><path d="M431 518c1 4 1 8 1 13 0 3-2 5-3 8 0 1-2 3-3 4-2 3-4 7-8 9h-3-1c-1 1-3 2-5 3v-1c2-1 4-2 5-3l1-1c2-1 6-4 6-7 6-7 8-14 8-23 1 2 0 5 1 8v2c1-4 1-8 1-12z" class="T"></path><path d="M429 520c1 2 0 5 1 8v2c-2 5-4 10-7 15-3 3-6 5-9 7-1 1-3 2-5 3v-1c2-1 4-2 5-3l1-1c2-1 6-4 6-7 6-7 8-14 8-23z" class="g"></path><path d="M371 504c4-4 8-8 12-10 9-5 18-5 27-2l2 3v1l-6-1c-2 0-4 0-6 1h0l-10 2c-1 0-2 1-3 1 0 1 0 1-1 1l-1 1v-1c-1 1-1 1-1 2l-2 1-1-1-3 3-1 1-2 2h-1c-1 2-2 3-3 5l2-5-1-1v-1l1-1 2-2c-1-1-1 0-2 0-2 2-3 5-4 7h-1l3-6z" class="m"></path><path d="M373 508c4-7 9-11 17-13 5-2 11-1 16 0-2 0-4 0-6 1h0l-10 2c-1 0-2 1-3 1 0 1 0 1-1 1l-1 1v-1c-1 1-1 1-1 2l-2 1-1-1-3 3-1 1-2 2h-1c-1 2-2 3-3 5l2-5z" class="L"></path><path d="M406 495l6 1v2h2c1 0 1 0 2 1-1 1-2 3-3 4l-4 5-1-1-1 2-3-1c-1-1-1-1-2-1h-3c-3 0-8 1-11 4h0l-1-1 1-1c1-1 2-1 3-2v-2h-1v-1-2h-1v2l-1-1-1-2-1 1 1 1h-1l-1-1v1 2l-2-2-1 1v-1l2-1c0-1 0-1 1-2v1l1-1c1 0 1 0 1-1 1 0 2-1 3-1l10-2h0c2-1 4-1 6-1z" class="I"></path><path d="M406 495l6 1v2h2c1 0 1 0 2 1-1 1-2 3-3 4l-4 5-1-1s2-2 2-3l-1-3h-1l-1-1h-2c0-1-1-1-2-1v-1l-1 1h0c-1 0-1 0-2-1v2h-1v-2l-1 1h0-1v-1c-1 1-1 0-1 1l-1-1c-1 1-1 1-1 2l-1-1-1 1h-1l-1-1v2c-1-1-1-1-2-1v2 1l-1-2-1 1 1 1h-1l-1-1v1 2l-2-2-1 1v-1l2-1c0-1 0-1 1-2v1l1-1c1 0 1 0 1-1 1 0 2-1 3-1l10-2h0c2-1 4-1 6-1z" class="W"></path><path d="M406 495l6 1v2h2c1 0 1 0 2 1-1 1-2 3-3 4-2-1-1-2-2-4-2-2-7-2-11-3 2-1 4-1 6-1z" class="F"></path><path d="M362 502h0c-4 8-6 20-4 29 1 2 1 5 2 7h-2l-1-2c-1 0-1 0-2 1l-1 1h-2c-1 1-1 1-2 1-1 1-1 1-3 2v-1c0-12 0-22 3-33l1-2h3l3 1h2l3-4z" class="b"></path><path d="M351 505h3l3 1c0 1-1 1-2 3h0c-2 0-2 1-3 2v1s0 1-1 1c1 1 0 1 1 2v1h1l1 1h-2v1h1v1h-1l-1 1h2l-1 1-1 1h2l-2 1c0 2-1 3 0 4v1c-1 1-1 1-1 3h4c-1 1-1 1-2 1l-2 2h0c1 2 0 3 0 5-1 1-1 1-3 2v-1c0-12 0-22 3-33l1-2zm17 45c4 4 8 8 14 10v1c2 0 5 1 7 2 12 2 22-1 32-7l1 3h0c-2 2-3 4-5 5-3 1-7 0-9 3h-3l-1-1h-1l-1 1h-1c-1 0-2 1-3 1l-1-1-1 1c-1 0-2-1-3-1-2-1-5-1-7-2h-2c-2-1-4-1-6-1-1 1-2 2-3 4h-1l3-5-1-1h-2v2c0 1 0 2-2 3 1-3 2-3 1-6h-1-2c-1 0-1 0-2 1v1h-2c-2 1-1 1-2 2h-1v-1l-2-2v1l-1 1-1-1c-1 0-1-1-2-2 2-3 5-6 8-9l1-1h1l1-1z" class="f"></path><path d="M367 551c1 1 1 1 1 3-1 2-3 3-5 5l-1 1 2 3v1l-1-1c-1-1-1-1-2-1l-2 1c-1 0-1-1-2-2 2-3 5-6 8-9l1-1h1z" class="U"></path><path d="M382 504l1-1 2 2v-2-1l1 1h1l-1-1 1-1 1 2 1 1v-2h1v2 1h1v2c-1 1-2 1-3 2l-1 1 1 1c-2 2-3 4-4 7h0c-1 4-1 9 0 13l3 3c4 3 7 4 11 5l-3 1h-4l-1-1c-1-1-4-2-4-3l-1-1c-2 2-5 5-6 7v1l1 2h1 1v1c1 0 1 0 1-1l1 1c0 1 0 0 1 1l1-1v2c-1 0-1 1-2 2l3 1 1 1 2 1c-2 0-4-1-6-1-4-1-9-6-12-9-1-1-4-8-5-9l1-1v-7-1-1c0-2 1-3 1-5s1-4 2-6 2-3 3-5h1l2-2 1-1 3-3 1 1v1z" class="F"></path><path d="M384 550c-2-1-4-1-5-3l-3-3h0 2l2 1h1 1v1c1 0 1 0 1-1l1 1c0 1 0 0 1 1l1-1v2c-1 0-1 1-2 2z" class="e"></path><path d="M369 519c0 5 0 10 1 15l1 1c1 0 0 0 1 1l-1 1c1 0 2 0 2 1v2l-1 1 1 1-1 1c-1-1-4-8-5-9l1-1v-7-1-1c0-2 1-3 1-5z" class="Q"></path><path d="M384 531l3 3c4 3 7 4 11 5l-3 1h-4l-1-1c-1-1-4-2-4-3l-1-1c-2 2-5 5-6 7v1l1 2-2-1v-1c0-1-1-1-1-2s3-3 3-4h0c-1 1-3 3-4 3l-2-3 1-1 1-2h-3v-1l3-1v1c1 0 3 0 4-1 1 0 3 0 4-1z" class="C"></path><path d="M382 504l1-1 2 2v-2-1l1 1h1l-1-1 1-1 1 2 1 1v-2h1v2 1h1v2c-1 1-2 1-3 2l-1 1 1 1c-2 2-3 4-4 7h0c-1 4-1 9 0 13-1 1-3 1-4 1-1 1-3 1-4 1v-1h-3v-1h1v-1h-1v-2-1-1c-1-1 0-1-1-2h2l1-1h-2l-1-1c2 0 2 0 3-1-1 0-2 0-3-1h3v-1c-1 0-1 0-2-1 1-1 1 0 2-1-1-1-1-1-1-2h2c-1-1-1-1-2-1l1-1s1-1 2-1c0-2-1 0 0-1s1-3 1-4c0 0 1-1 2-1v-1c0-1 2-1 2-1z" class="R"></path><path d="M377 511l2-2v-1c1 1 1 1 2 1 0 1 1 1 2 2l2-1v1 1c-1 1-2 0-4 1h1l1 1v1c-1 0-1 1-1 1l1 1v1h-1v1 1l-1 1v2 2 1 3h-1c-1-1-1-2-1-3-2-1-3-1-5-1v-1l1-1h-2l-1-1c2 0 2 0 3-1-1 0-2 0-3-1h3v-1c-1 0-1 0-2-1 1-1 1 0 2-1-1-1-1-1-1-2h2c-1-1-1-1-2-1l1-1s1-1 2-1c0-2-1 0 0-1z" class="H"></path><path d="M388 511h0c3-3 8-4 11-4h3c1 0 1 0 2 1l3 1 1-2 1 1c-1 1-2 3-2 4v1h0c-1 1 0 1-1 2h1l1-1h0c2-2 3-4 5-6l1 1h0c-2 1-3 2-3 4v1c1 5 0 10-1 15l-1 1c0 2-2 4-3 5-3 2-5 3-8 4-4-1-7-2-11-5l-3-3c-1-4-1-9 0-13h0c1-3 2-5 4-7z" class="Z"></path><path d="M388 521c1-2 1-3 2-5 2-1 3-3 5-3v1h3v1c-3 1-6 3-6 6-1 1 0 1-1 2v1c0 1 0 1 1 2v1c0 1 2 3 3 3 1 1 4 1 5 1l1-1c2 0 3-1 4-2v1l-4 4c-4 0-6 1-10-1h0c-3-4-3-6-3-11z" class="d"></path><path d="M413 508l1 1h0c-2 1-3 2-3 4v1c1 5 0 10-1 15l-1 1c0 2-2 4-3 5-3 2-5 3-8 4-4-1-7-2-11-5l1-1-1-1c0-1 0-2-1-3l1-1c1 1 1 2 2 3v1h2 0c4 2 6 1 10 1l4-4v-1h0l1-3c0-2 0-6-1-8-2-2-5-2-7-2v-1c1-1 2 0 3 0h0c2 0 4 1 5 2v1h1l1-3c2-2 3-4 5-6z" class="C"></path><path d="M413 508l1 1h0c-2 1-3 2-3 4v1c1 5 0 10-1 15l-1 1c0 2-2 4-3 5-3 2-5 3-8 4-4-1-7-2-11-5l1-1c0 1 1 2 2 2 4 2 8 2 13 1 2-1 4-3 5-5v-1c0-1 1-2 1-4s1-4 0-6l1-1c-1-1-2-1-3-2l1-3c2-2 3-4 5-6z" class="O"></path><path d="M388 511h0c3-3 8-4 11-4h3c1 0 1 0 2 1l3 1 1-2 1 1c-1 1-2 3-2 4v1h0c-1 1 0 1-1 2h1l1-1h0l-1 3h-1v-1c-1-1-3-2-5-2h0c-1 0-2-1-3 0h-3v-1c-2 0-3 2-5 3-1 2-1 3-2 5 0 5 0 7 3 11h-2v-1c-1-1-1-2-2-3l-1 1c1 1 1 2 1 3l1 1-1 1-3-3c-1-4-1-9 0-13h0c1-3 2-5 4-7z" class="F"></path><path d="M387 532s-1-1-1-2c-2-4-2-9 0-13 1-4 4-6 8-7-2 1-3 1-3 3-1 2-3 2-3 4-1 0-1 3-1 4h1c0 5 0 7 3 11h-2v-1c-1-1-1-2-2-3l-1 1c1 1 1 2 1 3z" class="g"></path><path d="M394 510c2-1 5-2 7-1s4 2 5 4l1-1v1h0c-1 1 0 1-1 2h1l1-1h0l-1 3h-1v-1c-1-1-3-2-5-2h0c-1 0-2-1-3 0h-3v-1c-2 0-3 2-5 3-1 2-1 3-2 5h-1c0-1 0-4 1-4 0-2 2-2 3-4 0-2 1-2 3-3z" class="S"></path><path d="M407 468l1 1c5-1 9 0 14 0l-1 3c1 0 2 0 3 1l1 3h-1c-1 4-2 9-5 12l-1-1-3-1c-11-5-21-5-32-1l-8 4c-4 2-8 6-11 10l-2 3h0l-3 4h-2l-3-1h-3c1-5 4-8 7-12l3-2 3-3 2-2 5-4 8-6 6-3c2-1 4-1 6-2l16-3z" class="c"></path><path d="M404 472c0 1 0 2-1 2 0 2 0 3-1 5l1 1c-3 0-4 0-6-1h-1 2 0c0-3 0-4 2-6 1 0 2 0 3-1h1z" class="F"></path><path d="M370 490c2-1 2-2 3-4v-1c1-2 2-3 4-4 5-3 10-5 15-7h2 0l-1 3c0-1-1-1-1-2-1 0-2 0-4 1-1 2-3 2-4 3-1 2-4 4-6 6h0c-1 1-2 2-3 2v2c-4 2-8 6-11 10l-2 3h0c2-5 5-8 8-12z" class="q"></path><path d="M394 474c2-1 2-1 3-1 0 2 0 4-1 6h1c2 1 3 1 6 1l-1-1c1-2 1-3 1-5l4 1-1 5c-2 2-5 1-7 1-3 0-6 0-9 1h-2l-6 3h1l-8 4v-2c1 0 2-1 3-2h0c2-2 5-4 6-6 1-1 3-1 4-3 2-1 3-1 4-1 0 1 1 1 1 2l1-3z" class="G"></path><path d="M384 479c1 0 2 0 3 1 2 0 2-1 4 0-4 2-9 4-13 5 2-2 5-4 6-6z" class="L"></path><path d="M384 479c1-1 3-1 4-3 2-1 3-1 4-1 0 1 1 1 1 2l-1 3h-1c-2-1-2 0-4 0-1-1-2-1-3-1z" class="e"></path><path d="M366 486c1 1 2 1 3 2h0l1 2c-3 4-6 7-8 12l-3 4h-2l-3-1h-3c1-5 4-8 7-12l3-2 3-3 2-2z" class="o"></path><defs><linearGradient id="u" x1="364.985" y1="489.265" x2="359.158" y2="492.961" xlink:href="#B"><stop offset="0" stop-color="#3f3f3d"></stop><stop offset="1" stop-color="#565755"></stop></linearGradient></defs><path fill="url(#u)" d="M366 486c1 1 2 1 3 2h0c-3 0-6 4-8 7l-3-2 3-2 3-3 2-2z"></path><path d="M358 493l3 2c-3 3-6 6-7 10h0-3c1-5 4-8 7-12z" class="q"></path><path d="M408 469c5-1 9 0 14 0l-1 3c1 0 2 0 3 1l1 3h-1c-1 4-2 9-5 12l-1-1-3-1c-11-5-21-5-32-1h-1l6-3h2c3-1 6-1 9-1 2 0 5 1 7-1l1-5-4-1c1 0 1-1 1-2l3-2 1-1z" class="Q"></path><path d="M418 487c-1-2 0-4 1-6l3-6 1 1 1-1v1c-1 4-2 9-5 12l-1-1z" class="I"></path><path d="M408 469c5-1 9 0 14 0l-1 3c0 1-1 2-2 3h-1c-1 1-3 4-3 5-1-1-1-1-1-2l-1 1c-2 1-3 1-4 2-2 0-2 0-3-1l1-5-4-1c1 0 1-1 1-2l3-2 1-1z" class="U"></path><path d="M407 470v5l-4-1c1 0 1-1 1-2l3-2z" class="a"></path><path d="M416 499h1c1 1 1 1 2 1h2 0c2 2 4 5 4 7 3 4 3 9 4 13 0 9-2 16-8 23-4 5-9 9-15 11l-6 1c-1 0-2 0-3-1l-1-1h-6l-2-1-1-1-3-1c1-1 1-2 2-2v-2l-1 1c-1-1-1 0-1-1l-1-1c0 1 0 1-1 1v-1h-1-1l-1-2v-1c1-2 4-5 6-7l1 1c0 1 3 2 4 3l1 1h4l3-1c3-1 5-2 8-4 1-1 3-3 3-5l1-1c1-5 2-10 1-15v-1c0-2 1-3 3-4h0l-1-1c-2 2-3 4-5 6h0l-1 1h-1c1-1 0-1 1-2h0v-1c0-1 1-3 2-4l4-5c1-1 2-3 3-4h0z" class="E"></path><path d="M380 545l-1-2v-1c1-2 4-5 6-7l1 1c0 1 3 2 4 3l1 1h4l3-1c3-1 5-2 8-4l-1 3h-1c-2 0-5 1-7 2h-2l-1 1h-3l-1 1v1c1-1 1 0 1-1l2 1v-1h1c1 1 1 2 1 4l1-1c1 1 2 1 4 1h0 1c1 0 1 0 2-1h4c1-1 2-2 3-2 0 0 1 1 2 0-1-1-1-1-1-2h1 0l2 2-1 2h-1-1v2l-1-1h-1v1h-1-1v1c-2 0-1-1-2 0h-2c-1 0-1 1-2 1 1 1 2 1 3 1s1 0 1-1h2 1l1 1-1 1h2s-1 0-1 1c-2 0-2 0-3 2l-6 1c-1 0-2 0-3-1l-1-1h-6l-2-1-1-1-3-1c1-1 1-2 2-2v-2l-1 1c-1-1-1 0-1-1l-1-1c0 1 0 1-1 1v-1h-1-1z" class="R"></path><path d="M386 548h1 0c1 0 1 1 2 2v-2l1 1 1 1h1l1-1c1 0 1 0 2 1v-1c1 0 1 0 1 1l2-1 1 1c1-1 1-1 2-1 1 1 2 1 3 1s1 0 1-1h2 1l1 1-1 1h2s-1 0-1 1c-2 0-2 0-3 2l-6 1c-1 0-2 0-3-1l-1-1h-6l-2-1-1-1-3-1c1-1 1-2 2-2z" class="Q"></path><defs><linearGradient id="v" x1="431.755" y1="519.425" x2="403.253" y2="536.766" xlink:href="#B"><stop offset="0" stop-color="#333433"></stop><stop offset="1" stop-color="#525050"></stop></linearGradient></defs><path fill="url(#v)" d="M416 499h1c1 1 1 1 2 1h2 0c2 2 4 5 4 7 3 4 3 9 4 13 0 9-2 16-8 23-4 5-9 9-15 11 1-2 1-2 3-2 0-1 1-1 1-1h-2l1-1-1-1h-1-2c0 1 0 1-1 1s-2 0-3-1c1 0 1-1 2-1h2c1-1 0 0 2 0v-1h1 1v-1h1l1 1v-2h1 1l1-2 3-2v-1l1-1h0v-1l1-1v-1-1c1 0 1 0 2-1v-1l1-1v-1h-1l1-1-1-2c1-1 1-2 2-3l-1-1 1-2c-2-1-1-2-2-3s-1-1-1-2v-1h-1c-1-1-1-2-2-3l-2-1h1c-1-2-2-2-2-3h0l-1-1c-2 2-3 4-5 6h0l-1 1h-1c1-1 0-1 1-2h0v-1c0-1 1-3 2-4l4-5c1-1 2-3 3-4h0z"></path><path d="M417 506c2 2 2 3 4 5v2l1 1-1 1 1 1v1 1c0 1 1 3 1 4-2-1-1-2-2-3s-1-1-1-2v-1h-1c-1-1-1-2-2-3l-2-1h1c-1-2-2-2-2-3h0l3-3z" class="R"></path><path d="M416 499h1c1 1 1 1 2 1h2 0c2 2 4 5 4 7-2-1-2-1-3-3h-1v1c-2 0-3 1-4 1l-3 3-1-1c-2 2-3 4-5 6h0l-1 1h-1c1-1 0-1 1-2h0v-1c0-1 1-3 2-4l4-5c1-1 2-3 3-4h0z" class="Z"></path><path d="M416 499h1c1 1 1 1 2 1h2 0c2 2 4 5 4 7-2-1-2-1-3-3h-1v1c-2 0-3 1-4 1l-3 3-1-1c1-1 5-3 5-5 0-1 0-1-1-2 0-1 0-1-1-2z" class="K"></path><path d="M465 470c3 0 6 4 8 6v1l2 2 4 6v1c2 0 3 0 5-1 2 0 4 0 5-1h0c4-1 10 0 14-1h4 2v-2l2-2 1 3 2 1c6 1 11 4 15 9l1 1v1c2 2 3 5 4 8 0 2 0 4 1 6l1-2v8c1 1 1 1 3 1l1-2c0 1 1 1 1 1h1v1h-2v1c3 3 0 11 1 15 1 1 0 4 0 5 0 8-1 17 0 25 0 3-1 8 0 11v11l2 2v3l4 2c2 0 3 1 4 1l8 4s1 1 2 1h0c2 0 2-2 4-3h0l1-2c1 1 1 2 2 2h1l1 1c0 1 1 1 2 1 2 0 2 1 3 2 1 2 1 2 2 2h1c1-1 1-1 2-1v2c1 1 2 1 3 2v1c-1 0-1 0-1-1l-1 1 2 2h-2c1 1 1 1 1 2l-2 1h-1c-1 0-1 0-2 1h0-3v3l-1 1-1 1v2 1 3h-1c-1 3-5 5-5 8-1 1 0 4 1 5l8 19h0l18 41 4 11 15 35h1c-1 1-1 2-2 3l4 8c1 1 1 2 2 3-2 1-4 1-6 1 0-1 0-1-1-1h4v-1c-2 0-5 1-7 1h-2c-2 1-4 1-5 2l-2-1-2 2-4-1-3 2-4 4c1 1 2 1 2 3l-2-2c0-1-4-3-5-4l-4 1-2 1v3l1 1h0l1 2c-1 0-2-1-3-1s-2 0-2-1l-1 1c-1 0-3 0-4-1h-2l-1-1-1 1-1-1c0-2-1-3-2-4-1-2-2-2-4-2h-1l-9 4v-4h-2v-2c-1-1 0-1 0-2h-5c-3-3-8-2-12-2h-15-8c-2 0-4 1-6 1h-6v1h-11-33-12-12v1c-5-1-11 0-16 0l7-18v-1c-1-4-3-7-5-11l-9-21-20-44-15-34-3-3 1-1-2-2c-1-2-4-4-6-5 2 0 3 1 4 2 2 1 4 2 7 3 1 0 1 1 2 1h2l1-1c3 1 6 3 9 3l23-1v1c0 1 1 2 2 3h1 1c2 2 6 3 9 3h0 2v1h3c2 0 5 1 7 0h4 1c2 0 3-1 4 0l1 2v-1h2l1-1c2 0 3 0 5-1h1 1v-2c3 0 4 0 6 2l1-1v1l-1 2 1-1c1 0 2 1 2 1 2-2 4-2 5-4 3 0 5-1 7-3l6-6c1-1 2-2 2-3 2-2 4-4 6-5 2-2 3-3 4-5 1-1 2-1 3-1-1-3-1-6-1-8v-4h1l-1-15v-21l2-49 1-1 1-1-2-1v-4l-1-1 1-2h0l-2-3-3-3h-7l-1-1c-1 0-2 0-3-1h-1c-1 0-2-1-2-2h0l2 1v-1l-6-5 1-1 1-1-1-1c-1-3-3-5-5-7l1-1h0z" class="l"></path><path d="M453 712c0-2 1-5 2-8 1 3 0 6 0 9v9c-1 0-2-1-3-1l-1-1 2-2h0v-2c1-2 1-2 1-3l-1-1z" class="K"></path><path d="M454 690v-2l3-12v9 1 1l-2 17c-1 3-2 6-2 8l-1 1h0v-2-1c1-2 1-3 1-4-1 0-1 1-1 2h0-1l3-18z" class="c"></path><path d="M451 708h1 0c0-1 0-2 1-2 0 1 0 2-1 4v1 2h0l1-1 1 1c0 1 0 1-1 3v2h0l-2 2 1 1c1 0 2 1 3 1l-1 11h-2v-2l-1-1c-1 1-1 2-2 3l-1 1v-4c1-1 1-1 1-2v-3-1l1-1v-3-1h-1c0 1 0 1-1 2h0l3-13z" class="b"></path><path d="M448 721h0c1-1 1-1 1-2h1v1 3l-1 1v1 3c0 1 0 1-1 2v4l1-1c1-1 1-2 2-3l1 1v2h2l-1 11h-5-3c-1-2 0-5 0-7l3-16z" class="K"></path><path d="M448 734l1-1c1-1 1-2 2-3l1 1v2c-1 1-1 1-1 2l-1 8h-4c0-1 0-2 1-3 0 0 1-1 2-1 0-2 0-3-1-5z" class="f"></path><path d="M544 737h2v4l-1 1-1-1v1h-3l-1 1c-2 0-25 0-27-1v-3c7-1 13 0 20-1 4 0 8-1 11-1z" class="C"></path><path d="M533 738c4 0 8-1 11-1v2l-1 1c-1 0-1 1-2 2-1 0-4-1-6-1-1 0 0 1-1 0h-2v-1h1v-2z" class="B"></path><path d="M453 690h1l-3 18-3 13-3 16c0 2-1 5 0 7h3c-2 2-4 1-7 1 1 1 0 1 1 1h4l1 1c-7 0-13-1-20 1h-9-1c-2 0-4 1-5 0l1-1h4c3-1 6-1 8-1h1-5-2c-2 0-5 1-6 0v-1c5-1 9-1 14-1h2 15l3-24 2-8 4-22z" class="J"></path><path d="M460 673c2 3 0 6 1 9v1l1 45v15 1h-8l3-34c0-2 0-3 1-5v-1-2c1-1 1-2 1-2 2-8 1-19 1-27z" class="c"></path><defs><linearGradient id="w" x1="455.049" y1="740.12" x2="464.995" y2="729.879" xlink:href="#B"><stop offset="0" stop-color="#181817"></stop><stop offset="1" stop-color="#3a3a39"></stop></linearGradient></defs><path fill="url(#w)" d="M462 744c-2-1-4 0-6-1 0-2 0-3 1-4l3-3-1-1h-2v-3h1c0-1 0-3 1-4l2-2 1 2v15 1z"></path><path d="M462 666c2 0 4 0 6 1 3 1 5 5 8 8h-1c-1 0-1-1-2-1h-1c0 2 1 4 2 6-3 0-3-4-4-6v4l-1-1v-3h-1v6h0-1c-1 1 0 1 0 2-1 3-1 7 0 10l2 21c0 7 0 15 2 23v8h-7c-1-6-1-14 0-20v-7c-1-9 0-18-1-27-1-2 0-4-2-7v-1c-1-3 1-6-1-9v-2c0-1 0-2 1-2 0-1 1-1 1-2v-1z" class="b"></path><path d="M468 738c1 2 1 3 2 5h-2-1l-2-2c1-1 1-2 3-3z" class="p"></path><path d="M464 717h2v-2h1c0 2 1 4-1 6h0c-1 1-1 1-1 2-1 0-1 1-1 1v-7z" class="m"></path><path d="M546 716h0c0 1 0 3 1 4v4c1-1 0-1 0-2s0-1 1-2v-1c0-1 0-2-1-3v-1c2-1 0-3 1-5l1 3v4 2l-1 4 1 1h2 3 9l1-1 2-2v-1 1 1l-4 10-1 2c-1 2-2 3-3 5h-1l-1-2c-2 1-5 1-7 1 0 1 0 0-1 1 0 0-1 0-1 1l-1 1v-4h-2c-3 0-7 1-11 1-7 1-13 0-20 1-4 0-1 3-3 4h-1c1-1 1 0 1-2h-1l-1 2h0c-4 0-10 1-13 0v-3h1c0 1 0 1 1 2h0v-3h-3v4c-1 1-5 1-7 1h0l2-1c0-2-1-3-1-6l1-1c0-1 0-1-1-2l1-1h3 6 4l-2 2c5 0 11-1 16 0 3 0 7 0 11-1h0l-2-1c6 0 12-2 18-2 2 0 2 1 3 2v-5-3-4l-1-3 1-2z" class="K"></path><path d="M525 733c6 0 12-2 18-2 2 0 2 1 3 2v3h-2c-1 0-2 1-2 1l-36 1h0c2-1 3-1 5 0 4-2 8-1 11-2-1-1-4 1-6-1h0c3 0 7 0 11-1h0l-2-1z" class="H"></path><path d="M542 737c-1 0-2-1-3-1-1-1-2-1-4-1h0v-1c2-1 5 0 7 0l2 2c-1 0-2 1-2 1z" class="S"></path><path d="M489 733h3 6 4l-2 2c5 0 11-1 16 0h0c2 2 5 0 6 1-3 1-7 0-11 2-2-1-3-1-5 0h0c-4 1-10 0-15 0h-1l-1 1h1 4v4c-1 1-5 1-7 1h0l2-1c0-2-1-3-1-6l1-1c0-1 0-1-1-2l1-1z" class="a"></path><path d="M491 738c2-1 5-1 7-1 1 0 3 0 5-1-3 0-9 1-10-1h7c5 0 11-1 16 0h0c2 2 5 0 6 1-3 1-7 0-11 2-2-1-3-1-5 0h0c-4 1-10 0-15 0z" class="C"></path><path d="M566 721v-1 1 1l-4 10-1 2c-1 2-2 3-3 5h-1l-1-2c-2 1-5 1-7 1 0 1 0 0-1 1 0 0-1 0-1 1l-1 1v-4c1-2 1-5 1-6h1l1-1v-1c-1-1-1-2-1-3 2-1 4-1 6-1v-1h9l1-1 2-2z" class="a"></path><path d="M549 729c-1-1-1-2-1-3 2-1 4-1 6-1s8 0 9 1c0 1 0 2-1 3h-13z" class="W"></path><path d="M546 737c1-2 1-5 1-6h1v1c5 1 9-1 13-1l1 1-1 2c-1 2-2 3-3 5h-1l-1-2c-2 1-5 1-7 1 0 1 0 0-1 1 0 0-1 0-1 1l-1 1v-4z" class="L"></path><path d="M561 734l-2 1c-2 0-3 0-5-1h-6l1-1 13-1-1 2z" class="M"></path><path d="M546 737c1-2 1-5 1-6h1v1 4c3 0 7-1 8 1h0c-2 1-5 1-7 1 0 1 0 0-1 1 0 0-1 0-1 1l-1 1v-4z" class="f"></path><path d="M467 682c0-1-1-1 0-2h1 0v-6h1v3l1 1c0 1 1 1 1 2l1 1v3c1 1 0 3 1 5 0 1 0 2 1 3v4 1l1 5 1 1v1 3l1 1v3c0 1 0 1 1 2v1 2h0c1 2 1 3 1 4l1 1c0-1 0-1 1-1v1c1 1 2 0 2 2h-2c1 2 2 3 3 4h1c1-1 2-1 3-3 1 2 1 2 1 4l1 1-1 4-1 1c1 1 1 1 1 2l-1 1c0 3 1 4 1 6l-2 1h0-6-8c-1-2-1-6-1-8l-2-24-1-14c-2-5-2-11-2-16z" class="m"></path><path d="M469 698c0 1 1 3 2 4 1 2 0 6 1 9v2h-1v-3l-1 1v1l-1-14z" class="K"></path><path d="M473 715v-5c-1-2 0-4-1-6v-3h0l-1-1v-4-2h1 0l2-2v4 1l1 5 1 1v1 3l1 1v3c0 1 0 1 1 2v1 2h0c0 1-1 2-1 3h0c-1-1-1-3-1-5l-1-1v4 2h-1v-4h-1z" class="t"></path><defs><linearGradient id="x" x1="470.009" y1="735.296" x2="483.244" y2="724.454" xlink:href="#B"><stop offset="0" stop-color="#1a1c1c"></stop><stop offset="1" stop-color="#373635"></stop></linearGradient></defs><path fill="url(#x)" d="M473 715h1v4h1v-2-4l1 1c0 2 0 4 1 5h0c0-1 1-2 1-3 1 2 1 3 1 4l-1 1v1c1 1 0 4 1 6 1 1 0 2 1 4v1l1 3c-1 2 0 4 1 6l1 1c1 1 2 0 4 1h0-6c-2-1-5 0-6-1v-4c-1-1-1-4-1-5 1-2 1-2 0-3v-4c-1-1-1-1-1-2l1-1h1v-1l-1-1v-1c-1-1 0-2-1-4v-2z"></path><path d="M475 739c-1-1-1-4-1-5 1-2 1-2 0-3v-4c-1-1-1-1-1-2l1-1v4l1 1v2c1 1 1 3 2 5v2l1 1 1 1h-2c-1-1-1-1-2-1z" class="f"></path><path d="M479 720l1 1c0-1 0-1 1-1v1c1 1 2 0 2 2h-2c1 2 2 3 3 4h1c1-1 2-1 3-3 1 2 1 2 1 4l1 1-1 4-1 1c1 1 1 1 1 2l-1 1c0 3 1 4 1 6l-2 1c-2-1-3 0-4-1l-1-1c-1-2-2-4-1-6l-1-3v-1c-1-2 0-3-1-4-1-2 0-5-1-6v-1l1-1z" class="n"></path><path d="M481 736v-1-3c2 0 2 0 3 1l1 1 1 1 1 1 1 1c0 3 1 4 1 6l-2 1c-2-1-3 0-4-1l-1-1c-1-2-2-4-1-6z" class="r"></path><path d="M481 736v-1-3c2 0 2 0 3 1l1 1 1 1c-1 1-2 0-3 0 0 1 1 1 1 2v3c-1 1-1 1-1 3l-1-1c-1-2-2-4-1-6z" class="b"></path><defs><linearGradient id="y" x1="455.67" y1="746.307" x2="458.009" y2="756.319" xlink:href="#B"><stop offset="0" stop-color="#a9a7a9"></stop><stop offset="1" stop-color="#c4c2bc"></stop></linearGradient></defs><path fill="url(#y)" d="M415 735h1v1c-1 2-2 4-3 7s-3 6-4 9c1-1 3-2 5-2 1-1 2-1 3-2h1c2 0 3 0 5 1 1 1 5-1 7-1l37-1h5c7-2 15-1 22-1h7 15 26c1 1 2 2 2 3l1 1h-21c-4 1-9 0-13 1h-8c-2 0-4 1-6 1h-6v1h-11-33-12-12v1c-5-1-11 0-16 0l7-18v-1h1z"></path><path d="M493 749l31 1c-4 1-9 0-13 1h-8-12l-11-1c-4 0-8 0-12-1h17 8z" class="W"></path><path d="M415 735h1v1c-1 2-2 4-3 7s-3 6-4 9c1-1 3-2 5-2 1-1 2-1 3-2h1c2 0 3 0 5 1-3 0-5 0-7 1 3 0 7 0 10 1h6l-1 1-12-1-1 1c2 1 3 0 5 1v1c-5-1-11 0-16 0l7-18v-1h1z" class="G"></path><path d="M516 746h26c1 1 2 2 2 3l1 1h-21l-31-1c2-1 7-1 10-1 4 0 9-1 13-2z" class="o"></path><path d="M494 746h7 15c-4 1-9 2-13 2-3 0-8 0-10 1h-8-17-9-19c-3 0-7 0-10-1l37-1h5c7-2 15-1 22-1z" class="S"></path><path d="M467 747h5c7-2 15-1 22-1-2 0-2 0-4 1-1 0-3 0-5 1h-15l-3-1z" class="Y"></path><defs><linearGradient id="z" x1="515.102" y1="739.02" x2="529.131" y2="718.641" xlink:href="#B"><stop offset="0" stop-color="#b0b0b3"></stop><stop offset="1" stop-color="#ebe7e3"></stop></linearGradient></defs><path fill="url(#z)" d="M545 711c0 2 1 3 1 5l-1 2 1 3v4 3 5c-1-1-1-2-3-2-6 0-12 2-18 2l2 1h0c-4 1-8 1-11 1-5-1-11 0-16 0l2-2h-4-6-3l1-4-1-1c0-2 0-2-1-4v-1c2 1 2 1 3 1v-1h4c6-1 14-2 20 0h0l1-1c2-2 6-3 9-4h1v-2c6 0 11 0 17-2h2v-3z"></path><path d="M509 726h3 2 0c0 1-1 2-1 3h-12c-3 0-6 1-9 0-1 0-1 0-2 1v-1l-1-1c1-1 0-1 1-1 3 1 6 1 9 0h1 0c2-1 6-1 9-1z" class="j"></path><path d="M514 726c6 1 10-1 15-1h2l1 1c1 0 2 0 3 1h-4l-1 1h1c1 0 2 0 3 1h-11c-2 0-7 1-8 0h0-2c0-1 1-2 1-3z" class="C"></path><path d="M514 726c6 1 10-1 15-1l-1 2c-2 0-4 0-6 1s-4 0-6 0c-1 0-1 0-1 1h-2c0-1 1-2 1-3z" class="N"></path><path d="M546 721v4 3c-1 1-2 2-3 2l1-2-1-1h-2-6c-1-1-2-1-3-1l-1-1h-2c-5 0-9 2-15 1h0-2-3 3v-1c5-1 10-1 14-1 6 0 13 0 19-2l1-1z" class="F"></path><path d="M531 725l9-1c0 1 1 1 1 2v1h-6c-1-1-2-1-3-1l-1-1z" class="S"></path><path d="M540 724h5l1 1v3c-1 1-2 2-3 2l1-2-1-1h-2v-1c0-1-1-1-1-2z" class="H"></path><path d="M543 730c1 0 2-1 3-2v5c-1-1-1-2-3-2-6 0-12 2-18 2l2 1h0c-4 1-8 1-11 1-5-1-11 0-16 0l2-2h-4-6-3l1-4v1c1 1 2 1 3 1 17 2 33 1 50-1z" class="F"></path><path d="M502 733h14 9l2 1h0c-4 1-8 1-11 1-5-1-11 0-16 0l2-2z" class="X"></path><path d="M515 723c5 0 10-2 15-2l-3 2h-5c1 1 3 0 4 1-4 0-9 0-14 1v1h-3c-3 0-7 0-9 1h0-1c-3 1-6 1-9 0-1 0 0 0-1 1 0-2 0-2-1-4v-1c2 1 2 1 3 1v-1h4c6-1 14-2 20 0h0z" class="H"></path><path d="M488 724v-1c2 1 2 1 3 1l21 1v1h-3c-3 0-7 0-9 1h0-1c-3 1-6 1-9 0-1 0 0 0-1 1 0-2 0-2-1-4z" class="O"></path><path d="M545 711c0 2 1 3 1 5l-1 2 1 3-1 1c-6 2-13 2-19 2-1-1-3 0-4-1h5l3-2c-5 0-10 2-15 2l1-1c2-2 6-3 9-4h1v-2c6 0 11 0 17-2h2v-3z" class="C"></path><path d="M545 711c0 2 1 3 1 5l-1 2c-1-1-2-1-4-1-5 0-10 1-15 1v-2c6 0 11 0 17-2h2v-3z" class="G"></path><path d="M530 721c2 0 4 0 5-1s2-1 3-1 2-1 3-1h1l-1 1h3c1 1 1 2 1 3-6 2-13 2-19 2-1-1-3 0-4-1h5l3-2z" class="k"></path><path d="M474 680c-1-2-2-4-2-6h1c1 0 1 1 2 1h1l8 12c1 2 2 4 3 4 1 1 2 2 2 3 1 0 1 1 2 1s1-1 2-1h1 9 10 2 6c-1 1-2 1-3 1v1h2 0v1h-5l-1 1v3 1h-6c-2 2-5 1-7 1h-1-7l-1 1c1 1 1 1 1 3h-1v-1c0-1 0-2-1-2v-1l-1 1c1 1 1 1 1 3l-1 2h1c0 2 0 2 1 3v1h1c0 2 0 1-1 2 1 1 5 1 7 1v1c-2 1-2 3-5 3 0 2 0 2 1 3h-4v1c-1 0-1 0-3-1v1c-1 2-2 2-3 3h-1c-1-1-2-2-3-4h2c0-2-1-1-2-2v-1c-1 0-1 0-1 1l-1-1c0-1 0-2-1-4h0v-2-1c-1-1-1-1-1-2v-3l-1-1v-3-1l-1-1-1-5v-1-4c-1-1-1-2-1-3-1-2 0-4-1-5v-3l-1-1c0-1-1-1-1-2v-4c1 2 1 6 4 6z" class="r"></path><path d="M483 693c1 0 1 1 2 2 0 2 1 5 0 7 1 1 1 1 1 2h-1 0c-1-1-1-2-2-3l-2-7h0v-1h1 1z" class="U"></path><defs><linearGradient id="AA" x1="507.236" y1="691.639" x2="513.753" y2="697.872" xlink:href="#B"><stop offset="0" stop-color="#60605d"></stop><stop offset="1" stop-color="#767676"></stop></linearGradient></defs><path fill="url(#AA)" d="M494 694h9 10 2 6c-1 1-2 1-3 1v1h2 0v1h-5l-1 1v3 1h-6-3-5-7-3v-1c0-1 0-2-1-3 0-1 0-1-1-2 1-1 1-1 1-2 1 0 1 1 2 1s1-1 2-1h1z"></path><path d="M493 702c-1-1-1-1-1-3h0 2v-1h-2 0-2v-2c2 0 1 1 3 1h3v2h1v2l3 1h-7z" class="W"></path><path d="M496 697h1 0c-1-1-1-1-2-1v-1h8l2 2h5 5l-1 1v3 1h-6-3-5l-3-1v-2h-1v-2z" class="j"></path><path d="M510 697h5l-1 1c-2 1-4 1-6 1h-6l1-1c1-2 5 0 7-1z" class="N"></path><path d="M508 699c2 0 4 0 6-1v3 1h-6-3l3-3z" class="B"></path><path d="M502 699h6l-3 3h-5l-3-1v-2h5z" class="I"></path><path d="M470 678v-4c1 2 1 6 4 6 1 2 0 2 1 3v1 2l2 4v1l1 5c0 1 1 1 1 2 0 2 1 3 1 5h1c0-2-1-6-2-8v-2-1c2 2 2 6 3 9s2 6 2 10c0 0 1 1 1 2 1-1 3-1 4-2l1-2h1c0 2 0 2 1 3v1h1c0 2 0 1-1 2 1 1 5 1 7 1v1c-2 1-2 3-5 3 0 2 0 2 1 3h-4v1c-1 0-1 0-3-1v1c-1 2-2 2-3 3h-1c-1-1-2-2-3-4h2c0-2-1-1-2-2v-1c-1 0-1 0-1 1l-1-1c0-1 0-2-1-4h0v-2-1c-1-1-1-1-1-2v-3l-1-1v-3-1l-1-1-1-5v-1-4c-1-1-1-2-1-3-1-2 0-4-1-5v-3l-1-1c0-1-1-1-1-2z" class="c"></path><path d="M489 711l1-2h1c0 2 0 2 1 3v1h1c0 2 0 1-1 2 1 1 5 1 7 1v1c-2 1-2 3-5 3 0 2 0 2 1 3h-4v1c-1 0-1 0-3-1v-1 1l-1-1c1 0 1-1 2-2v-1-3-5z" class="F"></path><path d="M520 697h1c1 2 3 2 5 3h5c4-1 8-1 12-1l-1 1 3 3v5h-2v1h2v2 3h-2c-6 2-11 2-17 2v2h-1c-3 1-7 2-9 4l-1 1h0c-6-2-14-1-20 0-1-1-1-1-1-3 3 0 3-2 5-3v-1c-2 0-6 0-7-1 1-1 1 0 1-2h-1v-1c-1-1-1-1-1-3h-1l1-2c0-2 0-2-1-3l1-1v1c1 0 1 1 1 2v1h1c0-2 0-2-1-3l1-1h7 1c2 0 5 1 7-1h6v-1-3l1-1h5z" class="W"></path><path d="M528 703h1 1c1 1 1 1 2 1h-3l-1 1h0c-3 0-6 1-8 1-3 1-5 1-8 1h-7c3-1 6 0 9-1 2 0 1 0 3-1 1 0 3 0 5-1h0c1-1 4-1 6-1z" class="C"></path><path d="M520 697h1c1 2 3 2 5 3h5 4c-1 1-1 1-1 2v1h-5-1v-1h0-14v-1-3l1-1h5z" class="J"></path><path d="M520 697h1c1 2 3 2 5 3-3 1-10 0-12 1v-3l1-1h5z" class="R"></path><path d="M531 700c4-1 8-1 12-1l-1 1 3 3v5h-2-1l-9 1c-2-1-3-1-5-1h-3-9c-2-1-3-1-4-1 3 0 5 0 8-1 2 0 5-1 8-1h0l1-1h3c-1 0-1 0-2-1h-1 5v-1c0-1 0-1 1-2h-4z" class="Z"></path><path d="M528 705c2 0 5 0 7-1s6-1 9-1v3h-1c-2 0-4 0-6 1h-11 0-1-4 2c1 0 2-1 3-1s1 0 2-1h0z" class="s"></path><path d="M531 700c4-1 8-1 12-1l-1 1 3 3v5h-2-1l1-2h1v-3c-3 0-7 0-9 1s-5 1-7 1l1-1h3c-1 0-1 0-2-1h-1 5v-1c0-1 0-1 1-2h-4z" class="R"></path><path d="M505 707h7c1 0 2 0 4 1h9 3c2 0 3 0 5 1l9-1h1v1h2v2 3h-2l-1-1c-2-1-5-1-7 0h-11-7c-1 1-2 1-3 0-3 1-11 1-14 0-3 0-5 0-8-1-1-1-1-1-1-3h6v-1l8-1z" class="W"></path><path d="M543 709h2v2 3h-2l-1-1c-2-1-5-1-7 0h-11c1 0 3 0 3-1 1 0 1-1 1-1 1-1 5-1 7-1 3-1 5-1 8-1z" class="R"></path><path d="M505 707h7c1 0 2 0 4 1h9 3c2 0 3 0 5 1h-11l-18 1-7-1v-1l8-1z" class="B"></path><path d="M491 709h6l7 1c5 1 12 0 17 0h5c-1 1-2 1-4 1-2 2-2 2-5 2-1 1-2 1-3 0-3 1-11 1-14 0-3 0-5 0-8-1-1-1-1-1-1-3z" class="O"></path><path d="M500 713h0c1-1 1-2 2-3v1 2h5v-1h-2l1-1h16c-2 2-2 2-5 2-1 1-2 1-3 0-3 1-11 1-14 0z" class="j"></path><defs><linearGradient id="AB" x1="516.703" y1="722.549" x2="525.2" y2="706.633" xlink:href="#B"><stop offset="0" stop-color="#bcbcbc"></stop><stop offset="1" stop-color="#f3f1f0"></stop></linearGradient></defs><path fill="url(#AB)" d="M492 712c3 1 5 1 8 1 3 1 11 1 14 0 1 1 2 1 3 0h7 11c2-1 5-1 7 0l1 1c-6 2-11 2-17 2v2h-1c-3 1-7 2-9 4l-1 1h0c-6-2-14-1-20 0-1-1-1-1-1-3 3 0 3-2 5-3v-1c-2 0-6 0-7-1 1-1 1 0 1-2h-1v-1z"></path><path d="M499 716h27v2h-1c-3 1-7 2-9 4l-1 1h0c-6-2-14-1-20 0-1-1-1-1-1-3 3 0 3-2 5-3v-1z" class="X"></path><path d="M506 719c4 0 6 0 9 2h-9v-2z" class="M"></path><path d="M506 719c1 0 1-1 1-1h5 13c-3 1-7 2-9 4l-1 1h0v-2c-3-2-5-2-9-2z" class="N"></path><path d="M571 725l1-2c1-1 3-2 5-2 1 0 3 1 4 2h-1v2c2 0 3 0 5-1l4 9h2l2 3v5l7 1h8 1 0 1l-1-2c0-3-2-5-3-8l1-1c1 4 3 8 4 11l4 8c1 1 1 2 2 3-2 1-4 1-6 1 0-1 0-1-1-1h4v-1c-2 0-5 1-7 1h-2c-2 1-4 1-5 2l-2-1-2 2-4-1-3 2-4 4c1 1 2 1 2 3l-2-2c0-1-4-3-5-4l-4 1-2 1v3l1 1h0l1 2c-1 0-2-1-3-1s-2 0-2-1l-1 1c-1 0-3 0-4-1h-2l-1-1-1 1-1-1c0-2-1-3-2-4-1-2-2-2-4-2h-1l-9 4v-4h-2v-2c-1-1 0-1 0-2h-5c-3-3-8-2-12-2h-15c4-1 9 0 13-1h21l-1-1c0-1-1-2-2-3h-26-15l6-1c4-1 8-1 13-1h23l1-2v-1l1 1 1-1 1-1c0-1 1-1 1-1 1-1 1 0 1-1 2 0 5 0 7-1l1 2h1 1l1-1c1 1 2 1 3 2h0c1-2 2-4 4-6l2-4 2-5z" class="S"></path><path d="M545 746c4-1 10-1 15-1 14 0 28-1 42 0l2 1-51 1c-3 0-6 1-8-1z" class="j"></path><path d="M607 731c1 4 3 8 4 11l4 8-1 1-2-1v1h-6 0c2-1 4-1 6-1l-1-1h-2v-1h1v-1c-2 0-4 0-6-1l-2-1c-14-1-28 0-42 0-5 0-11 0-15 1h-3-26-15l6-1c4-1 8-1 13-1h23 6 7 35c3 0 10 1 13 0l1-1c1 0 2 0 3-1h1 0 1l-1-2c0-3-2-5-3-8l1-1z" class="H"></path><path d="M607 731c1 4 3 8 4 11l4 8-1 1-2-1v1h-6 0c2-1 4-1 6-1l-1-1h-2v-1h1v-1c-2 0-4 0-6-1l-2-1 7 1h0l1-1h-2l-4-1 1-1c1 0 2 0 3-1h1 0 1l-1-2c0-3-2-5-3-8l1-1z" class="K"></path><path d="M569 730c1 1 2 3 3 4h2c1 0 2 1 3 1l1-1v4l2 2h0c1 1 2 2 4 3h1 1c2 0 4 0 6-1l-1-4c1 1 1 2 2 3h0l7 1h8c-1 1-2 1-3 1l-1 1c-3 1-10 0-13 0h-35-7-6l1-2v-1l1 1 1-1 1-1c0-1 1-1 1-1 1-1 1 0 1-1 2 0 5 0 7-1l1 2h1 1l1-1c1 1 2 1 3 2h0c1-2 2-4 4-6l2-4z" class="V"></path><path d="M569 730c1 1 2 3 3 4h2c1 0 2 1 3 1l1-1v4l2 2h-2l-1 1c-2 0-9-2-10-3 1-1 1-2 1-2l-1-2 2-4z" class="N"></path><path d="M548 739c1-1 1 0 1-1 2 0 5 0 7-1l1 2h1 1l1-1c1 1 2 1 3 2h0 1c1 1 3 0 4 2v1c-4 0-9 0-12 1h-7-6l1-2v-1l1 1 1-1 1-1c0-1 1-1 1-1z" class="f"></path><path d="M560 738c1 1 2 1 3 2-1 1-2 2-4 2-1 0-1 0-3-1l3-2 1-1z" class="p"></path><path d="M548 739c1-1 1 0 1-1 2 0 5 0 7-1l1 2h1 1l-3 2-1 1v-1-1h-2l-5-1z" class="i"></path><path d="M546 741l1-1c0-1 1-1 1-1l5 1 1 1v1c-2 2-3 1-5 1v1h-6l1-2v-1l1 1 1-1z" class="U"></path><path d="M571 725l1-2c1-1 3-2 5-2 1 0 3 1 4 2h-1v2c2 0 3 0 5-1l4 9h2l2 3v5h0c-1-1-1-2-2-3l1 4c-2 1-4 1-6 1h-1-1c-2-1-3-2-4-3h0l-2-2v-4l-1 1c-1 0-2-1-3-1h-2c-1-1-2-3-3-4l2-5z" class="Y"></path><path d="M573 725h1l1 3c2 0 3 1 5 1v1h-4l-4 2h0l-1-1v-1l1-1c2-1 0-1 0-2 1-1 1-1 1-2z" class="T"></path><path d="M580 740c1-1-1-2-1-3v-3h2l1 1v1c2 2 2 4 3 7h-1c-2-1-3-2-4-3z" class="D"></path><path d="M582 735s1 0 2-1c1 0 2-1 3-2-1-1-1-2-2-3-2-3-5-1-7-4l1-1-1-1h2v2c2 0 3 0 5-1l4 9h2l2 3v5h0c-1-1-1-2-2-3l1 4c-2 1-4 1-6 1h-1c-1-3-1-5-3-7v-1z" class="f"></path><path d="M589 733h2l2 3v5h0c-1-1-1-2-2-3l-2-5z" class="L"></path><path d="M545 750h15 24c0-1 0-1 1-1 8-1 17-2 24-1v1h2l1 1c-2 0-4 0-6 1h0 6v-1l2 1 1-1c1 1 1 2 2 3-2 1-4 1-6 1 0-1 0-1-1-1h4v-1c-2 0-5 1-7 1h-2c-2 1-4 1-5 2l-2-1-2 2-4-1c-1 0-2-1-2-1h-4c-3 0-7 0-11-1h-6-8-18-5c-3-3-8-2-12-2h-15c4-1 9 0 13-1h21z" class="I"></path><path d="M563 752h-7-3c4-1 19-2 23 0-4 0-10-1-13 0z" class="Y"></path><path d="M615 750c1 1 1 2 2 3-2 1-4 1-6 1 0-1 0-1-1-1h4v-1c-2 0-5 1-7 1h-2c-2 1-4 1-5 2l-2-1-2 2-4-1c-1 0-2-1-2-1h-4c-3 0-7 0-11-1h-6v-1h-6c3-1 9 0 13 0h22c2-1 5 1 8-1h6v-1l2 1 1-1z" class="S"></path><path d="M575 753h30c-2 1-4 1-5 2l-2-1-2 2-4-1c-1 0-2-1-2-1h-4c-3 0-7 0-11-1z" class="V"></path><path d="M569 753h6c4 1 8 1 11 1h4s1 1 2 1l-3 2-4 4c1 1 2 1 2 3l-2-2c0-1-4-3-5-4l-4 1-2 1v3l1 1h0l1 2c-1 0-2-1-3-1s-2 0-2-1l-1 1c-1 0-3 0-4-1h-2l-1-1-1 1-1-1c0-2-1-3-2-4-1-2-2-2-4-2h-1l-9 4v-4h-2v-2c-1-1 0-1 0-2h18 8z" class="f"></path><path d="M555 757c3-1 5 0 8-1h11c2 0 4 1 6 2l-4 1-2 1v3l1 1h0l1 2c-1 0-2-1-3-1s-2 0-2-1l-1 1c-1 0-3 0-4-1h-2l-1-1-1 1-1-1c0-2-1-3-2-4-1-2-2-2-4-2z" class="F"></path><path d="M563 763s0-1-1-2-1-3-2-5l3 1c2 1 5 3 7 4 0 1 0 1 1 2l2-1 1 1 1 1h0l1 2c-1 0-2-1-3-1s-2 0-2-1l-1 1c-1 0-3 0-4-1h-2l-1-1zm-8-112c1 0 1 0 2 1l1 2c0 1 1 2 1 3l1 1c1 0 0 0 0 1h0l1 1v1 1h2 0c1 2 1 2 2 3l1 3-3-3v1c1 2 3 5 4 7 4 8 6 15 7 24l1 5 10 22c-2 1-3 1-5 1v-2h1c-1-1-3-2-4-2-2 0-4 1-5 2l-1 2-2 5-2 4c-2 2-3 4-4 6h0c-1-1-2-1-3-2l-1 1h-1c1-2 2-3 3-5l1-2 4-10v-1-1 1l-2 2-1 1h-9-3-2l-1-1 1-4v-2-4l-1-3c-1 2 1 4-1 5v1c1 1 1 2 1 3v1c-1 1-1 1-1 2s1 1 0 2v-4c-1-1-1-3-1-4h0c0-2-1-3-1-5v-2h-2v-1h2v-5c0-4-1-9 0-12v-4l-2-1c0-2 0-1 1-2 0-1 0-2 1-2l-1-2v-3c0-1 1-2 1-3 0-3-1-2-2-3 1-2 1-3 1-5v-1c0-2 2-3 3-4l4-4c1-2 3-4 4-6z" class="N"></path><path d="M567 693h1v2h-4l-1-2h4z" class="M"></path><path d="M549 713v-1h10 0-3l2 2 1-1c1 0 1 0 1 1l-1 2c-4 0-7 0-10 1v-4z" class="H"></path><path d="M568 708v3 2c-1 1-1 2-2 3h-7l1-2c0-1 0-1-1-1l-1 1-2-2h3 0l1-1c1-1 1 0 2-1 1 0 1-1 2-2h4z" class="B"></path><path d="M568 708v3h-8c1-1 1 0 2-1 1 0 1-1 2-2h4z" class="M"></path><path d="M548 683v-1l2-1 5 1c3 0 8-1 10 0l1 2c-2 0-5 1-6 0h-1c1 0 1 0 2 1h1l-12 1-1 2v-1l-1-4z" class="S"></path><path d="M550 681l5 1c1 0 3 0 4 1h-4v1c-2 0-4 0-5-1v-2z" class="k"></path><path d="M563 693h-13l-1 1v-5h1c4-1 9 0 13 0h4v4h-4z" class="Z"></path><path d="M559 704h8l1 1c-1 1-1 2 0 3h-4c-5 0-9 0-14 1h-1v-5h10z" class="k"></path><path d="M557 676c1 0 3 1 4 1h3l1 2h1c0 1 1 2 1 4s1 5 0 6h-4c1-1 1-1 1-2-1 0-1 0-2-1h3l1-1v-1l-1-2c-2-1-7 0-10 0l-5-1-2 1v1h0v-3-4c2 0 6 1 9 0z" class="X"></path><path d="M557 676c1 0 3 1 4 1h3l1 2h-11c-2 0-3 1-5 1h-1v-4c2 0 6 1 9 0z" class="S"></path><path d="M559 716h7c1-1 1-2 2-3l-1 6c0 1 0 2-1 2v-1 1l-2 2-1 1h-9-3-2l-1-1 1-4v-2c3-1 6-1 10-1z" class="I"></path><path d="M563 724c-2-1-3-1-4-1-1-1-2-1-2-2v-1c1 1 1 1 2 1 3 0 4-1 7 0l-2 2-1 1z" class="E"></path><path d="M549 719h3c1 1 1 2 2 3l-1 1v-1c-1 1-2 1-2 2h-2l-1-1 1-4z" class="Y"></path><path d="M559 716h7c1-1 1-2 2-3l-1 6h-15-3v-2c3-1 6-1 10-1z" class="M"></path><path d="M555 651c1 0 1 0 2 1l1 2c0 1 1 2 1 3l1 1c1 0 0 0 0 1h0l1 1v1 1h2 0c1 2 1 2 2 3l1 3-3-3v1c1 2 3 5 4 7l-2 1h0c0 2 1 3 1 5h-1l-1-2h-3c-1 0-3-1-4-1-3 1-7 0-9 0v4 3h0l1 4-1 23c-1 2 1 4-1 5v1c1 1 1 2 1 3v1c-1 1-1 1-1 2s1 1 0 2v-4c-1-1-1-3-1-4h0c0-2-1-3-1-5v-2h-2v-1h2v-5c0-4-1-9 0-12v-4l-2-1c0-2 0-1 1-2 0-1 0-2 1-2l-1-2v-3c0-1 1-2 1-3 0-3-1-2-2-3 1-2 1-3 1-5v-1c0-2 2-3 3-4l4-4c1-2 3-4 4-6z" class="t"></path><path d="M563 662h0c1 2 1 2 2 3l1 3-3-3v1c1 2 3 5 4 7l-2 1c-3-6-5-2-10-5h0v-1c-2-1-3-1-5-1h0 0v-1h0-2c1-1 2-1 3-2h3c1 0 1 0 2-1 1 0 2 0 2 1 1 0 2 1 3 1h0v-3h2z" class="c"></path><path d="M555 651c1 0 1 0 2 1l1 2c0 1 1 2 1 3l1 1c1 0 0 0 0 1h0l1 1v1 1 3h0c-1 0-2-1-3-1 0-1-1-1-2-1s-2 0-3-1c-3 0-4 0-6-1l4-4c1-2 3-4 4-6z" class="U"></path><path d="M555 651c1 0 1 0 2 1l1 2v1 3h0c-1 0-1 0-1 1h-1c-1-1-2-1-2 0h-1c-1 0-2-1-2-2 1-2 3-4 4-6z" class="r"></path><path d="M555 668v1h0c5 3 7-1 10 5h0c0 2 1 3 1 5h-1l-1-2h-3c-1 0-3-1-4-1-3 1-7 0-9 0l-1-2c0-1-1-2-1-3h2c1-1 2-1 3-2 2 0 2 0 4-1z" class="P"></path><path d="M548 671l13 1v1h-1c-4 0-9 0-12 1h-1c0-1-1-2-1-3h2z" class="d"></path><path d="M555 668v1h0c5 3 7-1 10 5h0c0 2 1 3 1 5h-1l-1-2h-3c-1 0-3-1-4-1l-1-1h0l1-1 3 1c1-1 2-1 3-2l-2-1-13-1c1-1 2-1 3-2 2 0 2 0 4-1z" class="L"></path><path d="M567 673c4 8 6 15 7 24l1 5 10 22c-2 1-3 1-5 1v-2h1c-1-1-3-2-4-2-2 0-4 1-5 2l-1 2-2 5-2 4c-2 2-3 4-4 6h0c-1-1-2-1-3-2l-1 1h-1c1-2 2-3 3-5l1-2 4-10v-1c1 0 1-1 1-2l1-6v-2-3c-1-1-1-2 0-3l-1-1h-8l1-4c-3 0-8-1-10 0l-1 1v-5h12 6l1-1v-2h-1v-4c1-1 0-4 0-6s-1-3-1-4c0-2-1-3-1-5h0l2-1z" class="S"></path><path d="M560 700h2 6v4 1l-1-1h-8l1-4z" class="G"></path><path d="M560 700h2l-1 3c2 1 4 0 6 1h-8l1-4z" class="J"></path><defs><linearGradient id="AC" x1="567.655" y1="693.431" x2="574.332" y2="687.081" xlink:href="#B"><stop offset="0" stop-color="#1e1d21"></stop><stop offset="1" stop-color="#43433e"></stop></linearGradient></defs><path fill="url(#AC)" d="M567 673c4 8 6 15 7 24l1 5-1-1v-1h-1v1c0 2-1 3-1 4 0-11-2-21-7-31h0l2-1z"></path><path d="M568 725c1-2 1-4 2-6 0 2 0 4 1 6l-2 5-2 4c-2 2-3 4-4 6h0c-1-1-2-1-3-2l-1 1h-1c1-2 2-3 3-5l1-2 4-10c1 2-1 3 1 4h0v-1h1z" class="c"></path><path d="M566 722c1 2-1 3 1 4h0v-1h1c-1 4-5 9-8 13l-1 1h-1c1-2 2-3 3-5l1-2 4-10z" class="I"></path><path d="M572 705c0-1 1-2 1-4v-1h1v1l1 1 10 22c-2 1-3 1-5 1v-2h1c-1-1-3-2-4-2-2 0-4 1-5 2l-1 2c-1-2-1-4-1-6 1-5 1-10 2-14z" class="p"></path><path d="M541 588l1 1 1-1 4 2c2 0 3 1 4 1l8 4s1 1 2 1h0c2 0 2-2 4-3h0l1-2c1 1 1 2 2 2h1l1 1c0 1 1 1 2 1 2 0 2 1 3 2 1 2 1 2 2 2h1c1-1 1-1 2-1v2c1 1 2 1 3 2v1c-1 0-1 0-1-1l-1 1 2 2h-2c1 1 1 1 1 2l-2 1h-1c-1 0-1 0-2 1h0-3v3l-1 1-1 1v2 1 3h-1c-1 3-5 5-5 8-1 1 0 4 1 5l8 19h0l18 41 4 11 15 35h1c-1 1-1 2-2 3-1-3-3-7-4-11l-1 1c1 3 3 5 3 8l1 2h-1 0-1-8l-7-1v-5l-2-3h-2l-4-9-10-22-1-5c-1-9-3-16-7-24-1-2-3-5-4-7v-1l3 3-1-3c-1-1-1-1-2-3h0-2v-1-1l-1-1h0c0-1 1-1 0-1l-1-1c0-1-1-2-1-3l-1-2c-1-1-1-1-2-1-1 2-3 4-4 6l-4 4c-1 1-3 2-3 4l-2-1v-1c-1-1-1 0-2-1h-1c2-1 3-1 4-2v-2h0l-1-1-1-1v-1s-1 0-2-1c2 0 3-1 5-1 0-1-1-2-1-3v-1-5h1l-1-2v-1c-1-1-1-1-1-3 1-1 1-3 1-4-1 0-2 0-2-1l1-1v-2l-2 1v-5l1-1h-1l2-1v-1-5h-2v-1l2-1v-3-1c0-2 0-3-1-4l-2-1h0l2-1c1-6 0-13 0-18z" class="B"></path><path d="M557 624l1 1c1 1 1 2 2 3h1l2 5-1 1-2-2s-2-2-3-2l1-1-1-1c-1 0 0-1-1-2v-1l1-1z" class="D"></path><path d="M561 642c0-1-1-2-2-3h0c-1-1-2-2-2-3v-1l-3-2v-1-3c1 0 2 0 3 1 1 0 3 2 3 2-1 1-1 2-1 3h1c1 2 2 3 2 5 1 1 1 1 2 3 1 1 1 3 2 5 1 1 1 2 1 4 1 2 2 3 2 5l2 2h0c0 2 0 2 1 3v2l-2-1-3-3c0-1-1-1-1-1l1-1c-1-1-1-1-1-2l1-1c-1-1-2-1-3-2l-3-2 1-1 1 1c1-1 2-1 2-2l-2-2c0-1-1-2-1-3h1c0-1-1-1-2-2z" class="E"></path><path d="M565 634c-1-1-1-3-1-4 1 1 2 4 2 6l8 19 10 23 4 8c4 7 6 15 9 23l10 22-1 1-10-24-5-13c-1-2-2-4-3-5h-4c0 1 0 1 1 2v1c0 2 2 4 2 6l1 2h-1c-1-2-1-2-3-3l-2-3c-1-2-1-4-1-7l3-3 1-2-20-49z" class="i"></path><path d="M584 690v-1-1-1c0-1 1-2 2-3h1l1 6h-4z" class="k"></path><path d="M566 648c0-4-1-5-2-8s0-2 1-3c0-1-1-1-1-2l1-1 20 49-1 2-1-1-1-1c-2-2-3-5-4-8-1-4-4-7-6-11v-2c-1-1-1-1-1-3h0l-2-2c0-2-1-3-2-5 0-2 0-3-1-4z" class="Z"></path><path d="M588 690c1 1 2 3 3 5l5 13 10 24c1 3 3 5 3 8h0-1c-3-1-4-3-7-5-1-3-3-6-5-9-2-5-4-10-6-14h0c-1-2-2-6-4-7l-7-4c2-1 3 1 5 2h2l1-1h-1l1-1h1l-1-2c0-2-2-4-2-6v-1c-1-1-1-1-1-2h4z" class="S"></path><path d="M579 701c2-1 3 1 5 2h2l1-1h-1l1-1h1l1 2h0c1 3 2 7 4 10l2 5c1 1 1 1 1 2 0 2 1 3 2 4v1l-1-1-1-1c1 3 3 6 4 8l2 2 2 2c2 1 3 3 4 5-3-1-4-3-7-5-1-3-3-6-5-9-2-5-4-10-6-14h0c-1-2-2-6-4-7l-7-4z" class="H"></path><path d="M574 697c2 1 3 4 5 4l7 4c2 1 3 5 4 7h0c2 4 4 9 6 14 2 3 4 6 5 9 3 2 4 4 7 5h1 0l1 2h-1 0-1-8l-7-1v-5l-2-3h-2l-4-9-10-22-1-5z" class="J"></path><path d="M590 712c2 4 4 9 6 14 2 3 4 6 5 9l-2-2c0-1-1-2-1-3l-1 1c-3-4-7-14-7-19z" class="C"></path><path d="M586 720c1 2 3 3 4 5l2 4 2 3 1-1c2 3 4 5 7 7l2 2h1c1 0 2 0 2 1l2 1h-1-8l-7-1v-5l-2-3c-1-5-5-8-5-13z" class="a"></path><path d="M593 736c2 2 4 4 6 5l1 1h0l-7-1v-5z" class="F"></path><path d="M592 729l2 3 1-1c2 3 4 5 7 7l2 2h1c1 0 2 0 2 1-2 0-5 0-7-1-3-1-6-7-8-11z" class="W"></path><path d="M574 697c2 1 3 4 5 4l7 4c-2 1-5-1-7-2v1 1c2 1 3 5 3 7 0 1 1 1 1 2 2 2 2 4 3 6 0 5 4 8 5 13h-2l-4-9-10-22-1-5z" class="F"></path><path d="M561 651l3 2c1 1 2 1 3 2l-1 1c0 1 0 1 1 2l-1 1s1 0 1 1l3 3 2 1c2 4 5 7 6 11 1 3 2 6 4 8l1 1 1 1-3 3c0 3 0 5 1 7l2 3c2 1 2 1 3 3l-1 1h1l-1 1h-2c-2-1-3-3-5-2-2 0-3-3-5-4-1-9-3-16-7-24-1-2-3-5-4-7v-1l3 3-1-3c-1-1-1-1-2-3h0-2v-1-1l-1-1h0c0-1 1-1 0-1l-1-1c0-1-1-2-1-3l-1-2v-1c1 1 2 0 3 1h0l1-1z" class="J"></path><path d="M574 684l1 1 2-2 1 1h0c0 1 0 1 1 2v1h0c-1 0 0 0-1 1h-1-1c-1-2-2-2-2-4z" class="l"></path><path d="M561 651l3 2h-1l-1 1c1 1 2 1 3 2v1h-2c0 1 0 1 1 2h-2v1c1 1 1 1 1 2h-2v-1-1l-1-1h0c0-1 1-1 0-1l-1-1c0-1-1-2-1-3l-1-2v-1c1 1 2 0 3 1h0l1-1z" class="h"></path><path d="M563 662c2 1 3 2 4 3v1c1 1 3 2 4 4 0 1 1 1 2 2v2c1 1 1 2 2 3 0 2 1 3 1 4-1-1-2-2-2-3-1 1-2 1-2 1-2-2-2-4-3-6-1-1-1-1-1-2-1-2-1-2-2-3l-1-3c-1-1-1-1-2-3z" class="W"></path><path d="M570 663l2 1c2 4 5 7 6 11 1 3 2 6 4 8l1 1 1 1-3 3v-3h0l-2-2v-1c-1-1-1-2-1-3-1-1-1-2-2-2 0-1-1-2-1-3-1 0-1-1-1-2-1 0-1-1-1-2-1-1-1-2-2-3s-1-3-1-4z" class="I"></path><path d="M567 673c-1-2-3-5-4-7v-1l3 3c1 1 1 1 2 3 0 1 0 1 1 2 1 2 1 4 3 6 0 2 2 3 2 5s1 2 2 4h1 1c1-1 0-1 1-1 1 4 1 8 4 11h1c2 1 2 1 3 3l-1 1h1l-1 1h-2c-2-1-3-3-5-2-2 0-3-3-5-4-1-9-3-16-7-24z" class="G"></path><path d="M542 616h0c2-1 2-1 2-2h2 2l2 4-2 1 5 3 4 2-1 1v1c1 1 0 2 1 2l1 1-1 1c-1-1-2-1-3-1v3 1l3 2v1c0 1 1 2 2 3h0c1 1 2 2 2 3 1 1 2 1 2 2h-1c0 1 1 2 1 3l2 2c0 1-1 1-2 2l-1-1-1 1-1 1h0c-1-1-2 0-3-1v1c-1-1-1-1-2-1-1 2-3 4-4 6l-4 4c-1 1-3 2-3 4l-2-1v-1c-1-1-1 0-2-1h-1c2-1 3-1 4-2v-2h0l-1-1-1-1v-1s-1 0-2-1c2 0 3-1 5-1 0-1-1-2-1-3v-1-5h1l-1-2v-1c-1-1-1-1-1-3 1-1 1-3 1-4-1 0-2 0-2-1l1-1v-2l-2 1v-5l1-1h-1l2-1v-1-5h-2v-1l2-1z" class="M"></path><path d="M550 624l2 2s1 1 2 1v1l-5-2v1h0c1 2 0 4 1 5 0 2 3 4 4 5s1 1 1 2c-1-1-3-3-5-3l2 3v2h-1-1 0l-3-4-2-3v-1c1 0 2 1 2 1h1c-1-1-1-3-1-4h0v-1-3l-2-2h1c2 1 3 1 4 0z" class="L"></path><path d="M552 639l-2-3c2 0 4 2 5 3s4 3 6 3h0c1 1 2 1 2 2h-1c0 1 1 2 1 3l2 2c0 1-1 1-2 2l-1-1-1 1-1-1h-3c-1 0-1-1-2-2v-1c-1 0-2 0-2-1l-3-5h0 1 1v-2z" class="N"></path><path d="M552 639c3 2 5 5 7 7-1 0-2-1-3 0l4 4h-3c-1 0-1-1-2-2v-1c-1 0-2 0-2-1l-3-5h0 1 1v-2z" class="F"></path><path d="M543 634h2l2 3 3 4 3 5c-2 1-4 3-5 4l-2 1v1c-1 1-1 1-1 2l-1 1v-2c0-1-1-2-1-3v-1-5h1l-1-2v-1c-1-1-1-1-1-3 1-1 1-3 1-4z" class="p"></path><path d="M547 637l3 4 3 5c-2 1-4 3-5 4l-2 1v-2c1 0 1-1 2-1 0-1 1-1 2-1v-1c0-2-1-3-2-4-1-2-1-3-1-5z" class="U"></path><path d="M542 616h0c2-1 2-1 2-2h2 2l2 4-2 1h0l2 3h-3c1 1 2 2 3 2-1 1-2 1-4 0h-1l2 2v3 1h0c0 1 0 3 1 4h-1s-1-1-2-1v1h-2c-1 0-2 0-2-1l1-1v-2l-2 1v-5l1-1h-1l2-1v-1-5h-2v-1l2-1z" class="Q"></path><path d="M546 614h2l2 4-2 1h0l-3-4 1-1z" class="N"></path><path d="M542 624c0-1 1-1 1-2s-1-1 0-2h2v1l-1 1 1 1c0 1-1 1 0 2v7 1 1h-2c-1 0-2 0-2-1l1-1v-2l-2 1v-5l1-1h-1l2-1z" class="b"></path><path d="M541 625c2 2 1 2 2 4l-1 1-2 1v-5l1-1z" class="R"></path><path d="M553 646c0 1 1 1 2 1v1c1 1 1 2 2 2h3l1 1-1 1h0c-1-1-2 0-3-1v1c-1-1-1-1-2-1-1 2-3 4-4 6l-4 4c-1 1-3 2-3 4l-2-1v-1c-1-1-1 0-2-1h-1c2-1 3-1 4-2v-2h0l-1-1-1-1v-1s-1 0-2-1c2 0 3-1 5-1v2l1-1c0-1 0-1 1-2v-1l2-1c1-1 3-3 5-4z" class="P"></path><path d="M553 646c0 1 1 1 2 1v1c1 1 1 2 2 2h3l1 1-1 1h0c-1-1-2 0-3-1v1c-1-1-1-1-2-1l-1-1c-1 1-1 1-1 3h-1l-3-3h-1c1-1 3-3 5-4z" class="L"></path><path d="M541 588l1 1 1-1 4 2c2 0 3 1 4 1l8 4s1 1 2 1h0c2 0 2-2 4-3h0l1-2c1 1 1 2 2 2h1l1 1c0 1 1 1 2 1 2 0 2 1 3 2 1 2 1 2 2 2h1c1-1 1-1 2-1v2c1 1 2 1 3 2v1c-1 0-1 0-1-1l-1 1 2 2h-2c1 1 1 1 1 2l-2 1h-1c-1 0-1 0-2 1h0-3v3l-1 1-1 1v2 1 3c-2-1-2-2-3-2h-1c-2 3-5 5-6 9-1-1-1-2-1-3h-2v2c1 0 1 1 1 2-1-1-1-2-2-3l-1-1-4-2-5-3 2-1-2-4h-2-2c0 1 0 1-2 2h0v-3-1c0-2 0-3-1-4l-2-1h0l2-1c1-6 0-13 0-18z" class="C"></path><path d="M549 594h2c0 1 1 1 2 2h2c-1 1 0 1-1 1l2 2c3 1 3 2 5 3 1 1 2 1 3 2v2c0 1 2 2 2 4l-1-1h-1c-1-1-2-1-3-1h0c-1-1-1-2-1-3-2-2-4-4-7-5-1-1-1 0-2-1h0-2v-2-1h2l-2-2z" class="H"></path><path d="M580 600c1 1 2 1 3 2v1c-1 0-1 0-1-1l-1 1 2 2h-2c1 1 1 1 1 2l-2 1h-1c-1 0-1 0-2 1h0-3v3l-1 1-1 1v2c-1-2 0-5-1-8l-1 1v1c-1-2-1-2-1-3-1-2-4-4-5-5h1c1 1 4 3 5 4s2 2 2 3c2-1 2-2 3-3h0c2-1 3-2 5-4v-2z" class="F"></path><path d="M565 593h0l1-2c1 1 1 2 2 2h1l1 1c0 1 1 1 2 1 2 0 2 1 3 2 1 2 1 2 2 2h1c1-1 1-1 2-1v2 2c-2 2-3 3-5 4h0c-5-4-10-6-14-10h0c2 0 2-2 4-3z" class="q"></path><path d="M565 596h5l1 1c0 1 0 2-1 3l-2-1c-2-1-2-1-3-3z" class="c"></path><path d="M565 593h0l1-2c1 1 1 2 2 2h1l1 1c0 1 1 1 2 1 2 0 2 1 3 2h-3-1l-1-1h-5l-1-1 1-2z" class="N"></path><path d="M571 597h1 3c1 2 1 2 2 2h1c1-1 1-1 2-1v2 2l-4 1v-1c-1-1-2-1-4-1 0 0-1-1-2-1 1-1 1-2 1-3z" class="Q"></path><path d="M571 597h1c1 2 2 3 4 4v1c-1-1-2-1-4-1 0 0-1-1-2-1 1-1 1-2 1-3z" class="b"></path><path d="M557 613c0-1 1-1 2-2l4 2 2 3c0 2-3 3-5 5 3 0 4-2 6-3l-4 4h-2c0 1 1 1 1 2h-2v2c1 0 1 1 1 2-1-1-1-2-2-3l-1-1-4-2-5-3 2-1c0-1 1-1 2-1 1-2 2-3 4-5 0 1 0 0 1 1z" class="n"></path><path d="M558 615c2-1 4-1 5 0l-1 3h-3l-1-1h-1v-1l1-1z" class="f"></path><path d="M550 618c0-1 1-1 2-1 1-2 2-3 4-5 0 1 0 0 1 1-1 2-2 3-2 4l-1 1c1 1 2 2 3 2 1 1 2 1 3 2 0 1 1 1 1 2h-2v2c1 0 1 1 1 2-1-1-1-2-2-3l-1-1-4-2-5-3 2-1z" class="l"></path><path d="M553 622v-2h3 1c1 1 2 1 3 2 0 1 1 1 1 2h-2v2c1 0 1 1 1 2-1-1-1-2-2-3l-1-1-4-2z" class="I"></path><path d="M541 588l1 1c2 1 8 3 9 5h-2l2 2h-2v1 2h2 0c1 1 1 0 2 1 3 1 5 3 7 5 0 1 0 2 1 3h0c1 0 2 0 3 1h1l-1 1c-1-1-2-1-3 0v1l2 2h0l-4-2c-1 1-2 1-2 2-1-1-1 0-1-1-2 2-3 3-4 5-1 0-2 0-2 1l-2-4h-2-2c0 1 0 1-2 2h0v-3-1c0-2 0-3-1-4l-2-1h0l2-1c1-6 0-13 0-18z" class="E"></path><path d="M549 599h2 0c1 1 1 0 2 1 3 1 5 3 7 5-2 2-2 3-2 5h0-1v-3l1-1v-1h-1v1c-2-1-3-3-5-3l-1-1h0l-2 2v1h-2c0-2 1-4 2-6z" class="S"></path><path d="M548 607c1 1 2 1 3 2 0 2-1 4-3 5h0-2-2c0 1 0 1-2 2h0v-3-1c1-1 1-1 2-1l1-2 2-2h1z" class="K"></path><path d="M545 609l2-2 1 2h1v1c-1 1-2 2-4 2l-1-1 1-2z" class="m"></path><path d="M541 588l1 1c2 1 8 3 9 5h-2l2 2h-2v1 2c-1 2-2 4-2 6h2v-1l2-2h0c0 1 0 2-1 3l-1 1h-1v1h-1l-2 2-1 2c-1 0-1 0-2 1 0-2 0-3-1-4l-2-1h0l2-1c1-6 0-13 0-18z" class="Q"></path><path d="M545 609v-9c0-1 0-1-1-2 0-1 1-2 0-2 0-1 0-1 1-2l1 1v-1c-1 0-1-1-1-2 2 0 3 1 4 2l2 2h-2v1 2c-1 2-2 4-2 6h2v-1l2-2h0c0 1 0 2-1 3l-1 1h-1v1h-1l-2 2z" class="J"></path><path d="M490 591c1 8 0 16 0 24v16 10 7c1 5 0 11 1 16 0 1 0 2-1 3h2v1l-2 2h2c0 1-1 1-1 2s1 1 1 2v1c-1 2-2 6-1 9l-1 1v4 2l-1 3c0-1-1-2-2-3-1 0-2-2-3-4l-8-12c-3-3-5-7-8-8-2-1-4-1-6-1v1c0 1-1 1-1 2-1 0-1 1-1 2v2c0 8 1 19-1 27 0 0 0 1-1 2v2 1c-1 2-1 3-1 5v-4c-1-3 0-5 0-9v-10h0 0v-1-1-9l-3 12v2h-1l-4 22-2 8-3 24h-15-2c-5 0-9 0-14 1v1c1 1 4 0 6 0h2 5-1c-2 0-5 0-8 1h-4l-1 1c1 1 3 0 5 0-1 1-2 1-3 2-2 0-4 1-5 2 1-3 3-6 4-9s2-5 3-7v-1h-1l5-11c1-3 3-6 4-10 1-2 3-5 4-7 0-1 0-2 1-2 1-3 2-4 2-6 0 0 3-7 4-8 2-5 4-10 6-16 1-2 2-4 2-6v-1-6l1-2c3-2 5-6 8-9l7-11c1-1 4-5 5-7l-2-3-7 4-1-1 5-3h-2c2-2 4-2 5-4 3 0 5-1 7-3l6-6c1-1 2-2 2-3 2-2 4-4 6-5 2-2 3-3 4-5 1-1 2-1 3-1-1-3-1-6-1-8v-4h1z" class="l"></path><path d="M453 683c0 1 1 2 0 4h0v3l-4 22h-1v-3c1-1 1-2 1-3l-2-1h0l-2 1v1l-1-1 9-23z" class="b"></path><path d="M416 735h1l9-21c0 2-2 6-3 8l-3 8-1 1 1 1v1l-1 2h0v1l-2 2c0 1 0 1 1 2l-1 1 1 1 1-2v3h1c1-2 2-2 2-4v1l-1 2 1 1 1-1s0-1 1-1v1c2 0 1-1 3 0h0v2c-5 0-9 0-14 1v1c1 1 4 0 6 0h2 5-1c-2 0-5 0-8 1h-4l-1 1c1 1 3 0 5 0-1 1-2 1-3 2-2 0-4 1-5 2 1-3 3-6 4-9s2-5 3-7v-1zm29-29l2-1h0l2 1c0 1 0 2-1 3v3h1l-2 8h0c-1-1-1-1-1-2l-1-2c-1 0-1 0-2 1s0 2-1 4c-1 1-1 3-2 4s-1 1-1 2c-1 3-2 7-3 10-1 2-1 3-1 5v1c-1-1-1-1-1-2l-1 2-4 1c1-2 2-4 2-6l13-32 1 1v-1z" class="V"></path><path d="M445 706l2-1h0v1 6c-1-1-1 0-1-1-2 1-1 3-3 3 0-1 0-3 1-4l1-1v-3zm-14 32c1 1 1 1 1 2 1-1 2-3 2-4 2-1 1-4 2-6l1 1 1-1-1-1c1-1 1-2 2-3v1c-1 3-2 7-3 10-1 2-1 3-1 5v1c-1-1-1-1-1-2l-1 2-4 1c1-2 2-4 2-6z" class="f"></path><path d="M442 721c1-2 0-3 1-4s1-1 2-1l1 2c0 1 0 1 1 2h0l-3 24h-15 0l4-1 1-2c0 1 0 1 1 2v-1c0-2 0-3 1-5 1-3 2-7 3-10 0-1 0-1 1-2s1-3 2-4z" class="Q"></path><path d="M442 721v4s0 1-1 1c-1 2-2 4-1 6-1 3-1 6-2 8-1 0-2-2-2-3 1-3 2-7 3-10 0-1 0-1 1-2s1-3 2-4z" class="e"></path><path d="M440 732l1 1c0 1 1 1 1 2v1c0 2-1 4 0 7h0c-2 0-4 1-6 0v-1h-1c0-2 0-3 1-5 0 1 1 3 2 3 1-2 1-5 2-8z" class="K"></path><path d="M482 636v1c-1 1-1 2-2 4h1 0v1 1c-1 0-2 0-3 1 0 1-1 2-2 2l-9 12s0 1-1 2v2c-2 1-2 2-3 3h0l-1 1v1c0 1-1 1-1 2-1 0-1 1-1 2v2c0 8 1 19-1 27 0 0 0 1-1 2v2 1c-1 2-1 3-1 5v-4c-1-3 0-5 0-9v-10h0 0v-1-1-9l-3 12v2h-1v-3h0c1-2 0-3 0-4 4-13 10-26 19-37 3-4 7-7 10-10z" class="n"></path><path d="M476 627c0-1 1-1 2-1s2 2 4 2c1 1 1 2 3 2l1 1 2-2 1 1-1 1-2 1-2 2h-1l-2 1c-1 1-1 2-3 3v-1l-3-2v1c0 1 1 1 0 2h0c-1 0-2 1-2 1v2l-1 1c0 1-2 1-2 2-2 1-3 2-4 4-2-2-4-4-5-6l-1 1h0l3 4h0c-1-1-2-1-3-1h1l2 4h0l-2-1s-1 1-1 2l-3 3v-1l1-1v-1l-1 1-2 2v-1c1-1 1-2 2-3v-1c0-1 1-1 1-2v-1h-1c-1 2-1 3-1 5l-1 1h-1v-1l1-1c0-2 1-2 2-4v-1c1-2 2-3 4-4l1-1 1-2c0-1 3-3 4-4 2-3 4-5 7-7l1 1 1-1z" class="X"></path><path d="M475 636h-1l-2-2c1 0 1-1 2-2h2c1 0 1 1 2 1h2s1 1 1 2c-1 1-1 2-3 3v-1l-3-2v1z" class="j"></path><path d="M463 638c0-1 3-3 4-4 2-3 4-5 7-7l1 1-1 1 1 1c0 1 0 1-1 1-1 1-1 1-2 1l-1 1v-1c-3 1-5 5-8 7v-1zm13-11c0-1 1-1 2-1s2 2 4 2c1 1 1 2 3 2l1 1 2-2 1 1-1 1-2 1-2 2h-2v-1s-1-3-2-3h-1l-3-3z" class="i"></path><defs><linearGradient id="AD" x1="470.389" y1="610.955" x2="473.277" y2="626.966" xlink:href="#B"><stop offset="0" stop-color="#848283"></stop><stop offset="1" stop-color="#b4b4b2"></stop></linearGradient></defs><path fill="url(#AD)" d="M490 591c1 8 0 16 0 24v16l-1-1-1-1-2 2-1-1c-2 0-2-1-3-2-2 0-3-2-4-2s-2 0-2 1l-1 1-1-1 5-3 9-6h0l-6 3c-3 2-5 3-8 5-3 1-7 5-10 7l-2-3-7 4-1-1 5-3h-2c2-2 4-2 5-4 3 0 5-1 7-3l6-6c1-1 2-2 2-3 2-2 4-4 6-5 2-2 3-3 4-5 1-1 2-1 3-1-1-3-1-6-1-8v-4h1z"></path><path d="M481 614h1c1 0 2-1 2-1 0-1 1-1 1-2l1 3c-2 1-3 3-5 4l1 1v2l-8 5c-3 1-7 5-10 7l-2-3 15-8h0-1 0l2-3h0c1-2 2-3 3-5z" class="q"></path><path d="M490 591c1 8 0 16 0 24v16l-1-1-1-1-2 2-1-1c-2 0-2-1-3-2-2 0-3-2-4-2s-2 0-2 1l-1 1-1-1 5-3 9-6h0l-6 3v-2l-1-1c2-1 3-3 5-4l-1-3c0 1-1 1-1 2 0 0-1 1-2 1h-1l5-5 2-4-1-1c1-1 2-1 3-1-1-3-1-6-1-8v-4h1z" class="n"></path><path d="M484 628l2-1 1 1h2c-1 1 0 1-1 1l-2 2-1-1-1-2zm2-14l2-2v1c0 2 0 2-1 3s-2 1-2 1h-1-1l-1 2-1-1c2-1 3-3 5-4zm1-10c1-1 2-1 3-1l-1 7c-1 1-1 1-2 1l-1-2 2-4-1-1z" class="K"></path><path d="M479 624h1c2 0 4-3 6-3v1l-1 1v2h0l-2 2 1 1 1 2c-2 0-2-1-3-2-2 0-3-2-4-2s-2 0-2 1l-1 1-1-1 5-3z" class="e"></path><path d="M489 630l1 1v10 7c1 5 0 11 1 16 0 1 0 2-1 3h2v1l-2 2h2c0 1-1 1-1 2s1 1 1 2v1c-1 2-2 6-1 9l-1 1v4 2l-1 3c0-1-1-2-2-3-1 0-2-2-3-4l-8-12c-3-3-5-7-8-8-2-1-4-1-6-1l1-1h0c1-1 1-2 3-3v-2c1-1 1-2 1-2l9-12c1 0 2-1 2-2 1-1 2-1 3-1v-1-1h0-1c1-2 1-3 2-4v-1l1-2h1l2-2 2-1 1-1z" class="i"></path><path d="M466 662v-1c1 0 1-1 2-1s2 2 3 3h0l-2 1c-1 0-1 1-1 1v2c-2-1-4-1-6-1l1-1h0c1-1 1-2 3-3z" class="X"></path><path d="M485 673h-3c-1-1-2-1-2-2h1c-1-2-3-3-4-4s-3-3-3-5c-1-2-2-3-2-5l1 1h1c0 1 0 1 1 3l1 1c1 1 1 2 3 3 1 0 2-1 3-1l1-1h2c-1 1-1 1-1 2l1 1c-1 1-2 1-3 2v1h2l1 1-2 1h0 2v1 1z" class="N"></path><path d="M468 667v-2s0-1 1-1l2-1c1 3 3 5 5 7 1 0 1 1 1 2 1 2 3 4 4 6v1 2c1 2 2 3 3 5 0 1 3 4 3 5-1 0-2-2-3-4l-8-12c-3-3-5-7-8-8z" class="G"></path><defs><linearGradient id="AE" x1="480.325" y1="656.666" x2="476.977" y2="663.222" xlink:href="#B"><stop offset="0" stop-color="#878786"></stop><stop offset="1" stop-color="#a7a6a5"></stop></linearGradient></defs><path fill="url(#AE)" d="M473 658c1-1 1-2 2-3l1-1 1 2h4 2l1-1 2 1-1 1h-1c0 1 0 0 1 2v4h-2l-1 1c-1 0-2 1-3 1-2-1-2-2-3-3l-1-1c-1-2-1-2-1-3h-1z"></path><path d="M474 658l1-1 2 5h-1l-1-1c-1-2-1-2-1-3z" class="J"></path><path d="M489 630l1 1v10 1 7h-1l1-1h-2l1-1h0v-1l-1-1c-2 0-3 1-4 1v1l-1-1c1 0 1-1 2-1h1v-1h-2l-1-1h3v-1c-1 0-1 0-2-1h1 1 0l-1-1c1-1 1-1 1-2h0c-2 1-3 2-5 3h0-1c1-2 1-3 2-4v-1l1-2h1l2-2 2-1 1-1z" class="t"></path><path d="M488 631l1 1v2l-2 2-1-1v-3l2-1z" class="f"></path><path d="M490 641v7c1 5 0 11 1 16 0 1 0 2-1 3-1 0-1 0-3-1l1-1-2-1 2-2c-1 0-1 0-1-1 0 0 1-1 1-2l-3-2 1-1-2-1h-1 0-1c1-1 1-2 1-3h-1c1-3 4-4 6-6v-1l1 1v1h0l-1 1h2l-1 1h1v-7-1z" class="K"></path><path d="M485 663v-4c-1-2-1-1-1-2h1l3 2c0 1-1 2-1 2 0 1 0 1 1 1l-2 2 2 1-1 1c2 1 2 1 3 1h2v1l-2 2h2c0 1-1 1-1 2s1 1 1 2v1c-1 2-2 6-1 9l-1 1v4 2l-1 3c0-1-1-2-2-3 0-1-3-4-3-5-1-2-2-3-3-5h1 2 1v1c2 0 2 0 3-1v-1c-1 1-1 1-3 1l1-1c-1-1-1-2-1-3 1 0 2-1 3-1s0 0 1-1c-2 0-4 0-6 1h0v-1c1 0 2-1 4-1 1 0 0 0 1-1h-3 0v-1-1h-2 0l2-1-1-1h-2v-1c1-1 2-1 3-2l-1-1c0-1 0-1 1-2z" class="V"></path><path d="M361 619c-1-2-4-4-6-5 2 0 3 1 4 2 2 1 4 2 7 3 1 0 1 1 2 1h2l1-1c3 1 6 3 9 3l23-1v1c0 1 1 2 2 3h1 1c2 2 6 3 9 3h0 2v1h3c2 0 5 1 7 0h4 1c2 0 3-1 4 0l1 2v-1h2l1-1c2 0 3 0 5-1h1 1v-2c3 0 4 0 6 2l1-1v1l-1 2 1-1c1 0 2 1 2 1h2l-5 3 1 1 7-4 2 3c-1 2-4 6-5 7l-7 11c-3 3-5 7-8 9l-1 2v6 1c0 2-1 4-2 6-2 6-4 11-6 16-1 1-4 8-4 8 0 2-1 3-2 6-1 0-1 1-1 2-1 2-3 5-4 7-1 4-3 7-4 10l-5 11h-1c-1-4-3-7-5-11l-9-21-20-44-15-34-3-3 1-1-2-2z" class="p"></path><path d="M431 699c0 2-1 3-2 6-1 0-1 1-1 2-2 1-4 3-5 6h0l-3 1-1-1c2-3 5-5 8-8 2-2 3-4 4-6z" class="D"></path><path d="M380 627c3 1 8 3 11 2 7 2 15 1 23 1-5 2-11 2-16 2-2-1-4-1-6-1h-1-2c-1-1-1-1-2-1h-1c-1 0-2 0-3-1h-1c-1 0-2-1-2-2z" class="U"></path><path d="M406 625h1c2 2 6 3 9 3h0 2v1h3l-7 1c-8 0-16 1-23-1h1c3-1 7-1 10-1l5-1h0l-2-2h1z" class="G"></path><path d="M432 629h1c2 0 3-1 4 0l1 2-6 2-2 2c-1 1-2 1-3 2-1 0-1 1-2 1v1h1c-1 1-4 2-5 4h0c0 1-1 2-2 2-3 1-6 3-10 4 1 0 2-1 3-2h0c1 0 1-1 2-1 1-1 0 1 2-1 1-1 2-2 4-3h1v-1l1-2c2-2 6-4 7-7h1v-1l-2-2h4z" class="c"></path><path d="M361 619c-1-2-4-4-6-5 2 0 3 1 4 2 2 1 4 2 7 3 1 0 1 1 2 1h2l1-1c3 1 6 3 9 3l23-1v1c0 1 1 2 2 3l2 2h0l-5 1c-3 0-7 0-10 1h-1c-3 1-8-1-11-2h-1c-3-1-6-2-8-3h-4l-2 1-3-3 1-1-2-2z" class="i"></path><path d="M362 622l1-1 8 3h-4l-2 1-3-3z" class="f"></path><path d="M379 627c4-3 11 0 15 0 3 0 5 1 8 1-3 0-7 0-10 1h-1c-3 1-8-1-11-2h-1z" class="X"></path><path d="M406 669h5c2 1 5 3 6 5s1 5 0 7c-1 4-3 5-7 6v1c-1 0-3 0-4-1-2 0-4-2-6-4-1-2-1-5 0-7 1-3 3-5 6-7z" class="P"></path><path d="M406 676h1c2 0 3 0 5 2v2c0 2-1 2-3 3l-1 1c-1 0-3-1-4-2s-1-2-1-4c1-1 2-2 3-2z" class="p"></path><path d="M441 658h2v1l1 1-1 2v6 1c0 2-1 4-2 6-2 6-4 11-6 16v-3l1-1v-1h0c1-2 1-4 2-5v-1c1-2 0 0 1-1v-1-2h-1v2c-1 1-1 4-2 5l-4 2 1 2-3 2h0-1c-2 1-3 1-4 1l1-1c1 1 1 1 2 0s1-1 2-1 1 0 2-1v-2-1c1 0 1 0 2-1-1 0-1-1-2-1v-1h0v-1l-1 1h0c0 1-1 1-1 2v1h-1 0c1-1 0-2 0-3v-1h0c-1 0-2-2-3-3v-2-1-1h-1l1-2v-1l-3-1v-1l1-1c0-1-1 0-3 0l2-2v-1h1 1l-1 1c2 0 2-1 4 0l1-1h-1l1-1h1c2 0 5-1 6-2l3-1c0-1 2-2 2-2z" class="V"></path><path d="M430 672c1-1 2-2 4-2l1-1c2-1 3-1 5-1v2c-1 1-1 4-2 5 0 1-2 1-2 3h-3 0c1-2 2-1 2-2-1 0-1 0-2 1h0-1-4v-1h3v-1h-1-3l1-2-2-1c1 0 2-1 2-1l2 1z" class="O"></path><path d="M430 672c1-1 2-2 4-2l1-1c2-1 3-1 5-1v2l-3 1c-1 1 0 1-1 2h1v1h-1c-2-1-4-1-6 0h-1l1-1 1-1h-1z" class="e"></path><path d="M441 658h2v1l1 1-1 2c-1 1-3 1-3 2v2l-1 1h2l-1 1c-2 0-3 0-5 1l-1 1c-2 0-3 1-4 2l-2-1h1v-1l-1 1-1-1c0-1-1-1-1-3l2-2 1-1h-1l1-1h1c2 0 5-1 6-2l3-1c0-1 2-2 2-2z" class="F"></path><path d="M439 660l-2 2-2 1h-1l-2 2h1l4-2v1h-1l-2 2c-2 1-4 2-6 4l5-2c-1 2-2 2-4 3v-1l-1 1-1-1c0-1-1-1-1-3l2-2 1-1h-1l1-1h1c2 0 5-1 6-2l3-1z" class="O"></path><path d="M448 626c3 0 4 0 6 2l1-1v1l-1 2 1-1c1 0 2 1 2 1h2l-5 3 1 1 7-4 2 3c-1 2-4 6-5 7l-7 11c-3 3-5 7-8 9l-1-1v-1h-2s-2 1-2 2l-3 1c-1 1-4 2-6 2h-1l-1 1h1l-1 1c-2-1-2 0-4 0l1-1h-1-1v1l-2 2c2 0 3-1 3 0l-1 1v1l3 1v1l-1 2h1v1 1l-1 2h0v2 1h-1c-1-1 0 1-1-1v-1l-2 1v3c0 1 0 3 1 4l1 1h2l1 1v1h-2c0-1-3-2-3-3v-3c-1-4-1-8-2-11-1-1-1-2-2-3v-3h-3c-3-1-6-1-9 0h0l-1-1c3-1 6-1 9-2v-1c-1 1-2 1-3 1l-1-1 3-1v-1l-4 1c0-1 1-1 2-2h0l-1-1c1 0 1 0 2-1h-2s1-1 2-1l1-1c-1 0-2 1-4 0l3-2h-1c1-1 2-1 3-2l-1-1c1-1 3-2 4-2 1-1 2-1 3-1 0-1 0-1 1-1 1-1 2-2 2-3h-1 0c1-2 4-3 5-4h-1v-1c1 0 1-1 2-1 1-1 2-1 3-2l2-2 6-2v-1h2l1-1c2 0 3 0 5-1h1 1v-2z" class="b"></path><path d="M423 669l3 1v1l-1 2h1v1 1l-1 2h0v2 1h-1c-1-1 0 1-1-1v-1h0-1l1-1-1-1v-1h2v-1l-1-1 1-1-1-1v-2z" class="K"></path><path d="M424 654h1c1 0 2 0 3-1h1c0 1-1 1-1 2h3c0 1-1 1-2 1l1 1v2h1l-1 2h1l1-1 1 1-2 1-2 1-1 1h1l-1 1c-2-1-2 0-4 0l1-1h-1-1c-1 1-2 1-3 1 2-1 3-2 5-3-3 1-6 2-9 2l-1-1c4 0 6-2 10-3h-3c-2 1-3 1-4 1 1-1 2-2 4-2h0c-1-1-3 1-5 0l1-1c1 0 4-1 5-2h-2 0c1-1 2-2 3-2z" class="q"></path><path d="M431 662c-2-1-2 1-5-1v-1c-1 0-1 1-2 1l3-4h-3v-1c2-1 1-1 4-1h3c0 1-1 1-2 1l1 1v2h1l-1 2h1l1-1 1 1-2 1z" class="F"></path><path d="M421 643h0c1-2 4-3 5-4h-1v-1c1 0 1-1 2-1 1-1 2-1 3-2l2-2c1 1 2 1 3 1-2 2-5 4-8 5l1 1h0c0 1-1 1-1 2-1 0-2 0-2 1s-1 2-2 3c1 0 1 0 2-1 0 1-1 2-1 2l-3 2h2 0l-4 2 1 1c1 0 1 0 2-1l1 1-1 1c2 0 3-1 4-1h0l-2 2c-1 0-2 1-3 2h0 2c-1 1-4 2-5 2v-1c-1 0-2 0-3 1h-2c1 0 2-2 3-3h0-1c-1 1-1 1-2 1 1-2 5-4 6-6-1 1-2 1-3 2h-2c1 0 2-1 3-2l2-1v-1l-2 1h0-1c2-1 3-1 4-2v-1c1-1 2-2 2-3h-1z" class="Q"></path><path d="M435 634h4l-1 1h2l-2 2c-1 1-2 2-2 3h0 2c-1 1-3 2-3 3h0c0 1-2 1-3 2l-2 1-1 1c-1 0-3 1-3 2h2c-2 2-3 3-5 3l-1-1c-1 1-1 1-2 1l-1-1 4-2h0-2l3-2s1-1 1-2c-1 1-1 1-2 1 1-1 2-2 2-3s1-1 2-1c0-1 1-1 1-2h0l-1-1c3-1 6-3 8-5z" class="L"></path><path d="M438 635h2l-2 2c-1 1-2 2-2 3h0 2c-1 1-3 2-3 3h0c0 1-2 1-3 2l-2 1v-1c-1 1-1 1-2 1h0-1c1-1 2-2 4-3v-1c-1 1-2 1-4 2v-1c2 0 3-1 4-3 2-2 4-3 7-5z" class="P"></path><path d="M423 652c2 0 3-1 5-3h-2c0-1 2-2 3-2v1l1-1h1 0l-3 3c1 0 2 0 3-1v1c1 1 1 1 2 1l1-1c1 1 2 1 2 2l-1 1h1 3c-1 1-2 1-3 2h0c1 0 3 0 4-1h3l2-1v2s-3 3-4 3c0 0-2 1-2 2l-3 1c-1 1-4 2-6 2h-1l2-1 2-1-1-1-1 1h-1l1-2h-1v-2l-1-1c1 0 2 0 2-1h-3c0-1 1-1 1-2h-1c-1 1-2 1-3 1h-1l2-2h0c-1 0-2 1-4 1l1-1z" class="h"></path><path d="M430 657c2 0 3 0 5-1 0 1 0 1-1 2h2s1-1 2-1 2 0 3-1h0c-1 1-3 3-5 3v1 1c-1 1-4 2-6 2h-1l2-1 2-1-1-1-1 1h-1l1-2h-1v-2z" class="i"></path><path d="M448 626c3 0 4 0 6 2l1-1v1l-1 2 1-1c1 0 2 1 2 1h2l-5 3c-2 2-5 2-8 3-1 1-1 1-2 1l-6 3h-2 0c0-1 1-2 2-3l2-2h-2l1-1h-4c-1 0-2 0-3-1l6-2v-1h2l1-1c2 0 3 0 5-1h1 1v-2z" class="G"></path><path d="M440 635c1-1 1-1 2-1 2-3 5-2 8-3l1-1v1l-1 1c-1 0-1 0-2 1-2 1-7 3-10 4l2-2z" class="X"></path><path d="M448 626c3 0 4 0 6 2l1-1v1l-1 2c-1 0-2 1-3 1v-1l-1 1c-3 1-6 0-8 3-1 0-1 0-2 1h-2l1-1h-4c-1 0-2 0-3-1l6-2v-1h2l1-1c2 0 3 0 5-1h1 1v-2z" class="i"></path><path d="M438 631v-1h2l1-1c2 0 3 0 5-1-2 2-3 3-5 4-1 1-2 1-2 2h-4c-1 0-2 0-3-1l6-2z" class="O"></path><path d="M455 634l7-4 2 3c-1 2-4 6-5 7l-7 11c-3 3-5 7-8 9l-1-1v-1h-2c1 0 4-3 4-3v-2l-2 1h-3c-1 1-3 1-4 1h0c1-1 2-1 3-2h-3-1l1-1c0-1-1-1-2-2l-1 1c-1 0-1 0-2-1v-1c-1 1-2 1-3 1l3-3h0-1l-1 1v-1l1-1 2-1c1-1 3-1 3-2h0c0-1 2-2 3-3l6-3c1 0 1 0 2-1 3-1 6-1 8-3l1 1z" class="N"></path><path d="M452 640h3 0v1 1c1-2 1-2 3-2h1l-7 11c-1-2-1-2-1-3l-1-1 1-1h-1c1-2 3-4 4-5h0l-1 1c-1-1-1-1-2-1h0l1-1z" class="e"></path><path d="M455 634l7-4 2 3c-1 2-4 6-5 7h-1c-2 0-2 0-3 2v-1-1h0-3v-1h-2c2-1 2-1 3-3h-2c1-1 3-2 4-2z" class="Q"></path><path d="M450 647l1 1c0 1 0 1 1 3-3 3-5 7-8 9l-1-1v-1h-2c1 0 4-3 4-3v-2l-2 1h-3c-1 1-3 1-4 1h0c1-1 2-1 3-2h-3-1l1-1c2 0 3 0 4-1s2 0 3-1c-3-2-3 0-5 0 0-1 1-1 2-2h3c2-1 2-1 4-1l1 1 2-1z" class="D"></path><path d="M450 647l1 1c0 1 0 1 1 3-3 3-5 7-8 9l-1-1v-1h-2c1 0 4-3 4-3v-2l2-1v-1c-1 1-2 1-3 1 1-1 2-1 3-2 1 0 1 0 2-1h-2l1-1 2-1z" class="q"></path><path d="M438 640l6-3c1 0 1 0 2-1 3-1 6-1 8-3l1 1c-1 0-3 1-4 2h2c-1 2-1 2-3 3-3 1-6 3-10 5h1l6-3v1c-1 1-3 2-4 2-1 1-2 2-3 2-1 2-3 2-5 3v-2h-1l1-2h0-1-2c1-1 3-1 3-2h0c0-1 2-2 3-3z" class="C"></path><path d="M465 470c3 0 6 4 8 6v1l2 2 4 6v1c2 0 3 0 5-1 2 0 4 0 5-1h0c4-1 10 0 14-1h4 2v-2l2-2 1 3 2 1c6 1 11 4 15 9l1 1v1c2 2 3 5 4 8 0 2 0 4 1 6l1-2v8c1 1 1 1 3 1l1-2c0 1 1 1 1 1h1v1h-2v1c3 3 0 11 1 15 1 1 0 4 0 5 0 8-1 17 0 25 0 3-1 8 0 11v11l2 2v3l-1 1-1-1c0 5 1 12 0 18l-2 1h0l2 1c1 1 1 2 1 4v1 3l-2 1v1h2v5 1l-2 1h1l-1 1v5l2-1v2l-1 1c0 1 1 1 2 1 0 1 0 3-1 4 0 2 0 2 1 3v1l1 2h-1v5 1c0 1 1 2 1 3-2 0-3 1-5 1 1 1 2 1 2 1v1l1 1 1 1h0v2c-1 1-2 1-4 2h1c1 1 1 0 2 1v1l2 1v1c0 2 0 3-1 5 1 1 2 0 2 3 0 1-1 2-1 3v3l1 2c-1 0-1 1-1 2-1 1-1 0-1 2l2 1v4c-1 3 0 8 0 12l-3-3 1-1c-4 0-8 0-12 1h-5c-2-1-4-1-5-3h-1v-1h0-2v-1c1 0 2 0 3-1h-6-2-10-9-1c-1 0-1 1-2 1s-1-1-2-1l1-3v-2-4l1-1c-1-3 0-7 1-9v-1c0-1-1-1-1-2s1-1 1-2h-2l2-2v-1h-2c1-1 1-2 1-3-1-5 0-11-1-16v-7-10-16c0-8 1-16 0-24l-1-15v-21l2-49 1-1 1-1-2-1v-4l-1-1 1-2h0l-2-3-3-3h-7l-1-1c-1 0-2 0-3-1h-1c-1 0-2-1-2-2h0l2 1v-1l-6-5 1-1 1-1-1-1c-1-3-3-5-5-7l1-1h0z" class="G"></path><path d="M503 595c1 0 0-1 0-1 1-4-1-14 1-17v11 7c1 1 1 1 2 1v1h-1l-1 1 1 1-1 1c1 2 0 3 1 4v2 2c-1 0-1 0-1 1-2-2-1-11-1-14z" class="X"></path><path d="M519 613l17-1c1 1 2 1 3 1h1c-1 1-2 2-3 2l-8 1c-1 0-3 0-5-1-1 0-2 0-2-1-1 0-2 0-3-1z" class="k"></path><path d="M504 546c1 5 0 9 0 14v17c-2 3 0 13-1 17 0 0 1 1 0 1l1-49z" class="P"></path><path d="M524 608c4 0 11-1 15 1h-6c-1 1-2 2-3 2h-4c-4 1-8 1-12 0-2 0-4 0-5-1 3-2 7-1 10-2h5z" class="T"></path><path d="M509 534h1l1 41c0 2-1 3-1 5h0c0-1-1-3-1-4v-26c0-6-1-11 0-16z" class="Z"></path><path d="M513 614c1-1 4-1 6-1 1 1 2 1 3 1 0 1 1 1 2 1 2 1 4 1 5 1-2 0-2 0-3 1l-2 1v1l-1 1c-2 1-6 0-8 2h-3l1-1-2-2h0c-1-2-1-3 0-5h2z" class="S"></path><path d="M511 619c-1-2-1-3 0-5h2v1h5l1 1c-2 0-2 0-3 1l-1 1 1 1h-2v-1l-1-2h0v2h0c-1 0-1 0-2 1h0z" class="C"></path><path d="M519 616h3c1 1 1 2 1 3h-7l-1-1 1-1c1-1 1-1 3-1z" class="Q"></path><path d="M513 614c1-1 4-1 6-1 1 1 2 1 3 1 0 1 1 1 2 1 2 1 4 1 5 1-2 0-2 0-3 1l-2 1v1l-1 1v-1c0-1 0-2-1-3h-3l-1-1h-5v-1z" class="B"></path><path d="M509 576c0 1 1 3 1 4h0c0-2 1-3 1-5v19c-1 2-1 8-1 11 1 0 1 0 1 1l2-2h-1c0-1 1-2 1-2-1-1-1-2-1-3 1-1 2-1 3-2l-1-1h-1c0-1 1-2 1-3 1-1 2-2 2-3s-1-1-1-2h4l-1 1 1 1h-1l-1 1c1 1 1 1 1 2-1 3-1 5 0 7v4h-1-1v1c1 0 1 0 2 2h-7v-1l-1 2h-1v-12-5c-1-4 0-11 0-15z" class="E"></path><path d="M510 534l1-1h1v1 5 3 15 10 4h1c2 1 2 1 3 3 0 2 0 5-1 7v2c0 2-1 3-2 4v1c1 0 1 0 2 2 0 1 0 1-1 2v1c0 1-1 2-1 3h1l1 1c-1 1-2 1-3 2 0 1 0 2 1 3 0 0-1 1-1 2h1l-2 2c0-1 0-1-1-1 0-3 0-9 1-11v-19l-1-41z" class="N"></path><path d="M519 533v2c0 1-2 3-3 4v3c0 3 0 5-1 8v2c2 0 2 0 4-1 1 0 2-1 3-1 2 0 2 0 3 1l-1 1c-2 0-4-1-6 1 0 1 1 2 1 3 2 3 2 7 1 10-2 6-3 12-3 19h1 2l2 1c0 1 0 1-1 2l-1-1-1 1h-4c0 1 1 1 1 2s-1 2-2 3v-1c1-1 1-1 1-2-1-2-1-2-2-2v-1c1-1 2-2 2-4v-2c1-2 1-5 1-7-1-2-1-2-3-3h-1v-4-10-15-3-5h2 1c1 0 3 0 4-1z" class="I"></path><path d="M512 557c2 0 3 0 5 1l1 1c-1 1-1 2 0 3 0 0 1 0 1 1s0 3-1 4-2 1-2 1l-1 1h0l-1-1 3-2h-1c-1 0-1 0-2 1h-2v-10z" class="H"></path><path d="M499 528c2 1 3 3 5 4v14l-1 49c0 3-1 12 1 14 0 0 1 0 2 1h2v1 2c-2 0-4-1-5-1 0 1-1 1 0 1 1 1 1 1 1 2l-1 1h-1 0v1c1 0 1 0 1 1h-1l-2 1v-8-8-34c0-12 1-25-1-37v-4z" class="n"></path><path d="M540 613h2v3l-2 1v1h2v5 1l-2 1h1l-1 1v5h-1v-1c-1-1-2-1-4-1-2 2-4 1-7 2-2 0-6 0-9-1-2 1-4 0-6-1h-2s-1 0-2-1l2-1c-1 0-1 0-1-1v-1h1l-1-1 1-2h-4c-1-1-3-2-3-3h0 6 1 0l2 2-1 1h3c2-2 6-1 8-2l1-1v-1l2-1c1-1 1-1 3-1l8-1c1 0 2-1 3-2z" class="E"></path><path d="M524 619c5 0 11-1 16 0v1 1c-2 0-5 1-7 1-6 1-12 0-18 0 2-2 6-1 8-2l1-1z" class="k"></path><path d="M540 613h2v3l-2 1v1h2v5l-2-2v-1-1c-5-1-11 0-16 0v-1l2-1c1-1 1-1 3-1l8-1c1 0 2-1 3-2z" class="C"></path><path d="M514 627v-2l1-1h2c1 0 2 0 3 1l1 1h3l1-1h5 8 2 1l-1 1v5h-1v-1c-1-1-2-1-4-1-2 2-4 1-7 2-2 0-6 0-9-1-2 1-4 0-6-1 0-1 0-1 1-2z" class="d"></path><path d="M514 627h11c-2 1-3 2-5 2-1 0-1 0-1 1h0c-2 1-4 0-6-1 0-1 0-1 1-2z" class="S"></path><path d="M532 627c1-1 6-1 8-1v5h-1v-1c-1-1-2-1-4-1-2 2-4 1-7 2-2 0-6 0-9-1h0c0-1 0-1 1-1 2 0 3-1 5-2h7z" class="s"></path><path d="M532 627c1-1 6-1 8-1v5h-1v-1c-1-1-2-1-4-1h-5l-1-1c1 0 2 0 2-1h1z" class="B"></path><path d="M525 551c2 2 2 3 3 5v8l-1 25v2h-1v1c1 1 1 1 1 2v2l-1-1c-1 3-1 6-1 9v1c-3 0-6 0-8-1h1v-4c-1-2-1-4 0-7 0-1 0-1-1-2l1-1h1l-1-1 1-1 1-1 1 1c1-1 1-1 1-2l-2-1h-2-1c0-7 1-13 3-19 1-3 1-7-1-10 0-1-1-2-1-3 2-2 4-1 6-1l1-1z" class="S"></path><path d="M525 551c2 2 2 3 3 5v8c-1 2-1 4-1 6v1c0 1 0 3-1 4v13l-2 2c-1-1-1-1-1-2 0 0 1 0 1-1v-3c1-2 0-3 0-4 1-1 1-3 1-5 1-1 0-2 0-3l1-1v-16-2l-2-1 1-1z" class="E"></path><path d="M524 590l2-2v-13c1-1 1-3 1-4v-1c0-2 0-4 1-6l-1 25v2h-1v1c1 1 1 1 1 2v2l-1-1c-1 3-1 6-1 9v1c-3 0-6 0-8-1h1c1 0 1-1 2-1v-3c0-2 0-6-1-8h0l1-1 1 1 1-1v-1h2z" class="Z"></path><path d="M518 604c1 0 1-1 2-1v-3c0-2 0-6-1-8h0l1-1 1 1c1 1 1 1 3 1h1c1 1 0 3 0 5-1 0-2 1-3 1l-1 1 4 4v1c-3 0-6 0-8-1h1zm2-38c1-3 1-7-1-10 0-1-1-2-1-3 2-2 4-1 6-1l2 1v2c0 1-1 3-1 4-1 2-1 6-1 8l-3 12v4h-1-2c0-1 0-3 1-5 0-2 1-3 1-5s1-5 0-7z" class="o"></path><path d="M491 496c2 3 3 5 4 8 1 2 1 6 1 9v-6c1-1 1-3 1-5h-1l1-1h1 1c0-2 0-1-1-2l3-2-1 3-1 6 1 13c0 2-1 5 0 7l-1 2v4c2 12 1 25 1 37v34 8 8 34l-5 23h0-1s0-1-1-1l-1 1v-1-1c0-1-1-1-1-2s1-1 1-2h-2l2-2v-1h-2c1-1 1-2 1-3-1-5 0-11-1-16v-7-10-16c0-8 1-16 0-24l-1-15v-21l2-49 1-1 1-1-2-1v-4l-1-1 1-2z" class="I"></path><path d="M498 499l3-2-1 3-1 6 1 13c0 2-1 5 0 7l-1 2v4l-1 3v7 15c-1-2-1-4 0-6-1 2-1 6-1 9v-16c0-1 0-6-1-7v-24-6c1-1 1-3 1-5h-1l1-1h1 1c0-2 0-1-1-2z" class="E"></path><path d="M498 499l3-2-1 3-1 6c-1 1 1 3 0 4h-1v-5c0-1-1-2-1-3h-1l1-1h1 1c0-2 0-1-1-2z" class="B"></path><path d="M500 611v8 34l-5 23h0-1s0-1-1-1l-1 1v-1-1c1-2 2-3 2-5 0-4 1-7 1-11 1-10-1-21 1-31v7c0 1 1 2 1 3 0 2-1 4 0 6l1-1 1-1v-6l-1 2h0c0-3-1-8 0-10 1-1 1-2 1-2v-2h0v-3c0-3 0-6 1-9z" class="D"></path><path d="M496 562v65c-2 10 0 21-1 31 0 4-1 7-1 11 0 2-1 3-2 5 0-1-1-1-1-2s1-1 1-2h-2l2-2v-1h-2c1-1 1-2 1-3-1-5 0-11-1-16v-7-10-16l1-2v-6l1 6c1-1 0-2 0-3v-1h1v-4c1-2 1-5 1-8l1-14c0-4-1-19 1-21z" class="O"></path><path d="M490 648l4 1c0 5 1 11-1 16v3l1 1c0 2-1 3-2 5 0-1-1-1-1-2s1-1 1-2h-2l2-2v-1h-2c1-1 1-2 1-3-1-5 0-11-1-16z" class="N"></path><defs><linearGradient id="AF" x1="487.086" y1="614.39" x2="495.951" y2="645.741" xlink:href="#B"><stop offset="0" stop-color="#878687"></stop><stop offset="1" stop-color="#9f9f9c"></stop></linearGradient></defs><path fill="url(#AF)" d="M490 615l1-2v-6l1 6c1-1 0-2 0-3v-1h1v-4 21 2c0 3 0 6 1 9v12l-4-1v-7-10-16z"></path><defs><linearGradient id="AG" x1="518.65" y1="540.688" x2="466.692" y2="576.08" xlink:href="#B"><stop offset="0" stop-color="#6e6c6f"></stop><stop offset="1" stop-color="#898a85"></stop></linearGradient></defs><path fill="url(#AG)" d="M491 496c2 3 3 5 4 8 1 2 1 6 1 9v24 16 9c-2 2-1 17-1 21l-1 14c0 3 0 6-1 8v4h-1v1c0 1 1 2 0 3l-1-6v6l-1 2c0-8 1-16 0-24l-1-15v-21l2-49 1-1 1-1-2-1v-4l-1-1 1-2z"></path><path d="M493 569h1c0 3 0 6-1 9-1 1-1 2-1 4h0-1c-1-4 2-9 2-13z" class="i"></path><path d="M491 496c2 3 3 5 4 8h-1c-1 2-1 10-2 10 0-2 1-7 0-9l1-1-2-1v-4l-1-1 1-2z" class="O"></path><path d="M500 619l2-1v1 2c1 1 3 1 5 1h4l-1 2 1 1h-1v1c0 1 0 1 1 1l-2 1c1 1 2 1 2 1h2c2 1 4 2 6 1 3 1 7 1 9 1 3-1 5 0 7-2 2 0 3 0 4 1v1h1l2-1v2l-1 1c0 1 1 1 2 1 0 1 0 3-1 4 0 2 0 2 1 3v1l1 2h-1v5 1c0 1 1 2 1 3-2 0-3 1-5 1 1 1 2 1 2 1v1l1 1 1 1h0v2c-1 1-2 1-4 2h1c1 1 1 0 2 1v1l2 1v1c0 2 0 3-1 5 1 1 2 0 2 3 0 1-1 2-1 3v3l1 2c-1 0-1 1-1 2-1 1-1 0-1 2l2 1v4c-1 3 0 8 0 12l-3-3 1-1c-4 0-8 0-12 1h-5c-2-1-4-1-5-3h-1v-1h0-2v-1c1 0 2 0 3-1h-6-2-10-9-1c-1 0-1 1-2 1s-1-1-2-1l1-3v-2-4l1-1c-1-3 0-7 1-9v1l1-1c1 0 1 1 1 1h1 0l5-23v-34z" class="N"></path><path d="M526 666l11-1c2 0 3-1 5 0v1c0 1 0 1 1 2h-2c-1 0-2 1-3 2h-13-1-2c-1 0-2-1-2-2 2-2 3-2 6-2z" class="Z"></path><path d="M534 666h0c2 0 3 1 4 2h-1-2-5l1-1c1-1 2 0 3-1z" class="s"></path><path d="M520 674c8-1 16-2 23-2 1 1 1 1 0 2-1 2-4 2-6 3h-3-11-6 0c-2 0-3-1-3-2h5l1-1z" class="Z"></path><path d="M512 664h1l1 2h6 6c-3 0-4 0-6 2 0 1 1 2 2 2h2 1c-3 1-7 0-10 1h6c0 1 0 1-1 1v2l-1 1h-5c0 1 1 2 3 2h0 6c-4 1-7 1-11 1h-1-7c0-2-1-2-1-3l-2-1c-2 1-2 1-3 1v-3h-1c0-1 0-2 1-3h2 7c2 0 4 0 6-1-1 0-1-1-2-1l1-3h0z" class="B"></path><path d="M510 671h2v3l-8 1h-1l-2-1c2 0 6 1 8 0 1-1 1-3 1-3z" class="M"></path><path d="M503 675h1l1 1h8v1h-2v1h-7c0-2-1-2-1-3z" class="R"></path><path d="M515 671h6c0 1 0 1-1 1v2c-2 0-5 1-7 0v-3h2z" class="Q"></path><path d="M498 672h-1c0-1 0-2 1-3h2l1 1 9 1s0 2-1 3c-2 1-6 0-8 0-2 1-2 1-3 1v-3z" class="X"></path><defs><linearGradient id="AH" x1="508.293" y1="688.516" x2="535.896" y2="675.898" xlink:href="#B"><stop offset="0" stop-color="#b8b7b7"></stop><stop offset="1" stop-color="#f1f0ee"></stop></linearGradient></defs><path fill="url(#AH)" d="M523 677h11 3 3v1h0l-2 1 1 1 5-1v1l1 2c-2 0-3 2-5 2l-12 1c-3 0-7 0-10 1h-3-2c-2 0-4 0-5-1h-1-1c-1-1 0-1-1-1h-1l-1-1 1-1h3c1-1 3 1 5-1v-2-1c4 0 7 0 11-1z"></path><defs><linearGradient id="AI" x1="525.121" y1="682.338" x2="527.722" y2="675.798" xlink:href="#B"><stop offset="0" stop-color="#8e8e8c"></stop><stop offset="1" stop-color="#b5b3b3"></stop></linearGradient></defs><path fill="url(#AI)" d="M523 677h11 3 3v1h0l-2 1 1 1 5-1v1l1 2c-2 0-3 2-5 2h0c1-2 1-3 0-4-6 0-14 2-20 2h-6l-1-1c0-1 0-1-1-2v-1c4 0 7 0 11-1z"></path><path d="M513 681c2-1 4-1 6-1 0 1 0 1 1 2h-6l-1-1z" class="n"></path><path d="M498 672v3c1 0 1 0 3-1l2 1c0 1 1 1 1 3h7 1v1 2c-2 2-4 0-5 1h-3l-1 1 1 1h1c1 0 0 0 1 1h1 1c1 1 3 1 5 1h2c-2 1-2 1-4 1v1l1 1-5 1h-5l-1 2v1l2 1h-9-1c-1 0-1 1-2 1s-1-1-2-1l1-3v-2-4l1-1c-1-3 0-7 1-9v1l1-1c1 0 1 1 1 1h1 0 1l2-4z" class="X"></path><path d="M490 689c2-1 9-1 11 0l1 1c3-3 6-1 9-2l1 1-5 1h-5l-1 2v1l2 1h-9-1c-1 0-1 1-2 1s-1-1-2-1l1-3v-2z" class="L"></path><path d="M494 694c1-1 1-1 1-2h1s1-1 1-2h5l-1 2v1l2 1h-9zm4-22v3c1 0 1 0 3-1l2 1c0 1 1 1 1 3h7 1v1 2c-2 2-4 0-5 1h-3l-1 1 1 1h1c1 0 0 0 1 1h1 1c1 1 3 1 5 1h-9c-1 0-1-1-2 0h-1c0-1-1-1-2-2h-5c0-1-1-1-1-2 0-2 0-4 1-6h1 0 1l2-4z" class="J"></path><path d="M504 678h7 1v1 2h-10v-3h2z" class="a"></path><path d="M498 672v3c1 0 1 0 3-1l2 1c0 1 1 1 1 3h-2v3h-7c-1-2 0-3 0-5h0 1l2-4z" class="Q"></path><path d="M498 672v3c1 0 1 0 3-1l2 1c0 1 1 1 1 3h-2-6v-2l2-4z" class="D"></path><path d="M545 682c-1 0-1 1-1 2-1 1-1 0-1 2l2 1v4c-1 3 0 8 0 12l-3-3 1-1c-4 0-8 0-12 1h-5c-2-1-4-1-5-3h-1v-1h0-2v-1c1 0 2 0 3-1h-6-2-10l-2-1v-1l1-2h5l5-1-1-1v-1c2 0 2 0 4-1h3c3-1 7-1 10-1l12-1c2 0 3-2 5-2z" class="Y"></path><path d="M514 692h2l5 1-6 1h-2l1-2z" class="C"></path><path d="M521 693h5 0 5c0 1-1 1-1 2h0c-3 0-7-1-9-1h-6l6-1z" class="D"></path><path d="M502 690h5c-1 0-2 1-4 1v1c1 0 1 0 2-1v1c2 0 7 1 8 0h1l-1 2h-10l-2-1v-1l1-2zm41-4l2 1v4c-1 3 0 8 0 12l-3-3 1-1c1-1 0-1 1-2l-1-1c-3-2-8 0-12 0l-1-1h0c0-1 1-1 1-2h-5 0c0-1 1-1 1-1 2 1 6 0 8 0s4 0 6-1c1-1 2-2 3-4h-2l1-1z" class="I"></path><path d="M521 694c2 0 6 1 9 1l1 1c4 0 9-2 12 0l1 1c-1 1 0 1-1 2-4 0-8 0-12 1h-5c-2-1-4-1-5-3h-1v-1h0-2v-1c1 0 2 0 3-1z" class="s"></path><path d="M521 694c2 0 6 1 9 1l1 1-10 1h-1v-1h0-2v-1c1 0 2 0 3-1z" class="h"></path><path d="M545 682c-1 0-1 1-1 2-1 1-1 0-1 2l-1 1h2c-1 2-2 3-3 4-2 1-4 1-6 1-3-1-8 0-11-1-1-1-1-1-1-2h-7c-2 1-2 1-4 0l-1-1v-1c2 0 2 0 4-1h3c3-1 7-1 10-1l12-1c2 0 3-2 5-2z" class="N"></path><path d="M515 686h3c2 0 5 0 7 1 2 0 5-1 7 1l-9 1h-7c-2 1-2 1-4 0l-1-1v-1c2 0 2 0 4-1z" class="j"></path><path d="M542 687h2c-1 2-2 3-3 4-2 1-4 1-6 1-3-1-8 0-11-1-1-1-1-1-1-2l9-1 10-1z" class="o"></path><path d="M500 619l2-1v1 2c1 1 3 1 5 1h4l-1 2 1 1h-1v1c0 1 0 1 1 1l-2 1c1 1 2 1 2 1h2c2 1 4 2 6 1 3 1 7 1 9 1 3-1 5 0 7-2 2 0 3 0 4 1v1h1l2-1v2l-1 1c0 1 1 1 2 1 0 1 0 3-1 4 0 2 0 2 1 3v1l1 2h-1v5 1c0 1 1 2 1 3-2 0-3 1-5 1 1 1 2 1 2 1v1l1 1 1 1h0v2c-1 1-2 1-4 2h-7c-2 0-4 0-5 1h0-1c-2 1-4 1-5 1v1c-1 1 0 1-1 1h-6l-1-2h-1 0l-1 3c1 0 1 1 2 1-2 1-4 1-6 1h-7-2c-1 1-1 2-1 3h1l-2 4h-1l5-23v-34z" class="I"></path><path d="M511 643h1c1-1 1-2 1-2l1-1-1 2c3-2 6-1 9-1l2 1c4 1 9-1 13-1h4v1l2-1v1l-2 2 1 2h-1-1-3-16-6l-4-1c-2-1-2-1-3-2h3z" class="S"></path><path d="M537 646c-2-2-7-1-10-1 2-2 11-1 14-1l1 2h-1-1-3z" class="H"></path><path d="M513 642c3-2 6-1 9-1l2 1h-1c-2 1-6 1-8 1l-2-1z" class="Q"></path><path d="M500 619l2-1v1 2c1 1 3 1 5 1h4l-1 2 1 1h-1v1c0 1 0 1 1 1l-2 1c1 1 2 1 2 1v1h-3v1h2l1 1v3c1 1 2 2 3 2-1 0-2 1-3 0h-4-1c-1 0-3 0-5 1 0 1 0 0 1 2h-1c1 1 4 1 4 1 2 0 4 0 6 1v1h-3-6l-1 1v1c2 1 3 1 5 2h-3v1h7c-2 2-7 0-9 2v1c2 1 4 1 6 2h-5-1-1v-34z" class="V"></path><path d="M508 630v-1h-2c-2 0-3-1-4-1v-3h8v1c0 1 0 1 1 1l-2 1c1 1 2 1 2 1v1h-3z" class="C"></path><path d="M506 637v-1c-2 0-3-1-5-1v-2l2-2c2 1 5 1 8 1h0v3c1 1 2 2 3 2-1 0-2 1-3 0h-4-1z" class="N"></path><path d="M511 632h0v3c1 1 2 2 3 2-1 0-2 1-3 0l-1-1c-1 0-2 0-3-1v-2c1 0 2 0 3-1h1z" class="D"></path><path d="M535 629c2 0 3 0 4 1v1h1l2-1v2l-1 1c0 1 1 1 2 1 0 1 0 3-1 4 0 2 0 2 1 3l-2 1v-1h-4c-4 0-9 2-13 1l-2-1c-3 0-6-1-9 1l1-2-1 1s0 1-1 2h-1v-1c-2-1-4-1-6-1 0 0-3 0-4-1h1c-1-2-1-1-1-2 2-1 4-1 5-1h1 4c1 1 2 0 3 0-1 0-2-1-3-2v-3l-1-1h-2v-1h3v-1h2c2 1 4 2 6 1 3 1 7 1 9 1 3-1 5 0 7-2z" class="B"></path><path d="M514 633h8l1 2c-2 0-7 1-9-1v-1z" class="V"></path><path d="M511 629h2c2 1 4 2 6 1 3 1 7 1 9 1l2 1c-2 1-5 1-8 1h-8v-2h-1c-1 0-1-1-2-1v-1z" class="R"></path><path d="M535 629c2 0 3 0 4 1v1h1l2-1v2l-1 1c0 1 1 1 2 1 0 1 0 3-1 4 0-1 1-3 0-4h-1l-17 1v-1l-1 1-1-2c3 0 6 0 8-1l-2-1c3-1 5 0 7-2z" class="C"></path><path d="M535 629c2 0 3 0 4 1v1c-3 0-6 1-9 1l-2-1c3-1 5 0 7-2z" class="Z"></path><path d="M511 643v-1c-2-1-4-1-6-1 0 0-3 0-4-1h1c-1-2-1-1-1-2 2-1 4-1 5-1h1c2 1 3 2 5 2h3l1 1h2 7 7l1-1c1-1 7-1 9-1 0 2 0 2 1 3l-2 1v-1h-4c-4 0-9 2-13 1l-2-1c-3 0-6-1-9 1l1-2-1 1s0 1-1 2h-1z" class="C"></path><path d="M507 653h1c2 1 4 1 6 0 0-1-2-1-3-2 0-2 1-2 2-3l-1-1c-1-1-1-1-2-1h1c1 0 2 0 4 1l-1 2 1 1c1-1 22 0 26-1h2v1c0 1 1 2 1 3-2 0-3 1-5 1 1 1 2 1 2 1v1l1 1 1 1h0v2c-1 1-2 1-4 2h-7c-2 0-4 0-5 1h0-1c-2 1-4 1-5 1v1c-1 1 0 1-1 1h-6l-1-2h-1 0l-1 3c1 0 1 1 2 1-2 1-4 1-6 1h-7-2c-1 1-1 2-1 3h1l-2 4h-1l5-23h1 1 5z" class="B"></path><path d="M514 656c2-1 6 0 8 0l1 2h0c-2 1-7 1-9 0v-2z" class="b"></path><path d="M519 654l1-2h0c2 0 2 0 4-1 1-1 3-1 4-1 1 1 1 1 2 1 3-1 8 0 11 1-4 0-9 0-13 1v1h-9z" class="Z"></path><path d="M505 661c3-1 7 0 10 0h5 2 4c3 1 4 1 6 1-2 0-4 0-5 1h0-1c-2 1-4 1-5 1v1c-1 1 0 1-1 1h-6l-1-2h-1c-2-2-7 0-9-2h1l1-1z" class="E"></path><path d="M513 664l1-1c3-1 9 0 13 0h-1c-2 1-4 1-5 1v1c-1 1 0 1-1 1h-6l-1-2z" class="F"></path><path d="M495 676l5-23h1 1 0c1 1 2 2 3 2 2 0 3 1 5 1h1l1 2c-2 1-7 0-9 0l-2 2c1 0 2 1 4 1l-1 1h-1c2 2 7 0 9 2h0l-1 3c1 0 1 1 2 1-2 1-4 1-6 1h-7-2c-1 1-1 2-1 3h1l-2 4h-1z" class="e"></path><path d="M507 669v-1h-6 0l-1-1c0-1 0-1 1-2h10l1-1-1 3c1 0 1 1 2 1-2 1-4 1-6 1z" class="C"></path><path d="M543 650c0 1 1 2 1 3-2 0-3 1-5 1 1 1 2 1 2 1v1l1 1 1 1h0v2c-1 1-2 1-4 2h-7c-2 0-3 0-6-1h-4l2-1c1-1 1-1 2-1v-1h-3l-1-2c-2 0-6-1-8 0v-1h2l3-1h9 11l1-1v-1c2 0 2-1 3-2z" class="J"></path><path d="M542 657l1 1h0v2c-1 1-2 1-4 2h-7c-2 0-3 0-6-1h-4l2-1c1-1 1-1 2-1v-1l10-1h6z" class="S"></path><path d="M526 658l10-1v1c1 0 2 0 3 1v1c-3 1-9-1-13 1h-4l2-1c1-1 1-1 2-1v-1z" class="s"></path><path d="M465 470c3 0 6 4 8 6v1l2 2 4 6v1c2 0 3 0 5-1 2 0 4 0 5-1h0c4-1 10 0 14-1h4 2v-2l2-2 1 3 2 1c6 1 11 4 15 9l1 1v1c2 2 3 5 4 8 0 2 0 4 1 6l1-2v8c1 1 1 1 3 1l1-2c0 1 1 1 1 1h1v1h-2v1c3 3 0 11 1 15 1 1 0 4 0 5 0 8-1 17 0 25 0 3-1 8 0 11v11l2 2v3l-1 1-1-1c0 5 1 12 0 18l-2 1h0l2 1c0 1 0 1-1 2v1h-9-1c1 0 2-1 3-2h6c-4-2-11-1-15-1 2 0 3 0 4-1l-1-1-9 1c-1-2-1-2-2-2v-1h1c2 1 5 1 8 1v-1c0-3 0-6 1-9l1 1v-2c0-1 0-1-1-2v-1h1v-2l1-25v-8c-1-2-1-3-3-5-1-1-1-1-3-1-1 0-2 1-3 1-2 1-2 1-4 1v-2c1-3 1-5 1-8v-3c1-1 3-3 3-4v-2c-1 1-3 1-4 1h-1-2v-1h-1l-1 1h-1v-1c-2 0-4 0-5-1-2-1-3-3-5-4l1-2c-1-2 0-5 0-7l-1-13 1-6 1-3-3 2c1 1 1 0 1 2h-1-1l-1 1h1c0 2 0 4-1 5v6c0-3 0-7-1-9-1-3-2-5-4-8h0l-2-3-3-3h-7l-1-1c-1 0-2 0-3-1h-1c-1 0-2-1-2-2h0l2 1v-1l-6-5 1-1 1-1-1-1c-1-3-3-5-5-7l1-1h0z" class="G"></path><path d="M529 535v-18c0-4 0-10-1-13v-2l1 3 1 1h0c2 6 2 15 1 22-2 2 0 4-2 7z" class="E"></path><path d="M509 500c-1-1-1-2 0-3 2-1 4-2 6-2 1 0 0 0 1-1l2 1c1-1 2-1 3 0 3 1 5 5 5 7l1 1c-1 0-2-1-3-1l-3-5c-1 1-1 3-1 4-1 0-1-1-2-1-1-1-3-1-4 0 0 0-2 1-3 1s-1-1-2-1z" class="g"></path><path d="M530 559c1 3 1 8 0 11s0 8 0 11h1v-5l1-2c0 1 1 1 0 2v14s-2 1-2 2v3 7c1 2 2 2 3 2l1 1 1 1c-2 1-3 0-5 0-1 0-1 0-2 1l-1-1-9 1c-1-2-1-2-2-2v-1h1c2 1 5 1 8 1v-1c0-3 0-6 1-9l1 1v-2c0-1 0-1-1-2v-1h1v-2c2-2 1-8 1-10 0-3 1-5 1-7 0-4 0-9 1-13z" class="H"></path><path d="M527 594l1 3v4 5h-1l-9 1c-1-2-1-2-2-2v-1h1c2 1 5 1 8 1v-1c0-3 0-6 1-9l1 1v-2z" class="B"></path><path d="M520 501c0-1 0-3 1-4l3 5c1 0 2 1 3 1 2 7 1 16 1 23v30c-1-2-1-3-3-5-1-1-1-1-3-1-1 0-2 1-3 1-2 1-2 1-4 1v-2c1-3 1-5 1-8v-3c1-1 3-3 3-4v-2h1c0-2 0-2-1-4h0 1l1-1c1-2 1-3 2-4v-1-3-1c1-2 1-4 0-6 0-3 0-7-2-9-1 0-1 0-1-1h0v-1-1z" class="s"></path><path d="M520 501c0-1 0-3 1-4l3 5c1 0 2 1 3 1 2 7 1 16 1 23-1-2-1-4-1-6 0-3 1-6 0-8-1-1-1-1-1-2l-1-6c-1-1-1-1-3-2 1 2 1 1 0 2-1 0 0 0-1-1h-1v-1-1z" class="H"></path><path d="M527 520c0 2 0 4 1 6v30c-1-2-1-3-3-5-1-1-1-1-3-1h3l1 1 1-1c-2-5 0-9 0-13 0-2-1-4-1-5 0-5 0-8 1-12z" class="T"></path><path d="M509 481l2-2 1 3 2 1c6 1 11 4 15 9l1 1v1c2 2 3 5 4 8 0 2 0 4 1 6l-1 54c-1 2-1 5 0 7l-1 1h-1c-1 1-1 2-1 4h1l-1 2v5h-1c0-3-1-8 0-11s1-8 0-11v-2c-1-2-1-5-1-7v-15c2-3 0-5 2-7 1-7 1-16-1-22 0-4-2-8-5-12-1-1-4-4-6-4l-3-1 1-1h1 1v1c1 0 2 0 3 1h0 1 0c-2-3-5-3-8-4-4-3-8-1-12-3h4 2v-2z" class="Z"></path><path d="M531 528v20c0 3 0 6-1 9-1-2-1-5-1-7v-15c2-3 0-5 2-7z" class="C"></path><path d="M465 470c3 0 6 4 8 6v1l2 2 4 6v1c2 0 3 0 5-1 2 0 4 0 5-1h0c4-1 10 0 14-1 4 2 8 0 12 3 3 1 6 1 8 4h0-1 0c-1-1-2-1-3-1v-1h-1-1l-1 1 3 1-1 1c-1 0-1 0-2-1-1 1-1 1-2 1l-1-1c-1 1-1 0-1 1l-2-1c-2 1-3 3-4 5l-2 3-1-1-1 1 2 1c-2 0-2 0-4 1l1-3-3 2c1 1 1 0 1 2h-1-1l-1 1h1c0 2 0 4-1 5v6c0-3 0-7-1-9-1-3-2-5-4-8h0l-2-3-3-3h-7l-1-1c-1 0-2 0-3-1h-1c-1 0-2-1-2-2h0l2 1v-1l-6-5 1-1 1-1-1-1c-1-3-3-5-5-7l1-1h0z" class="Y"></path><path d="M465 470h0c2 2 5 4 6 6v1c2 2 5 7 4 9h-1l-6-5 1-1 1-1-1-1c-1-3-3-5-5-7l1-1z" class="s"></path><path d="M475 488l15-1h10v2h-3c-2 1-3 1-4 2s-3 2-4 2l-3-3h-7l-1-1c-1 0-2 0-3-1z" class="e"></path><path d="M486 490c3 0 8-1 11-1-2 1-3 1-4 2s-3 2-4 2l-3-3z" class="M"></path><path d="M489 484c4-1 10 0 14-1 4 2 8 0 12 3h-6l1 1c-3 1-6 2-9 2h-1v-2h-10v-1c-1-1-3 0-6-1 2 0 4 0 5-1h0z" class="B"></path><path d="M490 486h19l1 1c-3 1-6 2-9 2h-1v-2h-10v-1z" class="i"></path><path d="M515 486c3 1 6 1 8 4h0-1 0c-1-1-2-1-3-1v-1h-1-1l-1 1 3 1-1 1c-1 0-1 0-2-1-1 1-1 1-2 1l-1-1c-1 1-1 0-1 1l-2-1c-2 1-3 3-4 5l-2 3-1-1-1 1 2 1c-2 0-2 0-4 1l1-3-3 2c1 1 1 0 1 2h-1-1l-1 1h1c0 2 0 4-1 5v6c0-3 0-7-1-9-1-3-2-5-4-8h0l-2-3c1 0 3-1 4-2s2-1 4-2h3 1c3 0 6-1 9-2l-1-1h6z" class="R"></path><path d="M498 499h0-1v-3c1-1 1-1 3-2 1 0 1-1 2-1 0-1 0-2 1-2l1-1c-1 3-2 5-3 7h0l-3 2z" class="T"></path><path d="M500 489h1l-1 1h0c0 1 1 2 1 2-3 2-9 1-10 4l-2-3c1 0 3-1 4-2s2-1 4-2h3z" class="G"></path><path d="M504 490v1c4-3 7-3 12-2l3 1-1 1c-1 0-1 0-2-1-1 1-1 1-2 1l-1-1c-1 1-1 0-1 1l-2-1c-2 1-3 3-4 5l-2 3-1-1-1 1 2 1c-2 0-2 0-4 1l1-3h0c1-2 2-4 3-7z" class="O"></path><path d="M506 495c0 1-1 1 0 2 0 1 0 1-1 2l1 1h0c-1 2-1 3-1 5 1-2 1-3 3-4h0l1-1c1 0 1 1 2 1s3-1 3-1c1-1 3-1 4 0 1 0 1 1 2 1v1 1h0c0 1 0 1 1 1 2 2 2 6 2 9 1 2 1 4 0 6v1 3 1c-1 1-1 2-2 4l-1 1h-1 0c1 2 1 2 1 4h-1c-1 1-3 1-4 1h-1-2v-1h-1l-1 1h-1v-1c-2 0-4 0-5-1-2-1-3-3-5-4l1-2c-1-2 0-5 0-7l-1-13 1-6c2-1 2-1 4-1l-2-1 1-1 1 1 2-3z" class="T"></path><path d="M521 509v4c0 1 0 2 1 3v3l-1 2c0 1 1 2 2 3-1 1-1 2-2 4l-1 1h-1 0-3c0-2 1-2 2-3 1 0 1 0 2-1v-1h-1c1-2 1-3 1-5v-5l1-5z" class="B"></path><path d="M510 520c1-3 0-8 1-12 0-1 1-2 3-3 1-1 2-1 4 0s2 2 3 4l-1 5h-1c0-2 0-3-1-5h0c-2-1-2-1-3-2l-3 2c0 4 1 9 0 13v3h0l-1 1v-1l-1-5z" class="L"></path><path d="M520 519c0 2 0 3-1 5h1v1c-1 1-1 1-2 1-1 1-2 1-2 3h3c1 2 1 2 1 4h-1v-1h-5l-1-1h-1c-1-1-3-1-4-1v-1h2c0-1 0-1-1-1l-1-2h2v-6l1 5v1l1-1c1 1 4 1 5 0 2-1 3-4 3-6z" class="Y"></path><path d="M508 520v6l1 2c1 0 1 0 1 1h-2v1c1 0 3 0 4 1h1l1 1h5v1c-1 1-3 1-4 1h-1-2v-1h-1l-1 1h-1v-1c-2 0-4 0-5-1-2-1-3-3-5-4l1-2 3 1h1c1 1 2 1 4 1v-8z" class="J"></path><defs><linearGradient id="AJ" x1="510.678" y1="517.142" x2="502.229" y2="513.821" xlink:href="#B"><stop offset="0" stop-color="#7a7d76"></stop><stop offset="1" stop-color="#918d8f"></stop></linearGradient></defs><path fill="url(#AJ)" d="M509 500c1 0 1 1 2 1s3-1 3-1c1-1 3-1 4 0 1 0 1 1 2 1v1c-2-1-4-2-6-1-2 0-4 3-5 4-2 4-1 11-1 15v8c-2 0-3 0-4-1 1-1 1-2 1-4v-8c0-3-1-8 0-10s1-3 3-4h0l1-1z"></path><path d="M512 522c1-4 0-9 0-13l3-2c1 1 1 1 3 2h0c1 2 1 3 1 5h1v5c0 2-1 5-3 6-1 1-4 1-5 0h0v-3z" class="d"></path><path d="M512 522c1-4 0-9 0-13l3-2c1 1 1 1 3 2 0 1 0 2-1 2 0 1-1 1-1 2 0 0 2 0 1 1s-2 1-4 1v6h1l1-1-1-1 1-1 1 2h0c-1 1-1 1-1 2h-3z" class="R"></path><path d="M506 495c0 1-1 1 0 2 0 1 0 1-1 2l1 1h0c-1 2-1 3-1 5-1 2 0 7 0 10v8c0 2 0 3-1 4h-1l-3-1c-1-2 0-5 0-7l-1-13 1-6c2-1 2-1 4-1l-2-1 1-1 1 1 2-3z" class="V"></path><path d="M506 495c0 1-1 1 0 2 0 1 0 1-1 2l1 1h0c-1 2-1 3-1 5-1 2 0 7 0 10v8c0 2 0 3-1 4h-1v-16c0-2 0-5 1-7v-2c-1-1 0-2 0-3l-2-1 1-1 1 1 2-3z" class="P"></path><path d="M536 506v8c1 1 1 1 3 1l1-2c0 1 1 1 1 1h1v1h-2v1c3 3 0 11 1 15 1 1 0 4 0 5 0 8-1 17 0 25 0 3-1 8 0 11v11l2 2v3l-1 1-1-1c0 5 1 12 0 18l-2 1h0l2 1c0 1 0 1-1 2v1h-9-1c1 0 2-1 3-2h6c-4-2-11-1-15-1 2 0 3 0 4-1s1-1 2-1c2 0 3 1 5 0l-1-1-1-1c-1 0-2 0-3-2v-7-3c0-1 2-2 2-2v-14c1-1 0-1 0-2h-1c0-2 0-3 1-4h1l1-1c-1-2-1-5 0-7l1-54 1-2z" class="Y"></path><path d="M541 583l2 2v3l-1 1-1-1v-5z" class="K"></path><path d="M536 506v8c1 1 1 1 3 1l1-2c0 1 1 1 1 1h1v1h-2v1c-1 0-2 0-4 1h0l1 1v11 30c0 11 1 25-1 36 0 3 1 8-1 9-1-1 1-7 0-8h-2v-2c-1-1-1-3-1-4v-14c1-1 0-1 0-2h-1c0-2 0-3 1-4h1l1-1c-1-2-1-5 0-7l1-54 1-2z" class="d"></path><path d="M532 574h-1c0-2 0-3 1-4h1l1-1c-1-2-1-5 0-7l1 34h-2v-2c-1-1-1-3-1-4v-14c1-1 0-1 0-2z" class="S"></path><path d="M195 184c2 0 4 1 6 1 3 1 6 0 9 0l23 2 1-1 17 6c9 3 20 8 29 14h0c0 1 0 1 1 2h-1l12 9c1 0 2 2 3 2 0-1 1-1 0-2 5 4 10 8 14 13l4 4c1 0 1 0 2 1 2 1 4 7 7 7h1l1 1 2 11 1-1v-5l6 16c3 8 8 16 10 24h1 0 1l3-3c1-3 2-5 3-8 1-2 2-5 3-8-1-1 0-3 0-4h0v-2c1-3 0-5 2-8 1 1 0 1 0 3h1v-6-3l1 1v-2-6c0-1 0-3 1-5h13v-1c2 0 4 0 6-1v3l1-1 3 7c3 8 7 16 8 25 0 3 0 5-1 7 0 5-1 8-2 12l-4 15c-2 8-6 15-7 22 5 16 12 31 19 46l7 17 6 12h0c-1 1-1 1 0 3l-3 1v4c-1 1 0 5 0 6s0 1 1 1c9 6 11 17 16 26 1 1 2 3 2 5h0v3c2 2 6 6 6 9l-2 4v1c0 1-1 2-1 4h1c0-1 1-1 1-2l1-1h-1c1-2 3-3 3-4h-1v-1c1 0 2 1 2 2l5 13c-3-1-7-2-10-3h-1-2l-1 2c-1-1-2-1-3-1l1-3c-5 0-9-1-14 0l-1-1-16 3c-2 1-4 1-6 2l-6 3-8 6-5 4-2 2-3 3-3 2c-3 4-6 7-7 12l-1 2c-3 11-3 21-3 33l-2 2c-1 1-3 2-4 4h-1c-2 2-3 3-4 6-1 2 0 4-1 6 0 1-1 2-2 3 1 2 1 5 1 7v6 6l-3 3v-10c0-2-1-4 0-6h0c0-2 0-3-1-5 0-1-2-2-2-2v-2l-2-1c-2-2-6-5-6-7-2-2-5-4-7-5s-4-2-7-2h-1v-3c0 2 0 3-1 4h-1l-1-1-1-1v1l1 1h-1l-2-2-1 1 1 1c-1 1-1 0-1 2h-2c0 1-1 2-2 2l-2 2h0c-1 1-1 2-1 3v1h-1c1 2 2 3 3 4 1 0 2 1 2 2-2-1-3-3-5-4l-2-2h-1c-2-1-3-5-5-7h-1c1 3 3 4 4 7h-1c-1-1-2-3-3-4 0-1-1-1-1-2s0-1-1-1c0-2 0-3-1-4s0-1-1-2l-1-1h-1-1l1-1v-1c-2-3-2-8-5-11-1-4-3-8-3-13-2-7-1-15-1-23-1-6 0-13 2-19v-1l2-10-1-1c2-6 5-15 8-21 0-5-3-10-5-15l-15-33-47-104-24-49-12-23c-1-2-1-4-3-5h0l8-1s0-1-1-1c-2 0-2 0-4-1h0l-1-1 1-1c3 0 5 0 7-1 2 1 4 1 5 0h1c1 1 3 1 4 1l3-1c1-1 1-3 1-4v-4l1-2h0c1-1 2-3 2-4l-2-3c-1 0-3-2-3-2 1-1 1-1 2-1z" class="p"></path><path d="M222 215c-3 1-5 1-8 2l5-5 1 1h4l-2 2z" class="C"></path><path d="M174 207h11l4 1 3 1h-13s0-1-1-1c-2 0-2 0-4-1z" class="d"></path><path d="M294 410l2-2c0 2-1 2-2 4 0 1 0 2-1 2-1 2-1 3-1 5-1 1-1 1-1 3 0 0-1-1-1-2l-1-2 5-8z" class="J"></path><path d="M194 222c2-2 5-3 8-2 2 0 5 2 6 4l1 1h0l2 4c-1 0-2-1-2-2-2-3-5-3-8-4-1 0-1-1-2-2h-2c-1 1-2 1-3 1z" class="E"></path><path d="M289 418l1 2c0 1 1 2 1 2h1v1h-1s-1 0-1 1c-2 2-4 4-6 5-1 2-2 5-4 6 2-6 6-12 9-17z" class="W"></path><defs><linearGradient id="AK" x1="269.223" y1="379.572" x2="273.198" y2="394.859" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#8d8d8d"></stop></linearGradient></defs><path fill="url(#AK)" d="M274 396h-1 0l-1-1c-4-2-5-5-6-9 0-3 1-5 3-8l3 1c-1 1-2 2-2 4-1 2-1 2-1 4 1 1 2 1 3 2 2 2 3 3 6 3l1 2c-1 1-1 1-2 1-2 0-2 1-3 1z"></path><path d="M231 277l3 1v1l-1 1c2 1 2-1 5 1l1-1c0 1 0 1-1 2h1l1 1-2 1c1 1 2 0 3 0-1 2-2 2-4 3l-2 2h1l3-1h0c-1 1-2 1-3 2 1 1 1 1 2 0 1 0 2 0 3-1h0l-1 2c2 0 4-1 5-1v2c1 0 2 0 3-1v1 1h0 0l-1-1c-1 1-2 1-3 0h-4 0l-1-1c-2 1-4 1-5 2s-2 1-3 1v-1h-1l1-1-2-1h0c1-1 2-1 2-2 1-2 2-3 4-4l2-1-1-1-1-1h-1v1h-1-1c0-2 0-3 1-4-2 1-2 1-5 1l3-3z" class="U"></path><path d="M195 200v1l1 3c1 1 1 0 3 0v-2c1 1 2 1 3 2l-1 1 1 1h4 0l3-2c0 1 0 2 1 2h4 1 1v-1h1v1 1h2c2-1 3-2 5-2h1l-1 1c-1 1-3 2-4 3h-1l1 1 2-1v1c-1 1-2 1-3 2-2-2-6-2-9-2-6-1-12-1-18-1l-3-1-4-1h-11 0l-1-1 1-1c3 0 5 0 7-1 2 1 4 1 5 0h1c1 1 3 1 4 1l3-1c1-1 1-3 1-4z" class="J"></path><path d="M195 200v1l1 3c1 1 1 0 3 0v-2c1 1 2 1 3 2l-1 1 1 1h4v1h-7v-2h-3l1 2h-6c-2-1-4 0-6 0h-11 0l-1-1 1-1c3 0 5 0 7-1 2 1 4 1 5 0h1c1 1 3 1 4 1l3-1c1-1 1-3 1-4z" class="g"></path><path d="M197 221h2c1 1 1 2 2 2 3 1 6 1 8 4 0 1 1 2 2 2 0 2 1 5 0 7l-1 1-1-1c-1 1-2 3-4 4h-1c0 1 0 1-1 1-3 1-6 0-8-2-3-1-5-3-5-7h0c-1-1-1-1-1-2 1-3 2-6 5-8 1 0 2 0 3-1z" class="i"></path><path d="M204 234v1c-2 1-3 2-5 2-2-1-4-2-5-4l-1-3 1-3c1-1 1-2 3-3l-2 2c-1 2-1 3-1 5l1-1c1-2 2-2 4-2 1 0 3 0 4 1l2 1c0 1 0 3-1 4z" class="U"></path><path d="M203 229l2 1c-1 1-2 1-2 2v1c-1 0-1 0-2 1-2 0-2 0-4-1l-1-1 2-2c1 1 1 1 2 1h1c0-1 1-1 1-2h0 1z" class="p"></path><path d="M197 221h2c1 1 1 2 2 2 3 1 6 1 8 4 0 1 1 2 2 2 0 2 1 5 0 7l-1 1-1-1c-1 1-2 3-4 4h-1c0-1 0 0 1-1s2-2 2-4l-1 1v-2h-2c1-1 1-3 1-4l-2-1c-1-1-3-1-4-1v-2h0v-1-2c-1 0-1-1-2-2z" class="a"></path><path d="M209 225l3 3 1 1c1-1 2-1 3-1 1-1 1-1 2-1v1h3c-1 1-1 2-2 2l-1 1s-1 1-2 1h-1s-1 0-1 1c-2 1-1 4-1 6v1l1-1h1l-1 1 1 1c1-1 2-1 3-1s1-1 2-2h0c4-1 7-4 11-5h0 0c-5 3-10 7-15 8v1c2 0 3-1 4-1 4-2 7-4 11-6l-8 7c3-2 6-4 10-4l-3 2h0c2 0 4-1 6-2h2c-3 1-5 2-7 4 3-1 7-4 10-3-3 1-6 2-8 5l7-3h3l-6 3h2c1 0 1-1 2-1 2-1 5-2 7-3-3 2-5 4-9 5-1 1-3 2-4 4h1 1v1h2c0 1-2 2-3 3l-1-1c-1 1-3 1-4 2l-2 1-2-2c-2 0-5 0-6 1h-2-1c2-2 4-3 6-4h-1l-2 1h-1l2-3c-1 1-2 1-3 2h-1 0v-1c0-1 0 0 0 0h-3 1v-1l1-1c2-1 4-3 6-4h-2c-1 1-2 1-4 2h-3v-1l-1-2c0-1-1-1-1-1h-1v-3h0c-2 2-3 3-6 2 2-1 3-3 4-4l1 1 1-1c1-2 0-5 0-7l-2-4z" class="f"></path><path d="M237 244h2c1 0 1-1 2-1 2-1 5-2 7-3-3 2-5 4-9 5-1 1-3 2-4 4h1 1v1h2c0 1-2 2-3 3l-1-1c-1 1-3 1-4 2l2-2h-1-5c1 0 1-1 1-1l4-2h-1l-3 1h-1c2-2 7-6 10-6z" class="K"></path><path d="M236 216c5 1 10 2 14 4v1h-1-1v1 1h0c1 0 1 1 2 1h1-1c-1 1-2 1-3 1s-2 1-3 1c-2 2-6 4-9 6h0c2 0 4-2 5-1-2 1-5 2-6 4l1 1c1-1 1-1 2 0-1 1-3 1-4 2-4 0-7 2-10 4l8-7c-4 2-7 4-11 6-1 0-2 1-4 1v-1c5-1 10-5 15-8h0 0c-4 1-7 4-11 5h0c-1 1-1 2-2 2s-2 0-3 1l-1-1 1-1h-1l-1 1v-1c0-2-1-5 1-6 0-1 1-1 1-1h1c1 0 2-1 2-1l1-1c1 0 1-1 2-2h-3l5-3h1l3-1c0-1 1-1 1-1h1l1-1h0l1-1c1 0 1 0 2 1 1 0 0 0 1-1h1 1 1v-1h1c1 0 2 0 3-1h0l1 2h0 3 1 1v-1c-2-1-4-2-7-2-2 0-2 0-4-2z" class="V"></path><path d="M225 229h2l2-1c1 0 1-1 2-1 2-1 3-2 5-3 2 0 3-1 4-1 3-1 3-2 6-1l-2 2c-5 1-9 3-12 5l-8 4h-1c-1 0-2 2-4 2v-1c1 0 2 0 3-2l3-3z" class="c"></path><path d="M236 216c5 1 10 2 14 4v1h-1-1v1h-2c-3-1-3 0-6 1-1 0-2 1-4 1-2 1-3 2-5 3-1 0-1 1-2 1l-2 1h-2l-3 3c-1 2-2 2-3 2v1l1 1c1 0 1-1 2-1 1-1 1-1 2-1l3-2v1h0c-1 1-3 2-5 3-1 1-2 1-2 2h0 0c-1 1-1 2-2 2s-2 0-3 1l-1-1 1-1h-1l-1 1v-1c0-2-1-5 1-6 0-1 1-1 1-1h1c1 0 2-1 2-1l1-1c1 0 1-1 2-2h-3l5-3h1l3-1c0-1 1-1 1-1h1l1-1h0l1-1c1 0 1 0 2 1 1 0 0 0 1-1h1 1 1v-1h1c1 0 2 0 3-1h0l1 2h0 3 1 1v-1c-2-1-4-2-7-2-2 0-2 0-4-2z" class="U"></path><path d="M215 232c1 1 1 0 2 0h0c3-1 6-4 8-6 0 2-4 5-6 6l1 1c3-3 6-7 10-8 3-2 6-3 10-4h0c-1 2-4 2-6 3-1 0-1 1-2 1-3 1-5 3-7 4l-3 3c-1 2-2 2-3 2v1l1 1c1 0 1-1 2-1 1-1 1-1 2-1l3-2v1h0c-1 1-3 2-5 3-1 1-2 1-2 2h0 0c-1 1-1 2-2 2s-2 0-3 1l-1-1 1-1h-1l-1 1v-1c0-2-1-5 1-6 0-1 1-1 1-1z" class="b"></path><path d="M289 352h1c3 0 4 0 7-1-1 1-2 1-3 3 1 0 1 0 3-1l1-1-2 2h1 2c0 1 0 1-1 2h1l1 1-1 1h2c1-1 2-1 4-2l-2 3h1l1-1h1l-3 3h1l1-1 1 1h-1l1 1 1-1v1 1c1-1 1-1 2-1h2v1h0 1 2c-1 1-2 2-4 2-1 0-1 1-1 1-2 1-2 0-3 0l-2 1h0l2-2v-1c-1 1-2 1-3 2-1 0-3 1-4 1s-2 1-3 1v-1c-2 0-3 1-6 1l-1 2 2-1v1 2h2c-1 0-1 1-1 1l-1 1c-1 0-1-1-1-2v-1c-1 1-2 1-3 0h-1l1-1h-2v-1-1c-2 0-4 1-6 1-1 1-3 1-5 1-1 0-1 1-2 0h1l-1-1c1 0 2-1 3-1 0 0 1 0 2-1l2-1c1-1 1-1 2-1 1-1 0-1 1-1-2 0-3 1-5 1l1-1h2c1-2 1-1 1-2-2 0-3 1-4 1l-1-1 1-2v-1l12-7z" class="U"></path><path d="M289 352h1c3 0 4 0 7-1-1 1-2 1-3 3 1 0 1 0 3-1l1-1-2 2-2 2h1 1 0c-1 1-1 2-4 2h0c-1 1-3 2-4 3-3 0-5 1-7 2l3-3-1-1-5 3h0c3-3 7-5 9-8-3 2-7 5-10 6v-1l12-7z" class="K"></path><path d="M283 359h1c1-1 2-1 3-2l5-1c-1 1-3 2-4 3h1s1-1 2-1h1c-1 1-3 2-4 3-3 0-5 1-7 2l3-3-1-1z" class="c"></path><defs><linearGradient id="AL" x1="289.879" y1="362.536" x2="298.6" y2="357.548" xlink:href="#B"><stop offset="0" stop-color="#2f2f30"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#AL)" d="M296 354h1 2c0 1 0 1-1 2h1l1 1-1 1h2c1-1 2-1 4-2l-2 3h1l1-1h1l-3 3h1l1-1 1 1h-1l1 1 1-1v1 1c1-1 1-1 2-1h2v1h0 1 2c-1 1-2 2-4 2-1 0-1 1-1 1-2 1-2 0-3 0l-2 1h0l2-2v-1c-1 1-2 1-3 2-1 0-3 1-4 1s-2 1-3 1v-1c-2 0-3 1-6 1h0-1-1c1-1 1-2 1-3l-2 1h0-1l3-3c-2 0-5 2-8 2l6-4h1c1-1 3-2 4-3h0c3 0 3-1 4-2h0-1-1l2-2z"></path><path d="M305 361l1 1 1-1v1 1c1-1 1-1 2-1h2v1h0 1 2c-1 1-2 2-4 2-1 0-1 1-1 1-2 1-2 0-3 0l-2 1h0l2-2v-1c-1 1-2 1-3 2-1 0-3 1-4 1 1-1 2-1 3-2s2-1 3-2h0c-2 1-4 1-6 2l2-2 4-2z" class="L"></path><path d="M250 301c1 1 2 3 3 5v3c0 1 0 1 1 2 0 1 0 2-1 3v2h3 1c1-1 1-1 3-2 2 0 3-1 5-2-2 2-4 3-6 3-1 0-2 1-3 2v1h1 0c1-1 2-1 3-1v-1h2c-2 1-5 3-6 5l8-3c-1 0-3 2-4 3 1 0 2 0 3-1 1 0 2-1 3-1h0c1-1 1-1 1-2 2-1 3-2 5-2 1 0 1-1 2-1 1-1 2-1 3-2l1 1 3-2v1h0l-2 2h2 2v1c-1 0-2 1-3 1 1 1 1 1 2 0h1l1 1h-1l1 1 1-1h2 1l-1 1h2 3l-4 2v1l-2 2h-1l-2 2-2 1-1-1c-1 1-2 1-3 2l-1 1-1 1h1v1h-1-2 0l-1-1-4 2c1 0 2 0 2 1l-2 1h0c0 1-1 1-1 2l1-1h2v1h-3l-1-1 1-1h-4l1-1c1 0 1 0 2-1-2 0-3 1-5 1v-1l1-1h0-2l1-1h1l1-1h-1l1-1h-1c-2 1-4 1-6 2l-6 3 3-3c1 0 1-1 2-1l3-2-1-1c0-1 1-1 2-2l-1-1c-2 0-3 0-4-1-2-1-1-2-1-3l-1-1c-1 1-2 1-4 1h0c1 0 1-1 2-2 2-2 3-5 3-8l-3-6h-1l1-1z" class="m"></path><defs><linearGradient id="AM" x1="273.564" y1="325.314" x2="278.51" y2="318.763" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#555"></stop></linearGradient></defs><path fill="url(#AM)" d="M278 313l3-2v1h0l-2 2h2 2v1c-1 0-2 1-3 1 1 1 1 1 2 0h1l1 1h-1l1 1 1-1h2 1l-1 1h2 3l-4 2v1l-2 2h-1l-2 2-2 1-1-1c-1 1-2 1-3 2l-1 1c-1-1-2 0-4 0 1-1 2-1 3-2-1-1-1 0-2 0s-3-2-3-2v-1h-3l-1 1-1-1c1 0 2-1 3-1v-1l-2 1-1-1c2-1 4-2 6-4h-1l-4 2c1-1 1-1 1-2 2-1 3-2 5-2 1 0 1-1 2-1 1-1 2-1 3-2l1 1z"></path><path d="M278 313l3-2v1h0l-2 2h2 2v1c-1 0-2 1-3 1l-2 1 1 1h1 0l-1 1-2-1-6 3v-1c2-1 3-2 5-3h-1l1-1v-1l-1 1-1-1c1-1 2-1 4-2z" class="e"></path><path d="M282 316h1l1 1h-1l1 1 1-1h2 1l-1 1h2 3l-4 2v1l-2 2h-1-2v-1h-2c-1-1-1-2-2-3l1-1h0-1l-1-1 2-1c1 1 1 1 2 0z" class="P"></path><path d="M279 319l1-1h0-1l-1-1 2-1c1 1 1 1 2 0v1 1h2c-1 1-1 1-1 2 1 0 1 1 2 1v1l1 1h-1-2v-1h-2c-1-1-1-2-2-3z" class="L"></path><path d="M221 257c4 1 7 1 10 5 0 1 1 1 1 2 1 1 1 2 1 3 1 4 1 6-1 9l-1 1-3 3c-3 1-6 2-9 1h-1c-2-1-5-3-6-5-2-3-2-5-2-8l1-1v-2c2-4 6-6 10-8z" class="W"></path><path d="M226 274c-1 1-2 2-3 2-2 1-4 1-5-1-2-1-3-4-3-6 1-2 2-3 4-4v1c1 1 2 1 2 1l3 1 2-1c1 0 2 1 3 2l-3 5z" class="p"></path><path d="M225 263h-1v-1c3 0 5 0 7 2h1c1 1 1 2 1 3 1 4 1 6-1 9l-1 1-3 3c-3 1-6 2-9 1v-1c-2-1-4-2-5-4 1 1 2 2 3 2h3c1 0 2 0 4-1h0c2 0 3-2 4-3h-2l3-5c-1-1-2-2-3-2l-2 1-3-1 1-2c1-2 2-2 3-2z" class="i"></path><path d="M228 274h1s0 1 1 2c-2 2-3 2-6 3h0v-2c2 0 3-2 4-3z" class="M"></path><path d="M230 266c1 1 2 3 2 4 0 2-2 4-2 6-1-1-1-2-1-2h-1-2l3-5h1c-1-1 0-2 0-3h0z" class="l"></path><path d="M221 267l1-2c1-2 2-2 3-2 2 1 4 1 5 3h0c0 1-1 2 0 3h-1c-1-1-2-2-3-2l-2 1-3-1z" class="D"></path><path d="M254 359h0c-3-1-5-3-6-6-2-3-2-6 0-9 1-3 3-6 5-7h1c2-1 4-1 7-1 3 2 6 3 8 7 0 2 1 3 2 5-1 2-1 4-2 6l-2 2c-2 2-6 4-9 4-2 0-3-1-4-1z" class="G"></path><path d="M259 355c-2 0-5 0-6-2-1-1-2-3-2-5s2-4 4-5l2 2c1 1 3 1 4 0h3c1 2 1 2 1 4-1 2-2 4-4 5l-1 1h-1z" class="U"></path><path d="M261 345h3c1 2 1 2 1 4-1 2-2 4-4 5l-1 1 1-4c1 0 2-1 3-2l-1-1h-5-1v-1-2c1 1 3 1 4 0z" class="c"></path><path d="M259 341c1-1 2-2 3-1 1 0 1 0 2 1l1 1 1-1c1 1 1 2 3 2 0 2 1 3 2 5-1 2-1 4-2 6l-2 2c-2 2-6 4-9 4-2 0-3-1-4-1l1-1c-1 0-2-1-3-2 1 0 1 1 2 1h2v1h2c1-1 1-1 2-1l-1-2h1l1-1c2-1 3-3 4-5 0-2 0-2-1-4h-3c-1 0-2 0-3-1-1 0-1-1-2-2 1 0 2-1 2-1h1z" class="X"></path><path d="M259 341l5 1c1 2 4 3 4 6v2c-1 3-1 4-4 6 0 1-1 1-2 1h-1-1l-1-2h1l1-1c2-1 3-3 4-5 0-2 0-2-1-4h-3c-1 0-2 0-3-1-1 0-1-1-2-2 1 0 2-1 2-1h1z" class="N"></path><path d="M259 341l5 1c-2 1-3 2-5 3l-1-1c-1 0-1-1-2-2 1 0 2-1 2-1h1z" class="o"></path><path d="M235 297c4-2 6-2 9-2 5 3 7 5 9 11h0c-1-2-2-4-3-5l-1 1h1l3 6c0 3-1 6-3 8-1 1-1 2-2 2l-3 1c-4 1-7 1-10-1-3-1-5-4-6-7v-5c1-4 3-7 6-9z" class="h"></path><path d="M235 297c4-2 6-2 9-2 5 3 7 5 9 11h0c-1-2-2-4-3-5-1-2-2-2-4-3h-1-2-3c-1-1-3-1-5-1z" class="E"></path><path d="M237 314l-1-1c-2-1-2-3-2-4 1-2 3-3 4-5v-1h1v2h7c0 1 1 2 1 3 0 2-1 5-3 7-2 0-3 1-5 0-1 0-1 0-2-1z" class="r"></path><path d="M246 305h1c1 0 1 1 1 2s0 2 1 3h2 0 1l1-2c0 3-1 6-3 8-1 1-1 2-2 2l-3 1c-4 1-7 1-10-1-3-1-5-4-6-7h1v-3h1c0 2 2 4 4 6 1 1 1 0 2 0 1 1 1 1 2 1 2 1 3 0 5 0 2-2 3-5 3-7 0-1-1-2-1-3z" class="X"></path><path d="M246 305h1c1 0 1 1 1 2s0 2 1 3l-3 5 1 1 1-1 1 1c-1 1-1 1-2 1l-1 1h-2v1h-3 0c-2 0-5 0-6-2h1 1 2c-1 0-2-1-2-1v-1l1 1 1-1c2 1 3 0 5 0 2-2 3-5 3-7 0-1-1-2-1-3z" class="a"></path><path d="M276 374c2-1 5-1 8-1l5 5c0 1 1 1 1 2 1 1 1 1 1 2-1 2-1 3-1 4 0 4-3 6-4 8 1 0 1 1 1 0 1-1 1-1 2-1h1 1l-1-1c0-2 0-2 1-3h1v-1h-1-1c1-1 1-2 2-2l1-1c0 1 0 2 1 2l1 1c0-1-1-2-1-2v-1-1-1c0-1-1-2-1-4-1-1-1-2-1-4 2 0 2 0 3 1l-1 1-1-1c0 3 2 8 3 12l1 6v8l-1 6h0l-2 2-1-1 2-2h0v-1-3c-2 1-2 2-5 2l-1-2-1 1v-2l-1-1c-1-1-1-3-1-5h-1c-2 1-3 1-5 0-2 1-4 1-6 0 1 0 1-1 3-1 1 0 1 0 2-1l-1-2c-3 0-4-1-6-3-1-1-2-1-3-2 0-2 0-2 1-4 0-2 1-3 2-4l-3-1c2-2 4-3 7-4z" class="U"></path><path d="M296 388l1 6v8c-3 0-3 0-6 2h-1l-1-4c-1 0-1-1-1-2l-1-1v-1l1-1c1 1 1 2 1 3l1 1h1v-1c0-1 0-1 1-2h0l1-2 3-6z" class="m"></path><path d="M276 374c3 0 6 0 9 3 1 2 4 5 4 8s-2 6-4 8h0c1-2 2-4 2-6 0-1-1-2-2-3-1-3-3-5-6-6-2 0-5 0-7 1l-3-1c2-2 4-3 7-4z" class="l"></path><path d="M272 379c2-1 5-1 7-1 3 1 5 3 6 6 1 1 2 2 2 3 0 2-1 4-2 6h0c-2 2-3 3-5 3-2 1-4 1-6 0 1 0 1-1 3-1 1 0 1 0 2-1l-1-2c-3 0-4-1-6-3-1-1-2-1-3-2 0-2 0-2 1-4 0-2 1-3 2-4z" class="P"></path><path d="M283 384h1l3 3c-3 4-4 6-8 7l-1-2c1 0 2 0 2-1 3-2 3-4 3-7z" class="l"></path><path d="M272 389v-1c0-1-1-2-1-3 1-1 2-2 3-2h0c2 0 3-1 4-1 1 1 2 1 3 1l2 1h0c0 3 0 5-3 7 0 1-1 1-2 1-3 0-4-1-6-3z" class="r"></path><path d="M275 333h0c1 0 2 0 2-1v1l-3 3c1 0 1-1 2-1 0 1-1 1-2 2 1 0 1 1 1 0 2 0 3-1 5-1v1h-1l1 1c1-1 2-1 4-1h0l1 1c1 0 2-1 2-1 1-1 1-1 2-1h1l2 2h0 2l-3 2h2l-1 1h3 0l-1 1 3-1c1 0 1 0 2-1v1h0l-1 1 1 1c1 1 1 2 1 2 1 2 2 3 3 5h1l-1 1-1 1h2 0c0 1 1 1 2 1l1 1c0 1-1 2-2 2h0c-2 1-3 1-4 2h-2l1-1-1-1h-1c1-1 1-1 1-2h-2-1l2-2-1 1c-2 1-2 1-3 1 1-2 2-2 3-3-3 1-4 1-7 1h-1l-12 7v1l-1 2-1-1h0c-1-1-1-1-2 0h-4l-1-1 1-2-2-2 2-2c1-2 1-4 2-6-1-2-2-3-2-5-2-4-5-5-8-7 1-1 1-1 2 0l1-1c1 1 2 1 3 3 0 0 1 0 2 1 1 0 2-1 3-1v-1h-3l-1-1 2-1c0 1 1 1 2 2v-1l1-1h-1l2-2h1z" class="b"></path><path d="M280 338c1-1 2-1 4-1l-1 1c-1 1-1 1-3 1v-1z" class="c"></path><path d="M289 336h1l2 2h0 2l-3 2h2l-1 1h3 0l-1 1c-1 0-3 2-3 2h-3l-1-1h2l-2-2 1-1-1-1-1 1h-1l3-2-1-1c1-1 1-1 2-1z" class="V"></path><path d="M269 354c0 1 1 2 1 2v3c2-1 3-1 4-2l8-5c1-1 2-2 4-2s3-1 5-2c-2 2-4 3-6 5h0l1-1c2-1 5-2 7-2l-1 1-3 1-12 7v1l-1 2-1-1h0c-1-1-1-1-2 0h-4l-1-1 1-2-2-2 2-2z" class="c"></path><path d="M297 341c1 0 1 0 2-1v1h0l-1 1 1 1c1 1 1 2 1 2 1 2 2 3 3 5h1l-1 1-1 1h2 0c0 1 1 1 2 1l1 1c0 1-1 2-2 2h0c-2 1-3 1-4 2h-2l1-1-1-1h-1c1-1 1-1 1-2h-2-1l2-2-1 1c-2 1-2 1-3 1 1-2 2-2 3-3-3 1-4 1-7 1h-1l3-1 1-1c-2 0-5 1-7 2l-1 1h0c2-2 4-3 6-5 1 0 2 0 2-1h0-2-1l3-3h0l-5 1c1 0 2-1 3-1 0 0 2-2 3-2l3-1z" class="e"></path><path d="M302 352h2 0c0 1 1 1 2 1l1 1c0 1-1 2-2 2h0c-2 1-3 1-4 2l-1-1 4-3h-2l1-1h-2l1-1z" class="i"></path><path d="M297 341c1 0 1 0 2-1v1h0l-1 1 1 1c1 1 1 2 1 2 1 2 2 3 3 5h1l-1 1h-1c-1 0-2-1-3-1v-2-2c0-1-1-1-2-2v1l-2-1c1-1 1-1 1-2l1-1z" class="F"></path><path d="M275 333h0c1 0 2 0 2-1v1l-3 3c1 0 1-1 2-1 0 1-1 1-2 2 1 0 1 1 1 0 2 0 3-1 5-1v1h-1l1 1v1l-1 1 1 1h0c2-1 3 0 5 0h0l-3 3c1 0 2-1 3-1h0c-1 1-3 4-5 4-4 0-5 4-9 1-1-2-2-3-2-5-2-4-5-5-8-7 1-1 1-1 2 0l1-1c1 1 2 1 3 3 0 0 1 0 2 1 1 0 2-1 3-1v-1h-3l-1-1 2-1c0 1 1 1 2 2v-1l1-1h-1l2-2h1z" class="f"></path><path d="M253 284s1-1 2-1l-1-1c2-1 4-3 6-3l-2 2v1c1-1 2-1 3-1h0c-1 0-2 1-2 2 0 0 3-1 4-1v1c-1 0-2 0-3 1v1s1 0 1-1h2c0 1-1 1-2 2h0 4c-1 1-2 1-3 2v1h1c0 1-1 2-2 2l-2 1 1 1 1-1h3 1l-1 1s-1 0-1 1c2 0 3 0 4-1h0c-1 1-2 2-4 2l-1 1h4 1l-2 2c1 0 2 0 2 1 1-1 1-1 2 0 2-1 2 0 4-1h1s0 1-1 1l-1 1v1l2-1h0l-1 1s-1 1-1 2c1 0 1-1 2-1 0 1-1 2-2 2v1l3-1h0l-1 2h0l2-1c0 1-3 4-4 5h-1c-1 0-2 1-3 2h1c1 0 3-1 4-2h0c-1 1-7 6-7 7l3-2c2-1 5-2 8-3h0 0c-1 1-2 1-3 2-1 0-1 1-2 1-2 0-3 1-5 2 0 1 0 1-1 2h0c-1 0-2 1-3 1-1 1-2 1-3 1 1-1 3-3 4-3l-8 3c1-2 4-4 6-5h-2v1c-1 0-2 0-3 1h0-1v-1c1-1 2-2 3-2 2 0 4-1 6-3-2 1-3 2-5 2-2 1-2 1-3 2h-1-3v-2c1-1 1-2 1-3-1-1-1-1-1-2v-3h0c-2-6-4-8-9-11h3c1 1 1 1 2 1s2 2 3 2l1 1c0 1 1 1 0 2 0 1 0 1 1 2 1 0 1 0 2-1-1 0-1-1-2-1l1-1c-1-1 0-1-1-1v-1h-1l-1-1h0c0-1-1-2-1-2v-1l1 1 1-1c-1 0-1 0-2-1h-1-2v-1-1c-1 1-2 1-3 1v-2c-1 0-3 1-5 1l1-2h0c-1 1-2 1-3 1-1 1-1 1-2 0 1-1 2-1 3-2 1 0 2-1 3-1h1c2 1 4-1 6-2 1-1 2-1 4-1z" class="V"></path><path d="M273 298h1s0 1-1 1l-1 1v1l2-1h0l-1 1s-1 1-1 2c1 0 1-1 2-1 0 1-1 2-2 2v1c-1 1 0 1-1 1h-1v-1l-1-1-2 2-1-1 3-3c-1 0-2 1-3 2 0-1 1-2 1-4v1-2c1-1 1-1 2 0 2-1 2 0 4-1z" class="Q"></path><path d="M273 298h1s0 1-1 1l-1 1v1h-1-2-1l1-1h0-2v1-2c1-1 1-1 2 0 2-1 2 0 4-1z" class="L"></path><path d="M253 284s1-1 2-1l-1-1c2-1 4-3 6-3l-2 2v1c1-1 2-1 3-1h0c-1 0-2 1-2 2 0 0 3-1 4-1v1c-1 0-2 0-3 1v1s1 0 1-1h2c0 1-1 1-2 2h0 4c-1 1-2 1-3 2v1h1c0 1-1 2-2 2h-2c1-1 1-1 1-2-1-1-1-1-2-1l1-2h0c-2 0-2 0-4 1v1l-2 2h1l2-1v1l-2 1c1 0 2-1 3-1 0 2-3 3-4 5l1-1 6-3-5 4h1 0c2-1 3-1 5-1v1c-1 0-2 0-3 1 1 1 2 0 3 0v1l-1 1h2c-1 2-2 3-4 4l-1 1c2 0 3-1 4-2v1c-1 1-1 2-3 2l1 1c1-1 3-2 5-3v1c-1 1-3 2-4 3h1c1 0 2-1 3-1-1 2-3 3-5 3v1c3 0 5-2 8-3-3 3-6 4-9 5v1c3 0 5-2 8-3-2 2-4 3-7 4 0 0-1 0-1 1l-2-1c1-1 0-1 1-1v-1h-2 0v-2c-1-1-1-3-2-3-2-6-4-8-9-11h3c1 1 1 1 2 1s2 2 3 2l1 1c0 1 1 1 0 2 0 1 0 1 1 2 1 0 1 0 2-1-1 0-1-1-2-1l1-1c-1-1 0-1-1-1v-1h-1l-1-1h0c0-1-1-2-1-2v-1l1 1 1-1c-1 0-1 0-2-1h-1-2v-1-1c-1 1-2 1-3 1v-2c-1 0-3 1-5 1l1-2h0c-1 1-2 1-3 1-1 1-1 1-2 0 1-1 2-1 3-2 1 0 2-1 3-1h1c2 1 4-1 6-2 1-1 2-1 4-1z" class="c"></path><path d="M253 284s1-1 2-1l-1-1c2-1 4-3 6-3l-2 2v1c1-1 2-1 3-1h0c-1 0-2 1-2 2 0 0 3-1 4-1v1c-1 0-2 0-3 1v1s1 0 1-1h2c0 1-1 1-2 2h0 4c-1 1-2 1-3 2v1h1c0 1-1 2-2 2h-2c1-1 1-1 1-2-1-1-1-1-2-1l1-2h0c-2 0-2 0-4 1-1 1-3 1-4 2l-1-1 7-4c-3 1-8 4-11 4l4-2 3-2z" class="Q"></path><path d="M195 184c2 0 4 1 6 1 3 1 6 0 9 0l23 2v1h-3 3c-1 1-6 1-7 0h-1c-1-1-2 0-3 0l-2 1c2 0 5 1 7 2l5 8-1 1-1-1h-1l-2-1c0 3-1 5-2 7h-1c-2 0-3 1-5 2h-2v-1-1h-1v1h-1-1-4c-1 0-1-1-1-2l-3 2h0-4l-1-1 1-1c-1-1-2-1-3-2v2c-2 0-2 1-3 0l-1-3v-1-4l1-2h0c1-1 2-3 2-4l-2-3c-1 0-3-2-3-2 1-1 1-1 2-1z" class="S"></path><path d="M216 191c-1-1-1 0-1-1h-1-1v1h-1v-2h1c2-1 5 0 7 0s5 1 7 2l5 8-1 1-1-1h-1c0-2 0-3-2-5-3-3-5-2-9-2-1-1-1-1-2-1z" class="k"></path><path d="M193 185c1-1 1-1 2-1 3 1 5 3 8 4 2 1 5 2 8 3l-2 1-2-1h-1c-1 0-2 0-3 1-3 1-3 2-4 5v2c-1 0-1 0-1 1h-1l-1-1c0-2 1-4 2-5 0-1 1-2 1-2 0-1 1-1 2-2-2-2-3-2-5-3-1 0-3-2-3-2z" class="s"></path><path d="M195 184c2 0 4 1 6 1 3 1 6 0 9 0l23 2v1h-3 3c-1 1-6 1-7 0h-1c-1-1-2 0-3 0l-2 1c-2 0-5-1-7 0h-1v2h1v-1h1 1c0 1 0 0 1 1v1l-1 1c0 1 1 2 0 2l-4-4h0l-8-3c-3-1-5-3-8-4z" class="G"></path><path d="M215 206c0-1 1-1 1-2v-2c0-1 1-2 0-3h0c0-3 2-4 3-5h4c2 1 3 2 4 4 0 3-1 5-2 7h-1c-2 0-3 1-5 2h-2v-1-1h-1v1h-1z" class="j"></path><path d="M199 202c0-3 1-6 3-8 1-1 3-1 5-1 1 0 3 2 4 3 1 2 0 4 0 6l-2 2-3 2h0-4l-1-1 1-1c-1-1-2-1-3-2z" class="h"></path><path d="M292 318l1-1h2 0 2l-2 3c1 0 2-1 3-2 0 2-1 3-2 4h0 3 0l-1 1c-1 0-2 1-2 2 1-1 2-1 4-1-1 0-2 1-3 2h1 1l-1 2c1 0 1 1 2 1 0 1 0 1-1 1v1l2-1h2v1h-1c-1 0-2 1-2 1v1c1 0 1 0 2-1h2 0c1-1 2-1 3-1 0 1 0 0-1 1-1 0-2 1-3 2h1l2-1h0c2-1 2-1 4-1l-2 1v1h2c0 1-2 1-2 3 1-1 3-1 4-1l-1 1v1h0v1l-1 1v1s1 0 2-1v1l-2 1 1 1 2-1v1l-1 2h1l2 1v2l1-1h0l-1 1 1 1c-2 0-2 1-2 0l-1 1-1 2h1c0 1 1 1 0 2 1 0 2 0 3 2l-2 2h2l-1 4-1 1h0-2-1 0v-1h-2c-1 0-1 0-2 1v-1-1l-1 1-1-1h1l-1-1-1 1h-1l3-3h-1l-1 1h-1l2-3h0c1 0 2-1 2-2l-1-1c-1 0-2 0-2-1h0-2l1-1 1-1h-1c-1-2-2-3-3-5 0 0 0-1-1-2l-1-1 1-1h0v-1c-1 1-1 1-2 1l-3 1 1-1h0-3l1-1h-2l3-2h-2 0l-2-2h-1c-1 0-1 0-2 1 0 0-1 1-2 1l-1-1h0c-2 0-3 0-4 1l-1-1h1v-1c-2 0-3 1-5 1 0 1 0 0-1 0 1-1 2-1 2-2-1 0-1 1-2 1l3-3v-1c0 1-1 1-2 1h0v-3h1v-1h-1l1-1 1-1c1-1 2-1 3-2l1 1 2-1 2-2h1l2-2v-1l4-2z" class="W"></path><path d="M306 333h0c2-1 2-1 4-1l-2 1v1h2c0 1-2 1-2 3 1-1 3-1 4-1l-1 1v1h0v1l-1 1v1s1 0 2-1v1l-2 1 1 1 2-1v1l-1 2h1l2 1v2l1-1h0l-1 1 1 1c-2 0-2 1-2 0l-1 1-2-2h0l-1-1 1-1h-1v-1l-3-1v-1l1-1h-1v-1l-1-1v-1c0-1-1-1-2-1l1-2h-1-1l3-3z" class="J"></path><path d="M288 321c1-1 3-1 4-2l-3 3h2v1l-1 1v1c1 0 2-1 3 0l-2 2c1 0 3-1 4-1-1 1-3 2-4 3 1 0 2-1 3-1h1l-1 2h1v1h0v1 1h2c0 1-1 2-1 3 1-1 2-2 3-1l-1 1-2 2h1c0-1 1-1 1-1l1 1c-1 1-2 2-4 3h0 0-3l1-1h-2l3-2h-2 0l-2-2h-1v-1l2-1v-1h-1v-1c0-1 1-1 1-2h-1-1l2-2c-2 0-3 1-5 2h0-1v-1h1c-2-1-2-2-3-3h-2l2-1 2-2h1l2-2z" class="F"></path><path d="M283 325h0c1 0 2 0 3-1v1 1c1 0 1 1 2 1l1-1v1h-1c-1 1-1 2-2 3h0-1v-1h1c-2-1-2-2-3-3h-2l2-1z" class="O"></path><defs><linearGradient id="AN" x1="281.983" y1="337.082" x2="287.783" y2="331.811" xlink:href="#B"><stop offset="0" stop-color="#333334"></stop><stop offset="1" stop-color="#4c4c4b"></stop></linearGradient></defs><path fill="url(#AN)" d="M276 328l1-1c1-1 2-1 3-2l1 1h2c1 1 1 2 3 3h-1v1h1 0c2-1 3-2 5-2l-2 2h1 1c0 1-1 1-1 2v1h1v1l-2 1v1c-1 0-1 0-2 1 0 0-1 1-2 1l-1-1h0c-2 0-3 0-4 1l-1-1h1v-1c-2 0-3 1-5 1 0 1 0 0-1 0 1-1 2-1 2-2-1 0-1 1-2 1l3-3v-1c0 1-1 1-2 1h0v-3h1v-1h-1l1-1z"></path><path d="M276 328l1-1c1-1 2-1 3-2l1 1h2c1 1 1 2 3 3h-1v1h1 0-2c-1 1-2 1-2 2v1l-1 1-1-1-1 1h-2l-1 1c-1 0-1 1-2 1l3-3v-1c0 1-1 1-2 1h0v-3h1v-1h-1l1-1z" class="b"></path><path d="M276 328l1-1c1-1 2-1 3-2l1 1h2c1 1 1 2 3 3h-1v1h1 0-2c-1 1-2 1-2 2h-2l2-2h-1c-1-1-3-2-5-2z" class="V"></path><path d="M283 326c1 1 1 2 3 3h-1v1h1 0-2c-1 1-2 1-2 2h-2l2-2c1 0 2 0 2-1-1 0-2-1-3-2l2-1z" class="Q"></path><path d="M295 341h0c2-1 3-2 4-3l-1-1s-1 0-1 1h-1l2-2h2c1 1 1 1 3 1v1 1l1 1c0 1 0 3 1 4s3 2 2 3c1 0 1 1 2 1l-1 1h0 1l2 3h0l1 2h1c1 0 2 0 3 2l-2 2h2l-1 4-1 1h0-2-1 0v-1h-2c-1 0-1 0-2 1v-1-1l-1 1-1-1h1l-1-1-1 1h-1l3-3h-1l-1 1h-1l2-3h0c1 0 2-1 2-2l-1-1c-1 0-2 0-2-1h0-2l1-1 1-1h-1c-1-2-2-3-3-5 0 0 0-1-1-2l-1-1 1-1h0v-1c-1 1-1 1-2 1l-3 1 1-1z" class="P"></path><path d="M295 341h0c2-1 3-2 4-3l-1-1s-1 0-1 1h-1l2-2h2c1 1 1 1 3 1v1 1l1 1c0 1 0 3 1 4s3 2 2 3c1 0 1 1 2 1l-1 1h0 1l2 3h0l1 2h1c1 0 2 0 3 2l-2 2-1 1v-1-1c-2 0-2 0-3-1h-1l1-1c-1-1-1-2-1-2-1-1-1 0-3-1l2-1-1-1h-2l1-1h0-1-1c0-2 0-2-1-3s-1-2-2-3c0-1-1-1-2-2h0v-1c-1 1-1 1-2 1l-3 1 1-1z" class="j"></path><path d="M314 363h0v1 2 3c1 1 2 1 4 1v2l-1 1c0 3-2 6-2 9v1c1-2 1-5 3-7-1 4-2 9-4 13l-1 1c0 1 0 2 1 3 1 2 1 4 1 7-2 1-3 3-4 4-1 3-3 4-5 6l-4 5v1l-8 13c-1 1-2 4-2 5-1 0-2 0-2 1l2-7c1-1 1-1 0-2l-1-3h1v-1h-1c0-2 0-2 1-3 0-2 0-3 1-5 1 0 1-1 1-2 1-2 2-2 2-4h0l1-6v-8l-1-6c-1-4-3-9-3-12l1 1 1-1c1 1 2 2 3 4v1h1v-2c1-1 1-2 0-3-1 0-1 0-2-1h1v-1l-5-1v-1h0-2v-2-1l-2 1 1-2c3 0 4-1 6-1v1c1 0 2-1 3-1s3-1 4-1c1-1 2-1 3-2v1l-2 2h0l2-1c1 0 1 1 3 0 0 0 0-1 1-1 2 0 3-1 4-2z" class="Y"></path><path d="M307 381c2 4 1 11 1 16-1 3-1 5-1 8l-1 1c0-6 1-12 0-17 0-3 0-4-2-5l2-2 1-1z" class="K"></path><path d="M301 382l2 1-1 1v4c1 4-1 9 0 13 1 3 0 7 0 10v3 1 1l-3 1v-3l2-7v-5c1-3 1-6 0-8 0-2 0-4-1-6 0-2-1-4 1-6z" class="J"></path><path d="M300 389v-1c1 2 1 4 1 6 1 2 1 5 0 8v5l-2 7v3l3-1-8 13c-1 1-2 4-2 5-1 0-2 0-2 1l2-7c1-1 1-1 0-2l-1-3h1v-1h-1c0-2 0-2 1-3 0-2 0-3 1-5 1 0 1-1 1-2 1-2 2-2 2-4h0l1-6v-8 1c2 0 2-1 3-2v-4z" class="N"></path><path d="M296 417c0-3 0-5 1-7h2c0 2 0 4-1 6l-1-1-1 2z" class="D"></path><path d="M293 414l1 3h2c0 2 0 3-2 6v1l-2 2-1-3h1v-1h-1c0-2 0-2 1-3 0-2 0-3 1-5z" class="C"></path><path d="M300 393c0 6-1 11-1 17h-2c-1 2-1 4-1 7h-2l-1-3c1 0 1-1 1-2 1-2 2-2 2-4h0l1-6v-8 1c2 0 2-1 3-2z" class="R"></path><path d="M314 363h0v1 2 3c1 1 2 1 4 1v2l-1 1c0 3-2 6-2 9v1c1-2 1-5 3-7-1 4-2 9-4 13-1-2-1-4-1-6v-1c-2-2-3-4-6-6-1 0-1 0-2 1l2 4-1 1-2 2-1-1-2-1c-2 2-1 4-1 6v1 4c-1 1-1 2-3 2v-1l-1-6c-1-4-3-9-3-12l1 1 1-1c1 1 2 2 3 4v1h1v-2c1-1 1-2 0-3-1 0-1 0-2-1h1v-1l-5-1v-1h0-2v-2-1l-2 1 1-2c3 0 4-1 6-1v1c1 0 2-1 3-1s3-1 4-1c1-1 2-1 3-2v1l-2 2h0l2-1c1 0 1 1 3 0 0 0 0-1 1-1 2 0 3-1 4-2z" class="t"></path><path d="M301 382c0-1 1-2 2-2s2 0 2 1l1 1-2 2-1-1-2-1z" class="U"></path><path d="M299 368h1c0 1 2 2 2 2 0 1-1 1 0 2l-2 1c-1-1-2-1-2-2l1-2v-1z" class="V"></path><path d="M299 367c1 0 3-1 4-1h0c0 1-1 1-1 2h2v1c-1 0-1 1-1 2h0l2 1 4-1h0l-2 2c-1-1-2-1-2-1l-3 3-1-1 2-2h-1c-1-1 0-1 0-2 0 0-2-1-2-2h-1c-1 1-3 1-4 1l1-1c1 0 2-1 3-1z" class="Q"></path><path d="M296 388c-1-4-3-9-3-12l1 1c3 3 5 8 6 12v4c-1 1-1 2-3 2v-1l-1-6z" class="B"></path><path d="M314 363h0v1 2 3c1 1 2 1 4 1v2l-1 1c-1-2-1-1-3-2 0 0-1-1-2-1s0 1-1 3h-1c-1 1-1 0-2 1s-2 1-3 1l-1-1c1-1 2-1 3-1l2-2h0l-4 1-2-1h0c0-1 0-2 1-2v-1h-2c0-1 1-1 1-2h0c1-1 2-1 3-2v1l-2 2h0l2-1c1 0 1 1 3 0 0 0 0-1 1-1 2 0 3-1 4-2z" class="O"></path><path d="M282 270h0l3-3-1-1h-1l1-2c-1 0-1 0-1-1l1-1v-1l-2-1v-1h-1l1-1h3l3 4h1 0c0 1 0 1 1 1l1 1-1 1h2c0 1 2 3 3 4h2v1c2 1 1 2 3 3l-1 1h2v1l-1 1c1 0 2 0 2 1s0 1 1 2v2l1 1v1h1v1l1 1c0 1 0 2 1 3l1 1h1l-1 1 1 1h0v2h2 0c-1 1-1 1-2 3h1v-1c1 0 2 1 2 2v1c-2 0-4-1-6 0 1 1 2 1 3 1v1l7 2 6 2c1 1 4 1 5 3h4v1c1 0 2 0 3 1l-2 2v1c0 1-1 3-2 4-1 0-2 0-3 1h0l-1 1c-3 0-8 2-9 5h-1v1l1 1c1 0 1 1 2 1l-1 2h0c1 1 2 1 3 1l1 1c0 1-1 2-2 3h-3l-2 1h-1c1-1 3-2 4-2-1-1-2 0-3 0l1-2c-1-1-3 0-4 0l-2 1h1l-1 1c-2 0-2 0-4 1h0l-2 1h-1c1-1 2-2 3-2 1-1 1 0 1-1-1 0-2 0-3 1h0-2c-1 1-1 1-2 1v-1s1-1 2-1h1v-1h-2l-2 1v-1c1 0 1 0 1-1-1 0-1-1-2-1l1-2h-1-1c1-1 2-2 3-2-2 0-3 0-4 1 0-1 1-2 2-2l1-1h0-3 0c1-1 2-2 2-4-1 1-2 2-3 2l2-3h-2 0-2l-1 1h-3-2l1-1h-1-2l-1 1-1-1h1l-1-1h-1c-1 1-1 1-2 0 1 0 2-1 3-1v-1h-2-2l2-2h0v-1l-3 2-1-1h0 0c-3 1-6 2-8 3l-3 2c0-1 6-6 7-7h0c-1 1-3 2-4 2h-1c1-1 2-2 3-2h1c1-1 4-4 4-5l-2 1h0l1-2h0l-3 1v-1c1 0 2-1 2-2-1 0-1 1-2 1 0-1 1-2 1-2l1-1h0l-2 1v-1l1-1c1 0 1-1 1-1h-1c-2 1-2 0-4 1-1-1-1-1-2 0 0-1-1-1-2-1l2-2h-1-4l1-1c2 0 3-1 4-2h0c-1 1-2 1-4 1 0-1 1-1 1-1l1-1h-1-3l-1 1-1-1 2-1c1 0 2-1 2-2h-1v-1c1-1 2-1 3-2h-4 0c1-1 2-1 2-2h-2c0 1-1 1-1 1v-1c1-1 2-1 3-1v-1c1 0 2-1 3-2h0l-2 1h-1l2-2h1l-1-1 1-1 2-1c2-1 7-3 8-5 1-1 1-1 3-2l2 2 1-1z" class="Z"></path><path d="M303 318h1c1 0 0 0 1-1 2 0 4 0 5 1-1 0-1 1-2 1h-2v1h1l1 1c-2 1-2 1-4 1l1-1c-1 0-2 0-2 1h-2c2-1 2-2 3-3v-1c-1 1-2 2-4 2 1-1 2-1 3-2h0z" class="B"></path><path d="M294 301l1 1c1-1 2-1 4-1h1c-1 0-1 1-2 1s-2 0-3 1v1c2 0 3 0 5-1l-2 2c-1 0-2 1-3 2 1 0 1 1 2 0s1-2 3-2l-3 3c-1 0-2 0-3 1l-3 1-3 1c0-1 1-1 1-2h0-2 0l2-2v-1c-2 0-2 0-3 1h-1-1l4-2h1c1-2 3-3 5-4z" class="Y"></path><path d="M297 308l-1 1c1 0 5-3 6-4-1 2-2 3-4 4l1 1c1-1 1-1 2-1l-1 1 2 2h0c1-1 1-1 2-1l-4 3h4c-1 1-2 2-3 2l1 1c1-1 2-1 4-2-1 1-2 1-2 2l-1 1h0c-1 1-2 1-3 2 2 0 3-1 4-2v1c-1 1-1 2-3 3h2c0-1 1-1 2-1l-1 1 1 1 3-1 1 1c-1 1-2 1-3 2h0l4-1v1l-1 1h0 3 0c-1 2-2 2-4 2v1h0 2c1 0 1 0 2-1v1l-2 1v1h1l-1 1c-2 0-2 0-4 1h0l-2 1h-1c1-1 2-2 3-2 1-1 1 0 1-1-1 0-2 0-3 1h0-2c-1 1-1 1-2 1v-1s1-1 2-1h1v-1h-2l-2 1v-1c1 0 1 0 1-1-1 0-1-1-2-1l1-2h-1-1c1-1 2-2 3-2-2 0-3 0-4 1 0-1 1-2 2-2l1-1h0-3 0c1-1 2-2 2-4-1 1-2 2-3 2l2-3h-2 0-2l-1 1h-3-2l1-1h-1-2l2-2v-1h-1c1-1 1-2 3-2l2-2 3-1c1-1 2-1 3-1z" class="R"></path><path d="M298 326c1-1 2-1 4-1h1 0v1l-2 1h2c1-1 2-1 3-1 0 1-1 1-2 1l-2 2h-1c1 1 2 1 4 0l1-1h1v1c-1 0-2 1-3 2v1h0-2c-1 1-1 1-2 1v-1s1-1 2-1h1v-1h-2l-2 1v-1c1 0 1 0 1-1-1 0-1-1-2-1l1-2h-1z" class="J"></path><path d="M294 309v1c-1 0-1 0-2 1h3 0l-3 3h1l2-1v1c-1 0-2 1-2 2h0l4-1h0l-2 2h-2l-1 1h-3-2l1-1h-1-2l2-2v-1h-1c1-1 1-2 3-2l2-2 3-1z" class="j"></path><path d="M275 294c1-1 2-1 4-1l2-1c-1 2-2 2-4 3h0c2 0 4-1 6-1 0 0-1 1-2 1-1 1-2 1-3 2h1l5-2v1h0v1c1 0 2-1 3-1s1 0 1-1c2-1 5-2 7-2l-1 1c-2 0-4 2-6 3l7-2s0 1-1 1l-3 3h1c0-1 1-1 2-1h0c-2 2-4 3-6 5 3-1 6-3 9-4-1 1-2 2-3 2-2 1-4 2-5 4h-1l-4 2h1 1c1-1 1-1 3-1v1l-2 2h0 2 0c0 1-1 1-1 2l3-1-2 2c-2 0-2 1-3 2h1v1l-2 2-1 1-1-1h1l-1-1h-1c-1 1-1 1-2 0 1 0 2-1 3-1v-1h-2-2l2-2h0v-1l-3 2-1-1h0 0l-8 3-3 2c0-1 6-6 7-7h0c-1 1-3 2-4 2h-1c1-1 2-2 3-2h1c1-1 4-4 4-5l-2 1h0l1-2h0l-3 1v-1c1 0 2-1 2-2-1 0-1 1-2 1 0-1 1-2 1-2l1-1h0l-2 1v-1l1-1c1 0 1-1 1-1h-1c-1 0-1 0-2-1 0 0 1-1 2-1l2-1v-1z" class="a"></path><path d="M272 310h0c2-2 4-3 7-4h0c-2 1-4 2-5 4 2-1 3-2 5-2h0c-1 1-2 2-2 3 2-1 3-2 6-2l-6 3h0l-8 3-3 2c0-1 6-6 7-7h0c-1 1-3 2-4 2h-1c1-1 2-2 3-2h1z" class="O"></path><path d="M288 295c2-1 5-2 7-2l-1 1c-2 0-4 2-6 3l7-2s0 1-1 1l-3 3h1c0-1 1-1 2-1h0c-2 2-4 3-6 5 3-1 6-3 9-4-1 1-2 2-3 2-2 1-4 2-5 4h-1-2c-1 0-3 1-5 1l1-1 2-2c-2 0-3 1-4 1 1-1 2-2 4-3h-1c-1 0-2 1-4 1l2-1c1-1 1-1 1-2h-3 0c1-2 2-2 5-3v1c1 0 2-1 3-1s1 0 1-1z" class="C"></path><path d="M295 269h2v1c2 1 1 2 3 3l-1 1h2v1l-1 1c1 0 2 0 2 1s0 1 1 2v2l1 1v1h1v1l1 1c0 1 0 2 1 3l1 1h1l-1 1 1 1h0v2h2 0c-1 1-1 1-2 3h1v-1c1 0 2 1 2 2v1c-2 0-4-1-6 0 1 1 2 1 3 1v1l7 2 6 2c1 1 4 1 5 3h4v1c1 0 2 0 3 1l-2 2v1c0 1-1 3-2 4-1 0-2 0-3 1h0l-1 1c-3 0-8 2-9 5h-1v-1c2-2 4-3 6-4 1 0 1 0 1-1h-1c-2-2-5-3-7-3 0-2-1-2-1-4l-10-10v-3h-1c1-1 2-2 2-3l-1-1v-1-2c-1-1-1-1-1-2h0v-1-2h0l-3-3c0-2-1-2 0-4-1-1-1-1-2-1v-1h0l-1-1v-1-1h-1v-1-1h-2l1-2z" class="l"></path><path d="M309 300l7 2 6 2c1 1 4 1 5 3h4v1c1 0 2 0 3 1l-2 2v1c0 1-1 3-2 4-1 0-2 0-3 1h0-1v-1l1-1-12-10c-2-2-5-3-6-5z" class="t"></path><path d="M331 308c1 0 2 0 3 1l-2 2h-3v-1l2-2z" class="c"></path><path d="M282 270h0l3-3-1-1h-1l1-2c-1 0-1 0-1-1l1-1v-1l-2-1v-1h-1l1-1h3l3 4h1 0c0 1 0 1 1 1l1 1-1 1h2c0 1 2 3 3 4l-1 2h2v1 1h1v1 1l1 1h0v1c1 0 1 0 2 1-1 2 0 2 0 4l3 3h0v2 1h0c0 1 0 1 1 2v2 1h-2-1c-2 0-4 0-6 2l-7 2c2-1 4-3 6-3l1-1c-2 0-5 1-7 2 0 1 0 1-1 1s-2 1-3 1v-1h0v-1l-5 2h-1c1-1 2-1 3-2 1 0 2-1 2-1-2 0-4 1-6 1h0c2-1 3-1 4-3l-2 1c-2 0-3 0-4 1v1l-2 1c-1 0-2 1-2 1 1 1 1 1 2 1-2 1-2 0-4 1-1-1-1-1-2 0 0-1-1-1-2-1l2-2h-1-4l1-1c2 0 3-1 4-2h0c-1 1-2 1-4 1 0-1 1-1 1-1l1-1h-1-3l-1 1-1-1 2-1c1 0 2-1 2-2h-1v-1c1-1 2-1 3-2h-4 0c1-1 2-1 2-2h-2c0 1-1 1-1 1v-1c1-1 2-1 3-1v-1c1 0 2-1 3-2h0l-2 1h-1l2-2h1l-1-1 1-1 2-1c2-1 7-3 8-5 1-1 1-1 3-2l2 2 1-1z" class="Y"></path><path d="M274 274c1-1 2-1 4-2h0l-1 1v1h2c0 1 1 2 2 3v1c-1 0-2-1-3-2v1c-1 0-2 0-3-1l1-1h0c-2 1-3 1-4 2v1h-3l1-1c1-1 2-1 4-2v-1z" class="I"></path><path d="M272 277c1-1 2-1 4-2h0l-1 1c1 1 2 1 3 1v-1c1 1 2 2 3 2 0 1-1 2-2 3l1 1h1c-1 2-4 3-6 3h0c-2 0-3 0-5 1h0c1-2 2-2 4-3 1 0 0 0 1-1l-1-1h-1l2-2v-1l-2 1c-1-1-1 0-1-1v-1z" class="D"></path><path d="M274 274v1c-2 1-3 1-4 2l-1 1h3v-1 1c0 1 0 0 1 1l2-1v1l-2 2h1l1 1c-1 1 0 1-1 1-2 1-3 1-4 3h0c0 1-2 1-3 1h0c0-1 0-1 1-2h-3v-1l2-2h0c-1 0-2 1-3 1h-1v-1c1 0 2-1 3-2h0l-2 1h-1l2-2h1l-1-1 1-1 2-1v1l6-3z" class="P"></path><path d="M263 283h1c1 0 2-1 3-1h0l-2 2v1h3c-1 1-1 1-1 2h0c1 0 3 0 3-1 2-1 3-1 5-1-2 1-3 2-5 3 3 0 6-2 9-3-1 1-3 2-4 3-1 0-2 0-3 1 2 0 4 0 6-1 0 1-4 3-5 4 2 0 3-1 5-2l1 1-6 3h1 1v1l-2 1c-1 0-2 1-2 1 1 1 1 1 2 1-2 1-2 0-4 1-1-1-1-1-2 0 0-1-1-1-2-1l2-2h-1-4l1-1c2 0 3-1 4-2h0c-1 1-2 1-4 1 0-1 1-1 1-1l1-1h-1-3l-1 1-1-1 2-1c1 0 2-1 2-2h-1v-1c1-1 2-1 3-2h-4 0c1-1 2-1 2-2h-2c0 1-1 1-1 1v-1c1-1 2-1 3-1z" class="F"></path><path d="M282 270h0l3-3-1-1h-1l1-2c-1 0-1 0-1-1l1-1v-1l-2-1v-1h-1l1-1h3l3 4h1 0c0 1 0 1 1 1l1 1-1 1h2c0 1 2 3 3 4l-1 2h2v1 1h1v1 1l1 1h0v1c1 0 1 0 2 1-1 2 0 2 0 4l3 3h0v2 1h0c0 1 0 1 1 2v2 1h-2-1c-2 0-4 0-6 2l-7 2c2-1 4-3 6-3l1-1c-2 0-5 1-7 2l1-1c1-1 3-2 4-2s1 0 1-1c-2-1-6 2-9 3 2-1 3-2 4-3 2 0 3-1 4-2h-1c-1 0-2 1-3 1v-1c1 0 2-1 3-2-1 0-1 0-2 1-1 0-2 1-4 1 1-2 3-2 4-4l-3 1h0c0-1 1-2 2-2s0 0 1-1l-5 1h0l2-2-1-1h-1v-1h-2-1c1-1 1-2 2-2v-1l-2 1v-1l2-1v-1l-2 1-1-1 2-1h-1c-1-1-1-1-2-1l1-2 1-1z" class="o"></path><path d="M282 270c2 0 3 0 5-1v-1 1h0l-4 2 1 1c1-1 3-1 5-2h2l-3 2h-3l-1 2h3c1 0 1-1 2-1h0l-3 2 1 1c1 0 2-1 4-2-1 2-4 3-5 4l1 1v-1c3-1 4-1 6-1l-1 1h0c-2 0-5 1-6 2v1h-1v-1h-2-1c1-1 1-2 2-2v-1l-2 1v-1l2-1v-1l-2 1-1-1 2-1h-1c-1-1-1-1-2-1l1-2 1-1z" class="B"></path><path d="M293 277h1c0 2-1 3-3 4l1 1s1-1 2-1h0l-2 3h1c1-1 2-2 3-2h0v1s0 1 1 1c-2 1-3 2-5 3-1 0-1 0-2 1-1 0-2 1-4 1 1-2 3-2 4-4l-3 1h0c0-1 1-2 2-2s0 0 1-1l-5 1h0l2-2-1-1v-1c1-1 4-2 6-2h0l1-1z" class="Z"></path><path d="M282 270h0l3-3-1-1h-1l1-2c-1 0-1 0-1-1l1-1v-1l-2-1v-1h-1l1-1h3l3 4h1 0c0 1 0 1 1 1l1 1-1 1h2c0 1 2 3 3 4l-1 2-2-1h-1-2c-2 1-4 1-5 2l-1-1 4-2h0v-1 1c-2 1-3 1-5 1z" class="k"></path><path d="M358 318h2c1 1 1 3 1 4 0 2-1 4 0 6 3 0 5 1 8 0l2-1v1c0 2-1 3-2 4l-2 2 2 1c-2 2-3 4-6 5-1 0-3 1-4 3h0 0l1 1h1c3 4 3 10 4 15 0 1-1 2-2 3-3 4-7 7-10 10l-17 17-1-1-1 2-1-1c0-2 1-2 1-4l-3 3c-1 1-3 2-3 3h-1c0 2-2 2-3 4h0c-1 1-2 1-3 2h-1c0-1 0-1-1-2 0-3 1-5 1-8v-6c1 0 1-1 1-1 1-1 0-4 0-6 1-2 2-5 2-7l-1-2c-1-1-2-2-4-2v-2l1-1-1-2h0-1-1-2l2-2c-1-2-2-2-3-2 1-1 0-1 0-2h-1l1-2 1-1c0 1 0 0 2 0l-1-1 1-1h0l-1 1v-2l-2-1h-1l1-2v-1l-2 1-1-1 2-1v-1c-1 1-2 1-2 1v-1l1-1v-1h0v-1l1-1c-1 0-3 0-4 1 0-2 2-2 2-3h-2v-1l2-1 1-1h-1l2-1c1 0 3-1 4 0l-1 2c1 0 2-1 3 0-1 0-3 1-4 2h1l2-1h3c1-1 2-2 2-3l-1-1c-1 0-2 0-3-1h0l1-2c-1 0-1-1-2-1l-1-1v-1h1c1-3 6-5 9-5-1 1-1 2-2 3l1 1c1-1 1 0 3-1l1 1c2 0 7 1 8 3 1 0 3-1 4-1l6-2 4-2 7-2z" class="H"></path><path d="M334 345v1l2 5c-1 1-2 1-3 2l-1-4c0-2 0-2 2-4z" class="X"></path><path d="M336 351c1 2 2 4 1 6l-1 1h1v1c-1 0-1 0-2-1s-1-2-1-3l-1-2c1-1 2-1 3-2z" class="i"></path><path d="M322 385c1-3 2-8 4-10v1l-3 15-2 2-2 2c0-3 1-5 1-8l2-2z" class="h"></path><path d="M320 387l2-2c0 1 1 2 0 4l-1 3v1l-2 2c0-3 1-5 1-8z" class="M"></path><path d="M345 341c1 5-1 11-2 15l-3 5h0v-5h-1c0-1 1-2 1-4l1-2c-1-1-1 0-1-1v-3c-1-1-1-3-1-4h2c0 1 0 1 1 2v1c1 1 1 1 2 1 1-2 0-3 0-4l1-1z" class="F"></path><path d="M346 337l2 2h0l1 4c-1 4 0 8-2 12-1 1-1 3-3 4h0c-1-1-1-1-1-3 1-4 3-10 2-15l-1-3c1 0 2-1 2-1z" class="T"></path><path d="M322 365h1c2 1 3 2 5 2-1 2-1 4-1 6l-1 2c-2 2-3 7-4 10l-2 2v-6c1 0 1-1 1-1 1-1 0-4 0-6 1-2 2-5 2-7l-1-2z" class="H"></path><path d="M322 365h1c2 1 3 2 5 2-1 2-1 4-1 6-1-3-2-4-4-6l-1-2z" class="O"></path><path d="M329 352l1-2c1-1 1-1 2-1l1 4 1 2-2 5v1c1 1 0 4 0 6l-2 6c-1 1-3 2-4 3v-1l1-2c0-2 0-4 1-6-2 0-3-1-5-2h-1c-1-1-2-2-4-2v-2l1-1c2-1 4-2 6-4 2-1 3-2 4-3v-1z" class="S"></path><path d="M319 360c2-1 4-2 6-4 2-1 3-2 4-3v4c0 3 0 7-1 10-2 0-3-1-5-2h-1c-1-1-2-2-4-2v-2l1-1z" class="X"></path><path d="M329 357c0 3 0 7-1 10-2 0-3-1-5-2 1-1 1-2 1-3 2-1 3-2 4-3v-1-1h1z" class="Q"></path><path d="M371 328c0 2-1 3-2 4l-2 2 2 1c-2 2-3 4-6 5-1 0-3 1-4 3h0 0l1 1-2 1c-1 1-2 1-3 2v3l-1 1c0 2 0 3-2 5l-3 6-6 7c-2 2-4 5-7 6-2 2-3 4-5 5v-2c0-1 1-1 1-2v-1c1 0 0-1 1 0h1v-1h1c2-4 2-7 4-10 1-1 1-1 2-1v-1l1 2h1c1 0 1 1 1 0l1-1 3-3c2-4 1-13 1-17l-1-4 3 2 2-2h1v1h1l1-1-1-2s1-1 2-1c0 0-1 1-1 2 1-1 2-1 2-2h1c1-1 1-1 2-1l3-3c1 2-1 1-1 3v1h0c2-1 4-3 5-4 1-2 2-3 3-4z" class="j"></path><path d="M352 356c0-3-1-9 1-12l-1 2c2 2 2 2 2 5 0 2 0 3-2 5z" class="C"></path><path d="M353 344h0c1 0 2 0 2-1l1-1v1h1 2 0 0l1 1-2 1c-1 1-2 1-3 2v3l-1 1c0-3 0-3-2-5l1-2z" class="R"></path><path d="M336 375c2-4 2-8 4-12l3 6c-2 2-4 5-7 6z" class="H"></path><path d="M358 318h2c1 1 1 3 1 4 0 2-1 4 0 6 3 0 5 1 8 0l2-1v1c-1 1-2 2-3 4-1 1-3 3-5 4h0v-1c0-2 2-1 1-3l-3 3c-1 0-1 0-2 1h-1c0 1-1 1-2 2 0-1 1-2 1-2-1 0-2 1-2 1l1 2-1 1h-1v-1h-1l-2 2-3-2h0c1-1 1-1 1-2s-1-1-1-2c0-2 0-2-1-4v-2s-1 0-1-1c-3-1-6 0-9-1l-1-1h-1 0l2-1c1 0 3-1 4-1l6-2 4-2 7-2z" class="f"></path><path d="M351 320l7-2 1 1v4c-3 0-6 1-9 2-1 0-2-1-3 0-1 0-2 0-2 1h-1v-2c1 0 2-1 3-2h0l4-2z" class="r"></path><path d="M341 324l6-2h0c-1 1-2 2-3 2v2h1 4 4v2c1 0 1 1 2 1h1 1c0 1 0 1 1 1 0 1 0 1-1 2 1 1 1 0 2 0v1c0 1 1 1 2 2-1 0-1 0-2 1h-1c0 1-1 1-2 2 0-1 1-2 1-2-1 0-2 1-2 1l1 2-1 1h-1v-1h-1l-2 2-3-2h0c1-1 1-1 1-2s-1-1-1-2c0-2 0-2-1-4v-2s-1 0-1-1c-3-1-6 0-9-1l-1-1h-1 0l2-1c1 0 3-1 4-1z" class="K"></path><path d="M347 331h3v2l1 1-1 2h2 1l-2 2v1h2l-2 2-3-2h0c1-1 1-1 1-2s-1-1-1-2c0-2 0-2-1-4z" class="Q"></path><path d="M341 324l6-2h0c-1 1-2 2-3 2v2h1 4 4v2c1 0 1 1 2 1h0c-2 2-4 2-5 1s-1-1 0-3h-3c-1-1-3 0-4 0l-2-3z" class="c"></path><path d="M356 329h1c0 1 0 1 1 1 0 1 0 1-1 2 1 1 1 0 2 0v1c0 1 1 1 2 2-1 0-1 0-2 1h-1c0 1-1 1-2 2 0-1 1-2 1-2-1 0-2 1-2 1-1 0-2 0-2 1h-1 0c0-1 2-2 2-3l2-2v-1h0c-1 0-2 1-3 0h-1l3-2 1-1z" class="t"></path><path d="M326 318c-1 1-1 2-2 3l1 1c1-1 1 0 3-1l1 1c2 0 7 1 8 3l-2 1h0 1l1 1c3 1 6 0 9 1 0 1 1 1 1 1v2c1 2 1 2 1 4 0 1 1 1 1 2s0 1-1 2l-2-2s-1 1-2 1l1 3-1 1c0 1 1 2 0 4-1 0-1 0-2-1v-1c-1-1-1-1-1-2h-2c0-2 0-2 1-4l-2-2-1-1h-1-3l-4 1 1-1v-1l-2 1c0-1-1-1 0-2h0c-1-3-2-3-5-4l-4-3c-1 0-1-1-2-1l-1-1v-1h1c1-3 6-5 9-5z" class="L"></path><path d="M342 345c1-2 1-2 2-3 0 0-1 0-1-1h-1v-1c1-1 1-2 2-2l1 3-1 1c0 1 1 2 0 4-1 0-1 0-2-1zm-1-15c2 0 3 0 4 1h2c1 2 1 2 1 4 0 1 1 1 1 2s0 1-1 2l-2-2h-1c1-1 1-1 1-2l-1-1v-1l-1-1h0c-1-1-2-1-3-2z" class="O"></path><path d="M319 324c6 1 11 2 16 2h0 1l1 1c3 1 6 0 9 1 0 1 1 1 1 1v2h-2c-1-1-2-1-4-1-1 0-3-1-4-1-2-1-4-1-7-2l-10-2h-1v-1z" class="Q"></path><path d="M326 318c-1 1-1 2-2 3l1 1c1-1 1 0 3-1l1 1c2 0 7 1 8 3l-2 1c-5 0-10-1-16-2l-2-1c1-3 6-5 9-5z" class="S"></path><path d="M316 323h1l2 1v1h1l10 2c2 1 3 1 4 2l2 1v2c2 0 4 0 5 2l1 1h1c-1 1-1 2-2 3h-1l-2-2-1-1h-1-3l-4 1 1-1v-1l-2 1c0-1-1-1 0-2h0c-1-3-2-3-5-4l-4-3c-1 0-1-1-2-1l-1-1v-1z" class="j"></path><defs><linearGradient id="AO" x1="363.289" y1="374.215" x2="333.196" y2="373.784" xlink:href="#B"><stop offset="0" stop-color="#4a4e49"></stop><stop offset="1" stop-color="#74706f"></stop></linearGradient></defs><path fill="url(#AO)" d="M360 344h1c3 4 3 10 4 15 0 1-1 2-2 3-3 4-7 7-10 10l-17 17-1-1-1 2-1-1c0-2 1-2 1-4l-3 3c-1 1-3 2-3 3h-1c0 2-2 2-3 4h0c-1 1-2 1-3 2h-1c0-1 0-1-1-2l2-2 2-2c1-1 2-2 2-3 1-3 4-5 6-8 2-1 3-3 5-5 3-1 5-4 7-6l6-7 3-6c2-2 2-3 2-5l1-1v-3c1-1 2-1 3-2l2-1z"></path><path d="M355 350v-3c1-1 2-1 3-2 2 2 2 2 2 4v1h-2c0 1 0 1-1 1h-1 0l-1-1z" class="b"></path><path d="M354 351l1-1 1 1c1 1 1 2 2 2s2 0 3-1c1 1 2 1 2 3s-1 3-2 5c-1-2-1-2-3-3h-3c-1 0-1 1-2 2 0 1-1 2-3 3h-1l3-6c2-2 2-3 2-5z" class="S"></path><path d="M349 362h1c2-1 3-2 3-3 1-1 1-2 2-2h3c2 1 2 1 3 3l-20 18c-2 2-6 5-7 7l-3 3c-1 1-3 2-3 3h-1c0 2-2 2-3 4h0c-1 1-2 1-3 2h-1c0-1 0-1-1-2l2-2 2-2c1-1 2-2 2-3 1-3 4-5 6-8 2-1 3-3 5-5 3-1 5-4 7-6l6-7z" class="k"></path><path d="M310 331l2-1c1 0 3-1 4 0l-1 2c1 0 2-1 3 0-1 0-3 1-4 2h1l2-1h3c1-1 2-2 2-3l-1-1c-1 0-2 0-3-1h0l1-2 4 3c3 1 4 1 5 4h0c-1 1 0 1 0 2l2-1v1l-1 1 4-1h3 1l1 1 2 2c-1 2-1 2-1 4 0 1 0 3 1 4v3c0 1 0 0 1 1l-1 2-1-1v-1c-1-1-1-2-1-4l-1 1c-1 0-2-1-3-1v-1c-2 2-2 2-2 4-1 0-1 0-2 1l-1 2v1c-1 1-2 2-4 3-2 2-4 3-6 4l-1-2h0-1-1-2l2-2c-1-2-2-2-3-2 1-1 0-1 0-2h-1l1-2 1-1c0 1 0 0 2 0l-1-1 1-1h0l-1 1v-2l-2-1h-1l1-2v-1l-2 1-1-1 2-1v-1c-1 1-2 1-2 1v-1l1-1v-1h0v-1l1-1c-1 0-3 0-4 1 0-2 2-2 2-3h-2v-1l2-1 1-1h-1z" class="R"></path><path d="M334 344c1-1 1-2 2-2s1 0 1 1l1 3-1 1c-1 0-2-1-3-1v-1-1z" class="S"></path><path d="M320 355h-2c0-2-1-2-3-2v-2l1-1h1 3v1l-2 1v1h1c1-1 2-1 3-1-1 1-2 1-2 3z" class="D"></path><path d="M320 355c1-1 4-2 5-4l6-6c1-1 2-1 3-1v1c-2 2-2 2-2 4-1 0-1 0-2 1l-1 2v1c-1 1-2 2-4 3-2 2-4 3-6 4l-1-2h0-1-1-2l2-2c-1-2-2-2-3-2 1-1 0-1 0-2h-1l1-2 1-1c0 1 0 0 2 0h0v1l-1 1v2c2 0 3 0 3 2h2z" class="l"></path><path d="M318 358c2-1 3-2 5-3 1 0 2 0 3-2 2-1 2-1 3-1v1c-1 1-2 2-4 3-2 2-4 3-6 4l-1-2z" class="h"></path><path d="M310 331l2-1c1 0 3-1 4 0l-1 2c1 0 2-1 3 0-1 0-3 1-4 2h1l2-1h3c1-1 2-2 2-3l-1-1c-1 0-2 0-3-1h0l1-2 4 3c3 1 4 1 5 4h0c-1 1 0 1 0 2l2-1v1l-1 1 4-1v2c1 1 4-1 5 1-1 1-1 2-2 2-1 1-3 1-4 2l-1 1c-1 1-3 3-5 3s-6 3-8 4c2-2 3-3 5-4v-1c-1 0-1 0-2 1s-2 1-3 2h-1l1-1 2-1v-1c-2 1-2 1-3 1l2-2c-1 0-1 1-3 0l1-1s1 0 1-1h-1c0-1 0-1 1-2h0-1v-1h1l-1-1h0l1-1v-1c-1 0-2 1-2 1h-1l-1-1c-1 1-2 1-3 2h0v-1l1-1c-1 0-3 0-4 1 0-2 2-2 2-3h-2v-1l2-1 1-1h-1z" class="B"></path><path d="M250 220c4 2 8 3 12 6 5 2 10 7 14 10 8 6 16 14 21 22l11 17c2 3 3 7 5 10 1 3 5 10 8 12v1h-7-2v-1c0-1-1-2-2-2v1h-1c1-2 1-2 2-3h0-2v-2h0l-1-1 1-1h-1l-1-1c-1-1-1-2-1-3l-1-1v-1h-1v-1l-1-1v-2c-1-1-1-1-1-2s-1-1-2-1l1-1v-1h-2l1-1c-2-1-1-2-3-3v-1h-2c-1-1-3-3-3-4h-2l1-1-1-1c-1 0-1 0-1-1h0-1l-3-4h-3l-1 1h1v1l2 1v1l-1 1c0 1 0 1 1 1l-1 2h1l1 1-3 3h0l-1 1-2-2c-2 1-2 1-3 2-1 2-6 4-8 5l-2 1-1 1 1 1h-1l-2 2h1l2-1h0c-1 1-2 2-3 2s-4 1-4 1c0-1 1-2 2-2h0c-1 0-2 0-3 1v-1l2-2c-2 0-4 2-6 3l1 1c-1 0-2 1-2 1-2 0-3 0-4 1-2 1-4 3-6 2h-1c-1 0-2 1-3 1h0l-3 1h-1l2-2c2-1 3-1 4-3-1 0-2 1-3 0l2-1-1-1h-1c1-1 1-1 1-2l-1 1c-3-2-3 0-5-1l1-1v-1l-3-1 1-1c2-3 2-5 1-9 0-1 0-2-1-3 0-1-1-1-1-2-3-4-6-4-10-5h2 2c2 1 3 1 5 3l1-1c1 1 2 1 4 1l-2-1v-1h-1-1-1l1-1 1-1c-1 0-2 0-3-1l2-1c1-1 3-1 4-2l1 1c1-1 3-2 3-3h-2v-1h-1-1c1-2 3-3 4-4 4-1 6-3 9-5-2 1-5 2-7 3-1 0-1 1-2 1h-2l6-3h-3l-7 3c2-3 5-4 8-5-3-1-7 2-10 3 2-2 4-3 7-4h-2c-2 1-4 2-6 2h0l3-2c1-1 3-1 4-2-1-1-1-1-2 0l-1-1c1-2 4-3 6-4-1-1-3 1-5 1h0c3-2 7-4 9-6 1 0 2-1 3-1s2 0 3-1h1-1c-1 0-1-1-2-1h0v-1-1h1 1v-1z" class="P"></path><path d="M251 244c1-1 2-1 3-1h1c1-1 2-2 4-2-2 2-3 3-6 5l-1-1c-1 0-2 1-2 1l-2 1h-1c1-1 3-2 4-3z" class="j"></path><path d="M249 250l1-1 1 1-2 2h2l-2 1h2l2 2-1 1 1 1c-1 0-1 0-2 1h-1c-1 0-1-1-1-1-1 0-2 1-3 1l2-3h0c-1-1 0-1 0-2h-2-1l2-1 2-2z" class="F"></path><path d="M254 248c2-1 4-3 6-4l-1-1c-1 2-3 3-4 4l-1-1c3-2 5-5 8-6v-1c2 0 3-1 5-1-1 1 0 1-1 1v1h3v1h1c2 1 3 2 5 4h-4v-2l-2 1v-2c-2 1-3 2-4 2h-1c-2 0-3 1-5 1l-1 1c-1 0-1 1-2 2s-1 1-2 0z" class="l"></path><path d="M243 241l1-1h1l2-1 7-4h0v1l5-2v1l-6 3 1 1c1-1 2-1 3-2l4-2v1c0 1-1 1-2 1l-1 1-3 2c1 0 2-1 3-2h2l1-1h3 0l-5 4c-2 0-3 1-4 2h-1c-1 0-2 0-3 1l-3 1c-1 0-2 1-4 1v-1l2-2h0c-2 1-4 3-7 2 4-1 6-3 9-5-2 1-5 2-7 3-1 0-1 1-2 1h-2l6-3z" class="M"></path><path d="M239 245c3 1 5-1 7-2h0l-2 2v1c2 0 3-1 4-1l3-1c-1 1-3 2-4 3h1l2-1v1c-1 1-2 1-3 2 1 1 1 1 2 1l-2 2-2 1h1 2c0 1-1 1 0 2h0l-2 3h-3 0c-1-1-2-1-3-2 0-1 1-1 1-2h-1 0v-2c-2 0-2 1-3 2h-2l1-1c1-1 3-2 3-3h-2v-1h-1-1c1-2 3-3 4-4z" class="n"></path><path d="M244 245v1c2 0 3-1 4-1l3-1c-1 1-3 2-4 3h1l2-1v1c-1 1-2 1-3 2 1 1 1 1 2 1l-2 2-2 1h1 2c0 1-1 1 0 2-2 1-3 2-5 2 1-1 1-2 2-2l1-1h0-2v-1-1h-2s3-2 4-2c-2-1-3 0-4 0v-1-1c-1 1-2 1-4 1 2-2 4-3 6-4z" class="e"></path><path d="M231 254c1-1 3-1 4-2l1 1-1 1h2c1-1 1-2 3-2v2h0 1c0 1-1 1-1 2 1 1 2 1 3 2h0 3c1 0 2-1 3-1 0 0 0 1 1 1h1v1h0 2l-3 2 1 1 2-2v1l-2 2 2-1v1l-2 2c1 0 2-1 3-1h0l-2 3h0 0c1-1 2-1 3-1 0 2-4 3-5 4h0l-3 1c-2 1-4 2-7 3-1 1-3 3-4 3l-2 2v-1l-3-1 1-1c2-3 2-5 1-9 0-1 0-2-1-3 0-1-1-1-1-2-3-4-6-4-10-5h2 2c2 1 3 1 5 3l1-1c1 1 2 1 4 1l-2-1v-1h-1-1-1l1-1 1-1c-1 0-2 0-3-1l2-1z" class="m"></path><path d="M237 254c1-1 1-2 3-2v2h0 1c0 1-1 1-1 2 1 1 2 1 3 2h0l1 1h1c-1 2-2 2-4 2h0l-1-1h-1c-1 1-2 1-3 1v-1h2l1-1h1l-1-1-1-1c-1-1-1-1-2-1h-1l2-1v-1h0z" class="K"></path><path d="M246 258c1 0 2-1 3-1 0 0 0 1 1 1h1v1h0 2l-3 2 1 1 2-2v1l-2 2 2-1v1l-2 2c1 0 2-1 3-1h0l-2 3h0 0c1-1 2-1 3-1 0 2-4 3-5 4h0l-3 1c-2 1-4 2-7 3-1 1-3 3-4 3l-2 2v-1l-3-1 1-1c2-3 2-5 1-9l2 2v-2h4v1h-2l1 1h1 3c2-1 5-3 6-4-2 0-3 1-4 1l2-2h-4c0-1 0-1 1-1 1-1 2-1 3-1h-6l1-1h0c2 0 3 0 4-2h-1l-1-1h3z" class="q"></path><path d="M233 267l2 2v-2h4v1h-2l1 1h1c0 2 2 2 1 3-2-1-1-2-3-3v2l-1-1c-2 2-2 4-2 5h1 1v1c5-2 9-7 14-6l-3 1c-2 1-4 2-7 3-1 1-3 3-4 3l-2 2v-1l-3-1 1-1c2-3 2-5 1-9z" class="b"></path><path d="M250 220c4 2 8 3 12 6 5 2 10 7 14 10 8 6 16 14 21 22l11 17c2 3 3 7 5 10 1 3 5 10 8 12v1h-7-2v-1c0-1-1-2-2-2v1h-1c1-2 1-2 2-3h0-2v-2h0l-1-1 1-1h-1l1-3c-1-1-1-2-3-2v-1c0-1-1-3-1-4-1-2-1-3-3-3 0-2-1-3-2-4h0c0-2 0-2-1-4-1 0-1 0-2-1l1-1h-1v-1l-2-2h0c-1-1-1-1-2-1l1-1c0-1-1-1-1-2-1-1-2-1-3-1v-2c-2-1-2-3-4-3-1-1-1-1-1-2-1-1-2-1-3-1v-1c0-1 0-1-1-1h0v-1l-1-1h-1v-2h-1c-2-1-3-2-4-3v-1c-3 0-4-3-6-3-1-1-1-1-2-1h0l-1-1c0-1-1-1-1-1h-1v-1c-1-1-2 0-3 0v-1-1h-4l-2 1 1-1h-1c-1 0-2 0-2 1-5 0-9 4-13 6h-1-2c-2 1-4 2-6 2h0l3-2c1-1 3-1 4-2-1-1-1-1-2 0l-1-1c1-2 4-3 6-4-1-1-3 1-5 1h0c3-2 7-4 9-6 1 0 2-1 3-1s2 0 3-1h1-1c-1 0-1-1-2-1h0v-1-1h1 1v-1z" class="O"></path><path d="M240 231c-1-1-3 1-5 1h0c3-2 7-4 9-6 1 0 2-1 3-1s2 0 3-1h1l4 2h2c1 1 2 2 4 2h0c-2 1-2 1-4 0l-2 1h0v-1c-1 0-2 0-3 1h-1l1-2c-1 0-2 0-3 1h-2v-1c-2 0-3 2-5 3l-2 1z" class="Q"></path><path d="M250 220c4 2 8 3 12 6 5 2 10 7 14 10 8 6 16 14 21 22l11 17c2 3 3 7 5 10 1 3 5 10 8 12v1h-7v-2c0-1 0-2-1-2l-1-1h1l-1-1s0-2-1-2v-2c-1-1-1-2-2-3 0-1-1-2-1-2-1-3-2-6-4-8-1-2-2-3-2-4-1-3-3-5-4-8-1-1-2-1-3-2 0-2-2-4-3-5-1-3-4-5-6-7l-3-3h0c-1 0-1-1-2-1v-1l-1-1c-1-1-1-2-3-3h-1v-1l-2-2v-1c-1 0-3 0-4-1-1 0-1-1-2-2-2-2-5-3-7-5h0c-2 0-3-1-4-2h-2l-4-2h-1c-1 0-1-1-2-1h0v-1-1h1 1v-1z" class="V"></path><path d="M250 220c4 2 8 3 12 6h0c2 3 6 5 8 7h-1l-1-1c-1-1-3-2-5-3 0-1-1-1-2-1-2 0-3-1-4-2h-2l-4-2h-1c-1 0-1-1-2-1h0v-1-1h1 1v-1z" class="b"></path><path d="M264 244h1c1 0 2-1 4-2v2l2-1v2h4c1 1 5 4 6 6 1 1 1 1 1 2l1 1h0l2 2c1 0 1 1 2 2l1 2v1h3c0 1-1 1-1 2-1 0-1 0-1-1h0-1l-3-4h-3l-1 1h1v1l2 1v1l-1 1c0 1 0 1 1 1l-1 2h1l1 1-3 3h0l-1 1-2-2c-2 1-2 1-3 2-1 2-6 4-8 5l-2 1-1 1 1 1h-1l-2 2h1l2-1h0c-1 1-2 2-3 2s-4 1-4 1c0-1 1-2 2-2h0c-1 0-2 0-3 1v-1l2-2c-2 0-4 2-6 3l1 1c-1 0-2 1-2 1-2 0-3 0-4 1-2 1-4 3-6 2h-1c-1 0-2 1-3 1h0l-3 1h-1l2-2c2-1 3-1 4-3-1 0-2 1-3 0l2-1-1-1h-1c1-1 1-1 1-2l-1 1c-3-2-3 0-5-1l1-1 2-2c1 0 3-2 4-3 3-1 5-2 7-3 3 0 6-1 8-2l1-1c1 0 3-1 4-1h0c2-1 2-1 3-2h-1c-1 0-1 1-2 1 0-1 1-2 2-2h-3c1-1 1-1 1-2h-1v-2h-1c-1-1-1-1-2-1s-1-2-2-2h0l1-2-1-1v-1c-1 0-1-1-2-1 0-1 1-1 1-2h-2c1-1 2-1 3-2 1 1 1 1 2 0s1-2 2-2l1-1c2 0 3-1 5-1z" class="N"></path><path d="M259 255l-1-1c-1 0-2 0-3-1l1-1v-1h-1-1l2-2 1 1h1v1c1 0 2-1 3-1h0l-2 2v1l2-1v1h-1l-1 2z" class="h"></path><path d="M259 257h3l2 2c-1 1-1 1-1 2 1 1 0 1 1 1 0 1-1 1-2 2h-3c1-1 1-1 1-2h-1v-2h-1l1-1v-2z" class="G"></path><path d="M258 260c-1-1-1-1-2-1s-1-2-2-2h0l1-2-1-1v-1c-1 0-1-1-2-1 0-1 1-1 1-2h-2c1-1 2-1 3-2 1 1 1 1 2 0l1 1h-1l-2 2h1 1v1l-1 1c1 1 2 1 3 1l1 1-2 1h1l2-1c-1 1-1 2-2 2 1 1 0 1 1 0v2l-1 1z" class="X"></path><path d="M264 244h1c1 0 2-1 4-2v2l2-1v2h4c1 1 5 4 6 6 1 1 1 1 1 2h-1c-2-1-3-3-4-3-2-1-2-2-4-3-1-1-1-1-2-1-2 0-3 1-4 3 1 0 3 1 4 2-1 1-2 1-3 2h-1v-1c-1 1-2 1-3 2h-1c0-2 2-2 3-3-1-1-2 0-3 0h-1c1-1 2-2 2-3l-3 2-1-1 5-3v-1l-2 1h-1l2-2z" class="I"></path><path d="M267 253h1c1-1 2-1 3-2-1-1-3-2-4-2 1-2 2-3 4-3 1 0 1 0 2 1 2 1 2 2 4 3 1 0 2 2 4 3h1l1 1h0l2 2c1 0 1 1 2 2l1 2v1h3c0 1-1 1-1 2-1 0-1 0-1-1h0-1l-3-4c-1 0-1-1-2-2-1 0-1-1-2-2h0c-1-1-2 0-3 0v1l-1 1-3 2c0-1 0-1-1-2h0l2-2-1-1-2 1c-1 0-2 1-4 1l5-3c-2 0-4 1-6 2v-1z" class="E"></path><path d="M264 254c1-1 2-1 3-2v1 1c2-1 4-2 6-2l-5 3c2 0 3-1 4-1l2-1 1 1-2 2h0c1 1 1 1 1 2l3-2 1-1v-1c1 0 2-1 3 0h0c1 1 1 2 2 2 1 1 1 2 2 2h-3l-1 1h1v1l2 1v1l-1 1c0 1 0 1 1 1l-1 2h1l1 1-3 3h0l-1 1-2-2c-2 1-2 1-3 2l-5 2v1c-1 0-3 1-4 1v-1h2v-1h-2c0-1-1 0-2-1l2-2c-1 0-2 0-3 1h0l3-3h0c-2 1-4 1-6 2v-1c2 0 3-1 5-2h0c1-1 2-1 2-2h-1c1-1 0-1 1-1v-1h-1-1l2-2c-1 1-2 1-3 1l2-2c-1 0-2 1-3 0 1-1 1-1 3-1v-2c-1 0-1 0-2 1l-1-1v-1h-1c0-1 0-1 1-1l1-1h-1z" class="D"></path><path d="M272 254l2-1 1 1-2 2h0c1 1 1 1 1 2h-1 0c2 0 3 0 5-1v1h0c-2 1-3 2-4 3h3c-2 1-2 2-4 3h1l1 1c-1 0-1 0-1 1l2-1v1h1 1l-2 2v1h0 0c0 1-1 1-2 2v-1c-1 0-2 0-3 1s0 1-1 1c1-2 2-2 4-3l-1-1c-1 1-1 1-2 1 0-1 1-1 2-2h0v-1c-1 1-1 1-2 1l1-2h-2l2-2v-1h-2v-1c1 0 2-1 3-1-1-1-3 0-4 0 0-1 1-1 2-2h-2v-1l3-3h0z" class="R"></path><path d="M274 258l3-2 1-1v-1c1 0 2-1 3 0h0c1 1 1 2 2 2 1 1 1 2 2 2h-3l-1 1h1v1l2 1v1l-1 1c0 1 0 1 1 1l-1 2h1l1 1-3 3h0l-1 1-2-2c-2 1-2 1-3 2l-5 2v-1l2-1h1c1-1 2-1 2-2h0 0v-1l2-2h-1-1v-1l-2 1c0-1 0-1 1-1l-1-1h-1c2-1 2-2 4-3h-3c1-1 2-2 4-3h0v-1c-2 1-3 1-5 1h0 1z" class="T"></path><path d="M277 261v-1l1 1 2-1v1l-1 1c0 1-1 2-1 2-1 0-3 0-3 1l-1-1h-1c2-1 2-2 4-3z" class="E"></path><defs><linearGradient id="AP" x1="249.618" y1="279.284" x2="258.521" y2="274.54" xlink:href="#B"><stop offset="0" stop-color="#434343"></stop><stop offset="1" stop-color="#5e5e5e"></stop></linearGradient></defs><path fill="url(#AP)" d="M260 267l-2 1c0 1-1 1-2 2 2 0 3-1 4-2 2 0 4-1 6-1-2 1-3 2-5 2v1c2-1 4-1 6-2h0l-3 3h0c1-1 2-1 3-1l-2 2c1 1 2 0 2 1h2v1h-2v1c1 0 3-1 4-1v-1l5-2c-1 2-6 4-8 5l-2 1-1 1 1 1h-1l-2 2h1l2-1h0c-1 1-2 2-3 2s-4 1-4 1c0-1 1-2 2-2h0c-1 0-2 0-3 1v-1l2-2c-2 0-4 2-6 3l1 1c-1 0-2 1-2 1-2 0-3 0-4 1-2 1-4 3-6 2h-1c-1 0-2 1-3 1h0l-3 1h-1l2-2c2-1 3-1 4-3-1 0-2 1-3 0l2-1-1-1h-1c1-1 1-1 1-2l-1 1c-3-2-3 0-5-1l1-1 2-2c1 0 3-2 4-3 3-1 5-2 7-3 3 0 6-1 8-2l1-1c1 0 3-1 4-1z"></path><path d="M247 278h0c2-1 3-1 4-2 0 1-2 2-3 2l-1 1 1 1c1-1 3-2 5-3-2 2-5 3-6 6h-1v1h-1c-1 1-2 1-3 2h-1c0 1-1 1-2 2l-3 1h-1l2-2c2-1 3-1 4-3-1 0-2 1-3 0l2-1-1-1c1-1 2-2 3-2h2c1-2 1-2 3-2z" class="b"></path><path d="M255 269c-1 1-3 2-4 3h-1l1 1 2-1c0 1-3 3-4 4h-2v2c-2 0-2 0-3 2h-2c-1 0-2 1-3 2h-1c1-1 1-1 1-2l-1 1c-3-2-3 0-5-1l1-1 2-2c1 0 3-2 4-3 3-1 5-2 7-3 3 0 6-1 8-2z" class="V"></path><defs><linearGradient id="AQ" x1="373.079" y1="309.93" x2="351.626" y2="288.294" xlink:href="#B"><stop offset="0" stop-color="#b9b8b6"></stop><stop offset="1" stop-color="#e1e0dd"></stop></linearGradient></defs><path fill="url(#AQ)" d="M378 235v3l1-1 3 7c3 8 7 16 8 25 0 3 0 5-1 7 0 5-1 8-2 12l-4 15c-2 8-6 15-7 22h-1c0-1 1-2 1-3v-4 1c-2 3-2 7-4 11 0 2-2 4-3 5l-2-1 2-2c1-1 2-2 2-4v-1l-2 1c-3 1-5 0-8 0-1-2 0-4 0-6 0-1 0-3-1-4h-2l-7 2-4 2-6 2c-1 0-3 1-4 1-1-2-6-3-8-3l-1-1c-2 1-2 0-3 1l-1-1c1-1 1-2 2-3l1-1h0c1-1 2-1 3-1 1-1 2-3 2-4v-1l2-2c-1-1-2-1-3-1v-1h-4c-1-2-4-2-5-3l-6-2-7-2v-1c-1 0-2 0-3-1 2-1 4 0 6 0h2 7v-1h1 6c2-2 6-3 8-4l3-1c0-3 0-4-1-6 1 1 2 2 3 2h2 1 0 1l3-3c1-3 2-5 3-8 1-2 2-5 3-8-1-1 0-3 0-4h0v-2c1-3 0-5 2-8 1 1 0 1 0 3h1v-6-3l1 1v-2-6c0-1 0-3 1-5h13v-1c2 0 4 0 6-1z"></path><path d="M371 288h3v2c-1 1-1 1-2 1v2 1 1h-1c-1 2-3 3-4 4s0 1-1 1l-6-6c0-1-1 0 0-1v-2c1 2 2 4 4 5h4l1-1v-1c2-2 2-4 2-6z" class="H"></path><path d="M360 291c1-2 2-4 3-5 2-1 4-1 5-1l1 1c1 0 2 1 2 2 0 2 0 4-2 6v1l-1 1h-4c-2-1-3-3-4-5z" class="L"></path><path d="M365 292h0 1 1 2v2 1h-1-1c-1 0-2 0-3-1h1v-2z" class="G"></path><path d="M365 292h-2-1v-3c1-1 2-1 3-2 1 0 2 0 3 1v1h-2l-1 3h0z" class="d"></path><path d="M351 316h0l1 1c3-1 6-4 8-7s5-8 9-10l2-1v1 2c0 1 0 2-1 3 0 2 1 2 0 4h-1-2c-2 1-3 2-3 3h0c-1 1-2 2-2 3-1 1-1 2-2 3h-2l-7 2-4 2-6 2c-1 0-3 1-4 1-1-2-6-3-8-3 4-1 8 0 11-2 2-1 4-1 6-2 0-1 1-1 1-1 2 0 1 0 2-1h2z" class="R"></path><path d="M351 320l-1-1c1-2 6-2 7-4l2-1s1-1 2-1c0-2-1-1 0-2 1 0 1-1 2-2 0-1 0 0 1-1l1-1c0-1 1-2 2-2v-1c1-1 1-1 2-1 0 1 0 1 1 2h0c0 2 1 2 0 4h-1-2c-2 1-3 2-3 3h0c-1 1-2 2-2 3-1 1-1 2-2 3h-2l-7 2z" class="D"></path><path d="M357 300c0-1-1-2-1-4h-1c0-2 0-4 1-5h0c1 5 3 7 3 12 0 2 0 3-1 5-2 3-5 5-7 8h-2c-1 1 0 1-2 1 0 0-1 0-1 1-2 1-4 1-6 2-3 2-7 1-11 2l-1-1v-1h2c4 0 10-3 13-4 4-1 6-4 8-7v-1c-1 0-2 1-3 1l2-2-1-1-2-1-1-1v-3-1l1-1 2-2v1c1-1 2-2 3-1 2 0 3 1 4 2 0 1 0 0 1 1z" class="a"></path><path d="M352 303c1 0 1 0 2-1h0c1 1 1 1 0 2s-3 3-4 3l-1-1c-1-2-1-2-1-3h4z" class="C"></path><path d="M349 297v1l-1 2c1 1 2 2 4 3h-4c0 1 0 1 1 3l-2-1-1-1v-3-1l1-1 2-2z" class="J"></path><path d="M357 300c0-1-1-2-1-4h-1c0-2 0-4 1-5h0c1 5 3 7 3 12 0 2 0 3-1 5-2 3-5 5-7 8h-2c-1 1 0 1-2 1 0 0-1 0-1 1h-1c-2 0-3 1-5 1l1-1c6-2 12-5 15-11 2-3 2-4 1-7z" class="g"></path><path d="M344 303h1l1 1 1 1 2 1 1 1-2 2c1 0 2-1 3-1v1c-2 3-4 6-8 7-3 1-9 4-13 4h-2v1c-2 1-2 0-3 1l-1-1c1-1 1-2 2-3l1-1h0c1-1 2-1 3-1 1-1 2-3 2-4v-1l2-2c-1-1-2-1-3-1v-1h-4c1 0 2 0 3-1 3 0 7 0 10-1l4-2z" class="W"></path><path d="M344 303h1l1 1 1 1 1 2c-2 1-3 3-5 3v-1s-1-1-1-2l-2 2 1 1h-1 0c-1 0-6-1-6-1-1-1-2-1-3-1v-1h-4c1 0 2 0 3-1 3 0 7 0 10-1l4-2z" class="L"></path><path d="M332 312c3 1 4 1 7 1 1-1 3-1 5-1h1c-1 1-2 2-3 2v1l1 1c-3 1-9 4-13 4h-2v1c-2 1-2 0-3 1l-1-1c1-1 1-2 2-3l1-1h0c1-1 2-1 3-1 1-1 2-3 2-4z" class="R"></path><path d="M330 316c2 0 3 0 4-1h0c1 0 2 0 3-1l1 1c-2 1-4 2-5 3-2 0-4 0-6-1 1-1 2-1 3-1z" class="B"></path><path d="M327 317h0c2 1 4 1 6 1h-4c0 1 1 1 1 2h-2v1c-2 1-2 0-3 1l-1-1c1-1 1-2 2-3l1-1z" class="E"></path><defs><linearGradient id="AR" x1="344.605" y1="300.598" x2="328.754" y2="291.723" xlink:href="#B"><stop offset="0" stop-color="#767571"></stop><stop offset="1" stop-color="#acaaa9"></stop></linearGradient></defs><path fill="url(#AR)" d="M358 242c0-1 0-3 1-5h13c-2 0-4 1-5 2-2 0-6 0-7-1l-1 1v1l2 3v2h-1l1 1c-1 0-1 0-2 1v3 8c0 1 0 1 1 2 2 4-1 11 1 15-1 5-2 9-4 13 0 1-1 2-1 3-1 1-1 3-1 5h1c0 2 1 3 1 4-1-1-1 0-1-1-1-1-2-2-4-2-1-1-2 0-3 1v-1l-2 2-1 1v1 3l-1-1h-1l-4 2c-3 1-7 1-10 1-1 1-2 1-3 1-1-2-4-2-5-3l-6-2-7-2v-1c-1 0-2 0-3-1 2-1 4 0 6 0h2 7v-1h1 6c2-2 6-3 8-4l3-1c0-3 0-4-1-6 1 1 2 2 3 2h2 1 0 1l3-3c1-3 2-5 3-8 1-2 2-5 3-8-1-1 0-3 0-4h0v-2c1-3 0-5 2-8 1 1 0 1 0 3h1v-6-3l1 1v-2-6z"></path><path d="M316 302c2 0 4-1 6 0h1 1l1 1h-2s-1 0-1 1l-6-2z" class="C"></path><path d="M344 303h-1 0l1-1c0-1 0-2 1-2l1-1c-2 0-2 0-2-1l2-5v3 2l1 1-1 1v1 3l-1-1h-1z" class="G"></path><path d="M353 283v3c-2 1-2 3-3 5-1 1-2 3-2 5h1v1l-2 2-1-1v-2-3c3-3 5-7 7-10z" class="W"></path><path d="M331 297v1c-3 1-6 3-9 4-2-1-4 0-6 0l-7-2v-1c-1 0-2 0-3-1 2-1 4 0 6 0h2 7l10-1z" class="B"></path><path d="M358 242c0-1 0-3 1-5h13c-2 0-4 1-5 2-2 0-6 0-7-1l-1 1v1l2 3v2h-1l1 1c-1 0-1 0-2 1v3 8c0 1 0 1 1 2-1 5-1 11-3 15v1c0 1 0 1-1 2l-1 1c2-11 4-20 3-31v-6z" class="E"></path><path d="M354 269h0c0 3-1 5-2 7-2 7-4 14-10 18-1 2-3 2-4 2v-1c-2 0-5 1-7 2l-10 1v-1h1 6c2-2 6-3 8-4l3-1c0-3 0-4-1-6 1 1 2 2 3 2h2 1 0 1l3-3c1-3 2-5 3-8 1-2 2-5 3-8z" class="F"></path><path d="M338 286c1 1 2 2 3 2h2 1v1h1l1 1c-1 1-3 1-4 2l-4 2-2-1 3-1c0-3 0-4-1-6z" class="L"></path><defs><linearGradient id="AS" x1="352.666" y1="296.845" x2="354.499" y2="275.203" xlink:href="#B"><stop offset="0" stop-color="#9f9f99"></stop><stop offset="1" stop-color="#c8c7c2"></stop></linearGradient></defs><path fill="url(#AS)" d="M360 260c2 4-1 11 1 15-1 5-2 9-4 13 0 1-1 2-1 3-1 1-1 3-1 5h1c0 2 1 3 1 4-1-1-1 0-1-1-1-1-2-2-4-2-1-1-2 0-3 1v-1-1h-1c0-2 1-4 2-5 1-2 1-4 3-5v-3l2-4 1-1c1-1 1-1 1-2v-1c2-4 2-10 3-15z"></path><path d="M353 286l1-2 1 1-3 6h-2c1-2 1-4 3-5z" class="g"></path><path d="M378 235v3l1-1 3 7c3 8 7 16 8 25 0 3 0 5-1 7 0 5-1 8-2 12l-4 15c-2 8-6 15-7 22h-1c0-1 1-2 1-3v-4 1c-2 3-2 7-4 11 0 2-2 4-3 5l-2-1 2-2c1-1 2-2 2-4v-1l-2 1c-3 1-5 0-8 0-1-2 0-4 0-6 0-1 0-3-1-4 1-1 1-2 2-3 0-1 1-2 2-3h0c0-1 1-2 3-3h2c-1 2-2 4-2 6 1 0 2-3 3-2v2l2-1c-1 0 0-1 0-1l2-2c0-2 0-3 1-4 1-3 5-12 3-15h-2l-2-2v-2h-3c0-1-1-2-2-2l-1-1v-1h2v-1c0-1 1-2 1-3l-3-1h0c-2 1-2 2-4 2-2-1-2-3-2-5l-1-1c-2-4 1-11-1-15-1-1-1-1-1-2v-8-3c1-1 1-1 2-1l-1-1h1v-2l-2-3v-1l1-1c1 1 5 1 7 1 1-1 3-2 5-2v-1c2 0 4 0 6-1z" class="C"></path><path d="M382 253c3 6 3 12 3 19 0 3 0 5-2 7 0-2 1-5 1-8 0-1-1-2-1-4v-8c-1-1-1-1-1-2v-1-3z" class="N"></path><path d="M375 251c1-1 2-1 3 0 0 1 1 2 2 3v1 2c1 2 0 4 1 6 0 4 0 9-1 13v-4c-1 0-1-1-2-2v-1c0-5 2-12-1-16-1-1-1-2-2-2z" class="E"></path><path d="M360 318c1-1 1-2 2-3 0-1 1-2 2-3h0c0-1 1-2 3-3h2c-1 2-2 4-2 6 1 0 2-3 3-2v2l2-1v1 4h-1v1c-1 0-2 1-2 2v1 1l2 1v-1-2l2-2v2c0 2-1 3-1 4-1 1-1 0-1 1l-2 1c-3 1-5 0-8 0-1-2 0-4 0-6 0-1 0-3-1-4z" class="h"></path><path d="M361 322v-3h1 1l-1 1v4l1 1c0-2 1-3 1-5l2-1-1 3c-1 1-1 2-1 3 1 1 1 1 3 1v-3l2 1 2 1v-1-2l2-2v2c0 2-1 3-1 4-1 1-1 0-1 1l-2 1c-3 1-5 0-8 0-1-2 0-4 0-6z" class="E"></path><path d="M385 272v1c1 1 1 3 1 4h1v-7c1 1 1 5 1 7h0l-1 1v2c-1 1-1 2-1 3v2 1c1-1 1-1 1-2s0-2 1-2v-3c1-1 1-2 1-2v-1c0 5-1 8-2 12l-4 15c-2 8-6 15-7 22h-1c0-1 1-2 1-3v-4 1c-2 3-2 7-4 11 0 2-2 4-3 5l-2-1 2-2c1-1 2-2 2-4v-1c0-1 0 0 1-1 0-1 1-2 1-4v-3-2c1-2 1-4 2-5v-1c5-10 7-20 8-32 2-2 2-4 2-7z" class="F"></path><path d="M378 235v3l1-1 3 7c3 8 7 16 8 25 0 3 0 5-1 7v1s0 1-1 2v3c-1 0-1 1-1 2s0 1-1 2v-1-2c0-1 0-2 1-3v-2l1-1h0c0-2 0-6-1-7v7h-1c0-1 0-3-1-4v-1c0-7 0-13-3-19-1-2-1-3-2-5-1-1-1-2-1-3v-1c-3-1-4 2-6 2-3 0-5 1-8 1-1 1-2 0-4 0v-1l-1-1h1v-2l-2-3v-1l1-1c1 1 5 1 7 1 1-1 3-2 5-2v-1c2 0 4 0 6-1z" class="a"></path><path d="M378 235v3c-1 2-3 5-6 6-2 1-7 2-10 2l-1-1v-2l-2-3v-1l1-1c1 1 5 1 7 1 1-1 3-2 5-2v-1c2 0 4 0 6-1z" class="B"></path><path d="M371 239l2 2c-1 1-1 1-3 2h-2v-1c1-2 1-2 3-3z" class="T"></path><path d="M359 240c3 0 4 0 6 2v1l-1 1c-1 0-2 0-3-1l-2-3z" class="k"></path><path d="M361 246v1c2 0 3 1 4 0l4 1h2l1 1c0-1 0-1 1-1 1 1 1 1 2 3 1 0 1 1 2 2 3 4 1 11 1 16v1c1 1 1 2 2 2v4c-1 3-1 7-3 10h-1 0l-2 2h-3c0-1-1-2-2-2l-1-1v-1h2v-1c0-1 1-2 1-3l-3-1h0c-2 1-2 2-4 2-2-1-2-3-2-5l-1-1c-2-4 1-11-1-15-1-1-1-1-1-2v-8-3c1-1 1-1 2-1z" class="Z"></path><path d="M364 258v-3c2-2 4-2 6-3-1 2 0 2 0 3h-1v1h2v1l-1 1h-1c0 1-1 2-1 3l-2-1c-1 0-2-1-2-2z" class="F"></path><path d="M370 252c2 1 3 1 5 3 0 2 1 4 0 7-2 0-4 0-5-1h-1c0-1 1-2 1-3l1-1v-1h-2v-1h1c0-1-1-1 0-3z" class="O"></path><path d="M361 247c2 0 3 1 4 0l4 1h2l1 1c0-1 0-1 1-1 1 1 1 1 2 3 1 0 1 1 2 2-1 0-1 1-2 2-2-2-3-2-5-3-2 1-4 1-6 3v3l-1-1c1-2 1-2 0-3 1 0 2-1 2-2 0-3-3-3-4-5z" class="S"></path><path d="M372 268c2 1 3 1 4 3v1c0 2 0 3-1 5-1 1-1 2-3 2h-1l-2-1c-2-1-3-2-3-4 0-1 0-3 1-4 1-2 3-2 5-2z" class="F"></path><path d="M375 277h0l-1-1c0-1-1-1-1-2v-2h3c0 2 0 3-1 5z" class="O"></path><path d="M372 268c1-1 2-1 3-1 1 1 2 2 3 2v1c1 1 1 2 2 2v4c-1 3-1 7-3 10h-1 0l-2 2h-3c0-1-1-2-2-2l1-1c0-1 1-3 2-4 0-1-1-1-1-2h1c2 0 2-1 3-2 1-2 1-3 1-5v-1c-1-2-2-2-4-3z" class="B"></path><path d="M234 186l17 6c9 3 20 8 29 14h0c0 1 0 1 1 2h-1l12 9c1 0 2 2 3 2 0-1 1-1 0-2 5 4 10 8 14 13l4 4c1 0 1 0 2 1 2 1 4 7 7 7h1l1 1 2 11 1-1v-5l6 16c3 8 8 16 10 24h-2c-1 0-2-1-3-2 1 2 1 3 1 6l-3 1c-2 1-6 2-8 4h-6-1c-3-2-7-9-8-12-2-3-3-7-5-10l-11-17c-5-8-13-16-21-22-4-3-9-8-14-10-4-3-8-4-12-6s-9-3-14-4h-2c-4-1-8-1-12-1l2-2h-4l-1-1h0c1-1 2-1 3-2v-1l-2 1-1-1h1c1-1 3-2 4-3l1-1c1-2 2-4 2-7l2 1h1l1 1 1-1-5-8c-2-1-5-2-7-2l2-1c1 0 2-1 3 0h1c1 1 6 1 7 0h-3 3v-1l1-1z" class="Z"></path><path d="M311 244l2 2v1h-4l2-3z" class="T"></path><path d="M307 252h2v1l-2 3h-1c-1-1-1-1-1-3 1 0 1 0 1-1h1zm3-4c2 0 2 0 4 2h0l-1 1c-1 0-2 1-3 1v-1c0-1-1-2 0-3z" class="o"></path><path d="M286 220l1 1v2h1v-2 2l-1 1h0c0 1 0 2-1 2v1h-1l-1 1-2-1c3-2 4-4 4-7z" class="S"></path><path d="M227 191l9 2h-4v1l2 2v2h2l1-1 1 1c1 1 0 2 0 3-1 1-1 0-2 0 0 0-1 1-1 2l-2-1c-1-1 0-1 0-2l-1-1-5-8z" class="Y"></path><path d="M295 217c5 4 10 8 14 13-1 0-1 0-2 1h-1l-14-14c1 0 2 2 3 2 0-1 1-1 0-2zm21 55c1-2 2-3 4-4 2 0 3 0 4 1s2 2 2 4v2c-2 1-4 2-7 3l-1-1c-1-2-1-4-2-5z" class="h"></path><path d="M318 277c1-1 2-1 2-1v-1c-1-2 1-1 1-2 1-1 0-1 1-2h1v1 1h3v2c-2 1-4 2-7 3l-1-1z" class="L"></path><path d="M308 265l1-1c1 1 0 2 1 2 1 1 2 1 2 2l1 1h0c0 1 0 2 1 3v-1h1c1-2 1-3 3-5v1c-1 1-2 2-2 3v1 1c1 1 1 3 2 5l1 1c1 1 2 2 4 2 1 1 2 0 4 1h0c-2 0-4 3-6 4-4-3-4-6-7-10-1-2-1-4-2-6l-4-4z" class="o"></path><path d="M266 207c1 0 1-1 2 0h1l1 1c1 0 2 1 3 2h1 0c1 2 1 2 1 4h-1c0 1-1 2-2 3-1 3-4 4-5 6 0 1 1 2 2 2s1 1 2 1v-3-1-2l2-2c2 0 3-1 4-2s3-1 5-1h-1l-1-1c1 0 2 0 3 1v1l2 2c-1-1-3-2-5-2s-4 2-5 4l-2 2c-1 2-1 4-1 6-5-4-10-5-15-9 3 0 4 1 7 1h1 0 1c2-1 4-2 4-3l1-1 2-4c-2-3-4-4-7-5z" class="B"></path><path d="M299 243c2-3 4-4 7-5l3 3c1 1 1 3 2 3l-2 3c-1 1-2 2-4 2-1 1-1 1-2 1-2-1-3-3-4-5-1-1 0-1 0-2z" class="F"></path><path d="M299 243c1 1 1 2 2 2h1l1-2 2 1 1-1v1h0c1 1 1 0 3 1-1 2-3 2-4 4-1 1-1 1-2 1-2-1-3-3-4-5-1-1 0-1 0-2z" class="j"></path><path d="M273 222l2-2c1-2 3-4 5-4s4 1 5 2 1 2 1 2c0 3-1 5-4 7h0l-2 1h-4c-2-2-2-3-3-6z" class="O"></path><path d="M266 207c3 1 5 2 7 5l-2 4-1 1c0 1-2 2-4 3h-1 0-1c-1-1-3-2-4-3-1-2 0-4 1-6s2-4 5-4z" class="L"></path><path d="M308 265c-1-2-1-3-1-5h1 1v1h0l1-1c2 0 2-1 3-1s0 1 2 0c1 0 0 0 2 1h0l-1 1c1 1 1 1 2 1 1-2 2-2 2-4 0-1-1-3-3-5h-1v-1c-1-1-1-1-1-3h0 0l1 2c0-2 1-2 0-3h-2l1-1c1 1 2 1 2 3 0 1 0 1 1 2h1-1v-2-1-1c1 1 1 3 2 4l-1 1 2 2c1 0 1 1 2 3h0v2c0 2 1 2 1 4 1 1 1 2 1 3v1h0c-1 1 0 1-1 1-1-1-2-1-4-1-2 1-3 2-4 4v-1-1c0-1 1-2 2-3v-1c-2 2-2 3-3 5h-1v1c-1-1-1-2-1-3h0l-1-1c0-1-1-1-2-2-1 0 0-1-1-2l-1 1z" class="s"></path><path d="M327 281l1-1c0-2 0-2-1-3v-1c1 0 1 1 1 1v-1l1-1c0 1 1 2 1 3l1 1v1l1 1c1 0 1 0 2-1 1 2 3 4 3 5l1 1c1 2 1 3 1 6l-3 1c-2 1-6 2-8 4-2-1-3-2-4-2v-1c-2-2-3-4-5-7h1c1 1 1 2 3 2v-2c0-1 1-2 1-2l1-1c1 0 1-1 2-1h1l-1-2z" class="B"></path><path d="M337 285l1 1c1 2 1 3 1 6l-3 1c-2 1-6 2-8 4-2-1-3-2-4-2 2-1 4-1 6-1 1 0 1-1 2-1s1 0 2-1h1c1 0 0 0 1-1s1-4 1-6z" class="C"></path><path d="M286 227l2 1 1-2 2 1c1-1 2-2 3-2s1 0 2 1c2 1 3 2 4 4v1l1 1-2 2c-2 2-4 3-5 6h2v1c-1 1-2 1-3 2s0 1-1 2l-1-2 2-2h0l-2-1h-1l-1 1c-3 1-3-2-5-4-1-1-3-2-4-3 1-1 3-2 3-4h1v-1-1l1-1h1z" class="B"></path><defs><linearGradient id="AT" x1="253.404" y1="206.322" x2="254.199" y2="188.373" xlink:href="#B"><stop offset="0" stop-color="#66645d"></stop><stop offset="1" stop-color="#8a8a87"></stop></linearGradient></defs><path fill="url(#AT)" d="M234 186l17 6c9 3 20 8 29 14h0c0 1 0 1 1 2h-1c-14-7-29-13-44-15l-9-2c-2-1-5-2-7-2l2-1c1 0 2-1 3 0h1c1 1 6 1 7 0h-3 3v-1l1-1z"></path><defs><linearGradient id="AU" x1="329.912" y1="249.492" x2="316.34" y2="265.708" xlink:href="#B"><stop offset="0" stop-color="#7c7b6f"></stop><stop offset="1" stop-color="#88878b"></stop></linearGradient></defs><path fill="url(#AU)" d="M306 231h1c1-1 1-1 2-1l4 4c1 0 1 0 2 1 2 1 4 7 7 7h1l1 1 2 11 1-1v-5l6 16c3 8 8 16 10 24h-2c-1 0-2-1-3-2l-1-1c0-1-2-3-3-5-5-12-10-25-17-36-3-4-7-9-11-13z"></path><path d="M313 234c1 0 1 0 2 1 2 1 4 7 7 7h1l1 1 2 11h-1c-1-3-2-7-4-9-2-4-5-7-8-11h0z" class="g"></path><path d="M327 248l6 16c3 8 8 16 10 24h-2c0-3-4-8-5-10l-11-24h1l1-1v-5z" class="C"></path><path d="M272 228c0-2 0-4 1-6 1 3 1 4 3 6h4l2-1h0l2 1v1 1h-1c0 2-2 3-3 4 1 1 3 2 4 3 2 2 2 5 5 4l1-1h1l2 1h0l-2 2 1 2 4 4c2 2 2 3 4 4v1l2 2c1 3 3 6 5 9l9 17c1 1 2 3 3 5 2 3 3 5 5 7v1c1 0 2 1 4 2h-6-1c-3-2-7-9-8-12-2-3-3-7-5-10l-11-17c-5-8-13-16-21-22 1-1 0-3 0-4-1-1-1-1 0-2-1-1-3-1-4-2z" class="d"></path><path d="M313 285v-1c0-2-2-4-2-6h0c2 2 3 5 4 7 1 3 3 5 5 8 1 1 2 2 2 4h-1c-3-2-7-9-8-12z" class="W"></path><path d="M272 228c0-2 0-4 1-6 1 3 1 4 3 6h4l2-1h0l2 1v1 1h-1c0 2-2 3-3 4 1 1 3 2 4 3 2 2 2 5 5 4l1-1h1v1c-3 0-5 1-7-1v-2h0c-2-1-3-1-4-2v-1c-1-2-3-3-4-5-1-1-3-1-4-2z" class="E"></path><path d="M238 198c2 1 3 1 4 3l1 1c0 1 0 1 2 2l3-2h2 2c3 1 4 1 6 4 0 2 0 3 3 5-1 2-2 4-1 6 1 1 3 2 4 3-3 0-4-1-7-1 5 4 10 5 15 9 1 1 3 1 4 2-1 1-1 1 0 2 0 1 1 3 0 4-4-3-9-8-14-10-4-3-8-4-12-6s-9-3-14-4h-2c-4-1-8-1-12-1l2-2h-4l-1-1h0c1-1 2-1 3-2v-1l-2 1-1-1h1c1-1 3-2 4-3l1-1c1-2 2-4 2-7l2 1h1l1 1 1-1 1 1c0 1-1 1 0 2l2 1c0-1 1-2 1-2 1 0 1 1 2 0 0-1 1-2 0-3z" class="H"></path><path d="M241 211c1 0 3-1 3 0h2c-1 2 0 1-1 2-2 0-3 1-4 0v-2zm-14-13l2 1h1l1 1v1 1h0c-1 1-1 3-2 4s-3 2-4 3h-1l1-2-1-1 1-1c1-2 2-4 2-7z" class="Z"></path><path d="M229 199h1l1 1v1l-1 1h-1v-3z" class="o"></path><path d="M243 202c0 1 0 1 2 2l3-2h2 2c-2 0-3 0-4 2-1 1-1 3-2 4v3h-2c0-1-2 0-3 0v-1c1-1 1-2 1-3l-1-1h-1-1-1-1-1-1c0-1 1-2 1-2l2-2h4 1z" class="T"></path><path d="M224 213c3 0 7 0 11 1 2 0 5 0 7 1 1 0 2 1 3 1h1c1 1 2 1 3 1 2 1 2 0 5 2h0 3c5 4 10 5 15 9 1 1 3 1 4 2-1 1-1 1 0 2 0 1 1 3 0 4-4-3-9-8-14-10-4-3-8-4-12-6s-9-3-14-4h-2c-4-1-8-1-12-1l2-2z" class="J"></path><path d="M316 358h1 1 0l1 2-1 1v2c2 0 3 1 4 2l1 2c0 2-1 5-2 7 0 2 1 5 0 6 0 0 0 1-1 1v6c0 3-1 5-1 8 1 1 1 1 1 2h1c1-1 2-1 3-2h0c1-2 3-2 3-4h1c0-1 2-2 3-3l3-3c0 2-1 2-1 4l1 1 1-2 1 1c-2 1-3 3-5 5-1 3-3 5-5 7-1 1-1 2-2 3-2 5-8 8-10 14 1-1 1-1 2-1h1l1-1 1 1h1v1l2-2-1-1 3-4v1 2c1 1 2 2 3 4l1 1 1 2c1 2 1 3 1 4-2 2-3 4-5 6-1 1-2 2-3 4v1c0 1-2 1-2 3l1 1c-3 3-7 6-8 10-1 2-3 3-4 5v2c-1 2-1 4-3 6l-3 6c-3 5-5 12-8 18l-1 2h1c1 0 1 0 2 1v-1h0 1c-1 2-2 3-2 5s-2 6-1 7c-1 2-1 3-2 5h0c-1-1-1-1 0-2v-2h-1c-2 9-2 18-1 26-1 1-1 2-2 4 1 3 2 7 4 10 1 1 1 2 2 3l1 1c0 1-1 2-2 2l-2 2h0c-1 1-1 2-1 3v1h-1c1 2 2 3 3 4 1 0 2 1 2 2-2-1-3-3-5-4l-2-2h-1c-2-1-3-5-5-7h-1c1 3 3 4 4 7h-1c-1-1-2-3-3-4 0-1-1-1-1-2s0-1-1-1c0-2 0-3-1-4s0-1-1-2l-1-1h-1-1l1-1v-1c-2-3-2-8-5-11-1-4-3-8-3-13-2-7-1-15-1-23-1-6 0-13 2-19v-1l2-10-1-1c2-6 5-15 8-21h0c1-1 2-3 3-4 2-1 3-4 4-6 2-1 4-3 6-5 0-1 1-1 1-1l1 3c1 1 1 1 0 2l-2 7c0-1 1-1 2-1 0-1 1-4 2-5l8-13v-1l4-5c2-2 4-3 5-6 1-1 2-3 4-4 0-3 0-5-1-7-1-1-1-2-1-3l1-1c2-4 3-9 4-13-2 2-2 5-3 7v-1c0-3 2-6 2-9l1-1v-2c-2 0-3 0-4-1v-3-2-1l1-1 1-4z" class="i"></path><path d="M280 468c1 2 1 3 1 6l-2 8-1 2v-2c-1-3 0-4 0-6l2-8z" class="d"></path><path d="M286 456l2-2h1l-1 2 1 1c-1 2-3 6-3 8-1 2-2 5-3 7l-1 2h-1c0-3 0-4-1-6 1-2 2-5 3-6l3-6z" class="g"></path><path d="M274 521h0 1c1 5 3 11 5 16 3 6 5 12 9 17 0-3-1-5-3-8-1-1-1-2-2-3-1-4-4-8-5-12l-1-4h0c2 6 5 13 9 18 1 1 1 3 3 4h2v1c-1 1-1 2-1 3v1h-1c1 2 2 3 3 4 1 0 2 1 2 2-2-1-3-3-5-4l-2-2c-6-10-13-21-14-33z" class="K"></path><path d="M282 474l1-2c1-2 2-5 3-7 0 2-1 4 0 6l-2 6c1 2 1 4 0 7l-1 3-1 1-4-1v-2-1l1-2 2-8h1z" class="i"></path><path d="M280 486s1 0 2-1c0-2 1-6 2-8 1 2 1 4 0 7l-1 3-1 1-4-1v-2l2 1z" class="S"></path><path d="M281 474h1c0 4-1 8-2 11v1l-2-1v-1l1-2 2-8z" class="D"></path><path d="M278 527c-1-8-5-17-2-25 0-2 1-5 1-7s0-4 1-6l2 1v1h1c1 0 0 0 1 1v8l-1 2h-2-1-1l1 2c1 0 1 0 2 1 0 5 1 11 2 16l3 9 1-2v-1-2c1 1 1 2 1 3s1 2 1 4l1-1v1c1 3 2 7 4 10 1 1 1 2 2 3l1 1c0 1-1 2-2 2l-2 2h0v-1h-2c-2-1-2-3-3-4-4-5-7-12-9-18z" class="W"></path><path d="M278 502l-1-2 2-2c1 1 1 1 3 2l-1 2h-2-1z" class="P"></path><path d="M278 504c1 0 1 0 2 1 0 5 1 11 2 16l3 9 1-2v-1-2c1 1 1 2 1 3s1 2 1 4l1-1v1c1 3 2 7 4 10 1 1 1 2 2 3l1 1c0 1-1 2-2 2l-2 2h0v-1h-2v-2l-2-2c-1-1-2-3-2-4-1-2-4-8-3-11l1 3h0v-2c-1-2-1-4-2-5 0-1-1-2-1-4s-1-5-1-8c-1-2 0-5-2-6h0l1-2-2-1 1-1z" class="Q"></path><path d="M286 528v-1-2c1 1 1 2 1 3s1 2 1 4c2 4 2 8 5 12h-1c-2-1-2-4-3-6-2-3-3-5-4-8l1-2z" class="G"></path><path d="M289 531v1c1 3 2 7 4 10 1 1 1 2 2 3l1 1c0 1-1 2-2 2l-2 2h0v-1h-2v-2l-2-2c2-1 4 0 5 0v-1c-3-4-3-8-5-12l1-1z" class="b"></path><path d="M290 547c1 0 2 0 4 1l-2 2h0v-1h-2v-2z" class="V"></path><path d="M280 435c2-1 3-4 4-6 2-1 4-3 6-5 0-1 1-1 1-1l1 3c1 1 1 1 0 2l-2 7-2 4-3 9h-3c-7 20-13 43-10 65 1 2 1 5 2 8 1 12 8 23 14 33h-1c-2-1-3-5-5-7h-1c1 3 3 4 4 7h-1c-1-1-2-3-3-4 0-1-1-1-1-2s0-1-1-1c0-2 0-3-1-4s0-1-1-2l-1-1h-1-1l1-1v-1c-2-3-2-8-5-11-1-4-3-8-3-13-2-7-1-15-1-23-1-6 0-13 2-19v-1l2-10-1-1c2-6 5-15 8-21h0c1-1 2-3 3-4z" class="v"></path><defs><linearGradient id="AV" x1="278.495" y1="429.318" x2="285.675" y2="447.615" xlink:href="#B"><stop offset="0" stop-color="#706e70"></stop><stop offset="1" stop-color="#9fa097"></stop></linearGradient></defs><path fill="url(#AV)" d="M280 435c2-1 3-4 4-6 2-1 4-3 6-5 0-1 1-1 1-1l1 3c1 1 1 1 0 2l-2 7-2 4-3 9h-3l8-20c-9 10-16 26-20 40 0 2-1 5-2 8-1 5 0 10-2 15-1-6 0-13 2-19v-1l2-10-1-1c2-6 5-15 8-21h0c1-1 2-3 3-4z"></path><path d="M316 358h1 1 0l1 2-1 1v2c2 0 3 1 4 2l1 2c0 2-1 5-2 7 0 2 1 5 0 6 0 0 0 1-1 1v6c0 3-1 5-1 8 1 1 1 1 1 2h1c1-1 2-1 3-2h0c1-2 3-2 3-4h1c0-1 2-2 3-3l3-3c0 2-1 2-1 4l1 1 1-2 1 1c-2 1-3 3-5 5-1 3-3 5-5 7-1 1-1 2-2 3-2 5-8 8-10 14 1-1 1-1 2-1h1l1-1 1 1v2 2c-1 0-2 1-3 2h-3 0c-1-1-1-1-2-1-1 1-1 1-2 3 0 1 0 1-1 2l-1-1c0 1-1 2-2 3-1 2-3 4-4 6l-5 11c-1 1-2 2-3 4 0 1-1 3-2 4l-2 3-1-1 1-2h-1l-2 2-3 6-1-1c-3 7-6 15-7 23-1 4-1 7-1 11h-1v-3c1-1 1-2 1-4 0-3 0-6 1-9 2-11 6-21 10-31l3-9 2-4c0-1 1-1 2-1 0-1 1-4 2-5l8-13v-1l4-5c2-2 4-3 5-6 1-1 2-3 4-4 0-3 0-5-1-7-1-1-1-2-1-3l1-1c2-4 3-9 4-13-2 2-2 5-3 7v-1c0-3 2-6 2-9l1-1v-2c-2 0-3 0-4-1v-3-2-1l1-1 1-4z" class="S"></path><path d="M334 385c0 2-1 2-1 4-2 1-3 3-4 4-2 4-7 8-10 11h-1l13-16 3-3z" class="P"></path><path d="M299 429l1 1h-1c-3 6-4 12-8 17-2 3-4 6-5 9l-3 6-1-1c2-5 5-10 7-15 3-6 6-12 10-17z" class="M"></path><path d="M316 358h1 1 0l1 2-1 1v2c2 0 3 1 4 2l1 2c0 2-1 5-2 7h0l-1 1v-3l-1 3c0-1 0-2-1-3v-2c-2 0-3 0-4-1v-3-2-1l1-1 1-4z" class="I"></path><path d="M316 358h1 1 0l1 2-1 1v2 1h-2l-1-2 1-4z" class="S"></path><path d="M318 370c-1 0-1-1-1-1-1-2-2 0-2-3 0 0 1-1 2-1l3 3c0 2 1 4 1 6l-1 1v-3l-1 3c0-1 0-2-1-3v-2z" class="Z"></path><path d="M299 429c3-7 8-12 12-17 0 3-1 3-2 6l-2 3c-1 1-1 1-2 3v1c2 0 2-2 4-2l-2 3c0 1-1 2-2 3-1 2-3 4-4 6l-5 11c-1 1-2 2-3 4 0 1-1 3-2 4l-2 3-1-1 1-2h-1l-2 2c1-3 3-6 5-9 4-5 5-11 8-17h1l-1-1z" class="d"></path><path d="M333 389l1 1 1-2 1 1c-2 1-3 3-5 5-1 3-3 5-5 7-1 1-1 2-2 3-2 5-8 8-10 14 1-1 1-1 2-1h1l1-1 1 1v2 2c-1 0-2 1-3 2h-3 0c-1-1-1-1-2-1-1 1-1 1-2 3 0 1 0 1-1 2l-1-1 2-3c-2 0-2 2-4 2v-1c1-2 1-2 2-3l2-3c1-3 2-3 2-6l7-8h1c3-3 8-7 10-11 1-1 2-3 4-4z" class="X"></path><path d="M317 417l1-1 1 1v2 2c-1 0-2 1-3 2h-3c1-2 2-4 4-6z" class="Y"></path><path d="M333 389l1 1 1-2 1 1c-2 1-3 3-5 5s-4 4-5 6c-2 3-6 6-9 9-2 2-3 4-4 6l-1-1h1l6-10c3-3 8-7 10-11 1-1 2-3 4-4z" class="F"></path><path d="M324 411v1 2c1 1 2 2 3 4l1 1 1 2c1 2 1 3 1 4-2 2-3 4-5 6-1 1-2 2-3 4v1c0 1-2 1-2 3l1 1c-3 3-7 6-8 10-1 2-3 3-4 5v2c-1 2-1 4-3 6l-3 6c-3 5-5 12-8 18l-1 2h1c1 0 1 0 2 1v-1h0 1c-1 2-2 3-2 5s-2 6-1 7c-1 2-1 3-2 5h0c-1-1-1-1 0-2v-2h-1c-2 9-2 18-1 26-1 1-1 2-2 4v-1l-1 1c0-2-1-3-1-4s0-2-1-3v2 1l-1 2-3-9c-1-5-2-11-2-16-1-1-1-1-2-1l-1-2h1 1 2l1-2v-8c-1-1 0-1-1-1h-1v-1h2v-2l1-1 1-3c1-3 1-5 0-7l2-6c-1-2 0-4 0-6s2-6 3-8l2-3c1-1 2-3 2-4 1-2 2-3 3-4l5-11c1-2 3-4 4-6 1-1 2-2 2-3l1 1c1-1 1-1 1-2 1-2 1-2 2-3 1 0 1 0 2 1h0 3c1-1 2-2 3-2v-2-2h1v1l2-2-1-1 3-4z" class="I"></path><path d="M319 417h1v1l2-2c1 2 2 3 3 4v3l-1 1-9 9c-3 0-4 3-6 6-1 1-3 2-4 4 2-4 5-8 7-11l3-3c1-1 0-1 1-1 2-3 2-4 3-7v-2-2z" class="a"></path><path d="M319 417h1v1l2-2c1 2 2 3 3 4-1 2-2 3-3 3l-1 1c-1-1-1-1 0-1l-2-2v-2-2z" class="K"></path><path d="M307 426l1 1c1-1 1-1 1-2 1-2 1-2 2-3 1 0 1 0 2 1 0 2-1 3-1 5-1 2-2 4-4 6 0 0-1 1-1 2h-1v-1l-1 1c0 1-2 2-3 2v1c1 0 2 0 2 1l-1 3c-1 3-4 7-7 9h-1l-3 3-1-1c1-1 2-3 2-4 1-2 2-3 3-4l5-11c1-2 3-4 4-6 1-1 2-2 2-3z" class="q"></path><path d="M303 443c-1 3-4 7-7 9h-1l3-5s1-1 1-2c1-1 3-2 4-2z" class="a"></path><path d="M324 411v1 2c1 1 2 2 3 4l1 1 1 2c1 2 1 3 1 4-2 2-3 4-5 6-1 1-2 2-3 4l-11 10c1-1 1-2 1-4 1 0 2-2 3-2h0c0-2 1-2 2-3l-1-1c-1 1-3 2-4 3h0c0-1 0-2 2-3 1 0 1-1 1-2l9-9 1-1v-3c-1-1-2-2-3-4l-1-1 3-4z" class="H"></path><path d="M324 411v1 2c1 1 2 2 3 4l1 1c-1 2 0 5-3 6l-1-1 1-1v-3c-1-1-2-2-3-4l-1-1 3-4z" class="J"></path><g class="Z"><path d="M315 433c1 0 2 0 3-1 1-2 4-3 6-5h0l1 1c-1 3-8 10-10 11h0c0-2 1-2 2-3l-1-1c-1 1-3 2-4 3h0c0-1 0-2 2-3 1 0 1-1 1-2z"></path><path d="M312 438h0c1-1 3-2 4-3l1 1c-1 1-2 1-2 3h0c-1 0-2 2-3 2 0 2 0 3-1 4l-10 14c-1 2-3 4-4 7-2 1-3 2-3 4l-1 5c-1 4-3 9-5 13-1 0-2 2-2 3-1-1 0-2 0-2 0-1-1-2-1-2h-2l1-3c1-3 1-5 0-7l2-6c-1-2 0-4 0-6s2-6 3-8l2-3 1 1 3-3h1l-1 2 1 1c-1 2-2 4-4 6h1l3-3c2-4 6-10 9-13 1-2 3-3 4-4l3-3z"></path></g><path d="M291 470c1-2 2-4 4-5l2 1c-2 1-3 2-3 4-1 0-1 1-2 1l-1-1z" class="a"></path><path d="M295 452h1l-1 2c-3 6-6 11-9 17-1-2 0-4 0-6s2-6 3-8l2-3 1 1 3-3z" class="F"></path><path d="M291 470l1 1c1 0 1-1 2-1l-1 5c-1 4-3 9-5 13-1 0-2 2-2 3-1-1 0-2 0-2 0-1-1-2-1-2 0-3 2-6 3-8l3-9z" class="I"></path><path d="M291 470l1 1v4c-2 1-2 3-2 5-1 0-2-1-2-1l3-9z" class="D"></path><path d="M312 438h0c1-1 3-2 4-3l1 1c-1 1-2 1-2 3h0c-1 0-2 2-3 2-4 5-7 9-10 13-2 3-4 8-7 10l-1-1c1-1 1-1 1-2 1-1 1-2 1-3 2-4 6-10 9-13 1-2 3-3 4-4l3-3z" class="o"></path><path d="M322 435v1c0 1-2 1-2 3l1 1c-3 3-7 6-8 10-1 2-3 3-4 5v2c-1 2-1 4-3 6l-3 6c-3 5-5 12-8 18l-1 2h1c1 0 1 0 2 1v-1h0 1c-1 2-2 3-2 5s-2 6-1 7c-1 2-1 3-2 5h0c-1-1-1-1 0-2v-2h-1c-2 9-2 18-1 26-1 1-1 2-2 4v-1l-1 1c0-2-1-3-1-4s0-2-1-3v2 1l-1 2-3-9c-1-5-2-11-2-16-1-1-1-1-2-1l-1-2h1 1 2l1-2v-8c-1-1 0-1-1-1h-1v-1h2v-2l1-1h2s1 1 1 2c0 0-1 1 0 2 0-1 1-3 2-3 2-4 4-9 5-13l1-5c0-2 1-3 3-4 1-3 3-5 4-7l10-14 11-10z" class="P"></path><path d="M309 455v2c-1 2-1 4-3 6-1-1-1-2-2-2l5-6z" class="H"></path><path d="M304 461c1 0 1 1 2 2l-3 6c-3 5-5 12-8 18l-4 1c2-10 7-19 13-27z" class="T"></path><path d="M283 487h2s1 1 1 2c0 0-1 1 0 2 0-1 1-3 2-3-1 5-3 9-4 14-1 6 0 14 1 19 0 3 0 5 1 7l-1 2-3-9c-1-5-2-11-2-16-1-1-1-1-2-1l-1-2h1 1 2l1-2v-8c-1-1 0-1-1-1h-1v-1h2v-2l1-1z" class="Y"></path><path d="M291 488l4-1-1 2h1c1 0 1 0 2 1v-1h0 1c-1 2-2 3-2 5s-2 6-1 7c-1 2-1 3-2 5h0c-1-1-1-1 0-2v-2h-1c-2 9-2 18-1 26-1 1-1 2-2 4v-1l-1-8c-2-12-1-23 2-34l1-1z" class="S"></path><path d="M291 488l4-1-1 2-2 6v-3c0-1-1-2-2-3l1-1z" class="k"></path><path d="M294 489h1c1 0 1 0 2 1v-1h0 1c-1 2-2 3-2 5s-2 6-1 7c-1 2-1 3-2 5h0c-1-1-1-1 0-2v-2h-1l1-7h-1l2-6z" class="M"></path><path d="M294 489h1c1 0 1 0 2 1v-1h0 1c-1 2-2 3-2 5v-2l-2 2c0 1 0 1-1 1h-1l2-6z" class="W"></path><path d="M369 335c1-1 3-3 3-5 2-4 2-8 4-11v-1 4c0 1-1 2-1 3h1c5 16 12 31 19 46l7 17 6 12h0c-1 1-1 1 0 3l-3 1v4c-1 1 0 5 0 6s0 1 1 1l-2 1 1 1v1h-1c-1-1-2-1-3-1-2-1-5 0-7 0-4 0-8-1-12 0h-1-1c-2 1-4 0-7 1h-6l-1 1h-1l-1 1c0 2 0 3 1 4 1 3-2 4 1 6h0c-2 1-3 2-4 3-3 4-6 7-10 10l-1 1c-2 0-3 1-4 2-1 0-1 1-2 1h-1 1c-1-1-1-2-2-3v-2-2l1-1h-2c1-1 0-1 1-2-1 1-2 1-3 2l-1-3h-1l-8 2c-2-2 0-1-1-3 1-2 2-3 3-5l2-2-1-1c-1 0-2 1-2 1h-1l1-2-1-1c0-1 0-2-1-4l-1-2-1-1c-1-2-2-3-3-4v-2-1l-3 4 1 1-2 2v-1h-1l-1-1-1 1h-1c-1 0-1 0-2 1 2-6 8-9 10-14 1-1 1-2 2-3 2-2 4-4 5-7 2-2 3-4 5-5l17-17c3-3 7-6 10-10 1-1 2-2 2-3-1-5-1-11-4-15h-1l-1-1h0 0c1-2 3-3 4-3 3-1 4-3 6-5z" class="H"></path><path d="M345 386c2-1 4-1 6-1 1 1 1 1 1 2l-1 1c-2-1-3-1-4 0-1 0-1 0-1 1-1 1-1 2-2 3l1 2 4 10v1 1l1 2 1 4h1 0l-2 2c-1-1-1 0-2-1l1-1h0c-1-1-1-1-1-2l-3-3 1-1c-1-1-1-1-2-1h-1c0-1 1-1 1-2h-1l-1-1 2-1h-2v-2h-1l1-1-1-1v-3h1l-1-1-1-1 2-1c0-1 0-1 1-2s2-1 2-3z" class="S"></path><path d="M350 408l-1-2v-1-1l-4-10-1-2c1-1 1-2 2-3 0-1 0-1 1-1 1-1 2-1 4 0l-1 1 2-1v1l-1 1c1 1 1 1 2 1l-1 2h0 1v1h-2l1 1v1c0 1 1 2 2 2v1h0l-1 1h2l-2 1v1h0l1 1c0 1 0 1-1 2v3h0-2-1z" class="v"></path><path d="M355 396l1-1c1 0 1 0 2-1h0 1l-2-2v-1h1c1 0 2-1 3-1 0 0 1 2 2 3l-2 2h1s1 0 1-1c1 0 2-1 3-1l-3 3v1c2-1 4-3 6-4l-4 5c-2 2-2 4-2 7v6c-1 0-2 1-2 1l-1 1h0c1 0 1 0 1 1h0l-2 1c1 1 1 1 2 1l-1 1 1 1h0v6h0v1l-1 1-2 1c-2 1-6 0-8 0v-1h2v-1h-3c1-1 3-1 5-2h-1l-1-1v-1c-2-3 0 0-2-2h0c0-1 2-2 2-3-1 0-1-1-2-1h-1l1-1 2-2h0-1l-1-4h1 2 0v-3c1-1 1-1 1-2l-1-1h0v-1l2-1h-2l1-1h0v-1c-1 0-2-1-2-2v-1l-1-1h2c1 1 1 2 2 2z" class="S"></path><path d="M360 426l-1-1h-1l1-1v-1h-3l2-2 3-3v6h0v1l-1 1z" class="E"></path><path d="M350 408h1 2 0v-3c1-1 1-1 1-2l-1-1h0v-1h1c1 2 0 1 1 2 1 0 1 1 1 2v1h-1l1 1h1v1h-3v1l1 1c-1 0-1 0-1 1 1 2 1 3 1 5l-1 1c1 0 2 0 3-1h2v1h-1c-1 0-1 0-2 1h-1c1 1 2 1 3 1h0c-1 0-3 2-4 1l-1-1h-3c0-1 2-2 2-3-1 0-1-1-2-1h-1l1-1 2-2h0-1l-1-4z" class="s"></path><path d="M355 396l1-1c1 0 1 0 2-1h0 1l-2-2v-1h1c1 0 2-1 3-1 0 0 1 2 2 3l-2 2h1s1 0 1-1c1 0 2-1 3-1l-3 3v1c2-1 4-3 6-4l-4 5c-2 2-2 4-2 7v6c-1 0-2 1-2 1v-1h-2l1-1 1-1c-1 0-1 0-2-1h-1c1 0 2-1 2-2h0c-1-1-1 0-1-1l-1-1c-1-1-1-1-1-2v-2h0-1c1-1 0-1 1-2h-2l2-2h-2z" class="N"></path><path d="M363 405c1-1 1-2 1-3 0-2 1-2 2-4l1 1c-1 4-1 9 0 12v7l-1 1h-1l-1 1c0 2 0 3 1 4 1 3-2 4 1 6h0c-2 1-3 2-4 3-3 4-6 7-10 10l-1 1c-2 0-3 1-4 2-1 0-1 1-2 1h-1 1c-1-1-1-2-2-3v-2-2l1-1h-2c1-1 0-1 1-2-1 1-2 1-3 2l-1-3h-1l-8 2c-2-2 0-1-1-3 1-2 2-3 3-5l2-2c2-2 4-5 6-7 0-1 0-2 1-3l1 1c0 1 0 1-1 2l1 1h-2c1 1 0 1 1 2l-1 1c1 0 1 0 1 1l1-1c1-1 2-1 4-2 1 0 2 1 3 2h3v1h-2v1c2 0 6 1 8 0l2-1 1-1v-1h0v-6h0l-1-1 1-1c-1 0-1 0-2-1l2-1h0c0-1 0-1-1-1h0l1-1s1-1 2-1v-6z" class="R"></path><path d="M361 418h0l-1-1 1-1c-1 0-1 0-2-1l2-1h0c1 1 0 1 1 2h0c0 3 1 7 1 10 0 2-1 2-1 4v3c-3 4-6 7-10 10l-1 1c-2 0-3 1-4 2 0-2 2-3 3-5l-1 1c-1-1-2-1-2-2l1-2v-1c2 0 3 0 4-1h1c0 1-1 1-1 2h1c0-1 1-2 2-2l6-6v-3l1-1s-1 0 0-1v-1h-1v-6z" class="J"></path><path d="M363 405c1-1 1-2 1-3 0-2 1-2 2-4l1 1c-1 4-1 9 0 12v7l-1 1h-1l-1 1c0 2 0 3 1 4 1 3-2 4 1 6h0c-2 1-3 2-4 3v-3c0-2 1-2 1-4 0-3-1-7-1-10h0c-1-1 0-1-1-2h0 0c0-1 0-1-1-1h0l1-1s1-1 2-1v-6z" class="G"></path><path d="M340 421c0-1 0-2 1-3l1 1c0 1 0 1-1 2l1 1h-2c1 1 0 1 1 2l-1 1c1 0 1 0 1 1l1-1h3l-1 1-1 1h2l-2 2c1 0 2 1 3 1-1 1-1 1-2 1l1 1h2v1h1v2h1l-1 1v1 1l-1 2c0 1 1 1 2 2l1-1c-1 2-3 3-3 5-1 0-1 1-2 1h-1 1c-1-1-1-2-2-3v-2-2l1-1h-2c1-1 0-1 1-2-1 1-2 1-3 2l-1-3h-1l-8 2c-2-2 0-1-1-3 1-2 2-3 3-5l2-2c2-2 4-5 6-7z" class="P"></path><path d="M338 428l2 3h0s1 0 1 1l-1 1c-2-1-2 0-4-1 0-1 0-2 1-2h0l1-2z" class="L"></path><path d="M340 421c-1 2-2 3-2 5h1 0c-1 1-1 1-1 2l-1 2h0-5l2-2c2-2 4-5 6-7z" class="e"></path><path d="M332 430h5c-1 0-1 1-1 2 2 1 2 0 4 1h0c1 1 0 1 1 1l-2 2h-1l-8 2c-2-2 0-1-1-3 1-2 2-3 3-5z" class="Q"></path><path d="M365 359l1-3h1 0c1 1 1 3 1 4 1 1 1 1 3 1h0c0 2 0 2-1 4 1 0 2 0 3-1l2 1c0 1 0 1-1 2h2c0 1 0 1 1 2h0v2l1 1 1-1v1 1l1 1-1 1h1v1 2h1v1h1v1l-1 1h0c-2 1-4 2-5 2h-3c-1 1-2 1-2 1l-1-1c2 0 3-1 4-2-2 0-3 1-5 2 0 2-2 2-3 4l-2 3h0l1-1c0 1 1 1 0 2l1 1c-1 1-2 1-3 2 0 1-1 1-1 1h-1l2-2c-1-1-2-3-2-3-1 0-2 1-3 1h-1v1l2 2h-1 0c-1 1-1 1-2 1l-1 1c-1 0-1-1-2-2v-1h-1 0l1-2c-1 0-1 0-2-1l1-1v-1l-2 1 1-1 1-1c0-1 0-1-1-2-2 0-4 0-6 1 0 2-1 2-2 3s-1 1-1 2l-2 1 1 1-1 2-1 1c0 2 0 4 1 7v2l1 1-1 1 1 1h-1l1 1h0l1 1-1 2 1 1c0 1 0 2-1 3h1v1 1-1h2l-1 2h1v1l-1 1h2v1h-1v1h2c-2 1-3 1-4 2l-1 1c0-1 0-1-1-1l1-1c-1-1 0-1-1-2h2l-1-1c1-1 1-1 1-2l-1-1c-1 1-1 2-1 3-2 2-4 5-6 7l-1-1c-1 0-2 1-2 1h-1l1-2-1-1c0-1 0-2-1-4l-1-2-1-1c-1-2-2-3-3-4v-2-1l-3 4 1 1-2 2v-1h-1l-1-1-1 1h-1c-1 0-1 0-2 1 2-6 8-9 10-14 1-1 1-2 2-3 2-2 4-4 5-7 2-2 3-4 5-5l17-17c3-3 7-6 10-10 1-1 2-2 2-3z" class="G"></path><path d="M368 383h1c0 2-2 2-3 4l-3 1c-1-1 1-2 2-3v-1h2l1-1zm-38 38c2 1 2 0 4-1s3-4 6-5v1h0l-3 3-3 3c-1 1-2 3-3 4l-1-1c0-1 0-2-1-4h1z" class="X"></path><path d="M333 405h3l-1 2h2l-4 4h1l2-1c-1 1-3 2-4 4h0c1 0 2-1 3-1-2 2-4 4-6 5v1c2 0 2-1 3-1-1 1-2 2-2 3h-1l-1-2-1-1c-1-2-2-3-3-4v-2c3-1 4-3 6-4 1 0 2-1 3-2h-2l2-1z" class="F"></path><path d="M324 412c3-1 4-3 6-4l-1 2h2c0 2-2 1-2 3l1 1c-1 1-2 3-3 4-1-2-2-3-3-4v-2z" class="V"></path><path d="M345 386c1-1 2-1 3-2l5-4v2c2-1 3-1 4-2v1l-2 2h4l-1 1v1l1 1c-1 0-1 0-1 1h1l-1 2h2v1l-2 1h-1v1l2 2h-1 0c-1 1-1 1-2 1l-1 1c-1 0-1-1-2-2v-1h-1 0l1-2c-1 0-1 0-2-1l1-1v-1l-2 1 1-1 1-1c0-1 0-1-1-2-2 0-4 0-6 1z" class="l"></path><defs><linearGradient id="AW" x1="370.472" y1="362.735" x2="358.719" y2="370.284" xlink:href="#B"><stop offset="0" stop-color="#333433"></stop><stop offset="1" stop-color="#555454"></stop></linearGradient></defs><path fill="url(#AW)" d="M368 360c1 1 1 1 3 1h0c0 2 0 2-1 4l-1 1h1v1l-1 1-1-1c-1 1-3 2-4 3 0 2-1 2-2 4-1 1-2 2-3 2l1 1c-2 1-3 1-5 2l2-2v-1l-10 6c3-3 9-6 10-10 1-2 10-8 11-12z"></path><path d="M364 370c0 2-1 2-2 4-1 1-2 2-3 2l1 1c-2 1-3 1-5 2l2-2v-1h0c0-1 1-1 1-2l6-4z" class="M"></path><path d="M327 402c7-8 14-15 21-22 3-3 6-6 9-8-1 4-7 7-10 10-3 4-7 7-9 11v1l-1 2c-1 2-4 4-6 6h1c1-1 1-1 2-1l1 1h-2l1 1h1l-2 2-2 1h2c-1 1-2 2-3 2-2 1-3 3-6 4v-1l3-3v-1h-2v-1l2-1v1h2v-1l-2-3z" class="O"></path><path d="M364 370c1-1 3-2 4-3l1 1-1 2 1 1 1-1 1 1-2 1c1 0 3 0 4-1v1l-3 2-1 1h0l1 1-4 2v1c1 0 3-1 4-2v1c-1 1-2 2-2 3l4-2c-1 1-3 2-4 4l-1 1h-2v1l-3 1v-1c1 0 1-1 2-2h-3-1l2-2h0c-1 0-2 1-3 1h0l3-3-1-1c-1 1-1 1-2 1l2-2h0-1l-1-1c1 0 2-1 3-2 1-2 2-2 2-4z" class="a"></path><path d="M364 370c1-1 3-2 4-3l1 1-1 2 1 1 1-1 1 1-2 1c1 0 3 0 4-1v1l-3 2-1 1h0c-1 1-2 1-4 2v-1l2-1v-1l-3 1h0c0-1 0-1 1-2l-3 1c1-2 2-2 2-4z" class="e"></path><path d="M370 365c1 0 2 0 3-1l2 1c0 1 0 1-1 2h2c0 1 0 1 1 2h0v2l1 1 1-1v1 1l1 1-1 1h1v1 2h1v1h1v1l-1 1h0c-2 1-4 2-5 2h-3c-1 1-2 1-2 1l-1-1c2 0 3-1 4-2-2 0-3 1-5 2h-1c1-2 3-3 4-4l-4 2c0-1 1-2 2-3v-1c-1 1-3 2-4 2v-1l4-2-1-1h0l1-1 3-2v-1c-1 1-3 1-4 1l2-1-1-1-1 1-1-1 1-2 1-1v-1h-1l1-1z" class="m"></path><path d="M370 366c1-1 1-1 2 0h1l-1 1 1 1h1c0 2 0 1-1 2 1 1 2-1 3 1-1 0-1 1-2 1h1 1v1h-1c-2 0-3 1-5 1l3-2v-1c-1 1-3 1-4 1l2-1-1-1-1 1-1-1 1-2 1-1v-1z" class="K"></path><path d="M370 374c2 0 3-1 5-1l-1 2h0 2c0 1-1 1-1 2l1 1v1h2v1c-1 1-2 1-3 2 2-1 4-1 6-1-2 1-4 2-5 2h-3c-1 1-2 1-2 1l-1-1c2 0 3-1 4-2-2 0-3 1-5 2h-1c1-2 3-3 4-4l-4 2c0-1 1-2 2-3v-1c-1 1-3 2-4 2v-1l4-2-1-1h0l1-1z" class="O"></path><path d="M365 359l1-3h1 0c1 1 1 3 1 4-1 4-10 10-11 12-3 2-6 5-9 8-7 7-14 14-21 22l2 3v1h-2v-1l-2 1v1h2v1l-3 3-3 4 1 1-2 2v-1h-1l-1-1-1 1h-1c-1 0-1 0-2 1 2-6 8-9 10-14 1-1 1-2 2-3 2-2 4-4 5-7 2-2 3-4 5-5l17-17c3-3 7-6 10-10 1-1 2-2 2-3z" class="R"></path><path d="M316 417l11-15 2 3v1h-2v-1l-2 1v1h2v1l-3 3-3 4 1 1-2 2v-1h-1l-1-1-1 1h-1z" class="n"></path><defs><linearGradient id="AX" x1="397.907" y1="361.645" x2="370.66" y2="367.548" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272727"></stop></linearGradient></defs><path fill="url(#AX)" d="M369 335c1-1 3-3 3-5 2-4 2-8 4-11v-1 4c0 1-1 2-1 3h1c5 16 12 31 19 46l7 17 6 12h0c-1 1-1 1 0 3l-3 1v4c-1 1 0 5 0 6s0 1 1 1l-2 1 1 1v1h-1c-1-1-2-1-3-1-2-1-5 0-7 0-4 0-8-1-12 0h-1-1c-2 1-4 0-7 1h-6v-7c-1-3-1-8 0-12l-1-1c-1 2-2 2-2 4 0 1 0 2-1 3 0-3 0-5 2-7l4-5c-2 1-4 3-6 4v-1l3-3c-1 0-2 1-3 1 1-1 2-1 3-2l-1-1c1-1 0-1 0-2l-1 1h0l2-3c1-2 3-2 3-4 2-1 3-2 5-2-1 1-2 2-4 2l1 1s1 0 2-1h3c1 0 3-1 5-2h0l1-1v-1h-1v-1h-1v-2-1h-1l1-1-1-1v-1-1l-1 1-1-1v-2h0c-1-1-1-1-1-2h-2c1-1 1-1 1-2l-2-1c-1 1-2 1-3 1 1-2 1-2 1-4h0c-2 0-2 0-3-1 0-1 0-3-1-4h0-1l-1 3c-1-5-1-11-4-15h-1l-1-1h0 0c1-2 3-3 4-3 3-1 4-3 6-5z"></path><path d="M367 356h1 4c0 1-1 1-2 1v1h4v1l-3 2h0c-2 0-2 0-3-1 0-1 0-3-1-4z" class="b"></path><path d="M368 347l1 2h0v2l-1 1h2l-1 2h2c0 1-2 1-3 2h-1 0c0-3-1-5-1-7l2-2z" class="K"></path><path d="M366 349l-2-7c1-1 3-2 4-3v1c0 1 0 1-1 1-1 1-1 1-1 2 0 0 1 0 2 1l1-1c0 1 0 1-1 2h3c-1 1-2 1-2 2h-1l-2 2z" class="m"></path><path d="M386 386h1v3h2l-1 1c-1 0-2 1-4 1l-1-1h-2l-1 1h-3l1-1 2-1c1 0 1 0 2-1h1l1-1 2-1z" class="U"></path><path d="M408 400h0c-1 1-1 1 0 3l-3 1h-1c0 1 0 2-1 3v-5h-5v-1l-1-1c4 0 8 1 11 0z" class="k"></path><path d="M369 335c1-1 3-3 3-5 2-4 2-8 4-11v-1 4c0 1-1 2-1 3-1 3-2 6-3 8-1 1-4 5-4 6-1 1-3 2-4 3l2 7c0 2 1 4 1 7h-1l-1 3c-1-5-1-11-4-15h-1l-1-1h0 0c1-2 3-3 4-3 3-1 4-3 6-5z" class="C"></path><path d="M369 383c2-1 3-2 5-2-1 1-2 2-4 2l1 1s1 0 2-1h3l-5 2v1c1 0 2-1 4-1h1 0l-4 2v1l2-1c0 2-1 3-3 4h1c1 0 3 0 4-1v1c-1 1-2 1-2 2 2 0 5 0 7 1h1c2 1 2 1 3 3 2 1 3 2 5 3h-14c-2-1-7-1-9-1h0l-1-1c-1 2-2 2-2 4 0 1 0 2-1 3 0-3 0-5 2-7l4-5c-2 1-4 3-6 4v-1l3-3c-1 0-2 1-3 1 1-1 2-1 3-2l-1-1c1-1 0-1 0-2l-1 1h0l2-3c1-2 3-2 3-4z" class="P"></path><path d="M367 399c0-1 1-1 1-2h1v-1c1-1 1 0 2-1h0 6c1 1 3 0 4 0l4 2h0c2 1 3 2 5 3h-14c-2-1-7-1-9-1z" class="L"></path><path d="M367 399c2 0 7 0 9 1h14 7l1 1v1h5v5c1-1 1-2 1-3h1v4c-1 1 0 5 0 6s0 1 1 1l-2 1 1 1v1h-1c-1-1-2-1-3-1-2-1-5 0-7 0-4 0-8-1-12 0h-1-1c-2 1-4 0-7 1h-6v-7c-1-3-1-8 0-12h0z" class="W"></path><path d="M371 407l4 1v1h1v-2h1l1 1h0v1c0 1 0 2-1 2s-5 1-6 0v-4z" class="G"></path><path d="M382 404c3-2 9-1 13-1 1 1 3 1 4 2 0 1 1 2 1 3l-1 1h-2l-1-1-2 1c-1-1-1-1-1-2h-1v2c-1 0-2-2-2-3l-1 2h0c-1 0-1 0-1-1l-1 1h-1v-1l-1 1h-2-3v-1c-1 0-1 0-1 1l-1 1v-1h0l-1-1h-1v2h-1v-1l-4-1h-1v-2h5l4-1h3z" class="E"></path><path d="M382 404c3-2 9-1 13-1 1 1 3 1 4 2l-17-1z" class="N"></path><path d="M390 400h7l1 1v1h5v5 5c-2-2-1-6-3-9h-5c-4 0-10-1-13 1h-3l-4 1c-2 0-3 0-5-1v-2c1 0 3 0 4-1h1 0 4c-1-1-3-1-3-1h14z" class="M"></path><path d="M390 400h7l1 1v1l-23-1h4c-1-1-3-1-3-1h14z" class="s"></path><path d="M367 399c2 0 7 0 9 1 0 0 2 0 3 1h-4 0c-2 0-5 0-6 1v5h-1v-5l-1-1v8 2l1 4 22-1c2 0 5-1 7 0h1c3 0 4 0 6 2h0l1 1v1h-1c-1-1-2-1-3-1-2-1-5 0-7 0-4 0-8-1-12 0h-1-1c-2 1-4 0-7 1h-6v-7c-1-3-1-8 0-12h0z" class="k"></path><path d="M406 415c9 6 11 17 16 26 1 1 2 3 2 5h0v3c2 2 6 6 6 9l-2 4v1c0 1-1 2-1 4h1c0-1 1-1 1-2l1-1h-1c1-2 3-3 3-4h-1v-1c1 0 2 1 2 2l5 13c-3-1-7-2-10-3h-1-2l-1 2c-1-1-2-1-3-1l1-3c-5 0-9-1-14 0l-1-1-16 3c-2 1-4 1-6 2l-6 3-8 6-5 4-2 2-3 3-3 2c-3 4-6 7-7 12l-1 2c-3 11-3 21-3 33l-2 2c-1 1-3 2-4 4h-1c-2 2-3 3-4 6-1 2 0 4-1 6 0 1-1 2-2 3 1 2 1 5 1 7v6 6l-3 3v-10c0-2-1-4 0-6h0c0-2 0-3-1-5 0-1-2-2-2-2v-2l-2-1c-2-2-6-5-6-7-2-2-5-4-7-5s-4-2-7-2h-1v-3c0 2 0 3-1 4h-1l-1-1-1-1v1l1 1h-1l-2-2-1 1 1 1c-1 1-1 0-1 2h-2l-1-1c-1-1-1-2-2-3-2-3-3-7-4-10 1-2 1-3 2-4-1-8-1-17 1-26h1v2c-1 1-1 1 0 2h0c1-2 1-3 2-5-1-1 1-5 1-7s1-3 2-5h-1 0v1c-1-1-1-1-2-1h-1l1-2c3-6 5-13 8-18l3-6c2-2 2-4 3-6v-2c1-2 3-3 4-5 1-4 5-7 8-10l-1-1c0-2 2-2 2-3v-1c1-2 2-3 3-4 2-2 3-4 5-6l1 1-1 2h1s1-1 2-1l1 1-2 2c-1 2-2 3-3 5 1 2-1 1 1 3l8-2h1l1 3c1-1 2-1 3-2-1 1 0 1-1 2h2l-1 1v2 2c1 1 1 2 2 3h-1 1c1 0 1-1 2-1 1-1 2-2 4-2l1-1c4-3 7-6 10-10 1-1 2-2 4-3h0c-3-2 0-3-1-6-1-1-1-2-1-4l1-1h1l1-1h6c3-1 5 0 7-1h1 1c4-1 8 0 12 0 2 0 5-1 7 0 1 0 2 0 3 1h1v-1l-1-1 2-1z" class="p"></path><path d="M339 475h1 1c1-2 3-5 6-6v2c-1 1-1 2-2 2-1 1-1 2-2 4h0c-2 2-4 5-5 8l-3 5c0-3 0-3 1-5 1-1 0-2 0-3l1-1h1c0-1 1-2 1-2 1-1 1-1 1-2-2 2-3 3-6 3h-1l1-1c2-1 3-3 5-4z" class="U"></path><path d="M346 449h0l1 2c0 1-1 2-1 3h2l3 2-1 1-5-1c-1 1-2 3-3 4l-3 7c-1 1 0 1-1 2 0 2-1 3-2 5v1l-1 1v2l-1 1-1 1h-1c1-6 5-14 8-20 1-1 2-3 3-5l3-6z" class="D"></path><path d="M337 450v-1c0-1 0-2 1-2l1-1c0-1 0-1 1-2h0c1 1 2 2 2 4h1c1 0 1 0 2 1h1l-3 6c-1 2-2 4-3 5 0-2 0-3 1-4 0-1 0-1-1-2-1 1-3 1-5 1v1-3l2-3z" class="b"></path><path d="M337 450v-1c0-1 0-2 1-2l1-1c0-1 0-1 1-2h0c1 1 2 2 2 4h1c1 0 1 0 2 1h1l-3 6v-3-1h-1-1v-1h-4z" class="Q"></path><path d="M342 460l2 1 2-3 2 1v1h-1c-2 1-3 2-4 4v1s1 1 2 1 1-1 2-2l1 2c-3 3-7 5-9 9-2 1-3 3-5 4l1-1v-2l1-1v-1c1-2 2-3 2-5 1-1 0-1 1-2l3-7z" class="I"></path><path d="M340 444v-1l3-3v2 2c1 1 1 2 2 3h-1 1c1 0 1-1 2-1 1-1 2-2 4-2l-4 3 5 2 7 3v1h-1l1 1h-1c-2 1-3 2-5 4l-1 1c-1 2-4 4-5 5s-1 2-2 2-2-1-2-1v-1c1-2 2-3 4-4h1v-1l-2-1-2 3-2-1c1-1 2-3 3-4l5 1 1-1-3-2h-2c0-1 1-2 1-3l-1-2h0-1c-1-1-1-1-2-1h-1c0-2-1-3-2-4h0z" class="Y"></path><path d="M340 444v-1l3-3v2 2c1 1 1 2 2 3h-1 1c1 0 1-1 2-1 1-1 2-2 4-2l-4 3 5 2 7 3v1h-1c-3-1-8-3-10-5h-1l-1 1h0-1c-1-1-1-1-2-1h-1c0-2-1-3-2-4h0z" class="L"></path><path d="M365 455l5 1c1 0 1 1 2 1 1 1 3 1 5 2l-2 2h-2c-2-1-2-1-4 0-1 1-2 1-3 2l-3 3c-2 1-5 4-7 5h-2l3-3c-2 0-4 0-5 2-1 1-2 3-3 4-2 3-9 9-10 12v1c-1 1-2 2-3 2 1-1 2-2 2-4h0c1-3 3-6 5-8h0c1-2 1-3 2-4 1 0 1-1 2-2v-2c-3 1-5 4-6 6h-1-1c2-4 6-6 9-9l7-7 3 2h0c1-1 4-3 5-3 1-1 3 0 4 0v-1l-2-2z" class="P"></path><path d="M357 468l7-6c1 0 1 1 2 1l-3 3c-2 1-5 4-7 5h-2l3-3z" class="G"></path><path d="M348 466l7-7 3 2c-5 5-9 11-15 16h0c1-2 1-3 2-4 1 0 1-1 2-2v-2c-3 1-5 4-6 6h-1-1c2-4 6-6 9-9z" class="m"></path><path d="M371 429h4l-1 1h-2l-1 1c1 1 1 1 2 1v1c2 0 3 1 5 0v1c-1 1-1 2-1 4 1-2 2-4 3-5v1c0 1-3 5-4 7v1c1 1 1 1 2 1 2-2 2-4 4-6 0-1 1 0 1 0-1 1-2 3-3 5 0 1 0 2-1 3l1 1 1-1v1l-2 3c-3 2-5 3-9 4-1 0-3 1-5 2h0l2 2v1c-1 0-3-1-4 0-1 0-4 2-5 3h0l-3-2-7 7-1-2c1-1 4-3 5-5l1-1c2-2 3-3 5-4h1l-1-1h1v-1l-7-3-5-2 4-3 1-1c4-3 7-6 10-10 1-1 2-2 4-3h0l5-1z" class="D"></path><path d="M352 449c1 0 1-1 1-2l1-2v1h1c1 1 2 1 3 1v1c0 1 0 2 1 3v1l-7-3z" class="E"></path><path d="M371 429h4l-1 1h-2l-1 1c1 1 1 1 2 1v1c2 0 3 1 5 0v1c-1 1-1 2-1 4 1-2 2-4 3-5v1c0 1-3 5-4 7v1c-1 1-1 1-2 1v-4-1c-2 1-1 3-2 5v-7-2l-1 1h-1c1-2 1-2 0-4 0 0 1-1 1-2z" class="W"></path><path d="M362 433c1-1 2-2 4-3 0 2 0 2-1 2-1 1-2 1-2 2l1 2h-1v2h-1-1c-3 2-3 7-7 7l-1 2c0 1 0 2-1 2l-5-2 4-3 1-1c4-3 7-6 10-10z" class="T"></path><path d="M371 429c0 1-1 2-1 2 1 2 1 2 0 4h0c-1 2-3 5-5 6 0 2 0 2-1 3l-2-1v1c0 1 0 0-1 1 0-2 0-4 1-6v-1h1v-2h1l-1-2c0-1 1-1 2-2 1 0 1 0 1-2h0l5-1z" class="H"></path><path d="M370 431c1 2 1 2 0 4h0c-2 0-2 1-3 3-1 0-1 1-2 1 0 1 0 1-1 1 0-2 0-4 1-6 2-2 3-2 5-3z" class="T"></path><path d="M355 459c8-5 15-12 24-14l1 1 1-1v1l-2 3c-3 2-5 3-9 4-1 0-3 1-5 2h0l2 2v1c-1 0-3-1-4 0-1 0-4 2-5 3h0l-3-2z" class="K"></path><path d="M322 435c1-2 2-3 3-4 2-2 3-4 5-6l1 1-1 2h1s1-1 2-1l1 1-2 2c-1 2-2 3-3 5 1 2-1 1 1 3l8-2h1l1 3c1-1 2-1 3-2-1 1 0 1-1 2h2l-1 1-3 3v1c-1 1-1 1-1 2l-1 1c-1 0-1 1-1 2v1l-2 3v3c-5 1-10 3-14 5h0c-2 2-10 7-11 9h2c2 1 3 2 4 3 1 2 1 4 0 5l-2 2c-1 0-2 1-3 0s-1-1 0-3h2 1l1-1c-1-1-2-2-3-2-1-1-3-1-4 0l-2-1c-1 1-2 2-2 3l-1 1c-1-1 0-3 0-4h0c0-1 1-3 1-4h-1l3-6c2-2 2-4 3-6v-2c1-2 3-3 4-5 1-4 5-7 8-10l-1-1c0-2 2-2 2-3v-1z" class="W"></path><path d="M328 451c1 0 3-1 4-2 0 3-1 3-2 5v1h-1-1c0-2 1-2 2-2l-1-2h-1z" class="M"></path><path d="M332 446c1-1 2-2 3-1 1 2 1 6 0 8v3c-5 1-10 3-14 5h0l-1-1 4-2h1l1-1c1 0 1 0 2-1h2v-1-1c1-2 2-2 2-5v-3z" class="d"></path><path d="M332 446v3c-1 1-3 2-4 2s-2 0-2 2v3h-1c-1-1-1-3-2-4-3 1-6 6-9 7-1 1-4 5-5 7 0 0-1 0-1 1-1 2 0 0-1 1l-1 2v1h0l-1-1-2 3h0c0-1 1-3 1-4h-1l3-6c2-2 2-4 3-6 2-1 4-1 6-1l6-6 1 1 3-3 4-2 1 1 2-1z" class="a"></path><path d="M309 457c2-1 4-1 6-1-4 5-8 9-10 14l-2 3h0c0-1 1-3 1-4h-1l3-6c2-2 2-4 3-6z" class="D"></path><path d="M322 435c1-2 2-3 3-4 2-2 3-4 5-6l1 1-1 2h1s1-1 2-1l1 1-2 2c-1 2-2 3-3 5 1 2-1 1 1 3l8-2h1l1 3c1-1 2-1 3-2-1 1 0 1-1 2h2l-1 1-3 3v1c-1 1-1 1-1 2l-1 1c-1 0-1 1-1 2v1l-2 3c1-2 1-6 0-8-1-1-2 0-3 1l-2 1-1-1-4 2-3 3-1-1-6 6c-2 0-4 0-6 1v-2c1-2 3-3 4-5 1-4 5-7 8-10l-1-1c0-2 2-2 2-3v-1z" class="Y"></path><path d="M338 436h1l1 3c-3 3-8 5-11 7l-4 2 1-1 1-1 7-5v-1c-1 1-1 1-1 0h-1l2-2h1l1 1c1-1 1-2 2-3z" class="B"></path><path d="M315 450c0-1 1-1 1-2 1-1 2-2 2-3l1-1c0-1 2-2 3-2 1-1 1-1 2-3h0c2-1 3-3 5-4 1 2-1 1 1 3-7 3-13 11-19 17 1-2 2-4 4-5z" class="F"></path><path d="M340 439c1-1 2-1 3-2-1 1 0 1-1 2h2l-1 1-3 3v1c-1 1-1 1-1 2l-1 1c-1 0-1 1-1 2v1l-2 3c1-2 1-6 0-8-1-1-2 0-3 1l-2 1-1-1c3-2 8-4 11-7z" class="K"></path><path d="M322 435c1-2 2-3 3-4 2-2 3-4 5-6l1 1-1 2h1s1-1 2-1l1 1-2 2c-1 2-2 3-3 5-2 1-3 3-5 4h0c-1 2-1 2-2 3-1 0-3 1-3 2l-1 1c0 1-1 2-2 3 0 1-1 1-1 2h-2c1-4 5-7 8-10l-1-1c0-2 2-2 2-3v-1z" class="M"></path><path d="M406 415c9 6 11 17 16 26 1 1 2 3 2 5h0c-2-2-3-5-4-7-2 0-3 0-4-1s-1 0-2-1h-1c-5 0-10 1-16 0h0c-2-3-8-1-11-1l-2 1h-1s-1-1-1 0c-2 2-2 4-4 6-1 0-1 0-2-1v-1c1-2 4-6 4-7v-1c-1 1-2 3-3 5 0-2 0-3 1-4v-1c-2 1-3 0-5 0v-1c-1 0-1 0-2-1l1-1h2l1-1h-4l-5 1c-3-2 0-3-1-6-1-1-1-2-1-4l1-1h1l1-1h6c3-1 5 0 7-1h1 1c4-1 8 0 12 0 2 0 5-1 7 0 1 0 2 0 3 1h1v-1l-1-1 2-1z" class="i"></path><path d="M365 419v2 1l3 3c2 1 3 0 5 1h2c4 1 8 1 12 1v1c-2 0-6-1-7 0 3 2 13 0 15 3-2 0-5-1-7-1h-12-2l1-1h-4l-5 1c-3-2 0-3-1-6-1-1-1-2-1-4l1-1zm47 13v-1c0-2-1-3-2-4h0v-1h1l1 2c1 0 1 0 1-1 3 4 5 8 7 12-2 0-3 0-4-1s-1 0-2-1h-1c-5 0-10 1-16 0h0c-2-3-8-1-11-1l-2 1h-1s-1-1-1 0c-2 2-2 4-4 6-1 0-1 0-2-1v-1c1-2 4-6 4-7 1-1 2-1 3-1 2-1 5 0 7-1h0 2c1 0 3 0 4-1v-1h0l1 1 1-1h1v2h1v-2c3 0 7 1 10 2h2z" class="h"></path><path d="M412 432v-1c0-2-1-3-2-4h0v-1h1l1 2c1 0 1 0 1-1 3 4 5 8 7 12-2 0-3 0-4-1s-1 0-2-1h-1c1 0 1 0 2-1h0l-12-1 11-1c-2-1-6 0-8-2h4 2z" class="b"></path><path d="M406 415c9 6 11 17 16 26 1 1 2 3 2 5h0c-2-2-3-5-4-7-2-4-4-8-7-12 0 1 0 1-1 1l-1-2h-1v1h0c1 1 2 2 2 4v1l-1-1c0-1 0-1-1-2s-1-1-3-2l1-1h0v-1h-8c-4-1-8 0-12 0h-2v1h3v1h-2c-4 0-8 0-12-1h-2c-2-1-3 0-5-1l-3-3v-1-2h1l1-1h6c3-1 5 0 7-1h1 1c4-1 8 0 12 0 2 0 5-1 7 0 1 0 2 0 3 1h1v-1l-1-1 2-1z" class="V"></path><path d="M365 421c5 1 10 0 15 1h-9v1c3 0 8 0 10 1h1c2 0 4 0 6 1h-2v1h3v1h-2c-4 0-8 0-12-1h-2c-2-1-3 0-5-1l-3-3v-1z" class="L"></path><path d="M367 418h6 2v1c2 1 10 0 10 1l16 1c2 0 5 0 7 1v1c-9-2-19-1-28-1-5-1-10 0-15-1v-2h1l1-1z" class="d"></path><path d="M367 418h6 2v1c2 1 10 0 10 1h-14c-2 0-4 0-5-1l1-1z" class="P"></path><path d="M406 415c9 6 11 17 16 26 1 1 2 3 2 5h0c-2-2-3-5-4-7-2-4-4-8-7-12v-1c-2 0-3-2-4-3h-1v-1c-2-1-5-1-7-1l-16-1c0-1-8 0-10-1v-1h-2c3-1 5 0 7-1h1 1c4-1 8 0 12 0 2 0 5-1 7 0 1 0 2 0 3 1h1v-1l-1-1 2-1z" class="S"></path><path d="M375 418l29 1c2 0 3 1 4 2h3c1 2 2 3 2 5-2 0-3-2-4-3h-1v-1c-2-1-5-1-7-1l-16-1c0-1-8 0-10-1v-1z" class="F"></path><path d="M369 461c2-1 2-1 4 0h2l-1 1 1 2h-1v1c1 1 1 1 3 2l-1 1c-1 1-2 2-2 3-1 2-1 3-1 5-1 1-2 1-2 3l1-1 6-3h1l-1 1h1l-8 6-5 4-2 2-3 3-3 2c-3 4-6 7-7 12l-1 2h-2 0c-2 2-2 3-4 3-1 1-1 2-2 3-1 0-3-1-3-1l-2 2-1-2-2 1c-2-1-1-3-2-5l-2 1v-2l1-1c-1 0-1 1-2 1l-4-2c2-1 3-2 5-4s3-6 4-10l1-1 3-5h0c0 2-1 3-2 4 1 0 2-1 3-2v-1c1-3 8-9 10-12 1-1 2-3 3-4 1-2 3-2 5-2l-3 3h2c2-1 5-4 7-5l3-3c1-1 2-1 3-2z" class="j"></path><path d="M341 488c0-2 1-2 2-4l1 2-1 1c1 0 2 1 2 2h0-1l-1 1h1v1c-1 0-2 0-2-1v-1l-1-1z" class="W"></path><path d="M347 481h1 0c4 0 5 1 7 3l1 2-1 1h2v1l-4 3c-2-1-5-3-6-5s0-3 0-5z" class="C"></path><path d="M348 481c4 0 5 1 7 3l1 2-1 1c-2-1-4-1-5-3-2-1-1-1-2-3z" class="Q"></path><path d="M339 486c1-1 2-2 4-2-1 2-2 2-2 4s0 3 1 5l2 1c2 0 3 0 5 1l1 1h1l-2 1c-4 1-7 2-11 3h-1c-3 1-5 1-7 2v-1c2-2 3-6 4-10l1-1 3-5h0c0 2-1 3-2 4 1 0 2-1 3-2v-1z" class="J"></path><path d="M335 496l3 4h-1c-2 0-2 0-3-1l1-3z" class="E"></path><path d="M335 496c1-1 2-2 2-3l1-1 2 2h4c2 0 3 0 5 1l1 1h1l-2 1c-4 1-7 2-11 3l-3-4z" class="I"></path><path d="M351 496c0 2-1 3-2 5l-1 1c0 1-1 2 0 3v2h0c-2 2-2 3-4 3-1 1-1 2-2 3-1 0-3-1-3-1l-2 2-1-2-2 1c-2-1-1-3-2-5l-2 1v-2l1-1c-1 0-1 1-2 1l-4-2c2-1 3-2 5-4v1c2-1 4-1 7-2h1c4-1 7-2 11-3l2-1z" class="L"></path><path d="M335 504c1-1 2-1 3 0l1-1v1h-1l-1 1v2l-1-1-2 1v-1c0-1-1-1-1-2h2z" class="V"></path><path d="M335 503c1-1 2-1 4-1 2-1 2-1 4-1l-2 2c2 1 3 1 4 1v1h-1-5l-1-1h1v-1l-1 1c-1-1-2-1-3 0v-1z" class="O"></path><path d="M338 500c4-1 7-2 11-3-1 2-1 2-2 3l-4 1c-2 0-2 0-4 1-2 0-3 0-4 1h-4 0l-1-1c2-1 4-1 7-2h1z" class="n"></path><path d="M330 501v1l1 1h0 4v1h-2c0 1 1 1 1 2-1 2-1 3 0 4s2 1 4 1v-1c1 0 3-1 5-1l1-1v1 1c-1 1-1 2-2 3-1 0-3-1-3-1l-2 2-1-2-2 1c-2-1-1-3-2-5l-2 1v-2l1-1c-1 0-1 1-2 1l-4-2c2-1 3-2 5-4z" class="P"></path><path d="M369 461c2-1 2-1 4 0h2l-1 1 1 2h-1v1c1 1 1 1 3 2l-1 1c-1 1-2 2-2 3-1 2-1 3-1 5-1 1-2 1-2 3l1-1 6-3h1l-1 1h1l-8 6-5 4-2 2-3 3h-2c-1 1-2 1-3 2h-1l3-3-1-2v-1h-2l1-1-1-2c-2-2-3-3-7-3h0-1c0-1 1-1 1-2 2-2 4-4 7-6l1-2c2-1 5-4 7-5l3-3c1-1 2-1 3-2z" class="l"></path><path d="M352 477l2-1c0 1 0 1 1 2h5c1 0 2 1 3 1l2 1-2 2c-2 0-2 0-3-2-2-1-4-1-6-1 0-1 0-1-1-2h-1z" class="J"></path><path d="M361 484c1-1 2-1 3-1h1c0-1 1-1 2-1s1-1 2-1c1-1 0-1 1 0h1v1l-5 4-2 2h-1c0-1 1-2 2-3-2 0-3-1-4-1z" class="M"></path><path d="M355 484l2-1c1 0 1 1 3 0l1 1c1 0 2 1 4 1-1 1-2 2-2 3h1l-3 3h-2c-1 1-2 1-3 2h-1l3-3-1-2v-1h-2l1-1-1-2z" class="a"></path><path d="M370 472c1-1 1-2 1-3 0 1 1 3 1 4-1 2-4 4-5 6 0 0-1 1-2 1l-2-1c-1 0-2-1-3-1h-5c-1-1-1-1-1-2l-2 1c0 1-1 2-2 3l-2-1c2-2 4-4 7-6 2 3 4 3 7 4h4v-1c1-1 2-2 4-3v-1z" class="I"></path><path d="M369 461c1 0 2 0 3 2h0c0 2 0 4-1 6 0 1 0 2-1 3v1c-2 1-3 2-4 3v1h-4c-3-1-5-1-7-4l1-2c2-1 5-4 7-5l3-3c1-1 2-1 3-2z" class="T"></path><path d="M356 471c2-1 5-4 7-5v1c-1 2-4 4-4 6l3 1-1 1c2 0 3 0 4 1 1-1 2-1 2-2h-2 0c1-1 2-1 3-1 1-1 1-1 2-1v1c-2 1-3 2-4 3v1h-4c-3-1-5-1-7-4l1-2z" class="E"></path><path d="M386 436c3 0 9-2 11 1h0c6 1 11 0 16 0h1c1 1 1 0 2 1s2 1 4 1c1 2 2 5 4 7v3c2 2 6 6 6 9l-2 4v1c0 1-1 2-1 4h1c0-1 1-1 1-2l1-1h-1c1-2 3-3 3-4h-1v-1c1 0 2 1 2 2l5 13c-3-1-7-2-10-3h-1-2l-1 2c-1-1-2-1-3-1l1-3c-5 0-9-1-14 0l-1-1-16 3c-2 1-4 1-6 2l-6 3h-1l1-1h-1l-6 3-1 1c0-2 1-2 2-3 0-2 0-3 1-5 0-1 1-2 2-3l1-1c-2-1-2-1-3-2v-1h1l-1-2 1-1 2-2c-2-1-4-1-5-2-1 0-1-1-2-1l-5-1h0c2-1 4-2 5-2 4-1 6-2 9-4l2-3v-1l-1 1-1-1c1-1 1-2 1-3 1-2 2-4 3-5h1l2-1z" class="Z"></path><path d="M412 446c0-1-1-1-1-2h2l3 2v1l1 1v1 1l-1-1h-1c-1-1-2-3-3-3zm1 8l1 1-1 1c-2 0-4-1-5 0v5c-2 0-2 0-4-1v-3l1 2h0c1-1 1-2 1-3l1-1 1 1c0-1 0-1 1-2h3 1z" class="B"></path><path d="M416 446l2-1c3 1 6 5 7 7v1c0 1-1 1-1 1-3-2-3-4-7-6l-1-1v-1z" class="g"></path><path d="M412 446c1 0 2 2 3 3 1 3 2 5 2 8-1 0-2 0-3-1h-1l1-1-1-1c-1-2-2-4-4-5h0l-1-1 1-1c1 1 2 1 3 2v-3z" class="s"></path><path d="M397 437c6 1 11 0 16 0h1c1 1 1 0 2 1s2 1 4 1c1 2 2 5 4 7v3c-1-2-3-4-6-6-5-2-13-4-19-2h-1c0-1 0-3-1-4z" class="f"></path><path d="M399 441h1 3c1 0 3 0 4 1s2 1 2 2v1c-1 0-1-1-2-1-3-1-9 0-11 1-3 2-4 4-7 5l-1 1c-2 1-2-2-4 0-1-1-1 0 0-1 0-1 1-2 1-3l1-2c2-2 9-4 12-4h1z" class="o"></path><defs><linearGradient id="AY" x1="394.386" y1="435.38" x2="382.594" y2="447.106" xlink:href="#B"><stop offset="0" stop-color="#212120"></stop><stop offset="1" stop-color="#3a3a3a"></stop></linearGradient></defs><path fill="url(#AY)" d="M386 436c3 0 9-2 11 1h0c1 1 1 3 1 4-3 0-10 2-12 4-3 1-5 3-7 4l2-3v-1l-1 1-1-1c1-1 1-2 1-3 1-2 2-4 3-5h1l2-1z"></path><path d="M383 437h1l2-1c-1 1-1 3-1 5-2 1-3 1-5 1 1-2 2-4 3-5z" class="V"></path><path d="M394 458l1 1c1-2 1-3 1-5h1c0 1-1 4 0 5 1-1 1-3 2-5v-2-3h1v3c-1 1-1 5-1 7h1c1-1 0-1 1-2h1 0l1 1v-4l1-2v5 3c2 1 2 1 4 1v2c-1 1-1 2-1 4 1 0 3 0 4 1h-4l-16 3c-3-1-4 0-6 0v-1c1-1 3-2 5-2 1 0 1 0 2-1l1-1v1c3 0 8 0 10-2v-2l-1-1v3h-1v-2l-1-1v3h-1v-3c-1 1-1 1-1 3h-1v-2l-1 1c0 1 0 1-1 2v-1-1-1c-1 1-1 1-2 1 0-2 0-3 1-5v-1z" class="E"></path><path d="M417 448c4 2 4 4 7 6 0 0 1 0 1-1 1 2 2 4 3 7h0l-1 1 1 1v1c0 1-1 2-1 4h1c0-1 1-1 1-2l1-1h-1c1-2 3-3 3-4h-1v-1c1 0 2 1 2 2l5 13c-3-1-7-2-10-3h-1-2l-1 2c-1-1-2-1-3-1l1-3c-5 0-9-1-14 0l-1-1h4c-1-1-3-1-4-1 0-2 0-3 1-4v-2-5c1-1 3 0 5 0h1c1 1 2 1 3 1 0-3-1-5-2-8h1l1 1v-1-1z" class="C"></path><path d="M422 469c1 1 3 1 5 2h-2l-1 2c-1-1-2-1-3-1l1-3z" class="u"></path><path d="M408 461v-5c1-1 3 0 5 0v7h0l-1 1-1 1h-3l-1-1 1-1h0v-2z" class="J"></path><path d="M414 459v1c1 1 2 1 3 0l1 1c1-1 1-1 1-2l1-5c0 1 1 2 1 3s0 2 1 3 1 3 1 5c1 0 1 0 2-1l-1 1-1 1c-1-1-1-2-2-3 0 1 0 1-1 1v-4c-1 1-1 3-1 5l-1-1c-1 1-2 1-4 1l-1-2v-7h1v3z" class="R"></path><path d="M417 448c4 2 4 4 7 6 1 3 1 6 0 9l1 1c-1 1-1 1-2 1 0-2 0-4-1-5s-1-2-1-3-1-2-1-3l-1 5c0 1 0 1-1 2l-1-1c-1 1-2 1-3 0v-1-3c1 1 2 1 3 1 0-3-1-5-2-8h1l1 1v-1-1z" class="Y"></path><path d="M414 456c1 1 2 1 3 1v2c-1 1-2 1-3 0v-3z" class="B"></path><path d="M379 449c2-1 4-3 7-4l-1 2c-2 2-3 3-3 6 1 0 1 0 2-1 1 1 2 1 3 2l1-1v3c1 1 1 2 2 3 0-1 0-1 1-2 1 2 0 2 1 3 1-1 1-5 2-6v-1c1 2 0 4 0 5v1c-1 2-1 3-1 5 1 0 1 0 2-1v1 1 1c1-1 1-1 1-2l1-1v2h1c0-2 0-2 1-3v3h1v-3l1 1v2h1v-3l1 1v2c-2 2-7 2-10 2v-1l-1 1c-1 1-1 1-2 1-2 0-4 1-5 2v1c2 0 3-1 6 0-2 1-4 1-6 2l-6 3h-1l1-1h-1l-6 3-1 1c0-2 1-2 2-3 0-2 0-3 1-5 0-1 1-2 2-3l1-1c-2-1-2-1-3-2v-1h1l-1-2 1-1 2-2c-2-1-4-1-5-2-1 0-1-1-2-1l-5-1h0c2-1 4-2 5-2 4-1 6-2 9-4z" class="C"></path><path d="M387 457l1-1c1 1 1 2 2 3l-1 5h0l-1-1-2 2v-2c1-2 1-4 1-6z" class="E"></path><path d="M374 471l1 1c2 0 1-1 2-1h2c3 0 3-1 5-3l1 1-3 3h1 0c1 0 2 0 2 1l-6 3h-1l1-1h-1l-6 3-1 1c0-2 1-2 2-3 0-2 0-3 1-5zm5-22c2-1 4-3 7-4l-1 2c-2 2-3 3-3 6 1 0 1 0 2-1 1 1 2 1 3 2l1-1v3l-1 1c0 2 0 4-1 6v2c-2 0-2 0-3 1s-2 1-3 1l-3-3 1-1 2-1v-1c-1-1-2-2-3-2-2-1-4-1-5-2-1 0-1-1-2-1l-5-1h0c2-1 4-2 5-2 4-1 6-2 9-4z" class="J"></path><path d="M365 455h0c2-1 4-2 5-2 2 0 3 0 4 1l1 1-2 1v1h7v3-1c1-1 1-1 2-1 1 1 1 1 2 3h0c-1 1-1 1-1 2-1 0-1-1-2 0l-1-1v-1c-1-1-2-2-3-2-2-1-4-1-5-2-1 0-1-1-2-1l-5-1z" class="Y"></path><path d="M379 449c2-1 4-3 7-4l-1 2c-2 2-3 3-3 6 1 0 1 0 2-1 1 1 2 1 3 2l1-1v3l-1 1c0-1-1-1-2-2-1 1-3 2-5 3v-1h-7v-1l2-1-1-1c-1-1-2-1-4-1 4-1 6-2 9-4z" class="C"></path><path d="M375 455c1-1 4-2 6-1h2c1 0 1 1 2 1-1 1-3 2-5 3v-1h-7v-1l2-1z" class="W"></path><path d="M303 469h1c0 1-1 3-1 4h0c0 1-1 3 0 4l1-1c0-1 1-2 2-3l2 1c1-1 3-1 4 0 1 0 2 1 3 2l-1 1h-1-2c-1 2-1 2 0 3s2 0 3 0l2-2 2 1v3c3-1 5-1 7 1 3 2 5 4 6 7 1 1 2 1 3 1-1 4-2 8-4 10s-3 3-5 4l4 2c1 0 1-1 2-1l-1 1v2l2-1c1 2 0 4 2 5l2-1 1 2 2-2s2 1 3 1c1-1 1-2 2-3 2 0 2-1 4-3h0 2c-3 11-3 21-3 33l-2 2c-1 1-3 2-4 4h-1c-2 2-3 3-4 6-1 2 0 4-1 6 0 1-1 2-2 3 1 2 1 5 1 7v6 6l-3 3v-10c0-2-1-4 0-6h0c0-2 0-3-1-5 0-1-2-2-2-2v-2l-2-1c-2-2-6-5-6-7-2-2-5-4-7-5s-4-2-7-2h-1v-3c0 2 0 3-1 4h-1l-1-1-1-1v1l1 1h-1l-2-2-1 1 1 1c-1 1-1 0-1 2h-2l-1-1c-1-1-1-2-2-3-2-3-3-7-4-10 1-2 1-3 2-4-1-8-1-17 1-26h1v2c-1 1-1 1 0 2h0c1-2 1-3 2-5-1-1 1-5 1-7s1-3 2-5h-1 0v1c-1-1-1-1-2-1h-1l1-2c3-6 5-13 8-18z" class="p"></path><path d="M298 489c2-1 2-7 4-10v2l-4 11c2 0 5-1 7 1v1c-1 0-2 1-3 1l1 2c-2 2-4 4-6 7 0-2 1-3 0-4-1 0-1 1-2 2v-1h0c-1-1 1-5 1-7s1-3 2-5z" class="B"></path><path d="M295 501c0-1 1-2 2-4 2-1 4-1 5-2l1 2c-2 2-4 4-6 7 0-2 1-3 0-4-1 0-1 1-2 2v-1z" class="n"></path><path d="M329 548l4 13c1 2 1 5 1 7v6 6l-3 3v-10c0-2-1-4 0-6h0c0-2 0-3-1-5 0-1-2-2-2-2v-2l-2-1c-2-2-6-5-6-7 2 2 5 5 8 6 1-1 0-3-1-5 2 1 2 3 3 5-1 1-1 2-1 3h0c1 1 2 2 2 3l1 1h0c0-3-1-6-2-8l-1-1v-1-5z" class="V"></path><path d="M303 469h1c0 1-1 3-1 4h0c0 1-1 3 0 4l1-1c0-1 1-2 2-3l2 1c1-1 3-1 4 0 1 0 2 1 3 2l-1 1h-1-2c-1 2-1 2 0 3s2 0 3 0l2-2 2 1v3c3-1 5-1 7 1 3 2 5 4 6 7 1 1 2 1 3 1-1 4-2 8-4 10s-3 3-5 4h-2c-1 0 0 0-2-1-2 0-5-1-7-2-2-2-3-4-3-7-1-3 0-5 2-7v-2c-1 1-3 1-4 1-2-1-4-2-5-3l-2-3v-2c-2 3-2 9-4 10h-1 0v1c-1-1-1-1-2-1h-1l1-2c3-6 5-13 8-18z" class="l"></path><path d="M331 490c1 1 2 1 3 1-1 4-2 8-4 10s-3 3-5 4h-2c-1 0 0 0-2-1l1-1h2c2-1 4-3 5-6h0c1-2 2-4 2-7z" class="m"></path><path d="M308 474c1-1 3-1 4 0 1 0 2 1 3 2l-1 1h-1-2c-1 2-1 2 0 3s2 0 3 0l2-2 2 1v3h-1c-2 2-4 3-7 2-2-1-4-2-5-4v-1h-1c0-2 2-4 4-5z" class="r"></path><path d="M319 486h0c1 2 0 2 1 3 1 0 3 0 4-1h0c2 1 3 2 5 4l-6 6c-1 1-2 1-3 2-1 0-2-1-2-1-2-2-3-4-3-6 1-2 2-3 4-5v-2z" class="p"></path><path d="M348 507h2c-3 11-3 21-3 33l-2 2c-1 1-3 2-4 4h-1c-2 2-3 3-4 6-1 2 0 4-1 6 0 1-1 2-2 3l-4-13-1-7c0-10 0-19 2-29v-3l2-1c1 2 0 4 2 5l2-1 1 2 2-2s2 1 3 1c1-1 1-2 2-3 2 0 2-1 4-3h0z" class="E"></path><path d="M333 520v-2c1 0 1-1 3-1v2c1-1 1-2 1-2l1-1h1v4h1 0l1-1c0 1 1 2 0 3h0 0v-1c-1 1-1 1-3 2 0-1 0-1-1-2l-1 1c0 2 0 2-1 3-1-1-1-3-1-5h-1z" class="C"></path><path d="M339 516h2l2-2c2 3 0 12 0 16v9c-2 2-3 4-5 6l1-2 1-1v-1c1-2 1-5 1-7v-12h0c1-1 0-2 0-3l-1 1h0-1v-4z" class="D"></path><path d="M328 541h1v-3l1 1c0 3 0 5 1 8 0 1 0 3 1 4 3-1 5-3 6-6 2-2 3-4 5-6 0 3-1 4-2 7h-1c-2 2-3 3-4 6-1 2 0 4-1 6 0 1-1 2-2 3l-4-13-1-7z" class="N"></path><path d="M330 509l2-1c1 2 0 4 2 5l2-1 1 2 2-2s2 1 3 1c1-1 1-2 2-3 0 3 2 3 0 6 0 1 0 2 1 4l-1 10h-1c0-4 2-13 0-16l-2 2h-2-1l-1 1s0 1-1 2v-2c-2 0-2 1-3 1v2c0 1 0 3-1 4-1-4 1-9-1-12h-1v-3z" class="M"></path><path d="M330 539c0-3-1-12 1-14 1 2 0 4 1 6 0-2 1-4 1-5 0-2 0-2 1-3v2h-1 1v3c1 4 2 6 2 10 1-1 1-2 1-3v-1c0-2 0-4 1-5 0 3 0 6-1 9v4c-1 0-1 1-1 2h0c-1 1 0 1-1 2h-1v2c-2 0-2 0-3-1-1-3-1-5-1-8zm18-32h2c-3 11-3 21-3 33l-2 2c-1 1-3 2-4 4 1-3 2-4 2-7v-9h1l1-10c-1-2-1-3-1-4 2-3 0-3 0-6 2 0 2-1 4-3h0z" class="H"></path><path d="M344 510c2 0 2-1 4-3-1 5-2 8-3 13-1-2-1-3-1-4 2-3 0-3 0-6z" class="P"></path><path d="M344 530c0 4 0 8 1 12-1 1-3 2-4 4 1-3 2-4 2-7v-9h1z" class="L"></path><path d="M303 497c1 6 5 12 7 17l17 37h0c1 2 2 4 1 5-3-1-6-4-8-6s-5-4-7-5-4-2-7-2h-1v-3c0 2 0 3-1 4h-1l-1-1-1-1v1l1 1h-1l-2-2-1 1 1 1c-1 1-1 0-1 2h-2l-1-1c-1-1-1-2-2-3-2-3-3-7-4-10 1-2 1-3 2-4-1-8-1-17 1-26h1v2c-1 1-1 1 0 2h0c1-2 1-3 2-5h0v1c1-1 1-2 2-2 1 1 0 2 0 4 2-3 4-5 6-7z" class="w"></path><path d="M295 501v1c1-1 1-2 2-2 1 1 0 2 0 4-1 3-3 6-3 9 0 1 0 2-1 3h1l1-1v1l-1 2v3 3h0l1-2h0v-3c1 1 1 2 1 4l-1 1v2h-1l1 2v2l3-3c1-1 1 0 2-1 0 2-4 5-6 6 1 1 1 1 1 2 0 2 1 6 3 8 0-1 1-1 1-2h-1c0-1 2-3 3-3h2c1 1 1 2 2 3 0 2 0 3-1 4h-1l-1-1-1-1v1l1 1h-1l-2-2-1 1 1 1c-1 1-1 0-1 2h-2l-1-1c-1-1-1-2-2-3-2-3-3-7-4-10 1-2 1-3 2-4-1-8-1-17 1-26h1v2c-1 1-1 1 0 2h0c1-2 1-3 2-5h0z" class="P"></path><path d="M291 528c1 4 1 7 3 10 1 1 1 1 1 3h-1c-1 1 0 1-1 1-2-3-3-7-4-10 1-2 1-3 2-4z" class="g"></path></svg>