<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="144 120 749 796"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#d71f22}.C{fill:#de3234}.D{fill:#c7181b}.E{fill:#e72c2e}.F{fill:#e02c2d}.G{fill:#a4171c}.H{fill:#7a0b0f}.I{fill:#dbd9d9}.J{fill:#d1161a}.K{fill:#b02327}.L{fill:#010101}.M{fill:#b71017}.N{fill:#c1bfbf}.O{fill:#f04a4b}.P{fill:#a21115}.Q{fill:#e95052}.R{fill:#920e13}.S{fill:#7e0a0e}.T{fill:#bf191c}.U{fill:#a6a4a5}.V{fill:#f4f3f3}.W{fill:#cfcece}.X{fill:#ea3131}.Y{fill:#8b898b}.Z{fill:#a90d15}.a{fill:#ea2222}.b{fill:#f17274}.c{fill:#aeacac}.d{fill:#130b0c}.e{fill:#e9e8e7}.f{fill:#9b9799}.g{fill:#ee6a6d}.h{fill:#323036}.i{fill:#b7b5b6}.j{fill:#bc3237}.k{fill:#fff}.l{fill:#971013}.m{fill:#242226}.n{fill:#6b0a0d}.o{fill:#f48182}.p{fill:#29272c}.q{fill:#686669}.r{fill:#610a0c}.s{fill:#535156}.t{fill:#310707}.u{fill:#4e070a}.v{fill:#928f91}.w{fill:#f47578}.x{fill:#424045}.y{fill:#3b080b}.z{fill:#727073}.AA{fill:#525156}.AB{fill:#807f80}.AC{fill:#cb4b52}.AD{fill:#210304}.AE{fill:#7b797b}.AF{fill:#42060a}.AG{fill:#f6a3a5}.AH{fill:#a67b7e}.AI{fill:#b98c8d}</style><path d="M312 157c-3 0-6-1-8-1 3-1 5-1 8 0v1zm399-1c3-1 5-1 8 0h0-5c-1 1-2 1-3 1v-1z" class="e"></path><path d="M311 412c1 1 2 2 2 4-1-1-1 0-3 0h0-2c1-1 2-3 3-4z" class="b"></path><path d="M713 418c1-1 1-2 3-2l2 2c0 1-2 1-2 2-1 0-2-1-3-2z" class="C"></path><path d="M235 258c1 1 3 2 4 4l-5-1s0-1-1-1l-2-1 4-1z" class="J"></path><path d="M243 285c1 1 2 2 4 3l-3 3h-1c-1-2-1-4 0-6z" class="D"></path><path d="M731 448h1v1h-2c0 1-2 4-2 5-1-3-1-6 0-9v1c1 1 1 1 1 2h2z" class="Q"></path><path d="M342 541l1 1-1 1h0v1 2l-2 1-2-2c1-2 3-3 4-4z" class="I"></path><path d="M679 531l6 6h-1c-2-1-2-1-4-1l-2-2 1-3h0z" class="K"></path><path d="M484 119c1-1 4-2 6-2h3l-1 1-2 1-2 1-2 1-2-2z" class="U"></path><path d="M421 891c2-1 3-2 5-1 4 2 7 0 11 1h-14-2z" class="N"></path><path d="M332 197l3-3v2h0l-1 5h-1l-1-4z" class="o"></path><path fill="#f0cecf" d="M741 571c1 0 2 1 2 2v8h0c-1-3-1-6-2-8s-1-1 0-2z"></path><path d="M713 418l-1-1c0-2 0-6 1-7l3 6c-2 0-2 1-3 2z" class="B"></path><path d="M487 890h2c2-1 5-1 7-1v2h-8l-1-1z" class="v"></path><path d="M284 445l2-1c1 0 0 0 1 1-1 1-1 2-1 4-2 1-2 1-2 2l-2-1c0-2 1-4 2-5z" class="E"></path><path d="M680 536c2 0 2 0 4 1h1l2 2v1l-2 2h-1c0-1-1-1-1-2h0v-1c0-1-2-2-3-3z" class="Z"></path><path d="M802 248l6-2 2 1-11 5c0-1 1-2 1-2 1-1 2-1 2-2z" class="R"></path><path d="M329 531l1 1c-1 1-1 2-1 3v3c-2-1-4-3-5-5 2 0 3-1 5-2z" class="E"></path><path d="M281 343c2 1 2 2 3 3-2 1-3 2-5 3h-1-1v-1-1l4-4z" class="O"></path><path d="M282 450l2 1c-1 3-2 5-1 8v3h-1v-5c0-1 0-2-1-3v1c0 1 0 2-1 4h0c-1-2 0-5 1-7l1-2z" class="a"></path><path d="M247 287c2 0 2 0 3 1h2l-4 9v-9l-1-1z" class="P"></path><path d="M375 218l3-1c2-2 4-4 7-5v1c0 1 2 2 2 3-3 0-4 0-6 1s-4 1-6 1z" class="J"></path><path d="M776 274h1c4 0 7-1 11-1l1 1c-2 2-5 1-6 3-1 0-1 2-1 3-1-2-1-3 0-4 0 0 0-1 1-1h-6l-1-1z" class="B"></path><path d="M340 163l7 3c-1 1-3 2-4 2-1-1-3-1-5-1 1-1 1-2 1-2 0-1 0-2 1-2z" class="Q"></path><path d="M694 458l5 6h-7c0-2 1-4 2-6z" class="D"></path><path d="M277 347v1 1h1 1c-2 1-5 1-7 2v2h0c-1-1-1-2-2-3-1 0-1 0-1-1h1 0l-1-1c2 0 3 1 5 0 1 0 2-1 3-1z" class="C"></path><path d="M637 647c2 0 3 0 5 2l1 2 2 2c0 1 1 2 1 3l1 1h-2c-2-4-6-7-8-10z" class="F"></path><path d="M338 167c2 0 4 0 5 1l-12 6 5-6c1-1 2-1 2-1z" class="E"></path><path d="M651 726c2-3 6-5 9-7v3l-9 6v-1l4-3c-1 0-2 1-3 2h-1z" class="B"></path><path d="M745 409l1 1h3l-3 4 3 3h0-1c-3-1-5-1-8 0 2-3 5-5 5-8z" class="X"></path><path d="M275 409c4 1 6 5 9 7h1v1l-4-1-6 1c1-1 2-2 3-2 0-2-2-4-3-6z" class="a"></path><path d="M356 217c0-2 1-2 2-3h1c3 1 7 3 10 3l-2 1h-1c-3 0-7 0-10-1z" class="F"></path><path d="M751 174c-4-1-8-2-12-4h1c4-1 8 0 12 2l-1 2z" class="AG"></path><path d="M474 125v-1c1-2 8-4 10-5l2 2c-3 1-5 2-7 4h-1-1 0c-1 2 0 3-1 4l-1-4h-1z" class="f"></path><path d="M679 166c2 0 3 0 4 1h3 1l6 6-11-4c-1-1-3-2-4-3h1z" class="a"></path><path d="M340 607h3c1 0 2 0 3 1h4c2 1 5 0 7-1l2 2h0l-7 3c0-1 0-2-1-3h-2c-1 0-3 0-4-1h-1c-2 0-3 0-4-1h0z" class="I"></path><path d="M364 608h0c1-1 2-1 3-2l-1-2h2c-2-3-7-6-9-8v-1c4 3 7 5 11 9-1 0-2 1-2 3l1 1v1h1l-4 1-2-2z" class="C"></path><path d="M341 205v-4h3v3h2c-1 1-2 1-2 2 1 4 4 7 8 9 1 1 2 1 3 1l-1 1-1-1c-2 0-3-1-4-2-1 0-2-1-3-2l-3-3v-1-2h0l-2-1z" class="J"></path><path d="M327 563l2-2 1 1c1 1 1 1 2 0v6h1l1 1c-1 0-2 0-3 1v1c-2-3-3-5-4-8z" class="D"></path><path d="M271 452h4v-2c1 0 1 0 2 1l3 2c-1 2-3 3-5 5l-1 1s0-1-1-1v-1-1c0-2-1-3-2-4z" class="V"></path><path d="M396 714l1-1c3 1 5 1 7 4l-1 1c-3-1-8-1-12 0h-4-1 1l1-1c3 0 6 0 8-3z" class="J"></path><path d="M668 750c4 6 7 9 14 10 3 1 4 2 7 2l-2 2c-1-1-2-1-3-1l-1 1c-2-2-7-4-9-6-3-2-5-5-6-8h0z" class="X"></path><path d="M330 530l9 1v1l-1 1c-1 0-2-1-3 0l-3 3-1 1v2c-1-1-1-1-2-1v-3c0-1 0-2 1-3l-1-1 1-1z" class="D"></path><path d="M330 532l1 1c0 2 1 0 0 1v3 2c-1-1-1-1-2-1v-3c0-1 0-2 1-3z" class="G"></path><path d="M325 562c-2 0-3 0-4 1h-1 0l2-2c1-1-2-4-3-5 1 0 2-1 2-1 3-2 3-1 6 0-1 2-1 5-2 7z" class="D"></path><path d="M709 725v1l-6 6c-3 1-6 3-7 5l1 3h-1c-2-1-3 0-5 0h-1-3c2-1 3-1 5-2 1-1 2-3 4-4 5-3 9-5 13-9z" class="E"></path><path d="M568 889l12 1h0c-2 0-2 1-3 1-2 0-3-1-5 0h-25 2l1-1h0 2c0 1 0 1 1 1 3 0 6 0 8-1l7-1z" class="U"></path><path d="M276 317c2 1 2 1 2 3 0 1 1 2 0 3 0 3 4 11 6 15-1 1-1 1-1 2-1-1-2-3-2-4-3-6-4-12-6-19h1z" class="C"></path><path d="M298 614c-4-3-7-6-9-10 2-2 4-3 6-4-1 3-2 4 0 6 1 1 0 1 1 1 0 3 0 4 1 6l1 1z" class="w"></path><path d="M740 475c1-2 1-2 3-3l-3 13c0 3-1 5-2 8 0-2 0-3 1-4l-1-1c-2 2-4 8-6 10 0-1 2-4 3-5 3-6 4-11 5-18z" class="J"></path><path d="M752 172c4 2 10 6 13 11h0l-2 1c-1-2-3-4-5-4-2-1-5-4-7-6l1-2z" class="w"></path><path d="M357 607c-2-1-4-3-5-5h0c2 2 7 7 10 6h2l2 2-22 5-1-1 9-2 7-3h0l-2-2zm-93-303l1-1 1-4h1-1c0 2 0 5 1 7l1 1-1 1c0 1 1 1 0 2v3l-1 1v-3c1-1 1-3 0-4h0c-3 0-5 3-7 4-4 2-9 3-13 4 7-3 13-4 18-11h0z" class="B"></path><path d="M337 663l1-20c1 2 2 3 4 4-2 3-2 8-3 12h0c-1 1-1 2-2 4zm258 55h1c5 0 11-6 15-10l3-3v1c-3 3-6 6-8 9-2 2-3 5-6 6l-4 1-2-2 1-2z" class="X"></path><path d="M664 707c0 2 0 2-1 4v2-1c1-1 1-1 1-2s0-1 1-2h1l-2 7c2-2 4-5 6-7h1c-3 4-7 7-8 11h0c-1 3-1 6-1 9-1-1-1-3-1-4l-1 1v-3-3l4-12z" class="E"></path><path d="M713 509h1l-10 11 9-2c-6 3-13 4-19 7l-1-2h0l6-3c6-1 10-7 14-11z" class="J"></path><path d="M660 725l1-1c0 1 0 3 1 4 0 3 0 5 1 8 1 2 2 5 3 8 0 2 1 4 2 6h0c-3-4-5-8-7-13v-1c-1-4-1-8-1-11z" class="a"></path><path d="M743 452l3 9 1 2c1-2 3-5 5-7h0c-1 3-2 5-4 7h-1l-6 3v-7c1-2 1-4 2-7z" class="E"></path><path d="M355 553h2c1 0 2-1 4-1l1 2c0 1 1 2 2 3v1 3h-3c-3-1-4-3-4-5-1-1 0-1-1-1 0-1-1-1-1-2z" class="I"></path><path d="M357 556h0l2 2h3v-3-1c0 1 1 2 2 3v1 3h-3c-3-1-4-3-4-5z" class="F"></path><path d="M234 261c-7 1-13 0-19 0h-7c-1 0-2 1-4 1 2-1 3-1 4-1l1-1h12c-2-2-4-4-5-6 2 2 4 4 6 5h9l2 1c1 0 1 1 1 1z" class="B"></path><path d="M808 246c8-3 18-6 27-6-2 1-2 1-4 1v1l3-1s1 1 2 1c-9 1-17 3-26 5l-2-1z" class="S"></path><path d="M733 433c4 6 8 13 10 19-1 3-1 5-2 7v-6c-1-4-4-9-7-13v-1l-2-4 1-2z" class="B"></path><path d="M382 712c-1-1-1-2-1-3l3-3c2 0 3 0 4 1h1c1 0 3 1 5 2v1h0-2c-1 0-1-1-2-1v1h-1l1 1v1c-1 1-1 1-2 1h-2c-1 1-1 1-1 2-1-1-1-1-2-1v-2h-1z" class="V"></path><path d="M580 890h25c2 0 6-1 8 0l1 1h-9-33c2-1 3 0 5 0 1 0 1-1 3-1h0zm-124-1c4 0 8 0 13 1h1c4-1 8-1 12 0v1h1v-1h1c1 0 1 0 1 1h0c-5 1-11 0-17 0-4 0-8 1-13 0h-2l1-1h0l2-1z" class="c"></path><path d="M687 655c0 4-1 9 0 13l4-11c0 1-1 3-2 5 0 2-1 5-1 8-1 4-1 8-1 12-1-2-1-4-1-6l-1-1c-1-1-1-1-1-2l1-16h1l1-2zm-293 78c5 0 10 3 13 7l4 5h1c-2 2-2 3-3 6h-1l-1 1v-1c1-1 0-2 0-4v-2c-1-4-10-10-13-12h0z" class="a"></path><path d="M741 466l6-3c1 2 1 4 1 6 0 1 0 1-1 1l-1-3h0c-2 1-2 4-3 5-2 1-2 1-3 3-2 3-5 6-7 9h0c3-6 7-12 8-18z" class="X"></path><path d="M336 679c1-5 0-11-1-15 0-2-2-5-2-7 1 3 2 5 3 8h1v-2c1-2 1-3 2-4h0c1 4 1 9 1 13l-1 4-2 2-1 1z" class="E"></path><path d="M789 258l4 1c5 1 10 1 14-2h0l-2 2c2 2 7 1 10 2h1c1 0 2 0 3 1-5-1-9-1-14-1-3 0-5 1-8 1-4 0-8 0-12-1h1 1c1-1 1-1 1-2h0l1-1z" class="F"></path><path d="M283 340c0-1 0-1 1-2l9 21c2 4 5 10 6 15v1c-3 1-5 2-8 1l2-1c2 0 2-1 4-2v-2c0-2-3-6-4-8l-6-13c0-2-1-4-2-5l-1 1c-1-1-1-2-3-3 1-1 2-2 2-3z" class="B"></path><path d="M294 429l2 2c-2 2-4 5-5 7l1 1c1 1 1 1 1 2 3 2 4 5 4 8v3h-1c-1-1-2-2-2-3v-1c1 1 1 1 1 2h1v-2c-1-1-1-1-2-1l1-2c-1 0-1 0-2-1l-2-5-4 6h0c-1-1 0-1-1-1l-2 1c3-5 6-11 10-16z" class="a"></path><path d="M718 418c6 4 12 9 15 15l-1 2h0c-4-5-9-11-15-12h0-5l1-1h4l-2-1 1-1c0-1 2-1 2-2z" class="X"></path><path d="M631 889c3 1 6 0 9 0h2 3 4l1 1h-1-11c1 1 1 0 2 1 3 0-1-1 2 0h6c6 0 13-1 20 0 1 0 4-1 5 0h-38-19c-4 0-8 1-11 0h9 4c4-2 9-1 13-2z" class="W"></path><path d="M355 740c1 1 0 1 1 0 2-1 3-2 5-3h1v2h0c3 2 7 3 10 3 0 0-1 1-2 1v1l2 2h0l-6-1-2 3h-1c1-2 1-3 0-4s-2-2-4-3c-2 1-3 3-6 3v-3s1 0 2-1zm386-402l1 2c0 2 1 3 3 5l6 3 7-2v1l-4 2 1 1v1c-1 0-1 1-3 1-4-1-9-3-12-7l-1 2c-1-3 1-6 2-9z" class="X"></path><path d="M312 156c10 1 18 2 28 7-1 0-1 1-1 2 0 0 0 1-1 2 0 0-1 0-2 1v-2c-6-6-17-7-24-9v-1z" class="C"></path><path d="M729 601l5 3c-3 6-7 10-14 14 1-3 2-4 4-6s2-4 3-7h0l2-4z" class="g"></path><path d="M756 302c1-1 2-2 3-2v1l1 1h1 1c0 1-1 3 0 5 3 6 11 5 16 8-4 0-8-1-11-3-3-1-5-3-8-5v8c1 2 3 7 2 8-1-2-1-3-1-4l-2-7-1-1h1l1-2-2-2c-1 0-2-1-3-1l2-4z" class="F"></path><path d="M756 302c1-1 2-2 3-2 0 2 0 4-1 7h-1c-1 0-2-1-3-1l2-4zM321 520c2 0 2 1 4 1l5 3c1 0 3 1 4 1l2 2c1 1 4 2 6 2l-1 1-2 1-9-1v-2s-1 0-2-1l-3-2-6-4c1-1 2-1 2-1z" class="V"></path><path d="M711 156v1c-9 2-17 4-24 9v1h-1-3c-1-1-2-1-4-1 10-7 20-8 32-10z" class="C"></path><path d="M596 722l4-1c2 6 3 14 8 19v1c-5-5-13-6-19-8 1 0 1-1 1-1 2 0 4 2 6 2 2 1 3 1 4 0 0-4-2-9-4-12z" class="B"></path><path d="M594 720l2 2c2 3 4 8 4 12-1 1-2 1-4 0-2 0-4-2-6-2v-2c1-3 2-6 4-10z" class="k"></path><path d="M382 712h1v2c1 0 1 0 2 1h-1-1l-1 1 4 4c3 3 5 11 8 13h0c-3 1 0 5-1 8l-1-1-1-1h-1l1-1c0-4-3-10-5-14-3-5-5-6-9-7l-1-1h3c1-1 2-2 3-4h0z" class="X"></path><path d="M651 726h1c1-1 2-2 3-2l-4 3v1c-3 4-6 8-9 13 0 1-1 2 0 3h3c-2 1-3 1-5 2 0 1-1 2-1 4h0-1l-1-3c0-1-2-2-3-2-1-1-1 0-1-1 1 0 3 1 4 0 1 0 3-9 5-10 3-3 6-6 9-8z" class="a"></path><path d="M361 552c1 0 2 0 3-1 1 0 3-1 4 0 2 0 3 1 3 3l1 2v3 1 1c-1 0-1 1-1 1h0c-3 0-4-2-7-1v-3-1c-1-1-2-2-2-3l-1-2z" class="V"></path><path d="M364 558h3c2 1 2 1 5 1h0v1 1c-1 0-1 1-1 1h0c-3 0-4-2-7-1v-3z" class="B"></path><path d="M517 889l2 1v1h1c2 0 3 0 4-1 2 1 7 1 9 1 2-2 4-1 7-1h2 1c2-1 4 0 6 0h12c-2 1-5 1-8 1-1 0-1 0-1-1h-2 0l-1 1h-2-51v-2h6 15z" class="f"></path><path d="M517 889l2 1v1h-11-4c-1 0-2-1-2-2h15z" class="Y"></path><path d="M324 586h1c5 0 8-1 12-5 0-3-1-7-2-11h0c2 3 3 7 4 10 0 0 1 2 1 3 1 0 2 1 3 2 4 2 9 5 12 8l-15-6h0c-5-1-9 1-13 2v-1c1 0 1 0 1-1l1 1v-1h-5v-1z" class="J"></path><path d="M696 546c2-1 2-1 3-3s0-4 0-6c-1-1-2 0-3 0 1-1 2-2 3-2 3 3 2 8 2 12-1 6-4 12-2 19v1l-2 1c0-1 0-2-1-3-1 2 0 3-2 4h-1c0-3 1-5 1-8v1h1v-4h1l-1-1c1-3 2-7 2-10l-1-1z" class="E"></path><path d="M248 213h1v1l2 1c-1 1-1 2-2 3 2 1 6 1 8 1l-9 1c-5 0-13-1-17-2-2-3-5-4-7-6l8 2c6 2 11 1 16-1z" class="a"></path><path d="M310 416c2 0 2-1 3 0l1 2c-1 1-2 2-2 4l-1-1-1 1 1 2c-2 0-5 0-7 2l-1 1v-1c-3 0-5 3-7 5l-2-2h0c4-5 9-9 14-13h2 0z" class="Q"></path><path d="M310 416l1 1v4h0l-1 1h0c-1-1 0-4 0-6h0z" class="E"></path><path d="M310 416c2 0 2-1 3 0l1 2c-1 1-2 2-2 4l-1-1h0v-4l-1-1z" class="O"></path><path d="M687 539l3 2c2 0 3-2 5-3-1 2-3 3-3 5 2 1 3 2 4 3l1 1c0 3-1 7-2 10l-3-5v-1h0c-1-1-2-2-3-2 0 1 1 3 1 5l-1 3c0-2 0-6-1-8h-1c-1-2-3-4-5-5 0-1 0-1 1-1s1 0 1-1h1l2-2v-1z" class="C"></path><path d="M685 542l2-2 1 3-1 1c-1 0-1-1-2-2z" class="P"></path><path d="M690 546c2-1 4 0 6 1h1c0 3-1 7-2 10l-3-5v-1h0c-1-1-2-2-3-2v-1c-1 0-1 0-1-1l1-2 1 1z" class="V"></path><path d="M689 549v-1c-1 0-1 0-1-1l1-2 1 1 2 5h0c-1-1-2-2-3-2z" class="B"></path><path d="M689 549c1 0 2 1 3 2h0v1l3 5 1 1h-1v4h-1v-1c0 3-1 5-1 8-1-1-2-1-4 0-2 4-4 8-2 13v1l-2-2h0l-1 1 1-11c0-1-1-1-2-2v-1c1 1 2 1 3 1 2-4 3-7 3-12l1-3c0-2-1-4-1-5z" class="X"></path><path d="M689 549c1 0 2 1 3 2h0v1h0v3 1l-2-2h0c0-2-1-4-1-5z" class="a"></path><path d="M689 569c1-2 2-7 4-8h1c0 3-1 5-1 8-1-1-2-1-4 0z" class="I"></path><path d="M273 458a30.44 30.44 0 0 0-8-8h-1c-6 2-9 6-12 11l1-5c0-3 8-8 11-10l-5-15 14 18h-1c0-1-1-2-2-2 0 2 0 3 1 4v1c1 1 2 2 2 4v1 1z" class="B"></path><path d="M752 456c0-3 0-6 2-9 3-6 8-11 12-17-2 6-4 12-5 18 4 2 8 4 10 8l2 6c-2-4-5-10-10-11-3-1-7 2-9 3-1 1-2 1-2 2h0z" class="a"></path><path d="M338 537l1 1h5 0c0 1 0 1 1 1-1 0-3 2-3 2-1 1-3 2-4 4-2 2-6 6-6 10 0 1 0 1 1 2 0 1 0 4-1 5s-1 1-2 0l-1-1-2 2-2-1c1-2 1-5 2-7v-1c3-6 7-11 11-17z" class="J"></path><path d="M327 554l3 1v2l-1 4-2 2-2-1c1-2 1-5 2-7v-1z" class="M"></path><path d="M608 740c2 1 4 3 6 4 2-2 3-4 5-6 0-2 1-9 3-10 0-1 5-1 6-2-1 1-2 1-2 1-3 2-4 3-5 5-1 3-2 7-3 10-1 2-2 4-3 7-1 6-1 13-1 19v29c-2-10-1-21-1-32 0-4 0-9-1-13l-1-1c0-4-2-7-3-10v-1z" class="C"></path><path d="M474 125h1l1 4 1 6c0 3 0 6-1 9l-1 4c-1 0-1 1-2 2 0 1-1 2-2 3l-1 5c-1 2-3 6-5 8 0-1-2-5-2-5l1-1c2-2 4-3 4-5 2-4 3-9 3-13l3-17z" class="L"></path><path d="M243 278c2 0 4 2 6 3 4 2 8 4 12 8 2 2 5 5 7 8v2h-1 0-1l-1 4-1 1c-1-3-1-6-3-9s-5-6-9-7h-2c-1-1-1-1-3-1v1c-2-1-3-2-4-3v-2c1-2 0-3 0-5z" class="Z"></path><path d="M862 230l3-5c2-2 8-6 11-6-2 3-4 7-6 11l-6 12-19-1c4-1 9 0 13 0 1-4 2-7 4-11z" class="g"></path><path d="M629 673l4-1c-1 0-1 1-2 2l4 1c-2 1-3 2-4 3 1 1 1 2 2 2v1c1 2 3 4 5 6 2 1 3 1 5 1 1-2 0-2 1-4l1 1c0 2 0 4-1 6 0 3 0 5-1 7v-6l-5 1-2-3c-3-5-9-8-15-10l-6 5c1-1 3-3 3-5h1v-3h3c3 0 4-1 6-3h-1l2-1z" class="J"></path><path d="M642 668l2-1 1 1-3 3c7 3 17 7 21 15h0l-1-1c-5-4-11-7-17-7h-1l-1 2h-1l-1-3h0c-1 1-1 2-2 2-1 1-4 1-6 2v-1c-1 0-1-1-2-2 1-1 2-2 4-3l-4-1c1-1 1-2 2-2l2-2 2-1c1 1 2 1 3 1s1-1 2-2z" class="B"></path><path d="M637 674c0-1 1-2 1-3h1v2l1 2v1l-1-1s-1 0-2-1z" class="D"></path><path d="M637 674v2l1 1c-2 1-4 2-5 3-1 0-1-1-2-2 1-1 2-2 4-3l2-1z" class="I"></path><path d="M280 453l1-1c-1 2-2 5-1 7h0c1-2 1-3 1-4v-1c1 1 1 2 1 3v5h1c0 4 1 9 3 12h1c0 3 4 8 5 11h0c-2-3-4-6-7-9 1 10 4 17 10 26h-1c-2-3-5-6-7-10-4-7-5-16-8-24h0l-2 2c-1-2 0-4-1-6 0-2-1-3-2-5l1-1c2-2 4-3 5-5z" class="E"></path><path d="M280 453l1-1c-1 2-2 5-1 7-1 1-2 3-3 4 0-2-1-4-2-5 2-2 4-3 5-5z" class="I"></path><path d="M356 523h2v3h-1c-2 2-1 4-1 7 0 1-1 1 0 2v1l-1 1h0l-1 1v-1h-2c-3-1-5 1-7 2-1 0-1 0-1-1h0-5l-1-1c1-1 1-1 1-2l-7 1 3-3c1-1 2 0 3 0l1-1v-1l2-1 1-1c-2 0-5-1-6-2 2 0 5 2 7 2 1 0 3-1 4-2 1 0 1-1 2-2v2h5c2-2 2-2 2-4z" class="l"></path><path d="M341 530c0 2-1 2-2 4h-2 0 2c1 1 1 2 3 3v-1h2 0v-1h0l2-1c1 0 1 0 2-1 2 1 2 1 4 3l2-1v2h-2c-3-1-5 1-7 2-1 0-1 0-1-1h0-5l-1-1c1-1 1-1 1-2l-7 1 3-3c1-1 2 0 3 0l1-1v-1l2-1z" class="G"></path><path d="M180 241l-19 1-12-24 1-1h1v1c1 1 5 5 7 5l1 1c1 1 1 2 3 2h2l-1 1c1 4 3 8 5 11l2 1c2 0 3 1 4 2h1 5z" class="w"></path><path d="M158 223l1 1c1 1 1 2 3 2h2l-1 1c1 4 3 8 5 11l2 1c2 0 3 1 4 2h1-9c-2-6-5-12-8-18z" class="B"></path><path d="M314 418c1 1 1 3 2 4l1 3h0 0l2 3 2 3h0c-2-1-4 0-6 0-4 0-8 0-12 2s-7 5-10 8c0-1 0-1-1-2l-1-1c1-2 3-5 5-7s4-5 7-5v1l1-1c2-2 5-2 7-2l-1-2 1-1 1 1c0-2 1-3 2-4z" class="C"></path><path d="M296 431c2-2 4-5 7-5v1l1 3-8 5c-1 1-2 3-4 4l-1-1c1-2 3-5 5-7zm18-13c1 1 1 3 2 4l1 3h0c0 2 1 2 0 4-3 1-6 0-10 0l-3-3c2-2 5-2 7-2l-1-2 1-1 1 1c0-2 1-3 2-4z" class="V"></path><path d="M314 418c1 1 1 3 2 4l-5 2-1-2 1-1 1 1c0-2 1-3 2-4z" class="F"></path><path d="M250 200c1-9 5-17 12-23 5-4 12-8 19-7h3c-2 2-6 2-8 2-9 3-20 12-21 22-1 3 0 6 2 8 1 2 2 3 4 4l-1 1v1c0 1 1 2 2 3v1h-2-2c-4-3-5-8-8-12z" class="b"></path><path d="M348 703l3-1c2 1 2 6 4 7v1l-1 8c0 4 0 8-1 13h-1c0 3-2 5-3 7 3 0 4 0 6 1v1c-1 1-2 1-2 1l-1-1c-3 1-4 0-5 3-2 3-1 8 0 11l1 1h1c-1 1-3 2-4 3l1-1c-1-2-2-4-3-5 0-2 2-6 1-7l-2 2h-1c1-5 5-7 6-12 0-2 2-5 3-7 1-4 1-9 0-12l-4-2v-1c1 1 2 2 3 1 0-2-1-4-1-6l1-1-1-4z" class="a"></path><path d="M348 703l3-1c2 1 2 6 4 7v1c-1 1-1 3-2 5l-4-8-1-4z" class="V"></path><path d="M338 199c2-2 5-4 7-7v-2c0-1 1-3 1-3s2-1 2-2c6-4 11-5 17-5 3 0 3-1 4-4h1c1 1 2 2 2 3 5 3 9 1 13 7v1c-1-1-1-1-2-1-2-2-5-3-8-3s-6 2-9 2-8 4-11 5v-1-2h-1c-2 0-4 0-5 2-2 2-2 5-2 7-1 2-2 3-3 5h-3v4l-2 1v-1c0 1-1 2-1 2-1-2-1-2-3-3h1c1-2 1-3 1-5h1z" class="F"></path><path d="M338 199v5h0l1 1c0 1-1 2-1 2-1-2-1-2-3-3h1c1-2 1-3 1-5h1z" class="a"></path><path d="M477 125c0 2 1 3 1 4l2 8c1 1 1 2 1 4s1 7 0 9h0c-1 1-2 2-3 2-1 1-1 8-1 8l-2 2c0 3-2 7-3 10-1 1-2 2-4 2h-2c0-1 0-2-1-3v-3-2c2-2 4-6 5-8l1-5c1-1 2-2 2-3 1-1 1-2 2-2l1-4c1-3 1-6 1-9l-1-6c1-1 0-2 1-4z" class="h"></path><path d="M477 125c0 2 1 3 1 4l2 8v1c-1-1-1-2-1-3h-1v4 2l-1-1v-5l-1-6c1-1 0-2 1-4z" class="s"></path><path d="M475 148l1 1v-3c1-1 1-2 1-3v4 3h0c1-1 1-1 1-2l1-1c1 1 1 2 1 3h1 0c-1 1-2 2-3 2-1 1-1 8-1 8l-2 2v-1-2c0-2 1-4 0-6 0-1 0-1-1-2-2 2-2 5-3 7h0-1l1-5c1-1 2-2 2-3 1-1 1-2 2-2z" class="p"></path><path d="M471 158h0c1-2 1-5 3-7 1 1 1 1 1 2 1 2 0 4 0 6v2 1c0 3-2 7-3 10-1 1-2 2-4 2h-2c0-1 0-2-1-3v-3-2c2-2 4-6 5-8h1z" class="L"></path><path d="M470 158h1c0 2-1 5-2 6-1 3-3 4-4 7v-3-2c2-2 4-6 5-8z" class="z"></path><path d="M777 275h6c-1 0-1 1-1 1-1 1-1 2 0 4s1 8 0 10v1c-2-1-2-3-4-4l-1 1 1 11c-1-3-3-9-6-10-4 2-6 4-8 8l-2 5h-1-1l-1-1v-1c-1 0-2 1-3 2l-1-1c0-2 0-3 1-5 0-1 1-1 1-2 1-3 5-6 7-8l6-3v-3l4-1c2-1 2-1 4-3l-1-1z" class="M"></path><path d="M778 276h0c1 1 2 1 3 2-1 1-3 1-4 2-1-1-2-1-3-1 2-1 2-1 4-3z" class="B"></path><path d="M770 280l4-1c1 0 2 0 3 1l-2 1h0c-2 2-4 2-6 3s-3 2-5 3c-2 2-6 5-7 8 0 1 0 1 1 2 1 0 2 1 3 2v-2h3l-2 5h-1-1l-1-1v-1c-1 0-2 1-3 2l-1-1c0-2 0-3 1-5 0-1 1-1 1-2 1-3 5-6 7-8l6-3v-3z" class="J"></path><path d="M770 280l4-1c1 0 2 0 3 1l-2 1-5 2v-3z" class="F"></path><path d="M707 427l3-4c1-2 3-2 5-2l2 1h-4l-1 1h5 0c6 1 11 7 15 12h0l2 4v1c-2 1-2 2-3 4 0 1-1 2 0 4h-2c0-1 0-1-1-2v-1-1c1-1 2-2 1-4-3-4-7-7-12-9-3 3-7 4-11 6h-1-1c-1 0-1 0-2 1h-3c-1 0-2 0-3-1v-1h2 2c0-2 2-3 3-4 1-2 3-4 4-5z" class="O"></path><path d="M717 423c6 1 11 7 15 12h0l2 4-1 1-2-2-1-1c-1 0-1-1-2-3 0 1 0 1-1 2-1-2-2-3-3-3-1-1-3-1-4-3 1-1 1-2 2-3v-1h-2l-1-1-2-2z" class="V"></path><path d="M707 427l3-4c1-2 3-2 5-2l2 1h-4l-1 1h5 0l2 2v1c1 2 1 2 0 4-1 1-3 0-5 0s-3 0-5-1c-1 0-1-1-2-2z" class="I"></path><path d="M707 427l3-4c1-2 3-2 5-2l2 1h-4l-1 1h5 0l2 2v1c-2-1-3-2-6-2 0 1-1 1-1 2h0l-1-1-2 2v1l5 2c-2 0-3 0-5-1-1 0-1-1-2-2z" class="D"></path><path d="M703 432c1-2 3-4 4-5 1 1 1 2 2 2-1 1-2 1-3 2l1 1h2c1-1 1-1 2-1v-1c1 0 2 0 3 1h3c-3 3-7 4-11 6h-1-1c-1 0-1 0-2 1h-3c-1 0-2 0-3-1v-1h2 2c0-2 2-3 3-4z" class="b"></path><path d="M703 432c1-2 3-4 4-5 1 1 1 2 2 2-1 1-2 1-3 2l1 1c-2 2-5 3-7 4 0-2 2-3 3-4z" class="a"></path><path d="M733 336c1-1 2-1 3-3 2-4 3-6 6-10v3 3h2v3c0-1 1-1 2-2l-4 10-1-2c-1 3-3 6-2 9-1 2-3 5-4 7-2 4-3 9-5 12h0v-1c0-2 0-3 1-4l-2-2-2 1c1 1 1 1 1 2l-1 1-2-1c-1 2-2 3-4 4l3-8 8-18 1-4z" class="O"></path><path d="M733 336c1-1 2-1 3-3 2-4 3-6 6-10v3c-1 3-2 5-4 7 0 0 0 1-1 1-1 2-3 5-5 6l1-4z" class="X"></path><path d="M734 343c0-2 1-4 3-5 0-1 0-2 1-3 2-2 3-2 4-4h0c0 1 1 2 0 4l-1 3c-1 3-3 6-2 9-1 2-3 5-4 7h-1c1-2 1-4 1-5 1-3 1-5 1-8h0l-2 2z" class="b"></path><path d="M734 343l2-2h0c0 3 0 5-1 8 0 1 0 3-1 5h1c-2 4-3 9-5 12h0v-1c0-2 0-3 1-4l-2-2-2 1c1 1 1 1 1 2l-1 1-2-1c-1 2-2 3-4 4l3-8h1v-1c2-1 2-3 3-5 2-3 4-7 6-9z" class="o"></path><path d="M250 200c3 4 4 9 8 12h2 2v-1c-1-1-2-2-2-3v-1l1-1c3 2 5 3 9 3 4-1 8-1 12-3 1-1 3-2 5-3 1-1 1-1 3 0 0 2-1 3-1 5l-2-1c-2 2-4 3-6 5h-1c-1 1 0 0-2 1h0c-1 1-1 1-2 1s-2 1-3 2h1c-3 1-5 1-8 1v2h0-9c-2 0-6 0-8-1 1-1 1-2 2-3l-2-1v-1h-1c2-3 2-10 2-13z" class="C"></path><path d="M258 212h2 2v-1c-1-1-2-2-2-3v-1l1-1c3 2 5 3 9 3l-2 2h-1c-1-1-1-1-2 0v1c-2 1-4 1-6 1l-1-1z" class="g"></path><path d="M289 208l-1 2c3 3 10-2 13 0 1 0 3 0 4-1v-1c0 1 0 1 1 2h1c4-1 6-3 7-7 1-1 1-3 2-4s2-1 4-2c2 2 3 4 4 6h1l1 1c-5 3-11 7-16 9v1l-1 1h1l-2 1c-1 1-1 2-2 2h-3-10c-2-1-5-1-7-1-4-1-8-1-12-1h-1c1-1 2-2 3-2s1 0 2-1h0c2-1 1 0 2-1h1c2-2 4-3 6-5l2 1z" class="E"></path><path d="M299 214l11-1v1l-1 1h1l-2 1h-7l-2-2z" class="b"></path><path d="M286 217l4-1c2 1 7 0 10 0-1 0-1 0-1-1h0c-2-1-4-1-5 0h-4c3-1 6-1 9-1l2 2h7c-1 1-1 2-2 2h-3-10c-2-1-5-1-7-1z" class="O"></path><path d="M620 654c5 1 15 2 18 6l2 2v5l2 1c-1 1-1 2-2 2s-2 0-3-1l-2 1-2 2-4 1-2 1h-1c-3-1-5-2-8-2l-2-2-2-1-1-2c3-3 3-8 6-11 0 0 1-1 1-2z" class="k"></path><path d="M629 673c0-1 0-2 1-3h5l-2 2-4 1z" class="V"></path><path d="M634 666h0c2-2 3-3 6-4v5l2 1c-1 1-1 2-2 2s-2 0-3-1c-1 0-2-2-3-3h-1-1 2z" class="C"></path><path d="M620 654c5 1 15 2 18 6l2 2c-3 1-4 2-6 4h0c0-2-1-3-2-5-3-3-8-4-13-5 0 0 1-1 1-2z" class="B"></path><path d="M722 611l5-6c-1 3-1 5-3 7s-3 3-4 6l-9 8-9 12c-1 1-3 4-4 4 0 0 1-2 1-3 2-3 5-7 4-10l-1-1c-6 3-13 8-17 12h0c2 5 2 10 2 15l-1 2h-1c-1-2-1-6-2-8-1-4-4-5-7-7l-9-6 15 3c6-3 14-9 19-14-6-4-13-7-20-9l1-1c6 1 17 6 23 4 3 0 5-2 7-3s4-3 5-4h1l1-2s1-1 1-2h0 2 0c1 0 1 0 2-1-1 1-1 3-2 4z" class="F"></path><path d="M722 611l5-6c-1 3-1 5-3 7s-3 3-4 6l-9 8c0-3 2-4 4-7 1-1 1-3 2-4h1l1-1c2-1 2-2 3-3z" class="X"></path><path d="M378 577v-2c2-1 2-1 4-1l6 12-2 5c-2 5-3 11-7 16v3h-3c-1-1-3-1-4-1h-2-1v-1l-1-1c0-2 1-3 2-3h1l2-3c0-1 1-2 0-4-1-6-10-10-15-13h0l4-1c0 1 1 1 1 2 1 0 3 1 5 1h1c0-1 1-1 1-1 3-1 6-5 8-8h0z" class="Z"></path><path d="M378 600c0-2-1-5 0-7v-1l-3-3c1-2-1-2-1-4 2-2 5-5 7-6 1 1 1 2 1 3 1 1 1 2 1 4l-1 1h-1 1l4-1 1 1h0c-1 1-1 2-1 4-2 5-3 11-7 16v3h-3c-1-1-3-1-4-1 3-3 5-5 6-9z" class="G"></path><path d="M381 587c-1 0-1 1-2 2l-1-1 1-1c0-3 1-6 1-8 1 2 1 5 1 7l1 1h-1z" class="H"></path><path d="M378 600h0c1-1 1-2 2-3l1-1v4c0 2-2 4-2 7v3h-3c-1-1-3-1-4-1 3-3 5-5 6-9z" class="S"></path><path d="M630 873h44c-2 1-5 1-7 1h-9-2-2-2v1 2 1 1 2c-1 1-1 2-2 3v2c-1 1-1 2-1 3h-4-3-2c-3 0-6 1-9 0h0-10c2-3 2-7 3-10 0-3 0-5 4-5 1 0 1 0 2-1z" class="N"></path><defs><linearGradient id="A" x1="744.82" y1="296.321" x2="762.113" y2="312.1" xlink:href="#B"><stop offset="0" stop-color="#cf141d"></stop><stop offset="1" stop-color="#fd403c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M770 280v3l-6 3c-2 2-6 5-7 8 0 1-1 1-1 2-1 2-1 3-1 5l1 1-2 4c-1 1-2 3-3 5l-5 19c-1 1-2 1-2 2v-3h-2v-3-3c-3 4-4 6-6 10-1 2-2 2-3 3l7-18 10-28c0-1 0 0 1-1l3-3 2-2v-1c1 1 0 2 1 4l1-1c1-1 2-2 3-2s1-1 2-2h-2l1-1 1 1c2 0 5-2 7-2z"></path><path d="M740 318c1 0 2-1 2-2h1 0c1-1 1-1 2-1 1-2 0-3 2-3 0 2-1 3-2 5s-1 4-3 6h0c-3 4-4 6-6 10-1 2-2 2-3 3l7-18z" class="F"></path><path d="M663 217c4-1 8 0 12 0 4-1 6-4 8-7-1-2-2-4-2-6l-3-10c-1-2-1-4-2-5-2-2-3-2-5-3-2 1-2 2-3 3h0s0 1-1 0l-1-1c-2-1-5-2-6-2-3-1-6-3-10-4-3 1-7 1-9 3-2 1-3 3-4 4l3-6c3-2 7-3 11-4 1-1 2-3 3-5 1 2 1 4 3 5 2 2 9 0 12 2 2 1 8 3 9 6 1 1 1 3 2 4 1 5 3 10 7 14 2-3 3-5 4-8 1 0 1 1 2 2 1 2 1 4 2 6v2-2h-1c0 1 0 2 1 3-1 1-1 2-2 3v1h0v1c-2 1-3 2-5 3h-1l2 2 1-1h1c0 1 1 1 2 1h1c-2 1-5 1-7 0h0c-4 1-6 1-10 1h0c1-1 1-1 2-1 0-1-1-1 0-1v-1l-1 1c-3 2-11 1-15 0z" class="E"></path><path d="M693 199c1 2 1 4 2 6v2-2h-1c0 1 0 2 1 3-1 1-1 2-2 3l-1 1c-4 3-8 5-12 6 4-3 7-6 9-11 2-2 1-7 4-8z" class="Q"></path><path d="M423 891h-80 0 22c2-1 4-1 6-1h0l-1-1v-4h0c1-3-1-4-1-7 0-1 1-1 0-2h0l1-2h0-1-4-1-1c-8-1-15 0-22 0-3 0-6 0-9-1h152l-30 1-64-1c1 1 2 1 3 1v1 5l1 5c1 2 1 3 3 4l1 1h2l-5 1h14 12 2z" class="I"></path><path d="M250 272l1 1c1 1 3 3 6 4 0-1 0-1-1-2l2-1c1 1 3 2 5 4h0c2 3 5 5 7 8l6 6h1v1 5l1 2 4 10 5 13 1 4c-1-1-1-1-2-3 0-1 0-1-1-1 0-1 0-2-1-3 0-1 0-1-1-1v2h-1v-2c-1-2-1-2-2-3l-1 1s0 1-1 2v1c0-2 0-2-2-3h-1c-1-7-3-12-8-18h1v-2c-2-3-5-6-7-8-4-4-8-6-12-8-2-1-4-3-6-3-2-1-6-4-8-5h2c3 1 6 1 9 1 2-1 3 0 4-2z" class="B"></path><path d="M250 272l1 1c-1 1-1 3-1 4-1 0-2-1-3-1l-1-2c2-1 3 0 4-2z" class="P"></path><path d="M274 296c1 2 3 3 4 4l4 10 5 13 1 4c-1-1-1-1-2-3 0-1 0-1-1-1 0-1 0-2-1-3 0-1 0-1-1-1v2h-1v-2c-1-2-1-2-2-3l-1 1s0 1-1 2v1c0-2 0-2-2-3 1-1 2-1 3-2 1 0 1-1 1-2s0-2-1-3c-1 0-1-1-2-1l1-1v-4h0c0-3-2-5-4-6v-1-1z" class="T"></path><path d="M251 273c1 1 3 3 6 4 0-1 0-1-1-2l2-1c1 1 3 2 5 4h0c2 3 5 5 7 8l6 6h1v1 5l1 2c-1-1-3-2-4-4-3-3-5-7-8-9l-9-6c-2-1-3-1-5-3l-2-1c0-1 0-3 1-4z" class="l"></path><path d="M252 278c5 1 9 4 13 5 3 1 5 5 7 7s4 5 5 8l1 2c-1-1-3-2-4-4-3-3-5-7-8-9l-9-6c-2-1-3-1-5-3z" class="K"></path><path d="M723 598l6 3-2 4h0l-5 6c1-1 1-3 2-4-1 1-1 1-2 1h0-2 0c0 1-1 2-1 2l-1 2h-1c-1 1-3 3-5 4s-4 3-7 3c-6 2-17-3-23-4l-12-4c-2 0-2 0-3-1h-3l-1 1c-2-1-4-2-5-4v-1l2-2c1 0 2 0 3 1l3 3c2 0 3 1 4 2l1-1c4 0 8-2 13-2 11-1 22-1 34-2l2 1h3v-1c1-1 1-1 1-2l-2-1v-1h0 1c1 1 1 1 2 1v-1l1-1c-1-1-2-1-3-1v-1z" class="k"></path><path d="M658 607v-1l2-2c0 2 2 3 4 4l1 1 2 1h-3l-1 1c-2-1-4-2-5-4z" class="J"></path><path d="M712 616l-1-2c-2 0-3 1-5 0h2c0-1 2-1 3-1 1-1 1-2 2-2h1 3v1c-1 1-3 3-5 4z" class="I"></path><path d="M723 598l6 3-2 4h0l-5 6c1-1 1-3 2-4-1 1-1 1-2 1h0-2 0c0 1-1 2-1 2l-1 2h-1v-1h-3c2-1 3-1 4-2l-1-1c-6-1-12-1-18-1-5 0-11 0-16 1-4 0-8 1-11 3l-2-1 1-1c4 0 8-2 13-2 11-1 22-1 34-2l2 1h3v-1c1-1 1-1 1-2l-2-1v-1h0 1c1 1 1 1 2 1v-1l1-1c-1-1-2-1-3-1v-1z" class="E"></path><path d="M334 201l1-5c1 1 1 2 2 3 0 2 0 3-1 5h-1c2 1 2 1 3 3 0 0 1-1 1-2v1l2-1 2 1h0v2 1l3 3c1 1 2 2 3 2 1 1 2 2 4 2l1 1c-3 1-5 1-7 2-8 1-15 1-23 0h-10c-3 0-7 2-10 1h-2v-1h-12l-1-1h4 10 3c1 0 1-1 2-2l2-1h-1l1-1v-1c5-2 11-6 16-9l-1-1c2-2 5-5 7-6l1 4h1z" class="a"></path><path d="M335 204c2 1 2 1 3 3l-3 1v2 1c-2-1-1-3-1-5 0-1 0-1 1-2z" class="F"></path><path d="M339 205v1 2 1c0 2 1 3 3 4h1 1c1 1 1 1 3 2v1h-1-1c-2-2-5-3-7-6l-1-1-1 1-1-2 3-1s1-1 1-2z" class="B"></path><path d="M334 201l1-5c1 1 1 2 2 3 0 2 0 3-1 5h-1c-1 1-1 1-1 2-1 2-2 3-4 3h-1v1h0v1l-1-1v-1c3-2 5-6 6-8z" class="C"></path><path d="M325 203c2-2 5-5 7-6l1 4h1c-1 2-3 6-6 8v1l-1-1v-1c-1-1-2 0-3 0-4 3-9 6-14 7h-1l1-1v-1c5-2 11-6 16-9l-1-1z" class="w"></path><path d="M325 203c2-2 5-5 7-6l1 4c-2 1-4 1-5 2s-1 2-3 3l-1 1c-1 1-2 1-3 2-3 1-6 3-9 4l-2 1v-1c5-2 11-6 16-9l-1-1z" class="g"></path><path d="M288 777c-7-12-10-29-9-42v-1c1-5 2-10 4-14 6-16 18-27 34-34-1 2-2 3-4 4-2 2-6 4-8 7l2 1c-8 8-14 17-17 29-1 3-1 5-2 8v-1c-2 1-1 4-2 6l-1-1v2c0 1 0 1-1 2v11h1v3c0 2 0 2 1 3v2 1c1 0 1 1 1 2 1 2 1 4 0 6 2 2 1 3 1 6z" class="X"></path><path d="M305 697l2 1c-8 8-14 17-17 29-1 3-1 5-2 8v-1c-2 1-1 4-2 6l-1-1c2-13 4-23 12-34 3-3 6-5 8-8z" class="Z"></path><path d="M802 248c0 1-1 1-2 2 0 0-1 1-1 2l-4 1-6 5-1 1h0c0 1 0 1-1 2h-1-1l-2 2-3 5 4 1h0c-1 1-2 1-3 1l-2-1c-1 1-2 2-3 4v1l1 1 1 1c-2 2-2 2-4 3l-4 1c-2 0-5 2-7 2l-1-1-1 1h2c-1 1-1 2-2 2s-2 1-3 2l-1 1c-1-2 0-3-1-4v1l-2 2-1-2h-3l2-2 2-6h0c0-3 3-5 5-7 1-2 2-2 4-3l-1-1v-1c5-2 10-6 15-7 1-1 2-1 3-2h1 0v-2l13-3c3 0 5-1 8-2z" class="T"></path><path d="M782 256v1l-1 1-2 2c-1 2-3 3-4 4v-1-2c1-2 2-1 3-3 0-1 3-2 4-2z" class="D"></path><path d="M778 258c-1 2-2 1-3 3v2 1c-3 2-6 5-9 7v-1h0l-2-3 1-3c4-2 9-5 13-6z" class="F"></path><path d="M765 264l-1 3 2 3h0v1c-4 3-7 6-10 9-1 2-2 2-3 3-1-1-1 0-1-1l2-6h0c0-3 3-5 5-7 1-2 2-2 4-3l2-2z" class="C"></path><path d="M765 264l-1 3c-3 1-5 4-6 7l-1 1v1c-1 1-1 1-1 2v2c-1 2-2 2-3 3-1-1-1 0-1-1l2-6h0c0-3 3-5 5-7 1-2 2-2 4-3l2-2z" class="B"></path><path d="M802 248c0 1-1 1-2 2 0 0-1 1-1 2l-4 1-6 5-1 1h0c0 1 0 1-1 2h-1-1l-2 2h-1c1-1 3-2 3-4v-1c0-1 0-2-1-3l-2 1c-1 0-4 1-4 2-4 1-9 4-13 6l-2 2-1-1v-1c5-2 10-6 15-7 1-1 2-1 3-2h1 0v-2l13-3c3 0 5-1 8-2z" class="S"></path><path d="M784 255h2 1c4 0 5-2 8-2l-6 5-1 1h0c0 1 0 1-1 2h-1-1l-2 2h-1c1-1 3-2 3-4v-1c0-1 0-2-1-3z" class="P"></path><path d="M780 218l78-1h13 4l1 1v1c-3 0-9 4-11 6l-3 5h-1c0-1 1-2 0-3-7-1-15 0-22 0h-32c-6 0-12 0-19-1h0 3 14 0c-1-2-2-3-3-3s-1-1-1-2c-1-1 0-1 0-2-2-1-3-1-5-1-4 0-12 1-15 0h-1z" class="Z"></path><path d="M324 440l1-1 7 17c2 5 4 10 6 14 1 4 2 7 4 10 0 2-1 2-1 3v2c-4 2-9 8-13 6-5-1-8-4-14-1-3 2-7 4-8 8-1 1 0 3 1 4 1 3 4 7 7 9 1 1 0 1 1 2 2 3 7 6 10 8-2 0-2-1-4-1-5-4-9-9-13-14h-1l-2-2v-1l2 2h0c-1-2-2-4-3-5 0-3 1-5 2-7 2-4 6-7 10-9 5-3 12-4 15-9 2-2 2-6 1-8 0-2-2-3-3-3-2-1-4-1-6 0v2l1 1c-1 1-1 2-2 3l-1-1c1 0 1-1 1-2 0-2 0-2-1-3-3-1-6-1-9 0-1 1 0 1-1 0 3-1 4-5 7-7s5-5 5-9c1-3 1-5 1-8z" class="M"></path><path d="M721 366c2-1 3-2 4-4l2 1 1-1c0-1 0-1-1-2l2-1 2 2c-1 1-1 2-1 4v1h0l-3 6c1 1 2 2 4 3h3 0l1 1c-1 0-2 1-4 1h0-3l-2-1c-3 4-13 31-13 31v3c-1 1-1 5-1 7l1 1c1 1 2 2 3 2l-1 1c-2 0-4 0-5 2l-3 4c-1 1-3 3-4 5-1 1-3 2-3 4h-2 0c-1 0-1 0-2-1h-1v1l-1-1c2-6 6-11 6-18 0-1 19-46 20-50l1-1z" class="AG"></path><path d="M698 436c-1-1-1-1-1-2h0c0-2 1-3 3-4l3-3c1-1 0-1 1-1h0l-1 2v4c-1 1-3 2-3 4h-2 0z" class="o"></path><path d="M704 426c2-3 4-7 5-10s2-6 4-9v3c-1 1-1 5-1 7l1 1c1 1 2 2 3 2l-1 1c-2 0-4 0-5 2l-3 4c-1 1-3 3-4 5v-4l1-2h0z" class="g"></path><defs><linearGradient id="C" x1="583.239" y1="876.13" x2="621.693" y2="886.624" xlink:href="#B"><stop offset="0" stop-color="#898788"></stop><stop offset="1" stop-color="#bab8b8"></stop></linearGradient></defs><path fill="url(#C)" d="M577 873h53c-1 1-1 1-2 1-4 0-4 2-4 5-1 3-1 7-3 10h10 0c-4 1-9 0-13 2h-4l-1-1c-2-1-6 0-8 0h-25l-12-1h12c0-2 0-4 1-6l2-2v-1h1l1-6h-7l-1-1z"></path><path d="M645 638c0-1 0-1 1-2h1 0l2 1c1 2 2 3 3 5 1 1 1 2 2 3h0c-1 0-2-1-2-2l-1 1v1 1c-1-2-1-2-3-3-1 1-1 2-1 3v1h0c0 1 1 2 2 3 4 2 8 1 12 1 7 0 12 7 15 12 2 2 4 4 5 7 1 2 1 4 1 6 0 1 0 1 1 2 1-2 1-4 1-5 0 1 0 1 1 2l1 1c0 2 0 4 1 6 0 5-1 9-2 13 0 3 0 6-1 9v-11-6c-2 2-2 5-2 7 0 3-1 7-1 11-1 0-1 1-1 1 0-4 1-9 1-14-4 6-6 12-10 16h-1c-2 2-4 5-6 7l2-7h-1c-1 1-1 1-1 2s0 1-1 2v1-2c1-2 1-2 1-4 0-1 0-3 1-4 0-3 1-6 2-8 2-2 5-4 7-7 2-2 3-4 4-6 0-4-2-8-4-12-3-5-10-12-17-14-2 0-4-1-6 0l-4 1-1-1c0-1-1-2-1-3l-2-2-1-2c-2-2-3-2-5-2l-1-1c-2-1-5 0-7 1-3 2-6 4-8 6-1-1-1-1 0-2 2-1 4-3 6-4 3-1 5-2 8-4v-2c1-2 1-2 3-3 1 0 2 1 3 1 1-1 2 0 3 0l1-1z" class="B"></path><path d="M645 638c0-1 0-1 1-2h1 0v3c-1 0-1 0-2-1z" class="E"></path><path d="M643 651v-1c0-2-2-4-3-5h-1c-1-1-1-2-2-2 1-2 2-2 3-2 2 2 3 2 4 4 0 2 0 2 1 2 0 1 0 1-1 2 0 1 0 2 1 3h2v1h-2l-2-2z" class="D"></path><path d="M666 708c3-7 6-11 12-16-2 6-4 11-8 16-2 2-4 5-6 7l2-7z" class="V"></path><defs><linearGradient id="D" x1="532.665" y1="872.227" x2="579.229" y2="890.427" xlink:href="#B"><stop offset="0" stop-color="#504d51"></stop><stop offset="1" stop-color="#7e7c7e"></stop></linearGradient></defs><path fill="url(#D)" d="M535 873h8c6 0 14-1 21 0h13l1 1h7l-1 6h-1v1l-2 2c-1 2-1 4-1 6h-12l-7 1h-12c-2 0-4-1-6 0h-1-2c-3 0-5-1-7 1-2 0-7 0-9-1l5-7c2-2 3-5 5-8l1-2z"></path><path d="M535 873h8c6 0 14-1 21 0l-30 2 1-2z" class="Y"></path><path d="M345 704c1-1 2-1 3-1l1 4-1 1c0 2 1 4 1 6-1 1-2 0-3-1v1l4 2c1 3 1 8 0 12-1 2-3 5-3 7-1 5-5 7-6 12l-5 5c-2-3-3-6-4-10l-2 2-1-1c-2-2-4-3-7-4l-1-1v-2-1c0-3 1-6 2-10 4-6 8-11 13-16 3-2 6-3 9-5z" class="k"></path><path d="M345 704c1-1 2-1 3-1l1 4-1 1-3-4z" class="D"></path><path d="M321 738v-2c4 1 9 3 11 6h0l-2 2-1-1c-2-2-4-3-7-4l-1-1z" class="K"></path><defs><linearGradient id="E" x1="444.067" y1="874.755" x2="395.083" y2="887.705" xlink:href="#B"><stop offset="0" stop-color="#888688"></stop><stop offset="1" stop-color="#c4c3c2"></stop></linearGradient></defs><path fill="url(#E)" d="M421 891h-12-14l5-1h-2l-1-1c-2-1-2-2-3-4l-1-5v-5-1c-1 0-2 0-3-1l64 1h-1-9v1c1 1 0 3 0 4v1c2 2 2 3 2 6v1 2c2 0 7 0 8-1l2 1-2 1h0l-1 1-1-1v1h-1-4c-3 1-7 1-10 0-4-1-7 1-11-1-2-1-3 0-5 1z"></path><path d="M426 890h11c3-1 5 0 7 0 2-1 3-1 5 1v-1c1 0 1 0 2 1h-4c-3 1-7 1-10 0-4-1-7 1-11-1z" class="i"></path><path d="M285 739l1 1c1-2 0-5 2-6v1c-1 12 1 23 6 34l-1 2c1 3 3 6 4 9 3 4 6 7 10 11 2 1 3 3 6 4 2 0 4 2 6 2 0 0 3 1 4 2l9 3c11 2 24 2 34-3 6-3 10-6 16-10l-1-1h0c3-3 6-9 11-10-4 12-10 20-20 26-9 4-18 6-27 7-6 0-11-1-17-3-17-4-31-16-40-31 0-3 1-4-1-6 1-2 1-4 0-6 0-1 0-2-1-2v-1-2c-1-1-1-1-1-3v-3h-1v-11c1-1 1-1 1-2v-2z" class="M"></path><path d="M381 788h0c3-3 6-9 11-10-4 12-10 20-20 26-9 4-18 6-27 7l-1-1c2-1 5 0 6-1h2 2c1 0 3-1 4-1h1l1-1h3c1 0 1-1 2-1 1-1 1 0 2-1h0 1c0-1-2 0-2-1h0l1-1h1c1 0 1-1 1-1l1-1h2 1 0v1h1v-1l3-2c2-2 4-3 5-5 1-1 3-2 3-3l2-3h0l1-1c1-2 2-5 2-7l-4 4h1l1 1-1 1c-2 1-3 3-5 3h0l-1-1z" class="Z"></path><path d="M285 739l1 1c1-2 0-5 2-6v1c-1 12 1 23 6 34l-1 2c1 3 3 6 4 9h-1c-1-2-2-5-3-7v-1c-1-2-1-2-1-4v-1-1c-1-1-1-2-1-3l-1-4c-1-1-1-1-1-2l-1-2v-3h0l-1 1h-1v-3c0 3 0 6 1 9v6c0-1 0-2-1-2v-1-2c-1-1-1-1-1-3v-3h-1v-11c1-1 1-1 1-2v-2z" class="B"></path><path d="M490 117c5-3 10-5 15-7 3-2 6-3 10-4v7l1 3c0 1 0 4-1 5v7 1h1l-1 3c1 3 0 5 0 7v8 5c0 2 0 4 1 6v1c-2 0-3 0-4 1h0l-1 1v-2c0-1 1-1 2-1v-1l1-1v-5c0-1 0-2-1-3 0-1-2-1-3-2 0 0 0-1-1-2-1 0-1 1-2 1 0-1-1-2-2-3l-3-3c-1 0-2 0-4-1h-2c-1 0-1-1-2-1h-1v-1c-1-1-2-1-4-1l-1-1-2 2c1 0 1 1 2 1-2 0-4 0-6 2l-1 2c0-2 0-3-1-4l-2-8c0-1-1-2-1-4h0 1 1c2-2 4-3 7-4l2-1 2-1 2-1 1-1h-3z" class="s"></path><path d="M497 128l1 1c0 1 0 1 1 1 0 1 1 2 2 2-1 0-2 0-3-1h-3 0v-2l2-1z" class="AA"></path><path d="M502 129h0v3l2 1-1 1c0-1-1-1-2-2-1 0-2-1-2-2-1 0-1 0-1-1 1 1 0 0 1 0l2 1 1-1z" class="q"></path><path d="M504 133l1-1c2 0 3 4 4 3h1c0 1-1 1-2 2l-1-1v-1c-1 1-2 1-3 1l-1-2 1-1z" class="AA"></path><path d="M511 136c1 0 2 1 3 2l1 1v8 5c0 2 0 4 1 6v1c-2 0-3 0-4 1h0l-1 1v-2c0-1 1-1 2-1v-1l1-1v-5-4-1-5h-1c-1-1-2-3-2-5z" class="q"></path><path d="M500 126c1 1 2 1 3 1 1 1 2 1 3 1l2 1 1-1v1c1 0 2 0 3 1v-1h3 1l-1 3c1 3 0 5 0 7l-1-1c-1-1-2-2-3-2l-1-1h-1c-1 1-2-3-4-3l-1 1-2-1v-3h0c-1-1-1-2-2-3z" class="v"></path><path d="M508 129l1-1v1c1 0 2 0 3 1v-1h3 1l-1 3-1 1c-2-1-2-1-2-2-1-1-3-1-4-1v-1z" class="U"></path><path d="M502 129h2c2 1 3 3 6 3 1 0 3 2 4 3v3c-1-1-2-2-3-2l-1-1h-1c-1 1-2-3-4-3l-1 1-2-1v-3z" class="z"></path><path d="M496 116l-1 1c2 0 2 0 3 2h1l1-1v3h1c1 0 2 0 2-1l1 1-2 1 3 1c0 3 1 3 1 5-1 0-2 0-3-1-1 0-2 0-3-1 1 1 1 2 2 3l-1 1-2-1c-1 0 0 1-1 0l-1-1v-1c-2 0-3 0-4-2l-1-1-2 1c-1-1-1 0-1-1v-2l-1-2 2-1 2-1 1-1 3-1z" class="AE"></path><path d="M490 119c1 0 2 0 3 1l-1 2h0l2 1v1l-1 1-1-1-2 1c-1-1-1 0-1-1v-2l-1-2 2-1z" class="z"></path><path d="M496 116l-1 1c2 0 2 0 3 2h1l-2 2c1 1 1 0 2 2l-1 1h1v2c-2-1-4-1-5-4-1-2 2-2 2-4-1 1-3 0-4 0l1-1 3-1z" class="Y"></path><path d="M499 119l1-1v3h1c1 0 2 0 2-1l1 1-2 1 3 1c0 3 1 3 1 5-1 0-2 0-3-1-1 0-2 0-3-1h-1v-2h-1l1-1c-1-2-1-1-2-2l2-2z" class="U"></path><path d="M504 121c4-3 7-7 11-8l1 3c0 1 0 4-1 5v7 1h-3v1c-1-1-2-1-3-1v-1l-1 1-2-1c0-2-1-2-1-5l-3-1 2-1z" class="V"></path><path d="M515 121v7 1h-3v1c-1-1-2-1-3-1v-1l-1 1-2-1c0-2-1-2-1-5 1 0 3 1 5 2v1 1h2c1-1 0-1-1-2h2 1l1-4z" class="i"></path><path d="M490 117c5-3 10-5 15-7 3-2 6-3 10-4v7c-4 1-7 5-11 8l-1-1c0 1-1 1-2 1h-1v-3l-1 1h-1c-1-2-1-2-3-2l1-1-3 1h-3z" class="c"></path><path d="M496 116l9-3c-2 2-3 4-5 5l-1 1h-1c-1-2-1-2-3-2l1-1z" class="f"></path><path d="M486 121l2-1 1 2v2c0 1 0 0 1 1l2-1 1 1c1 2 2 2 4 2v1l-2 1v2h0l4 3h0 2l1 1h1c1 1 1 2 2 3l1-1c2 2 0 0 1 2 2 1 4 1 4 4l1-1h1c0 1 0 2 1 3v1 1 4c0-1 0-2-1-3 0-1-2-1-3-2 0 0 0-1-1-2-1 0-1 1-2 1 0-1-1-2-2-3l-3-3c-1 0-2 0-4-1h-2c-1 0-1-1-2-1h-1v-1c-1-1-2-1-4-1l-1-1-2 2c1 0 1 1 2 1-2 0-4 0-6 2l-1 2c0-2 0-3-1-4l-2-8c0-1-1-2-1-4h0 1 1c2-2 4-3 7-4z" class="x"></path><path d="M486 130c1 0 1 0 2 1h3l1 1c2 1 3 2 5 3h-3c-1 0-2-1-2-1h-3v-2l-2 1h-1l1-1-1-2zm17 8h2v1c0 1 1 2 3 3h2v2c2 1 2 2 4 3v4c0-1 0-2-1-3 0-1-2-1-3-2 0 0 0-1-1-2-1 0-1 1-2 1 0-1-1-2-2-3l-2-4z" class="h"></path><path d="M489 134h3s1 1 2 1h3c3 1 4 1 6 3l2 4-3-3c-1 0-2 0-4-1h-2c-1 0-1-1-2-1h-1v-1c-1-1-2-1-4-1v-1z" class="m"></path><path d="M490 125l2-1 1 1c1 2 2 2 4 2v1l-2 1v2c-1-1-1-1-2-1-3 0-4-1-6-2-1-1 0 0-1 0s-2-1-2-1l-2-2h0 1l2 2h1c1-1 2 0 3 0h1c1-1 1-1 0-2z" class="s"></path><path d="M490 125l2-1 1 1c1 2 2 2 4 2v1l-2 1c-2-1-2 0-3-2h-1-1c1-1 1-1 0-2z" class="q"></path><path d="M486 121l2-1 1 2v2c0 1 0 0 1 1s1 1 0 2h-1c-1 0-2-1-3 0h-1l-2-2h-1-3c2-2 4-3 7-4z" class="AA"></path><defs><linearGradient id="F" x1="481.512" y1="138.615" x2="483.476" y2="129.882" xlink:href="#B"><stop offset="0" stop-color="#0f0d10"></stop><stop offset="1" stop-color="#2c292f"></stop></linearGradient></defs><path fill="url(#F)" d="M478 129h1 6 1v1h0l1 2-1 1h1l2-1v2 1l-1-1-2 2c1 0 1 1 2 1-2 0-4 0-6 2l-1 2c0-2 0-3-1-4l-2-8z"></path><defs><linearGradient id="G" x1="510.378" y1="873.28" x2="445.99" y2="889.122" xlink:href="#B"><stop offset="0" stop-color="#484449"></stop><stop offset="1" stop-color="#7e7c7f"></stop></linearGradient></defs><path fill="url(#G)" d="M484 873l24 1c1 1 2 2 2 3 2 3 3 7 5 10l2 2h-15-6c-2 0-5 0-7 1h-2l-2 1c0-1 0-1-1-1h-1v1h-1v-1c-4-1-8-1-12 0h-1c-5-1-9-1-13-1l-2-1c-1 1-6 1-8 1v-2-1c0-3 0-4-2-6v-1c0-1 1-3 0-4v-1h9 1l30-1z"></path><defs><linearGradient id="H" x1="381.899" y1="755.235" x2="333.192" y2="786.905" xlink:href="#B"><stop offset="0" stop-color="#c10d0a"></stop><stop offset="1" stop-color="#f22428"></stop></linearGradient></defs><path fill="url(#H)" d="M314 736c3 0 5 1 7 2l1 1c3 1 5 2 7 4l1 1 2-2c1 4 2 7 4 10l5-5h1c-3 4-10 12-10 17 3 0 5-2 8-3h0l-8 6c3 8 7 12 15 16 3 1 6 2 9 2h1c10 1 20-2 27-8 4-3 6-7 9-11 0 4 0 9-1 12-5 1-8 7-11 10h0c-7 4-16 5-24 4-11-1-23-5-29-14-4-3-7-8-9-12-4-9-6-20-5-30z"></path><path d="M319 766c1 0 1 0 1-1 2-1 2-6 2-9 0-1 0-1 1-2l2 4c-1 2-1 3 0 6 2 3 3 6 4 9h0l-1-1v-1c-1-1-1-2-2-2h-1c0 1-1 2-2 2 1 1 1 2 2 3h0c1 2 3 2 3 4-4-3-7-8-9-12z" class="Q"></path><path d="M323 754c-1-3-2-5-1-7 0-1 1-1 1-2s0-1-1-1c-1-2-1-3-1-4l1-1c3 1 5 2 7 4l1 1c2 3 4 6 4 10-1 2-4 6-7 7-1-1-2-2-2-3l-2-4z" class="k"></path><path d="M314 736c3 0 5 1 7 2l1 1-1 1c0 1 0 2 1 4 1 0 1 0 1 1s-1 1-1 2c-1 2 0 4 1 7-1 1-1 1-1 2 0 3 0 8-2 9 0 1 0 1-1 1-4-9-6-20-5-30z" class="o"></path><path d="M295 600h0c9-4 18-11 24-20 3-5 4-11 7-16-1 6-4 16-2 22h0v1h5v1l-1-1c0 1 0 1-1 1v1c-9 3-18 8-25 15 11 3 20 2 31 2 2 1 5 0 7 1h0c1 1 2 1 4 1h1c1 1 3 1 4 1h2c1 1 1 2 1 3l-9 2 1 1c-7 3-15 6-22 10 5 5 10 8 15 11l-1 1c-4-3-9-6-14-8h-1c0 5 3 9 5 14h0c-6-9-13-17-21-23l-7-6-1-1c-1-2-1-3-1-6-1 0 0 0-1-1-2-2-1-3 0-6z" class="F"></path><path d="M296 607c2 3 4 5 6 7l3 3v3l-7-6-1-1c-1-2-1-3-1-6z" class="g"></path><path d="M343 614c-7 2-20 8-27 5-3-1-10-5-12-9l1-1c1 0 2-1 2-1 4-1 9-1 13-1h20c1 1 2 1 4 1h1c1 1 3 1 4 1h2c1 1 1 2 1 3l-9 2zm29-5c1 0 3 0 4 1l-1 7c-1 4-4 6-6 9 2-1 4-2 6-4 1-1 2-3 3-3 1-1 2-1 3 0v1c1 1 1 1 0 3 2-1 2-1 3-2h0c2 0 0 0 2-1h3l-4 15-1-1-1 2h-4l-1-1c-2-2-7-1-9-1-3-1-5 0-8 0-7 1-14 8-19 13-2-1-3-2-4-4 0 0 1-2 1-3l-3-3 1-1c-5-3-10-6-15-11 7-4 15-7 22-10l22-5 4-1h2z" class="k"></path><path d="M361 634c6-2 11-4 16-8v2c-1 2-2 3-5 4-1 0-2 1-3 2-3-1-5 0-8 0h0z" class="K"></path><path d="M361 634h0c-7 1-14 8-19 13-2-1-3-2-4-4 0 0 1-2 1-3l-3-3 1-1 4 2c4 1 9-1 13-2 2-2 4-2 7-2z" class="B"></path><path d="M381 620c1 1 1 1 0 3 2-1 2-1 3-2h0c2 0 0 0 2-1h3l-4 15-1-1-1 2h-4l-1-1c-2-2-7-1-9-1 1-1 2-2 3-2 3-1 4-2 5-4v-2c2-2 3-4 4-6z" class="G"></path><path d="M381 620c1 1 1 1 0 3 2-1 2-1 3-2h0c2 0 0 0 2-1h3l-4 15-1-1-1 2h-4l-1-1h3c1 0 1 0 2-1 1-2 2-6 2-8 0-1-1-2-2-2-2 0-4 2-6 4v-2c2-2 3-4 4-6z" class="P"></path><path d="M341 485l3 2c3 3 4 4 6 8l-1 2c-1 0-1 0-2 2v1c1 1 2 1 3 3 0 1 1 3 1 4-3 1-6 2-8 2-1 1-2 1-2 1l1 2c4 0 6 0 9 3 1 1 1 2 1 3 2 0 2 0 4 1v4c0 2 0 2-2 4h-5v-2c-1 1-1 2-2 2-1 1-3 2-4 2-2 0-5-2-7-2l-2-2c-1 0-3-1-4-1l-5-3c-3-2-8-5-10-8-1-1 0-1-1-2-3-2-6-6-7-9-1-1-2-3-1-4 1-4 5-6 8-8 6-3 9 0 14 1 4 2 9-4 13-6z" class="k"></path><path d="M342 512c-2 0-4 0-6-1-2 0-5-1-7-1-4 0-8 1-11 1 2-1 4-1 6-2 3-1 6-1 9-1l-4-4c3 1 4 4 6 5 2 0 4 0 6-1l2 1c-1 1-2 1-2 1l1 2z" class="J"></path><path d="M763 184l2-1c5 8 7 18 5 28 0 2-1 4-2 6h8c1 0 3 0 4 1h1c3 1 11 0 15 0 2 0 3 0 5 1 0 1-1 1 0 2 0 1 0 2 1 2s2 1 3 3h0-14-3 0-51 0l-1-1c-2-1-9-1-11-1h-46c2 0 4-1 6-1-2 0-5 0-7-1-1-1-1-2-2-2-4 0-8 0-11-1h0 8c1-1 3 0 4 0h0c4 0 6 0 10-1h0c2 1 5 1 7 0h-1c-1 0-2 0-2-1h-1l-1 1-2-2h1c2-1 3-2 5-3v-1h0v-1c1-1 1-2 2-3-1-1-1-2-1-3h1v2-2c-1-2-1-4-2-6-1-1-1-2-2-2h-1c-2-3-3-5-4-8 3 2 7 4 9 7 4 6 9 11 15 14l9-3c-1 1-3 3-5 4l9 6c4 1 9 0 14 1 7 0 18 1 24-2 3-1 6-4 7-7 2-8-1-18-5-25z" class="B"></path><path d="M361 634c3 0 5-1 8 0 2 0 7-1 9 1l1 1h4l1-2 1 1-1 5c-1 4-2 10-6 13-2 1-4 2-6 2l-2 2c0 1 0 2 1 3 2 1 3 0 5 0h0l1 1c-1 6-3 10-6 15-3 1-17 2-19 3l-3-1c-1-1-1-2-1-4l-1 1 1 1c-1-1-2-1-3-1s0 0-2-1v-2l-2 2-2 2 1-4c0-4 0-9-1-13 1-4 1-9 3-12 5-5 12-12 19-13z" class="k"></path><path d="M367 635c1 0 1 0 2 1v1c-2 1-4 2-5 3-3 2-6 5-9 7h-2v-1l2-1c3-2 4-5 6-7l6-3z" class="F"></path><path d="M378 635l1 1h4l1-2 1 1-1 5c-1 0-3 1-3 3l-1 2-1 1v-3c0-1-1-2-1-3h-2c-2 0-3 1-4 3h0c1-3 4-5 6-8h0z" class="Z"></path><path d="M379 643v3l1-1 1-2c0-2 2-3 3-3-1 4-2 10-6 13-2 1-4 2-6 2l-1-1c0-1 0-1-1-2l-1 1-1-1c2 0 5-1 7-2 3-2 3-4 4-7z" class="B"></path><path d="M343 672c1-3 4-5 5-8 1-2 1-4 2-6s3-5 5-8v1l-6 12c3-3 5-7 9-10 1 1 1 1 2 1s4-2 5-3 1 0 2-1l1 2 1 1 1-1c1 1 1 1 1 2l1 1-2 2c0 1 0 2 1 3 2 1 3 0 5 0h0c-3 1-6 2-8 1-1 0-1-1-2-1-4 0-8 4-10 7l-4 4c-1 1-2 3-4 3l-1 1 1 1c-1-1-2-1-3-1s0 0-2-1v-2z" class="J"></path><path d="M352 671l-1-2c1-2 2-3 3-4l2 2-4 4z" class="B"></path><path d="M371 654l1 1-2 2c0 1 0 2 1 3 2 1 3 0 5 0h0c-3 1-6 2-8 1-1 0-1-1-2-1-4 0-8 4-10 7l-2-2 6-4c1 0 1 0 2-1 1 0 1 0 1-1 2 0 2-1 4-1l1 2h1v-4l2-2z" class="C"></path><path d="M376 660l1 1c-1 6-3 10-6 15-3 1-17 2-19 3l-3-1c-1-1-1-2-1-4 2 0 3-2 4-3l4-4c2-3 6-7 10-7 1 0 1 1 2 1 2 1 5 0 8-1zm315-186l1 1c2 2 3 4 6 5 1 0 1 0 2-1 7 4 16 8 19 16 1 3 0 7-2 9-1 2-2 4-3 5h-1c-4 4-8 10-14 11l-6 3h0l1 2c-5 2-9 3-14 4v-1l-4-1c-1-3-2-4-4-5 0-1 1-1 2-2l-1-3 1-4v-3c1-1 2-2 2-3l1-1h-1c0-2 1-4 2-5s1-1 2-1c-1-1-1-1-3-1 0-4 4-12 6-15v-1l4-9c2 1 2 0 4 0z" class="k"></path><path d="M672 522c0-1 1-1 2-2 2 3 3 5 6 6 5 0 9-1 13-3l1 2c-5 2-9 3-14 4v-1l-4-1c-1-3-2-4-4-5z" class="D"></path><path d="M677 506v1c2 1 4 2 6 1h1v-2c1 1 1 2 1 3 2 0 4 0 6-1 1 0 1-1 1-1l3-3c-1 2-2 2-2 4 3 1 8 2 11 3h-5-1-1c-3-1-5 0-8 0v1l1 1c-2 0-3-1-4-2h-2-2l-1 1-1-1c-2 0-4 1-6 2h0v-3c1-1 2-2 2-3l1-1z" class="H"></path><defs><linearGradient id="I" x1="686.086" y1="476.009" x2="712.422" y2="497.404" xlink:href="#B"><stop offset="0" stop-color="#b20108"></stop><stop offset="1" stop-color="#e72022"></stop></linearGradient></defs><path fill="url(#I)" d="M691 474l1 1c2 2 3 4 6 5 1 0 1 0 2-1 7 4 16 8 19 16 1 3 0 7-2 9-1 2-2 4-3 5h-1l1-2c2-2 5-6 4-9-1-4-5-6-8-8-2 0-5-2-7-1-3 0-7 2-9 3-4-2-7-5-11-8v-1l4-9c2 1 2 0 4 0z"></path><path d="M651 627c1-1 1-3 2-4 0-1 1-1 1-2h-2c-2-1-2-3-2-4l-2-5c2-2 3-4 6-5 1-1 2 0 4 0 1 2 3 3 5 4l1-1h3c1 1 1 1 3 1l12 4-1 1c7 2 14 5 20 9-5 5-13 11-19 14l-15-3 9 6c3 2 6 3 7 7 1 2 1 6 2 8l-1 16c0 1 0 3-1 5-1-1-1-1-1-2 0-2 0-4-1-6-1-3-3-5-5-7-3-5-8-12-15-12-4 0-8 1-12-1-1-1-2-2-2-3h0v-1c0-1 0-2 1-3 2 1 2 1 3 3v-1-1l1-1c0 1 1 2 2 2h0c-1-1-1-2-2-3-1-2-2-3-3-5l2-2v-3-2h2 0c1 1 1 0 2 1l1-1c-1-1-3-2-5-3z" class="k"></path><path d="M663 642l2-2c2 3 3 4 5 6l-1 1c3 4 5 7 7 12h-1c-2-2-3-6-5-9s-5-5-7-8zm4-32c1 1 1 1 3 1l12 4-1 1c-2-1-10-4-11-3l-2 2v-2c-2 0-3-1-5-2l1-1h3z" class="X"></path><path d="M651 630h2 0c1 1 1 0 2 1l1-1c2 1 5 3 7 4 1 2 2 4 2 6h0l-2 2c-2-2-5-3-7-4h-1l-2-2-2-1v-3-2z" class="T"></path><path d="M651 630h2 0c1 1 1 0 2 1 0 1 1 1 1 2s0 1-1 1l-2 2-2-1v-3-2z" class="Z"></path><defs><linearGradient id="J" x1="467.21" y1="755.078" x2="421.422" y2="815.16" xlink:href="#B"><stop offset="0" stop-color="#a30c12"></stop><stop offset="1" stop-color="#cc0e16"></stop></linearGradient></defs><path fill="url(#J)" d="M375 692c5 0 13 2 17 5 3 1 6 4 8 6l15 11c2 3 5 5 8 7 12 10 24 22 33 35 9 12 17 26 24 40l17 38c5 9 6 12 6 12 3 7 7 15 11 22 2 4 3 9 6 13l1 1 1-1c0 2 0 6-1 8-1 0-2 1-2 1l-2-1-2-2c-2-3-3-7-5-10 0-1-1-2-2-3l-23-49c-9-20-18-41-30-59-4-5-8-11-13-15-2 2-4 4-5 6v7c0 4 0 8-1 12 0-6 1-16-2-21-1-1-3-3-3-5 1 2 2 3 2 4 1 1 1 2 3 2 2-2 3-4 5-7a25.03 25.03 0 0 0-13-12c-1 1-2 2-2 4 0 4 2 5 5 8-2-1-4-2-5-4-1-1-1-1-1-3l-1-1v-1-1c-1 0-1 0-2 1s-2 1-3 1c3-1 6-4 8-6-1-2-3-3-4-5-2-2-4 0-7 0v-5c-4-4-8-5-13-7l1-1c-2-3-4-3-7-4l-1 1v-2-1l-2-1v-1c-2-1-4-2-5-2h-1c0-1 0-2-1-2-9-7-18-7-29-6-1-1-2-2-3-2 2-1 4-2 6-1h1c2-1 3-2 6-2h4v-1c0-1 2 0 3-1z"></path><path d="M510 877c2 1 2 0 3 2 1 1 1 2 1 4h0c0 2 1 2 1 4-2-3-3-7-5-10z" class="D"></path><path d="M381 695c3 0 5 2 8 2h3c3 1 6 4 8 6-4-1-8-3-12-5-1 0-2 0-3-1-2 0-3-1-4-2z" class="R"></path><path d="M375 692c5 0 13 2 17 5h-3c-3 0-5-2-8-2-2 1-7-1-9-1v-1c0-1 2 0 3-1z" class="G"></path><path d="M387 216c2 0 4 1 6 0s2-2 3-4l5 5h1l1 1h12 11l1 1h-1c-4 1-8 4-10 8h0-1l-8-1h-16-46-3c1 0 1 0 2-1h1-44-2v1h-8 0-113-14-2c-2 0-2-1-3-2l-1-1c-2 0-6-4-7-5v-1l80 1c4 1 12 2 17 2l9-1h9 0v-2c3 0 5 0 8-1 4 0 8 0 12 1 2 0 5 0 7 1h-4l1 1h12v1h2c3 1 7-1 10-1h10c8 1 15 1 23 0 2-1 4-1 7-2l1-1 1 1c3 1 7 1 10 1h1l2-1c1 0 5 0 6 1 2 0 4 0 6-1s3-1 6-1z" class="D"></path><path d="M415 218h11l1 1h-1c-4 1-8 4-10 8h0-1l-8-1c-1-1-4-1-6-1h0 16c0-2 1-2 1-3 1-1 0-2 0-3h0l-3-1z" class="T"></path><path d="M403 218h12l3 1h-5c-3-1-9-1-12 0-2 0-6 2-9 1h0c-1 1-3 1-4 1 1 0 2 0 3 1h1 1c3 1 6 1 9 1h0l-1 1c-4-2-10-1-15-2h0l-2 1c-1 0-1 0-2-1l1-2c2-1 3 0 5 0s2 1 3-1c3 1 9-1 12-1z" class="Z"></path><path d="M301 225c6-1 11-1 16-1l23 1h43c4 0 9 0 13 1h-5 0-46-3c1 0 1 0 2-1h1-44z" class="b"></path><path d="M274 216c4 0 8 0 12 1 2 0 5 0 7 1h-4l1 1h-1l4 1-1 1c-2 0-6 0-8-1-1 0-3 1-4 1-2 0-7 0-9-1h-3 0-2l-4 2v-1c-2 0-2-1-4 0v-1h-10l9-1h9 0v-2c3 0 5 0 8-1z" class="F"></path><path d="M266 219l23-1 1 1h-1 0c-4 0-10 1-14 0h-9 0z" class="E"></path><path d="M274 216c4 0 8 0 12 1 2 0 5 0 7 1h-4l-23 1v-2c3 0 5 0 8-1z" class="Q"></path><path d="M159 224c4 1 9 0 13 0l27 1h100v1h-8 0-113-14-2c-2 0-2-1-3-2z" class="w"></path><path d="M159 224c4 1 9 0 13 0l27 1c-4 1-9 0-13 0h-4c4 1 10 1 15 1h0-12-7-14-2c-2 0-2-1-3-2z" class="o"></path><path d="M278 320v-1c1-1 1-2 1-2l1-1c1 1 1 1 2 3v2h1v-2c1 0 1 0 1 1 1 1 1 2 1 3 1 0 1 0 1 1 1 2 1 2 2 3l-1-4h1v-2c0 1 0 1 1 2v1h1 0l5 13 2 5 5 13c1 1 1 2 1 3 2 2 2 4 4 6l1-1 2 6c0-3-1-6 1-8 2 3 4 6 5 10v-3l2 5 1 2 3 7c0 1 1 2 1 3 0 0 1 1 1 2l1 1c0 2 1 2 2 3 1 2 1 2 2 3l1 1c1 2 2 3 4 4 1 0 1 1 1 2l2 3c2 4 3 8 4 12s2 7 4 11v3 2c-2 0-6-4-8-6-1 0-3-3-4-4l-3-4-1 4 1 3c1 1 1 3 1 5 3 8 8 16 11 25l13 29v1c0 2 0 3 1 4 0 1 1 2 1 3l1 2h-1c-1-3-2-6-5-9l-13-30-4-9c-1-1-2-1-2-2-1 2-1 4-1 6 2 2 2 2 2 4l-1-1c-1 1-1 1-1 2v1l-7-17-1 1v-1c-1-3-2-6-3-8h0l-2-3-2-3h0 0l-1-3c-1-1-1-3-2-4l-1-2c0-2-1-3-2-4l1-1c1-2-1-7-1-9-1-1-1-3-2-4l-5-11-5-12v-1c-1-5-4-11-6-15l-9-21c-2-4-6-12-6-15 1-1 0-2 0-3z" class="AG"></path><path d="M302 367l1-1v1l1 1c0 1 0 1 1 1v2h1c2 2 4 6 5 9l1 2v1c0 2 0 3 1 5s1 4 2 6c0 2 1 3 2 5h-1c-1-2-2-5-3-7l-11-25z" class="C"></path><path d="M339 455v-1c-2-6-5-11-7-17-1-2-2-3-2-5v-1l1 1c0 1 1 3 2 5v1l1 1c1 2 1 3 2 5 1 0 0 1 1 2 0 0 0 1 1 1v2c1 2 2 4 3 5v1h1l13 29v1c0 2 0 3 1 4 0 1 1 2 1 3l1 2h-1c-1-3-2-6-5-9l-13-30z" class="b"></path><path d="M312 383l7 13 2 5 2 2c1 1 1 3 2 5l1 2 4 8-1 4 1 3c1 1 1 3 1 5l-15-31h1c-1-2-2-3-2-5-1-2-1-4-2-6s-1-3-1-5z" class="X"></path><path d="M321 401l2 2c1 1 1 3 2 5l1 2h-3c0-3-2-6-2-9z" class="D"></path><path d="M323 410h3l4 8-1 4-6-12z" class="J"></path><path d="M322 416c3 5 5 11 7 16s4 9 6 14c-1-1-2-1-2-2-1 2-1 4-1 6 2 2 2 2 2 4l-1-1c-1 1-1 1-1 2v1l-7-17-1 1v-1c-1-3-2-6-3-8h0c2 1 3 4 4 6 2-5-2-9-2-14 0-2-1-4-1-7z" class="Q"></path><path d="M312 398h1c1 2 9 16 9 18 0 3 1 5 1 7 0 5 4 9 2 14-1-2-2-5-4-6l-2-3-2-3h0 0l-1-3c-1-1-1-3-2-4l-1-2c0-2-1-3-2-4l1-1c1-2-1-7-1-9h0 1v2-2-1c0-1-1-2 0-3z" class="b"></path><path d="M311 402h0 1v2-2-1c0-1-1-2 0-3 0 2 1 3 1 5s2 5 3 7c2 4 3 7 5 11v2c0 2-1 3-2 5l-2-3h0 0l-1-3c-1-1-1-3-2-4l-1-2c0-2-1-3-2-4l1-1c1-2-1-7-1-9z" class="o"></path><path d="M278 320v-1c1-1 1-2 1-2l1-1c1 1 1 1 2 3v2h1v-2c1 0 1 0 1 1 1 1 1 2 1 3 1 0 1 0 1 1 1 2 1 2 2 3l-1-4h1v-2c0 1 0 1 1 2v1h1 0l5 13 2 5 5 13c1 1 1 2 1 3 2 2 2 4 4 6 0 1 1 3 1 4v1l-1-1v1c0-1-1-1-1-2h-1l1 4h-1v-2c-1 0-1 0-1-1l-1-1v-1l-1 1c-1-1-1-3-2-5l-5-10c-1-1-2-2-3-4-1-3-4-6-6-9s-3-7-5-10l-3-6c1-1 0-2 0-3z" class="B"></path><path d="M281 329v-1-6l3 3c0 1 2 2 3 3v3c0 2 1 3 2 5v3l1 1h0c0 1 0 1 1 2l1 2c1 2 1 3 2 4 0 2 1 3 1 4-1-1-2-2-3-4-1-3-4-6-6-9s-3-7-5-10z" class="X"></path><path d="M287 323h1v-2c0 1 0 1 1 2v1h1 0l5 13 2 5 5 13c1 1 1 2 1 3 2 2 2 4 4 6 0 1 1 3 1 4v1l-1-1v1c0-1-1-1-1-2h-1l-3-7c-2-4-4-8-5-12l-9-21-1-4z" class="H"></path><path d="M303 358c2 2 2 4 4 6 0 1 1 3 1 4v1l-1-1v1c0-1-1-1-1-2h-1l-3-7c2 1 2 3 4 5v-1l-1-1c-1-1-1-3-2-5h0z" class="R"></path><path d="M310 369c0-3-1-6 1-8 2 3 4 6 5 10v-3l2 5 1 2 3 7c0 1 1 2 1 3 0 0 1 1 1 2l1 1c0 2 1 2 2 3 1 2 1 2 2 3l1 1c1 2 2 3 4 4 1 0 1 1 1 2l2 3c2 4 3 8 4 12s2 7 4 11v3 2c-2 0-6-4-8-6-1 0-3-3-4-4l-3-4-4-8-1-2c-1-2-1-4-2-5l-2-2-2-5-7-13v-1l-1-2c-1-3-3-7-5-9l-1-4h1c0 1 1 1 1 2v-1l1 1v-1c0-1-1-3-1-4l1-1 2 6z" class="t"></path><path d="M319 392c2 4 4 7 4 11l-2-2-2-5v-4z" class="P"></path><path d="M312 382c1 0 1 0 1-1v-1l1-1 5 13v4l-7-13v-1z" class="T"></path><path d="M307 364l1-1 2 6c1 3 3 7 4 10l-1 1v1c0 1 0 1-1 1l-1-2c-1-3-3-7-5-9l-1-4h1c0 1 1 1 1 2v-1l1 1v-1c0-1-1-3-1-4z" class="G"></path><path d="M305 367h1c0 1 1 1 1 2v-1l1 1v-1c1 2 2 4 2 6l1 2h0c0 1 0 1 1 2 0 1 0 1-1 2-1-3-3-7-5-9l-1-4z" class="P"></path><path d="M316 368l2 5 1 2 3 7c0 1 1 2 1 3 0 0 1 1 1 2l1 1c0 2 1 2 2 3 1 2 1 2 2 3l1 1c1 2 2 3 4 4 1 0 1 1 1 2l2 3-2-2c-2 0-2-3-4-3l-1 2h-1c-2-5-5-10-7-15s-3-10-6-15v-3z" class="u"></path><path d="M325 408c0-3 0-5-1-8v-2h1c3 0 4 5 6 7 2 3 6 6 8 9 0 2 0 3 1 5 1 1 1 3 1 5l-1 1c1 1 2 2 2 3l-1-1c-1 0-3-1-4-1s-3-3-4-4l-3-4-4-8-1-2z" class="d"></path><path d="M622 631c2 2 2 2 3 5l-3 5v5l1 1-2 4c-1 1-1 1 0 2l-1 1c0 1-1 2-1 2-3 3-3 8-6 11l1 2 2 1 2 2c3 0 5 1 8 2h1 1c-2 2-3 3-6 3h-3v3c-4-1-7-1-10 0l-2 5-12 33-1 2c-2 4-3 7-4 10v2s0 1-1 1c-2 5-5 11-6 16-1 1-1 2-1 3 3 0 6 0 8 1l6 6c-3-2-7-6-11-5-2 1-4 3-6 5-3 5-5 11-7 17l-10 26-27 71-1 2c-2 3-3 6-5 8l-5 7c-1 1-2 1-4 1h-1v-1s1-1 2-1c1-2 1-6 1-8l-1 1-1-1c2-2 4-7 4-10l89-235h1l1-2c1 0 1 0 1 1h1v1l-1 1c0 1-1 1-1 2v2c2-4 3-8 7-10z" class="r"></path><path d="M611 674c0-3 1-5 2-7l1 2 2 1 2 2h-2l-1 1h0l-1 1c0 1 0 1-1 2-1 0-2 1-3 1l1-3z" class="H"></path><path d="M616 670l2 2h-2l-1 1h0l-1 1c0 1 0 1-1 2-1 0-2 1-3 1l1-3s1 0 2-1c1 0 2-2 3-3z" class="G"></path><path d="M589 709v3h1 0c-1 4-3 10-6 13h-1l6-16z" class="u"></path><path d="M613 667c1-3 1-7 2-10 0-4 2-8 3-11 2-1 3-3 4-5v5l1 1-2 4c-1 1-1 1 0 2l-1 1c0 1-1 2-1 2-3 3-3 8-6 11z" class="n"></path><path d="M618 672c3 0 5 1 8 2h1 1c-2 2-3 3-6 3h-3v3c-4-1-7-1-10 0 0-1 1-2 1-3 1 0 2-1 3-1 1-1 1-1 1-2l1-1h0l1-1h2z" class="P"></path><path d="M618 672c3 0 5 1 8 2-1 1-2 1-3 2-2 0-5 1-7 0l-1-2h-1l1-1h0l1-1h2z" class="R"></path><path d="M613 636h1l1-2c1 0 1 0 1 1h1v1l-1 1c0 1-1 1-1 2v2c-2 7-6 14-8 22l-13 34-4 9c0 1-1 2-1 3l-6 16-56 147c-1 3-2 6-2 9 1 1 3 2 4 2l-5 7c-1 1-2 1-4 1h-1v-1s1-1 2-1c1-2 1-6 1-8l-1 1-1-1c2-2 4-7 4-10l89-235z" class="M"></path><path d="M520 891l1-1c1 0 1-1 2-1-1-6 1-12 4-17-1 3-2 6-2 9 1 1 3 2 4 2l-5 7c-1 1-2 1-4 1z" class="R"></path><path d="M677 541l2 1c1 1 2 1 3 2 2 1 4 3 5 5h1c1 2 1 6 1 8 0 5-1 8-3 12-1 0-2 0-3-1v1c1 1 2 1 2 2l-1 11 1-1h0l2 2v-1c-2-5 0-9 2-13 2-1 3-1 4 0h1c2-1 1-2 2-4 1 1 1 2 1 3l2-1c2 6 5 13 9 18 4 6 10 9 15 13v1c1 0 2 0 3 1l-1 1v1c-1 0-1 0-2-1h-1 0v1l2 1c0 1 0 1-1 2v1h-3l-2-1c-12 1-23 1-34 2-5 0-9 2-13 2l-1 1c-1-1-2-2-4-2l-3-3c-1-1-2-1-3-1l-2 2v1c-2 0-3-1-4 0-3 1-4 3-6 5l2 5c0 1 0 3 2 4h2c0 1-1 1-1 2-1 1-1 3-2 4 2 1 4 2 5 3l-1 1c-1-1-1 0-2-1h0-2v2 3l-2 2-2-1h0-1c-1 1-1 1-1 2l-1 1c-1 0-2-1-3 0-1 0-2-1-3-1-2 1-2 1-3 3v2c-3 2-5 3-8 4-2 1-4 3-6 4l2-4-1-1v-5l3-5c-1-3-1-3-3-5-4 2-5 6-7 10v-2c0-1 1-1 1-2l1-1v-1h-1c0-1 0-1-1-1l-1 2h-1l13-31c0-1 0-2 1-2l1-2c2-5 4-9 6-14l4-12c1-4 3-9 5-12l1-3c1 0 2-1 3-1 1-1 2-3 2-6-1 0-2 0-2-1v-3l2-1 1-1 1 1 1-2h2l1-1h1c2-1 4-2 6-2h6c2 0 6 1 8 1l-1-1 2-2z" class="V"></path><path d="M659 575c2 0 3-1 4-1h3c-2 2-4 4-6 5-1 1-1 1-2 1v-1c0-2 0-2 1-4z" class="C"></path><path d="M655 597c1 0 1 0 2-1 0 0-1-5 0-6h0c1 1 3 2 3 3l-1 2v3l-2 1-2-2z" class="K"></path><path d="M672 566c1 1 1 2 2 2h3v2c-1 1-2 1-3 1-3 0-2-2-5 0l-3 3h-3c-1 0-2 1-4 1 2-2 6-2 7-4s4-4 6-5z" class="D"></path><path d="M671 556l1 1c1 1 5 2 5 2 1 3 0 6 0 9h-3c-1 0-1-1-2-2h0c1-2 3-4 3-6h-2c-1-1-1-2-2-2l-1-1h0l1-1z" class="S"></path><path d="M656 568c1 0 2 1 4 2 1 0 3 0 4 1h2c-1 2-5 2-7 4-1 2-1 2-1 4-1-2-3-3-5-4-1 0-1 0-1-1 1-2 3-4 4-6h0z" class="V"></path><path d="M653 575c2 1 4 2 5 4v1c0 2-1 2-1 4 1 1 1 3 1 5l-1 1h0c-1 1 0 6 0 6-1 1-1 1-2 1-2-4-4-6-5-11 0-3 0-4 1-7 1 0 1-1 1-2s1-1 1-2z" class="n"></path><path d="M651 579c0 4 1 8 3 11v1l2-1-1-1 1-1h1v-4c1 1 1 3 1 5l-1 1h0c-1 1 0 6 0 6-1 1-1 1-2 1-2-4-4-6-5-11 0-3 0-4 1-7z" class="H"></path><path d="M671 558c1 0 1 1 2 2h2c0 2-2 4-3 6h0c-2 1-5 3-6 5h-2c-1-1-3-1-4-1 1-1 1-3 1-4v-4c-1-2 0-2 1-4 2 2 3 0 5 1 2 0 2 0 4-1z" class="r"></path><path d="M661 566c2 1 2 3 3 5-1-1-3-1-4-1 1-1 1-3 1-4z" class="S"></path><path d="M656 568c0-5-1-12 2-16 0-1 0-2 1-3 2-3 4-4 7-4 3-1 5-1 7 1 1 0 1 1 2 1s1 0 2 1c-2 0-3 1-4 2-1 2-2 4-2 6l-1 1h0l1 1c-2 1-2 1-4 1-2-1-3 1-5-1-1 2-2 2-1 4v4c0 1 0 3-1 4-2-1-3-2-4-2z" class="H"></path><path d="M664 558c-1-1-1 0-2-1-1-2-1-2 0-4 0-2 2-2 3-2l-1 7z" class="S"></path><path d="M665 551l2-1c1 0 1 0 2 1v4l1 2h0c-2 1-3 1-5 1h0-1l1-7z" class="I"></path><path d="M693 569h1c2-1 1-2 2-4 1 1 1 2 1 3l2-1c2 6 5 13 9 18 4 6 10 9 15 13v1c1 0 2 0 3 1l-1 1v1c-1 0-1 0-2-1h-1 0v1l2 1c0 1 0 1-1 2v1h-3l-2-1 2-1v-1c-7-6-14-11-23-14-11-4-17-1-27 4l8-6c2-2 4-3 6-5h0l1-1h0l2 2v-1c-2-5 0-9 2-13 2-1 3-1 4 0z" class="J"></path><path d="M693 569h1c2-1 1-2 2-4 1 1 1 2 1 3 2 5 5 13 3 17l-2 2c-4-1-8-2-11-4v-1c-2-5 0-9 2-13 2-1 3-1 4 0z" class="k"></path><path d="M662 543h6c2 0 6 1 8 1 2 1 3 1 3 3v1h-2c-1-1-1-1-2-1s-1-1-2-1c-2-2-4-2-7-1-3 0-5 1-7 4-1 1-1 2-1 3-3 4-2 11-2 16h0c-1 2-3 4-4 6 0 1 0 1 1 1 0 1-1 1-1 2s0 2-1 2c-1 3-1 4-1 7 1 5 3 7 5 11l2 2 1 2v1c-2 0-4-3-5-4h-1c0 1 0 1-1 1h-2l-3-3c0-2-1-3-2-4h-3c1-2 2-3 3-5v-1l-1 2h-1l-1-1v-1l1-1v-1-1h-2c-1 2-2 5-4 7-1-3 3-11 3-15h-1c1-4 3-9 5-12l1-3c1 0 2-1 3-1 1-1 2-3 2-6-1 0-2 0-2-1v-3l2-1 1-1 1 1 1-2h2l1-1h1c2-1 4-2 6-2z" class="B"></path><path d="M642 588v-3c1-2 3-6 5-7l1 1c0 1-1 2-2 3 0 1 0 2-1 2l-1 1v1l-1 2h-1z" class="E"></path><path d="M644 587c2 4 5 8 8 11 0 1 0 1-1 1h-2l-3-3c0-2-1-3-2-4h-3c1-2 2-3 3-5z" class="Z"></path><path d="M655 545h1c2-1 4-2 6-2l-3 3c-3 2-5 5-6 9 1 1 1 1 1 2-1 2-1 5-1 7v2c-1 2-5 4-7 7-1 3-2 7-4 10l-1-1c1-3 3-5 3-8 0-2 3-5 4-7 2-2 3-5 3-7 1-2 1-3 2-5l-2-2c1-1 2-2 2-5l1-2 1-1z" class="C"></path><path d="M650 547l1 1 1-2h2l-1 2c0 3-1 4-2 5l2 2c-1 2-1 3-2 5 0 2-1 5-3 7-1 2-4 5-4 7-2 3-3 6-4 9-1 2-2 5-4 7-1-3 3-11 3-15h-1c1-4 3-9 5-12l1-3c1 0 2-1 3-1 1-1 2-3 2-6-1 0-2 0-2-1v-3l2-1 1-1z" class="F"></path><path d="M649 553c2-1 3-3 4-5 0 3-1 4-2 5-1 2-2 6-4 7h0v-1c1-1 2-3 2-6h0z" class="a"></path><path d="M650 547l1 1 1-2h2l-1 2c-1 2-2 4-4 5h0c-1 0-2 0-2-1v-3l2-1 1-1z" class="X"></path><path d="M647 549l2-1v5h0c-1 0-2 0-2-1v-3z" class="J"></path><path d="M644 560c1 0 2-1 3-1v1l-3 6c-2 3-4 6-5 9h-1c1-4 3-9 5-12l1-3z" class="D"></path><path d="M639 575c0 4-4 12-3 15 2-2 3-5 4-7h2v1 1l-1 1v1l1 1h1l1-2v1c-1 2-2 3-3 5h3c1 1 2 2 2 4l3 3h2c1 0 1 0 1-1h1c1 1 3 4 5 4v-1l-1-2 2-1h1c1 1 2 1 3 0l-1 2c3 2 6 6 9 7l2-2c-1 1-1 1-1 2-1 1-1 1-1 2l-1 1c-1-1-2-2-4-2l-3-3c-1-1-2-1-3-1l-2 2v1c-2 0-3-1-4 0-3 1-4 3-6 5l2 5c0 1 0 3 2 4h2c0 1-1 1-1 2-1 1-1 3-2 4 2 1 4 2 5 3l-1 1c-1-1-1 0-2-1h0-2v2 3l-2 2-2-1h0-1c-1 1-1 1-1 2l-1 1c-1 0-2-1-3 0-1 0-2-1-3-1-2 1-2 1-3 3v2c-3 2-5 3-8 4-2 1-4 3-6 4l2-4-1-1v-5l3-5c-1-3-1-3-3-5-4 2-5 6-7 10v-2c0-1 1-1 1-2l1-1v-1h-1c0-1 0-1-1-1l-1 2h-1l13-31c0-1 0-2 1-2l1-2c2-5 4-9 6-14l4-12h1z" class="F"></path><path d="M622 631c2-1 4-1 6-1l2 1c-1 2-3 3-3 5-2 2-2 4-2 7l-2 4-1-1v-5l3-5c-1-3-1-3-3-5z" class="S"></path><path d="M639 575c0 4-4 12-3 15 0 3-3 8-4 11l-1-1c0-1 1-1 0-2 1-1 1-1 1-2h0c1-1 0-2 1-2v-1-1c-2 3-3 9-6 11l1-2c2-5 4-9 6-14l4-12h1z" class="B"></path><path d="M632 633h3c2 1 3 3 3 5-2 1-2 1-3 3v2c-3 2-5 3-8 4-2 1-4 3-6 4l2-4 2-4 4-7c1-2 1-3 3-3z" class="V"></path><path d="M632 633h3c2 1 3 3 3 5-2 1-2 1-3 3v2c0-2-1-3-2-4v-1c0-2 0-3-1-5z" class="C"></path><path d="M640 583h2v1 1l-1 1v1l1 1h1l1-2v1c-1 2-2 3-3 5l-1 3-1 3c-2 3-2 6-3 9l-1 2c-1 2-2 3-2 5v2c-1 2-1 4-1 5l1 1c0 2 0 3 1 5-2-2-5-5-5-7 0-1 1-3 1-4 1-2 1-5 1-7-1-2 1-6 1-8 1-3 4-8 4-11 2-2 3-5 4-7z" class="J"></path><g class="M"><path d="M636 607v-5l-1-1c2-2 3-4 5-6l-1 3c-2 3-2 6-3 9z"></path><path d="M641 592h3c1 1 2 2 2 4l3 3h2c1 0 1 0 1-1h1c1 1 3 4 5 4v-1l-1-2 2-1h1c1 1 2 1 3 0l-1 2c3 2 6 6 9 7l2-2c-1 1-1 1-1 2-1 1-1 1-1 2l-1 1c-1-1-2-2-4-2l-3-3c-1-1-2-1-3-1l-2 2v1c-2 0-3-1-4 0-3 1-4 3-6 5l2 5c0 1 0 3 2 4h2c0 1-1 1-1 2-1 1-1 3-2 4 2 1 4 2 5 3l-1 1c-1-1-1 0-2-1h0-2v2 3l-2 2-2-1h0l-1-1-2-1c-3 0-4 0-5-2h-2c0-3-2-4-3-5-1-2-1-3-1-5l-1-1c0-1 0-3 1-5v-2c0-2 1-3 2-5l1-2c1-3 1-6 3-9l1-3 1-3z"></path></g><path d="M642 622l-1-2v-1c1 0 1 0 2 1l1-2c0 1 0 1 1 2 0 2 1 4 2 5h0v-3-1l-1-1c0-1-1-2-1-3v-1c1 0 2 1 3 2v2l3 3v1l-1 1-2 1c-2 0-5-2-6-4z" class="I"></path><path d="M642 622c1 2 4 4 6 4l2-1v2h1c2 1 4 2 5 3l-1 1c-1-1-1 0-2-1h0-2l-2-1-1 2h-1c-1-1-1-3-2-4 0-1-3-1-4-1 0-2 0-2-2-4h3z" class="P"></path><path d="M650 600l1 1c-2 4-5 7-8 11-1 1-2 3-4 4h-1l-1-1c1-1 1-2 2-2l2-4 9-9z" class="J"></path><path d="M635 609c1 1 2 1 3 1 1-1 2-1 3-1l-2 4c-1 0-1 1-2 2 0 2 0 4 1 5v1l-2 1c0 1-1 1-1 2l-2-2-1-1c0-1 0-3 1-5v-2c0-2 1-3 2-5z" class="P"></path><path d="M638 621l1 1c2 2 2 2 2 4 1 0 4 0 4 1 1 1 1 3 2 4h1l1-2 2 1v2 3l-2 2-2-1h0l-1-1-2-1c-3 0-4 0-5-2h-2c0-3-2-4-3-5-1-2-1-3-1-5l2 2c0-1 1-1 1-2l2-1z" class="l"></path><path d="M633 622l2 2c1 2 2 4 3 7l1 1h-2c0-3-2-4-3-5-1-2-1-3-1-5z" class="T"></path><path d="M651 632c-1 1-1 1-2 1h0l-1 1c-2 0-4 0-5-1s-2-1-3-3c-1-1-2-3-2-4h3c1 0 4 0 4 1 1 1 1 3 2 4h1l1-2 2 1v2z" class="H"></path><path d="M641 592h3c1 1 2 2 2 4l3 3h2l-1 1-9 9c-1 0-2 0-3 1-1 0-2 0-3-1l1-2c1-3 1-6 3-9l1-3 1-3z" class="S"></path><path d="M649 599h2l-1 1-9 9c-1 0-2 0-3 1-1 0-2 0-3-1l1-2c1-3 1-6 3-9v2c0 1-1 2 0 3 0 1 0 3-1 4v1 1h1c1-1 1-2 2-3v-2h2 0 1l3-3v-1l1 1c1-1 1-1 1-2z" class="G"></path><defs><linearGradient id="K" x1="356" y1="568.925" x2="398.839" y2="539.341" xlink:href="#B"><stop offset="0" stop-color="#d20106"></stop><stop offset="1" stop-color="#f53b3a"></stop></linearGradient></defs><path fill="url(#K)" d="M332 456v-1c0-1 0-1 1-2l1 1c0-2 0-2-2-4 0-2 0-4 1-6 0 1 1 1 2 2l4 9 13 30c3 3 4 6 5 9h1l-1-2c0-1-1-2-1-3-1-1-1-2-1-4v-1c4 8 6 16 10 23 3 1 1 5 6 5v2h2c0 1 0 1 1 2s3 1 4 1h1l3 2c-3 0-3 1-5 3 1 0 1 1 1 1 1 1 1 3 1 4l1 1v-1c1-1 2-1 3-1l5 2c-2 0-3 0-5 2 0 1 0 2 1 4 0 2 1 5 3 7 0 1 2 2 2 3h1 0c1 0 1 1 2 1 1 2 2 2 4 3 9 9 19 20 20 34 0 5-1 9-1 14 0 3 1 6 2 8h0-1l-1-2c-1-2-1-3-3-4l3 9c2 3 4 7 6 10l-4-1v1l1 4v1l-2-2h0v1c-1 0-2-2-3-3l6 15c0 1 0 2 1 3h0c-1 1-1 1-1 2l1 1c1 1 0 0 1 2l2 4c1 3-1-2 0 1l1 2h0c-1 1-1 2-1 2v1l1-2 1 1c2 2 3 8 4 10l-1 1-1-1h0l-1-1c-2 2-1 4-2 6 0 1-1 2-1 4-1 1-1 2-2 2l-1 1h0c-2 3-3 4-5 6l-2 1c-7 3-11 3-18 3h0c-3-2-5-3-7-6h-2c0-1-1-2-1-2l-2 1h-3l-3 1h-6c3-5 5-9 6-15l-1-1h0c-2 0-3 1-5 0-1-1-1-2-1-3l2-2c2 0 4-1 6-2 4-3 5-9 6-13l1-5 4-15h-3c-2 1 0 1-2 1h0c-1 1-1 1-3 2 1-2 1-2 0-3v-1c-1-1-2-1-3 0-1 0-2 2-3 3-2 2-4 3-6 4 2-3 5-5 6-9l1-7h3v-3c4-5 5-11 7-16l2-5-6-12c-2 0-2 0-4 1v2c-1-6-2-11-7-14v-1h0s0-1 1-1v-1-1-3l-1-2c0-2-1-3-3-3-1-1-3 0-4 0-1 1-2 1-3 1-2 0-3 1-4 1h-2c-1-1-2-2-3-2l-2-2-2 1-1-1c-1 0-2 1-3 1l2-1c0-2 0-2 1-3 0-1 0-1-1-2h-2c-1-1-1-2-1-2l-1-1s2-2 3-2c2-1 4-3 7-2h2v1l1-1h0l1-1v-1c-1-1 0-1 0-2 0-3-1-5 1-7h1v-3h-2v-4c-2-1-2-1-4-1 0-1 0-2-1-3-3-3-5-3-9-3l-1-2s1 0 2-1c2 0 5-1 8-2 0-1-1-3-1-4-1-2-2-2-3-3v-1c1-2 1-2 2-2l1-2c-2-4-3-5-6-8l-3-2v-2c0-1 1-1 1-3-2-3-3-6-4-10-2-4-4-9-6-14z"></path><path d="M387 577c2 3 2 4 3 7h-1c-2-2-2-5-2-7z" class="B"></path><path d="M342 480c1 2 2 4 2 6v1l-3-2v-2c0-1 1-1 1-3z" class="J"></path><path d="M352 550l-1-2c2 0 4-1 6-2l1 2v-1c2 0 3 1 4 2v1c-1 1-4 2-6 2-1 0-2-1-4-2z" class="V"></path><path d="M425 650c2 2 3 8 4 10l-1 1-1-1h0l-1-1c-2 2-1 4-2 6 0 1-1 2-1 4-1 1-1 2-2 2l-1 1h0c2-6 1-11-1-18h1c1 1 1 2 1 4 1 2 1 4 1 6v1c1-2 1-3 2-4 0-2 1-3 3-3h1l-1-3-2-4v-1z" class="S"></path><path d="M400 590h1 2l2 2c1 2 3 6 3 8h-1c0 2 1 3 1 5l3 7c1 1 1 1 0 3l-11-25z" class="G"></path><path d="M411 615c1-2 1-2 0-3l-3-7c0-2-1-3-1-5h1c2 6 5 12 9 17h0l1 4v1l-2-2h0v1c-1 0-2-2-3-3l-2-3z" class="P"></path><path d="M368 513c2 4 3 8 4 11l6 14c1 3 3 6 3 9h0 0l-3-7-1-4c-1-1-1-1-2-1v1l3 7c1 1 1 2 1 3h0l-14-31c3 3 5 9 7 13 0-1 0-2-1-4l-1-1v-1c0-2-1-4-2-6v-3z" class="C"></path><path d="M355 484c4 8 6 16 10 23l3 6v3c1 2 2 4 2 6v1l1 1c1 2 1 3 1 4-2-4-4-10-7-13l-13-30c3 3 4 6 5 9h1l-1-2c0-1-1-2-1-3-1-1-1-2-1-4v-1z" class="O"></path><path d="M392 571c1-1 1-3 1-4 0-3-2-3-1-6 1 2 2 4 4 6v1l4 5-2-1c0 2 0 2 1 4 3 5 5 10 6 16l-2-2h-2-1l-8-19z" class="H"></path><path d="M372 556c2 1 3 1 4 2l3 6c2 2 3 2 3 4 1 3 4 6 5 9 0 2 0 5 2 7h1c-1 5-1 9-1 14-1-4 1-9-1-12l-6-12c-2 0-2 0-4 1v2c-1-6-2-11-7-14v-1h0s0-1 1-1v-1-1-3z" class="J"></path><path d="M372 556c2 1 3 1 4 2 0 3 1 5 2 8 1 2 3 5 4 8-2 0-2 0-4 1v2c-1-6-2-11-7-14v-1h0s0-1 1-1v-1-1-3z" class="M"></path><path d="M372 556c2 1 3 1 4 2 0 3 1 5 2 8l-2-2c-1 0 0 0-1 1h0l-3-3-1 1v-1h0s0-1 1-1v-1-1-3z" class="T"></path><defs><linearGradient id="L" x1="412.35" y1="593.834" x2="405.522" y2="595.701" xlink:href="#B"><stop offset="0" stop-color="#210101"></stop><stop offset="1" stop-color="#3c0202"></stop></linearGradient></defs><path fill="url(#L)" d="M405 592c-1-6-3-11-6-16-1-2-1-2-1-4l2 1v1c3 3 5 5 7 9 1 2 2 5 2 5 0 4 1 7 3 10l3 9c2 3 4 7 6 10l-4-1v1h0c-4-5-7-11-9-17 0-2-2-6-3-8z"></path><path d="M365 507c3 1 1 5 6 5v2h2c0 1 0 1 1 2s3 1 4 1h1l3 2c-3 0-3 1-5 3 1 0 1 1 1 1 1 1 1 3 1 4l1 1v-1c1-1 2-1 3-1l5 2c-2 0-3 0-5 2h-1 0v2c0 1 0 1 1 2l-2-1-1 1c0 1 1 2 1 4h1v2h0l-2-2 3 6 1 2h0c-1 0-1-1-2-2v-1h-1c1 1 1 2 2 3v4l-2-3c0-3-2-6-3-9l-6-14c-1-3-2-7-4-11l-3-6z" class="Z"></path><path d="M351 507h1c1 0 1 1 1 2l5 12v1c1 2 2 4 3 5 0 3-1 6 0 9l-1 4v3c0 1 0 2-1 3l-1 1v1l-1-2c-2 1-4 2-6 2l1 2v1l-2-2-2 1-1-1c-1 0-2 1-3 1l2-1c0-2 0-2 1-3 0-1 0-1-1-2h-2c-1-1-1-2-1-2l-1-1s2-2 3-2c2-1 4-3 7-2h2v1l1-1h0l1-1v-1c-1-1 0-1 0-2 0-3-1-5 1-7h1v-3h-2v-4c-2-1-2-1-4-1 0-1 0-2-1-3-3-3-5-3-9-3l-1-2s1 0 2-1c2 0 5-1 8-2z" class="T"></path><path d="M352 543h4l1-1c1 1 1 1 1 3l-1 1h0c-2 1-4 2-6 2l1 2v1l-2-2-2 1-1-1c-1 0-2 1-3 1l2-1c1-3 3-4 6-6z" class="D"></path><path d="M354 537v1l1-1h0l1-1v-1c-1-1 0-1 0-2 0-3-1-5 1-7h1c1 3 2 9 1 12l-1 1c-1-2 0-2-1-3-1 1-1 2-2 3h0c-1 0-1 1-2 1l1 1-2 2c-3 2-5 3-6 6 0-2 0-2 1-3 0-1 0-1-1-2h-2c-1-1-1-2-1-2l-1-1s2-2 3-2c2-1 4-3 7-2h2z" class="S"></path><path d="M345 539c2-1 4-3 7-2l-9 7 10-4 1 1-2 2c-3 2-5 3-6 6 0-2 0-2 1-3 0-1 0-1-1-2h-2c-1-1-1-2-1-2l-1-1s2-2 3-2z" class="e"></path><path d="M388 586c2 3 0 8 1 12 1 2 1 5 1 8 1 1 0 5 0 7 1 1 2 1 2 3 2 0 4 1 6 1v1c2 0 4 2 5 3 1 4 2 7 5 10 1 1 4 5 4 6l-1 1h1c0 1 0 1 1 2l2-1h2v1h-2l-1 1v1c1 1 1 1 1 2l1 1c1 1 1 2 1 2 1 2 2 5 3 7h-1 0c-2-5-6-14-10-16-2 0-2 0-3 2-2 1-3 3-3 5v2-1c-2-3 2-8 3-10-1-5-3-11-7-15-2-2-3-4-6-4-2 0-3 2-4 3h-3c-2 1 0 1-2 1h0c-1 1-1 1-3 2 1-2 1-2 0-3v-1c-1-1-2-1-3 0-1 0-2 2-3 3-2 2-4 3-6 4 2-3 5-5 6-9l1-7h3v-3c4-5 5-11 7-16l2-5z" class="M"></path><path d="M386 597c1 0 0 0 1 1-2 4-2 10-4 14-1 0 0 0-1-1 1-5 2-10 4-14z" class="Z"></path><path d="M381 538c0-2-1-3-1-4l1-1 2 1c-1-1-1-1-1-2v-2h0 1c0 1 0 2 1 4 0 2 1 5 3 7 0 1 2 2 2 3h1 0c1 0 1 1 2 1 1 2 2 2 4 3 9 9 19 20 20 34 0 5-1 9-1 14 0 3 1 6 2 8h0-1l-1-2c-1-2-1-3-3-4-2-3-3-6-3-10 0 0-1-3-2-5-2-4-4-6-7-9v-1l-4-5v-1c-2-2-3-4-4-6-1 3 1 3 1 6 0 1 0 3-1 4l-9-21v-4c-1-1-1-2-2-3h1v1c1 1 1 2 2 2h0l-1-2-3-6 2 2h0v-2h-1z" class="T"></path><path d="M381 538c0-2-1-3-1-4l1-1 2 1c-1-1-1-1-1-2v-2h0 1c0 1 0 2 1 4 0 2 1 5 3 7 0 1 2 2 2 3h-1c1 1 1 2 2 2 0 1-1 1 0 2 2 3 5 5 6 8-2-2-4-4-7-6v-1l-1-2v-1c0-2-1-2-2-3-1 0-1-1-2-2l-3-3zm15 30c0-2 0-2 1-3l2 1 10 14h1c2 2 1 4 2 6-1 2-1 3-1 6l-1-1h0l-1-3h0s-1-3-2-5c-2-4-4-6-7-9v-1l-4-5z" class="K"></path><path d="M389 550c3 2 5 4 7 6 5 4 10 9 13 15 1 3 1 6 1 9h-1l-10-14c-3-5-8-9-11-15l1-1z" class="L"></path><path d="M381 538l3 3c1 1 1 2 2 2 1 1 2 1 2 3v1l1 2v1l-1 1c3 6 8 10 11 15l-2-1c-1 1-1 1-1 3v-1c-2-2-3-4-4-6-1 3 1 3 1 6 0 1 0 3-1 4l-9-21v-4c-1-1-1-2-2-3h1v1c1 1 1 2 2 2h0l-1-2-3-6 2 2h0v-2h-1z" class="G"></path><path d="M389 620c1-1 2-3 4-3 3 0 4 2 6 4 4 4 6 10 7 15-1 2-5 7-3 10v1-2c0-2 1-4 3-5 1-2 1-2 3-2 4 2 8 11 10 16h0c2 7 3 12 1 18-2 3-3 4-5 6l-2 1c-7 3-11 3-18 3h0c-3-2-5-3-7-6h-2c0-1-1-2-1-2l-2 1h-3l-3 1h-6c3-5 5-9 6-15l-1-1h0c-2 0-3 1-5 0-1-1-1-2-1-3l2-2c2 0 4-1 6-2 4-3 5-9 6-13l1-5 4-15z" class="k"></path><path d="M376 660l1-1h1v1 2c2 1 3 0 4-1 2-1 2-2 3-4 1 0 2-1 3 0 1 0 1 1 2 1s3-1 4-2l1-2h1c-1 2-5 5-4 7l3 3c-1 2-2 2-3 3 1 1 0 0 1 2 1 1 1 2 1 4h-1c-1-1 0-3-2-4l-3 3v4h-2c0-1-1-2-1-2l-2 1h-3l-3 1h-6c3-5 5-9 6-15l-1-1h0z" class="D"></path><path d="M437 194c4-6 10-12 16-17l12-9v3c1 1 1 2 1 3s0 3 1 4c0 2 1 5 3 7l-1 8c1 1 1 1 3 1v3h0c-1 5-1 9-1 13l-5 46-4 18c-3 10-5 21-9 32 0 2-2 5-3 8 0 1-1 1-1 2-2-1-4-4-5-5l-12-9c-2-2-6-4-9-6l-1 1h0l-2 3c-1 3-4 6-5 8l1 1 1 2-9 13 1 3c-1 1-1 2-2 3v-1l-1-1c-1-3-2-7-4-9-1 0-1-1-2-2-1-2-3-5-5-7l-4-6c0-1-2-3-2-4-1-1-1-1-1-2h1c1-1 1-1 2-1v-1l1-2c-1-3-3-5-3-9-1-2-2-6-2-10-2-1-1-7-1-9l2-7 3-6c6-6 12-10 20-11v-3-1c0-4 3-8 5-11h0c2-4 6-7 10-8h1l-1-1h-11-12l-1-1 4-3c1 1 2 1 4 1 5-1 11-2 15-6s7-10 11-14l1-1z" class="V"></path><defs><linearGradient id="M" x1="445.523" y1="206.826" x2="460.997" y2="221.191" xlink:href="#B"><stop offset="0" stop-color="#9c9c9c"></stop><stop offset="1" stop-color="#cccaca"></stop></linearGradient></defs><path fill="url(#M)" d="M467 178c0 2 1 5 3 7l-1 8-2 4-2 9v1h-1v-1l1-1c0-1 0-2-1-3l-1 1c0 1 0 2-1 3l-1 1-16 34c-1-1-2-1-3-1 0-2 2-4 3-5 2-4 4-8 5-12 3-7 6-13 8-20l3-12c1-1 1-2 2-3h1l2-4c0-2 0-4 1-6z"></path><defs><linearGradient id="N" x1="470.873" y1="184.525" x2="460.908" y2="199.81" xlink:href="#B"><stop offset="0" stop-color="#b7b5b6"></stop><stop offset="1" stop-color="#e4e4e3"></stop></linearGradient></defs><path fill="url(#N)" d="M467 178c0 2 1 5 3 7l-1 8-2 4-2 9v1h-1v-1l1-1c0-1 0-2-1-3l-1 1c0 1 0 2-1 3l-1 1c1-5 3-9 4-14 0-3 1-6 1-9 0-2 0-4 1-6z"></path><defs><linearGradient id="O" x1="454.705" y1="243.881" x2="468.983" y2="248.028" xlink:href="#B"><stop offset="0" stop-color="#c5c4c4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#O)" d="M467 197l2-4c1 1 1 1 3 1v3h0c-1 5-1 9-1 13l-5 46-4 18-1 1v1l-1 1v1 1c0 1-1 2-1 3v2c-1 0-1 1-1 2v2c-1 1-1 2-2 4h-1c-2 1-2 4-3 6l-1 1v-1c1-1 0-1 1-1v-1-1-1h1v-1-1-1l1-1v-1h0 1c-2-1-3 0-4-2l14-80v-1l2-9z"></path><path d="M467 197l2-4c1 1 1 1 3 1v3h0c-1 5-1 9-1 13-1-2 0-5 0-7h-1v-2c-2 3-2 9-2 12h0v-4-2l-2-2v2l-1-1 2-9z" class="c"></path><path d="M467 197l2-4c1 1 1 1 3 1v3h0-2l-1 4h-1l-1-4z" class="f"></path><defs><linearGradient id="P" x1="412.308" y1="294.131" x2="395.887" y2="302.436" xlink:href="#B"><stop offset="0" stop-color="#939091"></stop><stop offset="1" stop-color="#bdbcbc"></stop></linearGradient></defs><path fill="url(#P)" d="M388 275c4 0 7 1 10 2h1c3 2 6 7 9 9 5 4 10 7 15 10l-1 1h0l-2 3c-1 3-4 6-5 8l1 1 1 2-9 13 1 3c-1 1-1 2-2 3v-1l-1-1c-1-3-2-7-4-9-1 0-1-1-2-2-1-2-3-5-5-7l-4-6c0-1-2-3-2-4-1-1-1-1-1-2h1c1-1 1-1 2-1v-1l1-2c-1-3-3-5-3-9-1-2-2-6-2-10h1z"></path><path d="M398 298h2c2 3 3 8 3 12l-1-1c-1-2-3-4-4-6 0-2-1-3 0-5z" class="N"></path><path d="M411 304v-3c1-1 1-2 1-3h-1c1-1 1-1 2-1v4c1 1 1 0 1 2v2l-3 5-2 3-4 8-4-5v-2l1 1 2 2c0-1 1-2 1-3s0-2 1-3v-1c0-1 0-2 1-3s2-2 2-3v-1h1l1 1z" class="U"></path><path d="M411 304v-3c1-1 1-2 1-3h-1c1-1 1-1 2-1v4c1 1 1 0 1 2v2l-3 5-2 3c0-1-1-2-1-2 1-1 2-3 2-4s0-2 1-3z" class="Y"></path><path d="M392 294c2 6 5 14 9 20v2l1 3c-1 0-1-1-2-2-1-2-3-5-5-7l-4-6c0-1-2-3-2-4-1-1-1-1-1-2h1c1-1 1-1 2-1v-1l1-2z" class="O"></path><path d="M414 305c1-2 1-5 3-7l1-1-1-1h0v-1c2 0 2 0 4 1l1 1-2 3c-1 3-4 6-5 8l1 1 1 2-9 13 1 3c-1 1-1 2-2 3v-1l-1-1c-1-3-2-7-4-9l-1-3 4 5 4-8 2-3 3-5z" class="q"></path><path d="M414 305c1-2 1-5 3-7l1-1-1-1h0v-1c2 0 2 0 4 1l-1 1c-1 2-1 4-3 5 0 1 0 2-1 2-1 2-3 5-5 6l3-5z" class="z"></path><defs><linearGradient id="Q" x1="405.096" y1="322.774" x2="416.811" y2="314.593" xlink:href="#B"><stop offset="0" stop-color="#8b898a"></stop><stop offset="1" stop-color="#a7a5a6"></stop></linearGradient></defs><path fill="url(#Q)" d="M415 308l1 1 1 2-9 13 1 3c-1 1-1 2-2 3v-1l-1-1c1-8 6-13 9-20z"></path><path d="M388 275c4 0 7 1 10 2h1c3 2 6 7 9 9 5 4 10 7 15 10l-1 1h0l-1-1c-2-1-2-1-4-1v1h0l1 1-1 1c-2 2-2 5-3 7v-2c0-2 0-1-1-2v-4c1-1 1-1 0-2-3-2-8-6-12-7-4-2-7-3-12-3-1-2-2-6-2-10h1z" class="AB"></path><path d="M437 194c4-6 10-12 16-17l12-9v3c1 1 1 2 1 3s0 3 1 4c-1 2-1 4-1 6l-2 4h-1c-1 1-1 2-2 3l-3 12c-2 7-5 13-8 20-1 4-3 8-5 12-1 1-3 3-3 5 1 0 2 0 3 1-4 6-9 12-15 16-5 4-11 7-17 10-4 2-9 4-13 5-3 0-6 1-9 0l-2 2-1 1h-1c-2-1-1-7-1-9l2-7 3-6c6-6 12-10 20-11v-3-1c0-4 3-8 5-11h0c2-4 6-7 10-8h1l-1-1h-11-12l-1-1 4-3c1 1 2 1 4 1 5-1 11-2 15-6s7-10 11-14l1-1z" class="W"></path><path d="M426 218h3c0 1 0 1-1 2l-11 19 1 1c-3 2-4 2-7 2v-3-1c0-4 3-8 5-11h0c2-4 6-7 10-8h1l-1-1z" class="Q"></path><path d="M426 218h3c0 1 0 1-1 2-2 0-4 1-5 2l-1 2c-1 2-2 3-3 4h-2c-2 3-4 7-6 10 0-4 3-8 5-11h0c2-4 6-7 10-8h1l-1-1z" class="O"></path><defs><linearGradient id="R" x1="464.99" y1="192.376" x2="430.197" y2="191.182" xlink:href="#B"><stop offset="0" stop-color="#929092"></stop><stop offset="1" stop-color="#c9c7c6"></stop></linearGradient></defs><path fill="url(#R)" d="M437 194c4-6 10-12 16-17l12-9v3c1 1 1 2 1 3s0 3 1 4c-1 2-1 4-1 6l-2 4h-1v-6c-1-1-1-1-3-1h-1c-9 6-14 17-19 27l-1-1c-1 2-3 3-4 4 0 1 0 1-1 1v1h-1l-3 4h0c0-2-1-4 0-6v-1c0-2 2-3 3-4l12-14h-1c-1 0-4 0-5 1s-1 1-2 1h0z"></path><path d="M418 240c-1 2-3 4-4 6-3 4-8 8-9 12-1 2-3 4-5 5-2 2-3 4-5 6-1 1-2 2-3 2l-1 1-2 2-1 1h-1c-2-1-1-7-1-9l2-7 3-6c6-6 12-10 20-11 3 0 4 0 7-2z" class="I"></path><path d="M459 181h1c2 0 2 0 3 1v6c-1 1-1 2-2 3l-3 12c-2 7-5 13-8 20 0 1-1 1-1 2v1l-3 5-1 1c-1 1-3 1-4 3 0 2 0 2-1 3h-1l-2 2 1 1c-2 2-4 3-6 5h-1l1-3c-5 5-11 9-17 13-3 1-6 3-8 4l3-4v-1c4-5 8-8 12-13 8-10 13-23 18-34 5-10 10-21 19-27z" class="V"></path><defs><linearGradient id="S" x1="443.281" y1="212.111" x2="453.038" y2="217.6" xlink:href="#B"><stop offset="0" stop-color="#b2b0b1"></stop><stop offset="1" stop-color="#dad9d9"></stop></linearGradient></defs><path fill="url(#S)" d="M459 181h1c2 0 2 0 3 1v6c-1 1-1 2-2 3l-3 12c-2 7-5 13-8 20 0 1-1 1-1 2v1l-3 5-1 1c-1 1-3 1-4 3 0 2 0 2-1 3h-1l-2 2 1 1c-2 2-4 3-6 5h-1l1-3 12-15c1-4 3-7 4-11h0l3-9c1-2 2-3 2-5 0-4 2-7 3-11 2-3 3-7 3-10v-1z"></path><path d="M448 217c0 3 0 4-1 6-2 1-2 3-3 5 1-4 3-7 4-11z" class="W"></path><path d="M459 181h1c2 0 2 0 3 1v6c-1 1-1 2-2 3v-5h-1l1-1c1-2 1-2 0-3h0l-1 1-1-1v-1z" class="U"></path><path d="M448 217l3-9c1-2 2-3 2-5 0-4 2-7 3-11 1 4 0 7-2 11 0 1 0 1-1 2-1 4-2 9-5 12z" class="W"></path><path d="M679 224h46c2 0 9 0 11 1l1 1h0 51c7 1 13 1 19 1h32c7 0 15-1 22 0 1 1 0 2 0 3h1c-2 4-3 7-4 11-4 0-9-1-13 0l-9 1c-1 0-2-1-2-1l-3 1v-1c2 0 2 0 4-1-9 0-19 3-27 6l-6 2c-3 1-5 2-8 2l-13 3v2h0-1c-1 1-2 1-3 2-5 1-10 5-15 7v1l1 1c-2 1-3 1-4 3-2 2-5 4-5 7h0l-2 6-2 2h3l1 2-3 3c-1 1-1 0-1 1-3 1-6 4-9 6-4 3-9 7-13 11l-1-1v-3-5c-1-2-2-3-2-6h3c-1-1-1-2-2-3l2-2 3-2c-3-1-4 0-7 1h0v1h-5l-2 1h2v1c-1 1-2 1-3 1l-3-1c-3-1-6-1-9 0l-3 3c0 1-1 3-1 4-1 2 0 5 0 7 0-1-1-2-1-3l-2-7-1-1c0-1 0-2-1-2 0-1 0-3-1-4h1v-1c0-1 0-2-1-3l1-1c-1 0-1-1-2-1v-2l-2-4c-1-2-2-1-2-2-2-1-1-1-3-2 0 0-1-2-2-3-1 1-1 2-2 3l-1-2c-1-1-2-1-3-2 0 2 1 3 1 4l-3-1c-3-5-7-9-11-13-3-3-6-6-10-8-5-2-10-3-15-4l1-1h-1l2-1c-2-1-3-1-5-1-2-1-5 0-7-1l1-3c-2-4-5-7-7-11l8 1 1-1h1 4 12l29-2z" class="L"></path><path d="M738 249l3-1c2 1 2 1 3 2-2 0-3 0-5 1v-1l-1-1z" class="B"></path><path d="M734 258h0v6l2 1c-1 3-3 7-5 10 2-6 3-11 3-17z" class="AC"></path><path d="M734 258c2-1 2-1 4-1v1c0 2-1 4-2 7l-2-1v-6z" class="F"></path><path d="M733 252c-3 0-6 1-9 1 5-2 9-4 14-4l1 1v1h0c-3 1-4 1-6 1z" class="C"></path><path d="M741 248c5-1 10-1 14-6v-1l1 1c0 1 0 3-2 4-2 3-6 4-10 4h0c-1-1-1-1-3-2z" class="j"></path><path d="M739 251c2 1 2 2 3 4-1 1-2 2-4 3v-1c-2 0-2 0-4 1h0c0-2-1-4-1-6 2 0 3 0 6-1z" class="E"></path><path d="M631 237c1 1 3 1 5 1 4 0 8 0 12 1-2 1-5 0-6 2v1h0c-2-1-3-1-5-1-2-1-5 0-7-1l1-3zm30 5c4 2 9 4 13 7l-2 1v1h-2s-1-1-2-1c-3-2-4-4-8-6h0 1l-1-1c1 0 1 0 1-1z" class="R"></path><path d="M681 254c1 1 2 1 4 2 1 2 3 3 4 5l4 5-1 1-1-1c0 1-1 2-1 3-2-4-5-9-9-13v-2z" class="d"></path><path d="M861 230h1c-2 4-3 7-4 11-4 0-9-1-13 0l-9 1c-1 0-2-1-2-1l-3 1v-1c2 0 2 0 4-1 5-1 10-1 16-2 2 0 5 0 7-2l3-6z" class="G"></path><path d="M679 224h46c2 0 9 0 11 1l1 1h0-104 1 4 12l29-2z" class="b"></path><defs><linearGradient id="T" x1="717.788" y1="267.956" x2="712.351" y2="284.118" xlink:href="#B"><stop offset="0" stop-color="#b61e25"></stop><stop offset="1" stop-color="#d53a41"></stop></linearGradient></defs><path fill="url(#T)" d="M710 283c2-1 3-1 5-3v-3c-2-10-14-15-20-21-3-2-6-6-9-8h-1 1c3 1 5 3 8 5l15 11c3 2 6 3 8 6 3 3 4 8 6 12v2h1 1 0c0 1-1 1-1 2v1h-5c-1 0-2-2-3-2-1-1-2 0-3 0-1-1-2-2-3-2z"></path><path d="M781 253v2h0-1c-1 1-2 1-3 2-5 1-10 5-15 7v1l-3 1c-3 1-6 2-8 3-5 3-9 7-13 10-3 2-5 5-7 6-3-1-4 0-7 1h0c0-1 1-1 1-2h0-1-1v-2l1 1 2-1c9-3 17-11 25-17 9-5 20-9 30-12z" class="G"></path><path d="M762 265l1 1c-2 1-3 1-4 3-2 2-5 4-5 7h0c-2 0-2 0-3 1-4 4-8 10-13 13-3 1-7 2-10 2-1-1-1-2-2-3l2-2 3-2c2-1 4-4 7-6 4-3 8-7 13-10 2-1 5-2 8-3l3-1z" class="L"></path><defs><linearGradient id="U" x1="738.827" y1="289.486" x2="727.047" y2="288.459" xlink:href="#B"><stop offset="0" stop-color="#a5191a"></stop><stop offset="1" stop-color="#c72c34"></stop></linearGradient></defs><path fill="url(#U)" d="M762 265l1 1c-2 1-3 1-4 3-2 2-5 4-5 7h0c-2 0-2 0-3 1-4 4-8 10-13 13-3 1-7 2-10 2-1-1-1-2-2-3l2-2 1 1c16 3 17-13 27-20 1 0 2-1 3-2l3-1z"></path><path d="M728 292c3 0 7-1 10-2 5-3 9-9 13-13 1-1 1-1 3-1l-2 6-2 2h3l1 2-3 3c-1 1-1 0-1 1-3 1-6 4-9 6-4 3-9 7-13 11l-1-1v-3-5c-1-2-2-3-2-6h3z" class="L"></path><path d="M750 284h3l1 2-3 3c-1 1-1 0-1 1-3 1-6 4-9 6 1-1 1-2 2-2 1-1 0-1 1-1-2 1-3 1-4 1h0c1-1 3-2 5-4s4-4 5-6z" class="P"></path><path d="M740 294h0c1 0 2 0 4-1-1 0 0 0-1 1-1 0-1 1-2 2-4 3-9 7-13 11l-1-1v-3-5l1 3c3-3 8-5 12-7z" class="G"></path><path d="M674 249l3 2c2 1 3 3 4 3v2c4 4 7 9 9 13 3 6 5 11 7 17 4-3 8-3 13-3 1 0 2 1 3 2 1 0 2-1 3 0 1 0 2 2 3 2l-2 1h2v1c-1 1-2 1-3 1l-3-1c-3-1-6-1-9 0l-3 3c0 1-1 3-1 4-1 2 0 5 0 7 0-1-1-2-1-3l-2-7-1-1c0-1 0-2-1-2 0-1 0-3-1-4h1v-1c0-1 0-2-1-3l1-1c-1 0-1-1-2-1v-2l-2-4c-1-2-2-1-2-2-2-1-1-1-3-2 0 0-1-2-2-3-2-2-3-4-4-6-3-3-6-7-10-10h2v-1l2-1z" class="Z"></path><path d="M677 251c2 1 3 3 4 3v2l-1-1c-2 0-3-1-4-2h1 0v-2z" class="n"></path><path d="M674 249l3 2v2h0-1l-4-2v-1l2-1z" class="S"></path><defs><linearGradient id="V" x1="697.5" y1="286.461" x2="718.503" y2="286.696" xlink:href="#B"><stop offset="0" stop-color="#bc2730"></stop><stop offset="1" stop-color="#de4952"></stop></linearGradient></defs><path fill="url(#V)" d="M697 286c4-3 8-3 13-3 1 0 2 1 3 2 1 0 2-1 3 0 1 0 2 2 3 2l-2 1h2v1c-1 1-2 1-3 1l-3-1c-3-1-6-1-9 0l-3 3c0 1-1 1-1 2h-1v2h0l-1-1v-2-2l-1-1v-4z"></path><path d="M648 239c2 0 5 1 7 2l6 1c0 1 0 1-1 1l1 1h-1 0c4 2 5 4 8 6 1 0 2 1 2 1 4 3 7 7 10 10 1 2 2 4 4 6-1 1-1 2-2 3l-1-2c-1-1-2-1-3-2 0 2 1 3 1 4l-3-1c-3-5-7-9-11-13-3-3-6-6-10-8-5-2-10-3-15-4l1-1h-1l2-1h0v-1c1-2 4-1 6-2z" class="X"></path><path d="M656 246h1c4 2 7 4 11 7-1-1-2-1-3-1-1-1-1 0-2-1h0c-1-1-3-2-4-3h0c-1-1-2-1-3-2z" class="C"></path><path d="M676 260c1 1 3 2 3 2l1-1c1 2 2 4 4 6-1 1-1 2-2 3l-1-2c-1-3-3-5-5-8z" class="H"></path><path d="M665 256h3l3 3c2 1 6 5 7 7 0 2 1 3 1 4l-3-1c-3-5-7-9-11-13z" class="Q"></path><path d="M657 246l1-1c1 0 2 1 3 2h2c-1-1-1-1-2-1 0-1-1-1-2-1l1-1c4 2 5 4 8 6 1 0 2 1 2 1 4 3 7 7 10 10l-1 1s-2-1-3-2c-2-2-5-5-8-7-4-3-7-5-11-7z" class="S"></path><path d="M648 239c2 0 5 1 7 2l6 1c0 1 0 1-1 1l1 1h-1 0l-1 1c1 0 2 0 2 1 1 0 1 0 2 1h-2c-1-1-2-2-3-2l-1 1h-1c-1-1-3-1-4-2l-10-2v-1c1-2 4-1 6-2z" class="H"></path><path d="M655 241l6 1c0 1 0 1-1 1l1 1h-1c-2-1-4-1-6-2l1-1z" class="l"></path><path d="M417 617v-1l4 1c0 1 8 11 9 11 0 1 2 1 2 2l2 4c1-1 0-1 1-3 0 2 1 3 1 4v7l6 19c1 2 2 6 3 8l6 20c1 4 3 9 5 13l11 30v5l-1 1 2 5 2 4 1 6 8 18h0c1 5 3 9 5 14l4 11c0 2 1 4 2 5l4 11h2l2 4c3 4 3 9 5 13l3 7c2 4 3 7 5 11l4 7c1 3 3 7 6 9h0c1 2 3 5 2 7l1 1c0 3-2 8-4 10-3-4-4-9-6-13-4-7-8-15-11-22 0 0-1-3-6-12l-17-38c-7-14-15-28-24-40-9-13-21-25-33-35-3-2-6-4-8-7l-15-11c-2-2-5-5-8-6-4-3-12-5-17-5-1 1-3 0-3 1v1h-4c-3 0-4 1-6 2h-1c-2-1-4 0-6 1 1 0 2 1 3 2l-6 2c1 2 3 5 3 8-2-1-2-6-4-7l-3 1c-1 0-2 0-3 1-3 2-6 3-9 5-5 5-9 10-13 16-1 4-2 7-2 10v1 2c-2-1-4-2-7-2-1 10 1 21 5 30 2 4 5 9 9 12 6 9 18 13 29 14 8 1 17 0 24-4l1 1c-6 4-10 7-16 10-10 5-23 5-34 3l-9-3c-1-1-4-2-4-2-2 0-4-2-6-2-3-1-4-3-6-4-4-4-7-7-10-11-1-3-3-6-4-9l1-2c-5-11-7-22-6-34 1-3 1-5 2-8 3-12 9-21 17-29l-2-1c2-3 6-5 8-7 2-1 3-2 4-4 2-1 5-2 8-3s9-2 11-4l1-1 2-2 2-2 2-2v2c2 1 1 1 2 1s2 0 3 1l-1-1 1-1c0 2 0 3 1 4l3 1c2-1 16-2 19-3h6l3-1h3l2-1s1 1 1 2h2c2 3 4 4 7 6h0c7 0 11 0 18-3l2-1c2-2 3-3 5-6h0l1-1c1 0 1-1 2-2 0-2 1-3 1-4 1-2 0-4 2-6l1 1h0l1 1 1-1c-1-2-2-8-4-10l-1-1-1 2v-1s0-1 1-2h0l-1-2c-1-3 1 2 0-1l-2-4c-1-2 0-1-1-2l-1-1c0-1 0-1 1-2h0c-1-1-1-2-1-3l-6-15c1 1 2 3 3 3v-1h0l2 2v-1l-1-4z" class="L"></path><path d="M294 769c7 12 13 20 25 28-2 0-4-2-6-2-3-1-4-3-6-4-4-4-7-7-10-11-1-3-3-6-4-9l1-2z" class="C"></path><path d="M335 702c1-1 1-1 2-1 10-7 26-11 38-9-1 1-3 0-3 1v1h-4c-3 0-4 1-6 2h-1c-2-1-4 0-6 1h-1c-1 0-1 0-2-1v1h-2c1 0 1-1 2-1v-1c-5 2-10 4-14 6-1 1-2 1-3 1z" class="R"></path><path d="M504 839l2 5 14 33c1-2 3-5 3-7l1 1c0 3-2 8-4 10-3-4-4-9-6-13-4-7-8-15-11-22 1 0 1 1 2 1v1c5 8 8 17 12 26h0l-1-3c0-1 0-2-1-3v-1l-1-3-1-1c0-1 0-1-1-2 0-1-1-3-1-5-1-1-1-2-2-3l-1-1c0-2 1-1 0-2s-1-1-1-2c-1-1-1-3-2-4-1-2-1-3-1-4v-1z" class="y"></path><path d="M490 801l4 11h2l2 4c3 4 3 9 5 13v3 3h0-1c-1-1-12-29-13-33l1-1z" class="S"></path><path d="M496 812l2 4h0-1l-1 1c-1-1-1-3-2-5h2z" class="y"></path><path d="M503 829l3 7c2 4 3 7 5 11l4 7c1 3 3 7 6 9h0c1 2 3 5 2 7 0 2-2 5-3 7l-14-33-2-5-2-4h1 0v-3-3z" class="M"></path><path d="M503 829l3 7c1 3 1 5 0 8l-2-5-2-4h1 0v-3-3z" class="H"></path><path d="M335 702c1 0 2 0 3-1 4-2 9-4 14-6v1c-1 0-1 1-2 1h2v-1c1 1 1 1 2 1h1c1 0 2 1 3 2l-6 2c1 2 3 5 3 8-2-1-2-6-4-7l-3 1c-1 0-2 0-3 1-3 2-6 3-9 5-5 5-9 10-13 16-1 4-2 7-2 10v1 2c-2-1-4-2-7-2 1-5 2-9 4-14h0c3-8 10-16 17-20z" class="D"></path><path d="M318 722l1 1-2 4 4 2h0c1-2 1-3 2-4h0c-1 4-2 7-2 10v1 2c-2-1-4-2-7-2 1-5 2-9 4-14z" class="Q"></path><path d="M350 697h2v-1c1 1 1 1 2 1h1c1 0 2 1 3 2l-6 2c1 2 3 5 3 8-2-1-2-6-4-7l-3 1c-1 0-2 0-3 1-3 2-6 3-9 5v-1s1 0 1-1c1 0 1 0 2-1 1 0 2-1 3-1l1-1h-1l3-3v-1c1 0 1 0 2-1v-2c1 1 2 0 3 0z" class="M"></path><defs><linearGradient id="W" x1="359.915" y1="673.615" x2="354.766" y2="703.221" xlink:href="#B"><stop offset="0" stop-color="#a6000a"></stop><stop offset="1" stop-color="#dd332f"></stop></linearGradient></defs><path fill="url(#W)" d="M420 672h0l1-1c1 0 1-1 2-2 0-2 1-3 1-4 1-2 0-4 2-6l1 1c1 4 1 8 0 12-1 3-2 6-5 9h0v1c-2 1-3 3-5 3-2 2-7 3-10 4h0l-1 1c0 1 1 2 2 2s2 0 3 1c-1 0-2 0-3 1-8-3-15-4-23-6-21-4-46-7-67 2-3 2-8 4-11 8l-2-1c2-3 6-5 8-7 2-1 3-2 4-4 2-1 5-2 8-3s9-2 11-4l1-1 2-2 2-2 2-2v2c2 1 1 1 2 1s2 0 3 1l-1-1 1-1c0 2 0 3 1 4l3 1c2-1 16-2 19-3h6l3-1h3l2-1s1 1 1 2h2c2 3 4 4 7 6h0c7 0 11 0 18-3l2-1c2-2 3-3 5-6z"></path><path d="M377 678l2 2h-1c-3 3-6 2-10 2 1-1 2-1 3-1 2 0 4 0 5-2l1-1z" class="R"></path><path d="M383 678c2 1 2 1 2 2h-1c-1 1-1 2-2 2v1c-2 1-2 0-4-1 1 0 3-2 3-3l2-1z" class="J"></path><path d="M377 676l3-1 4 1 1 1-2 1-2 1-2 1-2-2 1-1-1-1z" class="P"></path><path d="M380 675h3l2-1s1 1 1 2h2c2 3 4 4 7 6h-1c-3-1-4-2-5-4h0-1v2l-1 1c-1 0-2 1-2 2h-1v-1-2h1c0-1 0-1-2-2l2-1-1-1-4-1z" class="M"></path><path d="M343 672v2c2 1 1 1 2 1s2 0 3 1l-1-1 1-1c0 2 0 3 1 4l3 1c-2 2-3 1-5 1s-4 0-7 1c-1 0-1 1-3 0v-3l2-2 2-2 2-2z" class="R"></path><path d="M343 672v2c2 1 1 1 2 1s2 0 3 1l-1-1 1-1c0 2 0 3 1 4l3 1c-2 2-3 1-5 1 0-1 0-2-1-3l-1-1c0-1-1 0-2 0 0 0-1-2-2-2l2-2z" class="l"></path><path d="M420 672h0l1-1c1 0 1-1 2-2 0-2 1-3 1-4 1-2 0-4 2-6l1 1c1 4 1 8 0 12-1 3-2 6-5 9h0v1c-2 1-3 3-5 3-2 2-7 3-10 4h0c-5 0-10-1-14-3-1-2-4-2-5-3 0 0-1-1-1-2l1-1v-2h1 0c1 2 2 3 5 4h1 0c7 0 11 0 18-3l2-1c2-2 3-3 5-6z" class="S"></path><path d="M393 686c2 0 3 0 5 1l2 1 1-1c1-1 2-1 3-1h2 1c-1-1-2 0-4 0h-2l-1-1-1-1c1 0 2 0 3 1h4c1 0 6-2 6-1 1 0 1 0 1 1-1 0-1 0-1 1-2 1-5-1-6 2 0 0 1 0 1 1h0c-5 0-10-1-14-3zm27-14h0l1-1c1 0 1-1 2-2 0-2 1-3 1-4 1-2 0-4 2-6l1 1c1 4 1 8 0 12-1 3-2 6-5 9h0v1c-2 1-3 3-5 3h0l2-2c1-1 2-1 3-2 0-2 1-3 2-4l1-1v-3h0l1-1 1-2-1-1h-1c-3 2-3 4-4 7-1 2-2 2-4 2h0c-1 0-2 1-3 1h-1l2-1c2-2 3-3 5-6z" class="H"></path><path d="M417 617v-1l4 1c0 1 8 11 9 11 0 1 2 1 2 2l2 4c1-1 0-1 1-3 0 2 1 3 1 4v7l6 19c1 2 2 6 3 8l6 20c1 4 3 9 5 13l11 30v5l-1 1 2 5 2 4 1 6 8 18h0c1 5 3 9 5 14l4 11c0 2 1 4 2 5l-1 1-18-46-7-19c-2-3-3-8-6-11-2-3-5-5-8-8-13-10-27-18-42-24 1-1 2-1 3-1-1-1-2-1-3-1s-2-1-2-2l1-1h0c3-1 8-2 10-4 2 0 3-2 5-3v-1h0c3-3 4-6 5-9 1-4 1-8 0-12h0l1 1 1-1c-1-2-2-8-4-10l-1-1-1 2v-1s0-1 1-2h0l-1-2c-1-3 1 2 0-1l-2-4c-1-2 0-1-1-2l-1-1c0-1 0-1 1-2h0c-1-1-1-2-1-3l-6-15c1 1 2 3 3 3v-1h0l2 2v-1l-1-4z" class="M"></path><path d="M422 682h1 4c2 2 3 6 3 9 1 1 1 2 2 3h0c2 1 3 2 5 4l-1 1h0c1 2 2 2 2 4v1l-1 1 5 4c-3-1-5-2-7-4-8-4-16-9-24-12-1-1-2-1-3-1s-2-1-2-2l1-1h0c3-1 8-2 10-4 2 0 3-2 5-3z" class="B"></path><path d="M417 617v-1l4 1c0 1 8 11 9 11 0 1 2 1 2 2l2 4c1-1 0-1 1-3 0 2 1 3 1 4v7l6 19c1 2 2 6 3 8l6 20c1 4 3 9 5 13l11 30v5l-1 1-34-83-1-1h0c-1-1-2-3-2-4v-1c-1 1-1 1-1 2 1 1 1 2 1 3l-7-15c-1-2-1-4-3-6l-6-15c1 1 2 3 3 3v-1h0l2 2v-1l-1-4z" class="L"></path><path d="M430 628c0 1 2 1 2 2l2 4c1-1 0-1 1-3 0 2 1 3 1 4v7c-1-5-4-9-6-14z" class="P"></path><path d="M413 618c1 1 2 3 3 3v-1h0l2 2v-1c2 3 3 6 4 9l10 25-1-1h0c-1-1-2-3-2-4v-1c-1 1-1 1-1 2 1 1 1 2 1 3l-7-15c-1-2-1-4-3-6l-6-15z" class="H"></path><path d="M724 286h0c3-1 4-2 7-1l-3 2-2 2c1 1 1 2 2 3h-3c0 3 1 4 2 6v5 3l1 1c4-4 9-8 13-11 3-2 6-5 9-6l-10 28-7 18-1 4-8 18-3 8-1 1c-1 4-20 49-20 50 0 7-4 12-6 18l1 1v-1h1c1 1 1 1 2 1h0-2v1c1 1 2 1 3 1h3c1-1 1-1 2-1h1 1c-4 4-6 8-8 12-2 3-4 5-4 9-1 2-2 4-2 6-1 1-1 2-2 2 0 3 0 5 1 8h0c-2 0-2 1-4 0l-4 9v1c-2 3-6 11-6 15 2 0 2 0 3 1-1 0-1 0-2 1s-2 3-2 5h1l-1 1c0 1-1 2-2 3v3l-1 4 1 3c-1 1-2 1-2 2 2 1 3 2 4 5l4 1v1c0 1-1 1-1 2h0l-1 3 2 2c1 1 3 2 3 3v1h0c0 1 1 1 1 2s0 1-1 1-1 0-1 1c-1-1-2-1-3-2l-2-1-2 2 1 1c-2 0-6-1-8-1h-6c-2 0-4 1-6 2h-1l-1 1h-2l-1 2-1-1-1 1-2 1v3c0 1 1 1 2 1 0 3-1 5-2 6-1 0-2 1-3 1l-1 3c-1 0-1 0-2 1h0v-3-1-2c0-1 0-2-1-3v-8-5c-1-8-1-14-3-21l1-1-2-5 1-1c0-2 0-3-1-4h0l2-3-1-2c0-1 6-7 7-8l-7 1c-4 2-9 5-13 3h0l-4 1c-5 1-8 0-12-1l-3-1 1-7 1-9c1-2 1-3 2-5l1-1 2-1v1h0c2-6 5-15 3-21l3-17 4-12c2-10 5-21 9-30l1-3c2-2 4-4 5-6 3-2 7-5 9-9 1-1 2-2 4-3 1-2 2-3 4-4 1-1 3-2 4-3 2-2 4-4 7-5l1 1 8-9 7-5 2-2 1 1-1 3h0l1-1c2 3 1 7 1 9l4-4 1-1 1-2 1-1h0c0-1 1-2 1-3 3-5 5-11 6-17 1-10 2-20 1-31 0-2-1-5 0-7 0-1 1-3 1-4l3-3c3-1 6-1 9 0l3 1c1 0 2 0 3-1v-1h-2l2-1h5v-1z" class="L"></path><path d="M708 341c1-1 0-1 1 0h1v-2l1 1 3 9c1 5 4 8 3 13v-1c-1-4-3-8-5-12-1-2-2-3-3-5l-1-3z" class="T"></path><path d="M669 418c-1 3-2 7-3 10h1l-1 1c-1 1-2 5-3 7l-2-1 1-4c1-3 2-7 1-10h1l1 1c1 0 1-1 1-2l3-2zm10-39c3-4 5-7 9-10l-9 20s-1-1-2-1c1-3 2-5 3-8 0 0 0-1-1-1z" class="B"></path><path d="M724 312l1 2v-3h-2v-5-1h0l1 1v2l2 1v1c0-1 0-1 2-2 1 7-1 16-6 22-2 3-8 8-11 10l-1-1v-1h1c5-5 10-11 12-17 1-3 1-6 1-9z" class="M"></path><path d="M673 463h2l6-9c-2 8-7 16-13 21l-1 4 1 1 3-1-1 2-2 1-2 2h0c-3 1-7 3-10 3v-1l2-2v-2c0-2 4-5 5-7 2-2 5-5 7-8 1-1 2-3 3-4z" class="R"></path><path d="M662 481l6-6-1 4 1 1 3-1-1 2-2 1h-2c-2 0-2 0-4-1z" class="AD"></path><path d="M659 483c1-1 2-2 3-2 2 1 2 1 4 1h2l-2 2h0c-3 1-7 3-10 3v-1l2-2 1-1z" class="AF"></path><path d="M659 483c1-1 2-2 3-2 2 1 2 1 4 1-1 1-4 2-5 2s-1-1-2-1z" class="t"></path><path d="M711 325v-1c3-5 8-8 12-12h1c0 3 0 6-1 9-2 6-7 12-12 17h-1c0-4-1-9 1-13z" class="L"></path><path d="M720 367c-1 4-20 49-20 50l-22 58c-1-1-1-2-2-4 1-2 3-5 4-8l3-10c1-3 3-6 4-8l12-28c2-6 4-12 7-19l11-26c1-3 1-4 3-5z" class="Z"></path><defs><linearGradient id="X" x1="682.992" y1="361.384" x2="684.508" y2="369.116" xlink:href="#B"><stop offset="0" stop-color="#363437"></stop><stop offset="1" stop-color="#4d4b52"></stop></linearGradient></defs><path fill="url(#X)" d="M695 353l1-1 1-1 2-2c1-1 3-2 4-3 2-1 3-2 4-4l1 1v1l-2 1h1 1v-1h1c1 2 2 3 3 5l-2 1c-5 4-11 8-17 11-7 5-14 12-20 19 0 1-1 2-2 2h-1l1-3c1-1 2-2 2-3l5-6 7-8 4-4 1-1 1-2 1-1h1l2-1z"></path><path d="M695 353l1-1 1-1 2-2c1-1 3-2 4-3 2-1 3-2 4-4l1 1v1l-2 1h1 1v-1h1c1 2 2 3 3 5l-2 1h0c-3 1-6 4-9 5-2 2-4 3-6 4-1 0-3 2-3 2h-2c0-2 1-3 2-4l1-2h0l2-2z" class="d"></path><path d="M679 379c1 0 1 1 1 1l-3 8c1 0 2 1 2 1 0 1 0 3-1 4-1 6-4 12-6 17-1 3-2 5-3 8l-3 2c0 1 0 2-1 2l-1-1c-1-17 5-30 15-42z" class="o"></path><path d="M675 395l2-7c1 0 2 1 2 1 0 1 0 3-1 4s-1 2-3 2z" class="E"></path><path d="M675 395c2 0 2-1 3-2-1 6-4 12-6 17-1 3-2 5-3 8l-3 2 5-15c2-3 3-7 4-10z" class="Q"></path><defs><linearGradient id="Y" x1="697.075" y1="473.806" x2="677.399" y2="448.751" xlink:href="#B"><stop offset="0" stop-color="#ed5a5a"></stop><stop offset="1" stop-color="#fa8185"></stop></linearGradient></defs><path fill="url(#Y)" d="M678 475l22-58c0 7-4 12-6 18l1 1v-1h1c1 1 1 1 2 1h0-2v1c1 1 2 1 3 1h3c1-1 1-1 2-1h1 1c-4 4-6 8-8 12-2 3-4 5-4 9-1 2-2 4-2 6-1 1-1 2-2 2 0 3 0 5 1 8h0c-2 0-2 1-4 0l-4 9v1c-2 3-6 11-6 15v-3c-2-2-2-2-4-3h2s1 0 2 1h0l1-1v-1c0-1 0-1 1-2-2-1-3-2-4-2h-1c1-2 1-4 1-6h-1c-1-1-1-3-1-5l3-6c1 2 1 3 2 4z"></path><path d="M687 474l3-8c0 3 0 5 1 8h0c-2 0-2 1-4 0zm-14 3l3-6c1 2 1 3 2 4-1 2-2 4-3 7h-1c-1-1-1-3-1-5z" class="l"></path><path d="M724 286h0c3-1 4-2 7-1l-3 2-2 2c1 1 1 2 2 3h-3c0 3 1 4 2 6v5 3l1 1v1c-2 1-2 1-2 2v-1l-2-1v-2l-1-1h0v1 5h2v3l-1-2h-1c-4 4-9 7-12 12v1c-2 4-1 9-1 13v1 2h-1c-1-1 0-1-1 0l1 3h-1v1h-1-1l2-1v-1l-1-1c-1 2-2 3-4 4-1 1-3 2-4 3l-2 2-1 1-1 1-2 1h-1 0c0-1 1-2 1-3 3-5 5-11 6-17 1-10 2-20 1-31 0-2-1-5 0-7 0-1 1-3 1-4l3-3c3-1 6-1 9 0l3 1c1 0 2 0 3-1v-1h-2l2-1h5v-1z" class="L"></path><path d="M709 323l2 2c-2 4-1 9-1 13v1 2h-1c-1-1 0-1-1 0-1-6-1-12 1-18z" class="B"></path><path d="M724 286h0c3-1 4-2 7-1l-3 2-2 2c1 1 1 2 2 3h-3c0 3 1 4 2 6v5 3l1 1v1c-2 1-2 1-2 2v-1l-2-1v-2l-1-1h0v1 5h2v3l-1-2h-1c-4 4-9 7-12 12v1l-2-2c1-6 9-10 13-13 1-6-2-10-4-15-1-2-1-3-2-4-2 0-2-1-3-2h0l3 1c1 0 2 0 3-1v-1h-2l2-1h5v-1z" class="D"></path><path d="M724 286h0c3-1 4-2 7-1l-3 2-2 2c1 1 1 2 2 3h-3-1c-2 1-3 2-4 3-1-2-1-3-3-5h-1c1 0 2 0 3-1v-1h-2l2-1h5v-1z" class="E"></path><path d="M719 287h5l1 3c-1 1-1 1-2 1-2 0-4 0-6-1h-1c1 0 2 0 3-1v-1h-2l2-1z" class="F"></path><path d="M720 295c1-1 2-2 4-3h1c0 3 1 4 2 6v5 3l1 1v1c-2 1-2 1-2 2v-1l-2-1v-2l-1-1c0-3-1-6-3-10z" class="C"></path><path d="M667 428c3 1 7 4 9 7 3 4 4 10 3 15v2c-2 4-3 8-6 11-1 1-2 3-3 4-2 3-5 6-7 8-1 2-5 5-5 7v2l-2 2v1l-1 2c-1 1-3 3-4 3h-3-2v3h-5v1h-2 0l-2 2c-4 2-9 5-13 3h0c2-1 4-3 6-5 3-3 5-7 7-11 1-2 2-3 2-5 1-1 1-1 1-2 0 1 1 1 1 2l3-8c0-2 2-4 3-6 1-3 3-5 5-7 1-3 2-5 3-7 3-5 4-12 6-17l2 1c1-2 2-6 3-7l1-1z" class="L"></path><path d="M660 462l2 3c-1 2-1 3-1 4l1 1-1 1-1 1c-1-1-1-3-2-4 0-2 1-4 2-6z" class="l"></path><path d="M660 462c1-4 2-8 4-11 1 5-1 10-2 14l-2-3z" class="H"></path><path d="M658 468c1 1 1 3 2 4l-2 1v1c0 1-3 4-3 4-2-1-3 0-5 0v-1c0-1 1-1 1-2l1-2c1-1 4-1 5-2 0-1 1-2 1-3z" class="K"></path><path d="M661 435l2 1-8 20v-4c3-5 4-12 6-17z" class="P"></path><path d="M647 466c1-3 3-5 5-7 1-3 2-5 3-7v4l-5 16c-3 4-3 8-5 12l-2-1c0-1 0-2 1-3 0-2 0-3 1-4 0-2 1-3 2-4v-6z" class="Q"></path><defs><linearGradient id="Z" x1="678.177" y1="453.005" x2="662.824" y2="452.012" xlink:href="#B"><stop offset="0" stop-color="#b5171a"></stop><stop offset="1" stop-color="#e13436"></stop></linearGradient></defs><path fill="url(#Z)" d="M667 428c3 1 7 4 9 7 3 4 4 10 3 15v2c-2 4-3 8-6 11-1 1-2 3-3 4-2 3-5 6-7 8h-3l1-1h0v-3l1-1c6-5 13-12 15-19v-1-2c0-5-3-11-6-14-1-2-3-3-4-4l-1-1 1-1z"></path><path d="M645 484c2-4 2-8 5-12 1 1 1 1 2 1l-1 2c0 1-1 1-1 2v1c2 0 3-1 5 0 0 0 3-3 3-4v-1l2-1 1-1v3h0l-1 1h3c-1 2-5 5-5 7v2l-2 2v1l-1 2c-1 1-3 3-4 3h-3-2v3h-5c2-1 4-3 5-6v-2h-2l1-3z" class="B"></path><path d="M661 471v3h0l-1 1c-1 2-3 3-3 6h-1l-1 2h-2c0-1 0-1-1-2l3-3s3-3 3-4v-1l2-1 1-1z" class="Q"></path><path d="M651 483l1-2c1 1 1 1 1 2h2c-1 2-2 4-4 6v3h-3-2v3h-5c2-1 4-3 5-6l4-5 1-1z" class="E"></path><path d="M648 492c0-1 1-3 1-4l2 1v3h-3z" class="C"></path><path d="M651 483l1-2c1 1 1 1 1 2h2c-1 2-2 4-4 6l-2-1 1-2v-2l1-1z" class="O"></path><path d="M651 483l1-2c1 1 1 1 1 2-1 1-2 3-3 3v-2l1-1z" class="F"></path><path d="M645 484c2-4 2-8 5-12 1 1 1 1 2 1l-1 2c0 1-1 1-1 2v1c2 0 3-1 5 0l-3 3-1 2-1 1-4 5v-2h-2l1-3z" class="R"></path><path d="M650 478c2 0 3-1 5 0l-3 3-1 2-2-2c0-1 0-1 1-3z" class="H"></path><path d="M647 466v6c-1 1-2 2-2 4-1 1-1 2-1 4-1 1-1 2-1 3l2 1-1 3h2v2c-1 3-3 5-5 6v1h-2 0l-2 2c-4 2-9 5-13 3h0c2-1 4-3 6-5 3-3 5-7 7-11 1-2 2-3 2-5 1-1 1-1 1-2 0 1 1 1 1 2l3-8c0-2 2-4 3-6z" class="b"></path><path d="M643 483l2 1-1 3h2v2c-1 3-3 5-5 6v1h-2 0l-1-2c1-4 4-7 5-11z" class="O"></path><path d="M639 496c2-2 3-6 5-9h2v2c-1 3-3 5-5 6v1h-2z" class="G"></path><path d="M671 479l2-2c0 2 0 4 1 5h1c0 2 0 4-1 6h1c1 0 2 1 4 2-1 1-1 1-1 2v1l-1 1h0c-1-1-2-1-2-1h-2c2 1 2 1 4 3v3c2 0 2 0 3 1-1 0-1 0-2 1s-2 3-2 5h1l-1 1c0 1-1 2-2 3v3l-1 4 1 3c-1 1-2 1-2 2 2 1 3 2 4 5l4 1v1c0 1-1 1-1 2h0l-1 3 2 2c1 1 3 2 3 3v1h0c0 1 1 1 1 2s0 1-1 1-1 0-1 1c-1-1-2-1-3-2l-2-1-2 2 1 1c-2 0-6-1-8-1h-6c-2 0-4 1-6 2h-1l-1 1h-2l-1 2-1-1-1 1-2 1v3c0 1 1 1 2 1 0 3-1 5-2 6-1 0-2 1-3 1l-1 3c-1 0-1 0-2 1h0v-3-1-2c0-1 0-2-1-3v-8-5c-1-8-1-14-3-21l1-1-2-5 1-1c0-2 0-3-1-4h0l2-3-1-2c0-1 6-7 7-8l-7 1 2-2h0 2v-1h5v-3h2 3c1 0 3-2 4-3l1-2c3 0 7-2 10-3h0l2-2 2-1 1-2z" class="E"></path><path d="M649 539h2l3-1-4 8c-1-1-1-6-1-7z" class="AD"></path><path d="M655 545c2-3 4-4 7-5 2 0 5 1 6 2v1h-6c-2 0-4 1-6 2h-1z" class="O"></path><path d="M675 543c-2-1-10-5-11-8h1c3 1 8 4 11 6h1l-2 2z" class="o"></path><path d="M671 536c-2-1-3-2-4-3 0-2-1-3 0-5s1-4 1-6l1-2 1-1v-1c1-2 2-3 2-4v-1c1-1 1-1 1-2 1-1 0-1 0-2 1-1 2-3 3-3h0 1l-1 1c0 1-1 2-2 3v3l-1 4 1 3c-1 1-2 1-2 2-1 1-1 2-2 3 0 1 0 3-1 4v3l2 4z" class="F"></path><path d="M670 525c0-2 0-3 1-5h0c0-2 1-3 2-4v1l1 3c-1 1-2 1-2 2-1 1-1 2-2 3z" class="J"></path><path d="M671 479l2-2c0 2 0 4 1 5l-6 15v-3-3h0l-2 2v-2l-3 3v1l-1 1-1-1v-1h-1l-1-1c0-1 2-2 2-2 2-1 3-2 4-3s1-2 2-3l-1-1h0l2-2 2-1 1-2z" class="n"></path><path d="M670 481l1 1-3 6-2 3-3 3v1l-1 1-1-1v-1h-1l-1-1c0-1 2-2 2-2 2-1 3-2 4-3s1-2 2-3l-1-1h0l2-2 2-1z" class="G"></path><path d="M661 494l1-2h1v2 1l-1 1-1-1v-1z" class="T"></path><path d="M672 522c2 1 3 2 4 5l4 1v1c0 1-1 1-1 2h0l-1 3 2 2c1 1 3 2 3 3v1h0c0 1 1 1 1 2s0 1-1 1-1 0-1 1c-1-1-2-1-3-2l-2-1h-1v-2h0c-1-1-2-2-2-3h-3l-2-4v-3c1-1 1-3 1-4 1-1 1-2 2-3z" class="G"></path><path d="M676 537c-1-3-1-6-3-8l1-2 1 1c1 1 1 3 2 4 0 1 1 2 1 3s-1 1-1 2h-1z" class="H"></path><path d="M674 536l2 1h1l6 3h0c0 1 1 1 1 2s0 1-1 1-1 0-1 1c-1-1-2-1-3-2l-2-1h-1v-2h0c-1-1-2-2-2-3z" class="B"></path><path d="M679 542h1c1-1 1-1 3-2 0 1 1 1 1 2s0 1-1 1-1 0-1 1c-1-1-2-1-3-2z" class="F"></path><path d="M663 494l3-3v2l2-2h0v3 3l-4 12-10 29-3 1h-2c1-6 1-12 3-18l-1-1c1-2 1-3 1-5v-2c1-3 4-8 6-10 2-3 4-5 5-8v-1z" class="L"></path><path d="M663 494l3-3v2l2-2h0v3 3l-4 12c0-2 1-5 0-6l-4 2c-3 4-5 8-7 12l-1 4-1-1c1-2 1-3 1-5v-2c1-3 4-8 6-10 2-3 4-5 5-8v-1z" class="P"></path><path d="M666 493l2-2h0v3 3l-4 12c0-2 1-5 0-6l-4 2c1-4 4-8 6-12z" class="t"></path><path d="M656 487c3 0 7-2 10-3l1 1c-1 1-1 2-2 3s-2 2-4 3c0 0-2 1-2 2l1 1h1v1l1 1 1-1c-1 3-3 5-5 8-2 2-5 7-6 10v2c0 2 0 3-1 5l1 1c-2 6-2 12-3 18 0 1 0 6 1 7v1l-1 1-2 1v3c0 1 1 1 2 1 0 3-1 5-2 6-1 0-2 1-3 1l-1 3c-1 0-1 0-2 1h0v-3-1-2c0-1 0-2-1-3v-8-5c-1-8-1-14-3-21l1-1-2-5 1-1c0-2 0-3-1-4h0l2-3-1-2c0-1 6-7 7-8l-7 1 2-2h0 2v-1h5v-3h2 3c1 0 3-2 4-3l1-2z" class="L"></path><path d="M647 552c0 1 1 1 2 1 0 3-1 5-2 6-1 0-2 1-3 1l3-8z" class="B"></path><path d="M651 520l1 1c-2 6-2 12-3 18 0 1 0 6 1 7v1l-1 1-2 1 1-20c0-3 2-6 3-9z" class="M"></path><path d="M638 520c1 0 1 0 2 1h1v1h1c0 11-1 22-2 33v-8-5c-1-8-1-14-3-21l1-1z" class="D"></path><path d="M659 493l1 1h1v1l1 1 1-1c-1 3-3 5-5 8-2 2-5 7-6 10v2l-7 7c2-5 3-9 4-14 0-3 1-5 0-7h0c1-1 2-2 3-2l7-6z" class="M"></path><path d="M659 493l1 1c-2 5-4 8-7 12 0-2 0-5-1-7l7-6z" class="AD"></path><path d="M656 487c3 0 7-2 10-3l1 1c-1 1-1 2-2 3s-2 2-4 3c0 0-2 1-2 2l-7 6c-1 0-2 1-3 2h0c-2 2-7 6-7 9-1 2-1 4-1 5l1 7h-1v-1h-1c-1-1-1-1-2-1l-2-5 1-1c0-2 0-3-1-4h0l2-3-1-2c0-1 6-7 7-8l-7 1 2-2h0 2v-1h5v-3h2 3c1 0 3-2 4-3l1-2z" class="J"></path><path d="M641 504c0 4-1 8 0 11l1 7h-1v-1h-1c-1-1-1-1-2-1l-2-5 1-1c0-2 0-3-1-4h0l2-3c1-1 3-2 3-3z" class="B"></path><path d="M656 487c3 0 7-2 10-3l1 1c-1 1-1 2-2 3s-2 2-4 3c-2 0-4 1-6 2-4 2-12 6-14 11 0 1-2 2-3 3l-1-2c0-1 6-7 7-8l-7 1 2-2h0 2v-1h5v-3h2 3c1 0 3-2 4-3l1-2z" class="X"></path><path d="M656 487c3 0 7-2 10-3l1 1c-1 1-1 2-2 3-1-1-2-1-4-1-1 1-1 2-3 2h-3l1-2z" class="C"></path><path d="M683 350l1 1-1 3h0l1-1c2 3 1 7 1 9l-7 8-5 6c0 1-1 2-2 3l-1 3c-6 9-10 21-8 33 0 2 0 4 1 6 1 3 0 7-1 10l-1 4c-2 5-3 12-6 17-1 2-2 4-3 7-2 2-4 4-5 7-1 2-3 4-3 6l-3 8c0-1-1-1-1-2 0 1 0 1-1 2 0 2-1 3-2 5-2 4-4 8-7 11-2 2-4 4-6 5l-4 1c-5 1-8 0-12-1l-3-1 1-7 1-9c1-2 1-3 2-5l1-1 2-1v1h0c2-6 5-15 3-21l3-17 4-12c2-10 5-21 9-30l1-3c2-2 4-4 5-6 3-2 7-5 9-9 1-1 2-2 4-3 1-2 2-3 4-4 1-1 3-2 4-3 2-2 4-4 7-5l1 1 8-9 7-5 2-2z" class="e"></path><path d="M683 354h0l1-1c2 3 1 7 1 9l-7 8c0-4 3-10 4-13h0l1-3z" class="o"></path><path d="M662 392c0 1 1 2 0 3v2c0 1 0 1-1 2v2c0 1-1 3-1 4v6c-1 2-1 5-1 7l-3 12c-1 1-1 3-2 3v-1l2-4c0-1 0-2 1-3v-1-1c1-1 0-1 0-3l-2 4v1c2-7 2-14 4-21 0-1 0-3 1-4 0-1 0-3 1-4h0c0-2 1-3 1-4z" class="W"></path><path d="M662 392c0-1 0 0 1-1 0-1 0-2 1-3l2-3v-2c1-2 3-4 5-4l-1 3c-6 9-10 21-8 33-1 1-1 3-2 4l-1-1c0-2 0-5 1-7v-6c0-1 1-3 1-4v-2c1-1 1-1 1-2v-2c1-1 0-2 0-3z" class="I"></path><path d="M610 478l2-1v1c-1 8-2 16-4 23l-3-1 1-7 1-9c1-2 1-3 2-5l1-1z" class="i"></path><path d="M662 415c0 2 0 4 1 6 1 3 0 7-1 10-1 2-2 3-4 4-1 1-1 1-2 1-1 1-2 2-3 2 3-6 6-12 7-19 1-1 1-3 2-4z" class="b"></path><path d="M636 429c1-10 1-21 6-30 0-2 1-3 2-4v1c-1 1-1 3-1 5l-1 11-3 24-3 22c-1 8-2 16-4 24l-1 4v1 1 2c0 1-1 1-1 2v3l-1 1c0 1 0 0 0 0h-1c1-2 1-2 1-3v-2c1-2 1-2 1-3v-3c1-1 1-1 1-2v-3c1-2 1-2 1-3v-3l1-1v-2-3h0c1-2 1-3 1-4s1-6 1-7v-1c1-1 1-6 0-8-1-4-2-8-1-12 1-2 2-5 2-7z" class="N"></path><path d="M653 438c1 0 2-1 3-2 1 0 1 0 2-1 2-1 3-2 4-4l-1 4c-2 5-3 12-6 17-1 2-2 4-3 7-2 2-4 4-5 7-1 2-3 4-3 6l-3 8c0-1-1-1-1-2 0 1 0 1-1 2 0 2-1 3-2 5l3-11c3-8 6-16 8-23 0-2 1-3 1-5h-1c-2 0-3-1-5-1-1-2-1-2-1-4h1c1 1 1 3 3 3 3 0 5-3 7-6z" class="g"></path><path d="M648 451c1 3 0 10-2 13-1 3-3 8-6 10 3-8 6-16 8-23z" class="w"></path><defs><linearGradient id="a" x1="632.462" y1="395.042" x2="643.237" y2="404.689" xlink:href="#B"><stop offset="0" stop-color="#aba9aa"></stop><stop offset="1" stop-color="#dfdfde"></stop></linearGradient></defs><path fill="url(#a)" d="M650 377h1l2-3v2l-1 1-3 3h1v1c-1 1-2 1-2 2l-2 2c-1 2-5 5-6 7 0 2 0 3-1 4v2l-1 4h-1c-2 6-6 10-8 15-1 2-2 8-3 9-1 0-3 1-4 2 2-10 5-21 9-30l1-3c2-2 4-4 5-6 3-2 7-5 9-9 1-1 2-2 4-3z"></path><path d="M683 350l1 1-1 3-1 3h0c-3 0-7 5-9 8-3 5-5 11-9 15v-1l-1-1c0-1 0-2 1-2v-1c1-2 2-3 3-4h1c1 0 2-2 3-3 0-1 1-3 2-4 0-2 2-3 3-4v-1c-2 0-4 2-5 4-1 1-1 2-1 2-1 3-2 3-4 5-1 2-2 5-3 7-3 4-6 8-7 12 0 1-1 1-1 2-1 2-2 3-3 5 0 3-1 5-3 7 1-2 1-7 2-9 0-2 1-5 2-7l1-3-1-1c0-1 0-1 1-1 0-1 0-1-1-2h-1c-1 3-3 4-6 5l2-2c0-1 1-1 2-2v-1h-1l3-3 1-1v-2l-2 3h-1c1-2 2-3 4-4 1-1 3-2 4-3 2-2 4-4 7-5l1 1 8-9 7-5 2-2z" class="N"></path><path d="M653 387c2-1 3-4 4-6 2-5 5-10 8-14 0 2 0 4-1 6-4 7-9 13-12 21l-1 1v-1c0-2 1-5 2-7z" class="i"></path><defs><linearGradient id="b" x1="664.401" y1="371.561" x2="655.02" y2="371.393" xlink:href="#B"><stop offset="0" stop-color="#7e7c7e"></stop><stop offset="1" stop-color="#9b999a"></stop></linearGradient></defs><path fill="url(#b)" d="M658 370c2-2 4-4 7-5l1 1-1 1c-3 4-6 9-8 14-1 2-2 5-4 6l1-3-1-1c0-1 0-1 1-1 0-1 0-1-1-2h-1c-1 3-3 4-6 5l2-2c0-1 1-1 2-2v-1h-1l3-3 1-1v-2l-2 3h-1c1-2 2-3 4-4 1-1 3-2 4-3z"></path><path d="M648 383c2-2 6-7 9-8 0 2-2 6-3 9h0l-1-1c0-1 0-1 1-1 0-1 0-1-1-2h-1c-1 3-3 4-6 5l2-2zm-28 119v-12c2-2 2-6 2-8l4-16c0-2 0-5 1-7 0-1 1-2 1-3 0-9 2-16 4-24 2-5 2-11 5-16v1c-1 1 0 2 0 4-1 1-1 0-1 1v2 4 1c0 2-1 5-2 7-1 4 0 8 1 12 1 2 1 7 0 8v1c0 1-1 6-1 7s0 2-1 4h0v3 2l-1 1v3c0 1 0 1-1 3v3c0 1 0 1-1 2v3c0 1 0 1-1 3v2c0 1 0 1-1 3h1s0 1 0 0h1c-2 2-4 4-6 5l-4 1z" class="W"></path><path d="M301 225h44-1c-1 1-1 1-2 1h3 46 16l8 1h1c-2 3-5 7-5 11v1 3c-8 1-14 5-20 11l-3 6-2 7c0 2-1 8 1 9 0 4 1 8 2 10 0 4 2 6 3 9l-1 2v1c-1 0-1 0-2 1h-1c0 1 0 1 1 2 0 1 2 3 2 4l4 6c2 2 4 5 5 7 1 1 1 2 2 2 2 2 3 6 4 9l1 1v1c2 3 4 7 5 10 2 4 3 10 7 13 0 1 1 2 2 3h1c-1 0-2-1-3-1h-2c-1 0-3-2-4-2h0c-3 0-9-3-12-4h1l-1 1-2-1h-4c-3 0-20-8-22-10-4-3-7-7-11-10h-1c-2 4-5 7-3 13 1 2 3 4 5 6 4 4 6 9 5 15-1 2-1 4-2 5-2 1-3 2-5 3l-4 6c-4 6-5 12-7 20h0c0 1 0 2-1 3h-1c-1-3-1-5-1-8v1l-1-1c0-1 0-2-1-2v-4c-2-1-5-6-5-9v-2l-1 1v-2-1c-1 4 1 9 2 13l1 1c2 2 1 5 1 7h-1 0c-1 0-1 1-1 2h1v3c1 2 1 2 1 4l-2-2h-1l-1-2h-1v2h-3c0-1 0-2-1-2-2-1-3-2-4-4l-1-1c-1-1-1-1-2-3-1-1-2-1-2-3l-1-1c0-1-1-2-1-2 0-1-1-2-1-3l-3-7-1-2-2-5v3c-1-4-3-7-5-10-2 2-1 5-1 8l-2-6-1 1c-2-2-2-4-4-6 0-1 0-2-1-3l-5-13-2-5-5-13h0-1v-1c-1-1-1-1-1-2v2h-1l-5-13-4-10-1-2v-5-1h-1l-6-6c-2-3-5-5-7-8h0c-2-2-4-3-5-4l-2 1c1 1 1 1 1 2-3-1-5-3-6-4l-1-1c-1-2-4-5-6-6h0c-1-2-3-3-5-4-1-2-3-3-4-4l-12-7-20-6-9-2-12-1-2-1h-5-1c-1-1-2-2-4-2l-2-1c-2-3-4-7-5-11l1-1h14 113 0 8v-1h2z" class="L"></path><path d="M170 239h11l1 3-2-1h-5-1c-1-1-2-2-4-2zm22 2c1 0 9 0 9 1v1h-2c1 0 2 1 4 1v1l-9-2c-1-1-1-2-2-2z" class="G"></path><path d="M181 239l11 2c1 0 1 1 2 2l-12-1-1-3z" class="l"></path><path d="M278 250h-4c-2-1-5-3-6-5v-3c2 1 3 3 5 4l1 1 4 3z" class="K"></path><path d="M299 299l3 3v3c0 2 1 4 1 6l1 7-5-14 1-1-1-4z" class="R"></path><path d="M383 287l5 11c0 1 0 1 1 2 0 1 2 3 2 4l-1 2-2-4-6-11 1-4z" class="E"></path><path d="M309 247c7 1 15 3 21 7l-1 2c-6-5-13-6-20-7v-1c1 1 2 0 3 0l-3-1z" class="K"></path><path d="M371 261c0-3 1-6 2-9 4-6 9-9 16-11v2c-2 1-3 2-5 3-2 0-2 0-3 1-3 1-7 6-7 9l-1 5-1-2h0c0 1-1 2-1 2zm-91-2c-4 2-6 5-9 8-3-2-5-3-8-5h2 3c5 0 10-4 13-8 0 1 0 4-1 5z" class="S"></path><path d="M201 242c8 1 14 4 21 6 1 1 2 1 2 3h-1l-20-6v-1c-2 0-3-1-4-1h2v-1z" class="H"></path><path d="M388 302l2 4 1-2 4 6c0 2 1 4 1 6l-2-2-1 1-4-4-4-6c2-1 2 0 3-3z" class="t"></path><path d="M391 304l4 6c0 2 1 4 1 6l-2-2-4-8 1-2z" class="X"></path><path d="M279 246l2-1c1 1 5 2 6 3l1-1c1 1 2 1 3 2l-2 1c-2 1-4 1-6 3h-1v-2h-1l-3-1-4-3 4-1h1z" class="r"></path><path d="M279 246c2 1 4 2 5 3v1h-2c0-2-1-2-3-4z" class="n"></path><path d="M278 246h1 0c2 2 3 2 3 4h-1 0c-1-1-2-1-3-2l1-1-1-1z" class="H"></path><path d="M278 246l1 1-1 1c1 1 2 1 3 2h0v1l-3-1-4-3 4-1z" class="R"></path><path d="M351 280c6 1 13 2 18 6h0c0 1 1 2 2 3l-2-1h-2l-9-3-7-2v-3z" class="AC"></path><path d="M302 302c7 6 10 10 11 19 1 5 0 10 0 15-1 0-1 1-1 1l-1-16c0-8-3-11-9-16v-3z" class="T"></path><path d="M283 245l9-1h3c0-1 2-3 2-3 2-2 3-3 6-5h0c-2 3-4 8-8 10l1 2c-1 0-3 1-5 1-1-1-2-1-3-2l-1 1c-1-1-5-2-6-3h2z" class="AF"></path><path d="M281 245h2c2 0 2 0 4 1 1 1 3 1 5 1-2 1-3 1-4 0l-1 1c-1-1-5-2-6-3z" class="u"></path><path d="M292 247c1-1 0-1 1-1h1c0-1 1-1 1-1v1l1 2c-1 0-3 1-5 1-1-1-2-1-3-2 1 1 2 1 4 0z" class="y"></path><path d="M301 225h44-1c-1 1-1 1-2 1h3-54 0 8v-1h2z" class="g"></path><path d="M371 261s1-1 1-2h0l1 2v6l3-2c0 3 0 6 1 9l1 4c1 2 1 3 1 5-2-3-6-6-7-10s-1-8-1-12z" class="G"></path><path d="M373 267l3-2c0 3 0 6 1 9h-1c-2-1-2-5-3-7z" class="t"></path><path d="M415 227h1c-2 3-5 7-5 11v1h0c-3 1-6 1-8 2-7 2-13 5-18 10h-1c1-4 6-5 7-8h-2v-2c4-1 9-3 14-4h7v-3c0-1 0-1 1-1l2-4 2-2z" class="M"></path><path d="M389 243h2c-1 3-6 4-7 8h1c-3 6-4 10-5 16 0 1-1 2-1 3-1 1 0 4 0 5-1 1-1 2-1 3l-1-4c-1-3-1-6-1-9l-3 2v-6l1-5c0-3 4-8 7-9 1-1 1-1 3-1 2-1 3-2 5-3z" class="J"></path><path d="M373 261l1-5c0-3 4-8 7-9 1-1 1-1 3-1-6 6-8 11-8 19l-3 2v-6z" class="AD"></path><path d="M291 310c0-1 0-2-1-3s-1-2-2-3v-1l1-1-1-1c0-1-1-2-1-3l2-1-1-1v-1h-1l1-1c1 2 3 4 4 6 6 9 10 19 14 29l4 12c0 1 1 1 0 2-2-2-2-4-4-6v-1h-1c-1-5-5-11-7-15l-2-2c-1 0-2-1-3-2 0-2-1-3-3-5v-2h1z" class="P"></path><path d="M290 310h1l3 3 4 8-2-2c-1 0-2-1-3-2 0-2-1-3-3-5v-2z" class="n"></path><path d="M291 310c0-1 0-2-1-3s-1-2-2-3v-1l1-1-1-1c0-1-1-2-1-3l2-1-1-1v-1h-1l1-1c1 2 3 4 4 6h-1l-2-2v1c0 1 1 2 1 3 2 3 4 8 4 11l-3-3z" class="G"></path><path d="M222 248c12 3 22 9 31 17 4 3 7 7 12 10h1c-1 1-1 2-2 2l-1 1h0c-2-2-4-3-5-4l-2 1c1 1 1 1 1 2-3-1-5-3-6-4l-1-1c-1-2-4-5-6-6h0c-1-2-3-3-5-4-1-2-3-3-4-4l-12-7h1c0-2-1-2-2-3z" class="S"></path><path d="M266 275c6 4 12 8 17 14 2 1 4 3 5 5l-1 1h1v1l1 1-2 1c0 1 1 2 1 3l1 1-1 1v1c1 1 1 2 2 3s1 2 1 3h-1v2c0-1-1-1-1-1l-1 1-2-4h-1c1 5 5 11 5 16h0-1v-1c-1-1-1-1-1-2v2h-1l-5-13-4-10-1-2v-5-1h-1l-6-6c-2-3-5-5-7-8l1-1c1 0 1-1 2-2z" class="H"></path><path d="M277 292c-1-1-2-3-2-5 3 3 6 5 8 9s4 9 6 13l1 1v2c0-1-1-1-1-1l-1 1-2-4h-1c1 5 5 11 5 16h0-1v-1c-1-1-1-1-1-2v2h-1l-5-13-4-10-1-2v-5-1z" class="t"></path><path d="M277 293c3 4 5 10 7 15l-2 2-4-10-1-2v-5z" class="H"></path><path d="M284 308v1-3c0-2 0-3-1-4h1c1 2 2 4 2 6h-1c1 5 5 11 5 16h0-1v-1c-1-1-1-1-1-2v2h-1l-5-13 2-2z" class="n"></path><path d="M290 324c0-5-4-11-5-16h1l2 4 1-1s1 0 1 1c2 2 3 3 3 5 1 1 2 2 3 2l2 2c2 4 6 10 7 15h1c-1 3-1 4-3 6 1 1 2 4 3 6 0 1 0 2 1 3 0 3 2 7 4 10-2 2-1 5-1 8l-2-6-1 1c-2-2-2-4-4-6 0-1 0-2-1-3l-5-13-2-5-5-13z" class="y"></path><path d="M296 335l2 5c0 1-1 1-1 2l-2-5 1-2z" class="D"></path><path d="M300 334c2 1 4 2 5 2h1c-1 3-1 4-3 6l-3-8z" class="u"></path><path d="M302 349l5 12s1 1 1 2l-1 1c-2-2-2-4-4-6 0-1 0-2-1-3l1-1c-1-2-1-3-1-5z" class="P"></path><path d="M298 340c1 2 4 7 4 9s0 3 1 5l-1 1-5-13c0-1 1-1 1-2z" class="K"></path><path d="M293 317c1 1 2 2 3 2l2 2c2 4 6 10 7 15-1 0-3-1-5-2l-7-17z" class="r"></path><path d="M290 324c0-5-4-11-5-16h1l2 4 8 23-1 2-5-13z" class="B"></path><path d="M411 239v3c-8 1-14 5-20 11l-3 6-2 7c0 2-1 8 1 9 0 4 1 8 2 10 0 4 2 6 3 9l-1 2v1c-1 0-1 0-2 1h-1l-5-11-1 4-3-8c0-2 0-3-1-5 0-1 0-2 1-3 0-1-1-4 0-5 0-1 1-2 1-3 1-6 2-10 5-16 5-5 11-8 18-10 2-1 5-1 8-2h0z" class="O"></path><path d="M388 259l-2 7c-3 5-4 12-4 17-1-2-1-4-1-6 0-3 0-7 1-9 1-1 1-4 2-5h0v2h0c0 1-1 2 0 3 0-1 1-2 1-3h0l1-1v-2h0v-1l2-2z" class="b"></path><path d="M378 278c0-1 0-2 1-3 0-1-1-4 0-5 0-1 1-2 1-3 0 3 0 7 1 10 0 2 0 4 1 6 0 2 1 3 1 4l-1 4-3-8c0-2 0-3-1-5z" class="B"></path><path d="M382 283c0-5 1-12 4-17 0 2-1 8 1 9 0 4 1 8 2 10 0 4 2 6 3 9l-1 2v1c-1 0-1 0-2 1h-1l-5-11c0-1-1-2-1-4z" class="w"></path><path d="M296 248c4-1 10-2 13-1l3 1c-1 0-2 1-3 0v1c7 1 14 2 20 7l1-2c4 3 6 7 8 12 0 1 1 4 1 5 1 1 3 2 4 3 2 2 5 5 8 6v3l7 2 9 3h2l2 1c-1-1-2-2-2-3h0c4 2 9 7 11 12h0l5 7 4 6 4 4 1-1 2 2c0-2-1-4-1-6 2 2 4 5 5 7 1 1 1 2 2 2 2 2 3 6 4 9l1 1v1c2 3 4 7 5 10 2 4 3 10 7 13 0 1 1 2 2 3h1c-1 0-2-1-3-1h-2c-1 0-3-2-4-2h0c-3 0-9-3-12-4h1l-1 1-2-1h-4c-3 0-20-8-22-10-4-3-7-7-11-10h-1c-2 4-5 7-3 13 1 2 3 4 5 6 4 4 6 9 5 15-1 2-1 4-2 5-2 1-3 2-5 3l-4 6c-4 6-5 12-7 20h0c0 1 0 2-1 3h-1c-1-3-1-5-1-8v1l-1-1c0-1 0-2-1-2v-4c-2-1-5-6-5-9v-2l-1 1v-2-1c-1 4 1 9 2 13l1 1c2 2 1 5 1 7h-1 0c-1 0-1 1-1 2h1v3c1 2 1 2 1 4l-2-2h-1l-1-2h-1v2h-3c0-1 0-2-1-2-2-1-3-2-4-4l-1-1c-1-1-1-1-2-3-1-1-2-1-2-3l-1-1c0-1-1-2-1-2 0-1-1-2-1-3l-3-7-1-2-2-5v3c-1-4-3-7-5-10s-4-7-4-10c-1-1-1-2-1-3-1-2-2-5-3-6 2-2 2-3 3-6v1c2 2 2 4 4 6 1-1 0-1 0-2h1l1-4s0-1 1-1c0-5 1-10 0-15-1-9-4-13-11-19l-3-3 1 4-1 1c-2-5-3-11-5-15-1-2-4-3-5-5-3-2-5-5-7-8-3-5-4-11-2-17 1-1 1-4 1-5l1-1h1c2-2 4-2 6-3l2-1c2 0 4-1 5-1z" class="L"></path><path d="M389 343l9 2c2 0 3 1 4 2-2-1-6-2-8-1-1-1-3-1-5-1v-2z" class="m"></path><path d="M390 315h0v-1h1l-2-2v-1l4 4c1 1 1 3 2 4l-2 1-4-5h1z" class="B"></path><path d="M283 253c2-2 4-2 6-3l3 1c-4 2-7 3-9 7v-1-2l1-1-1-1z" class="D"></path><path d="M297 287l14 8h0c-3 0-4-1-6-2l2 2h1l-1 1c-3-1-7-5-9-6-1-1-1-2-1-3z" class="E"></path><path d="M342 279l4 3c2 1 3 1 5 1l7 2 2 2c-5-1-14-1-17-4-1-1-1-2-1-4z" class="H"></path><path d="M374 305l1 1-1 2c0 7 4 18 8 23 1 1 1 0 1 1h-1c-4-3-8-11-8-15l-1-1v-2c0-3-1-7 1-9zm-78-57c4-1 10-2 13-1l3 1c-1 0-2 1-3 0v1c-6 0-12 1-17 2l-3-1 2-1c2 0 4-1 5-1z" class="G"></path><path d="M375 301l6 5 1 1c1 1 1 1 2 1v1h-1v1l3 2c1 1 3 2 4 3h-1l-14-9-1-1-3-2s1-1 2 0h1l1-2z" class="D"></path><path d="M371 303c-7-4-14-7-21-5l-4 1c9-5 17-3 26-1 1 1 2 2 3 2v1l-1 2h-1c-1-1-2 0-2 0z" class="AC"></path><path d="M376 299c0-1 0-2-1-3h0c-1-2-3-3-4-5h0c1 1 2 2 4 2h0l4 5h1l5 7 4 6v1l2 2h-1v1h0c-1-1-3-2-4-3l-3-2v-1h1v-1c-1 0-1 0-2-1l-1-1-6-5v-1h0l1 1h1l-1-2z" class="K"></path><path d="M358 285l9 3h2l2 1c-1-1-2-2-2-3h0c4 2 9 7 11 12h0-1l-4-5h0c-2 0-3-1-4-2h0c1 2 3 3 4 5h0c1 1 1 2 1 3l1 2h-1l-1-1h0c-1 0-2-1-3-2 0-1-1-2-2-3-2-3-4-4-7-6-1 0-2-1-3-2l-2-2z" class="G"></path><path d="M358 285l9 3 2 2c-2-1-4-1-6-1-1 0-2-1-3-2l-2-2z" class="S"></path><path d="M370 295l2-1c2 1 3 3 4 5h0l1 2h-1l-1-1h0c-1 0-2-1-3-2 0-1-1-2-2-3z" class="C"></path><path d="M364 361v-1c1-4 1-9-2-12-2-2-5-4-6-7s-1-6 1-9c1-2 2-3 4-3-2 4-5 7-3 13 1 2 3 4 5 6 4 4 6 9 5 15-1 2-1 4-2 5-2 1-3 2-5 3l1-2v-2h-2c2-2 3-4 4-6z" class="G"></path><path d="M363 367c2-1 3-2 5-4-1 2-1 4-2 5-2 1-3 2-5 3l1-2 1-2z" class="S"></path><path d="M364 361c1 3 0 4-1 6l-1 2v-2h-2c2-2 3-4 4-6z" class="Z"></path><path d="M291 283h0c0-2-1-4-1-5-2-8 0-14 4-20 4-4 7-5 12-6h6c-1 1-3 1-4 1-5 1-10 4-13 8-3 5-3 10-2 16l2 6v2l1 1v1c0 2 1 3 1 5h-1c0-1-1-3-2-4 0-2 0-3-1-4l-1 1h0l-1-2z" class="K"></path><path d="M361 329c-3-5-8-12-6-18h0 0 0c1 9 11 20 17 26 5 4 11 5 17 6v2c2 0 4 0 5 1 3 1 5 1 7 3h1l-1 1-2-1h-4c-3 0-20-8-22-10-4-3-7-7-11-10h-1z" class="p"></path><path d="M282 253h1l1 1-1 1v2 1c-1 5-2 11 1 16 2 3 5 6 7 9l1 2h0l1-1c1 1 1 2 1 4 1 1 2 3 2 4h1c0-2-1-3-1-5v-1l-1-1v-2c0 1 2 3 2 4s0 2 1 3c-2 2 1 7 1 9l1 4-1 1c-2-5-3-11-5-15-1-2-4-3-5-5-3-2-5-5-7-8-3-5-4-11-2-17 1-1 1-4 1-5l1-1z" class="G"></path><path d="M330 254c4 3 6 7 8 12 0 1 1 4 1 5 1 1 3 2 4 3 2 2 5 5 8 6v3c-2 0-3 0-5-1l-4-3c-4-2-8-3-11-7 1-1 1-1 1-2 1-1 2-1 2-3 0-5-2-8-5-11l1-2z" class="j"></path><path d="M360 367h2v2l-1 2-4 6c-4 6-5 12-7 20h0c0 1 0 2-1 3h-1c-1-3-1-5-1-8v1l-1-1c0-1 0-2-1-2v-4s1 0 1-1c1-5 5-10 10-14 0-1 4-3 4-4z" class="C"></path><path d="M356 371l-3 8c-3 4-5 8-6 13v1l-1-1c0-1 0-2-1-2v-4s1 0 1-1c1-5 5-10 10-14z" class="G"></path><path d="M395 310c2 2 4 5 5 7 1 1 1 2 2 2 2 2 3 6 4 9l1 1v1c2 3 4 7 5 10 2 4 3 10 7 13 0 1 1 2 2 3h1c-1 0-2-1-3-1h-2c-1 0-3-2-4-2h0c-3-2-5-5-7-8-4-8-8-17-13-25l2-1c-1-1-1-3-2-4l1-1 2 2c0-2-1-4-1-6z" class="C"></path><path d="M395 310c2 2 4 5 5 7 1 1 1 2 2 2 2 2 3 6 4 9l1 1h-2s0-1-1-2h0c-1-2-3-4-5-5l-3-6c0-2-1-4-1-6z" class="F"></path><defs><linearGradient id="c" x1="312.765" y1="341.529" x2="334.586" y2="324.921" xlink:href="#B"><stop offset="0" stop-color="#ac1b23"></stop><stop offset="1" stop-color="#c85559"></stop></linearGradient></defs><path fill="url(#c)" d="M307 296l1-1h-1l-2-2c2 1 3 2 6 2h0c4 2 8 7 9 11 2 5 3 10 5 14 4 5 9 8 13 12 4 3 6 8 7 12 1 5 1 10 0 15s-4 10-4 14c0 3 1 5 2 7s2 4 3 5c0 1-1 1-1 1-2-1-5-6-5-9v-2l-1 1v-2-1c-1-3-2-8-1-12 1-5 3-11 4-16 0-1-1-2-1-3l-1-1c-3-8-12-11-17-18-4-6-3-15-8-21-2-2-5-4-8-6z"></path><g class="L"><path d="M342 345c2 8 0 14-3 21h0l-1-5c1-5 3-11 4-16z"></path><path d="M298 290c2 1 6 5 9 6 3 2 6 4 8 6 5 6 4 15 8 21 5 7 14 10 17 18l1 1c0 1 1 2 1 3-1 5-3 11-4 16-1 4 0 9 1 12-1 4 1 9 2 13l1 1c2 2 1 5 1 7h-1 0c-1 0-1 1-1 2h1v3c1 2 1 2 1 4l-2-2h-1l-1-2h-1v2h-3c0-1 0-2-1-2-2-1-3-2-4-4l-1-1c-1-1-1-1-2-3-1-1-2-1-2-3l-1-1c0-1-1-2-1-2 0-1-1-2-1-3l-3-7-1-2-2-5v3c-1-4-3-7-5-10s-4-7-4-10c-1-1-1-2-1-3-1-2-2-5-3-6 2-2 2-3 3-6v1c2 2 2 4 4 6 1-1 0-1 0-2h1l1-4s0-1 1-1c0-5 1-10 0-15-1-9-4-13-11-19l-3-3c0-2-3-7-1-9z"></path></g><path d="M340 341l1 1-5 13-5 5h0-1c3-6 3-13 6-18h1c2 1 2 0 3-1z" class="t"></path><path d="M314 347s2 1 3 2c1 0 1-1 2 0l2 2c2 4 5 8 8 10h0l1-1h0 1c-1 2-1 3 0 5-2 0-3 0-4 1l-1-2-1 1h0l-1-1c0-1-5-7-7-8-1-2-2-4-3-7v-2z" class="d"></path><path d="M313 336c0 3 0 8 1 11v2c1 3 2 5 3 7 2 1 7 7 7 8l1 1h0l1-1 1 2c1-1 2-1 4-1-1-2-1-3 0-5h0l5-5 5-13c0 1 1 2 1 3-1 5-3 11-4 16-1 4 0 9 1 12-1 4 1 9 2 13l1 1c2 2 1 5 1 7h-1 0c-1 0-1 1-1 2h1v3c1 2 1 2 1 4l-2-2h-1l-1-2h-1v2h-3c0-1 0-2-1-2-2-1-3-2-4-4l-1-1c-1-1-1-1-2-3-1-1-2-1-2-3l-1-1c0-1-1-2-1-2 0-1-1-2-1-3l-3-7-1-2-2-5v3c-1-4-3-7-5-10s-4-7-4-10c-1-1-1-2-1-3-1-2-2-5-3-6 2-2 2-3 3-6v1c2 2 2 4 4 6 1-1 0-1 0-2h1l1-4s0-1 1-1z" class="r"></path><path d="M306 348c1 0 2 1 3 2l1 1h-2-1c-1-1-1-2-1-3z" class="u"></path><path d="M314 357h3l1 1-2 3c-1-1-2-2-2-4z" class="G"></path><path d="M314 357l-3-6c0-1 1-1 2-2h1c1 3 2 5 3 7v1h-3z" class="H"></path><path d="M318 358l3 7h-2c0 2 0 3 1 5s1 3 1 5c-3-4-4-9-5-14l2-3z" class="R"></path><path d="M307 351h1v1c1 2 3 4 4 6s1 4 2 6 1 3 2 4v3c-1-4-3-7-5-10s-4-7-4-10z" class="AF"></path><path d="M321 375c0-2 0-3-1-5s-1-3-1-5h2l8 14h-1c-1-1-2-1-3-2 0 0 0 1-1 2l2 3v3l-5-10z" class="D"></path><path d="M326 382l-2-3c1-1 1-2 1-2 1 1 2 1 3 2h1 0c1 2 3 5 4 7l2 2c1 2 2 4 4 5h1c1 0 1 1 1 3h1v3c1 2 1 2 1 4l-2-2h-1l-1-2h-1v2h-3c0-1 0-2-1-2-2-1-3-2-4-4-1-4-3-6-4-10v-3z" class="E"></path><path d="M335 388c1 2 2 4 4 5h1c1 0 1 1 1 3h1v3h-1c-1-1-1-1-1-2-1 0-2 0-2-1-1-2-2-3-4-5h0l1-3z" class="j"></path><path d="M326 382l-2-3c1-1 1-2 1-2 1 1 2 1 3 2h1 0c1 2 3 5 4 7l2 2-1 3-6-9h-2 0z" class="B"></path><path d="M326 382h0c2 2 2 4 3 6 2 4 5 7 5 11-2-1-3-2-4-4-1-4-3-6-4-10v-3z" class="T"></path><path d="M341 342c0 1 1 2 1 3-1 5-3 11-4 16-1 4 0 9 1 12-1 4 1 9 2 13l1 1c2 2 1 5 1 7h-1 0c-1 0-1 1-1 2 0-2 0-3-1-3h-1c-2-1-3-3-4-5l-2-2c-1-2-3-5-4-7h0l-8-14-3-7-1-1v-1c2 1 7 7 7 8l1 1h0l1-1 1 2c1-1 2-1 4-1-1-2-1-3 0-5h0l5-5 5-13z" class="C"></path><path d="M332 368c1-1 1-3 1-5 1-1 1-2 2-3 0 2-1 5-1 7v4h-1l-1-3z" class="n"></path><path d="M331 360h0l5-5-1 5c-1 1-1 2-2 3 0 2 0 4-1 5l-2 2c0-2 0-3 1-5-1-2-1-3 0-5z" class="AF"></path><path d="M333 371h1c1 8 3 15 6 22h-1c-2-1-3-3-4-5l-2-2 1-1c1-2 0-3 0-5h0c-1-3 0-6-1-9z" class="r"></path><path d="M317 356c2 1 7 7 7 8l1 1h0l1-1 1 2c1-1 2-1 4-1-1 2-1 3-1 5l2-2 1 3c1 3 0 6 1 9h0c0 2 1 3 0 5l-1 1c-1-2-3-5-4-7h0l-8-14-3-7-1-1v-1z" class="t"></path><path d="M330 370l2-2 1 3c1 3 0 6 1 9h0c0 2 1 3 0 5l-1 1c-1-2-3-5-4-7 1 1 2 1 3 3h1c1-2-1-6-2-7-1-2-1-3-1-5z" class="y"></path><path d="M361 329h1c4 3 7 7 11 10 2 2 19 10 22 10h4l2 1 1-1h-1c3 1 9 4 12 4h0c1 0 3 2 4 2h2c1 0 2 1 3 1h-1c-1-1-2-2-2-3 10 5 16 15 22 24-1 1-2 1-3 1l-1 1c1 1 2 2 2 4-1-2-2-3-4-3l5 7 4 4-1 1v-1l-3-3h-2c0 4 3 8 4 12 1 3 2 5 3 8 1 2 2 3 2 4l2 3 3 6 4 7c4 9 9 18 14 27 1 0 1 0 1 1 0 2 1 3 2 5h1c2 7 5 12 8 18l1 1 1 1 1 1h1c1 1 2 1 3 1v1l1 2h5 6 3c0 2 0 2-1 3-1 2-2 4-2 7 1 2 1 3 3 4s6 2 9 2c4 1 8 0 12 3h0l1 2c4 5 4 10 6 15 0 1 1 3 1 4l2 1c-3 1-6 1-10 1 1 1 3 1 5 1l1 1v1h-1v1h3c2-1 3-1 4-1h1l6-1 1 1c-1 2-1 3 0 5 0 1 0 3 1 4l4 18 1 1c1 4 2 9 5 13 0 1 1 1 1 2l1-1c0-1 0-1 2-2 0-1 1-2 2-3v1l2-1c-1-3 0-9 0-13 2 0 8 4 9 4 1-1 2-1 4-1 1 1 2 1 3 2l1 1 1-2 6 3c3-2 3-9 4-12l7-25 2 1 3-23 2 2c6 4 12 8 19 8 3-1 5-2 7-4 1 1 1 2 1 3 1 0 2-2 3-2 1 1 1 2 1 4l-1 1 2 5-1 1c2 7 2 13 3 21v5 8c1 1 1 2 1 3v2 1 3h0c1-1 1-1 2-1-2 3-4 8-5 12l-4 12c-2 5-4 9-6 14l-1 2c-1 0-1 1-1 2l-13 31-89 235-1-1c1-2-1-5-2-7h0c-3-2-5-6-6-9l-4-7c-2-4-3-7-5-11l-3-7c-2-4-2-9-5-13l-2-4h-2l-4-11c-1-1-2-3-2-5l-4-11c-2-5-4-9-5-14h0l-8-18-1-6-2-4-2-5 1-1v-5l-11-30c-2-4-4-9-5-13l-6-20c-1-2-2-6-3-8l-6-19v-7c0-1-1-2-1-4-1 2 0 2-1 3l-2-4c0-1-2-1-2-2-1 0-9-10-9-11-2-3-4-7-6-10l-3-9c2 1 2 2 3 4l1 2h1 0c-1-2-2-5-2-8 0-5 1-9 1-14-1-14-11-25-20-34-2-1-3-1-4-3-1 0-1-1-2-1h0-1c0-1-2-2-2-3-2-2-3-5-3-7-1-2-1-3-1-4 2-2 3-2 5-2l-5-2c-1 0-2 0-3 1v1l-1-1c0-1 0-3-1-4 0 0 0-1-1-1 2-2 2-3 5-3l-3-2h-1c-1 0-3 0-4-1s-1-1-1-2h-2v-2c-5 0-3-4-6-5-4-7-6-15-10-23l-13-29c-3-9-8-17-11-25 0-2 0-4-1-5l-1-3 1-4 3 4c1 1 3 4 4 4 2 2 6 6 8 6v-2-3c-2-4-3-7-4-11s-2-8-4-12l-2-3h3v-2h1l1 2h1l2 2c0-2 0-2-1-4v-3h-1c0-1 0-2 1-2h0 1c0-2 1-5-1-7l-1-1c-1-4-3-9-2-13v1 2l1-1v2c0 3 3 8 5 9v4c1 0 1 1 1 2l1 1v-1c0 3 0 5 1 8h1c1-1 1-2 1-3h0c2-8 3-14 7-20l4-6c2-1 3-2 5-3 1-1 1-3 2-5 1-6-1-11-5-15-2-2-4-4-5-6-2-6 1-9 3-13z" class="L"></path><path d="M377 416l2-5 2 1-2 5v-1l-1 1-1-1z" class="D"></path><path d="M485 659l1 4c0 2-1 3-2 5l-1 1 2-10z" class="n"></path><path d="M440 531c0-1 0-2 1-3l2 1c0 1 1 2 2 3-1 0-2 0-3 1l-2-2z" class="G"></path><path d="M377 416l1 1 1-1v1c0 1-1 3-2 4l-4-1c2-1 3-2 4-4z" class="j"></path><path d="M631 539h4l-8 9 4-9z" class="P"></path><path d="M510 616l4 1c-1 1-2 1-4 2h-2v-1h-2c1-1 3-2 4-2z" class="H"></path><path d="M543 711h8l2 2c-2 1-3 1-5 2l-1-3-4-1z" class="R"></path><path d="M434 479l3 3v7s-1-1-1-2l-1-3-1-5z" class="l"></path><path d="M542 711h1l4 1 1 3-7 1c1-2 1-3 1-5z" class="M"></path><path d="M386 415c3-1 7-1 10 1l1 1-1 1h-1 0c-3-1-6-2-9-2v-1z" class="K"></path><path d="M551 711l12-4c-3 2-7 6-10 6l-2-2zM432 526c4-1 8-1 11 1v2l-2-1c-1 1-1 2-1 3-1-2-2-3-3-4-2 0-2 0-4 1 0 1-1 2-1 3v-3-2z" class="S"></path><path d="M514 617h5v3c-1 1-1 1-2 1-2 0-5-1-7-2 2-1 3-1 4-2z" class="u"></path><path d="M425 369c2 2 6 3 8 5s3 3 4 5c1 1 2 2 2 4-1-2-2-3-4-3l-1-2-1-1c-1-1-1-2-2-3-2-1-6-3-6-5z" class="Y"></path><path d="M392 360l3-6 1-1v1c1 1 1 2 3 3h0-2v1l-3 6v-2l-2-2z" class="H"></path><path d="M525 706l2-1c-1-1-3-2-4-3l1-1c3 2 5 4 8 6 2 1 4 1 7 4-5-1-9-2-14-5z" class="d"></path><path d="M368 420c1 0 3 1 5 0l4 1c-2 2-2 3-5 4h-1-1c-2-2-2-2-2-5z" class="D"></path><path d="M480 621c2-2 1-5 2-7h1v4 1c0 5 1 9 1 14h0c-1-4-4-9-4-12z" class="K"></path><path d="M627 552c2-3 4-5 6-8 2-2 3-5 5-7 1 1 0 3 0 4-2 4-7 10-10 14 1-4 3-5 4-8-1 1-3 4-5 4v1z" class="M"></path><path d="M472 701c1-2 0-4 1-6 1-4 4-7 7-11l1 3c-3 4-7 9-9 14z" class="n"></path><path d="M476 628c-1-7-1-16 1-23 1 7 0 16 2 23l-1 3c-1-1-1-2-2-3z" class="G"></path><path d="M429 532c1 0 1-1 3-1-1 4 0 8 0 12l1 15-1-2c1-7-3-16-3-24z" class="j"></path><path d="M469 564v33l-1-6-2-18h1c0-1-1-2 0-4l1 1v2l1-8z" class="K"></path><path d="M469 656c0-1 0-2 1-3 2 14 2 27 1 41h-1v-7c1-6 1-13 0-19-1-4-2-8-1-12z" class="G"></path><path d="M490 603c1 0 3 0 4-1 3-2 5-5 6-9h0 1c0 1 0 1 1 2l-1 1c-1 1 0 3-1 5s-5 5-7 6h-4c0-2 0-3 1-4z" class="r"></path><path d="M607 614h0c0 2-1 3-2 5h1c1 0 1-1 2-1 0-1 1-2 2-2-2 3-4 6-5 9-3 4-4 9-4 14h-1v-2c-1-7 3-17 7-23z" class="S"></path><path d="M486 663c0 9-1 16-5 24l-1-3h0v-1c2-4 2-9 3-14l1-1c1-2 2-3 2-5z" class="H"></path><path d="M419 374c-3-4-3-9-8-12h-1l1-1s1 0 1 1l3 2h1c3 3 7 5 9 9 2 2 4 4 5 6h-1c-1-1-1-2-2-2l-1 1c-2 0-2-2-3-3l-3-3h0l-1-2h-1c1 1 1 3 1 4z" class="Z"></path><path d="M635 520h0v-2c0-1 1-2 1-3l2 5-1 1v3c0 4 1 10-1 14h-1v1h-4c3-6 5-12 4-19z" class="R"></path><path d="M640 547v8c1 1 1 2 1 3v2 1 3h0c1-1 1-1 2-1-2 3-4 8-5 12l-4 12-2 1-2-1c1-1 1-1 0-3l2-1c0 1 0 0 1 1 0-1 1-1 1-2 0-2 1-4 2-6h0c1-2 2-4 2-5h0c1-2 1-5 1-7v-3c0-5 0-9 1-13v-1z" class="AD"></path><path d="M433 513c1-3 0-5 1-8l1 4c1 3 3 6 6 9l8 7c-5-1-11-2-13-6l-2-1-1-2v-3h0z" class="j"></path><path d="M435 509c1 3 3 6 6 9h-2c-2-1-3-2-4-3-1-3-1-4 0-6z" class="G"></path><path d="M420 458c6 2 12 8 15 15l2 5v4l-3-3c-1-2-2-4-3-5 0-1-1-3-1-4h-1c-1-2-8-10-9-12z" class="H"></path><path d="M430 470c2 2 4 4 4 7h1l2 1v4l-3-3c-1-2-2-4-3-5 0-1-1-3-1-4z" class="G"></path><path d="M501 593c2 1 3 3 3 6 1 4 0 12 4 16l2 1c-1 0-3 1-4 2-1 0-1 0-2 1-2 2-3 3-6 5 3-4 4-6 4-11v-1c1-5 2-12 0-17-1-1-1-1-1-2z" class="E"></path><path d="M507 688l2 2c3 6 10 12 15 16h-1c-2 1-5 0-7-2l-3-2c-3-3-5-6-7-10 0-1 0-1 1-1v-1-2z" class="F"></path><path d="M479 628c1 4 4 12 7 14v-1c0 4 0 8 1 12l-2 4-1-6c-1-2-2-5-3-7-1-6-4-10-5-16 1 1 1 2 2 3l1-3z" class="S"></path><path d="M486 642v-1c0 4 0 8 1 12l-2 4-1-6v-1 1h1v-2c1-2 0-5 1-7z" class="K"></path><path d="M630 584c1 2 1 2 0 3l2 1 2-1c-2 5-4 9-6 14l-1 2c-1 0-1 1-1 2l-2-3h-1l2-3v-11c3 0 4-2 5-4z" class="y"></path><path d="M627 596c1 1 0 3 1 5l-1 2c-1 0-1 1-1 2l-2-3h-1l2-3c0-1 1-2 2-3z" class="t"></path><path d="M630 584c1 2 1 2 0 3l-3 9c-1 1-2 2-2 3v-11c3 0 4-2 5-4z" class="r"></path><path d="M484 696c0-2 1-3 2-5v-1l1 1h0c0 1 1 2 2 3v1c0 4 2 8 4 12v1l1 1 5 7h0l1 5c-8-7-11-16-16-24v-1z" class="K"></path><path d="M435 484l1 3c0 1 1 2 1 2 0 6-2 11-3 16-1 3 0 5-1 8h0c-1 0-2 1-3 1l-1-2v-1c-1 2-1 3-2 4h0v-1l-1-6-1-1 4-2 2-2 2-2c2-6 2-11 2-17z" class="C"></path><path d="M433 501h0c0 1-1 2-1 4-1 2 0 5 1 8h0c-1 0-2 1-3 1l-1-2c1 0 2-1 2-2v-7l2-2z" class="F"></path><path d="M431 503v7c0 1-1 2-2 2v-1c-1 2-1 3-2 4h0v-1l-1-6-1-1 4-2 2-2z" class="O"></path><path d="M425 507l4-2 1 2c-1 1-1 1-3 1h-1l-1-1z" class="AC"></path><path d="M445 669v-1h0c0-2-1-4-1-5h0c1 1 0 0 1 2l1 1 1 5v-1c3 6 4 12 6 18l7 20c0 1 0 2 1 3v1c-2-3-2-7-5-10-2-4-4-9-5-13l-6-20z" class="P"></path><path d="M627 552v-1c2 0 4-3 5-4-1 3-3 4-4 8-1 5 0 10 0 15-1 6-3 10-3 15v1c-1 1-1 3-1 5v-9l1-1v-3-1c-1 1-1 3-2 4 0 2 1 9-1 10 0-4 0-8 1-11s2-6 2-9c0-5-2-10-1-14 0-2 1-4 3-5z" class="T"></path><path d="M496 706c2 4 4 7 6 11 4 7 10 14 14 22 3 5 4 12 8 16 3 5 7 8 12 11-3 0-7-3-9-5-6-6-7-13-11-20-3-6-7-12-12-16l-4-4-1-5h0l-5-7h1l3 3c0-1-1-2-1-3h-1v-2l-1-1h1z" class="C"></path><path d="M499 716c2 1 4 4 5 6v3l-4-4-1-5z" class="B"></path><path d="M435 593l1 1 2 25c1 9 1 17 2 26 1 6 3 11 5 17 1 2 2 6 2 8v1l-1-5-1-1c-1-2 0-1-1-2h0c0 1 1 3 1 5h0v1c-1-2-2-6-3-8l-6-19v-7l1-1h1c1-2 0-8 0-10l-3-31z" class="G"></path><path d="M415 364c3 0 8 3 10 5h0c0 2 4 4 6 5 1 1 1 2 2 3l1 1 1 2 5 7 4 4-1 1v-1l-3-3h-2c0 4 3 8 4 12-2-2-4-6-6-9-2-4-5-8-6-12-1-2-3-4-5-6-2-4-6-6-9-9h-1z" class="y"></path><path d="M431 374c1 1 1 2 2 3l1 1 1 2 5 7 4 4-1 1v-1l-3-3h-2s-1-2-2-2c-2-4-4-7-7-11l2-1z" class="AB"></path><path d="M361 371c2-1 3-2 5-3l-2 4-2 5c-3 3-4 7-6 10-2 5-5 10-4 16 0 1 2 7 2 7v2 2h-1c-1-4-2-7-3-11h1c0-2 0-4-1-6 2-8 3-14 7-20l4-6z" class="u"></path><path d="M361 371c2-1 3-2 5-3l-2 4c-2 2-4 3-7 5l4-6z" class="H"></path><path d="M468 591l1 6 1 56c-1 1-1 2-1 3l-1-2h0l-1 1-1 8h-1c-1-8 1-15 2-23l1-49z" class="r"></path><path d="M456 702c3 3 3 7 5 10v-1c-1-1-1-2-1-3 4 8 7 16 10 25 1 3 3 8 4 12l-1 1-2-2v2l-1 1-2-4-2-5 1-1v-5l-11-30z" class="R"></path><path d="M467 732l1 2 3 10v2l-1 1-2-4-2-5 1-1v-5z" class="AD"></path><path d="M467 732l1 2v5c1 1 1 2 1 3l-1 1-2-5 1-1v-5z" class="d"></path><path d="M622 591c2-1 1-8 1-10 1-1 1-3 2-4v1 3l-1 1v9c0-2 0-4 1-5v-1 3 11l-2 3c-5 4-9 9-13 14-1 0-2 1-2 2-1 0-1 1-2 1h-1c1-2 2-3 2-5h0c4-6 10-11 13-17 1-2 1-4 2-6z" class="P"></path><path d="M548 640h1c0-1-1-1-1-2v-1c0-1-1-1-1-2 1 0 1 1 2 1l1 1v1l9 18 3 3c2 6 5 13 8 19 1 1 2 4 3 5 0 1-1 2-1 2-2 0-5-7-6-9l-18-36z" class="D"></path><path d="M401 349c3 1 9 4 12 4h0c1 0 3 2 4 2h2c1 0 2 1 3 1h-1c-1-1-2-2-2-3 10 5 16 15 22 24-1 1-2 1-3 1l-1 1c-1-2-2-3-4-5h3v-1l-1-1c-1-2-4-5-6-7s-5-4-8-6c-5-3-11-5-16-7-3-1-7-2-9-3h-1 4l2 1 1-1h-1z" class="h"></path><path d="M427 515c1-1 1-2 2-4v1l1 2c1 0 2-1 3-1v3l1 2 2 1c-1 1-1 1-1 2 1 0 1 0 2 1-1 1-2 1-3 1l-1-1v1l2 1c3 1 8 1 11 4v1l-3-2c-3-2-7-2-11-1v2 3h0c-2 0-2 1-3 1l-1-3v-3l-6-9 2-3 2 3 1-1v-1h0z" class="b"></path><path d="M428 523l2 4c1-1 2-1 2-1v2 3h0c-2 0-2 1-3 1l-1-3v-3-3z" class="F"></path><path d="M428 529h0c2 0 2 0 4-1v3h0c-2 0-2 1-3 1l-1-3zm-4-15l2 3 1-1v-1h0l1 5v-1c-1 0-1 0-1-1l-1 1c0 1 1 2 2 4v3l-6-9 2-3z" class="E"></path><path d="M427 515c1-1 1-2 2-4v1l1 2c1 0 2-1 3-1v3l1 2 2 1c-1 1-1 1-1 2h-2-1c-1 0-2-1-4-1l-1-5z" class="Q"></path><path d="M430 514c1 0 2-1 3-1v3l1 2 2 1c-1 1-1 1-1 2h-2v-1c0-2-1-1-1-2-2-1-2-2-2-4z" class="O"></path><defs><linearGradient id="d" x1="452.229" y1="551.46" x2="460.124" y2="547.3" xlink:href="#B"><stop offset="0" stop-color="#ac2128"></stop><stop offset="1" stop-color="#d1393c"></stop></linearGradient></defs><path fill="url(#d)" d="M442 533c1-1 2-1 3-1 5 8 13 14 19 21 2 4 5 7 5 11l-1 8v-2l-1-1c-1 2 0 3 0 4h-1c0-5-1-10-3-14-3-8-10-11-16-17-2-3-4-6-5-9z"></path><path d="M392 360l2 2v2c-8 10-19 15-17 29v3h0c1 3 3 6 4 9 0 1 0 2-1 4v1c0-3-3-7-4-11h-1v-3c-2-7 1-15 4-21 1-2 2-5 3-6 4-3 7-5 10-8v-1z" class="l"></path><path d="M513 702l3 2c2 2 5 3 7 2h1 1c5 3 9 4 14 5h3c0 2 0 3-1 5h-9c-4 0-7-1-11-2l-3-4h1c-2-3-5-4-7-7l1-1z" class="D"></path><path d="M518 710h1c4 2 9 4 13 6-4 0-7-1-11-2l-3-4z" class="S"></path><path d="M471 569c2 2 3 4 5 6l1 2-1 1c1 1 0 1 1 2l1 5c2 9 5 19 4 29-1 2 0 5-2 7l-8-46-1-6z" class="E"></path><path d="M471 569c2 2 3 4 5 6l1 2-1 1c1 1 0 1 1 2l1 5v1h-1c0-1-1-1-1-2v-1c-1-2-1-5-2-6 0-1-1-2-2-2l-1-6z" class="F"></path><path d="M381 405l5-22 1-1c0 7-1 14-1 20 0 4-1 9 0 13v1h0c-1 1 0 1-1 3l-1 2c-1 1-1 2-2 3v1l-1 1c-1 1-2 1-4 1h-2-1c-1 0-2-1-3-2h1c3-1 3-2 5-4 1-1 2-3 2-4l2-5-2-1 1-1v-1c1-2 1-3 1-4z" class="C"></path><path d="M381 412l1-3h0c0 2 0 3-1 5h1 1c1 0 0 0 1 1h1-1v6c-1 1-1 2-2 3v1l-1 1c-1 1-2 1-4 1h-2-1c-1 0-2-1-3-2h1c3-1 3-2 5-4 1-1 2-3 2-4l2-5z" class="Q"></path><path d="M383 414c1 0 0 0 1 1h1-1v6c-1 1-1 2-2 3 0-3 0-7 1-10zm-2-2l1-3h0c0 2 0 3-1 5h1c-1 1-1 3-2 4 0 1-1 2-1 3-1 2-2 3-4 4h-3c3-1 3-2 5-4 1-1 2-3 2-4l2-5z" class="F"></path><path d="M436 519c2 4 8 5 13 6 8 8 19 17 22 29 1 6 1 12 4 17 1 2 3 4 4 6h-1-1l-1-2c-2-2-3-4-5-6l-3-12c-1-2-2-5-3-7 0-3-1-5-3-7-2-3-5-7-9-9-2-2-4-3-6-5h-1v-1c-3-3-8-3-11-4l-2-1v-1l1 1c1 0 2 0 3-1-1-1-1-1-2-1 0-1 0-1 1-2z" class="Q"></path><path d="M477 580c-1-1 0-1-1-2l1-1h1 1c3 4 8 11 9 16 1 4 2 7 2 10-1 1-1 2-1 4-2 3-4 6-5 9l-1 2v-4h-1c1-10-2-20-4-29l-1-5z" class="C"></path><path d="M477 580c-1-1 0-1-1-2l1-1h1 1c3 4 8 11 9 16l-1 1c-1-2-1-4-2-5-1-2-1-3-2-4v1c0 1 1 3 2 5h0v3 1c-1-6-4-11-8-15z" class="E"></path><path d="M477 580c4 4 7 9 8 15 0 3 1 6 0 9 0 3-2 7-2 10h-1c1-10-2-20-4-29l-1-5z" class="L"></path><path d="M636 510c1 1 1 2 1 4l-1 1c0 1-1 2-1 3v2h0c-2 5-5 11-7 16l-15 39-5 14s-1 3-2 3h0l-1-1c0-2 0-2 1-4 0-4 2-7 3-10 2-6 5-13 7-19l9-24c2-6 4-12 7-18l1-4c1 0 2-2 3-2z" class="T"></path><path d="M636 510c1 1 1 2 1 4-2 0-4 1-5 2l1-4c1 0 2-2 3-2z" class="D"></path><defs><linearGradient id="e" x1="430.405" y1="382.504" x2="422.376" y2="389.331" xlink:href="#B"><stop offset="0" stop-color="#ba080f"></stop><stop offset="1" stop-color="#d8262c"></stop></linearGradient></defs><path fill="url(#e)" d="M419 374c0-1 0-3-1-4h1l1 2h0l3 3c1 1 1 3 3 3l1-1c1 0 1 1 2 2h1c1 4 4 8 6 12 2 3 4 7 6 9 1 3 2 5 3 8 1 2 2 3 2 4h-1c-2-2-4-4-6-5l2 5h0c-2 0-3-2-3-3h-1c0-1 0-1-1-2v-1c-1-1-1-1-3-2l-15-30z"></path><path d="M435 393v-1c0-1-2-4-3-5 0-1 0-1-1-1v-2-1c-1 0-1-1-2-2s0-1 0-2h1c1 4 4 8 6 12 2 3 4 7 6 9 1 3 2 5 3 8 1 2 2 3 2 4h-1c-2-2-4-4-6-5l-1-1c-1-3-5-10-4-13z" class="M"></path><path d="M440 407l-1-1c-1-3-5-10-4-13 2 4 6 13 10 15 1 2 2 3 2 4h-1c-2-2-4-4-6-5z" class="C"></path><path d="M434 404c2 1 2 1 3 2v1c1 1 1 1 1 2h1c0 1 1 3 3 3h0c2 4 3 9 6 12l3 7 31 65v2c0 2 1 3 2 5h-1c1 3 3 4 3 6 0 1 0 1 1 1v1 1c2 1 3 3 3 5 1 2 0 0 1 1l1 2 4 8v1l1 1-1 1-13-26c-1-2-2-5-4-7-3-4-5-11-8-16l-20-42-17-36z" class="B"></path><path d="M434 404c2 1 2 1 3 2v1c1 1 1 1 1 2h1c0 1 1 3 3 3h0c2 4 3 9 6 12l3 7-1 1h0c1 1 0 1 1 2 0 1 1 3 2 5h-1c0-1-1-2-1-4-1-1-1-2-2-3-1 0-1-1-1-2s-1-2-2-3c0-1 0-2-1-2l-1-2v-1c0-1-1-1-1-2 0 2 1 4 2 6 0 1 1 3 2 4l3 6c0 1 1 2 1 4l-17-36z" class="F"></path><path d="M482 498l6 6c1 1 1 2 3 3h-1l-1 1c0 1 1 3 1 3v1l-1 1c6 12 11 25 18 37 2 2 4 4 5 6l3 6 10 23c2 4 4 9 7 13h0l6 12c1 2 2 5 3 7h0l3 6c0 5 4 10 6 14l-1-1c-1 0-1-1-2-1 0 1 1 1 1 2v1c0 1 1 1 1 2h-1l-69-142c2 2 3 5 4 7l13 26 1-1-1-1v-1l-4-8-1-2c-1-1 0 1-1-1 0-2-1-4-3-5v-1-1c-1 0-1 0-1-1 0-2-2-3-3-6h1c-1-2-2-3-2-5z" class="J"></path><path d="M482 498l6 6c1 1 1 2 3 3h-1l-1 1c0 1 1 3 1 3v1l-1 1h0l-5-10c-1-2-2-3-2-5z" class="X"></path><path d="M487 639c2-11 10-17 15-27v1c0 5-1 7-4 11 3-2 4-3 6-5 1-1 1-1 2-1h2v1c-6 4-12 11-14 18-3 13 1 26 7 37 3 5 5 10 8 14v2l-2-2v2 1c-1 0-1 0-1 1l-6-9c0-1-1-2-1-3l-1 1c-3-2-6-9-7-12-1-1-1-2-1-3 0 0-1-1-1-2s0 0-1-1c0-1 0-1 1-2-1 0-1-1-1-2h-1v-6c-1-4-1-8-1-12l1-2z" class="K"></path><path d="M490 659l1-1h-1v-1-1-5h1v1c-1 5 2 8 3 13h0l-1-1v2c-1-3-2-5-3-7z" class="F"></path><path d="M487 639c0 4 0 8 1 12l1-4v1h0c0 3 0 5 1 8v3c1 2 2 4 3 7v-2l1 1h0c1 2 3 5 4 7 0 1 0 1 1 2l3 6 3 4 1 2s0 1 1 2v2 1c-1 0-1 0-1 1l-6-9c0-1-1-2-1-3l-1 1c-3-2-6-9-7-12-1-1-1-2-1-3 0 0-1-1-1-2s0 0-1-1c0-1 0-1 1-2-1 0-1-1-1-2h-1v-6c-1-4-1-8-1-12l1-2z" class="E"></path><path d="M487 639c0 4 0 8 1 12l2 15s-1-1-1-2 0 0-1-1c0-1 0-1 1-2-1 0-1-1-1-2h-1v-6c-1-4-1-8-1-12l1-2z" class="j"></path><path d="M397 417c4 2 8 4 9 9 2 5 2 10-1 15 0 1-1 2-2 3 6 4 12 9 17 14 1 2 8 10 9 12-1 0-2-1-3 0h-1l-6-7c-3-3-6-6-10-9-1 0-1 0-2 1-6-4-11-7-17-10v-1h1 1 0l2-1 1 1 1-1c-1-3-3-3-5-5-1 0-1 0-2-1-1 0-2 0-2-1v-1h-3 0c-2-1-3-2-5-3l5-1c1 0 2 0 3 1h1l1-1 5-2c2 0 5-1 6-3 1-1 1-2 1-3-1-2-3-3-5-5l1-1z" class="E"></path><path d="M392 444h0c6 3 12 6 17 10-1 0-1 0-2 1-6-4-11-7-17-10v-1h1 1z" class="n"></path><path d="M403 424l1 1c1 2 2 4 3 7 0 5-2 7-5 11-1 0-3-2-4-3l-6-5 6-4c2-2 4-4 5-7z" class="L"></path><path d="M471 744l2 2 1-1 47 118h0c-3-2-5-6-6-9l-4-7c-2-4-3-7-5-11l-3-7c-2-4-2-9-5-13l-2-4h-2l-4-11c-1-1-2-3-2-5l-4-11c-2-5-4-9-5-14h0l-8-18-1-6 1-1v-2z" class="J"></path><path d="M481 768l6 18h-1v-2l-1 1h-1c-2-5-4-9-5-14 1 0 1 1 2 1v1-5z" class="AF"></path><path d="M484 785h1l1-1v2h1c4 7 6 17 9 26h-2l-4-11c-1-1-2-3-2-5l-4-11z" class="u"></path><path d="M471 744l2 2c0 1 1 2 1 3l7 19v5-1c-1 0-1-1-2-1h0l-8-18-1-6 1-1v-2z" class="t"></path><path d="M471 744l2 2c0 1 1 2 1 3v4l-1 1-2-1-1-6 1-1v-2z" class="y"></path><path d="M541 617c3 1 4 4 6 7 1 1 0 1 1 1l5 8h2v-1l-2-2c-1-1-1-1-2-1 0-1-1-2-1-3v-1c1 0 2 3 3 3h2c2 3 2 8 5 10 1-1 2-1 3-1h2c2 1 5 3 7 4l2 2v1l1-2 1-8v1 2c0 3 0 7 1 9v1c0 1 0 1 1 2l-2 5-1 6c-1 3-2 5-3 7-1 1-1 1-2 3 0 0 0 1 1 2 0 1 0 4-1 6-3-6-6-13-8-19l-3-3-9-18v-1c-2-4-6-9-6-14l-3-6z" class="Q"></path><path d="M544 623c8 11 14 24 18 36l-3-3-9-18v-1c-2-4-6-9-6-14z" class="E"></path><path d="M563 637h2c2 1 5 3 7 4-1 1-2 1-3 2l-1-1h-2c-1 2-3 4-3 6h0c0 1 0 2 1 3h-1c-1 0-1-1-2-2s-3-5-3-8c0-2 1-2 2-3s2-1 3-1z" class="T"></path><path d="M565 637c2 1 5 3 7 4-1 1-2 1-3 2l-1-1h-2c-1 2-3 4-3 6h0c0 1 0 2 1 3h-1c-1 0-1-1-2-2 1-1 1-1 0-2l1-2c1-1 1-2 1-3 2-1 2-3 2-5z" class="G"></path><path d="M576 634v1 2c0 3 0 7 1 9v1c0 1 0 1 1 2l-2 5-1 6c-1 3-2 5-3 7l-2-2c-2-3-3-5-4-8l-1-3-1-3h0c-1-1-1-2-1-3h0c0-2 2-4 3-6h2l1 1c1-1 2-1 3-2l2 2v1l1-2 1-8z" class="AH"></path><path d="M570 665c0-1 1-1 1-2l-2-2c1-1 1-2 2-3v4c2-1 1 0 2-1 0 0 1 0 2-1-1 3-2 5-3 7l-2-2z" class="AI"></path><path d="M571 658c0-1 1-2 1-3s0 0 1-1h3l-1 6c-1 1-2 1-2 1-1 1 0 0-2 1v-4z" class="c"></path><path d="M572 641l2 2v1c1 2 2 5 2 7h-1-1-1c-1 1-1 2-3 2l3-4c0-1-1-2-1-3s-2-2-3-3c1-1 2-1 3-2z" class="G"></path><path d="M572 641l2 2c-1 2-1 3-1 4v2c0-1-1-2-1-3s-2-2-3-3c1-1 2-1 3-2z" class="C"></path><path d="M564 651c-1-1-1-2-1-3h0c0-2 2-4 3-6h2l1 1c1 1 3 2 3 3s1 2 1 3l-3 4s-1 1-2 1v-1h-1v1h-2l-1-3h0z" class="AI"></path><path d="M568 642l1 1c1 1 3 2 3 3v3h-1l-1-1c-1-1-1-1-1-2s-1-2-1-3h-1l1-1z" class="f"></path><path d="M564 651c-1-1-1-2-1-3h0c0-2 2-4 3-6h2l-1 1h1l-1 1v5l-1-1h-2v1c1 1 1 1 0 2h0z" class="AH"></path><path d="M605 570c4-3 6-13 7-17 1-1 1-2 2-3h1l1-1v1h1l1-2h0l1 1-1 1c0 1 0 1-1 2h0c0 2-1 3-1 5v1c-2 6-5 13-7 19-1 3-3 6-3 10-1 2-1 2-1 4l1 1h0l-33 91c-1-1-2-4-3-5 1-2 1-5 1-6-1-1-1-2-1-2 1-2 1-2 2-3 1-2 2-4 3-7l1-6 2-5 3-11h0l24-68z" class="C"></path><path d="M400 500l1 1 1 1 2-2 14 7c3 1 5 0 7 0l1 1 1 6v1 1l-1 1-2-3-2 3 6 9v3l1 3c0 8 4 17 3 24-8-4-16-13-21-21-3-5-6-13-11-17 0 0-1-1-1-2l-2-12c1-2 2-3 3-4z" class="L"></path><path d="M400 500l1 1c-1 1 0 2 0 3 0 4-1 10-1 14 0 0-1-1-1-2l-2-12c1-2 2-3 3-4z" class="K"></path><path d="M402 502l2-2 14 7c3 1 5 0 7 0l1 1 1 6v1 1l-1 1-2-3-2 3c-3-3-5-6-9-9-3-2-7-4-11-6z" class="C"></path><path d="M425 507l1 1 1 6v1 1l-1 1-2-3c-1-2-3-3-5-5l-1-1v-1c3 1 5 0 7 0z" class="K"></path><path d="M425 507l1 1 1 6-2-1c-1-2-2-3-3-5-2 0-2 0-3 1l-1-1v-1c3 1 5 0 7 0z" class="D"></path><path d="M487 653v6h1c0 1 0 2 1 2-1 1-1 1-1 2 1 1 1 0 1 1s1 2 1 2c0 1 0 2 1 3 1 3 4 10 7 12l1-1c0 1 1 2 1 3l6 9c2 4 4 7 7 10l-1 1c2 3 5 4 7 7h-1l3 4-8-3c-3-1-6-3-9-5-1-1-3-2-4-3-2-1-3-3-5-3l-1 1 2 5h-1l1 1v2h1c0 1 1 2 1 3l-3-3h-1l-1-1v-1c-2-4-4-8-4-12v-1c-1-1-2-2-2-3h0l-1-1v1c-1 2-2 3-2 5-2 3-4 6-5 9-4 13 1 30 4 43 0 2 1 5 1 7-2-4-4-10-5-14-3-8-5-17-6-25-1-5-1-10-1-15 2-5 6-10 9-14 4-8 5-15 5-24l-1-4v-2l2-4z" class="G"></path><path d="M491 669c1 3 4 10 7 12l1-1c0 1 1 2 1 3h-1c0 2 3 6 2 7h-1c-1-1-3-4-3-5-1-3-2-4-3-6v-1l-1-1v-2c-2-3-2-4-2-6z" class="D"></path><path d="M491 691l-1-8h1 0c1 2 2 3 3 4 3 2 4 6 6 9v2l-6-6-1-2c-1 0-1 0-2 1z" class="r"></path><path d="M500 690h1c1-1-2-5-2-7h1l6 9c2 4 4 7 7 10l-1 1-1-1c-1 0-1 0-1 1h0l-3-1-2-1c0-2-1-4-2-5-1-2-3-4-3-6z" class="T"></path><path d="M491 691c1-1 1-1 2-1l1 2 6 6c1 2 2 4 4 5 1 0 1 1 1 1 3 3 6 4 8 7-3-1-6-3-9-5-1-1-3-2-4-3-2-1-3-3-5-3l-1 1c-1-3-2-7-3-10z" class="n"></path><path d="M491 691c1-1 1-1 2-1l1 2c1 2 2 5 3 7 2 3 5 4 7 7-1-1-3-2-4-3-2-1-3-3-5-3l-1 1c-1-3-2-7-3-10z" class="u"></path><path d="M500 696l5 5 2 1 3 1h0c0-1 0-1 1-1l1 1c2 3 5 4 7 7h-1l3 4-8-3c-2-3-5-4-8-7 0 0 0-1-1-1-2-1-3-3-4-5v-2z" class="H"></path><path d="M507 702l3 1h0c0-1 0-1 1-1l1 1c2 3 5 4 7 7h-1c-4-2-8-4-11-8z" class="M"></path><path d="M440 407c2 1 4 3 6 5h1l2 3 3 6 4 7c4 9 9 18 14 27 1 0 1 0 1 1 0 2 1 3 2 5h1c2 7 5 12 8 18l1 1 1 1 1 1h1c1 1 2 1 3 1v1l1 2h5 6 3c0 2 0 2-1 3-1 2-2 4-2 7v-3h-1c0 2 0 3-1 5 2 2 5 3 8 4 1 0 2 1 3 2-1 0-2 1-3 1 0 1-1 2-2 3l-1 1s-1 0-2 1c0-1 0 0-1-1v-3h-1c-3 1-6 1-9 1h-1 1c-2-1-2-2-3-3l-6-6v-2l-31-65-3-7c-3-3-4-8-6-12l-2-5z" class="o"></path><path d="M449 415l3 6v1l-2-2c0-1 0 0-1-1-1 0-2 0-3-1v-3c1 1 0 1 1 1s1 0 2-1z" class="Q"></path><path d="M440 407c2 1 4 3 6 5h1l2 3c-1 1-1 1-2 1s0 0-1-1v3 1c1 2 2 3 2 5-3-3-4-8-6-12l-2-5zm42 81c1 2 2 4 4 6s4 5 6 7v2c-1 1 0 1-1 1h-3l-6-6v-2c1 1 2 1 2 2l2 2-1-1v-2c-1-1-1-1-1-2s0-2-1-3-1-2-1-4h0z" class="O"></path><path d="M482 488l-2-5c-1-1-1-2 0-3l2-1 1 1 1 1 1 1h1c1 1 2 1 3 1v1l1 2h5 6 3c0 2 0 2-1 3-1 2-2 4-2 7v-3h-1c0 2 0 3-1 5 2 2 5 3 8 4 1 0 2 1 3 2-1 0-2 1-3 1 0 1-1 2-2 3l-1 1s-1 0-2 1c0-1 0 0-1-1v-3h-1c-3 1-6 1-9 1h-1 1c-2-1-2-2-3-3h3c1 0 0 0 1-1v-2c-2-2-4-5-6-7s-3-4-4-6z" class="E"></path><path d="M492 501c1 1 3 1 5 1 3 1 7 0 10 0 1 0 2 1 3 2-1 0-2 1-3 1 0 1-1 2-2 3l-1 1s-1 0-2 1c0-1 0 0-1-1v-3h-1c-3 1-6 1-9 1h-1 1c-2-1-2-2-3-3h3c1 0 0 0 1-1v-2z" class="C"></path><path d="M491 507l2-2c2 0 4 0 7 1-3 1-6 1-9 1h-1 1z" class="E"></path><path d="M501 506l6-1c0 1-1 2-2 3l-1 1s-1 0-2 1c0-1 0 0-1-1v-3z" class="D"></path><path d="M490 486h5 6 3c0 2 0 2-1 3-1 2-2 4-2 7v-3h-1c0 2 0 3-1 5-3-1-3-1-4-4l-1-1-6-5 1-1 1-1z" class="a"></path><path d="M494 493l-6-5 1-1c3 3 6 2 11 3l-4 2c-1 0-2 1-2 1z" class="O"></path><path d="M500 490c1-1 1-1 3-1-1 2-2 4-2 7v-3h-1c0 2 0 3-1 5-3-1-3-1-4-4l-1-1s1-1 2-1l4-2z" class="g"></path><path d="M495 494l5-1c0 2 0 3-1 5-3-1-3-1-4-4z" class="O"></path><path d="M407 455c1-1 1-1 2-1 4 3 7 6 10 9l6 7h1c1-1 2 0 3 0h1c0 1 1 3 1 4 1 1 2 3 3 5l1 5c0 6 0 11-2 17l-2 2-2 2-4 2c-2 0-4 1-7 0l-14-7-2-2h-2c0-1 0-2 1-3l-2-2 1-1v1h1v-3c-1-3-1-7-1-10h-1l1-1c1 0 2 1 3 2v-1l-1-1 1-1h2v-4h-1 4 4c1-1 1-2 2-3v-2-4c-2-3-3-5-5-8l-2-2z" class="E"></path><path d="M403 480l4 3c3 2 4 3 5 6 0-1-1-1-1-1-3-2-5-4-7-5-1 0-1-1-1-2v-1z" class="B"></path><path d="M400 480l1 1c1 2 0 4 0 6 1 2 2 4 3 5l1 1h0c1 0 2 1 3 2h-1c-2-1-3-1-4-1s-1-1-2-1v-3c-1-3-1-7-1-10z" class="C"></path><path d="M419 463l6 7h1c1-1 2 0 3 0h1c0 1 1 3 1 4h-1v3c0 2 0 3-1 5h0c-2 1-2 1-3 3 0-5-1-9-3-14-1-2-3-5-5-6l-1-1 1-1 1 1v-1z" class="R"></path><path d="M425 470h1c1-1 2 0 3 0h1c0 1 1 3 1 4h-1v3c0 2 0 3-1 5h0c0-4-2-8-4-12z" class="O"></path><path d="M407 455c1-1 1-1 2-1 4 3 7 6 10 9v1l-1-1-1 1 1 1c3 7 6 15 3 21 0 2-1 3-2 4-2 1-3 0-5 0l-1-1h-1c-1-3-2-4-5-6l-4-3-1-1 1-1h2v-4h-1 4 4c1-1 1-2 2-3v-2-4c-2-3-3-5-5-8l-2-2z" class="H"></path><path d="M409 457l2-1c2 2 4 4 5 6v1c0 1-1 2-2 2l-5-8z" class="j"></path><path d="M414 465c1 0 2-1 2-2 2 3 4 7 3 10s-4 6-7 7l-4 2-1 1-4-3-1-1 1-1h2v-4h-1 4 4c1-1 1-2 2-3v-2-4z" class="B"></path><path d="M404 474h4 4c-2 1-3 1-4 2l-3-2h-1z" class="G"></path><path d="M403 480l-1-1 1-1h2l3 1 1 1h3l-4 2-1 1-4-3z" class="D"></path><path d="M429 482c1-2 1-3 1-5v-3h1c1 1 2 3 3 5l1 5c0 6 0 11-2 17l-2 2-2 2-4 2c-2 0-4 1-7 0l-14-7-2-2h-2c0-1 0-2 1-3l-2-2 1-1v1h1c1 0 1 1 2 1s2 0 4 1h1l2 1 6-1 2 1c1-1 3-1 3-2 4-2 4-6 5-9 1-2 1-2 3-3h0z" class="g"></path><path d="M416 495l2 1h3v1h1 1s1-1 2-1c-2 2-4 3-6 3h-2l-1-1v-3z" class="G"></path><path d="M429 482h0c0 4 0 10-4 14h0c-1 0-2 1-2 1h-1-1v-1h-3c1-1 3-1 3-2 4-2 4-6 5-9 1-2 1-2 3-3z" class="K"></path><path d="M399 493l1-1v1h1c1 0 1 1 2 1s2 0 4 1h1l2 1 6-1v3l1 1h-4-8c-1-1-2-1-3-1h-2c0-1 0-2 1-3l-2-2z" class="Q"></path><path d="M410 496l6-1v3l-7-1 1-1z" class="D"></path><path d="M399 493l1-1v1h1c1 0 1 1 2 1s2 0 4 1h1l2 1-1 1c-3-1-6-2-8-2l-2-2z" class="B"></path><path d="M339 373v1 2l1-1v2c0 3 3 8 5 9v4c1 0 1 1 1 2l1 1v-1c0 3 0 5 1 8h1c1-1 1-2 1-3h0c1 2 1 4 1 6h-1c1 4 2 7 3 11h1v-2-2l9 9 5 1c0 3 0 3 2 5h1c1 1 2 2 3 2h1 2c2 0 3 0 4-1l1-1v-1c1-1 1-2 2-3l1-2c1-2 0-2 1-3h0c3 0 6 1 9 2h0 1c2 2 4 3 5 5 0 1 0 2-1 3-1 2-4 3-6 3l-5 2-1 1h-1c-1-1-2-1-3-1l-5 1c2 1 3 2 5 3h0 3v1c0 1 1 1 2 1 1 1 1 1 2 1 2 2 4 2 5 5l-1 1-1-1-2 1h0c-8-4-13-3-21-2-1-1-2-1-3-1s-2-1-3-1v1c-2-1-3-1-5-1h0c0-1 0-1-1-2-2-1-3-2-5-4l-1-1-2-1v3h0l-4-4h0c-1-2-2-3-2-4-2-4-3-7-4-11s-2-8-4-12l-2-3h3v-2h1l1 2h1l2 2c0-2 0-2-1-4v-3h-1c0-1 0-2 1-2h0 1c0-2 1-5-1-7l-1-1c-1-4-3-9-2-13z" class="D"></path><path d="M358 421c-2-2-2-3-2-5h1c1 0 2 2 3 3 1 0 2 2 3 2h2c-1 0-2 1-2 1-2-1-3-2-5-1z" class="C"></path><path d="M358 421c2-1 3 0 5 1l2 2h0l-1 1 1 1c1 2 1 2 1 4-1-1-4-3-5-4-1-2-2-4-3-5z" class="F"></path><path d="M365 424c2 2 4 3 6 4l7 3 1 1c2 1 3 2 5 3h0c-2 1-5 0-6 1l-10-5-2-1c0-2 0-2-1-4l-1-1 1-1z" class="Q"></path><path d="M368 431c1-1 1-1 3-1 1 1 1 2 3 3h2l2-2 1 1c2 1 3 2 5 3h0c-2 1-5 0-6 1l-10-5z" class="O"></path><path d="M349 419h1l1 2c1 1 1 2 2 3h1v1c2 2 5 5 8 7 0 1 4 2 4 3v1h-4l3 2c-1 0-2 1-3 1l-2 1h0c0-1 0-1-1-2-2-1-3-2-5-4l-1-1h2 0l-2-2c-2-2-3-4-4-6l1-2-1-4z" class="K"></path><path d="M355 433h0l-2-2c-2-2-3-4-4-6l1-2c3 5 6 10 12 13l3 2c-1 0-2 1-3 1l-2 1h0c0-1 0-1-1-2-2-1-3-2-5-4l-1-1h2z" class="F"></path><path d="M355 433c2 2 3 3 5 4l2 2-2 1h0c0-1 0-1-1-2-2-1-3-2-5-4l-1-1h2z" class="Q"></path><path d="M384 421l1-2c1-2 0-2 1-3 0 4-1 8 2 11 1 2 3 2 6 2l-5 2-1 1h-1c-1-1-2-1-3-1l-5 1-1-1-7-3c-2-1-4-2-6-4h0l-2-2s1-1 2-1h-2c-1 0-2-2-3-2h3l5 1c0 3 0 3 2 5h1c1 1 2 2 3 2h1 2c2 0 3 0 4-1l1-1v-1c1-1 1-2 2-3z" class="j"></path><path d="M384 435h3v1c0 1 1 1 2 1 1 1 1 1 2 1 2 2 4 2 5 5l-1 1-1-1-2 1h0c-8-4-13-3-21-2-1-1-2-1-3-1s-2-1-3-1v1c-2-1-3-1-5-1l2-1c1 0 2-1 3-1l8-1h1c1 0 2 0 4-1 1-1 4 0 6-1z" class="F"></path><path d="M373 437h1l3 1 1 1c-2 1-2 1-4 1l-1-1v-2z" class="Q"></path><path d="M365 438l8-1v2c-2 0-3 0-4 1h-4v1c-2-1-3-1-5-1l2-1c1 0 2-1 3-1z" class="AC"></path><path d="M386 416c3 0 6 1 9 2h0 1c2 2 4 3 5 5 0 1 0 2-1 3-1 2-4 3-6 3-3 0-5 0-6-2-3-3-2-7-2-11h0z" class="L"></path><path d="M339 373v1 2l1-1v2c0 3 3 8 5 9v4c1 0 1 1 1 2l1 1v-1c0 3 0 5 1 8h1c1-1 1-2 1-3h0c1 2 1 4 1 6h-1v4l-1 1 1 3h0v2c1 1 1 1 1 2v3 3l-1-2h-1l1 4-1 2c1 2 2 4 4 6l2 2h0-2l-2-1v3h0l-4-4h0c-1-2-2-3-2-4-2-4-3-7-4-11s-2-8-4-12l-2-3h3v-2h1l1 2h1l2 2c0-2 0-2-1-4v-3h-1c0-1 0-2 1-2h0 1c0-2 1-5-1-7l-1-1c-1-4-3-9-2-13z" class="AC"></path><path d="M349 415c1 1 1 2 2 3v3l-1-2h-1l-1-2 1-2z" class="F"></path><path d="M347 428c2 1 3 3 4 4v3h0l-4-4v-3z" class="E"></path><path d="M342 396v-1c1 1 1 1 1 2 0 2 1 2 1 3v1c0 1 1 1 1 2v1 1c-1 1-1 1-2 3h1v3c0 2 1 3 2 4h-1l-1-2v4 1c-2-6-2-11-6-17v-2h1l1 2h1l2 2c0-2 0-2-1-4v-3z" class="F"></path><path d="M335 401h3c4 6 4 11 6 17 1 3 2 7 3 10v3h0c-1-2-2-3-2-4-2-4-3-7-4-11s-2-8-4-12l-2-3z" class="B"></path><path d="M339 373v1 2l1-1v2c0 3 3 8 5 9v4c1 0 1 1 1 2l1 1v-1c0 3 0 5 1 8h1c1-1 1-2 1-3h0c1 2 1 4 1 6h-1v4l-1 1 1 3h0v2c1 1 1 1 1 2v3c-1-1-1-2-2-3-1-3-2-7-4-10v-1-1c0-1-1-1-1-2v-1c0-1-1-1-1-3 0-1 0-1-1-2v1h-1c0-1 0-2 1-2h0 1c0-2 1-5-1-7l-1-1c-1-4-3-9-2-13z" class="j"></path><path d="M341 396c0-1 0-2 1-2h0 1c1 0 1 0 2 1 1 2 1 7 0 9v-1c0-1-1-1-1-2v-1c0-1-1-1-1-3 0-1 0-1-1-2v1h-1z" class="E"></path><defs><linearGradient id="f" x1="373.595" y1="469.589" x2="404.314" y2="468.797" xlink:href="#B"><stop offset="0" stop-color="#9e060f"></stop><stop offset="1" stop-color="#d13133"></stop></linearGradient></defs><path fill="url(#f)" d="M351 432l2 1 1 1c2 2 3 3 5 4 1 1 1 1 1 2h0c2 0 3 0 5 1v-1c1 0 2 1 3 1s2 0 3 1c8-1 13-2 21 2h-1-1v1c6 3 11 6 17 10l2 2c2 3 3 5 5 8v4 2c-1 1-1 2-2 3h-4-4 1v4h-2l-1 1 1 1v1c-1-1-2-2-3-2l-1 1h1c0 3 0 7 1 10v3h-1v-1l-1 1c-1-2-1-3-2-5h-1c-1 1-1 1-1 3l-1-3c-1-3-4-6-6-8v-1-1c0-1-1-2-2-3l1-2v-1l1 1c1 1 1 2 3 3v-1c-1-1-1-3-2-3v-1-1c-3-5-7-10-12-12-2-1-3-2-4-3l-6-2-4-3h-1c-2-2-3-4-5-5-1 0-1-1-2-2 0-1 0 0-1-1v-1-1l-3-2v-3h0v-3z"></path><path d="M389 470c4 6 6 12 8 18h-1c-1 1-1 1-1 3l-1-3c-1-3-4-6-6-8v-1-1c0-1-1-2-2-3l1-2v-1l1 1c1 1 1 2 3 3v-1c-1-1-1-3-2-3v-1-1z" class="Q"></path><path d="M351 432l2 1 1 1c2 2 3 3 5 4 1 1 1 1 1 2h0c2 0 3 0 5 1v-1c1 0 2 1 3 1s2 0 3 1h-2c1 1 1 1 2 1 2 1 3 1 5 3h-3 0c1 2 3 2 4 4l-1 1 3 3-2 2c-1 0-3-1-4-1l-6-2-4-3h-1c-2-2-3-4-5-5-1 0-1-1-2-2 0-1 0 0-1-1v-1-1l-3-2v-3h0v-3z" class="R"></path><path d="M360 440h0c2 0 3 0 5 1v-1c1 0 2 1 3 1s2 0 3 1h-2c-2-1-7 0-10 0l-1-2h2z" class="D"></path><path d="M351 432l2 1 1 1c2 2 3 3 5 4 1 1 1 1 1 2h-2l1 2c-2 0-3-1-5-2l-3-2v-3h0v-3z" class="G"></path><path d="M351 432l2 1 1 1v4l-3-3v-3z" class="F"></path><path d="M354 438v-4c2 2 3 3 5 4 1 1 1 1 1 2h-2c-1 0-2-1-4-2z" class="E"></path><path d="M371 442c8-1 13-2 21 2h-1-1v1c6 3 11 6 17 10l2 2c2 3 3 5 5 8v4 2c-1 1-1 2-2 3h-4-4c-2-1-6-6-8-9l-9-9c-4-3-7-7-11-10-2-2-3-2-5-3-1 0-1 0-2-1h2z" class="H"></path><path d="M396 465c3 1 4 2 6 4 1 0 2 1 3 1l7 1h2c-1 1-1 2-2 3h-4-4c-2-1-6-6-8-9z" class="K"></path><path d="M371 442c8-1 13-2 21 2h-1-1v1l-1 2-2-1-2-1-1-1h-6v1c2 1 3 0 5 1 0 0 1 1 2 1 1 2 3 4 3 7h0 0c-1-3-3-5-4-7-2-1-3 0-5 0 2 3 6 5 9 9h-1c-4-3-7-7-11-10-2-2-3-2-5-3-1 0-1 0-2-1h2z" class="r"></path><defs><linearGradient id="g" x1="403.431" y1="453.258" x2="400.545" y2="458.283" xlink:href="#B"><stop offset="0" stop-color="#150404"></stop><stop offset="1" stop-color="#2b0a0a"></stop></linearGradient></defs><path fill="url(#g)" d="M390 445c6 3 11 6 17 10l2 2c2 3 3 5 5 8v4c-1 0-1-1-2-1s-2-2-3-3c-1-2-3-4-5-6-4-4-10-9-15-12l1-2z"></path><path d="M330 418l3 4c1 1 3 4 4 4 2 2 6 6 8 6v-2-3c0 1 1 2 2 4h0l4 4v3l3 2v1 1c1 1 1 0 1 1 1 1 1 2 2 2 2 1 3 3 5 5h1l4 3 6 2c1 1 2 2 4 3 5 2 9 7 12 12v1 1c1 0 1 2 2 3v1c-2-1-2-2-3-3l-1-1v1l-1 2c1 1 2 2 2 3v1 1c2 2 5 5 6 8l1 3c0-2 0-2 1-3h1c1 2 1 3 2 5l2 2c-1 1-1 2-1 3h2l2 2-2 2-1-1-1-1c-1 1-2 2-3 4l2 12-12-13c-3-1-7-6-9-6l-1 1c1 1 2 2 2 3v1l4 3-1 2c-2 0-4-1-6-1h-1c-1 0-1 0-1 1v3l-1 1-2-2v3c-5 0-3-4-6-5-4-7-6-15-10-23l-13-29c-3-9-8-17-11-25 0-2 0-4-1-5l-1-3 1-4z" class="M"></path><path d="M339 432h1c1 0 2 2 3 2h4l2 3c-1-1-2-1-3-1h-1c-2 0-2-1-4-2v2h0v3c0-2-1-2-1-3l-1-1 1-2v-1h-1z" class="P"></path><path d="M347 431h0l4 4v3l3 2v1 1c1 1 1 0 1 1-2-1-4-3-5-4 0-1-1-1-1-2l-2-3v-3z" class="K"></path><path d="M333 422c1 1 3 4 4 4 2 2 6 6 8 6v-2-3c0 1 1 2 2 4v3h-4c-1 0-2-2-3-2h-1c0-1-1-2-2-2 0-1-1-2-1-2h-1c-1-2-2-4-2-6z" class="R"></path><path d="M369 496v-1c-1-1-1-1-1-2-1-1-1-2-2-2 1-1 2-1 4-1v1h2c4 1 5 5 9 6 3 2 4 4 6 6-3-1-7-6-9-6l-1 1c1 1 2 2 2 3v1l-10-6z" class="j"></path><path d="M369 496l10 6 4 3-1 2c-2 0-4-1-6-1h-1c-1 0-1 0-1 1v3l-1 1-2-2v-1c-1-1-1-3-1-4-1-1-1-1-1-2s-1-1-1-2c0-2 0-3 1-4z" class="H"></path><path d="M354 463h-2-2c-1-1-2-3-3-5l6-4v-1l-2-1c-1-1-3-3-3-4v-1l-1-1v-5h1c3 0 4 2 6 4 3 2 5 4 8 5h1l4 3 6 2c1 1 2 2 4 3 5 2 9 7 12 12v1 1c1 0 1 2 2 3v1c-2-1-2-2-3-3l-1-1v1c-2-2-3-4-5-6-1 1-2 1-3 0 0 1 1 1 1 2l-2-2h-2-1-1l-1 1-2 2h0c-2 1-3 2-5 2l-2-1h-1c-1-1-1-1-2-1s-2 1-3 0h0c-1-1-2-1-4-1-1-2 0-4 0-6z" class="J"></path><path d="M366 464c3 1 7 1 9 3h-1l-1 1-2 2h0l-4-2c-1-1-1-2-1-4z" class="E"></path><path d="M359 464c2 0 5-1 7 0 0 2 0 3 1 4l4 2c-2 1-3 2-5 2l-2-1h1c0-1 0-1-1-1-1-2-3-3-4-6h-1z" class="K"></path><path d="M359 464c2 0 5-1 7 0 0 2 0 3 1 4-2-1-4-2-7-4h-1z" class="j"></path><path d="M374 462c-2-1-4-2-7-3h-7 0 4v-1c0-3 1-4 3-5l6 2c1 1 2 2 4 3h-1l-4-2h-1l-2-1h-3v2l1-1h3l2 2c0 2 1 3 2 4z" class="B"></path><path d="M354 463c2 0 4 0 5 1h1c1 3 3 4 4 6 1 0 1 0 1 1h-1-1c-1-1-1-1-2-1s-2 1-3 0h0c-1-1-2-1-4-1-1-2 0-4 0-6z" class="R"></path><path d="M374 462c-1-1-2-2-2-4l-2-2h-3l-1 1v-2h3l2 1h1l4 2h1c5 2 9 7 12 12v1 1c1 0 1 2 2 3v1c-2-1-2-2-3-3l-1-1v1c-2-2-3-4-5-6s-5-4-8-5z" class="C"></path><path d="M382 467c2 2 3 4 5 6l-1 2c1 1 2 2 2 3v1 1c2 2 5 5 6 8l1 3c0-2 0-2 1-3h1c1 2 1 3 2 5l2 2c-1 1-1 2-1 3h2l2 2-2 2-1-1-1-1c-1 1-2 2-3 4l2 12-12-13c-2-2-3-4-6-6a30.44 30.44 0 0 0-8-8c-2-1-6-4-7-6-1-1-1-2-1-3-1 0-1 0-2 1 0 1 0 1-1 2s-1 2-1 3h-1v-1c0-1 1-1 0-2h-1c-2 0-1-1-2-2v-2c-1-2-2-3-2-5v-1c-1-1-1-1-1-2l1-1 1 1c2 1 3 0 5 1l2-1h1l2 1c2 0 3-1 5-2h0l2-2 1-1h1 1 2l2 2c0-1-1-1-1-2 1 1 2 1 3 0z" class="B"></path><path d="M388 480c2 2 5 5 6 8-1 1 0 1-2 1 0-3-5-7-7-9h3z" class="D"></path><path d="M395 491c0-2 0-2 1-3h1c1 2 1 3 2 5l2 2c-1 1-1 2-1 3h2l2 2-2 2-1-1-1-1-2-2c-1-1-2-6-3-7z" class="AC"></path><path d="M371 470c3 1 7 3 10 5 2 1 3 3 6 4h1v1h-3l-2-1-4-2c-4-2-8-3-13-5 2 0 3-1 5-2h0z" class="T"></path><path d="M382 467c2 2 3 4 5 6l-1 2c1 1 2 2 2 3v1h-1c-3-1-4-3-6-4-3-2-7-4-10-5l2-2 1-1h1 1 2l2 2c0-1-1-1-1-2 1 1 2 1 3 0z" class="F"></path><path d="M379 470c3 1 5 3 7 5 1 1 2 2 2 3v1h-1c-1-2-2-3-3-4-2-1-3-3-5-4v-1z" class="O"></path><path d="M382 467c2 2 3 4 5 6l-1 2c-2-2-4-4-7-5l-3-3h2l2 2c0-1-1-1-1-2 1 1 2 1 3 0z" class="B"></path><g class="L"><path d="M365 480c4-2 5-2 9 0 8 3 19 11 22 19l1 5 2 12-12-13c-2-2-3-4-6-6a30.44 30.44 0 0 0-8-8c-2-1-6-4-7-6-1-1-1-2-1-3z"></path><path d="M379 502v-1c0-1-1-2-2-3l1-1c2 0 6 5 9 6l12 13c0 1 1 2 1 2 5 4 8 12 11 17 5 8 13 17 21 21l1 2c6 3 12 6 18 7l9 3c-10 0-18-3-27-7l3 33-1-1 3 31c0 2 1 8 0 10h-1l-1 1c0-1-1-2-1-4-1 2 0 2-1 3l-2-4c0-1-2-1-2-2-1 0-9-10-9-11-2-3-4-7-6-10l-3-9c2 1 2 2 3 4l1 2h1 0c-1-2-2-5-2-8 0-5 1-9 1-14-1-14-11-25-20-34-2-1-3-1-4-3-1 0-1-1-2-1h0-1c0-1-2-2-2-3-2-2-3-5-3-7-1-2-1-3-1-4 2-2 3-2 5-2l-5-2c-1 0-2 0-3 1v1l-1-1c0-1 0-3-1-4 0 0 0-1-1-1 2-2 2-3 5-3l-3-2h-1c-1 0-3 0-4-1s-1-1-1-2h-2v-2-3l2 2 1-1v-3c0-1 0-1 1-1h1c2 0 4 1 6 1l1-2-4-3z"></path></g><path d="M383 530c2-2 3-2 5-2l2 1c-1 0-3 0-4 1v5l-2-1c-1-2-1-3-1-4z" class="R"></path><path d="M384 534l2 1c2 6 6 8 10 13-2-1-3-1-4-3-1 0-1-1-2-1h0-1c0-1-2-2-2-3-2-2-3-5-3-7z" class="l"></path><path d="M382 519l3 1 5 2c2 2 5 3 7 5v1c2 2 5 4 6 7l1 1v1h0c2 2 3 4 4 6l1 1c0 5 1 9 1 13-2-1-2-9-3-12-2-7-11-13-17-16l-2-1-5-2c-1 0-2 0-3 1v1l-1-1c0-1 0-3-1-4 0 0 0-1-1-1 2-2 2-3 5-3z" class="D"></path><path d="M397 528c-3-1-8-3-9-5l2-1c2 2 5 3 7 5v1z" class="B"></path><path d="M417 592l-1-3h1c4 6 7 14 11 21 3 8 9 16 9 24l-1 1c0-1-1-2-1-4-1 2 0 2-1 3l-2-4c0-1-2-1-2-2-1 0-9-10-9-11-2-3-4-7-6-10l-3-9c2 1 2 2 3 4l1 2h1 0c-1-2-2-5-2-8h1l1-4z" class="D"></path><path d="M417 592c2 9 6 18 10 26-5-3-7-9-10-14-1-2-2-5-2-8h1l1-4z" class="d"></path><path d="M412 598c2 1 2 2 3 4l1 2h1 0c3 5 5 11 10 14 2 1 3 3 3 5v2c1 2 4 4 5 6-1 2 0 2-1 3l-2-4c0-1-2-1-2-2-1 0-9-10-9-11-2-3-4-7-6-10l-3-9z" class="K"></path><path d="M379 502v-1c0-1-1-2-2-3l1-1c2 0 6 5 9 6l12 13c0 1 1 2 1 2 5 4 8 12 11 17 5 8 13 17 21 21l1 2c6 3 12 6 18 7l9 3c-10 0-18-3-27-7l3 33-1-1-3-31c0-2-3-3-4-4-7-3-13-9-19-14l-1-1c-1-2-2-4-4-6h0v-1l-1-1c-1-3-4-5-6-7v-1c-2-2-5-3-7-5l-5-2-3-1-3-2h-1c-1 0-3 0-4-1s-1-1-1-2h-2v-2-3l2 2 1-1v-3c0-1 0-1 1-1h1c2 0 4 1 6 1l1-2-4-3z" class="F"></path><path d="M383 513l-2-3h1c2 0 4 2 6 1 1 0 2 1 3 2l-1 1h-3 0c0 2 4 3 6 5h0l4 4v1h0c-1-1-3-2-4-3l-9-6-1-2z" class="E"></path><path d="M382 515h2l9 6c1 1 3 2 4 3h0c3 3 7 6 9 10l-9-7c-2-2-5-3-7-5l-5-2-3-1-3-2h2 3l-2-2z" class="P"></path><path d="M383 505l3 3 2 3c-2 1-4-1-6-1h-1l2 3 1 2h-2l2 2h-3-2-1c-1 0-3 0-4-1s-1-1-1-2h-2v-2-3l2 2 1-1v-3c0-1 0-1 1-1h1c2 0 4 1 6 1l1-2z" class="j"></path><path d="M383 513c-2-1-4-2-5-3l1-1h2l1 1h-1l2 3z" class="C"></path><path d="M386 508l2 3c-2 1-4-1-6-1l-1-1h1 0c2 0 2 0 4-1z" class="T"></path><path d="M371 509l2 2 1-1v-3c0-1 0-1 1-1h1v2l-1 1c1 1 1 2 1 3l2 1c1 0 2 0 3 1 0 1 0 0 1 1l2 2h-3-2-1c-1 0-3 0-4-1s-1-1-1-2h-2v-2-3z" class="G"></path><path d="M378 513h0c0 1-1 2-2 2-1-1-2-1-3-2v-1h3l2 1z" class="H"></path><path d="M604 503l2 2c6 4 12 8 19 8 3-1 5-2 7-4 1 1 1 2 1 3l-1 4c-3 6-5 12-7 18l-9 24v-1c0-2 1-3 1-5h0c1-1 1-1 1-2l1-1-1-1h0l-1 2h-1v-1l-1 1h-1c-1 1-1 2-2 3-1 4-3 14-7 17l-24 68h0l-3 11c-1-1-1-1-1-2v-1c-1-2-1-6-1-9v-2-1l-1 8-1 2v-1l-2-2c-2-1-5-3-7-4h-2c-1 0-2 0-3 1-3-2-3-7-5-10l6-6c1 0 3-2 4-2 2-2 4-3 6-5l-1-1-2 1c-1-4-1-7-2-10-1-6-2-11-3-16l-3-6c0-1 0-3-1-4-1-2-1-3-2-5l1-1c0-1 0-1 2-2 0-1 1-2 2-3v1l2-1c-1-3 0-9 0-13 2 0 8 4 9 4 1-1 2-1 4-1 1 1 2 1 3 2l1 1 1-2 6 3c3-2 3-9 4-12l7-25 2 1 3-23z" class="W"></path><path d="M594 572l4 2-3 13c-1-2-1-3 0-5v-3-1c0-1 0-2 1-3l-1-1v3c-1 2-1 3-2 5 0-1 0-3 1-5v-5z" class="i"></path><path d="M593 571l1 1v5c-1 2-1 4-1 5l-5 20c0 3-1 6-2 8h-1c0-1 0-2 1-3v-3-1l1-1v-8c1-6 4-12 5-19h0c1-1 1-2 1-4z" class="c"></path><path d="M595 568l3-17 7-32c0 6 0 11-1 16 0 2-1 4-1 6l-1 2c0 1-1 3-1 5l1 1h-1l-2 15c-1 1-1 2-2 4v1 1h1v3c-1-2-2-3-3-5z" class="N"></path><path d="M604 503l2 2c1 5 0 9-1 14l-7 32-3 17-1-1 3-15 4-26 3-23z" class="U"></path><path d="M599 525l2 1-4 26-3 15-6-5c3-2 3-9 4-12l7-25z" class="W"></path><path d="M632 509c1 1 1 2 1 3l-1 4c-3 6-5 12-7 18l-9 24v-1c0-2 1-3 1-5h0c1-1 1-1 1-2l1-1-1-1h0l-1 2h-1v-1l-1 1h-1c-1 1-1 2-2 3-1 4-3 14-7 17l13-35 3-11c1-4 2-8 4-11 3-1 5-2 7-4z" class="g"></path><path d="M591 569l2 2c0 2 0 3-1 4h0c-1 7-4 13-5 19v8l-1 1v1 3c-1 1-1 2-1 3h1l-5 22c-1 1-1 3-1 5v1 2h0l1-2-3 11c-1-1-1-1-1-2v-1c-1-2-1-6-1-9v-2-1c-1-2 0-4 1-6l2-16c0-4 2-10 3-14 1-1 2-2 2-4l4-13c0-3 1-5 1-8l2-4z" class="N"></path><path d="M585 610h1l-5 22c-1 1-1 3-1 5v1 2h0l1-2-3 11c-1-1-1-1-1-2v-1c-1-2-1-6-1-9v-2-1c-1-2 0-4 1-6l2-16c1 3 0 12-1 14v8c-1 1-1 1-1 2l1-1v-2c0-2 2-3 2-5h1l2-9v-2c1-2 1-4 2-7h0z" class="U"></path><defs><linearGradient id="h" x1="595.809" y1="620.486" x2="558.792" y2="578.028" xlink:href="#B"><stop offset="0" stop-color="#b6b6b6"></stop><stop offset="1" stop-color="#eae9e9"></stop></linearGradient></defs><path fill="url(#h)" d="M564 555c2 0 8 4 9 4 1-1 2-1 4-1 1 1 2 1 3 2l1 1 1-2 6 3 6 5 1 1c1 2 2 3 3 5v1l-4-2-1-1-2-2-2 4c0 3-1 5-1 8l-4 13c0 2-1 3-2 4-1 4-3 10-3 14l-2 16c-1 2-2 4-1 6l-1 8-1 2v-1l-2-2c-2-1-5-3-7-4h-2c-1 0-2 0-3 1-3-2-3-7-5-10l6-6c1 0 3-2 4-2 2-2 4-3 6-5l-1-1-2 1c-1-4-1-7-2-10-1-6-2-11-3-16l-3-6c0-1 0-3-1-4-1-2-1-3-2-5l1-1c0-1 0-1 2-2 0-1 1-2 2-3v1l2-1c-1-3 0-9 0-13z"></path><path d="M580 560l1 1c3 3 7 5 10 8l-2 4c0-1 0-2 1-3v-1l-4-3c-2-1-4-1-7 0 1-2 1-4 1-6z" class="I"></path><path d="M582 559l6 3 6 5 1 1c1 2 2 3 3 5v1l-4-2-1-1-2-2c-3-3-7-5-10-8l1-2z" class="X"></path><defs><linearGradient id="i" x1="558.348" y1="576.85" x2="575.799" y2="557.514" xlink:href="#B"><stop offset="0" stop-color="#cbc8c9"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#i)" d="M564 555c2 0 8 4 9 4 1-1 2-1 4-1 1 1 2 1 3 2 0 2 0 4-1 6 0 1 0 1-1 2s-1 1-2 1c-2 2-3 4-5 5-1-1-2-1-2-2v-2h-1c-2 3-2 4-2 8h1-1c-2 3-1 4-1 7-3-3-5-5-6-9 0-1 1-1 1-1v-2-1l1-1s1-1 1-2h0l2-1c-1-3 0-9 0-13z"></path><path d="M557 574l1-1c0-1 0-1 2-2 0-1 1-2 2-3v1h0c0 1-1 2-1 2l-1 1v1 2s-1 0-1 1c1 4 3 6 6 9 1 1 4 3 4 4-1 0-1 1-2 1 1 5 4 10 3 15-1-1 0-2-2-3 1 2 1 4 2 7 0 1-1 2 0 4 0-1 1-1 1-1 0-1 0-2 1-2h1v-1l1 4v3c0 2 0 3-1 4v3c0 1 0 1-1 1v5 7c0 2 1 4 3 6l-1 2v-1l-2-2c-2-1-5-3-7-4h-2c-1 0-2 0-3 1-3-2-3-7-5-10l6-6c1 0 3-2 4-2 2-2 4-3 6-5l-1-1-2 1c-1-4-1-7-2-10-1-6-2-11-3-16l-3-6c0-1 0-3-1-4-1-2-1-3-2-5z" class="N"></path><path d="M565 620h0v15l4 3c0-5 0-10 2-14h0c0 4 0 8 1 12 0 2 1 4 3 6l-1 2v-1l-2-2c-2-1-5-3-7-4h-2c-1 0-2 0-3 1-3-2-3-7-5-10l6-6c1 0 3-2 4-2z" class="U"></path><defs><linearGradient id="j" x1="556.33" y1="630.276" x2="561.799" y2="630.61" xlink:href="#B"><stop offset="0" stop-color="#726f73"></stop><stop offset="1" stop-color="#858485"></stop></linearGradient></defs><path fill="url(#j)" d="M561 622c1 3 1 5 1 7s1 4 1 6v2c-1 0-2 0-3 1-3-2-3-7-5-10l6-6z"></path><path d="M500 493h1v3c1 2 1 3 3 4s6 2 9 2c4 1 8 0 12 3h0l1 2c4 5 4 10 6 15 0 1 1 3 1 4l2 1c-3 1-6 1-10 1 1 1 3 1 5 1l1 1v1h-1v1h3c2-1 3-1 4-1h1l6-1 1 1c-1 2-1 3 0 5 0 1 0 3 1 4l4 18 1 1c1 4 2 9 5 13 0 1 1 1 1 2 1 2 1 3 2 5 1 1 1 3 1 4l3 6 3 16c1 3 1 6 2 10l2-1 1 1c-2 2-4 3-6 5-1 0-3 2-4 2l-6 6h-2c-1 0-2-3-3-3v1c0 1 1 2 1 3 1 0 1 0 2 1l2 2v1h-2l-5-8c-1 0 0 0-1-1-2-3-3-6-6-7h0c-1-2-2-5-3-7l-6-12h0c-3-4-5-9-7-13l-10-23-3-6c-1-2-3-4-5-6-7-12-12-25-18-37l1-1v-1s-1-2-1-3l1-1h1c3 0 6 0 9-1h1v3c1 1 1 0 1 1 1-1 2-1 2-1l1-1c1-1 2-2 2-3 1 0 2-1 3-1-1-1-2-2-3-2-3-1-6-2-8-4 1-2 1-3 1-5z" class="E"></path><path d="M520 563l3 6c1 1 1 2 2 3h1l3 7h-1 0v-1h-1l-1-3-1-1c-1 0-1-1-1-1l-1-1h0c-1-2-1-3-2-4-1-2-1-3-1-5z" class="B"></path><path d="M544 605l-1 1c-3-2-4-5-5-8h-1c0-2-1-3-1-5 2 2 3 4 5 5l1 1v1c2 0 3 1 5 1 0 1-1 2-2 3 0 1 0 1-1 1z" class="T"></path><path d="M541 598l1 1v1c0 1 0 1-1 2h-1v-2l1-2z" class="M"></path><path d="M532 598h0c3 3 5 6 7 10l2 2c1 2 1 4 2 6 2 3 5 6 5 9-1 0 0 0-1-1-2-3-3-6-6-7h0c-1-2-2-5-3-7l-6-12z" class="O"></path><path d="M511 551l-2-2c1-2 0-4 0-6-1 0-1-1-1-2v-1h-1l1-1c-1-2-1-4-2-6h-1l1-2h3l-1-1h-1 1c1 0 2 0 2 1h1 3 4 2 3 3c1 1 2 0 4 0v1h-3c-5 0-9 1-13 1-1-1-2 0-2 0-1 0-1 1-1 2l-1 1v1l1 9v5zm-11-45h1v3c1 1 1 0 1 1-1 3-1 6-1 9-2-2-1-3-2-5h-4v2h-1l-4-4v-1s-1-2-1-3l1-1h1c3 0 6 0 9-1z" class="F"></path><path d="M511 546l-1-9v-1l1-1c0-1 0-2 1-2 0 0 1-1 2 0 4 0 8-1 13-1h0c-3 1-7 1-9 2-1 1-3 3-4 5v6h2l1-1h1l1 1v4h1v2c-1 1-2 2-3 2l-1 1h-2l-3-3v-5z" class="T"></path><path d="M511 546c2 3 3 5 3 8l-3-3v-5z" class="B"></path><path d="M514 545h2l1-1h1l1 1v4h1v2c-1 1-2 2-3 2l-1-1c-1 0-1-1-2-2v-3c-1-1 0-1 0-2z" class="K"></path><defs><linearGradient id="k" x1="525.473" y1="531.232" x2="518.283" y2="543.542" xlink:href="#B"><stop offset="0" stop-color="#a09d9f"></stop><stop offset="1" stop-color="#c6c6c7"></stop></linearGradient></defs><path fill="url(#k)" d="M518 534c2-1 6-1 9-2 1 0 3 0 4 1v4s-1 1-1 2c-1 2-3 4-5 5l-5 5h-1v-4l-1-1h-1l-1 1h-2v-6c1-2 3-4 4-5z"></path><path d="M514 545v-6c1-2 3-4 4-5v1c0 1-1 2-2 2l2 1c-1 0-1 0-2 1v4l1 1-1 1h-2z" class="c"></path><path d="M518 544c2-3 9-4 12-5-1 2-3 4-5 5l-5 5h-1v-4l-1-1z" class="G"></path><path d="M500 493h1v3c1 2 1 3 3 4s6 2 9 2c4 1 8 0 12 3h0l1 2c4 5 4 10 6 15 0 1 1 3 1 4l2 1c-3 1-6 1-10 1h-1l-2-1h-6-1c-3 0-7-1-10-1-2-3-3-4-4-7h0c0-3 0-6 1-9 1-1 2-1 2-1l1-1c1-1 2-2 2-3 1 0 2-1 3-1-1-1-2-2-3-2-3-1-6-2-8-4 1-2 1-3 1-5z" class="N"></path><path d="M512 505c3-1 5-1 7 0l1 4h-3c-2-1-2-3-5-4z" class="f"></path><path d="M502 510c1-1 2-1 2-1-1 2-2 3-1 6v3h1c0 3 2 6 5 7 2 1 4 1 7 2h-1c-3 0-7-1-10-1-2-3-3-4-4-7h0c0-3 0-6 1-9z" class="S"></path><path d="M519 505c2 1 5 2 6 3 1 3 2 5 1 8l-1-1c-3 0-4-2-6-4l-2-2h3l-1-4z" class="AI"></path><path d="M517 509h3c2 0 3 1 5 3 1 1 0 1 0 2v1c-3 0-4-2-6-4l-2-2z" class="P"></path><path d="M519 511c2 2 3 4 6 4l1 1h-2c2 4 3 7 4 11-1 1-2 1-4 1l-2-1 1-1c-1-3-2-5-3-8l1-1v-2c-1-1-1-2-2-4h0z" class="c"></path><path d="M500 493h1v3c1 2 1 3 3 4s6 2 9 2c4 1 8 0 12 3h0l1 2-1 1c-1-1-4-2-6-3s-4-1-7 0c-2 0-5 2-7 3 1-1 2-2 2-3 1 0 2-1 3-1-1-1-2-2-3-2-3-1-6-2-8-4 1-2 1-3 1-5z" class="l"></path><path d="M526 507c4 5 4 10 6 15 0 1 1 3 1 4l2 1c-3 1-6 1-10 1h-1c2 0 3 0 4-1-1-4-2-7-4-11h2c1-3 0-5-1-8l1-1z" class="q"></path><path d="M530 539c0-1 1-2 1-2 1 1 1 2 1 4 1 3 2 7 3 10 1 2 1 4 2 5l-1 4 1 1c2 12 9 24 10 36l-1 1v-1c-1 1-1 1-2 1h-1c-1 0-1 0-1 1l-1-1c-2-1-3-3-5-5 0-1-6-13-7-14l-3-7h-1c-1-1-1-2-2-3l-3-6c-1-2-4-4-4-7 2-2 2-2 4-2v-3-2l5-5c2-1 4-3 5-5z" class="N"></path><path d="M530 539c0-1 1-2 1-2 1 1 1 2 1 4 1 3 2 7 3 10 1 2 1 4 2 5l-1 4c-1-5-3-8-4-12v-1c0-1 0-1-1-2 0-1-1-1-1-2h-1l-4 1c2-1 4-3 5-5z" class="U"></path><path d="M520 563c-1-2-4-4-4-7 2-2 2-2 4-2 1 2 2 7 4 9 2 1 4 1 5 2v1c0 1-3 2-3 3v3h-1c-1-1-1-2-2-3l-3-6z" class="D"></path><path d="M538 531l6-1 1 1c-1 2-1 3 0 5 0 1 0 3 1 4l4 18 1 1c1 4 2 9 5 13 0 1 1 1 1 2 1 2 1 3 2 5 1 1 1 3 1 4l3 6 3 16c1 3 1 6 2 10-2 1-4 2-7 3-2 0-4 1-6 0-2 0-5-3-6-4-2-2-5-6-5-9 1 0 1 0 1-1 1-1 2-2 2-3-2 0-3-1-5-1v-1c0-1 0-1 1-1h1c1 0 1 0 2-1v1l1-1c-1-12-8-24-10-36l-1-1 1-4c-1-1-1-3-2-5-1-3-2-7-3-10 0-2 0-3-1-4v-4c-1-1-3-1-4-1h0 3 3c2-1 3-1 4-1h1z" class="AB"></path><path d="M548 587c2 3 6 10 5 14v1l-1 2-2-10c-1-3-2-5-2-7z" class="v"></path><path d="M544 564h1c3 5 4 14 5 20h-1c-1-2-1-4-2-6-1-5-3-10-3-14z" class="Y"></path><path d="M533 532c2-1 3-1 4-1 1 2 1 4 2 6l1 4-1 3c1 3 3 6 3 9 0 1 0 2 1 3v1h-1c-1-7-4-13-6-19-1-2-1-4-3-6z" class="q"></path><path d="M539 537l1 4-1 3v-2c-1-2-1-3 0-5z" class="z"></path><path d="M538 531l6-1 1 1c-1 2-1 3 0 5 0 1 0 3 1 4l4 18 1 1s-1 1-1 2v1c-1-1-2-3-2-4v3c1 1 1 3 1 4l-3-9-6-15-1-4c-1-2-1-4-2-6h1z" class="e"></path><defs><linearGradient id="l" x1="548.121" y1="587.107" x2="538.59" y2="590.902" xlink:href="#B"><stop offset="0" stop-color="#a09e9f"></stop><stop offset="1" stop-color="#c2c0c1"></stop></linearGradient></defs><path fill="url(#l)" d="M537 556l11 31c0 2 1 4 2 7l2 10c1 2 2 5 2 7s0 6 1 7c-2 0-5-3-6-4-2-2-5-6-5-9 1 0 1 0 1-1 1-1 2-2 2-3-2 0-3-1-5-1v-1c0-1 0-1 1-1h1c1 0 1 0 2-1v1l1-1c-1-12-8-24-10-36l-1-1 1-4z"></path><defs><linearGradient id="m" x1="572.104" y1="591.205" x2="541.204" y2="583.609" xlink:href="#B"><stop offset="0" stop-color="#c4c2c3"></stop><stop offset="1" stop-color="#eee"></stop></linearGradient></defs><path fill="url(#m)" d="M549 565c0-1 0-3-1-4v-3c0 1 1 3 2 4v-1c0-1 1-2 1-2 1 4 2 9 5 13 0 1 1 1 1 2 1 2 1 3 2 5 1 1 1 3 1 4l3 6 3 16c1 3 1 6 2 10-2 1-4 2-7 3 0-3 0-6-1-9l-11-44z"></path><path d="M516 106a104.13 104.13 0 0 1 12 5c4 1 7 3 11 5 5 2 9 4 13 7l1 1c2 4 2 8 2 12l3 18c1 3 4 5 6 8l2 1c0 1-1 3-1 4l1 4 3 2 2 1h1 1 1c2-1 4 0 6-1h1l1-2 1 1c1-1 1 0 1-1s0-1 1-1v2l1-1h2 0c-1 1-2 1-2 2h-1c-2 0-2 1-3 1h-1c-2 0-3 0-5 1h-5c4 7 10 12 14 18 2 2 4 4 5 6 1 1 1 1 1 2 2 2 4 5 6 6 2 2 6 2 9 3 4 1 8 2 13 3 2 1 3 1 4 3v1h26 13 1c4 1 12 2 15 0l1-1v1c-1 0 0 0 0 1-1 0-1 0-2 1-1 0-3-1-4 0h-8 0c3 1 7 1 11 1 1 0 1 1 2 2 2 1 5 1 7 1-2 0-4 1-6 1l-29 2h-12-4-1l-1 1-8-1c2 4 5 7 7 11l-1 3c2 1 5 0 7 1 2 0 3 0 5 1l-2 1h1l-1 1c5 1 10 2 15 4 4 2 7 5 10 8 4 4 8 8 11 13l3 1c0-1-1-2-1-4 1 1 2 1 3 2l1 2c1-1 1-2 2-3 1 1 2 3 2 3 2 1 1 1 3 2 0 1 1 0 2 2l2 4v2c1 0 1 1 2 1l-1 1c1 1 1 2 1 3v1h-1c1 1 1 3 1 4 1 0 1 1 1 2l1 1 2 7c0 1 1 2 1 3 1 11 0 21-1 31-1 6-3 12-6 17 0 1-1 2-1 3h0l-1 1-1 2-1 1-4 4c0-2 1-6-1-9l-1 1h0l1-3-1-1-2 2-7 5-8 9-1-1c-3 1-5 3-7 5-1 1-3 2-4 3-2 1-3 2-4 4-2 1-3 2-4 3-2 4-6 7-9 9-1 2-3 4-5 6l-1 3c-4 9-7 20-9 30l-4 12-3 17c2 6-1 15-3 21h0v-1l-2 1-1 1c-1 2-1 3-2 5l-1 9-1 7 3 1c4 1 7 2 12 1l4-1h0c4 2 9-1 13-3l7-1c-1 1-7 7-7 8l1 2-2 3h0c-1 0-2 2-3 2 0-1 0-2-1-3-2 2-4 3-7 4-7 0-13-4-19-8l-2-2-3 23-2-1-7 25c-1 3-1 10-4 12l-6-3-1 2-1-1c-1-1-2-1-3-2-2 0-3 0-4 1-1 0-7-4-9-4 0 4-1 10 0 13l-2 1v-1c-1 1-2 2-2 3-2 1-2 1-2 2l-1 1c0-1-1-1-1-2-3-4-4-9-5-13l-1-1-4-18c-1-1-1-3-1-4-1-2-1-3 0-5l-1-1-6 1h-1c-1 0-2 0-4 1h-3v-1h1v-1l-1-1c-2 0-4 0-5-1 4 0 7 0 10-1l-2-1c0-1-1-3-1-4-2-5-2-10-6-15l-1-2h0c-4-3-8-2-12-3-3 0-7-1-9-2s-2-2-3-4c0-3 1-5 2-7 1-1 1-1 1-3h-3-6-5l-1-2v-1c-1 0-2 0-3-1h-1l-1-1-1-1-1-1c-3-6-6-11-8-18h-1c-1-2-2-3-2-5 0-1 0-1-1-1-5-9-10-18-14-27l-4-7-3-6-2-3c0-1-1-2-2-4-1-3-2-5-3-8-1-4-4-8-4-12h2l3 3v1l1-1-4-4-5-7c2 0 3 1 4 3 0-2-1-3-2-4l1-1c1 0 2 0 3-1-6-9-12-19-22-24-4-3-5-9-7-13-1-3-3-7-5-10 1-1 1-2 2-3l-1-3 9-13-1-2-1-1c1-2 4-5 5-8l2-3h0l1-1c3 2 7 4 9 6l12 9c1 1 3 4 5 5 0-1 1-1 1-2 1-3 3-6 3-8 4-11 6-22 9-32l4-18 5-46c0-4 0-8 1-13h0v-3c-2 0-2 0-3-1l1-8c-2-2-3-5-3-7-1-1-1-3-1-4h2c2 0 3-1 4-2 1-3 3-7 3-10l2-2s0-7 1-8c1 0 2-1 3-2h0c1-2 0-7 0-9l1-2c2-2 4-2 6-2-1 0-1-1-2-1l2-2 1 1c2 0 3 0 4 1v1h1c1 0 1 1 2 1h2c2 1 3 1 4 1l3 3c1 1 2 2 2 3 1 0 1-1 2-1 1 1 1 2 1 2 1 1 3 1 3 2 1 1 1 2 1 3v5l-1 1v1c-1 0-2 0-2 1v2l1-1h0c1-1 2-1 4-1v-1c-1-2-1-4-1-6v-5-8c0-2 1-4 0-7l1-3h-1v-1-7c1-1 1-4 1-5l-1-3v-7h1z" class="V"></path><path d="M512 389c4-1 7-1 11-1-1 0-2 1-2 1l-2 2c-2 0-4-1-7-2z" class="I"></path><path d="M545 238l3-1-2 9h-1l-1 1 1-9z" class="i"></path><path d="M546 231c1 0 2-2 2-2v8l-3 1 1-7z" class="U"></path><path d="M560 306c1-1 2 0 3 0 0 2 0 2-1 4 0 1 0 2-1 2l-1-1v-3l-1-1-1 2h-1c1-2 1-2 3-3zm-48 87h1c1 0 2 1 2 2 1 1 0 2 0 4h-2c-1 0-1 0-2-1s-1-1-1-2c0-2 0-2 2-3z" class="N"></path><path d="M472 265l1 1h2s1 1 0 2c0 1 0 2 1 3l-2 15v8c-1-2-1-7 0-10h0v-10c0-3-1-6-2-9z" class="W"></path><path d="M572 319h2c0 1 0 2 1 3v1 1c0 2-1 2-2 3-2 0-2-1-3-1 0-2-1-3-1-4 1-2 2-2 3-3zm-92-68l2 1 4 19h0-1c-1-1-1-2-1-3-2-6-4-11-4-17z" class="e"></path><path d="M535 419c3 1 7 2 8 5 1 1 1 2 0 3s-2 1-3 1c-1 2-2 4-2 7-1-4 3-9 2-13h-1l-1 2c-2-2-5-3-8-4 2 0 4 0 5-1z" class="U"></path><path d="M501 396c1-1 1-2 2-3 1 4 1 8 4 11l6 4h-4v1l1 2-2-1c-4-5-6-8-7-14z" class="c"></path><path d="M555 204v4c1 3 2 6 2 9l-1 5c-1 4 0 11-3 15l1-4-1-18h-1c-1 0-1 0-1-1 1 0 1-1 2-1h1c0-3-1-6 1-9z" class="f"></path><path d="M552 215c-1 0-1 0-1-1 1 0 1-1 2-1h1c0 6 1 14 0 20l-1-18h-1z" class="N"></path><path d="M468 250c2 2 2 4 2 6l1-1v2c0 1 0 2 1 3v5c1 3 2 6 2 9l-3 22c-1-5 3-12 1-16-3-5-2-13-2-19-1-2 0-4-1-6l-1 1c-1-2 0-4 0-5v-1z" class="I"></path><path d="M510 411l-1-2v-1h4l22 11c-1 1-3 1-5 1-4-1-8-2-12-4-3-1-5-2-7-4l-1-1z" class="v"></path><path d="M512 410l6 3c2 0 2 1 3 2-1 0-3 0-4-1-2-1-4-2-5-4z" class="Y"></path><defs><linearGradient id="n" x1="562.742" y1="373.806" x2="537.01" y2="395.632" xlink:href="#B"><stop offset="0" stop-color="#7f7f7f"></stop><stop offset="1" stop-color="#bdb9ba"></stop></linearGradient></defs><path fill="url(#n)" d="M553 359l-1 3 3 2c-3 6-6 12-5 19 0 3-1 6 0 9l-1 12c0 2-1 4 0 5v1h-1v-1c-1-1-1-3-1-4v-19c-1-10-1-20 6-27z"></path><defs><linearGradient id="o" x1="474.334" y1="243.493" x2="474.611" y2="276.033" xlink:href="#B"><stop offset="0" stop-color="#b8b6b6"></stop><stop offset="1" stop-color="#d8d8d8"></stop></linearGradient></defs><path fill="url(#o)" d="M474 241c2 10 3 21 6 31v6h0l-1-1c0 1 0 1 1 3v1c0 2 3 7 2 8l-3-9c-1-1-1-3-1-4l-1-1v-2l-1-2v-1l-1-2c1-1 0-2 0-2h-2l-1-1v-5c-1-1-1-2-1-3v-2l-1 1c0-2 0-4-2-6l1-1h2c0 1 0 1 1 1 2-3 0-6 2-9z"></path><path d="M576 221c3 3 5 7 7 11l12 18c-4-3-6-5-10-6 3 4 6 7 10 11h0c-3-2-8-6-10-9 0-2-2-3-3-4-1-2-2-3-3-5-1 0-1-1-1-1-1-1-1-2-2-3l-1-1c0-3-1-8 1-11z" class="e"></path><path d="M557 217c1 2 1 4 2 6 1 10 2 20 4 30 1 2 2 4 3 7 1 2 2 4 2 6l-1 1c-3-6-4-14-6-20l-1-2h0c-1-1-1-2-1-3v-1c-1-1-1-3-1-4v-3-1c-1 2-1 4-1 5 0 4-1 7-1 10-1-2-1-4 0-6-1 1-1 3-1 5 0 0 0 1-1 1-1 2-1 6-1 8l-2 14-1 1v-1c1-5 2-32 3-33 3-4 2-11 3-15l1-5z" class="i"></path><defs><linearGradient id="p" x1="460.403" y1="216.508" x2="483.097" y2="235.992" xlink:href="#B"><stop offset="0" stop-color="#858185"></stop><stop offset="1" stop-color="#b7b6b3"></stop></linearGradient></defs><path fill="url(#p)" d="M472 197h0v5l1-2 1 2h1c1 1 1 2 1 3h-1-1l-1 26 1 10c-2 3 0 6-2 9-1 0-1 0-1-1h-2l-1 1v1c0 1-1 3 0 5-1 2-1 5-1 7l-1-3v-3-1l5-46c0-4 0-8 1-13z"></path><path d="M468 251c0-2 0-5 1-6s0 0 2-1v5h-2l-1 1v1zm6-49h1c1 1 1 2 1 3h-1-1l-1 26v-9l1-20z" class="N"></path><path d="M590 281c-7-8-15-17-19-27-1-2-2-5-1-7 5 1 6 7 9 11l1-1-5-7c-2-3-2-7-3-10 0-2-1-4-1-5 0-6 1-11 5-15v1c-2 3-1 8-1 11 0 1 1 3 1 4v1c0 4 3 9 2 12 1 5 7 9 11 13l3 3c2 1 5 2 6 3-5-1-12-4-15-8v1 1c-1-1-1-1-2-1 1 4 5 8 8 12h-1l-4-5-2-2c-1-1-1-3-2-4 0-1-1-2-1-2l-3-4h-1c-1-1-3-3-4-5 0-1 0-1 1-2l-1-1-1 1c1 3 3 7 5 11l4 6c1 2 2 3 3 4s1 1 1 2l3 3c0 1 1 1 1 2 1 0 1 1 2 1l2-2c0-1 1-2 2-2-1 1-2 3-3 4v3z" class="W"></path><path d="M575 342l-1 7h0 1l2-3c-1 3-2 7-5 9 1-2 1-4 1-7-1 1-2 2-2 3l-1 5c-5 2-7 5-9 9s-5 7-6 11c-2 4-2 7-3 11-1-1-1-1-1-2l1-1v-2h0v-3l-1 3c-1 3-1 7-1 10-1-3 0-6 0-9-1-7 2-13 5-19l-3-2 1-3c1-3 8-6 10-8 3-2 5-4 7-6 2-1 4-2 5-3z" class="N"></path><path d="M555 364c2-3 4-5 6-8-1 2-2 4-2 6v1c-4 7-7 13-9 20-1-7 2-13 5-19z" class="U"></path><defs><linearGradient id="q" x1="556.36" y1="350.269" x2="569.059" y2="355.764" xlink:href="#B"><stop offset="0" stop-color="#7c797b"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#q)" d="M575 342l-1 7h0 1l2-3c-1 3-2 7-5 9 1-2 1-4 1-7-1 1-2 2-2 3-2 1-3 2-4 4-4 2-6 4-8 8v-1c0-2 1-4 2-6-2 3-4 5-6 8l-3-2 1-3c1-3 8-6 10-8 3-2 5-4 7-6 2-1 4-2 5-3z"></path><path d="M570 356l-1 5c-2 8-3 16-6 24l-3 9c-1 2-3 4-4 6v-1c-2 5-5 10-6 16l-2 2v-2l8-18h-1s0 1-1 1v-1l-5 12c-1-1 0-3 0-5l1-12c0-3 0-7 1-10l1-3v3h0v2l-1 1c0 1 0 1 1 2 1-4 1-7 3-11 1-4 4-7 6-11s4-7 9-9z" class="I"></path><defs><linearGradient id="r" x1="555.96" y1="193.897" x2="571.54" y2="210.603" xlink:href="#B"><stop offset="0" stop-color="#c2bfc0"></stop><stop offset="1" stop-color="#eeeeed"></stop></linearGradient></defs><path fill="url(#r)" d="M565 167l1 4c0 5 1 11 2 16 1 2 3 6 2 8v1l2 6h-1c-1-1-1-1-1-2l-1-4h-1v5c1 4 2 9 2 12v1c-1-1-1-2-1-2v-3l-2-8c0 2-1 6 0 7l1 2c1 1 1 5 1 6v1 1h-1v-1c-1-2-1-5-2-7 0-1 0-3-1-4-1 5-2 9-2 15v1c-1 0-1 1-1 2l-1-8c0-1-1-2-1-2l-1 3v6c-1-2-1-4-2-6 0-3-1-6-2-9v-4h0c0-1 0-4-1-6v-6h1l-1-2 5-5c2-2 5-7 5-10 0-2 0-4 1-5v-3z"></path><path d="M557 210c1-1 1 0 1-1v-1l1-1 2 1-1 3h-1-1l-1-1z" class="I"></path><path d="M556 198h2c1 1 1 2 1 3l1 1v-1l1-1v7c-1 1-1 1 0 1l-2-1-1 1v1c0 1 0 0-1 1 1-4 0-8-1-12z" class="N"></path><path d="M561 196v-2c0-4 1-11 4-14l1 2c-2 3-1 8-1 10v4h-1c0-1 0-1-1-2l-2 2z" class="c"></path><path d="M563 194c0-1-1-3 1-4 0 1 1 1 1 2v4h-1c0-1 0-1-1-2z" class="N"></path><defs><linearGradient id="s" x1="551.858" y1="198.392" x2="559.683" y2="201.902" xlink:href="#B"><stop offset="0" stop-color="#959195"></stop><stop offset="1" stop-color="#aeafab"></stop></linearGradient></defs><path fill="url(#s)" d="M554 190l5-5c0 2-1 2-1 4-1 0 0 2 0 3h-1c-1 2-1 4-1 6 1 4 2 8 1 12l1 1 1 6v6c-1-2-1-4-2-6 0-3-1-6-2-9v-4h0c0-1 0-4-1-6v-6h1l-1-2z"></path><path d="M565 167l1 4c0 5 1 11 2 16 1 2 3 6 2 8v1l-4-14-1-2c-3 3-4 10-4 14v2 3 1l-1 1v1l-1-1c0-1 0-2-1-3h-2c0-2 0-4 1-6h1c0-1-1-3 0-3 0-2 1-2 1-4 2-2 5-7 5-10 0-2 0-4 1-5v-3z" class="f"></path><path d="M558 192v4l1-1v-4l1 1c0 2 0 4 1 7v1l-1 1v1l-1-1c0-1 0-2-1-3h-2c0-2 0-4 1-6h1z" class="U"></path><path d="M511 412c2 2 4 3 7 4 4 2 8 3 12 4 3 1 6 2 8 4l1-2h1c1 4-3 9-2 13-1 4-5 10-8 14h1l-4 3c-1 0-1 0-1-1-2-8-6-16-10-24h0l1-1 1 2h1l-1-1v-2c-3-4-5-8-7-13z" class="I"></path><defs><linearGradient id="t" x1="513.196" y1="418.301" x2="534.854" y2="427.966" xlink:href="#B"><stop offset="0" stop-color="#acaaab"></stop><stop offset="1" stop-color="#cccbcb"></stop></linearGradient></defs><path fill="url(#t)" d="M511 412c2 2 4 3 7 4 4 2 8 3 12 4 3 1 6 2 8 4l-1 1c-2 2-1 6-3 8-1 2-1 5-2 7v2-1h0c1-5 0-8-2-12l-11-7 1 6h0-1l-1-1v-2c-3-4-5-8-7-13z"></path><path d="M506 388l6-6c5-5 12-8 19-11 1-1 4-2 6-2 3 7 2 16 3 24l2 14c0 3 1 5 1 8-7-8-8-25-20-27-4 0-7 0-11 1-3 1-7 2-9 4-1 1-1 2-2 3l-1-2c1-2 2-3 3-5v1l1 1 2-3z" class="i"></path><path d="M506 388l6-6c5-5 12-8 19-11 1-1 4-2 6-2-1 3-2 4-4 5-3 1-5 2-8 3-6 3-11 7-17 10 0 1-1 1-2 1z" class="f"></path><defs><linearGradient id="u" x1="569.785" y1="316.334" x2="602.743" y2="312.146" xlink:href="#B"><stop offset="0" stop-color="#7a787b"></stop><stop offset="1" stop-color="#a5a3a3"></stop></linearGradient></defs><path fill="url(#u)" d="M608 248l10 8c0 1 0 2 1 3v2l-3 3-12 9-7 6c1 1 1 2 3 2v1c0 1 1 3 2 4 0 1 1 1 1 1 1 1 2 1 2 2-1 2 0 4-1 6 0 1-1 3-2 4-2 4-4 7-6 10-4 7-7 14-10 22l-9 15-2 3h-1 0l1-7c5-9 6-18 9-27v-1c2-3 7-6 9-9 2-2 3-5 4-8 0-6-4-11-7-16v-3c1-1 2-3 3-4 7-3 13-8 18-13 0 2-1 4-1 6l3-3 2-2c0-3-1-3-2-6 1 0 2 2 4 3v-1l-1-1c-1-2-3-4-5-6-1 0-2-1-3-2v-1z"></path><path d="M602 299h0c0-6-3-12-6-16-1-1-1-1-1-3l2-1c1 1 1 2 3 2v1c0 1 1 3 2 4 0 1 1 1 1 1 1 1 2 1 2 2-1 2 0 4-1 6 0 1-1 3-2 4z" class="N"></path><path d="M611 261c0 2-1 4-1 6h-1c-2 1-3 2-5 4-3 2-9 5-11 9-2 0-2-1-3-2 1-1 2-3 3-4 7-3 13-8 18-13z" class="Y"></path><path d="M466 256v1 3l1 3c-1 3-1 6-1 8 0 3 2 6 3 9 0 1 1 3 1 4s-2 3-2 4c-2 4-1 10 0 14v1c1 4 4 8 6 12 4 6 8 12 14 17 3 4 7 6 11 10 4 5 9 12 12 19 0 0 3 4 3 5s-2 4-3 6l-1-2c-3-7-10-12-14-18-3-4-4-7-7-10-1-2-3-3-4-5-2-2-3-5-6-7l-3-6c-3-5-6-9-7-14l-3-10h0c-1-1-1-1-1-2v-1-3-1c0 3-1 9 0 11v1c0 5 1 10 1 15 0 1 0 3-1 4l-2-22-1-1v-18l-8 23c-1 2-2 6-4 8 1-3 3-6 3-8 4-11 6-22 9-32l4-18z" class="U"></path><path d="M463 302v4l1-1v-8c-1-6 0-12 0-17l1-1 1 21h0 0c-1-1-1-1-1-2v-1-3-1c0 3-1 9 0 11v1c0 5 1 10 1 15 0 1 0 3-1 4l-2-22z" class="N"></path><path d="M466 300h0l3 10c1 5 4 9 7 14l3 6c3 2 4 5 6 7 1 2 3 3 4 5 3 3 4 6 7 10 4 6 11 11 14 18l1 2-7 15-1 3v-1c-1 2-2 3-3 5l-2-6-2-6-7-16c0-2-1-4-1-7-1-1-2-5-2-6l-6-17-1-1-1-1v-1l-1-2c-1-5-5-9-7-14l-1-3-2-4c-1-2 0-4 0-5l-1-1 1-1c-1-1-1-1-1-3z" class="I"></path><path d="M498 382c0 2 1 4 3 6h0c1-2 0 0 0-2l3-6 1 1-1 3v3l-1 3v-1c-1 2-2 3-3 5l-2-6-2-6h1v-1l1 1z" class="W"></path><path d="M498 388h1l1 1h1l3-5v3l-1 3v-1c-1 2-2 3-3 5l-2-6z" class="N"></path><path d="M488 359l5 8c2 1 4 3 5 5h0c1 1 1 1 1 2v1 4h0c-1-2 0-4-2-7h0c-1-2-3-3-4-4 2 5 4 9 5 14h0l-1-1v1h-1l-7-16c0-2-1-4-1-7z" class="e"></path><path d="M569 173l2 1h1 1 1c2-1 4 0 6-1h1l1-2 1 1c1-1 1 0 1-1s0-1 1-1v2l1-1h2 0c-1 1-2 1-2 2h-1c-2 0-2 1-3 1h-1c-2 0-3 0-5 1h-5c4 7 10 12 14 18 2 2 4 4 5 6h0c-2-1-2-1-3-2s-1-2-2-2l-7-9c1 4 4 7 6 11 1 3 3 6 5 9l17 34 3 3c4 6 8 10 14 14 1 0 1 1 2 1-2 3-6 5-8 8l-2 2h0l-5 5c1 2 1 3 1 4h-1v-3h-1a30.44 30.44 0 0 0-8 8c0 1 0 2 1 3 0 1 0 1 1 1h1v1h-1s-1 0-1-1c-1-1-2-3-2-4v-1c-2 0-2-1-3-2l7-6 12-9 3-3v-2c-1-1-1-2-1-3l-10-8v1c1 1 2 2 3 2 2 2 4 4 5 6l1 1v1c-2-1-3-3-4-3 1 3 2 3 2 6l-2 2-3 3c0-2 1-4 1-6l1-1c2-4-12-13-14-16-12-11-20-27-26-42l-2-6v-1c1-2-1-6-2-8-1-5-2-11-2-16l3 2z" class="I"></path><path d="M566 171l3 2h0-2c0 1 0 1 1 2v7c0 3 2 8 3 10s0 3 1 5c2 4 3 8 5 12 3 6 6 11 10 16s8 11 12 15c2 1 2 2 3 3 2 1 5 4 6 5v1c1 1 2 2 3 2 2 2 4 4 5 6l1 1v1c-2-1-3-3-4-3 1 3 2 3 2 6l-2 2-3 3c0-2 1-4 1-6l1-1c2-4-12-13-14-16-12-11-20-27-26-42l-2-6v-1c1-2-1-6-2-8-1-5-2-11-2-16z" class="c"></path><path d="M465 305v-1c-1-2 0-8 0-11v1 3 1c0 1 0 1 1 2 0 2 0 2 1 3l-1 1 1 1c0 1-1 3 0 5l2 4 1 3c2 5 6 9 7 14l1 2v1l1 1 1 1 6 17c0 1 1 5 2 6 0 3 1 5 1 7l7 16 2 6 2 6 1 2c1 6 3 9 7 14l2 1 1 1c2 5 4 9 7 13v2l1 1h-1l-1-2-1 1h0c4 8 8 16 10 24 0 1 0 1 1 1 0 2 1 3 1 5 0 4 2 7 3 10 1 2 1 4 2 5 2 1 1 1 2 2l2 8s1 1 1 2l-1 1 1 3v2c1-1 0-1 1-1 0 2 0 5 2 7l1-1c1-1 2-1 3-1-1 1-2 3-3 4 0 1 0 3-1 5l1 1c-1 2 0 10 0 12h0c1 2 1 6 1 8 0 1-1 1-2 1 0-1-1-2-1-3h-1c0-2 0-4-1-5v-4c0-1-1-2-1-3l-1-2c0-1 0-2-1-3v1l-1-1v-2c0-1 0-3-1-3v-2c-1-1-1-1-1-2v-2h-1c-1-6-2-13-4-18-1-1-1-1-1-2v-1c-1-1-1-2-1-3 0-3-2-7-3-9-2-5-3-9-5-14h0l1 2 1-1-1-1h0l-1-1c0-3 1 0 0-2 0 0 0-1-1-2v-1c-1-2-2-3-2-6 0-1-1-3-2-4l-6-13c-1-4-3-8-5-12-4-7-8-13-12-20l-6-16-3-9h-1l-1-1v2c-1-3-2-6-4-10-3-9-7-18-9-28 1-1 1-3 1-4 0-5-1-10-1-15z" class="V"></path><path d="M508 410l2 1 1 1c2 5 4 9 7 13v2l1 1h-1l-1-2-1 1h0l-8-17z" class="N"></path><path d="M465 305c1 3 2 6 2 9 0 2 0 2 1 4v2c1 6 4 12 5 19 0 1 1 3 1 4-1 3 3 11 4 14 0 1 1 3 2 4h-1l-1-1v2c-1-3-2-6-4-10-3-9-7-18-9-28 1-1 1-3 1-4 0-5-1-10-1-15z" class="I"></path><path d="M514 435v1c2 4 4 9 5 13l14 37 4 17c1 3 2 7 3 11h-1v-1c-1 3 0 7 2 11 1-1 0-1 1-1v-3-1c0-4-1-7-1-11 0-1-1-3-1-4s0-1-1-2h0c1-2 0-5-1-6-1-8-3-16-5-24 2 1 1 1 2 2l2 8s1 1 1 2l-1 1 1 3v2c1-1 0-1 1-1 0 2 0 5 2 7l1-1c1-1 2-1 3-1-1 1-2 3-3 4 0 1 0 3-1 5l1 1c-1 2 0 10 0 12h0c1 2 1 6 1 8 0 1-1 1-2 1 0-1-1-2-1-3h-1c0-2 0-4-1-5v-4c0-1-1-2-1-3l-1-2c0-1 0-2-1-3v1l-1-1v-2c0-1 0-3-1-3v-2c-1-1-1-1-1-2v-2h-1c-1-6-2-13-4-18-1-1-1-1-1-2v-1c-1-1-1-2-1-3 0-3-2-7-3-9-2-5-3-9-5-14h0l1 2 1-1-1-1h0l-1-1c0-3 1 0 0-2 0 0 0-1-1-2v-1c-1-2-2-3-2-6z" class="W"></path><defs><linearGradient id="v" x1="563.251" y1="336.812" x2="597.079" y2="359.594" xlink:href="#B"><stop offset="0" stop-color="#c8c7c7"></stop><stop offset="1" stop-color="#f8f7f6"></stop></linearGradient></defs><path fill="url(#v)" d="M603 287h1v-1h-1c-1 0-1 0-1-1-1-1-1-2-1-3a30.44 30.44 0 0 1 8-8h1v3h1c3 12 0 22-3 33-1 6-2 11-4 17h0l-1-1 1-3h0c-1 2-3 5-3 7 0 1 0 1-1 2s-1 3-1 5l-3 16c-1 4-2 8-3 13-3 9-11 18-17 25l-9 9-7 8-1 1c-3 3-7 8-9 12l-1 1-1 2v-7l2-2c1-6 4-11 6-16v1c1-2 3-4 4-6l3-9c3-8 4-16 6-24l1-5 1-5c0-1 1-2 2-3 0 3 0 5-1 7 3-2 4-6 5-9l9-15c3-8 6-15 10-22 2-3 4-6 6-10 1-1 2-3 2-4 1-2 0-4 1-6 0-1-1-1-2-2z"></path><path d="M599 317h1c-1 3-4 6-6 9-1 1-3 3-3 5v1h1c0-1 1-2 2-2-1 2-4 5-5 7-2 3-4 7-6 10h0l1-2v-1l-1 1c-1 1-2 1-3 2-2 3-4 8-5 12s-2 8-2 11c0 2-2 3-2 5-1 2-2 5-3 8 0-2 1-5 1-8 1-2 1-5 2-8 0-4 0-8 1-12 3-2 4-6 5-9l9-15 5-2c0-1 1-2 1-3l7-9z" class="W"></path><path d="M586 331l5-2-12 18-3 6-4 14c-1 1-1 1-1 0 0-4 0-8 1-12 3-2 4-6 5-9l9-15z" class="i"></path><defs><linearGradient id="w" x1="600.634" y1="325.372" x2="591.866" y2="293.628" xlink:href="#B"><stop offset="0" stop-color="#a5a2a3"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#w)" d="M605 289c2 4 2 9 1 13 0 1 1 4 0 6-1 3-5 6-6 9h-1l-7 9c0 1-1 2-1 3l-5 2c3-8 6-15 10-22 2-3 4-6 6-10 1-1 2-3 2-4 1-2 0-4 1-6z"></path><path d="M599 317v-2h0c3-4 5-9 7-13 0 1 1 4 0 6-1 3-5 6-6 9h-1z" class="N"></path><g class="e"><path d="M571 351c0-1 1-2 2-3 0 3 0 5-1 7-1 4-1 8-1 12-1 3-1 6-2 8 0 3-1 6-1 8 1 1 1 2 1 3-1 1-1 1-1 2-2 2-3 5-4 7-1 1-1 1-1 2l-9 12c0 2-3 4-4 6 1-6 4-11 6-16v1c1-2 3-4 4-6l3-9c3-8 4-16 6-24l1-5 1-5z"></path><path d="M567 400l9-9c6-7 14-16 17-25 1-5 2-9 3-13l3-16c0-2 0-4 1-5s1-1 1-2c0-2 2-5 3-7h0l-1 3 1 1-7 39-1 12c0 2 0 5-1 6 0 2 1 4 0 6-1 6-1 12-2 18 0 1 1 3 0 4h-1l-1-23c0-1 1-2 0-2 0 2 0 3-1 5v1 8h0c0 1 0 4-1 5 0 1 1 3 0 4v1h1v-1h1c0 1 0 4-1 5l1 5c0 1-1 2 0 2-1 7-2 12-1 19 0 2 1 3 2 5s1 4 3 6c-3 3-6 7-11 10-3 3-5 5-7 9h0c-1 4-1 8-1 12 0 5 0 11 1 16 1 1 1 2 1 3v2c1 2 1 4 2 6v1-2c0 1 1 2 0 4h-1c-1-1-2-2-2-3v-1c-1-2 0 0-1-1-1-3-3-6-4-9l-4-11v-2c-1-2-1-2-1-4h0v-2c-1-1-1-2-1-3-1-1 0-4 0-5l-1-1v2c0-1 0-3-1-5v-7-17-5l-3-3h0c1 2 2 3 2 6v1c0 1-1 3-1 5-1 1 0 3 0 4 0 3-1 6-1 10 1 2 1 4 1 7 1 2 0 4 1 6 1 1 0 3 0 4 1 3 1 6 1 8v2l1 1v4c1 3 3 7 2 10 0 2-2 5-3 6l-1 2v1h-1v-1c-1-2-2-3-3-4h-2l-1 1c0 2 4 6 4 7l-3 1-2 2-1 1c-2 3-5 7-9 9v2l-1-1-6 1h-1c-1 0-2 0-4 1h-3v-1h1v-1l-1-1c-2 0-4 0-5-1 4 0 7 0 10-1h1l5-2c1 0 2 0 2-1 0-2 0-6-1-8h0c0-2-1-10 0-12l-1-1c1-2 1-4 1-5 1-1 2-3 3-4-1 0-2 0-3 1l-1 1c-2-2-2-5-2-7-1 0 0 0-1 1v-2l-1-3 1-1c0-1-1-2-1-2l-2-8c-1-1 0-1-2-2-1-1-1-3-2-5-1-3-3-6-3-10 0-2-1-3-1-5l4-3c6-3 10-8 13-14v-1l3-6h0v-10-13c0 1 0 3 1 4v1h1v-1l5-12v1c1 0 1-1 1-1h1l-8 18v2 7l1-2 1-1c2-4 6-9 9-12l1-1 7-8z"></path></g><path d="M539 489c0-1 1-1 1-1 1-1 2 0 3-1h1v1c-1 1-3 0-4 1v1l1 2c0 1 0 2 1 3l-1 1c-2-2-2-5-2-7zm16 2h1l-5 17v1c0 1-1 4-2 4v-1-2h1v-3c-1 1-1 2-1 3h0l-2 3h0l8-22z" class="I"></path><path d="M542 504l1-2 1 1c1-2 2-3 3-4-1 2-2 5-2 7-1 3-1 6-1 9 0 2 0 5 1 7v1 1h-2c0-2 0-6-1-8h0c0-2-1-10 0-12z" class="U"></path><path d="M552 479l1-3c1 5-1 9-1 13l-4 6c0 1 0 3-1 4s-2 2-3 4l-1-1-1 2-1-1c1-2 1-4 1-5 1-1 2-3 3-4 4-4 7-9 7-15z" class="f"></path><path d="M541 503c1-1 2-2 2-3 1-2 2-4 4-6l1 1c0 1 0 3-1 4s-2 2-3 4l-1-1-1 2-1-1z" class="v"></path><path d="M552 479v-26c0-1-1-4 0-5 0-1 0-1 1-1 0-1 1-2 2-2h1c-1 5-1 10-1 15v12c0 2 0 6-1 9h1v-2c1 2-1 5-1 7v1c-1 1-1 2-2 2 0-4 2-8 1-13l-1 3z" class="I"></path><defs><linearGradient id="x" x1="537.072" y1="448.049" x2="548.915" y2="430.621" xlink:href="#B"><stop offset="0" stop-color="#999798"></stop><stop offset="1" stop-color="#d1d0cf"></stop></linearGradient></defs><path fill="url(#x)" d="M544 434l1 1c1-1 1-2 1-2 2-3 3-6 5-8 0 1 1 1 1 2-2 3-3 6-5 9-3 7-7 13-14 17-2 1-4 3-5 4 0-2-1-3-1-5l4-3c6-3 10-8 13-14v-1z"></path><path d="M544 435c0 3-3 7-5 9s-4 4-5 7l-2 1h1v1c-2 1-4 3-5 4 0-2-1-3-1-5l4-3c6-3 10-8 13-14z" class="Y"></path><path d="M556 509c0 2 4 6 4 7l-3 1-2 2-1 1c-2 3-5 7-9 9v2l-1-1-6 1h-1c-1 0-2 0-4 1h-3v-1h1v-1l-1-1c-2 0-4 0-5-1 4 0 7 0 10-1h1l5-2c1 0 2 0 2-1h2v-1c3-1 6-6 8-9 0-2 1-3 3-5z" class="D"></path><path d="M545 524c0 2-1 3-2 4s-2 1-3 1c0 1-1 1-2 1v1h-1c-1 0-2 0-4 1h-3v-1h1v-1l-1-1c-2 0-4 0-5-1 4 0 7 0 10-1h1l5-2c1 0 2 0 2-1h2z" class="B"></path><path d="M567 400l9-9c6-7 14-16 17-25 1-5 2-9 3-13l3-16c0-2 0-4 1-5s1-1 1-2c0-2 2-5 3-7h0l-1 3 1 1-7 39-1 12c0 2 0 5-1 6 0 2 1 4 0 6-1 6-1 12-2 18 0 1 1 3 0 4h-1l-1-23c0-1 1-2 0-2 0 2 0 3-1 5v1c-1 1-1 3-2 5v-1c-1-1 0-4 0-6 0-3 1-5 1-7-4 3-6 9-10 12-2 2-4 5-4 7h0l-1 1c-1 2-1 4-1 6-3 6-3 12-4 18-1 13-1 25-1 38 1 10 3 21 7 30 1 2 1 3 2 3 1 1 1 2 1 3v2c1 2 1 4 2 6v1-2c0 1 1 2 0 4h-1c-1-1-2-2-2-3v-1c-1-2 0 0-1-1-1-3-3-6-4-9l-4-11v-2c-1-2-1-2-1-4h0v-2c-1-1-1-2-1-3-1-1 0-4 0-5l-1-1v-9-16c0-3 0-7-2-9-1-2-2-2-4-2-2 1-3 2-4 4h0c-2 2-4 5-5 8s-3 8-5 10h-1c2-11 8-23 14-31 3-4 6-8 7-12 1-2 1-2 0-3h1c-5 1-11 10-15 14-2 2-3 5-5 8 0 0 0 1-1 2l-1-1 3-6h0v-10-13c0 1 0 3 1 4v1h1v-1l5-12v1c1 0 1-1 1-1h1l-8 18v2 7l1-2 1-1c2-4 6-9 9-12l1-1 7-8z" class="N"></path><path d="M567 400h1c2-1 4-2 6-4l-6 6v1c-1 1-3 2-4 3h-1c-1 1-2 2-3 2l7-8z" class="i"></path><path d="M573 410c0-2 0-4 1-6l1-1h0c0-2 2-5 4-7 4-3 6-9 10-12 0 2-1 4-1 7 0 2-1 5 0 6v1c1-2 1-4 2-5v8h0c0 1 0 4-1 5 0 1 1 3 0 4v1h1v-1h1c0 1 0 4-1 5l1 5c0 1-1 2 0 2-1 7-2 12-1 19 0 2 1 3 2 5s1 4 3 6c-3 3-6 7-11 10-3 3-5 5-7 9h0c-1 4-1 8-1 12 0 5 0 11 1 16-1 0-1-1-2-3-4-9-6-20-7-30 0-13 0-25 1-38 1-6 1-12 4-18z" class="V"></path><path d="M573 410c0-2 0-4 1-6l1-1h0c0-2 2-5 4-7 4-3 6-9 10-12 0 2-1 4-1 7 0 2-1 5 0 6v1 1c-1 6 0 13-1 20v1c-1-2 0-4 0-6l-1-22h1c0-1-1-2 0-3 0-1 0-1 1-2v-1c-3 5-7 8-9 13l1 1 2-3v1l-9 12z" class="I"></path><defs><linearGradient id="y" x1="639.604" y1="404.24" x2="532.114" y2="544.552" xlink:href="#B"><stop offset="0" stop-color="#9e9c9c"></stop><stop offset="1" stop-color="#efeeed"></stop></linearGradient></defs><path fill="url(#y)" d="M626 394c1 0 2-1 3-1l-1 2v1c1 1 2 1 3 2-4 9-7 20-9 30l-4 12-3 17c2 6-1 15-3 21h0v-1l-2 1-1 1c-1 2-1 3-2 5l-1 9-1 7 3 1c4 1 7 2 12 1l4-1h0c4 2 9-1 13-3l7-1c-1 1-7 7-7 8l1 2-2 3h0c-1 0-2 2-3 2 0-1 0-2-1-3-2 2-4 3-7 4-7 0-13-4-19-8l-2-2-3 23-2-1-7 25c-1 3-1 10-4 12l-6-3-1 2-1-1c-1-1-2-1-3-2-2 0-3 0-4 1-1 0-7-4-9-4 0 4-1 10 0 13l-2 1v-1c-1 1-2 2-2 3-2 1-2 1-2 2l-1 1c0-1-1-1-1-2-3-4-4-9-5-13l-1-1-4-18c-1-1-1-3-1-4-1-2-1-3 0-5v-2c4-2 7-6 9-9l1-1 2-2 3-1c0-1-4-5-4-7l1-1h2c1 1 2 2 3 4v1h1v-1l1-2c1-1 3-4 3-6 1-3-1-7-2-10v-4l-1-1v-2c0-2 0-5-1-8 0-1 1-3 0-4-1-2 0-4-1-6 0-3 0-5-1-7 0-4 1-7 1-10 0-1-1-3 0-4 0-2 1-4 1-5v-1c0-3-1-4-2-6h0l3 3v5 17 7c1 2 1 4 1 5v-2l1 1c0 1-1 4 0 5 0 1 0 2 1 3v2h0c0 2 0 2 1 4v2l4 11c1 3 3 6 4 9 1 1 0-1 1 1v1c0 1 1 2 2 3h1c1-2 0-3 0-4v2-1c-1-2-1-4-2-6v-2c0-1 0-2-1-3-1-5-1-11-1-16 0-4 0-8 1-12h0c2-4 4-6 7-9 5-3 8-7 11-10-2-2-2-4-3-6s-2-3-2-5c-1-7 0-12 1-19l1 1c-1 1-1 2-1 3 1-2 3-4 4-5 2-3 5-5 7-7v1c1-1 3-2 4-3h0c0-1 1-2 2-2h0v1c-2 2-4 4-5 6-1 3-4 3-4 6l2-2h1 1 1 2c2-3 4-5 6-8 4-6 8-12 13-17l1-2z"></path><path d="M626 394c1 0 2-1 3-1l-1 2v1c-1 1-1 1-1 3h1v2l-2-2v-2l-1-1 1-2z" class="v"></path><path d="M592 446c1-2 1-3 2-5l1 3v3l3 3-3 2c-2-2-2-4-3-6z" class="U"></path><path d="M555 519l2-2c2 3 3 5 4 8v3h-1s0-1-1-2c-1-2-2-5-4-7z" class="C"></path><path d="M560 551c1 2 2 2 2 4l-3 12v2h-1c-2-3-2-9-2-13 1 2 0 1 1 2v1 2c0 1 0 2 1 3h1v-1-1c0-1 1-2 1-3v-2c0-1 1-1 1-2-1-1-1-2-1-4z" class="e"></path><path d="M591 488c-2-3-3-8-3-12-1-4 2-9 5-12 2-2 5-3 8-3l1 1c-5 2-8 4-10 9-3 5-2 11 0 17h-1z" class="T"></path><path d="M559 550l1-1c4 2 8 4 12 5l9 4 1 1-1 2-1-1c-1-1-2-1-3-2-2 0-3 0-4 1-1 0-7-4-9-4-2 4-2 8-5 12l3-12c0-2-1-2-2-4l-1-1z" class="C"></path><path d="M598 450h3l-1-16 1 1v3c-1 3 0 8 0 12l1-1c1 0 1 0 2 1h0c-1 1-2 2-4 3-8 6-16 16-18 27l-1 1h0c-1-2 0-3 0-5l3-14c5-3 8-7 11-10l3-2z" class="v"></path><path d="M577 499c-1-5-1-11-1-16 0-4 0-8 1-12h0c2-4 4-6 7-9l-3 14c0 2-1 3 0 5h0v5l-2 9c0 5 0 9 1 14h0v2-1c-1-2-1-4-2-6v-2c0-1 0-2-1-3z" class="i"></path><path d="M559 550l-1-2h1 2c1-1 1-3 1-5v-3h1c1-3 0-6 1-9 1 0 2 0 2 1 1 0 2 1 2 2l1 1c1 3 1 5 1 8v3c0 1 1 1 1 1h1l1 1c-1 1-2 0-1 2 0 1 0 2-1 3l1 1c-4-1-8-3-12-5l-1 1z" class="e"></path><path d="M609 479v-2c1-4 0-9 1-13 1-7 3-15 4-22 1 0 0 1 0 2v1 4-2c1-2 0 0 1-1l1-5c0-2 1-4 1-6l1-1c0-3 0 1 0-1v-1h0c1 1 1 1 0 2v2c0 1 0 1-1 2v1l1 1-3 17c2 6-1 15-3 21h0v-1l-2 1-1 1z" class="U"></path><path d="M617 439l1 1-3 17c-1 2-1 5-2 7 0-3 0-5 1-8 0-4 0-7 1-10 1-1 1-3 1-4 1-1 1-2 1-3z" class="c"></path><path d="M613 464c1-2 1-5 2-7 2 6-1 15-3 21h0v-1l-2 1c0-2 1-4 2-6l1-8z" class="W"></path><path d="M602 415c1-1 3-2 4-3h0c0-1 1-2 2-2h0v1c-2 2-4 4-5 6-1 3-4 3-4 6l2-2h1 1 1 2v2-1c-3 2-4 5-5 7v2c-1 1-1 2-2 2-1 2-2 3-2 5l-1-1c1-1 1-1 1-2h0l3-3-1-1c-1 1-2 2-3 4h0c-1 2-1 5-2 6-1 2-1 3-2 5-1-2-2-3-2-5-1-7 0-12 1-19l1 1c-1 1-1 2-1 3 1-2 3-4 4-5 2-3 5-5 7-7v1z" class="AB"></path><path d="M590 441h3l1-3c0-1 1-2 2-3h0c-1 2-1 5-2 6-1 2-1 3-2 5-1-2-2-3-2-5z" class="f"></path><path d="M602 414v1 1l-2 2h0c-1 1-1 2-1 3l-3 6c-2 3-2 7-3 10h-1c0-3 0-7 2-10l-1-1c0-1 1-2 1-3 1-1 1-1 1-2 2-3 5-5 7-7z" class="Y"></path><path d="M592 488c3 5 8 9 13 12l3 1c4 1 7 2 12 1l4-1h0c4 2 9-1 13-3l7-1c-1 1-7 7-7 8l1 2-2 3h0c-1 0-2 2-3 2 0-1 0-2-1-3-2 2-4 3-7 4-7 0-13-4-19-8l-2-2c-5-4-10-9-13-15h1z" class="E"></path><path d="M637 505l1 2-2 3h0c-1 0-2 2-3 2 0-1 0-2-1-3 1 0 4-3 5-4z" class="O"></path><path d="M554 520v1c0 1 0 2-1 3h0v4 5l1 3v1 4c1 1 1 0 0 2 0 1 0 1 1 2v6c1 1 1 3 1 4v1c0 4 0 10 2 13h1v-2c3-4 3-8 5-12 0 4-1 10 0 13l-2 1v-1c-1 1-2 2-2 3-2 1-2 1-2 2l-1 1c0-1-1-1-1-2-3-4-4-9-5-13l-1-1-4-18c-1-1-1-3-1-4-1-2-1-3 0-5v-2c4-2 7-6 9-9z" class="W"></path><path d="M581 558c-1-4 1-8 2-11 2-9 4-18 8-27 2 1 1 2 2 4l1-1v1l1-1v-1c1-2 2-2 3-2l2 2-1 3-7 25c-1 3-1 10-4 12l-6-3-1-1zM454 306l8-23v18l1 1 2 22c2 10 6 19 9 28 2 4 3 7 4 10v-2l1 1h1l3 9 6 16c4 7 8 13 12 20 2 4 4 8 5 12l6 13c1 1 2 3 2 4 0 3 1 4 2 6v1c1 1 1 2 1 2 1 2 0-1 0 2l1 1h0l1 1-1 1-1-2h0c2 5 3 9 5 14 1 2 3 6 3 9 0 1 0 2 1 3v1c0 1 0 1 1 2 2 5 3 12 4 18h1v2c0 1 0 1 1 2v2c1 0 1 2 1 3v2l1 1v-1c1 1 1 2 1 3l1 2c0 1 1 2 1 3v4c1 1 1 3 1 5h1c0 1 1 2 1 3l-5 2h-1l-2-1c0-1-1-3-1-4-2-5-2-10-6-15l-1-2h0c-4-3-8-2-12-3-3 0-7-1-9-2s-2-2-3-4c0-3 1-5 2-7 1-1 1-1 1-3h-3-6-5l-1-2v-1c-1 0-2 0-3-1h-1l-1-1-1-1-1-1c-3-6-6-11-8-18h-1c-1-2-2-3-2-5 0-1 0-1-1-1-5-9-10-18-14-27l-4-7-3-6-2-3c0-1-1-2-2-4-1-3-2-5-3-8-1-4-4-8-4-12h2l3 3v1l1-1-4-4-5-7c2 0 3 1 4 3 0-2-1-3-2-4l1-1c1 0 2 0 3-1-6-9-12-19-22-24-4-3-5-9-7-13-1-3-3-7-5-10 1-1 1-2 2-3l-1-3 9-13-1-2-1-1c1-2 4-5 5-8l2-3h0l1-1c3 2 7 4 9 6l12 9c1 1 3 4 5 5 0-1 1-1 1-2 2-2 3-6 4-8z" class="e"></path><path d="M417 311c2 0 3-1 4-2-2 4-3 8-5 11l-7 7-1-3 9-13z" class="Y"></path><path d="M454 306l8-23v18l-1-7v-2-1l-1 8c-1 3-1 6-2 9 0 2-1 4-1 6h-1v-1-1c0-3 1-5 1-8v-1-2l1 1v1c1 0 1 0 0-1 0-2 1-3 0-5-1 3-1 6-4 9z" class="W"></path><path d="M454 324h0c0 2-1 4-1 6-1 2-2 3-2 5-1 1-1 1-1 2v1c-2 4-2 8-3 12s-3 9-4 13v1l-1-1c2-13 6-26 10-38 1 0 0 0 1 1v1c0-2 0-2 1-3zm-5 2h1c0 4-3 8-4 11s-2 7-3 11-3 8-3 12c-1 3 0 6 0 9h-1l-1-1c-2-4 0-10 1-14 2-10 6-19 10-28z" class="I"></path><defs><linearGradient id="z" x1="434.633" y1="301.56" x2="426.935" y2="332.083" xlink:href="#B"><stop offset="0" stop-color="#989696"></stop><stop offset="1" stop-color="#cccacb"></stop></linearGradient></defs><path fill="url(#z)" d="M423 296c3 2 7 4 9 6l12 9c1 1 3 4 5 5v1c0 1-2 4-3 5l-8 16c-2-8-2-16-7-23 0-2-2-3-3-4-2-1-4-2-7-2-1 1-2 2-4 2l-1-2-1-1c1-2 4-5 5-8l2-3h0l1-1z"></path><path d="M423 296c3 2 7 4 9 6h0-1-2-2-2l1-2-3-1-3 1 2-3h0l1-1z" class="Y"></path><path d="M416 309h2c2-1 3-2 4-3 2-1 5-2 8-2l2 2 1 3 3 6c-2-3-5-5-7-7-1-1-3-1-4-1-2-1-3 0-4 1h0 2c1 0 2 0 3 1 1 0 2 1 2 2-2-1-4-2-7-2-1 1-2 2-4 2l-1-2z" class="c"></path><path d="M466 367l-2-2h-1c-3-1-5-4-7-6 0-2 0-3 1-5 2 0 4 3 6 4l4 3c-3-5-6-9-8-14v-8l8 13c2 3 4 5 6 7 4 4 5 10 7 15 0 3 3 6 4 9l15 27c2 3 4 7 5 11l11 25c1 2 1 4 1 6 2 6 5 11 6 17 0 1 1 1 1 2l1 1c0 1 0 1 1 2v-2c-1-1 0-2 0-2 0 1 0 2 1 3v1c0 1 0 1 1 2 2 5 3 12 4 18h1v2c0 1 0 1 1 2v2c1 0 1 2 1 3v2l1 1v-1c1 1 1 2 1 3l1 2c0 1 1 2 1 3v4c1 1 1 3 1 5h1c0 1 1 2 1 3l-5 2h-1l-2-1c0-1-1-3-1-4-2-5-2-10-6-15l-1-2h1 0v-4c0-1-1-3-1-4l-9-24c-8-18-16-37-28-54-1-2-3-4-5-6-3-3-7-4-10-8-5-11-2-26-7-38h0z" class="V"></path><path d="M516 473c5 4 8 18 11 23 1 4 2 7 3 10s3 6 3 10c1 3 2 7 4 10h1l1-1c0-3 1 1 0-2v-1l-1-1c0-1-1-3-1-4-1-2 0-4-1-6l1-1c0 1 1 2 1 3v4c1 1 1 3 1 5h1c0 1 1 2 1 3l-5 2h-1l-2-1c0-1-1-3-1-4-2-5-2-10-6-15l-1-2h1 0v-4c0-1-1-3-1-4l-9-24z" class="e"></path><path d="M526 501c6 8 7 18 10 26h-1l-2-1c0-1-1-3-1-4-2-5-2-10-6-15l-1-2h1 0v-4z" class="Y"></path><defs><linearGradient id="AA" x1="468.499" y1="447.612" x2="498.719" y2="434.874" xlink:href="#B"><stop offset="0" stop-color="#676468"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#AA)" d="M466 367h0c5 12 2 27 7 38 3 4 7 5 10 8 2 2 4 4 5 6 12 17 20 36 28 54l9 24c0 1 1 3 1 4v4h0-1 0c-4-3-8-2-12-3-3 0-7-1-9-2s-2-2-3-4c0-3 1-5 2-7 1-1 1-1 1-3h-3-6-5l-1-2v-1c-1 0-2 0-3-1h-1l-1-1-1-1-1-1c-3-6-6-11-8-18h-1c-1-2-2-3-2-5 0-1 0-1-1-1-5-9-10-18-14-27l-4-7-3-6-2-3c0-1-1-2-2-4-1-3-2-5-3-8-1-4-4-8-4-12h2l3 3v1l1-1-4-4-5-7c2 0 3 1 4 3 0-2-1-3-2-4l1-1c1 0 2 0 3-1 3 4 7 7 11 9 2 1 6 3 8 2 5-3 3-16 5-21h1z"></path><path d="M512 488h1l2 2 2 2 2 2v2c1 1 1 2 2 3l1 1c1 1 2 3 3 5-4-3-8-2-12-3v-2c-1-2-1-4 0-6l-1-1c-1-1-1-3-3-4 2 0 2 0 3-1z" class="AH"></path><path d="M513 494v1h2 0c0 2 1 3 2 4l-3 3h-1v-2c-1-2-1-4 0-6z" class="f"></path><path d="M512 488h1l2 2 2 2v2c-1 0-2 1-2 1h-2v-1l-1-1c-1-1-1-3-3-4 2 0 2 0 3-1z" class="B"></path><path d="M515 490l2 2v2c-1 0-2 1-2 1h-2c1-2 1-3 2-5z" class="M"></path><path d="M503 489h6c2 1 2 3 3 4l1 1c-1 2-1 4 0 6v2c-3 0-7-1-9-2s-2-2-3-4c0-3 1-5 2-7z" class="W"></path><path d="M497 475c1-1 1-1 1-2-1-2-1-3 0-4 1-2 2-2 4-3h0c1-1 0-1 2-1l5 10c2 5 4 8 4 13h-1c-1 1-1 1-3 1h-6c1-1 1-1 1-3h-3c-1-3-1-6-1-10h0l-1-1v1l-1-1h-1z" class="c"></path><path d="M504 486c3 0 5 1 8 2-1 1-1 1-3 1h-6c1-1 1-1 1-3z" class="F"></path><path d="M466 405h2c2 1 4 5 6 7 3 4 7 8 12 12 5 5 9 14 9 21 0 3 0 4 2 6v-1h2c1 2 2 3 2 5 0 1 0 2 1 3h1v1 1c0 1 1 2 1 4v1c-2 0-1 0-2 1h0c-2 1-3 1-4 3-1 1-1 2 0 4 0 1 0 1-1 2l-3-2-1-1h-1l-1-2c4 0 5-2 7-4 0-1 1-2 2-2v-1s0-1-1-2c0-1-1-3-2-5h0v-3h0c-1 1-1 0-2 1l-1-1-1-3c1-1 1-3 1-4h-1v1h0l-1-5v-1c0-3-3-11-6-13h0-1c-5-3-10-9-13-13-2-2-6-7-6-10z" class="v"></path><path d="M493 447h0v-1h1c0 1 0 3-1 4l1 3 1 1c1-1 1 0 2-1h0v3h0c1 2 2 4 2 5 1 1 1 2 1 2v1c-1 0-2 1-2 2-2 2-3 4-7 4l1 2h1l1 1 3 2h1l1 1v-1l1 1h0c0 4 0 7 1 10h-6-5l-1-2v-1c-1 0-2 0-3-1h-1l-1-1-1-1c1-1 1-3 2-4l2-10c1-4 3-9 4-13 1-1 1-5 2-6z" class="U"></path><path d="M490 466c1-3 2-6 4-8l2 2c2 2 2 4 2 6-2 2-3 4-7 4l-1 1v1h0c-1-1 0-4 0-6h0z" class="N"></path><path d="M490 466h0c0 2-1 5 0 6h0v-1l1-1 1 2h1l1 1c-1 1-2 2-2 5l-1 4v2l1 1h-1l-2-1v-1c-1 0-2 0-3-1h-1l-1-1c2-1 2-7 3-10 1-2 2-4 3-5z" class="i"></path><path d="M485 482h2l1-1c0-1 0-2-1-3v-1c1-1 1-2 2-4l2 1 1 4-1 4v2l1 1h-1l-2-1v-1c-1 0-2 0-3-1h-1z" class="U"></path><path d="M494 473l3 2h1l1 1v-1l1 1h0c0 4 0 7 1 10h-6-5l-1-2 2 1h1l-1-1v-2l1-4c0-3 1-4 2-5z" class="W"></path><defs><linearGradient id="AB" x1="472.581" y1="412.192" x2="466.336" y2="418.382" xlink:href="#B"><stop offset="0" stop-color="#959394"></stop><stop offset="1" stop-color="#b0aeaf"></stop></linearGradient></defs><path fill="url(#AB)" d="M435 380c2 0 3 1 4 3l3 3h0c3 2 5 4 7 6 3 2 7 3 10 5 2 1 3 4 5 5 1 1 1 2 2 3 0 3 4 8 6 10 3 4 8 10 13 13h1 0c3 2 6 10 6 13v1l1 5c-1 1-1 5-2 6l-1-1v-1c1-1 1-1 1-2h0c0-3 1-5-1-7h0c-1-1-1-2-1-2-4-8-13-13-20-18-1-2-5-6-7-7-1 0-1 3-1 4-1 0-1 1-2 1h0c0 2-1 3-1 5-1 2-1 2-2 3l-4-7-3-6-2-3c0-1-1-2-2-4-1-3-2-5-3-8-1-4-4-8-4-12h2l3 3v1l1-1-4-4-5-7z"></path><path d="M444 391c8 6 15 13 16 23v5c-1 0-1 0-1 1h0c-1-1 0-3 0-4 0-3-1-5-2-7-2-2-4-5-6-7-3-4-6-6-8-10l1-1z" class="m"></path><defs><linearGradient id="AC" x1="442.333" y1="407.832" x2="456.714" y2="406.354" xlink:href="#B"><stop offset="0" stop-color="#898688"></stop><stop offset="1" stop-color="#b9b7b8"></stop></linearGradient></defs><path fill="url(#AC)" d="M438 388h2l3 3v1c2 4 5 6 8 10 2 2 4 5 6 7 1 2 2 4 2 7 0 1-1 3 0 4 0 2-1 3-1 5-1 2-1 2-2 3l-4-7-3-6-2-3c0-1-1-2-2-4-1-3-2-5-3-8-1-4-4-8-4-12z"></path><path d="M459 420c1 0 1-1 2-1 0-1 0-4 1-4 2 1 6 5 7 7 7 5 16 10 20 18 0 0 0 1 1 2h0c2 2 1 4 1 7h0c0 1 0 1-1 2v1l1 1c-1 4-3 9-4 13l-2 10c-1 1-1 3-2 4l-1-1c-3-6-6-11-8-18h-1c-1-2-2-3-2-5 0-1 0-1-1-1-5-9-10-18-14-27 1-1 1-1 2-3 0-2 1-3 1-5h0z" class="W"></path><path d="M471 456h0c3 1 5 0 7 0l-4 5h-1c-1-2-2-3-2-5z" class="O"></path><path d="M611 277c0-1 0-2-1-4l5-5h0l2-2c2-3 6-5 8-8-1 0-1-1-2-1-6-4-10-8-14-14l-3-3-17-34c-2-3-4-6-5-9-2-4-5-7-6-11l7 9c1 0 1 1 2 2s1 1 3 2h0c1 1 1 1 1 2 2 2 4 5 6 6 2 2 6 2 9 3 4 1 8 2 13 3 2 1 3 1 4 3v1h26 13 1c4 1 12 2 15 0l1-1v1c-1 0 0 0 0 1-1 0-1 0-2 1-1 0-3-1-4 0h-8 0c3 1 7 1 11 1 1 0 1 1 2 2 2 1 5 1 7 1-2 0-4 1-6 1l-29 2h-12-4-1l-1 1-8-1c2 4 5 7 7 11l-1 3c2 1 5 0 7 1 2 0 3 0 5 1l-2 1h1l-1 1c5 1 10 2 15 4 4 2 7 5 10 8 4 4 8 8 11 13l3 1c0-1-1-2-1-4 1 1 2 1 3 2l1 2c1-1 1-2 2-3 1 1 2 3 2 3 2 1 1 1 3 2 0 1 1 0 2 2l2 4v2c1 0 1 1 2 1l-1 1c1 1 1 2 1 3v1h-1c1 1 1 3 1 4 1 0 1 1 1 2l1 1 2 7c0 1 1 2 1 3 1 11 0 21-1 31-1 6-3 12-6 17 0 1-1 2-1 3h0l-1 1-1 2-1 1-4 4c0-2 1-6-1-9l-1 1h0l1-3-1-1-2 2-7 5-8 9-1-1c-3 1-5 3-7 5-1 1-3 2-4 3-2 1-3 2-4 4-2 1-3 2-4 3-2 4-6 7-9 9-1 2-3 4-5 6l-1 3c-1-1-2-1-3-2v-1l1-2c-1 0-2 1-3 1l-1 2c-5 5-9 11-13 17-2 3-4 5-6 8h-2-1-1-1l-2 2c0-3 3-3 4-6 1-2 3-4 5-6v-1h0c-1 0-2 1-2 2h0c-1 1-3 2-4 3v-1c-2 2-5 4-7 7-1 1-3 3-4 5 0-1 0-2 1-3l-1-1c-1 0 0-1 0-2l-1-5c1-1 1-4 1-5h-1v1h-1v-1c1-1 0-3 0-4 1-1 1-4 1-5h0v-8-1c1-2 1-3 1-5 1 0 0 1 0 2l1 23h1c1-1 0-3 0-4 1-6 1-12 2-18 1-2 0-4 0-6 1-1 1-4 1-6l1-12 7-39h0c2-6 3-11 4-17 3-11 6-21 3-33z" class="V"></path><path d="M674 326c2 0 3 0 4 1l-5 3h0c1-2 1-3 1-4z" class="N"></path><path d="M637 241c2 0 3 0 5 1l-2 1h1l-1 1-10-1c1-1 2-1 4-1h0v-1h3z" class="C"></path><path d="M673 322c2-1 3-2 5-1 2 0 3 1 4 2v1h-1c-3 1-5-1-8-2z" class="U"></path><path d="M670 325h1c1 0 2 0 3 1 0 1 0 2-1 4h0c-1 1-1 1-2 1l-2-1v-1c-1-2 0-2 1-4z" class="W"></path><path d="M669 330v-1c-1-2 0-2 1-4 2 2 2 2 2 5h-2-1z" class="I"></path><path d="M671 325l2-3c3 1 5 3 8 2h1c1 2 1 2 1 3-1 0-1 0-1-1l-4 1c-1-1-2-1-4-1-1-1-2-1-3-1z" class="i"></path><path d="M688 306c2 2 2 4 2 6 1 4 0 7 1 11 1 1 1 3 1 5l2-1-1 5c-1 0-2 1-3 3-1 1-1 3-2 4 0 1-1 3-2 4l-1-1c5-10 5-25 3-36z" class="b"></path><path d="M692 328l2-1-1 5c-1 0-2 1-3 3l2-7z" class="g"></path><path d="M597 366c0 4 1 7 1 11h1c1-3 2-5 3-7 0 6-3 12-4 18-1 2 0 4-1 6s0 4 0 6l1 1-1 1v6c-1 1-1 2-1 3-1-1-1-2-1-2 1-4 1-10 1-14 0-2-1-5 0-7 1-1 1-2 1-3-1-1-1-1-1-2v-5l1-12z" class="I"></path><path d="M596 378v5c0 1 0 1 1 2 0 1 0 2-1 3-1 2 0 5 0 7 0 4 0 10-1 14 0 0 0 1 1 2h1v-1s0-1 1-1v-1c2-2 4-5 7-7h1c-2 3-6 7-8 10-2 2-4 6-5 9-1 1-1 2-1 3l-1-1c-1 0 0-1 0-2l-1-5c1-1 1-4 1-5h-1v1h-1v-1c1-1 0-3 0-4 1-1 1-4 1-5h0v-8-1c1-2 1-3 1-5 1 0 0 1 0 2l1 23h1c1-1 0-3 0-4 1-6 1-12 2-18 1-2 0-4 0-6 1-1 1-4 1-6z" class="W"></path><path d="M598 388c3-1 6-8 7-11l1-2c3 0 5 0 7-2l5-1c-1 2-2 3-3 5v1l-2 3c-3 4-8 8-11 13-1 2-2 5-3 8-2 2-2 4-2 6v-6l1-1-1-1c0-2-1-4 0-6s0-4 1-6z" class="e"></path><path d="M682 292h2v1c-2 6-7 11-11 16-7 10-15 22-26 30-1 1-3 3-5 3-1 1-2 1-2 2-2 0-4 1-5 2h-1l9-8 12-12c4-4 7-8 10-12l17-22z" class="I"></path><path d="M591 201c2 2 4 5 6 6 2 2 6 2 9 3 4 1 8 2 13 3 2 1 3 1 4 3v1h26 13c-4 1-9 0-13 0h-24-12l-2 1-1 1h-1c0 6 4 10 7 15 1 3 2 5 4 7h-1c-9-10-17-23-25-35-1-2-2-3-3-5z" class="W"></path><path d="M663 217c4 1 12 2 15 0l1-1v1c-1 0 0 0 0 1-1 0-1 0-2 1-1 0-3-1-4 0h-8 0c3 1 7 1 11 1 1 0 1 1 2 2 2 1 5 1 7 1-2 0-4 1-6 1l-29 2h-12-4-1l-1 1-8-1c2 4 5 7 7 11l-1 3c2 1 5 0 7 1h-3v1h0c-2 0-3 0-4 1l-4 1-15-25h-1l1-1 2-1h12 24c4 0 9 1 13 0h1z" class="M"></path><path d="M610 219l1-1 2-1c2 4 7 5 9 8 1 1 1 2 1 2 2 2 3 4 4 5 1 3 3 4 4 7-3-2-4-4-5-7l-1-1h-1c1 1 1 2 2 3 0 2 3 5 4 6 2 1 5 0 7 1h-3v1h0c-2 0-3 0-4 1l-4 1-15-25h-1z" class="D"></path><defs><linearGradient id="AD" x1="644.956" y1="340.776" x2="636.544" y2="426.724" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#878588"></stop></linearGradient></defs><path fill="url(#AD)" d="M685 342c0 3-2 5-2 8l-2 2-7 5-8 9-1-1c-3 1-5 3-7 5-1 1-3 2-4 3-2 1-3 2-4 4-2 1-3 2-4 3-2 4-6 7-9 9-1 2-3 4-5 6l-1 3c-1-1-2-1-3-2v-1l1-2c-1 0-2 1-3 1l-1 2c-5 5-9 11-13 17-2 3-4 5-6 8h-2-1-1-1l-2 2c0-3 3-3 4-6 1-2 3-4 5-6v-1h0c-1 0-2 1-2 2h0c-1 1-3 2-4 3v-1c7-8 13-18 21-25 9-9 19-18 30-25 4-2 8-3 11-5 4-3 6-6 10-8l11-9z"></path><path d="M660 365h0c5-3 9-6 14-8l-8 9-1-1c-3 1-5 3-7 5l-1-1c2-2 3-2 3-4z" class="AE"></path><path d="M660 365c0 2-1 2-3 4l1 1c-1 1-3 2-4 3-2 1-3 2-4 4-2 1-3 2-4 3-2 4-6 7-9 9-1 2-3 4-5 6l-1 3c-1-1-2-1-3-2v-1l1-2c-1 0-2 1-3 1l8-8c8-8 16-16 26-21z" class="Y"></path><path d="M646 380c-2 4-6 7-9 9-1 2-3 4-5 6 1-3 2-4 4-6v-1c4-2 5-6 10-8z" class="U"></path><path d="M678 266c1 1 2 1 3 2l1 2c1-1 1-2 2-3 1 1 2 3 2 3 2 1 1 1 3 2 0 1 1 0 2 2l2 4v2c1 0 1 1 2 1l-1 1c1 1 1 2 1 3v1h-1c1 1 1 3 1 4 1 0 1 1 1 2l1 1 2 7c0 1 1 2 1 3 1 11 0 21-1 31-1 6-3 12-6 17 0 1-1 2-1 3h0l-1 1-1 2-1 1-4 4c0-2 1-6-1-9l-1 1h0l1-3-1-1c0-3 2-5 2-8h0l1 1c1-1 2-3 2-4 1-1 1-3 2-4 1-2 2-3 3-3l1-5-2 1c0-2 0-4-1-5-1-4 0-7-1-11 0-2 0-4-2-6-1-9-4-17-6-26l-6-11 3 1c0-1-1-2-1-4z" class="M"></path><path d="M678 266c1 1 2 1 3 2l1 2c1-1 1-2 2-3 1 1 2 3 2 3l1 6v1 2l2 7c-2-3-3-6-6-8 0 0-1-1-1 0v2l-6-11 3 1c0-1-1-2-1-4z" class="b"></path><path d="M678 266c1 1 2 1 3 2l1 2 3 6-1-1-5-5c0-1-1-2-1-4z" class="O"></path><path d="M684 267c1 1 2 3 2 3l1 6v1 2l-2-3-3-6c1-1 1-2 2-3z" class="R"></path><path d="M690 335c1-2 2-3 3-3v2c3 6-1 11 0 17 0 1-1 2-1 3h0l-1 1-1 2-1 1-4 4c0-2 1-6-1-9l-1 1h0l1-3-1-1c0-3 2-5 2-8h0l1 1c1-1 2-3 2-4 1-1 1-3 2-4z" class="D"></path><path d="M689 351l2-8c1 1 1 3 1 5s-1 4 0 6h0l-1 1-1 2-1 1c-1-2 0-5 0-7z" class="C"></path><path d="M690 335c1-2 2-3 3-3v2l-2 9-2 8v-3c1-3 0-6-1-9 1-1 1-3 2-4z" class="w"></path><path d="M688 339c1 3 2 6 1 9v3c0 2-1 5 0 7l-4 4c0-2 1-6-1-9l-1 1h0l1-3-1-1c0-3 2-5 2-8h0l1 1c1-1 2-3 2-4z" class="g"></path><path d="M682 280v-2c0-1 1 0 1 0 3 2 4 5 6 8 4 13 5 27 5 41l-2 1c0-2 0-4-1-5-1-4 0-7-1-11 0-2 0-4-2-6-1-9-4-17-6-26z" class="o"></path><path d="M516 106a104.13 104.13 0 0 1 12 5c4 1 7 3 11 5 5 2 9 4 13 7l1 1c2 4 2 8 2 12l3 18c1 3 4 5 6 8l2 1c0 1-1 3-1 4v3c-1 1-1 3-1 5 0 3-3 8-5 10l-5 5 1 2h-1v6c1 2 1 5 1 6h0c-2 3-1 6-1 9h-1c-1 0-1 1-2 1 0 1 0 1 1 1-1 2 0 5-1 7-1 1-1 3-1 4v2l-1 1h-1s-1 2-2 2l-1 7-1 9c-4 33-12 65-23 97-1 5-3 12-5 15h0c-1-4-3-8-5-12l-8-19c-7-19-13-37-17-57l-4-19-2-1-3-37-2-4h0v-1-4h1c0-1 0-2-1-3h-1l-1-2-1 2v-5-3c-2 0-2 0-3-1l1-8c-2-2-3-5-3-7-1-1-1-3-1-4h2c2 0 3-1 4-2 1-3 3-7 3-10l2-2s0-7 1-8c1 0 2-1 3-2h0c1-2 0-7 0-9l1-2c2-2 4-2 6-2-1 0-1-1-2-1l2-2 1 1c2 0 3 0 4 1v1h1c1 0 1 1 2 1h2c2 1 3 1 4 1l3 3c1 1 2 2 2 3 1 0 1-1 2-1 1 1 1 2 1 2 1 1 3 1 3 2 1 1 1 2 1 3v5l-1 1v1c-1 0-2 0-2 1v2l1-1h0c1-1 2-1 4-1v-1c-1-2-1-4-1-6v-5-8c0-2 1-4 0-7l1-3h-1v-1-7c1-1 1-4 1-5l-1-3v-7h1z" class="L"></path><path d="M501 265l2 3-1 2-2-2c0-1 1-2 1-3z" class="x"></path><path d="M489 258c-2-2-2-6-2-8 1 2 2 5 5 7h0l-3 1z" class="p"></path><path d="M541 205l3 1-5 7v-2c0-1 1-1 1-2h0l-1-2 2-2z" class="c"></path><path d="M514 172c1 1 0 3 1 4s2 1 4 2l-3 3v-1l-2-2v-6z" class="d"></path><path d="M520 290v-2c1-2 1-3 3-4l-1 10c-1-1-2-2-2-3v-1z" class="h"></path><path d="M520 199l1-1 1-2h0c0 4 0 9-1 12l-1-9z" class="d"></path><path d="M515 311c0 1 0 2 1 3 0 5 0 11-1 17v-20z" class="h"></path><path d="M552 123l1 1c0 1 0 2-1 2 0 3-1 5-2 7l-2-2 4-8z" class="z"></path><path d="M493 256l2 3v2c1 1 1 1 1 2l-7-5 3-1 1-1z" class="x"></path><path d="M554 163c2 1 2 2 2 4l1 2c-1 1-2 1-2 2-1 1 0 2 0 2l-1 1c0-4-1-7 0-11z" class="q"></path><path d="M503 268c3 5 5 9 7 13l-8-11 1-2z" class="p"></path><path d="M547 139l-1-2c-1-1-1 0-3-1l5-5 2 2-1 1c-1 2-1 2-1 4l-1 1z" class="AA"></path><path d="M525 245c2-5 4-9 6-13l-2 11-2 1c-1 0-1 0-2 1z" class="h"></path><path d="M489 212l1-1 2 1c1 1 3 3 4 3v3l-1 1c-2-2-5-4-6-7z" class="AA"></path><path d="M549 134c1 4 1 12-1 15h0c-1 0-2-1-3-2h0c2-3 2-5 2-8l1-1c0-2 0-2 1-4z" class="q"></path><path d="M520 291c0 1 1 2 2 3l-1 16h-1v-9h-1v2h-1v-2c0-1 0-3 1-4l1-6z" class="m"></path><path d="M554 163l1-1c2 2 2 4 3 7v-2l2-1c1-2 0-1 2-2h1c1 2 1 3 1 5-2 2-5 0-7 2v-2l-1-2c0-2 0-3-2-4z" class="p"></path><path d="M520 176h1c-1 2-2 3-2 5h1c1 2 3 4 3 7l-1 1v1l-2 1v-2c-1-1-1-1 0-2l-1-3h-1-1l1-2c-1 0-1 0-2 1v-2l3-3 1-2z" class="h"></path><path d="M492 252c-2-5-5-9-6-15v-3l9 16c-1 1-1 0-2 1l-1 1z" class="x"></path><path d="M483 213c2 5 5 9 7 14 1 3 2 7 4 11h-1c-6-7-9-16-10-25z" class="p"></path><path d="M557 169v2c1 2 1 2 2 2h3v1 1c0 1-1 1-1 2 0-1 0-1-1-1-2-1-2-2-4-2-2 1-3 3-4 4-2 5-6 8-10 10 2-2 4-6 6-8s4-2 6-5v-1l1-1s-1-1 0-2c0-1 1-1 2-2z" class="Y"></path><path d="M516 158v1h3 2c0 2 0 3-1 4s-3 0-4 1-2 6-2 8v6l-2 1c2-5 2-10 2-14-1-2-2-3-3-4l1-1h0c1-1 2-1 4-1v-1z" class="h"></path><path d="M516 159l-1 3c0 1 0 1-1 2v1c-1-2-2-3-3-4l1-1h0c1-1 2-1 4-1z" class="z"></path><path fill="#6f3f41" d="M520 176c1-3 3-7 5-11 2-3 6-7 8-10 1-2 2-3 4-4l-3 3c0 2 0 3-1 4l-4 7c-1 2-3 3-4 5l1 1h1v1h-2v-1c-2 1-3 4-4 5h-1z"></path><path d="M522 111c0 1 1 1 2 1 0 1 2 1 3 2 0 0 0 1 1 2 1 2 5 2 6 5 1 1 0 3 0 4l-1 1c-1 1-1 3-2 5l-1 2-1-3c0-2 1-3 0-4 0-1 1-2 1-3 1-2-2-4-3-6-1-1-1-3-3-3l-2-2v-1z" class="p"></path><path d="M495 250l6 15c0 1-1 2-1 3l-4-5c0-1 0-1-1-2v-2l-2-3-1-4 1-1c1-1 1 0 2-1zm22-34v-12c0-2 0-4 1-5 1-2 1-3 1-4h1v4l1 9v13c-1-1-1-2-1-4-2 0-2 0-3-1z" class="s"></path><path d="M530 134c-1 2-1 4-2 6v1c-1 0-1 0-1 1-1 0-2 1-2 1-1 1 0 2-1 3s-1 1-1 2c-1 1-2 2-3 1h-2c-1 2-1 6-1 9h1 0l1 1h-3v-1c-1-2-1-4-1-6v-5c1 0 1-1 1-1l2-2 2 2v-1h1c1 0 2 0 2 1 1-2 0-4 2-6h1v-2l4-4z" class="d"></path><path d="M515 147c1 0 1-1 1-1l2-2 2 2c-3 2-4 3-5 6v-5z" class="m"></path><path d="M495 228c5 7 7 14 9 21l4 12c0 1 0 3 1 5v2c0-1 0-1-1-2l-5-12c-3-9-5-17-8-26z" class="AA"></path><path d="M511 215h1c0 6 0 10-3 14-2 1-3 1-4 0-4-2-7-7-10-10l1-1v-3c1 2 3 4 5 6l2 1 3 4 1-1 2-3c0-1 0-1-1-2v-3c1 0 2 0 3-1v-1z" class="s"></path><path d="M511 216c0 2-1 4-1 5l-1 1c0-1 0-1-1-2v-3c1 0 2 0 3-1zm-15-1c1 2 3 4 5 6l2 1v3h-1l-6-7v-3z" class="x"></path><defs><linearGradient id="AE" x1="502.948" y1="305.067" x2="510.052" y2="285.933" xlink:href="#B"><stop offset="0" stop-color="#09090c"></stop><stop offset="1" stop-color="#2d292c"></stop></linearGradient></defs><path fill="url(#AE)" d="M500 278h0c1 1 1 2 1 3s1 3 1 4v1h1v-3-1c2 2 5 5 5 7l1 1c1 3 0 7 1 10s1 7 0 10v-1l-4-13c-1-2-1-4-2-6-1 2 0 5-1 7v-1-2-1c-1-2-1-4-1-5v-1-1c-1-1-1-2-1-3v-1c0-2-1-3-1-4z"></path><path d="M562 173c1-2 1-2 3-3-1 1-1 3-1 5 0 3-3 8-5 10l-5 5c-1 2-2 3-3 5l-1 1v-1c-2 4-4 8-6 11l-3-1c1-3 3-6 5-8 2-1 3-3 4-5h-1-1c1-1 2-1 2-2h-1v-1c1 0 2 0 3-1h-1v-1c1 0 4-1 5-2 3-3 4-6 4-9 1 0 1 0 1 1 0-1 1-1 1-2v-1-1z" class="f"></path><path d="M562 173c1-2 1-2 3-3-1 1-1 3-1 5 0 3-3 8-5 10l-5 5c-1 2-2 3-3 5l-1 1v-1l3-5c1-1 1-2 1-2 5-3 6-6 7-11 0-1 1-1 1-2v-1-1z" class="m"></path><path d="M512 179l2-1 2 2v1 2c1-1 1-1 2-1l-1 2h1 1l1 3c-1 1-1 1 0 2v2h-5-2c-2 0-3 1-5 0-1-1-1-2-1-2 0-3 4-8 5-10z" class="q"></path><path d="M525 245c1-1 1-1 2-1l2-1-6 36-3-1 1-10 4-23z" class="s"></path><defs><linearGradient id="AF" x1="525.182" y1="295.554" x2="504.818" y2="282.946" xlink:href="#B"><stop offset="0" stop-color="#3b393e"></stop><stop offset="1" stop-color="#69676d"></stop></linearGradient></defs><path fill="url(#AF)" d="M515 252l1-1 1 43c0 5-2 11-1 16v4c-1-1-1-2-1-3v-59z"></path><path d="M541 219c0 3-1 6-1 8-4 16-4 33-7 49-1 5-2 9-3 13-1-23 6-47 11-70z" class="h"></path><defs><linearGradient id="AG" x1="471.552" y1="222.754" x2="486.448" y2="227.746" xlink:href="#B"><stop offset="0" stop-color="#797477"></stop><stop offset="1" stop-color="#9da09e"></stop></linearGradient></defs><path fill="url(#AG)" d="M478 195h0l4 57-2-1-3-37c0-5-1-11 0-15l1-4z"></path><defs><linearGradient id="AH" x1="551.163" y1="203.284" x2="553.041" y2="208.816" xlink:href="#B"><stop offset="0" stop-color="#918f90"></stop><stop offset="1" stop-color="#aaa9aa"></stop></linearGradient></defs><path fill="url(#AH)" d="M554 190l1 2h-1v6c1 2 1 5 1 6h0c-2 3-1 6-1 9h-1c-1 0-1 1-2 1 0 1 0 1 1 1-1 2 0 5-1 7-1 1-1 3-1 4v2l-1 1h-1s-1 2-2 2v-4l1-8 2-16c0-2 1-4 1-7l1-1c1-2 2-3 3-5z"></path><path d="M546 227c1-1 1-2 2-3v-1c1-1 1-3 2-4l-1 10h-1s-1 2-2 2v-4z" class="Y"></path><path d="M547 219c1-1 1-1 1-3l1 1v1c0-3 0-5 1-8v9c-1 1-1 3-2 4v1c-1 1-1 2-2 3l1-8z" class="AB"></path><path d="M554 190l1 2h-1c-1 1-1 1-1 3 0 3 0 5-1 8h0c-1 2-1 5-2 7-1 3-1 5-1 8v-1l-1-1c0 2 0 2-1 3l2-16c0-2 1-4 1-7l1-1c1-2 2-3 3-5z" class="AE"></path><path d="M553 195c0 3 0 5-1 8h0l-1-1c0-3 0-5 2-7z" class="Y"></path><path d="M515 252v-9h1c0 5 0 11 1 16l1 7c1-1 1 0 2-1h0c0 1 1 2 1 3l-1 10 3 1v5c-2 1-2 2-3 4v2 1l-1 6c-1 1-1 3-1 4v2c0 1 0 3-1 4l-1 3c-1-5 1-11 1-16l-1-43-1 1z" class="p"></path><path d="M520 278l3 1v5c-2 1-2 2-3 4v2c-2-2-1-6-1-8v-2h1v-2z" class="x"></path><path d="M516 106a104.13 104.13 0 0 1 12 5h-1c-3-1-8-4-11-4v2c2 0 3 1 4 1l2 1v1l2 2c2 0 2 2 3 3 1 2 4 4 3 6 0 1-1 2-1 3l-1-1 1-2v-1l-1 1-1-1 1-1-1-1c-1-1-2-2-4-2l-1 1 1 1v3c1 0 2 0 3 1-1 1-2 2-3 2-2-1-2 0-4 0-1 1-2 3-3 3h-1v-1-7c1-1 1-4 1-5l-1-3v-7h1z" class="m"></path><path d="M515 106h1c-1 2-1 3 0 4l2 1h0l-1 1 1 1c2 1 3 3 5 5l-1 1 1 1v3c1 0 2 0 3 1-1 1-2 2-3 2-2-1-2 0-4 0l1-3 1-1v-2h1c0-2-1-2-2-3-2 0-2-1-3-2l-1 1-1-3v-7z" class="x"></path><path d="M516 116l1-1c1 1 1 2 3 2 1 1 2 1 2 3h-1v2l-1 1-1 3c-1 1-2 3-3 3h-1v-1-7c1-1 1-4 1-5z" class="s"></path><path d="M520 123l-1 3c-1 1-2 3-3 3h-1v-1c0-1 1-1 1-2 2-2 2-2 4-3z" class="AA"></path><path d="M529 165l6-3c1 0 3-1 4-2 1 0 2 0 3-1 1 0 1 0 2 1l-1 2 1 1c0 1-1 1-1 2-1 1-1 2-1 3-3 4-7 7-11 8h-1c-1 1-2 1-4 1l-1-2v-3h2v-1h-1l-1-1c1-2 3-3 4-5z" class="T"></path><path fill="#f0cecf" d="M527 171c3-3 7-5 11-8l1 1c0 1-1 3-2 4-2 2-6 4-10 4v-1z"></path><path d="M523 118c2 0 3 1 4 2l1 1-1 1 1 1 1-1v1l-1 2 1 1c1 1 0 2 0 4l1 3v1l-4 4v2h-1c-2 2-1 4-2 6 0-1-1-1-2-1h-1v1l-2-2-2 2s0 1-1 1v-8c0-2 1-4 0-7l1-3c1 0 2-2 3-3 2 0 2-1 4 0 1 0 2-1 3-2-1-1-2-1-3-1v-3l-1-1 1-1z" class="h"></path><path d="M518 131l1 1v3l2-1 1 1v1l-1 1h-2v2h-1c-2-1-2-1-2-2l1-1c0-2 0-3 1-5zm2 14c0-1-1-1 0-2 1 0 1-1 2-1v-1c1-1 2-3 2-4v-1l2-2c1 0 0 0 1-1v-3h2l1 3v1l-4 4v2h-1c-2 2-1 4-2 6 0-1-1-1-2-1h-1z" class="m"></path><path d="M538 198h1l1-1 1-1c2-1 4-2 5-3 1 0 2-1 2-1h1 1c-1 2-2 4-4 5-2 2-4 5-5 8l-2 2 1 2h0c0 1-1 1-1 2v2c-2 3-14 16-18 17 0 0-1 0-1-1-3-3-3-8-3-13 1 1 1 1 3 1 0 2 0 3 1 4 0-1 1-2 1-3 1-2 2-5 2-7 1-3 2-8 4-11 1 1 0 3 1 4 1-4 4-7 6-10h0l-2 4 2 1c0-1 1-2 2-3h0 1l-1-1c1-1 1-2 2-2h1l-2 5z" class="q"></path><path d="M524 217l1 3c-1 1-1 0-1 1l-2-1 2-3z" class="d"></path><path d="M528 208v5l1 1c-1 1-1 1-1 2-1 2-2 3-3 4l-1-3 4-9z" class="p"></path><path d="M533 198l2 1-6 15-1-1v-5l5-10z" class="m"></path><path d="M531 213c1-2 3-3 3-5 1-1 1-2 2-2v-1c1 0 1-1 2-1h0c-1 2-2 4-4 6-1 2-2 3-3 4-3 4-7 9-11 12 1-2 1-4 2-6l2 1c0-1 0 0 1-1s2-2 3-4l3-3z" class="L"></path><path d="M535 199c0-1 1-2 2-3h0 1l-1-1c1-1 1-2 2-2h1l-2 5v1 1c-2 4-7 7-7 13l-3 3c0-1 0-1 1-2l6-15z" class="AB"></path><path d="M538 198h1l1-1 1-1c2-1 4-2 5-3 1 0 2-1 2-1h1 1c-1 2-2 4-4 5-2 2-4 5-5 8l-2 2-1 2-1 1-1 1c-1 2-2 2-3 3 0 2 1 1 0 2-1 0-2 1-2 1h-1c6-7 10-14 16-21-4 1-6 5-8 8-1 0-1 1-2 1v1c-1 0-1 1-2 2 0 2-2 3-3 5 0-6 5-9 7-13v-1-1z" class="s"></path><path d="M488 137c-1 0-1-1-2-1l2-2 1 1c2 0 3 0 4 1v1h1c1 0 1 1 2 1h2c2 1 3 1 4 1l3 3c1 1 2 2 2 3 1 0 1-1 2-1 1 1 1 2 1 2 1 1 3 1 3 2 1 1 1 2 1 3v5l-1 1v1c-1 0-2 0-2 1v2c1 1 2 2 3 4 0 4 0 9-2 14-1 2-5 7-5 10 0 0 0 1 1 2 2 1 3 0 5 0h2c-1 5-1 11-2 16l-1 8h-1v1c-1 1-2 1-3 1v3c1 1 1 1 1 2l-2 3-1 1-3-4-2-1c-2-2-4-4-5-6-1 0-3-2-4-3l-2-1-1 1-10-18-1 1h0l-1 4c-1 4 0 10 0 15l-2-4h0v-1-4h1c0-1 0-2-1-3h-1l-1-2-1 2v-5-3c-2 0-2 0-3-1l1-8c-2-2-3-5-3-7-1-1-1-3-1-4h2c2 0 3-1 4-2 1-3 3-7 3-10l2-2s0-7 1-8c1 0 2-1 3-2h0c1-2 0-7 0-9l1-2c2-2 4-2 6-2z" class="L"></path><path d="M498 203l-1-2c-1-2-1-3 0-4 1 0 1 2 2 3l-1 3z" class="q"></path><path d="M513 191h2c-1 5-1 11-2 16l-1 1c2-5 1-11 1-17z" class="x"></path><path d="M507 145c1 0 1-1 2-1 1 1 1 2 1 2 1 1 3 1 3 2 1 1 1 2 1 3v5l-1-2c0-2 0-4-1-5h-2c-2-1-2-2-3-3v-1z" class="m"></path><path d="M499 200c2 3 2 5 4 8l-1 4-4-9 1-3z" class="s"></path><path d="M495 157l-3-3c-1-2-2-3-3-4 0-1-1-2-2-3h0v-1c2 0 3 1 4 2 1 2 3 3 3 4 1 1 0 1 1 2s2 2 2 3v1c-1-1-1-1-2-1z" class="d"></path><path d="M503 208c1 2 1 2 1 4l3 8v1l1-1c1 1 1 1 1 2l-2 3c0-1-1-2-1-2-1-4-3-7-4-11l1-4zm-15-71c-1 0-1-1-2-1l2-2 1 1c2 0 3 0 4 1v1h1c1 0 1 1 2 1h2c2 1 3 1 4 1l3 3c1 1 2 2 2 3v1l-2-2c-1 0-2-1-2-1h0 1v-1l-1-1h-1l-1-1h-6c0 1-1 1-1 1l-1-1v-1c-2 0-3 0-5-2z" class="p"></path><path d="M508 217c-1-5-1-11 1-16h0c0 1-1 3 0 4 0 1 0 0 1 1-1-2-1-4 0-6v9l1 2h0c1-1 1-2 1-3l1-1-1 8h-1v1c-1 1-2 1-3 1z" class="m"></path><path d="M512 208l1-1-1 8h-1-2v-6h1l1 2h0c1-1 1-2 1-3z" class="h"></path><path d="M487 165l1-2c4 0 9 3 12 6l2 1 1 2v1c-4-1-11-3-14-6l-2-2z" class="AG"></path><path d="M494 152c4 4 5 8 8 13l-1 1-1-1v1c0 2 2 3 2 4l-2-1c-3-3-8-6-12-6l-1 2-1-1v-2h1l1-1 1 1h3 0 2c1 1 2 2 4 2l-1-1v-2c-1-1-2-2-2-4h0c1 0 1 0 2 1v-1c0-1-1-2-2-3s0-1-1-2z" class="h"></path><path d="M491 202c-2-2-4-6-5-9 2 1 3 3 5 4l1 1h1c0 2 1 4 2 6 3 4 5 9 6 13l2 2-1 1-1 1c-2-2-4-4-5-6-1 0-3-2-4-3l-3-7v-1-3l2 1z" class="d"></path><path d="M491 202c-2-2-4-6-5-9 2 1 3 3 5 4l7 15c-3-3-5-6-7-10z" class="Y"></path><path d="M477 187c0 1 2 3 3 3 2 0 2 0 4 1 0 1 0 1 1 1l1 1c1 3 3 7 5 9l-2-1v3 1l3 7-2-1-1 1-10-18-1 1h0c-1-3-1-5-1-8z" class="s"></path><path d="M483 194c2 3 4 7 6 10v1 1h-2c-2-4-3-7-4-11v-1z" class="Y"></path><path d="M480 190c2 0 2 0 4 1 0 1 0 1 1 1l1 1c1 3 3 7 5 9l-2-1v3c-2-3-4-7-6-10 0-2-2-3-3-4z" class="m"></path><path d="M479 194c-1-1-1-2-1-2l1-1 7 16h1v-1h2v-1l3 7-2-1-1 1-10-18z" class="AE"></path><path d="M484 181c-2-2-3-4-5-5-1-1-3-1-4-3h0v-1h0c-1-2 0-6 1-8l1 4v2c0 2 1 3 2 4l2 1 2 1c1 1 2 2 4 3 0 1 0 1 1 1 1 1 2 1 2 3 1 0 0 1 1 2s3 3 4 5c0 1-1 1-1 2l-8-3c-1-1-2-1-3-1s-1-1-2-1v-1c-1-1-1-3-2-4h-1l-1 2h-1-1l1-1h1v-3c2 1 4 2 5 3 1-1 2-1 2-2z" class="p"></path><path d="M484 181c2 1 5 3 6 5 0 1 0 1 1 2-2 0-7-3-9-5h0c1-1 2-1 2-2z" class="h"></path><path d="M475 162l2-2c-1 1-1 3-1 4-1 2-2 6-1 8h0v1h0c1 2 3 2 4 3 2 1 3 3 5 5 0 1-1 1-2 2-1-1-3-2-5-3v3h-1l-1 1h1v2h0l1 1c0 3 0 5 1 8l-1 4c-1 4 0 10 0 15l-2-4h0v-1-4h1c0-1 0-2-1-3h-1l-1-2-1 2v-5-3c-2 0-2 0-3-1l1-8c-2-2-3-5-3-7-1-1-1-3-1-4h2c2 0 3-1 4-2 1-3 3-7 3-10z" class="AE"></path><path d="M470 185v1c2 2 3 2 3 5l-1 1v2c-2 0-2 0-3-1l1-8z" class="AB"></path><path d="M470 181c0-1-1-2 0-4 0 0 0-1 1-1 2 0 3 1 4 2 1 2 1 4 1 5l-1 1v-1l-1 1 1 1h0c-2-1-3-2-5-4z" class="d"></path><defs><linearGradient id="AI" x1="475.842" y1="189.556" x2="473.989" y2="200.917" xlink:href="#B"><stop offset="0" stop-color="#9d9b9c"></stop><stop offset="1" stop-color="#bab8b9"></stop></linearGradient></defs><path fill="url(#AI)" d="M473 200c1-3 0-7 1-10 1-1 1-2 1-3-2-1-4-2-5-4v-2c2 2 3 3 5 4h0l-1-1 1-1v1h1v2h0l1 1c0 3 0 5 1 8l-1 4c-1 4 0 10 0 15l-2-4h0v-1-4h1c0-1 0-2-1-3h-1l-1-2z"></path></svg>