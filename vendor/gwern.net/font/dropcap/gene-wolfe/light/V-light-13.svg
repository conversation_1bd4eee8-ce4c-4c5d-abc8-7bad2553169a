<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="102 101 849 828"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#020202}.C{fill:#cccccd}.D{fill:#ebeaea}.E{fill:#dfdede}.F{fill:#a0a2a6}.G{fill:#d2d1cc}.H{fill:#afaeae}.I{fill:#bfbebe}.J{fill:#9f9f9e}.K{fill:#e8e8e7}.L{fill:#bbb8b3}.M{fill:#838384}.N{fill:#090909}.O{fill:#313237}.P{fill:#494b52}.Q{fill:#34373d}.R{fill:#9c9894}.S{fill:#757577}.T{fill:#2d2e32}.U{fill:#64656a}.V{fill:#ebebeb}.W{fill:#57595f}.X{fill:#444853}.Y{fill:#fdfdfc}.Z{fill:#8f8273}</style><path d="M468 885l1-1 2 2-1 2c-1-1-1-2-2-3z" class="C"></path><path d="M773 160l-1-2v-1c2 0 4 1 6 1l2 2c0 1 0 1-1 1-2-1-3-1-5-2l-1 1z" class="P"></path><path d="M199 263c2 3 3 7 4 10v3c-1-2-3-3-3-5v-1-1-3c-1-1-1-1-1-3z" class="V"></path><path d="M736 215c3 1 4 1 6 2 1 0 1 0 2 1 2 0 4 1 6 1 1 0 1 1 1 1-2 1-3 0-5 1v1c-5-1-6-4-10-7z" class="E"></path><path d="M203 273l4 6c2 5 4 10 5 16-5-6-5-13-9-19v-3z" class="K"></path><path d="M773 160l1-1c2 1 3 1 5 2 3 3 7 6 10 8 1 2 2 3 3 4 1 0 4 2 4 2v1c-3-1-5-3-8-4l-3-2c-5-3-8-6-12-10z" class="N"></path><path d="M285 460c1 2 2 3 3 4s3 2 4 3c2 1 4 3 6 4v1c-2 2-3 4-4 6-4-5-8-11-9-18z" class="D"></path><defs><linearGradient id="A" x1="476.982" y1="872.309" x2="466.597" y2="881.189" xlink:href="#B"><stop offset="0" stop-color="#9ea0a4"></stop><stop offset="1" stop-color="#cecdcd"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M466 873h0l1 1h1 2 0c2 0 6 0 8-2h2c1 2 1 3 2 5l-4 2-1 1-2-1c-1 2-4 5-4 7l-2-2-1 1c-1-4-1-8-2-12z"></path><path d="M396 157h34-15-6l6 8h-1c-3 1-5 3-8 3h0c-3-4-6-8-10-11z" class="F"></path><path d="M830 201l6 3c-1 2-2 4-4 6l-7 5c-2 1-4 2-6 2h-1l-10 4-6 2h-2-1l-2 1h-1l-4 1h-3v1c-2 0-3-1-4 0-5 0-12 1-17 0l2-1c1 0 3 0 5-1v1h2l1-1v1c1 0 2 0 3-1h1v1h1 1c1 0 1 0 2-1h1 3c1-1 0-1 1 0h1c1-1 1-1 3-1h3v-1h2c1-1 1 0 2-1h2s1 0 1-1l3-1s1 0 2-1l1-1h1c3-2 7-1 11-3l2-2h1c1-1 1-1 2-1s1-1 2-2h0 0c2-1 4-3 5-5-2 0-3-1-4-3h-1z" class="E"></path><path d="M802 200h2c2 0 4 1 5 2 2 2 2 4 2 6s-2 4-4 5c-2 0-3 1-5 0-1 0-3-1-4-3-1-1-1-4 0-6 0-2 2-3 4-4zm-103-34c-2-2-3-4-2-7 0-1 0-2 1-2 3-2 9-1 13-1 1 1 1 2 2 4 0 3-1 4-3 6-1 1-3 2-4 2-3 1-5-1-7-2z" class="B"></path><defs><linearGradient id="C" x1="305.004" y1="481.45" x2="295.96" y2="486.039" xlink:href="#B"><stop offset="0" stop-color="#a4a5a8"></stop><stop offset="1" stop-color="#cdcdce"></stop></linearGradient></defs><path fill="url(#C)" d="M299 472c2 1 3 1 4 2l2 1c2 1 2 2 3 4 0 0-1 1-1 2-2 2-3 5-4 7 0 2-1 3-1 4v2c-1 1-1 2-1 3v1c-3-4-3-10-4-14l-3-6c1-2 2-4 4-6h0 1z"></path><path d="M430 157h6l-1 6c-6 1-12 1-18 2h-2l-6-8h6 15z" class="B"></path><path d="M657 158c3-2 9-1 12-1h0-8-1c1 1 3 0 5 1 1 0 1 0 2 1h1 1l1 1h1 0c1 0 2 0 3 1h1l1 1h2v1c2 1 4 0 6 1h0c0-2-2-2-3-2l-1-1h-1c-1 0-2-1-3-1h-1c-1-1-2-1-3-1h0c3-2 10 2 13 3s5 2 7 3c2 0 5 2 7 1 2 1 4 3 7 2 1 0 3-1 4-2 0 1-1 2-1 2h0l-1 1h-5-1l-2-1c-2-1-4 0-5-1-2-1-3-1-5-1h0 0c1 0 2 0 3 1h0l2 2h1l2 2h0l3 3v1l1 1v2h1 0c3 3 6 5 8 8 1 1 2 2 2 4l-15-15c-12-9-26-15-41-17z" class="C"></path><path d="M765 204h3c2 1 4 3 5 5 0 2 0 4-1 6-1 3-3 3-5 4-2 0-4 0-6-1-1-1-3-2-3-4-1-3-1-5 1-7 1-2 3-3 6-3zm-29-48c4 0 15-1 17 1v3c0 3-2 5-5 7-1 0-1 1-3 1s-5 0-8-1c-2-2-4-5-5-7 0-2 0-2 1-3l3-1z" class="B"></path><path d="M444 824h2c1 1 2 3 3 3s2 0 3 1c0 2 2 6 1 9 0 2 0 3 2 4 2 3 7 3 9 6-1 7-1 15 0 22l-1-1c-2-1-4-3-4-5-1-5-2-10-4-14-2-3-4-6-5-9-1-4-2-9-4-13l-2-3z" class="G"></path><path d="M736 183c-3 0-4 0-6-1h-2c-1 0-2-1-2-1-1 0-3 0-4-1h-2l-1-1h-1-1s-1-1-2-1h0-1c-2-1-1-1-2-1s-1 0-2-1l-2-1c-1 0-2-1-2-2s0-1 1-2l2 1 1 1 1 1h3l2 2h2s1 0 2 1h2c1 1 0 1 2 1h2c0 1 0 1 2 1h2c1 1 1 1 2 1h3c0 1 1 1 2 1h1 0 3c1 0 1 1 2 1h3 1c2 1 9 0 12 0 2 0 3 1 4 0h1 1 5c1-2 5 0 8-1h0 1c1-1 2-1 4-1h2v-1h3l1-1h2c1-1 2-1 3-1h1c-2-2-5-2-7-4v-1c3 1 5 3 8 4h0c4 1 7 2 10 3h3 0c-3 2-8 0-11 1-5 0-10 1-14 2-13 2-27 3-40 2l-8-1h0z" class="C"></path><path d="M796 176c4 1 7 2 10 3h3 0c-3 2-8 0-11 1-5 0-10 1-14 2-13 2-27 3-40 2l-8-1h0c-2-1-3-1-3-1-2-1-2-1-3-1h2c1 1 4 0 6 1 3 1 8 1 11 1 7 0 14 1 20 0 3-1 7-1 10-1 4-1 9-2 14-4 2 0 3 0 3-2z" class="I"></path><path d="M332 572c1 1 3 2 4 3s3 1 4 2c0 1 1 1 1 1 1 0 2 1 3 2l-1 2 1-1 2-1 2-2 1-1c2 0 7 1 7 3l1 1c-2 1-2 2-2 4-1 0-2 1-2 1 0 3-3 8-4 11h-1l-1 5h0l-1 1c-3-6-4-12-7-18-2-4-6-8-7-13z" class="C"></path><g class="L"><path d="M345 592h1c1 1 1 2 1 2l1 1c0 1 0 1 1 2h-1l-1 5h0c0-3-1-6-2-10z"></path><path d="M336 575c1 1 3 1 4 2 0 1 1 1 1 1 1 0 2 1 3 2l-1 2 1-1c2 3 1 8 1 11-1-3-2-5-3-7-2-3-4-5-4-8l-2-2z"></path></g><path d="M338 577c1 1 3 2 5 3v2c-1 0-2-1-2 0l1 3c-2-3-4-5-4-8z" class="D"></path><defs><linearGradient id="D" x1="352.286" y1="583.471" x2="344.26" y2="589.968" xlink:href="#B"><stop offset="0" stop-color="#909093"></stop><stop offset="1" stop-color="#b3b6b8"></stop></linearGradient></defs><path fill="url(#D)" d="M349 577c2 0 7 1 7 3l1 1c-2 1-2 2-2 4-1 0-2 1-2 1 0 3-3 8-4 11-1-1-1-1-1-2l-1-1s0-1-1-2h-1 0c0-3 1-8-1-11l2-1 2-2 1-1z"></path><path d="M349 577c2 0 7 1 7 3l1 1c-2 1-2 2-2 4-1 0-2 1-2 1 0-1 0-3-1-4-2-1-3-1-4-4l1-1z" class="M"></path><path d="M837 183c3 0 5 1 7 2l4 4c-2 5-10 9-12 15l-6-3c-2-3-4-6-4-10 1-2 2-4 4-6h1c1-2 4-2 6-2z" class="B"></path><path d="M344 179l1 1c2 1 2 2 3 4v1h0l1 2c1 1 2 3 3 5v1 1l1 1v1 1l1 1s1 1 1 2-1-1 0 1c1 1 1 2 1 3l1 1v2c1 1 1 2 1 4l1 1v-2l-1-1v-3c0-1 0-1-1-2v-2-1l-1-1v-1c0-1 0-1-1-1l-2-5v-2h-1l-1-3v-1l-1-1v-2h0l-1-1v-1c0-1-1-1-1-2 2 1 3 4 4 5 0 1 0 2 1 4v1l4 9c1 2 1 5 2 8s2 5 2 8c0 1 0 0 1 1 0 2 0 3-1 4v3c1 1 0 3 0 5 0 3-2 8-1 10l-1 8c-1 5-1 10-1 15v12c0 4 1 10 0 13l-4-6c1-1 1-1 1-2 1-3 0-7 0-11-1-9-1-20 1-29l1-5c1-2 1-3 1-5v-1c1-3 2-5 1-8 0-3-1-6-2-9-3-11-8-21-13-31z" class="C"></path><path d="M235 345c3 5 5 10 9 14 2 2 4 5 4 8 1 2 2 4 2 6 1 2 2 4 2 6 0 3 3 6 5 9 1 1 2 4 3 5 0 2 1 4 1 5 1 3 2 6 3 8 1 0 1 0 2-1l8-4c3-2 7-3 10-3 1 0 1 0 2 1h-2l-4 2-11 5c-1 2-4 3-4 5v2h-1v-1h-1c-3-6-4-13-7-19-1-3-4-6-6-10-3-5-4-11-6-16-2-6-6-10-8-16-1-2-1-3-1-6z" class="G"></path><defs><linearGradient id="E" x1="380.954" y1="641.078" x2="365.836" y2="647.577" xlink:href="#B"><stop offset="0" stop-color="#a6a6a5"></stop><stop offset="1" stop-color="#d6d6d6"></stop></linearGradient></defs><path fill="url(#E)" d="M357 627h3l2 4h0c-1 1 0 2 0 3 4 3 9 6 14 8v1h3l1 1c1 0 1 0 2-1v1c1 2 3 4 5 5h-1c-1 0-1 0-2-1 0 3 0 6-1 10-3 0-8-1-10 0h-2c1 1 0 1 1 2v2l-3-6v-1c-1-2-1-5-2-8-2-3-4-6-6-10-1-2-3-4-3-6l-1-4z"></path><path d="M357 627h3l2 4h0c-1 1 0 2 0 3-1-1-3-2-4-3l-1-4z" class="L"></path><path d="M375 157h14 7c4 3 7 7 10 11-5 2-11 5-16 9l-15-20z" class="B"></path><path d="M379 676c2 3 4 8 7 10l3 3v1c1 1 1 1 1 2v1h1c2-1 4 0 6 0h2c0 2 0 3 1 4l1 1c1 1 1 2 2 3v1l1 1h-2 0c1 1 1 1 3 2v2 1l1 1-1 1c-1-2-2-4-4-5-1-1-3 0-4 0l2 3 4 9v4l-1 1 2 1c1 1 1 3 1 4v1l-2 1c-2-2-3-3-3-5-2-3-2-7-4-10-2-5-6-8-7-13l-2-5c-1-5-4-9-7-14-1-2-1-3-1-6z" class="C"></path><path d="M389 701c2 1 3 3 5 4 2 2 2 4 3 6v1c0 2 1 5 2 7s1 3 1 5c-2-3-2-7-4-10-2-5-6-8-7-13z" class="E"></path><path d="M386 686l3 3v1c1 1 1 1 1 2v1h1c2-1 4 0 6 0-2 1-4 2-5 2l-1 1h1c2 1 4 5 5 7l-2 2-1-1c-2-2-4-5-4-8 0-2-1-2-2-4l-2-6z" class="L"></path><path d="M397 693h2c0 2 0 3 1 4l1 1c1 1 1 2 2 3v1l1 1h-2 0c1 1 1 1 3 2v2 1l1 1-1 1c-1-2-2-4-4-5-1-1-3 0-4 0h-2l2-2c-1-2-3-6-5-7h-1l1-1c1 0 3-1 5-2z" class="F"></path><path d="M832 210l-4 6h-1c-1 0-2 1-2 1-4 3-8 4-12 6-17 7-38 8-56 5l-5-1 1 1h-1c-6-1-12-5-15-9h0c0-3-2-4-4-6h-1c0 2 0 1 1 2l6 11h-1c-1-1-2-3-3-5l-6-11c3 1 5 3 7 5 4 3 5 6 10 7v-1c1 1 2 0 3 1l-1 1h0 0c2 1 2 1 3 2h1 2 1c1 0 2 0 3 1v-1h2v-1h1 1c1 1 1 1 2 1h1 2 1 0v1c5 1 12 0 17 0 1-1 2 0 4 0v-1h3l4-1h1l2-1h1 2l6-2 10-4h1c2 0 4-1 6-2l7-5zM267 421h1c1 0 2 1 4 1v2c3 1 5 3 7 5 1 0 4 1 5 1 0 3 5 7 7 8l1 1 8 5-1 1c-3 1-6 1-8 2l-2 1-1 1c-2 0-3 0-4 1-1 0-1 0-2 1l-1 2c0-3-2-7-4-10-4-4-4-10-7-16 0-2-3-4-3-6z" class="C"></path><path d="M268 421c1 0 2 1 4 1v2c3 1 5 3 7 5 1 2 2 4 4 6 2 3 5 11 8 12l-2 1c-2-1-4-2-5-4 0-1 0 0-1-1s-3-6-3-8c-1 0-1-1-1-1-1-1-1-1-1-2-1-1-2-3-3-5h-1l1-1-3-1v1h0c-1-1-1-2-2-3l-2-2z" class="I"></path><defs><linearGradient id="F" x1="296.327" y1="435.697" x2="283.216" y2="438.507" xlink:href="#B"><stop offset="0" stop-color="#969494"></stop><stop offset="1" stop-color="#afafaf"></stop></linearGradient></defs><path fill="url(#F)" d="M279 429c1 0 4 1 5 1 0 3 5 7 7 8l1 1 8 5-1 1c-3 1-6 1-8 2-3-1-6-9-8-12-2-2-3-4-4-6z"></path><defs><linearGradient id="G" x1="359.58" y1="598.496" x2="346.539" y2="604.098" xlink:href="#B"><stop offset="0" stop-color="#aaabae"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#G)" d="M353 586s1-1 2-1c1 2 1 2 3 3h1c1 1 1 1 2 1 0 2 0 6-1 9-1 1-1 1-1 3v5l1 1s1 0 1 1v1c-1 3-1 6 1 9l-1 1-3 2c-1 2-1 2-1 4 2-2 3-4 6-5h0c-1 1-1 2-2 3-1 2-1 2-1 4h-3l-1-2c-1-4-3-8-5-11h0c-2-4-4-7-5-11l1-1h0l1-5h1c1-3 4-8 4-11z"></path><path d="M358 621c0-1-1-4-1-5 1 0 2 1 2 1 1 2-1 1 1 2h1l-3 2z" class="D"></path><path d="M351 614c1 1 2 1 3 2v-1c2 4 3 5 2 10-1-4-3-8-5-11z" class="C"></path><defs><linearGradient id="H" x1="344.449" y1="250.682" x2="357.913" y2="253.64" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#f8f8f7"></stop></linearGradient></defs><path fill="url(#H)" d="M342 262v-2c2-13 7-22 16-32 0 2 0 3-1 5l-1 5c-2 9-2 20-1 29 0 4 1 8 0 11 0 1 0 1-1 2l-12-18z"></path><path d="M416 760h2 1c1 3 3 4 3 7 1 1 1 2 1 3 1 2 0 6 2 8l2 2c-1 2 1 4 2 6 0 1 4 5 5 6 1-1 1-1 2 0h1c5 5 10 9 13 15-1 0-1 1-2 1v4c0 4 0 8 2 11 0 2 1 3 2 5-1-1-2-1-3-1s-2-2-3-3h-2c-4-4-4-10-6-14-1-3-3-6-4-9s-2-6-3-8c-2-2-4-4-5-7-1-2-1-5-2-7-2-7-5-13-8-19z" class="C"></path><defs><linearGradient id="I" x1="447.193" y1="800.877" x2="438.387" y2="803.476" xlink:href="#B"><stop offset="0" stop-color="#79797b"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#I)" d="M434 792c1-1 1-1 2 0h1c5 5 10 9 13 15-1 0-1 1-2 1v4c0 4 0 8 2 11h-2c-1-1-2-1-2-2-2-2-2-11-1-14-1 0-1-1-2-1l-3-2h0l-1-1c-2-4-3-8-5-11z"></path><path d="M440 804l3-1c1 1 1 2 2 3v1c-1 0-1-1-2-1l-3-2z" class="F"></path><path d="M448 823c-2-4-3-9-1-13v-2-1l1 1v4c0 4 0 8 2 11h-2z" class="M"></path><defs><linearGradient id="J" x1="412.821" y1="712.228" x2="402.439" y2="714.943" xlink:href="#B"><stop offset="0" stop-color="#6d6e71"></stop><stop offset="1" stop-color="#a3a2a0"></stop></linearGradient></defs><path fill="url(#J)" d="M405 705c-2-1-2-1-3-2h0 2l3 1 14 9c1 1 5 2 6 3l2 1 4 3c-2 2-5 4-7 6-1 0-2 1-3 2 0 0-1 0-1 1h-1 0l-4-4c-1-1-2-1-3 0h-3-1-1c-1 1-2 2-3 2h-1c0-1 0-3-1-4l-2-1 1-1v-4l-4-9-2-3c1 0 3-1 4 0 2 1 3 3 4 5l1-1-1-1v-1-2z"></path><path d="M405 727c0-1 0-3-1-4l-2-1 1-1h1 1c1 2 2 3 2 5h0c-1 1-1 0-1 1h-1z" class="I"></path><path d="M399 708h2c1 3 2 5 2 7v2l-4-9z" class="F"></path><defs><linearGradient id="K" x1="422.569" y1="713.895" x2="411.207" y2="713.185" xlink:href="#B"><stop offset="0" stop-color="#575961"></stop><stop offset="1" stop-color="#6f7074"></stop></linearGradient></defs><path fill="url(#K)" d="M405 705c-2-1-2-1-3-2h0 2l3 1 14 9c1 1 5 2 6 3h-1c-1 1-2 1-4 1-5 0-8 3-12 6v1 1h-1c-1-1 2-5 2-7h1l1-1v-5c0-1-1-2-1-3-1 0-3-1-4-1l-3-3z"></path><defs><linearGradient id="L" x1="428.391" y1="716.794" x2="418.369" y2="726.457" xlink:href="#B"><stop offset="0" stop-color="#787878"></stop><stop offset="1" stop-color="#abaaab"></stop></linearGradient></defs><path fill="url(#L)" d="M410 724v-1c4-3 7-6 12-6 2 0 3 0 4-1h1l2 1 4 3c-2 2-5 4-7 6-1 0-2 1-3 2 0 0-1 0-1 1h-1 0l-4-4c-1-1-2-1-3 0h-3-1v-1z"></path><path d="M410 724v-1c4-3 7-6 12-6 2 0 3 0 4-1h1l2 1-19 7z" class="O"></path><defs><linearGradient id="M" x1="422.909" y1="736.249" x2="409.369" y2="741.331" xlink:href="#B"><stop offset="0" stop-color="#a7a8a9"></stop><stop offset="1" stop-color="#cfcfcf"></stop></linearGradient></defs><path fill="url(#M)" d="M410 725h1 3c1-1 2-1 3 0l4 4h0 1c0-1 1-1 1-1l2 1 1-1h5l1 2c-1-1-3-1-4-1 0 1 1 2 2 3v1l-1 1c0 2 0 3-2 5-1 0-2 1-3 1s-2 0-2-1c1 3 0 5-1 7-1 3-4 6-5 9v2c0 1 0 1 1 2 1 0 1 1 1 1h-2c-2-2-2-4-3-7h0 0v-1c-1-1-1-2-1-3-1-1-3-3-4-5-1-5-3-11-5-15l2-1v-1h1c1 0 2-1 3-2h1z"></path><path d="M422 739c-1 0-2-1-2-2-1-1-1-3 0-4 1-2 2-2 4-3 2 1 4 2 5 4 0 2 0 3-2 5-1 0-2 1-3 1s-2 0-2-1z" class="B"></path><path d="M403 729l2-1c3 3 4 9 5 13 1 1 1 3 2 4 1-3-3-10-2-12 2 2 4 10 4 13l-1 1c-1-1 0-1-1 0h0c1 1 1 1 1 4 0 0 1 2 0 2h0 0v-1c-1-1-1-2-1-3-1-1-3-3-4-5-1-5-3-11-5-15z" class="G"></path><path d="M425 778v-1c2-1 3-2 4-3h2l1 2c-1 0-1 1-2 2 0 1 0 2 1 4l1 1 1 1 3 3h0c-1 1-1 2-1 3 2 0 2-2 4-2 3-2 5-4 8-5l5-3 5-2-1 3h1l1 1h0c2 3 2 4 3 7l1 1v3c0 1 0 1 1 2v3l1 1v3c1 1 1 1 1 2v1c-2 3-4 6-6 7-1 1-2 3-4 3 0 3 0 3 2 5l-2 10c0 2-1 4-1 6l-1 1c1-3-1-7-1-9-1-2-2-3-2-5-2-3-2-7-2-11v-4c1 0 1-1 2-1-3-6-8-10-13-15h-1c-1-1-1-1-2 0-1-1-5-5-5-6-1-2-3-4-2-6l-2-2z" class="S"></path><path d="M455 830l-1-2c0-1 1-2 0-4s-1-5 0-8c0-1 0-1 1-1 0 3 0 3 2 5l-2 10z" class="Q"></path><path d="M457 778l-1 3h1l1 1h0c2 3 2 4 3 7l1 1v3c0 1 0 1 1 2v3l1 1v3c1 1 1 1 1 2v1c-2 3-4 6-6 7l3-6c1-1-1-4-1-5-2-4-2-8-4-11-1-2-4-7-7-8-1 0-2 1-3 1l5-3 5-2z" class="X"></path><path d="M425 778v-1c2-1 3-2 4-3h2l1 2c-1 0-1 1-2 2 0 1 0 2 1 4l1 1 1 1 3 3h0c-1 1-1 2-1 3 2 0 2-2 4-2 5 1 10 4 13 8 1 0 0 0 1 1l2 3v1c1 3 2 5 1 8v1c0 1-1 2-1 3-2 0-3-3-4-5l-1-1h0c-3-6-8-10-13-15h-1c-1-1-1-1-2 0-1-1-5-5-5-6-1-2-3-4-2-6l-2-2z" class="D"></path><path d="M436 792l1-1c4 2 8 4 10 7 1 2 1 2 2 3 0 1 1 2 2 3h0c0 1 0 2 1 3v1l-1-1h-1 0c-3-6-8-10-13-15h-1z" class="C"></path><path d="M425 778v-1c2-1 3-2 4-3h2l1 2c-1 0-1 1-2 2 0 1 0 2 1 4l1 1 1 1 3 3h0-2c-3-1-5-4-7-7l-2-2z" class="R"></path><path d="M295 157h11c2 3 6 9 9 10l1-1-4-8 16-1c6 7 12 14 16 22 5 10 10 20 13 31 1 3 2 6 2 9l-10-14c-3-5-7-10-11-14-8-9-19-19-30-27-4-2-9-4-13-7h0zm-112 82c-9-9-19-15-31-18-4-1-9-1-13 0h-1c-8-12-17-23-28-32l1-1 4 1c7 2 15 3 23 6 19 7 32 21 42 37 1 2 2 5 3 7z" class="B"></path><path d="M351 158l24-1 15 20c0 1 0 1-1 2l-3 2-2 1c-1 2-3 5-5 5h0c-1-1-1-1-2-1h-3c-1 0-5-3-6-3-6-3-13-5-18-8-3-2-5-3-7-5l-3-3-8-9v-1l2 1 4-1c3 0 5 0 8 1h5z" class="V"></path><path d="M377 186c-1-2-3-3-4-4h3c1 1 2 2 4 3v-1c1-1 3-1 4-2-1 2-3 5-5 5h0c-1-1-1-1-2-1zm-31-28h5c2 0 4 0 6 1h1c6 2 10 9 15 12 4 2 8 5 11 8v1c-1 0-1 0-1-1-2-1-3-2-5-3h-1-2l-1-1h0c-4 0-7-4-10-6-5-5-10-9-17-10l-1-1h0z" class="C"></path><path d="M351 158l24-1 15 20c0 1 0 1-1 2l-3 2c-1-3-3-5-5-7-1-1-5-5-7-5-1 1 0 1-1 2-5-3-9-10-15-12h-1c-2-1-4-1-6-1z" class="K"></path><path d="M381 174c1 0 1 0 2-1l6 6-3 2c-1-3-3-5-5-7z" class="G"></path><defs><linearGradient id="N" x1="363.035" y1="183.414" x2="368.376" y2="177.593" xlink:href="#B"><stop offset="0" stop-color="#aaa8a9"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#N)" d="M340 167l-8-9v-1l2 1 4-1 2 2c6 3 12 7 18 10h0l4 1 2 1-1 1 1 2 11 8h1-3c1 1 3 2 4 4h-3c-1 0-5-3-6-3-6-3-13-5-18-8-3-2-5-3-7-5l-3-3z"></path><path d="M358 169l4 1 2 1-1 1 1 2 1 1c0 1 1 1 2 2 0 1 0 1 1 2-1-1-2-1-3-1-2-1-3-3-5-3v-2c0-1 0-1 1-2h0c-1-1-2-1-3-2z" class="D"></path><path d="M340 167l-8-9v-1l2 1 4-1 2 2c6 3 12 7 18 10h0c1 1 2 1 3 2h0c-1 1-1 1-1 2v2h1l-1 1v1h0c-2-1-2-2-4-3l-2-1c-1-1-2-1-3-2h-1s0-1-1-1h-1v-1c-3-1-5-2-8-2z" class="I"></path><path d="M334 158l4-1 2 2c-2 1-5 0-6-1z" class="G"></path><defs><linearGradient id="O" x1="321.043" y1="444.29" x2="299.957" y2="461.71" xlink:href="#B"><stop offset="0" stop-color="#050301"></stop><stop offset="1" stop-color="#2a2d36"></stop></linearGradient></defs><path fill="url(#O)" d="M300 444c4 1 7 2 11 3l14 1c1 0 3 0 4 1l2 2c2 0 4-1 6 0h-5c0 2-2 3-3 4-3 4-11 10-10 15h-1l1 4v1l-1 1-2 1-3 1-3-2-1 1-1 2c-1-2-1-3-3-4l-2-1c-1-1-2-1-4-2h-1 0v-1c-2-1-4-3-6-4-1-1-3-2-4-3s-2-2-3-4l-1-2-2-2-1-3 1-2c1-1 1-1 2-1 1-1 2-1 4-1l1-1 2-1c2-1 5-1 8-2l1-1z"></path><defs><linearGradient id="P" x1="297.816" y1="450.232" x2="285.668" y2="454.935" xlink:href="#B"><stop offset="0" stop-color="#a2a1a2"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#P)" d="M299 445l-1 1c-1 1-2 2-3 2l-1 2c3 1 0 0 2 0 1 0 1 1 2 1 1 1 2 1 3 1h1c-6 2-12 3-18 4v2l-2-2-1-3 1-2c1-1 1-1 2-1 1-1 2-1 4-1l1-1 2-1c2-1 5-1 8-2z"></path><path d="M299 445l-1 1c-5 1-11 4-13 8-1 1-1 2-1 2v2l-2-2-1-3 1-2c1-1 1-1 2-1 1-1 2-1 4-1l1-1 2-1c2-1 5-1 8-2z" class="K"></path><path d="M300 444c4 1 7 2 11 3l14 1c1 0 3 0 4 1l2 2c-4 0-9-1-13 0l-16 1h-1c-1 0-2 0-3-1-1 0-1-1-2-1-2 0 1 1-2 0l1-2c1 0 2-1 3-2l1-1 1-1z" class="R"></path><defs><linearGradient id="Q" x1="323.915" y1="449.722" x2="315.817" y2="467.46" xlink:href="#B"><stop offset="0" stop-color="#14161a"></stop><stop offset="1" stop-color="#3c3f48"></stop></linearGradient></defs><path fill="url(#Q)" d="M331 451c2 0 4-1 6 0h-5c0 2-2 3-3 4-3 4-11 10-10 15h-1l1 4v1l-1 1-2 1-3 1-3-2-1 1-1 2c-1-2-1-3-3-4l-2-1c-1-1-2-1-4-2l3-3 1-1c1 0 3-2 3-2 1-2 3-3 5-4 6-4 12-7 18-11h-11 0c4-1 9 0 13 0z"></path><path d="M306 466c2 0 5 1 7 2v1c2 0 3 0 5 1l1 4v1l-1 1-2 1-3 1-3-2-1 1-1 2c-1-2-1-3-3-4l-2-1c-1-1-2-1-4-2l3-3 1-1c1 0 3-2 3-2z" class="M"></path><path d="M303 468c2 1 6 2 7 4h0v4l-1-1c-2-3-3-4-7-6l1-1z" class="S"></path><path d="M306 466c2 0 5 1 7 2v1c-2 1-2 2-3 3h0c-1-2-5-3-7-4 1 0 3-2 3-2z" class="U"></path><path d="M313 469c2 0 3 0 5 1l1 4v1l-1 1-2 1-3 1-3-2h0v-4c1-1 1-2 3-3z" class="B"></path><path d="M103 170c-2-1-8-6-9-9l5 4c1 0 2 0 3 1 3 1 7 0 10-1h-1l-9-6 73-1-8 6-23 17c-2-1-5-1-8-2l-17-3c-6-2-10-3-16-6z" class="N"></path><defs><linearGradient id="R" x1="327.512" y1="226.196" x2="353.53" y2="236.399" xlink:href="#B"><stop offset="0" stop-color="#d4d4d3"></stop><stop offset="1" stop-color="#fefefd"></stop></linearGradient></defs><path fill="url(#R)" d="M349 205l10 14c1 3 0 5-1 8v1c-9 10-14 19-16 32v2l-17-19v-2-8c1-9 7-16 14-21l8-5c1-1 1-1 2-1v-1z"></path><path d="M435 163l1-1c1 3 1 5 2 8h-1-1c-13-2-30 4-40 12-3 2-6 5-8 7-14 14-24 32-26 51l-1 6c-1-3 0-5-1-8-1-2 1-7 1-10 0-2 1-4 0-5v-3c1-1 1-2 1-4-1-1-1 0-1-1 0-3-1-5-2-8s-1-6-2-8l-4-9v-1c-1-2-1-3-1-4-1-1-2-4-4-5 0-2-3-5-4-7h0c-1-1-1-2-1-3 2 2 4 3 7 5 5 3 12 5 18 8 1 0 5 3 6 3h3c1 0 1 0 2 1h0c2 0 4-3 5-5l2-1 3-2c1-1 1-1 1-2 5-4 11-7 16-9h0c3 0 5-2 8-3h1 2c6-1 12-1 18-2z" class="L"></path><path d="M343 170c2 2 4 3 7 5 5 3 12 5 18 8 1 0 5 3 6 3h3c1 0 1 0 2 1h0l-9 12c-2 3-4 7-5 11h0v-1c-1-5-5-11-8-15-1-2-3-4-3-6l-2-3c-1-1-2-4-4-5 0-2-3-5-4-7h0c-1-1-1-2-1-3z" class="K"></path><path d="M374 186h3c1 0 1 0 2 1l-2 1-3-2z" class="I"></path><path d="M344 173c2 0 3 2 4 3 2 2 4 2 6 4 4 2 7 7 10 11 3 3 4 4 5 8-1 1-1 3-2 3-4-1-10-11-13-14l-2-3c-1-1-2-4-4-5 0-2-3-5-4-7zm86 560c2 2 7 7 8 10l2 3 1 2c3 3 6 7 9 9 1 1 2 1 3 2v1l1-1h0l6 3c4 2 8 4 13 6h1c-1 1-2 1-3 1-6 2-11 4-16 7-2 0-4 1-5 2l-6 4-8 5-3-3-1-1-1-1c-1-2-1-3-1-4 1-1 1-2 2-2l-1-2h-2c-1 1-2 2-4 3v1c-2-2-1-6-2-8 0-1 0-2-1-3 0-3-2-4-3-7h-1s0-1-1-1c-1-1-1-1-1-2v-2c1-3 4-6 5-9 1-2 2-4 1-7 0 1 1 1 2 1s2-1 3-1c2-2 2-3 2-5l1-1z" class="H"></path><path d="M431 768c2 0 3 1 5 2l-3 3v-1c0-1 0-1-1-2 0-1 0-1-1-2h0z" class="U"></path><path d="M430 774l1-1c0-1-1-3-1-4-1-1-1 0-1-2l2 1h0c1 1 1 1 1 2 1 1 1 1 1 2v1l-2 1h-2 1z" class="S"></path><path d="M440 772c0-1-1-3 0-4s2-2 4-3c2 0 3 2 4 3 1 2 1 3-1 5-1 1-1 1-2 1h-1l-4-2z" class="N"></path><path d="M419 760l4 1 2 4c2 3 2 6 5 9h-1c-1 1-2 2-4 3v1c-2-2-1-6-2-8 0-1 0-2-1-3 0-3-2-4-3-7z" class="D"></path><defs><linearGradient id="S" x1="439.141" y1="777.376" x2="433.608" y2="771.877" xlink:href="#B"><stop offset="0" stop-color="#7b7b7d"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#S)" d="M436 770c1 1 2 2 4 2l4 2c-2 2-3 4-5 5-3 1-6 1-7 4l-1-1c-1-2-1-3-1-4 1-1 1-2 2-2l-1-2 2-1 3-3z"></path><defs><linearGradient id="T" x1="444.113" y1="762.286" x2="437.6" y2="750.619" xlink:href="#B"><stop offset="0" stop-color="#242427"></stop><stop offset="1" stop-color="#45464c"></stop></linearGradient></defs><path fill="url(#T)" d="M435 760l-2-1v-1c1-3 4-5 6-7 1-2 1-2 2-3 3 3 6 7 9 9 1 1 2 1 3 2v1h-18z"></path><defs><linearGradient id="U" x1="451.648" y1="780.954" x2="452.561" y2="763.556" xlink:href="#B"><stop offset="0" stop-color="#646468"></stop><stop offset="1" stop-color="#9b9b9a"></stop></linearGradient></defs><path fill="url(#U)" d="M448 768c1-2-1-3 0-5 1-1 2-1 4 0 5 1 9 5 14 6h3 1 1c-6 2-11 4-16 7-2 0-4 1-5 2l-6 4-8 5-3-3-1-1c1-3 4-3 7-4 2-1 3-3 5-5h1c1 0 1 0 2-1 2-2 2-3 1-5z"></path><path d="M433 784l3-1c2-1 4-2 7-2l1 1-8 5-3-3z" class="W"></path><defs><linearGradient id="V" x1="435.572" y1="740.097" x2="424.426" y2="742.082" xlink:href="#B"><stop offset="0" stop-color="#6b6c6f"></stop><stop offset="1" stop-color="#989796"></stop></linearGradient></defs><path fill="url(#V)" d="M430 733c2 2 7 7 8 10l2 3 1 2c-1 1-1 1-2 3-2 2-5 4-6 7v1l2 1h-5-4-6c-2-1 0-3 0-4s-1-2-1-2l-3 3v-2c1-3 4-6 5-9 1-2 2-4 1-7 0 1 1 1 2 1s2-1 3-1c2-2 2-3 2-5l1-1z"></path><path d="M419 754h0v-1c1-2 2-3 3-4l2-3v1c1-1 1-1 1-2h0l1-1c1 1 1 2 2 2 0 2-1 2-1 3-1 0-1 0-2 1v3 1c-2 2-4 3-5 5v1c-2-1 0-3 0-4s-1-2-1-2z" class="J"></path><path d="M425 754c2-1 3-2 5-4 0-1 0-1 1-2h0l1-2 1 1v3c-2 3-6 7-7 10h-6v-1c1-2 3-3 5-5z" class="M"></path><path d="M438 743l2 3 1 2c-1 1-1 1-2 3-2 2-5 4-6 7v1l2 1h-5-4c1-3 5-7 7-10 2-2 3-4 5-7z" class="W"></path><path d="M440 746l1 2c-1 1-1 1-2 3-2 2-5 4-6 7v1l2 1h-5l-1-1 11-13z" class="P"></path><defs><linearGradient id="W" x1="167.419" y1="191.146" x2="146.406" y2="245.432" xlink:href="#B"><stop offset="0" stop-color="#ababac"></stop><stop offset="1" stop-color="#d1d3d8"></stop></linearGradient></defs><path fill="url(#W)" d="M103 170c6 3 10 4 16 6l17 3c3 1 6 1 8 2 2 3 8 6 10 7 5 1 10 6 13 9l8 8h1l3-1c3 2 5 4 7 6 1 1 1 3 3 5 1 1 4 2 6 3 0 2-1 5 0 7l-4 8c1 3 4 7 6 10l7 16c2 2 4 6 4 8l1 3-1 1v1l-1 1h-1l-1 1h0c1 1 2 2 2 4v1l-4-6c-1-3-2-7-4-10-1-2-2-5-4-8 0 1-1 1-2 1l-1-1c-1-2-2-5-2-6-2-4-4-7-7-10-1-2-2-5-3-7-10-16-23-30-42-37-8-3-16-4-23-6 1 0 0 0 1-1v-4c-6-4-9-8-13-14z"></path><path d="M208 267l1 3-1 1v1l-1 1h-1v-2c0-1 1-2 2-4z" class="G"></path><path d="M191 245h1c1 1 1 1 1 2 3 7 8 14 11 21h1l1 3v2l-1 1h0c1 1 2 2 2 4v1l-4-6c-1-3-2-7-4-10-1-2-2-5-4-8l-4-10z" class="D"></path><path d="M181 230c1 1 0 0 1 2 0 3 5 5 6 7l1 3 2 3h0l4 10c0 1-1 1-2 1l-1-1c-1-2-2-5-2-6-2-4-4-7-7-10-1-2-2-5-3-7l1-2z" class="C"></path><path d="M176 205l3-1c3 2 5 4 7 6 1 1 1 3 3 5 1 1 4 2 6 3 0 2-1 5 0 7l-4 8c-1-4-3-8-5-11-2-6-5-13-10-17z" class="O"></path><path d="M116 184c8 2 17 3 25 7 6 2 12 6 17 9 5 4 11 8 14 14 3 4 7 9 8 15l1 1-1 2c-10-16-23-30-42-37-8-3-16-4-23-6 1 0 0 0 1-1v-4z" class="E"></path><path d="M625 213c-1 0-2-1-2-2-2-1-5-3-7-4-14-7-27-5-41 0l4-36h7c5 0 11 0 16 1 11 3 25 8 32 17v1h1c2 2 4 4 6 7 0 2 0 3-1 5v2c0 2-1 4-1 7-1 2-2 4-3 5v4l-2 1c-1-1-1-1-1-2-2-2-5-6-8-6z" class="B"></path><path d="M458 191c5 4 8 11 12 15l13 17c-1 0-2-1-3-2s1 1 0-1l-2-1c0-1-1-2-2-3v-1l-2-1h0c0 2 1 3 1 5v1c1 1 0 1 0 2v4l-1 1c0 1-3 4-4 5h-1c-1 1-2 2-3 2h-1l-1 1h-1-1l-1 1c-2 0-2 0-2 1-2 0-3 0-5 1h0-1c-1 1-1 1-2 1l-1 1v-1l-1 1-1 1-1-1v1h-2l-1 1h0c-1 1-5 3-5 4-1 0-1 1-2 2h-1l-1 1-1 1-1 1v1c-1 0-1 0-1 1l-1 1-1 1v1c-1 0-1 0-1 1l-1 1v1l-1 1-1 2h0l-1 3c-1 0-1 1-1 2h0c0 1-1 1-1 2h0v1c-1 1-1 0-1 1v1h0v1 1h-1v1 1h0v1c0 1 0 2-1 3 0 1 1 2 0 3 0 2 0 5-1 6 0 1 0 2 1 3v2 1 2 1c0 1 0 3 1 4 0 1-1 1 0 2 0 1 0 2 1 3v3 1h1c0 1-1 2 0 3 0 1 0 1 1 2v2 1h1v2 1l3 8c1 2 2 3 2 5l2 7c1 1 1 2 1 3l1 2h0c1 2 2 4 2 6 1 2 2 4 3 7 0 1 1 1 1 3l1 2h0c2 3 3 7 4 10 1 4 3 8 5 12l1 2v1l4 8v2l2 4 1 3 1 1 5 13 1 2 1 3 2 4 1 3 1 1 3 8 1 2 3 6v2l1 1c0 1 1 2 1 3l1 2 1 4h1c0 1 1 2 1 4l1 2 3 6v2l5 10 1 2 1 4 2 3c2 4 4 8 6 13 2 3 3 7 5 11 0 1 1 2 1 3l1 1 1 3h0l1 1c1 2 1 4 2 6 1 1 1 2 2 3 0 1 0 1 1 2v2l3 5v1c1 1 1 0 1 1s1 2 1 3l1 1v1h0l1 2 1 1c0 1 0 1 1 2s1 2 1 4l3 4v2l1 1v1c1 1 1 1 1 2 1 1 1 1 1 2 1 1 1 1 1 2l1 1 3 7h0c1 1 0 0 1 2 0 0 1 1 1 2 1 1 1 1 1 2v1l1 1c1 2-1-1 1 1v1l3 6c1 1 2 2 2 3l2 4c1 1 1 1 1 2s0 0 1 1c0-1 0-2 1-4v-1c1-2 0 1 1-1v-2-1l1-1c0-1 0-2 1-3 0-1 0-3 2-4h0l-9 28-1-2h1v-1-1c0-1 0-1-1-2v-2l-1-1v-1l-2-3-1-3c-1-1-2-2-2-3l-3-5-1-1c0-1 0-1-1-2v-2l-8-15c-1-2-1-3-2-4l-1-2c0-1-1-2-1-3l-1-2c-1-2-1-3-2-4v-1l-1-1-1-2v-1h-1c0-2 0-1-1-2 0-1 0-2-1-3l-3-8-2-4-1-4-2-3-1-3v-1c-1-1-2-2-3-4-2-4-3-9-5-13-1-1-2-3-2-4v-1l-2-2-4-11-1-2c-1-1-1-2-2-3v-1l-1-2-2-6-1-2-3-6-1-4-2-3-1-2c0-1-1-2-1-3s-1-3-2-4l-1-2c0-2-1-3-1-4v-1l-1-1-3-6v-1l-1-2-1-4c-1-1-1-2-2-2v-2-1l-1-1c-1-1-1-2-1-3l-1-2-5-12-1-1c0-2-1-2-1-4l-1-1v-1l-1-1-4-11v-1l-1-1c-1-3-3-6-3-8-3-8-8-16-10-24-1-3-2-5-3-8v-1l-1-2c0-1-1-2-1-3l-2-4v-1s-1-1-1-2l-1-4c0-1-1-2-1-3v-1l-1-1c0-1-1-2-1-3v-1l-1-1v-2l-2-5v-1c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-3c-1-1-1-1-1-2-1-2 0-4 0-5-1-2-1-9-1-11 1-1 1-5 1-6 1-2 0-3 0-4 1-1 1-1 1-2v-2c1-1 1-1 1-2v-1c1-1 1-2 1-3h1l3-7c0-1 0-2 1-2l1-2c1-1 1-2 2-2l1-1c0-2 1-2 2-3 1-2 2-4 4-5 1 0 1-1 2-1l1-1h1c1-1 1-2 3-2 0-1 1-1 1-1l1-1c1 0 1 0 2-1h1c0-1 1-1 2-1s2-1 3-1h1v-1l1 1c0-1 0-1 1-1s2-1 2-1h2s1-1 2-1h1c1 0 2-1 2-1h1 1c1-1 1-1 2-1h1c0-1 1-1 2-1 1-1 1-2 2-3h0l1-1v-1-3-3h-1l-1-1c-2-10-9-15-12-23v-1z" class="G"></path><defs><linearGradient id="X" x1="175.963" y1="174.113" x2="335.287" y2="199.203" xlink:href="#B"><stop offset="0" stop-color="#9c9b99"></stop><stop offset="1" stop-color="#f6f6f5"></stop></linearGradient></defs><path fill="url(#X)" d="M175 160c1-1 3-2 4-3h6 13 68l19 1c3 0 7 0 10-1h0c4 3 9 5 13 7 11 8 22 18 30 27 4 4 8 9 11 14v1c-1 0-1 0-2 1l-8 5c-7 5-13 12-14 21v8 2l-13-11-20-12 1-1-1-2c-1-2-4-2-6-4-4-2-9-4-14-6h-2c-9-2-17-5-25-7-2 0-2-1-4-1h-2c1 1 1 1 1 2-1-1-2-1-3-1h0l-1-1h-3 0c-1 0-2 0-3-1l-6-1c-2 0-3 1-4 0h-8l-20 1h-5c-6 1-10-1-14-4l-5-1-1 1c-1-1-2-2-3-2 1 1 3 3 3 5-3-3-8-8-13-9-2-1-8-4-10-7l23-17h3c1-1 3-3 5-4z"></path><path d="M201 161c3 0 10-1 13 1 1 0 2-1 3 0 1 0 2 0 3 1h0 3 4c1 1 3 1 4 1 0 1-1 3-1 4h-2l1-1v-1c-2-2-19-3-23-3-2 0-3-1-5-2z" class="J"></path><path d="M233 162l-26-2h0c2 0 5-1 7 0h1 5l1 1c3 0 9-1 12 0h1c1 0 3-1 4 0 3 2 7 1 10 1h1 1 1c2-1 3-1 4-1 2-1 5 1 7 0h0c0 1 0 1-1 1h3l2-1h2 4l-18 3-7 1-2 1h-1c3-2 6-3 9-3-4 0-10-1-14 0v-1c-2-1-4-1-6 0z" class="C"></path><path d="M233 162c2-1 4-1 6 0v1c4-1 10 0 14 0-3 0-6 1-9 3h1l2-1 7-1 18-3h1c3 1 7 1 11 1-3 2-10-1-14 0l-7 1-8 2c-2 0-5 0-7 1-2 2-5 4-8 6l-1-1v2c2 0 13-1 14 0l1 1c0 2 0 2-1 3 1 0 1 1 2 1s1 1 2 1c-2 0-3-1-4-2v-2-1c-5 0-12 0-17-1-1 0-4-1-4-2 0 0 1 0 2 1 1 0 2 0 3-1h-3c-1-1-2-2-4-3 0-1 1-3 1-4l2-2z" class="F"></path><path d="M233 162c2-1 4-1 6 0v1c1 2 1 3 1 5l-3 3h-3c-1-1-2-2-4-3 0-1 1-3 1-4l2-2z" class="B"></path><defs><linearGradient id="Y" x1="261.873" y1="198.182" x2="279.59" y2="185.013" xlink:href="#B"><stop offset="0" stop-color="#bab8b8"></stop><stop offset="1" stop-color="#f6f6f2"></stop></linearGradient></defs><path fill="url(#Y)" d="M243 176c3 0 5 0 8 1h0 2c1 1 2 2 4 2v1c1 0 2 1 3 1 2 1 5 3 7 4 1 1 2 1 3 1l1 1h0l19 10-1 1v1c-1 3-1 6 0 9s1 5 3 8v1c-1-2-4-2-6-4-4-2-9-4-14-6h-2l-13-11c-3-3-15-15-15-20h1z"></path><path d="M257 196c-3-3-15-15-15-20h1l2 1h0 0c2 0 3 3 4 5h1v1 1l4 4h1l3 3c0 2 2 2 3 3 1 2 3 4 5 5 1 1 1 2 2 3h0l3 3 1 2h-2l-13-11z" class="H"></path><path d="M250 182v1 1l-1-1 1-1z" class="L"></path><defs><linearGradient id="Z" x1="315.776" y1="209.679" x2="340.511" y2="221.359" xlink:href="#B"><stop offset="0" stop-color="#d1d0d0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Z)" d="M338 191c4 4 8 9 11 14v1c-1 0-1 0-2 1l-8 5c-7 5-13 12-14 21v8 2l-13-11v-1c0-1-1-2-1-3-1-6 2-15 6-20v-1c3-4 6-7 10-10 2-1 5-5 7-5h3l1-1z"></path><path d="M175 160h5c2-1 6 0 9 0 2 0 3 0 6 1h6c2 1 3 2 5 2 4 0 21 1 23 3v1l-1 1h2c2 1 3 2 4 3h3c-1 1-2 1-3 1-1-1-2-1-2-1 0 1 3 2 4 2 5 1 12 1 17 1v1 2h-2 0c-3-1-5-1-8-1h-1c0 5 12 17 15 20l13 11c-9-2-17-5-25-7-2 0-2-1-4-1h-2c1 1 1 1 1 2-1-1-2-1-3-1h0l-1-1h-3 0c-1 0-2 0-3-1l-6-1c-2 0-3 1-4 0h-8l-20 1h-5c-6 1-10-1-14-4l-5-1-1 1c-1-1-2-2-3-2 1 1 3 3 3 5-3-3-8-8-13-9-2-1-8-4-10-7l23-17h3c1-1 3-3 5-4z" class="G"></path><path d="M175 160h5c2-1 6 0 9 0 2 0 3 0 6 1h6c2 1 3 2 5 2 4 0 21 1 23 3v1l-1 1c-10-3-20-4-30-4-9-1-18-1-26 0h-2c1-1 3-3 5-4z" class="F"></path><path d="M161 170c2-1 5-3 6-3 1-1 5 0 6 1l16 2c1 1 1 1 3 2 1 1 1 2 3 2 1 0 1 0 1 1-8 1-19 0-27-1l-8-1-1-1 1-2z" class="M"></path><path d="M161 170h1c3 2 6 2 9 3h0c-1 1-2 0-2 1l-8-1-1-1 1-2z" class="S"></path><defs><linearGradient id="a" x1="227.603" y1="196.571" x2="238.276" y2="182.589" xlink:href="#B"><stop offset="0" stop-color="#919090"></stop><stop offset="1" stop-color="#cac9c7"></stop></linearGradient></defs><path fill="url(#a)" d="M189 170c5 0 10 0 15 2 5 1 11 2 16 4l4 1c1 0 2 0 3 1h0c5 1 9 4 14 6 5 3 11 12 16 12l13 11c-9-2-17-5-25-7-3-2-7-4-11-6l-21-12c-4-2-9-3-13-6-1 0-1-1-2-1v-1l-2 1c0-1 0-1-1-1-2 0-2-1-3-2-2-1-2-1-3-2z"></path><path d="M162 190l-10-6c-1-1-3-2-4-3h-1c1-1 2-2 3-2l6-3h9c2 1 5 3 8 3 7 2 13 2 20 4 3 1 6 2 10 3 5 1 10 3 14 5 5 2 9 4 13 6v1l-6-1c-2 0-3 1-4 0h-8l-20 1h-5c-6 1-10-1-14-4l-5-1-6-3z" class="R"></path><path d="M163 180h5c2 2 2 2 2 5 0 1 0 2-1 3-2 1-4 1-6 0-1-1-1-2-1-3 0-2 0-3 1-5z" class="B"></path><path d="M196 192c9-1 19 3 28 5-2 0-3 1-4 0h-8l5-1h-12c-3 0-8 1-11-1l3-1-1-2z" class="D"></path><path d="M169 188c5-1 11 1 16 1h0c4 1 8 1 11 3l1 2-3 1-15-4c-2-1-9-1-10-3z" class="C"></path><path d="M185 189c4 1 8 1 11 3l1 2c-4 1-5-1-8-2-2-1-4-1-5-2l1-1z" class="K"></path><path d="M162 190c3 0 6 0 9 1 2 1 5 0 8 0l15 4c3 2 8 1 11 1h12l-5 1-20 1h-5c-6 1-10-1-14-4l-5-1-6-3z" class="F"></path><path d="M183 196c2 0 4 0 6 1h2l1 1h-5c-1-1-3-1-4-2z" class="R"></path><path d="M173 194c2 1 4 1 6 1 2 1 3 1 4 1 1 1 3 1 4 2-6 1-10-1-14-4z" class="J"></path><defs><linearGradient id="b" x1="296.742" y1="222.676" x2="245.679" y2="293.196" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#303238"></stop></linearGradient></defs><path fill="url(#b)" d="M167 197c0-2-2-4-3-5 1 0 2 1 3 2l1-1 5 1c4 3 8 5 14 4h5l20-1h8c1 1 2 0 4 0l6 1c1 1 2 1 3 1h0 3l1 1h0c1 0 2 0 3 1 0-1 0-1-1-2h2c2 0 2 1 4 1 8 2 16 5 25 7h2c5 2 10 4 14 6 2 2 5 2 6 4l1 2-1 1 20 12 13 11 17 19 12 18 4 6 3 14c1 4 3 9 3 13-3-4-7-7-10-10s-5-5-8-7c-2-2-4-4-6-5l-24-14-4-2-6-3c-11-5-22-8-34-10l-16-1-23 1-15 4-3 1-5 2-1 1-1-3c0-2-2-6-4-8l-7-16c-2-3-5-7-6-10l4-8c-1-2 0-5 0-7-2-1-5-2-6-3-2-2-2-4-3-5-2-2-4-4-7-6l-3 1h-1l-8-8z"></path><path d="M226 230c2-2 3-5 5-6s3-1 4 0h0v1l1 1-1 1v1c-3 0-5 0-7 2h-2z" class="O"></path><path d="M301 259h2l1 1c0 2-1 5-3 7h-1c-2 0-3-1-4-2v-2c2-2 3-3 5-4z" class="W"></path><defs><linearGradient id="c" x1="218.582" y1="229.195" x2="212.607" y2="235.542" xlink:href="#B"><stop offset="0" stop-color="#3a3c43"></stop><stop offset="1" stop-color="#55565b"></stop></linearGradient></defs><path fill="url(#c)" d="M216 231c2-1 4-1 7-1-2 3-3 5-6 7h-3c-2-1-3-1-3-2-1-2-1-2 0-3s3-1 5-1z"></path><path d="M306 272l1-13c1-1 1-1 2-1 3 3 3 6 3 10 1 2 0 4 0 7l-6-3z" class="P"></path><path d="M261 240c2 0 3 0 4 1 0 2 0 3-1 5s-3 3-6 4h-2c-1-1-2-1-2-3 0-1-1-2 0-4 2-2 5-3 7-3z" class="Q"></path><defs><linearGradient id="d" x1="236.431" y1="229.882" x2="225.346" y2="231.887" xlink:href="#B"><stop offset="0" stop-color="#3e4043"></stop><stop offset="1" stop-color="#57565a"></stop></linearGradient></defs><path fill="url(#d)" d="M236 226h0l1 1 1-1h1 1l-1-1c1-1 0-1 1 0 1 0 2 1 3 2v4 2h0c-1 1-2 3-2 4v1c0 1-1 2-1 4l-1 1v1 1c-1 2-1 4-2 5 0 1-2 1-2 2 0-5 1-8 2-12 1-3 3-5 3-8 0-2-1-3-2-4l-2 2c-1 0-5 2-5 3-2 2-4 3-6 6 0-3 1-6 1-9h2c2-2 4-2 7-2v-1l1-1z"></path><path d="M256 261h8c4-7 7-13 12-20h1c2 3 3 6 3 9 0 4-5 8-8 11v1l-16-1z" class="U"></path><path d="M236 230l2-2c1 1 2 2 2 4 0 3-2 5-3 8-1 4-2 7-2 12-2 2-2 3-2 6h-1 0c-1 0-2 1-2 2v1 1h0 3l-15 4v-1l9-12 6-12c1-3 3-5 3-8v-3z" class="P"></path><path d="M199 217c1-2 3-3 4-5 1 0 2 1 3 2l1 2v1c1 1 1 1 1 2l1 3c-1 0-2 1-2 2h0c0 2 0 3-1 4 0 1 1 3 1 4v1c0 2 0 4 1 6l1-1h0c1 2 0 6 0 8v1c2 2 1 9 1 12h0l-1-1v-1c0-1 0-1 1-2h-1v-2c-1 1-2 4-2 6h1v1c0 1 1 2 1 4l1 1c0 1 0 1-1 2 1 1 1 1 1 2l-1 1-1-3c0-2-2-6-4-8 1-2 1-4 0-6-1-3 0-6 0-9-1-3-1-5-1-7 0-5 1-10 0-15 0-2-2-4-4-5z" class="O"></path><defs><linearGradient id="e" x1="198.677" y1="231.206" x2="191.884" y2="232.243" xlink:href="#B"><stop offset="0" stop-color="#7b7b7d"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#e)" d="M199 217c2 1 4 3 4 5 1 5 0 10 0 15 0 2 0 4 1 7 0 3-1 6 0 9 1 2 1 4 0 6l-7-16c-2-3-5-7-6-10l4-8c1-3 3-5 4-8z"></path><path d="M236 230v3c0 3-2 5-3 8l-6 12-9 12v1l-3 1c-1-4-1-9 0-12 0-3 0-6 2-8 1-2 3-2 6-3l1-1c2-1 1-2 1-4 2-3 4-4 6-6 0-1 4-3 5-3z" class="M"></path><path d="M231 233l1 1c-1 5-4 8-6 12-4 5-6 9-10 13h-1v-4h0c0-3 0-6 2-8 1-2 3-2 6-3l1-1c2-1 1-2 1-4 2-3 4-4 6-6z" class="R"></path><path d="M215 255c0-3 0-6 2-8 1-2 3-2 6-3v1l-1 2v1h0c-1 1-2 2-2 3h-1c-1 2-3 4-3 6l-1-2h0z" class="F"></path><defs><linearGradient id="f" x1="223.301" y1="180.677" x2="233.199" y2="230.323" xlink:href="#B"><stop offset="0" stop-color="#c8c6c8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#f)" d="M167 197c0-2-2-4-3-5 1 0 2 1 3 2l1-1 5 1c4 3 8 5 14 4h5l20-1h8c1 1 2 0 4 0l6 1c1 1 2 1 3 1h0 3l1 1h0c1 0 2 0 3 1 0-1 0-1-1-2h2c2 0 2 1 4 1 8 2 16 5 25 7h2c5 2 10 4 14 6 2 2 5 2 6 4l1 2-1 1c-34-17-75-26-113-16l-3 1h-1l-8-8z"></path><path d="M846 233v1c-2 2 0 0-2 1l-2 3h0c1 0 1 0 2 1 1 0 3-1 4 0h12c2 0 3 0 5 1 1 0 4-1 6 0 1 0 1 0 3 1h2c1 0 1 0 2 1h1c1 0 2 1 3 1 2 2 4 4 4 7l1 1v4h0 0v1h-1v1 1 1h0l-1 1v1h0v1h-1v1h0v1l-1 1c-1 1-1 2-2 4h0v1h0-1l1 1-2 1h1l-1 1h0l-1 1h0v1l-1 1v1l-1 1-1 2h0v1h-1v1h0l-1 1c-1 1 0 2-2 4h0c0 1 0 1-1 2h0 0c-1 1-2 4-2 5l-1 1h0l-1 1c-1 2-1 5-3 7h0 0c-1 1-2 3-2 4l-1 1v1c0 1-1 1-1 2h0l-1 1h0v1s-1 0-1 1h0v2h-1v1l-1 1v2s-1 1-1 2h-1v1h0l-1 1h0v1c-1 1-1 2-2 3v2h-1v1h0l-1 1h0v1c0 1-1 1-1 2v1l-1 1-1 2-1 2v1h0c0 1 0 0-1 1v2l-1 1h0c0 2 0 0-1 2h0c0 1-1 2-1 3h0l-1 1h0 0c0 1 0 1-1 2h0v1c-1 1-1 1-1 2-1 2-1 4-2 5-1 2-1 1-1 2s-1 2-1 2v1 1h-1c0 1 1 0 0 1l-1 2-1 2v1c-1 1 0 1-1 2h0c0 1 0 0-1 1v1h0c-1 1-1 2-1 3l-1 1-1 1c0 1 0 2-1 2v1 2h-1v1c-1 3 0-1-1 2h0c0 1-1 2-1 3l-1 1v2l-2 2h0v1 1l-1 2c-1 1 0 1-1 2 0 1 0 0-1 1h0c0 1 0 2-1 3h0v1h0 0c-1 1-1 1-1 2v1h-1v2l-1 1c0 1-1 2-1 4h-1v1 1h-1v1 1c0 1 0 1-1 1v2l-1 2-1 1v2h-1v1 1l-1 1h0v1l-1 1v1c-1 1 0 1-1 2v1l-1 2v1h-1v1 1l-1 1h0c0 1-1 2-1 3h0 0c-1 1 0 1-1 2 0 1 0 0-1 1h0v1h0l-1 2h0v1h0c0 1 0 1-1 2h0c0 2 0 2-1 3h0c0 1-1 1-1 2h0c0 1-1 2-1 3h0l-1 1h0v1h0l-1 1h0c0 1 0 2-1 3 0 1-1 2 0 2l-1 1h0l-1 2v2l-1 1v1c-1 1 0 1-1 2v1l-1 1v1 1l-1 1h0c0 1 0 1-1 2v1h0l-1 2v1h-1v2 1h-1v2s-1 1-1 2c-1 1-1 2-2 3h0v1h0v1l-1 1v1c-1 1 0 1-1 2l-1 1v1l-1 2c0 1 0 2-1 3l-1 2h0v2l-1 1v1 1l-1 1v1h0 0c-1 1-1 1-1 2v1l-1 1v1h0 0c-1 1-1 2-1 2l-1 1v2l-1 1v1c0 1 0 0-1 2 0 0 0 1-1 1v1l-1 2v1c-1 2-1 3-2 5 0 1-1 1-1 2l-1 1v2s0 1-1 1v1 1l-1 1v2l-1 1v1l-1 2v1l-1 1c0 1-1 2-1 2v2l-1 1c0 1-1 2-1 2v1c-1 1 0 1-1 2v1c-1 1-1 3-2 4l-1 2v1h0l-1 1h0 0c0 1-1 2-1 3v1l-2 5-1 1h0v1l-1 1v2c-1 0-1 1-1 1 0 1-1 2-1 2v1 1l-1 1-1 2v1h0c0 1 0 0-1 1v1 1h-1v1 1c-1 0-1 1-1 2s-1 1-1 3l-1 2-1 1v2l-1 1c0 1-1 1-1 2s0 2-2 4h0v1h0l-1 1h0v2h0l-1 1h0v1h0v1h-1v1 1l-1 1h0v1l-1 2c0 1-1 1-1 2s0 1-1 2v1h0l-1 1v1 1l-1 1h0v1c-1 0-1 1-1 2h0 0l-1 2-1 1v1 1c-1 1-1 3-2 4v1l-1 1v1s0 1-1 2c0 0 0 1-1 3-1 1 0 2-2 4h0c0 1 0 1-1 2 0 1-1 2 0 3h-1l-1 2v2c-1 0-1 1-1 1 0 1-1 2-1 3v1l-1 1h0v1l-1 1v2h-1c0 1 1 0 0 1l-1 2h0v1c-1 1 0 1-1 2h0l-1 1h0v1c-1 1-1 1-1 2v1c-1 0-1 1-1 1v2h-1v1 2l-1 1c0 1-1 2-1 3l-1 1v1 1h-1c0 2 1 0 0 2l-1 1c0 1 0 1-1 2h0 0c0 1 0 1-1 2h0v1h0c-1 1-1 2-2 3v2l-2 3c0 1 0 1-1 2h0v1c-1 1-1 1-1 2s-1 2-1 3c-1 2-2 3-2 4h0v1c-1 0-1 1-1 2-1 0-1 1-1 2v1h-1v2l-1 2-1 1c0 1 0 1-1 2h0 0c0 1 0 2-1 2v1c0 1-1 2-1 3v1h-1v1l-1 1v2c-1 1-1 2-2 3h0v1h0 0c-1 2-2 3-2 5l-1 1v1c0 1 0 1-1 1v2l-1 1v1c-1 1-1 2-2 3h0 0c0 1 0 1-1 2h0v1c-1 1-1 1-1 2v1l-1 1v1c-1 1 0 1-1 2l-1 3c-1 0-1 1-1 2h0 0c-1 1-2 2-2 4h0l-1 2-1 1v1 1c-1 1-1 1-1 2l-2 4-1 1c-1 1-1 2-1 3l-1 1v2h-1c0 1-1 2-1 3-1 1-1 0-1 1-1 3-2 4-3 6v1h-1v2c-1 1-2 2-2 3-1 2-2 3-3 5 0 1 0 1-1 2l-1 1c0 1 0 1-1 2l-1 1v1c-1 1-2 3-3 5l-1 1h0c0 1-1 1-1 2l-1 1h0c-1 1-1 3-2 4-1 0-1 1-2 2l-1 2c-1 0-1 1-2 2l-1 2c-1 0-1 1-2 2-1 2-3 3-4 5 0 1 0 1-1 2 0 0-1 1-1 2l-1 1-2 4h-1c-1 2-2 4-4 4v1c-1 1-3 1-4 1h-1v1l2 1v-1l1 1h2c-1 1-2 0-3 1h-1c-1-1-1-1-2-1 0 1-1 0-1 0h-3v1h3c1 1 2 1 3 1-3 0-6-1-9-1l-14-1c-7-1-13-1-20-2 4-1 8-1 12-1l12 1c3 0 6 0 8-1v-1h3 2v-1h2c1 0 3-3 4-4 0-1 1-2 1-2 1-1 1-2 2-3 0-1 1-2 2-2l1-2 3-4 3-4 2-2c0-1 1-2 1-3 1-1 1 0 1-1l2-2c2-3 3-6 5-9 1-2 3-5 4-7s2-3 3-5l4-8c1-1 1-1 1-2v-1c1 0 1-1 1-2 1 0 1-1 2-2v-1c0-1 1-2 1-3h0l1-1c1-2 1-3 2-5l1-1v-2l1-1 1-2v-1-1c1 0 1-1 2-2v-1-1l2-4 1-1v-1-1l1-1v-2l1-1c0-1 1-2 1-3l1-2c1-1 2-3 2-5h0l1-1v-1s1-1 1-2l1-2v-2c1 0 1 0 1-1l1-1v-1-1c1-3 3-7 4-9v-1l1-2c1-1 1-1 1-2h0l1-1v-1c0-1 1-2 1-3v-1l1-2h0l2-2c0-3 1-4 2-7h0l1-2v-1-1h1c0-1 0-1 1-2v-1s1-1 1-2 0 0 1-1v-2-1l1-1s0-1 1-1v-1l1-2c0-1 1-2 1-3v-1l2-4 1-1v-2l1-1c0-1 1-1 1-3 1-4 4-9 5-14 1-2 2-3 3-5v-2c1-1 1-2 2-3h0v-1-1c1-1 1-2 1-3h1c0-1 1-2 1-4 1-2 2-6 4-9v-1l1-1 4-10 1-3v-1-1c1 0 1-1 1-2l2-4 1-1v-1-1l1-1 1-2c1-2 1-3 2-5v-2h1c0-1 1-2 1-3h0v-2h1v-1-1c1 0 1-1 1-1v-1c1-1 1-1 1-2v-1c1 0 1-1 2-2v-1-1c1-1 1-2 2-3v-2l1-1v-1h0c1-1 1-2 1-3h1l1-2v-1-1l2-2v-2-1l1-1c2-4 3-7 4-10l1-2v-1l1-2h0c0-1 1-2 1-3h0c1-1 1-2 1-3h1c0-2 1-3 2-5h0v-2l1-1 1-2v-2l1-1v-2l1-1h0l1-1v-1-1-1c1 0 1-1 2-2 0-1 1-3 1-4v-1l1-1c3-5 4-11 6-15l1-2v-1l1-2h0l1-2v-1l1-2h0c0-1 1-2 1-3h0c1-1 1-2 2-3l1-3c1-2 1-3 2-5 0-1 0-1 1-2v-1-2c1 0 1-1 1-1l1-2 1-1c1-2 1-4 2-6v-1l1-2 5-11v-1l1-1c1-2 1-4 2-6v-1l1-2c1-1 1-2 2-3v-1l1-2v-1-1c1-1 2-2 2-4h0v-1l1-1s0-1 1-1v-1c0-1 0-1 1-2v-1s1-1 1-2v-1l1-2h0c0-1 1-2 1-3h0c1-1 1-2 1-3h1v-2l1-1v-1h0c1-1 1-1 1-2v-1c1 0 1-1 1-2l1-1v-2h1v-2l1-2c1-1 1-1 1-2h0l1-1v-1c1-1 1-3 2-4v-1l1-2 2-5 1-2c0-2 1-3 1-4v-1l1-1 3-6v-1l1-2c1-2 1-3 2-5v-2h1v-1-1h1v-1c0-1 1-2 1-3h0l1-2v-1-1h1v-1-1c1 0 1-1 1-2l1-2 1-2h0l1-2 1-4 1-1 1-2v-1l1-2 1-1v-1s1-1 1-2l2-4v-1-1l1-1 1-1v-2c2-2 3-5 4-8l1-1v-2l1-1 1-3 1-2h0v-1l1-1c1-1 3-5 3-7 1-1 2-2 2-4l2-3 1-2v-1-1c1 0 1-1 2-2h0v-1c1-1 1-1 1-2h1v-2l1-1v-1h0c1-1 1-1 1-2h1c0-1 1-3 2-4v-1-1l1-1c0-1 1-1 1-2 1-1 1-2 2-3 0-1 0-1 1-2 1-2 3-4 3-7 1 0 1 0 1-1 1-1 1-2 2-3v-2c1 0 1-1 1-1l1-1v-1-1h1v-1-1-1h1v-2h0v-4-1c0-1 0-1-1-1v-1l-1-1h0l-1-1-3-3h0-3 0c-1-1-1-1-2-1-2-1-3 0-4 0-2-1-5-1-7-1-1-1-3 0-5 0v-1c-2 0-3-1-5-1l-2 2-1-1c3-3 7-6 11-9z" class="G"></path><defs><linearGradient id="g" x1="237.963" y1="312.786" x2="347.806" y2="285.573" xlink:href="#B"><stop offset="0" stop-color="#989693"></stop><stop offset="1" stop-color="#f1f1f0"></stop></linearGradient></defs><path fill="url(#g)" d="M256 261l16 1c12 2 23 5 34 10l6 3 4 2 24 14c2 1 4 3 6 5 3 2 5 4 8 7s7 6 10 10c5 6 5 15 8 22 1 1 1 3 2 5l3 8c2 6 6 12 7 18l1 1c0 2 1 4 1 5 0 2 1 4 1 6l2 3c2 3 3 6 4 9l-1 1-2-3-8-8c-18-18-42-34-67-40-3-1-6-1-8-2l-9-2-8-1c-16-1-32 0-48 4-2 0-6 1-8 2v1c-3-1-3-4-4-6-1-4-2-7-4-10l-1-1c-1-3-2-5-3-7-2-5-2-9-5-14-1-3-4-6-5-9-1-6-3-11-5-16v-1c0-2-1-3-2-4h0l1-1h1l1-1v-1l1-1 1-1 5-2 3-1 15-4 23-1z"></path><path d="M321 326h3 0l1 1 1-1 1 2c-1 0-1 0-2-1h-1c-1 0-2 0-3-1zm-14-52l3 2c1 1 1 2 2 2 1 1 1 1 2 1s1 0 1 1c-1 0-2-1-3-1h-1-1c-1 0-1 1-2 1s-2 0-3 1c0-2 1-2 3-3h1l-1-1h1c-1-1-2-1-2-3z" class="G"></path><path d="M308 324v-1c3 3 5 6 8 9 0 0 0 1 1 1l1 1h1c-2 1-3 0-4 0l-2-2h-1l2 2c0 1 0 0 1 1l1 1h1c2 1 2 1 3 2h0c-1 0-4-1-5-2l-4-4c0-1-1-1-1-2l-2-6z" class="H"></path><path d="M303 316l2 1h1v-2l1-1v-3l1-1v-1h0v2c0 2 0 3-1 5-1 1 0 6 1 8h0l2 6h-1c-3-4-6-8-7-13l1-1z" class="E"></path><defs><linearGradient id="h" x1="281.984" y1="304.959" x2="293.709" y2="300.266" xlink:href="#B"><stop offset="0" stop-color="#8b8b8b"></stop><stop offset="1" stop-color="#aeaca8"></stop></linearGradient></defs><path fill="url(#h)" d="M281 305c1-1 1-2 1-2 2-5 6-13 11-15-2 3-5 7-4 10 0 6 5 9 7 14h1l1 1c-6-2-11-5-16-7l-1-1z"></path><path d="M256 261l16 1c12 2 23 5 34 10l6 3 4 2h0c-3-1-5-2-7-3h-2c0-1 0-1-1-1l-11-4-13-4c-1 0-2-1-3-1h-1-3v-1h-4c-2 0-4 0-6-1h-1c-6 0-14-1-19 0-2 1-4 0-6 1-4 0-9 1-13 2h2c3-1-1 0 1 0 1-1 1 0 2-1h3c1-1 3 0 4 0 3-1 6-1 8-1h1c1-1 11-1 13 0h6s1 0 2 1h4c1 0 1 0 2 1h4 1c1 1 2 1 3 1 2 0 3 1 5 2l-15-3c-3-1-7-1-10-1-6-1-13-1-19 0-10 0-19 2-28 5l-6 3h-1v-1l1-1 1-1 5-2 3-1 15-4 23-1z" class="V"></path><defs><linearGradient id="i" x1="285.559" y1="315.613" x2="295.525" y2="309.005" xlink:href="#B"><stop offset="0" stop-color="#979695"></stop><stop offset="1" stop-color="#c5c4c3"></stop></linearGradient></defs><path fill="url(#i)" d="M281 305h0l1 1c5 2 10 5 16 7 0 0 1 0 1 1 1 1 2 1 3 1l1 1-1 1c1 5 4 9 7 13h1c0 1 1 1 1 2-5-1-11-3-15-6-5-1-10-4-14-6 0-2 0-3-1-5v-3h0v-7z"></path><path d="M281 312c1 1 1 1 1 2 2 4 5 5 8 7 2 2 4 3 6 4v1c-5-1-10-4-14-6 0-2 0-3-1-5v-3z" class="M"></path><path d="M209 272l6-3c9-3 18-5 28-5-6 4-11 9-13 16-2 3-2 5-2 8-1 0-2 0-3-1h0c-4-3-7-7-11-9-2-2-4-4-5-6z" class="J"></path><defs><linearGradient id="j" x1="277.322" y1="284.127" x2="302.491" y2="293.55" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#dbdbda"></stop></linearGradient></defs><path fill="url(#j)" d="M274 291c4-8 11-16 19-19 2-2 4-1 7 0 2 1 4 2 6 2h1c0 2 1 2 2 3h-1l1 1h-1c-2 1-3 1-3 3h-1c-1 1 0 1-1 1-2 1-3 2-5 3s-4 3-5 3c-5 2-9 10-11 15 0 0 0 1-1 2h0v7h0v3c1 2 1 3 1 5l-6-3c-1-1-1-2-2-3-1-4-2-8-2-12l1-6v-2l1-3z"></path><path d="M280 311c0-2-2-4-3-5v-1h0 4v7c-1-1-1 0-1-1z" class="H"></path><path d="M273 296v1 4 1h0l2 2 2-1c0-1 1-2 1-3h-1v-2-1c2 2 2 3 2 6-1 1-2 1-3 1l-1 1c1 3 3 4 5 6 0 1 0 0 1 1h0v3c1 2 1 3 1 5l-6-3c-1-1-1-2-2-3-1-4-2-8-2-12l1-6z" class="R"></path><path d="M281 315h-1c-2-1-4-4-5-6s-1-3-1-5h2l-1 1c1 3 3 4 5 6 0 1 0 0 1 1h0v3z" class="J"></path><defs><linearGradient id="k" x1="262.394" y1="271.192" x2="285.107" y2="283.809" xlink:href="#B"><stop offset="0" stop-color="#9f9e9d"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#k)" d="M265 266h2c0-1 1-1 2-1h3l15 3c2 0 4 1 6 2v1c2 0 5-1 7 0v1c-3-1-5-2-7 0-8 3-15 11-19 19l-1 3v2l-1 6c0 4 1 8 2 12 1 1 1 2 2 3-3-1-6-2-8-3l-4-2s-1-1-2-1c0 0-1-1-1-2 0-4 1-7 2-12 0-1 1-3 1-4-1-1-1-4-3-6h0v-8c1-3 2-6 2-8 1-1 2-3 2-4v-1z"></path><path d="M273 283c1-1 3-2 5-3-2 2-5 6-5 8 1 1 1 2 1 3l-1 3v-1c-1-1-1-4-1-6 1-1 1-2 1-4h0z" class="D"></path><path d="M273 283h0c0 2 0 3-1 4 0 2 0 5 1 6v1 2l-1 6c0 4 1 8 2 12 1 1 1 2 2 3-3-1-6-2-8-3l-4-2s-1-1-2-1c0 0-1-1-1-2 0-4 1-7 2-12 0-1 1-3 1-4 1-2 2-3 3-5 1-1 3-2 4-3l2-2z" class="J"></path><path d="M273 283h0c0 2 0 3-1 4 0 2 0 5 1 6v1 2l-1 6-3-2c2-3 1-6 1-9 0-2 1-4 1-6l2-2z" class="H"></path><path d="M274 314l-2-2c-2-4-5-6-6-10 0-1-1-3 0-4l3 2 3 2c0 4 1 8 2 12z" class="E"></path><path d="M267 288c1 2 1 4 0 6l-1 3c-2 1-2 1-3 2 0 3 0 4 1 6l1 3c0 1 1 3 2 4s1 1 1 2l-4-2s-1-1-2-1c0 0-1-1-1-2 0-4 1-7 2-12 0-1 1-3 1-4 1-2 2-3 3-5z" class="M"></path><defs><linearGradient id="l" x1="229.513" y1="277.91" x2="259.439" y2="278.04" xlink:href="#B"><stop offset="0" stop-color="#878685"></stop><stop offset="1" stop-color="#b2b1b0"></stop></linearGradient></defs><path fill="url(#l)" d="M243 264c6-1 13-1 19 0 3 0 7 0 10 1h-3c-1 0-2 0-2 1h-2v1c0 1-1 3-2 4 0 2-1 5-2 8v8h0c2 2 2 5 3 6 0 1-1 3-1 4-1-1-5-2-7-3l-3-2c-1 0-1 1-1 1h-1c0-1 0-3-1-5v7 3h0l1 6c-3-1-4-4-6-7-1-1-5-5-6-8l-1 1-2-2c-1-1-1-1-2-3 0 0 0-1-1-2h-2c0-1 0-1-1-1v-1-1h0c2-7 7-12 13-16z"></path><path d="M236 284c2 1 3 2 4 5h0-1l-1 1c-1-3-2-4-2-6z" class="Z"></path><path d="M233 283v-1-1-2l1 1c0 1 0 3 1 3l1 1c0 2 1 3 2 6l-2-2c-1-1-1-1-2-3 0 0 0-1-1-2z" class="M"></path><path d="M262 264c3 0 7 0 10 1h-3c-1 0-2 0-2 1h-2-1c-2 0-4 0-6 2-2 1-4 2-6 4-1 1-3 4-5 4v1h1 1v1h-2c-1-1-2 0-3-1l1-2c3-4 9-8 13-9l1-1h0l3-1z" class="L"></path><path d="M249 278v-1h-1-1v-1c2 0 4-3 5-4 2-2 4-3 6-4 2-2 4-2 6-2-6 4-9 10-12 16v-1l-3-3z" class="D"></path><path d="M247 278h2l3 3c-1 1-1 2-1 4l-1 3v7 3h0l1 6c-3-1-4-4-6-7 0-2-1-4-2-6s-3-6-2-8l1-1c0-1 0-2 1-2 1-2 2-2 4-2z" class="J"></path><path d="M243 286c1 1 2 1 4 2h-2l4 8 1-1v3c-1-1-2-1-2-2-2-2-6-7-5-10z" class="F"></path><path d="M251 285l-1 3v7l-1 1-4-8h2c2-1 3-2 4-3z" class="H"></path><path d="M247 278h2l3 3c-1 1-1 2-1 4-1 1-2 2-4 3-2-1-3-1-4-2 0-1-2-2-2-3l1-1c0-1 0-2 1-2 1-2 2-2 4-2z" class="B"></path><defs><linearGradient id="m" x1="251.113" y1="279.148" x2="264.318" y2="279.351" xlink:href="#B"><stop offset="0" stop-color="#818182"></stop><stop offset="1" stop-color="#abaaa8"></stop></linearGradient></defs><path fill="url(#m)" d="M264 266h1v1c0 1-1 3-2 4 0 2-1 5-2 8v8h0c2 2 2 5 3 6 0 1-1 3-1 4-1-1-5-2-7-3l-3-2c-1 0-1 1-1 1h-1c0-1 0-3-1-5l1-3c0-2 0-3 1-4v1c3-6 6-12 12-16z"></path><path d="M214 278c4 2 7 6 11 9h0c1 1 2 1 3 1 0-3 0-5 2-8h0v1 1c1 0 1 0 1 1h2c1 1 1 2 1 2 1 2 1 2 2 3l2 2 1-1c1 3 5 7 6 8 2 3 3 6 6 7l-1-6h0v-3-7c1 2 1 4 1 5h1s0-1 1-1l3 2c2 1 6 2 7 3-1 5-2 8-2 12 0 1 1 2 1 2 1 0 2 1 2 1 0 1 0 1 1 2 2 4 5 7 9 9 3 2 10 3 11 5l-20-5c-4-1-8-2-12-4-2-1-3-1-5-1h0c-2 2-3 2-6 2-6-3-12-11-16-17-1-1-2-3-1-5h0v-1c0-2 1-4 0-6-1-1-2-2-4-2v-2h0v-2c-1-1-2-2-4-3 0-1-1-1-1-2l-2-2z" class="H"></path><path d="M250 295v-7c1 2 1 4 1 5h1s0-1 1-1l3 2h-2 1c1 1 1 2 1 3s1 2 1 4v-1c1 3 4 6 3 9-1 0-2 0-3-1l-3-1c-1 0-1-1-2-1l-1-2-1-6h0v-3z" class="J"></path><path d="M250 295v-7c1 2 1 4 1 5h1c1 4 2 7 3 11l2 3v1l-3-1c-1 0-1-1-2-1l-1-2-1-6h0v-3z" class="S"></path><path d="M250 298h1l4 9h-1c-1 0-1-1-2-1l-1-2-1-6z" class="M"></path><path d="M242 320c-6-3-12-11-16-17-1-1-2-3-1-5 2 3 4 6 7 8 6 4 10 8 16 11v1c-2 2-3 2-6 2z" class="C"></path><path d="M230 280h0v1 1c1 0 1 0 1 1h2c1 1 1 2 1 2 1 2 1 2 2 3l2 2 1-1c1 3 5 7 6 8 2 3 3 6 6 7l1 2-8-5c-3-2-7-5-10-7-2-2-4-5-6-6 0-3 0-5 2-8z" class="S"></path><path d="M238 290l1-1c1 3 5 7 6 8 2 3 3 6 6 7l1 2-8-5c-1-4-5-7-6-11z" class="F"></path><path d="M208 272h1c1 2 3 4 5 6l2 2c0 1 1 1 1 2 2 1 3 2 4 3v2h0v2c2 0 3 1 4 2 1 2 0 4 0 6v1h0c-1 2 0 4 1 5 4 6 10 14 16 17 3 0 4 0 6-2h0v2c1 2 2 2 4 3l3 1c2 0 4 0 6 1 4 2 8 2 12 4l1 1 8 1c3 0 6 1 9 2l6 2h-3-4c-16-1-32 0-48 4-2 0-6 1-8 2v1c-3-1-3-4-4-6-1-4-2-7-4-10l-1-1c-1-3-2-5-3-7-2-5-2-9-5-14-1-3-4-6-5-9-1-6-3-11-5-16v-1c0-2-1-3-2-4h0l1-1h1l1-1z" class="C"></path><path d="M226 306l3 6c1 4 3 7 3 11 0-1-1-1-1-2-2-3-3-6-4-9v-2c-1-1-1-2-1-4z" class="L"></path><path d="M221 289c2 0 3 1 4 2 1 2 0 4 0 6v1h-5 0c-2-1-3-2-4-3 0-2 0-3 1-4 1-2 3-2 4-2z" class="N"></path><defs><linearGradient id="n" x1="240.598" y1="311.67" x2="229.352" y2="315.168" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#b2b2b1"></stop></linearGradient></defs><path fill="url(#n)" d="M225 298h0c-1 2 0 4 1 5 4 6 10 14 16 17 1 1 2 1 2 2-2 0-3 0-4 1l-1 1c0 2 1 3 3 5l-7-2-3-4c0-4-2-7-3-11l-3-6h-1v-1c-1-2-3-4-3-5s-1-1-2-2h5z"></path><path d="M248 318h0v2c1 2 2 2 4 3l3 1c2 0 4 0 6 1 4 2 8 2 12 4l1 1 8 1c-3 2-12 0-15-1-5-1-10 0-15 0-3 0-7-1-10-1-2-2-3-3-3-5l1-1c1-1 2-1 4-1 0-1-1-1-2-2 3 0 4 0 6-2z" class="F"></path><path d="M248 318h0v2c1 2 2 2 4 3-3 0-5-1-8-1 0-1-1-1-2-2 3 0 4 0 6-2z" class="J"></path><path d="M255 324c2 0 4 0 6 1 4 2 8 2 12 4l1 1c-2 0-5-1-8-1-1-1-3-1-5-1-2-2-4-2-6-4z" class="L"></path><path d="M354 303c3 3 7 6 10 10 5 6 5 15 8 22 1 1 1 3 2 5l3 8c2 6 6 12 7 18l1 1c0 2 1 4 1 5 0 2 1 4 1 6l2 3c2 3 3 6 4 9l-1 1-2-3-8-8c-18-18-42-34-67-40-3-1-6-1-8-2l-9-2-8-1h4 3l-6-2c-3-1-6-2-9-2l-8-1-1-1c-4-2-8-2-12-4-2-1-4-1-6-1l-3-1c-2-1-3-1-4-3v-2c2 0 3 0 5 1 4 2 8 3 12 4l20 5c-1-2-8-3-11-5-4-2-7-5-9-9-1-1-1-1-1-2l4 2c2 1 5 2 8 3l6 3c4 2 9 5 14 6 4 3 10 5 15 6l4 4c1 1 4 2 5 2 5 3 12 5 17 7-2-2-3-3-4-5h1 1c1-3 0-6 1-9v-3l1-2v-2-1c1-1 1-2 1-2l1-1c0-1 0-1 1-2v-1-1-1c1 0 1 0 2-1v-1l1-1v-1h1c1-2-1 0 1-2h0v-1l2-1h0c1-1 3-2 4-3 1 0 2 0 3-1z" class="K"></path><path d="M382 380c2 1 3 3 5 3l-1-1c-1-1-1-1-1-2h0c2 1 4 3 5 6h0v2l-8-8z" class="Y"></path><path d="M320 338c5 3 12 5 17 7-2-2-3-3-4-5h1 1c1-3 0-6 1-9v-3l1-2v-2-1c1-1 1-2 1-2l1-1c0-1 0-1 1-2v-1-1-1c1 0 1 0 2-1v-1l1-1v-1h1c1-2-1 0 1-2h0v-1l2-1h0c-6 8-8 15-11 25-1 2 0 5 0 7 1 2 0 3 1 4l1 2 2 2h-1l-1-1s-1 0-2-1l-8-3c-1 0-1 0-2-1l-8-3h-2c-1-1-2-1-3-1 1-1 1-1 2-1 1 1 4 2 5 2zm-55-24c2 1 4 3 6 5l23 10c6 3 14 3 20 7-3 0-6-2-9-2-6-2-14-3-20-6-1-2-8-3-11-5-4-2-7-5-9-9z" class="D"></path><defs><linearGradient id="o" x1="542.167" y1="824.549" x2="456.684" y2="826.737" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2f36"></stop></linearGradient></defs><path fill="url(#o)" d="M496 760c3 2 4 4 7 6 2 1 5 2 7 3v-2l7 7c4 3 8 6 12 8 2 1 3 2 4 2 1 1 2 2 3 2 0-1 0-1 1-2l9 12c4 6 7 11 9 18 0 1 0 2-1 3 3 8 4 15 4 24-1 13-3 25-7 37 2 1 4 1 6 3h1 1v1h0c1 3 3 5 5 8l1 2c-2 1-7 0-9 0h-22l8 1h0c-2 0-5 0-7 1h-19-16-1v1c-2 1-4 0-6 0-5 0-10 0-15-2h-1c-2 0-6-3-7-5l1-2c0-2 3-5 4-7l2 1 1-1 4-2c-1-2-1-3-2-5h-2c-2 2-6 2-8 2h0-2-1l-1-1h0l-2-4c-1-7-1-15 0-22-2-3-7-3-9-6-2-1-2-2-2-4l1-1c0-2 1-4 1-6l2-10c-2-2-2-2-2-5 2 0 3-2 4-3 2-1 4-4 6-7v-1c0-1 0-1-1-2v-3l-1-1v-3c-1-1-1-1-1-2v-3l-1-1c-1-3-1-4-3-7h0l-1-1h-1l1-3-5 2-5 3c-3 1-5 3-8 5-2 0-2 2-4 2 0-1 0-2 1-3h0l8-5 6-4c1-1 3-2 5-2 5-3 10-5 16-7 1 0 2 0 3-1 6-1 11 0 16 0 2 1 4 1 6 1s5 1 7 1c1 0 1-1 1-2-1-1-2-2-4-3-1-1-3-3-4-5z"></path><path d="M510 842l5 4c0 1 1 2 0 4-2 0-3-1-4-2-1-2-1-4-1-6z" class="Q"></path><path d="M510 842a30.44 30.44 0 0 1 8-8l-3 12-5-4zm-7-37c1-2 3-3 5-5v1c-1 0-3 2-3 3-1 0-1 5-1 6v1 1l1 1c0 2-1 3-1 5v1c1 2 0 5 0 7-1-2-1-3-1-4v-1h0c1-2 1-3 1-6-2 2-3 5-4 7 0-2 1-4 1-6 1-3 2-7 2-11z" class="T"></path><defs><linearGradient id="p" x1="534.735" y1="822.532" x2="528.978" y2="830.484" xlink:href="#B"><stop offset="0" stop-color="#3a3e48"></stop><stop offset="1" stop-color="#585a62"></stop></linearGradient></defs><path fill="url(#p)" d="M533 820l1 1v1h0c1 1 1 1 2 1v-1h0v6c-1-1-1-1-2-1-1 2-2 5-2 7-1-2-1-2-2-3h-1c-1 2-2 5-4 6l8-17z"></path><path d="M475 814h1l5-1v1 1c1 0 1 1 1 2-1 1-1 2-1 3h1c-1 2-1 4-2 6l1 1c-1 3-2 5-3 7-1 1-1 2-1 3l-3 4 6-23v-1l-1-1c-3-2-5 0-7-1l3-2v1z" class="Q"></path><path d="M458 782l2 1v-1l1-1 3 6v3c1 2 2 4 2 6v-1c0 2 0 4 1 5 0 1-1 2 0 3h1c0 1-1 2-2 3l-1-1v-1c0-1 0-1-1-2v-3l-1-1v-3c-1-1-1-1-1-2v-3l-1-1c-1-3-1-4-3-7zm45 23c0 4-1 8-2 11 0 2-1 4-1 6s-1 4-2 6v1s-1 2-1 3v1 2c-1 1-1 1-1 2v3-1l1-1v1c0 1-1 2-2 3v-1c-2 3-1 6-3 8l1 1-1 1v-1 1c-1 1-1 1-1 2v1h0c-1 2-1 3-1 4 0 2 0 3-1 5 0 1 0 1-1 2 0 1-1 3-2 4h-1-1 0-1c0 1 0 1-1 2 2-5 5-8 5-13 1-10 16-43 13-50l3-3z" class="O"></path><path d="M500 808c3 7-12 40-13 50 0 5-3 8-5 13h2v-1h2 0 2l7-1c-3 2-6 4-10 6l-3 2c-1-2-1-3-2-5h-2c2-2 4-4 4-6 2-5 3-11 5-16l8-24c1-4 3-8 3-12 0-1-1-1-2-2l4-4z" class="X"></path><path d="M480 872l6-1c0 2 0 3-1 4l-3 2c-1-2-1-3-2-5z" class="F"></path><path d="M481 790l3-3 2-1h0l-3 3 1 2 1-1h0 1c0 1 1 1 0 2 0 2 1 4 0 5 0 2 0 3-1 5 0 3-1 5-1 7-1 2-4 3-6 4h-1l-2 1v-1l-3 2h-1l3-14-1-1c-1 1-2 2-3 2h-1l6-7 3-3 3-2z" class="X"></path><path d="M478 792l3-2 1 2c0 4-1 7-1 11-1 3-1 6-3 10h-1c2-5 3-12 3-18 0-1-1-2-2-3z" class="W"></path><path d="M478 792c1 1 2 2 2 3 0 6-1 13-3 18l-2 1v-1l3-16c-1-1-1-1-3-2l3-3z" class="U"></path><path d="M475 795c2 1 2 1 3 2-1 6-2 11-3 16l-3 2h-1l3-14-1-1c-1 1-2 2-3 2h-1l6-7z" class="M"></path><path d="M496 812c1 1 2 1 2 2 0 4-2 8-3 12l-8 24c-2 5-3 11-5 16 0 2-2 4-4 6s-6 2-8 2h0v-1h2c2-2 4-4 5-7 2-4 2-9 4-13 3-10 8-19 10-29 1-2 1-3 0-5 1-2 2-3 3-4l2-3z" class="U"></path><path d="M496 812c1 1 2 1 2 2 0 4-2 8-3 12l-8 24c-2 5-3 11-5 16 0 2-2 4-4 6s-6 2-8 2h0v-1h2c2 0 3 0 5-2 1-1 3-4 4-5 1-4 1-8 3-12l7-22c2-4 4-10 4-15 0-1 0-1-1-2l2-3z" class="W"></path><defs><linearGradient id="q" x1="530.275" y1="850.101" x2="514.285" y2="849.442" xlink:href="#B"><stop offset="0" stop-color="#5c5d66"></stop><stop offset="1" stop-color="#a2a29e"></stop></linearGradient></defs><path fill="url(#q)" d="M525 837c2-1 3-4 4-6h1c1 1 1 1 2 3 0 2 0 3-1 5v3c0 1-1 2-1 3l-3 11c-2 4-4 7-6 11-1 1-2 1-3 2-3 2-3 3-6 3l13-35z"></path><path d="M491 819c1 2 1 3 0 5-2 10-7 19-10 29-2 4-2 9-4 13-1 3-3 5-5 7h-2v1h0 0-2-1l-1-1h0l-2-4 1-1 1-2c2-8 6-16 10-23v-1c1-2 2-4 4-6 1-3 4-7 5-9l6-8z" class="M"></path><path d="M485 827c1 1 1 3 0 5-2 9-6 16-8 24-2 4-2 7-4 11-1 2-2 5-4 6l-1 1h-1c1-3 3-6 5-8 1-3 2-7 3-10 1-6 4-12 5-17h-1c1-1 1-2 1-3 1-3 4-7 5-9z" class="J"></path><path d="M480 836c0 1 0 2-1 3h1c-1 5-4 11-5 17-1 3-2 7-3 10-2 2-4 5-5 8l-1-1h0l-2-4 1-1 1-2c2-8 6-16 10-23v-1c1-2 2-4 4-6z" class="H"></path><path d="M476 843c0 2-1 5-2 7l-5 15-3 8h0l-2-4 1-1 1-2c2-8 6-16 10-23z" class="I"></path><defs><linearGradient id="r" x1="490.994" y1="811.655" x2="515.443" y2="772.246" xlink:href="#B"><stop offset="0" stop-color="#d1d0d0"></stop><stop offset="1" stop-color="#fdfdfd"></stop></linearGradient></defs><path fill="url(#r)" d="M496 760c3 2 4 4 7 6 2 1 5 2 7 3v-2l7 7c4 3 8 6 12 8 2 1 3 2 4 2 1 1 2 2 3 2 0-1 0-1 1-2l9 12c4 6 7 11 9 18 0 1 0 2-1 3-3-7-6-12-10-18-3-3-7-7-10-9-17-15-39-19-60-16l-7 1-10 3-5 2-5 3c-3 1-5 3-8 5-2 0-2 2-4 2 0-1 0-2 1-3h0l8-5 6-4c1-1 3-2 5-2 5-3 10-5 16-7 1 0 2 0 3-1 6-1 11 0 16 0 2 1 4 1 6 1s5 1 7 1c1 0 1-1 1-2-1-1-2-2-4-3-1-1-3-3-4-5z"></path><path d="M467 775l1-2h0c3 0 3 0 6 1l-7 1z" class="K"></path><path d="M444 782l6-4c-1 2-4 3-6 5h1 1c1-2 4-3 6-4v1l-5 3c-3 1-5 3-8 5-2 0-2 2-4 2 0-1 0-2 1-3h0l8-5z" class="G"></path><path d="M496 760c3 2 4 4 7 6 2 1 5 2 7 3 3 2 5 4 8 6l-15-5h0c1 0 1-1 1-2-1-1-2-2-4-3-1-1-3-3-4-5z" class="J"></path><path d="M469 802h1c1 0 2-1 3-2l1 1-3 14h1c2 1 4-1 7 1l1 1v1l-6 23-2 4v1 3l1-2c1-2 1-4 2-5h1v1c-4 7-8 15-10 23l-1 2-1 1c-1-7-1-15 0-22-2-3-7-3-9-6-2-1-2-2-2-4l1-1c0-2 1-4 1-6l2-10c-2-2-2-2-2-5 2 0 3-2 4-3 2-1 4-4 6-7l1 1c1-1 2-2 2-3l1-1z" class="M"></path><defs><linearGradient id="s" x1="466.861" y1="834.513" x2="456.982" y2="833.109" xlink:href="#B"><stop offset="0" stop-color="#818181"></stop><stop offset="1" stop-color="#a8a9ab"></stop></linearGradient></defs><path fill="url(#s)" d="M465 828c1 0 2-1 3-2 0 1 0 1-1 2v1c1 4-1 7-2 11-1 2-1 3-1 6v1c-2-3-7-3-9-6-2-1-2-2-2-4l1-1h1 1l9-8z"></path><path d="M471 815h1c2 1 4-1 7 1l1 1v1l-6 23-2 4v1 3l1-2c1-2 1-4 2-5h1v1c-4 7-8 15-10 23-2-2 0-9 1-11 0-4 0-8 1-12l4-14c0-2 1-5 0-7-2 1-5 4-7 6l-9 8h-1c5-6 12-11 18-16-2-1-5-1-6-3 0-1 3-2 4-2z" class="P"></path><path d="M479 816l1 1v1l-6 23-2 4c0-2 1-4 1-6l4-14c0-2 0-4 1-6v-2l1-1z" class="O"></path><defs><linearGradient id="t" x1="477.241" y1="812.615" x2="450.759" y2="821.885" xlink:href="#B"><stop offset="0" stop-color="#9e9fa5"></stop><stop offset="1" stop-color="#bfbebd"></stop></linearGradient></defs><path fill="url(#t)" d="M469 802h1c1 0 2-1 3-2l1 1-3 14c-1 0-4 1-4 2 1 2 4 2 6 3-6 5-13 10-18 16h-1c0-2 1-4 1-6l2-10c-2-2-2-2-2-5 2 0 3-2 4-3 2-1 4-4 6-7l1 1c1-1 2-2 2-3l1-1z"></path><path d="M465 805l1 1c-3 4-7 9-9 14-2-2-2-2-2-5 2 0 3-2 4-3 2-1 4-4 6-7z" class="B"></path><defs><linearGradient id="u" x1="557.757" y1="843.017" x2="528.666" y2="838.15" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292c34"></stop></linearGradient></defs><path fill="url(#u)" d="M544 799c4 6 7 11 10 18 3 8 4 15 4 24-1 13-3 25-7 37 2 1 4 1 6 3h1 1v1h0c1 3 3 5 5 8l1 2c-2 1-7 0-9 0h-22l8 1h0c-2 0-5 0-7 1h-19-16-1v1c-2 1-4 0-6 0-5 0-10 0-15-2h-1c-2 0-6-3-7-5l1-2c0-2 3-5 4-7l2 1 1-1h2 29c1-2 2-5 3-7 3 0 3-1 6-3 1-1 2-1 3-2 2-4 4-7 6-11l3-11c0-1 1-2 1-3v-3c1-2 1-3 1-5s1-5 2-7c1 0 1 0 2 1v-6h0v1c-1 0-1 0-2-1h0v-1l-1-1 6-15c1-2 2-4 5-6z"></path><path d="M520 878h1c1-1 3-1 4-3h1l1-1v1c0 1 0 2-1 3h-4 3v1h-6l1-1z" class="B"></path><path d="M536 822c1 0 2 1 2 2v2 4 2c0 1 1 2 1 4l-1-1c-1 2-1 2-1 4 0 1-1 2-1 3v1c-1 1 0 2-1 3v3 3c-1 1-1 2-1 3l-1 1h-1l-1 2 2 4c-1 1-2 1-3 2v1c0 1 0 2-1 3h-1 0c0 2-1 3-2 4v1c-2 2-4 3-6 5l-1 1h-8c1-1 2-1 3-1l4-2c3-2 5-5 7-8 2-4 3-7 5-11l5-20 1-9v-6z" class="Q"></path><path d="M531 858l2 4c-1 1-2 1-3 2v1c0 1 0 2-1 3h-1 0c0 2-1 3-2 4v1c-2 2-4 3-6 5l-1 1h-8c1-1 2-1 3-1 2 0 3 0 5-1h0l2-2h1l3-3c0-1 0 0 1-1 0-1 1-2 1-3h1c0-1 1-2 1-3s0 0 1-1v-1l1-1v-4z" class="T"></path><path d="M532 834c0-2 1-5 2-7 1 0 1 0 2 1l-1 9-5 20c-2 4-3 7-5 11-2 3-4 6-7 8l-4 2c-1 0-2 0-3 1h8 6 4c-6 1-14 0-21 0l-17 1c-3 0-7 0-11-1h29c1-2 2-5 3-7 3 0 3-1 6-3 1-1 2-1 3-2 2-4 4-7 6-11l3-11c0-1 1-2 1-3v-3c1-2 1-3 1-5z" class="P"></path><defs><linearGradient id="v" x1="517.046" y1="893.68" x2="517.693" y2="878.058" xlink:href="#B"><stop offset="0" stop-color="#888"></stop><stop offset="1" stop-color="#a6a7a8"></stop></linearGradient></defs><path fill="url(#v)" d="M529 879h15c2 0 5 0 7-1 2 1 4 1 6 3h1 1v1h0c1 3 3 5 5 8l1 2c-2 1-7 0-9 0h-22l8 1h0c-2 0-5 0-7 1h-19-16-1v1c-2 1-4 0-6 0-5 0-10 0-15-2h-1c-2 0-6-3-7-5l1-2c0-2 3-5 4-7l2 1 1-1h2c4 1 8 1 11 1l17-1c7 0 15 1 21 0z"></path><defs><linearGradient id="w" x1="501.058" y1="896.592" x2="502.349" y2="889.89" xlink:href="#B"><stop offset="0" stop-color="#c0bdb8"></stop><stop offset="1" stop-color="#d3d6de"></stop></linearGradient></defs><path fill="url(#w)" d="M478 893c7-1 15-1 22-1h34l8 1h0c-2 0-5 0-7 1h-19-16-1v1c-2 1-4 0-6 0-5 0-10 0-15-2z"></path><defs><linearGradient id="x" x1="359.198" y1="428.621" x2="391.874" y2="430.868" xlink:href="#B"><stop offset="0" stop-color="#c5c4c4"></stop><stop offset="1" stop-color="#fbfbfa"></stop></linearGradient></defs><path fill="url(#x)" d="M234 342v-1c2-1 6-2 8-2 16-4 32-5 48-4l8 1 9 2c2 1 5 1 8 2 25 6 49 22 67 40l8 8 2 3 2 3c4 6 6 13 9 20 1 2 2 6 4 9 0 1 1 2 1 4 3 4 4 9 6 13l12 29c0 1 1 2 2 3 0 1 1 2 1 3 2 6 4 14 7 20 1 3 3 5 3 9h-1c-21-28-51-45-86-51l-12-2h-3c-2-1-4 0-6 0l-2-2c-1-1-3-1-4-1l-14-1c-4-1-7-2-11-3l-8-5-1-1c-2-1-7-5-7-8-1 0-4-1-5-1-2-2-4-4-7-5v-2c-2 0-3-1-4-1h-1c-2-3-3-6-4-9h1v1h1v-2c0-2 3-3 4-5l11-5 4-2h2c-1-1-1-1-2-1-3 0-7 1-10 3l-8 4c-1 1-1 1-2 1-1-2-2-5-3-8 0-1-1-3-1-5-1-1-2-4-3-5-2-3-5-6-5-9 0-2-1-4-2-6 0-2-1-4-2-6 0-3-2-6-4-8-4-4-6-9-9-14 0-1-1-2-1-3z"></path><defs><linearGradient id="y" x1="385.286" y1="439.827" x2="413.019" y2="441.729" xlink:href="#B"><stop offset="0" stop-color="#c7c7c7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#y)" d="M394 414l14 13c3 4 4 9 6 13-1 4-4 6-6 9-2 4-3 8-4 12v5h-2v1c-1 0-2-1-3-1h-1l-1-1-10-6c-1-1-1-2-2-3v-1c-2-11 2-25 6-35 1-2 2-3 3-5v-1z"></path><path d="M395 460c2 1 3 1 4 3h0l3 3v1c-1 0-2-1-3-1h-1l-1-1v-1l-2-4z" class="E"></path><path d="M387 459c2 0 2 0 3 1l1 1h1v-1l-1-1v-1l1 1v-1h1c0 1 1 1 1 2h1 0l2 4v1l-10-6z" class="C"></path><defs><linearGradient id="z" x1="329.876" y1="411.371" x2="370.401" y2="415.063" xlink:href="#B"><stop offset="0" stop-color="#bab9b8"></stop><stop offset="1" stop-color="#e5e5e4"></stop></linearGradient></defs><path fill="url(#z)" d="M349 395h3c7 2 13 4 20 7 2 1 5 1 7 3h-1-1c-1-1-2-1-3-1h-1c1 1 2 1 3 1v1c-3-1-3 0-5 1-6 6-9 13-12 21 0 2-1 4-1 6 0 1 0 2 1 2 0 2 0 4 1 5 0 3 2 6 3 8v1 1h-1l-1-1c-3 0-6-1-8-3l-7-3c-5-2-9-5-14-8l-1-3c-1-1-1-2-2-3h0c0-3-1-6-1-10 0-1 1-3 1-5 2-6 7-16 12-19 2-1 5 0 6 0s2-1 2-1z"></path><path d="M329 415c1 3 4 6 7 7 1 1 2 3 3 4l-11-5v-1c0-1 1-3 1-5z" class="F"></path><defs><linearGradient id="AA" x1="328.48" y1="422.353" x2="370.353" y2="424.781" xlink:href="#B"><stop offset="0" stop-color="#bbbaba"></stop><stop offset="1" stop-color="#fefefd"></stop></linearGradient></defs><path fill="url(#AA)" d="M339 426c3 1 7 3 10 5l4 2c1-2 2-6 2-8v-2h1v-1l1-2v-1c1 0 1-1 1-1l1-1c0-1 0-1 1-2 2-3 4-8 8-9 1-1 2-1 4-1l-1 2c-6 6-9 13-12 21 0 2-1 4-1 6 0 1 0 2 1 2 0 2 0 4 1 5 0 3 2 6 3 8v1 1h-1l-1-1c-3 0-6-1-8-3l-7-3c-5-2-9-5-14-8l-1-3c-1-1-1-2-2-3h0c0-3-1-6-1-10v1l11 5z"></path><path d="M328 420v1l1 2 1 1c2 0 4 3 5 5 0 0 1 1 2 1 3 3 6 6 11 7v1-1c5 3 8 8 13 11l2 2v1h-1l-1-1c-3 0-6-1-8-3l-7-3c-5-2-9-5-14-8l-1-3c-1-1-1-2-2-3h0c0-3-1-6-1-10z" class="I"></path><path d="M329 430l1-1v1l1-1 11 9c4 3 9 4 11 9l-7-3c-5-2-9-5-14-8l-1-3c-1-1-1-2-2-3z" class="F"></path><path d="M331 433c3 1 7 5 10 6l1 1 2 2c1 0 2 1 2 1v1c-5-2-9-5-14-8l-1-3z" class="J"></path><path d="M307 425l6 3h0l14 6c1 1 3 2 5 2 5 3 9 6 14 8l7 3c2 2 5 3 8 3l1 1h1v-1-1c0 1 1 2 2 2 1 1 3 2 5 2 2 1 5 3 8 3l1 1h1c1 1 1 1 2 1 1 1 2 1 3 1 1 1 2 1 3 2 2 1 5 2 7 4 1 0 3 1 3 1h1c1 0 2 1 3 1v-1h2v-5c1-4 2-8 4-12 2-3 5-5 6-9l12 29c0 1 1 2 2 3 0 1 1 2 1 3 2 6 4 14 7 20 1 3 3 5 3 9h-1c-21-28-51-45-86-51l-12-2h-3c-2-1-4 0-6 0l-2-2c-1-1-3-1-4-1l-14-1c-4-1-7-2-11-3l-8-5-1-1c-2-1-7-5-7-8l1 1 3 1 6 3c2 1 2 1 4 1l1 1 1-1v-1l-1-1c2 0 3 1 5 1-2-1-2-1-3-3h4l1-1h2v-2l1 1-2-5z" class="K"></path><path d="M424 478c1 0 0 0 0 1s1 3 1 4l-1 1s-1 0-2-1c0-2 1-3 2-5zm-117-53l6 3h0c-1 0-4-1-4-1v2h1c1 0 2 1 3 2h1v1l2 2 1 1h-2c-3-1-4-3-6-5l-2-5z" class="C"></path><path d="M314 432c1 0 2 1 3 1h0c3 1 6 4 9 5l12 6c-5 0-7-4-12-5-3-1-6-2-9-4l-1-1-2-2z" class="E"></path><defs><linearGradient id="AB" x1="314.573" y1="442.93" x2="322.511" y2="433.975" xlink:href="#B"><stop offset="0" stop-color="#b4b2b3"></stop><stop offset="1" stop-color="#d2d2d1"></stop></linearGradient></defs><path fill="url(#AB)" d="M308 429l1 1c2 2 3 4 6 5h2c3 2 6 3 9 4 5 1 7 5 12 5l6 3 3 1c1 0 2 1 3 1h1c-7 0-13-2-20-3l1 1c2 0 3 1 5 1h0-4 0l7 1v1 1h-3c-2-1-4 0-6 0l-2-2c-1-1-3-1-4-1l-14-1c-4-1-7-2-11-3l-8-5-1-1c-2-1-7-5-7-8l1 1 3 1 6 3c2 1 2 1 4 1l1 1 1-1v-1l-1-1c2 0 3 1 5 1-2-1-2-1-3-3h4l1-1h2v-2z"></path><path d="M308 429l1 1c2 2 3 4 6 5h-1c-1 1-4-1-6-2v-2-2zm-9 5c2 0 3 1 5 1 2 1 8 3 10 6l30 6 3 1c1 0 2 1 3 1h1c-7 0-13-2-20-3h0l-8-2c-8-2-16-4-24-7l1-1v-1l-1-1z" class="F"></path><path d="M284 430l1 1 3 1 6 3c2 1 2 1 4 1l1 1c8 3 16 5 24 7l8 2h0l1 1c2 0 3 1 5 1h0-4 0l7 1v1 1h-3c-2-1-4 0-6 0l-2-2c-1-1-3-1-4-1l-14-1c-4-1-7-2-11-3l-8-5-1-1c-2-1-7-5-7-8z" class="G"></path><path d="M285 431l3 1 6 3c2 1 2 1 4 1l1 1c8 3 16 5 24 7-5 2-10 1-14-1h-1c-9-3-16-6-23-12z" class="D"></path><path d="M336 392l9 2 4 1s-1 1-2 1-4-1-6 0c-5 3-10 13-12 19 0 2-1 4-1 5 0 4 1 7 1 10h0c1 1 1 2 2 3l1 3c-2 0-4-1-5-2l-14-6h0l-6-3 2 5-1-1v2h-2l-1 1h-4c1 2 1 2 3 3-2 0-3-1-5-1l1 1v1l-1 1-1-1c-2 0-2 0-4-1l-6-3-3-1-1-1c-1 0-4-1-5-1-2-2-4-4-7-5v-2c-2 0-3-1-4-1h-1c-2-3-3-6-4-9h1v1h1v-2c0-2 3-3 4-5l11-5 4-2h2c4-2 9-3 13-4l9-1 2-1 1 1 2-1c7 0 13 0 20 1 2-1 4 0 5-1h-2v-1z" class="R"></path><path d="M280 401l4-2 2 4c-2-1-4 0-6-2z" class="F"></path><path d="M286 399c4-2 9-3 13-4l4 1-1 4c-1 1-1 1-1 2l-1 1v-1c-1 2-1 4-1 6l-13-5-2-4h2z" class="M"></path><path d="M311 394l2-1c-2 3-4 8-5 11l-2 7c2 0 3 1 5 2-1 0-2 0-3-1-1 2 0 2-1 3-1-1-1-2-1-3v4l1 1-1 1v1c0 2 0 4 1 6h0l2 5-1-1h-1v-3c-1-1-4-3-6-3l-1-2c1-4-2-8-1-12h1l1-1 1-2c0-5 2-9 6-12l2-1 1 1z" class="H"></path><path d="M311 394l2-1c-2 3-4 8-5 11h0-1l-1 1v-1c0-3 1-6 3-9l2-1zm-11 27c1-4-2-8-1-12h1c2 0 4 1 5 2 0 2 0 6 1 8 0 2 0 4 1 6h0l2 5-1-1h-1v-3c-1-1-4-3-6-3l-1-2z" class="I"></path><defs><linearGradient id="AC" x1="311.385" y1="425.871" x2="316.159" y2="415.535" xlink:href="#B"><stop offset="0" stop-color="#a19f9f"></stop><stop offset="1" stop-color="#bcbcba"></stop></linearGradient></defs><path fill="url(#AC)" d="M306 418l1-1-1-1v-4c0 1 0 2 1 3 1-1 0-1 1-3 1 1 2 1 3 1 3 1 6 2 8 4v1l1 1 1-1v5c1 4 5 7 6 11l-14-6h0l-6-3h0c-1-2-1-4-1-6v-1z"></path><path d="M306 418c2 2 2 4 3 7h-2c-1-2-1-4-1-6v-1z" class="F"></path><defs><linearGradient id="AD" x1="289.845" y1="429.968" x2="296.361" y2="421.456" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#c2c1c0"></stop></linearGradient></defs><path fill="url(#AD)" d="M263 412h1v1h1v-2c0-2 3-3 4-5l1 1c5 2 8 4 14 5h0c2 0 4 0 5 2l3 3 4 2h0l4 2 1 2c2 0 5 2 6 3v3h1v2h-2l-1 1h-4c1 2 1 2 3 3-2 0-3-1-5-1l1 1v1l-1 1-1-1c-2 0-2 0-4-1l-6-3-3-1-1-1c-1 0-4-1-5-1-2-2-4-4-7-5v-2c-2 0-3-1-4-1h-1c-2-3-3-6-4-9z"></path><path d="M298 436c-1-1-3-2-4-3l1-1h0c1 1 2 2 4 2h0l1 1v1l-1 1-1-1z" class="J"></path><path d="M289 430c1 1 2 1 3 2s1 2 2 2v1l-6-3v-1l1-1z" class="F"></path><path d="M296 419l4 2 1 2c2 0 5 2 6 3v3h1v2h-2c-1-1-4-2-6-4s-5-5-4-8z" class="E"></path><path d="M301 423c2 0 5 2 6 3v3h0c-3-1-5-4-7-6h1z" class="K"></path><path d="M284 412c2 0 4 0 5 2l3 3c-1 2-2 3-5 4h-2c-2-2-2-3-3-5 1-1 2-3 2-4z" class="B"></path><defs><linearGradient id="AE" x1="288.494" y1="418.537" x2="263.815" y2="419.626" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#d8d8d8"></stop></linearGradient></defs><path fill="url(#AE)" d="M263 412h1v1h1v-2c0-2 3-3 4-5l1 1c5 2 8 4 14 5h0c0 1-1 3-2 4v1s0 1-1 1v1 1c2 3 5 8 8 10l-1 1v1l-3-1-1-1c-1 0-4-1-5-1-2-2-4-4-7-5v-2c-2 0-3-1-4-1h-1c-2-3-3-6-4-9z"></path><defs><linearGradient id="AF" x1="306.412" y1="404.405" x2="336.14" y2="403.938" xlink:href="#B"><stop offset="0" stop-color="#9b9a99"></stop><stop offset="1" stop-color="#c5c4c4"></stop></linearGradient></defs><path fill="url(#AF)" d="M336 392l9 2 4 1s-1 1-2 1-4-1-6 0c-5 3-10 13-12 19 0 2-1 4-1 5 0 4 1 7 1 10h0c1 1 1 2 2 3l1 3c-2 0-4-1-5-2-1-4-5-7-6-11v-5l-1 1-1-1v-1c-2-2-5-3-8-4-2-1-3-2-5-2l2-7c1-3 3-8 5-11 7 0 13 0 20 1 2-1 4 0 5-1h-2v-1z"></path><path d="M336 392l9 2 4 1s-1 1-2 1-4-1-6 0c-5 3-10 13-12 19 0 2-1 4-1 5 0 4 1 7 1 10h0c1 1 1 2 2 3l1 3c-2 0-4-1-5-2-1-4-5-7-6-11v-5c2-2 3-7 4-10 2-4 7-10 11-12 1 0 3-1 4-2h-7c2-1 4 0 5-1h-2v-1z" class="K"></path><path d="M321 418c2-2 3-7 4-10 2-4 7-10 11-12-3 4-6 8-7 12-1 1-2 7-2 8h-2v3h0-2c0 2 2 5 3 7 1 1 2 3 3 4h0c1 1 1 2 2 3l1 3c-2 0-4-1-5-2-1-4-5-7-6-11v-5z" class="G"></path><path d="M315 340c25 6 49 22 67 40l8 8 2 3 2 3c4 6 6 13 9 20 1 2 2 6 4 9 0 1 1 2 1 4l-14-13c-5-3-10-7-15-9-2-2-5-2-7-3-7-3-13-5-20-7h-3l-4-1-9-2v1h2c-1 1-3 0-5 1-7-1-13-1-20-1l-2 1-1-1-2 1-9 1c-4 1-9 2-13 4-1-1-1-1-2-1l10-4h0c1 0 1 0 2-1h1 3c1-1 1-1 2-1h1c1-1 2-3 2-5 0-1-1 0 0-1v-1l-1-2v-1c0-1-1-1-1-2 1 0 1 1 1 1 1 2 2 4 4 5v1l5 4-3-18 1-14c1-2 1-5 2-7 1-1 1-2 1-3h0v-1l1-3c1-1 1-2 2-3l-2-1v-1z" class="N"></path><path d="M309 392c8-1 16-1 24 0h3v1h2c-1 1-3 0-5 1-7-1-13-1-20-1l-2 1-1-1v-1h-1z" class="G"></path><path d="M294 394c0 1 3 0 4 0l11-2h1v1l-2 1-9 1c-4 1-9 2-13 4-1-1-1-1-2-1l10-4z" class="V"></path><path d="M354 374c4 1 7 6 10 9l10 13 6 7c-11-5-20-18-26-29z" class="D"></path><path d="M335 372c1-1 2-3 3-4l1 1 2 1c2 2 3 4 5 6 1 2 3 3 4 6l1 2c1 3 1 4 0 7 0 1-1 2-2 2h-1c1 1 2 1 4 1v1h0-3l-4-1c-1-2-3-5-5-6l-1-2 1-1c0 1 1 1 2 2s2 2 4 3v1h2v-1l1-1c0-4-2-7-4-10-1-1-1-2-2-3h-1c0-2 1-1 0-2-3-2-4-2-7-2z" class="B"></path><path d="M335 372c3 0 4 0 7 2 1 1 0 0 0 2h1c1 1 1 2 2 3 2 3 4 6 4 10l-1 1v1h-2v-1c-2-1-3-2-4-3s-2-1-2-2l-1 1c-2-2-5-6-5-8v-2h1v-4z" class="O"></path><path d="M342 387c-3-3-6-7-6-11v-1c2 0 3 0 4 1 2 1 6 8 6 10v4c-2-1-3-2-4-3z" class="M"></path><path d="M354 374v-1c-1 0-1 0-1-1l-4-9h1v-1c2 2 3 4 5 5h1 2l1 2 2 2h0l1 2h0c1 1 2 2 3 4l1 1 2 4 2 4c1 0 1 1 1 2 1 3 3 5 3 8l-10-13c-3-3-6-8-10-9z" class="O"></path><path d="M333 392l1-1c-1-1-1-2-2-2l1-1c-1-1-2-2-2-3l-5-4c-1-2-1-2 0-4 0-1 0-1 1-2l2-2h2l1-1 1 1 1 3v2c0 2 3 6 5 8l1 2c2 1 4 4 5 6l-9-2h-3z" class="T"></path><path d="M340 388h-1c-3-2-9-6-9-9 1-1 2-1 4-1 0 2 3 6 5 8l1 2z" class="H"></path><defs><linearGradient id="AG" x1="306.367" y1="331.429" x2="255.501" y2="382.609" xlink:href="#B"><stop offset="0" stop-color="#020200"></stop><stop offset="1" stop-color="#2b2e35"></stop></linearGradient></defs><path fill="url(#AG)" d="M234 342v-1c2-1 6-2 8-2 16-4 32-5 48-4l8 1 9 2c2 1 5 1 8 2v1l2 1c-1 1-1 2-2 3l-1 3v1h0c0 1 0 2-1 3-1 2-1 5-2 7l-1 14 3 18-5-4v-1c-2-1-3-3-4-5 0 0 0-1-1-1 0 1 1 1 1 2v1l1 2v1c-1 1 0 0 0 1 0 2-1 4-2 5h-1c-1 0-1 0-2 1h-3-1c-1 1-1 1-2 1h0l-10 4c-3 0-7 1-10 3l-8 4c-1 1-1 1-2 1-1-2-2-5-3-8 0-1-1-3-1-5-1-1-2-4-3-5-2-3-5-6-5-9 0-2-1-4-2-6 0-2-1-4-2-6 0-3-2-6-4-8-4-4-6-9-9-14 0-1-1-2-1-3z"></path><path d="M257 348h3c0 2-2 2-2 3h2v1c-2 0-4 1-5 0v-2l2-2z" class="T"></path><path d="M298 336l9 2c-1 2-2 2-2 4v1c0 1-1 1-1 2h0-4c-2 0-4-1-7-1h-1c2-3 5-4 6-7v-1z" class="B"></path><path d="M308 387l-1-5c-2-4-2-9-2-13l1-1c2 0 3-1 4 0v5l3 18-5-4z" class="S"></path><path d="M264 354c1-2 2-3 3-4 2 0 3 1 4 3h0-1v1c2 2 1 4 2 6 1 1 0 1 0 3 0 0 0 1 1 2 0 2 0 5-1 7 0 2 0 4 1 5v-1c1 1 1 4 1 6-1 3 0 11 2 13 1 0 2 1 2 1-2 0-4-1-7-1v-2-1c-1-3 0-6-1-8-2-5 0-11-1-15s0-10-2-13h0c0-1-1-2-3-2z" class="Q"></path><path d="M264 354c2 0 3 1 3 2h0c2 3 1 9 2 13s-1 10 1 15c1 2 0 5 1 8v1 2c3 0 5 1 7 1h1l-1 2c-2 0-3 0-5 1l-1-1c-2 0-3-1-5-2 0-1-1-1-2-1l-1-12c-1-3 0-5 0-8v-10-5c-1-1-2-2-2-3l2-3z" class="W"></path><path d="M279 369c2-1 3 0 5 1 1 1 1 2 2 3v4c2 0 2-1 3-1v1 3c0 1-1 3-1 5h1 0c-1 3-3 10-6 11h-3l-2 2 1-2c0-1-1-2-1-2-1-5-2-9-2-14h0-1c0-4 0-8 3-11h1z" class="T"></path><path d="M284 380c0 4-3 10-6 14-1-5-2-9-2-14 3 1 5 1 8 0z" class="M"></path><path d="M279 369c3 1 4 1 6 3 0 2 1 4 0 5l-1 3h0c-3 1-5 1-8 0h0-1c0-4 0-8 3-11h1z" class="N"></path><defs><linearGradient id="AH" x1="263.116" y1="370.555" x2="252.485" y2="373.465" xlink:href="#B"><stop offset="0" stop-color="#86888f"></stop><stop offset="1" stop-color="#afb1b4"></stop></linearGradient></defs><path fill="url(#AH)" d="M262 357c0 1 1 2 2 3v5 10c0 3-1 5 0 8l1 12c1 0 2 0 2 1 2 1 3 2 5 2l1 1v1l1 1-8 4c-1 1-1 1-2 1-1-2-2-5-3-8 0-1-1-3-1-5-1-1-2-4-3-5-2-3-5-6-5-9 0-2-1-4-2-6 2-2 4-5 6-8l-2 7c2-2 4-7 5-9l3-6z"></path><path d="M250 373c2-2 4-5 6-8l-2 7c-1 2-1 4-1 6h0l3 4c2 4 4 7 4 11-1-1-2-4-3-5-2-3-5-6-5-9 0-2-1-4-2-6z" class="L"></path><path d="M261 398v-3-1h0v-7c0 1 1 2 1 3s1 2 1 3c1 1 1 2 2 2s2 0 2 1c2 1 3 2 5 2l1 1v1l1 1-8 4c-1 1-1 1-2 1-1-2-2-5-3-8z" class="F"></path><path d="M272 398l1 1v1l1 1-8 4v-1l6-6z" class="P"></path><path d="M449 611c5 2 11 4 16 7 8 5 16 12 22 19 3 2 5 6 8 9l2 2c2 3 3 7 5 11l5 12c1 2 1 5 2 7 2 4 5 8 6 12 2 7 3 14 5 21h-1c-1-2-2-4-2-6v-1c-1-1-1-3-1-4h-1l-1 2c0 1-1 3 0 5v2l-1 1 8 12c4 8 10 18 12 28h0 0c2 2 4 9 6 12-1-3-1-6 0-9 0-2 0-3 1-5l1-1-1 5-1 5c2 2 5 4 9 4h2v-1c1 0 2-1 3-1h1v1l1-1c1 3 0 4 0 7-1 3 0 7 1 10v1l8 26h3c-1 1-1 1-1 2-1 2-1 3-1 6v2c-1 2 0 3-1 5-1 1 0 1-1 2-2-1-2-2-4-3h-1l-2-8c-1 1-1 3-1 5-2-7-5-12-9-18l-9-12c-1 1-1 1-1 2-1 0-2-1-3-2-1 0-2-1-4-2-4-2-8-5-12-8l-7-7v2c-2-1-5-2-7-3-3-2-4-4-7-6 1 2 3 4 4 5 2 1 3 2 4 3 0 1 0 2-1 2-2 0-5-1-7-1s-4 0-6-1c-5 0-10-1-16 0h-1c-5-2-9-4-13-6l-6-3h0l-1 1v-1c-1-1-2-1-3-2-3-2-6-6-9-9l-1-2-2-3c-1-3-6-8-8-10v-1c-1-1-2-2-2-3 1 0 3 0 4 1l-1-2h-5l-1 1-2-1c1-1 2-2 3-2 2-2 5-4 7-6l-4-3-2-1c-1-1-5-2-6-3l-14-9-3-1-1-1v-1c-1-1-1-2-2-3l-1-1c-1-1-1-2-1-4h-2c-2 0-4-1-6 0h-1v-1c0-1 0-1-1-2v-1l-3-3c-3-2-5-7-7-10l-4-8-3-6v-2c-1-1 0-1-1-2h2c2-1 7 0 10 0 1-4 1-7 1-10 1 1 1 1 2 1h1c-2-1-4-3-5-5 6 2 10 2 16 3-1-2-2-3-3-4v-2c-1-2-1-5 1-7-1-1-1 0-1-1-1-2 1-5 2-7 0-2 2-4 3-5h2c4 1 7 3 10 5 1 1 2 1 3 1 3 1 8 4 12 7l3 1 1-1c0-3 1-6 3-9h0c2-4 7-8 10-10 2-1 3-2 5-4z" class="N"></path><path d="M424 677c2 0 3 0 4 2 2 1 4 3 4 5s-1 3-3 5c0 0-1 0-2 1h-2l-1-1c-1-4-2-8 0-12z" class="O"></path><path d="M378 665c3-1 6-2 9-2v1h-1c-1 0-2 0-2 1h1 0c1 0 1 0 2 1h-1 0c2 1 3 2 5 3h1c1 0 2 1 3 1v2c2-1 2-2 4-2h1v1l-2 1 7 4c1 2 3 4 5 6v1 1 1l-1 1v-2c-1-1-1-1-1-2l-1-1c-3-4-6-7-11-8-1 0-1 0-2 1 0 1-1 1-2 2 1-1 1-2 1-3-5-3-10-6-15-8z" class="T"></path><path d="M394 674c1-1 1-1 2-1 5 1 8 4 11 8l1 1c0 1 0 1 1 2v2l1-1 1 2h1v5l1 1c0 1 0 1 1 2l-1 3-3 1-3 1c-1-2 0-3-2-5 1-1 1 0 1-1 2-2 2-4 1-6v-1c0-1 0-2-1-3-2-4-7-8-12-10z" class="Q"></path><path d="M411 687h1v5l1 1c0 1 0 1 1 2l-1 3-3 1c-1-1 0-3 0-4 1-1 1-1 1-2-1-2 0-4 0-6z" class="T"></path><path d="M377 666l1-1c5 2 10 5 15 8 0 1 0 2-1 3 1-1 2-1 2-2 5 2 10 6 12 10 1 1 1 2 1 3v1c1 2 1 4-1 6 0 1 0 0-1 1 2 2 1 3 2 5l-4 2v-1c-1-1-1-2-2-3l-1-1c-1-1-1-2-1-4h-2c-2 0-4-1-6 0h-1v-1c0-1 0-1-1-2v-1l-3-3c-3-2-5-7-7-10l-4-8c1-1 1-2 2-2z" class="P"></path><path d="M375 668c1-1 1-2 2-2 1 3 4 7 6 9 3 3 6 4 6 7l2-2c2 1 5 2 7 4-3 1-6 2-9 2v1l2 1v-1c2-1 6-2 8-3v1c1 3 2 5 3 7-1 1-2 1-3 1h-2c-2 0-4-1-6 0h-1v-1c0-1 0-1-1-2v-1l-3-3c-3-2-5-7-7-10l-4-8z" class="J"></path><path d="M390 692h1v-2c3 0 5 2 8 2v1h-2c-2 0-4-1-6 0h-1v-1z" class="H"></path><path d="M382 644c6 2 10 2 16 3h1c6 5 10 10 18 11l5 1c2 0 4 0 7 1 2 1 5 1 7 2l1-1c1 0 2 1 3 1v2h0c1 0 1 0 1 1l-23-4c-10 0-21 0-31 2-3 0-6 1-9 2l-1 1c-1 0-1 1-2 2l-3-6v-2c-1-1 0-1-1-2h2c2-1 7 0 10 0 1-4 1-7 1-10 1 1 1 1 2 1h1c-2-1-4-3-5-5z" class="C"></path><path d="M418 661h6c1 1 2 0 4 1h1 2 3c-2-1-5-1-8-2-1 0-2-1-4-1h0 0c2 0 4 0 7 1 2 1 5 1 7 2l1-1c1 0 2 1 3 1v2h0c1 0 1 0 1 1l-23-4z" class="G"></path><path d="M383 658c1-4 1-7 1-10 1 1 1 1 2 1h1c6 4 11 6 18 7l4 1c-5 1-11 1-16 1h-10z" class="J"></path><path d="M451 668c25 6 46 22 62 42l8 12c4 8 10 18 12 28h0c-8-14-19-25-31-35-3-2-7-5-11-7-6-3-13-6-19-8l-9-3h-1v-1l2 1h2l6 2c4 1 8 3 12 5 2 0 3 1 5 2-5-5-10-10-12-17v-2l-1-1c-1-1 0-2-1-2-1-2-2-3-2-5l1-1c-4-2-9-4-13-6-3-1-7-2-10-4h0z" class="B"></path><path d="M475 682c3 4 7 8 10 12 3 5 5 9 7 14-4-4-9-9-12-15-2-3-3-7-5-11z" class="F"></path><defs><linearGradient id="AI" x1="404.924" y1="638.802" x2="423.064" y2="627.25" xlink:href="#B"><stop offset="0" stop-color="#898887"></stop><stop offset="1" stop-color="#ececeb"></stop></linearGradient></defs><path fill="url(#AI)" d="M397 626c0-2 2-4 3-5h2c4 1 7 3 10 5 1 1 2 1 3 1 3 1 8 4 12 7 0 0 1 1 1 2 1 2 0 4 0 6l-1 1h0v2 1 1c-1-1-1-1-1-2 0 0-1 0-1 1 1 3 1 7 3 10h1c2 2 3 2 5 4 1 1 2 1 3 1l-1 1c-2-1-5-1-7-2-3-1-5-1-7-1l-5-1c-8-1-12-6-18-11h-1c-1-2-2-3-3-4v-2c-1-2-1-5 1-7-1-1-1 0-1-1-1-2 1-5 2-7z"></path><path d="M417 645l-1-1c-1-1-1-3-2-5 0-1-1-1-1-2v4 1c-1-2 0-3-1-4v-10c1 3 1 6 2 9 2 4 5 8 6 13-1-1-2-4-3-5z" class="J"></path><path d="M412 645c1-1 1-1 2 0h2 1c1 1 2 4 3 5l2 2h-1l-1-1h-1c-4-1-5-3-8-5v-1h1 0z" class="H"></path><path d="M412 645c1-1 1-1 2 0h2 1c1 1 2 4 3 5l2 2h-1l-1-1c-2-3-5-4-8-6z" class="F"></path><path d="M397 626l1 1-1 1 1 1v1c1 1 1 0 1 1 1 1 1 2 2 3l1 1h1l5 6h-1c-4-1-8-4-11-7-1-1-1 0-1-1-1-2 1-5 2-7z" class="M"></path><defs><linearGradient id="AJ" x1="398.739" y1="645.863" x2="403.295" y2="639.326" xlink:href="#B"><stop offset="0" stop-color="#949493"></stop><stop offset="1" stop-color="#b3b2b1"></stop></linearGradient></defs><path fill="url(#AJ)" d="M395 643v-2c-1-2-1-5 1-7 3 3 7 6 11 7 2 1 4 2 5 4h-1v1c3 2 4 4 8 5h1l1 1h1c1 1 1 1 3 2 1 1 1 2 2 3h1c0 2 2 2 4 3h2c1 1 2 1 3 1l-1 1c-2-1-5-1-7-2-3-1-5-1-7-1l-5-1c-8-1-12-6-18-11h-1c-1-2-2-3-3-4z"></path><path d="M417 658c0-1-1-1-2-2h0c0-1-3-3-5-4l-4-4c-1-1-3-3-5-4v-1c4 0 8 4 11 6 1 1 2 3 4 4l6 1 1-1-2-1h1c1 1 1 1 3 2 1 1 1 2 2 3h1c0 2 2 2 4 3h2c1 1 2 1 3 1l-1 1c-2-1-5-1-7-2-3-1-5-1-7-1l-5-1z" class="J"></path><path d="M449 611c5 2 11 4 16 7 8 5 16 12 22 19 3 2 5 6 8 9l2 2c2 3 3 7 5 11l5 12c1 2 1 5 2 7 2 4 5 8 6 12 2 7 3 14 5 21h-1c-1-2-2-4-2-6v-1c-1-1-1-3-1-4h-1l-1 2c0 1-1 3 0 5v2l-1 1c-16-20-37-36-62-42-2-1-5-2-7-2l-3-1c0-1 0-1-1-1h0v-2c-1 0-2-1-3-1s-2 0-3-1c-2-2-3-2-5-4h-1c-2-3-2-7-3-10 0-1 1-1 1-1 0 1 0 1 1 2v-1-1-2h0l1-1c0-2 1-4 0-6 0-1-1-2-1-2l3 1 1-1c0-3 1-6 3-9h0c2-4 7-8 10-10 2-1 3-2 5-4z" class="K"></path><path d="M440 662c2 0 3 1 4 2v2l-3-1c0-1 0-1-1-1h0v-2z" class="E"></path><path d="M444 654c1 2 1 4 1 6 0 1 1 2 2 2v1h1 1l3 2h1l-4-2v-1c0-1 0-2-1-2v1h-1c-1-1-2-1-2-3v-2c1-1 1-2 1-3v-1-2l1-1v-1c1 1 0 1 0 2s2 3 2 4v1 2 1l1 1 1-1-1-1v-2l1 2h1c1 2 0 4 1 5h1c2 0 3 2 5 4h-2 0l1 1c-4 0-11-4-14-6v-7z" class="C"></path><path d="M441 660c-4-2-10-5-14-9v-5c1-4 3-9 6-12l3-4v1l-1 1-2 3 1 1 1 1h1s0 1 1 1h-1c-1 0-2-1-3-2 0 3 0 8 2 10 3 4 3 10 6 14z" class="L"></path><path d="M441 660c-3-4-3-10-6-14-2-2-2-7-2-10 1 1 2 2 3 2h1c1 1 2 1 2 2h1l2 2c1 0 1 0 2 1l-1 1c1 1 2 1 3 2l-1 1v1h0l-1 2v2h0 1v2h-1v7l-3-1z" class="E"></path><path d="M428 695c9-1 17 0 26 0 2 1 4 1 6 2h2 1l9 3c6 2 13 5 19 8 4 2 8 5 11 7 12 10 23 21 31 35h0c2 2 4 9 6 12-1-3-1-6 0-9 0-2 0-3 1-5l1-1-1 5-1 5c2 2 5 4 9 4h2v-1c1 0 2-1 3-1h1v1l1-1c1 3 0 4 0 7-1 3 0 7 1 10v1l8 26h3c-1 1-1 1-1 2-1 2-1 3-1 6v2c-1 2 0 3-1 5-1 1 0 1-1 2-2-1-2-2-4-3h-1l-2-8c-1 1-1 3-1 5-2-7-5-12-9-18l-9-12c-1 1-1 1-1 2-1 0-2-1-3-2-1 0-2-1-4-2-4-2-8-5-12-8l-7-7v2c-2-1-5-2-7-3-3-2-4-4-7-6 1 2 3 4 4 5 2 1 3 2 4 3 0 1 0 2-1 2-2 0-5-1-7-1s-4 0-6-1c-5 0-10-1-16 0h-1c-5-2-9-4-13-6l-6-3h0l-1 1v-1c-1-1-2-1-3-2-3-2-6-6-9-9l-1-2-2-3c-1-3-6-8-8-10v-1c-1-1-2-2-2-3 1 0 3 0 4 1l-1-2h-5l-1 1-2-1c1-1 2-2 3-2 2-2 5-4 7-6l-4-3-2-1c-1-1-5-2-6-3l-14-9-3-1-1-1 4-2 3-1 3-1 15-3z" class="I"></path><path d="M454 695c2 1 4 1 6 2h-4 0c-1 0-4 0-5-1 1 0 2-1 3-1z" class="C"></path><path d="M523 761l2-2c2 4 6 8 7 12v1l-9-11z" class="K"></path><path d="M513 747c1 0 2 0 3 1 4 3 6 8 9 11l-2 2-4-4c0-1 0-2-1-2-1-3-4-5-5-8z" class="E"></path><path d="M476 718v-1h1c1 1 0 1 2 1 1 1 2 2 4 3 0 1 1 1 2 2h1l1 1 1 1 1 1h1l1 1h1c0 1 1 1 2 2l1 1h1c0-1-3-3-2-4v1l9 6c4 3 5 7 7 11 1 0 1 0 1 1-1 0-1 0-2 1v-1a30.44 30.44 0 0 0-8-8c-6-8-16-13-25-19z" class="G"></path><path d="M443 704c3 0 5 3 7 4 1 1 2 2 3 2 2 1 3 2 5 3-1-1-2-2-4-3l-7-6-2-2h0c-1-1-1-2-1-2-1-1-1 0-1-1h2v1c1 0 1 0 2 1 0 1 1 1 2 2l1 1c1 1 3 1 4 3l1 1h0c3 3 6 4 9 6 4 3 8 6 13 9v1c-6-3-12-6-17-9-6-3-12-6-17-11z" class="J"></path><path d="M510 747h1v-1l2 1h0c1 3 4 5 5 8 1 0 1 1 1 2l4 4 9 11c1 5 7 9 9 13l3 6c1 2 2 4 2 5l-9-12c-1 1-1 1-1 2-1 0-2-1-3-2 0-2-3-5-5-7-3-4-5-8-8-11-1 0-3 0-4-1-1-2-2-3-3-5l-5-6-1-4 1-2 2 1v-2z" class="H"></path><path d="M515 756h1l3 6v1c-2-2-3-5-4-7z" class="L"></path><path d="M519 762l18 22c-1 1-1 1-1 2-1 0-2-1-3-2 0-2-3-5-5-7-3-4-5-8-8-11l-1-3v-1z" class="V"></path><defs><linearGradient id="AK" x1="505.566" y1="754.016" x2="519.853" y2="758.257" xlink:href="#B"><stop offset="0" stop-color="#838485"></stop><stop offset="1" stop-color="#a5a3a2"></stop></linearGradient></defs><path fill="url(#AK)" d="M507 750l1-2 2 1c1 2 3 5 4 7h1c1 2 2 5 4 7l1 3c-1 0-3 0-4-1-1-2-2-3-3-5l-5-6-1-4z"></path><path d="M450 704c-1-2-1-4-3-5h-1v-1h1s2 1 2 2l7 5c7 5 14 9 20 13 9 6 19 11 25 19-1 0-5-2-5-3-5-3-9-5-15-5-1 0-1 0-2-1 0 0-1 0-2 1-1-1-3-1-5-1l-3 3v-2c-5-1-9-3-13-5-3-1-6-3-9-5l1-1h4l1 1h0l4 1c6 2 11 5 17 6h5v-1c-1 0-2-1-2-1v-1c-5-3-9-6-13-9-3-2-6-3-9-6h0l-1-1c-1-2-3-2-4-3z" class="M"></path><path d="M479 706c2 1 4 1 7 2v1c2 1 4 2 5 3 5 3 10 5 14 9l9 9 8 8c4 6 7 14 11 20s7 11 9 18l1 3v5c1 1 1 1 1 2h-1c-1-3-1-6-3-8v-1c-2-3-4-5-6-8h0l-1-1c-2-2-3-4-4-7-2-6-6-10-9-15-9-13-21-25-34-34-2-2-5-4-7-6z" class="C"></path><path d="M486 712l2-1c5 3 12 6 16 12 4 7 10 11 14 18 1 0 2 2 2 3v2c-9-13-21-25-34-34z" class="V"></path><defs><linearGradient id="AL" x1="464.639" y1="701.743" x2="423.361" y2="723.257" xlink:href="#B"><stop offset="0" stop-color="#656668"></stop><stop offset="1" stop-color="#8c8b8f"></stop></linearGradient></defs><path fill="url(#AL)" d="M413 698l15-3 3 1-2 1c6 0 10 3 14 7 5 5 11 8 17 11 5 3 11 6 17 9 0 0 1 1 2 1v1h-5c-6-1-11-4-17-6l-4-1h0l-1-1h-4c1-2 1-2 0-4-2-2-3-2-6-2l4-1-15-5c-3 1-5 0-8 0-6-2-10-3-16-2l-3-1-1-1 4-2 3-1 3-1z"></path><path d="M413 698l15-3 3 1-2 1c-6 1-14 1-19 4h-1c8 1 15 3 22 5-3 1-5 0-8 0-6-2-10-3-16-2l-3-1-1-1 4-2 3-1 3-1z" class="D"></path><path d="M460 697h2 1l9 3c6 2 13 5 19 8 4 2 8 5 11 7 12 10 23 21 31 35h0c2 2 4 9 6 12-1-3-1-6 0-9 0-2 0-3 1-5l1-1-1 5-1 5c2 2 5 4 9 4h2c0 1-1 2 0 3v1h0c-2 3-4 8-5 11l-1 1 5 14c1 1 2 4 2 5v1c-3-6-6-12-8-18l-1-3c-2-7-5-12-9-18s-7-14-11-20l-8-8-9-9c-4-4-9-6-14-9-1-1-3-2-5-3v-1c-3-1-5-1-7-2-4-2-8-4-12-5l-11-4h4z" class="K"></path><defs><linearGradient id="AM" x1="541.377" y1="770.579" x2="544.437" y2="758.78" xlink:href="#B"><stop offset="0" stop-color="#727173"></stop><stop offset="1" stop-color="#9b9a9b"></stop></linearGradient></defs><path fill="url(#AM)" d="M533 750c2 2 4 9 6 12-1-3-1-6 0-9 0-2 0-3 1-5l1-1-1 5-1 5c2 2 5 4 9 4h2c0 1-1 2 0 3v1h0c-2 3-4 8-5 11l-1 1c-2-6-5-12-7-18-2-3-3-5-4-9z"></path><defs><linearGradient id="AN" x1="551.461" y1="796.45" x2="563.186" y2="791.953" xlink:href="#B"><stop offset="0" stop-color="#979798"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#AN)" d="M550 760c1 0 2-1 3-1h1v1l1-1c1 3 0 4 0 7-1 3 0 7 1 10v1l8 26h3c-1 1-1 1-1 2-1 2-1 3-1 6v2c-1 2 0 3-1 5-1 1 0 1-1 2-2-1-2-2-4-3h-1l-2-8v-1c-2-4-3-8-5-12 0-1-1-4-2-5l-5-14 1-1c1-3 3-8 5-11h0v-1c-1-1 0-2 0-3v-1z"></path><path d="M550 760c1 0 2-1 3-1h1c-1 2-2 4-2 5s-1 2-1 2c-5 14 8 35 5 42-2-4-3-8-5-12 0-1-1-4-2-5l-5-14 1-1c1-3 3-8 5-11h0v-1c-1-1 0-2 0-3v-1z" class="R"></path><defs><linearGradient id="AO" x1="506.646" y1="739.125" x2="499.281" y2="773.1" xlink:href="#B"><stop offset="0" stop-color="#777878"></stop><stop offset="1" stop-color="#afacad"></stop></linearGradient></defs><path fill="url(#AO)" d="M469 731l3-3c2 0 4 0 5 1 1-1 2-1 2-1 1 1 1 1 2 1 6 0 10 2 15 5 0 1 4 3 5 3a30.44 30.44 0 0 1 8 8v1l1 1v2l-2-1-1 2 1 4 5 6c1 2 2 3 3 5 1 1 3 1 4 1 3 3 5 7 8 11 2 2 5 5 5 7-1 0-2-1-4-2-4-2-8-5-12-8l-7-7c-1-1-3-4-4-5-7-7-14-13-22-19-3-1-6-3-9-5h0c-2 0-4-1-5-2-1-2-1-3-1-5z"></path><path d="M479 730c3 1 4 2 5 4h-1c-2 2-3 3-5 4l-1-1c1-3 2-4 2-7z" class="M"></path><path d="M516 765c1 1 3 1 4 1 3 3 5 7 8 11 2 2 5 5 5 7-1 0-2-1-4-2v-1c-3-4-5-7-10-10-2-1-4-3-6-5 2 0 3 2 5 3 0-1-2-2-2-4h0z" class="H"></path><path d="M477 729c1-1 2-1 2-1 1 1 1 1 2 1l1 1c2 2 5 3 7 5 3 2 8 7 10 9 0 2 1 4 1 5l-16-15c-1-2-2-3-5-4l-2-1z" class="E"></path><path d="M469 731l3-3c2 0 4 0 5 1l2 1c0 3-1 4-2 7l-2 1c-2 0-4-1-5-2-1-2-1-3-1-5z" class="N"></path><path d="M499 744l1 3c1 0 1-1 2-1 2 1 4 5 6 8l5 6c1 2 2 3 3 5h0c0 2 2 3 2 4-2-1-3-3-5-3l-13-17c0-1-1-3-1-5z" class="C"></path><path d="M481 729c6 0 10 2 15 5 0 1 4 3 5 3a30.44 30.44 0 0 1 8 8v1l1 1v2l-2-1-1 2 1 4c-2-3-4-7-6-8-1 0-1 1-2 1l-1-3c-2-2-7-7-10-9-2-2-5-3-7-5l-1-1z" class="R"></path><path d="M506 749v-4h3v1l1 1v2l-2-1-1 2h0l-1-1z" class="Z"></path><path d="M482 730c2 0 4 1 6 3 6 2 10 7 15 9 1 3 2 5 3 7l1 1h0l1 4c-2-3-4-7-6-8-1 0-1 1-2 1l-1-3c-2-2-7-7-10-9-2-2-5-3-7-5z" class="H"></path><path d="M407 704c6-1 10 0 16 2 3 0 5 1 8 0l15 5-4 1c3 0 4 0 6 2 1 2 1 2 0 4l-1 1c3 2 6 4 9 5 4 2 8 4 13 5v2c0 2 0 3 1 5 1 1 3 2 5 2h0c3 2 6 4 9 5 8 6 15 12 22 19 1 1 3 4 4 5v2c-2-1-5-2-7-3-3-2-4-4-7-6 1 2 3 4 4 5 2 1 3 2 4 3 0 1 0 2-1 2-2 0-5-1-7-1v-1c-2-1-4-3-6-4-4-3-8-7-12-10l-6-5h0v-1c-10-11-24-16-35-25l-4-3-4-3-2-1c-1-1-5-2-6-3l-14-9z" class="L"></path><path d="M442 712c3 0 4 0 6 2 1 2 1 2 0 4l-1 1-3 1c-1 0-2 0-3-1s-2-2-2-3c1-2 2-3 3-4z" class="B"></path><defs><linearGradient id="AP" x1="483.661" y1="761.287" x2="489.339" y2="747.846" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#aba9a9"></stop></linearGradient></defs><path fill="url(#AP)" d="M472 748c0-1-2-3-1-4s1-2 3-2c3-1 5 2 7 3l15 15c1 2 3 4 4 5 2 1 3 2 4 3 0 1 0 2-1 2-2 0-5-1-7-1v-1c-2-1-4-3-6-4-4-3-8-7-12-10l-6-5h0v-1z"></path><path d="M500 765c2 1 3 2 4 3 0 1 0 2-1 2-2 0-5-1-7-1v-1c2-1 4 0 6-1l-2-2z" class="R"></path><path d="M433 720l4 3c11 9 25 14 35 25v1h0l6 5c4 3 8 7 12 10 2 1 4 3 6 4v1c-2 0-4 0-6-1-5 0-10-1-16 0h-1c-5-2-9-4-13-6l-6-3h0l-1 1v-1c-1-1-2-1-3-2-3-2-6-6-9-9l-1-2-2-3c-1-3-6-8-8-10v-1c-1-1-2-2-2-3 1 0 3 0 4 1l-1-2h-5l-1 1-2-1c1-1 2-2 3-2 2-2 5-4 7-6z" class="F"></path><path d="M433 720l4 3c-4 1-8 3-11 5l-1 1-2-1c1-1 2-2 3-2 2-2 5-4 7-6z" class="U"></path><path d="M472 751c1 1 1 2 3 2 1 0 1 1 1 1h2c4 3 8 7 12 10 2 1 4 3 6 4v1c-2 0-4 0-6-1v-1c-8-2-16-7-22-12h0 1v-1-1c1-1 2-1 3-2z" class="I"></path><path d="M430 733v-1c-1-1-2-2-2-3 1 0 3 0 4 1 1 2 4 3 5 5l12 13 4 4v2l-3 3c-3-2-6-6-9-9l-1-2-2-3c-1-3-6-8-8-10z" class="D"></path><defs><linearGradient id="AQ" x1="453.298" y1="724.493" x2="456.534" y2="756.27" xlink:href="#B"><stop offset="0" stop-color="#9a999a"></stop><stop offset="1" stop-color="#b5b4b3"></stop></linearGradient></defs><path fill="url(#AQ)" d="M468 755c-2-1-4-3-6-4-7-6-12-11-18-17-2-1-5-3-5-5-1-1-1-2 0-3 1 0 2 0 2 1 5 6 11 10 17 15 3 2 6 5 9 7 2-1 2 0 3 0 1-1 1-1 2 0l6 5h-2s0-1-1-1c-2 0-2-1-3-2-1 1-2 1-3 2v1 1h-1 0z"></path><path d="M470 749c1-1 1-1 2 0l6 5h-2s0-1-1-1c-2 0-2-1-3-2l-1-1c-2 1-2 2-5 2 2-1 3-2 4-3h0z" class="L"></path><defs><linearGradient id="AR" x1="364.115" y1="542.099" x2="450.133" y2="525.347" xlink:href="#B"><stop offset="0" stop-color="#b5b4b3"></stop><stop offset="1" stop-color="#f6f6f6"></stop></linearGradient></defs><path fill="url(#AR)" d="M337 451h3l12 2c35 6 65 23 86 51l6 10 4 12c1 2 1 4 3 6l-1 1 2 3c1 1 2 2 2 3l21 51c0 2 1 3 2 5l6 15c0 2 1 3 0 5 3 6 5 11 7 17 1 2 2 5 3 8l2 6c-3-3-5-7-8-9-6-7-14-14-22-19-5-3-11-5-16-7-2 2-3 3-5 4-3 2-8 6-10 10h0c-2 3-3 6-3 9l-1 1-3-1c-4-3-9-6-12-7-1 0-2 0-3-1-3-2-6-4-10-5h-2c-1 1-3 3-3 5-1 2-3 5-2 7 0 1 0 0 1 1-2 2-2 5-1 7v2c1 1 2 2 3 4-6-1-10-1-16-3v-1c-1 1-1 1-2 1l-1-1h-3v-1c-5-2-10-5-14-8 0-1-1-2 0-3h0l-2-4c0-2 0-2 1-4 1-1 1-2 2-3h0c-3 1-4 3-6 5 0-2 0-2 1-4l3-2 1-1c-2-3-2-6-1-9v-1c0-1-1-1-1-1l-1-1v-5c0-2 0-2 1-3 1-3 1-7 1-9-1 0-1 0-2-1h-1c-2-1-2-1-3-3 0-2 0-3 2-4l-1-1c0-2-5-3-7-3l-1 1-2 2-2 1-1 1 1-2c-1-1-2-2-3-2 0 0-1 0-1-1-1-1-3-1-4-2s-3-2-4-3l-2-8h0c-1-1-1-1-1-2v-1l-1-1c-1-3-2-6-3-10-1-2-4-4-4-6 0-1 0-1-1-1-2-5-3-11-5-16-1-3-4-6-6-9-2-4-2-9-4-14-1-2-3-4-4-6v-1c0-1 0-2 1-3v-2c0-1 1-2 1-4 1-2 2-5 4-7 0-1 1-2 1-2l1-2 1-1 3 2 3-1 2-1 1-1v-1l-1-4h1c-1-5 7-11 10-15 1-1 3-2 3-4h5z"></path><path d="M425 563c-1-2-2-4-2-5-1-6-1-12 1-16h1l-2 4c1 1 1 1 2 1 1 1 1 0 1 1-1 0-2-1-3 0 1 3 2 5 4 8v1h0c1 3 3 7 5 9-2 0-5-3-7-3z" class="I"></path><path d="M399 540l6 9c3 3 8 6 9 10l-15-7c-1-4-1-8 0-12z" class="J"></path><path d="M383 523h1c-1 2-2 2-1 4 0 1 0 2 1 2 1 1 2 1 2 1-1 0-2 0-3 1 0 5 5 11 8 15 1 1 2 2 2 3-4-2-11-6-13-10-1-5 1-12 3-16z" class="H"></path><path d="M398 551l-1-1-1-1c-1-3-4-6-6-9l-1-2-4-6-1-1c1 0 3 1 4 1 2 1 5 2 7 2 1 0 1 0 2 1h1l1-2h0l1 1h0c0-1 1-1 1-2v-1h1l1-1h0 1 0l-1 1c-2 2-4 6-4 9h0c-1 4-1 8 0 12 0 0-1 0-1-1z" class="C"></path><path d="M366 504h1 8c-2 1-5 1-8 1-1 3-1 4-1 7 1 2 1 4 2 7 1 1 1 2 2 3s2 2 2 3h0c-1 1-7-2-10-3-2-1-5-2-8-3 2-4 4-10 7-13 2-2 3-2 5-2z" class="F"></path><defs><linearGradient id="AS" x1="340.638" y1="512.621" x2="359.672" y2="506.637" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#cdcccc"></stop></linearGradient></defs><path fill="url(#AS)" d="M353 503l8-1h8c-1 1-9 1-11 1h-1s1 1 2 1v1c0 1 0 1-1 2s-2 1-2 2l-2 4c-1 1-2 2-4 3h-4c0-1-3-1-3-2-1-1-3-3-3-4v-2c1-1 1-1 2-1l3-1c1 0 1 0 2-1l-1-1s6-1 7-1z"></path><path d="M359 504c1 0 5 0 6-1h11 4c-2 2-13 0-14 1-2 0-3 0-5 2-3 3-5 9-7 13 0 2 0 5 1 7s1 4 2 6c1 0 3 3 3 4-1 1-1 1-1 3 1 1 2 3 3 3 8 7 19 11 28 14-1 0-2 0-4-1h-2v1c-2 0-4-1-6-1s-3 0-4-1c2 0 4 1 5 0v-1h-2c-3-2-6-3-9-4-6-3-11-7-15-12v-1h2c0-2-2-4-3-6-1-4-3-7-5-11l-1-3h4c2-1 3-2 4-3l2-4c0-1 1-1 2-2s1-1 1-2v-1z" class="C"></path><defs><linearGradient id="AT" x1="360.903" y1="536.049" x2="368.15" y2="525.99" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#bcbbba"></stop></linearGradient></defs><path fill="url(#AT)" d="M354 519l8 3c3 1 9 4 10 3s0-2 1-3c1 1 1 2 1 4h-2v1c0 1 0 2 1 4h0c0 1 1 2 1 3l1 4c1 3 4 6 6 8 3 2 4 2 6 4s5 3 7 3c0 0 1 1 2 1v1h0l-4 1h-2c-9-3-20-7-28-14-1 0-2-2-3-3 0-2 0-2 1-3 0-1-2-4-3-4-1-2-1-4-2-6s-1-5-1-7z"></path><path d="M362 522c3 1 9 4 10 3s0-2 1-3c1 1 1 2 1 4h-2v1c0 1 0 2 1 4h0c0 1 1 2 1 3l1 4c-1-2-3-4-4-6s-1-3-2-4c-2-3-4-4-7-6z" class="I"></path><path d="M360 536c8 8 21 14 33 18l3 1h0l-4 1h-2c-9-3-20-7-28-14-1 0-2-2-3-3 0-2 0-2 1-3z" class="D"></path><path d="M301 497c1 1 2 1 3 1l5 1h3c2 0 4 1 6 2v2c-1 1-2 1-3 2h0c1 1 4 1 6 1h3l1 1c0 1 0 1 1 2 0 1 0 1 1 2h0v-1l3-1 4-1 1-1 8-2 3-1 1 1c-1 1-1 1-2 1l-3 1c-1 0-1 0-2 1v2c0 1 2 3 3 4 0 1 3 1 3 2l1 3c2 4 4 7 5 11 1 2 3 4 3 6h-2v1c4 5 9 9 15 12 3 1 6 2 9 4h2v1c-1 1-3 0-5 0 1 1 2 1 4 1s4 1 6 1v-1h2c2 1 3 1 4 1h2l4-1h0v-1c-1 0-2-1-2-1-2 0-5-1-7-3s-3-2-6-4c-2-2-5-5-6-8l-1-4v-7l2-1c1 0 1-5 1-6 1-2 2-6 4-7 0 1 0 1-1 2-1 3-1 4-1 7 0 1-1 1 0 2v1h-1v2h2 0 0v-1h1c0-2 2-4 2-5h0c1-1 1-2 2-2 1-3 4-7 7-9 1 0 1 0 2-1h1l1-1v-1h1 1c-1 1-1 1-2 1l-1 1c-5 3-9 8-12 13v1c-2 4-4 11-3 16 2 4 9 8 13 10 2 1 3 2 5 2 0 1 1 1 1 1l15 7c3 2 8 3 11 4 2 0 5 3 7 3-2-2-4-6-5-9h0c1 0 2 1 3 1l1 2h0 0l1 2c1 1 2 2 3 4 1 1 2 2 3 4l1 1 1 1h1c1 1 1 2 2 2h1l-3-3v-1c0-1-1-2-1-2 0-12 7-21 14-29l21 51c0 2 1 3 2 5l6 15c0 2 1 3 0 5l-14-17c-13-14-30-26-48-32-16-7-34-9-51-9-6 0-11 0-16 1l-12 2c-4 1-8 2-12 4h0c-1-1-1-1-1-2v-1l-1-1c-1-3-2-6-3-10-1-2-4-4-4-6 0-1 0-1-1-1-2-5-3-11-5-16-1-3-4-6-6-9-2-4-2-9-4-14-1-2-3-4-4-6v-1z" class="E"></path><path d="M470 592c2 1 2 2 3 4h-1v1c-1-1-2-1-2-2-1-2-1-2 0-3z" class="C"></path><path d="M396 555c4 1 8 3 12 3h-1-3-1l-1 1c-1 0-1-1-2-1l-8-2 4-1z" class="Y"></path><path d="M361 555l6 1h-4 3c1 0 1 0 2 1h2c-6 0-11 0-16 1l-12 2v-1l1 1 1-1h1 0-3c-1-1-1-1-1-2h1 8c3-1 8 0 11-2z" class="I"></path><defs><linearGradient id="AU" x1="348.602" y1="543.816" x2="353.65" y2="535.256" xlink:href="#B"><stop offset="0" stop-color="#8b8b8b"></stop><stop offset="1" stop-color="#abaaa8"></stop></linearGradient></defs><path fill="url(#AU)" d="M334 526c2 1 3 1 5 1l2 1v1h1c3 1 6 3 9 5l2 2v1c4 5 9 9 15 12 3 1 6 2 9 4h2v1c-1 1-3 0-5 0-4-1-8-1-12-2-9-3-16-9-23-16-2-3-4-6-5-10z"></path><defs><linearGradient id="AV" x1="332.018" y1="524.059" x2="315.158" y2="533.397" xlink:href="#B"><stop offset="0" stop-color="#a5a4a5"></stop><stop offset="1" stop-color="#dcdcdb"></stop></linearGradient></defs><path fill="url(#AV)" d="M301 497c1 1 2 1 3 1l5 1h3c2 0 4 1 6 2v2c-1 1-2 1-3 2h0c1 1 4 1 6 1h3l1 1c0 1 0 1 1 2 0 1 0 1 1 2h0v-1l3-1 4-1 1-1 8-2 3-1 1 1c-1 1-1 1-2 1l-3 1c-1 0-1 0-2 1v2c0 1 2 3 3 4 0 1 3 1 3 2l1 3c2 4 4 7 5 11 1 2 3 4 3 6h-2l-2-2c-3-2-6-4-9-5h-1v-1l-2-1c-2 0-3 0-5-1 1 4 3 7 5 10v1c1 2 2 3 2 5l1 1h0c4 4 7 6 11 10 2 1 5 2 8 2-3 2-8 1-11 2h-8-1c0 1 0 1 1 2h3 0-1l-1 1-1-1v1c-4 1-8 2-12 4h0c-1-1-1-1-1-2v-1l-1-1c-1-3-2-6-3-10-1-2-4-4-4-6 0-1 0-1-1-1-2-5-3-11-5-16-1-3-4-6-6-9-2-4-2-9-4-14-1-2-3-4-4-6v-1z"></path><path d="M309 499h3v1 1c1 0 0 0 1 1h1c-1 0-2 0-3-1-1 0-1-1-2-1h-1l1-1z" class="L"></path><path d="M319 515l-3 6c-2-2-3-4-3-7 0-1-2-3-2-4h0v-2l2-1 1 1v1c1 0 2 0 3 2l-1 2h-2v1c2 0 3 1 5 1z" class="I"></path><path d="M350 557l-3-1c-2-1-9-4-10-6 4-3 11 2 16 3h0c2 1 5 2 8 2-3 2-8 1-11 2z" class="R"></path><path d="M314 508h1v-1c-1 0-2 0-3-1-1 0-1 0-1-1h4c1 1 4 1 6 1h3l1 1c0 1 0 1 1 2 0 1 0 1 1 2l-8 4c-2 0-3-1-5-1v-1h2l1-2c-1-2-2-2-3-2v-1z" class="J"></path><defs><linearGradient id="AW" x1="324.594" y1="511.476" x2="318.458" y2="512.067" xlink:href="#B"><stop offset="0" stop-color="#63626b"></stop><stop offset="1" stop-color="#79797c"></stop></linearGradient></defs><path fill="url(#AW)" d="M324 506l1 1c0 1 0 1 1 2 0 1 0 1 1 2l-8 4c-2 0-3-1-5-1v-1h2c2-1 3-1 5-3 2-1 2-1 3-4z"></path><path d="M346 504l1 1c-1 1-1 1-2 1l-3 1c-1 0-1 0-2 1v2c0 1 2 3 3 4 0 1 3 1 3 2l1 3c2 4 4 7 5 11 1 2 3 4 3 6h-2l-2-2c-3-2-6-4-9-5h-1v-1l-2-1c-2 0-3 0-5-1 1 4 3 7 5 10v1c1 2 2 3 2 5l1 1h0v3c-2 1-4 2-6 2 0-1-1-2-1-2 0-3-1-5-2-7l-2-5-1-1c-1-1-1-2-1-2l-1-1c-1-1-1-3-3-3-1-1-3-3-4-5-1-1-1-2-2-2 0-2 1-3 2-4l2-2c1 0 1-1 2-1l2-2v-1l3-1 4-1 1-1 8-2 3-1z" class="J"></path><path d="M342 529c0-1-1-2 0-3 1 0 0 0 1-1v1c1 0 2 0 3 1v1c2 1 4 5 5 6-3-2-6-4-9-5z" class="S"></path><path d="M334 525v1c1 4 3 7 5 10v1c1 2 2 3 2 5l1 1c-5-4-8-11-11-17l3-1z" class="G"></path><path d="M331 526l-1 1h-1c-2-1-8-5-8-7 0-1 0-1 2-2l2 2h0 3c2 2 3 2 6 1 0 2-1 3 0 4l-3 1z" class="M"></path><path d="M334 521c1-1 2-2 3-2 2 0 3 0 4 1s1 2 1 4c-1 2-2 3-3 3-2 0-3 0-5-1v-1c-1-1 0-2 0-4z" class="B"></path><path d="M337 451h3l12 2c35 6 65 23 86 51l6 10 4 12c1 2 1 4 3 6l-1 1c-4-4-9-8-14-12-19-12-41-19-64-20-3 0-7 0-11 1l-8 1c-1 0-7 1-7 1l-3 1-8 2-1 1-4 1-3 1v1h0c-1-1-1-1-1-2-1-1-1-1-1-2l-1-1h-3c-2 0-5 0-6-1h0c1-1 2-1 3-2v-2c-2-1-4-2-6-2h-3l-5-1c-1 0-2 0-3-1 0-1 0-2 1-3v-2c0-1 1-2 1-4 1-2 2-5 4-7 0-1 1-2 1-2l1-2 1-1 3 2 3-1 2-1 1-1v-1l-1-4h1c-1-5 7-11 10-15 1-1 3-2 3-4h5z" class="N"></path><path d="M369 492c-1-1-3-3-3-4-1-4-1-7 1-10 0 0 1 1 2 1h0 1l1-1h5v6c-1-1-1-1-2-1h-4c-2 2-2 2-2 4s1 3 1 5h0z" class="Q"></path><path d="M369 492h0c0-2-1-3-1-5s0-2 2-4h4c1 0 1 0 2 1-1 6-1 12 1 17-3-3-6-5-8-9z" class="S"></path><defs><linearGradient id="AX" x1="360.172" y1="456.098" x2="320.248" y2="485.39" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2e36"></stop></linearGradient></defs><path fill="url(#AX)" d="M337 451h3l12 2h1c1 1 2 1 3 1s2 0 3 1h0c-1 2-3 3-4 5l-1 2c-1 1-3 4-4 5l-4 5c1-1 1-1 2-1v1l-4 4c1 2 2 2 3 4 1 1 1 0 1 1 1 2 2 3 2 5l1 1c0 2-1 4-1 6 0 1 0 2-1 3v3l1 1-1 1h0v2l1-1c1 0 2 1 3 0v1c-1 0-7 1-7 1l-3 1-8 2-1 1-4 1-3 1v1h0c-1-1-1-1-1-2-1-1-1-1-1-2l-1-1h-3c-2 0-5 0-6-1h0c1-1 2-1 3-2v-2c-2-1-4-2-6-2h-3l-5-1c-1 0-2 0-3-1 0-1 0-2 1-3v-2c0-1 1-2 1-4 1-2 2-5 4-7 0-1 1-2 1-2l1-2 1-1 3 2 3-1 2-1 1-1v-1l-1-4h1c-1-5 7-11 10-15 1-1 3-2 3-4h5z"></path><path d="M322 474v-1-1c2 2 3 3 4 6v1c-1 0 0 0-1-1h0l-1 1h0l-2-5z" class="Q"></path><path d="M345 467h0v3 1 1c-1 1-2 2-4 2h-2v-2c1-2 4-4 6-5z" class="B"></path><path d="M325 482l1 3v1c0-2 0-4 1-6v-1c1 1 1 2 1 4v2c-1 1-1 2 0 3v1 2 8c2 1 3 0 5 0h0 2 0c1 1 2 1 3 1 1 1 1 2 2 4h2c1 0 1 0 1 1l-8 2-1 1-4 1-3 1v1h0c-1-1-1-1-1-2s0-2 1-3c-1-1-2-1-2-2l1-1c1-2 0-2-1-3h1c-1-5-1-12-1-18z" class="Q"></path><path d="M329 500h4c0 3-1 6 1 8l-4 1v-1c0-3-1-5-1-8z" class="O"></path><path d="M326 500h3c0 3 1 5 1 8v1l-3 1v1h0c-1-1-1-1-1-2s0-2 1-3c-1-1-2-1-2-2l1-1c1-2 0-2-1-3h1z" class="W"></path><path d="M327 506c1 2 0 3 0 4v1h0c-1-1-1-1-1-2s0-2 1-3z" class="X"></path><path d="M333 499h0 2 0c1 1 2 1 3 1 1 1 1 2 2 4h2c1 0 1 0 1 1l-8 2h-1v-4c0-2 0-2-1-4z" class="T"></path><defs><linearGradient id="AY" x1="342.911" y1="493.767" x2="338.581" y2="493.288" xlink:href="#B"><stop offset="0" stop-color="#4f4f56"></stop><stop offset="1" stop-color="#67696f"></stop></linearGradient></defs><path fill="url(#AY)" d="M338 500c0-8 1-14 4-21l5 1c1 1 1 0 1 1 1 2 2 3 2 5l1 1c0 2-1 4-1 6 0 1 0 2-1 3v3l1 1-1 1h0v2l1-1c1 0 2 1 3 0v1c-1 0-7 1-7 1l-3 1c0-1 0-1-1-1h-2c-1-2-1-3-2-4z"></path><defs><linearGradient id="AZ" x1="309.375" y1="488.987" x2="301.399" y2="489.104" xlink:href="#B"><stop offset="0" stop-color="#a7a8ac"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#AZ)" d="M319 474l1-2v1h1l1 1 2 5h0l2 2-1 1h0c0 6 0 13 1 18h-1c1 1 2 1 1 3l-1 1c0 1 1 1 2 2-1 1-1 2-1 3-1-1-1-1-1-2l-1-1h-3c-2 0-5 0-6-1h0c1-1 2-1 3-2v-2c-2-1-4-2-6-2h-3l-5-1c-1 0-2 0-3-1 0-1 0-2 1-3v-2c0-1 1-2 1-4 1-2 2-5 4-7 0-1 1-2 1-2l1-2 1-1 3 2 3-1 2-1 1-1v-1z"></path><path d="M308 479l1-2c1 2 0 4 1 6 0 3 1 10 0 13v2l-1-1v-1c1-2 0-6 0-9v-1c0-2 0-3-1-4v-1h-1 0c0-1 1-2 1-2z" class="F"></path><path d="M324 500h1c1 1 2 1 1 3l-1 1c0 1 1 1 2 2-1 1-1 2-1 3-1-1-1-1-1-2l-1-1h-3c-2 0-5 0-6-1h0c2 0 8-1 9-2v-1l-1-2h1z" class="U"></path><path d="M313 478l3-1c2 2 2 3 2 5 1 4 0 7 2 11-1 2 0 5 0 7h0c-2 0-3 0-4-2-1-1-2-2-1-3v-7c-1-4 0-6-2-10z" class="S"></path><path d="M319 474l1-2v1h1l1 1 2 5h0l2 2-1 1h0c0 6 0 13 1 18h-1-1-1-3c0-2-1-5 0-7-2-4-1-7-2-11 0-2 0-3-2-5l2-1 1-1v-1z" class="X"></path><path d="M319 475c2 2 3 4 3 6l1 2c0 6-1 12 1 17h-1c-1-2-1-5-1-8 0-1-1-2-1-4 0-5 0-7-3-12l1-1z" class="P"></path><path d="M316 477l2-1c3 5 3 7 3 12 0 2 1 3 1 4 0 3 0 6 1 8h-3c0-2-1-5 0-7-2-4-1-7-2-11 0-2 0-3-2-5z" class="W"></path><defs><linearGradient id="Aa" x1="442.576" y1="575.394" x2="397.474" y2="626.308" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#25262b"></stop></linearGradient></defs><path fill="url(#Aa)" d="M370 557c17 0 35 2 51 9 18 6 35 18 48 32l14 17c3 6 5 11 7 17 1 2 2 5 3 8l2 6c-3-3-5-7-8-9-6-7-14-14-22-19-5-3-11-5-16-7-2 2-3 3-5 4-3 2-8 6-10 10h0c-2 3-3 6-3 9l-1 1-3-1c-4-3-9-6-12-7-1 0-2 0-3-1-3-2-6-4-10-5h-2c-1 1-3 3-3 5-1 2-3 5-2 7 0 1 0 0 1 1-2 2-2 5-1 7v2c1 1 2 2 3 4-6-1-10-1-16-3v-1c-1 1-1 1-2 1l-1-1h-3v-1c-5-2-10-5-14-8 0-1-1-2 0-3h0l-2-4c0-2 0-2 1-4 1-1 1-2 2-3h0c-3 1-4 3-6 5 0-2 0-2 1-4l3-2 1-1c-2-3-2-6-1-9v-1c0-1-1-1-1-1l-1-1v-5c0-2 0-2 1-3 1-3 1-7 1-9-1 0-1 0-2-1h-1c-2-1-2-1-3-3 0-2 0-3 2-4l-1-1c0-2-5-3-7-3l-1 1-2 2-2 1-1 1 1-2c-1-1-2-2-3-2 0 0-1 0-1-1-1-1-3-1-4-2s-3-2-4-3l-2-8c4-2 8-3 12-4l12-2c5-1 10-1 16-1z"></path><path d="M387 589c2 0 3 0 4 2v1c-2 1-3 3-4 5-1 1-1 2 0 4s3 5 4 6l-2 1c-1-1-2-2-3-4-3-5-1-10 1-15z" class="M"></path><path d="M387 597c1 2 2 4 4 4 1 1 3 1 4 0h0c2 1 2 3 2 5h0l-6 1c-1-1-3-4-4-6s-1-3 0-4z" class="R"></path><path d="M433 585l14 23c-8-6-12-13-14-23z" class="L"></path><path d="M391 592c3 0 3 0 5 2 1 2 1 4 0 5l-1 2h0c-1 1-3 1-4 0-2 0-3-2-4-4 1-2 2-4 4-5z" class="B"></path><path d="M412 605c0-3 1-4 3-6 0 0 2-1 2-2l-1-1c0-2-1-4 0-6 1 0 2 1 3 2 2 2 2 4 2 7-1 2-2 3-3 5-1 0-1 0-1 1h-5z" class="S"></path><path d="M389 584c0-1 1-2 2-3 2 0 3 1 4 2 3 2 5 4 5 8 1 0 1 1 1 2 1 2 1 4 1 6 1 2 0 4 1 5h0c1 1 2 1 2 1l-4 1h-1 0-3 0c0-2 0-4-2-5l1-2c1-1 1-3 0-5-2-2-2-2-5-2v-1c-1-2-2-2-4-2 0-2 1-3 2-4v-1z" class="T"></path><path d="M389 584c4 0 6 2 8 5 3 5 4 11 4 17h-1c-1-3 0-6-2-9-1-3-2-7-4-9-1-2-3-3-5-3v-1z" class="Q"></path><path d="M389 585c2 0 4 1 5 3 2 2 3 6 4 9 2 3 1 6 2 9h0-3 0c0-2 0-4-2-5l1-2c1-1 1-3 0-5-2-2-2-2-5-2v-1c-1-2-2-2-4-2 0-2 1-3 2-4z" class="O"></path><defs><linearGradient id="Ab" x1="350.121" y1="559.76" x2="341.8" y2="569.645" xlink:href="#B"><stop offset="0" stop-color="#27282e"></stop><stop offset="1" stop-color="#51545e"></stop></linearGradient></defs><path fill="url(#Ab)" d="M342 560l12-2v1c-1 0-3 0-4 1h1c2 2 5 5 6 7h2c1-1 1-1 2-1-4 3-9 6-13 11-1 1-2 2-2 3l-2 1-1 1 1-2c-1-1-2-2-3-2 0 0-1 0-1-1-1-1-3-1-4-2s-3-2-4-3l-2-8c4-2 8-3 12-4z"></path><defs><linearGradient id="Ac" x1="402.923" y1="613.721" x2="444.436" y2="623.943" xlink:href="#B"><stop offset="0" stop-color="#969493"></stop><stop offset="1" stop-color="#e6e5e3"></stop></linearGradient></defs><path fill="url(#Ac)" d="M417 605l12 1c6 1 14 3 20 5-2 2-3 3-5 4-3 2-8 6-10 10h0c-2 3-3 6-3 9l-1 1-3-1c-4-3-9-6-12-7-1 0-2 0-3-1-3-2-6-4-10-5h-2v-1c5-5 11-10 19-12-3 0-6-1-9-1h-7 0c-1-1-2-1-3-1h0 1l4-1h7 5z"></path><path d="M417 605l12 1h-1c-1 1-2 0-4 0h-5l-1 1c1 1 7 0 8 1h-7c-3 0-6-1-9-1h-7 0c-1-1-2-1-3-1h0 1l4-1h7 5z" class="E"></path><defs><linearGradient id="Ad" x1="372.936" y1="578.131" x2="361.072" y2="585.176" xlink:href="#B"><stop offset="0" stop-color="#21252e"></stop><stop offset="1" stop-color="#515763"></stop></linearGradient></defs><path fill="url(#Ad)" d="M359 569l4-3c1 0 1-1 2-1 0 1-1 2-2 2 6 6 9 12 13 18l2 2h3 1l1 1c-2 4-4 7-8 9l-1 1c0 2 1 5 2 7s1 4 1 6l-2 1-5 2-4 2-4 2c-2-3-2-6-1-9v-1c0-1-1-1-1-1l-1-1v-5c0-2 0-2 1-3 1-3 1-7 1-9-1 0-1 0-2-1h-1c-2-1-2-1-3-3 0-2 0-3 2-4l-1-1c0-2-5-3-7-3l6-5 4-3z"></path><path d="M359 569c1 1 2 1 2 2l4 5c-4-3-6-4-10-4l4-3z" class="O"></path><defs><linearGradient id="Ae" x1="377.688" y1="591.172" x2="372.749" y2="594.072" xlink:href="#B"><stop offset="0" stop-color="#464a54"></stop><stop offset="1" stop-color="#616268"></stop></linearGradient></defs><path fill="url(#Ae)" d="M382 587l1 1c-2 4-4 7-8 9h-4v-3c3-3 7-4 10-7h1z"></path><path d="M357 581c1-1 2-1 5-1 1 1 2 2 2 3 0 2 0 3-1 4-2 1-3 1-5 1-2-1-2-1-3-3 0-2 0-3 2-4z" class="B"></path><path d="M366 616c0-2 1-6 1-8-2 0-2-1-3-1-1-2 0-4 0-6h1c0-2 1-2 2-2h0v7c2 2 5 1 5 3h0c-1 2-2 3-2 5l-4 2z" class="U"></path><path d="M367 599c1-1 1-1 2-1l2-1h4l-1 1c0 2 1 5 2 7s1 4 1 6l-2 1-5 2c0-2 1-3 2-5h0c0-2-3-1-5-3v-7z" class="P"></path><path d="M375 612c0-1 1-3 0-5h-3v-1c-1-1-1-1-1-2 0-2 0-5 1-6h2c0 2 1 5 2 7s1 4 1 6l-2 1z" class="T"></path><defs><linearGradient id="Af" x1="366.151" y1="613.004" x2="403.753" y2="628.81" xlink:href="#B"><stop offset="0" stop-color="#939293"></stop><stop offset="1" stop-color="#adacac"></stop></linearGradient></defs><path fill="url(#Af)" d="M397 606h3c1 0 2 0 3 1h0 7c3 0 6 1 9 1-8 2-14 7-19 12v1c-1 1-3 3-3 5-1 2-3 5-2 7 0 1 0 0 1 1-2 2-2 5-1 7v2c1 1 2 2 3 4-6-1-10-1-16-3v-1c-1 1-1 1-2 1l-1-1h-3v-1c-5-2-10-5-14-8 0-1-1-2 0-3h0l-2-4c0-2 0-2 1-4 1-1 1-2 2-3h0c-3 1-4 3-6 5 0-2 0-2 1-4l3-2 1-1 4-2 4-2 5-2 2-1 12-3 2-1 6-1z"></path><path d="M387 611l3-1h1c-1 1-2 1-2 1-2 2-1 4-5 3l-1 2h-1c1-3 3-3 5-5z" class="S"></path><path d="M410 607c3 0 6 1 9 1-8 2-14 7-19 12h0l2-3 2-2c-2 0-3 2-4 3-2 1-3 4-6 5h0c3-4 7-9 12-12 1-1 3-1 5-2h1l1-1-3-1z" class="D"></path><path d="M361 623c2 0 4-1 5 0s2 2 2 4c0 1-1 2-2 3s-1 1-3 1h0-1 0l-2-4c0-2 0-2 1-4z" class="B"></path><path d="M397 606h3c1 0 2 0 3 1h0c-4 1-9 1-13 3l-3 1c-3 0-5 0-7 1l-17 8h0c-3 1-4 3-6 5 0-2 0-2 1-4l3-2 1-1 4-2 4-2 5-2 2-1 12-3 2-1 6-1z" class="V"></path><defs><linearGradient id="Ag" x1="388.083" y1="633.306" x2="394.37" y2="630.265" xlink:href="#B"><stop offset="0" stop-color="#8e8e8d"></stop><stop offset="1" stop-color="#a9a7a7"></stop></linearGradient></defs><path fill="url(#Ag)" d="M381 623v-1h1c0 1 1 2 2 2l1-2c0 1 1 1 1 1 2 0 3 4 5 5 0 0 1 1 2 1v-1c-1-1-1-1-1-2h1v1c1 0 1 0 2 1h0c0-1 0-1 1-2h0v-2c1-1 2-1 2-2v-1l2-1h0v1c-1 1-3 3-3 5-1 2-3 5-2 7 0 1 0 0 1 1-2 2-2 5-1 7v2c-2-2-3-5-4-7-1-1-1-2-2-3s-2-2-4-2v-3c-1-2-2-4-4-5z"></path><path d="M381 623c2 1 3 3 4 5v3c2 0 3 1 4 2s1 2 2 3c1 2 2 5 4 7 1 1 2 2 3 4-6-1-10-1-16-3v-1l-4-6c-1-2-2-6-2-8l1-2s0-1 1-1c0-2 1-2 3-3z" class="H"></path><path d="M381 623c2 1 3 3 4 5v3c-1 0-3 1-4 1-3-1-3-2-5-3l1-2s0-1 1-1c0-2 1-2 3-3z" class="B"></path><defs><linearGradient id="Ah" x1="674.38" y1="559.323" x2="736.181" y2="584.561" xlink:href="#B"><stop offset="0" stop-color="#918677"></stop><stop offset="1" stop-color="#d1cec5"></stop></linearGradient></defs><path fill="url(#Ah)" d="M835 242l1 1 2-2c2 0 3 1 5 1v1c2 0 4-1 5 0 2 0 5 0 7 1 1 0 2-1 4 0 1 0 1 0 2 1h0 3 0l3 3 1 1h0l1 1v1c1 0 1 0 1 1v1 4h0v2h-1v1 1 1h-1v1 1l-1 1s0 1-1 1v2c-1 1-1 2-2 3 0 1 0 1-1 1 0 3-2 5-3 7-1 1-1 1-1 2-1 1-1 2-2 3 0 1-1 1-1 2l-1 1v1 1c-1 1-2 3-2 4h-1c0 1 0 1-1 2h0v1l-1 1v2h-1c0 1 0 1-1 2v1h0c-1 1-1 2-2 2v1 1l-1 2-2 3c0 2-1 3-2 4 0 2-2 6-3 7l-1 1v1h0l-1 2-1 3-1 1v2l-1 1c-1 3-2 6-4 8v2l-1 1-1 1v1 1l-2 4c0 1-1 2-1 2v1l-1 1-1 2v1l-1 2-1 1-1 4-1 2h0l-1 2-1 2c0 1 0 2-1 2v1 1h-1v1 1l-1 2h0c0 1-1 2-1 3v1h-1v1 1h-1v2c-1 2-1 3-2 5l-1 2v1l-3 6-1 1v1c0 1-1 2-1 4l-1 2-2 5-1 2v1c-1 1-1 3-2 4v1l-1 1h0c0 1 0 1-1 2l-1 2v2h-1v2l-1 1c0 1 0 2-1 2v1c0 1 0 1-1 2h0v1l-1 1v2h-1c0 1 0 2-1 3h0c0 1-1 2-1 3h0l-1 2v1c0 1-1 2-1 2v1c-1 1-1 1-1 2v1c-1 0-1 1-1 1l-1 1v1h0c0 2-1 3-2 4v1 1l-1 2v1c-1 1-1 2-2 3l-1 2v1c-1 2-1 4-2 6l-1 1v1l-5 11-1 2v1c-1 2-1 4-2 6l-1 1-1 2s0 1-1 1v2 1c-1 1-1 1-1 2-1 2-1 3-2 5l-1 3c-1 1-1 2-2 3h0c0 1-1 2-1 3h0l-1 2v1l-1 2h0l-1 2v1l-1 2c-2 4-3 10-6 15l-1 1v1c0 1-1 3-1 4-1 1-1 2-2 2v1 1 1l-1 1h0l-1 1v2l-1 1v2l-1 2-1 1v2h0c-1 2-2 3-2 5h-1c0 1 0 2-1 3h0c0 1-1 2-1 3h0l-1 2v1l-1 2c-1 3-2 6-4 10l-1 1v1 2l-2 2v1 1l-1 2h-1c0 1 0 2-1 3h0v1l-1 1v2c-1 1-1 2-2 3v1 1c-1 1-1 2-2 2v1c0 1 0 1-1 2v1s0 1-1 1v1 1h-1v2h0c0 1-1 2-1 3h-1v2c-1 2-1 3-2 5l-1 2-1 1v1 1l-1 1-2 4c0 1 0 2-1 2v1 1l-1 3-4 10-1 1v1c-2 3-3 7-4 9 0 2-1 3-1 4h-1c0 1 0 2-1 3v1 1h0c-1 1-1 2-2 3v2c-1 2-2 3-3 5-1 5-4 10-5 14 0 2-1 2-1 3l-1 1v2l-1 1-2 4v1c0 1-1 2-1 3l-1 2v1c-1 0-1 1-1 1l-1 1v1 2c-1 1-1 0-1 1s-1 2-1 2v1c-1 1-1 1-1 2h-1v1 1l-1 2h0c-1 3-2 4-2 7l-2 2h0l-1 2v1c0 1-1 2-1 3v1l-1 1h0c0 1 0 1-1 2l-1 2v1c-1 2-3 6-4 9v1 1l-1 1c0 1 0 1-1 1v2l-1 2c0 1-1 2-1 2v1l-1 1h0c0 2-1 4-2 5l-1 2c0 1-1 2-1 3l-1 1v2l-1 1v1 1l-1 1-2 4v1 1c-1 1-1 2-2 2v1 1l-1 2-1 1v2l-1 1c-1 2-1 3-2 5l-1 1h0c0 1-1 2-1 3v1c-1 1-1 2-2 2 0 1 0 2-1 2v1c0 1 0 1-1 2l-4 8c-1 2-2 3-3 5s-3 5-4 7c-2 3-3 6-5 9l-2 2c0 1 0 0-1 1 0 1-1 2-1 3l-2 2-3 4-3 4-1 2c-1 0-2 1-2 2-1 1-1 2-2 3 0 0-1 1-1 2-1 1-3 4-4 4h-2v1h-2-3v1c-2 1-5 1-8 1l-12-1c-4 0-8 0-12 1l-17-2c-3 0-6 0-8-1l-21-2v-1h1 16 19 31c0-2 1-4 1-5h1l29-76 38-99 54-141 84-205 34-82c1-2 2-4 2-6 1-2 1-3 2-5 1-1 2-3 3-4l5-9c4-7 10-15 16-20z"></path><path d="M568 889h0l-2 6h-28s0-1 1 0l1 1c-4 0-9-1-13-1s-7 0-11-1h19 31c0-2 1-4 1-5h1z" class="H"></path><path d="M499 895v-1h1 16c4 1 7 1 11 1s9 1 13 1h1c2 1 5-1 6 1h2v-1h2v-1c2 0 9-1 10 0h1 1 1v2h2 3 1c1 1 2 0 3 1 1-1 0 0 1 0s2 0 3 1h0c-2 1-5 1-8 1l-12-1c-4 0-8 0-12 1l-17-2c-3 0-6 0-8-1l-21-2z" class="L"></path><path d="M520 897h3 3 1 3-1v-1l29 2c5 0 10 1 15 0 1-1 0 0 1 0s2 0 3 1h0c-2 1-5 1-8 1l-12-1c-4 0-8 0-12 1l-17-2c-3 0-6 0-8-1z" class="I"></path><path d="M438 170l3 4h3l1 2c5 3 10 11 13 15v1c3 8 10 13 12 23l1 1h1v3 3 1l-1 1h0c-1 1-1 2-2 3-1 0-2 0-2 1h-1c-1 0-1 0-2 1h-1-1s-1 1-2 1h-1c-1 0-2 1-2 1h-2s-1 1-2 1-1 0-1 1l-1-1v1h-1c-1 0-2 1-3 1s-2 0-2 1h-1c-1 1-1 1-2 1l-1 1s-1 0-1 1c-2 0-2 1-3 2h-1l-1 1c-1 0-1 1-2 1-2 1-3 3-4 5-1 1-2 1-2 3l-1 1c-1 0-1 1-2 2l-1 2c-1 0-1 1-1 2l-3 7h-1c0 1 0 2-1 3v1c0 1 0 1-1 2v2c0 1 0 1-1 2 0 1 1 2 0 4 0 1 0 5-1 6 0 2 0 9 1 11 0 1-1 3 0 5 0 1 0 1 1 2v3c0 1 0 1 1 2v2c0 1 0 1 1 2v2c0 1 0 1 1 2v1l2 5v2l1 1v1c0 1 1 2 1 3l1 1v1c0 1 1 2 1 3l1 4c0 1 1 2 1 2v1l2 4c0 1 1 2 1 3l1 2v1c1 3 2 5 3 8 2 8 7 16 10 24 0 2 2 5 3 8l1 1v1l4 11 1 1v1l1 1c0 2 1 2 1 4l1 1 5 12 1 2c0 1 0 2 1 3l1 1v1 2c1 0 1 1 2 2l1 4 1 2v1l3 6 1 1v1c0 1 1 2 1 4l1 2c1 1 2 3 2 4s1 2 1 3l1 2 2 3 1 4 3 6 1 2 2 6 1 2v1c1 1 1 2 2 3l1 2 4 11 2 2v1c0 1 1 3 2 4 2 4 3 9 5 13 1 2 2 3 3 4v1l1 3 2 3 1 4 2 4 3 8c1 1 1 2 1 3 1 1 1 0 1 2h1v1l1 2 1 1v1c1 1 1 2 2 4l1 2c0 1 1 2 1 3l1 2c1 1 1 2 2 4l8 15v2c1 1 1 1 1 2l1 1 3 5c0 1 1 2 2 3l1 3 2 3v1l1 1v2c1 1 1 1 1 2v1 1h-1l1 2-5 13c0 2-1 4-1 6l-1 3-1 3-2 5-1 3h0c-1 10-1 21-1 31v8l-2 7-3 9c1 1 1 1 1 2s0 2 1 3c0 2 0 2-1 3v4c0 1 1 2 1 4 0 1 0 0 1 1 3-3 4-6 8-9l1-1 28-13c4-2 9-6 14-7h0c0 2 1 4 2 5-2 1-4 3-7 3h0c-1 1-1 1-2 1l-13 7c-1 1-3 2-5 2h0v1c-1 1-2 2-4 2v1h2l-1 1h0c-1 1-3 2-4 2l-1 2c-3 2-4 3-5 7v1 3l-3 6v1c0 2-2 5-3 7h0l-1 1c-1 2-1 3-1 5-1 3-1 6 0 9-2-3-4-10-6-12h0 0c-2-10-8-20-12-28l-8-12 1-1v-2c-1-2 0-4 0-5l1-2h1c0 1 0 3 1 4v1c0 2 1 4 2 6h1c-2-7-3-14-5-21-1-4-4-8-6-12-1-2-1-5-2-7l-5-12c-2-4-3-8-5-11l-2-2-2-6c-1-3-2-6-3-8-2-6-4-11-7-17 1-2 0-3 0-5l-6-15c-1-2-2-3-2-5l-21-51c0-1-1-2-2-3l-2-3 1-1c-2-2-2-4-3-6l-4-12-6-10h1c0-4-2-6-3-9-3-6-5-14-7-20 0-1-1-2-1-3-1-1-2-2-2-3l-12-29c-2-4-3-9-6-13 0-2-1-3-1-4-2-3-3-7-4-9-3-7-5-14-9-20l-2-3 1-1c-1-3-2-6-4-9l-2-3c0-2-1-4-1-6 0-1-1-3-1-5l-1-1c-1-6-5-12-7-18l-3-8c-1-2-1-4-2-5-3-7-3-16-8-22 0-4-2-9-3-13l-3-14c1-3 0-9 0-13v-12c0-5 0-10 1-15l1-8c1 3 0 5 1 8l1-6c2-19 12-37 26-51 2-2 5-5 8-7 10-8 27-14 40-12h1 1z" class="B"></path><path fill="#6a5c48" d="M532 641l3 3c1 2 1 4 3 6l-1 3h0l-5-12z"></path><path fill="#867968" d="M508 582c3 3 3 8 6 11 1 2 2 5 3 7s2 3 3 5l8 19c2 4 5 8 7 12 1 3 2 6 4 8h1v1l-2 5c-2-2-2-4-3-6l-3-3c-1-3-2-6-3-8l-7-17-14-34z"></path><path fill="#6a5c48" d="M394 306c-5-19-9-37-5-56 3-16 12-30 25-39 12-8 24-10 38-7l-1 1c-6 3-13 4-19 6-4 2-7 4-11 5-5 2-9 8-13 11-2 1-4 4-5 7-7 10-10 21-12 33 0 5-1 14 0 19 1 7 3 13 3 20z"></path><path d="M438 170l3 4c-7 0-14 0-21 1-11 3-22 9-31 17-16 16-24 38-25 60 0 21 4 41 10 60 5 16 11 30 17 45l27 67 89 223 17 43c3 6 6 12 7 18 1 1 1 1 1 2s0 2 1 3c0 2 0 2-1 3v4c0 1 1 2 1 4 0 1 0 0 1 1 3-3 4-6 8-9l1-1 28-13c4-2 9-6 14-7h0c0 2 1 4 2 5-2 1-4 3-7 3h0c-1 1-1 1-2 1l-13 7c-1 1-3 2-5 2h0v1c-1 1-2 2-4 2v1h2l-1 1h0c-1 1-3 2-4 2l-1 2c-3 2-4 3-5 7v1 3l-3 6v1c0 2-2 5-3 7h0l-1 1c-1 2-1 3-1 5-1 3-1 6 0 9-2-3-4-10-6-12h0 0c-2-10-8-20-12-28l-8-12 1-1v-2c-1-2 0-4 0-5l1-2h1c0 1 0 3 1 4v1c0 2 1 4 2 6h1c-2-7-3-14-5-21-1-4-4-8-6-12-1-2-1-5-2-7l-5-12c-2-4-3-8-5-11l-2-2-2-6c-1-3-2-6-3-8-2-6-4-11-7-17 1-2 0-3 0-5l-6-15c-1-2-2-3-2-5l-21-51c0-1-1-2-2-3l-2-3 1-1c-2-2-2-4-3-6l-4-12-6-10h1c0-4-2-6-3-9-3-6-5-14-7-20 0-1-1-2-1-3-1-1-2-2-2-3l-12-29c-2-4-3-9-6-13 0-2-1-3-1-4-2-3-3-7-4-9-3-7-5-14-9-20l-2-3 1-1c-1-3-2-6-4-9l-2-3c0-2-1-4-1-6 0-1-1-3-1-5l-1-1c-1-6-5-12-7-18l-3-8c-1-2-1-4-2-5-3-7-3-16-8-22 0-4-2-9-3-13l-3-14c1-3 0-9 0-13v-12c0-5 0-10 1-15l1-8c1 3 0 5 1 8l1-6c2-19 12-37 26-51 2-2 5-5 8-7 10-8 27-14 40-12h1 1z" class="E"></path><path d="M363 289c0-3-1-5 0-7 3 11 5 22 9 34s9 24 13 36h-1c0-1-1-2-1-3l-2-4-12-34-1-1-5-21z" class="F"></path><path d="M396 379l36 89-1 1-3-7c-3-8-6-18-11-25l-1-2c-3-10-9-19-12-29-1-5-3-10-4-14s-5-9-4-13z" class="J"></path><path d="M360 238c1 3 0 5 1 8 0 6-1 12 0 18 0 6 2 12 2 18-1 2 0 4 0 7l5 21 1 1 12 34 2 4c0 1 1 2 1 3h1c2 3 3 7 5 10 2 6 4 11 6 17-1 4 3 9 4 13s3 9 4 14c3 10 9 19 12 29-2-1-2-3-3-5-2-1-3-4-4-6h0v1c-1-1-1-2-2-3v1c-2-3-3-7-4-9-3-7-5-14-9-20l-2-3 1-1c-1-3-2-6-4-9l-2-3c0-2-1-4-1-6 0-1-1-3-1-5l-1-1c-1-6-5-12-7-18l-3-8c-1-2-1-4-2-5-3-7-3-16-8-22 0-4-2-9-3-13l-3-14c1-3 0-9 0-13v-12c0-5 0-10 1-15l1-8z" class="I"></path><path d="M372 335h1 0l1 3c1 0 1 0 1-1 1 1 1 1 1 2l1 4 1 3c1 2 2 4 2 6l-3-4-3-8c-1-2-1-4-2-5z" class="C"></path><path d="M360 238c1 3 0 5 1 8 0 6-1 12 0 18 0 6 2 12 2 18-1 2 0 4 0 7-1-1-1-2-2-3h0c-1-5-1-11-1-16l-1-24 1-8z" class="H"></path><path d="M377 348l3 4c5 9 8 19 11 29 2 4 3 8 4 13h-1l-2-3 1-1c-1-3-2-6-4-9l-2-3c0-2-1-4-1-6 0-1-1-3-1-5l-1-1c-1-6-5-12-7-18z" class="G"></path><path d="M381 345l2 4c0 1 1 2 1 3h1c2 3 3 7 5 10 2 6 4 11 6 17-1 4 3 9 4 13s3 9 4 14c-2-2-5-6-6-8 0-2 0-4-1-6l-4-13c-1-4-2-8-4-12l-2-4-4-9-1-3c-1-2-1-4-1-6z" class="H"></path><path d="M407 423v-1c1 1 1 2 2 3v-1h0c1 2 2 5 4 6 1 2 1 4 3 5l1 2c5 7 8 17 11 25l3 7 1-1 6 15 17 44 43 106 12 31c2 5 5 10 6 15 3 4 4 9 6 14 2 4 5 8 6 13 2 4 3 9 4 14 0 1 1 2 1 4 0 1 0 0 1 1 3-3 4-6 8-9l1-1 28-13c4-2 9-6 14-7h0c0 2 1 4 2 5-2 1-4 3-7 3h0c-1 1-1 1-2 1l-13 7c-1 1-3 2-5 2h0v1c-1 1-2 2-4 2v1h2l-1 1h0c-1 1-3 2-4 2l-1 2c-3 2-4 3-5 7v1 3l-3 6v1c0 2-2 5-3 7h0l-1 1c-1 2-1 3-1 5-1 3-1 6 0 9-2-3-4-10-6-12h0 0c-2-10-8-20-12-28l-8-12 1-1v-2c-1-2 0-4 0-5l1-2h1c0 1 0 3 1 4v1c0 2 1 4 2 6h1c-2-7-3-14-5-21-1-4-4-8-6-12-1-2-1-5-2-7l-5-12c-2-4-3-8-5-11l-2-2-2-6c-1-3-2-6-3-8-2-6-4-11-7-17 1-2 0-3 0-5l-6-15c-1-2-2-3-2-5l-21-51c0-1-1-2-2-3l-2-3 1-1c-2-2-2-4-3-6l-4-12-6-10h1c0-4-2-6-3-9-3-6-5-14-7-20 0-1-1-2-1-3-1-1-2-2-2-3l-12-29c-2-4-3-9-6-13 0-2-1-3-1-4z" class="I"></path><path d="M514 709c1 2 2 3 3 5 1 0 1 1 2 1h1v2c0 1 1 2 1 3v2l-8-12 1-1zm0-7l1-2h1c0 1 0 3 1 4v1c0 2 1 4 2 6h1l1 1 2 7h0c-1-1-1-1-1-2l-1-3h-2c0-1-1-2-1-3-2-3-3-6-4-9z" class="G"></path><path d="M516 679c3 4 4 9 6 14 2 4 5 8 6 13 2 4 3 9 4 14 0 1 1 2 1 4 0 1 0 0 1 1 3-3 4-6 8-9l1 1-3 2c-1 1-1 2-1 3l-1 1-1 1c-1 2-1 2-2 3v-1-1l-1 1h-1l-1-1c1-1 1-1 0-1v-1-2c0-1-1-1-1-2v-1l-1-1c1-1 1-1 0-2 0-2-1-4-1-5v-1l-3-6c0-1 0 0-1-1v-1-1l-1-1-1-3c0-1 0-1-1-2v-1c-1 0-1-1-1-2h-1c1-2 0-2-1-3v-2c-1-1-1-1-1-2l-1-1-1-3v-1z" class="H"></path><path d="M429 475c2 2 2 3 3 6 0 0 1 0 1 1 1 1 2 3 2 4l5 13c0 2 2 3 2 5v2 2l1 1c0 2 1 3 1 5l-6-10h1c0-4-2-6-3-9-3-6-5-14-7-20zm155 221c-1 3-2 3-4 4-13 7-27 12-36 23-1 3-4 5-4 8l-1 1v-1c-1 0-1 0-2-1l2-5c1-1 2-1 3-2h0l1-1h0l1-1c0-1 2-4 4-4l1-1 1-2h1l2-2h1l5-3c5-3 13-5 19-9 1-2 4-3 6-4z" class="D"></path><path d="M493 640c3 0 3 2 4 5l1 1c3 6 6 12 9 19l8 22 2 6h0c1 6 2 13 5 19h-1l-1-1c-2-7-3-14-5-21-1-4-4-8-6-12-1-2-1-5-2-7l-5-12c-2-4-3-8-5-11l-2-2-2-6z" class="C"></path><path d="M407 423v-1c1 1 1 2 2 3v-1h0c1 2 2 5 4 6 1 2 1 4 3 5l1 2c0 4 1 6 2 9l6 16c1 4 3 6 3 10-1-1-2-2-2-3l-12-29c-2-4-3-9-6-13 0-2-1-3-1-4zm44 109c1 0 1 0 2 1 1 2 2 5 3 7 2 4 4 9 6 12 1 1 1 3 1 4h0l13 33c0 1 1 2 1 2v1 3c-1-2-2-3-2-5l-21-51c0-1-1-2-2-3l-2-3 1-1z" class="D"></path><path d="M585 695c0 2 1 4 2 5-2 1-4 3-7 3h0c-1 1-1 1-2 1l-13 7c-1 1-3 2-5 2h0v1c-1 1-2 2-4 2v1h2l-1 1h0c-1 1-3 2-4 2l-1 2c-3 2-4 3-5 7v1 3l-3 6v1l-1 2-2 3-1-1h1v-1c-1 0-1 0-1-1v-1c0-3-1-5 0-8l1-3-1 1c0-3 3-5 4-8 9-11 23-16 36-23 2-1 3-1 4-4l1-1z" class="E"></path><path d="M543 742c-1 0-1 0-1-1-2-6 1-12 4-16l1-2c2-2 3-3 6-3l-1 2c-3 2-4 3-5 7v1 3l-3 6v1l-1 2z" class="K"></path><path d="M544 739h-1c0-5 0-9 3-12v3h1v3l-3 6z" class="V"></path><path d="M432 468l6 15 17 44 43 106 12 31c2 5 5 10 6 15v1h0l-3-6-1-1c0-1-1-2-1-3v-1l-1-1c0-1 0-1-1-2l-1-3-2-4-1-3v-1c-1-2 0 0-1-1-1-2-1-4-2-5-1-2-2-5-3-7h0l-1-1v-1c-1-1-1-1-1-2-1-1-1-2-2-3l-2-8v-1c-1-1-1-1-1-2v-1l-1-1c-1-2-1-2-1-4-1-1-2-2-2-4 0-1-1-2-1-3l-1-2-2-5-1-3-1-2c0-1-1-2-1-4-1-2-2-4-3-7v-1l-2-6c0-1-1-2-1-4-1 0-1-1-1-1v-1l-1-1c-1-2-1-4-2-6l-1-1c0-1 0-1-1-2 0-2-1-3-1-4l-6-13c-1-2-1-3-2-4v-1l-1-1c0-2-1-3-1-4l-1-2c-1-1-1-1-1-2v-1l-1-2h0l-1-1v-3-1l-3-4c0-1 0-2-1-3v-1-1c-1-1-1-3-2-4v-1l-1-2-4-8-1-2c0-1 0-1-1-2l-1-6c-1-1-1-2-1-3l-1-1v-2s-1-1-1-2-1-2-1-3c-1-1-1-2-1-3l-1-1c0-1-1-2-1-3-1-1-1-2-2-3v-2l1-1z" class="F"></path><path d="M445 176c5 3 10 11 13 15v1c3 8 10 13 12 23l1 1h1v3 3 1l-1 1h0c-1 1-1 2-2 3-1 0-2 0-2 1h-1c-1 0-1 0-2 1h-1-1s-1 1-2 1h-1c-1 0-2 1-2 1h-2s-1 1-2 1-1 0-1 1l-1-1v1h-1c-1 0-2 1-3 1s-2 0-2 1h-1c-1 1-1 1-2 1l-1 1s-1 0-1 1c-2 0-2 1-3 2h-1l-1 1c-1 0-1 1-2 1-2 1-3 3-4 5-1 1-2 1-2 3l-1 1c-1 0-1 1-2 2l-1 2c-1 0-1 1-1 2l-3 7h-1c0 1 0 2-1 3v1c0 1 0 1-1 2v2c0 1 0 1-1 2 0 1 1 2 0 4 0 1 0 5-1 6 0 2 0 9 1 11 0 1-1 3 0 5 0 1 0 1 1 2v3c0 1 0 1 1 2v2c0 1 0 1 1 2v2c0 1 0 1 1 2v1l2 5v2l1 1v1c0 1 1 2 1 3l1 1v1c0 1 1 2 1 3l1 4c0 1 1 2 1 2v1l2 4c0 1 1 2 1 3l1 2v1c1 3 2 5 3 8 2 8 7 16 10 24 0 2 2 5 3 8l1 1v1l4 11 1 1v1l1 1c0 2 1 2 1 4l1 1 5 12 1 2c0 1 0 2 1 3l1 1v1 2c1 0 1 1 2 2l1 4 1 2v1l3 6 1 1v1c0 1 1 2 1 4l1 2c1 1 2 3 2 4s1 2 1 3l1 2 2 3 1 4 3 6 1 2 2 6 1 2v1c1 1 1 2 2 3l1 2 4 11 2 2v1c0 1 1 3 2 4 2 4 3 9 5 13 1 2 2 3 3 4v1l1 3 2 3 1 4 2 4 3 8c1 1 1 2 1 3 1 1 1 0 1 2h1v1l1 2 1 1v1c1 1 1 2 2 4l1 2c0 1 1 2 1 3l1 2c1 1 1 2 2 4l8 15v2c1 1 1 1 1 2l1 1 3 5c0 1 1 2 2 3l1 3 2 3v1l1 1v2c1 1 1 1 1 2v1 1h-1l1 2-5 13c0 2-1 4-1 6l-1 3-1 3v-1h-1c-2-2-3-5-4-8-2-4-5-8-7-12l-8-19c-1-2-2-3-3-5s-2-5-3-7c-3-3-3-8-6-11-2-3-3-7-4-10l-9-20-25-61-42-102-11-26-6-15c-2-4-4-8-5-12l-12-30c0-7-2-13-3-20-1-5 0-14 0-19 2-12 5-23 12-33 1-3 3-6 5-7 4-3 8-9 13-11 4-1 7-3 11-5 6-2 13-3 19-6l1-1 3 1c1 1 5 3 6 2-2-3-4-8-6-12-3-6-7-12-10-19z" class="L"></path><path d="M403 291c1 0 1 1 2 1 1 1 0 0 0 1v2h0c1 1 1 2 1 4l1 1v2c-1 0-1 0-2-1h0v-1l-1-1v-1-1c0-2-1-4-1-6zm55 149h1v-1c1 2 0 3 1 3l1 1v1c1 1 1 1 2 3h-1v2h1c1 1 0 2 1 3v1c0 1 0 1 1 2h0c1 1 1 1 1 2l-1 1c-1-2-2-4-2-6-2-4-4-8-5-12zm23 54l1 1s1 0 1 1c1 0 1 1 1 2l1 1h-1l1 1v1 1h1v1l1 1c0 1 1 1 0 2v1l1 1v1c2 1 2 2 2 4v1 1h1c1 1 0 2 1 3l1 1h0v1h1v1s-1 0-1 1c-2-2-3-5-4-7-3-7-6-14-8-21z" class="H"></path><defs><linearGradient id="Ai" x1="454.818" y1="206.568" x2="458.302" y2="213.161" xlink:href="#B"><stop offset="0" stop-color="#817260"></stop><stop offset="1" stop-color="#9e9487"></stop></linearGradient></defs><path fill="url(#Ai)" d="M455 205c1 1 5 3 6 2 2 1 3 2 4 4l1 3v1l-1-1h0-1 0v-1l-1 1s0 1-1 0v1l-1-1h-1c-1 1-2 0-3 1s-2 1-4 1v1l-1-1v1c-2 0-2 1-3 1h-1-1c1-1 2-1 3-2s4-1 5-3h-5c1 0 2-1 2-1h1v-1l-4 1 2-2c-2 0-4 0-6 1l1-1c1-1 2-1 3-1 2-1 4-1 6-4z"></path><path d="M400 286v-1c1-2 1-4 1-6h0c0 1 0 3 1 4 0 0 0 1-1 1 0 1 0 2 1 2l-1 1v1c1 0 2 0 2 1v1 1c0 2 1 4 1 6v1 1l1 1v1c1 1 2 2 2 4v1 1l1 1h-1c0 2 2 3 2 5l1 1h-1v1 1h1c1 1 1 2 1 3v1l3 7v1c1 1 1 1 1 3h0c0 1 0 2 1 3 0 0 0 1 1 1v3s0 1 1 2h0c2 1 2 4 3 6v1c1 1 1 2 1 3h1v1c0 1 0 1 1 2l1 3v1l1 1c0 1 0 2 1 3 0 0 1 1 1 2s0 2 1 3h0c0 2 1 3 2 5 0 1 0 2 1 3v1l1 1c0 1 0 2 1 3h0c1 1 1 1 1 3h1c0 2 0 3 1 4s1 1 1 2v1c1 1 1 1 1 2v1l1 1v1c1 2 2 5 3 7l1 1-1 1c1 1 1 1 1 2v1l1-2v2c1 1 1 1 1 2 1 1 1 1 1 2v1c1 1 1 2 2 4h-1c1 2 1 2 1 4 1-1 1 0 1-1l1 2h-1l1 1c1 0 1 1 1 2s1 2 1 3c1 0 1 1 1 2v1c1 0 1 1 1 2l1 1v1 1h0c1 1 1 2 2 3v1 2c1 4 3 8 5 12 0 2 1 4 2 6 1 0 1 1 2 2h-1l1 1v1h1c1 1 2 3 2 4v1h0l1 1c0 1 1 2 1 3 0 0 0 1 1 2h0 0c1 2 0 3 1 3l3 6h-1l1 1h0c1 1 1 2 2 4 0 2 2 4 3 6l-1 1h0c2 7 5 14 8 21 1 2 2 5 4 7h1v3l1 1v1h0l1 1h-1l1 1-1 1h1l1 2h-1l1 1h0c1 1 1 2 2 3v1 1l2 4 1 1 1 1c0 1 0 2 1 3h-1v1 2l2 2v-2h0v2c1 1 1 1 1 2l1 1v1l1-2h0v4l1 1v2h1l1 2c0 1 1 2 1 3h0l-1 1h0l-1-2h0v1 2h1 0l2 2-1 1 1 1s1 1 1 2h0v1h1c0 2 1 2 2 4v2h0c1 1 1 2 2 3 0 1-1 1 0 2h0c0 2 0 2 1 3h0v-1-1 1h1v2c1 0 1 1 2 2l-1 1 2 2-1 1 1 1v2l1 1c1 2 0 3 2 4v1c0 1 1 1 1 2v2l1-1s0 1 1 2h-1c1 1 0 1 1 1 0 1 1 1 1 2s1 1 1 2c1 1 1 2 2 3h-1l1 1v-1l1 2h-1c1 1 1 1 1 2h0c1 1 1 1 1 2l1 1h1v-1c0 1 0 1 1 2 0 0 0 1 1 2h-1 0-1l1 2c1 0 1 1 1 2h1v-1s0-1 0 0l2 1h-1v2 2c-3-2-4-7-6-9-2-4-3-8-5-11-1-2-3-5-4-7 0-1-1-2-1-3-1-2-1-3-2-5l-2-3c-2-6-4-11-7-16l-18-43-8-20c-1-2-2-4-3-7l-19-43-27-66-19-48-13-34c-4-11-7-23-8-35z" class="R"></path><g class="Z"><path d="M450 213h5c-1 2-4 2-5 3s-2 1-3 2-3 0-4 1c-1 0-1 0-2 1l-4 2h-2c0 1-1 1-1 2l-1-1-1 1c-1 0-2 1-3 1h-1v1c-1 0-2 1-2 1l-4 3h0c-2 1-6 5-7 7-1 1-1 1-1 2l-2 1-3 5s-1 1-1 2v1c-1 0-1 1-1 1l-1 2v1c0 1 0 1-1 2 0 1-1 3-1 4v1l-1 1 1 1c-1 1-1 2-1 4l-1 1c0 2 0 1-1 3v3h1l-1 1v2c0 1-1 1-1 2v1c-1 2-1 4-1 7l1 1c1 12 4 24 8 35l13 34 19 48 27 66 19 43c1 3 2 5 3 7l8 20 18 43c3 5 5 10 7 16l2 3c1 2 1 3 2 5 0 1 1 2 1 3 1 2 3 5 4 7 2 3 3 7 5 11 2 2 3 7 6 9 0-1 0-2 1-2v-1h0c0 2-1 4-1 6-1 0-2-1-3-1 0-1-1-3-1-4l-10-19c-3-5-5-11-7-17l-19-42-10-24c-1-3-3-7-4-10l-6-12-18-43-23-58-23-55c-5-13-10-25-14-38-2-6-3-11-4-16-3-10-2-20-2-30 0-6 2-14 5-19 1-3 2-5 3-7s2-3 3-5 3-3 4-5c3-3 7-6 10-9 9-6 17-9 27-12z"></path><path d="M452 204l3 1c-2 3-4 3-6 4-1 0-2 0-3 1l-1 1c2-1 4-1 6-1l-2 2 4-1v1h-1s-1 1-2 1c-10 3-18 6-27 12-3 3-7 6-10 9-1 2-3 3-4 5s-2 3-3 5-2 4-3 7c-3 5-5 13-5 19 0 10-1 20 2 30 1 5 2 10 4 16 4 13 9 25 14 38l23 55 23 58 18 43 6 12c1 3 3 7 4 10l10 24 19 42c2 6 4 12 7 17l10 19c0 1 1 3 1 4 1 0 2 1 3 1l-1 3-1 3v-1h-1c-2-2-3-5-4-8-2-4-5-8-7-12l-8-19c-1-2-2-3-3-5s-2-5-3-7c-3-3-3-8-6-11-2-3-3-7-4-10l-9-20-25-61-42-102-11-26-6-15c-2-4-4-8-5-12l-12-30c0-7-2-13-3-20-1-5 0-14 0-19 2-12 5-23 12-33 1-3 3-6 5-7 4-3 8-9 13-11 4-1 7-3 11-5 6-2 13-3 19-6l1-1z"></path></g><path d="M452 204l3 1c-2 3-4 3-6 4-1 0-2 0-3 1l-1 1c-3 1-6 1-9 2-10 4-20 10-26 18-2 2-5 4-6 8-2 3-4 7-5 10-2 6-3 12-5 18-1 7 0 15 1 22v6c2 8 4 17 7 25l6 18c1 3 3 6 3 10-2-4-4-8-5-12l-12-30c0-7-2-13-3-20-1-5 0-14 0-19 2-12 5-23 12-33 1-3 3-6 5-7 4-3 8-9 13-11 4-1 7-3 11-5 6-2 13-3 19-6l1-1zm-3 8l4-1v1h-1s-1 1-2 1c-10 3-18 6-27 12-3 3-7 6-10 9-1 2-3 3-4 5s-2 3-3 5-2 4-3 7c-3 5-5 13-5 19 0 10-1 20 2 30 1 5 2 10 4 16 4 13 9 25 14 38l23 55 23 58 18 43 6 12c1 3 3 7 4 10l10 24 19 42c2 6 4 12 7 17l10 19c0 1 1 3 1 4 1 0 2 1 3 1l-1 3-3-3c-1-3-3-6-5-9l-7-15c-2-5-5-9-6-14-1-2-2-4-2-5l-22-48v-1l-1-2v-1l-6-12c0-2-1-5-2-7l-3-6-6-14-26-65c-2-5-5-10-7-15l-10-25c-3-6-6-12-8-18l-13-33c-1-2-2-5-2-8-1-1-2-2-2-4-3-6-5-14-7-21-3-9-6-18-7-27v-9c0-11 1-20 5-30 1-4 3-7 5-10 3-5 8-12 14-15 3-2 6-5 9-6 6-4 13-5 20-7z" fill="#867968"></path><path d="M582 157c2-1 5-1 7-1 6 0 14-1 20 1h28c6-1 13-1 20 1 15 2 29 8 41 17l15 15 16 20 6 11c1 2 2 4 3 5h1l-6-11c-1-1-1 0-1-2h1c2 2 4 3 4 6h0c3 4 9 8 15 9h1l-1-1 5 1c18 3 39 2 56-5 4-2 8-3 12-6 0 0 1-1 2-1h1l4-6c2-2 3-4 4-6 2-6 10-10 12-15l-4-4c-2-1-4-2-7-2l2-1-30-3h0-3c-3-1-6-2-10-3h0v-1s-3-2-4-2c-1-1-2-2-3-4-3-2-7-5-10-8 1 0 1 0 1-1l-2-2h2 9 24 12l10 1h100 0c-1 3-3 6-5 8-2 3-4 7-7 10 0 1 1 1 1 2 2 1 2 2 2 4 0 1 1 2 0 3-3 1-5 3-7 5-7 6-13 14-18 21-1 1-4 6-5 7-1 0-6-1-8-1-16 1-30 5-42 15-4 3-8 6-11 9-6 5-12 13-16 20l-5 9c-1 1-2 3-3 4-1 2-1 3-2 5 0 2-1 4-2 6l-34 82-84 205-54 141-38 99-29 76h-1c0 1-1 3-1 5h-31c2-1 5-1 7-1h0l-8-1h22c2 0 7 1 9 0l-1-2c-2-3-4-5-5-8h0v-1h-1-1c-2-2-4-2-6-3 4-12 6-24 7-37 0-9-1-16-4-24 1-1 1-2 1-3 0-2 0-4 1-5l2 8h1c2 1 2 2 4 3 1-1 0-1 1-2 1-2 0-3 1-5v-2c0-3 0-4 1-6 0-1 0-1 1-2h-3l-8-26v-1c-1-3-2-7-1-10 0-3 1-4 0-7l-1 1v-1h-1c-1 0-2 1-3 1v1h-2c-4 0-7-2-9-4l1-5 1-5h0c1-2 3-5 3-7v-1l3-6v-3-1c1-4 2-5 5-7l1-2c1 0 3-1 4-2h0l1-1h-2v-1c2 0 3-1 4-2v-1h0c2 0 4-1 5-2l13-7c1 0 1 0 2-1h0c3 0 5-2 7-3-1-1-2-3-2-5h0c-5 1-10 5-14 7l-28 13-1 1c-4 3-5 6-8 9-1-1-1 0-1-1 0-2-1-3-1-4v-4c1-1 1-1 1-3-1-1-1-2-1-3s0-1-1-2l3-9 2-7v-8c0-10 0-21 1-31h0l1-3 2-5 1-3 1-3c0-2 1-4 1-6l5-13 9-28 6-18 10-32 29-91 25-74c6-19 13-38 18-58 3-15 4-31 4-46-1-17-5-36-15-50-2-4-6-7-9-10 3 0 6 4 8 6 0 1 0 1 1 2l2-1v-4c1-1 2-3 3-5 0-3 1-5 1-7v-2c1-2 1-3 1-5-2-3-4-5-6-7h-1v-1c-7-9-21-14-32-17-5-1-11-1-16-1 0-1 0-3-1-4-2-2-3-7-4-9l1-1z" class="F"></path><path d="M624 691h0c0-2 1-4 3-5s5 0 6 0c-1 4-2 9-4 13-3-2-4-4-5-8z" class="N"></path><path d="M831 226v1c-1 1-1 2-2 4-1 1-1 2-2 4 1-1 2-2 2-3v-1c1-1 2-1 3-2l-7 13c-1 2-4 9-5 10h-1c-1 1-1 1-1 2v1h-1v-1c-1 1-1 3-2 4v1l-2 2c1-3 2-5 3-8 2-3 3-7 5-10 1-3 3-5 4-7 2-4 4-7 6-10z" class="D"></path><path d="M813 158h12l10 1 13 7c5 3 11 6 17 7 4 2 8 2 11 4l-1 1c-3 1-6 0-9-1-5-1-10-3-15-5-2-1-5-3-8-4-3-2-7-3-11-4-3-1-7-3-10-3-3-1-6-1-9-3z" class="C"></path><path d="M825 158l10 1 13 7-1 1c-6-2-10-4-15-6-2-1-5-1-7-3z" class="E"></path><path d="M780 158h9l5 1c2 0 5 1 8 1 10 2 21 5 31 8 4 2 7 4 11 5 8 4 18 5 27 6-3 1-6 2-8 2-3-2-11-2-14-3-9-2-18-5-27-8-12-3-24-5-35-9-2 0-5-1-7-2v-1z" class="U"></path><path d="M924 179c2 1 2 2 2 4 0 1 1 2 0 3-3-1-7 0-10 0-15 1-30 3-44 9-18 8-30 18-40 34-1 1-2 1-3 2v1c0 1-1 2-2 3 1-2 1-3 2-4 1-2 1-3 2-4v-1c11-19 27-32 48-38 9-3 17-4 26-5 6-1 14-1 19-4z" class="G"></path><path d="M792 262v1l1 1 12-1c0 1 0 2-1 3-1 4-7 22-10 24-10-7-20-14-28-23 5-1 10 0 15 0 1 0 2-1 3-1 3 0 5-1 8-4z" class="D"></path><path d="M792 262v1l1 1 12-1c0 1 0 2-1 3-1-1-3 0-5 0l-9 1c-1 0-3 1-5 1-1 0-3-1-4-1 1 0 2-1 3-1 3 0 5-1 8-4z" class="E"></path><defs><linearGradient id="Aj" x1="822.079" y1="180.176" x2="823.114" y2="172.189" xlink:href="#B"><stop offset="0" stop-color="#18181d"></stop><stop offset="1" stop-color="#35363e"></stop></linearGradient></defs><path fill="url(#Aj)" d="M778 158h2v1c2 1 5 2 7 2 11 4 23 6 35 9 9 3 18 6 27 8 3 1 11 1 14 3l-5 1h0c-1 1-2 2-3 2-3 2-5 3-7 5l-4-4c-2-1-4-2-7-2l2-1-30-3h0-3c-3-1-6-2-10-3h0v-1s-3-2-4-2c-1-1-2-2-3-4-3-2-7-5-10-8 1 0 1 0 1-1l-2-2z"></path><path d="M846 182h12 0c-1 1-2 2-3 2h-8c2-1 3-1 4-1-2 0-3 0-5-1z" class="D"></path><path d="M794 172c6 2 12 6 18 6l-3 1h-3c0-2-10-5-12-7z" class="N"></path><path d="M839 182h7c2 1 3 1 5 1-1 0-2 0-4 1-1 0-2 0-3 1-2-1-4-2-7-2l2-1z" class="G"></path><path d="M847 184h8c-3 2-5 3-7 5l-4-4c1-1 2-1 3-1z" class="K"></path><path d="M789 169l5 3c2 2 12 5 12 7-3-1-6-2-10-3h0v-1s-3-2-4-2c-1-1-2-2-3-4zm46-10h100 0c-6 2-12 5-17 7-14 4-28 8-42 11-3-2-7-2-11-4-6-1-12-4-17-7l-13-7zm-3 70c10-16 22-26 40-34 14-6 29-8 44-9 3 0 7-1 10 0-3 1-5 3-7 5-7 6-13 14-18 21-1 1-4 6-5 7-1 0-6-1-8-1-16 1-30 5-42 15-4 3-8 6-11 9-6 5-12 13-16 20l-5 9c-1 1-2 3-3 4l9-23c1-1 4-8 5-10l7-13z" class="B"></path><path d="M738 226h1l-6-11c-1-1-1 0-1-2h1c2 2 4 3 4 6h0c3 4 9 8 15 9h1c6 1 11 2 16 2 17 1 46-2 58-13l1-1-4 7-18 39h-1c-4 0-8 1-13 1v-1c-3 3-5 4-8 4-1 0-2 1-3 1-5 0-10-1-15 0-2-2-4-5-6-7-8-10-16-22-22-34z" class="D"></path><path d="M760 260l21 4c1 1 2 1 3 2-1 0-2 1-3 1-5 0-10-1-15 0-2-2-4-5-6-7z" class="C"></path><path d="M753 228c6 1 11 2 16 2 17 1 46-2 58-13l1-1-4 7v-2c-5 2-10 4-16 6h2c-1 1-2 2-3 2-17 5-34 6-51 3-4-1-8-1-11-3l-5-2v-1c2 0 4 1 5 2 1 0 2 1 3 1h0c1 1 2 1 3 1l4 1h3l1 1h2c1 0 2 0 3 1h1 2 5 12 1c1-1 2-1 3-1h2 0c2-1 3 0 5-1 1-1 2-1 3-1s2 0 3-1h2l1-1-7 2h-3c-3 0-6 0-9 1s-6 0-9 1c-1 0-2 0-3-1h-9c-1-1-2 0-4-1-2 0-5-1-8-2h1z" class="K"></path><path d="M781 264c-1-1-2-1-2-2-1-2-1-4-1-6 1-2 2-4 4-4 2-1 4-2 6-1s4 3 5 5 0 4-1 6c-3 3-5 4-8 4-1-1-2-1-3-2z" class="B"></path><defs><linearGradient id="Ak" x1="560.214" y1="781.767" x2="612.468" y2="790.364" xlink:href="#B"><stop offset="0" stop-color="#c1c0c1"></stop><stop offset="1" stop-color="#fbfbf9"></stop></linearGradient></defs><path fill="url(#Ak)" d="M624 691c1 4 2 6 5 8 0 1-1 3-2 4l-4 12-21 54c-2 5-5 10-6 15-2 6-4 13-8 18 1 1 1 1 0 1l-3 8-8 22-16 41c-1 2-2 5-2 7h-1-1c-2-2-4-2-6-3 4-12 6-24 7-37 0-9-1-16-4-24 1-1 1-2 1-3 0-2 0-4 1-5l2 8h1c2 1 2 2 4 3 1-1 0-1 1-2 1-2 0-3 1-5v-2c0-3 0-4 1-6 0-1 0-1 1-2v-1c0-3 2-5 4-7v1c2-2 3-4 5-5l2-2 2-4v-1l1-1c0-1 0-1 1-1v-2l2-3c1-3 3-5 4-7 2-4 4-7 7-10 1-3 3-4 5-7s4-6 6-10h-1c1-1 1-2 1-3h1l-4-1h1c-2-1-4-1-5-3-2-2-2-4-1-7l-1-1c1-2 4-4 6-6l7-10c1-1 2-4 3-5h2c2-2 3-6 5-8v-1c1 0 1-1 1-2 1-1 1-1 1-2 1-1 1-2 2-3z"></path><path d="M580 810v-1-1h0v-1-1c1-1 1 0 1-1v-1c0-1 0-1 1-2v-5-1h1l-1-2h1v-1h0v-1l1-1v-1l1-1h0l1-1v-1l1 1c-2 3-4 7-4 11 2 2 3 2 5 3 1 1 1 1 0 1l-3 8c-1-1 0-6-1-8-1-1 0-1-1-1s-1 1-1 2v1c-1 1-1 2-1 3l-1 2z" class="E"></path><path d="M561 835c3-3 3-7 4-11 0-2 2-9 5-11-2 6-4 11-5 17 0 4 0 8-1 13v8c-1-3-2-6-2-9-1-2 0-5-1-7z" class="U"></path><path d="M558 841c1 3 1 7 0 10v5c-1 3-1 6-2 9s-1 11 1 14l1 1h0c0-1 1-2 1-3s1-2 2-3c-1 2-2 5-2 7h-1-1c-2-2-4-2-6-3 4-12 6-24 7-37z" class="H"></path><path d="M582 786c0 5-3 11-4 16-2 7-3 15-5 23 0 4-1 8-2 12 0 2 0 4-1 6v1h0c0-9 1-18 3-26 1-5 2-10 2-15h0l3-9c1-3 2-6 4-8z" class="E"></path><path d="M581 808c0-1 0-2 1-3v-1c0-1 0-2 1-2s0 0 1 1c1 2 0 7 1 8l-8 22c-1-1-1-4-1-5l4-18 1-2z" class="C"></path><path d="M581 808v1c1 3 1 7 0 10s-2 6-4 9h-1l4-18 1-2z" class="L"></path><path d="M599 757c0 3-2 5-3 7-2 2-3 5-5 8s-4 6-6 10c2-2 2-4 4-5 0 3-4 7-5 10-2 3-3 5-3 8-2 4-1 9-2 13-1 1-1 3-1 4-1 1 0 0-1 2v1c-2 3-1 7-2 10 0 4-2 9-4 12 1-4 2-8 2-12 2-8 3-16 5-23 1-5 4-11 4-16l8-14s1-2 2-3c2-4 5-8 7-12z" class="K"></path><path d="M587 788c3-4 4-4 9-4-2 6-4 13-8 18-2-1-3-1-5-3 0-4 2-8 4-11z" class="N"></path><defs><linearGradient id="Al" x1="558.117" y1="831.098" x2="562.883" y2="817.902" xlink:href="#B"><stop offset="0" stop-color="#707074"></stop><stop offset="1" stop-color="#9c9b9a"></stop></linearGradient></defs><path fill="url(#Al)" d="M566 805h2 0 1l1 1h0c1 0 2 0 2 1l-1 4c0 1-1 2-1 2-3 2-5 9-5 11-1 4-1 8-4 11l-3-18h1c2 1 2 2 4 3 1-1 0-1 1-2 1-2 0-3 1-5v-2c0-3 0-4 1-6z"></path><path d="M598 729c1-1 2-3 5-3 2-1 4 0 6 1 1 1 2 2 2 4s-1 5-2 6c-2 2-3 2-5 2h0c-2-1-4-1-5-3-2-2-2-4-1-7z" class="B"></path><path d="M606 743c1-2 2-4 4-6l-11 20h0c-2 4-5 8-7 12-1 1-2 3-2 3l-8 14c-2 2-3 5-4 8h-1c0 2-1 4-2 5 0 1-1 2-1 3s-1 1-1 2 0 2-1 3c0-1-1-1-2-1h0l-1-1h-1 0-2c0-1 0-1 1-2v-1c0-3 2-5 4-7v1c2-2 3-4 5-5l2-2 2-4v-1l1-1c0-1 0-1 1-1v-2l2-3c1-3 3-5 4-7 2-4 4-7 7-10 1-3 3-4 5-7s4-6 6-10z" class="C"></path><path d="M577 794h0v-2l6-11c2-3 3-6 5-9h2l-8 14c-2 2-3 5-4 8h-1z" class="I"></path><path d="M813 261l2-2v-1c1-1 1-3 2-4v1h1v-1c0-1 0-1 1-2h1l-9 23c-1 2-1 3-2 5 0 2-1 4-2 6l-34 82-84 205-54 141-38 99-29 76h-1c0 1-1 3-1 5h-31c2-1 5-1 7-1h0l-8-1h22c2 0 7 1 9 0l-1-2c0-2 0-2-1-3 2-4 3-9 4-13l13-33c2-5 5-9 7-14l9-27c2-6 6-12 8-18l10-27 8-18 9-26 8-21 5-14 12-31 13-35c1-1 2-3 3-5l22-58 31-75 50-121 20-50 10-21 8-19z" class="C"></path><defs><linearGradient id="Am" x1="631.171" y1="776.104" x2="577.549" y2="642.7" xlink:href="#B"><stop offset="0" stop-color="#dad9d9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Am)" d="M636 613c1 1 1 2 2 3v9l1-7 3 1c1 0 2-1 3-1l1 1c0 1 0 0-1 1 0 2 1 6 0 7 0 1 0 4-1 5h0c1 1 1 3 0 4v10 13l-7 18c-1 3-2 6-4 9-1 0-4-1-6 0s-3 3-3 5h0c-1 1-1 2-2 3 0 1 0 1-1 2 0 1 0 2-1 2v1c-2 2-3 6-5 8h-2c-1 1-2 4-3 5l-7 10c-2 2-5 4-6 6l1 1c-1 3-1 5 1 7 1 2 3 2 5 3h-1l4 1h-1c0 1 0 2-1 3h1c-2 4-4 7-6 10s-4 4-5 7c-3 3-5 6-7 10-1 2-3 4-4 7l-2 3v2c-1 0-1 0-1 1l-1 1v1l-2 4-2 2c-2 1-3 3-5 5v-1c-2 2-4 4-4 7v1h-3l-8-26v-1c-1-3-2-7-1-10 0-3 1-4 0-7l-1 1v-1h-1c-1 0-2 1-3 1v1h-2c-4 0-7-2-9-4l1-5 1-5h0c1-2 3-5 3-7v-1l3-6v-3-1c1-4 2-5 5-7l1-2c1 0 3-1 4-2h0l1-1h-2v-1c2 0 3-1 4-2v-1h0c2 0 4-1 5-2l13-7c1 0 1 0 2-1h0c3 0 5-2 7-3-1-1-2-3-2-5h0c2-5 4-6 9-8 1 1 3 2 4 3 8-5 12-14 16-22l1-3c2-5 8-26 6-31l-1 1v-12l1-2h1c0-1 1-1 1-1 1 1 1 3 1 4 1 1 1 2 1 3v1h0c2-2 3-4 5-6 1-2 3-3 5-5 1-1 1-2 1-4z"></path><path d="M631 654c2-1 4-1 6 1 2 1 3 3 3 5s-1 4-2 5c-2 1-4 2-5 1-2 0-3-1-4-2s-2-2-2-4h0l1-1c0-2 1-3 3-5z" class="B"></path><path d="M571 760c1 0 2-1 4 0 2 0 5 1 6 3 1 3 1 5 0 8 0 3-2 4-5 5-1 0-2 1-4 0-2 0-4-1-6-3-1-2-1-5-1-8 1-3 3-4 6-5z" class="N"></path><defs><linearGradient id="An" x1="587.727" y1="737.248" x2="571.142" y2="710.339" xlink:href="#B"><stop offset="0" stop-color="#a3a3a4"></stop><stop offset="1" stop-color="#e6e5e5"></stop></linearGradient></defs><path fill="url(#An)" d="M561 724l29-14c11-7 19-18 26-29 1-2 3-7 6-8 0 1-1 1-1 3-1 1-1 2-1 4l-6 10c-6 9-12 17-20 25l-17 17-1 1-14 15c0 1-1 2-2 2-1 3-3 5-5 9l-1 1v-1h-1c-1 0-2 1-3 1v1h-2c-4 0-7-2-9-4l1-5 1-5h0c1-2 3-5 3-7v-1l3-6v-3-1c1-4 2-5 5-7 3-1 5 0 8 2h1z"></path><path d="M550 760l2-2v-1c3-2 5-4 8-7h0c-1 3-3 5-5 9l-1 1v-1h-1c-1 0-2 1-3 1z" class="J"></path><path d="M562 748h-2-1c2-1 4-3 5-5l11-11 1 1-14 15z" class="H"></path><path d="M577 732c-1-1-1-1-1-2 3-4 6-6 10-9 2-2 4-5 6-7 1 0 1 0 2 1l-17 17z" class="I"></path><path d="M552 722c3-1 5 0 8 2h1c1 2 2 4 2 6 0 3-1 5-3 6-2 2-4 2-6 2-3-1-5-2-6-4 0-1 0-1-1-1v-3-1c1-4 2-5 5-7z" class="B"></path><path d="M636 613c1 1 1 2 2 3v9l-1 20v10c-2-2-4-2-6-1-2 2-3 3-3 5l-1 1h0c-1 1-1 1-1 2 0 2-1 4-2 6 0 1-1 2-1 2 0 1 0 2-1 3h0c-3 1-5 6-6 8-7 11-15 22-26 29l-29 14h-1c-3-2-5-3-8-2l1-2c1 0 3-1 4-2h0l1-1h-2v-1c2 0 3-1 4-2v-1h0c2 0 4-1 5-2l13-7c1 0 1 0 2-1h0c3 0 5-2 7-3-1-1-2-3-2-5h0c2-5 4-6 9-8 1 1 3 2 4 3 8-5 12-14 16-22l1-3c2-5 8-26 6-31l-1 1v-12l1-2h1c0-1 1-1 1-1 1 1 1 3 1 4 1 1 1 2 1 3v1h0c2-2 3-4 5-6 1-2 3-3 5-5 1-1 1-2 1-4z" class="D"></path><path d="M594 687c1 1 3 2 4 3 1 2 2 4 2 6-1 2-3 5-5 5-3 1-6 0-8-1-1-1-2-3-2-5h0c2-5 4-6 9-8z" class="B"></path><path d="M636 613c1 1 1 2 2 3v9l-1 20v10c-2-2-4-2-6-1-1-1-1-1-1-2 4-8 4-16 4-25 1-2 1-4 1-6v-1l-1-1h0c1-1 1-1 1-2 1-1 1-2 1-4zm-15 21l4-4c0 1 1 1 1 2l-1 3h0c1 1-2 10-2 11-2 8-3 15-5 22-2 5-5 12-9 15-2 2-3 4-5 6-1 1-3 2-4 4v3c0-2-1-4-2-6 8-5 12-14 16-22l1-3c2-5 8-26 6-31z" class="E"></path><path d="M700 458v4c-1 2 1 4 1 7v2c0 2 1 5 1 8 1 7 2 15 5 22l-3 5c-1 2-1 4-2 5-2 4-3 10-6 13h-1l1 1c0 1-1 3-2 5l-5 14-23 58-8 21-9 22-5 14v-13-10c1-1 1-3 0-4h0c1-1 1-4 1-5 1-1 0-5 0-7 1-1 1 0 1-1l-1-1c-1 0-2 1-3 1l-3-1-1 7v-9c-1-1-1-2-2-3 0 2 0 3-1 4-2 2-4 3-5 5-2 2-3 4-5 6h0v-1c0-1 0-2-1-3 0-1 0-3-1-4 0 0-1 0-1 1h-1l-1 2-2-14c0-2 0-6-1-7 0-1-1-1-1-1-1-1-1-3-1-4v-2l7-11 3-6c1-2 1-4 1-5h-1c0-1 0-2-1-3h-2l-2 1c-1 1-4 1-6 0-1 0-3-2-3-4-1-3-1-5 1-8l-1-1-1 2-2 3-6 7-1 2-3 6-1-3v-1c0-1 0-1-1-2h-1v1c-1-1-1-2-1-3 0 3 0 7-1 9v3l-7-7 1-1-1-1 1-1c0-2 1-5 0-7l8-25v-1c0-1 0-2 2-4 4-2 9-6 13-9l15-12 14-10 6-6 10-7c-1 1-1 2-1 3 4-2 9-7 13-10l20-15c1-1 3-2 4-3l9-8z" class="K"></path><path d="M647 555v1h1v1c-1 3 0 6 0 8l-1-1v3c0-2-1-4-2-6v-1h0-1 0l3-5z" class="C"></path><path d="M696 470v-1c1-2 1-3 2-5 1 3-1 7-2 10 0 4-1 7-1 11-2 8-5 16-8 24l-1-1c1 0 1-1 1-2 1-1 2-3 3-5l1-5c1-1 1-2 1-3 1-2 1-5 2-7 0-1 0-2 1-3v-4c-1 2-1 3-1 4 0 2-1 4-1 6-1 2-2 3-3 4v1h0-3-2c0 1-1 1-1 2l-4 4c4-6 8-11 11-17 2-4 3-9 5-13z" class="I"></path><path d="M666 557c2 0 3 0 4 1 2 1 3 3 4 5 0 2 0 4-2 5-1 2-3 3-5 3h-3c-2-1-3-2-4-4 0-2 0-4 1-6 1-3 3-3 5-4zm-30 51c2-2 3-4 6-5 1 0 3 0 5 1 1 1 3 3 3 5 1 2 0 4-1 6-1 1-3 2-5 3-2 0-4-1-6-2-1-1-1-2-2-3v-5zm52-98c2-2 3-3 6-3 2 0 4 0 5 1l2 3h1c-2 4-3 10-6 13h-1c-3-1-6-2-8-5-1-3 0-6 1-9z" class="B"></path><path d="M696 474c2-3 2-6 3-8 1 1 1 2 1 3h1v2c0 2 1 5 1 8 1 7 2 15 5 22l-3 5c-1 2-1 4-2 5h-1l-2-3c-1-1-3-1-5-1-3 0-4 1-6 3l-1-1c3-8 6-16 8-24 0-4 1-7 1-11z" class="D"></path><path d="M700 469h1v2c0 2 1 5 1 8 1 7 2 15 5 22l-3 5h0c-1-5-1-9-1-14-1-2 0-5-1-7v-5c-1-2-1-3-1-4v-3l-1-4z" class="K"></path><defs><linearGradient id="Ao" x1="620.263" y1="544.328" x2="636.653" y2="554.251" xlink:href="#B"><stop offset="0" stop-color="#dad9d9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ao)" d="M638 531c2 1 4 1 7 1l1 1c-2 4-5 7-7 11-6 9-11 19-13 29h-1c0-1 0-2-1-3h-2l-2 1c-1 1-4 1-6 0-1 0-3-2-3-4-1-3-1-5 1-8l-1-1c2-3 5-6 7-8 5-7 11-14 18-17l2-2z"></path><path d="M612 559c2-1 3-2 5-2 3 1 5 2 6 4 2 1 2 3 1 5 0 1-1 2-2 4l-2 1c-1 1-4 1-6 0-1 0-3-2-3-4-1-3-1-5 1-8z" class="B"></path><defs><linearGradient id="Ap" x1="619.584" y1="583.713" x2="644.292" y2="607.24" xlink:href="#B"><stop offset="0" stop-color="#d1d1d0"></stop><stop offset="1" stop-color="#fdfdfc"></stop></linearGradient></defs><path fill="url(#Ap)" d="M625 578h1l1 6 16-23v1 2c-1 1-1 3-1 5-1 6 0 12-1 18l-1 1v3l-1 1v3 2 3c0 1-1 2-1 3h0l-1 1v1h0c-1-3 1-6 0-9v1c0 1 0 1-1 2v2c0 1 0 2-1 4 0 1 0 1 1 2v1 5c0 2 0 3-1 4-2 2-4 3-5 5-2 2-3 4-5 6h0v-1c0-1 0-2-1-3 0-1 0-3-1-4 0 0-1 0-1 1h-1l-1 2-2-14c0-2 0-6-1-7 0-1-1-1-1-1-1-1-1-3-1-4v-2l7-11 3-6z"></path><path d="M700 458v4c-1 2 1 4 1 7h-1c0-1 0-2-1-3-1 2-1 5-3 8 1-3 3-7 2-10-1 2-1 3-2 5v1l-1-2c-4 4-7 8-10 13l-1 2c-2 1-2 4-3 6-1 4-3 8-4 12 0 1-1 2-1 3-1 2-4 4-6 5l-13 10c-2 2-5 4-6 6-1 3-2 5-4 6l-2 1c-3 0-5 0-7-1l-2 2c-7 3-13 10-18 17-2 2-5 5-7 8l-1 2-2 3-6 7-1 2-3 6-1-3v-1c0-1 0-1-1-2h-1v1c-1-1-1-2-1-3 0 3 0 7-1 9v3l-7-7 1-1-1-1 1-1c0-2 1-5 0-7l8-25v-1c0-1 0-2 2-4 4-2 9-6 13-9l15-12 14-10 6-6 10-7c-1 1-1 2-1 3 4-2 9-7 13-10l20-15c1-1 3-2 4-3l9-8z" class="I"></path><path d="M645 498l10-7c-1 1-1 2-1 3-2 2-5 4-7 6l-12 9c-1 1-3 2-5 4l-18 14c0-1 0-1 1-1l-1-1-1 1h-1l15-12 14-10 6-6z" class="G"></path><path d="M630 513h1c-2 2-3 4-5 5s-3 3-4 4c-5 3-8 8-13 11-2 1-7 5-8 7l-1 2c-5 5-8 13-10 20-1 4 0 9 0 13l-1 1-2-2-1-1 1-1c0-2 1-5 0-7l8-25v-1c0-1 0-2 2-4 4-2 9-6 13-9h1l1-1 1 1c-1 0-1 0-1 1l18-14z" class="F"></path><path d="M610 526h1l1-1 1 1c-1 0-1 0-1 1-5 5-12 8-17 13v-1c0-1 0-2 2-4 4-2 9-6 13-9z" class="D"></path><path d="M700 458v4c-1 2 1 4 1 7h-1c0-1 0-2-1-3-1 2-1 5-3 8 1-3 3-7 2-10-1 2-1 3-2 5v1l-1-2c-4 4-7 8-10 13l-1 2v-1c1-3 3-5 4-8l-41 31c-4 3-8 8-13 12-5 3-10 7-15 10-3 3-6 7-9 9-1 0-2 1-2 2-1 1 0 0-2 0l-1 2h0a30.44 30.44 0 0 1 8-8l6-6 2-2c4-3 8-7 12-10 3-2 6-6 10-8 1-1 2-2 4-3l12-11c8-6 17-11 24-18 1-1 3-3 4-5 1-1 3-2 4-3l9-8z" class="C"></path><defs><linearGradient id="Aq" x1="603.545" y1="540.532" x2="619.449" y2="549.611" xlink:href="#B"><stop offset="0" stop-color="#cbcbcb"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#Aq)" d="M605 540h0l1-2c2 0 1 1 2 0 0-1 1-2 2-2 3-2 6-6 9-9 5-3 10-7 15-10 0 1 0 2-1 3h0c1 2 0 6 0 8-1 2-5 5-6 6-3 3-6 7-9 10-1 2-3 4-5 5 0 1-1 1-1 2l-1 1c-2 2-5 5-6 8v1h0v1c-1 1-1 2-2 3h0c0 2-1 4-1 5l-1 2-3 6-1-3v-1c0-1 0-1-1-2h-1v1c-1-1-1-2-1-3l-1-1v-6c1-3 2-5 2-8 1-2 2-3 2-5l3-3c0-1 0-2 1-3s1 0 1-1l3-3z"></path><path d="M597 575c1-2 2-3 4-4v1h0l-3 6-1-3z" class="V"></path><path d="M634 517c5-4 9-9 13-12l41-31c-1 3-3 5-4 8v1c-2 1-2 4-3 6-1 4-3 8-4 12 0 1-1 2-1 3-1 2-4 4-6 5l-13 10c-2 2-5 4-6 6-1 3-2 5-4 6l-2 1c-3 0-5 0-7-1l-2 2c-7 3-13 10-18 17-2 2-5 5-7 8l-1 2-2 3-6 7c0-1 1-3 1-5h0c1-1 1-2 2-3v-1h0v-1c1-3 4-6 6-8l1-1c0-1 1-1 1-2 2-1 4-3 5-5 3-3 6-7 9-10 1-1 5-4 6-6 0-2 1-6 0-8h0c1-1 1-2 1-3z" class="Y"></path><path d="M638 531c-1-1-1-1-1-2-1-2-2-5-1-7s3-3 5-4c1 0 3 0 5 1s4 3 5 6c-1 3-2 5-4 6l-2 1c-3 0-5 0-7-1z" class="B"></path><path d="M655 354c3-1 7 1 9 2 17 7 28 17 38 31 1 3 2 5 4 8v-1c0-2-1-3-2-4 4 0 7-1 10 1 1 0 1 0 2 1 2 0 5 2 7 3 1 1 3 2 4 3l2 1 3 2c4 2 6 9 8 13v2l-1 1 1 1v-1l1 1-7 15-26 65c-1 1-1 2-1 3-3-7-4-15-5-22 0-3-1-6-1-8v-2c0-3-2-5-1-7v-4l-9 8c-1 1-3 2-4 3l-20 15c-4 3-9 8-13 10 0-1 0-2 1-3l-10 7h-1l-1 1c-1 1-1 1-2 1-1 1 0 1-1 1v-2c-1-2-2-5-4-6l-1 1c-2-3-6-7-9-10-1 0-1 0-1-1v-1l-2-1-2-1c-2-2-4-3-5-5 0-1 0-2 1-3 1 0 2-1 3-1h2v-1c2 0 2 0 4-1h0l4-3-8-10c1-1 1-3 1-4 2-2 3-8 4-10l-1-1c1-1 1-3 2-5 1-1 2-3 2-5v-1c2 0 5-1 6-2-1-1-2-2-3-2 0 0-1 0-1-1 0-2 1-3 2-5s1-5 2-7l2-5 1-6c1-2 1-4 3-6l2-9 3-9c1-1 2-5 2-6l6-18z" class="N"></path><path d="M650 418c1 0 1 0 2 1v-1c4 3 6 7 9 10v1l-11-11z" class="O"></path><path d="M636 413l2-5c0 3 1 4 2 6l1 1v3c0 2 1 5 2 7h0c-1-1-2-3-2-4l-5-8z" class="B"></path><path d="M634 420c1-2 1-5 2-7l5 8c0 1 1 3 2 4l4 7c0 1 2 2 2 4 0 1 2 3 2 4 1 2 1 3 2 4l1 1c1 1 1 2 1 4 2 4 6 9 7 14 1 1 0 3 2 4l2 1c-1 0-4-1-6-1-2-1-5-2-7-4v-1c1-3-4-9-5-12s-1-6-3-9c-3-3-4-7-6-11 0-1-2-2-2-3-1-3-2-5-3-7z" class="T"></path><path d="M630 430c2 0 5-1 6-2-1-1-2-2-3-2 0 0-1 0-1-1 0-2 1-3 2-5 1 2 2 4 3 7 0 1 2 2 2 3 2 4 3 8 6 11 2 3 2 6 3 9s6 9 5 12v1c-3-2-6-3-9-6-1-1-3-2-5-4h-1c-3-4-6-6-9-10l-1-2-1 1-1-1c1-1 1-3 2-5 1-1 2-3 2-5v-1z" class="F"></path><path d="M633 433l4 3c1 1 2 3 3 4-2 0-4-1-5-2h-1c-1-2-1-3-1-5z" class="L"></path><path d="M630 431l3 2c0 2 0 3 1 5h1-3v2c-1 0-3-3-3-3l-1-1c1-1 2-3 2-5z" class="D"></path><path d="M629 437l1-1 4 2h1-3v2c-1 0-3-3-3-3z" class="C"></path><path d="M632 440v-2h3c1 1 3 2 5 2 1 1 2 2 2 3 1 1 1 2 1 3-2 0-3-1-4-1h0l-7-5z" class="E"></path><path d="M628 436l1 1s2 3 3 3l7 5h0v1c2 3 5 7 6 11h-1c-1-1-3-2-5-4h-1c-3-4-6-6-9-10l-1-2-1 1-1-1c1-1 1-3 2-5z" class="I"></path><path d="M629 443h3c3 2 6 6 8 9l-1 1h-1c-3-4-6-6-9-10z" class="C"></path><defs><linearGradient id="Ar" x1="681.162" y1="427.386" x2="674.296" y2="431.168" xlink:href="#B"><stop offset="0" stop-color="#27292f"></stop><stop offset="1" stop-color="#4b4f59"></stop></linearGradient></defs><path fill="url(#Ar)" d="M644 387c6 6 9 14 15 20 2 1 5 3 7 5 2 1 5 2 7 5h0l1 2v-1h1v2l1 1v-1 2h1c0 1 0 1 1 1 1 1 2 2 2 3l1 1c2 2 2 5 5 6v1c2 0 3 1 5 1l1 1h0l-21-9-5-2c-2-1-4-2-6-4-9-4-15-10-21-19 1-2 1-4 3-6l2-9z"></path><path d="M666 425l1-1c-1-1-1-2-2-3v-2-1c3 2 5 5 6 8v1l-5-2z" class="W"></path><path d="M639 402c1-2 1-4 3-6 2 2 3 6 6 8 4 6 9 11 13 16l-1 1h0c-9-4-15-10-21-19z" class="I"></path><path d="M627 442l1-1 1 2c3 4 6 6 9 10h-1l-5-5-3-3c1 2 2 5 3 7 2 3 4 5 5 8h0c1 1 1 3 2 4 0 1 1 0 1 1 1 2 3 4 4 6 1 3 2 5 3 8v-1h1v3h0c1 1 1 1 1 2l1 2v-1l1 1v2c0 1 1 1 0 2-1 2-2 3-2 5-2 0-1 0-2-1h-2l-2-1c0-1-1-1-2-1-4-1-7-3-11-4 1 2 5 4 6 6l-1 1c-2-3-6-7-9-10-1 0-1 0-1-1v-1l-2-1-2-1c-2-2-4-3-5-5 0-1 0-2 1-3 1 0 2-1 3-1h2v-1c2 0 2 0 4-1h0l4-3-8-10c1-1 1-3 1-4 2-2 3-8 4-10z" class="T"></path><path d="M626 469c1 0 2-1 3-1 3 7 8 14 10 21-5-2-9-5-14-6v-1l-2-1-2-1c-2-2-4-3-5-5 0-1 0-2 1-3 1 0 2-1 3-1h2v-1c2 0 2 0 4-1z" class="E"></path><path d="M706 395v-1c0-2-1-3-2-4 4 0 7-1 10 1 1 0 1 0 2 1 2 0 5 2 7 3 1 1 3 2 4 3l2 1 3 2c4 2 6 9 8 13v2l-1 1 1 1v-1l1 1-7 15-26 65c-1 1-1 2-1 3-3-7-4-15-5-22 0-3-1-6-1-8v-2c0-3-2-5-1-7v-4l-9 8c-1 1-3 2-4 3l-20 15c-4 3-9 8-13 10 0-1 0-2 1-3 4-4 8-7 12-10 15-12 31-26 39-44 6-15 6-28 0-42z" class="K"></path><defs><linearGradient id="As" x1="699.658" y1="435.025" x2="730.523" y2="479.536" xlink:href="#B"><stop offset="0" stop-color="#bbbaba"></stop><stop offset="1" stop-color="#f3f2f1"></stop></linearGradient></defs><path fill="url(#As)" d="M705 454h0c1-1 2-2 3-2l1 1 1 1c1 1 0 2 0 4v2l1 1h-1c-1 1 0 4 0 5s-1 2-1 2c0 3 0 9 1 11h1v-14c0-6 0-13 1-19 1-8 3-16 4-25 6 3 13 8 18 12l-26 65c-1 1-1 2-1 3-3-7-4-15-5-22 0-3-1-6-1-8v-2c0-3-2-5-1-7v-4l2-2 3-2z"></path><path d="M705 454v18c0 6 1 12 2 18 0 3 0 6 1 8-1 1-1 2-1 3-3-7-4-15-5-22 0-3-1-6-1-8v-2c0-3-2-5-1-7v-4l2-2 3-2z" class="D"></path><path d="M702 456c1 3 1 12-1 15v-2c0-3-2-5-1-7v-4l2-2z" class="F"></path><defs><linearGradient id="At" x1="566.709" y1="434.27" x2="614.555" y2="449.251" xlink:href="#B"><stop offset="0" stop-color="#151517"></stop><stop offset="1" stop-color="#3a465a"></stop></linearGradient></defs><path fill="url(#At)" d="M634 189c4 3 8 7 10 11 10 12 14 26 17 41 3 18 2 36-1 53l-2 11c0 2-1 4-1 6 0 3 0 4-1 6-1 4-2 8-2 12v-2l1-2v-1c0-1 1-2 2-3 2-5 3-11 4-16 1-2 2-5 2-7l2-12v3c-1 2 0 3-1 5 0 1 0 3-1 4v1 2c0 2 0 4-1 7v7c-1 1-1 3-1 4l-1 6c-3 5-4 10-5 15v1h0c-1 2-1 4-1 6v3 3l1 1-6 18c0 1-1 5-2 6l-3 9-2 9c-2 2-2 4-3 6l-1 6-2 5c-1 2-1 5-2 7s-2 3-2 5c0 1 1 1 1 1 1 0 2 1 3 2-1 1-4 2-6 2v1c0 2-1 4-2 5-1 2-1 4-2 5l1 1c-1 2-2 8-4 10 0 1 0 3-1 4l8 10-4 3h0c-2 1-2 1-4 1v1h-2c-1 0-2 1-3 1-1 1-1 2-1 3 1 2 3 3 5 5l2 1 2 1v1c0 1 0 1 1 1 3 3 7 7 9 10l1-1c2 1 3 4 4 6v2c1 0 0 0 1-1 1 0 1 0 2-1l1-1h1l-6 6-14 10-15 12c-4 3-9 7-13 9-2 2-2 3-2 4v1l-8 25c1 2 0 5 0 7l-1 1 1 1-1 1 7 7v-3c1-2 1-6 1-9 0 1 0 2 1 3v-1h1c1 1 1 1 1 2v1l1 3 3-6 1-2 6-7 2-3 1-2 1 1c-2 3-2 5-1 8 0 2 2 4 3 4 2 1 5 1 6 0l2-1h2c1 1 1 2 1 3h1c0 1 0 3-1 5l-3 6-7 11v2c0 1 0 3 1 4 0 0 1 0 1 1 1 1 1 5 1 7l2 14v12l1-1c2 5-4 26-6 31l-1 3c-4 8-8 17-16 22-1-1-3-2-4-3-5 2-7 3-9 8-5 1-10 5-14 7l-28 13-1 1c-4 3-5 6-8 9-1-1-1 0-1-1 0-2-1-3-1-4v-4c1-1 1-1 1-3-1-1-1-2-1-3s0-1-1-2l3-9 2-7v-8c0-10 0-21 1-31h0l1-3 2-5 1-3 1-3c0-2 1-4 1-6l5-13 9-28 6-18 10-32 29-91 25-74c6-19 13-38 18-58 3-15 4-31 4-46-1-17-5-36-15-50-2-4-6-7-9-10 3 0 6 4 8 6 0 1 0 1 1 2l2-1v-4c1-1 2-3 3-5 0-3 1-5 1-7v-2c1-2 1-3 1-5-2-3-4-5-6-7h-1v-1z"></path><path d="M536 692l121-381c0 3 0 4-1 6-1 4-2 8-2 12v-2l1-2v-1c0-1 1-2 2-3-2 10-6 20-9 29l-14 45-40 123-8 27-18 55c-3 9-7 18-9 28l-8 25-10 30c-2 5-3 11-6 15l-1 1 2-7z" class="G"></path><defs><linearGradient id="Au" x1="609.16" y1="453.32" x2="621.365" y2="457.332" xlink:href="#B"><stop offset="0" stop-color="#95979b"></stop><stop offset="1" stop-color="#bebfc1"></stop></linearGradient></defs><path fill="url(#Au)" d="M661 305c1-2 2-5 2-7l2-12v3c-1 2 0 3-1 5 0 1 0 3-1 4v1 2c0 2 0 4-1 7v7c-1 1-1 3-1 4l-1 6c-3 5-4 10-5 15v1h0c-1 2-1 4-1 6v3 3l1 1-6 18c0 1-1 5-2 6l-3 9-2 9c-2 2-2 4-3 6l-1 6-2 5c-1 2-1 5-2 7s-2 3-2 5c0 1 1 1 1 1 1 0 2 1 3 2-1 1-4 2-6 2v1c0 2-1 4-2 5-1 2-1 4-2 5l1 1c-1 2-2 8-4 10 0 1 0 3-1 4l8 10-4 3h0c-2 1-2 1-4 1v1h-2c-1 0-2 1-3 1-1 1-1 2-1 3 1 2 3 3 5 5l2 1 2 1v1c0 1 0 1 1 1 3 3 7 7 9 10l1-1c2 1 3 4 4 6v2c1 0 0 0 1-1 1 0 1 0 2-1l1-1h1l-6 6-14 10-15 12c-4 3-9 7-13 9-2 2-2 3-2 4v1l-8 25c1 2 0 5 0 7l-1 1 1 1-1 1-1-2c-2 1-3 6-3 8l-7 21c-1 1-1 4-2 5v-1h0l-1 1v1 1l-1 1v1 1h0c0-2 1-4 0-6 0 1-1 2-1 3l-1 1c-1 3-1 6-2 8 0 2-1 3-2 5-1 1-1 3-2 5-1 5-2 11-4 16v-3l-1 1v-4-1-1l1-1v-3c1-1 1-1 1-2v-2c1-1 1-2 1-3-1 1-1 2-2 3 2-10 6-19 9-28l18-55 8-27 40-123 14-45c3-9 7-19 9-29 2-5 3-11 4-16z"></path><path d="M627 431l3-1h0v1c0 2-1 4-2 5-1 2-1 4-2 5-1-1-1-2-1-3 1-2 2-5 2-7z" class="E"></path><path d="M618 460c1-5 3-9 4-14 1-3 2-6 3-8 0 1 0 2 1 3l1 1c-1 2-2 8-4 10l-1-1c-1 3-2 7-4 9z" class="G"></path><path d="M641 385c0-4 2-10 4-14 1-1 1-1 1-2 2-3 3-7 4-11s1-8 3-13l-2 11c-1 4-2 7-2 11-1 3-2 6-4 9-1 3-2 6-4 9z" class="I"></path><path d="M655 340v1h0c-1 2-1 4-1 6v3 3l1 1-6 18c0 1-1 5-2 6h-1s-1 2-1 3l-3 7c-1 1-1 1-1 2l-2 4-2 9c-1 2-2 4-3 5l7-23c2-3 3-6 4-9 2-3 3-6 4-9 0-4 1-7 2-11l2-11 2-5z" class="D"></path><path d="M634 408c1-1 2-3 3-5l2-9 2-4c0-1 0-1 1-2l3-7c0-1 1-3 1-3h1l-3 9-2 9c-2 2-2 4-3 6l-1 6-2 5c-1 2-1 5-2 7s-2 3-2 5c0 1 1 1 1 1 1 0 2 1 3 2-1 1-4 2-6 2h0l-3 1 7-23zm-52 173l-1-2c1-1 1-1 1-2l-1-1c0-3 1-6 2-9l4-15 6-18 4-11c1-2 1-4 2-6 0 2 0 3 1 5l-3 13c-2 2-2 3-2 4v1l-8 25c1 2 0 5 0 7l-1 1 1 1-1 1-1-2c-2 1-3 6-3 8z" class="C"></path><path d="M587 565c1 2 0 5 0 7l-1 1-1-1c-1-2 1-6 2-7z" class="J"></path><path d="M618 460c2-2 3-6 4-9l1 1c0 1 0 3-1 4l8 10-4 3h0c-2 1-2 1-4 1v1h-2c-1 0-2 1-3 1-1 1-1 2-1 3h-1c0 2-1 4-2 5-1 4-4 10-3 13l-2 5-2 7-1 2c-1 4-3 8-4 12v1l-1 2c-1-2-1-3-1-5 3-5 4-12 5-17 2-6 4-11 6-17 1-2 2-5 3-8 2-5 3-10 5-15z" class="E"></path><path d="M615 475c1-7 4-13 7-19l8 10-4 3h0c-2 1-2 1-4 1v1h-2c-1 0-2 1-3 1-1 1-1 2-1 3h-1z" class="D"></path><path d="M608 498c0 2 1 3 1 4h0c2 1 4 1 6 3 1 1 2 4 4 4l2 2h0c1 0 1 1 2 2l2 1-15 12c-4 3-9 7-13 9l3-13 1-2v-1c1-4 3-8 4-12l1-2 2-7z" class="O"></path><path d="M605 507l1-2c2 2 6 3 7 6s-1 5-2 7-4 6-6 7h-1c-1-2-1-3-3-5v-1c1-4 3-8 4-12z" class="U"></path><path d="M601 519c1-4 3-8 4-12 1 2 3 5 3 8-1 2-3 3-5 4h-2z" class="M"></path><path d="M615 475h1c1 2 3 3 5 5l2 1 2 1v1c0 1 0 1 1 1 3 3 7 7 9 10l1-1c2 1 3 4 4 6v2c1 0 0 0 1-1 1 0 1 0 2-1l1-1h1l-6 6-14 10-2-1c-1-1-1-2-2-2h0l-2-2c-2 0-3-3-4-4-2-2-4-2-6-3h0c0-1-1-2-1-4l2-5c-1-3 2-9 3-13 1-1 2-3 2-5z" class="W"></path><path d="M623 481c-1 1-1 1-1 2h0l-1 1c-1-1-2-2-2-3l2-1 2 1z" class="P"></path><path d="M625 482v1c0 1 0 1 1 1 3 3 7 7 9 10l1-1c2 1 3 4 4 6v2c1 0 0 0 1-1 1 0 1 0 2-1l1-1h1l-6 6-14 10-2-1c4-3 7-7 10-10-1-9-4-12-9-18 0-2 0-2 1-3z" class="Q"></path><path d="M610 493c-1-3 2-9 3-13l5 5h-1-2l-1 1h1c4 4 9 11 10 16 0 3-1 5-3 7 0 0-1 0-1 1v1h0l-2-2c-2 0-3-3-4-4-2-2-4-2-6-3h0c0-1-1-2-1-4l2-5z" class="D"></path><path d="M610 493c3 6 6 12 11 17v1h0l-2-2c-2 0-3-3-4-4-2-2-4-2-6-3h0c0-1-1-2-1-4l2-5zm1 65l1 1c-2 3-2 5-1 8 0 2 2 4 3 4 2 1 5 1 6 0l2-1h2c1 1 1 2 1 3h1c0 1 0 3-1 5l-3 6-7 11v2c0 1 0 3 1 4 0 0 1 0 1 1 1 1 1 5 1 7l2 14v12l1-1c2 5-4 26-6 31l-1 3c-4 8-8 17-16 22-1-1-3-2-4-3-5 2-7 3-9 8-5 1-10 5-14 7l-28 13-1 1c-4 3-5 6-8 9-1-1-1 0-1-1 0-2-1-3-1-4v-4c1-1 1-1 1-3-1-1-1-2-1-3s0-1-1-2l3-9 1-1c3-4 4-10 6-15l10-30 8-25c1-1 1-2 2-3 0 1 0 2-1 3v2c0 1 0 1-1 2v3l-1 1v1 1 4l1-1v3c2-5 3-11 4-16 1-2 1-4 2-5 1-2 2-3 2-5 1-2 1-5 2-8l1-1c0-1 1-2 1-3 1 2 0 4 0 6h0v-1-1l1-1v-1-1l1-1h0v1c1-1 1-4 2-5l7-21c0-2 1-7 3-8l1 2 7 7v-3c1-2 1-6 1-9 0 1 0 2 1 3v-1h1c1 1 1 1 1 2v1l1 3 3-6 1-2 6-7 2-3 1-2z" class="N"></path><path d="M562 662c2 0 3 0 4 1h2c1 0 1 0 3 1h1c1 0 3 1 4 3v1c1 2 2 3 3 5 1 1 1 0 1 1-1 2-1 2-2 3h-1v-3h0c0-2-1-4-3-5-2-3-3-4-6-4-2-1-3-1-4-2l-2-1z" class="O"></path><path d="M568 665c3 0 4 1 6 4 2 1 3 3 3 5h0v3c-1 1-2 0-3 0-1-5-3-8-6-12z" class="B"></path><path d="M565 632c1 3-1 6-1 9 1-1 1-2 1-3 2 4-2 9 0 13v1c0 1-1 3-1 4 0 2-1 3-2 4v2l2 1c-3-1-4-2-7-2v-1c1-5 3-10 4-15 1-4 2-9 4-13z" class="Q"></path><path d="M557 661c3 0 4 1 7 2 1 1 2 1 4 2 3 4 5 7 6 12-5-1-11-4-15-7l1-1c0-2-2-6-3-8z" class="P"></path><path d="M597 679l1 1c-2 2-4 4-6 5s-3 2-3 3c2 0-1 0 1-1h1 1 1l1-2h1c2-2 3-5 6-5l-7 7c-5 2-7 3-9 8-5 1-10 5-14 7l-28 13-1 1c-4 3-5 6-8 9-1-1-1 0-1-1h1c2-3 2-8 4-11 1-1 1-2 2-3 2 0 5-2 7-3l19-7c12-6 22-12 31-21z" class="K"></path><path d="M540 710c2 0 5-2 7-3l19-7c-5 6-14 7-20 12-2 1-3 1-3 3h0l-1 1c-4 3-5 6-8 9-1-1-1 0-1-1h1c2-3 2-8 4-11 1-1 1-2 2-3z" class="D"></path><path d="M573 607c1-1 1-4 2-5l2 3-1 1c1 1 1 2 1 2v2l2 1v3 3c1 3 0 4 2 7l8 8c1 1 1 1 1 3h1 0v2c1 1 1 1 1 2s1 2 1 4v1l-6-3c-1 0-2 0-3-1-1 0-1-1-2-1h0c-2-8-8-12-13-17l4-15z" class="B"></path><path d="M559 644c2-5 3-11 4-16 1-2 1-4 2-5 1-2 2-3 2-5 1-2 1-5 2-8l1-1c0-1 1-2 1-3 1 2 0 4 0 6h0v-1-1l1-1v-1-1l1-1h0v1l-4 15c5 5 11 9 13 17-5-3-10-5-15-8-1-1 0 0-1 0l-1 1c-2 4-3 9-4 13-1 5-3 10-4 15v1c1 2 3 6 3 8l-1 1c-1-1-3-3-5-3l-4 13c0 1-1 4-2 5h-2c1-5 5-11 5-16 1-9 6-17 8-25z" class="D"></path><path d="M594 570c0 1 0 2 1 3v-1h1c1 1 1 1 1 2v1l1 3-2 3 12 17c4 8 7 17 9 26 2 16-2 37-12 50-1 2-3 5-4 6-3 0-4 3-6 5h-1l-1 2h-1-1-1c-2 1 1 1-1 1 0-1 1-2 3-3s4-3 6-5l-1-1c9-12 16-25 17-40 2-21-8-41-21-57v-3c1-2 1-6 1-9z" class="E"></path><path d="M615 597c0 1 0 3 1 4 0 0 1 0 1 1 1 1 1 5 1 7l2 14v12l1-1c2 5-4 26-6 31l-1 3c-4 8-8 17-16 22-1-1-3-2-4-3l7-7c1-1 3-4 4-6 10-13 14-34 12-50-2-9-5-18-9-26h0l1 1 1 1h0v-3l2 3c2-1 2-2 3-3z" class="L"></path><defs><linearGradient id="Av" x1="542.629" y1="672.375" x2="551.309" y2="675.602" xlink:href="#B"><stop offset="0" stop-color="#a1a2a6"></stop><stop offset="1" stop-color="#cfcfcf"></stop></linearGradient></defs><path fill="url(#Av)" d="M559 628c1-1 1-2 2-3 0 1 0 2-1 3v2c0 1 0 1-1 2v3l-1 1v1 1 4l1-1v3c-2 8-7 16-8 25 0 5-4 11-5 16h2c0 1-1 6-2 7-2 6-3 12-6 17v1c-1 1-1 2-2 3-2 3-2 8-4 11h-1c0-2-1-3-1-4v-4c1-1 1-1 1-3-1-1-1-2-1-3s0-1-1-2l3-9 1-1c3-4 4-10 6-15l10-30 8-25z"></path><path d="M540 709h0l-1 1h-1v1l-1-1c0-4 3-7 5-10 0-2 1-3 1-4v-1c1-2 2-2 3-3-2 6-3 12-6 17z" class="C"></path><defs><linearGradient id="Aw" x1="597.08" y1="584.512" x2="624.851" y2="583.778" xlink:href="#B"><stop offset="0" stop-color="#afaeaf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Aw)" d="M611 558l1 1c-2 3-2 5-1 8 0 2 2 4 3 4 2 1 5 1 6 0l2-1h2c1 1 1 2 1 3h1c0 1 0 3-1 5l-3 6-7 11v2c-1 1-1 2-3 3l-2-3v3h0l-1-1-1-1h0l-12-17 2-3 3-6 1-2 6-7 2-3 1-2z"></path><path d="M625 573h1c0 1 0 3-1 5l-3 6c0-2 1-3 1-4 1-3 1-5 2-7z" class="Y"></path><path d="M608 563l1 1c-1 4-3 6-4 10h-1c0 2-1 4-1 6-1-1-1-2-1-3v-1-1c0-1 1-1 1-2 1-1 1-3 2-4h-1l-2 4-1-1 1-2 6-7z" class="I"></path><path d="M582 157c2-1 5-1 7-1 6 0 14-1 20 1h28c6-1 13-1 20 1 15 2 29 8 41 17l15 15 16 20 6 11c1 2 2 4 3 5 6 12 14 24 22 34 2 2 4 5 6 7 8 9 18 16 28 23l-4 10c-1 3-2 6-3 8l-4 8-2 5c-1 2-1 3-2 5-3 8-7 17-10 25-1 0-1 3-2 3l-10 24-11 28-5 12h0l-1-1v1l-1-1 1-1v-2c-2-4-4-11-8-13l-3-2-2-1c-1-1-3-2-4-3-2-1-5-3-7-3-1-1-1-1-2-1-3-2-6-1-10-1 1 1 2 2 2 4v1c-2-3-3-5-4-8-10-14-21-24-38-31-2-1-6-3-9-2l-1-1v-3-3c0-2 0-4 1-6h0v-1c1-5 2-10 5-15l1-6c0-1 0-3 1-4v-7c1-3 1-5 1-7v-2-1c1-1 1-3 1-4 1-2 0-3 1-5v-3l-2 12c0 2-1 5-2 7-1 5-2 11-4 16-1 1-2 2-2 3v1l-1 2v2c0-4 1-8 2-12 1-2 1-3 1-6 0-2 1-4 1-6l2-11c3-17 4-35 1-53-3-15-7-29-17-41-2-4-6-8-10-11-7-9-21-14-32-17-5-1-11-1-16-1 0-1 0-3-1-4-2-2-3-7-4-9l1-1z" class="D"></path><path d="M671 277c3-1 8 0 11 0l1 1-1 1c-2 0-8 1-11 0v-2z" class="E"></path><path d="M702 278c2-1 3-1 5-1 3 0 4 1 6 3l-1 1c-2-1-5-1-7-1h-4c-1 0-1-1-1-1l2-1z" class="C"></path><path d="M667 226c2 1 5 2 6 4 1 1 1 4 1 5-1 2-3 4-4 5-2-4-2-9-3-14z" class="B"></path><path d="M645 172c2 1 5 4 7 6 1 0 2 1 3 2v1c3 2 5 5 7 7-2 0-2-1-3-2h-1c0 2 4 5 5 7 0 2 1 3 2 5h1l-1 1h-1c0-1-1-1-1-2l-5-7c-1-2-2-4-4-6l-1-1c-1-2-3-3-4-5s-4-4-4-6z" class="V"></path><path d="M737 298h3c3 1 5 2 7 5v4c-1 3-2 4-4 5s-4 1-5 0c-2 0-4-1-5-3s-1-4 0-7c1-2 2-3 4-4zm-49-25c3-1 5-1 8 1 2 1 3 3 3 6s-2 4-4 6l-4 1c-2 0-4-1-5-3-2-1-2-4-2-6 0-3 3-4 4-5z" class="B"></path><path d="M661 319h1c0-4 2-9 3-12l2 1c2 1 4 1 5 3s1 4 1 5l-1 2h0v1 1c-2 2-4 2-6 3h-1c3 0 5 1 7 0s2-1 4 0c1-1 1-1 2-1l1 1h11 5c2-1 5 0 7 0l1-1h7 3c1-1 6 0 8 0 2-2 9-1 12-1h2c1 0 1-1 3-1l1 1c1 0 2 0 3-1 2 1 5 0 7 0v-1h7 1 1 3c1 1 3 0 4 0h10c1-1 2 0 4 0l-1-1c-1-1-3-1-3-1-2 0-4 0-6 1-3 0-7 1-10 0l1-1h5c2-2 6 0 8-1s7 0 10 0l-2 5h0c0-1 0-1-1-2-5 1-11 1-17 1-1 1-2 2-4 2l-57 3-24 1c-4 0-9 0-13 1h-4v1l-2 7h0v-3l1-1v-6l1-6z" class="G"></path><path d="M667 308c2 1 4 1 5 3s1 4 1 5l-1 2h0v1c-1 0-2 1-3 2-2 1-4 2-6 1h0c0-3 2-11 4-14z" class="B"></path><path d="M665 327l-3-1c0-1 0-1 1-2l100-4c-1 1-2 2-4 2l-57 3-24 1c-4 0-9 0-13 1z" class="H"></path><path d="M582 157h14c1 0 2 1 4 1 3 1 7 1 10 2 14 4 25 12 35 22 7 7 12 17 16 27 1 4 3 8 4 12 0 1 1 4 2 5 1 5 1 10 3 14l-1 1c2 18 1 36-1 54 0 3-1 10-3 12-1 3-3 8-3 12h-1c0-1 0-3 1-4v-7c1-3 1-5 1-7v-2-1c1-1 1-3 1-4 1-2 0-3 1-5v-3l-2 12c0 2-1 5-2 7-1 5-2 11-4 16-1 1-2 2-2 3v1l-1 2v2c0-4 1-8 2-12 1-2 1-3 1-6 0-2 1-4 1-6l2-11c3-17 4-35 1-53-3-15-7-29-17-41-2-4-6-8-10-11-7-9-21-14-32-17-5-1-11-1-16-1 0-1 0-3-1-4-2-2-3-7-4-9l1-1z" class="L"></path><path d="M585 167c7 0 13 0 19 1 14 3 28 11 37 22 8 8 13 17 17 28 1 4 2 7 3 11h1 0c0 1 0 1 1 2v1 1c1 2 0-1 0 1v1c1 1 1 4 1 5 1 2 0 3 1 5l1-3c1 2 0 5 1 7 0 12 0 25-1 37-1 1-1 2-1 3v-3l-2 12c0 2-1 5-2 7-1 5-2 11-4 16-1 1-2 2-2 3v1l-1 2v2c0-4 1-8 2-12 1-2 1-3 1-6 0-2 1-4 1-6l2-11c3-17 4-35 1-53-3-15-7-29-17-41-2-4-6-8-10-11-7-9-21-14-32-17-5-1-11-1-16-1 0-1 0-3-1-4z" class="E"></path><path d="M661 229h1 0c0 1 0 1 1 2v1 1c1 2 0-1 0 1v1c1 1 1 4 1 5 1 2 0 3 1 5 1 5 1 9 1 15v-1c0-2 0-4-1-6l-4-24z" class="F"></path><path d="M665 245l1-3c1 2 0 5 1 7 0 12 0 25-1 37-1 1-1 2-1 3v-3l-2 12c0 2-1 5-2 7l3-20c1-11 1-21 1-32 1 2 1 4 1 6v1c0-6 0-10-1-15z" class="H"></path><path d="M763 320c6 0 12 0 17-1 1 1 1 1 1 2h0c-1 2-1 3-2 5-3 8-7 17-10 25l-48-5c-1-1-6-1-8-1-1 2-2 4-4 5s-5 1-7 0c-1 0-3-2-3-3h0-1v-3c-3-1-4 0-7 0-2 0-7 1-9 0-3 0-6 0-8-1-1 0-4 0-5-1h-1 0-4 0l-2-2h-2v-1c-1-1-1-3-1-4l2-7v-1h4c4-1 9-1 13-1l24-1 57-3c2 0 3-1 4-2z" class="D"></path><path d="M692 337c2 0 5-1 6 0-1 1-3 1-4 1h-1c-2 0-4 0-6-1h5z" class="K"></path><path d="M660 339l14-1-1 1c-1 0-1 1-1 1-2 1-7 0-8 0l8 2c9 1 17 1 26 1l1 4h0-1v-3c-3-1-4 0-7 0-2 0-7 1-9 0-3 0-6 0-8-1-1 0-4 0-5-1h-1 0-4 0l-2-2h-2v-1z" class="E"></path><path d="M751 333h4c1 1 3 2 4 4v6c-1 2-3 3-6 4h-3c-2-1-3-2-4-4s-1-4-1-6c1-2 3-3 6-4zm-53 10c1-2 1-4 3-5 2-2 5-2 7-1s4 2 4 4c1 1 1 2 1 4-1 2-2 4-4 5s-5 1-7 0c-1 0-3-2-3-3l-1-4z" class="B"></path><path d="M763 320c6 0 12 0 17-1 1 1 1 1 1 2h0c-1 2-1 3-2 5v-3-1h-1-2c-4 0-7 1-10 1-5 0-11-1-15 1l-1-1c-2 0-5 0-8 1h-9c-5 0-10 0-14 1h-10c-2 1-5 0-8 1-5 0-9-1-13 0h-2c1 1 1 1 2 1h1c2 1 9 0 12 0-4 1-9 0-13 1-7 1-16 2-23 1-1 0-2-1-3-1h-1v-1h4c4-1 9-1 13-1l24-1 57-3c2 0 3-1 4-2z" class="E"></path><path d="M763 320c6 0 12 0 17-1 1 1 1 1 1 2-2 1-6 0-8 0l-14 1c2 0 3-1 4-2z" class="L"></path><defs><linearGradient id="Ax" x1="707.684" y1="360.166" x2="694.654" y2="393.484" xlink:href="#B"><stop offset="0" stop-color="#d7d6d6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ax)" d="M655 340c1-5 2-10 5-15v6l-1 1v3h0c0 1 0 3 1 4v1h2l2 2h0 4 0 1c1 1 4 1 5 1 2 1 5 1 8 1 2 1 7 0 9 0 3 0 4-1 7 0v3h1 0c0 1 2 3 3 3 2 1 5 1 7 0s3-3 4-5c2 0 7 0 8 1l48 5c-1 0-1 3-2 3l-10 24-11 28-5 12h0l-1-1v1l-1-1 1-1v-2c-2-4-4-11-8-13l-3-2-2-1c-1-1-3-2-4-3-2-1-5-3-7-3-1-1-1-1-2-1-3-2-6-1-10-1 1 1 2 2 2 4v1c-2-3-3-5-4-8-10-14-21-24-38-31-2-1-6-3-9-2l-1-1v-3-3c0-2 0-4 1-6h0v-1z"></path><path d="M742 373c5 1 10 3 14 6l-1 1c-2 0-6-1-8-2s-4-3-5-5z" class="D"></path><path d="M655 340c1-5 2-10 5-15v6l-1 1v3c-1 4-2 7-3 11-1 2-2 3-1 5 1-1 1-2 1-3 1 0 1-1 2-1h2c1 1 3 0 4 1h1c-1 0-2 1-3 0-1 0-3 0-4 1h-1v1h1l3-1c1 0 3 0 4 1l1 1-4 1c-1 0-3 0-4-1h-2v1h1c1 1-1 0 1 1h2l1 1c1 0 3 0 4 2h-1c-2-1-6-3-9-2l-1-1v-3-3c0-2 0-4 1-6h0v-1z" class="C"></path><path d="M702 387c7 1 13 3 20 7 5 3 11 8 17 10l2-2c1 1 2 2 2 3 1 2 0 5-1 7 2-1 1-4 4-6l-5 12h0l-1-1v1l-1-1 1-1v-2c-2-4-4-11-8-13l-3-2-2-1c-1-1-3-2-4-3-2-1-5-3-7-3-1-1-1-1-2-1-3-2-6-1-10-1 1 1 2 2 2 4v1c-2-3-3-5-4-8z" class="D"></path><path d="M725 367c3-2 5-3 8-2 2 0 4 2 6 4 0 1 1 2 1 3 0 2 0 3-1 4 0 2-2 4-4 4-2 1-5 1-7 0s-4-2-4-4c-1-3-1-6 1-9z" class="B"></path><path d="M659 335h0c0 1 0 3 1 4v1h2l2 2h0 4 0 1c1 1 4 1 5 1 2 1 5 1 8 1 2 1 7 0 9 0 3 0 4-1 7 0v3h1 0c0 1 2 3 3 3 2 1 5 1 7 0s3-3 4-5c2 0 7 0 8 1l48 5c-1 0-1 3-2 3l-10 24-1 1c-4-3-9-5-14-6 0 0-1-1-2-1 0-1-1-2-1-3-2-2-4-4-6-4-3-1-5 0-8 2-7-1-14-5-21-8-11-4-22-7-34-10h-1c-1-1-2-1-4-1h-1c-1-1-3 0-4-1h-2c-1 0-1 1-2 1 0 1 0 2-1 3-1-2 0-3 1-5 1-4 2-7 3-11z" class="K"></path><path d="M721 346l48 5c-1 0-1 3-2 3h0v-1-1c-3 0-6 1-8 1s-4-1-5-1c-4-1-8-1-12-1v-1h3-1c-1 0-1-1-2-1h-3-3c-1 0-1-1-2-1h-2-1c-2-1-2 0-4-1h-2c-2 0-3 0-4-1h0z" class="G"></path><path d="M609 157h28c6-1 13-1 20 1 15 2 29 8 41 17l15 15 16 20 6 11c1 2 2 4 3 5 6 12 14 24 22 34 2 2 4 5 6 7 8 9 18 16 28 23l-4 10c-1 3-2 6-3 8l-23-4c-15-4-29-10-40-20-15-13-26-30-35-47-5-11-10-23-16-33l-5-7-6-9c-2-2-4-5-7-7v-1c-1-1-2-2-3-2-2-2-5-5-7-6-1-2-4-3-5-4-6-4-14-7-21-9-3-1-7-1-10-2h0z" class="B"></path><path d="M670 189h0c-1-1-6-4-6-5h2c1 1 2 1 3 1 6 3 12 7 18 11 2 2 6 3 7 5-1 0-1 0-2-1l-15-9c-1-1-2-1-3-2s-1-1-2-1c-1 1-1 0-2 1z" class="O"></path><defs><linearGradient id="Ay" x1="734.777" y1="250.137" x2="719.269" y2="254.351" xlink:href="#B"><stop offset="0" stop-color="#262731"></stop><stop offset="1" stop-color="#50565d"></stop></linearGradient></defs><path fill="url(#Ay)" d="M716 246c1 0 1 0 2-1l-1-2v-1c0 1 1 1 2 1-1-1-1-1-1-2h3c2 1 3 2 6 3 1 2 3 6 6 6 2 1 3 5 6 6 2 0 3 1 4 2h1c2 1 3 3 5 5h-1c-3-2-6-5-8-6h-2l-2-1c1 2 3 5 2 7l1 1c1 0 1 0 2 1l2 2 4 3c1 0 2 1 4 2l-1 1-34-27z"></path><defs><linearGradient id="Az" x1="714.008" y1="235.478" x2="702.961" y2="239.357" xlink:href="#B"><stop offset="0" stop-color="#55555c"></stop><stop offset="1" stop-color="#7f8183"></stop></linearGradient></defs><path fill="url(#Az)" d="M701 219c5 5 12 9 17 14h1s1 0 1 1v1 1h3-1l-1 1h3l-1 1h-1l1 1h0c1 0 2 0 3 1h-2c1 2 2 3 3 4-3-1-4-2-6-3h-3c0 1 0 1 1 2-1 0-2 0-2-1v1l1 2c-1 1-1 1-2 1l-26-25h2l2 2h1c1-1 2-2 3-2l1-1 1-1h1z"></path><defs><linearGradient id="BA" x1="664.292" y1="182.398" x2="661.696" y2="189.636" xlink:href="#B"><stop offset="0" stop-color="#15161b"></stop><stop offset="1" stop-color="#363639"></stop></linearGradient></defs><path fill="url(#BA)" d="M652 178c6 0 13 4 17 7-1 0-2 0-3-1h-2c0 1 5 4 6 5h0c2 1 3 2 5 3h1c1 0 2 1 3 2s3 2 5 4h0l3 3h0v2h1v-1c1 0 1 1 2 2 3 5 7 10 11 15h-1l-1 1-1 1c-1 0-2 1-3 2h-1l-2-2h-2 0c-3-3-6-7-9-10s-5-5-7-8l-1 1-5-7-6-9c-2-2-4-5-7-7v-1c-1-1-2-2-3-2z"></path><path d="M655 180h3c-1 0-2 1-3 1v-1z" class="N"></path><path d="M668 197l1-1-2-2c1 0 1 0 2 1s4 3 6 4h-2v1h-1c0 1 1 2 2 3l-1 1-5-7z" class="P"></path></svg>