<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="152 49 765 918"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#141413}.C{fill:#bdbcba}.D{fill:#a4a2a0}.E{fill:#bab8b7}.F{fill:#292828}.G{fill:#e3e2e0}.H{fill:#30302f}.I{fill:#b6b5b2}.J{fill:#d6d5d3}.K{fill:#2a2927}.L{fill:#efeeed}.M{fill:#212020}.N{fill:#cac9c7}.O{fill:#373635}.P{fill:#252423}.Q{fill:#d0cfcc}.R{fill:#91908f}.S{fill:#1b1a1a}.T{fill:#e9e8e7}.U{fill:#484746}.V{fill:#4f4f4d}.W{fill:#8b8988}.X{fill:#dbd9d8}.Y{fill:#41403e}.Z{fill:#696867}.a{fill:#3b3a39}.b{fill:#9c9b99}.c{fill:#e0dedd}.d{fill:#7e7e7c}.e{fill:#adacaa}.f{fill:#f6f5f3}.g{fill:#838381}.h{fill:#969592}.i{fill:#767673}.j{fill:#070706}.k{fill:#5c5b58}.l{fill:#656462}.m{fill:#6a6969}.n{fill:#5c5b5a}.o{fill:#fefefe}.p{fill:#edebe7}</style><path d="M212 920l-6-1h6v1zm379-458l1-1c0 1 1 1 1 1l1 3c-1 0-2 1-3 2l-1-2 1-3z" class="L"></path><path d="M821 913h4c6 1 14 0 21 1-5 1-12 0-16 0-3-1-6 0-9-1z" class="G"></path><path d="M589 465s1-1 1 0l1 2-3 3-3-2c2-1 3-1 4-3z" class="a"></path><path d="M777 926h8c5 1 10 0 14 1 3 0 5 0 8 1h-2c-3-1-5 0-7-1h-13c-3 0-6 0-8-1z" class="T"></path><path d="M249 923c4 0 8 0 12 1h0-1l-25 1h-1v-1l15-1z" class="c"></path><path d="M595 429c1 1 2 2 3 4 2 2 2 6 1 8v1l-1-2c0-1 0-3-1-5 0-2-1-3-2-4s0-1 0-2z" class="M"></path><path d="M575 650h11c-2 1-4 3-5 4h-3v1c-1-1-1-2-1-3v-1h-1l-1-1z" class="E"></path><path d="M212 919c10-2 20 0 30-2h4v1c-3 0-7-1-9 1l-25 1v-1z" class="G"></path><path d="M434 963h-46c6-1 12-1 18-1h21c3 0 5 1 7 1zm-129-22l-45-1v-1l45 2h0z" class="T"></path><path d="M731 886c2 0 3 0 5 1h8 12l15 1c3 1 8 0 12 1-12 0-25 0-37-1-3 0-6 1-9 0h-2l-4-2z" class="X"></path><path d="M427 962l49-1 1 1c-9 1-18 0-27 1-5 0-11 1-16 0-2 0-4-1-7-1z" class="f"></path><path d="M740 896c5 0 57 1 58 1h-37-14-7v-1z" class="T"></path><path d="M699 928h14-1-2 2c1 0 1 1 2 1h6 14c1 0 1-1 2 0-1 1-3 0-4 1-3 0-17 1-19 0s-4-1-6-1h-14-2v-1h2 6z" class="f"></path><path d="M747 943h3l-25 2c-3 0-5-1-8 0h-10-18c1-1 2-1 4 0l1-1h0-3l1-1c1 0 2 0 4 1l51-1z" class="L"></path><path d="M756 919h1 3c-1-1-2-1-3-1h1 0 3v-1h1 2 5v-1 1c2 1 7-1 9 1-3 0-9-1-11 1h0c6 0 12 0 18 1-6 0-12-1-18 0-3 1-8 0-10 2-2 1-5 0-7-1h0 4 1c-1 0-2-1-2-1l3-1z" class="G"></path><path d="M258 904c3-1 17 0 21 0l-3 1c3 1 7-2 9 2-1 1-3 3-6 4-2-2-7 0-9-1-1 0-2-1-3-1h3c1-1 0-1 1-1h2 6 0c0-1 1-1 2-1-2-1-2-1-4-1-1-1-4-1-5 0h-1c-1 0-2-1-3-1-3 0-7 0-10-1z" class="J"></path><path d="M267 915v-1c-2-3-7-1-10-2h4c2 0 2-1 4-1h1-4c-1 1-3 0-5 0h0-6-12 1l25-2h2c1 0 2 1 3 1 2 1 7-1 9 1-2 1-5 2-8 2h-2c-1 1-1 1-1 2h-1z" class="G"></path><path d="M597 435c1 2 1 4 1 5l1 2h0c-1 2-3 4-4 6v2c-1 4 1 11-1 15l-1-3s-1 0-1-1l-1 1v-1c-1-5-1-9 2-12l-2-2c2-2 5-5 6-7v-5z" class="O"></path><path d="M593 449c1 4 1 9 0 13h0s-1 0-1-1l-1 1v-1c-1-5-1-9 2-12z" class="G"></path><path d="M777 911c2 0 3 0 4-1l1 1h1 1 0 1c1 0 1 0 1 1 4 1 9 0 13 2h-28s0-1 1-1h-1c-1 0-4-1-5 0-2 1-5 0-7 0 0 0-1 1-2 1h-3v-1h4c1-1 5-1 7-1 2-2 9 1 12-1zm-415 37l-1-1h-2c-5-1-11 0-16 0h-28c-3 0-6 0-8-1h1 21 10c4-1 8 0 12-1h1c1 1 3 1 5 1h12l2 2h-9z" class="T"></path><path d="M537 960l-2-1h0 14 1 6 1l4 1 4 1c2 1 5 0 8 1h5c5 1 33 0 35 2h-7-15l-11-1h-5c-3-1-5-1-8-1h-6c-8-2-16-2-24-2z" class="L"></path><path d="M340 950h0 2c1-1 1-1 2-1 2 1 3 1 5 1 1 1 4 0 6 0 1 1 2 0 4 1l-1 1c-4 0-7-1-10-1h-39c-6 0-12-1-18 0h-1 1l6-1h16 27z" class="f"></path><path d="M757 914c1 0 2-1 2-1 2 0 5 1 7 0 1-1 4 0 5 0h1c-1 0-1 1-1 1-3 0-5 0-7 1h-2-1l-2 1h-1-1c-1 0-1 0-2 1h-2 0c1 2 2 0 3 2l-3 1s1 1 2 1h-1-4 0l-13 1h0c1 0 2-1 3-1l4-2h5c-1-1-4-1-6-1v-1c4-1 10-1 14-2v-1z" class="Q"></path><path d="M750 907c2 0 4 0 7 1h2c5 0 12-1 17 1-1 1-2 1-4 1h-4-7-1c1 1 4 0 6 0h6c1 0 1 1 2 1h3c-3 2-10-1-12 1-2 0-6 0-7 1h-4v-1h-4v-1h0l1-1v-1h-1v-2z" class="G"></path><path d="M258 904c-1 1-3 1-4 1h-12c2-1 5-1 7-1l11-1c2 0 4 1 6 0 3-1 6-1 10-1 1 0 4 0 6-1 2 0 5 0 7-1v-1c-1-1-2 0-3 0h-3 0c-2-1-2-1-3-1-3 0-5-1-7-1v-1l17 1h0c-1 2 0 4-1 6-4 0-7 1-10 1-4 0-18-1-21 0z" class="T"></path><path d="M246 918l23-1c1 0 2 0 3 1l1 1h0l1 1h7l-19 1h-7c-1-1-3-1-5-1-4 0-9 0-13-1 2-2 6-1 9-1z" class="L"></path><path d="M272 918l1 1h0l1 1c-3 1-8 0-11 0l-15-1h16c1-1 3-1 4-1h3 1z" class="G"></path><path d="M642 949h58c-11 1-22 0-32 0l-37 2c-6 0-11-1-17 1-1-1-2 0-3 0h-11l-14 1c-7 0-13 1-20 0-2 0-5 0-8-1 0-1-9 0-10-1h0 1 9c5 1 11 2 17 2 5 0 10-1 15-1l15-1h10c4-1 9-1 13-1l14-1z" class="E"></path><path d="M363 768v-2c1-3 1-6 2-9 0-1 0-4 1-5l2-2c-5-2-9-5-14-7v-1h-1c1 0 2 0 3 1v-1-1-1l1 1h1 0 1l1 1h-2v1l2 1c1 1 1 1 2 1h0v-1l1 1h1v-2c0-1-1-1-1-2l2 1 1-1c7 1 15 1 22 1h0-13l1 1-1 1-1-1-1 1c1 1 1 2 0 3v1-1l-1-1-1 1-1 2h2v1h-3l-1 1h0c0 1-1 2-1 2l-1 1c-1 4-1 10-3 14z" class="K"></path><path d="M750 907h0c-2 0-3-1-4-2h-2l-1-1h-1 1c1-1 6-1 7-1h4-2c1-1 3-1 4-1h0c1 1 1 1 2 1h1 2l1 1h9c2-1 4-1 6 0h7l8 1v1h-7-1-1c-3-1-6 0-9 0v-1h5 0-12 1c1 0 3 0 4 1h1l-1 1v-1c-2 0-3 1-4 0h-5v1l1-1 2 1h0c-1 0-2 0-3 1l-1-1c-1 0-2 0-3 1h-2c-3-1-5-1-7-1z" class="L"></path><path d="M585 468l3 2-9 6v21h-3v-3l-1-10c0-1 1-4 0-6l-2 2v1l-3 1h-1v-1l3-3v-1c5-3 9-6 13-9z" class="F"></path><path d="M734 896h6v1h-3v1h5v1h-2v1l18 1c3 1 5 1 7 1h0c-2 0-4 1-6 0h-3c-1 0-3 0-4 1h2-4c-1 0-6 0-7 1h-1 1l1 1h2c1 1 2 2 4 2h0v2h1v1l-1 1h0v1h-1c0-1 0-1-1-1v1l-3-3h0 2c-1-2-6-1-8-3-1-1-3-1-4-1v-1c1 0 2 0 4-1-2 0-2 0-4-1l1-1c-1-1-1-1-2-1 0-2 0-2-1-3h2l-1-1z" class="J"></path><path d="M652 800l1 9h7c4 0 7 3 8 6l1 1 1 2-2 1c-1-1-5-1-6 0-2 1-3 2-3 4h-1c0-2 2-3 3-4h-3-2l-1-2h-1l-1-1 1-1c0-1 1-2 0-3s-1-2-2-3c0-1 0-2-1-2l-1-2h1v-1c0-2 0-2 1-4z" class="Y"></path><path d="M660 809c4 0 7 3 8 6l1 1v1h-4l-7-1 1-1v-3h0c1-1 1-2 1-3z" class="E"></path><path d="M665 817l-2-1c-1 0-2-1-2-2 1-1 1-1 3-1s3 2 4 3l1 1h-4z" class="N"></path><path d="M617 703c-1 0-2 0-3-1v-1c1-5 10-19 14-20l1-1v-4l-1 1-1-1c1-1 2-3 2-4 1-4 0-8 1-12 0 3 0 7 1 10v6c1 2 1 4 3 6v1h0l-2-2-3 1v2h-2s-2 1-2 2v2c-1 1-2 1-2 3l-3 6h1v2h1v-2l1 1c0 1 0 1 1 2h-1l-1 1h-1c-1 1-2 1-3 1l-1 1z" class="F"></path><path d="M591 318c2 0 2-1 4 0 2 2 4 5 4 8 1 5-2 7-4 11v3c0 3 1 9-1 12h0v-1c1-3 0-9-1-13h0c-1 1-2 4-2 5-1 0-1-1-2-1-2 0-3-1-5 0l-7-2-1-1c1-1 0-1 0-3-1 0 0-1 0-1l9 3c1 0 3 1 4 1 0-1 0-2 1-3 0-2 0-4 1-6h1c-1 1-1 2-1 3l1 1 1-2v2h1c1-1 2-3 2-5h0v-2c1-3 0-6-3-9h-2 0z" class="H"></path><path d="M558 952c3 1 6 1 8 1 7 1 13 0 20 0l14-1h11c1 0 2-1 3 0h1c2 1 5 0 6 0h14c1 0 2 1 3 1h0c-7 0-15 0-22 1-5 1-11 0-17 1s-12-1-18 1h-12 0-4l-1-1h-2c-1-1-3-1-4-2h-1l1-1zm-150 2c6 0 13-1 20 0 2 1 3 0 6 1h8c1 1 1 1 2 1 2 0 8-1 10 0h3v1c-2 1-8 0-11 0-2-1-4 0-7 0l-1-1h-8c-3 2-12 1-15 1l-38-1h-65l88-1c1-1 3-1 4-1h5-1z" class="G"></path><defs><linearGradient id="A" x1="579.578" y1="397.094" x2="573.946" y2="397.405" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M574 368c2-1 3-1 5 0 0 8-1 57 1 60l2 1c-2 0-3-1-4-1-1-1-1-1-2-1-2 0-3 0-4-1s-2-1-3-1h0l-1-1h-3v-1h0 1c1 0 2 0 3 1h1l4 2c1-8 0-16 1-24l1 1v-1-28c0-2-1-4-2-6z"></path><path d="M336 818v-6c0-1-2-2-2-2 0-1 1-2 2-2l3-3c4-2 6-6 10-9l1 1c1 0 2 0 2 1h1l-1 1v1h1c1 2 2 2 2 4 0 1 0 1-1 2-2-1-2-1-4-1h0-2v1 1 1 2h-1c-1 0-2-1-3 0s-1 1-1 2-1 4 0 5v1c0 3 0 4 1 7l1 1-9 1c-1-3 0-7 0-9z" class="G"></path><path d="M338 814h0c2-1 2 0 3 1v2c-2 0-2 0-3-1v-2z" class="f"></path><path d="M336 818c0 1 0 2 1 2h0 1c1 0 0 0 2-1h2l1-1c0 3 0 4 1 7l1 1-9 1c-1-3 0-7 0-9z" class="L"></path><path d="M343 818c0 3 0 4 1 7l-1 1c-1-1-3-1-4-2 0-1 0-2-1-3h0c1-1 3-1 4-2l1-1zm324 115c3 0 22 0 24 1 1 0 2-1 4 0h2 3 1c1 0 4 1 5 0 1 0 5-1 7 0h6c1 1 3 0 4 0 2 1 4 0 6 0v1h-6l1 1c1-1 6-1 7 0-1 1-3 0-5 1h-1-5-1v1h1l-1 1h0l-1-1h-1c-2 0-16 0-17 1-2 0-4 0-5-1h-5l1 1v1c-2 0-2 0-2-1h-3 0v-1c-2-1-6 0-8 0h-19 0c-2 0-6 0-7 1-2 0-4-1-5 0-2 0-5 0-6 1h-19c-1 0-5-1-6 0h-10c-1 0-3 1-4 0h-8v-1h6 4 1c2 1 5 0 7 0h21 7c4-1 7-1 11-1 1-1 4-1 5-1h31 0 3 0l-1-1c-2-1-5 0-7 0h-5c-1 0-1-1-2-1h-11c-2 0-4 1-5 0 2-1 5 0 8-2z" class="f"></path><path d="M353 800c2 0 3 1 4 2v2h1l1 1v1c0 1-1 3 0 4v4 1c0 1 0 1 1 2h-1v2c1 2 0 5 1 7h-12-3l-1-1c-1-3-1-4-1-7v-1c-1-1 0-4 0-5s0-1 1-2 2 0 3 0h1v-2-1-1-1h2 0c2 0 2 0 4 1 1-1 1-1 1-2 0-2-1-2-2-4z" class="I"></path><path d="M351 814c0 1-1 2-1 2 1 2 1 3 1 4l1 1c1 0 1 1 2 2v2c1 1 2 0 4 0 1-1 1-1 1-3l-2-2v-1h2c1 2 0 5 1 7h-12v-1c0-1 0-2 1-3h2v-1c-1 0-2 0-2-1v-5l2-1z" class="E"></path><path d="M343 812h10l1 1-1 2 1 1h-1-1v-2h-1l-2 1v5c0 1 1 1 2 1v1h-2c-1 1-1 2-1 3v1h-3l-1-1c-1-3-1-4-1-7v-1c-1-1 0-4 0-5z" class="X"></path><path d="M599 935c1-1 2-1 3-1 1-1 4 0 6 0 2-1 6-1 8-1 1-1 6 0 9 0 1-1 6-1 8-1 1 2 17 1 20 1 1 1 3 0 4 0h10c-3 2-6 1-8 2 1 1 3 0 5 0h11c1 0 1 1 2 1h5c2 0 5-1 7 0l1 1h0-3 0-31c-1 0-4 0-5 1-4 0-7 0-11 1h-7-21c-2 0-5 1-7 0h-1-4l1-2h13v-1h2v-1h-2l-1 1c-3 0-12 0-14-1z" class="c"></path><path d="M267 915h1c0-1 0-1 1-2h2l-1 2c1 1 2 1 4 1 5 1 10 2 15 2h0l11 1c2 1 4 1 6 1l6 1 23 1-2-2 1-1c0-1 1-1 2-1l5-2 1 1-1 2v1c1 0 2 0 3 1v1h-1v1c-4 0-9 0-13-1-1 0-5 0-6 1h-1c-2 1-8 0-11 0s-6 0-9 1h-42 0c-4-1-8-1-12-1h6c1-1 1-1 2-1l1 1h6c3-1 6 0 9-1h-1 0l3-1h2 1c0 1 1 0 1 0h6l-4-1h-7l-1-1h0l-1-1c-1-1-2-1-3-1l-23 1v-1l21-2z" class="Q"></path><path d="M306 920l6 1h0v1c-2 0-5 0-6-1v-1z" class="J"></path><path d="M341 916l1 1-1 2v1c1 0 2 0 3 1v1h-1-8l-2-2 1-1c0-1 1-1 2-1l5-2z" class="M"></path><path d="M249 923h6c1-1 1-1 2-1l1 1h6c3-1 6 0 9-1h-1 0l3-1h2 1c0 1 1 0 1 0h1c5 1 11 0 16 0l3 1h4v1l-42 1c-4-1-8-1-12-1z" class="L"></path><path d="M318 860h0c1-1 3-1 4-1 3 0 5 0 7-1l1 1s0 1 1 1h3 11v1c2 0 7-1 8 1-2 1-8 0-10 0v1c-2-1-2 0-3-1-1 1-1 2-1 2l-1 1v-1h-2l-2 1c0 1 1 1 0 2s-1 1-1 2l-2 1-1-2c-1 0-3-1-5-2h0c-2 0-3 0-5-1-1 1-2 1-3 3l2-1v1h0c-1 2-1 4-1 6v1l-6 3c-1 1-1 2-2 3v1 4c-1-1-1 0-1-1 0-2 0-3 1-5 0-2 0-3 2-3 1-1 1 0 1-1v-1l-1 1h-1v-1c-1-2-3-3-5-5h-2c-1-1-4-2-4-3h2c0-1 0 0 1-1-2 0-2 0-3-1v-1c1-1 2-1 3-1s2 0 3-1h2v1h-1v1c2 0 6 0 8-1v-4l3 1z" class="Y"></path><path d="M302 867h2c1 0 2-1 3-1 1 1 1 1 1 2l-1 1c-1 1 0 0 0 1h-1-2c-1-1-4-2-4-3h2z" class="K"></path><path d="M320 865v-1c-2 0-1 1-3-1 0 0 0-1-1-1 1-1 2 0 4-1 1 1 3 0 3 1s1 1 1 1v1c1-2 0-2 1-3l1 1c0 1-1 2-1 4h0c-2 0-3 0-5-1z" class="Z"></path><path d="M380 940c4 2 13 1 18 1h5c2 0 4-1 7-1h5c4-1 10-1 14-1l-1 1-19 1c-2 0-2 0-4 1h-4-10c-1 0-3 0-4 1h0 6l41 3-1 1-32 1-24-2h-1c-1-1-3-1-4-1h-14-5c1-2 10-1 12-1 4-1 8 0 11-1h3 5l1 1v-1h-1-4v-1h2-17-6c0-1 0-1-1-1h-53 0l69-1h6z" class="G"></path><path d="M587 701l4 2 4-1c2 3 5 6 5 9 1 3 0 6 0 9-1 1-1 2-1 3 1 1 1 2 1 3v3c-4 0-8 0-12-1h0-7c-1-1-2-1-3-1v-1-4c0-4-1-7-1-11v1h3l1-1h1c-1-1-1-2 0-3 1-3 3-5 5-7z" class="d"></path><path d="M595 702c2 3 5 6 5 9 1 3 0 6 0 9l-1 2h-2l-1-1v-5l1-1v-3c-1-1-1-2-1-3 2 3 2 5 2 8-1 1-1 3 0 4 1-3 1-7 1-9 0-3-5-7-8-9l4-1z" class="H"></path><path d="M582 708h1l1-1h1v1c-1 3-1 5-1 8 1 2 1 3 0 5h-2c-2 1-2 1-4 1 0-4-1-7-1-11v1h3l1-1h1c-1-1-1-2 0-3z" class="O"></path><path d="M588 722l-2-1v-1-7c-1-2-1-4 0-6 2-1 2-1 4 0 1 0 3 1 3 3l1 11c-1 0-1 0-2 1h0-2-2z" class="M"></path><path d="M578 722c2 0 2 0 4-1 1 0 3 1 4 1h2 2 2l7 1c1 1 1 2 1 3v3c-4 0-8 0-12-1h0-7c-1-1-2-1-3-1v-1-4z" class="C"></path><path d="M590 722h2l7 1c1 1 1 2 1 3l-1 1c-2 1-4 1-6 1-2-2-3-4-3-6z" class="j"></path><path d="M641 734c1 1 1 2 2 2 0 1-1 2-1 2 1 2 3 2 5 3v-1c0-2 1-5 0-8h1v10l2 10c2 1 4 1 5 2l2 17h-1-4v-1l-1-1v2h-2l-1-1h1 1c-1-1 0-1-1-1h0v-3h-2-1v2l1 1-1 1-1-1h-3-1l-1-2c-4-4-7-8-11-12l-4-2 2-3c0 1 1 1 2 1 0-2 0-3 1-5 2-1 1-3 2-5v-1l1 1v-2c2 0 4 0 5-1h1v-3l-1-1h1 2z" class="H"></path><path d="M651 761v-1l-1-1c1-2 1-2 1-4 1 0 1 0 2 1l1-1c1 2 0 4 1 6 0 3 0 7 1 10h-4v-1l-1-1v2h-2l-1-1h1 1c-1-1 0-1-1-1h0v-3h-2v-2h0l1-1 1 1c1-1 1-2 2-3z" class="G"></path><path d="M646 766c-1 0-2 0-3 1h-1l-1-1-4-4 3-4h-1c1-2 0-1 0-3v-1c2 0 3 0 4 1l-2 2c2 0 3-3 5-3l1 1 1-1 2 2-1 1h0v1l-1 1 3 2c-1 1-1 2-2 3l-1-1-1 1h0v2h-1z" class="X"></path><path d="M641 734c1 1 1 2 2 2 0 1-1 2-1 2 1 2 3 2 5 3v-1c0-2 1-5 0-8h1v10h-1l1 11h0-9c-1 0-2-1-3-1h0c-2 0-4 1-5 0l-2-1c0-2 0-3 1-5 2-1 1-3 2-5v-1l1 1v-2c2 0 4 0 5-1h1v-3l-1-1h1 2z" class="h"></path><path d="M647 742l1 11h0-9l1-3v-3h1l-1 2h1l1-1v-1l1-1v2c1 0 1 1 2 1l1 1c1-1 0-4 0-6 1-1 1 0 0-1l1-1z" class="N"></path><path d="M633 741c0 2 0 4 1 6l1 1 1-1v-5-2h3c0 3-1 7 1 10l-1 3c-1 0-2-1-3-1h0c-2 0-4 1-5 0l-2-1c0-2 0-3 1-5 2-1 1-3 2-5v-1l1 1z" class="C"></path><path d="M628 725h2l1-1h1c1 0 2-1 2-2l1 1 1 1c2-1 3-1 5-1v2 9h-2-1l1 1v3h-1c-1 1-3 1-5 1v2l-1-1v1c-1 2 0 4-2 5-1 2-1 3-1 5-1 0-2 0-2-1l-2 3-1-1h-3-1-3-3-1l1-1c-2 0-3 0-4-1v-2l-2 1c0-2 0-2 1-2l1-1c-1-1-1-2-2-2l-1-1-1 1-1-2c-1 0-2 0-2-1h-1-2v-4c2 1 4 0 6 1h1l1-8c1 3 1 6 1 8 3-2 9-5 10-8l1 1h1l1-1h0v-1c1 0 1-1 1-1 2-3 3-3 5-3z" class="Z"></path><path d="M617 747c1 1 3 1 5 2h3l2 1-2 3-1-1h-3-1-3-3-1l1-1c1 0 1-1 2-1l1-3z" class="K"></path><path d="M617 752c0-2 0-2 1-3h2l1 1-1 2h-3z" class="P"></path><path d="M632 740v-3h-2v-2h-2c1-2 1-2 1-4v-1c0-2 0-2 1-4h2c1-1 1-1 2-1v1h1l1 2c-1 1-3 1-5 2 1 1 1 1 1 2l-1 1h1 0 2c-1 2-1 3-1 5v1 2l-1-1z" class="D"></path><path d="M635 726c1 0 3-1 4-1v5c0 2 0 2 1 3 1-2 1-5 1-8v9h-2-1l1 1v3h-1c-1 1-3 1-5 1v-1c0-2 0-3 1-5h-2 0-1l1-1c0-1 0-1-1-2 2-1 4-1 5-2l-1-2z" class="E"></path><path d="M638 738h0v-1-1c-1 1-2 1-2 1l-1-1c0-1 1-2 1-3v-1l3-1v3h-1l1 1v3h-1z" class="G"></path><path d="M617 733h1 1c1-1 2-1 3-1 2 2 2 4 3 5v1c0 1 0 2 1 3v1h0c1 2 1 3 1 5l-1 1c-2 0-2-1-3-1-1 1-1 1-2 1v-1c-1-1-2-1-3-2-1-3-2-4-3-6l3-4c0-1 0-1-1-2z" class="e"></path><path d="M617 733h1 1c1-1 2-1 3-1l-3 3 2 1 1-1c-1 2-2 3-3 4 0 1 1 2 1 2l1 1c1 0 0 0 1 1 0 1 0 3 1 4-1 1-1 1-2 1v-1c-1-1-2-1-3-2-1-3-2-4-3-6l3-4c0-1 0-1-1-2z" class="D"></path><path d="M619 730l1 1h1l1-1h0v-1c1 0 1-1 1-1 2-3 3-3 5-3l-2 2c0 1 1 2 0 3-1 0-1 0-2 1l3 3v1l-1 1v1h2v3 3 2h-1c0-1 0-2-1-3h0v-1c-1-1-1-2-1-3v-1c-1-1-1-3-3-5-1 0-2 0-3 1h-1-1c-1 2-2 3-3 3 0 1 0 2 1 3h-1c-1 1-1 2-1 4h1c1 1 2 3 3 4h0l-1 3c-1 0-1 1-2 1-2 0-3 0-4-1v-2l-2 1c0-2 0-2 1-2l1-1c-1-1-1-2-2-2l-1-1-1 1-1-2c-1 0-2 0-2-1h-1-2v-4c2 1 4 0 6 1h1l1-8c1 3 1 6 1 8 3-2 9-5 10-8z" class="U"></path><path d="M607 740h3l1 1-1 2-2 1-1-1-1 1-1-2c-1 0-2 0-2-1h-1l5-1z" class="H"></path><path d="M600 737c2 1 4 0 6 1h1v2l-5 1h-2v-4z" class="L"></path><path d="M331 870l2-1c0-1 0-1 1-2s0-1 0-2l2-1h2v1l1-1s0-1 1-2c1 1 1 0 3 1v-1c1 2 2 3 2 4h0c1 1 2 4 1 5h0v1h-1l2 1c0 2-1 3-3 4-2 0-4 1-5 3-2 0-2 0-4-1h0c-1 1-1 2-2 2l-1 1v1 1h0c-1 0-1 1-1 1h0c-2 2-4 3-6 5l-3 3-1 1-2 1-8 3c0-1 2-2 2-3v-1h-2l-1-1-1-1c-1 1-2 2-3 2l-4 2c0-1 0-3 1-4 0-1 1-2 1-3v-1c2 0 2 0 4-1l1 1h0 2v-1c-1-2-1-4-1-6 1-1 1-2 2-3l6-3v-1c0-2 0-4 1-6h0v-1l-2 1c1-2 2-2 3-3 2 1 3 1 5 1h0c2 1 4 2 5 2l1 2z" class="g"></path><path d="M324 880c4-2 7-2 11-1-1 1-1 2-2 2l-1 1c-2 0-4 1-5 2s-1 2-2 3-3 2-4 3l-1-1c-1 0-2 0-3 1v-2c1-3 5-5 7-8h0z" class="J"></path><path d="M321 868h4c1 2 1 3 1 4l1 2-1 1-1 1c-2 1-3 2-4 5 0 1-1 1-2 2s0 0 0 1c-2 2-5 3-6 6l-1 1v1h0l-2 1-1-1c-1 1-2 2-3 2l-4 2c0-1 0-3 1-4 0-1 1-2 1-3v-1c2 0 2 0 4-1l1 1h0 2v-1c-1-2-1-4-1-6 1-1 1-2 2-3l6-3v-1c0-2 0-4 1-6h0 2z" class="D"></path><path d="M321 868h4c1 2 1 3 1 4l1 2-1 1-1 1c-1 0-1-1-2 0h0-1c0-1 0-1 1-1 1-1 1-1 1-2 0-2-1-3-3-5z" class="h"></path><path d="M331 870l2-1c0-1 0-1 1-2s0-1 0-2l2-1h2v1l1-1s0-1 1-2c1 1 1 0 3 1v-1c1 2 2 3 2 4h0c1 1 2 4 1 5h0v1h-1l2 1c0 2-1 3-3 4-2 0-4 1-5 3-2 0-2 0-4-1h0c-4-1-7-1-11 1 1-1 3-2 4-3h1c0-3 0-6-1-8h-1c-2-1-2-1-2-3h0c2 1 4 2 5 2l1 2z" class="K"></path><path d="M325 866h0c2 1 4 2 5 2l1 2v3h1v-2l1 1v2h-1l-1 1c0 1-1 2-3 2h1c0-3 0-6-1-8h-1c-2-1-2-1-2-3z" class="k"></path><path d="M345 866c1 1 2 4 1 5h0v1h-1 0c-3 0-6 0-8 1l-1-4 1-1 1-1h3l1 1c1 0 1-1 3-1v-1z" class="d"></path><path d="M337 873l-1-4 1-1c1 0 2 2 3 2 2 0 4-1 5 2-3 0-6 0-8 1z" class="R"></path><path d="M337 873c2-1 5-1 8-1h0l2 1c0 2-1 3-3 4-2 0-4 1-5 3-2 0-2 0-4-1 1-2 0-4 2-6z" class="E"></path><path d="M602 940c1 1 3 0 4 0h10c1-1 5 0 6 0h19c1-1 4-1 6-1 1-1 3 0 5 0 1-1 5-1 7-1h0 19c2 0 6-1 8 0v1h0-13c-3 0-5 0-8 1h-1-4c2 0 4-1 5 0 2 1 5 0 7 1h19c2 0 6 0 7-1h11c1 0 4-1 5 0-2 2-3 1-5 1h-2c-1 1-1 1-2 1-2 0-6 0-8 1h-6-2-17v1h-12 0c3 1 6 1 8 0 1 0 2 0 2 1 2 0 4 0 5-1h3v1c2 0 3 0 4-1l2 2 1-1h1 3c-3 1-7 1-11 1h-23c-2 2-7 1-10 0h0-5-13c-3-1-6 0-9 0h-14c-4 0-7 0-10-1-1 0-2 0-3-1-4 0-6 1-10 1-1 0-2-1-3-1h-17c-1 0-3 1-4 1l-1-1c-4-1-8 0-12 0-1-1-3 0-5-1h-4-10l-1-1h-7-10l-1-1h16c2 0 3-1 4 0 1 0 5 1 6 0 3 1 6 1 9 1h2c2 1 3 0 4 1h3c2 0 5 0 7-1h4 0 13 3 10 5c1-1 5-1 7-1h1 1l1-1z" class="L"></path><path d="M556 944l1-1h31 2c2-1 4 0 6 0 1 0 1-1 2-1l38 1h12l9 1c1 0 1 0 3 1 5 1 13-1 18 1h-23c-2 2-7 1-10 0h0-5-13c-3-1-6 0-9 0h-14c-4 0-7 0-10-1-1 0-2 0-3-1-4 0-6 1-10 1-1 0-2-1-3-1h-17c-1 0-3 1-4 1l-1-1z" class="J"></path><path d="M607 943c7 0 14 0 20 1 5 1 10-1 14 1h-19v-1c-3 0-13 1-15-1z" class="c"></path><path d="M591 944c2-1 6 0 8 0 3 0 5-1 8-1 2 2 12 1 15 1v1h-28c-1 0-2 0-3-1z" class="p"></path><path d="M315 845v-1l-2-1c-1-3 8-9 10-11 1 0 3-1 3-2 1-1-1-4 1-6 1 2 0 5 1 7h2 3l2-1c1 1 2 2 4 1h1l1 1c0-1 0-1 1-2l1 1 1 1h2c-1 1-2 2-2 3-2 0-3 0-4-1-1 2-1 2-1 3v2 1h-1v-1c0 2-1 4-1 6 3-1 1-2 3-4 0 0 1 1 2 1v4c0 2-1 6 0 7h0l3 1v1c-1 0-1 0-2 1l1 1 1-1 1-1v1l1 1h1 2 0c0 1 0 1 1 2h0c1 0 2 0 2 1h-8-11-3c-1 0-1-1-1-1l-1-1c-2 1-4 1-7 1-1 0-3 0-4 1h0l-3-1 1-1c0-2-1-6-1-8v-5z" class="k"></path><path d="M315 845h1c1 1 1 1 3 0h2c1 0 1 0 2 1l-2 2c-2 0-2-1-4 0l-1 1v5 3h0v1c0-2-1-6-1-8v-5z" class="a"></path><path d="M317 848h4l-1 1c-1 0-1 0-2 1h1v4c0 3 0 4-1 6l-3-1 1-1v-1h0v-3-5l1-1z" class="N"></path><path d="M319 854v-4h-1c1-1 1-1 2-1l2 1h0 2l1 1 1 2c-1 1-1 0-1 2h2 1l2 2v2l-1-1c-2 1-4 1-7 1-1 0-3 0-4 1h0c1-2 1-3 1-6z" class="V"></path><path d="M319 854l1 3v1l2-3v-2l1-1c1 2-1 4 2 4h0l2 2v-3h1l2 2v2l-1-1c-2 1-4 1-7 1-1 0-3 0-4 1h0c1-2 1-3 1-6z" class="b"></path><path d="M321 840c4-1 12 1 15-1 0-1 0-1 1-2l1 2c0 2-1 4-1 6-1 1-1 2-2 2h-1l-2 1c-1 0-2 0-2-1-2 0-4 1-6 0v-2c-1-1-1-1-1-2l1-1 1 1s0-1 1-2l-5-1z" class="Z"></path><path d="M324 845l11-1h1l-1 3h-1l-2 1c-1 0-2 0-2-1-2 0-4 1-6 0v-2z" class="H"></path><path d="M333 831l2-1c1 1 2 2 4 1h1l1 1c0-1 0-1 1-2l1 1 1 1h2c-1 1-2 2-2 3-2 0-3 0-4-1-1 2-1 2-1 3v2 1h-1v-1l-1-2c-1 1-1 1-1 2-3 2-11 0-15 1l-7 1c2-2 5-4 8-6l1-1 2 2v-2c1 0 2-1 4-1l1-1v-1h3z" class="Q"></path><path d="M330 831h3c0 2 1 2 1 4h-1v-1h-1c-1-1-2-1-2-2v-1z" class="T"></path><path d="M329 833l1-1c0 1 1 1 2 2v3h0v1c-2 0-3-1-4-2 0-1 0-2 1-3z" class="c"></path><path d="M337 845c3-1 1-2 3-4 0 0 1 1 2 1v4c0 2-1 6 0 7h0l3 1v1c-1 0-1 0-2 1l1 1 1-1 1-1v1l1 1h1 2 0c0 1 0 1 1 2h0c1 0 2 0 2 1h-8-11-3c-1 0-1-1-1-1v-2l-2-2h-1-2c0-2 0-1 1-2l-1-2h4c1-1 0-3 1-4 0 1 1 1 2 1l2-1h1c1 0 1-1 2-2z" class="C"></path><path d="M330 847c0 1 1 1 2 1l2-1c0 2 0 3-1 5 0 1-1 1-1 2-1 1-2 2-2 3l-2-2h-1-2c0-2 0-1 1-2l-1-2h4c1-1 0-3 1-4z" class="a"></path><path d="M309 885c0 1 0 0 1 1v-4-1c0 2 0 4 1 6v1h-2 0l-1-1c-2 1-2 1-4 1v1c0 1-1 2-1 3-1 1-1 3-1 4l4-2c1 0 2-1 3-2l1 1 1 1h2v1c0 1-2 2-2 3l8-3 2-1c0 1 0 2 1 3h5l1 1c-1 2-3 3-4 4v1c0 1-1 2-2 3v1l-1 1 1 1h0l-6 2c-2 1-3 1-5 1h-6-4c-2 0-2-1-3-2h0l-1 2c-2 1-3 2-4 4s-2 2-4 2h0c-5 0-10-1-15-2-2 0-3 0-4-1l1-2c3 0 6-1 8-2 3-1 5-3 6-4-2-4-6-1-9-2l3-1c3 0 6-1 10-1 1-2 0-4 1-6h0c2-5 6-6 10-9 2-2 7-1 9-3z" class="W"></path><path d="M299 890h2v1 1 1h0c-2 1-2 2-3 3l1 1h0l-1 2c-2 0-3 0-4 1h-1v2h-3 1v-6c2-3 5-5 8-6z" class="T"></path><path d="M285 907l2-2-1-1h3l-1 1v1c-1 0-1 1-2 2 0 1 0 0-1 1v1c0 1 0 1 2 3 1-1 2-3 4-4 1-1 2-2 3-4l1 1c0 1-1 1-1 2v1h0c1 0 3 0 4 1l-1 2c-2 1-3 2-4 4s-2 2-4 2h0c-5 0-10-1-15-2-2 0-3 0-4-1l1-2c3 0 6-1 8-2 3-1 5-3 6-4z" class="H"></path><path d="M294 909c1 0 3 0 4 1l-1 2c-2 1-3 2-4 4s-2 2-4 2h0c-1-1-1-2-3-2v-1l1-1v1c2 0 3 0 4-1 0-1 1-1 1-2h1l1-3z" class="K"></path><path d="M321 894c0 1 0 2 1 3h5l1 1c-1 2-3 3-4 4v1c0 1-1 2-2 3v1l-1 1 1 1h0l-6 2c-2 1-3 1-5 1h-6-4c-2 0-2-1-3-2h0c-1-1-3-1-4-1h0v-1c0-1 1-1 1-2l-1-1s1 0 2-1v1l2-2h-1v-2l3-1v1h2l1 1 2-1h0c-1 2-3 2-4 3-2 1-3 2-5 3v1l1-1h2c0-1 0-1 2-1h0 1c1 0 3 1 4 0s3-1 4-1v-1c1-1 2-1 3-1l-1-1s-1-1-1-2c-1-1 0-1 0-2l8-3 2-1z" class="k"></path><path d="M307 906h10-4c-1 1-1 1-1 2h-1c-2 0-3-1-4-2z" class="E"></path><path d="M312 908c0-1 0-1 1-2h4c1 1 3 1 4 2l1 1h0l-6 2 2-2h0-1l-1-1h-4z" class="Q"></path><path d="M298 910c3-2 6-3 9-4 1 1 2 2 4 2h1 4l1 1h1 0l-2 2c-2 1-3 1-5 1h-6-4c-2 0-2-1-3-2z" class="G"></path><path d="M305 912l1-1c2-1 4-1 6-1l-1 2h-6z" class="Q"></path><path d="M317 909h1 0l-2 2c-2 1-3 1-5 1l1-2c2 0 3 0 5-1z" class="C"></path><path d="M321 894c0 1 0 2 1 3h5l1 1c-1 2-3 3-4 4v1c-2-1-4-1-6 0s-3 1-5 0l-1-1s-1-1-1-2c-1-1 0-1 0-2l8-3 2-1z" class="E"></path><path d="M321 894c0 1 0 2 1 3h5c-2 2-4 2-6 3-1 0-1-1-2 0-1 0-1 1-2 2-2 0-3 1-4 1l-1-1h1c1-1 2-3 4-3 1 0 2 0 3-1v-1l-1-2 2-1z" class="I"></path><path d="M319 895l1 2v1c-1 1-2 1-3 1-2 0-3 2-4 3h-1s-1-1-1-2c-1-1 0-1 0-2l8-3z" class="L"></path><path d="M622 701l1-1h1c-1-1-1-1-1-2l-1-1v2h-1v-2h-1l3-6c0-2 1-2 2-3v-2c0-1 2-2 2-2h2v-2l3-1 2 2h0l1 1 8 12c1 2 2 4 2 6l-3 1c0 6 0 12 1 17v2l-2 1c-2 0-3 0-5 1l-1-1-1-1c0 1-1 2-2 2h-1l-1 1h-2c-2 0-3 0-5 3 0 0 0 1-1 1v1h0l-1 1h-1l-1-1-1-6c-1-1-2-1-2-2l1-9v-10l1-1c1 0 2 0 3-1h1z" class="J"></path><path d="M617 713c1 2 2 4 4 4h0c1-1 1-1 1-2l1 1h0c1-1 1-1 2 0h4v1h3 1 0l-1 1h-1c-3 0-6 0-8 1h-1l-1 1h0l-2-1v1 1c-1 1-1 1-1 3-1-1-2-1-2-2l1-9z" class="M"></path><path d="M622 701h2l-1 2h-1c0 1-1 2-1 3l1 1c0 1-1 2-1 3v1h1 4 0 1 2v1c-1 0-1 1-2 1s-1-1-2 0c2 1 3 1 4 1 2-2 1-2 2-3h1c1 1 1 1 2 1-1 0-1 0-1 1h-3l1 2c1-1 1 0 2-1v1h0c-2 0-2 1-4 1h0-4c-1-1-1-1-2 0h0l-1-1c0 1 0 1-1 2h0c-2 0-3-2-4-4v-10l1-1c1 0 2 0 3-1h1z" class="B"></path><defs><linearGradient id="C" x1="618.918" y1="724.26" x2="632.556" y2="723.85" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#C)" d="M641 706h0c1-1 1-2 1-3 0 6 0 12 1 17v2l-2 1c-2 0-3 0-5 1l-1-1-1-1c0 1-1 2-2 2h-1l-1 1h-2c-2 0-3 0-5 3 0 0 0 1-1 1v1h0l-1 1h-1l-1-1-1-6c0-2 0-2 1-3h16 7v-2-2-1c-1-3-1-7-1-10h0z"></path><path d="M622 701l1-1h1c-1-1-1-1-1-2l-1-1v2h-1v-2h-1l3-6c0-2 1-2 2-3v-2c0-1 2-2 2-2h2v-2l3-1 2 2h0l1 1 8 12c1 2 2 4 2 6l-3 1c0 1 0 2-1 3h0l-1-2h-1c0 1 0 3 1 4l-1 1-1-1v1h-1v-1l1-1c0-1 1-3 0-4-1 1-2 0-3 1v1c-1-1-1-2-2-2l-1 1-1-1h0c-1 1-2 0-3 2h-1l-1-1c-1 1-1 1-1 2h2 1l1 1h0c1 0 1 0 1-1l1 1h1v-1c0 1 0 1 1 2l1-1v-1h1v2l1 1c-1 0-5 1-6 1l-1-1h0c-1 1-2 1-4 1l1 1h-4-1v-1c0-1 1-2 1-3l-1-1c0-1 1-2 1-3h1l1-2h-2z" class="P"></path><path d="M635 685l10 17-10-1c-1 0-1 0-1-1h2l1-1h-4-1v-1h1v-3c1-1 1-2 2-3v-1l-1-1c0-1 0-2 1-3v-1-1z" class="T"></path><path d="M635 692h0c1 0 1 1 2 1 1 1 1 1 1 3-1 1-2 1-4 2h-1v-3c1-1 1-2 2-3z" class="o"></path><path d="M622 701l1-1h1c-1-1-1-1-1-2l-1-1v2h-1v-2h-1l3-6c0-2 1-2 2-3v-2c0-1 2-2 2-2h2v-2l3-1 2 2h0l1 1v1 1 1c-1 1-1 2-1 3l1 1v1c-1 1-1 2-2 3v3h-1v1h1 4l-1 1h-2c0 1 0 1 1 1h-9-2-2z" class="R"></path><path d="M635 686v1c-1 1-1 2-1 3l1 1v1c-1 1-1 2-2 3v3h-1v1h1 4l-1 1h-2c0 1 0 1 1 1h-9c1-2 3-1 4-1v-1h-1-2c1-2 0-1 0-2 0-2 1-5 1-7 0-1 1-1 1-2v-1c2 0 3 0 5-1h1z" class="N"></path><path d="M635 686v1c-1 1-1 2-1 3l1 1v1c-1 1-1 2-2 3l-1 2h-2l-1-1h0c1-1 2-3 2-4v-3h0l-2-1v-1c2 0 3 0 5-1h1z" class="c"></path><path d="M565 646c1 1 3 2 4 3l4-1c1 1 2 1 2 2l1 1h1v1c0 1 0 2 1 3v-1h3c1-1 3-3 5-4 2 1 4 1 6 2 1 1 1 2 1 3 1 0 2 0 3-1 1 1 2 1 3 1l1 2-6 8c1 0 3 0 4 1 1 2 1 4 0 6 0 1-2 3-3 4-1 0-2 0-3 1h-2l-1-1h0c-2 1-8 1-10 0h-1 0c-1 1-3 1-4 0-2 0-5 0-7-1v-1l-5-1h-3l1-1c0-1 0-3 1-4l-1-1h-1-3l1-1c1 0 2 0 3-1v-1h0l1-1 1-1-2-2v-1h2v-1c1-1 0-1 0-2h0v-1l-2-2h-2-1l2-2c-1 0-1 0-1-1l2-1c1 0 2-1 3-1l-3-1v-1c2 1 3 1 5 0z" class="D"></path><path d="M562 656l4-5c1 1 2 1 3 1-1 3-3 4-5 7l-2 3-2-2v-1h2v-1c1-1 0-1 0-2h0z" class="N"></path><path d="M561 663h1 0l2-2h0c1 1 2 0 3 0 1 1 1 1 1 3h-3c2 2 5 1 8 1v1c-3 0-5 0-8 1h-2c-2-1-2-1-3 0h-1-3l1-1c1 0 2 0 3-1v-1h0l1-1z" class="I"></path><path d="M565 646c1 1 3 2 4 3l4-1-4 4c-1 0-2 0-3-1l-4 5v-1l-2-2h-2-1l2-2c-1 0-1 0-1-1l2-1c1 0 2-1 3-1l-3-1v-1c2 1 3 1 5 0z" class="G"></path><path d="M559 651c2-1 3-1 6-1l1 1-4 5v-1l-2-2h-2-1l2-2z" class="a"></path><path d="M560 667c1-1 1-1 3 0l2 1h0 10c5 0 9-1 14-1 2 0 2 0 3 1l1 1c-2 1-4 0-5 1h-3v1c1 1 4 0 6 0h2l1 1-1 1h0-2l-1 1v1l-1 1h0c-2 1-8 1-10 0h-1 0c-1 1-3 1-4 0-2 0-5 0-7-1v-1l-5-1h-3l1-1c0-1 0-3 1-4l-1-1z" class="W"></path><path d="M560 667c1-1 1-1 3 0l2 1h0 1c1 1 2 1 4 1-2 1-3 1-5 2h-3l-1 1c1 0 1 1 1 1h-3l1-1c0-1 0-3 1-4l-1-1z" class="h"></path><path d="M591 671h2l1 1-1 1h0-2l-1 1v1l-1 1h0c-2 1-8 1-10 0h-1 0c-1 1-3 1-4 0-2 0-5 0-7-1v-1h1l1-1h-3v-1h2 5 13c2 0 3 0 5-1z" class="H"></path><path d="M591 671h2l1 1-1 1h0-2l-1 1v1c-3-1-8 0-11 0 2-1 4-1 7-3h0c2 0 3 0 5-1z" class="P"></path><path d="M586 650c2 1 4 1 6 2 1 1 1 2 1 3 1 0 2 0 3-1 1 1 2 1 3 1l1 2-6 8c1 0 3 0 4 1 1 2 1 4 0 6 0 1-2 3-3 4-1 0-2 0-3 1h-2l-1-1 1-1v-1l1-1h2 0l1-1-1-1h-2c-2 0-5 1-6 0v-1h3c1-1 3 0 5-1l-1-1c-1-1-1-1-3-1l-1-1h-7c-2 0-3 0-4-1 0-3-1-6 1-8l3-3c1-1 3-3 5-4z" class="R"></path><path d="M581 666v-3c1 0 1-1 1-1v-1-2l1-1h0c0-2 1-3 2-4h2c1 0 1-1 2-1l2 1v2c-1 1-2 2-2 3-1 2-1 5-1 7h-7z" class="W"></path><path d="M382 715h7v2c1 0 1-1 1-1l1-1 1 1-1 1h1v-1c1-1 1-1 2-1h3c1 0 1 0 2 1h3c1 0 0 0 2 1h0c0 1 0 1-1 2v-1c0 1-1 1 0 2v2c-1 0-1 0 0 1h0v2h1s1 1 1 2v1 2 5h0v5 1h3v1h-20c-7 0-15 0-22-1l-1 1-2-1c0 1 1 1 1 2v2h-1l-1-1v1h0c-1 0-1 0-2-1l-2-1v-1h2c1 0 1 0 1-1-2-2-3-1-4-1l-1-1-2 1v1l-1-1 1-1c2-1 3-1 5 0h3l4-1v-6h-2v-1l2-1v-2h-1l-1 1-1-1c0-2 2-2 3-3v-3-3c0-1 1-2 2-2s3-1 5-1h2l1-1 1 1h2v1h-2v1l1 1 1-1c0 1 0 1 1 1 0 0 1 0 2-1h-2l1-1h1 0v-2h0z" class="I"></path><path d="M399 729v1l1 1v1 7c-1-1-1 0-2-1 0 1 0 1-1 2h-1v-1l-1-1v-2-3c0-1 1-2 0-4v-4c1-2 2-1 3-2v2c-1 2-1 1-1 4h2z" class="S"></path><path d="M382 715h7v2c1 0 1-1 1-1l1-1 1 1-1 1h1v-1c1-1 1-1 2-1h3c1 0 1 0 2 1h3c1 0 0 0 2 1h0c0 1 0 1-1 2v-1c0 1-1 1 0 2v2c-1 0-1 0 0 1h0v2h1s1 1 1 2v1 2 5l-2-2-1 1 2 2h0c1 2 0 2 0 4-1 0-2 1-3 1v-10c0-1-1-2-2-2h-2c0-3 0-2 1-4v-2-1l-1-1v2l-1-1v-1l-1 1c-2-1-3 0-4-1h-1c-1 0-2 0-3 1v-2c-1 1-3 1-4 1h-1c-1 1-1 1-2 1v-1c-1-1-6 0-8 0s-2 1-3 1h-3v-3c0-1 1-2 2-2s3-1 5-1h2l1-1 1 1h2v1h-2v1l1 1 1-1c0 1 0 1 1 1 0 0 1 0 2-1h-2l1-1h1 0v-2h0z" class="B"></path><path d="M535 943h4c2 1 4 0 5 1 4 0 8-1 12 0l1 1c1 0 3-1 4-1h17c1 0 2 1 3 1 4 0 6-1 10-1 1 1 2 1 3 1 3 1 6 1 10 1h14c3 0 6-1 9 0h13 5 0l-1 1h-12c2 0 2 0 4 1s4 0 6 1l-14 1c-4 0-9 0-13 1h-10l-15 1c-5 0-10 1-15 1-6 0-12-1-17-2h-9-1 0c1 1 10 0 10 1l-1 1h1c1 1 3 1 4 2l-15-1c-2-1-6 0-8-1-3-1-7 0-11 0l-19-1h0c-7 0-14 0-22-1l1-1h-12-8c-3 0-6 0-9-1h0 17c2 0 5 0 6-1h1 6c1-1 3 0 5 0 1-1 4-1 6-1h5 4 0l-1-1h-3-1v-1h16 0c3 0 6-1 8-1 3-1 4 0 7-1z" class="T"></path><path d="M488 950l1-1h1 3v-1c5 0 11 0 15 1h7c4 0 25 1 27 0l1-1h12c1 0 3 0 4 1h5 13 6c-1 1-3 2-5 1h-7-1 0-1c-2 1-3 0-5 0l-12-1c-2 0-2 1-3 1h-1l1 1h0-1 0c1 1 10 0 10 1l-1 1h1c1 1 3 1 4 2l-15-1c-2-1-6 0-8-1-3-1-7 0-11 0l-19-1h0c-7 0-14 0-22-1l1-1z" class="X"></path><path d="M627 946h13 5 0l-1 1h-12c2 0 2 0 4 1s4 0 6 1l-14 1c-4 0-9 0-13 1h-10l-15 1c-5 0-10 1-15 1-6 0-12-1-17-2h-9 0l-1-1h1c1 0 1-1 3-1l12 1c2 0 3 1 5 0h1 0 1 7c2 1 4 0 5-1h-6-13-5c-1-1-3-1-4-1 2-1 6 0 9 0h9c1-1 2-1 3-1s2 1 3 1h5l1-1c3-1 26 0 30 0 3 1 10 1 12-1z" class="p"></path><path d="M555 948c2-1 6 0 9 0h9c1-1 2-1 3-1s2 1 3 1h5 1c2 1 5 0 7 0h15v1c-6 0-14-1-20 0h-4-6-13-5c-1-1-3-1-4-1z" class="L"></path><path d="M502 112c-7 1-15 0-22 0h-47-159-32c-3 0-9 0-12 1-2 1-5 3-7 5-7 4-13 9-18 15-12 11-21 24-27 39-4 9-7 18-9 28-9 37-10 75-10 113l28 1c2-16 10-33 18-47 16-26 36-43 66-50 22-5 51-3 71 9 17 10 27 29 31 48 1 6 2 13 2 19l1 66v125 144 73c0 1 0 1-1 2v-2-62-237-72c-1-16 0-33-2-49-1-12-6-24-12-34-10-16-23-24-41-28-26-6-55-4-78 10-30 20-46 52-54 86h-30v-1c-1-35 1-70 8-104 2-13 5-27 11-39 6-14 14-26 25-37 5-6 22-21 29-22 2-1 4-1 6-1h12 50 133 78c-3 1-5 1-8 1h0z" class="V"></path><path d="M367 822h3c0 2 0 5 1 7v-2c1-5 0-5 5-5 1 3 0 7 0 10h-1c-2 1-3 0-4 0l-1 1 1 1-1 1c1 0 1 0 2 1h1-3c-1 0-2-2-3-2 0 1 1 1 1 2 0 3 1 7 0 9 0 1 0 2 1 3l-1 2 1 1-1 1 1 1c-1 1-2 1-2 2h0c1 0 2 1 2 1 1 0 1-2 2-2v1 3c0 2-1 3-1 4-1 2-1 4-1 6h-2v1l1 1v1h-1c-1 2 0 0 0 2h-1 3 1l-1 1c-2 1-4 1-6 3l-1 1-1 1h-1c-2 0-4-1-6-2l-2-1h0c-3 0-5 0-8 1 2-1 3-2 3-4l-2-1h1v-1h0c1-1 0-4-1-5h0c0-1-1-2-2-4 2 0 8 1 10 0-1-2-6-1-8-1v-1h8c0-1-1-1-2-1h0c-1-1-1-1-1-2h0-2-1l-1-1v-1l-1 1-1 1-1-1c1-1 1-1 2-1v-1l-3-1h0c-1-1 0-5 0-7v-4c-1 0-2-1-2-1-2 2 0 3-3 4 0-2 1-4 1-6v1h1v-1-2c0-1 0-1 1-3 1 1 2 1 4 1 0-1 1-2 2-3h-2l-1-1-1-1c-1 1-1 1-1 2l-1-1h-1c-2 1-3 0-4-1l3-1h4v-1c-2 0-3 0-5 1h0-1v-2l9-1h3 12c2 0 5 0 7-1v-3z" class="g"></path><path d="M355 831h4c1 1 1 0 2 0l1 1 1-1 2 2v1c-1 0-3 1-4 1-1-1-1-1-3-1 0 0-1-1-2-1v-1l-1-1zm0 26c2 0 12-1 14 1h0v2h-3-13c0-1-1-1-2-1h0c-1-1-1-1-1-2h0 5z" class="I"></path><path d="M361 837l2 2h1c1 4 0 8 1 12 0 1 0 2-1 2v2l1 1h-2 0c-1-1 0-1-1-1l-1 1h-2c0-2-1-3 1-6-1-2-1-10-1-12l2-1z" class="E"></path><path d="M361 837l2 2v11h-3c-1-2-1-10-1-12l2-1z" class="K"></path><path d="M369 860v1h-1c-2 2-3 4-4 7-1 2-2 6-2 8l4-3h3 1l-1 1c-2 1-4 1-6 3l-1 1-1 1h-1c-2 0-4-1-6-2l-2-1h0c-3 0-5 0-8 1 2-1 3-2 3-4l-2-1h1v-1h0c1-1 0-4-1-5h0c0-1-1-2-2-4 2 0 8 1 10 0-1-2-6-1-8-1v-1h8 13 3z" class="a"></path><path d="M357 870h4c0 1 0 1-1 1l1 1v3h-1l-1 1 1 1-1 1c1 1 1 0 2 1h-1c-2 0-4-1-6-2l-2-1h3l-1-1v-1-2c1 0 2 0 3-1v-1z" class="Z"></path><path d="M357 870h4c0 1 0 1-1 1v1l1 1h-2c-1 0-3 1-4 1l-1 1v-1-2c1 0 2 0 3-1v-1z" class="d"></path><path d="M348 867v4h1c0-2 0-2 1-4l2 1 1-1c0 2 0 3-1 4 2 0 3-1 5-1v1c-1 1-2 1-3 1v2 1l1 1h-3 0c-3 0-5 0-8 1 2-1 3-2 3-4l-2-1h1c2-1 0-2 1-3 0-1 0-1 1-2z" class="g"></path><path d="M369 860v1h-1c-2 2-3 4-4 7-2-1-4-1-6 0h-3v-1h-2l-1 1-2-1c-1 2-1 2-1 4h-1v-4c-1 1-1 1-1 2-1 1 1 2-1 3v-1h0c1-1 0-4-1-5h0c0-1-1-2-2-4 2 0 8 1 10 0-1-2-6-1-8-1v-1h8 13 3z" class="H"></path><path d="M369 860v1h-1c-2 2-3 4-4 7-2-1-4-1-6 0v-4h0c0-1 0-1 1-2 0 1 0 1 1 2v-2h1 1 0 1c0 2 0 3 1 4l1-1c0-1 0-1-1-2h0c1-1 1-2 2-3h3z" class="B"></path><path d="M348 867v-3l1-1c0 1 1 1 1 1l2-1c1 1 0 1 1 1 1-1 1-1 1-2h2 1 0v1l1 1v4h-3v-1h-2l-1 1-2-1c-1 2-1 2-1 4h-1v-4z" class="M"></path><path d="M343 831h0v-2c1-1 1-2 2-2s0 0 1 1c1-1 2-1 3-1s2 0 4 1v-1c2-1 6 0 8 0l1 1h-2l-2-1v1h-1-2v1h1v1h-1v1l1 1v1c1 0 2 1 2 1 2 0 2 0 3 1 1 0 3-1 4-1v-1l-2-2 3 1h0v3 1h-4l-1 1-2 1c0 2 0 10 1 12-2 3-1 4-1 6-2 0-3 0-4 1h-5-2-1l-1-1v-1l-1 1-1 1-1-1c1-1 1-1 2-1v-1l-3-1h0c-1-1 0-5 0-7v-4c-1 0-2-1-2-1-2 2 0 3-3 4 0-2 1-4 1-6v1h1v-1-2c0-1 0-1 1-3 1 1 2 1 4 1 0-1 1-2 2-3h-2l-1-1z" class="E"></path><path d="M349 837c-1-1-1-1-3-1h-1c1-2 1-2 3-3 0 1 1 2 1 2h1c-1 1-1 1-1 2z" class="I"></path><path d="M342 842v-1-3c1-1 2-1 4-1 0 0 1 1 2 1v11c0 1-1 3-1 4h-3v-1l-2 1h0c-1-1 0-5 0-7v-4z" class="a"></path><path d="M363 831l3 1h0v3 1h-4l-1 1-2 1c0 2 0 10 1 12-2 3-1 4-1 6-2 0-3 0-4 1h-5-2-1l-1-1v-1l-1 1-1 1-1-1c1-1 1-1 2-1v-1l-3-1 2-1v1h3c0-1 1-3 1-4h0v-1c1-3 1-8 1-11 0-1 0-1 1-2 1 1 2 1 3 2v5l2 1v-5-2h0 1c2 0 4-1 5-1s3-1 4-1v-1l-2-2z" class="e"></path><path d="M353 842l2 1c1 0 0 0 1 1h1v1h-1c-2 1-1 6-1 8l-1-1c0-2-1-5-1-6v-3-1z" class="Q"></path><path d="M673 864c4-1 7-2 12 0l1-1c3 2 6 3 8 6 1 1 2 1 3 2 2 1 3 4 5 5h1c4-1 7-1 11 1 1 1 2 1 3 2 2 2 6 6 9 8h5v-1l4 2-8-1c1 2 2 3 3 5 0 1 1 3 2 4h2l1 1h-2c1 1 1 1 1 3 1 0 1 0 2 1l-1 1c2 1 2 1 4 1-2 1-3 1-4 1v1c1 0 3 0 4 1 2 2 7 1 8 3h-2 0-1c-2 0-3 0-5-1s-3 0-5-1c0 1-1 1-2 2-1 0-1 0-2-1v-2c-2-3-4-5-7-6-1 0-2-1-3 0-2 0-5-1-7 0l-4 2c-2-3-3-5-6-7h0c-1 0-4-2-5-2 0-1-2-2-3-2 2-1 3 0 5 0-1-1-2-1-2-2-2 0-3-1-4-1v-1h-1c-3-1-3-1-6-1-1 0-1 0-2-2-2-1-5-3-7-3-3-1-6 0-8 1l-2 2v1h0l-4-2-2-2h0l1-1c0-2 0-4-1-5 0-1 0-2 1-3h0c1-4 3-5 7-7l3-1z" class="V"></path><path d="M718 893c2-2 2-2 5-2 3 1 6 4 7 6 1 1 2 2 2 3v1h-1l-1 1c-3-2-7-5-10-6l-1-1h-1-1l1-2z" class="L"></path><path d="M703 895h0c4 0 7 1 11 0 1 1 2 1 3 0h1 1l1 1c3 1 7 4 10 6l1-1h1v-1c1 3-1 4-2 6-2-3-4-5-7-6-1 0-2-1-3 0-2 0-5-1-7 0l-4 2c-2-3-3-5-6-7h0z" class="I"></path><path d="M698 881c3 1 5 2 8 4h0l1 3c1 1 2 1 3 0 2 0 4 0 6 2 1 0 1 1 2 3l-1 2c-1 1-2 1-3 0-4 1-7 0-11 0h0c-1 0-4-2-5-2 0-1-2-2-3-2 2-1 3 0 5 0-1-1-2-1-2-2-2 0-3-1-4-1v-1h-1c-3-1-3-1-6-1-1 0-1 0-2-2l2-1h1l2-1c3-1 6 0 8-1z" class="f"></path><path d="M688 883l3 2h2c1 1 2 1 3 2s1 1 2 1c4 1 6 3 9 4 2 0 3 1 4 2l3 1c-4 1-7 0-11 0h0c-1 0-4-2-5-2 0-1-2-2-3-2 2-1 3 0 5 0-1-1-2-1-2-2-2 0-3-1-4-1v-1h-1c-3-1-3-1-6-1-1 0-1 0-2-2l2-1h1z" class="E"></path><path d="M686 863c3 2 6 3 8 6 1 1 2 1 3 2 2 1 3 4 5 5h1c4-1 7-1 11 1 1 1 2 1 3 2h-1l8 8h0l4 5-1-1h-3c-2-1-3-1-5-1-1 0-1 0-2-1-2-1-3-2-5-2h-4c-1-1-1-2-2-2h0c-3-2-5-3-8-4h-3v-1h1v-2c0-2 0-3-1-5-2-4-6-7-10-9l1-1z" class="b"></path><path d="M724 887h-1c-1 0-2 0-4-1-3-2-7-2-11-5 0-1-1-1-2-1l-2-2 2-1c2-1 2-1 5-1 2 1 4 2 5 3l8 8z" class="f"></path><path d="M673 864c4-1 7-2 12 0 4 2 8 5 10 9 1 2 1 3 1 5v2h-1v1h3c-2 1-5 0-8 1l-2 1h-1l-2 1c-2-1-5-3-7-3-3-1-6 0-8 1l-2 2v1h0l-4-2-2-2h0l1-1c0-2 0-4-1-5 0-1 0-2 1-3h0c1-4 3-5 7-7l3-1z" class="C"></path><path d="M665 871c1-1 2 0 3 0l1 1 3 3-1 1c2 1 3 0 5 2 2 0 2-1 3 1-1 0-1 0-2 1h-4l-2-2v-1h-3 0c-1-1-2-1-3-2h0c-1-2 0-3 0-4z" class="e"></path><path d="M665 871c0 1-1 2 0 4h0c1 1 2 1 3 2h0 3v1l2 2h4c3 1 6 3 9 2l1 1-2 1c-2-1-5-3-7-3-3-1-6 0-8 1l-2 2v1h0l-4-2-2-2h0l1-1c0-2 0-4-1-5 0-1 0-2 1-3h0l2-1z" class="V"></path><path d="M664 883l2-2c2-1 2-1 3 0l1 1-2 2v1h0l-4-2z" class="Y"></path><path d="M670 865l3-1-1 2 1 1h0c1 0 2 1 3 1h1l3 1c2 1 4 2 6 4l6 4 3 1-2 1h-1c-2 0-3 0-4-1-2 1-2 1-3 1l-1-1h2v-1c-3 0-6-1-8-2s-2-1-4-1h0v-1l1-1c-1-1-1-1-3-1l-3-3 2-1-1-2z" class="Q"></path><path d="M681 874l5-1 6 4c-3 0-7-2-11-3z" class="c"></path><path d="M680 869c2 1 4 2 6 4l-5 1c-1 0-2 0-3-1l1-1v-1l-2-1h2l1-1z" class="G"></path><path d="M673 864c4-1 7-2 12 0 4 2 8 5 10 9 1 2 1 3 1 5h-1l-3-1-6-4c-2-2-4-3-6-4l-3-1h-1c-1 0-2-1-3-1h0l-1-1 1-2zm-239 82c10-1 21-2 31 0 2 1 5 0 7 0 1 1 3 1 4 0h6c2 0 6-1 8 0l-1 1v1h-6-1c-1 1-4 1-6 1h-17 0c3 1 6 1 9 1h8 12l-1 1c8 1 15 1 22 1h0l19 1c4 0 8-1 11 0 2 1 6 0 8 1l15 1h2l1 1h4 0-5v1c-3 1-12 1-15 0h-2v1h2 0v1h-14 0l2 1c-7 0-14 1-21 1-4 0-7-1-11-1h-21l-19-1h0 16c3 0 4-1 6-1h-3-1-1c-2-1-3 0-5 0-3-1-6 0-8 0-1 1-4 0-5 0h-1c-2 0-4 0-6-1v-1h-3c-2-1-8 0-10 0-1 0-1 0-2-1h-8c-3-1-4 0-6-1-7-1-14 0-20 0l-6-1h0 1-1c-1-1-2 0-3 0h-9c-1 0-2 0-4-1 1-1 2 0 4-1l1 1c1 0 3-1 4-1 3 0 10 1 12 0h-3v-1h4 0l1 1h1 4 1v1c2 0 2 0 3-1 0 0-1 0-1-1h-1c-3-1-7 0-10 0h-2l-6-1-1 1c-1-1-2-1-2-1-7 1-15 0-22 0h-16c-3 0-6 0-8-1h13 9l-2-2h7 1l24 2 32-1 1-1z" class="f"></path><path d="M434 946c10-1 21-2 31 0 2 1 5 0 7 0 1 1 3 1 4 0h6c2 0 6-1 8 0l-1 1v1h-6-1c-1 1-4 1-6 1h-17 0c3 1 6 1 9 1h8 12l-1 1c8 1 15 1 22 1h0c-4 0-10-1-14 1 9 0 19-1 28 1h7v1c-4 0-9 0-13-1h-19-9-16-6c-1-1-3 0-4 0-2-1-3-1-4-1l-2 1c-1 0-2-1-3-1-1-1-19-2-21-2-5-1-10-1-15 0 0 0-1 0-1-1h-1c-3-1-7 0-10 0h-2l-6-1-1 1c-1-1-2-1-2-1-7 1-15 0-22 0h-16c-3 0-6 0-8-1h13 9l-2-2h7 1l24 2 32-1 1-1z" class="J"></path><path d="M462 951h25c8 1 15 1 22 1h0c-4 0-10-1-14 1h-6c-7 0-14 1-21 0-2-1-7 0-8-2h2z" class="p"></path><path d="M434 946c10-1 21-2 31 0 2 1 5 0 7 0 1 1 3 1 4 0h6c2 0 6-1 8 0l-1 1v1h-6-1c-1 1-4 1-6 1h-17 0c3 1 6 1 9 1h8 12l-1 1h-25-5l-13-2c-2 0-3-1-5-1h-11-9c-2 1-3 1-5 1-4-1-9 0-13 0v-1l32-1 1-1z" class="N"></path><path d="M434 946c10-1 21-2 31 0 2 1 5 0 7 0 1 1 3 1 4 0h6c2 0 6-1 8 0l-1 1h-1-10c-4 1-8 1-12 0h-7c-3 0-6 0-8-1-6 0-12 1-18 1l1-1z" class="c"></path><path d="M687 893c3-2 7-1 10-1l1 1c1 0 4 2 5 2h0c3 2 4 4 6 7l4-2c2-1 5 0 7 0 1-1 2 0 3 0 3 1 5 3 7 6v2c1 1 1 1 2 1 1-1 2-1 2-2 2 1 3 0 5 1s3 1 5 1h1l3 3v-1c1 0 1 0 1 1h1 4v1 1h3v1c-4 1-10 1-14 2h-3c-4 2-10 1-15 1-2 2-6 1-9 2h-4c-6 0-11 0-17 2h-5l1-1c0-1 0-1-1-2h-1c-2 0-4-1-6-1h0c-1 0-2 0-3 1 0-3 1-5 1-7l-2-2h-1c-2-1-3-2-5-2 0-2 0-3-1-5-1-1-1-2-3-2l-2-1 3-1c1 0 0 0 1-1l-1-1v-2c1 0 1 0 2-1l2 2c1 0 1 0 2 1h2c1-2 1-2 3-2 2-1 4-2 5-2h1z" class="Q"></path><path d="M700 901c2 1 4 2 5 4h-1l-2 1h-1l-1-1 1-1v-1l-2-1h0l1-1z" class="G"></path><path d="M683 904c2-2 3-2 6-2h1v1c-1 1-3 1-4 2l-3-1z" class="J"></path><path d="M686 893h1c-1 1-1 1-2 4 1-1 1-2 2-2 2 0 2 0 4 1 1 0 2 0 3 1h1c2 1 3 2 5 3-5 0-9-1-12-4-1 1-1 1-2 1h-1c-2 1-4 1-6 1l-1-1c1-2 1-2 3-2 2-1 4-2 5-2z" class="c"></path><path d="M687 893c3-2 7-1 10-1l1 1c1 0 4 2 5 2h0c3 2 4 4 6 7l4-2c2-1 5 0 7 0 3 1 7 2 8 5 0 1 0 1 1 2-1 1-1 1-3 1-4-2-10-2-14-2-3 0-5 0-7-1-1-2-3-3-5-4v-1c-2-1-3-2-5-3h-1c-1-1-2-1-3-1-2-1-2-1-4-1-1 0-1 1-2 2 1-3 1-3 2-4z" class="f"></path><path d="M672 894l2 2c1 0 1 0 2 1h2l1 1c1 1 2 2 3 2 0 1 0 3 1 4l3 1c3 2 8 1 11 1h1l2 2-3 1 2 1 2 2h-1l2 3v2h0l-3 1c-3 1-6 1-9 1h-1c-2 0-4-1-6-1h0c-1 0-2 0-3 1 0-3 1-5 1-7l-2-2h-1c-2-1-3-2-5-2 0-2 0-3-1-5-1-1-1-2-3-2l-2-1 3-1c1 0 0 0 1-1l-1-1v-2c1 0 1 0 2-1z" class="i"></path><path d="M681 909h0c1 0 2 0 3 1 1-1 3-1 4-1-2 1-3 2-4 4-2 1-2 1-2 4l1 1h0c-1 0-2 0-3 1 0-3 1-5 1-7l-2-2 2-1z" class="a"></path><path d="M667 900l3-1v1c1 0 2 0 2 1 1 1 1 2 2 3l1 1 1 1h1c1 1 1 1 1 2 0 0 2 1 3 1l-2 1h-1c-2-1-3-2-5-2 0-2 0-3-1-5-1-1-1-2-3-2l-2-1z" class="U"></path><path d="M672 894l2 2c1 0 1 0 2 1h2l1 1c1 1 2 2 3 2 0 1 0 3 1 4l3 1c3 2 8 1 11 1h1l2 2-3 1h-1v-1c-4-1-7 1-9 0l-1-1c-1 0-3 0-4-1-2-1-3-1-3-3v-1h-2l-1-1 2-1c-1-1-2-1-3-2l-1 1c-1-1-2-1-2-2s-1-1-2-2c1 0 1 0 2-1z" class="D"></path><path d="M689 909c4 0 7 0 10 2 0 1 0 1 1 1h0l2 3v2h0l-3 1c-3 1-6 1-9 1h-1c-2 0-4-1-6-1l-1-1c0-3 0-3 2-4 1-2 2-3 4-4h1z" class="X"></path><path d="M700 912h0l2 3-1 1h-1c-1-1-3-1-4-1-2 0-2-1-3-2h1 1c1-1 1-1 2-1 2 1 2 0 3 0z" class="f"></path><path d="M689 909c4 0 7 0 10 2 0 1 0 1 1 1-1 0-1 1-3 0-1 0-1 0-2 1h-1-1c-1-1-2-1-4-1l-1-1 1-2z" class="T"></path><path d="M683 918l-1-1c0-3 0-3 2-4 3 1 6 1 9 3 1 0 2 1 3 1h0c1-1 2-1 4-1h1l1-1v2h0l-3 1c-3 1-6 1-9 1h-1c-2 0-4-1-6-1z" class="h"></path><path d="M720 900c1-1 2 0 3 0 3 1 5 3 7 6v2c1 1 1 1 2 1 1-1 2-1 2-2 2 1 3 0 5 1s3 1 5 1h1l3 3v-1c1 0 1 0 1 1h1 4v1 1h3v1c-4 1-10 1-14 2h-3c-4 2-10 1-15 1-2 2-6 1-9 2h-4c-6 0-11 0-17 2h-5l1-1c0-1 0-1-1-2 3 0 6 0 9-1l3-1h0v-2l-2-3h1l-2-2-2-1 3-1h3 5c1 1 1 1 2 0 2-1 9 0 11 1h2c1-1 2-1 3-1 2 0 2 0 3-1-1-1-1-1-1-2-1-3-5-4-8-5z" class="H"></path><path d="M712 920l1-1 1-1h11c-2 2-6 1-9 2h-4z" class="l"></path><path d="M734 909c2 0 5 0 8 1 1 1 3 2 3 4l-1 1h-3c-4 0-8 1-11-2 1-2 2-3 4-4z" class="T"></path><path d="M729 907v3l-1 2v2c0 1 1 1 0 2h-6c-1 1-4 0-4 0l-10 1c-2-2-2-1-4-1l-2-2v1l-2-3h1l-2-2-2-1 3-1h3 5c1 1 1 1 2 0 2-1 9 0 11 1h2c1-1 2-1 3-1 2 0 2 0 3-1z" class="R"></path><path d="M700 908h3l4 2h-4-4l-2-1 3-1zm15 6v-1h2 8c1 0 2 0 3 1 0 1 1 1 0 2h-6c-1 0 0 0-1-1-1 0-2-1-3 0-1 0-2-1-3-1z" class="b"></path><path d="M728 914c0 1 1 1 0 2h-6c-1 0 0 0-1-1 1-1 2-1 3-1 1 1 2 1 3 1v1c1-1 1-1 1-2z" class="h"></path><path d="M701 912c1-1 1-1 2-1 3 1 5 1 7 2l2 1c1 0 2 1 3 0 1 0 2 1 3 1 1-1 2 0 3 0 1 1 0 1 1 1-1 1-4 0-4 0l-10 1c-2-2-2-1-4-1l-2-2v1l-2-3h1z" class="g"></path><path d="M729 907v3l-1 2c-2-1-6 0-9-1-2 0-3 0-5 1-2 0-3 0-5-1l-2-1-4-2h5c1 1 1 1 2 0 2-1 9 0 11 1h2c1-1 2-1 3-1 2 0 2 0 3-1z" class="I"></path><path d="M709 911l1-2c1 0 1 0 2 1h4c1 0 2 1 3 1-2 0-3 0-5 1-2 0-3 0-5-1z" class="b"></path><path d="M729 907v3c-1 0-1 1-2 2-1-2-2-2-4-2-1 0-1-1-2-1h2c1-1 2-1 3-1 2 0 2 0 3-1z" class="C"></path><path d="M513 125l-1-1v1c0 3 1 10-1 12-1-1 0-2 0-4-1-3-1-7-1-10 1-2 1-43 1-50 0-3-1-8 0-12h0 1c3 4 7 9 11 11l6-3c1-2 2-2 4-3 2 0 4 1 5 3 3 3 5 5 9 5s6-1 9-3h1c0 1-1 2-2 3-3 2-6 3-9 2-2 0-4-1-6-2l-2-1c-3-1-10 1-12 3l-2 2c-2 0-2 0-3-1-2-2-5-2-8-2-1 3 0 7 0 9v27l356 1v309H766c-1-13-3-27-6-40-9-42-27-80-61-107-28-23-64-37-100-41-10-1-22-1-33 0h-2v-1c6-1 13-1 19-1 33 1 68 11 97 28 40 24 67 64 78 109 5 17 8 35 9 52h101V112H513v13z" class="a"></path><path d="M529 69l4-3c2 2 4 3 6 5-5 1-9 2-13 3-1 0-1-1 0-1 1-2 2-3 3-4z" class="N"></path><path d="M526 74c-1 1-2 1-3 2v1c-3-1-6-3-8-5v-3h0v-2h0c2 2 4 4 7 5 1 1 2 1 4 1-1 0-1 1 0 1z" class="I"></path><path d="M344 922l10 1h0c0 1 0 1-1 1 2 0 4 0 6 1h-3c-2 1-7 1-10 1-2 1-3 0-4 1-8 0-16 1-24 0l-1 1h0c2 1 7 1 10 1v-1c3-1 8 1 12 0 5-1 10-1 15-1h5v1h1c-3 1-9 0-12 0-2 0-3 0-4 1-2 0-10-1-11 0 1 1 5 2 7 1 4-1 9 0 13 0 2 0 4-1 6 0h5 1 1 0c-1 0-2 0-3 1h-10c-1 0-3 0-4-1v1c-5 1-20-2-23 1 1 1 5 0 7 0h22c4 0 10 0 14-1h76 1v1h-6l-2 1h0c2 1 7 1 9 1h2l-1 1h0c-6-1-12 0-17 0h-12c2 1 3 1 5 1h0l-15 1h7v1c-1 0-4-1-5 0h-2c3 0 7 1 10 0 2 0 8-1 10 0h1c1 1 2 1 4 1h2 15l-6 1c-4 1-13 1-17 0l1-1c-4 0-10 0-14 1h-5c-3 0-5 1-7 1h-5c-5 0-14 1-18-1h-6c-2-1-7-1-9-1h-25c-4 0-8 1-12 0v-1c-1-1-2-1-4-1h-1-14l-32-1-14 1h-4s0-1-1-1c1-1 4 0 5 0l16-1h4 15c2 0 4 1 6 0s4 0 6 0l1-1h7v-1h-4c-4-1-10-1-14 0-3 1-6 0-9 0-5 0-23 1-26 0l1-1h11c1-1 3 0 5 0 2-1 4 0 6-1 1-1 3-1 5-1h1c1 0 2 0 3-1s3 0 5 0c-2-1-4 0-6 0-1 1-1 0-2 0-2 1-9 1-10 1h0c1-1 3-1 5-1h0-4v-1h3-8-2-3c-2-1-5-1-6-1h-16l1-1h0 9c2 0 4-1 5-1 3 1 5 0 7 0 3 1 6 1 9 0h2 3c1 0 4 1 5 0h-3 0-18c-3 0-6 1-9 0l-4-1h1 42c3-1 6-1 9-1s9 1 11 0h1c1-1 5-1 6-1 4 1 9 1 13 1v-1h1z" class="L"></path><path d="M344 922l10 1h0c0 1 0 1-1 1 2 0 4 0 6 1h-3c-5 0-10 0-14-1-3 0-7 0-10-1h-9 1c1-1 5-1 6-1 4 1 9 1 13 1v-1h1z" class="e"></path><path d="M365 938l26-2c-1-1-8-1-10-2 2-1 11 0 13 0 3 1 6 0 8 1h10 6 1c2 1 3 1 5 1h0l-15 1h7v1c-1 0-4-1-5 0h-2c3 0 7 1 10 0 2 0 8-1 10 0h1c1 1 2 1 4 1h2 15l-6 1c-4 1-13 1-17 0l1-1c-4 0-10 0-14 1h-5c-3 0-5 1-7 1h-5c-5 0-14 1-18-1h8c4-1 9-1 13-1h0l-4-1h-6c-2-1-3-1-5-1l-1 1h-2c-3 1-8 1-11 1h-34l-1-1h28z" class="X"></path><path d="M355 932c4 0 10 0 14-1h76 1v1h-6l-2 1h0c2 1 7 1 9 1h2l-1 1h0c-6-1-12 0-17 0h-12-1-6-10c-2-1-5 0-8-1-2 0-11-1-13 0 2 1 9 1 10 2l-26 2c2-1 3-1 5-1h1-1c-2-1-5-1-6-1-2 1-4 1-5 1-6 0-13-2-18 0-1 0-3 0-4-1 4-2 11 1 14-1-2-2-8-1-10-1-2 1-5 1-6 0h0c2 0 4 0 6-1 7-1 15 0 22 0l1-1h-8-1z" class="G"></path><path d="M438 933c2 1 7 1 9 1h2l-1 1h0c-6-1-12 0-17 0h-12-1-6l-3-1h-3c2 0 4 1 5 0 2-1 5 0 7 0h5c1 0 1-1 2-1 4 0 9 1 13 0z" class="T"></path><path d="M352 888c3 1 6 2 8 5h0l2 2 1 3h2 0c1-1 2-1 2-2 1 0 1-1 2-1l2-2c1-1 2-2 4-2v2c-1 2-1 5-1 7v4 1l-1 1c1 1 1 2 2 2v2c3 2 8 2 11 2h0c-1 2-2 2-4 3-2 0-5 1-7 1h-4l-1 1h-1c2 1 2 2 3 4h4 0c-2 1-3 1-4 1v1h0-18l-10-1v-1c-1-1-2-1-3-1v-1l1-2-1-1-5 2c-1 0-2 0-2 1l-1 1 2 2-23-1-6-1c-2 0-4 0-6-1l-11-1c2 0 3 0 4-2s2-3 4-4l1-2h0c1 1 1 2 3 2h4 6c2 0 3 0 5-1l6-2h0l-1-1 1-1v-1c1-1 2-2 2-3v-1c1-1 3-2 4-4l-1-1h-5c-1-1-1-2-1-3l1-1c0 1 0 0 1 1v1l1-1c1 0 1 0 2 1-1 0-1 0-2 1h1c2 0 3 0 5-1 1-1 2-2 4-2 1-1 1-1 2-1h1c3-1 7-3 10-3 1 0 1-1 2-1h3z" class="i"></path><path d="M320 917c1-1 3-2 5-1h0v2c-1 1-1 1-2 1-2-1-2-1-3-2z" class="F"></path><path d="M298 915c3-1 7-1 10-1 2 1 5 0 7 1-2 1-3 1-5 1h-1c-2 1-5 0-7 0l-4-1z" class="D"></path><path d="M322 909h1l1-1v2 3c-3 1-6 2-9 2-2-1-5 0-7-1-3 0-7 0-10 1h-1l-1 1-1 1-2-1c1-2 2-3 4-4l1-2h0c1 1 1 2 3 2h4 6c2 0 3 0 5-1l6-2z" class="E"></path><path d="M297 912c3 1 7 1 11 1v1c-3 0-7 0-10 1h-1l-1 1-1 1-2-1c1-2 2-3 4-4z" class="X"></path><path d="M325 918h1l2-2c-1-2 0-2-1-3h-2v-1c3 0 6-2 9-2 0 0 1 1 0 2-2 1-3 2-5 5 1 1 2 1 3 2h2l-1 1 2 2-23-1-6-1c-2 0-4 0-6-1l-11-1c2 0 3 0 4-2l2 1 1-1 1-1h1l4 1c-1 1-1 2-3 2h5c2 1 2 1 4 0 2-2 9 0 12-1 1 1 1 1 3 2 1 0 1 0 2-1z" class="O"></path><path d="M295 917l1-1 1-1h1l4 1c-1 1-1 2-3 2-1 0-3 0-4-1z" class="W"></path><path d="M352 888c3 1 6 2 8 5l-1-1c-1 0-2-1-3-1l-1 1c0 3 1 4 3 6h-5l-2 1c-2 1-5 3-8 3-1 0-1 0-1 1h-2c-2 1-3 2-5 3-1 1-1 1-2 1-1 1-2 1-3 1l-2 2h0-2-2v-2l-1 1h-1 0l-1-1 1-1v-1c1-1 2-2 2-3v-1c1-1 3-2 4-4l-1-1h-5c-1-1-1-2-1-3l1-1c0 1 0 0 1 1v1l1-1c1 0 1 0 2 1-1 0-1 0-2 1h1c2 0 3 0 5-1 1-1 2-2 4-2 1-1 1-1 2-1h1c3-1 7-3 10-3 1 0 1-1 2-1h3z" class="C"></path><path d="M328 898l2 2c-2 2-3 2-5 3l-1-1c1-1 3-2 4-4zm21-5h2l1 2v1h-5v1c-1 0-2 0-3 1-2 0-2 1-3 2v1h-3l-2 2c-1 0-2 0-3 1l-1 1c-1 0-1 1-2 2h0-4-1l1-1-1-2c1 0 1 0 3 1 1 0 3-2 4-3v-2s-1 0-1-1v-1h1c2-1 3-2 5-2h6c0-1 0 0 1-1h4c1 0 1-1 1-2z" class="J"></path><path d="M349 888h3v3c-2 0-4 0-6 1h0c1 1 2 1 3 1 0 1 0 2-1 2h-4c-1 1-1 0-1 1h-6c-2 0-3 1-5 2h-1v1l-1 1-2-2-1-1h-5c-1-1-1-2-1-3l1-1c0 1 0 0 1 1v1l1-1c1 0 1 0 2 1-1 0-1 0-2 1h1c2 0 3 0 5-1 1-1 2-2 4-2 1-1 1-1 2-1h1c3-1 7-3 10-3 1 0 1-1 2-1z" class="L"></path><path d="M349 888h3v3c-2 0-4 0-6 1h0c1 1 2 1 3 1 0 1 0 2-1 2h-4c-1 1-1 0-1 1h-6l4-3c1 1 2 1 3 1h1 3-1c-3 0-4-1-6-2h-3-1c3-1 7-3 10-3 1 0 1-1 2-1z" class="T"></path><path d="M369 895l2-2c1-1 2-2 4-2v2c-1 2-1 5-1 7v4 1l-1 1c1 1 1 2 2 2v2c3 2 8 2 11 2h0c-1 2-2 2-4 3-2 0-5 1-7 1h-4l-1 1h-1c2 1 2 2 3 4h4 0c-2 1-3 1-4 1v1h0-18l-10-1v-1c-1-1-2-1-3-1v-1l1-2-1-1-5 2c-1 0-2 0-2 1h-2c-1-1-2-1-3-2 2-3 3-4 5-5 1-1 0-2 0-2h0c2-1 3-1 4-2s3-1 4-2c1 0 2-1 2-1h3v-1c2 0 3 1 5 1h0c2 0 2-1 3-2 2 0 2 0 4-1l1-1h0c2-2 2-4 2-6l1 3h2 0c1-1 2-1 2-2 1 0 1-1 2-1z" class="U"></path><path d="M367 907v-1c1 0 2-1 2-2 2-1 1-2 3-4l1 1 1 3v1l-1 1c-2-1-4 0-6 1z" class="O"></path><path d="M352 909c0-1 1-2 2-2 2-1 3-1 5-1l1 1c-1 2-4 3-7 4h-1v-1h1c-1-1 0-1-1-1z" class="D"></path><path d="M342 917c2-1 3-1 6-1 2 1 2 1 2 3v1l-6 1c-1-1-2-1-3-1v-1l1-2z" class="c"></path><path d="M362 895l1 3h2 0c1-1 2-1 2-2-2 4-5 6-6 10h-2c-2 0-3 0-5 1-1 0-2 1-2 2h-1v2c-1 0-1 1-2 1l-1-2c1-1 1-1 1-3v-1l-2-1v-1c2 0 3 1 5 1h0c2 0 2-1 3-2 2 0 2 0 4-1l1-1h0c2-2 2-4 2-6z" class="F"></path><path d="M347 905l2 1v1 1c-2 1-4 2-6 2s-4 0-6 2h2c1 0 3 0 4 1l1-1 1 1-4 3h0l-5 2c-1 0-2 0-2 1h-2c-1-1-2-1-3-2 2-3 3-4 5-5 1-1 0-2 0-2h0c2-1 3-1 4-2s3-1 4-2c1 0 2-1 2-1h3z" class="g"></path><path d="M367 907c2-1 4-2 6-1 1 1 1 2 2 2v2c3 2 8 2 11 2h0c-1 2-2 2-4 3-2 0-5 1-7 1h-4l-1 1h-1c2 1 2 2 3 4h4 0c-2 1-3 1-4 1v1h0-18l-10-1v-1l6-1c2 0 1-1 2-2l2 1v-3c2-3 5-5 8-6h0l5-3z" class="f"></path><path d="M367 907c2-1 4-2 6-1 1 1 1 2 2 2v2c3 2 8 2 11 2h0c-1 2-2 2-4 3-2 0-5 1-7 1l-1-2h3v-1c0-1-1-1-2-1-2-1-2-2-4-3-1-1-2-1-3-1l-1 1h0c-2 1-2 1-4 1h-1l5-3z" class="L"></path><path d="M354 919v-3c2-3 5-5 8-6l-1 1c0 1 0 1-1 1-2 1-4 3-5 5l5-1c3-1 3-2 7-2h2l2-1c1 1 2 1 3 1l1 2h-4l-1 1h-1c-1 0-2-1-4 0l-1 1 1 1-1 1c-3 0-6 1-9 0l-1-1z" class="G"></path><path d="M365 919l-1-1 1-1c2-1 3 0 4 0 2 1 2 2 3 4h4 0c-2 1-3 1-4 1v1h0-18l-10-1v-1l6-1c2 0 1-1 2-2l2 1 1 1c3 1 6 0 9 0l1-1z" class="P"></path><path d="M365 919l-1-1 1-1c2-1 3 0 4 0 2 1 2 2 3 4-3 0-5 0-7-2z" class="B"></path><path d="M554 338l1 1c1 2 1 2 2 3 2 0 4 1 6 1h2v1l1-1c1 0 2-1 2-1h1c1-1 1 0 1-1h1 0c1 0 1 0 2-1l1-1h0 2l1 1 7 2c2-1 3 0 5 0 1 0 1 1 2 1v1 3l1 1c-1 2-1 3 0 5h1c-2 4-7 7-10 9-1 1-3 2-3 3-1 0-1 2-1 3-2-1-3-1-5 0 1 2 2 4 2 6v28c-1-1-1-2-2-3s-3-2-4-3l-5-3c-2-1-4-2-6-2-1 0-1-1-3-1h0c-1-1-1-1-2-1l-1 1c-1-1-1-1-2-1h-3l-1-1c1-2 1-6 1-8-1 0-2-1-3-1s-1 0-2-1v-13c0-1 0 0 1-1v-4h0c0-1 1-2 1-2v-1c0-1 2-4 4-4h1 1v-3h0l-1-1-1 1c0-1 0-1 1-2 1 0 2-1 2-2v-1l1-1 1-1c0-1-1-2-2-3 1 0 1-1 2-2h0z" class="m"></path><path d="M545 358c1 0 2 0 3 1l1 2-1 1-4-2h0c0-1 1-2 1-2z" class="i"></path><path d="M573 366l1 2c1 2 2 4 2 6-1 0-1-1-2-1h0c-1-2-1-4-2-5l1-2z" class="n"></path><path d="M544 360l4 2v1l-1 2h-4 0c0-1 0 0 1-1v-4z" class="D"></path><path d="M572 359c0-2 0-4 2-6l1 1c1 0 1-1 2-1l1 1c-1 2-4 4-6 5z" class="U"></path><path d="M558 352l2 2 2-1c1 1 2 1 4 2 1 0 2 1 3 3v1 1l-10-4s-1-1-2-1l-1 1h-3c1-1 1-2 2-3l1 1 2-2z" class="Z"></path><path d="M573 366c-1-2-2-2-2-4 2-2 6-6 9-7 2-1 5-1 7-1-1 1-3 3-5 4h0c-1 0-2 1-2 2-2-1-2-1-3-1h-2v1c1 0 2 0 2 1-1 1-1 1-3 2v1c0 1 0 0-1 1v1z" class="K"></path><path d="M568 387c1-3 1-5 3-7v-2h0-2c0-2-1-3 0-4h1c1 0 0 0 1-1 1 0 2 1 2 2 1 0 1 2 1 3v12h-3-1l-1-1h0-2c1-1 1-2 1-2z" class="i"></path><path d="M577 340l7 2c2-1 3 0 5 0 1 0 1 1 2 1v1c-1 2 0 5 0 7v2h-1l-1-2v2h-3 0c1-1 1-1 2-1v-2c0-2 0-3-2-4-1 1 0 2-1 4h-1v-4-1h-2l-1 2c0 1 0 1-1 1v-4h-1c-1-1-2-1-3-1-2-2 1 0-1-1l2-2z" class="C"></path><path d="M584 342c2-1 3 0 5 0 1 0 1 1 2 1v1c-1 2 0 5 0 7v2h-1l-1-2c1-2 0-5 0-7l-5-2z" class="M"></path><defs><linearGradient id="D" x1="574.247" y1="366.216" x2="586.885" y2="351.72" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#373635"></stop></linearGradient></defs><path fill="url(#D)" d="M589 351l1 2h1v-2c0-2-1-5 0-7v3l1 1c-1 2-1 3 0 5h1c-2 4-7 7-10 9-1 1-3 2-3 3-1 0-1 2-1 3-2-1-3-1-5 0l-1-2h0v-1c1-1 1 0 1-1v-1c2-1 2-1 3-2 0-1-1-1-2-1v-1h2c1 0 1 0 3 1 0-1 1-2 2-2s2 0 3-1c2-1 3-2 4-4v-2z"></path><defs><linearGradient id="E" x1="566.895" y1="346.577" x2="565.357" y2="354.486" xlink:href="#B"><stop offset="0" stop-color="#282727"></stop><stop offset="1" stop-color="#414040"></stop></linearGradient></defs><path fill="url(#E)" d="M554 338l1 1c1 2 1 2 2 3 2 0 4 1 6 1h2v1l1-1c1 0 2-1 2-1h1c1-1 1 0 1-1h1 0c1 0 1 0 2-1l1-1h0 2l1 1-2 2c2 1-1-1 1 1 1 0 2 0 3 1h1v4c1 0 1 0 1-1 0 2 0 3 2 4h-1-2l-1 1c-1 0-2 0-2-1-1 0-2-1-3-1v1l1 1-1 1c-2 2-2 4-2 6l-2 2c-1 0-1 0-1-1v-1-1c-1-2-2-3-3-3-2-1-3-1-4-2l-2 1-2-2-2 2-1-1c-1 1-1 2-2 3v1h0c-1 0-1-1-1-2l1-1-1-1h-1v-3h0l-1-1-1 1c0-1 0-1 1-2 1 0 2-1 2-2v-1l1-1 1-1c0-1-1-2-2-3 1 0 1-1 2-2h0z"></path><path d="M569 359v-2c1-1 0-1 0-2v-4c-1 0-1-1-2-2h1 2c2 0 2 2 4 2l1 1-1 1c-2 2-2 4-2 6l-2 2c-1 0-1 0-1-1v-1z" class="F"></path><path d="M554 343c3 0 5 0 8 1 2 1 3 1 5 2-2 1-2 1-5 1v1h2v1h-1-2c0 2 1 1 0 4h-1l-1-2h-1v1l-2 2-1-1c-1 1-1 2-2 3v1h0c-1 0-1-1-1-2l1-1-1-1h-1v-3h0l-1-1-1 1c0-1 0-1 1-2 1 0 2-1 2-2v-1l1-1 1-1z" class="H"></path><path d="M554 347v-1h3c1 0 2-1 3 1h-1l-4 1-1-1z" class="P"></path><path d="M553 344l1 3 1 1c0 1-1 1 0 2h0c1 0 1 0 2 1 1-1 1-1 2-1v1h-1v1l-2 2-1-1c-1 1-1 2-2 3v1h0c-1 0-1-1-1-2l1-1-1-1h-1v-3h0l-1-1-1 1c0-1 0-1 1-2 1 0 2-1 2-2v-1l1-1z" class="a"></path><path d="M554 338l1 1c1 2 1 2 2 3 2 0 4 1 6 1h2v1l1-1c1 0 2-1 2-1h1c1-1 1 0 1-1h1 0c1 0 1 0 2-1l1-1h0 2l1 1-2 2c2 1-1-1 1 1 1 0 2 0 3 1h1v4c1 0 1 0 1-1 0 2 0 3 2 4h-1-2l-13-5c-2-1-3-1-5-2-3-1-5-1-8-1 0-1-1-2-2-3 1 0 1-1 2-2h0z" class="D"></path><defs><linearGradient id="F" x1="567.394" y1="372.682" x2="552.349" y2="390.289" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#F)" d="M548 363c1 1 1 1 1 2l2-2v1h4c1 2 0 9 1 12 0-2-1-7 0-9 1 0 2 1 3 1 0 1 0 1 1 2v3l1-5c1 0 1 0 2 1 1 0 2 1 3 2v1l2 2v3 10s0 1-1 2h2 0l1 1h1 3v6 3c-1-1-3-2-4-3l-5-3c-2-1-4-2-6-2-1 0-1-1-3-1h0c-1-1-1-1-2-1l-1 1c-1-1-1-1-2-1h-3l-1-1c1-2 1-6 1-8-1 0-2-1-3-1s-1 0-2-1v-13h0 4l1-2z"></path><path d="M566 372l2 2v3 10s0 1-1 2c0-1 0-1-1-2v-15z" class="S"></path><path d="M556 376c0-2-1-7 0-9 1 0 2 1 3 1 0 1 0 1 1 2v3 1 6c0 1-1 3 0 5h-1-1c0-1-1-1-2-1v-8z" class="V"></path><path d="M617 752h3 1 3l1 1 4 2c4 4 7 8 11 12l1 2h1 3l1 1 1-1-1-1v-2h1 2v3h0c1 0 0 0 1 1h-1-1l1 1h2v-2l1 1v1h4 1l-1 1-5 1c2 2 3 3 4 5 1 1 1 3 1 5s-2 4-4 5v3c0 3 1 7 0 9s-1 2-1 4v1h-1l1 2c1 0 1 1 1 2 1 1 1 2 2 3s0 2 0 3l-1 1c-3 0-5 0-8-1h-1c-1 0-1 0-2-1 1-1 1-2 1-4h0l1-2c-2-1-4 0-6 0-3-1-5 0-8 0l-11-1v-1c-1-2-1-6-1-8v-4-9h-3c1 1 1 1 1 2-1 0-1 1-2 1l-2-2h0c1 0 1-1 2-1l-1-1c-2 0-3 0-5-1v-4c1-1 1-1 0-2 1-2 1-4 1-6l-1-1c3 0 2 1 4 2l1-1c1-2 1-2 0-4h6c-1-2-2-2-3-3v-1c-1 0-1-1-2-2 1-1 1-2 1-3 0-2-1-2-2-3 0-1 1-1 1-2v-1h3z" class="b"></path><path d="M645 800c1 0 2 0 3 1s1 3 1 4l-2 1v1h-1l-1-1 1-1c0-1-1-3-1-4v-1z" class="J"></path><path d="M632 793l1 1v1c2-1 2-1 4-1l1 1h1c1 2 0 5 1 7 0 1 1 1 1 3h-1v2c-1 0-2 0-2 1-3-1-5 0-8 0l-11-1v-1h3 4 3 0c1-3 1-5 0-7l-1-1 1-1v-1l1-2 1-1h1z" class="I"></path><path d="M632 793l1 1v1c2-1 2-1 4-1l1 1v10c-2 1-3 1-5 1-1-2 0-5 0-7 2-1 3 0 4-1l-1-1c-1 0-1 0-3 1l-1-5z" class="V"></path><path d="M622 791v-1l-1-1 1-1v-1h-2v-1c4 0 9 1 13 1h1l1 1v1c-2-1-2-1-3 0s0 1 0 2v-1h2l1 1v1l-1-1c-1 1-2 1-3 2l-1 1-1 2v1l-1 1 1 1c1 2 1 4 0 7h0-3-4-3c-1-2-1-6-1-8v-4l2-1v-3l2 1z" class="R"></path><path d="M626 806l-1-1v-1c0-1 1-3 0-5h4v7h-3z" class="U"></path><path d="M618 794l2-1v-3l2 1c-1 1-1 1-1 2h1v2l-2 1v2c1 1 1 2 2 2v1h-2v2h0 2 1v-2l1 1v2l-2 2h-3c-1-2-1-6-1-8v-4z" class="m"></path><path d="M646 766h1 2v3h0c1 0 0 0 1 1h-1-1l1 1h2v-2l1 1v1h4 1l-1 1-5 1c2 2 3 3 4 5 1 1 1 3 1 5s-2 4-4 5v3s-1 0-1-1h-1 0c-1-1-3-1-4-1l-1 1c-1 0-1-1-2 0l-1 1h-1v1l3 3-1 1h-1-1v-4c0-1-1-2-1-3-1-1-2-1-4 0h-1v-1-1h4c2 0 4 0 5-1-1-1-1-1-3-1h0l2-1c1 0 1 0 1-1v-2-1c1-2 1-4 0-5-1-2-2-2-3-2l1-1-1-2 1-1h3l1 1 1-1-1-1v-2z" class="F"></path><path d="M649 781v-2l-2-2v-1l3 2v-1l-1-2 1-1c1 1 3 3 5 4 0 2 1 5-1 7-1 1-2 1-3 1h-1 0v-1h-1v-2c-1 0-1 0-1 1h-1l2-3z" class="N"></path><path d="M617 752h3 1 3l1 1 4 2c4 4 7 8 11 12l1 2h1l-1 1 1 2-1 1c1 0 2 0 3 2 1 1 1 3 0 5v1 2c0 1 0 1-1 1l-2 1h0c2 0 2 0 3 1-1 1-3 1-5 1h-4v1l-1-1h-1c-4 0-9-1-13-1v1h2v1l-1 1 1 1v1l-2-1v3l-2 1v-9h-3c1 1 1 1 1 2-1 0-1 1-2 1l-2-2h0c1 0 1-1 2-1l-1-1c-2 0-3 0-5-1v-4c1-1 1-1 0-2 1-2 1-4 1-6l-1-1c3 0 2 1 4 2l1-1c1-2 1-2 0-4h6c-1-2-2-2-3-3v-1c-1 0-1-1-2-2 1-1 1-2 1-3 0-2-1-2-2-3 0-1 1-1 1-2v-1h3z" class="k"></path><path d="M613 771c2-2 3-1 5-1v6c1 2 1 7 0 8v1h0-3c1 1 1 1 1 2-1 0-1 1-2 1l-2-2h0c1 0 1-1 2-1l-1-1c-2 0-3 0-5-1v-4c1-1 1-1 0-2 1-2 1-4 1-6l-1-1c3 0 2 1 4 2l1-1z" class="E"></path><path d="M641 769h1l-1 1 1 2-1 1c1 0 2 0 3 2 1 1 1 3 0 5v1 2c0 1 0 1-1 1h-10c-2-1-4 0-6-1h-1c-2-1-4 0-5-1v-1c1 0 2 0 3 1h3 0v-1h-1l-5-1v-1h3v-1c-2 0-3 0-4-1h0v-4h1 1 1 2 0l1 1c1 0 1-2 2-2l1 1h1l1 1c2-2 2-1 4-1l1-3c2 0 3 0 5-1h0z" class="X"></path><path d="M644 780h-1c-2-1-4 0-5-1 0-1 0-2 1-3v-3h2c1 0 2 0 3 2 1 1 1 3 0 5z" class="T"></path><path d="M617 752h3 1 3l1 1 4 2c4 4 7 8 11 12v1c-2 1-3 0-4 0h-6l-7-1h-2l1-1v-1c-1 0-1 0-3-1-1 0-1-1-2-1h-1c-1 0-1-1-2-2 1-1 1-2 1-3 0-2-1-2-2-3 0-1 1-1 1-2v-1h3z" class="W"></path><path d="M617 752h3 1-4c0 1 1 1 1 2s0 2-1 3c0 1 0 2 1 3h0c0 1 1 2 1 3v1c-1 0-1-1-2-1h-1c-1 0-1-1-2-2 1-1 1-2 1-3 0-2-1-2-2-3 0-1 1-1 1-2v-1h3z" class="i"></path><path d="M624 752l1 1 4 2c-2 1-2 0-4 2 1 3 2 5 4 8h0c0 1 1 2 1 3l-7-1v-1h2v-1c-1 0-2-1-2-2-2-1-2-5-4-7v-1c1-1 2 0 3-1l1-1 1-1z" class="C"></path><path d="M630 768c0-1-1-2-1-3h0c-2-3-3-5-4-8 2-2 2-1 4-2 4 4 7 8 11 12v1c-2 1-3 0-4 0h-6z" class="L"></path><path d="M371 854c-1 0-1 2-2 2 0 0-1-1-2-1h0c0-1 1-1 2-2l-1-1 1-1-1-1 1-2c-1-1-1-2-1-3 1-2 0-6 0-9 0-1-1-1-1-2 1 0 2 2 3 2h3 5 8 1 5 6c2 2 2 2 2 4v1h2 1l1 1 1-1 1 1 3-1 1 1h2l1 1-1 1 1 2h-2v1 1c-1 2-2 3-3 5v1l-1 1-1 3c1 1 1 1 2 1l-3 2-2 2c-1 1-1 3-2 4 0 1-1 2 0 3l-1 1-3-1h-4c-3 0-6 1-8 3h-1c-1 2-3 4-3 6-1 2-1 4-2 6l-3 5-1 1c-2 0-3 1-4 2l-2 2c-1 0-1 1-2 1 0 1-1 1-2 2h0-2l-1-3-2-2h0c-2-3-5-4-8-5h-3c-1 0-1 1-2 1-3 0-7 2-10 3h-1c-1 0-1 0-2 1-2 0-3 1-4 2-2 1-3 1-5 1h-1c1-1 1-1 2-1-1-1-1-1-2-1l-1 1v-1c-1-1-1 0-1-1l3-3c2-2 4-3 6-5h0s0-1 1-1h0v-1-1l1-1c1 0 1-1 2-2h0c2 1 2 1 4 1 1-2 3-3 5-3 3-1 5-1 8-1h0l2 1c2 1 4 2 6 2h1l1-1 1-1c2-2 4-2 6-3l1-1h-1-3 1c0-2-1 0 0-2h1v-1l-1-1v-1h2c0-2 0-4 1-6 0-1 1-2 1-4v-3-1z" class="k"></path><path d="M376 852l1 1-3 6h-1v2h1v-2c1-1 2-2 4-2 0 2 0 3 1 5l-2 2c-1 1-3 2-5 3h0c-1 0-1 1-2 1 0-2 0-3 1-5l1-1-1-1c2-3 3-6 5-9z" class="b"></path><path d="M399 855l2-2v-1h-3l-1-1 3-1h1l1 1v1l1 2h0l1 2 2 2c1 1 1 1 2 1l-3 2-2 2c-1 1-1 3-2 4 0 1-1 2 0 3l-1 1-3-1h-4l2-2h1v-1h-1c1-1 1 0 2-1 1 0 2-2 3-4 0-1 1-1 2-2v-1c0-2-1-2-2-3l-1-1z" class="O"></path><path d="M404 856l2 2c1 1 1 1 2 1l-3 2h-1l-1-2 1-3z" class="H"></path><path d="M380 853h1v3 2h2c2-1 2-1 4-1v1c0 1 0 1 1 2 0 0-1 1-1 2-1 0-1 0-1 1s-1 1-1 2v1h0l1 1s1 0 1 1c-2 1-4 1-7 1v1h4 0c-3 2-5 0-7 1v1 1c-1-1-2-2-3-2 1-2 4-4 5-6s1-6 1-8v-4z" class="G"></path><path d="M380 853h1v3 2h2l3 2v1l-2 1-1-1c-1 0-2 0-3-1v-3-4z" class="L"></path><path d="M400 841h2 1l1 1 1-1 1 1 3-1 1 1h2l1 1-1 1 1 2h-2v1 1c-1 2-2 3-3 5v1l-1 1-1 3-2-2-1-2h0l-1-2v-1l-1-1h-1l-3 1 1 1h3v1l-2 2c-2-2-5-3-8-4l2-2h-1c1-1 2-1 3-1v-3c2-1 3-2 4-3v-1h1z" class="Y"></path><path d="M405 841l1 1 3-1 1 1c0 2 1 3 0 5h-2l-1 1-2-1v-3c0-1-1-2-1-2l1-1z" class="n"></path><path d="M405 841l1 1c0 1-1 1 0 2l-1 1 1 1c1-1 2-1 2-2l1 1c0 1-1 1-1 2l-1 1-2-1v-3c0-1-1-2-1-2l1-1z" class="Z"></path><path d="M400 841h2v4 1 1h-1c-2 0-4 2-5 3h-1c0-1 0-1-1-1h-1-1c1-1 2-1 3-1v-3c2-1 3-2 4-3v-1h1z" class="d"></path><path d="M410 842h2l1 1-1 1 1 2h-2v1 1c-1 2-2 3-3 5v1l-1 1-1 3-2-2-1-2h0l-1-2v-1h0c1-1 1-1 1-2v-1h1v1h2 1c2 0 0 0 1-1 0-1 1-1 2-1 1-2 0-3 0-5z" class="F"></path><path d="M403 854h4v1l-1 3-2-2-1-2z" class="S"></path><path d="M364 886h0v-1c2 0 3 1 4 0 2-1 5-2 7-2 1-1 2-3 2-4h4c-1 2-1 4-2 6l-3 5-1 1c-2 0-3 1-4 2l-2 2c-1 0-1 1-2 1 0 1-1 1-2 2h0-2l-1-3-2-2h0c-2-3-5-4-8-5h-3c2-1 3-1 5-1 1 1 2 1 2 1l1-1h2l1-1 1 1c1-1 2-1 3-1z" class="a"></path><path d="M360 893l1-1 3 2c2-1 2-1 3 0l2 1c-1 0-1 1-2 1 0 1-1 1-2 2h0-2l-1-3-2-2z" class="M"></path><path d="M367 894c2-1 2-1 2-2s-1-1-1-1l-2 1h-1l-1 1-2-2-1-2v-1h1l1 1h0c2 0 4 0 6-1 1 0 1 0 2-1h2 1 0l-2 1c2 0 3 1 4 2l-1 1c-2 0-3 1-4 2l-2 2-2-1z" class="H"></path><path d="M380 853v-1c1-1 1-2 3-3h3c2 1 4 1 5 2 3 1 6 2 8 4l1 1h-2v1l2 1-1 1c-1 0-2 0-3 2v1h-2l1 1v1c-2 1-3 0-4 2h-1c-1 0-2 1-3 2 0-1-1-1-1-1l-1-1h0v-1c0-1 1-1 1-2s0-1 1-1c0-1 1-2 1-2-1-1-1-1-1-2v-1c-2 0-2 0-4 1h-2v-2-3h-1z" class="Q"></path><path d="M384 853l1-2c1 0 1 0 2 1 1-1 2-1 3 0h0l-1 1c0 1 1 1 2 2h-1c-1 0-2 0-2 1l-4-1-3 1v-3h1 2z" class="X"></path><path d="M384 853l3-1v1l-3 2-3 1v-3h1 2z" class="f"></path><path d="M391 855c1 0 1 1 2 2-1 1-2 1-3 2l2 1c-1 1-2 2-3 2h0c-1 1-1 2-3 2v1s1 0 1 1h-1v1l-1-1h0v-1c0-1 1-1 1-2s0-1 1-1c0-1 1-2 1-2-1-1-1-1-1-2v-1c-2 0-2 0-4 1h-2v-2l3-1 4 1c0-1 1-1 2-1h1z" class="J"></path><path d="M370 873c2 0 3 1 5 2l1 1c-1 1-2 1-3 1v2l-2 1c-1 1 0 2-2 1h-1v2l-2-1-1 1 1 1c-1 1-2 0-3 1l1 1c-1 0-2 0-3 1l-1-1-1 1h-2l-1 1s-1 0-2-1c-2 0-3 0-5 1-1 0-1 1-2 1-3 0-7 2-10 3h-1l-1-1c0-1 1-2 2-3h3c2-1 4-2 6-2 2-1 5-3 7-4l3-1h0 1c-1-1 0-1-1-1s-2-1-2-1v-2c2 1 4 2 6 2h1l1-1 1-1c2-2 4-2 6-3l1-1z" class="C"></path><path d="M363 877c2-2 4-2 6-3l2 2-4 2c-1 0-3-1-4-1z" class="X"></path><path d="M356 881c1 1 2 1 3 1s1-1 1 0h-3l-1 2c0 1 0 1-1 2v-1c-3-1-7 0-9 1 2-1 5-3 7-4l3-1z" class="N"></path><path d="M354 877c2 1 4 2 6 2h1l1-1c1 0 1 1 2 1s1-1 2 0l-6 3c0-1 0 0-1 0s-2 0-3-1h0 1c-1-1 0-1-1-1s-2-1-2-1v-2z" class="X"></path><path d="M344 877c3-1 5-1 8-1h0l2 1v2s1 1 2 1 0 0 1 1h-1 0l-3 1c-2 1-5 3-7 4-2 0-4 1-6 2h-3c-1 1-2 2-2 3l1 1c-1 0-1 0-2 1-2 0-3 1-4 2-2 1-3 1-5 1h-1c1-1 1-1 2-1-1-1-1-1-2-1l-1 1v-1c-1-1-1 0-1-1l3-3c2-2 4-3 6-5h0s0-1 1-1h0v-1-1l1-1c1 0 1-1 2-2h0c2 1 2 1 4 1 1-2 3-3 5-3z" class="f"></path><path d="M335 879h0c2 1 2 1 4 1-2 3-4 4-8 5 0 0 0-1 1-1h0v-1-1l1-1c1 0 1-1 2-2z" class="V"></path><path d="M334 887c3 0 5-1 8-2 3-2 7-3 11-3-2 1-5 3-7 4-2 0-4 1-6 2h-3c-1 1-2 2-2 3l1 1c-1 0-1 0-2 1-2 0-3 1-4 2-2 1-3 1-5 1h-1c1-1 1-1 2-1-1-1-1-1-2-1l-1 1v-1c-1-1-1 0-1-1l3-3c2-2 4-3 6-5v2h3z" class="Q"></path><path d="M325 890c2-2 4-3 6-5v2h3c-1 1-2 1-2 2-1 2-3 2-4 3l-1-1-2-1z" class="L"></path><path d="M371 854c-1 0-1 2-2 2 0 0-1-1-2-1h0c0-1 1-1 2-2l-1-1 1-1-1-1 1-2c-1-1-1-2-1-3 1-2 0-6 0-9 0-1-1-1-1-2 1 0 2 2 3 2h3 5 8 1 5 6c2 2 2 2 2 4v1h-1v1c-1 1-2 2-4 3v3c-1 0-2 0-3 1h1l-2 2c-1-1-3-1-5-2h-3l1-1h-1c-1 0-1 0-2-1-2 0-5 1-7 2l-3 6v-1z" class="I"></path><path d="M384 841c0-1 1-1 1-2h0v-1l1-1v1l1 1v2l1 1h1c1 0 1 1 1 2h-1-3v1h-2l1-1c0-1-1-2-1-3z" class="C"></path><path d="M391 841h1l2 1c0 1 0 2 1 3v3c-1 0-2 0-3 1h1l-2 2c-1-1-3-1-5-2h4v-3h-1l1-1 2 1c0-1 0-1 1-2l-2-2v-1z" class="h"></path><path d="M372 841h4v-1l1-1c0 1 1 1 1 2h3 3c0 1 1 2 1 3l-1 1h2c1 0 0 0 1 1l-1 1-2-1h-2c-3-2-6-2-9-2l-2-1 1-2z" class="E"></path><path d="M387 836h5 6c2 2 2 2 2 4v1h-1v1c-1 1-2 2-4 3-1-1-1-2-1-3l-2-1h-1-3-1v-2l-1-1 1-2z" class="Y"></path><path d="M387 839l3-2c1 1 1 1 2 1v2h-1l-3 1h-1v-2z" class="F"></path><path d="M392 841h2v-2h1v1c1 0 1 1 2 1h2v1c-1 1-2 2-4 3-1-1-1-2-1-3l-2-1z" class="W"></path><path d="M371 854c-1 0-1 2-2 2 0 0-1-1-2-1h0c0-1 1-1 2-2l-1-1 1-1-1-1 1-2c-1-1-1-2-1-3 1-2 0-6 0-9 0-1-1-1-1-2 1 0 2 2 3 2h3 5 8 1l-1 2v-1l-1 1v1h0c0 1-1 1-1 2h-3-3c0-1-1-1-1-2l-1 1v1h-4-2c0 3 0 7 1 10v3z" class="V"></path><path d="M447 934l-1-1h0c4-1 9-1 13-1-1-1-2-1-3-1h-1c-1 0-4 0-5-1 1-1 3 0 4 0h13 1v1h1c2 0 4-1 5 0 2 0 9 1 11 0 2 0 8-1 10 0h0 1 1c1 0 8-1 9 0h13c2 0 4 1 5 0 1 0 2 0 3-1 1 0 3 1 4 0 2 0 14-1 15 0-2 1-4 0-6 1l-2 1h0 12c3 0 6 2 9 0v1l1 1c-4 0-19-1-20 0h-13v1h33c0-1 0-1 1-1 2 0 5-1 7 0 1 0 3 0 5 1 1 0 2-1 4 0h13c1 0 3 1 5 0h4c2 1 11 1 14 1l1-1h2v1h-2v1h-13l-1 2h-6v1h8l-1 1h-1-1c-2 0-6 0-7 1h-5-10-3-13 0-4c-2 1-5 1-7 1h-3c-1-1-2 0-4-1h-2c-3 0-6 0-9-1-1 1-5 0-6 0-1-1-2 0-4 0h-16l1 1h10 7l1 1h10c-3 1-4 0-7 1-2 0-5 1-8 1h0-16v1h1 3l1 1h0-4-5c-2 0-5 0-6 1-2 0-4-1-5 0v-1l1-1c-2-1-6 0-8 0h-6c-1 1-3 1-4 0-2 0-5 1-7 0-10-2-21-1-31 0l-41-3h-6 0c1-1 3-1 4-1h10 4c2-1 2-1 4-1l19-1c4 1 13 1 17 0l6-1h-15-2c-2 0-3 0-4-1h-1c-2-1-8 0-10 0-3 1-7 0-10 0h2c1-1 4 0 5 0v-1h-7l15-1h0c-2 0-3 0-5-1h12c5 0 11-1 17 0h0l1-1h-2z" class="N"></path><path d="M393 943h-6 0c1-1 3-1 4-1h10 4c2-1 2-1 4-1 1 0 1 1 2 1h5c4 0 7 1 11 1 1 0 2-1 3 0l-2 1h0-3c-5 0-11 0-15-1h-5c-2-1-10-1-11 0h-1z" class="J"></path><path d="M424 936l70 1v1h-65c-2-1-8 0-10 0-3 1-7 0-10 0h2c1-1 4 0 5 0v-1h-7l15-1h0z" class="G"></path><path d="M482 946l1-1h0-5c-2 1-10 1-12 0s-3 0-5 0c-5-1-11-1-17-1l1-1h0 13c1-1 1-1 2-1 1-1 3 0 5 0 6-1 14-1 21-1l1 1h5c1 0 2 0 4-1h10l1 1h10 7l1 1h10c-3 1-4 0-7 1-2 0-5 1-8 1h0-16v1h1 3l1 1h0-4-5c-2 0-5 0-6 1-2 0-4-1-5 0v-1l1-1c-2-1-6 0-8 0zm78-11c0-1 0-1 1-1 2 0 5-1 7 0 1 0 3 0 5 1 1 0 2-1 4 0h13c1 0 3 1 5 0h4c2 1 11 1 14 1l1-1h2v1h-2v1h-13l-1 2h-6v1h8l-1 1h-1-1c-2 0-6 0-7 1h-5-10-3-13 0-4c-2 1-5 1-7 1h-3c-1-1-2 0-4-1h-2c-3 0-6 0-9-1-1 1-5 0-6 0-1-1-2 0-4 0v-1h-1c-3-1-4 0-7 0h-11-2-20-4-4-14 0c2-1 6-1 9-1h25 2 12c1 0 3 0 4-1h1 1v-1h-10-2-3v-1c8 0 16-1 24 0 2 1 35 1 36 0 0 0 1-1 2-1z" class="X"></path><path d="M577 942v-1h-1c-3-1-9 0-12-1 1-1 3-1 5-1h12c0 1 0 1 1 2 1 0 3-1 5 1h-10z" class="T"></path><path d="M581 939h13v1h8l-1 1h-1-1c-2 0-6 0-7 1h-5c-2-2-4-1-5-1-1-1-1-1-1-2z" class="p"></path><path d="M532 941c3-1 5-1 7-1h0c-1 0-1 0-2-1l1-1h0l1 1h1c1 0 2 0 4-1 1 0 4 1 5 0 3 0 4-1 7 1-1 1-4 0-4 1h3v1h0-7v1l13-1v1h0-4c-2 1-5 1-7 1h-3c-1-1-2 0-4-1h-2c-3 0-6 0-9-1z" class="J"></path><path d="M619 807l11 1c3 0 5-1 8 0 2 0 4-1 6 0l-1 2h0c0 2 0 3-1 4 1 1 1 1 2 1h1c3 1 5 1 8 1l1 1h1l1 2h2 3c-1 1-3 2-3 4h1c0-2 1-3 3-4 1-1 5-1 6 0h0-2v1h0c1 6-1 12-1 18l-1 1 1 1v6c-1 2 0 5 0 7l6 7c2 2 4 1 6 2h6l3 1-1 1c-5-2-8-1-12 0l-3 1c-4 2-6 3-7 7h0c-1 1-1 2-1 3 1 1 1 3 1 5l-1 1-5-2v-5c-1-1-1-3-2-5l-2-2c-2-3-4-4-7-4v-1c-1 0-2 1-4 1 0-1-1-2-1-2l-1-1-2 1c-1-1-2-3-4-3l-1-1-4-3v2c-2-1-5-1-7 0l-2 1-2 1v1c-2 0-3 0-4 1h-2c0 1 1 2 3 3l-4 1v1l-1 1c-1 3-2 6-4 9h0-1c-1-1-1-1-2-1h0l-1-1c-1 0-2 0-3-1h-1l-1-1v-3h0l-1-1v-1c0-1-1-1-2-1v-3-3c0-3 3-8 5-9h1l1-1 1-1c2-1 2-2 3-3v-1h1c1-1 3-2 3-3-1-1-1-1-2-1v-2c0-1-2-2-3-2h-1c-1 1-3 1-5 1 0-1 1-2 1-4l-1-1h1l1-1c1 1 1 1 2 0 2 0 4 0 6-1l-1-1 1-1c1-1 1 0 1-1 1-1 1-2 2-3l1-1c-1-1-1-1-1-2h1v-1-1h-4-3 0c1-2 1-4 1-6h2c1 0 2-2 2-2l2-1 1 1 1 1h1v-1h2l1-3 1-1z" class="E"></path><path d="M645 829h1c1 0 1-1 2-1l1-2h1c0 1 0 2-1 3l-1 1c0 1-1 2-1 2-2 0-2-1-3 0l-1-1c1 0 1-1 2-2z" class="I"></path><path d="M631 826h0c2-1 4-1 6-1l2 1h0c1 0 1 1 1 0h1 2v1h-3v1h0l1 1c2 0 3-1 4 0-1 1-1 2-2 2-1-1-1 0-1-1v-1h-8l-1-1-2-2z" class="D"></path><path d="M655 836c2-1 5 0 8 0v1 1 7h0v3 4c-1-1-2 0-3 0h-1s0-1-1-1l-1 1-1-1c1-1 1-1 1-2-2-3-1-9-2-12v-1z" class="G"></path><path d="M661 842l-1-1c-1-1 0-3 0-4l1-1 1 1-1 2h0v3z" class="f"></path><path d="M661 839l2-1v7c-1 0-3-1-4 0l-1 2h0v-4c1 0 1 0 2-1h1v-3z" class="X"></path><path d="M658 847l1-2c1-1 3 0 4 0h0v3 4c-1-1-2 0-3 0h-1c0-2 0-4-1-5z" class="f"></path><path d="M619 807l11 1c3 0 5-1 8 0 2 0 4-1 6 0l-1 2h0c0 2 0 3-1 4 1 1 1 1 2 1h1c3 1 5 1 8 1l1 1h1l1 2h-2v2h-1v-2c-2-1-7 0-10-1l-1 1v5h4v1h-9c-2 0-4 0-6 1h0-1-1l1-1-1-1v-3c-1 1-2 1-3 1h0c-2-1-3-1-4-2h-1l-1-1h-1-1 0l2 2 1 1v1c-2-1-3-2-5-2-1 0-1 0-2-1h0l-2 1v-1-1h-4-3 0c1-2 1-4 1-6h2c1 0 2-2 2-2l2-1 1 1 1 1h1v-1h2l1-3 1-1z" class="U"></path><path d="M630 810l1-1c1 1 1 1 3 1v2l-1 1c0 2 0 3-1 4h-1c-1-2-1-5-1-7z" class="E"></path><path d="M634 812l1-1v-1c1 0 1 0 2 1v-1l2-1h1v7h-1 0-3-1c-1-1-1-1-2-3l1-1z" class="D"></path><path d="M620 810h3c1 0 2 0 2 1l1-1c1 1 1 3 2 3v-3h2c0 2 0 5 1 7-3 0-2-2-4 0-1 0-2-2-2-1l-1 1c-2-1-2 0-4 0h-1c0-3 0-5 1-7z" class="h"></path><path d="M613 811l1 1h1v-1h2l1-3 2 2c-1 2-1 4-1 7h1c2 0 2-1 4 0l1-1c0-1 1 1 2 1 2-2 1 0 4 0h1c1-1 1-2 1-4 1 2 1 2 2 3h1 3 0c0 3 2 6 0 8h-1c-3-1-5 0-8 1l-1-1v-3c-1 1-2 1-3 1h0c-2-1-3-1-4-2h-1l-1-1h-1-1 0l2 2 1 1v1c-2-1-3-2-5-2-1 0-1 0-2-1h0l-2 1v-1-1h-4-3 0c1-2 1-4 1-6h2c1 0 2-2 2-2l2-1 1 1z" class="O"></path><path d="M613 811l1 1h1v-1h2l1-3 2 2c-1 2-1 4-1 7h1c-1 1-2 1-3 1 0-1-1-1-1-1-2 0-1 1-3 1h0-1c0-1 0-2 1-3h0l-1-2h2c-1-1-1-1-1-2z" class="V"></path><path d="M612 810l1 1c0 1 0 1 1 2h-2l1 2h0c-1 1-1 2-1 3-1 1-2 1-4 1h-3 0c1-2 1-4 1-6h2c1 0 2-2 2-2l2-1z" class="n"></path><path d="M639 816h0c0 3 2 6 0 8h-1c-3-1-5 0-8 1l-1-1v-3c-1 1-2 1-3 1h0c-2-1-3-1-4-2 1-1 1 0 3 0l1-1h0c3 0 5 0 7-1h1l2 1c1-1 2-1 3-3z" class="R"></path><path d="M621 823v-1l-1-1-2-2h0 1 1l1 1h1c1 1 2 1 4 2h0c1 0 2 0 3-1v3l1 1-1 1h1 1l2 2 1 1h8v1c0 1 0 0 1 1l1 1c1-1 1 0 3 0l2 4c2 1 2 3 2 5 0 1 0 1 1 1v-1l1-1 1 1h0c0-3 0-3-1-5v-1c1 1 1 0 2 1v1c1 3 0 9 2 12 0 1 0 1-1 2l1 1 1-1c1 0 1 1 1 1h1c1 0 2-1 3 0l2 1 6 7c2 2 4 1 6 2h6l3 1-1 1c-5-2-8-1-12 0l-3 1c-4 2-6 3-7 7h0c-1 1-1 2-1 3 1 1 1 3 1 5l-1 1-5-2v-5c-1-1-1-3-2-5l-2-2c-2-3-4-4-7-4v-1c-1 0-2 1-4 1 0-1-1-2-1-2l-1-1-2 1c-1-1-2-3-4-3l-1-1-4-3h-1-2v-1l1-1 1-1c-1 0-2 0-3-1l1-1 1-1c2-2 3-3 6-3l1-1c0-2 0-4-1-7h-1v-3h0v-2c-1-2-3-3-4-4h-1v1 1l-1-1c-1-3-3-4-5-6z" class="a"></path><path d="M643 848c2 0 2-1 4 0v2h0l-1 1h1v2l-2-2c-1 0-2-1-3-2v-1h1z" class="K"></path><path d="M647 839h0v5s1 1 2 1 1-1 2-1h0v6c-1 1-1 2-1 4h0c-1-1-2-3-3-4v-2c-1-3 0-6 0-9z" class="g"></path><path d="M652 842v-1l1-1 1 1h0c0-3 0-3-1-5v-1c1 1 1 0 2 1v1c1 3 0 9 2 12 0 1 0 1-1 2l1 1 1-1c1 0 1 1 1 1h1c1 0 2-1 3 0l2 1 6 7v1h-1l-2-2c-1-2-3-4-4-5-2-1-3-1-5 0l-1-1-1 1c-1 1-1 1-3 1v-3h-1c0 2-1 2-1 3-1-1-1-3-1-5v-6l1-2z" class="R"></path><path d="M629 854h-1-2v-1l1-1 1-1c-1 0-2 0-3-1l1-1 1-1c2-2 3-3 6-3v1h1c1-1 1 0 2 0l-3 1h0l2 1c5 0 8 4 11 7l1 3c1 0 1 0 2 1-2 1-2 1-3 3-1 0-2 1-4 1 0-1-1-2-1-2l-1-1-2 1c-1-1-2-3-4-3l-1-1-4-3z" class="X"></path><path d="M632 849h1l2 1h1c2 0 1 2 3 3 0-1 0 0 1-1 1 1 1 2 1 3l-1 1-2-2h-1 0-1c-1-1-1-2-2-2l-2-2v-1z" class="T"></path><path d="M629 854h-1-2v-1l1-1 1-1c-1 0-2 0-3-1l1-1 1-1c2-2 3-3 6-3v1h1c1-1 1 0 2 0l-3 1h0l2 1h-4l-1 1 1 1-2 2 7 4 4 4-2 1c-1-1-2-3-4-3l-1-1-4-3z" class="m"></path><path d="M659 854c2-1 3-1 5 0 1 1 3 3 4 5l2 2h1v-1c2 2 4 1 6 2h6l3 1-1 1c-5-2-8-1-12 0l-3 1c-4 2-6 3-7 7h0c-1 1-1 2-1 3 1 1 1 3 1 5l-1 1-5-2v-5c-1-1-1-3-2-5l-2-2c-2-3-4-4-7-4v-1c1-2 1-2 3-3h0v2l2-2v1h0v3l2 1h0v-2h1v-2-2c1-2 4-4 5-4z" class="V"></path><path d="M659 854c2-1 3-1 5 0 1 1 3 3 4 5l2 2h1v-1c2 2 4 1 6 2l-5 2-2-2h-2l-7-6-2-2z" class="X"></path><path d="M677 862h6l3 1-1 1c-5-2-8-1-12 0l-3 1c-4 2-6 3-7 7h0c-1 0-1 0-1-1h0-2c-1-1-2-1-2-3 1 1 2 1 2 2h1l1-1 1-1h0c0-1 0-2-1-4v-1h2l1-1 1 1c0 1 0 1 1 2 1 0 3-1 5-1l5-2z" class="K"></path><path d="M649 859h0v2l2-2v1h0v3l2 1h0v-2h1l2 2c1 2 3 2 4 3 1 0 2 1 3 1h0l-1 1-1 1h-1c0-1-1-1-2-2h-1-1c0 1-1 1-1 1l-2-2c-2-3-4-4-7-4v-1c1-2 1-2 3-3z" class="Y"></path><path d="M655 869s1 0 1-1h1 1c0 2 1 2 2 3h2 0c0 1 0 1 1 1-1 1-1 2-1 3 1 1 1 3 1 5l-1 1-5-2v-5c-1-1-1-3-2-5z" class="P"></path><path d="M621 823v-1l-1-1-2-2h0 1 1l1 1h1c1 1 2 1 4 2h0c1 0 2 0 3-1v3l1 1-1 1h1 1l2 2 1 1h8v1c0 1 0 0 1 1l1 1c1-1 1 0 3 0l2 4c2 1 2 3 2 5 0 1 0 1 1 1l-1 2h0c-1 0-1 1-2 1s-2-1-2-1v-5h0c0 3-1 6 0 9-2-1-2 0-4 0h-1l-1-1-1 1c-2 0-3-1-4-2-1 0-1-1-2 0h-1v-1l1-1c0-2 0-4-1-7h-1v-3h0v-2c-1-2-3-3-4-4h-1v1 1l-1-1c-1-3-3-4-5-6z" class="W"></path><path d="M640 839v7h-2l1-1v-1c-1-1-2-1-3-2 0-2 0-2 1-4-1 0 0 0-1-1 1-1 2-1 4-1v3z" class="I"></path><path d="M640 836c2 0 3-1 5 0 1 1 1 2 2 3 0 3-1 6 0 9-2-1-2 0-4 0l1-3h0l-2 1h-1l1-2h-1c0-2 0-3-1-4v-1-3z" class="H"></path><path d="M642 844v-1h0c1-1 2-1 3-1l1 2v1h-2 0l-2 1h-1l1-2z" class="K"></path><path d="M621 823v-1l-1-1-2-2h0 1 1l1 1h1c1 1 2 1 4 2h0c1 0 2 0 3-1v3l1 1-1 1h1 1l2 2-1 1-2-2-1 1c1 0 3 1 3 2 1 1 0 1 2 1h1l1 1h-1v3h-1v-2h-1v4h-1v-3h0v-2c-1-2-3-3-4-4h-1v1 1l-1-1c-1-3-3-4-5-6z" class="H"></path><path d="M612 821l2-1h0c1 1 1 1 2 1 2 0 3 1 5 2 2 2 4 3 5 6l1 1v-1-1h1c1 1 3 2 4 4v2h0v3h1c1 3 1 5 1 7l-1 1c-3 0-4 1-6 3l-1 1-1 1c1 1 2 1 3 1l-1 1-1 1v1h2 1v2c-2-1-5-1-7 0l-2 1-2 1v1c-2 0-3 0-4 1h-2c0 1 1 2 3 3l-4 1v1l-1 1c-1 3-2 6-4 9h0-1c-1-1-1-1-2-1h0l-1-1c-1 0-2 0-3-1h-1l-1-1v-3h0l-1-1v-1c0-1-1-1-2-1v-3-3c0-3 3-8 5-9h1l1-1 1-1c2-1 2-2 3-3v-1h1c1-1 3-2 3-3-1-1-1-1-2-1v-2c0-1-2-2-3-2h-1c-1 1-3 1-5 1 0-1 1-2 1-4l-1-1h1l1-1c1 1 1 1 2 0 2 0 4 0 6-1l-1-1 1-1c1-1 1 0 1-1 1-1 1-2 2-3l1-1c-1-1-1-1-1-2h1z" class="k"></path><path d="M611 848c1 1 1 1 1 2s0 2 1 3l1 3v1c2 1 2 1 4 1v1c-2 0-3 0-4 1h-2l-3-2c1-1 1-1 2-1l-1-1h-1c-1-2-1-4-1-6 1-2 2-1 3-2z" class="d"></path><path d="M605 845v-1h1c0 1-1 3-2 4h2c0 1 0 2-1 3v1c1 1 0 2 1 3 0 1 2 2 3 3l3 2c0 1 1 2 3 3l-4 1v1l-1 1v-3c-1-4-4-6-7-8h-2v-1h1l-1-1h-1-1c1-1 2-1 4-1l-1-1v-1-2h0c2-1 2-2 3-3z" class="H"></path><path d="M611 848c-1-1-2-2-2-3 1-1 2-1 3 0 0 0 0 1 1 1 2 1 3 1 4 2s1 2 2 2v2c1 1 1 2 1 4l-2 2c-2 0-2 0-4-1v-1l-1-3c-1-1-1-2-1-3s0-1-1-2z" class="D"></path><path d="M619 852c1 1 1 2 1 4l-2 2c-2 0-2 0-4-1v-1l-1-3c2-1 3 1 5 1l1-2z" class="I"></path><path d="M632 837h1c1 3 1 5 1 7l-1 1c-3 0-4 1-6 3l-1 1-1 1c1 1 2 1 3 1l-1 1-1 1v1h2 1v2c-2-1-5-1-7 0l-2 1-2 1v1-1l2-2c0-2 0-3-1-4v-2s1 1 2 1h2 0c1 0 2-2 3-4v-4-1c1-1 0-1 1-1h4l1-1c-1 0-1-1-1-1 0-1 0-1 1-1v-1z" class="U"></path><path d="M617 847c0-1 1-1 0-2-1-3 0-8 0-11h9v7 1 1 4c-1 2-2 4-3 4h0-2c-1 0-2-1-2-1-1 0-1-1-2-2v-1z" class="C"></path><path d="M617 847c2 2 1 1 3 1l1 2h1l1-2c-1-1-1-2-2-3l1-1 2 1c0-1 1-1 2-2h0v4c-1 2-2 4-3 4h0-2c-1 0-2-1-2-1-1 0-1-1-2-2v-1z" class="W"></path><path d="M612 821l2-1h0c1 1 1 1 2 1 2 0 3 1 5 2 2 2 4 3 5 6l1 3c-3 1-5 0-7 1h-2-1 0c-1 1-2 1-3 1h-4c-1 1-3 0-4 0l-2 2h-1c-1 1-3 1-5 1 0-1 1-2 1-4l-1-1h1l1-1c1 1 1 1 2 0 2 0 4 0 6-1l-1-1 1-1c1-1 1 0 1-1 1-1 1-2 2-3l1-1c-1-1-1-1-1-2h1z" class="i"></path><path d="M598 832h1l1-1c1 1 1 1 2 0 2 2 5 2 7 2h8c-1 1-2 1-3 1h-4c-1 1-3 0-4 0l-2 2h-1c-1 1-3 1-5 1 0-1 1-2 1-4l-1-1z" class="H"></path><path d="M599 833l4 3c-1 1-3 1-5 1 0-1 1-2 1-4z" class="C"></path><path d="M621 823c2 2 4 3 5 6l1 3c-3 1-5 0-7 1l-3-2v-6c1-1 2-2 4-2z" class="T"></path><path d="M601 849l1-1h0v2 1l1 1c-2 0-3 0-4 1h1 1l1 1h-1v1h2c3 2 6 4 7 8v3c-1 3-2 6-4 9h0-1c-1-1-1-1-2-1h0l-1-1c-1 0-2 0-3-1h-1l-1-1v-3h0l-1-1v-1c0-1-1-1-2-1v-3-3c0-3 3-8 5-9h1l1-1z" class="O"></path><path d="M600 869c0-1-1-3-2-3-1-2-1-1-2-3l2-2v1 2h1v-1c1-1 1-1 2-1v3l2-2h0v-1h0c1 0 2 1 3 1v4l1 1-3 3c-2-1-3-1-4-2z" class="K"></path><path d="M607 868l1-2v-6c-1-1-3-3-5-3-2-1-3-1-4 0v1c-1 1-1 1-2 1 0-2 0-2 1-3l5-1c3 2 6 4 7 8v3c-1 3-2 6-4 9h0-1c-1-1-1-1-2-1h0l-1-1c-1 0-2 0-3-1h-1v-1h1c1 1 2 1 4 1h0c-1-1-2-1-4-2l1-1c1 1 2 1 4 2l3-3z" class="Z"></path><path d="M629 854l4 3 1 1c2 0 3 2 4 3l2-1 1 1s1 1 1 2c2 0 3-1 4-1v1c3 0 5 1 7 4l2 2c1 2 1 4 2 5v5l5 2h0l2 2 4 2h0v-1l2-2c2-1 5-2 8-1 2 0 5 2 7 3 1 2 1 2 2 2 3 0 3 0 6 1h1v1c1 0 2 1 4 1 0 1 1 1 2 2-2 0-3-1-5 0 1 0 3 1 3 2l-1-1c-3 0-7-1-10 1h-1c-1 0-3 1-5 2-2 0-2 0-3 2h-2c-1-1-1-1-2-1l-2-2c-1 1-1 1-2 1v2l1 1c-1 1 0 1-1 1l-3 1 2 1c2 0 2 1 3 2 1 2 1 3 1 5 2 0 3 1 5 2h1l2 2c0 2-1 4-1 7 1-1 2-1 3-1h0c2 0 4 1 6 1h1c1 1 1 1 1 2l-1 1h5c6-2 11-2 17-2h4c3-1 7 0 9-2 5 0 11 1 15-1h3v1c2 0 5 0 6 1h-5l-4 2c-1 0-2 1-3 1h0-1c0 1-1 1-1 1h0 1c5-1 11 0 16 0-2 1-4 1-6 1h-10v1l16 1c-6 1-12 0-18 1h-16l-5 1h-14-6-2v1h2 14c2 0 4 0 6 1h-2c-2 1-3 1-5 1h-12c-1-1-4-1-5-1-2-1-17 0-20 0-2 1-6 0-8 0h-3-4v1h9 24c3 0 7 1 10 1s11-1 12 0c-2 2-6 0-8 1h0c-2-1-6 0-8 0-3 0-7-1-10-1h-11c-4 0-18 1-20 0-3 0-10-1-12 0-1 0-3 1-4 0-2 0-4 0-5-1-2 0-5 0-6 1-3 0-8-1-10 0-2 0-5 0-7 1-2 0-5-1-6 0-2 0-4 0-5 1-2 0-5-1-6 0h-10c-2 0-4 1-5 0-2 0-4 0-5-1-3 0-9-1-11 1l-1-1v-1c-3 2-6 0-9 0h-12 0l2-1c2-1 4 0 6-1-1-1-13 0-15 0-1 1-3 0-4 0-1 1-2 1-3 1-1 1-3 0-5 0h-13c-1-1-8 0-9 0h-1-1 0c-2-1-8 0-10 0-2 1-9 0-11 0-1-1-3 0-5 0h-1v-1h-1-13c-1 0-3-1-4 0 1 1 4 1 5 1h1c1 0 2 0 3 1-4 0-9 0-13 1h0l1 1c-2 0-7 0-9-1h0l2-1h6v-1h-1c-1-1-74 0-76 0-4 1-10 1-14 1h-22c-2 0-6 1-7 0 3-3 18 0 23-1v-1c1 1 3 1 4 1h10c1-1 2-1 3-1h0-1-1-5c-2-1-4 0-6 0-4 0-9-1-13 0-2 1-6 0-7-1 1-1 9 0 11 0 1-1 2-1 4-1 3 0 9 1 12 0h-1v-1h-5c-5 0-10 0-15 1-4 1-9-1-12 0v1c-3 0-8 0-10-1h0l1-1c8 1 16 0 24 0 1-1 2 0 4-1 3 0 8 0 10-1h3c-2-1-4-1-6-1 1 0 1 0 1-1h0 18 0v-1c1 0 2 0 4-1h0-4c-1-2-1-3-3-4h1l1-1h4c2 0 5-1 7-1 2-1 3-1 4-3h0c-3 0-8 0-11-2v-2c-1 0-1-1-2-2l1-1v-1-4c0-2 0-5 1-7v-2l1-1 3-5c1-2 1-4 2-6 0-2 2-4 3-6h1c2-2 5-3 8-3h4l3 1 1-1h2c1 0 1-1 2-1h4l1-1-1-1c3-1 5 0 9 0h1l1 1v1c3 1 7 2 9 3l3 2c0-1 1-1 1-1l2-2h0-2c1-1 2-1 2-1 2-1 3-3 4-4 0-2-1-3-2-4h2c2 1 2 1 4 1l1-1 1 1h7 23c3 0 11-1 12 0h6c0 2-1 2-2 3 2 0 4 0 5 1 2-1 5-1 7-1 1 0 3-1 4-2 4-1 11-2 16 0 1 0 3 1 5 2l3 3 2 2 2 5v2 1h3c4-1 9-1 13-1h1l5-4c4-1 6 0 10 1l1-2v-1h2c3-1 5-1 8-1h1c3 0 6 1 9-1 2 1 4 2 6 4v-5-4c1 0 2 0 2 1v1l1 1h0v3l1 1h1c1 1 2 1 3 1l1 1h0c1 0 1 0 2 1h1 0c2-3 3-6 4-9l1-1v-1l4-1c-2-1-3-2-3-3h2c1-1 2-1 4-1v-1l2-1 2-1c2-1 5-1 7 0v-2z" class="J"></path><path d="M618 917l3 3v1c2-1 1 0 2-1h3 1c0 1 0 0 1 1h1c1-1 2-1 3-1l3-1 1 1h0v2h4v1h-5-15-9c1-1 1-1 2-1h1v-1c-1 0-1 0-2-1h3v-1c1-1 1-1 2-1l1-1z" class="K"></path><path d="M632 920l3-1 1 1h0v2h-1c-2-1-2-1-3-2z" class="j"></path><path d="M650 913h2 1c0 1 1 2 1 2 2 2 4 3 7 4l4 1v2c-3 1-6 1-9 1h-16v-1h-4v-2c1 0 1 1 2 1h0c2 0 4 1 5 0h1 2l1-1 2-2h1v-1c0-1 0-1-1-1l1-1h1 0l-2-2h1z" class="B"></path><path d="M644 910c2 0 5 1 6 2v1h-1l2 2h0-1l-1 1c1 0 1 0 1 1v1h-1l-2 2-1 1h-2-1c-1 1-3 0-5 0h0c-1 0-1-1-2-1h0l-1-1-3 1v-2l1-1c2-1 2-2 4-4 1 1 1 1 3 1l1-1c1-2 2-2 3-3z" class="P"></path><path d="M644 910c2 0 5 1 6 2v1h-1c0 1-1 1-2 1-2 1-4 0-6-1 1-2 2-2 3-3z" class="I"></path><path d="M559 924h1c2 1 2 1 4 1l-4-2 1-1c1 1 2 1 4 1h4c1 1 1 1 2 1v1c-2 0-2 0-4-1l-1 1h0c2 1 2 1 4 1 1 0 2 1 3 1-2 0-3 0-6 1-1 1-4 0-5 0-5 1-11 0-16 0-3 0-7 1-10 1-2 0-5 0-8 1-6 1-15 1-21 0h-4l-1-1v-1c2 0 4 0 6 1 8 1 16-1 24-1 3 0 8 0 10-1v-1h-4 2c6 0 12 1 17 0l2-2z" class="n"></path><path d="M460 924c1-2 3-3 5-3h0l1 1h-1c-1 0-3 1-3 2 0 0 1 0 2-1l1 1v-1c2-1 6 0 8 0l1 1h4 3 2l1-2h0v1l1 1h9 0c0 1-2 1-2 1l-1 1c-2 1-5 0-7 1h1-4c-3 1-7 1-11 0h-3l-1 1 1 1h0l-50-1 33-1v-1c2-1 6 0 8 0 0-1 0-1 1-2h1z" class="Z"></path><path d="M484 922h0v1l1 1h9 0c0 1-2 1-2 1l-1 1c-2 1-5 0-7 1h1-4c-3-2-5 0-8-2h0 6l-1-1h3 2l1-2z" class="U"></path><path d="M576 930h0v-1-1h10c1-1 9 0 12 0 1-1 3-1 5-1 1-1 3 0 5 0 2-2 9-1 12-1h1 21c4 0 8-1 12 0 2 1 4 0 6 1h10c2-1 4 0 5-1h1 1 5 13c4 1 8 1 12 0 8 0 15 0 23 1h-11-1l-5 1h-14c-4-1-8 0-12-1-4 0-9 0-13 1h-6-9c-3-1-8 0-11-1h0c-6 1-31-1-33 1-3 0-8-1-10 0-2 0-4 0-5 1-2 0-5-1-6 0-3 0-10 0-11 1h-7z" class="E"></path><path d="M620 909h2c3-2 6-2 9-1 1 1 2 1 3 2l3 3c-2 2-2 3-4 4l-1 1v2c-1 0-2 0-3 1h-1c-1-1-1 0-1-1h-1-3c-1 1 0 0-2 1v-1l-3-3c-1-1-1-2-1-4l1-1c0-1 1-2 2-3z" class="Z"></path><path d="M634 910l3 3c-2 2-2 3-4 4l-1 1v2c-1 0-2 0-3 1 0-2 0-3-2-4l2-1h2c2-1 2-3 3-5v-1z" class="l"></path><path d="M617 913c1 2 1 3 3 4 3 0 4 0 6 1l1-1c2 1 2 2 2 4h-1c-1-1-1 0-1-1h-1-3c-1 1 0 0-2 1v-1l-3-3c-1-1-1-2-1-4z" class="k"></path><path d="M620 909h2c3-2 6-2 9-1 1 1 2 1 3 2v1l-3-2c-1 1-1 0-2 1l2 1c0 1-2 2-3 3h-4l-2-2v1h-1l-1-1h-1-1c0-1 1-2 2-3z" class="R"></path><path d="M576 930h7c1-1 8-1 11-1 1-1 4 0 6 0 1-1 3-1 5-1 2-1 7 0 10 0 2-2 27 0 33-1h0c3 1 8 0 11 1h9 6c4-1 9-1 13-1 4 1 8 0 12 1h-6-2v1h2 14c2 0 4 0 6 1h-2c-2 1-3 1-5 1h-12c-1-1-4-1-5-1-2-1-17 0-20 0-2 1-6 0-8 0h-3c-3-3-9-2-12-2h-17-3c-2 1-8 0-10 0-2 1-6 1-7 1-2 1-7 0-8 0-2 1-4 1-5 1-2 1-7 0-9 0-2 1-8 1-10 1-1-1-3-1-4-1h-2v-1h3c1 1 1 1 2 1z" class="p"></path><path d="M588 921h1c0-2 0-2-1-4h0l1-1c2 0 3 0 5-1h0 0c1 3 0 6 0 9l17-1h9v1h34l-42 1c-13 1-26 3-39 2-1 0-2-1-3-1-2 0-2 0-4-1h0l1-1c2 1 2 1 4 1v-1l1-1c3 1 5 2 8 0l1 1h1c2-1 2-1 4-1l1 1c0-1 1-2 1-3z" class="P"></path><path d="M588 921h1c0-2 0-2-1-4h0l1-1c2 0 3 0 5-1h0 0c1 3 0 6 0 9l17-1h9v1l-13 1h-13v-1c-1 0-2-1-3-1s-2 1-3 2v-1h0v-1l-1 1c0-1 1-2 1-3z" class="D"></path><path d="M354 923h18 0 48 8 5c4 0 10 1 13 0h10l-1 1c1 0 2 0 3 1-2 1-6 0-8 0v1c-6 1-14 0-20 0h-15c-6 1-13 0-19 0l-25-1-1 1c-4-1-7-1-11-1-2-1-4-1-6-1 1 0 1 0 1-1h0z" class="b"></path><path d="M433 923c4 0 10 1 13 0h10l-1 1c1 0 2 0 3 1-2 1-6 0-8 0v1c-6 1-14 0-20 0h-15l-5-1 28-1h0l-10-1h0 5z" class="V"></path><path d="M578 905v-2h2s0-1 1-1c0-1 0 0 0 0 1 2 1 2 3 3h2 1 3c1 1 2 1 3 1v2c1 2 1 4 1 7h0c-2 1-3 1-5 1l-1 1h0c1 2 1 2 1 4h-1c0 1-1 2-1 3l-1-1c-2 0-2 0-4 1h-1l-1-1c-3 2-5 1-8 0v-18l2 1c2 0 3 0 4-1z" class="E"></path><path d="M582 912c0-2 0-3-1-5 1 0 2-1 4 0h0 5c1 0 2 0 3 1v5h-6c-1 1-2 0-3 0h-3l1-1z" class="G"></path><path d="M586 909h1 1v1l-1 2h-1l-1-1 1-2z" class="L"></path><path d="M578 905v-2h2s0-1 1-1c0-1 0 0 0 0 1 2 1 2 3 3-1 0-2 1-4 0l-1 1c2 1 2 2 2 4 0 1-1 2-1 3l1 1h0-6l-1 1c1 0 1 1 2 1 0 0-1 1-2 1 1 2 1 2 2 3h3c2 0 4-1 5-1h2c1 1 0 1 1 2h1c0 1-1 2-1 3l-1-1c-2 0-2 0-4 1h-1l-1-1c-3 2-5 1-8 0v-18l2 1c2 0 3 0 4-1z" class="I"></path><path d="M578 905v-2h2s0-1 1-1c0-1 0 0 0 0 1 2 1 2 3 3-1 0-2 1-4 0l-1 1c2 1 2 2 2 4 0 1-1 2-1 3h-5c-1-2-1-3-1-5v-1l3-1c0 1 1 1 2 1v-1l-1-1z" class="G"></path><path d="M574 908v-1l3-1 1 4c-1 1-1 1-2 1h-1v-3h-1z" class="L"></path><path d="M495 919l1-3h1l2-1v4h1l-1 1-1-1c-1 1-1 2-1 3l-1 2v1h2 2c3-1 6-2 9-2h13c2 0 8-1 9 0 1 0 2 1 2 2l2-1 1-1v1c3 0 11 1 12-1 3 1 8 1 11 1l-2 2c-5 1-11 0-17 0h-2 4v1c-2 1-7 1-10 1-8 0-16 2-24 1-2-1-4-1-6-1v1l1 1h-24l-2 1c-3-2-6 0-8-1-1 0-1-1-2-1h0l-1-1 1-1h3c4 1 8 1 11 0h4-1c2-1 5 0 7-1l1-1s2 0 2-1h1v-5z" class="g"></path><path d="M495 919l1-3h1l2-1v4h1l-1 1-1-1c-1 1-1 2-1 3l-1 2v1h2l-1 1h11v1h-23-1c2-1 5 0 7-1l1-1s2 0 2-1h1v-5z" class="H"></path><path d="M536 923v1c3 0 11 1 12-1 3 1 8 1 11 1l-2 2c-5 1-11 0-17 0h-2c-4 0-8-1-12-1-5 0-11 1-17 0h0c6 0 11-1 16-1 2 0 5 1 7 1h1l2-1 1-1z" class="P"></path><path d="M725 918c5 0 11 1 15-1h3v1c2 0 5 0 6 1h-5l-4 2c-1 0-2 1-3 1h0-1c0 1-1 1-1 1h0 1c5-1 11 0 16 0-2 1-4 1-6 1h-10c-2 1-4 1-5 1-9 1-18 0-27 0-3 0-7 1-10 0-1-1-4 0-5 0h-11c-1 0-2-1-3-1-2 0-6 1-8 0l-2-1h1l-1-1v-2h6 2 4c1 0 1 0 3-1h0c1-1 2-1 3-1h0c2 0 4 1 6 1h1c1 1 1 1 1 2l-1 1h5c6-2 11-2 17-2h4c3-1 7 0 9-2z" class="N"></path><path d="M725 918c5 0 11 1 15-1h3v1c2 0 5 0 6 1h-5c-2 0-5 0-7 1h-7-14c3-1 7 0 9-2z" class="d"></path><path d="M690 922h5c6-2 11-2 17-2v1c4 0 15-1 18 1h-10c-7 0-15 1-22 1-2 0-6 0-8-1h0z" class="R"></path><path d="M683 918c2 0 4 1 6 1h1c1 1 1 1 1 2l-1 1h0-3-14-5v1l-1 1-2-1h1l-1-1v-2h6 2 4c1 0 1 0 3-1h0c1-1 2-1 3-1h0z" class="H"></path><path d="M683 918c2 0 4 1 6 1h1c1 1 1 1 1 2l-1 1h0-3v-1c-2 0-4 1-5 0-1 0-1-1-1-1v-1l2-1h0z" class="M"></path><path d="M557 874c4-1 6 0 10 1l3 3c3 5 2 11 2 17 0 3 1 7 0 10v18l-1 1c-1 0-1 0-2-1h-4c-2 0-3 0-4-1l-1 1 4 2c-2 0-2 0-4-1h-1c-3 0-8 0-11-1-1 2-9 1-12 1v-1-1-12l-1-5v-26h3c4-1 9-1 13-1h1l5-4z" class="B"></path><path d="M555 900l2 1 1-1c0 2 0 6-1 8l1 1c0 1 1 1 1 2 0 0-1 0-1 1v3 1h-1l1-1c-1-1-1-1-1-2v-1l-1-1c-1-1-1-1-1-2v-9z" class="F"></path><path d="M555 900v-5c0-4 0-9-1-13h0l1-2c2 0 3 1 4 2l1 1v-1c0-2 1-4 2-5h3c0 2 1 3 1 5-2-1-3-1-5 0 0 1-1 2-2 4l1 1h0-1v-2l-1-1c-1 2-1 2-1 4v1 4c1 1 1 0 1 2h-1l1 1v1l-1 2h-1l-1 1z" class="M"></path><path d="M555 900l1-1h1l1-2v-1l-1-1h1c0-2 0-1-1-2v-4-1c0-2 0-2 1-4l1 1v2h1 0l-1-1c1-2 2-3 2-4 2-1 3-1 5 0l-1 34c-1-1-1-1-1-2-1 1-1 1-2 1-1-2-1-6-1-8h0c0 2 0 3-1 4h-1v-3c2-5 1-11 1-16l-1-1c0 5 1 10 0 15l-1 3-1-1c1-2 1-6 1-8l-1 1-2-1z" class="O"></path><path d="M535 905h13v5 10 3c-1 2-9 1-12 1v-1-1-12l-1-5z" class="E"></path><path d="M536 913h10c0 1 0 2-1 3l-1 1 2 1h-8c-1 0-1 0-1-1h0l2-1c-1-1-2-1-3-1v-2z" class="J"></path><path d="M536 910v3 2c0 2 0 4 1 5h11v3c-1 2-9 1-12 1v-1-1-12z" class="R"></path><path d="M535 905h13v5c-1-1-2-1-4 0h0c1 1 1 1 3 1v1l-1 1h-10v-3l-1-5z" class="G"></path><path d="M565 877l1-1c2 1 3 2 4 2 3 5 2 11 2 17 0 3 1 7 0 10v18l-1 1c-1 0-1 0-2-1h1c-1-1-3-2-5-3l-1-1h-2l-1-1h-1v1l-1 1v-2c-2-1-3-1-5-1h2l1-1c1 1 2 0 3 0l1-2v2h2 0c0 1 0 0-1 1h-1 1c1 0 2 0 3-1l1-34c0-2-1-3-1-5z" class="j"></path><path d="M535 879h3c4-1 9-1 13-1-1 3-2 7-3 10v2c1 5 0 10 0 15h-13v-26z" class="L"></path><defs><linearGradient id="G" x1="590.466" y1="904.887" x2="607.301" y2="900.427" xlink:href="#B"><stop offset="0" stop-color="#030301"></stop><stop offset="1" stop-color="#242324"></stop></linearGradient></defs><path fill="url(#G)" d="M594 865c1 0 2 0 2 1v1l1 1h0v3l1 1h1c1 1 2 1 3 1l1 1h0c1 0 1 0 2 1h1 0 2 0v-1l1-1h1l2 2h1c1 0 2-1 3 0l3 3h0c1-1 1 0 2-1v2c1 0 2-1 2-1v2 1 1l-5 1-1 2c-1 0-2 0-2 1l2 2c0 1 0 1 1 2l1 1c-1 1-3 2-3 3h-4c-2 0-3 0-4 1h-1l-3 1c1 1 1 1 2 1 1-1 2 0 3 0l1 2-1 1h1c0 1 1 1 1 2l-1 2 1 1v1h-2l-1 1c1 0 1 0 2 1 0 1 0 1 1 2h7c1-1 1-1 2-1-1 1-2 2-2 3l-1 1c0 2 0 3 1 4l-1 1c-1 0-1 0-2 1v1h-3c1 1 1 1 2 1v1h-1c-1 0-1 0-2 1l-17 1c0-3 1-6 0-9h0c0-3 0-5-1-7v-2c-1 0-2 0-3-1h-3-1-2c-2-1-2-1-3-3 0 0 0-1 0 0-1 0-1 1-1 1h-2v2c-1 1-2 1-4 1l-2-1c1-3 0-7 0-10 0-6 1-12-2-17l-3-3 1-2v-1h2c3-1 5-1 8-1h1c3 0 6 1 9-1 2 1 4 2 6 4v-5-4z"></path><path d="M610 910l2 1-2 1h-6l1-1c1 0 2 0 3-1h2z" class="H"></path><path d="M601 910h7c-1 1-2 1-3 1l-1 1c-1 2-1 2-3 2l-2 2h0-1v-1c1-1 2-1 3-1 0-1 1-1 1-2h0l-1-2z" class="S"></path><path d="M601 914c-1-1-1 0-1-1l-1-1c0-1-1-2-1-4s1-3 2-4c1 1 1 1 2 1 0 1-1 2-2 2v1h1 1v1h-3l1 1h1l1 2h0c0 1-1 1-1 2z" class="B"></path><path d="M600 902l-1 1-1-1v-1h0v-3c1-1 1-2 1-3h0l-1-1-1-1v-1l1-1 1 1c1 0 2 0 3 1 0 1 0 1 1 1h3 1v1l-3 1-3 3-1 2c1 0 0 0 1 1h-1z" class="K"></path><path d="M618 910c1-1 1-1 2-1-1 1-2 2-2 3l-1 1c0 2 0 3 1 4l-1 1c-1 0-1 0-2 1h0c-1-2-2-3-4-5v1h-1-2 0-1l1-2h3l-1-1 2-1-2-1h1 7z" class="M"></path><path d="M610 910h1 7c-1 1-2 2-4 2-1 0-2 0-2-1l-2-1z" class="Y"></path><path d="M599 885h3 7 0l-1 1v1c1 1 1 2 1 3l-1-1h-1v2h-3l-2 2c-1-1-2-1-3-1l-1-1h5v-1h-3c-2-1-1 0-2-1h-1 2l1-1h-1l-1-1-1-1h3l-1-1z" class="Z"></path><path d="M609 885h0l-1 1v1c1 1 1 2 1 3l-1-1h-1l-1-1c-1 1-2 1-3 1v-1h1c-1-1-1-2-2-3h7z" class="n"></path><path d="M593 895c0-3-1-10 0-13v24c-1 0-2 0-3-1h-3-1v-5h0v-2c2 0 3-1 5 0h2v-3z" class="X"></path><path d="M586 905v-5h0v-2c2 0 3-1 5 0l-1 1c1 0 2 0 3 1v1l-1 1c-2 0-2 0-3-1-2 1-1 2-2 4h-1z" class="T"></path><path d="M611 883h1v3l1 1 1-1h1l2 2c0 1 0 1 1 2l1 1c-1 1-3 2-3 3h-4c-2 0-3 0-4 1h-1v-1h-1-3c-1 0-1 0-1-1l2-2h3v-2h1l1 1c0-1 0-2-1-3v-1l1-1 2-2z" class="H"></path><path d="M612 889h1 1v1l2 1h0v2h-2c0-1 0-1-1-1l-1-1h-1v1h-1c0-2 1-2 2-3z" class="P"></path><path d="M601 899l3-3c1 1 1 1 2 1 1-1 2 0 3 0l1 2-1 1h1c0 1 1 1 1 2l-1 2 1 1v1h-2l-1 1c1 0 1 0 2 1 0 1 0 1 1 2h-1-2-7-1l-1-1h3v-1h-1-1v-1c1 0 2-1 2-2-1 0-1 0-2-1 1 0 1 0 2-1l-2-1h1c-1-1 0-1-1-1l1-2z" class="V"></path><path d="M601 899l3-3c1 1 1 1 2 1 1-1 2 0 3 0l1 2-1 1h1c0 1 1 1 1 2l-1 2 1 1v1h-2c1-1 1 0 1-1-1 0-2 1-4 1v-1c1 0 2 0 3-1l-1-1h-4l1-1c0-1 0-1 1-2 1 0 1 0 1-1-2 0-2 2-4 2 0-1 1-2 1-3h0l-2 2-1-1zm-7-34c1 0 2 0 2 1v1l1 1h0v3l1 1h1c1 1 2 1 3 1l1 1h0c1 0 1 0 2 1h1 0 2 0v-1l1-1h1l2 2h1c1 0 2-1 3 0l3 3h0c1-1 1 0 2-1v2c1 0 2-1 2-1v2 1 1l-5 1-1 2c-1 0-2 0-2 1h-1l-1 1-1-1v-3h-1l-2 2h0-7-3-2v-1c1 0 2 0 3-1h-3v-2c-1 0-1-1-2-1l-1 1c-1-2 0-5 0-7v-5-4z" class="l"></path><path d="M604 878c1 0 3 1 5 0 1 1 1 0 2 1v1h3l-1 2c-1 0-1 0-2 1l-2 2h0c-1-2-2-3-3-5v-1c-1 0-2 0-2-1z" class="m"></path><path d="M606 875h2l3 1v3c-1-1-1 0-2-1-2 1-4 0-5 0s-2-1-2-3h3 1 0z" class="d"></path><path d="M608 875h0v-1l1-1h1l2 2h1c1 0 2-1 3 0l3 3h0c0 1-1 2-1 2-1 0-1 0-2 1l-2 1h-1l1-2h-3v-1-3l-3-1z" class="H"></path><path d="M611 876c2 1 3 1 4 3l-1 1h-3v-1-3z" class="W"></path><path d="M619 878c1-1 1 0 2-1v2c1 0 2-1 2-1v2 1 1l-5 1-1 2c-1 0-2 0-2 1h-1l-1 1-1-1v-3h-1c1-1 1-1 2-1h1l2-1c1-1 1-1 2-1 0 0 1-1 1-2z" class="a"></path><path d="M594 865c1 0 2 0 2 1v1l1 1h0v3l1 1h1l-2 1 1 1h2l-1 1h-1c0 1 0 2 1 3-1 0-1-1-2-1v1l2 1h0c1-1 2-1 3 0l-2 2h0-3c-1 0-1-1-2-1l-1 1c-1-2 0-5 0-7v-5-4z" class="F"></path><path d="M579 871c3 0 6 1 9-1 2 1 4 2 6 4 0 2-1 5 0 7l-1 1c-1 3 0 10 0 13v3h-2c-2-1-3 0-5 0v2h0v5h-2c-2-1-2-1-3-3 0 0 0-1 0 0-1 0-1 1-1 1h-2v2c-1 1-2 1-4 1l-2-1c1-3 0-7 0-10 0-6 1-12-2-17l-3-3 1-2v-1h2c3-1 5-1 8-1h1z" class="c"></path><g class="f"><path d="M577 898h-2c0-1-1-2 0-2v-3c-1-1-1-2-1-3 1-1 3-1 4-1 0 2 0 5-1 7v2zm6-26h6l2 3c1 1 1 1 1 3h-2c-3 0-4 0-6-1l-1 1h-1c0-3 0-4 1-6z"></path><path d="M578 871h1c0 2 0 3-1 6h1l1-1v2h-1c-2 0-3 0-5 1h-1l-3-4c0-1-1-1-2-1v-1-1h2c3-1 5-1 8-1z"></path><path d="M575 879h16v2 6 1l-17-1v-6-1l1-1z"></path></g><path d="M578 889h15c0 2-1 5 0 6v3h-2c-2-1-3 0-5 0v2h0v5h-2c-2-1-2-1-3-3 0 0 0-1 0 0-1 0-1 1-1 1h-2v2c-1 1-2 1-4 1 0-1 0-2 1-2v-4-1h1 3c2-1 5 0 7-1-1-1-1 0-3 0-2-1-4 0-6 0v-2c1-2 1-5 1-7z" class="L"></path><path d="M507 864c4-1 11-2 16 0 1 0 3 1 5 2l3 3 2 2 2 5v2 1 26l1 5v12 1l-1 1-2 1c0-1-1-2-2-2-1-1-7 0-9 0h-13c-3 0-6 1-9 2h-2-2v-1l1-2c0-1 0-2 1-3l1 1 1-1h-1v-4l-2 1h-1l-1 3v-6-5-2-3-2c1-2 2-1 3-2v-12c-1-1-1-2-1-2l1-1h-1v-4l1 1h0l1-1c-1-1 0-2-1-3 0-1 0-1-1-2l1-3c2-3 3-4 5-6 1 0 3-1 4-2z" class="j"></path><path d="M508 892c1 2 0 8 1 10h4v1h-4l-1 6c3 0 5 1 7 0 1 0 2 1 3 1h1 2c1 1 2 0 3 0-1 0-2-1-3-1h0 4v1l-1 1h2l1-2v-1c-4 0-7 0-10-1v-1l1-1 1 1 2-1c2 1 4 1 5 1s2 0 2-1c0-2 0-4 1-6 0 5-1 11 1 16h0c0 1 1 1 1 2l1 1h1v3c1 0 2 1 3 1v1l-1 1-2 1c0-1-1-2-2-2-1-1-7 0-9 0h-13c-3 0-6 1-9 2h-2-2v-1l1-2c0-1 0-2 1-3l1 1 1-1c0-1 0-2 1-3v1l1 1v-2-1h2 0 2c0-2 0-2-1-3h1l-1-8c1 1 1 1 2 1v-2h1v-11z" class="B"></path><g class="V"><path d="M505 904c1 1 1 1 2 1v-2h1v8c-1 0-1 0-2 1l-1-8z"></path><path d="M499 880h0v-1c1-5 7-10 11-12h1c1 0 2 1 3 2 1 0 1 0 2-1h1c-1 2-4 3-5 3-2 1-4 3-5 5h1l1-1c2-2 5-4 8-4l-1 1c-3 2-5 4-6 8s-2 8-2 12v11h-1v2c-1 0-1 0-2-1l1 8h-1c1 1 1 1 1 3h-2 0-2v1 2l-1-1v-1c-1 1-1 2-1 3h-1v-4l-2 1h-1l-1 3v-6-5-2-3-2c1-2 2-1 3-2v-12c-1-1-1-2-1-2l1-1h-1v-4l1 1h0l1-1z"></path></g><path d="M500 908l1-11c2 2-1 6 1 8l1 1c0 2-1 6 0 8l-1 1h-1c0-3 1-6 0-8l-1 1z" class="U"></path><path d="M503 906v-17c0-4 0-8 2-10v-1l2-2h1c-1 2-2 3-3 5v1c-1 6-1 15 0 22l1 8h-1c1 1 1 1 1 3h-2 0-2l1-1c-1-2 0-6 0-8z" class="K"></path><path d="M498 884h2c-1 1-1 1-1 2v1h2l1 1c-1 1-1 1-3 1-1 8 0 17 0 25h1v-6l1-1c1 2 0 5 0 8h1v1 2l-1-1v-1c-1 1-1 2-1 3h-1v-4l-2 1h-1l-1 3v-6-5-2-3-2c1-2 2-1 3-2v-12c-1-1-1-2-1-2l1-1z" class="Y"></path><path d="M508 876l1-1c2-2 5-4 8-4l-1 1c-3 2-5 4-6 8s-2 8-2 12v11h-1v2c-1 0-1 0-2-1-1-7-1-16 0-22v-1c1-2 2-3 3-5z" class="Z"></path><defs><linearGradient id="H" x1="524.675" y1="891.963" x2="532.791" y2="889.865" xlink:href="#B"><stop offset="0" stop-color="#4b4b4b"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#H)" d="M507 864c4-1 11-2 16 0 1 0 3 1 5 2l3 3 2 2 2 5v2 1 26l1 5v12c-1 0-2-1-3-1v-3h-1l-1-1c0-1-1-1-1-2h0c-2-5-1-11-1-16v-5c-1-5 0-13-3-17-1-2-4-4-7-5h-3l1-1c-3 0-6 2-8 4l-1 1h-1c1-2 3-4 5-5 1 0 4-1 5-3h-1c-1 1-1 1-2 1-1-1-2-2-3-2h-1c-4 2-10 7-11 12v1h0c-1-1 0-2-1-3 0-1 0-1-1-2l1-3c2-3 3-4 5-6 1 0 3-1 4-2z"></path><defs><linearGradient id="I" x1="542.645" y1="914.642" x2="525.657" y2="888.817" xlink:href="#B"><stop offset="0" stop-color="#211f1d"></stop><stop offset="1" stop-color="#36383a"></stop></linearGradient></defs><path fill="url(#I)" d="M533 886c1-2 1-2 2-3v-4 26l1 5v12c-1 0-2-1-3-1v-3h-1l-1-1c1-2 1-5 1-8v-22l1-1z"></path><defs><linearGradient id="J" x1="513.484" y1="862.793" x2="518.567" y2="885.896" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#393939"></stop></linearGradient></defs><path fill="url(#J)" d="M507 864c4-1 11-2 16 0 1 0 3 1 5 2l3 3 2 2 2 5v2 1 4c-1 1-1 1-2 3l-1 1c0-3 0-5-1-8-1-4-6-9-10-11l-1 1c-2-1-2-1-3-1h-1c-1 1-1 1-2 1-1-1-2-2-3-2h-1c-4 2-10 7-11 12v1h0c-1-1 0-2-1-3 0-1 0-1-1-2l1-3c2-3 3-4 5-6 1 0 3-1 4-2z"></path><path d="M510 867c5-2 9-1 14 1 2 2 8 8 9 11v1 1 5l-1 1c0-3 0-5-1-8-1-4-6-9-10-11l-1 1c-2-1-2-1-3-1h-1c-1 1-1 1-2 1-1-1-2-2-3-2h-1z" class="Y"></path><path d="M445 863h7 23c3 0 11-1 12 0h6c0 2-1 2-2 3 2 0 4 0 5 1 2-1 5-1 7-1-2 2-3 3-5 6l-1 3c1 1 1 1 1 2 1 1 0 2 1 3l-1 1h0l-1-1v4h1l-1 1s0 1 1 2v12c-1 1-2 0-3 2v2 3 2 5 6 5h-1 0-9l-1-1v-1h0l-1 2h-2-3-4l-1-1c-2 0-6-1-8 0v1l-1-1c-1 1-2 1-2 1 0-1 2-2 3-2h1l-1-1h0c-2 0-4 1-5 3h-1c-1 1-1 1-1 2-2 0-6-1-8 0h0v-1c2 0 6 1 8 0-1-1-2-1-3-1l1-1 1-1v-5-1-6-26l-1 3h-8v-1-1c0-2 0-4 1-6-1 1-2 1-2 1l-1-1-1-1v-9-3h1l-1-3z" class="B"></path><path d="M457 917h1v-4c1-2 1-3 1-4v-1c1 2 0 3 0 5l-1 1c1 1 2 2 2 4 0 1-1 1-1 2s0 2 1 4h-1c-1 1-1 1-1 2-2 0-6-1-8 0h0v-1c2 0 6 1 8 0-1-1-2-1-3-1l1-1 1-1v-5z" class="M"></path><path d="M465 898h6c1 1 1 2 0 3 1 3 1 8 0 12h0v-4c-2 2 0 4-2 5h-1-2v2h-1l-2-2c0-3-1-8 0-12 1-1 1-2 2-4z" class="Y"></path><path d="M466 899l1 1c0 3 1 7-1 9h0l-1-8v-1l1-1z" class="V"></path><path d="M466 899c1 0 1-1 2 0h2v1s0 1 1 2v-1c1 3 1 8 0 12h0v-4c-2 2 0 4-2 5l-1-1h1l-1-1v-2h1l-1-1c0-2 1-7 0-9h-1l-1-1z" class="U"></path><path d="M484 906h3c2 1 6 0 8 0v2 5 6 5h-1 0-9l-1-1v-1h0v-5-5-6z" class="I"></path><path d="M484 917h0c1 2 2 1 3 3 0 0-2 1-3 2h0v-5z" class="C"></path><path d="M484 923c1-1 2-1 3-1h7v2h-9l-1-1z" class="b"></path><path d="M484 912v1h2 9v6 5h-1 0v-2c0-2 1-3-1-5 0 1-1 1-2 2-1-1-1-1-2-1l-5-1h0v-5z" class="N"></path><path d="M484 912v1l2 1h3c-1 2 0 2 0 4l-5-1h0v-5z" class="J"></path><path d="M484 906h3c2 1 6 0 8 0v2 5h-9-2v-1-6z" class="L"></path><path d="M484 906h3c2 1 6 0 8 0v2c-1 0-3 0-4-1h-5v6h-2v-1-6z" class="c"></path><path d="M466 874c2-1 4-1 5-1-1 1-2 1-3 3 1 1 1 1 1 2 1 1 1 2 1 3l1 1v-1c1 0 2 0 3-1-1-1-1-1 0-2h1v4h0v35h0l-1-1v-1-1-3l-1-1v5c-1 0-1 0-1-1l-1-1c1-4 1-9 0-12 1-1 1-2 0-3h-6c-1 2-1 3-2 4v-1h-1c0 2 1 5 0 7 0 2 0 4-1 6-1-3 1-6 0-8-1-1-1 0-1-1v-1c0 1 0 2-1 4v1c0 1 0 2-1 4v4h-1v-1-6-26c0-1 1-2 1-3l1-2 2-2v-1c2-1 3-1 5-2z" class="V"></path><path d="M465 889l2 2-1 1 1 1h2c0 1 1 1 2 1v-2h2c1 3 0 10 0 14v4 5c-1 0-1 0-1-1l-1-1c1-4 1-9 0-12 1-1 1-2 0-3h-6v-9z" class="Z"></path><path d="M466 874c2-1 4-1 5-1-1 1-2 1-3 3 1 1 1 1 1 2 1 1 1 2 1 3l1 1v-1c1 0 2 0 3-1-1-1-1-1 0-2h1v4h0 0l-1 1 1 1h-1v3l-1-1v-4h-1l-1 4h0c-1-1-1-2-1-3h-1v2h-1v-2c-2 1-1 2-1 3l-1 1c-1 0-1 1-1 2v9c-1 2-1 3-2 4v-1h-1c0 2 1 5 0 7h0v-11c2-3-1-10 0-14h1c0-1 0-1-1-2h-1 0c1-3 2-4 4-5 1 0 1-1 1-2z" class="K"></path><path d="M464 880l1-2h3v3h-1l-3-1z" class="H"></path><path d="M464 880l3 1c0 1 0 2-1 3-1 0-2 0-3 1 0-2 1-3 1-5z" class="a"></path><path d="M467 881h1v2c-2 1-1 2-1 3l-1 1c-1 0-1 1-1 2v9c-1 2-1 3-2 4v-1-16c1-1 2-1 3-1 1-1 1-2 1-3z" class="U"></path><path d="M461 876c2-1 3-1 5-2 0 1 0 2-1 2-2 1-3 2-4 5h0 1c1 1 1 1 1 2h-1c-1 4 2 11 0 14v11h0c0 2 0 4-1 6-1-3 1-6 0-8-1-1-1 0-1-1v-1c0 1 0 2-1 4v1c0 1 0 2-1 4v4h-1v-1-6-26c0-1 1-2 1-3l1-2 2-2v-1z" class="F"></path><path d="M445 863h7 23c3 0 11-1 12 0h6c0 2-1 2-2 3 2 0 4 0 5 1 2-1 5-1 7-1-2 2-3 3-5 6l-1 3c1 1 1 1 1 2 1 1 0 2 1 3l-1 1h0l-1-1v4h1l-1 1s0 1 1 2v12c-1 1-2 0-3 2v2 3c-2 0-6 1-8 0h-3l-1-5v-4-11c0-2 0-3-1-5-2-5-7-6-11-8-1 0-3 0-5 1s-3 1-5 2v1l-2 2-1 2c0 1-1 2-1 3l-1 3h-8v-1-1c0-2 0-4 1-6-1 1-2 1-2 1l-1-1-1-1v-9-3h1l-1-3z" class="I"></path><path d="M452 878h5c1 0 2 0 2 1l-1 2c-1-1-3-1-5-1l-1-2z" class="J"></path><path d="M449 873v-1c1-1 5-1 6-1s1 1 2 1h2 1c1 1 1 1 2 1v1h0l-1 1v1 1l-4-1h-2-4l-1-1-3 3-1-1 3-3h0v-1z" class="T"></path><path d="M449 879c1 0 2-1 3-1l1 2c2 0 4 0 5 1 0 1-1 2-1 3l-1 3h-8v-1-1c0-2 0-4 1-6z" class="G"></path><path d="M448 885l1-4h1c1 1 1 2 1 4-1 1-2 0-3 1v-1z" class="L"></path><path d="M449 873c-1-1-1-1-2-1v-1h2c0-1 0-1 1-2s2-1 4-1h1c3-1 6 1 8 0 0-1 3 0 4 0h0l1 2c-1 0-2 1-3 1h-1c-1 0-3 1-5 1h-2c-1 0-1-1-2-1s-5 0-6 1v1z" class="J"></path><path d="M445 863h7 23c3 0 11-1 12 0h6c0 2-1 2-2 3-1 0-3 0-4 1h-1c0-1-1-1-2-1-2 0-3 0-5 2-1-2-2-2-4-3-2 0-4 1-6 0h-2l-13 1c-2 0-5-1-7 0v1l-2 2v-3h1l-1-3z" class="Z"></path><path d="M445 863h7v2h-6v1l-1-3z" class="m"></path><path d="M487 863h6c0 2-1 2-2 3-1 0-3 0-4 1h-1c0-1-1-1-2-1-2 0-3 0-5 2-1-2-2-2-4-3v-1h4 0 1c2-1 4 0 7-1z" class="R"></path><path d="M491 866c2 0 4 0 5 1 2-1 5-1 7-1-2 2-3 3-5 6l-1 3-1 2c-1-1-1-1-3-2h-13c1 1 1 3 3 4 1 0 2 0 3 1-1 1-2 0-3 1v1l1 1 1 1h2l-1 1c-1 0-1 0-3 1 0-2 0-3-1-5-2-5-7-6-11-8-1 0-3 0-5 1s-3 1-5 2v-1l1-1h0v-1c-1 0-1 0-2-1h-1c2 0 4-1 5-1h1c1 0 2-1 3-1l-1-2c2 1 2 0 3-1 1 0 1 1 1 1 1 0 1-1 2-1s1 0 2 1l-1 1h0l3-1c0 1 0 0 1 1l1-1c2-2 3-2 5-2 1 0 2 0 2 1h1c1-1 3-1 4-1z" class="G"></path><path d="M479 868c2-2 3-2 5-2 1 0 2 0 2 1h1c0 2 0 2-1 4h-2-4c-1-1-2 0-3-1h0l-2 1c-1 0-2-1-3-1-2 1-2 0-4 0l-1-2c2 1 2 0 3-1 1 0 1 1 1 1 1 0 1-1 2-1s1 0 2 1l-1 1h0l3-1c0 1 0 0 1 1l1-1z" class="N"></path><path d="M486 867h1c0 2 0 2-1 4h-2v-1c1-1 2-2 2-3z" class="J"></path><path d="M491 866c2 0 4 0 5 1 2-1 5-1 7-1-2 2-3 3-5 6-1-2-9-1-12-1 1-2 1-2 1-4 1-1 3-1 4-1z" class="E"></path><path d="M483 886c2-1 2-1 3-1l1-1h-2l-1-1-1-1v-1c1-1 2 0 3-1-1-1-2-1-3-1-2-1-2-3-3-4h13c2 1 2 1 3 2l1-2c1 1 1 1 1 2 1 1 0 2 1 3l-1 1h0l-1-1v4h1l-1 1s0 1 1 2v12c-1 1-2 0-3 2v2 3c-2 0-6 1-8 0h-3l-1-5v-4-11z" class="f"></path><path d="M496 877l1-2c1 1 1 1 1 2 1 1 0 2 1 3l-1 1h0l-1-1v4h1l-1 1s0 1 1 2v12c-1 1-2 0-3 2v-1-1-1c0-6 0-15 1-21z" class="U"></path><path d="M483 897c4 1 8 0 12 1v1 1 1 2 3c-2 0-6 1-8 0h-3l-1-5v-4z" class="c"></path><path d="M495 900c-3 1-7 0-10-1h-1c2-2 9 0 11 0v1z" class="o"></path><path d="M483 901l12 2v3c-2 0-6 1-8 0h-3l-1-5z" class="L"></path><path d="M629 854l4 3 1 1c2 0 3 2 4 3l2-1 1 1s1 1 1 2c2 0 3-1 4-1v1c3 0 5 1 7 4l2 2c1 2 1 4 2 5v5l5 2h0l2 2 4 2h0v-1l2-2c2-1 5-2 8-1 2 0 5 2 7 3 1 2 1 2 2 2 3 0 3 0 6 1h1v1c1 0 2 1 4 1 0 1 1 1 2 2-2 0-3-1-5 0 1 0 3 1 3 2l-1-1c-3 0-7-1-10 1h-1c-1 0-3 1-5 2-2 0-2 0-3 2h-2c-1-1-1-1-2-1l-2-2c-1 1-1 1-2 1v2l1 1c-1 1 0 1-1 1l-3 1 2 1c2 0 2 1 3 2 1 2 1 3 1 5 2 0 3 1 5 2h1l2 2c0 2-1 4-1 7h0c-2 1-2 1-3 1h-4-2-6l-4-1c-3-1-5-2-7-4 0 0-1-1-1-2h-1-2v-1c-1-1-4-2-6-2-1 1-2 1-3 3l-1 1c-2 0-2 0-3-1l-3-3c-1-1-2-1-3-2-3-1-6-1-9 1h-2c-1 0-1 0-2 1h-7c-1-1-1-1-1-2-1-1-1-1-2-1l1-1h2v-1l-1-1 1-2c0-1-1-1-1-2h-1l1-1-1-2c-1 0-2-1-3 0-1 0-1 0-2-1l3-1h1c1-1 2-1 4-1h4c0-1 2-2 3-3l-1-1c-1-1-1-1-1-2l-2-2c0-1 1-1 2-1l1-2 5-1v-1-1-2s-1 1-2 1v-2c-1 1-1 0-2 1h0l-3-3c-1-1-2 0-3 0h-1l-2-2h-1l-1 1v1h0-2c2-3 3-6 4-9l1-1v-1l4-1c-2-1-3-2-3-3h2c1-1 2-1 4-1v-1l2-1 2-1c2-1 5-1 7 0v-2z" class="D"></path><path d="M620 904h1c0-1 1-1 1-1 2 0 4-1 5 0 1 0 3 0 4-1 2-1 6 0 7-2 3 0 6-2 10-1-1 0-3 2-4 2h-2c1 1 1 1 2 1l-1 1h-2v1h-3l-1-1c-1 1-1 1-2 1h-3-3-3c-3 1-4 1-6 0zm16-15h0 3v1c-1 1-1 1-3 1v3c0 1-1 1-1 3h0l1-1h2l1-1 1 1-1 1h0c-1 0-2 1-3 1-2 0-3 1-4 2-2 1-3 0-5 0h-1-1l-1-1v1h-1v-1c0-1-1-2-1-3h4 4c2 0 3-1 4-2v-1c-1 0-2 1-3 1 0-1 0-1 1-1v-1c2 0 3-1 4-3z" class="h"></path><path d="M622 896h4c1 1 1 2 2 2-1 1-1 1-2 1l-1 1-1-1v1h-1v-1c0-1-1-2-1-3z" class="b"></path><path d="M620 904c2 1 3 1 6 0h3 3 3c1 0 1 0 2-1l1 1c-1 1-1 2-2 3-1-1-2-1-3 0h-1-1v1c-3-1-6-1-9 1h-2c-1 0-1 0-2 1h-7c-1-1-1-1-1-2-1-1-1-1-2-1l1-1h2 0 3c3 0 4 0 6-2z" class="d"></path><path d="M611 910c-1-1-1-1-1-2-1-1-1-1-2-1l1-1h2 0c2 1 3 2 5 2s4-1 5-2h1c2 1 4 0 7 0l3 1h-1v1c-3-1-6-1-9 1h-2c-1 0-1 0-2 1h-7z" class="Z"></path><path d="M637 881c1 0 2 2 3 3 1 0 1 0 2 1-2 1-3 2-4 3h1c1 0 1-1 2-2l1 1-1 1h2l-1 2 1 1 1 1c0-1 1-1 2-2h-1l1-1c1 0 1 1 2 0h2l1-1h0c1 2 2 2 3 3l2 1c1 0 1 0 2-1 1 0 2 1 3 1h1 1l-3 3h0l-1-1h-2c-1 0-2 1-3 1h-4v-1c1-1 1-1 2-3h0c-1 0-1 0-2 1h-1 0-2c0 1 0 1-1 1-1 1 0 1-1 1l-1-1v-1l-1 1c-3-2-4 0-6 1h-1v-3c2 0 2 0 3-1v-1h-3 0l1-3h1v-1l-2-2 1-2z" class="I"></path><path d="M612 894h4c1 1 4 1 6 2 0 1 1 2 1 3v1c-1 1-2 1-3 0-2 0-4 0-6 1 0 0-1 0-1 1v2h1c2-1 3 0 5-1v1h1c-2 2-3 2-6 2h-3 0v-1l-1-1 1-2c0-1-1-1-1-2h-1l1-1-1-2c-1 0-2-1-3 0-1 0-1 0-2-1l3-1h1c1-1 2-1 4-1z" class="h"></path><path d="M611 896h5l1 1c-2 1-3 1-4 2v1c-1-1-1-2-1-3l-2 1v-1l1-1z" class="I"></path><path d="M612 894h4c1 1 4 1 6 2 0 1 1 2 1 3-1 0-2-1-4-1-1 0-1 0-2-1l-1-1h-5l1-2z" class="E"></path><path d="M623 882h5c2 1 3 0 4 2l1 1 1 1-1 1s-1 0-1 1v2h1c0 1 0 1-1 2h0v1c-1 0-1 0-1 1 1 0 2-1 3-1v1c-1 1-2 2-4 2h-4-4c-2-1-5-1-6-2 0-1 2-2 3-3l-1-1c-1-1-1-1-1-2l-2-2c0-1 1-1 2-1l1-2 5-1z" class="m"></path><path d="M623 882h5c2 1 3 0 4 2l1 1 1 1-1 1s-1 0-1 1v2c-2 1-2-1-3-1l-1 1-1-1h-7l-2-1-1-1v-2l1-2 5-1z" class="C"></path><path d="M617 885l1-2 1 4h2c1-1 2 0 3 0h2 1s1-1 2-1v1c2 0 3-1 4-2l1 1-1 1s-1 0-1 1v2c-2 1-2-1-3-1l-1 1-1-1h-7l-2-1-1-1v-2z" class="b"></path><path d="M660 895h4 1 1l-1 1h0-2c-2 1-3 1-5 1v2h-1c-2 1-4 2-5 4l-2 4c0 1 1 1 2 2v2l1 1s1 0 1 1v2s-1-1-1-2h-1-2v-1c-1-1-4-2-6-2-1 1-2 1-3 3l-1 1c-2 0-2 0-3-1l-3-3c-1-1-2-1-3-2v-1h1 1c1-1 2-1 3 0 1-1 1-2 2-3h3v-1h2l1-1c-1 0-1 0-2-1h2c1 0 3-2 4-2h1v-1h-1v-1h3c2 0 2 0 3-1h2c2 0 3 0 4-1h0z" class="k"></path><path d="M638 904h3c1 1 0 1 1 1h-3l1 2c-1 1-1 1-2 1h-1-2-1l-3-1h1 1c1-1 2-1 3 0 1-1 1-2 2-3z" class="m"></path><path d="M631 907l3 1c1 1 2 2 4 3 1-1 3-1 4-2l2 1c-1 1-2 1-3 3l-1 1c-2 0-2 0-3-1l-3-3c-1-1-2-1-3-2v-1z" class="B"></path><path d="M642 909l1-1h1c1 0 1-1 2-1 2-2 3-3 4-5 1-1 2-1 3-2l1-1h3c-2 1-4 2-5 4l-2 4c0 1 1 1 2 2v2l1 1s1 0 1 1v2s-1-1-1-2h-1-2v-1c-1-1-4-2-6-2l-2-1z" class="O"></path><path d="M660 895h4 1 1l-1 1h0-2c-2 1-3 1-5 1h-1c-1 1-3 1-4 1-2 1-4 3-5 4l-1 2c-1 1-2 2-3 2l-2-1c-1 0 0 0-1-1v-1h2l1-1c-1 0-1 0-2-1h2c1 0 3-2 4-2h1v-1h-1v-1h3c2 0 2 0 3-1h2c2 0 3 0 4-1h0z" class="i"></path><path d="M653 879h4l5 2h0l2 2 4 2h0c1 2 2 2 2 4l-3 1c-1 0-1 1-2 1l-2 1h-1-1c-1 0-2-1-3-1-1 1-1 1-2 1l-2-1c-1-1-2-1-3-3h0l-1 1h-2c-1 1-1 0-2 0l-1 1h1c-1 1-2 1-2 2l-1-1-1-1 1-2h-2l1-1-1-1c-1 1-1 2-2 2h-1c1-1 2-2 4-3v-2l1 1c1-1 1-1 2-1v-1l5-2 3-1z" class="N"></path><path d="M651 884c1 1 1 2 2 3h1l1 1c-2-1-2 0-4 0h0l-1-1c0-1 0-2 1-3z" class="Q"></path><path d="M650 880l3-1-1 2-1 1h-3l-1 1h2l-1 1c0 1 0 1-1 1 0 1-1 1-2 1v-1l1-1c0-1 0-1-1-1v-1l5-2z" class="c"></path><path d="M652 881l1 1c2 0 2 0 4 2v2h0c-1 1-2 1-3 1h-1c-1-1-1-2-2-3l-2-1h-2l1-1h3l1-1z" class="T"></path><path d="M653 879h4l5 2h0c0 2 1 3 1 5-3-1-3-1-6 0h0v-2c-2-2-2-2-4-2l-1-1 1-2z" class="L"></path><path d="M662 881l2 2 4 2h0c1 2 2 2 2 4l-3 1c-1 0-1 1-2 1l-2 1h-1-1c-1 0-2-1-3-1-1 1-1 1-2 1l-2-1c-1-1-2-1-3-3 2 0 2-1 4 0l-1-1c1 0 2 0 3-1 3-1 3-1 6 0 0-2-1-3-1-5z" class="E"></path><path d="M662 881l2 2 4 2-1 1h-4 0c0-2-1-3-1-5z" class="G"></path><path d="M657 886c3-1 3-1 6 0h0c0 1 0 2 1 3-2 1-3 1-4 1h-1c-1-1-1-1-2-1h-2v-1l-1-1c1 0 2 0 3-1z" class="X"></path><path d="M670 882c2-1 5-2 8-1 2 0 5 2 7 3 1 2 1 2 2 2 3 0 3 0 6 1h1v1c1 0 2 1 4 1 0 1 1 1 2 2-2 0-3-1-5 0 1 0 3 1 3 2l-1-1c-3 0-7-1-10 1h-1c-1 0-3 1-5 2-2 0-2 0-3 2h-2c-1-1-1-1-2-1l-2-2c-1 1-1 1-2 1v2l1 1c-1 1 0 1-1 1l-3 1c-1-1-1-2-3-2s-4 1-6 1v-2c2 0 3 0 5-1h2 0l1-1h-1-1-4l3-3 2-1c1 0 1-1 2-1l3-1c0-2-1-2-2-4v-1l2-2z" class="R"></path><path d="M666 895c2 0 4-2 6-3h1c1 1 2 0 4 1 2 0 5 1 7 0l1-1 1 1c-1 0-3 1-5 2-2 0-2 0-3 2h-2c-1-1-1-1-2-1l-2-2c-1 1-1 1-2 1v2l1 1c-1 1 0 1-1 1l-3 1c-1-1-1-2-3-2s-4 1-6 1v-2c2 0 3 0 5-1h2 0l1-1z" class="O"></path><path d="M672 894c3-1 6 0 9 1-2 0-2 0-3 2h-2c-1-1-1-1-2-1l-2-2z" class="Q"></path><path d="M670 882c2-1 5-2 8-1 2 0 5 2 7 3 1 2 1 2 2 2 3 0 3 0 6 1h1v1c1 0 2 1 4 1 0 1 1 1 2 2-2 0-3-1-5 0h-5c0-1-1-2-1-2-1 0-4 1-5 1s-1-1-2 0h-1-2-3c-2-1-4-1-6-1 0-2-1-2-2-4v-1l2-2z" class="I"></path><path d="M670 882c2-1 5-2 8-1 2 0 5 2 7 3 1 2 1 2 2 2v1c-2 1-4-1-7-1-2 0-5 0-8 1h0c-2-1-2-1-4-3l2-2z" class="X"></path><path d="M637 869c4 0 6 1 9 4h1l1 1c1 2 1 4 2 6l-5 2v1c-1 0-1 0-2 1l-1-1v2c-1-1-1-1-2-1-1-1-2-3-3-3l-1 2 2 2v1h-1l-1 3c-1 2-2 3-4 3h0c1-1 1-1 1-2h-1v-2c0-1 1-1 1-1l1-1-1-1-1-1c-1-2-2-1-4-2h-5v-1-1-2s-1 1-2 1v-2c-1 1-1 0-2 1h0l-3-3h2l5-1c2-1 3-2 5-2h1c3-2 5-3 8-3z" class="C"></path><path d="M637 869c4 0 6 1 9 4h1l-1 1-2 2-1-1c-1-2-1-2-3-2l1 2h-1l-2-2c-1 0-3 1-4 0h0l-1 1c-2-1-3-2-4-2 3-2 5-3 8-3z" class="c"></path><path d="M628 872h1c1 0 2 1 4 2l-2 1c-1 0-1-2-3 0l-1 1 1 1c1-1 0-1 1 0h2l1 1-2 3-2-1h-1c-1-2-2-2-4-2 0 0-1 1-2 1v-2c-1 1-1 0-2 1h0l-3-3h2l5-1c2-1 3-2 5-2z" class="W"></path><path d="M623 874c2-1 3-2 5-2l-7 5c-1 1-1 0-2 1h0l-3-3h2l5-1z" class="O"></path><path d="M623 878c2 0 3 0 4 2h1l2 1 2-3c1 1 1 0 2 0l3 3-1 2 2 2v1h-1l-1 3c-1 2-2 3-4 3h0c1-1 1-1 1-2h-1v-2c0-1 1-1 1-1l1-1-1-1-1-1c-1-2-2-1-4-2h-5v-1-1-2z" class="V"></path><path d="M632 878c1 1 1 0 2 0l3 3-1 2 2 2v1h-1l-1-1c-1-2-4-3-6-4l2-3z" class="h"></path><path d="M658 899c2 0 4-1 6-1s2 1 3 2l2 1c2 0 2 1 3 2 1 2 1 3 1 5 2 0 3 1 5 2h1l2 2c0 2-1 4-1 7h0c-2 1-2 1-3 1h-4-2-6l-4-1c-3-1-5-2-7-4v-2c0-1-1-1-1-1l-1-1v-2c-1-1-2-1-2-2l2-4c1-2 3-3 5-4h1z" class="c"></path><path d="M678 910h1l2 2-2 1 1 2v1h-2l-2-1-10-2c3-2 4-2 7-3l1-1 1 1-1 1 2 1 2-2z" class="f"></path><path d="M678 910h1l2 2-2 1 1 2v1h-2l-2-1c1 0 1 0 2-1-1-1-2-1-3-2l-1-1 2 1 2-2z" class="L"></path><path d="M658 899c2 0 4-1 6-1s2 1 3 2l2 1c2 0 2 1 3 2 1 2 1 3 1 5-4 0-7-1-10-2-1 0-2-1-2-1l-1-1h-7 0l-1-1c1-2 3-3 5-4h1z" class="C"></path><path d="M669 901c2 0 2 1 3 2 1 2 1 3 1 5-4 0-7-1-10-2l1-1s2 0 3 1c1 0 2 0 3-1 0-1-1-3-1-4z" class="K"></path><path d="M658 899c2 0 4-1 6-1l-1 1 1 1c-2 1-2 0-3 0h-1l-3 3c1 1 5 1 7 2l-1 1c-1 0-2-1-2-1l-1-1h-7 0l-1-1c1-2 3-3 5-4h1z" class="I"></path><path d="M654 915v-2c0-1-1-1-1-1l-1-1v-2c-1-1-2-1-2-2l2-4 1 1h0l1 1c2 2 2 4 5 5l-1 1h-1v1h2v-1c1 0 1 0 1 1 1 1 3 2 5 3v1c2 1 7 1 10 1 1 0 2-1 3-1h2v-1l-1-2 2-1c0 2-1 4-1 7h0c-2 1-2 1-3 1h-4-2-6l-4-1c-3-1-5-2-7-4z" class="D"></path><path d="M681 912c0 2-1 4-1 7h0c-2 1-2 1-3 1h-4-2-6l-4-1h0v-1h-1v-1c4 1 7 1 11 1 1-1 2-1 4-1 1 0 2-1 3-1h2v-1l-1-2 2-1z" class="R"></path><path d="M629 854l4 3 1 1c2 0 3 2 4 3l2-1 1 1s1 1 1 2c2 0 3-1 4-1v1c3 0 5 1 7 4l2 2c1 2 1 4 2 5v5h-4l-3 1c-1-2-1-4-2-6l-1-1h-1c-3-3-5-4-9-4-3 0-5 1-8 3h-1c-2 0-3 1-5 2l-5 1h-2c-1-1-2 0-3 0h-1l-2-2h-1l-1 1v1h0-2c2-3 3-6 4-9l1-1v-1l4-1c-2-1-3-2-3-3h2c1-1 2-1 4-1v-1l2-1 2-1c2-1 5-1 7 0v-2z" class="C"></path><path d="M632 858h0v2h1c0 1 1 2 2 2l-1 1h0-2c0 1 0 0-1 1 0-1 0-1-1-2-2 0-3-1-4-1-1-1-1-2-1-3h0c1 0 2 1 3 1 0 1 0 1 1 1 1-1 2-2 3-2z" class="L"></path><path d="M618 859v-1c2 2 3 3 5 3l1 1h0c1 1 2 1 3 2v2c-3 0-5-3-9-4-2 0-2-1-4-2 1-1 2-1 4-1z" class="D"></path><path d="M618 862c4 1 6 4 9 4 2 0 3 0 4 1l-1 3s-1 0-2 1h0c-1-1-2-2-4-3l-3-2-3-4z" class="g"></path><path d="M612 860h2c2 1 2 2 4 2l3 4 3 2c2 1 3 2 4 3h0c1-1 2-1 2-1l6-2 1 1c-3 0-5 1-8 3h-1c-2 0-3 1-5 2 0-1-1-2-2-2v-1c-3-1-3-2-4-5v-1c-1-2-1-2-2-2-2-1-3-2-3-3z" class="Z"></path><path d="M615 863c1 0 1 0 2 2v1c1 3 1 4 4 5v1c1 0 2 1 2 2l-5 1h-2c-1-1-2 0-3 0h-1l-2-2h-1l-1 1v1h0-2c2-3 3-6 4-9l1-1v-1l4-1z" class="Y"></path><path d="M617 866c1 3 1 4 4 5v1c1 0 2 1 2 2l-5 1c-1-1-1-1-1-2l-1 1c-1-1-1-1-2-1 1-1 2-2 2-3s0-2 1-4z" class="n"></path><path d="M611 865c2 0 3 0 4 1h0l-3 3c0 1 0 2 1 3l-3 1h-1l-1 1v1h0-2c2-3 3-6 4-9l1-1z" class="P"></path><path d="M629 854l4 3 1 1c2 0 3 2 4 3l2-1 1 1s1 1 1 2c2 0 3-1 4-1v1c3 0 5 1 7 4l2 2c1 2 1 4 2 5v5h-4l-3 1c-1-2-1-4-2-6l-1-1h-1c-3-3-5-4-9-4l-1-1 2-2h-1-2c0-1 0-1-1-2l-2-1h2 0l1-1c-1 0-2-1-2-2h-1v-2h0c-1-1-2-2-3-2v-2z" class="k"></path><path d="M632 858c3 1 5 3 6 6v2h-1-2c0-1 0-1-1-2l-2-1h2 0l1-1c-1 0-2-1-2-2h-1v-2z" class="G"></path><path d="M648 874h0c2 1 3 2 4 3h4c1-1 1-1 1-3v5h-4l-3 1c-1-2-1-4-2-6z" class="U"></path><path d="M646 863c3 0 5 1 7 4l2 2c1 2 1 4 2 5-1 1-1 1-2 1v1c-3 0-4-2-6-4v1l-1-1v-1l-1-1c0-1 0-1-1-2h-1l1 2-2-2h-1l1-1c-1-1-1 0-2-1h0v-1c1-1 3-2 4-2z" class="T"></path><path d="M437 862h2c2 1 2 1 4 1l1-1 1 1 1 3h-1v3 9l1 1 1 1s1 0 2-1c-1 2-1 4-1 6v1 1h8l1-3v26 6 1 5l-1 1h-10c-3 1-9 0-13 0h-5-8-48v-1c1 0 2 0 4-1h0-4c-1-2-1-3-3-4h1l1-1h4c2 0 5-1 7-1 2-1 3-1 4-3h0c-3 0-8 0-11-2v-2c-1 0-1-1-2-2l1-1v-1-4c0-2 0-5 1-7v-2l1-1 3-5c1-2 1-4 2-6 0-2 2-4 3-6h1c2-2 5-3 8-3h4l3 1 1-1h2c1 0 1-1 2-1h4l1-1-1-1c3-1 5 0 9 0h1l1 1v1c3 1 7 2 9 3l3 2c0-1 1-1 1-1l2-2h0-2c1-1 2-1 2-1 2-1 3-3 4-4 0-2-1-3-2-4z" class="j"></path><path d="M449 879c-1 2-1 4-1 6v1 1c-2 3 0 7-2 9v-17l1 1s1 0 2-1z" class="J"></path><path d="M437 862h2c2 1 2 1 4 1l1-1 1 1 1 3h-1v3 9-3c-1-1-1-2-1-4h1c-2 0-2 0-3-1v5c-1 2-1 3-1 5l-1 2c-1 1-1 2-1 3v1 1c-1 1 0 1-1 3v1c1 1 2 1 2 2l-1 1-2-1-2-1-3 3h-1-1c0-1 0-1-1-2l-1-2-3 1-2-2 1-1h3v-1-2-2h-4 2c1-1 3-2 4-3v-1c1 0 2 0 3-1v-2h1v-2l-1-1c0-1 1-1 1-1l2-2h0-2c1-1 2-1 2-1 2-1 3-3 4-4 0-2-1-3-2-4z" class="K"></path><path d="M427 888c2-2 4-2 6-2s3 1 4 1c0 1 0 1-1 1v2h1v3l-2-1-3 3h-1-1c0-1 0-1-1-2l-1-2-3 1-2-2 1-1h3v-1z" class="O"></path><path d="M428 891c3 0 5 0 7 1l-3 3h-1-1c0-1 0-1-1-2l-1-2z" class="Q"></path><path d="M427 888c2-2 4-2 6-2s3 1 4 1c0 1 0 1-1 1h-1c0 1 0 1 1 1l-1 1h0c-3-1-4 0-6-1h-1-1v-1z" class="m"></path><path d="M437 862h2c2 1 2 1 4 1l1-1 1 1 1 3h-1v3 9-3c-1-1-1-2-1-4h1c-2 0-2 0-3-1v5l-1-1-2 2v-1l-1 1h0l1 1h0v3c-1-1-1-2-2-3l-1 1h1v3h2v1h-2c-1-1-1-1-1-2l-1 1-1-1c-1 0-1 1-1 2l-1-1c-2 1-4 2-5 3h-4 2c1-1 3-2 4-3v-1c1 0 2 0 3-1v-2h1v-2l-1-1c0-1 1-1 1-1l2-2h0-2c1-1 2-1 2-1 2-1 3-3 4-4 0-2-1-3-2-4z" class="V"></path><path d="M437 862h2c2 1 2 1 4 1l1-1 1 1 1 3h-1v3 9-3c-1-1-1-2-1-4h1c-2 0-2 0-3-1v5l-1-1v-3c0-1 0-2 1-3h-1v1l-1 2h-1c0-2 0-2 1-3-1-1 0-1-1-1v-1c0-2-1-3-2-4z" class="S"></path><path d="M443 863l1-1 1 1 1 3h-1c-1 1-2 1-3 1s0 0-1-1l2-3z" class="B"></path><path d="M448 887h8l1-3v26 6 1 5l-1 1h-10v-6-21c2-2 0-6 2-9z" class="D"></path><defs><linearGradient id="K" x1="447.263" y1="922.684" x2="453.967" y2="920.111" xlink:href="#B"><stop offset="0" stop-color="#7c7d7d"></stop><stop offset="1" stop-color="#949391"></stop></linearGradient></defs><path fill="url(#K)" d="M446 923v-6 4c1-1 1-1 1-2 2 0 2 1 3 2 3 0 5 0 7 1h0l-1 1h-10z"></path><path d="M449 893c-1-1-1-2-1-4 2-1 6-1 9-1l-1 7-1-1h-2l1-1h1l-2-1c-1 0-1-1-2 0-1 0-1 1-2 1z" class="X"></path><path d="M457 916c-3 0-7 1-9-1-1 0-1 0-1-1 1 0 2 0 3-1h-2v-3l-1-2c0-1 1-1 2-2 1 0 2 0 4 1h1l2-1v1l1 3v6z" class="J"></path><path d="M449 910h1l2 2h-2-1v-2zm0-17c1 0 1-1 2-1 1-1 1 0 2 0l2 1h-1l-1 1h2l1 1v10l-7-1c-1-1-1-4-1-5l1-1c-1-2-1-2 0-3l1-1-1-1z" class="c"></path><path d="M425 892l3-1 1 2c1 1 1 1 1 2h1 1l3-3 2 1 2 1 1-1 2 2v5c-1 1-1 1-2 1v1h2 0c-1 1-2 1-3 2h1 1c0 1-2 2-2 3l2-1v1c-1 1-2 1-4 1l-2 2c1 0 0 0 1-1h3c-1 2-4 2-6 2v-1l-1 1-1-1c-2 0-3 0-5-1-1 0-1 0-1-1h2l1-1c-1-1-2-1-4-1v1l-1 1c-2 1-4 1-5 1h-1c0 1-1 1-2 1-1 1-2 1-3 2v-1l2-2c1 0 2-1 3-1l-2-2c-1 1-2 2-3 2l-1 2h-4l1-1c1 0 2 0 3-1 0-1 1-2 0-3 2-1 2-1 3-2l1-2c1-2 2-3 3-4l1-2 6-3z" class="D"></path><path d="M415 906l3-2h2c0 1 0 1-1 2h4v-1h-1v-1h5l1-1c-1 0-1 0-2-1h2c1 1 3 1 4 1h1c1 1 2 1 3 1v1h-4-2c1 1 1 1 2 1l-1 1h-3c-1-1-2-1-4-1v1l-1 1c-2 1-4 1-5 1h-1c0 1-1 1-2 1-1 1-2 1-3 2v-1l2-2c1 0 2-1 3-1l-2-2z" class="g"></path><path d="M439 894l1-1 2 2v5c-1 1-1 1-2 1v1h2 0c-1 1-2 1-3 2h1 1c0 1-2 2-2 3l2-1v1c-1 1-2 1-4 1l-2 2c1 0 0 0 1-1h3c-1 2-4 2-6 2v-1l-1 1-1-1c-2 0-3 0-5-1-1 0-1 0-1-1h2l1-1h3l1-1c-1 0-1 0-2-1h2 4v-1c-1 0-2 0-3-1h-1 5l1-1-7-1h7v-1c-1 0-2 0-3-1h4v-1l-3-1h2v-1c0-1 0-1 1-2h0z" class="k"></path><path d="M425 892l3-1 1 2c1 1 1 1 1 2h1 1l3-3 2 1 2 1h0c-1 1-1 1-1 2v1h-2-1v1h-1l-1 1h-1c-2 0-2 0-3 1h-3l-1 2h-1c-3 0-5-1-8 1l-1 1-1-1 1-2c1-2 2-3 3-4l1-2 6-3z" class="I"></path><path d="M425 892l3-1 1 2c1 1 1 1 1 2h-1c-1 1 0 1-1 2-3 3-7 2-10 4h-3c1-2 2-3 3-4l1-2 6-3z" class="G"></path><path d="M425 892l3-1 1 2v1h-3v1c-2 2-3 2-5 3-1 0-2 0-3-1l1-2 6-3z" class="T"></path><path d="M407 910h4l1-2c1 0 2-1 3-2l2 2c-1 0-2 1-3 1l-2 2v1c1-1 2-1 3-2 1 0 2 0 2-1h1c1 0 3 0 5-1l1-1v-1c2 0 3 0 4 1l-1 1h-2c0 1 0 1 1 1 2 1 3 1 5 1l1 1 1-1v1l1 1h2l-2 2h1 1v1 1h1 0c-2 2-2 2-4 2l-1 1-1-1c-1 1 0 1 0 2 1 1 1 1 3 1l1 1h-1c0 1 0 0-1 1h-5-8-48v-1c1 0 2 0 4-1h0-4c-1-2-1-3-3-4h1l1-1h4c2 0 5-1 7-1 2-1 3-1 4-3h0c1 0 2 0 4-1 2 1 7 0 10 0s6 0 7-1z" class="M"></path><path d="M415 910l3 1c-3 1-4 1-6 4v1 2h-1c-2 0-3 0-5 1l-1 1c1 0 4-1 4 1-2 1-6 1-8 1h-8c2 0 3-1 4-1 1-2 4-1 5-3-1 0-1 0-2-1h2c2 1 2 0 3 0l2-1h2l1-2 2-2c1-1 2-1 3-2z" class="i"></path><path d="M386 912h2l-1 1v1l-2 3c0 1-2 2-3 3h-1c-3 1 1 0-2 1h-3-4c-1-2-1-3-3-4h1l1-1h4c2 0 5-1 7-1 2-1 3-1 4-3z" class="C"></path><path d="M408 914h2l-1 2h-2l-2 1c-1 0-1 1-3 0h-2c1 1 1 1 2 1-1 2-4 1-5 3-1 0-2 1-4 1h0c-2 0-3-1-4-2s-1-3-1-4c1-1 2-2 3-2 2 0 2 1 4 1 1 0 1 0 2 1 2 0 3 0 4-1 2 1 5 0 7-1z" class="R"></path><path d="M391 914c2 0 2 1 4 1 1 0 1 0 2 1l2 1v1l-2-1c0 1-1 1-1 2 0 0-1 1-2 1s-3-1-4 0h-1c-1-1-1-3-1-4 1-1 2-2 3-2z" class="J"></path><path d="M407 910h4l1-2c1 0 2-1 3-2l2 2c-1 0-2 1-3 1l-2 2v1l-2 2h-2c-2 1-5 2-7 1-1 1-2 1-4 1-1-1-1-1-2-1-2 0-2-1-4-1-1 0-2 1-3 2 0-1 0-1-1-2v-1l1-1h-2 0c1 0 2 0 4-1 2 1 7 0 10 0s6 0 7-1z" class="b"></path><path d="M391 914c2-1 4-1 6-2 3 0 7 1 10-1l1 1h-1l1 2c-2 1-5 2-7 1-1 1-2 1-4 1-1-1-1-1-2-1-2 0-2-1-4-1z" class="I"></path><path d="M415 910c1 0 2 0 2-1h1c1 0 3 0 5-1l1-1v-1c2 0 3 0 4 1l-1 1h-2c0 1 0 1 1 1 2 1 3 1 5 1l1 1 1-1v1l1 1h2l-2 2h1 1v1 1h1 0c-2 2-2 2-4 2l-1 1-1-1c-1 1 0 1 0 2 1 1 1 1 3 1l1 1h-1c0 1 0 0-1 1h-5-8 0c-1 0-1 0-2-1 1-1 0-1 1-1 0-1 1-1 1-2l-1-1-2 1v1l1 1h-2l-1-1-1 1-1-1c0-2 1-3 2-5-1 0-2 1-3 1v-1c2-3 3-3 6-4l-3-1z" class="F"></path><path d="M420 919h3c1-1 3-2 5-3v2l1 1h-4c-2 1-3 3-5 3v1c-1 0-1 0-2-1 1-1 0-1 1-1 0-1 1-1 1-2z" class="B"></path><path d="M416 914c2-1 4-2 6-2 1 0 2 1 3 1v1 1l-1-1c-2 1-3 0-3 1-2 0-1 1-3 1l-2-2z" class="C"></path><path d="M416 914l2 2c2 0 1-1 3-1 0-1 1 0 3-1l1 1c-1 1-2 3-4 3h-2l-2 1v1l1 1h-2l-1-1-1 1-1-1c0-2 1-3 2-5l1-1z" class="e"></path><defs><linearGradient id="L" x1="429.472" y1="913.266" x2="420.528" y2="907.234" xlink:href="#B"><stop offset="0" stop-color="#5c5b59"></stop><stop offset="1" stop-color="#747271"></stop></linearGradient></defs><path fill="url(#L)" d="M415 910c1 0 2 0 2-1h1c1 0 3 0 5-1l1-1v-1c2 0 3 0 4 1l-1 1h-2c0 1 0 1 1 1 2 1 3 1 5 1l1 1 1-1v1l1 1h2l-2 2h1 1v1c-1 0-2 1-2 1-3 1-4-1-6-2-3-3-6-3-10-3l-3-1z"></path><path d="M401 870h2c1 0 1-1 2-1h4l1-1-1-1c3-1 5 0 9 0h1l1 1v1l9 3 3 2 1 1v2h-1v2c-1 1-2 1-3 1v1c-1 1-3 2-4 3h-2 4v2 2 1h-3l-1 1 2 2-6 3-1 2c-1 1-2 2-3 4l-1 2c-1 1-1 1-3 2 1 1 0 2 0 3-1 1-2 1-3 1l-1 1c-1 1-4 1-7 1s-8 1-10 0c-2 1-3 1-4 1-3 0-8 0-11-2v-2c-1 0-1-1-2-2l1-1v-1-4c0-2 0-5 1-7v-2l1-1 3-5c1-2 1-4 2-6 0-2 2-4 3-6h1c2-2 5-3 8-3h4l3 1 1-1z" class="X"></path><path d="M404 890v-1-1h2l1-1c2 0 1 0 2-1h-1c-1 0-2-1-3-2l3-1s1-1 2-1l2-1 1 1h1v1h-4c1 1 4 2 6 2v1s-1 0-1-1c-3 1-6 3-9 4-1 0-1 0-2 1z" class="Q"></path><path d="M406 889c3-1 6-3 9-4v3l-4 1c-2 1-4 3-6 4v2l-5 2h-2c-1 1-2 1-3 2-1 0-3 0-4 1-2 0-3 1-4 1l-1-2 1-1h3l1-3h6s0-1 1-2h3l1-1h-3l-1-1h3c0-1 1-1 2-1h1c1-1 1-1 2-1z" class="C"></path><path d="M404 890c1-1 1-1 2-1v1 1l-1-1c-1 1-2 1-2 2s1 1 0 2h-1c-1 0-1 1-2 1h-1c-1 1-1 1-2 1s-1 1-2 1h-4l-1 1 1-3h6s0-1 1-2h3l1-1h-3l-1-1h3c0-1 1-1 2-1h1z" class="N"></path><path d="M375 893v2c1 1 2 0 3 1h1l2-1c1-1 1-1 3-1v-1h2v1h5v1l-1 3h-3l-1 1 1 2c-2 1-3 2-4 3-2 1-2 1-4 0-2 1-3 1-5 1v-1-4c0-2 0-5 1-7z" class="X"></path><path d="M382 897c1 0 2 1 3 1h2l-1 1c-1 1-2 2-3 2l-1-1 1-1-1-2z" class="Q"></path><path d="M383 901c1 0 2-1 3-2l1 2c-2 1-3 2-4 3-2 1-2 1-4 0 1 0 1-1 2-1h0l1-1h-1v-1h2z" class="E"></path><path d="M375 893v2c1 1 2 0 3 1l-1 1v3h-3c0-2 0-5 1-7z" class="T"></path><path d="M379 896l2-1c1-1 1-1 3-1v-1h2v1h5v1l-1 3h-3-2c-1 0-2-1-3-1l-3 3h-1c0-2 1-3 1-4z" class="p"></path><path d="M385 898l-1-2h2 1l4-1-1 3h-3-2z" class="X"></path><path d="M429 872l3 2 1 1v2h-1v2c-1 1-2 1-3 1v1c-1 1-3 2-4 3h-2 4v2 2 1h-3l-1 1 2 2-6 3h0v-2l-1-1c-4 0-7 2-11 4-2 1-3 1-4 1h-1c-1 1-1 0-2 0l5-2v-2c2-1 4-3 6-4l4-1v-3c0 1 1 1 1 1v-1c-2 0-5-1-6-2h4v-1h-1l-1-1 3-1h1v-1h-1v-1h3v-1c-1 0-1 0-2-1h1c2-2 4-3 7-2h1c2 0 3-1 4-2z" class="i"></path><path d="M429 872l3 2 1 1c-1 1-1 1-3 2v-1c-1 0-2-1-2-1l-1 1 1 2-1 1h-2l-2-2h-1 1l2-3c2 0 3-1 4-2z" class="R"></path><path d="M416 876h1c2-2 4-3 7-2h1l-2 3h-1 1l2 2h-4c1 1 2 1 3 1l1 1c-1 0-4 0-5 1l-1 2h-3v1c-2 0-5-1-6-2h4v-1h-1l-1-1 3-1h1v-1h-1v-1h3v-1c-1 0-1 0-2-1z" class="e"></path><path d="M423 884h4v2 2 1h-3l-1 1 2 2-6 3h0v-2l-1-1c-4 0-7 2-11 4 0-1 0-2 1-3h2l1-2c3 0 4-1 6-1 1-1 2-1 3-2l-1-1c1-1 1-2 2-2l2-1z" class="U"></path><path d="M427 886v2 1h-3v-2l3-1z" class="S"></path><path d="M418 892v-1c2-1 3-1 4-2v-2h2v2l-1 1 2 2-6 3h0v-2l-1-1z" class="P"></path><path d="M418 892l1 1v2h0l-1 2c-1 1-2 2-3 4l-1 2c-1 1-1 1-3 2 1 1 0 2 0 3-1 1-2 1-3 1l-1 1c-1 1-4 1-7 1s-8 1-10 0c-2 1-3 1-4 1-3 0-8 0-11-2v-2c-1 0-1-1-2-2l1-1c2 0 3 0 5-1 2 1 2 1 4 0 1-1 2-2 4-3 1 0 2-1 4-1 1-1 3-1 4-1 1-1 2-1 3-2h2c1 0 1 1 2 0h1c1 0 2 0 4-1 4-2 7-4 11-4z" class="d"></path><path d="M419 895h0l-1 2c-1 1-2 2-3 4l-1 2c-1 1-1 1-3 2-2 0-4 1-6 2h-1-1l-1-2h0c-1 0-1-1-2-1h-2c2-2 4-1 6-2h1 1c1-1 3-1 5-2 0 0 1 0 2-1 1 0 2-1 3-2s2-2 3-2z" class="F"></path><path d="M404 907h1c2-1 4-2 6-2 1 1 0 2 0 3-1 1-2 1-3 1l-1 1c-1 1-4 1-7 1s-8 1-10 0c1-1 2-1 4-2h0c1-1 3-2 4-1h1c1 0 3 0 4-1h1z" class="E"></path><path d="M404 907h1c2-1 4-2 6-2 1 1 0 2 0 3-1 1-2 1-3 1l-1 1c-1 1-4 1-7 1 1 0 1-1 2-1 1-1 2 0 3-1v-1l-1-1z" class="e"></path><path d="M418 892l1 1v2c-1 0-2 1-3 2-1 0-2 0-3 1h-1-2c-4 2-8-1-12 2h-1c-1 1-2 1-3 1h-1v1c1 1 2 1 2 2-3 2-4 2-8 2-3 1-5 2-7 2-1 1-2 1-3 1l-2-1h0c-1 0-1-1-2-2l1-1c2 0 3 0 5-1 2 1 2 1 4 0 1-1 2-2 4-3 1 0 2-1 4-1 1-1 3-1 4-1 1-1 2-1 3-2h2c1 0 1 1 2 0h1c1 0 2 0 4-1 4-2 7-4 11-4z" class="h"></path><path d="M387 901c1 0 2-1 4-1-1 1 0 1-1 2s-3 3-4 2h0-3c1-1 2-2 4-3z" class="e"></path><path d="M379 904c2 1 2 1 4 0h3 0c-2 1-3 2-4 3l-2 1c-1 1-2 1-3 1l-2-1h0c-1 0-1-1-2-2l1-1c2 0 3 0 5-1z" class="C"></path><path d="M401 870h2c1 0 1-1 2-1h4l1-1-1-1c3-1 5 0 9 0h1l1 1v1l9 3c-1 1-2 2-4 2h-1c-3-1-5 0-7 2h-1l-1-1c-2 3-7 7-11 7h0l-1 2h-1c1 1 2 1 3 2l1 1h-4-1l-2 2h0c-1 0-2 0-3 1s-4 0-5 0l-1 1h-3v1h0 2c1 0 1 0 2-1h1 1c-2 2-5 2-7 2h-2v1c-2 0-2 0-3 1l-2 1h-1c-1-1-2 0-3-1v-2-2l1-1 3-5c1-2 1-4 2-6 0-2 2-4 3-6h1c2-2 5-3 8-3h4l3 1 1-1z" class="f"></path><path d="M394 881c1 0 1 0 2 1 2-1 1-2 3-2l1 1-1 3c-2 0-3-2-5-2v-1z" class="g"></path><path d="M416 868l4 1 9 3c-1 1-2 2-4 2h-1c-3-1-5 0-7 2h-1l-1-1c1-1 2-1 3-2h3l-1-1c-2 0-4-1-6 0v1c-2-1-3-1-4-1v-1c1-2 4-2 6-3z" class="T"></path><path d="M416 868l4 1 9 3c-1 1-2 2-4 2h-1v-1h1-2c-1 0-1-1-2-2h-4l-1-3z" class="J"></path><path d="M401 870h2c1 0 1-1 2-1h4l1-1-1-1c3-1 5 0 9 0h1l1 1v1l-4-1c-2 1-5 1-6 3-2 4-7 7-10 10l-1-1c-2 0-1 1-3 2-1-1-1-1-2-1v1h-8c1-1 1-1 3-1l1-1h1 1v-2h1c1-1 1-1 2-1h1c1 0 1 0 2-1l-2-1c1-1 2-1 3-1s1 0 2-1c-2-1-3-2-4-3l3 1 1-1z" class="h"></path><path d="M397 870l3 1 3 1c1 0 1 0 2 1-1 1-3 2-5 3 0 2 0 2-1 2-2 1-3 1-4 2h-2l-1 1h2v1h-8c1-1 1-1 3-1l1-1h1 1v-2h1c1-1 1-1 2-1h1c1 0 1 0 2-1l-2-1c1-1 2-1 3-1s1 0 2-1c-2-1-3-2-4-3z" class="X"></path><path d="M393 870h4c1 1 2 2 4 3-1 1-1 1-2 1s-2 0-3 1l2 1c-1 1-1 1-2 1h-1c-1 0-1 0-2 1h-1v2h-1-1l-1 1c-2 0-2 0-3 1s-2 1-3 1l-4 2c1-2 1-4 2-6 0-2 2-4 3-6h1c2-2 5-3 8-3z" class="f"></path><path d="M381 879c0-2 2-4 3-6v3c-1 1-2 1-1 2l1 1s1-1 2-1h1v1c2 0 7-3 9-4l2 1c-1 1-1 1-2 1h-1c-1 0-1 0-2 1h-1v2h-1-1l-1 1c-2 0-2 0-3 1s-2 1-3 1l-4 2c1-2 1-4 2-6z" class="T"></path><path d="M579 497c31 3 65 1 90-19 13-11 23-26 28-43 2-5 3-11 3-17 1-1 1-6 1-6 1-1 12-1 27 0v261h-29c-4-27-11-56-35-74-19-14-42-17-65-14l-6 1 2 16c4 0 7-1 11-1 16-2 33 1 47 11 17 12 25 32 29 52l1 24h59l1-290h-56c-1 25-5 48-24 66-18 18-46 19-70 18-3 0-9 0-11-1h5c5 1 10 1 14 1 20 0 42-2 58-15 17-14 24-34 26-55l1-14 57-1v292h-60c-1-26-5-52-25-71-13-13-31-17-49-17-3 0-5 1-8 1-2 1-4 1-5 2l-1 28c0 2 1 8 0 10-7 0-16-5-22-7-3-1-6-3-9-3l1 11v3c-2 1-3 1-5 0h-1c-1-1-1-1-2-3s-1-2-4-3l-1-1 1-1h1c-2-2-2-3-3-5l-1-3v-5l-2-4c-2-3-4-4-7-5h-1c-1-1-2-1-3-1v-1l1-1c-2-2-2-2-4-2v-3h0-4-3-1c-2-1-5-1-7-1v-3c-1 0-2-1-3-2-4-2-7-6-9-9-1-1-5-2-6-2l-8-1v-1h-1c0-5-1-10 0-14 2-1 3-3 5-5l3-3 1-2c1-1 0-2 0-3h-4v-1c-2 0-3-1-4-2-1-3 0-7 0-10v-56c1 0 4 1 5 0l-1-1c3 0 6 0 9-1h1v-4c0-10 3-19 5-28s3-18 2-27c2 2 2 8 2 11-1 1-1 1-1 2h1c3 9 6 19 14 24h3 0c2-1 4-3 6-4 0-1 2-2 2-3l-2-2 4-3 2 1c2 1 6 1 9 2 1 0 2 1 4 1h0c1 0 3 1 4 1 3 1 5 2 7 2 0 3 0 4 1 6l1 2 1 3 1-1h2l-3 2v1 1l-3 3v1h1l3-1v-1l2-2c1 2 0 5 0 6l1 10v3h3z" class="j"></path><path d="M693 531h1c1 1 2 2 2 3l-1 1h-2c-1-2 0-2 0-4z" class="D"></path><path d="M619 540h1c1 1 2 1 4 2v1h-1c-2 0-2 0-3 1l-2-2 1-2zm95 18h0c1 0 1 1 2 1l2 2v1l-1 1h-2v-2l-1-3z" class="B"></path><path d="M631 530c1 1 2 2 2 4 1 0 1 0 1 1-1 1-2 1-3 2l-1-2c0-1-1-1-3-1l1-1 1 1c1-1 2-3 2-4z" class="I"></path><path d="M631 530l2-1c1 1 1 1 2 1h1l1 1-1 2 1 1h-1l1 1-1 1-2-1c0-1 0-1-1-1 0-2-1-3-2-4zm70 7h2v1l2 4h0 1 1v1c-1 1-2 1-3 2h-1l-1-1v-1h-2c0-1 0-2-1-3h0v-1h2 1l-1-2z" class="B"></path><path d="M648 563c1 2 0 3 1 5 0 1 3 3 4 3l1 1h-1l-1-1c-2 0-3 0-4 2v2h-1c-2-4 1-4-5-6 1 0 2 0 3-1 1 0 1 0 2-1 0-1 0-2 1-4z" class="J"></path><path d="M623 520h3c0 1 0 1 1 1v1 2h-1c-2 1-4 1-6 1h-1c0 1-1 1-1 2l-1-1-1-1c1-1 1-2 2-2s1 1 2 0c-1-1-1-1 0-2l3-1zm19 49l-1 1-1-1h-4 0l-1-1c-2 0-3-1-3-2 1-1 1-1 2-1l1 1 1-1c2 1 3 1 5 0h2c2 0 1-1 3-1 0 0 1 0 2-1-1 2-1 3-1 4-1 1-1 1-2 1-1 1-2 1-3 1z" class="B"></path><path d="M578 577v-9-21c3 7 6 16 8 24-1-1-2-1-3-1h-1l-1-2-1 1h0l-1 1v4 7 13 1-11l-1-1v-6z" class="i"></path><path d="M572 530c2 1 3 1 4 1v32l-6-3v-2c1-9 0-19 0-27l2-1z" class="l"></path><defs><linearGradient id="M" x1="577.967" y1="589.229" x2="592.388" y2="589.618" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#M)" d="M578 583l1 1v11-1-13-7-4l1-1h0l1-1 1 2h1c1 0 2 0 3 1 3 9 5 18 7 28 0 2 1 4 0 5 0 1-1 1-2 2-5 1-10 3-15 5v-1h0c1-1 1-2 2-3v-9-15z"></path><defs><linearGradient id="N" x1="559.233" y1="565.087" x2="574.916" y2="563.895" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#686867"></stop></linearGradient></defs><path fill="url(#N)" d="M559 500l2 4 1 2c0 3 1 6 2 9v6 1c0 1 1 2 1 3 1 1 2 1 3 1 1 2 3 3 4 4l-2 1c0 8 1 18 0 27v2h-1c0 1 3 2 4 2 1 1 3 2 3 2v4 15c1-3 1-7 1-10v4h1v6 15 9c-1 1-1 2-2 3h0v1h-4-2c-3-1-6-2-9-1v-1c0-1 0-3 1-4l-1-2v-23l-3-2v-1h1l1 1v-2h1l1-12-2-26c1 2 1 5 1 8v1l1-1v-1-4h0c0-2 0-3 1-4l-4-37z"></path><path d="M570 558v-1h-1v-26h1c0 8 1 18 0 27z" class="Z"></path><path d="M563 537c1 5 0 11 1 16v12c-1-2 0-6 0-8-1-1-1 0-1-2h1l-2-2c0 4 1 8 0 11l-2-26c1 2 1 5 1 8v1l1-1v-1-4h0c0-2 0-3 1-4z" class="F"></path><path d="M559 500l2 4 1 2c0 3 1 6 2 9v6 1c0 1 1 2 1 3 0 2 1 8 0 10h0c1 3-1 7 1 9v1l-1-1c0 3 1 7-1 9-1-5 0-11-1-16l-4-37z" class="V"></path><defs><linearGradient id="O" x1="583.679" y1="587.296" x2="562.735" y2="597.081" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2a2c"></stop></linearGradient></defs><path fill="url(#O)" d="M576 583c1-3 1-7 1-10v4h1v6 15 9c-1 1-1 2-2 3h0v1h-4-2c-3-1-6-2-9-1v-1c0-1 0-3 1-4l-1-2h3c4-1 8-1 12 1v-21z"></path><path d="M562 605l2-1c2 1 2 0 4 0s2 0 3 1h1l1 1c-2 1-2 0-3 3l1 1c1-1 1-1 2-1l1 1h2v1h-4-2c-3-1-6-2-9-1v-1c0-1 0-3 1-4z" class="F"></path><defs><linearGradient id="P" x1="558.662" y1="624.527" x2="592.148" y2="630.105" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#P)" d="M594 626c1 2 1 4 1 6v-19c0-3-1-7 1-9l-1 28c0 2 1 8 0 10-7 0-16-5-22-7-3-1-6-3-9-3l1 11v3c-2 1-3 1-5 0h-1c-1-1-1-1-2-3s-1-2-4-3l-1-1 1-1h1c-2-2-2-3-3-5l-1-3v-5-2l1-2v-3-1c0-1-1-3-2-4h2 1v-2-1h1l7 1v2l1-3c3-1 6 0 9 1h2 4c5-2 10-4 15-5 1-1 2-1 2-2 1 8 1 15 1 22z"></path><path d="M563 619c1-2 7-4 10-5l-3 3c-1 1-2 1-2 2h-5z" class="U"></path><path d="M560 639c0-2 1-4 1-6v-1h1 2 0l1 11v3c-2 1-3 1-5 0h-1c-1-1-1-1-2-3h1c2-1 2-1 2-4z" class="P"></path><path d="M557 643h1c2-1 2-1 2-4v3c2 1 3 1 5 1v3c-2 1-3 1-5 0h-1c-1-1-1-1-2-3z" class="B"></path><path d="M552 610h1l7 1v2c-1 7-3 16-4 23v2h-1-1c-2-2-2-3-3-5l-1-3v-5-2l1-2v-3-1c0-1-1-3-2-4h2 1v-2-1z" class="X"></path><path d="M552 610h1c0 7 0 13 1 19 0 2 1 5 2 7h0v2h-1-1c-2-2-2-3-3-5l-1-3v-5-2l1-2v-3-1c0-1-1-3-2-4h2 1v-2-1z" class="O"></path><defs><linearGradient id="Q" x1="574.851" y1="621.196" x2="594.416" y2="622.919" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#Q)" d="M591 606c1-1 2-1 2-2 1 8 1 15 1 22v15h-1c-8-6-17-9-25-15-2-1-5-2-6-4l-1-1 2-2h5c0-1 1-1 2-2l3-3 2-2c5-1 13-3 16-6z"></path><path d="M573 614l2-2c0 5-1 9-1 13v1c-1-3 0-8-1-10-1 1-1 0-2 1h-1l3-3zm-10 5h5c-1 2-1 3 0 4 0 1 2 2 4 3l3 3c-2 0-9-5-11-7l-1-1-1 1-1-1 2-2z" class="V"></path><path d="M570 617h1c1-1 1 0 2-1 1 2 0 7 1 10l-1-1c-1-1 0-6 0-7-1 2-1 6-1 8-2-1-4-2-4-3-1-1-1-2 0-4 0-1 1-1 2-2z" class="n"></path><path d="M543 457l2 1c2 1 6 1 9 2 1 0 2 1 4 1h0c1 0 3 1 4 1 3 1 5 2 7 2 0 3 0 4 1 6l1 2 1 3 1-1h2l-3 2v1 1l-3 3v1h1l3-1v-1l2-2c1 2 0 5 0 6l1 10v3 34c-1 0-2 0-4-1-1-1-3-2-4-4-1 0-2 0-3-1 0-1-1-2-1-3v-1-6c-1-3-2-6-2-9l-1-2-2-4 4 37c-1 1-1 2-1 4h0v4 1l-1 1v-1c0-3 0-6-1-8l-7-46-6-20-1-4c-2-1-3-1-5-2-1 0-2-1-2-1 0-1 2-2 2-3l-2-2 4-3z" class="B"></path><path d="M559 470c2 0 5 0 6 1l1 1h-2-5c1-1 1-1 0-2z" class="O"></path><path d="M541 462l2-1c1 1 1 1 1 2l2 5c-2-1-3-1-5-2-1 0-2-1-2-1 0-1 2-2 2-3z" class="V"></path><path d="M555 484l-1-2c-1-4-3-9-1-12h1l1 2h1v-2l1-1 2 1c1 1 1 1 0 2h-1l-1-1v1h0c-1 2 0 3-1 5l1 1v1h-3v1h3v1h-2-1l1 1v2z" class="P"></path><path d="M555 484v-2l-1-1h1 2v-1h-3v-1h3v-1l-1-1c1-2 0-3 1-5h0v-1l1 1h1 5 2c0 1 1 1 1 2-2 2-1 7-6 7v1 1c0 1-1 2-2 3l-1 1c1 2 8 4 8 5-1 0-7-2-8-3h0-2c0-2-1-4-1-5z" class="O"></path><path d="M559 472h5 2c0 1 1 1 1 2-2 2-1 7-6 7v1h0c-1 0-2-1-3-1l1-1h1c-1-2-1-5-1-8z" class="U"></path><path d="M559 472h5v1h0-2l-1 1c0 1 0 1-1 2 0 1 0 3 1 4v2c-1 0-2-1-3-1l1-1h1c-1-2-1-5-1-8z" class="a"></path><defs><linearGradient id="R" x1="561.31" y1="482.168" x2="575.371" y2="486.172" xlink:href="#B"><stop offset="0" stop-color="#4b4b4b"></stop><stop offset="1" stop-color="#696768"></stop></linearGradient></defs><path fill="url(#R)" d="M571 472l1 3 1-1h2l-3 2v1 1l-3 3v1h1l3-1 1 14c-1 0-1 1-1 1h-1-1l-5-4c0-1-7-3-8-5l1-1c1-1 2-2 2-3v-1-1c5 0 4-5 6-7 1 1 1 3 2 5 2-2 2-5 2-7z"></path><path d="M573 481v-1l2-2c1 2 0 5 0 6l1 10v3 34c-1 0-2 0-4-1-1-1-3-2-4-4-1 0-2 0-3-1 0-1-1-2-1-3v-1-6c-1-3-2-6-2-9l-1-2-2-4c-1-3-2-8-3-11h2 0c1 1 7 3 8 3l5 4h1 1s0-1 1-1l-1-14z" class="l"></path><path d="M556 489h2 0c1 1 1 2 2 4h0c1 4 1 9 2 13l-1-2-2-4c-1-3-2-8-3-11z" class="n"></path><defs><linearGradient id="S" x1="584.406" y1="497.633" x2="564.658" y2="520.294" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#S)" d="M573 481v-1l2-2c1 2 0 5 0 6l1 10v3 34c-1 0-2 0-4-1-1-1-3-2-4-4h0 2l1 1 1-1c-1-1-1-1-1-2-1-3 1-6 0-8 0 0-1 0-1-1v-3l-1-1v-6-3-1l-1-2h0v-1c1 1 2 2 2 4h1c2-1 2-2 3-4 0 0-1-1-1-2h0s0-1 1-1l-1-14z"></path><path d="M525 581l14-5c0 1 0 2-1 2 1 1 2 1 2 1h1l1-1c5-2 11-2 16 0l3 2v23l1 2c-1 1-1 3-1 4v1l-1 3v-2l-7-1h-1v1 2h-1-2c1 1 2 3 2 4v1 3l-1 2v2l-2-4c-2-3-4-4-7-5h-1c-1-1-2-1-3-1v-1l1-1c-2-2-2-2-4-2v-3h0-4-3-1c-2-1-5-1-7-1v-3c-1 0-2-1-3-2-4-2-7-6-9-9h4v-1l3-1c2-2 4-3 6-5l-4 1 5-4 4-2z" class="o"></path><path d="M550 606c4 0 7 2 11 3v1l-1 3v-2l-7-1h-1v-2l-2-2z" class="m"></path><path d="M540 579h1c1 3 0 7 0 11l1 12h-2l-1-9c0-4 0-9 1-14z" class="C"></path><path d="M539 593l1 9h2c1 1 3 0 4 1 2 0 3 1 4 2v1l2 2v2 1 2h-1-2c1 1 2 3 2 4v1 3l-1 2v2l-2-4c-2-3-4-4-7-5h-1c-1-1-2-1-3-1v-1l1-1c-2-2-2-2-4-2v-3h2c2 0 2 0 2-2 1-2 1-6-1-8h1c1-2 1-3 1-5z" class="K"></path><path d="M546 603c2 0 3 1 4 2v1l2 2-1 2h-5v-1h-3c-1 1 0 0-1 0h-1c1-1 1-1 3-2 1-1 1-2 2-3v-1zm-3 12c-1-1-3-2-3-4 2 0 2 0 4 2h5c1 1 2 3 2 4v1 3l-1 2v2l-2-4c-2-3-4-4-7-5 1 0 1 0 2-1z" class="B"></path><path d="M543 615c2-1 2-1 4 0 1 1 2 1 2 3 0 1-1 2-1 3-2-3-4-4-7-5 1 0 1 0 2-1z" class="P"></path><path d="M525 581l14-5c0 1 0 2-1 2 1 1 2 1 2 1-1 5-1 10-1 14 0 2 0 3-1 5h-1c2 2 2 6 1 8 0 2 0 2-2 2h-2 0-4-3-1c-2-1-5-1-7-1v-3c-1 0-2-1-3-2-4-2-7-6-9-9h4v-1l3-1c2-2 4-3 6-5l-4 1 5-4 4-2z" class="j"></path><path d="M521 587h1c3 0 8-6 11-5-6 4-12 9-19 9 2-2 4-3 6-5l1 1z" class="F"></path><path d="M525 581l14-5c0 1 0 2-1 2l-17 9-1-1-4 1 5-4 4-2zm-13 12c7-2 18 2 25 5 2 2 2 6 1 8 0 2 0 2-2 2h-2 0-4-3-1c-2-1-5-1-7-1v-3c-1 0-2-1-3-2-4-2-7-6-9-9h4 1z" class="U"></path><path d="M507 593h4 1c1 2 3 2 4 4 3 2 6 5 9 7 1 1 3 2 4 3l-2 1h-1c-2-1-5-1-7-1v-3c-1 0-2-1-3-2-4-2-7-6-9-9z" class="Z"></path><path d="M525 604c1 1 3 2 4 3l-2 1h-1c-2-1-5-1-7-1v-3l1 1 1 1h3l1-2z" class="n"></path><path d="M514 432c2 2 2 8 2 11-1 1-1 1-1 2h1c3 9 6 19 14 24h3 0c2-1 4-3 6-4 0 0 1 1 2 1 2 1 3 1 5 2l1 4 6 20 7 46 2 26-1 12h-1v2l-1-1h-1v1c-5-2-11-2-16 0l-1 1h-1s-1 0-2-1c1 0 1-1 1-2l-14 5-4 2-5 4 4-1c-2 2-4 3-6 5l-3 1v1h-4c-1-1-5-2-6-2l-8-1v-1h-1c0-5-1-10 0-14 2-1 3-3 5-5l3-3 1-2c1-1 0-2 0-3h-4v-1c-2 0-3-1-4-2-1-3 0-7 0-10v-56c1 0 4 1 5 0l-1-1c3 0 6 0 9-1h1v-4c0-10 3-19 5-28s3-18 2-27z" class="o"></path><path d="M539 576c2-1 5-1 7-1l1-1c4 0 9 0 13 2v2l-1-1h-1v1c-5-2-11-2-16 0l-1 1h-1s-1 0-2-1c1 0 1-1 1-2z" class="a"></path><path d="M509 557c1-1 3-2 4-2l4-1v-1c1 0 2 0 3-1 6 0 13 0 19 1 2 0 2 0 4 1h-3 0-20v14l2 1c1 3 0 10 3 12l-4 2-5 4-8 4 1-24c0-2-1-7 0-8v-2z" class="J"></path><path d="M520 568l2 1c1 3 0 10 3 12l-4 2c-2-2-1-12-1-15zm-12-78c1 0 1 0 2-1l8-3h1c-1 2 0 27 0 32h11c2 1 2 0 4 1h1 0c-1 1-1 1-2 0h-5c-1-1-7-1-8 0h-1v1h0c1 6 0 14 0 20l1 12c-1 1-2 1-3 1v1l-4 1c-1 0-3 1-4 2h0l-1-1v-14-52z" class="G"></path><path d="M516 445c3 9 6 19 14 24h3 0c2-1 4-3 6-4 0 0 1 1 2 1 2 1 3 1 5 2l1 4 6 20-3-1c0-1-1-1-2-1l-4-1h-4c-1-1-2-1-4-1l-4-1c-3 0-4 0-6-1h-7-1l-8 3c-1 1-1 1-2 1 0-15 5-30 7-45h1z" class="f"></path><defs><linearGradient id="T" x1="540.27" y1="471.57" x2="543.045" y2="480.681" xlink:href="#B"><stop offset="0" stop-color="#9d9c9b"></stop><stop offset="1" stop-color="#c5c4c2"></stop></linearGradient></defs><path fill="url(#T)" d="M539 465s1 1 2 1c2 1 3 1 5 2l1 4 6 20-3-1c0-1-1-1-2-1l-4-1h-4c-1-1-2-1-4-1l-4-1c-3 0-4 0-6-1h-7-1c5-1 11 0 16 0 2 0 4 1 6 1 0-2-1-4-1-6-1 0-2 0-4-1h-1c-1 0-1 0-1-1-2-3-2-7-3-10h3 0c2-1 4-3 6-4z"></path><path d="M533 469c1 1 1 1 2 1l4-3v10 4c-1 0-2 0-4-1h-1c-1 0-1 0-1-1-2-3-2-7-3-10h3z" class="N"></path><path d="M533 479h0v-3h1 2 0l1 1h2v4c-1 0-2 0-4-1h-1c-1 0-1 0-1-1z" class="L"></path><defs><linearGradient id="U" x1="547.303" y1="474.521" x2="546.334" y2="488.68" xlink:href="#B"><stop offset="0" stop-color="#c2c1c0"></stop><stop offset="1" stop-color="#ecebe9"></stop></linearGradient></defs><path fill="url(#U)" d="M547 472l6 20-3-1c0-1-1-1-2-1l-4-1h-4l1-3v-1-3h1l2 2 1 1c0-5-1-9 2-13z"></path><path d="M514 432c2 2 2 8 2 11-1 1-1 1-1 2-2 15-7 30-7 45v52 14l1 1h0v2c-1 1 0 6 0 8l-1 24 8-4 4-1c-2 2-4 3-6 5l-3 1v1h-4c-1-1-5-2-6-2l-8-1v-1h-1c0-5-1-10 0-14 2-1 3-3 5-5l3-3 1-2c1-1 0-2 0-3h-4v-1c-2 0-3-1-4-2-1-3 0-7 0-10v-56c1 0 4 1 5 0l-1-1c3 0 6 0 9-1h1v-4c0-10 3-19 5-28s3-18 2-27z" class="I"></path><path d="M498 560c1-1 0-1 0-2l3-1v1c2 0 4-1 5 1-1 1-3-1-5 0v2 1h-4v-1l1-1z" class="D"></path><path d="M493 549v1 1c2 1 3 0 4 1s3 4 4 5l-3 1c0 1 1 1 0 2l-1 1c-2 0-3-1-4-2-1-3 0-7 0-10z" class="E"></path><path d="M493 559l2-2 2 1 1 2-1 1c-2 0-3-1-4-2z" class="d"></path><path d="M514 432c2 2 2 8 2 11-1 1-1 1-1 2-2 15-7 30-7 45v52 14l1 1h0v2c-1 1 0 6 0 8l-1 24 8-4 4-1c-2 2-4 3-6 5l-3 1v1h-4c-1-1-5-2-6-2l-8-1v-1l14 3v-3-97l-9 1-1-1c3 0 6 0 9-1h1v-4c0-10 3-19 5-28s3-18 2-27z" class="O"></path><path d="M501 565c2 4 1 9 2 13 0 1 1 3 1 3 1 1 2 1 3 3v5 3l-14-3h-1c0-5-1-10 0-14 2-1 3-3 5-5l3-3 1-2z" class="R"></path><path d="M510 111V60l1-1v-1l-1 1v-1l1-1h1c1 2 0 2 0 4h-1 0c-1 4 0 9 0 12 0 7 0 48-1 50 0 3 0 7 1 10 0 2-1 3 0 4 2-2 1-9 1-12v-1l1 1h0 341v65 112 70 20 13h-72c-2-11-3-23-5-35-10-42-33-81-67-108-33-26-75-41-116-43-11-1-21-1-31 0l-7 1c4 7 8 13 11 20 4 8 7 15 4 23l-1 2c3 6 6 12 8 19s1 14 0 20l-1 10c3 2 7 4 11 5h1l2-1h0 2c3 3 4 6 3 9v2h0c0 2-1 4-2 5h-1v-2l-1 2-1-1c0-1 0-2 1-3h-1c-1 2-1 4-1 6-1 1-1 2-1 3-1 0-3-1-4-1l-9-3s-1 1 0 1c0 2 1 2 0 3h-2 0l-1 1c-1 1-1 1-2 1h0-1c0 1 0 0-1 1h-1s-1 1-2 1l-1 1v-1h-2c-2 0-4-1-6-1-1-1-1-1-2-3l-1-1h0c-1 1-1 2-2 2 1 1 2 2 2 3l-1 1-1 1v1c0 1-1 2-2 2-1 1-1 1-1 2l1-1 1 1h0v3h-1-1c-2 0-4 3-4 4v1s-1 1-1 2h0v4c-1 1-1 0-1 1v13c1 1 1 1 2 1s2 1 3 1c0 2 0 6-1 8l1 1h3c1 0 1 0 2 1l1-1c1 0 1 0 2 1h0c2 0 2 1 3 1 2 0 4 1 6 2l5 3c1 1 3 2 4 3s1 2 2 3v1l-1-1c-1 8 0 16-1 24l-4-2h-1c-1-1-2-1-3-1h-1 0v1h3l1 1h0c1 0 2 0 3 1s2 1 4 1c1 0 1 0 2 1 1 0 2 1 4 1h1c2 0 5 2 7 1 2 0 2-1 5-1 0 1-1 1 0 2s2 2 2 4v5c-1 2-4 5-6 7l2 2c-3 3-3 7-2 12v1l-1 3c0-1-1 0-1 0-1 2-2 2-4 3-4 3-8 6-13 9v-1l3-2h-2l-1 1-1-3-1-2c-1-2-1-3-1-6-2 0-4-1-7-2-1 0-3-1-4-1h0c-2 0-3-1-4-1-3-1-7-1-9-2l-2-1-4 3 2 2c0 1-2 2-2 3-2 1-4 3-6 4h0-3c-8-5-11-15-14-24h-1c0-1 0-1 1-2 0-3 0-9-2-11 1 9 0 18-2 27s-5 18-5 28v4h-1c-3 1-6 1-9 1l1 1c-1 1-4 0-5 0v56c0 3-1 7 0 10 1 1 2 2 4 2v1h4c0 1 1 2 0 3l-1 2-3 3c-2 2-3 4-5 5-1 4 0 9 0 14h1v1l8 1c1 0 5 1 6 2 2 3 5 7 9 9 1 1 2 2 3 2v3c2 0 5 0 7 1h1 3 4 0v3c2 0 2 0 4 2l-1 1v1c1 0 2 0 3 1h1c3 1 5 2 7 5l2 4v5l1 3c1 2 1 3 3 5h-1l-1 1 1 1c3 1 3 1 4 3s1 2 2 3h1v1l3 1c-1 0-2 1-3 1l-2 1c0 1 0 1 1 1l-2 2h1 2l2 2v1h0c0 1 1 1 0 2v1h-2v1l2 2-1 1-1 1h0v1c-1 1-2 1-3 1l-1 1h3 1l1 1c-1 1-1 3-1 4l-1 1h3l5 1v1c2 1 5 1 7 1 1 1 3 1 4 0h0 1c2 1 8 1 10 0h0l1 1h2 7l2 2c0 2 0 2-1 3-1 0-2 1-3 1 0 3 1 7-1 10l-4 6 3 3-4 1-4-2c-2 2-4 4-5 7-1 1-1 2 0 3h-1l-1 1h-3v-1c0 4 1 7 1 11v4 1c1 0 2 0 3 1h7 0c4 1 8 1 12 1v8 4h2 1c0 1 1 1 2 1l1 2 1-1 1 1c1 0 1 1 2 2l-1 1c-1 0-1 0-1 2l2-1v2c1 1 2 1 4 1l-1 1h1v1c0 1-1 1-1 2 1 1 2 1 2 3 0 1 0 2-1 3 1 1 1 2 2 2v1c1 1 2 1 3 3h-6c1 2 1 2 0 4l-1 1c-2-1-1-2-4-2l1 1c0 2 0 4-1 6 1 1 1 1 0 2v4c2 1 3 1 5 1l1 1c-1 0-1 1-2 1h0l2 2c1 0 1-1 2-1 0-1 0-1-1-2h3v9 4c0 2 0 6 1 8v1l-1 1-1 3h-2v1h-1l-1-1-1-1-2 1s-1 2-2 2h-2c0 2 0 4-1 6h0 3 4v1 1h-1c0 1 0 1 1 2l-1 1c-1 1-1 2-2 3 0 1 0 0-1 1l-1 1 1 1c-2 1-4 1-6 1-1 1-1 1-2 0l-1 1h-1l1 1c0 2-1 3-1 4 2 0 4 0 5-1h1c1 0 3 1 3 2v2c1 0 1 0 2 1 0 1-2 2-3 3h-1v1c-1 1-1 2-3 3l-1 1-1 1h-1c-2 1-5 6-5 9v3 3 4 5c-2-2-4-3-6-4-3 2-6 1-9 1h-1c-3 0-5 0-8 1h-2v1l-1 2c-4-1-6-2-10-1l-5 4h-1c-4 0-9 0-13 1h-3v-1-2l-2-5-2-2-3-3c-2-1-4-2-5-2-5-2-12-1-16 0-1 1-3 2-4 2-2 0-5 0-7 1-1-1-3-1-5-1 1-1 2-1 2-3h-6c-1-1-9 0-12 0h-23-7l-1-1-1 1c-2 0-2 0-4-1h-2c1 1 2 2 2 4-1 1-2 3-4 4 0 0-1 0-2 1h2 0l-2 2s-1 0-1 1l-3-2c-2-1-6-2-9-3v-1l-1-1h-1c-4 0-6-1-9 0l1 1-1 1h-4c-1 0-1 1-2 1h-2c-1-1 0-2 0-3 1-1 1-3 2-4l2-2 3-2c-1 0-1 0-2-1l1-3 1-1v-1c1-2 2-3 3-5v-1-1h2l-1-2 1-1-1-1h-2l-1-1-3 1-1-1-1 1-1-1h-1-2v-1c0-2 0-2-2-4h-6-5-1-8-5-1c-1-1-1-1-2-1l1-1-1-1 1-1c1 0 2 1 4 0h1c0-3 1-7 0-10-5 0-4 0-5 5v2c-1-2-1-5-1-7h-3v3c-2 1-5 1-7 1-1-2 0-5-1-7v-2h1c-1-1-1-1-1-2v-1-4c-1-1 0-3 0-4v-1l-1-1h-1v-2c-1-1-2-2-4-2h-1v-1l1-1 3-3v-4-1c0-2-1-3-3-4h0c0-2 0-2 1-4 2-3 5-7 7-10 0-1 1-1 2-2v-1-1c2-4 2-10 3-14l1-1s1-1 1-2h0l1-1h3v-1h-2l1-2 1-1 1 1v1-1c1-1 1-2 0-3l1-1 1 1 1-1-1-1h13 0 20v-1h-3v-1-5h0v-5-2-1c0-1-1-2-1-2h-1v-2h0c-1-1-1-1 0-1v-2c-1-1 0-1 0-2v1c1-1 1-1 1-2h0c-2-1-1-1-2-1h-3c-1-1-1-1-2-1h-3c-1 0-1 0-2 1v1h-1l1-1-1-1-1 1s0 1-1 1v-2h-7-1-10c-2 1-4 2-6 2-1 0-1 0-2-1l20-18c2 1 3 0 5 1h0l1-284v-87-26c0-7 0-14-1-21-2-16-7-33-16-46-13-20-29-29-52-34-20-4-44-3-64 5-29 11-54 36-68 63-3 6-6 12-8 19-2 4-3 9-5 13l-2 2-1-1c-1-2 0-5 0-8 0-18 1-37 3-55 3-23 7-48 17-69 6-11 14-21 23-29 4-5 10-10 15-13 1-1 2-2 3-2 2-1 4-1 6-1h11 49 211v-13h-8 0c3 0 5 0 8-1z" class="j"></path><path d="M439 642h1c1 2 1 5 0 7v1l-1-1v-7zm122-470l1-1c2 1 2 1 3 2l-1 2h-2c0-1 0-1-1-2v-1zm149 37h1v2l1 1c0 1-1 1-2 2h0l-1 1v-1c0-2 0-3 1-5zm-103-75c1-1 1-1 2-1s1 0 3 1c-1 1-1 1-1 2l-1 1h-1c-1-1-1-2-2-3zm53 87h1l4 5h-1v-1c-1 1-2 1-2 1v1l-1-1v-3h-2l1-2zm-225 88l1-1h1v-2h1c2 1 2 0 3 1v1c-1 1-2 0-2 1l-2 1h-1l-1-1zm167-123c1 0 1 1 2 1 0 1 0 2 1 3h0l-2 2-1-1c-1-2 1 0-1-1v-1c0-1 1-2 1-3zm206 67h1l1 1c0 1-1 2-1 3h0v1l-1 2-2-1v-3l1-1c0-1 0-1 1-2z" class="B"></path><path d="M423 536l1-1c1-1 2 0 3 0l-2 6-2-2v-1-2z" class="K"></path><path d="M657 200h4c1 1 1 1 2 3l-1 1h-2l-2-1-1-2v-1z" class="C"></path><path d="M790 355l-1-1c1-1 2-1 3-2l-1-2 1-1 1 1c0 3 0 4 1 7h2c-1 1 0 1-1 1l-5-3zM373 173l1-1c1 2 2 2 2 3 1 1 1 2 0 3-1 0-2-1-4-2 0-2 0-2 1-3zm455 21h3v3l-1 1c1 0 1 0 1 1l-1 1h-2l-1-1c1-1 1-1 1-2l-1-1 1-2zm-620 28c1 0 1 0 2 1v2c-1 1-1 2-2 3h-2l-1-1c0-1 1-2 2-3s1-1 1-2zm617 108h2l1 1-1 1v1h0v1 2h1l-1 1c-2 0-2 0-3-1-1-2 0-4 1-6zM421 145h1c2 0 2 0 3 1v2h-1v1h2v1c-1 1-4 0-6-1 0-2 0-2 1-4zm242 9h1l1 1 1-1 1 1-1 1c1 1 1 1 1 2h-2v-1l-1 1h-1l-1 1-2-2 3-3zm139 153h0c2 0 3 0 4 1s0 1 1 1 0-1 1 1c0 0-1 1-2 1l-2-1h-1-3v-2l2-1z" class="B"></path><path d="M397 773c0-2 0-2 1-4 1 1 1 1 3 1v-1c2 1 2 2 3 2l1-1 1 1c2 0 5 1 7 1-5 0-11 0-16 1z" class="n"></path><path d="M821 323c1-1 2-1 3 0 0 1-1 2-1 3l-1 1c-1-1-1-1-2-1l1 1 1 1h-2v-1h-2v-2-1-1h1 2zM307 165c5-1 13-1 18 0h-5l-1 1c-1 1-2 1-3 2-1 0-2 0-3-1h0c0-1 0-1-1-2h-5zm131 29h2v3c1 0 1 0 2 1v1h-1v1h-2v-2l-1 1c-1 0-1-2-2-3v-2h2zm1 67v1 1h2c1 0 1 0 2 1l-3 2v1l-2 2-2-1c0-1 0-3 1-4l1-1h0 0-1-1l2-2v1l1-1zm348-69c2 0 2 1 3 1v3c0 4 1 2 4 4h-1-1c-3 0-4-1-6-3 0 0 0-1 1-1v-1-3zM441 317c0 1 1 3 0 4v1 1 1c0 1 0 1 1 2-1 1-2 1-3 3h0c1 0 1 1 2 1l-2 2-1-1 2-1h-1l-1-1c0 1 0 2-1 4v-5l1-4c0-1 1-2 2-3h0v-3l1-1zm338-95h0l1 1-1 1h1c-1 2-2 3-3 5 1 0 0 0 1 1l-1 1-1-1h-2-1l-1-1 3-2c0-1-1 0 0-1s2-3 4-4zm-321-57h1l2 1h1v3c1 0 1 0 2 1l2-2v1c-1 1-2 1-3 2 0 1 1 2 1 3h-1c-1-1-1-1-2-1l-1 1-1-1c1 0 1-1 2-1l1-1v-1l-1-1h-2l-1-1c-1 0-1 0-1-1l1-2z" class="B"></path><path d="M424 456c0 2 0 3 2 4l2 3-1 1c-1 0-1-1-3 0 0 1 0 2-1 2v-3c0-1-3-2-4-3h0l3-1c1 0 1-2 2-3h0z" class="I"></path><path d="M429 279c0 3-7 18-9 22v-8c1-2 4-4 5-6 2-2 3-5 4-8z" class="C"></path><path d="M603 195h0v2c2 2 4 2 7 4-2-1-5-1-7-1 0 0-1 2-1 3l-1 1h-1l1-3c-2-2-5-3-7-4 2 0 5 1 7 0 1 0 1-1 2-2z" class="N"></path><path d="M790 355l5 3c1 0 0 0 1-1l1-2v2c0 1-1 1-1 2 1 4 9 8 12 10h0c-3-1-10-6-13-6v1 1-2l-5-8z" class="E"></path><path d="M421 744c0-3-1-6-1-9 1-1 1-1 0-3h0c1-1 1-1 1-2v-1l2 1h-1v3c0 2 1 5 1 8-1 2 0 5 1 7h-2c0-2 0-2-1-4z" class="Z"></path><path d="M730 251h1c1 1 2 1 3 2l1 1c1 1 1 1 1 2s0 1-1 2h0l-1-1-3 1h-1-1c1-1 1-1 1-2s-1-2-2-3h1l1-2z" class="S"></path><path d="M572 146h1c0 1 0 2 1 3l-1 1 2 1c-1 2-1 2-3 3h-1 0v1h-1-1-1v1l-1-1v-1l1-1v-2c1-1 1-1 2-1 1-1 1-2 1-3l1-1z" class="B"></path><path d="M586 157c1-1 1 0 3 0 2-3 5-4 9-5l-1 1c0 1 0 1 1 2l-1 1-1-1h-2c0 1 0 2-1 3s-1 0-1 0c-1 2 1 2-1 3v-1h-3l-1-1c-1-1-1 0-1-1v-1z" class="S"></path><path d="M828 249l1-1 1 1c-1 1 0 1-1 2 0 1-1 1-1 2 0 2-1 3-2 5h0l-2 1-1-2v-2-1h-1l3-5 1 1h0v1l2-1v-1z" class="B"></path><path d="M460 449c1-1 0-1 1-2 0-1 0-1 1-2v8c0 2-1 10 1 11-1 1-2 2-3 2v-17z" class="N"></path><path d="M811 289h1s1 0 2-1c2 1 3 2 5 4h1l-1 1h2c1 0 1 0 3 1h0l1 1 1 1h-2 0-1c-1 0-2-1-2-1v-1c-3-1-6-1-9-2-1-1-1-2-1-3z" class="B"></path><path d="M424 749l1 2-1 1h-1l-1-1c-2 2-1 13-1 17l-3-6c1-1 1-2 2-3-2-1-2-5-3-7l2-1c1-1 3-1 5-2z" class="S"></path><path d="M753 229l-19-19h1l21 17-3 2z" class="J"></path><path d="M798 275c1-2 1-5 1-6v-1l1-1c0-2-1-1 0-2 1-4 1-9 2-14 0-2 1-5 1-7v-1 3l-1 15-1 13-3 1z" class="I"></path><path d="M549 212c3 4 8 10 10 16-1 0-1 1-1 1-1 0-1 0-1-1-2-2-4-3-7-5h1 1l1-1-2-2c-1-3-2-6-2-8z" class="W"></path><path d="M702 165c2 0 2 1 4 1h1v1c1 1 1 0 2 1l1 1h-1c1 1 1 1 1 2l-2 1-3 1v1h-2l1-1-1-1v-3c-1-1 0-3-1-4zm-120-7h4c0 1 0 0 1 1l1 1c0 1 0 1 1 2v1c-1 0-1 1-2 1s-2-1-3-1v-1l-1 1v2h-2-1l1-2c-1-2-1-2-2-3l1-1c1 0 1 0 2-1h0z" class="B"></path><path d="M814 345c2 1 1 2 1 3l4 4c0 1-1 1-2 2h-2-1c-1 1-1 1-3 1v-2c0-1 0-1-1-2 1-1 1-1 1-3l3-1v-2z" class="S"></path><path d="M424 456l-1-1 1-2h1 1c0 1 1 1 2 1l1-1h2v1h0c-1 2-1 3-1 5h-1v1c0 1-1 2-1 3l-2-3c-2-1-2-2-2-4zm-12-92c0-1 0-1 1 0s1 1 0 3c1 0 1 1 1 2-1 2-2 3-4 5-2 0-2-2-3-3v-1-2l2-2 2-2h1z" class="B"></path><path d="M436 431v3c0 1 0 2-1 3h0c0 1 0 1-1 2h0v-2-2-1h-2 0v-1-1-1c1-1 1-1 2-3v-3c0-1 1-1 2-2-1 0-1 0-1-1s0-2 1-3h1l1 2h-1c0 1 0 2 1 3h-1c0 2 0 4-1 5v2h0 0z" class="P"></path><path d="M228 187l1-1c1 1 1 1 2 1h1 0c2 2 4 2 6 2 1 0 2-1 3-1h2c-2 2-7 1-8 4l1 5-5-5h-1-1c-1 0-2 1-3 1h0c0-1 0 0 1-1s2-2 2-3c-1-1-1-1-1-2h0z" class="c"></path><path d="M692 227h2c1 1 1 0 1 2h1l-1 1 1 1c0 1 0 2-1 3h0c-1 1-1 1-2 1-1-1 0-1-1-1h-1c-1-1-2 0-3-1l-1-2-1-1 1-2 1-1v1 2l1-1c0-1 1-1 2-1l1 1v1l1-1v-1l-1-1z" class="B"></path><path d="M465 610l2 1c2 0 4 0 6 1h1l-3 3-1-1c-1 0-1 0-1 1-1 0-2 0-3 1-3 1-5 3-8 3 1-3 5-4 7-6l-1-1c-1 0-3 1-4 1 2-1 3-2 5-3z" class="M"></path><path d="M298 150v-1-1h0v-2c1-2 0-6 1-8v-1c-1-1 0 0 0-1v-1c1 1 1 4 1 6 1 2 0 4 2 6l1 1 1-1h1c-1 2-3 2-4 4v1l2 2v1h-1l-1 1-1 1c-1-1-1-2-1-3h1l-1-1v-1l-1-2z" class="S"></path><path d="M681 140c1-1 2-1 3 0 0 0 0 1 1 2v1l-1 1h-2-1v2h-1-1v1c-1 1-1 1-3 1-1 0-1-1-2-1l2-1h0v-1h-2l1-1v-1c-1 0-1-1-1-2v-1l1 1v1c1 0 2 1 4 1v-1h-1v-1h2c0-1 0-1 1-1zm78 101v3l-1 1-1-1-1-1h-1c0 1 0 1-1 2h-2v-1h-1v-2c1-2 1-3 2-4h0v-1c0-1 0-1 1-1h4c0 2 0 4 1 5z" class="B"></path><path d="M395 777v1h1v-4l1-1c0 2 0 4-1 6v6c0 6-1 12 1 17l-1 1c-1-1-1 0-1-1h-1v-2c-1-5 0-10 0-15 0-3 0-6 1-8z" class="H"></path><path d="M227 153c1-1 2 0 3 0v2c-1 1-2 2-3 2l-1 1c0 1-1 1-2 1v1l-1 1-1 1v2h-2l-1 1h-1c0-2 0-3 1-4v-1l1-1c1 0 1-2 3-3v-1c1-1 1-1 2-1 0-1 1-1 1-1h1z" class="B"></path><path d="M582 158l2-2v-1h0v-2-1c1-1 1-2 2-2v-1h1v1 1h2l1 1 1-1h1s1-1 2-1l1 1h5l1-1v1c-1 1-1 1-3 1h0c-4 1-7 2-9 5-2 0-2-1-3 0v1h-4z" class="K"></path><path d="M586 157l-1-1h1c1-2 3-3 5-3 2-1 5-2 7-1-4 1-7 2-9 5-2 0-2-1-3 0z" class="E"></path><path d="M642 173l36 5h0c-7 0-14-1-21-1-3 0-5 0-8-1l-1 1c-2-1-2-1-3-1v1 1h0c-1-1-1 0-1-1-2 0-1 1-3 0 0-2 0-2 1-4z" class="N"></path><path d="M391 792h0v-1c1-2 1-3 1-6h2c0 5-1 10 0 15v2h1-5c-2-4-1-5-1-9v-2l1-1 1 2z" class="n"></path><path d="M389 791l1-1 1 2v1c0 1 0 1-1 2h1l1 1v2h-2l1 1c0 1 1 0 1 1l1 1s1 0 1 1h1-5c-2-4-1-5-1-9v-2z" class="V"></path><path d="M270 133h1s0 1 1 2h3v4s1 0 1 1c-1 0-2 1-3 2v1c-1 0-1 0-2 1h-2l-2-2h-1-1c1-2 1-3 2-4l1-1h-1v-1l1-1v1h1v-1c1-1 1-1 1-2z" class="S"></path><path d="M270 137c1 0 2 1 3 1v2l-2 1h-1l-2-2 2-2z" class="e"></path><path d="M226 193c-1 2-2 3-3 3s-1 0-2-1v1c-1 1-3 1-4 1s-1 2-2 2l-1-1 3-2c0-1 0-2 1-2v-1-1h0v-3h1c1 0 2 1 4 1h0l2-1c1-1 1-1 3-2h0c0 1 0 1 1 2 0 1-1 2-2 3s-1 0-1 1h0z" class="S"></path><path d="M784 288v1h0l-1-2-1 1c0-1-1-1-1-1h-3c-1-1-1-1-2-1h-2c-1-1-1-1-2-1h-1-2l-1-1h-1-2 0l-1-1v-1l1 1c1 0 2 0 4-1 0 0 1 1 2 1v-1c1 1 1 1 2 1s1 0 2-1l1 1c1 0 1 0 2-1-1-1-1-1-1-2h-1l1-2 1 1v1c1 0 1 1 2 2 0 1 1 1 2 2h1 1v-1l1 1v1h0v1c-1 1-1 1-1 2z" class="B"></path><path d="M393 770c1-1 1-3 1-4l1 11c-1 2-1 5-1 8h-2c0 3 0 4-1 6v1h0l-1-2-1 1v-11-4h1 1c1 0 1 1 2 1v-7z" class="m"></path><path d="M439 677v-2c1-2 1-3 2-5 1 1 1 1 2 1 1 1 1 2 1 3 1 1 1 0 3 0l3 1 1-2v-2c2 0 3-1 4-2 1 2 1 3 2 4h2l1 2h0 3v1c-8 0-17-1-24 1z" class="k"></path><path d="M418 617h7l-3 16-1 3h-1l-2-19z" class="I"></path><path d="M460 332h0v1c1-1 1-2 2-3 1-2 4-4 6-5h1l1 1h1 0 2c-2 1-6 3-7 5h0c-1 2-4 4-4 5s-1 3 0 4v2 4 1 1c-1 1-1 1-1 2v1h-1v-11-6-2z" class="c"></path><path d="M435 309l1 1h1l2-1c0-1 1 0 2-1v1c-1 1-1 2-1 3v2 2h0l1 1-1 1v3h0c-1 1-2 2-2 3l-1 4v5h-1c0-3 0-5-1-8h-1c0-1 1-2 1-3 1-1 1-2 1-3l-1-1s0-3 1-3c1-1 1 0 2-1-1-2-1-2-3-2-1-2-1-2 0-3z" class="S"></path><path d="M429 527v1l-2 7c-1 0-2-1-3 0l-1 1-1-1c-1 0-1 1-2 2 0 1 1 2 2 3l-1 1c-1-2-3-3-4-5v-1l-2 1c0 1 0 1-1 2l-1-1v-1-1-2-1l-1-1v-3c3 2 8 5 12 4 2 0 2-1 3-2l2-3z" class="B"></path><path d="M411 657h1l1 2h1l-1 1v1c0 3-1 7 0 10v9l-1 1h-2v3 2h-1v-2h-1l1-2s0-1 1-2v-2h0v-1h-1c1-2 1-2 1-4-1 0-1-1-2-2 1-1 1-2 3-3h0v-4c-1-2-2-1-4-2 1-1 2-2 2-3h1l-1-1v-1h2z" class="S"></path><path d="M412 528v-1c0-5-3-14 0-17l1-1c1 1 1 3 1 5 0 3 0 9 2 12 0 1 2 2 3 2 2 2 4 2 7 2h1c-1 1-1 2-3 2-4 1-9-2-12-4z" class="E"></path><path d="M720 219h1l1 2v2h1l-1 1v1s-1 0-1 1l-1-1c1-1 1 0 2-1l-1-1v1c-1 1-1 0-2 1v2h1l1-1v1h1c-1 1-1 1-1 2v1h-1 0-1 1v1h0 1l1-1h1 0c0 1 0 2-1 2v1 1h-2 1c-1 1-1 1-3 1v2h-2c-1 1 0 1-1 0 0-1 1-1 1-2h-1c-1 0-2 0-3-1l2-1v-3c1-1 1 0 1-1l-2 1h-1l-1 1-1-1 1-1h2l1-2h2 0 1l-2-3h0v-1c1-1 1-1 2-1l1-1v1l2-2v-1z" class="K"></path><path d="M801 274l1-13c0 2 0 5 1 7-1 1-1 2-1 3 1 1 2 1 3 2 0 1-1 1-2 3 1 1 2 1 4 2l-1 1-2-1-1 1v3h2l1 1h1v1l-1 1v1l1 1v1c1 1 2 0 4 1 0 1 0 2 1 3l-9-3c0-1-1-1-2-2v-4l-1-3h-2v-1-4l3-1z" class="S"></path><path d="M798 275l3-1v9l-1-3h-2v-1-4z" class="E"></path><path d="M436 419v-2c0-1 0-2-1-3-1-3-3-5-5-8 1 0 1 1 2 2 1 2 4 3 6 4 1 0 2-2 2-2v-2l2-2c0-2 1-4 1-6 1-2 1-4 2-6v-2h0c0 5-2 11-3 16v1 1c0 1 0 2-1 3l1 1v1c0 2 1 4 2 5 0 1 0 1 1 2l2 3c-1 0-3-2-4-3 0 1-1 2-1 2-2 0-2 1-3 2v1h-1v1h-1l-1 3h0 0v-2c1-1 1-3 1-5h1c-1-1-1-2-1-3h1l-1-2h-1z" class="G"></path><path d="M437 428h0v-2l3-6c1 0 2 1 3 2 0 1-1 2-1 2-2 0-2 1-3 2v1h-1v1h-1z" class="M"></path><path d="M756 227l2 1h1v-1h0l2 1c1 0 1-1 2-2v-2h1v1 2l2 2h-2c0 1 0 1 1 1h2l-2 2h-1l1 1c1 1 3 0 4 0v1l-4 1 10 11h0c-3-2-9-8-12-8v2c-1-1-1-1-1-2h-1l-2-1v4c-1-1-1-3-1-5h-4c1-1 1-2 1-2 0-1 0-1-1-2h2c0-1-2-2-3-3l3-2z" class="L"></path><path d="M381 700l3-1c1 0 3 1 4 2l4 4c2 3 5 6 9 8l1 1h-13c-1-1-1-2-2-2 0-1-1-2-1-2-2-2-3-5-3-7 0-1-1-2-2-3z" class="h"></path><path d="M455 668h-3c3-5-2-15 2-20 2-3 6-5 9-6l2 1c-1 0-3 2-3 3 1 0 2 1 2 1 0 2 1 3 1 4l1 1h-2 0c-2 1-5 1-7 1v1l-1 1c-2 3-1 8-1 12v1z" class="D"></path><path d="M462 646c1 0 2 1 2 1 0 2 1 3 1 4l1 1h-2 0c-2 1-5 1-7 1v-2c1-2 3-4 5-5z" class="Q"></path><path d="M414 709c3-2 5-2 7-2 2-1 3 0 5-1 2 0 4 1 6 0h2v1c-2 0-3-1-4 1v6c2 2 3 0 5 1 0 1 0 1 1 2s4-1 6 1h-4c-5 1-11 0-16 1-2 1-6 1-8 0v-2l1-1h1 2v-6h1v6h1c2-2 1-6 1-8 1 2 1 5 1 7l1 1 1-1v-5-1h1l1 2c1 0 0 0 1 1l-1-1-1 1v3h1v-1h1v1h1c1-1 0-5 0-7h-9c-1 1-2 1-4 1h-1z" class="E"></path><path d="M426 530c-3-1-6-2-8-4-1 0-2-1-2-2-1-1-1-3-1-3l-2-11c4 4 9 7 13 11 2 2 2 3 3 6l-2 3h-1z" class="X"></path><path d="M381 700c1 1 2 2 2 3 0 2 1 5 3 7 0 0 1 1 1 2 1 0 1 1 2 2h-9-9c-1 0-2 0-4 1h-1c4-5 10-10 15-15z" class="J"></path><path d="M377 707l4-2v1c0 1-1 1 0 2h1l1 2-2 2v1l-1 1h-9v-1h-1c1-1 2-1 3-2l4-4z" class="L"></path><path d="M377 707l4 4-6 2h-2v-2l4-4z" class="o"></path><path d="M298 150l1 2v1l1 1h-1c0 1 0 2 1 3 0 1 0 3 1 4h3v1c3 2 19 2 24 2l-3 1c-5-1-13-1-18 0-1 0-2 0-3 1l1 1v1l-1-1-1 1c-2 0-2 0-3 1l-1 2c0 2 0 4-1 7v-4c0-2 0-4-1-6-1-1-1-1-1-2v-1c-3-1-5-1-8-1h-1c-2 0-4-1-6-1-3 0-6 1-9 0l11-1c1 0 4 0 5 1h0l2-1 1 1c2-1 3-1 4-1l1-1c1-1 1-1 1-3 0-1 1-2 1-2v-1-1-3-1z" class="L"></path><path d="M446 606c4 1 7 1 11 1h-1c-1 1-1 1-2 1l-1 1c4 1 8-1 12 1-2 1-3 2-5 3l-5 5-3-3v-2c-2 1-8 0-10 0l1-2h1c-1-1-2-1-4-1h-8v-1-1h2 1 2l2-2c0 1 0 1 1 1s4 0 6-1h0z" class="Y"></path><path d="M432 610v-1-1h2 1 2 8v1h-7 0l2 1h-8z" class="P"></path><path d="M641 177v1c-1 0-1-1-2 0v2c-1 3-1 6 0 9v9c-2-1-1-8-2-10v-1c0-2 0-3-1-4v-1-3h-1l1-2c-2-1-4-1-5-2l-1-1-1 1-1-1-1 1-1-1h-1c-6-3-13-1-19-3 2-1 5 0 6 0 3 0 8 1 11 1v-1l1 1h1 5 0l4-1h1c1-4 1-9 1-13v-9-1-1c0 1 1 2 1 2v2 1 6 1c1 2 1 5 1 7 0 1 0 1 1 2 0 1 0 1 1 1l1 1h-1-1c1 2 1 2 3 3-1 2-1 2-1 4zM419 579c1 1 3 2 5 3h0c0 1 0 1 1 2 0 2-1 5-1 8-1 6-1 12-3 18l-4 4 2-35z" class="T"></path><path d="M394 756v-2c2-2 3-2 5-2l1-1c1 2 2 3 1 5v1h0c0 1 0 1 1 2h4c0 1 0 1-1 3 1 2 1 3 1 5h-2l-1 1v1c1 1 1 1 2 1l-1 1c-1 0-1-1-3-2v1c-2 0-2 0-3-1-1 2-1 2-1 4h0l-1 1v4h-1v-1l-1-11c1-2 1-9 0-10z" class="P"></path><path d="M402 759h-6v-4l3-1 2 2h0v1h0c0 1 0 1 1 2z" class="B"></path><path d="M793 287h0v-2c0-1 1-1 2-1-1-2-1-3 0-5h3v1h2l1 3v4c1 1 2 1 2 2 2 1 6 2 9 3s6 1 9 2v1c-2 0-4 0-6-1-1-1-1 0-2 0l-1-1h-1-2-2-1l-1-1c-1 1-2 1-2 2l-1 1v-1-1h-1c-3 2 0 1 0 3h-2l-3 18v-7h0c1-2 1-4 1-6l-1-1v-4-1l-1-2-1-1c-2-1-2-2-4-2-1-1-2-1-2-1-2-1-2-1-4-1 0-1 0-1 1-2l7 2 1-1z" class="L"></path><path d="M793 287h0v-2c0-1 1-1 2-1-1-2-1-3 0-5h3v1h2l1 3v4c-1-1-1-1-1-3l-2 1c0 1 0 2-1 3-2 0-3 1-5 0l1-1z" class="X"></path><path d="M793 287h0v-2c0-1 1-1 2-1-1-2-1-3 0-5h3v1c0 2 0 3-1 5h-1-1l1 1c-1 1-1 1-3 1z" class="O"></path><path d="M422 719c5-1 11 0 16-1l2 2h-2v1h0l1 1c0 2-1 2-2 3l-1 2 1 1h4 1l-1 2h-1c-1 0-3 0-4 1 1 1 4-1 6 0-2 2-6 0-9 1-3 0-8 1-11 1v-3h1l3-1v-1h-4c-2-1-1-2-1-4-1-1-1-2-1-3l2-2z" class="S"></path><path d="M428 728l1-1v-2-1l-1-1c0-1 0-1 1-2h2c0 1 0 3 1 4-2 2-2 2-2 4h-3l1-1z" class="j"></path><path d="M431 721l1-1v3l1-1v-2h3c1 2 1 3 1 5l-1 2h-1v-1c-2 0-2 0-3-1s-1-3-1-4z" class="H"></path><path d="M426 728v-6h-1l1-1h1c1 2 1 5 1 7h0l-1 1h3c0-2 0-2 2-4 1 1 1 1 3 1v1h1l1 1h4 1l-1 2h-1c-1 0-3 0-4 1 1 1 4-1 6 0-2 2-6 0-9 1-3 0-8 1-11 1v-3h1l3-1v-1z" class="m"></path><path d="M432 725c1 1 1 1 3 1v1h1l1 1c-2 1-4 1-6 1h-1c0-2 0-2 2-4z" class="F"></path><path d="M430 699l1 1v3h4v-1c-1 0-1 0-2 1l-1-1c1-1 2-1 3-1 0-1 0-1 1-2l1-1v3h3c0-1 0-1-1-1l2-2 1 1c0 2 0 4 1 6h-1v3c1 2 0 4 0 6l2 1h-1l-1 1c2 1 7 0 10 0v1h3c1 0 1-1 1-1 1 0 3 2 3 2v1 1l-6-3c-4 1-8 1-11 1-2-2-5 0-6-1s-1-1-1-2c-2-1-3 1-5-1v-6c1-2 2-1 4-1v-1h-2c-2 1-4 0-6 0-2 1-3 0-5 1-2 0-4 0-7 2h0-1l-1-2v-1c5-2 11-2 17-2 1-2 1-3 1-5z" class="R"></path><path d="M408 741c3 0 5 1 8 1h-1c-6 2-10 6-15 9l-1 1c-2 0-3 0-5 2v2h-1v3l-2-2c1-2 1-3 0-4l-2-2h-1-3-2c-1 1-1 2-1 3l-2 1h-1l1-1-2-2c-2 0-3 0-5-1v-1l-1 1-3-1h3v-1h-2l1-2 1-1 1 1v1-1c1-1 1-2 0-3l1-1 1 1 1-1-1-1h13 0 20v-1z" class="P"></path><path d="M384 747c-1-2-1-2 0-3h1c1 0 2 0 3-1h0l1 1c0 1-1 2-1 4l-2-1h-2z" class="B"></path><path d="M393 750l1-1c0-1 1-3 2-4 1 0 2 0 3 1v3c-1 0-2 1-3 1h0-3z" class="U"></path><path d="M369 750h3v-1h-2l1-2 1-1 1 1v1-1c1-1 1-2 0-3l1-1 1 1c1 2 2 1 2 4h1v-1c2-1 2-1 3-2l-1-1-1 1-1-1 1-1h2c1 1 1 1 1 2v4h1s2 0 3 1h-1c1 0 2 0 3 1h-3-2c-1 1-1 2-1 3l-2 1h-1l1-1-2-2c-2 0-3 0-5-1v-1l-1 1-3-1z" class="W"></path><path d="M373 750c3 0 7 1 10 1-1 1-1 2-1 3l-2 1h-1l1-1-2-2c-2 0-3 0-5-1v-1z" class="G"></path><path d="M408 741c3 0 5 1 8 1h-1c-6 2-10 6-15 9l-1 1c-2 0-3 0-5 2v2h-1v3l-2-2c1-2 1-3 0-4l-2-2h-1c-1-1-2-1-3-1h1c-1-1-3-1-3-1h-1v-4h0c1 1 1 2 1 3h1v-1h2l2 1c0-2 1-3 1-4 1 0 2 1 2 2l1 3v1h1 3 0c1 0 2-1 3-1 1-1 2-2 3-2h1c2 0 5-3 7-4l-2-1v-1z" class="k"></path><path d="M383 751h2 3 1l2 2c1 1 1 2 0 4l2 2v-3h1c1 1 1 8 0 10 0 1 0 3-1 4v7c-1 0-1-1-2-1h-1-1v4c-2 2-2 4-2 7h-2-1-1l1-7-1-1c0-1 0-2-1-3l-4-3c2-2 0-6 1-8 0-2 1-2 1-3-1 0-1 0-2-1 0-1-1-2-1-3 0-2 1-2 2-3h1l2-1c0-1 0-2 1-3z" class="Q"></path><path d="M385 751h3c-1 1-1 1-1 2v13l-2 4c0-6-1-13 0-19z" class="C"></path><path d="M388 751h1l2 2c1 1 1 2 0 4s-1 3-2 5v6h-1l-1-1v-1-13c0-1 0-1 1-2z" class="b"></path><path d="M385 770l2-4v1l1 1c0 2 0 3 1 5v1h-1l1 2v4c-2 2-2 4-2 7h-2-1-1l1-7-1-1c0-1 0-2-1-3 2-2 2-3 3-6z" class="D"></path><path d="M384 780v3 1h1v-2h0c0-1 0-1 1-2 0-1 1-2 1-3s1-2 1-3h0l1 2v4c-2 2-2 4-2 7h-2-1-1l1-7z" class="R"></path><path d="M391 757l2 2v-3h1c1 1 1 8 0 10 0 1 0 3-1 4v7c-1 0-1-1-2-1h-1-1l-1-2h1v-1c-1-2-1-3-1-5h1v-6c1-2 1-3 2-5z" class="Z"></path><path d="M390 776v-2c-1-1 0-1 0-2s0-2-1-3c2-3 2-7 2-11 1 4 1 8 2 12v7c-1 0-1-1-2-1h-1z" class="W"></path><path d="M424 582l8 2c1 1 3 2 4 2l9 17c0 1 1 1 1 3h0c-2 1-5 1-6 1s-1 0-1-1l-2 2h-2-1-2v1 1c-1-1-2-2-3-2s-2-1-3-1c-2 1-3 2-5 3 2-6 2-12 3-18 0-3 1-6 1-8-1-1-1-1-1-2h0z" class="E"></path><path d="M426 607c6-3 13-2 20-1-2 1-5 1-6 1s-1 0-1-1l-2 2h-2-1-2v1 1c-1-1-2-2-3-2s-2-1-3-1z" class="F"></path><path d="M444 684l1-1h0c1 1 2 1 3 1v2c1 0 2 1 3 1h1c0 1 1 3 0 4 1 1 1 1 2 1l1 1h0v1c3 1 4 2 5 4h2c2-1 3-1 5 0h0 7c0 1-1 1-2 1l-1 2h-2c-1 0 0 0-1 1-2 0-4 0-6 1l-2-1h0-1l-1 2h0l-1-1-2-2c0-1 0-1-1-1-2-1-2-1-3 0-2-1-2-1-3-2v-1h-3v2c1 1 2 1 4 3l2 1-1 2c-2 1-3 2-4 4l-1 4h1c-1 1-1 2-2 2l-2-1c0-2 1-4 0-6v-3h1c-1-2-1-4-1-6l-1-1-2 2c1 0 1 0 1 1h-3v-3l-1 1c-1 1-1 1-1 2-1 0-2 0-3 1l1 1c1-1 1-1 2-1v1h-4v-3l-1-1h-1c0-1-1-1 0-2 3-3 7-7 11-9 1-1 2-1 3-1l1 2h1l-1-5z" class="B"></path><path d="M445 699c1 1 2 1 4 3l2 1-1 2c-2 1-3 2-4 4l-1 4c-1-3 0-11 0-14z" class="e"></path><path d="M429 699c0-1-1-1 0-2 3-3 7-7 11-9 1-1 2-1 3-1l1 2v1c-1 1-1 2-1 3h-1l1 1-1 1-1-1h-1l2 2h-2c-2 0-4 1-6 1-2 1-3 1-5 2z" class="b"></path><path d="M444 684l1-1h0c1 1 2 1 3 1v2c1 0 2 1 3 1h1c0 1 1 3 0 4 1 1 1 1 2 1l1 1h0v1c3 1 4 2 5 4h2c2-1 3-1 5 0h0 7c0 1-1 1-2 1l-1 2h-2c-1 0 0 0-1 1-2 0-4 0-6 1l-2-1h0-1l-1 2h0l-1-1c1 0 1-1 1-1 0-1 0-3-1-4-3-2-8-6-11-6 0 2 0 3-1 4v-3l1-1-2-1c1-1 1-1 1-2l-1-5z" class="R"></path><path d="M448 686c1 0 2 1 3 1-1 2-1 3-1 5l-1-1c-1-2-1-3-1-5z" class="F"></path><path d="M451 687h1c0 1 1 3 0 4 1 1 1 1 2 1l1 1h0v1c-2-1-4-1-5-2 0-2 0-3 1-5z" class="S"></path><path d="M445 699v-2h3v1c1 1 1 1 3 2 1-1 1-1 3 0 1 0 1 0 1 1l2 2 1 1h0l1-2h1 0l2 1c2 0 3 0 5 1 2 0 2 1 3 2 1 0 3-1 4 0v3 2 3 3 4l-3-1c-3-1-3-1-6 1l-2 1h1l-3 2c1 1 1 1 2 3-1 0-2 0-2 1h-1l-2-7 1-1v-1-1s-2-2-3-2c0 0 0 1-1 1h-3v-1c-3 0-8 1-10 0l1-1h1c1 0 1-1 2-2h-1l1-4c1-2 2-3 4-4l1-2-2-1c-2-2-3-2-4-3z" class="b"></path><path d="M451 703l3 5h2l1-1h1 2v3s0 1-1 2c0-1-1-2-1-2h0c-2 0-3 0-4 1-1-2-2-3-3-4l-1-2 1-2z" class="D"></path><path d="M445 713l1-4c1-2 2-3 4-4l1 2 1 6h-6-1z" class="C"></path><path d="M460 702l2 1c2 0 3 0 5 1 2 0 2 1 3 2 1 0 3-1 4 0v3 2 3 3 4l-3-1c-3-1-3-1-6 1l-2 1h1l-3 2v-1-15c0-2 0-3-1-4h-1v-1l1-1z" class="e"></path><path d="M465 716l1-2h-2c2-2 4-1 7-1v-1c-1-1-2-1-3-1l-2 1h0l-1-1h0c0-2 0-2-1-4h5c1 2 2 1 3 2v2h0v2c0 2 0 3 1 4h1v4l-3-1c-3-1-3-1-6 1l-2 1v-3c0-2 1-2 2-3z" class="C"></path><path d="M465 716h1 4 1 1c0 2-1 2-1 4-3-1-3-1-6 1l-2 1v-3c0-2 1-2 2-3z" class="N"></path><path d="M434 584c11 3 21 5 31 12 2 1 3 4 4 6-1 3 0 5 0 7-1 1-1 2-2 2l-2-1c-4-2-8 0-12-1l1-1c1 0 1 0 2-1h1c-4 0-7 0-11-1 0-2-1-2-1-3l-9-17c-1 0-3-1-4-2h2z" class="V"></path><path d="M436 586c12 3 18 12 25 21h-4c-4 0-7 0-11-1 0-2-1-2-1-3l-9-17z" class="g"></path><path d="M494 183h-1c0-2 3-5 5-8l6-12c2-5 3-11 6-16h2c2 1 2 4 3 6 2 7 5 14 9 20 2 2 6 7 7 10 0 0-1 0-1-1-3 0-6-2-9-3-3 0-6-1-9-1s-5 1-7 1h-1c-3 1-7 2-10 4z" class="f"></path><path d="M512 178h0 5v-1c0-2 0-2-1-3h0v-1h1 4c0-1 0-1-1-2h0 1c1 1 2 1 3 2h0c2 2 6 7 7 10 0 0-1 0-1-1-3 0-6-2-9-3-3 0-6-1-9-1z" class="c"></path><path d="M442 731h4v1h-1c0 1 0 1 1 1h0v2h0v4c1 1 3 1 5 2l-1 2h1 1c-2 2-3 3-3 6-2 4-1 10-1 15v4h-2 0-1c0-2 0-4-1-6v-10c-1 0-2 1-2 1l-1 1-1 1c0-1-1-3-1-4l-1-1c0-1 0-1-1-1v1c-1 0-1 0-2-1v2h-1v-2c-2 0-2 1-3 2h0v-2-1 1l-3-1-4 1c-2 1-4 1-5 2l-2 1-1-1h0c1-1 3-1 4-2s1-4 1-5c1 2 1 2 1 4h2c-1-2-2-5-1-7 0-3-1-6-1-8 3 0 8-1 11-1 3-1 7 1 9-1z" class="l"></path><path d="M428 748l3-1c2-1 4-1 7-1h1v5l-1-1c0-1 0-1-1-1v1c-1 0-1 0-2-1v2h-1v-2c-2 0-2 1-3 2h0v-2-1 1l-3-1z" class="F"></path><path d="M422 733c3 0 8-1 11-1h4v1l-1 1c1 1 1 3 2 4v1h0v2c1 1 2 1 3 1v1l-1 1h-4c0-3 0-7-1-10-1 0-1 0-1 1s0 2-1 3v1h-1l-1 4v1l-2-2c-1 0-1 1-1 2h-1l-1 1v2l-2 1c-1-2-2-5-1-7 0-3-1-6-1-8z" class="S"></path><path d="M429 737v-3h2c1 1 0 1 0 2 0 0 1 1 2 1v2h-1-2s0-1-1-2h0z" class="B"></path><path d="M426 745l-1-6v-1h0c0-1 1-2 2-3v1l2 1h0c1 1 1 2 1 2h2l-1 4v1l-2-2c-1 0-1 1-1 2h-1l-1 1z" class="P"></path><path d="M442 731h4v1h-1c0 1 0 1 1 1h0v2h0v4c1 1 3 1 5 2l-1 2h1 1c-2 2-3 3-3 6-2 4-1 10-1 15v4h-2 0-1c0-2 0-4-1-6v-10c-1 0-2 1-2 1-1-2 1-5 0-8 0-1-1-1-2-1l1-1v-1c-1 0-2 0-3-1v-2h0v-1c-1-1-1-3-2-4l1-1v-1h-4c3-1 7 1 9-1z" class="M"></path><path d="M438 739l6 2c-1 1-1 2-2 2h-1v-1c-1 0-2 0-3-1v-2zm0-1c-1-1-1-3-2-4l1-1 2 1v1c1 1 2 1 3 1s0 0 1 1h1v2h-1c-1-1-3-1-5-1z" class="B"></path><path d="M450 743h1 1c-2 2-3 3-3 6-2 4-1 10-1 15v4h-2v-23c0-2 1-1 3-1 0-1 1-1 1-1z" class="b"></path><path d="M424 749l4-1 3 1v-1 1 2h0c1-1 1-2 3-2v2h1v-2c1 1 1 1 2 1v-1c1 0 1 0 1 1l1 1c0 1 1 3 1 4l1-1 1-1s1-1 2-1v10c1 2 1 4 1 6h1v2c-2 2-2 5-3 8v1h-5v1l-1-1c-1 0-2 0-3 1h6l-6 1v1l-2-1v-1h-1c-1 1-1 0-2 1l-2-4c-1 0-1 0-2 1 0 1 0 1 1 2l-1 1-2-1v-1c-1-2 0-3 0-5l-2 1v-3-2-2c0-4-1-15 1-17l1 1h1l1-1-1-2z" class="g"></path><path d="M424 749l4-1 3 1-1 1-1 1-1-1c-1 2 0 5 0 7v14c1 0 4 0 5 1-2 1-9-1-10 1v1h0l-2 1v-3-2-2c0-4-1-15 1-17l1 1h1l1-1-1-2z" class="B"></path><path d="M424 753v1h1v-1h1c1 4 1 14-1 17-1 1-1 1-2 0 2-2 1-8 0-10 0-3 1-5 1-7z" class="H"></path><path d="M421 768c0-4-1-15 1-17l1 1 1 1c0 2-1 4-1 7 1 2 2 8 0 10h-2v-2z" class="g"></path><path d="M437 750v-1c1 0 1 0 1 1l1 1c0 1 1 3 1 4l1-1 1-1s1-1 2-1v10c1 2 1 4 1 6h1v2c-2 2-2 5-3 8v1h-5-7l1-1v-1-1l2 2h1c1-2 0-5 0-7 0-6 1-13 0-20v-2c1 1 1 1 2 1z" class="H"></path><path d="M437 750v-1c1 0 1 0 1 1v20c0 2 1 6-1 8h-1v-17c0-4 0-7 1-11z" class="Z"></path><path d="M442 753s1-1 2-1v10c0 3 0 5-1 8l-1-1c-1 1-1 2-2 3-1-1 0-3 0-5v-12l1-1 1-1z" class="m"></path><path d="M442 718c3 0 7 0 11-1l6 3-1 1 2 7h1c0-1 1-1 2-1-1-2-1-2-2-3l3-2h-1l2-1c3-2 3-2 6-1l3 1v5c1 1 1 1 2 1h1 4 3 2c-2 1-4 1-6 1h0c2 1 3 1 5 1 1 0 2 0 3 1h7 0l1 1h10v1 2l-1 1 1 1h-1v-2-1l-1-1h-8v3l-1 1v1h-8c-6 0-12 1-18 0h-9-2-3-1-6l-2-2h0v-2h0c-1 0-1 0-1-1h1v-1h-4c-2-1-5 1-6 0 1-1 3-1 4-1h1l1-2h-1-4l-1-1 1-2c1-1 2-1 2-3l-1-1h0v-1h2l-2-2h4z" class="R"></path><path d="M447 725l2 1v-2h0l2 2c1-1 1-3 1-4h1v5h4l3 1h1 7c-2 1-5 1-7 1l-10 1c-4 0-7-1-10 0l1-2h-1 0l5-1c1-1 1-2 1-2z" class="V"></path><path d="M463 722l2-1c3-2 3-2 6-1l3 1v5c1 1 1 1 2 1v1c-2 0-7-1-8 0h-7c0-1 1-1 2-1-1-2-1-2-2-3l3-2h-1z" class="N"></path><path d="M463 722l2-1c3-2 3-2 6-1v2h-2v4h-5v-3-1h-1z" class="c"></path><path d="M446 731h1v1 1h1v-2c2-1 7 0 9 0h25l2 1c-1 1 0 2-1 2s-2 0-2 1c-1-1-1-2-1-3-1 1-1 2-2 3v-1-1-1l-2 1h-1 0c0 1-1 1-1 2l-2-2c-1 1-1 2-1 3h0c-1-1-1-3-1-3-1 0-1 0-1 1l-1-1h-1l-1 1-1-1-1 1h-1l-1-1c-1 1-1 1-1 2l1 1c2 0 6 0 7 1h-9-2-3-1-6l-2-2h0v-2h0c-1 0-1 0-1-1h1v-1z" class="a"></path><path d="M442 718c3 0 7 0 11-1l6 3-1 1 2 7-3-1h-4v-5h-1c0 1 0 3-1 4l-2-2h0v2l-2-1s0 1-1 2l-5 1h0-4l-1-1 1-2c1-1 2-1 2-3l-1-1h0v-1h2l-2-2h4z" class="F"></path><path d="M453 717l6 3-1 1h-2v3c0-1-1-1-1-2v-1c-1-1-2-2-2-4z" class="a"></path><path d="M456 724v-3h2l2 7-3-1v-1c0-1 0-1-1-2z" class="K"></path><path d="M440 720h1c1 0 2 0 4-1l1 2 1-1c1 0 1 0 2 1l-2 3v1s0 1-1 2l-5 1h0-4l-1-1 1-2c1-1 2-1 2-3l-1-1h0v-1h2z" class="B"></path><path d="M495 675h4l1 1c0-2 1-3 1-4h0c1 1 1 2 1 3h1l-1 2c3 0 6 0 10 1h0c2-1 3-2 5-1l2 1h5v1h-11-11c-1 0-1 1-1 2h-5c0 1 0 2-1 3 0-1 0-1-1-2 0-1-1-1-2-1-1 2-1 4-1 6h-1-5-1-1c-1 1-2 0-3 0v1 2c0 1 0 1-1 1 0 1 0 1-1 1v2l-1-1-1-2h-1v2c0 1-1 2-1 3l-1 1-1-1h-1c-1 1-3 1-4 2-2-1-3-1-5 0h-2c-1-2-2-3-5-4v-1h0l-1-1c-1 0-1 0-2-1 1-1 0-3 0-4h-1c-1 0-2-1-3-1v-2c-1 0-2 0-3-1h0l-1 1c-1-1-1-2-2-3l-1-1h-3c0-2 0-2 1-3 7-2 16-1 24-1h10c1 0 3 0 5-1h1 3s1 1 2 0c1 0 1 0 2 1 2-2 6-1 9-1z" class="l"></path><path d="M475 679h10c1 0 3 0 5 1-5 1-9 1-14 1 1-1 2-1 3-1h1l-5-1z" class="U"></path><path d="M485 687c0-2 1-3 2-5 1 0 2 0 3 1v4h-5z" class="C"></path><path d="M462 679h13l5 1h-1c-1 0-2 0-3 1-1 0-2 0-3 1-2 0-7-1-9-1l-2-2z" class="Y"></path><path d="M441 680l21-1 2 2c-3 0-8 1-10 0h-5-7l-1-1z" class="F"></path><path d="M464 688h1c2 0 3 0 4-1 1 0 1 1 2 0 2 0 4 0 6 1 1-1 1-1 2-1h1v1 2c0 1 0 1-1 1 0 1 0 1-1 1v2l-1-1-1-2h-1v2c0 1-1 2-1 3l-1 1-1-1h-1c-1 1-3 1-4 2-2-1-3-1-5 0h-2c-1-2-2-3-5-4v-1h0l-1-1c-1 0-1 0-2-1 1-1 0-3 0-4 4 0 9 0 12 1z" class="P"></path><path d="M455 693c0-2 0-2-1-4h1 1c1 1 2 1 4 1 1-1 1-1 2-1v3h2c1 1 1 1 1 2h-1-1c-1 1-1 2-1 4h-2c-1-2-2-3-5-4v-1z" class="B"></path><path d="M464 688h1c2 0 3 0 4-1 1 0 1 1 2 0 2 0 4 0 6 1 1-1 1-1 2-1h1v1 2c0 1 0 1-1 1 0 1 0 1-1 1v2l-1-1-1-2h-1v2h-3v2l-1-2c-1-1-1-1-1-2-1-2-1-2-2-3h-3-1z" class="Y"></path><path d="M495 675h4l1 1c0-2 1-3 1-4h0c1 1 1 2 1 3h1l-1 2c3 0 6 0 10 1h0c2-1 3-2 5-1l2 1h5v1h-11-11c-1 0-1 1-1 2h-5c-1-1-3 0-4 0s-2-1-2-1c-2-1-4-1-5-1h-10-13l-21 1h-3c0-2 0-2 1-3 7-2 16-1 24-1h10c1 0 3 0 5-1h1 3s1 1 2 0c1 0 1 0 2 1 2-2 6-1 9-1z" class="h"></path><path d="M485 679c1-1 3 0 5 0l11-1 1 1c-1 0-1 1-1 2h-5c-1-1-3 0-4 0s-2-1-2-1c-2-1-4-1-5-1z" class="k"></path><path d="M429 528c1-1 2-1 3-1h0l-1-1c-1-3 2-6 3-9l4-11 1 12v31c0 3 0 9-1 11h0l1 14c0 2 0 4-1 5h-2c1 2 2 2 3 3h-4v1l-1 1h-2l-8-2c-2-1-4-2-5-3v-2c-1-3 0-7 1-10 1-9 3-18 5-26l2-6 2-7z" class="o"></path><path d="M419 577c5 1 11 3 17 2 1 2 2 2 3 3h-4v1l-1 1h-2l-8-2c-2-1-4-2-5-3v-2z" class="M"></path><path d="M414 776c2-2 4-4 7-4v3l2-1c0 2-1 3 0 5v1l2 1 1-1c-1-1-1-1-1-2 1-1 1-1 2-1l2 4c1-1 1 0 2-1h1v1 4 2h-1-1c1 2 2 2 4 3h4c2 0 5 0 7 1h-5l-2 1h0l-3-1c-1 1 0 2 0 3h0v17 4h-2l-9 1v1h1v1s0 1 1 2h0-2v-1c-1-1-1 0-2 0h-2v1c-1 0-1 1-1 0l-2-1h-6c-1-1-1 0-2 0l-1-1c-1 1-2 1-3 1l1-1 1-1v-1l2-1h0-2c0-1-1-2-1-3h0v-7-7-2l-1-10v-2c1-2 2-2 4-2v-1l-1-1 2-1 4-3z" class="W"></path><path d="M413 787h-2v-1h2l1-1c1 0 3 2 4 3l-1 1 1 2h0l-1 2h-1 0c-1 1-1 2-1 3l1 1v-2h1c1 1 0 1 2 2l1-1v2 3 1l-2-2h0c-1 1-1 2-1 3l1 1-1 1v1h-3v-13-2-1-2l-1-1z" class="e"></path><path d="M405 786h1c0 1 1 2 1 2h0c1 0 1-1 1-2l1 1v1l1 1v-1h2l1-1 1 1v2 1 2 13c-2-1-3-1-5-1-1 0-1 0-2-1v-1-1c0-2 0-2-1-4v-2l-1-10z" class="E"></path><path d="M414 791l-1 2h-2c-1-1-1-1 0-3 1-1 1-1 3 0v1z" class="M"></path><path d="M409 805c2-2 1-3 2-6l2-1v-1l-2 1c-1-1-1-2-1-3 1-1 3-1 4-2v13c-2-1-3-1-5-1z" class="O"></path><path d="M414 776c2-2 4-4 7-4v3l2-1c0 2-1 3 0 5v1l2 1 1-1c-1-1-1-1-1-2 1-1 1-1 2-1l2 4c1-1 1 0 2-1h1v1 4c-1 0 0 0-1-1-1 0-1-1-2-1l-2 1h0c-1 0-2 0-2-1h-2c-2 2-2 10-2 13-1-2 0-10 0-13-2 0-5-1-7 0-1 0 0-1-1-1h-4v-1l-1-1 2-1 4-3z" class="P"></path><path d="M414 776c2-2 4-4 7-4v3 5c-1 0-1 1-2 1h-3-1-2-4l-1-1 2-1 4-3z" class="D"></path><path d="M410 779l4-3h0c0 1 1 1 1 2l-1 1h-4z" class="E"></path><path d="M421 796c0-3 0-11 2-13h2c0 1 1 1 2 1h0l2-1c1 0 1 1 2 1 1 1 0 1 1 1v2h-1-1c1 2 2 2 4 3h4c2 0 5 0 7 1h-5l-2 1h0l-3-1c-1 1 0 2 0 3h0v17c0-2 0-3-1-4h-1c-2-1-2-1-4-1h-5-1l-1 1v-2h0c0-3 0-6-1-9z" class="e"></path><path d="M426 796h2v5 3l-3 1 1-1c0-2 0-5-1-7l1-1z" class="D"></path><path d="M428 801h2c1 0 1 0 1 1l1 1v1h-2v1l-1 1h-5c1-1 0-1 1-1l3-1v-3z" class="W"></path><path d="M429 789l3 3v9 2l-1-1c0-1 0-1-1-1h-2v-5h-2l-3-1v-1l1-1 1 1v1h3v-1l-1-1 1-1c0-1 0-2 1-3z" class="g"></path><path d="M427 784l2-1c1 0 1 1 2 1 1 1 0 1 1 1v2h-1-1c1 2 2 2 4 3h4c2 0 5 0 7 1h-5l-2 1h0l-3-1c-1 1 0 2 0 3h0v17c0-2 0-3-1-4h-1c-2-1-2-1-4-1l1-1v-1h2v-1-2-9l-3-3c0-2-1-3-2-5z" class="O"></path><path d="M406 798c1 2 1 2 1 4v1 1c1 1 1 1 2 1 2 0 3 0 5 1h3v-1l1-1-1-1c0-1 0-2 1-3h0l2 2v2l2 1h0v2l1-1h1 5c2 0 2 0 4 1h1c1 1 1 2 1 4v4h-2l-9 1v1h1v1s0 1 1 2h0-2v-1c-1-1-1 0-2 0h-2v1c-1 0-1 1-1 0l-2-1h-6c-1-1-1 0-2 0l-1-1c-1 1-2 1-3 1l1-1 1-1v-1l2-1h0-2c0-1-1-2-1-3h0v-7-7z" class="D"></path><path d="M424 806h5c2 0 2 0 4 1h1c1 1 1 2 1 4v4h-2c-1-2-1-3-3-4h-1c1-1 1 0 2-1v-2c-1 0-1 1-2 2 0-1 0-2-1-3h-4l-1-1h1z" class="F"></path><path d="M409 815h12v-4-2-1l1 1-1 1c0 1 0 4 1 5l2 1v1h1v1s0 1 1 2h0-2v-1c-1-1-1 0-2 0h-2v1c-1 0-1 1-1 0l-2-1h-6c-1-1-1 0-2 0l-1-1c-1 1-2 1-3 1l1-1 1-1v-1l2-1z" class="P"></path><path d="M406 798c1 2 1 2 1 4v1 1c1 1 1 1 2 1 2 0 3 0 5 1h3l2 1h-2l1 1v1l-1 1h0c0 2 0 3 1 4v1h-9-2c0-1-1-2-1-3h0v-7-7z" class="C"></path><path d="M411 807c1 0 2 0 3 1h4v1l-1 1c-1 0-2 1-4 1v-2h-3c0-1 0-1 1-1v-1z" class="E"></path><path d="M413 811c-1 0-2 1-4 1l-1-1c0-1 0-2 1-2l-1-1v-2l1-1c0 2 0 2 2 2v1c-1 0-1 0-1 1h3v2z" class="X"></path><path d="M451 578c2 0 5 0 7 1l5 1 28 8 1 1h1v1l8 1c1 0 5 1 6 2 2 3 5 7 9 9 1 1 2 2 3 2v3h-10l-2 2c-1 1-1-1-2 1l-1-1h-1-2 0c-1 1-2 2-3 2l-3 1h-3l2-2v-1c-3 0-6 0-8-1h-1-1l1-1c-1-1-1-1-1-2 1-1 2 0 3 0 2 0 2 0 3-1l-3-3c-1 0-2 0-3-1h-2 0-2l2 6h1v3l-1 2-1-1-7 2h-1c-2-1-4-1-6-1 1 0 1-1 2-2 0-2-1-4 0-7-1-2-2-5-4-6-10-7-20-9-31-12l1-1v-1h4c3 0 6 1 9 1 1 1 2 1 4 1v-2h1 3 0c-2-1-3-2-5-4h0z" class="B"></path><path d="M485 593c-2-1-5-3-7-5 2 1 6 2 8 1h6l-1 1c1 2 3 4 4 6h0c-2-2-5-4-8-5l-2 2z" class="U"></path><path d="M451 578c2 0 5 0 7 1h0c-2 1-2 0-4 0h-1l3 2c1 1 1 1 0 2h2c1-1 2-1 3-1l1 1h2c1 1 1 2 2 2 2-1 3 0 4 0l1 1h-2c-1 1-2 1-3 2h-5v-2h-1c-1-1-1-1-2-1h0c-3 0-4 0-6-1v-2h1 3 0c-2-1-3-2-5-4h0z" class="K"></path><path d="M480 600h-1c0-2-2-3-2-5 1-1 2-1 3-2l-1-1c-1 1-2 1-3 1v-1h3 1c1 0 3 1 4 1 1 1 2 2 4 2 2 1 3 3 4 5h0c-2-1-3-2-4-3 0 1-1 2-1 4h0c-1 0-2 0-3-1h-2 0-2z" class="O"></path><path d="M484 593c1 1 2 2 4 2 2 1 3 3 4 5h0c-2-1-3-2-4-3 0 1-1 2-1 4-2-2-4-2-6-3v-1l4-1c1-1-1-1-1-3z" class="U"></path><path d="M485 593l2-2c3 1 6 3 8 5h0c2 1 3 3 4 5s2 5 4 7h-2l-2-2c-2-2-4-4-7-6-1-2-2-4-4-5l-3-2z" class="i"></path><path d="M487 601c0-2 1-3 1-4 1 1 2 2 4 3h0c3 2 5 4 7 6l2 2h2l6-1-2 2c-1 1-1-1-2 1l-1-1h-1-2 0c-1 1-2 2-3 2l-3 1h-3l2-2v-1c-3 0-6 0-8-1h-1-1l1-1c-1-1-1-1-1-2 1-1 2 0 3 0 2 0 2 0 3-1l-3-3h0z" class="n"></path><path d="M503 608l6-1-2 2c-1 1-1-1-2 1l-1-1h-1-2 0c-1 1-2 2-3 2l-3 1h-3l2-2v-1c-3 0-6 0-8-1h-1 16 2z" class="M"></path><path d="M469 602h0v-1-3c1-1 1-1 3-1 0 1-1 2-2 2l1 1 3-2c1 1 1 3 3 3 0 0 1 1 2 1 0 2 1 3 3 4h0 1v3l-1 2-1-1-7 2h-1c-2-1-4-1-6-1 1 0 1-1 2-2 0-2-1-4 0-7z" class="P"></path><path d="M469 609h1c0-1 0-4 1-5v1 3h1c1-2 1-4 1-6 1 1 1 0 1 1s1 2 1 2c1 1 2 2 3 2h0c2 0 1 2 3 2 0 1 1 0 0 1l-7 2h-1c-2-1-4-1-6-1 1 0 1-1 2-2z" class="H"></path><path d="M492 589l1 1 8 1c1 0 5 1 6 2 2 3 5 7 9 9 1 1 2 2 3 2v3h-10l-6 1c-2-2-3-5-4-7s-2-4-4-5c-1-2-3-4-4-6l1-1z" class="g"></path><path d="M369 750l3 1 1-1v1c2 1 3 1 5 1l2 2-1 1c-1 1-2 1-2 3 0 1 1 2 1 3 1 1 1 1 2 1 0 1-1 1-1 3-1 2 1 6-1 8l4 3c1 1 1 2 1 3l1 1-1 7h1 1 2c0-3 0-5 2-7v11 2c0 4-1 5 1 9h-5c-2 1-5 1-7 1h-3c-2 0-2 1-3 1-2 1-4 1-6 0h0l-7 1-1-1h-1v-2c-1-1-2-2-4-2h-1v-1l1-1 3-3v-4-1c0-2-1-3-3-4h0c0-2 0-2 1-4 2-3 5-7 7-10 0-1 1-1 2-2v-1-1c2-4 2-10 3-14l1-1s1-1 1-2h0l1-1z" class="b"></path><path d="M365 800h0c1-2 0-4 1-6h2c1 1 0 2 0 3v3 1c-1 1-1 1-2 1l-1-2z" class="C"></path><path d="M368 801c1 0 3 0 4 1v1c-2 1-3 1-5 1h-1 0 0l-7 1-1-1c3 0 6 0 8-1v-1c1 0 1 0 2-1z" class="E"></path><path d="M356 790h1c0-1-1-2-1-3h1l1 1c2-2 2-2 5-2h1v5 2h-4l-1 1c-1 1-1 1 0 2v2c-1 1-1 2 0 2 1 1 2 1 4 1 1-2 0-5 0-6h1c0 2 0 3 1 5l1 2v1c-2 1-5 1-8 1h-1v-2c-1-1-2-2-4-2h-1v-1l1-1 3-3v-4-1z" class="J"></path><path d="M389 780v11 2c0 4-1 5 1 9h-5c-2 1-5 1-7 1l-2-2h-2-1c-1-1 0-4 0-5-1-1-1-2-1-3 3-1 6 0 8-2l-2-1 1-2h1l1-1h2 0 1 1 2c0-3 0-5 2-7z" class="d"></path><path d="M389 780v11 2c0 4-1 5 1 9h-5 0c1 0 1-1 1-1 1-2 1-3 0-5 1-2 1-6 1-9s0-5 2-7z" class="i"></path><path d="M379 788h1c1 0 2 1 3 1 0 2 1 10 0 13-2-1-3-1-4-1v-7l-1-1v8c-2-1 0-7-2-8v1 7h-2-1c-1-1 0-4 0-5-1-1-1-2-1-3 3-1 6 0 8-2l-2-1 1-2z" class="P"></path><path d="M363 769h1l4-1c1 1 2 2 2 3 0 0-1 0-1 1 0 2 0 4 1 7l-1 1h-1c4 0 9 0 13-1h2l1 1-1 7h0-2l-1 1h-1 0l-1-2-2 1c0 1 0 3-1 4h-2v-2c1 0 1 0 1 1h1c1-2 0-3 0-6l-1 1c-3 1-7-1-9 1 0 2 0 3-1 5v-5h-1c-3 0-3 0-5 2l-1-1h-1c0 1 1 2 1 3h-1c0-2-1-3-3-4h0c0-2 0-2 1-4 2-3 5-7 7-10 0-1 1-1 2-2v-1z" class="F"></path><path d="M354 784c3-2 6-2 9-3 4 0 7 0 11 1-2 2-2 2-5 2-4 1-9 0-14 1h-1v-1z" class="E"></path><path d="M354 782c2-3 5-7 7-10v1c-1 3-3 6-5 8l6-1h6c4 0 9 0 13-1h2l1 1-1 7h0-2l-1 1h-1 0c1-1 0-3 0-3l2-1v-1c-1-2-3-1-5-1-1-1-1-1-2 0-4-1-7-1-11-1-3 1-6 1-9 3v-2z" class="S"></path><path d="M363 769h1l4-1c1 1 2 2 2 3 0 0-1 0-1 1 0 2 0 4 1 7l-1 1h-1-6l-6 1c2-2 4-5 5-8v-1c0-1 1-1 2-2v-1z" class="D"></path><path d="M363 770c1 0 2-1 3-1l1 2c0 1-1 1-2 2s-1 2-1 3c0 2-1 3-2 4l-6 1c2-2 4-5 5-8v-1c0-1 1-1 2-2z" class="E"></path><path d="M369 750l3 1 1-1v1c2 1 3 1 5 1l2 2-1 1c-1 1-2 1-2 3 0 1 1 2 1 3 1 1 1 1 2 1 0 1-1 1-1 3-1 2 1 6-1 8l4 3c1 1 1 2 1 3h-2c-4 1-9 1-13 1h1l1-1c-1-3-1-5-1-7 0-1 1-1 1-1 0-1-1-2-2-3l-4 1h-1v-1c2-4 2-10 3-14l1-1s1-1 1-2h0l1-1z" class="f"></path><path d="M369 750l3 1v5h-2l-1-1c-1 0-1-1-3-1l1-1s1-1 1-2h0l1-1z" class="T"></path><path d="M364 769c2-1 4-2 6-2 0-1 1 0 1 0v-1l-1-1h1 0l2 2c0 1 1 1 1 2 1 2 3 3 4 4l4 3c1 1 1 2 1 3h-2c-3-2-4-5-6-8l-1-1c-2 1-1 1-2 1-1-1-1-1-1-2h0c-1-1-1-1-3-1l-4 1z" class="V"></path><path d="M368 768c2 0 2 0 3 1h0c0 1 0 1 1 2 1 0 0 0 2-1l1 1c2 3 3 6 6 8-4 1-9 1-13 1h1l1-1c-1-3-1-5-1-7 0-1 1-1 1-1 0-1-1-2-2-3z" class="R"></path><path d="M375 771c2 3 3 6 6 8-4 1-9 1-13 1h1l5-1c0-1 0-3-1-4l-2-2h3l1-2z" class="k"></path><path d="M475 693v-2h1l1 2 1 1-1 1c1 1 2 1 2 1h2c1 0 1 1 2 1 0 0 0-1 1-1h3 4c1-1 1-1 2-1l2 2c1-1 3-2 4-3 3 1 4 1 6 1h4 0l2 3c-1 0-1 1-2 1h-3c1 0 2 1 3 2 1-2 3-2 6-2 3 1 5 2 8 4l1 1c2 2 4 3 4 7 1 2 0 5 0 7-1 4-3 6-6 8l-4 1-3 1c-3 0-9-1-12 0-2 1-7 0-8 2h-7c-1-1-2-1-3-1-2 0-3 0-5-1h0c2 0 4 0 6-1h-2-3-4-1c-1 0-1 0-2-1v-5-4-3-3-2-3c-1-1-3 0-4 0-1-1-1-2-3-2-2-1-3-1-5-1 2-1 4-1 6-1 1-1 0-1 1-1h2l1-2c1 0 2 0 2-1h-7 0c1-1 3-1 4-2h1l1 1 1-1c0-1 1-2 1-3z" class="K"></path><path d="M482 723h0l1 1v2h-1l-2-1v-1c1 0 1-1 2-1z" class="F"></path><path d="M491 707l2 1c0 2-1 6 0 8-1 1-1 2-2 2v-11z" class="Q"></path><path d="M477 710c1-2 1-5 4-6 0 0 1 0 1-1l1 1-1 1c-1 1-1 3-1 4s1 1 1 2c-2 0-3 0-5-1z" class="b"></path><path d="M477 710c2 1 3 1 5 1 1 1 1 4 1 6-1 0-1 0-2-1h-2 0v-1-3c-2 0-3 1-5 2v-3l3-1z" class="M"></path><path d="M484 727l1-2c-1-1-1-3-1-4 2-2 0-6 1-8 2 0 3-1 4 1v1h-1l-2 1c0 2 0 8 1 10l-1 1h-2z" class="H"></path><path d="M486 727l1-1c-1-2-1-8-1-10l2-1v1 2 4 1c1 1-1 1 1 2v1h-1c0 1 1 1 2 1s0 0 1-1l1 1h8 0 2l1 1c-2 1-7 0-8 2h-7c-1-1-2-1-3-1-2 0-3 0-5-1h0c2 0 4 0 6-1z" class="e"></path><path d="M474 714c2-1 3-2 5-2v3 1h-1c0 3 0 8-1 11h-1c-1 0-1 0-2-1v-5-4-3z" class="F"></path><path d="M493 716c2 2-1 3 2 3v1 1c1 0 3-1 4 1s0 3 1 4v1h-8l-1-1v-8c1 0 1-1 2-2z" class="T"></path><path d="M497 706h3l-1 3h1c-2 1-3 3-3 5-1 4 5 8 5 13h-2 0v-1c-1-1 0-2-1-4s-3-1-4-1v-1-1c-3 0 0-1-2-3-1-2 0-6 0-8l1-2 2 1 1-1z" class="c"></path><path d="M475 693v-2h1l1 2 1 1-1 1c1 1 2 1 2 1h2c1 0 1 1 2 1 0 0 0-1 1-1h3 4c1-1 1-1 2-1l2 2c1-1 3-2 4-3 3 1 4 1 6 1h4 0l2 3c-1 0-1 1-2 1h-3c1 0 2 1 3 2l-5 3-5 5 1-3h-3l-1 1-2-1-1 2-2-1c-2-3-4-5-7-6s-6 0-9 3c0 0-1 1-1 2v3-3c-1-1-3 0-4 0-1-1-1-2-3-2-2-1-3-1-5-1 2-1 4-1 6-1 1-1 0-1 1-1h2l1-2c1 0 2 0 2-1h-7 0c1-1 3-1 4-2h1l1 1 1-1c0-1 1-2 1-3z" class="D"></path><path d="M475 693v-2h1l1 2 1 1-1 1c1 1 2 1 2 1h2c1 0 1 1 2 1 0 0 0-1 1-1h3 4c1-1 1-1 2-1l2 2c1-1 3-2 4-3 3 1 4 1 6 1h4 0l2 3c-1 0-1 1-2 1-2-1-4-1-6-2h0c-1-1-1-1-2 0-2 0-4 0-5 1h-1l-1-1c-1 0-1 0-2 1-3 0-4 1-6-1 0 0-1 1-1 2-3 0-4-1-7-1l-1 1c-1 0-1 0-3-1h-7 0c1-1 3-1 4-2h1l1 1 1-1c0-1 1-2 1-3z" class="i"></path><path d="M475 693v-2h1l1 2 1 1-1 1c1 1 2 1 2 1v1h-2c-1 0-1-1-3-1 0-1 1-2 1-3z" class="O"></path><path d="M484 701c2-1 3 0 5 0l-1-2h0c2 0 6 0 8 1 1-1 5-2 7-2l3 1c1 0 2 1 3 2l-5 3-5 5 1-3h-3l-1 1-2-1-1 2-2-1c-2-3-4-5-7-6z" class="E"></path><path d="M497 706l-1-2-3 1c-2 0-2 0-2-1l1-2h1c2 0 3-1 5-1 2-1 3 0 4-1s2-1 3-1l1 1c-1 1 0 1-1 1-1 1-1 2-1 3l-5 5 1-3h-3z" class="J"></path><path d="M509 701c1-2 3-2 6-2 3 1 5 2 8 4l1 1c2 2 4 3 4 7 1 2 0 5 0 7-1 4-3 6-6 8l-4 1-3 1c-3 0-9-1-12 0l-1-1c0-5-6-9-5-13 0-2 1-4 3-5h-1l5-5 5-3z" class="V"></path><path d="M516 709c1 1 2 1 3 2v2c0 2 0 2-2 3h-2l-1-3c0-2 1-3 2-4z" class="a"></path><path d="M509 701c1-2 3-2 6-2 3 1 5 2 8 4l1 1c2 2 4 3 4 7 0-1-1-1-1-1 0-1 0-2-1-2-1 1 0 2 0 3-2-3-3-4-5-6-1-1-1-1-2-1-1-1 0-1-1-1l-1-1h-1c-2-1-2-1-4-1-2 1-3 1-4 2-2 2-6 4-8 6h0-1l5-5 5-3z" class="Y"></path><path d="M521 715v-3-1-3h1c0 1 1 1 1 2 1 1 1 2 2 3 1-1 0-1 1-2 0-1-1-2 0-3 1 0 1 1 1 2 0 0 1 0 1 1 1 2 0 5 0 7-1 4-3 6-6 8l-4 1-3 1v-3-2c0-1-1-1-1-1 0-1 0-3 1-4h1c2 0 3-1 4-2l1-1z" class="U"></path><path d="M521 715c1 0 1 0 3 1l-1 2c1 1 1 1 2 1 0 2 0 2-2 4h-1c0-1 1-1 1-3h0l-3 3h-1v-3h-1-1l1 7-3 1v-3-2c0-1-1-1-1-1 0-1 0-3 1-4h1c2 0 3-1 4-2l1-1z" class="Y"></path><path d="M515 725c1-1 1-2 1-3s0-2 1-3v1l1 7-3 1v-3z" class="F"></path><path d="M406 796v2 7 7h0c0 1 1 2 1 3h2 0l-2 1v1l-1 1-1 1c1 0 2 0 3-1l1 1c1 0 1-1 2 0h6l2 1c0 1 0 0 1 0v-1h2c1 0 1-1 2 0v1h2c0 2-1 2-2 4-1 4-4 7-6 11-1 2-2 4-2 7l-1 3-2 1-1-2 1-1-1-1h-2l-1-1-3 1-1-1-1 1-1-1h-1-2v-1c0-2 0-2-2-4h-6-5-1-8-5-1c-1-1-1-1-2-1l1-1-1-1 1-1c1 0 2 1 4 0h1c0-3 1-7 0-10-5 0-4 0-5 5v2c-1-2-1-5-1-7h-3v3c-2 1-5 1-7 1-1-2 0-5-1-7v-2h1c-1-1-1-1-1-2v-1-4c-1-1 0-3 0-4v-1l7-1h0c2 1 4 1 6 0 1 0 1-1 3-1h3c2 0 5 0 7-1h5 5c0 1 0 0 1 1l1-1c2-1 4 0 6-1 1 0 2-1 2-1l1-4z" class="D"></path><path d="M384 830h-1v-1l-1-1c-1-2-1-2-1-4v-1c-2 2 0 5-2 7h-1l-1-8h7v1 7z" class="a"></path><path d="M390 817c3 0 7-1 10 0 1 1 2 1 4 1l1-1 1 1-1 1c1 0 2 0 3-1l1 1c1 0 1-1 2 0h6l2 1-4 1h0c-1 0-1-1-2-1-3 1-6 1-9 1h-4c-1 2-1 8 0 10v1h-2-2l-1-2v-5-4h3l1-1v-2c-2 0-3 0-5 1h0l-1-1-2 1-1-2z" class="b"></path><path d="M395 825v-4h3c1 2 0 9 0 11h-2l-1-2v-5z" class="a"></path><path d="M384 823l1-1h2c0 3-1 6 1 9h3 1v-1-2h2v-1c0-1 0-1 1-2v5l1 2h2 2l2-1c0 2 0 3-1 4-3 0-6 0-9 1h-5-1-8-5-1c-1-1-1-1-2-1l1-1-1-1 1-1c1 0 2 1 4 0h1 4s1-1 2-1v1h1 1v-2-7z" class="C"></path><path d="M395 830l1 2h2 2l2-1c0 2 0 3-1 4-3 0-6 0-9 1h-5-1-8c1-1 2-2 3-2 2 1 3 0 5 0 2 1 3 0 5 0h2l1-1c0-1 1-1 1-3z" class="I"></path><path d="M372 804h2c1 1 1 3 1 4 0 2-1 4 0 6 1 0 2 0 3-1h4s-1 1-1 2h-2l1 2c0 1-2 2-2 2h-11v3 3c-2 1-5 1-7 1-1-2 0-5-1-7v-2h1c-1-1-1-1-1-2v-1-4c-1-1 0-3 0-4v-1l7-1h0c2 1 4 1 6 0z" class="b"></path><path d="M366 804c2 1 4 1 6 0v2l2 2-1 1c-2 0-2 0-3 1 2 1 3 1 3 3l-2 2c-1 1-3 1-4 1v1h0v2 3h-2v-2l1-1s0-1-1-2v-1l2-2v-5c-1-2 0-3-1-5h0z" class="X"></path><path d="M359 805l7-1c1 2 0 3 1 5v5l-2 2v1c1 1 1 2 1 2l-1 1v2h2v3c-2 1-5 1-7 1-1-2 0-5-1-7v-2h1c-1-1-1-1-1-2v-1-4c-1-1 0-3 0-4v-1z" class="L"></path><path d="M359 806l2 2c-1 2-1 3 0 5h1l1-1v3h0c-1 1-1 1-1 2l-2 2c1 1 1 1 2 1s2-1 3 0v2h2v3c-2 1-5 1-7 1-1-2 0-5-1-7v-2h1c-1-1-1-1-1-2v-1-4c-1-1 0-3 0-4z" class="G"></path><path d="M406 796v2 7 7h0c0 1 1 2 1 3h2 0l-2 1v1l-1 1-1-1-1 1c-2 0-3 0-4-1-3-1-7 0-10 0-2-1-5 1-7-1 1-1 1-2 1-3h0-2-4c-1 1-2 1-3 1-1-2 0-4 0-6 0-1 0-3-1-4h-2c1 0 1-1 3-1h3c2 0 5 0 7-1h5 5c0 1 0 0 1 1l1-1c2-1 4 0 6-1 1 0 2-1 2-1l1-4z" class="F"></path><path d="M383 811l-1-1c-1-1-1-3 0-4v-2h2 0c2 0 3 0 4 1h-2-1v1h2c1 0 0 0 1 1h1v-1-1l1-1h5v2c0 1 0 0 1 1l1-1h1v-1l1-1c0 1 0 2 1 2v-2h2l1 1h-1l-1 1h1 1c0 1 0 1-1 2 1 0 1 0 2 1h0c-1 1-2 1-3 1h-3-3c-1 0-3 0-4 1v-1l-1 1-1-1-4 1h-2z" class="i"></path><path d="M402 804l4 1v7h0c0 1 1 2 1 3h2 0l-2 1v1l-1 1-1-1-1 1c-2 0-3 0-4-1-3-1-7 0-10 0-2-1-5 1-7-1 1-1 1-2 1-3h0-2-4c1 0 1-1 3-1 0-1 0-1 2-1h2l4-1 1 1 1-1v1c1-1 3-1 4-1h3 3c1 0 2 0 3-1h0c-1-1-1-1-2-1 1-1 1-1 1-2h-1-1l1-1h1l-1-1z" class="H"></path><path d="M384 813c6 0 18-1 22 3-2 1-4 1-6 1-3-1-7 0-10 0-2-1-5 1-7-1 1-1 1-2 1-3h0z" class="I"></path><path d="M422 819c1 0 1-1 2 0v1h2c0 2-1 2-2 4-1 4-4 7-6 11-1 2-2 4-2 7l-1 3-2 1-1-2 1-1-1-1h-2l-1-1-3 1-1-1-1 1-1-1h-1-2v-1c0-2 0-2-2-4h-6c3-1 6-1 9-1 1-1 1-2 1-4l-2 1v-1c-1-2-1-8 0-10h4c3 0 6 0 9-1 1 0 1 1 2 1h0l4-1c0 1 0 0 1 0v-1h2z" class="W"></path><path d="M400 832v-1c-1-2-1-8 0-10h4l1 1v9h-2c0-2 0-4 1-6 0-1-1-2-1-2h-1c0 1 0 1 1 2h0c-1 2-1 4-1 6l-2 1z" class="F"></path><path d="M416 832l2 3c-1 2-2 4-2 7l-1 3-2 1-1-2 1-1-1-1h-2l-1-1-3 1-1-1-1 1-1-1h-1-2v-1c0-2 0-2-2-4h12c2 0 4 0 6-1v-2-1z" class="M"></path><path d="M400 840c1-1 1-2 2-2h2s1 0 2-1c1 1 1 2 1 3-1 1-1 1-2 1l-1 1-1-1h-1-2v-1z" class="S"></path><path d="M409 841l-1-1v-2h3c1 0 2 0 3-1 1 1 1 1 1 3l1 2-1 3-2 1-1-2 1-1-1-1h-2l-1-1z" class="B"></path><path d="M422 819c1 0 1-1 2 0v1h2c0 2-1 2-2 4-1 4-4 7-6 11l-2-3h0c-1 0-3 0-4-1v-6l-1-1c0 2 1 5 0 7h-4l-1-1 1-2c-1-1-1-3-1-4v-1l-1-1-1-1c3 0 6 0 9-1 1 0 1 1 2 1h0l4-1c0 1 0 0 1 0v-1h2z" class="H"></path><path d="M422 819c1 0 1-1 2 0v1h2c0 2-1 2-2 4-1 4-4 7-6 11l-2-3h0c-1-1-1-2-1-3l1-1c0 1 0 1 1 2l1-1c0-2 0-2-1-3h-2v-5l4-1c0 1 0 0 1 0v-1h2z" class="B"></path><path d="M419 820c0 1 0 0 1 0v-1h2c-1 1-2 1-2 2 0 2 0 3-1 5 0 1 0 2 1 3h-1-1c0-2 0-2-1-3h-2v-5l4-1z" class="K"></path><path d="M480 600h2 0 2c1 1 2 1 3 1l3 3c-1 1-1 1-3 1-1 0-2-1-3 0 0 1 0 1 1 2l-1 1h1 1c2 1 5 1 8 1v1l-2 2h3l3-1c-1 2-2 3-3 4 0 1 1 1 2 2l3-3-1 5v1l-1 5v7 4l-2-1h-1v2c0 1 0 1-1 1h-1c-2 1-3 1-5 1h-1l-4 1v1h3 1l-1 2c0 1 1 1 2 2v1c0 2 1 4 1 6 1 1 1 2 1 3 1 2 0 3 0 4v1c1 1 1 2 1 3h-1v1 1l-1 1h0v3 2c-1 1 0 1-1 1h-3c-1 1-1 2-2 2h-1c-1 1-2 1-3 1h0-1c-2 1-4 1-5 1h-10v-1h-3 0l-1-2h-2c-1-1-1-2-2-4h2c1 0 2-1 2-2l-4 1v-1c0-4-1-9 1-12l1-1v-1c2 0 5 0 7-1h0 2l-1-1c0-1-1-2-1-4 0 0-1-1-2-1 0-1 2-3 3-3l-2-1 1-1c-2 1-5 2-7 2v-1h0s1 0 0-1h0l3-7-1-1 1-2v-2l-2-1v-1c1-2 7-6 9-7l3-3h0c0-1 0 0-1-1h0c0-1 1-1 1-2l1 1 3-3 7-2 1 1 1-2v-3h-1l-2-6z" class="U"></path><path d="M482 637c2-1 2-1 4-1v1c-1 1-2 2-4 2v-2z" class="F"></path><path d="M479 637c1 1 1 2 2 3h1c0 1-1 2-2 3h-2c-1-1 1-2 1-3v-3z" class="Y"></path><path d="M494 623c0-1 1-2 2-3 1 2-1 3 2 5v7 4l-2-1h-1v-10l-1-1v-1z" class="O"></path><path d="M482 632h1c3-3 5-6 8-9h3v1h0c-1 2-1 5-1 7h-2l-2 1 1 1 1-1c0 1 0 2 1 3l-2 2h-4v-1c-2 0-2 0-4 1v-1c1-2 1-3 0-4z" class="K"></path><path d="M462 641c1-2 2-3 4-5l4-4 1 1-1 1v1c1 2-1 5-2 7 1 1 1 1 1 2 1-1 1-2 3-3v1s-1 2-2 2h-1v2l2-1h0v1c0 1-1 1-1 2l-2 3c-1 0-2-1-3 0 0-1-1-2-1-4 0 0-1-1-2-1 0-1 2-3 3-3l-2-1 1-1c-2 1-5 2-7 2v-1h0 2c1 0 1-1 3-1z" class="h"></path><path d="M465 643l1-1c1 1 1 1 1 3h0c-1 1-2 1-3 2 0 0-1-1-2-1 0-1 2-3 3-3z" class="I"></path><path d="M492 612h3l3-1c-1 2-2 3-3 4 0 1 1 1 2 2l3-3-1 5v1l-1 5c-3-2-1-3-2-5-1 1-2 2-2 3h-3c-3 3-5 6-8 9h-1v-1-8c0-2 2-7 4-8s4-1 6-3z" class="Y"></path><path d="M491 620l2-2h1l-3 5c-3 3-5 6-8 9h-1v-1c1-3 2-4 4-7h0l-2 1-1-1c1-1 3-2 4-2h0l-1 1 1 1c1-2 3-3 4-4z" class="n"></path><path d="M495 615c0 1 1 1 2 2l3-3-1 5v1l-1 5c-3-2-1-3-2-5-1 1-2 2-2 3h-3l3-5h-1l-2 2-1-1 5-4z" class="F"></path><path d="M484 608h1 1c2 1 5 1 8 1v1l-2 2c-2 2-4 2-6 3s-4 6-4 8v8 1c1 1 1 2 0 4v1 2 1h-1c-1-1-1-2-2-3v-2l-1-1 1-1v-2-2-3c-1 1-1 0-2 1h0l-1 2h0l-2-1 1-1-1-2c1-1 1-2 1-4h-1c1-2 2-3 3-4h3 0c1-3 3-7 4-9z" class="B"></path><path d="M475 621h-1c1-2 2-3 3-4h3c-2 3-3 7-5 10l-1-2c1-1 1-2 1-4z" class="Z"></path><path d="M473 646c3-2 7-4 11-3l3 3 2 8v7l1 2v1l-5 1-3-2h-5v-2h2l1-1c-1-2-1-5-1-7 1 0 2-1 3-1l-1-1h-3 0c-2 1-4 0-6-1 1-1 0-1 1-1v-1-2z" class="T"></path><path d="M489 661l1 2v1l-5 1-3-2 2-1h4 0l1-1z" class="J"></path><path d="M489 661h0c-2-1-2-3-2-5-1 0-2 0-3-1 0-1 1-2 2-3 1 1 2 1 3 2v7z" class="G"></path><path d="M473 646c3-2 7-4 11-3l3 3 2 8c-1-1-2-1-3-2 0-1 0-2-1-3 0-1-6-1-8-2v1c1 1 1 1 2 1 2 0 3 1 5 2l-1 1h-1l-1-1h-3 0c-2 1-4 0-6-1 1-1 0-1 1-1v-1-2z" class="E"></path><path d="M480 600h2 0 2c1 1 2 1 3 1l3 3c-1 1-1 1-3 1-1 0-2-1-3 0 0 1 0 1 1 2l-1 1-4 9h0-3c-1 1-2 2-3 4h1c0 2 0 3-1 4l1 2-1 1-2 4c0 1-1 2-2 3v-1l1-1-1-1-4 4c-2 2-3 3-4 5-2 0-2 1-3 1h-2s1 0 0-1h0l3-7-1-1 1-2v-2l-2-1v-1c1-2 7-6 9-7l3-3h0c0-1 0 0-1-1h0c0-1 1-1 1-2l1 1 3-3 7-2 1 1 1-2v-3h-1l-2-6z" class="U"></path><path d="M480 600h2 0 2c-1 1-1 2-1 3 1 1 0 2 0 3h-1l-2-6z" class="a"></path><path d="M466 627l1 1-1 2h1v1l-1 1h0-1c-2 1-2 1-4 1 1-2 4-4 5-6z" class="Z"></path><path d="M466 627l5-5h1c0 1-1 2-1 3-1 2-2 4-4 6v-1h-1l1-2-1-1z" class="l"></path><path d="M481 610l1 1-2 2c-2 1-3 2-4 3l-6 1c0-1 0 0-1-1h0c0-1 1-1 1-2l1 1 3-3 7-2z" class="B"></path><path d="M470 617l6-1c-6 5-11 10-16 15v-2l-2-1v-1c1-2 7-6 9-7l3-3h0z" class="M"></path><path d="M475 621c0 2 0 3-1 4l1 2-1 1-2 4c0 1-1 2-2 3v-1l1-1-1-1-4 4c-2 2-3 3-4 5-2 0-2 1-3 1h-2s1 0 0-1h0l3-7 1-1c2 0 2 0 4-1h1 0c3-1 4-3 6-6l3-5z" class="l"></path><path d="M461 633c2 0 2 0 4-1h1l-2 5-3 3 1 1c-2 0-2 1-3 1h-2s1 0 0-1h0l3-7 1-1z" class="i"></path><path d="M461 640h-1-1c1-2 2-5 4-6 0 1 1 2 1 3l-3 3z" class="d"></path><path d="M473 646v2 1c-1 0 0 0-1 1 2 1 4 2 6 1h0 3l1 1c-1 0-2 1-3 1 0 2 0 5 1 7l-1 1h-2v2h5l3 2 5-1v1l-1 1h0v3 2c-1 1 0 1-1 1h-3c-1 1-1 2-2 2h-1c-1 1-2 1-3 1h0-1c-2 1-4 1-5 1h-10v-1h-3 0l-1-2h-2c-1-1-1-2-2-4h2c1 0 2-1 2-2l-4 1v-1c0-4-1-9 1-12l1-1v-1c2 0 5 0 7-1h0 2l-1-1c1-1 2 0 3 0l2-3 3-2z" class="C"></path><path d="M469 664c0-1 0-1-1-2 1-1 1-1 2-1l1-1v1c1 2 2 2 4 2h0l-2 3h-1c-1-1-1-1-3-2z" class="Q"></path><path d="M457 653c2 0 5 0 7-1 1 1 2 1 2 2v3c-1-1-1 0-1-1-2 0-3 1-4 2v3 1h1c1 0 1 0 2-1h1l1 2h0c1 1 1 0 2 1h1c2 1 2 1 3 2h0c-2 1-3 0-3 1h-2-4-2-2l-4 1v-1c0-4-1-9 1-12l1-1v-1z" class="J"></path><path d="M459 665l-1-1c0-1 0-1 1-2h2 1v3h-3zm-2-12c2 0 5 0 7-1 1 1 2 1 2 2v3c-1-1-1 0-1-1-3 0-6-1-8-2v-1z" class="X"></path><path d="M455 668v-1c0-4-1-9 1-12v2h0c0 2 1 3 1 4 0 2-1 2 0 4h2 3v-3c1 0 1 0 2-1h1l1 2h0c1 1 1 0 2 1h1c2 1 2 1 3 2h0c-2 1-3 0-3 1h-2-4-2-2l-4 1z" class="E"></path><path d="M477 663h5l3 2 5-1v1l-1 1h0v3 2c-1 1 0 1-1 1h-3c-1 1-1 2-2 2h-1c-1 1-2 1-3 1h0-1c-2 1-4 1-5 1h-10v-1h-3 0l-1-2h-2c-1-1-1-2-2-4h2c1 0 2-1 2-2h2 2 4 2c0-1 1 0 3-1h0 1 3c1-1 1-2 1-3z" class="Z"></path><path d="M467 667h2c0-1 1 0 3-1v2l-1 2c-1-1-1-1-2 0h0l-2-1v-2z" class="D"></path><path d="M461 667h2l1 2c0 2 0 4-1 5h-1c0-3 0-5-1-7h0z" class="F"></path><path d="M477 663h5l3 2 5-1v1l-1 1h0v3 2c-1 1 0 1-1 1h-3c-1 1-1 2-2 2h-1c-1 1-2 1-3 1h0-1v-8h-2c-1 0-2 0-3 1h4v1h-3c-1 1 0 0-1 0l-1-1v-2h0 1 3c1-1 1-2 1-3z" class="Y"></path><path d="M477 663h5l3 2-9 1c1-1 1-2 1-3z" class="Q"></path><path d="M485 672h-3l-1-6h8 0v3 2c-1 1 0 1-1 1h-3z" class="D"></path><path d="M489 666v3c0 1-1 1-2 1s-4 0-5-1v-1h2c3 0 3 0 5-2z" class="J"></path><path d="M512 178c3 0 6 1 9 1 3 1 6 3 9 3-1 1-2 1-3 1 3 2 6 4 8 7s3 5 5 8l9 14c0 2 1 5 2 8l2 2-1 1h-1v-1c-2 0-2 0-3 1l-2-1h-1l-1 1c0 1 1 1 2 1 0 1 1 4 1 5 1 1 1 2 2 3h0l-13-4c-1 0-3 0-5-1-1 0-3 0-4-1-2 0-4-1-6-1l-10 1c-1 0-1 1-2 1s-2 1-2 1c-2 0-2 0-4 2h-1c-7 2-14 3-22 6v1c-1 0-1 1-2 1h0c1 0 2 1 2 1h1 1l-1 1-13 5c-3 1-5 4-7 6l-1-1-3 3-1-1c0-1 0-2 1-3 2-7 5-15 8-21 7-13 14-25 22-36 3-4 4-6 8-8l-1-1c3-2 7-3 10-4h1c2 0 4-1 7-1z" class="o"></path><path d="M517 185c1 0 2-1 3 0h4l6 3h1l1 1c1 1 2 2 2 3h-1l-1 1-3-1s-1-1-1-2h-2 2v-1l-6-3h0-1c-1 0-2-1-3-1h-1z" class="J"></path><path d="M531 206c1 1 2 2 2 4l3 3c0 2 0 2-1 3 1 1 2 1 4 2 1 1 2 1 3 1 1 2 2 2 3 3l-1 1c0 1 1 1 2 1 0 1 1 4 1 5 1 1 1 2 2 3h0l-13-4c-1 0-3 0-5-1-1 0-3 0-4-1 2-1 2-1 5-1 1 0 1 0 2-1-1-1-4-2-5-3h0l-2 1v-1h-1l1-2h2l2 1h3v-1c-1-1-2-1-2-2-2-2-1-2 0-4v-2h0c0-2-1-3-1-4v-1z" class="G"></path><path d="M540 224s1-1 2-1c1 1 1 1 1 2s0 2-1 3l-1-1c-1 0-1-2-1-3z" class="L"></path><path d="M536 228c1-1 2-2 3-4v-1h1v1c0 1 0 3 1 3l1 1 5 1c1 1 1 2 2 3h0l-13-4z" class="N"></path><path d="M512 178c3 0 6 1 9 1 3 1 6 3 9 3-1 1-2 1-3 1 3 2 6 4 8 7l-3-1-1-1h-1l-6-3h-4c-1-1-2 0-3 0-4 1-9-1-13 1-1 0-3 0-4 1h-2c-1 0-2 1-4 1-1 1-2 2-3 2h-1c-1 1-2 1-3 2 3-4 4-6 8-8l-1-1c3-2 7-3 10-4h1c2 0 4-1 7-1z" class="L"></path><path d="M512 178c3 0 6 1 9 1 3 1 6 3 9 3-1 1-2 1-3 1-12-3-20-3-32 1l-1-1c3-2 7-3 10-4h1c2 0 4-1 7-1z" class="V"></path><path d="M457 249l1-1c6-7 15-13 24-17l17-4c1-2 1-3 1-4v-1l1 5c3-1 6-1 10-1-1 0-1 1-2 1s-2 1-2 1c-2 0-2 0-4 2h-1c-7 2-14 3-22 6v1c-1 0-1 1-2 1h0c1 0 2 1 2 1h1 1l-1 1-13 5c-3 1-5 4-7 6l-1-1-3 3-1-1c0-1 0-2 1-3z" class="a"></path><path d="M480 236v1c-1 0-1 1-2 1h0c1 0 2 1 2 1h1 1l-1 1-13 5c-3 1-5 4-7 6l-1-1c6-6 12-11 20-14z" class="Q"></path><path d="M532 189l3 1 5 8 9 14c0 2 1 5 2 8l2 2-1 1h-1v-1c-2 0-2 0-3 1l-2-1h-1c-1-1-2-1-3-3-1 0-2 0-3-1-2-1-3-1-4-2 1-1 1-1 1-3l-3-3c0-2-1-3-2-4 0-3-1-6-1-9-1 0-1-1-1-1-1-1-1-2 0-4l3 1 1-1h1c0-1-1-2-2-3z" class="I"></path><path d="M530 197c4 3 8 7 10 12h-1c1 2 3 4 3 6-1 1-2 0-1 2l1 2c-1 0-2 0-3-1-2-1-3-1-4-2 1-1 1-1 1-3l-3-3c0-2-1-3-2-4 0-3-1-6-1-9z" class="J"></path><path d="M545 222h1l2 1c1-1 1-1 3-1v1h-1c3 2 5 3 7 5 0 1 0 1 1 1 0 0 0-1 1-1l7 13 1-1c4 8 7 15 4 23l-1 2c3 6 6 12 8 19l-1 1-1-2c-2-7-6-14-12-19-2-2-4-3-6-4-2-2-3-2-5-3l-7-2c-4-2-8-3-12-4l-9-1 1 11h-3c-2 0-4 0-5-1-1 0-3 1-4 0h-1c-2 0-8 0-10 1h-6c-1 0-1 0-2 1h-1-2l-2 1v1h-2-3l-2 1h-1c-1 1-1 1-2 1-1 1-2 1-2 1h-1c0 2 0 2-1 4v1 1h-2l-6 4c-2-1-2-2-3-4l-2 2-4 3c0 1-1 2-2 3l-1 2h0c-1 1-1 1-1 2l-2 2c0-5 2-10 4-14l-1-5c-1-2-2-6-1-8 0-3 1-5 2-7l3-3 1 1c2-2 4-5 7-6l13-5 1-1h-1-1s-1-1-2-1h0c1 0 1-1 2-1v-1c8-3 15-4 22-6h1c2-2 2-2 4-2 0 0 1-1 2-1s1-1 2-1l10-1c2 0 4 1 6 1 1 1 3 1 4 1 2 1 4 1 5 1l13 4h0c-1-1-1-2-2-3 0-1-1-4-1-5-1 0-2 0-2-1l1-1z" class="j"></path><path d="M464 253l4-3 1 1h0v1l-1 1c-1 1-1 1-2 1s-2 2-2 2v-3z" class="B"></path><path d="M495 240h2c1 1 1 1 1 2 0 2 1 5-1 7h-2c-1-3-1-6 0-9z" class="G"></path><path d="M510 234c3-1 9-1 12 0h0 1l3 3v1c-7-1-13-1-20-1h-3v-1c1 0 2-1 3-1 2 0 2 0 4-1h0z" class="E"></path><defs><linearGradient id="V" x1="486.5" y1="235.171" x2="501.5" y2="239.829" xlink:href="#B"><stop offset="0" stop-color="#989695"></stop><stop offset="1" stop-color="#b5b5b4"></stop></linearGradient></defs><path fill="url(#V)" d="M482 239h1c9-3 18-5 27-5h0c-2 1-2 1-4 1-1 0-2 1-3 1v1h3c-6 1-13 2-19 4l-2-1h-4l1-1z"></path><path d="M460 250l1 1c2-2 4-5 7-6l13-5h4l2 1c-7 2-13 5-19 9l-4 3c-3 3-5 5-7 9 0 2 0 5-1 6-1-2-2-6-1-8 0-3 1-5 2-7l3-3z" class="W"></path><path d="M455 260h1v-1c1-2 4-5 6-6h0c-2 3-5 5-5 9h0c0 2 0 5-1 6-1-2-2-6-1-8z" class="l"></path><path d="M457 262c2-4 4-6 7-9v3c-2 3-3 3-3 6 1 1 3 1 4 1 0 0 1-1 2-1l-4 8c1 1 2 1 2 3h0 0l-2 2-4 3c0 1-1 2-2 3l-1 2h0c-1 1-1 1-1 2l-2 2c0-5 2-10 4-14l-1-5c1-1 1-4 1-6z" class="c"></path><path d="M463 270h0c1 1 2 1 2 3h0 0l-2 2-4 3h0c0-2 1-3 2-5 1-1 2-1 2-3z" class="T"></path><path d="M457 262c2-4 4-6 7-9v3c-2 3-3 3-3 6 1 1 3 1 4 1 0 0 1-1 2-1l-4 8h0 0l2-8c-3 3-6 7-8 11l-1-5c1-1 1-4 1-6z" class="M"></path><path d="M522 234c3 0 5 1 8 1 2 1 4 1 7 1 1 0 3 1 4 1l7 2 2 1c0-1 0-1-1-1l1-1c2 1 6 4 8 4 2 1 4 3 6 5 3 3 5 6 5 11h0c-1 2-1 3-1 5h-2v-3l1-1c1-1 1-1 1-3-5-3-9-6-14-9-9-5-18-7-28-9v-1l-3-3h-1z" class="D"></path><path d="M554 247l1-1-3-1-1-1c-2-1-4-1-5-2v-1c1 0 2 1 3 1s3 1 5 2h0c1-1 2-1 3-1 0 0 1 0 2 1s2 2 4 3h1c3 3 5 6 5 11h0c-1 2-1 3-1 5h-2v-3l1-1c1-1 1-1 1-3-5-3-9-6-14-9z" class="g"></path><path d="M503 230c7-1 14-1 21-1h1l9 2 1 2c1 0 2 1 3 1l1 1c1 0 2 1 3 1h0v1h-1c-1 0-3-1-4-1-3 0-5 0-7-1-3 0-5-1-8-1h0c-3-1-9-1-12 0-9 0-18 2-27 5h-1-1-1s-1-1-2-1h0c1 0 1-1 2-1v-1c8-3 15-4 22-6h1z" class="f"></path><path d="M525 229l9 2 1 2c1 0 2 1 3 1l1 1c1 0 2 1 3 1h0v1h-1c-1 0-3-1-4-1-3 0-5 0-7-1h6l-1-1h-2c-3-1-5-3-8-5z" class="J"></path><path d="M483 254c2-1 5-2 6-2l-1 12h-3l-2 1h-1c-1 1-1 1-2 1-1 1-2 1-2 1h-1c0 2 0 2-1 4v1 1h-2l-6 4c-2-1-2-2-3-4h0 0c0-2-1-2-2-3l4-8c4-3 7-6 12-7l4-1z" class="f"></path><path d="M483 254h0c0 2 0 3 1 4l-1 1c-2-2-2-3-4-4l4-1z" class="L"></path><path d="M465 273l3-1 2-2c1 0 1-1 2-1h1c1-1 2-1 4-2 0 2 0 2-1 4v1 1h-2l-6 4c-2-1-2-2-3-4h0z" class="X"></path><path d="M490 263h-1c0-4 0-8 1-11l11-2v6-5l1-1 12-1v1l1-1c4 0 7 0 10 1l1 11h-3c-2 0-4 0-5-1-1 0-3 1-4 0h-1c-2 0-8 0-10 1h-6c-1 0-1 0-2 1h-1-2l-2 1z" class="o"></path><path d="M545 222h1l2 1c1-1 1-1 3-1v1h-1c3 2 5 3 7 5 0 1 0 1 1 1 0 0 0-1 1-1l7 13 1-1c4 8 7 15 4 23l-1 2c3 6 6 12 8 19l-1 1-1-2c-2-7-6-14-12-19l2-1h2c0-2 0-3 1-5h0c0-5-2-8-5-11-2-2-4-4-6-5-2 0-6-3-8-4l-1 1c1 0 1 0 1 1l-2-1-7-2h1v-1h0c-1 0-2-1-3-1l-1-1c-1 0-2-1-3-1l-1-2-9-2h-1c-7 0-14 0-21 1 2-2 2-2 4-2 0 0 1-1 2-1s1-1 2-1l10-1c2 0 4 1 6 1 1 1 3 1 4 1 2 1 4 1 5 1l13 4h0c-1-1-1-2-2-3 0-1-1-4-1-5-1 0-2 0-2-1l1-1z" class="S"></path><path d="M534 231c9 2 17 5 24 11-2 0-6-3-8-4l-1 1c1 0 1 0 1 1l-2-1-7-2h1v-1h0c-1 0-2-1-3-1l-1-1c-1 0-2-1-3-1l-1-2z" class="E"></path><path d="M511 226l10-1c2 0 4 1 6 1 1 1 3 1 4 1 1 1 1 1 2 1s1 0 2 1c2 1 6 1 8 3-2 1-8-2-9-2-10-3-18-2-27-2 0 0 1-1 2-1s1-1 2-1z" class="Y"></path><defs><linearGradient id="W" x1="565.292" y1="238.276" x2="549.368" y2="226.343" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#aeacaa"></stop></linearGradient></defs><path fill="url(#W)" d="M545 222h1l2 1c1-1 1-1 3-1v1h-1c3 2 5 3 7 5 0 1 0 1 1 1 0 0 0-1 1-1l7 13 2 4c-6-5-12-9-19-13h0c-1-1-1-2-2-3 0-1-1-4-1-5-1 0-2 0-2-1l1-1z"></path><path d="M548 673c5 2 11 0 16 2h3c2 1 5 1 7 1 1 1 3 1 4 0h0 1c2 1 8 1 10 0h0l1 1h2 7l2 2c0 2 0 2-1 3-1 0-2 1-3 1 0 3 1 7-1 10l-4 6 3 3-4 1-4-2c-2 2-4 4-5 7-1 1-1 2 0 3h-1l-1 1h-3v-1c0 4 1 7 1 11v4 1c1 0 2 0 3 1h-20-9l-1-1c-1 1-5 1-7 1h0l-10-1c-3 0-6 0-9-1l1-1c1-1 2-2 3-4 0-1-1-2-1-3 0-2 1-5 0-7 0-4-2-5-4-7l-1-1c-3-2-5-3-8-4-3 0-5 0-6 2-1-1-2-2-3-2h3c1 0 1-1 2-1l-2-3h0-4c-2 0-3 0-6-1-1 1-3 2-4 3l-2-2c-1 0-1 0-2 1h-4-3c-1 0-1 1-1 1-1 0-1-1-2-1h-2s-1 0-2-1l1-1v-2c1 0 1 0 1-1 1 0 1 0 1-1v-2-1c1 0 2 1 3 0h1 1 5 1c0-2 0-4 1-6 1 0 2 0 2 1 1 1 1 1 1 2 1-1 1-2 1-3h5c0-1 0-2 1-2h11 11v-1h14c-1-1-2-1-3-1s-1 0-2-1h8l1-1h0v-1c1-1 4 0 6-1z" class="g"></path><path d="M565 685l1-1h4v2l-1 2h-3c0-1 0-2-1-3z" class="C"></path><path d="M506 687l-1-3c0-1 1-1 2-2l1 1 2-1v1 3c-1 1-2 1-4 1h0z" class="N"></path><path d="M552 685v-2h1l1 1c1 0 3-1 4-1v2c-1 2 0 2-2 3h-2l-1-1-1-2z" class="E"></path><path d="M545 686c0-1 0-3 1-4l1 1s0-1 1-1 1 0 2 1v2c0 2 0 2-2 2-1 0-2 0-3-1zm-12-4c1-1 3 0 5 0v1h1 1c1 0 1 0 2-1v1h2v3c-1 1-3 1-4 1h-2c-2 0-2-1-2-2v-2l-1-1-1 1-1-1z" class="C"></path><path d="M502 679h11 11 5c0 1 0 0-1 1-8-1-19-1-27 1 0-1 0-2 1-2z" class="O"></path><path d="M533 682l1 1 1-1 1 1v2h-1l-2 2h-2c-1-1-1-1-3-1-1 1-1 1-2 0l-1-1v-1c1-2 2-1 4-2v3h1l1-1-1-1 1-1c1 1 1 1 2 0z" class="N"></path><path d="M512 686v-4h1c0 1 0 2 1 2h1v-1-1h5l2 1 1 1 1-2v1 3c-2 1-2 1-4 1l-1-1v-2h-1l-1 2c-1 1-1 1-2 1s-2 0-3-1z" class="J"></path><path d="M538 678l28 1 7 1h-4v1c2 0 2 0 3 1l1 1h-4c-1-1-2-1-3-1h-9-5c-2-1-4 0-5-1-4-1-11 1-15 0-2 0-2-1-4-1 1-1 1 0 1-1h-5v-1h14z" class="Y"></path><path d="M515 687c1 0 1 0 2-1l1-2h1v2l1 1c2 0 2 0 4-1l1-1 1 1c1 1 1 1 2 0 2 0 2 0 3 1h2l2-2h1c0 1 0 2 2 2h2v4c-1 1-1 1-2 1-2-1-2-2-3-4h1-2v2c0 2-1 2-1 3v2 1 1h-3-1l-1-1c-2 1-2 1-4 1l-2-2h0-2l-3-3v-3l-1-1-2 1 1-2z" class="O"></path><path d="M528 695v-4c1-1 1 0 1-1l1 1c1 1 1 1 2 0v2h1v2 1l-5-1z" class="H"></path><path d="M528 695l5 1v1h-3-1l-1-1c-2 1-2 1-4 1l-2-2h6z" class="Z"></path><path d="M510 686h2c1 1 2 1 3 1l-1 2 2-1 1 1v3l3 3h-2-3-6 0-4c-2 0-3 0-6-1-1 1-3 2-4 3l-2-2c-1 0-1 0-2 1h-4-3c-1 0-1 1-1 1-1 0-1-1-2-1h-2s-1 0-2-1l1-1v-2c1 0 1 0 1-1 1 0 1 0 1-1v-2-1c1 0 2 1 3 0h1 1 5 1 4 1 1c2 1 1 0 3-1 1 1 1 1 3 2l1-2 2 1h0c2 0 3 0 4-1z" class="F"></path><path d="M507 694c0-2-1-2-1-4h4l1 1-1 2 1 1c1 0 1-1 2-1l2 2h-6 0l-1-1h-1z" class="V"></path><path d="M514 689l2-1 1 1v3l3 3h-2-3l-2-2c-1 0-1 1-2 1l-1-1 1-2c2 0 2 0 3-2z" class="U"></path><path d="M490 687h1 4 0c-1 1-2 1-3 1v3c1 0 1 0 1 1h0l-1 1-1-2-1 1h0l-1 1v-1l-1-2c-1 0-2 0-3-1 0-1-1-1-2-2h1 1 5z" class="a"></path><path d="M499 694v-1l-3-3c2 0 7-1 8 1l2 2c0 1 1 1 1 1h1l1 1h-4c-2 0-3 0-6-1z" class="U"></path><path d="M483 687c1 1 2 1 2 2 1 1 2 1 3 1l1 2-2 1c-1 1-1 2 0 3h-3c-1 0-1 1-1 1-1 0-1-1-2-1h-2s-1 0-2-1l1-1v-2c1 0 1 0 1-1 1 0 1 0 1-1v-2-1c1 0 2 1 3 0z" class="O"></path><path d="M478 692l1 1h1l1-1c0 2 1 3 0 4h-2s-1 0-2-1l1-1v-2z" class="F"></path><path d="M485 689c1 1 2 1 3 1l1 2-2 1c-1 1-1 2 0 3h-3c-1-1-1-2-1-3v-1h-1l-1-1c2-1 3 0 4-2z" class="H"></path><path d="M558 685h0l2 2c1 1 2 1 3 1 1-1 1-2 2-3 1 1 1 2 1 3h3l1-2 1 1 1 1h1 5c0 1-1 2 0 3 1 2 0 4-1 7h0v2c-2 1-5 1-7 2l-2 1c-1-1-2-1-3-2l-1 1h-2l-2 2-3-2c-1-1-3-1-5-1-1 1-3 2-4 3-2 0-2-1-2-2h-1l-1-2c-1 0-1 0-2-1h0c-1 0-1 0-2-1h-7l-3-1h3v-1-1-2c0-1 1-1 1-3v-2h2-1c1 2 1 3 3 4 1 0 1 0 2-1v-4c1 0 3 0 4-1h1c1 1 2 1 3 1 2 0 2 0 2-2h2l1 2 1 1h2c2-1 1-1 2-3z" class="F"></path><path d="M549 696h5l-1-1 1-1h1v2h1v-2h1v1h1v-1h1c0 1 0 1 1 2 1 0 1 0 3-1h1v1c0 1-1 1-2 1s-2 1-2 1h-4-2c-2-1-3-2-5-2z" class="l"></path><path d="M553 687l1 1h2 0c0 1-1 2-1 3s-1 1-2 2v-1c-2 0-5 1-7 0h0-1c1-1 1-2 2-2h1 1v-1 1c1-2 2-2 4-3z" class="a"></path><path d="M553 687l1 1-1 2h-2-2c1-2 2-2 4-3z" class="M"></path><path d="M566 688h3v3h1c-1 2-1 3-1 5h-1c-1-1-2-1-4-1h-1v-2h1l-1-1-2 1c-1-1-2 0-4-1l1-1c2 0 4-1 6 0h1c1 0 1 0 2-1l-1-2z" class="H"></path><path d="M533 695c2 0 4 1 6 1l1-1c1 0 1 1 2 1 2 0 3 0 4-1h0l1-1c1 1 1 2 2 2 2 0 3 1 5 2h-2c-1 0-2 1-3 0h-7-2-7l-3-1h3v-1-1z" class="d"></path><path d="M571 687l1 1h1 5c0 1-1 2 0 3 1 2 0 4-1 7h0v2c-2 1-5 1-7 2l-2 1c-1-1-2-1-3-2l-1 1h-2l-2 2-3-2c-1-1-3-1-5-1-1 1-3 2-4 3-2 0-2-1-2-2h-1l-1-2c-1 0-1 0-2-1h0c-1 0-1 0-2-1h2 7c1 1 2 0 3 0h2 2 4s1-1 2-1 2 0 2-1v-1c2 0 3 0 4 1h1c0-2 0-3 1-5h-1v-3l1-2 1 1z" class="e"></path><path d="M546 702c0-1 1-1 2-2h1v2c1-1 2-1 3-1-1 1-3 2-4 3-2 0-2-1-2-2z" class="C"></path><path d="M564 695c2 0 3 0 4 1 0 0 1 1 0 2h9 0v2c-2 1-5 1-7 2h-2v-2c-2 0-3 0-4-1l-1 2c-1 0-2-2-3-2-1-1-3 0-4-1h4s1-1 2-1 2 0 2-1v-1z" class="h"></path><path d="M564 695c2 0 3 0 4 1 0 0 1 1 0 2h-3-5s1-1 2-1 2 0 2-1v-1z" class="V"></path><path d="M571 687l1 1h1 5c0 1-1 2 0 3 1 2 0 4-1 7h-9c1-1 0-2 0-2h1c0-2 0-3 1-5h-1v-3l1-2 1 1z" class="M"></path><path d="M570 691l2 1v3h-1c-1 1-1 1-2 1 0-2 0-3 1-5z" class="B"></path><path d="M578 691c1 2 0 4-1 7h-9c1-1 0-2 0-2h1c1 0 1 0 2-1h2l-1-2 1-1 1 1c1-1 2-2 4-2z" class="Y"></path><path d="M515 695h3 2 2 0l2 2c2 0 2 0 4-1l1 1h1l3 1h7c1 1 1 1 2 1h0c1 1 1 1 2 1l1 2h1c0 1 0 2 2 2-3 5-3 11-3 17 1 1 0 3 1 5h0v1l-2 1-10-1c-3 0-6 0-9-1l1-1c1-1 2-2 3-4 0-1-1-2-1-3 0-2 1-5 0-7 0-4-2-5-4-7l-1-1c-3-2-5-3-8-4-3 0-5 0-6 2-1-1-2-2-3-2h3c1 0 1-1 2-1l-2-3h6z" class="c"></path><path d="M530 706h1c0 1 1 1 1 1 1 0 1 0 2 1l-2 1 2 1v1c-2 0-2 0-4-2v-3z" class="L"></path><path d="M523 703c2-1 1-1 2-3h1l1 2c2 0 4 1 6 1l-2 2h-1c-1 0-3 0-4-1h-2l-1-1z" class="T"></path><path d="M543 713v2h-6-3c0-1 0-1-1 0h-2l-1-1 1-1c2-1 4-1 6-1l4 1h2 0zm-12 3h0 3s1 0 1 1c2 0 2-1 3 0s3 1 4 0h0l1 1v3h-1c-1 0-2 0-3-1l-1-1-1 1v1l-1 1c0-1-1-2-2-2s-2 1-4 0c0-2 0-2 1-4z" class="p"></path><path d="M545 702h1c0 1 0 2 2 2-3 5-3 11-3 17 0-2 0-3-1-5h0l1-1-2-2h0-2l-4-1v-1h1c2 0 3 0 4 1h1l1-1v-6l-7-1c-1 0-2 0-3-1h-1 9v1c2 0 2-1 3-2z" class="Q"></path><path d="M515 695h3 2 2 0l2 2c2 0 2 0 4-1l1 1h1l3 1h7c1 1 1 1 2 1h0c1 1 1 1 2 1l1 2c-1 1-1 2-3 2v-1h-9 0c-2 0-4-1-6-1l-1-2h-1c-1 2 0 2-2 3-3-2-5-3-8-4-3 0-5 0-6 2-1-1-2-2-3-2h3c1 0 1-1 2-1l-2-3h6z" class="I"></path><path d="M527 702h0c2 0 3 0 5-1h1c2 0 2-1 3-2h1l1 1c1 1 2 1 3 2l1 1h-9 0c-2 0-4-1-6-1z" class="C"></path><path d="M522 695h0l2 2c2 0 2 0 4-1l1 1h1l3 1c-4 1-8 1-13 1 0-1 0-2-1-2l1-1h2v-1z" class="W"></path><path d="M515 695h3 2 2v1h-2l-1 1c1 0 1 1 1 2h0-2c-2-1-3-2-5-1-1 0 0 0-1-1l-1 1-2-3h6z" class="h"></path><path d="M552 701c2 0 4 0 5 1l3 2 3 3 1-1h1 4l1 1c1-2 4-1 6-1 1 0 1 1 2 2l-1 3c0 4 1 7 1 11v4 1c1 0 2 0 3 1h-20-9l-1-1c-1 1-5 1-7 1h0l2-1v-1h0c-1-2 0-4-1-5 0-6 0-12 3-17 1-1 3-2 4-3z" class="Y"></path><path d="M549 709v8l-2 1 1-1v-1l-2 1c0-1 0-3 1-4v1c2-1 0-2 1-3 0-1 1-1 1-2z" class="O"></path><path d="M555 705c2 0 2 1 3 1 1 3 0 7 0 11 0 3 1 7 0 9-1 1-2 1-3 1h-6-1-2v-1h0v-7l1-1 2-1v-8c0-1 0-1 1-2 1 1 1 1 1 2 1 0 2 1 4 1h0-3-1c-2 2-1 4-1 7v8l1 1c1 0 1 0 2-1 1 1 1 1 2 1h0l1-7c-1-1-3-1-4-1v-1h2c1 0 1-1 2-1 0-3 0-7-1-11z" class="b"></path><path d="M556 719l1-1v8h-2l1-7z" class="D"></path><path d="M547 718l2-1v9c-1 1-2 0-3 0h0v-7l1-1z" class="F"></path><path d="M557 702l3 2 3 3h0c1 6 0 13 0 19-1 1-1 1-2 1-1-1-2-1-3-1 1-2 0-6 0-9 0-4 1-8 0-11-1 0-1-1-3-1h-3 0l2-2c1 0 1 0 2-1h1z" class="V"></path><path d="M557 702l3 2 3 3h0c-1 1-2 2-2 3h0v2c0 1-1 2 0 3v1l-1 1h-2 0c0-4 1-8 0-11-1 0-1-1-3-1h-3 0l2-2c1 0 1 0 2-1h1z" class="U"></path><path d="M563 707l1-1h1 4l1 1c1-2 4-1 6-1 1 0 1 1 2 2l-1 3c0 4 1 7 1 11v4 1c1 0 2 0 3 1h-20-9l-1-1c-1 1-5 1-7 1h0l2-1h2 1 6c1 0 2 0 3-1 1 0 2 0 3 1 1 0 1 0 2-1 0-6 1-13 0-19h0z" class="Q"></path><path d="M568 721h1l1 1c0 1 1 3 0 4h-3-2v-2c1-2 1-2 3-3z" class="G"></path><path d="M548 673c5 2 11 0 16 2h3c2 1 5 1 7 1 1 1 3 1 4 0h0 1c2 1 8 1 10 0h0l1 1h2 7l2 2c0 2 0 2-1 3-1 0-2 1-3 1 0 3 1 7-1 10l-4 6 3 3-4 1-4-2c-2 2-4 4-5 7-1 1-1 2 0 3h-1l-1 1h-3v-1l1-3c-1-1-1-2-2-2-2 0-5-1-6 1l-1-1h-4-1l-1 1-3-3 2-2h2l1-1c1 1 2 1 3 2l2-1c2-1 5-1 7-2v-2h0c1-3 2-5 1-7-1-1 0-2 0-3h-5-1l-1-1v-3h2v-1l-1-1c-1-1-1-1-3-1v-1h4l-7-1-28-1c-1-1-2-1-3-1s-1 0-2-1h8l1-1h0v-1c1-1 4 0 6-1z" class="g"></path><path d="M548 673c5 2 11 0 16 2v1h3v1h-14v1c6 0 12-1 17 0v1h-4l-28-1c-1-1-2-1-3-1s-1 0-2-1h8l1-1h0v-1c1-1 4 0 6-1z" class="b"></path><path d="M548 673c5 2 11 0 16 2v1h-13c-3 0-6-1-9-1v-1c1-1 4 0 6-1z" class="i"></path><path d="M599 677l2 2c0 2 0 2-1 3-1 0-2 1-3 1 0 3 1 7-1 10l-1-1c1-2 1-3 1-5l-1-2h0l-1-1h-1c0 1-1 2-1 3l-1 1h-1v-1h-1c-1 0-1 1-3 0v1h-7-1-5-1l-1-1v-3h2v-1l-1-1c-1-1-1-1-3-1v-1h4c4 0 25 1 27 0 0-3-8-1-10-2l9-1z" class="O"></path><path d="M586 688l-2-1c-1 1-1 1-3 0v-3l-1-1h3c0 1 0 1 1 2v-2h3l-1 4v1z" class="R"></path><g class="I"><path d="M573 684l1 1c1-1 0 0 1-2h4c1 2 0 3 0 5h-1-5-1l-1-1v-3h2z"></path><path d="M590 688h1l1-1c0-1 1-2 1-3h1l1 1h0l1 2c0 2 0 3-1 5l1 1-4 6 3 3-4 1-4-2c-2 2-4 4-5 7-1 1-1 2 0 3h-1l-1 1h-3v-1l1-3c-1-1-1-2-2-2-2 0-5-1-6 1l-1-1h-4-1l-1 1-3-3 2-2h2l1-1c1 1 2 1 3 2l2-1c2-1 5-1 7-2v-2h0c1-3 2-5 1-7-1-1 0-2 0-3h1 7v-1c2 1 2 0 3 0h1v1z"></path></g><path d="M595 692l1 1-4 6c-1-1-1-1-2-3h-1-2 2l3-1c1 0 2-2 3-3z" class="P"></path><path d="M590 688h1l1-1c0-1 1-2 1-3h1l1 1c0 2-1 3 0 5-1 2-2 2-5 3 0-1 1-1 1-3 0 0 0-2-1-2z" class="F"></path><path d="M582 699c2-2 3-3 5-3h2 1c1 2 1 2 2 3l3 3-4 1-4-2-3-1-2-1z" class="B"></path><path d="M582 699c2-2 3-3 5-3h2l-1 2-2 1-2 1-2-1z" class="K"></path><path d="M578 708l4-9 2 1 3 1c-2 2-4 4-5 7-1 1-1 2 0 3h-1l-1 1h-3v-1l1-3z" class="F"></path><path d="M586 687c2 1 2 0 3 0h1v1c1 0 1 2 1 2 0 2-1 2-1 3v1c-6 2-8 3-11 9l-2-3v-2h0c1-3 2-5 1-7-1-1 0-2 0-3h1 7v-1z" class="B"></path><path d="M577 698c1 1 1 1 2 1h1l-1-1c1-1 2-3 4-3 0 0 1 0 1-1v-2h-2v-1c2 0 3 0 4 1 1 0 2 1 3 1l1 1c-6 2-8 3-11 9l-2-3v-2z" class="M"></path><path d="M525 250l9 1c4 1 8 2 12 4l7 2c2 1 3 1 5 3 2 1 4 2 6 4 6 5 10 12 12 19l1 2 1-1c2 7 1 14 0 20v1c-1 0-2 0-2 1h-1l-1 2c-1-2-3-4-4-6-2-1-4-2-5-3l-9-5h0c-1 0-1-1-3-1l-1-1-1 1v1s-1 1-2 1h0-2l-1 1-1-1h-2l-4-1-9-2h-1c-6-1-11-1-17-1l-18 2h-2l-9 3-7 3-4 2-1 1h-4l-1 1h-1c-1 0-2 1-3 1l2-3-3 3-2 2c-2 2 0 0-1 2l-1 1-1 1-2-12v-1-3c-1-2-1-4-1-7l2-2c0-1 0-1 1-2h0l1-2c1-1 2-2 2-3l4-3 2-2c1 2 1 3 3 4l6-4h2v-1-1c1-2 1-2 1-4h1s1 0 2-1c1 0 1 0 2-1h1l2-1h3 2v-1l2-1h2 1c1-1 1-1 2-1h6c2-1 8-1 10-1h1c1 1 3 0 4 0 1 1 3 1 5 1h3l-1-11z" class="p"></path><path d="M534 251c4 1 8 2 12 4l7 2c2 1 3 1 5 3 0 2 0 3 1 6l2 4v4c-2-1-4-3-6-3l-1-1-1-1h-2l-3-1h0c-1-1-1-1-2-1h-1c-1-1-1-1-2-1h-2l-1-1h-2l-1-1h-2-1-1l-1-1c-1 0-2 0-4-1h1 2l3 1h2 1c-1-1-1-2-1-3h0c-1-2-1-2-1-3v-2c-1-2-1-3-1-4z" class="I"></path><path d="M546 255l7 2c-1 0-1 1-2 2l-1 1h1 0c1-1 3 0 4 0 1 1 1 1 1 2l-1 1-1-1-1-1-3 2h0c-1-1-1-2-1-3l-1 1-1-3c0-1 0-2-1-3z" class="C"></path><path d="M534 251c4 1 8 2 12 4 1 1 1 2 1 3l1 3h0v2l1 3c-4 0-7-2-10-2-1 0-1-1-2-1h0c-1-1-1-2-1-3h0c-1-2-1-2-1-3v-2c-1-2-1-3-1-4z" class="G"></path><path d="M558 260c2 1 4 2 6 4 6 5 10 12 12 19l1 2 1-1c2 7 1 14 0 20v1c-1 0-2 0-2 1h-1l-1 2c-1-2-3-4-4-6-2-1-4-2-5-3l-9-5h1l1-8c1-2 2-5 2-7s1-3 1-4c-4-2-7-3-11-4-2-1-3-2-5-2l-17-4v13c0 2 0 5-1 6v-19l-12-1c1 4 2 19 0 21v-7-15h4l29 5 3 1h2l1 1 1 1c2 0 4 2 6 3v-4l-2-4c-1-3-1-4-1-6z" class="F"></path><path d="M558 260c2 1 4 2 6 4 6 5 10 12 12 19h-1l-2-4v2c-2-2-3-4-5-5-1 0-1 0-2-1h-1 0 0l-2-1c0-1 0-2-1-3v2l-1 1v-4l-2-4c-1-3-1-4-1-6z" class="W"></path><path d="M560 281l2-5 7 5h-1c-1 5-3 12-3 17v1l-9-5h1 0l2-8 1-5z" class="h"></path><path d="M560 281c2 3 2 6 4 9v1c-1 0-2-1-3-2h1c0-1-1-2-1-3h-2l1-5z" class="D"></path><path d="M565 298c1-1 1-4 1-5v-1l2-4c0-2 0-5 1-6l7 7-2 9c0 2-1 4 0 6 2-3 1-9 2-12 0-2 0-1 1-1 0 2 0 4-1 6l-1 9-1 2c-1-2-3-4-4-6-2-1-4-2-5-3v-1z" class="g"></path><path d="M527 284c1-1 1-4 1-6v-13l17 4c2 0 3 1 5 2 4 1 7 2 11 4 0 1-1 2-1 4s-1 5-2 7l-1 8h-1 0c-1 0-1-1-3-1l-1-1-1 1v1s-1 1-2 1h0-2l-1 1-1-1h-2l-4-1-9-2h-1c-6-1-11-1-17-1l-18 2c4-3 13-3 18-3h-3c-1-1 0-1-1-2 2 0 4 0 6-1h1 4c1-1 2-1 3-1l1 1h4v-3z" class="D"></path><path d="M558 286l-1-1v2h-1v-4h0c0-1 1-2 1-3s-1-1-1-2c2 0 3 0 4 1 0 2-1 5-2 7z" class="b"></path><defs><linearGradient id="X" x1="529.07" y1="298.071" x2="524.029" y2="282.732" xlink:href="#B"><stop offset="0" stop-color="#b8bab9"></stop><stop offset="1" stop-color="#f9f4f4"></stop></linearGradient></defs><path fill="url(#X)" d="M519 287c1 0 3 0 4 1 2 0 4 0 5-1 1 0 2 0 4 1h4l-1 1c1 0 2 0 3 1h2c1 0 2 0 3 1h1l2 1v2l-5-1-7-1c-2-1-5-1-7-1-2-1-4 0-6-1h-9-3c-1-1 0-1-1-2 2 0 4 0 6-1h1 4z"></path><path d="M536 288h1v-4c1-1 1-3 1-5v-2-1c1-1-1-6 1-8 0 1 1 1 1 2h0l1 1c1 0 1 1 3 1l1-3c2 0 3 1 5 2v2l-1 4-1 5v7 2h-1c-1 0 0 0-1 1l-2-1h-1c-1-1-2-1-3-1h-2c-1-1-2-1-3-1l1-1z" class="N"></path><path d="M540 290l2-1c1 1 2 1 3 1 2-1 1-5 2-7h0l1-1-1-2c0-1 0-3 1-5 0 1 0 2 1 3l-1-1c1-2 0-2 1-3l1-1-1 4-1 5v7 2h-1c-1 0 0 0-1 1l-2-1h-1c-1-1-2-1-3-1z" class="I"></path><path d="M527 284c1-1 1-4 1-6v-13l17 4-1 3c-2 0-2-1-3-1l-1-1h0c0-1-1-1-1-2-2 2 0 7-1 8v1 2c0 2 0 4-1 5v4h-1-4c-2-1-3-1-4-1-1 1-3 1-5 1-1-1-3-1-4-1 1-1 2-1 3-1l1 1h4v-3z" class="G"></path><path d="M490 263l2-1h2 1c1-1 1-1 2-1l1 1h3l2 1c-1 0-1 1-1 2h0c0 1 0 2 1 3 0 2-1 11 0 13v4l2 2h5c1 0 3-1 5 0h-1c-2 1-4 1-6 1 1 1 0 1 1 2h3c-5 0-14 0-18 3h-2l-9 3-7 3-4 2-1 1h-4l-1 1h-1c-1 0-2 1-3 1l2-3-3 3-2 2c-2 2 0 0-1 2l-1 1-1 1-2-12v-1-3c-1-2-1-4-1-7l2-2c0-1 0-1 1-2h0l1-2c1-1 2-2 2-3l4-3 2-2c1 2 1 3 3 4l6-4h2v-1-1c1-2 1-2 1-4h1s1 0 2-1c1 0 1 0 2-1h1l2-1h3 2v-1z" class="o"></path><path d="M459 278l4-3 2-2c1 2 1 3 3 4l-2 1c-3 2-6 5-8 7 0 1-1 1-1 2l-1 3-1 1-1 2h2v2 1l-2 2v-1-3c-1-2-1-4-1-7l2-2c0-1 0-1 1-2h0l1-2c1-1 2-2 2-3z" class="E"></path><path d="M490 263l2-1h2 1c1-1 1-1 2-1l1 1h3l2 1c-1 0-1 1-1 2h-3c-2 0-2 0-3 1h-2c-2 0-2 0-3 1h-2l-8 3-5 2v-1c1-2 1-2 1-4h1s1 0 2-1c1 0 1 0 2-1h1l2-1h3 2v-1zm13 22l2 2h5c1 0 3-1 5 0h-1c-2 1-4 1-6 1 1 1 0 1 1 2h3c-5 0-14 0-18 3h-2l-9 3-7 3-4 2-1 1h-4l-1 1h-1c-1 0-2 1-3 1l2-3c1-1 2-1 3-2l1-1 1-1 8-3 1-1c2-1 1-2 1-3h0l1 2c1 0 5-1 6-2 2 0 3 0 4-1h3c2-1 5-2 8-2h2v-2z" class="L"></path><path d="M519 607c2 0 5 0 7 1h1 3 4 0v3c2 0 2 0 4 2l-1 1v1c1 0 2 0 3 1h1c3 1 5 2 7 5l2 4v5l1 3c1 2 1 3 3 5h-1l-1 1 1 1c3 1 3 1 4 3s1 2 2 3h1v1l3 1c-1 0-2 1-3 1l-2 1c0 1 0 1 1 1l-2 2h1 2l2 2v1h0c0 1 1 1 0 2v1h-2v1l2 2-1 1-1 1h0v1c-1 1-2 1-3 1l-1 1h3 1l1 1c-1 1-1 3-1 4l-1 1h3l5 1v1h-3c-5-2-11 0-16-2-2 1-5 0-6 1v1h0l-1 1h-8c1 1 1 1 2 1s2 0 3 1h-14-5l-2-1c-2-1-3 0-5 1h0c-4-1-7-1-10-1l1-2h-1c0-1 0-2-1-3h0c0 1-1 2-1 4l-1-1h-4c-3 0-7-1-9 1-1-1-1-1-2-1-1 1-2 0-2 0h-3 0c1 0 2 0 3-1h1c1 0 1-1 2-2h3c1 0 0 0 1-1v-2-3h0l1-1v-1-1h1c0-1 0-2-1-3v-1c0-1 1-2 0-4 0-1 0-2-1-3 0-2-1-4-1-6v-1c-1-1-2-1-2-2l1-2h-1-3v-1l4-1h1c2 0 3 0 5-1h1c1 0 1 0 1-1v-2h1l2 1v-4-7l1-5v-1l1-5-3 3c-1-1-2-1-2-2 1-1 2-2 3-4 1 0 2-1 3-2h0 2 1l1 1c1-2 1 0 2-1l2-2h10z" class="a"></path><path d="M543 639l3 1v3c-1 0-2-1-3-1v-3z" class="W"></path><path d="M538 640c2-1 3-1 5-1v3l-1 1-2-2c-1-1-1-1-2-1z" class="g"></path><path d="M545 632l1 1c3 1 3 1 5 0 1 2 1 3 3 5h-1-1c-1-1-2-1-3-1v1h-1l-3-6z" class="W"></path><path d="M525 627h1c1 1 1 1 2 1l2 2 3 2h1v2 1h-2c-3-2-5-4-7-8z" class="R"></path><path d="M548 627l2 3 1 3c-2 1-2 1-5 0l-1-1-2-3 1-1v-1c1 1 1 1 2 1h1l1-1z" class="i"></path><path d="M548 627l2 3 1 3c-2 1-2 1-5 0l1-1c0-1 1-1 1-2h0l-1-2 1-1z" class="d"></path><path d="M538 640c1 0 1 0 2 1l2 2c1 1 1 1 1 3 0 0 0 1 1 1-1 1-1 1-2 1l-1 2c1 1 1 3 1 4s-1 1-1 2v4 2l-1 1-1-1c-1-1-1-1 0-2v-2c0-2 0-2 1-4 0-5-2-10-2-14z" class="R"></path><path d="M540 654l1 6v2l-1 1-1-1c-1-1-1-1 0-2v-2c0-2 0-2 1-4z" class="K"></path><path d="M540 641l2 2c1 1 1 1 1 3 0 0 0 1 1 1-1 1-1 1-2 1l-1 2c-1-2 1-4 0-6-1-1-1-1-1-3z" class="I"></path><path d="M536 617h1l9 21c-3 0-6 0-8-1v-2h0v-1c1-1 1 0 1-1-1-1-1-1-2-1l1-1 1-1h-2c0-1-1-4 0-5l2 2c0-4-3-4-4-7l2 1c0-2-1-3-1-4z" class="d"></path><path d="M540 616h1c3 1 5 2 7 5l2 4v5l-2-3-1 1h-1c-1 0-1 0-2-1v1l-1 1-2-5-2-5-1-3h2z" class="W"></path><path d="M539 619h4l-1 2h1c0 1 1 3 0 4 0 0-1-1-2-1l-2-5z" class="d"></path><path d="M540 616h1c3 1 5 2 7 5l2 4v5l-2-3-1-1c0-1 0-3-1-4 0-2-1-2-3-3h-4l-1-3h2z" class="i"></path><path d="M508 631c4-1 9-1 13-1 2 1 3 1 4 1h1l2 2c1 1 3 3 4 3v1l-12-1c-4-1-10 1-14 1l1-5 1-1z" class="I"></path><path d="M508 631c4-1 9-1 13-1 2 1 3 1 4 1h1l2 2c-2 1-2 1-3 0l-1-1c-1 0-2 0-3 1h-2c-1 0-1 1-1 1-1 0-1 0-2-1v2h-1v-3h-8l1-1z" class="W"></path><path d="M515 617h1l4 5 6 9h-1c-1 0-2 0-4-1-4 0-9 0-13 1 2-5 3-10 7-14z" class="E"></path><path d="M519 607c2 0 5 0 7 1h1 3 4 0v3c2 0 2 0 4 2l-1 1v1h-2v1h0c1 1 1 0 1 1l-2-1c0 1 0 2 1 3v2h-1c0 4 1 7 1 11h-1-1l-3-2-2-2c-1 0-1 0-2-1h-1 0l-5-8v3l-4-5h-1c-1-1 0-2-1-3h-2c-1 2-1 3-1 4l-1-1c-1 1-1 1-1 3 0 1 0 1-1 2 0 1 0 2-1 3h0c0-1 0-3 1-5-2 0-4 0-6-1h0c-1 1-2 1-3 1v-1l1-5-3 3c-1-1-2-1-2-2 1-1 2-2 3-4 1 0 2-1 3-2h0 2 1l1 1c1-2 1 0 2-1l2-2h10z" class="S"></path><path d="M518 609h2l-2 2h-2-1-1v-1c2-1 3-1 4-1z" class="B"></path><path d="M526 608h1 3c1 1 1 1 1 2h-3c0 2 0 2 1 4h0l-2-1c-1 0-2-1-3-2h0 3v-1l-1-1h-1 1v-1z" class="P"></path><path d="M519 617l-1-3h3c3 1 6 1 8 2 1 0 2 2 2 2l-12-1z" class="Z"></path><path d="M530 608h4 0v3c2 0 2 0 4 2l-1 1v1h-2v1h0c1 1 1 0 1 1l-2-1c-1-1-1-2-2-4-1 0-1-1-1-2s0-1-1-2z" class="F"></path><path d="M519 617l12 1 2 7-1-1c-1-1-3-3-4-3-2-1-3-1-4-2h-1-1c-2-1-1-1-2 0-1-1-1-2-1-2z" class="d"></path><path d="M500 614l3-3c0 1 1 1 1 1h5c0 1 0 5-1 7h0v1c-2 0-4 0-6-1h0c-1 1-2 1-3 1v-1l1-5z" class="m"></path><path d="M499 619c3-2 5-2 9-2v2 1c-2 0-4 0-6-1h0c-1 1-2 1-3 1v-1z" class="g"></path><path d="M520 619c1-1 0-1 2 0h1 1c1 1 2 1 4 2 1 0 3 2 4 3l1 1v7l-3-2-2-2c-1 0-1 0-2-1h-1 0l-5-8h0z" class="W"></path><path d="M525 627v-1-3h0c2 0 4 0 6 1v3h-2l-2-1-1 1h-1 0z" class="b"></path><path d="M546 643c1 6 1 12 0 19 2 1 2 0 3 0v-4s1 0 1-1h0v-1l-1-1v-1-9-1l-1-4h0 5 0c3 1 3 1 4 3s1 2 2 3h1v1l3 1c-1 0-2 1-3 1l-2 1c0 1 0 1 1 1l-2 2h1 2l2 2v1h0c0 1 1 1 0 2v1h-2v1l2 2-1 1-1 1h0v1c-1 1-2 1-3 1l-1 1h3 1l1 1c-1 1-1 3-1 4l-1 1h3l5 1v1h-3c-5-2-11 0-16-2-2 1-5 0-6 1v1h0l-1 1h-8c1 1 1 1 2 1s2 0 3 1h-14-5l-2-1c-2-1-3 0-5 1h0c-4-1-7-1-10-1l1-2h-1c0-1 0-2-1-3h0c0 1-1 2-1 4l-1-1h-4c-3 0-7-1-9 1-1-1-1-1-2-1-1 1-2 0-2 0h-3 0c1 0 2 0 3-1h1c1 0 1-1 2-2h3c1 0 0 0 1-1v-2-3h0l1-1v-1-1h1v1c0 1 0 1-1 2l2 2h1l1-1h1c1-1 2-1 3-1 2 0 3-1 4-1 2-2 1-5 1-8v1c1 2 1 4 1 6 0 0 1 1 2 1h-1c1 1 2 1 3 1h1c2-1 7-2 9-1h0l1 1 5-1c3-1 5-2 7-2h1 8 0l1-1v-2-4c0-1 1-1 1-2s0-3-1-4l1-2c1 0 1 0 2-1-1 0-1-1-1-1 0-2 0-2-1-3l1-1c1 0 2 1 3 1z" class="D"></path><path d="M560 664c-3 1-5 1-7 1h-3c-1-1-4-1-5-2h5c1-1 2-1 3-2h0v-5-1-1l1-2c0-2 3-3 5-4l1 1-2 1c0 1 0 1 1 1l-2 2h1 2l2 2v1h0c0 1 1 1 0 2v1h-2v1l2 2-1 1-1 1h0z" class="E"></path><path d="M557 653h1 2l2 2v1h0c-2 2-3 3-4 5v1 1h-1c-1 1-2 0-3 0l-1-1c1-3 2-7 4-9z" class="V"></path><path d="M524 665c3-1 5-2 7-2h1 8 2v1c-2 1-3 2-5 2-2 1-2 2-4 2l-5 1-5 1h-1-9c-1-1-3-1-4-2l10-2 5-1z" class="f"></path><path d="M524 665h-1c-1 1-3 1-4 2l-1 1c3 2 11 0 15 0l-5 1-5 1h-1-9c-1-1-3-1-4-2l10-2 5-1z" class="G"></path><path d="M537 670c2 0 4-1 7-1 0 0 0-1 1-1l1 1c0-1 0-1 1-2v4h1c0-1 0-3 1-4h7 3 1l1 1c-1 1-1 3-1 4l-1 1h3l5 1v1h-3c-5-2-11 0-16-2h-3c-3-1-7 0-10 0l-3-2 5-1z" class="O"></path><path d="M560 667l1 1c-1 1-1 3-1 4h-5v-1l-1-1c1-1 2-1 3-1 2 0 2-1 2-2h1z" class="M"></path><path d="M503 657v1c1 2 1 4 1 6 0 0 1 1 2 1h-1c1 1 2 1 3 1h1c2-1 7-2 9-1h0l1 1-10 2c1 1 3 1 4 2h9 1 14l-5 1 3 2c3 0 7-1 10 0h3c-2 1-5 0-6 1v1h0l-1 1h-8c1 1 1 1 2 1s2 0 3 1h-14-5l-2-1c-2-1-3 0-5 1h0c-4-1-7-1-10-1l1-2h-1c0-1 0-2-1-3h0c0 1-1 2-1 4l-1-1h-4c-3 0-7-1-9 1-1-1-1-1-2-1-1 1-2 0-2 0h-3 0c1 0 2 0 3-1h1c1 0 1-1 2-2h3c1 0 0 0 1-1v-2-3h0l1-1v-1-1h1v1c0 1 0 1-1 2l2 2h1l1-1h1c1-1 2-1 3-1 2 0 3-1 4-1 2-2 1-5 1-8z" class="D"></path><path d="M535 673c3 0 7-1 10 0h3c-2 1-5 0-6 1v1h0c-3 0-8 0-11-1h-4l8-1z" class="W"></path><path d="M497 673l2-2c1-1 1 0 3-1 0 1 1 1 2 1 1 1 2 1 4 2 4 1 20 0 24-2l3 2-8 1-16 1h-8-1c0-1 0-2-1-3h0c0 1-1 2-1 4l-1-1h-4c0-1 0-1 2-2z" class="P"></path><path d="M497 673l2-2c1-1 1 0 3-1 0 1 1 1 2 1 1 1 2 1 4 2h-3-2v1c2 2 6 1 8 1h-8-1c0-1 0-2-1-3h0c0 1-1 2-1 4l-1-1h-4c0-1 0-1 2-2z" class="U"></path><path d="M502 670c-1 0-1 0-2-1h3c1-1 1-2 3-2v1h2 1c1 1 3 1 4 2h9 1 14l-5 1c-4 2-20 3-24 2-2-1-3-1-4-2-1 0-2 0-2-1z" class="g"></path><path d="M502 670c-1 0-1 0-2-1h3c1-1 1-2 3-2v1h2 1c1 1 3 1 4 2h9c-4 1-11 1-15 0-1 0-3 1-3 1-1 0-2 0-2-1z" class="D"></path><path d="M503 657v1c1 2 1 4 1 6 0 0 1 1 2 1h-1c1 1 2 1 3 1h1c2-1 7-2 9-1h0l1 1-10 2h-1-2v-1c-2 0-2 1-3 2h-3c1 1 1 1 2 1-2 1-2 0-3 1l-2 2c-2 1-2 1-2 2-3 0-7-1-9 1-1-1-1-1-2-1-1 1-2 0-2 0h-3 0c1 0 2 0 3-1h1c1 0 1-1 2-2h3c1 0 0 0 1-1v-2-3h0l1-1v-1-1h1v1c0 1 0 1-1 2l2 2h1l1-1h1c1-1 2-1 3-1 2 0 3-1 4-1 2-2 1-5 1-8z" class="k"></path><path d="M505 665c1 1 2 1 3 1h1c2-1 7-2 9-1h0l1 1-10 2h-1-2v-1c-2 0-2 1-3 2h-3c1 1 1 1 2 1-2 1-2 0-3 1l-2 2-2-3v-1h3c1-1 2-1 3-2 2-1 3-2 4-2z" class="h"></path><path d="M507 625h0c1-1 1-2 1-3 1-1 1-1 1-2 0-2 0-2 1-3l1 1c0-1 0-2 1-4h2c1 1 0 2 1 3-4 4-5 9-7 14l-1 1-1 5c4 0 10-2 14-1v2c3 0 7 0 10 1h2v1c1 1 3 1 4 2h-1-2v1c1 0 3 1 3 2v1c0 3 1 5 1 8 1 2 1 4 2 6-1 1-1 1 0 2l1 1h0-8-1c-2 0-4 1-7 2l-5 1-1-1h0c-2-1-7 0-9 1h-1c-1 0-2 0-3-1h1c-1 0-2-1-2-1 0-2 0-4-1-6v-1c0 3 1 6-1 8-1 0-2 1-4 1-1 0-2 0-3 1h-1l-1 1h-1l-2-2c1-1 1-1 1-2v-1c0-1 0-2-1-3v-1c0-1 1-2 0-4 0-1 0-2-1-3 0-2-1-4-1-6v-1c-1-1-2-1-2-2l1-2h-1-3v-1l4-1h1c2 0 3 0 5-1h1c1 0 1 0 1-1v-2h1l2 1v-4-7l1-5c1 0 2 0 3-1h0c2 1 4 1 6 1-1 2-1 4-1 5z" class="H"></path><path d="M506 640l1-1c4-2 9-2 13-1-2 2-3 1-5 2h-2c-2 0-2 0-3 1v1h-2v-2h-2z" class="D"></path><path d="M506 640h2v2h2c1 0 2-1 3-1 1 1 1 1 1 2v1h0l-3 1c-2 0-4 1-6 2l1-7z" class="b"></path><path d="M533 643c1 0 3 1 3 2v1c0 3 1 5 1 8v4h-1c0-1-1-3-2-4-5-6-15-9-23-9l3-1h0c2-1 4-1 6 0h1l2 1c3 0 6 1 9 3 1-1 2-1 2-2s-1-1-1-1h-1c1-1 0-1 1-2h0z" class="g"></path><path d="M499 620c1 0 2 0 3-1h0c2 1 4 1 6 1-1 2-1 4-1 5l-1 2-4 10c-1 1-3 1-4 1v-2-4-7l1-5z" class="D"></path><path d="M498 625c1 0 2 1 2 1v5l-1 1v-1l-1 1v-7z" class="b"></path><path d="M499 620c1 0 2 0 3-1h0c2 1 4 1 6 1-1 2-1 4-1 5l-1 2c-1-1-2-2-3-2s-1 1-2 1h-1s-1-1-2-1l1-5z" class="R"></path><path d="M520 638c3 0 7 0 10 1h2v1c1 1 3 1 4 2h-1-2v1h0c-1 1 0 1-1 2h1s1 0 1 1-1 1-2 2c-3-2-6-3-9-3l-2-1h-1c-2-1-4-1-6 0v-1c0-1 0-1-1-2-1 0-2 1-3 1v-1c1-1 1-1 3-1h2c2-1 3 0 5-2z" class="E"></path><path d="M491 640h4c1 1 0 3 0 5 0 5-1 11-1 16v6l-1 1h-1l-2-2c1-1 1-1 1-2v-1c0-1 0-2-1-3v-1c0-1 1-2 0-4 0-1 0-2-1-3 0-2-1-4-1-6v-1c-1-1-2-1-2-2l1-2h1 1l1-1h1z" class="m"></path><path d="M487 641h1 1l1-1h1v11l-2 1c0-2-1-4-1-6v-1c-1-1-2-1-2-2l1-2z" class="V"></path><path d="M495 667l3-27c1-1 3-1 4-1 0 0 1 0 1 1v6c-1 4 0 7 0 11 0 3 1 6-1 8-1 0-2 1-4 1-1 0-2 0-3 1z" class="h"></path><path d="M499 651h2v1c-1 0-1 1-2 1v1l-1 1h0v-3l1-1z" class="D"></path><path d="M506 649l2-2c2-2 7-1 10-1l8 5v1l1 3h1l1-1v-2h1c1 2 1 2 1 5 1-2 2-2 3-3 1 1 2 3 2 4h1v-4c1 2 1 4 2 6-1 1-1 1 0 2l1 1h0-8-1c-2 0-4 1-7 2l-5 1-1-1h0c-2-1-7 0-9 1h-1c-1 0-2 0-3-1h1c0-4-2-12 0-16z" class="Z"></path><path d="M531 657c1-2 2-2 3-3 1 1 2 3 2 4 0 2-1 2-2 3h-2v-1h0c-1-1-1-2-1-3z" class="M"></path><path d="M529 654v-2h1c1 2 1 2 1 5 0 1 0 2 1 3-2 0-3 1-4 2h-1c-1 0-2-1-3 0h-3-1l2-1 1-2c0-3 1-5 3-7l1 3h1l1-1z" class="F"></path><path d="M529 654v-2h1c1 2 1 2 1 5 0 1 0 2 1 3-2 0-3 1-4 2h-1l-1-2c0-2 3-3 4-4 0-1-1-1-1-2z" class="B"></path><path d="M506 649l2-2c2-2 7-1 10-1l8 5v1c-2 2-3 4-3 7-1-2-1-3-1-4l-1-1c-1 1-1 0-2 1 0 0-1 2-1 3s0 1-1 2c0-2 0-3-1-4-1 1-1 1-1 2v4h-1v-4h-1l-1 2h1l-1 1c0-2 0-2-1-3l-2 1v3h-1v-1h-1l-1 2c-1-4 0-9 0-14z" class="G"></path><path d="M518 646l8 5v1c-2 2-3 4-3 7-1-2-1-3-1-4l-1-1h1c1 0 1-1 2-1-1-1-1-1-2-1l-1-2c-1 1-2 1-2 1-2 0-3 0-5-1h0 2c1-1 1-2 2-3v-1z" class="J"></path><path d="M512 291c6 0 11 0 17 1h1l9 2 4 1h2l1 1 1-1h2 0c1 0 2-1 2-1v-1l1-1 1 1c2 0 2 1 3 1h0l9 5c1 1 3 2 5 3 1 2 3 4 4 6l1-2h1c0-1 1-1 2-1v-1l-1 10c3 2 7 4 11 5h1l2-1h0 2c3 3 4 6 3 9v2h0c0 2-1 4-2 5h-1v-2l-1 2-1-1c0-1 0-2 1-3h-1c-1 2-1 4-1 6-1 1-1 2-1 3-1 0-3-1-4-1l-9-3s-1 1 0 1c0 2 1 2 0 3h-2 0l-1 1c-1 1-1 1-2 1h0-1c0 1 0 0-1 1h-1s-1 1-2 1l-1 1v-1h-2c-2 0-4-1-6-1-1-1-1-1-2-3l-1-1h0c-1 1-1 2-2 2 1 1 2 2 2 3l-1 1-1 1v1c0 1-1 2-2 2-1 1-1 1-1 2l1-1 1 1h0v3h-1-1c-2 0-4 3-4 4v1s-1 1-1 2c-1 0-2 0-3-1-1-2-2-6-2-8l-2-1c-1 0-2-1-3-2h1c1 1 1 1 2 1 1-2 1-10 0-11v-1c-3-2-6-2-9-2v1 9h-1-1v-10l-13-1h-1c-1 3 0 9 0 13l-1 1h-2c-5 0-10 1-15 2h-1-1c-2 0-4 0-6 1h1v1h-1c-1 0-1 0-2 1l-4 1-1 1h1l-1 1h0c-3 2-12 5-14 8-1 1-2 1-3 2v-1l-2 2v-1c0-4-1-7 0-11v-4h1v-1c0-1 0-1 1-2v-1-1-4-2c-1-1 0-3 0-4s3-3 4-5h0c1-2 5-4 7-5h-2 0-1l-1-1h-1c-2 1-5 3-6 5-1 1-1 2-2 3v-1h0l-3-17c0-2-1-3-1-5l1-1 1-1c1-2-1 0 1-2l2-2 3-3-2 3c1 0 2-1 3-1h1l1-1h4l1-1 4-2 7-3 9-3h2l18-2z" class="R"></path><path d="M560 321h2c0 1 0 3 1 4h-1l-2-1v-3z" class="J"></path><path d="M552 321l-1-2 1-1c0-1 1-1 1-2l1-1c1 2 0 4 0 6h-2z" class="N"></path><path d="M564 322c2 0 3 0 3 2 0 1 0 2 1 3h0c-2 0-3-1-4-1v-4z" class="Q"></path><path d="M560 314h3c0 2 0 5-1 7h-2v-7zm18 7c1 0 1 0 2 1-1 3 0 7 0 10-1 0-1 0-2-1v-3c0-2-1-4 0-7zm-41 3c1 3 0 7 1 11h-3c1-3-1-5 0-7v-1c1-2 1-2 2-3z" class="N"></path><path d="M583 322h0c2 3 1 8 1 11h-2l-1-1v-1c2-2 0-6 2-9z" class="E"></path><path d="M564 322c0-2 0-4 1-7h1c2 3 2 9 2 12-1-1-1-2-1-3 0-2-1-2-3-2z" class="N"></path><path d="M574 319c1 0 1 0 2 1v3c0 2 1 6 0 7-1 0-2 0-2-1v-1-1c-1-1-1-6 0-8z" class="J"></path><path d="M516 316h8c1 0 2 0 4 1 2 0 5 0 7 1h3v2h-1l-7-1c-4-1-7-1-10-1v-1c-1-1-2-1-4-1z" class="Q"></path><path d="M575 306h1c0-1 1-1 2-1v-1l-1 10c3 2 7 4 11 5-3 1-9-2-12-4-1 0-3-1-4-1h0v1c-1 0-2-1-3-2h0v-1l1 1h3v-1h1c1-2 1-3 0-4l1-2z" class="a"></path><path d="M556 325v-13l2-1 1 1v2h1v7 3c-1 0-2-1-3 0l-1 1h0z" class="e"></path><path d="M557 305l-1-1 1-1h1c3 3 8 5 12 7 1 1 1 1 3 1v1h0v1h-3l-1-1v1h0l-9-4h0l-1 1-1-1h-2c1-2 1-2 1-4z" class="H"></path><path d="M535 335l-8-2c1-4 1-9 0-13 2 0 4 1 7 1 1 0 1 0 3 1v2c-1 1-1 1-2 3v1c-1 2 1 4 0 7z" class="X"></path><path d="M469 325c3-2 6-3 8-4 12-5 27-6 39-5 2 0 3 0 4 1v1c-12-1-25 0-36 4-4 1-7 3-11 4h-2 0-1l-1-1z" class="L"></path><path d="M553 332l1 2c-1 0-1 0-2 1 0 1 0 1 1 1l1 2h0c-1 1-1 2-2 2 1 1 2 2 2 3l-1 1-1 1v1c0 1-1 2-2 2-1 1-1 1-1 2l1-1 1 1h0v3h-1-1c-2 0-4 3-4 4v1s-1 1-1 2c-1 0-2 0-3-1-1-2-2-6-2-8v-3c0-2 0-3 1-5 2-2 7-7 10-8l3-3z" class="H"></path><g class="W"><path d="M547 343c-1 2-3 5-5 6h-2l1-1c0-1-1-2 0-3 0-2 2-4 4-5 1 1 1 2 2 3z"></path><path d="M553 332l1 2c-1 0-1 0-2 1 0 1 0 1 1 1l1 2h0c-1 1-1 2-2 2 1 1 2 2 2 3l-1 1-1 1c-3 2-7 7-9 9l-2 2v-1c1-2 1-3 2-4h0l5-7-1-1h0c-1-1-1-2-2-3 2-2 4-3 5-5l3-3z"></path></g><path d="M553 332l1 2c-1 0-1 0-2 1 0 1 0 1 1 1-2 2-4 5-6 7h0c-1-1-1-2-2-3 2-2 4-3 5-5l3-3z" class="b"></path><path d="M507 319c4-1 9-1 13 0 2 0 4 0 6 1 1 4 0 9 0 13-5 1-11-1-17-1 0-2-1-7-1-10l1-3h-2z" class="G"></path><path d="M546 296l1-1h2 0c1 0 2-1 2-1v-1l1-1 1 1c2 0 2 1 3 1h0l9 5c1 1 3 2 5 3 1 2 3 4 4 6 1 1 1 2 0 4h-1 0v-1c-2 0-2 0-3-1-4-2-9-4-12-7h-1l-1 1 1 1c-2 0-3 0-4 2v1c0 2-3 4-5 6l-1 1-1-1 3-3c1-1 3-3 4-5h-1c-2 2-5 6-8 7 1-2 3-4 5-5-2-3 0-8-2-10-1-1 0-1-1-2z" class="i"></path><path d="M546 296l6 2h1l3 2c0 1 0 1-1 2-2 0-5 4-6 6-2-3 0-8-2-10-1-1 0-1-1-2z" class="I"></path><path d="M546 296l1-1h2 0c1 0 2-1 2-1v-1l1-1 1 1c2 0 2 1 3 1 2 3 6 3 8 5l-1 1h-1l1 1c1 0 1 1 2 1v1l-3-2c-1 0-2-1-4-2l-2-1c-1 0-2-1-4-1v1l-6-2z" class="R"></path><path d="M556 294h0l9 5c1 1 3 2 5 3 1 2 3 4 4 6 1 1 1 2 0 4h-1 0v-1c-2 0-2 0-3-1l1-2-4-2-2-2v-1-1c-1 0-1-1-2-1l-1-1h1l1-1c-2-2-6-2-8-5z" class="d"></path><path d="M556 294h0l9 5c1 1 3 2 5 3 1 2 3 4 4 6 1 1 1 2 0 4h-1 0v-1-2s0-1-1-2c-1-4-5-6-8-8-2-2-6-2-8-5z" class="P"></path><path d="M589 319l2-1h0 2c3 3 4 6 3 9v2h0c0 2-1 4-2 5h-1v-2l-1 2-1-1c0-1 0-2 1-3h-1c-1 2-1 4-1 6-1 1-1 2-1 3-1 0-3-1-4-1l-9-3s-1 1 0 1c0 2 1 2 0 3h-2 0l-1 1c-1 1-1 1-2 1h0-1c0 1 0 0-1 1h-1s-1 1-2 1l-1 1v-1h-2c-2 0-4-1-6-1-1-1-1-1-2-3l-1-1-1-2c-1 0-1 0-1-1 1-1 1-1 2-1l-1-2 1-1h0v-2l1-1-1-1-4 4s-1 2-2 2l-1 1c-1 1-2 1-3 2h-1c1-1 1-1 2-1l1-1v-1h-1c0-3 2-3 3-5 1 0 0-4 0-5l1-1c0 3 0 4 2 6h1l-1-1c2-2 2-3 2-6h-1 2v4h1 1 0c1 0 2 0 3 1 2 1 5 1 7 2l8 3 2 1 2 1c2 0 3 1 5 2h2v1l1-1c-2-4 1-10-2-13 1 0 1 1 2 1h4 0 1l-1 2c1 0 1 1 1 2h-1v1h2l1-1c-1-1 0-3-1-5h0l1-1-1-1c-1 0-1 0-2-1h-1z" class="C"></path><path d="M557 333v-5c1 0 2 1 3 1l9 3-1 2-2-1-1 1-1 1c-2-1-4-1-5-2l-1 1-1-1z" class="K"></path><path d="M569 332l7 3s-1 1 0 1c0 2 1 2 0 3h-2 0l-1 1c-1 1-1 1-2 1h0-1c0 1 0 0-1 1h-1s-1 1-2 1l-1 1v-1h-2c-2 0-4-1-6-1-1-1-1-1-2-3l2-4-1-1 1-1 1 1 1-1c1 1 3 1 5 2l1-1 1-1 2 1 1-2z" class="e"></path><path d="M569 332l7 3s-1 1 0 1c0 2 1 2 0 3h-2l-10-4 1-1 1-1 2 1 1-2z" class="F"></path><path d="M557 335l1 1v5l1-1v-2c1-1 2-1 3-1l1 2 1-1c1 0 1 1 3 1v2c0 1 0 1-1 2l-1 1v-1h-2c-2 0-4-1-6-1-1-1-1-1-2-3l2-4z" class="R"></path><path d="M507 319h2l-1 3c0 3 1 8 1 10-10 1-20 2-30 5l-1-1v1c-1 1-4 2-6 3-3 1-7 4-10 6v-4-2c-1-1 0-3 0-4s3-3 4-5h0c4-3 8-4 13-6 8-4 19-5 28-6z" class="o"></path><path d="M512 291c6 0 11 0 17 1h1l9 2 4 1h2l1 1c1 1 0 1 1 2 2 2 0 7 2 10-2 1-4 3-5 5-3 1-4 4-7 4-8-2-16-3-25-3v-2c-1-4 0-9 0-12 1-3 0-6 0-9z" class="G"></path><path d="M539 304c-1-1-2-2-2-3 0-3 1-3 2-5v3 5z" class="J"></path><path d="M539 294l4 1h2l1 1c1 1 0 1 1 2 2 2 0 7 2 10-2 1-4 3-5 5-3 1-4 4-7 4 1-1 2-2 2-3l-1-1c0-1 1-1 1-1v-3-1h-2c-2-1-2-1-3-2l1-1c2 2 2 2 4 2v-3-5-3-2z" class="C"></path><path d="M504 334h9-1c-1 3 0 9 0 13l-1 1h-2c-5 0-10 1-15 2h-1-1c-2 0-4 0-6 1h1v1h-1c-1 0-1 0-2 1l-4 1-1 1h1l-1 1h0c-3 2-12 5-14 8-1 1-2 1-3 2v-1l-2 2v-1c0-4-1-7 0-11v-4h1v-1l4-4 12-7 1 12h1v-12l10-3h2 1c1-1 0-1 1-1h1c4 0 7-1 10-1z" class="o"></path><path d="M504 334h9-1c-1 3 0 9 0 13l-1 1h-2l1-2c-1-4 1-7-1-11-1-1-3-1-5-1z" class="T"></path><path d="M465 346c0 1 0 2 1 3v3l-1 1v6h0c-2 1-3 2-5 3v-2-5-4h1v-1l4-4z" class="f"></path><path d="M461 351c1 3 1 5-1 9v-5-4h1z" class="G"></path><path d="M486 351h1v1h-1c-1 0-1 0-2 1l-4 1-1 1h1l-1 1h0c-3 2-12 5-14 8-1 1-2 1-3 2v-1l-2 2v-1c0-4-1-7 0-11v5 2c2-1 3-2 5-3 3-1 6-3 9-4l9-3 3-1z" class="e"></path><path d="M494 293l18-2c0 3 1 6 0 9 0 3-1 8 0 12v2c-14 1-33 3-44 11-2 1-5 3-6 5-1 1-1 2-2 3v-1h0l-3-17c0-2-1-3-1-5l1-1 1-1c1-2-1 0 1-2l2-2 3-3-2 3c1 0 2-1 3-1h1l1-1h4l1-1 4-2 7-3 9-3h2z" class="o"></path><path d="M456 310l1-1 1-1c1-2-1 0 1-2l2-2 3-3-2 3c1 0 2-1 3-1h1l1-1h4l-4 2-2 2c-2 2-4 5-6 7v1l-2 1c0-2-1-3-1-5z" class="c"></path><path d="M469 458l10-10c2-2 3-3 5-4 5 2 7 7 8 11l3 6c1 2 2 5 3 7l8 17c0 1 0 1 1 2v4h-1c-3 1-6 1-9 1l1 1c-1 1-4 0-5 0v56c0 3-1 7 0 10 1 1 2 2 4 2v1h4c0 1 1 2 0 3l-1 2-3 3c-2 2-3 4-5 5-1 4 0 9 0 14l-1-1-28-8-5-1c-2-1-5-1-7-1h0c2 2 3 3 5 4h0-3-1v2c-2 0-3 0-4-1-3 0-6-1-9-1-1-1-2-1-3-3h2c1-1 1-3 1-5l-1-14h0c1-2 1-8 1-11v-31h0c0-5 0-11 1-15l2-11c4-10 10-18 19-25l-1-1c1 0 2-1 3-2l6-6z" class="L"></path><path d="M455 518c-1 0-2 0-3-1v-3c1-2 2-3 2-5v-1l1 1-1 1 1 1c0 1 0 1-1 2 0 0 0 1 1 2v1 1 1z" class="G"></path><path d="M439 518h0c0-5 0-11 1-15v73l1 1c3 0 7 0 10 1h0c2 2 3 3 5 4h0-3-1v2c-2 0-3 0-4-1-3 0-6-1-9-1-1-1-2-1-3-3h2c1-1 1-3 1-5l-1-14h0c1-2 1-8 1-11v-31z" class="Y"></path><path d="M444 580l9 2h0-1v2c-2 0-3 0-4-1h0c-1-1-1-1-2-1l-2-2z" class="F"></path><path d="M436 579h2c2 1 5 0 6 1l2 2c1 0 1 0 2 1h0c-3 0-6-1-9-1-1-1-2-1-3-3z" class="H"></path><path d="M454 508c0-2 0-4 1-5s1-1 2-1v-1l2 1h0 2c0 2 0 2 1 3 0 1-1 2 1 3h2v12 31l-2 1v-2c-2-2-3-2-5-2-2-1-4-1-6-1l-2 3c-1-1-1-2-3-2-1 0-1-1-2-2h7v-2l-1-1c0-1 1-1 1-2 0-3-1-9 1-11v-3-1c1-2 1-3 1-5s0-2 1-3v-1-1-1c-1-1-1-2-1-2 1-1 1-1 1-2l-1-1 1-1-1-1z" class="C"></path><path d="M455 517l2 1c0-1 1-1 1-2 1-1 0-2 2-2v-1l1-1c1 0 1 1 2 2v2c-1 1-2 1-3 2 0 1 0 1 1 2-2 1-4-1-6 0l-1 1c0-2 0-2 1-3v-1z" class="J"></path><path d="M467 462c1 1 1 2 1 3 0 3-1 6-1 9-3 2-4 4-5 7 1-2 2-3 4-4h1v7l-2 1v2h0v11 10h-2c-2-1-1-2-1-3-1-1-1-1-1-3h-2c-1-2 0-3 0-5-2 0-2 0-3 1v2l-1 2-1-1c0-2 0-3 1-6l-1-1h-1c-2 0-8 1-10 0 3-2 8 0 11-2h-1c-3-1-7-1-11 0 4-10 10-18 19-25l6-5z" class="G"></path><path d="M466 477h1v7l-2 1v2h0v11 10h-2c-2-1-1-2-1-3-1-1-1-1-1-3h-2c-1-2 0-3 0-5-2 0-2 0-3 1v2l-1 2-1-1c0-2 0-3 1-6 2-1 2-1 5-1v1l3-3v-2l-1-1v-1h-1v3h-3l-1-1c0-1 1-2 2-3 0-1 0-2 1-3 0-1 1-2 2-3 1-2 2-3 4-4z" class="Q"></path><path d="M466 477h1v7l-2 1v2h0l-1-3-1 1c-1-1-2-1-3-1 0-1 1-2 2-3 1-2 2-3 4-4z" class="N"></path><path d="M466 477h1v7l-2 1v-4c1-1 1-2 1-4z" class="R"></path><path d="M441 577l1-1c1-1 1-3 1-4v-14c0-2 0-4 1-5 0 0 1-1 2-1 0-1-1-1-2-2v-3l1-1c1 1 1 2 2 2 2 0 2 1 3 2l2-3c2 0 4 0 6 1 2 0 3 0 5 2v2c0 1-1 1-2 1s0 0-1 1c1 0 1 0 2 1 1 0 1 0 2-1v1l-2 1v1l2 1c0-1 0-1 1-2v1c2 1 6 2 9 2h1s1 0 1 1h3c1-1 1-1 2-3 0 1 0 2 2 3h0c1 0 2 1 2 2h0c-1 0-1 0-1 1h3l2 2 1-1v1c-1 1 0 1-1 1l-1 1v-1c-1 0-3 1-3 2 0 0 1 2 1 3l-1 1h1v-1h3c1 1 2 3 3 4-1 4 0 9 0 14l-1-1-28-8-5-1c-2-1-5-1-7-1-3-1-7-1-10-1z" class="e"></path><path d="M481 571c0-1-1-2-1-3l1-1h0 1l1-1 2 2s1 2 1 3l-1 1h1v-1h3c1 1 2 3 3 4-1 4 0 9 0 14l-1-1v-3h-1c-2-3-2-5-2-8-1 0-1 0-2-1v-1c-2-2-4-3-5-4z" class="l"></path><path d="M486 575c2 0 3 0 4 1l1 9h-1c-2-3-2-5-2-8-1 0-1 0-2-1v-1z" class="b"></path><path d="M450 550l2-3c2 0 4 0 6 1 2 0 3 0 5 2v2c0 1-1 1-2 1s0 0-1 1c1 0 1 0 2 1 1 0 1 0 2-1v1l-2 1v1l2 1c0-1 0-1 1-2v1c2 1 6 2 9 2h1s1 0 1 1h3c1-1 1-1 2-3 0 1 0 2 2 3h0c1 0 2 1 2 2h0c-1 0-1 0-1 1h3l2 2 1-1v1c-1 1 0 1-1 1l-1 1v-1c-1 0-3 1-3 2l-2-2-1 1h-1 0l-1 1c0 1 1 2 1 3-2-2-3-4-3-7l-9-3v1l-1 1v-2c-1-1-3-1-4-1l-10-3v-3l-1 1-2-1v1c-1-1-1-1-2-1v1c0 1 1 2 2 2l2-1v1l-1 1h-2c-1-1-2-2-3-4 0-2 2-3 3-4z" class="H"></path><path d="M458 548c2 0 3 0 5 2v2c0 1-1 1-2 1s0 0-1 1h-1l-1-1c0-1 0-2-1-3h0v-2h1z" class="e"></path><path d="M481 557c0 1 0 2 2 3h0c1 0 2 1 2 2h0c-1 0-1 0-1 1h3l2 2 1-1v1c-1 1 0 1-1 1l-1 1v-1c-1 0-3 1-3 2l-2-2-1 1h-1 0l-1 1c0 1 1 2 1 3-2-2-3-4-3-7h2 0c2 1 6 1 7 0-2 0-3 0-4-1-2 0-2 0-3-1-2 0-2-1-3-1h-3l1-1h1 3c1-1 1-1 2-3z" class="Y"></path><path d="M441 577l1-1c1-1 1-3 1-4v-14c0-2 0-4 1-5 0 0 1-1 2-1 0-1-1-1-2-2v-3l1-1c1 1 1 2 2 2 2 0 2 1 3 2-1 1-3 2-3 4 1 2 2 3 3 4h2l1-1v-1l-2 1c-1 0-2-1-2-2v-1c1 0 1 0 2 1v-1l2 1 1-1v3c0 3 1 4 2 6 3 5 6 10 6 15l1-1v-1l1 1 3 3c-1 0-1-1-2-1h-2v1l-5-1c-2-1-5-1-7-1-3-1-7-1-10-1z" class="J"></path><path d="M467 474c2 1 2 2 3 3 2 4 2 8 2 13v1c1 2 3 5 4 7 2 4 2 13 1 17 0 1 0 2-1 3 1 0 2 1 3 2 3 1 7 2 10 3l2 1c1 3 0 8 0 12v22h0l-2-1h-1c-1 2-2 2-3 2h-1v1c1 0 1 0 2-1 2 1 3 3 4 5l-1 1-2-2h-3c0-1 0-1 1-1h0c0-1-1-2-2-2h0c-2-1-2-2-2-3-1 2-1 2-2 3h-3c0-1-1-1-1-1h-1c-3 0-7-1-9-2v-1c-1 1-1 1-1 2l-2-1v-1l2-1v-1c-1 1-1 1-2 1-1-1-1-1-2-1 1-1 0-1 1-1s2 0 2-1l2-1v-31-12-10-11h0v-2l2-1v-7h-1c-2 1-3 2-4 4 1-3 2-5 5-7z" class="g"></path><path d="M476 518c1 0 2 1 3 2 3 1 7 2 10 3l-1 1c-2 0-5 0-8-1h0l-1-1c-2-1-2-2-3-4z" class="b"></path><path d="M472 508v23 11 6c0 1-1 2-1 3v3h-1-1l1-24v7c1-2 0-4 1-6s0-5 0-7c0-5 0-10 1-15v-1z" class="H"></path><path d="M470 554h1v-3c6 0 11 4 17 6-1 2-2 2-3 2h-1v1c1 0 1 0 2-1 2 1 3 3 4 5l-1 1-2-2h-3c0-1 0-1 1-1h0c0-1-1-2-2-2h0c-2-1-2-2-2-3h-1c-1 0-1 1-2 0-3-1-6-2-8-3z" class="a"></path><path d="M467 474c2 1 2 2 3 3 2 4 2 8 2 13v1 12 5 1c-1 5-1 10-1 15 0 2 1 5 0 7s0 4-1 6v-7c-1-3 0-8 0-11 0-13 0-27-1-40v-1l-2-1h-1c-2 1-3 2-4 4 1-3 2-5 5-7z" class="M"></path><path d="M467 477l2 1v1c1 13 1 27 1 40 0 3-1 8 0 11l-1 24h1c2 1 5 2 8 3 1 1 1 0 2 0h1c-1 2-1 2-2 3h-3c0-1-1-1-1-1h-1c-3 0-7-1-9-2v-1c-1 1-1 1-1 2l-2-1v-1l2-1v-1c-1 1-1 1-2 1-1-1-1-1-2-1 1-1 0-1 1-1s2 0 2-1l2-1v-31-12-10-11h0v-2l2-1v-7z" class="d"></path><path d="M467 484v21c0 3 0 6-1 10 0 1 0 2-1 3v2-12-10-11h0v-2l2-1z" class="W"></path><path d="M467 484v21c-2-3-1-9-1-12 0-1 0-3-1-4 0 2 1 5 0 8v1-11h0v-2l2-1z" class="h"></path><path d="M469 554h0c-1 0-1 0-2-1 1-3 1-6 1-8v-23-7-35l1-1c1 13 1 27 1 40 0 3-1 8 0 11l-1 24z" class="R"></path><path d="M469 458l10-10c2-2 3-3 5-4 5 2 7 7 8 11l3 6c1 2 2 5 3 7l8 17c0 1 0 1 1 2v4h-1c-3 1-6 1-9 1l1 1c-1 1-4 0-5 0v56c0 3-1 7 0 10 1 1 2 2 4 2v1h4c0 1 1 2 0 3l-1 2-3 3c-2 2-3 4-5 5-1-1-2-3-3-4h-3v1h-1l1-1c0-1-1-3-1-3 0-1 2-2 3-2v1l1-1c1 0 0 0 1-1v-1c-1-2-2-4-4-5-1 1-1 1-2 1v-1h1c1 0 2 0 3-2h1l2 1h0v-22c0-4 1-9 0-12l-2-1c-3-1-7-2-10-3-1-1-2-2-3-2 1-1 1-2 1-3 1-4 1-13-1-17-1-2-3-5-4-7v-1c0-5 0-9-2-13-1-1-1-2-3-3 0-3 1-6 1-9 0-1 0-2-1-3l-6 5-1-1c1 0 2-1 3-2l6-6z" class="O"></path><path d="M492 473c0-2 1-4 0-6h0v-8h1c0 1 1 2 2 2 1 2 2 5 3 7 0 1 0 1-1 1v2l-2 2c-1 1-1 1-2 0h-1z" class="D"></path><path d="M488 557h1c-1 2-1 2-1 4 2 0 2 0 3 2v2l1 1c1-2 0-3 0-4h1 1 3 0 4c0 1 1 2 0 3l-1 2-3 3c-2 2-3 4-5 5-1-1-2-3-3-4h-3v1h-1l1-1c0-1-1-3-1-3 0-1 2-2 3-2v1l1-1c1 0 0 0 1-1v-1c-1-2-2-4-4-5-1 1-1 1-2 1v-1h1c1 0 2 0 3-2z" class="V"></path><path d="M485 568c0-1 2-2 3-2v1c1 0 2 1 3 2 0 1 0 1 1 2v-3h1c2 1 3 0 4 0l2-2 1 1-3 3c-2 2-3 4-5 5-1-1-2-3-3-4h-3v1h-1l1-1c0-1-1-3-1-3z" class="H"></path><path d="M498 468l8 17c0 1 0 1 1 2v4h-1c-3 1-6 1-9 1l1 1c-1 1-4 0-5 0-1-6-1-13-1-20h1c1 1 1 1 2 0l2-2v-2c1 0 1 0 1-1z" class="e"></path><path d="M472 490s2 0 2 1l1 1c1 1 8 0 9 0h1l6 1v30c-4-1-8-3-12-4v1c-1-1-2-2-3-2 1-1 1-2 1-3 1-4 1-13-1-17-1-2-3-5-4-7v-1z" class="R"></path><path d="M467 462l9-9c2-2 4-5 6-6s2 0 3 0c3 1 4 3 5 6 1 4 1 9 1 14v1 24c-3 0-13-4-15-3l9 3h-1c-1 0-8 1-9 0l-1-1c0-1-2-1-2-1 0-5 0-9-2-13-1-1-1-2-3-3 0-3 1-6 1-9 0-1 0-2-1-3z" class="D"></path><path d="M467 462l9-9c2-2 4-5 6-6s2 0 3 0c0 1-1 2-1 4s0 6-1 8c-1 1-2 1-2 2v1h-2c0 1 0 1 1 1l6 2c1 0 2 1 2 1v1l-4-1c-1 0-1-1-2-1h-5c-2 3-3 7-4 10v-1c-1 0-2 0-3 1v1 1c-1-1-1-2-3-3 0-3 1-6 1-9 0-1 0-2-1-3z" class="C"></path><path d="M477 461c-1-1-1-1 0-2l1-1 1 1v3c-1 1 0 0 0 1v1h-2v-3z" class="E"></path><path d="M528 718c0 1 1 2 1 3-1 2-2 3-3 4l-1 1c3 1 6 1 9 1l10 1h0c2 0 6 0 7-1l1 1h9 20 7 0c4 1 8 1 12 1v8 4h2 1c0 1 1 1 2 1l1 2 1-1 1 1c1 0 1 1 2 2l-1 1c-1 0-1 0-1 2l2-1v2c1 1 2 1 4 1l-1 1h1v1c0 1-1 1-1 2 1 1 2 1 2 3 0 1 0 2-1 3 1 1 1 2 2 2v1c1 1 2 1 3 3h-6c1 2 1 2 0 4l-1 1c-2-1-1-2-4-2l1 1c0 2 0 4-1 6 1 1 1 1 0 2v4c2 1 3 1 5 1l1 1c-1 0-1 1-2 1h0c-1 1-2 1-3 1 2 2 2 3 3 4 0 1-1 2-1 3l-2-1-1 1-1 1h-1c0 1 0 1 1 2l-1-1-2 1v1h-1v-1h1c-1-1-2-1-3-1h-1l-2 2c-1 1-1 1-1 2-1-1-1-2-2-3l2-2v-4h0-6-7-4-6-4-4c-2 1-5 1-7 1-3-1-9-1-12-1h-39-35-3-24-1c-2-1-5-1-7-1h-4c-2-1-3-1-4-3h1 1v-2-4l2 1v-1l6-1h-6c1-1 2-1 3-1l1 1v-1h5v-1c1-3 1-6 3-8v-2h0 2v-4c0-5-1-11 1-15 0-3 1-4 3-6h-1-1l1-2c-2-1-4-1-5-2v-4l2 2h6 1 3 2 9c6 1 12 0 18 0h8v-1l1-1v-3h8l1 1v1 2h1l-1-1 1-1v-2-1h-10l-1-1h0c1-2 6-1 8-2 3-1 9 0 12 0l3-1 4-1c3-2 5-4 6-8z" class="C"></path><path d="M581 738h14 2l1 2c-1-1-2-1-3-1-1 1-1 1-2 1-1-1-3 0-4 0h-17c-1-1-4-1-6-1 4-1 8 1 12 0v-1h3z" class="E"></path><path d="M561 741h15v2l-1 1h-1-4l-1 1h3c-2 1-4 1-6 1-1 0-2-1-3 0h-1l-1-2v-3z" class="O"></path><path d="M576 741h15c-1 1-1 2-2 3-1 0-2 1-3 1l-1-2-1 1v2h0l-2-1c-1 0-5 1-5 1-1 0-1-1-2-1h-3-3l1-1h4 1l1-1v-2z" class="K"></path><path d="M493 740h1 6l1-2h22 20c-1 2-4 0-6 2h0 4c-2 1-7 0-8 1h-22c-5 0-10 1-14-1l-3 1-1-1z" class="I"></path><path d="M593 741h4l1 2-1 1c-1 2-3 3-4 4-3 2-4 2-7 2-3-1-8 0-11 0h-1c0-1 0-2-1-3l-1 1-1-1c-1-1-4-1-5-1 2 0 4 0 6-1h3c1 0 1 1 2 1 0 0 4-1 5-1l2 1h0v-2l1-1 1 2c1 0 2-1 3-1 1-1 1-2 2-3h2z" class="k"></path><path d="M591 741h2l-2 2v2c-1 1-2 2-4 3h0-1v-1l1-1-1-1c1 0 2-1 3-1 1-1 1-2 2-3z" class="Y"></path><path d="M593 741h4l1 2-1 1h-2-1c-1 1-2 1-3 1v-2l2-2z" class="O"></path><path d="M554 730l14 1c-1 0-3 1-4 1l-1 2v4l-3-1c-2 0-3 0-4-1l1-1-1-1-1 1v2h-4c1-1 1-2 1-4h-1v3 1c-2 1-8 0-10-1-1 0 0-1 0-1v-2h-1v3l-1 1-2-1v-2l1-1c0 1 0 1 1 2h0c0-2 0-2-1-3h-2-3l-1-1c3-1 6-1 9-1h13z" class="V"></path><path d="M560 737v-2h-1l1-2h2l1-1v2 4l-3-1zm-114-2l2 2 1 1c5 1 11 1 16 0 4 0 7 1 11 0h1v1c-2 0-3 0-4 1h9l-4 1-1 1h-1c0 1 1 3-1 4-2 0-4 1-6 0v-1c-1 0-2-1-3-2-1 0-1 0-2 1h-1l-1 1c-1-2-1-2-3-2-2 1-2 1-3 1v1l-1 1h0v-1c-1-2-2-2-3-2h-1-1l1-2c-2-1-4-1-5-2v-4z" class="W"></path><path d="M469 741c2 0 5-1 6 0l-1 2c1 1 0 1 1 2v1c-2 0-4 1-6 0v-1-4z" class="M"></path><path d="M451 741c6 1 12 0 18 0v4c-1 0-2-1-3-2-1 0-1 0-2 1h-1l-1 1c-1-2-1-2-3-2-2 1-2 1-3 1v1l-1 1h0v-1c-1-2-2-2-3-2h-1-1l1-2z" class="F"></path><path d="M541 740h15c2 0 4 0 5 1v3l1 2h1c1-1 2 0 3 0s4 0 5 1l1 1 1-1c1 1 1 2 1 3-1 0-5 1-6 0h-5c1-1 1-1 1-2h-1c-2 0-4-1-6 0h0l-2-1c-1 1-2 1-3 1h-1-4c-1-1-2-1-4-1-1 0-2 0-3-1h-4v1h0-1c-1 0-2 0-4-1 1-1 1-2 1-4h2l-1-1c1-1 6 0 8-1z" class="Z"></path><path d="M552 746h0c0-2-1-3 0-4h3v1c0 1 0 2 1 3h-4zm-16 0h-1v-3l1-1h3c1 2 1 3 1 4h-4z" class="S"></path><path d="M543 747v-3l1-1c0 2 0 2 1 4v-1l2 1 1-1 1 1c1 0 2 0 3-1h4 0l1 1h5c3 1 7 1 10 1l1-1c1 1 1 2 1 3-1 0-5 1-6 0h-5c1-1 1-1 1-2h-1c-2 0-4-1-6 0h0l-2-1c-1 1-2 1-3 1h-1-4c-1-1-2-1-4-1z" class="W"></path><path d="M544 728c2 0 6 0 7-1l1 1h9 20 7 0c4 1 8 1 12 1v8 4l-2-1h0l-1-2h-2-14-3-8l-3-1-4 1v-4l1-2c1 0 3-1 4-1l-14-1h2v-1h-9c-1 0-2 0-3-1z" class="Y"></path><path d="M567 737l-2-1 1-2-1-1h3l1-1c1 1 1 1 2 1l1 2 1-2 1-1c0 1 0 1 1 2v-2l1 1v4l1 1v-5h3c1 1 1 3 1 5h-3-8l-3-1z" class="k"></path><path d="M544 728c2 0 6 0 7-1l1 1h9 20 7 0c4 1 8 1 12 1v8 4l-2-1h0l-1-2h-2l1-1c-1-1 0-1-1-2h0s1-1 1-2c1 0 1 1 3 0v-2h-1-3c-9-1-18 1-27 0l-14-1h2v-1h-9c-1 0-2 0-3-1z" class="b"></path><path d="M588 728c4 1 8 1 12 1v8 4l-2-1h0l-1-2h-2l1-1c-1-1 0-1-1-2h0s1-1 1-2c1 0 1 1 3 0v-2-1h-10c-1-1-2 0-2-1l1-1z" class="O"></path><path d="M528 718c0 1 1 2 1 3-1 2-2 3-3 4l-1 1c3 1 6 1 9 1l10 1h0c1 1 2 1 3 1h9v1h-2-13c-3 0-6 0-9 1l1 1h3 2c1 1 1 1 1 3h0c-1-1-1-1-1-2l-1 1v2l2 1c-8 1-16-1-24 0h-4-16v-1l1-1v-3h8l1 1v1 2h1l-1-1 1-1v-2-1h-10l-1-1h0c1-2 6-1 8-2 3-1 9 0 12 0l3-1 4-1c3-2 5-4 6-8z" class="l"></path><path d="M511 737l1-1h0c-1-2-1-2-3-2l1-1c-1-1-1-1-2-1v-1h7v6h-4z" class="D"></path><path d="M528 718c0 1 1 2 1 3-1 2-2 3-3 4l-1 1c3 1 6 1 9 1l10 1h0c1 1 2 1 3 1h9v1h-2-13c-3 0-6 0-9 1-1 0 0 0-1-1h-1l1-1c-2 0-1 1-2 1-1 1-6 0-7 0h-27 0c1-2 6-1 8-2 3-1 9 0 12 0l3-1 4-1c3-2 5-4 6-8z" class="C"></path><path d="M494 741l3-1c4 2 9 1 14 1h22l1 1h-2c0 2 0 3-1 4 2 1 3 1 4 1h1 0v-1h4c1 1 2 1 3 1 2 0 3 0 4 1h4 1c1 0 2 0 3-1l2 1h0c2-1 4 0 6 0h1c0 1 0 1-1 2v2l-2 1c-2-1-4-1-6-1l-1-2h-2c-2 1-3 1-5 0h-7c-3-1-7-1-10-1-1 0-1 1-2 0h-1c-2 0-3 0-5 1l3 1h-3v1 1h-1-2 0-4l-3 2v1 1c-1 1-1 1-1 2l-2 2c1 0 1 1 2 1 0 2 0 2-2 3 1 1 1 0 1 1h-2v-1c-2-4 0-7-1-10h-1c0-1 0-2-1-3h-4c-1 0 0 0-1-1-1 0-1 1-2 1l-1-1h-8c0 1-1 2-1 3h-1-2v-1c-2-1-4-1-6-1l-2 1c-2 0-3 1-4 3v1h-2l-1 1-1-1-2 1c0-2-1-3-3-4l-1-1v-2l-3-2c-1-1-2-3-4-4v-1c1 0 1 0 3-1 2 0 2 0 3 2l1-1h1c1-1 1-1 2-1 1 1 2 2 3 2v1c2 1 4 0 6 0 2-1 1-3 1-4h1l1-1 4-1 1 1h9l1-1 1 1z" class="D"></path><path d="M468 751h4 0c2-1 4 0 6 0l1 1-2 1c-2 0-3 1-4 3v1h-2c0-2 1-3 0-4-1 0-2-1-3-2z" class="C"></path><path d="M463 751h0l1-1h0c1 0 2 0 4 1 1 1 2 2 3 2 1 1 0 2 0 4l-1 1-1-1-2 1c0-2-1-3-3-4l-1-1v-2z" class="E"></path><path d="M478 751c3 0 8-2 9 0 2-1 3-1 5-1 4 0 9-1 13 1 3 1 4-1 7-1h3 7l3 1h-3v1 1h-1-2 0-4l-3 2v1 1c-1 1-1 1-1 2l-2 2c1 0 1 1 2 1 0 2 0 2-2 3 1 1 1 0 1 1h-2v-1c-2-4 0-7-1-10h-1c0-1 0-2-1-3h-4c-1 0 0 0-1-1-1 0-1 1-2 1l-1-1h-8c0 1-1 2-1 3h-1-2v-1c-2-1-4-1-6-1l-1-1z" class="Q"></path><path d="M508 765c0-3 0-6 1-8 2-3 4-3 6-4l-3 2v1 1c-1 1-1 1-1 2l-2 2c1 0 1 1 2 1 0 2 0 2-2 3 1 1 1 0 1 1h-2v-1z" class="K"></path><path d="M494 741l3-1c4 2 9 1 14 1h22l1 1h-2c0 2 0 3-1 4h-6 0c-3-1-1-3-2-4l-2 1v-1l1 1c0 1 0 2-1 3-2 0-4 0-6 1h-7c-1 1-2 1-3 1-3 0-7-1-10 0-1 1-2 0-3 0-2 0-3 0-4 1-1 0-2-1-3-1h-2-3l-1-1h1l-1-1h-2v-4l1-1 4-1 1 1h9l1-1 1 1z" class="h"></path><path d="M493 740l1 1v1h-2c1 2 2 2 2 4h-5v-4l3-1 1-1z" class="P"></path><path d="M483 741h9l-3 1v4h0-1-2c-2 0-1-2-1-3l-2-2h0z" class="O"></path><path d="M482 740l1 1h0l-1 2 1 2c-1 1-1 1-3 1h-1-2v-4l1-1 4-1z" class="F"></path><path d="M480 746c0-1-1-2-1-3l1-1h2v1l1 2c-1 1-1 1-3 1zm17-6c4 2 9 1 14 1h22l1 1h-2c0 2 0 3-1 4h-6 0c-3-1-1-3-2-4l-2 1v-1l1 1c0 1 0 2-1 3-2 0-4 0-6 1 0-2 0-4-1-5h-1l-1 1 1 1h0l-1 2h-6-1c-2-1-1-2-2-4h-2c1 2 1 2 1 3l-1 1h-6v-4-1l2-1z" class="M"></path><path d="M522 750c2-1 3-1 5-1h1c1 1 1 0 2 0 3 0 7 0 10 1h7c2 1 3 1 5 0h2l1 2-2 1c-2 1-3 2-4 5v7 10l-1 1h-2-2-4c-4 0-9 1-13 0v-2h0l-1 2c0 1-1 1-2 1v1h-8-8l-1-2v-9l1-1h2c0-1 0 0-1-1 2-1 2-1 2-3-1 0-1-1-2-1l2-2c0-1 0-1 1-2v-1-1l3-2h4 0 2 1v-1-1h3l-3-1z" class="L"></path><path d="M545 767l1 1c0 1 0 1-1 2l1 1v2 3h-2-4c1 0 2-1 2-2h0l-1 1c-1-1-1-1-2-1h-1v-2h1v-4h6v-1z" class="T"></path><path d="M542 774c0-1-1-3 0-4 1 2 0 3 2 4 1 0 1 0 2-1v3h-2-4c1 0 2-1 2-2h0z" class="J"></path><path d="M519 753h2 1v-1-1h3 4c1 1 4 0 6 0s4 1 6 1c-1 0-3 1-5 0h0 0-1-7c-1 1-2 3-1 4 0 1 0 1 1 1h0 2v1l-1 1h0v5 1h-1c-1-1-1-1-2-3v-4c-1-3-4-4-7-5z" class="G"></path><defs><linearGradient id="Y" x1="533.976" y1="776.501" x2="531.39" y2="766.986" xlink:href="#B"><stop offset="0" stop-color="#d7d7d4"></stop><stop offset="1" stop-color="#fefdfc"></stop></linearGradient></defs><path fill="url(#Y)" d="M527 774c1 0 1 0 2-1v-6l1-1 1 1c1 1 5 0 6 1l-1 1v1h1c1 1 1 1 1 2v2h1c1 0 1 0 2 1l1-1h0c0 1-1 2-2 2-4 0-9 1-13 0v-2z"></path><path d="M522 750c2-1 3-1 5-1h1c1 1 1 0 2 0 3 0 7 0 10 1h7c2 1 3 1 5 0h2l1 2-2 1c-2 1-3 2-4 5v7 10l-1 1h-2v-3-2l-1-1c1-1 1-1 1-2l-1-1h-2v-1h3c1-1 1-6 1-8v-2c0-1-1-2-2-3h0c-1-1-2-1-4-1s-4-1-6-1-5 1-6 0h-4l-3-1z" class="N"></path><path d="M529 751c1-1 7-1 8 0 4 1 10 0 13 0 0 2-2 2-2 3s0 1-1 2c0-1-1-2-2-3h0c-1-1-2-1-4-1s-4-1-6-1-5 1-6 0z" class="J"></path><path d="M515 753h4 0c3 1 6 2 7 5v4c1 2 1 2 2 3h1l2 1h-1l-1 1v6c-1 1-1 1-2 1h0l-1 2c0 1-1 1-2 1v1h-8-8l-1-2v-9l1-1h2c0-1 0 0-1-1 2-1 2-1 2-3-1 0-1-1-2-1l2-2c0-1 0-1 1-2v-1-1l3-2z" class="F"></path><path d="M523 758l2 2c-1 1-1 2-1 4h-2 0c0-2 0-4 1-6z" class="B"></path><path d="M526 762c1 2 1 2 2 3h1l2 1h-1l-1 1v6c-1 1-1 1-2 1h0c-1-4 0-8-1-12z" class="J"></path><path d="M516 776c0-1 1-2 1-2 1-1 1-2 2-3v4c2 2 5 1 7 1 0 1-1 1-2 1v1h-8v-2h0z" class="I"></path><path d="M511 762l1 1v1h2l1-3c0-2 0-3 1-5 2 0 2 0 4 1 0 2-1 7 0 8h4l1 1c-2 1-4 0-6 1v4c-1 1-1 2-2 3 0 0-1 1-1 2 0-3 1-8-1-10h-5c0-1 0 0-1-1 2-1 2-1 2-3z" class="D"></path><path d="M508 766h2 5c2 2 1 7 1 10h0v2h-8l-1-2v-9l1-1z" class="B"></path><path d="M510 768c1 0 2 0 3 1v2c-1 0-2 0-2-1-1-1-1-1-1-2z" class="j"></path><path d="M507 776c3 0 6-1 9 0v2h-8l-1-2z" class="E"></path><path d="M598 740l2 1h2 1c0 1 1 1 2 1l1 2 1-1 1 1c1 0 1 1 2 2l-1 1c-1 0-1 0-1 2l2-1v2c1 1 2 1 4 1l-1 1h1v1c0 1-1 1-1 2 1 1 2 1 2 3 0 1 0 2-1 3 1 1 1 2 2 2v1c1 1 2 1 3 3h-6c1 2 1 2 0 4l-1 1c-2-1-1-2-4-2l1 1c0 2 0 4-1 6 1 1 1 1 0 2v4c2 1 3 1 5 1l1 1c-1 0-1 1-2 1h0c-1 1-2 1-3 1 2 2 2 3 3 4 0 1-1 2-1 3l-2-1-1 1-1 1h-1c0 1 0 1 1 2l-1-1-2 1v1h-1v-1h1c-1-1-2-1-3-1h-1l-2 2c-1 1-1 1-1 2-1-1-1-2-2-3l2-2v-4h0-6-7-4-6-4-4c2-1 5 0 8 0v-1h-3-1l-1-1h-1-6l-1-3v-2c1-1 1-2 3-2v-1h1c4-1 9-1 13-1h0c-1 0-3 0-4-1h-1-3v-1h-20c-2-1-5-1-6-2h2 2l1-1v-10-7c1-3 2-4 4-5l2-1c2 0 4 0 6 1l2-1v-2h5c1 1 5 0 6 0h1c3 0 8-1 11 0 3 0 4 0 7-2 1-1 3-2 4-4l1-1-1-2 1-1z" class="P"></path><path d="M572 775v2c2 1 2 1 4 1 1 0 2-1 3-1 1-1 1-1 2-1l1-1v1 1h0 2v-1l2-1v1c1 1 1 2 2 3h-15-3v-1l1-1c0-1 0-1 1-2z" class="H"></path><path d="M588 779h5l-1-1 1-1v1h0 1v-2c0-1 1-1 2-1 1 1 0 4 1 4 0 0 2 0 2 1v1h-29-5c4-1 9-1 13-1h0c-1 0-3 0-4-1h-1 15z" class="D"></path><path d="M601 757l1-1h2l1 2c0 1 0 2 1 3v2 2h1v1l1 1h5c1 2 1 2 0 4l-1 1c-2-1-1-2-4-2l1 1c0 2 0 4-1 6h-1v2c-1 1-2 1-3 1l-1-1v-2c-2 0-3-1-4-2 2-1 4 0 5-3h1l2 1v-1c0-2 0-2-2-3v-4h-2v-1l1-1v-1c-1-1-1-2-2-3l-1 1h0v-3z" class="n"></path><path d="M591 763c0 2-1 4 1 5h2 4c1-1 1-2 2-3l-1-1-1-1v-1l2-1c1-1 0-2 1-4v3h0l1 11h-26-6l1-1h15 0c1-1 2-1 3-2v-1c0-1 0-3 1-4v-1l1 1z" class="R"></path><path d="M598 740l2 1h2 1c0 1 1 1 2 1l1 2 1-1 1 1c1 0 1 1 2 2l-1 1c-1 0-1 0-1 2l2-1v2c1 1 2 1 4 1l-1 1h1v1c0 1-1 1-1 2 1 1 2 1 2 3 0 1 0 2-1 3 1 1 1 2 2 2v1c1 1 2 1 3 3h-6-5l-1-1v-1h-1v-2-2c-1-1-1-2-1-3l-1-2h-2l-1 1c-1 2 0 3-1 4l-2 1v1l1 1 1 1c-1 1-1 2-2 3h-4-2c-2-1-1-3-1-5l-1-1v-1c0-1 0-2-1-3l-1-1-1-1 1-1h1c0-2 2-2 3-2l1-1v-4c1-1 3-2 4-4l1-1-1-2 1-1z" class="d"></path><path d="M588 755l2-1 2 3-1 1v5l-1-1v-1c0-1 0-2-1-3l-1-1-1-1 1-1z" class="W"></path><path d="M608 749l2-1v2c1 1 2 1 4 1l-1 1h1v1c0 1-1 1-1 2 1 1 2 1 2 3 0 1 0 2-1 3 1 1 1 2 2 2v1c1 1 2 1 3 3h-6-5c1-1 1-2 2-4l-2-2c-1-2-2-3-2-5v-1-3h1c1-1 1-2 1-3z" class="Q"></path><path d="M610 763v2h1c0-1 1-1 1-1 2 0 1 1 4 1v-1c1 1 2 1 3 3h-6-5c1-1 1-2 2-4z" class="N"></path><path d="M608 749l2-1v2c1 1 2 1 4 1l-1 1h1v1c0 1-1 1-1 2 1 1 2 1 2 3 0 1 0 2-1 3-2-3-4-6-7-9 1-1 1-2 1-3z" class="Y"></path><path d="M598 740l2 1h2 1c0 1 1 1 2 1l1 2 1-1 1 1c1 0 1 1 2 2l-1 1c-1 0-1 0-1 2 0 1 0 2-1 3h-1c-1 0-2 0-3 1h-9l-1-1v-4c1-1 3-2 4-4l1-1-1-2 1-1z" class="O"></path><path d="M598 740l2 1h2 1c0 1 1 1 2 1l1 2h-1c0 1 0 2-1 3-1 0-2 0-3-1s-1-2-3-3l-1-2 1-1z" class="Y"></path><path d="M598 743c2 1 2 2 3 3v2c0 1 1 2 1 3h1v1 1h-9l-1-1v-4c1-1 3-2 4-4l1-1z" class="P"></path><path d="M596 750l1-2h3 1c0 1 1 2 1 3h1v1 1h-9c1-2 1-2 2-3z" class="S"></path><path d="M596 750l1-2h3c-1 1-1 2-2 3 0 0-1-1-2-1z" class="B"></path><path d="M603 779l1 1c1 0 2 0 3-1v-2h1c1 1 1 1 0 2v4c2 1 3 1 5 1l1 1c-1 0-1 1-2 1h0c-1 1-2 1-3 1 2 2 2 3 3 4 0 1-1 2-1 3l-2-1-1 1-1 1h-1c0 1 0 1 1 2l-1-1-2 1v1h-1v-1h1c-1-1-2-1-3-1h-1l-2 2c-1 1-1 1-1 2-1-1-1-2-2-3l2-2v-4h0-6-7-4-6-4-4c2-1 5 0 8 0v-1h-3-1l-1-1h-1-6l-1-3v-2c1-1 1-2 3-2v-1h1 5 29v-1l4-1z" class="Y"></path><path d="M575 789h11c1-2 2-3 2-4h1v3c2 2 6 0 8 1l1 1-1 1h-6-7-4-6-4-4c2-1 5 0 8 0v-1h-3-1l-1-1c1 0 1-1 2 0h4z" class="C"></path><path d="M565 781h5v1c1 0 1 1 2 1l1 1 1-1h1c1 1 0 3 1 4v1c-1 1-1 0-1 1h-4c-1-1-1 0-2 0h-1-6l-1-3v-2c1-1 1-2 3-2v-1h1z" class="l"></path><path d="M572 783l1 1 1-1v5h-2v-5z" class="Z"></path><path d="M561 786v-2c1-1 1-2 3-2v3l-1 3c2 1 4 0 5 1h-6l-1-3z" class="g"></path><path d="M565 781h5v1c-1 0-1 0-2 1s0 2-1 3c0 0-1 0-1-1h-1-1v-3-1h1z" class="H"></path><path d="M603 779l1 1c1 0 2 0 3-1v-2h1c1 1 1 1 0 2v4c2 1 3 1 5 1l1 1c-1 0-1 1-2 1h0c-1 1-2 1-3 1-1 1-2 1-3 2 0 2 1 4-1 6l-1-1h-4 0l-1-1h2v-1c-1 0-1 0-1-1v-2-7-1h-1v-1l4-1z" class="R"></path><path d="M604 794l-2-2c1-1 0-1 1-1l-1-1c0-2 0-2 1-3 0 1 0 0 1 1l1-3c2 0 5 0 7 1h0c-1 1-2 1-3 1-1 1-2 1-3 2 0 2 1 4-1 6l-1-1z" class="e"></path><path d="M593 748v4l-1 1c-1 0-3 0-3 2h-1l-1 1 1 1 1 1c1 1 1 2 1 3v1 1c-1 1-1 3-1 4v1c-1 1-2 1-3 2h0-15l-1 1h6c0 1-1 1-1 2-1 1-2 1-3 2s-1 1-1 2l-1 1h-20c-2-1-5-1-6-2h2 2l1-1v-10-7c1-3 2-4 4-5l2-1c2 0 4 0 6 1l2-1v-2h5c1 1 5 0 6 0h1c3 0 8-1 11 0 3 0 4 0 7-2z" class="g"></path><path d="M568 756l2 2v7h-1s-1 0-2 1c1-2 1-2 1-3 1-2 0-5 0-7z" class="d"></path><path d="M558 759h4 0v4h-1v1h1v1h-3c-1-2-1-4-1-6z" class="S"></path><path d="M558 767c2-1 3-1 4-1 1 2 1 3 0 4h1v3c-1 1-3 0-4 0-1-1-1-2-1-3h0v-3z" class="M"></path><path d="M558 767c2-1 3-1 4-1l-1 2h0v2l-1 1-2-1v-3z" class="B"></path><path d="M571 766c1-1 2-1 3-1 0 0 1 1 2 1v-2l1-1h1v3l2 1s1 0 1 1h0-2c-1 0-2 1-3 1-2 0-5 0-6-1v-1h-1c-1-1-1-1-1-2l3 1z" class="R"></path><path d="M555 752c2 0 4 0 6 1 1 2 1 3 1 6h0-4c0-1 1-2 0-3-2-1-2 0-3 0l-2 1-1-1 1-3 2-1z" class="O"></path><path d="M555 756c1 0 1-1 3 0 1 1 0 2 0 3 0 2 0 4 1 6l-1 2v3h0l-2 1v3h-1c1-2 0-5 0-7v-1h-1l-3-1c3 0 3 0 4-1 1-3 1-5 0-8z" class="D"></path><path d="M549 765h2l3 1h1v1c0 2 1 5 0 7h0c-2 0-4 0-6 1v-10z" class="S"></path><path d="M549 765h2l3 1v3h-1-1c0-1 0-1-1-1l-1 1h1v4c1 1 3 0 4 1-2 0-4 0-6 1v-10z" class="K"></path><path d="M576 760v1l1 1 1-1c-1-1-1-2-1-3l1-1h1c1 1 1 3 1 4l-1 1 1 1v4l-2-1v-3h-1l-1 1v2c-1 0-2-1-2-1-1 0-2 0-3 1 1-3 0-7 1-9h3c0 1 1 2 1 3z" class="U"></path><path d="M549 765v-7c1-3 2-4 4-5l-1 3 1 1 2-1c1 3 1 5 0 8-1 1-1 1-4 1h-2z" class="a"></path><path d="M554 760h1v3h-3l-1-1v-1c1 0 2 0 3-1z" class="S"></path><path d="M558 770c0 1 0 2 1 3s4 1 6 1h5c0 1-1 1-1 2s1 1 1 2h-20c-2-1-5-1-6-2h2 2l1-1c2-1 4-1 6-1h0 1v-3l2-1z" class="I"></path><path d="M593 748v4l-1 1c-1 0-3 0-3 2h-1l-1 1 1 1 1 1c1 1 1 2 1 3v1 1c-1 1-1 3-1 4v1c-1-1-1-2-1-4-1 0-1 1-2 2-1-1-2-1-3-2v2h-1c-1-1-1-3-1-4h0c1-2 0-3 1-5l1 1v3h2v-2l-1-1c1-1 1-1 1-2l-1-1h-5l-1 1c-1 0-1 0-2 1v2 1c0-1-1-2-1-3-1-2-1-1-2-1l1-1v-1l-1-1c-1 1-2 1-3 1s-3 2-4 3l2-1c0 2 1 5 0 7 0 1 0 1-1 3 0-1 0 0-1 0 0 2 1 6 0 7-1 0-1 0-2-1 0-1 1-5 0-7v-8c0-1-1-3-1-5v-2h5c1 1 5 0 6 0h1c3 0 8-1 11 0 3 0 4 0 7-2z" class="i"></path><path d="M588 757l1 1c1 1 1 2 1 3v1 1c-1 1-1 3-1 4v1c-1-1-1-2-1-4s-1-5 0-7z" class="F"></path><path d="M593 748v4l-1 1-21-1h-7c1-1 3-1 4-2 1 1 5 0 6 0h1c3 0 8-1 11 0 3 0 4 0 7-2z" class="S"></path><path d="M448 764c0-5-1-11 1-15 0-3 1-4 3-6 1 0 2 0 3 2v1h0l1-1c2 1 3 3 4 4l3 2v2l1 1c2 1 3 2 3 4l2-1 1 1 1-1h2v-1c1-2 2-3 4-3l2-1c2 0 4 0 6 1v1h2 1c0-1 1-2 1-3h8l1 1c1 0 1-1 2-1 1 1 0 1 1 1h4c1 1 1 2 1 3h1c1 3-1 6 1 10v1l-1 1v9l1 2h8 8v-1c1 0 2 0 2-1l1-2h0v2c4 1 9 0 13 0h4c1 1 4 1 6 2h20v1h3 1c1 1 3 1 4 1h0c-4 0-9 0-13 1h-1v1c-2 0-2 1-3 2v2l1 3h6 1l1 1h1 3v1c-3 0-6-1-8 0s-5 1-7 1c-3-1-9-1-12-1h-39-35-3-24-1c-2-1-5-1-7-1h-4c-2-1-3-1-4-3h1 1v-2-4l2 1v-1l6-1h-6c1-1 2-1 3-1l1 1v-1h5v-1c1-3 1-6 3-8v-2h0 2v-4z" class="d"></path><path d="M479 781h6l-2 2c-1 1 0 1-1 2h-2v1c-1-1-1-3-1-5z" class="K"></path><path d="M485 781h15c-1 2-1 3-1 5h-1v-1l-2 1c-1-1-1-1-1-2-1 0-1 1-1 2-1 0-1-1-2-1h0v-3c-2 0-1 1-2 2v1h-1v-1-1h-1c-1 1-1 1-1 2h-1-1c-1 1-1 1-2 1h-1v-1c1-1 0-1 1-2l2-2z" class="l"></path><path d="M432 781l2 1 1 1 1 1c1 2 1 2 1 3l-1 1c2 1 3 1 5 1h6c3-1 6 0 9 0l49-1v1h-2c-3-1-6 0-9 0 0 0 0 1 1 1 2 1 5 0 8 1h5-35-3-24-1c-2-1-5-1-7-1h-4c-2-1-3-1-4-3h1 1v-2-4z" class="N"></path><path d="M435 783l1 1c1 2 1 2 1 3l-1 1h-2c0-2 0-3 1-5z" class="a"></path><path d="M460 779h9c0 1 1 1 2 2h6 1l-1 1-1 1-1 3h0l-1-1s-1 1-2 0c0 0-2-2-2-3h-1c0 1-1 2-1 3h-1-1c-3 0-2-2-5 0l-1-1h0c0-1 0-1-1-2l-1 2v2 1h-1v-1c-1-3 0 2-1-2v-1l-1-1c-1 2 0 3-2 3h-2v2h-1v-1l-1-4h-1c0 2-1 2-2 4h-1v-3l-1-1c-1 1 0 3-1 4l-1-1c-1 0-1-1-2-1h0l-1-1h0v-1l-3 2-1-1-1-1v-1l6-1h-6c1-1 2-1 3-1l1 1v-1h5 12 5z" class="H"></path><path d="M460 779h9c0 1 1 1 2 2h-11l2-1h1c-1 0-2-1-3-1z" class="N"></path><path d="M443 779h12 5c1 0 2 1 3 1h-1l-2 1-20-1h-6c1-1 2-1 3-1l1 1v-1h5z" class="C"></path><path d="M496 775c0-3 0-5 1-7v1 4l1 1c0 2 1 2 3 4h7 8 8v-1c1 0 2 0 2-1l1-2h0v2c4 1 9 0 13 0h4c1 1 4 1 6 2h20v1h3 1c1 1 3 1 4 1h0c-4 0-9 0-13 1h-1-16-2-4 0-7-2-1-7-7-12-6-15-6l-1-1-1 1h-6c-1-1-2-1-2-2h-9-5-12v-1h2 7c4 0 10 1 14 0h11v-1h4c1-1 1-1 1-2 1 0 1 0 2-1h4l1-1v3h6l1-1z" class="Z"></path><path d="M469 779h9v1l-1 1h-6c-1-1-2-1-2-2z" class="C"></path><path d="M515 779h18l-1 2h-7c-3-1-7-1-9-1h-1v-1zm-26-6v3c-1 1-2 1-3 2-2 1-6 0-9 0v-1h4c1-1 1-1 1-2 1 0 1 0 2-1h4l1-1z" class="N"></path><path d="M496 775c0-3 0-5 1-7v1 4l1 1c0 2 1 2 3 4h-15c1-1 2-1 3-2h6l1-1z" class="X"></path><path d="M527 774h0v2c4 1 9 0 13 0h4c1 1 4 1 6 2h-26v-1c1 0 2 0 2-1l1-2z" class="C"></path><path d="M570 779h3 1c1 1 3 1 4 1h0c-4 0-9 0-13 1h-1-16-2-4 0-7-2-1l1-2h37z" class="E"></path><path d="M478 779h37v1h1c2 0 6 0 9 1h-7-12-6-15-6l-1-1v-1z" class="I"></path><path d="M487 754h1c0-1 1-2 1-3h8l1 1c1 0 1-1 2-1 1 1 0 1 1 1h4c1 1 1 2 1 3h1c1 3-1 6 1 10v1l-1 1v9l1 2h-7c-2-2-3-2-3-4l-1-1v-4-1c-1 2-1 4-1 7l-1 1h-6v-3l-1 1h-4-2v-5-2-1c1-2 2-2 4-2h-1-1c1-1 1-1 1-2v-1l2-1c0-2-1-2-2-4v-2h2z" class="L"></path><path d="M498 774c1 0 2-1 4 0l2 2h3c0-3-1-6 0-8v-1 9l1 2h-7c-2-2-3-2-3-4zm-11-20h1c0-1 1-2 1-3h8l1 1c1 0 1-1 2-1 1 1 0 1 1 1h4-1c-3 1-6 0-9 1-2 0-4 0-5-1l-1 1c0 2 1 3 2 5v1c-1 2 0 5 0 6l1 1-1 2c0 2 0 5 2 6 1 0 2 0 3 1l-1 1h-6v-3l-1 1h-4-2v-5-2-1c1-2 2-2 4-2h-1-1c1-1 1-1 1-2v-1l2-1c0-2-1-2-2-4v-2h2z" class="G"></path><path d="M485 754h2c2 4 2 7 2 12-2 0-2 1-4 1h-3v-1c1-2 2-2 4-2h-1-1c1-1 1-1 1-2v-1l2-1c0-2-1-2-2-4v-2z" class="K"></path><path d="M489 766v7l-1 1h-4-2v-5-2h3c2 0 2-1 4-1z" class="F"></path><path d="M482 767h3v1 2h-2v-1h-1v-2z" class="S"></path><path d="M518 781h7 7 1 2 7 0 4 2 16v1c-2 0-2 1-3 2v2l1 3h6 1l1 1h1 3v1c-3 0-6-1-8 0s-5 1-7 1c-3-1-9-1-12-1h-39-5c-3-1-6 0-8-1-1 0-1-1-1-1 3 0 6-1 9 0h2v-1c2 0 2 0 3-1l-1-1c-1-1-1-4-1-5h12z" class="J"></path><path d="M506 781h12v1h3c-1 1 0 1-1 2-1 0-1 1-1 2h0 1 0l2 2c1-1 1-2 2-3h1l1 3s2 0 3 1h0c-8 1-16 0-24 0v-1c2 0 2 0 3-1l-1-1c-1-1-1-4-1-5z" class="g"></path><path d="M506 781h12v1c0 1 0 2-1 3h0v2c-2-2-1-4-4-6-1 2-1 4-2 5l-1-2c-1 1-2 1-3 2-1-1-1-4-1-5z" class="a"></path><path d="M518 781h7 7 1 2 7 0 4 2 16v1c-2 0-2 1-3 2v2l1 3h-33c-1-1-3-1-3-1l-1-3h-1c-1 1-1 2-2 3l-2-2h0-1 0c0-1 0-2 1-2 1-1 0-1 1-2h-3v-1z" class="i"></path><path d="M535 781h7l-2 2c-1 0-1-1-1-1-1 1-1 1-1 3h0-1-1l-1 1h0v-5z" class="Y"></path><path d="M542 781h4v6l-2-2c-1 1-1 1-2 1 0 0 0-1-1-2 0-1 1-2 1-3z" class="K"></path><path d="M518 781h7 7 1 0c-1 2-1 3-1 5h-1c0-1-1-3-1-4h-1-1v5c-1-1-1-2-1-2-1-1-2-1-2-1-1-1 0-1-1-2-1 1-1 1-1 2-1 0-2 1-3 2h-1 0c0-1 0-2 1-2 1-1 0-1 1-2h-3v-1z" class="U"></path><path d="M548 781h16v1c-2 0-2 1-3 2v2l-1-1c-1 1-1 1-2 1v-4c-1 0-1 0-1-1l-2 3 1 1c-3 0-1 0-3 1l-1-4-1 1c-1 1-1 2-2 3v1h0l-1-6z" class="O"></path><path d="M448 764c0-5-1-11 1-15 0-3 1-4 3-6 1 0 2 0 3 2v1h0l1-1c2 1 3 3 4 4l3 2v2l1 1c2 1 3 2 3 4l2-1 1 1 1-1h2v-1c1-2 2-3 4-3l2-1c2 0 4 0 6 1v1 2c1 2 2 2 2 4l-2 1v1c0 1 0 1-1 2h1 1c-2 0-3 0-4 2v1 2 5h2c-1 1-1 1-2 1 0 1 0 1-1 2h-4v1h-11c-4 1-10 0-14 0h-7-2c1-3 1-6 3-8v-2h0 2v-4z" class="C"></path><path d="M479 752c2 0 4 0 6 1v1 2c1 2 2 2 2 4l-2 1v1c0 1 0 1-1 2h-1c-1-3 0-6-2-9h-1c-3 3 0 8-3 11-1-2-1-3-1-4h-1v1l-1-1v-2c0-1 0-1-1-2v-1-1c1-2 2-3 4-3l2-1z" class="P"></path><path d="M473 757v-1c1-2 2-3 4-3-1 1-2 3-2 4h2c1 1 0 4 0 5h-1-1v1l-1-1v-2c0-1 0-1-1-2v-1z" class="K"></path><path d="M471 757h2v1c1 1 1 1 1 2v2l1 1v-1h1c0 1 0 2 1 4l-2-1 2 2h2v6c-1 1-4 1-6 1v-7c-2 0-3 0-4 1h0l-1-2h0v-2h0c-1-2 0-4-1-6l2-1 1 1 1-1z" class="M"></path><path d="M471 757h2v1 5 2 2c-2 0-3 0-4 1h0l-1-2h0v-2h0c-1-2 0-4-1-6l2-1 1 1 1-1z" class="Q"></path><g class="I"><path d="M473 765v2c-2 0-3 0-4 1h0l-1-2h0v-2c2 0 3 1 5 1z"></path><path d="M471 757h2v1 5c-1-2-1-2-2-3l-1-1c-1 0-1 0-2 2v3c-1-2 0-4-1-6l2-1 1 1 1-1z"></path></g><path d="M448 764l1 3h7 2l2-1h5 3 0l1 2c-1 1-3 1-3 2v2h-1l-3-3c-1 2-1 3 0 5h-1v2l2 1h0l1 1h2 0c-4 1-10 0-14 0h-7-2c1-3 1-6 3-8v-2h0 2v-4z" class="D"></path><path d="M445 778c1-1 1-3 2-4s2-1 3-1c1-1 1-1 2-1 1 1 2 2 2 3l-2 3h-7z" class="I"></path><path d="M448 764l1 3h7 2l2-1h5 3 0c-3 2-8 0-10 2l1 3h-1-1c-1-1-3-1-4-1l-1 1c-1-1-2-1-2-1h-2l-1-1-1 1v-2h0 2v-4z" class="U"></path><path d="M454 775h0c1-1 2-1 3-2h1c1-1 1-2 1-3v-1l2-1 1 1c-1 2-1 3 0 5h-1v2l2 1h0l1 1h2 0c-4 1-10 0-14 0l2-3z" class="e"></path><path d="M448 764c0-5-1-11 1-15 0-3 1-4 3-6 1 0 2 0 3 2v1h0l1-1c2 1 3 3 4 4l3 2v2l1 1c2 1 3 2 3 4 1 2 0 4 1 6h0v2h-3-5l-2 1h-2-7l-1-3z" class="j"></path><path d="M460 749l3 2v2c-1-1-2-1-3-1-1 2-1 3 0 5v3l-1 1 1 2v3l-2 1c0-4-1-16 0-18h2z" class="h"></path><path d="M460 760v-3c-1-2-1-3 0-5 1 0 2 0 3 1l1 1c2 1 3 2 3 4 1 2 0 4 1 6h0v2h-3-5v-3l-1-2 1-1z" class="D"></path><path d="M460 763c2-1 4-1 6-2 1 2 0 3-1 5h-5v-3z" class="I"></path><path d="M460 760v-3c-1-2-1-3 0-5 1 0 2 0 3 1l1 1v1h1c1 2 1 3 1 4-2 1-2 1-4 1-1 1-1 0-2 0z" class="e"></path><path d="M513 334l13 1v10h1 1v-9-1c3 0 6 0 9 2v1c1 1 1 9 0 11-1 0-1 0-2-1h-1c1 1 2 2 3 2l2 1c0 2 1 6 2 8 1 1 2 1 3 1h0v4c-1 1-1 0-1 1v13c1 1 1 1 2 1s2 1 3 1c0 2 0 6-1 8l1 1h3c1 0 1 0 2 1l1-1c1 0 1 0 2 1h0c2 0 2 1 3 1 2 0 4 1 6 2l5 3c1 1 3 2 4 3s1 2 2 3v1l-1-1c-1 8 0 16-1 24l-4-2h-1c-1-1-2-1-3-1h-1 0v1h3l1 1h0c1 0 2 0 3 1s2 1 4 1c1 0 1 0 2 1 1 0 2 1 4 1h1c2 0 5 2 7 1 2 0 2-1 5-1 0 1-1 1 0 2s2 2 2 4v5c-1 2-4 5-6 7l2 2c-3 3-3 7-2 12v1l-1 3c0-1-1 0-1 0-1 2-2 2-4 3-4 3-8 6-13 9v-1l3-2h-2l-1 1-1-3-1-2c-1-2-1-3-1-6-2 0-4-1-7-2-1 0-3-1-4-1h0c-2 0-3-1-4-1-3-1-7-1-9-2l-2-1-4 3 2 2c0 1-2 2-2 3-2 1-4 3-6 4h0-3c-8-5-11-15-14-24h-1c0-1 0-1 1-2 0-3 0-9-2-11 1 9 0 18-2 27s-5 18-5 28c-1-1-1-1-1-2l-8-17c-1-2-2-5-3-7l-3-6c-1-4-3-9-8-11-2 1-3 2-5 4l-10 10-6 6c-2-1-1-9-1-11v-8c-1 1-1 1-1 2-1 1 0 1-1 2l-1-39 1-13-1-23v-6c1 0 3-2 3-2 1-1 2-1 3-2 2-3 11-6 14-8h0l1-1h-1l1-1 4-1c1-1 1-1 2-1h1v-1h-1c2-1 4-1 6-1h1 1c5-1 10-2 15-2h2l1-1c0-4-1-10 0-13h1z" class="o"></path><path d="M466 370c1 1 1 13 1 15l-2 1h-1c1-5 1-10 1-15l1-1z" class="Y"></path><path d="M479 397c0-2 1-4 3-5 1-1 3-1 4-1 2 1 2 2 3 4-3 1-5 0-7 1-1 1-2 1-3 1z" class="B"></path><path d="M465 415c1 1 2 3 3 3h2v1c-1 1-1 2-1 4l1 1v2h1v2c-1 1-1 2-2 2h-2l-1-1c1-1 0-1 0-2-1-1-1-2-1-3h-1c0-2 0-4 1-5v-4z" class="T"></path><path d="M487 363h2 1c0 4 1 10-1 14l-1 1h-2v-6c-1-2-1-5-1-7 1-2 1-1 2-2z" class="p"></path><path d="M462 453l1-1c1-1 1-3 2-4l1-1v1h1c-1-2-1-2 0-3h1c0 2 0 4 1 6v5 2l-6 6c-2-1-1-9-1-11z" class="f"></path><path d="M526 349l1 1v3l-1 1h0c-3-1-6-1-9-1-13-1-27 0-39 5-4 1-9 4-13 6 2-3 11-6 14-8 8-2 15-4 23-5h11c4 0 8-1 11 0h2v-1l-2-1h2z" class="l"></path><path d="M460 397h1 0 1c-1 1-1 2-2 2v3h1c1 0 3-1 4-2 0 0 1-1 2-1 2-1 4-2 5-2 1 1 1 2 1 3l-1 1c0 1-1 2-2 3h0v-1-2h-1l-1 4c-1 1-1 3-1 5l-1 1c-1-1-1-1-1-2 0 1-1 1-1 2l-1-1h-1l-1-1-2 1 1-13z" class="L"></path><path d="M512 347l1 1c2 1 8 2 10 1h1c1-1 1-1 2 0h-2l2 1v1h-2c-3-1-7 0-11 0h-11c-8 1-15 3-23 5h0l1-1h-1l1-1 4-1c1-1 1-1 2-1h1v-1h-1c2-1 4-1 6-1h1 1c5-1 10-2 15-2h2l1-1z" class="N"></path><path d="M459 410l2-1 1 1h1l1 1v1l1 2v1 4c-1 1-1 3-1 5h1c0 1 0 2 1 3 0 1 1 1 0 2l-1-1h-1v4h1l1 1c0 1-1 1-2 2l-1-1h-1c0 2-1 5 0 6v2h0v3c-1 1-1 1-1 2-1 1 0 1-1 2l-1-39z" class="X"></path><path d="M489 395h0v37c-6 1-12 3-18 5-3 1-6 2-9 5h0v-2h1c5-3 10-5 16-6v-17-2-2c0-3-1-5 0-7v-1c-1-2 0-6 0-8 1 0 2 0 3-1 2-1 4 0 7-1zm31-8c2 1 4 2 5 4v2 24 10c0 1 0 2-2 3h-1-7l-1-1v-10-19c0-3-1-8 2-11 1-1 2-1 4-2z" class="j"></path><path d="M520 387c2 1 4 2 5 4v2c0 1-1 3-1 3h-8 0c2 2 6 0 8 2-2 1-5 0-7 0h-2 0c2 1 8 0 9 2-2 1-8 0-10 0 0-3-1-8 2-11 1-1 2-1 4-2z" class="M"></path><path d="M513 334l13 1v10h1 1v-9-1c3 0 6 0 9 2v1c1 1 1 9 0 11-1 0-1 0-2-1h-1c1 1 2 2 3 2l2 1c0 2 1 6 2 8 1 1 2 1 3 1h0v4c-1 1-1 0-1 1v13c1 1 1 1 2 1s2 1 3 1c0 2 0 6-1 8l1 1h3c1 0 1 0 2 1l1-1c1 0 1 0 2 1h0c2 0 2 1 3 1 2 0 4 1 6 2l5 3c1 1 3 2 4 3s1 2 2 3v1l-1-1c-1 8 0 16-1 24l-4-2h-1c-1-1-2-1-3-1h-1 0c-2 0-3-1-4-2h-1-2c-1-1-2-2-3-2-3-1-6-2-8-4-2-1-4-2-5-2s-2 1-3 2h0c-1-6-1-12 0-18 0-3 0-7-1-10v4 1c-1 6-1 13-1 19 0 1 1 5 1 5l-2 2-5 5-1-1h1c-1-1-2-4-4-4 0 0-1 0-2-1h0v-24-2c-1-2-3-3-5-4v-2h-7c-1-1-3-1-5-1-1 1-3 1-5 1s-5 0-7 1h-3-3c-1 1-2 1-3 0h0 1c1-1 2-1 3-1l5-1c1 0 3 1 4 0l1-1v1c2-1 10-1 12-1v-7c1-3 0-8 1-10v-1-3c0-1 1-3 0-4v-5h3c3 0 6 0 9 1h0l1-1v-3l-1-1c-1-1-1-1-2 0h-1c-2 1-8 0-10-1l-1-1c0-4-1-10 0-13h1z" class="G"></path><path d="M528 336v-1c3 0 6 0 9 2v1h-2v-1l-1-1c-1 2-1 2-2 3-2-1-2-2-3-3h-1z" class="N"></path><path d="M512 334l1 1c1 3 0 10 0 13l11 1h-1c-2 1-8 0-10-1l-1-1c0-4-1-10 0-13z" class="C"></path><path d="M530 380l1-1v5c2 1 3 1 5 1v-6l1-2c0 2 1 6 1 8 0 0-1 0 0 1v1 4c-1 0 0 0-1 1h0l-1-1v-2l-1-1 1-1v-1h-1-3l-1-1h-3c1-1 1-1 2-1v-4z" class="Q"></path><defs><linearGradient id="Z" x1="538.943" y1="366.55" x2="529.315" y2="382.152" xlink:href="#B"><stop offset="0" stop-color="#bab9b8"></stop><stop offset="1" stop-color="#e4e2e1"></stop></linearGradient></defs><path fill="url(#Z)" d="M535 361l1-2 1 1-1 5v3l1 1v8l-1 2v6c-2 0-3 0-5-1v-5l-1 1c-1-2-1-4 0-5 1-4-2-10 1-13h2s1 0 2 1v-2z"></path><path d="M537 350l2 1c0 2 1 6 2 8 1 1 2 1 3 1h0v4c-1 1-1 0-1 1v13c1 1 1 1 2 1s2 1 3 1c0 2 0 6-1 8-2-1-4-2-7-2l7 2c1 8 1 18 0 25v1 1c-2-1-4-2-5-2s-2 1-3 2h0c-1-6-1-12 0-18 0-3 0-7-1-10v-1c-1-1 0-1 0-1 0-2-1-6-1-8v-8l-1-1v-3l1-5-1-1-1 2c0-2 0-6 1-8 1-1 1-2 1-3z" class="C"></path><defs><linearGradient id="a" x1="574.139" y1="406.086" x2="547.784" y2="406.886" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#8d8d8c"></stop></linearGradient></defs><path fill="url(#a)" d="M547 415v-1-1c1-7 1-17 0-25l-7-2c3 0 5 1 7 2l1 1h3c1 0 1 0 2 1l1-1c1 0 1 0 2 1h0c2 0 2 1 3 1 2 0 4 1 6 2l5 3c1 1 3 2 4 3s1 2 2 3v1l-1-1c-1 8 0 16-1 24l-4-2h-1c-1-1-2-1-3-1h-1 0c-2 0-3-1-4-2h-1-2c-1-1-2-2-3-2-3-1-6-2-8-4z"></path><path d="M568 398h1l1 1c-2 3-1 6-1 9 1 4 1 9 0 12v-1h0l-1 1h0v-8-14z" class="i"></path><path d="M538 387c1 3 1 7 1 10-1 6-1 12 0 18h0c1-1 2-2 3-2s3 1 5 2c2 2 5 3 8 4 1 0 2 1 3 2h2 1c1 1 2 2 4 2v1h3l1 1h0c1 0 2 0 3 1s2 1 4 1c1 0 1 0 2 1 1 0 2 1 4 1h1c2 0 5 2 7 1 2 0 2-1 5-1 0 1-1 1 0 2s2 2 2 4v5c-1 2-4 5-6 7l2 2c-3 3-3 7-2 12v1l-1 3c0-1-1 0-1 0-1 2-2 2-4 3-4 3-8 6-13 9v-1l3-2h-2l-1 1-1-3-1-2c-1-2-1-3-1-6-2 0-4-1-7-2-1 0-3-1-4-1h0c-2 0-3-1-4-1-3-1-7-1-9-2l-2-1-4 3 2 2c0 1-2 2-2 3-2 1-4 3-6 4h0-3c-8-5-11-15-14-24h-1c0-1 0-1 1-2 0-3 0-9-2-11v-1l-2-1c0-1 1-1 2-1l1 1h7 1c2-1 2-2 2-3v-10h0c1 1 2 1 2 1 2 0 3 3 4 4h-1l1 1 5-5 2-2s-1-4-1-5c0-6 0-13 1-19v-1-4z" class="d"></path><path d="M574 442h0c1-4-2-7 1-10 1 0 1 0 2 1 0 2-1 5 0 8v2c-2 0-2 0-3-1z" class="e"></path><path d="M565 440v-1c0-3-1-7 0-10v-1c1 0 1 0 3 1l-1 2h1c1 3 0 7 0 10-1 0-3-1-3-1z" class="C"></path><path d="M570 441c-1-3-1-8 0-11 1 0 2 0 3 1v11c-1 0-2 0-3-1z" class="I"></path><path d="M583 442c1-2 0-4 1-6v-2c1 0 2 1 3 1h2v11c-1 1-2 1-4 1v-1c-1-1-1-2-1-3s0-1-1-1z" class="e"></path><path d="M555 424l7 3h1c1 2 0 6 1 9 0 1-1 2 0 3l-3-1c-1 0-2 0-3-1h0c-1-1-1-2-1-3 0-2 1-3 1-4 0-2 0-3-2-5l-1-1z" class="b"></path><path d="M563 427c1 2 0 6 1 9 0 1-1 2 0 3l-3-1-1-1h0l1-1h0v-2c0-1-1-2-1-3h1l2-4z" class="C"></path><path d="M589 435v-2h1v1 8 6l1-1 2 2c-3 3-3 7-2 12v1l-1 3c0-1-1 0-1 0v-5-4h-2c-5-1-10-3-14-5h0l1-1h-2c0-1 0-1 1-2 2 1 7 1 9 3 1 0 1 2 2 2 1 1 3 1 5 1v-1c0-1-1-1-2-2h1l1-1v-4-11z" class="K"></path><path d="M547 432l1 1v1l10 3c1 1 2 1 3 1l3 1 1 1s2 1 3 1h2c1 1 2 1 3 1h1c1 1 1 1 3 1h1l1-1c0-1 0-3-1-5h0c1-1 1-1 1-2v-2l1 1v5c-1 1-1 2 0 2v1l-1 2 1 1c1-2 0-4 1-7v-2s0-1 1-2v1c0 3 1 6 0 9v1l1 1v-1-3c1 0 1 0 1 1s0 2 1 3v1c2 0 3 0 4-1v4l-1 1c-3-2-7-3-10-3l-20-7c-5-1-9-2-13-5-1-1-1-1 0-3l2-1z" class="N"></path><path d="M542 417c2-1 3 0 4 1l1 1-1 1 1 1c1 0 0 0 1 1h0-1v1c1 1 1 5 1 7v3l-1-1-2 1c-1 2-1 2 0 3 4 3 8 4 13 5l20 7c3 0 7 1 10 3h-1c1 1 2 1 2 2v1c-2 0-4 0-5-1-1 0-1-2-2-2-2-2-7-2-9-3l-9-3h-3v-1 1c-1 1-1 2-2 2l-14-5c0 3-1 7 0 10h-1c-1 0-1-1-1-2v-21-2c0-2 0-5-1-6v-1-1-2z" class="V"></path><path d="M544 429c2 0 3 0 4 1v3l-1-1h-2c-1-1-1-2-1-3z" class="J"></path><path d="M544 429c0-2-1-7 0-9h2l1 1c1 0 0 0 1 1h0-1v1c1 1 1 5 1 7-1-1-2-1-4-1z" class="I"></path><path d="M545 442l1-2h2c0 1 1 1 2 1 3 1 8 2 10 4h1c-1 1-1 2-2 2l-14-5z" class="K"></path><path d="M538 387c1 3 1 7 1 10-1 6-1 12 0 18h0c1-1 2-2 3-2s3 1 5 2c2 2 5 3 8 4 1 0 2 1 3 2l-1 1h-1-3 0v1c1 0 1 0 1 1h1l1 1c2 2 2 3 2 5 0 1-1 2-1 4 0 1 0 2 1 3h0l-10-3v-1-3c0-2 0-6-1-7v-1h1 0c-1-1 0-1-1-1l-1-1 1-1-1-1c-1-1-2-2-4-1l-1-1c-3 0-10 9-12 11s-4 5-5 6l-1-1v-2c2-1 2-2 2-3v-10h0c1 1 2 1 2 1 2 0 3 3 4 4h-1l1 1 5-5 2-2s-1-4-1-5c0-6 0-13 1-19v-1-4z" class="g"></path><path d="M525 427l1-4c2-1 2 0 4 1-2 2-4 4-5 7l-2 1v-2c2-1 2-2 2-3z" class="c"></path><path d="M554 424h1l1 1c0 3 1 9-1 11-2-2-1-9-1-12z" class="E"></path><path d="M525 417h0c1 1 2 1 2 1 2 0 3 3 4 4h-1l1 1-1 1c-2-1-2-2-4-1l-1 4v-10z" class="Q"></path><path d="M545 442l14 5c1 0 1-1 2-2v-1 1h3l9 3c-1 1-1 1-1 2h2l-1 1h0c4 2 9 4 14 5h2v4 5c-1 2-2 2-4 3-4 3-8 6-13 9v-1l3-2h-2l-1 1-1-3-1-2c-1-2-1-3-1-6-2 0-4-1-7-2-1 0-3-1-4-1h0c-2 0-3-1-4-1-3-1-7-1-9-2l-2-1c0-2 0-3 1-4v-1h1c-1-3 0-7 0-10z" class="e"></path><path d="M577 461c0-1 0-3 1-5 1 1 1 1 1 2v4l-2-1z" class="H"></path><path d="M561 445v-1 1h3l9 3c-1 1-1 1-1 2h2l-1 1h0l-9-2-5-2c1 0 1-1 2-2z" class="F"></path><path d="M561 445v-1 1h3c0 1 1 2 1 3l-1 1-5-2c1 0 1-1 2-2z" class="P"></path><path d="M570 461c-2-1-4-1-5-2h-2c-1 0 0 0-1-1 0 0 1-1 1-2 1-1 0-2 2-3 2 1 4 1 6 3v5h-1z" class="Z"></path><path d="M544 453c0 1 1 2 2 2 3 1 8 4 11 4 1-1 1 0 2 0s2 0 3 1c1 0 2 0 3 1l5 1 4 2-1 1c-2 0-2 0-4-1-2 0-4-1-7-2-1 0-3-1-4-1h0c-2 0-3-1-4-1-3-1-7-1-9-2l-2-1c0-2 0-3 1-4z" class="U"></path><path d="M572 459c0-2 0-2 1-3h1l1 1c0 2 1 3 1 5l1-1 2 1c1 1 2 1 4 2v-2c0-1-1-2-1-4 1 0 2-1 2-1l1-1v1 2l2-1c0 1 0 1 1 2h1v5c-1 2-2 2-4 3-4 3-8 6-13 9v-1l3-2h-2l-1 1-1-3-1-2c-1-2-1-3-1-6 2 1 2 1 4 1l1-1-4-2v-1h1 1v-2z" class="d"></path><path d="M572 459c0-2 0-2 1-3h1l1 1c-1 2-1 3-1 5l-1-1-1-2z" class="O"></path><path d="M573 465l1-1c3 0 5 0 7 1v1l-1-1c-1 0-2 1-3 1s-2 1-2 1l-1 1c0-1 0-2-1-3z" class="a"></path><path d="M569 464c2 1 2 1 4 1 1 1 1 2 1 3l1-1 1 2c2 0 2 0 4-1v-1h1l1 2c-2 2-5 3-7 5h-2l-1 1-1-3-1-2c-1-2-1-3-1-6z" class="m"></path><path d="M569 464c2 1 2 1 4 1 1 1 1 2 1 3l-2 2v-1-1l-2 2c-1-2-1-3-1-6z" class="M"></path><path d="M523 432l1 1c1-1 3-4 5-6s9-11 12-11l1 1v2 1 1c1 1 1 4 1 6v2 21c0 1 0 2 1 2v1c-1 1-1 2-1 4l-4 3 2 2c0 1-2 2-2 3-2 1-4 3-6 4h0-3c-8-5-11-15-14-24h-1c0-1 0-1 1-2 0-3 0-9-2-11v-1l-2-1c0-1 1-1 2-1l1 1h7 1v2z" class="b"></path><path d="M525 458v-2c1-2 4-4 5-6h1c1-2 3-4 4-5 0 2-2 3-3 5l1 1 1-1v1c0 2-1 3-2 4-1 0-1 1-2 1h-2v-1l-3 3z" class="d"></path><path d="M516 445v-1c1 0 1 0 2 1l2-3v4c-1 0-1 1-1 2 0 3 3 8 4 10h1 1l3-3v1l-4 5c1 1 3 3 4 5 2-1 3-3 5-4 1-1 5-6 7-6-3 3-6 7-9 10 0 1 0 0-1 1h1l2 2h0-3c-8-5-11-15-14-24z" class="F"></path><path d="M542 430l1-1v21c0 1 0 2 1 2v1c-1 1-1 2-1 4l-4 3 2 2c0 1-2 2-2 3-2 1-4 3-6 4l-2-2h-1c1-1 1 0 1-1 3-3 6-7 9-10l2-2-1-1c0-1-1-1-1-2v-2c2 0 1 0 2-1-1-2-3-1-5-3v-2-1c1 0 2-1 2-1 1-1 1-2 1-3v-1c-1-2 0-4 1-5l1-1v-1z" class="E"></path><path d="M531 467l8-7 2 2c0 1-2 2-2 3-2 1-4 3-6 4l-2-2z" class="M"></path><path d="M520 446c0-1 2-2 2-2 1 0 1 1 3 0v-3l2-1v1l2-2-1-1c1-1 1-2 2-2v1l1 1h1v2h2c0 1 1 1 1 3l1 1-1 1c-1 1-3 3-4 5h-1c-1 2-4 4-5 6v2h-1-1c-1-2-4-7-4-10 0-1 0-2 1-2z" class="D"></path><path d="M523 432l1 1c1-1 3-4 5-6s9-11 12-11l1 1v2 1 1c1 1 1 4 1 6v2l-1 1v1l-1 1c-1 1-2 3-1 5v1c0 1 0 2-1 3 0 0-1 1-2 1l-1 2-1-1c0-2-1-2-1-3h-2v-2h-1l-1-1v-1c-1 0-1 1-2 2l1 1-2 2v-1l-2 1v3c-2 1-2 0-3 0 0 0-2 1-2 2v-4l-2 3c-1-1-1-1-2-1v1h-1c0-1 0-1 1-2 0-3 0-9-2-11v-1l-2-1c0-1 1-1 2-1l1 1h7 1v2z" class="C"></path><path d="M542 430c-1-1-1-1-2-1h0v-3c1 0 2 0 3 1v2l-1 1z" class="N"></path><path d="M514 432v-1l-2-1c0-1 1-1 2-1l1 1h7c0 2 0 3-1 5l-1 1h0c-1 3-1 4-4 6v1c0-3 0-9-2-11z" class="G"></path><path d="M520 442a30.44 30.44 0 0 0 8-8h1c3 0 3-3 5-3 1 3-1 3-2 5v2h-1l-1-1v-1c-1 0-1 1-2 2l1 1-2 2v-1l-2 1v3c-2 1-2 0-3 0 0 0-2 1-2 2v-4z" class="k"></path><path d="M616 787c0-1 0-1-1-2h3v9 4c0 2 0 6 1 8v1l-1 1-1 3h-2v1h-1l-1-1-1-1-2 1s-1 2-2 2h-2c0 2 0 4-1 6h0 3 4v1 1h-1c0 1 0 1 1 2l-1 1c-1 1-1 2-2 3 0 1 0 0-1 1l-1 1 1 1c-2 1-4 1-6 1-1 1-1 1-2 0l-1 1h-1l1 1c0 2-1 3-1 4 2 0 4 0 5-1h1c1 0 3 1 3 2v2c1 0 1 0 2 1 0 1-2 2-3 3h-1v1c-1 1-1 2-3 3l-1 1-1 1h-1c-2 1-5 6-5 9v3 3 4 5c-2-2-4-3-6-4-3 2-6 1-9 1h-1c-3 0-5 0-8 1h-2v1l-1 2c-4-1-6-2-10-1l-5 4h-1c-4 0-9 0-13 1h-3v-1-2l-2-5-2-2-3-3c-2-1-4-2-5-2-5-2-12-1-16 0-1 1-3 2-4 2-2 0-5 0-7 1-1-1-3-1-5-1 1-1 2-1 2-3h-6c-1-1-9 0-12 0h-23-7l-1-1-1 1c-2 0-2 0-4-1h-2c1 1 2 2 2 4-1 1-2 3-4 4 0 0-1 0-2 1h2 0l-2 2s-1 0-1 1l-3-2c-2-1-6-2-9-3v-1l-1-1h-1c-4 0-6-1-9 0l1 1-1 1h-4c-1 0-1 1-2 1h-2c-1-1 0-2 0-3 1-1 1-3 2-4l2-2 3-2c-1 0-1 0-2-1l1-3 1-1v-1c1-2 2-3 3-5v-1-1h2l2-1 1-3c0-3 1-5 2-7 2-4 5-7 6-11 1-2 2-2 2-4h0c-1-1-1-2-1-2v-1h-1v-1l9-1h2v-4-17h0c0-1-1-2 0-3l3 1h0l2-1h5 1 24 3 35 39c3 0 9 0 12 1 2 0 5 0 7-1h4 4 6 4 7 6 0v4l-2 2c1 1 1 2 2 3 0-1 0-1 1-2l2-2h1c1 0 2 0 3 1h-1v1h1v-1l2-1 1 1c-1-1-1-1-1-2h1l1-1 1-1 2 1c0-1 1-2 1-3-1-1-1-2-3-4 1 0 2 0 3-1l2 2c1 0 1-1 2-1z" class="U"></path><path d="M556 826l1 1h6v1 5c-1 0-2 0-3 1h-2c0-2 1-4 0-6 0-1-1-1-2-1v-1z" class="j"></path><path d="M522 822v2h3 4v1h-7v13h-3c1-2 1-5 0-7l-1-1v-4l2-1v-2h1v1l1-2z" class="D"></path><path d="M579 843c3 1 5 1 8 1h4 1l2-1 1 1h9l1 1c-1 1-1 2-3 3l-1 1c-1-1-1-1-2-1l-1-1h-2-1v1h-4c-2-1-2 0-3 0-2-1-4 0-5 0l-1-1c-1 1-2 1-3 1h-1c-1-1-2 0-3 0h-2l-2-1h-5v-1h0l-2-3 6 1h0l2-1c1 0 1 0 2 1 1-1 4-1 5-1z" class="Q"></path><path d="M564 843l6 1c1 0 2 1 3 3h-1-1-5v-1h0l-2-3z" class="X"></path><path d="M494 839h55-2l-1 1c1 1 3 1 5 1-3 1-5 0-7 1-1 0-2-1-2-1l-2 2h-2-8v1c-1-1-2-1-2-1-1 1-2 0-3 0h-6v1c-3-1-7-1-9-1l-1 1-1-1-1 1-1-1c-2 0-3 1-4 0-2 1-4 1-5 1-1-2-1-2-2-3v-1l-1-1z" class="E"></path><path d="M549 839h56v2h0-26v2c-1 0-4 0-5 1-1-1-1-1-2-1l-2 1h0l-6-1h-1c-2 0-4 1-5 1v-1h-18l2-2s1 1 2 1c2-1 4 0 7-1-2 0-4 0-5-1l1-1h2z" class="N"></path><path d="M551 841c1 1 1 1 2 1h6l20-1v2c-1 0-4 0-5 1-1-1-1-1-2-1l-2 1h0l-6-1h-1c-2 0-4 1-5 1v-1h-18l2-2s1 1 2 1c2-1 4 0 7-1z" class="V"></path><path d="M507 804l1 1h-1c-2 3-2 5-2 8v1h6v2c0 3-1 5 0 8v2h0 5v5h3c1 2 1 5 0 7h-1c-4-1-11 1-15-1 0-4 1-10 0-14v-2c1-2 0-5 0-7v-2-1c1-4 2-5 4-7z" class="D"></path><path d="M519 831c1 2 1 5 0 7h-1c-1-3-2-4-2-7h3z" class="Q"></path><path d="M511 824c-1 0-4 0-5-1v-6-1l1 1h3l1-1c0 3-1 5 0 8zm0 2c-1 3 0 8-1 10l-2-2-2 1h0l-1-8v-1h1 4 1 0z" class="j"></path><path d="M595 848v-1h1 2l1 1c1 0 1 0 2 1l-1 1h-1c-2 1-5 6-5 9v3h-1c-2 1-4 1-6 0v-1h-4-2-2-1c-3 1-4 1-6-1l-1 1h-7 1c-1-1-1-1-1-2 1-4 1-6 0-10 0-1 0-1 2-2h5l2 1h2c1 0 2-1 3 0h1c1 0 2 0 3-1l1 1c1 0 3-1 5 0 1 0 1-1 3 0h4z" class="l"></path><path d="M587 861l7-2v3h-1c-2 1-4 1-6 0v-1zm-9-13h1c1 0 2 0 3-1l1 1c1 0 3-1 5 0 1 0 1-1 3 0h4 0l-1 3c0 1 0 1-1 2 0 3-1 4-3 6v-1l-1-1c-1 0-1 0-2 1l-1-1c2-3 1-3 1-5v-2-1l-3 2v1c1 2 0 5 0 8h-3l1-5-3-3h2l-1-1c-1 0-1-1-2-1v-2z" class="P"></path><path d="M564 849c0-1 0-1 2-2h5l2 1h2c1 0 2-1 3 0v2c1 0 1 1 2 1l1 1h-2c0 1-1 2-1 4 0 1-1 2-1 3-1 1-1 1-3 1l-1-1-1 1-1 1h-7 1c-1-1-1-1-1-2 1-4 1-6 0-10z" class="O"></path><path d="M575 848c1 0 2-1 3 0v2c1 0 1 1 2 1l1 1h-2c0 1-1 2-1 4-1-1-1-2-1-3-1-2-2-3-2-5z" class="F"></path><path d="M570 854h4 1c0 2 0 3-2 5l-1 1-1 1h-7 1l2-1v-1c0-2 1-4 3-5z" class="Z"></path><path d="M570 857h1l1 1-1 1h-2 0l1-2z" class="d"></path><path d="M564 849c0-1 0-1 2-2h5l2 1c-1 1-1 1-2 3h0c1 1 1 1 1 2-1 0-1 1-2 1-2 1-3 3-3 5v1l-2 1c-1-1-1-1-1-2 1-4 1-6 0-10z" class="F"></path><path d="M508 805c1-1 3-3 5-4 3-1 6 0 9 1v-1c5 3 7 6 9 12v5 4 5c0 2 1 10-1 11h-1c-1-4 1-9 0-13v-1h-4-3v-2l-1 2v-1h-1v2l-2 1v4l1 1h-3v-5h-5 0v-2c-1-3 0-5 0-8v-2h-6v-1c0-3 0-5 2-8h1z" class="I"></path><path d="M518 821v-1-3h0 2c1 1 1 4 1 5v1h-1-2l1-1h0l-1-1z" class="Z"></path><path d="M518 821l1 1h0l-1 1h2v2l-2 1v4l1 1h-3v-5c1-1 1-2 1-3l-1-1h-2v-1h4z" class="C"></path><path d="M522 822v-6h2 4c1 2 1 6 1 8h-4-3v-2z" class="F"></path><path d="M522 822v-6h2c1 1 1 3 2 4v1c-1 1-1 2-1 3h-3v-2z" class="M"></path><path d="M508 805c1-1 3-3 5-4 3-1 6 0 9 1 3 2 5 5 6 9l1 1-4-1v-3c-1-1-2-3-3-4h-4v-1c-2 1-3 1-4 3-2-2-4-1-7-1h1z" class="K"></path><path d="M529 812v2l-1 1c-2 0-4 0-6-1h-4 0c-1-3-2-7 0-10h4c1 1 2 3 3 4v3l4 1zm-22-7c3 0 5-1 7 1h2 0c-1 3 0 5-1 8h-4 0-6v-1c0-3 0-5 2-8z" class="j"></path><path d="M548 802c1 0 3-1 5 0h0c1 0 1-1 2-1h0v2l1 1h3 1c1 3 4 4 4 7h0v2c-1 0-1 2 0 3v1h1l-1 1c-1 2-1 5-1 7v1h-2l-4 1-1-1v1 7c-2 0-3-2-3-2-2 1-2 1-3 1 0-1 0-3-1-5h0c-1 1-1 1-1 2h-1c-1 3 0 5 0 8h-15c0-2-1-9 0-11 1-1 1 0 0-1v-1c0-1 1-2 0-3h0c1-3 0-8 0-11-2-3-3-5-5-7h1v-1-1h6 14z" class="T"></path><path d="M546 816l-2-1h-2c-1 0-1 0-2-1h1l-1-2c3 1 4 1 6 3v1z" class="L"></path><path d="M534 810c1 0 2 1 3 1v3h-2c-1 0-1-2-2-2l1-2z" class="f"></path><path d="M547 809c2 2 3 2 3 5v3c-2 0-3-1-4-1v-1c0-2 1-4 1-6z" class="K"></path><path d="M554 809h2v1h0v6c-1 0-3 0-4 1v-1c1-2 1-4 1-6l1-1z" class="S"></path><path d="M564 817h-6l-1-1v-7h2c1 0 1 1 2 1s2 0 3 1h0v2c-1 0-1 2 0 3v1z" class="B"></path><path d="M527 804h1v-1-1h6 14c0 1 1 1 1 2l-1 1v-1l-3 2-2-1c-1 1-1 1-3 1l1 1h1-4l-5-1-1 1v1 3c-2-3-3-5-5-7z" class="X"></path><path d="M533 806c2-1 2-1 4-1 1 1 1 1 1 2l-5-1z" class="G"></path><path d="M548 802c1 0 3-1 5 0h0c1 0 1-1 2-1h0v2l1 1h3 1c1 3 4 4 4 7-1-1-2-1-3-1s-1-1-2-1v-1h-5l-1 1h1l-1 1c0 2 0 4-1 6v1h-2v-3c0-3-1-3-3-5l1-1h0l-1-2 1-1 1-1c0-1-1-1-1-2z" class="P"></path><path d="M556 804h3c-1 1-3 1-4 1s-3 3-5 4l-2-1h0c3-3 5-3 8-4z" class="K"></path><path d="M548 802c1 0 3-1 5 0h0c1 0 1-1 2-1h0v2l1 1c-3 1-5 1-8 4l-1-2 1-1 1-1c0-1-1-1-1-2z" class="Q"></path><path d="M554 818c0-1 2-1 3-1 2 1 4 1 7 1-1 2-1 5-1 7v1h-2l-4 1-1-1v1 7c-2 0-3-2-3-2-2 1-2 1-3 1 0-1 0-3-1-5h0c-1 1-1 1-1 2h-1l-1-3c0-2-1-8 1-10h3l4 1z" class="B"></path><path d="M556 826v-1-1-4c0-1 0-2 1-3 2 1 4 1 7 1-1 2-1 5-1 7v1h-2l-4 1-1-1z" class="K"></path><path d="M561 826v-1c-1-1-1-1-1-2h-1v-2-1l2-1c0 1 0 1 1 2 1 2 1 3 1 4v1h-2z" class="B"></path><path d="M550 817l4 1-1 1v1c-1 2 0 4 0 5l2 1 1 1h-3 0-1-1 0-5c0-2-1-8 1-10h3z" class="F"></path><path d="M550 817l4 1-1 1v1c-1 2 0 4 0 5-1 1-2 1-2 1h-1v-8-1z" class="S"></path><path d="M540 843h18v1c1 0 3-1 5-1h1l2 3h0v1c-2 1-2 1-2 2 1 4 1 6 0 10 0 1 0 1 1 2h-1 7l1-1c2 2 3 2 6 1h1 2 2 4v1c2 1 4 1 6 0h1v3 4 5c-2-2-4-3-6-4-3 2-6 1-9 1h-1c-3 0-5 0-8 1h-2v1l-1 2c-4-1-6-2-10-1l-5 4h-1c-4 0-9 0-13 1h-3v-1-2l-2-5-2-2-3-3c-2-1-4-2-5-2-5-2-12-1-16 0v-1l1-1h0c-3 0-6 0-9-1v-1-1l-3-1-1-2c0-1 0-1 1-3h0c2 1 3 1 4 0v-5l2-1h1l7 1c1 0 1 0 1 1h1v-1c2-1 7-1 9 0h1c1-1 2-1 4-1-1-1-1-2-1-4 1 0 2 1 3 0 0 0 1 0 2 1v-1h8 2z" class="c"></path><path d="M558 844c1 0 3-1 5-1h1l2 3h0v1c-2 1-2 1-2 2h-1v-1h-1l1-1c0-2 0-2-1-3h-2c-1 1 0 1-1 1l-1-1z" class="Q"></path><path d="M540 847c2 1 3 1 5 1l2-1c1 1 3 0 5 1h-1c-2 0-2 0-3 1s-2 1-2 3v1h0-1l-2 1h-1c-1-1-2-1-4-1 1-2 2-4 2-6z" class="O"></path><path d="M539 847h1c0 2-1 4-2 6-1 1-2 1-3 2v5h-2v-13h1c2 0 3 1 5 0z" class="P"></path><path d="M534 847c2 0 3 1 5 0-1 2-2 2-2 4h1v1c-1 0-2 1-3 1-1-2-1-4-1-6z" class="O"></path><path d="M538 853c2 0 3 0 4 1h1c0 2 1 4 0 6h1l-1 1v-1c-3 1-5 1-8 0v-5c1-1 2-1 3-2z" class="i"></path><path d="M535 860v-5c1 1 1 2 0 4 3 1 5 0 8 1-3 1-5 1-8 0z" class="n"></path><path d="M531 847h2v13c-1 1-1 1-2 0l-1-1c-2 0-2 1-4 1l-1-2v2h-1l-1-1c0-1 0-3-1-4v-7c1-1 2-1 4-1h5z" class="F"></path><g class="d"><path d="M531 847h2v13c-1 1-1 1-2 0l-1-1c-2 0-2 1-4 1l-1-2c0-1 0-3 1-4h3c1 0 2 0 3-1v-1h-2v-2l1-1v-2z"></path><path d="M523 859l1 1h1v-2l1 2c2 0 2-1 4-1l1 1c1 1 1 1 2 0h2c3 1 5 1 8 0v1 1l-2 1h0v2h0c-1 1-2 2-4 3h0c-1 0-2 0-3-1h-1v1l1 1h-3l-3-3c-2-1-4-2-5-2l-1-2h-1-7v-1l1-1h3 1 1l1-1 1 2h0l1-2z"></path></g><path d="M528 866c0-1 1-1 2-2l1 1h10c-1 1-2 2-4 3h0c-1 0-2 0-3-1h-1v1l1 1h-3l-3-3z" class="D"></path><path d="M523 859l1 1h1v-2l1 2c2 0 2-1 4-1l1 1c1 1 1 1 2 0h2c3 1 5 1 8 0v1 1l-2 1c-6 0-13 0-19-1h-1-7v-1l1-1h3 1 1l1-1 1 2h0l1-2z" class="K"></path><path d="M552 848h1l1 1c1 0 1-1 2-2l6 1h1v1h1c1 4 1 6 0 10 0 1 0 1 1 2h-1-2c0-1-1-1-2-1v1c-2 0-3 1-5 0h-5-5l-1-1h-1c1-2 0-4 0-6l2-1h1 0v-1c0-2 1-2 2-3s1-1 3-1h1 0z" class="H"></path><path d="M563 849h1c1 4 1 6 0 10v-2h0c-1-1-2-2-3-2-1-1-1 0-2-1h2c2 0 2 0 3-1v-2c-1 0-1-1-2-1l1-1z" class="d"></path><path d="M552 848h0l-1 2 2 2v1c-1 1-1 1-3 1-1 0-1-1-2-1v-1-3c1-1 1-1 3-1h1z" class="P"></path><path d="M548 849c1-1 1-1 3-1 0 1-1 2-1 4h-1-1v-3z" class="K"></path><path d="M553 853c2 2-1 6 2 7h1v-1l4 1v1c-2 0-3 1-5 0h-5v-7c2 0 2 0 3-1z" class="m"></path><path d="M556 859v-1c1-2 1-3 3-4 1 1 1 0 2 1 1 0 2 1 3 2h0v2c0 1 0 1 1 2h-1-2c0-1-1-1-2-1l-4-1z" class="i"></path><path d="M548 849v3 1c1 0 1 1 2 1v7h-5l-1-1h-1c1-2 0-4 0-6l2-1h1 0v-1c0-2 1-2 2-3z" class="n"></path><path d="M543 854l2-1h1l-1 8-1-1h-1c1-2 0-4 0-6z" class="P"></path><path d="M502 847h1l7 1c1 0 1 0 1 1h1v-1c2-1 7-1 9 0h1v7c1 1 1 3 1 4l-1 2h0l-1-2-1 1h-1-1-3l-1 1v1h7 1l1 2c-5-2-12-1-16 0v-1l1-1h0c-3 0-6 0-9-1v-1-1l-3-1-1-2c0-1 0-1 1-3h0c2 1 3 1 4 0v-5l2-1z" class="H"></path><path d="M500 848l2-1c0 3-1 6 0 9v3l-2-1h0c-1 0-3-1-3-1v-1l-1-3h0c2 1 3 1 4 0v-5z" class="R"></path><path d="M503 847l7 1c1 0 1 0 1 1v5c-1 0-1 0-2-1-1 0-2 0-3-1v-1c-1-1-2-2-3-4z" class="M"></path><path d="M511 849h1c2 4-1 9 1 12v1h1 7 1l1 2c-5-2-12-1-16 0v-1l1-1h0c-3 0-6 0-9-1v-1-1l-3-1-1-2c0-1 0-1 1-3l1 3v1s2 1 3 1h0l2 1v1c1 1 2 0 3 0v-1-5c1 0 3 0 4-1 1 1 1 1 2 1v-5z" class="U"></path><path d="M509 853c1 1 1 1 2 1h1v5h-1v-1c-2 2-4 1-6 1v-5c1 0 3 0 4-1z" class="W"></path><path d="M512 849v-1c2-1 7-1 9 0h1v7c1 1 1 3 1 4l-1 2h0l-1-2-1 1h-1-1-3l-1 1v1h-1v-1c-2-3 1-8-1-12z" class="P"></path><path d="M515 860v-6h4c1 0 2 0 3 1s1 3 1 4l-1 2h0l-1-2-1 1h-1-1-3z" class="Z"></path><path d="M560 860c1 0 2 0 2 1h2 7l1-1c2 2 3 2 6 1h1 2 2 4v1c2 1 4 1 6 0h1v3 4 5c-2-2-4-3-6-4-3 2-6 1-9 1h-1c-3 0-5 0-8 1h-2v1l-1 2c-4-1-6-2-10-1l-5 4h-1c-4 0-9 0-13 1h-3v-1-2l-2-5-2-2h3l-1-1v-1h1c1 1 2 1 3 1h0c2-1 3-2 4-3h0v-2h0l2-1v-1l1-1 1 1h5 5c2 1 3 0 5 0v-1z" class="R"></path><path d="M560 860c1 0 2 0 2 1h2 7l1-1c2 2 3 2 6 1h1 2 2 4v1c2 1 4 1 6 0v1h-36l3-2v-1z" class="a"></path><path d="M594 862v3 4l-1-1h-2-1c-1 0-2-1-3-1-2 0-2 0-2 1h-9l-1 1c-1 0-2 0-2-1l-12 2-1 1c0-1-1-2-1-3h-3 1l-2-3h1 7 1c1 0 2 1 3 0 2 0 2 0 4 1 1 0 3-1 4-1h9c0 1 0 1 0 0 2 0 4 1 5 0 1 0 2 0 4 1v-3-1h1z" class="D"></path><path d="M557 868c1-1 2-1 3-2v1h2l1 1h0c4 0 7 0 10-1h0l1 1 1-1 1 1-1 1c-1 0-2 0-2-1l-12 2-1 1c0-1-1-2-1-3h-3 1z" class="I"></path><path d="M576 868h9c0-1 0-1 2-1 1 0 2 1 3 1h1 2l1 1v5c-2-2-4-3-6-4-3 2-6 1-9 1h-1c-3 0-5 0-8 1h-2v1l-1 2c-4-1-6-2-10-1-1-1-2-1-2-2v-2l5 1 1-1 12-2c0 1 1 1 2 1l1-1z" class="C"></path><path d="M561 870c2 0 3 1 5 0v1c0 1 0 0 1 1 1-2 4-2 6-2 2 1 3 0 5 1-3 0-5 0-8 1h-2v1l-1 2c-4-1-6-2-10-1-1-1-2-1-2-2v-2l5 1 1-1z" class="J"></path><path d="M544 860l1 1h5 5c2 1 3 0 5 0l-3 2h-5v1h0c1 0 3 0 4 1h-1l2 3h-1 3c0 1 1 2 1 3l-5-1h-4-1l-1-1h-1c-2 1-4 1-7 1h-7l-1 1-2-2h3l-1-1v-1h1c1 1 2 1 3 1h0c2-1 3-2 4-3h0v-2h0l2-1v-1l1-1z" class="C"></path><path d="M541 865h7v2c-3 1-2 0-4 0-1 0-2 1-2 1l-1-1-1 1h-3c2-1 3-2 4-3h0z" class="e"></path><path d="M544 860l1 1h5 5c2 1 3 0 5 0l-3 2h-5v1h0c1 0 3 0 4 1h-1l2 3h-1-5-1c-1 0-2-1-2-1v-2h-7v-2h0l2-1v-1l1-1z" class="W"></path><path d="M548 865h6l-3 3h-1c-1 0-2-1-2-1v-2z" class="D"></path><path d="M554 865h1l2 3h-1-5l3-3z" class="e"></path><path d="M544 860l1 1h5 5c2 1 3 0 5 0l-3 2h-5-11 0l2-1v-1l1-1z" class="Y"></path><path d="M533 871l1-1h7c3 0 5 0 7-1h1l1 1h1 4v2c0 1 1 1 2 2l-5 4h-1c-4 0-9 0-13 1h-3v-1-2l-2-5z" class="G"></path><path d="M555 872c0 1 1 1 2 2l-5 4-1-1c0-1-1-1-1-1-1-1-1-2-1-3 2 0 3 0 6-1zm-20 4c0-1 1-3 2-3s2 0 3 1c1 0 1 1 2 1h0v-2h1 2l1-1c1 1 1 3 2 5-4 1-9-1-13 1v-2z" class="L"></path><path d="M424 816l9-1h2v13 9l1 1 1 1h25 32l1 1v1c1 1 1 1 2 3 1 0 3 0 5-1 1 1 2 0 4 0l1 1 1-1 1 1 1-1c2 0 6 0 9 1v-1h6c0 2 0 3 1 4-2 0-3 0-4 1h-1c-2-1-7-1-9 0v1h-1c0-1 0-1-1-1l-7-1h-1l-2 1v5c-1 1-2 1-4 0h0c-1 2-1 2-1 3l1 2 3 1v1 1c3 1 6 1 9 1h0l-1 1v1c-1 1-3 2-4 2-2 0-5 0-7 1-1-1-3-1-5-1 1-1 2-1 2-3h-6c-1-1-9 0-12 0h-23-7l-1-1-1 1c-2 0-2 0-4-1h-2c1 1 2 2 2 4-1 1-2 3-4 4 0 0-1 0-2 1h2 0l-2 2s-1 0-1 1l-3-2c-2-1-6-2-9-3v-1l-1-1h-1c-4 0-6-1-9 0l1 1-1 1h-4c-1 0-1 1-2 1h-2c-1-1 0-2 0-3 1-1 1-3 2-4l2-2 3-2c-1 0-1 0-2-1l1-3 1-1v-1c1-2 2-3 3-5v-1-1h2l2-1 1-3c0-3 1-5 2-7 2-4 5-7 6-11 1-2 2-2 2-4h0c-1-1-1-2-1-2v-1h-1v-1z" class="U"></path><path d="M441 843l8 1v-2l2 2c0 1 0 1-1 2l-1 1h-8v-4z" class="N"></path><path d="M420 848c2-1 4-3 6-5l1 1c2 2 3 4 4 6-2 0-2 0-3-1l-2-1c-1 2-3 3-4 4-2-2-2-2-2-4z" class="C"></path><path d="M427 844c2 2 3 4 4 6-2 0-2 0-3-1l-2-1h-1c-2 0-1 0-2-1l2-1c0 1 0 1 1 1l1-1v-2z" class="N"></path><path d="M432 848h-1c0-1-2-2-2-3s1-2 1-3c1 1 1 1 2 1 1 1 1 1 2 1l1-1h1 2c1 1 2 0 3 0v4h8 2l-15 1h-3-1z" class="I"></path><path d="M437 839h25l-3 1v1c-1 1-4 0-6 0h-4 0-2c-2 0-3 0-5 1 0-1 0-1-1-1h-1-3c0 1-2 0-3 0h-6l-1-1c3-1 7-1 10-1z" class="E"></path><path d="M451 844c2-1 4-1 7 0l1-1c1 0 1 0 2 1l1-1c0 1 1 1 2 1v4c-2 0-4-1-6 0 0 1 1 2 1 4l1 4v3 1l-1-1v-2c-1-2-1-4-1-6 0-1-1-2-1-3-2-1-4-1-6-1h-2l1-1c1-1 1-1 1-2z" class="J"></path><path d="M462 839h32l1 1v1c-1 1-2 0-3 1-3-1-8 0-11 0l-22-1v-1l3-1z" class="N"></path><path d="M464 844c2-1 3 0 6 0h1c1-1 2-1 3 0l1-1 1 1c2-1 5 0 7-1 2 0 2 1 3 1 0 0 1-1 2-1s3 1 4 0v-1c2 1 2 2 5 2 1 0 3 0 5-1 1 1 2 0 4 0l1 1 1-1 1 1 1-1c2 0 6 0 9 1v-1h6c0 2 0 3 1 4-2 0-3 0-4 1h-1c-2-1-7-1-9 0v1h-1c0-1 0-1-1-1l-7-1h-1l-2 1-10-1c-1 0-2 0-3 1h-7-1-5-10v-4z" class="c"></path><path d="M519 844v-1h6c0 2 0 3 1 4-2 0-3 0-4 1h-1l1-4h-3z" class="J"></path><path d="M451 847c2 0 4 0 6 1 0 1 1 2 1 3 0 2 0 4 1 6v2l1 1v-1c2 1 3 1 4 1h3l2-1c0 1-1 1-1 2h1l3 1h3v1h-23-7l-1-1-1 1c-2 0-2 0-4-1l1-1h0 2c-1-3-4-8-7-10l-1 1 1 2h-1c-2-2-2-2-3-4 0-1 0-1 1-2h1 3l15-1z" class="F"></path><path d="M452 854c0-2-1-3 1-5 0 0 1 1 2 1v1 1l-1 2h-2zm-7 5c-1 0-1 0-2 1h0l-1-4-5-5c2-3 3-1 5-2h1v3l1 1-1 1v1l2 2v2z" class="B"></path><path d="M457 848c0 1 1 2 1 3 0 2 0 4 1 6v2l1 1v-1c2 1 3 1 4 1h3l2-1c0 1-1 1-1 2-3 2-9-2-12 1l-2-1c0-2 0-3 1-5l-1-1h-1c-4 1-1 4-3 6h-3-1c-1 0-1 0-2-1l1-1v-2l-2-2v-1h3 0 2c0-1 1-1 1-2l-1-1c0-1 0-1 1-2v1h1v3 1h2 2 1l1 1 1-1-1-2c1-2 1-3 1-4z" class="K"></path><path d="M424 816l9-1h2v13 9l1 1 1 1c-3 0-7 0-10 1 0 1-1 1-1 2h-1l-1 1h0c-1 2-3 3-5 4h-3l-1-2 1-3c0-3 1-5 2-7 2-4 5-7 6-11 1-2 2-2 2-4h0c-1-1-1-2-1-2v-1h-1v-1z" class="i"></path><path d="M432 823c1 2 1 5 0 7h-1v1c-1 0-2 0-3 1l-1-1h0l1-2c0-1 0 0-1-1 1-2 2-3 4-4 1 0 1 0 1-1z" class="m"></path><path d="M424 816l9-1h2v13 9l1 1 1 1c-3 0-7 0-10 1 0 1-1 1-1 2h-1c1-5 5-7 6-11v-1h1c1-2 1-5 0-7-2-1-4-1-6 0l-2 1c1-2 2-2 2-4h0c-1-1-1-2-1-2v-1h-1v-1z" class="S"></path><path d="M487 848c1-1 2-1 3-1l10 1v5c-1 1-2 1-4 0h0c-1 2-1 2-1 3l1 2 3 1v1 1c3 1 6 1 9 1h0l-1 1v1c-1 1-3 2-4 2-2 0-5 0-7 1-1-1-3-1-5-1 1-1 2-1 2-3h-6c-1-1-9 0-12 0v-1h-3l-3-1h-1c0-1 1-1 1-2l-2 1h-3c-1 0-2 0-4-1v-3l-1-4c0-2-1-3-1-4 2-1 4 0 6 0h10 5 1 7z" class="H"></path><path d="M480 862c0-1-1-1-1-2 1-2 1-2 1-3-1-1-1-1-1-2l2-1v5l1 1 1-1h2l1-1c0 1 0 2 1 3l-1 1h-6z" class="n"></path><path d="M493 863h10 4v1c-1 1-3 2-4 2-2 0-5 0-7 1-1-1-3-1-5-1 1-1 2-1 2-3z" class="W"></path><path d="M479 848h1l1 5v1l-2 1c0 1 0 1 1 2 0 1 0 1-1 3 0 1 1 1 1 2h-4c-1-1-1-2-2-3h0v-1c-1-2 0-3 1-5h-2 0l-1 1c-1-1-1-2-2-3v-2c1-1 2-1 4-1h5z" class="U"></path><path d="M474 848h5c0 2 0 4-1 6l-3-1h-2 0l-1 1c-1-1-1-2-2-3v-2c1-1 2-1 4-1z" class="S"></path><path d="M459 852c0-2-1-3-1-4 2-1 4 0 6 0h10c-2 0-3 0-4 1v2c0 1 0 3 1 4v5l-2 1h-1c0-1 1-1 1-2l-2 1h-3c-1 0-2 0-4-1v-3l-1-4z" class="M"></path><path d="M459 852c2 0 3 0 4-1l1 1h0v3l-1-1c-2 1-2 1-3 2l-1-4z" class="H"></path><path d="M464 852c1 1 1 1 2 1l1 1 1 1c0-1 1-2 2-2 0-2-2-3-1-4h1v2c0 1 0 3 1 4v5l-2 1h-1c0-1 1-1 1-2l-2 1h-3c-1 0-2 0-4-1v-3c1-1 1-1 3-2l1 1v-3z" class="R"></path><path d="M460 856c1-1 1-1 3-2l1 1v5c-1 0-2 0-4-1v-3z" class="M"></path><path d="M487 848c1-1 2-1 3-1l10 1v5c-1 1-2 1-4 0h0c-1 2-1 2-1 3v2h-1l1 1v1c-1 0-2 0-3 1h-2-1c1-1 1-1 1-2h-1l-2-1v-1h1 0c-1-1-1-2-1-3-1-1-2 0-3 0s-2 0-3-1l-1-5h7z" class="R"></path><path d="M480 848h7v1h0c0 2 0 3 1 4h0l-1 1c-1-1-2 0-3 0s-2 0-3-1l-1-5z" class="B"></path><path d="M490 847l10 1v5c-1 1-2 1-4 0h0c-1 2-1 2-1 3v2h-1l1 1v1c-1 0-2 0-3 1v-7c-2-2-2-4-2-7z" class="S"></path><path d="M411 848h9c0 2 0 2 2 4 1-1 3-2 4-4l2 1c1 1 1 1 3 1h0c1 2 1 2 3 4h1l-1-2 1-1c3 2 6 7 7 10h-2 0l-1 1h-2c1 1 2 2 2 4-1 1-2 3-4 4 0 0-1 0-2 1h2 0l-2 2s-1 0-1 1l-3-2c-2-1-6-2-9-3v-1l-1-1h-1c-4 0-6-1-9 0l1 1-1 1h-4c-1 0-1 1-2 1h-2c-1-1 0-2 0-3 1-1 1-3 2-4l2-2 3-2c-1 0-1 0-2-1l1-3 1-1v-1c1-2 2-3 3-5z" class="d"></path><path d="M407 855l1-1v1c1 1 1 1 1 2h2c1-1 2-1 4-1v1c-2 1-5 1-7 2-1 0-1 0-2-1l1-3z" class="i"></path><path d="M415 856c2 1 3 1 5 2 1 1 3 1 4 1h4l1-1c0-1 0-1-1-2 1-1 0 0 2-1v1c0 1 1 2 2 2h0v-1l1-1 2 3-1 1h1v1h0c-1 1-3 1-4 2s-3 2-4 4l1-4-1-1h-2v2c-1-2-2-2-3-4-2-1-4-2-7-2v-1-1z" class="H"></path><path d="M411 848h9c0 2 0 2 2 4 1-1 3-2 4-4l2 1v1c-1 0-1 1-1 2v1l-2-1-1 1 1 1v1h-2c-1-1-2 0-3 0-2 0-3 0-5-1v-1l-1 1-1-1-1 1h-2c-1 0-1 0-2-1 1-2 2-3 3-5z" class="D"></path><path d="M431 863c1-1 3-1 4-2 1 0 2 1 2 1 1 1 2 2 2 4-1 1-2 3-4 4 0 0-1 0-2 1h2 0l-2 2s-1 0-1 1l-3-2c-2-1-6-2-9-3v-1h4c0-1 0-3 1-4v-2h2l1 1-1 4c1-2 3-3 4-4z" class="h"></path><path d="M426 868l1 1c1 0 2 0 3-1h1v-1h-1-1v-1h1c1 1 2 1 2 3v1h0c-2 0-4 1-6-1v-1zm5-5c1-1 3-1 4-2 1 0 2 1 2 1 1 1 2 2 2 4-1 1-2 3-4 4v-2c1-1 2-1 3-2-1-1-1-1-2-1-2 0-3 0-4-1l-1-1z" class="g"></path><path d="M425 864v-2h2l1 1-1 4-1 1v1c2 2 4 1 6 1l1 1h2 0l-2 2s-1 0-1 1l-3-2c-2-1-6-2-9-3v-1h4c0-1 0-3 1-4z" class="M"></path><path d="M415 857v1c3 0 5 1 7 2 1 2 2 2 3 4-1 1-1 3-1 4h-4l-1-1h-1c-4 0-6-1-9 0l1 1-1 1h-4c-1 0-1 1-2 1h-2c-1-1 0-2 0-3 1-1 1-3 2-4l2-2 3-2c2-1 5-1 7-2z" class="Q"></path><path d="M415 857v1 1c0 1 1 1 0 3l-2 1c-1-1 0-1 0-2-2 0-5 2-6 3s-1 1-3 1v-1l-1-1 2-2 3-2c2-1 5-1 7-2z" class="T"></path><path d="M422 860c1 2 2 2 3 4-1 1-1 3-1 4h-4l-1-1h-1c-4 0-6-1-9 0l1 1-1 1h-4c-1 0-1 1-2 1h-2c-1-1 0-2 0-3h0 5c1-1 3-2 4-2 2 0 4 0 6-1v-2h2 1l3-2z" class="D"></path><path d="M445 791h1 24 3c1 1 2 1 3 2 0 1 0 3-1 4h1c1 0 1 0 2 1l6-1 2 2h0c2-1 2-1 3 0h1 2c1 1 1 1 2 1 4-1 9 0 13 1h1c-1 1-2 2-3 4 0 0-1 2-2 2-1 5-1 10-1 15v15l-1 1h-53-12l-1-1v-9-13-4-17h0c0-1-1-2 0-3l3 1h0l2-1h5z" class="T"></path><path d="M438 809c1 0 3-2 3-3l1-1 1-1v-2l1-1v1 2c1 1 1 1 2 0h3l2 1 1-1v-1l2-1h4l-1 1 1 1c1-1 2-1 3-2l2 1h4 0c-2 0-3 1-4 2-2 0-3 0-4-1l-3 3s-1 0-1 1h1v1c-3 0-5 0-7-1h-3c-2 0-3 2-5 2h-3v-1z" class="c"></path><path d="M435 828c2-5-2-13 1-18l1 2c1 1 0 1 2 0v2l-2 1v1c0 1 0 2-1 3v1h1 1 2 2l1 1-1 1v3l1 1h1 2c2 0 3 0 5 1h-5c-2 0-5 0-8 1l-1-1h0v4c0 1 0 4 1 5 2 2 6 1 10 2h-12l-1-1v-9z" class="C"></path><path d="M436 820h1 1 2 2l1 1-1 1v3-1l-1 1v1h-1l-1-1h-2c-2-2 0-2-1-5z" class="c"></path><path d="M435 794l1-1h0c0 1 1 1 1 1h1c1 1 1 2 2 3 2 1 3 2 4 3v1l-1 1v2l-1 1-1 1c0 1-2 3-3 3h-1l-1 1c-3 5 1 13-1 18v-13-4-17z" class="e"></path><path d="M437 794h1c1 1 1 2 2 3 0 3 1 4 1 7-1 1-1 1-2 1 0 1 0 1-1 1v-5-3l-1-4z" class="W"></path><path d="M445 791h1 24 3c1 1 2 1 3 2 0 1 0 3-1 4h1c1 0 1 0 2 1l6-1 2 2h0c2-1 2-1 3 0h1 2c1 1 1 1 2 1 4-1 9 0 13 1h1c-1 1-2 2-3 4 0 0-1 2-2 2-3-1-8 0-11-1-4 0-6-1-9 0l1 1-3-2c-2 0-3-1-4-1-3-1-6 0-9 2l-3 3v-3c1-1 2-1 3-2v-2l-1 1h-4l-2-1c-1 1-2 1-3 2l-1-1 1-1h-4l-2 1v1l-1 1-2-1h-3c-1 1-1 1-2 0v-2-1-1c-1-1-2-2-4-3-1-1-1-2-2-3h-1s-1 0-1-1h0l-1 1h0c0-1-1-2 0-3l3 1h0l2-1h5z" class="R"></path><path d="M444 802h2v-3c2 0 2 0 4 1v1 3c1 0 1 0 1-1h1v1l-1 1-2-1h-3c-1 1-1 1-2 0v-2z" class="D"></path><path d="M445 791h1 24 3c1 1 2 1 3 2 0 1 0 3-1 4h-1-12-9c-1 1-3 1-3 1-1 0-3-1-3-2l-9-4 2-1h5z" class="K"></path><path d="M455 793h1c1 0 1 0 2 1v1c-1 1-1 1-3 1v-3z" class="M"></path><path d="M470 791h3c1 1 2 1 3 2 0 1 0 3-1 4h-1v-1h0v-1c-3 0-1 1-3 1-1 0-1-1-1-1-1-1 0-2 0-4z" class="S"></path><path d="M445 791h1 0c2 1 4 1 6 1 1 0 1 0 1 1s0 3-1 4h-1l-2-2c-2 0 0 0-2 1h0l-9-4 2-1h5z" class="B"></path><path d="M458 802h0c2-1 3-2 5-2h3 1c2 0 5-1 7-1s2 1 4 1c2-1 5-1 7 0h3c1-1 3-1 4-1 1 1 1 1 2 1 4-1 9 0 13 1h1c-1 1-2 2-3 4 0 0-1 2-2 2-3-1-8 0-11-1-4 0-6-1-9 0l1 1-3-2c-2 0-3-1-4-1-3-1-6 0-9 2l-3 3v-3c1-1 2-1 3-2v-2l-1 1h-4l-2-1c-1 1-2 1-3 2l-1-1 1-1z" class="Q"></path><path d="M481 805v-3h1c1 0 1 0 2 2-1 1-1 1-1 2l1 1-3-2z" class="c"></path><path d="M507 801h1c-1 1-2 2-3 4 0 0-1 2-2 2-3-1-8 0-11-1h0l-1-1c-2 0-4 0-6-1 1-2 5 0 7-2h3v1c3-1 6 0 8-1v-1h4z" class="G"></path><path d="M468 806c3-2 6-3 9-2 1 0 2 1 4 1l3 2-1-1c3-1 5 0 9 0 3 1 8 0 11 1-1 5-1 10-1 15v15l-1 1h-53c-4-1-8 0-10-2h3v-1h-2 0l-1-1v-2c1-1 1 0 1-1h2 0c1 0 2 1 2 2s0 1 1 3h7 0c3-1 7 1 10-1h0c-1-1-1-2-1-3h2 0c-1-2-2-3-2-5h1 1v-1-11c0-2 1-4 3-5v-1l3-3z" class="Q"></path><path d="M478 831v-5h6c1 2 0 5 0 7-1-1-1-2-2-3 0-1 0-1-1-1-2 0-2 1-3 2z" class="B"></path><path d="M476 826v1c0 3 1 6 0 9l-1-1c-1 0-1 0-2-1 0-2-1-6 1-7l2-1z" class="j"></path><path d="M472 813v-4l1-2c1 1 3 1 3 3v5c-1 1-3 1-5 1v-1c1-1 1-1 1-2z" class="F"></path><path d="M478 831c1-1 1-2 3-2 1 0 1 0 1 1 1 1 1 2 2 3v3h-1 0c-2-1-3-1-4-1-1 1 0 1-1 1v-5z" class="j"></path><path d="M476 817h1 0c2 0 6-1 7 0v8h-3-1c-1 0-1 0-2-1l-1-6-1-1z" class="B"></path><path d="M480 825v-6h2v5l-1 1h-1z" class="j"></path><path d="M468 817c3-1 5 0 8 0v8h-3 0v-4-3h0c-2 1-1 5-1 7h-4v-1-4-3z" class="B"></path><path d="M465 826h7c0 2 1 9 0 10h-2l-2-1v-3c-1 1-1 2-1 4h0-2c-1-2 0-7 0-10z" class="j"></path><path d="M468 806h0c1 1 1 1 2 1v1h1c0 1 0 3 1 5 0 1 0 1-1 2v1c-1 0-2 0-3-1l-1-1v-1c-1 1-1 2-1 2l-1 1 3 1v3c-1 1-1 3-1 5h-1l-1-1c-1-4-1-9 0-14v-1l3-3z" class="H"></path><path d="M470 808h1c0 1 0 3 1 5 0 1 0 1-1 2v1c-1 0-2 0-3-1l-1-1c1-1 1-3 1-4s1-2 2-2z" class="B"></path><path d="M468 806c3-2 6-3 9-2 1 0 2 1 4 1l3 2c4 7 2 15 3 22 0 1-1 3 0 3l-1 4v1l-1-1v-1c0-3-1-6 0-9h0c-1-2 0-8 0-10h-5l-2-1h0c-1-2-1-5 0-8-2 0-3-1-5 0l-1 2v4c-1-2-1-4-1-5h-1v-1c-1 0-1 0-2-1h0z" class="M"></path><path d="M478 807v-1c3 0 3 2 6 4v4c-2 0-3 1-4 2l-2-1h0c-1-2-1-5 0-8z" class="j"></path><path d="M483 806c3-1 5 0 9 0 3 1 8 0 11 1-1 5-1 10-1 15v15c-1-1-2-1-3-1-2 0-3-1-5 0h-7v-4c-1 0 0-2 0-3-1-7 1-15-3-22l-1-1z" class="L"></path><path d="M616 787c0-1 0-1-1-2h3v9 4c0 2 0 6 1 8v1l-1 1-1 3h-2v1h-1l-1-1-1-1-2 1s-1 2-2 2h-2c0 2 0 4-1 6h0 3 4v1 1h-1c0 1 0 1 1 2l-1 1c-1 1-1 2-2 3 0 1 0 0-1 1l-1 1 1 1c-2 1-4 1-6 1-1 1-1 1-2 0l-1 1h-1l1 1c0 2-1 3-1 4-3 1-7 1-10 0h-20l-1-1h-1c1-2-1-2-1-3-1-1 0-3 0-4l-2-1v-1h-6l4-1h2v-1c0-2 0-5 1-7l1-1h-1v-1c-1-1-1-3 0-3v-2h0c0-3-3-4-4-7h-1-3l-1-1v-2h0c-1 0-1 1-2 1h0c-2-1-4 0-5 0h-14-6v1 1h-1c-1-1-3-2-4-3h-1v1c-3-1-6-2-9-1-2 1-4 3-5 4l-1-1 3-3h0-2-1c-4-1-9-2-13-1-1 0-1 0-2-1h-2-1c-1-1-1-1-3 0h0l-2-2-6 1c-1-1-1-1-2-1h-1c1-1 1-3 1-4-1-1-2-1-3-2h35 39c3 0 9 0 12 1 2 0 5 0 7-1h4 4 6 4 7 6 0v4l-2 2c1 1 1 2 2 3 0-1 0-1 1-2l2-2h1c1 0 2 0 3 1h-1v1h1v-1l2-1 1 1c-1-1-1-1-1-2h1l1-1 1-1 2 1c0-1 1-2 1-3-1-1-1-2-3-4 1 0 2 0 3-1l2 2c1 0 1-1 2-1z" class="W"></path><path d="M580 801h3 2l1 1-1 1h-2l-2 1c-1 1-1 2-1 3h-1l-1-1v-1c1 0 1-2 2-3v-1z" class="h"></path><path d="M584 798h3 3l-3 3-1-1c-1 0-1 0-3 1h-3-2v-1c-1-1-2-1-3-2h4c1 1 4 0 5 0z" class="d"></path><path d="M562 800c1-1 0-1 1-1s3 0 4 1h1 1 2c2 0 4 1 5 0l1 1h-1l1 1c0 1-1 2-2 3-2-2-2-2-4-3-2 0-2 0-3 1l-1-1h-2l-1-1-2-1z" class="b"></path><path d="M584 791h7l4 1v1h-1v-1c-1 1-2 1-2 3l-2-2-2 1c1 2 2 1 2 3v1h-3-3c0-1 1-1 1-2l-1-5z" class="S"></path><path d="M585 796l2-1c0-1 0-2 1-3 1 1 1 1 2 1l-2 1c1 2 2 1 2 3v1h-3-3c0-1 1-1 1-2z" class="P"></path><path d="M523 801h0l-2-1c-1-1-1-1-2-1h-1l-1-1h3l1 1c1 0 2 0 3-1 1 0 1 1 2 1s1 1 2 0h5c2 1 3 1 5 1-2 0-2 0-4 2h-6v1 1h-1c-1-1-3-2-4-3z" class="D"></path><path d="M538 800h4c2 0 4 0 6-1 2 0 5 1 7 0l1 2h-1 0c-1 0-1 1-2 1h0c-2-1-4 0-5 0h-14c2-2 2-2 4-2z" class="I"></path><path d="M580 791h4l1 5c0 1-1 1-1 2-1 0-4 1-5 0h-4-4c-2 0-3 0-5-1h-1l-2-1v-1h-2c0-1 0-1-1-2-1 0-1 0-2 1v1h-1-1l1-3h1 1c2 0 5 0 7-1h4 4 6z" class="F"></path><path d="M570 791h4v1h0v2 1c-1 0-1 0-2 1h-1c-1-2-1-2-1-5z" class="S"></path><path d="M580 791h4l1 5c0 1-1 1-1 2-1 0-4 1-5 0l1-1v-1c1-2 1-3 0-5z" class="K"></path><path d="M581 814l2-1v-1c1-1 3-1 5-1l-1 2c-1 0-2 0-2 1h0c3 0 5 0 7 2s1 8 1 11v3c1 0 4 2 5 2h0l1 1c0 2-1 3-1 4-3 1-7 1-10 0l1-3v-1c-1-1-1-1-1-2h1 1l1-1-1-1h-2 0-2-1c-2 0-3 0-4-1 0-1 0-2-1-3h0l1-1c-1-2-1-3-1-4 1-2 0-3 0-4 1-1 1-1 1-2z" class="E"></path><path d="M581 814l2-1v-1c1-1 3-1 5-1l-1 2c-1 0-2 0-2 1h0c-1 1-1 2-1 3v1h1v1c-1 0 0 0-1 1h1c1 3-2 5 3 7h0c-2 0-3 0-5-1-1-4 0-8 0-12h-2z" class="O"></path><path d="M585 814c3 0 5 0 7 2v1c0 2 1 7 0 9l-1 1-3 1v-1h0c-5-2-2-4-3-7h-1c1-1 0-1 1-1v-1h-1v-1c0-1 0-2 1-3z" class="h"></path><path d="M473 791h35 39c3 0 9 0 12 1h-1-1l-1 3h1 1v-1c1-1 1-1 2-1 1 1 1 1 1 2h2v1l2 1h1-1c-2 0-5 1-7 0h-15-5-6v1c-1-1-1-1-2-1h-5-1v-1l-1 1h-2-8-6 0-1v-3h-1v2l-1 1-1-1-1 1h-1l-1-1v1h-2v-1c0-1 0-2-1-3v1 1c0 1-1 1-1 2-2-1-5 0-7 0-1-1-3 0-4-1 0-1 0-1-1-2h0v3l-6 1c-1-1-1-1-2-1h-1c1-1 1-3 1-4-1-1-2-1-3-2z" class="H"></path><path d="M548 793h3v2h-2-1v-2z" class="S"></path><path d="M538 792h2v4c-1 0-1-1-2-1v-3z" class="K"></path><path d="M521 797l-2-1c1-1 1-2 1-3v-1h2c2 1 3 0 4 0h1v1c0 2-1 3-2 4h-1v-1l-1 1h-2z" class="M"></path><path d="M562 800l2 1 1 1h2l1 1c1-1 1-1 3-1 2 1 2 1 4 3s4 3 5 6c-1 2-1 3-2 6v3 1c0 3 0 5 2 8 2 1 4 2 7 2h1 0c0 1 0 1 1 2v1l-1 3h-20l-1-1h-1c1-2-1-2-1-3-1-1 0-3 0-4l-2-1v-1h-6l4-1h2v-1c0-2 0-5 1-7l1-1h-1v-1c-1-1-1-3 0-3v-2h0c0-3-3-4-4-7h-1-3l-1-1v-2h1l2-1c2 1 3 0 4 0z" class="c"></path><path d="M570 811c1-1 2 0 3 0-1 1-1 2-2 3h-1v-1h-1c0-1 1-1 1-2z" class="T"></path><path d="M579 831l-2-3c-1-3-1-7-1-11h1l1 3v1c0 3 0 5 2 8v2h-1z" class="E"></path><path d="M562 800l2 1-1 1c1 2 2 2 4 4v4c-2-2-4-4-7-6h-1-3l-1-1v-2h1l2-1c2 1 3 0 4 0z" class="N"></path><path d="M560 804c3 2 5 4 7 6l1 11c0 1-1 3 0 5h-5v-1c0-2 0-5 1-7l1-1h-1v-1c-1-1-1-3 0-3v-2h0c0-3-3-4-4-7z" class="H"></path><path d="M557 827l4-1h2 5 0v6c1 1 0 3 0 4l1 1c1 0 2 0 4-1h1c-1-1-2-1-3-1h0 4c1-1 0-2 2-3h1l1-1h1v-2c2 1 4 2 7 2h1 0c0 1 0 1 1 2v1l-1 3h-20l-1-1h-1c1-2-1-2-1-3-1-1 0-3 0-4l-2-1v-1h-6z" class="Q"></path><path d="M580 829c2 1 4 2 7 2h1 0c0 1 0 1 1 2v1c-1 0-1 0-2-1l-2 1-1-2c-2 0-2 0-4-1v-2z" class="C"></path><path d="M563 827h4c1 2 1 6 0 9h-1c1-2-1-2-1-3-1-1 0-3 0-4l-2-1v-1zm53-40c0-1 0-1-1-2h3v9 4c0 2 0 6 1 8v1l-1 1-1 3h-2v1h-1l-1-1-1-1-2 1s-1 2-2 2h-2c0 2 0 4-1 6h0 3 4v1 1h-1c0 1 0 1 1 2l-1 1c-1 1-1 2-2 3 0 1 0 0-1 1l-1 1 1 1c-2 1-4 1-6 1-1 1-1 1-2 0l-1 1h-1 0c-1 0-4-2-5-2v-3c0-3 1-9-1-11s-4-2-7-2h0c0-1 1-1 2-1l1-2v-1c1 2 2 4 4 4 0-1-2-2-3-3 0-3-2-7 0-10 1-2 2-3 5-4h1c1 1 1 2 2 3 0-1 0-1 1-2l2-2h1c1 0 2 0 3 1h-1v1h1v-1l2-1 1 1c-1-1-1-1-1-2h1l1-1 1-1 2 1c0-1 1-2 1-3-1-1-1-2-3-4 1 0 2 0 3-1l2 2c1 0 1-1 2-1z" class="B"></path><path d="M602 831v-1l1-1c0-1 0 0 1-1l1-3 3 3-1 1 1 1c-2 1-4 1-6 1z" class="H"></path><path d="M605 825l2-2c1 0 2-1 3-2h0 1c0 1 0 1 1 2l-1 1c-1 1-1 2-2 3 0 1 0 0-1 1l-3-3z" class="U"></path><path d="M602 820v1c-1 1-2 2-3 2-2-1-4-6-5-8h1 1c0 1 1 2 2 2h1c1 0 2 2 3 3z" class="V"></path><path d="M596 815h0 4 0c1-1 2-2 2-3l1-1c1 0 3-1 4-1s1 1 2 1c0-1 1-2 2-2l1 1-2 1s-1 2-2 2h-2c0 2 0 4-1 6l-2-1-1 2c-1-1-2-3-3-3h-1c-1 0-2-1-2-2z" class="U"></path><path d="M606 813c0 2 0 4-1 6l-2-1c-1 0-1 0-2-1h1 0 3l-2-2 1-2h2 0z" class="V"></path><path d="M595 797c1 1 1 2 2 3l-1 1c-1 1-1 2-1 4v1c1 1 1 1 2 1h2l2 1c-1 1-1 2-1 2-1 0-2-1-3-1l-1 1h1l2 1s0 1 1 1l-1 1h-2s-1 1-2 1l-3-3c0-3 0-4-1-6-1-1 0-3 0-4l3-3v-1h1z" class="j"></path><path d="M597 800c0-1 0-1 1-2l2-2h1c1 0 2 0 3 1h-1v1h1v-1l2-1c-1 1-1 3-1 3-1 1-2 1-1 2h5c1 1 0 2 1 3l-1 1c0-1 0-1-1-1v-1c1 0 1-1 1-1-2 0-2 0-3 1v3h-2l-2 1c-2-2-4-1-7-1v-1c0-2 0-3 1-4l1-1z" class="d"></path><path d="M596 801h0c1 0 2-1 3-1l1 1v-4h1c1 2-1 3 1 5h2l-1 1h-1l1 1c1 0 1 0 2 1-1 0-3-1-3 1h2l-2 1c-2-2-4-1-7-1v-1c0-2 0-3 1-4z" class="O"></path><path d="M616 787c0-1 0-1-1-2h3v9 4c0 2 0 6 1 8v1c-4-1-8 0-11-1h0l1-1 1-1c-1-1 0-2-1-3h-5c-1-1 0-1 1-2 0 0 0-2 1-3l1 1c-1-1-1-1-1-2h1l1-1 1-1 2 1c0-1 1-2 1-3-1-1-1-2-3-4 1 0 2 0 3-1l2 2c1 0 1-1 2-1z" class="W"></path><path d="M616 787c0-1 0-1-1-2h3v9 4h0v2l-1 1-1-2h0l-2-2c-1 0-1 0-2-1v-5c-1-1-1-2-3-4 1 0 2 0 3-1l2 2c1 0 1-1 2-1z" class="J"></path><path d="M616 791c0 2 2 3 2 7-1-1-2-2-3-2v-1c-2 0-2-1-2-3 1-1 1-1 3-1z" class="G"></path><path d="M616 787c0-1 0-1-1-2h3v9 4h0 0c0-4-2-5-2-7v-4z" class="C"></path></svg>