<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="68 40 544 636"><!--oldViewBox="0 0 668 752"--><style>.B{fill:#272827}.C{fill:#191716}.D{fill:#212120}.E{fill:#626363}.F{fill:#888787}.G{fill:#484848}.H{fill:#323332}.I{fill:#515251}.J{fill:#8e8c8b}.K{fill:#5b5857}.L{fill:#302e2e}.M{fill:#6e6e6e}.N{fill:#3e3a39}.O{fill:#383938}.P{fill:#ce4334}.Q{fill:#c73928}.R{fill:#999393}.S{fill:#801d16}.T{fill:#a7281f}.U{fill:#727170}.V{fill:#1d1d1d}.W{fill:#932119}.X{fill:#b1aeae}.Y{fill:#686a69}.Z{fill:#767777}.a{fill:#141312}.b{fill:#3f4140}.c{fill:#5c5e5d}.d{fill:#af291d}.e{fill:#7c7c7c}.f{fill:#555554}.g{fill:#818282}.h{fill:#bf2d20}.i{fill:#b7b1b1}.j{fill:#d43b25}.k{fill:#451916}.l{fill:#a5a2a3}.m{fill:#c63a2c}.n{fill:#71241f}.o{fill:#d3cac9}.p{fill:#9d2c25}.q{fill:#4a1716}.r{fill:#351e1c}.s{fill:#9a9a99}.t{fill:#4b4e4c}.u{fill:#434544}.v{fill:#b52619}.w{fill:#2e1715}.x{fill:#691714}.y{fill:#030202}.z{fill:#272120}.AA{fill:#89312c}.AB{fill:#c53727}.AC{fill:#adaaac}.AD{fill:#e34e35}.AE{fill:#db442f}.AF{fill:#eee5e4}.AG{fill:#b9b8b9}.AH{fill:#a18e8d}.AI{fill:#0c0d0c}.AJ{fill:#99342f}.AK{fill:#f9f6f6}.AL{fill:#512a27}.AM{fill:#8c221c}.AN{fill:#713936}.AO{fill:#5f1f1d}.AP{fill:#c2bdbe}.AQ{fill:#dd6b5a}.AR{fill:#9f1c12}.AS{fill:#8e5350}.AT{fill:#886d6c}.AU{fill:#ba2d1f}.AV{fill:#6b3836}.AW{fill:#b7a19f}.AX{fill:#937b79}.AY{fill:#7b7d7b}.AZ{fill:#e14d37}.Aa{fill:#321817}.Ab{fill:#8c4845}.Ac{fill:#604846}.Ad{fill:#fefdfc}.Ae{fill:#7f5a58}.Af{fill:#a27a78}.Ag{fill:#e3644d}.Ah{fill:#533c3a}.Ai{fill:#bc8380}.Aj{fill:#986765}.Ak{fill:#f2a38e}.Al{fill:#e4afa6}.Am{fill:#b16963}.An{fill:#735a5a}.Ao{fill:#a1625c}</style><path d="M376 699h1v3h-1v-3z" class="Ak"></path><path d="M457 233l3-3c0 1 0 3 1 4-2 0-3 0-4-1z" class="AZ"></path><path d="M176 148l2-1h0v1 1 3h-1v-1l-1-3zm170 474l3-3h2c0 2 0 2-2 3h-1-2z" class="j"></path><path d="M309 240c1 1 2 1 3 2 1 0 1 1 1 1h-1c-1 0-1 1-2 1l-1-1v-3z" class="P"></path><path d="M376 702h1c0 1 1 5 0 6h-1v-6z" class="Al"></path><path d="M184 491c1-1 2-1 4-1l-1 2c-1-1-2 0-3 1h0l-1-1s0-1 1-1z" class="j"></path><path d="M159 287h1c1 0 3 0 3 1-1 1-3 1-5 1v-1l1-1z" class="AZ"></path><path d="M171 243c1 0 2 1 3 1l-1 2-4-2c1-1 1-1 2-1z" class="j"></path><path d="M329 283l3-1v1c-1 1-2 1-4 2h-1v-1c1-1 1-1 2-1z" class="Q"></path><path d="M573 289v5h-1v2l-1-6 2-1z" class="AZ"></path><path d="M152 282c1 2 3 3 5 4h-5 0c0-1-1-1-1-2l1-2z" class="Ag"></path><path d="M601 129l2-2-1 7c-1-1-1-1-1-2v-3z" class="W"></path><path d="M556 341h3v3c-1 2 0 5-1 7v-7l-2-1v-2z" class="AD"></path><path d="M167 357h1v1h-6c-2 1-4 2-5 4v1l-1-1c1-2 2-3 4-4h1c2-1 4-1 6-1z" class="o"></path><path d="M459 255l3 3 1 1c-1 1-1 1-2 1h-1 0v-1c0-1-1-2-1-4z" class="AQ"></path><path d="M580 216c1 2 2 4 4 6-1 0-1 1-2 1l-1-1-1-1c1 0 1-1 1-2 0 0-1 0-1-1v-2zm-255-56v-1h1 1c0 1 1 2 1 2v2 1h-1c-1-1-2-3-2-4z" class="Q"></path><path d="M456 234l1-1c1 1 2 1 4 1h1c-2 1-3 1-4 2s-1 2-1 3c0-1 0-2-1-2v-3z" class="AD"></path><path d="M310 248c1 0 1 1 3 0-1 2-3 2-3 4-1 0-1 3-1 4v-5c0-1 0-2 1-3z" class="Ag"></path><path d="M323 542c1 0 1 0 2 1-1 2-3 3-4 3l-1-1h-1c2-1 3-2 4-3z" class="AE"></path><path d="M123 678l1 2v-1h0c1 2 0 6 0 8v1c-1-1-1-8-1-10z" class="o"></path><path d="M401 289c1-1 3-2 5-2 1 1 1 1 2 1-1 1-2 1-3 2-2 0-3-1-4-1z" class="h"></path><path d="M190 653l1 2h2c1 0 1 0 2 1-1 0-3 0-4 1-1-1-2-1-3-2l2-2z" class="Q"></path><path d="M164 596v-2-2h1 1 0l2 2c-1 0-1 1-2 2 0 0 0 1-1 1l-1-1h0z" class="AD"></path><path d="M313 261c1 0 2 0 3 1v1c-2 0-3 0-5 1l-1-1c1-2 1-2 3-2z" class="P"></path><path d="M323 542h0c1-2 1-3 2-5 1 0 1 1 1 2 1 0 1 0 1 1-1 1-1 2-2 3-1-1-1-1-2-1z" class="AD"></path><path d="M328 516c1-1 2-1 3 0h0c2 0 5 0 6 2-1 0-1 1-1 1-1-1-2-1-3-2l-2-1-1 1h-2 0v-1z" class="P"></path><path d="M368 653c1 1 1 0 2 0v6h-2v-6z" class="Q"></path><path d="M581 164l2-1c1 1 2 3 2 5h0c-2 0-2 0-3-1l-1-1h0v-2z" class="S"></path><path d="M556 343l2 1v7 1h-1l-1-9z" class="AZ"></path><path d="M155 132c-1-1-1-2-1-3 0-2-1-3-3-5h0v-1l2 1h1 1 1v4c-2 2-1 2-1 4z" class="AD"></path><path d="M333 335c-2-2-4-3-6-5 4 0 7 3 10 4h0v1h-1c-1 0-1 0-1-1-1 0-1 1-2 1z" class="AE"></path><path d="M191 476l1-2 1 1c0 1 0 1 1 2h1v3l-2 2c0-1-1-3-1-4s0-2-1-2z" class="Q"></path><path d="M102 637c-2-2-3-3-4-5l8 2 1 2c-2 0-4-1-5-1v1 1z" class="W"></path><path d="M337 518v1c1 0 2-1 2-1 1-1 1-2 2-3h0c1 1 1 1 1 2-1 1-1 2-1 4v-1h-3-2v-1s0-1 1-1z" class="AQ"></path><path d="M405 620h-1l1-1-1-1-2-2h1 1c1 1 4 2 5 3v1c-1 0-1 1-1 1-1 1-1 0-1 1l-2-2h0z" class="o"></path><path d="M311 532v-1c1 1 1 2 1 4 0 0 0 1 1 1 0 2-1 4-1 5-1 0-1 0-1-1 0-2 1-6 0-8z" class="j"></path><path d="M399 298s1 0 1 1c1 0 1 0 2 2l-1 1v3h-1c-2-2-1-5-1-7z" class="h"></path><path d="M150 286l1 1 1-1h5l2 1-1 1v1c-2 0-5-2-8-2-1 0-1 1-2 0l2-1z" class="AD"></path><path d="M174 323c-3 0-7 1-9 2h-1 0c1-1 1-2 3-2l7-3-1 3h1z" class="Ak"></path><path d="M353 616l1 1-1 2 1 1h-1v2c0 1 0 1-1 1h0-1v-1h-2c2-1 2-1 2-3 1-1 2-2 2-3z" class="h"></path><path d="M409 619v1c0 1-1 2-1 3l1 1-1 1c-1 0-2 0-2-1l-1 1v-1-4h0l2 2c0-1 0 0 1-1 0 0 0-1 1-1v-1z" class="P"></path><path d="M370 649v-1h3l1 1c-2 1-3 2-4 3v1c-1 0-1 1-2 0h0v-3h1l1-1z" class="v"></path><path d="M376 676c0 5 0 10 1 14v1-2 10h-1v-23z" class="Al"></path><path d="M126 655c1 0 2 1 3 2h2c0 1 0 2 1 3h-1c-2-1-4-2-7-3h0 1c1-1 1-1 1-2z" class="j"></path><path d="M384 628c0 2 1 2 3 2l3-3c0-1 1-2 2-3 0 2-3 4-3 6l1 1h-6-2v-1l1-1 1-1z" class="P"></path><path d="M167 282c1 1 1 1 2 1-2 2-3 4-6 5 0-1-2-1-3-1 3-1 5-3 7-5z" class="AE"></path><path d="M340 337c1-1 2-2 4-2h1c0 1 0 1-1 2l-2 2c-1 0-2 1-3 1l-1-1 1-1 1-1zm235-135l3 7-1 1-1 2c-1-2-1-4-2-5v-4c1 0 1 0 1-1h0z" class="AM"></path><path d="M353 615v-3c1-1 3-2 4-3h1l1-1c2-1 3-2 5-4h0 0c-1 4-7 5-9 9h0v1l-2 1z" class="P"></path><path d="M571 159c3 0 5 0 8 1h0c2 1 3 2 4 3l-2 1c-2-2-4-2-6-2l1-1h-1l-1-1h0 0c-1 0-1 0-2-1h-1z" class="AA"></path><path d="M381 325l4-4c1 1 1 1 1 2l1 1-5 3c0-1-1-1-1-2z" class="AF"></path><path d="M399 298c-1-1-1-2-1-3 0-3 1-5 3-6 1 0 2 1 4 1l-2 2s0-1-1-1-1 0-2 1c0 2-1 3-1 5v1z" class="T"></path><path d="M182 259c1 0 1 1 2 1v1c-2 0-3 1-5 2v1c-1-2-2-3-2-5h0 1c1 1 3 0 4 0h0zm155 75c1 0 1 0 2 1l1 2-1 1-1-1-1 1s-1 0-2 1h0c-1-2-1-3-2-4 1 0 1-1 2-1 0 1 0 1 1 1h1v-1z" class="Q"></path><path d="M556 337v-3l1 1h1l1 2c1 1 0 3 0 4h0-3v-1-3z" class="AZ"></path><path d="M556 337c1 0 2 1 3 2v2h-3v-1-3z" class="AQ"></path><path d="M163 610l1 1c-1 2-2 4-1 6v1s-1 0-1 1c-1 0-1 1-1 2v1l-2-2c1-1 1-2 2-4l-1-1c1 0 1-1 2-1 0-1 0-3 1-4z" class="P"></path><path d="M564 290h1 1l1 1v1c1 1 2 1 2 3v1h-1c-1-1-1-1-2-1h-1c-1-1-1-1-1-2h0v-3zM497 85h1s0-1 1-2c0 0 1-1 2-1v1l-2 2c1 1 2 1 3 1h2c0 1 0 2-2 2h-2-2-3l2-2h0v-1z" class="AE"></path><path d="M578 209l2 7v2c0 1 1 1 1 1 0 1 0 2-1 2v-1c-2-3-3-5-4-8l1-2 1-1z" class="d"></path><path d="M163 604v-6c0-3-1-5-3-6l-1-1c1 0 2 1 3 1 0 2 1 3 2 3v1h0l1 1h0l1 2h-1v2l-1 1v2h-1z" class="m"></path><path d="M600 133l1-1c0 1 0 1 1 2-1 5-2 8-5 12 0-3 1-4 2-7 1-2 1-4 1-6z" class="n"></path><path d="M458 338v-6c1-1 1-1 1-2v-1c1 2 0 4 1 6v1c1 1 3 1 4 1-1 1-2 1-3 1-1 1-1 1-1 2h-2v-2z" class="T"></path><path d="M315 649h1c0 1 1 1 1 1l2 2c-2 1-4 2-5 3-1 0-2-1-2-2l3-4zm61-20l4-5c1-1 1-2 1-4 1 3 1 5 1 8h0-1-2l-1 1h-2z" class="P"></path><path d="M556 329h0 2 1c0 2 1 3 0 5l-1 1h-1l-1-1v-1l-1-2c0-1 0-2 1-2z" class="AQ"></path><path d="M556 333h3v1l-1 1h-1l-1-1v-1z" class="Ag"></path><path d="M174 129c1 1 1 2 2 2-1 2-2 3-2 5l-2 2v2h-1s-1-3-1-4l2-2c0-1 1-3 2-4v-1z" class="j"></path><path d="M573 279v10l-2 1v-6c-1 2-3 5-4 7l-1-1c0-1 1-2 2-3s1-2 2-3 1-2 1-3c0 1 0 2 1 2h0c1-1 1-3 1-4z" class="AQ"></path><path d="M123 662h2c-1 5-1 11-1 17h0v1l-1-2c0-5-1-11 0-16z" class="AF"></path><path d="M572 296v-2h1c0 3 1 15 0 17h0c-1-2-1-4-1-6v-9z" class="Ag"></path><path d="M327 83c3-3 7-6 12-7h0c-2 1-4 2-5 3s-1 2-1 3v1c-1-1-3 0-4 1-1 0-1-1-2-1zm-19 573l4-3c0 1 1 2 2 2v1c-1 1-2 3-2 4-1 1-1 1-3 1v-1h0l1-1c1-1 1-1 0-2l-2-1z" class="m"></path><path d="M86 91l2 1-2 2c-1 0-3 1-4 2-2 1-2 3-5 4 2-4 6-7 9-9z" class="AJ"></path><path d="M568 87c4 1 7 2 11 3-1 1-2 1-3 1l-11-3v-1h3z" class="AZ"></path><path d="M460 230h0c1-2 1-6 1-8 1 2 0 4 1 5h1v5c1 1 1 2 3 3h-1 0c-1-1-2-1-3-1h-1c-1-1-1-3-1-4z" class="Q"></path><path d="M570 272h1l2-1c-1 3 0 5 0 8 0 1 0 3-1 4h0c-1 0-1-1-1-2v-3c-1-1-1-3-2-4 0-1 1-2 1-2z" class="j"></path><path d="M184 493c0 1-1 2-2 2-1 1-1 0-2 0-1 1-1 1-2 1-2 0-5 2-7 1-1 0-2-1-3-1h-1 3c2 0 5 0 7-1s4-2 5-3c0-1 0-1 1-2h0l1 1c-1 0-1 1-1 1l1 1z" class="P"></path><path d="M150 623h0v-1h-1c-1-2-1-7 0-9 0 0 0-1 1-1v1 1c0 1 0 3 1 4 0 1 0 2 2 2l1-2c0 1 0 2-1 3l-1 1c-1 0-1 1-2 1z" class="m"></path><path d="M335 339h0c1-1 2-1 2-1l1-1 1 1-1 1 1 1c1 0 2-1 3-1h1c-2 1-5 2-6 4h0c-1-1-1-1-2 0h-3c1-1 2-3 3-4z" class="p"></path><path d="M224 86c1-1 1-2 2-2s2 1 3 1v1c2-1 3-1 5-1l1 1c-1 0-1 0-2 1-1 0-3 0-4 1h-2-3v-2z" class="AE"></path><path d="M86 91c5-3 9-4 14-5v1c-1 0-1 1-2 1l-8 3-2 1-2-1z" class="v"></path><path d="M304 86v-1c2 0 2-2 2-4h0 1v3 1c2-1 3-1 5-1h0l1-1h2c0 1-1 1-2 2l-7 2h-2v-1z" class="P"></path><path d="M196 650c0 1 1 2 2 3h3s0 1-1 1l-1 1h-1 0c1 1 2 2 3 2s3 1 4 2c-2-1-4-1-6-2-1 0-3-1-4-1-1-1-1-1-2-1l2-1v-1-1-2h1z" class="v"></path><path d="M350 143v-1h0 2l-1 18-1-1v-16z" class="AD"></path><path d="M420 625c2-1 4-2 6-4 3-1 4-3 7-5-1 3-3 5-5 7-2 0-5 3-7 4 0-1 0-1-1-2z" class="j"></path><path d="M311 522h2l1-2 1 2h0c-2 1-2 2-3 3-1 2 0 4 0 5 0 0 0 1-1 1v1c-1 0-2-1-2-1 0-3 1-5 2-7v-2z" class="m"></path><path d="M297 644h2v1l-8 5 4 3h-2c-2 0-4-1-6-1h3c0-1 0-1-1-2h-4 4c3-1 6-4 8-6z" class="d"></path><path d="M304 137h2s1 1 1 2c-1 2-2 5-2 7h-1l-2-2h0l1-1c0-1 0-1-1-2v-1l1-1 1-2z" class="n"></path><path d="M303 139c1 0 2 1 2 1 0 1-1 1-1 2v1h-1c0-1 0-1-1-2v-1l1-1z" class="k"></path><path d="M100 86c1-1 12 0 15 0l-1 1h-1c-2 0-3 1-5 1h-7-3c1 0 1-1 2-1v-1z" class="m"></path><path d="M359 653c0 1 1 2 2 2-1 2-1 3-1 5v3l-1 1c0-1 0-1-1-2-2-1-5 0-7 0 1-1 4-2 6-2 1-2 1-5 2-7z" class="Q"></path><path d="M327 83c1 0 1 1 2 1 1-1 3-2 4-1l2 2 1-1 1 1v1h-12c0-1-1-1-1-2 1 0 2-1 3-1z" class="AU"></path><path d="M156 128l6-4h3c-2 1-4 2-6 4 0 1-1 2-1 3l-1 2-2 2v-3h0c0-2-1-2 1-4z" class="AE"></path><path d="M569 227c1 0 1-1 2-1 0-1 1-2 2-3h2l1 1h1c-4 2-5 4-7 7-1 2-1 3-2 4 0-1-1-2 0-3v-1c0-1 1-2 1-4z" class="AN"></path><path d="M201 653c3 1 6 1 8 3 1 1 0 3 1 4 1 2 4 5 3 7-1-1-1-2-1-2-1-2-2-3-3-4 0-1-1-3-2-4-1-2-5-2-8-2l1-1c1 0 1-1 1-1z" class="AU"></path><path d="M564 309c1 0 1 0 1 1l2 6c-2 3 0 10-1 14h0-1v-15c0-1-1-4-1-5v-1zM155 132v3l2-2 5 4-6-1c-2 4-3 6-5 9-1 1-1 1-2 1v-1c3-1 4-4 5-7-1 1-2 1-3 2v1h0c0-1 0-1 1-2 2-2 3-3 3-7z" class="Ak"></path><path d="M174 587h0v3h2 2c0 1 0 0 1 1l-1 1v1h-3l-1-1h0c-1 1-1 1-2 1-1-1-2 0-3 0l-1 1-2-2c2-1 4-1 5-1 1-1 2-3 3-4z" class="AE"></path><path d="M361 648c1 0 2 1 2 2h1v1h2c-2 1-2 1-3 2l-2 2c-1 0-2-1-2-2s0-2-1-3h1l-1-2h1 2z" class="j"></path><path d="M361 648c1 0 2 1 2 2h1c-3 1-3 1-5 0l-1-2h1 2z" class="AF"></path><path d="M117 635l11-1c5 0 3-2 5-5l1 1s-1 1 0 2h1l-1 1v1l-1 1h-2c-2 1-4 1-5 1h-9v-1z" class="Q"></path><path d="M106 634l1-1h0c1-1 2-3 3-4 2-1 3-1 4-1h1c1-2 1-3 1-5 0-1 0-1 1-2h1c-1 1-1 1-1 2v1c-1 2 1 2-1 4h1 1l1 1h-4c-2 1-3 2-4 4l-1 1h-4z" class="AQ"></path><path d="M460 305h0 4c2-1 3-1 4-1 1 1 0 1 0 2-1 1-3 2-4 3-2 0-3 1-4 2v-6z" class="j"></path><path d="M94 171c3 6 6 10 11 15h-2c-5-4-7-8-11-14h2v-1h0z" class="S"></path><path d="M320 560v-1l1 1c1 1 1 4 3 4 1 1 1 1 2 1 3-1 5-4 9-3h1-2l-8 4h-4c-1 0-2 0-3 1 0-1-1-1-1-1l2-3c1-1 0-2 0-3z" class="j"></path><path d="M317 80l-1-2c-1-2 0-4 0-6h1c0 3 0 5 1 6v1l1 2h1c2 0 3 2 4 3 0 1 1 1 1 2h-2c-1-1-1-1-1-2h-2s0 1-1 1h-1v-5h-1z" class="m"></path><path d="M172 273s-1 0-1-1c-3-2-4-2-8-1h0v-1c1 0 2 0 3-1h0l6 1 4-2h2v2h1l-6 2-1 1z" class="j"></path><path d="M375 665l1 1 1 2v4c0 5 1 11 0 17v2-1c-1-4-1-9-1-14l-1-11z" class="Ak"></path><path d="M295 653c2 1 3 2 4 4l1 1-1 1h-1v2 1l1 1v1h-1s-1 0-1-1c-1-1-1-3-1-4-1-1-1-2-2-3-1 0 0-1 0-2 0 0-1 0-1-1h2z" class="Q"></path><path d="M285 650h4c1 1 1 1 1 2h-3c-1 1-2 1-3 1-4 3-6 9-10 11h-2v-1h2s1 0 1-1c1-1 2-3 3-5 1-1 3-3 4-5h0l1-1s1 0 2-1z" class="v"></path><path d="M310 258c0 1 1 2 2 2l1 1c-2 0-2 0-3 2l1 1h0c-1 0-2 0-3-1-1 1-2 2-2 3-1 3 0 7-1 10l-1-12 1-1c1-1 1-1 2-1 0-1 1-1 1-2v-1h0v-1h2z" class="m"></path><path d="M584 222h2c1 0 2-1 3-2s2-4 1-5c0-1-2-3-2-5h0c2 1 4 2 5 4v1c-2 3-2 7-6 9h-4l-1-1c1 0 1-1 2-1z" class="P"></path><path d="M164 209l-1-1c-1 1-3 1-5 2-2 0-3-1-5 0h-1l-1 1-1-1v-2h0l2 2h0c1-1 2-1 3-2 0-1 0-1 1-1l1 1c3 0 5-2 7-3h1c2 1 4 1 7 1h-1c-1 1-3 0-5 1h-1l-1 2z" class="j"></path><path d="M488 83c1-4 6-6 8-9v-1s1-1 1-2c0-2 0-3-1-5h-1l1-1c0 1 2 3 2 4v2c0 1-1 2-1 3-1 3-4 4-4 7v1 1h-5zm-66 3h23c-2 1-4 0-6 0l1 2h6 0-11c0 1 0 1 1 1h-3 0-1c0-1-1-1-2-1s-2-1-4-2h-4 0z" class="P"></path><path d="M440 88h-5s-1-1-1-2h5l1 2z" class="AB"></path><path d="M192 524c1 1 1 2 1 3s0 1-1 2h0 0c-1 0-1-1-2-1h-1c-1 1-2 3-2 5 0 1-1 2-2 2h0l-1-4c2-2 4-5 6-6 1 0 1-1 2-1z" class="P"></path><path d="M251 85l2-2c-1-2-1-3-3-4-1-1-2-1-3-1v-1c1-1 3 1 5 1 0 0 2-4 3-5 0 3-4 5-1 9 1 1 2 0 3 1v1 1 1h-1-1-4v-1z" class="AB"></path><path d="M234 85c1-1 2-1 3-1l2 1c2 1 4 0 7 0l1-2h1c0 1 0 1 1 2h2v1h4-6c-2 1-5 1-7 1h-3-2c-1 0-1-1-2-1l-1-1zm324 224s1-1 1-2h2l-1 11h-1c-1 0-2 1-3 1v-1l1-3h0c1-2 0-4 1-6z" class="AZ"></path><path d="M558 309c1 2 1 4 0 5l-1 1h0 0c1-2 0-4 1-6z" class="AD"></path><path d="M557 315l1 1 1 2c-1 0-2 1-3 1v-1l1-3h0z" class="Q"></path><path d="M195 644c2 1 5 1 8 1h3c2 1 3 0 5 0-2 0-4 1-5 1-3 1-5 2-9 2 0-1-1 0-1 0l-1 1c-1 0-1 0-2 1h0v-1-1h0l2-4z" class="P"></path><path d="M427 476v3 1c-1 1-1 1 0 2h-1v2h0c-1 1 0 2-1 3 1-1 1-1 1-2v-1c1 0 1-1 1-1v-1h0l1-2v-1c1 0 1-1 1-1 0-1 0-1 1-2-1 2-4 11-4 13v1 2h0l-6 9 3-9c2-5 3-11 4-16z" class="D"></path><path d="M317 80h1v5h1c1 0 1-1 1-1h2c0 1 0 1 1 2l-9 1h-5-3l7-2c1-1 2-1 2-2 1-1 1-2 2-3z" class="T"></path><path d="M177 151v1h1v-3l1 31h-1c-1-2 0-8 0-10 0-6-1-12-1-19z" class="Ag"></path><path d="M187 549c1 0 1 0 1 1l1 1c1 2 1 5 1 7l-1 2c0 1 0 2-1 3 0-3-1-5-1-8l-3-4c2-1 2-1 3-2z" class="P"></path><path d="M187 555s1-1 2-1h0c1 1 0 1 0 2l1 2-1 2c0 1 0 2-1 3 0-3-1-5-1-8z" class="AE"></path><path d="M460 322h0c1 2 3 2 5 3h0c1-1 1-1 2-1l2 2-1 1c-1 1-3 0-5 1-1 0-3 3-3 5v2c-1-2 0-4-1-6 1-1 1-2 2-3h-2l1-1v-3z" class="AD"></path><path d="M421 627c2-1 5-4 7-4-4 5-8 10-14 12v-1c2-1 4-2 4-3-1 0-1 0-2 1v-1h-3l5-2 3-2z" class="k"></path><path d="M400 305h1v-3c1 1 1 2 1 3 0 0 3 2 4 3h0c1 0 2 0 3 1-3 2-4 3-6 5h-1c-1-1-1-1-1-2s1-2 1-4c0-1-1-2-2-3z" class="AB"></path><path d="M190 456c-3-3-8-3-12-1-1 0-3 1-4 1l-1-1h1c2 0 4-2 6-3 0-1 0-1 1-2 1 0 1 0 2 1h0c1-1 1-1 2-1 0 0 1 0 1 1h0 1 1l2 2 1 2s-1 0-1 1z" class="d"></path><path d="M299 664v-1l-1-1v-1-2h1l2 2v1c1 0 1 1 2 1l-1 1c0 2 2 4 3 5v4 13h0-1v-10c0-2 0-4-1-6s-2-5-3-6h-1z" class="P"></path><path d="M132 86h27c3-1 8-1 11 0h3c-1 1-2 1-4 1l-1 1h-1c-1 0-3 0-5-1-2 0-4 1-6 1 0-1 1-1 2-2h-6-17-3 0z" class="AQ"></path><path d="M191 497l2 1c0 1 0 1 1 2h0l1-1v2c-1 1-2 1-2 2l-2 1v-1c-1 0-2 1-3 2l-2 3v1 1h-1l-1-1h1c1-1 1-3 1-4l-2-1h0l3-1c1 0 2-1 2-2s1-1 1-2c0 0 1 0 1-1v-1z" class="W"></path><path d="M519 391l2 1-12 19v-2l1-3 9-15z" class="T"></path><path d="M583 251c2 1 3 2 3 4v6 5c-1 3-1 5-1 8 0 1 0 3-1 4 0 2 1 6 1 8h0c-1-3-2-6-1-9v-7c0-4 2-8 0-12 0-1 1-3 1-4l-3-3h1z" class="AO"></path><path d="M174 331c1 0 2 0 2-1 1 0 1-3 1-4 0 3 0 5 2 8h2 0v4c-2-1-3-1-4-2 0-2-1-3-1-5 0 4 1 9 0 13h0c-2-2-2-10-2-13z" class="m"></path><path d="M181 334v4c-2-1-3-1-4-2l1-1 3-1z" class="P"></path><path d="M456 237c1 0 1 1 1 2 0 2 0 5 1 7 0-1 1-1 1-2l1 1c-1 0-1 1-2 1 0 3-1 5-1 8 1 0 1 1 2 1 0 2 1 3 1 4v1h0l-2 1-1 1c0-1 1-5 0-6-1-2-1-4-1-6 1-2 0-9 0-11v-2z" class="Q"></path><path d="M179 264v-1c2-1 3-2 5-2l-1 1c-1 1-1 1-1 2v1l-2 2h0c1 1 1 1 2 1v-1h1v1 1s-1 0-1 1h0c1 0 1 1 2 1h0c0 1 0 1-1 1h-1-1-1v-2h-1-1v-2h-2l2-2 1-2z" class="d"></path><path d="M105 186l12 10c-5-1-13-4-17-8 1 0 2 1 4 2h1l1 1h0c1 0 2 1 3 1-1 0-2-1-2-1l-1-1h0c-1-1-3-2-4-3h0l1-1h2z" class="p"></path><path d="M531 333c1 1 1 2 1 4s-1 3-1 5l-4 14-1 4v1l-1-1 6-27z" class="v"></path><path d="M559 318h1c0 3 0 8-1 11h-1-2 0v-1c-1-1-1-2 0-3v-3c0-1 1-2 0-3 1 0 2-1 3-1zm5-25h0c0 1 0 1 1 2h1c-3 4 0 9-1 14-1-2-1-7-2-8-1 1-1 2-2 3 0 1 0 1 1 1-2-1-2 0-3-1v-3h0v-1-1l2 2v-1-1-1c1 0 2 0 2-1 0 0 1-3 1-4z" class="AD"></path><path d="M161 114c3 2 5 5 7 7 1 2 2 3 3 4-1 1-1 1-1 2v1h-1c1-1 0-3 0-4l-1-1-1 1c1 1 1 2 1 3h-1-1c0-1 0-2-1-2v-1h-3l3-3c-1-2-3-4-5-6l1-1z" class="AU"></path><path d="M404 625c1 1 1 1 1 2s0 2-1 3h0v1c-2 1-4 0-6 1-1 0-2 1-2 1h-3c-2-1-3-1-4-1s-1 0-2-1l-1 1c-1-1-1-1-2-1h6c1 0 4 1 5 0 1 0 2-1 3-1v-2-1h3c1 0 2-1 3-2z" class="Q"></path><path d="M184 190v-5-2h1v-2-2-1l1-2v-2-1h1 1-1v2 2c-1 2-1 3-1 5 1 0 1 0 2 1l-1 2s1 0 2 1c0 1 0 2-1 3l-1 1-2 1s-1 0-1-1zm150 93h1 1 1c3 1 5 0 7 1h0 1c2 1 5 5 5 7l-2 2c-1 0-2 0-3-1-1 0-2-1-2-2 0-2 1-2 2-3 0-1 0-1-1-2h-1c-1-1-4-1-6-1h-3v-1z" class="AK"></path><path d="M178 86h6 4c2 0 3-1 5 0h6 4v2l-2-1c-2 1-4 1-6 1-1-1-1-1-2-1s-4 0-4 1c-2 0-2 0-3 1v1l-1-1 1-1h-1c-2-1-6 0-8 0 0-1 1-1 1-2z" class="m"></path><path d="M193 86h6 4v2l-2-1c-2-1-5 0-8-1z" class="AD"></path><path d="M450 212v-2h1c2 4 2 8 3 12s2 8 2 12v3 2-1c-1-2-1-4-1-5l-1-2h0c0-2 0-2-1-3-1-6-1-11-3-16z" class="w"></path><path d="M409 624l2 2h0c-1 1-1 1-2 3h1c4-1 7-2 10-4 1 1 1 1 1 2l-3 2c-3-1-6 1-9 1h-1-2l-2 1v-1h0c1-1 1-2 1-3s0-1-1-2l1-1v1l1-1c0 1 1 1 2 1l1-1z" class="AE"></path><path d="M404 631l2-1h2 1c3 0 6-2 9-1l-5 2c-3 1-6 2-9 2h-3l-1 1h-1-2 0-2 0c-1 0 0 0-1-1h0-3 2 3s1-1 2-1c2-1 4 0 6-1z" class="AB"></path><path d="M172 138h0l1 2 1 4h4 3c-1 1-2 2-3 4v-1h0l-2 1h0-1c-1 0-3 0-4-1-1-2 0-4 0-7h1v-2z" class="P"></path><path d="M383 658h2 1l1 1c-1 1-1 1-1 2l-1 26c-1-1-1-8-1-10v-10c-1-1 0-2-1-4v-5z" class="j"></path><path d="M384 677c0 2 0 9 1 10 1 3 0 8 0 11v22h-1 0v-43z" class="AF"></path><path d="M157 668l1 1 1-1v21c-1 3 0 5-1 7v-20c-1-1 0-5-1-6h-1-1l2-2z" class="AD"></path><path d="M302 657h1c2 0 3 0 5-1l2 1c1 1 1 1 0 2l-1 1c-2 0-3 0-4 1s-2 1-2 2c-1 0-1-1-2-1v-1l-2-2 1-1c0-1 1-1 2-1h0z" class="T"></path><path d="M300 658c0-1 1-1 2-1h0c1 1 1 2 0 3 0 1 0 1-1 2v-1l-2-2 1-1z" class="x"></path><path d="M383 657c0-1 0-2 1-2l-1-1 1-1h2l1 1v-1 2c0 1 0 2 1 3 0 0 0 1 1 1h0 1c1 1 2 3 3 4v-3c4 3 6 8 11 11-2 0-2-1-3-2-2-1-4-5-7-6-1 1-1 1-2 1l-1-1c-1-1-1-2-3-3h0l-1-1-1-1h-1-2v-1z" class="T"></path><path d="M153 660h4 0v1s1 1 2 1l1 1h0c-1-1-2-2-2-3l2-2 1-1v-1h2c-1 2-2 4-3 7v2h-1v3l-1 1-1-1-1-3h-1v-3-1l-2-1z" class="Ag"></path><path d="M156 665h0c0-1 1-2 1-2h1c1 1 1 1 1 2v3l-1 1-1-1-1-3z" class="Q"></path><path d="M169 244c-1 0-1-1-1-1-6-1-8 8-12 10-2 1-3 1-5 0-3 1-5 4-6 6h0v-1c1-1 2-2 2-3 1-1 2-2 3-2 2-2 3 1 5-1 3-2 4-4 5-6 2-2 4-4 6-5h1l-2-3h0c2 0 5 3 6 5-1 0-1 0-2 1z" class="AE"></path><path d="M567 152c1 0 1 1 2 0 2 0 1-1 2-3-1 4 0 7 0 10h1c1 1 1 1 2 1h0 0l-3 1h-2v-1l-1 1c-1 0-1-1-1-1-1-2-1-3-2-5h0v-2h2v-1z" class="n"></path><path d="M565 153h2l2 7-1 1c-1 0-1-1-1-1-1-2-1-3-2-5h0v-2z" class="m"></path><path d="M188 395c1 0 1-1 1-1l1-1v2h1l1-1c1 0 1 0 1 1h1l-3 3s-1-1-2-1v1h-2c0 1-1 1-1 1l-3-1s-1 0-1-1h0c-1 0-1 0-1 1h-1-3c-1-1-2 0-3 1l-1 1v-1-1c1 0 2-1 3-1 1-1 3-1 4-2 1 0 1 0 2-1 1 0 2 2 4 3 1-1 2-1 2-2z" class="v"></path><path d="M458 310c0 1 0 2 1 3h0 0c1 1 2 2 3 2h0c-1 1-2 1-2 1-1 2 0 4 0 6v3l-1 1h2c-1 1-1 2-2 3v1c0 1 0 1-1 2v6c-1-3-1-6-1-8 0-5 0-11 1-17v-3z" class="W"></path><path d="M458 310c0 1 0 2 1 3h0 0c1 1 2 2 3 2h0c-1 1-2 1-2 1-1 2 0 4 0 6v3c-1-2-1-2-2-3v-9-3z" class="v"></path><path d="M572 195c1-2 1-4 1-6s1-3 1-5h1c-1 6-1 12 0 18h0c0 1 0 1-1 1v4h-2v-3h-1v-1c1-2 1-5 1-8z" class="n"></path><defs><linearGradient id="A" x1="92.165" y1="90.539" x2="98.141" y2="90.111" xlink:href="#B"><stop offset="0" stop-color="#968384"></stop><stop offset="1" stop-color="#a29b9c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M98 88h3c-4 3-8 6-12 10h0l-2 1 1-2 1-2c-1-1-1-1-3-1l2-2 2-1 8-3z"></path><path d="M88 92l2-1 2 1-1 1-2 2c-1-1-1-1-3-1l2-2z" class="Aj"></path><path d="M159 689v43l-1 1h-1c0-4 1-8 1-12l-1-20c0-1 0-4 1-5 1-2 0-4 1-7z" class="Ak"></path><path d="M186 358c1 1 2 1 2 1 0 1 1 2 2 3h1c0-1 0-1 2-2l1 1 1 1c0 1-1 2-1 3l-2-1-1-1c-2 2-2 8-2 10h-1s0-1-1-1h1c0-2 1-7 0-9 0-1-1-1-2-2-1 0-2-1-3 0-2 0-4 1-6 2l-1 1v-1c1-1 2-1 3-1 2-2 4-3 7-4z" class="P"></path><path d="M135 86h17 6c-1 1-2 1-2 2l-2 1c-1 0-2-1-3-1h-10l8 3c-3 0-7-1-10-2 0-1-3-2-4-3z" class="j"></path><path d="M458 281h1v3s-1 1-1 2 1 1 1 2c-1 2 0 3-1 4 0 2 1 4 0 6 0 2 0 5 2 7h0v6l-1 2h0c-1-1-1-2-1-3l-1-18c0-3 0-6 1-8v-3z" class="h"></path><path d="M368 659h2l-1 17v17c0 3 1 8 0 10h-1v-44z" class="j"></path><path d="M570 223v2h0v2h-1c0 2-1 3-1 4v1c-1 1 0 2 0 3 0 4 0 7 1 10v1h-2s-1-1-1-2c-1-1-1-3-1-4h0c-1-4 1-8 2-12 0-1 0-1 1-2 0-1 1-2 2-3z" class="k"></path><path d="M460 340h0c1 2 3 4 4 5h0c-2 1-3-1-5-2 0 3-1 7 0 10v2c3 3 8 8 12 9 1 0 2 0 3-1v1 1h-3c-1-1-3-1-3-1l-1 1c-3-3-5-6-8-7h0c0-1-1-2-1-3 0-3-1-7 0-10v-5h2z" class="m"></path><path d="M315 550h1c0 1 0 2 1 4 0 0 1 1 2 1l1-1v2h2s1 1 2 1v1c-1 0-2 0-3 1v1l-1-1v1c-1 0-1 0-1-1-1-1-4-2-6-3v3h-2c-1-1-1-2-2-3 0 0 2-2 3-2 0 0 1 1 1 0h1c0-1 0-1 1-2h-2 0-1v-1l3-1h0z" class="AU"></path><path d="M355 613c1 1 1 2 3 2 1 0 2-1 3-1h1c2-1 4-3 5-5 0-1 0-2 1-2l-1 3v1c1 1 1 1 2 1h0c-1 1-2 1-3 0-4 3-9 5-10 9v1 2h0l1 1-1 1c-1 0-1 0-1-1-1-1-1-2-2-3v-2h1l-1-1 1-2-1-1v-1h0l2-1v-1z" class="v"></path><path d="M555 311l1-1h0c0 2 0 3-1 4l1 1h1 0l-1 3-2 2c-1 2-1 4-2 6s0 15 0 18v3c-1-1 0-4-1-5-1-4-1-8-1-11l-1-1h0 0l2-7h0c2-2 2-4 2-6l2-6z" class="AQ"></path><path d="M176 649c1 0 1 0 2 1l1 1 1 1c0 1 1 2 0 3-1 0-1 0-2-1-1 0-2 1-2 2-2 2-1 7-1 10l-1 11v-4-15c-1 0-1 0-1-1s0-2-1-3l1-1c0-1 1-2 2-4h1z" class="v"></path><path d="M178 650l1 1 1 1c0 1 1 2 0 3-1 0-1 0-2-1h0v-4z" class="T"></path><path d="M175 649h1c0 1 1 2 0 3l-3 5c0-1 0-2-1-3l1-1c0-1 1-2 2-4z" class="o"></path><path d="M101 88h7c2 0 3-1 5-1l-1 1c-3 1-7 2-10 4-3 1-6 3-8 4-3 1-4 3-6 4h0l1-2h0c4-4 8-7 12-10z" class="i"></path><path d="M360 311v-1c2 0 3 0 4 2 1 1 1 2 1 3v1c1 0 1 1 0 2 0 3-1 7-4 8l-3 2h-5-2c0-1-1-1-2-1s-1-2-2-2c0-2 0-2 1-3 1 2 1 3 3 4s6 1 8 0 3-2 4-4c0-1 1-4 0-5-1-2-3-3-5-4 0 0-1 0-1-1h0c3 0 5 2 7 4v-1-1c-1-2-3-2-4-3z" class="N"></path><path d="M316 262c2 0 3 1 5 2v1 1c1 3 1 6 3 8 1 1 2 3 2 4l-1 1-2-5h0c-1 2 0 3 2 5h1c0 2 0 3 2 3l1 1c-1 0-1 0-2 1v1l-1-1c-1 0-2-2-2-3-1-2-3-4-3-6-1-3-2-7-3-10l-2-2v-1z" class="j"></path><path d="M515 392h1c0-1 1-2 1-3l4-8 1 1-2 5 1 1-2 3-9 15h-1v-1h0c1-3 2-5 4-7v-2c0-2 1-3 2-4z" class="AO"></path><path d="M515 392h1c0-1 1-2 1-3l4-8 1 1-2 5c-2 4-4 8-7 11v-2c0-2 1-3 2-4z" class="S"></path><path d="M574 219h3c1 1 1 1 3 1v1l1 1-1 1v1l3 3c1 1 2 0 3 2h3-2c-5 2-6-2-10-4v-1h-1l-1-1h-2c-1 1-2 2-2 3-1 0-1 1-2 1h1v-2h0v-2c1-2 3-3 4-4z" class="q"></path><path d="M483 449h-1c-1 3-12 17-14 18v-1c0-1 1-2 1-2l4-5 15-23c1 2 1 3 0 4l-5 7-1 1 1 1z" class="AR"></path><path d="M181 543h0c2-1 1-2 2-3h1v1c1 0 2 0 3-1l2 1v3h1 0c0 1 1 2 1 2l1 1-1 1-1-1-1-1c-1 0-1 0-1 1s-1 1 0 1l-1 1h0c-1 1-1 1-3 2l-3-8z" class="h"></path><path d="M187 540l2 1v3h1 0c0 1 1 2 1 2l1 1-1 1-1-1-1-1c0-1-1-1-2-1 1-1 1-1 1-2h-1c-1-1-1-1-2-1h0l-1-1c1 0 2 0 3-1z" class="d"></path><path d="M457 262l1-1c0 3 0 5 1 8 0 2-1 4 1 6 1 1 2 1 3 1l3-2c-1 2-2 3-3 6-1 1-3 2-4 4v-3h-1c-1-2-1-4-1-5 0-5-1-9 0-14z" class="P"></path><path d="M257 85s1-1 2-1v-1h2l1 1c2 1 4 1 5 1h2c1 1 4 1 5 1h6l4-4c1-1 1-1 2-1l1 1c-2 1-3 2-4 4-2 2-5 1-8 1l-19-1h1v-1z" class="AZ"></path><path d="M133 635h1 3c0-1 1-1 1-1h1c2-1 3 0 5 0-5 1-9 2-14 2l1 2h-8-5-1l-10-2-1-2h0 4c1 0 7 1 7 1v1h9c1 0 3 0 5-1h2z" class="d"></path><path d="M117 638c3-2 10-2 13-2l1 2h-8-5-1z" class="E"></path><path d="M313 559v-3c2 1 5 2 6 3 0 1 0 1 1 1 0 1 1 2 0 3l-2 3-5 3c0-2-1-5 0-6v-4z" class="Ad"></path><path d="M199 86c6-1 12-1 17 0 3 0 6-1 8 0h0v2h3l-2 1c-2-1-3-1-5-1h-6c-1 1-1 1-3 2-1-2-5-2-7-2h-1v-2h-4z" class="AQ"></path><path d="M224 86v2h3l-2 1c-2-1-3-1-5-1h-6-1c0-1-1-1-1-1 1-2 9-1 11-1h1 0z" class="Ag"></path><path d="M530 303v1c0 1-1 3-2 4v2c-1 1-1 2 0 3 0 2 3 3 4 4h1v-7c1 3 1 5 1 7v1 6c0 4-1 9-2 13 0-2 0-3-1-4l2-15c-3-1-5-2-6-4l-1-1c0 2 1 6 0 7h-1v-6l1-1v-2c0-1-3-3-4-4l4 2c2-2 2-4 4-6z" class="AR"></path><path d="M530 303l1-1h2v-1c1 3 1 6 0 9v7h-1c-1-1-4-2-4-4-1-1-1-2 0-3v-2c1-1 2-3 2-4v-1z" class="AK"></path><path d="M576 91c1 0 2 0 3-1 9 4 17 10 21 19l1 3c-1 0-2-1-2-1-1-1-1-2-1-3-2-2-3-5-6-6-3-2-6-5-10-7-2-2-4-3-6-4zm-83-9c1 1 1 2 2 2l2 1v1h0l-2 2c-2-1-2 0-3 0h-12c-2 0-2 0-3-2l8-2 3-1h5v-1z" class="j"></path><path d="M493 82c1 1 1 2 2 2h-2-8l3-1h5v-1z" class="AE"></path><path d="M582 223l1 1h4c4-2 4-6 6-9v-1c1 4 1 7-1 11 0 2-2 3-3 4h-3c-1-2-2-1-3-2l-3-3v-1l1-1 1 1z" class="k"></path><path d="M187 535v1c0 1-1 1-2 2l1 1-1 1h1 1c-1 1-2 1-3 1v-1h-1c-1 1 0 2-2 3h0c-1-1 0-1-1-1s-3 2-3 4c0 1 1 2 1 3-1 3-2 7-4 10-2 1-3 1-5 1h0c1-1 4-2 4-2 1-1 1-3 2-4 0-2 1-4 2-6v-1c-1 1-1 0-1 1h-1v-2c1-2 3-7 5-8 1-1 3-2 5-3h0 0 2z" class="p"></path><path d="M309 243l1 1c1 0 1-1 2-1h1 0c1 1 1 2 2 2 2 0 11-1 12-1v1c3 3 7 5 12 6 3 1 5-2 8-2s6-1 8 1c1 1 2 1 2 2h0c-3-3-5-2-9-3-2 0-5 2-7 2-1 1-3 1-4 0-3 0-6-2-8-4-1 0-3-2-4-2-4 0-8 4-11 3h-1c-2 1-2 0-3 0v-1h-1v-1-2h0v-1z" class="m"></path><path d="M309 246c1 0 3 0 4 1l1 1h-1c-2 1-2 0-3 0v-1h-1v-1z" class="P"></path><path d="M385 321h2 2c0 1 1 0 1 0 1-1 2-2 2-3l1-2c0-4-5-6-8-9 2 1 3 2 5 2l1-1h0v2c0 1 3 3 4 4l1 2c0 1 1 1 2 1 2-1 3-2 4-3h1c-1 1-2 2-3 4h-1c-3 1-10 7-12 6l-1-1c0-1 0-1-1-2z" class="AZ"></path><path d="M383 646h2v2c1 0 1 0 2-1 0-1 0-1 1-2v-1c1 1 2 1 3 1s1 0 2 1c1 0 1-1 2-2h2c0 1 0 3-2 3-1 1-3 0-3 1-1 1-1 1-2 1-1 1-2 2-2 4-1 2 1 4 2 6h-1 0c-1 0-1-1-1-1-1-1-1-2-1-3v-2 1l-1-1h-2l-1 1 1 1c-1 0-1 1-1 2v-2-2c-1 0-1 0 0-1v-6z" class="AU"></path><path d="M571 204h1v3h2c1 1 1 3 2 5 1 3 2 5 4 8-2 0-2 0-3-1h-3 1l-1-1-2-2c0 1 0 1-1 1v-2l-1-7h0 1v-4z" class="S"></path><path d="M571 204h1v3h2c1 1 1 3 2 5 1 3 2 5 4 8-2 0-2 0-3-1v-1-1l-3-6v-3l-1-1-1 1v1c-1 2 0 3 0 4h1l-2 2-1-7h0 1v-4z" class="x"></path><path d="M565 240h0c0 1 0 3 1 4 0 1 1 2 1 2h2v-1l2 3c1 1 2 1 3 2 0 1 0 2-1 3h-1 0l-2 1-2 2-1 1-1-1h-1v-1c0-2-1-5 0-8h0v-7z" class="AO"></path><path d="M565 256c0-2 1-2 2-3h1v3l-1 1-1-1h-1z" class="q"></path><path d="M571 248c1 1 2 1 3 2 0 1 0 2-1 3h-1 0l-2 1-2 2v-3l-1-1h-1v-2h2c1-1 1-1 2-1l1-1z" class="AA"></path><path d="M570 254c0-1-1-1-1-3h2l1 2-2 1z" class="AO"></path><path d="M309 542l2-2c0 1 0 1 1 1 2 2 5 3 7 4h1l1 1c-1 1-3 1-4 1l-1 3h-1 0l-3 1h-1v-2c-1 2-1 2-3 2h-1v-1c1-2 1-1 3-2l-1-1v-1-1c0-1 0-2-1-3h1z" class="h"></path><path d="M309 542c1 0 2 1 2 3h0l-1 1v1c2 1 3 1 5 1v2h0l-3 1h-1v-2c-1 2-1 2-3 2h-1v-1c1-2 1-1 3-2l-1-1v-1-1c0-1 0-2-1-3h1z" class="d"></path><path d="M311 549c1-1 2 0 3 0l1 1-3 1h-1v-2z" class="S"></path><path d="M557 352h1c1 3 0 7 0 10v30 16c0 1 1 4 0 5v1l-1-1v-61z" class="AD"></path><path d="M193 463l1-1h1v1 1l1 1v1c1 3 0 6 0 8 0 1 0 3-1 3h-1c-1-1-1-1-1-2l-1-1-1 2c-2-2-5-8-6-11 1 2 2 3 4 4 1 0 2 0 2-1 2-1 2-3 2-5z" class="d"></path><path d="M194 511l1-1h0c1 2 0 3 1 4h1v-1c1 0 1 0 1 1l1 4v5l-1 1-1-1v1l-1 1c0 1-1 2-2 2v2h-2 0c1-1 1-1 1-2s0-2-1-3v-2s1-1 1-2v-1c1-1 1-4 1-6h0c0-1-1-2 0-2z" class="T"></path><path d="M194 513v3c1 0 1 0 1 1v1c0 1 0 1 1 2v2c0 1 0 1 1 2l-1 1c0 1-1 2-2 2v2h-2 0c1-1 1-1 1-2s0-2-1-3v-2s1-1 1-2v-1c1-1 1-4 1-6z" class="Q"></path><path d="M196 522c0 1 0 1 1 2l-1 1c0 1-1 2-2 2v-2l1-1c0-1 0-1 1-2z" class="d"></path><path d="M188 490l1-1v-1c0-3-4-6-7-7-2-1-3-1-4-4l8 5c2 1 5 1 7 0h0l2-2v3 1 2 2l-2 1v1 2h0c-1 0-2 1-2 2l-1 1c-1-1-1-2-3-3l1-2z" class="AM"></path><path d="M193 492l-3-3v-4h1 1c0 2 0 2 1 3v1 1 2h0z" class="S"></path><path d="M137 650h0l2 5-2 1c-1 1-2 3-3 4 0 1 0 2 1 3 1 2 2 2 4 3l2 2c-2 0-3-1-4-1l-1-1h0-1v1c1 1 1 2 1 3v1c1 2-2 5-1 8 0 0 0 1 1 2 0 2 1 3 1 5v1c-1-2-1-4-2-5s-1-2-2-2c0 0 1-1 1-2 0-2 1-4 1-6v-1c0-4-4-7-4-11h1c-1-1-1-2-1-3 3 0 4-2 6-3l-3-3h2 1v-1z" class="AD"></path><path d="M179 270h1v2h1v1h2 0v1c-1 1-1 2-3 3-1 0-1 0-2 1h0c-1 2-2 3-3 4v-1l-2-2c-2 1-3 2-4 4-1 0-1 0-2-1 1-1 1-2 2-3-1-1-2-1-3-1h0v-1c2 0 2 0 3 1 1 0 1 1 1 1l1-1v-1l2-1 1-1-2-2 1-1 6-2z" class="Q"></path><path d="M179 270h1v2c0 1-1 1-1 1-1 1-2 2-4 2-1-1-1-1-2-3l6-2z" class="AF"></path><path d="M450 217c2 2 1 8 1 11-1 8-2 15-4 22l-2 10-1-1v-1h-2c1-4 2-7 3-10 3-10 5-20 5-31z" class="N"></path><path d="M442 258c1-4 2-7 3-10 1 1 1 2 0 4 0 2 0 4-1 6h-2z" class="E"></path><path d="M373 648h1c2-1 5-2 8-1l1-1v6c-1 1-1 1 0 1v2c-1-1-2-2-4-3-1 1-1 1-1 2s-1 2-1 2l-1 2s1 1 1 2c0 0-1 0-1 1 1 1 2 1 1 2 0 1 0 1-1 3l-1-1-1-16-1-1z" class="h"></path><path d="M383 646v6h-1c-1-1-1-2-1-3s0-1 1-2l1-1zm-6 10c-1 0-1-1-2-1 0-2 0-5 1-7h1c2 2 1 3 2 4-1 1-1 1-1 2s-1 2-1 2z" class="o"></path><path d="M188 373h1c0 2-1 9 1 11 0 0 1 0 1-1 1-2 3-4 4-6 0 2 0 3-1 5 1 1 1 1 0 2h0l1 1v1l1 3-1 2c0 1-1 1-1 1-2 0-3 1-4 1l-1 1s0 1-1 1h0c1-1 1-4 1-5 0 0-1-1-1-2-1-1-2-1-2-3 1 0 1-1 2-1h0v-11z" class="m"></path><path d="M195 385v1l1 3-1 2h-3v-3l1-1 2-2z" class="o"></path><path d="M184 239c0-1 0-1 1-1s1 0 1 1 0 3 1 4c0 1 0 1 1 2h2l-3 3h-1s0 1-1 2h-1-1 0c-2 1-3 0-4 2v1c-1-1-1-3-1-4-2-1-3-2-5-3l1-2c2 1 4 0 5 0 0 1 0 1 1 1 2-1 3-4 4-6h0z" class="AB"></path><path d="M184 249c0-1 0-1 1-2h1v1s0 1-1 2h-1-1l1-1z" class="T"></path><path d="M178 249c0-1 0-1-1-2h1 5c0 1 0 1 1 2l-1 1h0c-2 1-3 0-4 2v1c-1-1-1-3-1-4z" class="h"></path><path d="M186 399s1 0 1-1h2v-1c1 0 2 1 2 1h0c-2 3-2 9-2 13v26c0 1 1 1 0 2-1-2-1-6-1-8v-24c-1 1-2 2-4 3-1-1-1-1-2-1l-9 6c-1 0-2 1-2 2h-2l-2-2c2-1 3-1 4 0 4-2 7-5 10-7v-1c1 0 1 0 2 1 1 0 3-1 4-2v-1c0-1 1-4 0-6h-1z" class="j"></path><path d="M450 217c-1-1 0-4 0-5 2 5 2 10 3 16v8l-2 11v5s-1 0-1-1h0-1v-1h-2c2-7 3-14 4-22 0-3 1-9-1-11z" class="F"></path><path d="M450 246v-3h1c1 1 0 3 0 4v5s-1 0-1-1h0-1v-1l1-4z" class="R"></path><path d="M450 246h1v5h-1 0-1v-1l1-4z" class="l"></path><path d="M357 122c5 0 10 1 14 0h2c1 1 2 1 3 1-2 0-5 0-7 1 0 1 3 6 4 7 1 2 3 3 5 3h1c3 0 5-1 8-2-1 1-3 2-3 2 2 2 5 4 5 7v1c-2-3-2-5-5-6-1 0-2-1-3-1l-1 1c-1 0-1 0-1-1h-1c-2 0-4-1-6-3-1-1-1-3-2-4-2-1-4-2-6-2l-2 1-1-1c2-2 4-1 5-3h-2c-1 0-1 1-1 1-2 0-6-1-7 0l-1 1h-1c-1 1-1 2-1 4v-1h-1l-1-1h-1l-1-1s0-1 1-1v-1h0l-2-1h1c1-1 1 0 2 0h2c1 0 1 0 2-1h2z" class="h"></path><path d="M351 127c1-1 1-2 1-3h0l1-1c1 1 2 1 3 1l-1 1h-1c-1 1-1 2-1 4v-1h-1l-1-1z" class="AD"></path><path d="M188 563c1-1 1-2 1-3 1 1 1 3 1 4l-1 3c0 1 0 2-1 3v2l-1 2h1v1l-1 1-1 2h-1c-1-1 0-2-1-3l-1 1v1h-1-1c0 1-2 3-2 4-1 2-3 3-4 4 0 1 0 2-1 2h0c0-1 0-2 1-3v-1h-2 0c1-1 3-1 4-1 1-2 2-3 3-5 0-1-1-1-2-2 1 0 3 0 4-1 1 0 2-2 2-3v-2c-1-1-2-1-3-2s-3-2-4-3c2 0 4 1 6 2 0 0 1 0 2 1l1-2 2-2z" class="j"></path><path d="M188 563c1-1 1-2 1-3 1 1 1 3 1 4l-1 3-1-1h0c-1 0-2 0-2-1l2-2z" class="P"></path><path d="M129 107h1c3 0 8-1 12 0 2 0 6 0 8 1s5 3 6 4c2 1 3 1 5 2l-1 1-3-1-3 3v2l-1-1c-1 1-1 1-2 1-1 1-2 2-2 3h0-1c-2 1-5 3-8 4 2-1 4-3 6-4 2-2 3-5 6-7h0c0-2 0-4-1-5-1-2-10-3-13-3-2 2-4 1-6 2-1 1-1 1-1 2 1 2 2 2 3 4v2h0c-1-2-3-4-5-5v-5z" class="AB"></path><path d="M512 86h3c2 1 5 0 7 0h12c1-1 2-3 3-4l1 1c1 0 1 0 1 1 1 1 1 2 2 2h8c6 0 12 0 19 1h-3v1c-10-1-21 0-31 0-6 0-13-1-19 0h0-1v-1l-2-1z" class="AQ"></path><path d="M538 83c1 0 1 0 1 1v2h-2-1c1-1 1-2 2-3z" class="AK"></path><path d="M445 86h24c0-1 1-1 1-2h0c1-1 1-2 2-3-1-1-1-1-1-2 1 0 2 0 2 1v2c0 1-1 2-1 2l-1 1 2 1h4c1 2 1 2 3 2h-2l-11 1-21-1h0-6l-1-2c2 0 4 1 6 0z" class="m"></path><path d="M473 86h4c1 2 1 2 3 2h-2l-11 1-21-1h0c1-1 3 0 4 0 7-1 15-1 22-1l1-1z" class="AB"></path><path d="M473 86h4c1 2 1 2 3 2h-2-1c-2 0-4 0-5-1l1-1z" class="Q"></path><path d="M569 160v1h2l3-1 1 1h1l-1 1c2 0 4 0 6 2v2c-2 0-4 0-6 2-1 1-1 2-1 3h-1 0c-1 1-2 1-2 2-1-1-1 0-1-1v3c-1-2-1-3-2-5 1-3-1-5-1-8v-2s0 1 1 1l1-1z" class="q"></path><path d="M574 160l1 1h1l-1 1c-1 0-2 1-3 2h-1v-3l3-1z" class="Q"></path><path d="M569 160v1h2v3h1 0c-1 1-1 2-1 3-1 1 0 3-1 5v3c-1-2-1-3-2-5 1-3-1-5-1-8v-2s0 1 1 1l1-1z" class="S"></path><path d="M569 160v1c0 1 1 3 0 4h0l-2-3v-2s0 1 1 1l1-1z" class="AB"></path><path d="M565 272c0-1 1-3 2-5 0-1 0-1 1-1 1 1 1 2 2 4v2s-1 1-1 2c1 1 1 3 2 4v3c0 1 0 2-1 3s-1 2-2 3-2 2-2 3h-1c0-2-1-5 0-7h0c0-3 0-6 1-8 0-1-1-2-1-3z" class="AK"></path><path d="M115 86l3-3c1 0 1-1 2-2v-2c0-2-2-3-2-5 1 1 2 1 4 2 1 1 2 2 3 4 2 0 2-1 4 0v1c0 1-1 1-1 1l-2 2-1 1h-2-2l2 2c-2 0-5 0-8 1-7 1-14 5-21 9v-1c2-1 5-3 8-4 3-2 7-3 10-4l1-1h1l1-1z" class="AB"></path><path d="M122 76c1 1 2 2 3 4 2 0 2-1 4 0v1c0 1-1 1-1 1l-2 2-1 1h-2-2c-1 0-1 0-2-1h2 2v-1-1l1-1h0l-2-2v-1l-1-1 1-1z" class="d"></path><path d="M156 88c2 0 4-1 6-1 2 1 4 1 5 1h1l-2 2-1 2c0 1-1 2-1 3v1l-5-1-1-1-5-2h-1l-1-1h-1-1 0l-8-3h10c1 0 2 1 3 1l2-1z" class="i"></path><path d="M159 95l1-1c1 0 3 1 4 1v1l-5-1z" class="AP"></path><path d="M165 92c-1 0-2-1-3-1s-1-1-2-1h-1v-1h0c2 0 6 1 7 1l-1 2z" class="o"></path><path d="M313 644h17c4 0 9-1 13 0h4 0c-6 0-11 1-16 3l-9 3-3 2-2-2s-1 0-1-1h-1l2-2c-1-1-3-2-4-3z" class="d"></path><path d="M322 650l1-1c1-2 3-2 6-3 0 0 1 0 2 1l-9 3z" class="P"></path><path d="M194 240v2c0 3 0 4-2 6v4 1l-2 1v1h-1v2h-1-2l-1-1h-1l-1 1c0-3-3-2-4-4v-1c1-2 2-1 4-2h0 1 1c1-1 1-2 1-2h1l3-3c1-1 1-2 1-3h1v-1h1l1-1z" class="v"></path><path d="M192 245h0 1c-1 1-2 2-2 3s-1 2-1 2h0l-1-1c0-1 2-3 3-4h0z" class="P"></path><path d="M183 250h1v1c1 1 1 0 2 2l-1 1c0 1 1 2 1 3l-1-1h-1l-1 1c0-3-3-2-4-4v-1c1-2 2-1 4-2h0z" class="d"></path><path d="M195 362c2 3 1 7 1 10l-1 5c-1 2-3 4-4 6 0 1-1 1-1 1-2-2-1-9-1-11s0-8 2-10l1 1 2 1c0-1 1-2 1-3z" class="Ad"></path><path d="M91 138l1-1c0-2 1-3 2-5 1-4 3-8 6-11v2h1c-2 1-2 2-3 4-1 1-1 2-1 4 1 0 2 1 3 1l5 4h1c2 0 2-1 3-1h1l-2 1c-2 1-3 3-4 4 0 2 2 4 3 6l1 1c-1 0-2 0-3-1-1-2-1-5-4-6h-2c-1 0-1-1-2-2-1 0-2-1-4-2-1 3-1 5-2 7v-5z" class="Q"></path><path d="M98 134h1c1 0 5 3 6 3l-1 1-4 1c0-1-1-1-1-1l-1-1s0-1-1-1c0-1 0-1 1-2z" class="AK"></path><path d="M195 342v2 2 3c0 2 0 5 1 7v2c0 1-1 1-1 2l-1 1-1-1c-2 1-2 1-2 2h-1c-1-1-2-2-2-3 0 0-1 0-2-1l1-1v-1c-1-1-1-2-2-3-2-2-6-2-8-2h-1 0c3 0 5 0 8 1h0c2 1 3 2 5 3-1-2-2-4-3-5l1-3c1 1 2 1 3 2 1 0 1-2 2-3h1c1-1 1-1 1-2h0l1-2z" class="T"></path><path d="M187 357h1c1 0 1 0 2-2 0-1-1-2 0-4 0-1 0-1 1-1v1s1 1 1 2c0 0-1 1-1 2 0 0 1 1 1 2 1 0 1 0 1 1l2 2-1 1-1-1c-2 1-2 1-2 2h-1c-1-1-2-2-2-3 0 0-1 0-2-1l1-1z" class="S"></path><path d="M195 342v2 2 3c0 2 0 5 1 7v2c0 1-1 1-1 2l-2-2c0-1 0-1-1-1 0-1-1-2-1-2 0-1 1-2 1-2 0-1-1-2-1-2h1c1-1 1-2 1-4 1 0 1 0 2-1 0-1 0-1-1-2h0l1-2z" class="W"></path><defs><linearGradient id="C" x1="279.145" y1="86.39" x2="276.264" y2="92.481" xlink:href="#B"><stop offset="0" stop-color="#a07170"></stop><stop offset="1" stop-color="#908080"></stop></linearGradient></defs><path fill="url(#C)" d="M275 87c3 0 6 1 8-1 1-2 2-3 4-4 0 1 1 2 2 3 3 1 6 1 10 0l3-3 1 1h-1c0 1-1 2-1 3h3v1c-1 0-2 1-3 0h-1c-1 1-1 1-2 1l-4 1c-2 0-6 1-8 2-3 0-4 0-6 1v-2l-7 2-1-1c1-1 2-1 3-2s0-1 0-1v-1z"></path><path d="M280 90c4-1 9-2 13-2 2 0 3-1 5 0l-4 1c-2 0-6 1-8 2-3 0-4 0-6 1v-2z" class="AG"></path><path d="M574 173h1c0 1 1 2 1 2l2 1c1 0 0 0 0 1-1 2-3 4-3 7h-1c0 2-1 3-1 5s0 4-1 6c0 3 0 6-1 8v1 4h-1 0-1l-1 1c0-1 0-1-1-1l1-4v-3-1l1-1v-2-1h0c0-1 0-2 1-3h1v-3c0-1 0-3-1-4 0-1 0-2-1-3 1-2 2-6 3-7s2-2 2-3z" class="k"></path><path d="M571 190c0 1 0 1 1 2v-4 7c0 3 0 6-1 8v1 4h-1 0-1l-1 1c0-1 0-1-1-1l1-4v-3-1l1-1v-2-1h0c0-1 0-2 1-3h1v-3z" class="Aa"></path><path d="M569 196h0c0-1 0-2 1-3 1 3 1 5 0 8l-1-1v-3-1z" class="w"></path><path d="M569 197v3l1 1v3l1-1v1 4h-1 0-1l-1 1c0-1 0-1-1-1l1-4v-3-1l1-1v-2z" class="q"></path><path d="M568 204v1c1 0 1-1 2-1v4h0-1l-1 1c0-1 0-1-1-1l1-4z" class="AL"></path><path d="M175 281v1c1-1 2-2 3-4h0c1-1 1-1 2-1l-1 2c1 1 1 1 2 1v1h-2c1 1 3 1 4 1s2 1 3 1c-1 2-2 3-3 5 0 2 1 1 1 2v3l-1 1c-1-1 0-1-1 0l-1 1h0-3-2c-1-1 0-2 0-3-1 0-1-1-1-1v-10z" class="AK"></path><path d="M175 281v1c1-1 2-2 3-4h0c1-1 1-1 2-1l-1 2c1 1 1 1 2 1v1h-2l-2 2c-1 4-1 8-1 12-1-1 0-2 0-3-1 0-1-1-1-1v-10z" class="j"></path><path d="M442 258h2v1l1 1c0 3-2 6-3 8-2 4-5 8-6 12-1 2-3 3-4 5-1 1-2 3-3 4-2 1-3 2-4 4v1 1 1l-4 2c-1 1-2 1-3 1v-1c3-2 5-6 8-9 6-9 14-21 16-31z" class="I"></path><path d="M425 293v1 1 1l-4 2h0c0-2 3-4 4-5z" class="J"></path><path d="M599 132l1 1c0 2 0 4-1 6-1 3-2 4-2 7-5 5-10 8-18 7-3 0-6-2-8-4v-1-2h0v1l2 1c6 1 9 3 15 0 6-4 9-10 11-16z" class="y"></path><path d="M371 643l16-1-2 1h0c-1-1-3 0-4 0h-4s-1 1-2 1h-5 0s-1 0-1 1h1-1l1 1-1 2 1 1-1 1h-1v3l-2-2h-2v-1h-1c0-1-1-2-2-2h-2-1l1 2h-1l-1-1c-1-1-2-1-3-1l-2 1c-2-1-3-3-5-4v-1h0v-1h14 10z" class="W"></path><path d="M369 645h1-1l1 1-1 2c-1-1-2-1-2-2s1-1 2-1z" class="T"></path><path d="M358 646h4c2 0 3 0 4 2l2 2v3l-2-2h-2v-1h-1c0-1-1-2-2-2v-1h-2-1v-1z" class="h"></path><path d="M347 645h3c1 1 3 1 4 1 1-1 1-1 2-1v1h2v1h1 2v1h-2-1l1 2h-1l-1-1c-1-1-2-1-3-1l-2 1c-2-1-3-3-5-4z" class="P"></path><path d="M381 620c1-1 1-2 1-3 1-1 1-3 1-4h0 1c2 0 4-1 5-2h0c1-1 2-1 3-1l1 1c1-2 2-4 3-7-2-2-3-7-2-10 0-2 3-3 5-4 1 0 2 0 3-1 0-1 0-2-1-3l-2-5h0c1 1 3 5 5 5 1-1 2-1 4 0l6 3s1 0 2-1h0c-1 2-1 3 0 4-1 0-2 1-3 0v-1c-2-2-5-3-7-5l-4 4c-2 1-7 3-8 6 0 2 1 5 2 7h2v1h-1c0 1-1 2-1 3v1c1 1-1 1 0 3l2 1h-2c-2 0-4 3-5 4v-1l-3-2c-2 1-2 2-3 4h0c0 1 0 1-1 2v4h0c1 0 2-1 3-1 0-1 0-1 1-2h0v1c0 1 0 1-1 1-1 1-3 4-4 6h-1 0c0-3 0-5-1-8z" class="Q"></path><path d="M150 286c0-2-1-3-2-4-2-1-7 2-10 2h0c-2 3-3 7-4 11-1 1-2 3-2 4v2c1 2 2 3 2 5 1 6 2 13 2 19-3 4-7 6-11 10l1-2c2-3 6-5 8-8 0-4-1-22-3-25-1 0-1 0-1-1 2-4 5-11 5-16-1 0-2-1-3-1v-1c2 1 3 1 5 0 1-1 3 1 4 0 3-1 5-3 7-4h2c0 2 1 3 1 5h1l-1 2c0 1 1 1 1 2h0l-1 1-1-1z" class="AZ"></path><path d="M124 645h0c1 0 2 0 2-1h1c0 1 1 1 2 1h4l1 1c1 0 2-1 3 0l1 1h0c0 1-1 2-1 3h0v1h-1-2l3 3c-2 1-3 3-6 3h-2c-1-1-2-2-3-2 0 1 0 1-1 2h-1 0l1 5h-2v-6l-2-1v-1c1-1 1-1 2-1v-1c1-1 2-1 2-2v-2l1-2h0l-2-1z" class="Q"></path><path d="M134 646c1 0 2-1 3 0l1 1h0c0 1-1 2-1 3h0v1h-1-2l-1-1v-1s-1-1-1-2c-1 0 0-1 1-2l1 1z" class="o"></path><path d="M134 646c1 0 2-1 3 0l1 1h0c0 1-1 2-1 3h0c-1-1-2-3-3-4z" class="j"></path><path d="M126 655v-1c0-1 0-1 1-1 1-1 2-3 3-4 1 0 2 0 3 1l1 1 3 3c-2 1-3 3-6 3h-2c-1-1-2-2-3-2z" class="AF"></path><path d="M331 158h1l2 1-2 3c0 1 0 1 1 1h2c1 0 1 0 2 1v-1c2 1 3 0 5 0 3-1 5-1 7-1 1-1 1-2 1-3l1 1v2c1 1 3 0 4 1h-4v5 1c-1 2 0 7-1 8-1-2 0-12 0-14-4 0-7 1-10 2-1 0-3 0-4 1s-2 2-2 3c0 4 1 9 2 12 1 2 2 6 4 8 2 1 4-1 7-1 2 0 4-1 6-1 3-1 5-4 8-5h0 4 0c-1 0-3 0-4 1-2 1-7 6-9 5-4-2-11 7-14 1-3-5-4-10-5-15v-6c-1-2-2-4-3-5h-2v-2s-1-1-1-2c0 0 0-1 1-1h3 0z" class="AE"></path><path d="M331 158h1c0 2 0 2-2 3 0 1-1 0-2 0 0 0-1-1-1-2 0 0 0-1 1-1h3 0z" class="AK"></path><path d="M464 374v1l2 2h2 3 0c-3 2-4 5-6 8h0c2 1 4 4 5 6h2l-1 1c1 2 3 2 4 3h0c-1 0-2 0-3-1-1 0-2-1-2-2h0c-1-2-3-3-4-4l-2-2c-1 1-4 2-4 3 0 3 1 7 1 11 0 3-2 8 0 10l7 1 4 4v2-1c-2-2-3-3-6-3-2-1-5-1-7 0v-13-10c0-1 1-3 0-4v-5-4l1 1s1-1 2-1v-1l2-2z" class="Q"></path><path d="M464 374v1l-1 2h1c1 1 2 1 2 2 0 2-4 6-5 8l-1 1v-7c0-1 1-1 1-2-1 0-1 0-1-1 0 0 1-1 2-1v-1l2-2z" class="AF"></path><path d="M538 355c1-2 2-5 3-7l3-9c1-2 1-3 3-4-1 6-4 11-7 17l-19 40-2-1 2-3-1-1 2-5c1-3 3-5 4-8 0-1 1-2 2-4s2-5 3-7v2c1 0 1-1 2-2v-1l1-1v-1h1v-1l1-1v-1-1l1-1h1z" class="AJ"></path><path d="M526 374c1 1 0 2 0 4-2 3-3 7-5 10l-1-1 2-5c1-3 3-5 4-8z" class="x"></path><path d="M531 363v2c1 0 1-1 2-2v-1l1-1v-1h1v-1l1-1v-1-1l1-1h1l-11 22c-1 0 0 0-1 1 0-2 1-3 0-4 0-1 1-2 2-4s2-5 3-7z" class="S"></path><path d="M178 644c5 0 12-1 17 0l-2 4h0v1 1h0c1-1 1-1 2-1l1 1h-1v2 1 1l-2 1h-2l-1-2-2 2-2-1c-2 1-3 2-5 1h-1c1-1 0-2 0-3l-1-1c1-1 1-2 1-3 0 0-1-1-1-2h0c-1-1-1-1-1-2z" class="AK"></path><path d="M179 646c1 1 3 2 5 3l-1 1h0-1c-1 1-1 1-1 2h-1l-1-1c1-1 1-2 1-3 0 0-1-1-1-2h0z" class="AA"></path><path d="M184 649h1 1 0c1 1 1 1 2 1h0c0 1 0 2-1 2 1 1 2 1 3 1h0l-2 2-2-1-1-1c0-2-1-2-2-3h0l1-1z" class="T"></path><path d="M181 652c0-1 0-1 1-2h1c1 1 2 1 2 3l1 1c-2 1-3 2-5 1h-1c1-1 0-2 0-3h1z" class="d"></path><path d="M181 652c0-1 0-1 1-2h1c1 1 2 1 2 3h0c-2 0-3 0-4-1z" class="AM"></path><path d="M193 648h0v1 1h0c1-1 1-1 2-1l1 1h-1v2 1 1l-2 1h-2l-1-2h0c-1 0-2 0-3-1 1 0 1-1 1-2h0v-1l2 1 3-2z" class="T"></path><path d="M188 650v-1l2 1c1 1 1 1 1 2l1 1c0 1 0 1-1 2l-1-2h0c-1 0-2 0-3-1 1 0 1-1 1-2h0z" class="p"></path><path d="M574 250h1c3-1 4-2 7 0 1 0 1 0 1 1h-1l3 3c0 1-1 3-1 4-1 0-2-2-3-2-3 0-5 3-7 4v1h-1l-2 3c-1 1-2 1-2 1-2 0-2 0-3-1-1 0-1-1-1-2v-1c0-1 0-1-1-2 1 0 1-1 2-1 0-1 0-1-1-1l1-1 1 1 1-1 2-2 2-1h0 1c1-1 1-2 1-3z" class="AL"></path><path d="M566 264c0-1 1-1 2-2h0c1 1 1 2 1 3-2 0-2 0-3-1z" class="q"></path><path d="M574 250h1c3-1 4-2 7 0 1 0 1 0 1 1h-1c-2-1-4 0-5 0-1 1-2 2-4 3-3 2-5 4-8 7 0-1 0-1-1-2 1 0 1-1 2-1 0-1 0-1-1-1l1-1 1 1 1-1 2-2 2-1h0 1c1-1 1-2 1-3z" class="T"></path><path d="M509 405h0v1h1l-1 3v2c-6 10-13 21-21 31-1 2-3 5-5 7l-1-1 1-1 5-7c1-1 1-2 0-4l19-30 2-1z" class="S"></path><path d="M509 405v1h1l-1 3h-2c0-1 1-3 2-4z" class="x"></path><path d="M360 311h-1-1c0-1 0-1 1-1 1-1 2-2 3-2 1 1 2 1 3 2 3 2 5 5 5 9 1 4-1 8-2 12h-1l-2 2h-1-2l1-2c-2 0-3 0-5 1v1l-2 1c0-1 0-1 1-2h-5c0-1 0-1-1-2h1c-1-1-2-1-2-2l-1-1c1 0 2 0 2 1h2 5l3-2c3-1 4-5 4-8 1-1 1-2 0-2v-1c0-1 0-2-1-3-1-2-2-2-4-2v1z" class="J"></path><path d="M365 328v1h1l1 1v1l-2 2h-1-2l1-2s1-2 2-3z" class="D"></path><path d="M349 327c1 0 2 0 2 1h2c1 0 2 0 3 1 1 0 1 0 2 1 0 0 1-1 1 0l-2 2h-5c0-1 0-1-1-2h1c-1-1-2-1-2-2l-1-1z" class="X"></path><path d="M365 310c3 2 5 5 5 9 1 4-1 8-2 12h-1v-1l-1-1h-1v-1c1-2 2-5 2-8 0-2 0-6-1-8-1-1-1-1-1-2z" class="r"></path><path d="M367 320h0 1c1 1 1 2 0 3v1c-1 0-1 0-1 1l1 1c-1 1-2 2-2 3h-1v-1c1-2 2-5 2-8z" class="C"></path><path d="M416 632c1-1 1-1 2-1 0 1-2 2-4 3v1c-3 3-7 5-11 6-1 1-5 2-6 3h-2c-1 1-1 2-2 2-1-1-1-1-2-1s-2 0-3-1v1c-1 1-1 1-1 2-1 1-1 1-2 1v-2h-2l-1 1c-3-1-6 0-8 1h-1-3v1l-1-1 1-2-1-1h1-1c0-1 1-1 1-1h0 5c1 0 2-1 2-1h4c1 0 3-1 4 0h0l2-1c10-2 20-4 29-10z" class="AR"></path><path d="M304 219c1-2 1-4 2-6v10l3 8c3-1 6-1 10-1-1 1-1 1-1 2h-1-2-3c-1 0-4 2-4 4 0 1 1 3 1 4v3 1h0v2 1h1v1c-1 1-1 2-1 3v5l1 2h-2v1h0v1c0 1-1 1-1 2-1 0-1 0-2 1l-1 1 1-24-1-21z" class="T"></path><path d="M305 240h0c1 3 0 7 0 9v3h1 1v-1-1-1c1-2 0-5 1-7h0c1 1 1 1 1 2v2 1h1v1c-1 1-1 2-1 3v5l1 2h-2v1h0v1c0 1-1 1-1 2-1 0-1 0-2 1l-1 1 1-24z" class="AJ"></path><path d="M309 247h1v1c-1 1-1 2-1 3v5l1 2h-2v-1s0-1-1-1c1 0 1-1 1-1l-1-1c0-2 1-5 2-7z" class="AE"></path><defs><linearGradient id="D" x1="455.324" y1="242.004" x2="453.215" y2="241.949" xlink:href="#B"><stop offset="0" stop-color="#0a0b09"></stop><stop offset="1" stop-color="#2a1717"></stop></linearGradient></defs><path fill="url(#D)" d="M453 228c1 1 1 1 1 3h0l1 2c0 1 0 3 1 5v1c0 2 1 9 0 11v5h-1c0 3-1 6 0 9 1 2 0 6 0 8v5c0-1 0-2-1-3 0 1 0 1-1 2h0v-5l1-10-2 1v5l-1-1h0l-1 3v-2h-1v-1-1h-1l-1-1 2-2c-1-3 0-6 0-9v-1l1-1h0c0 1 1 1 1 1v-5l2-11v-8z"></path><path d="M452 259l1-4h1v6h0l-2 1v-3z" class="F"></path><path d="M454 261h0v4c1 3 0 5 1 7v5c0-1 0-2-1-3 0 1 0 1-1 2h0v-5l1-10z" class="G"></path><path d="M453 236c1 6 1 13 1 19h-1l-1 4v3 5l-1-1h0l-1 3v-2h-1v-1-1h-1l-1-1 2-2c-1-3 0-6 0-9v-1l1-1h0c0 1 1 1 1 1v-5l2-11z" class="E"></path><path d="M451 258v-1-4c1 1 1 1 1 2v4 3 5l-1-1h0l-1 3v-2l1-9z" class="G"></path><path d="M449 252c2 1 2 4 2 6l-1 9h-1v-1-1h-1l-1-1 2-2c-1-3 0-6 0-9v-1z" class="e"></path><path d="M192 327h1v1l1 1c-1 1-1 3-2 5 1 1 0 2 1 3s1 2 1 4v3h0c0 1 0 1-1 2h-1c-1 1-1 3-2 3-1-1-2-1-3-2l-1 3v-1c-3-3-2-6-2-10l-3-1v-4h0c1-1 2-1 4-2h1v1h1l1-1v-2c2-1 3-1 4-2v-1z" class="h"></path><path d="M193 327v1l1 1c-1 1-1 3-2 5-1-1-1-2-1-4l2-2v-1z" class="AU"></path><path d="M187 333h1c0 2-2 3-3 4h-2v-2c2 0 3-1 4-2z" class="T"></path><path d="M181 334c1-1 2-1 4-2h1v1h1 0c-1 1-2 2-4 2v2c0 1 0 1 1 2l-3-1v-4h0z" class="Q"></path><path d="M187 347v-1c-1-1-1-1-1-2 1-1 1-2 3-2v1c1 0 1-1 1-2 2-1 2-1 4 0v3h0c0 1 0 1-1 2h-1c-1 1-1 3-2 3-1-1-2-1-3-2z" class="Ad"></path><path d="M345 338l1-1c1-4-1-6-3-8-1-3-2-7-1-10 1-2 3-5 4-5 2-1 3-1 4 0 1 0 1 1 1 2 0 0-1 0-2 1s-1 2-2 3l1 2c-1 1-1 1-1 3 1 0 1 2 2 2l1 1c0 1 1 1 2 2h-1c1 1 1 1 1 2h5c-1 1-1 1-1 2h0l-2 2h-1c-1 1-2 1-3 2v1c-1 0-2 1-2 1h0c0 1-1 1-1 1s0-1-1-1h0c-1 1-2 1-3 1h-1l-5 2c1-2 4-3 6-4l2-1z" class="F"></path><path d="M350 314c1 0 1 1 1 2 0 0-1 0-2 1s-1 2-2 3v2c-1 1-1 2-1 3v1 1h1v3h0c-1-1-1-1-1-2-2-2-3-5-2-8 1-2 2-4 4-5l1-1h1z" class="C"></path><path d="M347 327h-1v-1-1c0-1 0-2 1-3v-2l1 2c-1 1-1 1-1 3 1 0 1 2 2 2l1 1c0 1 1 1 2 2h-1c1 1 1 1 1 2h5c-1 1-1 1-1 2h0l-2 2h-1c-1 1-2 1-3 2v1c-1 0-2 1-2 1h0c0 1-1 1-1 1s0-1-1-1h0c-1 1-2 1-3 1h-1l-5 2c1-2 4-3 6-4l2-1 3-3c2-1 1-3 1-5-1-1-1-2-2-3z" class="D"></path><path d="M352 332h5c-1 1-1 1-1 2h0l-2 2h-1c-1 1-2 1-3 2v1c-1 0-2 1-2 1h0c0 1-1 1-1 1s0-1-1-1h0c-1 1-2 1-3 1h-1c3-2 6-3 8-5 1-1 2-3 2-4z" class="R"></path><path d="M182 577h1v-1l1-1c1 1 0 2 1 3h1c1 1 1 1 2 1 0 2-1 2-1 4-1 1 0 2-1 3h0c0 1 0 1-1 2-1 2-1 4-2 7v1c-1 1-2 2-2 4h0c0 4-3 4-4 7h-1l1-2-1-1c1-1 1-1 1-2s0-1 1-2v-1c0-1 0-3 1-4 0-1 0-2-1-2v-1l1-1c-1-1-1 0-1-1h-2-2v-3c1 0 1-1 1-2 1-1 3-2 4-4 0-1 2-3 2-4h1z" class="P"></path><path d="M179 591h0c1-1 2-1 2-2h1v2h0v3h0l-4-2 1-1z" class="Q"></path><path d="M174 587c1 0 1-1 1-2 1-1 3-2 4-4 0-1 2-3 2-4h1c1 2 1 5 2 7-2 1-3 2-4 4-1 0-1 1-2 2h-2-2v-3z" class="Ad"></path><path d="M183 268c1 0 1 1 2 1h2 0c2 0 1-1 3-1 0 2-1 3 0 4 1-1 1-1 2-1v-1l1 1h1c1 2 0 4 1 6v1l-1 4 1 1c-1-1-1-1-2-1v1s0 1-1 1c1 1 1 2 1 3h-1v-1l-3-3h-2-1c-1 0-2-1-3-1s-3 0-4-1h2v-1c-1 0-1 0-2-1l1-2c2-1 2-2 3-3v-1h0-2v-1h1 1c1 0 1 0 1-1h0c-1 0-1-1-2-1h0c0-1 1-1 1-1v-1z" class="m"></path><path d="M194 280h0v2l1 1c-1-1-1-1-2-1v1s0 1-1 1v-1l-1-1c0-1 0-2 1-3l1 1h1z" class="v"></path><path d="M187 269h0c2 0 1-1 3-1 0 2-1 3 0 4 1-1 1-1 2-1v-1l1 1h1c1 2 0 4 1 6v1l-1 4v-2h0c-1-2-1-2-2-3h0c0-1 0-2-1-2 0-1 0-2-1-2h-2l-1-1v-1h0c-1-1-1 0-2-1v-1h2z" class="AE"></path><path d="M193 271h1c1 2 0 4 1 6v1l-1 4v-2h0c-1-2-1-2-2-3 1-1 1-1 2-1v-1c0-1 0-1-1-1-1-1 0-2 0-3z" class="d"></path><path d="M187 272l1 1h2c1 0 1 1 1 2 0 2-1 4-3 5-1 0-2 0-3-1-1 0-1-1-1-2-1-1 0-2 1-3h1l1-2z" class="AK"></path><path d="M573 171h1v1 1h0c0 1-1 2-2 3s-2 5-3 7c1 1 1 2 1 3 1 1 1 3 1 4v3h-1c-1 1-1 2-1 3h0v1 2l-1 1s-1 0-1-1 0-1-1-1v1 1l-1 2h-1v2h-1-1v3h0v-6-5c1-4 3-10 2-14 0-1 0-1-1-2h2c1-1 1-1 1-3l1 1 1-2v-1c1-1 0-3 0-5 1 2 1 3 2 5v-3c0 1 0 0 1 1 0-1 1-1 2-2h0z" class="p"></path><path d="M568 170c1 2 1 3 2 5v-3c0 1 0 0 1 1 0-1 1-1 2-2h0v1c-2 2-3 5-4 7 0-1-1-2-1-4 1-1 0-3 0-5z" class="AL"></path><path d="M568 175c0 2 1 3 1 4-1 3-2 7-3 10v-4c0-2 1-5 1-7l1-2v-1z" class="r"></path><path d="M569 183c1 1 1 2 1 3 1 1 1 3 1 4v3h-1c-1 1-1 2-1 3h0v1 2l-1 1s-1 0-1-1 0-1-1-1v1-3c1-2 1-4 1-6l2-7z" class="q"></path><path d="M567 190c1 1 1 4 1 4 0 1 1 1 1 1v1 1 2l-1 1s-1 0-1-1 0-1-1-1v1-3c1-2 1-4 1-6z" class="k"></path><path d="M566 177l1 1c0 2-1 5-1 7v4l-1 6c0 1 0 1 1 1v3 1l-1 2h-1v2h-1-1v3h0v-6-5c1-4 3-10 2-14 0-1 0-1-1-2h2c1-1 1-1 1-3z" class="Aa"></path><path d="M565 195c0 1 0 1 1 1v3 1l-1 2h-1c0-2 0-4 1-7z" class="S"></path><path d="M399 298v-1c0-2 1-3 1-5 1-1 1-1 2-1s1 1 1 1c0 1 0 1 1 2 2 0 5 0 6 1v2c2 1 2 0 3 0l1 1c1 0 1 0 1-1v-1c1-1 1 0 2 0 1 1 1 1 1 2v1c-1 1-2 1-2 2l1 1-2 2c-1 1-2 2-3 2-1 1-2 3-3 3-1-1-2-1-3-1h0c-1-1-4-3-4-3 0-1 0-2-1-3l1-1c-1-2-1-2-2-2 0-1-1-1-1-1z" class="p"></path><path d="M400 299c1-1 1 0 2-1 1 0 0-2 2-2 1 1 2 1 3 1 1 1 1 2 2 2h1c1 0 0 1 0 2h0l1 1h0l-2 1v-1c-1-2-3-3-4-3-2 0-2 1-3 2-1-2-1-2-2-2z" class="AU"></path><path d="M402 301c1-1 1-2 3-2 1 0 3 1 4 3v1l-1 2c-1 1-1 1-2 3h0c-1-1-4-3-4-3 0-1 0-2-1-3l1-1z" class="AK"></path><path d="M410 295v2c2 1 2 0 3 0l1 1c1 0 1 0 1-1v-1c1-1 1 0 2 0 1 1 1 1 1 2v1c-1 1-2 1-2 2l1 1-2 2c-1 1-2 2-3 2-1 1-2 3-3 3-1-1-2-1-3-1 1-2 1-2 2-3l1-2 2-1h0l-1-1h0c0-1 1-2 0-2h-1c0-1 1-2 0-3v-1h1z" class="d"></path><path d="M411 302v-1h2 0v1l-1 1-1-1h0z" class="W"></path><path d="M411 302l1 1c-1 1-2 2-4 2l1-2 2-1z" class="h"></path><path d="M416 301l1 1-2 2c-1 1-2 2-3 2 0-2 3-3 4-5z" class="N"></path><path d="M565 272c0 1 1 2 1 3-1 2-1 5-1 8h0c-1 2 0 5 0 7h-1v3c0 1-1 4-1 4 0 1-1 1-2 1v1 1 1l-2-2v1 1h0v3c1 1 1 0 3 1l-1 2h-2c0 1-1 2-1 2-1 2 0 4-1 6h-1l-1-1c1-1 1-2 1-4h0l-1 1-2 6c0 2 0 4-2 6h0v-4l1-1 1-3v-4c2-4 3-8 4-12l1-3 1-1v-10c0-3-1-6 0-8 0-1-1-1-1-2l1-1c0 1 1 2 2 3 0 1 0 3 1 5 0-1 0-3 1-4 0-2 1-4 2-6z" class="AM"></path><path d="M555 311v-1c1-3 2-5 3-7 0-1 1-2 1-2h0v3c1 1 1 0 3 1l-1 2h-2c0 1-1 2-1 2-1 2 0 4-1 6h-1l-1-1c1-1 1-2 1-4h0l-1 1z" class="j"></path><path d="M565 272c0 1 1 2 1 3-1 2-1 5-1 8h0c-1 2 0 5 0 7h-1v3c0 1-1 4-1 4 0 1-1 1-2 1v1 1 1l-2-2v1c0-2 0-4 1-6 0-1 1-1 1-2 0-3 1-7 1-10 0-1 0-3 1-4 0-2 1-4 2-6z" class="Q"></path><path d="M559 299l2-4v-2h1c0 2 0 3-1 5v1 1 1l-2-2z" class="AQ"></path><path d="M562 293l1-1v-1c-1-1-1-2-1-3h1 1v2 3c0 1-1 4-1 4 0 1-1 1-2 1 1-2 1-3 1-5z" class="AB"></path><path d="M306 506c0 4 1 8 3 11h7c2 0 4 0 6-1h1 0c0-2 0-2 1-3 1 0 0 1 0 2s1 1 1 1h3v1c-1 0-2 1-3 1v2c-1 1-3 0-5 1v-2c-1-1-2-1-3-1-1 1-2 1-3 2l-1 2h-2v2c-1 2-2 4-2 7 0 0 1 1 2 1 1 2 0 6 0 8l-2 2h-1c1 1 1 2 1 3v1c-1 0-1 1-2 1v-1-3l-1-1h0c-1-4 0-9 0-14v-14c0-2-1-5 0-8z" class="T"></path><path d="M306 542s0-1 1-2c0-3-1-6 0-10h0c0-1 1-5 2-6s1 0 1-1l1-1v2c-1 2-2 4-2 7 0 0 1 1 2 1 1 2 0 6 0 8l-2 2h-1c1 1 1 2 1 3v1c-1 0-1 1-2 1v-1-3l-1-1z" class="v"></path><path d="M102 636c1 1 2 2 3 2l9 3c3 0 6 1 9 1h0c2 1 3 0 5 1h1v2c-1 0-2 0-2-1h-1c0 1-1 1-2 1h0l2 1h0l-1 2v2c0 1-1 1-2 2v1c-1 0-1 0-2 1v1l-10-7h0l-1-1v-1h-1l-2 1h0c0-1 1-2 1-3-1-1-2-1-3-2l-3-2v-3-1z" class="h"></path><path d="M119 644c1-1 1-1 3 0l2 1c-1 0-3 0-3 1-1 0-1 0-1 1 1 1 2 3 3 4h-3c-1-2-1-5-1-7z" class="Q"></path><path d="M124 645h0l2 1h0l-1 2v2c0 1-1 1-2 2v-1c-1-1-2-3-3-4 0-1 0-1 1-1 0-1 2-1 3-1z" class="AF"></path><path d="M105 638l9 3c3 0 6 1 9 1h0c2 1 3 0 5 1h1v2c-1 0-2 0-2-1h-1c0 1-1 1-2 1h0 0l-2-1c-2-1-2-1-3 0h-1-1c-2 0-3 1-4 2-1 0-1 0-2 1v-1c0-1 0-1 1-2 2 0 3 0 4-1v-1c-1 0-2 0-3 1-1 0-1 0-2-1h-1c-1 0-2-1-3-1l-1-1c-1 0-1-1-1-2z" class="T"></path><path d="M566 199v-1c1 0 1 0 1 1s1 1 1 1v1 3l-1 4c1 0 1 0 1 1l1-1h1l1 7v2c1 0 1 0 1-1l2 2 1 1h-1c-1 1-3 2-4 4-1 1-2 2-2 3-1 1-1 1-1 2-1 4-3 8-2 12v7h0l-1-1v2 1c0 1-1 3 0 4h0l-1-1v-3-4 2h0l1-24v-19-2h1l1-2v-1z" class="n"></path><path d="M566 199v-1c1 0 1 0 1 1s1 1 1 1v1c-1 0-1 1-2 2v1h0 0l-1 2c0-2 1-4 1-6v-1z" class="r"></path><path d="M566 220h0v-4l2-2v-1 7c-1 2 0 3-1 5 0 0 0 1 1 1-1 1-1 1-1 2-1-1-1-3-1-4h0v-4z" class="AN"></path><path d="M566 204v-1c1-1 1-2 2-2v3l-1 4c1 0 1 0 1 1v4 1l-2 2v4h0-1c0-2 1-4 1-5h0 1c0-2-1-2-2-4l1-1v-1-5h0z" class="AV"></path><path d="M566 204v-1c1-1 1-2 2-2v3l-1 4h-1v-4z" class="AN"></path><path d="M568 209l1-1h1l1 7v2c1 0 1 0 1-1l2 2 1 1h-1c-1 1-3 2-4 4-1 1-2 2-2 3-1 0-1-1-1-1 1-2 0-3 1-5v-7-4z" class="Ae"></path><path d="M568 220c1-1 1-2 3-2h1c1 1 1 1 2 0l1 1h-1c-1 1-3 2-4 4-1 1-2 2-2 3-1 0-1-1-1-1 1-2 0-3 1-5z" class="Aj"></path><path d="M187 190c0 1 0 1-1 2s-1 2-2 4l1 1 2 1 1-1v2h-1c-1 1-1 1-1 2 0 2 1 4 1 6-1 4-4 7-1 10 1 1 1 1 2 1s3-1 4-2c-1 1-4 3-4 4-1 1-1 1-1 2 0 0 0 1 1 1v2h0 0v3c0-2 0-5-2-7l-4-4-5-5c-2 0-4-1-5-1-3-1-6 0-8-2l1-2h1c2-1 4 0 5-1h1c1 0 3 0 5-1h1 3l1-1v-4h-3 0-1c1-1 3-1 5-2l1-8c0 1 1 1 1 1l2-1z" class="h"></path><path d="M177 212c1 0 3 1 5 0 0 1 1 1 1 2l-1 1v1 1l-5-5z" class="P"></path><path d="M154 618l2-3-4-6c2 1 4 4 5 6 0 2-2 5-1 6h2l1-1 2 2h-1l1 1-1 1-1 1-1 1c0 1 1 1 1 2-1 1-1 1-2 1-3 2-7 3-10 4l-3 1h0c-2 0-3-1-5 0h-1s-1 0-1 1h-3-1l1-1v-1l1-1h0c2 0 2-1 3-3-1 0-1-1-2-1h0v-1h2 1c-2-2-2-4-4-6h0 0c1 0 2 1 3 2s2 1 3 1v1l-1 1v1h1c0-1 1-1 2-1v-1l2 1h0v-2h1l1 1c1 0 2-1 3-2 1 0 1-1 2-1l1-1c1-1 1-2 1-3z" class="h"></path><path d="M159 620l2 2h-1l1 1-1 1-1 1c-1-1-1-1-2-1h0-1l-1-1 1-1h1-1c1 0 1 0 1-1h1l1-1z" class="v"></path><path d="M160 622l1 1-1 1-1 1c-1-1-1-1-2-1l1-2 1 1 1-1z" class="P"></path><path d="M144 634c1-1 2-2 3-2 0-1 1-2 2-2s1-1 2-1c0-1 1-1 2-1 1 1 0 1 1 2 1-1 1-1 2-1h1c-3 2-7 3-10 4l-3 1z" class="v"></path><path d="M299 644c4 0 10-1 14 0 1 1 3 2 4 3l-2 2-3 4-4 3c-2 1-3 1-5 1h-1 0c-1 0-2 0-2 1l-1-1c-1-2-2-3-4-4l-4-3 8-5v-1z" class="Ad"></path><defs><linearGradient id="E" x1="272.359" y1="90.16" x2="246.344" y2="88.508" xlink:href="#B"><stop offset="0" stop-color="#b9a6a7"></stop><stop offset="1" stop-color="#cdcacb"></stop></linearGradient></defs><path fill="url(#E)" d="M255 86h1l19 1v1s1 0 0 1-2 1-3 2l1 1 7-2v2l-14 4c-1 1-1 2-2 3h-1l-1-1h0l-2 2-1-1c-1 1-2 1-3 1h-3c2-2 2-3 3-5v-1l-1-1v-1c-1-1-2-2-4-2-1-1-3-1-5-2h-2l-2-1c2 0 5 0 7-1h6z"></path><path d="M259 90v1c-1 1-1 2-2 2h-1v-1c1-1 2-1 3-2z" class="o"></path><path d="M259 90h4v1h-1-3v-1z" class="X"></path><path d="M263 91h2c1 0 2-1 3-1 1-1 5-1 6-2h1s1 0 0 1-2 1-3 2c-4 0-8 0-12 2l2-2h1z" class="R"></path><path d="M260 93c4-2 8-2 12-2l1 1-9 3h-4 0l-1-1 1-1z" class="J"></path><path d="M259 91h3l-2 2-1 1 1 1h0 4l-8 5h-3c2-2 2-3 3-5v-1l1-1c1 0 1-1 2-2z" class="AH"></path><path d="M259 91h3l-2 2h-2c0 2 0 2-1 3h-1v-1-1l1-1c1 0 1-1 2-2z" class="l"></path><path d="M273 92l7-2v2l-14 4c-1 1-1 2-2 3h-1l-1-1h0l-2 2-1-1c-1 1-2 1-3 1l8-5 9-3z" class="o"></path><path d="M259 99c2-2 5-2 7-3-1 1-1 2-2 3h-1l-1-1h0l-2 2-1-1z" class="i"></path><path d="M536 344l1 1h0 1 1l-1 3-4 9c-1 1-3 5-3 6-1 2-2 5-3 7s-2 3-2 4c-1 3-3 5-4 8l-1-1-4 8c0 1-1 2-1 3h-1c-1 1-2 2-2 4v2c-2 2-3 4-4 7l-2 1 18-46 1 1v-1l-1 6v1c2-4 3-7 5-10l1-1 2-6 3-6z" class="W"></path><path d="M525 366v1c2-4 3-7 5-10 0 2-2 5-2 7s-1 4-2 6l-5 11-4 8c0 1-1 2-1 3h-1c1-3 3-6 4-10l3-9c1-2 1-5 3-7z" class="q"></path><path d="M536 344l1 1h0 1 1l-1 3-4 9c-1 1-3 5-3 6-1 2-2 5-3 7s-2 3-2 4c-1 3-3 5-4 8l-1-1 5-11c1-2 2-4 2-6s2-5 2-7l1-1 2-6 3-6z" class="AA"></path><path d="M536 344l1 1h0 1 1l-1 3h-1c-1 1-1 1-1 2l-1 2-1 1c0 1 0 1-1 1l-2 2 2-6 3-6z" class="AN"></path><path d="M158 644h2c1 0 3 0 3 1v1c2 0 2 1 4 1l1-1 1 1c0 1 0 1 1 1s2 0 3 1h1 1c-1 2-2 3-2 4l-1 1v-4c-2 2-1 3-3 3-1 0-2-1-3-1h0c1 1 1 1 2 1 1 1 1 1 0 2l-1 3c-1-1-2-2-2-3-1 0-1 0-2 1h-2v1l-1 1-2 2c0 1 1 2 2 3h0l-1-1c-1 0-2-1-2-1v-1h0-4 0v-1l2-1c-1-1-1-3-1-5h-1-5c0-1 0-2 1-3 0-2 0-2 1-3l3-1c2-1 3 0 5 0h0c0-1 1-1 0-2z" class="h"></path><path d="M158 644h2c1 0 3 0 3 1v1c-1 0-3-1-5 1v-1c0-1 1-1 0-2z" class="AR"></path><path d="M153 646c2-1 3 0 5 0h0v1 1h-1c0 1 0 1-1 2l1 1h1v1h-1l-1 1-1-1c0-2-1-4-2-6z" class="m"></path><path d="M153 646c1 2 2 4 2 6l1 1v2c-1 1-1 2 0 3h-1c-1-1-1-3-1-5h-1-5c0-1 0-2 1-3 0-2 0-2 1-3l3-1z" class="AF"></path><path d="M149 650c1 0 3 1 4 3h0-5c0-1 0-2 1-3z" class="AQ"></path><path d="M387 324c2 1 9-5 12-6-1 1-1 2-2 2-1 1-2 1-2 2h0c-2 2-5 3-7 6l-5 3-1 1c-1 0-3 1-3 1-5 3-8 5-13 4h-2-1c-2 1-4 1-6 1-3 0-6 2-9 2h0s1-1 2-1v-1c1-1 2-1 3-2h1l2-2h0l2-1v-1c2-1 3-1 5-1l-1 2h2 1l2-2h1 0l2-2 3-3 1-1c1-1 1-1 2-1h2c1 0 2 1 3 1 0 1 1 1 1 2l5-3z" class="M"></path><path d="M373 326l1-1 1 2h1c1 0 1 1 1 1-1 1-1 2-2 2h-1l-1-1v1c-1-2 0-2 0-3v-1z" class="AF"></path><path d="M365 333h1c1-1 2-1 3-1h1c-2 2-5 2-7 3-2 0-3 2-6 2h-2c3-1 5-2 7-4h2 1z" class="L"></path><path d="M374 325c1-1 1-1 2-1h2c1 0 2 1 3 1 0 1 1 1 1 2-1 1-1 1-2 1h-1l-1 1-1-1s0-1-1-1h-1l-1-2z" class="AQ"></path><path d="M356 334l2-1v-1c2-1 3-1 5-1l-1 2c-2 2-4 3-7 4l-5 2v-1c1-1 2-1 3-2h1l2-2h0z" class="s"></path><path d="M363 337c4-2 8-1 12-5 1-1 3 0 4-1h1 3l-1 1c-1 0-3 1-3 1-5 3-8 5-13 4h-2-1z" class="J"></path><path d="M197 524v-1l1 1c-1 6-1 13 0 19 0 3 1 6 2 8-1 2-1 4-1 6-1-3 0-7-2-9l1 8c0 3 0 7-1 10h0 0v-1c0-3 0-8-2-10l-1-3h-1v2h-1c0 1 1 2 1 3h0c0 1 0 1 1 1l-1 1c-2-2-1-5-1-6-1-1-1-1-1-2h0 0c0-2-1-3-1-4l1 1 1-1-1-1s-1-1-1-2h0-1v-3l-2-1h-1-1l1-1-1-1c1-1 2-1 2-2v-1h-2c1 0 2-1 2-2 0-2 1-4 2-5h1c1 0 1 1 2 1h0 2v-2c1 0 2-1 2-2l1-1z" class="S"></path><path d="M196 529v1h0c0 1-1 2-1 3-1 2-2 5-1 7l-1 1v-4h0c-1 1-1 1-1 2v1c-1 1-1 0-1 1s-1 1-1 2h-1c0-1 0-1 1-2 1-2 1-4 1-6 1 0 1 0 2-1l-1-1 1-2s1 0 1-1l1-1h1z" class="x"></path><path d="M190 544h4 1l1 1c-1 0-2 2-2 3l1 1c0 1-1 2-1 3h-1v2h-1c0 1 1 2 1 3h0c0 1 0 1 1 1l-1 1c-2-2-1-5-1-6-1-1-1-1-1-2h0 0c0-2-1-3-1-4l1 1 1-1-1-1s-1-1-1-2h0z" class="v"></path><path d="M194 544h1l1 1c-1 0-2 2-2 3l1 1c0 1-1 2-1 3h-1v2h-1c0-4 1-7 2-10z" class="AR"></path><path d="M185 535c1 0 2-1 2-2 0-2 1-4 2-5h1c1 0 1 1 2 1h0 2 0v1c0 1-1 1-1 1-1 1-1 2-2 2-1 1-1 2-2 3l-3 3-1-1c1-1 2-1 2-2v-1h-2z" class="m"></path><path d="M187 535c1-1 2-2 3-2h1v-1h0c0-1 0-2 1-2h1l1-1v1c0 1-1 1-1 1-1 1-1 2-2 2-1 1-1 2-2 3l-3 3-1-1c1-1 2-1 2-2v-1z" class="h"></path><path d="M197 524v-1l1 1c-1 6-1 13 0 19 0 3 1 6 2 8-1 2-1 4-1 6-1-3 0-7-2-9v-1s0-1-1-1v-2c-1 0-2-1-2-1h-2c0-1 1-1 1-2l1-1c-1-2 0-5 1-7 0-1 1-2 1-3h0v-1h-1l-1 1v-1h0v-2c1 0 2-1 2-2l1-1z" class="k"></path><path d="M194 527c1 0 2-1 2-2v3 1h-1l-1 1v-1h0v-2z" class="W"></path><path d="M196 530v3c0 2 0 3 1 6h0v2c-1 0-1 0-1 1v2c-1 0-2-1-2-1h-2c0-1 1-1 1-2l1-1c-1-2 0-5 1-7 0-1 1-2 1-3h0z" class="q"></path><path d="M168 594l1-1c1 0 2-1 3 0 1 0 1 0 2-1h0l1 1h3c1 0 1 1 1 2-1 1-1 3-1 4v1c-1 1-1 1-1 2s0 1-1 2l1 1-1 2c-1 2-4 6-6 8h-1 0 0c-2 2-4 3-6 3v-1c-1-2 0-4 1-6l-1-1c-1-1 0-4 0-6h1v-2l1-1v-2h1l-1-2h0c1 0 1-1 1-1 1-1 1-2 2-2z" class="AK"></path><path d="M178 593c1 0 1 1 1 2h-2 0v-1c-1 0-1-1-2-1h3z" class="AF"></path><path d="M177 595h0 2c-1 1-1 3-1 4v1c-1 1-1 1-1 2s0 1-1 2l-1-1 1-4c-1 0-1 0-1-1l-1 1h-1v-2c1 0 1-1 1-1 1 0 2 0 3-1z" class="o"></path><path d="M163 604h1c1 2 2 4 4 7 0 1 1 2 1 4h0 0c-1-1-2-4-4-5l-1 1-1-1c-1-1 0-4 0-6z" class="Q"></path><path d="M164 611l1-1c2 1 3 4 4 5-2 2-4 3-6 3v-1c-1-2 0-4 1-6z" class="AK"></path><path d="M563 247v-2 4 3l1 1h0c-1-1 0-3 0-4v-1-2l1 1c-1 3 0 6 0 8v1h1l-1 1c1 0 1 0 1 1-1 0-1 1-2 1 1 1 1 1 1 2v1c0 1 0 2 1 2 1 1 1 1 3 1 0 0 1 0 2-1l2-3h1c-1 2-1 4-1 7v3h0l-2 1h-1v-2c-1-2-1-3-2-4-1 0-1 0-1 1-1 2-2 4-2 5-1 2-2 4-2 6-1 1-1 3-1 4-1-2-1-4-1-5-1-1-2-2-2-3l-1 1c-1-1-2-2-2-4v-3h0v-2c-1-2-1-2-3-3v-1-4h1l1 1h1v-6l1 1h0c1-1 1-1 2-1 1 1 1 2 1 3l2-10 1 1h0z" class="p"></path><path d="M570 270l2-2h1v3h0l-2 1h-1v-2z" class="h"></path><path d="M561 266v-1 12c-1-1-2-2-2-3h-1l3-3v-5z" class="x"></path><path d="M561 266v5l-3 3c0-1-1-1-1-2l1-2c1-1 1-2 2-3 0-1 0-1 1-1z" class="k"></path><path d="M559 261l1 1v-1s1 0 2-1l-1 5v1c-1 0-1 0-1 1-1 1-1 2-2 3 0-3 0-6 1-9h0z" class="AO"></path><path d="M563 247h0l-1 13c-1 1-2 1-2 1v1l-1-1h0 0c0-2 0-3-1-4h-1v-3h0c1-1 1-1 2-1 1 1 1 2 1 3l2-10 1 1z" class="q"></path><path d="M559 253c1 1 1 2 1 3 0 2 0 3-1 5h0 0c0-2 0-3-1-4h-1v-3h0c1-1 1-1 2-1z" class="S"></path><path d="M556 253l1 1v3h1c1 1 1 2 1 4h0c-1 3-1 6-1 9l-1 2c0 1 1 1 1 2h1l-1 1c-1-1-2-2-2-4v-3h0v-2c-1-2-1-2-3-3v-1-4h1l1 1h1v-6z" class="AA"></path><path d="M556 253l1 1v3c0 4 0 7-1 11h0v-2-5-2-6z" class="q"></path><path d="M554 258l1 1h1v2 5c-1-2-1-2-3-3v-1-4h1z" class="Ab"></path><path d="M555 259h1v2h-1v-2z" class="AS"></path><defs><linearGradient id="F" x1="539.326" y1="331.705" x2="546.126" y2="334.867" xlink:href="#B"><stop offset="0" stop-color="#521410"></stop><stop offset="1" stop-color="#811614"></stop></linearGradient></defs><path fill="url(#F)" d="M552 302c1-1 1-2 2-3h1v3c0 1-1 0-1 1h1c0-2 1-3 2-4-1 4-2 8-4 12v4l-1 3-1 1v4l-2 7h0c0 1-1 4-2 5-2 1-2 2-3 4l-3 9c-1 2-2 5-3 7h-1l-1 1v1 1l-1 1v1h-1v1l-1 1v1c-1 1-1 2-2 2v-2c0-1 2-5 3-6l4-9 1-3h-1-1 0l-1-1c2-4 4-9 6-14v-1c0-2 0-3 1-4s1-1 1-2l2 1v-2h0c1-1 2-2 2-4 0 0 0-1 1-1 0-2 0-3 1-4 0-3 2-4 1-6 0-1 1-2 1-2 1-1 0-2 0-3z"></path><path d="M544 323l2 1c0 1-1 1-1 2v2l-2 4c-1-1-1-1-1-2h0v-1c0-2 0-3 1-4s1-1 1-2z" class="S"></path><path d="M542 330c1-1 1-2 2-3h0l1 1-2 4c-1-1-1-1-1-2z" class="W"></path><path d="M542 330h0c0 1 0 1 1 2l-4 13h-1-1 0l-1-1c2-4 4-9 6-14z" class="AA"></path><path d="M187 549h0l1-1c-1 0 0 0 0-1s0-1 1-1l1 1c0 1 1 2 1 4h0 0c0 1 0 1 1 2 0 1-1 4 1 6l1-1c-1 0-1 0-1-1h0c0-1-1-2-1-3h1v-2h1l1 3c2 2 2 7 2 10v1h0c0 5-1 10-2 14-1 1-1 1-1 2 0 0-1 1-1 2-1 1-2 2-3 4v1h-1l-2 2h-1c0 1-1 2-1 3l-2 1c1-3 1-5 2-7 1-1 1-1 1-2h0c1-1 0-2 1-3 0-2 1-2 1-4-1 0-1 0-2-1l1-2 1-1v-1h-1l1-2v-2c1-1 1-2 1-3l1-3c0-1 0-3-1-4l1-2c0-2 0-5-1-7l-1-1c0-1 0-1-1-1z" class="h"></path><path d="M193 563h0-1v-1-1h1v2z" class="m"></path><path d="M187 576c1 0 1-1 2 0l-1 3c-1 0-1 0-2-1l1-2z" class="AZ"></path><path d="M190 580c0-1 1-4 2-5v1l-1 4h-1z" class="m"></path><path d="M188 572c1-1 1-2 2-3h1c0 2-1 2 0 4h-1 0c-1 1-1 2-1 3-1-1-1 0-2 0l1-1v-1h-1l1-2z" class="P"></path><path d="M190 580h1c1 1 2 1 3 2 0 0-1 1-1 2-1 1-2 2-3 4v-3l1-1h0v-1h-1-1v-2l1-1z" class="d"></path><path d="M187 549h0l1-1c-1 0 0 0 0-1s0-1 1-1l1 1c0 1 1 2 1 4v3c0 1 0 2 1 3-1 2-1 5-2 7 0-1 0-3-1-4l1-2c0-2 0-5-1-7l-1-1c0-1 0-1-1-1z" class="m"></path><path d="M192 554h1v-2h1l1 3c2 2 2 7 2 10v1h0c0 5-1 10-2 14-1 1-1 1-1 2-1-1-2-1-3-2l1-4c1-3 2-5 2-9 0-1 0-3-1-4v-2c1-1 1-2 1-2v-1c-1 0-1 0-1-1h0c0-1-1-2-1-3z" class="AR"></path><path d="M192 554h1v-2h1l1 3-1 1v1 2-1c-1 0-1 0-1-1h0c0-1-1-2-1-3z" class="d"></path><path d="M445 260l2-10h2v1h1l-1 1v1c0 3-1 6 0 9l-2 2 1 1h1v1 1h1v2 1l-2 10c0 1-1 2-1 3v1l-1 3h-1v-1h-1 0c-1 1-2 1-3 1h0l1 1c-1 1-2 1-3 1h0l-3 1h0l1-2h-1-2l-6 4c-1 1-2 3-3 3v-1-1c1-2 2-3 4-4 1-1 2-3 3-4 1-2 3-3 4-5 1-4 4-8 6-12 1-2 3-5 3-8z" class="e"></path><path d="M435 283c1-1 1-3 2-3s1 1 1 2c1 0 1 1 1 1l-1 1-2 1c0-1-1-1-1-2z" class="B"></path><path d="M444 274c1 1 1 1 2 1v2h-1 0 1v2l-1 1-1-1-1 1v1h-1v-2-1-1c1-2 1-2 2-3z" class="F"></path><path d="M435 283c0 1 1 1 1 2l2-1h0c0 1-1 1-1 1-1 0-2 2-3 3l-6 4c-1 1-2 3-3 3v-1c4-4 7-7 10-11z" class="O"></path><path d="M445 272h1c2-1 2-4 2-6h1v1h1v2 1-1c-1 0-1 1-1 2-1 0-1 0-1 1h0c0 2 0 3-1 4v1h-1v-2c-1 0-1 0-2-1h0v-1l1-1z" class="M"></path><path d="M445 272l2 2v2 1h-1v-2c-1 0-1 0-2-1h0v-1l1-1z" class="O"></path><path d="M441 282h4 0c1 1 1 1 2 1v1l-1 3h-1v-1h-1 0c-1 1-2 1-3 1h0l1 1c-1 1-2 1-3 1h0l-3 1h0l1-2h-1-2c1-1 2-3 3-3 0 0 1 0 1-1h0l1-1v-1h2z" class="F"></path><path d="M439 283v-1h2c0 1 0 2-1 3l-2-1h0l1-1z" class="X"></path><path d="M444 286v-1h0v-1h3l-1 3h-1v-1h-1z" class="Z"></path><path d="M445 260l2-10h2v1h1l-1 1v1c0 3-1 6 0 9l-2 2 1 1h1v1h-1c0 2 0 5-2 6h-1l-1 1v1c-1 0-1-1-2-1 0-2 0-3 1-4l-1-1c1-2 3-5 3-8z" class="I"></path><path d="M445 260l2-10h2v1s-1 0-1 1l-2 10c-1 2-2 5-3 7h0l-1-1c1-2 3-5 3-8z" class="Y"></path><path d="M449 253c0 3-1 6 0 9l-2 2 1 1h1v1h-1c0 2 0 5-2 6h-1l-1 1-1-1c1-2 3-4 3-7 2-4 2-8 3-12z" class="Z"></path><path d="M305 306h0c2 2 5 4 7 6 2 3 4 8 5 12v4l-1 1v1c2 1 2 0 3 1s1 2 1 3v2 2h1v1l4 2h-2v1 1c-1 1-3 1-4 1s-2 0-3-1l-2 1c-2-1-2-3-3-5v-5h0c-1 1-1 1-1 2h-1l-2-1c0-2 0-3-1-4h-2c-1 0-1 0-2-1 3-5 1-12 2-18 1-2 1-4 1-6z" class="h"></path><path d="M321 338v1 1 1l-2 1v-1-1l1-1 1-1z" class="T"></path><path d="M311 334c1-1 2-1 3-1h1c2 2 0 6 2 9 0 1-1 1-1 1l-2 1c-2-1-2-3-3-5v-5z" class="AK"></path><path d="M306 331h0v-5-8c-1-2-2-6 0-8l1-1c2 2 5 5 6 8v3c1 2 3 3 2 6-2 0-4 6-4 8h0c-1 1-1 1-1 2h-1l-2-1c0-2 0-3-1-4z" class="Ad"></path><path d="M336 84h1 0c6 3 15 1 22 2-9 2-17 3-25 4-9 1-18 3-26 5l-3 1c-1 0-1-1-2-1-4 1-8 3-13 3h0l2-2h-1l-1 1c-2 0-5 0-7 1l-2-1 3-2 3-1 6-2v-1h-2c-2 1-4 1-7 1l2-1c2-1 6-2 8-2l4-1c1 0 1 0 2-1h1c1 1 2 0 3 0h2 3 5l9-1h2 12v-1l-1-1z" class="X"></path><path d="M304 94l4 1-3 1c-1 0-1-1-2-1l1-1z" class="AG"></path><path d="M284 95l3-1h2v1h1 3 2 1 0 2 1 1 1 1l1-1h1l-1 1c-4 1-8 3-13 3h0l2-2h-1l-1 1c-2 0-5 0-7 1l-2-1 3-2z" class="AP"></path><path d="M306 87h3 5c-3 2-7 3-10 4-4 0-7 2-10 3h-5 0-2l6-2v-1h-2c-2 1-4 1-7 1l2-1c2-1 6-2 8-2l4-1c1 0 1 0 2-1h1c1 1 2 0 3 0h2z" class="i"></path><path d="M306 87h3l-8 3-1-1c-1-1-4 0-6 0l4-1c1 0 1 0 2-1h1c1 1 2 0 3 0h2z" class="U"></path><path d="M294 89c2 0 5-1 6 0l1 1c-2 0-6 2-8 2v-1h-2c-2 1-4 1-7 1l2-1c2-1 6-2 8-2z" class="K"></path><path d="M189 195l1-1 1 1c1 1 2 3 3 5 0 1 0 2 1 3v2 4l-1 1c0 1 0 1 1 2v1h0c-1 0-1 0-1-1h0c-1 1-1 2 0 3 0 2 1 3 0 5l1 1h0c-1 1-1 5-1 7 0 4-1 8 0 12l-1 1h-1v1h-1c0 1 0 2-1 3h-2c-1-1-1-1-1-2-1-1-1-3-1-4s0-1-1-1-1 0-1 1l-2-1c0-1 0-2 1-3 1-2 3-5 4-7 1-1 0-2 0-3-1-1-1-1-2-1l1-3c2 2 2 5 2 7v-3h0 0v-2c-1 0-1-1-1-1 0-1 0-1 1-2 0-1 3-3 4-4-1 1-3 2-4 2s-1 0-2-1c-3-3 0-6 1-10 0-2-1-4-1-6 0-1 0-1 1-2h1v-2c0-1 0-2 1-2z" class="Ad"></path><path d="M188 220c0 2 2 7 1 9 0 1-1 2-1 3-2 2-3 3-2 5 1 1 1 1 2 1h0l-2 1c0-1 0-1-1-1s-1 0-1 1l-2-1c0-1 0-2 1-3 1-2 3-5 4-7 1-1 0-2 0-3-1-1-1-1-2-1l1-3c2 2 2 5 2 7v-3h0 0v-2c-1 0-1-1-1-1 0-1 0-1 1-2z" class="j"></path><path d="M194 200c0 1 0 2 1 3v2 4l-1 1c0 1 0 1 1 2v1h0c-1 0-1 0-1-1h0c-1 1-1 2 0 3 0 2 1 3 0 5l1 1h0c-1 1-1 5-1 7 0 4-1 8 0 12l-1 1h-1v1h-1c0 1 0 2-1 3h-2c-1-1-1-1-1-2h0l2-1c2-2 3-4 4-6v-16c0-2 0-3-1-4 0-5 2-11 2-16z" class="P"></path><defs><linearGradient id="G" x1="70.624" y1="119.16" x2="87.312" y2="119.478" xlink:href="#B"><stop offset="0" stop-color="#000201"></stop><stop offset="1" stop-color="#1e1313"></stop></linearGradient></defs><path fill="url(#G)" d="M86 94c2 0 2 0 3 1l-1 2-1 2 2-1-1 2h0l-1 2v2h0v2c-1 1-2 1-2 3-1 1-2 2-3 4-1 1-1 2-1 2-1 1-2 2-2 4h0l-1 1c0 1 0 2-1 3h0c-2 4-1 10-1 14 1 1 1 2 1 4v1c0 1 0 1-1 1h-1c0 1-1 1-1 2 0 0 0 1-1 2h-1 0l-2-13v-2c-1-2 0-5 0-8 1-8 3-17 7-24 3-1 3-3 5-4 1-1 3-2 4-2z"></path><path d="M76 137c-2-3 0-6-1-9-1-4 0-9 1-13 1-2 1-4 1-6 1-3 3-7 5-9 2-3 3-3 6-3l-1 2 2-1-1 2h0l-1 2v2h0v2c-1 1-2 1-2 3-1 1-2 2-3 4-1 1-1 2-1 2-1 1-2 2-2 4h0l-1 1c0 1 0 2-1 3h0c-2 4-1 10-1 14z" class="Ac"></path><path d="M87 99l2-1-1 2h0l-1 2v2h0v2c-1 1-2 1-2 3-1 1-2 2-3 4-1 1-1 2-1 2-1 1-2 2-2 4h0l-1 1c0 1 0 2-1 3h0c0-2 0-5 1-7 2-7 6-11 9-17h0z" class="l"></path><path d="M87 104l-2 2-1-1 2-2 1 1h0z" class="AW"></path><path d="M192 252c1 2 1 2 2 3l1 3h0c1 0 2-3 2-4 0 1 0 2 1 3v9l1 3c0 3 1 6 2 10l4 11-1 1 1 1h0 0l-2-2v1c1 2 2 3 3 5v1l2 3h1v1l1 1c-2 0-4 0-5 1h-8c0-1-1-1-1-1l-1-1h-1c-1-2-1-4 0-5v-1c0-1 0-2-1-3h-1v-2h2v-2c1-2 1-3 1-5l-1-1 1-4v-1c-1-2 0-4-1-6h-1l-1-1v1c-1 0-1 0-2 1-1-1 0-2 0-4-2 0-1 1-3 1h0-2c-1 0-1-1-2-1v-1h-1v1c-1 0-1 0-2-1h0l2-2v-1c0-1 0-1 1-2l1-1v-1c-1 0-1-1-2-1l1-2 1-1h1l1 1h2 1v-2h1v-1l2-1v-1z" class="V"></path><path d="M189 262c1 0 2-1 3 0l1 1c0 1 1 1 2 1v13c-1-2 0-4-1-6h-1l-1-1-1-1v-3c-1-1-1-2-2-3h0-1l1-1z" class="AR"></path><path d="M189 262c1 0 2-1 3 0l1 1c0 1-1 3-1 3 1 1 1 1 1 2h-1c-1-2-1-3-1-5h-2 0-1l1-1z" class="P"></path><path d="M192 252c1 2 1 2 2 3l1 3v6c-1 0-2 0-2-1l-1-1c-1-1-2 0-3 0v-1-1h0-2-3c-1 0-1-1-2-1l1-2 1-1h1l1 1h2 1v-2h1v-1l2-1v-1z" class="W"></path><path d="M192 253l1 2c0 1-1 1-1 1l-1 1c0-1 0-1-1-2v-1l2-1z" class="T"></path><path d="M189 257v-2h1c1 1 1 1 1 2l1 2h0c-1 0-2-1-3-2h0z" class="Q"></path><path d="M184 256h1l1 1h2 1 0c1 1 2 2 3 2h0c1 1 1 1 0 1v2c-1-1-2 0-3 0v-1-1h0-2-3c-1 0-1-1-2-1l1-2 1-1z" class="T"></path><path d="M184 256h1l1 1h2 1 0v2h-1-1l-1-1c-1 0-2-1-2-2z" class="x"></path><path d="M187 260h2 0v1 1l-1 1h1 0c1 1 1 2 2 3v3l1 1v1c-1 0-1 0-2 1-1-1 0-2 0-4-2 0-1 1-3 1h0-2c-1 0-1-1-2-1v-1h-1v1c-1 0-1 0-2-1h0l2-2v-1c0-1 0-1 1-2l1-1v-1h3z" class="h"></path><path d="M184 260h3s-1 2-2 2c-1 1-1 1-2 0l1-1v-1z" class="Al"></path><path d="M183 267l2-2c1 0 1 0 2 1 1 0 1 1 1 2l-1 1h-2c-1 0-1-1-2-1v-1z" class="o"></path><path d="M196 279v-1c0-2-1-4 0-6v2c1 2 2 4 2 5l5 11v1c1 2 2 3 3 5v1l2 3h1v1l1 1c-2 0-4 0-5 1h-8c0-1-1-1-1-1l-1-1h-1c-1-2-1-4 0-5v-1c0-1 0-2-1-3h-1v-2h2v-2c1-2 1-3 1-5l-1-1 1-4 1 1z" class="g"></path><path d="M200 296l3 5c-1 0-2 0-3-1v-1-3z" class="H"></path><path d="M208 300h1v1l1 1c-2 0-4 0-5 1h-8 2v-1h1v-2c1 1 2 1 3 1h0c2 0 4 0 5-1z" class="E"></path><path d="M200 300c1 1 2 1 3 1h0v1c-1 1-3 1-4 1v-1h1v-2z" class="B"></path><path d="M199 289v-2l2 2c0 1 1 1 2 2 1 2 2 3 3 5v1c0 1 0 2-1 2h-1c-3-3-4-7-5-10z" class="l"></path><path d="M199 289v-2l2 2c0 1 1 1 2 2 1 2 2 3 3 5-4-1-4-4-6-7h-1z" class="R"></path><g class="z"><path d="M195 278l1 1c0 3 0 7 1 10s2 5 3 7v3 1 2h-1v1h-2c0-1-1-1-1-1l-1-1h-1c-1-2-1-4 0-5v-1c0-1 0-2-1-3h-1v-2h2v-2c1-2 1-3 1-5l-1-1 1-4z"></path><path d="M194 295l1-1c1 0 2 1 2 1 1 1 1 3 3 4v1 2h-1v1h-2c0-1-1-1-1-1l-1-1h-1c-1-2-1-4 0-5v-1z"></path></g><path d="M197 299c1 1 1 2 2 3v1h-2c0-1-1-1-1-1l-1-1h1c0-1 1-1 1-2z" class="N"></path><path d="M194 295l1-1c1 0 2 1 2 1 1 1 1 3 3 4v1 2h-1c-1-1-1-2-2-3-1-2-2-2-3-3v-1z" class="C"></path><path d="M191 175l1 4c1 0 2 0 2 1 1 1 1 2 1 3l1-1c0 1 0 1 1 2l1 1v5 3l-1 1h0c0 2 1 4 1 6 0 1 0 1 1 2s1 4 1 5 0 2-1 2c1 1 1 1 2 1h0c1 2 1 3 1 5l-1 8c-1 2-1 8-1 10l-1 15c0 3 0 6-1 9-1-1-1-2-1-3 0 1-1 4-2 4h0l-1-3c-1-1-1-1-2-3v-4c2-2 2-3 2-6v-2c-1-4 0-8 0-12 0-2 0-6 1-7h0l-1-1c1-2 0-3 0-5-1-1-1-2 0-3h0c0 1 0 1 1 1h0v-1c-1-1-1-1-1-2l1-1v-4-2c-1-1-1-2-1-3-1-2-2-4-3-5l-1-1-1 1c-1 0-1 1-1 2l-1 1-2-1-1-1c1-2 1-3 2-4s1-1 1-2l1-1c1-1 1-2 1-3-1-1-2-1-2-1l1-2c0-3 1-4 3-6v-2z" class="AI"></path><path d="M201 210h0c1 2 1 3 1 5l-1 8c-1 2-1 8-1 10v-5c-1-5-1-13 1-18z" class="D"></path><path d="M194 228c0-2 0-6 1-7h0l-1-1c1-2 0-3 0-5-1-1-1-2 0-3h0c0 1 0 1 1 1h0c2 4 0 10 1 14 1 1 1 3 1 4 1 6 2 12 1 18v1c1-1 1-1 1-2 0 3 0 6-1 9-1-1-1-2-1-3 0-5 1-10 0-15 0-4-1-7-2-10v-2l-1 1z" class="B"></path><path d="M194 228l1-1v2c1 3 2 6 2 10 1 5 0 10 0 15 0 1-1 4-2 4h0l-1-3c-1-1-1-1-2-3v-4c2-2 2-3 2-6v-2c-1-4 0-8 0-12z" class="y"></path><path d="M194 242c1 3 1 5 1 8h-2l-1-2c2-2 2-3 2-6z" class="q"></path><path d="M192 252v-4l1 2h2v2c-1 1-1 1-1 2 1 0 0 1 0 1-1-1-1-1-2-3z" class="n"></path><path d="M191 175l1 4c1 0 2 0 2 1 1 1 1 2 1 3l1-1c0 1 0 1 1 2l1 1v5 3l-1 1h0c0 2 1 4 1 6-1-2-1-3-2-5h0v-1c0-1 0-1-1-2v1 2 1l1 1v1c-1 2 0 4-1 7v-2c-1-1-1-2-1-3-1-2-2-4-3-5l-1-1-1 1c-1 0-1 1-1 2l-1 1-2-1-1-1c1-2 1-3 2-4s1-1 1-2l1-1c1-1 1-2 1-3-1-1-2-1-2-1l1-2c0-3 1-4 3-6v-2z" class="Aa"></path><path d="M192 179c1 0 2 0 2 1 1 1 1 2 1 3s1 3 1 4h-1l-1-2-2-2c0-1-1-2-1-4h1z" class="AX"></path><path d="M196 182c0 1 0 1 1 2l1 1v5 3l-1 1c-1-3-2-4-2-7h1c0-1-1-3-1-4l1-1z" class="U"></path><path d="M190 190c2 0 3 0 4 2v2c0 1 0 3 1 4v5c-1-1-1-2-1-3-1-2-2-4-3-5l-1-1-1 1c0-3 0-3 1-4v-1z" class="AL"></path><path d="M190 191c1 0 2 1 3 2v1l-2 1-1-1-1 1c0-3 0-3 1-4z" class="Aa"></path><path d="M191 175l1 4h-1l-1 1c0 1 0 2 1 3v1h0v1h-1 0c0 1 1 2 0 3l-1 1 1 1v1c-1 1-1 1-1 4-1 0-1 1-1 2l-1 1-2-1-1-1c1-2 1-3 2-4s1-1 1-2l1-1c1-1 1-2 1-3-1-1-2-1-2-1l1-2c0-3 1-4 3-6v-2z" class="AR"></path><path d="M84 134v1l1-1h0v1c0 1 1 1 1 2v3 3h0c0 5 1 9 1 14h2c1 2 1 3 2 4 1 3 2 7 3 10h0v1h-2c4 6 6 10 11 14l-1 1h0c1 1 3 2 4 3h0l1 1s1 1 2 1c-1 0-2-1-3-1h0l-1-1h-1c-2-1-3-2-4-2-11-6-20-18-25-29-2-4-2-8-3-12h0 1c1-1 1-2 1-2 0-1 1-1 1-2h1c1 0 1 0 1-1h0c1 2 1 2 1 4h1c0-1 0-3 1-5h0v1h1 2v-5l1-3z" class="k"></path><path d="M87 171l3 2h0v-1-1c1 2 2 3 3 5 2 2 3 4 4 6-4-1-9-7-11-11h1z" class="AS"></path><path d="M72 147h0 1c1-1 1-2 1-2 0-1 1-1 1-2h1v1c0 1 1 1 1 2v1c-1 1-1 3 0 5l1 3v1h-1 0c0-2-1-3-1-4v-1h-1l-1 1c0 1 0 2 1 3 0 2 0 3 1 4h-1c-2-4-2-8-3-12z" class="y"></path><path d="M77 142h0c1 2 1 2 1 4h1c0-1 0-3 1-5h0v1 5l1 1c0 2 1 5 2 8 2 5 4 10 7 15v1 1h0l-3-2h-1c-1-2-2-4-4-7-1-2-3-6-4-9l-1-3c-1-2-1-4 0-5v-1c0-1-1-1-1-2v-1c1 0 1 0 1-1z" class="AA"></path><path d="M77 152v-2h0c2 1 3 6 4 9l6 12h-1c-1-2-2-4-4-7l-4-9-1-3z" class="AG"></path><defs><linearGradient id="H" x1="80.268" y1="150.374" x2="85.722" y2="150.719" xlink:href="#B"><stop offset="0" stop-color="#948587"></stop><stop offset="1" stop-color="#a09c9a"></stop></linearGradient></defs><path fill="url(#H)" d="M84 134v1l1-1h0v1c0 1 1 1 1 2v3 3h0c0 5 1 9 1 14h2c1 2 1 3 2 4 1 3 2 7 3 10h0v1h-2c4 6 6 10 11 14l-1 1c-1-2-4-4-5-5-1-2-2-4-4-6-1-2-2-3-3-5-3-5-5-10-7-15-1-3-2-6-2-8l-1-1v-5h1 2v-5l1-3z"></path><path d="M89 157c1 2 1 3 2 4 1 3 2 7 3 10h0v1h-2l-6-13c1-1 1-1 1-2h2z" class="x"></path><path d="M84 134v1l1-1h0v1c0 1 1 1 1 2v3 3h0c0 5 1 9 1 14 0 1 0 1-1 2-1-3-2-7-3-10v-7-5l1-3z" class="AM"></path><path d="M84 134v1l1-1h0v1c0 1 1 1 1 2v3 3h0 0c0-2 1-4 0-5l-1 1v2c-1 1 0 2 0 3s-1 1-1 3l-1 2v-7-5l1-3z" class="AO"></path><defs><linearGradient id="I" x1="132.015" y1="110.854" x2="125.742" y2="85.79" xlink:href="#B"><stop offset="0" stop-color="#a78785"></stop><stop offset="1" stop-color="#c6c5c7"></stop></linearGradient></defs><path fill="url(#I)" d="M121 85h2 2l1-1 1 1 5 1h0 3c1 1 4 2 4 3 3 1 7 2 10 2h0 1 1l1 1h1l5 2 1 1 5 1c0 4 1 7 2 9 1 1 1 1 1 2l-2-3c-2-1-3-2-5-3-2-2-4-3-6-4-4-2-7-3-11-4h-4-9l-2 1c-1 0-1 0-1 1l-9 1-4 1c-6 2-11 4-16 8-1 0-1-1-2-2v1c-1 0-1 1-2 1v1h-1c-1 0-2 2-3 3h0c-1 1-1 2-3 3h0-2l-1 1h-2c1-2 2-3 3-4 0-2 1-2 2-3v-2h0v-2l1-2c2-1 3-3 6-4v1c7-4 14-8 21-9 3-1 6-1 8-1l-2-2z"></path><path d="M132 86h3c1 1 4 2 4 3l-7-1-2-2h2z" class="AB"></path><path d="M121 85h2 2l1-1 1 1 5 1h0-2l2 2-9-1-2-2z" class="Q"></path><path d="M98 98c4-2 7-4 12-5 10-3 23-2 33 0h-4-9l-2 1c-1 0-1 0-1 1l-9 1-4 1c-6 2-11 4-16 8-1 0-1-1-2-2v1c-1 0-1 1-2 1v1h-1c-1 0-2 2-3 3h0c-1 1-1 2-3 3h0-2l-1 1h-2c1-2 2-3 3-4 0-2 1-2 2-3v-2h0v-2l1-2c2-1 3-3 6-4v1l-1 1v1s1 0 1-1h4z" class="Ae"></path><path d="M90 108c0-1-1-1-1-2 2-1 2-2 4-2l1 1v1h-1c-1 0-2 2-3 3h0v-1z" class="R"></path><path d="M85 112v-2l3-2h2v1c-1 1-1 2-3 3h0-2z" class="l"></path><path d="M114 96c5-2 10-3 16-3l-2 1c-1 0-1 0-1 1l-9 1-4 1v-1z" class="Am"></path><path d="M88 100c2-1 3-3 6-4v1l-1 1v1s1 0 1-1h4c-2 1-4 2-5 3-2 2-4 3-6 5v-2h0v-2l1-2z" class="Af"></path><path d="M93 98v1s1 0 1-1h4c-2 1-4 2-5 3-2 0-3 0-4 1h-1c1-2 4-3 5-4z" class="AW"></path><path d="M96 103v-1l1-1c5-3 11-4 17-5v1c-6 2-11 4-16 8-1 0-1-1-2-2z" class="AX"></path><path d="M235 86c1 0 1 1 2 1h2 3l2 1h2c2 1 4 1 5 2 2 0 3 1 4 2v1l1 1v1c-1 2-1 3-3 5s-5 4-7 6c-3 2-5 3-7 4l-2 2c-1 0-2 0-3-1l-3 3h0c0-1 0-2-1-3h-1c-1 1-2 3-3 4v-2h-1l-1-1-2 1h-1c1-1 1-2 1-3-1-1-1-2-2-3l1-2 1-1s1 0 1-1c0 0-1 1-2 1 0-1 0-3 1-4l-1-1v-4l2-2v-1h-2c1-1 2-3 4-3l2-1h2c1-1 3-1 4-1 1-1 1-1 2-1z" class="t"></path><path d="M237 97h3c1 0 2 0 3 1-1 0-4 0-4 1l-2-2z" class="J"></path><path d="M240 97c2-1 4-1 6-1h1v1c-1 1-2 1-4 1-1-1-2-1-3-1z" class="U"></path><path d="M235 98l2-1 2 2h-2c-2 1-3 2-4 2h0c1-1 1-2 2-3h0z" class="F"></path><path d="M224 108l1 1c0-1 1-2 2-4v1c-1 2-3 4-3 6l-2 1h-1c1-1 1-2 1-3h0c1 0 1-1 2-2z" class="AT"></path><path d="M239 108l2-2h1c-1 2-2 3-3 4l-2 2c-1 0-2 0-3-1l2-2c2 0 2 0 3-1z" class="H"></path><path d="M229 102c2-2 3-3 6-4-1 1-1 2-2 3h0c-2 2-4 3-6 5v-1l2-3z" class="e"></path><path d="M223 103h1l1-1v1h0v1c1 0 1 0 2-1h0c1-1 1-1 2-1l-2 3c-1 2-2 3-2 4l-1-1c-1 1-1 2-2 2h0c-1-1-1-2-2-3l1-2 1-1s1 0 1-1z" class="N"></path><path d="M220 107l1-2 1-1c1 2 1 3 2 4-1 1-1 2-2 2h0c-1-1-1-2-2-3z" class="Ai"></path><defs><linearGradient id="J" x1="254.18" y1="96.242" x2="245.911" y2="104.139" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#424443"></stop></linearGradient></defs><path fill="url(#J)" d="M255 93l1 1v1c-1 2-1 3-3 5s-5 4-7 6c-1-3 1-4 3-6 0 0 1-1 1-2h-1c1 0 2-1 3-1 0-1 0-1 1-2l1 1 1-1v-2z"></path><path d="M246 88c2 1 4 1 5 2 2 0 3 1 4 2v1 2l-1 1-1-1c-1 1-1 1-1 2-1 0-2 1-3 1l-2-2h-1 0c0-1-1-1-2-2v-1-1c-1-1-2-1-2-2 1-1 2-2 4-2z" class="AI"></path><path d="M251 90c2 0 3 1 4 2v1 2l-1 1-1-1c-1 1-1 1-1 2-1 0-2 1-3 1l-2-2h-1 0c1 0 2-1 3-1h3l2-2c-1-1-1-1-2-1h1v-1h-2v-1z" class="C"></path><path d="M226 113c2-2 3-4 4-5 2-4 8-7 12-8l5-2h0 1l-6 6h0l-2 2s-1 0-1 1v1c-1 1-1 1-3 1l-2 2-3 3h0c0-1 0-2-1-3h-1c-1 1-2 3-3 4v-2z" class="Z"></path><path d="M236 109c1-1 1-2 2-3s3-2 4-2h0l-2 2s-1 0-1 1v1c-1 1-1 1-3 1h0z" class="K"></path><path d="M231 114c1-1 1-2 1-3 1-1 1-1 1-2l1-1h0l1-1 1 2h0l-2 2-3 3z" class="E"></path><path d="M235 86c1 0 1 1 2 1h2 3l2 1h2c-2 0-3 1-4 2 0 1 1 1 2 2v1 1c1 1 2 1 2 2h0c-2 0-4 0-6 1h-3l-2 1h0c-3 1-4 2-6 4-1 0-1 0-2 1h0c-1 1-1 1-2 1v-1h0v-1l-1 1h-1s-1 1-2 1c0-1 0-3 1-4l-1-1v-4l2-2v-1h-2c1-1 2-3 4-3l2-1h2c1-1 3-1 4-1 1-1 1-1 2-1z" class="AL"></path><path d="M231 95c1-1 4-2 5-3l1 1-3 2h0 0v1h0c-1 0-2 1-3 2s-1 1-2 1l-1-1v-1c2 0 2-1 4-1l-1-1z" class="Aa"></path><path d="M244 92v1 1c1 1 2 1 2 2h0c-2 0-4 0-6 1h-3l-2 1c-1 0-1 0-2-1-1 0-2 1-2 1 1-1 2-2 3-2h0v-1h0 0l3-2c1 0 1-1 2-1 2 0 3 1 5 0z" class="w"></path><path d="M234 95l12 1h0c-2 0-4 0-6 1h-3l-2 1c-1 0-1 0-2-1-1 0-2 1-2 1 1-1 2-2 3-2h0v-1z" class="r"></path><path d="M235 89c1 0 2 1 2 2l-1 1c-1 1-4 2-5 3h0c-3 2-6 4-8 7h0-1c0-1 0-1 1-2v-2c2-4 6-7 10-9h1 1z" class="a"></path><path d="M235 86c1 0 1 1 2 1h2 3l2 1h2c-2 0-3 1-4 2 0 1 1 1 2 2-2 1-3 0-5 0-1 0-1 1-2 1l-1-1 1-1c0-1-1-2-2-2h-1-1c-4 2-8 5-10 9l-1 2-1-1v-4l2-2v-1h-2c1-1 2-3 4-3l2-1h2c1-1 3-1 4-1 1-1 1-1 2-1z" class="X"></path><path d="M227 88h2c-2 1-4 3-6 5v-1h-2c1-1 2-3 4-3l2-1z" class="AV"></path><path d="M244 88h2c-2 0-3 1-4 2 0 1 1 1 2 2-2 1-3 0-5 0-1 0-1 1-2 1l-1-1 1-1c0-1-1-2-2-2 1-1 4 0 6-1h3z" class="AU"></path><path d="M502 86c3 0 8-1 10 0l2 1v1h1 0c6-1 13 0 19 0 10 0 21-1 31 0l11 3c2 1 4 2 6 4h0-2 0c-1 0-2-1-3-1h0l-1-1h-1c-1-1-4 0-5 0-2-1-4-2-7-2-2 0-5 0-7-1-4-1-9 1-13 1-1 0-2-1-4 0-7 1-13 3-19 6-2 2-5 4-7 6l-2 1-2 3-2-2v-1l-10-6c-3-1-7-2-10-3v-1l-9-2h-5l-1-1c0 1-1 1-1 1h-1-3 0l-1-1h-4v-1c2-1 3-1 5-1h0l11-1h2 12c1 0 1-1 3 0h3 2 2c2 0 2-1 2-2h-2z" class="o"></path><path d="M507 104h4l-2 3-2-2v-1z" class="AP"></path><path d="M505 91c0 1 1 1 2 1 2 2 4 3 6 3l1 1h2c0-1 1-1 1-1l1 1c-1 0-1 1-2 1-2 0-3 0-5-1s-5-3-7-4l1-1zm2-3c1 0 6 0 7 1s3 2 4 2h0c1 0 1 1 1 1v1c-1-1-1-1-2-1h-1c-3-2-6-2-8-2l-1-2z" class="X"></path><path d="M502 86c3 0 8-1 10 0l2 1v1h1 0l3 3h0c-1 0-3-1-4-2s-6-1-7-1h-1-4c2 0 2-1 2-2h-2z" class="P"></path><path d="M504 86h3c0 1 0 1-1 2h-4c2 0 2-1 2-2z" class="Ag"></path><path d="M479 89c1-1 2-1 3-1 2 0 3 1 4 1 1 1 4 2 5 2 7 1 13 6 18 11h0c-2-1-2-2-3-2-3-2-5-4-8-5-2-1-5-2-7-3l-12-3z" class="l"></path><path d="M480 88h12c1 0 1-1 3 0h3 2c1 1 2 1 3 2l2 1-1 1c-1-1-2-2-3-2h-3v-1h-5c0 1 0 0-1 1h-1v1c-1 0-4-1-5-2-1 0-2-1-4-1-1 0-2 0-3 1-3-1-8 0-12 0h0l11-1h2z" class="AW"></path><defs><linearGradient id="K" x1="473.192" y1="100.203" x2="487.494" y2="85.577" xlink:href="#B"><stop offset="0" stop-color="#bab2b5"></stop><stop offset="1" stop-color="#e6e7e6"></stop></linearGradient></defs><path fill="url(#K)" d="M467 89c4 0 9-1 12 0l12 3c2 1 5 2 7 3 0 1 0 2-1 3-3-1-7-2-10-3v-1l-9-2h-5l-1-1c0 1-1 1-1 1h-1-3 0l-1-1h-4v-1c2-1 3-1 5-1z"></path><path d="M192 284c1 0 1-1 1-1v-1c1 0 1 0 2 1 0 2 0 3-1 5v2h-2v2h1c1 1 1 2 1 3v1c-1 1-1 3 0 5-1 1-1 1 0 2-1 1-1 2-1 2v1 1c-1 1-1 1-1 2v1c0 1-1 1 0 2 0 2 0 3-1 4 0 1 0 2 1 3l1 1v1c1 1 2 2 1 4v3 1h0l-1-1v-1h-1v1c-1 1-2 1-4 2v2l-1 1h-1v-1h-1c-2 1-3 1-4 2h-2c-2-3-2-5-2-8 0 1 0 4-1 4 0 1-1 1-2 1v-6-2h-1l1-3v-6c0-5-1-10 0-16h0l-6 3-7-4-2 1c-3 1-5 0-7-1-1 1-2 1-3 2s-1 1-2 1c1-2 3-3 5-4l4 2c1-1 4-2 5-2 0 0 1 0 1 1 2 0 4 2 6 2 1-1 1-1 1-2h0v1 1h0c1 0 2-1 2-1l3-3c1-1 0-3 1-4 0 0 0 1 1 1 0 1-1 2 0 3h2 3 0l1-1c1-1 0-1 1 0l1-1v-3c0-1-1 0-1-2 1-2 2-3 3-5h1 2l3 3v1h1c0-1 0-2-1-3z" class="P"></path><path d="M178 305l1-1 1 1c1 1 1 2 1 3l-1 1h-2v-1l1-2-1-1z" class="j"></path><path d="M193 321c1 1 2 2 1 4v3 1h0l-1-1v-1h-1l-1-2 1-1h1c-1-1 0-2 0-3z" class="v"></path><path d="M180 309h1v4c-1 1-2 1-3 1v-5h2z" class="AZ"></path><path d="M191 325h0c-1 1-1 1-2 0-1 0-1-1-1-2l1-2h2c1 1 1 2 1 3l-1 1z" class="AF"></path><path d="M178 314c0 1 0 1 1 2-1 1-1 2-1 3-1 1-1 2-2 2l-1-1v-13h1l2 1v1 5zm9-12h1c0 1 0 1 1 2h1l1 1v1h-2 0l1 2-2 1v1l-1 1c-2-1-4-2-6-4l6-5zm-6 7c2 1 7 3 8 5 0 2-1 3-2 4h-2c-2-1-3-2-4-4h0 0v-1-4zm-2-12h2v-1c2 0 3 0 5 1h0v2h-2c-1 1 0 1 0 3h0c-1 1-3 2-4 3l-1-1-1 1h0-3 0c0-2 0-5 1-7 1 0 2 0 2-1h1z" class="AK"></path><path d="M193 292c1 1 1 2 1 3v1c-1 1-1 3 0 5-1 1-1 1 0 2-1 1-1 2-1 2v1 1c-1 1-1 1-1 2l-2-1-1-2h0 2v-1l-1-1h-1c-1-1-1-1-1-2h-1l1-1c0-1-1-1-2-1 1-1 1-1 2-1v-1c1 0 1-1 2-2h0v-2h2l1-2z" class="Q"></path><path d="M188 301c1-1 2-2 3-2v3l-1 2h-1c-1-1-1-1-1-2h-1l1-1z" class="AF"></path><path d="M177 326c1-1 1-3 3-4h2c2 0 3 1 4 3 0 0 1 2 1 3l-2 3v1c-2 1-3 1-4 2h-2c-2-3-2-5-2-8z" class="Ad"></path><path d="M192 284c1 0 1-1 1-1v-1c1 0 1 0 2 1 0 2 0 3-1 5v2h-2v2h1l-1 2h-2v2h0c-1 1-1 2-2 2v1c-1 0-1 0-2 1l-2 2h0c0-2-1-2 0-3h2v-2h0c-2-1-3-1-5-1v1h-2l-1-2h3 0l1-1c1-1 0-1 1 0l1-1v-3c0-1-1 0-1-2 1-2 2-3 3-5h1 2l3 3v1h1c0-1 0-2-1-3z" class="AB"></path><path d="M183 288h0 1c1-1 3 0 4 0 1 1 1 1 1 2-1 2-4 2-5 3v-3c0-1-1 0-1-2zm3 9c0-1-1-1-1-2v-1c2 0 3-2 5-2v2 2h0c-1 1-1 2-2 2v1c-1 0-1 0-2 1l-2 2h0c0-2-1-2 0-3h2v-2h0z" class="AF"></path><path d="M311 559h2v4c-1 1 0 4 0 6l5-3s1 0 1 1l-3 6c-1 0-1 1-1 2-1 2 0 7 0 9 1 2 3 3 5 4s6 3 8 3c1 0 2-1 3-1l8-2c-1-1-2-2-2-3 0-3 0-5 2-7 0 2 0 4-1 6 1 1 2 3 3 4-3 2-9 7-10 11 0 1 2 2 2 3l1 4v1c1 1 1 2 2 2h1c1 1 3 1 4 2v1c0 1-1 2-2 3 1 2 4 6 7 7h0 2 1 2v1h1 0c1 0 1 0 1-1 1 1 1 2 2 3 0 1 0 1 1 1l1-1-1-1h0v-2-1l2 2h1 0c0 1-1 1-1 2l1 1c1 0 1 0 2-1v1 1c1 1 3 2 5 2 1-2 1-2 1-4 1 0 2 1 2 1 0 1 0 2-1 3 2 1 5 1 7 1l1-1h2l1-1h2 1 1 1l-1 1-1 1v1h2c1 0 1 0 2 1l1-1c1 1 1 1 2 1s2 0 4 1h-2 3 0c1 1 0 1 1 1h0 2 0 2 1l1-1h3c-10 3-19 3-29 1-7 0-14-3-21-5-11-5-23-11-31-21-4-5-8-11-11-17v-3-3h0c-1-2-1-4-1-6l-2-5v-3-5c1-2 1-2 1-4l1-3z" class="m"></path><path d="M332 604l2 2v1c-1 0-1 0-2-1v-1-1z" class="AB"></path><path d="M333 602l1 4-2-2c0-1 1-1 1-2z" class="j"></path><path d="M311 579v1h0c0 1 0 0 1 1s1 5 1 6l-1-2h0c-1-2-1-4-1-6z" class="h"></path><path d="M309 574v-3h1 0v2c1 2 1 4 2 6l-1 1v-1l-2-5z" class="P"></path><path d="M323 598c1 0 4-1 5 0v5l-1 1h0c-2-1-4-4-4-6z" class="Ad"></path><path d="M312 585l1 2c1 2 2 5 4 6h-1c-1 2 0 3 0 4 2 2 3 4 5 6 1 2 2 2 2 4v1c-4-5-8-11-11-17v-3-3z" class="W"></path><path d="M317 593l-1-3h0c2 1 3 4 4 5 0 1 0 2 1 3h2c0 2 2 5 4 6 0 1-1 1-1 2 1 2 3 4 5 6h0a30.44 30.44 0 0 1-8-8c-1-2-3-5-4-7-1-1-1-3-2-4z" class="P"></path><path d="M570 93c1 0 4-1 5 0h1l1 1h0c1 0 2 1 3 1h0 2 0c4 2 7 5 10 7 3 1 4 4 6 6 0 1 0 2 1 3 0 0 1 1 2 1 2 5 2 10 2 15l-2 2v3l-1 1-1-1c-2 6-5 12-11 16-6 3-9 1-15 0l-2-1h2c-1-2-2-3-3-5v-1l-4-4-3-2c1-1 3-1 4-1h0v-1c0-3 1-5 2-8l1 1c1-1 2-1 3-2s2-2 4-2v-3c1 1 2 1 3 3l1 1v1c1 1 1 1 3 2l-1 2c0 1 0 1 1 2h3 0l-1 1 1 1h0c1-1 2 0 3 0 3-3 3-5 3-9-1-3-1-4-2-7 0-3-3-7-6-9 1 0 5 3 6 4l-1 1c0 1 1 1 1 2l1-1v-1c-1-1-1-1-1-2-1-2-1-3-2-4-2-3-5-4-7-7v-1c-1 0-2-1-3-1-3-2-6-3-9-4z" class="AH"></path><path d="M570 142c1 1 3 2 4 2h0c1 0 2 1 3 1l2 1c-1 1-1 1-2 1-1-1-1-1-2-1-1 1-1 1-2 1-1-2-2-3-3-5z" class="Ai"></path><path d="M596 119c1 5 1 12-1 16l-2 3h0l-1-1c1-2 2-3 4-5h0l-1-1c0-1 0-2-1-2l1-3c0-1 2-3 1-5v-2z" class="AV"></path><path d="M599 114l1 1c1 5 2 9 1 14v3l-1 1-1-1c1-6 1-12 0-18z" class="k"></path><path d="M588 142h0c1-2 2-2 4-3-1 1-2 3-3 4s-5 3-7 3h-3l-2-1h1c-1-2-3-2-4-4v-1c3 2 5 4 9 4 1-1 4-1 5-2z" class="P"></path><path d="M592 102c3 1 4 4 6 6 0 1 0 2 1 3 0 0 1 1 2 1 2 5 2 10 2 15l-2 2c1-5 0-9-1-14l-1-1c-1 0-1-3-2-4-1-3-3-6-5-8z" class="S"></path><path d="M592 132c1-1 1-2 2-3 1 0 1 1 1 2l1 1h0c-2 2-3 3-4 5-3 2-5 2-9 2 1-1 2-3 4-3-1-1-2-1-3-2h4c2 0 3-1 4-2z" class="Af"></path><path d="M592 132c1-1 1-2 2-3 1 0 1 1 1 2-1 2-2 3-4 5h0c0-1 1-2 1-3v-1z" class="AL"></path><path d="M592 132v1c0 1-1 2-1 3-2 1-3 1-4 2v-1h0c1 0 1 0 2-1h0-2c-1-1-2-1-3-2h4c2 0 3-1 4-2zm-25 1h1c1 0 1 0 1 1 1 0 1 1 2 1v1l3 4v1c1 2 3 2 4 4h-1c-1 0-2-1-3-1h0c-1 0-3-1-4-2v-1l-4-4-3-2c1-1 3-1 4-1h0v-1z" class="AS"></path><path d="M563 135c1-1 3-1 4-1 0 1 1 1 1 1v2c3 1 4 4 6 7h0c-1 0-3-1-4-2v-1l-4-4-3-2z" class="AW"></path><path d="M570 93c1 0 4-1 5 0h1l1 1h0c1 0 2 1 3 1h0 2 0c4 2 7 5 10 7 2 2 4 5 5 8v1c0-1-1-1-1-2h0-1c0 1 0 1 1 2-1 0-2-1-3-2h0l-2-2c-2-3-6-8-9-9-1 0-2-1-3-1-3-2-6-3-9-4z" class="AP"></path><path d="M582 98c3 1 7 6 9 9l2 2c1 3 3 7 3 10v2c1 2-1 4-1 5l-1 3c-1 1-1 2-2 3s-2 2-4 2l1-1 1-1c3-3 3-5 3-9-1-3-1-4-2-7 0-3-3-7-6-9 1 0 5 3 6 4l-1 1c0 1 1 1 1 2l1-1v-1c-1-1-1-1-1-2-1-2-1-3-2-4-2-3-5-4-7-7v-1z" class="r"></path><path d="M591 107l2 2c1 3 3 7 3 10v2c1 2-1 4-1 5l-1 3c-1 1-1 2-2 3s-2 2-4 2l1-1 1-1c3-3 3-5 3-9v5c0-1 1-1 1-2 1-1 1-3 1-4v-1c-1-1-1-3-1-4 0-4-3-7-3-10z" class="k"></path><path d="M596 121c1 2-1 4-1 5v-3c0-1 0-1 1-2z" class="AL"></path><path d="M577 119c1 1 2 1 3 3l1 1v1c1 1 1 1 3 2l-1 2c0 1 0 1 1 2h3 0l-1 1 1 1h0c1-1 2 0 3 0l-1 1-1 1h-4c1 1 2 1 3 2-2 0-3 2-4 3 4 0 6 0 9-2l1 1-1 1c-2 1-3 1-4 3h0c-1 1-4 1-5 2-4 0-6-2-9-4l-3-4v-1c-1 0-1-1-2-1 0-1 0-1-1-1h-1c0-3 1-5 2-8l1 1c1-1 2-1 3-2s2-2 4-2v-3z" class="AN"></path><path d="M569 125l1 1c0 2-1 3 0 5 0 1 1 3 1 4-1 0-1-1-2-1 0-1 0-1-1-1h-1c0-3 1-5 2-8z" class="An"></path><path d="M577 122v1c0 1-1 2-1 3v2 3l-2 2v1 1h0c1 1 1 2 2 3v1h-1c-1-1-3-3-4-5 0-2 0-6 1-7v-1l1-2c1-1 2-2 4-2z" class="Aa"></path><path d="M573 128h1 2v3l-2 2v1l-1-1v-4-1z" class="AL"></path><path d="M574 128h2v3l-2 2v-5z" class="w"></path><path d="M577 122v1c0 1-1 2-1 3v2h-2-1v1c-1-1-1-1-1-2v-1l1-2c1-1 2-2 4-2z" class="Aa"></path><path d="M572 126c1-1 1-1 2-1 1 1 1 1 1 2l-2 1v1c-1-1-1-1-1-2v-1z" class="r"></path><path d="M574 133l2-2c1 2 2 4 3 5s2 2 3 2l1 1c4 0 6 0 9-2l1 1-1 1c-2 1-3 1-4 3h0-4c-4 0-6 0-9-3h1v-1c-1-1-1-2-2-3h0v-1-1z" class="k"></path><path d="M574 133l2-2c1 2 2 4 3 5h-1c-2 0-2 0-3-1v-1l-1 1v-1-1z" class="z"></path><path d="M577 119c1 1 2 1 3 3l1 1v1c1 1 1 1 3 2l-1 2c0 1 0 1 1 2h3 0l-1 1 1 1h0c1-1 2 0 3 0l-1 1-1 1h-4c1 1 2 1 3 2-2 0-3 2-4 3l-1-1c-1 0-2-1-3-2s-2-3-3-5v-3-2c0-1 1-2 1-3v-1-3z" class="J"></path><path d="M581 124c1 1 1 1 3 2l-1 2-2 2h-1 0 0v2l-1-2v-1l1-1c1-1 1-3 1-4z" class="N"></path><path d="M581 130l2-2c0 1 0 1 1 2h3 0l-1 1 1 1h0c1-1 2 0 3 0l-1 1-1 1h-4-2l-2-2v-2h0 0 1z" class="r"></path><path d="M581 130l2-2c0 1 0 1 1 2h3 0l-1 1 1 1h0c1-1 2 0 3 0l-1 1c-2 0-4 0-5-1-1 0-2-2-3-2z" class="U"></path><path d="M577 123h1l1 1c0 1 0 2 1 3h0c-1 0-1 1-1 1v1 1l1 2 2 2h2c1 1 2 1 3 2-2 0-3 2-4 3l-1-1c-1 0-2-1-3-2s-2-3-3-5v-3-2c0-1 1-2 1-3z" class="AH"></path><path d="M582 134h2c1 1 2 1 3 2-2 0-3 2-4 3l-1-1h0v-1l-1-1c1 1 1 1 2 1h1c-1-1-1-2-2-3z" class="Ae"></path><path d="M576 128v-2c0 1 0 3 1 4 0 0 1 1 1 2 1 1 3 2 3 3v1l1 1v1h0c-1 0-2-1-3-2s-2-3-3-5v-3z" class="AX"></path><path d="M315 121c1 0 1 0 2-1h2c1-1 2 0 2 0 1 0 1 1 2 2h-1c1 1 3 2 4 2l2-1h3 5 4v1h1 0c1-1 2-1 3-1 2-1 2-1 4 0l2 1h0v1c-1 0-1 1-1 1l1 1h1l1 1h1v1c0 1-1 3-1 5v1 7h-2 0v1 16c0 1 0 2-1 3-2 0-4 0-7 1-2 0-3 1-5 0v1c-1-1-1-1-2-1h-2c-1 0-1 0-1-1l2-3-2-1h-1 0-3c-1 0-1 1-1 1h-1-1v1c0-4-1-6-2-10-1 1-5 3-7 3h0l-1-1c-1-2-2-3-4-5-1-1-1-2-2-4l1-5h-1l-2 1c0-1-1-2-1-2h-2c0-1 0-1-1-2v-1-1-1c1-1 2-2 4-2l-1-1v-1c1-1 3-2 5-2h0c0-2 1-2 2-3 1 0 2 0 2-1h0v-1z" class="Ad"></path><path d="M344 123c2-1 2-1 4 0l2 1h0v1c-1 0-1 1-1 1 0 1-1 1-1 3-1-1-2-4-3-5 0-1-1-1-1-1z" class="AD"></path><path d="M331 123h5l-3 1v1c1 1 1 2 1 3h-1l-1-4c-5 2-9 3-13 5l-2 1v-1c2-2 3-3 5-4l4-1 2-1h3z" class="AE"></path><path d="M349 126l1 1h1l1 1h1v1c0 1-1 3-1 5v1 7h-2 0v1c0-3-1-6-1-8s-1-4 0-6h-1c0-2 1-2 1-3z" class="Q"></path><path d="M349 126l1 1h1l1 1c-1 1-2 1-3 1h-1c0-2 1-2 1-3z" class="AB"></path><path d="M313 137c1 0 1 0 1 1h0c-1 2-1 4-1 7 1 2 4 2 5 4l-2 2v2l-1-1c-1-2-2-3-4-5-1-1-1-2-2-4l1-5c1 1 1 1 2 0h0l1-1z" class="AE"></path><path d="M316 151h1c1-1 1-1 1-2h1c2-1 6-4 9-4 0 1 1 1 1 2 0 0-1 1-1 2-1 2 0 4 0 6l3 3h0-3c-1 0-1 1-1 1h-1-1v1c0-4-1-6-2-10-1 1-5 3-7 3h0v-2z" class="P"></path><path d="M315 121c1 0 1 0 2-1h2c1-1 2 0 2 0 1 0 1 1 2 2h-1c1 1 3 2 4 2l-4 1c-2 1-3 2-5 4v1c-1 1-3 2-4 4v3l-1 1h0c-1 1-1 1-2 0h-1l-2 1c0-1-1-2-1-2h-2c0-1 0-1-1-2v-1-1-1c1-1 2-2 4-2l-1-1v-1c1-1 3-2 5-2h0c0-2 1-2 2-3 1 0 2 0 2-1h0v-1z" class="d"></path><path d="M307 130c0-1 0-2 1-2h1v2l-2 2h0c-1 1-1 1-1 2h1l-1 1h0-2 0c0-1 0-1-1-2v-1c1-1 2-2 4-2z" class="h"></path><path d="M306 129v-1c1-1 3-2 5-2v1h4v1h0v1l1 1 1-1v1c-1 1-3 2-4 4h-3c-1 0-1-1-2-1h1l-2-1h0l2-2v-2h-1c-1 0-1 1-1 2l-1-1z" class="T"></path><path d="M306 129v-1c1-1 3-2 5-2v1h4v1h0c-1 0-3 1-4 1l-1-1h-1v2-2h-1c-1 0-1 1-1 2l-1-1z" class="d"></path><path d="M317 129v1c-1 1-3 2-4 4h-3c-1 0-1-1-2-1h1l2-2h0 2c1-1 1-1 3-1l1-1z" class="v"></path><path d="M315 121c1 0 1 0 2-1h2c1-1 2 0 2 0 1 0 1 1 2 2h-1c1 1 3 2 4 2l-4 1c-2 1-3 2-5 4l-1 1-1-1v-1h0v-1h-4v-1h0c0-2 1-2 2-3 1 0 2 0 2-1h0v-1z" class="p"></path><path d="M311 126h0c0-2 1-2 2-3 1 0 2 0 2-1h0v1h-1 0l1 1v-1c1 0 2-1 3-1h1c1 1 1 0 2 0v1c0 1-1 2-3 3-1 0-1-1-2-1h0c0 1 0 2-1 3v-1h-4v-1z" class="T"></path><path d="M316 125c-1 1-2 1-2 1l-1-1h0c1-1 2-1 3-1v1h0z" class="d"></path><defs><linearGradient id="L" x1="560.753" y1="136.065" x2="544.706" y2="249.775" xlink:href="#B"><stop offset="0" stop-color="#020302"></stop><stop offset="1" stop-color="#6a2726"></stop></linearGradient></defs><path fill="url(#L)" d="M552 138c1-1 2-2 3-2 2-1 4-1 7 0v1h4l4 4v1c1 2 2 3 3 5h-2v-1h0v2 1c-1 2 0 3-2 3-1 1-1 0-2 0v1h-2v2h0c1 2 1 3 2 5v2c0 3 2 5 1 8 0 2 1 4 0 5v1l-1 2-1-1c0 2 0 2-1 3h-2c1 1 1 1 1 2 1 4-1 10-2 14v5 6h0v-3h1 1v19l-1 24-1-1-2 10c0-1 0-2-1-3-1 0-1 0-2 1h0l-1-1h0v-1h-1v-3-3h-1v-8h0c1-2 1-4 1-5v-8l-1-5v-3c-1-1-1-2-3-2v-1c-1-3-1-7-2-10h1 0v-2h0v-1-2c1-1 0-4-1-5l-2-7-1-6c-1-2-1-3-2-5 0-3-1-8-2-12h1l1 2c0-4-1-8 0-11 1-2 1-4 1-5 1-2 1-4 2-6 1 0 1-1 1-1 1-2 2-4 4-5z"></path><path d="M562 137h4l4 4c-1 0-2-1-2-1l-2 1c-1 0-3-3-4-4h0z" class="AS"></path><path d="M559 244c1 3 1 6 0 9-1 0-1 0-2 1h0l-1-1h0l1-6v5l1-1v-1s1-1 1-2v-1-3z" class="AO"></path><path d="M553 142c1 0 1 0 1-1 1-1 1-1 2-1v-1c1 0 1-1 1-1 1-1 2-1 3 0l1 1h0c-1 1-2 1-3 2h0c-1 0-2 1-3 1h-2z" class="C"></path><path d="M547 166c2 1 3 2 4 3v1 1 3s-1 0-1 1l-3-9z" class="q"></path><path d="M553 142h2c1 0 2-1 3-1h0c1-1 2-1 3-2 0 1 1 2 1 3l-2 1-1-1v1c0 1 1 2 2 2v2l-1-2c-1 1-2 1-3 1v-1s1-1 1-2h-1c-1 0-2 0-3 2h0-2c0-1 1-2 1-3z" class="r"></path><path d="M552 145h2 0c1-2 2-2 3-2h1c0 1-1 2-1 2h-1s0 1-1 1-1 0-2 1c0 0-1 0-1 1-2 0-2 2-3 3-1 3 0 5-1 7 0-1-1-3-1-4 1-4 2-6 5-9z" class="k"></path><path d="M561 147v-2c-1 0-2-1-2-2v-1l1 1 2-1c1 1 1 3 2 5s3 3 3 5v1h-2s0-1-1-1c0-3-1-3-2-5h-1z" class="S"></path><path d="M548 158c1-2 0-4 1-7 1-1 1-3 3-3 0-1 1-1 1-1 1-1 1-1 2-1s1-1 1-1h1v1c1 0 2 0 3-1l1 2h1c1 2 2 2 2 5l-4-4h-5c-2 0-3 2-4 3 0 1-1 2-1 2v1 1c-1 2-1 3-1 4l-1-1z" class="Ae"></path><path d="M550 155v-1-1s1-1 1-2c1-1 2-3 4-3h5l4 4c1 0 1 1 1 1v2l-1-1h0c-1-2-3-3-5-3s-3 0-5 1h0c-2 1-2 1-3 3h-1z" class="AH"></path><path d="M551 169l2 2h1l2 1-1 1-2 1v2 1 1 1l1 1-1 2c1 1 1 2 2 3-1 1-1 2-1 4h0c1 2 1 7 1 8-1-3-1-6-2-9l-3-13c0-1 1-1 1-1v-3-1-1z" class="n"></path><path d="M553 182c-1-2 0-4-1-6-1 0-1 0-1-1l1-1v-2h-1l1-1h1 1l2 1-1 1-2 1v2 1 1 1l1 1-1 2z" class="AN"></path><path d="M547 166h0c0-2-1-4-1-6 1-1 0-2 0-3s1-2 1-3c0 1 1 3 1 4l1 1c0 3 0 5 3 6l1 1c1 1 2 1 3 1h1v1h0c-1 0-2 0-2 1v1s0 1-1 1h-1l-2-2c-1-1-2-2-4-3z" class="C"></path><path d="M554 180l1 1-1 1 1 1 1-1h1c0 2 0 2-1 3l2 2 1 1h-1v1 1l1 1-1 2h-1v7 2h0c-1 1 0 1-1 0l-1-5c0-1 0-6-1-8h0c0-2 0-3 1-4-1-1-1-2-2-3l1-2z" class="AA"></path><path d="M557 193v-4c-1-1-1-1 0-1l1 2 1 1-1 2h-1z" class="r"></path><path d="M551 155c1-2 1-2 3-3 0 1-1 1-1 1 0 1-1 2-1 3 0 2-1 4 0 5 2 0 3 0 4 1 0 1 0 1 1 1h0v1l3-1 1 1c-1 1-2 1-2 2l-3 1c-1 0-2 0-3-1l-1-1c-3-1-3-3-3-6 0-1 0-2 1-4h1z" class="L"></path><path d="M552 161c2 0 3 0 4 1 0 1 0 1 1 1h0-2c0 1 0 1-1 1-1-1-1-2-2-3z" class="C"></path><path d="M550 155h1v6l1 1v3h1v1l-1-1c-3-1-3-3-3-6 0-1 0-2 1-4z" class="AT"></path><path d="M555 211c1 5 1 10 1 15 1 7 1 15 1 21l-1 6v-1h-1v-3-3h-1v-8h0c1-2 1-4 1-5v-8l-1-5v-3l-1-2v-2h1l1-2z" class="Ao"></path><path d="M555 225v6 15h-1v-8h0c1-2 1-4 1-5v-8z" class="Ae"></path><path d="M555 211c1 5 1 10 1 15v11c-1-2 0-4-1-6v-6l-1-5v-3l-1-2v-2h1l1-2z" class="Aj"></path><path d="M553 179v-1-1-1-2l2-1v4l1-1 2 2 5 2c1 1 1 1 1 2 1 4-1 10-2 14h-2c-1-2 0-1 1-3 0 0-1-1-1-2v1l-1-1-1-1v-1-1h1l-1-1-2-2c1-1 1-1 1-3h-1l-1 1-1-1 1-1-1-1-1-1z" class="Ai"></path><path d="M558 183l1-2c1 0 3 1 4 1-1 2-1 5-2 7 0 1 0 1-1 2v1l-1-1-1-1v-1-1h1 1v-1-1l1-1c-2-1-2 0-3 0v-2z" class="AV"></path><path d="M553 179v-1-1-1-2l2-1v4h0c1 1 2 2 2 3v4c1 0 1 0 1-1v2c1 0 1-1 3 0l-1 1v1 1h-1l-1-1-2-2c1-1 1-1 1-3h-1l-1 1-1-1 1-1-1-1-1-1z" class="Ab"></path><path d="M553 179l1-1h0c1 1 1 2 2 3h-1 0l-1-1-1-1z" class="AS"></path><path d="M542 164h1l1 2 11 45-1 2h-1v2l1 2c-1-1-1-2-3-2v-1c-1-3-1-7-2-10h1 0v-2h0v-1-2c1-1 0-4-1-5l-2-7-1-6c-1-2-1-3-2-5 0-3-1-8-2-12z" class="AW"></path><path d="M550 199l1 3c1 3 2 7 2 11v2l1 2c-1-1-1-2-3-2v-1c-1-3-1-7-2-10h1 0v-2h0v-1-2z" class="Ab"></path><path d="M550 199l1 3h0v3c-1 2 0 5 0 7h0c1 1 2 2 2 3l1 2c-1-1-1-2-3-2v-1c-1-3-1-7-2-10h1 0v-2h0v-1-2z" class="AV"></path><path d="M554 152c2-1 3-1 5-1s4 1 5 3h0l1 1h0l-1 3h0c-1 1-1 1 0 2h-1l1 1c-1 1-2 1-2 0v2l-1 1-1-1-3 1v-1h0c-1 0-1 0-1-1-1-1-2-1-4-1-1-1 0-3 0-5 0-1 1-2 1-3 0 0 1 0 1-1h0z" class="y"></path><path d="M564 154l1 1h0l-1 3h0c-1 1-1 1 0 2h-1l1 1c-1 1-2 1-2 0v2l-1 1-1-1-3 1v-1h0l2-2 1 1v-1c0-1 0-1 1-2h0c1-1 2-1 3-2v-3z" class="B"></path><path d="M562 161v-1h1l1 1c-1 1-2 1-2 0z" class="C"></path><path d="M554 152c2-1 3-1 5-1s4 1 5 3h0v3c-1 1-2 1-3 2v-4l-1-2c-1-1-1-1-3-1h-3z" class="w"></path><path d="M565 155c1 2 1 3 2 5v2c0 3 2 5 1 8 0 2 1 4 0 5v1l-1 2-1-1c0 2 0 2-1 3h-2l-5-2-2-2-1 1v-4l1-1-2-1c1 0 1-1 1-1v-1c0-1 1-1 2-1h0v-1h-1l3-1c0-1 1-1 2-2l1-1v-2c0 1 1 1 2 0l-1-1h1c-1-1-1-1 0-2h0l1-3z" class="k"></path><path d="M562 161c0 1 1 1 2 0v1c-1 2-1 3-2 5l-1 1v1c2 0 2 0 2 1-1 1-2 1-4 0-1 0-2 1-3 2l-2-1c1 0 1-1 1-1v-1c0-1 1-1 2-1h0v-1h-1l3-1c0-1 1-1 2-2l1-1v-2z" class="z"></path><path d="M556 172c1-1 2-2 3-2 2 1 3 1 4 0v1c1 0 2 0 3 1v1c1 1 1 2 2 3l-1 2-1-1c0 2 0 2-1 3h-2l-5-2-2-2-1 1v-4l1-1z" class="a"></path><path d="M556 176l1-3 1 1h2v1s0 1-1 1c0 1 0 2-1 2l-2-2z" class="z"></path><path d="M556 172c1-1 2-2 3-2l-2 3-1 3-1 1v-4l1-1z" class="i"></path><path d="M563 171c1 0 2 0 3 1v1c1 1 1 2 2 3l-1 2-1-1v-2h0-1c-1-2-2-2-2-4z" class="w"></path><path d="M559 191l1 1v-1c0 1 1 2 1 2-1 2-2 1-1 3h2v5 6h0v-3h1 1v19l-1 24-1-1-2 10c0-1 0-2-1-3 1-3 1-6 0-9v-6-16c0-1 0-2-1-3 0-1-1-1-1-1v-5h0v-1c-1-1-1-2-1-2v-1-2h-1l1-1v-4c1 1 0 1 1 0h0v-2-7h1l1-2z" class="AS"></path><path d="M560 196h2v5c0 1-1 2-1 2v1c-1 0-1 0-1-1v-7h0z" class="Af"></path><path d="M559 191l1 1c0 1 0 1-1 2v3 7c-1-1-1-1-1-2s0-1-1-2v-7h1l1-2z" class="AL"></path><path d="M562 237v9l-2 10c0-1 0-2-1-3 1-3 1-6 0-9v-6c1 1 1 1 1 2 1 1 0 3 0 4 1 2 1 3 1 5v-2-1-1c0-2-1-3 0-5h0l1-3z" class="p"></path><path d="M562 207h0v13c0 3 0 6 1 10v1-2c1-2 0-4 1-6l-1 24-1-1v-9-30z" class="w"></path><path d="M562 207v-3h1 1v19c-1 2 0 4-1 6v2-1c-1-4-1-7-1-10v-13z" class="a"></path><path d="M557 200c1 1 1 1 1 2s0 1 1 2v18c0-1 0-2-1-3 0-1-1-1-1-1v-5h0v-1c-1-1-1-2-1-2v-1-2h-1l1-1v-4c1 1 0 1 1 0h0v-2z" class="n"></path><path d="M556 202c1 1 0 1 1 0h0c1 3 2 7 1 9-2 0-1-4-2-5v-4z" class="AN"></path><path d="M130 93h9 4c4 1 7 2 11 4 2 1 4 2 6 4 2 1 3 2 5 3l2 3 1 1h1 1l4 6 2-1v1l2 2s1 1 1 2l2 2c0 2 2 5 3 7l2 1 1 2 2 7-1 1v-1h-1c1 2 1 4 2 6h0l-1-1-1 1 1 2 4 12h0c3 9 3 17 4 25l-1 1c0-1 0-2-1-3 0-1-1-1-2-1l-1-4c0-2 0-4-2-5v-1h1c0-2-1-4-1-5-3-12-8-22-13-33-1 0-1-1-2-2l-3-4c-1-1-2-2-3-4-2-2-4-5-7-7-2-1-3-1-5-2-1-1-4-3-6-4s-6-1-8-1c-4-1-9 0-12 0h-1c-12 1-21 6-29 14-3 3-5 7-6 11-1 2-2 3-2 5l-1 1v5 18c-1-1-1-2-2-4h-2c0-5-1-9-1-14h0v-3-3c0-1-1-1-1-2v-1h0l-1 1v-1l-1 3v5h-2-1v-1h0c-1 2-1 4-1 5h-1c0-2 0-2-1-4h0v-1c0-2 0-3-1-4 0-4-1-10 1-14h0c1-1 1-2 1-3l1-1h0c0-2 1-3 2-4 0 0 0-1 1-2h2l1-1h2 0c2-1 2-2 3-3h0c1-1 2-3 3-3h1v-1c1 0 1-1 2-1v-1c1 1 1 2 2 2 5-4 10-6 16-8l4-1 9-1c0-1 0-1 1-1l2-1z" class="y"></path><path d="M165 104l2 3 1 1v3l-4-4c0-1 0-2 1-3z" class="AV"></path><path d="M160 101c2 1 3 2 5 3-1 1-1 2-1 3l-6-5 2-1z" class="n"></path><path d="M151 98l3-1c2 1 4 2 6 4l-2 1-7-4z" class="Ab"></path><path d="M130 93h9l1 2c-5-1-9-1-13 0 0-1 0-1 1-1l2-1z" class="P"></path><path d="M183 134c1 0 1-1 1-2l3 5c1 2 1 4 2 6h0l-1-1-1 1c-1-3-3-6-4-9z" class="t"></path><path d="M170 108l4 6h0c-1 1-1 1-1 2l-5-5v-3h1 1z" class="p"></path><path d="M143 93c4 1 7 2 11 4l-3 1c-4-2-7-2-11-3l-1-2h4z" class="AS"></path><path d="M114 97l4-1-6 3-11 6c-1 0-1 1-3 0h0c5-4 10-6 16-8z" class="k"></path><path d="M91 138v5 18c-1-1-1-2-2-4-1-3 0-5 0-8 0-4 1-8 2-11z" class="AM"></path><path d="M180 125h0c2 2 2 4 3 6h1l-1-3c0-1 1-1 1-1l2 1 1 2 2 7-1 1v-1h-1l-3-5c0 1 0 2-1 2l-5-9h2z" class="E"></path><path d="M188 137l-2-4s-1-1-1-2v-1h2l2 7-1 1v-1z" class="K"></path><path d="M176 113v1l2 2s1 1 1 2l2 2c0 2 2 5 3 7 0 0-1 0-1 1l1 3h-1c-1-2-1-4-3-6h0-2c-1-3-3-6-5-9 0-1 0-1 1-2h0l2-1z" class="z"></path><path d="M173 116c0-1 0-1 1-2l6 11h-2c-1-3-3-6-5-9z" class="AJ"></path><path d="M176 114l2 2s1 1 1 2l2 2c0 2 2 5 3 7 0 0-1 0-1 1-1-2-2-3-3-5-1-3-2-6-4-9z" class="f"></path><path d="M96 103c1 1 1 2 2 2h0c2 1 2 0 3 0 0 3-4 6-6 9-2 2-3 4-5 7 0 1 0 2-1 3v7c0 1-1 2-1 3s0 1-1 2v3c0 1 0 1-1 1v-3c0-1-1-1-1-2v-1h0l-1 1v-1l-1 3v5h-2-1v-1h0c-1 2-1 4-1 5h-1c0-2 0-2-1-4h0v-1c0-2 0-3-1-4 0-4-1-10 1-14h0c1-1 1-2 1-3l1-1h0c0-2 1-3 2-4 0 0 0-1 1-2h2l1-1h2 0c2-1 2-2 3-3h0c1-1 2-3 3-3h1v-1c1 0 1-1 2-1v-1z" class="w"></path><path d="M85 134l1-2c1-1 1-1 2-1 0 2-1 4-2 6 0-1-1-1-1-2v-1z" class="x"></path><path d="M85 125l2 2h1v3h-2-1v-2-3z" class="k"></path><path d="M88 127v3h-2c0-1 1-2 2-3z" class="AO"></path><path d="M84 130l1-2v2h1 2v1c-1 0-1 0-2 1l-1 2h0l-1 1v-1-4h0z" class="q"></path><path d="M98 105c2 1 2 0 3 0 0 3-4 6-6 9v-1h0c-2 1-3 2-4 3v1h-1 0l1-1h0v-1h0l1-1c1 0 1-1 1-2h1c-2 0-3 3-4 4h-1c3-4 6-8 9-11z" class="Aa"></path><path d="M96 103c1 1 1 2 2 2h0c-3 3-6 7-9 11h-1v-2h0l-1-1v-1h0c2-1 2-2 3-3h0c1-1 2-3 3-3h1v-1c1 0 1-1 2-1v-1z" class="Af"></path><path d="M87 113l1 1h0v2c0 3-2 6-3 9v3l-1 2h0-1v-1l-1-1v1l-1 1 2-10v-1h-1c0-1 1-2 2-4 0 1 0 1 1 1l2-3z" class="AX"></path><path d="M83 128h0 1v2h0-1v-1l-1-1h1z" class="J"></path><path d="M83 120h1v2l-1 1v5h-1v1l-1 1 2-10z" class="R"></path><path d="M85 112h2v1l-2 3c-1 0-1 0-1-1-1 2-2 3-2 4h1v1l-2 10 1-1v-1l1 1v1h1v4l-1 3v5h-2-1v-1h0c-1 2-1 4-1 5h-1c0-2 0-2-1-4h0v-1c0-2 0-3-1-4 0-4-1-10 1-14h0c1-1 1-2 1-3l1-1h0c0-2 1-3 2-4 0 0 0-1 1-2h2l1-1z" class="AX"></path><path d="M85 112h2v1l-2 3c-1 0-1 0-1-1-1 2-2 3-2 4-2 4-3 9-3 13v5l-2-2v-1-4c1-4 2-7 3-11 1-1 1-3 2-4l2-2 1-1z" class="R"></path><path d="M82 119h1v1l-2 10 1-1v-1l1 1v1h1v4l-1 3v5h-2-1v-1c-1-2-1-3-1-4v-5c0-4 1-9 3-13z" class="Aj"></path><path d="M80 136h1l1-1h1v2 5h-2l-1-6z" class="Af"></path><path d="M82 128l1 1v1h1v4l-1 3v-2h-1l-1 1h-1c0-2 0-4 1-6l1-1v-1z" class="AH"></path><path d="M82 128l1 1v1h1v4l-1 3v-2c0-1 0-1-1-2v-4-1z" class="AX"></path><path d="M173 86h1c1-1 3-1 4 0 0 1-1 1-1 2 2 0 6-1 8 0h1l-1 1 1 1v-1c1-1 1-1 3-1 0-1 3-1 4-1s1 0 2 1c2 0 4 0 6-1l2 1h1c2 0 6 0 7 2 2-1 2-1 3-2h6c2 0 3 0 5 1-2 0-3 2-4 3h2v1l-2 2v4l1 1c-1 1-1 3-1 4 1 0 2-1 2-1 0 1-1 1-1 1l-1 1-1 2c-1 0-1-1-2-2 0 1-1 1-1 1l-2 2-1 2-1 2-1 1c0 1-1 2-1 3l-4 8-1 2c-1 3-2 6-2 9h0l-1 4v7c1 3 2 6 2 9 0 2 0 5-1 7 0 2 0 6 1 8l1-1-1 7v8c-2 2-2 6-3 9h0l-1-2c0-1 0-1-1-2v5l-1 1c-1-1-2-1-2-1h0l1-1v-3-5l-1-1c-1-1-1-1-1-2-1-8-1-16-4-25h0l-4-12-1-2 1-1 1 1h0c-1-2-1-4-2-6h1v1l1-1-2-7-1-2-2-1c-1-2-3-5-3-7l-2-2c0-1-1-2-1-2l-2-2v-1l-2 1-4-6h-1-1l-1-1c0-1 0-1-1-2-1-2-2-5-2-9v-1c0-1 1-2 1-3l1-2 2-2 1-1c2 0 3 0 4-1z" class="J"></path><path d="M189 137c1 2 1 5 2 7-1 0-1-1-2-1-1-2-1-4-2-6h1v1l1-1z" class="An"></path><path d="M181 118l1 1 1-1c1 3 2 7 3 10h0l-2-1c-1-2-3-5-3-7v-2z" class="U"></path><path d="M187 143l1-1 1 1h0c1 0 1 1 2 1v3c1 2 4 13 4 15-1-1-1-4-2-5h-1 0l-4-12-1-2z" class="K"></path><path d="M187 143l1-1 1 1h0c1 0 1 1 2 1v3h0c-1 0-2-2-2-2h-1l-1-2z" class="f"></path><path d="M197 184c0-2-2-17-1-18 0 0 1 0 1 1v1 3h1v-1-1h1c0-1 0-2 1-3v4l1 10c-1 1 0 3-1 5h0v2 2 5l-1 1c-1-1-2-1-2-1h0l1-1v-3-5l-1-1z" class="M"></path><path d="M199 178c2 3 1 5 1 7h0-1c0 2-1 3-1 5v-5h1v-7z" class="u"></path><path d="M198 190c0-2 1-3 1-5h1v2 2 5l-1 1c-1-1-2-1-2-1h0l1-1v-3z" class="b"></path><path d="M200 166v4l1 10c-1 1 0 3-1 5 0-2 1-4-1-7-1-2-1-5-1-8v-1h1c0-1 0-2 1-3z" class="G"></path><path d="M194 119c0 1 1 1 1 2v4c-1 1-1 3-2 4v4c0 1 0 2 1 4h0l2 1v2h0c0 1 0 1 1 2l-1 1 1 3v1c2 7 1 13 3 19-1 1-1 2-1 3h-1l-7-32c-1-2-1-3-2-5 0-1 1-1 1-2-1-1-1-2-1-3 1-1 1-2 1-3 1-1 1-3 1-4h2l1-1z" class="AO"></path><path d="M194 137l2 1v2h0c0 1 0 1 1 2l-1 1c-1 0-2-5-2-6z" class="Aj"></path><path d="M194 119c0 1 1 1 1 2v4c-1 1-1 3-2 4v4c0 1 0 2 1 4h-1l-1-3c0-1 0-2-1-2v-1 2c1 1 0 2 0 3l1 1 1 1h0l-2-1c-1-2-1-3-2-5 0-1 1-1 1-2-1-1-1-2-1-3 1-1 1-2 1-3 1-1 1-3 1-4h2l1-1z" class="q"></path><path d="M194 119c0 1 1 1 1 2v4c-1 1-1 3-2 4v4l-1-1c0-1-1-1-1-3l1-4c0-1 1-3 1-5l1-1z" class="Ab"></path><path d="M203 139v7c1 3 2 6 2 9 0 2 0 5-1 7 0 2 0 6 1 8l1-1-1 7v8c-2 2-2 6-3 9h0l-1-2c0-1 0-1-1-2v-2-2h0c1-2 0-4 1-5l-1-10v-4c-2-6-1-12-3-19h0l1 1h0v-1c0-1 0-1-1-2h0l1-3h1 0v5c1 0 1 1 1 2h1c1-2 0-2 0-4l1-1c0-1 1-3 1-5z" class="Am"></path><path d="M203 139v7c1 3 2 6 2 9 0 2 0 5-1 7v7l-2 1v-5-12c0-3-1-6 0-9 0-1 1-3 1-5z" class="S"></path><path d="M204 169v-7c0 2 0 6 1 8l1-1-1 7v8c-2 2-2 6-3 9h0l-1-2c0-1 0-1-1-2v-2-2h0c1-2 0-4 1-5l-1-10c1-1 1-3 1-4h0l1-1v5l2-1z" class="n"></path><path d="M202 172c0 3 0 6 1 8-1 2-1 4-1 6l-2 1v-2h0c1-2 0-4 1-5l1-1v-7z" class="d"></path><path d="M202 165v5 2 7l-1 1-1-10c1-1 1-3 1-4h0l1-1z" class="P"></path><path d="M204 169v-7c0 2 0 6 1 8l1-1-1 7v8c-2 2-2 6-3 9h0l-1-2c0-1 0-1-1-2v-2l2-1c0-2 0-4 1-6 2-4 1-7 1-11z" class="AO"></path><path d="M200 187l2-1c0 2 0 3-1 5 0-1 0-1-1-2v-2z" class="W"></path><path d="M189 88c0-1 3-1 4-1s1 0 2 1c-1 1-1 3-2 4-2 1-3 2-4 4l-1 2h0c0 1 0 3-1 4 0 1 1 4 1 5-1 2 0 5 0 8 1 1 3 3 3 5 0 1 0 3-1 4 0 1 0 2-1 3 0 1 0 2 1 3 0 1-1 1-1 2l-2-5-3-9-2-6h0c-1-1-2-3-2-4l1-1h0 2c-1-2 0-5 0-8s1-7 3-9v-1c1-1 1-1 3-1z" class="AL"></path><path d="M181 107h2c0 3 1 5 1 8v3l-2-6h0c-1-1-2-3-2-4l1-1h0z" class="Ae"></path><path d="M181 107c1 2 1 3 1 5-1-1-2-3-2-4l1-1z" class="An"></path><path d="M186 118c1 0 2 0 2 2h0c0 2 1 3 2 4 0 1 0 2-1 3 0 1 0 2 1 3 0 1-1 1-1 2l-2-5c1-3-1-6-1-9z" class="S"></path><path d="M186 117c1-1 1-2 2-2 1 1 3 3 3 5 0 1 0 3-1 4-1-1-2-2-2-4h0c0-2-1-2-2-2v-1z" class="AB"></path><path d="M185 101h0l1-1 2-2c0 1 0 3-1 4 0 1 1 4 1 5-1 2 0 5 0 8-1 0-1 1-2 2-1-4-1-10-1-13l-1-1 1-2z" class="AA"></path><path d="M185 101h0l1-1 2-2c0 1 0 3-1 4h0c-1 2 0 3-1 5h0c-1-1-1-3-1-3l-1-1 1-2z" class="p"></path><path d="M189 88c0-1 3-1 4-1s1 0 2 1c-1 1-1 3-2 4-2 1-3 2-4 4l-1 2h0l-2 2-1 1h0l-1 2h0v2h-1v-6c0-3 1-7 3-9v-1c1-1 1-1 3-1z" class="k"></path><path d="M186 95c1 2 1 2 2 3h0l-2 2-1 1h0c0-2 0-4 1-6z" class="AJ"></path><path d="M190 90c0 2-1 4-1 6l-1 2c-1-1-1-1-2-3 1-1 1-2 2-3l2-2z" class="d"></path><path d="M189 88c0-1 3-1 4-1s1 0 2 1c-1 1-1 3-2 4-2 1-3 2-4 4 0-2 1-4 1-6l-2 2c0-2 0-2-2-3 1-1 1-1 3-1z" class="Q"></path><path d="M189 88c1 0 2-1 4 0-1 1-2 1-3 2l-2 2c0-2 0-2-2-3 1-1 1-1 3-1z" class="S"></path><path d="M188 107v2h2c1 0 1 1 2 2l-2 1 1 1 3 1c2 1 3 1 4 1h0l1-1h3l1 1h0c1 0 1 1 2 1l-2 2c0 1 1 1 1 2l1 1c-1 1-2 1-3 3l1 1 1 1h0 1c0-1 0-1 1 0-1 3-2 6-2 9h0l-1 4c0 2-1 4-1 5l-1 1c0 2 1 2 0 4h-1c0-1 0-2-1-2v-5h0-1l-1 3h0c1 1 1 1 1 2v1h0l-1-1h0v-1l-1-3 1-1c-1-1-1-1-1-2h0v-2l-2-1h0c-1-2-1-3-1-4v-4c1-1 1-3 2-4v-4c0-1-1-1-1-2l-1 1h-2c0-2-2-4-3-5 0-3-1-6 0-8z" class="AX"></path><path d="M195 125l-1 1v1 2 1c1 1 1 3 1 5 1 1 1 2 1 3l-2-1h0c-1-2-1-3-1-4v-4c1-1 1-3 2-4z" class="AT"></path><path d="M204 135c-1 0-1 0-1-1l-1 1h-1v-2h-1c-1 0-2-2-2-3-1-2 0-3 1-5 0 0 0-1 1-2l1 1h-1v1h1 2l1 1h0 1c0-1 0-1 1 0-1 3-2 6-2 9h0z" class="p"></path><path d="M188 107v2h2c1 0 1 1 2 2l-2 1 1 1 3 1c2 1 3 1 4 1h0l1-1h3l1 1h0c1 0 1 1 2 1l-2 2c0 1 1 1 1 2-1 0-1 0-3 1 0 0-1 0-2 1 0 1-1 2-2 3v6h-1v-6c0-1 1-2 1-3v-3l-2 2c0-1-1-1-1-2l-1 1h-2c0-2-2-4-3-5 0-3-1-6 0-8z" class="K"></path><path d="M199 114h3l1 1c-1 0-1 1-2 2s-2 1-3 1c1-1 1-2 1-3h-1 0l1-1z" class="N"></path><path d="M199 122v-1c0-2 2-2 4-3h0c0 1 1 1 1 2-1 0-1 0-3 1 0 0-1 0-2 1z" class="Ah"></path><path d="M191 113l3 1c2 1 3 1 4 1h1c0 1 0 2-1 3h-1c-1 0-2 1-3 1v-1h-2l-1-2v-3z" class="w"></path><path d="M191 116c2 0 2 0 4 1l-1 1h-2l-1-2z" class="a"></path><path d="M188 107v2h2c1 0 1 1 2 2l-2 1 1 1v3l1 2h2v1h0l-1 1h-2c0-2-2-4-3-5 0-3-1-6 0-8z" class="r"></path><path d="M188 109h2c1 0 1 1 2 2l-2 1c0-1-1-2-2-3z" class="Af"></path><path d="M173 86h1c1-1 3-1 4 0 0 1-1 1-1 2 2 0 6-1 8 0h1l-1 1 1 1c-2 2-3 6-3 9s-1 6 0 8h-2 0l-1 1c0 1 1 3 2 4h0l-1 1 2 5-1 1-1-1v2l-2-2c0-1-1-2-1-2l-2-2v-1l-2 1-4-6h-1-1l-1-1c0-1 0-1-1-2-1-2-2-5-2-9v-1c0-1 1-2 1-3l1-2 2-2 1-1c2 0 3 0 4-1z" class="R"></path><path d="M172 91l1-1c0 1 1 1 1 2h2v1c-1 1-1 1-2 1 0 2 0 3-1 4v-1c-1-1-2-1-3-1v-1c0-2 1-3 2-4z" class="AT"></path><path d="M178 93c1-1 2-2 3-2h1c-1 3-2 5-2 8l-1 2h0c-2 0-1-1-1-3h0v-5z" class="AC"></path><path d="M177 88c2 0 6-1 8 0l-2 2-1 1h-1c-1 0-2 1-3 2h-2v-1h-2c0-1-1-1-1-2 1 0 2-1 3-2h1z" class="AH"></path><path d="M185 88h1l-1 1 1 1c-2 2-3 6-3 9s-1 6 0 8h-2 0l-1 1v-2-7c0-3 1-5 2-8l1-1 2-2z" class="AS"></path><path d="M182 91l1-1c0 3-2 4-2 5v4 8h0l-1 1v-2-7c0-3 1-5 2-8z" class="Ac"></path><path d="M174 99h1c1 2 0 3 1 5 0 2 1 4 2 6 0-3 0-6 1-9l1-2v7 2c0 1 1 3 2 4h0l-1 1 2 5-1 1-1-1v2l-2-2c0-2-3-12-4-13s-1-4-1-6z" class="F"></path><path d="M180 106v2c0 1 1 3 2 4h0l-1 1 2 5-1 1-1-1c0-2-2-5-1-7v-5z" class="E"></path><path d="M170 96c1 0 2 0 3 1v1h-1v1h1 1c0 2 0 5 1 6s4 11 4 13c0-1-1-2-1-2l-2-2v-1l-2 1-4-6-2-6 2-2h-1c0-2 0-3 1-4z" class="K"></path><path d="M170 96c1 0 2 0 3 1v1h-1v1h1 0c-1 1-2 1-3 1 0 1 1 1 1 2 1 0 1-1 2-1 0 2 0 3 1 4l-1 1c0 2 1 2 1 4-1-2-2-3-2-5 0 0-1 0-1-1-1-1-1-2-1-4h-1c0-2 0-3 1-4z" class="Ah"></path><path d="M168 102l2-2c0 2 0 3 1 4 0 1 1 1 1 1 0 2 1 3 2 5l2 3-2 1-4-6-2-6z" class="a"></path><path d="M173 86h1c1-1 3-1 4 0 0 1-1 1-1 2h-1c-1 1-2 2-3 2l-1 1c-1 1-2 2-2 4v1c-1 1-1 2-1 4h1l-2 2 2 6h-1-1l-1-1c0-1 0-1-1-2-1-2-2-5-2-9v-1c0-1 1-2 1-3l1-2 2-2 1-1c2 0 3 0 4-1z" class="Q"></path><path d="M167 96c0-1 1-2 2-3l1 2v1c-1 1-1 2-1 4h1l-2 2c-1-2-1-4-1-6z" class="k"></path><path d="M168 92s0-1 1-2 2-3 4-3c1 0 2 0 3 1-1 1-2 2-3 2l-1 1c-1 1-2 2-2 4l-1-2c-1 1-2 2-2 3 0-1 0-2 1-4z" class="n"></path><path d="M168 92c1 0 1-1 2-2 1 0 1 1 2 1-1 1-2 2-2 4l-1-2c-1 1-2 2-2 3 0-1 0-2 1-4z" class="AO"></path><defs><linearGradient id="M" x1="213.058" y1="97.573" x2="197.594" y2="104.387" xlink:href="#B"><stop offset="0" stop-color="#141311"></stop><stop offset="1" stop-color="#2f221f"></stop></linearGradient></defs><path fill="url(#M)" d="M201 87l2 1h1c2 0 6 0 7 2 2-1 2-1 3-2h6c2 0 3 0 5 1-2 0-3 2-4 3h2v1l-2 2v4l1 1c-1 1-1 3-1 4 1 0 2-1 2-1 0 1-1 1-1 1l-1 1-1 2c-1 0-1-1-2-2 0 1-1 1-1 1l-2 2-1 2-1 2-1 1c0 1-1 2-1 3l-4 8-1 2c-1-1-1-1-1 0h-1 0l-1-1-1-1c1-2 2-2 3-3l-1-1c0-1-1-1-1-2l2-2c-1 0-1-1-2-1h0l-1-1h-3l-1 1h0c-1 0-2 0-4-1l-3-1-1-1 2-1c-1-1-1-2-2-2h-2v-2c0-1-1-4-1-5 1-1 1-3 1-4h0l1-2c1-2 2-3 4-4 1-1 1-3 2-4 2 0 4 0 6-1z"></path><path d="M195 99l2-2c1 0 3 1 4 1h0l-5 1h-1z" class="L"></path><path d="M211 100c-1-1-2-3-3-3-1-1-2-2-3-2h-4-1v-1c1 0 2-1 4 0 3 0 5 2 7 4l1 2h-1z" class="K"></path><path d="M201 98c2 0 3 1 5 2l1 1c2 2 2 3 3 5h-1 0l-1 1-1 1c-1-3-2-5-5-6v-1c-1 0-2 1-3 1 0-1 0-1 1-2 0-1 0-1 1-2h0z" class="AC"></path><path d="M207 101c2 2 2 3 3 5h-1 0l-1 1c-1-2-1-3-1-6z" class="J"></path><path d="M201 98c2 0 3 1 5 2l-1 1c-1 0-2-1-2-1h-1v1c-1 0-2 1-3 1 0-1 0-1 1-2 0-1 0-1 1-2h0z" class="AH"></path><path d="M202 102c3 1 4 3 5 6l1-1 1-1h0 1v4c-1 2-2 4-4 6h-1c-1 0-1-1-2-1h0l-1-1h-3c2-1 3-2 4-4 1 0 1-1 2-2h0c0-3-2-4-3-6z" class="Ah"></path><path d="M210 106v4c-1 2-2 4-4 6h-1c-1 0-1-1-2-1 2-3 3-4 4-7l1-1 1-1h0 1z" class="F"></path><path d="M213 104v4 1h0c0 1-1 2-1 2v1 1c0 1-1 2-1 3l-4 8-1 2c-1-1-1-1-1 0h-1 0l-1-1-1-1c1-2 2-2 3-3l-1-1c0-1-1-1-1-2l2-2h1c2-2 3-4 4-6l1 1v-1c1-2 1-4 2-6h0z" class="AJ"></path><path d="M205 116h1c1 0 1 0 2 1h0c-1 2-2 3-3 4h0 0l-1-1c0-1-1-1-1-2l2-2z" class="L"></path><path d="M213 104v4 1h0c0 1-1 2-1 2v1c-1 2-2 4-4 5h0c-1-1-1-1-2-1 2-2 3-4 4-6l1 1v-1c1-2 1-4 2-6h0z" class="D"></path><path d="M201 87l2 1h1c2 0 6 0 7 2 1 0 1 0 1 1v1c-3-2-5-3-9-2-3 1-5 2-7 4-1 3-3 4-4 6h-1 0c-1 0-1-1-1-1-2 4-2 6 0 10h-2v-2c0-1-1-4-1-5 1-1 1-3 1-4h0l1-2c1-2 2-3 4-4 1-1 1-3 2-4 2 0 4 0 6-1z" class="AG"></path><path d="M191 100c1-3 2-4 5-6-1 3-3 4-4 6h-1 0z" class="N"></path><path d="M201 87l2 1h1l-7 3c-2 0-3 0-4 1 1-1 1-3 2-4 2 0 4 0 6-1z" class="P"></path><path d="M193 92c1-1 2-1 4-1-3 3-6 4-7 8-2 4-2 6 0 10h-2v-2c0-1-1-4-1-5 1-1 1-3 1-4h0l1-2c1-2 2-3 4-4z" class="AT"></path><path d="M214 88h6c2 0 3 0 5 1-2 0-3 2-4 3h2v1l-2 2v4l1 1c-1 1-1 3-1 4 1 0 2-1 2-1 0 1-1 1-1 1l-1 1-1 2c-1 0-1-1-2-2 0 1-1 1-1 1l-2 2-1 2-1 2-1 1v-1-1s1-1 1-2h0v-1-4h-1c0-2-1-2-1-4h1l-1-2c1 0 1 0 1-1h0c-2-2 0-2 0-4v-1-1c0-1 0-1-1-1 2-1 2-1 3-2z" class="AC"></path><path d="M216 91h1 1 0l-3 2c0 1-1 0-2 0h0l1-1c1 0 1-1 2-1z" class="AG"></path><path d="M214 103v-1h1c0-2 0-2 1-4h0v4l1 4-2 2-1-1v-4z" class="AP"></path><path d="M212 93c1 1 1 2 1 3v3c0 1 0 3 1 4v4l1 1-1 2-1 2-1 1v-1-1s1-1 1-2h0v-1-4h-1c0-2-1-2-1-4h1l-1-2c1 0 1 0 1-1h0c-2-2 0-2 0-4z" class="L"></path><path d="M221 92h2v1l-2 2v4l1 1c-1 1-1 3-1 4 1 0 2-1 2-1 0 1-1 1-1 1l-1 1-1 2c-1 0-1-1-2-2 0 1-1 1-1 1l-1-4c0-2 1-4 1-6 1-2 2-3 4-4z" class="F"></path><path d="M217 96c1 1 1 1 1 2v1l1 1c0 2-1 3-1 5 0 1-1 1-1 1l-1-4c0-2 1-4 1-6z" class="n"></path><path d="M221 92h2v1l-2 2c-1 2-1 3-2 5l-1-1v-1c0-1 0-1-1-2 1-2 2-3 4-4z" class="x"></path><path d="M196 99l5-1c-1 1-1 1-1 2-1 1-1 1-1 2 1 0 2-1 3-1v1c1 2 3 3 3 6h0c-1 1-1 2-2 2-1 2-2 3-4 4l-1 1h0c-1 0-2 0-4-1l-3-1-1-1 2-1c-1-1-1-2-2-2-2-4-2-6 0-10 0 0 0 1 1 1h0 1 1l2-1h1z" class="AT"></path><path d="M196 99l5-1c-1 1-1 1-1 2-1 1-1 1-1 2h-1-1c-1 1-1 1-2 1l-1-1c1-1 1-2 2-3z" class="AX"></path><path d="M195 99h1c-1 1-1 2-2 3s-1 1-1 2 0 2 1 3l-1 1-1-1c0-1 0-3-1-5l-1-1 1-1h1 1l2-1z" class="r"></path><path d="M191 100h1 1l-2 2-1-1 1-1z" class="z"></path><path d="M199 102c1 0 2-1 3-1v1c1 2 3 3 3 6h0c-1 1-1 2-2 2h-1l1-1h0-2l1-1-1-1h0c-1 0-1 1-1 1-1 1-2 0-3 0v-1h2l1-1h-1 0c-1 0-2 1-3 0 0-1 1-3 2-4h1z" class="AX"></path><path d="M199 102c1 0 2-1 3-1v1c1 2 3 3 3 6h0c-2-2-3-3-4-5h-3c0 1 1 2 1 3h0c-1 0-2 1-3 0 0-1 1-3 2-4h1z" class="N"></path><path d="M190 99s0 1 1 1h0l-1 1 1 1c1 2 1 4 1 5l1 1v2h0c1 1 3 2 5 2l3-3h2 0l-1 1h1c-1 2-2 3-4 4l-1 1h0c-1 0-2 0-4-1l-3-1-1-1 2-1c-1-1-1-2-2-2-2-4-2-6 0-10z" class="R"></path><path d="M193 110l-2-2c-1-2-1-5-1-7l1 1c1 2 1 4 1 5l1 1v2z" class="N"></path><path d="M202 110h1c-1 2-2 3-4 4l-1 1h0c-1 0-2 0-4-1l-3-1-1-1 2-1c1 1 3 2 4 2 2 0 3 0 4-1l2-2h0z" class="AT"></path><path d="M452 262l2-1-1 10c-1 3 0 6-1 9 0 4-1 9-2 13l-2 4h0l-2 8v1h1v2c0 2-1 5-3 7-1 1-4 3-4 4l-1 1c-2 1-3 5-6 6l-2 2c-4 5-17 8-16 10l-2 1v1l-8 2-2 2v1 1l-1-1-1 1v2c-1 0-1 0-2 1 1 0 1 1 2 1l-1 2c-4-3-9-3-14-4h0l-16-2c-4 0-10 0-13 1h-4c-4 0-9-1-13 0l-25 2c-2 0-4 0-7-1h2 0v-1s1 0 2-1h2c-3-1-3-3-5-5 1 0 1-1 2-2 1 2 1 4 3 5l2-1c1 1 2 1 3 1s3 0 4-1v-1-1h2c2 1 4 2 7 2h3c1-1 1-1 2 0h0l5-2h1c1 0 2 0 3-1h0c1 0 1 1 1 1s1 0 1-1c3 0 6-2 9-2 2 0 4 0 6-1h1 2c5 1 8-1 13-4 0 0 2-1 3-1l1-1 5-3c2-3 5-4 7-6h0c0-1 1-1 2-2 1 0 1-1 2-2h1c1-2 2-3 3-4 2-2 3-3 6-5 1 0 2-2 3-3 1 0 2-1 3-2l2-2-1-1c0-1 1-1 2-2 1 0 2 0 3-1l4-2v-1c1 0 2-2 3-3l6-4h2 1l-1 2h0l3-1h0c1 0 2 0 3-1l-1-1h0c1 0 2 0 3-1h0 1v1h1l1-3v-1c0-1 1-2 1-3l2-10v-1l1-3h0l1 1v-5z" class="l"></path><path d="M342 341h1c1 0 2 0 3-1h0c1 0 1 1 1 1-2 1-4 2-7 2-1 0-1 0-1 1h-4l2-1h0l5-2z" class="s"></path><path d="M314 346h3c1 1 2 1 3 1h1c-3 0-6 0-9 1l3 1c-2 0-4 0-7-1h2 0v-1s1 0 2-1h2z" class="S"></path><path d="M410 336c-5 0-9 1-13 2h-1l1-1h1 1 2c1-1 2-1 3-1h1 0c1 0 3 0 4-1h3v-1h2c1 0 2-1 2-1v-1l1-1h0c2 0 4-1 5-1l1 1c-4 2-9 3-13 5z" class="AG"></path><path d="M370 345c-3-1-7 0-10 0v-1l10-1c3 1 7 0 10 1h-3v1h4-11z" class="M"></path><path d="M377 337l4-1c-1 1-2 1-4 2 1 0 3 0 3-1h1 2c-10 3-22 4-33 6v-1h1l26-5z" class="O"></path><path d="M424 322l1 1c-1 1-3 2-4 4h0c-5 3-11 7-17 6-1 0-2 1-3 1l6-3c6-2 12-5 17-9z" class="e"></path><path d="M383 335c2 0 4 0 7-1 3 0 7-2 10-3 2 1 5 1 7 0l-6 3c-6 2-12 3-18 3h-2-1c0 1-2 1-3 1 2-1 3-1 4-2l2-1z" class="H"></path><path d="M432 317c2-3 4-6 7-8l1 1 2-1h0l-5 6c-3 4-6 6-10 9l-6 3h0c1-2 3-3 4-4l-1-1c1 0 3-1 3-1 2-2 3-3 5-4z" class="Y"></path><defs><linearGradient id="N" x1="376.935" y1="338.096" x2="387.719" y2="346.397" xlink:href="#B"><stop offset="0" stop-color="#767779"></stop><stop offset="1" stop-color="#afaead"></stop></linearGradient></defs><path fill="url(#N)" d="M383 340h4v1h2 6 0c-1 1-2 1-3 1l-1 1h0c1 0 2 0 2 1h-13c-3-1-7 0-10-1l13-3z"></path><path d="M383 340h4v1h2l-6 1-1-1 1-1z" class="e"></path><path d="M309 341c1 0 1-1 2-2 1 2 1 4 3 5l2-1c1 1 2 1 3 1s3 0 4-1v-1-1h2c2 1 4 2 7 2h3c1-1 1-1 2 0l-2 1c-5 1-9 3-14 3h-1c-1 0-2 0-3-1h-3c-3-1-3-3-5-5z" class="AM"></path><path d="M314 344l2-1c1 1 2 1 3 1-2 1-3 2-5 1v-1z" class="AJ"></path><path d="M393 344c4 0 8-1 12-2l-2 2v1 1l-1-1-1 1v2c-1 0-1 0-2 1 1 0 1 1 2 1l-1 2c-4-3-9-3-14-4h0l-16-2h-3v-1h3 11-4v-1h3 13z" class="B"></path><path d="M391 347h2c2-1 3-1 5 0h1l1-1c0-1 0-1 1-2h1 0v1l-1 1v2c-1 0-1 0-2 1l-8-2z" class="b"></path><defs><linearGradient id="O" x1="387.97" y1="342.274" x2="378.081" y2="351.343" xlink:href="#B"><stop offset="0" stop-color="#929690"></stop><stop offset="1" stop-color="#b9b3b9"></stop></linearGradient></defs><path fill="url(#O)" d="M370 345h11l10 2 8 2c1 0 1 1 2 1l-1 2c-4-3-9-3-14-4h0l-16-2h-3v-1h3z"></path><path d="M447 296l1 1-2 8v1h1v2c0 2-1 5-3 7-1 1-4 3-4 4l-1 1c-2 1-3 5-6 6l1-1h0l3-4-3 2v-1h-1l1-1h-1l-1 1c-1 1-2 2-5 2h0c4-3 7-5 10-9l5-6h0l-2 1-1-1 6-10c1-1 1-2 2-2v-1z" class="R"></path><path d="M446 305v1c-1 3-3 5-5 8v-1c1-1 2-3 2-5l3-3z" class="Y"></path><path d="M447 296l1 1-2 8-3 3c0 2-1 4-2 5v1c0 1-3 4-4 4-2 1-3 2-5 3 2-2 5-4 7-7-1 1-1 1-2 1l5-6h0l-2 1-1-1 6-10c1-1 1-2 2-2v-1z" class="F"></path><path d="M447 296l1 1-2 8-3 3v-1c0-2 2-4 3-6 0-1 1-3 1-4v-1z" class="l"></path><defs><linearGradient id="P" x1="393.358" y1="328.253" x2="422.271" y2="341.497" xlink:href="#B"><stop offset="0" stop-color="#777471"></stop><stop offset="1" stop-color="#9c9e9f"></stop></linearGradient></defs><path fill="url(#P)" d="M423 331h0c2-1 4-1 5-3h3c-4 5-17 8-16 10l-2 1v1l-8 2c-4 1-8 2-12 2 0-1-1-1-2-1h0l1-1c1 0 2 0 3-1h0-6-2v-1l8-1c2 0 4 0 6-1s6-1 9-2c4-2 9-3 13-5z"></path><path d="M413 339v1l-8 2c-4 1-8 2-12 2 0-1-1-1-2-1h0l1-1c1 0 2 0 3-1h0l18-2z" class="AG"></path><path d="M423 316l12-8h1c-2 2-4 5-6 6 1 1 1 2 2 3-2 1-3 2-5 4 0 0-2 1-3 1-5 4-11 7-17 9-2 1-5 1-7 0 5-1 9-4 14-6l-1-2 10-7z" class="C"></path><path d="M423 316l12-8h1c-2 2-4 5-6 6l-12 9c-1 0-2 1-3 2h-1l-1-2 10-7z" class="i"></path><path d="M452 262l2-1-1 10c-1 3 0 6-1 9 0 4-1 9-2 13l-2 4h0l-1-1v1c-1 0-1 1-2 2l-6 10c-3 2-5 5-7 8-1-1-1-2-2-3 2-1 4-4 6-6h0c3-2 3-5 5-8-1 0-1 0-1-1h0c0-1-1-2-2-2h0v-1-1-1h2c1-1 1-2 1-3h-1-1v-2c1 0 2 0 3-1l-1-1h0c1 0 2 0 3-1h0 1v1h1l1-3v-1c0-1 1-2 1-3l2-10v-1l1-3h0l1 1v-5z" class="L"></path><path d="M445 299l4-13c0 2 0 5-1 7 0 1 0 2-1 3v1c-1 0-1 1-2 2z" class="e"></path><path d="M444 286h1v1h1c0 2-1 4-2 5 0 1 0 2-1 3v-1c-1 1-1 2-1 3s-1 2-1 3c-1 0-1 0-1-1h0c0-1-1-2-2-2h0v-1-1-1h2c1-1 1-2 1-3h-1-1v-2c1 0 2 0 3-1l-1-1h0c1 0 2 0 3-1h0z" class="U"></path><path d="M441 291c0-1 1-1 2-1h0c0 1 0 1-1 2 0 1 2 1 1 2s-1 2-1 3-1 2-1 3c-1 0-1 0-1-1h0c0-1-1-2-2-2h0v-1-1-1h2c1-1 1-2 1-3z" class="F"></path><path d="M438 294h2v2h-2v-1-1z" class="E"></path><defs><linearGradient id="Q" x1="447.294" y1="263.342" x2="455.046" y2="284.255" xlink:href="#B"><stop offset="0" stop-color="#737372"></stop><stop offset="1" stop-color="#8b8b8c"></stop></linearGradient></defs><path fill="url(#Q)" d="M452 262l2-1-1 10c-1 3 0 6-1 9 0 4-1 9-2 13l-2 4h0l-1-1c1-1 1-2 1-3 1-2 1-5 1-7v-4l3-15v-5z"></path><path d="M405 318h1c1 0 2-1 2-1l1-1 1 1-3 2h3v1 1c-1 0-2 1-3 1v2l-2 1h1 1v1l6-3 1 2c-5 2-9 5-14 6-3 1-7 3-10 3-3 1-5 1-7 1l-2 1-4 1h-1c-1-1-1 0-2 0 2-1 3-2 4-3l1-1s2-1 3-1l1-1 5-3c2-3 5-4 7-6 1 0 4-2 5-2s4-2 5-2z" class="AG"></path><path d="M403 325h0c1-2 2-2 4-3v2l-2 1h-2z" class="s"></path><path d="M388 328h3c-3 2-5 3-8 4h-1l1-1 5-3z" class="l"></path><path d="M382 332h1 2v1h-1c-1 1-1 1-2 1 2 0 3-1 4-1-1 1-2 2-3 2l-2 1-4 1h-1c-1-1-1 0-2 0 2-1 3-2 4-3l1-1s2-1 3-1z" class="i"></path><path d="M405 318h1c1 0 2-1 2-1l1-1 1 1-3 2c-5 4-11 6-16 9h-3c2-3 5-4 7-6 1 0 4-2 5-2s4-2 5-2z" class="F"></path><defs><linearGradient id="R" x1="398.013" y1="326.509" x2="400.925" y2="331.226" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#b0b0af"></stop></linearGradient></defs><path fill="url(#R)" d="M413 323l1 2c-5 2-9 5-14 6-3 1-7 3-10 3-3 1-5 1-7 1 1 0 2-1 3-2 1 0 2-1 3-1l6-3 8-4h2 1 1v1l6-3z"></path><path d="M403 325h2 1 1v1c-4 2-9 4-14 5l2-2 8-4z" class="U"></path><path d="M425 295c1 0 2-2 3-3l6-4h2 1l-1 2h0l3-1h0v2h1 1c0 1 0 2-1 3h-2v1 1 1h0c1 0 2 1 2 2h0c0 1 0 1 1 1-2 3-2 6-5 8h0-1l-12 8-10 7-6 3v-1h-1-1l2-1v-2c1 0 2-1 3-1v-1-1h-3l3-2-1-1-1 1s-1 1-2 1h-1c-1 0-4 2-5 2s-4 2-5 2h0c0-1 1-1 2-2 1 0 1-1 2-2h1c1-2 2-3 3-4 2-2 3-3 6-5 1 0 2-2 3-3 1 0 2-1 3-2l2-2-1-1c0-1 1-1 2-2 1 0 2 0 3-1l4-2v-1z" class="J"></path><path d="M419 310l5-3c4-2 7-3 10-6l1 1c-3 2-6 5-10 7-1 1-2 1-3 2-1 0-2 0-3-1z" class="X"></path><path d="M435 302c1-1 1-1 2-1h0l-1 2-3 3c-2 0-6 4-7 5-2 1-5 2-6 3s-2 1-3 2l-1-1c2-1 4-2 6-4 1-1 2-1 3-2 4-2 7-5 10-7z" class="g"></path><path d="M419 310c1 1 2 1 3 1-2 2-4 3-6 4-2 2-4 3-6 5v-1h-3l3-2c3-1 7-5 9-7z" class="AC"></path><defs><linearGradient id="S" x1="424.323" y1="295.796" x2="424.718" y2="300.455" xlink:href="#B"><stop offset="0" stop-color="#1b1d1a"></stop><stop offset="1" stop-color="#322c30"></stop></linearGradient></defs><path fill="url(#S)" d="M425 296h1c2-1 8-2 10-1-3 1-6 2-8 3s-3 1-5 1l-6 3-1-1c0-1 1-1 2-2 1 0 2 0 3-1l4-2z"></path><path d="M425 295c1 0 2-2 3-3l6-4h2 1l-1 2h0l3-1h0v2h1 1c0 1 0 2-1 3h-2v-2l-1-1c-1 0-2 0-3 1v1 1h3 0l-1 1c-2-1-8 0-10 1h-1v-1z" class="e"></path><path d="M438 292h2c1 1 0 1 0 2h-2v-2z" class="Z"></path><path d="M417 305c3-2 7-4 11-5h1c1-1 3-1 4-2-2 2-4 4-6 5s-5 2-7 4c-4 2-7 5-11 8v-1c0-1 1-2 2-3l6-6z" class="L"></path><path d="M438 295v1 1h0c1 0 2 1 2 2h0c0 1 0 1 1 1-2 3-2 6-5 8h0-1l-12 8h-1l3-2v-1c0-1 0-1 1-2s5-5 7-5l3-3 1 1 1-1c1-1 1-1 1-2s0-2-1-2-1 0-2 1h-1 0c0-1-1-1-1-2h1l3-3z" class="AY"></path><path d="M436 303l1 1c-1 2-4 4-6 6-2 1-4 3-6 4v-1c0-1 0-1 1-2s5-5 7-5l3-3z" class="AC"></path><path d="M412 306c1 0 2-1 3-2 1 1 1 1 2 1l-6 6c-1 1-2 2-2 3v1c-1 1-2 2-4 3-1 0-4 2-5 2s-4 2-5 2h0c0-1 1-1 2-2 1 0 1-1 2-2h1c1-2 2-3 3-4 2-2 3-3 6-5 1 0 2-2 3-3z" class="f"></path><path d="M412 306c1 0 2-1 3-2 1 1 1 1 2 1l-6 6s-1-1-2 0-2 2-4 3l-2 2c-1 0-2 1-3 2 1-2 2-3 3-4 2-2 3-3 6-5 1 0 2-2 3-3z" class="e"></path><path d="M416 315l1 1c1-1 2-1 3-2s4-2 6-3c-1 1-1 1-1 2v1l-3 2h1l-10 7-6 3v-1h-1-1l2-1v-2c1 0 2-1 3-1v-1c2-2 4-3 6-5z" class="X"></path><path d="M422 316h1l-10 7-6 3v-1h-1-1l2-1v-2c1 0 2-1 3-1h2c1-1 1-1 2-1 3-1 6-2 8-4z" class="E"></path><path d="M410 321h2c-2 1-3 2-5 3v-2c1 0 2-1 3-1z" class="J"></path><defs><linearGradient id="T" x1="253.393" y1="121.309" x2="248.801" y2="113.625" xlink:href="#B"><stop offset="0" stop-color="#c2bebe"></stop><stop offset="1" stop-color="#e3e0e1"></stop></linearGradient></defs><path fill="url(#T)" d="M280 92c2-1 3-1 6-1l-2 1c3 0 5 0 7-1h2v1l-6 2-3 1-3 2 2 1c2-1 5-1 7-1l1-1h1l-2 2h0l-14 7-1 2c-2 0-4 1-6 2 1 1 0 2 0 3v2l-7 4c-2 1-3 3-5 4h2c-2 1-3 2-4 3v1h1v3h-1c-1 1-2 1-3 2h0c-1 0-2 1-3 1h0c-1 1-2 2-3 2-3 3-6 5-9 9 0 1-3 4-3 5 1 0 1 0 2-1v1l-1 1-1 1c0 1 0 2 1 3h1 0v1c1 0 1 0 1 1v1h0 0c-2 0-5 2-7 4h-1l-1-1-3 4h-2l-1 1-2 2h-1l-2 4c-1 3-2 5-3 8v-3-1l-3 3h-1l-1 3c-1-1-1-1-1-2-1 2-2 4-3 5v1-8l1-7-1 1c-1-2-1-6-1-8 1-2 1-5 1-7 0-3-1-6-2-9v-7l1-4h0c0-3 1-6 2-9l1-2 4-8c0-1 1-2 1-3l1-1 1-2 1-2 2-2s1 0 1-1c1 1 1 2 2 2 1 1 1 2 2 3 0 1 0 2-1 3h1l2-1 1 1h1v2c1-1 2-3 3-4h1c1 1 1 2 1 3h0l3-3c1 1 2 1 3 1l2-2c2-1 4-2 7-4 2-2 5-4 7-6h3c1 0 2 0 3-1l1 1 2-2h0l1 1h1c1-1 1-2 2-3l14-4z"></path><path d="M268 100v1h1 1c0-1 1-1 1-1-1 2-3 2-5 3s-4 3-7 4l1-2c1-1 3-2 4-3s2-1 4-2z" class="R"></path><path d="M236 129v-1l3-3c3-2 5-6 9-6l-1 1-8 8c-1 0-2 1-3 1z" class="X"></path><path d="M271 98c5-2 9-4 13-3l-3 2-10 3s-1 0-1 1h-1-1v-1c1-1 2-1 3-2z" class="F"></path><path d="M247 120s1-1 2-1h2 2c-3 2-7 4-9 6l-3 3-3 3-2 1c0-2 2-3 3-4l8-8z" class="AH"></path><path d="M257 104h3l-1 1h1l-1 2-15 10-1-1c1-2 5-3 6-6 2-3 6-4 8-6z" class="J"></path><path d="M236 129c1 0 2-1 3-1-1 1-3 2-3 4-4 4-9 8-12 14-2 2-3 4-4 6l-1-1c0-1 1-2 1-3h1l-1-1c1-2 2-3 3-4l13-14z" class="l"></path><path d="M280 92c2-1 3-1 6-1l-2 1c3 0 5 0 7-1h2v1l-6 2-3 1c-4-1-8 1-13 3-1 1-2 1-3 2-2 1-3 1-4 2s-3 2-4 3h-1l1-1h-3c2-2 4-4 6-5h1c1-1 1-2 2-3l14-4z" class="R"></path><path d="M260 104c3-3 6-5 10-6h1c-1 1-2 1-3 2-2 1-3 1-4 2s-3 2-4 3h-1l1-1z" class="U"></path><path d="M284 92c3 0 5 0 7-1h2v1l-6 2-3 1c-4-1-8 1-13 3h-1l14-6z" class="f"></path><defs><linearGradient id="U" x1="262.361" y1="123.475" x2="272.589" y2="93.128" xlink:href="#B"><stop offset="0" stop-color="#b29e9c"></stop><stop offset="1" stop-color="#c4bbbe"></stop></linearGradient></defs><path fill="url(#U)" d="M290 97l1-1h1l-2 2h0l-14 7-1 2c-2 0-4 1-6 2l-5 3-11 7h-2-2c-1 0-2 1-2 1l1-1s1-1 1-2l5-3c6-4 13-8 20-12 3-2 5-4 9-4 2-1 5-1 7-1z"></path><path d="M290 97l1-1h1l-2 2h0l-14 7-2 1v-1-1c3-2 6-3 10-5 1 0 3 0 4-1 1 0 1-1 2-1z" class="o"></path><path d="M269 109c1 1 0 2 0 3v2l-7 4c-2 1-3 3-5 4h2c-2 1-3 2-4 3v1h1v3h-1c-1 1-2 1-3 2h0c-1 0-2 1-3 1h0c-1 1-2 2-3 2-3 3-6 5-9 9h-2c1-2 3-4 4-6 1 0 2-1 2-1 0-1 0-1-1-1l2-2c-1 0-3 0-4 1 1-1 1-2 1-3h-1 0l3-3 3-3c2-2 6-4 9-6l11-7 5-3z" class="M"></path><path d="M251 124h0c2-1 2-1 4-1l-6 6c-2 2-5 4-7 6l-1 1c0-1 0-1-1-1l2-2c1 0 2-2 3-2h0l1-3c1-1 3-3 5-4z" class="AJ"></path><path d="M244 125c2 0 3-1 4-2 2-1 3-1 5-2l-2 3c-2 1-4 3-5 4l-1 3h0c-1 0-2 2-3 2s-3 0-4 1c1-1 1-2 1-3h-1 0l3-3 3-3z" class="W"></path><path d="M238 131l3-3h1l1 1-1 1h0c1-1 2-2 4-2l-1 3h0c-1 0-2 2-3 2s-3 0-4 1c1-1 1-2 1-3h-1 0z" class="AM"></path><path d="M269 109c1 1 0 2 0 3v2l-7 4c-2 1-3 3-5 4-1 0-1 0-2 1-2 0-2 0-4 1h0l2-3c-2 1-3 1-5 2-1 1-2 2-4 2 2-2 6-4 9-6l11-7 5-3z" class="p"></path><path d="M264 112v1c0 1 0 1-1 1v1c0 1-3 3-4 3s-1 1-2 1c0 0-3 1-4 2-2 1-3 1-5 2-1 1-2 2-4 2 2-2 6-4 9-6l11-7z" class="n"></path><path d="M236 132l2-1h0 1c0 1 0 2-1 3 1-1 3-1 4-1l-2 2c1 0 1 0 1 1 0 0-1 1-2 1-1 2-3 4-4 6h2c0 1-3 4-3 5 1 0 1 0 2-1v1l-1 1-1 1c0 1 0 2 1 3h1 0v1c1 0 1 0 1 1v1h0 0c-2 0-5 2-7 4h-1l-1-1-3 4h-2l-1 1-2 2h-1l1-3h0c0-1 1-3 2-4 0 0 0-1 1-1v-2c1-1 1-2 1-3l-1 1v-1-1c0-1 1-1 1-1l1-4c-1 0-1-1-1-1 3-6 8-10 12-14z" class="G"></path><path d="M235 143h2c0 1-3 4-3 5 1 0 1 0 2-1v1l-1 1-1 1c-1 1-2 2-2 3h-1-2l3-3v-1c-1 0-2 1-3 1 0-2 4-5 6-7z" class="F"></path><path d="M220 163l4-5c1-1 1-2 3-3l2-2h2c-1 2-2 3-4 3v1c0 2-2 3-4 6l-1 1-2 2h-1l1-3h0z" class="M"></path><path d="M232 153c0-1 1-2 2-3 0 1 0 2 1 3h1 0v1c1 0 1 0 1 1v1h0 0c-2 0-5 2-7 4h-1l-1-1-3 4h-2c2-3 4-4 4-6v-1c2 0 3-1 4-3h1z" class="s"></path><path d="M232 153c0-1 1-2 2-3 0 1 0 2 1 3h1l-1 1c-1-1-2-1-3-1z" class="AC"></path><path d="M228 159c2-2 4-3 7-5h0v2h2 0 0c-2 0-5 2-7 4h-1l-1-1z" class="e"></path><path d="M236 132l2-1h0 1c0 1 0 2-1 3 1-1 3-1 4-1l-2 2c1 0 1 0 1 1 0 0-1 1-2 1-3 2-5 5-8 6-2 3-5 6-7 10l-1 1v-1-1c0-1 1-1 1-1l1-4c-1 0-1-1-1-1 3-6 8-10 12-14z" class="AB"></path><path d="M224 151l1-1c1-2 1-3 2-4s1-2 2-2c0-1 1-1 1-2 1-1 2-1 3-2 0 2-1 2-2 3-2 3-5 6-7 10l-1 1v-1-1c0-1 1-1 1-1z" class="AU"></path><path d="M236 132l2-1h0c-4 4-8 8-11 13-1 1-1 2-2 3-1 0-1-1-1-1 3-6 8-10 12-14z" class="AA"></path><path d="M238 134c1-1 3-1 4-1l-2 2c1 0 1 0 1 1 0 0-1 1-2 1-3 2-5 5-8 6 1-1 2-1 2-3l2-2c1-2 2-3 3-4z" class="W"></path><path d="M260 100l2-2h0l1 1c-2 1-4 3-6 5s-6 3-8 6c-1 3-5 4-6 6l1 1c-2 2-4 3-5 5l-5 5s-1 1-2 1c-2 2-5 6-7 7-1 0-2 1-2 1l-1-1 2-2h0c0-1 0-1 1-2 1-2 2-3 3-4 2-2 4-5 6-8-2 1-2 1-3 2l-2-1c0-1 1-1 1-2h1-1l-1-2 2-2h0l3-3c1 1 2 1 3 1l2-2c2-1 4-2 7-4 2-2 5-4 7-6h3c1 0 2 0 3-1l1 1z" class="X"></path><path d="M224 133h0l1 1c0-1 1-2 2-3v-1c2-1 2-2 3-4h1c1-2 2-2 2-3a30.44 30.44 0 0 1 8-8c-2 3-5 6-8 9-1 2-2 4-4 5-1 2-3 4-4 6-1 0-2 1-2 1l-1-1 2-2z" class="i"></path><path d="M241 115l8-5c-1 3-5 4-6 6l1 1c-2 2-4 3-5 5l-5 5s-1 1-2 1c-2 2-5 6-7 7 1-2 3-4 4-6 2-1 3-3 4-5 3-3 6-6 8-9z" class="R"></path><defs><linearGradient id="V" x1="233.057" y1="107.8" x2="251.188" y2="112.778" xlink:href="#B"><stop offset="0" stop-color="#88807e"></stop><stop offset="1" stop-color="#abacac"></stop></linearGradient></defs><path fill="url(#V)" d="M256 100c1 0 2 0 3-1l1 1c-5 3-11 6-16 10-3 2-5 4-8 6-1 1-1 3-2 3-2 1-2 1-3 2l-2-1c0-1 1-1 1-2h1-1l-1-2 2-2h0l3-3c1 1 2 1 3 1l2-2c2-1 4-2 7-4 2-2 5-4 7-6h3z"></path><path d="M231 118l3-3c0 2 0 2-1 3h-1s-1 1-2 1v-1h1z" class="R"></path><path d="M231 114l3-3c1 1 2 1 3 1l-3 3-3 3h-1l-1-2 2-2h0z" class="N"></path><path d="M225 135c2-1 5-5 7-7 0 1 0 1-1 2v1c-1 1-2 2-2 3-2 2-6 7-6 9h0c-1 1-2 2-3 4l1 1h-1c0 1-1 2-1 3l1 1c1-2 2-4 4-6 0 0 0 1 1 1l-1 4s-1 0-1 1v1 1l1-1c0 1 0 2-1 3v2c-1 0-1 1-1 1-1 1-2 3-2 4h0l-1 3-2 4c-1 3-2 5-3 8v-3-1l-3 3h-1l-1 3c-1-1-1-1-1-2-1 2-2 4-3 5v1-8l1-7-1 1c-1-2-1-6-1-8 1-2 1-5 1-7h1v-1c1 0 2 0 3-1l1-3c0 1 1 2 1 3l1 1 1-2c1-1 2-2 2-3 1-2 2-4 3-7 2-1 3-3 4-5l1-1h0s1-1 2-1z" class="AC"></path><path d="M220 147l1 1h-1c0 1-1 2-1 3l1 1-3 6-2-2c2-3 4-6 5-9z" class="g"></path><path d="M222 137l1 1c-1 4-5 7-7 11v1l-1-1c1-2 2-4 3-7 2-1 3-3 4-5z" class="X"></path><path d="M209 163h0 1c0-1 0-2 1-2h0c0-1 0-1 1-1v-1c1-1 1-2 1-3h2c0 1-1 2-1 4-1 0-1 1-1 1 0 2-1 2-2 4s-2 5-3 7l-1-1v-6l2-2z" class="l"></path><path d="M210 150c0 1 1 2 1 3l1 1-1 2-2 7-2 2v6l1 1-1 2c0 1-1 2-1 2h-1l1-7-1 1c-1-2-1-6-1-8 1-2 1-5 1-7h1v-1c1 0 2 0 3-1l1-3z" class="M"></path><path d="M210 150c0 1 1 2 1 3l1 1-1 2v-1c-2 0-2 4-3 4h0c0-2 1-4 1-6l1-3z" class="e"></path><path d="M205 155h1v-1c1 0 2 0 3-1 0 2-1 4-1 6-1 2-2 4-2 6v4l-1 1c-1-2-1-6-1-8 1-2 1-5 1-7z" class="z"></path><path d="M224 146s0 1 1 1l-1 4s-1 0-1 1v1 1l1-1c0 1 0 2-1 3v2c-1 0-1 1-1 1-1 1-2 3-2 4h0l-1 3-2 4-3 8v-3-1l-3 3h-1l-1 3c-1-1-1-1-1-2-1 2-2 4-3 5v1-8h1s1-1 1-2l1-2c1-2 2-5 3-7s2-2 2-4c0 0 0-1 1-1 0-2 1-3 1-4l2 2 3-6c1-2 2-4 4-6z" class="AJ"></path><path d="M215 156l2 2c-1 5-5 9-6 13l-1 1-1 2h0c0-2 1-2 1-3l1-3 1-1c0-1 0-2-1-2 1-2 2-2 2-4 0 0 0-1 1-1 0-2 1-3 1-4z" class="Y"></path><path d="M211 165c1 0 1 1 1 2l-1 1-1 3c0 1-1 1-1 3h0l1-2v3l1 1-1 1-1 3c-1-1-1-1-1-2-1 2-2 4-3 5v1-8h1s1-1 1-2l1-2c1-2 2-5 3-7z" class="K"></path><path d="M210 172v3l1 1-1 1-1 3c-1-1-1-1-1-2s1-2 1-4l1-2z" class="AJ"></path><path d="M211 171c1 0 1-1 2-2l1-3 1-1c0-1 1-1 1-2 0 0 1-1 2-1 0-2 1-5 3-6v1s-1 2-2 3v1c-1 1-1 1-1 2v1c-1 1-2 2-2 3 2-1 2-3 4-4l-1 3-2 4-3 8v-3-1l-3 3h-1l1-1-1-1v-3l1-1z" class="p"></path><path d="M216 167c2-1 2-3 4-4l-1 3-2 4-3 8v-3-1l-3 3h-1l1-1c1-3 4-6 5-9z" class="Aa"></path><path d="M217 106s1 0 1-1c1 1 1 2 2 2 1 1 1 2 2 3 0 1 0 2-1 3h1l2-1 1 1h1v2c1-1 2-3 3-4h1c1 1 1 2 1 3l-2 2 1 2h1-1c0 1-1 1-1 2l2 1c1-1 1-1 3-2-2 3-4 6-6 8-1 1-2 2-3 4-1 1-1 1-1 2h0l-2 2 1 1h0l-1 1c-1 2-2 4-4 5-1 3-2 5-3 7 0 1-1 2-2 3l-1 2-1-1c0-1-1-2-1-3l-1 3c-1 1-2 1-3 1v1h-1c0-3-1-6-2-9v-7l1-4h0c0-3 1-6 2-9l1-2 4-8c0-1 1-2 1-3l1-1 1-2 1-2 2-2z" class="a"></path><path d="M213 112c0 1 1 2 1 2l-2 2h-1c0-1 1-2 1-3l1-1z" class="D"></path><path d="M217 106s1 0 1-1c1 1 1 2 2 2 1 1 1 2 2 3 0 1 0 2-1 3l-1 3-1 1h-1-1c-1 0-1-1-2-1 0-2 1-3 2-4v-1c-1-1-2-1-3-1l1-2 2-2z" class="C"></path><path d="M217 112h1c0 1-1 2-1 3 0 0 1 1 1 2h-1c-1 0-1-1-2-1 0-2 1-3 2-4z" class="L"></path><path d="M224 112l1 1-4 7h-1c0 2 0 2-1 3v2h1v1 1h-3v1l-4 2s-1 0-1 1c-1 0-1 1-2 1v1h-1v-3-1l3-8c1-2 2-3 3-5 1 0 1 1 2 1h1 1l1-1 1-3h1l2-1z" class="E"></path><path d="M212 121l3-3 1 1c1 0 0 1 0 2l-1-1c-1 1-1 2-1 3-1 1-1 2-2 2v1 2l1 2s-1 0-1 1c-1 0-1 1-2 1v1h-1v-3-1l3-8z" class="f"></path><path d="M224 112l1 1-4 7h-1c-1 0-1 0-3 1h0c0 2-2 2-2 4h-1c0-1 1-2 1-2 0-1 1-1 1-2s1-2 0-2l-1-1-3 3c1-2 2-3 3-5 1 0 1 1 2 1h1 1l1-1 1-3h1l2-1z" class="N"></path><path d="M220 116l1-3h1v1c0 1-1 3-2 4v-2z" class="G"></path><defs><linearGradient id="W" x1="203.53" y1="142.389" x2="211.754" y2="143.329" xlink:href="#B"><stop offset="0" stop-color="#31100e"></stop><stop offset="1" stop-color="#121a1a"></stop></linearGradient></defs><path fill="url(#W)" d="M207 124v2l-1 1c0 1 1 2 1 3l-1-1c0 1-1 1 0 2h0v2h0l1-2v1 1 1l2-4v3h1v-1c1 0 1-1 2-1 0-1 1-1 1-1l4-2c-1 0-1 1-1 1 1 1 1 2 1 3-1 0-2 1-2 2v3l-3 2c0 1 0 1 1 2h1c0 2-3 7-4 9l-1 3c-1 1-2 1-3 1v1h-1c0-3-1-6-2-9v-7l1-4h0c0-3 1-6 2-9l1-2z"></path><path d="M213 130l4-2c-1 0-1 1-1 1 1 1 1 2 1 3-1 0-2 1-2 2v3l-3 2c0 1 0 1 1 2l-2-1c-2 2-3 6-4 8 0-1 1-4 1-5-1-1-1-2-1-3v-6l2-4v3h1v-1c1 0 1-1 2-1 0-1 1-1 1-1z" class="K"></path><path d="M216 129c1 1 1 2 1 3-1 0-2 1-2 2l-3 1h0l2-3c0-1 1-2 2-3z" class="AN"></path><path d="M212 135h0l3-1v3l-3 2c0 1 0 1 1 2l-2-1h0-1 0-1v-1c0-1 0-1 1-2l2-2z" class="AV"></path><path d="M226 115c1-1 2-3 3-4h1c1 1 1 2 1 3l-2 2 1 2h1-1c0 1-1 1-1 2l2 1c1-1 1-1 3-2-2 3-4 6-6 8-1 1-2 2-3 4-1 1-1 1-1 2h0l-2 2 1 1h0l-1 1c-1 2-2 4-4 5-1 3-2 5-3 7 0 1-1 2-2 3l-1 2-1-1c0-1-1-2-1-3 1-2 4-7 4-9h-1c-1-1-1-1-1-2l3-2v-3c0-1 1-2 2-2 0-1 0-2-1-3 0 0 0-1 1-1v-1h3v-1-1h-1v-2c1-1 1-1 1-3h1l4-7h1v2z" class="F"></path><path d="M228 121l1-1 2 1-8 8c-1 2-2 4-4 6-1 1-2 3-2 4l-2 2h0c0-1 0-2 1-3 0-2 1-3 2-4 2-3 4-6 7-8v-1-1h0l3-3z" class="Z"></path><path d="M231 121c1-1 1-1 3-2-2 3-4 6-6 8-1 1-2 2-3 4-1 1-1 1-1 2h0l-2 2 1 1h0l-1 1c-1 2-2 4-4 5-1 3-2 5-3 7 0 1-1 2-2 3v-1c1-5 3-9 6-13 1-1 0-2 0-3 2-2 3-4 4-6l8-8z" class="s"></path><path d="M226 115c1-1 2-3 3-4h1c1 1 1 2 1 3l-2 2 1 2h1-1c0 1-1 1-1 2l-1 1-3 3h0c-3 4-7 7-9 11 0 0-1 1-1 2v-3c0-1 1-2 2-2 0-1 0-2-1-3 0 0 0-1 1-1v-1h3v-1-1h-1v-2c1-1 1-1 1-3h1l4-7h1v2z" class="K"></path><path d="M217 128v-1h3c0 1-1 2-2 3l-1 1v1c0-1 0-2-1-3 0 0 0-1 1-1z" class="Ac"></path><path d="M226 115c1-1 2-3 3-4h1c1 1 1 2 1 3l-2 2 1 2h1-1c0 1-1 1-1 2l-1 1-1-2v1h-2c-1 1-1 2-2 2h0l1-3c0-1 1-2 2-4z" class="F"></path><path d="M230 111c1 1 1 2 1 3l-2 2c0-2 1-3 1-5z" class="e"></path><path d="M230 118h1-1c0 1-1 1-1 2l-1 1-1-2c0-1 0-1 1-1h1 1z" class="f"></path><path d="M520 97c6-3 12-5 19-6 2-1 3 0 4 0 4 0 9-2 13-1 2 1 5 1 7 1 3 0 5 1 7 2 3 1 6 2 9 4 1 0 2 1 3 1v1c2 3 5 4 7 7 1 1 1 2 2 4 0 1 0 1 1 2v1l-1 1c0-1-1-1-1-2l1-1c-1-1-5-4-6-4 3 2 6 6 6 9 1 3 1 4 2 7 0 4 0 6-3 9-1 0-2-1-3 0h0l-1-1 1-1h0-3c-1-1-1-1-1-2l1-2c-2-1-2-1-3-2v-1l-1-1c-1-2-2-2-3-3v3c-2 0-3 1-4 2s-2 1-3 2l-1-1c-1 3-2 5-2 8v1h0c-1 0-3 0-4 1l3 2h-4v-1c-3-1-5-1-7 0-1 0-2 1-3 2-2 1-3 3-4 5 0 0 0 1-1 1-1 2-1 4-2 6 0 1 0 3-1 5-1 3 0 7 0 11l-1-2h-1c-1 0-1-1-1-2l-1-9h-1v-6c-1-1-1-1-1-2v-4-1l-1 1-1-1c1-2 1-3 1-5 0-1 0-1-1-2-1 1-1 1-2 1l-1 1-1 2v-2h-2c-1 1-1 2-1 3l-1 1h0-2l-1 1c-1 0-1 0-2-1h2c-1-1-1-2-2-3-1 1-2 1-3 2h0v-1c1-1 0-1 0-2v1h-1c-2-1-2-3-3-4v1h0l-4-6-1-1-2-2h-3l-2-1c1-2 2-3 3-4v-1l-1-1s-1 0-2-1h0c-1 0-1 0-1-1v-1l-1-2c0-1-1 0-1-1h2c0 1 1 1 1 1l4-5h1l2-3 2-1c2-2 5-4 7-6z" class="y"></path><path d="M521 124l2 1c-1 1-1 1-1 2l2 2-1 2h-1s-1-1-2-1v2 2h-1c0-2 0-6 1-7s1-1 1-3z" class="r"></path><path d="M533 101c1-1 3-2 5-2h1c-3 1-7 3-9 5-5 3-8 7-11 11l-1-1c1-2 2-4 3-5 2-2 3-4 5-5 2-2 5-2 7-3z" class="J"></path><path d="M529 112c0 1 1 1 1 1-1 2-2 3-2 4l-1 1 1 2v1c0 1 0 1-1 2h-1c-1 0-1-1-2-1l-1 3-2-1c0 2 0 2-1 3 0-3 1-6 3-8 2-3 3-5 6-7z" class="Aa"></path><path d="M521 124c1-2 2-3 3-4h1l-1 2-1 3-2-1z" class="k"></path><path d="M527 118l1 2v1c0 1 0 1-1 2h-1c-1 0-1-1-2-1l1-2 2-2z" class="AL"></path><path d="M526 123l1-3h1v1c0 1 0 1-1 2h-1z" class="k"></path><path d="M518 114l1 1c-3 5-4 8-4 13 0 1 1 3 1 4v1h0l-4-6-1-1c0-4 1-7 4-11v1c1 0 2-2 3-2z" class="R"></path><path d="M515 115v1c-1 4-2 7-3 11l-1-1c0-4 1-7 4-11z" class="Ac"></path><path d="M538 106c1 0 1-1 1-1 1 0 3-1 4-1 4-3 8-4 13-5h4c5 0 9 1 14 2 3 2 6 3 10 5l1 1c3 2 6 6 6 9l-4-4v-1c-2-4-10-6-14-7-1-1-3-2-5-1-2-2-8 0-11 1-3 0-6 2-10 3-3 0-7 4-9 6-2 1-3 2-4 2l-2 2-3 3-1 1v-1l-1-2 1-1c0-1 1-2 2-4 0 0-1 0-1-1l9-6z" class="N"></path><path d="M538 106c1 0 0 1 1 2v-1h1l2-1c1-1 2-1 3-1l-2 1-5 3-5 3c-1 0-2 1-3 1 0 0-1 0-1-1l9-6z" class="r"></path><path d="M538 106c1 0 1-1 1-1 1 0 3-1 4-1 4-3 8-4 13-5h4c5 0 9 1 14 2h-6-6c-6 0-12 2-17 4-1 0-2 0-3 1l-2 1h-1v1c-1-1 0-2-1-2z" class="z"></path><path d="M534 115c1 0 2-1 4-2l-1 2h1v2c0 1-1 2-1 3h0c-1 1-1 2-1 4l-1 2c1 1 1 1 2 1h0c1 1 1 2 2 2h0-2v6h0c0-1 0-1-1-2-1 1-1 1-2 1l-1 1-1 2v-2h-2c-1 1-1 2-1 3l-1 1h0-2l-1 1c-1 0-1 0-2-1h2c-1-1-1-2-2-3-1 1-2 1-3 2h0v-1c1-1 0-1 0-2v-1-2-2c1 0 2 1 2 1h1l1-2-2-2c0-1 0-1 1-2l1-3c1 0 1 1 2 1h1c1-1 1-1 1-2l1-1 3-3 2-2z" class="Ae"></path><path d="M532 124l2-3v6h-2 0l1-1v-1l-1-1z" class="AX"></path><path d="M534 115c1 0 2-1 4-2l-1 2-2 3c-1 1-2 1-3 1 0-1 1-1 2-2v-2z" class="Ac"></path><path d="M537 115h1v2c0 1-1 2-1 3h0c-1 1-1 2-1 4l-1 2c0-2 0-3 1-4l-2-2 1-2 2-3zm0 12c1 1 1 2 2 2h0-2v6h0c0-1 0-1-1-2-1 1-1 1-2 1l-1 1v-1l1-1v-2s1-1 1-2c1 0 1-1 1-1l1-1h0z" class="U"></path><path d="M530 121c1-1 1-1 2-1h1c-1 1-2 3-3 4v1h0c1 0 1 0 2-1l1 1v1l-1 1h0 0c-1 1-5 3-6 3-1-1-1-1-2-1h0l-2-2c0-1 0-1 1-2l1-3c1 0 1 1 2 1h1c1-1 1-1 1-2l1-1c0 1 0 1 1 1z" class="AT"></path><path d="M526 124l2 2c-1 1-2 3-2 4-1-1-1-1-2-1 0-2 1-3 2-5z" class="AN"></path><path d="M528 121l1-1c0 1 0 1 1 1 0 2-1 3-2 5l-2-2 1-1c1-1 1-1 1-2z" class="K"></path><defs><linearGradient id="X" x1="524.265" y1="122.72" x2="524.557" y2="127.335" xlink:href="#B"><stop offset="0" stop-color="#3c201b"></stop><stop offset="1" stop-color="#2f2723"></stop></linearGradient></defs><path fill="url(#X)" d="M523 125l1-3c1 0 1 1 2 1h1l-1 1c-1 2-2 3-2 5h0l-2-2c0-1 0-1 1-2z"></path><path d="M532 127l1 1-3 3h0l1-1v1h1 1l-1 2c0 1 0 1 1 2l-1 2v-2h-2c-1 1-1 2-1 3l-1 1h0-2l-1 1c-1 0-1 0-2-1h2c-1-1-1-2-2-3-1 1-2 1-3 2h0v-1c1-1 0-1 0-2v-1-2-2c1 0 2 1 2 1h1l1-2h0c1 0 1 0 2 1 1 0 5-2 6-3z" class="AA"></path><path d="M523 136v-1-1h0c1 1 2 3 3 3s1-1 2-2h2c-1 1-1 2-1 3l-1 1h0-2l-1 1c-1 0-1 0-2-1h2c-1-1-1-2-2-3z" class="Q"></path><path d="M532 127l1 1-3 3h0l1-1v1h1 1l-1 2c0 1 0 1 1 2l-1 2v-2h-2-2-1l-1-1 1-1v1l1-1-1-1s-1 0-2 1h-1l1-1c-1-1-1-1-2-1l1-2h0c1 0 1 0 2 1 1 0 5-2 6-3z" class="Ab"></path><path d="M531 131h1 1l-1 2h-1c0 1-1 1-2 0v-1c0-1 1 0 2-1z" class="AT"></path><path d="M538 113c2-2 6-6 9-6 4-1 7-3 10-3 3-1 9-3 11-1 2-1 4 0 5 1-2 0-4-1-7 0 4 1 7 1 10 2-2 0-5 0-8 1h0c-2 1-2 1-4 1-1 0-2 1-3 1-1 1-2 1-3 2l-6 3-4 4h0c-1 1-3 2-4 4l-2 3-1 1-2 3c-1 0-1-1-2-2h0c-1 0-1 0-2-1l1-2c0-2 0-3 1-4h0c0-1 1-2 1-3v-2h-1l1-2z" class="M"></path><path d="M542 115h0c1-1 1-1 2-1l1-1h1l2-2 1-1c1-1 2-1 3-1l-6 4c-2 1-3 2-4 3-2 2-4 2-5 4 0-1 1-2 1-3l1-2 1 1 2-1z" class="F"></path><path d="M539 115c2-2 5-5 7-6h1 1c0 1 0 1-1 2h-1l-4 4-2 1-1-1z" class="R"></path><path d="M566 104c4 1 7 1 10 2-2 0-5 0-8 1h0c-2 1-2 1-4 1l2-2c-1 0-2 0-3 1-2 0-4 1-6 1s-3 1-4 3c-1 1-3 1-4 3h0l-1-1v1l-1-1v1l-1-1 6-4c4-2 9-3 14-5h0z" class="AW"></path><path d="M549 114h0c1-2 3-2 4-3 1-2 2-3 4-3s4-1 6-1c1-1 2-1 3-1l-2 2c-1 0-2 1-3 1-1 1-2 1-3 2l-6 3-4 4-1-1-1-1c1-1 2-1 3-2z" class="AP"></path><path d="M546 113l1 1v-1l1 1v-1l1 1c-1 1-2 1-3 2l1 1 1 1h0c-1 1-3 2-4 4l-2 3-1 1-2 3c-1 0-1-1-2-2h0c-1 0-1 0-2-1l1-2c0-2 0-3 1-4h0c1-2 3-2 5-4 1-1 2-2 4-3z" class="l"></path><path d="M537 120v1h1 1 0l-3 3c0-2 0-3 1-4z" class="J"></path><path d="M537 120c1-2 3-2 5-4 0 2-1 3-2 4 0 0 0 1-1 1h0-1-1v-1h0z" class="s"></path><path d="M537 127h2c0-2 0-3 1-4h1v2h1l-1 1-2 3c-1 0-1-1-2-2z" class="R"></path><path d="M520 97c6-3 12-5 19-6 2-1 3 0 4 0 4 0 9-2 13-1 2 1 5 1 7 1 3 0 5 1 7 2 3 1 6 2 9 4-3 0-6-2-9-2h-10c-4 0-7 1-11 2-2 0-3 1-5 1-1 0-4 1-5 1h-1c-2 0-4 1-5 2-2 1-5 1-7 3-2 1-3 3-5 5-1 1-2 3-3 5-1 0-2 2-3 2v-1c-3 4-4 7-4 11l-2-2h-3l-2-1c1-2 2-3 3-4v-1l-1-1s-1 0-2-1h0c-1 0-1 0-1-1v-1l-1-2c0-1-1 0-1-1h2c0 1 1 1 1 1l4-5h1l2-3 2-1c2-2 5-4 7-6z" class="D"></path><path d="M520 106c3-4 6-5 10-7h0l-3 2v1c-1 2-5 3-7 4z" class="G"></path><path d="M539 96c2-2 4-2 6-2 1 0 1 0 2-1h3v1 1h-4v1c1 0 2 0 3 1-2 0-3 1-5 1-1 0-4 1-5 1h-1c-2 0-4 1-5 2h-3-1l-2 1v-1l3-2h0c2-2 7-3 9-3z" class="b"></path><path d="M539 96c2-2 4-2 6-2 1 0 1 0 2-1h3v1 1h-4v1c1 0 2 0 3 1-2 0-3 1-5 1-1-1-2-1-3-1 1-1 2-2 3-2h1c1-1 3-1 4-1h1-4l-7 2z" class="L"></path><path d="M513 103c2-2 5-4 7-6 0 2-2 3-2 5-2 2-2 3-4 5-3 3-6 7-7 11l-1-1s-1 0-2-1h0c-1 0-1 0-1-1v-1l-1-2c0-1-1 0-1-1h2c0 1 1 1 1 1l4-5h1l2-3 2-1z" class="AM"></path><path d="M513 103c2-2 5-4 7-6 0 2-2 3-2 5-2 2-2 3-4 5h-1c-2 0-4 4-6 6v-1c0-3 3-3 3-5v-1c2-1 3-1 3-3h0z" class="x"></path><path d="M527 102l2-1h1 3c-2 1-5 1-7 3-2 1-3 3-5 5-1 1-2 3-3 5-1 0-2 2-3 2v-1c-3 4-4 7-4 11l-2-2h-3l-2-1c1-2 2-3 3-4v1h0c1 0 2 1 2 1h1v-1c0-5 7-11 10-14 2-1 6-2 7-4z" class="K"></path><path d="M510 121c0-1 0-1 1-1 1-2 1-3 2-5 1 0 2-2 3-2 0 1-1 2-1 2-3 4-4 7-4 11l-2-2h-3l-2-1c1-2 2-3 3-4v1h0c1 0 2 1 2 1h1z" class="E"></path><path d="M504 123c1-2 2-3 3-4v1 1c1 1 2 2 2 3h-3l-2-1z" class="w"></path><path d="M573 104c4 1 12 3 14 7v1l4 4c1 3 1 4 2 7 0 4 0 6-3 9-1 0-2-1-3 0h0l-1-1 1-1h0-3c-1-1-1-1-1-2l1-2c-2-1-2-1-3-2v-1l-1-1c-1-2-2-2-3-3v3c-2 0-3 1-4 2s-2 1-3 2l-1-1c-1 3-2 5-2 8v1h0c-1 0-3 0-4 1l3 2h-4v-1c-3-1-5-1-7 0-1 0-2 1-3 2-2 1-3 3-4 5 0 0 0 1-1 1-1 2-1 4-2 6 0 1 0 3-1 5-1 3 0 7 0 11l-1-2h-1c-1 0-1-1-1-2l-1-9h-1v-6c-1-1-1-1-1-2v-4-1l-1 1-1-1c1-2 1-3 1-5h0v-6h2 0l2-3 1-1 2-3c1-2 3-3 4-4h0l4-4 6-3c1-1 2-1 3-2 1 0 2-1 3-1 2 0 2 0 4-1h0c3-1 6-1 8-1-3-1-6-1-10-2 3-1 5 0 7 0z" class="a"></path><path d="M551 135c0 1 0 2 1 3-2 1-3 3-4 5 0-3 2-6 3-8z" class="G"></path><path d="M583 118c1 1 1 2 2 3 0 2 0 3-1 5-2-1-2-1-3-2v-1h1 1c1-1 1-2 1-3-1-1-1-1-1-2h0z" class="z"></path><defs><linearGradient id="Y" x1="570.266" y1="123.03" x2="576.737" y2="120.533" xlink:href="#B"><stop offset="0" stop-color="#7d6e6d"></stop><stop offset="1" stop-color="#938483"></stop></linearGradient></defs><path fill="url(#Y)" d="M569 125c1-1 2-3 3-4 2-1 3-2 5-2v3c-2 0-3 1-4 2s-2 1-3 2l-1-1z"></path><path d="M565 120l2-2h1c0-1 1-1 2-1h1c-4 4-6 7-8 12h0c-2 0-3-1-5-1h-1c1-1 3-1 4-1 1-1 2-3 2-4 1-1 1-2 1-3h1z" class="F"></path><path d="M576 106c2 0 3 1 4 2v1h0c-5-1-10 0-15 2-2 0-2-1-4-2 1 0 2-1 3-1 2 0 2 0 4-1h0c3-1 6-1 8-1z" class="i"></path><defs><linearGradient id="Z" x1="553.378" y1="133.601" x2="559.339" y2="133.26" xlink:href="#B"><stop offset="0" stop-color="#585150"></stop><stop offset="1" stop-color="#726567"></stop></linearGradient></defs><path fill="url(#Z)" d="M551 135c1-1 2-2 4-2 2-1 5-2 7-1 1 1 3 1 5 2h0c-1 0-3 0-4 1l3 2h-4v-1c-3-1-5-1-7 0-1 0-2 1-3 2-1-1-1-2-1-3z"></path><path d="M562 136v-1l-1-1h0c1 0 1 0 2 1l3 2h-4v-1z" class="Ae"></path><path d="M558 128h-1c-1 1-3 1-4 2l-1 1c-1 0-1 0-1 1h-1v-1s1 0 1-1l2-1c1-1 1-2 2-3 3-3 6-7 11-9-1 1-1 2-1 3h-1c0 1 0 2-1 3 0 1-1 3-2 4-1 0-3 0-4 1h1z" class="D"></path><path d="M557 128h-1c3-3 5-6 8-8 0 1 0 2-1 3 0 1-1 3-2 4-1 0-3 0-4 1z" class="L"></path><defs><linearGradient id="a" x1="576.719" y1="112.761" x2="580.523" y2="120.161" xlink:href="#B"><stop offset="0" stop-color="#828383"></stop><stop offset="1" stop-color="#a29f9d"></stop></linearGradient></defs><path fill="url(#a)" d="M566 117c1-1 2-2 4-2 1 0 2-1 3-1 3-1 7-2 10 0l3 2-1 2v1c1 1 1 3 1 5h-1v-3c-1-1-1-2-2-3l-3-3c-3-1-7 1-9 2h-1c-1 0-2 0-2 1h-1l-2 2c0-1 0-2 1-3z"></path><path d="M573 104c4 1 12 3 14 7v1l4 4c1 3 1 4 2 7 0 4 0 6-3 9-1 0-2-1-3 0h0l-1-1 1-1h0-3c-1-1-1-1-1-2l1-2c1-2 1-3 1-5v3h1c0-2 0-4-1-5v-1l1-2-3-2h1c1 0 2 1 2 1l1-1c-2-2-4-3-7-5h0v-1c-1-1-2-2-4-2-3-1-6-1-10-2 3-1 5 0 7 0z" class="J"></path><path d="M587 114c2 3 3 7 3 11 0 2-1 4-3 5h0c0-1 1-2 1-3 1-4 0-7-2-11l-3-2h1c1 0 2 1 2 1l1-1z" class="N"></path><path d="M586 116c2 4 3 7 2 11 0 1-1 2-1 3h-3c-1-1-1-1-1-2l1-2c1-2 1-3 1-5v3h1c0-2 0-4-1-5v-1l1-2z" class="R"></path><path d="M585 119l1-1v1l1 1c0 2 0 5-1 6 0 1-1 1-1 2l-1 1v1c-1-1-1-1-1-2l1-2c1-2 1-3 1-5v3h1c0-2 0-4-1-5z" class="F"></path><defs><linearGradient id="b" x1="555.144" y1="130.258" x2="544.49" y2="126.694" xlink:href="#B"><stop offset="0" stop-color="#bb8f8d"></stop><stop offset="1" stop-color="#b9b2b4"></stop></linearGradient></defs><path fill="url(#b)" d="M561 109c2 1 2 2 4 2-5 2-10 5-13 10v2l-1 1-1 1c0 1-1 2-1 3l-1 1-1 1v1l-3 7v1c0 3-1 6 0 9l1 2c0 1 0 3-1 5-1 3 0 7 0 11l-1-2h-1c-1 0-1-1-1-2l-1-9h-1v-6c-1-1-1-1-1-2v-4-1l-1 1-1-1c1-2 1-3 1-5h0v-6h2 0l2-3 1-1 2-3c1-2 3-3 4-4h0l4-4 6-3c1-1 2-1 3-2z"></path><path d="M548 118h2v1l-4 4-2-1c1-2 3-3 4-4z" class="Am"></path><path d="M558 111v1c-3 2-6 4-8 7v-1h-2 0l4-4 6-3z" class="Ai"></path><path d="M544 122l2 1-3 7h-1v-2c0-1-1-1-1-2l1-1 2-3z" class="Ao"></path><path d="M541 126c0 1 1 1 1 2v2h1c-1 3-2 7-2 10 0 2-1 5 0 7v-1s0-1 1-1c-1 2 0 6-1 8l-1-1v1h-1v-6c-1-1-1-1-1-2v-4-1l-1 1-1-1c1-2 1-3 1-5h0v-6h2 0l2-3z" class="Af"></path><path d="M541 140c0 2-1 5 0 7v-1s0-1 1-1c-1 2 0 6-1 8l-1-1c0-4 0-8 1-12z" class="AW"></path><path d="M541 126c0 1 1 1 1 2v2c-2 2-2 4-4 6v4l-1 1-1-1c1-2 1-3 1-5h0v-6h2 0l2-3z" class="AS"></path><path d="M537 135h0v-6h2c0 3-1 5-1 7v4l-1 1-1-1c1-2 1-3 1-5z" class="AV"></path><path d="M552 121v2l-1 1-1 1c0 1-1 2-1 3l-1 1-1 1v1l-3 7v1c0 3-1 6 0 9l1 2c0 1 0 3-1 5-1 3 0 7 0 11l-1-2h-1c-1 0-1-1-1-2l-1-9v-1l1 1c1-2 0-6 1-8v-1c1-1 1-5 1-6 2-6 5-13 9-17z" class="w"></path><path d="M544 148l1 2c0 1 0 3-1 5l-1-3c1-2 1-3 1-4z" class="C"></path><path d="M542 145v-1 13c0 2 2 6 1 7h-1c-1 0-1-1-1-2l-1-9v-1l1 1c1-2 0-6 1-8z" class="AH"></path><path d="M455 272c0-2 1-6 0-8-1-3 0-6 0-9h1v-5c0 2 0 4 1 6 1 1 0 5 0 6-1 5 0 9 0 14 0 1 0 3 1 5v3c-1 2-1 5-1 8l1 18v3c-1 6-1 12-1 17 0 2 0 5 1 8v2 5c-1 3 0 7 0 10 0 1 1 2 1 3h0c0 2-1 5 0 8 0 2 1 3 2 5l3 3-2 2v1c-1 0-2 1-2 1l-1-1v4 5c1 1 0 3 0 4v10 13l-3 16-6 20c-2 5-4 9-7 13-1 2-1 5-2 7-5 8-10 16-15 23v-2-1c0-2 3-11 4-13l1-6v-1l1-3 4-13c1-3 3-7 2-10 1-4 1-7 1-11 1-5 1-10 1-15v-7-9l-1-5c-2-8-6-15-11-22-4-7-11-11-18-14-5-2-9-4-13-6-5-2-9-3-13-4l1-1h0l1-1c5 1 10 1 14 4l1-2c-1 0-1-1-2-1 1-1 1-1 2-1v-2l1-1 1 1v-1-1l2-2 8-2v-1l2-1c-1-2 12-5 16-10l2-2c3-1 4-5 6-6l1-1c0-1 3-3 4-4 2-2 3-5 3-7v-2h-1v-1l2-8h0l2-4c1-4 2-9 2-13 1-3 0-6 1-9v5h0c1-1 1-1 1-2 1 1 1 2 1 3v-5z" class="y"></path><path d="M452 321l1-2c1-1 1-2 2-3 0 3-1 5-1 8h-2v-3z" class="u"></path><path d="M444 329c0 1 1 1 1 2h1l-4 3-1-2 3-3z" class="C"></path><path d="M407 349c1 0 3 1 4 2-1 1-2 1-4 1h-1c-1 0-2 0-3-1 1 0 2-1 3-1l1-1z" class="I"></path><path d="M456 331v9 3c-1 0-1 1-1 2v-1h0v-2-9l1-2z" class="C"></path><path d="M423 340l1-1c1 1 2 1 2 2s0 2-1 3h-1c1 0 1 1 2 1 0-1 0-1 1-1h1c-2 1-2 2-4 1h-1l1-2-1-1 1-1-1-1z" class="AI"></path><path d="M428 344h0l2-1c-2 2-3 4-4 6 0-1 0-2-1-3v2c-1 1-1 1-1 2-2-1-2-1-3-2 1-1 1-2 1-2 1 0 1 1 1 2h1v-3c2 1 2 0 4-1z" class="C"></path><path d="M423 340l1 1-1 1 1 1-1 2h1v3h-1c0-1 0-2-1-2l-2-2c0-2 2-3 3-4z" class="O"></path><path d="M451 363h0c2-1 3-2 4-4-1 5-1 8-1 12-1-1-1-3-1-4-1-1-2-3-3-4h1z" class="f"></path><path d="M452 324h2v2c0 2 1 3 2 5l-1 2c-1-2-1-3-1-4-1-1-2-1-4-1l-1 1v-1s0-1 1-1c0-1 1-2 2-3z" class="L"></path><path d="M452 324h2v2l-2-1v-1z" class="I"></path><path d="M452 324v1c0 2 0 2 1 3h1v1c-1-1-2-1-4-1l-1 1v-1s0-1 1-1c0-1 1-2 2-3z" class="G"></path><path d="M448 326l4-5v3c-1 1-2 2-2 3-1 0-1 1-1 1-1 1-2 1-3 2v1h-1c0-1-1-1-1-2 1-1 1-1 2-1 0-1 1-1 2-2z" class="H"></path><path d="M446 328c0-1 1-1 2-2l-2 4v1h-1c0-1-1-1-1-2 1-1 1-1 2-1zm-22 22c0-1 0-1 1-2v-2c1 1 1 2 1 3v1c0 1 1 1 2 1h1c-1 1-1 3-1 5l-5-1c0-1 1-1 1-2v-2h0v-1z" class="D"></path><path d="M424 351h1c1 1 1 2 1 3h-1l-1-1v-2z" class="H"></path><path d="M459 366c0 2 1 3 2 5l3 3-2 2v1c-1 0-2 1-2 1l-1-1v-4h1c-1-1-1-2-1-3-1-1-1-2 0-4z" class="Ai"></path><path d="M446 328l4-8c1-2 2-3 3-5 1-1 1-1 1-2l1-1v1 3c-1 1-1 2-2 3l-1 2-4 5c-1 1-2 1-2 2z" class="V"></path><path d="M442 390c1 2 2 4 3 5 1 5 1 9 2 14l1 13c0 2 0 4-1 6 0-4 1-9 0-13-1-2-1-5-1-8-1-2-1-5-2-7l-2-10z" class="B"></path><path d="M411 351c4 1 8 3 12 4l5 1c2 0 4 0 7 2h0v1c-1 0-2 0-3-1h-2c-3-1-5-2-8-2v1c-1 0-13-5-15-5 2 0 3 0 4-1z" class="c"></path><defs><linearGradient id="c" x1="455.407" y1="388.43" x2="450.328" y2="374.726" xlink:href="#B"><stop offset="0" stop-color="#131414"></stop><stop offset="1" stop-color="#303130"></stop></linearGradient></defs><path fill="url(#c)" d="M454 371c1 2 0 5 0 7 1 5 2 10 1 15h0l-1-1c0-6-3-10-4-15l-1-5c0 1 1 1 1 2s1 1 2 2v1s0 1 1 2c1-2 1-6 1-8z"></path><path d="M430 373c3 5 6 10 8 15 1 4 2 9 3 14v2h0l-1-3-1-5c-2-8-6-15-11-22l2-1z" class="s"></path><path d="M403 345c2 0 4 1 5 2 1 0 2 0 2 1 2-1 3-2 5-2h2c0 1 1 1 1 2-2 1-3 1-5 1s-4 0-6-1h-1l1 1-1 1c-1 0-2 1-3 1-1-1-2-1-2-3v-2l1-1 1 1v-1z" class="O"></path><path d="M410 348c2-1 3-2 5-2l-1 1v1h-4z" class="L"></path><path d="M402 345l1 1c1 1 3 2 3 4-1 0-2 1-3 1-1-1-2-1-2-3v-2l1-1z" class="y"></path><defs><linearGradient id="d" x1="425.037" y1="336.984" x2="420.715" y2="332.41" xlink:href="#B"><stop offset="0" stop-color="#757676"></stop><stop offset="1" stop-color="#9ea1a0"></stop></linearGradient></defs><path fill="url(#d)" d="M415 338c7-3 16-6 22-11 0-1 1-2 2-3l2-2c1-1 3-4 4-5v-1s1-1 2-1c-4 9-13 17-22 21-3 1-8 4-12 4v-1l2-1z"></path><defs><linearGradient id="e" x1="451.565" y1="397.96" x2="447.932" y2="386.055" xlink:href="#B"><stop offset="0" stop-color="#5d625f"></stop><stop offset="1" stop-color="#787c7b"></stop></linearGradient></defs><path fill="url(#e)" d="M447 381c5 9 8 22 5 32v-10c-1-4-2-8-3-11h-1l-1-1c-1-2-2-6-3-8l1-1h1 0l1-1z"></path><path d="M444 383l1-1c1 3 3 7 4 10h-1l-1-1c-1-2-2-6-3-8z" class="X"></path><defs><linearGradient id="f" x1="449.59" y1="416.355" x2="450.163" y2="394.028" xlink:href="#B"><stop offset="0" stop-color="#6d6d6d"></stop><stop offset="1" stop-color="#969696"></stop></linearGradient></defs><path fill="url(#f)" d="M447 391l1 1h1c1 3 2 7 3 11v10l-1 15c-1-1-1-3-1-5 1-2 1-5 0-8v-3c-1-6-1-13-3-20v-1z"></path><defs><linearGradient id="g" x1="441.46" y1="430.999" x2="454.533" y2="424.788" xlink:href="#B"><stop offset="0" stop-color="#201c1d"></stop><stop offset="1" stop-color="#4a4f4d"></stop></linearGradient></defs><path fill="url(#g)" d="M447 409c1 1 1 4 1 5 1 0 1 0 1-1h1v-1 3c1 3 1 6 0 8 0 2 0 4 1 5 0 1-1 3-1 5-2 6-3 13-6 18 0-3 1-6 2-9 1-4 1-9 1-14 1-2 1-4 1-6l-1-13z"></path><path d="M443 359c1 1 2 2 3 2v-1l2 1c0 1 0 1 2 1h1-1l1 1h-1c1 1 2 3 3 4 0 1 0 3 1 4h0c0 2 0 6-1 8-1-1-1-2-1-2v-1c-1-1-2-1-2-2s-1-1-1-2l-2-4v-1l-1-1c-1 0-1 1-1 1-1 0-2-2-3-2 0 0-1 0-2-1 1 0 1 0 2-1v-1l1-1v-2z" class="E"></path><path d="M447 368c1 0 1 1 2 2v-1-1h-1 1l3 8c-1-1-2-1-2-2s-1-1-1-2l-2-4z" class="N"></path><path d="M443 359c1 1 2 2 3 2v-1l2 1c0 1 0 1 2 1h1-1l1 1h-1-2c0 1 1 1 1 2v3h-1 1v1 1c-1-1-1-2-2-2v-1l-1-1c-1 0-1 1-1 1-1 0-2-2-3-2 0 0-1 0-2-1 1 0 1 0 2-1v-1l1-1v-2z" class="G"></path><path d="M443 361l2 2v1l1-1c0 1 1 3 1 4l-1-1c-1 0-1 1-1 1-1 0-2-2-3-2 0 0-1 0-2-1 1 0 1 0 2-1v-1l1-1z" class="L"></path><path d="M443 361l2 2h-1v1c-1 0-2 0-2-1v-1l1-1z" class="D"></path><defs><linearGradient id="h" x1="439.708" y1="367.175" x2="436.025" y2="375.804" xlink:href="#B"><stop offset="0" stop-color="#7e7e7f"></stop><stop offset="1" stop-color="#969696"></stop></linearGradient></defs><path fill="url(#h)" d="M431 362l1 2c3 2 6 4 9 7 2 2 4 7 6 10l-1 1h0-1l-1 1c1 2 2 6 3 8v1l-1-1-3-6c-1-3-3-4-4-7 0-1-1-1-1-3l-8-8c1-2 0-1 0-3 0 0 1-1 1-2z"></path><path d="M442 377c1 1 2 3 4 5h-1l-1 1c-1-2-2-4-2-6z" class="F"></path><path d="M438 375h1c-1-2-3-3-4-5h1c3 1 4 4 6 7 0 2 1 4 2 6s2 6 3 8v1l-1-1-3-6c-1-3-3-4-4-7 0-1-1-1-1-3z" class="f"></path><defs><linearGradient id="i" x1="445.003" y1="380.041" x2="429.567" y2="380.746" xlink:href="#B"><stop offset="0" stop-color="#424947"></stop><stop offset="1" stop-color="#635f61"></stop></linearGradient></defs><path fill="url(#i)" d="M427 366h3v1l8 8c0 2 1 2 1 3 1 3 3 4 4 7l3 6 1 1c2 7 2 14 3 20v1h-1c0 1 0 1-1 1 0-1 0-4-1-5-1-5-1-9-2-14-1-1-2-3-3-5-2-6-6-13-10-19-1-2-3-3-5-5z"></path><path d="M445 395v-1 1l1-2c0 2 1 4 1 6 1 5 2 9 2 14 0 1 0 1-1 1 0-1 0-4-1-5-1-5-1-9-2-14z" class="K"></path><path d="M441 347c2-1 3-1 5 0 0 1-1 2-2 2h-1c-1 2-2 3-3 5h0c0 1 1 3 0 4v2l2 2v1c-1 1-1 1-2 1-2 0-5 0-7-1l-1 1-1-2-9-5v-1c3 0 5 1 8 2h2c1 1 2 1 3 1v-1h0c0-2 0-2 1-4v-3-1h2c1-2 1-2 3-3z" class="O"></path><path d="M435 358h2 2 0c0 1 0 1-1 1v1c1 0 1 0 1 1-1 0-1 1-1 1h-1c-1-1-2-1-3-1-1-1-3-2-4-3h2c1 1 2 1 3 1v-1z" class="t"></path><path d="M422 357v-1c3 0 5 1 8 2 1 1 3 2 4 3 1 0 2 0 3 1-2 0-2 0-4-1h-1-1l2 2h0l-1 1-1-2-9-5z" class="B"></path><path d="M441 347c2-1 3-1 5 0 0 1-1 2-2 2h-1c-1 2-2 3-3 5h0v3h-1v-3h0c0-1 0-2 1-2v-2c-1 1-1 2-2 3h0 0-2v1-3-1h2c1-2 1-2 3-3z" class="f"></path><path d="M436 350h2l-2 3v1-3-1zm10-3c2 0 5 2 6 3v2h1 0c0 1 1 1 1 2h1v-4 7 1l-4 4h-1c-2 0-2 0-2-1l-2-1v1c-1 0-2-1-3-2v2l-1 1-2-2v-2c1-1 0-3 0-4h0c1-2 2-3 3-5h1c1 0 2-1 2-2z" class="D"></path><path d="M447 357l3 3c-1 0-1 1-2 1l-2-1v-1l1-2z" class="J"></path><path d="M450 360s1 0 2-1c1 0 1-1 2-3h0l1 2-4 4h-1c-2 0-2 0-2-1 1 0 1-1 2-1z" class="U"></path><path d="M448 351v2c-1 1-1 2-1 4l-1 2c-1-2-1-3-1-6 1-1 1-2 3-2z" class="F"></path><path d="M446 347c2 0 5 2 6 3v2c0 1 1 1 1 2l-2 2h-1-1c1-1 1-2 1-3h-2v-2-1c-1-1-3-1-5-1h1c1 0 2-1 2-2z" class="G"></path><path d="M443 349c2 0 4 0 5 1v1c-2 0-2 1-3 2 0 3 0 4 1 6v1 1c-1 0-2-1-3-2v2l-1 1-2-2v-2c1-1 0-3 0-4h0c1-2 2-3 3-5z" class="V"></path><path d="M443 359v-1-1h0l-1-1v-2c1 0 2 0 3-1 0 3 0 4 1 6v1 1c-1 0-2-1-3-2z" class="B"></path><path d="M453 271v5h0c1-1 1-1 1-2 1 1 1 2 1 3v7c0 10-3 22-8 31-1 0-2 1-2 1v1c-1 1-3 4-4 5l-2 2c-1 1-2 2-2 3-6 5-15 8-22 11-1-2 12-5 16-10l2-2c3-1 4-5 6-6l1-1c0-1 3-3 4-4 2-2 3-5 3-7v-2h-1v-1l2-8h0l2-4c1-4 2-9 2-13 1-3 0-6 1-9z" class="E"></path><path d="M453 271v5h0c1-1 1-1 1-2 1 1 1 2 1 3v7h-1c0-1 1-6 0-7h0c-1 4-2 9-2 13l-1 4h-1v-1c1-4 2-9 2-13 1-3 0-6 1-9z" class="K"></path><path d="M452 290c1 2-1 8-2 11-1 2-1 5-3 7v-2h-1v-1l2-8h0l2-4v1h1l1-4z" class="i"></path><path d="M450 293v1h1l-4 12h-1v-1l2-8h0l2-4z" class="U"></path><defs><linearGradient id="j" x1="449.048" y1="443.511" x2="422.641" y2="453.073" xlink:href="#B"><stop offset="0" stop-color="#50504f"></stop><stop offset="1" stop-color="#8a8d8d"></stop></linearGradient></defs><path fill="url(#j)" d="M440 401l1 3h0v-2c2 5 2 10 2 16 1 4 0 10 0 14-1 9-2 18-5 27-2 6-4 12-7 18-1 4-3 8-5 12 0-2 3-11 4-13l1-6v-1l1-3 4-13c1-3 3-7 2-10 1-4 1-7 1-11 1-5 1-10 1-15v-7-9z"></path><path d="M440 401l1 3h0v-2c2 5 2 10 2 16v7c-1-2-1-4-2-6 0-3 0-6-1-9h0v-9z" class="Y"></path><path d="M399 349c1-1 1-1 2-1 0 2 1 2 2 3s2 1 3 1h1c2 0 14 5 15 5l9 5c0 1-1 2-1 2 0 2 1 1 0 3v-1h-3c2 2 4 3 5 5-1-1-3-1-4-2-1-2-4-6-6-6h-1v1c2 0 3 2 4 3s3 2 3 4v1l2 1-2 1c-4-7-11-11-18-14-5-2-9-4-13-6-5-2-9-3-13-4l1-1h0l1-1c5 1 10 1 14 4l1-2c-1 0-1-1-2-1z" class="F"></path><path d="M386 348c5 1 10 1 14 4h0v1l-15-4h0l1-1z" class="u"></path><path d="M400 352l9 3c4 1 10 4 14 7l4 4c2 2 4 3 5 5-1-1-3-1-4-2-1-2-4-6-6-6h-1v1c2 0 3 2 4 3s3 2 3 4v1c-7-9-18-15-28-19v-1z" class="H"></path><defs><linearGradient id="k" x1="414.903" y1="361.011" x2="419.584" y2="354.854" xlink:href="#B"><stop offset="0" stop-color="#797979"></stop><stop offset="1" stop-color="#9e9e9e"></stop></linearGradient></defs><path fill="url(#k)" d="M399 349c1-1 1-1 2-1 0 2 1 2 2 3s2 1 3 1h1c2 0 14 5 15 5l9 5c0 1-1 2-1 2 0 2 1 1 0 3v-1h-3l-4-4c-4-3-10-6-14-7l-9-3h0l1-2c-1 0-1-1-2-1z"></path><path d="M423 362c2 0 5 2 7 4h-3l-4-4z" class="U"></path><path d="M449 328v1l1-1c2 0 3 0 4 1 0 1 0 2 1 4v9 2h0v1 5 4h-1c0-1-1-1-1-2h0-1v-2c-1-1-4-3-6-3-2-1-3-1-5 0s-2 1-3 3h-2v1 3c-1 2-1 2-1 4-3-2-5-2-7-2 0-2 0-4 1-5h-1c-1 0-2 0-2-1v-1c1-2 2-4 4-6 3-4 7-6 12-9l4-3v-1c1-1 2-1 3-2z" class="y"></path><path d="M436 350c0-1 1-2 2-3s1 0 3 0c-2 1-2 1-3 3h-2z" class="C"></path><path d="M455 345v5 4h-1c0-1-1-1-1-2h0c0-3 0-5 2-8v1z" class="H"></path><path d="M429 351c0 1 0 1 1 2l-1 1h1c1 1 2 2 3 2 2-1 1-2 1-3s1-2 2-2v3c-1 2-1 2-1 4-3-2-5-2-7-2 0-2 0-4 1-5zm18-17l4 2v2c0 1-1 2-2 2-1 1-1 1-2 1h0c0-1 0-1 1-1 0-1 0-1 1-2h-1c-2 0-2 1-4 2-1 0-2 1-3 1l1-3 3-1 2-2h-2l2-1z" class="C"></path><path d="M447 334l4 2v2c-1 0-1-1-2-1h-4l2-2h-2l2-1z" class="c"></path><path d="M445 335h2l-2 2-3 1-1 3-2 1s-1 0-2 1c0 0-1 2-2 2h-1c-2 2-4 6-4 9h-1l1-1v-1c0-5 5-10 9-14 1 0 5-2 6-3z" class="M"></path><path d="M442 338l-1 3-2 1-2-1c2-1 3-2 5-3z" class="D"></path><path d="M437 341l2 1s-1 0-2 1c0 0-1 2-2 2h-1l3-4z" class="C"></path><path d="M449 328v1h0c-1 2-4 4-4 5h2l-2 1c-1 1-5 3-6 3-4 4-9 9-9 14v1c-1-1-1-1-1-2h-1c-1 0-2 0-2-1v-1c1-2 2-4 4-6 3-4 7-6 12-9l4-3v-1c1-1 2-1 3-2z" class="b"></path><path d="M429 351c0-1-1-1-1-2v-2c1-3 5-7 8-8 1-1 2-1 3-1-4 4-9 9-9 14v1c-1-1-1-1-1-2z" class="B"></path><path d="M197 548c2 2 1 6 2 9 0-2 0-4 1-6 1 6 2 11 4 17l1-1c0 1 0 1 1 2v1h1 0v1l1 1c1 1 3 4 4 6l4 6c2 3 5 6 7 9l1 1 2 2c1 0 1 0 2 1h0c1 0 1 1 2 1s2 1 2 2c2 1 4 3 5 4v-1l-2-2c0-1 0-1-1-1v-1h0s1 0 1 1h2c1 1 2 1 3 2v-1c4 3 8 6 11 9 2 1 5 2 6 4h0c-1 0-1-1-2-1h-1c1 1 2 1 3 2v1l12 6c0-1 0-2 1-2l25 10c2 1 5 2 6 3v1c3 1 6 1 9 2 1 0 3 1 4 1h1c1 1 2 1 3 2 5 0 10 1 15 1h3 3c1-1 2-1 3-1l8 1v1h3c2 0 6 1 8 2h-14v1h-4c-4-1-9 0-13 0h-17c-4-1-10 0-14 0h-2-7-27-15c-4 0-7-1-11-1s-7 1-11 1h-11l-2 2c-1 0-2 0-2-1-2 0-3 1-5 0h-3c-3 0-6 0-8-1-5-1-12 0-17 0 0 1 0 1 1 2h0c0 1 1 2 1 2 0 1 0 2-1 3l-1-1c-1-1-1-1-2-1h-1-1-1c-1-1-2-1-3-1s-1 0-1-1l-1-1-1 1c-2 0-2-1-4-1v-1c0-1-2-1-3-1h-2c1 1 0 1 0 2h0c-2 0-3-1-5 0l-3 1c-1 1-1 1-1 3-1 1-1 2-1 3h-1l-1-3c-1-1-1-2-2-2-2-1-4-1-6-1h0l-1-1c-1-1-2 0-3 0l-1-1h-4v-2h-1c-2-1-3 0-5-1h0c-3 0-6-1-9-1l-9-3c-1 0-2-1-3-2v-1c1 0 3 1 5 1l10 2h1 5 8l-1-2c5 0 9-1 14-2h0l3-1c3-1 7-2 10-4 1 0 1 0 2-1 0-1-1-1-1-2l1-1 1-1 1-1-1-1h1v-1c0-1 0-2 1-2 0-1 1-1 1-1 2 0 4-1 6-3h0 0 1c2-2 5-6 6-8h1c1-3 4-3 4-7h0c0-2 1-3 2-4v-1l2-1c0-1 1-2 1-3h1l2-2h1v-1c1-2 2-3 3-4 0-1 1-2 1-2 0-1 0-1 1-2 1-4 2-9 2-14h0c1-3 1-7 1-10l-1-8z" class="y"></path><path d="M240 633l-2 6c-1-3-1-4 0-7l2 1z" class="a"></path><path d="M229 637h4c-1 1-1 1-3 2h-3v-2h0l1 1 1-1z" class="F"></path><path d="M233 622c1 2 1 4 1 5l-1 1h0l-1-1v-4h-1l2-1z" class="c"></path><path d="M201 579l3 8h-1-1c-1-1-1-4-1-6v-2z" class="t"></path><path d="M177 632c0 1 0 1 1 2-2 2-5 5-8 5 0-1 3-2 4-3s1-1 1-2l2-2z" class="I"></path><path d="M204 591l1 1-2 1h0c0 1 1 1 1 1h1c-2 0-3 1-4 3l-2-1 1-3 2-1 2-1z" class="C"></path><path d="M203 587h1c1 1 1 3 2 4 0 1 0 1-1 1l-1-1-2 1v-5h1z" class="I"></path><path d="M202 587h1c0 1 0 3 1 4h0l-2 1v-5z" class="V"></path><path d="M342 639l8 1v1l-17-1h3 3c1-1 2-1 3-1z" class="R"></path><path d="M188 639h-4-1c0-2 1-3 3-4 1 1 2 2 2 4zm-2-5c2-1 3-3 4-4h0s1 0 1 1c-1 1 0 2 0 4 0 0-1 1-2 1-1-1-1-2-2-2h-1z" class="C"></path><path d="M203 645c2-1 5-1 8-1 2 0 3-1 4 0l-2 2c-1 0-2 0-2-1-2 0-3 1-5 0h-3z" class="m"></path><path d="M204 612h1c2 1 3-2 5-2 1 1 2 1 3 1-3 0-4 1-6 3-2 1-3 3-4 5v-1c0-1 1-2 2-3 0-1-1-2-1-3z" class="V"></path><path d="M118 638h5c2 0 5 0 7 1s4 0 6 1c-5 0-11 0-15-1l-3-1z" class="H"></path><path d="M200 618h2v-1c0-2 1-2 1-4v-1h1c0 1 1 2 1 3-1 1-2 2-2 3v1 1h-1v-1h-1v3c-1 1 0 2 0 3s1 1 1 2c-1 0-1-1-2-1v-8z" class="AI"></path><path d="M186 634h1c1 0 1 1 2 2 1 0 2-1 2-1l3 4h-6c0-2-1-3-2-4v-1z" class="B"></path><path d="M232 627l1 1h0l1-1c0 4 0 7-1 10h-4c1 0 1-1 2-2 1-2 1-5 1-8z" class="J"></path><path d="M240 627v-1-3h0v-1l6 3-1 1c2 1 3 1 4 2v1c-1-1-2-1-3-2-2 0-3 0-5-1h0l-1 1z" class="AI"></path><path d="M194 629c-1-1-1-3-1-4 1-1 1-1 1-2v-1-1c1 0 1 0 1-1v-1l1-1 1-2v-1h0v-1c1-1 2-2 2-3 0-2 0-2 1-3h2l-2 1c0 1 1 1 1 2v2l-2 1v-1c-1 1-1 2-2 3h0c-1 2-1 5-2 7s-1 4-1 6zm52-4l16 9h-1l-8-3c-1-1-3-2-4-3s-2-1-4-2l1-1z" class="a"></path><path d="M197 616c1 2 1 4 1 5h-1-1c0 3 0 3 2 6-1 1-2 2-3 2l2 2h-2l-1-2c0-2 0-4 1-6s1-5 2-7z" class="U"></path><path d="M194 629c0-2 0-4 1-6v6l2 2h-2l-1-2z" class="O"></path><defs><linearGradient id="l" x1="221.675" y1="636.529" x2="227.27" y2="631.178" xlink:href="#B"><stop offset="0" stop-color="#797a7b"></stop><stop offset="1" stop-color="#949394"></stop></linearGradient></defs><path fill="url(#l)" d="M224 630h3c1 1 0 2 0 3l-1 3c0 1-1 2-2 2s-2 1-3 0c2-3 3-5 3-8z"></path><path d="M199 603c2-2 4-4 7-5 0 1-1 2-2 3h-1l-2 2c0 1 0 1-1 1-2 3-3 6-6 9h0c-2 2-3 6-5 7l10-17z" class="C"></path><path d="M174 633h0s1 0 1 1 0 1-1 2-4 2-4 3c-1 1-1 1-2 1v-1h-1l-1 1h-2-5l1-1 2-1 1 1c1-1 2-1 4-2h0 1c2-1 3-2 5-3l1-1z" class="N"></path><path d="M168 637c2-1 3-2 5-3 0 2-1 2-3 3h-2z" class="G"></path><path d="M123 642l23 1h-2c-1 1-1 1-1 2h-3 0c-1 1-2 1-3 1-1-1-2 0-3 0l-1-1h-4v-2h-1c-2-1-3 0-5-1h0z" class="W"></path><path d="M245 634c2 0 3 1 5 1h4c3 1 7 3 11 4 2 1 4 1 6 2h-2-3c-6-1-11-2-17-5-1 0-3-1-4-2z" class="E"></path><path d="M205 594h0c2 2 5 2 7 4h-1 0c-2-1-3 0-5 0-3 1-5 3-7 5l-1-1-2 2c0-1 0-1 1-2 1-2 3-4 4-5 1-2 2-3 4-3z" class="t"></path><path d="M205 605l1-1c1 1 1 2 2 2 0 0 1 0 1-1 1 1 1 1 2 1h7l1 1c1 0 1 0 2 1-1 0-3-1-4-1h-4-2v1h0l1 2h-2c-2 0-3 3-5 2h-1-1v1c0 2-1 2-1 4v1h-2s0-1 1-1v-3c2-2 1-3 3-4 1-1 1-1 2-1 0-1 1-2 1-3-1 0-1-1-2-1z" class="C"></path><path d="M209 607v-1l2 1v1l-1 1-1-2z" class="D"></path><path d="M209 607l1 2h-3 0c0-1 1-1 2-2z" class="B"></path><path d="M210 618c2 0 2 0 4 1h0c2 1 3 3 4 5h0c-1 1-1 1-2 1v1c0 2 0 3-1 5-1 1-1 2-2 2-1 1-1 2-2 3h0v-1s1 0 1-1l-1-1c0-1 1-1 1-2 1 0 0 0 1-1h0v-1-1c0 1-1 2-1 2-1 3-2 4-5 5-2 0-3 1-5 2-1 0-4-2-5-2v-1h0c3 1 4 2 8 1l4-1c2-2 3-5 4-7h1v4-1c1-2 1-4 1-6s-1-2-3-3l-2-3z" class="V"></path><path d="M198 627c1 2 3 4 6 4h1v-1h1 2l-3 3v2c-4 1-5 0-8-1-1-1-2-2-2-3h2l-2-2c1 0 2-1 3-2z" class="K"></path><path d="M195 631h2c1 1 3 3 5 3 1 0 2-1 3-1v2c-4 1-5 0-8-1-1-1-2-2-2-3z" class="H"></path><path d="M196 604l2-2 1 1-10 17-9 11v-1-2c1 0 2 0 2-1l1-2s0-1 1-1l8-12 2-5c1-1 1-2 2-3z" class="E"></path><path d="M164 635h1c-1 1-4 2-5 3-8 2-16 2-24 2-2-1-4 0-6-1s-5-1-7-1h8c4-1 9 0 13-1l5 1h0 0v1l5-1c2 0 5-1 7-2l3-1z" class="G"></path><path d="M174 644h3 1c0 1 0 1 1 2h0c0 1 1 2 1 2 0 1 0 2-1 3l-1-1c-1-1-1-1-2-1h-1-1-1c-1-1-2-1-3-1s-1 0-1-1l-1-1-1 1c-2 0-2-1-4-1v-1c0-1-2-1-3-1h14z" class="W"></path><path d="M168 646l1-1c1 0 1 0 2 1 0 1 0 1-1 1h-1l-1-1z" class="S"></path><path d="M174 644h3 1c0 1 0 1 1 2h0c0 1 1 2 1 2 0 1 0 2-1 3l-1-1c-1-1-1-1-2-1h-1-1l-1-1c0-1-1-1-2-1v-1h2c1-1 1-2 1-2z" class="AM"></path><path d="M174 644h3 0l-3 3h0l-1-1c1-1 1-2 1-2z" class="n"></path><path d="M146 643l10 1h2c1 1 0 1 0 2h0c-2 0-3-1-5 0l-3 1c-1 1-1 1-1 3-1 1-1 2-1 3h-1l-1-3c-1-1-1-2-2-2-2-1-4-1-6-1h0l-1-1c1 0 2 0 3-1h0 3c0-1 0-1 1-2h2z" class="T"></path><path d="M156 644h2c1 1 0 1 0 2h0c-1-1-3-1-4-2h2z" class="d"></path><path d="M138 647h0c1 0 2 0 3-1h0c1-1 4 0 5 0 0 1 1 3 0 4-1-1-1-2-2-2-2-1-4-1-6-1h0z" class="P"></path><path d="M146 646c1 0 2 1 3 1h1c-1 1-1 1-1 3-1 1-1 2-1 3h-1l-1-3c1-1 0-3 0-4z" class="AD"></path><path d="M240 627l1-1h0c2 1 3 1 5 1 1 1 2 1 3 2l2 2c2 1 5 3 7 4h2c1 0 1 0 1 1 1 0 2 0 3 1h2 0 2v1h1 2 1c1 1 1 1 2 1s2 0 3 1h-1-4 0v-1h-2-4l-1-1h-1l-4-1-1-1c-3-1-5-3-8-3-3-2-7-2-10-3l-1 1v2l-2-1c1-2 2-3 2-5z" class="B"></path><path d="M240 627l1-1h0c2 1 3 1 5 1 1 1 2 1 3 2l2 2c-2-1-6-1-8-3-1-1-1-1-2-1l-1 3h1l-1 1v2l-2-1c1-2 2-3 2-5z" class="D"></path><defs><linearGradient id="m" x1="216.229" y1="608.669" x2="222.427" y2="622.637" xlink:href="#B"><stop offset="0" stop-color="#4c4d4c"></stop><stop offset="1" stop-color="#6f7472"></stop></linearGradient></defs><path fill="url(#m)" d="M212 610h6v1h1c4 2 7 7 8 11 1 2 1 4 1 6s0 3-1 5c0-1 1-2 0-3h-3v-6c0-2-1-3-2-5-2-3-5-6-9-8-1 0-2 0-3-1h2z"></path><path d="M224 624v-2c1 0 1 0 2 1v1c0 1 1 3 2 4 0 2 0 3-1 5 0-1 1-2 0-3h-3v-6z" class="g"></path><path d="M205 567c0 1 0 1 1 2v1l4 11c1 2 2 4 3 5 3 4 8 10 12 12 1 0 1 1 2 1 4 5 9 8 14 12l-1 1c-12-8-26-19-32-32l-4-12 1-1z" class="I"></path><path d="M205 605c1 0 1 1 2 1 0 1-1 2-1 3-1 0-1 0-2 1-2 1-1 2-3 4v3c-1 0-1 1-1 1v8c1 0 1 1 2 1v2h1v-1l1-1c0 1 1 1 1 2 1 0 1 0 1 1h0-1v1h-1c-3 0-5-2-6-4-2-3-2-3-2-6h1 1c0-1 0-3-1-5h0c1-1 1-2 2-3v1l2-1v-2c0-1-1-1-1-2l2-1h0c0-1 1-1 1-1 1-1 1-2 2-2z" class="V"></path><path d="M198 627c-2-3-2-3-2-6h1 1c0 3 0 6 2 7s3 2 5 2v1h-1c-3 0-5-2-6-4z" class="F"></path><path d="M210 618l2 3c2 1 3 1 3 3s0 4-1 6v1-4h-1c-1 2-2 5-4 7l-4 1v-2l3-3h-2 0c1 0 1-1 2-1 0-2 0-3-1-4-1-2 0-3 0-5 1-1 2-1 3-2z" class="B"></path><path d="M207 625l1-1h1l1 1h1c0 2 0 3-2 4l-1 1h-2 0c1 0 1-1 2-1 0-2 0-3-1-4z" class="u"></path><path d="M210 618l2 3h0c0 1 0 2 1 2l-1 1c-1 0-1 0-1 1h-1l-1-1h-1l-1 1c-1-2 0-3 0-5 1-1 2-1 3-2z" class="H"></path><path d="M207 620c1 0 2 0 2 1 1 1 1 2 1 4l-1-1h-1l-1 1c-1-2 0-3 0-5z" class="f"></path><defs><linearGradient id="n" x1="218.241" y1="586.782" x2="215.873" y2="589.296" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#333738"></stop></linearGradient></defs><path fill="url(#n)" d="M206 570h1 0v1l1 1c1 1 3 4 4 6l4 6c2 3 5 6 7 9l1 1c0 1 0 1 1 1v1c0 1 2 2 2 2h1l-1 1c-1 0-1-1-2-1-4-2-9-8-12-12-1-1-2-3-3-5l-4-11z"></path><path d="M170 624l5-3c0 1 0 1 1 1s1 1 2 1h0 0 1l2-1h0c-1 3-3 6-4 9v1l-2 2c0-1-1-1-1-1h0l-1 1c-2 1-3 2-5 3h-1 0c-2 1-3 1-4 2l-1-1-2 1v-1h0c1-1 4-2 5-3h-1l1-1 3-2h0l1-1c1-1 2-1 3 0 1-1 1-1 1-2-1-1-2-2-3-4h-1 0l1-1z" class="Y"></path><path d="M168 632l1-1c1-1 2-1 3 0-2 3-6 5-10 7l-2 1v-1h0c1-1 4-2 5-3h-1l1-1 3-2h0z" class="D"></path><path d="M170 624l5-3c0 1 0 1 1 1s1 1 2 1h0 0 1l2-1h0c-1 3-3 6-4 9v1l-2 2c0-1-1-1-1-1h0c0-2 0-2 1-3 0-1-1-2 0-3v-1c-1-1-1-1-2-1h0-2-1-1 0l1-1z" class="f"></path><path d="M170 624l5-3c0 1 0 1 1 1s1 1 2 1h0c0 1-1 2-2 2 0-1 1-1 0-2-1 0-1 1-2 1h-4z" class="B"></path><path d="M179 623l2-1h0c-1 3-3 6-4 9v1l-2 2c0-1-1-1-1-1 1-1 1-1 1-2s0-1 1-1l3-7z" class="O"></path><path d="M166 630l2 2h0l-3 2-1 1-3 1c-2 1-5 2-7 2l-5 1v-1h0 0l-5-1c-4 1-9 0-13 1l-1-2c5 0 9-1 14-2h0l3-1v1h2 0c1 1 2 0 3 0 3-1 7-2 11-3h0c1-1 2-1 3-1z" class="M"></path><path d="M156 635c1-1 3-1 5-2 0 2-1 2-2 3l-1-1h-2z" class="G"></path><path d="M144 637c4-1 8-1 12-2h2l1 1c-3 1-7 2-10 2l-5-1z" class="I"></path><path d="M166 630l2 2h0l-3 2-1 1-3 1c-2 1-5 2-7 2l-5 1v-1h0 0c3 0 7-1 10-2 1-1 2-1 2-3 1 0 2-1 3-2h-1 0c1-1 2-1 3-1z" class="O"></path><path d="M166 630l2 2h0l-3 2-1 1-3 1 1-1c2-1 2-1 3-3 1-1 1-1 1-2z" class="a"></path><path d="M186 609c0-1 1-2 1-2 1 0 1 0 2 1v-1l1 1c0 1-1 2-1 3 1 1 1 1 2 1v-1l1 1-8 12c-1 0-1 1-1 1l-1 2c0 1-1 1-2 1v2 1c0 1-1 2-2 3-1-1-1-1-1-2v-1c1-3 3-6 4-9h0l-2 1h-1 0 0c-1 0-1-1-2-1s-1 0-1-1l4-4 5-6v-1l1-1h1z" class="G"></path><path d="M180 628v2 1c0 1-1 2-2 3-1-1-1-1-1-2v-1c1 0 2-1 2-1 1-1 0-1 1-2z" class="K"></path><path d="M189 607l1 1c0 1-1 2-1 3 1 1 1 1 2 1v-1l1 1-8 12h-1c0-3 2-6 3-9 1-1 2-3 2-4l-1-1 2-2v-1z" class="B"></path><path d="M186 609c0-1 1-2 1-2 1 0 1 0 2 1l-2 2-2 4c0 1-2 3-2 4 0 2-1 3-2 4h0l-2 1h-1 0 0c-1 0-1-1-2-1s-1 0-1-1l4-4 5-6v-1l1-1h1z" class="V"></path><path d="M179 617l5-6c0 1 0 3-1 4s-3 2-4 2z" class="B"></path><path d="M178 623c2-2 3-4 5-5 0 2-1 3-2 4h0l-2 1h-1z" class="b"></path><path d="M186 609c0-1 1-2 1-2 1 0 1 0 2 1l-2 2-2 4h0v-3c1-1 1-1 1-2h0z" class="C"></path><path d="M213 607h4c1 0 3 1 4 1 3 1 6 5 8 8 1 1 3 3 3 5l1 1-2 1h1v4c0 3 0 6-1 8-1 1-1 2-2 2l-1 1-1-1h0l-1-1 1-3c1-2 1-3 1-5s0-4-1-6c-1-4-4-9-8-11h-1v-1h-6l-1-2h0v-1h2z" class="D"></path><path d="M213 607l1 1h-3 0v-1h2z" class="L"></path><path d="M229 621v-1c2 1 2 1 2 3h1c-1 2-1 3-2 4v3c-1-3-1-6-1-9z" class="M"></path><path d="M230 630v-3 3c0 2-1 3 0 5h1c-1 1-1 2-2 2l-1 1-1-1c1-2 2-5 3-7z" class="l"></path><path d="M232 623v4c0 3 0 6-1 8h-1c-1-2 0-3 0-5v-3c1-1 1-2 2-4z" class="i"></path><path d="M227 617s0-1-1-2h1l2 1c1 1 3 3 3 5l1 1-2 1c0-2 0-2-2-3v1c-1 0-2-3-2-4z" class="G"></path><path d="M227 617c1 0 1 0 1 1 1 1 2 2 3 2h1v1l1 1-2 1c0-2 0-2-2-3v1c-1 0-2-3-2-4z" class="I"></path><path d="M197 548c2 2 1 6 2 9 0 7 0 15 2 22v2c0 2 0 5 1 6v5l-2 1-1 3h-1l-1 2c-1-1-1-6-1-7l1-2c-1-1-2-1-2-2 1-1 1-2 1-4v-1h0v-2c1 0 0-2 0-3h0c0 1 0 2-1 3h0c1-4 2-9 2-14h0c1-3 1-7 1-10l-1-8z" class="M"></path><path d="M197 587c1 3 1 6 1 9l-1 2c-1-1-1-6-1-7 1-1 1-2 1-4z" class="D"></path><path d="M200 593c0-3-1-8 0-11 0 1 0 2 1 2v-3c0 2 0 5 1 6v5l-2 1z" class="I"></path><path d="M197 566h0v8 10 3c0 2 0 3-1 4l1-2c-1-1-2-1-2-2 1-1 1-2 1-4v-1h0v-2c1 0 0-2 0-3h0c0 1 0 2-1 3h0c1-4 2-9 2-14z" class="C"></path><path d="M183 609h1 0v1 1l-5 6-4 4-5 3-1 1h0 1c1 2 2 3 3 4 0 1 0 1-1 2-1-1-2-1-3 0l-1 1-2-2c-1 0-2 0-3 1h0c-4 1-8 2-11 3-1 0-2 1-3 0h0-2v-1c3-1 7-2 10-4 1 0 1 0 2-1 0-1-1-1-1-2l1-1 1-1c2 0 2-1 3-2 2 0 3-1 4-2 2 1 3 0 4 2v-1c2 0 3-2 5-4 3-1 6-6 7-8z" class="X"></path><path d="M160 624c2 0 2-1 3-2 2 0 3-1 4-2 2 1 3 0 4 2-4 2-8 5-12 6 0-1-1-1-1-2l1-1 1-1z" class="T"></path><path d="M169 625h1c1 2 2 3 3 4 0 1 0 1-1 2-1-1-2-1-3 0l-1 1-2-2c-1 0-2 0-3 1h0c-4 1-8 2-11 3-1 0-2 1-3 0h0c5-2 9-4 14-6 2-1 4-1 6-3zm88-9l12 6c0-1 0-2 1-2l25 10c2 1 5 2 6 3v1c3 1 6 1 9 2 1 0 3 1 4 1h1c1 1 2 1 3 2-3 0-6 0-8-1-15-2-29-8-43-13-1-1-2-2-4-3l1-1s-7-4-8-5h1z" class="y"></path><path d="M270 620l25 10c2 1 5 2 6 3v1c-11-3-21-8-32-12 0-1 0-2 1-2z" class="c"></path><path d="M264 621c5 2 10 5 16 7 8 4 18 7 27 8 2 1 4 1 7 1 0 1 0 1 1 0 1 1 2 1 3 2-3 0-6 0-8-1-15-2-29-8-43-13-1-1-2-2-4-3l1-1z" class="t"></path><path d="M189 589h1c0 2-1 3-1 5l-2 6c-1 3-3 6-4 9-1 2-4 7-7 8-2 2-3 4-5 4v1c-1-2-2-1-4-2-1 1-2 2-4 2-1 1-1 2-3 2l1-1-1-1h1v-1c0-1 0-2 1-2 0-1 1-1 1-1 2 0 4-1 6-3h0 0 1c2-2 5-6 6-8h1c1-3 4-3 4-7h0c0-2 1-3 2-4v-1l2-1c0-1 1-2 1-3h1l2-2z" class="x"></path><path d="M183 595l2-1c0-1 1-2 1-3h1l2-2c-1 3-1 6-3 7 0 1-2 4-2 4 0 3-1 5-3 6 0 1 0 1-1 2 0 1-1 2-3 3 1-2 2-2 2-4 1-1 2-3 2-5 1-1 0-3 1-4s1-2 1-2v-1z" class="AU"></path><path d="M177 607c1-3 4-3 4-7h0c0-2 1-3 2-4 0 0 0 1-1 2s0 3-1 4c0 2-1 4-2 5 0 2-1 2-2 4l-3 4h0l2 1v1c-2 2-3 4-5 4v1c-1-2-2-1-4-2-1 1-2 2-4 2-1 1-1 2-3 2l1-1-1-1h1v-1c0-1 0-2 1-2 0-1 1-1 1-1 2 0 4-1 6-3h0 0 1c2-2 5-6 6-8h1z" class="AB"></path><path d="M176 607h1c0 2 0 3-2 4l-2 3c-1 0-2 1-3 1h-1 0 1c2-2 5-6 6-8z" class="AE"></path><path d="M172 617l2-2h0l2 1v1c-2 2-3 4-5 4v1c-1-2-2-1-4-2 2-2 3-2 5-3z" class="S"></path><path d="M167 620c2-2 3-2 5-3l-1 1c1 0 1 0 2 1l-2 2v1c-1-2-2-1-4-2z" class="W"></path><path d="M195 580h0c1-1 1-2 1-3h0c0 1 1 3 0 3v2h0v1c0 2 0 3-1 4 0 1 1 1 2 2l-1 2c0 1 0 6 1 7l1-2h1l2 1c-1 1-3 3-4 5-1 1-1 1-1 2-1 1-1 2-2 3l-2 5-1-1v1c-1 0-1 0-2-1 0-1 1-2 1-3l-1-1v1c-1-1-1-1-2-1 0 0-1 1-1 2h-1l-1 1v-1h0-1c1-3 3-6 4-9l2-6c0-2 1-3 1-5v-1c1-2 2-3 3-4 0-1 1-2 1-2 0-1 0-1 1-2z" class="y"></path><path d="M198 596h1l2 1c-1 1-3 3-4 5-1 1-1 1-1 2-1 1-1 2-2 3l-2 5-1-1v1c-1 0-1 0-2-1 0-1 1-2 1-3l-1-1c1-1 2-2 3-2 1-1 2-2 2-3l3-4 1-2z" class="N"></path><path d="M189 607c1-1 2-2 3-2 1-1 2-2 2-3 0 2 0 5-1 6s-2 2-2 3v1c-1 0-1 0-2-1 0-1 1-2 1-3l-1-1z" class="C"></path><path d="M193 584c1 3 0 9-1 13 0 3-3 7-5 10 0 0-1 1-1 2h-1l-1 1v-1h0-1c1-3 3-6 4-9l2-6c0-2 1-3 1-5v-1c1-2 2-3 3-4z" class="L"></path><path d="M189 594c1 0 1 0 1 1l-2 7c0-1 0-1-1-2l2-6z" class="g"></path><path d="M187 600c1 1 1 1 1 2l-3 7-1 1v-1h0-1c1-3 3-6 4-9z" class="J"></path><path d="M193 584c1 3 0 9-1 13h0l-1-1 1-6c-1 1-2 4-2 5 0-1 0-1-1-1 0-2 1-3 1-5v-1c1-2 2-3 3-4z" class="U"></path><path d="M228 598h-1s-2-1-2-2v-1c-1 0-1 0-1-1l2 2c1 0 1 0 2 1h0c1 0 1 1 2 1s2 1 2 2c2 1 4 3 5 4v-1l-2-2c0-1 0-1-1-1v-1h0s1 0 1 1h2c1 1 2 1 3 2v-1c4 3 8 6 11 9 2 1 5 2 6 4h0c-1 0-1-1-2-1h-1c1 1 2 1 3 2v1h-1c1 1 8 5 8 5l-1 1c2 1 3 2 4 3-10-2-19-7-27-13l1-1c-5-4-10-7-14-12l1-1z" class="N"></path><path d="M249 614c1 0 3 1 4 2v1c-1 1-2 0-2 0l-3-2 1-1z" class="V"></path><path d="M228 598l3 3c1 1 1 1 2 1 0 1 1 1 2 2l2 2 4 2 8 6-1 1c-2-2-5-3-7-4-5-4-10-7-14-12l1-1z" class="B"></path><path d="M226 596c1 0 1 0 2 1h0c1 0 1 1 2 1s2 1 2 2c2 1 4 3 5 4v-1l-2-2c0-1 0-1-1-1v-1h0s1 0 1 1h2c1 1 2 1 3 2v-1c4 3 8 6 11 9 2 1 5 2 6 4h0c-1 0-1-1-2-1h-1c1 1 2 1 3 2v1h-1c-1 0-2-1-4-2-9-5-18-11-26-18z" class="D"></path><path d="M234 599s1 0 1 1h2c1 1 2 1 3 2 2 2 5 4 7 6-4 0-6-3-9-5h-1l-2-2c0-1 0-1-1-1v-1h0z" class="L"></path><path d="M240 601c4 3 8 6 11 9 2 1 5 2 6 4h0c-1 0-1-1-2-1h-1c1 1 2 1 3 2-4-2-7-4-10-7-2-2-5-4-7-6v-1z" class="I"></path><path d="M403 87l4-1c5-1 10 0 15 0h0 4c2 1 3 2 4 2s2 0 2 1h1 0 3c-1 0-1 0-1-1h11l21 1h0c-2 0-3 0-5 1v1h4l1 1h0 3 1s1 0 1-1l1 1h5l9 2v1c3 1 7 2 10 3l10 6v1l2 2h-1l-4 5s-1 0-1-1h-2c0 1 1 0 1 1l1 2v1c0 1 0 1 1 1h0c1 1 2 1 2 1l1 1v1c-1 1-2 2-3 4l2 1h3l2 2 1 1 4 6h0v-1c1 1 1 3 3 4h1v-1c0 1 1 1 0 2v1h0c1-1 2-1 3-2 1 1 1 2 2 3h-2c1 1 1 1 2 1l1-1h2 0l1-1c0-1 0-2 1-3h2v2l1-2 1-1c1 0 1 0 2-1 1 1 1 1 1 2 0 2 0 3-1 5l1 1 1-1v1 4c0 1 0 1 1 2v6h1l1 9c0 1 0 2 1 2 1 4 2 9 2 12 1 2 1 3 2 5l1 6 2 7c1 1 2 4 1 5v2 1h0v2h0-1c1 3 1 7 2 10v1c2 0 2 1 3 2v3l1 5v8c0 1 0 3-1 5h0v8h1v3 3h1v1h0v6h-1l-1-1h-1v4 1c2 1 2 1 3 3v2h0v3c0 2 1 3 2 4 0 1 1 1 1 2-1 2 0 5 0 8v10l-1 1-1 3c-1 1-2 2-2 4h-1c0-1 1 0 1-1v-3h-1c-1 1-1 2-2 3 0 1 1 2 0 3 0 0-1 1-1 2 1 2-1 3-1 6-1 1-1 2-1 4-1 0-1 1-1 1 0 2-1 3-2 4h0v2l-2-1c0 1 0 1-1 2s-1 2-1 4v1c-2 5-4 10-6 14l-3 6-2 6-1 1c-2 3-3 6-5 10v-1l1-6 1-4 4-14c0-2 1-3 1-5 1-4 2-9 2-13v-6-1c0-2 0-4-1-7 1-3 1-6 0-9l1-16-3-24c-3-13-8-25-15-37-2-6-5-11-9-16 0 2 0 8-1 10-1-2 1-9-1-11v-1-1c1-1-17-23-18-25-11-12-23-23-37-31-3-2-7-4-11-6-16-7-32-13-49-17-5-2-9-3-14-3-1 0-2 0-3-1h-2c-4 1-9 0-14 0h-2c-1 1-1 1-2 1h-2c-1 0-1-1-2 0h-1c-2-1-2-1-4 0-1 0-2 0-3 1h0-1v-1h-4-5-3l-2 1c-1 0-3-1-4-2h1c-1-1-1-2-2-2 0 0-1-1-2 0h-2c-1 1-1 1-2 1v1h0c0 1-1 1-2 1-1 1-2 1-2 3h0c-2 0-4 1-5 2v1c-2 1-3 2-4 3-1 0-1 0-2 1v-1h-1c-1 1-2 2-4 3l-1 1c0-1 0-1-1-1v1c-1 1-2 2-2 3h-1 0-1l1 3-2-1-1-2c-1 0-3 0-3-1h-3l-1-1-1 2v2l-1 3h0c-1 2-3 4-3 6l-1 1-1 3v4l-1 1h-1c0-1 0-1 1-2-2 0-3 1-5 0h0l-1 1-1-2h-1v-1c0-1 0-2-1-4h-2 0c-2 1-3 1-5 2l-1 1h-2l-1-1c-1 1-2 1-3 1s-2 0-3 1l-1-1-1 1v1h-1l-1 1-2-2c-1 0-2 0-3 1h0v-1c0-1 0-1-1-1v-1h0-1c-1-1-1-2-1-3l1-1 1-1v-1c-1 1-1 1-2 1 0-1 3-4 3-5 3-4 6-6 9-9 1 0 2-1 3-2h0c1 0 2-1 3-1h0c1-1 2-1 3-2h1v-3h-1v-1c1-1 2-2 4-3h-2c2-1 3-3 5-4l7-4v-2c0-1 1-2 0-3 2-1 4-2 6-2l1-2 14-7c5 0 9-2 13-3 1 0 1 1 2 1l3-1c8-2 17-4 26-5 8-1 16-2 25-4h6 10 6l22 1z" class="y"></path><path d="M519 147h1v4l-2 2 1-6z" class="AD"></path><path d="M537 221h1v2l-1 1-1 4h0v-7h1z" class="n"></path><path d="M524 164c0 1 1 1 1 2v2h-1s-1 0-1 1c0 0 0 1-1 2v-3h0l1-1 1-3zm-175-47h3v1h0v2h-4v-1c1 0 1 0 2-1l-1-1z" class="a"></path><path d="M304 110c1-1 1-1 2-1h0c1 1 1 1 2 1l1 1c-2 1-3 1-4 1h-2l1-2z" class="AO"></path><path d="M520 151c1 1 1 1 0 2v1 1l-1 3-1 1v-6l2-2z" class="AE"></path><path d="M324 106h0v1h2l-1 1c-1 0-2 0-3 1h0l-2 1h-1v-2c2 0 3-1 5-1v-1z" class="a"></path><path d="M553 233l1 5v8l-1 1-1-10h1v-4z" class="AX"></path><path d="M538 223c1 1 1 2 1 3 0 2-1 3-1 5h-1c-1-1-1-2-1-3l1-4 1-1z" class="S"></path><path d="M354 112c1 0 1-1 3 0v3h-1l-3 1v-1c0-2 0-2 1-3z" class="x"></path><path d="M540 179c1 1 3 7 3 7l-1 2-3-8s1 0 1-1z" class="X"></path><path d="M303 112h2c-2 1-3 1-4 4 1 0 2 0 3 1h1c0 1 0 1-1 1h-1 0c-1 1-2 1-3 2h-1c0-2 2-2 2-4h-1c0-2 2-3 3-4z" class="r"></path><path d="M526 166h1v2c0 2 0 5 1 6l-3 1c-1-1 0-5 0-6l-1-1h1c1-1 1-1 1-2z" class="q"></path><path d="M300 109c2 0 3 0 4 1l-1 2c-1 1-3 2-3 4l-2-1h0c1-2 1-3 3-4v-1h-1v-1z" class="T"></path><path d="M539 235v11l-3-3c-1-1-1-3-1-4 1 1 1 2 1 3l1-1 1-1c0-2-1-2-1-3l2-2z" class="W"></path><path d="M539 305h1 1c0 1 0 2-1 3v2h-1c-1 2-1 4-1 6v1c-1-2 0-4 0-6l-1-4h1c0-1 0-2 1-2z" class="q"></path><path d="M510 137c1-1 1-1 2-1l3 3c2 2 4 4 4 7l-9-9z" class="AG"></path><path d="M543 186c2 4 4 9 4 13h0v-1l-1 2-4-12 1-2z" class="i"></path><path d="M535 239c0-2 0-6 1-7h2c1 1 1 2 1 3l-2 2c0 1 1 1 1 3l-1 1-1 1c0-1 0-2-1-3z" class="v"></path><path d="M536 221l-3-22c2 6 4 11 4 17 0 1 1 2 0 3v2h-1z" class="k"></path><path d="M544 299h0c0 1 1 2 0 3v-1-2 1c-1 1-1 3-1 4v1l1 1c-1 1-1 1-1 2 0 2 0 5-1 6l-1 3h0v-2-1l1-1-2-2c1-1 1-3 1-4 2-3 1-6 3-8z" class="q"></path><path d="M536 272v-3h1v3 13c0 2 0 4-1 6v-1-18z" class="AM"></path><path d="M546 200l1-2v1h0l2 5c1 3 1 7 2 10v1h-1l-4-15z" class="l"></path><path d="M305 117c1-1 2-2 4-2v-1c1 0 1-1 1-1 1-1 2-1 3 0l2-2h0c1 2 0 1 0 2-1 1-1 2-1 2v-2c-2 1-3 2-4 4h-1 0c-1 1-2 1-2 2l-1 1h-1c-1-1-1-1-1-2 1 0 1 0 1-1z" class="Ah"></path><path d="M365 86h10 6c-3 1-6 1-9 1-7 1-14 2-20 4v-2l15-3h-2z" class="Al"></path><path d="M554 246h1v3 3h1v1h0v6h-1l-1-1h-1v-11l1-1z" class="AS"></path><path d="M555 249v3h1v1h0v6h-1l-1-1v-1-1c1-1 1-4 1-5v-2z" class="AT"></path><path d="M315 116l3-3 1 1 1 2h0 1v1s0 1 1 1l-1 2s-1-1-2 0h-2c-1 1-1 1-2 1v-2h-1v-1l1-2z" class="d"></path><path d="M320 116h1v1s0 1 1 1l-1 2s-1-1-2 0h-2c-1 1-1 1-2 1v-2h-1v-1l2 1h1v-1c1 0 2-1 3-1v-1z" class="h"></path><path d="M549 270h1s1 1 1 2c0 0 1 0 1-1v4l-2 4c0 1-1 1-1 2h-1l-1-1c0-2-1-3-1-5l1-1c1 0 1 0 1-1s0-2 1-3z" class="a"></path><path d="M549 270h1s1 1 1 2 0 1-1 2l-1-1v-3z" class="AA"></path><path d="M547 274l1 1c0 1 0 2 1 3l1 1c0 1-1 1-1 2h-1l-1-1c0-2-1-3-1-5l1-1z" class="r"></path><path d="M501 126l1-1 1 1c0 1 0 3-1 4v3 6 14c0 2 0 4-1 6l-1-31v-1l1-1z" class="W"></path><path d="M501 126l1-1 1 1c0 1 0 3-1 4v3h-1c0-2 0-4-1-5v-1l1-1z" class="AR"></path><path d="M503 126v-2h1c2 2 3 4 5 6l-1 1c1 2 3 3 4 5-1 0-1 0-2 1 0 0-1-2-2-2l-5-5h-1c1-1 1-3 1-4z" class="X"></path><path d="M503 126v-2h1c2 2 3 4 5 6l-1 1-4-4h0c-1 1-1 2-1 3h-1c1-1 1-3 1-4z" class="w"></path><path d="M527 166l2 1c-1 5 3 28 1 31 0-2 0-4-1-6l-1-9v-9c-1-1-1-4-1-6v-2z" class="S"></path><path d="M304 118c0 1 0 1 1 2h1l1-1c0-1 1-1 2-2h0v1l-3 3c0 1-1 1-1 2-1 0-1 0-1 1h0c-2 0-2-1-3 0 0 1 0 1-1 1 0 1 0 1-1 2-1-1-1-1-1-2s0-2 1-2v-1h-1 0-1c1-1 1-2 2-2h1c1-1 2-1 3-2h0 1z" class="AN"></path><path d="M534 285v4l2 1v1c-2 2-1 8-1 11v7 5s1 2 0 2c-1 3 0 5-1 8v-6-1c0-2 0-4-1-7 1-3 1-6 0-9l1-16z" class="S"></path><path d="M530 164l-1-2h1l1 1c1 1 1 0 2 1 0 0 1 2 2 2 2 4 3 9 5 13 0 1-1 1-1 1l-9-16z" class="l"></path><path d="M505 182c-2-1-4-1-5-2s-2-3-3-4l2-2c1-1 2-1 4-1v1c1 1 0 2-1 3l3 1c1 1 1 2 1 3v2h-1v-1z" class="S"></path><path d="M505 182c0-1-1-2-1-3-1 0-2 0-3-1 0 0-1-1 0-2h1v1l3 1c1 1 1 2 1 3v2h-1v-1z" class="W"></path><path d="M525 159h0c0-2 1-2 2-3v3h3c1 1 1 2 1 4l-1-1h-1l1 2-1 2v1l-2-1h-1c0 1 0 1-1 2v-2c0-1-1-1-1-2v-1l1-1v-3z" class="AJ"></path><path d="M526 161l2 1v2c0-1-1-1-1-1-1-1-1-2-1-2z" class="T"></path><path d="M527 163s1 0 1 1 0 1 1 2v1l-2-1h-1v-1l1-2z" class="AR"></path><path d="M525 159h0c0-2 1-2 2-3v3l-1 2s0 1 1 2l-1 2v1c0 1 0 1-1 2v-2c0-1-1-1-1-2v-1l1-1v-3z" class="q"></path><path d="M525 162h1v3 1c0 1 0 1-1 2v-2c0-1-1-1-1-2v-1l1-1z" class="Aa"></path><path d="M536 243l3 3-1 2c0 3 1 5 1 7h0l-1 2v1h1c0 1 1 2 0 3h-1c-1 3-1 8-1 11v-3h-1v3-26-3z" class="AO"></path><path d="M536 249c1 1 1 2 2 3h0v-3-1c0 3 1 5 1 7h0-1-1v3-2c-1-2-1-5-1-7z" class="x"></path><path d="M536 243l3 3-1 2v1 3h0c-1-1-1-2-2-3v-3-3z" class="S"></path><path d="M547 285h0c1-1 1-1 1-2 0 1 1 2 1 2v1l2 2-2 10v-1h-1v-1 1 2h0c-3 3-3 7-3 11h0c-1 2 0 3 0 4-1 1-1 3-2 4h-1v-1s0-1 1-1v-2-2h1v-3l-1-1c1 0 1-3 1-3l1-3c0-1 0-1 1-2l1-1c-1-1-1-1-1-2 1-1 0-5 0-6 0-2 1-4 1-6z" class="r"></path><path d="M551 214v1c2 0 2 1 3 2v3l1 5v8c0 1 0 3-1 5h0l-1-5v4h-1c-1-3 0-7-1-11l-1-11h1v-1z" class="AH"></path><path d="M551 215c2 0 2 1 3 2v3l-1 1h0 0l-2-6z" class="AN"></path><path d="M554 220l1 5v8c0 1 0 3-1 5h0l-1-5c1-4 0-8 0-12h0 0l1-1z" class="AV"></path><path d="M403 87l4-1c5-1 10 0 15 0h0 4c2 1 3 2 4 2s2 0 2 1h1 0c2 1 4 2 7 2l-2 1c-3-1-7-1-11-2-8-1-16-3-24-3z" class="AP"></path><path d="M433 89l-23-2c1-1 10-1 12-1h4c2 1 3 2 4 2s2 0 2 1h1z" class="AB"></path><path d="M506 124h3l2 2 1 1 4 6h0v-1c1 1 1 3 3 4h1v-1c0 1 1 1 0 2v1h-1c-2 0-2 0-3-1l-1 2-3-3c-1-2-3-3-4-5l1-1c-2-2-3-4-5-6h2z" class="AT"></path><path d="M509 130l7 7-1 2-3-3c-1-2-3-3-4-5l1-1z" class="S"></path><path d="M506 124h3l2 2 1 1 4 6h0v-1c1 1 1 3 3 4h1v-1c0 1 1 1 0 2v1h-1c-3-3-5-5-7-8l-6-6z" class="n"></path><path d="M522 158l1 1c1 0 1-1 2-1v1 3l-1 1v1l-1 3-1 1h0v3 8l1 21c1 3 1 8 0 10h0l-3-41-1-5h1v-3l1-2 1-1z" class="S"></path><path d="M522 158l1 1c1 0 1-1 2-1v1 3l-1 1v1l-1 3-1 1h0v2c-1 1-1 2-1 3 0-2 0-2-1-4l-1-5h1v-3l1-2 1-1z" class="AM"></path><path d="M522 162l2 1v1l-1 3c-1-2-1-3-1-5z" class="W"></path><path d="M522 158l1 1c1 0 1-1 2-1v1 3l-1 1-2-1c0-1 0-2-1-3l1-1z" class="AR"></path><defs><linearGradient id="o" x1="538.339" y1="189.175" x2="551.981" y2="191.598" xlink:href="#B"><stop offset="0" stop-color="#2c2c30"></stop><stop offset="1" stop-color="#7f463f"></stop></linearGradient></defs><path fill="url(#o)" d="M535 166h1c4 6 6 15 11 21l2 7c1 1 2 4 1 5v2 1h0v2h0-1l-2-5c0-4-2-9-4-13 0 0-2-6-3-7-2-4-3-9-5-13z"></path><path d="M552 271l1-9v1c2 1 2 1 3 3v2l-2 4c-1 5 0 11-2 16l-1 8c0 1 0 2-1 3s0 2 0 4v3h-1l-1-1v-1l1-6 2-10-2-2v-1s-1-1-1-2c0 1 0 1-1 2h0s0-1-1-1l1-1h0v-1-2l1 1h1c0-1 1-1 1-2l2-4v-4z" class="AJ"></path><path d="M551 287v2c0 3 0 7-1 9 1-1 0-3 1-4v-2-3l1-1h0l-1 8c0 1 0 2-1 3s0 2 0 4v3h-1l-1-1v-1l1-6 2-10v-1z" class="W"></path><path d="M550 279l2-4c0 2 0 5-1 8v4 1l-2-2v-1s-1-1-1-2c0 1 0 1-1 2h0s0-1-1-1l1-1h0v-1-2l1 1h1c0-1 1-1 1-2z" class="w"></path><path d="M547 280l1 1h1c1 0 1 0 2 2h-1-2c0 1 0 1-1 2h0s0-1-1-1l1-1h0v-1-2z" class="a"></path><path d="M310 117c1-2 2-3 4-4v2l1 1-1 2v1h1v2 1h0c0 1-1 1-2 1-1 1-2 1-2 3h0c-2 0-4 1-5 2v1c-2 1-3 2-4 3-1 0-1 0-2 1v-1h-1c-1 1-2 2-4 3l-1 1c0-1 0-1-1-1v1l-2-2c1 0 1-1 1-1 1-1 2-1 2-1l3-3 2-2c1-1 1-1 1-2 1 0 1 0 1-1 1-1 1 0 3 0h0c0-1 0-1 1-1 0-1 1-1 1-2l3-3v-1h1z" class="S"></path><path d="M310 117h0c1 0 1 0 2-1h1 1c-1 1-2 2-2 3-1 0-2 1-3 1v-2-1h1z" class="d"></path><path d="M304 124l1-1 1 1v1 1 1l-1 1c0-1-1-1-1-1-1 0-1-1-2-1l1-1 1-1h0z" class="AL"></path><path d="M303 125h2v1h1v1l-1 1c0-1-1-1-1-1-1 0-1-1-2-1l1-1z" class="k"></path><path d="M309 118v2c1 0 2-1 3-1-2 1-3 2-4 4h-3l-1 1c0-1 0-1 1-1 0-1 1-1 1-2l3-3z" class="W"></path><path d="M314 119h1v2 1h0c0 1-1 1-2 1-1 1-2 1-2 3h0c-2 0-4 1-5 2v1c-2 1-3 2-4 3-1 0-1 0-2 1v-1c1-2 3-3 5-4l1-1c2-3 6-5 8-8z" class="AB"></path><path d="M299 127c1-1 1-1 1-2 1 0 1 0 1-1 1-1 1 0 3 0l-1 1-1 1c1 0 1 1 2 1 0 0 1 0 1 1-2 1-4 2-5 4h-1c-1 1-2 2-4 3l-1 1c0-1 0-1-1-1v1l-2-2c1 0 1-1 1-1 1-1 2-1 2-1l3-3 2-2z" class="R"></path><path d="M297 131l2 1c-1 1-2 2-4 3l-1 1c0-1 0-1-1-1l4-4z" class="K"></path><path d="M297 131l5-5c1 0 1 1 2 1 0 0 1 0 1 1-2 1-4 2-5 4h-1l-2-1z" class="L"></path><path d="M523 136c1 1 1 2 2 3h-2c1 1 1 1 2 1h0-1v2c1 2 2 4 4 6v1c0 1 0 1-1 2v1c0 1 0 2-1 3v1h1c-1 1-2 1-2 3h0v-1c-1 0-1 1-2 1l-1-1-1 1-1 2v3h-1c0-2 0-3-1-5l1-1 1-3v-1-1c1-1 1-1 0-2v-4h-1v-1c0-3-2-5-4-7l1-2c1 1 1 1 3 1h1 0c1-1 2-1 3-2z" class="AU"></path><path d="M518 159l1-1 1-3v6 3h-1c0-2 0-3-1-5z" class="Q"></path><path d="M524 147h0-1c-1-2-2-2-3-3v-2h1c2 1 2 2 2 4l1 1z" class="m"></path><path d="M524 142c1 2 2 4 4 6v1c0 1 0 1-1 2v1c0 1 0 2-1 3v1h1c-1 1-2 1-2 3h0v-1c-1 0-1 1-2 1l-1-1 2-3v-1h-1c0-1 0-4 1-5h1l1 1 1-1 1-1h-1-1l-1 1-1-1v-1l-1-1 1-4z" class="v"></path><path d="M525 149v4h1v-3l2-1c0 1 0 1-1 2v1l-1 1-2 1v1-1h-1c0-1 0-4 1-5h1z" class="T"></path><path d="M527 152c0 1 0 2-1 3v1h1c-1 1-2 1-2 3h0v-1c-1 0-1 1-2 1l-1-1 2-3v-1l2-1 1-1z" class="W"></path><path d="M288 123s1-1 1-2c1 0 0 0 1-1 0 1 1 1 1 1h1 1 2 0l2 2v-1h0 1 0 1v1c-1 0-1 1-1 2s0 1 1 2l-2 2-3 3s-1 0-2 1c0 0 0 1-1 1l2 2c-1 1-2 2-2 3h-1 0-1l1 3-2-1-1-2c-1 0-3 0-3-1h-3l-1-1c1-2 2-3 3-5l1-1h0l3-3 1-1c-1-1 0-2 0-4z" class="AV"></path><path d="M291 126h1v1l-2 2h-1c0-1 1-2 2-3z" class="AS"></path><path d="M293 129c1 0 1-1 2-1v-1h1l1 2-3 3h-1v-3z" class="Ah"></path><path d="M286 131c2-1 2-2 4-1 1-1 2-1 3-1v3c0-1 0-1-1-1 0 0-1 1-1 2h-1c-1 0-1 0-1-1h-1c0 1-1 1-2 1v-2z" class="Ac"></path><path d="M286 133c1 0 2 0 2-1h1c0 1 0 1 1 1h1c0-1 1-2 1-2 1 0 1 0 1 1h1s-1 0-2 1c0 0 0 1-1 1l2 2c-1 1-2 2-2 3h-1 0-1v-2c-1-2-1-2-3-3v-1z" class="J"></path><path d="M291 134l2 2c-1 1-2 2-2 3h-1 0c0-2 1-2 0-4l1-1z" class="X"></path><path d="M288 123s1-1 1-2c1 0 0 0 1-1 0 1 1 1 1 1h1 1 2 0l2 2c-1 1-3 2-3 3h-1l-1-1c-1 0-1 0-2 1-1 0-2 1-2 1-1-1 0-2 0-4z" class="h"></path><path d="M284 131h2v2 1c2 1 2 1 3 3v2l1 3-2-1-1-2c-1 0-3 0-3-1h-3l-1-1c1-2 2-3 3-5l1-1z" class="K"></path><path d="M283 132l1 1v1c-1 1-2 1-2 2v1h1c3 0 3 0 6 2h0 0l1 3-2-1-1-2c-1 0-3 0-3-1h-3l-1-1c1-2 2-3 3-5z" class="N"></path><path d="M549 297v1l-1 6-3 10c0 2-1 4-1 6l-1 5c-1 1-1 2-1 4v1c-2 5-4 10-6 14l-3 6-2 6-1 1c-2 3-3 6-5 10v-1l1-6 1-4 4-14c1 2 0 3 0 4l-1 3v2l1-2v-2c1-1 1-1 1-2v-1-2c1-1 1-1 1-2l1-1v-1c1-1 1-3 1-3 1-1 1-2 1-3h0l1-1v-2h0v-1c1-1 0-1 1-2v-3c-1 1-1 4-2 5h-1c0-1 1-2 1-2v-1c0-1 1-2 2-2v-1h1 0c1 2 0 4 0 5l1-1v-1s0-1 1-1c0-1 1-1 0-2v-1l1-1v-1l1-1 1-1v-1c1 0 0 0 0-1l1-1v-2c1-1 1-1 1-2v-1-1-1l1-1v-1-1-1l1-3c0-1 1-2 1-3z" class="w"></path><path d="M535 341c1-3 2-6 4-9 0-1 1-3 2-5s1-4 3-7l-1 5c-1 1-1 2-1 4v1c-2 5-4 10-6 14l-3 6 1-4 1-5z" class="AO"></path><path d="M531 349h0c1-1 1-1 1-2l1-1v-2h0c1-1 1 0 1-1l1-2h0l-1 5-1 4-2 6-1 1c-2 3-3 6-5 10v-1l1-6 1-4 4-14c1 2 0 3 0 4l-1 3v2l1-2z" class="Aa"></path><path d="M527 356c1 1 1 1 0 2h0 1l1-2c1-2 3-5 3-7 1-1 1-2 2-3l-1 4-2 6-1 1c-2 3-3 6-5 10v-1l1-6 1-4z" class="r"></path><path d="M332 115c2 0 4 1 5 1s1 1 1 1h1c1-2 4-2 7-2h0v1l3 1 1 1c-1 1-1 1-2 1v1h4c1 0 2 1 2 1l3 1h-2c-1 1-1 1-2 1h-2c-1 0-1-1-2 0h-1c-2-1-2-1-4 0-1 0-2 0-3 1h0-1v-1h-4-5-3l-2 1c-1 0-3-1-4-2h1c-1-1-1-2-2-2l1-2c2 1 5-1 7-2 1-1 2-1 3-1z" class="S"></path><path d="M333 118c1 0 2-1 3-1h0c-1 2-5 3-6 5 0-1-1-1-2-2l-1 1c0-1-1-1-1-1 1-1 2-1 4-1h1c1 0 1 0 2-1z" class="T"></path><path d="M323 122c1-1 1-2 3-2 0 0 1 0 1 1l1-1c1 1 2 1 2 2 0 0-1 1-2 1l-2 1c-1 0-3-1-4-2h1z" class="k"></path><path d="M322 118c2 1 5-1 7-2 1-1 2-1 3-1l1 1-1 2h1c-1 1-1 1-2 1h-1c-2 0-3 0-4 1-2 0-2 1-3 2-1-1-1-2-2-2l1-2zm9 5c1-1 2-1 3-2v-1c2-1 3-1 4-2h1l1 1c1 0 1-1 2-1 2 1 4 1 6 1v1h4c1 0 2 1 2 1l3 1h-2c-1 1-1 1-2 1h-2c-1 0-1-1-2 0h-1c-2-1-2-1-4 0-1 0-2 0-3 1h0-1v-1h-4-5z" class="AR"></path><path d="M340 119c1 0 1-1 2-1 2 1 4 1 6 1v1h4c1 0 2 1 2 1h-8c-2 0-4 1-5 1h-3l-1-1 3-2z" class="r"></path><path d="M556 268h0v3c0 2 1 3 2 4 0 1 1 1 1 2-1 2 0 5 0 8v10l-1 1-1 3c-1 1-2 2-2 4h-1c0-1 1 0 1-1v-3h-1c-1 1-1 2-2 3 0 1 1 2 0 3 0 0-1 1-1 2 1 2-1 3-1 6-1 1-1 2-1 4-1 0-1 1-1 1 0 2-1 3-2 4h0v2l-2-1c0 1 0 1-1 2l1-5c0-2 1-4 1-6l3-10v1l1 1h1v-3c0-2-1-3 0-4s1-2 1-3l1-8c2-5 1-11 2-16l2-4z" class="n"></path><path d="M544 323v-1c0-1 0-1 1-1l1 1v2l-2-1z" class="W"></path><path d="M550 313l1-7v-1c0-1 1-2 1-3 0 1 1 2 0 3 0 0-1 1-1 2 1 2-1 3-1 6z" class="x"></path><path d="M556 271c0 2 1 3 2 4h-1c-1 2-1 3-1 5h0-1c0-3 1-6 1-9h0z" class="W"></path><path d="M556 280c0-2 0-3 1-5h1c0 1 1 1 1 2-1 2 0 5 0 8v10l-1 1-1 3c-1 1-2 2-2 4h-1c0-1 1 0 1-1v-3h0c0-1-1-1-1-2l2-17z" class="q"></path><path d="M559 285v10l-1 1-1 3c-1 1-2 2-2 4h-1c0-1 1 0 1-1v-3h0c0-1-1-1-1-2l1 1 2-2h0v-3h0c1-1 1-3 1-4s0-2 1-4z" class="k"></path><path d="M451 93l7 2-1 1 17 7 13 8c1-1 1-1 1-2l3 2 9 7h1 1c0-1 0-2 1-3 0 1 0 1 1 1h0c1 1 2 1 2 1l1 1v1c-1 1-2 2-3 4l2 1h-2-1v2l-1-1-1 1-1 1c-2-3-4-5-7-7-13-11-29-19-46-25h1 2 2 0l-2-1 1-1z" class="X"></path><path d="M457 96l17 7 13 8c1-1 1-1 1-2l3 2 9 7h1 1c0-1 0-2 1-3 0 1 0 1 1 1h0c1 1 2 1 2 1l1 1v1c-1 1-2 2-3 4l2 1h-2-1v2l-1-1-1 1-1-3c0-1-1-2-2-3l-12-9c-4-2-8-5-12-7l-12-6c-2-1-4-1-6-2h1z" class="W"></path><path d="M503 115c0 1 0 1 1 1h0c1 1 2 1 2 1l1 1v1c-1 1-2 2-3 4 0 1 0 1-1 1 0-2 0-2 1-3l-1-1c-1 0-1 0-2 1h-1c1-1 1-2 2-3 0-1 0-2 1-3z" class="d"></path><path d="M503 115c0 1 0 1 1 1h0c-1 2-1 2-1 4h0c-1 0-1 0-2 1h-1c1-1 1-2 2-3 0-1 0-2 1-3z" class="T"></path><path d="M488 109l3 2 9 7h1 1c-1 1-1 2-2 3 0-1-1-2-2-3l-11-7c1-1 1-1 1-2z" class="AP"></path><path d="M446 88l21 1h0c-2 0-3 0-5 1v1h4l1 1h0 3l1 1h0l-1 1h1 2c2 2 5 2 7 3h-2v1h1l1 1v1c0 1 1 2 2 2v1l2 1c1 1 2 1 3 2s2 2 3 2c1 1 1 2 1 3l-3-2c0 1 0 1-1 2l-13-8-17-7 1-1-7-2-1 1 2 1h0-2-2-1l-9-3 2-1c-3 0-5-1-7-2h3c-1 0-1 0-1-1h11z" class="AA"></path><path d="M458 95c6 1 12 3 17 6-2 0-2 0-4-1-1 0-1-1-2-1v1c1 0 2 1 3 1s1 1 2 2l-17-7 1-1z" class="AC"></path><path d="M440 91c1 0 9 1 11 2l-1 1 2 1h0-2-2-1l-9-3 2-1z" class="AH"></path><path d="M474 103c-1-1-1-2-2-2s-2-1-3-1v-1c1 0 1 1 2 1 2 1 2 1 4 1l8 4 1-1c1 1 2 1 3 2s2 2 3 2c1 1 1 2 1 3l-3-2c0 1 0 1-1 2l-13-8z" class="AG"></path><path d="M484 104c1 1 2 1 3 2s2 2 3 2c1 1 1 2 1 3l-3-2-5-4 1-1z" class="AN"></path><defs><linearGradient id="p" x1="458.38" y1="85.126" x2="462.601" y2="103.057" xlink:href="#B"><stop offset="0" stop-color="#a18b90"></stop><stop offset="1" stop-color="#d1d8d5"></stop></linearGradient></defs><path fill="url(#p)" d="M446 88l21 1h0c-2 0-3 0-5 1v1h4l1 1h0 3l1 1h0l-1 1h1 2c2 2 5 2 7 3h-2v1h1l1 1v1c-4-1-8-3-13-4-7-3-14-4-22-5l-9-2c-1 0-1 0-1-1h11z"></path><path d="M470 92h1s1 0 1-1l1 1h5l9 2v1c3 1 7 2 10 3l10 6v1l2 2h-1l-4 5s-1 0-1-1h-2c0 1 1 0 1 1l1 2v1c-1 1-1 2-1 3h-1-1l-9-7c0-1 0-2-1-3-1 0-2-1-3-2s-2-1-3-2l-2-1v-1c-1 0-2-1-2-2v-1l-1-1h-1v-1h2c-2-1-5-1-7-3h-2-1l1-1h0l-1-1z" class="i"></path><path d="M500 102v-1l3 3 1 1h3l2 2h-1-3c-2-1-4-3-5-5zm-20-5h0c3 1 5 1 8 1v1h-2l-3 3h-1c-1 0-2-1-2-2v-1l-1-1h-1v-1h2z" class="o"></path><path d="M486 99c2 0 3 1 4 2h0l-1 2v1l-2 2c-1-1-2-1-3-2l-2-1v-1h1l3-3z" class="q"></path><path d="M482 102h1 3c1 1 2 1 3 2l-2 2c-1-1-2-1-3-2l-2-1v-1z" class="w"></path><path d="M470 92h1s1 0 1-1l1 1h5l9 2v1c3 1 7 2 10 3l10 6v1h-3l-1-1-3-3v1l-2-1-1 1h1l-1 1-3-3c-1-1-5-3-7-3h-2c-1 0-2 0-3-1-1 0-2 0-2 1h0c-2-1-5-1-7-3h-2-1l1-1h0l-1-1z" class="X"></path><path d="M487 95c3 1 7 2 10 3l10 6v1h-3l-1-1-3-3c-2-2-5-2-8-3-2-1-3-2-5-2h0v-1z" class="i"></path><path d="M489 103l1-2 3 3 1 1c2 1 4 3 5 5 1 0 1 1 1 1l1 1h1l1 2v1c-1 1-1 2-1 3h-1-1l-9-7c0-1 0-2-1-3-1 0-2-1-3-2l2-2v-1z" class="o"></path><path d="M489 104v-1c0 2 2 3 4 4-1 1-2 1-3 1s-2-1-3-2l2-2z" class="r"></path><path d="M493 107c3 4 6 7 8 11h-1l-9-7c0-1 0-2-1-3 1 0 2 0 3-1z" class="AV"></path><path d="M534 134c1 0 1 0 2-1 1 1 1 1 1 2 0 2 0 3-1 5l1 1 1-1v1 4c0 1 0 1 1 2v6h1l1 9c0 1 0 2 1 2 1 4 2 9 2 12 1 2 1 3 2 5l1 6c-5-6-7-15-11-21h-1c-1 0-2-2-2-2-1-1-1 0-2-1 0-2 0-3-1-4h-3v-3h-1v-1c1-1 1-2 1-3v-1c1-1 1-1 1-2v-1c-2-2-3-4-4-6v-2h1 0l1-1h2 0l1-1c0-1 0-2 1-3h2v2l1-2 1-1z" class="AS"></path><path d="M543 174l1 2c1 2 1 3 2 5h-1l-2-2v-1l-1-2c1-1 1-2 1-2z" class="AN"></path><path d="M540 165l3 9s0 1-1 2c0-1-1-2-1-3l-1-3c0-1-1-1-1-2 1-1 1-2 1-3z" class="AL"></path><path d="M526 155c1 0 2-2 3-3h1l1 2c-1 0-1 0-2 1l1 1c1 1 1 0 1 1v1c1 0 1 0 1 1l1 1v1c1 1 3 3 3 5h-1c-1 0-2-2-2-2-1-1-1 0-2-1 0-2 0-3-1-4h-3v-3h-1v-1z" class="AA"></path><path d="M530 147h1v1c1 0 1 1 2 1h0c1 0 1 0 1-1 1 0 1 1 1 1l1 3c0 1 1 1 1 2 1 1 1 4 1 5l2 6c0 1 0 2-1 3-1-2-2-5-4-7 0-1 0-1-1-2l-3-5c0-1 0-2 1-3h0c-1-1-1-2-2-2v-1-1z" class="r"></path><path d="M534 148c1 0 1 1 1 1v3 2c-1-1-1-3-2-5 1 0 1 0 1-1z" class="AN"></path><path d="M531 154c0-1 0-2 1-3 0 1 0 2 1 2v2c0 1 1 2 1 2v2l-3-5z" class="AL"></path><path d="M535 149l1 3c0 1 1 1 1 2 1 1 1 4 1 5 0-1-1-1-1-2-1-1-1-2-2-3v-2-3z" class="Ac"></path><path d="M530 135h2v2 1 2 1l1 1c1 1 1 1 2 3l-1 2v1c0 1 0 1-1 1h0c-1 0-1-1-2-1v-1h-1v1 1c1 0 1 1 2 2h0c-1 1-1 2-1 3h0l-1-2h-1c-1 1-2 3-3 3 1-1 1-2 1-3v-1c1-1 1-1 1-2v-1c-2-2-3-4-4-6v-2h1 0l1-1h2 0l1-1c0-1 0-2 1-3z" class="n"></path><path d="M532 141l1 1c1 1 1 1 2 3l-1 2v1c0 1 0 1-1 1h0c-1 0-1-1-2-1v-1h-1l1-1s1-1 1-2v-3z" class="AV"></path><path d="M533 142c1 1 1 1 2 3l-1 2c0-1-2-2-2-3l1-1v-1z" class="AN"></path><path d="M530 135h2v2 1 2 1l-1 1v3c-1 0-1 1-2 1l-1-1h1l-1-1v-1-1-1l-3-1h0l1-1h2 0l1-1c0-1 0-2 1-3z" class="AM"></path><path d="M530 135h2v2 1 2h0l-1 1h0l-1 1c0-1-1-1-1-1 0-1 1-1 1-2h-2l1-1c0-1 0-2 1-3z" class="p"></path><path d="M534 134c1 0 1 0 2-1 1 1 1 1 1 2 0 2 0 3-1 5l1 1 1-1v1 4c0 1 0 1 1 2v6h1l1 9c0 1 0 2 1 2 1 4 2 9 2 12l-1-2-3-9-2-6c0-1 0-4-1-5 0-1-1-1-1-2l-1-3s0-1-1-1v-1l1-2c-1-2-1-2-2-3l-1-1v-1-2-1l1-2 1-1z" class="Aj"></path><path d="M536 140l1 1 1-1v1l-1 9c-1-2-1-3-1-5v-5z" class="AS"></path><path d="M535 145h1c0 2 0 3 1 5v4c0-1-1-1-1-2l-1-3s0-1-1-1v-1l1-2z" class="An"></path><path d="M534 134c1 0 1 0 2-1 1 1 1 1 1 2 0 2 0 3-1 5v5h-1c-1-2-1-2-2-3l-1-1v-1-2-1l1-2 1-1z" class="AT"></path><path d="M533 135l1-1c0 1 0 2 1 3l-1 1c0 1 1 2 1 2v1h-1l-1 1-1-1v-1-2-1l1-2z" class="Ac"></path><path d="M365 86h2l-15 3v2h-4l-14 4s-3 0-3 1c-1 0-2 1-3 1-2 0-6 3-8 4-1 1-3 2-4 2-3 2-8 3-10 6-1 0-1 0-2 1-1-1-2-1-4-1v1h1v1c-2 1-2 2-3 4h0l2 1h1c0 2-2 2-2 4-1 0-1 1-2 2h0v1l-2-2h0-2-1-1s-1 0-1-1c-1 1 0 1-1 1 0 1-1 2-1 2h-2l-1-1h0c0-2-1-2-1-3h-1c-1 1-2 2-4 3 0 0-1 1-2 1l-3 3v-2l-1-1-3 3c-1 0-1 0-2-1l3-2h-1l-1-1h0l1-1c-2-1-4-2-6-2 0 0-1 0-2-1l7-4v-2c0-1 1-2 0-3 2-1 4-2 6-2l1-2 14-7c5 0 9-2 13-3 1 0 1 1 2 1l3-1c8-2 17-4 26-5 8-1 16-2 25-4h6z" class="o"></path><path d="M291 114l-1-2-2-1h1 0l1-1h1 1v1h2 0v-1c1 0 1-1 1-1h2c0 1-1 2-2 2l-2 2c-1 0-1 0-2 1z" class="AP"></path><path d="M297 109h1v2l2-1h1v1c-2 1-2 2-3 4h0 0c0 1 0 1-1 2v-1c0-1 0-2-1-2v-1l1-1h0l-2 1h-2 0l2-2c1 0 2-1 2-2z" class="p"></path><path d="M300 109c-2 0-3-2-5-3 3 0 5 1 8 2 1 0 8-4 9-5 6-3 12-6 19-7-1 0-2 1-3 1-2 0-6 3-8 4-1 1-3 2-4 2-3 2-8 3-10 6-1 0-1 0-2 1-1-1-2-1-4-1z" class="n"></path><path d="M293 113h2l2-1h0l-1 1v1c1 0 1 1 1 2v1c1-1 1-1 1-2h0l2 1h1c0 2-2 2-2 4-1 0-1 1-2 2h0v1l-2-2h0-2-1-1s-1 0-1-1c-1 1 0 1-1 1 0 1-1 2-1 2h-2l-1-1h0c0-2-1-2-1-3 1-1 2-2 3-2v-2c1 0 2-1 3-1h1c1-1 1-1 2-1h0z" class="AR"></path><path d="M284 119c1-1 2-2 3-2 1 1 1 2 1 4l-3 1h0c0-2-1-2-1-3z" class="d"></path><path d="M293 113h2l2-1h0l-1 1v1c-1 1-1 1-1 2 0 2-3 3-4 5 0 0-1 0-1-1-1 1 0 1-1 1 0 1-1 2-1 2h-2l-1-1 3-1c2-1 2-2 2-4l1-1c1-2 1-2 2-3h0z" class="AU"></path><path d="M293 113l1 1c0 1 0 2-2 3h0 0-2l1-1c1-2 1-2 2-3z" class="h"></path><path d="M290 117h2 0l-2 3c-1 1 0 1-1 1 0 1-1 2-1 2h-2l-1-1 3-1c2-1 2-2 2-4zm6-4v1c1 0 1 1 1 2v1c1-1 1-1 1-2h0l2 1h1c0 2-2 2-2 4-1 0-1 1-2 2h0v1l-2-2h0-2-1-1c1-2 4-3 4-5 0-1 0-1 1-2v-1z" class="j"></path><path d="M296 113v1c1 0 1 1 1 2v1c1-1 1-1 1-2h0l2 1h1c0 2-2 2-2 4-1 0-1 1-2 2h0c-1-2-1-2 0-5-1-1-1-2-1-3v-1z" class="h"></path><path d="M269 114h2c0-1 1-1 1-1 1-1 2-1 3-2l-1 2c0 1 1 1 1 1l1 1c1 0 3 0 4 1v-1h4 1c1-1 3-1 5-1-1 0-2 1-3 1v2c-1 0-2 1-3 2h-1c-1 1-2 2-4 3 0 0-1 1-2 1l-3 3v-2l-1-1-3 3c-1 0-1 0-2-1l3-2h-1l-1-1h0l1-1c-2-1-4-2-6-2 0 0-1 0-2-1l7-4z" class="R"></path><path d="M280 116v-1h4l1 2h-1c-1 0-3-1-4-1zm-13 1h1c1 0 2-1 2-2 3 0 5 0 7 1s3 1 4 2l-1 1h-3-1l-9-2z" class="AP"></path><path d="M267 117l9 2h1 3l1-1c1 0 2 0 2 1-1 1-2 2-4 3 0 0-1 1-2 1l-3 3v-2l-1-1-3 3c-1 0-1 0-2-1l3-2h-1l-1-1h0l1-1c-2-1-4-2-6-2v-1c1 0 2 0 3-1z" class="o"></path><path d="M280 119h0c-1 1-3 1-3 1-1 2-3 2-4 3l-3 3c-1 0-1 0-2-1l3-2h-1l-1-1h0l1-1c1 0 1 1 3 1 1-1 2-2 3-2l1-1h3z" class="s"></path><path d="M281 118c1 0 2 0 2 1-1 1-2 2-4 3 0 0-1 1-2 1l-3 3v-2l-1-1c1-1 3-1 4-3 0 0 2 0 3-1h0l1-1z" class="AW"></path><defs><linearGradient id="q" x1="295.28" y1="97.843" x2="299.272" y2="105.597" xlink:href="#B"><stop offset="0" stop-color="#8d211c"></stop><stop offset="1" stop-color="#6f3f3e"></stop></linearGradient></defs><path fill="url(#q)" d="M365 86h2l-15 3v2h-4v-2c-19 3-39 7-57 14-5 3-11 6-16 8-1 1-2 1-3 2 0 0-1 0-1 1h-2v-2c0-1 1-2 0-3 2-1 4-2 6-2l1-2 14-7c5 0 9-2 13-3 1 0 1 1 2 1l3-1c8-2 17-4 26-5 8-1 16-2 25-4h6z"></path><path d="M348 89h4v2h-4v-2z" class="AW"></path><path d="M275 107l6-2c-3 3-8 4-12 7 0-1 1-2 0-3 2-1 4-2 6-2z" class="Q"></path><path d="M290 98c5 0 9-2 13-3 1 0 1 1 2 1l-24 9-6 2 1-2 14-7z" class="i"></path><path d="M257 122c2-1 3-3 5-4 1 1 2 1 2 1 2 0 4 1 6 2l-1 1h0l1 1h1l-3 2c1 1 1 1 2 1l3-3 1 1v2l3-3c1 0 2-1 2-1 2-1 3-2 4-3h1c0 1 1 1 1 3h0l1 1h2c0 2-1 3 0 4l-1 1-3 3h0l-1 1c-1 2-2 3-3 5l-1 2v2l-1 3h0c-1 2-3 4-3 6l-1 1-1 3v4l-1 1h-1c0-1 0-1 1-2-2 0-3 1-5 0h0l-1 1-1-2h-1v-1c0-1 0-2-1-4h-2 0c-2 1-3 1-5 2l-1 1h-2l-1-1c-1 1-2 1-3 1s-2 0-3 1l-1-1-1 1v1h-1l-1 1-2-2c-1 0-2 0-3 1h0v-1c0-1 0-1-1-1v-1h0-1c-1-1-1-2-1-3l1-1 1-1v-1c-1 1-1 1-2 1 0-1 3-4 3-5 3-4 6-6 9-9 1 0 2-1 3-2h0c1 0 2-1 3-1h0c1-1 2-1 3-2h1v-3h-1v-1c1-1 2-2 4-3h-2z" class="X"></path><path d="M263 131h0l2 2h0c2 2 3 4 5 6v-1l2 1-2 2h-2c-1 0-1-1-2-1s-2-1-3-2h0c-1-1-1-2-2-2-1-1-3-1-4-1-1-1-2 0-3 0h-1-1l1-1h-1c1-1 3-1 5-2h1c2 1 3 1 4 1l-1-1 2-1z" class="AF"></path><path d="M263 131h0l2 2h0c2 2 3 4 5 6v-1l2 1-2 2-8-8-1-1 2-1z" class="AQ"></path><path d="M242 141l1-1c2 0 4-3 6-2h1c1-1 1-1 2-1 1 1 1 1 2 1h3c1 1 1 1 2 1l1-1c1 1 1 2 2 2h1v-2c1 1 2 2 3 2s1 1 2 1h2l2-2 1 1h1c-2 2-5 2-7 3l-3 2-2 1h-1v-1l-1 1c-1-1-1-1-2-1h-1 0v-1c1-2 0-3-1-4h-4 0-2c-2 0-3 1-5 1 0 0 0-1 1-1-2 0-3 0-4 1z" class="AP"></path><path d="M256 140c1 1 4 2 4 3 1 1 1 1 1 2l-1 1c-1-1-1-1-2-1h-1 0v-1c1-2 0-3-1-4zm17-17l1 1v2l-1 1-2 2v1l1 1h-1l1 1v2h0v2h2 0c2 2 2 3 3 6l-1 1c-1 0-2 1-3 1l-3 1c-1 1-3 2-4 2h-1c0-1-1-2-1-2l3-2c2-1 5-1 7-3h-1l-1-1-2-1v1c-2-2-3-4-5-6 1-1 1-2 1-3v-1c0-1 1-2 1-3l1-1c1 1 1 1 2 1l3-3z" class="C"></path><path d="M267 143l-1 3v1h-1c0-1-1-2-1-2l3-2z" class="w"></path><path d="M273 123l1 1v2l-1 1-2 2c-2 2-3 3-3 5s1 3 2 4h0v1c-2-2-3-4-5-6 1-1 1-2 1-3v-1c0-1 1-2 1-3l1-1c1 1 1 1 2 1l3-3z" class="i"></path><path d="M268 129v1 1h-1l-1-1 2-1z" class="o"></path><path d="M268 125c1 1 1 1 2 1-1 1-2 2-2 3l-2 1h0v-1c0-1 1-2 1-3l1-1z" class="AW"></path><path d="M257 122c2-1 3-3 5-4 1 1 2 1 2 1 2 0 4 1 6 2l-1 1h0l1 1h1l-3 2-1 1c0 1-1 2-1 3v1c0 1 0 2-1 3h0l-2-2h0l-2 1c0-1-1-1-2-1h0-7c1-1 2-1 3-2h1v-3h-1v-1c1-1 2-2 4-3h-2z" class="i"></path><path d="M258 128l-1-1v-1c2-1 3-3 5-4h3 0l-2 2c-1 1-3 2-4 2h0c-1 1-1 0-1 1v1z" class="o"></path><path d="M257 122c2-1 3-3 5-4 1 1 2 1 2 1 2 0 4 1 6 2l-1 1h0c-1 0-1-1-1-1h-1 0c-3-1-6 0-8 1h-2z" class="U"></path><path d="M265 122c1 1 3 1 5 1h1l-3 2-1 1v-1c-2 0-3 1-4 2s-2 1-3 1h-1-1v-1c0-1 0 0 1-1h0c1 0 3-1 4-2l2-2z" class="AF"></path><path d="M263 127c1-1 2-2 4-2v1c0 1-1 2-1 3v1c0 1 0 2-1 3h0l-2-2h0l-4-3h0 1c1 0 2 0 3-1z" class="AP"></path><path d="M259 128h1c1 0 2 0 3-1v3c-1-1-2-1-2-2h-2 0z" class="o"></path><path d="M266 129v1c0 1 0 2-1 3h0l-2-2c1 0 2-1 3-2z" class="Ai"></path><path d="M274 136l2-2c0 1 1 1 1 2s0 3 1 3h1v2l-1 3h0c-1 2-3 4-3 6l-1 1-1 3v4l-1 1h-1c0-1 0-1 1-2-2 0-3 1-5 0h0l-1 1-1-2h-1v-1c0-1 0-2-1-4h-2 0s0-1-1-2h1 0c1-1 1-1 2-1v-1l-1-1h0l2-1s1 1 1 2h1c1 0 3-1 4-2l3-1c1 0 2-1 3-1l1-1c-1-3-1-4-3-6z" class="Ah"></path><path d="M274 146l2-1c0-1 0-1 1-2l1 1h0c-1 2-3 4-3 6l-1 1c-1 0-1 0-2 1l-1-1c1 0 1-2 2-2 0-1 1-1 1-1v-2h0z" class="Ab"></path><path d="M272 152c1-1 1-1 2-1l-1 3v4l-1 1h-1c0-1 0-1 1-2-2 0-3 1-5 0h0c1-1 2-1 3-2 0-1 1-1 1-1h1v-2z" class="AA"></path><path d="M274 136l2-2c0 1 1 1 1 2s0 3 1 3h1v2l-1 3-1-1c-1 1-1 1-1 2l-2 1c1-1 1-2 2-3l1-1c-1-3-1-4-3-6z" class="W"></path><path d="M270 145l3-1v1c-1 1 1 2-1 2l-1 1v1c0 2 0 3-1 4 0 0 0 1-1 1h-1l-1-1c0-1 1-2 1-3v-1c1-2 2-2 3-3l-1-1z" class="AL"></path><path d="M271 146h1c-1 2-2 3-4 4v-1c1-2 2-2 3-3z" class="z"></path><path d="M262 146l2-1s1 1 1 2h1c1 0 3-1 4-2l1 1c-1 1-2 1-3 3v1c0 1-1 2-1 3l1 1c-1 1-1 2-3 2h0-1v-1c0-1 0-2-1-4h-2 0s0-1-1-2h1 0c1-1 1-1 2-1v-1l-1-1h0z" class="L"></path><path d="M261 151h4c1 1 1 1 1 2s0 1-1 2v1h-1v-1c0-1 0-2-1-4h-2z" class="N"></path><path d="M262 146l2-1s1 1 1 2 1 2 1 3h-1c-1 0-2 0-3 1l-1-2c1-1 1-1 2-1v-1l-1-1h0z" class="a"></path><path d="M284 119c0 1 1 1 1 3h0l1 1h2c0 2-1 3 0 4l-1 1-3 3h0l-1 1c-1 2-2 3-3 5l-1 2h-1c-1 0-1-2-1-3s-1-1-1-2l-2 2h0-2v-2h0v-2l-1-1h1l-1-1v-1l2-2 1-1 3-3c1 0 2-1 2-1 2-1 3-2 4-3h1z" class="n"></path><path d="M280 123l1 1v2c-1 1-1 2-3 2v-1c1-1 0-3 2-4z" class="AO"></path><path d="M277 123c0 1 1 2 1 2 0 1-1 1-1 2l-2 2v-1c-1 0-1-1-2-1l1-1 3-3z" class="AL"></path><path d="M273 127c1 0 1 1 2 1v1c-1 1-2 3-2 5l1 2h-2v-2h0v-2l-1-1h1l-1-1v-1l2-2z" class="a"></path><path d="M281 126v1h1c1 0 1 0 1 1s1 2 1 3h0l-1 1c-1 2-2 3-3 5l-1 2h-1c-1 0-1-2-1-3s-1-1-1-2c1 0 1 0 2 1l1-1c0-1 0-2 1-3v-1c0-1-1 0-2 0v-2c2 0 2-1 3-2z" class="d"></path><path d="M284 119c0 1 1 1 1 3h0l1 1h2c0 2-1 3 0 4l-1 1-3 3c0-1-1-2-1-3s0-1-1-1h-1v-1-2l-1-1-1-1c2-1 3-2 4-3h1z" class="AU"></path><path d="M286 123h2c0 2-1 3 0 4l-1 1-2-2 1-2h0v-1z" class="Q"></path><path d="M284 119c0 1 1 1 1 3h0l1 1v1h0 0-5l-1-1-1-1c2-1 3-2 4-3h1z" class="m"></path><path d="M237 143c3-4 6-6 9-9l-1 3h0c-2 1-3 2-5 4h2c1-1 2-1 4-1-1 0-1 1-1 1 2 0 3-1 5-1h2 0 4c1 1 2 2 1 4v1h0 1c1 0 1 0 2 1l1-1v1h1 0l1 1v1c-1 0-1 0-2 1h0-1c1 1 1 2 1 2-2 1-3 1-5 2l-1 1h-2l-1-1c-1 1-2 1-3 1s-2 0-3 1l-1-1-1 1v1h-1l-1 1-2-2c-1 0-2 0-3 1h0v-1c0-1 0-1-1-1v-1h0-1c-1-1-1-2-1-3l1-1 1-1v-1c-1 1-1 1-2 1 0-1 3-4 3-5z" class="AC"></path><path d="M236 147c0-1 1-2 2-2h1c1 0 4-2 5-2h1 0c-3 2-5 5-8 5h-1v-1z" class="s"></path><path d="M237 148c1 0 3 1 3 0 1 0 2-1 3-1h1c0 2 0 3-1 5h0-1l1 1h0v3l-1 1-2-2c-1 0-2 0-3 1h0v-1c0-1 0-1-1-1v-1h0-1c-1-1-1-2-1-3l1-1 1-1h1z" class="AP"></path><path d="M235 149h2c1 1 0 1 0 2v1l-1 1h0-1c-1-1-1-2-1-3l1-1z" class="X"></path><path d="M237 152h2 1c1-1 2 0 3 0h0-1l1 1h0v3l-1 1-2-2c-1 0-2 0-3 1h0v-1c0-1 0-1-1-1v-1l1-1z" class="R"></path><path d="M243 153h0v3l-1 1-2-2h-1v-1h0c2 0 2 0 3-1h1z" class="X"></path><path d="M252 140h4c1 1 2 2 1 4v1h0l1 2c-1 1-1 1-2 1l-1-1c-1-1-2-1-3-1l-2 2c-1-1-3-2-4-4 2-2 4-3 6-4z" class="AI"></path><path d="M261 145v1h1 0l1 1v1c-1 0-1 0-2 1h0-1c1 1 1 2 1 2-2 1-3 1-5 2l-1 1h-2l-1-1c-1 1-2 1-3 1s-2 0-3 1l-1-1-1 1v1h-1v-3h0l-1-1h1 4l2 1c0-1 0-1 1-2h0 1 1v-1l-2-2 2-2c1 0 2 0 3 1l1 1c1 0 1 0 2-1l-1-2h1c1 0 1 0 2 1l1-1z" class="Af"></path><path d="M243 153c2 0 3 0 4 1h2 0c-1 0-2 0-3 1l-1-1-1 1v1h-1v-3z" class="AH"></path><path d="M261 146h1 0l1 1v1c-1 0-1 0-2 1h0-1c1 1 1 2 1 2-2 1-3 1-5 2h-1c-1-1-1-1-1-2l7-5z" class="r"></path><path d="M306 129l1 1c-2 0-3 1-4 2v1 1 1c1 1 1 1 1 2l-1 2-1 1v1c1 1 1 1 1 2l-1 1h0l2 2h1v9 49l1 9c-1 2-1 4-2 6l1 21-1 24 1 12v30c0 2 0 4-1 6-1 6 1 13-2 18 1 1 1 1 2 1h2c1 1 1 2 1 4l2 1h1c0-1 0-1 1-2h0v5c-1 1-1 2-2 2 2 2 2 4 5 5h-2c-1 1-2 1-2 1v1h0-2c3 1 5 1 7 1l25-2c4-1 9 0 13 0h4c3-1 9-1 13-1l16 2h0l-1 1h0l-1 1c4 1 8 2 13 4 4 2 8 4 13 6 7 3 14 7 18 14 5 7 9 14 11 22l1 5v9 7c0 5 0 10-1 15 0 4 0 7-1 11 1 3-1 7-2 10l-4 13-1 3v1l-1 6c-1 1-1 1-1 2 0 0 0 1-1 1v1l-1 2h0v1s0 1-1 1v1c0 1 0 1-1 2 1-1 0-2 1-3h0v-2h1c-1-1-1-1 0-2v-1-3c1-13 0-25-2-38-1-6-2-11-4-17-2-8-7-16-12-22-12-13-26-20-42-24l-1 28c0 4 1 8 0 12h0v-22c0-6 0-12-1-18-19-6-40-6-60-6l1 137c-1 3 0 6 0 8v14c0 5-1 10 0 14h0l1 1v3 1c1 0 1-1 2-1v1l1 1c-2 1-2 0-3 2v1h1c2 0 2 0 3-2v2h1v1h1 0 2c-1 1-1 1-1 2h-1c0 1-1 0-1 0-1 0-3 2-3 2 1 1 1 2 2 3l-1 3c0 2 0 2-1 4v5 3l2 5c0 2 0 4 1 6h0v3 3c3 6 7 12 11 17 8 10 20 16 31 21 7 2 14 5 21 5 10 2 19 2 29-1 3 0 6-1 9-2h3v1c-9 6-19 8-29 10l-16 1h-10c-2-1-6-2-8-2h-3v-1l-8-1c-1 0-2 0-3 1h-3-3c-5 0-10-1-15-1-1-1-2-1-3-2h-1c-1 0-3-1-4-1-3-1-6-1-9-2v-1c-1-1-4-2-6-3l-25-10c-1 0-1 1-1 2l-12-6v-1c-1-1-2-1-3-2h1c1 0 1 1 2 1h0c-1-2-4-3-6-4-3-3-7-6-11-9v1c-1-1-2-1-3-2h-2c0-1-1-1-1-1h0v1c1 0 1 0 1 1l2 2v1c-1-1-3-3-5-4 0-1-1-2-2-2s-1-1-2-1h0c-1-1-1-1-2-1l-2-2-1-1c-2-3-5-6-7-9l-4-6c-1-2-3-5-4-6l-1-1v-1h0-1v-1c-1-1-1-1-1-2l-1 1c-2-6-3-11-4-17-1-2-2-5-2-8-1-6-1-13 0-19l1-1v-5l-1-4c0-1 0-1-1-1v1h-1c-1-1 0-2-1-4h0l-1 1c0-3 0-5-1-7h-1-1l2-1c0-1 1-1 2-2v-2l-1 1h0c-1-1-1-1-1-2l-2-1-1-2 1-1c0-1 1-2 2-2h0v-2-1l2-1v-2-2-1-3-3c1 0 1-2 1-3 0-2 1-5 0-8v-1l-1-1v-1-1h-1l-1 1-3-6v-1c0-1 1-1 1-1l-1-2h2v-3-1s1-1 2-1v-6c-1-5-1-11-1-16 1-6 1-11 2-17 0-2-2-4-2-6 0-1 1-1 2-2v-5-1h-1-1c0-1 0-1-1-1l-1 1h-1v-2c1 0 2-1 4-1 0 0 1 0 1-1l1-2-1-3v-1l-1-1h0c1-1 1-1 0-2 1-2 1-3 1-5l1-5c0-3 1-7-1-10l-1-1 1-1c0-1 1-1 1-2v-2c-1-2-1-5-1-7v-3-2-2l-1 2v-3c0-2 0-3-1-4s0-2-1-3c1-2 1-4 2-5h0v-1-3c1-2 0-3-1-4v-1l-1-1c-1-1-1-2-1-3 1-1 1-2 1-4-1-1 0-1 0-2v-1c0-1 0-1 1-2v-1-1s0-1 1-2c-1-1-1-1 0-2h1l1 1s1 0 1 1h8c1-1 3-1 5-1l-1-1v-1h-1l-2-3v-1c-1-2-2-3-3-5v-1l2 2h0 0l-1-1 1-1-4-11c-1-4-2-7-2-10l-1-3v-9c1-3 1-6 1-9l1-15c0-2 0-8 1-10l1-8c0-2 0-3-1-5h0c-1 0-1 0-2-1 1 0 1-1 1-2s0-4-1-5-1-1-1-2c0-2-1-4-1-6 0 0 1 0 2 1l1-1v-5c1 1 1 1 1 2l1 2h0c1-3 1-7 3-9v-1c1-1 2-3 3-5 0 1 0 1 1 2l1-3h1l3-3v1 3c1-3 2-5 3-8l2-4h1l2-2 1-1h2l3-4 1 1h1c2-2 5-4 7-4h0c1-1 2-1 3-1l2 2 1-1h1v-1l1-1 1 1c1-1 2-1 3-1s2 0 3-1l1 1h2l1-1c2-1 3-1 5-2h0 2c1 2 1 3 1 4v1h1l1 2 1-1h0c2 1 3 0 5 0-1 1-1 1-1 2h1l1-1v-4l1-3 1-1c0-2 2-4 3-6h0l1-3v-2l1-2 1 1h3c0 1 2 1 3 1l1 2 2 1-1-3h1 0 1c0-1 1-2 2-3v-1c1 0 1 0 1 1l1-1c2-1 3-2 4-3h1v1c1-1 1-1 2-1 1-1 2-2 4-3z" class="AI"></path><path d="M285 566h1l1 3-1 1h0v1l-1-1c0-2 0-2-1-3l1-1z" class="C"></path><path d="M299 153v-1c1-1 1-2 2-3 0 2 0 4-1 7h0c0-1 0-2-1-3z" class="N"></path><path d="M276 555h0l1-1c0 2 1 3 1 4h-1c-1 0-2-1-2-2l1-1z" class="G"></path><path d="M223 501l2 2v-1 1c1 1 2 2 3 4h-2l-3-6z" class="C"></path><path d="M212 494c1 2 1 4 1 6h-1v2l-1-6 1-2z" class="E"></path><path d="M303 583c0 1 1 2 2 4h-1-1l-2-2v-1h1l1-1z" class="H"></path><path d="M305 587c1 0 2 1 2 2v2c-1-1-3-2-4-3v-1h1 1z" class="B"></path><path d="M280 541l1 1h1v1c-1 0-1 0-1 1l3 3c-1-1-2-2-3-2 0-1-1-1-1-2-1 0-1-1-2-1l1-1h-1 0 2z" class="C"></path><path d="M317 621l6 3c-2 0-3 0-5-1l-1-2z" class="I"></path><path d="M276 555c-1-1-2-3-3-5h1c1 1 3 2 3 4l-1 1h0z" class="O"></path><path d="M223 514l2 5v1h-1l-2-4 1-2z" class="Y"></path><path d="M280 541c2-1 4-3 6-2l1 1 1 1h-4-3v1l-1-1z" class="B"></path><path d="M299 483c0-2 1-3 1-5 0-3 0-5 1-7 1 1 0 9 0 11l-1-2 1-1v-2c-1 2-1 4-2 6z" class="D"></path><path d="M270 375h1v5h-1l-2-2h-2-3c2-1 4-2 6 0l1 1h0v-2c-1-1-1-1 0-2z" class="V"></path><path d="M281 542v-1h3l-1 1c0 1 1 2 1 3s0 2 1 2v2h1v1c-2-1-2-1-2-3l-3-3c0-1 0-1 1-1v-1h-1z" class="H"></path><path d="M290 626l8 3s-1 0-1 1h-1l-7-3 1-1z" class="g"></path><path d="M286 571v-1c2 0 6 0 7 2v1c-2 1-6-1-7-2z" class="D"></path><path d="M309 582h0c1 2 1 2 3 3h0v3 3c-1-3-3-6-3-9z" class="AM"></path><path d="M207 511l1 11-1 1-2-12h1 1z" class="O"></path><path d="M217 570l4 7c-1 1-1 1-2 1l-3-6s0-1 1-2zm83-51l1 17-3-3 1-1v-2-1h1v-3-7z" class="E"></path><path d="M299 569c0 2 1 5-1 7h-1s-1 0-1-1c-1-1 0-2 0-3h0 3v-1h-1c0-1 1-1 1-2z" class="t"></path><path d="M271 532l11-4c-1 2-1 2-2 3-1 0-2 1-3 1h-6z" class="G"></path><path d="M299 148c1-1 2-1 2-2l1-1v2l-1 2c-1 1-1 2-2 3v1c0 1 0 1-1 2h0c0-1 0-2-1-3h0v-1c1-1 2-2 2-3z" class="Ah"></path><path d="M281 622l9 4-1 1-6-3-1-1h-1l-1-1h-1 2z" class="Y"></path><path d="M237 510l3 3c1 2 2 3 3 5-2-2-6-4-7-6l-2-2h3zm141 128h11l1 1h-1-7c-1 1-3 1-4 1h-8v-1h2c2-1 4-1 6-1z" class="C"></path><path d="M212 502v-2h1c0 1 0 3 1 4l2 9v1h-1c-1-4-3-8-3-12z" class="Y"></path><path d="M299 483c1-2 1-4 2-6v2l-1 1 1 2-1 7v-2c-1 1-1 3-2 4l-2 1 3-9z" class="B"></path><path d="M290 187c-2-1-5-4-5-6l1-1c2-2 3-1 4-1h1l-1 1v-1c-1 1-1 2-2 3 0 1 1 2 1 3h1v2z" class="E"></path><path d="M285 502c1-2 2-4 4-6-1 4-4 10-7 12l-2 1 5-7z" class="V"></path><path d="M279 567c0-1 0-2 1-3v1h1 2l2 1-1 1c-1 0-1 1-2 2v1h-1c-1-1-2-2-2-3z" class="I"></path><path d="M279 567c0-1 0-2 1-3v1h1 2c-1 1-3 2-4 2z" class="Y"></path><path d="M233 525c4 3 6 6 9 8h0-1v1l-1 1c-2-2-6-6-7-10z" class="D"></path><path d="M303 591c4 2 8 5 10 10-4-3-7-6-12-9l2-1z" class="Y"></path><path d="M278 174c3-1 7-2 10-3h3c1 0 1 1 1 2-2 0-4 0-7 1l-3 1c-1 0-3-1-4-1z" class="L"></path><path d="M215 483v-3 3l1-3v3c1 5 2 9 3 13v-1c-1 0-1-1-1-1 0-3-1-5-2-7h0v2c0 1 0 1 1 2v2c0 1 0 1 1 2v4h-1c0-6-2-11-2-16z" class="V"></path><path d="M227 408l4-3-1 2c-1 0-1 1-1 2-2 2-3 5-4 7l-3 3v-1l1-3 4-7z" class="F"></path><path d="M285 529l3-1c1 0 3 1 4 1 0-1 0-1 1-1l1 1-1 1c-1 0-2 0-3 1v1c-1 1 0 2-1 3l-1-2-2-2c-1-1-1-1-1-2h0z" class="g"></path><path d="M288 533h0c0-2 0-3 1-4 1 0 1 0 1 2v1c-1 1 0 2-1 3l-1-2z" class="J"></path><path d="M217 499h1c0 5 3 11 5 15l-1 2-1-3c-1-2-2-4-2-5-1-2-1-3-1-4-1-1-1-2-2-3l1-1v-1z" class="U"></path><path d="M350 640h8c3 1 7 0 9 1-1 1-3 1-4 2 3 0 5-1 8 0h-10c-2-1-6-2-8-2h-3v-1z" class="a"></path><path d="M295 305c2-4 4-8 5-13v9c0 2 0 3-1 5v-4h-1l-1 3-3 2 1-2z" class="b"></path><path d="M274 603l1 1h1c2 2 5 3 7 4 3 1 6 3 8 5h1l-1 1h-1c-1 0-3-1-4-2l-2-1-1-1h0-1l-1-2c-1 0-2-1-3-1-1-1-2-1-2-2l-1-1s-1 0-1-1z" class="B"></path><path d="M208 522c1 7 2 15 5 22l-2 1c-2-7-3-14-4-22l1-1z" class="H"></path><path d="M240 513c1 1 2 1 3 2h4c1 1 2 1 3 1l2 2h-3c1 0 3 1 4 2h-4c-2-1-4-2-6-2-1-2-2-3-3-5z" class="D"></path><path d="M243 515h4c1 1 2 1 3 1l2 2h-3c-1-1-3-1-4-2-1 0-2-1-2-1z" class="O"></path><path d="M262 547c3 0 9 0 11 2l1 1h-1c1 2 2 4 3 5l-1 1-1-2c0-1-2-3-3-4-1 0-2-1-3-1 0 1 0 2-1 2h-1c0-1 0-1-1-2h-2v-1l-1-1z" class="D"></path><path d="M302 141c1 1 1 1 1 2l-1 1h0l2 2h1v9c-1 3 0 7 0 10v-1c-1-4-1-9-1-12v-2c0 2-1 6 0 8h0c-1 1 0 3 0 4 0-2 0-14-1-15h-1v-2l-1 1c0 1-1 1-2 2 1-3 1-5 3-7z" class="q"></path><path d="M306 542h0l1 1v3 1c1 0 1-1 2-1v1l1 1c-2 1-2 0-3 2v1h1c0 1 0 1 1 2v1h0l-2 1s0 1 1 2v1h-1s-1-1-1-2c-1-4 0-9 0-14z" class="p"></path><path d="M205 511v-13c-1-5-1-11 0-16v8l2 21h-1-1z" class="b"></path><path d="M202 466l2-8c0 1 0 1 1 1l-3 24c-1 0-1 1-1 2h0 0c0-1 0-3-1-4 0-5 1-10 2-15z" class="c"></path><path d="M254 589c-3-3-6-5-9-8-1-1-2-2-4-3h0v-1l-1-1-4-4v-1l4 4h1l1 1 6 6c1 1 2 1 2 2 2 0 4 2 5 3 0 1 0 1-1 2z" class="L"></path><path d="M240 535l1-1v1c2 2 5 3 7 5s3 4 5 7c1 1 3 3 4 5-7-5-11-12-17-17z" class="H"></path><path d="M285 618l-1-1c-3-2-5-4-9-6-1-1-2-2-4-3v-1l13 8 8 5c1 1 3 1 4 2-4-1-8-2-11-4z" class="N"></path><path d="M285 174c3-1 5-1 7-1 3 1 4 3 5 5l-2 2h0c-1-1-1-2-2-2l-1-1c-2-1-3-1-5-1v-2h-2z" class="g"></path><path d="M287 574c1 0 1 0 2 1h1 0c1 0 2 1 3 1 2 1 4 4 6 3 1-1 1-2 2-3 0-1 0-2 1-2v6l1 3-1 1h-1l-1-2-13-7v-1z" class="a"></path><path d="M300 582c1-1 1-1 0-2h2l1 3-1 1h-1l-1-2z" class="V"></path><path d="M288 527c2-1 3-1 5-1l1 1c2 0 3 0 5 2h0v1 2l-1 1c-1-1-3-2-5-3l1-1-1-1c-1 0-1 0-1 1-1 0-3-1-4-1l-3 1v-1c1 0 1-1 3-1z" class="I"></path><path d="M294 529v1c0-1 1-1 1-2 1 1 3 1 4 2v2l-1 1c-1-1-3-2-5-3l1-1z" class="U"></path><path d="M263 378h3 2l2 2c-6 0-10 2-15 4-1 0-2 0-2-1 1-2 7-4 10-5z" class="B"></path><path d="M231 435v2l1-1h1 1c0 1 1 1 2 1v-1l-1-1c0-1 0-1 1-2 0 2 2 5 4 6 1 0 1 0 1 1h1c0 1-1 1-1 2-2-2-4-4-7-4-4 0-6 1-9 4v-2l4-4s1 0 2-1z" class="I"></path><path d="M262 555c-2-1-5-5-6-7 0 0 0-1 1-1 1-1 2 0 3 0h2l1 1v1h2l-1 2h-1v2h-1v2z" class="H"></path><path d="M261 549h1s1 1 1 2v2h-1c-1-1-1-2-1-4z" class="G"></path><path d="M260 547h2l1 1v1h2l-1 2h-1c0-1-1-2-1-2h-1-1v-2z" class="V"></path><path d="M292 538s1 0 1-1h0l2-2h2c1 3 1 5 2 8 0 3 1 6 1 9-1 0-1-1-1-2-1-1-1-2-1-3-1-1-1-2-2-3l-1-1v-1h0c1-1 0-2 0-3v-3h-1v1l-1 1h-1 0z" class="C"></path><path d="M297 620c-2 0-10-5-12-7 1 0 7 4 9 4v1h1 0 1 0c-1 0-1 0-1-1h0-2v-1h1 1c-1 0-2-1-3-2h-1l1-1v1c2 1 3 2 4 2 4 0 7 4 11 5l4 1-1 1c-3-1-7-1-10-3-1 0-1-1-2-1l-1 1z" class="D"></path><path d="M216 533l1 2h2c1 3 2 6 4 9s5 7 7 10c3 4 6 9 10 13h-1c-2-2-4-5-5-7l-10-13c-1-2-4-8-6-9-1-1-2-3-2-5z" class="B"></path><defs><linearGradient id="r" x1="297.989" y1="585.588" x2="290.496" y2="575.331" xlink:href="#B"><stop offset="0" stop-color="#525052"></stop><stop offset="1" stop-color="#707472"></stop></linearGradient></defs><path fill="url(#r)" d="M287 578v-1c0-1-1-1-1-2h0 1l13 7 1 2v1l-3-1-11-6z"></path><path d="M305 165c0-3-1-7 0-10v49l1 9c-1 2-1 4-2 6v-5-7c0-2-1-5 0-7l1-1v-34z" class="x"></path><path d="M305 204l1 9c-1 2-1 4-2 6v-5-8h1v-2z" class="S"></path><path d="M205 545l1-1c3 9 6 17 11 26-1 1-1 2-1 2-4-7-7-14-9-22l-2-5z" class="AY"></path><path d="M296 544c1 1 1 2 2 3 0 1 0 2 1 3 0 1 0 2 1 2 0 5 0 10 1 15-1-2-2-5-3-6l-1-1h0c1-1 0-1 1-1v1h1v-1c0-1 0-2-1-3l1-1c0-2-1-2-2-4l-4-3h1c0-1-1-1-1-2v-1h1c1 0 1 0 2-1z" class="O"></path><path d="M293 548h1c0-1-1-1-1-2v-1h1c1 3 4 3 3 6l-4-3z" class="I"></path><path d="M293 193h1c2-1 4-3 5-4v9 1h0c-1-1-1-1-3-1h-2 0c-1-1-2-3-2-5h1z" class="K"></path><path d="M292 193h1c1 1 2 1 4 1l2 2c-1 0-1 0-1 1l-2 1h-2 0c-1-1-2-3-2-5z" class="U"></path><path d="M294 198l1-2h3v1l-2 1h-2z" class="e"></path><path d="M213 544c1 5 2 9 4 13 3 7 7 13 11 18l-1 1c-7-9-13-19-16-31l2-1z" class="V"></path><path d="M267 604l4 3v1c2 1 3 2 4 3 4 2 6 4 9 6l1 1c-1 0-3-1-5-2-4-2-8-3-11-6h0 2c-1-1-1-2-1-2v-1l-4-2v-1h1z" class="H"></path><path d="M285 174h2v2h-1l-3 2-1 1c-1 1-1 1-1 3l2 3h-2l-2-2c-1-1-1-2-2-3l-2-2c1 0 2 0 3-1l4-2 3-1z" class="F"></path><path d="M283 178c0-1-1-1 0-2l1-1c0 1 1 1 2 1h0l-3 2z" class="Z"></path><path d="M281 182l-4-4h1 1 1c1 0 1 0 2 1-1 1-1 1-1 3z" class="AY"></path><path d="M230 504c0-1-2-2-2-4-2-2-3-4-4-7 0-1-1-2-1-3-1-2-2-4-1-6 1 2 1 3 2 5h2v-1c-1-1 0-3 0-4 0 2 0 3 1 4v1c0 2 1 5 1 7l2 8z" class="C"></path><path d="M285 580c2 1 5 2 8 3 0 1 1 1 2 1h3 0l3 1 2 2v1c1 1 3 2 4 3l2 1-1 1h0c-2-2-4-3-6-4-1 0-1 0-2-1l-3-1c-2 0-4-2-6-3s-5-2-7-4h1z" class="I"></path><path d="M298 584l3 1 2 2v1l-8-4h3 0z" class="C"></path><path d="M370 346l16 2h0l-1 1h0l-1 1c-5 0-10-1-15-2-10-1-19-1-29-1 4-1 9 0 13 0h4c3-1 9-1 13-1z" class="Z"></path><path d="M216 483c0-1 0-2 1-3v-1c1 8 3 16 6 22l3 6 1 2h-1l-1 1c-1-1-5-12-6-14-1-4-2-8-3-13z" class="b"></path><path d="M215 483c-1-2-1-4-1-6 0-6 0-11 1-17v1h1v2 1-2h1c0 1-1 3 0 4v13 1c-1 1-1 2-1 3v-3l-1 3v-3 3z" class="B"></path><path d="M216 463v1-2h1c0 1-1 3 0 4v13 1c-1 1-1 2-1 3v-3c0-3-1-5-1-7v-7c0-1 0-2 1-3z" class="O"></path><path d="M294 410c1 2 1 4 3 6l2 10c-1 8 1 16 0 23-1-1-1-2-1-3v-3h0v-1l-1-1v-2-8-5h0c0-2 0-3-1-4v-2l-1-4-1-6z" class="F"></path><path d="M296 420c1 1 1 1 1 2 0 3 1 6 1 9 1 5 1 10 0 15v-3h0v-1l-1-1v-2-8-5h0c0-2 0-3-1-4v-2z" class="s"></path><path d="M211 445c1-4 3-9 4-14 1-1 1-3 2-5 1-3 3-7 6-11l-1 3v1c-1 3-2 5-3 8s-1 6-3 9h0c-2 3-2 7-4 9v-1l-1 2v-1z" class="U"></path><path d="M227 514l2 1c3 5 7 10 12 13 2 1 3 2 4 2l2 1h-2-1c0-1 0-1-1-1s-1-1-1-1l-1-1h0-2 0c1 0 1 2 2 2h1v1l4 4h0-1c-1 0-2-1-3-2-3-2-5-5-9-8l-1-2c-3-3-4-5-5-9z" class="D"></path><path d="M232 523h1l5 5 3 3h1l4 4h0-1c-1 0-2-1-3-2-3-2-5-5-9-8l-1-2z" class="B"></path><defs><linearGradient id="s" x1="220.34" y1="469.577" x2="228.499" y2="472.656" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#292b29"></stop></linearGradient></defs><path fill="url(#s)" d="M225 482c-1-1-1-2-1-2-1-2-1-4-1-5v-5c0-1-1-1-1-2 1-2 0-3 1-4 0-1 0-3 1-4 0 2 1 3 2 5l2 2v-1l1 1v1c-1 0-2-1-3-2 0 2 0 4-1 6 0 2 0 2 1 3v6l-1 1h0z"></path><defs><linearGradient id="t" x1="224.621" y1="451.747" x2="217.552" y2="445.794" xlink:href="#B"><stop offset="0" stop-color="#525454"></stop><stop offset="1" stop-color="#898b8b"></stop></linearGradient></defs><path fill="url(#t)" d="M225 436h4l-4 4v2c-6 6-7 16-8 24-1-1 0-3 0-4h-1v2-1-2c1-9 4-18 9-25z"></path><path d="M221 577c3 3 6 7 9 11 5 4 10 9 15 13v1c-5-3-9-7-13-11-3-2-5-3-7-5-3-3-4-5-6-8 1 0 1 0 2-1z" class="G"></path><path d="M282 527h1c0-1 1-2 3-3l4-4v-1 1l3-3v-1c1-1 2-2 2-4h1v-1-2h0l-1 1v2c-1 0-1 1-1 1l-1 1 7-16v14c-1 1-1 1-1 2l-1 1v-1-2h0c-1 0-1 1-1 2h0-1c-2 2-5 5-5 8-3 2-4 4-7 5h-2z" class="D"></path><path d="M306 331c1 1 1 2 1 4l2 1h1c0-1 0-1 1-2h0v5c-1 1-1 2-2 2 2 2 2 4 5 5h-2c-1 1-2 1-2 1v1h0-2c-1 0-3 0-4-1v-1c1 0 2-1 2-2 1-2-1-5 0-7v-1-1h-1c1-1 1-2 1-3v-1z" class="x"></path><path d="M308 341h1c2 2 2 4 5 5h-2c-1 1-2 1-2 1 0-1 0-2 1-3h0l-3-3h0z" class="W"></path><path d="M307 335l2 1h1c0-1 0-1 1-2h0v5c-1 1-1 2-2 2h-1c-1-2 0-4-1-6z" class="AF"></path><path d="M286 512c-1 1-2 3-3 4l-4 5c-2 1-5 4-8 5s-7 2-11 2c-3 0-5 0-8-1-4 0-8-1-11-5 3 1 6 3 9 4 4 1 8 1 11 1h3l1-1c5-2 9-5 14-8 1-1 4-2 4-4h1c0-1 1-2 2-2z" class="D"></path><path d="M245 601c5 3 9 6 14 9 7 4 15 8 22 12h-2 1l1 1h1l1 1-17-8c-3-2-5-3-8-5-2-1-5-2-7-4-2-1-4-3-6-5v-1z" class="E"></path><path d="M297 178c1 1 2 3 1 5 0 1 0 1-1 1 0 2-2 3-3 4-2 1-3 0-4-1v-2h-1c0-1-1-2-1-3 1-1 1-2 2-3v1l1-1h-1 0c1-1 1-1 2-1h0 1c1 0 1 1 2 2h0l2-2z" class="Z"></path><path d="M297 178c1 1 2 3 1 5 0 1 0 1-1 1h0c-1 1-2 1-2 2l-1-1 1-1c1-1 1-2 0-3v-1l2-2z" class="Y"></path><path d="M293 178c1 0 1 1 2 2h0v1c0 1-1 2-2 3l-1 1c0-1-1-1-1-1l-2-2 1-2 1-1h-1 0c1-1 1-1 2-1h0 1z" class="C"></path><path d="M295 180h0v1c0 1-1 2-2 3h-1c0-1 0-1 1-1v-1-1c1-1 1-1 2-1z" class="y"></path><path d="M299 307h1c0-1 0-1 1-1v3c-1 6-2 11-5 15l-1 1s1 1 0 1c0 0-1 0-2-1h-2v-1l1-1v-1l2-3 5-12z" class="Y"></path><path d="M299 307h1c0-1 0-1 1-1v3l-1-1c0 1-1 2-1 4-2 3-4 7-6 10h1v2c1 0 1 0 1 1 0 0 1 1 0 1 0 0-1 0-2-1h-2v-1l1-1v-1l2-3 5-12z" class="C"></path><path d="M216 513c1 2 1 4 2 5 1 3 2 6 4 9 1 3 2 6 4 8 4 7 9 14 14 20l-1 1c-10-13-20-25-24-42h1v-1z" class="I"></path><path d="M297 152h0c1 1 1 2 1 3h0c1-1 1-1 1-2 1 1 1 2 1 3-1 2-1 6-1 8v9c-1-2-3-3-4-4h-1c0-1-1-1-1-1-1 0-1 0-1-1 1-1 2-2 2-4v-1c1 0 1 0 1-1 0-2 1-4 2-5v-4z" class="L"></path><path d="M295 164h1 2c0 1-1 3 0 4v1h-1v-1h-2v-1h-1v-1c1-1 1-1 1-2h0z" class="B"></path><path d="M263 541h3 1c1 0 3 0 4 1h1c2 0 6 1 7 2h1v1h1c-1 1-1 1-2 0 1 1 1 2 2 2l1 1c0 1 1 1 1 2 0 0 1 1 1 2l1 1c1 0 1-1 1-1h1v-1c2-1 4-1 6-1h0-1l-1-1-1 1h-1 0-1l-1 1v-1-1-1h0l1-1v3c1 0 2-1 3-1l2-1 4 3c1 2 2 2 2 4l-1 1c0-1-1-2-2-3h-1l-3-1h0-3-1v-1l-4 4h0v-1c0-1-1-2-2-3-5-7-11-8-19-10z" class="L"></path><path d="M288 551c3 0 4 0 6 1 1 0 1 0 2 1h-1l-3-1h0-3-1v-1z" class="J"></path><path d="M266 364l2 2c0 1 1 2 2 3h0c1 1 2 2 2 3l-1 3h-1c1-1 1-2 1-3l-1-1c-1 1-1 2-2 2-2 1-4 1-6 1-3 1-6 3-9 4-1 0-2 1-3 1 4-4 10-6 15-9l-1-1c0-1-1-2-1-3h2c0-2 0-2 1-2z" class="C"></path><defs><linearGradient id="u" x1="343.1" y1="636.667" x2="339.956" y2="628.294" xlink:href="#B"><stop offset="0" stop-color="#474a49"></stop><stop offset="1" stop-color="#5f6161"></stop></linearGradient></defs><path fill="url(#u)" d="M335 633l-2-2c-2-1-4-1-5-1l-12-4v-1c2 1 3 1 5 2l2-1s2 1 3 1l1-1c3 1 6 2 9 4l13 4c2 1 6 1 7 2h-6c-5-1-10-3-15-3z"></path><path d="M327 626c3 1 6 2 9 4h-1c-5 0-10-2-14-3l2-1s2 1 3 1l1-1z" class="G"></path><path d="M279 597h0c-6-4-13-7-19-12h1v-1c2 1 3 3 5 4l10 6c3 1 5 3 8 5 1 0 1 1 2 1l2 1 13 8 3 1 3 2 6 3c1 0 1 1 2 1s1 1 1 1v1c-1 0-1-1-2-1l-2-1s-1-1-2-1l-4-2-2-1-1-1c-1 0-2-1-3-1h0l-4-2c-1-1-2-2-4-3-1 0-2-1-3-2l-3-1-7-5z" class="C"></path><path d="M207 469c1 0 1 0 1 1h0c1-1 0-2 1-3v-1l1-1c-1 0-1 0-1-1h1v-2h1c0 1 0 2-1 2v1h0 1c0 5-1 10 0 15v4h-1 0v1 4h0c-1-6-1-12-1-18h-1v13c0 4 1 8 2 12 0 1 0 4 1 4l-1 3h0c0-1 0-1-1-2v-5l-2-10c-1-5 0-11 0-17z" class="E"></path><path d="M215 499l1 2c1 1 1 2 2 3 0 1 0 2 1 4 0 1 1 3 2 5h-1c0 1 1 2 1 4 1 2 2 5 4 7 0 1 1 2 2 3 1 2 2 5 3 6l6 9c1 1 2 2 2 3-3-4-6-7-9-11-4-7-8-13-11-20-1 1 0 2 0 4-1-1-1-3-2-5l-2-9h1c0 1 0 1 1 1l-1-6z" class="f"></path><path d="M214 504h1c0 1 0 1 1 1 0 3 2 6 2 9-1 1 0 2 0 4-1-1-1-3-2-5l-2-9z" class="a"></path><defs><linearGradient id="v" x1="239.317" y1="392.451" x2="241.43" y2="395.233" xlink:href="#B"><stop offset="0" stop-color="#252525"></stop><stop offset="1" stop-color="#404142"></stop></linearGradient></defs><path fill="url(#v)" d="M253 383c0 1 1 1 2 1l-1 2c-5 3-12 6-15 10l-3 3c-1 1-5 4-5 6l-4 3c1-1 1-1 1-2 1-3 4-5 6-8a30.44 30.44 0 0 1 8-8c1-1 2-1 4-2s4-3 6-5h1z"></path><path d="M435 429l1 3 1 6h1v5c1 3-1 7-2 10l-4 13-1 3v1l1-10v-2c1-1 0-1 0-2 1-1 2-2 2-3s1-2 1-4h-1 1v-12c-1-2-1-5 0-8z" class="B"></path><path d="M437 438h1v5c1 3-1 7-2 10l-4 13c1-5 2-9 2-13 1-3 1-6 3-8v-7z" class="b"></path><path d="M283 185h2 1v2h0 0v1l1 1 1 2 1 1c1 1 2 4 3 6 1 0 1 1 1 1v-1c0-1-1-2-1-3h0c-1-1-1-1-1-2h1c0 2 1 4 2 5h0 2c2 0 2 0 3 1h0v-1c1 2 1 5 1 7v9l-1 2-3-9c-3-7-6-13-11-19l-2-3z" class="C"></path><path d="M294 198h2c2 0 2 0 3 1-1 1-1 2-1 2-1 0-1 0-2 1 0-1-1-3-2-4h0z" class="AT"></path><path d="M299 199h0v15c-1-4-1-8-3-12 1-1 1-1 2-1 0 0 0-1 1-2z" class="Ae"></path><path d="M280 531c0 1 0 1 1 1v-2c2 0 3 0 4-1 0 1 0 1 1 2-2 1-5 3-7 3-6 3-16 4-23 2-2-1-3-1-5-2l-1-1 1-1v1l5 1c5 0 10 0 15-2h6c1 0 2-1 3-1z" class="M"></path><path d="M271 532h6c-1 1-3 2-5 3s-7 0-9 0h-1c-1 0-2 0-3-1h-3c5 0 10 0 15-2z" class="K"></path><path d="M218 538c2 1 5 7 6 9l10 13c1 2 3 5 5 7h1c0 1 1 1 2 2l-1 1c-3-2-4-5-6-6 1 2 3 3 3 4l1 1v1h0-1 0l-1-1c-1 0-1-1-2-2 0 0-1 0-1-1-1-2-3-2-3-4-1 0-1-1-1-1-1-2 0 0-1-1s-1-1-1-2l-1-1-2-3-2-2 1-1-6-13z" class="C"></path><path d="M292 538h0 1l1-1v-1h1v3c0 1 1 2 0 3h0v1l1 1c-1 1-1 1-2 1h-1v1c0 1 1 1 1 2h-1l-2 1c-1 0-2 1-3 1v-3l-1 1-1 1h-1v-2c-1 0-1-1-1-2s-1-2-1-3l1-1h4l-1-1h2c1-1 2-2 3-2z" class="O"></path><path d="M288 547h-1v-1c1 0 2-1 2-1 1-1 2-2 3-2-1 1-2 2-2 3s1 2 1 3c-1 0-2 1-3 1v-3z" class="t"></path><path d="M292 538h0 1l1-1v-1h1v3c-2 1-4 2-6 4h0c0-1 0-1-1-2l-1-1h2c1-1 2-2 3-2z" class="V"></path><path d="M292 543h3l1 1c-1 1-1 1-2 1h-1v1c0 1 1 1 1 2h-1l-2 1c0-1-1-2-1-3s1-2 2-3z" class="f"></path><defs><linearGradient id="w" x1="257.683" y1="579.905" x2="266.192" y2="574.277" xlink:href="#B"><stop offset="0" stop-color="#3c3f3d"></stop><stop offset="1" stop-color="#757777"></stop></linearGradient></defs><path fill="url(#w)" d="M240 555c3 4 7 8 11 12s9 8 13 11l10 8c1 1 3 2 4 4v-1h-2-1l4 2-1 1h0c-2-1-4-3-6-5-10-7-19-15-28-24-1-2-3-4-5-7l1-1z"></path><path d="M226 481v-6c-1-1-1-1-1-3 1-2 1-4 1-6 1 1 2 2 3 2v1h0l2 2 1 1 5 3h0c-1 0-1 1-2 1-1 2-3 2-4 4l-1 1c-1 1-1 2-1 3l-1 2h0c-1 1-1 2-1 3v-1c-1-1-1-2-1-4l-1-2h0l1-1z" class="H"></path><path d="M226 481c1 0 1 1 2 2l2-3h1l-1 1c-1 1-1 2-1 3l-1 2h0c-1 1-1 2-1 3v-1c-1-1-1-2-1-4l-1-2h0l1-1z" class="V"></path><path d="M228 486c-1-1-1-1-1-2l1-1 1 1-1 2h0z" class="C"></path><path d="M233 476v1c-1 0-2 1-2 1l-1 1v-1h-1 0c1-3-1-2-2-3 0-1 0-2-1-3 1-2 1-3 1-5 0 1 0 1 2 2h0l2 2 1 1 5 3-4 1z" class="b"></path><path d="M229 469l2 2v1l-1 1c-1-1-1-2-2-3l1-1z" class="t"></path><path d="M233 476l-2-1h0v-3h1l5 3-4 1z" class="I"></path><path d="M311 549v2h1v1h1 0 2c-1 1-1 1-1 2h-1c0 1-1 0-1 0-1 0-3 2-3 2 1 1 1 2 2 3l-1 3c0 2 0 2-1 4v5 3l2 5c0 2 0 4 1 6-2-1-2-1-3-3h0c-2-4-2-9-3-14v-12c0 1 1 2 1 2h1v-1c-1-1-1-2-1-2l2-1h0v-1c-1-1-1-1-1-2h-1 1c2 0 2 0 3-2z" class="W"></path><path d="M309 556c1 1 1 2 2 3l-1 3c0 2 0 2-1 4v5c-1-3-1-7-1-10v-3s1-1 1-2z" class="Q"></path><path d="M309 556c1 1 1 2 2 3l-1 3c-1-1-1-2-2-4 0 0 1-1 1-2z" class="AF"></path><path d="M247 556h1c1 1 2 2 3 2l7 5 18 12 6 3c1 0 2 1 3 2h-1c2 2 5 3 7 4s4 3 6 3l3 1c1 1 1 1 2 1v1l1 1-2 1-3-1-2-1c-1 0-2-1-2-1h-1c-1-1-2-2-3-2-2-2-4-3-6-4-3-2-5-5-8-7l-13-8-16-12z" class="G"></path><path d="M298 591c-1-1-2-1-3-2l-1-1c-1 0-1-1-2-1v-1l5 2c1 1 3 2 5 2l1 1-2 1-3-1z" class="E"></path><path d="M296 492l2-1c1-1 1-3 2-4v2c0 1-1 4-2 6-1 5-3 10-6 14l-3 6-1-1c-2 2-3 4-5 5-1 1-3 1-4 2l4-5c1-1 2-3 3-4 4-6 8-13 10-20z" class="G"></path><path d="M283 516c3-2 5-4 7-7s4-7 6-10c1-2 1-3 2-5v1c-1 5-3 10-6 14l-3 6-1-1c-2 2-3 4-5 5-1 1-3 1-4 2l4-5z" class="E"></path><path d="M211 500c0 4 0 8 1 11 0 6 2 11 4 16l3 8h-2l-1-2c0 2 1 4 2 5l6 13-1 1c-4-7-7-15-10-22-2-4-3-9-4-13 0-5 1-11 0-16 1 1 1 1 1 2h0l1-3z" class="N"></path><path d="M216 527l3 8h-2l-1-2c-1-1-2-4-2-6h0l1 1 1-1z" class="H"></path><path d="M297 620l1-1c1 0 1 1 2 1 3 2 7 2 10 3l1-1 1 1c2-1 2-1 3-1l3 1c2 1 3 1 5 1l4 2-1 1c-1 0-3-1-3-1l-2 1c-2-1-3-1-5-2v1l12 4c1 0 3 0 5 1l2 2c-6-1-13-2-18-4-7-2-14-6-20-9z" class="b"></path><path d="M312 623c2-1 2-1 3-1l3 1c2 1 3 1 5 1l4 2-1 1c-1 0-3-1-3-1l-11-3z" class="E"></path><defs><linearGradient id="x" x1="298.72" y1="213.117" x2="285.384" y2="213.548" xlink:href="#B"><stop offset="0" stop-color="#868989"></stop><stop offset="1" stop-color="#a9a4a4"></stop></linearGradient></defs><path fill="url(#x)" d="M285 188c5 6 8 12 11 19l3 9c0 6 2 14 1 20v1 2h0l-1-1v1h0c-1-9-2-18-4-27-1-3-2-5-3-8h0c-1-4-4-9-7-12v-4z"></path><defs><linearGradient id="y" x1="225.576" y1="508.798" x2="236.628" y2="517.67" xlink:href="#B"><stop offset="0" stop-color="#191b1c"></stop><stop offset="1" stop-color="#3b3b3a"></stop></linearGradient></defs><path fill="url(#y)" d="M226 507h2c3 6 7 12 13 15 3 4 7 5 11 5 3 1 5 1 8 1l-1 1c-1 0-1 0-2 1h-1c-2-1-4 0-6-1h-1l-3-2h-4l-1 1c-5-3-9-8-12-13l-2-1-2-4 1-1h1l-1-2z"></path><path d="M225 510l1-1h1c1 1 3 4 3 5l-1 1-2-1-2-4z" class="c"></path><path d="M229 515l1-1c2 4 6 8 9 10 3 1 5 2 7 3h-4l-1 1c-5-3-9-8-12-13z" class="M"></path><path d="M298 629c11 4 22 7 33 9 4 0 7 1 11 1-1 0-2 0-3 1h-3-3c-5 0-10-1-15-1-1-1-2-1-3-2h-1c-1 0-3-1-4-1-3-1-6-1-9-2v-1c-1-1-4-2-6-3h1 1c0-1 1-1 1-1z" class="F"></path><defs><linearGradient id="z" x1="303.391" y1="630.685" x2="306.517" y2="636.353" xlink:href="#B"><stop offset="0" stop-color="#585858"></stop><stop offset="1" stop-color="#6f7773"></stop></linearGradient></defs><path fill="url(#z)" d="M296 630h1l10 3c1 1 3 1 4 2 0 0-1 0-1 1h4v1c-1 0-3-1-4-1-3-1-6-1-9-2v-1c-1-1-4-2-6-3h1z"></path><path d="M266 551h1c1 0 1-1 1-2 1 0 2 1 3 1 1 1 3 3 3 4l1 2c0 1 1 2 2 2h1l-1 7-5-3c-1-1-1-1-2-1-3-1-6-4-8-6v-2h1v-2h1l1-2c1 1 1 1 1 2z" class="I"></path><path d="M266 551h1c1 0 1-1 1-2 1 0 2 1 3 1 1 1 3 3 3 4-1 1-1 1-2 1 0-1-1-1-1-2-1 0-1 1-2 2-1-1 0-2-1-3-1 0-1 0-2-1z" class="O"></path><path d="M263 551h1c0 2 1 3 3 4h0c1 1 2 1 4 1h1v2h1 2l1 1h-1c-1 0-2 1-3 2v1c-1-1-1-1-2-1-3-1-6-4-8-6v-2h1v-2z" class="b"></path><path d="M263 551h1c0 2 1 3 3 4-1 0 0 0 0 1h-1c-2 0-2-2-3-3v-2z" class="L"></path><defs><linearGradient id="AA" x1="301.99" y1="260.238" x2="295.928" y2="258.988" xlink:href="#B"><stop offset="0" stop-color="#5b605d"></stop><stop offset="1" stop-color="#838283"></stop></linearGradient></defs><path fill="url(#AA)" d="M299 239h0v-1l1 1h0v-2-1c2 11 1 22 0 33l-1 9v3c1 2 0 6-1 7v1-2c1-1 0-2 0-2l1-1h-1v2h-1l-2 5v-4l1-3h0c-1 0 0 0-1-1 0 1 0 2-1 2h0c1-5 3-10 3-15 1-5 2-9 2-13v-18z"></path><path d="M296 284c1-2 2-4 3-5l-2 7-2 5v-4l1-3z" class="e"></path><path d="M291 522c0-3 3-6 5-8h1 0c0-1 0-2 1-2h0v2 1l1-1c0-1 0-1 1-2v7h0v7 3h-1 0c-2-2-3-2-5-2l-1-1c-2 0-3 0-5 1-2 0-2 1-3 1v1h0c-1 1-2 1-4 1v2c-1 0-1 0-1-1 1-1 1-1 2-3v-1h2c3-1 4-3 7-5z" class="K"></path><path d="M288 527c0-1 2-2 3-2l1 1c1-1 1-3 1-5h1c0 2 0 4-1 5-2 0-3 0-5 1z" class="g"></path><path d="M291 522c0-3 3-6 5-8h1 0c0-1 0-2 1-2h0v2 1l1-1c0 2-1 3-1 4h-1v-3h0 0c-1 3-3 4-4 6l-2 1z" class="O"></path><path d="M297 522h0c1-1 1-2 1-3h1 1 0v7 3h-1 0c-2-2-3-2-5-2 1-2 1-4 1-5l1-1c0 1 0 2 1 2v-1z" class="U"></path><path d="M294 527c1-2 1-4 1-5l1-1c0 1 0 2 1 2v-1h1v2l-1 1 1 1s1 1 1 2v1c-2-2-3-2-5-2z" class="g"></path><path d="M265 175v-1l1 1h1 0 3 0 0c1 1 1 2 2 2l1-2 5-1c1 0 3 1 4 1l-4 2c-1 1-2 1-3 1l2 2c1 1 1 2 2 3l2 2h2l2 3v4c3 3 6 8 7 12h-1 0 0c-1-2-2-3-3-5-1-4-4-6-6-10-1-1-1-2-2-3l-1 1-4-4c-1-1-2-2-4-2v1l-3-2c-1-1-3-1-4-2l-1-1c0-1 1 0 1-1 1 0 1-1 1-1z" class="N"></path><path d="M281 185h2l2 3v4c-1-2-3-4-4-7z" class="X"></path><path d="M272 179c2 1 4 3 6 5 1 0 1 1 2 2l-1 1-4-4v-1l-3-2v-1z" class="R"></path><path d="M265 175v-1l1 1h1 0 3 0 0c0 1 0 2 1 3h0l1 1v1l3 2v1c-1-1-2-2-4-2v1l-3-2c-1-1-3-1-4-2l-1-1c0-1 1 0 1-1 1 0 1-1 1-1z" class="M"></path><path d="M268 180v-2-1-1l3 2 1 1v1l3 2v1c-1-1-2-2-4-2v1l-3-2z" class="Z"></path><path d="M279 521c1-1 3-1 4-2 2-1 3-3 5-5l1 1c-5 7-14 14-23 16-5 1-10 1-15 1l-1 1-3-2-2-1c-1 0-2-1-4-2l1-1h4l3 2h1c2 1 4 0 6 1h1c1-1 1-1 2-1l1-1c4 0 8-1 11-2s6-4 8-5z" class="U"></path><path d="M241 528l1-1h4l3 2h0c1 0 2 1 3 1h0-3c2 1 3 1 4 1h-4c-1 0-2-1-3-1h-1c-1 0-2-1-4-2z" class="F"></path><defs><linearGradient id="AB" x1="314.44" y1="595.595" x2="286.356" y2="610.248" xlink:href="#B"><stop offset="0" stop-color="#545d5a"></stop><stop offset="1" stop-color="#878283"></stop></linearGradient></defs><path fill="url(#AB)" d="M278 592l1-1-4-2h1 2v1c3 1 5 3 7 4l13 8c8 4 17 7 24 12 4 2 8 5 12 8h-2c-3-1-5-3-7-4l-24-12c-7-4-16-8-23-14z"></path><path d="M228 575c2 2 4 4 5 6 1 1 2 3 4 4 3 3 7 7 11 9 1 1 2 3 4 4l1 1c1 0 2 1 3 2h1c0 1 1 1 2 1h0l-3-2c0-1 0-1-1-2h0c-2-2-6-5-7-7v-1h0c2 2 5 6 8 7h0c1 1 1 1 2 1-1-2-3-3-5-4-1-1-1-2-2-2-2-1-4-4-5-5 1 0 1 1 2 1l12 10c2 2 5 4 7 6h-1v1l4 2v1s0 1 1 2h-2 0c-2 0-5-2-6-3-4-3-8-5-11-7s-5-5-8-7-6-4-8-7c-3-3-6-7-9-10l1-1z" class="B"></path><defs><linearGradient id="AC" x1="434.308" y1="425.505" x2="440.445" y2="427.472" xlink:href="#B"><stop offset="0" stop-color="#121312"></stop><stop offset="1" stop-color="#303232"></stop></linearGradient></defs><path fill="url(#AC)" d="M439 396l1 5v9 7c0 5 0 10-1 15 0 4 0 7-1 11v-5h-1l-1-6-1-3c-1-4 0-7 0-11l1-1h0v-1l1-1v-1h0v-2h0v-1c-1-1 0-3 0-4v-4h1l-1-1v-3l1-1c0-1 0-1 1-2z"></path><path d="M437 429c0 3 1 6 1 9h-1l-1-6 1-3z" class="N"></path><path d="M435 418c1 1 1 1 1 2h0v6c0 1 1 2 1 3l-1 3-1-3c-1-4 0-7 0-11z" class="L"></path><path d="M439 396l1 5v9 7l-1 1v2c-2-2-1-4-1-7 0-2 0-4-1-6v-4h1l-1-1v-3l1-1c0-1 0-1 1-2z" class="D"></path><path d="M295 362c3-2 4-4 5-7v42c0 6-1 12-1 17v6c0-2-1-4-1-6l-2-9c1 0 1 0 1-1 1 0 1-1 1-2v-6l-1-12-1-14c0-2 0-5-1-7h0v-1z" class="t"></path><path d="M298 402c1 3 1 8 1 11-1 1 0 1-1 1l-2-9c1 0 1 0 1-1 1 0 1-1 1-2z" class="e"></path><path d="M299 359v32h0l-1-1v-8l-2-15v-4c1-1 2-3 3-4z" class="F"></path><path d="M291 145l1 2 1-1 1 1-1 1c1 0 2 0 2 1v1c0 3-1 5-2 7-1 1-1 3-2 4-1 2-2 3-4 4l-2 2-3 2-2 1c-3 0-6 1-9 1 0-1 0-1 1-1v-1-2l1-1c1 0 2 0 3-1h4c2-1 3-2 5-4 1-1 2-3 3-5s2-3 3-6v-5z" class="AH"></path><path d="M290 158c1-1 2-3 2-5 0 2 0 3 1 4-1 1-1 3-2 4-1-1-1-2-1-3z" class="AW"></path><path d="M293 148c1 0 2 0 2 1v1c0 3-1 5-2 7-1-1-1-2-1-4 1-1 1-4 1-5z" class="AP"></path><path d="M273 166c1 0 2 0 3-1h4c-1 0-2 0-2 1-1 1-1 1-2 1l-2 2h1l3-2c1 1 1 1 1 2l1 1c-3 0-6 1-9 1 0-1 0-1 1-1v-1-2l1-1z" class="AJ"></path><path d="M280 167v-1c1-1 2-1 3-1s2-2 2-2c2-1 3-2 4-3h0l1-2c0 1 0 2 1 3-1 2-2 3-4 4l-2 2-3 2-2 1-1-1c0-1 0-1-1-2h2z" class="R"></path><path d="M284 166c0-1 1-1 2-2l1 1-2 2-1-1z" class="AX"></path><path d="M284 166l1 1-3 2-1-2 3-1z" class="Af"></path><path d="M281 167l1 2-2 1-1-1c0-1 0-1-1-2h2 1z" class="Ao"></path><path d="M306 129l1 1c-2 0-3 1-4 2v1 1 1c1 1 1 1 1 2l-1 2-1 1v1c-2 2-2 4-3 7 0 1-1 2-2 3v1h0c0-1 0-1-1-2h-1v-1c0-1-1-1-2-1l1-1-1-1-1 1-1-2v-2l-1-1-1-3h1 0 1c0-1 1-2 2-3v-1c1 0 1 0 1 1l1-1c2-1 3-2 4-3h1v1c1-1 1-1 2-1 1-1 2-2 4-3z" class="AL"></path><path d="M300 132v1l-1 1-3 3v-1l-1-1c2-1 3-2 4-3h1z" class="AH"></path><path d="M297 151v-2-2h0c0-1 1-2 1-3s-1-2 0-3 1-1 1-3h1v2h2 0v1c-2 2-2 4-3 7 0 1-1 2-2 3z" class="n"></path><path d="M306 129l1 1c-2 0-3 1-4 2v1 1 1c1 1 1 1 1 2l-1 2-1 1h0-2v-2h-1s0-1 1-2v-1l-1-1 1-1c1-1 1-1 2-1 1-1 2-2 4-3z" class="AV"></path><path d="M300 138c1-1 2-2 1-3 1-1 1-1 2-1v1c0 2-1 3-2 4l-1-1z" class="q"></path><path d="M303 135c1 1 1 1 1 2l-1 2-1 1h0-2v-2h0l1 1c1-1 2-2 2-4z" class="x"></path><path d="M295 135l1 1v1c-1 1-1 3-1 4l1 1v2 3 1l-1 1c0-1-1-1-2-1l1-1-1-1-1 1-1-2v-2l-1-1-1-3h1 0 1c0-1 1-2 2-3v-1c1 0 1 0 1 1l1-1z" class="Ai"></path><path d="M292 140c1 1 3 2 3 3-1 0-1 0-2 1v-1l-2-1 1-2z" class="AH"></path><path d="M291 142l2 1v1 1 1l-1 1-1-2v-2-1z" class="R"></path><path d="M293 144c1-1 1-1 2-1l1 1v3 1l-1 1c0-1-1-1-2-1l1-1-1-1v-1-1z" class="i"></path><path d="M293 144c1-1 1-1 2-1l1 1-2 2h-1v-1-1z" class="X"></path><path d="M295 135l1 1c-2 1-3 2-4 4l-1 2v1l-1-1-1-3h1 0 1c0-1 1-2 2-3v-1c1 0 1 0 1 1l1-1z" class="Z"></path><path d="M281 374h3c1 1 2 2 3 4 2 2 4 5 5 8 1 2 2 3 3 5 0 1 0 2 1 2 0 2-1 4 0 6l-1 1 1 5 2 9c0 2 1 4 1 6v-6c1 4 1 8 0 12l-2-10c-1-8-4-15-7-22-1-1-1-2-2-4s-3-5-5-6c-1-1 0-1-1-1h0v-1c0-1 1-1 1-2l1-3c-1 0-1-1-2-2h0l-1-1z" class="D"></path><path d="M281 374h3c1 1 2 2 3 4 2 2 4 5 5 8 1 2 2 3 3 5 0 1 0 2 1 2 0 2-1 4 0 6l-1 1c-3-8-6-15-11-23-1 0-1-1-2-2h0l-1-1z" class="AC"></path><path d="M261 516c3-1 6-1 8-2 5-3 9-7 13-10l3-2-5 7c-4 5-10 10-17 11-2 1-8 1-10 0-1-1-3-2-4-2h3l-2-2c-1 0-2 0-3-1-2-1-3-2-4-4-1-1-2-1-2-2-1-1 0-2 0-3v-1c1 1 1 1 1 2 0 0 1 1 2 1 2 3 3 4 6 5 1 0 2 1 4 1 1 0 2 0 3 1 1 0 3 1 4 1z" class="I"></path><path d="M261 518h-2c1-1 1-1 2-1s2 0 3 1h-3 0z" class="K"></path><path d="M250 516h1 2l1 1-1 1h-1l-2-2z" class="E"></path><path d="M254 514c1 0 2 0 3 1 1 0 3 1 4 1h-6v-1h0l-1-1z" class="c"></path><path d="M249 518h3 1c1 0 2 1 4 0h0 4 0 0c-1 1-1 1-2 1v1c1 0 3 0 4-1v1h0c-2 1-8 1-10 0-1-1-3-2-4-2z" class="H"></path><path d="M309 604l-19-11c-3-1-6-3-8-4-5-4-9-7-13-11v-1c1 1 2 2 4 3h0 1l-1 1 1 1h1v1l1-1h0l-2-2h1c-1 0-1 0-1-1l7 4c1 1 1 0 1 1h1l1 1h1c0 1 0 1 1 1 1 1 2 2 4 3h1c2 1 4 1 5 2h0l1 1h0c1 0 2 1 2 1 2 1 5 2 7 4 3 2 5 3 7 6 1 0 1 1 2 2 0 1 1 2 3 3-3-1-6-3-9-4z" class="b"></path><path d="M289 591c-1-1-6-4-7-6 1 0 1 0 2 1 2 1 4 3 6 4 3 0 6 2 9 3 2 1 5 2 7 4h-2v1l-15-7z" class="K"></path><path d="M289 591l15 7v-1h2c3 2 5 3 7 6 1 0 1 1 2 2 0 1 1 2 3 3-3-1-6-3-9-4l1-1-1-1c-1 0-2-1-3-1l-2-2-5-2-1-1-8-5h-1z" class="B"></path><path d="M304 598v-1h2c3 2 5 3 7 6h0c-2 0-8-4-9-5z" class="M"></path><path d="M263 363l1 1 1-1c1 0 1 0 1 1-1 0-1 0-1 2h-2c0 1 1 2 1 3l1 1c-5 3-11 5-15 9-14 9-24 22-32 36v-2l7-12c1 0 1-1 2-2l6-8c2-3 4-5 6-7h-2c1-1 1-2 2-3h0c2-2 6-5 9-7l1-1 5-4 1 1c1-1 3-1 4-2l2-2c1-1 1-2 2-3z" class="J"></path><path d="M263 363l1 1 1-1c1 0 1 0 1 1-1 0-1 0-1 2h-2c0 1 1 2 1 3h-2v-1c-1 0-1 0-2 1 0 0 0 1-1 1h-2 0c-1 1-1 2-2 2h-2l2-2c1-1 3-1 4-2l2-2c1-1 1-2 2-3z" class="Y"></path><path d="M254 369l1 1-2 2-14 12h-2c1-1 1-2 2-3h0c2-2 6-5 9-7l1-1 5-4z" class="D"></path><defs><linearGradient id="AD" x1="247.245" y1="553.595" x2="261.612" y2="542.746" xlink:href="#B"><stop offset="0" stop-color="#595b5a"></stop><stop offset="1" stop-color="#747575"></stop></linearGradient></defs><path fill="url(#AD)" d="M225 519h1c2 4 3 7 6 10 7 10 16 20 26 27l16 10c4 2 8 7 13 8v1h-1 0c0 1 1 1 1 2v1h0c-8-4-15-9-22-14-9-6-18-12-25-20-5-5-8-10-12-16-2-3-3-5-4-8h1v-1z"></path><path d="M238 545c0-1-1-2-2-3l-6-9c-1-1-2-4-3-6-1-1-2-2-2-3-2-2-3-5-4-7 0-2-1-3-1-4h1l1 3 2 4c1 3 2 5 4 8 4 6 7 11 12 16 7 8 16 14 25 20 7 5 14 10 22 14h0l11 6h0-3c-1 0-2 0-2-1-3-1-6-2-8-3-1-1-2-2-3-2l-6-3-18-12-7-5c-1 0-2-1-3-2h-1 0l-5-5c-1 0-1-1-2-2s-2-3-2-4z" class="a"></path><path d="M245 535h1c5 3 11 5 17 6 8 2 14 3 19 10 1 1 2 2 2 3v1h0l4-4v1h1 3 0l3 1h1c1 1 2 2 2 3 1 1 1 2 1 3v1h-1v-1c-1 0 0 0-1 1h0l-2 2c3 2 3 4 4 7 0 1-1 1-1 2h1v1h-3v-4c-1-2-3-3-5-3s-4 0-5 1h-1l-2-1h-2-1v-1c1-3 1-6 0-9-2-12-13-11-22-13-5-2-9-4-13-7z" class="g"></path><path d="M282 563c0-1 1-1 2-2v-2l2 2v1l-4 1z" class="l"></path><path d="M286 562c2-1 5-1 7 1h1l-1 1h0c-1-1-3-2-5-1-1 0-1 0-2-1z" class="E"></path><path d="M295 557l1 1h1v2h0l-2 2h0c-2 0-2-1-3-2 1 0 1 0 2-1l1-2z" class="Z"></path><path d="M292 552l3 1h1c1 1 2 2 2 3 1 1 1 2 1 3v1h-1v-1c-1 0 0 0-1 1v-2c0-2-1-3-2-4h-1c-1 0-1-1-2-2z" class="M"></path><path d="M282 563l4-1c1 1 1 1 2 1l1 1 2 1c-2 0-4 0-5 1h-1l-2-1h-2l1-2z" class="U"></path><path d="M292 560c-2-1-3-1-5-2 0-1 1-1 1-2 2-1 2-1 4-1 1 0 2 1 3 2l-1 2c-1 1-1 1-2 1z" class="C"></path><path d="M292 555c1 0 2 1 3 2l-1 2-2-2v-2z" class="D"></path><path d="M291 488c2-4 3-9 4-13 0-2 0-5 1-6l1-1v-2c0 9-2 21-7 29l-1 1c-2 2-3 4-4 6l-3 2c-4 3-8 7-13 10-2 1-5 1-8 2-1 0-3-1-4-1 2-2 5-1 8-2 2 0 6-3 8-4 1-1 0-2 0-3l2-2-1-1c-1-1 0-2 1-3v-1c0-1 0-2-1-4 1 1 1 1 2 0-1-1-2-1-3-2l1-1c1 0 1 1 2 1-1-1-1 0 0-1h1c0-1 1-1 2-1h1c2-2 5-5 6-7l2-2v1l2 1 1 1c-1 1 0 1-1 3h1z" class="M"></path><path d="M276 493c-1-1-1 0 0-1h1c1 1 3 2 3 3-1 1-1 1-2 1-1-1-1-2-2-3z" class="G"></path><path d="M274 492c1 0 1 1 2 1 1 1 1 2 2 3h-2v-1c-1-1-2-1-3-2l1-1z" class="Y"></path><path d="M276 495v1h2c1 0 1 0 2-1v3c0 1-1 3-2 4-1 3-2 4-5 7 1-1 0-2 0-3l2-2-1-1c-1-1 0-2 1-3v-1c0-1 0-2-1-4 1 1 1 1 2 0z" class="c"></path><path d="M276 496h2v3c-1 1-1 1-2 1 1-1 0-3 0-4z" class="M"></path><path d="M276 495v1c0 1 1 3 0 4l-1 4-1-1c-1-1 0-2 1-3v-1c0-1 0-2-1-4 1 1 1 1 2 0z" class="b"></path><path d="M279 491h1c2-2 5-5 6-7l2-2v1l2 1 1 1c-1 1 0 1-1 3h1c0 3-1 4-3 6v2l-7 7h0c0-2 1-3 1-5 0-1 1-2 2-3l-2-1-3-3z" class="F"></path><path d="M288 483l2 1c-1 1-1 2-2 2l-1-1c0-1 1-1 1-2z" class="R"></path><path d="M282 494c1-2 1-3 2-4v-1c1 0 1-1 2-1v1c-1 2-1 4-2 6l-2-1zm8-10l1 1c-1 1 0 1-1 3h0l-2 4s0 1-1 2v-1-1c1-2 1-3 1-6 1 0 1-1 2-2z" class="AY"></path><path d="M207 550c2 8 5 15 9 22l3 6c2 3 3 5 6 8 2 2 4 3 7 5 4 4 8 8 13 11 2 2 4 4 6 5 2 2 5 3 7 4 3 2 5 3 8 5l17 8 6 3 7 3h-1l-25-10c-1 0-1 1-1 2l-12-6v-1c-1-1-2-1-3-2h1c1 0 1 1 2 1h0c-1-2-4-3-6-4-3-3-7-6-11-9v1c-1-1-2-1-3-2h-2c0-1-1-1-1-1-4-4-9-7-12-11-2-2-4-5-6-8h0c-2-4-5-9-7-14 0-3-2-6-2-8l-1-3c1-2 1-3 1-5z" class="V"></path><path d="M257 615c-1-1-2-1-3-2h1c1 0 1 1 2 1h0l13 6h0c-1 0-1 1-1 2l-12-6v-1z" class="K"></path><path d="M216 580h1c2 3 4 6 7 9 2 2 3 3 5 4 2 2 4 3 6 5 1 1 4 2 5 3v1c-1-1-2-1-3-2h-2c0-1-1-1-1-1-4-4-9-7-12-11-2-2-4-5-6-8z" class="O"></path><path d="M236 491c1 0 2-1 2-1 2 0 6 0 7 1h1v-1h1v1 7 2c1 1 1 1 1 2v2c0 1 1 2 0 3l1 1v2h1-1v1l1 2c-3-1-4-2-6-5-1 0-2-1-2-1 0-1 0-1-1-2v1c0 1-1 2 0 3 0 1 1 1 2 2 1 2 2 3 4 4h-4c-1-1-2-1-3-2l-3-3h-3l-1-2-3-4-2-8v-1h1 1l1-1h1v-1c2 0 3-1 4-2z" class="H"></path><path d="M233 508c1 0 2 0 3 1l1 1h-3l-1-2z" class="D"></path><path d="M232 493c2 0 3-1 4-2-1 1-1 3-2 4 0 0 0 1-1 2l-1 2h0v-3-2-1z" class="V"></path><path d="M232 499h0v2l1 1v2l-1-1c-1-1-2-3-1-5h0c-1-1-1-1-1-2h0 2v3z" class="b"></path><path d="M234 495h1 0v6h1l1 1v1c-2-1-2 0-3 0-1-2-1-4-1-6 1-1 1-2 1-2z" class="G"></path><path d="M238 498c0 4 0 5 2 8h1c0 1-1 2 0 3 0 1 1 1 2 2 1 2 2 3 4 4h-4c-1-1-2-1-3-2l-3-3-1-1v-2c-1-1-1-3-2-4 1 0 1-1 3 0v-1l-1-1c1-1 1-1 2-3z" class="b"></path><path d="M235 505h1c1 0 1 1 1 2-1 0-1 0-1-1l-1-1z" class="f"></path><path d="M236 491c1 0 2-1 2-1 2 0 6 0 7 1h1v-1h1v1 7c0-1 0-2-1-3l-1 1c-1-1-2-1-2-2-1 0-2 0-3 1h0c-1 1-2 2-2 3h0c-1 2-1 2-2 3h-1v-6h0-1c1-1 1-3 2-4z" class="b"></path><path d="M237 496c-1-1 1-2 2-3 0-1 0-1-1-2h1c1 0 2 0 3 1-1 1-3 1-4 2l-1 2z" class="I"></path><path d="M242 492c1 1 3 1 4 2v1l-1 1c-1-1-2-1-2-2-1 0-2 0-3 1h0l-2-1c1-1 3-1 4-2z" class="B"></path><path d="M237 496l1-2 2 1c-1 1-2 2-2 3h0c-1 2-1 2-2 3h-1v-6h1v1h1z" class="O"></path><path d="M240 495c1-1 2-1 3-1 0 1 1 1 2 2l1-1c1 1 1 2 1 3v2c1 1 1 1 1 2v2c0 1 1 2 0 3l1 1v2h1-1v1l1 2c-3-1-4-2-6-5-1 0-2-1-2-1 0-1 0-1-1-2v1h-1c-2-3-2-4-2-8h0c0-1 1-2 2-3h0z" class="E"></path><path d="M242 503l-3-1v-1l1-1v-1l1 1 1 1v2zm7 5l-1 1c-1 0-2 0-3-1h0 1 2 1z" class="M"></path><path d="M240 495c1-1 2-1 3-1 0 1 1 1 2 2l1-1c1 1 1 2 1 3v2c1 1 1 1 1 2v2c0 1 1 2 0 3l1 1h-1-2c0-2-3-3-4-5v-2-4-1s-1-1-2-1z" class="K"></path><path d="M243 501c1-1 1-1 2-1v1 1h-2v-1z" class="U"></path><path d="M246 495c1 1 1 2 1 3v2 1h-1 0l-1-1v-1c1-1 0-2 0-3l1-1z" class="O"></path><path d="M247 500c1 1 1 1 1 2v2c0 1 1 2 0 3l-2-3v-1h1c-1-1-1-2-1-2h0 1v-1z" class="b"></path><path d="M279 139l1-2 1 1h3c0 1 2 1 3 1l1 2 2 1 1 1v2 5c-1 3-2 4-3 6s-2 4-3 5c-2 2-3 3-5 4h-4c-1 1-2 1-3 1l-1 1c-1-1-1-2-1-3v-1h0l1-1v-3l1-1v-4l1-3 1-1c0-2 2-4 3-6h0l1-3v-2z" class="D"></path><path d="M282 140v1 2h-1l-1-2 2-1z" class="N"></path><path d="M279 139l1-2 1 1c0 1 0 1 1 2l-2 1h-1v-2z" class="z"></path><path d="M279 150h1v1c0 1 0 1 1 2v2c-2-1-2-1-3-1l1-4z" class="C"></path><path d="M278 144c1 0 1 1 2 2l1 1h0c0 1-1 2-2 2h0v-1l-2 2h-2c0-2 2-4 3-6z" class="w"></path><path d="M277 150l2-2v1 1l-1 4c-1 1-2 2-3 2l-2 2h0v-4l1-3 1-1h2z" class="N"></path><path d="M275 150h2 0v3c-1 0-2 1-3 2h0c0-1 0 0-1-1l1-3 1-1z" class="r"></path><path d="M282 152c0-2 0-4 1-6h1c0 1 1 2 1 2 0 1 0 3 1 3l1-1v1c-1 2-1 2 0 4 0 1 0 1 1 1h0c-1 2-2 4-3 5l-2-2h-1c0-1 0-1-1-2 0-1 0-1-1-1h0l1-1h0 0v-2l1-1z" class="H"></path><path d="M282 152l1-1c0 2 2 3 2 4l-1-1h-2l-1 1h0v-2l1-1z" class="u"></path><path d="M281 155l1-1h2l1 1c0 2 0 2-1 3l-1 1h-1c0-1 0-1-1-2 0-1 0-1-1-1h0l1-1h0z" class="t"></path><path d="M280 156h1c0-1 1-1 1-1h1l1 3-1 1h-1c0-1 0-1-1-2 0-1 0-1-1-1h0z" class="E"></path><path d="M281 138h3c0 1 2 1 3 1l1 2 2 1 1 1v2 5l-2 1v1 1c-1 1-1 1-1 2l-1-1c0-1 0-2 1-3v-1c-1-2-1-4-2-6-1-1-2-1-3-2l-1-1v-1c-1-1-1-1-1-2z" class="a"></path><path d="M278 154c1 0 1 0 3 1h0 0l-1 1h0c1 0 1 0 1 1 1 1 1 1 1 2h1l2 2c-2 2-3 3-5 4h-4c-1 1-2 1-3 1l-1 1c-1-1-1-2-1-3v-1h0l1-1v-3l1-1h0l2-2c1 0 2-1 3-2z" class="N"></path><path d="M271 163h1c1 1 4-1 4 1h0c-2 0-2 1-3 2l-1 1c-1-1-1-2-1-3v-1h0z" class="k"></path><path d="M280 156c1 0 1 0 1 1 1 1 1 1 1 2-1 2-2 2-4 3v-1c1 0 1-1 2-2l-2-1 2-2z" class="M"></path><path d="M280 156c1 0 1 0 1 1s0 1-1 2l-2-1 2-2z" class="U"></path><path d="M278 158l2 1c-1 1-1 2-2 2v1c-2 0-3 1-5 1l-1-1 5-3 1-1z" class="J"></path><path d="M278 158l2 1c-1 1-1 2-2 2s-1-1-1-2l1-1z" class="R"></path><path d="M278 154c1 0 1 0 3 1h0 0l-1 1h0l-2 2-1 1-5 3h0v-3l1-1h0l2-2c1 0 2-1 3-2z" class="z"></path><path d="M278 154c1 0 1 0 3 1h0 0-2c-1 0-1 1-1 1 0 1-2 2-3 2h-2l2-2c1 0 2-1 3-2z" class="a"></path><defs><linearGradient id="AE" x1="208.821" y1="457.739" x2="221.303" y2="459.432" xlink:href="#B"><stop offset="0" stop-color="#5c5d5d"></stop><stop offset="1" stop-color="#747675"></stop></linearGradient></defs><path fill="url(#AE)" d="M225 416l1 1-2 3 4-3c-4 9-9 18-11 27-1 5-2 11-2 16-1 6-1 11-1 17 0 2 0 4 1 6 0 5 2 10 2 16v1l-1 1-1-2 1 6c-1 0-1 0-1-1h-1c-1-1-1-3-1-4 0-2 0-4-1-6l-1 2-1-7v-4-1h0 1v-4c-1-5 0-10 0-15h-1 0v-1c1 0 1-1 1-2h-1v2h-1c0 1 0 1 1 1l-1 1v1c-1 1 0 2-1 3h0c0-1 0-1-1-1 0-5 1-10 2-15 1-3 1-6 2-9v1l1-2v1c2-2 2-6 4-9h0c2-3 2-6 3-9s2-5 3-8l3-3z"></path><path d="M211 465c1-3 1-7 2-10v4c0 3-1 7-1 10v12l-1-1c-1-5 0-10 0-15z" class="G"></path><path d="M225 416l1 1-2 3-7 17h-1v-1c2-3 2-6 3-9l3-8 3-3z" class="l"></path><path d="M211 480l1 1c1 6 1 12 3 18l1 6c-1 0-1 0-1-1h-1c-1-1-1-3-1-4 0-2 0-4-1-6l-1 2-1-7v-4-1h0 1v-4z" class="H"></path><path d="M210 489v-4-1h0 1c0 3 1 7 1 10l-1 2-1-7z" class="Y"></path><path d="M274 370l1-1c1-1 2-1 3 0 1 0 2 1 3 2v2 1l1 1h0c1 1 1 2 2 2l-1 3c0 1-1 1-1 2v1h0c1 0 0 0 1 1 2 1 4 4 5 6s1 3 2 4h-2l-2-2v-1c-1-1-2-1-4-1h-1l-1-1h-4l-2 1-1-1h-3s-1-1-2-1h-4l-8 2c-4 1-7 2-10 4-3 0-5 2-7 2 3-4 10-7 15-10l1-2c5-2 9-4 15-4h1v-5l1-3 1-1h1v1h0v-2z" class="g"></path><path d="M274 372c1 1 1 2 2 3v2c-1 1-2 1-2 3h1v-1s1 0 1 1v2c0 1-1 2-1 4-1 0-1 0-2-1v-5c0-1 0-3 1-4v-1c-1-1 0-2 0-3h0z" class="B"></path><path d="M273 385v-5 3h1l2-1c0 1-1 2-1 4-1 0-1 0-2-1z" class="AY"></path><path d="M256 390c0-1 1-1 1-1 0-1 1-1 1-1h1c1-1 3-1 5-1 2-1 4-2 6-2 1 0 1 1 1 1v1l2 2h-3s-1-1-2-1h-4l-8 2z" class="J"></path><path d="M271 380v2h-1c-3 0-6 1-8 2l-8 2 1-2c5-2 9-4 15-4h1z" class="G"></path><path d="M274 370l1-1c1-1 2-1 3 0 1 0 2 1 3 2v2 1l1 1h0c1 1 1 2 2 2l-1 3c0 1-1 1-1 2v1h0c1 0 0 0 1 1 2 1 4 4 5 6s1 3 2 4h-2l-2-2v-1c-1-1-2-1-4-1h-1l-1-1h-4c0-1-1-1-1-2-1 0-2 0-3-1l1-1c1 1 1 1 2 1 0-2 1-3 1-4v-2c0-1-1-1-1-1v1h-1c0-2 1-2 2-3v-2c-1-1-1-2-2-3v-2z" class="c"></path><path d="M281 373v1l1 1h0c1 1 1 2 2 2l-1 3c-2-1-2-3-2-5v-2z" class="e"></path><path d="M281 388c1 0 2 0 3 1 2 1 3 3 4 5l-2-2v-1c-1-1-2-1-4-1h-1l-1-1 1-1z" class="Z"></path><path d="M288 390l-2-1c-2-1-3-3-4-5h-1l1-1h0c1 0 0 0 1 1 2 1 4 4 5 6z" class="u"></path><path d="M275 386h1c1 0 2-1 3-1h1l-1 1c0 1 0 1 1 2h1l-1 1h-4c0-1-1-1-1-2-1 0-2 0-3-1l1-1c1 1 1 1 2 1z" class="F"></path><path d="M275 387c2 0 3 0 4-1 0 1 0 1 1 2h1l-1 1h-4c0-1-1-1-1-2z" class="E"></path><path d="M274 370l1-1c1-1 2-1 3 0 1 0 2 1 3 2v2 2c0 3-2 5-3 7-1-1-1-2-2-2 0-1-1-1-1-1v1h-1c0-2 1-2 2-3v-2c-1-1-1-2-2-3v-2z" class="C"></path><path d="M278 369c1 0 2 1 3 2h-3v-2z" class="B"></path><path d="M256 493c0-1 1-1 1-1h2c2 0 4 1 5 2h2v1c1 1 1 2 2 2l2-2h4c1 2 1 3 1 4v1c-1 1-2 2-1 3l1 1-2 2c0 1 1 2 0 3-2 1-6 4-8 4-3 1-6 0-8 2-1-1-2-1-3-1-2 0-3-1-4-1l-1-2v-1h1-1v-2l-1-1c1-1 0-2 0-3v-2c1-3 2-6 4-8 1 1 1 1 2 1s2-1 2-2z" class="t"></path><path d="M274 503l1 1-2 2-4 4v-1l1-1v-1c0-1 0-1 1-1l2-2h0l1-1z" class="N"></path><path d="M254 502c1 2 1 4 3 5l2 2h2 1c1 1 1 1 2 1h0v1h-4-3c-1-1-2-3-3-4 0 0-1 0-1-1h0v-1h1v-3h0z" class="K"></path><path d="M268 497l2-2h4c1 2 1 3 1 4v1c-1 1-2 2-1 3l-1 1h-2-2c-1-1-1-2-1-3l1-1v-1h-2 0v-1h1v-1z" class="M"></path><path d="M267 499v-1h1c0 1 1 0 1 1 1 0 1 1 1 2v2c2 0 4-2 5-3-1 1-2 2-1 3l-1 1h-2-2c-1-1-1-2-1-3l1-1v-1h-2 0z" class="c"></path><path d="M254 502c1 0 1-2 2-2 0-1 1-2 2-2 2 0 3 1 4 2h2v2h1c1 1 1 1 1 3h0c-1 2-2 2-3 3s-3 1-4 1l-2-2c-2-1-2-3-3-5z" class="C"></path><path d="M262 500h2v2h1c1 1 1 1 1 3h0c-1 0-1 0-1-1v1c-1 1-2 1-4 2v-1c0-1 1-2 1-3h-1l1-1 1-1-1-1z" class="O"></path><path d="M262 500h2v2 1l-1 1c-1-1 0-2 0-3l-1-1z" class="K"></path><path d="M256 493c0-1 1-1 1-1h2c2 0 4 1 5 2h2v1c1 1 1 2 2 2v1h-1v1h0 2v1l-1 1-3 1h-1v-2h-2c-1-1-2-2-4-2-1 0-2 1-2 2-1 0-1 2-2 2h0v3h-1c-1-2 0-4 0-6 1-2 2-4 4-5h-1v-1z" class="M"></path><path d="M256 493c0-1 1-1 1-1h2c2 0 4 1 5 2h1v1c-1 0-1 1-2 1h0c-2-2-4-2-6-2h-1v-1z" class="B"></path><path d="M264 494h2v1c1 1 1 2 2 2v1h-1v1h0 2v1l-1 1-3 1h-1v-2c1-1 0-2-1-4 1 0 1-1 2-1v-1h-1z" class="H"></path><path d="M264 494h2v1c1 1 1 2 2 2v1h-1v1c-1-2-2-3-2-4v-1h-1z" class="G"></path><path d="M256 493v1h1c-2 1-3 3-4 5 0 2-1 4 0 6v1h0c0 1 1 1 1 1 1 1 2 3 3 4l-2 1c2 1 4 2 6 1h0c2 1 2 0 4 0-3 1-6 0-8 2-1-1-2-1-3-1-2 0-3-1-4-1l-1-2v-1h1-1v-2l-1-1c1-1 0-2 0-3v-2c1-3 2-6 4-8 1 1 1 1 2 1s2-1 2-2z" class="E"></path><path d="M256 493v1h1c-2 1-3 3-4 5 0 2-1 4 0 6v1h0c0 1 1 1 1 1 1 1 2 3 3 4l-2 1c-1-1-2-2-3-4 0-1-2-3-1-5v-2c0-2 2-5 3-6 1 0 2-1 2-2z" class="u"></path><defs><linearGradient id="AF" x1="253.473" y1="508.382" x2="251.99" y2="513.097" xlink:href="#B"><stop offset="0" stop-color="#515454"></stop><stop offset="1" stop-color="#6f6f6f"></stop></linearGradient></defs><path fill="url(#AF)" d="M248 504h0c1 1 1 2 1 3 1 0 2 1 3 1 1 2 2 3 3 4 2 1 4 2 6 1h0c2 1 2 0 4 0-3 1-6 0-8 2-1-1-2-1-3-1-2 0-3-1-4-1l-1-2v-1h1-1v-2l-1-1c1-1 0-2 0-3z"></path><path d="M249 511v-1h1l1 1h-2z" class="Z"></path><path d="M240 475c1 0 1-1 2-1 3 0 4-1 7-2-1 0-1 1-1 1l-1 1 1 1v-1 1l1-1 2 2c1 0 1 0 1 1s1 1 1 2c1 2 3 5 5 7l2 1-2 1-2-1h0v1c1 2 2 3 3 3v1h-2s-1 0-1 1-1 2-2 2-1 0-2-1c-2 2-3 5-4 8 0-1 0-1-1-2v-2-7-1h-1v1h-1c-1-1-5-1-7-1 0 0-1 1-2 1-1 1-2 2-4 2v1h-1l-1 1h-1-1v1c0-2-1-5-1-7 0-1 0-2 1-3h0l1-2c0-1 0-2 1-3l1-1c1-2 3-2 4-4 1 0 1-1 2-1h1 2z" class="O"></path><path d="M230 487l1-2 1 1c0 1 0 2-1 3v-1l-1-1z" class="L"></path><path d="M238 475h2c1 1 1 1 2 1h-3l-1-1zm-3 6h0c2-2 3-3 5-3-1 1-1 2-2 3h-3z" class="f"></path><path d="M235 481h3 2 0l-1 1c-1 0-2 1-2 1l-1-1h-2l1-1z" class="K"></path><path d="M237 475h1l1 1c-3 2-6 2-7 5h-2l1-1c1-2 3-2 4-4 1 0 1-1 2-1z" class="t"></path><path d="M240 475c1 0 1-1 2-1 3 0 4-1 7-2-1 0-1 1-1 1l-1 1 1 1c1 0 1 1 1 2 1 0 1 1 1 1-3-2-5-2-8-2-1 0-1 0-2-1z" class="c"></path><path d="M228 486h0v2h1c1 0 1 0 1-1l1 1v1 3c1-1 1-2 1-2l2 1-2 2v1h-1l-1 1h-1-1v1c0-2-1-5-1-7 0-1 0-2 1-3z" class="B"></path><path d="M232 490l2 1-2 2v1h-1v-2c1-1 1-2 1-2z" class="b"></path><path d="M240 478c3-1 6-1 8 1 3 1 4 6 4 9h0c-1-1-1-1-2-1-1-1-1-2-1-3v-1l-1-1c0-1 0-1-1-2h-2-2c-1 0-2 1-3 1h0-2c1-1 1-2 2-3z" class="M"></path><path d="M243 480h2 2c1 1 1 1 1 2l1 1v1c0 1 0 2 1 3h-1-1v-1h-4l-1-1v-1l-2 1v-1h-1c-1 0-1-1-1-2l1-1c1 0 2-1 3-1z" class="g"></path><path d="M240 481c1 0 2-1 3-1l-1 1 1 1v2l1 1 1-1h1 0c0 1 1 2 2 2h-4l-1-1v-1l-2 1v-1h-1c-1 0-1-1-1-2l1-1zm8-6v-1 1l1-1 2 2c1 0 1 0 1 1s1 1 1 2c1 2 3 5 5 7l2 1-2 1-2-1h0v1c1 2 2 3 3 3v1h-2s-1 0-1 1-1 2-2 2-1 0-2-1h0l1-3 1-2c0-4-2-8-4-11 0 0 0-1-1-1 0-1 0-2-1-2z" class="Z"></path><path d="M254 489c0 1 1 2 0 4l-2 1 1-3 1-2z" class="M"></path><path d="M244 486h4v1h1 1c1 0 1 0 2 1v3h1l-1 3h0c-2 2-3 5-4 8 0-1 0-1-1-2v-2-7-1h-1c0-1-1-1-2-2h0v-2z" class="K"></path><path d="M246 487c1 1 1 2 1 3h-1c0-1-1-1-2-2h1c1 0 1-1 1-1z" class="I"></path><path d="M249 490c1 1 1 2 2 3l-1 2-1-1v-4z" class="Y"></path><path d="M244 486h4v1h0-2s0 1-1 1h-1 0v-2z" class="c"></path><path d="M250 487c1 0 1 0 2 1v3c0 1-1 1-1 2-1-1-1-2-2-3 0 0-1-2-1-3h0 1 1z" class="Z"></path><path d="M251 493c0-1 1-1 1-2h1l-1 3h0c-2 2-3 5-4 8 0-1 0-1-1-2v-2-7c1 2 1 3 2 5l1-1 1-2z" class="I"></path><path d="M232 490l1-1c1-1 1-1 2-1v-1h-2v-1-1c1 0 1 0 2-1-1-1-1 0-2-1l1-1h2l1 1s1-1 2-1c0 1 0 2 1 2h1v1l2-1v1l1 1v2h0c1 1 2 1 2 2v1h-1c-1-1-5-1-7-1 0 0-1 1-2 1-1 1-2 2-4 2l2-2-2-1z" class="t"></path><path d="M240 486h0c1-1 2-1 3-1l1 1v2h-1c-1-1-2-1-3-2z" class="f"></path><path d="M237 483s1-1 2-1c0 1 0 2 1 2h1v1l2-1v1c-1 0-2 0-3 1h0l-1-1h-1v1h-1c-1-1 0-2 0-3z" class="E"></path><path d="M243 488h1 0c1 1 2 1 2 2v1h-1c-1-1-5-1-7-1 0 0-1 1-2 1-1 1-2 2-4 2l2-2 2-2c3-1 4-1 7-1z" class="D"></path><defs><linearGradient id="AG" x1="273.947" y1="589.009" x2="268.343" y2="597.601" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#AG)" d="M237 569l1 1h0 1 0v-1l-1-1c0-1-2-2-3-4 2 1 3 4 6 6l1-1c1 1 1 2 2 3 3 2 6 4 8 7 1 2 3 3 4 4 4 3 7 6 11 9 2 1 3 2 4 2 1 1 2 2 4 2 1 2 3 3 5 4 1 1 1 2 2 2 0-1 0-1-1-1 0-1-1-1-1-2h-1l-3-2v-1c1 0 1 1 2 1h1l7 5 3 1c1 1 2 2 3 2 2 1 3 2 4 3l4 2h0c1 0 2 1 3 1l1 1 2 1 4 2c-1 0 0 0-1 1l1 1h1 0 1c0 1 1 1 2 2h1v1l2 1 1 2-3-1c-1 0-1 0-3 1l-1-1-4-1c-4-1-7-5-11-5-1 0-2-1-4-2v-1h-1c-2-2-5-4-8-5-2-1-5-2-7-4h-1l-1-1-20-14c1-1 1-1 1-2-1-1-3-3-5-3 0-1-1-1-2-2l-6-6-1-1c-2-2-3-4-5-5v-1 1l1-1z"></path><path d="M276 604c2 1 5 2 8 3l-1 1h0 0c-2-1-5-2-7-4z" class="O"></path><path d="M242 576h1 1v1c1 1 4 3 4 4h0v1l-6-6z" class="V"></path><path d="M283 608h0 0l1-1c2 1 4 3 7 3l1 1v1l-1 1c-2-2-5-4-8-5z" class="b"></path><path d="M292 614l1-1c2 0 2 1 4 2 0 0 2 1 3 1l2 2h1l2 1 1 1h1v1c-4-1-7-5-11-5-1 0-2-1-4-2z" class="H"></path><path d="M308 620s-1-1-2-1c-1-1-3-2-4-3v-1c-1-1-2-1-3-2h0l10 4h-2l-1 1 2 2h0z" class="I"></path><path d="M309 617l5 2c1 1 0 1 1 1l2 1 1 2-3-1c-1 0-1 0-3 1l-1-1-4-1v-1l1 1h1 0l-1-1h0l-2-2 1-1h2z" class="K"></path><path d="M254 589c1-1 1-1 1-2 1 1 1 1 2 1l6 5 1 1c1 0 2 1 3 2h1c0 1 1 1 2 2h0c1 1 1 1 2 1l2 2 2 1v1l-1 1-1-1-20-14z" class="H"></path><path d="M297 286h1v-2h1l-1 1s1 1 0 2v2l-2 6c0 2 0 2-1 3v2c-1 1-1 2-2 3h0c0 1-1 2-1 2v1h-1c0 1-1 1-1 2s0 1-1 2h0v1s-1 1-1 2l-1 1c0 1 0 2 1 3s1 2 1 3c1 0 1 0 1 1h1l-1-1v-1-2h-1l-1-1v-2h0c1 0 1 0 1-1h1c0-1 0-2 1-2v-1c1-1 2-2 2-3h-1l3-2-1 2 3-2 1-3h1v4 1l-5 12-2 3v1l-1 1h0l-1 3v1c-1-2-1-3-2-5 0 1-1 1-1 1l-1-3c-1 0-2 0-3-1s-2-3-3-5c-3-3-5-6-8-9 0-1-8-8-10-9l-2-2c-1-1-1-1-1-3l2-1h0l-2-2c1-1 1-1 2-3v-1h0 1l1 2h0l1 2 4 4 2 1c1 2 3 4 5 5l3 3 1 2 2 1h1l1-1h1l7-13c2-1 3-3 4-4v4l2-5z" class="B"></path><path d="M294 307l3-2c-1 3-3 6-4 9v4s0 1 1 1l-2 3h0c-1-1-1-2-1-3-1-1-1-2 0-3h0c-1-1-1-1-2-1h0c0-1 1-2 2-2l3-6z" class="u"></path><path d="M291 319h0v-1c0-2 1-3 2-4v4s0 1 1 1l-2 3h0c-1-1-1-2-1-3z" class="E"></path><path d="M280 306c2 4 5 7 7 11 0 0 1 0 1 1v1c1 2 2 3 3 5l-1 3v1c-1-2-1-3-2-5-1-1-1-3-2-4-2-4-4-7-6-10v-3z" class="s"></path><defs><linearGradient id="AH" x1="299.603" y1="306.589" x2="291.283" y2="314.687" xlink:href="#B"><stop offset="0" stop-color="#5f6060"></stop><stop offset="1" stop-color="#767575"></stop></linearGradient></defs><path fill="url(#AH)" d="M297 305l1-3h1v4 1l-5 12c-1 0-1-1-1-1v-4c1-3 3-6 4-9z"></path><path d="M297 286h1v-2h1l-1 1s1 1 0 2v2l-2 6-9 18-2-4-2-1c0-2 0-2 1-4l7-13c2-1 3-3 4-4v4l2-5z" class="E"></path><path d="M291 291c2-1 3-3 4-4v4c0 2-1 3-1 5-1 1-2 3-2 4l-1-1-2 4v-1l1-1c0-1 1-3 1-4h0l-2 2c0 1-1 2-1 4l-1 1h0c-1 1-2 4-2 5l-2-1c0-2 0-2 1-4l7-13z" class="M"></path><path d="M261 285h1l1 2h0l1 2 4 4 2 1c1 2 3 4 5 5l3 3-1 2 3 2v3c2 3 4 6 6 10 1 1 1 3 2 4 0 1-1 1-1 1l-1-3c-1 0-2 0-3-1s-2-3-3-5c-3-3-5-6-8-9 0-1-8-8-10-9l-2-2c-1-1-1-1-1-3l2-1h0l-2-2c1-1 1-1 2-3v-1h0z" class="i"></path><path d="M280 315l2 1c1 0 1 1 1 2l3 3c-1 0-2 0-3-1s-2-3-3-5z" class="s"></path><path d="M261 291l1 1 1 1c-1 0-1 0-1 1v3l-2-2c-1-1-1-1-1-3l2-1z" class="X"></path><path d="M268 296l9 8 3 2v3l-1-2c-2 0-4-2-5-3s-1-1-1-2c0-2-4-4-5-6z" class="l"></path><path d="M261 285h1l1 2h0l1 2 4 4 2 1c1 2 3 4 5 5l3 3-1 2-9-8c-1-1-3-2-5-3l-1-1-1-1h0l-2-2c1-1 1-1 2-3v-1h0z" class="H"></path><path d="M261 285h1l1 2h0l1 2-2 2v1l-1-1h0l-2-2c1-1 1-1 2-3v-1h0z" class="S"></path><path d="M263 287l1 2-2 2v1l-1-1h0c0-2 1-3 2-4z" class="z"></path><defs><linearGradient id="AI" x1="275.042" y1="456.941" x2="298.321" y2="456.122" xlink:href="#B"><stop offset="0" stop-color="gray"></stop><stop offset="1" stop-color="#9c9c9c"></stop></linearGradient></defs><path fill="url(#AI)" d="M282 390c2 0 3 0 4 1v1l2 2h2c3 7 6 14 7 22-2-2-2-4-3-6l1 6 1 4v2c1 1 1 2 1 4h0v5 8 2l1 1v1h0v3c0 1 0 2 1 3l-2 17v2l-1 1c-1 1-1 4-1 6-1 4-2 9-4 13h-1c1-2 0-2 1-3l-1-1-2-1v-1l-2 2c-1 2-4 5-6 7h-1c-1 0-2 0-2 1h-1c-1 1-1 0 0 1-1 0-1-1-2-1-1-1-2-1-4-1-1 0-1 0-2-1h5 1 2l-1-1h0c1-1 1-1 1-2h0 1 2v-2c4-2 8-7 8-12 0 0 0-1-1-1v-1c7-13 7-29 6-44 0-1 0-3-1-5v6l-1-3h-1c1-4 1-9-1-12v-2c-1-4-4-7-4-10l4 4v1l2-1v-1l-3-5c0-1-3-2-4-3l1-1c-1-2-2-3-3-5h0 1z"></path><path d="M293 468c-1-2-1-5-1-7 1-1 1-1 2-1v1h1v2h-1v1h0c0 2-1 3-1 4z" class="R"></path><path d="M291 406h0l1 5c0 1 0 3 1 5h0v1 1 1l1 1 1 3v3l1 2v1c0 1 0 2 1 2v8 2c-1-3 0-7-1-10 0-1 0-2-1-3 0 1 1 2 1 3 0 5 0 10-1 15 0-8 1-18-3-25 0-3-1-7-2-10 1-2 1-4 1-5z" class="AG"></path><path d="M293 468c0-1 1-2 1-4h0v-1c-1 6-3 12-4 17-1 1-1 1-2 1 1 2 3 2 3 4l-1-1-2-1v-1l-2 2c-1 2-4 5-6 7h-1c-1 0-2 0-2 1h-1c-1 1-1 0 0 1-1 0-1-1-2-1-1-1-2-1-4-1-1 0-1 0-2-1h5 1 2l3-1c2 0 6-5 7-7 3-4 6-9 7-14z" class="K"></path><path d="M284 401l4 4v1l2-1v-1l1 2c0 1 0 3-1 5 1 3 2 7 2 10v6c0-1 0-3-1-5v6l-1-3h-1c1-4 1-9-1-12v-2c-1-4-4-7-4-10z" class="Y"></path><path d="M290 404l1 2c0 1 0 3-1 5l-2-5 2-1v-1z" class="X"></path><path d="M283 396l1-1c-1-2-2-3-3-5h0 1c4 4 7 11 10 17 1 1 1 2 2 3l1 6 1 4v2c1 1 1 2 1 4h0v5c-1 0-1-1-1-2v-1l-1-2v-3l-1-3-1-1v-1-1-1h0c-1-2-1-4-1-5l-1-5h0l-1-2-3-5c0-1-3-2-4-3z" class="i"></path><path d="M296 422h-1v-2-1c-1-1-1-2-1-3h1l1 4v2z" class="AG"></path><path d="M282 390c2 0 3 0 4 1v1l2 2h2c3 7 6 14 7 22-2-2-2-4-3-6-1-1-1-2-2-3-3-6-6-13-10-17z" class="e"></path><defs><linearGradient id="AJ" x1="297.753" y1="251.573" x2="279" y2="248.853" xlink:href="#B"><stop offset="0" stop-color="#898a8a"></stop><stop offset="1" stop-color="#a7a7a7"></stop></linearGradient></defs><path fill="url(#AJ)" d="M288 206c0-2 0-3-1-5h0v-3l1 1c1 2 2 3 3 5h0 0 1 0l3 8c2 9 3 18 4 27v18c0 4-1 8-2 13 0 5-2 10-3 15h0c1 0 1-1 1-2 1 1 0 1 1 1h0l-1 3c-1 1-2 3-4 4l-7 13h-1l-1 1h-1l-2-1-1-2-3-3h0c1 0 1-1 2-1l7-14c5-10 8-22 9-33s1-23-2-35c-1-3-2-7-3-10h0z"></path><path d="M284 284v3c0 1-1 2-1 3l-3 6c0 1-1 2 0 3-1 1-1 1-2 1v1h0 1l2-3v1 3l-2 2-1-2-3-3h0c1 0 1-1 2-1l7-14z" class="U"></path><path d="M291 204h0 1 0l3 8c2 9 3 18 4 27v18c0 4-1 8-2 13h0l-1-1c1-8 1-16 1-24 0-14-2-27-6-41z" class="a"></path><defs><linearGradient id="AK" x1="280.751" y1="291.644" x2="292.665" y2="290.33" xlink:href="#B"><stop offset="0" stop-color="#757772"></stop><stop offset="1" stop-color="#888a8c"></stop></linearGradient></defs><path fill="url(#AK)" d="M296 269l1 1h0c0 5-2 10-3 15h0c1 0 1-1 1-2 1 1 0 1 1 1h0l-1 3c-1 1-2 3-4 4l-7 13h-1l-1 1h-1l-2-1 2-2v-3-1l5-9c1-3 2-6 4-9l2-8v6h1v2c2-3 3-8 3-11z"></path><path d="M286 289c1 1 0 3-1 5l-3 6c0 1 0 3-1 4v1l-2-1 2-2v-3-1l5-9z" class="c"></path><path d="M296 269l1 1h0c0 5-2 10-3 15h0c1 0 1-1 1-2 1 1 0 1 1 1h0l-1 3c-1 1-2 3-4 4l-7 13h-1c4-7 8-16 10-24 2-3 3-8 3-11z" class="D"></path><path d="M294 285h0c1 0 1-1 1-2 1 1 0 1 1 1h0l-1 3c-1 1-2 3-4 4 1-2 1-4 3-6z" class="Y"></path><path d="M242 440h0c1-1 2-3 2-4h1c1 2 1 4 1 6v2h1v-1h0c1 0 1-1 1-1v-1l1 1c0 1 1 3 2 4h1 1l2 1 1 1c1-1 2-1 3-1l-2-2h3v-1h6c1-1 2-1 3-1s2-1 3-1c1-1 3-1 4-1 0 1-1 1-2 2h2v1h-2l1 1c-1 1-2 1-3 2l1 1v1h-2l1 1 1 1 1 1c-2 1-5-1-7 0-1 1-2 1-4 1-1 2-4 3-5 5-1 1-2 3-3 5h0c-1 2-1 3-2 5l1 9-1 2c0-1-1-1-1-2s0-1-1-1l-2-2-1 1v-1 1l-1-1 1-1s0-1 1-1c-3 1-4 2-7 2-1 0-1 1-2 1h-2-1 0l-5-3-1-1-2-2h0v-1-1l-1-1c0-1-1-3-1-4v1h0l1 1c0 1 0 1 1 1h0v-2c-1 0 0 0 0-1-1 0-1-1-1-2h0v-2h1v2c1 0 3 2 5 2-1 0-1 0-1-1-1 0-1 0-1-1h0l-1-1c0-3 0-6 2-10v-1h0l1-1v1c1-1 2-1 3-1h0c0-1 0-1 1-1h1c1-1 2-1 2-2v-2c0-1 1-1 1-2z" class="e"></path><path d="M240 468h2c0 1 0 2-1 3h-1l-2-2 2-1z" class="F"></path><path d="M256 452l4-1-2 2c-2 1-3 2-4 4l-1 1v-1s1 0 1-1v-3l2-1z" class="Y"></path><path d="M259 454l3-2-1 2 2-1c-1 2-4 3-5 5-1 1-2 3-3 5h0l-1-1h1v-1l1-1v-1c-3 2-3 5-6 7 1-4 4-5 6-8 0-1 1-1 1-1 1-1 2-2 2-3z" class="c"></path><path d="M250 462c-2 2-4 4-7 5v1h-1-2-3c1 0 2-1 3-2h0c2-1 4-1 6-3 1 0 2-1 4-1z" class="M"></path><path d="M240 473c-2-1-3-1-4-2h-1c-1-1-1-1-1-3l-2-2 1-1h0l1 1h1c1-1 1-1 2 0h3c-1 1-2 2-3 2h3l-2 1 2 2h4l-3 2h-1z" class="E"></path><path d="M240 471h-3 0l-1-2v-1h1 3l-2 1 2 2z" class="e"></path><path d="M251 446h1 1l2 1 1 1c4 1 7 1 11 1h4l1 1 1 1 1 1c-2 1-5-1-7 0-1 1-2 1-4 1l-2 1 1-2-3 2-1-1 2-2-4 1-1-1c1 0 2 0 3-1h-1c-1-1-1-1-2-1-2 0-3-2-4-3z" class="u"></path><path d="M264 450h4c1 1 3 0 4 0l1 1 1 1c-2 1-5-1-7 0-1 1-2 1-4 1l-2 1 1-2-3 2-1-1 2-2 4-1z" class="f"></path><path d="M264 450h1c-1 1-2 2-3 2l-3 2-1-1 2-2 4-1z" class="M"></path><path d="M250 466c3-2 3-5 6-7v1l-1 1v1h-1l1 1c-1 2-1 3-2 5l1 9-1 2c0-1-1-1-1-2s0-1-1-1l-2-2-1 1v-1 1l-1-1 1-1s0-1 1-1v-1-1c-2 0-4 2-6 3h-1-1l3-2c2-1 4-3 6-5z" class="f"></path><path d="M249 470h1s1-1 1-2h1v3h1v-3l1 9-1 2c0-1-1-1-1-2s0-1-1-1l-2-2-1 1v-1 1l-1-1 1-1s0-1 1-1v-1-1z" class="E"></path><path d="M228 466c0-1-1-3-1-4v1h0l1 1c0 1 0 1 1 1h0v-2c-1 0 0 0 0-1-1 0-1-1-1-2h0v-2h1v2c1 0 1 1 2 2s2 1 3 2c1 0 2 1 3 1h-4 0l-1 1 2 2c0 2 0 2 1 3h1c1 1 2 1 4 2h1 1 1c2-1 4-3 6-3v1 1c-3 1-4 2-7 2-1 0-1 1-2 1h-2-1 0l-5-3-1-1-2-2h0v-1-1l-1-1z" class="L"></path><path d="M231 464c1 1 1 2 1 4h0c1 1 1 2 2 3-2 0-3-1-3-2v-5z" class="O"></path><path d="M231 462c1 1 2 1 3 2 1 0 2 1 3 1h-4 0l-1 1 2 2c0 2 0 2 1 3h1c1 1 2 1 4 2-3 0-5-1-6-2s-1-2-2-3h0c0-2 0-3-1-4-1 0-1-1-1-1l1-1z" class="u"></path><path d="M272 442c1-1 3-1 4-1 0 1-1 1-2 2h2v1h-2l1 1c-1 1-2 1-3 2l1 1v1h-2-4c-4 0-7 0-11-1 1-1 2-1 3-1l-2-2h3v-1h6c1-1 2-1 3-1s2-1 3-1z" class="Z"></path><path d="M266 444c1-1 2-1 3-1l-2 2h-7v-1h6z" class="b"></path><path d="M267 449c0-1 1-1 1-1l1-1c1 0 1 0 2-1 1 0 2-1 3-2l1 1c-1 1-2 1-3 2l1 1v1h-2-4z" class="M"></path><path d="M272 442c1-1 3-1 4-1 0 1-1 1-2 2-2 1-4 2-7 2l2-2c1 0 2-1 3-1z" class="G"></path><path d="M242 440h0c1-1 2-3 2-4h1c1 2 1 4 1 6v2h1v-1h0c1 0 1-1 1-1v-1l1 1c0 1 1 3 2 4s2 3 4 3c1 0 1 0 2 1h1c-1 1-2 1-3 1l1 1-2 1v3c0 1-1 1-1 1v1c-1 1-2 3-3 4-2 0-3 1-4 1-2 2-4 2-6 3h0-3c-1-1-1-1-2 0h-1l-1-1h4c-1 0-2-1-3-1-1-1-2-1-3-2s-1-2-2-2c1 0 3 2 5 2-1 0-1 0-1-1-1 0-1 0-1-1h0l-1-1c0-3 0-6 2-10v-1h0l1-1v1c1-1 2-1 3-1h0c0-1 0-1 1-1h1c1-1 2-1 2-2v-2c0-1 1-1 1-2z" class="C"></path><path d="M248 453h2v1l-3 3h-1c1 0 1-1 1-1h0l1-1c-1 0-1 0-2-2h1c0 1 0 1 1 2h0v-2z" class="B"></path><path d="M248 461l-2 2c-2 2-4 2-6 3h0-3c-1-1-1-1-2 0h-1l-1-1h4c3 0 8-2 11-4z" class="I"></path><path d="M248 461l6-8v3c0 1-1 1-1 1v1c-1 1-2 3-3 4-2 0-3 1-4 1l2-2z" class="c"></path><path d="M246 444h1v-1h0c1 0 1-1 1-1v-1l1 1c0 1 1 3 2 4s2 3 4 3c1 0 1 0 2 1h1c-1 1-2 1-3 1-1 1-3 1-4 0-2 0-3-2-5-4v-3z" class="I"></path><path d="M246 447c1-1 1-1 3-1v3s2 1 2 2h0c-2 0-3-2-5-4z" class="Y"></path><path d="M242 445c0-1 1-1 1-2h1c1 1-1 2-1 3 1 1 1 1 1 2h0c-1 1-2 2-2 3h0 1v1 3c1 0 1 1 1 1 1 2 0 1 1 2-1 2-3 3-4 4-1 0-3 0-3 1l-2-4c-1-1 0-2 1-3 0 1 0 1 1 1h-1c1-1 1-1 1-2s0-2 1-3 1-1 1-2 2-2 2-3v-2z" class="H"></path><path d="M239 452h2l1 1c0 1-1 1-1 1 0 1 1 1 1 1v1c-2 1-3 1-4 1h-1c1-1 1-1 1-2s0-2 1-3z" class="u"></path><path d="M237 447h1v1h0v-1h3c1-1 1-1 1-2v2c0 1-2 2-2 3s0 1-1 2-1 2-1 3 0 1-1 2h1c-1 0-1 0-1-1-1 1-2 2-1 3l2 4c-2 0-3 0-4-1-1 0-1 0-1-1-1 0-1 0-1-1h0l-1-1c0-3 0-6 2-10v-1h0l1-1v1c1-1 2-1 3-1z" class="D"></path><path d="M240 450c0 1 0 1-1 2h-2v-1l3-1z" class="B"></path><path d="M237 452h2c-1 1-1 2-1 3s0 1-1 2h1c-1 0-1 0-1-1l-2-1v-1c1 0 1 0 2-1v-1z" class="O"></path><path d="M283 320c1 1 2 1 3 1l1 3s1 0 1-1c1 2 1 3 2 5v-1l1-3h0v1h2c1 1 2 1 2 1 2 0 3 0 5 2h0l1 2c-1 0-1 0-1 1-1 3-1 5 0 8 0 1 1 2 1 2v2c1 1 1 1 1 2h0c0 1-1 2-1 3-1 2-1 5-1 7-1 3-2 5-5 7v1h0c1 2 1 5 1 7l1 14 1 12v6c0 1 0 2-1 2 0 1 0 1-1 1l-1-5 1-1c-1-2 0-4 0-6-1 0-1-1-1-2-1-2-2-3-3-5-1-3-3-6-5-8-1-2-2-3-3-4h-3v-1-2c-1-1-2-2-3-2-1-1-2-1-3 0l-1 1v2h0v-1h-1l-1 1c0-1-1-2-2-3h0c-1-1-2-2-2-3 1-1 2-1 2-3v-1-2c2 1 4 0 6 0h6c-2-2-4 0-7-1 0 0-1 0-1-1h-1l-1-1h2c2-1 5-1 7-1 3 0 5-1 7-3l1-1h1l-2-9-1 1-6-2-10-6h0l-1-1h1l1 1h0 2l5-5c0-1 1-1 1-2 1-1 1-2 2-3 0-1 2-1 3-1l-2-5z" class="AY"></path><path d="M296 393c0 2 0 4 1 6v1h1v2c0 1 0 2-1 2 0 1 0 1-1 1l-1-5 1-1c-1-2 0-4 0-6z" class="J"></path><path d="M294 375c1 3 2 9 1 12 0-1 0-2-1-3l-1-7v-2h0 1z" class="f"></path><path d="M288 323c1 2 1 3 2 5 0 1-1 2-2 3h0c-1 0-1-1-1-1 1-3 0-4 0-6 0 0 1 0 1-1z" class="l"></path><path d="M289 336l2 2h1 1c0 2 0 4-1 5l1 1-1 2-1 2c0-2 0-3-1-5 0-1-1-3-1-4v-3z" class="M"></path><path d="M291 363l1-1v4l2 9h-1 0c-1-2-1-4-2-5s-1-1-2-1h-1c-1-1-1-1-2-1h0-1l-1-1-1-2h0c1-1 2-1 3-1 2 0 3-1 5-1z" class="t"></path><path d="M286 368c1-1 3-1 5-1h0c-1 1-2 1-2 2h0-1c-1-1-1-1-2-1h0z" class="E"></path><path d="M291 363l1-1v4c-1 0-3 1-5 0h-1l-1-1 1-1c2 0 3-1 5-1z" class="c"></path><path d="M291 363l1-1v4c-1 0-3 1-5 0 2-1 3-1 4-2v-1z" class="N"></path><path d="M289 352h1c1 3 1 7 2 10l-1 1c-2 0-3 1-5 1-1 0-2 0-3 1-1 0-1 0-1-1v-1h-1v-2c1 0 1 0 1 1h1v-1-1h-1c-2-2-4 0-7-1 0 0-1 0-1-1h-1l-1-1h2c2-1 5-1 7-1 3 0 5-1 7-3l1-1z" class="D"></path><path d="M286 358c1-1 3-2 4-2 1 1 1 2 0 3l-2 2c0-1-1-1-1-2l1-1h-1-1z" class="M"></path><path d="M274 357c2-1 5-1 7-1l-2 1c1 1 3 1 5 1h1 1 1 1l-1 1c0 1 1 1 1 2l-2 1c-1 0-2 1-4 1h-1v-2c1 0 1 0 1 1h1v-1-1h-1c-2-2-4 0-7-1 0 0-1 0-1-1h-1l-1-1h2z" class="J"></path><path d="M286 358h1 1l-1 1c0 1 1 1 1 2l-2 1c0-1-1-1-2-1h0l1-1h1v-1l-1-1h1z" class="F"></path><path d="M274 357c2-1 5-1 7-1l-2 1c1 1 3 1 5 1-4 1-7 0-10 0h-1l-1-1h2z" class="b"></path><path d="M300 339c0 1 1 2 1 2v2c1 1 1 1 1 2h0c0 1-1 2-1 3-1 2-1 5-1 7-1 3-2 5-5 7l-1-1c-1-2-1-5-2-7v-4c-1-1-1-1-1-2l1-2 1-2-1-1h1 2c1 1 1 0 1 1l2-2 2-1v-1-1z" class="J"></path><path d="M296 352h1 1v-3h1v3h0c-1 1-1 1-1 2-2 0-2-1-3-1l1-1z" class="g"></path><path d="M300 339c0 1 1 2 1 2v2c1 1 1 1 1 2h0-1v1l-1-1c-1 0-1 0-2 1l-2-2 2-2 2-1v-1-1z" class="B"></path><path d="M292 354c1-1 1-1 2-1 0-1 0-1 1 0 1 0 1 1 3 1-2 2-3 4-4 6v1c-1-2-1-5-2-7z" class="c"></path><path d="M293 344l1 1h1c0 2 1 5 1 7l-1 1c-1-1-1-1-1 0-1 0-1 0-2 1v-4c-1-1-1-1-1-2l1-2 1-2z" class="Z"></path><path d="M293 344l1 1v2l-1 1c-1-1-1-2-1-2l1-2z" class="g"></path><path d="M285 325v7c1 4 2 8 3 11l-1 1-6-2-10-6h0l-1-1h1l1 1h0 2l5-5c0-1 1-1 1-2 1-1 1-2 2-3 0-1 2-1 3-1z" class="B"></path><path d="M284 337h1v1l-1 1v2 1l-1-1c-1-1-1-2-3-2v-1c1 0 1 0 1-1l1 1 2-1z" class="b"></path><path d="M282 326l1 1c0 1-1 2-1 3l1-1h0v1c1 3 1 4 0 6l1 1-2 1-1-1c0 1 0 1-1 1h0c-2-1-3-1-4-1l-2-1 5-5c0-1 1-1 1-2 1-1 1-2 2-3z" class="f"></path><path d="M283 330c1 3 1 4 0 6l1 1-2 1-1-1c0-1 1-3 2-4v-3z" class="H"></path><path d="M279 331l1 1c-1 2-3 3-4 5l-2-1 5-5zm12-7h0v1h2c1 1 2 1 2 1 2 0 3 0 5 2h0l1 2c-1 0-1 0-1 1-1 3-1 5 0 8v1 1l-2 1-2 2c0-1 0 0-1-1h-2-1c1-1 1-3 1-5h-1-1l-2-2v-2c0-1 1-3 2-4v-2l-1-1 1-3z" class="AY"></path><path d="M293 338l1 1v2c-1-1-1-2-1-3z" class="g"></path><path d="M294 335l1 1-1 3-1-1h0-1l2-3z" class="J"></path><path d="M295 332v1c1 0 1-1 2-1h0l1 2c-1 1-1 2-2 2s-1-1-1-2v-2z" class="U"></path><path d="M293 334c1-1 1-2 1-3l1 1v2l-1 1-2 3h-1l-2-2v-2c1 1 2 1 3 2 1-1 1-1 1-2z" class="Y"></path><path d="M295 340c0-1 0-1 1-2h1s1 0 1 1 1 1 1 2c0-1 1-1 1-1v1l-2 1-2 2c0-1 0 0-1-1v-3z" class="R"></path><path d="M295 340c1 1 2 1 3 2h0l-2 2c0-1 0 0-1-1v-3z" class="F"></path><path d="M291 324h0v1h2c1 1 2 1 2 1 2 0 3 0 5 2h0v1c-2 0-3-2-4-2l-1 2c1 0 1 0 1 1 1 0 2-1 3 0 0 1 0 2-1 2h-1 0c-1 0-1 1-2 1v-1l-1-1c0 1 0 2-1 3 0 1 0 1-1 2-1-1-2-1-3-2 0-1 1-3 2-4v-2l-1-1 1-3z" class="J"></path><path d="M291 330c1 0 1 1 2 2h-1v1l1 1c0 1 0 1-1 2-1-1-2-1-3-2 0-1 1-3 2-4z" class="AG"></path><path d="M293 325c1 1 2 1 2 1 2 0 3 0 5 2h0v1c-2 0-3-2-4-2-1 2-1 2-1 3h-1c-1-1-1-1-1-2v-1c-1-1 0-1 0-2z" class="s"></path><path d="M282 360h1v1 1h-1c0-1 0-1-1-1v2h1v1c0 1 0 1 1 1h0l1 2 1 1h1 0c1 0 1 0 2 1h1c1 0 1 0 2 1s1 3 2 5v2l1 7c1 1 1 2 1 3v4c-1-2-2-3-3-5-1-3-3-6-5-8-1-2-2-3-3-4h-3v-1-2c-1-1-2-2-3-2-1-1-2-1-3 0l-1 1v2h0v-1h-1l-1 1c0-1-1-2-2-3h0c-1-1-2-2-2-3 1-1 2-1 2-3v-1-2c2 1 4 0 6 0h6z" class="F"></path><path d="M284 374c1 0 2-1 3 0l1 1v1c-1 0-1 1-1 2-1-2-2-3-3-4z" class="R"></path><path d="M294 384h0c-1-1-4-7-4-8s1-1 1-1h1l1 2 1 7z" class="i"></path><g class="U"><path d="M284 367l1 1h1 0c1 0 1 0 2 1h1c1 0 1 0 2 1h-1v1h0c-1 0-4 1-4 0-1 0-2-2-3-3h0l1-1z"></path><path d="M282 360h1v1 1h-1c0-1 0-1-1-1v2h1v1c0 1 0 1 1 1h0l1 2-1 1-3-2c0 1 0 1-1 2 0-1-1-1-1-2-2 1-4 2-5 3 0 0-1 0-1 1h0 2v2h0v-1h-1l-1 1c0-1-1-2-2-3h0c-1-1-2-2-2-3 1-1 2-1 2-3v-1-2c2 1 4 0 6 0h6z"></path></g><path d="M270 369v-1l2-2s1 1 0 2c0 0-1 1-2 1h0z" class="s"></path><path d="M270 363l1 2 1 1-2 2v1c-1-1-2-2-2-3 1-1 2-1 2-3z" class="g"></path><path d="M270 362c3 1 8 2 11 1h1v1c0 1 0 1 1 1h0l1 2-1 1-3-2s-1 0-1-1h-1c-1-1-5 0-7 0l-1-2v-1z" class="O"></path><path d="M279 365h4l1 2-1 1-3-2s-1 0-1-1z" class="c"></path><path d="M282 360h1v1 1h-1c0-1 0-1-1-1v2c-3 1-8 0-11-1v-2c2 1 4 0 6 0h6z" class="AC"></path><path d="M291 428v-6c1 2 1 4 1 5 1 15 1 31-6 44v1c1 0 1 1 1 1 0 5-4 10-8 12v2h-2-1 0c0 1 0 1-1 2h0l1 1h-2-1-5c1 1 1 1 2 1 2 0 3 0 4 1l-1 1c1 1 2 1 3 2-1 1-1 1-2 0h-4l-2 2c-1 0-1-1-2-2v-1h-2c-1-1-3-2-5-2v-1c-1 0-2-1-3-3v-1h0l2 1 2-1-2-1c-2-2-4-5-5-7l1-2-1-9c1-2 1-3 2-5h0c1-2 2-4 3-5 1-2 4-3 5-5 2 0 3 0 4-1 2-1 5 1 7 0l-1-1-1-1-1-1h2v-1l-1-1c1-1 2-1 3-2l-1-1h2v-1h-2c1-1 2-1 2-2l4-2c3-2 5-5 7-8v2c1 1 1 1 1 2 1-2 2-5 3-7z" class="AI"></path><path d="M257 469v-4c0 2 0 3 1 5s3 5 3 7c1 2 2 3 4 4h1l1 1h6 1 0c1-1 3-2 4-2l1-1h0c2-1 3-2 4-3-3 4-6 6-11 7-4 0-7-1-10-3l-2-2-1-1c-1-3-1-5-2-8z" class="C"></path><path d="M285 459c0-1 1-5 2-6v1-1 2 4c0 3-1 8-3 10h0c-1 0-1 0-2 1-1 0-1-1-2-1h-3c0-1 0-1 1-2h3 1c2-1 3-4 3-6h0v-2z" class="D"></path><path d="M277 469h3c1 0 1 1 2 1 1-1 1-1 2-1h0c-1 4-4 8-7 9l-2 1c0-1 0-1-1-2 0-2 1-2 2-3l-1-1h-1 0l3-4z" class="L"></path><path d="M285 459v2h0c0 2-1 5-3 6h-1-3c-1 1-1 1-1 2l-3 4h0 1l1 1c-1 1-2 1-2 3 1 1 1 1 1 2-1 1-3 1-4 1h-4c-2-1-4-3-4-4l-1-1c-1-3-1-7 0-10 1-2 2-4 4-5s4-1 6-1c1 1 2 2 3 4l1-3h1v2c2 0 3 0 5 1 1-2 2-3 3-4z" class="a"></path><path d="M269 479c2-2 3-3 4-5 0-1 1-1 1-1h0 1l1 1c-1 1-2 1-2 3v1h-1v-1c-2 1-3 2-4 2z" class="D"></path><path d="M263 476h1v1l2 2c1 0 1 0 2 1 0-1 1-1 1-1 1 0 2-1 4-2v1h1v-1c1 1 1 1 1 2-1 1-3 1-4 1h-4c-2-1-4-3-4-4z" class="B"></path><path d="M262 465c1 0 0 0 1 1h0c1-1 1-2 2-2 1-2 1-2 2-2-1 1-2 2-2 4v2h-1c-1 1 0 3 0 4 1 2 1 2 1 3l-1 1h-1l-1-1c-1-3-1-7 0-10z" class="O"></path><path d="M262 465c1-2 2-4 4-5s4-1 6-1c1 1 2 2 3 4l1-3h1v2h0c-2 3-2 6-5 8h0l-1 1c-2 0-3 0-4-1-1 0-2-1-2-2v-2c0-2 1-3 2-4-1 0-1 0-2 2-1 0-1 1-2 2h0c-1-1 0-1-1-1z" class="Z"></path><path d="M267 470h1c1-1 2-1 2-1l2 1h0l-1 1c-2 0-3 0-4-1z" class="U"></path><path d="M262 465c1-2 2-4 4-5s4-1 6-1c1 1 2 2 3 4-2 1-3 2-4 4h-1c-1-1-1-1-1-3l1-1v-1h-3c-1 0-1 0-2 2-1 0-1 1-2 2h0c-1-1 0-1-1-1z" class="E"></path><path d="M287 431v2c1 1 1 1 1 2h0c1 2 0 4 0 6 0 3 0 7-1 9v3c-1 1-2 5-2 6-1 1-2 2-3 4-2-1-3-1-5-1v-2h-1l-1-3c-2-2-4-2-7-3h3 2l-1-1c-2 0-3 0-5-1h0c2-1 5 1 7 0l-1-1-1-1-1-1h2v-1l-1-1c1-1 2-1 3-2l-1-1h2v-1h-2c1-1 2-1 2-2l4-2c3-2 5-5 7-8z" class="Z"></path><path d="M282 446c1-1 3-3 5-4l-2 5h0 0c-3 1-3 3-4 5h-1 0c1-2 1-3 2-5v-1z" class="G"></path><path d="M275 455l-1-2v-1l1 1c2-1 5-1 7 0-1 2-2 3-4 3-1 0-2 0-3-1z" class="J"></path><path d="M288 435c1 2 0 4 0 6 0 3 0 7-1 9v-3h-2l2-5c-2 1-4 3-5 4l-4 4c-2 0-3 1-3 2h-1l-1-1-1-1-1-1h2v-1h2c6-2 10-8 13-13z" class="H"></path><path d="M273 449h2v1l-2 1-1-1-1-1h2z" class="V"></path><path d="M287 431v2c1 1 1 1 1 2h0c-3 5-7 11-13 13h-2l-1-1c1-1 2-1 3-2l-1-1h2v-1h-2c1-1 2-1 2-2l4-2c3-2 5-5 7-8z" class="e"></path><path d="M287 431v2c-1 5-7 10-12 12h0l-1-1h2v-1h-2c1-1 2-1 2-2l4-2c3-2 5-5 7-8z" class="E"></path><defs><linearGradient id="AL" x1="285.082" y1="447.078" x2="279.359" y2="461.938" xlink:href="#B"><stop offset="0" stop-color="#414443"></stop><stop offset="1" stop-color="#5a5b5b"></stop></linearGradient></defs><path fill="url(#AL)" d="M285 447h2v3 3c-1 1-2 5-2 6-1 1-2 2-3 4-2-1-3-1-5-1v-2h-1l-1-3h1l-1-2c1 1 2 1 3 1 2 0 3-1 4-3l1-1c1-2 1-3 2-5h0z"></path><path d="M282 456h1c0 2 0 2-1 3l-1 1h-2 0v-1c2-1 2-2 3-3z" class="E"></path><path d="M283 452h1c0 1-1 3-2 4s-1 2-3 3c-2 0-2-1-3-2l-1-2c1 1 2 1 3 1 2 0 3-1 4-3l1-1z" class="N"></path><path d="M263 453c2 0 3 0 4-1h0c2 1 3 1 5 1l1 1h-2-3c-5 2-8 4-10 9 0 1-1 1-1 2v4c1 3 1 5 2 8l1 1 2 2c3 2 6 3 10 3 5-1 8-3 11-7l3-5v1c1 0 1 1 1 1 0 5-4 10-8 12v2h-2-1 0c0 1 0 1-1 2h0l1 1h-2-1-5c1 1 1 1 2 1 2 0 3 0 4 1l-1 1c1 1 2 1 3 2-1 1-1 1-2 0h-4l-2 2c-1 0-1-1-2-2v-1h-2c-1-1-3-2-5-2v-1c-1 0-2-1-3-3v-1h0l2 1 2-1-2-1c-2-2-4-5-5-7l1-2-1-9c1-2 1-3 2-5h0c1-2 2-4 3-5 1-2 4-3 5-5z" class="M"></path><path d="M259 477l1 1v3c-1-1-1-2-2-3l1-1z" class="Z"></path><path d="M274 490c-1 0-2-1-3-1 2-1 3-2 5-2h0c0 1 0 1-1 2h0l1 1h-2z" class="e"></path><path d="M266 484c2 1 3 1 5 1 1 0 3-1 5-1 1-1 2-1 3-1v1c-3 2-8 3-12 2h-1c-1 0-2 0-3-1h4c1 0 2 1 3 1l-2-1c-1 0-1 0-2-1h0z" class="g"></path><defs><linearGradient id="AM" x1="267.022" y1="487.109" x2="261.616" y2="493.16" xlink:href="#B"><stop offset="0" stop-color="#7a7b7a"></stop><stop offset="1" stop-color="#939393"></stop></linearGradient></defs><path fill="url(#AM)" d="M259 492v-1c-1 0-2-1-3-3v-1h0l2 1 2-1c1 0 2 1 4 1v-1c2 0 3 0 5 1v2h4-5c1 1 1 1 2 1 2 0 3 0 4 1l-1 1c1 1 2 1 3 2-1 1-1 1-2 0h-4l-2 2c-1 0-1-1-2-2v-1h-2c-1-1-3-2-5-2z"></path><path d="M260 487c1 0 2 1 4 1v1h-3c-1 0-2-1-3-1l2-1z" class="Y"></path><path d="M266 495l2-1c2-1 3-1 5-1 1 1 2 1 3 2-1 1-1 1-2 0h-4l-2 2c-1 0-1-1-2-2z" class="L"></path><path d="M264 487c2 0 3 0 5 1v2h4-5c1 1 1 1 2 1-3 1-6 0-9-2h3v-1-1z" class="E"></path><path d="M263 453c2 0 3 0 4-1h0c2 1 3 1 5 1l1 1h-2-3c-5 2-8 4-10 9 0 1-1 1-1 2v4c1 3 1 5 2 8l-1 1c1 1 1 2 2 3s2 2 4 2c0 1 2 1 2 1h0c1 1 1 1 2 1l2 1c-1 0-2-1-3-1h-4c1 1 2 1 3 1h1c-1 1-2 1-4 1h1v1c-2 0-3-1-4-1l-2-1c-2-2-4-5-5-7l1-2-1-9c1-2 1-3 2-5h0c1-2 2-4 3-5 1-2 4-3 5-5z" class="J"></path><path d="M257 469c1 3 1 5 2 8l-1 1-1-2c-1-1-1-1-1-2 1-1 1-1 1-2v-3z" class="F"></path><path d="M254 477v1c3 2 4 5 8 6 0 1 1 1 1 1 1 1 2 1 3 1h1c-1 1-2 1-4 1h1v1c-2 0-3-1-4-1l-2-1c-2-2-4-5-5-7l1-2z" class="AY"></path><path d="M254 477v1c2 4 4 7 8 8l1 1h1v1c-2 0-3-1-4-1l-2-1c-2-2-4-5-5-7l1-2z" class="K"></path><path d="M258 289h1l2 2h0l-2 1c0 2 0 2 1 3l2 2c2 1 10 8 10 9 3 3 5 6 8 9 1 2 2 4 3 5l2 5c-1 0-3 0-3 1-1 1-1 2-2 3 0 1-1 1-1 2l-5 5h-2v-1h-1c-1-1-2-1-3-2h-1c0-1 0-1-1-1l1-2h-1-1c-1 1-1 0-2 0l-3-2h-1v-1c-1 0-1 1-2 1l-1-2h0 0l-2-1-1-1s-1 1-2 1h-1c0 1 0 1 1 2-1-1-1-1-2-1v1h-3c-3-2-6-4-8-6h0c-2-2-4-3-5-6h0l-2-4v-1c-1-1-1-2-2-3-1-2-2-3-3-5 1-2 0-3 2-5h-1v-1c2-1 2 0 3 0l1-2h2l1-1h4 1 4 2c1 1 2 1 3 1s3 0 4-1h1c2-1 2-2 3-3l2-1z" class="a"></path><path d="M257 311v-2c0-1 0-2 1-3h0l1 3-2 2z" class="B"></path><path d="M259 309l1 1-2 3c0-1-1-2-1-2l2-2z" class="V"></path><path d="M248 297l1 1c1 2 3 4 3 6l-1 1h-1v-3c-1-1 0-1-1-1 0-1-1-2-1-3h-1l1-1z" class="b"></path><path d="M264 305l1-1c1 1 2 3 4 4-1 1-3 3-3 5l-1-1c-1 0-2 0-3 1v1l-2-1c0 1-2 2-2 3-1 2-3 3-4 5l-2 1c-1 0-1-1-2-2 3-2 5-5 8-7l2-3 3-4 1-1z" class="Y"></path><path d="M264 305l1-1c1 1 2 3 4 4-1 1-3 3-3 5l-1-1c-1 0-2 0-3 1v1l-2-1 3-3 1-3c1-1 1-1 0-2z" class="u"></path><defs><linearGradient id="AN" x1="238.348" y1="308.973" x2="243.598" y2="327.635" xlink:href="#B"><stop offset="0" stop-color="#676967"></stop><stop offset="1" stop-color="#7c7c7d"></stop></linearGradient></defs><path fill="url(#AN)" d="M234 311v-1c1-1 1-1 2-1 1 5 2 7 6 10h0c2 1 4 2 6 2 0 1 1 1 1 2s1 1 1 1v1c0 1 0 1 1 2-1-1-1-1-2-1v1h-3c-3-2-6-4-8-6l-4-10z"></path><path d="M242 319c2 1 4 2 6 2 0 1 1 1 1 2s1 1 1 1v1c0 1 0 1 1 2-1-1-1-1-2-1-1-1-2-2-3-2-1-1-1-1-2-1-1-1-2-2-2-3v-1z" class="g"></path><path d="M266 313c1 0 2-1 2-2l1-1c0-1 0-1 1-1v1c0 1-1 2-1 3v1s-1 2-2 2c-2 4-5 8-8 11-1 0-1 1-2 1l-1-2h0 0l-2-1-1-1-1-2 2-1c1-2 3-3 4-5 0-1 2-2 2-3l2 1v-1c1-1 2-1 3-1l1 1z" class="G"></path><path d="M258 316c0-1 2-2 2-3l2 1v1s-1 1-2 1h-2z" class="f"></path><path d="M254 321c1 0 1 0 1 1v1l-1 2-1-1-1-2 2-1z" class="O"></path><path d="M259 319c1 0 1 1 1 2-1 1-2 1-2 2l-1-1c1-1 1-2 2-3z" class="K"></path><path d="M259 319l4-4c2 0 2 0 3 2-1 1-2 1-3 2-1 0-2 1-2 2h-1c0-1 0-2-1-2z" class="I"></path><path d="M266 317v-1h1c-2 4-5 8-8 11-1 0-1 1-2 1l-1-2h0 0 0c0-1 1-2 2-3 0-1 1-1 2-2h1c0-1 1-2 2-2 1-1 2-1 3-2z" class="N"></path><path d="M256 326h3v1c-1 0-1 1-2 1l-1-2z" class="D"></path><path d="M245 296h1c0 1 0 1 2 1l-1 1h1c0 1 1 2 1 3 1 0 0 0 1 1v3h1l-1 2c0 1-1 2-2 3-2 1-4 1-6 0-1-1-2-2-3-4v-2-1h1l1-1c-1-1-3-1-3-2s2-1 3-2l-1-1c1 0 2 0 3-1h2z" class="L"></path><path d="M239 303h1c1 0 1 1 2 1h1l-1 1c-1 0-1 1-2 1-1-1-1-1-1-2v-1zm8 1l2 1c-1 1-1 2-2 3s-2 1-3 1c-1-1-2-1-2-3h1s1 1 2 1 2-1 2-3z" class="u"></path><path d="M243 304c0-1 1-1 2-1l2 1c0 2-1 3-2 3s-2-1-2-1l-1-1 1-1z" class="V"></path><path d="M241 302c1-2 3-3 5-3l3 3v3l-2-1-2-1c-1 0-2 0-2 1h-1c-1 0-1-1-2-1l1-1z" class="t"></path><path d="M245 296h1c0 1 0 1 2 1l-1 1h1c0 1 1 2 1 3 1 0 0 0 1 1h-1l-3-3c-2 0-4 1-5 3-1-1-3-1-3-2s2-1 3-2l-1-1c1 0 2 0 3-1h2z" class="C"></path><path d="M258 289h1l2 2h0l-2 1c0 2 0 2 1 3l2 2c2 1 10 8 10 9v1s0 1 1 2v1h-1c0-1 0-1-1-1l-2-1c-2-1-3-3-4-4l-1 1-1 1h-3c-1 0-2-1-2-2-1-1-3-1-3-2l-2-3c-1 0-2-1-4-1l-1-1c-2 0-2 0-2-1h-1v-1h-3l1-2h2c1 1 2 1 3 1s3 0 4-1h1c2-1 2-2 3-3l2-1z" class="y"></path><path d="M252 293h1l2 1c-3 1-7 1-9 2h-1v-1h-3l1-2h2c1 1 2 1 3 1s3 0 4-1z" class="H"></path><path d="M258 289h1l2 2h0l-2 1c0 2 0 2 1 3l-5-1-2-1c2-1 2-2 3-3l2-1z" class="h"></path><path d="M238 293h1 4l-1 2h3v1h-2c-1 1-2 1-3 1l1 1c-1 1-3 1-3 2s2 1 3 2l-1 1h-1-1l-1 1-1 2h0v3c-1 0-1 0-2 1v1l4 10h0c-2-2-4-3-5-6h0l-2-4v-1c-1-1-1-2-2-3-1-2-2-3-3-5 1-2 0-3 2-5h-1v-1c2-1 2 0 3 0l1-2h2l1-1h4z" class="O"></path><path d="M237 304v-2h-1 0c0-1 1-2 2-2 0 1 2 1 3 2l-1 1h-1-1l-1 1z" class="D"></path><path d="M234 311l-1-4 2-2h1v1 3c-1 0-1 0-2 1v1z" class="c"></path><path d="M242 295h3v1h-2l-4 1c-1 0-3 1-4 3-1 1-2 3-2 4v1c-1 3-1 6 1 10h0-1 0 0l-2-4v-8h1v-3c1 0 1-1 2-1 0-1 2-3 3-4h5z" class="I"></path><path d="M238 293h1 4l-1 2h-5c-1 1-3 3-3 4-1 0-1 1-2 1v3h-1v8-1c-1-1-1-2-2-3-1-2-2-3-3-5 1-2 0-3 2-5h-1v-1c2-1 2 0 3 0l1-2h2l1-1h4z" class="G"></path><path d="M228 297l1 1v1c-1 1-1 1-1 2 1 2 1 3 1 5l1 2v-1c1 1 1 2 1 3-1-1-1-2-2-3-1-2-2-3-3-5 1-2 0-3 2-5z" class="O"></path><path d="M233 294l1-1h4l-1 1-1 1c-2 0-5 3-6 5v2h0l-2-1c0-1 0-1 1-2v-1l-1-1h-1v-1c2-1 2 0 3 0l1-2h2z" class="c"></path><defs><linearGradient id="AO" x1="280.014" y1="316.619" x2="266.828" y2="327.713" xlink:href="#B"><stop offset="0" stop-color="#181918"></stop><stop offset="1" stop-color="#494b4c"></stop></linearGradient></defs><path fill="url(#AO)" d="M272 306c3 3 5 6 8 9 1 2 2 4 3 5l2 5c-1 0-3 0-3 1-1 1-1 2-2 3 0 1-1 1-1 2l-5 5h-2v-1h-1c-1-1-2-1-3-2h-1c0-1 0-1-1-1l1-2h-1-1c-1 1-1 0-2 0l-3-2h-1v-1c3-3 6-7 8-11 1 0 2-2 2-2v-1c0-1 1-2 1-3v-1c-1 0-1 0-1 1l-1 1c0 1-1 2-2 2 0-2 2-4 3-5l2 1c1 0 1 0 1 1h1v-1c-1-1-1-2-1-2v-1z"></path><path d="M273 331h2l-3 4h-1c-1-1-2-1-3-2l3-2h0c1 1 1 0 1 1l1-1z" class="J"></path><path d="M271 331h0c1 1 1 0 1 1l-2 2 1 1c-1-1-2-1-3-2l3-2z" class="G"></path><path d="M273 331l7-8h0c-1 3-2 6-5 8h-2z" class="g"></path><path d="M279 320l1 1s0 1 1 1l-1 1h0l-7 8-1 1c0-1 0 0-1-1h0c2-3 6-7 8-11h0z" class="I"></path><path d="M276 316c1 1 3 3 3 4h0c-2 4-6 8-8 11l-3 2h-1c0-1 0-1-1-1l1-2h-1c3-5 7-9 10-14h0z" class="f"></path><path d="M267 333v-1c1-1 9-11 11-12h1c-2 4-6 8-8 11l-3 2h-1z" class="g"></path><path d="M269 314c1-1 2-3 3-4l1 2 3 4h0c-3 5-7 9-10 14h-1c-1 1-1 0-2 0l-3-2h-1v-1c3-3 6-7 8-11 1 0 2-2 2-2z" class="F"></path><path d="M273 312l3 4h0c-1 0-2-1-2-1-3 3-5 6-8 9-1 2-3 4-3 6l-3-2c1 0 2-2 3-3 4-4 7-8 10-13z" class="Y"></path><path d="M263 330c0-2 2-4 3-6 3-3 5-6 8-9 0 0 1 1 2 1-3 5-7 9-10 14h-1c-1 1-1 0-2 0z" class="J"></path><path d="M256 390l8-2h4c1 0 2 1 2 1h3l1 1 2-1h4l1 1h0c1 2 2 3 3 5l-1 1c1 1 4 2 4 3l3 5v1l-2 1v-1l-4-4c0 3 3 6 4 10v2c2 3 2 8 1 12h1l1 3c-1 2-2 5-3 7 0-1 0-1-1-2v-2c-2 3-4 6-7 8l-4 2c-1 0-3 0-4 1-1 0-2 1-3 1s-2 0-3 1h-6v1h-3l2 2c-1 0-2 0-3 1l-1-1-2-1h-1-1c-1-1-2-3-2-4l-1-1v1s0 1-1 1h0v1h-1v-2c0-2 0-4-1-6h-1c0 1-1 3-2 4h0-1c0-1 0-1-1-1-2-1-4-4-4-6-1 1-1 1-1 2l1 1v1c-1 0-2 0-2-1h-1-1l-1 1v-2c-1 1-2 1-2 1h-4c-5 7-8 16-9 25h-1v-1c0-5 1-11 2-16 2-9 7-18 11-27l-4 3 2-3-1-1c1-2 2-5 4-7 0-1 0-2 1-2l1-2c0-2 4-5 5-6l3-3c2 0 4-2 7-2 3-2 6-3 10-4z" class="y"></path><path d="M233 407l5-3-5 7h-3c0-1 1-2 2-3l1-1z" class="X"></path><path d="M235 420c0-2 2-2 2-4h0c2-3 4-5 8-7l-2 4-2 1c-1 1-3 3-4 5l-2 1z" class="C"></path><path d="M250 401l4-1 2 1h0-1v1c-6 2-12 4-17 9l-1-1c4-4 9-6 13-9z" class="L"></path><path d="M235 420l2-1c-1 3-3 6-4 9-2 1-2 2-1 4-1 0-3 1-4 1h-1c2-3 3-5 4-8 1-2 3-3 4-5z" class="H"></path><path d="M237 419c1-2 3-4 4-5h1c0 1 0 1 1 1l-1 1 1 1c-2 1-3 3-4 4-2 1-3 2-4 4 0 1-1 2-1 3v2h-1v-2c1-3 3-6 4-9z" class="Y"></path><path d="M231 407v1h1 0c-1 1-2 2-2 3h3l-3 3-2 3-4 3 2-3-1-1c1-2 2-5 4-7l2-2z" class="AG"></path><path d="M230 411h3l-3 3c-1 0-1 0-2 1-1 0-1 1-2 1 1-2 3-3 4-5z" class="F"></path><path d="M226 416c1 0 1-1 2-1 1-1 1-1 2-1l-2 3-4 3 2-3v-1z" class="g"></path><path d="M248 408c4-2 8-3 13-4 3 0 5 0 8 1l-1 1-3-1v1h-1 0c-1 0-1 0-3 1h-3c-4 1-8 2-11 3l1-2z" class="H"></path><path d="M264 406h0 1v-1l3 1 1-1c3 1 5 3 7 5v2c-1 0-1-1-2 0l3 3h-2c-1-1-3-2-4-3-2-2-3-2-5-3-3-2-5-2-8-2h3c2-1 2-1 3-1z" class="B"></path><path d="M258 407h3c2-1 2-1 3-1 4 1 7 4 10 6l3 3h-2c-1-1-3-2-4-3-2-2-3-2-5-3-3-2-5-2-8-2zm-25 21v2h1v-2c0-1 1-2 1-3 1-2 2-3 4-4-2 4-3 8-3 12-1 1-1 1-1 2l1 1v1c-1 0-2 0-2-1h-1-1l-1 1v-2c-1 1-2 1-2 1h-4l1-2 1-1h1c1 0 3-1 4-1-1-2-1-3 1-4z" class="G"></path><path d="M227 433h1c1 0 3-1 4-1v3h-2 1c-1 1-2 1-2 1h-4l1-2 1-1z" class="C"></path><path d="M226 434c2 0 3 0 4 1h1c-1 1-2 1-2 1h-4l1-2z" class="M"></path><path d="M278 408v1l1-1 1 1s1 0 2 1h1v3c-1 0-2 1-2 1 2 0 2 0 3 1l1-2v4l-2 3h-2v-1c-2-1-3-2-4-4l-3-3c1-1 1 0 2 0v-2h1v2h1v-2c-1-1 0-1 0-2z" class="C"></path><path d="M284 415l1-2v4l-2 3h-2v-1c1-1 2-2 3-4z" class="K"></path><path d="M278 409l1-1 1 1s1 0 2 1h1v3c-1 0-2 1-2 1-1 0-1 0-2-1 0-2 0-3-1-4z" class="R"></path><defs><linearGradient id="AP" x1="260.515" y1="404.756" x2="268.303" y2="397.549" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#4e5252"></stop></linearGradient></defs><path fill="url(#AP)" d="M254 400c7-1 15-2 22 2h1l3 3 2 3c0 1 0 2 1 2h-1c-1-1-2-1-2-1l-1-1-1 1v-1c0-1-1-2-2-3 0 0-1 0-1-1-6-3-12-4-20-2v-1h1 0l-2-1z"></path><path d="M276 405h1l2 1c1 0 2 1 3 2 0 1 0 2 1 2h-1c-1-1-2-1-2-1l-1-1-1 1v-1c0-1-1-2-2-3z" class="Z"></path><defs><linearGradient id="AQ" x1="253.232" y1="411.98" x2="252.496" y2="408.73" xlink:href="#B"><stop offset="0" stop-color="#7c7d7d"></stop><stop offset="1" stop-color="#959595"></stop></linearGradient></defs><path fill="url(#AQ)" d="M258 407c3 0 5 0 8 2h0-3s-1 1-2 1h1l-1 2c-3-2-6-2-9-1-4 1-6 3-9 6l-1-1 1-1c-1 0-1 0-1-1h-1l2-1 2-4c1 0 1-1 3-1l-1 2c3-1 7-2 11-3z"></path><path d="M245 409c1 0 1-1 3-1l-1 2-4 3 2-4z" class="V"></path><path d="M261 412l1-2h-1c1 0 2-1 2-1h3 0c2 1 3 1 5 3 1 1 3 2 4 3h2c1 2 2 3 4 4v1h2c-1 1-2 2-3 2s-3-1-4-1v-1l-6-6h0-2c-2-1-4-1-5-2l-1 1-1-1z" class="E"></path><path d="M275 415h2c1 2 2 3 4 4v1h-1 0-1c-1 0-4-3-4-3v-2z" class="I"></path><path d="M261 412l1-2h-1c1 0 2-1 2-1h3 0c2 1 3 1 5 3-2 0-4-1-5-2v1c2 1 3 2 4 3h0-2c-2-1-4-1-5-2l-1 1-1-1z" class="g"></path><path d="M277 396h1l6 5c0 3 3 6 4 10v2h0c-1-1-1-2-1-3h0v2 6h0l-1 1v-2h0-1v-4l-1 2c-1-1-1-1-3-1 0 0 1-1 2-1v-3c-1 0-1-1-1-2l-2-3-3-3h-1c-7-4-15-3-22-2l-4 1 3-2c6-3 17-3 23-1h1 1c0 1 1 1 2 1h0c-1-1-2-1-2-2l-1-1z" class="D"></path><path d="M285 408c1 2 1 3 1 5 1 2 1 3 1 5l-1 1v-2h0-1v-4c0-2-1-3 0-5z" class="E"></path><path d="M280 405c2 1 2 2 4 2l1 1c-1 2 0 3 0 5l-1 2c-1-1-1-1-3-1 0 0 1-1 2-1v-3c-1 0-1-1-1-2l-2-3z" class="V"></path><path d="M256 390l8-2h4c1 0 2 1 2 1h3l1 1 2-1h4l1 1h0c1 2 2 3 3 5l-1 1c1 1 4 2 4 3l3 5v1l-2 1v-1l-4-4-6-5h-1l-1-1c-6-3-13-4-20-2-6 3-12 6-17 10 0 1 0 1-1 1l-5 3-1 1h0-1v-1l-2 2c0-1 0-2 1-2l1-2c0-2 4-5 5-6l3-3c2 0 4-2 7-2 3-2 6-3 10-4z" class="AP"></path><path d="M239 396c2 0 4-2 7-2l-7 5h-3l3-3z" class="J"></path><path d="M236 399h3c-3 2-5 5-8 8l-2 2c0-1 0-2 1-2l1-2c0-2 4-5 5-6z" class="s"></path><path d="M276 389h4l1 1h0c1 2 2 3 3 5l-1 1-3-3-6-3 2-1z" class="R"></path><path d="M278 396c3 0 4 1 6 3 1 0 2 1 3 2h0l-1-1 1-1 3 5v1l-2 1v-1l-4-4-6-5z" class="AG"></path><path d="M284 399c1 0 2 1 3 2h0l-1-1 1-1 3 5v1l-2 1v-1c0-3-2-4-4-6z" class="o"></path><path d="M233 407c2-2 4-5 6-6l8-5c2-1 5-3 7-3h2c-6 3-12 6-17 10 0 1 0 1-1 1l-5 3z" class="AC"></path><path d="M287 418h0v-6-2h0c0 1 0 2 1 3h0c2 3 2 8 1 12h1l1 3c-1 2-2 5-3 7 0-1 0-1-1-2v-2c-2 3-4 6-7 8l-4 2c-1 0-3 0-4 1-1 0-2 1-3 1s-2 0-3 1h-6v1h-3l2 2c-1 0-2 0-3 1l-1-1-2-1h-1-1c-1-1-2-3-2-4l-1-1v1s0 1-1 1h0v1h-1v-2c0-2 0-4-1-6h-1c0 1-1 3-2 4h0-1c0-1 0-1-1-1-2-1-4-4-4-6 0-4 1-8 3-12 1-1 2-3 4-4 3-3 5-5 9-6 3-1 6-1 9 1l1 1 1-1c1 1 3 1 5 2h2 0l6 6v1c1 0 3 1 4 1s2-1 3-2l2-3h1 0v2l1-1z" class="AI"></path><path d="M264 422c2 0 3-1 4 0s1 2 2 3l-1 1c-1 0-1-1-2-2 0 1-1 2-1 3v-2-3h-1-1z" class="V"></path><path d="M289 425h1l1 3c-1 2-2 5-3 7 0-1 0-1-1-2v-2c1-2 2-4 2-6z" class="AY"></path><path d="M274 427l2 2c0 1 0 1 1 1v1h3c1 0 3-2 4-3h1c-1 3-2 4-4 5h-3c-3-2-3-3-4-6z" class="f"></path><path d="M266 427c0-1 1-2 1-3 1 1 1 2 2 2l1-1c0 1 0 2 1 3v3c1 2 2 3 4 4l-2-1c-1 1-1 2-1 3l-1-1c-2-1-4-6-5-9h0z" class="C"></path><path d="M271 431h-2c0-1 0-1 1-2l1-1v3z" class="D"></path><path d="M262 413l1-1c1 1 3 1 5 2h2 0l6 6h-2c0 1 0 1-1 1h0c-1-1-3-4-5-5-3-1-6 1-8 2 1-2 2-3 2-4v-1z" class="X"></path><path d="M257 423c0-1 0-2 1-2h1c1 0 2 1 4 0 1 0 1 0 1 1h1 1v3 2h0 0c0 1 0 1-1 2h0c-1-1-1-2-1-2l-1-1c0 1-1 1-1 1-1 1-2 0-2 1v1c-1-1-1-1 0-2v-2-1h-1c-1 0-1-1-2-1z" class="D"></path><path d="M264 424v-1h0l2-1v3 2h0 0c0 1 0 1-1 2h0c-1-1-1-2-1-2l1-2-1-1z" class="B"></path><path d="M265 425c0 1 1 1 1 2s0 1-1 2h0c-1-1-1-2-1-2l1-2z" class="O"></path><path d="M260 424h1l1 1s1-1 2-1l1 1-1 2-1-1c0 1-1 1-1 1-1 1-2 0-2 1v1c-1-1-1-1 0-2v-2-1z" class="G"></path><path d="M273 421h0c1 2 2 3 4 4v1c1 0 3 0 4-1 2 0 4-2 5-4-1 1-1 2-1 2 0 1-3 3-4 3 0 1-1 1-2 0 1 1 1 1 1 2v3h1c1-2 3-3 4-5 0-1 0-2 2-3v1c0 2-1 3-2 4h0-1c-1 1-3 3-4 3h-3v-1c-1 0-1 0-1-1l-2-2c0-2-1-4-1-6z" class="D"></path><path d="M273 421h0c1 2 2 3 4 4v1 1c1 1 1 1 1 2l-1 1c-1 0-1 0-1-1l-2-2c0-2-1-4-1-6z" class="C"></path><path d="M285 417h1 0v2 2c-1 2-3 4-5 4-1 1-3 1-4 1v-1c-2-1-3-2-4-4 1 0 1 0 1-1h2v1c1 0 3 1 4 1s2-1 3-2l2-3z" class="e"></path><path d="M273 421c1 0 1 0 1-1h2v1h0c2 1 3 2 5 2v1c-1 0-1 1-2 1h-2c-2-1-3-2-4-4z" class="AC"></path><path d="M260 429v-1c0-1 1 0 2-1 0 0 1 0 1-1l1 1s0 1 1 2h0c1-1 1-1 1-2h0c1 3 3 8 5 9l1 1c0-1 0-2 1-3l2 1c1 2 2 2 4 4h1l-4 2c-1 0-3 0-4 1-1 0-2 1-3 1s-2 0-3 1c-1-1-1-2-2-3l-1-1-1-1c0 1-1 1-1 1l-2-2c0-1 0-2 1-4-1 0-1-1 0-1v-4z" class="H"></path><path d="M267 435l3 3h-4c0-1 1-2 1-3z" class="u"></path><path d="M268 441h3l1 1c-1 0-2 1-3 1s-2 0-3 1c-1-1-1-2-2-3h4z" class="B"></path><path d="M266 438h4l1 1v1c-1 1-2 1-3 1h-4l-1-1c0-1 0 0 1-1v1h1l1-1v-1z" class="G"></path><path d="M272 437c0-1 0-2 1-3l2 1c1 2 2 2 4 4h1l-4 2c-1 0-3 0-4 1l-1-1h2c0-1 1-2 1-3-1-1-1-1-2-1z" class="D"></path><path d="M260 429v-1c0-1 1 0 2-1 0 0 1 0 1-1l1 1s0 1 1 2h0c1 2 2 4 2 6 0 1-1 2-1 3v1l-1 1h-1v-1c-1 1-1 0-1 1l-1-1c0 1-1 1-1 1l-2-2c0-1 0-2 1-4-1 0-1-1 0-1v-4z" class="c"></path><path d="M265 429h0c1 2 2 4 2 6 0 1-1 2-1 3-1-1-1-3-1-4s0-1-1-2v-1c0-1 0-1 1-2h0z" class="t"></path><path d="M260 429v-1c0-1 1 0 2-1 0 0 1 0 1-1l1 1s0 1 1 2h0-1c0-1 0-1-1-1 0 0-1 0-1 1-1 2-1 6 1 8 0 1 0 1 1 1v1h2l-1 1h-1v-1c-1 1-1 0-1 1l-1-1c0 1-1 1-1 1l-2-2c0-1 0-2 1-4-1 0-1-1 0-1v-4z" class="I"></path><path d="M260 434c0 3 1 4 2 5 0 1-1 1-1 1l-2-2c0-1 0-2 1-4z" class="O"></path><path d="M241 440v-1c1-1 1-3 2-5 0 0 1-1 1-2v-2l1-1c1-2 5-5 7-5 1-1 2-2 3-2l1 1h1c1 0 1 1 2 1h1v1 2c-1 1-1 1 0 2v4c-1 0-1 1 0 1-1 2-1 3-1 4l2 2s1 0 1-1l1 1 1 1c1 1 1 2 2 3h-6v1h-3l2 2c-1 0-2 0-3 1l-1-1-2-1h-1-1c-1-1-2-3-2-4l-1-1v1s0 1-1 1h0v1h-1v-2c0-2 0-4-1-6h-1c0 1-1 3-2 4h0-1z" class="N"></path><path d="M241 440v-1c1-1 1-3 2-5 0 0 1-1 1-2v-2l1-1c1-2 5-5 7-5 1-1 2-2 3-2l1 1h1c1 0 1 1 2 1h1v1l-1 1c-1-1-1-1-1-2h-1v1c-1 1-1 0-2 0s-4 2-4 2c-2 2-4 3-6 5-1 1-1 2-2 4 1 0 1-1 1-2 1-1 3-2 4-3v2c-2 3-1 6-2 9 0-2 0-4-1-6h-1c0 1-1 3-2 4h0-1z" class="B"></path><path d="M251 441h0c-2-2-1-5-1-8l4-4v1 1c0 1-1 2-1 3l1 1v5c1 2 2 3 4 3l1 1h1v1h-3l2 2c-1 0-2 0-3 1l-1-1-2-1c-1-1-1-2-1-2-1-1-1-2-1-3z" class="t"></path><path d="M253 434l1 1v5h-1c-1-2 0-4 0-6z" class="B"></path><path d="M251 441h0c1 0 1 0 2 1 0 0 1 1 2 1 2 1 2 1 4 1h1v1h-3l2 2c-1 0-2 0-3 1l-1-1-2-1c-1-1-1-2-1-2-1-1-1-2-1-3z" class="E"></path><path d="M251 441h0c1 0 1 0 2 1 0 0 1 1 2 1h-2c1 2 2 2 2 4l-2-1c-1-1-1-2-1-2-1-1-1-2-1-3z" class="c"></path><path d="M254 430l2-1v3c1 1 1 1 1 2s1 3 2 4l2 2s1 0 1-1l1 1 1 1c1 1 1 2 2 3h-6-1l-1-1c-2 0-3-1-4-3v-5l-1-1c0-1 1-2 1-3v-1z" class="H"></path><path d="M254 431c1 1 1 2 1 3l-1 1-1-1c0-1 1-2 1-3z" class="D"></path><path d="M254 435l1-1c1 2 1 3 2 5 0 1 1 2 2 3l-1 1c-2 0-3-1-4-3v-5z" class="V"></path><path d="M260 175c1 1 2 1 3 2l1 1c1 1 3 1 4 2l3 2v-1c2 0 3 1 4 2l4 4 1-1c1 1 1 2 2 3 2 4 5 6 6 10l-1-1v3h0c1 2 1 3 1 5h0c1 3 2 7 3 10 3 12 3 24 2 35s-4 23-9 33l-7 14c-1 0-1 1-2 1h0c-2-1-4-3-5-5l-2-1-4-4-1-2h0l-1-2h-1v-5h0v-1c0-2-1-4 0-6 0 1 1 2 2 3h0c1-1 0-3 0-4l-1-1c0-2 1-3 2-5h0v-3l-2-1h0v-1c-1 0-1 0-2-1v-1c0-1 1-2 1-3l1-1v-1h0 1c0-1-1-1-1-2-1 0-1-1-1-2h2l1 1h0l2-1c-1-1-1-1-2-1v-4-9-7-1c-1-1-1-1-1-2v-1l-1-2c1-1 1-1 1-2v-2c1-1 0-1 0-2v-1h-1 0v-2l-1-1h0c-1-1-1-1-1-3h-1c0-1-1-1-2-2h0l-1-1c0-2-1-2-2-3h0l-1-2-3-1v-1h2v-1-1-2c0-1 0-1-1-2l1-1s-1-1-1-2h0c1-1 1-2 1-3v-2l1-2c0-2 0-3 1-4l1 1h1v2c1-2 3-5 3-7l1-1z" class="AI"></path><path d="M268 190c0-1-1-2-1-3h1c2 0 2 0 4 2 0 1 0 1-1 2l-1 1h-1c0-1-1-1-1-2z" class="q"></path><path d="M264 263c2 1 4 0 6 0v3h1l-1 1h-1l-1-1h-4v-3z" class="f"></path><path d="M288 237l-1-1c1-1 1-3 2-5 1-1 0-6 0-8v-4c1 2 1 3 2 5v1 4h0c-1-2-1-3-1-5v-1c0 3-1 6 0 10v1c-1 1-1 3-2 4v-1z" class="C"></path><path d="M281 207h1c1 0 1-1 1-1h1c1 2 2 5 2 8l-1-1c-1 0-1 1-2 0v-2c0-1-1-1-1-2h0l-1-1v-1z" class="f"></path><path d="M260 175c1 1 2 1 3 2l1 1-2 2c-1 0-1-1-2 0h0l-2 2h0c0 1 1 2 2 2l-1 2h1c1 0 1-1 2-1l-1 2h-1-1v-1l-2-1-1-2c1-2 3-5 3-7l1-1z" class="C"></path><path d="M275 199c1 1 1 1 2 1l1-1c1-1 1-1 2-1l4 8h-1s0 1-1 1h-1v-1h-1c0-1-1-2-1-2h-1v-1h-1l-1-1c-1-1-2-3-3-4l2 1h0z" class="G"></path><path d="M276 202h3 1c1 1 1 1 1 2v2h-1c0-1-1-2-1-2h-1v-1h-1l-1-1zm4 11h1c0-1 1-2 2-2v2c1 1 1 0 2 0l1 1c0 4 1 8 0 12 0-2 0-4-1-6h0c-1 0-2 1-3 1v-1h-1v-1l-1-2c-1-1-1-1-1-3h0l1-1z" class="E"></path><path d="M279 214l2 2c1 0 1 0 1-1 0 1 1 1 1 1-1 1-1 1-2 3l-1-2c-1-1-1-1-1-3z" class="M"></path><path d="M281 219c1-2 1-2 2-3l1 1h1 1v2h-3l-1 1h-1v-1z" class="F"></path><path d="M271 182v-1c2 0 3 1 4 2l4 4 1-1c1 1 1 2 2 3 2 4 5 6 6 10l-1-1v3h0c1 2 1 3 1 5-1-3-2-5-3-8-4-7-8-11-14-16z" class="s"></path><path d="M264 185c1 1 1 1 1 2l3 3c0 1 1 1 1 2h1l1-1c1-1 1-1 1-2 0 1 0 1 1 2h2v2l5 5c-1 0-1 0-2 1l-1 1c-1 0-1 0-2-1h0l-2-1c1 1 2 3 3 4l1 1h1v1s0 1-1 1-1 0-2 1v1h0l-2-2c-1-1-1-2-1-2v-1l-1-1-1-1-2-2h-1c-1-1-2-3-3-4l1-1-1-2s-1-1-1-2 1-3 1-4z" class="b"></path><path d="M272 189c0 1 0 1 1 2h2v2h0c0 1 0 1 1 2h-1c-1 0-2-1-2-2h0-1l-1 1c-1-1-1-1-1-2l1-1c1-1 1-1 1-2z" class="C"></path><path d="M275 193l5 5c-1 0-1 0-2 1l-1 1c-1 0-1 0-2-1 0-1 1-1 2-2v-1h-2v-1h1c-1-1-1-1-1-2h0z" class="H"></path><path d="M264 191c3 0 4 4 7 5h0c1 1 1 2 2 2 1 1 2 3 3 4l1 1h1v1s0 1-1 1-1 0-2 1v1h0l-2-2c-1-1-1-2-1-2v-1l-1-1-1-1-2-2h-1c-1-1-2-3-3-4l1-1-1-2z" class="e"></path><path d="M265 193l4 3-2 2c-1-1-2-3-3-4l1-1z" class="B"></path><path d="M269 196c1 1 2 2 3 4 0 1-1 1-1 1l-1-1-2-2h-1l2-2z" class="H"></path><path d="M272 200c2 1 3 4 3 6v1h0l-2-2c-1-1-1-2-1-2v-1l-1-1s1 0 1-1z" class="u"></path><path d="M279 242h1c1 0 3-4 4-6-1 8-6 16-13 20-1 1-3 2-4 3l-5 2c-1 0-1 0-2-1v-1c0-1 1-2 1-3l1-1v-1h0 1c0-1-1-1-1-2-1 0-1-1-1-2h2l1 1h0l2-1v-1h2c0-1 1-1 1-2 1 0 1 1 1 1l3-2 1 1h3c1-2 1-4 2-5z" class="M"></path><path d="M270 248l3-2 1 1h3c-1 1-2 2-2 3-1 1-2 1-2 1-2 1-4 3-5 4l-2-1c2-2 4-2 6-5-1 0-1 0-2-1z" class="c"></path><path d="M270 248l3-2 1 1c0 1-1 1-2 2h0c-1 0-1 0-2-1z" class="Y"></path><path d="M266 249h2c0-1 1-1 1-2 1 0 1 1 1 1 1 1 1 1 2 1-2 3-4 3-6 5v1h-2l-1 1v2 1c1-1 2-1 3-1l1 1-5 2c-1 0-1 0-2-1v-1c0-1 1-2 1-3l1-1v-1h0 1c0-1-1-1-1-2-1 0-1-1-1-2h2l1 1h0l2-1v-1z" class="e"></path><path d="M253 184c0-2 0-3 1-4l1 1h1v2l1 2 2 1v1h1 1l1-2v-2h0l1 2h1 0c0 1-1 3-1 4s1 2 1 2l1 2-1 1c1 1 2 3 3 4h1c-1 1-1 1-2 1h-1c0-1-1-1-1-2l-1 1c0 2-1 2-2 3h-1 0l-1 1h-1l-1-1v1h-1c-1 1-2 1-2 2h0l-1-2-3-1v-1h2v-1-1-2c0-1 0-1-1-2l1-1s-1-1-1-2h0c1-1 1-2 1-3v-2l1-2z" class="H"></path><path d="M262 185v-2h0l1 2h1 0c0 1-1 3-1 4h-1v3h-1v-1h-1v-2h0-1l-2-1h0v1c-1 0-1-1 0-2l1-1h1v1h1 1l1-2z" class="L"></path><path d="M261 191v-3h1v1 3h-1v-1z" class="N"></path><path d="M259 199h2v-2-4c1 0 1-1 2-1l1 2c1 1 2 3 3 4h1c-1 1-1 1-2 1h-1c0-1-1-1-1-2l-1 1c0 2-1 2-2 3h-1 0l-1-2z" class="G"></path><path d="M253 184c0-2 0-3 1-4l1 1h1v2l1 2 2 1h-1l-1 1c-1 1-1 2 0 2v-1h0l2 1-1 2v2h-1l-2-1-1 1s0-1-1-2h-2c1-1 1-2 1-3v-2l1-2z" class="T"></path><path d="M253 184c0-2 0-3 1-4l1 1h1l-1 1v2h-1v-1-1l-1 2z" class="p"></path><path d="M256 181v2l1 2 2 1h-1c-1 0-2 1-3 0v-2-2l1-1z" class="d"></path><path d="M251 191c1-1 1-2 1-3v1h3v1 1h3v2h-1l-2-1-1 1s0-1-1-2h-2z" class="AR"></path><path d="M251 191h2c1 1 1 2 1 2l1-1 2 1h1c0 2 1 3 1 5v1l1 2-1 1h-1l-1-1v1h-1c-1 1-2 1-2 2h0l-1-2-3-1v-1h2v-1-1-2c0-1 0-1-1-2l1-1s-1-1-1-2h0z" class="Q"></path><path d="M253 202l1-1v-2h1c0 1 1 2 1 3-1 1-2 1-2 2h0l-1-2zm1-9l1-1v1c1 1 2 2 2 3 0 2 0 2 1 4v2l-1-1c0-1-1-2-1-2v-2c-1-1-1 0-2 0s-1 0-1-1l1-1v-2z" class="T"></path><path d="M255 192l2 1h1c0 2 1 3 1 5v1l1 2-1 1h-1v-2c-1-2-1-2-1-4 0-1-1-2-2-3v-1z" class="AU"></path><path d="M282 221c1 0 2-1 3-1h0c1 2 1 4 1 6l-2 10c-1 2-3 6-4 6h-1c-1 1-1 3-2 5h-3l-1-1-3 2s0-1-1-1c0 1-1 1-1 2h-2v1c-1-1-1-1-2-1v-4-9-7-1c1-1 1-2 2-2l1 2v1h0l1-1h2 0c1-1 1-2 1-4v1h1v1l1 2 2 1c2 1 2 1 3 1v-1h-2c1-1 2-1 4-2h1v-1-1h0c1-1 1-2 2-3l-1-1z" class="R"></path><path d="M269 237c0-1 0-2 1-2l1 1-1 2-1-1z" class="l"></path><path d="M268 233c1 1 1 2 1 4-1 0-1 1-1 2l-1-3c0-1 0-2 1-3z" class="U"></path><path d="M274 234h0c0 2 0 3 1 5v2h-2-1-1l1-2c-1 0-1 0-2-1h0l1-2 2 2v-1l1-3z" class="e"></path><path d="M271 224v1h1v1l1 2 2 1-1 3v1 1h0l-1-2c0 1-1 1-1 2h-1c0-2 0-4-1-6h0 0c1-1 1-2 1-4z" class="AC"></path><path d="M271 225h1v1l1 2 2 1-1 3v1 1h0l-1-2c0-2 0-4-1-5-1 0 0-1-1-2z" class="F"></path><path d="M264 228c1-1 1-2 2-2l1 2v1h0l1 4c-1 1-1 2-1 3v-1c-2 0-2 0-3 1v-7-1z" class="D"></path><path d="M267 228v1h0l1 4c-1 1-1 2-1 3v-1-1c-1-1-1-2-1-3v-1-1l1-1h0z" class="E"></path><path d="M264 236c1-1 1-1 3-1v1l1 3c0-1 0-2 1-2l1 1h0c1 1 1 1 2 1l-1 2c0 1 0 2-1 4l1 1-2 1c0 1-1 1-1 2h-2v1c-1-1-1-1-2-1v-4-9z" class="C"></path><path d="M268 239c0-1 0-2 1-2l1 1h0c1 1 1 1 2 1l-1 2c0 1 0 2-1 4l1 1-2 1c0 1-1 1-1 2h-2v1c-1-1-1-1-2-1l1-1c1-1 2-1 2-3h0c1-2 1-4 1-6z" class="g"></path><path d="M270 245l1 1-2 1c0 1-1 1-1 2h-2c1-2 2-3 4-4z" class="Y"></path><defs><linearGradient id="AR" x1="278.232" y1="246.223" x2="275.451" y2="237.498" xlink:href="#B"><stop offset="0" stop-color="#565657"></stop><stop offset="1" stop-color="#6f7270"></stop></linearGradient></defs><path fill="url(#AR)" d="M274 233c1 0 2-1 3 0l2 2h0 1v-1c1 1 1 2 1 3-1 2-1 3-2 5-1 1-1 3-2 5h-3l-1-1-3 2s0-1-1-1l2-1-1-1c1-2 1-3 1-4h1 1 2v-2c-1-2-1-3-1-5v-1z"></path><path d="M271 246c1-1 1-2 2-3h1c-1 1-1 2-1 3l-3 2s0-1-1-1l2-1z" class="F"></path><path d="M274 233c1 0 2-1 3 0l2 2c-1 1-1 2-3 3 0 1 0 1-1 1-1-2-1-3-1-5v-1z" class="C"></path><path d="M282 221c1 0 2-1 3-1h0c1 2 1 4 1 6l-2 10c-1 2-3 6-4 6h-1c1-2 1-3 2-5 0-1 0-2-1-3v1h-1 0l-2-2c-1-1-2 0-3 0v-1l1-3c2 1 2 1 3 1v-1h-2c1-1 2-1 4-2h1v-1-1h0c1-1 1-2 2-3l-1-1z" class="e"></path><path d="M280 231h1l-1 1c-1 0-3 1-4 0v-1h4z" class="Y"></path><path d="M283 222v4 1h-1s-1 0-1-1v-1h0c1-1 1-2 2-3zm-3 5c1 1 1 1 2 1v4h0-1v-1h-1v-1h1v-1h-3-2c1-1 2-1 4-2z" class="J"></path><path d="M261 201c1-1 2-1 2-3l1-1c0 1 1 1 1 2h1c1 0 1 0 2-1l2 2 1 1 1 1v1s0 1 1 2l2 2h0v-1c1-1 1-1 2-1s1-1 1-1h1s1 1 1 2h1v1 1l1 1h0c0 1 1 1 1 2-1 0-2 1-2 2h-1l-1 1h0c0 2 0 2 1 3l1 2v1h1v1l1 1c-1 1-1 2-2 3h0v1 1h-1c-2 1-3 1-4 2h2v1c-1 0-1 0-3-1l-2-1-1-2v-1h-1v-1c0 2 0 3-1 4h0-2l-1 1h0v-1l-1-2c-1 0-1 1-2 2-1-1-1-1-1-2v-1l-1-2c1-1 1-1 1-2v-2c1-1 0-1 0-2v-1h-1 0v-2l-1-1h0c-1-1-1-1-1-3h-1c0-1-1-1-2-2h0l-1-1c0-2-1-2-2-3 0-1 1-1 2-2h1v-1l1 1h1l1-1h0 1z" class="M"></path><path d="M280 217l1 2v1h1v1l1 1c-1 1-1 2-2 3h0v1 1h-1c-2 1-3 1-4 2-1-1-2-2-3-2l1-1h0l2 1 1-1s1 0 1-1c1-1 2-2 2-4 0 0 0-1-1-1v-1c1 0 1-1 1-2z" class="AY"></path><path d="M272 219c2-1 3 0 5 1v3c-1 1-1 2-2 2h-1c0 1-1 1-2 1v-1-2c0-1 0-2-1-2 1-1 1-2 1-2z" class="D"></path><path d="M272 223l2 2c0 1-1 1-2 1v-1-2z" class="E"></path><path d="M273 211h0 2v-1h1 1l2 4h0c0 2 0 2 1 3 0 1 0 2-1 2h0c-1-1-1-1-1-2l-1-1-1 1c0 1 1 1 1 2v1c-2-1-3-2-5-1 0 0 0 1-1 2v1l-1-1v-4l1-2 1-1c1-1 0-2 1-3z" class="R"></path><path d="M271 215l1-1c1-1 0-2 1-3v1h0c1 1 2 2 2 3l1 2c-1 0-2 0-2-1v-1h-1l-1 1v2l-1 1h1s0 1-1 2v1l-1-1v-4l1-2z" class="F"></path><path d="M278 204h1s1 1 1 2h1v1 1l1 1h0c0 1 1 1 1 2-1 0-2 1-2 2h-1l-1 1-2-4h-1-1v1h-2 0c-1 1 0 2-1 3l-1 1v-3-1c0-1-1-2-1-3v-1-2l2 1-1-2 2 1 2 2h0v-1c1-1 1-1 2-1s1-1 1-1z" class="AY"></path><path d="M278 204h1s1 1 1 2l-1 1c-1 0-2-1-3-2 0 1 0 1-1 2v-1c1-1 1-1 2-1s1-1 1-1z" class="f"></path><path d="M271 204l2 1 2 2h0v2h0l-3-3-1-2z" class="c"></path><path d="M281 208l1 1h0c0 1 1 1 1 2-1 0-2 1-2 2h-1l-1 1-2-4h0c1-1 2-2 4-2z" class="C"></path><path d="M282 209c0 1 1 1 1 2-1 0-2 1-2 2h-1c-1 0-1-1-1-1l3-3z" class="Z"></path><path d="M267 211v-1h1v2h3v3l-1 2v4l1 1v-1c1 0 1 1 1 2v2h-1v-1c0 2 0 3-1 4h0-2l-1 1h0v-1l-1-2c-1 0-1 1-2 2-1-1-1-1-1-2v-1l-1-2c1-1 1-1 1-2v-2c1-1 0-1 0-2v-1h-1 0v-2l1-1h2v-1l2-1z" class="F"></path><path d="M270 228v-1c0-1-2-5-2-6l1-1c0 2 1 3 2 4 0 2 0 3-1 4z" class="AG"></path><path d="M268 217h2v4l1 1v-1c1 0 1 1 1 2v2h-1v-1c-1-1-2-2-2-4h0c-1-1-1-2-1-3z" class="Z"></path><path d="M263 217l1-1h1 2v1l-1 1c0 2 1 2 1 3h-2-1c0-1 0-1-1-2 1-1 0-1 0-2z" class="R"></path><path d="M262 223c1-1 1-1 1-2v-2c1 1 1 1 1 2h1v1l1 4c-1 0-1 1-2 2-1-1-1-1-1-2v-1l-1-2z" class="J"></path><path d="M265 222l1 4c-1 0-1 1-2 2-1-1-1-1-1-2 1-1 1-2 2-4z" class="B"></path><path d="M267 211v-1h1v2h3v3l-1 2h-2-1v-1h-2-1l-1 1v-1h-1 0v-2l1-1h2v-1l2-1z" class="k"></path><path d="M268 212h3v3l-1 2h-2-1v-1h-2-1l-1 1v-1s1 0 1-1l1 1h2l1-4z" class="AJ"></path><path d="M261 201c1-1 2-1 2-3l1-1c0 1 1 1 1 2h1c1 0 1 0 2-1l2 2 1 1 1 1v1s0 1 1 2l-2-1 1 2-2-1v2 1c0 1 1 2 1 3v1h-3v-2h-1v1l-2 1v1h-2l-1 1-1-1h0c-1-1-1-1-1-3h-1c0-1-1-1-2-2h0l-1-1c0-2-1-2-2-3 0-1 1-1 2-2h1v-1l1 1h1l1-1h0 1z" class="M"></path><path d="M270 207c-1-1-2-1-2-3 0 0 1 1 2 1v2z" class="g"></path><path d="M268 198l2 2-1 2-3-3c1 0 1 0 2-1z" class="K"></path><path d="M270 200l1 1 1 1v1s0 1 1 2l-2-1-2-2 1-2z" class="t"></path><path d="M265 205l1 1h1 0v2h0 1 0l3 3v1h-3v-2h-1v1h-1l-1-1c0-1-1-1-1-2s1-2 1-3z" class="Ab"></path><path d="M262 201h1c1 1 1 1 1 2l1 2c0 1-1 2-1 3s1 1 1 2l1 1h1l-2 1v1h-2l-1 1-1-1h0c-1-1-1-1-1-3v-1h2c1-1 0-1 0-2s0-1-1-2c1-1 1-2 1-4z" class="W"></path><path d="M260 210v-1h2c0 1 0 2-1 4h0c-1-1-1-1-1-3z" class="T"></path><path d="M264 208h-1v-1c-1-1-1-1-1-2 1-1 0-2 2-2l1 2c0 1-1 2-1 3z" class="k"></path><path d="M257 201l1 1h1l1-1h0 1 1c0 2 0 3-1 4 1 1 1 1 1 2s1 1 0 2h-2v1h-1c0-1-1-1-2-2h0l-1-1c0-2-1-2-2-3 0-1 1-1 2-2h1v-1z" class="d"></path><path d="M258 203h0c1 0 2 0 2 1v1h0c-1 0-1-1-2-1h0-1l1-1z" class="p"></path><path d="M290 234v-1c-1-4 0-7 0-10v1c0 2 0 3 1 5h0c0 4 1 7 1 11 0 3-1 6 0 9v2h1c-1 11-4 23-9 33l-7 14c-1 0-1 1-2 1h0c-2-1-4-3-5-5l-2-1-4-4-1-2h0l-1-2h-1v-5h0v-1c0-2-1-4 0-6 0 1 1 2 2 3h0c1-1 0-3 0-4l-1-1c0-2 1-3 2-5h0 4l1 1h1l1-1h-1v-3c2-2 5-3 6-5 6-5 10-14 12-21v1c1-1 1-3 2-4z" class="y"></path><path d="M286 259c-1 6-3 10-5 15-1 1 0 1-1 1 0-3 1-9 3-12l3-4z" class="B"></path><path d="M276 267l1 3h1v-3l1-1c1 1 0 3 0 4v1c-1 2-3 5-3 7s-1 2 0 4c-1 2-1 3-3 5h-1 0v1c0 1 1 1 1 2-2-1-2-1-3-1s-2-1-3-2v-1c1 0 1 0 2 1l1 1h1c-1-2-1-2-1-4h1v2h1c1-1 1-2 2-3 0-2 2-5 1-7h-1l2-1c-1-2 0-3 0-5v-3z" class="V"></path><path d="M270 263h1l1 1h1c0 1 1 1 2 1l1-1v3 3c0 2-1 3 0 5l-2 1h1c1 2-1 5-1 7-1 1-1 2-2 3h-1v-2l2-6v-3c1-1 1-2 1-3h0c-1-1-1-1-1-2h-2c0-1-1-2-1-3l1-1h-1v-3z" class="c"></path><path d="M275 265l1-1v3 3l-1-1c0-1-1-2-1-3l1-1z" class="I"></path><path d="M271 266c1 1 2 1 2 2 1 1 1 3 1 4h0c-1-1-1-1-1-2h-2c0-1-1-2-1-3l1-1z" class="V"></path><path d="M288 237v1c-1 4-1 7-3 11-2 5-4 9-8 13l-1 2-1 1c-1 0-2 0-2-1h-1l-1-1h-1c2-2 5-3 6-5 6-5 10-14 12-21z" class="t"></path><path d="M273 264c1-1 2-2 3-2h1l-1 2-1 1c-1 0-2 0-2-1z" class="E"></path><path d="M290 234v-1c-1-4 0-7 0-10v1c0 2 0 3 1 5h0c0 4 1 7 1 11 0 3-1 6 0 9v2c0 1-1 2-1 4h0-1c-1 2-1 4-2 6l-6 18c-2 1-3 2-4 3 0 1-1 3-2 3 1-3 3-6 4-10 1 0 0 0 1-1 2-5 4-9 5-15 3-8 5-16 4-25z" class="H"></path><path d="M264 266h4l1 1h1c0 1 1 2 1 3h2c0 1 0 1 1 2h0c0 1 0 2-1 3v3l-2 6h-1c0 2 0 2 1 4h-1l-1-1c-1-1-1-1-2-1v1c1 1 2 2 3 2l1 1c0 1-1 2-2 2 0 1 0 1-1 1l-4-4-1-2h0l-1-2h-1v-5h0v-1c0-2-1-4 0-6 0 1 1 2 2 3h0c1-1 0-3 0-4l-1-1c0-2 1-3 2-5h0z" class="a"></path><path d="M274 272h0c0 1 0 2-1 3v3c-1-1-2-1-2-3 0-1 1-2 3-3z" class="B"></path><path d="M268 276h1v3 1 1l-1-1-1-1h-1v-2l2-1z" class="D"></path><path d="M264 266h4l1 1h1c0 1 1 2 1 3l-1 1c-2 1-5 0-7 0v1l-1-1c0-2 1-3 2-5h0z" class="S"></path><path d="M264 266h4l1 1h0-3 0l-1 1-2 1v1 1 1l-1-1c0-2 1-3 2-5h0z" class="x"></path><path d="M261 273c0 1 1 2 2 3-1 1-1 2-1 4h1l2-1 1 1c-1 0-1 0-1 1h-1l3 2c1 1 2 1 3 1 0 2 0 2 1 4h-1l-1-1c-1-1-1-1-2-1v1c1 1 2 2 3 2l1 1c0 1-1 2-2 2 0 1 0 1-1 1l-4-4-1-2h0l-1-2h-1v-5h0v-1c0-2-1-4 0-6z" class="E"></path><path d="M264 281l3 2-1 1h-2v-3z" class="M"></path><path d="M261 273c0 1 1 2 2 3-1 1-1 2-1 4h1v5c1 1 0 1 0 2l-1-2h-1v-5h0v-1c0-2-1-4 0-6z" class="AN"></path><path d="M291 255h0c0-2 1-3 1-4h1c-1 11-4 23-9 33l-7 14c-1 0-1 1-2 1h0c-2-1-4-3-5-5l-2-1c1 0 1 0 1-1 1 0 2-1 2-2l-1-1c1 0 1 0 3 1l-1 1c0 1 0 1 1 1h1v-2l2-5c1 0 2-2 2-3 1-1 2-2 4-3l6-18c1-2 1-4 2-6h1z" class="B"></path><path d="M270 289c1 0 1 0 3 1l-1 1h-1c1 3 4 4 5 7h1c-1 0-1 1-2 1h0c-2-1-4-3-5-5l-2-1c1 0 1 0 1-1 1 0 2-1 2-2l-1-1z" class="I"></path><path d="M270 294h1c1 1 3 3 5 4h1c-1 0-1 1-2 1h0c-2-1-4-3-5-5z" class="K"></path><path d="M282 279l6-18c1-2 1-4 2-6h1c-1 3-2 7-3 10 0 3-1 6-2 9-1 4-4 8-5 12-1 3-3 5-4 7l-2 2c-1-2-1-3-1-5l2-5c1 0 2-2 2-3 1-1 2-2 4-3z" class="f"></path><path d="M276 285c1 0 2-2 2-3 1-1 2-2 4-3-1 3-3 5-4 8 0 2-1 3-3 5 1 1 1 1 2 1l-2 2c-1-2-1-3-1-5l2-5z" class="G"></path><path d="M198 343l1 4 1-1h1c1 2 1 4 2 6h0c1 0 2 0 3 1v1c1 2 2 3 4 5h2c2 3 4 5 8 7h2l1-1c-1-2-2-3-3-5l1-1 2 2 1 1 3 2h5c0 1 1 2 0 3h-1l2 2 3-2 3-1 3-2 2-2h2l1-1 2 1c0 2-3 5-5 6h0c-1 0-2 1-3 2l-2 1h2l-2 2c2 0 2 1 2 2v1l2-2h0 1 4c-3 2-7 5-9 7h0c-1 1-1 2-2 3h2c-2 2-4 4-6 7l-6 8c-1 1-1 2-2 2l-7 12v2c-6 14-10 29-13 44-1 0-1 0-1-1l-2 8c-1 5-2 10-2 15 1 1 1 3 1 4h0 0c0-1 0-2 1-2l-1 16c0 2 1 5 0 7l5 38-1 1 2 5c0 2 0 3-1 5l1 3c0 2 2 5 2 8 2 5 5 10 7 14h0c2 3 4 6 6 8 3 4 8 7 12 11h0v1c1 0 1 0 1 1l2 2v1c-1-1-3-3-5-4 0-1-1-2-2-2s-1-1-2-1h0c-1-1-1-1-2-1l-2-2-1-1c-2-3-5-6-7-9l-4-6c-1-2-3-5-4-6l-1-1v-1h0-1v-1c-1-1-1-1-1-2l-1 1c-2-6-3-11-4-17-1-2-2-5-2-8-1-6-1-13 0-19l1-1v-5l-1-4c0-1 0-1-1-1v1h-1c-1-1 0-2-1-4h0l-1 1c0-3 0-5-1-7h-1-1l2-1c0-1 1-1 2-2v-2l-1 1h0c-1-1-1-1-1-2l-2-1-1-2 1-1c0-1 1-2 2-2h0v-2-1l2-1v-2-2-1-3-3c1 0 1-2 1-3 0-2 1-5 0-8v-1l-1-1v-1-1h-1l-1 1-3-6v-1c0-1 1-1 1-1l-1-2h2v-3-1s1-1 2-1v-6c-1-5-1-11-1-16 1-6 1-11 2-17 0-2-2-4-2-6 0-1 1-1 2-2v-5-1h-1-1c0-1 0-1-1-1l-1 1h-1v-2c1 0 2-1 4-1 0 0 1 0 1-1l1-2-1-3v-1l-1-1h0c1-1 1-1 0-2 1-2 1-3 1-5l1-5c0-3 1-7-1-10l-1-1 1-1c0-1 1-1 1-2v-2c-1-2-1-5-1-7v-3l1-1h0l1 1v2c0-2 0-3 1-5z" class="AI"></path><path d="M207 439v1 4l-1 1v-1-1l-1 2h-1l3-6z" class="y"></path><path d="M195 386c1 0 2 1 3 1h1c-1 1-2 1-2 2v1c-1 0-1 0-1 1v-2l-1-3z" class="z"></path><path d="M200 481v-5c0-1 1-3 0-3v-2c0-1 0-2 1-2 0-1 0-2 1-3-1 5-2 10-2 15h0z" class="V"></path><path d="M192 457h1c0-2 2-2 3-2v6l-2-1c-1-1-1-2-2-3z" class="AK"></path><path d="M196 389v2 6 3h-1v-4-1h-1-1c0-1 0-1-1-1l-1 1h-1v-2c1 0 2-1 4-1 0 0 1 0 1-1l1-2z" class="W"></path><path d="M190 457h2c1 1 1 2 2 3l2 1v5-1l-1-1v-1-1h-1l-1 1-3-6z" class="T"></path><path d="M195 442v-1-6-2-1-2l1 1v16c-1-1-1-3-1-5z" class="AK"></path><path d="M203 425h1 1c0-1 1-1 1-2l-2 12h-1v-4c-1 0-1-1-1-2l1-4z" class="Z"></path><path d="M202 429c0 1 0 2 1 2v4h1c-1 5-3 10-4 15v1c-1-1-1-2-1-2 0-2 1-4 1-6 1-2 1-5 1-7 0-1 0-3 1-5h0v-2z" class="c"></path><path d="M195 499c1 0 1 0 1-1s1-3 1-4v-3-1-2h-1v-4h1l-1-1c0-2 0-4 1-6l2 26c-1-1-1-2-1-3l-1-1c0 1-1 1-1 2l-1 1v-1-2h0 0z" class="AM"></path><path d="M200 481h0c1 1 1 3 1 4h0 0c0-1 0-2 1-2l-1 16c0 2 1 5 0 7h0v-2c-1 3 0 6-1 8v-31z" class="I"></path><path d="M243 374h0 1 4c-3 2-7 5-9 7l-9 9c-1 1-2 3-3 4h-1c4-7 10-12 15-18l2-2z" class="Z"></path><path d="M194 442h1c0 2 0 4 1 5v5 3c-1 0-3 0-3 2h-1-2v-1c0-1 1-1 1-1l-1-2h2v-3-1s1-1 2-1v-6z" class="p"></path><path d="M190 453h2v-3-1s1-1 2-1c0 1 1 2 1 3-1 2-2 3-3 4h-1l-1-2z" class="AK"></path><path d="M199 523c0 8 0 16 1 23l3 12c0 3 1 5 2 7v1 1l-1 1c-2-6-3-11-4-17-1-2-2-5-2-8-1-6-1-13 0-19l1-1z" class="G"></path><path d="M216 410v4c-1 4-3 6-4 10-1 1-2 3-2 5-1 3-3 6-3 10l-3 6c-2 4-2 10-3 15-1 1-1 5-2 6v-2c2-5 2-11 3-16 1-3 2-5 3-7l4-13c1-4 3-10 6-14 0-1 1-3 1-4z" class="E"></path><path d="M195 502l1-1c0-1 1-1 1-2l1 1c0 1 0 2 1 3 0 5 1 11 0 15l-1-4c0-1 0-1-1-1v1h-1c-1-1 0-2-1-4h0l-1 1c0-3 0-5-1-7h-1-1l2-1c0-1 1-1 2-2v1z" class="W"></path><path d="M195 502l1-1c0-1 1-1 1-2l1 1-1 1h0v1h0c0 1 0 2-1 3h0l-1-3z" class="S"></path><path d="M191 504l2-1c0-1 1-1 2-2v1l1 3 1 1c-2 1-2 2-2 4l-1 1c0-3 0-5-1-7h-1-1z" class="d"></path><path d="M226 380c2 0 3-1 4-1v-1l1 1c-5 5-10 10-14 16h-1c0-1 0-2 1-3l-1-1 3-6c1-2 2-3 3-4h3l1-1z" class="s"></path><path d="M217 392l-1-1 3-6c1-2 2-3 3-4h3c-3 3-6 7-8 11z" class="N"></path><defs><linearGradient id="AS" x1="205.477" y1="521.23" x2="199.215" y2="523.051" xlink:href="#B"><stop offset="0" stop-color="#555d5a"></stop><stop offset="1" stop-color="#7a7879"></stop></linearGradient></defs><path fill="url(#AS)" d="M200 512c1-2 0-5 1-8v2h0l5 38-1 1c-3-11-5-21-5-33z"></path><path d="M196 474l1 1v-3l1 1c0 1 0 4-1 4-1 2-1 4-1 6l1 1h-1v4h1v2 1 3c0 1-1 3-1 4s0 1-1 1h0 0l-1 1h0c-1-1-1-1-1-2l-2-1-1-2 1-1c0-1 1-2 2-2h0v-2-1l2-1v-2-2-1-3-3c1 0 1-2 1-3z" class="S"></path><path d="M195 488v6-3l-2-1v-1l2-1z" class="AR"></path><path d="M193 490l2 1v3 5h0 0l-1 1h0c-1-1-1-1-1-2l-2-1-1-2 1-1c0-1 1-2 2-2h0v-2z" class="T"></path><path d="M194 495v2-1l1 3h0l-1 1h0c-1-1-1-1-1-2l1-3z" class="d"></path><path d="M193 492h0c0 1 1 2 1 3l-1 3-2-1-1-2 1-1c0-1 1-2 2-2z" class="n"></path><path d="M195 396v4h1v6c0 5 1 9 0 13 0 2 0 3-1 4l1 8-1-1v2 1 2 6 1h-1c-1-5-1-11-1-16 1-6 1-11 2-17 0-2-2-4-2-6 0-1 1-1 2-2v-5z" class="AQ"></path><path d="M216 395h1c-4 9-9 19-11 28 0 1-1 1-1 2h-1-1v-1l2-5 5-16 2-4c0-2 2-3 4-4z" class="J"></path><defs><linearGradient id="AT" x1="237.512" y1="398.283" x2="216.021" y2="398.149" xlink:href="#B"><stop offset="0" stop-color="#262728"></stop><stop offset="1" stop-color="#5e6060"></stop></linearGradient></defs><path fill="url(#AT)" d="M226 394h1c1-1 2-3 3-4l9-9h0c-1 1-1 2-2 3h2c-2 2-4 4-6 7l-6 8c-1 1-1 2-2 2h-1c-3 4-5 9-8 13v-4c0-1 1-2 2-3 2-5 4-10 8-13z"></path><path d="M216 414c3-4 5-9 8-13h1l-7 12v2c-6 14-10 29-13 44-1 0-1 0-1-1 0-4 2-9 2-13l1-1v-4-1c0-4 2-7 3-10 0-2 1-4 2-5 1-4 3-6 4-10z" class="E"></path><path d="M216 414c3-4 5-9 8-13h1l-7 12-3 6c-1 2-2 4-2 5-3 7-4 14-6 20v-4-1c0-4 2-7 3-10 0-2 1-4 2-5 1-4 3-6 4-10z" class="a"></path><path d="M200 381c1 2 2 3 4 4 1 0 1 0 2-1l1 1v1l1-2 1 1v1c0 2-1 5-2 7-1 1-2 3-3 5 0 1-1 1-1 2s0 2-1 4l-1-1v-2 10c-1-1-1-5-1-6v-1l-1-2v-13-1-1h-1 2v-6h0z" class="C"></path><path d="M208 384l1 1v1c0 2-1 5-2 7-1 1-2 3-3 5 0 1-1 1-1 2s0 2-1 4l-1-1v-2-7-9l1 1c0 2 1 4 3 5 0-1 0-1 1-2l1-3 1-2z" class="M"></path><path d="M201 394h0l1-1 1-1v3c-1 1-2 0-1 2 0 2 0 2 1 3 0 1 0 2-1 4l-1-1v-2-7z" class="c"></path><path d="M205 401l3-1c0 1-1 3-1 4l1 1c1 0 1-1 2-2l-5 16-2 5v1l-1 4v2h0c-1 2-1 4-1 5v-20-2-3-10 2l1 1v1h1c0-2 1-3 2-4h0z" class="I"></path><path d="M203 424c0-3 0-5 1-8v-3s1-1 1-2h1 0c-1 2-2 6-1 8l-2 5z" class="E"></path><path d="M205 401l3-1c0 1-1 3-1 4 0 2-1 3-2 4-1 3-1 6-2 8h-1c0-1 0-3 1-4v-7c0-2 1-3 2-4h0z" class="Z"></path><path d="M205 401v4l-2 7v-7c0-2 1-3 2-4z" class="E"></path><path d="M209 377c1 0 1 1 1 2l3-1v2c1-2 2-2 4-2h1v1l-1 2h2c1-1 2-2 4-3h0l-1 3c-1 1-2 2-3 4l-3 6 1 1c-1 1-1 2-1 3-2 1-4 2-4 4l-2 4c-1 1-1 2-2 2l-1-1c0-1 1-3 1-4l-3 1h0c-1 1-2 2-2 4h-1v-1c1-2 1-3 1-4s1-1 1-2c1-2 2-4 3-5 1-2 2-5 2-7v-1l-1-1h0v-3h1c1-1 0-3 0-4z" class="b"></path><path d="M217 378h1v1l-1 2c-1 3-3 7-5 9 0-1 1-3 1-5 2-2 2-5 4-7z" class="Y"></path><path d="M210 387h1c0 2-1 5-2 7 0 2 0 4-1 5h-2c1-2 1-3 1-5 2-1 3-5 3-7z" class="AY"></path><path d="M213 380c1-2 2-2 4-2-2 2-2 5-4 7v-1c-1 1-1 1-1 2l-1 1h-1l3-7z" class="I"></path><path d="M219 381c1-1 2-2 4-3h0l-1 3c-1 1-2 2-3 4l-3 6 1 1c-1 1-1 2-1 3-2 1-4 2-4 4 0-3 1-5 2-7l5-11z" class="Z"></path><path d="M209 377c1 0 1 1 1 2l3-1v2l-3 7c0 2-1 6-3 7 0 2 0 3-1 5-1 0-1 1-1 2h0c-1 1-2 2-2 4h-1v-1c1-2 1-3 1-4s1-1 1-2c1-2 2-4 3-5 1-2 2-5 2-7v-1l-1-1h0v-3h1c1-1 0-3 0-4z" class="B"></path><path d="M209 377c1 0 1 1 1 2l1 1s0 1-1 1c-1 2 0 4-1 5v-1l-1-1h0v-3h1c1-1 0-3 0-4z" class="K"></path><path d="M198 343l1 4 1-1h1c1 2 1 4 2 6h0c1 0 2 0 3 1v1c1 2 2 3 4 5h2c2 3 4 5 8 7h2l1-1c-1-2-2-3-3-5l1-1 2 2 1 1 3 2h5c0 1 1 2 0 3h-1l2 2 3-2 3-1 3-2 2-2h2l1-1 2 1c0 2-3 5-5 6h0c-1 0-2 1-3 2l-2 1h2l-2 2-6 5s-1 1-2 1l-1-1v1c-1 0-2 1-4 1l-1 1h-3l1-3h0c-2 1-3 2-4 3h-2l1-2v-1h-1c-2 0-3 0-4 2v-2l-3 1c0-1 0-2-1-2 0 1 1 3 0 4h-1v3h0l-1 2v-1l-1-1c-1 1-1 1-2 1-2-1-3-2-4-4h0v6h-2c-1 0-2-1-3-1v-1l-1-1h0c1-1 1-1 0-2 1-2 1-3 1-5l1-5c0-3 1-7-1-10l-1-1 1-1c0-1 1-1 1-2v-2c-1-2-1-5-1-7v-3l1-1h0l1 1v2c0-2 0-3 1-5z" class="y"></path><path d="M208 374c3 0 5 1 7 2-2 1-2 1-2 2l-3 1c0-1 0-2-1-2 0-1 0-2-1-3z" class="a"></path><path d="M215 376c3 0 7 1 10 0 2 0 3 0 4 1l-3 3-1 1h-3l1-3h0c-2 1-3 2-4 3h-2l1-2v-1h-1c-2 0-3 0-4 2v-2c0-1 0-1 2-2z" class="L"></path><path d="M213 378c0-1 0-1 2-2 0 1 1 1 1 1h3c0 1 0 1-1 2v-1h-1c-2 0-3 0-4 2v-2z" class="G"></path><path d="M200 376c1-1 1-2 2-3s2-1 3-1c1 1 2 1 3 2s1 2 1 3 1 3 0 4h-1v3h0l-1 2v-1l-1-1c-1 1-1 1-2 1-2-1-3-2-4-4v-5z" class="b"></path><path d="M204 375h1l-1 1c0 1 1 1 0 3l-1-1c-1-1-1-1 0-1l-1-2h2z" class="G"></path><path d="M200 376c1-1 1-2 2-3s2-1 3-1c0 1 0 2-1 3h-2c0 1-1 2-1 3l-1-2z" class="Z"></path><path d="M204 379c1-2 0-2 0-3l1-1c1 1 2 2 2 3-1 0-1 1-1 2v1 1h-1v-1c0-1-1-1-1-2z" class="D"></path><path d="M205 372c1 1 2 1 3 2s1 2 1 3 1 3 0 4h-1c0-1 0-2-1-3 0-1-1-2-2-3h-1c1-1 1-2 1-3z" class="E"></path><path d="M247 361l2 1c0 2-3 5-5 6h0c-1 0-2 1-3 2l-2 1h2l-2 2-6 5s-1 1-2 1l-1-1v1c-1 0-2 1-4 1l3-3c-1-1-2-1-4-1 2 0 4 0 6-1 0 0 1 0 1-1h1 1l1-1c1 0 2-1 3-2l-7 3c-2 1-7 1-9 0h0-1c1-1 2-1 3-1v-2l4 1c2 0 5-2 7-1 1 0 4-1 5-1l1-1s0-1-1-1l1-1-2-1 3-2 2-2h2l1-1z" class="V"></path><path d="M244 362h2c-1 2-3 4-5 5l-2-1 3-2 2-2z" class="L"></path><path d="M224 371l4 1c2 0 5-2 7-1-2 1-3 2-5 2-3 1-6 1-8 1h-1c1-1 2-1 3-1v-2z" class="K"></path><path d="M239 371h2l-2 2-6 5s-1 1-2 1l-1-1v1c-1 0-2 1-4 1l3-3c3-2 7-3 10-6z" class="e"></path><defs><linearGradient id="AU" x1="239.617" y1="359.632" x2="205.013" y2="364.306" xlink:href="#B"><stop offset="0" stop-color="#1a1a1b"></stop><stop offset="1" stop-color="#515252"></stop></linearGradient></defs><path fill="url(#AU)" d="M198 343l1 4 1-1h1c1 2 1 4 2 6h0c1 0 2 0 3 1v1c1 2 2 3 4 5h2c2 3 4 5 8 7h2l1-1c-1-2-2-3-3-5l1-1 2 2 1 1 3 2h5c0 1 1 2 0 3h-1l2 2 3-2 3-1 2 1-1 1c1 0 1 1 1 1l-1 1c-1 0-4 1-5 1-2-1-5 1-7 1l-4-1v2c-1 0-2 0-3 1l-1-1h-3l-1-1c-8-3-13-8-16-16-1-1-1-3-2-5l-1-3c0-2 0-3 1-5z"></path><path d="M224 366c1 1 2 1 3 1 2 1 2 2 4 2-4 0-6 0-10-1h0c1-1 3-1 3-2z" class="l"></path><path d="M217 369l1 1 6 1v2c-1 0-2 0-3 1l-1-1h-3l1-1-1-3z" class="U"></path><path d="M217 369l1 1c1 0 2 1 3 3h-1-3l1-1-1-3z" class="g"></path><path d="M198 343l1 4 1-1c0 1 1 2 1 3l2 5c0 2 1 4 2 5h-2c-1-1-1-2-2-3h-1c-1-1-1-3-2-5l-1-3c0-2 0-3 1-5z" class="E"></path><path d="M198 343l1 4c1 1 1 3 1 4h0l-1-1c0 1 0 1-1 1l-1-3c0-2 0-3 1-5z" class="G"></path><path d="M220 360l1-1 2 2 1 1 3 2h5c0 1 1 2 0 3h-1l2 2h-2c-2 0-2-1-4-2-1 0-2 0-3-1l-1-1c-1-2-2-3-3-5z" class="o"></path><path d="M227 367h4l2 2h-2c-2 0-2-1-4-2z" class="i"></path><path d="M203 352c1 0 2 0 3 1v1c1 2 2 3 4 5h2c2 3 4 5 8 7h2l1-1 1 1c0 1-2 1-3 2h0l-3-1c-7-2-12-9-15-15z" class="g"></path><path d="M200 356h1c1 1 1 2 2 3h2c1 2 3 4 5 6s4 3 7 4l1 3-1 1-1-1c-8-3-13-8-16-16z" class="J"></path><path d="M205 359c1 2 3 4 5 6h0c-4 0-5-3-7-6h2z" class="e"></path><path d="M214 299c1 1 2 2 3 2 3 2 6 4 8 7l3 6v-2c1 1 1 1 3 1h0 0l2 2h0c1 3 3 4 5 6h0c2 2 5 4 8 6h3v-1c1 0 1 0 2 1-1-1-1-1-1-2h1c1 0 2-1 2-1l1 1 2 1h0 0l1 2c1 0 1-1 2-1v1h1l3 2c1 0 1 1 2 0h1 1l-1 2c1 0 1 0 1 1h1c1 1 2 1 3 2h1v1h0l-1-1h-1l1 1h0l10 6 6 2 1-1 2 9h-1l-1 1c-2 2-4 3-7 3-2 0-5 0-7 1h-2l1 1h1c0 1 1 1 1 1 3 1 5-1 7 1h-6c-2 0-4 1-6 0v2 1c0 2-1 2-2 3l-2-2c0-1 0-1-1-1l-1 1-1-1c-1 1-1 2-2 3l-2 2c-1 1-3 1-4 2l-1-1-5 4-1 1h-4-1 0l-2 2v-1c0-1 0-2-2-2l2-2h-2l2-1c1-1 2-2 3-2h0c2-1 5-4 5-6l-2-1-1 1h-2l-2 2-3 2-3 1-3 2-2-2h1c1-1 0-2 0-3h-5l-3-2-1-1-2-2-1 1c1 2 2 3 3 5l-1 1h-2c-4-2-6-4-8-7h-2c-2-2-3-3-4-5v-1c-1-1-2-1-3-1h0c-1-2-1-4-2-6h-1l-1 1-1-4c-1 2-1 3-1 5v-2l-1-1h0l-1 1v-2-2l-1 2v-3c0-2 0-3-1-4s0-2-1-3c1-2 1-4 2-5h0v-1-3c1-2 0-3-1-4v-1l-1-1c-1-1-1-2-1-3 1-1 1-2 1-4-1-1 0-1 0-2v-1c0-1 0-1 1-2v-1-1s0-1 1-2c-1-1-1-1 0-2h1l1 1s1 0 1 1h8c1-1 3-1 5-1l-1-1h2v-2h2 1 0z" class="l"></path><path d="M247 350h1v3h-1v-3z" class="F"></path><path d="M244 362l3-3 1 1-1 1-1 1h-2z" class="H"></path><path d="M225 314c1 3 4 5 6 8 0 1 1 2 2 3l5 5c-1 0-1-1-2-1-5-4-9-8-12-14l1-1z" class="AP"></path><path d="M246 345v-2c2 0 4 4 6 5l6 3v1h0c1 2 3 2 5 4h-2c-1 0-1-1-2-2h-1v2h0c-1-1-2-2-3-4-1-1-2-2-3-2l-2-1h-2c0-2-1-2-2-4z" class="e"></path><path d="M246 345c1 0 2 0 2 1 1 0 1 1 2 1 1 1 1 1 1 2 1 0 1 0 1 1l-2-1h-2c0-2-1-2-2-4z" class="F"></path><path d="M242 357l1 1c1-2 2-3 3-5h0 1c-1 2-2 5-3 7l-2 3v1l-3 2-3 1-3 2-2-2h1c1-1 0-2 0-3h0c1 0 2-1 3-1 0 0-1 0-1-1l4-4c0 1 1 1 1 1 2-1 1-1 1-2l3-3-1 3z" class="X"></path><path d="M244 360l-2 3v1l-3 2-3 1 8-7z" class="AC"></path><path d="M238 358c0 1 1 1 1 1 2-1 1-1 1-2l3-3-1 3c-2 3-4 5-7 6 0 0-1 0-1-1l4-4z" class="H"></path><path d="M239 340l2 4c1 4 2 7 2 10l-3 3c0 1 1 1-1 2 0 0-1 0-1-1v-1c1-2 1-3 1-5v-2-1h-4v-1l-1 1c0-1-1-2-1-2v-3c1-1 1-1 1-2v-1h1 1 2l1-1z" class="C"></path><path d="M235 348l2-1h1l1 2h-4v-1z" class="L"></path><path d="M236 341h2v1 1c-1-1-3-1-4-1v-1h1 1z" class="O"></path><path d="M239 340l2 4c1 4 2 7 2 10l-3 3c0 1 1 1-1 2 0 0-1 0-1-1v-1c1-2 1-3 1-5v-2h1 1v-1c0-2-1-4-2-6h-1v-1-1l1-1z" class="B"></path><path d="M250 349l2 1c1 0 2 1 3 2 1 2 2 3 3 4h0v-2h1c1 1 1 2 2 2h2c4 1 7 2 10 2h1c0 1 1 1 1 1 3 1 5-1 7 1h-6c-2 0-4 1-6 0v2 1c0 2-1 2-2 3l-2-2c0-1 0-1-1-1l-1 1-1-1c-1 1-1 2-2 3l-2 2c-1-1-1-1-1-2l-1-1v-1h0l-1-1v-4l-1-2c-1-2-2-3-3-5h0v-1h0v-1h-2v-1z" class="s"></path><path d="M257 364v-2c0-1 0-2 2-3 1 0 2 0 3 1s1 1 1 3c-1 1-1 2-2 3l-2 2c-1-1-1-1-1-2l-1-1v-1h0z" class="B"></path><path d="M262 360v-2l1-1c0 1 1 1 1 2l2-1c1 1 2 1 3 1 1 1 4 1 7 1-2 0-4 1-6 0v2 1c0 2-1 2-2 3l-2-2c0-1 0-1-1-1l-1 1-1-1c0-2 0-2-1-3z" class="X"></path><path d="M262 360v-2l1-1c0 1 1 1 1 2l2-1c1 2 2 2 2 4h-1c-2 0-1-1-3-2h-2z" class="AG"></path><path d="M248 356l1-2c1-1 1-1 3-2 1 2 2 3 3 5l1 2v4l1 1h0v1l1 1c0 1 0 1 1 2-1 1-3 1-4 2l-1-1-5 4-1 1h-4-1 0l-2 2v-1c0-1 0-2-2-2l2-2h-2l2-1c1-1 2-2 3-2h0c2-1 5-4 5-6l-2-1 1-1-1-1 1-3z" class="C"></path><path d="M248 356l1-2c1-1 1-1 3-2 1 2 2 3 3 5l-3-1c-1 1-2 0-4 0z" class="D"></path><path d="M256 359v4 1c-1 0-1 1-1 2-1 1-1 1-2 1 0 1 0 1-1 1v-2l1-2c1-1 2-1 2-1 0-1-1-2-1-2l2-2z" class="B"></path><path d="M256 363l1 1h0v1l1 1c0 1 0 1 1 2-1 1-3 1-4 2l-1-1c1 0 1 0 1-1l-2-1c1 0 1 0 2-1 0-1 0-2 1-2v-1z" class="V"></path><path d="M252 366v2c1 0 1 0 1-1l2 1c0 1 0 1-1 1l-5 4-1 1h-4-1 0c2-3 6-5 9-8z" class="G"></path><path d="M248 360l2-2h2c1 1 1 1 1 2-1 1-1 2-2 3l-1 1c-1 1-2 3-4 3-1 1-1 2-3 3h0l-2 1h-2l2-1c1-1 2-2 3-2h0c2-1 5-4 5-6l-2-1 1-1z" class="B"></path><path d="M226 346c1-1 1-1 2-1 0 0 0 1 1 1l1-1v1s0 1 1 2v1h1l1-2s1 1 1 2l1-1v1h4v1 2c0 2 0 3-1 5v1l-4 4c0 1 1 1 1 1-1 0-2 1-3 1h0-5l-3-2-1-1-2-2-1 1c0-2-1-5 0-6 0-1 1-2 3-2 0 0 1 0 1 1 1 0 1 0 1 1h1c1-1 2-1 2-2v-3-1c0-1-1-2-2-2z" class="f"></path><path d="M238 353h0l1-1c0 2 0 3-1 5 0-1-1-1-1-2l1-2h0z" class="b"></path><path d="M229 353l1 1h1v2c-1-1-2 0-4 0v-1c1 0 2-1 2-2h0z" class="N"></path><path d="M230 350v1h1 1l-1 3h-1l-1-1c1-1 1-2 1-3zm2 14h0v-3c1 1 1 1 2 1 0 1 1 1 1 1-1 0-2 1-3 1z" class="G"></path><path d="M222 355c1 0 2 0 3 1l-2 2h-1c0-1-1-2-1-2l1-1zm8-9s0 1 1 2v1h1v2h0-1-1v-1-4z" class="N"></path><path d="M232 351h0v2h1s0-1 1-1h3v1h-1 0c-1 1-2 1-3 0-1 1-1 1-1 2l-1 1h0v-2l1-3z" class="L"></path><path d="M233 347s1 1 1 2l1-1v1c0 1-1 2-1 3-1 0-1 1-1 1h-1v-2-2l1-2z" class="D"></path><path d="M235 349h4v1 2l-1 1h0-1v-1h-3c0-1 1-2 1-3z" class="O"></path><path d="M232 355c0-1 0-1 1-2 1 1 2 1 3 0h0v1c-1 1-1 4-1 6-1 0-1 0-2-1v1h-2v-1h0 1c0-1-1-2-1-3l1-1z" class="u"></path><path d="M232 355h0 3 0c-1 2-2 3-2 4h-1c0-1-1-2-1-3l1-1z" class="B"></path><path d="M220 360c0-2-1-5 0-6 0-1 1-2 3-2h0l-1 1c0 1 1 1 1 2h-1l-1 1v2c2 1 4 0 5 0l1-2h2v2l1 1-1 1h0c0 1 1 1 2 2v1c-1-1-2-1-3-1s-1 1-1 2l-3-2-1-1-2-2-1 1z" class="F"></path><path d="M223 361v-1c1 0 1-1 2-1v1c0 1 0 2-1 2l-1-1z" class="K"></path><path d="M229 358l1 1-1 1h-2c0-1-1-1-1-2h1 2z" class="J"></path><path d="M225 360v2h1l1-1v-1h0 2 0c0 1 1 1 2 2v1c-1-1-2-1-3-1s-1 1-1 2l-3-2c1 0 1-1 1-2z" class="M"></path><path d="M214 299c1 1 2 2 3 2 3 2 6 4 8 7l3 6 1 1v1l-3-3c0-1-1-1-1-2l-1 1s1 1 1 2l-1 1c3 6 7 10 12 14 1 1 1 2 1 3v1c1 1 1 2 2 3 1 0 1 1 1 1 0 1 1 2 1 2 2 3 3 6 3 8v1c0-1-1-1-1-1 0-1 0-2-1-3v-1c-1 0-1 1-1 1l-2-4-1 1h-2-1-1v1c0 1 0 1-1 2v3l-1 2h-1v-1c-1-1-1-2-1-2v-1l-1 1c-1 0-1-1-1-1-1 0-1 0-2 1l-2-1h1l-1-1c-1-1-1 0-2 0v-1l1-1v-2-1-1h-1 0v-1c1-2 1-2 0-4v-3c0-4-5-9-8-12-4-2-9-2-14-2l-2 1v-1c-1 0-1 1-2 0h-5c1-1 1-2 1-4-1-1 0-1 0-2v-1c0-1 0-1 1-2v-1-1s0-1 1-2c-1-1-1-1 0-2h1l1 1s1 0 1 1h8c1-1 3-1 5-1l-1-1h2v-2h2 1 0z" class="a"></path><path d="M199 312c3-1 4-2 7-2h-1l-4 4h-1l-2-1 1-1zm28 27h-1 2c0-1 0-1 1-1h1c-2-1-4-1-6-1 1-2 3-5 5-7l1 1h0c-1 1-3 3-3 5h3c1 0 2 0 3 1l1 2h0l-1-1-1 1h-1-3-1z" class="B"></path><path d="M230 336c1 0 2 0 3 1l1 2h0l-1-1c-2-1-4-1-6-1v-1h3z" class="b"></path><path d="M200 316l1-1c1-1 5-2 6-2l2 1h0c3 0 5 2 7 3v1h-2c-4-2-9-2-14-2z" class="B"></path><path d="M230 331l1-1c2 1 5 3 5 5 2 1 2 3 3 5l-1 1h-2-1c0-1 0-2-1-2l-1-2c-1-1-2-1-3-1h-3c0-2 2-4 3-5h0z" class="AI"></path><path d="M236 335c2 1 2 3 3 5l-1 1h-2l1-2c1-1 0-3-1-4z" class="C"></path><path d="M203 307h4 1c2 0 3 1 5 2h2c1 2 3 4 4 5h0l-1 2c0-1-1-2-2-3-3-3-7-3-10-3s-4 1-7 2h-1c0-1-1-1-1-1 1-2 3-3 5-3l1-1z" class="e"></path><path d="M197 311c1-2 3-3 5-3h0l-2 2h1 1c1-1 1-1 2-1h0l1 1h1c-3 0-4 1-7 2h-1c0-1-1-1-1-1z" class="F"></path><path d="M208 304h4c2-1 4 0 5 1l3 3c0 2 0 5-1 6h0c-1-1-3-3-4-5h-2c-2-1-3-2-5-2h-1-4 0c0-1 1-1 2-2h0c1 0 2 0 3-1z" class="N"></path><path d="M208 304h4c2-1 4 0 5 1l3 3c0 2 0 5-1 6h0l1-1c-1-2-4-7-6-8h0c-2-1-4 0-6-1z" class="F"></path><path d="M223 338c1 0 2 0 3 1h1 1 3 1l1-1 1 1h0c1 0 1 1 1 2h-1v1c0 1 0 1-1 2v3l-1 2h-1v-1c-1-1-1-2-1-2v-1l-1 1c-1 0-1-1-1-1-1 0-1 0-2 1l-2-1h1l-1-1c-1-1-1 0-2 0v-1l1-1v-2-1-1z" class="L"></path><path d="M233 338l1 1h0c1 0 1 1 1 2h-1v1c0 1 0 1-1 2v3l-1 2h-1v-1c0-1 1-1 1-2s-1-3 0-4v-1c0-1-1-1-1-2h1l1-1z" class="u"></path><path d="M233 338l1 1h0c1 0 1 1 1 2h-1v1c0 1 0 1-1 2 0-2 0-4-1-5l1-1z" class="V"></path><path d="M223 340c2 0 3 0 5 2 1 0 1 2 2 3l-1 1c-1 0-1-1-1-1-1 0-1 0-2 1l-2-1h1l-1-1c-1-1-1 0-2 0v-1l1-1v-2z" class="E"></path><path d="M193 306v-1s0-1 1-2c1 2 2 1 4 2h7c-1 1-2 1-2 2h0l-1 1c-2 0-4 1-5 3 0 0 1 0 1 1h1l-1 1v1 2c-1 0-1 1-2 0h-5c1-1 1-2 1-4-1-1 0-1 0-2v-1c0-1 0-1 1-2v-1z" class="K"></path><path d="M193 306c1-1 2-1 4-1 0 1 0 2-1 3h-3v-1-1z" class="C"></path><path d="M193 307v1 2h1v-1h1c1 1 0 2 1 3h0l1-1s1 0 1 1h1l-1 1v1 2c-1 0-1 1-2 0h-5c1-1 1-2 1-4-1-1 0-1 0-2v-1c0-1 0-1 1-2z" class="L"></path><path d="M197 311s1 0 1 1h1l-1 1v1c-1 0-2 1-3 1 0-1 0-2 1-3h0l1-1z" class="Y"></path><path d="M214 299c1 1 2 2 3 2 3 2 6 4 8 7l3 6 1 1v1l-3-3c0-1-1-1-1-2l-1 1s1 1 1 2l-1 1s-1-1-1-2c-1-2-2-3-3-5l-3-3c-1-1-3-2-5-1h-4c-1 1-2 1-3 1h0-7c-2-1-3 0-4-2-1-1-1-1 0-2h1l1 1s1 0 1 1h8c1-1 3-1 5-1l-1-1h2v-2h2 1 0z" class="i"></path><path d="M214 299c1 1 2 2 3 2-3 0-5 0-7 1l-1-1h2v-2h2 1 0z" class="H"></path><path d="M194 303c-1-1-1-1 0-2h1l1 1s1 0 1 1h8 0c-2 1-4 1-6 1v1c1 0 2 0 3-1 1 0 2 0 2 1h1 0-7c-2-1-3 0-4-2z" class="X"></path><path d="M194 303c-1-1-1-1 0-2h1l1 1v1h-2z" class="AC"></path><path d="M220 308c1 2 2 3 3 5 0 1 1 2 1 2 3 6 7 10 12 14 1 1 1 2 1 3v1c1 1 1 2 2 3 1 0 1 1 1 1 0 1 1 2 1 2 2 3 3 6 3 8v1c0-1-1-1-1-1 0-1 0-2-1-3v-1c-1 0-1 1-1 1l-2-4c-1-2-1-4-3-5 0-2-3-4-5-5l-1 1-1-1s1-1 1-2c-1-1-3-3-4-5-2-2-3-4-4-6h-1v3h0c-1 0-1 0-2-1s-1-1-1-3l1-2c1-1 1-4 1-6z" class="J"></path><path d="M228 314v-2c1 1 1 1 3 1h0 0l2 2h0c1 3 3 4 5 6h0c2 2 5 4 8 6h3v-1c1 0 1 0 2 1-1-1-1-1-1-2h1c1 0 2-1 2-1l1 1 2 1h0 0l1 2c1 0 1-1 2-1v1h1l3 2c1 0 1 1 2 0h1 1l-1 2c1 0 1 0 1 1h1c1 1 2 1 3 2h1v1h0l-1-1h-1l1 1h0l10 6 6 2 1-1 2 9h-1l-1 1c-2 2-4 3-7 3-2 0-5 0-7 1h-2l1 1c-3 0-6-1-10-2-2-2-4-2-5-4h0v-1-2c-1-1-2-1-3-2h-1 0-1l-1-2h-1v-1c-2 0-2 0-3-1 0-1-2-2-2-3-1 0-1-1-1-1l-6-6c0-1 0 0 1-1l1 1h0v-1h0v-1l-3-2v-1h0c-1-1-3-2-4-4 0 0-1-1 0-1l1-1-6-7-1-1z" class="s"></path><path d="M265 346h1c1 1 2 1 3 1v1l-2 1c-1-1-1-2-2-3z" class="g"></path><path d="M250 338l7 3v2l-1-1h-2l-1-1c-2-1-2-1-3-3z" class="J"></path><path d="M257 341c3 1 5 2 8 5-2 0-2-1-4-1-1-1-2-1-3-2-1 0 0 1-1 0h0v-2z" class="F"></path><path d="M250 338c-2-1-5-3-7-5l1-1 6 4 7 4c2 1 5 1 7 2 1 1 2 1 3 1h1 2v1l1 1c0 1-2 1-2 2-1 0-2 0-3-1h-1 0c-3-3-5-4-8-5l-7-3z" class="Z"></path><path d="M270 344l1 1c0 1-2 1-2 2-1 0-2 0-3-1 0 0 1-1 2-1s1 0 2-1z" class="s"></path><path d="M250 336c2 0 7 1 9 2h0c1-1 2-1 2-1l10 4h0-3l-1 1 1 1h-1c-1 0-2 0-3-1-2-1-5-1-7-2l-7-4z" class="J"></path><defs><linearGradient id="AV" x1="244.745" y1="335.32" x2="245.509" y2="323.116" xlink:href="#B"><stop offset="0" stop-color="#999"></stop><stop offset="1" stop-color="#bcbab9"></stop></linearGradient></defs><path fill="url(#AV)" d="M238 328h0c-1-1-3-2-4-4 0 0-1-1 0-1l1-1c5 5 11 9 18 12l8 3s-1 0-2 1h0c-2-1-7-2-9-2l-6-4c-2-2-4-3-6-4z"></path><path d="M248 343l1-1c1 0 2 0 2 1h1s1 0 1 1h4 0c2 3 5 1 7 3l3 3h2l-1 1c0 1 1 1 2 1l1 1h0v1h-1v2l4 1h-2l1 1c-3 0-6-1-10-2-2-2-4-2-5-4h0v-1-2c-1-1-2-1-3-2h-1 0-1l-1-2h-1v-1c-2 0-2 0-3-1z" class="F"></path><path d="M258 349l1 2 1-1c1 1 3 2 4 2v-1c1 0 2 0 3 1 2 0 2 1 4 1h0v1h-1v2l4 1h-2l1 1c-3 0-6-1-10-2-2-2-4-2-5-4h0v-1-2z" class="s"></path><path d="M263 356c-2-2-4-2-5-4h0c4 2 8 4 12 4l4 1h-2l1 1c-3 0-6-1-10-2z" class="u"></path><path d="M272 340c2 1 7 3 9 3v-1l6 2 1-1 2 9h-1l-1 1c-2 2-4 3-7 3-2 0-5 0-7 1l-4-1v-2h1v-1h0l-1-1c-1 0-2 0-2-1l1-1 2 1-2-3v-1c0-1 2-1 2-2l-1-1v-1h-2l-1-1 1-1h3 0l1-1z" class="AY"></path><path d="M282 349l1-1v2c-1 1-1 1-1 2h-1-4-1 0l6-3z" class="E"></path><path d="M271 341l9 3v1c-1 0-5-1-7-2h-2-1-2l-1-1 1-1h3z" class="s"></path><path d="M271 345c2 0 3 0 5 1h0c-1 3-1 3-3 5h-2l-2-3v-1c0-1 2-1 2-2z" class="B"></path><path d="M288 343l2 9h-1l-1 1-1-1-1-1c-1-1-3 0-4 1 0-1 0-1 1-2v-2l-1 1c-1-1-1-1-2-1v-2h1 1 5 0v-2l1-1z" class="U"></path><path d="M288 343l2 9h-1c0-1 0-1-1-2-1 0-2-1-2-1-1-1-2-2-4-2h-1v-1h1 5 0v-2l1-1z" class="I"></path><path d="M282 352c1-1 3-2 4-1l1 1 1 1c-2 2-4 3-7 3-2 0-5 0-7 1l-4-1v-2h1v-1l6-1h4 1z" class="l"></path><path d="M282 352c1-1 3-2 4-1l1 1-3 2c-1 0-2 0-3 1 0-1 1-2 1-3h-1 1z" class="F"></path><path d="M281 352h1c0 1-1 2-1 3h-4c-3 0-4 0-6-1v-1l6-1h4zm-53-38v-2c1 1 1 1 3 1h0 0l2 2h0c1 3 3 4 5 6h0c2 2 5 4 8 6h3v-1c1 0 1 0 2 1-1-1-1-1-1-2h1c1 0 2-1 2-1l1 1 2 1h0 0l1 2c1 0 1-1 2-1v1h1l3 2c1 0 1 1 2 0h1 1l-1 2c1 0 1 0 1 1h1c1 1 2 1 3 2h1v1h0l-1-1h-1l1 1h0l10 6v1c-2 0-7-2-9-3l-1 1-10-4-8-3c-7-3-13-7-18-12l-6-7-1-1z" class="R"></path><path d="M253 324l1 1 2 1h0 0l1 2h-1 0c-1 0-1 1-2 0-1 0-2 0-3-1s-1-1-1-2h1c1 0 2-1 2-1z" class="L"></path><path d="M250 325h1c2 1 3 2 5 3-1 0-1 1-2 0-1 0-2 0-3-1s-1-1-1-2z" class="Z"></path><path d="M253 334h2c1 1 1 1 2 1l1 1 1-1h2 0l11 5-1 1-10-4-8-3z" class="G"></path><path d="M257 328c1 0 1-1 2-1v1h1l3 2c1 0 1 1 2 0h1 1l-1 2c1 0 1 0 1 1h1c1 1 2 1 3 2h1v1h0l-1-1h-1l1 1h0c-5-2-10-5-15-8h1z" class="H"></path><defs><linearGradient id="AW" x1="245.267" y1="315.691" x2="241.65" y2="331.453" xlink:href="#B"><stop offset="0" stop-color="#121112"></stop><stop offset="1" stop-color="#4b4d4c"></stop></linearGradient></defs><path fill="url(#AW)" d="M228 314v-2c1 1 1 1 3 1h0 0l2 2h0c1 3 3 4 5 6h0c2 2 5 4 8 6h3l7 4c2 1 4 3 5 4h0-2l-1 1-1-1c-1 0-1 0-2-1h-2c-7-3-13-7-18-12l-6-7-1-1z"></path><path d="M246 327h3l7 4c2 1 4 3 5 4h0c-5-2-11-4-15-8z" class="Z"></path><path d="M191 316h5c1 1 1 0 2 0v1l2-1c5 0 10 0 14 2 3 3 8 8 8 12v3c1 2 1 2 0 4v1h0 1v1 1 2l-1 1v1c1 0 1-1 2 0l1 1h-1l2 1c1 0 2 1 2 2v1 3c0 1-1 1-2 2h-1c0-1 0-1-1-1 0-1-1-1-1-1-2 0-3 1-3 2-1 1 0 4 0 6 1 2 2 3 3 5l-1 1h-2c-4-2-6-4-8-7h-2c-2-2-3-3-4-5v-1c-1-1-2-1-3-1h0c-1-2-1-4-2-6h-1l-1 1-1-4c-1 2-1 3-1 5v-2l-1-1h0l-1 1v-2-2l-1 2v-3c0-2 0-3-1-4s0-2-1-3c1-2 1-4 2-5h0v-1-3c1-2 0-3-1-4v-1l-1-1c-1-1-1-2-1-3z" class="D"></path><path d="M205 329c0-2 0-2 1-3 2 0 3-1 4 0s3 2 3 3v1c-1-1-2-2-3-2-2-1-3 0-5 1z" class="e"></path><path d="M206 346c-1-2-1-5-1-7-1-1-1-3 0-4 0-2 1-3 3-4 1-1 1 0 2 0v1h-1l-3 3c0 2 0 4 1 6v3c0 1 0 2-1 2z" class="I"></path><path d="M220 343h2v1c1 0 1-1 2 0l1 1h-1l2 1c1 0 2 1 2 2v1c-1-1-2-2-4-2s-4 2-5 4c-1 1-1 2-1 3-1 1-1 1-2 1v-2l-1-1v-1l-1-1v-2h0v-1l1 1v1-3c2 0 3-1 4-2l1-1z" class="Z"></path><path d="M220 343h2v1c1 0 1-1 2 0l1 1h-1c-2 1-3 1-4 2-1 0-1 0-2-1v2h1v1l-1 1-1-1h-2 0v-3c2 0 3-1 4-2l1-1z" class="I"></path><path d="M207 347h2c0-1 0-4-1-6h1c1 1 1 2 2 4 1-1 2-1 3-1 0 1 1 1 1 2v3-1l-1-1v1h0v2l1 1v1l1 1v2 1c1 1 2 1 2 2h-1 0-1l1 2c1 0 1 0 2 1v1c-4-2-7-7-9-10-1-2-2-4-3-5z" class="K"></path><path d="M215 352l1 1v2 1 1h0c-2-2-2-3-1-5z" class="g"></path><path d="M200 329l3-2c1-2 3-2 5-3l2 2c-1-1-2 0-4 0-1 1-1 1-1 3-2 2-4 4-4 7 0 9 5 17 11 23h-2c-2-2-3-3-4-5v-1c-1-1-2-1-3-1h0c-1-2-1-4-2-6h-1l-1 1-1-4v-4c-1-4 0-7 2-10z" class="U"></path><path d="M198 343v-4c-1-4 0-7 2-10h0c0 3 0 4-1 7 0 1 1 2 1 3s0 3 1 4v3h-1l-1 1-1-4z" class="K"></path><path d="M206 346c1 0 1-1 1-2v-3c-1-2-1-4-1-6l3-3-1 2c1 0 1 1 1 1h2v1c1 1 2 1 3 1v2l1 1h2 1c1 0 2-2 3-3l1-4c1 2 1 2 0 4v1h0 1v1 1 2l-1 1h-2l-1 1c-1 1-2 2-4 2 0-1-1-1-1-2-1 0-2 0-3 1-1-2-1-3-2-4h-1c1 2 1 5 1 6h-2l-1-1z" class="O"></path><path d="M208 334c1 0 1 1 1 1h2v1c1 1 2 1 3 1v2l1 1h2 1c-2 1-3 2-5 2h-1 0c-2-1-4-3-4-5-1-1 0-2 0-3z" class="F"></path><path d="M222 333c1 2 1 2 0 4v1h0 1v1 1 2l-1 1h-2l-1 1c-1 1-2 2-4 2 0-1-1-1-1-2-1 0-2-1-3-1v-1h1 1c2 0 3-1 5-2 1 0 2-2 3-3l1-4z" class="B"></path><path d="M223 339v1 2l-1 1h-2c1-2 1-3 3-4z" class="b"></path><path d="M214 344h3 2c-1 1-2 2-4 2 0-1-1-1-1-2z" class="O"></path><defs><linearGradient id="AX" x1="204.215" y1="318.768" x2="209.532" y2="337.727" xlink:href="#B"><stop offset="0" stop-color="#0d0d0c"></stop><stop offset="1" stop-color="#2d2e2c"></stop></linearGradient></defs><path fill="url(#AX)" d="M191 316h5c1 1 1 0 2 0v1l2-1c5 0 10 0 14 2 3 3 8 8 8 12v3l-1 4c-1 1-2 3-3 3h-1-2l-1-1v-2c-1 0-2 0-3-1v-1h-2s0-1-1-1l1-2h1l1 2h1l1-2v-2-1c0-1-2-2-3-3l-2-2c-2 1-4 1-5 3l-3 2c-2 3-3 6-2 10v4c-1 2-1 3-1 5v-2l-1-1h0l-1 1v-2-2l-1 2v-3c0-2 0-3-1-4s0-2-1-3c1-2 1-4 2-5h0v-1-3c1-2 0-3-1-4v-1l-1-1c-1-1-1-2-1-3z"></path><path d="M196 322h0c2-2 5-3 8-4 0 1-1 1-2 2 0 1-1 1-3 2-1 1-2 2-4 3v-2h1v-1z" class="V"></path><path d="M213 332l1-1 1 1v1c0 1-1 2-2 3-1-1-1-1-2 0h0v-1h-2s0-1-1-1l1-2h1l1 2h1l1-2z" class="M"></path><path d="M208 324c2 0 4 1 6 2 2 2 2 3 2 6-1 0 0 0-1 1v-1l-1-1-1 1v-2-1c0-1-2-2-3-3l-2-2z" class="K"></path><path d="M195 327c1 1 1 2 1 3 1-2 2-3 3-4 1-2 4-5 6-5 3 0 7 2 9 3v2c-2-1-4-2-6-2-2 1-4 1-5 3l-3 2c-2 3-3 6-2 10v4c-1 2-1 3-1 5v-2l-1-1h0l-1 1v-2-2l-1 2v-3c0-2 0-3-1-4s0-2-1-3c1-2 1-4 2-5h0v-1-3l1 1v1z" class="L"></path><path d="M194 325l1 1v1 8c-1 2 0 5 0 7l-1 2v-3c0-2 0-3-1-4s0-2-1-3c1-2 1-4 2-5h0v-1-3z" class="d"></path><defs><linearGradient id="AY" x1="218.241" y1="323.567" x2="209.67" y2="328.425" xlink:href="#B"><stop offset="0" stop-color="#515352"></stop><stop offset="1" stop-color="#747474"></stop></linearGradient></defs><path fill="url(#AY)" d="M191 316h5c1 1 1 0 2 0v1l2-1c5 0 10 0 14 2 3 3 8 8 8 12v3l-1 4c-1 1-2 3-3 3h-1-2l-1-1v-2h0 2c2-2 3-5 2-8 0-2-1-6-3-7-4-3-7-4-11-4-3 1-6 2-8 4h0v1h-1v2 1l-1-1c1-2 0-3-1-4v-1l-1-1c-1-1-1-2-1-3z"></path><path d="M214 339h1c1 0 2-1 3-2 1 0 1 0 2-1 0 0 0 1 1 1-1 1-2 3-3 3h-1-2l-1-1z" class="E"></path><defs><linearGradient id="AZ" x1="192.179" y1="318.581" x2="196.204" y2="319.071" xlink:href="#B"><stop offset="0" stop-color="#291a16"></stop><stop offset="1" stop-color="#212523"></stop></linearGradient></defs><path fill="url(#AZ)" d="M191 316h5c1 1 1 0 2 0v1l2-1c0 2-1 2-2 3 0-1 0-1-1-1l-1 1h1c0 1 0 1-1 1v2 1h-1v2 1l-1-1c1-2 0-3-1-4v-1l-1-1c-1-1-1-2-1-3z"></path><path d="M261 151h2c1 2 1 3 1 4v1h1l1 2 1-1h0c2 1 3 0 5 0-1 1-1 1-1 2h1v3l-1 1h0v1c0 1 0 2 1 3v2 1c-1 0-1 0-1 1h-1v4h0-3 0-1l-1-1v1s0 1-1 1c0 1-1 0-1 1-1-1-2-1-3-2l-1 1c0 2-2 5-3 7v-2h-1l-1-1c-1 1-1 2-1 4l-1 2v2c0 1 0 2-1 3h0c0 1 1 2 1 2l-1 1c1 1 1 1 1 2v2 1 1h-2v1l3 1 1 2h0c1 1 2 1 2 3l1 1h0c1 1 2 1 2 2h1c0 2 0 2 1 3h0l1 1v2h0 1v1c0 1 1 1 0 2v2c0 1 0 1-1 2l1 2v1c0 1 0 1 1 2v1 7 9 4c1 0 1 0 2 1l-2 1h0l-1-1h-2c0 1 0 2 1 2 0 1 1 1 1 2h-1 0v1l-1 1c0 1-1 2-1 3v1c1 1 1 1 2 1v1h0l2 1v3h0c-1 2-2 3-2 5l1 1c0 1 1 3 0 4h0c-1-1-2-2-2-3-1 2 0 4 0 6v1h0v5h0v1c-1 2-1 2-2 3h-1l-2 1c-1 1-1 2-3 3h-1c-1 1-3 1-4 1s-2 0-3-1h-2-4-1-4l-1 1h-2l-1 2c-1 0-1-1-3 0v1h1c-2 2-1 3-2 5 1 2 2 3 3 5 1 1 1 2 2 3v1l2 4-2-2h0 0c-2 0-2 0-3-1v2l-3-6c-2-3-5-5-8-7-1 0-2-1-3-2h0-1-2v2h-2v-1h-1l-2-3v-1c-1-2-2-3-3-5v-1l2 2h0 0l-1-1 1-1-4-11c-1-4-2-7-2-10l-1-3v-9c1-3 1-6 1-9l1-15c0-2 0-8 1-10l1-8c0-2 0-3-1-5h0c-1 0-1 0-2-1 1 0 1-1 1-2s0-4-1-5-1-1-1-2c0-2-1-4-1-6 0 0 1 0 2 1l1-1v-5c1 1 1 1 1 2l1 2h0c1-3 1-7 3-9v-1c1-1 2-3 3-5 0 1 0 1 1 2l1-3h1l3-3v1 3c1-3 2-5 3-8l2-4h1l2-2 1-1h2l3-4 1 1h1c2-2 5-4 7-4h0c1-1 2-1 3-1l2 2 1-1h1v-1l1-1 1 1c1-1 2-1 3-1s2 0 3-1l1 1h2l1-1c2-1 3-1 5-2h0z" class="a"></path><path d="M207 224l2-5 1 1 1-1c0 1-1 2-1 3v1c-1 0-1 1-2 1h-1z" class="i"></path><path d="M218 199l1 1v2l-3 4h-1c1-3 2-5 3-7z" class="s"></path><path d="M220 172h1l1-1h1 3c-3 2-5 3-7 6v-3s-1 0-2 1l3-3z" class="J"></path><path d="M206 286l3 8c1 2 1 4 2 5v2h-2v-1-2c0-1-1-2-1-2-1-2-2-4-2-6l3 6c0-1 0-1-1-2 0-1 0-2-1-2v-1-1c-1-1-1-2-1-3v-1z" class="B"></path><path d="M206 289v1c0 2 1 4 2 6 0 0 1 1 1 2v2h-1l-2-3v-1c-1-2-2-3-3-5v-1l2 2h0 0l-1-1 1-1 1-1z" class="H"></path><path d="M215 206h1l-5 13-1 1-1-1c1-5 4-9 6-13z" class="AG"></path><path d="M225 169c3 0 6 2 8 4v1c-2 0-2-1-3-2-1 0-1-1-2-1-1 1-1 1-2 0h0-3-1l-1 1h-1-1l-1-1 2-1c2-1 3-1 5-1z" class="H"></path><path d="M199 269v-3h1c1 8 3 16 6 23l-1 1-4-11c-1-4-2-7-2-10z" class="f"></path><path d="M219 202l1 1c-1 2-1 3-3 4v2l-1 2-2 6-4 5c0-1 1-2 1-3l5-13 3-4z" class="Ac"></path><path d="M200 233c0-2 0-8 1-10l1 1v3c-1 5-1 9-1 15-1 7-2 14-1 22v2h-1v3l-1-3v-9c1-3 1-6 1-9l1-15z" class="u"></path><path d="M198 266v-3h1l1 1v2h-1v3l-1-3z" class="G"></path><defs><linearGradient id="Aa" x1="227.577" y1="195.044" x2="222.259" y2="192.744" xlink:href="#B"><stop offset="0" stop-color="#707172"></stop><stop offset="1" stop-color="#8a8988"></stop></linearGradient></defs><path fill="url(#Aa)" d="M235 185h1c0 2 0 5-1 6-2 2-4 3-5 4-2 1-3 1-4 2-1 0-2 2-4 2 0 1-1 3-2 4l-1-1v-2l-1-1h0c4-6 10-12 17-14z"></path><path d="M235 185h1c0 2 0 5-1 6-2 2-4 3-5 4-2 1-3 1-4 2-1 0-2 2-4 2l6-6c2-2 4-3 6-4 1-2 1-2 1-4z" class="V"></path><path d="M228 178h0 3 1c0 1 1 1 1 1-1 1-3 2-4 2-3 2-5 4-8 7-7 7-11 17-14 26l-1 4h-1c0-1 1-3 1-4 2-5 3-10 5-14s7-16 11-18c2-1 4-3 6-4z" class="U"></path><path d="M217 170v1h1l1 1h1l-3 3c1-1 2-1 2-1v3c-3 3-4 6-6 10-5 12-9 24-11 37l-1-1 1-8c0-2 0-3-1-5 1-2 1-5 2-7 1-1 1-2 1-3 1 0 1-1 2-1 1-3 1-5 2-7v-1c0-4 4-10 6-13 1-3 2-5 3-8z" class="X"></path><path d="M201 210c1-2 1-5 2-7 1-1 1-2 1-3 1 0 1-1 2-1l-4 16c0-2 0-3-1-5z" class="z"></path><path d="M217 170v1h1l1 1h1l-3 3c-3 5-6 11-9 17v-1c0-4 4-10 6-13 1-3 2-5 3-8z" class="K"></path><defs><linearGradient id="Ab" x1="201.416" y1="254.403" x2="211.388" y2="253.094" xlink:href="#B"><stop offset="0" stop-color="#717273"></stop><stop offset="1" stop-color="#8d8e8d"></stop></linearGradient></defs><path fill="url(#Ab)" d="M207 224h1c1 0 1-1 2-1-1 4-3 8-3 12-1 4-1 7-2 11 0 1 0 1 1 2v4 19h0v9 2h-1c-4-15-4-31-1-46l3-12z"></path><path d="M205 246c0 1 0 1 1 2v4 19h0l-1-4c-2-5-2-15 0-21z" class="c"></path><path d="M210 177h1l3-3v1 3c-2 3-6 9-6 13v1c-1 2-1 4-2 7-1 0-1 1-2 1 0 1 0 2-1 3-1 2-1 5-2 7h0c-1 0-1 0-2-1 1 0 1-1 1-2s0-4-1-5-1-1-1-2c0-2-1-4-1-6 0 0 1 0 2 1l1-1v-5c1 1 1 1 1 2l1 2h0c1-3 1-7 3-9v-1c1-1 2-3 3-5 0 1 0 1 1 2l1-3z" class="a"></path><path d="M208 178c0 1 0 1 1 2l-6 17-1-1v-3c1-3 1-7 3-9v-1c1-1 2-3 3-5z" class="AA"></path><path d="M200 194v-5c1 1 1 1 1 2l1 2h0v3l1 1-1 3c0 2 0 4-1 6v1h-1c0-1 0-4-1-5s-1-1-1-2c0-2-1-4-1-6 0 0 1 0 2 1l1-1z" class="n"></path><path d="M200 194v-5c1 1 1 1 1 2l1 2h0v3l1 1-1 3s0 1-1 1v-4c0-1-1-1-1-1v-2z" class="x"></path><defs><linearGradient id="Ac" x1="203.356" y1="253.536" x2="235.317" y2="262.777" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2c2c"></stop></linearGradient></defs><path fill="url(#Ac)" d="M216 211l1 1c1 0 2 0 3 1h3c0 2-1 4-2 7-3 8-5 15-4 24h-1v-3l-2-1v1c-1 1-1 1-1 2-2 1-2 3-3 5 0 2-1 5-2 7 0 4 0 10 1 14l2 11h0c0 4 1 7 2 10s2 7 4 9c1 1 3 1 4 2h1c1 1 2 3 3 4v1 2c-2-3-5-5-8-7-1 0-2-1-3-2h0-1-2c-1-1-1-3-2-5l-3-8-1-4h1v-2-9h0v-19-4c-1-1-1-1-1-2 1-4 1-7 2-11 0-4 2-8 3-12v-1l4-5 2-6z"></path><path d="M212 293l2 6h0-1-2c-1-1-1-3-2-5 1 1 1 1 2 1l1-2z" class="I"></path><path d="M207 235c0 2 0 3 1 5l-2 12v-4c-1-1-1-1-1-2 1-4 1-7 2-11z" class="O"></path><path d="M210 222l4-5-6 23c-1-2-1-3-1-5 0-4 2-8 3-12v-1z" class="L"></path><path d="M206 271h0c1 7 3 16 6 22l-1 2c-1 0-1 0-2-1l-3-8-1-4h1v-2-9z" class="M"></path><path d="M220 213h3c0 2-1 4-2 7-3 8-5 15-4 24h-1v-3l-2-1h1c0-2 0-2-1-3v-3c0-2 0-4 1-6 0-5 2-11 5-15h0z" class="a"></path><path d="M214 234c0-1 1-1 2-2 0 2-1 3-2 5v-3z" class="L"></path><path d="M220 213l1 2c-1 0-1 1-1 1-1 1-1 1-1 2-1 1-2 4-2 6h1-1c-1 2-2 6-1 8-1 1-2 1-2 2 0-2 0-4 1-6 0-5 2-11 5-15z" class="D"></path><defs><linearGradient id="Ad" x1="238.747" y1="268.799" x2="210.758" y2="282.104" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#222"></stop></linearGradient></defs><path fill="url(#Ad)" d="M211 280h0l-2-11c-1-4-1-10-1-14 1-2 2-5 2-7 1-2 1-4 3-5 0-1 0-1 1-2v-1l2 1v3h1c1 16 8 29 20 39 5 4 10 7 15 10-1 1-3 1-4 1s-2 0-3-1h-2-4-1-4l-1 1h-2l-1 2c-1 0-1-1-3 0v1h1c-2 2-1 3-2 5 1 2 2 3 3 5 1 1 1 2 2 3v1l2 4-2-2h0 0c-2 0-2 0-3-1v2l-3-6v-2-1c-1-1-2-3-3-4h-1c-1-1-3-1-4-2-2-2-3-6-4-9s-2-6-2-10z"></path><path d="M227 278l2 2-2 2-1-1s-1 0-1-1l1-1c1 0 1 0 1-1z" class="G"></path><path d="M216 267l1 3h2 1l-1 2c0 1-1 1-1 2h-1l-1-7z" class="O"></path><path d="M224 277l1-2c1 1 1 2 2 3 0 1 0 1-1 1l-1 1-2 1 1-2v-2z" class="I"></path><path d="M224 277l1-2c1 1 1 2 2 3h-2l-1 1v-2z" class="u"></path><path d="M235 286l7 4v1h-3v1c1 0 3 0 4 1h-4c-1-1-1-1-2-1l1-1h0l-1-1s0-1-1-2c0 0-1 0-2-1l1-1h0z" class="B"></path><path d="M217 274h1 1v3h0c0 2 1 4 1 7h0c0 1 0 2 1 3v2c-1 0-1-1-1-2s-1-1-1-1v-1-1c-1-1-1-4-1-5-1-2-1-4-1-5z" class="D"></path><path d="M216 267c0-1 0-3 1-5h1c1 2 2 5 3 7l-1 1h-1-2l-1-3z" class="G"></path><path d="M221 269c0 1 2 2 2 3l2 3-1 2h-1c-2 0-2 0-3-1l-1 1h0v-3h-1c0-1 1-1 1-2l1-2 1-1z" class="L"></path><path d="M221 269c0 1 2 2 2 3h-1v2c0 1 0 0-1 1 0-1-1-1-1-2l-1-1 1-2 1-1z" class="D"></path><path d="M230 287c1-1 1-1 2-1 1-1 2 0 3 0h0l-1 1c1 1 2 1 2 1 1 1 1 2 1 2l1 1h0l-1 1c1 0 1 0 2 1h-1-4l-1 1c-1 0-2 0-3-1l1-1h0l-3-1v-2l2-2h0z" class="G"></path><path d="M230 287c1-1 1-1 2-1 1-1 2 0 3 0h0l-1 1c1 1 2 1 2 1 1 1 1 2 1 2h-3l-2-2 1-1h0 0-3z" class="O"></path><path d="M231 292c0-1 0-1 1-2h2 3l1 1h0l-1 1c1 0 1 0 2 1h-1-4l-1 1c-1 0-2 0-3-1l1-1h0z" class="I"></path><path d="M231 292l1 1v-1h1c1-1 3-1 5-1l-1 1c1 0 1 0 2 1h-1-4l-1 1c-1 0-2 0-3-1l1-1z" class="L"></path><path d="M219 277l1-1c1 1 1 1 3 1h1v2l-1 2 2-1c0 1 1 1 1 1l1 1v1l5-1c-1 1-3 2-4 3 1 1 1 2 2 2l-2 2v2c-1 0-1-1-1-2h0l-2-1v1h-1c0-1 0-3-1-5-1-1-1-3-2-4h-1v3 1h0c0-3-1-5-1-7z" class="N"></path><path d="M223 281l2-1c0 1 1 1 1 1-1 1-2 2-3 2v-2z" class="K"></path><path d="M225 288c0-1 1-2 2-3h1l-1 1c0 1 0 1 1 2 0 0-1 0-1 1l-2-1z" class="c"></path><path d="M228 285c1 1 1 2 2 2l-2 2v2c-1 0-1-1-1-2h0c0-1 1-1 1-1-1-1-1-1-1-2l1-1h0z" class="H"></path><path d="M211 280h0c0 2 1 4 2 6v-3h0c0-1 1-2 1-2h0c0-2-1-5-1-7h1l1 8 3 10 3 7 1 2h-1c-1-1-3-1-4-2-2-2-3-6-4-9s-2-6-2-10z" class="u"></path><path d="M215 282l3 10v-1c-1-1-3-4-3-6v-3z" class="H"></path><path d="M217 299l-1-7c1 2 2 3 3 5 0 1 1 1 2 2l1 2h-1c-1-1-3-1-4-2z" class="c"></path><path d="M220 284v-1-3h1c1 1 1 3 2 4 1 2 1 4 1 5h1v-1l2 1h0c0 1 0 2 1 2l3 1h0l-1 1c1 1 2 1 3 1h-2l-1 2c-1 0-1-1-3 0v1h1c-2 2-1 3-2 5-2-4-4-8-5-13v-2c-1-1-1-2-1-3z" class="G"></path><path d="M228 291l3 1h0l-1 1c-1 0-2-1-2-2z" class="B"></path><defs><linearGradient id="Ae" x1="218.034" y1="283.404" x2="225.953" y2="289.041" xlink:href="#B"><stop offset="0" stop-color="#454646"></stop><stop offset="1" stop-color="#606262"></stop></linearGradient></defs><path fill="url(#Ae)" d="M220 284v-1-3h1c1 1 1 3 2 4 1 2 1 4 1 5v2c1 1 1 1 1 2 1 1 1 2 1 4h-1v-1c0-1 0-1-1-2-1-2-1-5-3-7h0c-1-1-1-2-1-3z"></path><path d="M261 151h2c1 2 1 3 1 4v1h1l1 2 1-1h0c2 1 3 0 5 0-1 1-1 1-1 2h1v3l-1 1h0v1c0 1 0 2 1 3v2 1c-1 0-1 0-1 1h-1v4h0-3 0-1l-1-1v1s0 1-1 1c0 1-1 0-1 1-1-1-2-1-3-2l-1 1c0 2-2 5-3 7v-2h-1l-1-1c-1 1-1 2-1 4l-1 2v2c0 1 0 2-1 3h0c0 1 1 2 1 2l-1 1c1 1 1 1 1 2v2 1 1h-2v1h-3l-1 1c-1 0-1 1-2 2v2c1 0 1 1 1 2v2h0c-1 1-2 1-2 1-1 0-1 1-2 2l-1 1-1 1c-1 1-1 1-2 1v1l-1-1h0c0-1 0-1 1-2h0v-1-1h0 0c-1-2-1-2-1-3h-1c0-1 0-1-1 0-1 0-2 1-3 1v1l-3 2-1 2h0-1 0c-1-1-1-2-3-2h-3c-1-1-2-1-3-1l-1-1 1-2v-2c2-1 2-2 3-4 1-1 2-3 2-4 2 0 3-2 4-2 1-1 2-1 4-2 1-1 3-2 5-4 1-1 1-4 1-6h0c1-4-1-8-3-11h0v-1c-2-2-5-4-8-4-2 0-3 0-5 1l-2 1h-1v-1l2-4h1l2-2 1-1h2l3-4 1 1h1c2-2 5-4 7-4h0c1-1 2-1 3-1l2 2 1-1h1v-1l1-1 1 1c1-1 2-1 3-1s2 0 3-1l1 1h2l1-1c2-1 3-1 5-2h0z" class="R"></path><path d="M229 202h1s0-1 1-1v-1 4h-2v-2z" class="H"></path><path d="M229 198v4h0v2h2v4c-1 0-1-1-2-2l-1-1h0v-3l-1 1h-1c-1-1-1-1-1-2h1 1l2-3z" class="L"></path><path d="M230 195c1-1 3-2 5-4v2c-2 2-3 4-4 7h0v1c-1 0-1 1-1 1h-1 0v-4c0-1 1-1 1-3z" class="N"></path><path d="M235 197c2 0 3 0 4 1l-1 1v2c1 0 1 1 1 1h2s0 1 1 1c-1 1-1 1-1 2l-1 1c-1 1-2 0-2 1l1 2h-1-2-1c0-1 0-1-1 0-1 0-2 1-3 1v-2h1v-3-1-2c0-2 0-2 1-3l2-2z" class="r"></path><path d="M233 199c1 0 1 0 2 1l-1 1s-1 1-2 1c0-2 0-2 1-3z" class="w"></path><path d="M232 204c1-1 2-1 3-1v2h0l-2 1c0-1 0-1-1-1v-1zm3-7c2 0 3 0 4 1l-1 1c-1 0-2 1-3 0v-2z" class="z"></path><defs><linearGradient id="Af" x1="238.488" y1="207.413" x2="236.611" y2="204.407" xlink:href="#B"><stop offset="0" stop-color="#2a1918"></stop><stop offset="1" stop-color="#252321"></stop></linearGradient></defs><path fill="url(#Af)" d="M235 205h1l1-1c1-1 2-1 4-1v2l-1 1c-1 1-2 0-2 1l1 2h-1-2-1c0-1 0-1-1 0-1 0-2 1-3 1v-2h1v-3c1 0 1 0 1 1l2-1h0z"></path><path d="M235 205h0l1 2h-1-1l-1-1 2-1z" class="V"></path><path d="M230 195c0 2-1 2-1 3l-2 3h-1-1c0 1 0 1 1 2h1l1-1v3h0l1 1c1 1 1 2 2 2h0v2 1l-3 2-1 2h0-1 0c-1-1-1-2-3-2h-3c-1-1-2-1-3-1l-1-1 1-2v-2c2-1 2-2 3-4 1-1 2-3 2-4 2 0 3-2 4-2 1-1 2-1 4-2z" class="Ah"></path><g class="D"><path d="M227 211c-1 0-2 0-3-1l3-3v4z"></path><path d="M222 209c0-1 0-2 1-2 1 1 1 1 1 3h-2v-1z"></path></g><path d="M228 205l1 1c1 1 1 2 2 2h0v2 1l-3 2-1-2v-4l1-2z" class="C"></path><path d="M230 195c0 2-1 2-1 3l-2 3h-1-1c0 1 0 1 1 2h1v2l-1 1c-1 1-2 1-3 1s-1 1-1 2c-1 1-2 1-3 1l-2 2-1-1 1-2v-2c2-1 2-2 3-4 1-1 2-3 2-4 2 0 3-2 4-2 1-1 2-1 4-2z" class="AI"></path><path d="M223 202l2-1c0 1 0 1 1 2h1v2l-1 1c-1-1-2-3-3-4z" class="D"></path><path d="M230 195c0 2-1 2-1 3l-2 3h-1-1l-2 1c-2 3-4 5-6 7v-2c2-1 2-2 3-4 1-1 2-3 2-4 2 0 3-2 4-2 1-1 2-1 4-2z" class="S"></path><path d="M249 180c0 1 0 1 1 1v2c0 1 0 2 1 3h1v2c0 1 0 2-1 3h0c0 1 1 2 1 2l-1 1c1 1 1 1 1 2v2 1 1h-2v1h-3l-1 1c-1 0-1 1-2 2v2c1 0 1 1 1 2v2h0c-1 1-2 1-2 1-1 0-1 1-2 2l-1 1-1 1c-1 1-1 1-2 1v1l-1-1h0c0-1 0-1 1-2h0v-1-1h0 0c-1-2-1-2-1-3h2 1l-1-2c0-1 1 0 2-1l1-1c0-1 0-1 1-2-1 0-1-1-1-1h-2s0-1-1-1v-2l1-1 1 2h0c1-2 1-3 1-5v-1h3c1-2 1-3 2-5 0-1 0-2 1-3l1-4 1-2z" class="AH"></path><path d="M241 195c1 0 1 1 2 1l-1 1v1h1l2-2 2-2 1-1v1c1 0 1 0 2 1h1l-1 1c-1 0-2-1-3 0 0 0-1 0-1 1h0c-1 1-2 1-2 2l-3 2c-1 0-1-1-3 0v-2l1-1 1 2h0c1-2 1-3 1-5z" class="J"></path><path d="M249 180c0 1 0 1 1 1v2c0 1 0 2 1 3h1v2c0 1 0 2-1 3h0c0 1 1 2 1 2l-1 1-3-3v-1c-1-1-1-1-2-1 0-1 0-2 1-3l1-4 1-2z" class="w"></path><path d="M249 180c0 1 0 1 1 1v2l-2 8v-1c-1-1-1-1-2-1 0-1 0-2 1-3l1-4 1-2z" class="Af"></path><path d="M246 197c3 0 4 0 6 2v1h-2v1h-3l-1 1c-1 0-1 1-2 2v2c1 0 1 1 1 2v2h0c-1 1-2 1-2 1-1 0-1 1-2 2l-1 1-1 1c-1 1-1 1-2 1v1l-1-1h0c0-1 0-1 1-2h0v-1-1h0 0c-1-2-1-2-1-3h2 1l-1-2c0-1 1 0 2-1l1-1c0-1 0-1 1-2-1 0-1-1-1-1h-2s0-1-1-1c2-1 2 0 3 0l3-2c0-1 1-1 2-2z" class="n"></path><path d="M246 197c3 0 4 0 6 2v1c-2 0-4 0-6 1 0-1-1-1-1-1-1 0-1 0-1-1s1-1 2-2z" class="z"></path><path d="M248 168v-1h2c0 1 0 2-1 2v3c0 1 0 1-1 2 1 0 1 0 1 1v4 1l-1 2-1 4c-1 1-1 2-1 3-1 2-1 3-2 5h-3l-4 1h0-2l1-1h-1v-1-2c1-1 1-4 1-6h0c1-4-1-8-3-11h1 1l-1-3v-1l1 1h1 0l1-1c1 0 2 0 3 1l1 1h1c2-1 2-2 3-3h0v-1c2-1 2-1 3 0z" class="X"></path><path d="M238 178c1 2 1 3 0 5v-1l-1-4h1z" class="l"></path><path d="M234 171v-1l1 1c0 1 1 2 1 2 1 1 1 1 1 2s1 2 1 3h-1c-1-1-1-3-2-4l-1-3z" class="R"></path><path d="M242 172h1c0 2 0 2-1 3h-1l-2-1h0c1-1 1-2 2-2h1z" class="AC"></path><path d="M240 185c1-1 1-2 1-3s1-2 1-2v-1h1l1 2-1 2h-1v2h-2z" class="U"></path><path d="M240 185h2v-2h1c0 1 0 1 1 1v2l1-1 2 1c-1 1-1 2-1 3-1 2-1 3-2 5h-3l-4 1h0-2l1-1c0-2 2-2 3-3-1-1-1-1-2-1v-1h2c0-1 0-2 1-3v-1z" class="J"></path><path d="M240 185h2v-2h1c0 1 0 1 1 1v2h0-4v-1z" class="l"></path><path d="M237 195l1-2c1 0 2 0 4-1 0-1 0-1 1-1l1 3h-3l-4 1z" class="Ac"></path><path d="M244 186l1-1 2 1c-1 1-1 2-1 3-1 2-1 3-2 5l-1-3h0c0-1 1-2 1-3-1-1 0-1 0-2h0z" class="Ab"></path><path d="M248 168v-1h2c0 1 0 2-1 2v3c0 1 0 1-1 2 1 0 1 0 1 1v4 1l-1 2-1 4-2-1-1 1v-2c-1 0-1 0-1-1l1-2-1-2h1 0c-1-1-1-3-2-4-1 1-1 3-2 4 0-1-1-3-1-4h2 1c1-1 1-1 1-3h-1c2-1 2-2 3-3h0v-1c2-1 2-1 3 0z" class="F"></path><path d="M244 176v-3 1c2 1 2 1 2 3v1l-2-2zm1-8c2-1 2-1 3 0v2h-3v-1-1z" class="X"></path><path d="M246 180c1 1 2 1 2 2l-1 4-2-1c1-2 1-3 1-5z" class="AS"></path><path d="M248 168v-1h2c0 1 0 2-1 2v3c0 1 0 1-1 2 1 0 1 0 1 1v4 1l-1 2c0-1-1-1-2-2h0l-1-1-1-2v-1l2 2v-1c1-1 1-5 2-7v-2z" class="AT"></path><path d="M248 174c1 0 1 0 1 1v4 1l-1 2c0-1-1-1-2-2h0l1-1c0-1 1-2 1-3v-2z" class="Am"></path><path d="M249 154v3h1v1h0l3 1c-1 2-1 3-2 5-1 1-1 1-1 2v1h-2v1c-1-1-1-1-3 0v1h0c-1 1-1 2-3 3h-1l-1-1c-1-1-2-1-3-1l-1 1h0-1l-1-1v1l1 3h-1-1 0v-1c-2-2-5-4-8-4-2 0-3 0-5 1l-2 1h-1v-1l2-4h1l2-2 1-1h2l3-4 1 1h1c2-2 5-4 7-4h0c1-1 2-1 3-1l2 2 1-1h1v-1l1-1 1 1c1-1 2-1 3-1z" class="AT"></path><path d="M238 168c-1 0-1-1-1-2h-1-1l-1-1c1 0 1-1 2-1h1 3c0-1 0-1-1-2h1 1v2l1 1-2 2-1-1v1l-1 1z" class="AW"></path><path d="M240 155l2 2c2 1 3 3 4 5l-1 1h-2c0-1 0-2-1-2l-2-2c0-1-1-1-2-1h-2v-1l1-1h0c1-1 2-1 3-1z" class="L"></path><path d="M242 165v-1h3l1 1-1 3v1h0c-1 1-1 2-3 3h-1l-1-1s0-1-1-2v-1l1-1 2-2z" class="AJ"></path><path d="M240 171s0-1-1-2v-1h4v1h2c-1 1-1 2-3 3h-1l-1-1z" class="AA"></path><path d="M229 160h1c2-2 5-4 7-4l-1 1v1h2c1 0 2 0 2 1-1 0-2 1-3 1l-4 2c-1 0-1 0-1 1v1-1h2c0 1-1 0-1 2h1l-1 2-4-3 1-1c1-1 1-2 2-3h0l-3 2v-1-1z" class="U"></path><path d="M229 160h1c2-2 5-4 7-4l-1 1v1h2c1 0 2 0 2 1-1 0-2 1-3 1l-1-1c-1 0-1 1-2 1v-1h1v-1h-1c-1 0-2 1-2 2l-3 2v-1-1z" class="f"></path><path d="M249 154v3h1v1h0l3 1c-1 2-1 3-2 5-1 1-1 1-1 2v1h-2v1c-1-1-1-1-3 0l1-3v-3c-1-2-2-4-4-5l1-1h1v-1l1-1 1 1c1-1 2-1 3-1z" class="R"></path><path d="M249 164h-2v-2h0l2-1v3z" class="AC"></path><path d="M249 154v3s0 1-1 1c-2 0-3-2-4-3l1-1 1 1c1-1 2-1 3-1z" class="AW"></path><path d="M250 158l3 1c-1 2-1 3-2 5-1 1-1 1-1 2v1h-2c0-1 1-2 1-3v-3h0c0-2 1-2 1-3z" class="Ab"></path><path d="M228 159l1 1v1 1l3-2h0c-1 1-1 2-2 3l-1 1 4 3c1 1 2 2 2 3 1-1 2-1 3-2l1-1v-1l1 1-1 1v1c1 1 1 2 1 2-1-1-2-1-3-1l-1 1h0-1l-1-1v1l1 3h-1-1 0v-1c-2-2-5-4-8-4-2 0-3 0-5 1l-2 1h-1v-1l2-4h1l2-2 1-1h2l3-4z" class="l"></path><path d="M228 159l1 1v1 1l-1 1h0c-1 0-2 0-3 1h0v-1l3-4z" class="F"></path><path d="M222 164c-1 2-2 4-2 5v1l-2 1h-1v-1l2-4h1l2-2z" class="AX"></path><path d="M225 169l-2-1v-1c1-1 2-1 4-1 3 1 5 3 7 5l1 3h-1-1 0v-1c-2-2-5-4-8-4z" class="i"></path><path d="M261 151h2c1 2 1 3 1 4v1h1l1 2 1-1h0c2 1 3 0 5 0-1 1-1 1-1 2h1v3l-1 1h0v1c0 1 0 2 1 3v2 1c-1 0-1 0-1 1h-1v4h0-3 0-1l-1-1v1s0 1-1 1c0 1-1 0-1 1-1-1-2-1-3-2l-1 1c0 2-2 5-3 7v-2h-1l-1-1c-1 1-1 2-1 4l-1 2h-1c-1-1-1-2-1-3v-2c-1 0-1 0-1-1v-1-4c0-1 0-1-1-1 1-1 1-1 1-2v-3c1 0 1-1 1-2v-1c0-1 0-1 1-2 1-2 1-3 2-5l-3-1h0v-1h-1v-3c1 0 2 0 3-1l1 1h2l1-1c2-1 3-1 5-2h0z" class="AJ"></path><path d="M266 163h3 1v1c-1 1-2 1-3 2h-2-1c0-1 1-2 2-3zm-6 0h3l-3 5c-1 0-1 0-1-1h0c-1 1-1 1-2 1l-1 1h0l1-3 2-2 1-1z" class="w"></path><path d="M270 164h1c0 1 0 2 1 3v2 1c-1 0-1 0-1 1h-1-1c-1 0-1-1-2-1h0v-2h-1 1v-1-1c1-1 2-1 3-2z" class="p"></path><path d="M267 157h0c2 1 3 0 5 0-1 1-1 1-1 2h1v3l-1 1h0v1h-1v-1h-1-3v-1c-1 0-2 1-3 1h0-3c1-1 2-3 4-4 1 0 1-1 2-1l1-1z" class="AO"></path><path d="M267 160s0-1 1-1h1l1 1-2 2h-1v-2z" class="a"></path><path d="M271 159h1v3l-1 1h0-1v-3c1 0 1 0 1-1z" class="Ab"></path><path d="M261 170c0-1 1-2 2-2l4 2h0c1 0 1 1 2 1h1v4h0-3 0-1l-1-1v1s0 1-1 1c0 1-1 0-1 1-1-1-2-1-3-2v-2-1l1-2z" class="AI"></path><path d="M267 170c1 0 1 1 2 1h1v4h0-3 0l1-3h1l-2-2z" class="Ac"></path><path d="M260 175v-2-1c1 0 2 1 2 1 2 1 1 2 3 2 0 0 0 1-1 1 0 1-1 0-1 1-1-1-2-1-3-2z" class="U"></path><path d="M256 169h0l1-1c1 0 1 0 2-1h0c0 1 0 1 1 1l-1 2h2l-1 2v1 2l-1 1c0 2-2 5-3 7v-2h-1l-1-1c-1 1-1 2-1 4l-1 2h-1c-1-1-1-2-1-3v-2c2-4 3-8 6-12z" class="C"></path><path d="M259 170h2l-1 2v1 2l-1 1c0 2-2 5-3 7v-2h-1l-1-1c1-3 2-6 4-8 0 0 0-1 1-2z" class="W"></path><path d="M259 170h2l-1 2v1 2l-1 1v-3h-1v-1s0-1 1-2z" class="p"></path><path d="M254 180c1-3 2-6 4-8v1l-3 8-1-1z" class="AJ"></path><path d="M253 159c1 1 3 1 4 2 1 0 1 0 1 1s0 1 1 2l-2 2-1 3c-3 4-4 8-6 12-1 0-1 0-1-1v-1-4c0-1 0-1-1-1 1-1 1-1 1-2v-3c1 0 1-1 1-2v-1c0-1 0-1 1-2 1-2 1-3 2-5z" class="J"></path><path d="M257 161c1 0 1 0 1 1s0 1 1 2l-2 2h-1 0-1c-1 0 0 0-1-1l3-2v-2z" class="i"></path><path d="M249 172h0c1-1 2-2 2-3h0c1 2 1 3 1 4-2 2-2 4-3 6v-4c0-1 0-1-1-1 1-1 1-1 1-2z" class="Ao"></path><path d="M253 159c1 1 3 1 4 2v2c-1-1-1-1-2-1-1 1-2 1-3 3v1h-2c0-1 0-1 1-2 1-2 1-3 2-5z" class="AH"></path><path d="M255 166h1 0 1l-1 3c-3 4-4 8-6 12-1 0-1 0-1-1v-1c1-2 1-4 3-6v-3c1-1 1-1 1-2 1-1 2-1 2-2z" class="AC"></path><path d="M261 151h2c1 2 1 3 1 4v1h1l1 2c-1 0-1 1-2 1-2 1-3 3-4 4l-1 1c-1-1-1-1-1-2s0-1-1-1c-1-1-3-1-4-2l-3-1h0v-1h-1v-3c1 0 2 0 3-1l1 1h2l1-1c2-1 3-1 5-2h0z" class="o"></path><path d="M260 156l-1 1h-1c-1 1-1 1-2 1-1-1-1-1-1-2 1 0 1 0 2 1l1-1v1c1 0 1 0 2-1z" class="i"></path><path d="M264 156h1l1 2c-1 0-1 1-2 1-2 1-3 3-4 4l-1 1c-1-1-1-1-1-2l6-6z" class="X"></path><path d="M261 151h2c1 2 1 3 1 4l-1-1v1l-1 1-2-2s0-1 1-1h1 0l-1-1h0c0 1-1 1-1 1-1 0-2 1-2 1h0c0 1 1 1 2 2h0c-1 1-1 1-2 1v-1l-1-1h-3c-1 1 0 2-2 2h-2 0-1v-3c1 0 2 0 3-1l1 1h2l1-1c2-1 3-1 5-2h0z" class="AW"></path><path d="M249 154c1 0 2 0 3-1l1 1c-2 1-2 2-3 3h0-1v-3z" class="AH"></path><path d="M246 202l1-1h3l3 1 1 2h0c1 1 2 1 2 3l1 1h0c1 1 2 1 2 2h1c0 2 0 2 1 3h0l1 1v2h0 1v1c0 1 1 1 0 2v2c0 1 0 1-1 2l1 2v1c0 1 0 1 1 2v1 7 9 4c1 0 1 0 2 1l-2 1h0l-1-1h-2c0 1 0 2 1 2 0 1 1 1 1 2h-1 0v1l-1 1c0 1-1 2-1 3v1c1 1 1 1 2 1v1h0l2 1v3h0c-1 2-2 3-2 5l1 1c0 1 1 3 0 4h0c-1-1-2-2-2-3-1 2 0 4 0 6v1h0v5h0v1c-1 2-1 2-2 3h-1l-2 1c-1 1-1 2-3 3h-1c-5-3-10-6-15-10-12-10-19-23-20-39-1-9 1-16 4-24 1-3 2-5 2-7 2 0 2 1 3 2h0 1 0l1-2 3-2v-1c1 0 2-1 3-1 1-1 1-1 1 0h1c0 1 0 1 1 3h0 0v1 1h0c-1 1-1 1-1 2h0l1 1v-1c1 0 1 0 2-1l1-1 1-1c1-1 1-2 2-2 0 0 1 0 2-1h0v-2c0-1 0-2-1-2v-2c1-1 1-2 2-2z" class="AI"></path><path d="M247 270h0 3 1l-3 4-1-1v-2-1z" class="K"></path><path d="M231 215l1-1h0c1 1 2 1 3 2 0 2-1 2-2 3l-1 1v-2-1c0-1 0-2-1-2z" class="z"></path><path d="M247 267l2 2h0c1-1 2-1 3 0h0l-1 1h-1-3 0c-1-1-1-1 0-3z" class="G"></path><path d="M231 223l2-2v2c0 1-1 2-1 4h-1c-1 1-1 3-1 5v-6l1-3z" class="O"></path><path d="M245 259l4 2h2 1 0-1c0 1-1 1-1 2h0v1c-3-2-5-3-7-4l2-1z" class="c"></path><path d="M235 216v1c1 1 2 3 1 4v1h-2l-1 1v-2l-2 2c0-1 0-2 1-3l1-1c1-1 2-1 2-3z" class="H"></path><path d="M235 217c1 1 2 3 1 4v1h-2l-1 1v-2c1 0 1 0 1-1 1-1 1-2 1-3z" class="I"></path><path d="M228 217l3-2c0 1 0 2-1 3-1 2-1 3-2 5h0-3v-1c1-3 1-5 2-7h0v1l1 1z" class="G"></path><path d="M228 217l3-2c0 1 0 2-1 3h-1-1 0c-1 0-1 0-1-1h1z" class="B"></path><path d="M228 218h1 1c-1 2-1 3-2 5h0-3v-1h1s0-1 1-1c1-1 1-1 1-3z" class="V"></path><path d="M232 214h1c0-1 1-2 2-2l2 2h0c-1 1-1 1-1 2h0l1 1v-1c1 0 1 0 2-1-1 2-1 3-1 5h0c1 1 1 2 1 3v1l-2-1-1-2c1-1 0-3-1-4v-1c-1-1-2-1-3-2z" class="P"></path><path d="M255 264l4 1 3 1h2c-1 2-2 3-2 5l1 1c0 1 1 3 0 4h0c-1-1-2-2-2-3h0c-2-1-2-2-4-2l1-1 3-3c-1-1-4-2-6-2v-1z" class="C"></path><path d="M262 266h2c-1 2-2 3-2 5l1 1c0 1 1 3 0 4h0c-1-1-2-2-2-3h0v-2c0-2 1-4 1-5z" class="AV"></path><path d="M231 210c1 0 2-1 3-1 1-1 1-1 1 0h1c0 1 0 1 1 3h0 0v1 1l-2-2c-1 0-2 1-2 2h-1 0l-1 1h0l-3 2-1-1v-1l1-2 3-2v-1z" class="AA"></path><path d="M231 210c1 0 2-1 3-1 1-1 1-1 1 0 1 1 1 1 1 2-1 1-2 1-4 1l-1-1v-1z" class="m"></path><defs><linearGradient id="Ag" x1="227.792" y1="231.466" x2="244.919" y2="257.553" xlink:href="#B"><stop offset="0" stop-color="#444745"></stop><stop offset="1" stop-color="#7e8282"></stop></linearGradient></defs><path fill="url(#Ag)" d="M230 232c0-2 0-4 1-5h1v2 2 2c0 2 0 6 1 8h0c1 7 6 13 12 18l-2 1-2-2c-4-3-8-8-10-14 0-2-1-5-1-7v-5z"></path><path d="M230 232c0-2 0-4 1-5h1v2c-1 1-1 1-1 2-1 2-1 4-1 6v-5z" class="u"></path><path d="M225 223h3 0c-1 1-1 2-2 3 0 4 0 7 1 11 0 6 1 11 4 16 1 2 4 4 5 6h-2v3h-1 0c-5-6-8-15-9-22 0-5 1-9 1-13v-4z" class="b"></path><path d="M225 223h3 0c-1 1-1 2-2 3v-1h-1v2h0v-4z" class="D"></path><g class="O"><path d="M231 253c1 2 4 4 5 6h-2v1c-1-1-1-2-1-3l-2-2v-2z"></path><path d="M234 259h2l2 2c1 0 1 0 2 1s3 3 5 4v1h2c-1 2-1 2 0 3v1 2 1h-1l-1-1-1 1h0 0c-2-1-3-1-4-2-2-2-4-4-5-7l-2-3h0 1v-3z"></path></g><path d="M236 259l2 2c0 1 1 1 1 2 1 0 1 0 1 1l-4-3v-2zm9 8h2c-1 2-1 2 0 3v1h-2v-4h0z" class="H"></path><path d="M238 261c1 0 1 0 2 1s3 3 5 4v1h0c-1-1-1-1-2-1v1h-1c-1-1-2-2-2-3s0-1-1-1c0-1-1-1-1-2z" class="B"></path><path d="M240 267c1 1 3 2 4 4 0-1 0-1 1 0h2v2 1h-1l-1-1c-1 0-2-1-3-2l-1-1c0-1-1-2-1-3z" class="G"></path><path d="M234 259h2v2l-1 2h2c1 0 2 3 3 4 0 1 1 2 1 3l1 1c1 1 2 2 3 2l-1 1h0 0c-2-1-3-1-4-2-2-2-4-4-5-7l-2-3h0 1v-3z" class="I"></path><path d="M235 265h1c0 1 1 2 2 2 1 1 1 3 3 4h1c1 1 2 2 3 2l-1 1h0 0c-2-1-3-1-4-2-2-2-4-4-5-7z" class="K"></path><path d="M236 222v-1l1 2 2 1-1 1v5 1h0v5 5h1l2 3h-2 0v1c-1 0-1-1-2-1h0c-1-1-1-3-1-4l-1 2c-1 1-1 0-1 1l-1-3v1h0c-1-2-1-6-1-8v-2-2-2c0-2 1-3 1-4l1-1h2z" class="Y"></path><path d="M233 223l1-1h2c0 2-1 3-2 4l-1 1v1c-1 1-1 2-1 3h0v-2-2c0-2 1-3 1-4z" class="c"></path><path d="M232 233l1-1v1 3h1v-1-1-2l1-1v3h0c0 2 0 4 1 6l-1 2c-1 1-1 0-1 1l-1-3v1h0c-1-2-1-6-1-8z" class="E"></path><path d="M237 223l2 1-1 1v5 1h0v5 5h1l2 3h-2 0v1c-1 0-1-1-2-1h0c-1-1-1-3-1-4-1-2-1-4-1-6h0v-3h0c1-1 1-2 1-3 0-2 0-3 1-5z" class="S"></path><path d="M238 225v5 1h0v5 5-1l-1-1c0-2-1-3-2-5h0 1v-1c1-3 1-6 2-8z" class="n"></path><path d="M236 240c-1-2-1-4-1-6 1 2 2 3 2 5l1 1v1h1l2 3h-2 0v1c-1 0-1-1-2-1h0c-1-1-1-3-1-4z" class="AM"></path><path d="M233 241v-1l1 3c0-1 0 0 1-1l1-2c0 1 0 3 1 4h0c1 0 1 1 2 1v-1h0 2c2 3 5 7 8 9h0v2c0-1-1-1-1-2-1 0-1 0-2-1l-1 2c1 0 2 2 3 2 1 1 2 0 2 0 0 1 1 3 2 4v1h-1-2l-4-2c-6-5-11-11-12-18z" class="F"></path><path d="M237 244h0c1 0 1 1 2 1v-1h0 2c2 3 5 7 8 9h0v2c0-1-1-1-1-2-1 0-1 0-2-1l-1 2c-3-3-6-6-8-10z" class="p"></path><path d="M233 241v-1l1 3c0 1 1 2 2 3 1 2 2 3 3 4 2 2 3 5 6 6 1 1 1 1 1 2 1 1 1 1 2 1 2 0 2 1 3 2h-2l-4-2c-6-5-11-11-12-18z" class="l"></path><path d="M261 250c0 1 0 2 1 2 0 1 1 1 1 2h-1 0v1l-1 1c0 1-1 2-1 3v1c1 1 1 1 2 1v1h0l2 1v3h0-2l-3-1-4-1c-2-1-3-1-3-3h0v-1c-1-1-2-3-2-4 0 0-1 1-2 0-1 0-2-2-3-2l1-2c1 1 1 1 2 1 0 1 1 1 1 2v-2s1 0 2 1c1-1 1-2 1-3h4 1l4-1z" class="T"></path><path d="M254 258h2v1 1c-1-1-2-1-2-2h0z" class="AA"></path><path d="M252 260c1 0 2 1 3 2 1 0 1 0 1 1 1 0 2 0 3 1v1l-4-1c-2-1-3-1-3-3h0v-1z" class="e"></path><path d="M259 261c1 1 2 1 3 1h0l2 1v3h0-2l-3-1v-1c-1-1-2-1-3-1l2-1 1-1z" class="Ae"></path><path d="M259 261c1 1 2 1 3 1h0v2h-3c-1-1-2-1-3-1l2-1 1-1z" class="AS"></path><path d="M261 250c0 1 0 2 1 2 0 1 1 1 1 2h-1 0v1l-1 1c0 1-1 2-1 3v1c1 1 1 1 2 1v1c-1 0-2 0-3-1s-1-3-2-4h-2c0-1 0-1-1-1s-2-1-3-1v-1l-2 1v-2s1 0 2 1c1-1 1-2 1-3h4 1l4-1z" class="K"></path><path d="M257 255c2 0 3 0 5-1v1l-1 1c0 1-1 2-1 3l-1-1v-1-1l-1 1h-1v-2z" class="f"></path><path d="M261 250c0 1 0 2 1 2 0 1 1 1 1 2h-1 0c-2 1-3 1-5 1h-2v-2c1-2 1-1 2-2l4-1zm-23-19l1-1h2l-1 1c1 0 1 1 2 1h1v2l2-1 4 4c2 1 4 2 6 2v4l-5 5h0c1 1 1 2 2 3 0 1 0 2-1 3-1-1-2-1-2-1h0c-3-2-6-6-8-9l-2-3h-1v-5-5h0z" class="M"></path><path d="M238 231l1-1h2l-1 1c1 0 1 1 2 1h1v2c0 2 0 7 2 8l-1 1c-1 0-1-1-2-1v-2c0-1 0-1-1-2 0 1 0 2-1 2h-1v1h-1v-5-5h0z" class="t"></path><path d="M238 231l1-1h2l-1 1c1 0 1 1 2 1h1v2c-1 0-1 0-2 1l-1 1v-1c-1 0-2 1-2 1v-5h0z" class="I"></path><path d="M238 231l1-1h2l-1 1c0 1 1 1 0 2h-1c-1-1-1-1-1-2h0z" class="Z"></path><path d="M245 233l4 4c2 1 4 2 6 2v4l-5 5c-1-2-3-2-4-4h-1v-1-1c-2-1-2-6-2-8l2-1z" class="AI"></path><path d="M258 223h0 3 1 0l1 2v1c0 1 0 1 1 2v1 7 9 4c1 0 1 0 2 1l-2 1h0l-1-1h-2l-4 1h-1-4c-1-1-1-2-2-3h0l5-5v-4c-2 0-4-1-6-2l-4-4-1-2 1-2h1 1s0 1 1 0c1 0 2-1 2-2l1-1 2 1h1 1 0v-3c1 0 2-1 2-1h1z" class="I"></path><path d="M256 232l1 1h0l1 2h0l-1 1h-1v-3-1z" class="b"></path><path d="M252 232c0-1 0-3 1-4 1 1 1 2 2 3-1 1-2 1-3 1h0z" class="i"></path><path d="M255 231c0 2 0 4-1 5h-2v-4c1 0 2 0 3-1z" class="X"></path><path d="M255 239v-2h1c1 0 1 0 1-1l1 2c-1 1 0 1 0 2-1 2-1 2-3 3h0v-4z" class="f"></path><path d="M246 231h3v2h1l1-1h1 0v4h0-1c-2-2-4-3-5-5z" class="N"></path><path d="M244 231l1-2h1v2c1 2 3 3 5 5-1 0-1 0-2 1l-4-4-1-2z" class="U"></path><path d="M258 240c0-1-1-1 0-2 0 1 1 1 2 2-1 1-1 1-1 2l1 1 1-1h0l1-1v2l1 2h1v4c1 0 1 0 2 1l-2 1h0l-1-1h-2l-4 1h-1-4c-1-1-1-2-2-3h0l5-5h0c2-1 2-1 3-3z" class="B"></path><path d="M261 242l1-1v2l1 2h1v4c1 0 1 0 2 1l-2 1h0l-1-1v-1h-1c-1 0-1-1-2-1 1-2 1-4 1-5v-1h0z" class="D"></path><path d="M264 245v4c1 0 1 0 2 1l-2 1h0l-1-1v-1-4h1z" class="J"></path><path d="M258 240c0 2 0 3-1 4h2c0 1 1 1 0 2l-3 3v2h-4c-1-1-1-2-2-3h0l5-5h0c2-1 2-1 3-3z" class="AI"></path><path d="M258 240c0 2 0 3-1 4h0l-3 3c-1 0-3 0-4 1h0l5-5h0c2-1 2-1 3-3z" class="G"></path><path d="M254 247c0-1 0-2 1-3h2l-3 3z" class="b"></path><path d="M258 223h0 3 1 0l1 2v1c0 1 0 1 1 2v1 7 9h-1l-1-2v-2l-1 1v-3c0-1-1-1-1-1-1-2-1-3-1-4l-1 1-1-2h0l-1-1 1-1c-1-2-1-3-2-4v-3c1 0 2-1 2-1h1z" class="B"></path><path d="M262 223h0l1 2v1c0 1 0 1 1 2v1l-2 1v-7z" class="E"></path><path d="M257 233c1-1 2-3 3-4v3l-1 1v1l-1 1-1-2h0z" class="c"></path><path d="M258 223h0v2l1 1c0-1 1-1 1-1h1l-2 2h1v2c-1 1-2 3-3 4l-1-1 1-1c-1-2-1-3-2-4v-3c1 0 2-1 2-1h1z" class="L"></path><path d="M255 224c1 0 2-1 2-1v7 1c-1-2-1-3-2-4v-3z" class="N"></path><path d="M262 230l2-1v7 9h-1l-1-2v-2l-1 1v-3c0-1-1-1-1-1-1-2-1-3-1-4v-1l1 1h2v-3-1z" class="Y"></path><path d="M262 231v12-2l-1 1v-3c0-1-1-1-1-1-1-2-1-3-1-4v-1l1 1h2v-3z" class="b"></path><path d="M246 202l1-1h3l3 1 1 2h0c1 1 2 1 2 3l1 1h0c1 1 2 1 2 2h1c0 2 0 2 1 3h0l1 1v2h0 1v1c0 1 1 1 0 2v2c0 1 0 1-1 2h0-1-3 0-1s-1 1-2 1v3h0-1-1l-2-1-1 1c0 1-1 2-2 2-1 1-1 0-1 0h-1-1l-1 2 1 2-2 1v-2h-1c-1 0-1-1-2-1l1-1h-2l-1 1v-1-5l1-1v-1c0-1 0-2-1-3h0c0-2 0-3 1-5l1-1 1-1c1-1 1-2 2-2 0 0 1 0 2-1h0v-2c0-1 0-2-1-2v-2c1-1 1-2 2-2z" class="y"></path><path d="M250 222c1-1 2-1 3-1 0-1 1-1 1-1 1 0 1-1 2-3 0 2-1 4 0 5h1l1 1h-1s-1 1-2 1v-1h-1-3l-1-1z" class="B"></path><path d="M256 213h0c-2 1-3 1-4 2-2 1-4 1-6 1h0l3-2 1-1h6z" class="D"></path><path d="M241 224v-2h1l1 1s1 1 2 1l-1 1c-1 2-1 3-1 4l-1 3c-1 0-1-1-2-1l1-1h-2l-1 1v-1-5l1-1v-1h1s1 0 1 1z" class="t"></path><path d="M240 223s1 0 1 1l-3 6v-5l1-1v-1h1z" class="z"></path><path d="M250 222l1 1h3 1v1 3h0-1-1l-2-1-1 1c0 1-1 2-2 2-1 1-1 0-1 0h-1-1l-1 2 1 2-2 1v-2h-1l1-3c1 0 1-1 2-2l2-2 3-3z" class="N"></path><path d="M247 225l2 1c-1 1-1 1-2 1h-2l2-2z" class="G"></path><path d="M243 232v-2h1v1l1 2-2 1v-2z" class="D"></path><path d="M250 222l1 1c0 1-1 1-1 1v2h-1 0l-2-1 3-3z" class="O"></path><path d="M255 223v1 3h0-1-1l-2-1-1 1v-2l1-1c1 0 2 0 3-1h1z" class="G"></path><path d="M257 208c1 1 2 1 2 2h1c0 2 0 2 1 3h0l1 1v2h0 1v1c0 1 1 1 0 2v2c0 1 0 1-1 2h0-1-3 0l-1-1h-1c-1-1 0-3 0-5v-4h1v-5z" class="T"></path><path d="M256 218l3-2h0v1c0 1 1 3 1 4h-1c-1-1-1-2-2-2l-1-1z" class="m"></path><path d="M256 217v1l1 1c1 0 1 1 2 2h1c-1 1-2 1-3 1h-1c-1-1 0-3 0-5z" class="v"></path><path d="M257 208c1 1 2 1 2 2h1c0 2 0 2 1 3h0c-1 1-1 1-2 1l-2 2c-1-1 0-2 0-3v-5z" class="Q"></path><path d="M245 210h2 3c1 1 0 2 0 3l-1 1-3 2h0c-1 2-3 5-3 7l-1-1h-1v2c0-1-1-1-1-1h-1c0-1 0-2-1-3h0c0-2 0-3 1-5l1-1 1-1c1-1 1-2 2-2 0 0 1 0 2-1h0z" class="Y"></path><path d="M246 216h0c-1 2-3 5-3 7l-1-1h-1v2c0-1-1-1-1-1v-1c1-2 4-6 6-6z" class="O"></path><path d="M240 214l1 1c1 0 1 0 2-1l1 1c-1 0-2 1-2 2-1 1-2 3-4 3h0c0-2 0-3 1-5l1-1z" class="F"></path><path d="M245 210h2 3c1 1 0 2 0 3l-1 1c-1 0-4-1-5 0v1l-1-1c-1 1-1 1-2 1l-1-1 1-1c1-1 1-2 2-2 0 0 1 0 2-1h0z" class="e"></path><path d="M246 202l1-1h3l3 1 1 2h0c1 1 2 1 2 3l1 1h0v5h-1 0 0-6c0-1 1-2 0-3h-3-2v-2c0-1 0-2-1-2v-2c1-1 1-2 2-2z" class="z"></path><path d="M254 204h0c1 1 2 1 2 3h0c-1 0-3-1-3-1-1-1-1-1-1-2h2zm-8-2c1 0 2 0 3 1v1c-1 1-1 1-2 1s-1 1-2 1h-1v-2c1-1 1-2 2-2z" class="C"></path><path d="M245 208c0-1 1-2 2-2h4l1 1v1c-2 0-2-1-4 0v1l-1 1h-2v-2z" class="AI"></path><path d="M257 208h0v5h-1 0 0-6c0-1 1-2 0-3h-3l1-1h3l6-1z" class="Z"></path><path d="M248 209h3v2h1c1 0 1 0 2-1h1v2l1 1h-6c0-1 1-2 0-3h-3l1-1z" class="M"></path><defs><linearGradient id="Ah" x1="209.954" y1="244.063" x2="263.853" y2="270.954" xlink:href="#B"><stop offset="0" stop-color="#404342"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#Ah)" d="M223 213c2 0 2 1 3 2h0 1c-1 2-1 4-2 7v1 4c0 4-1 8-1 13 1 7 4 16 9 22l2 3c1 3 3 5 5 7 1 1 2 1 4 2h0 0l1-1 1 1h1v-1l1 1 1 1c1 0 1 0 2-1h0 1c2 1 4-3 5-3 2 0 2 1 4 2h0c-1 2 0 4 0 6v1h0v5h0v1c-1 2-1 2-2 3h-1l-2 1c-1 1-1 2-3 3h-1c-5-3-10-6-15-10-12-10-19-23-20-39-1-9 1-16 4-24 1-3 2-5 2-7z"></path><path d="M222 255l2 2h0l1 1c0 1 2 3 3 4v-1c3 5 6 9 10 13 2 2 4 4 6 5s5 3 6 4c-1 1-1 1-2 1l1 2-1-1v-1l-3-2-1-1c-1-1-3-2-4-3l-1 1c1 1 2 1 2 2 3 1 5 3 6 5v1c-7-5-14-10-19-17-3-4-4-8-6-11l-1-1s-1-1-1-2l1-1 1 2v-2z" class="e"></path><path d="M222 255l2 2h0l1 1c0 1 2 3 3 4 1 3 3 5 4 8v1h-1c-1-1-2-2-3-4s-3-4-4-6c-1-1-1-2-2-2l-1-1s-1-1-1-2l1-1 1 2v-2z" class="f"></path><path d="M228 261c3 5 6 9 10 13 2 2 4 4 6 5s5 3 6 4c-1 1-1 1-2 1l1 2-1-1v-1l-3-2-1-1c-1-1-3-2-4-3l-1 1h-1c-2-2-6-5-7-8h1v-1c-1-3-3-5-4-8v-1z" class="M"></path><path d="M223 213c2 0 2 1 3 2h0 1c-1 2-1 4-2 7v1 4c0 4-1 8-1 13 1 7 4 16 9 22l2 3c1 3 3 5 5 7l-1 1-1 1c-4-4-7-8-10-13v1c-1-1-3-3-3-4l-1-1h0l-2-2h0c-1-3-1-5-1-8l-1-8 1 1c1-1 0-4 0-4 0-3 1-6 1-9 0-1 0-3 1-4l-1-1c0-1 0-2-1-2 1-3 2-5 2-7z" class="a"></path><path d="M223 213c2 0 2 1 3 2h0c-2 2-2 5-3 8l-1-1c0-1 0-2-1-2 1-3 2-5 2-7z" class="u"></path><path d="M222 255h0c-1-3-1-5-1-8l-1-8 1 1c1 5 2 9 4 14 1 2 3 5 3 7v1c-1-1-3-3-3-4l-1-1h0l-2-2z" class="c"></path><path d="M257 271c2 0 2 1 4 2h0c-1 2 0 4 0 6v1h0v5h0v1c-1 2-1 2-2 3h-1l-2 1h0c-2-1-4-1-5-3l-2-1-1-2c1 0 1 0 2-1-1-1-4-3-6-4s-4-3-6-5l1-1 1-1c1 1 2 1 4 2h0 0l1-1 1 1h1v-1l1 1 1 1c1 0 1 0 2-1h0 1c2 1 4-3 5-3z" class="C"></path><path d="M240 272c1 1 2 1 4 2h0-1l-1 1c-1 0-2-1-3-2l1-1z" class="I"></path><path d="M249 277c2 1 3 3 5 4l1 1c-3 0-6-2-9-3l-1-1c1-1 2 0 4-1z" class="B"></path><path d="M249 286l-1-2c1 0 1 0 2-1l8 6-2 1h0c-2-1-4-1-5-3l-2-1z" class="J"></path><path d="M247 273l1 1 1 1c1 1 2 2 3 2l1 1 2 1c1 1 2 2 3 2l-1 1c1 2 2 2 4 3v1h-2l-1-1-3-3-1-1c-2-1-3-3-5-4l-5-3 1-1 1 1h1v-1z" class="t"></path><path d="M257 271c2 0 2 1 4 2h0c-1 2 0 4 0 6v1h0v5h0c-2-1-3-1-4-3l1-1c-1 0-2-1-3-2l-2-1-1-1c-1 0-2-1-3-2 1 0 1 0 2-1h0 1c2 1 4-3 5-3z" class="G"></path><path d="M253 278l2-2h1v2c0 1-1 1-1 1l-2-1z" class="B"></path><path d="M249 275c1 0 1 0 2-1h0c1 1 2 1 2 3h-1c-1 0-2-1-3-2z" class="D"></path><path d="M255 279s1 0 1-1c0 1 0 1 1 2v-2l1 1h0l1-1c0 1 1 1 2 1v1h0v5h0c-2-1-3-1-4-3l1-1c-1 0-2-1-3-2z" class="Y"></path><path d="M255 279s1 0 1-1c0 1 0 1 1 2v-2l1 1h0c1 1 2 2 2 3l-1 1-1-2c-1 0-2-1-3-2z" class="H"></path></svg>