<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="133 102 764 816"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#d3d2d2}.C{fill:#bfbebe}.D{fill:#aaa9a9}.E{fill:#a8a7a7}.F{fill:#ecebeb}.G{fill:#dfdede}.H{fill:#cccbcb}.I{fill:#918f90}.J{fill:#a2a0a0}.K{fill:#b4b1b0}.L{fill:#e5e4e4}.M{fill:#c8c5c4}.N{fill:#6c6b6c}.O{fill:#eeedee}.P{fill:#848283}.Q{fill:#7a797a}.R{fill:#5e5d5e}.S{fill:#4e4d4e}.T{fill:#2a2929}.U{fill:#3d3c3c}.V{fill:#f5f5f3}.W{fill:#353434}.X{fill:#df2626}</style><path d="M462 430l3-3h2 1 0v1l-1 1-3 3-1 1h-1 0v-3h0z" class="F"></path><path d="M634 330h1l6 10-1 1c-1-1-2-2-3-4s-3-4-3-7z" class="G"></path><path d="M738 414c4-2 9-5 13-6l-8 7h-5v-1z" class="C"></path><path d="M462 430h0v3h0 1c-2 4-7 8-10 10 1-3 3-5 5-7l4-6z" class="G"></path><path d="M611 378c1 2 4 4 5 6 1 4 4 5 5 8l-5-5-5-5h1l-1-4z" class="E"></path><path d="M617 735c-1 1-1 2-2 3s-3 2-4 3c-2 1-4 2-6 2h-1l1-1 3-3c3 0 7-3 9-4zM232 286c-7-3-14-8-21-12h0l15 6c1 2 1 3 3 4 2 0 2 1 3 2z" class="K"></path><path d="M626 709l10-6c2-1 4-3 5-3-4 4-10 8-15 12v-1-2zm-15-327c-5-4-8-10-13-15v-1c4 4 9 8 13 12l1 4h-1z" class="I"></path><path d="M797 278l18-9v1l-20 12h0c1-1 2-2 2-4z" class="J"></path><path d="M456 783l2 1c2 1 3 2 5 3-2 1-3 1-4 1h-2c-1 0-5-3-7-4h2c2 0 2 0 4-1z" class="D"></path><path d="M417 310l11-14h1c-6 7-12 14-16 22h-1l-1-1 6-7z" class="I"></path><path d="M384 252l3-4c0 3-2 6-3 8l-3 4c-2 1-3 2-5 2l8-10z" class="G"></path><path d="M592 429c-2-3-4-7-7-9l-1-1h0c4 3 8 5 11 8l3 4h-4l-2-2z" class="E"></path><path d="M245 321c-5-3-11-5-16-8h0l20 7c1 0 3 1 4 2v1c-2-1-4-1-6-1l-2-1z" class="J"></path><path d="M461 781c2 1 8 5 10 5l1 1h-1c-2 1-4-1-6-1-1-1-1-1-1 0l-1 1c-2-1-3-2-5-3v-1l1 1v-1-1c1 1 2 2 4 2 0-1-1-2-2-3z" class="B"></path><path d="M617 735l13-8 1 1-19 13h-1c1-1 3-2 4-3s1-2 2-3z" class="D"></path><path d="M428 340l10-11c1-2 3-5 5-6 0 2-4 6-6 8l-7 11c-1 1-2 3-3 4h0c1-2 1-3 1-6z" class="I"></path><path d="M466 456h1c4-2 8-8 12-12v1l-9 13c-1 0-2 2-3 2v-3l-2 2h-1c1-1 1-2 2-3z" class="B"></path><path d="M205 222l-16-9h0c2-1 7 3 9 3 4 1 7 2 10 4 0 0 0 1-1 1l-2 1z" class="K"></path><path d="M498 459l7-9c2-2 3-4 5-5h0l-14 21c0-1 1-2 1-4 1-1 1-2 1-3z" class="I"></path><path d="M298 403c4 2 8 5 12 6h1l1 3-9-3-4-1 1-2-2-3z" class="B"></path><path d="M729 428c2-1 4-2 7-2l-5 4c-2 0-2 1-4 1-2 2-3 3-6 3-1 0-1-1-1-1 3-2 6-4 9-5z" class="E"></path><path d="M564 821c1-1 3-3 5-4l17-10h1 0c-4 3-7 7-11 10 1-1 1-1 0-2-3-1-8 6-12 6z" class="M"></path><path d="M713 473l17-9v1l-17 12-1-2 1-2z" class="D"></path><path d="M435 772l12 6 9 5c-2 1-2 1-4 1h-2l-15-12zm117-284l14 15-2 2 1 1-1 1-6-8c-1-4-5-7-6-11z" class="Q"></path><path d="M392 659l-7-7c3 0 7 2 11 3l2 2 2 2v1c-3-1-5-1-8-1zm246-369l-18-25 1-1 15 18c0 3 1 4 3 7l-1 1zM426 748l-13-8c-3-2-6-3-7-5 2 1 5 2 7 4 4 2 8 3 12 6h0v1c1 0 2 1 3 2h0-1-1z" class="I"></path><path d="M599 423c-3-3-6-8-8-12v-1l1 1c2 4 5 6 8 10l9 9h-2l-8-7z" class="H"></path><path d="M497 515l2-1c-3 5-6 10-10 14-1 2-2 4-4 5 0-1 1-1 1-2l2-4h0c0-1 3-4 3-5l6-7z" class="D"></path><path d="M387 332v-1c1-1 2-3 3-3l8-7c2-2 5-7 8-8 0 2-2 3-4 5l1 1c-3 3-6 6-9 8-2 1-4 4-7 5z" class="B"></path><path d="M486 500c-1 2-1 3-2 5l1 1c-2 5-7 9-10 12l-1-1c2-3 5-7 7-11 1-2 3-4 5-6z" class="C"></path><path d="M487 537c2-1 4-3 5-5 2-4 6-9 9-11l-3 6c-1 1-1 3-2 5h1l-2 4c-1 0-1-1-2-1 1-1 2-2 2-3-2 0-5 4-7 6l-1-1z" class="M"></path><path d="M650 632l19-9c-1 2-3 2-4 3-3 2-6 5-9 7-2 0-4 1-7 1h-1c1-1 2-1 2-2z" class="J"></path><path d="M490 513c2-3 5-8 8-9 2-2 5-6 7-8v1l-3 4c-1 2-3 7-6 8-2 0-6 6-8 8 0-2 0-3 2-4z" class="M"></path><path d="M603 381c-4-6-8-12-13-17h1c1 1 3 3 4 5l10 10 6 6c-1 1-1 2-2 3l-6-7z" class="Q"></path><path d="M371 625c2 0 2 0 3 1l4 2h4v1c3 2 7 3 10 6v2l-3-1-18-11z" class="I"></path><path d="M542 516l-3-6-7-12v-1c5 6 10 13 14 20v2 1h-1c0-2-1-3-2-4h-1z" class="E"></path><path d="M740 407c6-4 13-6 19-9 2-1 5-3 7-3-1 2-21 13-24 14l2-2h0-4 0z" class="P"></path><path d="M771 319l21-6h0l-14 6c-3 1-7 3-10 5h-2c-1 0-2 0-3-1l8-4z" class="I"></path><path d="M644 305c-2-1-4-3-5-6v-1c2 3 6 7 9 9 2 2 5 4 8 5l1 4h-3l-7-7c0-1-1-2-2-3l-1-1z" class="P"></path><path d="M634 330c-3-6-9-13-13-19v-1l10 10 4 4c-1 2-1 2 0 4l2 3-2-1h-1zm14 293c-1 1-4 3-6 4s-5 2-7 4-4 3-7 3c-3 1-6 4-9 5v-1c9-6 19-12 29-15z" class="E"></path><path d="M693 512l23-9c-3 3-6 5-8 7-3 0-5 1-7 2l-11 2c1-1 2-2 3-2z" class="C"></path><path d="M642 265l-15-18 1-1 16 16c0 2-1 2 0 4l-2-1z" class="D"></path><path d="M814 202c2 0 5-2 7-2l12-4c-1 3-3 5-6 6h0c-2-1-9 0-11 1h-1l-4 1 3-2z" class="Q"></path><path d="M448 777c2 0 4 1 5 1h1l2-1c1 2 3 3 5 4 1 1 2 2 2 3-2 0-3-1-4-2v1 1l-1-1v1l-2-1-9-5 1-1z" class="H"></path><path d="M215 257l15 2c3 1 5 2 7 5-1 0-2 0-4-1-2 0-5-1-8-1-1 0-2 0-3-1-2-1-3-2-4-3-1 0-2 0-3-1z" class="B"></path><path d="M489 501c0 1 0 2-1 3l-1 1v1c-1 0-1 1-2 2l-1 1c2-1 3-3 5-4h0l-6 7c-3 4-5 7-8 9l-3-5 2 1 1 1c3-3 8-7 10-12l4-5z" class="F"></path><path d="M672 576c2-1 4-1 6-1l-18 14h-1c0-2 5-6 7-8 2-1 5-3 6-5z" class="I"></path><path d="M425 745l6 2 10 5c1 1 3 2 4 3 0 0 0 1 1 2l-2-1c-4-1-6-4-10-4h0c-2-1-3-1-5-1l-3-3h1 1 0c-1-1-2-2-3-2v-1h0z" class="P"></path><path d="M349 570c2 0 3 2 5 2 3 2 5 2 7 4 1 1 2 1 2 1 3 2 7 5 8 8v1c-8-4-15-10-22-16z" class="J"></path><path d="M417 376c1 1 1 3 0 4 0 1-1 3-2 4l-1 1c-2 1-3 3-4 4l-2-6h1c2-3 5-5 8-7z" class="I"></path><path d="M409 383l1 2c0-1 1 0 1-1 1-1 2-1 3-2l1 2-1 1c-2 1-3 3-4 4l-2-6h1z" class="K"></path><path d="M403 319l9-9v1c2 0 4-4 6-5l-2 3c-2 3-5 5-8 7-1 0-2 2-3 3l-7 7c-1 0-2 1-3 2l-1 1-1 1-2 1-4 3-1-1 1-1c3-1 5-4 7-5 3-2 6-5 9-8z" class="C"></path><path d="M464 438l12-12 6-8c1-1 2-3 3-3 0 1-6 7-7 9l-13 19h-1c0-1 0-2-1-3l1-2z" class="N"></path><path d="M497 510l20-20h1l-19 24-2 1c1-2 1-2 1-3l-1-2z" class="I"></path><path d="M393 265l21-21v1l-6 7c-3 3-9 14-12 16l-1-1-2 1v-3z" class="D"></path><path d="M487 537l1 1c2-2 5-6 7-6 0 1-1 2-2 3 1 0 1 1 2 1-1 1-1 2-1 3-2 2-4 4-6 5l-1 2-3-6 3-3z" class="G"></path><path d="M488 544v-2c1-2 3-5 5-7 1 0 1 1 2 1-1 1-1 2-1 3-2 2-4 4-6 5z" class="B"></path><path d="M406 375l16-21c0 2 0 3-1 5v2c-1 2-3 4-4 6 0 1-1 2-2 3-1 0-3 1-4 3-1 1-3 2-5 2z" class="L"></path><path d="M601 407l-17-24v-1l19 20c-2 2-1 3-2 5z" class="N"></path><path d="M653 621c2 0 15-7 17-8 3-2 6-3 8-4l-16 11h-1 0c-4 2-7 3-11 4l3-3z" class="K"></path><path d="M653 246l-14-16h0c5 5 11 10 18 14 4 3 8 5 12 8-4-1-6-3-9-4-2-1-4-3-5-3s-1 1-2 1z" class="I"></path><path d="M649 646c2 0 2-1 4-1-1 2-5 4-7 6-6 5-11 11-18 15-1 0-1 0-2 1h0l-1-1c5-3 7-7 12-11 2-2 5-2 7-4h1c1-1 1 0 1-1 1-3 1-3 3-4z" class="C"></path><path d="M371 625l-18-11h1c3 2 7 3 11 5l14 6 3 3h-4l-4-2c-1-1-1-1-3-1z" class="D"></path><path d="M290 404l5 2 4 2 4 1c-2 0-3 0-5 1h-2l3 2-1 2-8-4-2-1-2-2-3-2h4c1 1 1 1 2 1l2 1h1c1 1 1 1 2 1v-1c-1-1-2-1-4-2v-1z" class="G"></path><path d="M286 407c4 0 7 2 10 3l3 2-1 2-8-4-2-1-2-2zm112 250h3c2 3 2 4 3 8 0 1 1 3 2 5v1l-14-12c3 0 5 0 8 1v-1l-2-2z" class="D"></path><path d="M583 437c1 0 2 1 2 2l4 4c1 2 3 3 5 5 1 2 2 3 2 5 1 1 1 2 2 3 1 2 3 4 4 6v1l-2-2-5-5c-5-6-10-13-12-19z" class="O"></path><path d="M349 570l-18-12h1c5 3 12 6 18 9 2 2 6 4 9 5 1 1 2 2 3 4 1 0 1 1 1 1s-1 0-2-1c-2-2-4-2-7-4-2 0-3-2-5-2z" class="D"></path><path d="M322 513h3c1 1 0 1 1 1 3 0 7 3 10 4 2 3 5 7 7 9h-2c-6-5-14-9-19-14z" class="E"></path><path d="M447 405c-1 2-1 3-3 4v2c-1 1-3 2-4 4h0c-2 3-4 7-6 10l-5 4-1-2c2-5 6-9 9-12 3-4 6-7 10-10z" class="J"></path><defs><linearGradient id="A" x1="650.86" y1="640.435" x2="635.121" y2="638.739" xlink:href="#B"><stop offset="0" stop-color="#838383"></stop><stop offset="1" stop-color="#a4a0a1"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M649 634c3 0 5-1 7-1-6 4-12 10-18 14v-1c-2 0-3-1-5-1 6-3 11-7 16-11z"></path><path d="M523 507c2-3 4-7 7-9 0 2-4 5-4 7s15 19 18 22h0l5 6c-2 0-2 0-4 1-4-5-6-11-10-16-1-2-2-3-3-5l-1 1 3 5h0v1c-3-4-5-11-9-14l-1 2-1-1z" class="P"></path><path d="M445 799l-17-11h1l35 15 1 1c-1 1-7-2-9-3l-1 1-1-1h-4c-2-1-3-2-5-2zm2-394l21-23h0l-16 22c-2 1-4 3-6 5s-3 4-6 6h0c1-2 3-3 4-4v-2c2-1 2-2 3-4zm368-185l22-10h0c-5 4-10 7-15 10-2 1-4 2-5 3-3 0-6 1-9 1l7-4z" class="E"></path><path d="M738 407h2 0 4 0l-2 2-12 7-3 1h-4 0c1-1 1-2 2-2 2-1 3-1 4-3h0s8-4 9-5z" class="N"></path><path d="M300 442l-20-15c7 3 13 5 21 7-2 3-2 4-1 7v1z" class="K"></path><path d="M629 679l26-14v1l-19 12c-1 2-4 4-6 5l-1-1c-2 1-3 2-4 1l-2 1 6-5zm-205 59h0c-3-1-5-3-7-4l-15-11h0l19 9-1 1 12 6c-2 0-3 0-4-1h-1l-1 1-2-1zm31 75l-11-9 27 13-1 1v-1h-5-2c-2-1-5-4-8-4z" class="E"></path><path d="M390 255c1-4 6-9 9-12l7-9 1 1c-5 7-10 14-14 21-2 3-3 7-5 9l-2 2h-1c2-2 3-4 5-6l-1-1c-1-2 0-3 1-5h0z" class="K"></path><path d="M765 332l12-5c-5 6-13 12-20 17v-1c1-3 2-7 5-8l3-3z" class="J"></path><path d="M317 500c-6-2-12-7-18-10h0c3 0 6 2 8 3 5 2 10 4 15 5 2 1 5 2 7 2l2 3-4-1c-2 0-3-1-5 0-1-1-3-2-5-2z" class="E"></path><path d="M585 800l23-12h0l-18 13c-2 1-5 4-7 4s-3 0-4 1l-4 1c-1 1-2 1-3 1 2-1 6-4 8-5s3-1 5-3z" class="P"></path><path d="M435 380l12-15v1l-6 8c-1 2-3 4-3 5-1 3-3 6-5 7l-11 12c-1 1-2 3-4 3h-1c5-3 7-7 10-11l8-10z" class="C"></path><path d="M284 421c4 1 8 3 12 3 2 1 7 5 8 5l8 5h-3c-3 0-5-1-7-2-6-3-13-7-18-11z" class="M"></path><path d="M795 266h0c3-1 5-3 8-4v-1l-24 22-1 1c-2 2-4 3-7 4 0-1 1-1 2-1 7-5 10-10 15-16 2-2 4-4 7-5zm-581-7c3 1 5 3 8 4l10 4h0c1 2 3 2 4 3v1c-2 1-2 2-3 4h0l-19-16zm490 218c1-2 7-3 9-4l-1 2 1 2-11 8h-3l-1-1h-1l-1-1c1-1 5-4 6-4l2-2h0z" class="J"></path><path d="M702 479c1 0 1 0 2 1l-6 4h-1l-1-1c1-1 5-4 6-4z" class="B"></path><path d="M704 477c1-2 7-3 9-4l-1 2c-1 1-7 5-8 5-1-1-1-1-2-1l2-2h0z" class="H"></path><path d="M636 282l6 6c0 3 2 5 4 7h0l10 14h-2c-1-2-4-4-6-6-3-4-7-8-10-13l1-1c-2-3-3-4-3-7z" class="J"></path><path d="M707 464l31-20 1 1-15 12c-3 2-5 4-8 6v-1c-1 0-2 0-3 1h-1s-1 0-1-1c-1 1-2 2-4 2z" class="D"></path><path d="M625 373c-1 0-1 0-1-1-5-8-10-17-16-25v-1c6 7 11 15 18 21l2 3c0 2 0 3 2 5-2 0-4-1-5-2z" class="R"></path><defs><linearGradient id="C" x1="754.88" y1="377.075" x2="758.561" y2="362.164" xlink:href="#B"><stop offset="0" stop-color="#949495"></stop><stop offset="1" stop-color="#b4b1b1"></stop></linearGradient></defs><path fill="url(#C)" d="M751 370c8-3 16-8 24-11-8 7-18 12-26 18-1-1-2-1-3-1 1-1 2-2 2-3h0 0l3-3z"></path><path d="M464 794c4 2 11 5 13 9-1 1-1 1-3 1h0c-1 0-2 0-2-1h-5c1 1 2 1 3 2 2-1 3-1 5 0l2 2h1c0 1 1 1 1 2h0c-2-1-5-3-7-3h0c-2 0-4-2-7-2l-1-1c1 0 1-1 1-1l1-1c0-1-1-3-2-5h0v-2z" class="O"></path><path d="M744 384l29-14h0l-20 14-1-1-1 1h-1c-3 0-6 2-9 3l3-3z" class="I"></path><path d="M544 527l4 4c1 0 1 1 1 1l2 1c1 0 1 0 2 1-2-2-3-4-4-7l11 14c2 3 4 5 7 7 2 3 4 4 6 6v2l-14-12-10-11-5-6z" class="F"></path><path d="M440 415c3-2 4-4 6-6s4-4 6-5l-2 4-11 15c-1 1-2 1-4 2h-1c2-3 4-7 6-10z" class="R"></path><defs><linearGradient id="D" x1="278.552" y1="408.598" x2="275.948" y2="398.902" xlink:href="#B"><stop offset="0" stop-color="#868283"></stop><stop offset="1" stop-color="#a5a6a6"></stop></linearGradient></defs><path fill="url(#D)" d="M276 405c-2 0-14-8-16-9h0c2-1 4 1 6 2l17 7 3 2 2 2 2 1v1h-1-3c-3-1-7-4-10-6z"></path><path d="M276 405c3 0 5 2 7 3s4 1 5 1l2 1v1h-1-3c-3-1-7-4-10-6z" class="N"></path><path d="M346 541c-8-5-17-11-25-17l23 9c1 1 1 2 2 2l-1 1h1c-1 1-4 0-6 0 1 1 2 1 3 2s3 2 3 3h0zm72 168c-4-3-8-6-11-9l-15-10h0l8 4h0 1l6 3c1 1 2 2 4 3 4 2 12 8 13 12l-6-3z" class="E"></path><path d="M542 516h1c1 1 2 2 2 4h1v-1-2l1 2c1 0 3 1 4 0 1 2 3 6 3 8h-1l1 3 1 3c2 3 6 6 5 8l-11-14s-2-2-2-3l-5-8z" class="D"></path><path d="M551 525l2 2 1 3 1 3-3-3v-1c-1-2-2-2-1-4z" class="E"></path><path d="M547 519c1 0 3 1 4 0 1 2 3 6 3 8h-1l-2-2c-2-2-3-4-4-6z" class="F"></path><path d="M425 421h1l25-32v1c-7 11-15 23-24 33l-2-2zm283 30c4-1 9-3 13-4 5-2 9-5 14-8l1 1c-5 3-9 7-14 10h-1-1l-6 3h-1 0l1-2c-2 1-4 1-6 2s-5 3-7 3v-1l-2 1v-1l2-1c2-1 4-2 7-3z" class="C"></path><path d="M344 533h2c1 2 5 4 6 6-1 0-3 0-4-1l-1 1c2 1 3 2 5 3 2 2 5 3 7 5v1h1 1c3 1 5 3 8 4 1 0 2 1 3 2-2 0-4-1-6-2-1-1-1-1-2-1l-1-1h-1c-6-1-12-5-16-9h0c0-1-2-2-3-3s-2-1-3-2c2 0 5 1 6 0h-1l1-1c-1 0-1-1-2-2zm357-44l27-11h1l-6 3-13 8c-4 2-9 5-13 8 0-1 1-1 2-2-2 0-2 0-3 1l-1-1c2-1 3-3 4-5h0c1 0 2 0 2-1z" class="J"></path><path d="M616 379l-18-28v-1l21 24 2 4-1 1c-1-1-2-2-3-4-3-5-7-8-10-13h0c-1 1 1 4 2 5 2 3 7 9 7 12z" class="C"></path><path d="M324 488l-27-17 1-1 23 11c1 1 3 2 4 3l5 5-1 1-5-2z" class="J"></path><defs><linearGradient id="E" x1="464.724" y1="793.762" x2="471.015" y2="787.515" xlink:href="#B"><stop offset="0" stop-color="#b4b2b3"></stop><stop offset="1" stop-color="#eeeded"></stop></linearGradient></defs><path fill="url(#E)" d="M463 787l1-1c0-1 0-1 1 0 2 0 4 2 6 1h1l4 10c-6-2-13-5-19-9h2c1 0 2 0 4-1z"></path><path d="M785 293c3-2 6-2 10-4l14-6v1l-24 16v-2l-1-1-3 1-1-1c1 0 2-1 3-2v-1c2-1 0 1 1 0l1-1z" class="E"></path><path d="M429 751c2 0 3 0 5 1h0c4 0 6 3 10 4l2 1c5 3 13 6 17 11v1l-34-18z" class="Q"></path><path d="M618 693l25-13v1c-9 6-18 12-26 19v-2c-2 1-3 1-4 2l2-2v-1c-2 1-5 3-8 3 4-2 8-5 11-7z" class="E"></path><path d="M615 697c0-1 1-2 3-2h0l1 1-2 2c-2 1-3 1-4 2l2-2v-1z" class="M"></path><path d="M223 239c-11-4-22-12-32-18h0l19 7 9 4c1 3 4 3 6 5-3 0-4-1-6-2 0 1 1 1 2 2s2 1 2 2h0zm431 59l-8-11c-3-4-5-7-7-11 1 2 3 4 5 5h1c2 2 4 5 6 7 3 3 5 6 8 9v3s0 1-1 1c-2-1-2-1-3-2l-1-1z" class="I"></path><path d="M282 416c-3-1-6-3-9-5l20 9c4 2 7 3 10 6l3 2c-1 0-1 0-2 1-1 0-6-4-8-5-4 0-8-2-12-3-2 0-3-1-4-2l1-1c1 1 0 1 1 0h-1l-1-1h1 1v-1z" class="P"></path><path d="M284 421c-2 0-3-1-4-2l1-1c1 1 0 1 1 0h-1l-1-1h1 1v-1c5 3 10 5 14 8-4 0-8-2-12-3z" class="F"></path><path d="M486 500c3-6 9-14 14-18h1l-1 1c2 0 3-4 4-6l1 1h0c-5 8-11 15-16 23l-4 5-1-1c1-2 1-3 2-5zm166-273l-7-8 24 10c2 1 4 3 7 4v1c-2-1-4-2-6-2-3 0-6-1-9-2s-5-1-9-3zm134 21c-1 1 0 1 0 1 1 0 2 0 3-1 3-2 7-3 11-5 3-2 5-2 9-3v1h-1c-5 2-12 4-15 7 1 0 4-1 5-2 2 0 4-1 6-2s5-1 6-3l1-1h1 0c-2 3-5 5-8 7l-3 2c-3-1-5 0-7 1-2 0-4 0-5 1-3 1-5 1-8 2h-1c1-2 1-3 2-4 1 0 2-1 4-1z" class="K"></path><path d="M334 500l-37-20h0l24 10 11 5c1 1 4 2 5 4 0 1 1 2 2 2v1l-5-2z" class="I"></path><path d="M397 285l1-1c7-5 12-14 18-20v1c-5 6-10 12-14 18-2 3-3 6-5 9h0c-2 3-6 6-7 9-1 1-1 2-2 2l-2 1h0v-1h-1l-1 1-1 1h-1l4-5c3-3 6-6 8-9 1-2 2-3 3-4v-2h0z" class="Q"></path><path d="M407 697c2-1 5 1 7 2 3 1 6 1 8 3 1 0 2 1 3 1v2l-2 1c0 2 2 2 3 3 2 2 6 5 6 7-2-1-6-2-8-4-1-4-9-10-13-12-2-1-3-2-4-3z" class="C"></path><path d="M284 383c-9-6-20-11-29-18l19 8c3 2 7 3 9 5 2 1 2 4 4 6 1 0 1 1 1 1h0l-4-2z" class="D"></path><path d="M653 261l-24-33 1-1 23 29 6 7c-2 0-3 0-4-1v-1h-2 0z" class="E"></path><path d="M239 300l-24-15h0l33 12h0c1 1 2 1 3 2l1 1h1-2-2l-1-1c-3 0-5-1-8-2-2 0-2 0-3 1l1 1c1 0 1 0 1 1z" class="J"></path><defs><linearGradient id="F" x1="459.394" y1="453.889" x2="448.194" y2="459.293" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#cfcdcd"></stop></linearGradient></defs><path fill="url(#F)" d="M464 443h1c-3 6-6 11-9 16-2 3-4 6-7 9h-1c-1-1-1-3-2-4h1c1-3 4-6 6-9l11-12z"></path><path d="M447 464v-1h2c1 1 0 3 0 5h-1c-1-1-1-3-2-4h1z" class="O"></path><path d="M659 592c7-3 14-7 21-8-3 2-5 3-7 5l-15 8-20 6h0l21-11zM248 297l3 1 3 1v1l-1 1h-3l-2 2c5 3 9 7 14 10 2 2 5 3 8 5v1l-31-19c0-1 0-1-1-1l-1-1c1-1 1-1 3-1 3 1 5 2 8 2l1 1h2 2-1l-1-1c-1-1-2-1-3-2h0z" class="D"></path><path d="M248 297l3 1 3 1v1l-1 1h-3l-2 2-5-3h8 2-1l-1-1c-1-1-2-1-3-2h0z" class="C"></path><path d="M208 220l18 9c2 1 7 4 9 4 1 2 1 2 1 3h-2l-20-9-9-5 2-1c1 0 1-1 1-1z" class="J"></path><path d="M768 314l12-3c4-2 10-5 15-6l-19 10c-2 0-4 2-6 2-3 2-6 3-9 4s-6 2-9 2v-1c5-4 11-6 16-8z" class="K"></path><path d="M413 684l-34-23h0l24 12c1 1 1 2 3 2h0c1 1 0 1 1 2h1c2 2 4 2 4 5l1 1v1z" class="E"></path><path d="M584 790l32-20h0l-34 27-1-1 9-7h-1c-4 2-8 6-12 8 0-2 2-2 4-4l3-3z" class="J"></path><path d="M275 358l-34-21v-1l21 10 10 5c1 3 3 5 5 6h1c1 1 1 1 1 2-1 0-3-1-4-1z" class="D"></path><defs><linearGradient id="G" x1="573.845" y1="807.044" x2="553.155" y2="823.456" xlink:href="#B"><stop offset="0" stop-color="#696768"></stop><stop offset="1" stop-color="#888687"></stop></linearGradient></defs><path fill="url(#G)" d="M572 808c1 0 2 0 3-1l4-1c1-1 2-1 4-1-11 7-22 14-33 20-2 1-6 3-8 3l1-1c1 0 1 0 1-1l28-18z"></path><defs><linearGradient id="H" x1="709.863" y1="462.973" x2="701.137" y2="457.027" xlink:href="#B"><stop offset="0" stop-color="#a4a2a3"></stop><stop offset="1" stop-color="#cecdcd"></stop></linearGradient></defs><path fill="url(#H)" d="M713 453h1l6-3h1 1c-6 5-13 11-20 15-3 2-7 4-11 5h0v-1c7-5 14-11 22-16z"></path><path d="M642 288l11 11 1-1 1 1c1 1 1 1 3 2 1 0 1-1 1-1v-3l6 5-2 1h0-3c1 2 2 3 3 4s0 1 2 2l3 3 1 1v1c-1 0 0 0-1-1h-1c-1-1-4-3-5-4h-3c-3-3-7-7-10-11-1-1-1-2-3-3h0c-2-2-4-4-4-7z" class="E"></path><path d="M431 769c-3-3-8-5-12-7v-1c4 2 7 4 10 6h1c2 1 4 1 7 1 1 0 2-1 3-2 2 1 3 2 5 2 0 1-1 2-1 3h-1c3 2 7 5 10 6l1 1h-1c-1 0-3-1-5-1l-1 1-12-6c-1 0-3-2-4-3z" class="I"></path><path d="M440 766c2 1 3 2 5 2 0 1-1 2-1 3h-1l-6-3c1 0 2-1 3-2zm-9 3v-1h0c6 1 12 6 17 9l-1 1-12-6c-1 0-3-2-4-3z" class="G"></path><path d="M322 513c-5-2-9-6-14-9h1 0l9 3c11 5 23 8 34 13h0c-2 1-6-1-8-2l-3 1h0l-5-1c-3-1-7-4-10-4-1 0 0 0-1-1h-3z" class="C"></path><path d="M322 513v-1c-1-1-1-1-2-1l1-1 2 1c1 0 2 1 3 1l5 1c4 1 9 4 13 5l-3 1h0l-5-1c-3-1-7-4-10-4-1 0 0 0-1-1h-3zm112 233l8 4c1 2 5 4 7 4h1l4 3c2 1 4 4 6 5h1l4 8c-1 0-2 0-2-1v-1c-4-5-12-8-17-11-1-1-1-2-1-2-1-1-3-2-4-3l-10-5 3-1z" class="O"></path><path d="M501 521l17-22 1 1-1 1c-3 4-5 9-8 13l-6 9c0 1-1 2-2 3 0 1 0 2-1 3l1 1v1c-1 2-2 3-4 5l-1-1-3 4c0-1 0-2 1-3l2-4h-1c1-2 1-4 2-5l3-6z" class="K"></path><path d="M497 532l5-6c0 1 0 2-1 3l1 1v1c-1 2-2 3-4 5l-1-1-3 4c0-1 0-2 1-3l2-4z" class="G"></path><path d="M393 268l2-1 1 1-2 4h1 1c1-1 2-2 3-2h0v1c-5 3-8 8-12 12-2 1-3 2-4 3-2 0-3 2-5 3v-1l2-2h-2v-2c2-2 3-4 5-5 4-3 7-7 10-11z" class="K"></path><path d="M393 268l2-1 1 1-2 4c-2 1-4 4-5 6-1 1-3 2-4 3l-3 2h0v-1c1-1 1-2 1-3 4-3 7-7 10-11z" class="E"></path><path d="M720 433s0 1 1 1c3 0 4-1 6-3 2 0 2-1 4-1-7 7-15 15-24 20h0l-2 1h-1l1-1v-1l2-1v-1c1-2 3-3 4-5 1-1 1-3 2-4l4-3 2-2h1z" class="D"></path><path d="M707 448v-1c1-2 3-3 4-5 1-1 1-3 2-4l4-3c-1 4-6 9-9 13h-1z" class="K"></path><path d="M374 252c4-4 9-6 13-10l18-18 1 1c-7 6-14 15-19 23l-3 4c-3 1-4 2-6 4h-1c1-1 1-2 1-3-1 0-2-1-3-1h-1zm53 470l-35-22v-1l23 12 22 14c-1 1-1 1-2 1l-8-4z" class="P"></path><path d="M374 596c-11-5-21-13-31-19-2-1-7-4-8-5 1-1 27 12 30 14 1 0 2 1 2 2s0 1 1 1c3 2 5 3 6 7h0z" class="D"></path><path d="M650 624c4-1 7-2 11-4h0 1l-18 12-3 2c-4 1-8 2-11 3 2-2 4-3 6-5l-1-1c2-2 5-3 7-4s5-3 6-4 3-2 5-2l-3 3z" class="E"></path><path d="M648 623c1-1 3-2 5-2l-3 3c-4 1-10 6-14 8l-1-1c2-2 5-3 7-4s5-3 6-4z" class="H"></path><path d="M253 319l-21-11 1-1 2 2h2c1 1 1 1 3 0 0 0 1 1 2 1l16 6c3 1 5 3 8 4l-1 3 3 2h0l-15-6z" class="K"></path><path d="M258 316c3 1 5 3 8 4l-1 3h-1c-1 0-2-2-3-3s-1-2-2-3l-1-1z" class="C"></path><path d="M740 396l26-12 1 1c-1 1-5 3-7 4l-25 17c-1 1-4 3-6 3h-1l1-1c3-3 6-5 9-8v-1c1-1 1-2 1-2l1-1z" class="K"></path><path d="M651 288v-2l1-1c1 2 2 2 3 4h0c2 3 5 5 7 7l3 3 1 1c2 0 4 2 6 3-1 3-2 5-2 8l-1 2-1-1-3-3c-2-1-1-1-2-2s-2-2-3-4h3 0l2-1-6-5c-3-3-5-6-8-9z" class="G"></path><path d="M665 302h0l6 5-5-2-1 1c0 3 1 2 2 4 1 0 1 1 1 2l-3-3c-2-1-1-1-2-2s-2-2-3-4h3 0l2-1z" class="M"></path><path d="M253 319l15 6h0l-3-2 1-3 10 10h1c1 1 2 3 3 3l-1 2-5-4v1c-2 0-4-1-5-2l-9-3c-2-2-5-3-7-4v-1c-1-1-3-2-4-2h3l1 1h1 2-1c-1 0-1 0-2-1v-1z" class="B"></path><path d="M253 322c6 3 12 4 18 7 1 1 3 1 3 2v1c-2 0-4-1-5-2l-9-3c-2-2-5-3-7-4v-1z" class="Q"></path><path d="M585 780l12-5h0c-5 3-10 7-15 11-6 4-12 6-18 9-4 2-7 4-11 6l1-2c4-2 7-6 11-8 2-1 3-2 5-3l7-4 8-4z" class="N"></path><path d="M571 484l-25-37 1-1 17 24h3v-2c3 3 5 7 6 10 1 2 3 5 3 7l-1 2h-1 0l-3-3z" class="I"></path><path d="M567 468c3 3 5 7 6 10 1 2 3 5 3 7l-1 2h-1c-1-3-2-6-3-8-2-3-5-6-7-9h3v-2z" class="B"></path><path d="M625 421h1l-6-6c-5-4-10-8-14-13-1-1-2-3-3-5h-1l-3-7h-1l1-1 8 10c1 2 3 4 5 5h1 1c1 1 2 2 4 2 3 3 5 7 7 10 2 1 2 2 4 3l-2 5-2-3z" class="H"></path><path d="M614 404c1 1 2 2 4 2 3 3 5 7 7 10-2-1-3-3-5-5-3-2-5-4-7-7h1z" class="B"></path><path d="M279 368c-7-2-12-6-18-9l-22-13c3 0 5 2 7 3 7 2 13 6 20 8 2 1 3 2 5 3 4 2 6 5 10 6l3 1c-1 1-2 1-4 1h-1z" class="D"></path><defs><linearGradient id="I" x1="340.299" y1="513.847" x2="353" y2="509.558" xlink:href="#B"><stop offset="0" stop-color="#919091"></stop><stop offset="1" stop-color="#b5b3b3"></stop></linearGradient></defs><path fill="url(#I)" d="M317 500c2 0 4 1 5 2 2-1 3 0 5 0l4 1 1 1c6 2 10 7 17 6 1 1 3 2 5 2h0l3 5c-14-4-27-10-40-17z"></path><path d="M322 502c2-1 3 0 5 0l4 1 1 1v1c-2 0-4 0-5-1h-1l-4-2zm54 135c-4-2-8-5-11-7l-8-5h1l26 11h3 2l3 1 4 2 3 2-2 1c4 2 8 4 12 7h-3v-1h-2l-7-4c-2-1-2-1-3-2h-1 0c-2-1-4-3-7-3-3-1-7-2-10-2z" class="P"></path><path d="M389 636l3 1 4 2 3 2-2 1-13-6h3 2z" class="G"></path><path d="M423 700l-37-26h0c7 4 14 8 21 10 4 6 10 10 16 13v1c1 1 2 1 2 2 1 0 2 1 2 2l3 2h0l-7-4z" class="I"></path><path d="M731 417l7-3v1h5l-6 5c-2 1-4 3-7 4-5 2-10 6-15 7-3 1-6 5-10 6 2-3 6-5 9-7l9-7c1 0 7-5 8-6z" class="K"></path><path d="M738 415h5l-6 5c-2 0-4 1-6 2-4 2-7 4-11 6v-1c4-4 12-5 16-10l2-2z" class="M"></path><path d="M639 310c-1-2-4-5-5-7-4-6-8-12-12-17-2-3-7-7-8-10 2 1 4 4 6 6l8 9c3 4 7 9 10 11 2 1 4 3 5 5 3 2 5 4 7 8l4 6 3 3 1 2-12-13c-1 0-1-1-2-1-1-1-2-1-3-2v1l-2-1z" class="P"></path><path d="M641 310c-4-4-9-10-12-15l1-1 2 3c4 6 10 10 14 16-1 0-1-1-2-1-1-1-2-1-3-2z" class="G"></path><path d="M644 305l1 1c1 1 2 2 2 3l7 7h3l-1-4 9 9h0c-2-1-3-2-4-3 0 2 2 4 2 6 1 0 2 1 3 1v1l-3 6c-1 0-1-1-2-2-1-2-2-2-3-4l-1-2-3-3-4-6c-2-4-4-6-7-8l1-2z" class="D"></path><path d="M654 321h0v-2-1c2-1 2 1 3 1s2-1 3-1l-1-1c1 0 1 0 2 1 0 2 2 4 2 6-1-1-1 0-1-1-2-1-3-3-5-3v4l-3-3z" class="C"></path><path d="M657 324v-4c2 0 3 2 5 3 0 1 0 0 1 1 1 0 2 1 3 1v1l-3 6c-1 0-1-1-2-2-1-2-2-2-3-4l-1-2z" class="G"></path><path d="M646 295c2 1 2 2 3 3 3 4 7 8 10 11h3c1 1 4 3 5 4h1c1 1 0 1 1 1 0 2-1 4-2 6l-1 5c-1 0-2-1-3-1 0-2-2-4-2-6 1 1 2 2 4 3h0l-9-9c-3-1-6-3-8-5h1 1l1 1c1 0 1 1 2 1l1 1 2 1-1-1-1-1h2l-10-14z" class="L"></path><path d="M659 309h3c1 1 4 3 5 4h1c1 1 0 1 1 1 0 2-1 4-2 6 0-1 0-3-1-4-1-2-5-5-7-7z" class="H"></path><path d="M684 531l12-3c6-3 12-6 19-9l-14 10c-1 1-3 3-5 3s-4 2-7 2c-2-1-6 1-9 2h0l-8 2c-3 1-6 2-9 2l1-1 13-5 7-3z" class="I"></path><path d="M290 404l-30-18v-1l29 13c3 2 6 3 9 5l2 3-1 2-4-2-5-2z" class="D"></path><path d="M289 398c3 2 6 3 9 5l2 3-1 2-4-2v-1c-1-3-4-4-6-7z" class="C"></path><path d="M779 319c3-1 6-1 9-2-2 4-9 6-13 7l1 1h0l2-1v1l-1 1h0 1l-1 1-12 5h-7-1l1-1c0-1 0-1 1-1v-1-1h2c3-1 5-2 7-4 3-2 7-4 10-5h1z" class="D"></path><path d="M768 324c3-2 7-4 10-5h1c-3 2-7 3-9 6-2 1-5 2-6 4-2 1-4 1-6 2 0-1 0-1 1-1v-1-1h2c3-1 5-2 7-4z" class="C"></path><path d="M775 324l1 1h0l2-1v1l-1 1h0 1l-1 1-12 5h-7-1l1-1c2-1 4-1 6-2 4-1 8-3 11-5z" class="L"></path><path d="M226 280l24 11c2 0 2 0 3 1 2 1 5 3 7 2h1c0 1 1 1 2 1l2 5-2-1h-2c-9-3-16-6-24-10-1-1-4-2-5-3s-1-2-3-2c-2-1-2-2-3-4z" class="E"></path><path d="M250 291c2 0 2 0 3 1 2 1 5 3 7 2h1c0 1 1 1 2 1l2 5-2-1v-1c-5-1-9-3-13-7z" class="F"></path><path d="M594 447l1 1c2 3 3 6 6 9 1 2 1 1 2 2l3 3h1c1 2 2 3 3 4l-3 7c-3-1-20-21-23-25 3 1 5 3 7 5s6 8 9 8l2 2v-1c-1-2-3-4-4-6-1-1-1-2-2-3 0-2-1-3-2-5v-1z" class="L"></path><path d="M648 616l16-5c5-2 11-5 16-8v1l-18 8-12 7c-6 3-13 6-20 8-3 1-6 3-9 3l1-2h1c3-3 7-4 10-6 5-1 11-4 15-6z" class="I"></path><path d="M766 294c9-7 21-11 31-16 0 2-1 3-2 4h0l-12 8c-3 2-6 3-9 4-5 2-9 2-13 5v-1c0-1 0-1 1-1l4-3z" class="D"></path><path d="M489 505c3-3 7-7 11-10-4 6-7 12-12 17l2 1c-2 1-2 2-2 4l-6 5-4 6-2-5-1-2c3-2 5-5 8-9l6-7z" class="H"></path><path d="M488 512l2 1c-2 1-2 2-2 4l-6 5-4 6-2-5h0c1-1 2-1 2-1 4-3 7-7 10-10z" class="L"></path><path d="M390 255h0c-1 2-2 3-1 5l-4 5c-3 4-7 7-10 10h-1l1-2c0-1-1-2 1-3-2 0-6 2-7 3l-1 1-1-1-1-1c1 0 1 0 2-1l8-9c2 0 3-1 5-2h2c2-3 3-4 6-5h1z" class="K"></path><path d="M390 255h0c-1 2-2 3-1 5l-4 5-1-2 2-2c0-1 1-2 1-3l2-3h1z" class="D"></path><path d="M384 263l1 2c-3 4-7 7-10 10h-1l1-2c1-1 2-3 4-5 1-2 3-3 5-5z" class="Q"></path><path d="M381 260h2c2-3 3-4 6-5l-2 3c-4 2-6 6-10 9-3 2-7 4-10 6l-1-1c1 0 1 0 2-1l8-9c2 0 3-1 5-2z" class="F"></path><path d="M818 225h1c5-1 10-4 15-7v1c-16 10-30 23-48 29-2 0-3 1-4 1l1-1c2-1 4-2 6-4l10-5c2-1 3-3 4-4 2-1 5-2 7-4l8-6z" class="E"></path><path d="M741 387c3-1 6-3 9-3h1l1-1 1 1-15 11 2 1-1 1-2 1-1-2c-8 5-17 7-24 13 0-2 1-3 2-4 1 0 1 0 2-1l13-8c3-3 8-7 12-9z" class="Q"></path><path d="M736 396l2-1 2 1-1 1-2 1-1-2z" class="F"></path><defs><linearGradient id="J" x1="756.803" y1="365.942" x2="756.222" y2="352.622" xlink:href="#B"><stop offset="0" stop-color="#686869"></stop><stop offset="1" stop-color="#9a9797"></stop></linearGradient></defs><path fill="url(#J)" d="M751 359l31-13v1c-8 5-17 10-26 15-1 1-6 4-7 4l-4 2-6 1-1-1c1-1 2-1 3-2s3-1 5-3v-1c1-1 3-2 5-3z"></path><path d="M741 366c2 1 4 1 6 0h2l-4 2-6 1-1-1c1-1 2-1 3-2z" class="N"></path><path d="M612 707c10-8 22-13 33-20v1l-39 29c-2 0-5 2-6 1h0c3-2 5-5 7-7 2-1 4-3 5-4z" class="D"></path><defs><linearGradient id="K" x1="596.912" y1="748.781" x2="599.57" y2="762.626" xlink:href="#B"><stop offset="0" stop-color="#949092"></stop><stop offset="1" stop-color="#afb0af"></stop></linearGradient></defs><path fill="url(#K)" d="M597 754l31-17h0l-23 17c-3 1-6 3-9 5l-15 9c-2 1-5 3-7 4 2-3 6-4 8-7l15-11z"></path><path d="M652 227c4 2 6 2 9 3s6 2 9 2c2 0 4 1 6 2l1 2c-2-1-2-2-4-2l-3 1c-2 0-4-2-6-2-1 3 7 11 5 13l6 8c-1 0-1-1-2-1l-20-22v-1c2 3 4 5 6 7h1l-8-10z" class="H"></path><defs><linearGradient id="L" x1="631.594" y1="379.038" x2="634.424" y2="385.743" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#L)" d="M626 367c4 2 6 3 8 6 1 2 2 4 3 5h1 1v4c1 1 1 2 2 4h1c-1 2-2 5-3 7l-1-1c-4-7-9-12-13-19 1 1 3 2 5 2-2-2-2-3-2-5l-2-3z"></path><path d="M638 392c1-3-1-4-2-7 0-1 0-1 1-2 2 2 2 2 4 3h1c-1 2-2 5-3 7l-1-1z" class="C"></path><path d="M626 367c4 2 6 3 8 6 1 2 2 4 3 5h1 1v4l-1-1c-3-1-5-4-7-7l-1 1c-2-2-2-3-2-5l-2-3z" class="Q"></path><path d="M448 419l1 1c1 0 1 0 1-1l3-3c2-3 4-5 6-7l1 1h0c-1 1-7 7-7 8 0 2-3 5-4 6h1l1-1h1 1l3-3-9 10-2 3-8 13-3-8h1c3-2 5-5 7-7 0-2 1-3 1-4 2-2 4-5 5-8z" class="F"></path><path d="M435 438h0l1 1c3-1 8-8 10-10 0 1-1 3-1 4l-8 13-3-8h1zm160-11v-1c-4-3-7-8-10-12l1-1 8 9h0c2 0 3 1 4 1l5 5c3 2 4 4 7 6l-1 2 3 3c1 2 2 2 4 3h1v1h1l1 1-1 3c-7-5-14-10-20-16l-3-4z" class="B"></path><path d="M488 517c2-2 6-8 8-8 0 1-1 2-1 3l2-2 1 2c0 1 0 1-1 3l-6 7c0 1-3 4-3 5-2 1-3 3-4 4-1 0-1 1-2 1 0 1-1 1-1 1l-2-2-1-3 4-6 6-5z" class="I"></path><path d="M482 522c1 0 1 0 2 1h0c-1 2-1 3-2 5l-3 3-1-3 4-6z" class="J"></path><path d="M497 510l1 2c0 1 0 1-1 3l-6 7h0c-3 1-5 3-7 5 1-2 3-4 3-6 1-3 6-5 8-8v-1l2-2z" class="R"></path><defs><linearGradient id="M" x1="561.67" y1="809.647" x2="545.575" y2="825.585" xlink:href="#B"><stop offset="0" stop-color="#bcbcbb"></stop><stop offset="1" stop-color="#dedcdd"></stop></linearGradient></defs><path fill="url(#M)" d="M585 800c-2 2-3 2-5 3s-6 4-8 5l-28 18c0 1 0 1-1 1 1-3 2-7 3-10l1 1 7-3 2-1 5-3 9-6c2 0 3-1 5-1 1-1 2-1 3-2 2 0 4-1 7-2z"></path><path d="M554 815c0 1 0 2-1 2l-1 1c-1 0-2 1-4 2l-1-1v-1l7-3z" class="M"></path><path d="M575 804c-1 2-6 5-8 6-4 1-7 2-11 4h0l5-3 9-6c2 0 3-1 5-1z" class="J"></path><defs><linearGradient id="N" x1="425.561" y1="375.524" x2="429.39" y2="364.123" xlink:href="#B"><stop offset="0" stop-color="#656465"></stop><stop offset="1" stop-color="#7b7979"></stop></linearGradient></defs><path fill="url(#N)" d="M425 370c4-4 7-6 11-9l11-13v1l-10 15v-1c0-1 0-1 1-1l1-2v-1h0c-3 3-6 6-7 10-1 2-4 5-6 7 0 1-3 4-3 5l-4 4c-2 0-3 1-4 1l-1-1 1-1c1-1 2-3 2-4 1-1 1-3 0-4 3-2 6-4 8-6z"></path><path d="M425 370v2c0 1-3 2-3 3s0 1-1 2l-3 6c3-2 5-6 8-7 0 1-3 4-3 5l-4 4c-2 0-3 1-4 1l-1-1 1-1c1-1 2-3 2-4 1-1 1-3 0-4 3-2 6-4 8-6z" class="N"></path><defs><linearGradient id="O" x1="681.11" y1="580.916" x2="651.89" y2="570.584" xlink:href="#B"><stop offset="0" stop-color="#707372"></stop><stop offset="1" stop-color="#afaaac"></stop></linearGradient></defs><path fill="url(#O)" d="M657 578l9-3c4-2 9-2 13-4 3-2 7-3 10-5 1-1 3-2 5-2-1 1-3 2-4 3l-12 8c-2 0-4 0-6 1-1 2-4 4-6 5l-2 1c-2-1-3-1-5-1-5 1-9 3-14 4-1 1-1 1-2 1v-1l9-5 5-2z"></path><path d="M672 576c-1 2-4 4-6 5l-2 1c-2-1-3-1-5-1 4-2 9-3 13-5z" class="C"></path><path d="M616 379c0-3-5-9-7-12-1-1-3-4-2-5h0c3 5 7 8 10 13 1 2 2 3 3 4l1-1-2-4c2 1 2 1 3 3 2 3 4 5 6 7 3 4 6 11 10 13l-1 2c-1-1-1-1-2-1-1-1-3-2-4-3-4-3-7-7-12-11 0-1-2-4-3-5z" class="B"></path><defs><linearGradient id="P" x1="349.951" y1="538.53" x2="372.216" y2="548.885" xlink:href="#B"><stop offset="0" stop-color="#bdbcbc"></stop><stop offset="1" stop-color="#f2f2f2"></stop></linearGradient></defs><path fill="url(#P)" d="M346 533c5 2 10 4 15 7 2 1 4 2 6 4h1c2 2 4 6 5 10h-1c-1-1-2-2-3-2-3-1-5-3-8-4h-1-1v-1c-2-2-5-3-7-5-2-1-3-2-5-3l1-1c1 1 3 1 4 1-1-2-5-4-6-6z"></path><path d="M634 665l22-11h0l-32 25c-3 1-6 4-9 4h0l-1-1c2-4 7-7 11-9l-2-2c2-1 4-2 5-4h1l5-2z" class="D"></path><path d="M629 667l5-2c-1 2-6 7-8 8h-1l-2-2c2-1 4-2 5-4h1z" class="C"></path><path d="M408 332l29-36h1l-24 36c-3 3-5 6-8 9-2 2-4 3-6 5-1 1-2 3-4 4 0-2 5-6 6-7 1-2 2-3 4-5-3 1-4 3-6 5 1-4 5-8 8-11z" class="J"></path><defs><linearGradient id="Q" x1="458.435" y1="813.952" x2="476.705" y2="806.2" xlink:href="#B"><stop offset="0" stop-color="#6e6e6e"></stop><stop offset="1" stop-color="#999697"></stop></linearGradient></defs><path fill="url(#Q)" d="M445 799c2 0 3 1 5 2h4l1 1 1-1c2 1 8 4 9 3 3 0 5 2 7 2v1 1 2h0 2v1c2 0 2 1 3 2 3 2 6 4 8 6h1l1 2h-2c-6-4-14-7-20-10-7-4-14-8-20-12z"></path><path d="M455 802l1-1c2 1 8 4 9 3 3 0 5 2 7 2v1 1 2h0 2v1c2 0 2 1 3 2-3-1-6-3-8-4l-14-7z" class="H"></path><defs><linearGradient id="R" x1="291.758" y1="457.985" x2="334.242" y2="459.015" xlink:href="#B"><stop offset="0" stop-color="#a6a3a3"></stop><stop offset="1" stop-color="#ddd"></stop></linearGradient></defs><path fill="url(#R)" d="M304 458l-22-15 1-1c12 7 25 12 38 18l8 4c1 0 2 2 4 2l1-1 2 6c-2-1-4-3-7-4l-18-8c-2-1-1 0-2-1h-1c-1 0-3-1-4 0z"></path><path d="M214 227l20 9h2c2 2 3 4 4 6l2 5c-6-2-13-5-19-8h0c0-1-1-1-2-2s-2-1-2-2c2 1 3 2 6 2-2-2-5-2-6-5l-9-4h0 2l4 2c1 0 1 0 2 1l2 1h3c-1 0-1 0-2-1h-1l-2-1-1-1-3-1v-1z" class="G"></path><path d="M219 232c3 2 7 4 11 6 3 1 6 2 9 4h1l2 5c-6-2-13-5-19-8h0c0-1-1-1-2-2s-2-1-2-2c2 1 3 2 6 2-2-2-5-2-6-5z" class="E"></path><path d="M740 384h1 3l-3 3c-4 2-9 6-12 9l-13 8c-1 1-1 1-2 1l6-14c1 0 2 0 3-1 0 0 1 1 1 0 3 0 5-1 7-1 3-1 6-3 9-5z" class="H"></path><path d="M392 627c-5-2-10-5-15-8l-28-18h0c8 4 16 7 25 10 2 1 5 1 7 2l4 2h-1s-1 1-2 1c0 1 2 2 2 3 3 3 6 5 8 8h0z" class="I"></path><path d="M374 611c2 1 5 1 7 2l4 2h-1s-1 1-2 1c0 1 2 2 2 3-1 0-3-1-4-2-2-2-5-3-6-6z" class="K"></path><path d="M577 797c4-2 8-6 12-8h1l-9 7 1 1-5 4 1 1c-1 1-2 1-3 2-2 0-3 1-5 1l-9 6c1-2 2-2 3-3 2-1 4-3 6-4h-3l-4 2s-1 1-2 1v1c-2 1-5 2-7 2 2-1 3-1 5-3h0l2-1h-1 0-1c-1 1-1 0-2 0l2-1c1 0 1 0 2-1s2-1 4-2c1-1 2-2 3-2h1v-2h-1c-1 0-2 0-3 1h-1c-2 1-4 3-7 3 1-1 2-1 3-1l1-1c4-3 8-3 12-5 1-1 2-1 4-2l7-3-3 3c-2 2-4 2-4 4z" class="B"></path><path d="M570 805c1-1 3-2 5-4l6-5 1 1-5 4 1 1c-1 1-2 1-3 2-2 0-3 1-5 1z" class="C"></path><path d="M577 793l7-3-3 3c-2 2-4 2-4 4-1 2-7 4-8 5l-1-1 6-5 3-3z" class="D"></path><path d="M498 459c0 1 0 2-1 3 0 2-1 3-1 4-3 6-6 12-10 16 0 2-2 4-3 5l-1 1v-1l2-4v-1c-3 1-6 5-8 8-2 1-3 2-5 3h-1c1-1 2-3 3-4 3-6 9-10 13-17l6-6 6-7z" class="J"></path><path d="M473 489c2-1 4-3 5-5 3-4 7-7 9-11l4-4 1 1c-1 4-4 8-7 11l1 1c0 2-2 4-3 5l-1 1v-1l2-4v-1c-3 1-6 5-8 8-2 1-3 2-5 3h-1c1-1 2-3 3-4z" class="M"></path><path d="M440 766l-6-4c1-1 2 0 2-1-1-1-2-1-3-2l1-1 1 1h1c1 0 1 1 2 1h1c1 0 2 0 3 1l5 3c2 1 3 2 5 3 3 2 6 4 8 6-1 0-1 0-1-1-1-1-2-1-3-1-1-1 0-1-1-1h-1l1 1h0c1 1 2 2 3 2l2 2v1s1 0 2 1h-1c-2-1-3-1-5-1-1 0-1-1-1-1l-1 1h0l2 1-2 1-1-1c-3-1-7-4-10-6h1c0-1 1-2 1-3-2 0-3-1-5-2z" class="B"></path><path d="M445 768c1 0 1 0 2 1 1 0 1 1 2 1l6 3v1c-2 0-2-1-4 1l1 1c1 0 1 1 1 1-3-1-7-4-10-6h1c0-1 1-2 1-3z" class="H"></path><defs><linearGradient id="S" x1="444.155" y1="794.004" x2="456.06" y2="787.886" xlink:href="#B"><stop offset="0" stop-color="#999898"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#S)" d="M462 800c-7-3-14-8-21-13l-19-14h0l36 18 6 3v2h0c1 2 2 4 2 5l-1 1-3-2z"></path><path d="M458 791l6 3v2h0c1 2 2 4 2 5l-1 1-3-2v-3l-1-1c-1-2-2-3-3-5z" class="M"></path><path d="M603 381l6 7c2 3 4 7 7 10l13 16 1 2-1 3c-2-1-2-2-4-3-2-3-4-7-7-10-2 0-3-1-4-2-5-5-8-11-10-18-1-1-2-2-2-3h-1v-1-1l1 1 1-1z" class="L"></path><path d="M604 386c2 2 3 4 5 6l5 9c2 2 2 4 4 5-2 0-3-1-4-2-5-5-8-11-10-18z" class="M"></path><path d="M583 437h0l-2-3c0-1 0-1 1-1 2-1 4 0 5 1l3 3-3-6 1-1c1 1 1 1 2 1l2 3 2 4c1 1 1 2 2 3 1 2 1 3 2 4l6 9-1 1v1l4 6h-1l-3-3c-1-1-1 0-2-2-3-3-4-6-6-9l-1-1v1c-2-2-4-3-5-5l-4-4c0-1-1-2-2-2z" class="F"></path><path d="M594 447l-3-4c-2-2-2-3-3-5 2 1 5 3 6 5v1c0 1 1 1 1 2l1 1c2 3 5 6 7 9l4 6h-1l-3-3c-1-1-1 0-2-2-3-3-4-6-6-9l-1-1z" class="O"></path><path d="M430 733h-1c-4-3-8-6-12-8-3-3-8-5-12-9-1-1-3-2-5-4 3 1 7 3 10 4 2 2 7 3 9 5l3 1h5 0l8 4 1 1c-2 1-4 1-6 0v1c1 1 2 1 3 2l2 2 2 1c0 1 1 2 1 3h0c-1 0-8-3-8-3z" class="M"></path><path d="M419 721l3 1h5 0l8 4 1 1c-2 1-4 1-6 0-1-1-1-1-2-1l-6-3h-2c-1-1-1-1-1-2z" class="C"></path><path d="M481 469l9-9c3-3 4-5 7-7-1 2-3 5-4 8l-2 3v1l1 1-6 6c-4 7-10 11-13 17-1 1-2 3-3 4h1l-3 3c0 1-1 2-2 3h-1c1-1 3-4 4-5v-1-1l-2 1c1-2 2-3 3-4v-1c1-4 4-7 6-10 2-1 4-3 4-5s1-2 1-3v-1z" class="O"></path><path d="M470 489c1-2 2-3 3-5 2-3 5-5 8-8l5-7v3c-4 7-10 11-13 17-1 1-2 3-3 4h1l-3 3c0 1-1 2-2 3h-1c1-1 3-4 4-5v-1-1l-2 1c1-2 2-3 3-4z" class="H"></path><path d="M406 300l21-23 1 1-12 14c-3 3-6 7-8 11s-6 7-9 10c-1 2-1 3-3 4-3 2-6 7-9 10l-1 1v1c-1 1-1 2-1 3l-2-5v-1c3-4 7-6 8-10l1-1c1-3 6-7 8-8h1l-1 1h1c1-2 3-4 5-6l-1-1 1-1z" class="D"></path><path d="M392 315v1c0 1-1 3-2 4v2l-4 4 1 1-1 1v1c-1 1-1 2-1 3l-2-5v-1c3-4 7-6 8-10l1-1z" class="F"></path><defs><linearGradient id="T" x1="662.043" y1="243.994" x2="664.13" y2="261.72" xlink:href="#B"><stop offset="0" stop-color="#666565"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#T)" d="M653 246c1 0 1-1 2-1s3 2 5 3c3 1 5 3 9 4l4 2c2 2 4 3 6 5l-1 1c0-1-1-2-2-2s-3 0-4-1l-1 1c0 2 0 4 1 6h0c0 1 0 1 1 2v1c1 2 3 4 3 7-9-8-16-18-23-28z"></path><path d="M707 450h0l1 1c-3 1-5 2-7 3l-2 1v1l2-1v1c2 0 5-2 7-3s4-1 6-2l-1 2h0c-8 5-15 11-22 16v1h0l-5 3h-2l6-15c1-1 1-2 3-2 3 0 6-2 9-3 2-1 3-1 5-3z" class="G"></path><defs><linearGradient id="U" x1="579.487" y1="485.793" x2="592.726" y2="488.754" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#72706f"></stop></linearGradient></defs><path fill="url(#U)" d="M567 468l-5-8h0c3 3 5 7 8 10 1 1 1 2 2 3 0-2-1-4-2-5h0c8 9 17 17 27 24 0 1 1 1 1 2l-2 5c-4-3-10-5-13-9 0-1 0-1-1-2-2-3-5-6-6-9-1-1-1-2-2-3l-1 2c-1-3-3-7-6-10z"></path><path d="M402 291c4-7 10-13 14-19l6-9c1-1 2-3 4-4v1l-18 27c-1 1-4 5-4 6-1 2-4 4-4 5s0 1-1 2l-1 2c-3 3-5 7-9 8l-1 1c-2 3-5 4-7 6h-1c0-1 2-2 3-3l12-9 3-6 3-5c-6 6-12 12-19 16-1 1-2 3-4 3v-1l8-6-2-2 1-1h1v1h0l2-1c1 0 1-1 2-2 1-3 5-6 7-9h0c2 0 3-1 5-1z" class="J"></path><path d="M397 292c2 0 3-1 5-1-2 2-4 5-7 7l-6 6-3 2-2-2 1-1h1v1h0l2-1c1 0 1-1 2-2 1-3 5-6 7-9h0z" class="H"></path><defs><linearGradient id="V" x1="711.478" y1="472.232" x2="697.756" y2="467.266" xlink:href="#B"><stop offset="0" stop-color="#a09d9d"></stop><stop offset="1" stop-color="#d5d4d5"></stop></linearGradient></defs><path fill="url(#V)" d="M707 464c2 0 3-1 4-2 0 1 1 1 1 1h1c1-1 2-1 3-1v1l-10 9c-1 2-3 3-5 5h3 0l-2 2c-1 0-5 3-6 4-1 0-2 1-2 1-2 1-3 2-5 3l-1-1c0-1 0-1 1-2v-1c1-2 2-3 4-4h1s1 0 2-1h1v-1l-2-3-2 1h-1c2-1 4-2 5-3v-1c-1 1-2 1-3 1h-1c4-3 9-6 14-8z"></path><path d="M833 196c5-2 10-4 14-6h0l-39 30c2 1 4 0 7 0l-7 4-14 6v-1l-1-1 4-2c11-7 19-17 30-24h0c3-1 5-3 6-6z" class="R"></path><path d="M794 229l14-9c2 1 4 0 7 0l-7 4-14 6v-1zm-82 193c1 1 3 1 4 1 2 0 5-2 7 0l-9 7c-3 2-7 4-9 7-1 0-8 6-9 8v1s-2 1-2 2h-1l9-20 2-3c2-1 6-2 8-3z" class="G"></path><path d="M704 428c2 0 3-1 5 0 0 1-1 1-1 2l-2 1h-1c-1-2-1-2-1-3z" class="H"></path><path d="M450 408c1 2 2 2 3 2v1c-1 3-3 5-5 8-1 3-3 6-5 8 0 1-1 2-1 4-2 2-4 5-7 7h-1l-1-2-2-4-2-3 5-4h1c2-1 3-1 4-2l11-15z" class="F"></path><path d="M434 425h1c2-1 3-1 4-2-2 3-5 6-8 9l-2-3 5-4z" class="I"></path><path d="M433 436c2-1 4-4 6-6 2-3 5-7 8-11 1-2 4-7 6-8-1 3-3 5-5 8-1 3-3 6-5 8 0 1-1 2-1 4-2 2-4 5-7 7h-1l-1-2z" class="H"></path><path d="M485 489l13-11h1l-14 17c-1 0-4 4-4 4-3 4-7 11-11 13l-2-5-2-2c3-3 6-7 9-9 2-3 5-6 8-7 0 1-1 1 0 2l2-2z" class="E"></path><path d="M475 496h1c2-1 4-3 6-4l-5 5c-3 4-7 7-9 10l-2-2c3-3 6-7 9-9z" class="I"></path><defs><linearGradient id="W" x1="475.491" y1="501.673" x2="472.961" y2="509.474" xlink:href="#B"><stop offset="0" stop-color="#a9a6a6"></stop><stop offset="1" stop-color="#c5c4c5"></stop></linearGradient></defs><path fill="url(#W)" d="M468 507c2-3 6-6 9-10l1 1v2 1l1-1s1 0 2-1c-3 4-7 11-11 13l-2-5z"></path><defs><linearGradient id="X" x1="252.153" y1="311.707" x2="267.106" y2="306.587" xlink:href="#B"><stop offset="0" stop-color="#bebdbe"></stop><stop offset="1" stop-color="#f0f0ef"></stop></linearGradient></defs><path fill="url(#X)" d="M251 298c3 0 5 1 7 2v-1h1 2 2l2 1 7 19h-1-1v-1c-3-2-6-3-8-5-5-3-9-7-14-10l2-2h3l1-1v-1l-3-1z"></path><path d="M308 404l-8-4c-17-9-32-19-47-28v-1l24 12c3 1 6 3 8 4 6 1 8 4 13 6 3 2 6 6 9 6 0 1 1 2 1 4v1z" class="P"></path><path d="M285 387c6 1 8 4 13 6 3 2 6 6 9 6 0 1 1 2 1 4v-1l-16-10c-1-1-5-2-6-3 0-1 0-2-1-2z" class="C"></path><path d="M374 252h1c1 0 2 1 3 1 0 1 0 2-1 3h1c2-2 3-3 6-4l-8 10-8 9c-1 1-1 1-2 1l1 1 1 1-2 1h0v4c1 1 0 3 0 5 1 2 1 4 0 7v-4c0-2 0-4-1-6l-1-12c0-3-1-6-1-8v-1l11-8z" class="B"></path><path d="M367 259c2-3 4-3 8-2h0v1 3c0 1-2 3-3 4l1-2v-3s-1 1-1 2c-2 2-3 3-5 4h0-1c0-3 2-4 2-6l-1-1z" class="H"></path><path d="M374 252h1c1 0 2 1 3 1 0 1 0 2-1 3h1c2-2 3-3 6-4l-8 10-8 9v-1l4-5c1-1 3-3 3-4v-3-1h0c-4-1-6-1-8 2l-4 2v-1l11-8zm261 72l8 8h0c0-2-2-4-4-6l-9-11h0l10 11c3 5 7 9 11 12l7 6 1 1-1 1-2 6-1-1-3-3h0c-1-2-2-3-4-3s-5-4-7-5l-6-10 2 1-2-3c-1-2-1-2 0-4z" class="I"></path><path d="M635 330l2 1c3 4 7 9 11 12v-2c-1-2-2-3-3-5l5 4c2 2 4 4 7 5l1 1h0l-2 6-1-1-3-3h0c-1-2-2-3-4-3s-5-4-7-5l-6-10z" class="K"></path><path d="M650 340c2 2 4 4 7 5l1 1h0l-2 6-1-1c0-2-4-6-5-8-1-1-1-1 0-3z" class="B"></path><path d="M631 336c2 1 4 3 6 5 1 0 3 2 4 2l-1-2 1-1c2 1 5 5 7 5s3 1 4 3h0l3 3 1 1-3 8-2-1v-1c-3 0-4-3-6-3l-1 2-2-2c0-1-2-2-2-3-2-2-5-6-6-9h1v-1l-3-3-1-3z" class="G"></path><path d="M641 340c2 1 5 5 7 5s3 1 4 3h0 0c-2 0-3-1-4-2h-2l7 7h-1l-11-10-1-2 1-1z" class="B"></path><path d="M637 341c1 0 3 2 4 2l11 10c1 1 1 2 0 2-4-1-7-7-11-9-2-1-3-4-4-5z" class="L"></path><path d="M642 355c0-1-2-2-2-3-2-2-5-6-6-9h1c2 3 6 6 9 9 2 2 6 3 7 7v-1c-3 0-4-3-6-3l-1 2-2-2z" class="R"></path><path d="M815 203h1c2-1 9-2 11-1-11 7-19 17-30 24l-4 2 1 1v1l-1 1h-1l-1-1c4-7 9-13 14-20 1-3 4-4 6-6l4-1z" class="D"></path><path d="M811 204l4-1c1 1 1 1 1 2h-1c-3 1-6 2-9 5h-1c1-3 4-4 6-6z" class="C"></path><path d="M463 433l1-1 3-3c-1 1-1 2-2 3s-1 2-2 3c0 2 0 2 1 3l-1 2c1 1 1 2 1 3l-11 12c-2 3-5 6-6 9h-1l-1-2-3-6 8-9c1-1 2-2 3-4 3-2 8-6 10-10z" class="B"></path><path d="M452 451v4h1c-2 3-5 6-6 9h-1l-1-2c2-4 5-8 7-11z" class="N"></path><path d="M463 440c1 1 1 2 1 3l-11 12h-1v-4l11-11z" class="S"></path><path d="M730 424l-1 4c-3 1-6 3-9 5h-1c-1-1-2-1-3 0l1 1h-1c-3 2-8 7-9 11-2 4-6 7-10 8l-1 1c-1 0-3 2-5 1 0-1 1-1 1-2l-1-1v1c0-2 1-4 2-5h1c0-1 2-2 2-2v-1c1-2 8-8 9-8 4-1 7-5 10-6 5-1 10-5 15-7z" class="B"></path><defs><linearGradient id="Y" x1="295.505" y1="410.504" x2="296.465" y2="420.197" xlink:href="#B"><stop offset="0" stop-color="#6a6869"></stop><stop offset="1" stop-color="#828282"></stop></linearGradient></defs><path fill="url(#Y)" d="M303 409l9 3 2 4 3 8 2 4c-10-6-21-9-30-15l-3-2h3 1v-1l8 4 1-2-3-2h2c2-1 3-1 5-1z"></path><path d="M314 416l3 8v1c-2-3-6-4-8-6l-3-2v-1c2 0 4 2 6 2v-1l2-1z" class="G"></path><path d="M303 409l9 3 2 4-2 1v1c-2 0-4-2-6-2v1l3 2-5-2c-2 0-4-2-6-3l1-2-3-2h2c2-1 3-1 5-1z" class="M"></path><path d="M299 412c2 1 2 2 4 3l1 2c-2 0-4-2-6-3l1-2z" class="E"></path><path d="M303 409l9 3 2 4-2 1c-5-2-9-5-14-7 2-1 3-1 5-1z" class="F"></path><path d="M379 625l7 3c2 0 4 0 6 1 1 1 2 2 4 2 3 2 6 3 9 5l2 1h1 1l7 16c-2 0-5-3-7-4-4-3-8-5-12-7l2-1-3-2-4-2v-2c-3-3-7-4-10-6v-1l-3-3z" class="B"></path><path d="M391 631c3 0 6 2 8 5 1 1 2 3 4 5 1 1 2 1 4 2h0-3c-1 0-4-2-5-2l-3-2 2-2c-2-2-5-4-7-6h0z" class="H"></path><path d="M379 625l7 3c1 1 3 2 5 3h0c2 2 5 4 7 6l-2 2-4-2v-2c-3-3-7-4-10-6v-1l-3-3z" class="K"></path><path d="M405 636l2 1h1 1l7 16c-2 0-5-3-7-4-4-3-8-5-12-7l2-1c1 0 4 2 5 2 2 1 5 2 7 3-3-4-6-6-9-9l1-1 2 1v-1z" class="L"></path><defs><linearGradient id="Z" x1="615.789" y1="413.463" x2="623.143" y2="424.264" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#a5a4a4"></stop></linearGradient></defs><path fill="url(#Z)" d="M603 402l3 3 19 16 2 3-1 1-3 8-1-2-6-7-12-13-3-4c1-2 0-3 2-5z"></path><path d="M622 431h1c-1-1-1-2-1-2l-1-1v-1c-1-2-2-2-2-4 2-1 6 1 7 2l-3 8-1-2z" class="K"></path><path d="M603 402l3 3c0 3 2 4 4 6l5 8c1 2 2 3 2 5h-1l-12-13-3-4c1-2 0-3 2-5z" class="R"></path><path d="M625 345l-4-10 1-1 2 4c0 1 1 1 1 2h1c3 7 5 14 8 21 2 6 4 11 4 17h-1c-1-1-2-3-3-5l1-1c-4-6-9-11-13-18l-2-2-1-2-1-2v-1l1 1 1-1-1-3-3-3v-1h0 2 0c3 0 5 3 6 5h1z" class="L"></path><path d="M622 354l1 1c4 2 6 9 10 11l-5-9 2 2c1 1 2 1 4 2 2 6 4 11 4 17h-1c-1-1-2-3-3-5l1-1c-4-6-9-11-13-18z" class="M"></path><path d="M625 345l-4-10 1-1 2 4c0 1 1 1 1 2h1c3 7 5 14 8 21-2-1-3-1-4-2l-2-2h-1c0-1-1-2-1-2-1-2-2-3-3-4h0v-1l3 2v-2c-1-1-1-2-2-3v-1-1h1z" class="B"></path><path d="M625 345c2 3 3 6 3 9h-1s-1-1-1-2v-2c-1-1-1-2-2-3v-1-1h1z" class="F"></path><path d="M289 413c9 6 20 9 30 15 1 3 2 7 4 9 2 3 6 5 8 7v3h-1l1 2-4-1c-1 0-2-4-3-5-1-2-3-3-4-4-3-1-6-3-8-5l-8-5c1-1 1-1 2-1l-3-2c-3-3-6-4-10-6 0-3-3-4-4-7z" class="L"></path><path d="M321 438c3 3 7 5 9 9l1 2-4-1c-1 0-2-4-3-5-1-2-3-3-4-4l1-1z" class="T"></path><path d="M304 429c1-1 1-1 2-1 5 3 11 6 15 10l-1 1c-3-1-6-3-8-5l-8-5z" class="Q"></path><path d="M523 507l1 1-3 6c-1 0-1 0-2-1-1 1-1 2-1 3l-1 1c-1 1-1 1-1 3h0c1 3-1 5-2 7-1 1-2 3-3 4l-3 3h0c-1 2-2 4-4 6l-1-2c-2 2-3 4-4 5l-4 4c-2 1-2 2-4 2h-2l-1-1-1-2 1-2c2-1 4-3 6-5l3-4 1 1c2-2 3-3 4-5 3-4 6-7 9-11v2l12-15z" class="K"></path><path d="M494 539l3-4 1 1-4 6v2c-1 2-2 3-3 5h-2l-1-1-1-2 1-2c2-1 4-3 6-5z" class="F"></path><path d="M488 548l6-6v2c-1 2-2 3-3 5h-2l-1-1z" class="C"></path><path d="M516 520c1 3-1 5-2 7-1 1-2 3-3 4l-3 3h0c-1 2-2 4-4 6l-1-2c-2 2-3 4-4 5l-4 4c-2 1-2 2-4 2 1-2 2-3 3-5 5-4 11-10 15-15 1-1 1-2 2-3h2l3-6z" class="H"></path><path d="M503 538c1-2 3-4 5-4-1 2-2 4-4 6l-1-2z" class="L"></path><path d="M324 488l5 2c6 4 11 5 18 6 3 5 5 10 7 16h0c-2 0-4-1-5-2-7 1-11-4-17-6l-1-1-2-3 3 1 2-1 5 2v-1c-1 0-2-1-2-2-1-2-4-3-5-4l-11-5c2 0 2-1 3-2z" class="G"></path><path d="M332 495c2 1 4 1 5 3l13 9-3-1c-3 0-6-2-8-4h0v-1c-1 0-2-1-2-2-1-2-4-3-5-4z" class="C"></path><path d="M329 500l3 1c6 3 11 6 17 9-7 1-11-4-17-6l-1-1-2-3z" class="D"></path><path d="M808 224c3 0 6-1 9-1l-4 2c2 1 3 0 5 0l-8 6c-2 2-5 3-7 4-1 1-2 3-4 4l-10 5c-2 2-4 3-6 4l5-13c1-2 2-3 4-4h1l1-1 14-6z" class="B"></path><path d="M808 224c3 0 6-1 9-1l-4 2c-3 2-6 3-9 4-4 1-7 1-11 2l1-1 14-6z" class="I"></path><path d="M378 313c2 0 3-2 4-3 7-4 13-10 19-16l-3 5-3 6-12 9c-1 1-3 2-3 3h1c2-2 5-3 7-6l1-1c4-1 6-5 9-8l1-2c1-1 1-1 1-2s3-3 4-5v2c0 1-2 3-1 4l1-1 1 1 1 1-1 1 1 1c-2 2-4 4-5 6h-1l1-1h-1c-2 1-7 5-8 8l-1 1c-1 4-5 6-8 10v1c-2-5-4-9-5-14z" class="B"></path><path d="M405 301l1 1c-2 2-4 4-5 6h-1l1-1h-1c-2 1-7 5-8 8l-1 1c-4 1-5 4-8 6l-1-1c5-6 9-12 16-16 1-1 2-1 3-2v-1l1 1c-1 1-2 1-2 2 2-1 4-3 5-4z" class="P"></path><path d="M611 431c-1-6-6-10-9-15l-7-8 1-1 3 3h1 0c1 2 3 3 4 5l1 1h2c-1-1-2-2-2-3l-2-1 1-1 12 13 6 7 1 2-4 11-1-1h-1v-1h-1c-2-1-3-1-4-3l-3-3 1-2c-3-2-4-4-7-6l-5-5h1l8 7h2l2 1h0z" class="G"></path><path d="M611 431c3 0 5 2 6 4l1 1-1 1c-3-2-4-3-6-6h0z" class="F"></path><path d="M611 431c-1-6-6-10-9-15l-7-8 1-1 3 3c1 2 2 3 3 4l1 2h1c0 1 0 1 1 1l1 1c2 4 6 5 8 9 1 3 2 5 4 6v2h-1c-1-2-3-4-6-4z" class="B"></path><path d="M464 411l8-9 1 1c-4 5-9 12-12 18-2 3-2 5-4 8-4 6-8 12-13 18-1 1-2 3-3 3l-1 1-1-3-2-2 8-13 2-3 9-10 8-9z" class="K"></path><path d="M442 444h0c3 0 4-2 6-4-2 3-4 6-7 8l-1 1 1 1-1 1-1-3 3-4z" class="O"></path><path d="M458 422c-4 7-9 13-14 19v-1c0-2 2-5 3-7l11-11z" class="R"></path><path d="M464 411c0 4-5 7-6 10v1l-11 11v-3l9-10 8-9z" class="N"></path><path d="M445 433l2-3v3c-1 2-3 5-3 7v1l-2 3-3 4-2-2 8-13z" class="Q"></path><path d="M231 256c-5-3-9-8-13-10-2-1-3-2-4-3h0l1-1h0c-1-1-1-1-1-2h2c2 0 2 1 3 1 5 2 23 8 25 12h-1c2 2 4 4 5 6s2 4 2 6h0c-3-2-7-4-11-5v1c-1 0-2-1-3-2-1 0-2-1-3-2l-2-1z" class="B"></path><path d="M239 260v-2c-1-1-3-2-4-4v-1c-2 0-4-1-5-3h1c1 1 3 2 4 3h1c3 1 5 2 8 4 1 1 2 2 4 2 1 2 2 4 2 6h0c-3-2-7-4-11-5z" class="G"></path><path d="M236 253h0c-5-4-11-6-16-10l-3-1c0-1 2 0 2-1 5 2 23 8 25 12h-1c2 2 4 4 5 6-2 0-3-1-4-2-3-2-5-3-8-4z" class="C"></path><defs><linearGradient id="a" x1="700.841" y1="519.636" x2="678.329" y2="525.513" xlink:href="#B"><stop offset="0" stop-color="#969395"></stop><stop offset="1" stop-color="#d7d6d6"></stop></linearGradient></defs><path fill="url(#a)" d="M690 514l11-2c2-1 4-2 7-2-2 1-3 2-5 3l-20 18h1l-7 3v-1l2-1h1 0c-2-3 0-6-1-9-1-1-1-2-3-2h-3-1c-1 1-3 2-4 3h-1l2-3c3-2 7-4 10-6h5c2 0 4 0 6-1z"></path><path d="M690 514l11-2c2-1 4-2 7-2-2 1-3 2-5 3l-28 7c-1 0-2 1-3 1-1 1-3 2-4 3h-1l2-3c3-2 7-4 10-6h5c2 0 4 0 6-1z" class="F"></path><defs><linearGradient id="b" x1="456.649" y1="457.13" x2="465.709" y2="471.956" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#edeced"></stop></linearGradient></defs><path fill="url(#b)" d="M450 471c1-1 2-1 3-3l31-40 1 1-9 12c-2 2-4 5-5 7-1 3-3 5-5 8-1 1-1 2-2 3h1l2-2v3c1 0 2-2 3-2-1 2-3 4-4 5l1 1 16-21 1 1-7 9-11 14v1c-3 4-7 9-11 12l-1 1-4-10z"></path><path d="M467 464l-7 7-1-1c2-2 4-6 7-7l1 1zm-91 173c3 0 7 1 10 2 3 0 5 2 7 3h0 1c1 1 1 1 3 2l7 4h2v1h3c2 1 5 4 7 4 0 1 1 2 1 3l1 3c-3 2-11-3-14-5-3-1-5-3-8-3v1h-2-2 0c-6-3-10-8-15-12h0c1-1 1-1 1-2l-2-1z" class="F"></path><path d="M392 652h0c-6-3-10-8-15-12h0c6 5 13 8 19 11v1h-2-2z" class="K"></path><defs><linearGradient id="c" x1="380.127" y1="644.501" x2="405.873" y2="643.999" xlink:href="#B"><stop offset="0" stop-color="#666464"></stop><stop offset="1" stop-color="#7d7c7e"></stop></linearGradient></defs><path fill="url(#c)" d="M376 637c3 0 7 1 10 2 3 0 5 2 7 3h0 1c1 1 1 1 3 2l7 4h2v1h3c2 1 5 4 7 4 0 1 1 2 1 3-13-4-27-11-39-18l-2-1z"></path><path d="M759 346l23-12h1l-33 23 1 2c-2 1-4 2-5 3v1c-2 2-4 2-5 3s-2 1-3 2l1 1c-3 1-7 2-10 4l-1 1c-1-1-1-2-2-2l-4-1v-1h0l3-6c1 1 3 1 4 1h1 2c1 0 3-3 5-4l15-9c1 0 2-2 3-2l2-2c1 0 1-1 2-2z" class="J"></path><path d="M735 368c4-2 7-5 11-6v1c-2 2-4 2-5 3s-2 1-3 2l1 1c-3 1-7 2-10 4l-1 1c-1-1-1-2-2-2l-4-1v-1h0 1c1 0 2 0 3 1s1 0 2 1l7-4z" class="P"></path><path d="M735 368h-2c-1 0-1 0-1-1s1-2 2-2c5-4 10-5 16-8h0l1 2c-2 1-4 2-5 3-4 1-7 4-11 6z" class="B"></path><path d="M426 376c2-2 5-5 6-7 1-4 4-7 7-10h0v1l-1 2c-1 0-1 0-1 1v1c-2 3-3 5-5 8h0c1 4-2 7-3 10-1 2-2 3-3 5 2-1 3-3 5-5l1-1c1-1 2-2 2-3l1-1c0 1 0 1-1 2l1 1-8 10c-3 4-5 8-10 11h-1c-2-3-3-6-4-8l-2-4c1-1 2-3 4-4l1 1c1 0 2-1 4-1l4-4c0-1 3-4 3-5z" class="L"></path><path d="M410 389c1-1 2-3 4-4l1 1c1 0 2-1 4-1l-7 8-2-4z" class="J"></path><path d="M426 376c2-2 5-5 6-7 1-4 4-7 7-10h0v1l-1 2c-1 0-1 0-1 1v1c-2 3-3 5-5 8-5 8-10 17-17 24 1-3 4-6 5-9 1-2 3-4 3-6 0-1 3-4 3-5z" class="K"></path><path d="M635 631l1 1c-2 2-4 3-6 5 3-1 7-2 11-3l3-2 1 1 5-1c0 1-1 1-2 2h1c-5 4-10 8-16 11h-1c-1 0-1-1-1-1l-17 7-2 2 6-16 1 1v1c3-1 6-4 9-5 3 0 5-1 7-3z" class="L"></path><path d="M645 633l5-1c0 1-1 1-2 2h1c-5 4-10 8-16 11h-1c-1 0-1-1-1-1l-17 7c1-2 4-3 6-4 8-5 16-10 25-14z" class="E"></path><path d="M648 634h1c-5 4-10 8-16 11h-1c-1 0-1-1-1-1 0-1 11-6 12-7l5-3zM416 309l1 1-6 7 1 1h1l-7 13c-2 1-3 3-4 4s-1 2-2 3c-1 0-1 1-2 0v1l-2 2-1-1c-2 1-3 2-5 3v1l-3-6-2-4 1-1 1 1 4-3 2-1 1-1 1-1c1-1 2-2 3-2l7-7c1-1 2-3 3-3 3-2 6-4 8-7z" class="B"></path><path d="M391 331l4-1 1 1c-2 3-6 6-9 7l-2-4 1-1 1 1 4-3z" class="L"></path><path d="M411 317l1 1-6 8h0c-2 1-4 2-5 3l-5 3 3-4h0c0-3 5-5 7-7 2-1 4-3 5-4z" class="P"></path><path d="M412 318h1l-7 13c-2 1-3 3-4 4s-1 2-2 3c-1 0-1 1-2 0v1l-2 2-1-1c-2 1-3 2-5 3 1-2 4-4 6-6 2-3 5-4 5-8 1-1 3-2 5-3h0l6-8z" class="J"></path><path d="M406 326c0 1-1 2-2 3v1c-1 2-4 3-5 6-1 1-2 1-3 1 2-3 5-4 5-8 1-1 3-2 5-3z" class="K"></path><path d="M700 500l6-3c3-1 16-9 18-8 0 1-4 2-5 3-10 7-19 13-29 18-1 2-2 2-3 3h0l6-1c-1 0-2 1-3 2-2 1-4 1-6 1h-5c-3 2-7 4-10 6l-2 3h-2c2-6 6-9 11-13 1-1 2-1 3-2 1 0 0 0 1-1l4-3c1 0 2 0 3-1h1c1-1 3-3 5-4 2 1 4 1 7 0z" class="J"></path><path d="M690 510c-1 2-2 2-3 3h0l6-1c-1 0-2 1-3 2-2 1-4 1-6 1h-5l11-5z" class="L"></path><path d="M693 500c2 1 4 1 7 0l-6 4c-1 1-2 1-2 1-3 1-5 3-7 4-4 3-13 7-16 12h0l-2 3h-2c2-6 6-9 11-13 1-1 2-1 3-2 1 0 0 0 1-1l4-3c1 0 2 0 3-1h1c1-1 3-3 5-4z" class="B"></path><path d="M476 459c4-4 8-8 11-12l11-12v1l-10 14c-4 5-8 11-10 17l-6 9-6 6c2-4 5-7 7-11 1-1 3-3 3-4-2 1-5 6-6 7-4 4-8 10-12 14l-1-1-3-6 1-1c4-3 8-8 11-12 0 0 1-1 1-2 3-2 6-6 9-7z" class="N"></path><defs><linearGradient id="d" x1="464.225" y1="478.754" x2="458.975" y2="483.408" xlink:href="#B"><stop offset="0" stop-color="#7b7b7b"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#d)" d="M457 487l1-1v-2c0-2 2-3 3-5 0-1 0-2 1-2 1-3 3-5 5-7v2l-5 7h1c2-2 3-4 5-6l2 1c-4 4-8 10-12 14l-1-1z"></path><path d="M466 468s1-1 1-2c3-2 6-6 9-7-1 3-4 5-6 8-1 1-2 3-3 3-2 2-4 4-5 7-1 0-1 1-1 2-1 2-3 3-3 5v2l-1 1-3-6 1-1c4-3 8-8 11-12z" class="K"></path><defs><linearGradient id="e" x1="351.242" y1="534.817" x2="362.599" y2="530.74" xlink:href="#B"><stop offset="0" stop-color="#cac8c9"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#e)" d="M336 518l5 1h0l3-1c2 1 6 3 8 2h0c2 0 3 0 4 1h1l1-2 10 25h-1c-2-2-4-3-6-4h1v-1l-2-1h1 0c-4-2-8-3-12-6-3-1-6-3-8-5h2c-2-2-5-6-7-9z"></path><path d="M344 518c2 1 6 3 8 2h0c2 0 3 0 4 1 0 1 0 1-1 2-1-1-1-1-2-1l2 2h0c-4-2-9-3-14-5l3-1z" class="L"></path><path d="M336 518l5 1v1c1 1 2 1 2 2 1 1 2 3 2 4 1 2 3 4 4 6h0c-3-1-6-3-8-5h2c-2-2-5-6-7-9z" class="D"></path><path d="M421 349l7-9c0 3 0 4-1 6h0c0 1-2 5-3 5l-2 3-16 21-1 2-1-1-3-6-1-4-1-1c2-3 4-5 7-7l-2-2c2-2 3-3 6-4l1 1c2-1 2-2 4-2l-1 1s0 1 1 1c2-2 4-4 6-4z" class="J"></path><defs><linearGradient id="f" x1="413.524" y1="358.217" x2="414.976" y2="367.283" xlink:href="#B"><stop offset="0" stop-color="#767374"></stop><stop offset="1" stop-color="#8a8b8a"></stop></linearGradient></defs><path fill="url(#f)" d="M404 376l2-2h0c1-1 1-1 1-2 1-2 1-3 2-4h0c2-3 4-6 6-8 3-3 5-5 7-8 1-1 1-1 2-1l-2 3-16 21-1 2-1-1z"></path><path d="M414 352s0 1 1 1c2-2 4-4 6-4-1 2-3 4-5 6l-9 9c-2 1-5 3-6 6l-1-4-1-1c2-3 4-5 7-7l-2-2c2-2 3-3 6-4l1 1c2-1 2-2 4-2l-1 1z" class="G"></path><path d="M411 353c2-1 2-2 4-2l-1 1-14 14-1-1c2-3 4-5 7-7l-2-2c2-2 3-3 6-4l1 1z" class="I"></path><path d="M404 356c2-2 3-3 6-4l1 1c-1 2-4 4-5 5l-2-2z" class="K"></path><path d="M597 492c1 0 0 0 1-1-5-3-9-7-12-11l-1-1-1-1c-2-4-5-6-7-10-5-6-9-12-13-19l-2-4h-1l1-1 2 2c2 4 7 7 9 10 0 4 4 7 7 9h0l-4-6c-1-1-1-1-1-2l3 2c-1-2-3-4-4-6 4 3 7 7 10 11l11 15c2 3 4 5 4 9l1 1-2 5c0-1-1-1-1-2z" class="B"></path><defs><linearGradient id="g" x1="659.122" y1="592.218" x2="638.878" y2="586.782" xlink:href="#B"><stop offset="0" stop-color="#c3c2c3"></stop><stop offset="1" stop-color="#e6e5e5"></stop></linearGradient></defs><path fill="url(#g)" d="M643 585v1c1 0 1 0 2-1 5-1 9-3 14-4 2 0 3 0 5 1l2-1c-2 2-7 6-7 8h1c0 1 0 2-1 3l-21 11h0c-2 2-4 2-7 3l4-8c1-4 4-10 7-14l1 1z"></path><path d="M664 582l2-1c-2 2-7 6-7 8-5 5-13 5-18 9-1 0-2 1-2 1l-1-1c10-3 17-11 26-16z" class="E"></path><path d="M659 589h1c0 1 0 2-1 3l-21 11h0c-2 2-4 2-7 3l4-8 1 1h0c1-1 1 0 2-1l1 1s1-1 2-1c5-4 13-4 18-9z" class="L"></path><defs><linearGradient id="h" x1="428.879" y1="722.466" x2="439.274" y2="711.695" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#f2f1f1"></stop></linearGradient></defs><path fill="url(#h)" d="M423 700l7 4c3 2 6 4 9 5l11 25-2-1-11-5-1-1-1-1c1 0 1 0 2-1l-22-14c2-1 3-1 3-2l6 3c2 2 6 3 8 4 0-2-4-5-6-7-1-1-3-1-3-3l2-1v-2c-1 0-2-1-3-1l1-2z"></path><path d="M437 725c4 2 9 4 11 8l-11-5-1-1-1-1c1 0 1 0 2-1z" class="E"></path><path d="M425 703c1 1 3 1 4 3 1 1 1 2 1 3 1 1 3 2 4 4 1 1 0 2 1 3s2 2 2 3h0-1c-1-1-1-2-2-2h-1l-1-1c0-2-4-5-6-7-1-1-3-1-3-3l2-1v-2z" class="B"></path><path d="M470 474c1-1 4-6 6-7 0 1-2 3-3 4-2 4-5 7-7 11l6-6 6-9c0 1 0 2-1 3l-1 1v1h0c1 0 2-3 3-3v2h0l2-2v1c0 1-1 1-1 3s-2 4-4 5c-2 3-5 6-6 10v1c-1 1-2 2-3 4l2-1v1 1c-1 1-3 4-4 5h1c1-1 2-2 2-3l3-3c2-1 3-2 5-3 2-3 5-7 8-8v1l-2 4v1l1-1h1l1 2-2 2c-1-1 0-1 0-2-3 1-6 4-8 7-3 2-6 6-9 9l-8-17c4-4 8-10 12-14z" class="L"></path><path d="M549 917c-25 4-51 4-76 5-37 0-74-1-110-5h0l8-1c-2 0-3 0-4-1l1-1h1c5 2 11 2 16 3l23 2c5 0 9 0 14 1 7 1 16 0 24 0 29 0 57 0 86-4l16-1h0c-3 1-5 1-7 2 1 0 2 0 3-1 2 0 4 0 5 1z" class="G"></path><defs><linearGradient id="i" x1="745.652" y1="382.196" x2="727.492" y2="380.852" xlink:href="#B"><stop offset="0" stop-color="#9c999a"></stop><stop offset="1" stop-color="#c6c4c5"></stop></linearGradient></defs><path fill="url(#i)" d="M739 369l6-1c1 1 1 1 2 1v1h4l-3 3h0 0c0 1-1 2-2 3 1 0 2 0 3 1-3 2-6 5-9 7s-6 4-9 5c-2 0-4 1-7 1 0 1-1 0-1 0-1 1-2 1-3 1l2-7c2-3 4-7 6-10l1-1c3-2 7-3 10-4z"></path><path d="M733 379c2-1 5-2 7-3l1 1-18 13c-1 1-2 1-3 1l2-7c3 0 5 0 7-2l4-3z" class="V"></path><path d="M739 369l6-1c1 1 1 1 2 1v1h4l-3 3h0c-2 1-5 4-7 4l-1-1c-2 1-5 2-7 3l-4 3c-2 2-4 2-7 2 2-3 4-7 6-10l1-1c3-2 7-3 10-4z" class="L"></path><path d="M729 382l-1-1c2-3 6-5 10-7 0 0 1-1 2-1l1-1c1-1 2-1 4-1-3 2-11 5-12 8l-4 3z" class="B"></path><path d="M747 370h4l-3 3h0c-2 1-5 4-7 4l-1-1c-2 1-5 2-7 3 1-3 9-6 12-8 0-1 1-1 2-1z" class="C"></path><path d="M359 572l6 3 2-1c2 2 5 4 8 5 4 4 6 7 12 7l6 15 2 4-4-1h0c-4-3-10-4-15-6l-2-2h0c-1-4-3-5-6-7-1 0-1 0-1-1s-1-2-2-2c2 0 4 2 5 2 2 0 4 1 5 2v-1l-1-1c-1-1-2-2-3-2v-1c-1-3-5-6-8-8 0 0 0-1-1-1-1-2-2-3-3-4z" class="L"></path><defs><linearGradient id="j" x1="376.367" y1="600.983" x2="390.466" y2="595.581" xlink:href="#B"><stop offset="0" stop-color="#b6b3b5"></stop><stop offset="1" stop-color="#dfe0df"></stop></linearGradient></defs><path fill="url(#j)" d="M365 586c2 0 4 2 5 2 2 0 4 1 5 2 2 1 4 3 6 4 1 1 1 2 2 4 2 0 6 2 8 3 0 0 1 1 1 0h1l2 4-4-1h0c-4-3-10-4-15-6l-2-2h0c-1-4-3-5-6-7-1 0-1 0-1-1s-1-2-2-2z"></path><path d="M370 588c2 0 4 1 5 2 2 1 4 3 6 4 1 1 1 2 2 4-3-2-6-3-8-5-3-2-4-2-5-5z" class="H"></path><path d="M359 572l6 3c1 0 2 1 3 1 3 2 6 4 7 6 2 5 8 6 11 11 0 1 1 2 2 3h0c-1 0-2-1-3-1-3-2-7-4-10-6l-1-1c-1-1-2-2-3-2v-1c-1-3-5-6-8-8 0 0 0-1-1-1-1-2-2-3-3-4z" class="B"></path><path d="M368 579c3 1 5 4 7 6s5 3 6 5 3 3 4 5c-3-2-7-4-10-6l-1-1 1-1-1-1h0c-1-3-4-4-5-6l-1-1z" class="C"></path><path d="M359 572l6 3c1 0 2 1 3 1 0 1-1 1-1 2l1 1 1 1c1 2 4 3 5 6h0l1 1-1 1c-1-1-2-2-3-2v-1c-1-3-5-6-8-8 0 0 0-1-1-1-1-2-2-3-3-4z" class="E"></path><path d="M436 388c8-7 15-15 22-23v1l-14 19c-5 7-9 16-14 23-1 2-3 5-5 6 1-1 2-3 3-5l-1-1v1l-5 5v-2l-2-3-4-8h1 1c2 0 3-2 4-3l11-12v2h3z" class="J"></path><defs><linearGradient id="k" x1="430.305" y1="400.132" x2="420.102" y2="405.167" xlink:href="#B"><stop offset="0" stop-color="#656566"></stop><stop offset="1" stop-color="#807e7e"></stop></linearGradient></defs><path fill="url(#k)" d="M435 390h0c2 0 2-1 4-2-1 2-3 4-4 6l-10 12-3 3v3l-2-3c3-7 9-14 15-19z"></path><path d="M417 401h1c2 0 3-2 4-3l11-12v2h3l-1 2c-6 5-12 12-15 19l-4-8h1z" class="G"></path><path d="M616 384l2 1 1 1h1l1 1c0-1-1-1-1-2l-1-1c5 4 8 8 12 11 1 1 3 2 4 3 1 0 1 0 2 1l-3 7-1 1-3 9-1-2-13-16c-3-3-5-7-7-10 1-1 1-2 2-3l3 3 2-1 5 5c-1-3-4-4-5-8z" class="M"></path><path d="M616 387l5 5c3 5 7 7 12 10 0-1 0-1-1-2 1 0 2 1 2 1l1-1v-2c1 0 1 0 2 1l-3 7c-1-2-3-3-5-4l-3-3-12-11 2-1z" class="G"></path><path d="M619 384c5 4 8 8 12 11 1 1 3 2 4 3v2l-1 1s-1-1-2-1l-14-15 1 1h1l1 1c0-1-1-1-1-2l-1-1z" class="F"></path><path d="M611 385l3 3 12 11-1 2h-2l-1-1h-2l-1-1c-1-1-2-1-3-1-3-3-5-7-7-10 1-1 1-2 2-3z" class="N"></path><defs><linearGradient id="l" x1="622.115" y1="401.113" x2="629.052" y2="412.153" xlink:href="#B"><stop offset="0" stop-color="#7d7b7c"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#l)" d="M616 398c1 0 2 0 3 1l1 1h2l1 1h2l1-2 3 3c2 1 4 2 5 4l-1 1-3 9-1-2-13-16z"></path><path d="M626 399l3 3-1 1c-2-1-4-1-5-1v-1h2l1-2z" class="Q"></path><path d="M629 414h1c0-3-2-7-4-10l3 1c1 1 2 2 4 2h0l-3 9-1-2z" class="K"></path><path d="M370 911v-1h-2c1-1 2-1 3-1 1-1 1 0 2-1h-4c-1 0-3 0-4 1h-4c-1 0-2 1-4 0 22-4 44-5 66-5 35-1 71-1 106 2 9 1 18 1 27 4h-4c-2-1-3-1-5 0-3 1-6 0-9 0l-18-3h-16c-35-3-70-2-106 0-6 0-12 1-19 2-3 0-6 2-9 2z" class="L"></path><defs><linearGradient id="m" x1="285.833" y1="385.884" x2="296.95" y2="377.4" xlink:href="#B"><stop offset="0" stop-color="#cecdcd"></stop><stop offset="1" stop-color="#f3f2f3"></stop></linearGradient></defs><path fill="url(#m)" d="M284 367l5 2c2 1 5 2 5 5 1 1 2 1 3 1l10 24c-3 0-6-4-9-6-5-2-7-5-13-6-2-1-5-3-8-4 3 0 4 0 6 1l1-1 4 2h0s0-1-1-1c-2-2-2-5-4-6-2-2-6-3-9-5l2-3-2-1c1-1 2 0 3 0l2-1h1c2 0 3 0 4-1z"></path><path d="M276 370l-2-1c1-1 2 0 3 0l1 1c1 0 2 1 2 1l7 3c-2 1-4 0-6-1-2 0-4-2-5-3zm8-3l5 2c2 1 5 2 5 5-3-2-6-3-9-4-2-1-3-1-5-2 2 0 3 0 4-1z" class="M"></path><path d="M289 386v-2h3c2 0 5 3 7 5v2s1 1 0 1c-2-1-5-2-7-4l-3-2z" class="B"></path><path d="M277 383c3 0 4 0 6 1l1-1 4 2 1 1 3 2c2 2 5 3 7 4 0 1 0 1-1 1-5-2-7-5-13-6-2-1-5-3-8-4z" class="L"></path><defs><linearGradient id="n" x1="467.057" y1="832.235" x2="492.03" y2="827.749" xlink:href="#B"><stop offset="0" stop-color="#cdcbcc"></stop><stop offset="1" stop-color="#efefef"></stop></linearGradient></defs><path fill="url(#n)" d="M455 813c3 0 6 3 8 4h2 5v1l1-1h1c5 3 9 6 14 9l1-1c-1-2-1-3-2-4h2l9 22-1 1-6-3-12-6v-1l-3-3c-3-2-5-5-8-7-2-1-4-4-6-5l-5-6z"></path><path d="M455 813c3 0 6 3 8 4h2l3 2c-1 0-2-1-3-1h0v1h-2-3l-5-6z" class="O"></path><path d="M554 843c11-5 22-12 32-20v1c-18 15-36 33-56 46-1 1-3 2-4 3l6-17h1c6-6 14-8 21-13z" class="J"></path><path d="M533 856c2 0 5-2 7-3 5-1 10-5 15-7-6 6-14 10-18 17-2 3-5 4-7 7-1 1-3 2-4 3l6-17h1z" class="G"></path><defs><linearGradient id="o" x1="603.692" y1="432.012" x2="598.484" y2="462.276" xlink:href="#B"><stop offset="0" stop-color="#c4c4c3"></stop><stop offset="1" stop-color="#f7f5f6"></stop></linearGradient></defs><path fill="url(#o)" d="M590 431c-2-4-6-7-9-11 5 3 7 6 10 9h1l2 2h4c6 6 13 11 20 16-1 0-1 2-2 3l-6 16c-1-1-2-2-3-4l-4-6v-1l1-1-6-9c-1-1-1-2-2-4-1-1-1-2-2-3l-2-4-2-3z"></path><path d="M598 431c6 6 13 11 20 16-1 0-1 2-2 3-1-1-7-1-9-1-2-1-3-4-4-5l-9-13h4z" class="N"></path><path d="M598 434l4 3c2 1 6 3 6 5h-1c-5 0-7-5-9-8z" class="R"></path><path d="M736 396l1 2 2-1s0 1-1 2v1c-3 3-6 5-9 8l-1 1h1c2 0 5-2 6-3l3 1c-1 1-9 5-9 5h0c-1 2-2 2-4 3-1 0-1 1-2 2h0 4l3-1 1 1c-1 1-7 6-8 6-2-2-5 0-7 0-1 0-3 0-4-1-2 1-6 2-8 3 0-1 1-2 2-3l6-13c7-6 16-8 24-13z" class="G"></path><path d="M737 398l2-1s0 1-1 2v1c-3 3-6 5-9 8l-1 1h1c-2 2-4 2-7 3v1l-2 1c-2 1-4 1-6 2 1-1 0-1 1-1h-2v-1c7-3 11-11 18-12l6-4z" class="H"></path><path d="M737 398l2-1s0 1-1 2v1c-3 3-6 5-9 8l-1 1h1c-2 2-4 2-7 3 0 0 0-1 1-1 2-1 3-4 6-5 1-1 2-1 3-2 1 0 2-1 2-3 0 1-1 1-2 1h-1l6-4z" class="C"></path><path d="M706 422c2 0 4-1 5-2 6-3 12-6 18-8h0c-1 2-2 2-4 3-1 0-1 1-2 2h0 4l3-1 1 1c-1 1-7 6-8 6-2-2-5 0-7 0-1 0-3 0-4-1-2 1-6 2-8 3 0-1 1-2 2-3z" class="Q"></path><path d="M727 417l3-1 1 1c-1 1-7 6-8 6-2-2-5 0-7 0-1 0-3 0-4-1 5-1 10-2 15-5z" class="B"></path><path d="M639 310l2 1v-1c1 1 2 1 3 2 1 0 1 1 2 1l12 13c1 2 2 2 3 4 1 1 1 2 2 2l-4 13-1-1-7-6c-4-3-8-7-11-12l1-1-8-9-2-3v-1l3 4c1 0 1 0 1-1h1l4 5v-1c-1-2-1-3-1-5 1-2-1-1-1-3l1-1z" class="L"></path><path d="M641 325l-8-9-2-3v-1l3 4c2 1 3 3 5 4 3 1 6 5 9 8 2 3 5 6 7 9 1 1 3 3 3 4v1l-5-5h-2v1h0c-4-3-8-7-11-12l1-1z" class="H"></path><path d="M641 325c2 2 4 4 5 7 2 1 6 3 7 5h-2v1h0c-4-3-8-7-11-12l1-1z" class="B"></path><defs><linearGradient id="p" x1="307.154" y1="452.884" x2="323.249" y2="443.948" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#p)" d="M301 434c3 1 7 2 10 3h0c-1-1-2-1-2-1l-2-1h2l1 1 1-1-2-1h3c2 2 5 4 8 5 1 1 3 2 4 4 1 1 2 5 3 5l6 16c-5-3-10-4-14-8l-11-7-8-7v-1c-1-3-1-4 1-7z"></path><path d="M308 449v-3l1-1c2 3 4 6 6 8h1c0 1 1 1 2 2l1 1h0l-11-7z" class="C"></path><path d="M503 858c-7-2-14-7-21-10l-50-27h0l28 11v-1l-6-3v-1c7 2 15 6 23 8l12 6 6 3 1-1 5 12c1 1 1 2 2 3z" class="N"></path><path d="M474 838l7 2h0v1c3 2 7 5 10 6 2 1 3 3 4 4l-22-12 1-1z" class="K"></path><path d="M481 840c5 1 9 2 14 4h0l1-1 5 12h0c-2-2-4-3-6-4-1-1-2-3-4-4-3-1-7-4-10-6v-1z" class="G"></path><path d="M460 831l-6-3v-1c7 2 15 6 23 8l12 6 6 3h0c-5-2-9-3-14-4h0l-7-2-1 1-13-7v-1z" class="I"></path><path d="M460 831c2 0 4 1 5 1l1 1c3 1 5 4 8 5l-1 1-13-7v-1z" class="D"></path><path d="M673 253c1 0 1 1 2 1l-6-8c2-2-6-10-5-13 2 0 4 2 6 2l3-1c2 0 2 1 4 2 4 12 4 41-1 53h0l-1 2-1-1v-2c0-1-1-1-1-3v-1-1c-1-3-2-5-3-7-1-3-3-5-6-6-2-2-3-4-5-7l-6-7c2 0 3 0 4 2l1 1c1 2 6 6 7 8h0l5 5c3 0 4 3 7 4 0-1-1-1-1-2 0-3-2-5-3-7v-1c-1-1-1-1-1-2h0c-1-2-1-4-1-6l1-1c1 1 3 1 4 1s2 1 2 2l1-1c-2-2-4-3-6-5v-1z" class="L"></path><path d="M584 457c-1-3-3-7-5-10-6-10-13-20-20-29l1-1 24 31c3 4 20 24 23 25l-1 3-4 8-2 5-1-1c0-4-2-6-4-9l-11-15 1-1c-2-2-3-5-5-7l1-1 2 3 1-1z" class="Q"></path><path d="M588 462v-3-1c-1-2-3-4-4-6h1c2 2 4 6 6 8 3 4 5 5 6 9v1l-3-1-6-7z" class="N"></path><path d="M596 473h3c0-2 0-2-1-3l1-1c1 1 2 2 4 3 1 1 2 2 2 3h0l1 1-4 8c0-2-1-3-1-4l-5-7z" class="D"></path><path d="M601 480c1-1 1-4 0-6v-1l4 2 1 1-4 8c0-2-1-3-1-4z" class="C"></path><path d="M584 464l1-1c-2-2-3-5-5-7l1-1 2 3 1-1c2 2 3 4 4 5l6 7c0 1 2 3 2 4l5 7c0 1 1 2 1 4l-2 5-1-1c0-4-2-6-4-9l-11-15z" class="F"></path><path d="M398 339v-1c1 1 1 0 2 0 1-1 1-2 2-3s2-3 4-4l2 1c-3 3-7 7-8 11 2-2 3-4 6-5-2 2-3 3-4 5-1 1-6 5-6 7 2-1 3-3 4-4 2-2 4-3 6-5 3-3 5-6 8-9 1 0 1 0 2-1v1l-4 5v3l-2 3h1c1-1 3-1 4-3 1 0 1 0 1-1l2 1 1-2h2c2-1 3-4 5-5-1 3-5 6-6 9 0 4-2 6-4 8l-1 1c-2 0-2 1-4 2l-1-1c-3 1-4 2-6 4l2 2c-3 2-5 4-7 7-1-3-2-5-3-8l-6-13v-1c2-1 3-2 5-3l1 1 2-2z" class="G"></path><path d="M398 339v-1c1 1 1 0 2 0 1-1 1-2 2-3s2-3 4-4l2 1c-3 3-7 7-8 11h0l-1 1c-1 2-2 2-3 3h-4c2-2 4-3 5-5l1-2v-1zm16-7c1 0 1 0 2-1v1l-4 5c-2 3-4 7-7 10-1 1-1 1-1 2-1 3-6 7-8 9 0-1 0-3 1-4s3-2 4-4l1-1c0-1 1-1 1-2v-1h-3c2-2 4-3 6-5 3-3 5-6 8-9z" class="B"></path><path d="M418 340l1-2h2c2-1 3-4 5-5-1 3-5 6-6 9 0 4-2 6-4 8l-1 1c-2 0-2 1-4 2l-1-1c-3 1-4 2-6 4l-4 4h0c0-3 3-5 5-7 0-1 0-2 1-3h1c1-2 2-4 4-5 3-1 5-3 7-5z" class="I"></path><path d="M410 352l10-10c0 4-2 6-4 8l-1 1c-2 0-2 1-4 2l-1-1z" class="M"></path><defs><linearGradient id="q" x1="240.557" y1="285.239" x2="254.7" y2="276.604" xlink:href="#B"><stop offset="0" stop-color="#a7a6a6"></stop><stop offset="1" stop-color="#e4e3e3"></stop></linearGradient></defs><path fill="url(#q)" d="M214 259h0l-2-1h1v-1l1-1 1 1c1 1 2 1 3 1 1 1 2 2 4 3 1 1 2 1 3 1 3 0 6 1 8 1 2 1 3 1 4 1 4 1 9 4 12 6 1 2 2 3 4 3l10 22c-1 0-2 0-2-1-3-2-7-3-9-5-7-4-13-9-19-14h0c1-2 1-3 3-4v-1c-1-1-3-1-4-3h0l-10-4c-3-1-5-3-8-4z"></path><path d="M214 259h0l-2-1h1v-1l1-1 1 1c1 1 2 1 3 1 1 1 2 2 4 3 1 1 2 1 3 1 9 3 17 5 24 10-6-1-11-4-17-5l-10-4c-3-1-5-3-8-4z" class="F"></path><path d="M394 219h2 0l-9 14c-2 2-5 5-6 7-1 1-2 2-2 3-3 3-6 5-8 8-2 1 0-1-1 1h-1c-1 1-1 2-2 3s-3 1-4 2c0-8 1-15 3-22l1-3 27-13z" class="G"></path><path d="M394 219l-3 3c-1 3-8 9-11 12-1 1-2 3-3 4-1 2-4 5-6 7 2-6 6-10 11-15-5 2-11 4-16 5l1-3 27-13z" class="H"></path><path d="M541 493l1-1 6 9h0 2 1c6 14 10 27 19 39 2 2 4 5 6 7l-3 7c-2-2-4-3-6-6-3-2-5-4-7-7 1-2-3-5-5-8l-1-3-1-3h1c0-2-2-6-3-8l-9-17v-1l1 1c0 1 1 1 2 2h0l-1-5c-3-2-3-3-3-6z" class="B"></path><path d="M541 493l3 4v2c-3-2-3-3-3-6z" class="O"></path><path d="M554 530c2 0 2 0 3 1l12 16h0c-2-1-3-2-4-4h-1c0 2 2 3 3 5h0c-3-2-5-4-7-7 1-2-3-5-5-8l-1-3z" class="C"></path><defs><linearGradient id="r" x1="184.966" y1="203.34" x2="208.034" y2="197.16" xlink:href="#B"><stop offset="0" stop-color="#757575"></stop><stop offset="1" stop-color="#929091"></stop></linearGradient></defs><path fill="url(#r)" d="M230 228l-55-36v-1c13 7 27 8 39 15l3-1c6 6 10 14 14 21 2 2 3 5 4 7-2 0-7-3-9-4h4v-1z"></path><defs><linearGradient id="s" x1="200.455" y1="207.759" x2="209.045" y2="205.241" xlink:href="#B"><stop offset="0" stop-color="#979796"></stop><stop offset="1" stop-color="#bab8b9"></stop></linearGradient></defs><path fill="url(#s)" d="M222 220l-12-7c-2-2-4-4-7-5-2-1-6-3-7-5 1-1 3 0 4 1 2 0 4 1 6 1h2c1 1 1 2 3 3 1 1 2 2 4 2 2 1 3 2 5 3v3c0 1 0 0 1 2 0 0 0 1 1 2z"></path><path d="M217 205c6 6 10 14 14 21 2 2 3 5 4 7-2 0-7-3-9-4h4v-1c0-2-6-6-8-8-1-1-1-2-1-2-1-2-1-1-1-2v-3c-2-1-3-2-5-3-2 0-3-1-4-2-2-1-2-2-3-3l7 2-1-1 3-1z" class="B"></path><defs><linearGradient id="t" x1="630.736" y1="367.543" x2="638.628" y2="354.68" xlink:href="#B"><stop offset="0" stop-color="#545256"></stop><stop offset="1" stop-color="#6d6c6a"></stop></linearGradient></defs><path fill="url(#t)" d="M626 340c-1-3-3-6-4-9-2-3-7-10-6-13 0 1 1 1 2 1v-1c1 1 1 1 1 2l5 11 1-1c-1-2-1-3-2-4h1l7 10 1 3 3 3v1h-1c1 3 4 7 6 9 0 1 2 2 2 3h0l3 6v1s-1 0-1 1c0 2 2 3 0 6l1 2 1 5h1l-1 5c-1 2-1 3-3 4-1 1-1 0-1 1h-1c-1-2-1-3-2-4v-4h-1c0-6-2-11-4-17-3-7-5-14-8-21z"></path><path d="M628 340c4 2 5 4 6 8h0-1c-1 0-1 0 0 1l4 8-1 2c-2-7-6-12-8-19z" class="J"></path><path d="M639 378h1c2 1 3 3 5 3h1c-1 2-1 3-3 4-1 1-1 0-1 1h-1c-1-2-1-3-2-4v-4z" class="T"></path><path d="M636 359l1-2-4-8c-1-1-1-1 0-1h1l10 21 1 2c-1 2 0 3-1 4h-1c-1-1-2-3-2-4-2-4-4-8-5-12z" class="E"></path><path d="M624 331l1-1c-1-2-1-3-2-4h1l7 10 1 3 3 3v1h-1c1 3 4 7 6 9 0 1 2 2 2 3h0l3 6v1s-1 0-1 1c0 2 2 3 0 6l-10-21h0c-1-4-2-6-6-8-1-3-3-6-4-9z" class="B"></path><defs><linearGradient id="u" x1="661.723" y1="591.119" x2="630.614" y2="624.323" xlink:href="#B"><stop offset="0" stop-color="#949292"></stop><stop offset="1" stop-color="#f3f3f3"></stop></linearGradient></defs><path fill="url(#u)" d="M660 599l13-5-16 13c-3 3-6 7-10 7l1 1h0v1c-4 2-10 5-15 6l-7-2c1-1 1-2 1-4h0l1-3 3-7c3-1 5-1 7-3l20-6h0c1 0 2 0 3-1l1 1-2 1v1z"></path><path d="M658 597h0c1 0 2 0 3-1l1 1-2 1v1h0l-8 3c-5 2-10 5-14 7-3 1-4 2-6 4-1 0-2 1-3 1l-1-1 3-7c3-1 5-1 7-3l20-6z" class="F"></path><path d="M370 911c3 0 6-2 9-2 7-1 13-2 19-2 36-2 71-3 106 0h16l18 3c-5 2-11 0-16 0-5-2-12-2-18-2l-9-1h-11c-4 0-7-1-10-1h-33c-3 0-6 1-9 1h-17c-2 0-4 1-6 1l-13 1c-6 1-12 1-17 3l-1 1c1 1 2 2 4 2l26 3h11c1 0 3 1 4 1h41c18 0 36 0 54-3 3 0 11-1 14 0-29 4-57 4-86 4-8 0-17 1-24 0-5-1-9-1-14-1l-23-2c-5-1-11-1-16-3h1v-1-1h-1v-1h1z" class="B"></path><defs><linearGradient id="v" x1="439.341" y1="753.736" x2="452.967" y2="746.024" xlink:href="#B"><stop offset="0" stop-color="#bebcbd"></stop><stop offset="1" stop-color="#f3f2f2"></stop></linearGradient></defs><path fill="url(#v)" d="M430 733l8 3h0c0-1-1-2-1-3l-2-1-2-2c-1-1-2-1-3-2v-1c2 1 4 1 6 0l1 1 11 5 2 1 7 17 4 11h-1c-2-1-4-4-6-5l-4-3h-1c-2 0-6-2-7-4l-8-4-10-8 2 1 1-1h1c1 1 2 1 4 1l-12-6 1-1c1 0 3 1 5 2 0-1 1-1 2-1h2z"></path><path d="M424 738l2 1h0c1 1 2 1 3 1h1c2 1 5 3 8 4 0 1 2 2 3 2 0 1 1 3 1 3 1 1 0 1 0 1l-8-4-10-8z" class="J"></path><path d="M432 739l-12-6 1-1c1 0 3 1 5 2 1 0 2 1 4 1 3 1 5 2 8 4l11 6c2 1 4 2 5 4l-22-10z" class="D"></path><path d="M430 733l8 3h0c0-1-1-2-1-3l-2-1-2-2c-1-1-2-1-3-2v-1c2 1 4 1 6 0l1 1 11 5 2 1 7 17c-1-1-2-1-3-2-1-2-3-3-5-4l-11-6c-3-2-5-3-8-4-2 0-3-1-4-1 0-1 1-1 2-1h2z" class="F"></path><path d="M430 733l8 3h0c0-1-1-2-1-3l-2-1-2-2c-1-1-2-1-3-2v-1c2 1 4 1 6 0l1 1h-3c0 2 4 4 6 5 3 2 6 3 8 5-3 0-6 0-9-1-1 1-1 1-1 2-3-2-5-3-8-4-2 0-3-1-4-1 0-1 1-1 2-1h2z" class="B"></path><defs><linearGradient id="w" x1="576.996" y1="831.854" x2="531.496" y2="837.584" xlink:href="#B"><stop offset="0" stop-color="#c9c8c9"></stop><stop offset="1" stop-color="#f1f0ef"></stop></linearGradient></defs><path fill="url(#w)" d="M564 821c4 0 9-7 12-6 1 1 1 1 0 2l-16 19c-1 1-6 5-6 6v1c-7 5-15 7-21 13h-1l9-25c0 1 1 1 1 2 6-3 14-7 20-11l1-1h1z"></path><path d="M623 671l2 2c-4 2-9 5-11 9l1 1h0c3 0 6-3 9-4h3 2l-6 5 2-1c1 1 2 0 4-1l1 1-12 8c-8 6-18 11-28 16l1-3 11-27 1 1h0c2 0 5-1 6-2 2-1 3 0 5-1 3 0 7-2 9-4z" class="L"></path><path d="M627 679h2l-6 5-7 4c-3 0-7 3-9 4h-1 0v-1c7-4 13-9 21-12z" class="H"></path><path d="M623 684l2-1c1 1 2 0 4-1l1 1-12 8c-8 6-18 11-28 16l1-3 25-16 7-4z" class="I"></path><path d="M389 260l1 1c-2 2-3 4-5 6h1l2-2 1 1 1-1v1h0l1 1 2-2v3c-3 4-6 8-10 11-2 1-3 3-5 5v2h2l-2 2v1 1h-1c-2 2-3 3-4 5v1 2h1 0 1c0-1 0-2 1-3 0-1 1-1 2-2 0-1 1-1 2-2l1 1-1 1h0l2-2 2-2c3-2 6-6 10-7h0c-1 1-1 2-2 4h0 1c1 1 2-1 4-1h0v2c-1 1-2 2-3 4-2 3-5 6-8 9l-4 5h1l1-1 2 2-8 6-4-10h0l-3-8v-4c-1-1-1-1-1-2 0-2 2-4 4-5-1-1-1-2-1-3v-2c-2 0-3 1-4 2l-1 1v-1-1c1-1 3-1 4-3h1v1l2-2c3-3 7-6 10-10l4-5z" class="G"></path><path d="M374 302c1-1 3-2 5-4v1h2l-3 3h0-1-3 0z" class="L"></path><path d="M378 284v2h2l-2 2v1 1h-1c-2 2-3 3-4 5 0-1-1-3 0-4 0-2 3-5 5-7z" class="M"></path><path d="M381 299c1-1 2-1 3-2 5-3 9-8 13-12v2c-1 1-2 2-3 4-2 3-5 6-8 9l-1-1c-1 0-2 0-2 1-2 1-3 1-5 2l3-3z" class="C"></path><path d="M378 302c2-1 3-1 5-2 0-1 1-1 2-1l1 1-4 5h1l1-1 2 2-8 6-4-10h3 1 0z" class="G"></path><path d="M389 260l1 1c-2 2-3 4-5 6h1l2-2 1 1 1-1v1h0c-1 2-4 4-5 6s-5 6-7 6l-4 5c-1-1-1-2-1-3v-2c-2 0-3 1-4 2l-1 1v-1-1c1-1 3-1 4-3h1v1l2-2c3-3 7-6 10-10l4-5z" class="M"></path><defs><linearGradient id="x" x1="593.404" y1="782.048" x2="572.096" y2="769.952" xlink:href="#B"><stop offset="0" stop-color="#9c9a9a"></stop><stop offset="1" stop-color="#e3e2e2"></stop></linearGradient></defs><path fill="url(#x)" d="M574 772c2-1 5-3 7-4l15-9c3-2 6-4 9-5 0 1-1 2-2 2l-5 4v4l14-7c-1 2-3 3-5 4-6 7-15 13-23 18l1 1-8 4-7 4c-2 1-3 2-5 3-4 2-7 6-11 8l3-8 2-6c2-1 4-4 6-5h0c0-1 1-2 1-2l1-1c1 0 1-1 2-1h1l3-3 1-1z"></path><path d="M598 760v4c-1 1-3 1-4 1h0c1-2 2-3 4-5z" class="F"></path><path d="M577 784l1-1h0l-1-1 7-3 1 1-8 4z" class="O"></path><path d="M574 772c2-1 5-3 7-4l15-9c3-2 6-4 9-5 0 1-1 2-2 2-4 2-8 5-11 7-5 3-12 6-16 11-1 1-1 1-1 2 0 2-5 7-7 8-1 1-1 2-3 2-3 2-5 2-7 6v-1h-1l2-6c2-1 4-4 6-5h0c0-1 1-2 1-2l1-1c1 0 1-1 2-1h1l3-3 1-1z" class="G"></path><path d="M565 780h0c4-1 4-4 8-3-2 4-5 6-8 9-3 2-5 2-7 6v-1h-1l2-6c2-1 4-4 6-5z" class="F"></path><defs><linearGradient id="y" x1="688.973" y1="565.937" x2="646.513" y2="564.439" xlink:href="#B"><stop offset="0" stop-color="#9c9b9b"></stop><stop offset="1" stop-color="#f8f8f7"></stop></linearGradient></defs><path fill="url(#y)" d="M690 546c1-1 4-2 6-2 1-1 1 0 2-1h1l1 1h-1v1c-1 1-3 3-5 3l-16 15-6 6c-1 1-2 2-4 3-4 2-9 3-13 4 1 0 1 1 3 0h1l-1 1-1 1h0l-5 2c-2-1-4-1-6-1 1-1 2-3 3-5h0c1-2 2-4 3-5 0-1 0-2 1-2l1-2c1-1 2-2 2-4l1-1c2-1 3-2 5-3 1-1 2-1 3-2h1c0-1 1-1 2-1s1-1 2-1l8-2s1-1 2-1l4-1h1s1-1 2-1h0c2 0 2-1 3-2z"></path><path d="M690 546c1-1 4-2 6-2 1-1 1 0 2-1h1l1 1h-1v1c-1 1-3 3-5 3-4 2-8 4-13 5l-14 3c-1 1-3 1-5 2v-1c1-1 2-1 3-2h1c0-1 1-1 2-1s1-1 2-1l8-2s1-1 2-1l4-1h1s1-1 2-1h0c2 0 2-1 3-2z" class="F"></path><path d="M761 299c4-3 8-3 13-5 3-1 6-2 9-4 0 0 1 0 1 1 0 0 1 0 1 1l-1 1h0 1l-1 1c-1 1 1-1-1 0v1c-1 1-2 2-3 2l1 1 3-1 1 1v2c-1 0-2 1-3 2l-17 11v1c-1 1-1 0-2 1 1 0 1 0 2-1h3c-5 2-11 4-16 8v1c3 0 6-1 9-2s6-2 9-4c2 0 4-2 6-2v1s-1 0-2 1c-1 0-2 1-3 1l-1 1h1 0l-8 4c1 1 2 1 3 1h2c-2 2-4 3-7 4h-2c-2 0-4 1-6 1l-6 3h-1v-1c-1 1-2 1-3 1h0c6-6 7-15 11-22 2-4 5-7 7-11z" class="L"></path><path d="M746 331c1-1 4-2 6-3l11-5c1 1 2 1 3 1h2c-2 2-4 3-7 4h-2c-2 0-4 1-6 1l-6 3h-1v-1z" class="J"></path><defs><linearGradient id="z" x1="779.256" y1="292.122" x2="761.173" y2="316.792" xlink:href="#B"><stop offset="0" stop-color="#a6a5a5"></stop><stop offset="1" stop-color="#d7d6d7"></stop></linearGradient></defs><path fill="url(#z)" d="M784 293h1l-1 1c-1 1 1-1-1 0v1c-1 1-2 2-3 2l1 1 3-1 1 1v2c-1 0-2 1-3 2l-17 11c-3 1-10 6-12 5v-1c0-1 0-1 1-2 0-2 0-3 2-4 5-4 9-8 14-12 5-2 10-4 14-6h0z"></path><path d="M753 317c3 0 4-2 6-3 1-1 3-2 4-3l7-5c2-2 7-4 9-5 1 0 2 1 3 1l-17 11c-3 1-10 6-12 5v-1z" class="K"></path><defs><linearGradient id="AA" x1="363.226" y1="617.232" x2="393.398" y2="603.484" xlink:href="#B"><stop offset="0" stop-color="#9c9a9b"></stop><stop offset="1" stop-color="#eee"></stop></linearGradient></defs><path fill="url(#AA)" d="M370 605l-25-17 28 11c2 0 2 0 3-1 5 2 11 3 15 6h0l4 1 12 27c-2 0-3 0-4-1-4-1-8-1-11-4h0c-2-3-5-5-8-8 0-1-2-2-2-3 1 0 2-1 2-1h1l-4-2 1-1c-4-1-8-4-12-7z"></path><path d="M384 610l8 4c2 0 5 2 6 3-3 0-7-1-9-2 0-3-3-2-4-3 0 0-1-1-1-2z" class="B"></path><path d="M376 598c5 2 11 3 15 6h0 0 1c-1 1-1 1-1 2-6-2-11-5-18-7 2 0 2 0 3-1z" class="L"></path><path d="M370 605c3 0 3 0 5 1l1-1 1 1c1 2 5 3 7 4 0 1 1 2 1 2 1 1 4 0 4 3l-7-3c-4-1-8-4-12-7z" class="E"></path><path d="M385 615c3 1 8 2 10 5-3-1-5-3-9-3h0c3 3 6 6 9 8s6 3 8 5v1c-4-1-8-1-11-4h0c-2-3-5-5-8-8 0-1-2-2-2-3 1 0 2-1 2-1h1z" class="C"></path><path d="M693 472h1c1 0 2 0 3-1v1c-1 1-3 2-5 3h1l2-1 2 3v1h-1c-1 1-2 1-2 1h-1c-2 1-3 2-4 4v1c-1 1-1 1-1 2l1 1c2-1 3-2 5-3 0 0 1-1 2-1l1 1h1l1 1h3-1v4c0 1-1 1-2 1h0c-1 2-2 4-4 5l1 1c1-1 1-1 3-1-1 1-2 1-2 2-1 1-3 3-4 3-2 1-4 3-5 4h-1c-1 1-2 1-3 1l-4 3c-1 1 0 1-1 1-1 1-2 1-3 2l-1-1c-4 2-8 5-11 9h-4l3-3c1-2 6-4 7-6s1-5 2-7l10-25h0c3 1 8-5 11-6z" class="G"></path><path d="M698 484l1 1h3-1l-5 5-12 6-11 6c4-5 10-6 14-10 4-3 6-5 10-8h0 1z" class="E"></path><path d="M675 510c1-1 0-1 1-2l3-2 5-3c2-1 3-2 5-3h2c1-1 1-1 2-1l1-1h0c-3-1-6 0-9 0 4-3 10-7 14-8h0c-1 2-2 4-4 5l1 1c1-1 1-1 3-1-1 1-2 1-2 2-1 1-3 3-4 3-2 1-4 3-5 4h-1c-1 1-2 1-3 1l-4 3c-1 1 0 1-1 1-1 1-2 1-3 2l-1-1z" class="C"></path><path d="M568 495l-12-21c2 1 4 4 5 6l11 14 1-1-1-2h1c1 2 3 4 4 6l1-1c-1-1-2-3-3-5-2-3-4-3-4-7l3 3h0 1l1-2c0-2-2-5-3-7l1-2c1 1 1 2 2 3 1 3 4 6 6 9 1 1 1 1 1 2 3 4 9 6 13 9l-1 3-8 19-8-6-4-3-2-2c0-1-1-2-1-2l-2-2v-1l5 5c2 1 2 1 4 1-1-1-3-2-4-3-1-2-3-5-4-6l-1-1c0-1 0-2-1-2 0-1-1-2-1-4z" class="G"></path><path d="M571 484l3 3h0 1 0l6 14-3-5c-1-1-2-3-3-5-2-3-4-3-4-7z" class="E"></path><path d="M573 495c3 1 5 7 7 10l1 1c1 1 5 5 5 6 1 2 0 2 1 4h-1l-1-1 1-1c-6-5-10-13-13-19z" class="B"></path><path d="M573 478l1-2c1 1 1 2 2 3 1 3 4 6 6 9 1 1 1 1 1 2 3 4 9 6 13 9l-1 3-5-3c-1 1 1 3 1 5v1 1l-15-21c0-2-2-5-3-7z" class="P"></path><path d="M583 490c3 4 9 6 13 9l-1 3-5-3c-1 1 1 3 1 5l-8-13v-1z" class="B"></path><path d="M568 495l-12-21c2 1 4 4 5 6l11 14 1 1c3 6 7 14 13 19l-1 1-2-2c-3-2-5-4-7-7-3-3-5-7-8-10v-1z" class="C"></path><defs><linearGradient id="AB" x1="751.112" y1="344.545" x2="741.699" y2="339.133" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#ebeaea"></stop></linearGradient></defs><path fill="url(#AB)" d="M746 332h1l6-3c2 0 4-1 6-1v1 1c-1 0-1 0-1 1l-1 1h1 7l-3 3c-3 1-4 5-5 8v1l-4 3h1 0c2 0 3-1 5-1-1 1-1 2-2 2l-2 2c-1 0-2 2-3 2l-15 9c-2 1-4 4-5 4h-2-1c-1 0-3 0-4-1 5-11 8-22 17-31l1-1c1 0 2 0 3-1v1z"></path><path d="M735 354c1 0 1 1 2 0 2-1 2-2 4-2 0 1 0 1-1 1l-2 2h0 1c1 0 2-1 3-1l1-1h1c2-1 4 0 6-1 1-1 1-1 2 0l-15 9c-2 1-4 4-5 4h-2-1l6-11z" class="L"></path><path d="M743 332c1 0 2 0 3-1v1c-3 3-5 7-6 11l-5 11-6 11c-1 0-3 0-4-1 5-11 8-22 17-31l1-1z" class="X"></path><defs><linearGradient id="AC" x1="658.716" y1="268.413" x2="661.657" y2="302.799" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#f4f3f2"></stop></linearGradient></defs><path fill="url(#AC)" d="M650 269l1-1c-1-1-2-3-3-4 1 0 2 0 2 1h1c1-1-1-4-1-5 1 0 1 1 2 2l1-1h0 2v1c1 1 2 1 4 1 2 3 3 5 5 7 3 1 5 3 6 6 1 2 2 4 3 7v1 1c0 2 1 2 1 3v2l1 1-2 9c-1 1-1 2-1 3-2-1-4-3-6-3l-1-1-3-3c-2-2-5-4-7-7h0c0-2 0 1 0-2 1 1 0 1 1 1-1-2-3-4-5-6-1-3-2-5-3-7-2-3-4-7-6-10l2 1c-1-2 0-2 0-4l6 7z"></path><path d="M644 262l6 7c0 1 2 4 3 5 5 8 10 17 17 24h0c-5-4-9-10-12-15-5-6-9-12-14-17-1-2 0-2 0-4z" class="E"></path><path d="M653 261h0 2v1c1 1 2 1 4 1 2 3 3 5 5 7 3 1 5 3 6 6 1 2 2 4 3 7v1 1c0 2 1 2 1 3v2c-2-1-3-2-4-4l-1-1c-1-1-2-1-2-2-1-1-2-2-2-3l-5-5 1-1c-4-4-7-8-9-12l1-1z" class="H"></path><path d="M653 261h2v1c1 1 2 1 4 1 2 3 3 5 5 7l2 3c1 2 1 4 1 6-1 0-2-3-3-4-4-4-7-9-11-14z" class="I"></path><defs><linearGradient id="AD" x1="634.691" y1="668.178" x2="610.319" y2="657.099" xlink:href="#B"><stop offset="0" stop-color="#bfbebf"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#AD)" d="M643 647c5-2 9-4 14-6h0c-1 1-2 2-3 2-1 1-1 1-2 1l-3 2c-2 1-2 1-3 4 0 1 0 0-1 1h-1c-2 2-5 2-7 4-5 4-7 8-12 11l1 1h0c1-1 1-1 2-1l1 1h-1c-1 2-3 3-5 4-2 2-6 4-9 4-2 1-3 0-5 1-1 1-4 2-6 2h0l-1-1 10-24 2-2 17-7s0 1 1 1h1c2 0 3 1 5 1v1h0l1 1 4-1z"></path><path d="M624 652c-4 2-7 3-11 4h0l15-8c1 0 1 1 2 1-1 1-4 2-6 3z" class="C"></path><path d="M632 645h1c2 0 3 1 5 1v1h0l1 1-8 4h-1c-2-1-4 0-6 0 2-1 5-2 6-3-1 0-1-1-2-1l4-3z" class="B"></path><path d="M632 645h1c2 0 3 1 5 1v1h0c-1 0-3 1-4 1-2 0-2 0-4 1-1 0-1-1-2-1l4-3z" class="K"></path><path d="M628 667c-1 2-3 3-5 4-2 2-6 4-9 4-2 1-3 0-5 1-1 1-4 2-6 2h0c8-4 16-8 25-11z" class="D"></path><path d="M643 647v1c-1 1-2 2-3 2-1 1-2 1-3 2s-1 1-2 1c-7 2-13 5-20 7l10-6c2-1 4-1 5-2h1l8-4 4-1z" class="E"></path><path d="M643 647v1c-1 1-2 2-3 2h-1c-3 1-5 2-8 2h0l8-4 4-1z" class="I"></path><defs><linearGradient id="AE" x1="356.438" y1="569.819" x2="370.667" y2="557.908" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#AE)" d="M339 550l-12-10c7 3 14 5 22 7h3l4 2 3 1c1 0 2 0 2 1h1c3 3 6 4 9 6 5 4 8 11 10 17l6 12c-6 0-8-3-12-7-3-1-6-3-8-5l-9-7-19-17z"></path><defs><linearGradient id="AF" x1="344.125" y1="558.885" x2="356.907" y2="558.614" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#b2b1b0"></stop></linearGradient></defs><path fill="url(#AF)" d="M339 550c1 0 1 0 3 1 1 0 1-1 3 0 3 2 6 2 9 3l1 1c2 0 4 1 5 3v1c-1 1-2 0-3 0 0 3 1 5 1 7v1l-19-17z"></path><path d="M804 247c-1 4-8 8-12 10 0 1-1 1-2 2v1h1v1l20-6c-1 1-3 2-4 3s-2 3-4 3v1c-3 1-5 3-8 4h0c-3 1-5 3-7 5-5 6-8 11-15 16-1 0-2 0-2 1-2 2-3 3-5 4v2l-4 3c-1 0-1 0-1 1h-3 0c1-2 2-3 3-5 2-3 2-7 4-10 1-3 3-6 4-9 2-4 4-9 7-14 0 0 1-2 1-3 1-2 2-3 3-4h1c3-1 5-1 8-2 1-1 3-1 5-1 2-1 4-2 7-1l3-2z" class="O"></path><path d="M780 253h1c3-1 5-1 8-2 1-1 3-1 5-1 2-1 4-2 7-1-5 3-11 5-17 7-2 0-4 1-7 1 1-2 2-3 3-4z" class="Q"></path><path d="M791 261l20-6c-1 1-3 2-4 3h-1c-6 3-12 5-18 7-3 1-6 1-9 2-2 1-3 2-5 1h-1c1-1 3-1 5-2 2 0 3-2 5-2 3-1 5-2 8-3z" class="K"></path><path d="M766 292h-1v-1-2c0-5 4-13 8-15v-1c6-4 15-5 22-7-3 1-5 3-7 5-5 6-8 11-15 16-1 0-2 0-2 1-2 2-3 3-5 4z" class="C"></path><defs><linearGradient id="AG" x1="413.866" y1="684.09" x2="426.585" y2="679.305" xlink:href="#B"><stop offset="0" stop-color="#c6c5c5"></stop><stop offset="1" stop-color="#f0f0f0"></stop></linearGradient></defs><path fill="url(#AG)" d="M396 651c3 0 5 2 8 3 3 2 11 7 14 5l20 48 1 2c-3-1-6-3-9-5h0l-3-2c0-1-1-2-2-2 0-1-1-1-2-2v-1c-6-3-12-7-16-13l5 2 7 2c-2-1-4-2-5-3l-1-1v-1l-1-1c0-3-2-3-4-5h-1c-1-1 0-1-1-2h0c-2 0-2-1-3-2 4 1 8 3 11 5 2 2 8 6 10 6v-1c-1-2-6-4-8-5-3-2-6-4-9-7h-1v-1c-1-2-2-4-2-5-1-4-1-5-3-8h-3l-2-2h2c1 1 1 1 2 1l-1-1h-1c-1-1-3-1-4-2-1 0-1 0-2-1h2 2v-1z"></path><path d="M396 651c3 0 5 2 8 3l-1 1c2 1 8 3 9 4-2 1-8-1-11-2h-3l-2-2h2c1 1 1 1 2 1l-1-1h-1c-1-1-3-1-4-2-1 0-1 0-2-1h2 2v-1z" class="F"></path><path d="M396 651c3 0 5 2 8 3l-1 1c-3-1-6-1-9-3h2v-1z" class="C"></path><path d="M407 684l5 2c2 2 3 4 5 6 4 4 9 6 13 10 3 1 5 4 8 6v-1l1 2c-3-1-6-3-9-5h0l-3-2c0-1-1-2-2-2 0-1-1-1-2-2v-1c-6-3-12-7-16-13z" class="D"></path><path d="M672 521h1 3c2 0 2 1 3 2 1 3-1 6 1 9h0-1l-2 1v1l-13 5-1 1c3 0 6-1 9-2l8-2h0c3-1 7-3 9-2 3 0 5-2 7-2l-17 12c1 1 2 1 3 1l1-1h1 0c1 0 2-1 2-1l1 1c-1 0-2 1-3 1l-4 2h-1c-1 1-2 1-2 2 1 0 1 0 2-1 2 1 3 0 4 0s2-1 2-1h1l4-1c-1 1-1 2-3 2h0c-1 0-2 1-2 1h-1l-4 1c-1 0-2 1-2 1l-8 2c-1 0-1 1-2 1s-2 0-2 1h-1c-1 1-2 1-3 2-2 1-3 2-5 3l-1 1c-1 1-1 2-3 2v1l-2 1h-1l1-1h-1l-1-1 16-39h2 1c1-1 3-2 4-3z" class="L"></path><path d="M680 536v2c-1 0-3 1-4 2-3 2-6 6-8 9l-3 3c-1 2-3 2-5 3h-1c1-1 2-1 3-2l-1-1c-1 0-3 1-4 1h0c3-3 6-4 9-6s6-6 8-8h1c1-1 2-1 4-2h-1c-1 1-3 2-5 1h-1l8-2z" class="B"></path><path d="M689 534c3 0 5-2 7-2l-17 12c-5 3-9 6-14 8l3-3c2-3 5-7 8-9 1-1 3-2 4-2v-2h0c3-1 7-3 9-2z" class="N"></path><path d="M680 536h0c3-1 7-3 9-2-4 3-8 5-11 8s-6 6-10 7c2-3 5-7 8-9 1-1 3-2 4-2v-2z" class="E"></path><path d="M245 321l2 1c2 0 4 0 6 1s5 2 7 4l9 3c1 1 3 2 5 2 2 1 4 3 6 5 2 3 4 9 6 13 3 8 7 17 11 25-1 0-2 0-3-1 0-3-3-4-5-5l-5-2-3-1c-4-1-6-4-10-6-2-1-3-2-5-3 3 0 5 0 8 1h1c1 0 3 1 4 1 0-1 0-1-1-2h-1c-2-1-4-3-5-6l-10-5h6l-25-19c-1 0-2-2-3-3h0 1c-1-2-1-2-1-3 2 1 3 1 5 1v-1z" class="L"></path><path d="M247 322c2 0 4 0 6 1s5 2 7 4c-5 0-9-3-13-5z" class="D"></path><path d="M275 351v-1c-1-1-1-2-1-3 2 0 5 2 7 3 1 2 4 4 5 6-2-1-4-1-6-2l-5-3z" class="H"></path><path d="M272 351c6 3 12 6 16 11-1 0-3 0-4-1 0 0-4-1-5-2 0-1 0-1-1-2h-1c-2-1-4-3-5-6z" class="C"></path><path d="M266 357c3 0 5 0 8 1h1c1 0 3 1 4 1 1 1 5 2 5 2l-1 1 6 3h0l2 2h0c-1 1-1 1-2 0v2l-5-2-3-1c-4-1-6-4-10-6-2-1-3-2-5-3z" class="B"></path><path d="M281 366c-1-2-3-3-4-5 1 0 3 1 4 2 1 0 2 1 3 1l3 2 2-1h0l2 2h0c-1 1-1 1-2 0v2l-5-2-3-1z" class="G"></path><defs><linearGradient id="AH" x1="256.376" y1="341.486" x2="268.301" y2="337.454" xlink:href="#B"><stop offset="0" stop-color="#979697"></stop><stop offset="1" stop-color="#c6c4c4"></stop></linearGradient></defs><path fill="url(#AH)" d="M243 327c3 1 5 3 7 4 0-1-1-2-2-2l1-1 13 6h3c3 2 4 4 7 6v1l3 3c1 0 1 1 2 2 2 1 3 2 4 4-2-1-5-3-7-3 0 1 0 2 1 3v1c-3-1-5-3-7-5l-25-19z"></path><path d="M551 501c-2-4-3-9-4-14-2-5-3-9-4-15 3 4 4 9 7 14 1 0 1 1 2 2h0c1 4 5 7 6 11l6 8 1-1-1-1 2-2c2 3 4 5 7 7l2 2 4 3 8 6-2 3h0l-3 9-2 5c-1 2-1 2-1 3l-3 6c-2-2-4-5-6-7-9-12-13-25-19-39z" class="F"></path><path d="M555 500l4 6 8 14-1-1-1 1c-2-2-3-4-4-6h-1c-2-2-2-4-3-6-1-3-2-6-2-8z" class="J"></path><path d="M569 515c3 1 5 4 6 7 1 1 4 2 5 2 2 0 3-1 5 0l-3 9c-3-2-3-7-6-9s-5-6-7-9z" class="C"></path><path d="M566 503c2 3 4 5 7 7l2 2 4 3 8 6-2 3h0c-2-1-3 0-5 0-1 0-4-1-5-2-1-3-3-6-6-7l-5-8 1-1-1-1 2-2z" class="P"></path><path d="M575 512l4 3h0c0 2 1 3 2 4h-1c-3 0-4-1-6-3h0c1 0 0 0 1-1h0c1-1 0-2 0-3z" class="R"></path><path d="M566 503c2 3 4 5 7 7l2 2c0 1 1 2 0 3h0c-1 1 0 1-1 1-4-3-7-6-9-10l-1-1 2-2z" class="S"></path><defs><linearGradient id="AI" x1="581.228" y1="533.325" x2="547.772" y2="516.175" xlink:href="#B"><stop offset="0" stop-color="#2f2e2b"></stop><stop offset="1" stop-color="#5b5a5d"></stop></linearGradient></defs><path fill="url(#AI)" d="M551 501c-2-4-3-9-4-14-2-5-3-9-4-15 3 4 4 9 7 14 1 0 1 1 2 2h0c1 4 5 7 6 11 0 2 0 3 1 4v3l-4-6c0 2 1 5 2 8 1 2 1 4 3 6h1c1 2 2 4 4 6l1-1 1 1c3 5 7 14 13 18-1 2-1 2-1 3l-3 6c-2-2-4-5-6-7-9-12-13-25-19-39z"></path><path d="M549 488l6 12c0 2 1 5 2 8-3-3-4-6-6-10-1-3-2-6-2-10z" class="Q"></path><path d="M549 488l-3-6 3 3 1 1c1 0 1 1 2 2h0c1 4 5 7 6 11 0 2 0 3 1 4v3l-4-6-6-12z" class="B"></path><defs><linearGradient id="AJ" x1="577.328" y1="529.053" x2="559.172" y2="521.947" xlink:href="#B"><stop offset="0" stop-color="#898788"></stop><stop offset="1" stop-color="#a9a9a8"></stop></linearGradient></defs><path fill="url(#AJ)" d="M560 514h1c1 2 2 4 4 6l1-1 1 1c3 5 7 14 13 18-1 2-1 2-1 3-1 0-3 0-4-1-1 0-3-3-4-4-4-7-8-14-11-22z"></path><path d="M518 916c-18 3-36 3-54 3h-41c-1 0-3-1-4-1h-11l-26-3c-2 0-3-1-4-2l1-1c5-2 11-2 17-3l13-1c2 0 4-1 6-1h17c3 0 6-1 9-1h33c3 0 6 1 10 1h11l9 1c6 0 13 0 18 2 5 0 11 2 16 0 3 0 6 1 9 0 2-1 3-1 5 0h4c4 0 8 0 13 1v1c-1 2-1 2-3 2-2 1-3 1-4 1-4 1-9 2-13 2-1-1-3-1-5-1-1 1-2 1-3 1 2-1 4-1 7-2h0l-16 1c-3-1-11 0-14 0z" class="M"></path><defs><linearGradient id="AK" x1="542.429" y1="903.619" x2="559.074" y2="920.374" xlink:href="#B"><stop offset="0" stop-color="#d4ceca"></stop><stop offset="1" stop-color="#eceeef"></stop></linearGradient></defs><path fill="url(#AK)" d="M538 910c3 0 6 1 9 0 2-1 3-1 5 0h4c4 0 8 0 13 1v1c-1 2-1 2-3 2-2 1-3 1-4 1-4 1-9 2-13 2-1-1-3-1-5-1-1 1-2 1-3 1 2-1 4-1 7-2h0l-16 1c-3-1-11 0-14 0h-6c-5 0-9 1-14 1-3-1-5-1-8-1-5 1-12 1-18 0-3-1-8 0-12 0l11-1c3 1 7 1 10 0 3 1 6 1 9 1 4 0 9 0 13-1l1 1h8c2-1 5-1 7-1h2c3 0 12 0 15-2l-1-1c-4-1-8-1-12-2h-1c5 0 11 2 16 0z"></path><path d="M498 917c-8 1-16 1-24 1h-33c-6 0-15 1-21-1h-10l-21-3h-4v-1c1-1 3-2 4-2 5-1 9-1 14-1 4-1 8-1 11-1 8-2 16 1 24-1 6-2 15-1 22-1 6 0 13-1 19 1 5 1 10 0 16 0 2 0 3 1 5 1h7c5 0 11 1 16 2 1 0 3 0 4 2v1l-8 1c-2 0-5 0-7 1h-8l-1-1c5 0 11 0 16-2-4-2-9-2-13-3h-8c-1 0-2-1-3-1h-7c-5 0-10 0-15-1s-12 0-18 0c-5 0-12-1-16 1h-5-13c-2 0-4 1-5 1-4 1-7 0-11 1-3 0-5 0-8 1h-2v1c1 2 4 1 6 2h6c3 0 6 1 9 1 2 0 6-1 9 0 1 1 3 1 4 1h11l32-1c6 1 13 1 18 0 3 0 5 0 8 1z" class="K"></path><path fill="#bd9e93" d="M472 916l-32 1h-11c-1 0-3 0-4-1-3-1-7 0-9 0-3 0-6-1-9-1h-6c-2-1-5 0-6-2v-1h2c3-1 5-1 8-1 4-1 7 0 11-1 1 0 3-1 5-1h13 5c4-2 11-1 16-1 6 0 13-1 18 0s10 1 15 1h7c1 0 2 1 3 1h8c4 1 9 1 13 3-5 2-11 2-16 2-4 1-9 1-13 1-3 0-6 0-9-1-3 1-7 1-10 0l-11 1c4 0 9-1 12 0z"></path><path d="M426 915h-9c-3-1-6 0-8-2v-1h2c4-2 8 0 12-1 2-1 4-1 6-1 4 0 8 1 13 0 0 0 1 0 2-1h6c-2 1-3 1-5 2-5 1-12-2-17 1h-4v1c1 0 1 1 2 2zm51-5c3 0 12-1 16 1 2 1 6-1 9 1h2v1c-1 1-5 1-7 2-5 1-10-1-16 0-3 1-7 1-10 0h3c3-1 6-1 9-1h5c1-1 1-1 2-1h2c-1-1-4-1-5-1-1-1-2-1-3-1l-7-1z" fill="#ac8177"></path><path fill="#9e6058" d="M450 909l27 1 7 1c1 0 2 0 3 1 1 0 4 0 5 1h-2c-1 0-1 0-2 1h-5c-3 0-6 0-9 1h-3l-11 1c-6 0-13 0-20-1-5 0-9-1-14 0-1-1-1-2-2-2v-1h4c5-3 12 0 17-1 2-1 3-1 5-2z"></path><path d="M590 707c10-5 20-10 28-16v2c-3 2-7 5-11 7 3 0 6-2 8-3v1l-2 2c1-1 2-1 4-2v2c-1 1-2 2-3 4l-2 2v1c-1 1-3 3-5 4-2 2-4 5-7 7h0c1 1 4-1 6-1 1 0 2-1 3-1 4-2 7-3 10-4l7-3v2 1l-21 15v1h1 1c3 0 5-2 8-3 6-2 11-5 17-8h0c-2 3-6 5-9 7l-22 17h1c2-1 4-1 6-2l-3 3-1 1h1c2 0 4-1 6-2h1l-16 11c0 1 0 1 1 2l-15 11c-2 3-6 4-8 7l-1 1c-5 0-8 6-12 6l29-72z" class="F"></path><path d="M573 773c-1-1-1-1-1-2h0c0-2 3-3 5-4l1-1c1-1 2-1 4-1-2 3-6 4-8 7l-1 1zm42-76v1l-2 2c-3 1-5 3-7 4-3 3-4 4-8 5l4-4v-1c-1 1-2 1-3 1h-1c3-1 7-5 9-5 3 0 6-2 8-3z" class="O"></path><defs><linearGradient id="AL" x1="593.315" y1="755.176" x2="589.2" y2="751.308" xlink:href="#B"><stop offset="0" stop-color="#7c7b7d"></stop><stop offset="1" stop-color="#939190"></stop></linearGradient></defs><path fill="url(#AL)" d="M611 741h1l-16 11c-5 4-9 7-14 9-6 3-10 5-15 9v-1c1-3 17-11 20-14 2-1 7-5 9-6l8-6h1c2 0 4-1 6-2z"></path><path d="M606 717c1 0 2-1 3-1 4-2 7-3 10-4l1 2c-5 1-11 6-15 9-2 1-3 2-5 3v-1c1-1 3-2 4-3-2 1-3 1-5 0 1-1 0-1 1-1v-1c-3 2-9 6-13 6 4-3 9-5 12-9l1 1h0c1 1 4-1 6-1z" class="B"></path><path d="M626 709v2 1l-21 15-6 4c-4 2-8 4-12 5-1 1-4 3-6 3l19-13c2-1 3-2 5-3 4-3 10-8 15-9l-1-2 7-3z" class="P"></path><path d="M626 709v2l-6 3-1-2 7-3z" class="K"></path><path d="M602 741c2-1 4-1 6-2l-3 3-1 1-8 6c-2 1-7 5-9 6v-1c-2 0-4 1-6 1l-3 1c-2 1-4 1-7 1 2-2 4-3 6-4 3 0 4-2 6-3l18-9h1z" class="B"></path><path d="M602 741c2-1 4-1 6-2l-3 3-1 1-8 6c-2-1-4 1-6 2h-2c-3 1-5 2-7 3 3-3 6-4 9-6 1-1 3-2 5-3 2 0 5-2 7-4z" class="C"></path><defs><linearGradient id="AM" x1="603.365" y1="696.522" x2="599.925" y2="719.371" xlink:href="#B"><stop offset="0" stop-color="#b6b3b4"></stop><stop offset="1" stop-color="#ecedec"></stop></linearGradient></defs><path fill="url(#AM)" d="M617 698v2c-1 1-2 2-3 4l-2 2v1c-1 1-3 3-5 4-2 2-4 5-7 7l-1-1c-3-1-3 1-6 1-2 0-4 0-6-1v-1c1-2 4-2 6-3s4-3 5-4c4-1 5-2 8-5 2-1 4-3 7-4 1-1 2-1 4-2z"></path><path d="M607 728c3 0 5-2 8-3 6-2 11-5 17-8h0c-2 3-6 5-9 7l-22 17-18 9c-2 1-3 3-6 3 4-2 8-5 11-8 1-2 2-2 4-4 1-1 3-2 5-3h0c-4 0-8 2-11 3 3-2 6-5 9-7l12-6z" class="H"></path><path d="M588 745l6-3c2-1 4-3 6-5l8-6c2-1 3-2 5-2 1-1 3-1 4-2 2-1 4-3 6-3l-22 17-18 9c-2 1-3 3-6 3 4-2 8-5 11-8z" class="D"></path><path fill="#0f0f0f" d="M386 159h3 1c8 1 17 0 24 0h47c-14 4-28 7-42 13-3 1-8 3-11 6v1l-1 2c-1 0-1 1-2 1-1 1 0 0 0 2h-1c-2 4-5 7-7 10-6 8-13 17-20 25l-3 3c-2 2-3 4-4 5h-2l-1 5-1 3c-2 7-3 14-3 22v3 1c0 2 1 5 1 8l1 12c1 2 1 4 1 6v4c1-3 1-5 0-7 0-2 1-4 0-5v-4h0l2-1 1-1c1-1 5-3 7-3-2 1-1 2-1 3l-1 2h1l-2 2v-1h-1c-1 2-3 2-4 3v1 1l1-1c1-1 2-2 4-2v2c0 1 0 2 1 3-2 1-4 3-4 5 0 1 0 1 1 2v4l3 8h0l4 10v1c1 5 3 9 5 14l2 5v2l2 4 3 6 6 13c1 3 2 5 3 8l1 1 1 4 3 6 1 1 3 6 2 6 2 4c1 2 2 5 4 8l4 8 2 3v2l3 7 2 2 1 4 1 2 2 3 2 4 1 2 3 8 2 2 1 3c1 2 1 3 2 5l3 6 1 2c1 1 1 3 2 4l2 3 4 10 3 6 1 1 8 17 2 2 2 5 2 4 3 5 1 2 2 5 1 3 2 2 3 7 3 6 1 2 1 1h2c2 0 2-1 4-2l4-4c1-1 2-3 4-5l1 2c2-2 3-4 4-6h0l3-3c1-1 2-3 3-4 1-2 3-4 2-7h0c0-2 0-2 1-3l1-1c0-1 0-2 1-3 1 1 1 1 2 1l3-6 1-2c4 3 6 10 9 14v-1h0l-3-5 1-1c1 2 2 3 3 5 4 5 6 11 10 16 2-1 2-1 4-1l10 11 14 12v-2l3-7 3-6c0-1 0-1 1-3l2-5 3-9h0l2-3 8-19 1-3 2-5 2-5 2-5 4-8 1-3 3-7 6-16c1-1 1-3 2-3l1-3 4-11 3-8 1-1 2-5 1-3 3-9 1-1 3-7 1-2 1-4c1-2 2-5 3-7 0-1 0 0 1-1 2-1 2-2 3-4l1-5h-1l-1-5-1-2c2-3 0-4 0-6 0-1 1-1 1-1v-1l-3-6h0l2 2 1-2c2 0 3 3 6 3v1l2 1 3-8 2-6 1-1 4-13 3-6v-1l1-5c1-2 2-4 2-6v-1l1-2c0-3 1-5 2-8 0-1 0-2 1-3l2-9 1-2h0c5-12 5-41 1-53l-1-2v-1c-3-1-5-3-7-4l-2-2c-1-1-1-2-2-3-3-4-6-9-8-13l-11-16c-2-3-5-6-7-9 0-1-1-2-2-2 0-2 1-1 0-2s-2-1-2-3c-1-1-4-2-5-3-10-6-24-10-36-13-4-1-9-2-13-4h11c12-1 25 0 38 0h73 52c6 0 14-1 20 0h8 84 16c1 0 4 0 5 1-6 2-14 3-20 6-22 6-40 19-54 36l-3 2c-2 2-5 3-6 6-5 7-10 13-14 20l1 1c-2 1-3 2-4 4l-5 13-1 1c-1 1-1 2-2 4-1 1-2 2-3 4 0 1-1 3-1 3-3 5-5 10-7 14-1 3-3 6-4 9-2 3-2 7-4 10-1 2-2 3-3 5h0 3v1c-2 4-5 7-7 11-4 7-5 16-11 22h0l-1 1c-9 9-12 20-17 31l-3 6h0v1l4 1c1 0 1 1 2 2-2 3-4 7-6 10l-2 7-6 14c-1 1-2 2-2 4l-6 13c-1 1-2 2-2 3l-2 3-9 20c-1 1-2 3-2 5v-1l1 1c0 1-1 1-1 2 2 1 4-1 5-1l1-1c4-1 8-4 10-8 1-4 6-9 9-11h1l-1-1c1-1 2-1 3 0l-2 2-4 3c-1 1-1 3-2 4-1 2-3 3-4 5v1l-2 1v1l-1 1h1l2-1c-2 2-3 2-5 3-3 1-6 3-9 3-2 0-2 1-3 2l-6 15-2 5h0l-10 25c-1 2-1 5-2 7s-6 4-7 6l-3 3h4c3-4 7-7 11-9l1 1c-5 4-9 7-11 13l-16 39 1 1h1l-1 1h1l2-1v-1c2 0 2-1 3-2 0 2-1 3-2 4l-1 2c-1 0-1 1-1 2-1 1-2 3-3 5h0c-1 2-2 4-3 5 2 0 4 0 6 1l-9 5-1-1c-3 4-6 10-7 14l-4 8-3 7-1 3h0c0 2 0 3-1 4l7 2c-3 2-7 3-10 6h-1l-1 2-3 7-6 16-10 24-11 27-1 3-29 72c4 0 7-6 12-6l-3 3h-1c-1 0-1 1-2 1l-1 1s-1 1-1 2h0c-2 1-4 4-6 5l-2 6-3 8-1 2-7 16c-1 3-2 7-3 10l-1 1-1 3-9 25-6 17-8 21-15-36c-1-1-1-2-2-3l-5-12-9-22-1-2-10-22-4-10-1-1c-2-5-3-11-6-16l-4-8-4-11-7-17-11-25-1-2-20-48-1-3c0-1-1-2-1-3l-7-16-2-5-12-27-2-4-6-15-6-12c-2-6-5-13-10-17-3-2-6-3-9-6h-1c2 0 4 1 6 2 5 4 10 7 14 11 2 2 5 4 7 6 6 6 12 13 18 20 2 2 5 7 8 9h2c-11-17-27-33-44-45h1c-1-4-3-8-5-10l-10-25-1-2-3-5c-2-6-4-11-7-16-7-1-12-2-18-6l1-1-5-5c-1-1-3-2-4-3h4l1-1-8-6h0l4 2v-1l-1-1h1v-1l-18-15c1-1 3 0 4 0h1c1 1 0 0 2 1l18 8c3 1 5 3 7 4l-2-6-1-1-6-16 4 1-1-2h1v-3c-2-2-6-4-8-7-2-2-3-6-4-9l-2-4-3-8-2-4-1-3-3-5v-1c0-2-1-3-1-4l-10-24c-4-8-8-17-11-25-2-4-4-10-6-13-2-2-4-4-6-5v-1l5 4 1-2c-1 0-2-2-3-3l-5-11-7-19-2-5-10-22c-2 0-3-1-4-3-3-2-8-5-12-6-2-3-4-4-7-5l1-1v-2l2 1c1 1 2 2 3 2 1 1 2 2 3 2v-1c4 1 8 3 11 5h0c0-2-1-4-2-6s-3-4-5-6h1 1l-3-6-2-5c-1-2-2-4-4-6 0-1 0-1-1-3s-2-5-4-7c-4-7-8-15-14-21-14-18-35-31-57-38-7-3-16-4-23-7l8-1c8-1 16 0 24 0h49 168z"></path><path d="M410 604c3 1 4 0 5 3h-1c-1 0-3-2-4-3z" class="W"></path><path d="M716 355c1-2 3-2 5-2l-4 5-1-1v-2h0z" class="E"></path><path d="M364 488c1 0 2 0 3 1h0 5 0c-1 2-2 2-4 2-2-1-3-2-4-3z" class="W"></path><path d="M508 674c0 2 1 6 0 7 0 1-1 1-1 1v3l-1-8c0-2 1-2 2-3z" class="E"></path><path d="M713 251c1 0 2-1 3 0s2 2 4 2c-2 1-2 1-3 2-3-1-3-2-4-4z" class="T"></path><path d="M417 567c1 1 4 3 5 5-1 1-2 1-4 1 0-1-1-2-2-3l1-1v-2z" class="S"></path><path d="M668 368l7 4-2 1h-3c-1-2-2-3-2-5z" class="T"></path><path d="M284 172c1-2 2-2 4-1 1 0 2 0 3 1-1 1-2 2-4 2h0c-1-1-1-1-2-1-1-1 0-1-1-1z" class="P"></path><path d="M535 762l1-2c1-1 3-2 5-2 1 1 2 2 4 3-4 0-7 1-10 1z" class="V"></path><path d="M368 332l9 1c-1 1-1 2-2 2-1 1-2 1-3 1-2-1-3-2-4-4z" class="T"></path><path d="M339 208v1l-1 5-3 8v-1h-1-1c1-4 4-9 6-13z" class="H"></path><path d="M409 567c2 0 3 1 4 2 1 0 2 1 3 1 1 1 2 2 2 3-1 0-4 0-5-1-2-1-3-3-4-5z" class="U"></path><path d="M289 304c1 1 2 1 3 1 2 1 4 3 6 2l2 2h-1c-2 1-5 1-6 0-2-2-3-3-4-5z" class="S"></path><path d="M368 328c1 1 1 1 2 1 2 0 3 0 4 1s2 1 3 1v2l-9-1c1-1 0-3 0-4z" class="U"></path><path d="M333 221h1 1v1c-1 4-2 8-5 12h-1c1-5 2-9 4-13z" class="C"></path><path d="M279 297c0 1-1 2-1 4l-4 2c-1-1-2-2-3-4v-1c2 0 6 0 8-1z" class="U"></path><path d="M319 340l1 1c1 1 1 2 1 2 2 1 3 0 5 2h1 1v1c-1 0-2 1-2 1-3 0-4 0-6-2-2-1-2-2-2-5h1z" class="S"></path><path d="M360 484c2 0 4 0 5 1 3 1 4 2 7 2v2h-5 0c-1-1-2-1-3-1l-4-4z" class="U"></path><path d="M282 178c3 1 6-1 10-3 0 2 0 3-1 4-1 2-3 2-5 2s-3 0-4-1v-2z" class="S"></path><path d="M327 240c1 0 3 1 4 2 1 0 3 1 4 2-1 1-2 2-4 3-1 0-2-1-2-1-2-2-2-4-2-6z" class="T"></path><path d="M355 174l1-1c3-1 7-1 10 0l-2 2c-3 0-4 2-7 2l-2-3z" class="B"></path><path d="M411 561l2 2v1c1 1 2 2 4 3v2l-1 1c-1 0-2-1-3-1-1-1-2-2-4-2 0-2 0-4 2-6z" class="R"></path><path d="M284 172c1 0 0 0 1 1 1 0 1 0 2 1-2 1-4 2-6 4 4-1 7-3 10-3h1c-4 2-7 4-10 3-1-1-1-1-1-3s1-2 3-3z" class="D"></path><path d="M649 574h0c-1 2-2 4-3 5 2 0 4 0 6 1l-9 5-1-1c1-3 4-7 7-10zm-185 56h1c1 0 2 1 3 2s1 2 0 4h-2-2c0-1-1-1-2-2h0l-1-1c1-2 1-2 3-3z" class="V"></path><path d="M563 561c2-1 5-2 7-1h1l-3 8-5-7z" class="D"></path><path d="M513 691c0 1-1 2-1 4-1 1-2 1-3 1-2 0-3 0-4-2-1-1-1-2 0-4l1 1h5 2z" class="T"></path><path d="M627 616h0c0 2 0 3-1 4l7 2c-3 2-7 3-10 6h-1l5-12z" class="F"></path><path d="M341 219l-4 17h-1l-1-2c-1-3 0-6 1-9v3 1-1c2-3 3-6 5-9z" class="I"></path><path d="M711 247h2c1-1 1-1 3-1 1 1 1 2 3 2l1-1c1 2 1 4 0 6-2 0-3-1-4-2s-2 0-3 0c-1-2-1-3-2-4z" class="U"></path><path d="M500 817c1 3 1 10-1 13 0 1-1 1-2 1-2 0-2 0-3-1-1-2-1-4-1-5h2 4 1v-3-5z" class="S"></path><path d="M389 536c2-1 3-1 4 0s2 2 2 3c0 2 0 3-2 4-1-1-5-3-6-4 1-2 1-2 2-3z" class="V"></path><path d="M411 561c1-1 2-2 4-3 3 0 5 1 7 3-2 1-4 2-6 4l-3-2-2-2z" class="B"></path><path d="M691 176c1 2 1 6 0 8l-1 1v5c-2-2-3-4-4-7 0-1 0-2 1-3 0 1 0 1 1 1l-1-1 1-1h1c0-2 1-2 2-3z" class="R"></path><path d="M422 561c2 2 3 3 2 6 0 1 0 2-1 3-1 0-5-4-7-5 2-2 4-3 6-4z" class="N"></path><path d="M313 372c4 3 8 6 13 6v3c-1 2-1 1-3 2-4-2-8-7-10-11z" class="U"></path><path d="M348 280c1 0 1 1 2 2 0 2 0 4-2 6-1 1-3 2-5 2-1 0-3-1-3-1l-1-1c3-1 8-4 9-7v-1h0z" class="P"></path><path d="M348 280l-9 7c-1-1-1-1-1-2 0-2 1-4 2-6 2-1 3-1 5-1 1 0 2 0 3 2h0z" class="D"></path><path d="M557 582c0-2-2-5-3-7v-1l2 2 3 3h2l2-2h1l-5 12c0-2-1-4-2-6v-1z" class="H"></path><path d="M696 279c2-1 3-1 4-1 2 1 3 2 4 3 2 2 2 4 1 6h-1c-2-2-5-5-8-6h-1v-1l1-1z" class="I"></path><path d="M694 385l1 1c3-2 6-5 10-6-2 4-3 7-6 10-2 0-2 0-4-1-1-1-1-2-1-3v-1z" class="U"></path><path d="M683 173c2 0 5-1 8 1v2c-1 1-2 1-2 3h-1l-1 1 1 1c-1 0-1 0-1-1l-3-3c-1-1-3-2-4-4h3z" class="H"></path><path d="M680 173h3 5c-1 1-2 1-3 1-1 1-1 2-1 3-1-1-3-2-4-4z" class="B"></path><path d="M656 561c0 2-1 3-2 4l-1 2c-1 0-1 1-1 2l-6 7-2-1 5-12 1 1h1l-1 1h1l2-1v-1c2 0 2-1 3-2z" class="F"></path><path d="M526 627c1 1 2 3 2 4 1 3 5 6 6 9l3 3-2 6-7-15c0-1 0-1-1-2 0-2-1-2-2-3l1-2z" class="G"></path><path d="M669 479l1 3c-5 6-9 14-15 20v-1c3-7 9-15 14-22z" class="F"></path><path d="M513 782h2c2-1 2 0 3 1 1 2 1 3 1 5-1 1-1 1-3 2-1-1-1-2-3-2-1 0-1 0-2-1v-2c0-2 1-2 2-3z" class="V"></path><path d="M669 331c1 6 0 14 1 21l-1 2c-1 1-1 6-1 7l-1-1 1-29h1z" class="N"></path><path d="M749 201c0-2 1-4 2-5s3-2 4-1c2 0 4 1 5 2 0 1-1 1 0 2h0l-3 1-1 1h-1-6 0z" class="E"></path><path d="M749 201c4-2 7-3 11-4 0 1-1 1 0 2h0l-3 1-1 1h-1-6 0z" class="T"></path><path d="M253 245c-4-9-10-17-16-26h0c6 6 11 14 16 21 2 2 3 4 4 7v1l-1-1c-1-1-1-1-3-2z" class="H"></path><path d="M439 657v2c1 2 1 6-1 8-1 1-1 1-3 1l-2-2c-2-3-2-11-1-15v3 6l1-1h1c1 2 3 2 5 4h0v-6z" class="U"></path><path d="M577 652h1l12-10-7 16v-6c-2 2-2 2-4 3-3-1-2-1-3-3h1z" class="B"></path><path d="M556 707h0c6-4 10-13 14-19l-8 21-3 3v-4c1-1 1-1 1-2-1 1-2 1-4 1h0z" class="H"></path><path d="M766 258c-2 3-3 8-6 9-1 1-2 0-3 0-1-1-2-2-2-3s0-2 1-3h0c3-1 7-2 10-3z" class="U"></path><path d="M674 343l2 20-1 1h-4v-4h-1c-1-1-1-4-1-6l1-2v2c1 1 1 2 2 4 0-1 1-2 1-3v-2c1-3 1-7 1-10z" class="I"></path><path d="M364 290c-1-2 0-6-1-9 0-8-2-17-2-25h0l1 1h1v3 1c0 2 1 5 1 8l1 12c1 2 1 4 1 6h-1v4l-1-1z" class="P"></path><path d="M247 175h1c1 0 3 0 4 1s2 3 2 4c0 2-1 3-2 4-3 0-4-3-7-4h-1v-2c0-1 1-2 3-3z" class="O"></path><path d="M693 282h2l6 4c1 1 3 2 4 3l-1 1c-2 1-3 2-5 1-2 0-3-1-4-2-2-3-2-4-2-7z" class="U"></path><path d="M545 761v2c0 2 0 3-1 5-1 1-3 1-4 1-2 0-4-1-4-2-1-1-1-1-1-2-1-1 0-2 0-3 3 0 6-1 10-1z" class="N"></path><path d="M545 761v2c-2 0-5 1-7 0-2 1-1 1-3 0v2c-1-1 0-2 0-3 3 0 6-1 10-1z" class="S"></path><path d="M740 333l3-1h0l-1 1c-9 4-16 13-21 20-2 0-4 0-5 2l-2-1c8-8 16-15 26-21z" class="M"></path><defs><linearGradient id="AN" x1="364.678" y1="401.687" x2="356.822" y2="392.813" xlink:href="#B"><stop offset="0" stop-color="#646264"></stop><stop offset="1" stop-color="#868686"></stop></linearGradient></defs><path fill="url(#AN)" d="M357 375h1c1 4 2 9 3 13v1c0 2 1 4 1 6 2 3 2 8 1 11v1h-2l-4-32z"></path><path d="M760 199l2 1c0 1 0 2-1 4-1 1-3 2-4 3-2 0-4 0-5-1-2-1-3-2-3-4v-1h6 1l1-1 3-1zm-93 161l1 1c0-1 0-6 1-7 0 2 0 5 1 6h1v4h4l1-1c0 3 0 6-1 9l-7-4c0-2-1-6-1-8z" class="S"></path><path d="M494 783c-1-9-2-18-4-27 1 2 1 5 2 7 2 7 4 14 5 21 0 3 1 6 1 8l-1 1v5h-1c0-3 0-7-1-10v-1c0-1-1-2-1-4z" class="C"></path><path d="M318 340c0-1 1-2 2-3s3-2 5-1c1 0 2 1 3 2l1 2v1c1 1 0 2 0 3l-1 1h-1-1c-2-2-3-1-5-2 0 0 0-1-1-2l-1-1h-1z" class="I"></path><path d="M319 340c2 0 7-1 9 0v1 1c-2 0-5 0-8-1l-1-1z" class="T"></path><path d="M359 438c1 0 2 0 4 1 1 0 2 1 3 2 0 2 0 4-1 5 0 1-1 2-2 2-1-1-1-1-2-1l-6-3c0-1 0-2 1-3 0-2 1-2 3-3z" class="O"></path><path d="M714 354l2 1h0c-8 8-12 19-20 27v-1c5-10 11-18 18-27z" class="F"></path><path d="M528 631h2v-1l4 3h1c2 0 2 3 4 3 1-2 1-3 0-5 1 1 2 1 2 2l-4 10-3-3c-1-3-5-6-6-9z" class="B"></path><path d="M652 507h0l1-1c3-2 5-3 9-3-2 4-3 9-6 11-2 0-3 1-5 0v-5c0-1 0-1 1-2z" class="U"></path><path d="M634 540c7-10 19-18 29-24l-3 3c-7 6-14 13-22 19-1 0-3 2-4 2z" class="L"></path><path d="M519 839c2 1 3 3 5 5l-2 3c-1 2-2 4-4 5-2 0-2 0-3-1s-1-3-1-4 1-2 1-3c2-1 3-3 4-5z" class="W"></path><path d="M519 839c2 1 3 3 5 5l-2 3-3-2c-1 1-3 1-5 2 0-1 1-2 1-3 2-1 3-3 4-5z" class="S"></path><path d="M592 636h0l8-5c-1 1-2 3-3 4-2 1-3 2-5 3-4 3-9 7-13 11-1 1-2 2-2 3h-1c0-1 0-1-1-2h0c1-5 8-8 11-10l6-4z" class="M"></path><path d="M601 658v3c-3 6-7 13-10 18-2 3-5 5-8 6h-1v-1c1-1 1-2 2-3 3-2 5-5 7-9 1 0 8-12 10-14z" class="T"></path><defs><linearGradient id="AO" x1="491.237" y1="810.548" x2="503.481" y2="798.694" xlink:href="#B"><stop offset="0" stop-color="#9b9797"></stop><stop offset="1" stop-color="#c9cbcc"></stop></linearGradient></defs><path fill="url(#AO)" d="M496 798h1v-5l1-1c1 6 2 12 2 18v7 5c-1-1-1-2-3-2h0 0c-2-2-1-9-1-11v-11z"></path><path d="M496 809v1c1 2 1 4 1 6l1 1 1-1 1-6v7 5c-1-1-1-2-3-2h0 0c-2-2-1-9-1-11z" class="I"></path><path d="M289 304c0-2 1-3 2-5v-1c2-1 3-2 5-1 2 0 4 2 5 4 1 1 1 2 1 4l-1 2-1 1-9-8v1c2 2 4 4 7 6-2 1-4-1-6-2-1 0-2 0-3-1z" class="N"></path><path d="M231 256l2 1c1 1 2 2 3 2 1 1 2 2 3 2 4 1 7 3 10 4 0 1 2 1 2 2 1 1 1 3 2 4v2c-2 0-3-1-4-3-3-2-8-5-12-6-2-3-4-4-7-5l1-1v-2z" class="F"></path><path d="M719 204c2-1 4-1 6 0 1 1 2 2 2 3 1 2 1 4 0 6-1 1-2 2-3 2l1-1v-2-1l-8 2v-1c-1-1-1-3-1-4 1-2 2-3 3-4z" class="G"></path><path d="M584 670l5-7c-1 3-2 7-2 10 0 2-4 5-4 7l1 1c-1 1-1 2-2 3v1l-2-1c-1-1-2-1-2-3-1-3 2-6 3-8l3-3z" class="U"></path><path d="M584 670c-1 3-2 7-3 11v2c0 1 0 0 1 1v1l-2-1c-1-1-2-1-2-3-1-3 2-6 3-8l3-3z" class="W"></path><path d="M532 815v1l-6 12c-1 1-3 3-3 4 1 2 1 6 3 8l-2 4c-2-2-3-4-5-5-1 2-2 4-4 5 4-11 11-20 17-29z" class="G"></path><path d="M523 832c1 2 1 6 3 8l-2 4c-2-2-3-4-5-5l4-7z" class="N"></path><defs><linearGradient id="AP" x1="331.125" y1="237.625" x2="334.616" y2="229.478" xlink:href="#B"><stop offset="0" stop-color="#4a4a4a"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#AP)" d="M338 214c0 2 0 3-1 5l-1 6c-1 3-2 6-1 9l1 2h1c0 2-1 5-2 8-1-1-3-2-4-2-1-1-3-2-4-2 1-2 1-4 2-6h1c3-4 4-8 5-12l3-8z"></path><path d="M508 719v-3c1-1 2-3 3-3 2-1 4-1 5 0 2 1 2 2 3 3 0 2 0 3-1 5 0 2-2 2-3 3-2 0-3 0-5-1-1-1-2-2-2-4z" class="S"></path><path d="M508 719v-3c1-1 2-3 3-3 2-1 4-1 5 0 2 1 2 2 3 3v3h-1l-1 1c0 1-1 1-2 1h-1v-1c2-2 2-4 2-6h-1c-1 1-3 3-4 5-1 0-2-1-3 0z" class="B"></path><defs><linearGradient id="AQ" x1="489.031" y1="813.229" x2="500.598" y2="804.064" xlink:href="#B"><stop offset="0" stop-color="#6d6e6d"></stop><stop offset="1" stop-color="#a09f9f"></stop></linearGradient></defs><path fill="url(#AQ)" d="M494 783c0 2 1 3 1 4v1c1 3 1 7 1 10v11c0 2-1 9 1 11h0 0c2 0 2 1 3 2v3h-1-4-2l1-42z"></path><path d="M509 673c3 3 4 7 6 10h0c0 3-1 5-2 8h-2-5l-1-1 2-5v-3s1 0 1-1c1-1 0-5 0-7l1-1z" class="C"></path><path d="M257 248v-1c8 13 15 28 20 42h-1l-1-1c-1 0-1-1-2-1l-16-39z" class="F"></path><path d="M262 199c1-1 2-1 4-1 1 0 2 0 3 1v1c2 1 2 3 2 5s-1 3-2 4-2 1-4 1c0-2 0-2-1-4h-1l-1 2 1 1c-1 0-2 0-2-1-2-1-2-3-2-5s1-3 3-4z" class="J"></path><path d="M262 199c1-1 2-1 4-1 1 0 2 0 3 1-1 1-3 2-4 3h-1c-2-1-2-1-2-3z" class="F"></path><path d="M562 709c3 0 6-2 8-3s3-1 5 0c1 0 3 1 3 3 1 0 0 0 1 1 0 1 0 3-1 4 0 1-1 2-2 3-1 0-3 0-4-1s-2-2-3-4l-1-2c-1-1-3 0-3 0h-2c-2 1-4 3-7 4h0l3-2 3-3z" class="R"></path><path d="M575 706c1 0 3 1 3 3 1 0 0 0 1 1h-1c-2 0-4 0-6-1 2-1 2-2 3-3z" class="Q"></path><path d="M644 357l1-2c2 0 3 3 6 3v1l2 1c0 1-1 2-1 3l-5 13h-1l-1-5-1-2c2-3 0-4 0-6 0-1 1-1 1-1v-1l-3-6h0l2 2z" class="H"></path><path d="M644 357l1-2c2 0 3 3 6 3v1l2 1c0 1-1 2-1 3l-8-6z" class="S"></path><defs><linearGradient id="AR" x1="364.549" y1="411.566" x2="365.915" y2="400.231" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#AR)" d="M362 387l3 11c0 1 0 3 1 4 0 0 1 1 2 1s0 0 1-1c1 3 1 7 0 10 0 2-2 2-3 3-2 0-2-1-3-2-1-2-2-3-2-5v-1h2v-1c1-3 1-8-1-11 0-2-1-4-1-6v-1c1 0 1 0 1-1z"></path><defs><linearGradient id="AS" x1="772.686" y1="242.096" x2="767.314" y2="239.404" xlink:href="#B"><stop offset="0" stop-color="#cccacc"></stop><stop offset="1" stop-color="#f3f3f2"></stop></linearGradient></defs><path fill="url(#AS)" d="M790 216v1l-3 3v1c0 1 0 1-1 1-2 1-4 3-5 6l-2 4-8 10c-5 5-8 13-14 17 1-4 3-7 5-10 5-7 10-14 16-20 3-5 7-9 12-13z"></path><path d="M592 159c12-1 25 0 38 0h73 52c6 0 14-1 20 0h8c0 2 0 3-1 4-3 2-6 1-9-1 0-1-2-2-2-2-1-1-3 0-4 0h-13-104l-58-1z" class="D"></path><path d="M670 482c0-1 1-2 2-3l2 1c-5 8-9 15-12 23-4 0-6 1-9 3l-1 1h0c1-2 1-4 3-6v1c6-6 10-14 15-20z" class="J"></path><defs><linearGradient id="AT" x1="702.539" y1="372.068" x2="713.72" y2="363.613" xlink:href="#B"><stop offset="0" stop-color="#7d7b7b"></stop><stop offset="1" stop-color="#9f9ea0"></stop></linearGradient></defs><path fill="url(#AT)" d="M716 355v2l1 1c-4 7-10 14-12 22-4 1-7 4-10 6l-1-1c1-1 1-2 2-4v1c8-8 12-19 20-27z"></path><path d="M542 735c9 3 8-12 14-14l-10 28c-1-2-2-5-4-8 0-1-1-3-1-4v-2h1z" class="O"></path><defs><linearGradient id="AU" x1="763.62" y1="253.596" x2="776.795" y2="237.136" xlink:href="#B"><stop offset="0" stop-color="#888687"></stop><stop offset="1" stop-color="#abaaab"></stop></linearGradient></defs><path fill="url(#AU)" d="M757 259c6-4 9-12 14-17l8-10 1 3c-5 7-10 14-14 23-3 1-7 2-10 3h0l1-2z"></path><path d="M280 333c16 10 33 25 43 41h-1c-12-10-22-25-36-34l-7-5 1-2z" class="O"></path><path d="M790 216c7-8 15-15 22-21 3-2 6-6 9-7h1l-2 2c-4 3-8 7-11 10-7 7-13 13-19 20l-10 15-1-3 2-4c1-3 3-5 5-6 1 0 1 0 1-1v-1l3-3v-1z" class="K"></path><path d="M660 519h4l-24 24c-2 2-9 2-11 3 1-2 3-6 5-6 1 0 3-2 4-2 8-6 15-13 22-19z" class="P"></path><path d="M331 444c14 11 28 25 39 38-4 0-18-15-21-18l-6-6h-1c-2-1-4-4-6-4-2-2-4-3-5-5l-1-2h1v-3z" class="O"></path><path d="M330 447h1l9 8c0 1 2 2 3 3h-1c-2-1-4-4-6-4-2-2-4-3-5-5l-1-2zm141 259c-2-10-3-20-1-29 1-8 4-14 7-20h1c-5 11-6 21-6 33 1 6 3 12 5 18-1-1-1-1-2-1v1 1 1 1c-2-4-3-8-4-12h0c-1 1-1 1-1 2l1 1v1 3z" class="N"></path><path d="M471 699c0-3-1-13 1-15v6h0c1 6 3 12 5 18-1-1-1-1-2-1v1 1 1 1c-2-4-3-8-4-12z" class="K"></path><defs><linearGradient id="AV" x1="705.559" y1="229.547" x2="715.441" y2="215.953" xlink:href="#B"><stop offset="0" stop-color="#d1d2d0"></stop><stop offset="1" stop-color="#f6f4f7"></stop></linearGradient></defs><path fill="url(#AV)" d="M691 184c13 17 23 38 28 59h-1c-1-2-2-3-3-4-2-4-3-9-5-14-2-7-5-14-8-21-2-4-4-9-7-12l-5-7 1-1z"></path><path d="M691 455c2 1 4-1 5-1l1-1c4-1 8-4 10-8 1-4 6-9 9-11h1l-1-1c1-1 2-1 3 0l-2 2-4 3c-1 1-1 3-2 4-1 2-3 3-4 5v1l-2 1v1l-1 1h1l2-1c-2 2-3 2-5 3-3 1-6 3-9 3-2 0-2 1-3 2-4 1-14 18-16 22l-2-1c-1 1-2 2-2 3l-1-3h0c3-5 17-23 22-24z" class="M"></path><path d="M408 178v1l-1 2c-1 0-1 1-2 1-1 1 0 0 0 2h-1c-4 4-7 8-10 11-4 4-9 8-13 12-2 1-6 8-7 8 7-15 20-29 34-37z" class="O"></path><defs><linearGradient id="AW" x1="460.945" y1="584.98" x2="464.387" y2="592.551" xlink:href="#B"><stop offset="0" stop-color="#828080"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#AW)" d="M465 583c1 3 4 7 5 10h-1c0 3 0 7-1 11h-3c-1 0-2 0-3-1-1-4-4-15-2-18 1-1 3-1 5-2z"></path><path d="M465 604v-2c-1-2-2-3-3-5l1-1c1 0 1 1 2 1 1-1 2-1 2-2s1-2 1-3l1 1c0 3 0 7-1 11h-3z" class="M"></path><path d="M635 179c12 7 22 18 29 29 2 3 5 7 6 11h0l-2-2-1-1c-1-2-3-4-5-6l-16-16c-2-3-4-6-7-8 0-1-1-2-2-2 0-2 1-1 0-2s-2-1-2-3z" class="O"></path><path d="M286 340c14 9 24 24 36 34h1c2 1 2 3 3 4-5 0-9-3-13-6-4-6-9-13-14-18-4-4-8-7-12-11 0-1-1-2-1-3z" class="N"></path><defs><linearGradient id="AX" x1="674.17" y1="181.021" x2="694.178" y2="202.15" xlink:href="#B"><stop offset="0" stop-color="#454544"></stop><stop offset="1" stop-color="#706e6f"></stop></linearGradient></defs><path fill="url(#AX)" d="M690 190v8l-1 17v-1l-2-8-6-25c-1-1-2-5-2-6s1-1 1-2c1 2 3 3 4 4l3 3c-1 1-1 2-1 3 1 3 2 5 4 7z"></path><path d="M374 215c1 0 5-7 7-8 4-4 9-8 13-12 3-3 6-7 10-11-2 4-5 7-7 10-6 8-13 17-20 25l-3 3c-2 2-3 4-4 5h-2c1-4 3-9 6-12z" class="J"></path><path d="M595 650l1 1-19 22c-8 10-14 22-23 31v-1c1-8 9-17 14-23s9-12 14-16l13-14z" class="I"></path><defs><linearGradient id="AY" x1="350.446" y1="479.187" x2="355.988" y2="464.982" xlink:href="#B"><stop offset="0" stop-color="#515152"></stop><stop offset="1" stop-color="#7d7b7a"></stop></linearGradient></defs><path fill="url(#AY)" d="M336 454c2 0 4 3 6 4h1l6 6c3 3 17 18 21 18 1 2 2 3 2 5-3 0-4-1-7-2-1-1-3-1-5-1l-24-30z"></path><path d="M366 173s0 1 1 1c0 2-1 5-2 7l-4 22c-1 3-1 7-2 10h0c-1-9-3-19-2-29v-1c2-3 5-5 7-8l2-2z" class="R"></path><defs><linearGradient id="AZ" x1="529.167" y1="657.522" x2="534.175" y2="646.742" xlink:href="#B"><stop offset="0" stop-color="#989696"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#AZ)" d="M524 631c2 2 3 8 5 8h0 0l8 15c1 1 3 4 3 6v3c-1 1-1 2-2 2-5-3-7-11-10-15l-8-17h4v-2z"></path><path d="M365 291v-4h1v4c1 8 3 17 5 25 1 2 2 3 4 4v-3l2 11v3c-1 0-2 0-3-1s-2-1-4-1c-1 0-1 0-2-1l-1-5-3-33 1 1z" class="S"></path><path d="M371 316c1 2 2 3 4 4v-3l2 11-2-1c-2-2-3-8-4-11z" class="J"></path><defs><linearGradient id="Aa" x1="371.015" y1="323.253" x2="363.156" y2="302.054" xlink:href="#B"><stop offset="0" stop-color="#636062"></stop><stop offset="1" stop-color="#838482"></stop></linearGradient></defs><path fill="url(#Aa)" d="M364 290l1 1c1 5 2 11 3 17 2 5 2 12 2 17-1 0-2-1-3-2l-3-33z"></path><defs><linearGradient id="Ab" x1="407.264" y1="605.19" x2="406.724" y2="581.787" xlink:href="#B"><stop offset="0" stop-color="#383939"></stop><stop offset="1" stop-color="#666364"></stop></linearGradient></defs><path fill="url(#Ab)" d="M381 564c2 2 5 4 7 6 6 6 12 13 18 20 2 2 5 7 8 9h2c1 1 1 2 2 4 0 1-1 2-1 3l-2 1c-1-3-2-2-5-3-7-9-13-20-20-29-3-3-8-7-9-11z"></path><path d="M506 595v1l2 2v1l1 2c0 1 1 2 2 3h1c-1-2-1-4-2-6l1-1h0l1 1c0 1 1 2 1 3 0 2 1 4 2 6 1 5 3 10 7 15 1 2 3 3 4 5l-1 2c1 1 2 1 2 3 1 1 1 1 1 2 0 2 1 3 1 5h0c-2 0-3-6-5-8v2h-4l-8-20c-1-2-2-5-3-7-1-4-2-7-3-11z" class="M"></path><path d="M524 631c-1-1-4-5-3-7h1l1 2c1 1 1 2 2 2v1c1 1 2 1 2 3 1 1 1 1 1 2 0 2 1 3 1 5h0c-2 0-3-6-5-8z" class="B"></path><defs><linearGradient id="Ac" x1="701.355" y1="231.213" x2="713.024" y2="222.814" xlink:href="#B"><stop offset="0" stop-color="#565657"></stop><stop offset="1" stop-color="#858384"></stop></linearGradient></defs><path fill="url(#Ac)" d="M695 192c3 3 5 8 7 12 3 7 6 14 8 21 2 5 3 10 5 14 1 1 2 2 3 4h1c1 1 1 2 1 4l-1 1c-2 0-2-1-3-2-2 0-2 0-3 1h-2c0-4-2-8-2-12-3-10-5-19-8-28-2-5-4-9-6-14v-1z"></path><path d="M386 159h3l-1 7c-3 6-7 9-12 13l-1-1v-2c1-3 4-5 5-7 2-3 3-6 4-9l-239-1c8-1 16 0 24 0h49 168z" class="J"></path><path d="M386 159h3l-1 7c-1 0-3 1-3 2-1 1-2 2-4 3l4-9c1-1 1-1 1-3h0zm-31 15l2 3c3 0 4-2 7-2-2 3-5 5-7 8-3 6-7 12-10 18l-6 18c-2 3-3 6-5 9v1-1-3l1-6c1-2 1-3 1-5l1-5v-1c2-6 7-14 12-20 2-3 4-5 6-8l-1-1c-1-1-1-3-1-5z" class="D"></path><defs><linearGradient id="Ad" x1="647.746" y1="199.386" x2="662.053" y2="212.337" xlink:href="#B"><stop offset="0" stop-color="#979695"></stop><stop offset="1" stop-color="#c8c6c8"></stop></linearGradient></defs><path fill="url(#Ad)" d="M639 186c3 2 5 5 7 8l16 16c2 2 4 4 5 6l1 1 2 2h0c2 4 4 9 6 14-3-1-5-3-7-4l-2-2c-1-1-1-2-2-3-3-4-6-9-8-13l-11-16c-2-3-5-6-7-9z"></path><path d="M665 224c0-1 0-2-1-3-1-2-1-4-1-6l1 1c1 1 3 2 4 3 0 2 0 2 1 3l1-1v-2h0c2 4 4 9 6 14-3-1-5-3-7-4l-2-2c-1-1-1-2-2-3z" class="F"></path><defs><linearGradient id="Ae" x1="374.581" y1="295.505" x2="365.431" y2="295.473" xlink:href="#B"><stop offset="0" stop-color="#c0bebf"></stop><stop offset="1" stop-color="#e5e4e6"></stop></linearGradient></defs><path fill="url(#Ae)" d="M368 274l1-1c1-1 5-3 7-3-2 1-1 2-1 3l-1 2h1l-2 2v-1h-1c-1 2-3 2-4 3v1 1l1-1c1-1 2-2 4-2v2c0 1 0 2 1 3-2 1-4 3-4 5 0 1 0 1 1 2v4c0 1 0 2 1 4l3 19v3c-2-1-3-2-4-4-2-8-4-17-5-25 1-3 1-5 0-7 0-2 1-4 0-5v-4h0l2-1z"></path><path d="M368 281l1-1c1-1 2-2 4-2v2l-3 2h-1l-1-1z" class="L"></path><defs><linearGradient id="Af" x1="258.504" y1="282.93" x2="272.107" y2="271.198" xlink:href="#B"><stop offset="0" stop-color="#4f5052"></stop><stop offset="1" stop-color="#8f8c8c"></stop></linearGradient></defs><path fill="url(#Af)" d="M253 245c2 1 2 1 3 2l1 1 16 39c1 0 1 1 2 1l1 1h1c1 2 1 5 2 8-2 1-6 1-8 1l-18-53z"></path><path d="M353 352c-3-8-6-18-11-26h1c13 23 21 50 26 76-1 1 0 1-1 1s-2-1-2-1c-1-1-1-3-1-4l-3-11c0 1 0 1-1 1-1-4-2-9-3-13h-1 0l-4-23z" class="H"></path><path d="M353 352c3 6 4 12 5 18l4 17c0 1 0 1-1 1-1-4-2-9-3-13h-1 0l-4-23z" class="N"></path><path d="M644 575l2 1-36 64-1-1c0-1 1-3 0-4v-1l4-6-1-1c1-3 2-5 4-7l-1-1 5-8s2-3 2-4c4-7 8-13 13-19 4-4 7-9 9-13z" class="C"></path><path d="M620 611v1c0 1 0 2-1 3h1l-7 13-1-1c1-3 2-5 4-7l-1-1 5-8z" class="R"></path><path d="M635 588c0 1 1 2 0 3-1 2-3 3-4 6l-11 18h-1c1-1 1-2 1-3v-1s2-3 2-4c4-7 8-13 13-19z" class="S"></path><defs><linearGradient id="Ag" x1="543.676" y1="816.442" x2="535.129" y2="808.886" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#bebcbc"></stop></linearGradient></defs><path fill="url(#Ag)" d="M561 779c4 0 7-6 12-6l-3 3h-1c-1 0-1 1-2 1l-1 1s-1 1-1 2h0c-2 1-4 4-6 5-2 0-4 4-5 6-7 8-12 17-17 26l-11 23c-2-2-2-6-3-8 0-1 2-3 3-4l6-12v-1c1-3 4-6 6-8 4-6 8-12 13-17 3-3 8-6 10-11z"></path><path d="M675 291l1-2v1c0 3-1 7-1 10-1 4-1 9-1 14-1 10 0 19 0 29 0 3 0 7-1 10v2c0 1-1 2-1 3-1-2-1-3-2-4v-2c-1-7 0-15-1-21h-1l-2-5v-1l1-5c1-2 2-4 2-6v-1l1-2c0-3 1-5 2-8 0-1 0-2 1-3l2-9z" class="C"></path><path d="M670 311v6l-1 14h-1l-2-5v-1l1-5c1-2 2-4 2-6v-1l1-2z" class="T"></path><path d="M432 651c2-20 6-43 18-59h1l-6 12c-4 12-6 24-6 37v16 6h0c-2-2-4-2-5-4h-1l-1 1v-6-3z" class="C"></path><path d="M432 654c1-1 0-3 1-5 0-1 1-3 1-4 1 0 0 8 1 10v-1c0-1 0-2 1-3 1 1 1 0 1 1l1 6c1-4 0-9 0-13 0-2 0-3 1-4v16 6h0c-2-2-4-2-5-4h-1l-1 1v-6z" class="I"></path><path d="M471 706v-3-1l-1-1c0-1 0-1 1-2h0c1 4 2 8 4 12v-1-1-1-1c1 0 1 0 2 1 2 6 6 13 11 18 2 1 5 3 7 5 1 1 1 2 1 4v2l-2 2c-2 0-4 0-6-1-9-7-15-21-17-32z" class="R"></path><path d="M475 711v-1-1-1-1c1 0 1 0 2 1 2 6 6 13 11 18 2 1 5 3 7 5 1 1 1 2 1 4h-4c-8-5-13-16-17-24z" class="F"></path><path d="M740 333l12-26-4-3c0-1 3-5 4-6 7-9 11-20 16-30 2-3 4-6 6-10h1v1l1 1c-3 5-5 10-7 14-1 3-3 6-4 9-2 3-2 7-4 10-1 2-2 3-3 5h0 3v1c-2 4-5 7-7 11-4 7-5 16-11 22l-3 1z" class="X"></path><path d="M326 480l-8-6h0l4 2c1 1 4 2 6 3 3 1 6 3 9 4 6 4 30 20 35 19 2 1 5 3 7 4l12 6h0c-12-3-24-9-35-14l-9-3h0v1c-7-1-12-2-18-6l1-1-5-5c-1-1-3-2-4-3h4l1-1z" class="S"></path><path d="M326 480l6 6c-1 1-5-2-7-2-1-1-3-2-4-3h4l1-1z" class="O"></path><path d="M325 484c2 0 6 3 7 2 4 2 7 5 11 7 1 0 2 2 4 2v1c-7-1-12-2-18-6l1-1-5-5z" class="K"></path><path d="M719 296l39-79c-1 6-5 13-7 19l-12 31-9 23c-1 4-3 8-5 11v-2c1-1 1-3 2-4 0-1 0-1 1-2 0-1 1-2 1-4l1-1c0-1 0-2 1-3h-1c-1 1-2 1-3 0s0 0-2-1c-1 3-3 9-5 11l-1 1z" class="H"></path><defs><linearGradient id="Ah" x1="317.318" y1="475.075" x2="331.683" y2="465.974" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#Ah)" d="M304 458c1-1 3 0 4 0h1c1 1 0 0 2 1l18 8c3 1 5 3 7 4 2 4 4 9 6 12s6 5 9 6l21 13c-5 1-29-15-35-19-3-1-6-3-9-4-2-1-5-2-6-3v-1l-1-1h1v-1l-18-15z"></path><path d="M615 619l1 1c-2 2-3 4-4 7l1 1-4 6v1c1 1 0 3 0 4l1 1-9 18c-2 2-9 14-10 14-2 4-4 7-7 9l-1-1c0-2 4-5 4-7 0-3 1-7 2-10l-5 7-3 3c-1-1 0-1-1-1-1 1-2 1-3 1l19-22-1-1 2-1c4-5 7-11 10-16 0-2 1-3 2-4l6-10z" class="P"></path><path d="M615 619l1 1c-2 2-3 4-4 7l1 1-4 6c0 1-1 2-2 3l-5 6c1-2 5-8 5-10h0c0-2 1-3 2-4l6-10zm-21 37c1 3 0 5-1 8-2 5-5 9-7 14 1-1 2-3 3-4l2-2c-2 4-4 7-7 9l-1-1c0-2 4-5 4-7 0-3 1-7 2-10l5-7z" class="N"></path><defs><linearGradient id="Ai" x1="578.228" y1="667.679" x2="595.59" y2="658.418" xlink:href="#B"><stop offset="0" stop-color="#100f0e"></stop><stop offset="1" stop-color="#3d3e3f"></stop></linearGradient></defs><path fill="url(#Ai)" d="M596 651c3-3 5-7 10-8-3 2-7 5-9 8-1 2-1 4-3 5l-5 7-5 7-3 3c-1-1 0-1-1-1-1 1-2 1-3 1l19-22z"></path><path d="M722 370v1l4 1c1 0 1 1 2 2-2 3-4 7-6 10l-2 7-6 14c-1 1-2 2-2 4l-6 13c-1 1-2 2-2 3l-2 3c-2-1-3-1-5-3v-3c1-3 4-7 6-10l19-42z" class="X"></path><path d="M513 556v6c1 0 1 1 1 2s1 2 1 4v3l1 2c-1 3-1 5-1 7s1 4 0 6c0 4 1 8 1 12-2 3-1 5-1 9-1-2-2-4-2-6 0-1-1-2-1-3l-1-1h0l-1 1c1 2 1 4 2 6h-1c-1-1-2-2-2-3l-1-2v-1l-2-2v-1l-1-2c-1-3-3-4-3-8 0-2-1-4-2-6l-4-13-1-3c1 0 1 1 2 1 2 0 3-1 4-2l1-1 1-2 3 3h0c0 1 1 1 1 2-1 1-1 2-1 3 3-3 5-7 7-11z" class="B"></path><path d="M505 584c1-1 2-1 3-2l2 2h-1l-2 2c0 2 3 6 2 7h0c-1-1-2-2-2-3-1-2-1-4-2-6z" class="H"></path><path d="M501 575l1-1 2 3c1 2 2 3 4 4h2c0-2 1-3 1-4 1 2 1 4 1 6h-1c-1-1-1-1-3-1-1 1-2 1-3 2l-4-9z" class="C"></path><path d="M502 561l1-2 3 3h0c0 1 1 1 1 2-1 1-1 2-1 3l-2 3v1c2 1 6 2 7 5h-1l1 1h0c0 1-1 2-1 4h-2c-2-1-3-2-4-4l-2-3-1 1c-1 1-1 2-1 4l-4-13-1-3c1 0 1 1 2 1 2 0 3-1 4-2l1-1z" class="H"></path><path d="M501 562c1 1 1 1 2 1l-2 2c0 1-1 2-1 3l-2-2h-2l-1-3c1 0 1 1 2 1 2 0 3-1 4-2z" class="B"></path><path d="M506 562c0 1 1 1 1 2-1 1-1 2-1 3l-2 3v1l-2 2-1-1-1-1 6-9z" class="Q"></path><defs><linearGradient id="Aj" x1="518.507" y1="596.069" x2="508.88" y2="578.222" xlink:href="#B"><stop offset="0" stop-color="#4c4b49"></stop><stop offset="1" stop-color="#6e6d70"></stop></linearGradient></defs><path fill="url(#Aj)" d="M513 556v6c1 0 1 1 1 2s1 2 1 4v3l1 2c-1 3-1 5-1 7s1 4 0 6c0 4 1 8 1 12-2 3-1 5-1 9-1-2-2-4-2-6l-1-18c0-2 0-4-1-6h0l-1-1h1c-1-3-5-4-7-5v-1l2-3c3-3 5-7 7-11z"></path><path d="M513 556v6c1 0 1 1 1 2s1 2 1 4v3l1 2c-1 3-1 5-1 7v-5h-2v-2h-1c-1 0-2-1-4-1-1-1-2-1-4-2l2-3c3-3 5-7 7-11z" class="E"></path><path d="M512 570c-2 0-3-1-5-2 1-1 2-2 3-4 1 1 1 2 1 3-1 1 0 1 1 2v1z" class="H"></path><path d="M513 562c1 0 1 1 1 2s1 2 1 4v3l1 2c-1 3-1 5-1 7v-5h-2v-2h-1v-3-1l1-7z" class="Q"></path><path d="M518 572v-3l2 2c0 1 1 2 2 3h-1c2 3 5 6 7 9 1 4 3 7 5 10l5 7c1 0 2 2 2 2v2c0 1 2 2 2 3 3 2 4 6 7 9l-2 4-2 4c-1 3-2 6-4 9 0-1-1-1-2-2 1 2 1 3 0 5-2 0-2-3-4-3h-1l-4-3v1h-2c0-1-1-3-2-4-1-2-3-3-4-5-4-5-6-10-7-15 0-4-1-6 1-9 0-4-1-8-1-12 1-2 0-4 0-6s0-4 1-7v3c0-1 0-1 1-3l1-1z" class="O"></path><path d="M527 612h0c1 1 1 1 1 2 1 1 1 2 2 3 0 2 1 3 2 4v1l3 3c0 1 3 6 4 6 1 2 1 3 0 5-2 0-2-3-4-3-1-1-2-3-4-5 0-3-1-4-2-7 0-3-1-6-2-9z" class="L"></path><path d="M518 589c1 0 1-1 1-2h1v2l1-1c0 1 1 3 2 4-1 1-1 2-1 3l1 1v2c0 1 0 0 1 2v1c1 2 2 5 3 8l2 3 2 4v1h-1c-1-1-1-2-2-3 0-1 0-1-1-2h0c-1-1-2-3-3-5v-1c0-1 0-1-1-2 0-1-1-2-2-3l-3-12h0z" class="B"></path><path d="M518 572c1 4 2 9 4 13 1 3 3 5 4 7 0 2 2 3 3 4 0 1 1 2 2 3 2 3 4 6 7 9l9 12-2 4-5-7-1 1c1 1 2 3 2 5-3-5-7-9-10-14-3-6-5-12-8-17-1-1-2-3-2-4l-1 1v-2h-1c0 1 0 2-1 2-1-4 0-8-2-13 0-1 0-1 1-3l1-1z" class="N"></path><path d="M516 576c0-1 0-1 1-3l3 10c0 2 1 4 1 5l-1 1v-2h-1c0 1 0 2-1 2-1-4 0-8-2-13z" class="O"></path><path d="M531 599c2 3 4 6 7 9l9 12-2 4-5-7-1 1c-1-1-1-2-1-3h0l2 2c0-1 1-1 1-1-1-1-3-3-3-4-3-3-7-9-7-13z" class="J"></path><path d="M518 572v-3l2 2c0 1 1 2 2 3h-1c2 3 5 6 7 9 1 4 3 7 5 10l5 7c1 0 2 2 2 2v2c0 1 2 2 2 3 3 2 4 6 7 9l-2 4-9-12c-3-3-5-6-7-9-1-1-2-2-2-3-1-1-3-2-3-4-1-2-3-4-4-7-2-4-3-9-4-13z" class="G"></path><path d="M542 607h-1c-2-2-7-9-8-12-2-3-5-6-7-10l-4-8c-1-1-2-3-2-4l1 1c2 3 5 6 7 9 1 4 3 7 5 10l5 7c1 0 2 2 2 2v2c0 1 2 2 2 3z" class="C"></path><path d="M516 573v3c2 5 1 9 2 13h0l3 12c1 1 2 2 2 3 1 1 1 1 1 2v1c1 2 2 4 3 5 1 3 2 6 2 9 1 3 2 4 2 7 2 2 3 4 4 5h-1l-4-3v1h-2c0-1-1-3-2-4-1-2-3-3-4-5-4-5-6-10-7-15 0-4-1-6 1-9 0-4-1-8-1-12 1-2 0-4 0-6s0-4 1-7z" class="S"></path><path d="M521 601c1 1 2 2 2 3 1 1 1 1 1 2v1c1 2 2 4 3 5 1 3 2 6 2 9 1 3 2 4 2 7-1-1-2-3-3-4l-7-23z" class="C"></path><path d="M516 573v3c2 5 1 9 2 13h0l-1 1h0c1 1 1 2 1 3l1 3v1c0 3 0 6 1 9 0 3 2 6 1 8-3-5-4-10-5-16 0-4-1-8-1-12 1-2 0-4 0-6s0-4 1-7z" class="P"></path><path d="M516 598c1 6 2 11 5 16 0 2 4 7 6 8 0 1 1 1 1 2 1 1 2 3 3 4 2 2 3 4 4 5h-1l-4-3v1h-2c0-1-1-3-2-4-1-2-3-3-4-5-4-5-6-10-7-15 0-4-1-6 1-9z" class="W"></path><defs><linearGradient id="Ak" x1="453.508" y1="516.922" x2="442.683" y2="523.336" xlink:href="#B"><stop offset="0" stop-color="#aaa9a9"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#Ak)" d="M404 457l-30-65-10-24c-1-2-2-5-3-7 7 9 13 20 19 31l35 58 80 141 20 33c5 9 9 20 14 30 2 4 3 8 6 12 1 1 2 1 3 2l-1 1c-1 0-2-1-3-1h-2c-6-2-10-10-12-15l-14-26c-2-2-3-5-4-7-6-10-10-21-16-31l-7-10-9-17-15-26-10-16-8-15-8-12c-2-4-5-9-8-13-2-2-3-5-4-8-2-2-3-5-5-7l-1-2-6-8c0-1 0-2-1-2 0 1 1 2 0 3v1z"></path><path d="M480 571h0c1 0 2 1 3 2h-1-1l-2-2h1z" class="C"></path><path d="M474 558h1c1 2 2 3 2 6l-2-3c0-1 0-2-1-3zm15 27c1 0 1 1 2 2l2 3c0 1 0 0 1 1v1c1 2 1 4 3 5v1l1 1c1 2-1-1 1 1l1 2v1c1 0 1 1 1 2s1 2 2 3c0 1 1 2 1 3l2 2h-1c-2-1-6-9-8-12 0-1-1-3-2-4l-2-2v-1c0-1-1 0-1-2s-2-4-3-7z" class="E"></path><path d="M524 508l1-2c4 3 6 10 9 14v-1h0l-3-5 1-1c1 2 2 3 3 5 4 5 6 11 10 16 2-1 2-1 4-1l10 11 14 12-2 4h-1c-2-1-5 0-7 1l5 7-4 9h-1l-2 2h-2l-3-3-2-2v1c1 2 3 5 3 7v1c1 2 2 4 2 6l-1 4-2 4-7 19c-3-3-4-7-7-9 0-1-2-2-2-3v-2s-1-2-2-2l-5-7c-2-3-4-6-5-10-2-3-5-6-7-9h1c-1-1-2-2-2-3l-2-2v3l-1 1c-1 2-1 2-1 3v-3l-1-2v-3c0-2-1-3-1-4s0-2-1-2v-6c-2 4-4 8-7 11 0-1 0-2 1-3 0-1-1-1-1-2h0l-3-3-1 2-1 1c-1 1-2 2-4 2-1 0-1-1-2-1-2-2-2-4-3-7-1 0-2-3-2-3h0l-1-4h2c2 0 2-1 4-2l4-4c1-1 2-3 4-5l1 2c2-2 3-4 4-6h0l3-3c1-1 2-3 3-4 1-2 3-4 2-7h0c0-2 0-2 1-3l1-1c0-1 0-2 1-3 1 1 1 1 2 1l3-6z" class="O"></path><path d="M516 520h0c0-2 0-2 1-3l1-1c0-1 0-2 1-3 1 1 1 1 2 1-3 7-6 13-10 19v-2c1-1 2-3 3-4 1-2 3-4 2-7zm2 24v11c0 5-1 10 2 14l1 1c1 3 4 5 5 7-2-1-3-2-4-3s-2-2-2-3l-2-2v3l-1 1c-1 2-1 2-1 3v-3l-1-2v-3c0-2-1-3-1-4s0-2-1-2v-6h0c0-3 2-5 4-7l1-5z" class="C"></path><path d="M513 556h0c0-3 2-5 4-7-2 7-1 15-2 22v-3c0-2-1-3-1-4s0-2-1-2v-6z" class="P"></path><defs><linearGradient id="Al" x1="549.422" y1="567.837" x2="533.028" y2="539.692" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#a09e9e"></stop></linearGradient></defs><path fill="url(#Al)" d="M538 553l-10-19c-2-3-4-7-6-10l1-1c1 1 1 2 2 4s3 4 4 6l8 11c2 3 4 7 6 10s5 7 7 10c0 2 1 4 2 5-2 0-3-1-5-1-2-5-5-11-9-15z"></path><path d="M543 547l-8-15v-1l4 5c2 4 5 7 7 10l10 14 2 4c-2 0-2-3-4-2l2 3c1 3 3 5 5 8h0l-3-3v2l5 5-2 2h-2l-3-3v-2c-1-2-2-3-4-5-1-1-2-3-2-5-2-3-5-7-7-10 2 1 4 5 6 7h0c-1-2-2-4-3-5-1-2-1-2-1-3h1l-1-2c-1-1-1-2-2-4h0z" class="H"></path><path d="M543 547c5 7 9 17 15 23v2l5 5-2 2h-2l-3-3v-2c-1-2-2-3-4-5-1-1-2-3-2-5-2-3-5-7-7-10 2 1 4 5 6 7h0c-1-2-2-4-3-5-1-2-1-2-1-3h1l-1-2c-1-1-1-2-2-4h0z" class="F"></path><path d="M550 564l8 8 5 5-2 2h-2l-3-3v-2c-1-2-2-3-4-5-1-1-2-3-2-5z" class="J"></path><defs><linearGradient id="Am" x1="540.191" y1="585.761" x2="540.831" y2="570.199" xlink:href="#B"><stop offset="0" stop-color="#565657"></stop><stop offset="1" stop-color="#777574"></stop></linearGradient></defs><path fill="url(#Am)" d="M534 571h0c-4-10-12-17-14-27l19 28c1 0 2 0 3 1 2 2 4 5 5 8 4 4 7 9 11 12l-2 4c-2 0-3 1-5 2-3-4-6-8-7-12l-10-16z"></path><path d="M539 572c1 0 2 0 3 1 2 2 4 5 5 8 4 4 7 9 11 12l-2 4c-2 0-3 1-5 2-3-4-6-8-7-12 3 2 7 6 10 9 0-2-2-3-3-4l1-1 4 4c-6-8-12-15-17-23z" class="E"></path><path d="M549 580c-3-4-6-8-8-12-2-3-4-7-5-10-2-5-6-10-9-13v-1l9 10h0c1 0 1 1 1 1l1 1c0 1 1 3 2 4h1c0-1 0-2-1-2 0-1-1-2-1-3-1-1-1-1-1-2 4 4 7 10 9 15 2 0 3 1 5 1 2 2 3 3 4 5v2l-2-2v1c1 2 3 5 3 7v1c1 2 2 4 2 6l-1 4c-4-3-7-8-11-12l2-1z" class="C"></path><path d="M546 572c2 0 4 3 5 5s2 3 5 4l1 1v1c1 2 2 4 2 6l-1 4c-4-3-7-8-11-12l2-1 4 5h1c-3-4-6-8-8-13h0z" class="L"></path><path d="M538 553c4 4 7 10 9 15 2 0 3 1 5 1 2 2 3 3 4 5v2l-2-2v1c1 2 3 5 3 7l-1-1c-3-1-4-2-5-4s-3-5-5-5c-1-1-1-2-2-3h1l1-1c-4-5-8-9-10-14h0c1 0 1 1 1 1l1 1c0 1 1 3 2 4h1c0-1 0-2-1-2 0-1-1-2-1-3-1-1-1-1-1-2z" class="B"></path><path d="M547 568c2 0 3 1 5 1 2 2 3 3 4 5v2l-2-2v1c1 2 3 5 3 7l-1-1c-1-3-3-5-5-7-1-2-3-4-4-6z" class="Q"></path><path d="M527 570c-1-2-2-3-2-4 2 1 5 6 6 8 1 0 1 0 2 1h0c0-1 0-2 1-3v-1l10 16c1 4 4 8 7 12 2-1 3-2 5-2l-7 19c-3-3-4-7-7-9 0-1-2-2-2-3v-2s-1-2-2-2l-5-7c-2-3-4-6-5-10-2-3-5-6-7-9h1c1 1 2 2 4 3h0c1 1 1 1 1 2 1 0 2 1 3 2v1c-1-3-1-5-2-8-1-2-1-2-1-4h0z" class="L"></path><defs><linearGradient id="An" x1="535.543" y1="575.178" x2="540.957" y2="601.822" xlink:href="#B"><stop offset="0" stop-color="#a3a2a2"></stop><stop offset="1" stop-color="#c9c8c9"></stop></linearGradient></defs><path fill="url(#An)" d="M527 570c-1-2-2-3-2-4 2 1 5 6 6 8 8 11 14 23 18 36v1c-2-4-2-7-5-10-5-4-8-11-11-17-1-2-2-4-3-5v-5c-1-1-2-3-3-4h0z"></path><defs><linearGradient id="Ao" x1="561.366" y1="553.429" x2="550.916" y2="565.319" xlink:href="#B"><stop offset="0" stop-color="#c5c5c5"></stop><stop offset="1" stop-color="#efeeed"></stop></linearGradient></defs><path fill="url(#Ao)" d="M534 520v-1h0l-3-5 1-1c1 2 2 3 3 5 4 5 6 11 10 16 2-1 2-1 4-1l10 11 14 12-2 4h-1c-2-1-5 0-7 1l5 7-4 9h-1l-5-5v-2l3 3h0c-2-3-4-5-5-8l-2-3c2-1 2 2 4 2l-2-4-10-14h1c0-3-2-6-3-9h0l-10-17z"></path><path d="M544 537c1 1 2 3 4 5 1 3 3 4 3 8l8 11h-1v1l-2-2-10-14h1c0-3-2-6-3-9h0z" class="F"></path><defs><linearGradient id="Ap" x1="557.466" y1="554.525" x2="548.267" y2="531.785" xlink:href="#B"><stop offset="0" stop-color="#494949"></stop><stop offset="1" stop-color="#6e6c6c"></stop></linearGradient></defs><path fill="url(#Ap)" d="M545 534c2-1 2-1 4-1l10 11v1c-1 0-1 0-2 1l2 2c0 2 1 2 1 4v1 1h-2l-13-20z"></path><defs><linearGradient id="Aq" x1="562.921" y1="546.092" x2="567.755" y2="557.836" xlink:href="#B"><stop offset="0" stop-color="#5e5d5d"></stop><stop offset="1" stop-color="#7b7a7b"></stop></linearGradient></defs><path fill="url(#Aq)" d="M559 544l14 12-2 4h-1c-2-1-5 0-7 1-1-3-3-5-5-7h2v-1-1c0-2-1-2-1-4l-2-2c1-1 1-1 2-1v-1z"></path><path d="M510 541c2-3 5-6 8-9 0-1 1-1 2-2h0c-1 1-2 2-2 3h3l1-3 1 1-5 13-1 5c-2 2-4 4-4 7h0c-2 4-4 8-7 11 0-1 0-2 1-3 0-1-1-1-1-2h0l-3-3-1 2-1 1c-1 1-2 2-4 2-1 0-1-1-2-1-2-2-2-4-3-7-1 0-2-3-2-3h0l-1-4h2c2 0 2-1 4-2l4-4c1-1 2-3 4-5l1 2c2-2 3-4 4-6h0l3-3v2l-3 6c0 1 1 1 2 2z" class="H"></path><path d="M515 539c1 2 1 2 0 3-1 2-4 5-5 5l-1-1 6-7z" class="O"></path><path d="M510 541c2-3 5-6 8-9 0-1 1-1 2-2h0c-1 1-2 2-2 3-1 1-2 3-2 4h1v1l-2 1-6 7-2 1v-1c-1 0-2 1-3 2l6-7z" class="J"></path><path d="M497 558h1 1c3-2 5-5 7-6h1l1-1c2-1 3-2 5-4v1l-2 3a30.44 30.44 0 0 1-8 8l-1 2-1-1h-2c-2 0-2 0-3 1h0-1c0-1 1-2 2-3z" class="B"></path><path d="M521 533l1-3 1 1-5 13-1 5c-2 2-4 4-4 7h0c-2 4-4 8-7 11 0-1 0-2 1-3 0-1-1-1-1-2h0l-3-3a30.44 30.44 0 0 0 8-8l3-1c2-3 4-7 5-11v-2l2-4z" class="N"></path><path d="M511 551l3-1c-2 2-3 4-5 7-1 1-2 3-3 5l-3-3a30.44 30.44 0 0 0 8-8z" class="E"></path><path d="M508 534h0l3-3v2l-3 6c0 1 1 1 2 2l-6 7c-1 1-2 1-3 3h1l1-1c0 1-1 1-1 2-1 3-3 4-5 6-1 1-2 2-2 3h1 0c1-1 1-1 3-1h2l1 1-1 1c-1 1-2 2-4 2-1 0-1-1-2-1-2-2-2-4-3-7-1 0-2-3-2-3h0l-1-4h2c2 0 2-1 4-2l4-4c1-1 2-3 4-5l1 2c2-2 3-4 4-6z" class="G"></path><path d="M508 534h0l3-3v2l-3 6c-1 1-2 1-2 3-1 3-4 5-6 8-2 2-4 5-6 7h-1 0c2-2 4-5 5-7 1-4 4-7 6-10 2-2 3-4 4-6z" class="M"></path><path d="M503 538l1 2c-2 3-5 6-6 10-1 2-3 5-5 7l-1-1c-1 0-2-3-2-3h0l-1-4h2c2 0 2-1 4-2l4-4c1-1 2-3 4-5z" class="F"></path><path d="M491 549c2 0 2-1 4-2-1 2-2 3-3 4-1 0-1 1-2 2h0l-1-4h2z" class="G"></path><defs><linearGradient id="Ar" x1="467.327" y1="373.419" x2="686.77" y2="550.535" xlink:href="#B"><stop offset="0" stop-color="#cdcccb"></stop><stop offset="1" stop-color="#f6f5f4"></stop></linearGradient></defs><path fill="url(#Ar)" d="M719 296l1-1c2-2 4-8 5-11 2 1 1 0 2 1s2 1 3 0h1c-1 1-1 2-1 3l-1 1c0 2-1 3-1 4-1 1-1 1-1 2-1 1-1 3-2 4v2L621 560l2 2h2c-2 1-4 0-6 2-1 4-4 9-5 13 1 1 1 1 2 1h0-2c-2 3-2 9-5 11v1c-1 4-4 10-5 14l-12 32-6 4c-3 2-10 5-11 10h0c1 1 1 1 1 2 1 2 0 2 3 3 2-1 2-1 4-3v6l-2 4 1 2c-5 4-9 10-14 16s-13 15-14 23v1l-1 2c1 0 1 1 2 1h1 0c2 0 3 0 4-1 0 1 0 1-1 2v4l-3 2h0c3-1 5-3 7-4-3 3-5 6-8 8l-1 1 1 1 1-1 1 1-1 1c-6 2-5 17-14 14h-1v2l-26-54h0c-2-3-3-7-6-10l-39-80c-1-3-4-7-5-10l-61-126v-1c1-1 0-2 0-3 1 0 1 1 1 2l6 8 1 2c2 2 3 5 5 7 1 3 2 6 4 8 3 4 6 9 8 13l8 12 8 15 10 16 15 26 9 17 7 10c6 10 10 21 16 31 1 2 2 5 4 7l14 26c2 5 6 13 12 15h2c1 0 2 1 3 1l1-1v-1c2-3 4-7 6-11l8-18 32-68 95-193 28-57c4-8 8-17 12-24z"></path><path d="M614 565c-2-1-3 0-4-1 0-2-1-2 0-4h8c0 1-1 2-2 2s-3 1-4 1l2 2z" class="D"></path><path d="M621 560l2 2h2c-2 1-4 0-6 2-1 1-3 1-5 1l-2-2c1 0 3-1 4-1s2-1 2-2h3z" class="E"></path><path d="M556 714h0c3-1 5-3 7-4-3 3-5 6-8 8h-1c-3 1-7 5-9 7h0-1c-2 2-3 5-3 7-1-1-1-4 0-5 1-6 10-11 15-13z" class="D"></path><path d="M555 707c-2 1-2 0-3 0-1-1-2-2-2-3-1-8 26-35 31-42h0l1 2c-5 4-9 10-14 16s-13 15-14 23v1l-1 2c1 0 1 1 2 1z" class="T"></path><path d="M545 725h0c2-2 6-6 9-7h1l-1 1 1 1 1-1 1 1-1 1c-6 2-5 17-14 14l-1-3c0-2 1-5 3-7h1z" class="R"></path><path d="M545 725h0c2-2 6-6 9-7h1l-1 1-1 1c-3 3-5 7-8 11-1-1-1-2-1-3l1-3z" class="H"></path></svg>