<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="146 151 749 744"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#e8e3e0}.C{fill:#d9cdc7}.D{fill:#5e5757}.E{fill:#e7e0df}.F{fill:#89746a}.G{fill:#dcd3d1}.H{fill:#947e75}.I{fill:#cdbfba}.J{fill:#4e423e}.K{fill:#302f31}.L{fill:#a9a09e}.M{fill:#beaca7}.N{fill:#b5a199}.O{fill:#aa948c}.P{fill:#f5f3f2}.Q{fill:#7e685e}.R{fill:#1d1d1e}.S{fill:#1d1d1f}.T{fill:#4d413e}</style><path d="M572 440h2l1 1v1c-1 1-2 1-3 2l-1 1h-1c-1 0-1-1-2-2 1-1 2-2 4-3z" class="B"></path><path d="M514 500c1-2 3-6 6-6l1-1c2-1 4 0 6 0-2 2-5 3-7 6-1 1-2 1-2 2l-4-1z" class="D"></path><path d="M704 280c1 0 1-1 2-1v6l-5 7-4 6c-2 2-3 4-5 6v-2h1c0-2 1-4 2-5h1l2-3v-2c-2 1-3 4-5 5l-2 3h0c-1-1 4-7 5-8l4-5 1-2 1-2 2-3z" class="B"></path><path d="M405 278c2 8 4 15 9 23 3 5 8 9 13 14h0c-5-3-8-7-12-11-3-2-6-4-8-8v-1c0-1 0-1-1-2v-1-3h-1c-1-3-2-6-1-9l1-2z" class="C"></path><path d="M496 499h0v5c1 2 2 3 3 4v3 1c1 4 1 8 0 12 0 2-1 5-1 8l-1 1c-1-3 0-9 1-12h0c-1-1-1-3-2-4l-2-2-1-1 2-4v-5-2c0 1-1 1-1 2l2-6z" class="E"></path><defs><linearGradient id="A" x1="672.36" y1="262.536" x2="670.762" y2="278.723" xlink:href="#B"><stop offset="0" stop-color="#bbaca8"></stop><stop offset="1" stop-color="#dcd4d1"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M672 261c1 1 1 2 3 3h1l-4 11c0 1-1 3-1 4-3 5-5 11-9 16v-1l1-1 2-5h0l1-1v-1l1-1h0v-1c0-1 1-2 1-2l1-1v-2h-1l-1 1v-2c1-1 1-1 1-2 1-1 1-1 1-2v-1-1c1-2 1-3 1-5 1-1 1-4 2-6z"></path><path d="M476 384l-12-15c3 2 5 4 7 5 3 2 6 3 8 4 7 4 12 9 16 15h-1 0l-1-1c-1-1 0 0-1-2 0-1-1-2-2-2-1-1-1-1-1-2l-2-2h-1l-2-2h-2l-2-1h-1l1 1c2 1 3 3 5 4-2-1-4-3-6-4l-3 2z" class="C"></path><path d="M381 262c0-2 0-4 1-6l2 3v2c3 6 6 12 9 16l6 10h0c-2-1-3-2-4-4v-1l-2-3c-1-1-2-3-3-4-1 0 0 0-1 1 0 1 1 2 1 3l2 2c0 1 0 1 1 2v1l2 2c0 1 1 1 1 2v1c-6-6-9-15-13-23l-2-4zm95 122l3-2c2 1 4 3 6 4 0 1 1 2 2 3h0l2 2 1 1c0 1 1 1 1 2 1 1 1 2 2 3l6 12c1 2 2 3 2 4s1 3 2 4h0c0 1 0 1 1 2 0 1 0 2 1 3 0 1 0 2 1 3v1l2 3c0 1 1 1 1 3h0l2 4c1 1 0 1 0 2l-1-3-1-2-1-1-1-1c0-2-1-3-1-4-1-2-2-3-2-5l-4-8-6-11-1-3c-1-2-2-4-4-6l-1-1-3-4c-1 0-1 0-1-1-1 0-1-1-2-1-3-1-4-2-6-3z" class="B"></path><path d="M645 293c3-1 4-3 6-6 2-2 4-5 7-7 1-1 2-2 3-2 3 3 3 3 3 6v-1-1c1-1 2-3 3-4v2l-2 5-1 2v1c-1 1-2 3-3 4v-1-1c1 0 1-1 1-2 0 0-1 0-1-1s-1-1-1-2v-2l-1-1-1 1-1 1c-2 1-3 2-4 3 0 1-1 2-1 2-1 2-2 3-3 4l-1 1-1 1c-1 1-1 1-2 1-1 1-1 2-1 3v1c-1 1-2 3-2 5v1l-1 1c0-1 3-12 4-14z" class="C"></path><defs><linearGradient id="C" x1="395.112" y1="283.027" x2="406.241" y2="283.885" xlink:href="#B"><stop offset="0" stop-color="#bcaca7"></stop><stop offset="1" stop-color="#d8cdc8"></stop></linearGradient></defs><path fill="url(#C)" d="M393 277h1v1l1 1c1-1 1-4 1-6 2 2 4 4 6 5h1v1-1l1 2c-1 3 0 6 1 9h1v3 1c1 1 1 1 1 2v1l-8-9-6-10z"></path><path d="M653 321c1 0 2 0 2 1 0 3-5 6-6 9-1 2-5 6-5 8 0 3-2 5-3 8v1c-2 2-3 4-6 4h0v-1l3-9c3-6 6-11 11-16 1-2 2-3 4-5z" class="G"></path><path d="M635 351h0c2-2 3-4 5-6s3-4 4-6c0 3-2 5-3 8v1c-2 2-3 4-6 4h0v-1z" class="B"></path><path d="M527 493l4 2-3 4h-1l-1-1c-2 0-3 1-5 2-1 2-2 3-2 5l-1 1-1-2c-2 0-3 2-4 3-1 2-2 5-3 7-1 1-2 2-2 4h-2c0 1-1 1-1 2 0 2-1 4-3 6v-3c4-8 6-16 12-23l4 1c0-1 1-1 2-2 2-3 5-4 7-6z" class="H"></path><defs><linearGradient id="D" x1="691.226" y1="265.229" x2="685.477" y2="282.171" xlink:href="#B"><stop offset="0" stop-color="#afa6a5"></stop><stop offset="1" stop-color="#d8d3d2"></stop></linearGradient></defs><path fill="url(#D)" d="M696 260l2-2 1 1v3l-2 6-5 6-6 9c-2 2-6 5-7 8-1 2-3 5-5 6 0-2 0-3 2-5h0c1-2 2-2 3-3l3-3 1-1v-3l-3 3-4 5c-1 2-3 4-5 5h0l9-12 6-7c3-4 8-10 10-16z"></path><path d="M468 447c1 0 1 1 2 2l4 4 20 37c0 1 1 1 1 2l1 1v-3c1-1 1-2 2-3 0 1 1 1 0 2v1 1c-1 1-1 2-2 4v1l3-5c1-1 0-1 1-1l1-3c1-2 0 1 1-1v-1-1h1c0 1 0 1-1 2l-6 13h0c0-1-1-2-2-3s0-1-1-2l-8-15c0-2-1-3-2-5h0c0-1 0-1-1-2 0-1 0 0-1-1v-1h-1c-3-4-4-8-6-12-2-3-4-7-6-11z" class="B"></path><path d="M754 195v-1c1-3 2-5 1-8 0-2-2-3-4-5-1 1-3 5-4 5-1-1 0-2 0-3s0-1 1-2v1 1c2-1 2-2 4-2 3 2 4 8 8 9v-2-3-1h2 0v1c1 0 2 0 2-1-1-2-2-1-4-2v-6c1-1 2-2 3-2 0 2 0 1-1 2 0 1-1 3 0 4v1h0c3 1 4 2 5 4v2h-1l-1-1c-1-1 0-1-1 0h-2c-1 4 2 9 0 13 2 1 4 1 5 1h-4-1v1c1 0 3 0 4 1-5 0-9 0-14-1v-2h-2c1-1 2-3 4-4z" class="S"></path><path d="M754 195v-1c1-1 3-4 4-4v4c0 1-1 2-2 3l1 1c-2 1-4 1-5 1h-2c1-1 2-3 4-4zM386 310c0-2 0-2-1-4-1-1-1-1-1-2l1 1c1-1 1-3 2-4v1c2 1 2 1 4 0l4 4h1 0c2 1 3 1 5 0v-1l1-1v-5c1 1 1 1 2 1 1 1 1 1 2 1l26 23-1 1-1-2h-1l-1-1-1-1s-1-1-2-1c0-1-1 0-1-1-1-2-4-4-6-5l-1-1c-1-1-2-3-3-3-2-1-3-3-4-4l-2-1v1l-1 1 1 1c1 1 1 2 0 4l-2 1v-1h-2-1l-1-1h-2c1 1 2 2 4 3l3 4h-1c-1 0-1-1-1-1-4-4-8-7-11-11l-1 1-1-1-2 1c0 1-1 1-2 1v1h0c3 3 4 6 7 9v1h1c0 1 1 2 1 3 2 2 4 5 6 7h0l-17-19z" class="B"></path><path d="M531 493l-4-6c0-8 2-14 7-20 6-9 17-20 28-21 2 0 4 0 5 1 2 1 1 3 2 4l-2 1-1-1c0-1-1-1-2-1h-1c-3 1-6 1-9 3-9 3-18 15-22 24-1 2-3 6-2 8v1l1 1v1l-1 1 2 3-1 1z" class="F"></path><defs><linearGradient id="E" x1="668.286" y1="278.448" x2="664.577" y2="312.699" xlink:href="#B"><stop offset="0" stop-color="#9c8780"></stop><stop offset="1" stop-color="#d8ccc7"></stop></linearGradient></defs><path fill="url(#E)" d="M671 279h0c1 1 3 3 3 5l4-5c1 1 2 2 2 3v1h0l-9 12-21 24c0-1 0-1 1-2l1-1v-1l1-1h0l1-2h-1v1c0 1 0 0-1 1l-1 2-1 1-2 3h0c1-4 4-8 6-11l8-14c4-5 6-11 9-16z"></path><path d="M697 268l2-6c1 2 1 2 2 3l1 2c1 2 2 4 2 6l1 1-2 1v1c1 1 2 1 3 3-1 0-1 1-2 1l-2 3-1 2-1 2-4 5v-1c-1-1-1-2-1-3l-1-1c-1-2-1-2-2-3-2 0-2 1-3 2-1 0-3 0-4 1v1l-1-1c1-1 2-2 2-3v-1l6-9 5-6z" class="T"></path><path d="M693 279l3-4h3 0c0 1 1 1 2 2 0 1 0 2-1 2l1 1c0 1 0 2 1 3h0l-1 2-1-1c0-2-2-5-3-6-1 0-2 1-3 2-1 0-1-1-1-1z" class="J"></path><path d="M693 279s0 1 1 1c1-1 2-2 3-2 1 1 3 4 3 6l1 1-1 2c-2-2-3-4-6-6l-4 1 3-3z" class="D"></path><path d="M697 268l2-6c1 2 1 2 2 3l1 2c1 2 2 4 2 6l1 1-2 1v1c1 1 2 1 3 3-1 0-1 1-2 1h-1l-1-6c-1-1 0-1 0-2-1-1-1-1-1-2l-5 3h0c1-2 1-3 1-5h0z" class="K"></path><path d="M692 274s1 1 1 2c-1 2-5 6-4 7l1-1 4-1c3 2 4 4 6 6l-4 5v-1c-1-1-1-2-1-3l-1-1c-1-2-1-2-2-3-2 0-2 1-3 2-1 0-3 0-4 1v1l-1-1c1-1 2-2 2-3v-1l6-9z" class="F"></path><defs><linearGradient id="F" x1="651.246" y1="265.095" x2="663.615" y2="283.422" xlink:href="#B"><stop offset="0" stop-color="#a9958f"></stop><stop offset="1" stop-color="#e0d6d2"></stop></linearGradient></defs><path fill="url(#F)" d="M648 267c0 1 0 3 1 4 3-3 7-5 10-8v1l2-2v-2c1 0 1-1 1-2l1-1c1 2 0 5 1 6 0 1 1 0 1 2h0c1-2 2-6 2-9h0l1-2h0c1 1 1 1 1 2 0 2 1 4 1 6h-1l1 1v4c0 2 0 3-1 5v1 1c0 1 0 1-1 2 0 1 0 1-1 2s-2 3-3 4v1 1c0-3 0-3-3-6-1 0-2 1-3 2-3 2-5 5-7 7-2 3-3 5-6 6 2-9 2-17 3-26z"></path><defs><linearGradient id="G" x1="685.233" y1="282.704" x2="678.28" y2="316.316" xlink:href="#B"><stop offset="0" stop-color="#8d7870"></stop><stop offset="1" stop-color="#d9d1cf"></stop></linearGradient></defs><path fill="url(#G)" d="M686 283v1c0 1-1 2-2 3l1 1v-1c1-1 3-1 4-1 1-1 1-2 3-2 1 1 1 1 2 3l1 1c0 1 0 2 1 3v1c-1 1-6 7-5 8l-15 15v-1l1-1-1-1h-2-1c-1 1-2 1-3 2v1-1l-1-1c0-1-1-2-1-3v-1l-1-2c-1 1-1 1-2 1l5-6c1 0 2-1 2-2 1-2 0-2 2-3s4-4 5-6c1-3 5-6 7-8z"></path><path d="M685 290c0 1 0 2-1 3-1 0-2 2-3 2h-1 0-1c1-2 3-4 6-5z" class="O"></path><path d="M686 283v1c0 1-1 2-2 3l1 1v-1c1-1 3-1 4-1-1 1-2 4-4 4-3 1-5 3-6 5l-8 8c-1 2-3 3-4 4s-1 1-2 1l5-6c1 0 2-1 2-2 1-2 0-2 2-3s4-4 5-6c1-3 5-6 7-8z" class="H"></path><defs><linearGradient id="H" x1="655.961" y1="312.411" x2="661.07" y2="343.259" xlink:href="#B"><stop offset="0" stop-color="#cfc6c3"></stop><stop offset="1" stop-color="#fbfbfa"></stop></linearGradient></defs><path fill="url(#H)" d="M665 308c1 0 1 0 2-1l1 2v1c0 1 1 2 1 3l1 1v1-1c1-1 2-1 3-2h1 2l1 1-1 1v1l-25 26-7 5c-1 1-2 2-3 2v-1c1-3 3-5 3-8 0-2 4-6 5-8 1-3 6-6 6-9 0-1-1-1-2-1h0c3-4 8-11 12-13z"></path><path d="M641 347l1-1c1 0 2-1 3-2h1l2-2c0-2 1-3 2-4l1 3-7 5c-1 1-2 2-3 2v-1z" class="P"></path><path d="M377 304c2 1 2 1 3 3l1 1c2 1 0-1 1 1l1 1c1 1 1 2 2 3s1 2 2 3c3 5 7 9 10 14l5 6c1 1 3 4 4 5 0 3 4 5 5 7 1 3 4 5 5 7l14 22c3 3 5 7 7 11l12 20 18 30c2 5 5 10 7 15l-4-4c-1-1-1-2-2-2-1-1-7-12-8-14l-21-36c-5-10-11-20-18-29l-3-4-1-2-3-3-1-2-1-2c-1-1-2-2-3-4l-16-21c0-1 0-1-1-2 0-1-1-2-1-3l-5-7c-1-1-2-2-2-4-2-4-6-6-7-10z" class="C"></path><defs><linearGradient id="I" x1="693.137" y1="252.153" x2="679.624" y2="265.587" xlink:href="#B"><stop offset="0" stop-color="#3d3330"></stop><stop offset="1" stop-color="#796862"></stop></linearGradient></defs><path fill="url(#I)" d="M686 244l4 4 6 7v1 4c-2 6-7 12-10 16l-6 7h0v-1c0-1-1-2-2-3l-4 5c0-2-2-4-3-5h0c0-1 1-3 1-4l4-11h0l3-6v-2c1-1 1-3 1-4l1-1v-2l1-3 1 1v-1c1 0 1 0 2-1l1 1v-2z"></path><path d="M681 249l1-3 1 1v-1c1 0 1 0 2-1v1c0 2 0 3 1 5l1 1h1c-1 0-1 0-1 1-1-1-2-2-2-3-1 0-2 0-3 1h-1v-2z" class="J"></path><path d="M680 252h2c1 0 1 0 2-1 2 1 3 3 4 5v1 2c-1 0-2 0-2 1l-2 2-1 1-1-1v-5c-1-1 0-1-1-1l-2 2v-2c1-1 1-3 1-4z" class="D"></path><defs><linearGradient id="J" x1="685.696" y1="260.646" x2="672.521" y2="282.407" xlink:href="#B"><stop offset="0" stop-color="#695a54"></stop><stop offset="1" stop-color="#a38e87"></stop></linearGradient></defs><path fill="url(#J)" d="M679 258l2-2c1 0 0 0 1 1v5c-1 1-1 2-2 2l1 2h0c2 1 4 1 5 3h-1v1 1c-1 1-1 2-1 3l1 2h0 1l-6 7h0v-1c0-1-1-2-2-3l-4 5c0-2-2-4-3-5h0c0-1 1-3 1-4l4-11h0l3-6z"></path><path d="M531 493l1-1-2-3 1-1v-1l-1-1v-1c-1-2 1-6 2-8 4-9 13-21 22-24 3-2 6-2 9-3h1c1 0 2 0 2 1l1 1 2-1h0l-2 3-3 3-8 8c-6 5-11 13-16 19-2 3-4 6-7 9l-1 1-1-1z" class="B"></path><path fill="#fff" d="M533 493v-4c1-1 1-2 1-2 1-2 1-3 1-4s1-3 2-3l16-17c1-2 3-3 4-4 3-2 4-4 7-5l1 1c-1 0-1 1-1 2h0l-8 8c-6 5-11 13-16 19-2 3-4 6-7 9z"></path><defs><linearGradient id="K" x1="679.235" y1="192.54" x2="681.265" y2="210.96" xlink:href="#B"><stop offset="0" stop-color="#252323"></stop><stop offset="1" stop-color="#4a4b51"></stop></linearGradient></defs><path fill="url(#K)" d="M604 201c0-1 1-1 1-1 1-1 4 0 6 0h23 115-3c1 1 3 0 4 1 0 0 0 1 1 1 0 0 2 0 3 1v1l-1-1c-4 0-8 1-11 0-1 0-3 0-4 1h1 2 9l26 1c-4 1-9 1-12 0h-3c-7 0-14 0-21-1-5 0-12 0-17 1h-11-17-12-19c-2 1-4 1-6 2s-6 1-9 1h-23-12c-2 0-2-1-3-2h-2c-2-2-3-4-5-5z"></path><defs><linearGradient id="L" x1="394.964" y1="246.02" x2="409.066" y2="271.087" xlink:href="#B"><stop offset="0" stop-color="#6b5b56"></stop><stop offset="1" stop-color="#cab7b0"></stop></linearGradient></defs><path fill="url(#L)" d="M387 237l11-1c6 0 11-4 17-2l-1 3-2 3c-5 10-8 20-8 31l1 7-1 2-1-2v1-1h-1c-2-1-4-3-6-5 0 2 0 5-1 6l-1-1v-1h-1c-3-4-6-10-9-16v-2l-2-3v-2c-1-2-2-4-3-7l-1-1c0-1 0-2-1-3l1-1c1 1 1 1 3 1 1-3 3-4 6-6z"></path><path d="M402 278v-2c0-2 0-3 2-5l1 7-1 2-1-2v1-1h-1z" class="I"></path><defs><linearGradient id="M" x1="392.496" y1="238.002" x2="396.035" y2="247.268" xlink:href="#B"><stop offset="0" stop-color="#463936"></stop><stop offset="1" stop-color="#62534f"></stop></linearGradient></defs><path fill="url(#M)" d="M387 237l11-1c6 0 11-4 17-2l-1 3-2 3h-3c-3 2-4 5-9 3l-1 1v2h0-2c-1 1-2 1-2 2l-2 1v2l-1 1-1-1c0-1 0-1-1-1s-2 0-3 1v2l1-1h0l-2 2c0 4 1 5-2 7v-2l-2-3v-2c-1-2-2-4-3-7l-1-1c0-1 0-2-1-3l1-1c1 1 1 1 3 1 1-3 3-4 6-6z"></path><path d="M378 246h1l1 1c1-1 1-1 1-2h0c2-4 8-6 11-8l-5 7c-1 0-1 1-1 2-2 0-3 0-5 1h-2l-1-1zm15 3c0-2 1-2 2-3 2-2 5-5 8-7 2-1 3-1 5-1 2-1 4-1 6-1l-2 3h-3c-3 2-4 5-9 3l-1 1v2h0-2c-1 1-2 1-2 2l-2 1z" class="D"></path><path d="M386 246c0-1 0-2 1-2l-1 7h-1l1 3c0 4 1 5-2 7v-2l-2-3v-2c-1-2-2-4-3-7h2c2-1 3-1 5-1z" class="Q"></path><path d="M384 259v-4c0-2 0-3 1-4l1 3c0 4 1 5-2 7v-2z" class="F"></path><path d="M379 247h2c2-1 3-1 5-1-1 0-2 1-3 3 0 2 0 3-1 5-1-2-2-4-3-7z" class="J"></path><defs><linearGradient id="N" x1="787.451" y1="196.649" x2="786.554" y2="210.285" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#56575b"></stop></linearGradient></defs><path fill="url(#N)" d="M750 199h2v2c5 1 9 1 14 1-1-1-3-1-4-1v-1h1 4 55 17c3 0 6-1 8 0 2 2 5 6 5 8-1 3 4 11 6 14 2 4 3 11 4 15-2 0-2-1-3-2l-7-11c-2-5-5-13-9-15-1-1-4-1-5-1l-12 1c-6 0-13 0-20-1-2-1-5-1-7-1h-16-63 2 2c1-1 3-1 4-1 0-1-5 1-7 0s-6 0-9-1h11c5-1 12-1 17-1 7 1 14 1 21 1h3c3 1 8 1 12 0l-26-1h-9-2-1c1-1 3-1 4-1 3 1 7 0 11 0l1 1v-1c-1-1-3-1-3-1-1 0-1-1-1-1-1-1-3 0-4-1h3l1-1z"></path><path d="M847 200c2 2 5 6 5 8-1 3 4 11 6 14 2 4 3 11 4 15-2 0-2-1-3-2l-7-11c-2-5-5-13-9-15-1-1-4-1-5-1l-12 1c-6 0-13 0-20-1h19c6 0 13-1 19-1h1c1-2 2-4 2-7z" class="L"></path><defs><linearGradient id="O" x1="646.102" y1="236.216" x2="660.201" y2="265.017" xlink:href="#B"><stop offset="0" stop-color="#4a3d39"></stop><stop offset="1" stop-color="#b49f97"></stop></linearGradient></defs><path fill="url(#O)" d="M636 230c6 1 12-2 18 0 0 1 0 1 1 2 1 0 4 2 5 2h5v1c4 0 6 2 8 5 1-1 2-1 2-2 1 1 2 1 3 2h1c1 0 2 0 2 1 1 2 1 3 1 5l-1 3v2l-1 1c0 1 0 3-1 4v2l-3 6h0-1c-2-1-2-2-3-3-1 2-1 5-2 6v-4l-1-1h1c0-2-1-4-1-6 0-1 0-1-1-2h0l-1 2h0c0 3-1 7-2 9h0c0-2-1-1-1-2-1-1 0-4-1-6l-1 1c0 1 0 2-1 2v2l-2 2v-1c-3 3-7 5-10 8-1-1-1-3-1-4 0-10-3-20-7-29-1-2-2-4-4-6l-1-2z"></path><path d="M636 230c6 1 12-2 18 0 0 1 0 1 1 2 1 0 4 2 5 2 2 1 4 3 5 4 3 3 6 7 6 11v1h-1l-1-3c-1-2-1-4-3-5-1-1-3-2-4-4h-1c-1-2-2-2-4-2-1 0-1-1-2-1-1 1-2 1-3 0h0-1l-1 1c-1 0-2 1-3 1h-2c-1 1-2 1-3 1h-1c-1-2-2-4-4-6l-1-2z" class="J"></path><path d="M636 230c6 1 12-2 18 0 0 1 0 1 1 2 1 0 4 2 5 2 2 1 4 3 5 4-2 0-4-2-5-3l-9-3c-4-1-8 1-11 0h-3l-1-2z" class="K"></path><path d="M660 234h5v1c4 0 6 2 8 5 1-1 2-1 2-2 1 1 2 1 3 2h1c1 0 2 0 2 1 1 2 1 3 1 5l-1 3v2l-1 1c0 1 0 3-1 4v2l-3 6h0-1c-2-1-2-2-3-3-1 2-1 5-2 6v-4h0c1-1 1-3 1-4 0-3 0-5-2-7v-3h0v-2l1 3h1v-1c0-4-3-8-6-11-1-1-3-3-5-4z" class="H"></path><path d="M672 261c0-1 0-5 1-6 1 1 1 4 1 6l4-5h1v2l-3 6h0-1c-2-1-2-2-3-3z" class="L"></path><defs><linearGradient id="P" x1="675.561" y1="240.352" x2="674.371" y2="252.388" xlink:href="#B"><stop offset="0" stop-color="#5c4e4b"></stop><stop offset="1" stop-color="#74645c"></stop></linearGradient></defs><path fill="url(#P)" d="M665 235c4 0 6 2 8 5 1-1 2-1 2-2 1 1 2 1 3 2h1c1 0 2 0 2 1 1 2 1 3 1 5l-1 3v2l-1 1c0 1 0 3-1 4h-1-1v-1l-1 1c-1 0-1 1-2 1v-2l1-1v-4c-1-2 0-1-1-1-1-7-4-10-9-14z"></path><path d="M673 240c1-1 2-1 2-2v1l3 3v3c-2-1-4-3-5-5z" class="T"></path><path d="M675 254h1c1-1 1-2 2-4v-1h1 1 1v2l-1 1c0 1 0 3-1 4h-1-1v-1l-1 1c-1 0-1 1-2 1v-2l1-1z" class="F"></path><defs><linearGradient id="Q" x1="373.024" y1="268.355" x2="395.678" y2="308.707" xlink:href="#B"><stop offset="0" stop-color="#9f8d87"></stop><stop offset="1" stop-color="#dcd0cc"></stop></linearGradient></defs><path fill="url(#Q)" d="M377 243c1 1 1 2 1 3l1 1c1 3 2 5 3 7v2c-1 2-1 4-1 6l2 4c4 8 7 17 13 23l10 12c-1 0-1 0-2-1-1 0-1 0-2-1v5l-1 1v1c-2 1-3 1-5 0h0-1l-4-4c-2 1-2 1-4 0v-1c-1 1-1 3-2 4l-1-1c0 1 0 1 1 2 1 2 1 2 1 4l-10-14-4-4-4-6-3-4c1-1 0-2 0-4v-2-1c-1 1-1 1-2 1l1-4s1-1 1-2c0-2 0-3 1-4 2-1 3-2 5-3-1-1-1-2-1-3l1-2 1 3c3-4-1-7 1-10v-1c-1 0-1-1-1-1l3-3c0-1 1-2 2-3h0z"></path><path d="M375 265c1 0 1 0 2-1h-1c0-1 0-2-1-3v-1l-1-2c0-1 0-1 1-2h0c1 1 4 5 3 6v2s0 2 1 3c0 2 1 4 2 6-2-2-5-6-6-8z" class="F"></path><path d="M387 287c1 1 1 1 2 3 0 2 2 4 3 6l-1 1c-1-1-2-1-3-2-3-1-4-2-6-5-1 1-1 1-2 1h0l2-2v1c2-1 3-1 4-1 0-1 0-1 1-2z" class="M"></path><defs><linearGradient id="R" x1="375.154" y1="251.193" x2="376.935" y2="260.79" xlink:href="#B"><stop offset="0" stop-color="#544742"></stop><stop offset="1" stop-color="#786b65"></stop></linearGradient></defs><path fill="url(#R)" d="M377 243c1 1 1 2 1 3l1 1c1 3 2 5 3 7v2c-1 2-1 4-1 6l2 4-1 2h-1l-3-6c1-1-2-5-3-6h0c-1 1-1 1-1 2l1 2v1c1 1 1 2 1 3h1c-1 1-1 1-2 1-1-2-2-3-3-4 3-4-1-7 1-10v-1c-1 0-1-1-1-1l3-3c0-1 1-2 2-3h0z"></path><path d="M377 243c1 1 1 2 1 3l1 1c1 3 2 5 3 7v2c-1 2-1 4-1 6-3-6-4-12-4-19h0z" class="L"></path><defs><linearGradient id="S" x1="369.1" y1="267.234" x2="379.749" y2="291.269" xlink:href="#B"><stop offset="0" stop-color="#715e57"></stop><stop offset="1" stop-color="#bcaaa3"></stop></linearGradient></defs><path fill="url(#S)" d="M371 263l2 2 14 22c-1 1-1 1-1 2-1 0-2 0-4 1v-1c-1 0-3-1-4 0-2 0-2 0-4 1 0 2 0 1 1 2s1 3 1 4l-4-4-4-6-3-4c1-1 0-2 0-4v-2-1c-1 1-1 1-2 1l1-4s1-1 1-2c0-2 0-3 1-4 2-1 3-2 5-3z"></path><path d="M366 269c1 1 1 2 1 4 0 1-1 2-2 2-1 1-1 1-2 1l1-4c1 0 1-2 2-3z" class="J"></path><path d="M371 263l2 2-2 2c-2 1-2 0-3 1h-1l-1 1c-1 1-1 3-2 3 0 0 1-1 1-2 0-2 0-3 1-4 2-1 3-2 5-3z" class="D"></path><path d="M365 276c1-1 2-1 3-1h1s1 1 1 2l-1 1 1 1h-1c-1 1-1 1-1 2-1 2 0 3 0 5l-3-4c1-1 0-2 0-4v-2z" class="F"></path><path fill="#030303" d="M869 273c-2 4-5 6-9 8-5 3-12 5-18 3-4-1-7-3-8-7-2-2-2-6-1-8 1-3 3-7 6-8 1-1 2-1 3 0h0l-1 2c-2 2-3 3-3 6 0 2 1 4 2 5 2 2 5 2 7 2 4-1 8-3 10-6 3-4 4-9 3-13-1-6-4-10-9-14-9-7-21-10-33-8-19 3-34 16-45 31-4 5-8 13-7 20 2 6 6 8 10 11l10 6c-8 1-16 0-25 3-7 3-14 10-17 17-4 13 8 19 12 29 1 4 2 10 0 14-2 3-4 6-8 7-2 1-4 1-6 0s-3-3-4-5c1-1 3 2 5 2h2c1-1 3-2 3-3 2-2 2-4 1-6-1-3-3-5-5-6s-5-2-8-1c-4 2-8 7-10 11-4 7-7 14-10 21l-11 26-32 75-106 260-35 88c-5 13-10 27-16 40-6-13-11-26-16-39l-38-88-74-174-51-118-25-58c-5-11-9-22-15-32-1-4-4-7-6-10-2-2-5-5-8-4-3 0-7 2-9 4-1 1-2 3-1 5 0 2 1 3 2 4 2 1 4 1 6 0h0l1 1-3 3c-2 1-4 1-6 1-2-1-4-3-6-6-3-6-1-13 3-17 3-4 8-7 9-12 2-6 0-11-3-17-4-7-11-12-18-15-5-1-11-2-16-4 5-2 13-4 16-7 2-3 3-6 3-9-2-11-19-28-28-35-11-9-26-16-42-14-10 1-20 6-26 14-3 4-4 8-3 13s4 10 8 13c3 2 6 3 10 2 2 0 4-1 5-3 3-6-4-7-4-11-1-1 0-1 0-2 1 0 2 0 3 1 2 0 5 3 6 6s1 6 0 8c-2 4-5 7-9 8-5 2-11 2-16-1-6-3-11-9-13-16-5-16 5-30 12-43l12-23h76l21-1c4 0 9 1 14 0-1-1-1-1-1-2s-1-2-2-3c0-2 1-3 2-4 0-1 0-2-1-3 0-1 0-2-2-2l-2 1h0v-2c2-2 4-3 5-4 1 0 1 0 1-1 0-2-1-3-2-4v-1h0c2 0 2 1 3 2 1 2 0 5-1 7v1l1 1h1c0-2 0-3 1-4v3c0 1-1 2-1 2h-1c-2 2 0 0 0 2 0 1-1 2-1 4 2 0 3 5 5 7 6 1 13 0 19 0l29 1h74 21c4 0 8-1 11 0 2 3 4 6 5 9l14 27c7 15 14 28 9 45-3 9-8 16-16 19-3 2-6 2-9 2-4 0-8 0-12-3-1-1-2-2-2-4h1c1 1 3 2 5 3 4 1 9 0 13-3s8-9 9-14c1-10-1-20-6-28-10-14-24-19-39-21-1 0-2 1-3 2-6-2-11 2-17 2l-11 1c-3 2-5 3-6 6-2 0-2 0-3-1l-1 1h0c-1 1-2 2-2 3l-3 3s0 1 1 1v1c-2 3 2 6-1 10l-1-3-1 2c0 1 0 2 1 3-2 1-3 2-5 3-1 1-1 2-1 4 0 1-1 2-1 2l-1 4c1 0 1 0 2-1v1 2c0 2 1 3 0 4l3 4 4 6v-1c-1-1-2-1-3-2 0-1-1-2-2-3 0-1-1-1-1-2v1l1 1c0 2 2 3 3 5 0 0 1 1 1 2 1 1 1 1 1 2l1 1c1 1 1 2 1 3 1 2 2 3 3 5 1 4 5 6 7 10 0 2 1 3 2 4l5 7c0 1 1 2 1 3 1 1 1 1 1 2l16 21c1 2 2 3 3 4l1 2 1 2 3 3 1 2 3 4c7 9 13 19 18 29l21 36c1 2 7 13 8 14 2 4 4 8 6 11 2 4 3 8 6 12h1v1c1 1 1 0 1 1 1 1 1 1 1 2h0c1 2 2 3 2 5l8 15c1 1 0 1 1 2s2 2 2 3l-2 6c0-1 1-1 1-2v2 5l-2 4 1 1 2 2c1 1 1 3 2 4h0c-1 3-2 9-1 12l1-1v1l2-5c0-1 1-2 1-3s1-1 1-2v3c2-2 3-4 3-6 0-1 1-1 1-2h2c0-2 1-3 2-4 1-2 2-5 3-7 1-1 2-3 4-3l1 2 1-1c0-2 1-3 2-5 2-1 3-2 5-2l1 1h1l3-4 1-1 1-1c3-3 5-6 7-9 5-6 10-14 16-19l8-8 3-3 2-3c2-1 4-4 6-5 1-1 3-1 4-2 2 0 3-2 5-3l-2-1h-3c-2-1-1-3-2-5-1 0-2-1-2-2-1-1-1-4 0-6 3-11 17-22 27-27 2-3 5-3 7-6 1-4 2-7 4-9 5-5 10-10 15-14l20-18c8-7 14-15 22-23l22-26c2-2 3-4 5-6l4-6 5-7v-6c-1-2-2-2-3-3v-1l2-1-1-1c0-2-1-4-2-6l-1-2c-1-1-1-1-2-3v-3l-1-1-2 2v-4-1l-6-7-4-4v2l-1-1c-1 1-1 1-2 1v1l-1-1c0-2 0-3-1-5 0-1-1-1-2-1h-1c-1-1-2-1-3-2 0 1-1 1-2 2-2-3-4-5-8-5v-1h-5c-1 0-4-2-5-2-1-1-1-1-1-2-6-2-12 1-18 0-11 2-22 8-30 16-7 6-14 16-13 25 0 7 3 12 7 17 3 3 7 5 12 5 1-1 2-1 3 0-2 0-5 1-7 0-4 0-11-3-14-7-4-5-7-11-6-18v-5 1c-1 3-1 5-1 8l1 2c0 6 4 13 10 17 5 3 11 5 17 4 4-1 7-3 9-7h0c0 4-3 7-6 9-4 3-10 4-16 2-7-1-13-7-17-13-5-7-7-18-5-27h0c3-11 9-22 13-33 4-8 7-17 11-25 2 1 3 3 5 5h2c1 1 1 2 3 2h12 23c3 0 7 0 9-1s4-1 6-2h19 12 17c3 1 7 0 9 1s7-1 7 0c-1 0-3 0-4 1h-2-2 63 16c2 0 5 0 7 1 7 1 14 1 20 1l12-1c1 0 4 0 5 1 4 2 7 10 9 15l7 11c1 1 1 2 3 2-1-4-2-11-4-15-2-3-7-11-6-14l8 16c7 12 17 26 13 41-1 3-2 7-4 8z"></path><path d="M681 241l5 3v2l-1-1c-1 1-1 1-2 1v1l-1-1c0-2 0-3-1-5z" class="T"></path><path d="M813 216c1-1 2-1 3 0h1v-1c1 0 1 0 2 1h-1c1 1 1 1 3 1v-1l2 1-1 1h-1-1-2c-1 1-2 1-4 1l-2-2 1-1z" class="R"></path><path d="M743 217c1 0 1 0 2 1h1 0l-2-2 1-1c1-1 2 0 3-2 2 1 2 1 2 3-1 1-2-1-2 1s0 2 1 3h1c-1 1-1 3-2 4h-1-1c-1-1-1 0-2 0v-1-1c-1-2-1-2-1-5z" class="K"></path><path d="M545 682l1 1c1 6 6 13 9 17h1c2 3 3 7 5 10-7-6-12-16-16-24v-4z" class="N"></path><path d="M654 230c3 0 6 1 10 2h1c6 2 13 5 16 9 0-1-1-1-2-1h-1c-1-1-2-1-3-2 0 1-1 1-2 2-2-3-4-5-8-5v-1h-5c-1 0-4-2-5-2-1-1-1-1-1-2z" class="J"></path><path d="M389 236c8-3 21-6 29-4-1 0-2 1-3 2-6-2-11 2-17 2l-11 1 2-1zm339-11c-1-1-1-1-2-1l1-3h-1l-1-1v-1h-1c-1-1-1-1-2-1h-1c1-1 1-1 2-1 1 1 3 0 5 1h1v-1c1 0 1 0 2 1h0c-2 0-2 0-3 1 0 1 1 2 2 2 0-1 0 0 1-1l1 1 1 1 1-1c0 1 0 1 1 1h1v-1-3h0l-1-1h1c2 0 2 1 4 2v2h2v4h0c-1 0-2 1-3 1h-2c-1-1 0-2-3-2 0 2 0 3-1 4l-1-1 1-2h-1c-1-1 0-1-1-2h0c-1 0-2 0-3 1v1z" class="K"></path><defs><linearGradient id="T" x1="546.635" y1="691.837" x2="553.962" y2="681.049" xlink:href="#B"><stop offset="0" stop-color="#cabbbb"></stop><stop offset="1" stop-color="#e7ddd9"></stop></linearGradient></defs><path fill="url(#T)" d="M546 671c1-2 0-3 0-4l-1-1 1-1c-1-2-1-2-1-4 2 7 3 15 6 21l5 18h-1c-3-4-8-11-9-17 0-3-1-5-1-8l2 2v-1-4l-1-1h0z"></path><path d="M749 220h0c1-1 2-1 2-2v-1h0 1l1 1 1-1 1-1h1 0-3v-1h-1l-1-1c1-1 1-1 2-1h1 2c-1 1-2 1-2 2h2 1 0v1c1 0 1-1 3 0h0v1l-3 1c-1 0 0 1 0 1v1l1-1c1 0 2 1 3 1l1 1c-1 0-1 1-2 2h-1l-1-1 1 2v1h1c-2 0-3 1-4 0-1 0-1-1-1-1-1 2-1 2-3 2l-1-1-1 1c0 1 0 0 1 1l-1 1s-1 1-2 1h0-1l1 1h-1v2c1 0 1 0 2 1h0-2c-1 0-1 1-1 2s-1 1-1 1c-1 0-1 0-2-1h0v-1h1 0l1 1v-2l1-1h-1c-1-1-1-2-1-3h0 2c-1-2-1-2-3-4v-2h1v1c1 0 1-1 2 0h1 1c1-1 1-3 2-4h-1z" class="S"></path><defs><linearGradient id="U" x1="463.857" y1="299.506" x2="462.127" y2="282.344" xlink:href="#B"><stop offset="0" stop-color="#1d1c23"></stop><stop offset="1" stop-color="#4c4c4a"></stop></linearGradient></defs><path fill="url(#U)" d="M460 243v-1c5 8 9 15 10 25v7 1 1c0 1 0 0-1 1v5h1c2-1 2-1 4-1-3 9-8 16-16 19-3 2-6 2-9 2l2-1c9-6 14-11 17-21 4-13-2-25-8-37z"></path><path d="M561 728h0c-2 4-7 9-11 11-4 3-7 4-12 4-6 0-12-5-16-9v-2l-2-1v-1l3 2c1 0 1 0 2 1 3 4 8 5 13 7l2-1c1 0 1 0 1-1v-1c-1-1-1-1-2-1-1-1-1-1-2-1h3c5 0 9-2 14-3 1-1 3-1 4-1 1-1 2-3 3-3z" class="S"></path><path d="M376 367c-2-3-3-6-5-9-6-11-12-24-15-36l32 48c-1 0-2-1-3-1 0-1-1-2-2-3-2-3-4-7-6-9-1-2-2-4-4-6 0 2 2 4 2 6 1 2 2 3 3 5-1 0-1 0-2-1v-1c-1 1-2 1-2 2s3 3 2 5z" class="M"></path><path d="M306 303c3 1 7 11 10 13l1 1c1 2 3 3 4 5 1 1 2 2 3 4h-1c-1-1-2-2-3-4-1-1-1-2-2-3v1l2 3 2 5 4 6h0l1 1c0 1 0 1 1 2v1s1 1 1 2h0l2 3 2 3 3 6h0l1 1 1 2c0-1 0-2 1-3h1v4 1c1 1 0-1 1 1 1 1 2 2 2 3v1l2 3v1l1 2 1 1h-1 0v2h0l1 1c1 1 2 3 2 5 1 1 1 2 2 3l1 1c0 2 2 4 3 7l1 1h0l1 2h0v1h1v3h0v1c1 2 2 4 4 7l1 2 1 1c0 1 0 2 1 3h-1l-58-106z" class="K"></path><path d="M742 221c1-1 0-1 0-2h0c-1 1-1 1-2 0h0l1-2h1 1c0 3 0 3 1 5v1h-1v2c2 2 2 2 3 4h-2l-1 1c-1 1-1 2-3 3l-3-3-2 1v-1c-1 0-1 0-2 1h1 1l1 1v1l1-1h0v1h-1c-1 0 0 0-1 1v1l2-1h0 2 1 0l1 1 1-1 1 1c-1 1-2 0-3 0h-1c-1 0-2 1-2 1l-1 1 1-1 1 1-1 1c-1-1-1 0-2-1v-1h-1l-1-1c1-1 1-1 1-2s-1-1-1-1v2 2c-1 0-2 1-2 1l-2 2-2-2h0l-1-1c-1-1-2-1-2-2h-1-1c-1 1-1 1-1 2-2 0-2-1-3-1 0-1 0-2 1-3h1 0-2l-1-1s0-1 1-1 1-1 2-1h2 0c-1-1-1-1-2-1h0c-1-1-1-1-1-2v-1c0-1 0-1-1-2 1 0 1 0 2-1l1 1-1 1v1c1 0 1 1 3 0h1c0-1 0 0 1-1s0 0 1 1h2v-1c1-1 2-1 3-1h0c1 1 0 1 1 2h1l-1 2 1 1c1-1 1-2 1-4 3 0 2 1 3 2h2c1 0 2-1 3-1h0v-4z" class="R"></path><path fill="#6b6b6e" d="M859 235c1 1 1 2 3 2-1-4-2-11-4-15-2-3-7-11-6-14l8 16c7 12 17 26 13 41-1 3-2 7-4 8s-6 5-7 6h-1c3-3 6-9 6-13 2-9 0-18-6-26s-16-12-26-14v-1c5 1 8 2 12 4 2 0 5 2 7 4 1 0 3 2 5 2z"></path><path d="M681 378h0c1-5 5-11 9-13-3 5-5 9-6 14l-8 20c-1 1-2 4-2 4-2 1-4 6-5 7l-8 16-6 12-1-2c-1 3-3 7-5 9h0c0-1 1-2 1-3h1c0-1 1-2 1-3v-1c1-1 1-2 2-3 0-4 3-9 5-13-1-1-1-2-1-3 2-1 3-6 5-8l5-10c1-2 2-3 3-4 1-2 2-4 2-6h0c2-2 4-5 4-7v-1c2-1 3-3 4-5z" class="D"></path><path d="M659 422c1-1 1-2 2-3 1 0 1 1 2 1l-9 16c-1 3-3 7-5 9h0c0-1 1-2 1-3h1c0-1 1-2 1-3v-1c1-1 1-2 2-3 0-4 3-9 5-13z" class="G"></path><defs><linearGradient id="V" x1="679.516" y1="386.817" x2="672.484" y2="392.683" xlink:href="#B"><stop offset="0" stop-color="#a1897d"></stop><stop offset="1" stop-color="#b4a29e"></stop></linearGradient></defs><path fill="url(#V)" d="M681 378c1 3-4 13-5 17-1 1-2 3-3 4 0-1 0-2 1-3h0l-1-2c0 1-1 1-1 2l-1 1c1-2 2-4 2-6h0c2-2 4-5 4-7v-1c2-1 3-3 4-5z"></path><defs><linearGradient id="W" x1="677.031" y1="402.6" x2="656.091" y2="410.95" xlink:href="#B"><stop offset="0" stop-color="#bba9a3"></stop><stop offset="1" stop-color="#e3dcdc"></stop></linearGradient></defs><path fill="url(#W)" d="M671 397l1-1c0-1 1-1 1-2l1 2h0c-1 1-1 2-1 3-2 7-7 14-10 21-1 0-1-1-2-1-1 1-1 2-2 3-1-1-1-2-1-3 2-1 3-6 5-8l5-10c1-2 2-3 3-4z"></path><defs><linearGradient id="X" x1="412.368" y1="195.368" x2="417.109" y2="210.674" xlink:href="#B"><stop offset="0" stop-color="#262627"></stop><stop offset="1" stop-color="#444346"></stop></linearGradient></defs><path fill="url(#X)" d="M340 200h74 21c4 0 8-1 11 0-1 2-1 3-2 5l-2-2c-1 0-1 0-1 1-1 1-2 1-3 1h-1c-4 1-9 1-13 0l-21 1-32-1v-1h3 5 5 3v-1c3 0 4 0 7-1h1v-1l1 1 2-1h2 0c2 0 7 1 8 0-1-1-3-1-4 0h-6c-2-1-5-1-7-1h-7c-8-1-17 0-25 0-5 0-10-1-15 0h-4z"></path><path d="M824 209h19c4 2 7 10 9 15l7 11c-2 0-4-2-5-2-2-2-5-4-7-4-4-2-7-3-12-4v1c-4 0-9 0-13 1-6 0-11 2-17 3-1 1-3 2-4 2-3 1-5 2-7 4l-1-1c0-1 1-2 2-2l2-1 1-1c1 0 2-1 3-1l4-2h1c3-1-2 1 2-1 1 0 2 0 4-1h0 1c1 0 1-1 2-1h1 1 2c4-1 10-1 15-1h0l1-1c0-1 1-1 2-1l-1-1c0-1 0-2 1-2v-1c-1-1-2-1-3-2l1-1v1h1l-1-1h1l-2-1v1l-2-1 2-1h0v-1h-1 0v-1l1-1h-9l-1-1zm-44 35h2c-3 3-6 5-9 8-1 1-2 2-3 4-1 1-2 1-2 2-1 1 0 1-1 2s-1 0-1 1c-1 2-2 3-3 4v1c-1 1-2 2-3 4l-1 2-26 51h0c-1-1 3-7 3-9l3-5v-1c1-2 1-4 2-6l2-3h0-1l2-7 2-4c0-1 1-2 1-4 0 1-1 2-2 3-3 3-5 8-8 11h-1c0-1 1-3 2-4l5-8 3-6v-1l3-3v-1c1 0 1 0 1-1v-1c1-1 2-1 2-2s1-1 1-2h0c1-1 1-1 1-2 1-1 2-2 3-4 0 0 0-1 1-1v-1l2-2 4-5c-2-1-2 0-4 0 1 0 2-1 3-1 4-1 8-6 11-8h0l-4 4v1l-1 1s0 1 1 1 1-1 1-1l6-6c1 0 2 0 3-1h0z" class="K"></path><path d="M355 271c0-2-1-3 0-3 0-1 1-2 1-3h0l1-2-1-1c-1 1-1 2-2 3l-1 2c-1 2-2 3-2 5-1 3-1 7-3 9l-2 2h0 0v-2l1-1h1v-2c1-2 0 0 1-1v-2c0-1 0-2 1-2v-1c0-1 1-3 2-4v-2h1c0-2 1-2 1-3 1-1 2-2 3-4h0c1-1 1-2 1-2 2-2 3-4 4-6h1c1 0 2-1 2-2v-1l-2 2h-1l3-3c0-1 1-1 2-2v-1c2 0 3-1 4-2l1-1h0 1l1 1c1-1 2-2 4-3h1s1-1 2-1l1 1s1 0 1-1h1 0l-1-1h0-1v-1h0c1 0 1-1 2-1l2-1h0c1 0 2-1 3-1l3-1 1 1c-1 0-3 1-5 2l1 1-2 1c-3 2-5 3-6 6-2 0-2 0-3-1l-1 1h0c-1 1-2 2-2 3l-3 3s0 1 1 1v1c-2 3 2 6-1 10l-1-3-1 2c0 1 0 2 1 3-2 1-3 2-5 3-1 1-1 2-1 4 0 1-1 2-1 2l-1 4c1 0 1 0 2-1v1 2c0 2 1 3 0 4l-6-9-3-4-1 2z" class="R"></path><path d="M356 269l2-2c0-3 2-3 3-5 1-1 1-2 2-2 0-1 4-4 4-4 0-1-1-1-1-2 1 0 1-1 2-2h3v-3h1s0 1 1 1v1c-2 3 2 6-1 10l-1-3-1 2c0 1 0 2 1 3-2 1-3 2-5 3-1 1-1 2-1 4 0 1-1 2-1 2l-1 4c1 0 1 0 2-1v1 2c0 2 1 3 0 4l-6-9-3-4z" class="K"></path><path d="M363 263h2l1-1h2c0-1 1-1 1-2-1-2-1-3 0-5h0c1 1 1 2 2 3l-1 2c0 1 0 2 1 3-2 1-3 2-5 3-1 1-1 2-1 4-1-3-1-5-2-7z" class="T"></path><defs><linearGradient id="Y" x1="361.304" y1="266.539" x2="362.928" y2="274.612" xlink:href="#B"><stop offset="0" stop-color="#423836"></stop><stop offset="1" stop-color="#5c4f4b"></stop></linearGradient></defs><path fill="url(#Y)" d="M359 273c1-2 0-4 0-5 1-2 3-2 4-4v-1c1 2 1 4 2 7 0 1-1 2-1 2l-1 4c1 0 1 0 2-1v1 2c0 2 1 3 0 4l-6-9z"></path><path fill="#6b6b6e" d="M649 208c3 0 7 0 9-1s4-1 6-2h19 12 17c3 1 7 0 9 1s7-1 7 0c-1 0-3 0-4 1h-2-2 63 16c2 0 5 0 7 1 7 1 14 1 20 1l12-1c1 0 4 0 5 1h-19-210c-2 0-3-1-5-3h2c1 1 1 2 3 2h12 23z"></path><path d="M649 208c3 0 7 0 9-1s4-1 6-2h19 12 17c3 1 7 0 9 1s7-1 7 0c-1 0-3 0-4 1h-2-2l-71 1z" class="D"></path><defs><linearGradient id="Z" x1="453.212" y1="279.009" x2="465.944" y2="211.805" xlink:href="#B"><stop offset="0" stop-color="#494a4b"></stop><stop offset="1" stop-color="#a4a3a5"></stop></linearGradient></defs><path fill="url(#Z)" d="M445 214l1-1c1-1 1-2 1-4h-2v-1h0l1 1h5l14 27c7 15 14 28 9 45-2 0-2 0-4 1h-1v-5c1-1 1 0 1-1v-1-1-7c-1-10-5-17-10-25v1c-2-2-4-6-5-10l-10-19z"></path><path d="M446 200c2 3 4 6 5 9h-5l-1-1h0v1h2c0 2 0 3-1 4l-1 1c-1-2-2-4-5-5-5 1-11 0-17 0h-40-71c-14 0-30 1-44-1h0l64-1h1c3-1 9-1 12-1 1 1 3 1 5 1 1-1 2-1 4-1h12 0l1-1h4l32 1 21-1c4 1 9 1 13 0h1c1 0 2 0 3-1 0-1 0-1 1-1l2 2c1-2 1-3 2-5z" class="D"></path><path d="M446 200c2 3 4 6 5 9h-5l-1-1h0v1h2c0 2 0 3-1 4l-1 1c-1-2-2-4-5-5-2-1-7-1-10-1l11-1c2 0 2-1 3-2 1-2 1-3 2-5z" class="L"></path><defs><linearGradient id="a" x1="379.399" y1="202.333" x2="382.784" y2="210.087" xlink:href="#B"><stop offset="0" stop-color="#2d3337"></stop><stop offset="1" stop-color="#4d4b51"></stop></linearGradient></defs><path fill="url(#a)" d="M332 207h1c3-1 9-1 12-1 1 1 3 1 5 1 1-1 2-1 4-1h12 0l1-1h4l32 1c-2 0-5 0-7 1s-5 0-7 0h-13-44z"></path><defs><linearGradient id="b" x1="693.008" y1="238.334" x2="699.612" y2="264.135" xlink:href="#B"><stop offset="0" stop-color="#353437"></stop><stop offset="1" stop-color="#5b5d5f"></stop></linearGradient></defs><path fill="url(#b)" d="M692 247c-9-10-18-15-30-19h0c3 0 7 1 10 2h2l1 1h0l2 1h1l1 1h0c2 0 3 2 5 3v1c1 0 2 1 3 1 1 1 2 1 2 2l1 1v-2c-3-3-5-5-8-7h-1l-1-1c-2-1-6-2-7-4l-1-1c-1 0-2-1-3-1h-1l-8-2c-2-1-3 0-4-1 1-1 2-1 3-1l1 1h3c1 0 1 0 2 1 1 0 2 0 3 1h1l5 2c2 1 5 2 8 4l8 6 5 5c9 9 15 22 17 35 1 5 1 10 1 16 0 2-1 7-2 9 0 1-1 1-1 1-1 2-1 6-1 7l-3 9v-2-1c2-8 3-14 3-22v-8l-1-4c-1-5-2-10-4-14h-2l-1-2c-1-1-1-1-2-3v-3l-1-1-2 2v-4-1l-6-7c1 0 1 0 2 1h1c0-1-1-1-1-2z"></path><path d="M701 259c2 3 2 5 3 8h-2l-1-2c-1-1-1-1-2-3v-3l1 1 1-1z" class="S"></path><path d="M692 247c4 3 7 8 9 12l-1 1-1-1-1-1-2 2v-4-1l-6-7c1 0 1 0 2 1h1c0-1-1-1-1-2z" class="R"></path><path d="M708 281v-1-2-1c0-1 0-2-1-3v-2c-1-2-1-4-2-5v-2l-1-1s0-1 1-1h1v-1h0 1l1 2v1l1 1v2c1 3 2 6 2 8 0 1 1 3 1 4 1 2 0 9 0 12h1c0 2-1 7-2 9 0 1-1 1-1 1-1 2-1 6-1 7l-3 9v-2-1c2-8 3-14 3-22v-8l-1-4z" class="D"></path><defs><linearGradient id="c" x1="626.852" y1="490.587" x2="639.259" y2="497.205" xlink:href="#B"><stop offset="0" stop-color="#c7b7b4"></stop><stop offset="1" stop-color="#ebe7e3"></stop></linearGradient></defs><path fill="url(#c)" d="M655 438l6-12 8-16c1-1 3-6 5-7l-25 58-35 81-8 18s-1 4-2 4c-1 1-2 2-2 4h0v-2c2-3 1-6 3-8l1-3v-3h0l-1-1v-1l2-7 14-32c1-2 1-2 1-4v-3h1l1-2v-1l1-1c1-2 2-3 2-4s0-2 1-3v-2c1 0 1 0 1-1l1-1 1-3 1-1 1-2c2-4 3-8 5-11v-1l2-2v-1l2-5 2-3 1-2 1-2 1-3v-1l1-1 2-3h0l3-7h1c0-1 1-2 1-3z"></path><path d="M376 367c1-2-2-4-2-5s1-1 2-2v1c1 1 1 1 2 1-1-2-2-3-3-5 0-2-2-4-2-6 2 2 3 4 4 6 2 2 4 6 6 9 1 1 2 2 2 3 1 0 2 1 3 1l15 24 39 68c1 2 5 8 5 10 2 3 3 6 5 9l7 15h1c0 1 1 1 1 2 1 4 4 7 5 11 1 2 1 3 2 5l2 4 2 4 1 3c0 2 1 3 2 5 0 2 1 4 3 6l2 6c0 1 1 2 1 3 0 0 1 1 1 2v2l1 1c0 1 1 3 1 5 0 0 1 1 1 2s0 1 1 2v1l5 10c2 3 3 7 5 10 1 3 3 7 3 11-2-3-4-8-6-11l-11-24-9-17s-1-2-2-3l-5-10-19-36-41-73-30-50z" class="E"></path><path d="M447 472c2 3 3 6 5 9l7 15c0 1 0 2 1 3l6 13 1 2 1 3v1c2 4 4 8 5 12 0 1 1 2 1 3l1 2c0 1 0 0 1 2l2 4 1 2v1s0 1 1 1v1 2l1 1v2l1 2v3l-9-17c2 2 4 5 5 7 0 2 1 3 2 4h0l-2-6c-1-3-2-5-3-7 0-1-1-2-1-3l-2-4-1-2c0-1 0-1-1-2v-2c-1 0-1-2-2-3 0 0-1 0-1-1-1-1-2-4-2-5l-3-6c-1-2-1-3-2-5-3-5-5-11-8-16-1-5-4-9-5-13-1-1 0-2 0-3z" class="G"></path><path d="M460 496c0 1 1 1 1 2 1 4 4 7 5 11 1 2 1 3 2 5l2 4 2 4 1 3c0 2 1 3 2 5 0 2 1 4 3 6l2 6c0 1 1 2 1 3 0 0 1 1 1 2v2l1 1c0 1 1 3 1 5 0 0 1 1 1 2s0 1 1 2v1l5 10c2 3 3 7 5 10 1 3 3 7 3 11-2-3-4-8-6-11l-11-24v-3l-1-2v-2l-1-1v-2-1c-1 0-1-1-1-1v-1l-1-2-2-4c-1-2-1-1-1-2l-1-2c0-1-1-2-1-3-1-4-3-8-5-12v-1l-1-3-1-2-6-13c-1-1-1-2-1-3h1z" class="C"></path><defs><linearGradient id="d" x1="508.553" y1="603.52" x2="531.337" y2="594.789" xlink:href="#B"><stop offset="0" stop-color="#d8ccca"></stop><stop offset="1" stop-color="#fbfcf9"></stop></linearGradient></defs><path fill="url(#d)" d="M508 518c0-2 1-3 2-4 0 2-1 4-1 6-1 3-1 5 0 7s0 4 1 7c3 10 8 20 11 31 0 3 1 6 2 8l1 1c0 5 2 11 4 16l2-13c0-4 1-13 3-16l-4 25c-1 10 0 20-1 30-1 1-1 2 0 4l-2 1v2c1 1 3 2 3 4 0 1 0 7 1 8 1 0 1 1 2 3l1-1c1 5 1 10 2 15 1 1 0 2 1 4h0l4 9 2 1 3 9c0 3 1 5 1 8l-1-1v4l-3-6-13-25-13-29c-3-5-6-12-8-17l-9-18c0-4-2-8-3-11 1-4-1-6-2-9v-3c-2-5-6-11-7-16s-2-13 1-18l5 13c1 3 1 5 2 8 0-7-4-14-5-20 0-3-1-6 0-9v-1c1-4 2-7 3-11l1 1 2 2c1 1 1 3 2 4h0c-1 3-2 9-1 12l1-1v1l2-5c0-1 1-2 1-3s1-1 1-2v3c2-2 3-4 3-6 0-1 1-1 1-2h2z"></path><path d="M508 518c0-2 1-3 2-4 0 2-1 4-1 6-1 3-1 5 0 7s0 4 1 7c3 10 8 20 11 31 0 3 1 6 2 8l1 1c0 5 2 11 4 16l2-13c0-4 1-13 3-16l-4 25c-1 10 0 20-1 30-1 1-1 2 0 4l-2 1v-2c2-3 1-6 1-9 0-12-3-23-5-34l-9-30-3-10c-1-3-3-5-3-8h0v-2c0-3 0-5 1-8z" class="O"></path><path d="M531 652c-3-9 0-20-5-28h-1l1-1c1 1 3 2 3 4 0 1 0 7 1 8 1 0 1 1 2 3l1-1c1 5 1 10 2 15 1 1 0 2 1 4h0l4 9 2 1 3 9c0 3 1 5 1 8l-1-1v4l-3-6-13-25h1c0 1 1 2 1 3l4 8h1v-1c-1-2-1-3-2-4-1-2-2-3-3-5s-1-2 0-4z" class="Q"></path><path d="M532 651l3 1c1 1 0 2 1 4-2 0-2 0-3-1-1-2-1-2-1-4z" class="P"></path><path d="M540 665l2 1 3 9c0 3 1 5 1 8l-1-1c-3-6-4-12-5-17z" class="B"></path><path d="M530 635c1 0 1 1 2 3l1-1c1 5 1 10 2 15l-3-1h1c-1-2-1-4-1-5l-2-11z" class="M"></path><path d="M531 652c0 2 1 4 2 6s2 3 3 5c3 5 4 11 6 17h0l-13-25h1c0 1 1 2 1 3l4 8h1v-1c-1-2-1-3-2-4-1-2-2-3-3-5s-1-2 0-4z" class="I"></path><path d="M502 526c2-2 3-4 3-6 0-1 1-1 1-2h2c-1 3-1 5-1 8v2h0-1c-1 1-1 3-1 4-1 2-1 5-2 7-4 22 1 45 7 66 2 6 6 14 6 21-3-5-6-12-8-17 0-1-4-16-5-19-3-14-5-28-5-42 1-8 2-15 4-22z" class="F"></path><path d="M493 514l1 1 2 2c1 1 1 3 2 4h0c-1 3-2 9-1 12l1-1v1l2-5c0-1 1-2 1-3s1-1 1-2v3c-2 7-3 14-4 22 0 14 2 28 5 42 1 3 5 18 5 19l-9-18c0-4-2-8-3-11 1-4-1-6-2-9v-3c-2-5-6-11-7-16s-2-13 1-18l5 13c1 3 1 5 2 8 0-7-4-14-5-20 0-3-1-6 0-9v-1c1-4 2-7 3-11z" class="P"></path><path d="M353 278c4 5 10 11 13 16h-2c-2-1-3-4-5-5 1 3 2 7 5 9 1 2 2 5 3 7 1 1 2 2 2 4 1 1 2 3 3 5l12 20c1 2 2 4 4 6 1 6 5 12 8 17 1 2 2 5 4 8s4 7 6 11c1 2 3 4 4 6l-1 1 26 42c15 26 30 53 42 80l7 18c1 2 1 5 3 8l1 3c-3 5-2 13-1 18s5 11 7 16v3c1 3 3 5 2 9-2-3-3-7-5-10l-5-10v-1c-1-1-1-1-1-2s-1-2-1-2c0-2-1-4-1-5l-1-1v-2c0-1-1-2-1-2 0-1-1-2-1-3l-2-6c-2-2-3-4-3-6-1-2-2-3-2-5l-1-3-2-4-2-4c-1-2-1-3-2-5-1-4-4-7-5-11 0-1-1-1-1-2l-98-168c-3-4-8-11-9-16l-1-3c-1-3-1-7-1-10 1-3 0-7 1-11 0 1 1 1 1 1 0-1 1-2 1-3-1-3-1-4-1-8z" class="B"></path><path d="M484 523c1 2 1 5 3 8l1 3c-3 5-2 13-1 18s5 11 7 16v3c1 3 3 5 2 9-2-3-3-7-5-10l-5-10v-1c-1-1-1-1-1-2s-1-2-1-2c0-2-1-4-1-5l-1-1v-2c0-1-1-2-1-2 0-1-1-2-1-3l-2-6v1l3 2h0c-1-2-1-4-2-7 0-1-2-4-1-5 0 2 2 5 3 8h0 1l-1-1-1-4c0-1-1-2-1-3-1-1 0-1 0-2l4 10h0v-1c0-1 0-2 1-2v-1-3-5z" class="G"></path><path d="M356 304c2 1 3 1 5 2h2l5 8c2 3 5 7 7 11-3-2-5-3-7-6-1 0-2-2-3-2l1 1v1c1 1 1 2 2 4s2 4 3 7h0c-1-1-1-1-1-2-1-1-2-4-3-5h-1c1 2 2 3 1 4v1h0l2 2c1 1 2 3 3 4h-1c-3-3-5-7-8-10-2-2-3-5-5-7l-1 1v1h0c2 3 5 6 5 9-3-4-8-11-9-16l-1-3h0 2 0c2 1 2 1 3 0-1-1-1-2-2-3 1 0 1 1 2 1l-1-3z" class="I"></path><path d="M356 304c2 1 3 1 5 2h2l5 8c-2-1-2-1-3-1s-2-2-2-2c-1-1-2-1-2-2l-1-1v1c0 2 2 4 3 6 0 0 1 1 1 2l-1-1h-1c1 2 1 3 2 5h0v2c-2-1-4-5-5-7-2-2-3-4-5-7 2 1 2 1 3 0-1-1-1-2-2-3 1 0 1 1 2 1l-1-3z" class="L"></path><path d="M353 278c4 5 10 11 13 16h-2c-2-1-3-4-5-5 1 3 2 7 5 9 1 2 2 5 3 7 1 1 2 2 2 4 1 1 2 3 3 5l12 20h-1-1c1 2 1 2 0 4-2-3-6-8-6-12l-1-1c-2-4-5-8-7-11s-3-5-5-8h-2c-2-1-3-1-5-2l1 3c-1 0-1-1-2-1 1 1 1 2 2 3-1 1-1 1-3 0h0-2 0c-1-3-1-7-1-10 1-3 0-7 1-11 0 1 1 1 1 1 0-1 1-2 1-3-1-3-1-4-1-8z" class="D"></path><path d="M356 304c-2-1-3-6-4-8l1-2 2 1 1 2c1 2 3 3 4 5l3 4h-2c-2-1-3-1-5-2z" class="H"></path><path d="M353 278c4 5 10 11 13 16h-2c-2-1-3-4-5-5 1 3 2 7 5 9 1 2 2 5 3 7 1 1 2 2 2 4 1 1 2 3 3 5l-2-1c-1-2-3-6-5-8l-11-19c-1-3-1-4-1-8z" class="L"></path><path d="M360 302c2 0 3 1 4 2 0 1 1 1 1 1 2 2 4 6 5 8l2 1 12 20h-1-1c1 2 1 2 0 4-2-3-6-8-6-12l-1-1c-2-4-5-8-7-11s-3-5-5-8l-3-4z" class="N"></path><path d="M360 302c2 0 3 1 4 2 0 1 1 1 1 1 2 2 4 6 5 8l5 11c1 1 1 1 1 2l-1-1c-2-4-5-8-7-11s-3-5-5-8l-3-4z" class="F"></path><path fill="#fff" d="M366 318l-1-1c1 0 2 2 3 2 2 3 4 4 7 6l1 1c0 4 4 9 6 12 1-2 1-2 0-4h1 1c1 2 2 4 4 6 1 6 5 12 8 17 1 2 2 5 4 8s4 7 6 11c1 2 3 4 4 6l-1 1 26 42c15 26 30 53 42 80l7 18v5 3 1c-1 0-1 1-1 2v1l-1-5c-3-9-8-17-12-24l-17-36c-1-3-3-5-4-7l-10-17c-2-4-3-8-6-12l-5-8-25-43-18-31c-1-3-3-5-5-8-1-2-1-4-3-5-1-2-1-3-2-4-3-6-5-12-9-17z"></path><path d="M368 319c2 3 4 4 7 6l1 1c0 4 4 9 6 12l7 12c-1-1-2-1-2-2-2-2-5-4-6-6 0-1-1-2-2-3v-1c0-2-2-5-3-6s-1-1-1-2c-1-1-2-3-3-4-2-2-3-4-4-7z" class="B"></path><path d="M384 334c1 2 2 4 4 6 1 6 5 12 8 17 1 2 2 5 4 8s4 7 6 11c1 2 3 4 4 6l-1 1-20-33-7-12c1-2 1-2 0-4h1 1z" class="M"></path><path d="M658 419c0 1 0 2 1 3-2 4-5 9-5 13-1 1-1 2-2 3v1c0 1-1 2-1 3h-1c0 1-1 2-1 3h0c2-2 4-6 5-9l1 2c0 1-1 2-1 3h-1l-3 7h0l-2 3-1 1v1l-1 3-1 2-1 2-2 3-2 5v1l-2 2v1c-2 3-3 7-5 11l-1 2-1 1-1 3-1 1c0 1 0 1-1 1v2c-1 1-1 2-1 3s-1 2-2 4l-1 1v1l-1 2h-1v3c0 2 0 2-1 4l-14 32c-1 3-1 4-2 7v1l1 1h0v3l-1 3c-2 2-1 5-3 8v2h0c0-2 1-3 2-4l-14 35-10 23c0 2-1 4-2 6-1 4-3 8-5 11-1 5-3 10-5 15l-5 13c-1 0-2-1-2-2-2-2-1-8-1-10 0-1 1-2 0-3v-1-5h0v-5-1c1-1 1-3 0-4 0-2 0-5 1-6l4-17c0-1 1-3 1-4l6-19c0-4 2-7 3-10 3-8 5-16 9-23l3-10c1-1 2-3 2-5l14-31c1-1 2-3 3-4 1-4 3-8 6-12 1-2 2-5 4-8l6-13c1-3 3-6 6-9l1 1 11-18c3-7 8-13 12-20 2-3 3-6 6-9z" class="P"></path><path d="M578 615c2 2 0 7 0 11l-1 1 1 1c-1 4-3 8-5 11 0-3 1-6 1-9 2-5 3-10 4-15z" class="D"></path><path d="M622 474c1-3 3-6 6-9l1 1c-2 6-5 11-8 16l-7 14-2-1c1-2 2-5 4-8l6-13z" class="N"></path><path d="M658 419c0 1 0 2 1 3-2 4-5 9-5 13-1 1-2 3-3 4l-1-1h1l2-4v-1l1-3h0c-1 3-5 6-5 9 0 1-1 2-1 3l-1-1h0v-3l1-1h-1c-1 2-4 7-4 9-1 1-2 2-3 2 3-7 8-13 12-20 2-3 3-6 6-9z" class="E"></path><path d="M584 557l3-8c1-1 1-2 3-2l-13 35c-1 2-3 6-3 8s-1 3-2 4c0-1 1-3 0-4 0-4 2-7 3-10 3-8 5-16 9-23z" class="N"></path><path d="M591 572v4c-2 8-6 18-6 26-2 6-3 12-5 17v3c0 2-1 4-2 6l-1-1 1-1c0-4 2-9 0-11 3-15 8-29 13-43z" class="F"></path><path d="M603 511c1-1 2-3 3-4 1-4 3-8 6-12l2 1c-2 4-24 51-24 51-2 0-2 1-3 2-1 3-2 5-3 8l3-10c1-1 2-3 2-5l14-31z" class="C"></path><path d="M572 590c1 1 0 3 0 4 1-1 2-2 2-4 0 4 0 7-1 10-2 5-3 10-4 15l-3 9-3 17c-1 2-1 5-1 7v3c-1 1 0 3 0 4s-1 2-1 4l1-3v-2h1c0-1 1-3 1-3 1-4 2-9 4-12 1-2 1-5 1-7l4-15 1-2v-2c1-1 1-2 1-3l1-1c0 1-1 2-1 3s0 1-1 2v2c-1 4-2 8-3 13l-2 13c-1 3-2 7-2 10v2h1l-5 13c-1 0-2-1-2-2-2-2-1-8-1-10 0-1 1-2 0-3v-1-5h0v-5-1c1-1 1-3 0-4 0-2 0-5 1-6l4-17c0-1 1-3 1-4l6-19z" class="G"></path><path d="M572 590c1 1 0 3 0 4l-6 22v-3h-1c0-1 1-3 1-4l6-19z" class="O"></path><path d="M565 613h1v3c-1 4-1 8-2 12-1 5-3 9-3 14 0 1 0 2-1 4v-5-1c1-1 1-3 0-4 0-2 0-5 1-6l4-17z" class="H"></path><path d="M649 445c2-2 4-6 5-9l1 2c0 1-1 2-1 3h-1l-3 7h0l-2 3-1 1v1l-1 3-1 2-1 2-2 3-2 5v1l-2 2v1c-2 3-3 7-5 11l-1 2-1 1-1 3-1 1c0 1 0 1-1 1v2c-1 1-1 2-1 3s-1 2-2 4l-1 1v1l-1 2h-1v3c0 2 0 2-1 4l-14 32c-1 3-1 4-2 7v1l1 1h0v3l-1 3c-2 2-1 5-3 8v2h0c0-2 1-3 2-4l-14 35-10 23v-3c2-5 3-11 5-17 0-8 4-18 6-26v-4c2-7 6-15 9-22 14-36 33-70 49-105z" class="N"></path><path d="M607 543l-2 7v1l1 1h0v3l-1 3c-2 2-1 5-3 8v2h0c0-2 1-3 2-4l-14 35-10 23v-3c2-5 3-11 5-17l9-27c2-3 3-7 4-11 1-3 2-5 4-8 1-4 3-9 5-13z" class="I"></path><path d="M605 551l1 1h0v3l-1 3c-2 2-1 5-3 8v2h0c0-2 1-3 2-4l-14 35-1-1 1-1c-1-3 7-24 9-28l6-18z" class="C"></path><defs><linearGradient id="e" x1="616.872" y1="566.878" x2="571.585" y2="543.175" xlink:href="#B"><stop offset="0" stop-color="#d7d0cf"></stop><stop offset="1" stop-color="#fdfcfb"></stop></linearGradient></defs><path fill="url(#e)" d="M658 413l1-1 1-1c1-3 4-6 5-9 2-2 3-5 5-7v-1c1-2 2-2 3-3 0 2-1 4-2 6-1 1-2 2-3 4l-5 10c-2 2-3 7-5 8-3 3-4 6-6 9-4 7-9 13-12 20l-11 18-1-1c-3 3-5 6-6 9l-6 13c-2 3-3 6-4 8-3 4-5 8-6 12-1 1-2 3-3 4l-14 31c0 2-1 4-2 5l-3 10c-4 7-6 15-9 23-1 3-3 6-3 10l-6 19c0 1-1 3-1 4l-4 17c-1 1-1 4-1 6 1 1 1 3 0 4v1 5h0v5 1c1 1 0 2 0 3 0 2-1 8 1 10 0 1 1 2 2 2l-5 11c0 3-2 5-3 7v1l6 20c-2-2-3-5-4-7-2-6-4-12-5-18 0-1 0-2-1-3l-2-10c-1-4-2-9-2-13l-3-38c0-2 1-4 1-6l3-12c2-3 4-8 5-12 0 0 1-2 1-3l4-10 33-69 6-11 6-9 17-28c5-7 9-15 14-22l3-4c1-3 3-5 4-7 3-1 2-1 4-4h2 0v1l1 1 1-2 2-3h0v2h1v-2c1 0 1-1 2-2h0 1l1-1c1-1 1-2 1-3h1 0v1 1z"></path><path d="M641 424c3-1 2-1 4-4h2c-1 2-1 4-3 6h0-1c-1 0-2 1-2 2-1 1-2 2-4 3 1-3 3-5 4-7z" class="B"></path><path d="M552 666l-1-3v-3-9c1-4 2-7 3-10 0 4-1 8-1 12 0 2-1 3-1 5v8z" class="G"></path><path d="M619 476l3-2-6 13c-2 3-3 6-4 8-3 4-5 8-6 12-1 1-2 3-3 4l-1-3 17-32z" class="B"></path><path d="M647 420h0v1l1 1 1-2 2-3h0v2h1v-2c1 0 1-1 2-2h0 1l1-1c1-1 1-2 1-3h1 0v1 1c-3 3-5 8-8 12 0 1-1 3-2 3-2 1-3 2-4 4 0 1-1 1-1 2h0-1l-7 9h0c1-4 5-7 6-11l3-6h0c2-2 2-4 3-6z" class="E"></path><path d="M634 435c0 2 0 2-1 4 0 1-1 1-1 3 0 3-4 7-6 9-3 3-5 8-8 12-5 7-10 15-14 23-1 3-5 6-7 8l6-9 17-28c5-7 9-15 14-22z" class="B"></path><path d="M643 434c0-1 1-1 1-2 1-2 2-3 4-4l-9 17c-1 2-3 4-4 6v2c-1 1-2 2-3 4-2 2-3 5-4 8-3 3-5 6-6 9l-3 2c2-6 6-10 9-16l3-6 3-3v-2-1c2-5 6-10 9-14z" class="C"></path><path d="M658 413l1-1 1-1c1-3 4-6 5-9 2-2 3-5 5-7v-1c1-2 2-2 3-3 0 2-1 4-2 6-1 1-2 2-3 4l-5 10c-2 2-3 7-5 8-3 3-4 6-6 9-4 7-9 13-12 20l-11 18-1-1c1-3 2-6 4-8 1-2 2-3 3-4v-2c1-2 3-4 4-6l9-17c1 0 2-2 2-3 3-4 5-9 8-12z" class="O"></path><path d="M558 620c-1-2 2-8 3-11 5-19 13-39 21-57l9-21c4-8 7-16 11-23l1 3-14 31c0 2-1 4-2 5l-3 10c-4 7-6 15-9 23-1 3-3 6-3 10l-6 19-2 2v1l-4 8v-5c-1 2-2 3-2 5h0z" class="G"></path><path d="M560 615c1-2 2-4 3-5 0-4 1-7 2-10l6-15 2-5c0-1 0-1 1-2 1-5 3-9 4-13 1-2 2-3 2-4 1-3 7-18 9-19 0 2-1 4-2 5l-3 10c-4 7-6 15-9 23-1 3-3 6-3 10l-6 19-2 2v1l-4 8v-5z" class="I"></path><path d="M560 620l4-8v-1l2-2c0 1-1 3-1 4l-4 17c-1 1-1 4-1 6 1 1 1 3 0 4v1 5h0v5 1c1 1 0 2 0 3 0 2-1 8 1 10 0 1 1 2 2 2l-5 11c0 3-2 5-3 7v1l6 20c-2-2-3-5-4-7-2-6-4-12-5-18 0-1 0-2-1-3l-2-10c-1-4-2-9-2-13 1 2 1 4 2 6v3s1 1 1 2v5c1 1 1 3 1 4l1 4 2 8v-2-3h-1v-5-4-1c-1-2 0 0-1-1v-5-8c0-2 1-3 1-5 0-4 1-8 1-12 0-7 2-14 4-21h0c0-2 1-3 2-5v5z" class="B"></path><path d="M560 620l4-8v-1l2-2c0 1-1 3-1 4l-4 17c-1 1-1 4-1 6l-4 16c-1 1-1 3-2 5-1-5 1-8 1-13 1-8 3-16 5-24z" class="C"></path><defs><linearGradient id="f" x1="552.639" y1="680.241" x2="558.667" y2="672.606" xlink:href="#B"><stop offset="0" stop-color="#70554f"></stop><stop offset="1" stop-color="#877367"></stop></linearGradient></defs><path fill="url(#f)" d="M560 636c1 1 1 3 0 4v1 5h0v5 1c1 1 0 2 0 3 0 2-1 8 1 10 0 1 1 2 2 2l-5 11c0 3-2 5-3 7v1c-2-10-1-19-1-29 1-2 1-4 2-5l4-16z"></path><path d="M560 636c1 1 1 3 0 4v1 5h0c-1 4-2 9-2 13v2c-1 2-1 4-1 6h0l-2-1c1-5 1-10 1-14l4-16z" class="F"></path><path d="M558 661v-2c0-4 1-9 2-13v5 1c1 1 0 2 0 3 0 2-1 8 1 10 0 1 1 2 2 2l-5 11c-2-3 0-13 0-17z" class="B"></path><path fill="#fff" d="M356 269l3 4 6 9 3 4 4 6v-1c-1-1-2-1-3-2 0-1-1-2-2-3 0-1-1-1-1-2v1l1 1c0 2 2 3 3 5 0 0 1 1 1 2 1 1 1 1 1 2l1 1c1 1 1 2 1 3 1 2 2 3 3 5 1 4 5 6 7 10 0 2 1 3 2 4l5 7c0 1 1 2 1 3 1 1 1 1 1 2l16 21c1 2 2 3 3 4l1 2 1 2 3 3 1 2 3 4c7 9 13 19 18 29l21 36c1 2 7 13 8 14 2 4 4 8 6 11 2 4 3 8 6 12h1v1c1 1 1 0 1 1 1 1 1 1 1 2h0c1 2 2 3 2 5l8 15c1 1 0 1 1 2s2 2 2 3l-2 6c0-1 1-1 1-2v2 5l-2 4c-1 4-2 7-3 11v1c-1 3 0 6 0 9 1 6 5 13 5 20-1-3-1-5-2-8l-5-13-1-3c-2-3-2-6-3-8l-7-18c-12-27-27-54-42-80l-26-42 1-1c-1-2-3-4-4-6-2-4-4-8-6-11s-3-6-4-8c-3-5-7-11-8-17-2-2-3-4-4-6l-12-20c-1-2-2-4-3-5 0-2-1-3-2-4-1-2-2-5-3-7-3-2-4-6-5-9 2 1 3 4 5 5h2c-3-5-9-11-13-16v-1l1-1v-3l1-1v-1l1-2z"></path><path d="M364 298c-3-2-4-6-5-9 2 1 3 4 5 5h2l18 23c-2-1-2-2-3-2-3-2-5-5-8-8-2-1-3-3-4-5-2-3-4-4-5-7h-1l1 2v1z" class="G"></path><defs><linearGradient id="g" x1="354.919" y1="280.276" x2="362.717" y2="283.83" xlink:href="#B"><stop offset="0" stop-color="#2d2827"></stop><stop offset="1" stop-color="#4c3f3b"></stop></linearGradient></defs><path fill="url(#g)" d="M356 269l3 4 6 9 3 4 4 6v-1c-1-1-2-1-3-2 0-1-1-2-2-3 0-1-1-1-1-2v1l1 1c0 2 2 3 3 5 0 0 1 1 1 2 1 1 1 1 1 2l1 1c1 1 1 2 1 3 1 2 2 3 3 5 1 4 5 6 7 10 0 2 1 3 2 4l5 7c0 1 1 2 1 3-3-2-6-8-8-11l-18-23c-3-5-9-11-13-16v-1l1-1v-3l1-1v-1l1-2z"></path><defs><linearGradient id="h" x1="355.198" y1="276.269" x2="364.802" y2="276.731" xlink:href="#B"><stop offset="0" stop-color="#b5a7a0"></stop><stop offset="1" stop-color="#d5ccce"></stop></linearGradient></defs><path fill="url(#h)" d="M356 269l3 4 6 9 3 4 4 6v-1c-1-1-2-1-3-2 0-1-1-2-2-3 0-1-1-1-1-2v1l1 1c0 2 2 3 3 5 0 0 1 1 1 2 1 1 1 1 1 2l1 1c1 1 1 2 1 3l-19-27v-1l1-2z"></path><path d="M364 297c2 2 3 4 4 6s3 3 4 5 2 3 2 5c1 1 2 3 3 4 1 4 5 8 7 12v1c1 4 5 7 6 11 1 3 3 5 4 7 5 7 10 14 14 21 2 6 7 11 10 17l7 12c1 2 2 5 4 6l3 6c2 2 4 5 6 8l6 12c0 1 1 2 2 3 0 3 1 4 2 6 1 1 2 2 3 4h0c0 1 1 2 1 2v1h1c1 3 3 6 4 9l8 15c2 3 5 7 7 11 0 1 1 3 1 4 2 3 3 5 5 8 0 1 2 2 2 3v1h-1l2 3c1 2 2 3 3 4h1-3l2 2v1c2 1 3 2 3 5l1 1v2c-2-1-6-8-7-10-5-9-9-18-14-28l-14-28-14-23-10-18-3-6c-2-2-4-5-5-8-3-3-5-7-7-10l-11-19c-1-2-3-4-5-7l-3-6c-1-2-3-4-4-5-1-3-1-5-3-7s-3-4-4-6l-12-20c-1-2-2-4-3-5 0-2-1-3-2-4-1-2-2-5-3-7v-1z" class="C"></path><path d="M388 340c2 2 2 4 3 7 1 1 3 3 4 5l3 6c2 3 4 5 5 7l11 19c2 3 4 7 7 10 1 3 3 6 5 8l3 6 10 18 14 23 14 28c5 10 9 19 14 28 1 2 5 9 7 10v1c1-1 1 0 1-1v-2h0v-2l3-6c0-1 1-2 1-4 0-3-2-7-3-10l-10-21h1v1c1 1 1 0 1 1 1 1 1 1 1 2h0c1 2 2 3 2 5l8 15c1 1 0 1 1 2s2 2 2 3l-2 6c0-1 1-1 1-2v2 5l-2 4c-1 4-2 7-3 11v1c-1 3 0 6 0 9 1 6 5 13 5 20-1-3-1-5-2-8l-5-13-1-3c-2-3-2-6-3-8l-7-18c-12-27-27-54-42-80l-26-42 1-1c-1-2-3-4-4-6-2-4-4-8-6-11s-3-6-4-8c-3-5-7-11-8-17z" class="I"></path><path d="M494 505c0-1 1-1 1-2v2 5l-2 4c-1 4-2 7-3 11v1c-1 3 0 6 0 9-2-6-2-12 0-17v-1-1l3-7 1-4z" class="C"></path><path fill="#fff" d="M687 350l10-14c1-2 2-5 4-6-1 4-2 8-5 12 1 0 1 1 1 2 0 2-1 4-3 7h0l-2 5c1 2 1 5-1 7l-1 1v1h0c-4 2-8 8-9 13h0c-1 2-2 4-4 5v1c0 2-2 5-4 7h0c-1 1-2 1-3 3v1c-2 2-3 5-5 7-1 3-4 6-5 9l-1 1-1 1v-1-1h0-1c0 1 0 2-1 3l-1 1h-1 0c-1 1-1 2-2 2v2h-1v-2h0l-2 3-1 2-1-1v-1h0-2c-2 3-1 3-4 4-1 2-3 4-4 7l-3 4c-5 7-9 15-14 22l-17 28-6 9-6 11-33 69-4 10c0 1-1 3-1 3-1 4-3 9-5 12l-3 12c0 2-1 4-1 6l3 38c0 4 1 9 2 13l2 10c1 1 1 2 1 3-1 0-1 0-1 1-3-6-4-14-6-21 0 2 0 2 1 4l-1 1 1 1c0 1 1 2 0 4h0l1 1v4 1l-2-2-3-9-2-1-4-9h0c-1-2 0-3-1-4-1-5-1-10-2-15l-1 1c-1-2-1-3-2-3-1-1-1-7-1-8 0-2-2-3-3-4v-2l2-1c-1-2-1-3 0-4 1-10 0-20 1-30l4-25c1-1 1-2 2-3l3-12c4-12 9-23 16-33 13-20 28-38 43-57l32-37c5-6 11-12 14-18l7-8c3-3 6-7 9-10 9-10 19-20 27-31l1-2z"></path><path d="M538 648c2 3 1 7 2 10v-9h1v6c1 1 1 2 1 3s0 2 1 3c1 2 1 4 2 6 1 1 1 2 1 4h0l1 1v4 1l-2-2-3-9c-2-6-4-11-4-18z" class="P"></path><path d="M529 586c1 3 1 3 1 6-1 4 0 8-1 12v9c0 2-1 4-1 6 1 1 1 1 2 1 0-2 0-2 1-4h-1v-3h1v-13c0-1-1-3 0-4v-4h1c1 3-1 8 1 9-1-1-1-2 0-4v-1h0c1 2 0 4 0 6v9 4l1 7h-1 0l-1 1 1 1h-1v1 5-1l-2-2h-1c0-2-2-3-3-4v-2l2-1c-1-2-1-3 0-4 1-10 0-20 1-30z" class="B"></path><path d="M532 624l-2-2 1-1 1-19h0c0 3 0 5 1 8v1 4l1 7h-1 0l-1 1 1 1h-1z" class="C"></path><path d="M541 622v1h0c2-2 1-4 3-6l3 38c0 4 1 9 2 13l2 10c1 1 1 2 1 3-1 0-1 0-1 1-3-6-4-14-6-21l-4-39z" class="H"></path><path d="M533 615c1 4 2 8 2 11 0 4 1 9 2 13 1 3 1 6 1 9 0 7 2 12 4 18l-2-1-4-9h0c-1-2 0-3-1-4-1-5-1-10-2-15l-1 1c-1-2-1-3-2-3-1-1-1-7-1-8h1l2 2v1-5-1h1l-1-1 1-1h0 1l-1-7z" class="E"></path><path d="M529 627h1c1 4 2 7 3 10l-1 1c-1-2-1-3-2-3-1-1-1-7-1-8z" class="N"></path><path d="M687 350l10-14c1-2 2-5 4-6-1 4-2 8-5 12 1 0 1 1 1 2 0 2-1 4-3 7h0l-2 5c1 2 1 5-1 7l-1 1v1h0c-4 2-8 8-9 13h0c-1 2-2 4-4 5v1c0 2-2 5-4 7h0c-1 1-2 1-3 3v1c-2 2-3 5-5 7-1 3-4 6-5 9l-1 1-1 1v-1-1h0-1c0 1 0 2-1 3l-1 1h-1 0c-1 1-1 2-2 2v2h-1v-2h0l-2 3-1 2-1-1v-1h0-2c-2 3-1 3-4 4-1 2-3 4-4 7l-3 4c-5 7-9 15-14 22h0-2v-2l1-1c-3 3-6 8-9 9-2 3-3 4-4 6-1 3-4 4-5 6-2 4-5 6-7 10-2 1-2 2-2 4l-4 4c-3 3-5 6-8 9 0-1 1-2 1-3l2-2 1-3-1-1c-11 15-20 29-28 46-1 2-3 5-3 7-1 2-2 4-3 5-2 5-2 11-5 16v-4l1-1c0-1-1-3 0-4h1v-2c1-1 1-1 1-2s0-1 1-1v-1c0-1 1-3 1-4 1-2 2-3 2-4s0-2 1-2l1-2 1-2c0-1 0-2 1-3h0c0-1 1-2 1-3l1-1 2-4 1-2c0-1 1-2 1-2l2-3c1-2 2-3 3-5 0-2 1-2 2-3s0-2 1-3c2-3 4-5 5-7 2-3 4-5 5-7l3-3 69-90 19-26-1-1a30.44 30.44 0 0 1-8 8h0c-2 1-5 4-6 6s-3 3-5 4c3-3 6-7 9-10 9-10 19-20 27-31l1-2z" class="E"></path><path d="M669 381c0-1 2-4 3-5l12-17c1 1 1 2 1 2-1 2-2 4-3 5l-7 10h0l-1 2h-1c-2 1-3 4-4 5h0v-2z" class="F"></path><path d="M684 359l12-17c1 0 1 1 1 2 0 2-1 4-3 7h0-1c-1 1-3 5-4 6l-13 19h-1l7-10c1-1 2-3 3-5 0 0 0-1-1-2z" class="J"></path><path d="M669 381v2h0c1-1 2-4 4-5h1l1-2h0 1l-8 12c-3 4-6 9-10 13v1c-1 2-5 4-6 6l-1 1-1-1c1-3 5-7 7-10l12-17z" class="Q"></path><defs><linearGradient id="i" x1="639.81" y1="417.891" x2="641.19" y2="423.609" xlink:href="#B"><stop offset="0" stop-color="#715850"></stop><stop offset="1" stop-color="#846e60"></stop></linearGradient></defs><path fill="url(#i)" d="M650 408l1 1c-3 5-7 10-10 15-1 2-3 4-4 7l-3 4c-5 7-9 15-14 22h0-2v-2l1-1c-3 3-6 8-9 9l16-21h-1l1-1v-1c1 0 1-1 2-2h1v-1c-1 0-1 0-2 1h0c2-5 6-9 9-12 0-1 12-17 14-18z"></path><path d="M636 426s1 0 1 1-1 3-2 4l-9 11h-1l1-1v-1c1 0 1-1 2-2h1v-1c-1 0-1 0-2 1h0c2-5 6-9 9-12z" class="Q"></path><defs><linearGradient id="j" x1="601.813" y1="460.307" x2="610.187" y2="469.693" xlink:href="#B"><stop offset="0" stop-color="#7e675e"></stop><stop offset="1" stop-color="#9b837a"></stop></linearGradient></defs><path fill="url(#j)" d="M627 438h0c1-1 1-1 2-1v1h-1c-1 1-1 2-2 2v1l-1 1h1l-16 21c-2 3-3 4-4 6-1 3-4 4-5 6-2 4-5 6-7 10-2 1-2 2-2 4l-4 4c-3 3-5 6-8 9 0-1 1-2 1-3l2-2 1-3-1-1c1-3 4-6 6-8l13-16c8-10 16-21 25-31z"></path><path d="M676 376l13-19c1-1 3-5 4-6h1l-2 5c1 2 1 5-1 7l-1 1v1h0c-4 2-8 8-9 13h0c-1 2-2 4-4 5v1c0 2-2 5-4 7h0c-1 1-2 1-3 3v1c-2 2-3 5-5 7-1 3-4 6-5 9l-1 1-1 1v-1-1h0-1c0 1 0 2-1 3l-1 1h-1 0c-1 1-1 2-2 2v2h-1v-2h0l-2 3-1 2-1-1v-1h0-2c-2 3-1 3-4 4 3-5 7-10 10-15l1-1c1-2 5-4 6-6v-1c4-4 7-9 10-13l8-12z" class="G"></path><path d="M692 356c1 2 1 5-1 7l-1 1v1h0c-4 2-8 8-9 13h0c-1 2-2 4-4 5v1c0 2-2 5-4 7h0c-1 1-2 1-3 3v1c-2 2-3 5-5 7-1 3-4 6-5 9l-1 1-1 1v-1l34-56z" class="R"></path><defs><linearGradient id="k" x1="582.977" y1="524.508" x2="572.616" y2="519.195" xlink:href="#B"><stop offset="0" stop-color="#d1c6c3"></stop><stop offset="1" stop-color="#eeedec"></stop></linearGradient></defs><path fill="url(#k)" d="M610 463c3-1 6-6 9-9l-1 1v2h2 0l-17 28-6 9-6 11-33 69-4 10c0 1-1 3-1 3-1 4-3 9-5 12l-3 12c0 2-1 4-1 6-2 2-1 4-3 6h0v-1l-1-18c0-12 2-24 4-37h0c3-5 3-11 5-16 1-1 2-3 3-5 0-2 2-5 3-7 8-17 17-31 28-46l1 1-1 3-2 2c0 1-1 2-1 3 3-3 5-6 8-9l4-4c0-2 0-3 2-4 2-4 5-6 7-10 1-2 4-3 5-6 1-2 2-3 4-6z"></path><path d="M560 537l5-9c2-2 2-4 4-6l10-15c1-1 1-3 3-4 0-1 1-1 1-2-1 3-3 6-4 8s-2 4-2 5c-1 1-2 3-3 4s-1 2-2 3-2 2-2 3l-3 7-1 1c0 1 0 0-1 1-1 4-3 8-5 11-1 2-2 3-2 5-2 3-3 5-4 7v2c-1 1-1 3-2 5v-1c0 1-1 1-1 2 0 2-1 4-1 6-1 2-1 4-2 6h0v-3c0-2 1-3 1-5l1-4c1-3 2-7 3-10 2-6 4-11 7-17z" class="B"></path><path d="M558 574h-1v1 1l-1 1v-1-3h0c-1-2 1-3 1-5 1 0 0 0 1-1s1-1 1-2v-1c1-1 2-3 2-4v-1l1-2 1-1v-2h1c0-2 1-2 1-3l3-5c0-2 1-3 1-6 3-2 4-5 5-9l2-2v-1l1-1c0-1 0-1 1-2h0l2-4c1-1 1-2 1-2l1-1 1-2 1-2c1-2 2-3 3-5 0-1 0-2 1-3 1 0 1-1 2-2h0l1 1h0l-33 69z" class="C"></path><path d="M583 493l1 1-1 3-2 2c0 1-1 2-1 3 3-3 5-6 8-9 0 3-1 4-3 6l-2 2c0 1-1 1-1 2-2 1-2 3-3 4l-10 15c-2 2-2 4-4 6l-5 9c-3 6-5 11-7 17-1 3-2 7-3 10l-1 4c0 2-1 3-1 5v3l-2 8c0 1 0 2-1 3v7h0v-1c1-1 1-2 1-3v1h0c0 2 0 2 1 4h0v-1l2-2 1-2s1-1 1-2l1-1h1c-1 4-3 9-5 12l-3 12c0 2-1 4-1 6-2 2-1 4-3 6h0v-1l-1-18c0-12 2-24 4-37h0c3-5 3-11 5-16 1-1 2-3 3-5 0-2 2-5 3-7 8-17 17-31 28-46z" class="H"></path><path d="M543 612l5-13h0l-3 12c0 2-1 4-1 6-2 2-1 4-3 6h0v-1l-1-18 2-3c0 3-1 12 1 14v-3h0z" class="Q"></path><path d="M557 540h0c1-2 2-2 3-3-3 6-5 11-7 17-1 3-2 7-3 10l-1 4c-1 1-1 2-2 3l-1 1c1-10 6-23 11-32z" class="C"></path><path d="M580 502c3-3 5-6 8-9 0 3-1 4-3 6l-2 2c0 1-1 1-1 2-2 1-2 3-3 4l-10 15c-2 2-2 4-4 6l-5 9c-1 1-2 1-3 3h0c5-13 14-27 23-38z" class="M"></path><path d="M546 572l1-1c1-1 1-2 2-3 0 2-1 3-1 5v3l-2 8c0 1 0 2-1 3v7h0v-1c1-1 1-2 1-3v1h0c0 2 0 2 1 4h0v-1l2-2 1-2s1-1 1-2l1-1h1c-1 4-3 9-5 12h0l-5 13v-1c-1-1-1-5 0-8 0-10 1-21 3-31z" class="I"></path><defs><linearGradient id="l" x1="581.498" y1="416.267" x2="623.297" y2="442.925" xlink:href="#B"><stop offset="0" stop-color="#d2c6c2"></stop><stop offset="1" stop-color="#fbfbfb"></stop></linearGradient></defs><path fill="url(#l)" d="M704 267c2 4 3 9 4 14l1 4v8c0 8-1 14-3 22v1 2c0 4-3 8-5 12h0c-2 1-3 4-4 6l-10 14-1 2c-8 11-18 21-27 31-3 3-6 7-9 10l-7 8c-3 6-9 12-14 18l-32 37c-15 19-30 37-43 57-7 10-12 21-16 33l-3 12c-1 1-1 2-2 3-2 3-3 12-3 16l-2 13c-2-5-4-11-4-16l-1-1c-1-2-2-5-2-8-3-11-8-21-11-31-1-3 0-5-1-7s-1-4 0-7c0-2 1-4 1-6 1-2 2-5 3-7 1-1 2-3 4-3l1 2 1-1c0-2 1-3 2-5 2-1 3-2 5-2l1 1h1l3-4 1-1 1-1c3-3 5-6 7-9 5-6 10-14 16-19l8-8 3-3 2-3c2-1 4-4 6-5 1-1 3-1 4-2 2 0 3-2 5-3l-2-1h-3c-2-1-1-3-2-5-1 0-2-1-2-2-1-1-1-4 0-6 3-11 17-22 27-27 2-3 5-3 7-6 1-4 2-7 4-9 5-5 10-10 15-14l20-18c8-7 14-15 22-23l22-26c2-2 3-4 5-6l4-6 5-7v-6c-1-2-2-2-3-3v-1l2-1-1-1c0-2-1-4-2-6h2z"></path><path d="M523 500h2l1 1c0 2-1 4-2 6 0 1-1 2-2 4h0c-1-2-1-3-1-5 0-3 0-4 2-6z" class="E"></path><path d="M594 437l-2 4s0 1-1 1c0 3-3 5-4 7l-2 2c-1 1-1 1-2 1h0c-1 2-3 3-4 4v-1c1-1 2-2 3-4h0c-4 1-5 5-9 5 1-1 2-2 3-2 2-1 3-2 4-4 2-1 3-1 5-4 1-2 3-4 5-6 1-1 2-2 4-3z" class="C"></path><path d="M624 404c0 1-1 2-2 4v1c-1 1-2 3-3 4l-2 2c-1 1 0 0-1 2-1 1-4 2-5 5h-1c-2 1-3 3-4 5 0 1-1 1-2 1 0 1 0 1-1 0v-1c3-2 5-6 7-9 2-1 4-3 5-5l9-9z" class="E"></path><path d="M583 446h2c-2 3-3 3-5 4-1 2-2 3-4 4-1 0-2 1-3 2l-5 4c-1 1-6 5-7 5 0-1 0 0 1-1l1-1c0-1 1-2 2-3h0 0-3c-2 1-3 5-6 5l8-8 3-3h1c2 1 4 0 5-1 2-1 4-3 6-4s3-2 4-3zm116-133c1 0 1 1 2 1h0 2c0 5-3 11-5 15-3 3-5 7-8 10l2-4c1-4 3-7 4-10 0-1 0-1 1-2v1c-1 3-2 6-4 9h1v-1l1-1c1-2 3-4 3-6 1-1 1-1 1-2l1-1v-1c1-1 1-1 1-2l-1 1-2 2c-1 1-2 1-4 3h-1v-1l-1 1v-3l5-6 2-3z" class="M"></path><path d="M699 313c1 0 1 1 2 1-2 4-5 7-8 11v-1l-1 1v-3l5-6 2-3z" class="Q"></path><path d="M692 322v3l1-1v1h1l-5 8h0c-2 3-4 5-6 7-1 2-1 3-2 4-3 3-7 6-9 10-1 2-3 3-5 5l-8 9c-2 2-4 3-6 5l-2 1c9-11 20-20 28-32h-1c3-4 7-7 8-12l6-8z" class="E"></path><path d="M692 322v3l1-1v1l-14 17h-1c3-4 7-7 8-12l6-8z" class="H"></path><path d="M596 428c4-1 5-3 7-6 2-1 4-2 5-4v-1c1-1 1-1 2-1 1 1 1 1 0 2-2 3-4 7-7 9-2 1-2 1-3 2-2 2-5 6-6 8-2 1-3 2-4 3-2 2-4 4-5 6h-2c-1 1-2 2-4 3s-4 3-6 4c-1 1-3 2-5 1h-1l2-3c2-1 4-4 6-5 1-1 3-1 4-2 2 0 3-2 5-3l1-1 1-2 10-10z" class="O"></path><path d="M585 440c1-1 4-1 5-3-1 3-5 7-7 9-1 1-2 2-4 3s-4 3-6 4c-1 1-3 2-5 1h-1l2-3c2-1 4-4 6-5 1-1 3-1 4-2 2 0 3-2 5-3l1-1z" class="H"></path><path d="M661 374c1-2 6-6 8-8l1-2c1-1 1-2 2-3h-1c-1 2-4 4-6 6h0c2-3 4-6 7-8 4-6 9-11 12-17 1-1 1-1 1-2l3-3c0-1 1-1 2-2v-1c0-1 1-1 1-2 1-1 2-2 2-3l1-2c0-1 1-1 2-2-1 3-3 6-4 10l-2 4-11 16 1 1 1-1h1c0-2 1-2 2-3h0l1-1 1 1c-8 11-18 21-27 31-3 3-6 7-9 10l-7 8c0-1 1-2 2-3 0-1 0-2 1-4h1c0-2 3-3 4-5v-1l4-4v-2c3-2 5-5 6-8z" class="G"></path><path d="M679 355l1 1 1-1h1c0-2 1-2 2-3h0l1-1 1 1c-8 11-18 21-27 31-3 3-6 7-9 10l-7 8c0-1 1-2 2-3 4-4 8-10 12-15 2-2 8-7 8-11 1-2 4-4 5-6 3-4 6-8 9-11z" class="O"></path><path d="M597 456h0v-1-1l1-1h-1c0-3 3-4 4-6 2-2 4-5 6-7 1-1 1-2 2-2l16-20 3-3c0-2 1-3 2-4 1-2 2-3 4-5l2-2c1-2 2-3 4-5l2-2c0-1 2-2 2-4-1 1-1 1-2 1l4-3c0-1 1-1 2-1 1-1 0-1 1-2s1-1 1-2c1-2 7-6 8-8s2-3 3-4c-1 3-3 6-6 8v2l-4 4v1c-1 2-4 3-4 5h-1c-1 2-1 3-1 4-1 1-2 2-2 3-3 6-9 12-14 18l-32 37z" class="E"></path><path d="M686 330c-1 5-5 8-8 12h1c-8 12-19 21-28 32l-27 30-9 9c-1 2-3 4-5 5 1-1 1-1 0-2-1 0-1 0-2 1v1c-1 2-3 3-5 4-2 3-3 5-7 6l53-56c1 0 2-1 3-2 2-4 6-6 8-9 2-2 2-4 3-6 3-2 7-5 9-9 2-5 8-9 11-14l3-2z" class="N"></path><path d="M704 267c2 4 3 9 4 14l1 4v8c0 8-1 14-3 22v1 2c0 4-3 8-5 12h0c-2 1-3 4-4 6l-10 14-1 2-1-1-1 1h0c-1 1-2 1-2 3h-1l-1 1-1-1 11-16c3-3 5-7 8-10 2-4 5-10 5-15h-2c1-2 4-6 5-6l1-2-1-1-1 1h-1l2-4v-2c-1-1-1-2-2-2h-1-2l-3 3c-1-1-1-2-1-3l4-6 5-7v-6c-1-2-2-2-3-3v-1l2-1-1-1c0-2-1-4-2-6h2z" class="R"></path><path d="M701 292l5-7c1 5 1 11 0 17v-2c-1-1-1-2-2-2h-1-2l-3 3c-1-1-1-2-1-3l4-6z" class="J"></path><path d="M701 292v1c1 1 1 2 0 3h1l2-2h1v1c0 1 0 2 1 3v2c-1-1-1-2-2-2h-1-2l-3 3c-1-1-1-2-1-3l4-6z" class="F"></path><path d="M701 314c1-2 4-6 5-6 0 5-1 10-3 15s-6 11-10 16l-5 7c0 1-1 2-1 3v1l-1 2-1-1-1 1h0c-1 1-2 1-2 3h-1l-1 1-1-1 11-16c3-3 5-7 8-10 2-4 5-10 5-15h-2z" class="L"></path><path d="M523 573v-6l-1-1-1-4v-1l-1-1v-4h-1c0-2 0-3-1-4l-1-4-1-2c-2-8-7-16-7-24l2-2h0c0-1 1-2 1-3 1-3 3-4 5-7v-2l1 1c2 3 4 8 5 12 2 6 5 13 6 20l1 2v2c1 1 1 2 1 3l1 6v5h1s1-1 2-1c-1 1-1 2-2 3-2 3-3 12-3 16l-2 13c-2-5-4-11-4-16l-1-1z" class="B"></path><path d="M698 301l3-3h2 1c1 0 1 1 2 2v2l-2 4h1l1-1 1 1-1 2c-1 0-4 4-5 6h0c-1 0-1-1-2-1l-2 3-5 6-6 8-3 2c-3 5-9 9-11 14-2 4-6 7-9 9-1 2-1 4-3 6-2 3-6 5-8 9-1 1-2 2-3 2l-53 56-10 10-1 2-1 1-2-1h-3c-2-1-1-3-2-5-1 0-2-1-2-2-1-1-1-4 0-6 3-11 17-22 27-27 2-3 5-3 7-6 1-4 2-7 4-9 5-5 10-10 15-14l20-18c8-7 14-15 22-23l22-26c2-2 3-4 5-6 0 1 0 2 1 3z" class="P"></path><path d="M582 430l1-3 2-2 1 1c-1 2-2 4-2 6l-2 2 1-3-1-1zm115-120l1 1-3 4 2 1-5 6-6 8-3 2c1-1 1-2 2-3l2-2 1-1 1-1v-1c2-2 3-4 5-6h0 0-3l1-1c1-1 2-2 2-3 1-1 2-3 3-4z" class="B"></path><path d="M666 343v-1l4-4 6-6c1 0 2-1 3-1 1-1 2-3 4-4-1 2-2 3-3 5-1 1-1 2-3 4-1 1-3 3-5 4-1-1-1-1-2-1-1 1-3 2-3 4h-1zm-16 12c1-1 2-1 4-2 1-1 1-1 3-2-2 5-8 8-12 11-1 1-2 3-3 4h0v-2l-19 19c-1 1-3 2-4 4-1 1-1 1-3 1 1 1 1 1 1 2l-1 1c0-1 0-1-2-1v-1c3-4 6-7 10-10 2-3 5-6 8-9 2-1 4-2 6-4l12-11z" class="E"></path><path d="M694 314c0 1-1 2-2 3l-1 1c0 2 0 3-1 5h-1c-1 2-2 3-4 5l-5 5c1-1 2-3 3-4l1-1v-1h-1c-2 1-3 3-4 4-1 0-2 1-3 1l-6 6-4 4v1c-2 3-5 5-8 8h-1c-2 1-2 1-3 2-2 1-3 1-4 2 1-2 3-4 5-5 2-2 4-5 7-6v-1c2-1 4-4 5-6 3-3 7-5 9-8 2-2 4-5 6-7 1-1 2-2 3-2v1h1l1-1c2-2 5-4 7-6z" class="G"></path><path d="M670 330c2 1 4-1 6-1-2 3-6 5-9 8-1 2-3 5-5 6v1c-3 1-5 4-7 6-2 1-4 3-5 5l-12 11c-2 2-4 3-6 4-3 3-6 6-8 9-4 3-7 6-10 10v1c2 0 2 0 2 1-2 1-4 2-7 3 1-4 2-7 4-9 5-5 10-10 15-14l20-18c8-7 14-15 22-23z" class="I"></path><defs><linearGradient id="m" x1="689.272" y1="298.166" x2="680.413" y2="330.657" xlink:href="#B"><stop offset="0" stop-color="#948179"></stop><stop offset="1" stop-color="#ccbab3"></stop></linearGradient></defs><path fill="url(#m)" d="M698 301l3-3h2 1c1 0 1 1 2 2v2l-2 4h1l1-1 1 1-1 2c-1 0-4 4-5 6h0c-1 0-1-1-2-1l-2 3-2-1 3-4-1-1c-1 1-2 3-3 4-2 2-5 4-7 6l-1 1h-1v-1c-1 0-2 1-3 2-2 2-4 5-6 7-2 0-4 2-6 1l22-26c2-2 3-4 5-6 0 1 0 2 1 3z"></path><path d="M698 301l3-3h2 1c1 0 1 1 2 2v2l-2 4h-1-1c0 1 0 1-1 1 0-1 1-2 2-4h1c1-1 0-1 1-2l-1-1c-1 1-2 1-3 2h-1v-1h-2z" class="H"></path><path d="M693 310c-1 3-3 6-5 9-1 0-1 0-1 1l-1 1h-1v-1c-1 0-2 1-3 2 2-5 7-9 11-12z" class="B"></path><defs><linearGradient id="n" x1="699.25" y1="304.23" x2="691.796" y2="312.355" xlink:href="#B"><stop offset="0" stop-color="#9f8f89"></stop><stop offset="1" stop-color="#baada8"></stop></linearGradient></defs><path fill="url(#n)" d="M693 310c0-2 4-7 6-8h1v1c1 0 1-1 2-1l1-1 1 2h-1c-1 2-2 3-2 4 1 0 1 0 1-1h1 1 1l1-1 1 1-1 2c-1 0-4 4-5 6h0c-1 0-1-1-2-1l-2 3-2-1 3-4-1-1c-1 1-2 3-3 4-2 2-5 4-7 6 0-1 0-1 1-1 2-3 4-6 5-9z"></path><path d="M697 310v-1c1 0 1-1 1-2l4-4h1c-1 2-2 3-2 4 1 0 1 0 1-1h1 1 1l1-1 1 1-1 2c-1 0-4 4-5 6h0c-1 0-1-1-2-1l-2 3-2-1 3-4-1-1z" class="I"></path><path d="M704 306h1l1-1 1 1-1 2c-1 0-4 4-5 6h0c-1 0-1-1-2-1l4-7h1z" class="D"></path><path d="M608 398c1-1 2-2 4-2h0c1 1 1 1 0 2l-1 2-1 1v1c-1 1-1 2-1 3l-1 1c-3 1-3 0-5 2h-2c-1 0-3 2-4 3-3 2-6 4-9 7l-6 7-1 2c0 1 0 1 1 3l1 1-1 3-1 1 3 3h2l-1 2-1 1-2-1h-3c-2-1-1-3-2-5-1 0-2-1-2-2-1-1-1-4 0-6 3-11 17-22 27-27l6-2z" class="E"></path><path d="M608 398l1 1c0 1-1 2-2 3h-1c-9 2-15 9-21 15-3 2-5 6-7 10v6l1 1v1l3 3v2h-3c-2-1-1-3-2-5-1 0-2-1-2-2-1-1-1-4 0-6 3-11 17-22 27-27l6-2z" class="M"></path></svg>