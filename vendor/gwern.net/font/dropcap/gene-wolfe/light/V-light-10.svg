<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="108 78 832 872"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#191617}.C{fill:#170e0e}.D{fill:#151414}.E{fill:#551a1a}.F{fill:#271110}.G{fill:#bc2729}.H{fill:#2c2b2b}.I{fill:#0d0c0c}.J{fill:#cd1719}.K{fill:#d22426}.L{fill:#d82b28}.M{fill:#a52728}.N{fill:#8a1719}.O{fill:#fff}.P{fill:#551213}.Q{fill:#391616}.R{fill:#aa2024}.S{fill:#691319}.T{fill:#4f191a}.U{fill:#4f4e4f}.V{fill:#383738}.W{fill:#e14f50}.X{fill:#db474a}.Y{fill:#bb3336}.Z{fill:#4b4a4b}.a{fill:#6e6e6f}.b{fill:#db6062}.c{fill:#faf9fa}.d{fill:#7c3f3d}.e{fill:#8a898a}.f{fill:#e46e6e}.g{fill:#9f9e9f}.h{fill:#e28482}.i{fill:#462625}.j{fill:#bcbbbc}</style><path d="M628 272c-1 0-2 0-3-1 2-1 5-1 8-1l16-2v3c-5 0-11-1-16 0-2 0-3 0-5 1z" class="B"></path><path d="M286 378c2 5 2 9-1 14-1 1-1 2-2 2h0 0c-1-1-2-1-3-1 4-5 6-9 6-15z" class="H"></path><path d="M621 142h29c-6 3-10 1-16 2l-11 1-2-3z" class="K"></path><path d="M444 297c4-4 5-7 6-12l2 10c-1 2-1 4-3 5h-1c-2-2-2-2-4-2v-1z" class="E"></path><path d="M474 142l35-1-8 3c-2-1-5 0-7 0h-20c2-2 4 0 7-1-2 0-4 0-6-1h-1z" class="R"></path><path d="M361 143l36-1v2h-8 0l-23 1c-2-1-3-1-5-1v-1z" class="K"></path><path d="M432 290l1-1c4-5 5-10 6-16 1 6 2 13-1 19h0c-2 0-3 1-5 1-1 0-1-1-2-1h-1c0-1 1-2 2-2z" class="P"></path><path d="M750 396h2l1-1c-1 0-2 0-3-1-2 0-5-2-7-4-1-3 0-8 0-11 2 4 3 12 8 14l2 1c1 1 1 0 1 1 0 4 3 7 5 9h-1c-1 0-1-1-2-2h-1c1 2 1 2 3 3 1 1 1 3 2 5h0c-2-2-3-2-5-2-2-3-4-9-5-12z" class="V"></path><path d="M282 378v-2c0-2 0-2 1-3 1 1 2 2 2 3l1 1v1c0 6-2 10-6 15-2 1-4 2-5 4l-2-2c1-1 1-1 2-1l-1-1 1-1c4-1 6-10 7-14z" class="c"></path><path d="M282 378c0 5 0 10-3 14-1 1-2 1-4 2l-1-1 1-1c4-1 6-10 7-14z" class="I"></path><path d="M688 586h0c-2-1-3-2-4-3v-6c1 1 2 3 2 3 2 1 5 1 7 1 6 0 11 4 14 9 4 6 6 13 4 20 0-1 0-3-1-4 0-1 1-4 1-5l-1-2c0-1 0-1-1-2v-1-1l-1-1v-1l-1-1c0-1-1-1-1-3l-1-1-3-2h0v1c1 1 1 1 1 2v1l-1-2-3-3h-2-8l-1 1z" class="H"></path><path d="M680 142l73 1c-3 1-5 0-8 1-6 1-12 1-18 1h-40-1l-6-2h0v-1z" class="K"></path><path d="M647 348c-1-4-1-8-3-11-2-9-8-11-15-14h8l-1 1c3 0 6 1 8 1 1 0 2 0 3 1 2 0 2 0 3 1v11c-1 3 0 6-1 9-1 0-1 0-2 1z" class="B"></path><path d="M633 351c3 1 6 2 9 5 1 3 3 6 4 9v5h1 2c1 2 0 2 0 4-1 2-3 3-5 5-1 2-2 3-4 3 1-6 1-13 0-19-1-5-3-9-7-12zM462 184c4 6 7 15 5 22-1 7-6 14-12 18-3 2-7 4-11 4l1-2c1 0 2 0 3-1l3-1v-1c2-1 1 0 2-1 2-2 4-3 4-6v-1l2-2 3-6v-1-2c1-1 0-3 1-4l1-1v-5c-1-1-1-1-1-2l-1-1v-4h1c-1-1-1-2-1-3z" class="I"></path><path d="M593 227l-4-1c-6-3-11-9-13-15-3-7-2-15 1-22 2-7 9-17 16-20v2h0c-2 2-6 5-7 7-1 1-2 3-3 4l-3 7c-1 1-1 1-1 2v1l-1 4c-1 2 0 4 0 6 1 2 0 2 1 4v2c1 1 1 1 1 2 1 2 2 3 2 5 1 1 1 0 1 1 1 1 1 1 1 2v2c2 3 6 5 9 6v1z" class="D"></path><path d="M246 142h80 24c3 0 7 0 11 1v1c2 0 3 0 5 1h-77l-2-1h-12-27l-2-2z" class="L"></path><path d="M654 268h1c3 3 4 5 5 8l5 10c0 2 0 2-1 3h-1l-1 1c-2 2-3 3-5 4h2v1l1 2h0-4l3 2v1c-5-3-9-4-14-6h-1 0l-14-6 20 2-3-7c-2-6-6-7-11-9l-8-2c2-1 3-1 5-1 5-1 11 0 16 0l5-3z" class="B"></path><path d="M659 285l1 1h0c1 2 1 3 2 4-2 2-3 3-5 4h2v1l1 2h0-4l3 2v1c-5-3-9-4-14-6h-1l-2-2c4-2 9-1 13-2 2 0 3 0 4-1v-4z" class="S"></path><path d="M656 297c-2-1-4-2-5-3s-1 0-1-1l2-1c1 0 2 1 3 1l2 1h2v1l1 2h0-4z" class="F"></path><path d="M655 293l2 1h2v1c0 1-1 1-1 1-2 0-3 0-4-2l1-1z" class="C"></path><path d="M654 268h1c3 3 4 5 5 8l5 10c0 2 0 2-1 3h-1l-1 1c-1-1-1-2-2-4h0l-1-1c-1-1-1-3-2-4 0-1-1-2-1-3-2-1-3-3-5-4h-1-1c-2-1-11-2-13 0l-8-2c2-1 3-1 5-1 5-1 11 0 16 0l5-3z" class="G"></path><path d="M654 268h1c3 3 4 5 5 8h-2c-2-1-3-1-4-2h-1c-2-1-4-1-5-2 3 0 5-1 7-3l-1-1z" class="T"></path><path d="M653 274h1c1 1 2 1 4 2h2l5 10c0 2 0 2-1 3h-1l-1 1c-1-1-1-2-2-4h0l-1-1c-1-1-1-3-2-4 0-1-1-2-1-3 0-2-2-3-3-4z" class="E"></path><path d="M649 322l2-1c3 2 6 12 7 15 2 8 2 16 3 24 0 2-1 5-3 7-1 1-3 1-4 1l-1 2h0-7v-5c-1-3-3-6-4-9-3-3-6-4-9-5l-3-1 1-1c2 0 4 0 6 1 4 1 7 5 10 9v-11c1-1 1-1 2-1 1-3 0-6 1-9v-11c-1-1-1-1-3-1-1-1-2-1-3-1-2 0-5-1-8-1l1-1c4 0 8 1 12 0v-1z" class="T"></path><path d="M316 605c-1-9 5-21 14-24 3 0 8 0 10-1 1-1 1-2 2-2v-1c1 2 1 3 0 5s-3 3-4 4c-2 2-2 6-5 8-1-2-1-3-2-4-1 0-1 1-1 1-3 1-4 3-5 5-1 4 0 9 2 12 3 6 10 10 16 11 7 2 14 1 19-3 7-4 13-11 14-18 0-1 1-5 1-6l-4-4v-1c2 0 2 0 4-1v-1c0-6-7-12-11-17 5 2 8 4 12 7 0 1 0 1 1 2v3l1 2v3h1 1l-1-3h1c0 1 1 1 1 3v1l1 1c0 1 1 2 1 3v1s1 1 1 2c0 5 0 10-1 15-3 6-5 10-9 15l-3 3v1c-6 4-15 10-23 8h0 0c-4 0-6-1-10-1-1-1-2-1-3-2h0c-1-1-2-1-2-2l-1-1c-3-1-7-5-9-7l1-1h1 0l-1-1-1-1v-1c0-1-1-1-1-2l-3-6c-1-1 0-1-1-2-1-2-2-3-1-5 0-2 0-1-1-2v2l-1-1c-1 1-1 1-1 2v1z" class="D"></path><path d="M350 635h0v-1c-2 0-3 0-5-1h0c5 0 11 0 15-2 3-1 6-2 8-3-1 1-4 2-6 3v1c1 0 2-1 3-1 1-1 2-1 3-2h1l1-1c1-1 2-2 3-2v1c-6 4-15 10-23 8h0 0z" class="V"></path><path d="M380 585h1c-1 1-1 2 0 3 1 4 1 9 0 14-1 7-6 13-12 18-5 3-15 7-21 5-1 0-3 0-4-1h1l1 1c6 0 14-2 19-5 7-5 13-12 14-20l1-1c1-4 0-9 0-14z" class="j"></path><path d="M280 479c0-1 0-2 1-2 2-4 6-8 10-9h1c5-1 10-1 15 0 2 1 5 1 7 3v1l3 1v3l-1 1-3-1c-6-1-11-1-16 1l-6 3c2 0 3 1 5 0h1 2c1-1 2-1 3-1h4v1h-2v1c4 4 6 7 5 12 0 6-2 12-7 16-1 1-2 2-4 3v2c1 2 2 3 4 4h5c0 1 0 2-1 3l1 2c2 0 3 1 5 1h4v1l-1 1 1 1v1h0c5 2 11 0 15-1l-9 6c-7 1-16 0-22-4-7-4-12-10-17-16-4-5-8-10-9-16-2-6-1-13 3-18v1c2 0 2 0 3-1z" class="D"></path><path d="M291 480c-4 2-6 4-8 8 2-5 3-9 8-12l1 1h0 5l-6 3z" class="e"></path><path d="M316 527h-1l-1-1h-1c-2 1-8 0-10-1s-4-3-6-4l1-1c2 2 4 3 7 4 1 0 1 0 2-1 2 0 3 1 5 1h4v1l-1 1 1 1z" class="B"></path><path d="M307 468c2 1 5 1 7 3v1h-4c-7-1-12 0-19 3h1c1-3 4-3 7-3l1-1c2-1 2 0 4-1-2-1-4-1-6 0-3 1-6 3-9 3h0c6-4 11-4 18-5z" class="H"></path><path d="M280 479c0-1 0-2 1-2 2-4 6-8 10-9h1c5-1 10-1 15 0-7 1-12 1-18 5v1c-5 3-8 7-9 12l-1 1c0-3 2-6 1-8z" class="a"></path><path d="M291 475c7-3 12-4 19-3h4l3 1v3l-1 1-3-1c-6-1-11-1-16 1h-5 0l-1-1v-1z" class="j"></path><path d="M310 472h4l3 1v3l-1 1-3-1 1-1c-1-2-3-2-4-3z" class="g"></path><path d="M295 484c1 0 2-1 3 0 3 1 5 5 6 8 0 3-1 6-3 9-1 2-2 3-5 3-2 0-3 0-5-1-2 0-3-2-4-4s-1-6 0-9c2-3 4-5 8-6z" class="O"></path><path d="M252 435h0 0l-1-1-2-1c-2-1-2-4-2-6v-1c-1 0-6-2-7-2-2-1-4-3-6-5-1-1-6-2-8-3-5-1-9-4-13-7 7 1 14 3 21 3h3l1-2c3-3 4-8 9-9 1-1 2-1 3 0-2 2-4 4-5 7 0 3 0 5 1 7 1 1 2 1 3 1 3 1 5 0 6-2 3-1 5-4 7-6 3-3 8-8 9-13v-1c1-1 3-1 3-1l1 1c-1 0-1 0-2 1l2 2c1-2 3-3 5-4 1 0 2 0 3 1h0l-2 2c1 3 2 5 4 8 2 4 2 9 1 15 5-4 11-8 12-15l1-10c2 2 2 4 3 7l1-3v5c-1 7 0 15-2 22 2 1 7 1 8 2l-3 2-6-2c-2 1-4 1-6 1h0l3 1c-4 0-7-1-11-1h0c-5 1-15 4-17 8v1l-1 1-1-1 1-1-1-1-2 2h-3-1v-1h2v-1h-4c-2 1-5 1-7 0z" class="D"></path><path d="M254 428h2c2 0 4 0 7 1v1c-1 0-2 1-3 1h2 1l-1 1h-3c-2-1-3-2-5-3v-1z" class="U"></path><defs><linearGradient id="A" x1="255.346" y1="421.821" x2="265.482" y2="413.057" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#a7a7a7"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M271 405h0c-3 5-6 11-10 15-2 2-5 4-8 4l-1-1v-1c1 0 4-3 5-4 4-3 8-6 11-10l3-3z"></path><path d="M276 406h1c1 1 1 2 1 3 1 3 1 6-1 9-2 4-6 5-11 6h-3c1-3 3-5 5-7l8-11z" class="O"></path><path d="M303 398v5c-1 7 0 15-2 22 2 1 7 1 8 2l-3 2-6-2c-2 1-4 1-6 1h0l3 1c-4 0-7-1-11-1h0c-5 1-15 4-17 8v1l-1 1-1-1 1-1-1-1-2 2c0-1 0-1-1-2h-1c3-4 7-7 11-9 3-1 4-2 6-4l1-1h1v2c-1 0-1 0-1 1 2 1 3 0 5 0 4-1 9-1 13 0h1c1-4 1-9 1-13 0-3 1-7 1-10l1-3z" class="Z"></path><path d="M267 435v-1c2-2 5-5 8-6h2c1-1 1-1 3-1 6 1 13-1 20 0-2 1-4 1-6 1h0l3 1c-4 0-7-1-11-1h0c-5 1-15 4-17 8v1l-1 1-1-1 1-1-1-1zm454-37l1-1c2-3 4-4 7-6-2 2-4 6-4 9h0c2 9 6 12 13 17 0-8 2-13 6-20l-1-1c-4-6-5-10-4-17 0 3 0 6 2 9 1 4 5 6 9 7v1c1 3 3 9 5 12 2 0 3 0 5 2h0c-1-2-1-4-2-5-2-1-2-1-3-3h1c1 1 1 2 2 2h1l8 8c1 1 2 3 4 4 1 1 3 1 4 0h4c1-2 1-5 1-7-1-4-2-6-5-9 1 0 3 0 5 1s4 4 5 6c1 1 2 4 4 5h5l18-3c-4 4-10 7-16 8-1 0-4 0-4 1-1 0-2 2-2 2-1 3-6 4-9 5l-2 1s-1 1-1 2c-1 3-2 5-4 7-3 0-4 0-7 1l3 1-1 1-4-1c-2 1-4 1-5 2h-1c1-1 1-1 1-2v-3-1c-3-2-5-3-8-4-2 1-5 0-7 0-4-1-9-1-13-1-2 1-5 1-7 2-3 1-5 2-7 3-4 2-8 4-11 8-2 2-3 4-5 6-3 5-7 11-10 17 0 2-1 3-2 4v-1l3-12 3-7c3-4 6-8 10-12 5-4 13-7 14-13v-2c1-1 1-2 1-3v-2c1-2 1-2 1-3v-1-4-4c-1-1-1-2-1-3v-3z" class="D"></path><path d="M723 427c1-3 1-5 2-8v-2c0-1-1-3-1-4l1-1h0c1 2 1 5 2 7 0 0 0 1 1 2-1 1-2 1-2 3h3 1c-1 1-2 1-3 1s-3 1-4 2z" class="B"></path><path d="M755 408c2 0 3 0 5 2h0c3 5 7 9 12 13v2h-1c-7-5-12-9-16-17z" class="e"></path><path d="M728 421c1 1 2 2 4 3h0c4 0 8 1 12 0h0c-1-2-1-4-2-5l5 5v1c2 1 4 2 6 2 3 1 5 3 7 5v1c-3-2-5-3-8-4l-1-1c-5-1-10-2-15-2-3 0-6 0-9 1-3 0-5 2-8 3 2-1 3-2 4-3s3-2 4-2 2 0 3-1h-1-3c0-2 1-2 2-3z" class="a"></path><path d="M748 406h1c2 3 4 7 7 10 2 3 5 5 6 8h-2c-3 0-7-1-10-3-2-1-4-4-4-7-1-3 0-6 2-8z" class="O"></path><path d="M764 427v-2h1l2 1 2 1c1 0 2 0 3 1 1 0 1 0 2-1h0c0 1 0 1 1 1l2 1c0-1 1-1 1-1-1 3-2 5-4 7-3 0-4 0-7 1l3 1-1 1-4-1c-2 1-4 1-5 2h-1c1-1 1-1 1-2v-3-1-1c-2-2-4-4-7-5h8l2 7 1-7z" class="H"></path><path d="M760 434h0c1 1 2 2 3 2s1 1 2 1c-2 1-4 1-5 2h-1c1-1 1-1 1-2v-3zm4-7v-2h1l2 1 2 1c1 0 2 0 3 1 1 0 1 0 2-1h0c0 1 0 1 1 1l2 1c0-1 1-1 1-1-1 3-2 5-4 7-3 0-4 0-7 1l-4-1v-1h1 1 3l-1-1h-1v-1h2v-1l1-2c-1-1-3-2-5-2z" class="B"></path><path d="M608 224c9-3 16-5 24-1l1 1s1 1 2 1l1 1h-2c0 2 0 2 1 4h0l1 1v-1c2-2 3-2 6-3v1c-1 0-3 1-4 1-1 1-2 4-3 5-2 5-4 9-8 12 1 1 2 1 3 1s1 1 2 1h3c1 1 1 1 3 1h0c-2-1-3-1-4-3v-3-1-1c1 0 1-1 1-1 1-2 2-3 2-4l1-1v-2h1l1-2 1-1 1 1v1h-1l4 17h1c-1 1 0 1-1 2h-4l4 1c6 1 11 2 17 2v1c0 7 3 13 7 19 2 1 3 2 5 3h2 1l-1 1-3 33c-1 8-3 16-3 23l-1 7c-2 5-3 11-6 15h0-1c0 1-1 2-1 4-1-8-1-16-3-24-1-3-4-13-7-15l-2 1c-3-8-8-12-16-16h8l2-1c2 0 4-1 6-1l-1-6c0-2-2-3-4-4h0 1c5 2 9 3 14 6v-1l-3-2h4 0l-1-2v-1h-2c2-1 3-2 5-4l1-1h1c1-1 1-1 1-3l-5-10c-1-3-2-5-5-8h-1l-5 3v-3-1c0-3-3-8-5-9 0-1-2-1-2-1-1-1-2-1-3-1s-3 3-4 3l-1-1-1-3c-1-1-3-3-4-3-3-1-6 1-8 2 0-2 1-4 1-6-5-2-6-1-10-5 2 1 5 1 7 1 5-1 8-4 11-7l-3-6c-2-4-5-5-9-6l-6 1c0-1 0-1 1-2h-4-1z" class="D"></path><path d="M644 253c-7-1-11-2-16-5 4 1 8 2 13 2v1l4 1c-1 0-1 0-1 1z" class="G"></path><path d="M608 224c9-3 16-5 24-1l1 1s1 1 2 1l1 1h-2c-1 0-2-1-3-2-3-1-6-2-9-1h-1c-3 0-5 0-7 2h4l-6 1c0-1 0-1 1-2h-4-1z" class="H"></path><path d="M641 306l2-1c3 0 5 0 7 1l1 1h1c4 3 7 7 8 12 2 4 1 9 1 13h0c0-5-1-9-3-13 0-1 0 0-1-2-1-3-5-6-7-7l-1-1c-1 0-3-1-4-1-1-1-1 0-2-1-1 0-1 0-2-1z" class="P"></path><path d="M645 252c6 1 11 2 17 2v1c0 7 3 13 7 19h-1c0-1-1-2-1-2 0 2 1 4 2 6 0 1 1 1 1 2v1l1 1h-1l-8-19c-1-1-2-4-3-5-2 0-4-1-5-1l-10-4c0-1 0-1 1-1z" class="W"></path><path d="M645 249l-8-2c-1 0-2-1-3-2 1-4 4-9 6-13h1l4 17z" class="O"></path><path d="M665 286c4 7 2 17 2 25 0 3 1 5 1 8h0c-3-6-3-11-8-17 0 0 0-1-1-2h0v-1l-3-2h4 0l-1-2v-1h-2c2-1 3-2 5-4l1-1h1c1-1 1-1 1-3z" class="P"></path><path d="M663 289c0 1 1 3 0 4 0 1-2 2-2 3-1 2 1 1-1 3h-1l-3-2h4 0l-1-2v-1h-2c2-1 3-2 5-4l1-1z" class="Q"></path><path d="M670 282h1l-1-1v-1c0-1-1-1-1-2-1-2-2-4-2-6 0 0 1 1 1 2h1c2 1 3 2 5 3h2 1l-1 1-3 33c-1 8-3 16-3 23l-1 7c-2 5-3 11-6 15h0-1l4-20 2-11c0-2 0-5 1-6 1-9 2-17 2-26 0-4 0-7-1-11z" class="G"></path><path d="M669 274c2 1 3 2 5 3h2 1l-1 1c-1 1-2 3-4 3 0-1-1-2-2-3 0-1-1-2-1-4h-1 1z" class="X"></path><path d="M669 319h0c0 3 0 5-1 8v1l-1 11v-2-1l2-1v-3-2c1-2 1-3 1-5h-1c1-1 1-1 1-2h0c1-2 1-2 1-3v-1-2c1-2 1-4 2-6-1 8-3 16-3 23l-1 7c-2 5-3 11-6 15h0-1l4-20 2-11c0-2 0-5 1-6z" class="M"></path><path d="M277 479c0-2 2-5 2-6 0-2-1-4-2-6 2 1 3 1 5 1 1 0 2-1 3-2 0-2-1-5 0-7l2 2c1-3 1-7 3-10 2-5 6-8 10-12-3-1-6-1-10-1-4 1-13 1-16 5-1 1-1 3 0 4v1h-1c-3-1-4-2-6-3s-4-2-7-1-6 3-8 6l1 1c5 2 14 3 20 2l3-1c-3 2-5 4-8 5-14 4-24 0-36-6 1 10 1 17-5 26-6 7-12 10-20 11l7-5c5-3 9-9 10-15 2-15-9-26-17-38-5-6-9-13-8-22h0c1-4 2-6 4-9v3 4c1 5 3 9 7 13 5 4 10 7 16 10 6 5 11 10 18 13h0c3-3 5-5 8-7 2 1 5 1 7 0h4v1h-2v1h1 3l2-2 1 1-1 1 1 1 1-1v-1c2-4 12-7 17-8h0c4 0 7 1 11 1l-3-1h0c2 0 4 0 6-1l6 2 3-2c10 4 16 10 22 19 0 1 0 2 1 4 2 3 2 6 3 9l6 18c0 2 1 5 2 7l-1 2h0c-2 0-3-1-4-1h-1c-2 0-4-2-6-3l-9-3-6-2 1-1v-3l-3-1v-1c-2-2-5-2-7-3-5-1-10-1-15 0h-1c-4 1-8 5-10 9-1 0-1 1-1 2-1 1-1 1-3 1v-1z" class="I"></path><path d="M275 433c2 0 5-2 7-2 3 2 6 0 9 2h-9c-2 1-4 1-7 1v-1z" class="V"></path><path d="M282 431h11c3 0 7 2 9 3h-2l-9-1c-3-2-6 0-9-2z" class="H"></path><path d="M305 440c4 1 7 2 10 4 5 2 8 5 11 10h0l-2-3c-4-4-10-7-16-8h-7c1-2 2-2 4-3z" class="U"></path><defs><linearGradient id="C" x1="324.959" y1="479.752" x2="318.458" y2="474.348" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#8e8d8e"></stop></linearGradient></defs><path fill="url(#C)" d="M317 473c8 3 14 8 21 12h-1c-2 0-4-2-6-3l-9-3-6-2 1-1v-3z"></path><path d="M269 437v-1c2-4 12-7 17-8h0c4 0 7 1 11 1h0c7 3 22 9 25 16-6-4-13-9-20-11-2-1-6-3-9-3h-11c-2 0-5 2-7 2l-6 4z" class="Z"></path><path d="M309 427c10 4 16 10 22 19 0 1 0 2 1 4 2 3 2 6 3 9l6 18c0 2 1 5 2 7l-1 2-6-20c-1-5-2-10-4-14-6-11-14-19-26-23l3-2z" class="a"></path><defs><linearGradient id="D" x1="199.56" y1="408.989" x2="213.933" y2="423.005" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262527"></stop></linearGradient></defs><path fill="url(#D)" d="M224 444l1 2-1 1-3-6-1-1c0-1-1-1-1-2-1-1-2-2-2-3-1-1-2-1-3-3l-1-1c-1-1-2-1-2-2-1-1 0-1-1-2-2-2-4-5-6-8h0v-2h-1l-1-1c1-1 1-2 0-3v-1-1c-1-1-1-2-1-4l2-1c1 5 3 9 7 13 0 2 0 3 2 5l7 6c4 3 7 7 10 9l8 5v1l5 2c1 2 3 2 5 4h-1c-3-1-7-3-9-5-7-4-12-9-17-14l-1 2 2 3h0c2 2 3 4 3 7z"></path><path d="M224 444c-1-1-1-2-2-3 0-1-1-2-2-3l-1-1c0-2-1-2-2-3-1-2-2-3-3-4l-1-1c0-1-1-2-1-2v-1c3 1 5 4 8 6l-1 2 2 3h0c2 2 3 4 3 7z" class="B"></path><path d="M303 450c4-1 8 0 12 1 9 4 12 14 16 23l-11-6c-6-4-11-8-18-9 2 1 4 3 6 5l-14-1c0-3 0-7 2-9 2-3 4-4 7-4z" class="O"></path><path d="M111 145c-3-1-5-2-8-4 6 1 12 1 18 1h25 100l2 2h27 12l2 1h77l23-1h0 8v-2h12 7 13 45 1c2 1 4 1 6 1-3 1-5-1-7 1-23 1-47 1-69 7l-18 6c-8 3-16 7-24 7-20 3-40 2-59 2l-16-1c-1 0-4-1-6-1-6-1-13-1-19-2l-20-3-69-9c-17-3-34-5-52-6-3 0-9-1-11 1z" class="Y"></path><path d="M397 142h12c-3 3-8 2-12 2v-2z" class="J"></path><path fill="#e99593" d="M345 160h0c-25 3-51 2-76-2-6-1-14-1-20-3h-3-1v-1c12 0 24 3 36 4 8 2 18 2 26 2 12 0 24 0 36-1h3l-1 1z"></path><path d="M248 144h27 12c-3 1-6 1-9 1v2c2 2 4 2 6 3l9 1c3 0 8-1 11 1h4l16 2c3 1 6 2 9 2 4 0 8 0 12 1 2 0 4-1 7 0h0l1 1c-2 0-5 0-7 1h-3c-12 1-24 1-36 1-8 0-18 0-26-2h3c2 1 3 0 5 0 1 0 2 1 3 1h5c2 1 5 0 7 0l2 1c1-1 3 0 4-1h4c-3-1-7 0-10-1l-14-3c-3-1-6-2-9-2h-3c-1-1 0-1-2-1-4-1-10-2-13-4-2-1-3-2-4-2h-1c-3-1-6-1-9-1v1h-1v-2z" class="b"></path><path d="M284 150c-2 0-4-1-6-1-4-1-6-2-9-4h2 7v2c2 2 4 2 6 3zm9 1c3 0 8-1 11 1h4l16 2c3 1 6 2 9 2 4 0 8 0 12 1h-3c-6 1-12 0-18 0-3-1-7-1-10-1l-21-5z" class="f"></path><path fill="#e39b99" d="M389 144c8 0 17 0 25 1-6 2-12 2-18 4-17 4-33 10-51 11l1-1c2-1 5-1 7-1l-1-1h0c-3-1-5 0-7 0-4-1-8-1-12-1-3 0-6-1-9-2l-16-2h-4c-3-2-8-1-11-1l-9-1c-2-1-4-1-6-3v-2c3 0 6 0 9-1l2 1h77l23-1z"></path><path d="M287 144l2 1h-1c-2 1-6 0-9 0v2l2 1h1l3 1h3s1 0 1 1h4 6c-2-1-3 0-5-1s-4-1-6-2h-2l1-1c1 0 2 1 3 1 3 1 6 2 9 2l2 1c3 0 6 0 9 1h10c3 0 6 1 9 1 4 0 8 0 11 1 1-1 2-1 3-1 1 1 4 0 5 0h23c2 0 6-1 8 0h-2c-1 0-1 1-2 1-3 2-19 5-22 5l-1-1h0c-3-1-5 0-7 0-4-1-8-1-12-1-3 0-6-1-9-2l-16-2h-4c-3-2-8-1-11-1l-9-1c-2-1-4-1-6-3v-2c3 0 6 0 9-1z" class="h"></path><path d="M540 144c-2-1-5-2-7-3 11 1 22 1 33 1h55l2 3 11-1c6-1 10 1 16-2h30v1h0l6 2h1 40c6 0 12 0 18-1 3-1 5 0 8-1l14-1h108c15 0 32 1 47-1-2 2-5 3-7 3h0-19c-14 1-28 3-41 5l-70 9-28 5c-6 1-13 1-19 2h4 0c-1 1-3 1-5 2s-5 1-8 2l-24 2c-2-1-3-1-5-1 2 0 3 0 4-1-1 0-1 0-1-1v-1h0l-9-1h1 4c-4-1-8 0-11-1-8 0-15-1-22-2-13-3-24-8-36-12-15-4-30-5-45-6-9 0-19-1-28-1h-1c-2 1-5 1-8 1 0 1 0 1 1 2h1l1 1h-1c-3-1-7-2-10-4z" class="Y"></path><path d="M540 144h17-1c-2 1-5 1-8 1 0 1 0 1 1 2h1l1 1h-1c-3-1-7-2-10-4zm198 21h4 0c-1 1-3 1-5 2s-5 1-8 2l-24 2c-2-1-3-1-5-1 2 0 3 0 4-1-1 0-1 0-1-1v-1h0l-9-1h1 4l39-1z" class="C"></path><path d="M663 156v-1l-2-1h1 2c2 0 4-1 6 0h3c3-1 6-1 9-1l15-1c2 0 4-1 7-1h8c2 0 5 0 7-1 5 0 10-2 15-3 1 0 3 0 4-1h1 1c-2 2-8 3-11 3-2 1-4 1-7 1h3 4c1-1 3 0 4 0l5-1v-1c2 0 3-1 5-1h2c1-1 1-1 2-1 0 1-2 2-4 2-3 2-8 2-11 3h-5c-3 0-4 0-7 1l-17 2h-2c1 1 1 2 3 2l2-1h2l1 1h-1 6v1h2 3v1h-2 0c1 1 3 1 4 1h0v1h7c2-1 4 0 6-1 4-1 10 0 14-1 3-1 5-1 8-2h6c3-1 7-1 11-1l1-1 1 1c-2 1-8 1-10 2l-40 4c-6 1-11 1-17 1-15 0-30-2-45-6z" class="h"></path><defs><linearGradient id="E" x1="696.154" y1="160.209" x2="695.797" y2="155.084" xlink:href="#B"><stop offset="0" stop-color="#c75957"></stop><stop offset="1" stop-color="#e47474"></stop></linearGradient></defs><path fill="url(#E)" d="M721 160h-16c-4 0-8 0-11-1h-9-4c-1-1-2-1-3-1-2-1-4 0-6-1v-1c2 1 12 1 14 0s5-1 8-1c2-1 4-1 7-1 1 1 1 2 3 2l2-1h2l1 1h-1 6v1h2 3v1h-2 0c1 1 3 1 4 1h0v1z"></path><defs><linearGradient id="F" x1="741.706" y1="156.855" x2="739.343" y2="146.946" xlink:href="#B"><stop offset="0" stop-color="#c1504d"></stop><stop offset="1" stop-color="#df7073"></stop></linearGradient></defs><path fill="url(#F)" d="M753 143l14-1-2 2h10 1v1l-1 1c-2 2-3 3-5 4h0c-3 2-7 3-10 4l-8 1h-3v-1c-1 0-1 0-2 1v-1l-6 1c-1 1-2 1-3 2h1c1 0 0 0 1 1 1 0 2 0 3-1h4 2 1c2-1 3 0 5-1h1 0c-3 1-5 1-8 2-4 1-10 0-14 1-2 1-4 0-6 1h-7v-1h0c-1 0-3 0-4-1h0 2v-1h-3-2v-1h-6 1l-1-1h-2l-2 1c-2 0-2-1-3-2h2l17-2c3-1 4-1 7-1h5c3-1 8-1 11-3 2 0 4-1 4-2v-1c-2 0-3 1-4 1-4 0-7-1-10-1h-46 40c6 0 12 0 18-1 3-1 5 0 8-1z"></path><path d="M701 154h2 0c3 1 5 0 7 0v1h-2-2l-2 1c-2 0-2-1-3-2z" class="f"></path><path d="M753 143l14-1-2 2h-20c3-1 5 0 8-1z" class="L"></path><defs><linearGradient id="G" x1="687.695" y1="152.465" x2="687.038" y2="141.447" xlink:href="#B"><stop offset="0" stop-color="#df8d89"></stop><stop offset="1" stop-color="#ecb7b8"></stop></linearGradient></defs><path fill="url(#G)" d="M650 142h30v1h0l6 2h1 46c3 0 6 1 10 1 1 0 2-1 4-1v1c-1 0-1 0-2 1h-2c-2 0-3 1-5 1v1l-5 1c-1 0-3-1-4 0h-4-3c3 0 5 0 7-1 3 0 9-1 11-3h-1-1c-1 1-3 1-4 1-5 1-10 3-15 3-2 1-5 1-7 1h-8c-3 0-5 1-7 1l-15 1c-3 0-6 0-9 1h-3c-2-1-4 0-6 0h-2-1l2 1v1c-8-1-16-4-23-6l-13-4c-1 0-3 0-3-1h-1l11-1c6-1 10 1 16-2z"></path><path d="M650 142h30v1h0l6 2-52-1c6-1 10 1 16-2z" class="L"></path><path d="M774 435c3 2 6 5 9 7 5-3 9-7 14-11 9-7 21-11 25-22 1-3 1-7 1-10 3 7 5 12 2 20-2 7-7 12-12 18-5 7-10 15-11 24-1 4 0 8 2 13 2 4 5 8 10 11 1 1 3 2 4 3-9-2-14-5-19-12-6-8-7-16-6-25-10 5-20 9-31 6-3-1-7-2-9-4h2c4 0 9 0 13-1h5v-1c0-2 0-3-2-5-2-1-5-2-8-2-3 1-7 3-11 4 1-1 1-3 2-4-1-2-5-4-7-4-6-2-15-3-20-1v1c5 5 11 9 11 17v5l5-5c-1 4-2 8-2 11l7-1c0 2-2 5-1 6 0 1 1 3 1 3 2 4 4 7 4 10 2 11-4 21-10 29-4 5-9 10-15 13-5 4-14 7-21 5-5-1-8-3-11-6 4 1 9 3 14 1 1 0 1-1 1-2l-1-1v-1c3 0 6 1 9 0 2-3-1-5 1-8l1 1c1 1 2 1 4 0 2 0 3-2 4-3l1-1h0c-6-4-10-7-12-15-1-5-1-10 2-15l2-2-1-1h0l2-1v1h2l1 1 1-1h2v1-1-1c-2 0-5-1-6-2-2-2-4-1-6-2l-2 1h0-3c-4 1-8 2-11 4h-3c-1 0-3 1-3 1l-9 6 5-15c-1 1-1 0-1 1h-1 0c0-2 1-4 2-6v1c1-1 2-2 2-4 3-6 7-12 10-17 2-2 3-4 5-6 3-4 7-6 11-8 2-1 4-2 7-3 2-1 5-1 7-2 4 0 9 0 13 1 2 0 5 1 7 0 3 1 5 2 8 4v1 3c0 1 0 1-1 2h1c1-1 3-1 5-2l4 1 1-1-3-1c3-1 4-1 7-1z" class="I"></path><path d="M728 431c2 0 3 0 4 1h0c1 0 2 1 4 1-4 0-8 0-11-1l3-1z" class="H"></path><path d="M770 437c3 1 5 4 7 7 1 1 1 2 1 3-4-3-6-6-9-9l1-1z" class="B"></path><path d="M710 444l5-2-3 2v2h0c-4 1-6 3-9 5v-1c1-1 2-1 2-3 1-1 3-2 5-3z" class="Z"></path><path d="M715 442c1-1 3-1 4-1 2 0 6 3 8 4h-1c-5-1-9-1-14 1h0v-2l3-2z" class="U"></path><path d="M792 442s1 1 2 1c-3 3-10 7-14 7l1-2c1-1 3-2 5-3l6-3z" class="e"></path><path d="M792 442l16-13-4 4c-3 3-6 8-10 10-1 0-2-1-2-1z" class="a"></path><path d="M712 472h0c0 1-1 2-2 2 0 1 1 1 1 2-4 1-8 2-11 4h-3c4-3 9-6 15-8z" class="g"></path><path d="M707 441c3-4 7-6 11-8 2-1 4-2 7-3h1l-2 1c1 1 1 1 1 0h3l-3 1c-3 1-5 2-7 3-1 1-3 3-5 3s-4 2-5 4l-1-1z" class="U"></path><path d="M712 472v-1c2-3 9-3 12-3 1 1 2 1 3 1 3 0 6 1 8 3h0c-4-1-7-3-11-2 1 1 2 1 2 1 2 0 4 1 5 2-6-2-12-2-19-1h0z" class="V"></path><path d="M724 468c7 0 12 0 17 4 3 4 7 11 7 16v1c-1-2-1-3-2-4-2-6-6-10-11-13-2-2-5-3-8-3-1 0-2 0-3-1z" class="a"></path><path d="M690 467v1c1-1 2-2 2-4 3-6 7-12 10-17 2-2 3-4 5-6l1 1-2 2 1 1v-1h3c-2 1-4 2-5 3 0 2-1 2-2 3v1c-3 2-4 5-6 8-2 2-4 5-5 8-1 1-1 4-2 5s-1 0-1 1h-1 0c0-2 1-4 2-6z" class="V"></path><path d="M712 472c7-1 13-1 19 1 5 3 8 6 11 11-3-2-5-4-8-5-6-3-11-5-18-4l-2 1h0-3c0-1-1-1-1-2 1 0 2-1 2-2z" class="j"></path><path d="M725 430c2-1 5-1 7-2 4 0 9 0 13 1 2 0 5 1 7 0 3 1 5 2 8 4v1 3c0 1 0 1-1 2 0-1-1-1-2-2l-3-3h-7l-11-1c-2 0-3-1-4-1h0c-1-1-2-1-4-1h-3c0 1 0 1-1 0l2-1h-1z" class="V"></path><path d="M754 434c2-1 2 0 4 0 0 1 1 1 1 2l1 1c0 1 0 1-1 2 0-1-1-1-2-2l-3-3z" class="H"></path><path d="M728 484c1 0 1-1 1-1 2 0 4 1 5 2 3 2 5 4 5 7l1 1c0 2 0 4-1 7 0 2-2 3-4 4h-4c-3 0-4-1-6-3-2-3-3-7-2-10 1-4 3-6 5-7zm-10-34h4c3 0 7 1 9 4 2 2 2 5 1 8-1 2-11 2-13 3 1-2 2-3 4-5h-1c-4 0-7 2-10 4l-15 9c1-5 3-10 5-14 4-5 10-8 16-9z" class="O"></path><defs><linearGradient id="H" x1="379.758" y1="150.052" x2="390.754" y2="243.888" xlink:href="#B"><stop offset="0" stop-color="#000808"></stop><stop offset="1" stop-color="#3e1615"></stop></linearGradient></defs><path fill="url(#H)" d="M474 144h20c2 0 5-1 7 0l-36 17c-5 2-10 4-15 7l4 4c3 3 7 8 8 12 0 1 0 2 1 3h-1v4l1 1c0 1 0 1 1 2v5l-1 1c-1 1 0 3-1 4v2 1l-3 6-2 2v1c0 3-2 4-4 6-1 1 0 0-2 1v1l-3 1c-1 1-2 1-3 1l-1 2c-1 0-5-1-6-1-3-1-7-1-11-2h0 1c-2-2-3-1-5-2h-1c-4-1-9 0-13 2 0 1-1 2-2 3l-1-1c-2 2-3 4-5 6h-1l-24 15c-1 1-2 1-3 2l-7-8v-3c-2-5-6-9-8-14-2-4-2-9-3-13-2-7-5-12-10-18-6-6-16-12-25-13-2-1-5-1-7-1l-6 2 2 1c-9 1-15 4-23 9v-3c0-2 0-2-1-4v-1c0-1-1-2-1-3l-1-2v-3h0l-2-2 1-1c1 1 0 1 1 1h1c-1 0-1-1-1-2-1-1-1 0-1-2-2 0-5 0-7-1-3-1-5-1-7-3l9 1c-1-1-2-2-4-2h0-1-2c-1 0-1-1-2-1h-1-1-1 5 0 3c3 1 6 1 9 0 2 0 5 1 6 1l16 1c19 0 39 1 59-2 8 0 16-4 24-7l18-6c22-6 46-6 69-7z"></path><path d="M268 166l9 1 41 2c-4 2-7 0-11 1l-20 1-5-1c-2 0-5 0-7-1-3-1-5-1-7-3z" class="E"></path><defs><linearGradient id="I" x1="463.87" y1="207.23" x2="447.095" y2="179.682" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#29292a"></stop></linearGradient></defs><path fill="url(#I)" d="M458 194l-1-2-1-2v-1c-1-1-1-2-2-2v-2c-2-2-6-6-8-7-1-1-2-1-3-2h1c1 0 1 0 2 1h1l3 3h1c-1-2-4-5-6-6l-2-2c-2-3-6-3-10-5 2 0 6 1 8 2 2 2 6 6 8 6 0-1-1-2-2-3h0 1c2 1 3 2 4 3 0-3-4-5-4-6v-1l5 5 1-1c3 3 7 8 8 12 0 1 0 2 1 3h-1v4l1 1c0 1 0 1 1 2v5l-1 1c-1 1 0 3-1 4v2 1l-3 6-2 2v1l-2 2v-1l-1-1-1 2h-1l3-3c1-1 1-1 1-2 1-2 2-3 2-5h-1l1-1v-1-2-1c1-1 1-3 1-5l-1-1v-3z"></path><path d="M452 175c0-3-4-5-4-6v-1l5 5 1-1c3 3 7 8 8 12 0 1 0 2 1 3h-1v4h0c0-1-1-1-1-2l-1-1v-1c-1-1-2-3-3-5h0l-2-2 1-1c-2-1-3-3-4-4z" class="H"></path><path d="M318 169l41 3c-4 8-7 14-4 23l14 37c2 5 4 11 7 16-1 1-2 1-3 2l-7-8v-3c-2-5-6-9-8-14-2-4-2-9-3-13-2-7-5-12-10-18-6-6-16-12-25-13-2-1-5-1-7-1l-6 2 2 1c-9 1-15 4-23 9v-3c0-2 0-2-1-4v-1c0-1-1-2-1-3l-1-2v-3h0l-2-2 1-1c1 1 0 1 1 1h1c-1 0-1-1-1-2-1-1-1 0-1-2l5 1 20-1c4-1 7 1 11-1z" class="G"></path><path d="M337 175h0c2 1 4 0 7 1v1 1c-2 0-4 0-6-1h-6-1c-2 0-3-1-5-1h0l3-1c0 1 0 1 1 1h2v1l1-1h2c0-1 0-1 2-1z" class="L"></path><path d="M307 171l20 1c-1 0-3 1-4 1-1 1-1 1-2 0-5-1-11 0-16 0-2-1-5-1-7-1 1-1 7 0 9-1z" class="X"></path><path d="M282 170l5 1c7 1 14-1 20 0-2 1-8 0-9 1 2 0 5 0 7 1-4 0-10-1-14 1h-7c-1 0-1-1-1-2-1-1-1 0-1-2z" class="K"></path><path d="M286 192v-3c0-2 0-2-1-4v-1c0-1-1-2-1-3l-1-2v-3h0c1 1 1 2 2 4l3 8c7-5 11-5 19-6l2 1c-9 1-15 4-23 9z" class="W"></path><path d="M318 169l41 3c-4 8-7 14-4 23l14 37c-2-2-3-4-4-7 0-2-1-3-1-4-1-2-2-4-2-6-1-1-1-3-1-4l-2-2v-3c-1-1-1-1-1-2-1-2-2-4-2-6-1-1-1-1-1-2l-1-1v-2l-1-2v-2-2-3h0c0-1 0-2 1-3v-1l1-2h0l-2-1-1-1 1-1c-2-1-2-1-3-2h-1c-2-1-5-1-6-1l3 1h0c-2 1-4 0-6 0l-13-1-20-1c-6-1-13 1-20 0l20-1c4-1 7 1 11-1z" class="M"></path><defs><linearGradient id="J" x1="353.009" y1="199.262" x2="343.555" y2="206.07" xlink:href="#B"><stop offset="0" stop-color="#111314"></stop><stop offset="1" stop-color="#3f1616"></stop></linearGradient></defs><path fill="url(#J)" d="M313 180c4-2 14 0 19 1 8 3 15 7 20 15 5 10 8 22 11 32 1 3 4 9 3 11h0c-2-5-6-9-8-14-2-4-2-9-3-13-2-7-5-12-10-18-6-6-16-12-25-13-2-1-5-1-7-1z"></path><path d="M407 225v-2c-2-7 1-16 4-22 4-6 10-12 16-16 2-2 4-4 6-4 4-1 9 1 12 3 6 3 9 9 10 16 1-2 1-4 3-6v3l1 1c0 2 0 4-1 5v1 2 1l-1 1h1c0 2-1 3-2 5 0 1 0 1-1 2l-3 3h1l1-2 1 1v1l2-2c0 3-2 4-4 6-1 1 0 0-2 1v1l-3 1c-1 1-2 1-3 1l-1 2c-1 0-5-1-6-1-3-1-7-1-11-2h0 1c-2-2-3-1-5-2h-1c-4-1-9 0-13 2h-2z" class="O"></path><path d="M457 216c0 3-2 4-4 6-1 1 0 0-2 1v1l-3 1c-1 1-2 1-3 1l-1 2c-1 0-5-1-6-1-3-1-7-1-11-2h0 1c-2-2-3-1-5-2h-1c-4-1-9 0-13 2h-2c2-1 4-2 6-2 2-1 5-2 8-2 6 0 13 4 19 3l2 2h2c1-1 0-1 1-1h2l1-1s1-1 2-1l2-1c0-1 1-1 2-2 0-1 0-2 1-2l2-2z" class="H"></path><path d="M455 200c1-2 1-4 3-6v3l1 1c0 2 0 4-1 5v1 2 1l-1 1h1c0 2-1 3-2 5 0 1 0 1-1 2l-3 3h1l1-2 1 1v1c-1 0-1 1-1 2-1 1-2 1-2 2l-2 1c-1 0-2 1-2 1l-1 1h-2c-1 0 0 0-1 1h-2l-2-2 5-2c1 0 2-1 3-2-1-1-1-2-1-3 1-1 2-1 2-2 4-4 6-10 6-15z" class="I"></path><defs><linearGradient id="K" x1="626.09" y1="145" x2="625.087" y2="275.628" xlink:href="#B"><stop offset="0" stop-color="#000808"></stop><stop offset="1" stop-color="#491918"></stop></linearGradient></defs><path fill="url(#K)" d="M557 144c9 0 19 1 28 1 15 1 30 2 45 6 12 4 23 9 36 12 7 1 14 2 22 2 3 1 7 0 11 1h-4-1l9 1h0v1c0 1 0 1 1 1-1 1-2 1-4 1 2 0 3 0 5 1l24-2c4 0 7 0 10 1-2 1-3 1-5 1h-4l1 1c0 1-5 2-6 2-3 2-6 1-9 2h-5l-3 1c2 0 2 0 4 1l2 1c3 0 6 1 9 2 2 1 4 1 6 2v1l-4-1c-2-1-3-1-4-1h-1 1c0 1 1 1 1 2h0c1 2 2 4 3 5h-3l-2 1h0c-2-1-2-1-4-1l-1 1 1 1h0l1 4h1l2 2c2 2 4 5 6 8v1c-1-2-3-3-4-5-2-1-5-2-7-3h0-4-1c-2 1-2 2-3 3v2l-1-1-1-1 1 3-1 1c1 4 1 11-2 15-2 5-6 9-9 13l2 1c1 2 4 4 6 6l-8-5-2-1c-7-4-11-11-13-19v-1c-1-2 1-10-1-11v6c0 2 0 5 1 7v2 1 1l2 4 1 1v2c1 2 6 6 7 7 2 1 4 1 5 3h0-1c-1 0-2-1-3 0-1 0-1 1-2 1 0 1-1 1-1 1l-1 1h-1v1 1c-2 2-4 3-6 6l-2 2c0 2 0 3-2 4v2 4h0c-1 1-1 4-1 6 1 2 0 2 1 4s0 3 1 5v2h-2c-2-1-3-2-5-3-4-6-7-12-7-19v-1c-6 0-11-1-17-2l-4-1h4c1-1 0-1 1-2h-1l-4-17h1v-1l-1-1-1 1-1 2h-1v2l-1 1c0 1-1 2-2 4 0 0 0 1-1 1v1 1 3c1 2 2 2 4 3h0c-2 0-2 0-3-1h-3c-1 0-1-1-2-1s-2 0-3-1c4-3 6-7 8-12 1-1 2-4 3-5 1 0 3-1 4-1v-1c-3 1-4 1-6 3v1l-1-1h0c-1-2-1-2-1-4h2l-1-1c-1 0-2-1-2-1l-1-1c-8-4-15-2-24 1h1 4c-1 1-1 1-1 2-6 1-14 3-19 1v-1c-3-1-7-3-9-6v-2c0-1 0-1-1-2 0-1 0 0-1-1 0-2-1-3-2-5 0-1 0-1-1-2v-2c-1-2 0-2-1-4 0-2-1-4 0-6l1-4v-1c0-1 0-1 1-2l3-7c1-1 2-3 3-4 1-2 5-5 7-7h0v-2-1l-1 1c-10-6-21-11-31-16-3-1-7-3-11-5h1l-1-1h-1c-1-1-1-1-1-2 3 0 6 0 8-1h1z"></path><path d="M611 171c2 0 5-1 7 0h0l-7 1v-1z" class="B"></path><path d="M680 208v-1-1c-1-2-1-6-1-8l1-2h0l1 5v3 3c-1 1 0 3 0 5-1-1-1-3-1-4z" class="C"></path><path d="M668 185c2 3 4 5 4 10v1c-1-1-3-3-4-5 0-1-1-1-1-3h0c0-1 0-2 1-3z" class="Y"></path><path d="M679 227h1c1 3 2 5 4 7h1l-4 2c-1 2-1 3-1 5-2-1-6-5-7-7l4-2c1-1 1-3 2-5z" class="K"></path><path d="M611 171v1c-4 0-8 1-11 3h2c3-1 6 0 9 1h0c-7 0-12 0-18 5-2 1-4 4-6 6l-1 1-1-1 2-2c1-2 3-4 5-5l2-2v-1c-3 2-5 4-8 7h0l-1-1 5-5c1-1 3-3 6-4 3-2 10-3 15-3z" class="U"></path><path d="M670 175c-5-1-9-3-14-3-6-1-11 0-17 0v-1h3c9-2 20 0 30 2h1c1 0 0 0 1 1 2 1 5 2 7 2h1c3-2 7-1 11-2l-5 3c-2 0-4 0-5 1h-1c-1-1-3-1-5-1h0c-3-1-4-2-7-2z" class="M"></path><path d="M682 202v1c1 2 0 6 1 8h0c-1 3 0 5 0 8l1 1v1c1 1 2 2 2 3 0-1 1-1 0-2h0v-1l-1-1v-2-2c0-1-1-1-1-2v-3c-1-3-1-6 0-8v-2h0v2l1-1c1 1 1 2 1 3v2l1-1h1 0c-1 2-1 3-2 5v1 7h1l1 3c0 3 2 6 3 10-5-5-8-11-9-17-1-1-1-2-1-3 0-2-1-4 0-5v-3-3h1v1z" class="D"></path><path d="M681 201h1v1c0 4 1 9 0 13-1-1-1-2-1-3 0-2-1-4 0-5v-3-3z" class="B"></path><path d="M670 175c3 0 4 1 7 2h0c2 0 4 0 5 1h1c1-1 3-1 5-1l-3 1v1c1 1 1 3 1 4v1h1l2 1v1c-1 1-2 1-2 3v2c0 1 0 1 1 2l-3 8-1-11v-2c-3-7-8-10-14-13z" class="W"></path><path d="M677 177h0c2 0 4 0 5 1h1c1-1 3-1 5-1l-3 1v1c1 1 1 3 1 4h-1c-1-2-2-2-3-3-3-1-4-2-5-3zm9 7h1l2 1v1c-1 1-2 1-2 3v2c0 1 0 1 1 2l-3 8-1-11c1-1 1-2 1-3-1-1-1-2-2-3h1l1 2h1v-2z" class="Y"></path><path d="M680 208c0 1 0 3 1 4 0 1 0 2 1 3 1 6 4 12 9 17-1-4-3-7-3-10 1-4 2-7 4-10l2-1c1 1 1 1 1 3 2 5-3 9-4 14l2 4 1 1 2 1c1 2 4 4 6 6l-8-5-2-1c-7-4-11-11-13-19 0-2 0-5 1-7z" class="G"></path><path d="M668 191c-1-1-3-2-4-3-1 0-2 1-3 2h-1v-1c-1 0-3 0-4-1-3 0-8-2-10-4-1-2-2-4-2-6 1-1 3-1 5-1s5 1 7 1c4 2 8 4 12 7-1 1-1 2-1 3h0c0 2 1 2 1 3z" class="L"></path><path d="M656 178c4 2 8 4 12 7-1 1-1 2-1 3h0c-1 0-1-1-2-1-1-2-3-4-5-5-2 0-4-1-6-2l2-2z" class="M"></path><path d="M654 186c-3 0-5-2-7-4 0-1-1-2 0-3 1 0 1 0 2-1 2 0 3 1 4 2 0 1-1 2-1 3l1 1c1 1 1 1 1 2h0z" class="D"></path><path d="M653 180h0c3 2 7 3 9 6v1c-3 0-6 0-8-1h0c0-1 0-1-1-2l-1-1c0-1 1-2 1-3z" class="B"></path><path d="M729 169c4 0 7 0 10 1-2 1-3 1-5 1h-4l1 1c0 1-5 2-6 2-3 2-6 1-9 2h-5-3l-2-1h-5c2-1 3-1 5-2-4 0-9 0-13 1s-8 0-11 2h-1c-2 0-5-1-7-2-1-1 0-1-1-1h-1c11 0 22-1 33-2l24-2z" class="Y"></path><path d="M730 171l1 1c0 1-5 2-6 2-3 2-6 1-9 2h-5-3l-2-1h-5c2-1 3-1 5-2 8-2 17-1 24-2z" class="b"></path><path d="M693 174c4-1 9-1 13-1-2 1-3 1-5 2h5l2 1h3l-3 1c2 0 2 0 4 1l2 1c3 0 6 1 9 2 2 1 4 1 6 2v1l-4-1c-2-1-3-1-4-1h-1 1c0 1 1 1 1 2h0c-7-3-14-4-22-1-5 2-10 5-12 10-1-1-1-1-1-2v-2c0-2 1-2 2-3v-1l-2-1h-1v-1c0-1 0-3-1-4v-1l3-1 5-3z" class="f"></path><path d="M690 182c2 0 4-1 6 0-1 1-5 2-6 4v1c0 1-2 3-3 4v-2c0-2 1-2 2-3v-1l-2-1 3-2zm18-6h3l-3 1c2 0 2 0 4 1l2 1c-2-1-4-1-6 0-3 0-5 2-8 1 0 0 0-1-1-2l9-2z" class="X"></path><path d="M701 175h5l2 1-9 2c1 1 1 2 1 2-1 1-2 2-4 2-2-1-4 0-6 0 1-1 2-1 3-1-1-1-3-1-4-1 1-3 9-4 12-5z" class="f"></path><path d="M693 181c2-2 4-2 6-3 1 1 1 2 1 2-1 1-2 2-4 2-2-1-4 0-6 0 1-1 2-1 3-1z" class="W"></path><path d="M693 174c4-1 9-1 13-1-2 1-3 1-5 2-3 1-11 2-12 5 1 0 3 0 4 1-1 0-2 0-3 1l-3 2h-1v-1c0-1 0-3-1-4v-1l3-1 5-3z" class="L"></path><path d="M586 178c1-1 2-1 3-2s2-2 4-3l1-1v1c-1 1-1 1-2 1l-3 3 1 1-5 5 1 1h0c3-3 5-5 8-7v1l-2 2c-2 1-4 3-5 5l-2 2 1 1c-1 1-1 3-2 4v1 1l-1 3v4c0 2 0 4 1 6v1 2h1c0 2 1 4 3 5 1 1 2 3 3 4l1 1h0l1 1 2-2h0c1 3 4 4 7 4 2 1 4 1 6 1h0 1 4c-1 1-1 1-1 2-6 1-14 3-19 1v-1c-3-1-7-3-9-6v-2c0-1 0-1-1-2 0-1 0 0-1-1 0-2-1-3-2-5 0-1 0-1-1-2v-2c-1-2 0-2-1-4 0-2-1-4 0-6l1-4v-1c0-1 0-1 1-2l3-7c1-1 2-3 3-4z" class="B"></path><path d="M584 220h1c2 0 3 2 4 3 4 2 9 3 14 2l5-1h0 1 4c-1 1-1 1-1 2-6 1-14 3-19 1v-1c-3-1-7-3-9-6z" class="U"></path><path d="M693 205c2-2 4-4 8-4 2 1 2 2 4 3v1c1 4 1 11-2 15-2 5-6 9-9 13l-1-1-2-4c1-5 6-9 4-14 0-2 0-2-1-3l-2 1c-2 3-3 6-4 10l-1-3h-1v-7-1c1-2 1-3 2-5 1 1 3 0 4 0l1-1z" class="I"></path><path d="M688 206c1 1 3 0 4 0-4 4-6 8-5 13h-1v-7-1c1-2 1-3 2-5z" class="b"></path><path d="M688 193c2-5 7-8 12-10 8-3 15-2 22 1 1 2 2 4 3 5h-3l-2 1h0c-2-1-2-1-4-1l-1 1 1 1h0l1 4h1l2 2c2 2 4 5 6 8v1c-1-2-3-3-4-5-2-1-5-2-7-3h0-4-1c-2 1-2 2-3 3v2l-1-1-1-1 1 3-1 1v-1c-2-1-2-2-4-3-4 0-6 2-8 4l-1 1c-1 0-3 1-4 0h0-1l-1 1v-2c0-1 0-2-1-3 1 0 0-1 0-1 1-3 2-5 3-8z" class="C"></path><path d="M708 188h0l-3 1c-3 1-6 3-8 6-3 2-4 5-6 8h2v2l-1 1c-1 0-3 1-4 0h0c6-9 9-15 20-18z" class="W"></path><path d="M708 188c3-1 7-1 9 0 2 0 3 1 5 1l-2 1h0c-2-1-2-1-4-1l-1 1 1 1h0c-4-1-7 0-10 1-1 0-3 1-4 2l-1-1-1 1c-1 0-2 1-3 1 2-3 5-5 8-6l3-1h0z" class="F"></path><path d="M697 195c1 0 2-1 3-1l1-1 1 1c1-1 3-2 4-2 3-1 6-2 10-1l1 4h1l2 2c2 2 4 5 6 8v1c-1-2-3-3-4-5-2-1-5-2-7-3h0-4-1c-2 1-2 2-3 3v2l-1-1-1-1 1 3-1 1v-1c-2-1-2-2-4-3-4 0-6 2-8 4v-2h-2c2-3 3-6 6-8z" class="K"></path><path d="M693 203l2-2c3-1 7-1 9-2s3-2 4-2c1-1 1-2 3-1-1 0-1 0-1 1h5c2 1 6 2 7 4-2-1-5-2-7-3h0-4-1c-2 1-2 2-3 3v2l-1-1-1-1 1 3-1 1v-1c-2-1-2-2-4-3-4 0-6 2-8 4v-2z" class="b"></path><path d="M595 219c1-1 1-1 1-2-4-1-6-3-8-7s-3-9-1-14c1-5 5-11 10-13 2-1 6-2 9-1 2 0 4 3 6 4 9 7 17 16 22 26 1 4 2 8 3 11-2 1-3 1-5 0-8-4-15-2-24 1h0c-2 0-4 0-6-1-3 0-6-1-7-4z" class="O"></path><defs><linearGradient id="L" x1="801.266" y1="616.469" x2="313.486" y2="180.805" xlink:href="#B"><stop offset="0" stop-color="#060a0a"></stop><stop offset="1" stop-color="#1d100f"></stop></linearGradient></defs><path fill="url(#L)" d="M738 165c6-1 13-1 19-2l28-5 70-9c13-2 27-4 41-5h19c-23 11-54 22-63 48-2 5-2 10-1 15 2 9 7 16 10 25 7 19 1 43-18 53-7 3-16 4-24 2-4-2-7-4-10-7 2 0 7 0 9-2 2-1 2-4 2-5 0-2-1-3-2-5 2 0 2 1 3 2 3 3 1 9 7 9s10-1 14-5c5-4 8-10 8-17 0-6-3-12-7-16-2-3-5-5-8-6l-5-1v-1c2-1 5-1 7-1 3 1 6 2 8 4-6-7-12-12-22-13-9-1-20 2-27 8-8 7-14 18-19 26 12 7 19 17 23 30 5 13 4 28-2 40-10 19-29 30-47 38-5 2-11 4-15 7-2 2-3 4-4 7-1 1-1 4-3 5-4 4-8 6-8 12v1 1 3c0 1 0 2 1 3v4 4 1c0 1 0 1-1 3v2c0 1 0 2-1 3v2c-1 6-9 9-14 13-4 4-7 8-10 12l-3 7-3 12c-1 2-2 4-2 6h0 1c0-1 0 0 1-1l-5 15 9-6s2-1 3-1h3c3-2 7-3 11-4h3 0l2-1c2 1 4 0 6 2 1 1 4 2 6 2v1 1-1h-2l-1 1-1-1h-2v-1l-2 1h0c-3 1-6 1-9 2-6 2-12 6-17 10-5 3-10 5-13 9-3 2-4 5-6 8-2 2-4 5-5 8-3 5-5 12-7 18-4 11-8 22-11 33h1l1-3-2 14c0 2-1 5-1 7 1 0 2 1 3 2 0 1-2 3-3 4 0 2 1 6 1 8 2 7 8 14 14 17s13 3 19 1 12-6 15-11c1-4 2-8 1-11-1-2-2-5-4-5l-1-1c-2 1-2 3-2 4-3-2-4-5-6-8l1-1h8 2l3 3 1 2v-1c0-1 0-1-1-2v-1h0l3 2 1 1c0 2 1 2 1 3l1 1v1l1 1v1 1c1 1 1 1 1 2l1 2c0 1-1 4-1 5 1 1 1 3 1 4-2 11-10 20-19 26-10 6-22 10-32 15-3 2-8-2-10 1v1h-4c-2 0-8 7-9 8-1 3-3 6-2 9 6 6 12 10 12 20 0 5-2 10-5 14-3 3-7 5-12 6-2 0-5-2-7-3 5-1 11-2 14-7 2-3 2-7 1-10-1-4-4-7-7-9-5-3-9-3-14-2l-1 3c-2 0-4-2-6-2-2 3-3 7-4 11l-4 12c4 2 9 5 11 10v6h-1c-1-3-2-6-6-7-2-1-5-1-8-1l-50 141-21 61c-2 7-6 16-8 24l-19-53-40-105-18-46c-3-8-5-17-9-24-5 1-12 1-15 5 0 1-1 2-1 3h-1c0-3 0-6 2-8 3-4 8-5 11-7-3-8-5-19-14-23-5-2-11-2-16 0-4 1-8 5-10 9-1 3-1 6 0 9 2 5 5 8 10 9 2 1 3 1 4 1h1c-4 2-8 3-11 2-5-2-8-4-10-8-3-5-3-11-2-16 2-6 7-11 13-14h0l5-3-3-6-2-1-1 1-1-1 1-1v-6-1h-9l-7-1v-2h-7c-3 0-7-2-10-3-8-4-17-7-25-12-3-1-6-4-9-7-6-6-11-14-10-23v-1c0-1 0-1 1-2l1 1v-2c1 1 1 0 1 2-1 2 0 3 1 5 1 1 0 1 1 2l3 6c0 1 1 1 1 2v1l1 1 1 1h0-1l-1 1c2 2 6 6 9 7l1 1c0 1 1 1 2 2h0c1 1 2 1 3 2 4 0 6 1 10 1h0 0c8 2 17-4 23-8v-1l3-3c4-5 6-9 9-15 1-5 1-10 1-15 0-1-1-2-1-2v-1c0-1-1-2-1-3l-1-1v-1c0-2-1-2-1-3h-1l1 3h-1-1v-3l-1-2v-3c-1-1-1-1-1-2l3 1h0c-3-12-8-24-13-35-2-6-4-13-7-19l-6-9c-2-3-5-8-8-11-4-4-9-7-14-10-8-5-15-9-23-11h-6v-1h2v-1h-4c-1 0-2 0-3 1h-2-1c-2 1-3 0-5 0l6-3c5-2 10-2 16-1l3 1 6 2 9 3c2 1 4 3 6 3h1c1 0 2 1 4 1h0l1-2c-1-2-2-5-2-7l-6-18c-1-3-1-6-3-9-1-2-1-3-1-4-6-9-12-15-22-19-1-1-6-1-8-2 2-7 1-15 2-22v-5c-1-4-2-7-4-10s-4-5-6-8c-1-3-2-5-4-7-4-4-10-5-15-8-15-7-31-16-41-29-8-11-12-23-11-37 2-10 5-19 10-27 3-5 9-13 15-14v-1c0-2-2-4-3-6-5-8-11-17-19-22s-18-7-28-5c-8 2-15 8-19 15 3-3 6-6 11-7 2 0 5 0 6 1v1c-2 0-4 1-5 2-7 3-12 9-14 15s0 13 2 17c3 5 8 9 13 11 14 3 7-6 15-11h1c-1 1-2 3-2 4 0 2 0 4 1 5 2 3 7 3 10 3-2 2-4 4-7 5-7 4-15 4-22 1-10-3-18-11-22-20-6-11-5-26 0-36 2-7 6-13 8-19 2-4 2-8 2-12v-1c-1-3-1-5-2-7-10-25-40-36-62-46 2-2 8-1 11-1 18 1 35 3 52 6l69 9 20 3c6 1 13 1 19 2-3 1-6 1-9 0h-3 0-5 1 1 1c1 0 1 1 2 1h2 1 0c2 0 3 1 4 2l-9-1c2 2 4 2 7 3 2 1 5 1 7 1 0 2 0 1 1 2 0 1 0 2 1 2h-1c-1 0 0 0-1-1l-1 1 2 2h0v3l1 2c0 1 1 2 1 3v1c1 2 1 2 1 4v3c8-5 14-8 23-9l-2-1 6-2c2 0 5 0 7 1 9 1 19 7 25 13 5 6 8 11 10 18 1 4 1 9 3 13 2 5 6 9 8 14v3l7 8c1-1 2-1 3-2l24-15h1c2-2 3-4 5-6l1 1c1-1 2-2 2-3 4-2 9-3 13-2h1c2 1 3 0 5 2h-1 0c-5 1-9 3-13 7l-1 3h0c1 3 3 4 4 7s2 6 3 10c-2-3-4-6-6-8l3 9 8 20c3 5 5 11 7 17-1 0-2 1-2 2h1c1 0 1 1 2 1 2 0 3-1 5-1-1 1-1 3-1 4 2 1 3 1 4 1h3v1c2 0 2 0 4 2h1c2-1 2-3 3-5l2 5c-1 1-2 1-4 1-3 5-5 12-7 17 4 11 9 20 13 30l29 75 31 82c9 26 18 52 29 76 4 9 8 18 14 26 1 2 4 6 7 7 1 1 3 1 5 1 5-2 8-10 10-15 11-24 18-51 26-76l22-80c5-21 8-41 11-62 2 0 3-1 4-3 2-2 4-3 5-5 0-2 1-2 0-4h-2-1 7 0l1-2c1 0 3 0 4-1 2-2 3-5 3-7s1-3 1-4h1 0c3-4 4-10 6-15l1-7c0-7 2-15 3-23l3-33 1-1h-1v-2c-1-2 0-3-1-5s0-2-1-4c0-2 0-5 1-6h0v-4-2c2-1 2-2 2-4l2-2c2-3 4-4 6-6v-1-1h1l1-1s1 0 1-1c1 0 1-1 2-1 1-1 2 0 3 0h1 0c-1-2-3-2-5-3-1-1-6-5-7-7v-2l-1-1-2-4v-1-1-2c-1-2-1-5-1-7v-6c2 1 0 9 1 11v1c2 8 6 15 13 19l2 1 8 5c-2-2-5-4-6-6l-2-1c3-4 7-8 9-13 3-4 3-11 2-15l1-1-1-3 1 1 1 1v-2c1-1 1-2 3-3h1 4 0c2 1 5 2 7 3 1 2 3 3 4 5v-1c-2-3-4-6-6-8l-2-2h-1l-1-4h0l-1-1 1-1c2 0 2 0 4 1h0l2-1h3c-1-1-2-3-3-5h0c0-1-1-1-1-2h-1 1c1 0 2 0 4 1l4 1v-1c-2-1-4-1-6-2-3-1-6-2-9-2l-2-1c-2-1-2-1-4-1l3-1h5c3-1 6 0 9-2 1 0 6-1 6-2l-1-1h4c2 0 3 0 5-1-3-1-6-1-10-1 3-1 6-1 8-2s4-1 5-2h0-4z"></path><path d="M438 606l1 1v3 3h-1v-7z" class="P"></path><path d="M524 610c1-1 2 0 3 0 0 1-1 2-1 3-1 1-1 1-2 1l-1-3 1-1z" class="H"></path><path d="M377 650l5 1h0-1l3 2-7-1v-2z" class="B"></path><path d="M510 561v5l1 7v1h-1c0-2-1-4-1-7l1-5v-1z" class="D"></path><path d="M268 346l7 4-2 1-6-2 1-3z" class="U"></path><path d="M298 377h9v-2l1 1-1 2c-2 1-4 1-6 1l-3-2z" class="B"></path><path d="M448 484l-2-1c-1-1-2-2-3-4l1-1 6 4-2 2z" class="M"></path><path d="M800 179c-1 1-2 3-4 4l-1-1-1-1 2-2c1 0 1-1 3-2 0 1 0 1 1 2z" class="F"></path><path d="M275 350l2 1c2 0 3 1 4 2v1c-2 0-5-1-7-2 0 0-1 0-1-1l2-1z" class="H"></path><path d="M371 281h0c-1-1-1-2-1-4s0-3 2-4l1 10c-1-1-2-1-2-2z" class="Q"></path><path d="M508 556h1c1 2 1 3 1 5v1l-1 5c-1-4-2-7-1-11z" class="B"></path><path d="M561 716h1v4l-4 9c0-5 2-9 3-13z" class="C"></path><path d="M730 363l7-3c2 1 4 1 6 1-2 1-4 2-6 2s-5 1-7 0z" class="Z"></path><path d="M482 519c-1-2-2-5-2-7h2c2 1 2 3 3 5l-3 2z" class="P"></path><path d="M743 361l2-1c0 2-1 2-2 3-3 2-6 2-9 2l3-2c2 0 4-1 6-2z" class="B"></path><path d="M770 268c2 1 4 1 5 2l-2 2c-4-2-8-2-12-2 1 0 3 0 4-1h2 3v-1z" class="d"></path><path d="M632 629h0c-1 0-2 0-3-1-1 0 0-1-1-2l1-1h5c0 2 0 2 1 3h1c-1 0-3 0-4 1z" class="D"></path><path d="M692 450c2 0 3-1 4-2l-3 7c-1 0-2 1-2 2-1 1-2 1-2 2l3-9z" class="U"></path><path d="M417 537l4 1v1c4 2 8 7 10 11h-1c-2-1-4-5-5-6-2-3-5-5-8-7z" class="B"></path><path d="M552 766c0-2 1-5 2-7 0 1 0 3 1 5l-3 8c-1-2 0-4 0-6zM441 512c3 2 10 5 10 8h-2 0c-2-3-1-1-4-2-1-1-2-3-3-4s-1-1-1-2z" class="G"></path><path d="M304 385c-3-4-7-7-10-11 1 1 3 2 4 3l3 2 4 1c0 2 0 3-1 5z" class="V"></path><path d="M308 376c1 3 0 5-1 7 0 1-1 3-2 3l-1-1c1-2 1-3 1-5l-4-1c2 0 4 0 6-1l1-2zm437-16c1 0 3-1 3-1l10-3c-3 4-11 6-15 7 1-1 2-1 2-3z" class="H"></path><path d="M633 637h0c-2 1-2 1-3 0 1 0 1-1 2-1 0-2-2-1-3-3l1-1h5v1h2l1 1v1h0c-1 0-2 0-2 1h-1c-1 0-2 1-2 1z" class="B"></path><path d="M557 750c1-4 3-9 4-13l1 3h1v-1l-3 11h-3z" class="J"></path><path d="M717 382c4 1 8-2 12-4l-10 9h0c-2-1-2-3-2-5h0z" class="U"></path><path d="M450 482c5 3 8 8 11 13-4-4-8-8-13-11l2-2z" class="R"></path><path d="M448 431l3 7c-1 1-2 1-3 2-1-1-2-3-2-5v-2l2-2zm109 319h3l-5 14c-1-2-1-4-1-5 0-2 3-8 3-9z" class="K"></path><path d="M454 654v3c0 1-1 2-1 3-1 2-1 3-2 5v8h-1l-1-1c0-6 2-13 5-18z" class="F"></path><path d="M775 270c5 3 13 8 15 14h0c-5-6-10-9-17-12l2-2z" class="Z"></path><path d="M770 259c4 1 9 4 12 7h0v1l-3-2-2-2h-1 0c-1 0-3 0-5 1 0-1-1-1-1-1-1-1-1-2-2-3h-1l3-1z" class="i"></path><path d="M448 568l13 8h-2l-15-6 4-2z" class="J"></path><path d="M471 741c2 5 5 11 6 16v1c-1-1-1-2-2-3v-3c-1-1-1-2-2-3 0 2 2 6 2 7l-1 1c-2-5-4-11-5-16 1 0 1 1 2 2 0-1 0-1-1-1l1-1z" class="L"></path><path d="M505 545l-1-1c2-2 2-3 2-6 1 1 2 3 3 3h4v1l-1 1s-1 1-1 2 0 2 1 3c-1 1-1 2-1 3-2-1-2-3-2-4-1-1-1-1-1-2s2-1 3-2c-2-1-2 0-4 0 0 1-1 1-2 2z" class="d"></path><path d="M771 264c2-1 4-1 5-1h0 1l2 2 3 2v-1c2 1 3 2 4 3h-1v1c-1-1-2-2-3-2l-1 1 4 3h-1s0 1 0 0c-2-1-5-2-6-4s-4-3-7-4z" class="F"></path><path d="M509 760c3-1 5 0 7 1 1 1 4 3 5 4-1 1-1 2-2 3l-10-8z" class="h"></path><path d="M503 778c1-2 4-4 7-4 4-1 7 0 10 2-6-1-10 0-16 4h0c0-1 1-2 1-2h1l-1-1c0 1-1 1-2 1h0z" class="X"></path><path d="M451 665l1 1 2-2-1 7 1 1h0c0 2 0 6-1 7l-1-1c0-1 0-1-1-2v-1-1h-2v-2l1 1h1v-8z" class="C"></path><path d="M505 545c1-1 2-1 2-2 2 0 2-1 4 0-1 1-3 1-3 2s0 1 1 2c0 1 0 3 2 4 0-1 0-2 1-3v1c0 2 0 4-2 5v1l-1-2v3h-1c0-4-1-8-3-11z" class="i"></path><path d="M440 466v2c-3 3-10 7-10 12 0 4 1 6 3 9h-1c-1 0-2-1-3-1-1-2-1-4-1-6 2-7 7-11 12-16z" class="G"></path><path d="M842 219c6 5 10 12 13 19v1c-1-1-1-1-1-2l-1-1h0 0v2c1 1 0 1 0 2-2-7-7-15-11-21z" class="F"></path><path d="M478 472c1-2 2-2 3-3l2-1h2v1l-3 3v8 1c-1 2 0 5-1 7h0v-1c0-2 0-4-1-5l1-1v-1-1c-1-1-1-1-1-2v-1-1h-1c0-2 0-2-1-3z" class="B"></path><path d="M796 310c1-4 1-8 1-12v-6l-1-1v-5c1 1 1 2 1 3l1 4c0 4 1 8 0 12 0 2 1 3 0 6v1c-1 2-2 5-3 7l-1-1v-1c1-2 2-4 2-7z" class="I"></path><path d="M393 525l7 17v4c-1-1-2-3-2-5-3-5-5-11-7-16h2z" class="G"></path><path d="M854 247h0c1 3 1 5 1 7v8c1 3 0 4-1 7-1 0-1 1-2 2l-1 2h-1 0c0-2 1-3 2-5v-1-2h1v-1-2c1-4 0-8 1-12v-3z" class="I"></path><path d="M446 675v-2h1 0c3 5 6 12 6 18l-1 1-6-17z" class="K"></path><path d="M337 266c2 1 4 2 6 4 3 2 6 4 8 7v1h-1c-1-1-1-1-2 0-3-2-8-4-11-7 2 0 3 0 4 1h1v-2c-2 0-4-3-5-4z" class="F"></path><path d="M549 768c1-1 1-1 1-2h2c0 2-1 4 0 6v2c-1 5-3 9-4 14v-2c1-6 0-12 1-18z" class="N"></path><path d="M396 662c1 1 1 1 3 1h2l1 1h-1c0 2-1 4 1 6 3 0 6 0 8 2-3 0-7-1-11 0h-2c-2-1-2-1-3-1l5-3-3-6z" class="B"></path><path d="M440 466l4-6c-4 3-7 6-11 9 2-3 3-5 5-7 2-1 5-3 8-4v1c0 3-4 9-6 12h0v-3-2z" class="J"></path><defs><linearGradient id="M" x1="742.486" y1="361.658" x2="756.014" y2="352.342" xlink:href="#B"><stop offset="0" stop-color="#5a595b"></stop><stop offset="1" stop-color="#878788"></stop></linearGradient></defs><path fill="url(#M)" d="M737 360l21-7v1l1 1h0l-1 1-10 3s-2 1-3 1l-2 1c-2 0-4 0-6-1z"></path><path d="M418 476h1l1 1-1 3c0 3 1 9 2 12 1 4 2 6 2 9l-2-1h0c0-2 0-3-1-5 0-3-2-7-3-9 0-3-1-5 0-7s1-2 1-3z" class="P"></path><path d="M799 177c4-4 9-8 15-11 3-1 6-1 9-2l-3 2c-7 2-15 8-20 13-1-1-1-1-1-2z" class="Q"></path><path d="M566 693v-3c1-2 0-5 1-6 1-2 2-3 2-5v6c2 5 0 13-3 17h0v-1-8z" class="B"></path><path d="M438 513c2 1 4 4 6 6 1 1 2 2 4 3 0-1 0-1 1-2h0 2l1 3c1 4 2 7 4 11-2-2-4-5-6-7-4-5-9-9-12-14z" class="R"></path><path d="M449 520h2l1 3h-1l-2-1v-2z" class="J"></path><path d="M561 716c1-4 0-8 1-12 1-2 1-6 2-8 0-2 0-3 1-5l1 2v8 1l-2 7-2 7h-1z" class="Q"></path><path d="M438 513h-1c-3-4-6-9-8-14 2 3 4 6 7 8 1 2 3 3 5 5 0 1 0 1 1 2s2 3 3 4c3 1 2-1 4 2-1 1-1 1-1 2-2-1-3-2-4-3-2-2-4-5-6-6zm7-39c0-5 1-9 2-13l2 9 1 4c1 2 1 3 1 5-1 0-2 0-3-1-2-1-2-2-3-4z" class="N"></path><path d="M446 474c1-2 2-3 3-4l1 4-3 1-1-1z" class="E"></path><path d="M716 191h0c6 3 10 6 12 12l-2 2c-2-3-4-6-6-8l-2-2h-1l-1-4z" class="R"></path><path d="M717 382c-1-3 0-5 1-7 3-7 6-10 12-12 2 1 5 0 7 0l-3 2c-6 1-11 3-14 8-2 3-2 6-3 9h0z" class="V"></path><path d="M594 595c0-1-1-2-1-3s2-4 3-5c1-3 1-7 1-11l1 1c1 5 2 12 0 17h0l-1-1c-1 1-2 1-3 2z" class="K"></path><path d="M579 650c0-2-2-5-3-7-2-5-6-10-8-16 3 4 6 7 8 12 1 1 3 5 5 6 1 0 2-1 3-2v-1c0 3-2 5-3 7-1 1-1 3-1 5l-1-1v-3z" class="X"></path><path d="M726 205l2-2 1 4c1 7 0 15-2 22-1 5-2 11-4 16h0l1-5 2-7c0-2 0-3 1-5 0-7 1-13-1-20v-2-1z" class="R"></path><path d="M726 205l2-2 1 4v2c-1 0-2-1-3-1v-2-1z" class="G"></path><path d="M792 310v1c-1 7-6 13-11 18l-3 3v2c0 1 0 2-1 2l-3 3c0-1-1-1-1-2l1-1v-2c0-1 2-3 4-4 6-6 11-11 14-20z" class="U"></path><path d="M521 707c-1-2-3-3-3-4 3 1 4 3 6 4 1 1 2 1 2 1 1 2 2 2 3 2 2 5 4 8 4 13 0-1-3-4-3-5-2-4-6-7-9-11z" class="R"></path><path d="M439 610h1c1 3 2 6 2 9v4c1 2 1 5 2 7 0 2 1 3 0 5v-1l-1-1v-2-3l-1-1v-1c0-1-1-1-1-2-1 1-1 1-1 2h0v1c1 2 0 0 0 2 1 1 0 1 1 1v1 1 3c-2-7-3-15-3-22h1v-3z" class="E"></path><path d="M503 778h0c1 0 2 0 2-1l1 1h-1s-1 1-1 2h0c-3 7-3 14-3 21 0 3 1 5 0 7-1-6-2-13-2-20 0-3 1-7 4-10z" class="L"></path><path d="M770 189c2-3 7-9 6-13v-1h1c2 4 1 8 0 12-1 2-3 5-5 7v-1-1c0-1 0-1-1-2l-1 1c0 1-1 1-1 2l-1-1 2-3z" class="N"></path><path d="M498 719l2-2c2-4 7-9 11-9 3-1 5 0 7 2-3-1-5-1-8 0h-1c-5 2-7 7-9 12-1 2-2 5-2 7h-1l-1-2c1-3 1-5 2-8z" class="L"></path><path d="M513 549c3-4 7-7 11-10-1 2-4 4-5 7-3 7-3 15-4 22l-1-1v-6c1-2 1-4 1-6-1-2-1-4-2-6z" class="E"></path><path d="M463 654c3-2 7-1 11-1 2 1 4 2 6 4-8-1-15-1-21 4h-1v-1c1-1 2-2 3-2l1-1c1-1 1-2 1-3z" class="R"></path><path d="M417 529c4 1 8 2 11 4l1 1c-4-1-11-3-15-1-3 3-6 7-8 11-1-2 1-6 2-8 3-4 4-6 9-7z" class="K"></path><path d="M692 450c2-3 4-6 6-10l1 1h0c6-8 15-10 21-18-1 6-9 9-14 13-4 4-7 8-10 12-1 1-2 2-4 2z" class="a"></path><path d="M774 339c-8 6-17 9-26 12 2-2 5-3 8-4 7-4 12-8 18-13v2l-1 1c0 1 1 1 1 2z" class="g"></path><path d="M553 703c0-4 1-9 1-14h0l-1-1h0v-4l-1-2v-2l2 4v1c1 2 3 3 4 4l1 1c0 4-3 9-5 13h-1zm-85-187c1 0 2 4 3 5 2 2 2 3 3 4 2 5 3 10 5 15 1 4 3 7 4 11v-1c-2-2-4-7-5-10l-7-15c-2-3-3-6-3-9z" class="E"></path><path d="M373 627c2-1 3-1 5 0-8 7-17 10-28 9-3 0-6-1-10-2 4 0 6 1 10 1h0 0c8 2 17-4 23-8z" class="g"></path><path d="M711 476h3c-2 1-2 1-3 1v1c-2 1-5 1-7 2s-3 3-5 4-5 2-7 3c-3 2-6 4-9 4 1-2 3-4 6-5l7-4c2 0 3-1 4-2 3-2 7-3 11-4z" class="H"></path><path d="M404 555l1 1c2 6 4 13 6 19l5 13c0 2 1 3 1 6l-1-1-10-26c-1-4-2-8-2-12z" class="M"></path><path d="M391 605c3 5 5 11 6 17 0 1-1 1-2 2h-1 0c0-1 0-2-1-3h-2c-1-1 0-14 0-16z" class="c"></path><defs><linearGradient id="N" x1="265.405" y1="342.779" x2="257.773" y2="344.188" xlink:href="#B"><stop offset="0" stop-color="#676668"></stop><stop offset="1" stop-color="#848382"></stop></linearGradient></defs><path fill="url(#N)" d="M245 333l1-1v1c3 2 6 5 9 5h1l12 8-1 3c-9-5-15-9-22-16z"></path><defs><linearGradient id="O" x1="292.627" y1="368.391" x2="281.377" y2="355.543" xlink:href="#B"><stop offset="0" stop-color="#3a3939"></stop><stop offset="1" stop-color="#585858"></stop></linearGradient></defs><path fill="url(#O)" d="M280 358l12 4c5 2 9 4 12 8v1-1c-4-3-8-3-13-5l-16-5h0c2 0 3-1 5-2z"></path><path d="M630 671v1c-3 0-7 0-9-1s-1-1-2 0c2 3 4 2 8 3l6 3c1 0 2 1 2 2-3-2-6-3-10-4-1 0-3 0-3-1-2-1-4-3-6-5 3 0 5-1 7-2h1c3-1 4-2 6-4h1-1c0 2-1 2-1 3v1h1v4z" class="Z"></path><path d="M724 229v4 1c-1 1 0 2 0 3v-1-2c1 0 0-1 1-2v-1-1c0 1 1 2 1 3l-2 7-1 5c-1 1-1 2-1 3l-1 1v2l-3 11-1-1c0-1 0-2 1-4h-1c1-6 2-12 4-17 1-3 2-5 2-7l1-4z" class="I"></path><path d="M512 549h1c1 2 1 4 2 6 0 2 0 4-1 6v6l1 1c1 1 1 3 1 4-1 3 0 6-1 9-1-4 0-9-2-13-1-1-1-3-1-5-1-3-1-6-2-8v-1c2-1 2-3 2-5z" class="Q"></path><path d="M421 593l3 1 2-1h2c-6 4-14 10-17 15 0 2-1 3-2 4l2-10 10-9z" class="J"></path><defs><linearGradient id="P" x1="787.148" y1="327.584" x2="778.322" y2="329.798" xlink:href="#B"><stop offset="0" stop-color="#353537"></stop><stop offset="1" stop-color="#565454"></stop></linearGradient></defs><path fill="url(#P)" d="M792 310v-1-2l1-1c1 1 0 3 0 5v1s0 1-1 1v1 1h0c-1 3-2 4-3 6 0 1-1 1-1 2s-1 1 0 2c1-2 1-2 2-3l1-1 1-3c1-1 2-4 3-6h0c0-1 1-1 1-2 0 3-1 5-2 7-5 8-10 14-17 19 1 0 1-1 1-2v-2l3-3c5-5 10-11 11-18v-1z"></path><path d="M558 689h2c0-3 0-5-1-7 0-2 1-2 0-4h0 0c1 0 1 0 1 1v2c1 1 0 2 1 3 2 4-1 12-1 15l-2 10v-4c1-1 0-2 0-3v1h0c0 1 0 1-1 2-1-1 0-2 0-3h0v-2c0 2-2 4-2 5v1 2c0 1-1 2-1 3h-1 0c0-2-1-5 0-7v-1h1c2-4 5-9 5-13l-1-1z" class="C"></path><path d="M303 301h3 0c0 1 1 2 1 3 1 5 0 10-3 14v1c-3 4-6 7-10 9h-1c3-3 6-5 8-8 2-5 4-11 3-16-1-2-1-2-1-3z" class="H"></path><path d="M455 540c2 0 2 0 3 1 0 4 12 14 15 18 2 2 4 5 6 7s4 5 6 7h-1l-29-29h2c0-3-1-3-2-4z" class="J"></path><path d="M520 563l1 1 3 6h2 1v1c-3 3-5 7-6 11l-2 6c-1-5 0-10 0-15 0-3 0-7 1-10z" class="Q"></path><path d="M758 353c7-3 13-7 20-11 3-3 6-6 9-8-4 8-12 14-20 18-2 1-5 3-8 3h0 0l-1-1v-1z" class="e"></path><path d="M451 438l1 1c6-4 8-8 12-13-1-4-2-6-4-10 2 1 2 2 3 3l3 6c-4 4-9 10-9 15l-2 2c-5 0-10 3-13 6l-2-2c3 0 6-4 8-5l1-1h-1c1-1 2-1 3-2z" class="N"></path><path d="M478 472l-1-2c1-1 1-1 2-1 1-1 2-2 3-2l1-1 3-2h1l5-4h1c-1 1-4 3-5 5 0 1 0 3-1 4-2 7-2 12-3 19-1-2-1-4-2-7v-1-8l3-3v-1h-2l-2 1c-1 1-2 1-3 3z" class="P"></path><path d="M409 526c2 0 3 1 5 2 1 0 2 1 3 1-5 1-6 3-9 7-1 2-3 6-2 8-2 5-6 9-9 13 1-3 3-6 3-8l1-3c2-1 2-3 3-4v-2l3-6c0-1 1-2 1-3v-2c1-2 1-1 0-2l1-1z" class="N"></path><path d="M440 471h0c-1 3-3 7-1 11 4 8 14 14 22 17-6-1-12-2-17-6-3-3-6-7-7-11l-1-4c1-3 2-5 4-7zm-19 29l2 1 13 19-1 1v2l1 1c-5-4-9-10-13-16 0-1-1-3-1-5l-1-3z" class="G"></path><path d="M716 230h1l1 1c1-2 1-3 3-4 0 0 1 0 1 1l1 1 1-1v1l-1 4c0 2-1 4-2 7-2 5-3 11-4 17v-7h1c-1-2 0-4 0-6v-8h-1 0l-1-6z" class="E"></path><path d="M716 230h1l1 1c1-2 1-3 3-4 0 0 1 0 1 1l1 1 1-1v1l-1 4-4 1v2l-1-1-1-1v2l-1-6z" class="S"></path><path fill="#e99593" d="M519 768c1-1 1-2 2-3l10 8c2 2 5 4 8 4l5-1c-2 1-4 3-6 5l1 7h0l-1-3c-2-7-13-12-19-17z"></path><path d="M629 625v-1l8-20 2 18-1 1-3-3c-1 2 0 4-1 5h-5z" class="c"></path><path d="M231 288c3-7 8-13 16-16h1 4l-4 2c-7 3-13 9-16 17-1 3 0 8-1 11h0l-1 4c-1-2-1-10 0-12l1-6z" class="H"></path><path d="M403 661s0-1 1-2h1l1 2v2c1 2 11 8 11 9h0-7c-2-2-5-2-8-2-2-2-1-4-1-6h1l1 1v-4z" class="V"></path><path d="M403 661c2 2 3 2 3 5-1 0-2 0-2-1h-1v-4z" class="H"></path><path d="M337 271c-2-3-7-6-10-9-1-2-2-5-3-7l-1-1 1-1c0 1 1 2 1 3l1 1v-1-1h1l1 1h0 1l4 5c1 0 2 1 3 2 1 0 2 1 3 2-1 1-2-1-3-1l-1 1 2 1c1 1 3 4 5 4v2h-1c-1-1-2-1-4-1z" class="C"></path><path d="M290 274c2 0 4 0 5 1 1 0 3 1 3 2 1 3 2 4 2 6 3 5 3 9 3 14h1c0-5 0-8-1-12l-1-2 1-1c3 5 2 10 2 16-1 0-1 0-2 1-3-2-1-6-2-9l-5-10-6-6z" class="H"></path><path d="M847 207h0c2 3 2 7 4 10 2 4 4 9 6 14 4 8 4 18 2 27l-1 4h0 0c1-6 1-14 0-20-1-5-2-9-4-13-2-7-8-15-7-22z" class="Y"></path><path d="M444 570c-6-4-13-11-18-17l-6-8c2 1 4 3 6 4l9 9 13 10-4 2z" class="G"></path><path d="M319 259c-4-6-9-17-7-24 1-4 2-5 5-7l4 10c1 1 1 2 2 3 0 1 1 2 0 3-2-4-3-9-6-12-1 1-2 2-2 3-3 5 0 14 2 18 4 8 7 14 14 19l-1 1c-5-5-8-9-11-14z" class="X"></path><path d="M436 520l19 20c1 1 2 1 2 4h-2l-19-20-1-1v-2l1-1z" class="L"></path><path d="M632 629c1-1 3-1 4-1s2 0 3 1c2 0 5-1 6 0 2 0 4 2 5 4l2 1c-3 0-6 0-8 1h-2l-4 1h-1c-1 1-3 1-4 1 0 0 1-1 2-1h1c0-1 1-1 2-1h0v-1l-1-1h-2v-1h1v-1l-4-2h0z" class="H"></path><path d="M395 651h1c5-1 8-3 12-7l7 18h0c-2-1-3-2-5-2l-3-4c-3-4-6-4-12-5z" class="c"></path><path d="M322 479l9 3c2 1 4 3 6 3h1c1 0 2 1 4 1h0l1-2c2 2 2 4 3 6 1 0 2 0 3 1l1 6c-5-4-10-7-16-10-3-2-6-3-9-5-1-1-2-1-3-3z" class="U"></path><path d="M343 484c2 2 2 4 3 6h0c-2 0-3-2-4-4h0l1-2z" class="Z"></path><path d="M386 593c2 9 2 19-3 28-1 2-3 4-5 6-2-1-3-1-5 0v-1l3-3c4-5 6-9 9-15 1-5 1-10 1-15z" class="e"></path><path d="M385 608c0 3 0 4-1 7-1 1-1 4-3 5h-1-1v1l-1 1s-1 1-2 1c4-5 6-9 9-15z" class="a"></path><path d="M418 560h1l1 1h1c1 1 2 3 3 4s1 3 2 4l2 2c0 1 0 1 1 2 1 2 1 3 1 6h2c-1 2-2 4-4 6h0c0-1-1-1-1-2l-3-6c1-2 0-2 0-4l-6-13z" class="D"></path><path d="M418 560h1l1 1 4 6v1l1 1c0 1 1 2 2 3 1 2 1 3 2 4-2-1-3-1-5-3l-6-13z" class="B"></path><path d="M331 272c13 10 29 19 45 20h0c-3 2-5 0-8 0-2 1-6 0-8-1l-6-2c-1-1-2-1-3 0l-16-12c0-1-4-3-5-4l1-1zm158 389h4v1h-2c-3 0-7 1-10 3-6 5-7 20-8 27 0 2 1 3 0 4-1-4 0-9 0-13 0-5-1-13 2-18 3-3 10-3 14-4z" class="L"></path><path d="M717 303l2-2h2c0 1 1 2 1 2h0c-1 4-1 7-1 10 1 7 7 13 13 17-2 0-4-1-6-2-4-3-7-7-9-10-3-5-4-10-2-15z" class="H"></path><path d="M715 198c2 1 5 2 7 3 1 2 3 3 4 5v2c2 7 1 13 1 20-1 2-1 3-1 5 0-1-1-2-1-3v1 1c-1 1 0 2-1 2v2 1c0-1-1-2 0-3v-1-4-1-10c0-7-2-12-6-17-2-1-3-1-3-3z" class="B"></path><path d="M426 527c1 1 2 1 4 0l29 29v3l-22-20c-3-4-6-7-9-10h0l-2-2z" class="L"></path><path d="M452 692l1-1 13 34c1 5 4 11 5 16l-1 1c1 0 1 0 1 1-1-1-1-2-2-2l-17-49z" class="J"></path><defs><linearGradient id="Q" x1="749.571" y1="272.731" x2="748.169" y2="268.445" xlink:href="#B"><stop offset="0" stop-color="#9c5a59"></stop><stop offset="1" stop-color="#b16f70"></stop></linearGradient></defs><path fill="url(#Q)" d="M733 275l6-4c10-5 21-6 31-3v1h-3-2c-1 1-3 1-4 1-11 0-21 2-29 10v-1c0-1 1-3 1-4z"></path><path d="M717 303c0-1 1-2 1-4l-2-1h2c1-1 1-3 2-4h0v2c1-1 2-4 2-6h1c2-6 5-11 10-15 0 1-1 3-1 4v1c-3 4-7 9-7 14 1 2 2 4 2 5v1l-2-1c-1 2-2 3-3 4 0 0-1-1-1-2h-2l-2 2z" class="d"></path><path d="M369 640c8-1 18 1 26 3l4-2h0c-1 2-2 2-3 3s-4 3-5 3-3 0-4-1c-4 0-8-2-12-3l-10-2h-1l5-1z" class="O"></path><path d="M650 640c5-1 10 0 15 0-4 2-9 3-13 4s-7 2-11 3h-1c-4 0-8-2-11-5l1-1c2 0 4 3 6 3s6-2 8-3 4-1 6-1z" class="c"></path><defs><linearGradient id="R" x1="267.941" y1="346.84" x2="258.13" y2="355.243" xlink:href="#B"><stop offset="0" stop-color="#5b595c"></stop><stop offset="1" stop-color="#8c8b8a"></stop></linearGradient></defs><path fill="url(#R)" d="M275 360c-11-4-21-9-30-17-2-3-5-6-7-9 5 4 11 9 17 13 8 5 16 8 25 11-2 1-3 2-5 2h0z"></path><path d="M331 446c5 6 7 14 10 22l8 23c-1-1-2-1-3-1-1-2-1-4-3-6-1-2-2-5-2-7l-6-18c-1-3-1-6-3-9-1-2-1-3-1-4z" class="V"></path><path d="M631 663v-1c0-1 1-2 1-3 1 0 1-1 1-1v-1-2h-1l-1-1c1-1 2-1 4-1 1-1 1-1 2-1h2c1-1 3-1 5-1v1l2 1c-2 0-8 7-9 8-1 3-3 6-2 9-1 0-2-1-3-1l-2 2v-4h-1v-1c0-1 1-1 1-3h1z" class="D"></path><path d="M468 505c-1 4-3 9-4 13 0 5 2 10 4 15 3 6 7 14 11 20 1 3 4 6 5 9 2 3 5 6 6 9h-1c-4-5-7-11-10-16-6-8-11-16-15-25-2-5-4-10-2-15v-1l6-9z" class="L"></path><path d="M448 300c-3 0-6 1-9 1-4 0-9-1-13 0-2 1-5 3-7 4 4-4 7-10 11-13h1c1 0 1 1 2 1 2 0 3-1 5-1-1 1-1 3-1 4 2 1 3 1 4 1h3v1c2 0 2 0 4 2z" class="Y"></path><path d="M430 292h1c1 0 1 1 2 1 2 0 3-1 5-1-1 1-1 3-1 4 2 1 3 1 4 1h3v1c-2 0-4 1-6 0-2 0-4-1-6-1s-5 1-7 2h0c1-2 3-4 5-6v-1z" class="D"></path><path d="M402 471h1l3 2c2 2 4 3 7 3h5c0 1 0 1-1 3s0 4 0 7c1 2 3 6 3 9 1 2 1 3 1 5-8-9-12-20-19-29z" class="S"></path><path d="M413 476h5c0 1 0 1-1 3s0 4 0 7l-1-2c-1-2-2-3-3-5h0v-3z" class="E"></path><defs><linearGradient id="S" x1="431.873" y1="403.677" x2="450.127" y2="431.823" xlink:href="#B"><stop offset="0" stop-color="#aa1f24"></stop><stop offset="1" stop-color="#d02830"></stop></linearGradient></defs><path fill="url(#S)" d="M436 396l12 35-2 2v2l-7-17c-1-3-3-7-4-11 0-1-1-2-1-3l1-1c0-2 0-5 1-7z"></path><path d="M440 468v3c-2 2-3 4-4 7l1 4c1 4 4 8 7 11h-1-1-1l-3-3c-1 0-1 0-2-1-2-2-2-4-3-6l-1-1v-1h0v-1h-1c0 1 0 2 1 3 0 2 0 3 1 4s1 1 1 2l1 1h0c2 3 4 4 5 8-3-3-5-6-7-9s-3-5-3-9c0-5 7-9 10-12z" class="S"></path><defs><linearGradient id="T" x1="236.009" y1="322.004" x2="244.514" y2="318.589" xlink:href="#B"><stop offset="0" stop-color="#3a393a"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#T)" d="M230 306l1-4h0c2 6 3 12 7 17s8 8 12 12c2 2 4 4 6 7h-1c-3 0-6-3-9-5v-1l-1 1h0c-5-4-9-10-11-15-2-4-3-8-4-12z"></path><path d="M621 645c2 2 3 4 6 6l1 1h1l-2 2v6c-1 1-2 3-4 4l-4 1c-2 0-2 0-3-1 1-6 3-13 5-19z" class="O"></path><defs><linearGradient id="U" x1="272.212" y1="273.937" x2="274.885" y2="267.325" xlink:href="#B"><stop offset="0" stop-color="#945a58"></stop><stop offset="1" stop-color="#be7575"></stop></linearGradient></defs><path fill="url(#U)" d="M257 268c8-1 15-2 22 1 4 1 8 2 11 5l6 6 5 10c1 3-1 7 2 9l3 2h-3c0-1-2-2-3-3h-1v-1l1-1c1-3-1-7-2-10-4-8-11-12-19-15-8-2-16-1-23 0 0-1 0-2 1-3z"></path><path d="M393 653c0-1 0-1 1-2h1c6 1 9 1 12 5l3 4-4 1-1-2h-1c-1 1-1 2-1 2v4l-1-1-1-1h-2c-2 0-2 0-3-1l-2-1-1 1-1-1 1-1v-6-1z" class="D"></path><path d="M446 675c-3-9-27-69-25-72 1 1 1 3 2 5l4 11c7 18 15 36 20 54h0-1v2z" class="L"></path><path d="M459 556c18 18 36 37 47 59v1c-5-8-10-15-15-22-10-12-21-24-32-35v-3z" class="J"></path><path d="M722 184h0c0-1-1-1-1-2h-1 1c1 0 2 0 4 1h-1c6 8 11 13 19 17 4 3 8 3 12 3v1c-2 0-6 0-8-1h0v1h1l1 1c2 0 6 0 8-1 4-1 7-3 11-6v-1h1c-1 2-3 3-5 4-4 4-10 6-16 5-9-1-17-9-23-17-1-1-2-3-3-5z" class="R"></path><path d="M409 225c4-2 9-3 13-2h1c2 1 3 0 5 2h-1 0c-5 1-9 3-13 7l-1 3c0 1 0 1-1 2l-1-1h-2c-1-1-1-1-2-1h0l-1-1h-1c-2 3-5 4-8 6h0c4-4 7-8 10-12 1-1 2-2 2-3z" class="C"></path><path d="M409 225c4-2 9-3 13-2-2 0-4 0-6 1-4 2-8 6-11 10-2 3-5 4-8 6h0c4-4 7-8 10-12 1-1 2-2 2-3z" class="d"></path><path d="M456 453l2 1c3 3 4 8 6 12l7 19c1 3 3 6 4 9 0 1 0 1-1 2s-1 1-2 1c-2-4-4-8-5-12l-6-18c-2-4-5-10-5-14z" class="J"></path><path d="M584 642h0c-1-4-1-11 0-14l1-1h0c1 1 1 3 1 5-1 3-1 6-1 10v1 1h0v1l-1 1s-1 1-1 2v2c1-1 1-2 1-2 1 2-1 5-1 7 0 1 0 0-1 1v3c-1 3-1 7-1 10s0 5-1 9c-1 1 0 1 0 3l-2 7c-1-2 0-2-1-3v-6c1-5 3-10 2-14 0 0-1 0-1 1v3c-1 1-1 3-1 5l-1 5-1 5c0 1-1 1-1 2v1c0 1 0 1-1 1h0c0-3 1-7 2-9v-2-1-2h1v-2c0-1 0-2 1-3v-2c1-6 1-11 2-17v3l1 1c0-2 0-4 1-5 1-2 3-4 3-7z" class="C"></path><path d="M573 688h0c1 0 1 0 1-1v-1c0-1 1-1 1-2l1-5 1-5c0-2 0-4 1-5v-3c0-1 1-1 1-1 1 4-1 9-2 14v6c1 1 0 1 1 3l-6 14c0 1-1 6-2 7s-2 1-2 2c0 3-2 4-3 6l-1 3-1-1-1 1v-4l2-7 1 1v1l1-2 1-5v-1l4-9 1-3c0-1 0-2 1-3z" class="D"></path><path d="M563 719c1-2 3-6 3-8l1-1v-2l1-4c1-1 1-3 2-4v-1c1-1 1-2 2-2v1 4c0 1-1 6-2 7s-2 1-2 2c0 3-2 4-3 6l-1 3-1-1z" class="C"></path><path d="M323 343c-1-2-2-4-2-7-1-2-1-4-2-5-2-4-4-8-5-11l1-1 6 13c2-1 2-2 3-4l3-3v-2c1-4 0-6-2-9l-1-1 1-1 3 3v-2c1 1 1 1 3 1l2 2 1-1v1c1 3 2 6 2 9l-2 2h0c-2 0-2 0-4 1h0-1l-3 4v1h0c-2 3-3 3-4 6l2 3-1 1z" class="T"></path><path d="M334 316c1 3 2 6 2 9l-2 2h0c-2 0-2 0-4 1h0-1l-1-1c0-1 4-4 5-6-1-2-1-3 0-4 1 0 1 0 1-1z" class="P"></path><path d="M527 743c-1 1-2 2-4 2-3 1-6 1-9 0-4-2-7-6-9-10-1-5-1-10 1-14 2-3 4-6 7-7h1c2-1 4 0 6 0 3 2 5 3 7 5s3 4 4 6c-2-3-5-5-8-7l-1-1c-2-1-4-2-7-1-1 0-2 1-3 2h0c-1 1-1 2-2 2l1 1v-1c2 0 5-2 6-2 3 1 7 3 9 6h0l-4-2c-2-1-5-2-7-2-1 1-1 1-2 1l-1 1c-2 1-3 2-4 4-1 3-1 7 1 10s5 7 9 8c2 1 4 0 6 0 0-1 1-1 2-1l1-1v1z" class="X"></path><path d="M417 571c1 3 3 8 3 11l-1 1-1 1c-1-2-1-4-1-6-3-8-7-16-8-26 0-2-1-7 1-9 1 1 1 2 1 3 2 1 4 7 5 9 0 2 1 3 2 4v1l6 13c0 2 1 2 0 4-3-6-5-13-9-19 0 3 1 6 3 7l1 2h-1-1c-1 1 0 2 0 4z" class="Q"></path><path d="M417 571c-2-3-3-5-4-8l-1-4v-3h0c-1-1-1-1-1-2v-1c0-2 0-2 1-3 0 0 0 1 1 1v1 2l2 4c0 3 1 6 3 7l1 2h-1-1c-1 1 0 2 0 4z" class="B"></path><path d="M484 721c0-5 1-10 4-14 4-5 9-9 15-10h1c3 0 5 0 8 1h1 1c7 2 11 6 15 12-1 0-2 0-3-2 0 0-1 0-2-1-2-1-3-3-6-4 0 1 2 2 3 4-2-1-4-3-6-4-3-1-7-1-10 0l-1-2-2 1c-4 0-7 2-10 4-4 4-6 9-7 15h-1z" class="G"></path><path d="M429 488c1 0 2 1 3 1h1c2 3 4 6 7 9 3 2 7 4 11 4 8 1 14 0 21-3 2-1 5-1 7-3v-1l1 1c-1 2-4 4-7 5-9 4-18 9-27 5-8-3-14-10-17-18z" class="J"></path><path d="M733 258c2-3 4-5 7-7v-1c1 1 3 2 5 3h1 4c3 1 11 0 13 1-1 1-9 1-10 1l-1 2c1 0 2 0 3-1l1 1h0c-1 0-2 0-3 1-1 0-3 0-4 1-2 0-5 1-7 2s-5 2-7 4c-1 0-2 1-3 1h-1-1l-1 1h0c1-3 2-6 4-9z" class="i"></path><path d="M733 258h1 1c0-1 1-1 2-2h0c2-1 4-1 6-1h0l-1 1c-2 1-5 2-6 5v1l-5 4h-1l-1 1h0c1-3 2-6 4-9z" class="F"></path><path d="M736 262c2-2 5-3 7-4s4-2 6-2l1 1c1-1 1-1 3-2l-1 2c1 0 2 0 3-1l1 1h0c-1 0-2 0-3 1-1 0-3 0-4 1-2 0-5 1-7 2s-5 2-7 4c-1 0-2 1-3 1h-1l5-4z" class="B"></path><path d="M452 583c9 1 17 5 23 12 7 9 14 19 20 28 3 4 7 8 8 12l-9-12c-1-1-2-3-3-4-3-4-7-7-10-10h0v-1-1c-1-1-1-2-3-3-1-1-2-3-3-4l-10-9c-2 0-4-1-6-2-1-1-2 0-2 0h-1v-4h-1l-1-1h-1l-1-1z" class="P"></path><path d="M693 455l-3 12c-1 2-2 4-2 6h0 1c0-1 0 0 1-1l-5 15 9-6s2-1 3-1h3c-1 1-2 2-4 2l-7 4c-3 1-5 3-6 5l-6 6 12-38c0-1 1-1 2-2 0-1 1-2 2-2z" class="Z"></path><path d="M312 392l10 29h-4c-3-1-7-3-10-6 0 0-2-2-2-3l2-2c0-1 1-3 2-5 1-4 2-9 2-13z" class="O"></path><path d="M423 508l-18-26c-4-6-21-28-21-33 5 8 11 16 18 22 7 9 11 20 19 29h0l1 3c0 2 1 4 1 5z" class="K"></path><path d="M713 394c1 3 1 6 2 10 1 2 3 5 4 8-2 5-6 7-11 9l-6 2 11-29z" class="O"></path><path d="M565 691v-17-6c-1-1-1 0-1-1v-4h0c0-1 0-1-1-2l-1-4v-1l-1-2-1-2 1-1c2 2 3 4 6 5 2-1 3-1 4-3v-5c-1-1-1-1 0-1v-1c1 1 1 2 1 3v1c1 1 1 1 1 2l-1 7c-1 3-1 6-1 9l-2 11c0 2-1 3-2 5-1 1 0 4-1 6v3l-1-2z" class="T"></path><path d="M502 702l2-1 1 2c3-1 7-1 10 0 2 1 4 3 6 4 3 4 7 7 9 11v1 1h-1c-1-1 0-1-2-1-2-2-4-3-7-5 1-1 1-1 2-1-2-2-3-3-4-3-2-2-4-3-7-2-4 0-9 5-11 9l-2 2h-3l1-3c1-4 2-6 4-9l-1-1 3-4z" class="C"></path><path d="M502 702l2-1 1 2-5 4-1-1 3-4z" class="K"></path><defs><linearGradient id="V" x1="414.505" y1="503.968" x2="406.981" y2="508.54" xlink:href="#B"><stop offset="0" stop-color="#e24344"></stop><stop offset="1" stop-color="#eb7d7e"></stop></linearGradient></defs><path fill="url(#V)" d="M390 475v1l9 13c9 14 19 27 31 38-2 1-3 1-4 0-15-14-28-29-37-47v-1c2 2 2 5 5 6h1 0l-1-1c0-1-1-2-1-3-1-1-1-1-1-2h0c-1-1-2-1-2-2v-1l-1-1h1z"></path><path d="M459 621h-1v-1l1-1h13c1 1 3 2 5 2l1-1c5 5 10 9 15 14 10 10 19 24 24 37 1 3 3 8 3 12v1c-3-8-7-16-12-24-7-12-15-23-26-32-3-3-7-6-12-7-3 0-8-2-11 0z" class="G"></path><path d="M756 257h9c2 1 4 1 5 2l-3 1h1c1 1 1 2 2 3 0 0 1 0 1 1 3 1 6 2 7 4-3-2-5-3-9-4-11-3-23 1-33 7-4 2-9 7-11 11l-2 2c0-1 1-1 1-2 4-6 8-10 15-14v-2-1c-1 1-2 1-4 0 2-2 5-3 7-4s5-2 7-2c1-1 3-1 4-1 1-1 2-1 3-1z" class="T"></path><path d="M742 261l1 1c2-1 3-1 5-1v1c-2 2-6 4-9 6v-2-1c-1 1-2 1-4 0 2-2 5-3 7-4z" class="Q"></path><path d="M485 721c1-6 3-11 7-15 3-2 6-4 10-4l-3 4 1 1c-2 3-3 5-4 9l-1 3h3c-1 3-1 5-2 8v5l-1 1v6l-1 1c-1-1-1-4-2-4v1h-1c-1-5-2-9-2-13v-8h0c-1 3-1 6-2 9l-1 1-1-5z" class="F"></path><path d="M492 736c0-3-1-5 0-8 0-8 1-16 7-22l1 1c-2 3-3 5-4 9l-1 3h3c-1 3-1 5-2 8v5l-1 1v6l-1 1c-1-1-1-4-2-4z" class="G"></path><path d="M495 719h3c-1 3-1 5-2 8v5l-1 1c-1-5-1-9 0-14z" class="T"></path><path d="M718 201c4 5 6 10 6 17v10l-1 1-1-1c0-1-1-1-1-1-2 1-2 2-3 4l-1-1h-1c0-2 0-4-2-5-2-2-1-5-1-8-1-1 0-3-1-4v-5c0-1-1-2-2-2 1-2 2-1 3-2s1-2 2-3l1 1 1 1 1-2z" class="N"></path><path d="M350 377h0c2 5 3 9 5 14l6 17 11 30 10 22c3 5 6 10 8 15h-1l1 1v1c0 1 1 1 2 2h0c0 1 0 1 1 2 0 1 1 2 1 3l1 1h0-1c-3-1-3-4-5-6v1h0c-3-5-6-10-8-15-13-25-21-52-29-79-1-2-1-6-2-8v-1zm232 282h0v3c-1 1 0 3 0 4h0c0-1 1-2 1-3h0l1-1h0c1-2 1-1 1-2v-1l1-3v-1h1v2h2 1v1l-27 81v1h-1l-1-3 3-10v-2c1-2 1-3 1-4l1-2c0-1 0-2-1-2 1-2 3-3 3-6 0-1 1-1 2-2s2-6 2-7l6-14 2-7c0-2-1-2 0-3 1-4 1-6 1-9s0-7 1-10z" class="K"></path><path d="M587 657h2l-3 7v1h-1c0 1 0 1-1 2l3-10z" class="J"></path><path d="M565 717c1-2 3-3 3-6 0-1 1-1 2-2l-6 18v-2c1-2 1-3 1-4l1-2c0-1 0-2-1-2zm17-58h0v3c-1 1 0 3 0 4h0c0-1 1-2 1-3h0l1-1h0c1-2 1-1 1-2v-1l1-3v-1h1v2l-3 10-4 14c0-2-1-2 0-3 1-4 1-6 1-9s0-7 1-10z" class="B"></path><defs><linearGradient id="W" x1="512.902" y1="735.962" x2="513.703" y2="759.645" xlink:href="#B"><stop offset="0" stop-color="#9d1f1f"></stop><stop offset="1" stop-color="#e75357"></stop></linearGradient></defs><path fill="url(#W)" d="M485 721l1 5 1-1c1-3 1-6 2-9h0v8c0 4 1 8 2 13h1v-1c1 0 1 3 2 4l1-1v-6l1-1v-5l1 2h1c0 4 0 8 1 11 3 7 7 13 15 16 4 1 10 1 14-2 7-3 10-9 13-16 0 5-4 11-7 14-3 4-7 6-11 7-2 1-4 1-7 2-2-1-4-2-7-1-13-9-21-23-25-39h1z"></path><path d="M496 732v-5l1 2h1c0 4 0 8 1 11h-1c0-1-1-1-1-2v-1 3-1c-1-3-1-5-1-7z" class="G"></path><path d="M520 776c1 0 2 0 3 1h0-2v1 1c-2 1-4 0-5 1-1 0-4 3-4 4l-1 1c0 1-1 2-2 3v1l-1 2v1c-1 1-1 1-1 2v1c0 1 0 1-1 3 0 2 0 9 1 11 0 1-1 2 0 4 0 1 0 2 1 3v2c0 1 0 1 1 3v2c0 1 0 1 1 2v2 1c-1-1-1-1-1-2l-1 1h-1 0v-1h-1c-1-2-2-3-2-5v-1s-1-1-1-2v-1c-1-1-1-1-1-2v-1c-1-2-1-3-1-4v-2c1-2 0-4 0-7 0-7 0-14 3-21 6-4 10-5 16-4z" class="I"></path><path d="M324 342l69 183h-2l-68-182 1-1z" class="L"></path><path d="M432 579c2-2 4-4 6-5 2 0 4 0 5 1l11 1c2 0 3 1 5 0h2c6 3 11 6 15 10 8 8 15 17 21 27 13 18 26 39 33 60h0l-11-21c-9-18-20-35-32-51-4-5-8-10-13-15-4-3-8-5-12-7l-3-1h-1c-7-2-15-3-22 0l-1 1v2l2-1v1c-1 3-1 5 0 8h-1c-2 1-6 4-8 4h-2l-2 1-3-1c4-2 5-4 7-8h0c2-2 3-4 4-6z" class="N"></path><path d="M435 581c0 2-1 4-1 6-3 2-6 3-9 5 3-5 6-10 10-13v2z" class="I"></path><path d="M361 390h2v2c3 9 7 17 10 26 1 2 2 6 3 8 2 4 4 8 6 11l3 1c1 1 2 1 3 1h0c2 1 3 2 5 3 0 1 1 1 2 2l-1 1c3 2 5 4 7 7 0 1 0 1-1 1 4 4 10 5 16 6h1l-1 2c2 0 3 0 5-1h1c0 1 0 1 1 2-2 0-3 1-4 1-5 2-9 2-14 0s-10-6-13-10c-17-18-23-41-31-63z" class="J"></path><path d="M419 463c-2-1-6 1-9 0h-1c-2 0-5-1-7-3 0-1-1-1-1-1l-3-3 1-1c1 0 2 1 4 2s4 2 6 2c2 1 4 2 7 2h0c2 0 3 0 5-1h1c0 1 0 1 1 2-2 0-3 1-4 1z" class="S"></path><path d="M382 437l3 1c1 1 2 1 3 1h0c2 1 3 2 5 3 0 1 1 1 2 2l-1 1c3 2 5 4 7 7 0 1 0 1-1 1-6-3-14-10-18-16z" class="F"></path><path d="M706 204l-1-3 1 1 1 1v-2c1-1 1-2 3-3h1 4 0c0 2 1 2 3 3l-1 2-1-1-1-1c-1 1-1 2-2 3s-2 0-3 2c1 0 2 1 2 2v5c1 1 0 3 1 4 0 3-1 6 1 8 2 1 2 3 2 5l1 6h0 1v8c0 2-1 4 0 6h-1c0 1-1 1-1 2l-1 1c-1 1-1 2-1 3l-8 36v-1c3-14 6-29 7-43l1-13v-6c-1-2-2-3-3-4-2-1-4-1-6 0-3 2-6 6-9 9l-2-1c3-4 7-8 9-13 3-4 3-11 2-15l1-1z" class="R"></path><path d="M717 236h1v8c0 2-1 4 0 6h-1c0 1-1 1-1 2l-1 1c-1 1-1 2-1 3l3-20z" class="T"></path><path d="M706 204l-1-3 1 1 1 1v-2c1-1 1-2 3-3h1l-3 6c-1 2 1 6 1 9s-2 5-2 8l2 2c1 0 3 0 4 1 0 1 0 1 1 2v3c-1-2-2-3-3-4-2-1-4-1-6 0-3 2-6 6-9 9l-2-1c3-4 7-8 9-13 3-4 3-11 2-15l1-1z" class="f"></path><path d="M706 204l-1-3 1 1 1 1v-2c1-1 1-2 3-3h1l-3 6c-1 2 1 6 1 9s-2 5-2 8l2 2-7 3c1-3 3-5 4-8 1-4 2-10 0-14z" class="K"></path><defs><linearGradient id="X" x1="599.88" y1="616.769" x2="587.699" y2="611.319" xlink:href="#B"><stop offset="0" stop-color="#86161b"></stop><stop offset="1" stop-color="#ac2d33"></stop></linearGradient></defs><path fill="url(#X)" d="M594 595c1-1 2-1 3-2l1 1h0c1 3 1 8 4 10h2v1l-10 32c-1 3-2 7-3 11h-2l2-12c1-9 1-22-5-29l-4-5c3 1 6 4 9 5 1 0 2-1 3-1 2-4 1-7 0-11z"></path><path d="M587 607h2c0 1 0 1 1 1h1v1c1 1 2 2 2 3 1 2 1 5 0 8h-1v-2c-1-1 0-2-1-4h-1l-1-3v-1c-1-1-1-2-2-3z" class="S"></path><path d="M594 637c-1-1 0-1-1-1 0-3 1-6 2-9l4-14 3-8h2l-10 32z" class="F"></path><defs><linearGradient id="Y" x1="507.833" y1="733.298" x2="519.778" y2="733.674" xlink:href="#B"><stop offset="0" stop-color="#0b0a0a"></stop><stop offset="1" stop-color="#261f23"></stop></linearGradient></defs><path fill="url(#Y)" d="M524 744c-2 0-4 1-6 0-4-1-7-5-9-8s-2-7-1-10c1-2 2-3 4-4l1-1c1 0 1 0 2-1 2 0 5 1 7 2v2l1 1c1 0 1 1 2 2 1 0 2 1 3 2v1c1 1 1 2 1 4 1 0 1 1 1 2h0v3h0v1c-1 1-1 2-2 3h-1v-1l-1 1c-1 0-2 0-2 1z"></path><path d="M524 744v-2c1 0 2-1 3-1 0-2 0-2 1-3v-1-1l-3 2h-6l-3-1c-1-1-2-2-2-4h0l-1-1c0-2 0-3 1-5 2-1 1 0 2 1h3c1 0 2 1 3 2 3 1 5 2 7 4 1 0 1 1 1 2h0v3h0v1c-1 1-1 2-2 3h-1v-1l-1 1c-1 0-2 0-2 1z" class="I"></path><path d="M273 325h6c3 3 5 8 7 12l6 16c-6-1-11-4-16-6-4-2-7-4-10-7-2-1-4-3-5-5 0-2 1-3 2-4s1-1 2-1c0 4 1 8 3 12 0-4-1-11 1-14 0-2 2-2 4-3z" class="c"></path><path d="M747 318c2 3 4 6 7 7 4 3 10 5 12 8v1c-2 5-12 8-16 10l-15 6 12-32z" class="O"></path><path d="M739 170l12-2h6c2 0 4-2 6-2-6 4-11 7-16 13-2 2-3 5-5 8 0 1-1 3-2 3-2-1-4-3-6-4-1-1-3-1-5-2v-1c-2-1-4-1-6-2-3-1-6-2-9-2l-2-1c-2-1-2-1-4-1l3-1h5c3-1 6 0 9-2 1 0 6-1 6-2l-1-1h4c2 0 3 0 5-1z" class="R"></path><path d="M734 171h4l1 1c0 1 0 2-1 2-3 0-5 1-7 2h-1l-1-1h-1c-3 1-8 1-12 1 3-1 6 0 9-2 1 0 6-1 6-2l-1-1h4z" class="M"></path><path d="M379 260c0-1 1-3 2-4v1c0 1-1 1-1 2s1 1 2 2l2 6 1 1v2h1c1 2 2 5 3 8v1c1 1 1 1 1 2 1 1 1 1 1 2s1 2 1 2c0 1 0 2 1 3v1c1 2 0 1 1 2l1 2v1c1 1 1 0 1 2h0l1 2v1l1 1c0 2 1 3 2 5v2l2 2v2h0c1 1 1 2 1 3 1 1 1 1 1 2 1 0 1 2 2 4 0 0 0 1 1 2v1 1c1 1 1 2 2 3 1 2 1 4 2 6 0 0 1 1 1 2l2 5c0 1 1 2 1 3l1 1v2l2 4c1 0 1 1 1 2l1 2v1c1 0 1 1 1 2l1 1v2l1 1v1l1 1s0 2 1 3h0v1c1 1 1 1 1 2s0 0 1 2v2c1 2 2 3 3 6h0c1 1 1 2 1 3s0 1 1 2v2h0c2-1 2-3 3-5l1-1v-3-5l-1-1v-1c0-1 0-2-1-3v-1c0-1-1-1-1-1l-4-9c-1-1-2-3-3-4l-3-4-1-3-1-1v-1c-1-1-2-1-2-2v-1l-1-1c-1-1 0-2 0-3 1-1 1-1 2-1v-1-1l1 1v1l1 1c2 0 3 1 4 3v1c2 2 4 6 7 7 2 1 4 1 6 0 1-1 2-2 3-2l2-3c-1 1-3 5-3 6-1 3-2 5-4 7l-1 1c1 4 3 8 3 13 1 1 1 5 1 7l-1 1c0 1 0 2-1 3v1l-1 3c-2 3-4 6-7 7h0c-1 1-1 1-2 1h0-3l1-2h3c2-1 2-1 3-2-1-6-4-12-6-17l-17-41-18-45c-3-7-5-14-9-21-1-2-1-3-3-4z" class="F"></path><path d="M437 370h1c0 2 1 4 0 6v3c-1 0-1 1-1 2s-1 1-1 2h-1c1-1 1-1 1-2v-1c1-3 0-7 1-10z" class="C"></path><path d="M439 369h0-1c-1-5-3-10-5-15 1-1 2 0 3 0 1-2 2-3 3-4v-1l-2 1c-10 1-9-8-16-13h-1 3l3 3c2 2 4 6 7 7 2 1 4 1 6 0 1-1 2-2 3-2l2-3c-1 1-3 5-3 6-1 3-2 5-4 7l-1 1c1 4 3 8 3 13z" class="L"></path><path d="M379 260c2 1 2 2 3 4 4 7 6 14 9 21l18 45 17 41c2 5 5 11 6 17-1 1-1 1-3 2-6-8-8-21-12-30l-7-16-25-61c-3-7-7-15-6-23z" class="G"></path><path d="M447 603l6 1 2 1c9 3 16 9 23 15l-1 1c-2 0-4-1-5-2h-13l-1 1v1h1c0 3 0 5 1 7 2 3 5 6 7 9 7 7 15 12 22 18 2 0 2 1 4 1v1c2 0 3 1 4 2h0v1l-1-1c-1 0-2 0-3-1h-1-1l-2 1h1c-3 1-7-1-10-2-2-2-4-3-6-4-7-5-13-10-17-18l1-2c0-1-2-3-2-4-1-2-2-4-3-7-3-5-5-12-6-17v-2z" class="D"></path><path d="M447 603l6 1 2 1c9 3 16 9 23 15l-1 1c-4-3-8-7-12-9-6-3-12-5-18-7v-2z" class="K"></path><path d="M489 655h0c-4-1-7-4-9-6-7-5-15-11-19-18l-2-4c-2-3-4-7-3-11l1-1c2 0 8 2 9 2l6 2h-13l-1 1v1h1c0 3 0 5 1 7 2 3 5 6 7 9 7 7 15 12 22 18z" class="J"></path><path d="M458 633c5 8 12 14 20 20 3 2 7 5 11 6h0 1c-3 1-7-1-10-2-2-2-4-3-6-4-7-5-13-10-17-18l1-2z" class="W"></path><path d="M743 200c1-2 2-3 3-5l5-9c1-1 3-2 4-4 1-1 2-3 4-4l1-1 3-3h1c1-1 1-1 2-1l4-2h1c2-1 6-1 8 0h0c2 0 2 1 3 1 1 1 2 1 3 2l1-2 1 1c-1 1-1 2-2 3 0 0-2-1-2-2h-1l-1-1h-1c-1-1-1-1-2-1v1s1 0 2 1c1 0 3 1 3 3h0l-4-2h-2-1v1c1 4-4 10-6 13l-2 3 1 1c0-1 1-1 1-2l1-1c1 1 1 1 1 2v1 1l-3 3h-1v1c-4 3-7 5-11 6-2 1-6 1-8 1l-1-1h-1v-1h0c2 1 6 1 8 1v-1c-4 0-8 0-12-3z" class="P"></path><path d="M757 195h3c2 0 2-1 4-2 0-1 1-3 1-3 1-1 0-1 1-2h0l2-2c0-2 0-2 1-3l1 1-2 3v2h2l-2 3c-2 2-6 6-9 7-2 1-6 1-9 1l-1-1c0-3 4-8 5-11 0-1 1-2 2-3 0 1 0 2-1 3v1 1c-1 1-1 2-1 4l1 1h2z" class="B"></path><path d="M757 195l-1-1c0-1 0-4 1-5v-1c0-1 0-1 1-2h0c2-4 5-7 8-9 2-1 3-2 5-2v-1c4-1 5-1 8 1h-2-1v1c1 4-4 10-6 13h-2v-2l2-3-1-1c-1 1-1 1-1 3l-2 2h0c-1 1 0 1-1 2 0 0-1 2-1 3-2 1-2 2-4 2h-3z" class="I"></path><defs><linearGradient id="Z" x1="429.601" y1="620.389" x2="473.685" y2="641.898" xlink:href="#B"><stop offset="0" stop-color="#090708"></stop><stop offset="1" stop-color="#2d1816"></stop></linearGradient></defs><path fill="url(#Z)" d="M438 606l6-24c2 0 6 0 8 1l1 1h1l1 1h1v4l-3-3-1 1 1 2c-2-1-3-2-4-2v1l-1 1c0-2-1-3-2-4-1 1-1 2-1 3l-1 1h0l-1 3h0c0 3-1 7 0 10l1-3c0 1 0 1 1 1v2c1 0 1 0 2 1v2c1 5 3 12 6 17 1 3 2 5 3 7 0 1 2 3 2 4l-1 2c4 8 10 13 17 18-4 0-8-1-11 1 0 1 0 2-1 3l-1 1c-1 0-2 1-3 2v1h1c-3 3-4 6-5 11l-1-1 1-7-2 2-1-1c1-2 1-3 2-5 0-1 1-2 1-3v-3h-1c1-2 0-4-1-6v-1h-1v-2l-1-1v-2c-1-1-1-1-1-2v-1h-1v-1c0-1-1-2-1-3-1-2-2-4-2-6l-1-1v-1c-1 1 0 2 0 3-1-2-1-5-2-7v-4c0-3-1-6-2-9h-1v-3l-1-1z"></path><path d="M453 660v2l1-1c1 1 0 2 0 3l-2 2-1-1c1-2 1-3 2-5z" class="D"></path><path d="M454 664l1-1c1-5 4-7 8-9 0 1 0 2-1 3l-1 1c-1 0-2 1-3 2v1h1c-3 3-4 6-5 11l-1-1 1-7z" class="M"></path><path d="M447 630c3 5 8 13 7 18h-1l-7-17 1-1z" class="Q"></path><path d="M443 602l1-3c0 1 0 1 1 1v2c1 0 1 0 2 1v2c1 5 3 12 6 17 1 3 2 5 3 7 0 1 2 3 2 4l-1 2h0c-5-7-8-15-11-22-1-4-3-7-3-11z" class="M"></path><path d="M438 606l6-24c2 0 6 0 8 1l1 1h1l1 1h1v4l-3-3-1 1 1 2c-2-1-3-2-4-2v1l-1 1c0-2-1-3-2-4-1 1-1 2-1 3l-1 1h0l-1 3h0c-1 1-2 2-2 4v1 2h-1c0 3 0 7 1 10l6 21-1 1v-2l-2-4c-1-1-1-2-1-2l-1-4c0-3-1-6-2-9h-1v-3l-1-1z" class="T"></path><path d="M446 585v-1h4c1 0 2 1 3 2l-1 1 1 2c-2-1-3-2-4-2v1l-1 1c0-2-1-3-2-4z" class="F"></path><path d="M482 519l3-2 11 31 27 72 8 21c3 8 5 17 7 26 5 16 9 31 11 47 1 10 1 20 2 30 0 7 0 14-1 22 0 1 0 1-1 2 0-18 0-35-3-53-4-33-16-65-27-95l-17-47c-7-18-14-36-20-54z" class="J"></path><defs><linearGradient id="a" x1="517.686" y1="808.489" x2="482.091" y2="829.025" xlink:href="#B"><stop offset="0" stop-color="#c6040b"></stop><stop offset="1" stop-color="#e82512"></stop></linearGradient></defs><path fill="url(#a)" d="M477 757c6 14 10 29 15 43l13 33 14 47c1-1 1 0 1-1l17-55h0v-1 4h1l1-1 20-56 12-33 1-1c0-1 0-1 1-2l-1 4c-3 5-4 10-6 16l-14 40-24 73c-3 11-7 22-8 33-3-15-8-30-12-45l-34-98 1-1c0-1-2-5-2-7 1 1 1 2 2 3v3c1 1 1 2 2 3v-1z"></path><path d="M661 385c1 1 1 1 1 2v3c1 3 0 6 2 9 1 2 1 5 1 7 0-1 1-2 1-3 1-3 2-5 3-7 0 2-1 3 1 5l5-14h1v1l-86 270v-1h-1-2v-2c1-2 2-4 2-7h2c1-4 2-8 3-11l10-32v-1c1-1 2-4 2-6h0c-1-2-1-3 0-4h1v1h1c2-2 2-6 3-9l4-13 13-40h0c2-5 3-11 5-16l7-22c8-25 18-50 21-77 1-11 1-22 0-33z" class="J"></path><path d="M589 648h2c0 2 0 3-1 5v2 2h-1-2v-2c1-2 2-4 2-7z" class="G"></path><path d="M669 396c0 2-1 3 1 5l-6 16c0-3 0-8 1-11 0-1 1-2 1-3 1-3 2-5 3-7z" class="C"></path><defs><linearGradient id="b" x1="487.831" y1="622.644" x2="478.883" y2="629.789" xlink:href="#B"><stop offset="0" stop-color="#0c0e0f"></stop><stop offset="1" stop-color="#271313"></stop></linearGradient></defs><path fill="url(#b)" d="M446 585c1 1 2 2 2 4l1-1v-1c1 0 2 1 4 2l-1-2 1-1 3 3h1s1-1 2 0c2 1 4 2 6 2l10 9c1 1 2 3 3 4 2 1 2 2 3 3v1 1h0c3 3 7 6 10 10 1 1 2 3 3 4l9 12c0 2 1 2 2 4 1 1 2 3 3 5v2 1c2 2 3 3 4 5l1 2 1 1c1 0 1 1 1 2v1c0 1 0 1 1 2v3h0c1 1 0 2 0 2v1c0 1 0 2 1 3v2l3 3v1c1 2 1 3 2 5l1 1h0v1c1 1 1 1 1 2v1c0 1 0 1 1 2 0 1 0 3-1 4 0 2 0 3 1 5v1l-1 1c-2-3-3-9-4-12v-2-1c0-4-2-9-3-12-5-13-14-27-24-37-5-5-10-9-15-14-7-6-14-12-23-15l-2-1-6-1c-1-1-1-1-2-1v-2c-1 0-1 0-1-1l-1 3c-1-3 0-7 0-10h0l1-3h0l1-1c0-1 0-2 1-3z"></path><path d="M477 610l5 4-2 2-4-4 1-2z" class="J"></path><path d="M449 588v-1c1 0 2 1 4 2 0 1 3 3 3 3 0 2 1 2 1 3 1 1 1 1 1 2-4-3-6-6-9-9z" class="K"></path><path d="M471 604l6 6-1 2-8-7 3-1z" class="G"></path><path d="M447 594c1-1 1-1 1-2 2 5 6 7 9 10l-1 1c-3-2-5-3-8-6l-1-3z" class="N"></path><path d="M448 597c3 3 5 4 8 6l1-1 2 1v1h-2l-2 1-2-1c-1-1-2-2-3-2-1-1-1-1-2-1l-1-1c0-1 1-2 1-3z" class="Q"></path><path d="M446 585c1 1 2 2 2 4v3c0 1 0 1-1 2l1 3c0 1-1 2-1 3l1 1c1 0 1 0 2 1 1 0 2 1 3 2l-6-1c-1-1-1-1-2-1v-2c-1 0-1 0-1-1l-1 3c-1-3 0-7 0-10h0l1-3h0l1-1c0-1 0-2 1-3z" class="E"></path><path d="M447 600l-1 2-1-1 1-5 1-2 1 3c0 1-1 2-1 3z" class="F"></path><path d="M482 614l11 12 1-3 9 12c0 2 1 2 2 4 1 1 2 3 3 5v2 1c-3-3-6-8-9-12a170.44 170.44 0 0 0-19-19l2-2z" class="R"></path><path d="M494 623l9 12c0 2 1 2 2 4 1 1 2 3 3 5v2l-15-20 1-3z" class="C"></path><path d="M453 586l3 3h1s1-1 2 0c2 1 4 2 6 2l10 9c1 1 2 3 3 4 2 1 2 2 3 3v1 1h0c3 3 7 6 10 10 1 1 2 3 3 4l-1 3-11-12-5-4-6-6-3 1-4-3-6-5c0-1 0-1-1-2 0-1-1-1-1-3 0 0-3-2-3-3l-1-2 1-1z" class="B"></path><path d="M466 601l5 3-3 1-4-3 2-1z" class="K"></path><path d="M472 602l1-1 1 1 1-1 3 3c2 1 2 2 3 3v1 1h0l-9-7z" class="S"></path><path d="M456 592l10 9-2 1-6-5c0-1 0-1-1-2 0-1-1-1-1-3z" class="L"></path><path d="M457 589s1-1 2 0c2 1 4 2 6 2l10 9c1 1 2 3 3 4l-3-3-1 1-1-1-1 1c-5-5-11-8-15-13z" class="E"></path><path d="M251 280h11c3 4 4 8 6 12s4 9 6 13c-1 3-2 6-3 8-3 5-8 8-14 9-4 1-8 0-12-2-6-3-8-8-10-15-1-5-2-11 1-16 4-5 9-8 15-9zm517 0c4-1 9 0 13 2 4 1 8 6 9 10 2 6 0 15-3 20-2 6-7 8-12 10-5 1-10 1-14-1-4-3-7-7-9-11 0-1-1-3-1-4s2-5 3-6c2-6 5-13 8-19 2-1 4-1 6-1z" class="O"></path><path d="M335 277l16 12c1-1 2-1 3 0l6 2c2 1 6 2 8 1 3 0 5 2 8 0h0c2-1 3-2 4-3h0l3-3c1 0 1-2 2-3l25 61 7 16c4 9 6 22 12 30h-3-2 0c-1-1-1-1-1-2-2-5-4-9-6-13 0-1-1-3-1-4s0-1-1-2v-2c0-4-2-9-4-11l-3-3c0-2-2-3-3-4-3-4-5-9-9-14-16-21-39-37-60-55l-1-3z" class="I"></path><path d="M380 289h0l3-3c0 3 0 7-3 9-1 0-1 0-1 1v-1h-2c0-2 2-4 3-6z" class="N"></path><path d="M379 295v1c2 2 3 5 5 8l7 12-5-4c1 2 3 5 4 7v1c-4-4-7-10-10-15-1-1-2-4-4-5-1-1-4-1-6-2h2c2 0 4 0 6-1l1-2z" class="E"></path><path d="M351 289c1-1 2-1 3 0l6 2c2 1 6 2 8 1 3 0 5 2 8 0h0c2-1 3-2 4-3-1 2-3 4-3 6h2l-1 2c-2 1-4 1-6 1h-2c-6-2-14-5-19-9z" class="S"></path><path d="M376 292c2-1 3-2 4-3-1 2-3 4-3 6-4 1-8-1-12-1-2-1-3-1-5-3 2 1 6 2 8 1 3 0 5 2 8 0h0z" class="M"></path><path d="M333 217v1c2-2 4-4 4-6 1 2 0 3 0 5-3 2-4 4-6 5 1 1 2 1 4 1h0 3c1 2 3 4 4 6 0 1 0 1 1 2v1c1 3 3 5 5 7 1-4 3-9 7-12 5 5 7 10 11 15l7 8c1-1 2-1 3-2l24-15h1c2-2 3-4 5-6l1 1c-3 4-6 8-10 12h0l-9 6c-1 0-2 1-3 1l-1 1-6 4h-1c-2 2-4 3-6 5 0 1-2 3-2 3h-1c-1 1-2 1-2 3l-1 1c-1 0-1 1-1 2-1 1-3 3-3 5h-1v2h-1c0 1 3 3 4 3 2-3 3-6 7-8l1 1h0c0 2 0 3 1 4-2 1-2 2-2 4s0 3 1 4h0c-1 0-2 0-3-1-2-1-5-2-7-4-5-2-9-5-14-8-9-7-17-14-23-24h-1c1-1 0-2 0-3-1-1-1-2-2-3l-4-10 4-2c0-1 2-2 3-2 3-2 6-4 9-7z" class="K"></path><path d="M323 241l3 1 2 4c-1-1-1-1-2-1s-2-1-2-1h-1c1-1 0-2 0-3z" class="J"></path><path d="M370 268l1 1h0c0 2 0 3 1 4-2 1-2 2-2 4s0 3 1 4h0c-1 0-2 0-3-1v-4c-1 1-2 1-3 1 0-1 1-1 2-2 0-1 0-3 1-4l2-2v-1z" class="C"></path><path d="M365 258h1 1c1-1 2-2 3-1l-2 2v1c-1 1-2 1-2 3l-1 1c-1 0-1 1-1 2-1 1-3 3-3 5h-1v2h-1l-15-11c1 0 2 0 3 1h0 3c2 1 3 1 5 1l1-1c1 0 2 0 3-1l6-4z" class="B"></path><path d="M333 217v1c2-2 4-4 4-6 1 2 0 3 0 5-3 2-4 4-6 5 1 1 2 1 4 1l-3 2c-2 1-5 3-6 5 0 3 0 4 1 7l1 3c1 7 9 15 15 19 1 1 4 2 5 3 3 1 8-1 11 0-1 1-2 1-3 1l-1 1c-2 0-3 0-5-1h-3 0c-1-1-2-1-3-1s-2-1-3-2-3-2-4-4l1 1c0-2-3-4-5-5-2-2-3-4-5-6l-2-4-3-1c-1-1-1-2-2-3l-4-10 4-2c0-1 2-2 3-2 3-2 6-4 9-7z" class="S"></path><path d="M322 231l1 4 1 1c1 1 1 2 2 4v2l-3-1c-1-1-1-2-2-3 1-1 1-1 0-1v-3l1-3z" class="G"></path><path d="M321 226h0c0 1 0 1-1 1l1 2-1 1c1 1 1 1 2 1h0l-1 3v3c1 0 1 0 0 1l-4-10 4-2z" class="J"></path><path d="M326 230l-1 1c-1 0-1 0-2-1v-1c1-1 3-3 4-5 1-1 2-2 4-2 1 1 2 1 4 1l-3 2c-2 1-5 3-6 5z" class="E"></path><path d="M400 233h1c2-2 3-4 5-6l1 1c-3 4-6 8-10 12h0l-9 6c-1 0-2 1-3 1l-1 1-6 4h-1c-2 2-4 3-6 5 0 1-2 3-2 3h-1v-1l2-2c-1-1-2 0-3 1h-1-1l-6 4c-3-1-8 1-11 0-1-1-4-2-5-3-6-4-14-12-15-19 1 1 2 2 4 3 6 6 14 15 23 15 7 1 13-5 18-8 1-1 2-1 3-2l24-15z" class="W"></path><path d="M400 233h1c2-2 3-4 5-6l1 1c-3 4-6 8-10 12h0l-9 6c-1 0-2 1-3 1l-1 1-6 4h-1c-2 2-4 3-6 5 0 1-2 3-2 3h-1v-1l2-2c-1-1-2 0-3 1h-1-1c0-1 0-1 1-1 11-9 25-14 34-24z" class="C"></path><path d="M335 223h3c1 2 3 4 4 6 0 1 0 1 1 2v1c1 3 3 5 5 7 1-4 3-9 7-12 5 5 7 10 11 15l7 8c-5 3-11 9-18 8-9 0-17-9-23-15-2-1-3-2-4-3l-1-3c-1-3-1-4-1-7 1-2 4-4 6-5l3-2h0z" class="I"></path><path d="M335 223h3c1 2 3 4 4 6 0 1 0 1 1 2v1c-1-2-3-5-6-5-2-1-5 0-7 2-1 1-2 3-2 4 0 4 2 7 4 10-2-1-3-2-4-3l-1-3c-1-3-1-4-1-7 1-2 4-4 6-5l3-2h0z" class="b"></path><path d="M337 237h2c2 1 5 4 7 5l6 3c1 1 1 1 1 2l-1 1c-4 0-9-2-13-3-1-2-4-4-4-6l2-2z" class="E"></path><path d="M336 325l3 6h0c-1 0-1 0-2 1 4 2 8 5 11 9 1 2 3 5 4 8l5 7 10 22v2l-2-1h0l-1 1 1 1h0l1 2v1 1c1 1 1 1 1 2v1l4 9c1 1 1 2 1 4 0 1 1 2 2 3 0 1 1 6 1 8l-12-25h-1v-1l-1 1 2 3h-2l-6-20c-1-5-2-10-4-15-1-1-2-1-3-2l-1 1h1l-1 1v1 1c-1 2-1 4 0 7v4c0 1 1 3 1 4v1c0 1 0 0 1 2 0 0 0 1 1 2v1c1 2 1 6 2 8 8 27 16 54 29 79 2 5 5 10 8 15h0c9 18 22 33 37 47l2 2h0v1c0 1 1 1 1 2l-1 1c-3-2-7-3-11-4-1 0-2-1-3-1-2-1-3-2-5-2l-1 1c1 1 1 0 0 2v2c0 1-1 2-1 3l-3 6v2c-1 1-1 3-3 4h-1v-4l-7-17-69-183-2-3c1-3 2-3 4-6h0v-1l3-4h1 0c2-1 2-1 4-1h0l2-2z" class="I"></path><path d="M345 378c1 1 4 7 4 8 1 2 2 4 2 6h-1v1c1 1 1 2 2 3v1h-1c0-1-1-3-2-4v-2l-1-1-1-4-2-4v-4z" class="C"></path><path d="M347 354h-1c-1-2 1-5 1-7 0-1-1-3-1-3v-1l3 3 3 5v1c-1 1-1 1-1 2v1h0c-1-1-2-1-3-2l-1 1z" class="L"></path><path d="M336 325l3 6h0c-1 0-1 0-2 1-3-2-5-2-8-1l-3 2v-1l3-4h1 0c2-1 2-1 4-1h0l2-2z" class="E"></path><path d="M333 336h1c3 6 7 13 8 19 0 2 1 3 0 5-2-3-3-9-4-12l-5-12z" class="B"></path><defs><linearGradient id="c" x1="404.003" y1="535.697" x2="401.997" y2="540.803" xlink:href="#B"><stop offset="0" stop-color="#2a1111"></stop><stop offset="1" stop-color="#1b1515"></stop></linearGradient></defs><path fill="url(#c)" d="M400 542c0 1 1 1 1 2v1-3-1-2c2-5 4-10 7-13h1l-1 1c1 1 1 0 0 2v2c0 1-1 2-1 3l-3 6v2c-1 1-1 3-3 4h-1v-4z"></path><path d="M352 351c3 3 4 12 5 16l6 20h-1v-1l-1 1 2 3h-2l-6-20c-1-5-2-10-4-15h0v-1c0-1 0-1 1-2v-1z" class="M"></path><path d="M685 241v-1h1l1-1s1 0 1-1c1 0 1-1 2-1 1-1 2 0 3 0h1 0c-1-2-3-2-5-3-1-1-6-5-7-7v-2l-1-1-2-4v-1-1-2c-1-2-1-5-1-7v-6c2 1 0 9 1 11v1c2 8 6 15 13 19l2 1 8 5c-2-2-5-4-6-6 3-3 6-7 9-9 2-1 4-1 6 0 1 1 2 2 3 4v6l-1 13c-1 14-4 29-7 43v1c-3 14-8 29-12 43l-18 53v-1h-1l-5 14c-2-2-1-3-1-5-1 2-2 4-3 7 0 1-1 2-1 3 0-2 0-5-1-7-2-3-1-6-2-9v-3c0-1 0-1-1-2 0 0-3-9-3-10-1-3-3-4-5-5l1-2c1 0 3 0 4-1 2-2 3-5 3-7s1-3 1-4h1 0c3-4 4-10 6-15l1-7c0-7 2-15 3-23l3-33 1-1h-1v-2c-1-2 0-3-1-5s0-2-1-4c0-2 0-5 1-6h0v-4-2c2-1 2-2 2-4l2-2c2-3 4-4 6-6v-1z" class="I"></path><path d="M704 265h3c-1 6-2 13-4 20l-1-1 1-8 1-11zm0-18h0c1-2 1-4 1-6 1 0 2 1 2 3 2 4 1 13 0 17 0-2 0-3-1-5h-1v-3-1l1-4-2-1z" class="K"></path><path d="M693 317h2l-5 17-4 18h0-2l3-17 1-1h1c0-2 1-4 1-5l3-12z" class="M"></path><path d="M693 304h1l1-3 2-4 1-4h0l1 1h0c-1 1-1 1-1 2v2l-5 19-3 12c0 1-1 3-1 5h-1l-1 1c1-10 3-21 6-31zm13-13v1c-3 14-8 29-12 43l-18 53v-1h-1l9-35h2 0l4-18c1 0 2 1 2 1l14-44z" class="N"></path><path d="M686 352l4-18c1 0 2 1 2 1l-5 18c-1-1-1 0-1-1z" class="C"></path><path d="M704 247l2 1-1 4v1 3h1c1 2 1 3 1 5v4h-3l-1 11-1 8 1 1-8 32h-2l5-19v-2c0-1 0-1 1-2h0l-1-1h0l-1 4-2 4-1 3h-1c3-11 10-31 6-42h0c-2-5-6-8-9-11l7 4h1c3-2 5-4 6-8z" class="R"></path><path d="M698 255h1c1 0 2-1 3-2h1l-2 10-1-2c-1-3-1-4-3-6h0 1z" class="F"></path><path d="M700 278l3-2-1 8-1 4-1 1c0-1 0-3-1-4 1-2 1-5 1-7z" class="P"></path><path d="M700 278l2-15c1-3 1-7 3-10v3h1c1 2 1 3 1 5v4h-3l-1 11-3 2z" class="T"></path><path d="M705 256h1c1 2 1 3 1 5v4h-3c0-3 0-6 1-9z" class="L"></path><path d="M685 241h1c1-1 2-2 3-2 0-1 2-2 3-2 1 1 1 1 2 1-1 2-3 2-5 3-1 1-3 2-4 3-2 1-3 3-4 4-2 2-4 5-5 8 3-3 5-6 9-7 6 6 6 13 9 21v1c0-2 0-4-1-5 0-2-1-5 0-7 2 6 3 11 1 17v1l-5 17v-3-2c0-2 1-3 1-5-1 2-2 2-2 5 0 1 0 2-1 3v-4c-1-3-2-7-3-10l-7-15-1-1c0 4 2 12 1 15h-1v-2c-1-2 0-3-1-5s0-2-1-4c0-2 0-5 1-6h0v-4-2c2-1 2-2 2-4l2-2c2-3 4-4 6-6v-1z" class="E"></path><path d="M677 263h1c4 7 7 13 12 18l1 1 2-6 1 1-5 17v-3-2c0-2 1-3 1-5-1 2-2 2-2 5 0 1 0 2-1 3v-4c-1-3-2-7-3-10l-7-15z" class="X"></path><path d="M677 277c1-3-1-11-1-15l1 1 7 15c1 3 2 7 3 10v4c1-1 1-2 1-3 0-3 1-3 2-5 0 2-1 3-1 5v2 3c0 3-1 6-2 9l-3 19-4 25-7 35c-1 4-2 10-4 14-1 2-2 4-3 7 0 1-1 2-1 3 0-2 0-5-1-7-2-3-1-6-2-9v-3c0-1 0-1-1-2 0 0-3-9-3-10-1-3-3-4-5-5l1-2c1 0 3 0 4-1 2-2 3-5 3-7s1-3 1-4h1 0c3-4 4-10 6-15l1-7c0-7 2-15 3-23l3-33 1-1z" class="P"></path><path d="M670 334l2 2c-1 2-1 4-2 6-1-1-1 0-1-1l1-7z" class="E"></path><defs><linearGradient id="d" x1="687.29" y1="296.297" x2="663.306" y2="302.078" xlink:href="#B"><stop offset="0" stop-color="#000302"></stop><stop offset="1" stop-color="#4b1819"></stop></linearGradient></defs><path fill="url(#d)" d="M677 277c1-3-1-11-1-15l1 1 7 15c1 3 2 7 3 10 0 6-2 9-3 14l-3 12-1 1h0v-9c-2 1-1 1-3 0l-3 16c-1 5-2 9-2 14l-2-2c0-7 2-15 3-23l3-33 1-1z"></path><path d="M677 306l3-13c1 4 0 9 0 13-2 1-1 1-3 0z" class="E"></path><path d="M684 278c1 3 2 7 3 10 0 6-2 9-3 14l-3 12v-2-2l1-1c0-2-1-4 0-6 2-4 2-10 2-14s-1-7 0-11z" class="D"></path><path d="M646 370h7 0c2 1 4 2 5 5 0 1 3 10 3 10 1 11 1 22 0 33-3 27-13 52-21 77l-7 22c-2 5-3 11-5 16h0-1v1s-1 1-1 2l-1 5-3 6v3c-1 1-2 3-2 5v2l-1 1-1 3c0 2-1 7-3 8 0 2 0 3-1 5 0 1-1 3-1 5h-1l-1 4v1l-1 3-2 6h-3v4 3 2l-1 1h-1c-1-1-2-1-2-3-1-1-1-2-2-3v-1c-1-4 3-7 1-11v-2c0-1 0-1-1-2v-1-1-1c-1-1 0-1-1-1-1-1-1-1-2-1-1 2 0 5 0 7v2l-1 1c0 1-1 2-2 3 0 1-1 2-1 3v1l1 1c0 1 1 2 1 3v1 1c0 1 0 1 1 2l-1 2c0 1-1 2-2 3-1 0-2 0-3-1l-4-2-3-2-1-1c11-24 18-51 26-76l22-80c5-21 8-41 11-62 2 0 3-1 4-3 2-2 4-3 5-5 0-2 1-2 0-4h-2-1z" class="F"></path><path d="M111 145c2-2 8-1 11-1 18 1 35 3 52 6l69 9 20 3c6 1 13 1 19 2-3 1-6 1-9 0h-3 0-5 1 1 1c1 0 1 1 2 1h2 1 0c2 0 3 1 4 2l-9-1c2 2 4 2 7 3 2 1 5 1 7 1 0 2 0 1 1 2 0 1 0 2 1 2h-1c-1 0 0 0-1-1l-1 1 2 2h0v3l1 2c0 1 1 2 1 3v1c1 2 1 2 1 4v3c8-5 14-8 23-9l-2-1 6-2c2 0 5 0 7 1 9 1 19 7 25 13 5 6 8 11 10 18 1 4 1 9 3 13 2 5 6 9 8 14v3c-4-5-6-10-11-15-4 3-6 8-7 12-2-2-4-4-5-7v-1c-1-1-1-1-1-2-1-2-3-4-4-6h-3 0c-2 0-3 0-4-1 2-1 3-3 6-5 0-2 1-3 0-5 0 2-2 4-4 6v-1c-3 3-6 5-9 7-1 0-3 1-3 2l-4 2c-3 2-4 3-5 7-2 7 3 18 7 24 3 5 6 9 11 14 1 1 5 3 5 4l1 3c21 18 44 34 60 55 4 5 6 10 9 14 1 1 3 2 3 4l3 3c2 2 4 7 4 11v2c1 1 1 1 1 2s1 3 1 4c2 4 4 8 6 13 0 1 0 1 1 2h0 2l-1 2h3 0c1 0 1 0 2-1h0c3-1 5-4 7-7l1-3v-1c1-1 1-2 1-3l1-1c0-2 0-6-1-7 0-5-2-9-3-13l1-1c2-2 3-4 4-7 1 2 1 2 0 4 0 2 1 6 1 9v2l1 1c0 3 1 9 0 12 0 1 0 2-1 3v3c-1 0-1 2-1 3h0c-2 4-4 7-5 11-1 2-1 5-1 7l-1 1c0 1 1 2 1 3 1 4 3 8 4 11l7 17c0 2 1 4 2 5h1l-1 1c-2 1-5 5-8 5l2 2-4 3c-5 4-10 8-15 11-1-1-1-1-1-2h-1c-2 1-3 1-5 1l1-2h-1c-6-1-12-2-16-6 1 0 1 0 1-1-2-3-4-5-7-7l1-1c-1-1-2-1-2-2-2-1-3-2-5-3h0c-1 0-2 0-3-1l-3-1c-2-3-4-7-6-11-1-2-2-6-3-8-3-9-7-17-10-26v-2l-2-3 1-1v1h1l12 25c0-2-1-7-1-8-1-1-2-2-2-3 0-2 0-3-1-4l-4-9v-1c0-1 0-1-1-2v-1-1l-1-2h0l-1-1 1-1h0l2 1v-2l-10-22-5-7c-1-3-3-6-4-8-3-4-7-7-11-9 1-1 1-1 2-1h0l-3-6c0-3-1-6-2-9v-1c-2-8-11-8-17-12 2 0 5 1 7 1h1-1c-1-2-2-3-3-5-2-1-4-6-5-7-4-6-9-10-14-15-2-1-4-3-6-3l-1 1c-1-1-3-1-5-1-3-3-7-4-11-5-7-3-14-2-22-1-1 1-1 2-1 3l-4 1h-4-1c-8 3-13 9-16 16h0c0-2 1-4 2-5v-1c2-3 5-7 8-9l4-3-1-1 4-4h0v-1c-1 0-2 1-3 2v-1c4-4 13-8 19-8l7-1-1-1h-4l-1-1c-1 0-4 0-5 1h-1 0-2c-2 1-2 1-3 1-2 1-5 2-7 2v-1c0-2-2-4-3-6-5-8-11-17-19-22s-18-7-28-5c-8 2-15 8-19 15 3-3 6-6 11-7 2 0 5 0 6 1v1c-2 0-4 1-5 2-7 3-12 9-14 15s0 13 2 17c3 5 8 9 13 11 14 3 7-6 15-11h1c-1 1-2 3-2 4 0 2 0 4 1 5 2 3 7 3 10 3-2 2-4 4-7 5-7 4-15 4-22 1-10-3-18-11-22-20-6-11-5-26 0-36 2-7 6-13 8-19 2-4 2-8 2-12v-1c-1-3-1-5-2-7-10-25-40-36-62-46z" class="I"></path><path d="M307 226h1c1 2 1 3 1 5-1 1-1 2-2 3h0v-8z" class="D"></path><path d="M252 187c1 2 3 4 5 6l-2 2c-2-3-4-4-5-7 0 0 1 1 2 1v1-3z" class="b"></path><path d="M247 272c3-2 6-3 10-4-1 1-1 2-1 3l-4 1h-4-1z" class="d"></path><path d="M412 434c-2-3-3-4-3-7 1-1 1-2 2-2 2 1 2 3 2 4l-1 1v4z" class="Q"></path><path d="M247 257c3 0 5-1 7-2 3-2 5-4 8-5 2 0 3 0 4 1-2 1-6 2-8 2-2 1-3 2-4 3-2 1-5 2-7 2v-1z" class="T"></path><path d="M296 262c2-1 3-3 4-4l1 3v2l-4 4-1-1c-2 0-6 1-8 0h0 1c3 0 5-1 7-2v-2z" class="C"></path><path d="M279 269c2-1 3 0 4-1l5 1h1c1 0 3 0 4 1h-2c1 1 1 1 2 1l1 1 2 2-1 1c-1-1-3-1-5-1-3-3-7-4-11-5z" class="D"></path><path d="M437 451h1c-5 4-10 8-15 11-1-1-1-1-1-2h-1c-2 1-3 1-5 1l1-2c7-1 14-4 20-8z" class="M"></path><path d="M397 446l1-1v-1c-1-2-3-3-4-5l-2-2-1-1h-2c0-1-1-1-2-2-1 0-1 0-2-1v-1l5 2 1 1c1 0 1 0 1 1 1 0 2 1 3 1v1c2 0 3 1 4 2l-1 1 5 6-2 1c-1-1-2-1-4-2z" class="D"></path><path d="M249 169c6 0 11 1 16 5v1l-1-1c-4-3-7-4-13-3-2 0-4 0-6 1-1 2-3 3-3 6-1 0-1 1-1 2l-1-1c0-1-1-2-1-4 2-3 6-5 10-6z" class="W"></path><path d="M344 211c1 7 1 14 1 21-1-2-1-3-2-5-2-2-1-3-2-6h-1l2-3c1-1 1-2 1-4 0-1 0-2 1-3z" class="S"></path><path d="M342 218l2 1v3 1c-1 1-1 3-1 4-2-2-1-3-2-6h-1l2-3z" class="N"></path><path d="M395 423h3 2c2-2 4-2 7-3l-3 4c-2 2-4 3-5 6l-2 2c-1-1-1-1-2-3l2-3v-1c-1-1-2-1-3-1l-3 3-1-1c2-1 2-2 4-3h-1 2z" class="F"></path><path d="M319 259c3 5 6 9 11 14 1 1 5 3 5 4l1 3c-2-1-6-6-8-6-1 0-3-2-4-3s-1-2-2-3c-3-3-4-5-3-9z" class="C"></path><path d="M274 189c3 2 3 4 3 8v1c0 1-1 2-1 4-3 1-5 1-7 0-5-2-10-4-14-7l2-2c5 3 12 8 17 8 1 0 1 0 2-1 1-3-1-8-2-11h0z" class="X"></path><path d="M250 188c-2-2-2-5-2-8 1-2 3-4 5-5s5-1 8 0c5 2 10 6 12 12l1 2h0c-2-4-5-7-9-10-3-2-7-4-11-3-1 0-2 1-3 3s0 5 1 8v3-1c-1 0-2-1-2-1z" class="W"></path><path d="M307 226l-1-4v-2h1c1 0 1 0 2-1h0c-4-1-9-1-12 0h0l25-3c-2 1-4 3-6 4-3 0-6 1-7 4 0 1 0 2-1 2h-1z" class="N"></path><path d="M300 246l1-1c1 2 2 3 1 6v1l-2 6c-1 1-2 3-4 4h-1c-3 1-8 0-11-1s-6-1-8-1l-11-2-1-1 7-1c6 1 20 6 24 5 3-1 5-4 5-6v-1c1-3 1-6 0-8z" class="h"></path><path d="M276 260c2 0 5 0 8 1s8 2 11 1h1v2c-2 1-4 2-7 2h-1 0l-14-3h1c1-1 4 0 6-1h-2-1l-2-2z" class="M"></path><path d="M393 344c2 2 3 3 4 5l10 9h0l1-1 1 3c0 1 1 2 2 3 0-2-1-3-1-5-1-3-4-5-5-8v-1c1 1 3 2 3 4l3 3c2 2 4 7 4 11v2c1 1 1 1 1 2-2-4-6-7-9-11-4-5-10-10-14-16z" class="D"></path><path d="M437 451c-2-2-4-3-6-5-4-3-7-7-10-10-2-2-4-4-4-7h0c6 8 14 14 23 17l2 2-4 3h-1z" class="J"></path><path d="M412 434v-4l1-1c4 6 8 11 12 17 2 3 4 5 6 9-1-1-3-2-5-3-1-1-3-3-4-5-4-4-8-8-10-13z" class="E"></path><path d="M414 373l3 3v-1c2 4 4 8 6 13 0 1 0 1 1 2h0 2l-1 2c-1 0-3 0-4-1h0c-2 0-2-1-3-1-1-1-3-2-4-3-4-2-7-3-10-5l-6-6c1 0 2 0 3 1l3 3 2 1c1 1 2 1 3 2 1 0 1 1 2 2h1c1 0 2 1 3 1 1 1 2 1 3 2s2 0 3 0c-3-3-7-10-7-15zm-15 67c1 1 1 2 2 3 2 3 4 5 6 7 2 3 5 8 9 9-6-1-12-2-16-6 1 0 1 0 1-1-2-3-4-5-7-7l1-1 2 2c2 1 3 1 4 2l2-1-5-6 1-1z" class="Q"></path><path d="M403 447c1 2 3 3 3 5v1c-2-1-3-2-4-4l-1-1 2-1z" class="F"></path><path d="M286 192h0c-2-2-2-5-4-7-3-9-8-14-16-19v-1l2 1c2 2 4 2 7 3 2 1 5 1 7 1 0 2 0 1 1 2 0 1 0 2 1 2h-1c-1 0 0 0-1-1l-1 1 2 2h0v3l1 2c0 1 1 2 1 3v1c1 2 1 2 1 4v3z" class="Y"></path><path d="M313 180c2 0 5 0 7 1 9 1 19 7 25 13v2 1c1 1 2 2 2 3v1c-2-2-4-5-6-7-9-9-20-11-32-11l-2-1 6-2z" class="X"></path><path d="M300 246c-2-8-4-15-4-22v-1c2 0 3 0 4 1 5 5 4 11 5 17 1 3 2 6 2 9l-3-6-6-6 3 7-1 1z" class="F"></path><path d="M293 198c2-2 5-4 9-5-2 2-4 3-5 5l2 1c-2 2-4 4-4 7 0 0-3 4-3 5-4 10 1 23 6 33h-1l-3-4c-5-11-7-25-4-37 1-1 2-3 3-5z" class="M"></path><path d="M293 198c2-2 5-4 9-5-2 2-4 3-5 5s-3 3-5 5v1c-1 0-1 0-2-1 1-1 2-3 3-5z" class="S"></path><path d="M248 264c-1 0-2 1-3 2v-1c4-4 13-8 19-8l1 1 11 2 2 2h1 2c-2 1-5 0-6 1h-1c-7 0-15 0-22 3-2 1-5 2-7 4l-1-1 4-4h0v-1z" class="Y"></path><path d="M248 264c5-4 13-6 18-6v1 1 1h-2c-4 1-8 2-12 4v1c-2 1-5 2-7 4l-1-1 4-4h0v-1z" class="C"></path><path d="M397 381c1 2 3 4 5 5 3 4 5 8 7 12 1 2 2 4 4 6v1c1 2 0 4 0 7v1c1-1 3-2 4-4 0-1 1-3 2-4v-1l1-2 3-3h2c2-1 4 0 7 0 0 1 1 1 1 2v1h0v1h-1l-1-1h0c-2-1-5 0-7 0l-5 5c-2 3-4 6-7 8v-2h-1s-1-1-1-2h1c0-1 0-1-1-2 0-1-1-1-2-2v-2l-2-2c0-2-1-3-1-4 1 0 1 0 2 1v1h1c-2-3-5-6-6-8l1-1-1-1v-1c-1-1-2-2-3-4l-2-3v-2z" class="Q"></path><path d="M403 438c-1-1-2-2-1-3s2-1 3-1h1l3 3 4 4 12 12-4 4-2-1c-6-4-12-12-16-18z" class="T"></path><path d="M408 438l1-1 4 4c0 1 1 2 1 3h1l-1 1c-2-2-5-4-6-7z" class="P"></path><path d="M403 438c-1-1-2-2-1-3s2-1 3-1h1l3 3-1 1-2-1h0c0 1 0 1 1 2 1 2 2 3 2 5-1-2-3-3-4-4l-2-3v1z" class="E"></path><path d="M302 193c2 0 4-1 6-1 2-1 8-1 10-1 1 1 2 2 3 2 1 1 8 5 9 4 1 3 2 4 4 6 2 3 3 6 4 9h-1c0 2-2 4-4 6v-1l1-1c1-1 2-3 2-5 0-5-4-8-7-10-7-4-12-5-21-4-6 2-9 4-13 9 0-3 2-5 4-7l-2-1c1-2 3-3 5-5z" class="W"></path><path d="M308 197v-1h3c1-1 1-2 2-2 4-1 12 4 16 5v2c-7-4-12-5-21-4z" class="b"></path><path d="M302 193c2 0 4-1 6-1 2-1 8-1 10-1 1 1 2 2 3 2-7-1-13 0-19 4-1 1-2 1-3 2l-2-1c1-2 3-3 5-5z" class="N"></path><path d="M345 194c5 6 8 11 10 18 1 4 1 9 3 13 2 5 6 9 8 14v3c-4-5-6-10-11-15-4 3-6 8-7 12-2-2-4-4-5-7v-1c-1-1-1-1-1-2-1-2-3-4-4-6h-3c1-1 1-1 2-1l3-1h1c1 3 0 4 2 6 1 2 1 3 2 5l1 2h1c2-3 4-6 5-10 3-7-2-17-5-23v-1c0-1-1-2-2-3v-1-2z" class="M"></path><path d="M328 274c2 0 6 5 8 6 21 18 44 34 60 55 4 5 6 10 9 14v1l-13-16c-20-22-44-39-64-60z" class="G"></path><path d="M401 396c-1-3-3-6-5-9l-13-25c-2-3-4-7-6-10-1-1-2-3-3-5l-1-3c-2-2-3-5-5-7l-6-9-2-4-1-1-3-5-18-22 1-1h0c1 2 3 3 4 4 2 3 5 6 7 9l3 4 7 9c1 1 2 3 3 4s2 2 2 3c1 1 1 2 2 3l2 4 1 1 1 1c0 1 1 1 1 2s0 1 1 1l13 24c3 5 6 14 10 17h1v2l2 3c1 2 2 3 3 4v1l1 1-1 1c1 2 4 5 6 8h-1v-1c-1-1-1-1-2-1 0 1 1 2 1 4l2 2v2c1 1 2 1 2 2 1 1 1 1 1 2h-1l-1-2-8-13z" class="E"></path><path d="M293 270l1-1c3 0 6-1 10-2 1-1 4-3 5-2l-1 2c1 3 3 4 5 6 9 9 17 18 25 28 17 23 29 49 45 72h-1l-20-31 2 5h-1l-13-23c-10-15-21-30-32-43-3-4-6-8-10-10l-1-1h-1c-3-2-10 0-13 1-1 0-1 0-2-1h2z" class="G"></path><path d="M293 198c0-2 0-4 3-5l1-1h0c3-2 8-4 12-4h1c8-1 16 2 22 7 6 4 11 9 12 16-1 1-1 2-1 3 0 2 0 3-1 4l-2 3-3 1c-1 0-1 0-2 1h0c-2 0-3 0-4-1 2-1 3-3 6-5 0-2 1-3 0-5h1c-1-3-2-6-4-9-2-2-3-3-4-6-1 1-8-3-9-4-1 0-2-1-3-2-2 0-8 0-10 1-2 0-4 1-6 1-4 1-7 3-9 5z" class="E"></path><path d="M337 217v1l2-2 1 1c-1 2-2 3-3 5-1 0-1 0-2 1h0c-2 0-3 0-4-1 2-1 3-3 6-5z" class="T"></path><path d="M330 197c3 1 5 3 7 5 1 1 2 3 2 5h0v7c-1-1-1-1-1-2h0c-1-3-2-6-4-9-2-2-3-3-4-6z" class="i"></path><path d="M370 327l-39-44c2 1 4 2 6 4l23 25 25 23c3 3 5 7 8 9 4 6 10 11 14 16l-1 1-15-16-2-1 8 11c2 1 4 2 4 4h-1l-4-4-3-3c0 1 0 1 1 2h-1l-6-7c0-3-14-17-17-20z" class="N"></path><path d="M389 344c-2-1-4-3-5-4l-9-11h0 1 0a57.31 57.31 0 0 1 11 11c1 1 3 3 4 5l-2-1z" class="S"></path><path d="M252 187c-1-3-2-6-1-8s2-3 3-3c4-1 8 1 11 3 4 3 7 6 9 10 1 3 3 8 2 11-1 1-1 1-2 1-5 0-12-5-17-8-2-2-4-4-5-6zm118 140c3 3 17 17 17 20l6 7h1c-1-1-1-1-1-2l3 3 4 4h1c0-2-2-3-4-4l-8-11 2 1 15 16 1-1c3 4 7 7 9 11 0 1 1 3 1 4v1l-3-3c0 5 4 12 7 15-1 0-2 1-3 0s-2-1-3-2c-1 0-2-1-3-1h-1c-1-1-1-2-2-2-1-1-2-1-3-2l-2-1-3-3c-1-1-2-1-3-1 0 0-1 0-1-1v-1l-2-2c0-2-1-3-1-4v-1l-1-1c0-2 0-3 1-4-1-1-2-1-2-2-1 0-1-1-2-1-2 0-2-2-4-4-1-1-1-2-2-4l-5-7-2-3c-1-1-1-2-2-3 0-2-1-4-2-6 0 0 0-1-1-1l-2-4z" class="I"></path><path d="M407 360c3 4 7 7 9 11 0 1 1 3 1 4v1l-3-3c-2-4-5-8-8-12l1-1z" class="E"></path><path d="M370 327c3 3 17 17 17 20h0c-2 0-3-2-4-3-2-2-4-5-6-7l3 6s0 1 1 1c1 1 1 2 2 4l1 2c1 1 1 2 2 3 1 2 2 3 4 4 1 1 1 1 3 2l2 2-1 1c-1-1-2-1-2-2-1 0-1-1-2-1-2 0-2-2-4-4-1-1-1-2-2-4l-5-7-2-3c-1-1-1-2-2-3 0-2-1-4-2-6 0 0 0-1-1-1l-2-4z" class="C"></path><path d="M395 361h3c1 0 1 0 2 1h3c1 1 1 2 2 4l10 17v3c-1 0-2-1-3-1h-1c-1-1-1-2-2-2-1-1-2-1-3-2l-2-1-3-3c-1-1-2-1-3-1 0 0-1 0-1-1v-1l-2-2c0-2-1-3-1-4v-1l-1-1c0-2 0-3 1-4l1-1z" class="B"></path><path d="M293 271c3-1 10-3 13-1h1l1 1c4 2 7 6 10 10 11 13 22 28 32 43l13 23h1l-2-5 20 31h1c1 1 2 2 2 4l7 9c1 0 1 0 2 1h0c1 2 2 5 4 6s2 2 3 4v-1l8 13 1 2c0 1 1 2 1 2h1v2l-2 1-1 1c-2 1-3 1-4 2-3 1-7 2-10 4h-2-1c-2 1-3 2-5 3-1 0-3 1-5 0-1 0-2-1-3-2v2 2h0l-3-2c-1-2-2-6-3-8-3-9-7-17-10-26v-2l-2-3 1-1v1h1l12 25c0-2-1-7-1-8-1-1-2-2-2-3 0-2 0-3-1-4l-4-9v-1c0-1 0-1-1-2v-1-1l-1-2h0l-1-1 1-1h0l2 1v-2l-10-22-5-7c-1-3-3-6-4-8-3-4-7-7-11-9 1-1 1-1 2-1h0l-3-6c0-3-1-6-2-9v-1c-2-8-11-8-17-12 2 0 5 1 7 1h1-1c-1-2-2-3-3-5-2-1-4-6-5-7-4-6-9-10-14-15-2-1-4-3-6-3l-2-2-1-1z" class="I"></path><path d="M388 403l5 10h0v3h-1c-1-2-2-3-3-5v-2c-1-2-1-4-1-6z" class="C"></path><path d="M338 317h2v2l1 1c4 2 6 6 8 11l-1 1v-1c-1-2-1-3-2-4l-1 1c1 1 1 2 2 4l1 2-1 1c-3-4-6-11-8-15l-1-3z" class="F"></path><path d="M294 272c4 2 8 4 11 7 3 2 5 4 7 5a30.44 30.44 0 0 1 8 8c3 4 7 8 9 12l-2-1c-1-2-3-3-4-4h0-2c-2-1-4-6-5-7-4-6-9-10-14-15-2-1-4-3-6-3l-2-2z" class="d"></path><path d="M321 299h2 0c1 1 3 2 4 4l2 1c3 3 5 6 6 11 2 4 2 9 4 14 3 4 7 8 10 12l9 15h-1l-5-7c-1-3-3-6-4-8-3-4-7-7-11-9 1-1 1-1 2-1h0l-3-6c0-3-1-6-2-9v-1c-2-8-11-8-17-12 2 0 5 1 7 1h1-1c-1-2-2-3-3-5z" class="Y"></path><path d="M375 374c-1-1-1-2-2-3h0l-1-1c0-1-1-1-1-2 0-3-2-5-3-7l1-2 1 1c1 1 1 0 1 1 1 1 1 2 2 3v1c1 1 2 2 3 4 0 0 1 1 1 2l9 13c0 1 1 1 1 2 3 5 7 9 10 13h0c0-2-2-4-4-6-1-1-1-3-2-4-1-2-3-4-4-6 0-2-2-4-3-5s-2-3-2-5h1c1 1 2 2 2 4l7 9c1 0 1 0 2 1h0c1 2 2 5 4 6s2 2 3 4v-1l8 13 1 2c0 1 1 2 1 2h1v2l-2 1-1 1c-2 1-3 1-4 2l-1-1v1h-3l-21-33c-2-3-4-5-5-8v-1c0-1 0-1-1-2h0l1-1z" class="F"></path><path d="M392 386c1 0 1 0 2 1h0c1 2 2 5 4 6 1 3 1 5 3 6l3 6c-3-3-5-7-8-10 0-1-1-2-1-2-1-3-2-5-3-7z" class="D"></path><path d="M409 417c-1 0-2 0-3-1l1-1c0-2-2-4-3-5s-1-2-2-3v-1l4 4h1 1l1-1 1 2c0 1 1 2 1 2h1v2l-2 1-1 1z" class="C"></path><path d="M398 393c2 1 2 2 3 4v-1l8 13-1 1h-1c-1-2-2-3-3-5l-3-6c-2-1-2-3-3-6z" class="B"></path><path d="M375 374c-1-1-1-2-2-3h0l-1-1c0-1-1-1-1-2 0-3-2-5-3-7l1-2 1 1c0 2 1 3 1 4l1 1 5 8c0 1 1 2 1 3 1 1 2 2 2 4 1 2 2 4 3 5l1 3h-1c0-1-1-1-1-2-1-2-1-3-2-4s-1-2-2-3c0-1 0 0-1-1v-1l-2-3z" class="C"></path><path d="M375 378v-1c0-1 0-1-1-2h0l1-1 2 3v1c1 1 1 0 1 1 1 1 1 2 2 3s1 2 2 4c0 1 1 1 1 2h1c1 1 1 2 2 4h1c0 1 0 1 1 2s1 1 1 2l1 2 5 6c1 1 1 3 2 4l1 1h0c1 1 2 2 3 4 0 1 0 0 1 1 1 2 1 2 1 4l1 1h-3l-21-33c-2-3-4-5-5-8z" class="B"></path><path d="M388 403l-21-36c-3-4-8-11-8-16 1 2 2 5 4 7l6 9c2 4 3 7 6 10v1c1 3 3 5 5 8l21 33h3v-1l1 1c-3 1-7 2-10 4h-2-1c-2 1-3 2-5 3-1 0-3 1-5 0-1 0-2-1-3-2v2 2h0l-3-2c-1-2-2-6-3-8-3-9-7-17-10-26v-2l-2-3 1-1v1h1l12 25c0-2-1-7-1-8-1-1-2-2-2-3 0-2 0-3-1-4l-4-9v-1c0-1 0-1-1-2v-1-1l-1-2h0l-1-1 1-1h0l2 1v-2l-10-22h1 0l35 65 7-1c-2-3-5-5-7-7l-5-10z" class="N"></path><path d="M363 392c1 1 1 0 2 1v2c1 1 2 3 2 4l5 11c3 5 5 10 8 14h-1v2 2h0l-3-2c-1-2-2-6-3-8-3-9-7-17-10-26zm4-14c4 9 9 17 13 25 2 4 6 9 7 14 1 2 2 3 3 3l1 1c0 1-7 3-9 3-1-2-3-4-4-6-2-2-2-4-3-6 0-2-1-7-1-8-1-1-2-2-2-3 0-2 0-3-1-4l-4-9v-1c0-1 0-1-1-2v-1-1l-1-2h0l-1-1 1-1h0l2 1v-2z" class="D"></path></svg>