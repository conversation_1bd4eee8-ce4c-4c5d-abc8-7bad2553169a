<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="150 60 742 900"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#3f3e41}.C{fill:#a9a8a9}.D{fill:#cbcaca}.E{fill:#2d2d31}.F{fill:#4e4e56}.G{fill:#7a7a7f}.H{fill:#313134}.I{fill:#bab8b9}.J{fill:#5d5d61}.K{fill:#454448}.L{fill:#76767d}.M{fill:#69696b}.N{fill:#454549}.O{fill:#272628}.P{fill:#1b1a1c}.Q{fill:#9e9da1}.R{fill:#b0afaf}.S{fill:#c6c5c6}.T{fill:#585658}.U{fill:#8e8c8d}.V{fill:#2b2b2f}.W{fill:#626269}.X{fill:#a6a4a4}.Y{fill:#131315}.Z{fill:#86878d}.a{fill:#dee2e5}.b{fill:#959394}.c{fill:#020202}.d{fill:#777985}.e{fill:#f6f5f4}.f{fill:#60626d}.g{fill:#222227}.h{fill:#cbcac8}.i{fill:#0e0e0e}.j{fill:#dddddc}.k{fill:#f4f4f1}</style><path d="M816 320h2c0 1 0 1-1 2-1 0-1 0-1-1v-1z" class="e"></path><path d="M368 324c1 1 2 2 4 2l-2 2c-1-1-2-2-2-3v-1zm5 27c0-2-1-4 0-5l2 1v1l-1 1s-1 1-1 2z" class="D"></path><path d="M359 118h0v8 1c-1-3-1-6 0-9z" class="R"></path><path d="M629 200h1v1l-3 2c0-1-1-1-1-1 1-2 2-2 3-2zm31-23h2c1 0 1 0 2 1l-1 1h-2l-1 1v-1-2z" class="D"></path><path d="M811 521h2c0 1-1 2-2 2h0l-3-1 3-1z" class="N"></path><path d="M787 516l4 1 1 3-3-1-2-3z" class="E"></path><path d="M796 518l7 2c-1 0-2 0-4 1h-2l-1-3z" class="N"></path><path d="M791 517l5 1 1 3-5-1-1-3z" class="K"></path><path d="M678 131h1v5 3l-1-1-2-2h0l1 1 1-1v-5z" class="D"></path><path d="M311 177l3-3v5c-1 1-1 2-2 2 0-1 0-3-1-4z" class="I"></path><path d="M702 592h3c2 1 4 1 5 2l-5 2c-1-1-1-2-1-3l-2-1z" class="i"></path><path d="M485 421l-8 5v1c2-4 3-6 6-8l-1 2h3z" class="H"></path><path d="M290 558l2 1 3 1h-3c-2 0-6 1-8 1l6-3h0z" class="D"></path><path d="M359 146c1 0 1 0 2 1l1-1c1 1 1 2 2 3h0c-2 0-3 1-4 1l-1-4z" class="S"></path><path d="M759 188v1h0v1c0 2 0 8-1 10h0c-1-3 0-6 0-9 0-1 0-2 1-3z" class="e"></path><path d="M669 155c2 1 5 2 8 4v2l-2-1-6-5z" class="C"></path><path d="M617 408c1 1 2 1 3 2 1 2 2 4 3 5h-3c-1-2-2-4-3-7z" class="Q"></path><path d="M803 520c3 0 5 1 8 1l-3 1h-2l-7-1c2-1 3-1 4-1z" class="K"></path><path d="M806 522h2l3 1-4 3c-1-1-3-1-4-2l3-2z" class="E"></path><path d="M499 490l8-1-7 4c-1 0-1-1-1-2v-1zm-74-343h6v1l-11 2v-1h1l1-1h2-2v-1h3z" class="B"></path><path d="M425 410c0 1 0 3 1 4h-2v1c2 1 3 1 5 1v1l-1 1c-2-1-5-2-7-3 1-2 2-3 4-5z" class="D"></path><path d="M370 391c-1-2-1-2-1-4 1-1 1-3 2-3 1 1 1 1 3 2l-4 5z" class="I"></path><path d="M694 107c-1-4-2-7-2-11 1 2 2 4 3 7 0 3 1 6 1 9l-1-1c0-2 0-2-1-4z" class="Q"></path><path d="M609 471h1c1 1 2 2 3 4l-2 1h0c-1 0-1 0-2-1v-1-1h-1c0-1 0-1 1-2z" class="R"></path><path d="M617 389l7 8h-5c-1-2-2-6-2-8z" class="Z"></path><path d="M674 197c-1-1-1-1-3 0v-1h-1c-2-1-2-2-3-3 1-1 2-1 3-1v1l1 1h0c1-1 1-1 2-1 0 1 0 1-1 2h1 1l1 1-1 1z" class="e"></path><path d="M624 144l13 2 1 2h0l-12-2-2-2z" class="C"></path><path d="M787 534c1 0 1-1 2-2 2 0 3-1 5-1l2 1-10 5 1-3z" class="B"></path><path d="M483 419c4-1 8-1 12-2-3 2-7 3-10 4h-3l1-2z" class="O"></path><path d="M429 417c4-2 7-3 10-6-2 3-5 7-8 8l-3-1 1-1z" class="L"></path><path d="M381 369l1-8 3 6c-1 2-1 3-2 5v-1l-2-2z" class="T"></path><path d="M437 98h8l-16 6-1-1c2-1 3-1 4-2 2 0 4-1 5-3z" class="J"></path><path d="M822 201h2v1c0 1-1 4-2 5l-4 2-1 1c1-2 3-4 3-6 1-1 1-2 2-3z" class="h"></path><path d="M506 437l3 1c-1 2-7 3-10 4-2 1-3 2-5 3 2-4 8-4 11-8h1z" class="J"></path><path d="M754 199h1c0 1 1 2 1 3v1c0 2 1 5 0 8l-1 8-1-2v-1c1-3 1-6 1-9-1-1-1-2-1-3l1 1 1-1c-2-2-1-2-2-5z" class="D"></path><path d="M273 581l-5-3v-1l2-1c2 1 4 1 5 1h4v1h-3v1h-1c-1 1-2 1-2 2z" class="g"></path><path d="M705 169c-1-2-3-4-4-6 3 1 9 3 11 6v1h-1c-2 0-2-2-3-3h-1-1l-1 2z" class="V"></path><path d="M782 534h1l1 1c1 0 2 0 3-1l-1 3-11 4v-2c2-1 5-2 7-4v-1z" class="N"></path><path d="M517 507v-2h0v-1c2 1 3 0 5 0 1-2 2-3 4-4 1 0 1 1 1 1v1 2c-4 1-7 2-10 3z" class="e"></path><path d="M600 104l-18-6h9c3 3 6 4 10 5l-1 1zm-154 41l4 1h-1l-18 2v-1c5-2 10 0 15-2z" class="K"></path><path d="M443 166h6l-20 9c0-2 3-2 5-3 3-2 6-3 9-6z" class="T"></path><path d="M666 366v-3l-2-1c0-1-1-2-1-3h-1c-1 1-1 2-2 2h-1c1-1 2-2 2-3h1c1-1 1-2 2-3 1 2 2 4 2 6h0c1 2 1 3 0 4v1z" class="h"></path><path d="M608 391c2 0 3 1 4 1 0 3-1 5-1 7v4l1 1h1-2-1c-1-2 0-4-1-7 0-2 0-4-1-6z" class="G"></path><path d="M821 470l1 1-9 5s-2 1-3 1l-1-1 2-2c2 0 3-1 4-2h0c2-1 4-2 6-2zm-318-8l5 1c-4 3-8 5-11 8 0-2 2-5 3-7 1 0 2-1 3-2z" class="J"></path><path d="M375 178c1-1 4-1 6-1l1 1h4c1 0 3-1 5 1 1-1 1 0 2-1h5v-1c1-1 2-1 3-1h0c-1 1-2 1-3 2l2 2c-1 1-1 1-2 1-4-1-7-3-11-3h-6-6z" class="a"></path><path d="M825 416l4-3c3 1 6 0 8 2-2 1-5 3-8 3v-1l-4-1zm-649 34l-1-1c2-2 11 0 14 0v1l1 1h1-6 0c-3-1-7-1-9-1z" class="F"></path><path d="M746 134h0c1 1 2 3 2 4 0 4 1 8 2 11h1 0v14c0 1 0 1-1 2-1-11-1-21-4-31z" class="D"></path><path d="M791 216l1 1v2 3c-1 1-1 1-1 2v1c-1 1 0 2-1 2v1l1 1c2 1 2 0 4 0l1 1-2 2c-2 1-2 1-3 2s-1 2-1 3l-1 1c1-1 1-1 1-2 0-2 0-2 1-3-2-4-1-7 0-12v-1-4z" class="a"></path><path d="M537 434c-4-4-8-9-12-12-2-2-5-3-7-5 4 1 7 2 11 4-1 1-1 1-1 2l9 10v1z" class="K"></path><path d="M165 334c-3-5-8-12-8-18l2 2 3 5c1 3 2 7 3 10v1z" class="W"></path><path d="M270 576l21-7c-2 2-3 2-6 3l-3 1 1 1h0c-2 1-5 3-8 3-1 0-3 0-5-1z" class="H"></path><path d="M611 438h1l-1 3v2c-1 1-1 2 0 4h0l1-1 1 1h0c-1 0-1 0-1 1l1 2c-2 1-3 3-5 3 0 1-1 2-2 2h-1c2-2 4-5 4-8 1-2-1-2 0-4 1-1 1-1 1-2 1-1 1-2 1-3z" class="e"></path><path d="M435 144l11 1c-5 2-10 0-15 2h-6c2-1 4-2 6-2s2 0 4-1z" class="M"></path><path d="M676 626h2c4 2 12 2 16 1l1 1c-4 1-8 1-12 0-4 0-9-2-12 0l-1-1 1-1c1 0 3 0 4 1h2 0l-1-1z" class="B"></path><path d="M651 392c1 2 2 3 2 5v1c-1 2-2 3-3 5-1 1-3 2-4 4 0 0-1 1-2 1l-3-1v-1h2l1 1 2-1c1-1 2-3 3-4h-1 0c1-2 2-3 3-4-1-2-1-3 0-5v-1z" class="C"></path><path d="M426 100l11-2c-1 2-3 3-5 3-1 1-2 1-4 2-3 1-5 2-9 2 3-2 5-3 7-5z" class="W"></path><path d="M531 418c2-1 5 0 7 0 6 1 9 5 12 9-2-2-4-3-7-5-4-1-8-2-12-4z" class="O"></path><path d="M349 193c4-2 5-2 9-1-4 2-8 4-12 7h0l1-1c0-1 1-1 1-2h-1-1l3-3z" class="R"></path><path d="M165 344h0c-2-3-4-5-5-8v-1c2 0 3 1 4 1 0 2 1 2 2 3 1 2 2 3 2 5l1 1-4-1z" class="X"></path><path d="M379 378c1 0 1-1 2-1l1 1c-1 0-2 1-3 2v3l-8 11v-1l-1-2 4-5 5-8z" class="M"></path><path d="M530 485h0v4c-1 1-1 2-1 3v3l-6-3c-1 0-1-1-2-1v-1l7-1c2-1 2-2 2-4z" class="J"></path><path d="M523 492h4 2v3l-6-3z" class="M"></path><path d="M635 248c-6-2-12-3-19-3 2-2 4-3 7-4v2c5 2 9 0 12 5z" class="V"></path><path d="M694 107c1 2 1 2 1 4l1 1h0c1 3 1 9 0 11-1 0-1 0-2-1v-15z" class="R"></path><path d="M857 338l7-2c-1 2-2 4-3 5l-1 2h0c-1 0-2 0-2 1h-2-1c1-2 2-4 2-6z" class="C"></path><path d="M521 462h1c2 1 4 1 6 1 1 3 2 4 2 7v1c-3-4-8-5-11-8l2-1z" class="F"></path><path d="M342 130h1c0 2 0 2-1 4h0v1l2-2c1-1 0-1 1-1h1l-1 1-2 2c-2 2-5 5-8 6-1 1-3 2-5 3h0l-1-1c4-2 12-7 13-11v-1-1z" class="a"></path><path d="M600 143c8-1 16 1 24 1l2 2c-2 0-5-1-8-1l-18-2z" class="U"></path><path d="M670 192h6l-1 1c1 1 0 1 1 1 2 0 3 1 4 2 2 2 2 4 3 5-3 0-4-3-6-4h-2-1l1-1-1-1h-1-1c1-1 1-1 1-2-1 0-1 0-2 1h0l-1-1v-1z" class="I"></path><path d="M182 456h1c1 1 2 1 3 1 2 1 3 1 4 3v1c1 1 2 1 2 2l2-1 1 1 1 2-1 1h1v1h-1c-4-2-9-7-13-11z" class="B"></path><path d="M192 463l2-1 1 1 1 2c-2 0-2-1-4-2z" class="O"></path><path d="M831 427c1 0 2-1 3-1v1c-2 3-4 6-7 10l-2 2v-1l1-1h-1c-1 0-1 1-2 2h-1v-1l1-1 2-2 4-4 2-4z" class="E"></path><path d="M644 362c1-3 1-6-1-9h0c0-1 0-2 1-3l3 3c1 2 3 3 4 5l1 2c1 2 1 3 2 4-1-1-1 0-1-1l-3-4-3-1h-1c-1 0-1 3-1 4h-1z" class="C"></path><path d="M176 450c2 0 6 0 9 1h0-1c1 2 2 3 3 4l-1 2c-1 0-2 0-3-1h-1c-2-1-5-4-6-6z" class="E"></path><path d="M790 179h1l1 1c-1 0-1 1-1 1-2 1-4 1-5 2l-1 1v3 1 1c-1 1-1 2-1 3v3c-1 1-1 0-1 1v1h-1 0 0v-2-3c0-1 0-2 1-3v-3c0-1 0-2 1-3v-1c0-1 0-1 1-2h0c2 1 3 0 5-1z" class="a"></path><path d="M848 305l3-3c-2 4-4 7-7 10-1 1-2 2-3 2h0l-6 4c1-4 5-5 7-8h0l1-2 5-3z" class="W"></path><path d="M385 367c1 3 2 5 3 7l-2-1-1 1c0 1-1 2-2 3l-1 1-1-1c-1 0-1 1-2 1l1-3 1-6 2 2v1c1-2 1-3 2-5z" class="f"></path><path d="M380 375c2-1 2-1 3-3v5l-1 1-1-1c-1 0-1 1-2 1l1-3z" class="T"></path><path d="M741 572c6 2 12 3 18 5l-7 4h-1v-1h-1c0-2-1-2-2-3l1-1c-1 0-2 0-2-1h0c-2-1-3-1-5-3h-1z" class="K"></path><path d="M705 169l1-2h1 1c1 1 1 3 3 3h1c1 1 0 3 0 5 1 2 1 4 1 6-2-4-5-8-8-12z" class="O"></path><path d="M715 173c0-2-1-3-1-5 0 0 0-1-1-1v-1c-1-2-3-4-4-6l-2-5c0-1 1-2 0-2v-3l1 1v1c1 1 1 2 2 3l5 11c0 2 1 4 1 6l-1 1z" class="D"></path><path d="M579 473c3-2 6-5 10-7 0 1 0 0-1 1 1 1 1 1 1 2l3 4c-1 0-1 1-2 1v1c-1 1-2 2-3 2l1-1v-1l1-1h-3l1-1 2 1v-1h-1c-2-1-3-1-5-1-1 1-1 1-3 1h-1z" class="C"></path><path d="M846 337c3-3 6-6 8-9 1-3 3-5 5-7 0 3-1 4-2 6 0 2-1 3-2 4-2 3-4 5-7 6h-2z" class="S"></path><path d="M795 497c2-1 3-4 5-4-5 6-10 11-17 15h-3 1c1-1 2-1 3-2l-1-1-2 1h-3-1c3-1 6-3 10-3 1-1 2-1 4-2l4-4z" class="g"></path><path d="M491 444c1-1 2-2 3-2 2-2 4-2 6-4 1-1 3 0 5-1-3 4-9 4-11 8l-4 5-2-1c1-3 1-3 3-5z" class="G"></path><path d="M647 353h2c1 0 1-1 2-1-1-1-1-1-1-2h1c2 2 2 3 3 6v2c1 1 1 2 1 3v1c0 1 1 2 1 3h-1v1l-1-2c-1-1-1-2-2-4l-1-2c-1-2-3-3-4-5z" class="e"></path><path d="M469 438h0c1 0 1 1 2 2l-11 13-1 1h0c1-7 5-12 10-16z" class="D"></path><path d="M171 293c-1-1-2-2-2-3h-1v-1h1c1 1 2 1 2 3h0c4 1 5 4 8 5l2 2 5 5h1 1v1l-1 1h-2l-8-7c-2-2-4-6-6-6z" class="S"></path><path d="M411 388h0c0-1 0-3 1-4 2 0 7 2 9 4l-1 1c-1 0 0-1-1 0-2 1-5 2-7 3l-1-1v-3z" class="D"></path><path d="M273 581c0-1 1-1 2-2h1v-1h3c0 1-1 2-1 3 1 1 4 3 6 3h1 1 0 1c1 1 3 2 5 2l1 1h-1l3 1-1 1c-8-2-15-5-21-8z" class="E"></path><path d="M620 415h3l1 1 3 3c0 1 0 2-1 3l1 1c2-1 2-5 4-4l-2 7-1 3-8-14z" class="C"></path><path d="M651 350l1-1c2 3 3 5 3 8l1 1c1 2 0 4 1 6l4 7v4h-1l-1-2-2-2c0-1-1-2-2-3v-2-1h1c0-1-1-2-1-3v-1c0-1 0-2-1-3v-2c-1-3-1-4-3-6z" class="h"></path><path d="M646 386c-4-2-8-4-11-7 1-1 1-2 2-3 1 0 2 0 3 1h2v1h3c1 3 0 5 1 8z" class="S"></path><path d="M847 302c3-2 4-6 7-8 4-3 8-10 10-15 1-3 1-8 3-10v3 1c-3 11-11 19-15 29h-1l-3 3-1-3z" class="C"></path><path d="M375 178h6 6c0 1 1 1 1 2l2 1 6 2h-1-1-1c-3 0-7-1-10-2h-7l-2-2 1-1z" class="h"></path><path d="M381 178h6c0 1 1 1 1 2l2 1-6-1h-1l-2-2z" class="D"></path><path d="M362 634l1-1h2l4 11c2 4 4 7 6 11h0c-1 0-2-1-2-1h0l1 4 1 1v2l1 1 1 2v1l1 2v1c1 1 1 1 1 2-2-3-3-6-5-9l-10-24c-1-2-1-2-3-3h1z" class="I"></path><path d="M419 476l-1-2v-1h1l-1-1 1-1c1-1 1-1 1-2v-1c-1-1-2-1-4-2 0-1-1-1-1-1 1-2 0-1 2-2l1 2h-1c1 0 2 0 2 1l1 1 1 1c1 1 2 1 1 2l-1 1v1c1 0 1 0 2-1h2c0-1-1-2-1-3s0 0-1-1c0-1-1-1-2-1v2-1l-1-1h0l1-2c1 1 1 0 2 1h0l4 8c-1 1-2 1-3 2l-2-2-2 2 1 1-1 1v-1h-1z" class="R"></path><path d="M497 474l6 1h4v1c-3 2-5 4-7 7l-1 2-2-3v-8z" class="K"></path><path d="M497 474l6 1c-1 1-4 3-5 6l2 2-1 2-2-3v-8z" class="M"></path><path d="M846 408c1 0 4-2 6-3h0c-3 3-10 9-15 10-2-2-5-1-8-2l4-3c2-1 2-1 3-1v1c1 1 2 1 4 1h1l5-3z" class="J"></path><path d="M530 485h-1c-3-4-6-7-10-10 4 0 8-1 12 0l-1 3h-1v-2h-1c0 2 2 4 3 6 0 1 0 2-1 3h0z" class="B"></path><path d="M637 407h1l-3 3-3 9h-1c-2-1-2 3-4 4l-1-1c1-1 1-2 1-3l-3-3-1-1c-1-1-2-3-3-5 3 0 5 4 7 6l2-1 1-1 1 2c1-1 1-2 2-3 1-3 2-4 4-6z" class="U"></path><path d="M629 415l1-1 1 2-1 2h-1l-2-2 2-1z" class="J"></path><path d="M383 377c1-1 2-2 2-3l1-1 2 1 3 3 1 1c-1 1-2 1-3 2s-1 2-2 3c-2 0-4-4-6-3l-1 2-1 1v-3c1-1 2-2 3-2l1-1z" class="S"></path><path d="M334 97h0 1c-2 9 0 18-1 26h-2-1c-1-3 0-5 0-8 0-6 1-12 3-18z" class="Q"></path><path d="M581 145l8-1c1 0 2 1 3 1 4 0 8 1 12 2-3 1-4 1-6 0-2 0-3 0-4 1-2 0-4 0-5-1l-12-1h0c1-1 3-1 4-1z" class="T"></path><path d="M581 145l8-1c1 0 2 1 3 1l-1 1h-7c-1-1-1-1-2-1h-1z" class="M"></path><path d="M662 276l-3-3 15 12v1h-2c0 1 0 2 1 4l-1 2-4-6c-2-3-4-6-6-10z" class="B"></path><path d="M662 276c3 1 4 3 5 6 1 1 1 3 1 4-2-3-4-6-6-10z" class="J"></path><path d="M775 353c7-5 15-9 22-13l2 1-11 7-5 3c-1 1-3 1-3 2l-2 1-4 1 1-2z" class="E"></path><path d="M432 489h0c1 1 1 1 1 2s1 2 1 2c1 0 1 1 2 0v1c0 1 0 1-1 1v1l1-1h0l1 1h1 1c1-1 3-1 4-1 1 1 2 2 2 4l-1 1c-2 0-4 0-6 1-2 0-2 0-4-1v-2c-1-3-1-6-2-8v-1z" class="R"></path><path d="M434 498c0 1 1 1 2 2l2-1h1l3-3c1 0 1 2 3 3l-1 1c-2 0-4 0-6 1-2 0-2 0-4-1v-2z" class="X"></path><path d="M807 451c2-1 3 0 5 0l10-1 24-2-1 2h-3l-18 2h-8c-2-1-6-1-9-1z" class="L"></path><path d="M488 443l-8 6c1-2 2-3 3-5v-1c2-1 3-1 4-2 0-1 1-2 2-2 3-2 13-2 17-2h-1c-2 1-4 0-5 1-2 2-4 2-6 4-1 0-2 1-3 2-1-1 0-1-1-2l-2 1z" class="C"></path><path d="M448 501l1-1-1-1v-3l-1-2v-1l2 1c1-1 2-2 4-2l3 6-1 1-1-1h-2 0-1v1l1 1h0c1-1 2 0 3-1 2 0 3 0 4 2 1 0 1 1 2 2h-2c-1 0-1 0-3-1-1 0-2-1-3-1 0-1-2 0-3 0h-2z" class="R"></path><path d="M775 539v2c-2 1-5 1-8 3-1 0-2 2-4 2-2 1-4 1-6 2-4 1-7 3-10 2 2-2 2-2 2-4 2-1 3-1 5 0h3c2-2 3-2 6-2 1 0 2-1 3-2h2 1s1 0 1-1h-5c4-1 7-1 10-2z" class="E"></path><path d="M173 405h1c3 3 7 4 11 5l6 3v-2-1l3 3 4 3c-1 0-2 0-3 1-7-1-17-7-22-12z" class="N"></path><defs><linearGradient id="A" x1="558.139" y1="241.145" x2="549.794" y2="243.63" xlink:href="#B"><stop offset="0" stop-color="#353434"></stop><stop offset="1" stop-color="#4e4c4c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M557 236c2 0 2 0 4-1v1l-3 3c1-1 2-2 3-2v1c-3 3-14 12-14 16h0l-3 6-1 1c2-10 8-17 14-25z"></path><path d="M853 389c5-1 10-2 15-2l-8 9c-1 0-2-1-2-1-1-2-3-2-4-4 0-1 0-1-1-2z" class="S"></path><path d="M314 174c1-4 1-6 5-8 3-2 5-3 8-4-2 4-5 7-8 11-2 2-3 4-5 6v-5z" class="E"></path><path d="M619 397h5c4 4 11 8 17 9v1h-3-1c-2 2-3 3-4 6l-3-3 1-1-2-3v-3c-4-2-7-4-10-6z" class="G"></path><path d="M629 403c1 1 3 2 5 2 1 1 2 1 3 2-2 2-3 3-4 6l-3-3 1-1-2-3v-3z" class="V"></path><path d="M629 406l3 2h1l-1 1h-1l-2-3z" class="E"></path><path d="M629 403c1 1 3 2 5 2l-2 3-3-2v-3z" class="B"></path><path d="M784 498h2l1-2 2 2 1-1h5l-4 4c-2 1-3 1-4 2-4 0-7 2-10 3v1c0-2 1-3 1-4l1-1 1-3v1c1-1 2-1 4-2z" class="d"></path><path d="M784 498h2l1-2 2 2 1-1h5l-4 4-2-1v1-2h-2c-1 1-2 1-3 1s-2 1-4 1l1 2h-1l-1-1 1-3v1c1-1 2-1 4-2z" class="J"></path><path d="M578 237c2 0 4 0 5-1h3v1h-1 3 2v1c-5 1-10 1-16 3-3 1-7 3-11 5h-3 0c4-5 10-6 15-8l3-1z" class="T"></path><path d="M613 480h1c0-1 1-2 1-4 0-1 1-1 1-1 1-1 2-1 3-1s2-1 3-1h0c1-1 1-1 3-1l-1-1c1 0 1 0 1-1s0-2 1-3h0c1 3-2 10-4 14l-1 1h0v-1c-2 0-2 0-3 1l-2-2-1 1-2 2h0c0-1 0-1-1-2v-1h1z" class="R"></path><path d="M731 246h1c0 1-1 2-1 3v1c0 1 0 1-1 2v4 1c-1 1-1 1-1 2v1s0 1-1 2v1c0 1 0 2-1 2-1 2-1 2-2 2v3c-1 1 0 1-1 2 0-1-1-1-1-2h0 0l-1-1v-3c1-1 1-3 2-4 1-2 1-3 2-5h0v2l1-1v-2-1h0c1-1 1-1 1-2l1-1c0-3 0-5 2-6z" class="h"></path><path d="M571 477v-2-3h-4v-2c0-1 0-1 1-1s1 0 1-1h1v-1-1c0-1 0-1 1-2h-2l1-1c-2 0-2-1-3 0v1l1 1-1 1c0-1-1-1-1-2-1-1-2 0-3 0 0-1 0-1-1-2h1 0 3c1-1 0-1 1-1v-1 2h4c0 1 1 2 1 3v1s2 4 1 4v1l1-1h1 0 1c1 0 0 0 1-1h0c1 0 2 0 2 1l-2 2-4 4-1 1h-1z" class="S"></path><defs><linearGradient id="C" x1="434.556" y1="411.218" x2="426.231" y2="408.367" xlink:href="#B"><stop offset="0" stop-color="#afadae"></stop><stop offset="1" stop-color="#cfcdca"></stop></linearGradient></defs><path fill="url(#C)" d="M425 410c3-2 6-2 9-3 4-2 8-5 12-7-3 3-6 7-9 10h-1c-1 1-2 1-3 2s-2 1-4 1v1h-3c-1-1-1-3-1-4z"></path><path d="M677 159c5 3 11 8 15 13h0l-6-5h-1c0 2 1 2 3 4v2h-3 0-2-1-3l2-1-1-1 1-1v-1l-1-1h-1c0-3 0-4-2-7v-2z" class="S"></path><path d="M680 168c1 0 2-1 2-2 1 0 2 2 3 3 0 1 1 1 2 2l-1 1h-2-3l-1-1 1-1v-1l-1-1z" class="C"></path><path d="M209 474l6 2h1l2 1 6 2c-1 2-2 2-2 4l3 4 2 3h-4l-8-11 1-1c-2-1-5-2-7-3v-1z" class="g"></path><path d="M218 477l6 2c-1 2-2 2-2 4-2-2-4-3-4-6z" class="Y"></path><path d="M429 175c-1 1-3 2-3 2l-8 4h-1c1-1 3-1 4-3l-6 3c7-8 18-13 28-15-3 3-6 4-9 6-2 1-5 1-5 3z" class="M"></path><path d="M529 421c4 4 10 9 11 14 0 1 1 1 2 2 0 1 4 5 4 6v1c0 2 0 2 1 3h0l-4-2c-2-2-5-4-7-6 1 0 2 0 3 1v-1l-2-1c1 0 2 0 3-1l-3-3v-1l-9-10c0-1 0-1 1-2z" class="Z"></path><path d="M339 238c3-5 6-11 10-15l-1 7c-1 1-3 1-4 2l1 1 1-1 1 1-1 2v4h-1c-1 0-2 0-3 1-2 0-4 0-5 2l-1 1c0-1-1-2-2-2v-1c1 0 2-1 3-2l-1-1v-1l2-1v3h1z" class="B"></path><path d="M336 236l2-1v3h1l1 1c2-1 4-2 6-4v4h-1c-1 0-2 0-3 1-2 0-4 0-5 2l-1 1c0-1-1-2-2-2v-1c1 0 2-1 3-2l-1-1v-1z" class="D"></path><path d="M320 144c1 2 0 4-1 6v5c-1 3-3 6-4 9s-4 6-5 9c-1 2-2 6-4 7h0c0-1 1-2 1-3 1-4 2-9 4-13 3-6 6-13 9-20z" class="h"></path><defs><linearGradient id="D" x1="806.515" y1="488.986" x2="792.486" y2="489.014" xlink:href="#B"><stop offset="0" stop-color="#2f2f31"></stop><stop offset="1" stop-color="#50505b"></stop></linearGradient></defs><path fill="url(#D)" d="M807 479h1c1 0 1 0 2-1v1c-3 5-6 10-10 14-2 0-3 3-5 4h-5l3-2c-1-1-1-1-2-1l-1-2c1 1 3 0 4-1s2-1 4-1c2-3 5-6 7-10l2-1z"></path><path d="M794 491c1-1 2-1 4-1l-5 5c-1-1-1-1-2-1l-1-2c1 1 3 0 4-1z" class="g"></path><defs><linearGradient id="E" x1="424.978" y1="146.653" x2="422.815" y2="141.338" xlink:href="#B"><stop offset="0" stop-color="#827c80"></stop><stop offset="1" stop-color="#949491"></stop></linearGradient></defs><path fill="url(#E)" d="M410 143c8 0 17-1 25 1-2 1-2 1-4 1s-4 1-6 2h-3 0l-12 2-2-1 2-2-2-1h-1l1-1 2 1h0 4c1-1 1-1 3-1h0l-6-1h-1z"></path><path d="M421 145h10c-2 0-4 1-6 2h-3 0l-1-2z" class="L"></path><defs><linearGradient id="F" x1="417.744" y1="148.966" x2="410.256" y2="144.534" xlink:href="#B"><stop offset="0" stop-color="#7d7b7e"></stop><stop offset="1" stop-color="#939291"></stop></linearGradient></defs><path fill="url(#F)" d="M408 148l2-2-2-1h-1l1-1 2 1h0 6c2 1 4 0 5 0l1 2-12 2-2-1z"></path><path d="M311 177c1 1 1 3 1 4l-8 16c-3 7-6 13-7 20l-1 3c0-3 1-6 2-9l4-16 5-13h0c1-2 3-4 4-5h0z" class="M"></path><path d="M311 177c-1 5-3 8-4 12-1 1-1 3-2 4 0-1 0-2 1-3 0-2 2-5 2-7l-1-1h0c1-2 3-4 4-5z" class="R"></path><path d="M165 333s1 2 1 3l9 3c1 2 3 2 4 3 0 3-1 3-3 4h-5c-1 0-1 0-1-1h-1l-1-1c0-2-1-3-2-5-1-1-2-1-2-3h2l-1-2v-1z" class="D"></path><path d="M165 333s1 2 1 3 0 2 1 3h0c2 1 5 3 5 6h0c-1 1-1 0-2 0h-1l-1-1c0-2-1-3-2-5-1-1-2-1-2-3h2l-1-2v-1zm70 171h-1c-2 0-3-1-4-2-7-4-12-10-17-16 2 1 3 2 5 3 1 1 2 2 4 3l1-1v-1h4v1 1 2c1 1 2 1 3 1v2c1 1 3 4 4 5v1l1 1z" class="I"></path><path d="M223 490h4v1 1 2 2l-4-5v-1z" class="H"></path><path d="M227 494c1 1 2 1 3 1v2c1 1 3 4 4 5v1c-3-2-5-5-7-7v-2z" class="B"></path><path d="M750 580h1v1h1c-7 4-15 7-23 8-5 1-10 2-14 4h0c-2 0-3 1-5 1-1-1-3-1-5-2v-1-1h1 1 0 1 1 9c2-1 5-1 6-1 4 0 7-1 10-2l3-1v-1l-3 1 1-4h0c1 0 1 1 2 1l1-1h0c1 1 0 1 1 2 1 0 3 0 4-1h2c2-1 2-2 4-3h1z" class="N"></path><path d="M709 590h9v1h-8l-1 1c2 1 4 0 6 1-2 0-3 1-5 1-1-1-3-1-5-2v-1-1h1 1 0 1 1z" class="H"></path><path d="M300 192v1c0 1-1 2-1 4l1 1h0v-1l2-2-4 16c-1 3-2 6-2 9v2c0 1 0 1-1 2v1 1c-1 1-1 1-1 2s-1 2-1 3v-2c1-2 0-3 0-4v-1c0-8 2-17 4-25l3-7z" class="D"></path><path d="M334 628h0c6 0 11-1 17-2 2 0 5-1 7-2 2 3 3 6 4 10h-1l-14-1v-1c3-1 6-2 8-4h-21z" class="Y"></path><path d="M846 448h3l1 1c-3 3-6 6-9 8-5 5-9 9-15 12l-1-2 1-1 1-1c0-1 0-2 1-2h1c0-1 0-1 1-2h1l1-1 3-3 2 1c2-3 4-5 5-8h3l1-2z" class="M"></path><path d="M837 458c-2 2-6 4-9 6h1c4 0 8-4 12-7h0c-5 5-9 9-15 12l-1-2 1-1 1-1c0-1 0-2 1-2h1c0-1 0-1 1-2h1l1-1 3-3 2 1z" class="T"></path><path d="M726 537c3 0 6 1 9 1l15 3h14 1 5c0 1-1 1-1 1h-1-2c-1 1-2 2-3 2-3 0-4 0-6 2h-3c-2-1-3-1-5 0v-1-1c-2-1-3-1-5-2-6-2-12-3-18-5z" class="Y"></path><path d="M709 581l1 1h2 0c1 1 1 2 2 2s2 1 2 1c2 1 2 2 2 3h3 4c2-1 5-1 7-2h2l3-1v1l-3 1c-3 1-6 2-10 2-1 0-4 0-6 1h-9l-10-2v-1h4l1-3c1 1 1 2 3 2h1 0c0-1 1-1 1-2v-3z" class="G"></path><path d="M709 581l1 1h2 0c1 1 1 2 2 2s2 1 2 1c2 1 2 2 2 3-2 0-3 0-4-1h-1-2v-2h-1l-1 1h-1 0c0-1 1-1 1-2v-3zM577 166c6 1 11 1 18 3 3 1 5 2 8 4 2 2 5 3 7 5v2l1 1c-2 0-5-1-6-2-5-3-9-5-13-7l-15-6z" class="W"></path><path d="M681 234c-2-4-3-8-3-11l4 4h3 0c1 1 2 2 3 4s3 3 3 5v1 3c-1 0-1 0-2-1h-2c-2-1-3-1-5-1l-1-4z" class="K"></path><path d="M681 234l2-1s1 0 1 1c1 2 2 3 3 4v1h0c-2-1-3-1-5-1l-1-4z" class="H"></path><path d="M685 227c1 1 2 2 3 4s3 3 3 5v1 3c-1 0-1 0-2-1l-7-12h3 0z" class="e"></path><path d="M806 322h0l2 1h1c1-1 0-1 2-1 0-1 1-1 1-1l-2 1v2h-2v3h-1c-1-1-1-1-1-2h-1v1c0 1 0 2 2 3-1 1-2 1-2 2l-1-1v-1c-1 1-1 1-1 2h-1c-1 0-1-1-1-2h-2-1l1 3c0 1-1 1-1 3-1 0-2-1-2-1-1 1-2 1-2 2l-2 1c-1 0-1 0-1 2-2 1-4 1-5 3-1 0 0 1-1 1l-2 1c0-1 1-2 1-3s1-1 1-2c1-1 2-2 2-3h2v-2c1-1 2-1 2-2h3v-1c1-1 2-1 3-1v-2c1 0 1-1 2 0h1l1-2h3c0-2 0-3 1-4h1zM650 178c1 0 1-1 2 0v1h1v1h-1c-1 1-2 1-3 1h0-1-3l-3 1h-2c-1 1-2 1-2 1h-1-1c-1 0-2 1-3 1h-7-1 0c-1-1-1-1-2-1-1 1 0 1-1 1h-1v1c-1-1-2-1-2-1v-1l1-1c1 1 0 1 2 1 1-1 2-2 4-2 1 0 1 0 2-1h1 1c2-1 4-1 7-1 4-1 9-1 13-1z" class="D"></path><path d="M178 301l11 11c2 1 3 2 5 4l-1 2c-1 0-2 2-3 1l-1-1-2 2c-2-3-5-6-7-9h-1c-1-1-2-2-3-4l1-1c0 1 1 1 2 1 1 1 3 2 5 3-2-3-5-5-6-8v-1z" class="I"></path><path d="M180 311v1c3 2 4 5 7 6 1-1 1-1 3-2-1-2-2-2-1-4 2 1 3 2 5 4l-1 2c-1 0-2 2-3 1l-1-1-2 2c-2-3-5-6-7-9z" class="J"></path><path d="M353 289h1l3-5c1-3 5-6 7-8l1-1 1-1 4-3c-1 2-2 3-3 5 0 1-1 1-1 2-4 6-7 11-9 18v1h-4-1v-1c0-2 0-5 1-7z" class="D"></path><path d="M353 297l1-4c1 0 1 0 2-1h0c0 1 0 2 1 4v1h-4z" class="E"></path><path d="M356 292c0-2 1-4 2-5l1-1c1-2 4-8 7-8-4 6-7 11-9 18-1-2-1-3-1-4z" class="H"></path><path d="M488 443l2-1c1 1 0 1 1 2-2 2-2 2-3 5l2 1-2 2h5l-1 1c-1 0-2 1-3 1-3 3-2 6-5 9v1l-1 4-1-1-1 1v1c-1 0-2 1-3 0l2-1c-1-3 0-5 0-7 0-4 2-6 3-10h0l4-4 1-1v-3z" class="I"></path><path d="M488 443l2-1c1 1 0 1 1 2-2 2-2 2-3 5l2 1-2 2c-1 1-2 1-3 0l2-5 1-1v-3z" class="X"></path><path d="M607 388v-1c2 0 4 0 6-1 1-1 1-3 1-4l1 1c0 2 1 4 2 6 0 2 1 6 2 8 3 2 6 4 10 6v3l2 3-1 1h0c-3-2-5-4-8-7s-5-8-10-11c-1 0-2-1-4-1-1-1-1-2-1-3z" class="O"></path><path d="M615 385l1 7c-2-1-4-1-6-2l1-1c3-1 3-2 4-4z" class="R"></path><path d="M607 388v-1c2 0 4 0 6-1 1-1 1-3 1-4l1 1v2c-1 2-1 3-4 4l-1 1c-1-1-2-2-3-2z" class="S"></path><path d="M383 408l10-3 1 1c1 1 1 2 3 3-1 0-2 1-2 1-2 0-2-1-3 0-1 0-2-1-3-1 1 2 1 2 3 3l-1 1v1h1v3c-1 0-2 0-2-1-1 1-1 1-1 2l-1-1c-2 0-3 1-4 2-1-1-2-3-3-5s-2-3-1-4 2-2 3-2z" class="F"></path><path d="M381 414c1 1 1 1 3 2l5-3 1 1v2c-1 1-1 1-1 2l-1-1c-2 0-3 1-4 2-1-1-2-3-3-5z" class="G"></path><path d="M383 408l10-3 1 1c1 1 1 2 3 3-1 0-2 1-2 1-2 0-2-1-3 0-1 0-2-1-3-1l-1 1v1c0 2-2 2-3 3h-2l-1-1c0-1-1-2-1-3 1 0 2-1 2-2z" class="K"></path><path d="M584 403c1 0 2 0 3 1v1l4 1 5 2h-2 0v1c0 1 0 1 1 1l2 2 1 1 1 1h0v1c1 0 2 0 3-1h0 1v1l3-2 1-1 1 1-2 1c-1 0-1 1-3 1v1c-1 0-2 1-2 2h0l-7 2c-1-1-2-3-3-5h0v-1c-2-1-3-2-3-4 1 0 0 0 1-1 0-2-4-4-5-6z" class="D"></path><path d="M171 293c2 0 4 4 6 6l8 7 15 14 3 3c-1 0-2 0-2 2l2 2-7-2-5-2c-1-1-3-1-4-2v-1l2-2 1 1c1 1 2-1 3-1l1-2c-2-2-3-3-5-4l-11-11-7-8z" class="E"></path><path d="M200 320l3 3c-1 0-2 0-2 2l-1-1c-1-2-1-2 0-4z" class="Y"></path><path d="M194 316c2 2 3 4 4 6v1l-2 2-5-2c-1-1-3-1-4-2v-1l2-2 1 1c1 1 2-1 3-1l1-2z" class="F"></path><path d="M191 323c1-1 1-1 1-2h0l2-1 4 3-2 2-5-2z" class="V"></path><path d="M387 208c23-5 44-1 64 12 2 1 4 2 5 3-1 0-2 0-3-1l-9-4c-12-7-24-10-38-10-5 0-10 0-16 1l-3 1v-2z" class="K"></path><defs><linearGradient id="G" x1="487.389" y1="498.297" x2="479.589" y2="498.566" xlink:href="#B"><stop offset="0" stop-color="#676666"></stop><stop offset="1" stop-color="#858484"></stop></linearGradient></defs><path fill="url(#G)" d="M474 495h0c1-3 1-5 2-7l3 4c2 3 6 7 10 9h0l1 1c-1 1-2 1-1 3 0 0 1 0 1 1 1 1 1 2 1 3h-3-2-1c0-1-1-2-1-2 0-2-1-3-2-4h0l-1-1c-1-1-2-1-3-2h0l-2-2h-1l-2-1 1-2z"></path><path d="M482 503c0-1 1-1 1-1h1v1c2 2 2 3 2 6h-1c0-1-1-2-1-2 0-2-1-3-2-4z" class="G"></path><path d="M489 501l1 1c-1 1-2 1-1 3 0 0 1 0 1 1 1 1 1 2 1 3h-3v-1l-1-1c1-1 1-1 1-3-1-1-1-1 0-2l1-1z" class="J"></path><path d="M474 495h0c1-3 1-5 2-7l3 4-1 1v2c0 1 1 1 1 2s-1 2-1 3h0l-2-2h-1l-2-1 1-2z" class="Q"></path><defs><linearGradient id="H" x1="451.925" y1="234.476" x2="475.033" y2="232.528" xlink:href="#B"><stop offset="0" stop-color="#050908"></stop><stop offset="1" stop-color="#312c2c"></stop></linearGradient></defs><path fill="url(#H)" d="M453 222c1 1 2 1 3 1 9 6 16 13 22 22 1 2 3 5 4 9h0c-5-9-13-16-21-21-1-1-1-2-3-3-2-2-4-4-7-5l-3-2c1 0 2-1 2-1 1 0 1 1 2 0h1z"></path><defs><linearGradient id="I" x1="481.969" y1="509.339" x2="470.602" y2="502.966" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#afadac"></stop></linearGradient></defs><path fill="url(#I)" d="M469 504c1-2-1-1-1-3h2 0c-3-1-5-4-7-6h0v-1c2 2 6 6 9 7l1-2v-2l2 1h1l2 2h0c1 1 2 1 3 2l1 1h0c1 1 2 2 2 4 0 0 1 1 1 2h-6l-9 1h-2l-1-1h-1c1 0 1 0 1-1h1c1-1 0-1 1-1v-2h1l-1-1z"></path><path d="M473 499v-2l2 1v1c0 1-1 2-2 2h0v-2z" class="D"></path><defs><linearGradient id="J" x1="611.18" y1="96.497" x2="614.669" y2="117.615" xlink:href="#B"><stop offset="0" stop-color="#5a5758"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#J)" d="M591 98c15 3 28 7 42 14 0 1 0 2-1 3h-1l4 3v1c-3-1-5-3-7-5-8-5-18-10-27-11-4-1-7-2-10-5z"></path><path d="M326 126v1l2-3c0 1-1 2-1 4 0 1-1 2-1 3v1l-1 2c-1 1-1 1-1 2l-2 4-1 1 1 1h0c1 1 0 1 1 2l1-1c0-3 1-5 2-8 0-2 1-4 2-5v-2l1 1c-1 1-1 3-2 4l-3 8c0 1 1 2 0 3h-2v-1c-1 0 0 0-1-1 0 1-1 1-1 2-3 7-6 14-9 20-2 4-3 9-4 13-1 0-2 3-3 3 0 0 5-17 5-18 3-7 6-13 9-19 1-3 3-6 5-9 1-2 2-5 3-8z" class="a"></path><path d="M384 419c1-1 2-2 4-2l1 1c0-1 0-1 1-2 0 1 1 1 2 1 1 1 1 3 2 4 0 2 1 4 2 5 0 1 1 2 2 3h0l-2 7-1 1-2-3h-2l-1-1-2-6c-1-2-3-5-4-8z" class="b"></path><path d="M389 422l1-1 1 1 1 2c-1 1-1 0-2 0l-1 1c-1 0-1-1-1-1l1-2z" class="C"></path><path d="M389 425l1-1c1 3 2 7 4 9l2 3-1 1-2-3h-2l-1-1-2-6c1-1 1-1 1-2z" class="W"></path><path d="M384 419c1-1 2-2 4-2l1 1c0-1 0-1 1-2 0 1 1 1 2 1 1 1 1 3 2 4 0 2 1 4 2 5 0 1 1 2 2 3l-2 1-1-1v-1c-1-3-2-6-4-9h-2c-1 0-1 1-1 1l1 1v1l-1 2s0 1 1 1c0 1 0 1-1 2-1-2-3-5-4-8z" class="L"></path><defs><linearGradient id="K" x1="828.612" y1="424.191" x2="839.111" y2="418.043" xlink:href="#B"><stop offset="0" stop-color="#424349"></stop><stop offset="1" stop-color="#585756"></stop></linearGradient></defs><path fill="url(#K)" d="M828 419h1c1 0 1 0 2-1 3 0 5-1 7-2 1 0 2-1 3-1 0 1-6 11-7 12v-1c-1 0-2 1-3 1v-2h-1-2-1c-3 2-4 0-7 2-2 2-4 2-5 4h-2 0c-2 1-2 2-4 2h0c-1 1-3 1-4 1 0-1 1-1 1-2 2 0 4-1 6-2h1c1-1 3-3 5-4 1 0 1 0 2-1l4-4c-1 0-2 0-4 1 1-1 2-2 4-2l1-1h3z"></path><path d="M517 438v-1h14c2 0 4 0 6 1l2 1v1c-1-1-2-1-3-1 2 2 5 4 7 6h-2v1c0 1 2 2 2 3 0 2 1 3 2 4v1c0 1 1 2 1 3-2-2-2-4-4-6l-1 1-3-3c-7-6-13-8-21-11z" class="Z"></path><path d="M517 438v-1h14c2 0 4 0 6 1l2 1v1c-1-1-2-1-3-1 2 2 5 4 7 6h-2v1c0 1 2 2 2 3 0 2 1 3 2 4v1c0 1 1 2 1 3-2-2-2-4-4-6-1-1-1-1-1-2h0c-5-6-8-9-15-11-2-1-6-1-9 0z" class="C"></path><path d="M805 434c1 0 3 0 4-1h0c2 0 2-1 4-2h0 2c1-2 3-2 5-4 3-2 4 0 7-2h1 2 1v2l-2 4-4 4-2 2-1 1c0-1-1-2-1-3l-1-2c-2 1-3 1-5 0-1 1-3 1-4 1l-3 1c-1 1-3 1-4 2-1 0-2 0-3 1s-3 0-4 1c-2 1-5 1-7 1l1-1c2 0 3 0 5-1 1-1 1-1 2-1h1c1-1 3-1 4-3l1 1 1-1z" class="f"></path><path d="M815 433c2-1 2-2 5-2l-1-2h1l3 3 1-1 1 1-1 1 1 1v1l-2 2-1 1c0-1-1-2-1-3l-1-2c-2 1-3 1-5 0z" class="L"></path><path d="M820 433h2l1 4-1 1c0-1-1-2-1-3l-1-2z" class="G"></path><path d="M474 495l-1-1h-3c-2 0-5-4-7-6h1l1-1 1 1h1c2 1 1 1 1 3 1 1 2 2 3 2 1 1 1 0 2 0-1-1-1-2-1-2h-1l-1-1-1-1v-1-3c-1-1-1-3-1-5h0l1-3-1-1v1c-1 0-2 1-2 2v-1-1-1l1-1c0-1 0-2-1-3l1-1h0l1 2h0s1 0 1-1l-1-1 1-1 1 1c0-1 1-1 2-2v-3c0-1 0-1 1-2v-2-1h1c-2 10-1 18 2 27-1 2-1 4-2 7h0z" class="D"></path><defs><linearGradient id="L" x1="206.26" y1="316.366" x2="187.493" y2="312.802" xlink:href="#B"><stop offset="0" stop-color="#928f93"></stop><stop offset="1" stop-color="#afb0af"></stop></linearGradient></defs><path fill="url(#L)" d="M187 306l1 1c1-1 1 0 1-2 0-1-2-2-2-3-2 0-3-1-3-2l-1-1c0-1 0-2-1-4l1-1c1 2 2 3 3 4 0 2 1 3 2 4 4 5 9 10 13 14l4 4 8 11v1l-2-1h0l-8-4-2-2c0-2 1-2 2-2l-3-3-15-14h2z"></path><path d="M201 325c0-2 1-2 2-2l8 8h0l-8-4-2-2z" class="c"></path><path d="M842 310h0c-2 3-6 4-7 8l6-4h0l-4 4c0 1 1 2 2 3-6 3-13 5-20 8-7 4-14 8-20 12l-2-1 45-30z" class="O"></path><defs><linearGradient id="M" x1="612.135" y1="151.955" x2="616.61" y2="140.629" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#929090"></stop></linearGradient></defs><path fill="url(#M)" d="M589 144l11-1 18 2c3 0 6 1 8 1l12 2c-1 0-2 0-3 1v3 1s0 1 1 2c-11-5-21-7-32-8-4-1-8-2-12-2-1 0-2-1-3-1z"></path><defs><linearGradient id="N" x1="654.714" y1="627.988" x2="667.387" y2="630.668" xlink:href="#B"><stop offset="0" stop-color="#141416"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#N)" d="M657 622l1 1c4 1 9 4 12 4l1 1 6 3c1 1 2 1 3 2h-1c-1 1-4 1-5 1-4 0-8 0-12-1-3 0-5-2-8-2l-1-1 4-8z"></path><path d="M658 623c4 1 9 4 12 4l1 1 6 3-1 1c-6-1-14-3-19-7 0-1 1-1 1-2z" class="c"></path><path d="M495 939h-2c-1-2-2-5-2-7l-5-16-8-19h1c0 2 2 4 2 6 0 1 0 1 1 2l1 1c0 1 0 2 1 4h0c0 2 1 3 2 4l3 10c0 1 1 2 1 3v1h0l1-1v-1c0-1-1-3-1-4v-1c-1-1-1 0-1-1v-2l-2-5c-1-1-1-2-2-3v-1-1c-1-2-1-3-2-4h0v-2c-1-1-1-3-2-4l-3-9c0-1-1-1-1-2v-1l-1-1c0-1-1-2-1-3v-1l-1-1v-1c-1-1-1-2-1-3-1 0 0 0-1-1l-2-5-1-3-1-1c0-1-1-2-1-3v-1l-1-1v-1l-1-2c-1-1-1-1-1-2-1-2-2-5-3-7v-1c-1-1-1-1-1-2l-1-1v-1-1c-1-1-1-2-2-3s-1-2-1-3c-1-2-2-3-3-5 0-1 0-1-1-2l-1-2c0-1 0-2-1-3l-4-9v-1l-1-2c-1-1-1-1-1-2l-1-2c-1-1-1-2-1-3l-1-3-1-2h-1l1-1 9 20 19 43 21 54c2 7 5 14 7 21l-1 1z" class="a"></path><path d="M395 446l2-4h1v-1c0-1 1-2 1-2 2-2 3-5 4-7v-2c1-1 1-1 1-2 1-1 2-2 2-3l1 2c-1 1-2 0-2 2v1c1-1 2-2 2-4 1 1 2 1 2 2v2l-1 1-1-1c0 1 1 2 1 3h0l-2-1c0 1 0 1-1 2v2 1c1 1 1 0 1 2 1 1 2 1 2 2-1 0-2 1-4 1v-1l-1 1h0v3l1 1v4l1-1c1 0 8 1 9 1 1 1 2 2 2 4h1-1c-1 0-1-1-2-2-2-1-3-1-5-1h-1c-2 0-4 0-6-1h-1-1l2-1h0l-1-1h0c1-1 0-1 2-1v-4c-1-1-2 0-1-2l1-1h0c1 1 1 0 2 0h0v-2l-1-2v-2h0-1v2l-2 2c0 1 0 1-1 2h0v1c-2 2-3 3-4 5l1 1v1l1 2c0 1 0 2 1 3h1c0-2 1-2 3-2 1 0 1 0 1 1v1 1l1 1h0c1 2 1 0 1 3v1l-1-1v2l1 1v2h0c0 4 0 6 1 9h-1c-1-1-1-3-1-5-1-1-2-3-2-4 0-2-1-4-2-6-2 0-5-9-6-11z" class="e"></path><path d="M401 457c1-1 2-1 3-3 1 3 1 8 1 11v2c-1-1-2-3-2-4 0-2-1-4-2-6z" class="X"></path><path d="M627 206c17 1 31 9 42 22l4 4v2c3 3 5 6 6 10l-3-3c-2-4-4-6-7-9s-6-6-9-8l-10-8c-2-1-4-3-6-4l-2-2h-4l-1-1c-4-2-11 0-16-1h-2-4c1 0 2 0 3-1l1-1h8z" class="N"></path><path d="M650 216c6 0 8 6 13 8l4 4c0 2 4 5 6 6 3 3 5 6 6 10l-3-3c-2-4-4-6-7-9s-6-6-9-8l-10-8z" class="O"></path><path d="M612 392c5 3 7 8 10 11s5 5 8 7h0l3 3c-1 1-1 2-2 3l-1-2-1 1-2 1c-2-2-4-6-7-6-1-1-2-1-3-2l-2-3-2-1h-1l-1-1v-4c0-2 1-4 1-7z" class="M"></path><path d="M629 415l-1-1c0-1-1-1 0-2v-1c1-1 1 0 2-1h0l3 3c-1 1-1 2-2 3l-1-2-1 1z" class="G"></path><path d="M612 404c-1-2 0-4 0-6l2-1c1 1 1 2 1 3 1 2 3 3 5 5v1h-2l-2-1h-1l-2-1h-1z" class="J"></path><path d="M557 236h0c12-15 33-26 52-29 3-1 6-1 9-1h9-8l-1 1c-1 1-2 1-3 1-6 0-13 2-18 4-7 2-17 6-22 11l-4 2c3 1 3 0 6 0-1 1-3 2-5 3-1 1-4 3-4 4v1l-7 5v-1c-1 0-2 1-3 2l3-3v-1c-2 1-2 1-4 1z" class="M"></path><path d="M571 225c3 1 3 0 6 0-1 1-3 2-5 3-1 1-4 3-4 4v1l-7 5v-1c-1 0-2 1-3 2l3-3v-1-1c3-3 6-6 10-9z" class="O"></path><path d="M546 443c1 2 2 3 3 4l3 3v1 1s1 1 2 1c1 1 1 0 2 1h-1c-1 1-1 0-1 1v1l1 2h-1l-1-2h-1c1 1 1 2 1 3 1 0 1 1 1 2s0 1 1 2h2v1h0l-1 1v1l2 2c-2 0-3-2-4-3l1 4-1 1h-1l-4-1h-1c1-2 0-5-1-7 0-2-1-3-1-5 0-1-1-2-1-3v-1c-1-1-2-2-2-4 0-1-2-2-2-3v-1h2l4 2h0c-1-1-1-1-1-3v-1z" class="e"></path><path d="M554 465c-1-2-2-5-3-8l-4-8v-1c1 2 2 4 4 5v1c0 1 0 1 1 2h0c1 1 1 2 1 3 1 0 1 1 1 2s0 1 1 2h2v1h0l-1 1v1l2 2c-2 0-3-2-4-3z" class="R"></path><path d="M577 175h2 1l1-1 2 1c2 0 3 1 5 2l2 1c1 1 2 1 4 2 1 0 11 7 11 8v2c0 1 3 3 4 4l-1 1-7 3h0c-2-1-3-5-4-7h-1l-1-2v-1l-1-1-3-3v-1h-1c-2-2-3-3-5-4l-5-3c-1 0-2 0-3-1z" class="h"></path><defs><linearGradient id="O" x1="600.672" y1="485.142" x2="609.702" y2="476.427" xlink:href="#B"><stop offset="0" stop-color="#8f8d8e"></stop><stop offset="1" stop-color="#b5b3b3"></stop></linearGradient></defs><path fill="url(#O)" d="M598 481c0-3 2-6 2-9 0-1 0-2 1-3h0c1-1 1-2 2-3l2-1h0c2 0 2 1 3 2-1 1-2 0-3 0s-2 0-2 1l2 2-1 1s1 1 1 2h0l2 2 1 1v1c2 0 2 1 3 2l1 1h1-1v1c1 1 1 1 1 2h0c0 2-1 4-1 5l-3 6c-1-2-2-2-4-3l1-1c1 0 1-1 2-1v-1l-2-1c-1 1-2 1-4 1-1-2-1-2-1-4l2-1c1-1 1-1 0-2v1l-1-1c-1 1-1 1-2 1h0v-1-2c-1 1-1 2-2 2z"></path><path d="M180 225c0-3-1-6-1-9-2-7-5-14-5-22h0c4 5 7 10 11 15 3 4 1 5 1 10v1c-1 1-1 2-1 3v2h0v3l1 1h-3c-1 1-2 1-2 1-1-1-1-3-1-5z" class="e"></path><path d="M180 225l4 3h1l1 1h-3c-1 1-2 1-2 1-1-1-1-3-1-5z" class="a"></path><path d="M676 192c2 1 4 1 5 3 2 3 4 7 6 10 0 2 1 3 1 5 1 0 0 0 1 1v2l1 1v1c1 1 0 1 1 2v2l1 1v1h0v-2c0-2 0-2-1-2v-2c0-2 0-2-1-2v-2c-1-1-1-3 0-3h0c2 6 3 13 5 20 2 5 5 13 5 18 0 1-1 3 0 4v2c0 1 0 2 1 3v7c-1-2-1-5-1-6v-1c0-3-1-4-2-7 0-2 0-5-1-7s-1-4-2-6-1-5-3-8c0 2 0 5 1 7v2h-2c0-2-2-3-3-5s-2-3-3-4l1-1h0l1 1c1 1 1 2 2 2 0-1 0-1-1-2v-3-1h0c0-2 0-2 1-3 0-1 0-2-1-3 0-2 0-3-1-5 0-2-1-3-1-4-1-3-1-4-3-7-1-1-1-3-3-5-1-1-2-2-4-2-1 0 0 0-1-1l1-1z" class="D"></path><path d="M626 466v-2h-1c0-1 0-1 1-2h2l1-1c0-1 1-2 1-3l-1-1h0c-1-1-1-1-1-2-2-1-3-2-4-2l1 1c-1 1-1 2-2 2h-2l2-1h-1c-1-1-1-1-2-1s-1 0-2-1l1-2h2c1 1 1 1 3 1 0-1 0-1 1-1s1 0 1 1l1 1c1 0 0 0 1 1h1v-2h0l1 1c0 1 1 1 2 2 0-1 0-1 1-2h-1 0l-1 1v-1-1l-2-2-2-2v-1c-1-1-1 0-1-1v-1h-1c0-1-1-2-1-3-1-1-1-2-2-2v-2h-1l-1-1v-2-1h-1v-1-1c1-1 2-2 3-2l2 1-1 1-1-1c-1 0-1 1-2 1l1 2c0-1 0-1 1-2l1 1h-1c0 1 0 1 1 2v-1h1 1l-1 1v1l1-1 1 1v1h1c0 2 0 2 1 3v2h1l1-1c-1-1-1 0-1-1v-1l-2-2v-1c-1-1-1-1-1-2l-1-3v-1c-1-1-1-1-1-2-1-1-1-1-1-2-1-1-1-2-1-3h-2-1c-1 0-1 0-2 1v5h-1v-1-1h0c0-2 0-2-1-3l3-2h3c2 1 2 2 3 4 0 1 0 2 1 2 0 1 0 2 1 3 0 1 0 2 1 3l3 5v1c1 1 1 2 2 3v1h1l1 1c0 1 1 2 2 4l-1 1c-1 3-3 6-5 9 0 2-1 5-1 7l-1 1-1-1h-1z" class="S"></path><path d="M635 450l-2-2v-3h1c0 1 1 2 2 4l-1 1z" class="h"></path><defs><linearGradient id="P" x1="415.84" y1="103.518" x2="369.738" y2="144.646" xlink:href="#B"><stop offset="0" stop-color="#4c4b4b"></stop><stop offset="1" stop-color="#757474"></stop></linearGradient></defs><path fill="url(#P)" d="M428 103l1 1c-15 5-29 12-40 23-3 3-7 6-9 9s-4 7-7 9c-1 1-2 2-3 2l-1-1c2-5 6-11 10-15 1 0 3-4 4-4 3-3 6-6 10-9 6-5 14-8 21-11l5-2c4 0 6-1 9-2z"></path><defs><linearGradient id="Q" x1="565.864" y1="249.703" x2="549.031" y2="245.743" xlink:href="#B"><stop offset="0" stop-color="#282829"></stop><stop offset="1" stop-color="#464443"></stop></linearGradient></defs><path fill="url(#Q)" d="M578 229l2-1c0 1-1 1-1 2h0v1l9-2-1 1c-2 0-2 0-3 1h-1l-1 1h-2l-2 1c-3 1-6 3-9 5h6c-5 2-11 3-15 8h0 3c-8 4-14 10-20 17h0v-2l1-1 3-6h0c0-4 11-13 14-16l7-5c1-1 2-1 3-2l4-2h3z"></path><path d="M578 229l2-1c0 1-1 1-1 2h0v1h0c-3 2-6 3-9 4-9 5-18 11-23 19h0c0-4 11-13 14-16l7-5c1-1 2-1 3-2l4-2h3z" class="R"></path><defs><linearGradient id="R" x1="394.435" y1="153.574" x2="398.139" y2="165.1" xlink:href="#B"><stop offset="0" stop-color="#383737"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#R)" d="M422 147h0v1h2-2l-1 1h-1v1c-7 2-13 3-19 5-12 5-20 12-29 20-2-1-3-2-5-2 3-3 6-6 9-8l9-7c3-1 5-2 8-4 5-2 11-4 17-5l12-2z"></path><defs><linearGradient id="S" x1="417.578" y1="107.601" x2="376.369" y2="123.458" xlink:href="#B"><stop offset="0" stop-color="#878687"></stop><stop offset="1" stop-color="#d7d6d3"></stop></linearGradient></defs><path fill="url(#S)" d="M421 101c1 0 3 0 5-1-2 2-4 3-7 5l-5 2c-7 3-15 6-21 11-4 3-7 6-10 9-1 0-3 4-4 4l-1-1 3-2c-1-1-1-2-2-2l-2-2c0-1 0-1-1-1v-1l9-5c5-4 11-6 16-9 7-2 13-5 20-7z"></path><path d="M421 101c1 0 3 0 5-1-2 2-4 3-7 5l-5 2 1-1h1l1-1 4-4z" class="G"></path><defs><linearGradient id="T" x1="637.111" y1="121.076" x2="631.054" y2="130.85" xlink:href="#B"><stop offset="0" stop-color="#3e3d3c"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#T)" d="M601 103c9 1 19 6 27 11 2 2 4 4 7 5 9 8 19 16 23 27v2c-1 0-2-1-3-1-2-1-5-7-7-9-4-5-8-10-13-14-11-9-23-15-35-20l1-1z"></path><defs><linearGradient id="U" x1="502.115" y1="456.385" x2="485.813" y2="468.335" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#9f9d9d"></stop></linearGradient></defs><path fill="url(#U)" d="M493 452c2 0 6-1 8 1-1 3-6 4-7 6 3 3 5 3 9 3-1 1-2 2-3 2-1 2-3 5-3 7l-1 3h-3l-2-2v2l-2 1v-1h-2c0-1-1-1-1-3h0l-1-1c0 1 0 2-1 2h0c-1-1-1-1 0-2l-1-2 1-4v-1c3-3 2-6 5-9 1 0 2-1 3-1l1-1z"></path><path d="M488 460l1 1v2l-2-1 1-2z" class="Q"></path><path d="M487 462l2 1v1c-1 1-2 1-3 1l-1 1v1c0 2 0 2-1 3l-1-2 1-4 3-2z" class="U"></path><path d="M485 467l2 1c0 2 1 4 2 6h-2c0-1-1-1-1-3h0l-1-1c0 1 0 2-1 2h0c-1-1-1-1 0-2s1-1 1-3z" class="Q"></path><path d="M493 474v-1h0v-3c2-3 4-5 7-6-1 2-3 5-3 7l-1 3h-3z" class="U"></path><path d="M489 454c1 0 2-1 3-1l1 1 1 1c-1 1-6 4-6 5l-1 2-3 2v-1c3-3 2-6 5-9z" class="X"></path><path d="M489 454c1 0 2-1 3-1l1 1c-1 1-1 2-3 2l-1-2z" class="Q"></path><path d="M372 326l3 1-1 1h-2c-1 0-3 1-3 3 1 0 4 1 5 2-1 3-4 7-6 9h-1v1c-4 5-7 10-10 16 0-3-1-5-2-8-1-4-1-9 1-13l1-2c1-1 2-3 4-4l2 1 2-1c2-1 3-2 5-4l2-2z" class="N"></path><path d="M360 341l3-3 1 1c0 1 0 2-1 3v1l-3-2z" class="K"></path><defs><linearGradient id="V" x1="363.194" y1="344.216" x2="354.93" y2="353.112" xlink:href="#B"><stop offset="0" stop-color="#4f4f4f"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#V)" d="M357 359c0-3-1-5-2-8-1-4-1-9 1-13 0 3 0 6 1 9l3-6 3 2c2 1 1 0 2-1h2v1c-4 5-7 10-10 16z"></path><path d="M604 147c11 1 21 3 32 8 8 4 17 9 23 17v1c-3 2-3 2-6 0-7-6-14-11-22-15-12-6-25-8-37-10 1-1 2-1 4-1 2 1 3 1 6 0z" class="B"></path><path d="M491 474v-2l2 2h3 1v8l2 3v5 1c0 1 0 2 1 2 0 1-1 1 0 2 0 2-1 4 0 6v1 1h-1c-3-1-5-4-7-6l-1-2c-1-5-4-9-5-13v-1c-1-2-1-2-1-4v-2h2v-1h2v1l2-1z" class="I"></path><path d="M491 474v-2l2 2h3 1v8l2 3v5 1l-1-1c-2-1-2-1-3-1h-1c1-1 3-2 4-3l-3-1c-1-1-1-1-1-3h-1-2-1v1-1l-1-1c0-1 0-2-1-4h-3v-2h2v-1h2v1l2-1z" class="X"></path><path d="M489 474v1 1h0l1 1v2 1h2v-2l1 1c0 1 0 1-1 1 0 1-1 1-2 2v1-1l-1-1c0-1 0-2-1-4h-3v-2h2v-1h2z" class="R"></path><path d="M491 474v-2l2 2h3 1v8c-1-2-2-2-2-4 0 0-1 0-2-1 0-1 0 0-1-1 0-1-2 0-3 0h0v-1l2-1z" class="Q"></path><path d="M491 474l2 1c0 1 1 1 1 1l1 1v1s-1 0-2-1c0-1 0 0-1-1 0-1-2 0-3 0h0v-1l2-1z" class="C"></path><path d="M341 584l7 18h-4c-3 0-5-1-8-2-3 0-10 0-13-1l3-1v-1l-8-2c0-1-1-2 0-3h3c5-2 10-3 15-4 1-1 3-1 4-3 0-1 0 0 1-1h0z" class="c"></path><path d="M853 389c1 1 1 1 1 2 1 2 3 2 4 4 0 0 1 1 2 1-1 2-5 8-8 9h0c-2 1-5 3-6 3l-5 3h-1c-2 0-3 0-4-1v-1c-1 0-1 0-3 1 1-1 1-1 1-2v-2h0c1-1 3-2 4-2s1 0 2-1v-1l1-1c1-1 1-2 2-3h0v-1c0-2 0-3 1-5l1-1h1l-1-1h3c0-1 4-1 5-1z" class="D"></path><path d="M839 407h2l2-1 1 1 2-1h0l-1 2h1l-5 3c-1-1-2-1-3-2v-1l1-1z" class="I"></path><path d="M848 390h0c-2 2-3 3-4 5l1 1v2 3c-2 0-2 2-3 2l-2 2v1h0l-1 1-1 1v1c1 1 2 1 3 2h-1c-2 0-3 0-4-1v-1c-1 0-1 0-3 1 1-1 1-1 1-2v-2h0c1-1 3-2 4-2s1 0 2-1v-1l1-1c1-1 1-2 2-3h0v-1c0-2 0-3 1-5l1-1h1l-1-1h3z" class="b"></path><path d="M513 927h1l2 1c1 1 2 1 3 2s3 3 5 3c0-5 1-10 3-14 0-1 0-2 1-2v1 4h0v3c1-1 0-2 1-2h1c-1 4-4 12-3 15-3 2-7 1-10 1h-12c-3 0-7 1-10 0h0l1-1s1 0 2-1h1l3-4c2-2 8-6 11-6z" class="B"></path><path d="M528 922h0v3c1-1 0-2 1-2h1c-1 4-4 12-3 15-3 2-7 1-10 1h-12c-3 0-7 1-10 0h0l1-1s1 0 2-1h1 25l4-15z" class="Z"></path><defs><linearGradient id="W" x1="186.057" y1="406.159" x2="159.531" y2="386.957" xlink:href="#B"><stop offset="0" stop-color="#9c9b9c"></stop><stop offset="1" stop-color="#c8c6c6"></stop></linearGradient></defs><path fill="url(#W)" d="M173 405c-2-1-3-2-4-4-5-4-8-10-12-15 5 0 10 1 16 3 1 0 3 0 4 1v1h1 1c0 3 1 5 2 7l-1 1v1h1c0 2 1 3 3 4 0 1 1 1 2 1 0 1-1 2-1 3v2c-4-1-8-2-11-5h-1z"></path><path d="M173 389c1 0 3 0 4 1v1c0 1 0 1-1 2l1 2h-1c0-1-3-2-3-3v-3z" class="X"></path><path d="M177 391h1 1c0 3 1 5 2 7l-1 1c-1-1-1 0-2 0v-1l-2-3h1l-1-2c1-1 1-1 1-2z" class="b"></path><path d="M177 391h1c0 2 0 3 1 5l-1 2-2-3h1l-1-2c1-1 1-1 1-2z" class="Q"></path><path d="M367 343c0 2-1 3-2 5v1c-1 1-1 1-1 2v1l1 1c1-3 2-5 5-7v1c-1 1-1 2-2 3l-1 2v1c-1 1-1 1-1 2 1 1 1 1 1 3-2 2-4 4-5 6l-1 1h0v1s1 1 1 2v1h0c1 1 1 2 1 4l1 1v-1h2v-1c0-1 1-2 2-4h0c1-3 2-6 2-8 0-1 0-2 1-3 0-2 1-4 2-6 0-1 1-2 1-2l1-1v-1h1v1c0 2 0 4 1 6 2 0 4-3 5-4h0l-1 2h1l-1 1c0 1-1 1-1 2v1c-1 0-1 0-1 1 0 2-1 4-2 4-1 1-2 1-3 1-1 2-2 3-2 5-2 5-4 9-7 14l-4-8c0-4-5-10-4-14 3-6 6-11 10-16z" class="S"></path><path d="M367 352v1c-1 1-1 1-1 2 1 1 1 1 1 3-2 2-4 4-5 6l-1 1h0v1s1 1 1 2v1h0c1 1 1 2 1 4l1 1v-1h2v-1c0-1 1-2 2-4-1 3-1 5-2 6s-1 1-3 1c-1-3-2-7-3-10 1-2 1-3 2-5h0l1-2c1-2 2-4 4-6z" class="e"></path><defs><linearGradient id="X" x1="595.353" y1="507.354" x2="606.111" y2="481.454" xlink:href="#B"><stop offset="0" stop-color="#575657"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#X)" d="M598 481c1 0 1-1 2-2v2 1h0c1 0 1 0 2-1l1 1v-1c1 1 1 1 0 2l-2 1c0 2 0 2 1 4 2 0 3 0 4-1l2 1v1c-1 0-1 1-2 1l-1 1c2 1 3 1 4 3v1c0 1-1 2-1 3-1 2 0 2-1 3v1l-1 2c0 1 0 1-1 2h0v3h-2-1l-4 2c-1 0-1-1-2-1-1-1-1-1-2-3v-10l2-10c1-2 1-4 2-6z"></path><path d="M606 494c1 1 1 1 3 1 0 1-1 2-1 3h-2v-4z" class="G"></path><path d="M606 498h-2l-2-2h-1c1-1 1-1 1-2 2-1 2 0 4 0v4z" class="L"></path><path d="M594 507h1c1 0 2 0 3-1l1-2 1-1 1 1v1c0 1 1 2 1 3v1l-4 2c-1 0-1-1-2-1-1-1-1-1-2-3z" class="T"></path><path d="M330 222l2-6c3-4 3-9 5-14 1-3 4-6 5-9 0 2-1 4-2 7h0v1 1l-1 1 1 1c0-1 0-2 1-2v-2c1-1 1-2 2-3 1-3 4-4 6-4l-3 3h1 1c0 1-1 1-1 2v-1h-1l-2 1h0c-1 1-1 2-2 3v1 1c-1 1-1 3-2 4v1 2l-1 1v1 1l-1 1v2c0 2-1 3-1 4v1c0 1 0 2-1 3v2h0v-1-3h0c-2 2-2 5-2 7-1 4-3 7-3 10l-2 11c-1 0-2 1-2 2s1 0 0 2c0 1 0 1-1 2v1 1 1c0 1-1 2-1 3v1h0l-1-4h0c0 4 0 8-2 11 1-1 1-3 1-5h0c1-3 1-5 1-7v-5c0-1 0-2 1-3v-3c0-1 0-2 1-3v-3c1-3 0 0 1-2v-3l1-1v-3c0-1 0-1 1-2v-3c1-2 0 0 0-1 1-2 1-3 1-4z" class="e"></path><defs><linearGradient id="Y" x1="547.412" y1="483.473" x2="564.788" y2="476.012" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#b7b5b4"></stop></linearGradient></defs><path fill="url(#Y)" d="M554 465c1 1 2 3 4 3 0 0 1 1 1 2 1 0 1 1 1 2v1h0c1 1 1 2 1 3 1 1 1 3 2 5v1h2 0c0-2 1-2 2-3v1c1 0 1 1 1 2h1v-2l-1-1h1v-1l1-1h0l-1 2h1v2h1l1-1c0-1-1-2-1-2v-1h1l1-1 4-4v1l-1 1c-1 1-2 2-2 3h1 0v-1l3-3h1l-8 8c-2 2-4 5-6 6l-5 5h-1l-2 2c-1 0-2 2-3 3-1 0-2 1-3 1v1h0v-1c0-2 0-4-1-6l-3 1 3-5c3-5 5-11 4-17l-1-1h0 1l1-1-1-4z"></path><path d="M550 488v2l1 1h0c1 0 1 0 2-1 0-1 1-2 2-3h1 2c-1 2-3 3-3 6-1 2-1 2-1 4-1 0-2 1-3 1v1h0v-1c0-2 0-4-1-6l-3 1 3-5z" class="U"></path><path d="M787 236h1c0 4-2 5-3 8s-3 5-5 7c-1 1-1 3-2 4l-6 6-2 3-1 1-2 3-6 9-1 1v1c0 2 1 2 1 4-1 0-1 1-2 1v3h-1l1 1c-1 0-1 0-1 1h0c0 2 0 2-1 3l-6 1v1c-2 0-2 0-3-1v-1c0-1 1-3 2-4 1-5 4-9 7-13 1-1 1-2 2-3l13-17c1-1 2-2 3-4s3-3 4-5c1-3 5-7 8-10z" class="R"></path><defs><linearGradient id="Z" x1="629.957" y1="116.914" x2="658.022" y2="132.212" xlink:href="#B"><stop offset="0" stop-color="#969595"></stop><stop offset="1" stop-color="#b9b7b6"></stop></linearGradient></defs><path fill="url(#Z)" d="M633 112c17 9 35 20 48 35 2 2 4 4 5 6-1 0-1-1-2-2-1 0-1-1-1-2l-3-2v-1c-3-3-6-6-10-9-1 0-1-1-2-2s-2-2-4-2c0-1-1-2-2-2l-1-1c-1 0-1 0-2 1-1 2-2 2-2 3l-2 1-1 1v1c1 1 1 0 1 1 1 1 1 2 2 3l1 1v1 1c1 1 1 2 1 3 1 2 2 1 4 2h1c2 1-1 0 2 1h1c1 0 2 1 3 1v-1h0c-1-1-1-3-1-4h2c-1-1-3-2-3-4 1 0 1-1 2-1h1 0c1 1 1 2 2 3h-1l-1-1h-1c0 2 2 2 3 4h1c0 1 0 1 1 2 1 0 2 1 3 2v1l1-1c0-1-2-3-3-4l-2-2 1-1v1h1v1c1 0 1 1 2 1 1 2 0 0 0 2 1 1 2 1 2 3 0 1 0 1-1 1h-1c-1-1-2-3-4-4v2h0-2v1h1c1 0 1 0 1 1h0l-16-5v-2c-4-11-14-19-23-27v-1l-4-3h1c1-1 1-2 1-3z"></path><path d="M645 362c0-1 0-4 1-4h1l3 1 3 4c0 1 0 0 1 1l1 2v2c1 1 2 2 2 3l2 2 1 2c0 2 1 2 0 5v-1c-2 1-2 2-2 5 0 1 0 2 1 3l-2 2 1 2-1 3c-2-4-4-7-6-10-2-2-4-4-6-7-1 0-2-1-3 0-1-4 1-8 1-11 1-1 1-3 1-4h1z" class="a"></path><path d="M647 358l3 1 3 4c0 1 0 0 1 1l1 2v2c-1-2-2-3-4-4 0 0-1 0-1-1-1-1-2-3-3-5z" class="D"></path><path d="M644 362h1c1 7 3 15 7 20 1 3 3 5 5 7l1 2-1 3c-2-4-4-7-6-10-2-2-4-4-6-7-1 0-2-1-3 0-1-4 1-8 1-11 1-1 1-3 1-4z" class="J"></path><path d="M642 377c-1-4 1-8 1-11 2 2 1 3 1 5s1 4 1 6c-1 0-2-1-3 0z" class="X"></path><path d="M803 451c1-1 2-1 3-1v1h1c3 0 7 0 9 1h8l18-2c-1 3-3 5-5 8l-2-1-3 3-1 1h-1c-1 1-1 1-1 2h-1c-1 0-1 1-1 2l-1 1-1 1 1 2-4 2-1-1v-1-1l-1-1-4-1c1 0 1 0 1-1h-2c-1-1 0-1 0-2 1-1 1-1 1-2v-1c-2 0-3 0-4-1-1 0-1-1-2-1h-1l-1-1c1-1 2-1 3-1h1v-3h-3 0l-2-2c-2 0-2 1-4 0z" class="W"></path><path d="M812 456l1 1c0 1 0 1 1 2l2-1c0 1 0 0-1 1l1 1c-2 0-3 0-4-1-1 0-1-1-2-1h-1l-1-1c1-1 2-1 3-1h1zm-9-5c1-1 2-1 3-1v1h1c3 0 7 0 9 1h8c-3 1-6 1-9 1 1 1 2 0 3 1v1c-1 0-2 0-2-1l-2 1v-1c-1-1-1-1-2-1h-3 0l-2-2c-2 0-2 1-4 0z" class="J"></path><path d="M489 501h1 1l2 1s1 0 2 1h2 0c-2-1-3-2-4-3l-1-1-1 1-7-7c-6-6-6-13-6-21 1-1 0-1 1-2h-4l2-1-1-1c1-2 1-3 1-4v-1-2c1-2 1-2 2-3h1c-1-1-1-1-1-2 1-2 3-3 4-5h0c-1 4-3 6-3 10 0 2-1 4 0 7l-2 1c1 1 2 0 3 0v-1l1-1 1 1 1 2c-1 1-1 1 0 2h0c1 0 1-1 1-2l1 1h0c0 2 1 2 1 3v1h-2v2c0 2 0 2 1 4v1c1 4 4 8 5 13l1 2c2 2 4 5 7 6h1c1 0 3 1 5 2v2 1c-2 2-4 3-6 2l-8-1c0-1 0-2-1-3 0-1-1-1-1-1-1-2 0-2 1-3l-1-1h0z" class="h"></path><path d="M490 502l15 5v1c-2 2-4 3-6 2l-8-1c0-1 0-2-1-3 0-1-1-1-1-1-1-2 0-2 1-3z" class="F"></path><path d="M481 469v-1l1-1 1 1 1 2c-1 1-1 1 0 2h0c1 0 1-1 1-2l1 1h0c0 2 1 2 1 3v1h-2v2c0 2 0 2 1 4v1c1 4 4 8 5 13l1 2h-1l1 1h-1c-1 0-3-3-4-3 0-3-3-8-4-11 0-1-1-2-1-4 0-1 0-1 1-2h0c-1-1-2-1-2-2-1-1-2-3-1-4h0l2-2-1-1z" class="C"></path><path d="M481 469v-1l1-1 1 1 1 2c-1 1-1 1 0 2h0c1 0 1-1 1-2l1 1h0c0 2 1 2 1 3v1h-2v2c0 2 0 2 1 4v1c1 4 4 8 5 13-2-2-3-4-3-5-1-3-2-5-3-7 0-2-1-5-2-7v-3c-1 0-2 0-2-1h-1l2-2-1-1zM336 226c0 1 0 2-1 4 0 1 1 1 0 2v1l3-6 6-7c1-1 1-1 1-2 1-1 1-2 2-2s1-1 2-1l3-4v1l-11 17c-1 1-1 3-2 4 0 1 0 1-1 2l-2 1v1l1 1c-1 1-2 2-3 2v1c1 0 2 1 2 2l1-1c1-2 3-2 5-2-1 1 0 2-1 2-2 1-2 2-3 3-1 2-1 3-2 5s-2 5-3 7l-1 3h-1c0 4-1 7-2 10v1c0 1 0 2 1 3l-1 4v3l-1 12c0-2 0-3-1-5 0-2-1-5 0-7l1-1v-2c0-1 0-1 1-2 0-1 0-2-1-3h0-1l-1-2v5l1 1c-1 2 0 4-1 6v3h0c-1 1-1 2-2 4v-1l2-27 3-12 2-11c0-3 2-6 3-10 0-2 0-5 2-7h0v3 1z" class="X"></path><path d="M330 257c0 2-1 4-1 6v1h-1c0-3 1-7 2-11v-5c1-4 2-8 3-11h1 1c0-1 0-1 1-2v1 1l1 1c-1 1-2 2-3 2v1c1 0 2 1 2 2l1-1c1-2 3-2 5-2-1 1 0 2-1 2-2 1-2 2-3 3-1 2-1 3-2 5s-2 5-3 7l-1 3h-1 0l-1-2v-1z" class="F"></path><path d="M334 241c1 0 2 1 2 2-1 1-1 2-1 3-1 3-2 5-2 8v3l-1 3h-1 0l-1-2v-1c0-1 1-3 1-4 1-4 3-8 3-12z" class="Q"></path><path d="M336 243l1-1c1-2 3-2 5-2-1 1 0 2-1 2-2 1-2 2-3 3-1 2-1 3-2 5s-2 5-3 7v-3c0-3 1-5 2-8 0-1 0-2 1-3zm202 206l3 3 1-1c2 2 2 4 4 6 0 2 1 3 1 5 1 2 2 5 1 7-2 0-3-2-5-4l-2 2v-1 2 3h0c-1 1-1 2-1 2h-2-2v1h2 0c-1 1-2 1-2 2-2-1-2-1-3-1v1c0 1 1 2 1 2-1 1-1 1-2 1 0 1 0 1-1 3h0c-1-2-3-4-3-6h1v2h1l1-3c0-2-1-3-1-4v-1c0-3-1-4-2-7-2 0-4 0-6-1h-1c4 0 10-1 13-3v-1c-3-2-8-3-11-5l7-1c1 0 3-1 5-1l3-2z" class="U"></path><path d="M531 464h2 1c1 0 2 2 3 3h0l-1 1h-3l-1-1h2l-3-3z" class="X"></path><path d="M533 475h-1l1-2c-1-1-1-1-1-2l1-2h4c1 1 2 1 3 2l-2 2h-2v1h2 0c-1 1-2 1-2 2-2-1-2-1-3-1zm5-26l3 3 1-1c2 2 2 4 4 6 0 2 1 3 1 5 1 2 2 5 1 7-2 0-3-2-5-4l-1-2c-1-2-1-4-2-5 0-2-3-4-4-5h-3-3v-1c1 0 3-1 5-1l3-2z" class="Q"></path><path d="M538 449l3 3 1 2h0c-3 0-5-1-7-3l3-2zm286-152c1 1 2 1 2 3-1 2-1 3-2 5h-1c-1 1-3 2-4 4l-2 2c1 0 2 1 4 0l-1-1c1 0 0 0 1 1l-1 1h-1c-1 0-3 2-4 3v1h-2c0 1 0 1-1 2-2 1-3 2-5 3h-1v1h-1c-1 1-1 2-1 4h-3l-1 2h-1c-1-1-1 0-2 0v2c-1 0-2 0-3 1v1h-3c0 1-1 1-2 2v2h-2c0 1-1 2-2 3 0 1-1 1-1 2s-1 2-1 3c-2 1-3 2-4 3s-2 3-4 5v1-1c0-2 2-4 3-6 3-4 5-8 8-11l1-2c1-1 2-2 3-4 2-1 3-3 5-5 1-1 2-1 3-2v-1c0-3 4-7 7-8l1-1 4-4c-2 1-3 2-4 3h-1c3-4 9-6 14-10 2-1 4-3 5-4z" class="C"></path><path d="M824 297c1 1 2 1 2 3-2 0-2 1-4 2-1 1-3 2-4 3-1 0-2 1-2 1l-3 3c-2 1 0 0-1 1-2 0-2 1-3 2-2 1-3 3-4 4-1 0-2 1-3 2l-4 4v-1c0-3 4-7 7-8l1-1 4-4c-2 1-3 2-4 3h-1c3-4 9-6 14-10 2-1 4-3 5-4z" class="D"></path><path d="M388 238c-1 2-3 3-5 5-4 1-7 5-9 8h0l-1 1c1 1 2 1 2 0h2l4-4c1 0 2-1 4-2 3-1 6-2 10-3l1-1v1c1 0 1-1 2-1h1c0-1 0-1 1-1h1c1-1 1-1 2-1 3 1 6 3 8 5-18 1-33 7-45 21-6 6-12 14-13 23-1 2-1 5-1 7-1-1-1-3-1-5 1-12 6-22 13-32l13-15c4-3 7-5 11-6z" class="H"></path><path d="M395 243v1c-2 0-5 1-7 2s-5 3-7 2c1 0 2-1 4-2 3-1 6-2 10-3z" class="B"></path><path d="M179 342l3 1 3 2c0 1 0 2 1 3v2c2 1 4 3 6 6l-1 1c-2 0-2 0-3 1l1 1c1-1 1-1 2-1l-1 1c-1 2-1 2-1 4l1 2c-1 1-3 2-3 4l-2-1-1 1 1 1h-1l1 3v2c-2-1-3-2-5-4-4-6-9-13-11-20-1-1-3-6-4-7l4 1h1c0 1 0 1 1 1h5c2-1 3-1 3-4z" class="b"></path><path d="M176 359c1 0 2 0 2 1 1 1 1 1 1 2h-2c-1-1-1-1-1-3z" class="C"></path><path d="M182 343l3 2c0 1 0 2 1 3v2l-1 1v1h-1v1l-1-1v-1s0-1-1-2v-1c0-1 0-1-1-2 0-1 1-2 1-3z" class="G"></path><path d="M165 344l4 1h1c0 1 0 1 1 1h5l2 1 1 1-1 1h2-1l-1 1h1v1l-1 1v4h-1l-1 2h-1l-1 1-3-7-2-1c-1-1-3-6-4-7z" class="C"></path><path d="M170 347c2-1 3 0 4 0v1c-1-1-1 0-2 0h-1l1 2h1l1 1v2 1l-1-1c0-2-3-4-3-6z" class="X"></path><path d="M186 350c2 1 4 3 6 6l-1 1c-2 0-2 0-3 1l1 1c1-1 1-1 2-1l-1 1c-1 2-1 2-1 4l1 2c-1 1-3 2-3 4l-2-1-1 1 1 1h-1l1 3s-1 0-1-1l-1-1c-1 0-1-1-2-1 1-1 1-1 1-2-1-1-1-2-1-3l-2-3 2-1h-1v-1c0-1 0-1-1-2h0l1-1 3-2-2-2 1-1h1l1 1v-1h1v-1l1-1z" class="G"></path><path d="M186 350v1 2l-1 1c1 1 1 1 2 1h1v1h-2c-1 0-2-1-3-1l-2-2 1-1h1l1 1v-1h1v-1l1-1z" class="U"></path><path d="M180 357c1 0 1 0 2-1 1 1 2 1 4 2h1 1v1h0-2c0 1 0 1-1 1 0 1 0 1-1 2l1 1h0l-2-1h-1l-1 1v2l-2-3 2-1h-1v-1c0-1 0-1-1-2h0l1-1z" class="Z"></path><path d="M417 748l3 1 11 26 34 75c1 1 1 3 2 4v1c0 1 1 2 1 4l25 62c1 4 2 8 3 11 1 2 0 4 2 5-1 1-2 1-2 1-2-7-5-14-7-21l-21-54-19-43-9-20c-2-5-5-10-7-15l-16-37z" class="L"></path><path d="M412 392c2-1 5-2 7-3 1-1 0 0 1 0-1 1-1 3-2 4-1 3-1 5-1 7-1 2-1 3-2 4h-2c-2 2-2 4-3 7-1 1-2 1-3 3-3 5-5 11-9 15h0c-1-1-2-2-2-3-1-1-2-3-2-5-1-1-1-3-2-4v-3h-1v-1l1-1c-2-1-2-1-3-3 1 0 2 1 3 1 1-1 1 0 3 0 0 0 1-1 2-1-2-1-2-2-3-3l11-8c3-3 5-6 6-10v3l1 1z" class="G"></path><path d="M410 399l1 1h0l-1 2c-1 1-1 4-3 5l-1-1c0-2 1-2 2-4v-1l1-1 1-1z" class="W"></path><path d="M414 394h0c2-1 2-1 4-1-1 3-1 5-1 7-1 0-2 0-3-1-1 0-1 0-2-1 0-1 1-2 2-4z" class="Q"></path><path d="M412 392c2-1 5-2 7-3 1-1 0 0 1 0-1 1-1 3-2 4-2 0-2 0-4 1h0c-1 1-2 1-3 1l-1-1 2-2z" class="F"></path><path d="M394 406l11-8c0 4-3 7-6 9l-2 2c-2-1-2-2-3-3z" class="B"></path><path d="M402 414c-1-1-1-2-1-2l1-1c1 0 2 0 2-1 1 0 2-1 2-2 1 0 2 0 2-1h0c2-2 3-3 5-3-2 2-2 4-3 7-1 1-2 1-3 3h-3l-1 2h-1v-2z" class="Z"></path><defs><linearGradient id="a" x1="395.572" y1="413.985" x2="401.195" y2="423.528" xlink:href="#B"><stop offset="0" stop-color="#848282"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#a)" d="M392 414h1 1s1-1 2-1h0l1-1v1l1 1c0 1 1 1 2 1l2-1v2h1l1-2h3c-3 5-5 11-9 15h0c-1-1-2-2-2-3-1-1-2-3-2-5-1-1-1-3-2-4v-3z"></path><path d="M715 173l1-1 2 6v2l1 3v1c1 1 1 2 1 3l2 4 1 4 1 3v1 2l2 5c0 1 0 1 1 2l1 1v1 1c0 1 0 0 1 2h0c0 1 0 0 1 1-1-2-2-3-1-5l1 4 1-1c0-1-1-3-1-5v-1l-1-1v-2-1l2 5 2 10c1 4 1 7 3 10v1c-1 1 0 2 0 3h0l1-1v2c2 3 1 9 2 12l1 5c0 3 0 6 2 9h-1l1 2h0v3 3 1c0 1 0 1-1 3h0v1c0 1-1 2-1 2v1 2l-1 1v2 1h-1v-5-1h-1v-2c1-1 1-1 1-3h0c-2 2-1 5-1 7s-1 2 0 4v2c0 1-1 2 0 4 0-1 0-2 1-3l1-1v-1l1 2v2l-1 1v1c-1 0 0 1-1 2h0v1c0 1-1 2-2 2h-1v-1c1-2 0-1-1-2v-2c0-3 1-6 0-9 0 0 0-1 1-1v-4c2-2 1-17 1-20-1-2-1-7-1-8-1-2 0-4 0-6-1-1-1-3-1-4l-1-6c-1-4-2-7-4-11l-2-5-2-2v-1-2-1s-1-1-1-2l-2-9c-1-1-1-2-2-3s-2-3-3-5c0-1-1-2-1-2l-2-3c-1-1-1-1-1-2 0-2 0-4-1-6h3v-2z" class="h"></path><path d="M738 269v-6c1 2 0 3 1 6v2h1c-1-2-1-5-1-6 1-1 1-1 1-2v-6c2 3 2 9 1 11l-1 2c-1 1 0 3-2 4h-1v-2c1-1 1-1 1-3h0z" class="C"></path><defs><linearGradient id="b" x1="830.457" y1="360.471" x2="858.289" y2="355.485" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#bab9b8"></stop></linearGradient></defs><path fill="url(#b)" d="M854 339l3-1c0 2-1 4-2 6h1 2c0-1 1-1 2-1l-3 7c-3 6-6 12-10 18h0c-1 1-2 2-2 3-2 2-3 3-5 4-1-1-1-1-2-1s-1-1-2-2l1-1v-1c-1-1-2-1-4-1h-2l1-1v-2h0c-1-1-1-1-3-2h0l-1-1c2-1 3-1 5-1v-1h0c1-2 3-3 5-4v-1h-1l-1-1c3-2 4-4 6-6 0-1 0-1 1-2 0-1 1-2 1-4h2v-1l8-2v-1z"></path><path d="M848 346l1-1 2 2h-1-2v-1z" class="D"></path><path d="M841 363l2-1 2 2-1 2c-1-1-2-2-3-2v-1z" class="G"></path><path d="M846 343h0c-1 2-1 3-1 5l-3 1c0-1 0-1 1-2 0-1 1-2 1-4h2z" class="C"></path><path d="M841 371c0 1 0 1 1 2 2-1 3-3 5-5-1 1-2 2-2 3-2 2-3 3-5 4-1-1-1-1-2-1s-1-1-2-2l1-1h1l1-1c1 0 1 0 2 1z" class="G"></path><path d="M846 343c2 0 3-1 4-1h0c2 0 2 0 3 1 0 2 0 2-1 4h-1l-2-2-1 1-1-1-1-2z" class="I"></path><path d="M854 339l3-1c0 2-1 4-2 6h-1l-1-1c-1-1-1-1-3-1h0c-1 0-2 1-4 1v-1l8-2v-1z" class="R"></path><path d="M839 363h2v1c1 0 2 1 3 2-1 2-2 3-3 4v1c-1-1-1-1-2-1l-1 1h-1v-1c-1-1-2-1-4-1h-2l1-1v-1l4-1v-1l1-1c1-1 1-1 2-1z" class="b"></path><path d="M836 365l1-1c1-1 1-1 2-1l1 1c0 1 1 1 2 2h-2l-1-1h-1-2zm0 1c1 0 2 1 3 2v1h-1l-1-1v-1c-1 1-1 1-2 1s-1 1-2 1h-2l1-1v-1l4-1z" class="G"></path><defs><linearGradient id="c" x1="333.966" y1="613.098" x2="344.148" y2="630.308" xlink:href="#B"><stop offset="0" stop-color="#414249"></stop><stop offset="1" stop-color="#595b6a"></stop></linearGradient></defs><path fill="url(#c)" d="M348 602l10 22c-2 1-5 2-7 2-6 1-11 2-17 2h0c-5 1-12-2-16-5l12-6c4-2 9-5 13-8 1-2 4-3 5-5v-1l-4-1h4z"></path><defs><linearGradient id="d" x1="787.219" y1="513.9" x2="788.915" y2="535.044" xlink:href="#B"><stop offset="0" stop-color="#151516"></stop><stop offset="1" stop-color="#33343d"></stop></linearGradient></defs><path fill="url(#d)" d="M774 514l4 1 4 1h5l2 3 3 1 5 1h2l7 1-3 2c1 1 3 1 4 2-1 0-2 1-3 1l-8 5-2-1c-2 0-3 1-5 1-1 1-1 2-2 2-1 1-2 1-3 1l-1-1h-1l-5 3-3 1-1-2h0c0-1-1-2-2-3-1 1-1 1-2 0v-2h2v-1-1h0c-1-1-1-2 0-4-1 0-1-1-2-1 1-2 2-3 2-4l2 1 1-1v-1l1-1c0-1-1-2-1-3v-1z"></path><path d="M778 515l4 1 1 2c-3-1-4-2-5-3zm-1 4h2v1h0-1v1h0v2h-1l-1-1c0-1 0-2 1-3z" class="P"></path><path d="M782 516h5l2 3c-2 0-4-1-6-1l-1-2z" class="V"></path><path d="M771 530h2v-1h1l1 1c-1 1-1 1-2 1l-2 2c-1 1-1 1-2 0v-2h2v-1zm7-6h1c0 1 1 1 1 2s1 1 1 2h-1c-2 1-2 1-4 1 0-2 1-2 2-3v-2z" class="H"></path><path d="M801 528c0-1 0-2-1-2 1-1 2-1 3-2 1 1 3 1 4 2-1 0-2 1-3 1s-2 1-3 1z" class="f"></path><path d="M773 531c1 1 2 2 2 3v2s1 1 2 1l-3 1-1-2h0c0-1-1-2-2-3l2-2z" class="E"></path><path d="M801 528c1 0 2-1 3-1l-8 5-2-1 2-2c1 0 2 0 2-1h3z" class="K"></path><path d="M775 518l1 1h1c-1 1-1 2-1 3l1 1-1 1v-1l-2 1 1 1-2 1c0 1-1 2-1 3h-1 0c-1-1-1-2 0-4-1 0-1-1-2-1 1-2 2-3 2-4l2 1 1-1v-1l1-1z" class="P"></path><path d="M776 519h1c-1 1-1 2-1 3l1 1-1 1v-1l-2-2c1-1 1-1 1-2h1z" class="g"></path><path d="M346 235l1-2-1-1-1 1-1-1c1-1 3-1 4-2v7l1 1 1-1h0 1c-3 6-6 12-8 19-1 2-2 6-3 8v6c-2 6-3 12-3 18v1 9h-6v-1l-2-1c-1-1-1-2-1-3l1-12v-3l1-4c-1-1-1-2-1-3v-1c1-3 2-6 2-10h1l1-3c1-2 2-5 3-7s1-3 2-5c1-1 1-2 3-3 1 0 0-1 1-2s2-1 3-1h1v-4z" class="I"></path><path d="M342 240c1-1 2-1 3-1l-5 12c-1-2-1-4-2-6 1-1 1-2 3-3 1 0 0-1 1-2z" class="B"></path><path d="M337 289c-1-4 0-7-1-10v-1h0l1 1h0v-3c0-3 0-4 1-6 0-1 0-2 1-3 0-1 0-2 1-3v6c-2 6-3 12-3 18v1z" class="D"></path><path d="M329 278h4l-1 3v9c0 3 0 5-1 7l-2-1c-1-1-1-2-1-3l1-12v-3z" class="M"></path><path d="M329 278h4l-1 3h-3v-3z" class="F"></path><path d="M338 245c1 2 1 4 2 6l-4 12c-1 3-1 8-3 12v1 2h-4l1-4c-1-1-1-2-1-3v-1c1-3 2-6 2-10h1l1-3c1-2 2-5 3-7s1-3 2-5z" class="G"></path><path d="M331 274l2-1v2 1c-1-1-1-1-2-1v-1z" class="H"></path><path d="M330 274h1v1c1 0 1 0 2 1v2h-4l1-4z" class="B"></path><path d="M333 257c1-2 2-5 3-7v3l1 1c1 1 0 3-1 4s-2 1-2 2c-1 1-1 2-1 2l-1 1v-3l1-3z" class="J"></path><path d="M338 245c1 2 1 4 2 6l-4 12c-1-2-1-3 0-5 1-1 2-3 1-4l-1-1v-3c1-2 1-3 2-5z" class="F"></path><defs><linearGradient id="e" x1="429.521" y1="490.113" x2="412.257" y2="474.933" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#bcbbba"></stop></linearGradient></defs><path fill="url(#e)" d="M407 472v1c1 0 1 1 1 2l1 1 1 1c1 0 2 1 3 2h0 1c1 0 3 0 4-2h0l1-1h1v1l1-1-1-1 2-2 2 2c1-1 2-1 3-2l5 16v1c1 2 1 5 2 8v2l1 2 1 6c-1 1-1 1-2 1h-6v1h-2c-2-1-3-2-4-4l-2-4-3-7c-1-1-1-2-1-2l-10-21h1z"></path><defs><linearGradient id="f" x1="437.084" y1="503.568" x2="419.052" y2="493.054" xlink:href="#B"><stop offset="0" stop-color="#575656"></stop><stop offset="1" stop-color="#8f8d8e"></stop></linearGradient></defs><path fill="url(#f)" d="M416 493v-1l1 1 5 1c1-1 2-2 2-3 1-1 1-2 2-3h0c1 1 1 1 0 3h1c1-1 3-1 5-1 1 2 1 5 2 8v2l1 2 1 6c-1 1-1 1-2 1h-6v1h-2c-2-1-3-2-4-4l-2-4-3-7c-1-1-1-2-1-2z"></path><path d="M417 495c1 0 2 2 3 3s1 2 1 4c2 1 3 1 5 1l-2 1c1 1 1 1 2 1 0 1 0 1 1 2 0 1 1 1 1 2h0v1h-2c-2-1-3-2-4-4l-2-4-3-7z" class="J"></path><defs><linearGradient id="g" x1="673.979" y1="241.125" x2="643.02" y2="219.079" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#g)" d="M641 212v-1c1 1 2 1 3 2v-1c2 1 4 3 6 4l10 8c3 2 6 5 9 8s5 5 7 9l3 3c1 1 1 0 1 1 1 3 3 6 4 8 1 4 2 7 3 11v1h-1c-1-3-2-6-4-9-1-2-2-3-2-5-2-2-2-3-5-4h-7-1l-1 2-3 1s-1 1-1 2l-4-4-2-2 3 1v-2c2-1 3-2 4-3h2l-1-1h0c0-1-1-2-1-3l-4-3h0c-1-2-3-2-5-2l-2-2c-1-1-3-2-5-3 0-1-2-1-2-2-2-1-3-1-3-3-1 0-1-1-1-1l-1-1h0 2v-1l-1-1c0-1 1-1 0-2h0l1-1-1-4z"></path><path d="M641 212v-1c1 1 2 1 3 2v2c0 3 1 6 0 10-1-3-1-6-2-9l-1-4z" class="B"></path><path d="M644 212c2 1 4 3 6 4l10 8c0 2 3 4 5 6-3-1-5-2-7-4-3-3-7-6-11-8l-3-3v-2-1z" class="i"></path><path d="M652 231l6 1 9 6h1c2 2 4 4 5 6l1 1h1c0-2-1-3-2-5l-3-4-1-1h1c0-1-1-2-1-3 3 3 5 5 7 9l3 3c1 1 1 0 1 1 1 3 3 6 4 8 1 4 2 7 3 11v1h-1c-1-3-2-6-4-9-1-2-2-3-2-5-2-2-2-3-5-4h-7-1l-1 2-3 1s-1 1-1 2l-4-4-2-2 3 1v-2c2-1 3-2 4-3h2l-1-1h0c0-1-1-2-1-3l-4-3h0c-1-2-3-2-5-2l-2-2z" class="P"></path><path d="M659 235c2 0 4 1 5 2v2h2s1 0 1 1v1h1l1 1h0c1 1 2 2 2 4h0c-1 1-2 1-3 1h-1-1c0-1 1-1 1-2l-1-1c-1 0-2 1-2 1h-1v-1l2-1h1l-1-1-1-1h0c0-1-1-2-1-3l-4-3z" class="O"></path><path d="M665 242l1 1h-1l-2 1v1h1s1-1 2-1l1 1c0 1-1 1-1 2h1l-1 2-3 1s-1 1-1 2l-4-4-2-2 3 1v-2c2-1 3-2 4-3h2z" class="N"></path><path d="M656 246l3 1c2 1 5 0 7 0h1l-1 2-3 1s-1 1-1 2l-4-4-2-2z" class="Y"></path><path d="M642 377c1-1 2 0 3 0 2 3 4 5 6 7 2 3 4 6 6 10 0 5-4 11-5 16 0 1 0 1-1 2-1 2-1 4-2 6l-1 5c-1 1-2 1-3 2 0 1-1 2-1 3l-2 2v2c-1 0-1 1-1 2l-3 6c-1 1-1 2-2 3l-1-1-3-4-4-9 1-3 2-7h1l3-9 3-3h3l3 1c1 0 2-1 2-1 1-2 3-3 4-4 1-2 2-3 3-5v-1c0-2-1-3-2-5-2-1-3-4-5-6-1-3 0-5-1-8h-3v-1z" class="I"></path><path d="M643 415c0 1 1 2 2 2s2-2 3-4c0 2 0 3-2 5h-1v1l-2 2c-1 1-2 2-2 3-3 5-5 10-5 16l-1 2h0l-3-4c3-3 3-8 5-11 1-4 4-8 6-12z" class="B"></path><path d="M638 407h3l3 1 6 2-2 3c-1 2-2 4-3 4s-2-1-2-2c-2 4-5 8-6 12-2 3-2 8-5 11l-4-9 1-3 2-7h1l3-9 3-3z" class="Z"></path><path d="M633 421h3c-1 3-2 5-5 6 0-1 0-2 1-3 1-2 0-1 0-3h1z" class="L"></path><path d="M632 419s1 1 1 2h-1c0 2 1 1 0 3-1 1-1 2-1 3-1 1 0 1-1 1l-1-2 2-7h1z" class="M"></path><path d="M638 407h3l3 1 6 2-2 3c-1 2-2 4-3 4s-2-1-2-2l1-3c-1 0-1 0-2 1h-2c-2 3-3 5-4 8h-3c0-1-1-2-1-2l3-9 3-3z" class="K"></path><path d="M638 407v3c2 2 4 1 7 1-1 1 0 1-1 1s-1 0-2 1h-2l-2 1s0 1-1 1c-1 1-1 2-2 1 0-1 0-2 1-3v-1l-1-2 3-3z" class="B"></path><path d="M638 407h3l3 1 6 2-2 3c-1 2-2 4-3 4s-2-1-2-2l1-3c1 0 0 0 1-1-3 0-5 1-7-1v-3z" class="E"></path><path d="M676 578h1c2 2 4 3 7 4h0c5 3 10 4 15 6l10 2h-1-1 0-1-1v1 1h-3l2 1c0 1 0 2 1 3l-6 1v1h3c-2 2-6 1-8 2-3 0-5 1-8 2-2 0-5 1-8 0l1 3h0c-2 0-3 0-4-1-2 1-4 1-5 0h-2c-1 0-2 0-3-1l2-4 2-5 5-11c0-2 1-4 2-5z" class="c"></path><path d="M674 597c1-1 2-1 3-2l2-1v1c0 1 0 2-1 3 1 1 1 0 2 1l1-1 1 1-1 1h-2-1c-2-1-3-2-4-3z" class="Y"></path><path d="M669 594c1 1 1 2 3 2 1 0 1 0 2 1s2 2 4 3h1 2l1-1c1 1 1 1 3 2l1 1c-2 0-5 1-8 0l1 3h0c-2 0-3 0-4-1-2 1-4 1-5 0h-2c-1 0-2 0-3-1l2-4 2-5z" class="E"></path><path d="M669 594c1 1 1 2 3 2 1 0 1 0 2 1s2 2 4 3h1 2l1-1c1 1 1 1 3 2l1 1c-2 0-5 1-8 0h-1 0c-1 0-1-1-1-1-1-2-3-2-5-3v1l1 2v1c-2-1-4-1-5-3l2-5z" class="g"></path><path d="M676 578h1c2 2 4 3 7 4h0c5 3 10 4 15 6l10 2h-1-1 0-1-1v1 1h-3c-10-1-19-7-28-9 0-2 1-4 2-5z" class="P"></path><defs><linearGradient id="h" x1="613.9" y1="208.536" x2="617.223" y2="227.154" xlink:href="#B"><stop offset="0" stop-color="#1c1a1a"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#h)" d="M615 208h4 2c5 1 12-1 16 1l1 1h4l2 2v1c-1-1-2-1-3-2v1l1 4-1 1h0c1 1 0 1 0 2l1 1v1h-2 0l1 1s0 1 1 1c0 2 1 2 3 3 0 1 2 1 2 2l-1 1c-2-3-5-4-8-6h-5c-1-1-2-1-4-1l-18-5h-6c-7 1-15 2-22 6l-6 3c-2 2-5 3-6 5-1 1-2 1-3 2v-1c0-1 3-3 4-4 2-1 4-2 5-3-3 0-3 1-6 0l4-2c5-5 15-9 22-11 5-2 12-4 18-4z"></path><path d="M637 209l1 1h4l2 2v1c-1-1-2-1-3-2v1c-2 0-2 1-3 0-2-1-3-1-5-1l1-1h3v1-2z" class="O"></path><path d="M575 223c5-5 15-9 22-11h1c-3 1-6 2-8 4 1 0 2-1 3-1s1-1 2-1h1c1 0 2 0 3-1 2 0-2 1 1 0h2l-14 5-1 2-10 5c-3 0-3 1-6 0l4-2z" class="Y"></path><path d="M575 223h0c5 0 9-3 13-5l-1 2-10 5c-3 0-3 1-6 0l4-2z" class="E"></path><path d="M587 220c5-2 14-6 20-5 1 0 2-1 3-1 1-1 3 0 5 0h6l1 1c1 0 2 0 3 1v1h2l1 1h-1c-1 0-1 0-2 1h0 0c1 0 1 1 2 1h1v-1h0 2c1 0 3 1 4 2 2 1 3 0 4 2h-5c-1-1-2-1-4-1l-18-5h-6c-7 1-15 2-22 6l-6 3c-2 2-5 3-6 5-1 1-2 1-3 2v-1c0-1 3-3 4-4 2-1 4-2 5-3l10-5z" class="N"></path><defs><linearGradient id="i" x1="529.335" y1="476.816" x2="536.357" y2="499.044" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#c7c6c5"></stop></linearGradient></defs><path fill="url(#i)" d="M543 465c2 2 3 4 5 4h1l4 1h0l1 1c1 6-1 12-4 17l-3 5-6 6c-4 2-8 3-12 5l-1 2c-2 0-3 1-4 2v2l-1 1-1 2c-2-1-2-3-3-5h-14v-1-2c1 1 3 2 4 1s1-1 1-2h0c1 1 1 2 2 3h0 5c3-1 6-2 10-3v-2-1s0-1-1-1c1-1 2-2 3-2v-3-3c0-1 0-2 1-3v-4c1-1 1-2 1-3h0c1-2 1-2 1-3 1 0 1 0 2-1 0 0-1-1-1-2v-1c1 0 1 0 3 1 0-1 1-1 2-2h0-2v-1h2 2s0-1 1-2h0v-3-2 1l2-2z"></path><path d="M529 492c0-1 0-2 1-3h4 0l-1 1v2c-2 2-3 3-4 6v-3-3z" class="R"></path><path d="M541 468c1 0 1 0 2 1v1 3c1 2 1 5 1 8l-9 18c-2 1-4 3-6 4v1l-1 2c-2 0-3 1-4 2v2l-1 1-1 2c-2-1-2-3-3-5h-14v-1-2c1 1 3 2 4 1s1-1 1-2h0c1 1 1 2 2 3h0 5c3-1 6-2 10-3 1-1 2-1 2-2 5-3 8-9 10-14 1-2 2-3 2-5 0-3 0-5-1-8-1-1-1-1 0-2 0 0 0-1 1-2h0v-3z" class="G"></path><path d="M541 471c2 3 1 6 1 9h0c0 1 0 2-1 3 0-3 0-5-1-8-1-1-1-1 0-2 0 0 0-1 1-2z" class="Q"></path><path d="M543 465c2 2 3 4 5 4h1l4 1h0l1 1c1 6-1 12-4 17l-3 5-6 6c-4 2-8 3-12 5v-1c2-1 4-3 6-4l9-18c0-3 0-6-1-8v-3-1c-1-1-1-1-2-1v-2 1l2-2z" class="I"></path><path d="M543 465c2 2 3 4 5 4h1c-2 1-2 1-4 1l2 2c0 1 0 2-1 3l-2 6c0-3 0-6-1-8v-3-1c-1-1-1-1-2-1v-2 1l2-2z" class="Z"></path><path d="M553 470l1 1c1 6-1 12-4 17l-3 5-6 6c-4 2-8 3-12 5v-1c2-1 4-3 6-4 6-1 9-10 12-15 1-1 1-1 1-2 1-3 2-4 2-7 0-1 0-3 1-4l2-1z" class="h"></path><defs><linearGradient id="j" x1="655.331" y1="145.573" x2="658.998" y2="175.53" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#c3c2c0"></stop></linearGradient></defs><path fill="url(#j)" d="M637 146l17 4 10 4c1 0 3 1 5 1l6 5 2 1c2 3 2 4 2 7h1l1 1v1l-1 1 1 1-2 1c-1 0-2 0-4 2h1 2 0c1 0 2 1 3 1-3 1-5-1-7-1l-1 1c-1 0 0 0-1-1-2-2-9-3-12-2h-1v-1c-6-8-15-13-23-17-1-1-1-2-1-2v-1-3c1-1 2-1 3-1h0l-1-2z"></path><path d="M637 146l17 4 10 4c1 0 3 1 5 1l6 5c-1 1-1 2-2 2-1-1-1-2-2-3h-2-1-1v-1h1c-1-1-1-1-2-1h-1l1-1c-2 0-4 0-6-1s-3-2-5-2l-9-3-8-2-1-2z" class="X"></path><defs><linearGradient id="k" x1="242.805" y1="515.912" x2="246.66" y2="539.977" xlink:href="#B"><stop offset="0" stop-color="#121215"></stop><stop offset="1" stop-color="#34353c"></stop></linearGradient></defs><path fill="url(#k)" d="M244 515c2 0 4-1 7-1v2l1 1 2 2c1 0 2-1 3-2 2 0 4 0 6 1h2l2 1h-1-2l-1 1h-2-1v1c1 0 1 1 2 1l-2 2 1 2-2 1-1-2-1 1c0 1 0 2 1 3 0 0-1 1-1 2l1 2h0 2v6c-3 0-5-1-7-1-1 0-2 1-3 0-3 0-4-3-7-2 1 1 2 1 3 2l-1 1c-6-2-10-5-15-7-4-2-7-4-11-6-1 0-5-3-6-4v-1h3c3-1 7-2 11-2l17-4z"></path><path d="M230 532c1-1 3-1 4-2h1c1 1 1 2 1 3 2 1 3 2 5 2v1h2c1 1 2 1 3 2l-1 1c-6-2-10-5-15-7z" class="H"></path><path d="M216 521c1 0 2 0 3 1 1 0 8-1 10-2v1l-7 1c1 2 2 2 2 4h-1-1-3c-1 0-5-3-6-4v-1h3z" class="g"></path><path d="M216 521c3-1 7-2 11-2l17-4c-4 4-10 5-15 5-2 1-9 2-10 2-1-1-2-1-3-1z" class="V"></path><defs><linearGradient id="l" x1="687.438" y1="611.092" x2="676.517" y2="627.904" xlink:href="#B"><stop offset="0" stop-color="#5d606e"></stop><stop offset="1" stop-color="#8e919d"></stop></linearGradient></defs><path fill="url(#l)" d="M663 609l2-6c1 1 2 1 3 1h2c1 1 3 1 5 0 1 1 2 1 4 1h0c3 1 6 4 9 6 3 3 6 5 10 7 3 2 7 3 10 5-3 2-8 5-13 5l-1-1c-4 1-12 1-16-1h-2l1 1h0-2c-1-1-3-1-4-1l-1 1c-3 0-8-3-12-4l-1-1 2-4c1-4 2-7 4-9z"></path><path d="M671 620c1 0 2 0 2 1h2v1l1 1c1 1 2 1 2 3h-2c-1-1-3-2-4-2 1-1 0 0 1-2l-2-2z" class="U"></path><path d="M664 620l1-1c1 0 3 0 5 1h1l2 2c-1 2 0 1-1 2-3-1-6-2-8-4z" class="C"></path><path d="M659 618l5 2c2 2 5 3 8 4 1 0 3 1 4 2l1 1h0-2c-1-1-3-1-4-1l-1 1c-3 0-8-3-12-4l-1-1 2-4z" class="E"></path><path d="M663 609l2-6c1 1 2 1 3 1h2c1 1 3 1 5 0 1 1 2 1 4 1h0c3 1 6 4 9 6l-1 1h-1c-1 0-2 0-3-1-2-1-5-3-8-3h-1-1-2c-3 1-6 2-8 1z" class="W"></path><defs><linearGradient id="m" x1="678.332" y1="251.481" x2="672.794" y2="286.381" xlink:href="#B"><stop offset="0" stop-color="#3a393b"></stop><stop offset="1" stop-color="#686769"></stop></linearGradient></defs><path fill="url(#m)" d="M668 247h7c3 1 3 2 5 4 0 2 1 3 2 5 2 3 3 6 4 9h1v-1c1 3 2 6 2 8v1l-1-3-1 1v2 1 2l-1 1 1 1v3 1c0 1 0 4 1 6v4l1 1c1 1 1 3 1 4h-12c-2 0-3 0-5-1l1-2-2-2 1-2c-1-2-1-3-1-4h2v-1-1l-1-2c2-1 1-2 2-4l-5-11 2 2c1 2 3 6 4 6h0c0-3-3-7-4-10-3-4-6-9-10-13 0-1 1-2 1-2l3-1 1-2h1z"></path><path d="M683 269c1 1 2 1 3 3v3c-1-2-4-3-4-5l1-1z" class="F"></path><path d="M663 250c3 3 4 6 6 10 1 1 3 3 3 5-3-4-6-9-10-13 0-1 1-2 1-2z" class="P"></path><path d="M674 254h3c-1 1 0 1-1 2h0c1 0 1 1 2 1v1h1c0-1-1-2-1-2l1-1 1-1v1c0 1 0 1-1 2 1 1 1 1 0 2h2v2 2c0 2 0 2-1 3-1-3-2-5-4-8v-1s0-1-1-1l-1-1v-1z" class="B"></path><path d="M668 247h7c3 1 3 2 5 4 0 2 1 3 2 5l-2-2-1 1-1 1s1 1 1 2h-1v-1c-1 0-1-1-2-1h0c1-1 0-1 1-2h-3c-1-2-1-2-1-3s-1-1-1-1l-6-1 1-2h1z" class="H"></path><path d="M668 247h7c0 1 2 2 2 4l-1-1-1 1 1 1h-1c-1 0-1-1-2-1 0-1-1-1-1-1l-6-1 1-2h1z" class="c"></path><path d="M680 254l2 2c2 3 3 6 4 9h1v-1c1 3 2 6 2 8v1l-1-3-1 1v2 1 2l-1 1v-2-3c-1-2-2-2-3-3-2 0-2-2-3-3 1-1 1-1 1-3v-2-2h-2c1-1 1-1 0-2 1-1 1-1 1-2v-1z" class="N"></path><path d="M670 267l2 2c1 2 3 6 4 6h0l1 1c1 1 2 3 2 5l3 9c0 1 0 1 1 1v-2h2v-2c1 0 2 0 3 1h0v4l1 1c1 1 1 3 1 4h-12c-2 0-3 0-5-1l1-2-2-2 1-2c-1-2-1-3-1-4h2v-1-1l-1-2c2-1 1-2 2-4l-5-11z" class="W"></path><path d="M675 278c0 1 1 2 1 4 0 3 3 7 3 11h-1c0-1 0-1-1-2 0-2-2-5-3-7l-1-2c2-1 1-2 2-4z" class="E"></path><path d="M672 292l1-2c-1-2-1-3-1-4h2c1 1 1 2 2 3 1 3 2 6 2 8-2 0-3 0-5-1l1-2-2-2z" class="O"></path><path d="M672 292l1-2c-1-2-1-3-1-4h2c1 1 1 2 2 3l-3-2v1 1c1 2 1 3 1 5l-2-2z" class="H"></path><path d="M571 231c1-2 4-3 6-5l6-3c7-4 15-5 22-6h6l18 5c2 0 3 0 4 1h5c3 2 6 3 8 6l1-1c2 1 4 2 5 3l2 2c2 0 4 0 5 2h0l4 3c0 1 1 2 1 3h0l1 1h-2c-1 1-2 2-4 3v2l-3-1c-1 0-5-4-6-5-4-2-7-4-11-6 0 0-2-1-3-2l-7-3c-2-1-4-2-6-2-6-2-11-3-16-3 1 0 2 1 3 1 2 0 3 1 4 2h-1c-8-1-17-1-25 1l-9 2v-1h0c0-1 1-1 1-2l-2 1h-3l-4 2z" class="T"></path><path d="M634 225c1 0 2 0 3 1h0c2 0 3 1 4 2s1 1 2 1l1 1c1 1 2 3 4 4h0l2 1v2l-3-2c-2-2-5-5-6-5-2 0-2-1-3-2h0c-2-1-3-2-4-3z" class="J"></path><path d="M583 224c3-2 5-3 8-3 9-2 17-2 26-1 1 0 2 1 3 1 1 1 2 1 2 1 2 0 3 1 4 1 2 1 6 1 8 2 1 1 2 2 4 3h0c1 1 1 2 3 2 1 0 4 3 6 5-3-1-4-3-6-4l-2-1c-2 0-2-1-3-1-2-1-3-2-5-2-2-1-3-2-6-3-2-1-4-2-7-2-12-2-23-2-35 2z" class="f"></path><path d="M583 224c12-4 23-4 35-2 3 0 5 1 7 2 3 1 4 2 6 3 2 0 3 1 5 2l3 3c2 0 4 2 5 4h0c-2-1-3-1-5-1 0 0-2-1-3-2l-7-3-2-2-1-1-1-1h0c-3 0-4-2-6-2h-2c-4-2-9-2-14-2-8 0-19 0-25 7h-3c2-2 5-4 8-5z" class="L"></path><path d="M578 229c6-7 17-7 25-7 5 0 10 0 14 2h2c2 0 3 2 6 2h0l1 1 1 1 2 2c-2-1-4-2-6-2-6-2-11-3-16-3 1 0 2 1 3 1 2 0 3 1 4 2h-1c-8-1-17-1-25 1l-9 2v-1h0c0-1 1-1 1-2l-2 1z" class="G"></path><path d="M580 228c9-3 18-3 27-3 1 0 2 1 3 1 2 0 3 1 4 2h-1c-8-1-17-1-25 1l-9 2v-1h0c0-1 1-1 1-2z" class="S"></path><path d="M633 223h5c3 2 6 3 8 6l1-1c2 1 4 2 5 3l2 2c2 0 4 0 5 2h0l4 3c0 1 1 2 1 3h0l1 1h-2c-1 1-2 2-4 3v2l-3-1c-1 0-5-4-6-5-4-2-7-4-11-6 2 0 3 0 5 1h0c-1-2-3-4-5-4l-3-3c1 0 1 1 3 1l2 1c2 1 3 3 6 4l3 2v-2l-2-1h0v-1h0c-1-2-2-3-4-4-1-1-2-1-3-2 0-1-1-1-2-2-2 0-4-1-6-2z" class="F"></path><path d="M651 238h1v-3h1v1 1c1 1 1 2 1 3-1 0-2-1-3-2z" class="T"></path><path d="M659 245c-1 0-2-1-3-2h1v-3h2v1h3l1 1c-1 1-2 2-4 3z" class="N"></path><g class="B"><path d="M659 245c0-1-1-2 0-3l1 1 3-1c-1 1-2 2-4 3z"></path><path d="M654 233c2 0 4 0 5 2h0l4 3c0 1 1 2 1 3h0l1 1h-2l-1-1h-3v-1l2-1h-1-1-1v-1h1v-1l-1-1c-1-2-2-2-4-3z"></path></g><path d="M636 229c1 0 1 1 3 1l2 1c2 1 3 3 6 4l3 2 1 1c1 1 2 2 3 2h0l1 2c0 1 0 1-1 1l-10-7h0c-1-2-3-4-5-4l-3-3z" class="M"></path><defs><linearGradient id="n" x1="184.109" y1="460.172" x2="216.301" y2="458.036" xlink:href="#B"><stop offset="0" stop-color="#2b2c31"></stop><stop offset="1" stop-color="#484953"></stop></linearGradient></defs><path fill="url(#n)" d="M189 449c9 1 19 2 28 1h0 4c2 1 3 2 5 3l-1 1h-1c0-1 0-1-1-1v1h0c0 2 0 2-1 4l2-1h1 3 0c1 0 1 0 2-1 2-1 5 1 7 1h-4c-2 1-4 3-6 4v1l1 1c-1 0-1-1-1-1l-2 2 1 2 2 1v1c1 1 1 1 1 2h0-2c-1 0-2 0-3 1h-1v2l-1 1v-1c-2 0-3 1-4 1l-2 2h0-1l-6-2v1c-5-2-10-5-14-8h1v-1h-1l1-1-1-2-1-1-2 1c0-1-1-1-2-2v-1c-1-2-2-2-4-3l1-2c-1-1-2-2-3-4h1 6-1l-1-1v-1z"></path><path d="M190 461h2l-1-2h1v-2l1 1c0 1 2 2 3 2v1c-1 1-1 1-1 2h0l-1-1-2 1c0-1-1-1-2-2z" class="H"></path><path d="M215 456c1 0 1 1 2 1v2c0 1-1 1-1 2v1h-1-1c0-2-1-4 0-6h1z" class="F"></path><path d="M189 449c9 1 19 2 28 1h0 4c-1 1-2 1-3 1h-10c-1 0-3 0-5 1 2-1 5-1 6 0-6 0-12-1-18-1h-1l-1-1v-1z" class="T"></path><path d="M221 450c2 1 3 2 5 3l-1 1h-1c0-1 0-1-1-1v1h0c0 2 0 2-1 4h-2v1h1v2c-3-1-3 1-5 0 0-1 1-1 1-2v-2c-1 0-1-1-2-1l2-2-1-1h-2 0c1-1 2-1 4-2 1 0 2 0 3-1z" class="N"></path><path d="M223 454c0 2 0 2-1 4h-2v1h1v2c-3-1-3 1-5 0 0-1 1-1 1-2v-2h2c1-2 2-2 4-3z" class="J"></path><path d="M195 463c2 0 3 0 4 1v2h-1c2 1 2 1 4 0v1h0c0-1 0-1 1-2l-1-2 1-1c0 1 1 1 1 3l2-1v-1l1 1 2 1h1l3 3h0l-1 1c-1-1-2-2-4-2h0l1 3h-1-1 0c0 2 3 1 3 3l-1 1v1c-5-2-10-5-14-8h1v-1h-1l1-1-1-2h0z" class="K"></path><path d="M207 464l2 1h1l3 3h0l-1 1c-1-1-2-2-4-2-1-1 0-1-1-1h0-1 0l-1-1c1-1 1 0 2-1z" class="F"></path><path d="M228 457h0c1 0 1 0 2-1 2-1 5 1 7 1h-4c-2 1-4 3-6 4v1l1 1c-1 0-1-1-1-1l-2 2 1 2 2 1v1c1 1 1 1 1 2h0-2c-1 0-2 0-3 1h-1v2l-1 1v-1c-2 0-3 1-4 1l-2 2h0-1l-6-2 1-1c0-2-3-1-3-3h0 1 1l-1-3h0c2 0 3 1 4 2l1-1h4l-2-2v-2l2-1-1-1v-1c2 1 2-1 5 0v-2h-1v-1h2l2-1h1 3z" class="d"></path><path d="M225 464s0-1-1-1l1-1h2l-2 2z" class="f"></path><path d="M225 457h3c-1 2-1 2-2 3l-1-1v-2zm-3 1l2-1c0 2 0 3-1 4-1-1-1-2-1-3z" class="M"></path><path d="M226 466l2 1c-1 1-2 1-3 1h-1v-1l2-1z" class="W"></path><path d="M222 458c0 1 0 2 1 3-1 2 0 3-1 5v2c-1 1-2 1-4 1l1-1c0-1 1-2 1-2l-2-1 1-1 2-1v-2-2h-1v-1h2z" class="f"></path><path d="M216 461c2 1 2-1 5 0v2l-2 1-1 1 2 1s-1 1-1 2l-1 1c0 2-1 2-2 3h-5c0 1 3 2 4 4l-6-2 1-1c0-2-3-1-3-3h0 1 1l-1-3h0c2 0 3 1 4 2l1-1h4l-2-2v-2l2-1-1-1v-1z" class="W"></path><defs><linearGradient id="o" x1="273.871" y1="521.985" x2="275.251" y2="539.675" xlink:href="#B"><stop offset="0" stop-color="#313239"></stop><stop offset="1" stop-color="#51525e"></stop></linearGradient></defs><path fill="url(#o)" d="M277 520l1-2 2 1v1l2-2 1 1c1 0 1-1 2-1s1 0 2-1h2l-1 2 1 1c1 0 1 0 2-1v1l3-2 1 1c0 1 0 1-1 2v3c-1 1-2 1-2 2l-1 2h0v2l-1 1h0 1l1 2 1 1c-2 1-5 2-7 3h0v1l-1 1h0c-2 1-5 1-8 2-1 0-2 0-2 1h-1l-2 1h-1c-1 0-2 1-3 1l-1 1c3 2 4 0 7 1h1l2 2c-2 1-3 0-4 1-3-1-6-1-9-2s-5-3-8-4l-11-4 1-1c-1-1-2-1-3-2 3-1 4 2 7 2 1 1 2 0 3 0 2 0 4 1 7 1v-6h-2 0l-1-2c0-1 1-2 1-2-1-1-1-2-1-3l1-1 1 2 2-1-1-2 2-2c-1 0-1-1-2-1v-1h1 2l1-1h2 1v1h2c1-1 1-2 3-2l1 2h1v-1c2 0 2 1 3 1z"></path><path d="M269 530h1v1c0 1-1 2-1 4l-2-2 2-3zm2-3c1 0 2-1 3-2l1 1c-1 0-2 1-3 3h0c1 1 0 1 1 2h-1c-1-1-1-3-1-4zm15 10h-2l5-5v-1l1 1h0c0 1 0 1 1 2l1-1 1 1c-2 1-5 2-7 3z" class="E"></path><path d="M277 522l1-1 2 1c0-1 0-1 2-2 0 1 0 1 1 2 1-1 1-1 3-2 0 2-1 2-1 3h-1l-1-1c-1 1-2 1-2 2l-1-1h-3v-1z" class="H"></path><path d="M277 520l1-2 2 1v1l2-2 1 1c1 0 1-1 2-1s1 0 2-1h2l-1 2 1 1c1 0 1 0 2-1v1c-1 0-2 1-3 1-1-1-1-1-2-1-2 1-2 1-3 2-1-1-1-1-1-2-2 1-2 1-2 2l-2-1-1 1-1-1 1-1z" class="g"></path><path d="M291 520l3-2 1 1c0 1 0 1-1 2v3c-1 1-2 1-2 2l-1 2h0c-1 0-1 0-1-1h0l-2 1-1-1v-2c1-1 1-2 1-4h0c1 0 2-1 3-1z" class="H"></path><path d="M246 538c3 1 6 1 9 2 7 1 14 1 22 1-1 0-2 0-2 1h-1l-2 1h-1c-1 0-2 1-3 1l-1 1c3 2 4 0 7 1h1l2 2c-2 1-3 0-4 1-3-1-6-1-9-2s-5-3-8-4l-11-4 1-1z" class="P"></path><path d="M267 519v1h2c1-1 1-2 3-2l1 2h1v-1c2 0 2 1 3 1l-1 1 1 1v1h-1l1 1c0 1 0 1 1 2l-1 1-2-1-1-1c-1 1-2 2-3 2h0c-1 1-1 0-1 1h-1-1c-1 0-2-1-3-1s-1 0-2 1h0c-1 2-2 4-3 5h-2 0l-1-2c0-1 1-2 1-2-1-1-1-2-1-3l1-1 1 2 2-1-1-2 2-2c-1 0-1-1-2-1v-1h1 2l1-1h2 1z" class="H"></path><path d="M262 522c2 0 2 0 4-1h1l1 1 1-1c1 1 1 1 2 0h5l1 1v1h-1c0-1-1-1-1-1h-1-2c-1 0-1 0-1 1l-1-1c-1 1-2 0-3 1-1 0-3 2-5 3h-1l-1-2 2-2z" class="V"></path><path d="M267 519v1h2c1-1 1-2 3-2l1 2h1v-1c2 0 2 1 3 1l-1 1h-5c-1 1-1 1-2 0l-1 1-1-1h-1c-2 1-2 1-4 1-1 0-1-1-2-1v-1h1 2l1-1h2 1z" class="P"></path><defs><linearGradient id="p" x1="367.206" y1="309.565" x2="371.814" y2="317.97" xlink:href="#B"><stop offset="0" stop-color="#27282f"></stop><stop offset="1" stop-color="#40414a"></stop></linearGradient></defs><path fill="url(#p)" d="M388 299c2 1 5 2 7 4 0 3-1 8-2 11-8 3-18 4-25 10v1c0 1 1 2 2 3-2 2-3 3-5 4l-2 1-2-1c-2 1-3 3-4 4l-2-1c-1 1-2 2-4 1 0 0-2 0-2-1-4-1-6-2-9-1h0v-3l1-2 2-3c10-10 23-16 36-22l9-3v-2z"></path><path d="M393 307c0-2 0-2 2-4 0 3-1 8-2 11-8 3-18 4-25 10v1c0 1 1 2 2 3-2 2-3 3-5 4l-2 1-2-1c-2 1-3 3-4 4l-2-1c-1 1-2 2-4 1 0 0-2 0-2-1-4-1-6-2-9-1h0v-3l1-2 2-3c3 0 5 1 7 2 3 1 5-1 8-2l9-7c1-1 3-2 4-3l16-5c2-1 4-2 6-4z" class="f"></path><path d="M368 325c0 1 1 2 2 3-2 2-3 3-5 4l-2 1-2-1c-2 1-3 3-4 4l-2-1c-1 1-2 2-4 1 0 0-2 0-2-1-4-1-6-2-9-1h0v-3c4 0 8 2 12 3 5-2 9-6 15-9h0 1z" class="X"></path><path d="M366 327c-1 2-1 2-1 4v1l-2 1-2-1c1-3 3-4 5-5z" class="U"></path><path d="M368 325c0 1 1 2 2 3-2 2-3 3-5 4v-1c0-2 0-2 1-4l1-2h1z" class="e"></path><path d="M343 326c3 0 5 1 7 2 3 1 5-1 8-2l9-7c1-1 3-2 4-3l16-5c2-1 4-2 6-4-1 3-1 3-2 5l-7 3c-2 0-3 1-4 1-4 1-12 3-14 7v1c-5 2-9 5-14 7-3-1-7-2-11-2l2-3z" class="F"></path><path d="M382 225l1 1v1c-1 1-1 2-1 4l-2 2c0 2-2 2-2 3v2l2-1c2-1 4-3 6-5 0-1 1-1 2-1v1l1 1h1c1 0 2-1 3-1l2 1c-3 1-5 2-7 3v2c-4 1-7 3-11 6l-13 15c-7 10-12 20-13 32 0 2 0 4 1 5v1h-5l-2 1c-2-1-5-1-8 0v-9-1c0-6 1-12 3-18v3l1-1c0-2 1-3 1-5 2-3 4-1 7-2 1 0 3 0 4 1 1 0 1 1 2 2l2-4 2-4 4-6 2-2-2-2h1l2-2v-1l-4 1v-1c1 0 2-1 3-1-1-1-2-1-3-2 1-1 1-2 1-2v-1l3-3c1 0 0 0 1-1h0l5-5h0c2-1 3-2 4-2 2 0 4-4 6-5z" class="U"></path><path d="M347 294h3v-2l1-1c0 2 0 4 1 5v1h-5v-3z" class="G"></path><path d="M366 247h0l2-1h4l-7 6-2-2h1l2-2v-1z" class="E"></path><path d="M388 231v1l1 1h1c-3 2-6 5-10 7v-3c2-1 4-3 6-5 0-1 1-1 2-1z" class="d"></path><path d="M364 259c0-1 0-2 1-3l1-1-1-1s0-1 1-1c3-4 7-8 11-9l-13 15z" class="G"></path><path d="M374 240l1-1h1c0 2 0 1-1 2l1 1h0l-4 4h-4l-2 1h0l-4 1v-1c1 0 2-1 3-1l1-2c2-1 3-2 5-2 1 0 2 0 3-1v-1z" class="T"></path><path d="M382 225l1 1v1c-1 1-1 2-1 4l-2 2c0 2-2 2-2 3v2l2-1v3l-4 2h0l-1-1c1-1 1 0 1-2h-1l-1 1v1c-1 1-2 1-3 1-2 0-3 1-5 2l-1 2c-1-1-2-1-3-2 1-1 1-2 1-2v-1l3-3c1 0 0 0 1-1h0l5-5h0c2-1 3-2 4-2 2 0 4-4 6-5z" class="N"></path><path d="M377 233c1-2 4-4 6-6-1 1-1 2-1 4l-2 2h-3z" class="T"></path><path d="M366 244l1-2v-3c1-1 2-2 3-2s1 0 1-1h1c1-2 2-2 4-3 0 1-1 2-2 2-1 1-2 2-3 2-1 1-1 2-2 3h1 1 3v1c-1 1-2 1-3 1-2 0-3 1-5 2z" class="F"></path><path d="M380 233c0 2-2 2-2 3v2l2-1v3l-4 2h0l-1-1c1-1 1 0 1-2h-1l-1 1h-3-1-1c1-1 1-2 2-3 1 0 2-1 3-2 1 0 2-1 2-2h1 3z" class="J"></path><path d="M349 265c1 0 3 0 4 1 1 0 1 1 2 2l-3 6c-2 5-4 11-5 16v4 3l-2 1c-2-1-5-1-8 0v-9-1c0-6 1-12 3-18v3l1-1c0-2 1-3 1-5 2-3 4-1 7-2z" class="L"></path><path d="M344 282h2v2c-2 0-2 0-3 1l-1-1 1-2h1z" class="Z"></path><path d="M340 280c0-1 1-1 2-2s2-2 4-2c-1 1-1 2-1 3-1 1-1 1-1 2v1h-1v-1-1h-2-1z" class="T"></path><path d="M346 286v4h1v4 3l-2 1 1-5h-1l-1 1-1-1c1-2 2-3 2-4s1-2 1-3z" class="M"></path><path d="M350 274h2c-2 5-4 11-5 16h-1v-4c0-1 0-3 1-4 1-2 2-3 2-5v-1c0-1 1-1 1-2z" class="T"></path><path d="M349 265c1 0 3 0 4 1 1 0 1 1 2 2l-3 6h-2c-1-1-2-1-3-1 0 1 0 2-1 3h0c-2 0-3 1-4 2s-2 1-2 2c-2 2 0 7-3 8 0-6 1-12 3-18v3l1-1c0-2 1-3 1-5 2-3 4-1 7-2z" class="M"></path><path d="M353 266c1 0 1 1 2 2l-3 6h-2c-1-1-2-1-3-1l-1-1 1-1c1 0 2-1 4-1v-2l2-2z" class="J"></path><path d="M279 508l27-1c4 2 4 4 6 8l2 5c2 3 3 8 5 11l-6 1v-3c-1-1-3-1-4-1-2 1-1 1-2 1h-2c-2 0-3 2-6 2-1 1-2 1-4 2-1 1-1 1-2 1l-1-1-1-2h-1 0l1-1v-2h0l1-2c0-1 1-1 2-2v-3c1-1 1-1 1-2l-1-1-3 2v-1c-1 1-1 1-2 1l-1-1 1-2h-2c-1 1-1 1-2 1s-1 1-2 1l-1-1-2 2v-1l-2-1-1 2c-1 0-1-1-3-1v1h-1l-1-2c-2 0-2 1-3 2h-2v-1l-2-1h-2c-2-1-4-1-6-1-1 1-2 2-3 2l-2-2-1-1v-2l3-1h0c3 0 5-1 8-1 4-1 9-2 14-3 1 0 2 0 3-1z" class="c"></path><path d="M272 518h3c1 0 1 1 1 1 1-1 1-2 3-2h1c0-1 1-1 2-2v1c2 1 2-1 4-1 1-1 3-1 4-1 1 1 2 0 3-1 1 1 1 1 1 2h0 1c1-2 2-3 4-4 1 0 2 1 3 1h1v1l-2 2h0l-2-1v4l1 1v1c0 2-1 2-1 4h-1-1-3v-3c1-1 1-1 1-2l-1-1-3 2v-1c-1 1-1 1-2 1l-1-1 1-2h-2c-1 1-1 1-2 1s-1 1-2 1l-1-1-2 2v-1l-2-1-1 2c-1 0-1-1-3-1v1h-1l-1-2z" class="Y"></path><path d="M289 517l1-1h-1l1-1 1 2h1v-1c0-1 0-1 1-1l1 1h3l-1-1 1-1c0 1 0 1 1 1v-1h1v4l1 1v1c0 2-1 2-1 4h-1-1-3v-3c1-1 1-1 1-2l-1-1-3 2v-1c-1 1-1 1-2 1l-1-1 1-2z" class="V"></path><path d="M297 524c-1-2-2-2-2-4h5c0 2-1 2-1 4h-1-1z" class="E"></path><defs><linearGradient id="q" x1="298.081" y1="520.738" x2="308.875" y2="534.813" xlink:href="#B"><stop offset="0" stop-color="#3b3b43"></stop><stop offset="1" stop-color="#5a5c6a"></stop></linearGradient></defs><path fill="url(#q)" d="M303 512h2c2 0 3 1 4 2l3 1 2 5c2 3 3 8 5 11l-6 1v-3c-1-1-3-1-4-1-2 1-1 1-2 1h-2c-2 0-3 2-6 2-1 1-2 1-4 2-1 1-1 1-2 1l-1-1-1-2h-1 0l1-1v-2h0l1-2c0-1 1-1 2-2h3 1 1c0-2 1-2 1-4v-1l-1-1v-4l2 1h0l2-2v-1z"></path><path d="M303 512h2c2 0 3 1 4 2l3 1 2 5h-1c-1-1-2-3-3-4 0 0-2-1-3 0 0 0-2 2-3 2l-1-1-2 1-1 1-1-1v-4l2 1h0l2-2v-1z" class="H"></path><path d="M303 512h2c2 0 3 1 4 2h-3c-1 0-1 1-2 1l-1-2v-1z" class="P"></path><path d="M351 237c8-13 22-24 36-29v2l3-1 1 1c-2 1-5 2-6 3 0 1-1 1-1 2v1l-1 5-1 4c-2 1-4 5-6 5-1 0-2 1-4 2h0l-5 5h0c-1 1 0 1-1 1l-3 3v1s0 1-1 2c1 1 2 1 3 2-1 0-2 1-3 1v1l4-1v1l-2 2h-1l2 2-2 2-4 6-2 4-2 4c-1-1-1-2-2-2-1-1-3-1-4-1-3 1-5-1-7 2 0 2-1 3-1 5l-1 1v-3-6c1-2 2-6 3-8 2-7 5-13 8-19z" class="B"></path><path d="M362 248l4-1v1l-2 2h-1l-1-1c-1 1-1 2-2 3l-3-2c1-1 1-2 3-2h2z" class="P"></path><path d="M347 258l4-6c2 0 2-1 3-2h3l3 2c-1 1-1 1-2 1-1 1-2 3-4 4h0 0-1c-2 1-4 1-6 1z" class="K"></path><path d="M357 235h1s1-1 2-1v-1 2c-1 1-2 3-3 4v1l2 1-1 1h-2l-3 3c-1 2-2 3-4 4l-1-1 1-1c1-1 2-3 2-4 2-3 4-5 6-8z" class="E"></path><defs><linearGradient id="r" x1="363.098" y1="255.386" x2="350.177" y2="258.288" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#515150"></stop></linearGradient></defs><path fill="url(#r)" d="M360 252c1-1 1-2 2-3l1 1 2 2-2 2-4 6-2 4-1-1c-1-2-1-2-2-3-2 0-2 0-3-1h-3l-1 1v-2h0c2 0 4 0 6-1h1 0 0c2-1 3-3 4-4 1 0 1 0 2-1z"></path><path d="M359 260l-1-1v-3c2 0 3-1 4-2h0 1 0l-4 6z" class="B"></path><path d="M384 216l-1 5-1 4c-2 1-4 5-6 5-1 0-2 1-4 2h0l-5 5h0-1l2-3h-1c-1 1-3 3-4 5l-2 2v1c-1 1 0 1-1 2 0 1-2 1-2 2-1-1-1 0-2-1v-1c0-1 2-1 2-2l1-1-2-1v-1c1-1 2-3 3-4v-2c3-2 4-4 6-6s5-4 7-6c4-2 6-4 11-5z" class="V"></path><path d="M373 221c0 1 0 1 1 2h-1c-4 3-10 8-12 13l-1-1v-2c3-2 4-4 6-6s5-4 7-6zm-1 11h-1-4 2c0-1 1-2 2-2 3-1 6-7 10-8 1 0 1 0 2-1l-1 4c-2 1-4 5-6 5-1 0-2 1-4 2h0z" class="H"></path><path d="M351 237c8-13 22-24 36-29v2l3-1 1 1c-2 1-5 2-6 3 0 1-1 1-1 2v1c-5 1-7 3-11 5-2 2-5 4-7 6s-3 4-6 6v1c-1 0-2 1-2 1h-1c-2 3-4 5-6 8 0 1-1 3-2 4s-2 3-3 4v3h1c0 1 0 2-1 3 0 0 0 1 1 1v2l1-1h3c1 1 1 1 3 1 1 1 1 1 2 3l1 1-2 4c-1-1-1-2-2-2-1-1-3-1-4-1-3 1-5-1-7 2 0 2-1 3-1 5l-1 1v-3-6c1-2 2-6 3-8 2-7 5-13 8-19z" class="J"></path><path d="M354 260c1 1 1 1 2 3l1 1-2 4c-1-1-1-2-2-2-1-1-3-1-4-1 1-1 0-1 1-1 2 0 2-1 4-1v-3z" class="T"></path><path d="M346 251v3h1c0 1 0 2-1 3 0 0 0 1 1 1v2l-5 5c1-4 1-8 3-11v-1c0-1 0-1 1-2z" class="N"></path><path d="M390 209l1 1c-2 1-5 2-6 3 0 1-1 1-1 2v1c-5 1-7 3-11 5-2 2-5 4-7 6s-3 4-6 6v1c-1 0-2 1-2 1h-1c1-2 3-5 5-7 8-8 15-14 25-18l3-1z" class="P"></path><defs><linearGradient id="s" x1="573.987" y1="516.714" x2="572.391" y2="471.86" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#b5b4b3"></stop></linearGradient></defs><path fill="url(#s)" d="M579 473h1c2 0 2 0 3-1 2 0 3 0 5 1h1v1l-2-1-1 1h3l-1 1v1l-1 1v3c-1 1 0 4-1 5 0 1 0 1-1 2l1 1c1 0 1-1 2-1l1 1v2h2c1 0 2 0 3 1l1-1c0-2 0-2 1-3l-2 10c-1 0-3 1-4 0-1 0-2-3-4-1-1 2-2 5-2 6-1 5-2 9-2 14-1 2-1 5-1 7-1-2 0-4-1-5-5-1-9-4-13-6l-3-1h-2c-2-1-3-2-5-2h-1c0-2-2-4-4-6 0-1-1-2-1-4v-1c1 0 2-1 3-1 1-1 2-3 3-3l2-2h1l5-5c2-1 4-4 6-6l8-8z"></path><path d="M584 492h1 1 0l-1 2c0 1 0 0-1 1l-1-1 1-2z" class="C"></path><path d="M557 509l2-2c2 1 3 3 5 4h-2c-2-1-3-2-5-2zm26-23c1 0 0 0 1 1 0 1-1 2-2 2l-3 2-1-1h-2c1 0 1-1 2-1 2-1 3-2 5-3z" class="b"></path><defs><linearGradient id="t" x1="426.526" y1="229.253" x2="427.375" y2="258.552" xlink:href="#B"><stop offset="0" stop-color="#151414"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#t)" d="M443 226l18 7c8 5 16 12 21 21h0c2 3 3 7 4 10-6-7-12-12-19-17-19-11-43-12-64-7-1 0-1 0-2 1h-1c-1 0-1 0-1 1h-1c-1 0-1 1-2 1v-1l-1 1c-4 1-7 2-10 3-2 1-3 2-4 2l-4 4h-2c0 1-1 1-2 0l1-1h0c2-3 5-7 9-8 2-2 4-3 5-5v-2c2-1 4-2 7-3 3-2 7-3 11-4 11-2 21-2 32-1h1c1-1 2-1 4-2h0z"></path><path d="M395 233c3-2 7-3 11-4 11-2 21-2 32-1 4 1 7 2 11 3-5 1-10-1-15-2-11-1-22 0-33 3-4 2-9 4-13 6v-2c2-1 4-2 7-3z" class="L"></path><defs><linearGradient id="u" x1="457.908" y1="231.309" x2="453.689" y2="236.57" xlink:href="#B"><stop offset="0" stop-color="#a7a4a5"></stop><stop offset="1" stop-color="#c5c4c1"></stop></linearGradient></defs><path fill="url(#u)" d="M443 226l18 7c8 5 16 12 21 21h0c-9-11-20-17-33-23-4-1-7-2-11-3h1c1-1 2-1 4-2h0z"></path><path d="M696 531h1c2 0 6 1 9 0h1c4 4 10 4 15 5 1 1 3 1 4 1 6 2 12 3 18 5 2 1 3 1 5 2v1 1c0 2 0 2-2 4-2 1-4 2-5 3h1c-1 2-5 3-8 5l-1 1c-3 2-7 4-11 4v-1c-4 0-7-2-11-3l-4-2 1-1s1 0 2 1h0v-1c-1-1-1-1-1-2-1-1 0-1-1-2l-1 1c-1 0-2-1-3-2h0-1l-1-1v-2l-2-2-1-1h-3l-1 1v-2c-2-1-3-2-5-2l1-3 1-2 3-6z" class="c"></path><path d="M693 537l3 2c1 0 2 0 3 1 1 0 2 0 3 1h1c1 0 2 2 2 2h4c2 1 2 1 4 1 3 2 5 5 8 5v1 2l2-1 1 2-1 1v1c3 1 4 1 6 3 2-1 2 0 4 0h2l-1 1c-3 2-7 4-11 4v-1c-4 0-7-2-11-3l-4-2 1-1s1 0 2 1h0v-1c-1-1-1-1-1-2-1-1 0-1-1-2l-1 1c-1 0-2-1-3-2h0-1l-1-1v-2l-2-2-1-1h-3l-1 1v-2c-2-1-3-2-5-2l1-3 1-2z" class="i"></path><path d="M700 543c1 0 3 1 5 1v1c1 0 2 1 3 1l-3 1h0c-1-1-3-1-4-1l-1-1v-2z" class="g"></path><path d="M692 539c1 1 2 1 4 1 1 0 3 1 4 2v1 2h-3l-1 1v-2c-2-1-3-2-5-2l1-3z" class="P"></path><path d="M708 546l1-1 1 3h2 0v-2h1l1 1c0 1 1 1 2 2 0 1 0 1 1 1 1 1 1 1 2 1l1 1v2h0l-1 1h-2l-1-1 1-1v-1h-2 0l-1 1c0 1 1 2 2 3h-1c-1 0-1 0-2-1v-1l-2 2c-1-1-1-1-1-2-1-1 0-1-1-2l-1 1c-1 0-2-1-3-2h0-1l-1-1v-2l-2-2c1 0 3 0 4 1h0l3-1z" class="H"></path><path d="M710 554l1-1c0-1 0-2 1-2 1-1 2-2 3-2v3l-1 1c0 1 1 2 2 3h-1c-1 0-1 0-2-1v-1l-2 2c-1-1-1-1-1-2z" class="N"></path><path d="M715 552h0 2v1l-1 1 1 1h2l1-1h0l2-1v1l-1 1h2c3 1 4 1 6 3 2-1 2 0 4 0h2l-1 1c-3 2-7 4-11 4v-1c-4 0-7-2-11-3l-4-2 1-1s1 0 2 1h0v-1l2-2v1c1 1 1 1 2 1h1c-1-1-2-2-2-3l1-1z" class="F"></path><path d="M715 552h0 2v1l-1 1 1 1h2l1-1h0l2-1v1l-1 1h2c3 1 4 1 6 3 2-1 2 0 4 0h2l-1 1h-2c-2 1-7 1-9 0v-1c-1 0-3-1-4-1h-1l-1-1h-1c-1-1-2-2-2-3l1-1z" class="E"></path><path d="M607 225c5 0 10 1 16 3 2 0 4 1 6 2l7 3c1 1 3 2 3 2 4 2 7 4 11 6 1 1 5 5 6 5l2 2 4 4c4 4 7 9 10 13 1 3 4 7 4 10h0c-1 0-3-4-4-6l-2-2 5 11c-1 2 0 3-2 4-4-7-9-13-14-19-7-7-15-11-24-15-3-5-7-3-12-5v-2c-1-2-2-1-4-2-10-2-19-2-29-1v-1h-2-3 1v-1h-3c-1 1-3 1-5 1l-3 1h-6c3-2 6-4 9-5l2-1h2l1-1h1c1-1 1-1 3-1l1-1c8-2 17-2 25-1h1c-1-1-2-2-4-2-1 0-2-1-3-1z" class="i"></path><path d="M604 233c8 1 15 2 22 5 1 1 0 0 2 1l-1 1h-1c-1 0-2 0-3 1-1-2-2-1-4-2l-1-1v-1c-3-1-6-1-10-2h2c-2 0-4-1-6-2z" class="B"></path><path d="M628 239h0c2 0 3 1 5 2 1 1 3 1 5 2 7 3 14 9 20 14h-1l-2-2-1 1-3-2h-1c-4-3-7-7-12-9-4-2-9-3-12-5h1l1-1z" class="E"></path><path d="M578 237c3-1 6-2 10-3 5 0 11-1 16-1 2 1 4 2 6 2h-2c4 1 7 1 10 2v1l1 1c-10-2-19-2-29-1v-1h-2-3 1v-1h-3c-1 1-3 1-5 1z" class="K"></path><path d="M607 225c5 0 10 1 16 3 2 0 4 1 6 2l7 3c1 1 3 2 3 2 4 2 7 4 11 6 1 1 5 5 6 5l2 2 4 4c4 4 7 9 10 13 1 3 4 7 4 10h0c-1 0-3-4-4-6l-2-2c-6-8-13-17-21-23-4-3-7-5-11-7-3-2-9-3-11-6l-14-3h1c-1-1-2-2-4-2-1 0-2-1-3-1z" class="L"></path><path d="M607 225c5 0 10 1 16 3 2 0 4 1 6 2l7 3c1 1 3 2 3 2 4 2 7 4 11 6 1 1 5 5 6 5l2 2h-2c-9-6-18-16-29-17l-14-3h1c-1-1-2-2-4-2-1 0-2-1-3-1z" class="R"></path><path d="M293 534c1 0 1 0 2-1 2-1 3-1 4-2 3 0 4-2 6-2h2c1 0 0 0 2-1 1 0 3 0 4 1v3l6-1 4 10 4 9 1 2-25 11-1 1h0c-1-1-3-1-4-1-2-1-4-1-6-3h3l-3-1-2-1-7-4-1-1h1c-2-3-7-4-10-4 1-1 2 0 4-1l-2-2h-1c-3-1-4 1-7-1l1-1c1 0 2-1 3-1h1l2-1h1c0-1 1-1 2-1 3-1 6-1 8-2h0l1-1v-1h0c2-1 5-2 7-3z" class="c"></path><path d="M285 539l9-2 1 1 1-1h1c-2 1-5 2-7 3-2 0-4 0-5 2-2-1-2-1-3 0-4 1-10 0-13 3h3 1 2v1h-1c-3-1-4 1-7-1l1-1c1 0 2-1 3-1h1l2-1h1c0-1 1-1 2-1 3-1 6-1 8-2z" class="i"></path><defs><linearGradient id="v" x1="291.258" y1="528.472" x2="306.922" y2="538.462" xlink:href="#B"><stop offset="0" stop-color="#464858"></stop><stop offset="1" stop-color="#61636c"></stop></linearGradient></defs><path fill="url(#v)" d="M293 534c1 0 1 0 2-1 2-1 3-1 4-2 3 0 4-2 6-2h2c1 0 0 0 2-1 1 0 3 0 4 1v3h-1c-4 3-10 3-15 5h-1l-1 1-1-1-9 2h0l1-1v-1h0c2-1 5-2 7-3z"></path><path d="M294 537c5-3 12-5 18-5-4 3-10 3-15 5h-1l-1 1-1-1z" class="P"></path><defs><linearGradient id="w" x1="312.267" y1="545.469" x2="316.118" y2="555.19" xlink:href="#B"><stop offset="0" stop-color="#1f2027"></stop><stop offset="1" stop-color="#3f3f48"></stop></linearGradient></defs><path fill="url(#w)" d="M323 541l4 9 1 2-25 11-1 1h0c-1-1-3-1-4-1-2-1-4-1-6-3h3l-3-1c2-1 2-1 2-3h4c1 0 1-2 2-2 0 0 1 1 2 0v-2c1 0 1 0 2-1v-1c2-1 3-3 5-4 1 0 2-1 3-2h0l2-2c3-2 5-2 9-1z"></path><path d="M327 550l1 2-25 11-1 1h0c-1-1-3-1-4-1-2-1-4-1-6-3h3s2 0 2 1h7c3 0 12-4 14-7v-1h-1l1-1c1 1 1 1 3 1 0 0 1 0 2-1 0 0 1-1 2-1h1l1-1z" class="B"></path><path d="M312 544c1 1 1 0 2 2-1 0-1 0-2 1l-1 1c0 1 0 2-1 3-1 0-1 1-1 2l-2 2c-1 1 0 1-1 3 0 0-1 2-2 2h-4l-3 1c0-1-2-1-2-1l-3-1c2-1 2-1 2-3h4c1 0 1-2 2-2 0 0 1 1 2 0v-2c1 0 1 0 2-1v-1c2-1 3-3 5-4 1 0 2-1 3-2h0z" class="P"></path><path d="M311 548c0 1 0 2-1 3-1 0-1 1-1 2l-2 2c-1 1 0 1-1 3 0 0-1 2-2 2h-4 2c2 0 2-1 3-2v-1h1v-1l-2-1 1-2c1-1 1-2 2-2l4-3z" class="O"></path><path d="M691 236h2v-2c-1-2-1-5-1-7 2 3 2 6 3 8s1 4 2 6 1 5 1 7c1 3 2 4 2 7v1c0 1 0 4 1 6v-7c-1-1-1-2-1-3v-2c-1-1 0-3 0-4 1 3 1 5 1 8 0 1 1 1 1 2v2c0 1 0 1 1 2v3c0 1 0 1 1 2v1c0 1 0 2 1 4h-1l1 1v5h5c2 1 3 1 4 3 0 0 1 1 1 2 1 0 0 4 0 4v1l-1 1h0v2l-1 1v2c-1 1-1 1-1 2l-1 1h0l-1 1 1 1h2 2l3-1h4c1 1 1 0 2 0s2 1 4 1l-27 1h-6-3l-2-1c0-1 0-3-1-4l-1-1v-4c-1-2-1-5-1-6v-1-3l-1-1 1-1v-2-1-2l1-1 1 3v-1c0-2-1-5-2-8-1-4-2-7-3-11-1-2-3-5-4-8 0-1 0 0-1-1-1-4-3-7-6-10v-2c3 2 6 10 10 10v-3l-1-1c2 0 3 0 5 1h2c1 1 1 1 2 1v-3-1z" class="R"></path><path d="M706 278c2-1 2-1 4-1 2 1 3 2 4 4 1 1 0 3 0 4-2-2-6-5-8-7z" class="O"></path><path d="M694 275v-1-1l5 1 1 21h0c0-1 0-3-1-4 0-3 0-9-1-11-2 0-2 1-3 1l-1-6z" class="T"></path><defs><linearGradient id="x" x1="699.361" y1="281.128" x2="695.443" y2="295.852" xlink:href="#B"><stop offset="0" stop-color="#6a696a"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#x)" d="M695 281c1 0 1-1 3-1 1 2 1 8 1 11 1 1 1 3 1 4h0c1 1 1 2 1 3h-6v-17z"></path><path d="M696 258v1c1 5 3 10 3 15l-5-1v1 1l-3-12c1-2 3-4 5-5z" class="G"></path><path d="M683 239c3 1 5 1 8 3 0 2 1 4 1 6 1 3 1 6 2 8 0 1 1 1 2 2-2 1-4 3-5 5 0-3-1-6-2-8-2-5-4-11-6-16z" class="B"></path><path d="M691 236h2v-2c-1-2-1-5-1-7 2 3 2 6 3 8s1 4 2 6 1 5 1 7c1 3 2 4 2 7v1c0 1 0 4 1 6 1 4 2 9 2 13v2 3l1 1c0 2 0 4-1 5v-1c-1-3-1-7-2-10v-3-1c0-3-1-6-2-9v-2l-1-2v-2c-1-1-1-2-1-3l-2-8-1-3-1-1c0-1-1-2-1-3l-1-1v-1z" class="b"></path><path d="M703 275c1 2 2 2 2 3h1 0c2 2 6 5 8 7 0 2-1 3-2 5h0v1c-1 2-1 3-2 4h-1c-1 1-2 1-2 1-2 1-3 1-4 1-1-2-1-9 0-10v-1c1-1 1-3 1-5l-1-1v-3-2z" class="X"></path><path d="M673 232c3 2 6 10 10 10v-3h0c2 5 4 11 6 16 1 2 2 5 2 8l3 12 1 6v17h-3l-2-1c0-1 0-3-1-4l-1-1v-4c-1-2-1-5-1-6v-1-3l-1-1 1-1v-2-1-2l1-1 1 3v-1c0-2-1-5-2-8-1-4-2-7-3-11-1-2-3-5-4-8 0-1 0 0-1-1-1-4-3-7-6-10v-2z" class="D"></path><defs><linearGradient id="y" x1="684.247" y1="282.309" x2="694.513" y2="292.441" xlink:href="#B"><stop offset="0" stop-color="#585957"></stop><stop offset="1" stop-color="#747173"></stop></linearGradient></defs><path fill="url(#y)" d="M686 277l1-1v-2-1-2l1-1 1 3v-1c2 5 2 10 2 15 1 4 1 7 1 11l-2-1c0-1 0-3-1-4l-1-1v-4c-1-2-1-5-1-6v-1-3l-1-1z"></path><defs><linearGradient id="z" x1="447.086" y1="208.993" x2="383.545" y2="225.672" xlink:href="#B"><stop offset="0" stop-color="#030201"></stop><stop offset="1" stop-color="#414142"></stop></linearGradient></defs><path fill="url(#z)" d="M390 209c6-1 11-1 16-1 14 0 26 3 38 10l9 4h-1c-1 1-1 0-2 0 0 0-1 1-2 1l3 2c3 1 5 3 7 5 2 1 2 2 3 3l-18-7h0c-2 1-3 1-4 2h-1c-11-1-21-1-32 1-4 1-8 2-11 4l-2-1c-1 0-2 1-3 1h-1l-1-1v-1c-1 0-2 0-2 1-2 2-4 4-6 5l-2 1v-2c0-1 2-1 2-3l2-2c0-2 0-3 1-4v-1l-1-1 1-4 1-5v-1c0-1 1-1 1-2 1-1 4-2 6-3l-1-1z"></path><path d="M385 213h2c1 2 0 3 0 4 0 0-1 1-1 2v-1c-1-2-1-2-2-3 0-1 1-1 1-2z" class="K"></path><path d="M384 215c1 1 1 1 2 3v1c-2 2-3 4-3 7l-1-1 1-4 1-5v-1z" class="T"></path><path d="M412 214l-2-1 1-1c8 0 15 2 23 5-4-1-9-2-13-2h-4c-2-1-3-1-5-1z" class="H"></path><path d="M402 217s0-1 1-2h0l3-1h1l1-1 2 2c1-1 1-1 2-1 2 0 3 0 5 1h4c4 0 9 1 13 2 3 0 5 2 8 3-2 0-3 0-4-1-2 0-3 0-5-1h-1l-11-2c-3-1-6 1-9-1l-2 1c-1 0-1 0-2-1v1c-2 1-3 1-4 0h0l-2 1z" class="B"></path><path d="M402 217l2-1h0c1 1 2 1 4 0v-1c1 1 1 1 2 1l2-1c3 2 6 0 9 1l11 2h1c2 1 3 1 5 1 1 1 2 1 4 1l6 3 3 2h-1c-3-1-5-3-8-4-8-2-15-3-23-4-3 0-7 1-10 1-2 1-4 0-6 0-1 0-1 1-2 1-3 1-5 1-7 2s-3 1-4 1c-2 1-3 2-4 3h0l-1 1c1-1 1-2 2-3h0c2-2 3-3 5-4h2c1 0 2-1 3-1v-1c2 0 2 0 4 1l1-1z" class="N"></path><path d="M385 226l1-1h0c1-1 2-2 4-3 1 0 2 0 4-1s4-1 7-2c1 0 1-1 2-1 2 0 4 1 6 0 3 0 7-1 10-1 8 1 15 2 23 4 3 1 5 3 8 4h1c3 1 5 3 7 5-4-1-9-4-14-6-3-2-6-2-9-3-10-2-21-2-31 0-1 0-2 1-3 1h-1l-1 1h-3c-1 0-3 1-4 2l-8 3c0-1 1-1 1-2z" class="F"></path><path d="M384 228l8-3c1-1 3-2 4-2h3l1-1h1c1 0 2-1 3-1 10-2 21-2 31 0 3 1 6 1 9 3-7-2-14-3-22-3-2-1-4 0-6 0-3-1-10 1-13 2-1 1-2 2-3 2h0c-2 0-3 1-4 2l-5 3-1 1-1-1-1 1c-1 0-2 0-2 1-2 2-4 4-6 5l-2 1v-2c0-1 2-1 2-3l2-2 2-3z" class="W"></path><path d="M396 227c1-1 2-2 4-2h0c1 0 2-1 3-2 3-1 10-3 13-2 2 0 4-1 6 0 8 0 15 1 22 3 5 2 10 5 14 6 2 1 2 2 3 3l-18-7h0c-2 1-3 1-4 2h-1c-11-1-21-1-32 1-4 1-8 2-11 4l-2-1c-1 0-2 1-3 1h-1l-1-1v-1l1-1 1 1 1-1 5-3z" class="G"></path><path d="M396 227h1c1-1 3 0 5-1 1-1 2-1 4-1-1 1-2 1-3 2h-2-1c-3 1-5 3-9 3l5-3z" class="M"></path><path d="M393 232l9-3c2-1 5-2 7-2 5-1 10-2 16-2 5-1 12 0 18 1-2 1-3 1-4 2h-1c-11-1-21-1-32 1-4 1-8 2-11 4l-2-1z" class="R"></path><path d="M396 227c1-1 2-2 4-2h0c1 0 2-1 3-2 3-1 10-3 13-2 2 0 4-1 6 0 8 0 15 1 22 3 5 2 10 5 14 6 2 1 2 2 3 3l-18-7h0c-1-2-4-2-6-3h-5c-3-1-7 0-10-1h0c-1 1-5 0-6 0l-1 1h-3l-2 1c-2 0-3 0-4 1-2 0-3 0-4 1-2 1-4 0-5 1h-1z" class="f"></path><path d="M702 516h1c1 0 2-1 3-2h4l1 1 1-2c1 0 1 0 2 1l1-1c1 0 1 1 2 2v1l1-1h0c0-1 1-1 2-2 1 0 0 0 1 1h1l1 1h-1l1 1 1-1v1c1 1 2 0 3 0l1 1 1-1 1 1c1 2 2 2 3 4 2 1 2 1 5 1 2-1 2 0 5-1 0 1 0 2 2 2h0 2l2 1v1l1 2c0-1 4-3 5-4l1 1 1-1c1 0 2 0 3 1h1l1 1 2-1c2 0 2 0 4-1l1 1c1 0 1 1 2 1-1 2-1 3 0 4h0v1 1h-2v2c1 1 1 1 2 0 1 1 2 2 2 3h0l1 2 3-1 5-3v1c-2 2-5 3-7 4-3 1-6 1-10 2h-1-14l-15-3c-3 0-6-1-9-1-1 0-3 0-4-1-5-1-11-1-15-5h-1c-3 1-7 0-9 0h-1l6-15z" class="F"></path><path d="M750 529c1-1 1-2 1-3h1l1 1 2-2v1c1 0 2 1 4 1l1-1 2 2h-1v2l1 1v1c-1 0-1-1-2-1v-2-1c-1 0-2 1-2 1l-1 1c-1 1-1 1-2 1v-3l-1-1h-1c0 2-1 3-2 4h0v-1l-1-1z" class="N"></path><path d="M764 531h1 2 0v1h0v-2h1c0 1 0 3 1 3 1 1 1 1 2 0 1 1 2 2 2 3h0 0c-1 1-2 1-2 1l1 1h-2-1 0-1v-3h-2-2 0c-1 0-1 0-2-1 1 0 1-1 2-1h0c1-1 0-1 0-2h0z" class="B"></path><path d="M782 534v1c-2 2-5 3-7 4-3 1-6 1-10 2h-1v-1c-1 0-1 0-2-1h0l-2-2h-2v-1h2 2c1 0 1 1 2 1s2-1 3 0c0 1 0 1 1 2 2 0 3 0 5-1h1l3-1 5-3z" class="J"></path><path d="M738 522c2-1 2 0 5-1 0 1 0 2 2 2h0 2l2 1v1 2l-1 1s-1 0-1 1c-1 0-2-1-3-2v-1l-1-1c0 2-1 3-1 5l-1-1v-1-1c-1-1-1-1-1-2l-2-3h0z" class="K"></path><path d="M738 535l1-1h-1l1-1-1-1 2-1 1 1 2-1h2l2 3c1 0 3 1 4 1 0 0 0-1 1 0 1 0 1 0 2-1l1 1 1-1c-1 1-1 2-2 3h-4-1c-2 0-3 0-5-1v1h-1l-1-1h-1c-2 0-2 0-3-1z" class="W"></path><path d="M744 536c1-1 2-1 3-1 1 1 2 1 2 2-2 0-3 0-5-1z" class="M"></path><defs><linearGradient id="AA" x1="735.283" y1="527.167" x2="730.266" y2="531.744" xlink:href="#B"><stop offset="0" stop-color="#50525f"></stop><stop offset="1" stop-color="#676976"></stop></linearGradient></defs><path fill="url(#AA)" d="M729 527c1-1 1-1 2-1 0-1 0-1 1-1l1 1-1 1h1 1l1 1 1-1h0l2 1c-1 1-1 2-1 3v1 1l1 2c1 1 1 1 3 1l-3 2-2-1-3-1-2-2-1-1v-2c0-1 0-2-1-2v-2z"></path><path d="M733 534h1c1-1 2-1 3-1l1 2c1 1 1 1 3 1l-3 2-2-1-3-1-2-2c1-1 1-1 2-1v1z" class="M"></path><path d="M731 534c1-1 1-1 2-1v1l3 3-3-1-2-2z" class="G"></path><path d="M724 516c1 1 2 0 3 0l1 1 1-1 1 1c1 2 2 2 3 4 2 1 2 1 5 1h0l2 3c0 1 0 1 1 2v1 1l-1-1c-1 0 0 0-1-1h-1v1l-2-1h0l-1 1-1-1h-1-1l1-1 1-1c-1-1-1-2-2-3h0-1c0 1 0 1-1 3l-1-1v-1c0-1-1-1-1-2h-1l-3-3v-2z" class="B"></path><path d="M724 516c1 1 2 0 3 0l1 1 1-1 1 1c1 2 2 2 3 4-1 0-2 0-3-1v-2h-1v1 1l-2 1-3-3v-2z" class="N"></path><path d="M756 534l-1-1h1c1 1 1 2 2 3v1h2l2 2h0c1 1 1 1 2 1v1h-14c1-1 1 0 1-1-2 0-4-1-6-1-3-1-5-1-7-1l3-2h1l1 1h1v-1c2 1 3 1 5 1h1 4c1-1 1-2 2-3z" class="L"></path><path d="M756 534l-1-1h1c1 1 1 2 2 3v1h2l2 2h0c1 1 1 1 2 1h-6l-1-2h-2c-1 0-3-1-5-1h4c1-1 1-2 2-3z" class="f"></path><path d="M755 523l1 1 1-1c1 0 2 0 3 1h1l1 1 2-1c2 0 2 0 4-1l1 1c1 0 1 1 2 1-1 2-1 3 0 4h0v1 1h-2v2c-1 0-1-2-1-3h-1v2h0v-1h0-2-1l-1-1-1 1-1-1v-2h1l-2-2-1 1c-2 0-3-1-4-1v-1l-2 2-1-1h-1c0 1 0 2-1 3l-1-2v-2l1 2c0-1 4-3 5-4z" class="E"></path><g class="H"><path d="M763 526l1 1v1l1 1h-3c0-1 0-2 1-3z"></path><path d="M768 526h0l3-1c-1 2-1 3 0 4-1 0-1 1-1 1-1 0-1-1-2-1h-3 0v-1l3-2z"></path></g><path d="M762 525l2-1c2 0 2 0 4-1l1 1c1 0 1 1 2 1l-3 1h0l-4 1-1-1v-1h-1z" class="O"></path><path d="M702 516h1c1 0 2-1 3-2h4l1 1 1-2c1 0 1 0 2 1l1-1c1 0 1 1 2 2v1l1-1h0c0-1 1-1 2-2 1 0 0 0 1 1h1l1 1h-1l1 1 1-1v1 2l3 3h1c0 1 1 1 1 2v1l1 1c1-2 1-2 1-3h1 0c1 1 1 2 2 3l-1 1-1-1c-1 0-1 0-1 1-1 0-1 0-2 1v2c1 0 1 1 1 2v2l1 1 2 2 3 1 2 1c2 0 4 0 7 1 2 0 4 1 6 1 0 1 0 0-1 1l-15-3c-3 0-6-1-9-1-1 0-3 0-4-1-5-1-11-1-15-5h-1c-3 1-7 0-9 0h-1l6-15z" class="F"></path><path d="M716 517h4c2 2 5 4 7 6-2 0-2-1-3-2-2-1-3-1-5-1h0l-3-3z" class="W"></path><path d="M697 530l3-3c0-1 0 0 1-1v-1c1-1 1-3 2-4v-1l2-2c1-2 3-1 5-1-1 1-1 2-3 2 0 1 0 1 1 2l-1 1c-2 0-3 1-4 2-1 2-2 4-3 5l-1 1h-2z" class="J"></path><path d="M708 521l2 2-1 1 1 1-2 1-1 1-1 1c-1 0-1 0-2 1-2 0-3 1-5 1l1-1c1-1 2-3 3-5 1-1 2-2 4-2l1-1z" class="d"></path><path d="M710 517v-1h0 5v1h1l3 3h0c2 0 3 0 5 1 1 1 1 2 3 2l2 4v2c1 0 1 1 1 2v2c-2-2-2-2-4-3h-3c-1-1-2-1-2-2v-1h-8c-1-1-2 0-3 0 0 0-2 0-2-1l2-1-1-1 1-1-2-2c-1-1-1-1-1-2 2 0 2-1 3-2z" class="f"></path><path d="M710 525c1-1 2-2 1-3v-1l1-1c0 1 1 2 2 3l-1 1c1 1 0 0 1 0s2 0 2 1h1l-1-2 2-1c1 1 2 1 3 2l1-1c2 1 2 2 4 3 0 1 0 1-1 2l1 1h0 1 1c0 1 1 1 2 2v2c-2-2-2-2-4-3h-3c-1-1-2-1-2-2v-1h-8c-1-1-2 0-3 0 0 0-2 0-2-1l2-1z" class="G"></path><path d="M708 526c0 1 2 1 2 1 1 0 2-1 3 0h8v1c0 1 1 1 2 2h3c2 1 2 1 4 3l1 1 2 2 3 1 2 1c2 0 4 0 7 1 2 0 4 1 6 1 0 1 0 0-1 1l-15-3c-3 0-6-1-9-1-1 0-3 0-4-1-5-1-11-1-15-5h-1c-3 1-7 0-9 0v-1h2c2 0 3-1 5-1 1-1 1-1 2-1l1-1 1-1z" class="C"></path><path d="M707 531c9 1 19 4 28 7-3 0-6-1-9-1-1 0-3 0-4-1-5-1-11-1-15-5z" class="P"></path><path d="M708 526c0 1 2 1 2 1 1 0 2-1 3 0h8v1c0 1 1 1 2 2h3c2 1 2 1 4 3l1 1 2 2-4-1c-3-1-5-3-7-4-3-1-6 0-9-1l-1-1c-3-1-7 1-9 2h2 1c-3 1-7 0-9 0v-1h2c2 0 3-1 5-1 1-1 1-1 2-1l1-1 1-1z" class="Z"></path><path d="M726 530c2 1 2 1 4 3l1 1 2 2-4-1c-2-2-3-3-3-5z" class="L"></path><defs><linearGradient id="AB" x1="297.409" y1="561.697" x2="319.274" y2="593.715" xlink:href="#B"><stop offset="0" stop-color="#17171b"></stop><stop offset="1" stop-color="#555763"></stop></linearGradient></defs><path fill="url(#AB)" d="M328 552l2 5h0l1 4 2 5 7 16 1 1v1h0c-1 1-1 0-1 1-1 2-3 2-4 3-5 1-10 2-15 4h-3c-1 1 0 2 0 3-2-1-5-1-7-2-5-2-12-3-17-4l1-1-3-1h1l-1-1c-2 0-4-1-5-2h-1 0-1-1c-2 0-5-2-6-3 0-1 1-2 1-3v-1h-4c3 0 6-2 8-3h0l-1-1 3-1c3-1 4-1 6-3 4-1 8-3 11-5h0l1-1 25-11z"></path><path d="M331 561l2 5-1-1v-1l-1 1c-2-1-2-1-3-2h-4l1 1-1 1c-1-1-2-1-3 0h0-1-2l-5 1c-1 0-1 0-2-1 2-1 3-1 4-1 2-1 4-1 6-1 4-1 7-2 10-2z" class="V"></path><path d="M295 588h0c1 0 1 1 2 1h1 3l1 1c4 1 8 0 13-1 7-1 14-2 21-5 1 0 3-1 4-2l1 1v1h0c-1 1-1 0-1 1-1 2-3 2-4 3-5 1-10 2-15 4h-3c-1 1 0 2 0 3-2-1-5-1-7-2-5-2-12-3-17-4l1-1z" class="P"></path><path d="M328 552l2 5h0l1 4c-3 0-6 1-10 2-2 0-4 0-6 1-1 0-2 0-4 1-1-1-2-1-3 0-2 0-3 1-4 1l-2-2 1-1 25-11z" class="i"></path><path d="M691 542c2 0 3 1 5 2v2l1-1h3l1 1 2 2v2l1 1h1 0c1 1 2 2 3 2l1-1c1 1 0 1 1 2 0 1 0 1 1 2v1h0c-1-1-2-1-2-1l-1 1 4 2c4 1 7 3 11 3v1 1h1l12 6c1 0 4 1 5 2h1c2 2 3 2 5 3h0c0 1 1 1 2 1l-1 1c1 1 2 1 2 3h-1c-2 1-2 2-4 3h-2c-1 1-3 1-4 1-1-1 0-1-1-2h0l-1 1c-1 0-1-1-2-1h0l-1 4h-2c-2 1-5 1-7 2h-4-3c0-1 0-2-2-3 0 0-1-1-2-1s-1-1-2-2h0-2l-1-1v3c0 1-1 1-1 2h0-1c-2 0-2-1-3-2l-1 3h-4v1c-5-2-10-3-15-6h0c-3-1-5-2-7-4h-1c1-2 1-5 3-7 0-2 1-4 2-6l1-1 6-15 3-7z" class="E"></path><path d="M730 583v-1c1-1 0-2 1-4l2 2h1c0-1 1-1 1-2h1 2c1 1 1 1 2 1l2-2h1 0l-3 3s-3-1-4-1l-1 1 1 1-1 1h0l-1 4h-2-1c0-1 0-1-1-2v-1z" class="V"></path><path d="M712 574h0c1-1 1-2 1-3v3l1 1 2-2v1c0 1 1 2 1 3 1 1 2 3 4 3h2l-1 1v1l1 1 1 1c0 1 1 1 2 2v-1l1-1c0-1 0-1-1-2 0-1 0-1-1-2 0-1 0-1-1-1v-1c1 1 2 2 3 2s0-2 2 0v3h1 0v1c1 1 1 1 1 2h1c-2 1-5 1-7 2 0-3-4-4-5-7-2-1-3-2-4-4h-4v-3z" class="N"></path><path d="M710 573l2 1v3h4c1 2 2 3 4 4 1 3 5 4 5 7h-4-3c0-1 0-2-2-3 0 0-1-1-2-1s-1-1-2-2h0-2l-1-1c0-2 1-4 2-6-1 0-1-1-1-2z" class="B"></path><path d="M712 582c2 1 4 1 6 1l1 1v1c1 0 2 0 2 2v1h-3c0-1 0-2-2-3 0 0-1-1-2-1s-1-1-2-2z" class="F"></path><path d="M685 572c-1-1-1-2-2-3l1-1h1c1 1 2 1 3 1l1-1h1l2-1c0 2 1 2 2 2 2 0 3 0 5-1 0-1 0-1 1-1h2 0c2 1 2 1 4 1 1 0 1 1 2 2v1l-1 1c-1 0-1 0-1-1l-2 2-1 1v-3c-1-1-2-1-3-2v1l-2 2 1 1-1 1-1 1h-1l1-1h-1v-2h-1-1l1 2h-1c-1 1-1 2-1 3h-1c0-1-1-2-1-3-1-1-2 0-3-1v-1h-2-1z" class="N"></path><defs><linearGradient id="AC" x1="703.507" y1="570.058" x2="699.369" y2="585.729" xlink:href="#B"><stop offset="0" stop-color="#464853"></stop><stop offset="1" stop-color="#696b78"></stop></linearGradient></defs><path fill="url(#AC)" d="M693 577c0-1 0-2 1-3h1l-1-2h1 1v2h1l-1 1h1l1-1 1-1-1-1 2-2v-1c1 1 2 1 3 2v3l1-1 2-2c0 1 0 1 1 1l1-1v1l2 1h0c0 1 0 2 1 2-1 2-2 4-2 6v3c0 1-1 1-1 2h0-1c-2 0-2-1-3-2l-1 3h-4v1c-5-2-10-3-15-6h0c-3-1-5-2-7-4h-1c1-2 1-5 3-7h2c1-1 2 0 4 1h1 2v1c1 1 2 0 3 1 0 1 1 2 1 3h1z"></path><path d="M704 584c1 0 2 0 2-1l1 1-2 1 2 1c-2 0-2-1-3-2z" class="M"></path><path d="M686 578h2l-1-1v-1l1 1 1-1v-1l1-1 1 1v1l1 1h1c1 1 2 1 3 2 1 0 1 0 1 1h0l1-1 1-1 1 1-1 1v2c-1 1-3 0-4 0h-1-3c0-1-3-1-4-2l-1-2z" class="W"></path><path d="M679 571h2c1-1 2 0 4 1h1 2v1c1 1 2 0 3 1 0 1 1 2 1 3l-1-1v-1l-1-1-1 1v1l-1 1-1-1v1l1 1h-2l1 2c1 1 4 1 4 2h-1 0c-2-1-5 0-6 0h0c-3-1-5-2-7-4h-1c1-2 1-5 3-7z" class="F"></path><path d="M677 578l1-1 2 2c1 1 3 0 4 0 2 0 1-1 2-1l1 2c1 1 4 1 4 2h-1 0c-2-1-5 0-6 0h0c-3-1-5-2-7-4z" class="M"></path><path d="M691 542c2 0 3 1 5 2v2l1-1h3l1 1 2 2v2l1 1h1 0c1 1 2 2 3 2l1-1c1 1 0 1 1 2 0 1 0 1 1 2v1h0c-1-1-2-1-2-1l-1 1 4 2c4 1 7 3 11 3v1 1h1l-1 2c-3 0-6 0-9-1-1 0-3 0-4-1h-1-8l-1-2c-1 1-2 1-3 2 1 1 1 1 2 1h-2l-1-1h-2c-1 0-1 1-2 0-1 1-1 2-3 1h-1c-1 1-5 1-5 2-1 1-1 2-1 2h-1v-1-3l1-1 6-15 3-7z" class="g"></path><defs><linearGradient id="AD" x1="691.946" y1="542.464" x2="692.478" y2="549.082" xlink:href="#B"><stop offset="0" stop-color="#222125"></stop><stop offset="1" stop-color="#3c3c44"></stop></linearGradient></defs><path fill="url(#AD)" d="M691 542c2 0 3 1 5 2v2c0 1 0 2 1 3h-1c0 1 0 2 1 3h0l-4-1-1-1c-2 0-3 0-4-1l3-7z"></path><path d="M700 545l1 1 2 2v2l1 1h1 0c1 1 2 2 3 2l1-1c1 1 0 1 1 2 0 1 0 1 1 2v1h0c-1-1-2-1-2-1l-1 1c-1-1-3-1-4-2-1 0-1 0-1-1l-6-2h0c-1-1-1-2-1-3h1c-1-1-1-2-1-3l1-1h3z" class="K"></path><path d="M708 553l1-1c1 1 0 1 1 2 0 1 0 1 1 2v1h0c-1-1-2-1-2-1l-1 1c-1-1-3-1-4-2v-1h1c1 1 1 1 2 1l1-2z" class="B"></path><path d="M700 545l1 1 2 2v2h-2v-1-1h-1l-2 2-1-1c-1-1-1-2-1-3l1-1h3z" class="O"></path><path d="M710 564c0-1-2 0-3-1 0 0-1 0-1-1-2 0-4 0-5-1h-1s-1 0-1 1c-1 0-1 0-3-1 0-1 0-1 1-3v-1c3 0 7 0 10 1 2 1 3 1 4 1h1c4 1 7 3 11 3v1 1h1l-1 2c-3 0-6 0-9-1-1 0-3 0-4-1z" class="Y"></path><path d="M731 488c2 1 3 0 5 1 0 1 0 1 1 1 3 0 5 1 8 1v1c2 0 3-1 5 0h5 1c1 0 0 0 1 1h5l1 1 1 1h3 1v-1h3c2 0 2 0 4-2 1 1 2 1 3 1h1c1 0 4 0 4-1h1l1-1v1c1 0 3 0 4-1l1 1 1 2c1 0 1 0 2 1l-3 2-1 1-2-2-1 2h-2c-2 1-3 1-4 2v-1l-1 3-1 1c0 1-1 2-1 4v-1h1 3l2-1 1 1c-1 1-2 1-3 2h-1 3l-1 1c-7 3-11 3-18 2h-4c1 1 3 1 5 1l9 2v1c0 1 1 2 1 3l-1 1v1l-1 1-2-1c0 1-1 2-2 4l-1-1c-2 1-2 1-4 1l-2 1-1-1h-1c-1-1-2-1-3-1l-1 1-1-1c-1 1-5 3-5 4l-1-2v-1l-2-1h-2 0c-2 0-2-1-2-2-3 1-3 0-5 1-3 0-3 0-5-1-1-2-2-2-3-4l-1-1-1 1-1-1c-1 0-2 1-3 0v-1l-1 1-1-1h1l-1-1h-1c-1-1 0-1-1-1-1 1-2 1-2 2h0l-1 1v-1c-1-1-1-2-2-2l-1 1c-1-1-1-1-2-1l-1 2-1-1h-4c-1 1-2 2-3 2h-1l10-22 2-2c1-1 3-2 4-2l2 2h1l2-1h1s1-1 1-2h1 2c1 0 2-1 3-1z" class="i"></path><path d="M777 506h1 3l2-1 1 1c-1 1-2 1-3 2h-1c-2 1-4 1-5 1-4 1-8 2-11 1-1 0-2-1-3-1-2 0-4 1-5 0 2-1 4 0 6 0 5 0 10-1 15-2v-1z" class="K"></path><path d="M759 521l1-1v-2h1v2h3c-1-1-1-1-1-2h2l6 1v1c0 1-1 2-2 4l-1-1c-2 1-2 1-4 1l-2 1-1-1v-2h-1l1-1h-1-1z" class="P"></path><path d="M765 518l6 1v1c0 1-1 2-2 4l-1-1h-2-2l2-2v-2l-1-1z" class="Y"></path><defs><linearGradient id="AE" x1="726.963" y1="501.226" x2="730.346" y2="511.146" xlink:href="#B"><stop offset="0" stop-color="#646879"></stop><stop offset="1" stop-color="#7f8086"></stop></linearGradient></defs><path fill="url(#AE)" d="M730 505h5 1c1-1 2-1 2-1 2-1 2 0 3 0 1-1 3-1 4-2v-1l1 1c-1 1 0 1 0 2 0 0-2 2-2 3l1-1c1 0 1 1 3 1h6l1 1c-8 1-18-1-27-1-5 0-10 0-15 1-1-1-2-1-4-1l8-2h0 3c1 0 1 0 2-1h2 3c1 0 2 1 3 1z"></path><path d="M729 515l2-2v1 2c2 0 1-1 3-1l1 1v2h3l1-1h1c1 1 2-1 4-1l3 1h1c1 1 2 2 3 2 2 0 2 0 3 1h3s1 0 2 1h1 1l-1 1h1v2h-1c-1-1-2-1-3-1l-1 1-1-1c-1 1-5 3-5 4l-1-2v-1l-2-1h-2 0c-2 0-2-1-2-2-3 1-3 0-5 1-3 0-3 0-5-1-1-2-2-2-3-4l-1-1v-1z" class="V"></path><path d="M730 517c1 1 2 1 3 1 0 1 1 1 2 1s2 1 2 1l1 2c-3 0-3 0-5-1-1-2-2-2-3-4z" class="E"></path><path d="M740 517c1 1 2-1 4-1l1 3-1-1v1l-1 1-1-1c-2 0-2 1-3 1l1-3z" class="g"></path><path d="M745 523c2-1 6 0 8-2l2 2c-1 1-5 3-5 4l-1-2v-1l-2-1h-2 0z" class="H"></path><path d="M744 516l3 1h1c1 1 2 2 3 2 2 0 2 0 3 1l-4 2c-1-1-1-1-2-1h-3v-2h0l-1-3z" class="g"></path><path d="M744 516l3 1h1l-1 1v1h-2 0l-1-3z" class="Y"></path><defs><linearGradient id="AF" x1="760.521" y1="501.923" x2="759.912" y2="508.284" xlink:href="#B"><stop offset="0" stop-color="#727587"></stop><stop offset="1" stop-color="#888a94"></stop></linearGradient></defs><path fill="url(#AF)" d="M749 502v-1h1 2l3 1c2 0 7-1 8-1l2 2 1-1h3l1 1 2-1v1h3c1-1 2 0 3 0 0 1-1 2-1 4-5 1-10 2-15 2-2 0-4-1-6 0l-1-1-1-1h-6c-2 0-2-1-3-1l-1 1c0-1 2-3 2-3 0-1-1-1 0-2l1 1 2-1z"></path><path d="M749 502v-1h1 2l3 1c2 0 7-1 8-1l2 2 1-1h3l1 1 2-1v1h0c-1 1-2 1-3 0-4 0-7 1-10 0-2 0-4 1-6 1h-1c-1 0-1-1-2-1s-1 0-2 1l-1-1 2-1z" class="L"></path><path d="M749 502v-1h1 2l3 1h-2-4z" class="d"></path><path d="M718 490l2 2c-1 2-3 1-3 4v3l1 1c-1 1-1 1-1 2h-1 0c-1 0-1 0-1 1v1c1 0 1 0 2 1l-8 2c2 0 3 0 4 1h-3c-2 1-3 2-5 3v2h1 0c1-1 2-1 3-1s0 0 1-1c2 0 6-1 8 0h1 0 5c1 1 1 1 2 1 1 1 2-1 2 1v1l1 1v1l-1 1-1-1c-1 0-2 1-3 0v-1l-1 1-1-1h1l-1-1h-1c-1-1 0-1-1-1-1 1-2 1-2 2h0l-1 1v-1c-1-1-1-2-2-2l-1 1c-1-1-1-1-2-1l-1 2-1-1h-4c-1 1-2 2-3 2h-1l10-22 2-2c1-1 3-2 4-2z" class="g"></path><path d="M710 508l-3 1v-1c1-1 1-1 2-1 2 0 3 0 4 1h-3z" class="M"></path><path d="M718 490l2 2c-1 2-3 1-3 4v3c-1 0-2 0-3-1l-1 2-1-1c0-3 1-4 2-6v-1c1-1 3-2 4-2z" class="P"></path><path d="M731 488c2 1 3 0 5 1 0 1 0 1 1 1 3 0 5 1 8 1v1c2 0 3-1 5 0h5 1c1 0 0 0 1 1h5l1 1 1 1h3 1v-1h3c2 0 2 0 4-2 1 1 2 1 3 1h1c1 0 4 0 4-1h1l1-1v1c1 0 3 0 4-1l1 1 1 2c1 0 1 0 2 1l-3 2-1 1-2-2-1 2h-2c-2 1-3 1-4 2v-1l-1 3-1 1c-1 0-2-1-3 0h-3v-1l-2 1-1-1h-3l-1 1-2-2c-1 0-6 1-8 1l-3-1h-2-1v1l-2 1-1-1-1-1v1c-1 1-3 1-4 2-1 0-1-1-3 0 0 0-1 0-2 1h-1-5c-1 0-2-1-3-1h-3-2c-1 1-1 1-2 1h-3 0c-1-1-1-1-2-1v-1c0-1 0-1 1-1h0 1c0-1 0-1 1-2l-1-1v-3c0-3 2-2 3-4h1l2-1h1s1-1 1-2h1 2c1 0 2-1 3-1z" class="B"></path><path d="M738 498c1 1 1 1 1 2-1 1-2 2-3 2l-2-1v1c-1 0-1 1-2 1l-1-1-1 1h-1l-2 1h-3-2c-1 1-1 1-2 1 0-1 1-1 2-2h0c0-1 0-1 1-2 0 1 1 1 2 2v-1l2-2h1l1 1c1 0 3 0 5-1v-2l1 1c0 1 0 0 1 1l2-2z" class="F"></path><path d="M775 492c1 1 2 1 3 1h1c1 0 4 0 4-1h1l1-1v1c1 0 3 0 4-1l1 1 1 2h-2v-1l-1 1-2-1v1h-2l-1 2h-2c-1-1-1-1-2-1-2 0-3 0-4 1l-2-1v1l-2-1-2 2c-1-1-1-1-1-2h0v-1h3c2 0 2 0 4-2z" class="V"></path><path d="M722 498c1-1 1-1 1-2l1 1c2-1 0-2 2-3 0 1 0 1 1 2h1v-2h1c0-2 0-2 2-3 1 0 0 0 1 1 0 1 0 1 1 2h1s1 1 1 2v2c-1 0-1-1-2-1 0 0-1 1-2 1h-2v-2c-1 1-2 2-2 3v1l-2 2v1c-1-1-2-1-2-2-1 1-1 1-1 2h0c-1 1-2 1-2 2h-3 0c-1-1-1-1-2-1v-1c0-1 0-1 1-1h0 1c0-1 0-1 1-2l1 1 1-2 2-1z" class="K"></path><path d="M720 499l2-1c0 1 1 1 1 2l-1 1h-1-1c-1 2-2 2-3 4h0c-1-1-1-1-2-1v-1c0-1 0-1 1-1h0 1c0-1 0-1 1-2l1 1 1-2z" class="E"></path><defs><linearGradient id="AG" x1="742.622" y1="495.286" x2="743.646" y2="502.631" xlink:href="#B"><stop offset="0" stop-color="#4f505b"></stop><stop offset="1" stop-color="#696c7b"></stop></linearGradient></defs><path fill="url(#AG)" d="M738 498c-1-1-1-1-1-3h3c0 2 0 2 2 3 0 0 0-1 1 0h2c2 0 5 1 7 1v1 1h-2-1v1l-2 1-1-1-1-1v1c-1 1-3 1-4 2-1 0-1-1-3 0 0 0-1 0-2 1h-1-5c-1 0-2-1-3-1l2-1h1l1-1 1 1c1 0 1-1 2-1v-1l2 1c1 0 2-1 3-2 0-1 0-1-1-2z"></path><path d="M727 504l2-1h1l1-1 1 1c1 0 1 1 2 1-1 1-2 1-4 1-1 0-2-1-3-1z" class="d"></path><path d="M740 495h4c1 1 2 1 3 1l1-1 3 3 1-1h4l1 1c1-1 1-1 2-1 4 1 8 0 11 1s6-1 9 1c1-1 1-1 2-1h3 0c-2 1-3 1-4 2v-1l-1 3-1 1c-1 0-2-1-3 0h-3v-1l-2 1-1-1h-3l-1 1-2-2c-1 0-6 1-8 1l-3-1v-1-1c-2 0-5-1-7-1h-2c-1-1-1 0-1 0-2-1-2-1-2-3z" class="F"></path><path d="M752 500h4c3-1 6 0 9 0 1 1 3 0 4 0l1 1h1c1-1 2-1 4-1l2 1s1-1 2-1l1-1-1 3-1 1c-1 0-2-1-3 0h-3v-1l-2 1-1-1h-3l-1 1-2-2c-1 0-6 1-8 1l-3-1v-1z" class="f"></path><path d="M731 488c2 1 3 0 5 1 0 1 0 1 1 1 3 0 5 1 8 1v1c2 0 3-1 5 0h5 1c1 0 0 0 1 1h5l1 1 1 1h3l-3 1h-1c-3 1-5-1-7 0-3 0-3 0-5-1-1 0-2 0-3-1-1 0-1 0-2 1l-2-2h-2-1c-2-1-4 0-6-1l-1-1-2 1c-1-1 0-1-1-1-2 1-2 1-2 3h-1v2h-1c-1-1-1-1-1-2-2 1 0 2-2 3l-1-1c0 1 0 1-1 2l-2 1-1 2-1-1-1-1v-3c0-3 2-2 3-4h1l2-1h1s1-1 1-2h1 2c1 0 2-1 3-1z" class="H"></path><path d="M745 492c2 0 3-1 5 0h5 1c1 0 0 0 1 1h5l1 1c-1 1-2 1-3 1-3-2-5 0-8-2l-1 1c-1 0-1-1-2-1-2 0-2 0-4-1zm-14-4c2 1 3 0 5 1h-1c-1 1-2 1-3 0h-2c-1 1-1 2-2 4h-1l-1-1c-1 0-2 1-3 1-1 1-1 1-1 2-1 2-1 3-2 4l-1 2-1-1-1-1v-3c0-3 2-2 3-4h1l2-1h1s1-1 1-2h1 2c1 0 2-1 3-1z" class="g"></path><path d="M717 496l2-2 1 1 1-1 1 1c-1 2-1 3-2 4l-1 2-1-1-1-1v-3z" class="O"></path><path d="M160 314v-1c2-1 4 0 6 1l15 6c2 0 4 1 6 1 1 1 3 1 4 2l5 2 7 2 8 4c8 8 16 18 23 28 1 2 4 6 5 9 3 3 6 7 8 11v1l-1-1c-2-1-5-2-7-3l-2 1h0c-1-1-1-1-2-1-2 0-3 0-5-1l-9-3c-2-1-5-2-7-3h0-4c0-1 0-1-1 0h-1v-2l-1 1-1-1c-1 0-1 0-2-1l-1-1h-1c-1 0-2 0-3-1-3 1-4-1-7 3-1-1 0-1-1-1l-1-1h0l-1-2c0-2 0-2 1-4l1-1c-1 0-1 0-2 1l-1-1c1-1 1-1 3-1l1-1c-2-3-4-5-6-6v-2c-1-1-1-2-1-3l-3-2-3-1c-1-1-3-1-4-3-2-1-6-2-9-3 0-1-1-3-1-3-1-3-2-7-3-10l-3-5 2-1v-1l-1-1v-1z" class="c"></path><path d="M185 345c3 3 6 9 9 10 2 0 4 2 6 4l-1 1c1 0 1 0 2-1v1c1 1 2 1 3 1 1 2 1 2 3 3 2 2 4 4 7 5h-4c0-1 0-1-1 0h-1v-2l-1 1-1-1c-1 0-1 0-2-1l-1-1h-1c-1 0-2 0-3-1-3 1-4-1-7 3-1-1 0-1-1-1l-1-1h0l-1-2c0-2 0-2 1-4l1-1c-1 0-1 0-2 1l-1-1c1-1 1-1 3-1l1-1c-2-3-4-5-6-6v-2c-1-1-1-2-1-3z" class="J"></path><path d="M190 365l-1-2c0-2 0-2 1-4l1-1c-1 0-1 0-2 1l-1-1c1-1 1-1 3-1l1 1c2 0 2 1 3 2v3h2c3 0 4 1 6 2h-1c-1 0-2 0-3-1-3 1-4-1-7 3-1-1 0-1-1-1l-1-1h0z" class="L"></path><path d="M159 318l2-1v-1l-1-1v-1l3 3h0c1 1 3 2 4 3 0 2 2 4 4 6 5 6 10 13 17 18l-13-5c-2-1-6-2-9-3 0-1-1-3-1-3-1-3-2-7-3-10l-3-5z" class="a"></path><path d="M159 318l2-1v-1l-1-1v-1l3 3h0v1c0 2 2 5 1 6-1 0-1-1-2-1l-3-5z" class="D"></path><path d="M160 314v-1c2-1 4 0 6 1l15 6c2 0 4 1 6 1 1 1 3 1 4 2l5 2 7 2 8 4c8 8 16 18 23 28 1 2 4 6 5 9l-21-10c-19-11-35-23-51-38-1-1-3-2-4-3h0l-3-3zm679 7c1 0 8-2 10-3 4 0 6-2 9-3 2-1 4-2 5-1v2h0c1-2 2-3 4-4l-3 5v1c-1 2-1 3-3 4-1-1 0-1-1-3l-1 2h0c-2 2-4 4-5 7-2 3-5 6-8 9s-5 5-9 8l5-2h0l12-4v1l-8 2v1h-2c0 2-1 3-1 4-1 1-1 1-1 2-2 2-3 4-6 6l1 1h1v1c-2 1-4 2-5 4h0v1c-2 0-3 0-5 1l1 1h0c-2 0-2 0-4 1l-2-1h-2l-1 1c-2 2-4 2-6 3-1 1-1 1-2 1s-2 1-2 1c-2 1-3 1-4 2h-1c-2 1-4 1-6 1v1h-3c-3 1-5 1-8 1l-1 1c-3-1-3 0-6 0 0 0-1 1-2 1h0c-2 0-3 1-4 1h-2v1h-3c0 1-1 2-1 2-1 0-1-1-2-1-2 1-3 3-5 3l-2-1v-1l6-4 5-3 5-2v-1l5-2c-2-2 0-3 0-5 1-1 0-2 0-3v-7c1-1 1-2 2-3l5-3 11-7c6-4 13-8 20-12 7-3 14-5 20-8z" class="e"></path><path d="M783 351l5-3c-2 2-3 6-4 9 0 3 0 2 2 4l1 1h-1c-1 1 0 1-1 1l-2-1c0 1 1 3 2 4s0 0 1 0h1l-6 3c-2-2 0-3 0-5 1-1 0-2 0-3v-7c1-1 1-2 2-3z" class="D"></path><defs><linearGradient id="AH" x1="808.286" y1="360.26" x2="804.203" y2="351.759" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#AH)" d="M859 321h0c-2 2-4 4-5 7-2 3-5 6-8 9s-5 5-9 8l-7 4-5 2c-2 1-4 3-6 4-1 0-2 1-3 1l-2 1c-3 2-6 4-9 5-1 1-2 1-2 2-3 2-7 2-10 4-1 1-2 1-2 1l-3 1-5 1c-1 1-2 1-2 2l-6 3c-2 1-3 2-5 3 0 1-1 2-1 2-1 0-1-1-2-1-2 1-3 3-5 3l-2-1v-1l6-4 5-3 5-2v-1l5-2 6-3c3 0 8-3 10-4 14-7 28-14 41-24 7-5 14-11 21-17z"></path><path d="M854 339v1l-8 2v1h-2c0 2-1 3-1 4-1 1-1 1-1 2-2 2-3 4-6 6l1 1h1v1c-2 1-4 2-5 4h0v1c-2 0-3 0-5 1l1 1h0c-2 0-2 0-4 1l-2-1h-2l-1 1c-2 2-4 2-6 3-1 1-1 1-2 1s-2 1-2 1c-2 1-3 1-4 2h-1c-2 1-4 1-6 1v1h-3c-3 1-5 1-8 1l-1 1c-3-1-3 0-6 0 0 0-1 1-2 1h0c-2 0-3 1-4 1h-2v1h-3c2-1 3-2 5-3l6-3c0-1 1-1 2-2l5-1 3-1s1 0 2-1c3-2 7-2 10-4 0-1 1-1 2-2 3-1 6-3 9-5l2-1c1 0 2-1 3-1 2-1 4-3 6-4l5-2 7-4 5-2h0l12-4z" class="c"></path><path d="M854 339v1l-8 2v1h-2c0 2-1 3-1 4-1 1-1 1-1 2-2 2-3 4-6 6l1 1h1v1c-2 1-4 2-5 4h0v1c-2 0-3 0-5 1l1 1h0c-2 0-2 0-4 1l-2-1h-2l-1 1c-2 2-4 2-6 3-1 1-1 1-2 1s-2 1-2 1c-2 1-3 1-4 2h-1c-2 1-4 1-6 1v1h-3c2-2 5-3 8-3 2-1 6-2 8-3 1-1 1-1 2-1 1-1 2-2 3-2 1-1 1-1 1-2l3-2c3-3 9-6 13-9 2-1 3-3 4-4l1-1c1-1 2-2 3-4h0l12-4z" class="f"></path><path d="M844 343c0 2-1 3-1 4-1 1-1 1-1 2-2 2-3 4-6 6l1 1h1v1c-2 1-4 2-5 4h0v1c-2 0-3 0-5 1l1 1h0c-2 0-2 0-4 1l-2-1c1 0 1-1 2-1 2-1 4-1 5-2l-1-1c1-1 1-1 2-1h1 0l1-1c0-1 0-1 1-2v-1l1-1c2-1 2-1 3-3 0-2 2-3 3-4 1-2 1-2 3-4z" class="U"></path><path d="M186 220l1-3c0-1 0-1 1-2h1c3 4 7 7 11 10l3 3c2 1 3 2 4 4 4 6 11 10 15 16l2 2c2 2 4 4 7 6 0 1 1 2 2 3 0 1 1 2 1 3 1 1 2 3 4 4 1 2 1 3 1 4l1 1c1 2 1 3 0 5 6 4 14 10 17 17h-1l-14-2-12-2h0c-2 0-5 0-6-1l-19-3c0-2-6-5-8-6-5-3-14-9-16-15-1-1-2-3-3-4l-6-7c-3-4-6-9-7-14l-2-4c2 1 5 2 7 3 3 0 8 1 11 3h0c2 1 5 2 7 3-3-4-6-8-8-13l1-1s1 0 2-1h3l-1-1v-3h0v-2c0-1 0-2 1-3z" class="k"></path><path d="M230 289h5c0-1-1-2-1-2v-1l4 2c1 1 3 2 4 3l-12-2z" class="I"></path><path d="M185 268c3 1 4 2 6 4l3 1c2 1 3 3 5 3-1-1-2-1-2-2v-1c-1-2 0 1-1-1v-1-2h0c-2-1-2-1-2-2l1-1c-2-2-2-3-3-4l1-1 4 5 17 14c3 3 7 5 10 8l-19-3c0-2-6-5-8-6-5-3-14-9-16-15 1 1 3 4 4 4z" class="H"></path><path d="M211 282l7 5c-2-1-5-1-7-3h1c-1-1-1-1-1-2h0z" class="X"></path><defs><linearGradient id="AI" x1="209.468" y1="279.013" x2="203.032" y2="281.487" xlink:href="#B"><stop offset="0" stop-color="#a5a3a5"></stop><stop offset="1" stop-color="#c1c0bd"></stop></linearGradient></defs><path fill="url(#AI)" d="M185 268c3 1 4 2 6 4l3 1c2 1 3 3 5 3-1-1-2-1-2-2v-1c-1-2 0 1-1-1v-1-2h0c-2-1-2-1-2-2l1-1c-2-2-2-3-3-4l1-1 4 5c1 2 4 4 5 7 2 3 7 6 9 9h0c0 1 0 1 1 2h-1c-4-2-9-5-13-7-4-3-9-5-13-9z"></path><path d="M186 220l1-3c0-1 0-1 1-2h1c3 4 7 7 11 10l3 3c2 1 3 2 4 4 4 6 11 10 15 16l2 2c2 2 4 4 7 6 0 1 1 2 2 3 0 1 1 2 1 3 1 1 2 3 4 4 1 2 1 3 1 4l1 1c1 2 1 3 0 5 6 4 14 10 17 17h-1l-14-2c-1-1-3-2-4-3 1 0 2-1 3 0h1v-1-1c-1-2-1-2 0-4-2-1-3-2-4-4-2-1-4-2-4-5h-1l-35-23c-3-1-7-4-10-6-3-4-6-8-8-13l1-1s1 0 2-1h3l-1-1v-3h0v-2c0-1 0-2 1-3z" class="k"></path><path d="M198 246l42 30c6 4 14 10 17 17h-1l-2-2c-6-7-12-13-21-18l-35-23 1-1c0-1 0-2-1-3h0z" class="N"></path><path d="M233 273c9 5 15 11 21 18l2 2-14-2c-1-1-3-2-4-3 1 0 2-1 3 0h1v-1-1c-1-2-1-2 0-4-2-1-3-2-4-4-2-1-4-2-4-5h-1z" class="S"></path><path d="M185 225h1v1c1 6 6 9 8 15 1 1 4 3 4 5h0c1 1 1 2 1 3l-1 1c-3-1-7-4-10-6-3-4-6-8-8-13l1-1s1 0 2-1h3l-1-1v-3z" class="C"></path><path d="M183 229c2 3 3 7 6 11l1 1h-1c-2-1-5-5-6-7s-1-2-3-3l1-1s1 0 2-1z" class="e"></path><path d="M185 225h1v1c1 6 6 9 8 15 0 1 1 2 1 3-2 0-4-1-5-3l-1-1c-3-4-4-8-6-11h3l-1-1v-3z" class="j"></path><path d="M823 229h0l3-3c0-1 2-1 3-2 2-2 3-4 5-5l6-9c2-2 5-5 7-8 1-1 4-7 6-7v5l-1 2v3l-5 20c-1 1-3 5-3 6 1 1 1 1 1 3-2 3-4 7-6 10l15-6h1c3 0 5-1 7-2h1c0 1 0 1-1 2v1 1 1c-1 0-1 1-2 2v1c-1 1-1 1-1 2l-1 1-7 10h0c-1 2-2 3-3 4s0 1-1 2c-2 3-5 4-7 7-1 1-1 3-2 3h-1c-1 1-1 2-3 2l-8 7-1 1c-1 0-2 1-3 2l-3 1c-1 1-2 1-3 1l-3-1-7 2c-1 0-2 0-3 1-4 1-8 1-13 1l-16 2-3 1-2-1c1-1 2-3 3-5 0-1 0-2 1-4h1c0-1 1-2 2-3v-2l2-2c1-1 2-2 2-3 2-4 6-7 9-10l16-15c1-2 3-3 4-5 3-4 7-7 10-11 1-1 2-2 4-3z" class="k"></path><path d="M806 288l8-7c0 1 0 2-1 3v2h0l-7 2z" class="B"></path><path d="M788 273l4-4 1 1h0c1 0 1 0 2 1l1-1h2v1l-3 2h-1-1l-1 1c-2 1-3 0-4-1z" class="j"></path><path d="M814 281c2-2 4-4 6-5l9-8c-1 2-4 5-6 7l-10 11v-2c1-1 1-2 1-3z" class="F"></path><path d="M797 258c-1 4-5 5-6 9h2l1-1h0l-2 3-4 4-1 1c-1-1-1-2-2-3 2-2 3-3 3-5h0l2-3c2-1 5-4 7-5z" class="C"></path><path d="M788 273c1 1 2 2 4 1l1-1h1c-1 1-1 2-3 2-1 1-1 2-1 3l-1 1c-2 1-7 5-7 7-1 1 0 3 1 4h1c1 0 3 0 4-1h1 0 0c1-2 1-2 2-3s1-1 1-2c1 1 1 1 0 2 0 2-1 3-2 4l-16 2 13-13h0l-1-1-2 3h-1l1-2c1 0 1-1 2-2-1 0-1 0-2 1l-1-1c2 0 2 0 2-2 1 0 1 0 2-1h0l1-1z" class="h"></path><path d="M784 279l-1 2h1l2-3 1 1h0l-13 13-3 1-2-1c1-1 2-3 3-5 0-1 0-2 1-4h1c0-1 1-2 2-3 0 0 1 1 0 2 0 1-1 3-1 3l1-1 1 1 1-2c2-1 4-3 5-4h1z" class="C"></path><path d="M844 231c1 1 1 1 1 3-2 3-4 7-6 10-4 3-9 5-13 8l-3 3c-3 1-5 3-8 4v1l-1-1 4-3 1-1c2-1 4-3 6-5 5-4 9-9 13-14v1s-1 1-1 2c0 2-3 5-4 6 2-1 3-3 5-5l3-3 3-6z" class="S"></path><path d="M829 268c1-1 1-2 2-2v1c-1 5-2 6-6 9-1 1-1 2-2 3l-2 2c1 0 1 0 2-1h2l1-1c1 0 3-1 4-2l2-2h2l3-3c1 0 2-1 3-2-1 1-1 3-2 3h-1c-1 1-1 2-3 2l-8 7-1 1c-1 0-2 1-3 2l-3 1c-1 1-2 1-3 1l-3-1h0l10-11c2-2 5-5 6-7z" class="I"></path><path d="M776 280v-2l2-2c1-1 2-2 2-3 2-4 6-7 9-10l16-15c1-2 3-3 4-5 3-4 7-7 10-11 1-1 2-2 4-3-1 2-3 3-4 5l-3 4c-4 5-9 9-13 13-2 2-4 5-6 7-2 1-5 4-7 5l-2 3h0c0 2-1 3-3 5 1 1 1 2 2 3h0c-1 1-1 1-2 1 0 2 0 2-2 2l1 1c1-1 1-1 2-1-1 1-1 2-2 2h-1c-1 1-3 3-5 4l-1 2-1-1-1 1s1-2 1-3c1-1 0-2 0-2z" class="b"></path><path d="M776 284l1-1c1-3 3-6 5-7s1-1 1-2 2-2 2-3c1 1 1 2 2 3h0c-1 1-1 1-2 1 0 2 0 2-2 2l1 1c1-1 1-1 2-1-1 1-1 2-2 2h-1c-1 1-3 3-5 4l-1 2-1-1z" class="I"></path><defs><linearGradient id="AJ" x1="275.846" y1="498.707" x2="274.831" y2="509.426" xlink:href="#B"><stop offset="0" stop-color="#4f505e"></stop><stop offset="1" stop-color="#686a78"></stop></linearGradient></defs><path fill="url(#AJ)" d="M228 463l-1-1v-1c2-1 4-3 6-4h4c1 0 1 0 3 1s6 1 9 1h5l11 1c4 1 8 1 11 2 1 2 2 2 4 3l2 1h0c1 0 1 1 2 1 1 1 2 1 3 2 1 0 1 1 2 1 2 2 4 2 5 4l3 7c1 1 1 2 1 4 2 1 2 2 3 4l3 8 4 8v1h-2v1l-27 1c-1 1-2 1-3 1l-14 3c-3 0-5 1-8 1h0c-4-1-7-1-11-4l-8-5-1-1v-1c-1-1-3-4-4-5v-2c-1 0-2 0-3-1v-2-1-1l-2-3-3-4c0-2 1-2 2-4l-6-2-2-1h0l2-2c1 0 2-1 4-1v1l1-1v-2h1c1-1 2-1 3-1h2 0c0-1 0-1-1-2v-1l-2-1-1-2 2-2s0 1 1 1z"></path><path d="M291 502l1 1h2l-1-1v-1h2l1 2c-1 0-3 1-4 1l-1-1h-1 0v-2l1 1z" class="F"></path><path d="M251 502h0c3-1 5-1 8-1l2 1v1h-4c-1 0-1 1-2 1s-2-1-3-1l-1-1z" class="J"></path><path d="M234 502l2 1c1 1 1 1 3 1h6 1l1 1c0 1 1 1 2 2h-4l-2-1v1l3 1c-1 1-2 1-3 1l-8-5-1-1v-1z" class="H"></path><path d="M299 493l2 1h0c0 1 0 2 1 3h1l1 1v-1l4 8h-1c-3 0-3-1-5-2h-1c-1-1-2-1-2-2v-1l-2 2-1 1-1-2h-2v1l1 1h-2l-1-1 1-1v-2l3-2v1l1 1c-1-2 0-3 0-4l3-2z" class="N"></path><path d="M299 493l2 1h0c0 1 0 2 1 3h-1l-1-1c-1 1-1 1-1 2l-1-1-2 1c0 1 1 1 1 2l-1-1c-1-2 0-3 0-4l3-2z" class="B"></path><defs><linearGradient id="AK" x1="253.074" y1="515.666" x2="263.681" y2="502.635" xlink:href="#B"><stop offset="0" stop-color="#191817"></stop><stop offset="1" stop-color="#2f2f34"></stop></linearGradient></defs><path fill="url(#AK)" d="M246 508c4 0 9 1 13 1l16-1h4c-1 1-2 1-3 1l-14 3c-3 0-5 1-8 1h0c-4-1-7-1-11-4 1 0 2 0 3-1z"></path><path d="M230 495c2-1 5 1 6 2h0l2-1c1 1 2 1 4 1l1 1 1-1c1 0 3-1 4 0s1 0 2 0c2 1 3 1 5 1h1 5c2-1 5-1 7-1 0 1-1 1-1 1h-3l2 2c2-1 3-2 5-1l1 1h-3l-3 2-2-1c-1 0-2 0-3 1l-2-1c-3 0-5 0-8 1h0-1l-1 2c-1 0-1 0-3-2v2h-1-6c-2 0-2 0-3-1l-2-1c-1-1-3-4-4-5v-2z" class="F"></path><path d="M236 501l-3-2 1-1h1c1 0 2 1 2 2l-1 1z" class="M"></path><path d="M236 501l1-1 1 2c1 0 1-1 2-1v1l-1 2c-2 0-2 0-3-1v-2zm9 3c0-1 1-1 0-2l-1 2c-1-1-2-1-3-2v-2h-1l1-1c1 0 1 0 1 1 1 1 2 0 4 0 1 0 2 1 3 1l1 1-1 2c-1 0-1 0-3-2v2h-1z" class="J"></path><path d="M243 498l1-1c1 0 3-1 4 0s1 0 2 0c2 1 3 1 5 1h1 5c2-1 5-1 7-1 0 1-1 1-1 1h-3-1c-2 1-3 1-4 1-2 1-6 0-7 0l-1-1c-1 0-2 0-2 1-2-1-4 0-6-1z" class="K"></path><path d="M250 497c3 0 4-1 7 0 1-1 3-2 5-1 2 0 6 0 7-1h5 1 1l1-1h4c1-1 1-1 2-1h2l1-1c2 0 3 1 5 1h1v1l3-1 1 2c0 1-1 2 0 4l-1-1v-1l-3 2v2l-1 1-1-1 1-1-1-1-1 1-1 2-2-2h-2l-2 2-1 2-1-1-4-1h0l-1-2c-1 1-2 1-3 1v-1l-1-1c-2-1-3 0-5 1l-2-2h3s1 0 1-1c-2 0-5 0-7 1h-5-1c-2 0-3 0-5-1z" class="E"></path><path d="M287 497l1-2 1 1h1l-2 1h-1z" class="K"></path><path d="M282 497h1c0-1 0-1 1-2 1 0 1 2 2 3l1-1h1v2l1 1-1 2-2-2h-2l-2 2-1-1c0-1 1-1 0-2 0-1 0-1 1-2z" class="B"></path><defs><linearGradient id="AL" x1="275.129" y1="494.913" x2="276.397" y2="502.745" xlink:href="#B"><stop offset="0" stop-color="#3e3f48"></stop><stop offset="1" stop-color="#555763"></stop></linearGradient></defs><path fill="url(#AL)" d="M268 497c1-1 2-1 3-1h1l5 1h0c1 0 1 0 2-1h1 1l1 1c-1 1-1 1-1 2 1 1 0 1 0 2l1 1-1 2-1-1-4-1h0l-1-2c-1 1-2 1-3 1v-1l-1-1c-2-1-3 0-5 1l-2-2h3s1 0 1-1z"></path><path d="M224 479c2 1 4 1 7 2 8 1 17 2 25 2l35 1 1-1c1-1 2-1 3-2h2c1 1 1 2 1 4 2 1 2 2 3 4l3 8v1l-1-1h-1c-1-1-1-2-1-3h0l-2-1-3 2-1-2-3 1v-1h-1c-2 0-3-1-5-1l-1 1h-2c-1 0-1 0-2 1h-4l-1 1h-1-1-5c-1 1-5 1-7 1-2-1-4 0-5 1-3-1-4 0-7 0-1 0-1 1-2 0s-3 0-4 0l-1 1-1-1c-2 0-3 0-4-1l-2 1h0c-1-1-4-3-6-2-1 0-2 0-3-1v-2-1-1l-2-3-3-4c0-2 1-2 2-4z" class="c"></path><path d="M297 481c1 1 1 2 1 4l-7-1 1-1c1-1 2-1 3-2h2z" class="U"></path><path d="M262 493c2-1 4-1 6-1h2c1 0 3-1 5 0h1c1-1 3-1 5-2h3c2-1 3-1 4-1h2c1 0 2-1 3 0h0c3 0 5 0 7 1l1-1 3 8v1l-1-1h-1c-1-1-1-2-1-3h0l-2-1-3 2-1-2-1-1c-1-1-2-1-3-2-1 0-2 0-3 1-1 0-2-1-3-1 0 1 0 1-2 2h-2l-2 1c-2-1-1 0-3 0h-1-2c0 1-1 1-1 0l-2 1-1-1h-1c0 1 0 1-1 1h0-3c-1 0-2-1-2-1z" class="P"></path><path d="M291 490c2 0 4 0 5 1h1c1 1 2 1 2 2l-3 2-1-2-1-1c-1-1-2-1-3-2z" class="O"></path><path d="M225 487c2 1 3 2 6 2 1 0 2 0 3 1 3 1 6 1 9 1 2 1 5 1 8 1h3c1 0 7 0 8 1 0 0 1 1 2 1h3 0c1 0 1 0 1-1h1l1 1 2-1c0 1 1 1 1 0h2 1c2 0 1-1 3 0l2-1h2c2-1 2-1 2-2 1 0 2 1 3 1 1-1 2-1 3-1 1 1 2 1 3 2l1 1-3 1v-1h-1c-2 0-3-1-5-1l-1 1h-2c-1 0-1 0-2 1h-4l-1 1h-1-1-5c-1 1-5 1-7 1-2-1-4 0-5 1-3-1-4 0-7 0-1 0-1 1-2 0s-3 0-4 0l-1 1-1-1c-2 0-3 0-4-1l-2 1h0c-1-1-4-3-6-2-1 0-2 0-3-1v-2-1-1l-2-3z" class="V"></path><path d="M227 491l1 1c1 0 2 1 3 1 3 0 5 2 7 3l-2 1h0c-1-1-4-3-6-2-1 0-2 0-3-1v-2-1z" class="K"></path><path d="M225 487c2 1 3 2 6 2 1 0 2 0 3 1 3 1 6 1 9 1 2 1 5 1 8 1h3c1 0 7 0 8 1l-2 1h0c0 1-2 1-2 1l-1-1-2 1-1-1c-2 0-1 1-3 1l-1-1-1 1c-2 0-4-1-5-1h-3c-1-1-3-1-5-1 0-1-1-1-1-1-2 0-2-1-3-1s-3 0-4 1l-1-1v-1l-2-3z" class="Y"></path><defs><linearGradient id="AM" x1="260.324" y1="463.279" x2="256.988" y2="483.98" xlink:href="#B"><stop offset="0" stop-color="#a3a2a6"></stop><stop offset="1" stop-color="#bfbfbf"></stop></linearGradient></defs><path fill="url(#AM)" d="M228 463l-1-1v-1c2-1 4-3 6-4h4c1 0 1 0 3 1s6 1 9 1h5l11 1c4 1 8 1 11 2 1 2 2 2 4 3l2 1h0c1 0 1 1 2 1 1 1 2 1 3 2 1 0 1 1 2 1 2 2 4 2 5 4l3 7h-2c-1 1-2 1-3 2l-1 1-35-1c-8 0-17-1-25-2-3-1-5-1-7-2l-6-2-2-1h0l2-2c1 0 2-1 4-1v1l1-1v-2h1c1-1 2-1 3-1h2 0c0-1 0-1-1-2v-1l-2-1-1-2 2-2s0 1 1 1z"></path><path d="M234 471c1 0 1 0 2 1h3c-2 1-3 0-4 3h-1c-1 0-1 0-1 1h-2l1 1-2 1h0c0-1 0-1 1-2h-3s-1 0-1 1l-1-1c1 0 2-1 2-1 1-1 2-1 3-2 1 0 2 0 2-1l1-1zm51 1c0 1 1 1 2 2h1l1 1v1l-2-1-1 1c2 3 3 3 6 4 1 0 2 1 3 1-1 1-2 1-3 2l-1 1-35-1h21c2 0 6 0 8-1v-1-1l1 1v1h3c1 1 2-1 4-1h-2c-1-1-3-1-4-2l-3-3h-1c0-2 0-3 1-4h1z" class="C"></path><path d="M229 470l1-1c1 1 2 1 2 2l1 1c0 1-1 1-2 1-1 1-2 1-3 2 0 0-1 1-2 1h-2 0l1 1h-1c-1 0 0 0-1 1h2l1 1h1c1 0 2 1 4 2-3-1-5-1-7-2l-6-2-2-1h0l2-2c1 0 2-1 4-1v1l1-1v-2h1c1-1 2-1 3-1h2 0z" class="G"></path><path d="M223 478c-1-2-1-2 0-3l2-1 1 2c1-1 1-1 1-2 1-1 2-1 2-1l1-1v-1h2l1 1c0 1-1 1-2 1-1 1-2 1-3 2 0 0-1 1-2 1h-2 0l1 1h-1c-1 0 0 0-1 1z" class="b"></path><path d="M228 463c2-2 2-2 4-2 2 1 3 1 4 1h2c1 0 2 0 3 1l1-1c1 0 3 1 3 1 1 0 0-2 2-1 1 1 2 1 3 1h0v-1c3-1 5-1 8 0 4 0 10 0 14 2 3 1 8 3 10 5-1 0-3 1-4 0s-2-1-3-2l-2-1c-4-2-7-3-12-3l-1 1v-1c-4 0-4 3-7 3l-1-1v-1c-1 1-1 1 0 2h-3 0-2-4l-1 1h-3-1v1c1 1 1 2 2 3l-1 1h-3c-1-1-1-1-2-1l-1 1-1-1c0-1-1-1-2-2l-1 1c0-1 0-1-1-2v-1l-2-1-1-2 2-2s0 1 1 1z" class="Q"></path><path d="M238 462c1 0 2 0 3 1l-1 1h-1c-2-1-1-1-2-1l1-1z" class="U"></path><defs><linearGradient id="AN" x1="226.675" y1="464.974" x2="233.875" y2="467.375" xlink:href="#B"><stop offset="0" stop-color="#7b7b80"></stop><stop offset="1" stop-color="#929296"></stop></linearGradient></defs><path fill="url(#AN)" d="M228 463c2-2 2-2 4-2 2 1 3 1 4 1h2l-1 1v1c-1 0-2 1-2 2 0 0-1 1-1 2v1 2l-1 1-1-1c0-1-1-1-2-2l-1 1c0-1 0-1-1-2v-1l-2-1-1-2 2-2s0 1 1 1z"></path><path d="M236 462h2l-1 1v1h-2-1c-1-1-1-1-2-3 2 1 3 1 4 1z" class="G"></path><path d="M228 463l-1-1v-1c2-1 4-3 6-4h4c1 0 1 0 3 1s6 1 9 1h5l11 1c4 1 8 1 11 2 1 2 2 2 4 3l2 1h0c1 0 1 1 2 1 1 1 2 1 3 2 1 0 1 1 2 1 2 2 4 2 5 4l3 7h-2c-1 0-2-1-3-1-3-1-4-1-6-4l1-1 2 1v-1l-1-1h-1c-1-1-2-1-2-2-2-1-2-2-3-3-2-2-7-4-10-5-4-2-10-2-14-2-3-1-5-1-8 0v1h0c-1 0-2 0-3-1-2-1-1 1-2 1 0 0-2-1-3-1l-1 1c-1-1-2-1-3-1h-2c-1 0-2 0-4-1-2 0-2 0-4 2z" class="Z"></path><path d="M289 475c1 0 2 0 2 1l2 2h-1-2v-1l-1-1v-1zm-17-11c6 1 9 3 14 6 1 1 2 1 2 2h0l-1 1 1 1h-1c-1-1-2-1-2-2-2-1-2-2-3-3-2-2-7-4-10-5z" class="U"></path><path d="M228 463l-1-1v-1c2-1 4-3 6-4h4c1 0 1 0 3 1s6 1 9 1h5l11 1c-1 1-3 1-4 1-3-1-5 0-8-1-4 0-7-1-11 0 0 0-1 0-1-1-1 0-2 1-3 0l-2 3c-1 0-2 0-4-1-2 0-2 0-4 2z" class="L"></path><defs><linearGradient id="AO" x1="772.28" y1="463.523" x2="773.268" y2="482.012" xlink:href="#B"><stop offset="0" stop-color="#a2a1a6"></stop><stop offset="1" stop-color="#c8c8c7"></stop></linearGradient></defs><path fill="url(#AO)" d="M815 433c2 1 3 1 5 0l1 2c0 1 1 2 1 3v1h1c1-1 1-2 2-2h1l-1 1v1c-5 6-11 9-19 11-1 0-2 0-3 1 2 1 2 0 4 0l2 2h0 3v3h-1c-1 0-2 0-3 1l1 1h1c1 0 1 1 2 1 1 1 2 1 4 1v1c0 1 0 1-1 2 0 1-1 1 0 2h2c0 1 0 1-1 1l4 1 1 1v1 1c-2 0-4 1-6 2h0c-1 1-2 2-4 2l-2 2 1 1-3 2-2 1c-2 4-5 7-7 10-2 0-3 0-4 1s-3 2-4 1l-1-1c-1 1-3 1-4 1v-1l-1 1h-1c0 1-3 1-4 1h-1c-1 0-2 0-3-1-2 2-2 2-4 2h-3v1h-1-3l-1-1-1-1h-5c-1-1 0-1-1-1h-1-5c-2-1-3 0-5 0v-1c-3 0-5-1-8-1-1 0-1 0-1-1-2-1-3 0-5-1-1 0-2 1-3 1h-2-1c0 1-1 2-1 2h-1l-2 1h-1l-2-2c-1 0-3 1-4 2l-2 2 1-3s1 0 2-1 0-2 3-2c0 0 0-1 1-1l2-1s1 0 2-1v-1c1 0 2 0 2-1h1c1-1 1-2 3-2 1 0 0 0 1-1l-1-1c-1 1-1 0-2 1v-2c0-1 0 0 1-1h0c0-1 0-1-1-2l1-2h0-3-1v-2h0l-1-2c2-3 5-3 7-4l1-1c-3 0-4 0-7 2h-1c0-1 1-3 2-4 2 0 3-1 6-1 1 0 1-1 2-1s2-2 3-3h2c1-1 1-2 2-3v1c2 0 2-1 4-1h1l1-2 1-2c2 0 4-1 6-2l2-1c1 0 2 0 3-1h0 6 6c1-1 1-1 2 0v-1c1 0 2-1 3-1h1v-1h3c3-1 7-2 11-3 2 0 5 0 7-1 1-1 3 0 4-1s2-1 3-1c1-1 3-1 4-2l3-1c1 0 3 0 4-1z"></path><path d="M742 470l1 1s0 1 1 2c-1 1 0 1-1 2v1l1-1s1 1 1 0v1c-1 1-3 1-4 1h-2v1 1-1l-2-2 2-2c1 0 1-1 4 0 0-1-1-3-1-4zm47-3c2 1 3 2 5 2h0 1c1 1 1 2 2 2v3h1 0v-1h2c1 2 1 2 4 2l-3 1h0-1c-1 0-3 1-4 1 0 0-1 0-2-1-1-2-2-2-2-5l-2-2-1-1v-1z" class="C"></path><path d="M774 465h4c1 0 4 1 5 0h1c1 1 1 1 2 0l1 1c-1 1 0 1 0 2v1l-2 1h-1c-1-1-1-1-2-1l-1-1h-1l-2 1-1-1h-1 0l-1-1h-2-1l-1-1h-3v-1h6z" class="X"></path><path d="M797 471c1 1 1 1 3 1l2-1 1 1h2 1c1 0 1 0 2 1 1 0 2 0 3 1l-2 2 1 1-3 2-2 1h-4l-1-1h-2l1-1h1l1-1 1 1 1-1h1c2-1 0-1 2-1h1c-2 0-2-1-3-1-3 0-3 0-4-2h-2v1h0-1v-3z" class="b"></path><path d="M809 476l1 1-3 2-2 1h-4l-1-1 9-3z" class="O"></path><defs><linearGradient id="AP" x1="729.341" y1="478.438" x2="738.62" y2="484.217" xlink:href="#B"><stop offset="0" stop-color="#8e8e94"></stop><stop offset="1" stop-color="#b0b1b6"></stop></linearGradient></defs><path fill="url(#AP)" d="M731 478c0-1 2-2 3-3 1 0 1 1 2 1h1l2 2v1-1-1h2c-1 1-1 1-1 2h1 1 1l1-1h3c-2 2-4 2-6 2l1 2h3c1 0 2 1 3 1-4 0-10 0-14 1l-8 1h-3v-1c1 0 2 0 2-1h1c1-1 1-2 3-2 1 0 0 0 1-1l-1-1c-1 1-1 0-2 1v-2l1-1 1 1 1 1 1-1z"></path><path d="M798 479h2l1 1h4c-2 4-5 7-7 10-2 0-3 0-4 1s-3 2-4 1l-1-1c-1 1-3 1-4 1v-1l-1 1h-1c0 1-3 1-4 1h-1c-1 0-2 0-3-1-2 2-2 2-4 2h-3v1h-1-3l-1-1-1-1h-5c-1-1 0-1-1-1h-1-5c-2-1-3 0-5 0v-1c-3 0-5-1-8-1-1 0-1 0-1-1-2-1-3 0-5-1-1 0-2 1-3 1h-2-1c0 1-1 2-1 2h-1l-2 1h-1l-2-2c-1 0-3 1-4 2l-2 2 1-3s1 0 2-1 0-2 3-2c0 0 0-1 1-1l2-1s1 0 2-1h3l8-1c4-1 10-1 14-1 13 0 27 0 40-2 4 0 7-1 10-2z" class="c"></path><path d="M798 479h2l1 1-2 1h0c-4 1-7 1-11 0 4 0 7-1 10-2zm-36 14c2 0 3-1 4 0 2 0 7 0 9-1-2 2-2 2-4 2h-3v1h-1-3l-1-1-1-1z" class="P"></path><path d="M801 480h4c-2 4-5 7-7 10-2 0-3 0-4 1v-4l1 1c1 0 2 0 3-1h1l1-1c1-2 2-3 3-4h0v-1h-3c0 1-1 0-1 0h0l2-1z" class="i"></path><path d="M726 485l8-1h-1c-1 1-2 1-4 1l-1 1s-1 1-2 1l1 1h0c2-1 2-1 4 0-1 0-2 1-3 1h-2-1c0 1-1 2-1 2h-1l-2 1h-1l-2-2c-1 0-3 1-4 2l-2 2 1-3s1 0 2-1 0-2 3-2c0 0 0-1 1-1l2-1s1 0 2-1h3z" class="P"></path><path d="M723 485h3c-1 2-1 3-3 4 0 1-1 1-2 1v2h-1l-2-2c1 0 1-1 2-1 1-1 1-1 2-1h0l1-1h-2v-1s1 0 2-1z" class="V"></path><path d="M719 487l2-1v1h2l-1 1h0c-1 0-1 0-2 1-1 0-1 1-2 1s-3 1-4 2l-2 2 1-3s1 0 2-1 0-2 3-2c0 0 0-1 1-1z" class="B"></path><path d="M769 459c3-1 6-1 9-2 9-2 17-3 25-6 2 1 2 0 4 0l2 2h0 3v3h-1c-1 0-2 0-3 1l1 1h1c1 0 1 1 2 1 1 1 2 1 4 1v1c0 1 0 1-1 2 0 1-1 1 0 2h2c0 1 0 1-1 1l4 1 1 1v1 1c-2 0-4 1-6 2h0c-1 1-2 2-4 2-1-1-2-1-3-1-1-1-1-1-2-1h-1-2l-1-1-2 1c-2 0-2 0-3-1-1 0-1-1-2-2h-1 0c-2 0-3-1-5-2h0c1-2 0-2 0-3h-1l-1 1v1l-1-1c-1 1-1 1-2 0h-1c-1 1-4 0-5 0h-4c-2-1-3-1-5-1-4 1-9 0-13 1-1-1-3 0-5 0-1 0-1 1-2 1h-1c0 1 1 2 1 3h0l-3-2-3 1-1-1v3h0c0 1 1 3 1 4-3-1-3 0-4 0l-2 2h-1c-1 0-1-1-2-1-1 1-3 2-3 3l-1 1-1-1-1-1-1 1c0-1 0 0 1-1h0c0-1 0-1-1-2l1-2h0l1-1h0c0-2 3-4 5-5v1c1-1 1-1 2-1 1-1 2-1 2-3 9-3 21-4 31-5z" class="L"></path><path d="M809 465c0-1 0-1 1-1h0c0 1-1 2 0 4l1 1 2 2h-2 0l-2-2h0c-1-1 0-3 0-4z" class="W"></path><path d="M805 471l2-1h0c1 1 3 1 4 1h2 1l1 1c-1 1-2 2-4 2-1-1-2-1-3-1-1-1-1-1-2-1h-1v-1z" class="G"></path><path d="M800 466h0l1 1 2 2 1-1v1c0 1 0 1 1 2v1h-2l-1-1-2 1c-2 0-2 0-3-1-1 0-1-1-2-2v-1h2c1-1 2-2 3-2z" class="d"></path><path d="M795 469v-1h2 0c1 0 2 1 2 1l1 1h0v2c-2 0-2 0-3-1-1 0-1-1-2-2z" class="U"></path><path d="M810 464l2 1-1 1h3c1 1 1 1 2 1v-1l4 1 1 1v1 1c-2 0-4 1-6 2h0l-1-1h-1l-2-2-1-1c-1-2 0-3 0-4z" class="f"></path><path d="M820 467l1 1v1 1c-2 0-4 1-6 2h0l-1-1h0c1-1 2-2 3-2h1l2-2z" class="d"></path><path d="M785 458c3-2 5-1 8-2h1c1-1 3-1 4-1 1-1 2-1 4-1h1 1v1c1 0 2 1 3 2h1l1 1-1 1c-1 1-2 2-3 2v-1c-1-2-2 0-3-1 1-1 0-1 1-2-1 0-1-1-1-1-1 0-2 1-2 1l-2-1c-1 0-1 1-1 1h-3l-1 1c-1 0-2-1-2-1l-1 1h-5z" class="G"></path><path d="M804 455c1 0 2 1 3 2h1l1 1-1 1c-1 0-1-1-2-1h-1l-1-3z" class="M"></path><path d="M809 458h1c1 0 1 1 2 1 1 1 2 1 4 1v1c0 1 0 1-1 2 0 1-1 1 0 2h2c0 1 0 1-1 1v1c-1 0-1 0-2-1h-3l1-1-2-1h0c-1 0-1 0-1 1l-1-1h-1c-1 0-2-1-3-1l1-2c1 0 2-1 3-2l1-1z" class="d"></path><path d="M753 463c4-1 8-2 12-2l9-1h5c1-1 1 0 2 0h1l1-2 1 1c1 0 1 0 2-1v1l2-1 1 1c1-1 1-1 2-1h3v-1l2 1h1 2c1-1 1-1 1 0l1 1h-1c-1 1-1 1-2 1l1 1h-3l-1-2-1 1-1 1h0c-2-1-4-1-6-1v1l-1 1-1-1s-1-1-2-1h0c-3 1-7 1-9 1l-11 1c-4 0-6 1-10 1z" class="G"></path><path d="M730 474h1c1-1 1-2 2-3 1-2 3-1 5-2 0-1 1-2 2-3s2-1 4-1c-1 1-2 1-3 2 0 1 0 1-1 2h-1c0 1 0 1 1 2l-3 2c1 0 1 0 2 1l-2 2h-1c-1 0-1-1-2-1-1 1-3 2-3 3l-1 1-1-1-1-1-1 1c0-1 0 0 1-1h0c0-1 0-1-1-2l1-2 2 1z" class="Z"></path><path d="M728 473l2 1-2 1h1v1c1 0 1 1 2 2l-1 1-1-1-1-1-1 1c0-1 0 0 1-1h0c0-1 0-1-1-2l1-2z" class="d"></path><path d="M753 463c4 0 6-1 10-1l11-1c2 0 6 0 9-1h0c1 0 2 1 2 1l1 1 1-1v-1c2 0 4 0 6 1h0l1-1 1-1 1 2h3l-1 2h-3-1c-2 0-5-1-7 0-1 0-2 0-3-1h0c-1 1-2 0-3 0-7 0-17 0-24 1-1 0-1 1-2 1-2-1-3 1-5 0 0-1 2-1 3-1z" class="U"></path><path d="M769 459c3-1 6-1 9-2 9-2 17-3 25-6 2 1 2 0 4 0l2 2h0 3v3h-1c-1 0-2 0-3 1h-1c-1-1-2-2-3-2v-1h-1-1c-2 0-3 0-4 1-1 0-3 0-4 1h-1c-3 1-5 0-8 2-5 0-11 1-16 1z" class="W"></path><path d="M809 453h3v3h-1c-1-1-1-2-2-3z" class="F"></path><path d="M750 464c2 1 3-1 5 0 1 0 1-1 2-1 7-1 17-1 24-1 1 0 2 1 3 0h0c1 1 2 1 3 1 2-1 5 0 7 0h1 3c0 1 0 2 2 3h0c-1 0-2 1-3 2h-2v1h-1 0c-2 0-3-1-5-2h0c1-2 0-2 0-3h-1l-1 1v1l-1-1c-1 1-1 1-2 0h-1c-1 1-4 0-5 0h-4c-2-1-3-1-5-1-4 1-9 0-13 1-1-1-3 0-5 0-1 0-1 1-2 1h-1c0 1 1 2 1 3h0l-3-2-3 1-1-1v3h0c0 1 1 3 1 4-3-1-3 0-4 0-1-1-1-1-2-1l3-2c-1-1-1-1-1-2h1c1-1 1-1 1-2 1-1 2-1 3-2l1 1v-1l1-1h4z" class="Q"></path><path d="M815 433c2 1 3 1 5 0l1 2c0 1 1 2 1 3v1h1c1-1 1-2 2-2h1l-1 1v1c-5 6-11 9-19 11-1 0-2 0-3 1-8 3-16 4-25 6-3 1-6 1-9 2-10 1-22 2-31 5 0 2-1 2-2 3-1 0-1 0-2 1v-1c-2 1-5 3-5 5h0l-1 1h-3-1v-2h0l-1-2c2-3 5-3 7-4l1-1c-3 0-4 0-7 2h-1c0-1 1-3 2-4 2 0 3-1 6-1 1 0 1-1 2-1s2-2 3-3h2c1-1 1-2 2-3v1c2 0 2-1 4-1h1l1-2 1-2c2 0 4-1 6-2l2-1c1 0 2 0 3-1h0 6 6c1-1 1-1 2 0v-1c1 0 2-1 3-1h1v-1h3c3-1 7-2 11-3 2 0 5 0 7-1 1-1 3 0 4-1s2-1 3-1c1-1 3-1 4-2l3-1c1 0 3 0 4-1z" class="R"></path><path d="M821 435c0 1 1 2 1 3v1h-1 0l-3-3 3-1z" class="Q"></path><path d="M815 433c2 1 3 1 5 0l1 2-3 1-1-1h-1-2-1c-1 1-3 1-4 1h-1c-1 1-1 2-2 2h-2v-1c1-1 3-1 4-2l3-1c1 0 3 0 4-1z" class="U"></path><path d="M798 445c2-1 4-3 6-3l3-3c2 0 4-2 5-2l1 1c1 0 2-1 4-1v2h-2c-1 0-2 1-3 1-2 0-3 0-5 1h0l-1 1-1 1c-1 1-2 1-3 1s-2 1-4 1z" class="I"></path><path d="M798 445c2 0 3-1 4-1s2 0 3-1l1-1 1-1h0c2-1 3-1 5-1 1 0 2-1 3-1h2c1 2 1 2 0 4-6 3-13 5-19 6-4 1-7 2-11 2-1 1-2 1-3 0 1 0 1-1 3-1 1 0 2-1 3-2 3-1 6-1 8-3z" class="S"></path><path d="M748 455h0c1 0 3-1 5-1s4 0 6-1h1l1-1c1 0 3 1 4 0l1-1h1c1-1 9 1 12 0h1s1 0 2-1c1 0 2 0 3-1h0c2 0 3-1 5-1-1 1-2 2-3 2-2 0-2 1-3 1 1 1 2 1 3 0l-7 2-22 4-8 1-1-1c-1 0-2 1-2 1-3 0-5 0-8 1h-1l1-1s1 0 1-1c1 0 2-1 3-1s3-1 5-1z" class="I"></path><path d="M758 457c0-2 0-2 1-3 2 0 5 0 7-1l2-1h8 3l1 1-22 4z" class="S"></path><path d="M790 440c2 0 5 0 7-1 1-1 3 0 4-1s2-1 3-1v1c-2 2-3 2-5 2-2 1-3 2-5 3-1 1-3 2-5 3-1 1-3 1-5 2h-1c-1-1-1-1-2-1-2 1-4 2-5 1-4-1-9 1-12 2h-5-1c-1 1-2 1-2 2h-1c-2 0-5 2-7 3h0c-2 0-4 1-5 1s-2 1-3 1c0 1-1 1-1 1l-1 1h1c3-1 5-1 8-1 0 0 1-1 2-1l1 1-8 2c-1 0-2 0-3 1l-1-1c-2 1-4 1-5 2-1 0-2-1-2-1 1 0 1-1 2-1s2-2 3-3h2c1-1 1-2 2-3v1c2 0 2-1 4-1h1l1-2 1-2c2 0 4-1 6-2l2-1c1 0 2 0 3-1h0 6 6c1-1 1-1 2 0v-1c1 0 2-1 3-1h1v-1h3c3-1 7-2 11-3z" class="X"></path><path d="M790 440c2 0 5 0 7-1 1-1 3 0 4-1s2-1 3-1v1c-2 2-3 2-5 2s-3 1-4 1h-1c-2 0-4 2-6 3-2 0-4 0-5 1h-3c-2 0-3 0-3 1h-1c-1 0-2 0-3 1-1-1-1-1-2-1-3 1-5 1-8 1l-1 1c-1 0-2 1-3 1l-2-1c-1 1-1 2-3 2h-1l-2 1h-1c-1 0-2 1-3 2v-1h-1l1-2c2 0 4-1 6-2l2-1c1 0 2 0 3-1h0 6 6c1-1 1-1 2 0v-1c1 0 2-1 3-1h1v-1h3c3-1 7-2 11-3z" class="d"></path><defs><linearGradient id="AQ" x1="775.984" y1="459.119" x2="773.516" y2="446.881" xlink:href="#B"><stop offset="0" stop-color="#0b0a0a"></stop><stop offset="1" stop-color="#48474e"></stop></linearGradient></defs><path fill="url(#AQ)" d="M822 439h1c1-1 1-2 2-2h1l-1 1v1c-5 6-11 9-19 11-1 0-2 0-3 1-8 3-16 4-25 6-3 1-6 1-9 2-10 1-22 2-31 5 0 2-1 2-2 3-1 0-1 0-2 1v-1c-2 1-5 3-5 5h0l-1 1h-3-1v-2h0l-1-2c2-3 5-3 7-4l1-1c-3 0-4 0-7 2h-1c0-1 1-3 2-4 2 0 3-1 6-1 0 0 1 1 2 1 1-1 3-1 5-2l1 1c1-1 2-1 3-1l8-2 8-1 22-4 7-2c4 0 7-1 11-2 6-1 13-3 19-6 2-1 3-3 4-4h1z"></path><path d="M724 471c5-3 9-5 14-7 0 2-1 2-2 3-1 0-1 0-2 1v-1c-2 1-5 3-5 5h0l-1 1h-3-1v-2z" class="M"></path><path d="M731 461s1 1 2 1c1-1 3-1 5-2l1 1c1-1 2-1 3-1-3 2-8 4-11 4s-4 0-7 2h-1c0-1 1-3 2-4 2 0 3-1 6-1z" class="d"></path><defs><linearGradient id="AR" x1="377.705" y1="228.512" x2="307.616" y2="196.114" xlink:href="#B"><stop offset="0" stop-color="#999899"></stop><stop offset="1" stop-color="#ecebe7"></stop></linearGradient></defs><path fill="url(#AR)" d="M312 195l11-15h1c-1 1-1 1-1 2l-1 1-1 2h-1v1c3-3 5-6 8-9h0l-1-1c1-1 2-1 3-1 1-1 2-3 3-3h1v-1c2-1 1 0 2-1 5-5 10-8 16-12 2-1 3-2 4-3 3-1 6-2 8-3 5 0 10-3 15-4 10-3 21-4 31-5h1l6 1h0c-2 0-2 0-3 1h-4 0l-2-1-1 1h1l2 1-2 2 2 1c-6 1-12 3-17 5-3 2-5 3-8 4l-9 7c-3 2-6 5-9 8-1-1-2-1-3-1h-1l-1 1h0c-2-1-3-1-4 0l-1 1h0c-5 4-8 8-11 13h0l-1 1v1l-1 1-3 3c0 1 0 1-1 2v1l-1 1c0 1-1 1-1 2l-2 4c-1 1 0 1-1 2l-1 4c0 1-1 3-1 4h0c0 2-1 2-1 3v1l-1 1c0 1 0 3-1 4 0 1 0 2-1 4 0 1 1-1 0 1v3c-1 1-1 1-1 2v3l-1 1v3c-1 2 0-1-1 2v3c-1 1-1 2-1 3v3c-1 1-1 2-1 3v5c0 2 0 4-1 7h0c0 2 0 4-1 5 2-3 2-7 2-11h0l1 4h0v-1c0-1 1-2 1-3v-1-1-1c1-1 1-1 1-2 1-2 0-1 0-2s1-2 2-2l-3 12-2 27v1c1-2 1-3 2-4h0v-3c1-2 0-4 1-6l-1-1v-5l1 2h1 0c1 1 1 2 1 3-1 1-1 1-1 2v2l-1 1c-1 2 0 5 0 7 1 2 1 3 1 5 0 1 0 2 1 3l2 1v1c-9 1-20 0-29-1h0c-1-1-1-1-2-1h-1 1v-5h0v-1c-1-1-1-2-2-2 0-1-1-1-2-2 0-1-1-2 0-4v-2l-2 1v1c-1-3 0-5 0-8-1-2-1-4-1-6-1-3 0-6-1-9v-12l1-7h0v-2c0-1 0-1 1-2v-3h0v-1c0-1 1-1 1-2v-1-1c0-1 0 1 0-1l1-1v-1-1s1-1 1-2 1-3 1-5l1 1h0c0-2 0-2 1-4l2-2c0-1 1-1 1-2h0l1-1 2-4 6-10z"></path><path d="M361 156h2c1 1 1 1 1 2l-1 1c-1 0-1-1-2-2v-1z" class="G"></path><path d="M334 185c1 1 1 1 1 3v1c-1-1-2-2-3-2h-1c0-1 2-2 3-2z" class="h"></path><path d="M376 165l-2-3h0 1l1-1h1c1-1 3-2 4-3v1c1 0 2 0 3-1h1l-9 7z" class="Q"></path><path d="M334 185c2-2 3-2 6-2 1 2 0 2 0 3-1 2-1 2-3 2h-1l-1 1v-1c0-2 0-2-1-3z" class="D"></path><path d="M380 149h2 0c-2 2-6 3-9 3-2 1-3 2-4 3 1 0 1 0 2-1l1 1 1 1h0l1 2v1c-3 0-3 0-5-1v-1l-2-2c0-1 0-1 1-2 1 0 1 0 2-1l2-1h2c1-1 3-1 4-1l2-1z" class="I"></path><path d="M315 288c-1-3-2-5-2-7s2-3 3-3c2-1 3-2 5-1v1c-1 0-1 1-2 2h1s0 1-1 1c-1 1-2 2-3 4h-1v1 2z" class="B"></path><path d="M408 148l2 1c-6 1-12 3-17 5-3 2-5 3-8 4h-1c-1 1-2 1-3 1v-1c4-2 7-4 11-6h2c1-1 2-2 4-3 3-1-1 1 1 0l1-1v1l-1 1h1c1-1 2-1 3-1h2c1-1 2-1 3-1z" class="U"></path><path d="M296 245s1 0 1 1l3 3-1-1v2 1c0 2 1 3 2 5l1-1v-2 1l2 8v5c0 1 0 0-1 1v-1l-2-3-2-5-1-3v-2c-1-1-1-1-1-2v-2c-1-2-1-1-1-2v-3h0z" class="C"></path><path d="M312 195c2 1 3 2 5 3h2l1-1h1v-2l1 1c-1 0 0 1-1 1 0 1-1 0-1 2-2 6-5 13-7 19l-1-1c-2-1-2-1-3-2s-1-2 0-3v-2l1-1c0-1 0-2-1-2h-1c-1-1-1-2-2-2l6-10z" class="a"></path><path d="M322 270c2-3 2-7 2-11h0l1 4h0v-1c0-1 1-2 1-3v-1-1-1c1-1 1-1 1-2 1-2 0-1 0-2s1-2 2-2l-3 12-2 27c-1 1-1 3-1 4 0 0-1 0-1 1v1c-1 0-1 1-2 0-1 1-2 1-3 0 0-1-1-3-1-4s0 0-1-1v-2-2-1h1c1-2 2-3 3-4 1 0 1-1 1-1h-1c1-1 1-2 2-2v-1-1c1 1 0 1 2 1v-5c-1-1-1-1-1-2z" class="I"></path><path d="M315 288v-2-1h1c1-2 2-3 3-4 1 1 1 1 1 2v1h0c1 1 0 3 0 5l-1 1c2 0 2 0 3 1-1 1-2 1-3 1v1c1 0 1 0 2 1 0 1 0 1-1 1-1 1-2 1-3 0 0-1-1-3-1-4s0 0-1-1v-2z" class="R"></path><path d="M819 301c-5 4-11 6-14 10h1c1-1 2-2 4-3l-4 4-1 1c-3 1-7 5-7 8v1c-1 1-2 1-3 2-2 2-3 4-5 5-1 2-2 3-3 4l-1 2c-3 3-5 7-8 11-1 2-3 4-3 6v1h0l-1 2 4-1 2-1c0-1 2-1 3-2-1 1-1 2-2 3v7c0 1 1 2 0 3 0 2-2 3 0 5l-5 2v1l-5 2-5 3-6 4v1l-3 6c-4 6-6 13-8 19l-3 5c-1 1-1 3-2 4l-2 5v1 1l-5 10-5 12-1 1-3 9-3 7c-1 1-2 3-2 4h1c3-2 4-2 7-2l-1 1c-2 1-5 1-7 4l1 2h0v2h1 3 0l-1 2c1 1 1 1 1 2h0c-1 1-1 0-1 1v2c1-1 1 0 2-1l1 1c-1 1 0 1-1 1-2 0-2 1-3 2h-1c0 1-1 1-2 1v1c-1 1-2 1-2 1l-2 1c-1 0-1 1-1 1-3 0-2 1-3 2s-2 1-2 1l-1 3-10 22-6 15-3 6-1 2-1 3-3 7-6 15-1 1c-1 2-2 4-2 6-2 2-2 5-3 7-1 1-2 3-2 5l-5 11-2 5-2 4-2 6c-2 2-3 5-4 9l-2 4-4 8c0 1-1 2-1 3-1 2-2 3-3 5 0 1-1 2-1 3l-14 33c-1 1-1 2-2 4l-3 6c-2 8-6 15-9 22l-90 234h-1l-2-2c-1-3 2-11 3-15l50-135 11-26 28-66c-1-3 1-5 1-7v-1-1l14-33h2l16-39 9-22c2-4 4-9 5-13 4-6 6-14 9-20l20-49 64-151c3-9 9-18 14-26 4-1 7-7 11-10 1 0 3-1 4-2l2-2c1 0 1-1 2-1v-1c9-6 17-13 27-17z" class="a"></path><path d="M634 654h2l-17 42c-1-3 1-5 1-7v-1-1l14-33z" class="f"></path><path d="M774 355l4-1-3 3c0 2-1 4-3 5l-1 4c1 1 1 1 2 1v1h0c-2 2-2 3-3 6h1l-5 3-6 4c1-3 3-6 4-9 2-4 4-9 6-12 1-2 2-3 4-5z" class="U"></path><path d="M774 355l4-1-3 3c-2 1-3 2-4 3h-1c1-2 2-3 4-5z" class="g"></path><path d="M772 362l-1 4c1 1 1 1 2 1v1h0c-2 2-2 3-3 6h1l-5 3v-1h1l-1-1c1-4 4-9 6-13z" class="Q"></path><path d="M780 353c0-1 2-1 3-2-1 1-1 2-2 3v7c0 1 1 2 0 3 0 2-2 3 0 5l-5 2v1l-5 2h-1c1-3 1-4 3-6h0v-1c-1 0-1 0-2-1l1-4c2-1 3-3 3-5l3-3 2-1z" class="R"></path><path d="M780 353c0-1 2-1 3-2-1 1-1 2-2 3l-2 2c-1 2-1 4-1 6-1 3-2 5-2 7v2 1l-2-1c0-1 0-2 1-3 0-1 1-1 1-2-1-2-1 0-3-1 1-3 3-6 5-8 1-1 2-2 2-4z" class="I"></path><path d="M781 354v7c0 1 1 2 0 3 0 2-2 3 0 5l-5 2v-2c0-2 1-4 2-7 0-2 0-4 1-6l2-2z" class="S"></path><defs><linearGradient id="AS" x1="719.088" y1="476.582" x2="726.095" y2="479.792" xlink:href="#B"><stop offset="0" stop-color="#5f606c"></stop><stop offset="1" stop-color="#727684"></stop></linearGradient></defs><path fill="url(#AS)" d="M723 466h1c3-2 4-2 7-2l-1 1c-2 1-5 1-7 4l1 2h0v2h1 3 0l-1 2c1 1 1 1 1 2h0c-1 1-1 0-1 1v2c1-1 1 0 2-1l1 1c-1 1 0 1-1 1-2 0-2 1-3 2h-1c0 1-1 1-2 1v1c-1 1-2 1-2 1l-2 1c-1 0-1 1-1 1-3 0-2 1-3 2s-2 1-2 1l3-9 7-16z"></path><path d="M723 482l-2-1 1-1c1-1 2-1 3-1v1l1 3h-1c0 1-1 1-2 1v1c-1 1-2 1-2 1l-2 1c-1-1-1 0-1-1 2-2 4-2 5-3v-1z" class="L"></path><path d="M716 482h2 3c1 0-1 0 1 0h1v1c-1 1-3 1-5 3 0 1 0 0 1 1-1 0-1 1-1 1-3 0-2 1-3 2s-2 1-2 1l3-9z" class="W"></path><path d="M819 301c-5 4-11 6-14 10-2 1-2 1-3 2-2 4-5 5-8 7-4 4-9 8-13 12-3 2-3 4-5 7s-4 6-6 8c-1 2-3 3-4 4-1 2-1 3-2 5-1 0-1 0-2 1h0c-1 2-1 2-3 3 3-9 9-18 14-26 4-1 7-7 11-10 1 0 3-1 4-2l2-2c1 0 1-1 2-1v-1c9-6 17-13 27-17z" class="S"></path><defs><linearGradient id="AT" x1="796.442" y1="380.128" x2="822.983" y2="407.816" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#f9f9f9"></stop></linearGradient></defs><path fill="url(#AT)" d="M823 364l2 1c2-1 2-1 4-1 2 1 2 1 3 2h0v2l-1 1h2c2 0 3 0 4 1v1l-1 1c1 1 1 2 2 2s1 0 2 1c2-1 3-2 5-4v1l-1 1c1 0 2 1 3 0h0c1 0 2 0 3-1l14-3c-4 5-8 10-13 13-1 1-2 2-3 2l-2 2c-1 0-1 1-2 1h0c-1 2-2 3-4 4h1l4-1 1 1h-1l-1 1c-1 2-1 3-1 5v1h0c-1 1-1 2-2 3l-1 1v1c-1 1-1 1-2 1s-3 1-4 2h0v2c0 1 0 1-1 2l-4 3-4 3 4 1v1l-1 1h-3l-1 1c-2 0-3 1-4 2 2-1 3-1 4-1l-4 4c-1 1-1 1-2 1-2 1-4 3-5 4h-1c-2 1-4 2-6 2 0 1-1 1-1 2l-1 1-1-1c-1 2-3 2-4 3h-1c-1 0-1 0-2 1-2 1-3 1-5 1l-1 1c-4 1-8 2-11 3h-3v1h-1c-1 0-2 1-3 1v1c-1-1-1-1-2 0h-6-6 0c-1 1-2 1-3 1l-2 1c-2 1-4 2-6 2l-1 2-1 2h-1c-2 0-2 1-4 1v-1c-1 1-1 2-2 3h-2c-1 1-2 3-3 3s-1 1-2 1c-3 0-4 1-6 1l3-7 3-9 1-1 5-12 5-10v-1-1l2-5c1-1 1-3 2-4l3-5c2-6 4-13 8-19l3-6 2 1c2 0 3-2 5-3 1 0 1 1 2 1 0 0 1-1 1-2h3v-1h2c1 0 2-1 4-1h0c1 0 2-1 2-1 3 0 3-1 6 0l1-1c3 0 5 0 8-1h3v-1c2 0 4 0 6-1h1c1-1 2-1 4-2 0 0 1-1 2-1s1 0 2-1c2-1 4-1 6-3l1-1h2z"></path><path d="M805 388h0c2-1 4-2 6-2-2 3-5 4-8 7 0 1-1 1-1 1v1s1 0 1 1v2c-1 0 0 0-1-1s-1-1-2-1c-6 0-11 5-15 6-3 1-5 5-8 6-1 0-1 1-2 1-2 2-4 3-7 3 0 1-1 1-1 1-1 0-2 1-3 2-1 0-3 0-4 1h-1c1-1 2-2 4-3 0 0 1-1 2-1 1-1 0-1 1-1l3-2c1-1 3-3 4-3s2 0 3-1c0-1 1-1 2-2 4-1 6-2 10-4 2-2 4-3 7-4 2-1 3-3 6-3 2-1 3-2 4-4z" class="S"></path><defs><linearGradient id="AU" x1="813.022" y1="389.213" x2="829.09" y2="370.059" xlink:href="#B"><stop offset="0" stop-color="#232121"></stop><stop offset="1" stop-color="#646469"></stop></linearGradient></defs><path fill="url(#AU)" d="M831 369h2c2 0 3 0 4 1v1l-1 1c1 1 1 2 2 2s1 0 2 1l-4 1-25 10h0c-2 0-4 1-6 2h0l-3 1 1-1 5-2v-1c-2 1-3 1-5 1h-1c4-2 8-3 12-4 5-3 9-5 13-9v-1c2-1 3-2 4-3z"></path><path d="M831 369h2c2 0 3 0 4 1v1l-1 1c1 1 1 2 2 2s1 0 2 1l-4 1c-1-2-2-3-3-4l-6 1v-1c2-1 3-2 4-3z" class="L"></path><path d="M802 389l3-1c-1 2-2 3-4 4-3 0-4 2-6 3-3 1-5 2-7 4-4 2-6 3-10 4-1 1-2 1-2 2-1 1-2 1-3 1s-3 2-4 3l-3 2c-1 0 0 0-1 1-1 0-2 1-2 1-2 1-3 2-4 3h-3c-2 1-4 3-6 4l-1-2v-1l1 1h1c1-1 0-2 0-3l1 1c1-1 1-1 1-2h0 1 2l2-1c1-1 3-2 4-3v-1l1-1c1 0 2 0 3-1h1l1-1c1 0 1 1 1 0 1-1 2-2 3-2 1-1 1-1 2-1 1-1 1-1 2-1h0l1-1h2v-1h2v-1c2 0 2-1 3-2l12-5c2-1 5-2 6-3z" class="R"></path><path d="M752 416h4c0-1 1-2 1-2h2c1-1 0-1 2-2l2 1c-2 1-3 2-4 3h-3c-2 1-4 3-6 4l-1-2v-1l1 1h1c1-1 0-2 0-3l1 1z" class="I"></path><path d="M784 397c-1 1-1 2-3 2v1h-2v1h-2l-1 1h0c-1 0-1 0-2 1-1 0-1 0-2 1-1 0-2 1-3 2 0 1 0 0-1 0l-1 1h-1c-1 1-2 1-3 1l-1 1v1c-1 1-3 2-4 3l-2 1h-2-1 0c0 1 0 1-1 2l-1-1c0 1 1 2 0 3h-1l-1-1v1l1 2-2 1v1l3 1-10 5 1-3v-2-1-1l2-5c1-1 1-3 2-4h0c3 0 4-1 6-2 5-3 10-5 16-7l16-6z" class="C"></path><path d="M754 414l1-1c1-1 1-1 1-2h3v-2h3 0v1c-1 1-3 2-4 3l-2 1h-2z" class="Q"></path><path d="M802 386h1c2 0 3 0 5-1v1l-5 2-1 1c-1 1-4 2-6 3l-12 5-16 6c-6 2-11 4-16 7-2 1-3 2-6 2h0l3-5 7-4 11-5c2-1 4-1 6-2 10-2 20-7 29-10z" class="Y"></path><path d="M837 392c2-2 5-4 7-5h0c-1 2-2 3-4 4h1l4-1 1 1h-1l-1 1c-1 2-1 3-1 5v1h0c-1 1-1 2-2 3l-1 1v1c-1 1-1 1-2 1s-3 1-4 2h0c-2 1-5 1-8 0l-1 1-1-1c0 1-1 1-1 1-1 0-2 1-2 1-4 0-7 1-10 1l-24 6-21 7c-10 3-19 6-28 12l-1-1 5-10v2l-1 3 10-5-3-1v-1l2-1c2-1 4-3 6-4l2 1v1h0c1 1 2 1 2 2 26-9 52-16 77-28z" class="c"></path><path d="M750 420c2-1 4-3 6-4l2 1v1h0c1 1 2 1 2 2l-9 3-3-1v-1l2-1z" class="S"></path><path d="M837 392c2-2 5-4 7-5h0c-1 2-2 3-4 4h1l4-1 1 1h-1l-1 1c-1 2-1 3-1 5v1h0c-1 1-1 2-2 3l-1 1v1c-1 1-1 1-2 1s-3 1-4 2h0c-2 1-5 1-8 0l-1 1-1-1c0 1-1 1-1 1-1 0-2 1-2 1-4 0-7 1-10 1 7-3 15-5 21-10 3-2 5-4 8-6v-1h-1-1-1z" class="L"></path><path d="M845 391l-1 1c-1 2-1 3-1 5v1h0c-1 1-1 2-2 3l-1 1v1c-1 1-1 1-2 1s-3 1-4 2h0c-2 1-5 1-8 0h2c0-1 0-1 1-1h2l4-2h1l1-1c3-2 2-4 4-7 1-1 2-3 4-4z" class="U"></path><path d="M823 364l2 1c2-1 2-1 4-1 2 1 2 1 3 2h0v2l-1 1c-1 1-2 2-4 3v1c-4 4-8 6-13 9-4 1-8 2-12 4-9 3-19 8-29 10-2 1-4 1-6 2l-11 5-7 4c2-6 4-13 8-19l3-6 2 1c2 0 3-2 5-3 1 0 1 1 2 1 0 0 1-1 1-2h3v-1h2c1 0 2-1 4-1h0c1 0 2-1 2-1 3 0 3-1 6 0l1-1c3 0 5 0 8-1h3v-1c2 0 4 0 6-1h1c1-1 2-1 4-2 0 0 1-1 2-1s1 0 2-1c2-1 4-1 6-3l1-1h2z" class="I"></path><path d="M779 385v-2h1 1v-1h1 2 1c0 1 0 1-1 1 0 1-3 2-3 2v2c-1 0-2 0-3 1l-3-1 1-1 2 1v-1l1-1h1-1z" class="R"></path><path d="M784 388c1-1 1 0 1-1 1 0 1-2 2-2h1v-1h-2l1-1 1-1c1 1 1 1 3 1h0l1 1-2 2c-1 1-5 1-6 2z" class="S"></path><path d="M767 388h1c2-1 3-1 5-2 1 0 1 0 1-1h0l2-2h1c0 1 1 1 1 2h1 1-1l-1 1v1l-2-1-1 1c-1 2-2 3-4 3h0v1h0c0 1 0 1-1 1l-3 3v3l-11 5-1-1c0-1 7-3 8-4l-1-1h0l2-3c1 0 2 0 2-1l-1-2c0-1 0-1 1-2l1 1v-2z" class="X"></path><defs><linearGradient id="AV" x1="752.062" y1="395.458" x2="760.734" y2="396.766" xlink:href="#B"><stop offset="0" stop-color="#71717a"></stop><stop offset="1" stop-color="#868996"></stop></linearGradient></defs><path fill="url(#AV)" d="M749 407c2-6 4-13 8-19 1 1 1 1 2 1l1 1h0 2 1c1-1 2-1 3-1-1 1-1 1-1 2l1 2c0 1-1 1-2 1l-2 3h0l1 1c-1 1-8 3-8 4l1 1-7 4z"></path><path d="M760 390h2v1l1 2c-1 0-2 0-2 1-2 0-1 0-3-1l2-2v-1z" class="U"></path><path d="M763 390c1-1 2-1 3-1-1 1-1 1-1 2l1 2c0 1-1 1-2 1h-1-2c0-1 1-1 2-1l-1-2v-1h1z" class="C"></path><path d="M805 372l-2 2h0c-2 0-3 1-4 2s-2 1-4 1c0 1-1 1-1 1l-1 1h-1-2l-1 1s-3 1-4 1h-1c0 1 0 1 1 1h-1-2-1v1h-1-1v2h-1c0-1-1-1-1-2h-1l-2 2h0c0 1 0 1-1 1-2 1-3 1-5 2h-1v2l-1-1c-1 0-2 0-3 1h-1-2 0l-1-1c-1 0-1 0-2-1l3-6 2 1c2 0 3-2 5-3 1 0 1 1 2 1 0 0 1-1 1-2h3v-1h2c1 0 2-1 4-1h0c1 0 2-1 2-1 3 0 3-1 6 0l1-1c3 0 5 0 8-1h3v-1c2 0 4 0 6-1z" class="W"></path><path d="M769 383c1 1 1 1 3 1h-1c0 1-1 1-2 1h0c0 1 0 1-1 1h-1v2 2l-1-1c-1 0-2 0-3 1h-1-2 0l-1-1 1-2c2-2 6-3 9-4z" class="b"></path><path d="M760 387l4 1-2 1h1v1h-1-2 0l-1-1 1-2z" class="d"></path><path d="M760 387c2-2 6-3 9-4l-1 2c-1 0-1 0-2 1-1 0-2 1-2 2l-4-1z" class="G"></path><path d="M805 372l-2 2h0c-2 0-3 1-4 2s-2 1-4 1c0 1-1 1-1 1l-1 1h-1-2l-1 1s-3 1-4 1h-1c0 1 0 1 1 1h-1-2-1v1h-1-1v2h-1c0-1-1-1-1-2h-1l-2 2h0c0 1 0 1-1 1-2 1-3 1-5 2h-1v-2h1c1 0 1 0 1-1h0c1 0 2 0 2-1h1v-1l1-1 1 1c1-1 2-1 3-2h0l2 1c1-1 0-1 0-3l2 1 2-1c1 0 1-1 2-1s3-1 4 0c1 0 2-1 3-1 1-1 2-1 4-1 1 0 2-2 3-2v-1c2 0 4 0 6-1z" class="C"></path><path d="M823 364l2 1c2-1 2-1 4-1 2 1 2 1 3 2h0v2l-1 1c-1 1-2 2-4 3v1c-4 4-8 6-13 9-4 1-8 2-12 4-9 3-19 8-29 10v-1c-1 0-2 0-3 1h0-2v-1c1-1 1-1 2-1v1c2-1 3-1 4-3 2-2 7-4 10-4 1-1 5-1 6-2l2-2-1-1c1-1 1-1 0-3 1 1 3 0 4 0l2-2c1 0 0-1 2-1l1 1v1l1-1v-1-1h-1c1-1 2-1 2-1 1-1 1 0 2-1 1 0 1-1 1-1h2l-1-1c1-1 2-1 4-2 0 0 1-1 2-1s1 0 2-1c2-1 4-1 6-3l1-1h2z" class="D"></path><path d="M812 374c1 0 2-1 3-2l1 2 1 2c-2 1-3 1-5 1v-3z" class="a"></path><path d="M800 379h1c2-1 3-2 4-3h1c1 1 2 1 4 1h1v1c-1 0-2 1-3 1-2 0-3 1-5 2v1c-1-1-1-1-2-1h0c-2-1-2-1-2-2l1-1v1z" class="j"></path><path d="M823 364l2 1c2-1 2-1 4-1 2 1 2 1 3 2h0v2l-1 1c-1 1-2 2-4 3 0-1 0-1-1-1s-1 0-2-1h0l-2 1h-1c0 1 0 1-1 1v-2c-2 0-3 0-4-1-1 1-1 1-1 3l1 1v1l-1-2c-1 1-2 2-3 2h-3c0 1-1 1-1 1h-1l-1-1-1 1-4 1v1-1h-1c1-1 2-1 2-1 1-1 1 0 2-1 1 0 1-1 1-1h2l-1-1c1-1 2-1 4-2 0 0 1-1 2-1s1 0 2-1c2-1 4-1 6-3l1-1h2z" class="S"></path><path d="M823 364l2 1c2-1 2-1 4-1 2 1 2 1 3 2h0-3c-1 1-2 1-3 1-2-1-3-1-4 0l-2-2 1-1h2z" class="I"></path><path d="M821 408s1-1 2-1c0 0 1 0 1-1l1 1 1-1c3 1 6 1 8 0v2c0 1 0 1-1 2l-4 3-4 3 4 1v1l-1 1h-3l-1 1c-2 0-3 1-4 2 2-1 3-1 4-1l-4 4c-1 1-1 1-2 1-2 1-4 3-5 4h-1c-2 1-4 2-6 2 0 1-1 1-1 2l-1 1-1-1c-1 2-3 2-4 3h-1c-1 0-1 0-2 1-2 1-3 1-5 1l-1 1c-4 1-8 2-11 3h-3v1h-1c-1 0-2 1-3 1v1c-1-1-1-1-2 0h-6-6 0c-1 1-2 1-3 1l-2 1c-2 1-4 2-6 2l-1 2-1 2h-1c-2 0-2 1-4 1v-1c-1 1-1 2-2 3h-2c-1 1-2 3-3 3s-1 1-2 1c-3 0-4 1-6 1l3-7 3-9 1-1 5-12 1 1c9-6 18-9 28-12l21-7 24-6c3 0 6-1 10-1z" class="D"></path><path d="M813 423l12-7 4 1v1l-1 1h-3l-1 1c-2 0-3 1-4 2s-3 1-5 1h-2z" class="O"></path><path d="M811 409c3 0 6-1 10-1-3 1-5 2-8 3s-6 2-9 2c-2 0-3 1-4 1-2 0-4 1-5 1-11 3-22 5-32 11-2 1-4 3-5 4-2 2-4 2-6 2v3h-2v1h0c1-1 2-1 3-1 0-1 1-1 1-2h1v3c-2 0-3 0-4 1-2 0-4 1-5 1-1 1-2 1-3 1-1 2-2 3-4 4-1 0-1 0-1 1l-5 3-2-1 1-1 5-12 1 1c9-6 18-9 28-12l21-7 24-6z" class="C"></path><path d="M737 433l1 1v2h0c1 1 2 2 2 3s-1 1-2 2h-1c-1 0-1 1-2 2 0 1-2 2-3 2l5-12z" class="d"></path><path d="M820 422c2-1 3-1 4-1l-4 4c-1 1-1 1-2 1-2 1-4 3-5 4h-1c-2 1-4 2-6 2 0 1-1 1-1 2l-1 1-1-1c-1 2-3 2-4 3h-1c-1 0-1 0-2 1-2 1-3 1-5 1l-1 1c-4 1-8 2-11 3h-3v1h-1c-1 0-2 1-3 1v1c-1-1-1-1-2 0h-6-6 0c-1 1-2 1-3 1l-2 1c-2 1-4 2-6 2l-1 2-1 2h-1c-2 0-2 1-4 1v-1c-1 1-1 2-2 3h-2c-1 1-2 3-3 3s-1 1-2 1c-3 0-4 1-6 1l3-7 3-9 2 1 5-3 6-2 69-19h2c2 0 4 0 5-1z" class="c"></path><path d="M746 445c4-1 7-2 11-3 5 0 10 2 14 0 2 0 4 0 6-1h0 2c1 0 1-1 2-1 1-1 3 0 5-1h1 0l1-1h2c2-1 6-1 8-1-1 0-1 0-2 1-2 1-3 1-5 1l-1 1c-4 1-8 2-11 3h-3v1h-1c-2 0-3 0-4 1h-1c-1-1-1 0-2 0-2 0-4 0-5-1h-3-2c-1 0-1 1-1 1-1 0-2-1-2-1l-2 1h-4l-1 2h-1v1h-1v-1l-1 1h-2c0 2 0 2-2 2l-1-1h1c0-1 0-2 2-3 1 0 2-1 3-1z" class="B"></path><path d="M738 444l6-2-1 1-3 3 1 1c1-1 1-2 2-2h1 2c-1 0-2 1-3 1-2 1-2 2-2 3h-1l1 1c2 0 2 0 2-2h2l1-1v1h1v-1h1l1-2h4l2-1s1 1 2 1c0 0 0-1 1-1h2 3c1 1 3 1 5 1 1 0 1-1 2 0h1c1-1 2-1 4-1-1 0-2 1-3 1v1c-1-1-1-1-2 0h-6-6 0c-1 1-2 1-3 1l-2 1c-2 1-4 2-6 2l-1 2-1 2h-1c-2 0-2 1-4 1v-1c-1 1-1 2-2 3h-2c-1 1-2 3-3 3s-1 1-2 1c-3 0-4 1-6 1l3-7 3-9 2 1 5-3z" class="G"></path><path d="M741 450c2 0 2 0 2-2h2v1c0 1 0 1 1 2-1 0-2 1-4 1l-1-2z" class="J"></path><path d="M738 444l6-2-1 1-3 3 1 1c1-1 1-2 2-2h1 2c-1 0-2 1-3 1-2 1-2 2-2 3h-1v1s-1 1-1 2h-2l-2 2c-1 0-2 0-2 1h-2v1l2 1h0-1-2c-1 1-1 1-1 3h-1c0-3 2-3 0-5l3-9 2 1 5-3z" class="F"></path><path d="M732 448c2 0 4 0 6 1h0l-1 1v2c-3-1-2 1-4 1h-2v-1c0-2 0-3 1-4z" class="E"></path><path d="M738 444l6-2-1 1-3 3 1 1c1-1 1-2 2-2h1 2c-1 0-2 1-3 1-2 1-2 2-2 3l-2-1v2h-2l1-1h0c-2-1-4-1-6-1l1-1 5-3z" class="H"></path><path d="M205 320c1 0 2 1 4 2h1c8 8 15 17 21 26 12 15 23 31 33 48 13 23 24 48 34 73h-1l-2-2c0-1-1 0-2 0l-1 1c-1-2-1-3-3-4l-4-2h-4-3-2c-3-1-7-1-11-2l-11-1h-5c-3 0-7 0-9-1s-2-1-3-1c-2 0-5-2-7-1-1 1-1 1-2 1h0-3-1l-2 1c1-2 1-2 1-4h0v-1c1 0 1 0 1 1h1l1-1c-2-1-3-2-5-3h-4c-7-2-13-6-18-11 0-1-2-3-3-4-4-6-8-13-12-20 2 1 5 1 7 2 1 1 2 1 3 1l1-1c1-1 2-1 3-1l-4-3-3-3v1 2l-6-3v-2c0-1 1-2 1-3-1 0-2 0-2-1-2-1-3-2-3-4h-1v-1l1-1c-1-2-2-4-2-7h-1-1v-1l8 1c-2-1-6-3-7-5l-1-1c-1-1-2-1-3-2-4-3-8-8-11-12 0 0-1-1-1-2 3 1 6 2 10 2 0 0 1 0 2 1l2 1h3c1 1 1 1 2 1l-2-2 1-1c2 2 3 3 5 4v-2l-1-3h1l-1-1 1-1 2 1c0-2 2-3 3-4h0l1 1c1 0 0 0 1 1 3-4 4-2 7-3 1 1 2 1 3 1h1l1 1c1 1 1 1 2 1l1 1 1-1v2h1c1-1 1-1 1 0h4 0c2 1 5 2 7 3l9 3c2 1 3 1 5 1 1 0 1 0 2 1h0l2-1c2 1 5 2 7 3l1 1v-1c-2-4-5-8-8-11-1-3-4-7-5-9-7-10-15-20-23-28h0l2 1v-1l-8-11z" class="f"></path><path d="M277 455l5 2c3 0 5 1 6 3 1 1 1 3 1 4l-4-2-7-3c1 0 1 0 1-2l-2-2z" class="C"></path><path d="M231 396c1 0 3 0 4 1 2 2 6 2 9 4 7 2 13 5 20 8 2 3 7 10 7 14h0l-9-3 1-1h1v-1l-3-3c-1-1-1-4-2-5s-4-2-6-2c-3-2-6-3-8-5l-9-4c-2-1-3-2-5-3z" class="I"></path><path d="M225 387l34 13 1 1c2 2 3 4 4 7l-34-15-5-3c1 0 2 1 4 1h1l2 1v-1l-3-1-4-2v-1z" class="c"></path><defs><linearGradient id="AW" x1="232.36" y1="381.302" x2="226.576" y2="390.604" xlink:href="#B"><stop offset="0" stop-color="#c6c5c6"></stop><stop offset="1" stop-color="#ececeb"></stop></linearGradient></defs><path fill="url(#AW)" d="M192 367c3-4 4-2 7-3 1 1 2 1 3 1h1l1 1c1 1 1 1 2 1l1 1 1-1v2h1l3 1v1h1 1c2 0 3 1 4 1 0 0 1 1 2 1h1s1 1 1 2l1-1c1 0 1 1 2 1h1c1 0 0 1 1 1s2 1 3 1c2 0 1 1 3 1 1 0 0-1 1 0 2 1 3 0 4 0l1 1c1 0 1 0 2 1 1 0 2 1 3 2l1 1v-1l1 1c1 0 2 0 3 1s2 1 3 3l2 2v1c1 2 3 4 3 6 1 1 2 3 2 4l-34-13c-1-1-4-1-5-2-4-1-8-3-11-4l-9-6-5-5-1-1-2-2z"></path><path d="M254 389v1c1 2 3 4 3 6-3 0-5-3-7-4l1-1c0-1 1-1 3-2z" class="Q"></path><path d="M254 389v1l-1 2c-1 0-1 0-2-1 0-1 1-1 3-2zm-62-22c3-4 4-2 7-3 1 1 2 1 3 1h1l1 1c1 1 1 1 2 1l1 1 1-1v2h1l3 1v1h1 1l2 2c1 1 1 1 3 1l1 1c-1 2-2 2-3 4l-1-1h0c-1 1-1 1-2 1v-2l-1 1h-4 0c1-2 2-1 3-3v-1c-2 1-2 1-3 0h-1c1-2 1-2 1-3h-1l-1 1v1 1h-1v-1c-1 1-1 0-1 1-1-1-1-1-1-2h-1l-1 1c0 1 1 3 2 4 1 0 1 0 2 1 1 0 2 1 3 3l-9-6-5-5-1-1-2-2z" class="D"></path><path d="M192 367c3-4 4-2 7-3 1 1 2 1 3 1h1l1 1v1c-1 1-1 1-1 2h-1c-2-1-2-1-4-1h-1l-2 2-1-1-2-2z" class="S"></path><path d="M214 371c2 0 3 1 4 1 0 0 1 1 2 1h1s1 1 1 2l1-1c1 0 1 1 2 1h1c1 0 0 1 1 1s2 1 3 1c2 0 1 1 3 1 1 0 0-1 1 0 2 1 3 0 4 0l1 1c1 0 1 0 2 1 1 0 2 1 3 2l1 1v-1l1 1c1 0 2 0 3 1s2 1 3 3l2 2c-2 1-3 1-3 2l-1 1-2-1c-1-1-2-1-4-2h-3c-2 0-2-1-4-1-2-1-2 0-4-1h-2c0-1-1-1-1-2h0c-3 0-4-1-7-2-1-1-2-1-3-2 0 0-2-1-3-1-1-1-1 0-3-1 1 0 1 0 2-1h0l1 1c1-2 2-2 3-4l-1-1c-2 0-2 0-3-1l-2-2z" class="R"></path><path d="M229 380l2-1 1 1c1 1 2 1 3 1s3 1 5 1c0 1 1 1 2 2l1-1c0 2 0 2-1 4-1-1-2-1-3-2l-3-3-2 1c0-1-1-1-1-2l-2 1c0-1 0-1-1-2h-1z" class="I"></path><path d="M246 383c1 0 2 0 3 1s2 1 3 3l2 2c-2 1-3 1-3 2l-1 1c0-1 0-2-1-2v-1c1-1 1-1 1-2-2 0-2 0-3 1h0c0-1 1-2 1-2-1-1-1-1-2-1v-2z" class="X"></path><path d="M229 380h1c1 1 1 1 1 2l2-1c0 1 1 1 1 2l2-1 3 3c1 1 2 1 3 2l2 2h-3c-1 0-3-1-3-1-1-1 0-2-1-3l-2 2-3-2 1-1v-1h-1-2l-1 1-1-1s0-1-1-1l2-2z" class="S"></path><path d="M219 377c2-1 3-1 5-1v1h1c1 1 2 1 3 1 0 2-1 2-1 3l-1 1h1c1 0 1 1 1 1l1 1 1-1h2 1v1l-1 1 3 2 2-2c1 1 0 2 1 3 0 0 2 1 3 1-2 0-2-1-4-1-2-1-2 0-4-1h-2c0-1-1-1-1-2h0c-3 0-4-1-7-2-1-1-2-1-3-2 0 0-2-1-3-1-1-1-1 0-3-1 1 0 1 0 2-1h0l1 1c1-1 2-1 2-2z" class="j"></path><path d="M219 377c2-1 3-1 5-1v1h1c1 1 2 1 3 1 0 2-1 2-1 3l-1-1c-2 0-2 1-4 2l-1-1v-1c1-1 2-1 2-3h-4z" class="D"></path><defs><linearGradient id="AX" x1="228.816" y1="379.715" x2="199.397" y2="408.114" xlink:href="#B"><stop offset="0" stop-color="#cac9c9"></stop><stop offset="1" stop-color="#f3f3f3"></stop></linearGradient></defs><path fill="url(#AX)" d="M190 365h0l1 1c1 0 0 0 1 1l2 2 1 1 5 5 9 6c3 1 7 3 11 4 1 1 4 1 5 2v1l4 2 3 1v1l-2-1h-1c-2 0-3-1-4-1l5 3c1 1 2 2 3 2l2 2c-1-1-3-1-4-1 2 1 3 2 5 3l9 4c2 2 5 3 8 5 2 0 5 1 6 2s1 4 2 5l3 3v1h-1l-1 1c-25-10-53-16-76-30-1-1-7-4-9-5-1-1-2-1-3-2-4-3-8-8-11-12 0 0-1-1-1-2 3 1 6 2 10 2 0 0 1 0 2 1l2 1h3c1 1 1 1 2 1l-2-2 1-1c2 2 3 3 5 4v-2l-1-3h1l-1-1 1-1 2 1c0-2 2-3 3-4z"></path><path d="M209 384l5 2 8 3c0 1 2 1 3 1l5 3c1 1 2 2 3 2l2 2c-1-1-3-1-4-1 2 1 3 2 5 3l9 4c-3 0-5-1-8-2h-1l-1-1c-1 0-1 0-2-1l-1-1c-2-1-5-3-7-2-1 0-2 1-2 2l-1 1c-1 0-1 0-3-1v-1c1-1 1-1 1-2v-1c-3-1-8-3-9-6 0-1-1-2-2-4h0z" class="h"></path><path d="M214 386l8 3c0 1 2 1 3 1l5 3c1 1 2 2 3 2l2 2c-1-1-3-1-4-1-2-1-4-2-5-3-1 1-2 1-3 1h0l-6-4c0-1-2-3-3-4z" class="D"></path><path d="M190 365h0l1 1c1 0 0 0 1 1l2 2 1 1 5 5 9 6c3 1 7 3 11 4 1 1 4 1 5 2v1l4 2 3 1v1l-2-1h-1c-2 0-3-1-4-1s-3 0-3-1l-8-3-5-2c-1-2-8-4-10-5l-7-2-7-2v-2l-1-3h1l-1-1 1-1 2 1c0-2 2-3 3-4z" class="O"></path><path d="M194 369l1 1 5 5c-2 0-6-2-8-3 0-1-2-2-2-2 1 0 2 0 4-1z" class="F"></path><path d="M192 377c-1-2-2-2-3-3-2 0-1 0-2-1l2-3h1s2 1 2 2c-1 1-1 0-1 1 2 1 2 2 4 2 1 1 3 2 4 4h0l-7-2z" class="B"></path><path d="M190 365h0l1 1c1 0 0 0 1 1l2 2c-2 1-3 1-4 1h-1l-2 3c1 1 0 1 2 1 1 1 2 1 3 3l-7-2v-2l-1-3h1l-1-1 1-1 2 1c0-2 2-3 3-4z" class="M"></path><path d="M177 385c2 1 8 4 9 5 23 14 51 20 76 30l9 3c1 1 3 4 3 6h0c1 2 2 3 2 4 1 2 2 3 2 5l2 4-6-1c-21-7-43-10-64-18-4-2-8-4-12-7l-4-3-3-3v1 2l-6-3v-2c0-1 1-2 1-3-1 0-2 0-2-1-2-1-3-2-3-4h-1v-1l1-1c-1-2-2-4-2-7h-1-1v-1l8 1c-2-1-6-3-7-5l-1-1z" class="D"></path><path d="M261 426c2 0 2 0 4 1 1 1 4 1 5 3 1 0 1 1 2 2 1 0 3 1 4 1 1 2 2 3 2 5l-4-1-1-1c-2-1-3-2-4-4s-6-5-8-6z" class="b"></path><path d="M191 410c-1-1-1-2-1-3s1-1 2-2h0c6 3 14 4 21 6 12 4 26 4 37 10h0c-1 1-3 0-4-1l-12-3c-2 0-3-1-5-1-5 0-11-1-16-2-2-1-5-2-7-2s-3-2-5-2c-2-1-2-1-4 0-2-1-3-2-5-1 1 1 2 2 2 3v1l-3-3z" class="I"></path><path d="M213 409l24 6c8 2 16 5 24 8 5 2 9 3 13 6 1 2 2 3 2 4-1 0-3-1-4-1-1-1-1-2-2-2-1-2-4-2-5-3-2-1-2-1-4-1l-11-5h0c-11-6-25-6-37-10l-2-2h2z" class="Z"></path><path d="M177 385c2 1 8 4 9 5 23 14 51 20 76 30l9 3c1 1 3 4 3 6h0c-4-3-8-4-13-6-8-3-16-6-24-8l-24-6h-2l2 2c-7-2-15-3-21-6h0c-1 1-2 1-2 2s0 2 1 3v1 2l-6-3v-2c0-1 1-2 1-3-1 0-2 0-2-1-2-1-3-2-3-4h-1v-1l1-1c-1-2-2-4-2-7h-1-1v-1l8 1c-2-1-6-3-7-5l-1-1z" class="c"></path><path d="M177 385c2 1 8 4 9 5v2h-2c0 1 6 7 7 8 2 2 9 5 12 6s7 1 10 3h-2l2 2c-7-2-15-3-21-6h0c-1 1-2 1-2 2s0 2 1 3v1 2l-6-3v-2c0-1 1-2 1-3-1 0-2 0-2-1-2-1-3-2-3-4h-1v-1l1-1c-1-2-2-4-2-7h-1-1v-1l8 1c-2-1-6-3-7-5l-1-1z" class="L"></path><path d="M179 391c2 1 2 2 3 4v1l1 1 1 1c-1 1-1 1-1 3l-2-1h-1v-1l1-1c-1-2-2-4-2-7z" class="Z"></path><path d="M181 400l2 1c2 1 3 2 5 3 1 0 3 0 4 1 3 0 5 1 7 1l2 1h4l1 1h2 1c1 1 1 1 2 1l2 2c-7-2-15-3-21-6h0c-1 1-2 1-2 2s0 2 1 3v1 2l-6-3v-2c0-1 1-2 1-3-1 0-2 0-2-1-2-1-3-2-3-4z" class="b"></path><path d="M199 439c0-1-2-3-3-4-4-6-8-13-12-20 2 1 5 1 7 2 1 1 2 1 3 1l1-1c1-1 2-1 3-1 4 3 8 5 12 7 21 8 43 11 64 18l6 1 3 7c1 1 1 2 1 3h-1c-1 0-1 1-2 1h0v1l1 1h0v2l-5-2 2 2c0 2 0 2-1 2l7 3h-4-3-2c-3-1-7-1-11-2l-11-1h-5c-3 0-7 0-9-1s-2-1-3-1c-2 0-5-2-7-1-1 1-1 1-2 1h0-3-1l-2 1c1-2 1-2 1-4h0v-1c1 0 1 0 1 1h1l1-1c-2-1-3-2-5-3h-4c-7-2-13-6-18-11z" class="N"></path><path d="M256 445h5 0c2 0 5-1 7 1l1-1h0c2 1 4 2 6 1l-1 1v2c-1 1-1 2-1 3-1-1-1-2-2-3-1 0-1 0-2-1h0c-1-1-2-1-3-1l-1-1c-1 0-2 1-3 1 0 0-1-1-2-1l-2 2-2-3z" class="W"></path><path d="M274 449v-2c2 0 3 1 4 1 1 1 1 1 2 1v1c1 2 1 0 1 2v1 1l1 1h0v2l-5-2h-2c-2-1-2-2-3-2h0l1-1c0-1 0-2 1-3z" class="Z"></path><path d="M274 449v-2c2 0 3 1 4 1 1 1 1 1 2 1v1h-1l-2 1c-1 0-2-2-3-2z" class="G"></path><path d="M195 417c1-1 2-1 3-1 4 3 8 5 12 7h-2c-1 0-1-1-2 0-1 0-1 0-2-1h0v1c6 2 10 8 16 10 6 3 13 3 19 6 5 2 12 2 17 4-3 0-9 0-12-1-5-3-11-3-16-4-2-1-5-2-8-3-5-2-9-4-14-7-2-2-4-3-6-5-2-1-3-2-5-4h2c1 1 2 1 3 1h0c-2-2-3-2-5-3z" class="E"></path><path d="M196 429l-1-3v-1l1 1c2-1 2-1 4-1 0 1 0 2 1 2 2 1 6 3 8 5h2c1 1 2 1 3 2 6 1 11 4 16 5l15 4h3c1 0 2 1 3 1 2 1 3 0 5 1-2 0-2 0-3 1l-1-1h-1 0c-5 0-10-1-14-2s-9-2-12-4l-17-5c-2 2-3 3-4 5h-1c-1-1-2-2-2-3h0l-1-2c-1-1-2-1-2-2s-1-2-1-3h-1z" class="J"></path><path d="M196 429l2-1c2 0 4 0 5 2h0-1l-5-1h-1z" class="f"></path><path d="M197 429l5 1h1c1 1 2 1 3 2 1 0 1 0 2 1s3 1 4 1c4 1 10 3 13 5l-17-5c-2 2-3 3-4 5h-1c-1-1-2-2-2-3h0l-1-2c-1-1-2-1-2-2s-1-2-1-3z" class="G"></path><path d="M202 430h1v2h-2v-1l1-1z" class="L"></path><path d="M200 434h1c1 1 1 1 2 0h2c0 1-1 2-1 2-2 0-2 0-2-1l-1 1h0l-1-2z" class="f"></path><path d="M203 439h1c1-2 2-3 4-5l17 5c3 2 8 3 12 4s9 2 14 2h0 1l1 1c1-1 1-1 3-1l2 3 2-2c1 0 2 1 2 1 1 0 2-1 3-1l1 1c1 0 2 0 3 1h0c1 1 1 1 2 1l-3 1h-1l-2 1c-2 0-2-1-4-1h-1s-1 0-1 1h-1v-2l-1 1v1h-3c-2-1-8-2-10-2-5-1-9-2-13-3-8-3-14-6-22-8-2 1-3 1-4 3h-1v-1l-1-1z" class="b"></path><path d="M261 450c1-1 2-2 3-2h1c0 2 0 1 2 2l-2 1c-2 0-2-1-4-1z" class="C"></path><path d="M203 439h1c1-2 2-3 4-5l17 5c3 2 8 3 12 4s9 2 14 2h0 1l1 1c1-1 1-1 3-1l2 3h0c-1 1-2 1-3 1v1l-1-1c-1 0-2 1-2 1l-2-2h-1 0-2l-1-1h-2c-2-1-3-1-4-1h-1-3l-2-1h0c-1 0-2-1-2-1h-1c-1 0-2-1-3-1l-3-1c-1-1-1-1-2-1h-1l-2-1c-1 0-2-1-3-1h-1l-1-1h-1c-1-1-1-1-2-1h-1c-2 0-3-1-5 1-1 0-1 1-2 2l-1-1z" class="U"></path><path d="M256 443c-5-2-12-2-17-4-6-3-13-3-19-6-6-2-10-8-16-10v-1h0c1 1 1 1 2 1 1-1 1 0 2 0h2c21 8 43 11 64 18l6 1 3 7v1c-2-1-3-2-5-3-2-2-5-2-8-3h-1-1c-2-1-4 0-6-1h-1c-2 1-3 1-5 0z" class="c"></path><path d="M274 441l6 1 3 7v1c-2-1-3-2-5-3-2-2-5-2-8-3h5c1 0 2 1 2 1h1c1 0 1 1 2 1v-1h-1c-3-1-4-1-5-4z" class="P"></path><path d="M205 441c1-2 2-2 4-3 8 2 14 5 22 8 4 1 8 2 13 3 2 0 8 1 10 2h3v-1l1-1v2h1c0-1 1-1 1-1h1c2 0 2 1 4 1l2-1h1l3-1c1 1 1 2 2 3l-1 1h0c1 0 1 1 3 2h2l2 2c0 2 0 2-1 2l7 3h-4-3-2c-3-1-7-1-11-2l-11-1h-5c-3 0-7 0-9-1s-2-1-3-1c-2 0-5-2-7-1-1 1-1 1-2 1h0-3-1l-2 1c1-2 1-2 1-4h0v-1c1 0 1 0 1 1h1l1-1c-2-1-3-2-5-3h-4c-7-2-13-6-18-11 3 0 3 0 5 1v1h1z" class="I"></path><path d="M254 451h3v-1l1-1v2h1c0-1 1-1 1-1h1c2 0 2 1 4 1l2-1h1l3-1c1 1 1 2 2 3l-1 1h0c1 0 1 1 3 2-4 0-6 0-10-2-1-1-1 0-2 0-3-1-7-1-9-2z" class="Q"></path><path d="M271 449c1 1 1 2 2 3l-1 1h0l-1 1c-2-1-2-2-3-4l3-1z" class="C"></path><defs><linearGradient id="AY" x1="242.507" y1="456.406" x2="243.414" y2="449.214" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#333032"></stop></linearGradient></defs><path fill="url(#AY)" d="M199 439c3 0 3 0 5 1v1h1c11 11 56 13 73 18l7 3h-4-3-2c-3-1-7-1-11-2l-11-1h-5c-3 0-7 0-9-1s-2-1-3-1c-2 0-5-2-7-1-1 1-1 1-2 1h0-3-1l-2 1c1-2 1-2 1-4h0v-1c1 0 1 0 1 1h1l1-1c-2-1-3-2-5-3h-4c-7-2-13-6-18-11z"></path><path d="M226 453l28 6h-5c-3 0-7 0-9-1s-2-1-3-1c-2 0-5-2-7-1-1 1-1 1-2 1h0-3-1l-2 1c1-2 1-2 1-4h0v-1c1 0 1 0 1 1h1l1-1z" class="f"></path><path d="M547 493l3-1c1 2 1 4 1 6v1h0c0 2 1 3 1 4 2 2 4 4 4 6h1c2 0 3 1 5 2h2l3 1c4 2 8 5 13 6 1 1 0 3 1 5l-1 5 1 2c1-1 1-2 2-2l2-1h2c-1 1-1 2-2 3v2l1 1 1-1-1-2 1-1 2 1 1-1 1 1h1 1 0v1 3c0 1-1 2-1 3s-2 3-2 4c-2-1-2-3-4-2-1 0-1 0-1-1l-2 2c-1 4-1 8-4 10 0 1-1 2-1 3l-1 1s0 1-1 2l1 1-1 1h-1c-1-1-2 0-3 0h0l-2 2h0l-1-2-9 8c-3 0-5 1-8 2s-6 3-10 4-8 2-13 3c-2-1-3 0-5-2h-3l-1 1h-1-5c-2-2-2-3-4-4l-2 2h-3l-1 1h-1-1-5v1l-1 1-1-1c-2 0-3 1-4 1-3-1-6-3-9-3h-3l-7-5-1 1-4-3c-2-1-5-3-5-5h0l-1-1h-1c-1-1-2-2-3-2h-2-1v-3l-1 1v1h-1l-2-2h-5c-1 2 0 4 0 5-1-1-2-3-2-5-2-3-2-6-3-8l-1-4c-1-2-2-3-2-5l-2-1c0-2-1-3-2-4l-10-21c-1-1-1-3-1-5 1 2 2 3 4 4h2v-1h6c1 0 1 0 2-1l-1-6-1-2c2 1 2 1 4 1 2-1 4-1 6-1l1-1s0 1 1 1h1l1 1h2c1 0 3-1 3 0 1 0 2 1 3 1 2 1 2 1 3 1h2 1s0-1 1-1l1 2c1 1 3 1 4 2l1-2 1 1h-1v2c-1 0 0 0-1 1h-1c0 1 0 1-1 1h1l1 1h2l9-1h6 1 2 3l8 1c2 1 4 0 6-2h14c1 2 1 4 3 5l1-2 1-1v-2c1-1 2-2 4-2l1-2c4-2 8-3 12-5l6-6z" class="R"></path><path d="M524 549h2 1v2c-1 1-1 1-2 1l-1-1v-2z" class="C"></path><path d="M521 539c1-1 1-1 2-1 2 1 3 3 5 4-1 0-1 0-2-1-1 1-1 1-2 1l-3-3z" class="X"></path><path d="M485 530c2 0 4 3 5 4v1h-4l1-1c-1-2-2-3-2-4z" class="I"></path><path d="M518 541c1 1 2 3 3 4s1 1 2 1v2h-1c-2-2-4-3-5-5l1-2z" class="C"></path><path d="M494 531c-1 0-2-1-3-2v-2l2 1c2-1 3-1 4-3 1 0 1 0 2 1-2 1-1 2-3 4-1 0-2 0-2 1z" class="X"></path><path d="M552 503c2 2 4 4 4 6-4-2-9-2-14-2 3-1 3-3 5-4s2 1 3 1 2-1 2-1z" class="L"></path><path d="M494 531c0-1 1-1 2-1 0 2 2 4 3 6 1 3 4 4 5 7 3 3 6 5 8 8l2 1v-1c1 1 3 2 3 4 1 1 3 2 4 3v1h-1-1l-3-3c-7-5-14-13-19-20l-2-2-3 2c1-2 2-3 2-5z" class="b"></path><path d="M518 541c-2 0-2 0-3-1s-1 0-1-1l1-1-1-1 1-1c-2 0-2 1-3 1s-1 0-2-1h0c-1-2-6-5-5-6 0-1-1-1 0-1 2 0 3 2 4 3h1c1 1 2 3 3 3h1c1-1 2-1 3-1 1 1 1 1 1 2 1 1 1 0 2 1h1c1-2 1-2 1-4v-1c1-1 1-1 2-1 1 1 1 2 1 3s-1 1-1 2c2 3 5 5 7 8 1 0 1 1 2 1l2 2v1h0l-3-3c-2 0-2-1-3-2l-1-1c-2-1-3-3-5-4-1 0-1 0-2 1l-1 1-2-2c-1-1 0 0-2-1v-1c-1 1-1 1-1 2l1 1 2 2z" class="C"></path><path d="M547 493l3-1c1 2 1 4 1 6v1h0c0 2 1 3 1 4 0 0-1 1-2 1s-1-2-3-1-2 3-5 4h-3l-15 3v-2c1-1 2-2 4-2l1-2c4-2 8-3 12-5l6-6z" class="M"></path><path d="M547 493l3-1c1 2 1 4 1 6v1s0-1-1-1c0-1-1-2-2-2-2 1-4 3-6 3h-1l6-6z" class="L"></path><path d="M528 506h0c2 0 4-1 5-1 2-1 4-1 5-1h1l-1 1v1l1 1-15 3v-2c1-1 2-2 4-2z" class="B"></path><defs><linearGradient id="AZ" x1="451.974" y1="517.799" x2="456.167" y2="501.738" xlink:href="#B"><stop offset="0" stop-color="#878687"></stop><stop offset="1" stop-color="#acaaaa"></stop></linearGradient></defs><path fill="url(#AZ)" d="M445 499s0 1 1 1h1l1 1h2c1 0 3-1 3 0 1 0 2 1 3 1 2 1 2 1 3 1h2 1v1 1c2 0 3 1 4 2v2l-1 1c-1 0-1 0-2 1h-1c-1 1-2 1-2 2h-1l-3 3c-1 0-2 1-3 2l-2 2c-2 1-3 1-4 2v-3l-1-2v-7c0-2 0-4-1-6v-1c0-1-1-2-1-3l1-1z"></path><path d="M446 517h3c0-1 2-1 3-2 2-1 4-1 7-2l-3 3c-1 0-2 1-3 2l-2 2c-2 1-3 1-4 2v-3l-1-2z" class="C"></path><path d="M447 519c2-1 4-1 6-1l-2 2c-2 1-3 1-4 2v-3z" class="X"></path><path d="M492 536l3-2 2 2c5 7 12 15 19 20l3 3h1 1v-1c1 1 1 1 2 1 2 0 3 0 4 2 0 0 1 1 1 2h0c-1 1-5 7-6 7l-1 3h0l-1 1h-1-5c-2-2-2-3-4-4l-2 2h-3l-6-1h5c1-1 3 0 4-2-4-4-8-9-12-14-2-2-4-4-6-7-1-2 0-3 0-5l1-1v-2c0-2 1-3 1-4z" class="C"></path><path d="M512 561h-1c0-1 0-2 1-2v-1-1l1-1c1 1 2 3 4 4 1 2 3 4 4 5 0 2 1 4 1 5l-1 3h0l-1 1h-1-5c-2-2-2-3-4-4l-2 2h-3l-6-1h5c1-1 3 0 4-2 1-1 1-1 1-3 1-1 2-3 3-5z" class="U"></path><path d="M512 561l1-1c0 2 1 4 1 5 0 2 0 3-1 4v-2c-2-1-2-1-4-1 1-1 2-3 3-5z" class="I"></path><path d="M521 565l-1 2c-1 0-1 1-3 1h-1l1-1v-1c0-2-1-3-2-4l2-2c1 2 3 4 4 5z" class="G"></path><path d="M514 565c1 2 1 2 1 4 1 1 2 3 3 4l1 1h-5c-2-2-2-3-4-4l3-1c1-1 1-2 1-4z" class="R"></path><path d="M509 566c2 0 2 0 4 1v2l-3 1-2 2h-3l-6-1h5c1-1 3 0 4-2 1-1 1-1 1-3z" class="h"></path><path d="M528 563l19-30c1-2 3-6 5-7h2 5v1c-2-1-3 0-5 0h2v1h0 1 0 3c1 1 3 2 4 3h3c1 0 6 1 8 1 1 0 2-1 3-2h1l1-2 1 2c1-1 1-2 2-2l2-1h2c-1 1-1 2-2 3v2l1 1 1-1-1-2 1-1 2 1 1-1 1 1h1 1 0v1 3c0 1-1 2-1 3s-2 3-2 4c-2-1-2-3-4-2-1 0-1 0-1-1l-2 2c-1 4-1 8-4 10 0 1-1 2-1 3l-1 1s0 1-1 2l1 1-1 1h-1c-1-1-2 0-3 0h0l-2 2h0l-1-2-9 8c-3 0-5 1-8 2s-6 3-10 4-8 2-13 3c-2-1-3 0-5-2h-3 0l1-3c1 0 5-6 6-7z" class="a"></path><defs><linearGradient id="Aa" x1="566.243" y1="559.423" x2="557.181" y2="547.065" xlink:href="#B"><stop offset="0" stop-color="#363535"></stop><stop offset="1" stop-color="#515051"></stop></linearGradient></defs><path fill="url(#Aa)" d="M583 528l2-1h2c-1 1-1 2-2 3v2l1 1 1-1-1-2 1-1 2 1 1-1 1 1h1 1 0v1 3c0 1-1 2-1 3s-2 3-2 4c-2-1-2-3-4-2-1 0-1 0-1-1l-2 2c-1 4-1 8-4 10 0 1-1 2-1 3l-1 1s0 1-1 2l1 1-1 1h-1c-1-1-2 0-3 0h0l-2 2h0l-1-2-9 8c-3 0-5 1-8 2s-6 3-10 4-8 2-13 3c-2-1-3 0-5-2 3-1 6-1 10-2 7-1 14-4 21-7 13-8 20-18 24-33v-1l1-2 1 2c1-1 1-2 2-2z"></path><path d="M580 528l1 2v1h-2v-1l1-2z" class="T"></path><path d="M569 558c2-2 3-4 5-6 1-1 3-3 4-5h0 0c1 1 1 2 1 3s-1 2-1 3l-1 1s0 1-1 2l1 1-1 1h-1c-1-1-2 0-3 0h0l-2 2h0l-1-2z" class="B"></path><path d="M572 558c0-1 2-2 3-3 0-1-1-1 0-2h3l-1 1s0 1-1 2l1 1-1 1h-1c-1-1-2 0-3 0h0z" class="H"></path><path d="M434 500c2 1 2 1 4 1 2-1 4-1 6-1 0 1 1 2 1 3v1c1 2 1 4 1 6v7l1 2v3c1-1 2-1 4-2h0c2 0 3 0 5-1l-2 2h4c1 0 1-1 2-1 1 1 2 2 3 2s1 1 2 1h1l-1 1c1 1 3 1 4 1h1s1 1 2 1l4 2h2c3 5 9 9 11 14l1 1c0 2-1 3 0 5 2 3 4 5 6 7 4 5 8 10 12 14-1 2-3 1-4 2h-5l6 1-1 1h-1-1-5v1l-1 1-1-1c-2 0-3 1-4 1-3-1-6-3-9-3h-3l-7-5-1 1-4-3c-2-1-5-3-5-5h0l-1-1h-1c-1-1-2-2-3-2h-2-1v-3l-1 1v1h-1l-2-2h-5c-1 2 0 4 0 5-1-1-2-3-2-5-2-3-2-6-3-8l-1-4c-1-2-2-3-2-5l-2-1c0-2-1-3-2-4l-10-21c-1-1-1-3-1-5 1 2 2 3 4 4h2v-1h6c1 0 1 0 2-1l-1-6-1-2z" class="a"></path><path d="M451 520h0c2 0 3 0 5-1l-2 2h-1c2 0 4 1 6 0v1c-2 1-4 1-5 1h-3-1c-1 1-1 2-1 4h5 0c-1 0-4 0-6 1l-1-6c1-1 2-1 4-2z" class="I"></path><path d="M439 542v-3c0-1 1-1 2-2v-1h1c1-1 2-2 2-3v-1c1 2 2 4 3 5s3 1 4 1c8 16 19 24 34 30l11 3h3l6 1-1 1h-1-1-5v1l-1 1-1-1c-2 0-3 1-4 1-3-1-6-3-9-3h-3l-7-5-1 1-4-3c-2-1-5-3-5-5h0l-1-1h-1c-1-1-2-2-3-2h-2-1v-3l-1 1v1h-1l-2-2h-5c-1 2 0 4 0 5-1-1-2-3-2-5-2-3-2-6-3-8l-1-4z" class="B"></path><path d="M444 540c0-1 1-2 2-3 1 1 1 2 1 3h-3z" class="O"></path><path d="M462 560c2 1 3 2 5 3 2 2 3 3 6 4l1 1c1-1 1 0 1-1l6 2c1 0 2 0 4 1h1l3 1h7 3l6 1-1 1h-1-1-5v1l-1 1-1-1c-2 0-3 1-4 1-3-1-6-3-9-3h-3l-7-5-1 1-4-3c-2-1-5-3-5-5z" class="F"></path><path d="M481 569c1 0 2 0 4 1h1l3 1h7 3l6 1-1 1h-1-1-5v1l-2-1h-2c-4-1-8-2-12-4z" class="N"></path><path d="M440 546v-3-1c0-2 1-2 3-3l1 1h3l1 1 1 2v5c0 1 0 1 1 2s1 3 2 4l1 1v1h-1l-2-2h-5c-1 2 0 4 0 5-1-1-2-3-2-5-2-3-2-6-3-8z" class="V"></path><path d="M444 540h3l1 1-1 2c0 1 0 1-1 1v1c1 0 1 0 2 1h0-1c-2 0-1 1-2 2h0v-2-2l-3-3h2v-1z" class="B"></path><path d="M444 540h3l1 1-1 2c-1-1-2-2-3-2v-1zm-1 14v-2h1 1c0-1 0 0-1-1 2 0 3-1 4 0v1l2 2h-5c-1 2 0 4 0 5-1-1-2-3-2-5z" class="E"></path><defs><linearGradient id="Ab" x1="441.457" y1="538.327" x2="434.772" y2="508.894" xlink:href="#B"><stop offset="0" stop-color="#504f50"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#Ab)" d="M434 500c2 1 2 1 4 1 2-1 4-1 6-1 0 1 1 2 1 3v1c1 2 1 4 1 6v7l1 2v3l1 6 3 10c-1 0-3 0-4-1s-2-3-3-5v1c0 1-1 2-2 3h-1v1c-1 1-2 1-2 2v3c-1-2-2-3-2-5l-2-1c0-2-1-3-2-4l-10-21c-1-1-1-3-1-5 1 2 2 3 4 4h2v-1h6c1 0 1 0 2-1l-1-6-1-2z"></path><path d="M429 517c1 0 2 1 2 2v2h0c-2-1-2-2-2-4z" class="J"></path><path d="M428 509h6l1 1v1l-1 1-1-1-1 1 1 1c-1 1-2 1-3 2l-2-1-1-2c0-1 0-1-1-2h2v-1z" class="Z"></path><path d="M437 537v-4h0c2-2 2-1 4-1h1v-1h3l-1 1v1c0 1-1 2-2 3h-1v1c-1 1-2 1-2 2v3c-1-2-2-3-2-5z" class="F"></path><path d="M434 500c2 1 2 1 4 1 2-1 4-1 6-1 0 1 1 2 1 3v1c1 2 1 4 1 6h-4 0s-1-1-2-1l-1 1c-2-1-2-1-3-2l-1-6-1-2z" class="Z"></path><path d="M434 500c2 1 2 1 4 1 2-1 4-1 6-1 0 1 1 2 1 3v1h-5c-1 0-2-2-3-2h-2l-1-2z" class="Q"></path><path d="M208 230l-1-1c-1-1-1-1-1-2l-2-2-2-3-3-4v-1c-3-4-4-9-7-13-4-5-4-11-4-17l4 3c3 2 10 9 10 13-1 0-2-1-3-1l8 6v-1l-1-3v-1c-1-2-1-5-1-7l-3-12h-1l3 18-2-4c-3-3-5-9-6-14v-1c0-4-1-10 0-15v-6c4 5 6 10 10 16 1 1 1 2 3 3 1 0 1 0 2 1 1 0 2 0 3 1 1 0 1 0 3 1h0 1l1 1h1c0-2-1-4-1-6v-4-1c0-1 0-4-1-5v-5-4l1-1c1 1 1 1 3 1v-3-2l5-10v1 1l1 1v1 1c0 2 2 3 3 5h0 0v-5c0-3 1-5 2-8 1-2 2-5 3-7l1-3c0-1 1-2 1-3 1-1 1-2 2-3v1c1 1 1 2 1 3l1 5 2 6 2 8c0 1 1 2 1 3l3 7c0 1 1 5 3 5h1s0 2 1 2h1v-4c0-5-1-10 0-15l3-10c0-1 0-2 1-4v-1l1-8 1-6 1-5c0-2 0-4 1-5 1 0 1 1 2 2l3 13c1 1 2 3 2 5 1 0 1 1 1 2v1l1 1c0 4 2 8 2 12l2 13c0 2-1 4 1 6v-5c0-2 1-5 1-8 1-6 2-13 4-19 0-4 1-7 2-10l1-1 1 2c1 1 1 2 2 3v1l1 4c1 1 1 0 1 1s0 1 1 2h0v2l2 3v1 1h1l3-6c0-1 1-2 1-3l3-7 2-5c0-1 1-2 1-3s1-1 2-2v1 15 11c1 3 1 7 1 10v3l8-14c3-5 7-11 9-16 0-1 1-2 1-3l1 1c0 1-1 3-1 4-1 3-2 6-3 8-2 3-4 6-5 9-3 6-6 12-9 19 0 1-5 18-5 18l-4 12-3 7c-2 8-4 17-4 25v1c0 1 1 2 0 4v2c-1 1 0 2-1 3 0 1 0 3-1 4l1 1h0l1 1-1 7v12c1 3 0 6 1 9 0 2 0 4 1 6 0 3-1 5 0 8v-1l2-1v2c-1 2 0 3 0 4 1 1 2 1 2 2 1 0 1 1 2 2v1h0v5h-1 1c1 0 1 0 2 1h0c-5 0-11 0-15-1-8 0-15-1-23-2-2 0-5 0-7-1-3-7-11-13-17-17 1-2 1-3 0-5l-1-1c0-1 0-2-1-4-2-1-3-3-4-4 0-1-1-2-1-3-1-1-2-2-2-3-3-2-5-4-7-6l-2-2c-4-6-11-10-15-16l1-2z" class="k"></path><path d="M265 183c0-2 0-4 1-5 3 2 2 4 2 7 0 1 1 1 1 2v9h-1l-1-1h0l-1-1c0-3 0-8-1-11z" class="a"></path><path d="M267 255l1 1v1 2h1c3 12 9 23 16 33l1 3-1 1-2-1c0-2-2-4-3-6-2-4-3-7-5-10-4-6-8-10-12-15v-1l13 16c-2-3-4-7-5-11l-1-2c0-1 0-2-1-2 0-1 0-1-1-2v-1l-1-3c0-1-1-2 0-3z" class="S"></path><path d="M240 233c-1-1-1-2-1-3v-1l2 2c0 1 1 1 2 2 2 1 4 3 7 4l-1-2c2 1 2 1 2 3l1 1c0-2 1-4 0-5h0c0-2 0-3-1-4l1-1c0-2 0-4-1-6l1-1c1 2 2 3 2 5 0 5 0 11 1 16v3c1 3 2 6 4 8 1 2 4 5 5 7 0 1-1 1-1 2v1l-9-11c-4-6-10-13-16-17 2-1 2-1 2-3z" class="C"></path><path d="M240 233c4 4 9 9 12 14l4 5h0v-1c-1-2-1-3-1-5 1 3 2 6 4 8 1 2 4 5 5 7 0 1-1 1-1 2v1l-9-11c-4-6-10-13-16-17 2-1 2-1 2-3z" class="j"></path><defs><linearGradient id="Ac" x1="275.004" y1="286.379" x2="262.428" y2="186.26" xlink:href="#B"><stop offset="0" stop-color="#abaaa9"></stop><stop offset="1" stop-color="#e9e8e3"></stop></linearGradient></defs><path fill="url(#Ac)" d="M265 183c1 3 1 8 1 11l1 1h0l1 1c0 2 0 4 1 6l1 5c0 4 1 8 2 11l-1 5 3 20c1 2 1 5 1 6 1 12 5 23 8 35 0 2 1 5 3 8h-1c-7-10-13-21-16-33h-1v-2-1l-1-1c-1-1-2-2-2-4v-1l-1-2v-2l-1-1v-2l-1-1v-1c0-1 0-2-1-4v-2c-1-1-1-2-1-2v-1-2c-1-1-1-2-1-3v-1c-1-1 0-3 0-5-1-2-1-6-1-8 0-1 0-1 1-2l6-28z"></path><path d="M267 195l1 1c0 2 0 4 1 6l1 5c0 4 1 8 2 11l-1 5-4-28z" class="k"></path><path d="M208 230l21 23c2 2 7 5 8 8 1 0 1 0 2 1h1 0v-1c0-1 0-1 1-2v-1-1h0c1-1 1-2 2-3l-1-1h-1c1-2 2-5 1-7-1-3-3-6-4-9v-1c6 4 12 11 16 17l9 11c4 5 8 9 12 15 2 3 3 6 5 10 1 2 3 4 3 6l2 1 1-1-1-3h1c0 1 1 2 1 4h0c-8 0-15-1-23-2-2 0-5 0-7-1-3-7-11-13-17-17 1-2 1-3 0-5l-1-1c0-1 0-2-1-4-2-1-3-3-4-4 0-1-1-2-1-3-1-1-2-2-2-3-3-2-5-4-7-6l-2-2c-4-6-11-10-15-16l1-2z" class="X"></path><path d="M208 230l21 23c2 2 7 5 8 8l6 6c1 2 4 3 5 6h1c0 1 1 1 2 1s0 0 1 1c3 2 5 7 5 10v3l1 1-1 1-2-2-1-1c0-1-1-1-1-2-1-1-1-3-2-4h0l-2-2 1-1c-1-1-2-2-2-4 0-1-2-1-2-3-3-4-9-9-13-12h0c-1-1-2-2-2-3-3-2-5-4-7-6l-2-2c-4-6-11-10-15-16l1-2z" class="C"></path><path d="M237 261c1 0 1 0 2 1h1 0v-1c0-1 0-1 1-2v-1-1h0c1-1 1-2 2-3l-1-1h-1c1-2 2-5 1-7-1-3-3-6-4-9v-1c6 4 12 11 16 17h0c-5-3-8-10-13-14l-2-2c1 2 1 4 3 6 1 2 3 4 5 6v1c1 0 1 1 1 2h1c3 2 6 10 8 13 2 4 4 7 7 11 4 6 8 13 14 18l-1 1h0c-1-1-1-1-2-1-3-1-7-5-9-8-4-7-8-15-13-22-1-2-3-4-5-6-1 0-1-1-2-2-2 3-1 4-1 6-1 1-1 2-2 3v1 1l-6-6z" class="I"></path><defs><linearGradient id="Ad" x1="270.941" y1="288.835" x2="310.89" y2="183.582" xlink:href="#B"><stop offset="0" stop-color="#c3c1bf"></stop><stop offset="1" stop-color="#fcfcf7"></stop></linearGradient></defs><path fill="url(#Ad)" d="M269 196h0c1 3 0 4 2 7v-1h0v-1l1-1 1-2c1-1 1-2 2-2l2-3v1 2c1 0 1 0 2 1 1-1 1-2 2-3 1 0 2-1 3-2 1 0 2-1 3-1 2 0 3-2 4-3h1c1-3 3-8 6-9 0 1 0 1-1 2 0 3-1 8-1 11h0v3l1 1c-1 1-1 1-1 2l1 1c-2 8-4 17-4 25v1c0 1 1 2 0 4v2c-1 1 0 2-1 3 0 1 0 3-1 4l1 1h0l1 1-1 7v12c1 3 0 6 1 9 0 2 0 4 1 6 0 3-1 5 0 8v-1l2-1v2c-1 2 0 3 0 4 1 1 2 1 2 2 1 0 1 1 2 2v1h0v5h-1 1c1 0 1 0 2 1h0c-5 0-11 0-15-1h0c0-2-1-3-1-4-2-3-3-6-3-8-3-12-7-23-8-35 0-1 0-4-1-6l-3-20 1-5c-1-3-2-7-2-11l-1-5c-1-2-1-4-1-6h1z"></path><path d="M286 284c1-1 2-1 3 0 2 2 1 4 2 6 1 1 1 1 1 2l-2-2-1-2h-1c0 2 1 3 2 4l1 1h0l-1 1c-2-2-2-3-3-5l-1-5z" class="D"></path><path d="M276 242c1 6 1 12 2 18l4 14c0 2 1 5 1 7v3c-3-12-7-23-8-35 1 1 1 2 2 4v1-3h-1v-3-6z" class="j"></path><path d="M272 218c2 8 3 16 4 24v6 3h1v3-1c-1-2-1-3-2-4 0-1 0-4-1-6l-3-20 1-5z" class="a"></path><path d="M205 285l19 3c1 1 4 1 6 1h0l12 2 14 2h1c2 1 5 1 7 1 8 1 15 2 23 2 4 1 10 1 15 1 9 1 20 2 29 1h6c3-1 6-1 8 0l2-1h5 1 4l29-2 2 2v1 1 2l-9 3c-13 6-26 12-36 22l-2 3-1 2v3h0c3-1 5 0 9 1 0 1 2 1 2 1 2 1 3 0 4-1l2 1-1 2c-2 4-2 9-1 13 1 3 2 5 2 8-1 4 4 10 4 14l4 8c0 2 1 5 2 6 1 2 0 0 1 1 0 1 1 1 1 2s1 2 2 3v1c0 3 2 6 4 9 0 1 0 2 1 2v3l1 1v1c0 2 1 3 2 5 0 1 1 2 2 4 1 1 2 1 2 3l1 1 1 1c0 1 0 2 1 2l1 2c1 1 1 2 2 2 0 1 1 2 1 3l1 1h2l2 3-2 5c0 1 0 1 1 2v1l1 1c1 2 4 11 6 11 1 2 2 4 2 6 0 1 1 3 2 4 0 2 0 4 1 5l10 21s0 1 1 2l3 7 2 4c0 2 0 4 1 5l10 21c1 1 2 2 2 4l2 1c0 2 1 3 2 5l1 4c1 2 1 5 3 8 0 2 1 4 2 5 0-1-1-3 0-5h5l2 2h1v-1l1-1v3h1 2c1 0 2 1 3 2h1l1 1h0c0 2 3 4 5 5l4 3 1-1 7 5h3c3 0 6 2 9 3 1 0 2-1 4-1l1 1 1-1v-1h5 1 1l1-1h3l2-2c2 1 2 2 4 4h5 1l1-1h3c2 2 3 1 5 2 5-1 9-2 13-3s7-3 10-4 5-2 8-2l9-8 1 2h0l2-2h0c1 0 2-1 3 0h1l1-1-1-1c1-1 1-2 1-2l1-1c0-1 1-2 1-3 3-2 3-6 4-10l2-2c0 1 0 1 1 1 2-1 2 1 4 2 0-1 2-3 2-4s1-2 1-3v-3-1h0-1-1l-1-1-1 1-2-1-1 1 1 2-1 1-1-1v-2c1-1 1-2 2-3h-2l-2 1c-1 0-1 1-2 2l-1-2 1-5c0-2 0-5 1-7 0-5 1-9 2-14 0-1 1-4 2-6 2-2 3 1 4 1 1 1 3 0 4 0v10c1 2 1 2 2 3 1 0 1 1 2 1l4-2h1 2v-3h0c1-1 1-1 1-2l1-2v-1c1-1 0-1 1-3 0-1 1-2 1-3v-1l3-6c0-1 1-3 1-5l2-2 1-1 2 2c1-1 1-1 3-1v1h0l1-1c2-4 5-11 4-14v-1h1l1 1 1-1c0-2 1-5 1-7 2-3 4-6 5-9l1-1c1-3 3-6 4-9 1-1 2-2 2-3 3-4 5-10 6-14l1-5c1-2 1-4 2-6 1-1 1-1 1-2 1-5 5-11 5-16l1-3-1-2 2-2c-1-1-1-2-1-3 0-3 0-4 2-5v1c1-3 0-3 0-5h1v-4c0 1 1 1 1 2v1h1c1-1 1-1 1-2s1-2 1-3 0-2 1-3h0v-1c1-1 1-2 0-4h0c0-2-1-4-2-6h0c-1-3-1-1-3-3 1-2-2-4-2-7 1 0 1-1 2-1v-1c-1-2-3-5-6-6h0c-1-1 0-2-1-4v-1c3-1 5-1 8 1l2-1h0c-1-3-3-5-6-7-6-5-12-8-20-11-1 0-2 0-3-1s-4-10-4-11h1c1-1 2-1 2-1l4-3v-2-1c3 0 6 0 8 1 6 0 13 1 20 1h3c1 0 1-1 2-1l1 1h1v-1h0c2 1 3 1 5 1h12l2 1h3 6l27-1h6l37-4 3-1 16-2c5 0 9 0 13-1 1-1 2-1 3-1l7-2 3 1 2 2-1 1v1h0-1-2l3 1 3 2c1 1 2 1 3 1l1 2c-1 1-3 3-5 4-10 4-18 11-27 17v1c-1 0-1 1-2 1l-2 2c-1 1-3 2-4 2-4 3-7 9-11 10-5 8-11 17-14 26l-64 151-20 49c-3 6-5 14-9 20-1 4-3 9-5 13l-9 22-16 39h-2l-14 33v1 1c0 2-2 4-1 7l-28 66-11 26-50 135h-1c-1 0 0 1-1 2v-3h0v-4-1c-1 0-1 1-1 2-2 4-3 9-3 14-2 0-4-2-5-3s-2-1-3-2l-2-1h-1c-3 0-9 4-11 6l-3 4h-1c-2-1-1-3-2-5-1-3-2-7-3-11l-25-62c0-2-1-3-1-4v-1c-1-1-1-3-2-4l-34-75-11-26-3-1-15-35-21-45c0-1-1-1-2-2v-2l-1-2-2-4c-1-1-1-2-1-3h0c-2-4-4-7-6-11l-4-11h-2l-1 1c-1-4-2-7-4-10l-10-22-7-18v-1l-1-1-7-16-2-5-1-4h0l-2-5-1-2-4-9-4-10c-2-3-3-8-5-11l-2-5c-2-4-2-6-6-8v-1h2v-1l-4-8-3-8c-1-2-1-3-3-4 0-2 0-3-1-4l-3-7c-1-2-3-2-5-4-1 0-1-1-2-1-1-1-2-1-3-2-1 0-1-1-2-1h0l-2-1c-2-1-3-1-4-3h2 3 4l4 2c2 1 2 2 3 4l1-1c1 0 2-1 2 0l2 2h1c-10-25-21-50-34-73-10-17-21-33-33-48-6-9-13-18-21-26h-1c-2-1-3-2-4-2l-4-4c-4-4-9-9-13-14-1-1-2-2-2-4-1-1-2-2-3-4 1 1 3 2 3 3l11-7v-3h0 1c3-2 4-2 7-2z" class="c"></path><path d="M682 347c0-2 1-4 1-7h2c2 0 4-1 5 0l1 1-4 1h0c-1 1-2 1-2 1l-1 2-2 2z" class="Y"></path><path d="M584 630l1-2c2 0 3-1 4-2l-13 32c0-2-1-1 0-2l1-2v-1c1-1 0-1 1-1 0-1 0-2 1-2v-2l2-5 1-1c0-1 0-2 1-3 0-1 0-1 1-2l1-1v-2l1-1v-1c0-1 1-2 1-2v-1l-3 1z" class="V"></path><path d="M240 299l5 1c0 1 1 2 1 2 1 1 3 0 3 1 3 2 5 3 7 5l1 1c1 0 2 1 3 2-4-1-10-4-13-7-1 0-1 0-2-1-2-1-3-1-5-4z" class="Y"></path><path d="M666 304l10 1c2 3 3 5 5 7 1 1 1 1 1 2-5-2-10-6-15-9-1 0-1-1-1-1z" class="g"></path><path d="M330 349c-1-4-2-8-3-13h7c2 0 3-1 5-2h1 0v1l-6 4c-2 1-2 2-3 4h0v2 1 1l-1 2z" class="Y"></path><path d="M348 587l15 32h-1c-1-2-1-2-3-3h0l-12-27 1-2z" class="N"></path><defs><linearGradient id="Ae" x1="776.801" y1="310.865" x2="785.291" y2="314.39" xlink:href="#B"><stop offset="0" stop-color="#26272e"></stop><stop offset="1" stop-color="#46474f"></stop></linearGradient></defs><path fill="url(#Ae)" d="M784 310c1 0 2-1 3 0l-1 1v1 1c-1 1-1 2-1 3 0 3-1 4-2 6h0l-1 1c-1-3-6-7-8-9l10-4z"></path><path d="M183 294c1 1 3 2 3 3h1c8 8 16 16 23 25h-1c-2-1-3-2-4-2l-4-4c-4-4-9-9-13-14-1-1-2-2-2-4-1-1-2-2-3-4z" class="J"></path><path d="M330 349l1-2v-1-1-2h0c1-2 1-3 3-4l6-4-1 1c0 1 0 1-1 2-1 2-2 5-3 7s0 6 0 8v3c0 1 1 2 1 3v2l1 1 3 7c0 2 1 3 1 5 1 1 1 2 1 3-2-3-3-7-4-10l-8-18z" class="V"></path><path d="M683 373l1-6c0 2-1 4 1 6l1 1c-1 4-2 8-3 11l-5 16c-1 4-2 8-3 11 0-4 2-8 1-12h0c3-6 3-12 4-18l2-6c0-2 0-2 1-3z" class="L"></path><path d="M683 373l1-6c0 2-1 4 1 6l1 1c-1 4-2 8-3 11v-4-1c-1-2 0-5 0-7z" class="M"></path><path d="M328 541l20 46-1 2c0-1-1-2-2-3l-6-13c-1-5-4-11-6-16-1-2-1-4-2-6s-2-3-3-5v-5z" class="d"></path><path d="M577 637c1 0 2-1 2-2l1-1h1l-2 4c-1 1-1 2-2 3-2 3-2 7-5 9-1 1-1 2-2 4-1 1-2 5-3 6 0 1-1 1-2 1 0 1-7 17-7 19h0 1v-1c1-1 1-2 2-3 0-1 1-3 1-4 1-1 1-3 2-5v-1c1-1 1-2 2-3v-1l1-1h1c-1 3-3 6-4 9 0 2-1 4-2 5-2 4-5 11-9 14l19-44h0 1l4-8z" class="V"></path><path d="M638 314c-1 0-2 0-3-1s-4-10-4-11h1c1-1 2-1 2-1 2 1 3 0 5 1 1 0 2 1 3 2 6 3 11 5 15 10h0c-3-1-6-4-8-6-1 1-1 1-1 2h0c-2-1-3 0-4 0h-2c-1 1-2 1-2 2l-1 1-1 1z" class="H"></path><defs><linearGradient id="Af" x1="694.606" y1="350.415" x2="677.459" y2="362.56" xlink:href="#B"><stop offset="0" stop-color="#191d1f"></stop><stop offset="1" stop-color="#666676"></stop></linearGradient></defs><path fill="url(#Af)" d="M682 347l2-2 1-2s1 0 2-1h0l4-1c-1 12-3 22-5 33l-1-1c-2-2-1-4-1-6s1-6 1-8c0 1 0 2-1 3-1-1-2-1-3 0 1-5 2-10 1-15z"></path><path d="M335 306l17-2c0 2-1 2-3 3-3 1-7 5-9 8-5 2-10 1-16 2-3 0-7 0-11-1h9 3c0-1 0-2 1-3 1-3 0-5 0-7h5 4z" class="Z"></path><path d="M331 306h4c-1 4-3 7-6 10h-3-1c0-1 0-2 1-3 1-3 0-5 0-7h5z" class="Q"></path><path d="M326 306h5c-2 3-4 7-5 10h-1c0-1 0-2 1-3 1-3 0-5 0-7z" class="I"></path><path d="M676 396c1-3 1-8 2-10h0c1-2 1-3 2-4-1 6-1 12-4 18h0c1 4-1 8-1 12l-15 40c-1 0-1-1-1-2h-3c7-18 15-36 20-54z" class="d"></path><defs><linearGradient id="Ag" x1="685.68" y1="311.714" x2="694.063" y2="309.05" xlink:href="#B"><stop offset="0" stop-color="#555764"></stop><stop offset="1" stop-color="#7d7f8c"></stop></linearGradient></defs><path fill="url(#Ag)" d="M676 305h7l1 1h10 3 5c1 3 2 6 2 10h-4-2-1-2-6l-2-1-5-1c0-1 0-1-1-2-2-2-3-4-5-7z"></path><path d="M695 316h0v-2h1l1 2h-2z" class="L"></path><path d="M683 305l1 1h0v2c0 2 1 3 2 5h0 1c0 1 1 1 1 2l1 1-2-1c-1 0-1-1-2-2l-2-1c-1-2 0-5 0-7z" class="F"></path><path d="M694 306h3c1 3 3 5 3 9v1h-2c-2-3-4-7-4-10z" class="Z"></path><path d="M676 305h7c0 2-1 5 0 7l2 1c1 1 1 2 2 2l-5-1c0-1 0-1-1-2-2-2-3-4-5-7z" class="N"></path><path d="M697 306h5c1 3 2 6 2 10h-4v-1c0-4-2-6-3-9z" class="C"></path><path d="M330 557c2 2 3 4 4 6 1 4 2 7 5 10l6 13c1 1 2 2 2 3l12 27h0l5 11-1 1c1 1 2 2 2 4v1h0c-1-1-1-1-2 0l-1 1c-1-4-2-7-4-10l-10-22-7-18v-1l-1-1-7-16-2-5-1-4z" class="Q"></path><path d="M330 557c2 2 3 4 4 6 1 4 2 7 5 10l6 13c-1-1-2-2-2-3-1-4-4-9-6-12 0 2 1 3 1 5 1 1 3 5 3 7h0l-1-1-7-16-2-5-1-4z" class="U"></path><defs><linearGradient id="Ah" x1="254.032" y1="308.556" x2="264.888" y2="301.879" xlink:href="#B"><stop offset="0" stop-color="#141518"></stop><stop offset="1" stop-color="#4c4a53"></stop></linearGradient></defs><path fill="url(#Ah)" d="M245 300c2 1 4 1 6 1l20 3h2 5l5 1c1 3 3 6 3 10h-3l-3-1h-1c-6-1-13-1-19-3-1-1-2-2-3-2l-1-1c-2-2-4-3-7-5 0-1-2 0-3-1 0 0-1-1-1-2z"></path><path d="M245 300c2 1 4 1 6 1v1 1h2v1l-4-1c0-1-2 0-3-1 0 0-1-1-1-2z" class="P"></path><path d="M271 304h2c2 1 4 3 4 5v1c1 0 2 1 3 3v1h-1c-4-3-6-6-8-10z" class="d"></path><path d="M273 304h5l5 11-3-1v-1c-1-2-2-3-3-3v-1c0-2-2-4-4-5z" class="L"></path><path d="M278 304l5 1c1 3 3 6 3 10h-3l-5-11z" class="Z"></path><defs><linearGradient id="Ai" x1="294.775" y1="302.446" x2="319.079" y2="318.001" xlink:href="#B"><stop offset="0" stop-color="#bcbbc1"></stop><stop offset="1" stop-color="#d6e0eb"></stop></linearGradient></defs><path fill="url(#Ai)" d="M283 305h5 1l13 1h6 18c0 2 1 4 0 7-1 1-1 2-1 3h-3-9l-20-1h-2-5c0-4-2-7-3-10z"></path><path d="M288 305h1c2 0 2 0 3 1l1 5v4h-2l-1-1c-1-3-2-6-2-9z" class="D"></path><path d="M283 305h5c0 3 1 6 2 9l1 1h-5c0-4-2-7-3-10z" class="C"></path><path d="M308 306h18c0 2 1 4 0 7-1 1-1 2-1 3h-3v-3l1-1c0-1 0-3-1-4 0-1-12-2-14-2z" class="S"></path><path d="M441 789l61 144-3 4h-1c-2-1-1-3-2-5-1-3-2-7-3-11v-4-1s-1-1-1-2l-3-8c0-1 0-2-1-3v-1l-1-1c0-2 0-3-1-4h0l-1-2c0-2-1-4-2-6s-1-3-1-4c-1-2-2-2-2-4-1-1-2-3-2-4l-1-3-1-1v-2l-4-8v-1l-1-1v-2l-3-5c0-2-1-3-1-4h-1v-2c-1-2-1-3-2-4l-1-2-1-4-3-5-1-2-1-3h-1l-1-2-1-2c-3-10-10-20-12-30v-1c-1-2-1-2-1-4z" class="K"></path><defs><linearGradient id="Aj" x1="787.736" y1="310.327" x2="792.278" y2="313.683" xlink:href="#B"><stop offset="0" stop-color="#474852"></stop><stop offset="1" stop-color="#5d5f6c"></stop></linearGradient></defs><path fill="url(#Aj)" d="M817 292l3 2c1 1 2 1 3 1l1 2c-1 1-3 3-5 4-10 4-18 11-27 17v1c-1 0-1 1-2 1l-2 2c-1 1-3 2-4 2-4 3-7 9-11 10 2-4 5-7 9-11l1-1h0c1-2 2-3 2-6 0-1 0-2 1-3v-1-1l1-1c-1-1-2 0-3 0 3-3 7-4 11-6 1-1 3-2 4-2l11-6 6-3 1-1z"></path><path d="M795 304c1-1 3-2 4-2l11-6c-2 2-4 5-6 6-2 3-8 5-11 6 0-2 1-3 2-4z" class="F"></path><path d="M817 292l3 2c1 1 2 1 3 1l1 2c-1 1-3 3-5 4-10 4-18 11-27 17v1c-1 0-1 1-2 1l-2 2c-1 1-3 2-4 2 1-3 4-4 7-6l3-3c2-1 4-3 6-5s4-3 7-5c2-3 5-5 8-7 1 0 2 0 2-1 1-1 1-1 1-2l-2-2h0l1-1z" class="Q"></path><path d="M817 292l3 2 1 3v1h-1c-3 1-6 2-9 4-1 1-2 3-3 3-3 2-5 4-8 5 2-2 4-3 7-5 2-3 5-5 8-7 1 0 2 0 2-1 1-1 1-1 1-2l-2-2h0l1-1z" class="Z"></path><defs><linearGradient id="Ak" x1="661.423" y1="311.148" x2="654.749" y2="338.541" xlink:href="#B"><stop offset="0" stop-color="#292a30"></stop><stop offset="1" stop-color="#5a5b66"></stop></linearGradient></defs><path fill="url(#Ak)" d="M638 314l1-1 1-1c0-1 1-1 2-2h2c1 0 2-1 4 0h0c0-1 0-1 1-2 2 2 5 5 8 6h0c4 3 7 7 11 11 5 7 10 13 11 22v3c-1 7-1 16-3 22v-9c1-4 2-13-1-16-2-2-3-2-5-2v-2c-1-4-3-6-5-9-1 0-2 0-3-1h0l2-1h0c-1-3-3-5-6-7-6-5-12-8-20-11z"></path><path d="M347 387c3 4 5 8 7 12l16 31 8 15 12 24c6 12 12 24 17 37l3 6c2 4 5 9 7 14 1 3 4 6 5 9 2 5 4 11 8 15l4 8 7 17h0c-1-1-1 0-1-1l-1-1c0-1-1-1-1-2 0-2-2-5-3-6-2-2-2-4-3-6l-1-1c-1-3-3-6-4-9 0-1-1-2-1-3-1-2-3-2-4-3 0 2 1 4 2 6l5 12h0l-82-174z" class="H"></path><defs><linearGradient id="Al" x1="319.334" y1="506.296" x2="304.479" y2="515.86" xlink:href="#B"><stop offset="0" stop-color="#646b7c"></stop><stop offset="1" stop-color="#8a8a92"></stop></linearGradient></defs><path fill="url(#Al)" d="M285 462l4 2c2 1 2 2 3 4l1-1c1 0 2-1 2 0l2 2h1l3 8 27 64v5c1 2 2 3 3 5s1 4 2 6c2 5 5 11 6 16-3-3-4-6-5-10-1-2-2-4-4-6h0l-2-5-1-2-4-9-4-10c-2-3-3-8-5-11l-2-5c-2-4-2-6-6-8v-1h2v-1l-4-8-3-8c-1-2-1-3-3-4 0-2 0-3-1-4l-3-7c-1-2-3-2-5-4-1 0-1-1-2-1-1-1-2-1-3-2-1 0-1-1-2-1h0l-2-1c-2-1-3-1-4-3h2 3 4z"></path><path d="M297 469h1l3 8c-1-1 0-1-1-2h-1c-1-2-2-3-2-6z" class="M"></path><path d="M285 462l4 2c2 1 2 2 3 4h-1c-1 0-5-3-7-3l-6-3h3 4z" class="c"></path><path d="M359 616c2 1 2 1 3 3h1c6 11 10 24 16 35l33 73 19 40c3 7 8 15 10 22 0 2 0 2 1 4v1c2 10 9 20 12 30l1 2 1 2h1l1 3 1 2 3 5 1 4 1 2c1 1 1 2 2 4v2h1c0 1 1 2 1 4l3 5v2l1 1v1l4 8v2l1 1 1 3c0 1 1 3 2 4 0 2 1 2 2 4 0 1 0 2 1 4s2 4 2 6l1 2h0c1 1 1 2 1 4l1 1v1c1 1 1 2 1 3l3 8c0 1 1 2 1 2v1 4l-25-62c0-2-1-3-1-4v-1c-1-1-1-3-2-4l-34-75-11-26-3-1-15-35-21-45c0-1-1-1-2-2v-2l-1-2-2-4c-1-1-1-2-1-3h0c-2-4-4-7-6-11l-4-11h-2c1-1 1-1 2 0h0v-1c0-2-1-3-2-4l1-1-5-11z" class="F"></path><path d="M364 627l10 23c2 6 6 12 7 18 0-1-1-1-2-2v-2l-1-2-2-4c-1-1-1-2-1-3h0c-2-4-4-7-6-11l-4-11h-2c1-1 1-1 2 0h0v-1c0-2-1-3-2-4l1-1z" class="D"></path><path d="M402 713s1 1 2 1c1 1 1 3 2 4l14 31-3-1-15-35z" class="G"></path><path d="M340 335v-1c0 4 0 9 1 13s2 7 3 12c1 2 1 3 1 6 3 8 8 16 12 24 1 2 1 5 3 7l10 21c1 3 3 5 4 8s2 6 4 9c1 1 2 3 3 5s3 6 4 9c0 1 1 2 1 3 3 3 4 8 6 11 0 4 2 7 3 10l23 51 8 18c2 3 3 6 4 9-4-4-6-10-8-15-1-3-4-6-5-9-2-5-5-10-7-14l-3-6c-5-13-11-25-17-37l-12-24-8-15-16-31c-2-4-4-8-7-12-2-3-3-7-5-10 0-1 0-2-1-3 0-2-1-3-1-5l-3-7-1-1v-2c0-1-1-2-1-3v-3c0-2-1-6 0-8s2-5 3-7c1-1 1-1 1-2l1-1z" class="N"></path><path d="M679 350c1 5 0 9 0 14 0 1 0 4 1 5l1-7c1-1 2-1 3 0 1-1 1-2 1-3 0 2-1 6-1 8l-1 6c-1 1-1 1-1 3l-2 6c-1 1-1 2-2 4h0c-1 2-1 7-2 10-5 18-13 36-20 54l-6 14c-1 2-2 4-2 6-3 3-4 7-5 10-2 5-4 10-7 14v2h-1c-4 6-6 13-9 20-2 6-5 12-7 18l-8 18c0 1-2 7-3 7s-1 0-1 1c-1 0-1 2-2 3l-3 9-14 30c-2 4-3 8-6 12-2 5-4 9-6 14l-10 24-17 37-6 15c-2 5-4 12-7 17 1-9 5-17 8-25 2-3 3-6 4-9l4-8 3-6c6-16 14-32 21-48 1-3 2-5 3-7 2-4 4-8 5-13l19-40 8-21 17-38 6-15 12-30c2-5 4-10 5-14 3-6 5-12 7-18 8-18 15-38 18-57 2-6 2-15 3-22z" class="K"></path><defs><linearGradient id="Am" x1="687.757" y1="367.953" x2="672.574" y2="378.81" xlink:href="#B"><stop offset="0" stop-color="#616274"></stop><stop offset="1" stop-color="#91929a"></stop></linearGradient></defs><path fill="url(#Am)" d="M681 362c1-1 2-1 3 0 1-1 1-2 1-3 0 2-1 6-1 8l-1 6c-1 1-1 1-1 3l-2 6c-1 1-1 2-2 4h0c-1 2-1 7-2 10v-2h-1v-2l1-2h0l-2 1c2-7 5-14 6-22l1-7z"></path><path d="M674 391l2-1h0l-1 2v2h1v2c-5 18-13 36-20 54l-6 14c-1 2-2 4-2 6-3 3-4 7-5 10-2 5-4 10-7 14v2h-1l39-105z" class="Q"></path><path d="M205 285l19 3c1 1 4 1 6 1h0l12 2 14 2h1c2 1 5 1 7 1 8 1 15 2 23 2 4 1 10 1 15 1 9 1 20 2 29 1h6c3-1 6-1 8 0l2-1h5 1 4l29-2 2 2v1l-23 5c-4 0-8 1-13 1l-17 2h-4-5-18-6l-13-1h-1-5l-5-1h-5-2l-20-3c-2 0-4 0-6-1l-5-1-24-5-11-3c-3-1-6-2-8-1v-3h0 1c3-2 4-2 7-2z" class="e"></path><defs><linearGradient id="An" x1="648.604" y1="577.239" x2="589.347" y2="504.981" xlink:href="#B"><stop offset="0" stop-color="#242944"></stop><stop offset="1" stop-color="#8d8c87"></stop></linearGradient></defs><path fill="url(#An)" d="M648 470c0-2 1-4 2-6l6-14h3c0 1 0 2 1 2l-71 174c-1 1-2 2-4 2l-1 2c-1 1-2 3-3 4h-1l-1 1c0 1-1 2-2 2l-4 8h-1l13-31c2-6 4-11 6-16 2-3 3-6 4-8l13-31c1 0 3-6 3-7l8-18c2-6 5-12 7-18 3-7 5-14 9-20h1v-2c3-4 5-9 7-14 1-3 2-7 5-10z"></path><path d="M619 534h1c4-7 7-15 10-22 1-3 3-6 5-9-2 6-5 11-7 17-1 3-2 5-3 8s-3 6-5 9c-3 5-5 11-9 15l8-18z" class="L"></path><path d="M611 552c4-4 6-10 9-15l-12 30c-2 3-10 21-12 23h-1l13-31c1 0 3-6 3-7zm24-56h1v-2c3-4 5-9 7-14 1-3 2-7 5-10 0 2 0 3-1 4l-12 29c-2 3-4 6-5 9-3 7-6 15-10 22h-1c2-6 5-12 7-18 3-7 5-14 9-20z" class="d"></path><path d="M608 567c1 5-3 9-5 13 0 1-1 3-1 4-1 4-3 7-5 11l-11 23c-3 7-7 13-9 19l-4 8h-1l13-31c2-6 4-11 6-16 2-3 3-6 4-8h1c2-2 10-20 12-23z" class="B"></path><path d="M608 567c1 5-3 9-5 13 0 1-1 3-1 4l-6 10-6 12c-2 3-2 6-5 8 2-6 4-11 6-16 2-3 3-6 4-8h1c2-2 10-20 12-23z" class="F"></path><path d="M813 286l3 1 2 2-1 1v1h0-1-2c-5 3-13 4-19 5-1 3-4 4-6 6-5 4-10 9-15 11h-1-5-4l-15 1-24 1-7 1h-14c0-4-1-7-2-10h-5-3-10l-1-1h-7l-10-1-28-6v-2-1c3 0 6 0 8 1 6 0 13 1 20 1h3c1 0 1-1 2-1l1 1h1v-1h0c2 1 3 1 5 1h12l2 1h3 6l27-1h6l37-4 3-1 16-2c5 0 9 0 13-1 1-1 2-1 3-1l7-2z" class="e"></path><path d="M749 314c1-1 1-3 2-5s2-3 3-5c2-1 5-1 7-1l12-2-1 2c-2 0-4 0-5 1-1 2-2 5-3 6v1 2l-15 1z" class="W"></path><path d="M784 299l11-3c-1 3-4 4-6 6-5 4-10 9-15 11h-1-5-4v-2-1c1-1 2-4 3-6 1-1 3-1 5-1l1-2 6-1 5-1z" class="P"></path><path d="M779 300l5-1c-2 2-3 2-5 3-3 2-5 5-5 7 0 1 0 3-1 4h-5-4v-2-1c1-1 2-4 3-6 1-1 3-1 5-1l1-2 6-1z" class="B"></path><path d="M779 300l5-1c-2 2-3 2-5 3-3 2-5 5-5 7 0 1 0 3-1 4h-5l2-1c2-1 1-5 2-7v-2l5-1c1-1 1-2 2-2z" class="V"></path><defs><linearGradient id="Ao" x1="709.938" y1="302.861" x2="742.462" y2="315.716" xlink:href="#B"><stop offset="0" stop-color="#c5c9d0"></stop><stop offset="1" stop-color="#feffff"></stop></linearGradient></defs><path fill="url(#Ao)" d="M708 306l38-2c0 1 1 1 1 2-1 1-2 0-3 1-6 0-13 0-17 4l-3 3c0 1 1 1 1 1l-7 1h-14c0-4-1-7-2-10h3 3z"></path><path d="M705 306h3c1 2 2 5 2 7l-1 1 1 1h0v-1h1c1 1 2 1 3 1l4 1h-14c0-4-1-7-2-10h3z" class="D"></path><path d="M705 306h3c1 2 2 5 2 7l-1 1 1 1h0v-1h1c1 1 2 1 3 1-2 1-4 1-6 0l-3-9z" class="I"></path><defs><linearGradient id="Ap" x1="545.664" y1="676.451" x2="661.755" y2="787.678" xlink:href="#B"><stop offset="0" stop-color="#0e0706"></stop><stop offset="1" stop-color="#454e5b"></stop></linearGradient></defs><path fill="url(#Ap)" d="M669 564l21-45c1-3 2-6 5-8l-20 49c-3 6-5 14-9 20-1 4-3 9-5 13l-9 22-16 39h-2l-14 33v1 1c0 2-2 4-1 7l-28 66-11 26-50 135h-1c-1 0 0 1-1 2v-3h0v-4-1c-1 0-1 1-1 2-2 4-3 9-3 14-2 0-4-2-5-3s-2-1-3-2l-2-1h-1c1-1 2-2 2-4 4-9 7-19 10-28l18-45 53-126 57-126 13-26c1-2 2-5 3-8z"></path><path d="M634 654c3-10 8-19 12-28 1-3 2-7 4-10l2-1-16 39h-2z" class="J"></path><path d="M593 750v1 3s0 1-1 2c-1 2-2 4-1 6l-11 26-1-4 14-34z" class="L"></path><path d="M669 564l21-45c1-3 2-6 5-8l-20 49c-3 6-5 14-9 20 0-2 0-3 1-4h0c1-2 0-1 1-2l1-1v-2l2-5 1-2c0-1 0-1 1-2 0-1 0-1 1-2 0-1 0-1 1-2v-1c1-2 2-3 2-5l1-1v-1c0-1 0-1 1-2l1-2v-1l1-1h0v-1c0-1 0-1 1-2 0-1 1-2 1-4h0c1-1 1-2 2-3v-1c1-1 1-1 1-2l-6 12-10 21h-1z" class="g"></path><path d="M620 687v1 1c0 2-2 4-1 7l-28 66c-1-2 0-4 1-6 1-1 1-2 1-2v-3-1c0-2 1-4 2-6l9-21 16-36zm-41 97l1 4-50 135h-1c-1 0 0 1-1 2v-3h0v-4h0l51-134z" class="d"></path><path d="M340 334c3-1 5 0 9 1 0 1 2 1 2 1 2 1 3 0 4-1l2 1-1 2c-2 4-2 9-1 13 1 3 2 5 2 8-1 4 4 10 4 14l4 8c0 2 1 5 2 6 1 2 0 0 1 1 0 1 1 1 1 2s1 2 2 3v1c0 3 2 6 4 9 0 1 0 2 1 2v3l1 1v1c0 2 1 3 2 5 0 1 1 2 2 4 1 1 2 1 2 3l1 1 1 1c0 1 0 2 1 2l1 2c1 1 1 2 2 2 0 1 1 2 1 3l1 1h2l2 3-2 5c0 1 0 1 1 2v1l1 1c1 2 4 11 6 11 1 2 2 4 2 6 0 1 1 3 2 4 0 2 0 4 1 5l10 21s0 1 1 2l3 7 2 4c0 2 0 4 1 5l10 21c1 1 2 2 2 4l2 1c0 2 1 3 2 5l1 4c1 2 1 5 3 8 0 2 1 4 2 5v1c2 3 3 7 5 10l19 44c2 3 3 8 5 10 1 2 1 3 2 5l4 9c2 4 6 7 8 10v-1c1 1 2 2 3 2 1 1 2 1 2 2 2 0 3 1 4 1l3 1h6c4-1 8-1 12-1l1 1v2c-1 1 0 1-1 3l1 1-1 2c1 0 1 1 1 1 2 2 3 3 3 5 1 3 2 7 2 11-1 1-2 2-4 3 1 1 1 2 1 3-2-1-4-1-7-1h-3l-1-1c-2 0-5 1-7-1l-1 1c0 1 1 1 2 1l-2 1c2 2 2 3 4 4l2-1v1c-1 2-3 3-2 6h0v1c0 2 0 0 1 2 0 0 0 2 1 3v2h1c0 2 3 4 3 6 0 1 1 1 1 2 1 2 2 5 2 7-1 2-1 2-1 4 0 4 0 6 1 9v3c0 1 1 2 1 3h0l-1-1h0c-4-6-6-12-9-18l-14-31s-1-2-1-3l-4-8-28-61-18-39h0l-7-17-4-8c-1-3-2-6-4-9l-8-18-23-51c-1-3-3-6-3-10-2-3-3-8-6-11 0-1-1-2-1-3-1-3-3-7-4-9s-2-4-3-5c-2-3-3-6-4-9s-3-5-4-8l-10-21c-2-2-2-5-3-7-4-8-9-16-12-24 0-3 0-4-1-6-1-5-2-8-3-12s-1-9-1-13z" class="k"></path><path d="M391 434h2l2 3-2 5c0-3-1-5-2-8z" class="K"></path><path d="M361 373h0l4 8c0 2 1 5 2 6 1 2 0 0 1 1 0 1 1 1 1 2s1 2 2 3v1c0 3 2 6 4 9 0 1 0 2 1 2v3l1 1v1c0 2 1 3 2 5-4-4-5-10-8-15-2-4-4-8-5-12v-1l-1-1v-2l-1-1v-1-1c0-1 0-1-1-2v-1l-1-1c-1-2-1-3-1-4z" class="a"></path><path d="M420 502l2 4c0 2 0 4 1 5l10 21c1 5 3 9 5 14 1 4 2 8 4 11l3 9 5 14-2-1c-1-1-1-3-2-4-2-3-3-7-4-11-1-3-2-5-3-7v-1c-1-2-1-3-2-4 0-1-1-3-1-4v-1l-1-4c-1-1-1-2-2-3-1-3-1-5-2-8l-1-2-1-2s-1-1-1-2 0 0-1-1l-1-2-1-3c-1-3-3-6-4-9h0v-1c0-1 0-2-1-3s0-3 0-5z" class="j"></path><path d="M392 462c1 1 1 2 2 4v1c2 2 3 4 3 7 3 6 6 11 9 17 9 18 18 36 25 55 1 3 2 5 3 8v4l-4-8c-1-3-2-6-4-9l-8-18-23-51c-1-3-3-6-3-10z" class="a"></path><path d="M340 334c3-1 5 0 9 1 0 1 2 1 2 1 2 1 3 0 4-1l2 1-1 2c-2 4-2 9-1 13 1 3 2 5 2 8-1 4 4 10 4 14h0l-1-1-3-6v-2c0 1-1 1-1 2l-1-1c0-1 0-2 1-3h0c-1-1-1-2-2-3 0-1-1-2-1-2l-1 1c0-3 0-7-3-10h0v-1l-2-3h0c1 1 1 0 1 1l1 1h1v-1-1c-1-1-2-1-3-1l-2-1v1c0 2 1 3 0 6v-1c0-1 0-2-1-3v1l-2-3-1-3v4c1 1 0 2 0 3-1-4-1-9-1-13z" class="D"></path><path d="M342 343v-8c3 1 8 4 11 4h1c-1 2-2 2-3 4h-4l-2-1v1c0 2 1 3 0 6v-1c0-1 0-2-1-3v1l-2-3z" class="a"></path><defs><linearGradient id="Aq" x1="510.695" y1="684.727" x2="488.305" y2="704.273" xlink:href="#B"><stop offset="0" stop-color="#aca8a4"></stop><stop offset="1" stop-color="#d1d5dd"></stop></linearGradient></defs><path fill="url(#Aq)" d="M433 532c1 1 2 2 2 4l2 1c0 2 1 3 2 5l1 4c1 2 1 5 3 8 0 2 1 4 2 5v1c2 3 3 7 5 10l19 44c2 3 3 8 5 10 1 2 1 3 2 5l4 9c2 4 6 7 8 10v-1c1 1 2 2 3 2 1 1 2 1 2 2 2 0 3 1 4 1l3 1h6c4-1 8-1 12-1l1 1v2c-1 1 0 1-1 3l1 1-1 2c1 0 1 1 1 1 2 2 3 3 3 5 1 3 2 7 2 11-1 1-2 2-4 3 1 1 1 2 1 3-2-1-4-1-7-1h-3l-1-1c-2 0-5 1-7-1l-1 1c0 1 1 1 2 1l-2 1c2 2 2 3 4 4l2-1v1c-1 2-3 3-2 6h0v1c0 2 0 0 1 2 0 0 0 2 1 3v2h1c0 2 3 4 3 6 0 1 1 1 1 2 1 2 2 5 2 7-1 2-1 2-1 4 0 4 0 6 1 9v3c0 1 1 2 1 3h0l-1-1h0c-4-6-6-12-9-18l-14-31c2 2 3 5 4 7h1v-3l-1-1c-2-6-5-12-7-18l-39-91-5-14-3-9c-2-3-3-7-4-11-2-5-4-9-5-14z"></path><path d="M445 560c2 3 3 7 5 10l19 44c2 3 3 8 5 10 1 2 1 3 2 5l4 9c2 4 6 7 8 10l1 1-1 1 2 3h-2c0 1 0 1 1 2l-1 1c0-1-1-1-1-2l-3-5-1-1c-2-2-2-4-3-6-1-3-3-5-4-8l-11-26-13-30c-3-6-6-12-7-18z" class="N"></path><defs><linearGradient id="Ar" x1="489.355" y1="653.52" x2="511.534" y2="666.33" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#Ar)" d="M488 647c1 1 2 2 3 2 1 1 2 1 2 2 2 0 3 1 4 1l3 1h6c4-1 8-1 12-1l1 1v2c-1 1 0 1-1 3l1 1-1 2c1 0 1 1 1 1 2 2 3 3 3 5 1 3 2 7 2 11-1 1-2 2-4 3 1 1 1 2 1 3-2-1-4-1-7-1h-3l-1-1c-2 0-5 1-7-1l-1 1c0 1 1 1 2 1l-2 1c-1-2-2-5-4-6l-1-3c-2-3-4-4-6-7-3-6-5-13-8-20l1 1 3 5c0 1 1 1 1 2l1-1c-1-1-1-1-1-2h2l-2-3 1-1-1-1v-1z"></path><path d="M489 655c-1-1-1-1-1-2h2l2 1c-1 0-2 0-2 1v1 1c-1-1-1-1-1-2z" class="K"></path><path d="M494 654c-1-2-1-2-1-3 2 0 3 1 4 1l3 1-6 1z" class="Y"></path><path d="M502 668c1 2-1 4-2 6l-1-1-1-2 1-2c2 0 2 0 3-1h0z" class="M"></path><path d="M488 647c1 1 2 2 3 2 1 1 2 1 2 2s0 1 1 3h-2l-2-1-2-3 1-1-1-1v-1z" class="P"></path><path d="M502 666h-1c-1 0-2-2-3-3h0c1-1 3-2 4-2l1-1c1 0 2 0 3-1 2 0 4 0 6 1v1l-3 1c-3 1-5 2-7 4z" class="Y"></path><path d="M512 661v3c-2 4-3 7-3 12 1 2 1 3 3 4l-1 3-1-1c-2 0-5 1-7-1l-1 1c0 1 1 1 2 1l-2 1c-1-2-2-5-4-6v-4l1-1 1 1c1-2 3-4 2-6v-2h0c2-2 4-3 7-4l3-1z" class="G"></path><path d="M509 662v2h0c-2 1-2 2-3 4v3h-1v-2c-1-2-2-2-3-3h0c2-2 4-3 7-4z" class="L"></path><path d="M502 675c2 0 4 3 6 5h0l2 2c-2 0-5 1-7-1 2-1 0-2 0-4h0l-1-2z" class="U"></path><path d="M499 673l1 1 2 1 1 2h0c0 2 2 3 0 4l-1 1c0 1 1 1 2 1l-2 1c-1-2-2-5-4-6v-4l1-1z" class="F"></path><path d="M512 661v3c-2 4-3 7-3 12h-1l1 2h-1 0c-2-2-1-8-2-10 1-2 1-3 3-4h0v-2l3-1z" class="C"></path><path d="M512 664c1-2 1-4 1-5l1-1c2 0 3 1 4 3 1 0 1 1 1 1 2 2 3 3 3 5 1 3 2 7 2 11-1 1-2 2-4 3 1 1 1 2 1 3-2-1-4-1-7-1h-3l1-3c-2-1-2-2-3-4 0-5 1-8 3-12z" class="a"></path><path d="M512 680l4 1-2 2h-3l1-3z" class="J"></path><path d="M516 681h4c1 1 1 2 1 3-2-1-4-1-7-1l2-2z" class="T"></path><path d="M654 333v-1c3-1 5-1 8 1h0c1 1 2 1 3 1 2 3 4 5 5 9v2c2 0 3 0 5 2 3 3 2 12 1 16v9c-3 19-10 39-18 57-2 6-4 12-7 18-1 4-3 9-5 14l-12 30-6 15-17 38-8 21-19 40c-1 5-3 9-5 13-1 2-2 4-3 7-7 16-15 32-21 48l-3 6-4 8c-1 3-2 6-4 9-3 8-7 16-8 25l-13 31c-1-1-1 0-1-1-1-2-2-5-3-7-2-3-3-6-4-9h0l1 1h0c0-1-1-2-1-3v-3c-1-3-1-5-1-9 0-2 0-2 1-4 0-2-1-5-2-7 0-1-1-1-1-2 0-2-3-4-3-6h-1v-2c-1-1-1-3-1-3-1-2-1 0-1-2v-1h0c-1-3 1-4 2-6v-1l-2 1c-2-1-2-2-4-4l2-1c-1 0-2 0-2-1l1-1c2 2 5 1 7 1l1 1h3c3 0 5 0 7 1s11 0 13 0l1-1 1-1 1 2s2-2 2-3l1-2c0-1 0-1 1-2 0-1 0-1 1-2 0-1 0-1 1-2h0l2-3c1-1 2-1 3-1l5-13 10-23c2-6 4-12 7-18 2-8 5-16 8-24 2-5 5-10 5-15 0-2 2-4 2-6l6-15c-2-1-5 1-7 0-1 0-1-1-2-1-2 1-3 1-5 3l-1-1c1-1 1-2 1-2l1-1c0-1 1-2 1-3 3-2 3-6 4-10l2-2c0 1 0 1 1 1 2-1 2 1 4 2 0-1 2-3 2-4s1-2 1-3v-3-1h0-1-1l-1-1-1 1-2-1-1 1 1 2-1 1-1-1v-2c1-1 1-2 2-3h-2l-2 1c-1 0-1 1-2 2l-1-2 1-5c0-2 0-5 1-7 0-5 1-9 2-14 0-1 1-4 2-6 2-2 3 1 4 1 1 1 3 0 4 0v10c1 2 1 2 2 3 1 0 1 1 2 1l4-2h1 2v-3h0c1-1 1-1 1-2l1-2v-1c1-1 0-1 1-3 0-1 1-2 1-3v-1l3-6c0-1 1-3 1-5l2-2 1-1 2 2c1-1 1-1 3-1v1h0l1-1c2-4 5-11 4-14v-1h1l1 1 1-1c0-2 1-5 1-7 2-3 4-6 5-9l1-1c1-3 3-6 4-9 1-1 2-2 2-3 3-4 5-10 6-14l1-5c1-2 1-4 2-6 1-1 1-1 1-2 1-5 5-11 5-16l1-3-1-2 2-2c-1-1-1-2-1-3 0-3 0-4 2-5v1c1-3 0-3 0-5h1v-4c0 1 1 1 1 2v1h1c1-1 1-1 1-2s1-2 1-3 0-2 1-3h0v-1c1-1 1-2 0-4h0c0-2-1-4-2-6h0c-1-3-1-1-3-3 1-2-2-4-2-7 1 0 1-1 2-1v-1c-1-2-3-5-6-6h0c-1-1 0-2-1-4z" class="U"></path><path d="M520 718l3 1 1-1c1 1 1 2 1 3v1c2 3 1 11 0 14 0 2 0 3-1 5 0-6 2-14-1-19h-1-1l-1-1v-1c-1-1-1-1 0-2z" class="W"></path><path d="M512 708c2 1 6 6 7 7l1-1 1 1-1 3c-1 1-1 1 0 2v1l1 1h1 1c3 5 1 13 1 19 0 2 0 4-1 6v2l-1 2c-1-2-2-5-3-7-2-3-3-6-4-9h0l1 1h0c0-1-1-2-1-3v-3c-1-3-1-5-1-9 0-2 0-2 1-4 0-2-1-5-2-7 0-1-1-1-1-2z" class="J"></path><path d="M521 735c0-2-1-6 0-8 2 2 2 9 2 12h0l-1-2v2l-1-4z" class="O"></path><path d="M521 735l1 4v-2l1 2h0c1 2 1 5 0 8v2l-2-7v-7z" class="H"></path><path d="M515 717v1c2 8 2 16 6 24l2 7-1 2c-1-2-2-5-3-7-2-3-3-6-4-9h0l1 1h0c0-1-1-2-1-3v-3c-1-3-1-5-1-9 0-2 0-2 1-4z" class="S"></path><defs><linearGradient id="As" x1="522.785" y1="731.706" x2="544.442" y2="687.188" xlink:href="#B"><stop offset="0" stop-color="#a09e9e"></stop><stop offset="1" stop-color="#c8c5c5"></stop></linearGradient></defs><path fill="url(#As)" d="M537 684s2-2 2-3l1-2c0-1 0-1 1-2 0-1 0-1 1-2 0-1 0-1 1-2h0l2-3c1-1 2-1 3-1l-2 5h0c-1 2-1 3-2 4 0 2-1 3-1 4l1 1 1-1c0 1-1 3-2 4l-1 5v1h-1v2 1c-1 2-1 4-1 5l-5 17c-2 5-4 11-7 16v1-1l-1-13c0-1-1-4-3-4-1-1-1 0-3-1 0-2 1-3 2-4 3-4 5-10 8-14 2-5 3-10 6-13z"></path><path d="M523 711c1 3 2 2 4 3 1 1 1 4 2 5 0 2 0 12-1 14l-1-13c0-1-1-4-3-4-1-1-1 0-3-1 0-2 1-3 2-4z" class="b"></path><path d="M537 684s2-2 2-3l1-2c0-1 0-1 1-2 0-1 0-1 1-2 0-1 0-1 1-2h0l2-3c1-1 2-1 3-1l-2 5h0c-1 2-1 3-2 4 0 2-1 3-1 4l1 1 1-1c0 1-1 3-2 4 0-1 0-1-1-2 0 2 0 3-2 5v1c-1 1-2 1-2 3-1 0-1 1-1 1-5 5-5 13-11 17h0c-1-2 0-3 1-4h0c1-2 2-4 3-5 1-2 2-4 2-6l-1 1c2-5 3-10 6-13z" class="C"></path><g class="D"><path d="M537 684s2-2 2-3l1-2c0-1 0-1 1-2 0-1 0-1 1-2 0-1 0-1 1-2h0l2-3c1-1 2-1 3-1l-2 5c-2 1-3 4-5 6l-5 9c-1 2-2 5-4 7l-1 1c2-5 3-10 6-13z"></path><path d="M504 683c-1 0-2 0-2-1l1-1c2 2 5 1 7 1l1 1h3c3 0 5 0 7 1s11 0 13 0l1-1 1-1 1 2c-3 3-4 8-6 13-3 4-5 10-8 14-1 1-2 2-2 4h0l-1-1-1 1c-1-1-5-6-7-7 0-2-3-4-3-6h-1v-2c-1-1-1-3-1-3-1-2-1 0-1-2v-1h0c-1-3 1-4 2-6v-1l-2 1c-2-1-2-2-4-4l2-1z"></path></g><path d="M526 689c-3 1-11 1-14 0l14-1v1z" class="I"></path><path d="M504 683c1 1 3 2 4 3v1l-2 1c-2-1-2-2-4-4l2-1z" class="O"></path><path d="M535 683l1-1 1 2c-3 3-4 8-6 13-3 4-5 10-8 14-1 1-2 2-2 4h0l-1-1 12-26c0-1-5 0-6 1v-1l7-2c1-1 1-1 1-2l1-1z" class="T"></path><path d="M654 333v-1c3-1 5-1 8 1h0c1 1 2 1 3 1 2 3 4 5 5 9v2c2 0 3 0 5 2 3 3 2 12 1 16v9c-3 19-10 39-18 57-2 6-4 12-7 18v-1L542 697l-2 3c0-1 0-3 1-5v-1-2h1v-1l1-5c1-1 2-3 2-4l-1 1-1-1c0-1 1-2 1-4 1-1 1-2 2-4h0l2-5 5-13 10-23c2-6 4-12 7-18 2-2 3-4 4-6 4-7 7-14 9-21 2-4 4-9 6-13h1 0v2c-1 1-1 1-1 2l76-183c0-1 1-2 1-2 0-2 1-2 2-4 1-3 2-7 2-11h0v-1c1-2 1-3 1-4v-3c1-1 1-2 1-2v-1-3l-2-2c-1 1-2 1-3 2l-1 1h0v-1c1-1 1-2 0-4h0c0-2-1-4-2-6h0c-1-3-1-1-3-3 1-2-2-4-2-7 1 0 1-1 2-1v-1c-1-2-3-5-6-6h0c-1-1 0-2-1-4z" class="R"></path><path d="M670 350c2 0 2 0 4 1 1 0 2 1 2 2 1 2 0 10-1 13v1-8-1c0-2 0-3-1-5h-1c-2 0-2 0-3 1 0 1 0 2-1 3 0-1-1-1 0-3 0-1 1-2 1-4z" class="Q"></path><path d="M546 674v2c1-1 1-1 1-2l1-2 1-1c0-1 1-2 1-3s0 0 1-1v-1l4-8c0-1 1-1 1-2s1-2 2-3l-1 4-12 25-1 1-1-1c0-1 1-2 1-4 1-1 1-2 2-4z" class="D"></path><path d="M670 345c2 0 3 0 5 2 3 3 2 12 1 16v9c-3 19-10 39-18 57-2 6-4 12-7 18v-1c0-2 1-4 2-6l10-26c2-4 3-9 4-14 4-11 7-22 8-33v-1c1-3 2-11 1-13 0-1-1-2-2-2-2-1-2-1-4-1v-5z" class="b"></path><path d="M654 333v-1c3-1 5-1 8 1h0c1 1 2 1 3 1 2 3 4 5 5 9v2 5c0 2-1 3-1 4-2-4-5-8-8-11-1-2-3-5-6-6h0c-1-1 0-2-1-4z" class="B"></path><path d="M654 333v-1c3-1 5-1 8 1h0c1 1 2 1 3 1 2 3 4 5 5 9-1 2 0 4-1 6l-1-2c0-1-1-3-2-4-1-3-3-5-5-7-2-1-3-1-4 0l-2-1c1-1 1-1 0-1l-1-1z" class="O"></path><path d="M589 575h1 0v2c-1 1-1 1-1 2l76-183c0-1 1-2 1-2 0-2 1-2 2-4-2 9-5 16-7 24l-10 28-5 11-8 18c-1 2-1 5-2 6l-11 24-29 68c-4 8-6 17-10 24-2 3-3 6-4 8l-1 1c0 2-2 4-3 6-2 3-3 6-4 9-4 7-7 15-10 22-2 5-3 10-6 14-1 1-2 2-2 3s-1 1-1 2l-4 8v1c-1 1-1 0-1 1s-1 2-1 3l-1 1-1 2c0 1 0 1-1 2v-2h0l2-5 5-13 10-23c2-6 4-12 7-18 2-2 3-4 4-6 4-7 7-14 9-21 2-4 4-9 6-13z" class="j"></path><path d="M666 366l1-1c1-1 2-1 3-2l2 2v3 1s0 1-1 2v3c0 1 0 2-1 4v1h0c0 4-1 8-2 11-1 2-2 2-2 4 0 0-1 1-1 2l-76 183c0-1 0-1 1-2v-2h0-1c-2 4-4 9-6 13-2 7-5 14-9 21-1 2-2 4-4 6 2-8 5-16 8-24 2-5 5-10 5-15 0-2 2-4 2-6l6-15c-2-1-5 1-7 0-1 0-1-1-2-1-2 1-3 1-5 3l-1-1c1-1 1-2 1-2l1-1c0-1 1-2 1-3 3-2 3-6 4-10l2-2c0 1 0 1 1 1 2-1 2 1 4 2 0-1 2-3 2-4s1-2 1-3v-3-1h0-1-1l-1-1-1 1-2-1-1 1 1 2-1 1-1-1v-2c1-1 1-2 2-3h-2l-2 1c-1 0-1 1-2 2l-1-2 1-5c0-2 0-5 1-7 0-5 1-9 2-14 0-1 1-4 2-6 2-2 3 1 4 1 1 1 3 0 4 0v10c1 2 1 2 2 3 1 0 1 1 2 1l4-2h1 2v-3h0c1-1 1-1 1-2l1-2v-1c1-1 0-1 1-3 0-1 1-2 1-3v-1l3-6c0-1 1-3 1-5l2-2 1-1 2 2c1-1 1-1 3-1v1h0l1-1c2-4 5-11 4-14v-1h1l1 1 1-1c0-2 1-5 1-7 2-3 4-6 5-9l1-1c1-3 3-6 4-9 1-1 2-2 2-3 3-4 5-10 6-14l1-5c1-2 1-4 2-6 1-1 1-1 1-2 1-5 5-11 5-16l1-3-1-2 2-2c-1-1-1-2-1-3 0-3 0-4 2-5v1c1-3 0-3 0-5h1v-4c0 1 1 1 1 2v1h1c1-1 1-1 1-2s1-2 1-3 0-2 1-3z" class="a"></path><path d="M619 488c0 2 0 3-1 5v1l-3 9-3 10-3 7c0 1-1 2-1 4l-4 9-1 1v1l-1 3h0c-1 1-1 2-1 3l-1 1v2l-1 3c-1 2-1 5-2 7-1 4-3 8-4 12h-1v-1l-3-2c2-4 4-7 6-12l1-1c0-2 1-2 1-4 0-1 1-2 1-4 2-3 3-7 5-10 0-1 0-2 1-3h0v-4c0-1 1-2 1-3l4-10 9-23 1-1z" class="j"></path><defs><linearGradient id="At" x1="611.471" y1="505.587" x2="604.27" y2="500.739" xlink:href="#B"><stop offset="0" stop-color="#464545"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#At)" d="M615 481l1-1 2 2c1-1 1-1 3-1v1c-1 2-2 4-2 6l-1 1-9 23h-1s-1 0-1 1c-2 3-2 6-4 8v-2-1l-1 1c-1 1-1 1-2 0v-1c1-1 2-3 2-4 1-2 0-2 1-3v-1-1h2v-3h0c1-1 1-1 1-2l1-2v-1c1-1 0-1 1-3 0-1 1-2 1-3v-1l3-6c0-1 1-3 1-5l2-2z"></path><defs><linearGradient id="Au" x1="616.096" y1="487.88" x2="615.904" y2="482.12" xlink:href="#B"><stop offset="0" stop-color="#7f7d7e"></stop><stop offset="1" stop-color="#979695"></stop></linearGradient></defs><path fill="url(#Au)" d="M615 481l1-1 2 2c1-1 1-1 3-1v1c-1 2-2 4-2 6l-1 1h-1l-3 5v-1c1-1 1-3 2-5-1 0-2 1-3 1h0l-1-1c0-1 1-3 1-5l2-2z"></path><path d="M615 481c1 0 1 0 1 1 1 1 1 1 2 1l-2 1h-2l-1-1 2-2z" class="b"></path><path d="M602 529l2-4v4h0c-1 1-1 2-1 3-2 3-3 7-5 10 0 2-1 3-1 4 0 2-1 2-1 4l-1 1c-2 5-4 8-6 12l3 2v1h1l-4 9c-2 4-4 9-6 13-2 7-5 14-9 21-1 2-2 4-4 6 2-8 5-16 8-24 2-5 5-10 5-15 0-2 2-4 2-6l6-15c1 0 7-15 7-17 1-1 4-8 4-9z" class="e"></path><path d="M603 521c2-2 2-5 4-8 0-1 1-1 1-1h1l-4 10c0 1-1 2-1 3l-2 4c0 1-3 8-4 9 0 2-6 17-7 17-2-1-5 1-7 0-1 0-1-1-2-1-2 1-3 1-5 3l-1-1c1-1 1-2 1-2l1-1c0-1 1-2 1-3 3-2 3-6 4-10l2-2c0 1 0 1 1 1 2-1 2 1 4 2 0-1 2-3 2-4s1-2 1-3v-3-1h0-1l-1-2c1 0 2-1 4-1h1v-3h0 1c1 0 2-1 2-2-1-1-1-1-2-1l-2-2v-1c2 0 3 1 5 1 1 1 1 1 2 0l1-1v1 2z" class="B"></path><path d="M594 542l-1-1c1-1 2-1 2-3 1 0 1 0 1-1 1-1 1-2 2-3 1-2 1-4 2-5h1l1-1v1c0 1-3 8-4 9h-1l-3 4h0z" class="V"></path><path d="M597 521l-2-2v-1c2 0 3 1 5 1 1 1 1 1 2 0l1-1v1 2h-1v1c0 2-2 4-2 5-2 1-3 1-4 1-1 1-1 1-1 2l-1 1c-1 1-1 2-1 3v-3-1h0-1l-1-2c1 0 2-1 4-1h1v-3h0 1c1 0 2-1 2-2-1-1-1-1-2-1z" class="K"></path><path d="M594 542h0l3-4h1c0 2-6 17-7 17-2-1-5 1-7 0-1 0-1-1-2-1-2 1-3 1-5 3l-1-1c1-1 1-2 1-2l1-1c0-1 1-2 1-3 3-2 3-6 4-10l1 1v2l2-1 1 1v2h1 0l2-2c1 0 1 0 1 1l1 1h1l1-3z" class="O"></path><path d="M588 547h2v2l-2 2h-1l-1-1 1-1c0-1 0-1 1-2z" class="E"></path><defs><linearGradient id="Av" x1="593.708" y1="524.409" x2="584.212" y2="496.965" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#898787"></stop></linearGradient></defs><path fill="url(#Av)" d="M581 523c0-2 0-5 1-7 0-5 1-9 2-14 0-1 1-4 2-6 2-2 3 1 4 1 1 1 3 0 4 0v10c1 2 1 2 2 3 1 0 1 1 2 1l4-2h1v1 1c-1 1 0 1-1 3 0 1-1 3-2 4v1c-2 0-3-1-5-1v1l2 2c1 0 1 0 2 1 0 1-1 2-2 2h-1 0v3h-1c-2 0-3 1-4 1l1 2h-1l-1-1-1 1-2-1-1 1 1 2-1 1-1-1v-2c1-1 1-2 2-3h-2l-2 1c-1 0-1 1-2 2l-1-2 1-5z"></path><path d="M597 521c1 0 1 0 2 1 0 1-1 2-2 2h-1 0v3h-1c-2 0-3 1-4 1l1 2h-1l-1-1-1 1-2-1-1 1 1 2-1 1-1-1v-2c1-1 1-2 2-3h-2l-2 1c0-1 0-2 1-2v-2l2-2h1v2c0 1 1 2 2 2v-1h1l1 1 2-1-1-3c1 0 3 0 5-1z" class="T"></path><defs><linearGradient id="Aw" x1="624.924" y1="460.528" x2="637.665" y2="466.132" xlink:href="#B"><stop offset="0" stop-color="#cfcdcb"></stop><stop offset="1" stop-color="#fffefe"></stop></linearGradient></defs><path fill="url(#Aw)" d="M666 366l1-1c1-1 2-1 3-2l2 2v3 1s0 1-1 2v3c0 1 0 2-1 4v1h0l-1 1-1 2v1h0c0 1 0 2-1 3v4h0c-1 1-1 2-2 3 0 0-1 1-1 3h0c-1 1 0 1-1 2v2l-3 9c0 3-2 5-3 7l-10 24c-1 3-3 6-4 9l-29 70-1 1-6 14v1l-3 6v2h-1c-1 2-2 5-2 7l-2 4c0 1 0 1-1 2l-1 2c-1 2-1 5-3 7l14-39c1-4 4-8 5-11 2-5 4-10 5-15 0-1 0-2 1-2v-1c0-1 0-1 1-1v-2l1-1c0-1 0-1 1-2v-2h0c1-1 1-2 1-2l1-2 1-1c0-2 0-1-1-2v-1h0c0 1 0 2-1 2v3l-4 8h-1v-1c1-2 1-3 1-5s1-4 2-6h0l1-1c2-4 5-11 4-14v-1h1l1 1 1-1c0-2 1-5 1-7 2-3 4-6 5-9l1-1c1-3 3-6 4-9 1-1 2-2 2-3 3-4 5-10 6-14l1-5c1-2 1-4 2-6 1-1 1-1 1-2 1-5 5-11 5-16l1-3-1-2 2-2c-1-1-1-2-1-3 0-3 0-4 2-5v1c1-3 0-3 0-5h1v-4c0 1 1 1 1 2v1h1c1-1 1-1 1-2s1-2 1-3 0-2 1-3z"></path><path d="M665 369l1 2v1c-1 2 1 3 0 5h0c0 2 0 2-1 3v1c0 1 0 2-1 3h0c0 1 0 1-1 2 0 1 0 2-1 3s-1 2-2 3l-2 6v1c-3 4-4 10-6 15 0 1-1 3-3 4 1-2 1-4 2-6 1-1 1-1 1-2 1-5 5-11 5-16l1-3-1-2 2-2c-1-1-1-2-1-3 0-3 0-4 2-5v1c1-3 0-3 0-5h1v-4c0 1 1 1 1 2v1h1c1-1 1-1 1-2s1-2 1-3z" class="D"></path><path d="M659 387c0-2 1-4 2-6l1 1-1 2c0 3-1 5-3 7h0l-1-2 2-2z" class="h"></path><path d="M445 559c0-1-1-3 0-5h5l2 2h1v-1l1-1v3h1 2c1 0 2 1 3 2h1l1 1h0c0 2 3 4 5 5l4 3 1-1 7 5h3c3 0 6 2 9 3 1 0 2-1 4-1l1 1 1-1v-1h5 1 1l1-1h3l2-2c2 1 2 2 4 4h5 1l1-1h3c2 2 3 1 5 2 5-1 9-2 13-3s7-3 10-4 5-2 8-2l9-8 1 2h0l2-2h0c1 0 2-1 3 0h1l1-1c2-2 3-2 5-3 1 0 1 1 2 1 2 1 5-1 7 0l-6 15c0 2-2 4-2 6 0 5-3 10-5 15-3 8-6 16-8 24-3 6-5 12-7 18l-10 23-5 13c-1 0-2 0-3 1l-2 3h0c-1 1-1 1-1 2-1 1-1 1-1 2-1 1-1 1-1 2l-1 2c0 1-2 3-2 3l-1-2-1 1-1 1c-2 0-11 1-13 0 0-1 0-2-1-3 2-1 3-2 4-3 0-4-1-8-2-11 0-2-1-3-3-5 0 0 0-1-1-1l1-2-1-1c1-2 0-2 1-3v-2l-1-1c-4 0-8 0-12 1h-6l-3-1c-1 0-2-1-4-1 0-1-1-1-2-2-1 0-2-1-3-2v1c-2-3-6-6-8-10l-4-9c-1-2-1-3-2-5-2-2-3-7-5-10l-19-44c-2-3-3-7-5-10v-1z" class="i"></path><path d="M551 640c1 1 1 1 1 2v1h-3v-1l2-2z" class="P"></path><path d="M544 643v-1c1-2 1-2 3-3v2l1 2h0-1-2-1z" class="E"></path><path d="M536 670c0-2 0-4 2-7v1c0 1 0 2 1 3-1 2-2 2-3 3z" class="O"></path><path d="M562 577c2-1 3-2 6-2l1 1v-1h3c-1 2-2 3-4 3h0v-1c-1 0-1 0-2-1h0l-1 1h-3z" class="P"></path><path d="M556 603h1c-1 1-3 3-3 4 0 2-2 4-4 6v-1h-1v-2c3-2 5-5 7-7z" class="B"></path><path d="M541 609h1c1 0 1-1 2-1l-1 1 2 1h0c-2 2-5 4-7 5 0-1 0-2 1-3h0v-2l2-1z" class="H"></path><path d="M577 557c2-2 3-2 5-3l-1 3c-1 2-4 5-5 6 0-1 1-2 1-2v-2c-1 0-1 0-2 1v-2h0 1l1-1z" class="Y"></path><path d="M543 652c2-2 5-7 7-7v3h0c-1 0-2 2-2 3-1 0-1 0-2 1-1 0-1 0-2 1v1l1 1c-1 0-1-1-2-1l1-1h-1v-1z" class="E"></path><path d="M535 675v-1-1h1c1-1 1-1 3 0v-1c1-1 2-1 3-2-1 3-2 5-3 7-1-1-2-1-4-2z" class="B"></path><path d="M542 656l1-2c1 0 1 1 2 1v2 1c-1 1-2 3-3 5l-2 2h0l-1-2h-1v-1c2-1 3-2 4-4v-2z" class="O"></path><path d="M567 567l2-2c1-1 2-1 2-2 1-2 2-3 4-5v2 2c-1 1-2 0-2 1-1 3-4 5-6 8-1 1-2 1-3 1 1-1 2-3 3-5z" class="E"></path><path d="M557 576c2-2 4-3 6-6 1-1 2-2 4-3-1 2-2 4-3 5-5 4-9 8-14 11 1-2 3-3 4-4l3-3z" class="H"></path><path d="M556 603c2-2 5-7 7-8l1 1-1 1h1c0 1 0 1 1 2h-1l-1-1-2 2c0 1-1 2-2 2h2v1l-1 3h-2-1l1 1h-1-1l-1 1h0l-1-1c0-1 2-3 3-4h-1z" class="P"></path><path d="M548 600l7-5c1-1 2-1 4-2-2 2-3 4-4 5-4 3-6 7-10 10l1 1s-1 0-1 1h0l-2-1 1-1c-1 0-1 1-2 1h-1l7-6v-3zm6 7l1 1h0l1-1h1 1c0 2-2 3-3 5s-5 7-7 7-3 1-4 2c-1 0-1 1-2 1 0-1 2-2 3-3 2-2 3-4 5-6s4-4 4-6z" class="E"></path><path d="M534 660c2 0 3 1 5 0 0-2 0-3 1-4v-2h0l2 2v2c-1 2-2 3-4 4v1h0c-2 3-2 5-2 7-1 2-3 4-5 5-1 1-3 2-5 2l2-2c2-2 4-3 6-5v-4-2c0-1 0-1-1-2l1-2z" class="H"></path><path d="M526 660h5 3l-1 2c1 1 1 1 1 2v2 4c-2 2-4 3-6 5 0-2 2-5 1-6 0-1-1-3-1-4h-1v-2c-1-1-1-2-1-3z" class="N"></path><path d="M531 660h3l-1 2c0 2-1 3-2 4l-1-1-1-2c1-1 1-2 2-3z" class="i"></path><path d="M526 660h5c-1 1-1 2-2 3l1 2h-2-1v-2c-1-1-1-2-1-3z" class="P"></path><path d="M543 584c3-3 9-7 14-8l-3 3c-1 1-3 2-4 4-3 2-7 4-10 6-2 0-7 2-8 3v1c-1 0-5 1-6 1l-1 1c-1-2 0-2 0-3 3 1 11-3 14-5l1-1c1 0 2-1 3-2z" class="B"></path><defs><linearGradient id="Ax" x1="549.363" y1="615.356" x2="535.575" y2="618.903" xlink:href="#B"><stop offset="0" stop-color="#3a393a"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#Ax)" d="M549 610v2h1v1c-2 2-3 4-5 6-1 1-3 2-3 3 1 0 1-1 2-1 1-1 2-2 4-2l-2 2c-2 1-5 3-8 4-1-1-2-1-3-2l2-2c-1-2-1-2-1-3 1-1 3-2 5-2 3-2 6-4 8-6z"></path><path d="M520 662c0-1 0-1 1-2h5c0 1 0 2 1 3v2h1c0 1 1 3 1 4 1 1-1 4-1 6l-2 2h-1l-1 1c0-4-1-8-2-11 0-2-1-3-3-5h1z" class="U"></path><path d="M522 667c2 1 3 2 4 5 0 2 0 3-1 5h0l-1 1c0-4-1-8-2-11z" class="Q"></path><path d="M520 662c0-1 0-1 1-2h5c0 1 0 2 1 3v2c-1 0-1 0-1-1-2-1-4-1-6-2z" class="i"></path><path d="M531 675c1 0 2 0 2 1l-3 3 1 1 4-3h0v-2c2 1 3 1 4 2l-3 5-1 1-1 1c-2 0-11 1-13 0 0-1 0-2-1-3 2-1 3-2 4-3l1-1h1c2 0 4-1 5-2z" class="P"></path><path d="M526 681h2c0 1 0 2-1 3h-3-1l-1-1c2-2 2-2 4-2z" class="i"></path><path d="M535 675c2 1 3 1 4 2l-3 5-1 1v-2c1 0 2-1 2-1v-1c-3-1-4 2-6 2v-1l4-3h0v-2z" class="O"></path><defs><linearGradient id="Ay" x1="541.54" y1="651.334" x2="520.414" y2="660.693" xlink:href="#B"><stop offset="0" stop-color="#2f2d2d"></stop><stop offset="1" stop-color="#555454"></stop></linearGradient></defs><path fill="url(#Ay)" d="M518 652h8 3v1 1h1v-1c3 1 5-1 8 0h5 1l-1 1-1 2-2-2h0v2c-1 1-1 2-1 4-2 1-3 0-5 0h-3-5-5c-1 1-1 1-1 2h-1s0-1-1-1l1-2-1-1c1-2 0-2 1-3v-2l-1-1z"></path><path d="M544 643h1v1c0 1 0 1 1 2l-1 1s-1 1-1 2-1 1-1 2v1 1h-5c-3-1-5 1-8 0v1h-1v-1-1h-3-8c-4 0-8 0-12 1h-6l-3-1h3 1 0c4 0 7-1 10-1s10 1 13-1h0l1-1v-1h0c-2 0-3 0-5-2h3 1c3 0 6-1 8-1s5-1 7-2h1 2v1 2c1-1 2-2 2-3z" class="N"></path><path d="M520 646h3c1 1 3 1 6 1l4-1 1 1c-2 0-4 1-6 1l-3 1v-1h0c-2 0-3 0-5-2z" class="F"></path><path d="M539 643h1 2v1 2c-2 2-3 3-6 5h-1c2-2 4-4 5-7-1 0-2 0-3 1h-1-1 0c-1 0-2 0-2 1l-4 1c-3 0-5 0-6-1h1c3 0 6-1 8-1s5-1 7-2z" class="H"></path><path d="M544 643h1v1c0 1 0 1 1 2l-1 1s-1 1-1 2-1 1-1 2v1 1h-5c-3-1-5 1-8 0 2 0 4-1 5-2h1c3-2 4-3 6-5 1-1 2-2 2-3z" class="Y"></path><defs><linearGradient id="Az" x1="548.299" y1="606.374" x2="524.788" y2="612.055" xlink:href="#B"><stop offset="0" stop-color="#2c2b2b"></stop><stop offset="1" stop-color="#555354"></stop></linearGradient></defs><path fill="url(#Az)" d="M535 604c1-1 4-1 5-2 1 0 1 0 2-1h0c2-1 3-1 3-3h0c1-1 2-1 3-1-2 2-4 5-8 6l1 1c1-1 2-1 3-2s3-2 4-2v3l-7 6-2 1v2h0c-1 1-1 2-1 3-1 0-2 1-3 1l-2 1v1c-1 1-2 1-3 1h-3-1-1v1l-1 1v-3c1-1 1-3 1-5 0-1 0-1 1-2v-3h2c2 0 6-1 7-2v-1-1z"></path><path d="M542 670l41-94c0 5-3 10-5 15-3 8-6 16-8 24-3 6-5 12-7 18l-10 23-5 13c-1 0-2 0-3 1l-2 3h0c-1 1-1 1-1 2-1 1-1 1-1 2-1 1-1 1-1 2l-1 2c0 1-2 3-2 3l-1-2 3-5c1-2 2-4 3-7z" class="X"></path><path d="M533 618h3c0 1 0 1 1 3l-2 2c1 1 2 1 3 2 3-1 6-3 8-4l-3 3h2 0c1 0 2-1 3-1v3l1-1v-2c1 1 2 1 3 2s1 1 2 1v2c0 1 2 1 1 3l-3 3h0c1 1 1 2 2 2v2c-1 1-1 3-2 4 0-1 0-1-1-2l-1-1-3 2v-2c-2 1-2 1-3 3v1c0 1-1 2-2 3v-2-1h-2-1v-1c-1 0-1 0-3-1h-1-3l1-1 1-3v-1c1-2 4-4 4-6 1-2 0-3-1-4-2 1-4 1-6 2-3 0-6 0-10-1h-3l-2-2 7-3 1-1 1-1v-1h1 1 3c1 0 2 0 3-1z" class="Y"></path><path d="M546 626c1 1 2 1 3 2 1 0 1 0 2 1l1-1c0 2 1 2 0 3 0 1-1 2-1 3h0v-1l-1-1-2 2 1-2c-1 0-2 0-2-2l-1-1v-3z" class="P"></path><path d="M547 630l1-1 1 1v2c-1 0-2 0-2-2z" class="Y"></path><path d="M533 618h3c0 1 0 1 1 3l-2 2c1 1 2 1 3 2l-5 1c-4 1-7 1-11 1h-1-3l-2-2 7-3 1-1 1-1v-1h1 1 3c1 0 2 0 3-1z" class="L"></path><path d="M533 618h3c0 1 0 1 1 3l-2 2c1 1 2 1 3 2l-5 1v-1c1-1 1-2 1-3v-1c-1 0-2 0-3-1l-1-1c1 0 2 0 3-1z" class="M"></path><path d="M523 622c2 0 4-1 6 0v1h0l-2 2h0c-2 0-4 1-5 2h-1-3l-2-2 7-3z" class="b"></path><defs><linearGradient id="BA" x1="546.87" y1="629.669" x2="533.699" y2="634.138" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#BA)" d="M537 626c2 0 4 0 6-1v2c1 0 1 1 1 0 1 0 1 0 2-1v3l1 1c0 2 1 2 2 2l-1 2 2-2 1 1v1c-1 1-2 2-2 3-1 1-2 1-2 2-2 1-2 1-3 3v1c0 1-1 2-2 3v-2-1h-2-1v-1c-1 0-1 0-3-1h-1-3l1-1 1-3v-1c1-2 4-4 4-6 1-2 0-3-1-4z"></path><path d="M534 637c1 1 1 2 3 3l1-1v-2l1 1 1-1v1h2 0 1l-7 3h-1-3l1-1 1-3z" class="O"></path><path d="M548 634l2-2 1 1v1c-1 1-2 2-2 3-1 1-2 1-2 2-2 1-2 1-3 3v1c0 1-1 2-2 3v-2-1h-2-1v-1c-1 0-1 0-3-1l7-3h-1 1l1-1c2-1 3-2 4-3z" class="V"></path><path d="M543 638c0 1 1 1 1 3-1 0-2 0-3 1h-2c-1 0-1 0-3-1l7-3z" class="B"></path><defs><linearGradient id="BB" x1="484.631" y1="622.989" x2="524.249" y2="652.676" xlink:href="#B"><stop offset="0" stop-color="#363535"></stop><stop offset="1" stop-color="#727172"></stop></linearGradient></defs><path fill="url(#BB)" d="M480 616c1 1 1 2 2 2l3 2 1 1 3 1h0l2 1c2 1 3 1 5 2 1 0 4 1 5 1v-1c1 0 2-1 3-1h1l4-1c2 0 3-1 5 1l2 1h0l2 2h3c4 1 7 1 10 1 2-1 4-1 6-2 1 1 2 2 1 4 0 2-3 4-4 6v1l-1 3-1 1h3 1c2 1 2 1 3 1v1c-2 1-5 2-7 2s-5 1-8 1h-1-3c2 2 3 2 5 2h0v1l-1 1h0c-3 2-10 1-13 1s-6 1-10 1h0-1-3c-1 0-2-1-4-1 0-1-1-1-2-2-1 0-2-1-3-2v1c-2-3-6-6-8-10l-4-9c-1-2-1-3-2-5 2 1 3 3 4 5 1-1 1 0 2-1v-1c-1-1-2-2-2-4 0-1 0-2-1-3v-1c0-2 0 0 1-1 1 0 1-1 2-2z"></path><path d="M484 640l1-1v-1c-1-1-1-1 0-2h0c1 2 1 3 1 5h1v-1h1v3l2 2v3c1 0 1 1 1 1-1 0-2-1-3-2-1-2-2-2-2-4-1-1-1-2-2-3zm6-7h0v-1h1c0 2 1 3 2 4h0l-1 2c-3 0-3 0-5-1v-2c1-1 1-1 3-2z" class="B"></path><path d="M528 639h2 1l2 1-1 1h3 1c2 1 2 1 3 1v1c-2 1-5 2-7 2-2-1-5-1-7-1h1l1-1c1 0 1 0 2 1l1-1h2c-1-1-1 0-2-1-1 0-3 1-4 0 1-1 3 0 5-2h-2l-1-1z" class="F"></path><path d="M500 643c7-1 14-1 21 1 1 1 3 1 4 1l-1 1h-1-3c-4 0-9 0-14-1h0c-3-1-4-1-6-2z" class="S"></path><path d="M480 616c1 1 1 2 2 2l3 2 1 1 3 1c-1 1-3 3-3 4v1l-1 1v-1c-1-1-1 0-2-1l-1 1c1 1 1 2 1 3l1-1 1 1v2h-3v-1h-1v1c0 1 0 2 1 2 0 1 1 1 1 2v2s1 1 1 2c1 1 1 2 2 3 0 2 1 2 2 4v1c-2-3-6-6-8-10l-4-9c-1-2-1-3-2-5 2 1 3 3 4 5 1-1 1 0 2-1v-1c-1-1-2-2-2-4 0-1 0-2-1-3v-1c0-2 0 0 1-1 1 0 1-1 2-2z" class="E"></path><path d="M483 626c-1-2-1-2-1-4l2-1 2 1c-1 1-2 3-2 3l-1 1z" class="O"></path><path d="M480 616c1 1 1 2 2 2l3 2-1 1-2 1c0 2 0 2 1 4h-1c-1 0-2-2-3-1h1c1 1 2 2 2 4-1 1-2 0-4 1l-2-1c-1-2-1-3-2-5 2 1 3 3 4 5 1-1 1 0 2-1v-1c-1-1-2-2-2-4 0-1 0-2-1-3v-1c0-2 0 0 1-1 1 0 1-1 2-2z" class="Y"></path><defs><linearGradient id="BC" x1="506.296" y1="638.138" x2="506.174" y2="651.076" xlink:href="#B"><stop offset="0" stop-color="#4c4b4b"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#BC)" d="M492 638v1l1 1c1 0 2 1 3 1l2 1v-1c1 1 2 1 2 2 2 1 3 1 6 2h0c5 1 10 1 14 1 2 2 3 2 5 2h0v1l-1 1h0c-3 2-10 1-13 1s-6 1-10 1h0-1-3c-1 0-2-1-4-1 0-1-1-1-2-2 0 0 0-1-1-1v-3l-2-2v-3-1c1 0 2 0 3-1h1z"></path><path d="M488 643h1l2 2s0-1 1-1c0 2 1 4 3 5 1 1 2 1 3 1l1 1h-1 2v1h-3c-1 0-2-1-4-1 0-1-1-1-2-2 0 0 0-1-1-1v-3l-2-2z" class="K"></path><defs><linearGradient id="BD" x1="530.444" y1="640.776" x2="510.958" y2="623.701" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#7a7878"></stop></linearGradient></defs><path fill="url(#BD)" d="M509 623c2 0 3-1 5 1l2 1h0l2 2h3c4 1 7 1 10 1 2-1 4-1 6-2 1 1 2 2 1 4 0 2-3 4-4 6v1l-1 3-2-1h-1-2-3v-1h-1l-1-1h-3-1l-1-1h0 0c0-1-1-1-1-1h-1v2h-1 0c0 1-2 2-3 3-1 0-4-2-5-3-5-6-1-4-1-9h3v-1l-8-1v-1c1 0 2-1 3-1h1l4-1z"></path><path d="M537 626c1 1 2 2 1 4 0 2-3 4-4 6v-3-3h1v-1l-4-1c2-1 4-1 6-2z" class="B"></path><path d="M509 623c2 0 3-1 5 1l2 1v2c-2 1-5 0-7 0l-8-1v-1c1 0 2-1 3-1h1l4-1z" class="I"></path><defs><linearGradient id="BE" x1="542.686" y1="562.962" x2="511.314" y2="610.038" xlink:href="#B"><stop offset="0" stop-color="#343232"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#BE)" d="M445 559c0-1-1-3 0-5h5l2 2h1v-1l1-1v3h1 2c1 0 2 1 3 2h1l1 1h0c0 2 3 4 5 5l4 3 1-1 7 5h3c3 0 6 2 9 3 1 0 2-1 4-1l1 1 1-1v-1h5 1 1l1-1h3l2-2c2 1 2 2 4 4h5 1l1-1h3c2 2 3 1 5 2 5-1 9-2 13-3s7-3 10-4 5-2 8-2l9-8 1 2h0l2-2h0c1 0 2-1 3 0h0c-2 2-3 3-4 5 0 1-1 1-2 2l-2 2c-2 1-3 2-4 3-2 3-4 4-6 6-5 1-11 5-14 8-1 1-2 2-3 2l-1 1c-3 2-11 6-14 5 0 1-1 1 0 3l1-1c1 0 5-1 6-1 5-1 10-3 15-5 5-3 10-7 15-11h3l1-1h0c1 1 1 1 2 1v1c-1 2-2 3-3 4 0 1-1 2-2 3h0c-2 1-3 2-5 4-3 2-6 5-10 8-1 0-2 0-3 1h0c0 2-1 2-3 3h0c-1 1-1 1-2 1-1 1-4 1-5 2v1 1c-1 1-5 2-7 2h-2v3c-1 1-1 1-1 2 0 2 0 4-1 5v3l-1 1-7 3h0l-2-1c-2-2-3-1-5-1l-4 1h-1c-1 0-2 1-3 1v1c-1 0-4-1-5-1-2-1-3-1-5-2l-2-1h0l-3-1-1-1-3-2c-1 0-1-1-2-2-1 1-1 2-2 2-1 1-1-1-1 1v1c1 1 1 2 1 3 0 2 1 3 2 4v1c-1 1-1 0-2 1-1-2-2-4-4-5-2-2-3-7-5-10l-19-44c-2-3-3-7-5-10v-1z"></path><path d="M495 599h2 0 1 2c1 0 2 1 3 1 0 1 1 1 2 1l2 1h-1c-4 0-8 1-11-2v-1z" class="F"></path><path d="M527 604c0-2 3-1 4-2l3-3h1v-1h1v1h1 1v-1c1 0 1-1 2-1l1-1c2-1 3-1 5-1h0l-8 5h0c-2 2-3 3-6 3-1 1 0 1-1 1h-1-3z" class="K"></path><path d="M485 595c4 0 7 3 10 5 3 3 7 2 11 2h1 2 1c-2 1-3 1-4 1l-6 1c-3-2-5-1-7-3-1 0-3-1-3-2-1 0-1-1-1-2-1-1-3-1-4-2z" class="N"></path><path d="M492 581l3 2h0c3 1 5 3 8 4 2 0 3 1 5 3 1-1 1-1 2-1s1 1 2 1v1c1 0 2 0 3-1 1 0 2-1 3-1h2l1 1 1 1-2 1c0-1-1-1-1-2-1 0-1 0-2 1s-3 1-4 2h-3s-1 1-2 1l-1-1h1v-1c-2 0-2 0-3 1v1h0c-2 0-2 0-4-1 0-1 0-1 1-1-1 0-1 0-2-1v-1c-2 0-4 0-6-1l-3-2c2 0 4 0 6-1l-3-2c-1 0-1 0-2-1-1 0-1 0-1-1l1-1z" class="G"></path><path d="M497 586c2 1 6 3 8 3v1l-3 2c-1 0-1 0-2-1v-1c-2 0-4 0-6-1l-3-2c2 0 4 0 6-1z" class="T"></path><path d="M565 577l1-1h0c1 1 1 1 2 1v1c-1 2-2 3-3 4 0 1-1 2-2 3h0c-2 1-3 2-5 4-3 2-6 5-10 8-1 0-2 0-3 1h0c0 2-1 2-3 3h0c-1 1-1 1-2 1-1 1-4 1-5 2h-4c1 0 0 0 1-1 3 0 4-1 6-3h2l1-1h1l2-1c0-1 1-1 2-2h0l2-2v-1c5-2 7-7 10-10 1-1 3-2 4-3l1-1v2c1-1 1-2 2-3l1-1h-1z" class="V"></path><path d="M565 577l1-1h0c1 1 1 1 2 1v1c-1 2-2 3-3 4 0 1-1 2-2 3h0l-1-1-4 3h0v-1c1-1 3-3 4-3 1-1 0-1 1-2h0c1-1 1-2 2-3l1-1h-1z" class="O"></path><path d="M510 602l1 1 1-1h0c1-1 3-1 5-1l1 1c2 0 5-1 7 1l1 1h1 3 1 4v1 1c-1 1-5 2-7 2h-2l-2-1h0-5-2-6-5l-3-2 1-1h-4 0l6-1c1 0 2 0 4-1z" class="M"></path><path d="M500 604l6-1-1 2c4 2 11 1 15 0h1l-2 2h-2-6-5l-3-2 1-1h-4 0z" class="J"></path><path d="M531 604h4v1 1c-1 1-5 2-7 2h-2l-2-1h0-5l2-2h2c2 0 6 0 7-1h1z" class="T"></path><path d="M535 605v1c-1 1-5 2-7 2h-2l-2-1h0c3 0 7-2 11-2z" class="B"></path><defs><linearGradient id="BF" x1="468.912" y1="589.821" x2="491.207" y2="582.945" xlink:href="#B"><stop offset="0" stop-color="#292828"></stop><stop offset="1" stop-color="#545252"></stop></linearGradient></defs><path fill="url(#BF)" d="M466 579l1-1 2 2 2-2 1 2 8 5 8 3c1 1 3 1 4 2h1c0 1 1 1 2 2l1 1h0 3c-1 1-3 1-5 1h0c-1 0-3 0-4 1h1c1 0 1 0 2 1h-1-1v1h1c1 0 2 1 3 2v1c-3-2-6-5-10-5h0c-2-1-2 0-4-2h0-3c-1 0-2-1-3-2s-3-2-4-3l-4-3c-1-1-1-2-1-4 1-1 0-1 0-2z"></path><path d="M471 588l1-1c2 2 3 2 3 4-1-1-3-2-4-3z" class="H"></path><path d="M480 585l8 3c-1 1-1 0-3 1 0 0-1 1-2 1-1-1 0-1 0-2-2 0-3-1-5-1l2-2z" class="K"></path><path d="M478 587v-1c-2-1-3-1-4-2s-2-2-3-2l1-2 8 5-2 2z" class="N"></path><path d="M506 607h5 6 2 5 0l2 1v3c-1 1-1 1-1 2 0 2 0 4-1 5v3l-1 1-7 3h0l-2-1c-2-2-3-1-5-1l2-3c0-1-1-2-1-3l-1-1h-1c-1-1-2-2-2-4-1-1-1-1-1-2l1-2v-1z" class="K"></path><path d="M519 619l2 1v1 1h-2c-1-2-1-1 0-3z" class="T"></path><path d="M524 607l2 1v3c-1 1-1 1-1 2 0 2 0 4-1 5h0c-1-1-1-3 0-4v-1s-1-1-1-2h0c0-1-1-2-1-3l2-1z" class="E"></path><path d="M506 607h5 6l-5 1 1 1c0 1 0 1-1 2 0 2-1 4-2 6l-1-1h-1c-1-1-2-2-2-4-1-1-1-1-1-2l1-2v-1z" class="T"></path><path d="M505 610l2-1v1c-1 1 0 2 1 3h1 1c1-1 1-2 2-2 0 2-1 4-2 6l-1-1h-1c-1-1-2-2-2-4-1-1-1-1-1-2z" class="V"></path><defs><linearGradient id="BG" x1="463.187" y1="568.765" x2="471.191" y2="563.743" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#51504f"></stop></linearGradient></defs><path fill="url(#BG)" d="M453 555l1-1v3h1 2c1 0 2 1 3 2h1l1 1h0c0 2 3 4 5 5l4 3 1-1 7 5c2 1 3 2 5 3l2 1 2 2c1 2 2 2 4 3l-1 1c0 1 0 1 1 1 1 1 1 1 2 1l3 2c-2 1-4 1-6 1l3 2c2 1 4 1 6 1v1c-3 0-5-1-8-1-1-1-3-1-4-2l-8-3-8-5-1-2-2 2-2-2-1 1-3-3v2l-1-1c-3-1-3-2-4-5 1 1 1 1 2 1l-1-2v-1l5 3 1-1c-1 0-2-1-2-2l-1-1c-1-2-4-3-5-5l-1-1-1-1v-1-2h-2l-1-2v-1h1v-1z"></path><path d="M470 575l1-1c2 1 3 4 5 4h2 1c0 1 1 2 3 2l1 3c-5-2-9-5-13-8z" class="B"></path><path d="M478 573c2 1 4 2 7 4l-1 1c1 2 2 3 3 4-1 0-1 0-2-1l-5-3v-1c-1 0-1 0-2-1h0v-3z" class="T"></path><path d="M452 557c1 0 2 1 3 1s1 0 2 1l1 2c1 1 3 2 4 4 0 2 1 4 3 5h1v1l3 3c-3-1-4-3-7-5-1-2-4-3-5-5l-1-1-1-1v-1-2h-2l-1-2z" class="H"></path><path d="M471 568l1-1 7 5c2 1 3 2 5 3l2 1 2 2c1 2 2 2 4 3l-1 1c0 1 0 1 1 1 1 1 1 1 2 1-3 0-5-1-7-2-1-1-2-2-3-4l1-1c-3-2-5-3-7-4l-7-5z" class="W"></path><path d="M462 569c3 2 4 4 7 5l1 1c4 3 8 6 13 8 3 2 5 3 8 4l3 2c2 1 4 1 6 1v1c-3 0-5-1-8-1-1-1-3-1-4-2l-8-3-8-5-1-2-2 2-2-2-1 1-3-3v2l-1-1c-3-1-3-2-4-5 1 1 1 1 2 1l-1-2v-1l5 3 1-1c-1 0-2-1-2-2l-1-1z" class="P"></path><path d="M466 575l5 3-2 2-2-2v-1s-1-1-1-2h0z" class="B"></path><path d="M463 576v-3c2 1 2 1 3 2h0c0 1 1 2 1 2v1l-1 1-3-3z" class="O"></path><path d="M445 559c0-1-1-3 0-5h5l2 2v1l1 2h2v2 1l1 1 1 1c1 2 4 3 5 5l1 1c0 1 1 2 2 2l-1 1-5-3v1l1 2c-1 0-1 0-2-1 1 3 1 4 4 5l1 1v-2l3 3c0 1 1 1 0 2 0 2 0 3 1 4l4 3c1 1 3 2 4 3s2 2 3 2h3 0c2 2 2 1 4 2h0c1 1 3 1 4 2 0 1 0 2 1 2 0 1 2 2 3 2-3 0-3 0-5-1 0 0-1-1-2-1-3-1-5-2-7-4-2-1-5-3-7-4-1-1-3-2-4-3-2 2-1 4-1 5s-1 2-1 2c1 3 2 5 3 7l3 3c0 1 1 1 2 1l3 3c1 1 1 2 2 3l-2 2 3 2c-1 1-1 2-2 2-1 1-1-1-1 1v1c1 1 1 2 1 3 0 2 1 3 2 4v1c-1 1-1 0-2 1-1-2-2-4-4-5-2-2-3-7-5-10l-19-44c-2-3-3-7-5-10v-1z" class="c"></path><path d="M462 580c1 2 2 2 3 4l-2 1c-1-2-1-3-1-5z" class="i"></path><path d="M474 606l3 3c1 1 1 2 2 3l-2 2-4-6 1-2z" class="H"></path><path d="M463 576l3 3c0 1 1 1 0 2 0 2 0 3 1 4l-2-1c-1-2-2-2-3-4l-1-1c1 0 1 1 2 1h1 0l-1-2v-2z" class="E"></path><path d="M466 595l-1-1v-1c-1-1-2-3-2-5h0l-1-1 1-1h2v1c1 0 2 0 3 1-2 2-1 4-1 5s-1 2-1 2z" class="i"></path><path d="M478 593h3 0c2 2 2 1 4 2h0c1 1 3 1 4 2 0 1 0 2 1 2 0 1 2 2 3 2-3 0-3 0-5-1h0c-2-3-7-4-10-7z" class="B"></path><path d="M457 564c1 2 4 3 5 5l1 1c0 1 1 2 2 2l-1 1-5-3v1l1 2c-1 0-1 0-2-1 1 3 1 4 4 5l1 1 1 2h0-1c-1 0-1-1-2-1s-1-1-2-2c0-1 0 0-1-1l-4-9v-1c3 2 3 2 4 5h0c0-1 2-2 2-2 0-1-3-3-3-4v-1z" class="i"></path><defs><linearGradient id="BH" x1="479.158" y1="606.406" x2="505.414" y2="610.914" xlink:href="#B"><stop offset="0" stop-color="#2e2d2c"></stop><stop offset="1" stop-color="#5b5a5a"></stop></linearGradient></defs><path fill="url(#BH)" d="M466 595s1-1 1-2-1-3 1-5c1 1 3 2 4 3 2 1 5 3 7 4 2 2 4 3 7 4 1 0 2 1 2 1 2 1 2 1 5 1 2 2 4 1 7 3h0 4l-1 1 3 2v1l-1 2c0 1 0 1 1 2 0 2 1 3 2 4h1l1 1c0 1 1 2 1 3l-2 3-4 1h-1c-1 0-2 1-3 1v1c-1 0-4-1-5-1-2-1-3-1-5-2l-2-1h0l-3-1-1-1-3-2c-1 0-1-1-2-2l-3-2 2-2c-1-1-1-2-2-3l-3-3c-1 0-2 0-2-1l-3-3c-1-2-2-4-3-7z"></path><path d="M471 595h2 1l4 4v1c5 6 11 12 18 17h0l-4-1h0c-5-3-9-7-13-11-1-2-2-4-4-6l-2-1c0-2-1-2-2-3z" class="Y"></path><path d="M466 595s1-1 1-2-1-3 1-5c1 1 3 2 4 3 2 1 5 3 7 4 0 1 1 3 2 3h1l1 1c1 1 1 1 2 3h0c-2 0-5-1-7-2v-1l-4-4h-1-2c1 1 2 1 2 3l2 1c2 2 3 4 4 6-1 0-1-1-2-1h-1-2l-2 1-3-3c-1-2-2-4-3-7z" class="P"></path><path d="M471 600h-1-1c2-2 0-3 0-5h2c1 1 2 1 2 3l2 1-4 1z" class="E"></path><path d="M475 599c2 2 3 4 4 6-1 0-1-1-2-1h-1-2l-2-1c-1-1-1-2-1-3l4-1z" class="V"></path><defs><linearGradient id="BI" x1="476.966" y1="612.288" x2="488.873" y2="610.593" xlink:href="#B"><stop offset="0" stop-color="#373536"></stop><stop offset="1" stop-color="#575656"></stop></linearGradient></defs><path fill="url(#BI)" d="M474 604h2 1c1 0 1 1 2 1 4 4 8 8 13 11h0c-1 0-1 0-2 2h0c1 1 2 1 2 2h0c-1 1-2 2-3 2h0l-3-1-1-1-3-2c-1 0-1-1-2-2l-3-2 2-2c-1-1-1-2-2-3l-3-3c-1 0-2 0-2-1l2-1z"></path><path d="M482 618v-1-1h2l1 1c2 1 3 2 5 2v-1c1 1 2 1 2 2h0c-1 1-2 2-3 2h0l-3-1-1-1-3-2z" class="T"></path><path d="M482 618v-1-1h2l1 1 1 4-1-1-3-2z" class="K"></path><path d="M477 609l-1-1v-1c1 1 2 2 3 2 2 2 4 3 5 6v1h-2v1 1c-1 0-1-1-2-2l-3-2 2-2c-1-1-1-2-2-3z" class="B"></path><path d="M506 612c0 2 1 3 2 4h1l1 1c0 1 1 2 1 3l-2 3-4 1h-1c-1 0-2 1-3 1v1c-1 0-4-1-5-1-2-1-3-1-5-2l-2-1c1 0 2-1 3-2h0c0-1-1-1-2-2h0c1-2 1-2 2-2l4 1h0l1 1h4v-1h4l1-5z" class="U"></path><path d="M496 617l1 1-1 2 1 2c0 1-1 2-1 3-2-1-3-1-5-2 1-1 2-1 3-2s1-2 2-4z" class="L"></path><path d="M490 618h0c1-2 1-2 2-2l4 1h0c-1 2-1 3-2 4s-2 1-3 2l-2-1c1 0 2-1 3-2h0c0-1-1-1-2-2z" class="W"></path><path d="M506 612c0 2 1 3 2 4h1l1 1c0 1 1 2 1 3l-2 3-4 1v-1c1 0 2-1 3-2l-1-2c-1-1-5-1-6-1v-1h4l1-5z" class="B"></path><defs><linearGradient id="BJ" x1="541.957" y1="561.609" x2="525.441" y2="596.607" xlink:href="#B"><stop offset="0" stop-color="#535353"></stop><stop offset="1" stop-color="#858384"></stop></linearGradient></defs><path fill="url(#BJ)" d="M572 558h0c1 0 2-1 3 0h0c-2 2-3 3-4 5 0 1-1 1-2 2l-2 2c-2 1-3 2-4 3-2 3-4 4-6 6-5 1-11 5-14 8h-1c-1 1-2 1-4 2-3 2-7 3-10 5h-4-2l-1-1-1-1h-2c-1 0-2 1-3 1-1 1-2 1-3 1v-1c-1 0-1-1-2-1s-1 0-2 1c-2-2-3-3-5-3-3-1-5-3-8-4h0l-3-2c-2-1-3-1-4-3l-2-2-2-1c-2-1-3-2-5-3h3c3 0 6 2 9 3 1 0 2-1 4-1l1 1 1-1v-1h5 1 1l1-1h3l2-2c2 1 2 2 4 4h5 1l1-1h3c2 2 3 1 5 2 5-1 9-2 13-3s7-3 10-4 5-2 8-2l9-8 1 2h0l2-2z"></path><path d="M521 573h3c2 2 3 1 5 2l-5 1c-1 0-2 1-3 2h-1v-1l1-1h2v-1c-1 0-2 0-3-1h0l1-1z" class="F"></path><defs><linearGradient id="BK" x1="568.833" y1="569.305" x2="558.749" y2="565.228" xlink:href="#B"><stop offset="0" stop-color="#2d2b2d"></stop><stop offset="1" stop-color="#464544"></stop></linearGradient></defs><path fill="url(#BK)" d="M572 558h0c1 0 2-1 3 0h0c-2 2-3 3-4 5 0 1-1 1-2 2l-2 2c-2 1-3 2-4 3-2 3-4 4-6 6-5 1-11 5-14 8h-1c-1 1-2 1-4 2-3 2-7 3-10 5h-4-2l-1-1h1v1c3-1 6-3 9-4 1-1 2-1 3-2h1l2-2 5-2 4-3 5-3c0-1 1-1 2-2 1 0 0 0 1-1 0-1 1-1 2-2l4-4 9-8 1 2h0l2-2z"></path><path d="M561 568l7-7c-1 3-3 5-5 7h-2z" class="B"></path><path d="M569 558l1 2-2 1-7 7-4 2-3 2c0-1 1-1 2-2l4-4 9-8z" class="T"></path><path d="M510 570c2 1 2 2 4 4h5 1 0c1 1 2 1 3 1v1h-2l-1 1v1h1c1 1 2 1 2 1l1 1c-2 2-6 3-7 6h-3v2c-1 1-1 2-2 2v1-1c-1 0-1-1-2-1s-1 0-2 1c-2-2-3-3-5-3-3-1-5-3-8-4h0l-3-2c-2-1-3-1-4-3l-2-2-2-1c-2-1-3-2-5-3h3c3 0 6 2 9 3 1 0 2-1 4-1l1 1 1-1v-1h5 1 1l1-1h3l2-2z" class="Z"></path><path d="M516 575h2l2 2v1c-2 1-3 0-5 0 1-1 1-2 1-3z" class="b"></path><path d="M510 570c2 1 2 2 4 4h5 1 0c1 1 2 1 3 1v1h-2l-1 1-2-2h-2c0 1 0 2-1 3h-5c-1 0-2 0-3-1 0 3 0 3-2 5h0c-1 0-1 0-2 1-3 0-5-1-7-2h2 3 1l1-1-2-2c1-2 2-1 3-1 1-1 2-2 3-2l-1-1 2-2 2-2z" class="W"></path><path d="M510 570c2 1 2 2 4 4-3 0-4-1-6-2l2-2z" class="D"></path><path d="M507 577l1-1c1-2 2-1 3-1 0 1-1 1-1 3h0c-1 0-2 0-3-1z" class="Q"></path><path d="M511 575h5c0 1 0 2-1 3h-5 0c0-2 1-2 1-3z" class="X"></path><path d="M505 572h3l-2 2 1 1c-1 0-2 1-3 2-1 0-2-1-3 1l2 2-1 1h-1-3-2c-1-1 0-1-1 0h-1l-6-3h0l-2-2-2-1c-2-1-3-2-5-3h3c3 0 6 2 9 3 1 0 2-1 4-1l1 1 1-1v-1h5 1 1l1-1z" class="J"></path><path d="M484 575c4 0 7 1 10 2 2 1 4 1 5 2l2 1h1l-1-1v-2h0l-1-1 1-1h4l1-1h0l1 1c-1 0-2 1-3 2-1 0-2-1-3 1l2 2-1 1h-1-3-2c-1-1 0-1-1 0h-1l-6-3h0l-2-2-2-1z" class="M"></path></svg>