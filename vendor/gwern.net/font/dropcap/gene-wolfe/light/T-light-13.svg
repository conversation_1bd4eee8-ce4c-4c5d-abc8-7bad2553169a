<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="117 89 819 855"><!--oldViewBox="0 0 1024 1023"--><style>.B{fill:#b8b6b5}.C{fill:#8f8e8d}.D{fill:#9c9b9a}.E{fill:#abaaa9}.F{fill:#838282}.G{fill:#e1e0df}.H{fill:#2a2a2a}.I{fill:#c6c5c4}.J{fill:#242425}.K{fill:#acaaa9}.L{fill:#0e0e0e}.M{fill:#393838}.N{fill:#6f6e6d}.O{fill:#626161}.P{fill:#cdcccc}.Q{fill:#b4b3b2}.R{fill:#7b7a7a}.S{fill:#1c1b1c}.T{fill:#f3f2f3}.U{fill:#4a4949}</style><path d="M726 427v2h1c0-1 1-2 1-3 0 4 0 8-1 12-1-1-1-5-2-7l1-4z" class="S"></path><path d="M454 142c3-4 8-5 13-6l-6 5-1-1c-1 1-1 1-2 1h-1l-2 2-1-1z" class="J"></path><path d="M840 274c5 3 9 3 14 4-6 1-15 1-21-1l1-1h2c3 1 6 2 9 2l-6-3 1-1z" class="E"></path><path d="M315 449l1-1c1 3 1 6 2 8l-2 12c-1-6-2-13-1-19z" class="F"></path><path d="M680 823l21 3c-6 1-12 1-18 0h-1c-1-2-2-1-3-1v-1l1-1z" class="L"></path><path d="M582 534h1v11c0 2 0 4 1 7v1c0 2 1 3 2 4h0l-1 1c-1-2-2-3-3-5-1-6-1-13 0-19z" class="M"></path><path d="M216 252h1c1 2 1 3 1 4v1l-1 2c0 1-1 3-2 4v-1-1-5l-1 1c-1 1-4 4-6 4h0-1l4-5c2-1 4-2 5-4z" class="G"></path><path d="M306 521c-4-2-8-3-11-6-4-3-8-7-8-11h0c2 7 10 11 17 14l4 1v1l-2 1zm417-4l1-1c5-2 10-7 13-12v-4l1-1c0 5-2 10-6 14-3 3-7 5-11 7h-5v-1c2-1 5-2 7-2z" class="L"></path><path d="M458 141c1 0 1 0 2-1l1 1c-2 3-2 6-1 10 0 5 2 7 5 10l-1 1c-2-1-4-4-5-6-3-5-3-9-1-15z"></path><defs><linearGradient id="A" x1="714.455" y1="519.05" x2="699.605" y2="520.544" xlink:href="#B"><stop offset="0" stop-color="#252222"></stop><stop offset="1" stop-color="#3e3d3c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M706 519h10v1h5l-3 1c-6 1-13 1-19-1h0v-1h0 7z"></path><path d="M411 147v2h0c-1 4-1 8 0 12 1 2 3 7 6 8 2 2 6 2 8 1v1c-2 1-6 1-8 0-3-1-6-5-7-8-2-5-1-11 1-16z"></path><path d="M660 469c1 3-1 8-2 11 1-4 0-8 0-12h0c-1-4-2-7-4-9-3-4-6-8-11-9h-2c2-1 4 0 7 1 4 2 8 8 10 12 0 1 0 1 1 1 1 2 0 4 1 5z" class="C"></path><defs><linearGradient id="C" x1="588.902" y1="520.414" x2="591.148" y2="530.969" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#444343"></stop></linearGradient></defs><path fill="url(#C)" d="M590 519c1 6 3 12 6 18v1c-1-1-2-3-3-5l-1 1c0-1 0-1-1-1-2-4-3-8-4-12l1-1c1 0 1 0 2-1z"></path><path d="M562 154c1-2 3-5 2-8 0-4-3-7-6-10l4 2c4 1 7 5 9 9l-2 1c0-3-1-4-3-6h-1 0l1 2c1 3 0 8-1 10l-1 1c-1-1-1-1-2-1z" class="L"></path><defs><linearGradient id="D" x1="368.535" y1="513.861" x2="356.465" y2="522.639" xlink:href="#B"><stop offset="0" stop-color="#1c1615"></stop><stop offset="1" stop-color="#383b3b"></stop></linearGradient></defs><path fill="url(#D)" d="M349 517l2-1c3 1 6 0 10 0 4 1 9 2 13 3l1 1c0 1 0 1-1 2h-1c-8-3-16-4-24-5z"></path><path d="M399 574c1 0 2-1 3 0 2 0 3 2 5 4h-1l-2-2c-2-1-3-1-5 0l-3 3c-2 3-1 7 0 10 0 1 1 3 2 4-1 1-1 1-1 2v1c-2-5-6-9-4-15 1-4 3-6 6-7z" class="M"></path><path d="M455 143l2-2h1c-2 6-2 10 1 15 1 2 3 5 5 6l-1 1-4-1-4-4v-1l-1-2c-2-3 0-8 1-12z" class="Q"></path><path d="M292 524c9 6 17 10 28 13-5 1-8 0-13-2h-1c-3-2-8-4-11-5-1 0-3-1-5-2h4l-2-2c-1 0-2 0-3-1-1 0 0 0-1-1h3 1z" class="B"></path><path d="M221 504h1 0c1 2 3 4 3 6 2 6-3 12-3 17s1 9 4 12c0-1-1-3 0-4h0c0-1 0-3 1-4 0 7 1 10 6 16-4-3-7-5-9-8-3-4-5-9-4-14 1-4 4-7 4-11 1-4-1-7-4-10h1z" class="J"></path><path d="M136 233v1c1 3 2 6 2 9 2 13 6 22 18 28l-1 1c-2-1-4-2-6-4-6-5-11-12-12-19-1-5-2-10-2-15l1-1z" class="R"></path><path d="M633 824c15-3 30 1 46 1 1 0 2-1 3 1h1l-43-1c-2 0-5 0-7-1z"></path><path d="M374 453c2-2 5-4 8-5v1c-6 4-12 8-15 17-1 4-1 8-1 13-1-5-3-8-3-13h1c1-2 1-5 3-6 0-1 0-1 1-2 1-2 2-4 5-6l1 1z" class="K"></path><path d="M364 466c1-2 1-5 3-6 0-1 0-1 1-2 1-2 2-4 5-6l1 1c-4 5-8 10-8 16l-1 1-1-4z" class="Q"></path><path d="M192 489c4-1 7-1 11-1 6 4 13 10 18 16h-1c-4-4-11-12-17-13-4-1-10 0-14 0-5 0-10-1-15-2 5 0 11 1 16 0v-1c1 1 1 1 2 1z"></path><path d="M582 534l4-33 4 18c-1 1-1 1-2 1l-1 1v2l-3-2-1 13h-1z" class="J"></path><path d="M584 521l3-10 1 9-1 1v2l-3-2z" class="N"></path><path d="M189 410l-1-15c-1-7-8-10-14-14-3-2-6-4-8-6 5 2 10 5 16 5 2 1 4-1 6-2v-1l1 1-1 1c-1 1-4 3-6 2-3 0-6-1-9-2 6 4 14 7 16 15 1 5 1 11 1 16h-1z" class="S"></path><path d="M642 441c1 0 3 1 4 2s1 3 4 4 6 5 7 9v3c-4-6-9-10-15-13-6-2-14-1-20 1v-1l1-1c7-2 13-2 20 0h1v-1l-2-1v-2zm-326 7c1-4 2-8 3-13 2-5 4-9 5-14h1c1 0 1 1 2 2h-1v2 1c0 5-3 10-5 14l-3 16c-1-2-1-5-2-8z" class="C"></path><path d="M320 513c-1-1-2-2-2-3h0c4 2 7 0 12-1-13-5-23-12-34-19l-12-8c6 1 10 2 15 5 1 1 6 5 6 5-2 0-6-4-8-5l-1 1c2 1 4 2 6 4l21 12c4 2 8 1 10 4-4 2-8 4-13 5z" class="H"></path><path d="M596 476v-8c0-9 5-16 11-22 6-7 15-10 24-10 8 1 16 4 22 10 6 7 8 14 7 23-1-1 0-3-1-5s-1-3-2-5v-3c-1-4-4-8-7-9s-3-3-4-4-3-2-4-2l-5-2c-8-2-16-2-23 3-9 4-15 13-17 22 0 1 0 2-1 3v9h0z" class="O"></path><defs><linearGradient id="E" x1="599.818" y1="565.278" x2="599.275" y2="557.117" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2827"></stop></linearGradient></defs><path fill="url(#E)" d="M583 545c0 1 1 2 1 3v-2l1 1h0c1 5 1 10 6 13 1 1 3 2 4 2h2 3l-2-2 1-1c1 1 2 2 3 2h4c3 0 6-1 8 0h1v1 1h1c-7 2-15 5-22 2h0c-4-1-7-4-9-7l1-1h0c-1-1-2-2-2-4v-1c-1-3-1-5-1-7z"></path><path d="M599 559c1 1 2 2 3 2h4c3 0 6-1 8 0-6 2-12 3-19 1h2 3l-2-2 1-1z" class="I"></path><path d="M388 346h1c6-8 14-12 23-14l1 1c-11 4-23 11-28 23-1 2-4 7-4 11 0 3 2 7 3 10-2-2-4-5-5-7-2-3-3-7-4-9-1-1-2-1-3-1h2v-1h2c1 1 2 3 3 4v1h1v-3l1-1c1-6 4-10 7-14z" class="J"></path><path d="M453 720h0c1-7 1-14 1-21v-25-80c0-15-1-30 0-44v105c1-2 1-4 1-6v32c1 6 1 12 1 18-1 5 0 10-1 15l-1 7-1-1z" class="H"></path><path d="M455 649v32h0v-2c0 1 0 2-1 3v-27c1-2 1-4 1-6z" class="S"></path><path d="M614 334c7 3 14 7 19 12h1c1 1 1 2 2 3 2 3 4 5 6 8 0 1 1 2 2 3 1 0 2 0 2 1v2c-1 6-3 11-7 16 2-5 3-10 1-15v-2c-4-13-14-19-26-26 1 0 0-1 0-2z" class="J"></path><path d="M299 487c0-2-1-4 0-6l3-3c2-1 2-1 4-1v1c-3 0-4 0-6 3v3c2 2 3 3 5 4 4 3 8 5 12 8 7 4 14 9 22 11l-6 1c-2-3-6-2-10-4l-21-12c-2-2-4-3-6-4l1-1c2 1 6 5 8 5 0 0-5-4-6-5z" class="U"></path><defs><linearGradient id="F" x1="673.983" y1="519.643" x2="674.128" y2="514.222" xlink:href="#B"><stop offset="0" stop-color="#393837"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#F)" d="M676 514c3-1 6 0 10 0l20 5h-7 0v1h0c-4 0-8-2-13-2-12-2-26 0-38 4-1-1-1-1-1-2v-1h2c6-3 15-4 22-5h5z"></path><path d="M671 514h5l5 1h-5c-1 0 1 0-1 0-1-1-3 0-4-1z" class="O"></path><path d="M465 161c1 1 1 1 3 1 4-3 8-8 11-12 12-16 22-37 29-56 1-5 2-11 4-16 3 18 11 36 19 52l-1 1c-7-14-14-30-18-46l-7 19c-4 11-10 22-16 33-5 7-10 15-16 22-4 4-9 8-13 11-1-2-1-5-1-8l4 1 1-1 1-1z" class="L"></path><path d="M433 525l1-2h0c-1 9-6 17-13 23-7 5-17 8-25 8l-1 1v1 1h-5l-1 1c-8-1-13 0-20 3l3-3c8-8 18-6 28-7 4 0 8-1 12-3 11-5 16-13 20-24l1 1z" class="S"></path><path d="M433 525l1-2h0c-1 9-6 17-13 23-7 5-17 8-25 8-5 0-9 0-14 1-3 1-6 2-8 2 4-2 9-4 14-4 4-1 8 0 12-1 5 0 10-2 14-4 10-5 15-12 19-23z" class="T"></path><path d="M323 480c4 4 7 8 11 11 3 2 6 5 10 8 10 6 21 11 33 16-2-2-3-4-4-5-2-2-7-5-9-8h4c5 1 9 6 15 5 1-1 2-2 2-4v-1l1-1c0 3 0 5-1 7-1 1-2 1-2 2h-2c1 3-1 6-3 9s-6 8-9 10l-6 3h0v-1c3-2 9-5 10-9h1c1-1 1-1 1-2l-1-1c1 0 1 0 2-1-1-1-1-1-1-2-1 0-3-1-4-2-4-2-8-3-12-5s-8-5-13-7c-8-5-17-14-23-21v-1z" class="D"></path><defs><linearGradient id="G" x1="328.321" y1="523.806" x2="331.004" y2="510.558" xlink:href="#B"><stop offset="0" stop-color="#363735"></stop><stop offset="1" stop-color="#625f61"></stop></linearGradient></defs><path fill="url(#G)" d="M319 515c2 0 4-1 6-1v1h1 0c1 0 2-1 2-1 1 0 3 0 4-1h3c1-1 1 0 2 0 0-1 1-1 1-1 1-1 3 0 4 0h1c2 0 2 0 4 1l7 1 7 2c-4 0-7 1-10 0l-2 1h-4c-4 0-7 0-11 1-9 2-17 5-26 3h-2l2-1v-1l-4-1 4-1c4-1 7-1 11-2z"></path><path d="M308 519l1-1 3 1v1 1h-4-2l2-1v-1zm46-5l7 2c-4 0-7 1-10 0l-2 1h-4c-1-1-4-1-5-1h-1c4-2 10-1 14-1l1-1z" class="M"></path><path d="M319 515c2 0 4-1 6-1v1h1 0c1 0 2-1 2-1 1 0 3 0 4-1h3c1-1 1 0 2 0 0-1 1-1 1-1 1-1 3 0 4 0h1c2 0 2 0 4 1h-11c-6 1-11 4-17 5-3 1-7 1-10 0l-1 1-4-1 4-1c4-1 7-1 11-2z" class="G"></path><path d="M314 380l1-2 2 2v5c0 2 1 3 0 5-1 3 0 8 1 11v5l3 4c1 4 3 8 6 12h0c2 0 3 0 4 1h-3l-2 3v-1-2h1c-1-1-1-2-2-2h-1c-1 5-3 9-5 14-1 5-2 9-3 13l-1 1 3-16c-1-1-2-1-3-2-2-2-2-3-2-5 1 2 2 3 4 4 1 0 1 0 2-1 1-3 2-8 1-10v-1c-1-2-2-7-4-8l-1-1c-2 0-3-1-4-1-3 0-4 0-6 1l-1 1h0c1-2 3-3 5-4 2 0 5 1 7 2-4-10-4-17-2-28z" class="M"></path><path d="M318 406c-1-2-2-5-3-8s-2-9 0-12c0-1 0-1 2-1 0 2 1 3 0 5-1 3 0 8 1 11v5z" class="C"></path><path d="M569 436c0 2 0 5 1 7v-84-23c0-5-1-9 0-13v257c-1-3 0-7 0-10v-23-11-13-1c-1 0 0-1-1-1v2h-1v-6-18l1-63z" class="J"></path><path d="M363 466c1-9 4-17 11-23 4-4 10-6 16-7h0c9-1 18 2 25 7 8 6 12 15 13 24 2 16-7 31-16 43-1 1-2 2-2 3 0-1 1-2 1-3 1-2 2-4 4-5 6-8 10-17 12-27 1-4 1-8 0-12v-1c-1-8-6-16-12-21-8-5-17-7-26-6l-7 2c-1 1-2 1-2 2l-1 2c1 1 3 1 4 1h0c6-1 10-2 16 0h1v1c-8-2-16-1-23 3-2 1-3 2-4 3-3 2-4 4-5 6-1 1-1 1-1 2-2 1-2 4-3 6h-1z" class="F"></path><path d="M725 485c3-1 7-1 10-1l3-1v1c-5 2-9 4-14 7-10 7-19 15-32 18 4 1 8 3 12 1v1l-2 2c-4 0-9-2-13-3-2-1-5 0-7 0l13-7 6-3 8-5 6-3c3-2 8-4 10-7z" class="H"></path><path d="M709 495l6-3-3 3c-3 2-7 5-10 6v-1h-1l8-5z" class="M"></path><defs><linearGradient id="H" x1="313.565" y1="372.53" x2="304.616" y2="391.859" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#363738"></stop></linearGradient></defs><path fill="url(#H)" d="M298 407c1-4 2-7 3-10 3-11 9-22 16-31l-4 13-8 19c-2 5-4 11-4 17v2c-1 4-1 8 0 12 2 12 6 25 12 35l7 12c1 1 3 3 3 4v1l-3-3c-10-13-17-29-20-45-1-5-2-10-1-15v-2-8l1-1h-2z"></path><defs><linearGradient id="I" x1="712.322" y1="397.739" x2="726.342" y2="406.725" xlink:href="#B"><stop offset="0" stop-color="#0c0e0e"></stop><stop offset="1" stop-color="#3e3b3b"></stop></linearGradient></defs><path fill="url(#I)" d="M717 388l5 13h2c2 8 2 18 2 26l-1 4c0 3 0 6-1 9v-5h-1c-6 27-23 50-46 66l-14 8-1-1c17-7 32-21 43-36 4-5 7-10 10-16 5-12 9-27 7-41-1-8-5-17-8-25l2 2 1-1v-2-1z"></path><path d="M724 401c2 8 2 18 2 26l-1 4c0 3 0 6-1 9v-5h-1v-2c2-10 2-22-1-32h2z" class="B"></path><path d="M190 410h0c1 3 0 10 2 12v1c-2 3 0 13 1 17 0 3 0 5 1 8s3 6 3 9l-1 1v1l3 11c2 3 1 7 0 11l-4 4c-1 1-3 2-3 4-1 0-1 0-2-1 4-3 7-6 8-10v-7c-1-3-3-7-4-10-2-5-3-10-6-15s-8-5-14-6c4 0 9-1 12-4 1-1 1-3 2-5 1-7 1-14 1-21h1z" class="M"></path><path d="M188 435c1 2 1 4 2 5 1 3 4 8 2 10l-1-4c-3-3-5-5-9-6h-1c3-1 5-2 7-4v-1z" class="B"></path><path d="M190 410c1 3 0 10 2 12v1c-2 3 0 13 1 17 0 3 0 5 1 8s3 6 3 9l-1 1v1l-4-9c2-2-1-7-2-10-1-1-1-3-2-5h1c1-3 1-6 1-9v-16z" class="F"></path><path d="M453 720l1 1c-8 27-21 50-41 70-6 6-14 13-22 17-6 4-13 6-20 9l-13 5c-3 1-7 1-9 3v1h4c-3 1-6 0-9 1-5 0-10 0-15-1 21-2 42-9 60-19 32-18 53-53 64-87z" class="S"></path><defs><linearGradient id="J" x1="205.016" y1="460.279" x2="196.54" y2="487.783" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#a8a7a6"></stop></linearGradient></defs><path fill="url(#J)" d="M196 459v-1l1-1c0-3-2-6-3-9s-1-5-1-8c-1-4-3-14-1-17 4 12 6 25 11 37v2l1 1c0 1 0 2 1 3 0 3 0 8-1 11l-1 6c1 1 1 1 2 1 0 2 1 2 2 3v1h-4c-4 0-7 0-11 1 0-2 2-3 3-4l4-4c1-4 2-8 0-11l-3-11z"></path><path fill="#fefdfe" d="M587 523v-2c1 4 2 8 4 12 1 5 3 9 6 14l1 2h0c-2 2-2 5-2 7 0 1 1 3 2 4l2 2h-3-2c-1 0-3-1-4-2-5-3-5-8-6-13h0l-1-1v2c0-1-1-2-1-3v-11l1-13 3 2z"></path><path d="M584 521l3 2c0 4-1 7-1 11l-1 13h0l-1-1v2c0-1-1-2-1-3v-11l1-13z" class="R"></path><path d="M434 518l4-17 1 13c1 5 2 11 2 17 1 9 3 19-2 27-3 4-8 6-13 7-13 2-24-5-37-7l1-1h5l14 4 1-1c5 1 12 1 17 0 3 0 4-3 8-2l2-2c4-12 1-23-1-35h0l-2-3z"></path><path d="M410 560c5 1 12 1 17 0 3 0 4-3 8-2-4 4-8 5-14 5-4 0-9-1-12-2l1-1z" class="E"></path><path d="M434 518l4-17 1 13c-1 1-1 3 0 5 1 3 1 7 1 11v5c1 2 0 3 1 5 0 2 1 4 0 6v1c0 1 0 2-1 3-1 2-1 4-3 5v1c4-12 1-23-1-35h0l-2-3z" class="J"></path><path d="M614 336c-12-5-25-7-39-9 23-6 50-9 73-2 11 3 21 9 31 15 22 13 40 36 46 61 2 8 3 17 3 25 0 1-1 2-1 3h-1v-2c0-8 0-18-2-26h-2l-5-13-1-2 2-1c-4-9-11-19-17-26-5-6-11-9-17-14-14-10-29-18-46-21-6-1-12-1-19-2-12-1-26 2-37 4 10 2 22 4 32 8 0 1 1 2 0 2z"></path><path d="M718 385c3 5 4 10 6 16h-2l-5-13-1-2 2-1z" class="G"></path><defs><linearGradient id="K" x1="206.849" y1="254.712" x2="175.449" y2="270.385" xlink:href="#B"><stop offset="0" stop-color="#a1a09d"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#K)" d="M203 243v-1c1 1 2 1 2 2h2c1 4 1 7 1 11l-1 5v1h1c-4 3-6 8-11 10-2 4-10 6-14 7-2 1-3 1-5 0-2 0-5 1-6 0 1-1 2-1 4-1 1-1 9-4 9-4l4-4c1-2 3-4 3-6l1-1v-1c1-4 0-10-2-14h3c-1 2 0 2 1 4 1-1 1-2 1-3l1-1v-1c1 1 1 1 2 1v-3h1l1 1s1 1 2 1v-3z"></path><path d="M203 243v-1c1 1 2 1 2 2h2c1 4 1 7 1 11h-2l-1 2c-1-1-1 0-1-1v-2c0-3 0-7-1-11z" class="J"></path><path d="M204 254l1-4h1v5l-1 2c-1-1-1 0-1-1v-2z" class="H"></path><path d="M208 255l-1 5v1h1c-4 3-6 8-11 10 1-1 2-2 3-4 1-3 3-7 4-11 0 1 0 0 1 1l1-2h2z" class="S"></path><path d="M200 244l1 1c0 3 1 7-1 10 0 1 0 1-1 1-3 6-7 15-14 17l4-4c1-2 3-4 3-6l1-1v-1c1-4 0-10-2-14h3c-1 2 0 2 1 4 1-1 1-2 1-3l1-1v-1c1 1 1 1 2 1v-3h1z" class="J"></path><path d="M200 244l1 1c0 3 1 7-1 10 0 1 0 1-1 1l1-8c-1 2-2 5-3 6 0-1-1-1 0-2 0-1 1-3 0-5v-1c1 1 1 1 2 1v-3h1z" class="N"></path><path d="M721 539c-14 3-28 4-42 0l-3-1-6-2c-8-3-19-8-25-15-4-6-8-14-7-22 1-3 3-5 5-7l2 1c-3 3-6 6-6 10 0 2 1 3 2 5 4 1 7-2 11-3l7-2c-5 3-9 6-13 10l-1 1 1 1 2-1c4-3 9-4 14-6l1 1h-1c-5 3-12 4-17 8h1l3 2h-2v1c0 1 0 1 1 2l4 4c5 3 11 7 17 9 10 3 19 4 30 3 4 0 8 0 11-1l14-6c2 0 3 0 4-1 1 1 2 0 3 0l-4 3h0c4 0 7-2 10-4 9-4 16-9 23-16 1-1 1-2 2-2-10 13-26 24-41 28z" class="F"></path><path d="M724 531c2 0 3 0 4-1 1 1 2 0 3 0l-4 3c-4 2-8 3-11 3l-1 1h-5l14-6z" class="I"></path><path d="M646 513l-1 1 1 1 2-1c4-3 9-4 14-6l1 1h-1c-5 3-12 4-17 8h1l3 2h-2-1c-3-2-4-5-5-8l9-4c-1 2-4 4-4 6z" class="D"></path><path d="M570 547v23c0 3-1 7 0 10l1 83v34c0 7 0 14 2 21 1 7 4 13 6 20 8 18 18 35 32 49 20 19 43 29 69 36l-1 1c-5 0-10-2-15-4-21-6-40-17-55-33-14-15-24-32-32-50-2-7-4-13-6-19-2-9-2-19-2-28v-28-75-10-20c0-3 0-7 1-10zm-338-23c10 3 20 10 28 16 4 3 9 7 14 9 4 2 8 2 12 3l2-1 4 2c3 2 3 2 4 6 0 2 0 8 1 9l1 1v1c-1 5-3 10-7 13-3 2-5 2-8 2-1-1-1-2-2-2-2-1-4-1-5-1 1 1 3 2 5 3 1 0 0 0 1 1l2-1 1 1h4c1-1 1-1 2-1l1 1v-2l1-1c2-1 6-2 8-4h0c2-3 6-10 10-11h0c14-8 29-9 43-15 5-2 10-4 15-7 4-2 7-5 11-6l-1 1c-13 11-31 15-47 20-6 2-13 3-18 6-9 5-10 17-20 20-5 2-11 2-16 0-7-4-10-11-13-18 2 3 4 6 7 8s8 4 13 4c2 0 3-2 5-3 3-5 3-13 2-18 0-2-1-4-3-5-4-2-10-2-15-4-7-3-13-9-19-13s-13-9-19-11c-2-1-3-1-5-1-2 1-3 3-4 5-1 1-1 3-1 4h0l-1-1c0-4 2-6 4-9l3-1z"></path><path d="M288 551l4 2c3 2 3 2 4 6 0 2 0 8 1 9l1 1c-2 4-3 9-6 12l-1-1 2-2c-1-1 0-1-1-1 2-5 3-14 1-19-1-3-4-4-7-6l2-1z" class="O"></path><path d="M298 569v1c-1 5-3 10-7 13-3 2-5 2-8 2-1-1-1-2-2-2-2-1-4-1-5-1 1 1 3 2 5 3 1 0 0 0 1 1l2-1 1 1h4c1-1 1-1 2-1l1 1v-2l1-1c2-1 6-2 8-4h0c2-3 6-10 10-11h0l-4 4c-3 4-5 9-9 12-4 2-8 3-12 3-7-1-12-5-16-10v1c5 2 11 5 16 4 3-1 5-3 6-5 1 0 0 0 1 1l-2 2 1 1c3-3 4-8 6-12z" class="F"></path><path d="M592 534l1-1c1 2 2 4 3 5v-1c6 8 14 12 25 14s24 1 32 11c-10-5-21-4-31-1l-6 2h-1v-1-1h-1c-2-1-5 0-8 0h-4c-1 0-2-1-3-2l-1 1c-1-1-2-3-2-4 0-2 0-5 2-7h0l-1-2c-3-5-5-9-6-14 1 0 1 0 1 1z"></path><path d="M610 554l9 2c4 1 10-1 13 1-6 1-11 2-17 4h-1c-2-1-5 0-8 0l3-3v-2c1-1 1-1 1-2z" class="T"></path><path d="M592 534l1-1c1 2 2 4 3 5 2 3 4 5 7 7 5 4 13 6 20 7 9 1 16 1 24 6h-1c-3-1-6-1-9-1h-5c-3-2-9 0-13-1l14-1h0c-9-1-20-2-28-6-6-3-11-9-13-15z" class="Q"></path><path d="M591 533c1 0 1 0 1 1 2 6 7 12 13 15 8 4 19 5 28 6h0l-14 1-9-2c0 1 0 1-1 2v2l-3 3h-4c-1 0-2-1-3-2l-1 1c-1-1-2-3-2-4 0-2 0-5 2-7h0l-1-2c-3-5-5-9-6-14z" class="L"></path><path d="M599 559l-1-1c-2-3-1-6 0-9 4 2 8 4 12 5 0 1 0 1-1 2v2l-3 3h-4c-1 0-2-1-3-2zm-165-41l2 3h0c2 12 5 23 1 35l-2 2c-4-1-5 2-8 2-5 1-12 1-17 0l-1 1-14-4v-1-1l1-1c8 0 18-3 25-8 7-6 12-14 13-23h0l-1 2-1-1 2-6z" fill="#fefdfe"></path><path d="M395 556h10l1 1c2 1 4 1 6 1l1-1h1v1c-1 1-3 1-4 2l-1 1-14-4v-1z" class="G"></path><path d="M434 518l2 3c1 6-5 16-8 22 1 0 3 1 4 2v1l-1-1h-4l-3 3c0 1 2 2 2 3 1 1 0 5 0 6-1 1-1 2-2 2 0-3 2-7 0-9 0-1-1-1-1-1l-6 3c-4 2-8 3-12 4h-10v-1l1-1c8 0 18-3 25-8 7-6 12-14 13-23h0l-1 2-1-1 2-6z"></path><path d="M349 825l18-2c20-1 45 2 64 11 10 5 19 12 28 20 25 21 51 53 53 87 2-6 3-13 5-19 2-8 6-15 10-23 5-9 11-18 18-26 13-14 29-30 46-38 13-7 28-10 42-11 2 1 5 1 7 1-6 1-12 1-18 2-22 3-38 11-54 26-8 7-15 14-22 22-13 16-24 34-29 54-2 7-3 15-4 23-2-9-2-18-5-27-11-34-38-65-68-84-18-11-43-17-65-16-7 0-15 1-22 1h-4v-1z" class="L"></path><path d="M400 445c8 4 14 10 17 18s2 17-2 25c-5 10-13 17-23 23v1 1h0c-1 1-1 1-3 2-1-1 0-1-2-1-3 1-4 4-6 7-3 3-7 7-11 10l-4 3v-1h0c1-1 2-1 3-2 2-2-2 1 1-1 1 0 1-1 2-1l5-5h-2c-3 3-6 6-11 8h-1l6-3c3-2 7-7 9-10s4-6 3-9h2c0-1 1-1 2-2 1-2 1-4 1-7l-1 1c-1-3-2-5-4-7-3-4-8-5-13-5s-7 2-10 6l2-4c2-3 6-6 10-7 5-1 11 0 16 3 5 4 7 7 8 13 9-4 15-9 19-18 3-7 3-15 0-23-3-6-7-11-13-14v-1z"></path><path d="M376 490c-4-2-8-2-12-1h0c5-3 10-4 15-3 5 2 10 5 12 10 2 3 2 7 1 11v4 1 1h0c-1 1-1 1-3 2-1-1 0-1-2-1l1-6c1-3 2-10 1-13-2-3-6-5-9-6l-1-1h0c-1 0-1-1-2-1-3 0-5 0-8 1l2-1c2 1 4 1 5 2v1z" class="I"></path><path d="M376 490v-1c-1-1-3-1-5-2l-2 1c3-1 5-1 8-1 1 0 1 1 2 1h0l1 1c3 1 7 3 9 6 1 3 0 10-1 13l-1 6c-3 1-4 4-6 7-3 3-7 7-11 10l-4 3v-1h0c1-1 2-1 3-2 2-2-2 1 1-1 1 0 1-1 2-1l5-5h-2c-3 3-6 6-11 8h-1l6-3c3-2 7-7 9-10s4-6 3-9h2c0-1 1-1 2-2 1-2 1-4 1-7l-1-1c-1-5-4-8-9-10z" class="G"></path><path d="M844 378l10-3-10 7c-4 4-11 7-11 13-1 6 1 14 1 20 0 5-1 11 0 16h0c1 3 2 5 4 6 2 2 6 2 9 3-5 1-12 1-15 6-3 7-5 15-7 22 0 4-1 7 0 11 2 4 5 7 8 9 2 0 3 1 5 1h0c3 1 7 1 10 0-1 1-3 2-4 2-6 1-12 0-17 0-2 0-5 0-7 1-4 1-8 4-11 6-4 4-7 8-9 13 1 5 3 9 4 14 0 5-1 9-4 13-2 3-6 7-10 9 1-1 3-3 4-5 0-1 0-2 1-3v1c3-3 2-6 2-9-1-4-3-5-6-7-1 0-1 0-2-2 2 1 5 1 6 0v-1h1c-2-6-2-11-1-16 0-2 1-3 3-4h0 1l1-1h1c3-4 6-8 9-10l1-1c1 0 2-1 4-2 1 0 1 0 2-1h1c3-2 2-6 5-8 0-6 2-13 3-18l-1-1c0-1 1-4 1-5l1-10c1-5 1-10 2-15 0-2 1-3 0-6 0 0 1-2 1-3 1 2 0 6 1 8l1-1v1c0-6 1-11 0-16 0-6-1-11-1-16 1-4 3-8 6-10 2-3 6-4 7-8z"></path><path d="M829 486c2 1 5 3 6 4l-10-1c2-1 2-1 3-1h-1l-1-1h0 2l1-1z" class="D"></path><path d="M799 504v1c1-1 1-1 1-2l2-2h1s1 0 2-1c-3 4-6 7-6 12v1l-2-2c0-2 0-5 2-7z" class="N"></path><path d="M826 454c1-2 1-4 1-5 1-3 2-4 4-6l1 1-6 16-1-1c0-1 1-4 1-5z" class="F"></path><path d="M831 428l1-1v1h0c1 4 1 7 4 10 2 1 4 1 6 1v1c-5 0-7 1-10 4l-1-1c1-5 0-10 0-15z" class="E"></path><path d="M830 420c1 2 0 6 1 8 0 5 1 10 0 15-2 2-3 3-4 6 0 1 0 3-1 5l1-10c1-5 1-10 2-15 0-2 1-3 0-6 0 0 1-2 1-3z" class="R"></path><defs><linearGradient id="L" x1="806.864" y1="504.503" x2="825.198" y2="481.665" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#a7a6a5"></stop></linearGradient></defs><path fill="url(#L)" d="M818 486c3-2 2-6 5-8 2 3 4 6 6 8l-1 1h-2 0l1 1h1c-1 0-1 0-3 1-8 0-12 4-18 9l-2 2c-1 1-2 1-2 1h-1l-2 2c0 1 0 1-1 2v-1l1-4h1c3-4 6-8 9-10l1-1c1 0 2-1 4-2 1 0 1 0 2-1h1z"></path><path d="M795 505c0-2 1-3 3-4h0 1l1-1-1 4c-2 2-2 5-2 7l2 2c2 6 5 13 3 19-2 4-4 8-8 10 0-1 0-2 1-3v1c3-3 2-6 2-9-1-4-3-5-6-7-1 0-1 0-2-2 2 1 5 1 6 0v-1h1c-2-6-2-11-1-16z" class="C"></path><path d="M795 505c0-2 1-3 3-4h0 1l1-1-1 4c-2 2-2 5-2 7 1 7 3 12 4 18-2-2-4-7-5-8-2-6-2-11-1-16z" class="D"></path><path d="M788 522h1c1 2 1 2 2 2 3 2 5 3 6 7 0 3 1 6-2 9v-1c1-3 2-6 1-9-1-2-2-3-4-3-5-2-10 1-14 3-9 6-16 13-25 18-5 3-10 5-15 7-3 1-8 3-9 7s0 15 3 18c1 2 4 2 6 2 5 0 11-4 14-8l4-5c-2 7-5 13-12 17-5 3-11 4-16 2-7-2-10-8-13-13h0c-3-3-5-5-8-7h1s1 0 2 1c0 0 1 0 1 1l1-1-1-2h1l3-1c2 0 3-1 5-1h3v-4l1-1v-2c0-2 1-3 2-4 8-5 17-9 25-13s14-9 20-13c1 1 1 0 1 1 3-2 6-3 10-5 2 0 4 0 6-1v-1z"></path><path d="M788 522h1c1 2 1 2 2 2-5 0-9 2-13 5-8 5-15 12-24 17-5 3-10 5-16 7l-2-2c6-2 13-4 18-8 6-4 12-9 18-14 3-2 6-3 10-5 2 0 4 0 6-1v-1z" class="R"></path><path d="M771 528c1 1 1 0 1 1-6 5-12 10-18 14-5 4-12 6-18 8l2 2c-4 2-9 3-10 7-2 5 0 13 1 18v3c-1-1-1-1-2-1-4-6-3-14-3-20v-2c0-2 1-3 2-4 8-5 17-9 25-13s14-9 20-13z" class="S"></path><defs><linearGradient id="M" x1="728.956" y1="553.585" x2="733.825" y2="578.457" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#919293"></stop></linearGradient></defs><path fill="url(#M)" d="M736 551l2 2c-4 2-9 3-10 7-2 5 0 13 1 18v3c-1-1-1-1-2-1l-1-3c-1-5-3-16-1-20 2-3 8-5 11-6z"></path><path d="M707 568h1s1 0 2 1c0 0 1 0 1 1l1-1-1-2h1l3-1c2 0 3-1 5-1h3v-4l1-1c0 6-1 14 3 20 1 0 1 0 2 1v-3s1 1 1 2c1 2 3 2 5 3 6 1 12-2 16-6h0v1c-1 2-7 6-9 7-3 2-6 2-10 2-7 0-10-3-14-9l-3-3h0c-3-3-5-5-8-7z" class="C"></path><path d="M297 403h1c0 1-1 3 0 4h0 2l-1 1v8 2c-1 5 0 10 1 15 3 16 10 32 20 45l3 3c6 7 15 16 23 21 5 2 9 5 13 7s8 3 12 5c1 1 3 2 4 2 0 1 0 1 1 2-1 1-1 1-2 1-4-1-9-2-13-3l-7-2-7-1c-2-1-2-1-4-1h-1c-1 0-3-1-4 0 0 0-1 0-1 1-1 0-1-1-2 0h-3c-1 1-3 1-4 1 0 0-1 1-2 1h0-1v-1c-2 0-4 1-6 1 0 0 1-1 2-1h0l-1-1c5-1 9-3 13-5l6-1c-8-2-15-7-22-11-4-3-8-5-12-8 0-2-2-2-3-3s-1-2-1-2c0-1 0-2 1-3s2-1 4-1h0l1-1v1 1-2l1-1 1 1v-1h1c-12-23-20-48-13-74z" class="T"></path><path d="M310 477c3 5 7 10 11 14-2 0-2 0-4-1l-1-1c-2-1-4-3-5-5h-1l-3-6 1-1 1 1v-1h1z" class="G"></path><path d="M297 403h1c0 1-1 3 0 4h0 2l-1 1v8c-4 20 5 45 16 61 4 7 9 13 15 18 4 2 8 5 11 8l-20-12c-4-4-8-9-11-14-12-23-20-48-13-74z" class="U"></path><path d="M725 431c1 2 1 6 2 7-1 9-4 19-8 28-1 3-3 6-4 8l-1 1c0 2 0 3-1 4h1c1-1 1-1 2-1h0c3 0 5-1 6 1 2 2 2 4 3 6-2 3-7 5-10 7l-6 3-8 5-6 3-13 7c2 0 5-1 7 0 4 1 9 3 13 3l-1 1h0 2c1 0 2 1 3 2 5 0 12-1 17 1-2 0-5 1-7 2h-10l-20-5c-4 0-7-1-10 0h-5c-7 1-16 2-22 5l-3-2h-1c5-4 12-5 17-8h1l14-8c23-16 40-39 46-66h1v5c1-3 1-6 1-9z" class="T"></path><path d="M690 496c4-2 7-6 10-9l2 2-1 1h1c-2 2-5 3-8 5-1 0-2 1-4 1z" class="M"></path><path d="M695 503l2-3c4-3 8-7 14-8l-2 3-8 5-6 3z" class="G"></path><path d="M716 478c3 0 5-1 6 1 2 2 2 4 3 6-2 3-7 5-10 7l-6 3 2-3c0-1 2-1 2-2l10-6v-1c0-2-1-3-2-4-2 0-3-1-4-1h-1z" class="F"></path><path d="M703 489h2c-4 8-16 11-23 15-1 1-2 1-3 2v-1h0c2-2 6-5 10-6l-1-1 2-2c2 0 3-1 4-1 3-2 6-3 8-5l1-1z" class="B"></path><path d="M686 514c2 0 3-1 5 1 1-1 1-1 2 0h2 0 4c1 1 1 1 2 1h1l-3-1c-2 0-3 0-4-1 3 0 7 2 11 2 5 0 12-1 17 1-2 0-5 1-7 2h-10l-20-5z" class="P"></path><path d="M712 472v1c0 1-1 2-1 3l1 1c1-1 1-2 2-3h1l-1 1c-2 3-4 7-7 9 1 1 2 2 2 3-1 1-3 2-4 2h-2l-1 1h-1l1-1-2-2c5-4 8-10 12-15z" class="J"></path><path d="M702 489c0-2 0-3 1-4h3c-1 2-2 3-3 4l-1 1h-1l1-1zm23-58c1 2 1 6 2 7-1 9-4 19-8 28-1 3-3 6-4 8h-1c-1 1-1 2-2 3l-1-1c0-1 1-2 1-3v-1c6-10 10-21 12-32 1-3 1-6 1-9z" class="H"></path><path d="M217 259h1c1 0 2 1 4 1 3 0 6 3 8 6 2 2 3 5 2 8l-3 10c1-1 2-1 3-1-1 1-1 3-2 4h-1c-3 3-4 8-6 13v1l-1 2h-1v1c-3 4-6 10-7 16l-2 3c-2 4-3 8-4 12-1 2 0 4-2 6v1c-1 2-2 3-3 5h0v1c1-1 2-2 3-4l3-3c0 1 0 5-1 6s-1 2-1 3c0 2-1 5-2 7 0 3-1 6-3 9-4 2-7 9-11 13l-1-1h-1l-1-1c5-4 9-9 9-16s-9-10-14-13c-2-2-4-4-5-6 4 2 9 5 14 5 2 0 4 0 7-2 2-2 3-5 5-8 2-5 4-10 4-15 0-6-16-12-20-15-3-2-6-5-9-8 5 2 9 5 14 6h1c4 1 8 2 12 0 4-1 7-4 9-8 3-8-3-17-5-24 0-3 3-7 5-10 1-1 2-3 2-4z"></path><path d="M220 292c4-8 8-16 7-26l-3-4c2 0 3 1 4 3 2 2 2 4 2 7-1 8-4 15-9 21l-1-1z" class="O"></path><defs><linearGradient id="N" x1="205.349" y1="353.65" x2="190.528" y2="348.913" xlink:href="#B"><stop offset="0" stop-color="#929190"></stop><stop offset="1" stop-color="#b4b2b1"></stop></linearGradient></defs><path fill="url(#N)" d="M210 320v1 3h1l1-2v1c-2 4-3 8-4 12-1 2 0 4-2 6v1c-1 2-2 3-3 5h0v1c1-1 2-2 3-4l3-3c0 1 0 5-1 6s-1 2-1 3c0 2-1 5-2 7 0 3-1 6-3 9-4 2-7 9-11 13l-1-1 4-6c3-3 5-9 5-13-1-6-11-10-15-13 2 1 4 2 7 2s6 0 8-3c2-1 3-3 4-5 3-6 6-13 7-20z"></path><defs><linearGradient id="O" x1="226.692" y1="307.303" x2="195.698" y2="312.053" xlink:href="#B"><stop offset="0" stop-color="#8c8b88"></stop><stop offset="1" stop-color="#b2b1b0"></stop></linearGradient></defs><path fill="url(#O)" d="M229 284c1-1 2-1 3-1-1 1-1 3-2 4h-1c-3 3-4 8-6 13v1l-1 2h-1v1c-3 4-6 10-7 16l-2 3v-1l-1 2h-1v-3-1c-1-3-5-5-8-7l-15-8c7 1 13 4 19 2 7-2 10-9 14-15l1 1c-1 3-5 7-5 10h0c3-4 6-7 9-12 1-2 2-5 4-7z"></path><path d="M794 262c3-2 6-4 10-4v2c3 4 6 9 9 15-3 5-7 11-8 18 1 4 3 9 7 12 6 2 15 1 20-1l10-5c-3 4-7 7-11 9-4 3-18 8-18 14 0 3 1 7 2 10 2 4 4 9 7 13 2 2 5 2 8 2 1 0 2 0 3-1h1l9-4c-3 5-9 7-14 10-2 1-5 3-6 4s-1 2-1 2c0 6 6 17 11 21 1 1 3 2 6 2l3-1h0l-1 1c-1 1-4 1-5 1-6-2-10-10-13-15l-1 2c-2-4-3-7-4-10s-2-5-3-7c-3-4-4-8-5-13-2-4-4-9-5-14-1-4-1-9-3-13l-15-36c-1-5-3-9-4-14l2 2v1l1 2h1l7-5z"></path><path d="M794 263c1-1 3-1 4 0l-4 3c0-1 0-1-1-2l1-1z" class="N"></path><path d="M794 262c3-2 6-4 10-4v2c-2 0-4 1-6 3-1-1-3-1-4 0v-1z" class="U"></path><path d="M818 359c1-2 1-4 3-5l1 1c-1 1-1 2-1 3 0 3 1 7 2 9h0l-1 2c-2-4-3-7-4-10z" class="D"></path><defs><linearGradient id="P" x1="816.726" y1="354.55" x2="825.694" y2="350.989" xlink:href="#B"><stop offset="0" stop-color="#888786"></stop><stop offset="1" stop-color="#a1a09f"></stop></linearGradient></defs><path fill="url(#P)" d="M820 350c2 0 1 1 2 0l-5-5c-2-1-3-2-4-3l1-1c1 1 3 2 4 4l1-2c3 3 5 5 10 5 1 0 2 0 4-1v1c0 1-5 3-6 4-2 0-3 2-5 3l-1-1c-2 1-2 3-3 5-1-3-2-5-3-7 2 0 4 0 5-2z"></path><path d="M818 345l1-2c3 3 5 5 10 5l-1 1c-1 1-3 1-4 1-2-2-4-4-6-5z" class="K"></path><path d="M794 262v1l-1 1c1 1 1 1 1 2v6c2 11 8 20 13 30h1c4 4 7 6 13 5 4 0 8-1 12-2-5 4-21 9-22 16 0 1 1 3 1 5 1 6 4 12 7 17l-1 2c-1-2-3-3-4-4l-1 1c1 1 2 2 4 3l5 5c-1 1 0 0-2 0-1 2-3 2-5 2-3-4-4-8-5-13-2-4-4-9-5-14-1-4-1-9-3-13l-15-36c-1-5-3-9-4-14l2 2v1l1 2h1l7-5z" class="E"></path><path d="M810 339c1 1 2 2 3 4s0 7 3 7h4c-1 2-3 2-5 2-3-4-4-8-5-13z" class="D"></path><path d="M794 262v1l-1 1c-2 1-3 2-3 4 0 8 8 28 13 34 0 1 3 4 3 5h0c-2-2-4-7-7-8v1h0c-4-9-8-19-11-28-1-2-3-5-3-7l1 2h1l7-5z" class="H"></path><defs><linearGradient id="Q" x1="785.789" y1="273.161" x2="808.532" y2="289.709" xlink:href="#B"><stop offset="0" stop-color="#696967"></stop><stop offset="1" stop-color="#9c9b9c"></stop></linearGradient></defs><path fill="url(#Q)" d="M793 264c1 1 1 1 1 2v6c2 11 8 20 13 30l-1-1-3-3v1 3c-5-6-13-26-13-34 0-2 1-3 3-4z"></path><path d="M297 403c4-24 20-46 40-60 7-6 16-11 25-14 27-11 60-9 88-4l-37 8-1-1c-9 2-17 6-23 14h-1c-3 4-6 8-7 14l-1 1v3h-1v-1c-1-1-2-3-3-4h-2v1h-2-1c-2-1-3-2-4-4 1 0 1 0 2 1v1c1 1 1 1 3 1v-1c-1 0-2 0-3-1 1 0 4 1 5 0 1 0 4-2 4-2 3-2 4-5 6-7-3 3-6 6-9 7h0c3-3 10-8 10-13 1-2 0-4-1-6l-1-1h0c2 1 3 3 5 4h0c6-8 14-10 22-13-8 1-15 2-23 4-10 2-19 5-28 9-4 2-9 4-12 7-1 1-2 3-2 5s1 4 1 6c1 4-1 8-3 11l-7 8c2-5 4-11 2-16-1-1-1-2-3-1-3 0-5 3-7 5-7 10-10 20-8 32 1 9 5 19 12 26 1 0 0 0 1 1h-1-1c-1-1-2-1-4-1h0c-3-4-5-8-6-12l-3-4v-5c-1-3-2-8-1-11 1-2 0-3 0-5v-5l-2-2-1 2-1-1 4-13c-7 9-13 20-16 31-1 3-2 6-3 10h0c-1-1 0-3 0-4h-1z"></path><path d="M322 408c2 4 3 7 5 10 1 1 4 3 5 5h-1c-1-1-2-1-4-1h0c-3-4-5-8-6-12l1-2z" class="G"></path><path d="M317 390c1 3 2 6 3 10 0 2 2 6 2 8l-1 2-3-4v-5c-1-3-2-8-1-11z" class="P"></path><path d="M345 340c18-14 41-17 63-17h20c-2 1-6 1-9 1-8 0-16-1-24 0-18 2-33 7-48 17l-2-1z" class="T"></path><defs><linearGradient id="R" x1="418.538" y1="332.989" x2="416.063" y2="327.054" xlink:href="#B"><stop offset="0" stop-color="#c6c3c5"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#R)" d="M388 346c3-4 5-7 9-10 7-6 15-7 23-9l12-3c4 0 8 1 12 1l-32 7c-9 2-17 6-23 14h-1z"></path><path d="M345 340l2 1c-3 2-5 5-6 8 2 6 4 11 1 17-1 2-1 3-2 4 1-4 2-9 1-13-1-1-3-3-5-3-2-1-5 0-7 2-8 5-10 13-14 22l-1 2-1-1 4-13c-7 9-13 20-16 31-1 3-2 6-3 10h0c-1-1 0-3 0-4l1-2c5-26 24-47 46-61z" class="G"></path><path d="M614 334c-10-4-22-6-32-8 11-2 25-5 37-4 7 1 13 1 19 2 17 3 32 11 46 21 6 5 12 8 17 14 6 7 13 17 17 26l-2 1 1 2v1 2l-1 1-2-2h0c-3-5-6-11-8-17-1-3-3-5-5-8 1 3 3 5 4 8 5 13 5 25 0 38v-1c2-2 3-2 6-2 2 0 3 1 5 3 1 1 1 2 2 3h-1v-1c-2-2-3-3-6-3-2-1-3 0-4 1-3 3-5 7-5 11 0 2 0 6 2 8 2 0 3 0 5-2v-1c0 2 0 2-1 4-2 0-3 0-5 1 0 2 1 5 2 7 2 9 4 20 1 30-1-15-4-31-12-43h-7c1-1 2-1 2-2l2-1c2-2 3-4 5-7 3-7 7-16 7-24 0-5-1-10-3-15-1-6-4-12-9-16-1-1-4-2-6-2s-2 1-3 2c0 5 2 9 4 13-1 0-2-1-3-2-2-2-5-6-6-9-1-4 0-8 0-11-2-4-4-6-7-8-7-6-16-9-24-11-12-4-25-7-37-5 5 2 10 2 15 6 2 1 4 4 6 5-1-2-5-4-5-7v-1l9 10c0-1 1-2 1-3 1-2 4-4 6-5h2c-2 1-4 3-6 5-1 1-1 2-1 4 1 4 9 12 13 15 0-1 1-1 2-1 1-2 1-2 1-4h0c1 1 1 1 1 3-1 1-2 2-4 3v1h3c1-1 1-1 2 0-2 1-4 2-6 2h0l-2 2v-2c0-1-1-1-2-1-1-1-2-2-2-3-2-3-4-5-6-8-1-1-1-2-2-3h-1c-5-5-12-9-19-12z"></path><path d="M614 334c-10-4-22-6-32-8 11-2 25-5 37-4v1c-10 0-19 2-29 3 3 0 6 0 9 1 14 2 26 7 35 19h-1c-5-5-12-9-19-12z" class="G"></path><path d="M700 377h1l2-4v1c1 0 1 1 1 2l3 9v1 1c1 1 1 6 0 7l-1 5c0 2 0 4-1 5l-2 5h0-1l1-2v-1-2c-2 6-4 12-9 18h3c2-2 4-6 6-8-1 2-3 5-3 7-1 7 9 35 7 38-1-1-1-2-1-2 0-1 0-2-1-3-1-7-3-13-6-20-1-3-3-7-5-9-1-1-2-1-3-1h-2l2-1c2-2 3-4 5-7 3-7 7-16 7-24 0-5-1-10-3-15z" class="I"></path><path d="M700 377h1l2-4v1c1 0 1 1 1 2l3 9v1 1c1 1 1 6 0 7l-1 5c0 2 0 4-1 5l-2 5h0-1l1-2v-1-2l2-6v-1-10h-1c0 3 0 7-1 11v1c-2 8-5 18-11 24h-1c2-2 3-4 5-7 3-7 7-16 7-24 0-5-1-10-3-15z" class="H"></path><path d="M619 322c7 1 13 1 19 2 17 3 32 11 46 21 6 5 12 8 17 14 6 7 13 17 17 26l-2 1c-5-10-12-21-21-29-3-2-6-3-10-3-1 0-2 1-3 2-1 3-2 6-2 8v2-1c-1-1-1-3-1-4 1-2 1-4 1-6 1-5 1-7-2-11-9-10-32-17-45-19-5-1-10-1-14-2v-1z" class="T"></path><defs><linearGradient id="S" x1="728.608" y1="529.774" x2="594.561" y2="470.093" xlink:href="#B"><stop offset="0" stop-color="#a6a5a3"></stop><stop offset="1" stop-color="#dcdbdb"></stop></linearGradient></defs><path fill="url(#S)" d="M596 476h0v-9c1-1 1-2 1-3 2-9 8-18 17-22 7-5 15-5 23-3l5 2v2l2 1v1h-1c-7-2-13-2-20 0l-1 1v1c-7 5-11 10-13 18-1 9 0 18 6 25 4 6 9 9 14 12 2-6 4-11 10-14 4-2 10-4 15-2a10.85 10.85 0 0 1 8 8l1 1-4-4c-2-1-5-2-7-2s-5 2-7 4l-2-1c-2 2-4 4-5 7-1 8 3 16 7 22 6 7 17 12 25 15l6 2 3 1c14 4 28 3 42 0h1 3 0l-2 1c-1 1-1 1-2 1-1 2-3 2-4 3-5 2-9 3-14 5-2 0-4 1-5 1-3 1-6 3-8 5 0 1 0 2 1 3h1c1 0 3 1 4 2l2 1h2c1 1 2 2 4 3 0 0 1 0 1 1l1 1c-1 0-1 0-2 1-12-5-24-8-36-12-9-4-17-8-25-14l-10-8c-3-2-6-5-8-7s-5-4-7-7c-4-6-9-12-13-18l-2-3c-1-3-3-6-4-9l-2-3v-1c-1-2-1-4-1-5-1-2-1-3 0-4z"></path><path d="M637 439l5 2v2c-2 0-3-1-5-1-3-1-8 0-12 0h-1l2-1h-3c4-1 11 0 14-2z" class="I"></path><path d="M625 442c4 0 9-1 12 0 2 0 3 1 5 1l2 1v1h-1c-7-2-13-2-20 0l4-2v1l1-1h2 0-4-1v-1z" class="G"></path><path d="M622 446v1c-7 5-11 10-13 18-1 9 0 18 6 25 4 6 9 9 14 12 2-6 4-11 10-14 4-2 10-4 15-2a10.85 10.85 0 0 1 8 8l1 1-4-4c-2-1-5-2-7-2s-5 2-7 4l-2-1 3-2c3-1 6-2 10-1h2c-1 0-1 0-1-1-3-2-8-2-11-1-5 1-10 4-13 8s-3 11-1 16v1c-9-5-15-10-20-19-5-8-8-18-5-27 2-9 7-15 15-20zM439 293h3 0c4 1 7 1 11 1 2 7 2 14 2 22v41c0 8 1 16 0 25-1 3 0 8 0 12 0 1 0 2-1 3v-16-1-38-16c0-3 0-7-1-10-1-6-5-11-8-15-1 2-4 7-7 7-4 1-8 1-12 1-10 0-20-1-29 0-49 5-97 31-125 72-16 22-24 55-19 82 4 22 13 44 32 57l4 3h-1c-4-1-15-9-18-13-20-18-30-45-30-72-1-32 15-66 38-89 11-12 24-21 39-29 22-13 46-23 72-26 16-2 33-1 50-1zm132 0l58 2c46 5 93 24 122 61 22 27 34 61 30 96-2 21-10 40-24 55-2 2-4 5-7 7-1 1-4 2-4 4-3 2-11 8-14 8 5-4 10-8 14-13 13-14 19-32 22-51 4-33-8-66-28-91-27-35-70-57-114-62-13-1-27 1-40-1-3-2-5-5-7-8-1 1-2 3-3 4-3 6-5 12-6 19-1 4 0 8 0 13v23 84c-1-2-1-5-1-7v-80-45c0-6 0-12 2-18z"></path><path d="M442 293h15 9l-5 1c-3 4-2 84-2 94v23c0 3 0 7-1 11v1c2 12 1 26 1 38v74 16c0 3-1 6 0 9h1v1c1 1 1 0 2 0 1 1 2 3 2 3v1h3v1 1l2-1 1 1-2 1s-1-1-1 0h-4l1-1 1-1c-3 0-4-1-6-3-1 3 0 6 0 9v23 84 23c0 5 0 11-1 16-1 7-4 12-6 18-12 34-38 67-72 81l-7 3c15 0 30 1 45 6 23 7 43 23 60 40 15 17 26 36 35 57 2-7 5-13 8-19 10-20 23-36 39-51 8-7 16-14 25-19 20-11 44-15 67-15-4-2-9-4-14-6s-10-6-14-9c-19-12-33-31-43-51-6-12-11-24-13-37-2-8-2-16-2-24-1-9 0-107-1-109l-1-1c-1 0-1 0-2-1h-1l-9-9-5-7h1c1 0 2 0 2-1h1v1c2 0 1 1 3 1h-1v-1-1c0-2 0-3-1-4l4 2 3-1h6c1-2 0-6 0-8v-30-151-61c0-6-1-12-2-18h8c-2 6-2 12-2 18v45 80l-1 63v18 6h1v-2c1 0 0 1 1 1v1 13 11c-1 3-1 7-1 10v20 10 75 28c0 9 0 19 2 28 2 6 4 12 6 19 8 18 18 35 32 50 15 16 34 27 55 33 5 2 10 4 15 4v1c-16 0-31-4-46-1-14 1-29 4-42 11-17 8-33 24-46 38-7 8-13 17-18 26-4 8-8 15-10 23-2 6-3 13-5 19-2-34-28-66-53-87-9-8-18-15-28-20-19-9-44-12-64-11l-18 2c2-2 6-2 9-3l13-5c7-3 14-5 20-9 8-4 16-11 22-17 20-20 33-43 41-70l1-7c1-5 0-10 1-15 0-6 0-12-1-18v-32c0 2 0 4-1 6V550 427v-47 1 16c1-1 1-2 1-3 0-4-1-9 0-12 1-9 0-17 0-25v-41c0-8 0-15-2-22-4 0-7 0-11-1h0z" class="O"></path><path d="M568 523h1v-2c1 0 0 1 1 1v1 13 11c-1 3-1 7-1 10v20 10l-1-64zM454 380v1 16c1-1 1-2 1-3 0-4-1-9 0-12v50c0-4 1-11-1-14v8 1-47z" class="H"></path><path d="M552 560l4 2 3-1h6v1 19h-2-1-1l-9-9-5-7h1c1 0 2 0 2-1h1v1c2 0 1 1 3 1h-1v-1-1c0-2 0-3-1-4z"></path><path d="M559 561h6v1c-2 2-4 3-6 4v-5z" class="F"></path><path d="M556 562l3-1v5c1 1 3 0 4 1-2 1-3 1-5 2 0-3-1-5-2-7z" class="D"></path><path d="M554 570c4 3 6 8 9 11h-1-1l-9-9c2-1 2-1 2-2z" class="R"></path><path d="M552 560l4 2c1 2 2 4 2 7 0 1 1 2 2 2h2v1c-3 0-5-1-8-3v1c0 1 0 1-2 2l-5-7h1c1 0 2 0 2-1h1v1c2 0 1 1 3 1h-1v-1-1c0-2 0-3-1-4z" class="B"></path><path d="M454 427v-1-8c2 3 1 10 1 14v217c0 2 0 4-1 6V550 427z" class="L"></path><defs><linearGradient id="T" x1="783.34" y1="543.633" x2="687.004" y2="269.323" xlink:href="#B"><stop offset="0" stop-color="#a7a6a3"></stop><stop offset="1" stop-color="#eeeded"></stop></linearGradient></defs><path fill="url(#T)" d="M775 261v-5l-3-9c-1-2-1-4-1-7l1 2v1c1 1 1 3 2 5 1 3 2 9 5 11-2-6-6-12-6-18h0l2 3 8 18c1 5 3 9 4 14l15 36c2 4 2 9 3 13 1 5 3 10 5 14 1 5 2 9 5 13 1 2 2 4 3 7s2 6 4 10l1-2c3 5 7 13 13 15 1 0 4 0 5-1l1-1h0c0-1 1-1 2-2-1 4-5 5-7 8-3 2-5 6-6 10 0 5 1 10 1 16 1 5 0 10 0 16v-1l-1 1c-1-2 0-6-1-8 0 1-1 3-1 3 1 3 0 4 0 6-1 5-1 10-2 15l-1 10c0 1-1 4-1 5l1 1c-1 5-3 12-3 18-3 2-2 6-5 8h-1c-1 1-1 1-2 1-2 1-3 2-4 2l-1 1c-3 2-6 6-9 10h-1l-1 1h-1 0c-2 1-3 2-3 4-1 5-1 10 1 16h-1v1c-1 1-4 1-6 0h-1v1c-2 1-4 1-6 1-4 2-7 3-10 5 0-1 0 0-1-1-6 4-12 9-20 13s-17 8-25 13c-1 1-2 2-2 4v2l-1 1v4h-3c-2 0-3 1-5 1l-3 1h-1l1 2-1 1c0-1-1-1-1-1-1-1-2-1-2-1h-1l-3-1c1-1 1-1 2-1l-1-1c0-1-1-1-1-1-2-1-3-2-4-3h-2l-2-1c-1-1-3-2-4-2h-1c-1-1-1-2-1-3 2-2 5-4 8-5 1 0 3-1 5-1 5-2 9-3 14-5 1-1 3-1 4-3 1 0 1 0 2-1l2-1h0-3-1c15-4 31-15 41-28-1 0-1 1-2 2-7 7-14 12-23 16-3 2-6 4-10 4h0l4-3c-1 0-2 1-3 0-1 1-2 1-4 1 3-2 6-3 8-5 3 0 11-6 14-8 0-2 3-3 4-4 3-2 5-5 7-7 14-15 22-34 24-55 4-35-8-69-30-96-29-37-76-56-122-61l3-1h0c1-2 1-2 3-3v-1l26 4c1 1 3 1 4 1l7 2 6 2c1 1 2 0 2 0 1 1 2 2 4 2l2 1h2c1 1 2 2 4 2l1 1h1l2 1 11 5 10 6c1 1 2 1 2 1 2 0 2-1 3-1l5 2 8 6 2-2c2 1 2 2 3 3l1-1h0l1 1h-1l1 1s0 1 1 2l-1 1c3 4 7 7 9 10l10 14c3 4 7 8 9 13h0c3 5 6 11 8 17v1l3-8c0 3-1 5-1 7-1 2-2 3-1 6 1 2 2 4 3 5 8 23 13 48 12 73h-2c0 10 2 18-5 25l-1 2h1l1 4c-2 2-3 4-5 6 4-3 8-6 11-10 12-16 16-37 20-56 1-8 3-16 3-23 0-10-7-22-13-30v-1c3 3 8 12 11 12h0c1-2 1-4 1-5-1-4-1-8-2-11-2-5-5-9-6-14-1-4-1-9-3-14l-4-13c-2-7-3-15-6-22l-12-30-8-27z"></path><path d="M821 454l1 1h0c1-1 1-2 1-3 1-1 1-2 2-3l-3 12c-1 1-1 2-2 4h0c0 1 0 2-1 2h0c0-5 1-9 2-13z" class="M"></path><path d="M792 467c-1 6-2 13-6 18v-2c1-1 1-1 1-2l-1 1 3-9c0-1 0-4 1-4 0-1 1-2 2-2z" class="B"></path><path d="M819 467h0c1 0 1-1 1-2h0c1-2 1-3 2-4 0 5-2 10-3 16-1 2-1 6-3 8v-1c0-1 1-4 1-6 1-4 1-8 2-11z" class="H"></path><path d="M732 526c3 0 11-6 14-8l-2 2h2 0c-4 4-10 6-15 10-1 0-2 1-3 0-1 1-2 1-4 1 3-2 6-3 8-5z" class="P"></path><path d="M751 340l10 14c3 4 7 8 9 13h-1v-1l-1-1c-1 0 0-1-1-1v-1l-1-1c-1-1-1-1-1-2h0c-1-1-1-1-1-2-1 0-1-1-2 0l5 8h-1c0-1-1-3-2-4l-7-11-4-4c-1-2-1-2-2-3h0v-4z" class="G"></path><defs><linearGradient id="U" x1="822.42" y1="437.494" x2="827.58" y2="441.006" xlink:href="#B"><stop offset="0" stop-color="#141115"></stop><stop offset="1" stop-color="#21231f"></stop></linearGradient></defs><path fill="url(#U)" d="M829 423c1 3 0 4 0 6-1 5-1 10-2 15-1 1-2 5-2 5-1 1-1 2-2 3 0 1 0 2-1 3h0l-1-1c1-4 2-7 3-11 1-7 2-13 5-20z"></path><defs><linearGradient id="V" x1="817.946" y1="469.788" x2="824.668" y2="474.288" xlink:href="#B"><stop offset="0" stop-color="#737370"></stop><stop offset="1" stop-color="#888788"></stop></linearGradient></defs><path fill="url(#V)" d="M825 449s1-4 2-5l-1 10c0 1-1 4-1 5l1 1c-1 5-3 12-3 18-3 2-2 6-5 8l-2-1c2-2 2-6 3-8 1-6 3-11 3-16l3-12z"></path><path d="M788 424c0 1 0 1 1 2v2 2c1 3 2 8 2 12 1 8 3 18 1 26 0 10 2 18-5 25l-1 2h-1c-1 0-2 1-3 0 1-3 3-7 4-10 4-5 5-12 6-18 0-10-1-19-2-29-1-4-1-9-2-14z"></path><path d="M722 317l5 2 8 6 2-2c2 1 2 2 3 3l1-1h0l1 1h-1l1 1s0 1 1 2l-1 1c3 4 7 7 9 10v4h0c-1 0-5-3-5-4-1-2-4-6-6-7-3-1-6-5-9-7l-1-1h-1l-2-2h0l-4-3c-1 0-2-1-4-2 2 0 2-1 3-1z" class="P"></path><path d="M737 323c2 1 2 2 3 3l1-1h0l1 1h-1l1 1s0 1 1 2l-1 1-7-5 2-2z" class="L"></path><path d="M795 505c-1 5-1 10 1 16h-1v1c-1 1-4 1-6 0h-1v1c-2 1-4 1-6 1-4 2-7 3-10 5 0-1 0 0-1-1 4-3 8-5 11-8 5-4 9-10 13-15z" class="J"></path><path d="M786 519c1-3 4-7 6-10l1 9c-2 0-3-1-5 0l-2 1z" class="O"></path><path d="M786 519l2-1c2-1 3 0 5 0l2 2v1 1c-1 1-4 1-6 0h-1v1l-7-1c1-1 3-2 5-3z" class="C"></path><path d="M788 522v-1c2-1 5-1 7-1v1 1c-1 1-4 1-6 0h-1z" class="B"></path><path d="M779 390c1 2 2 4 3 5 8 23 13 48 12 73h-2c2-8 0-18-1-26 0-4-1-9-2-12v-2-2c-1-1-1-1-1-2-1-1-1-2-2-4l-4-14c-1-5-5-11-4-15v-1h1z" class="E"></path><path d="M779 259c-2-6-6-12-6-18h0l2 3 8 18c1 5 3 9 4 14l15 36c2 4 2 9 3 13 1 5 3 10 5 14 1 5 2 9 5 13 1 2 2 4 3 7s2 6 4 10c2 3 5 7 7 10v1c-3-3-5-6-7-9-1-2-1-3-2-4-3-4-4-9-6-13v-1 1 1c3 15 9 29 11 44 0 5 1 9 0 14 0-1 0-1-1-2 1-6-1-13-2-19-4-18-9-35-14-53-3-8-4-18-7-26l-10-24-12-30z" class="M"></path><path d="M824 411c1 1 1 1 1 2 1-5 0-9 0-14-2-15-8-29-11-44v-1-1 1c2 4 3 9 6 13 1 1 1 2 2 4 2 3 4 6 7 9v-1c-2-3-5-7-7-10l1-2c3 5 7 13 13 15 1 0 4 0 5-1l1-1h0c0-1 1-1 2-2-1 4-5 5-7 8-3 2-5 6-6 10 0 5 1 10 1 16 1 5 0 10 0 16v-1l-1 1c-1-2 0-6-1-8v-1-2-3h1v2c0-2 1-6 1-7-1 0-1-1-1-1 0-2 0-2-1-4 0 1-1 2-1 3-1 1-1 2-1 2l-1 1h2c0 1 0 2-1 3 0 2 0 4-1 6l-1 1-1 1v1h-1v-11z" class="C"></path><path d="M825 421c-1-1 0-4 0-6h1l1-2c1 2 0 5-1 7l-1 1z"></path><path d="M822 369l1-2c3 5 7 13 13 15 1 0 4 0 5-1l1-1h0c0-1 1-1 2-2-1 4-5 5-7 8-3 2-5 6-6 10l-2 5c-1 2-1 3-2 5-1-4-1-10 0-13 1-2 1-4 3-6v-1c-1-1-2 1-3 1-1-1-1-1-1-2-4-4-1-8-4-12v-1-1c2 3 4 6 7 9v-1c-2-3-5-7-7-10z" class="E"></path><defs><linearGradient id="W" x1="205.789" y1="524.31" x2="364.365" y2="331.615" xlink:href="#B"><stop offset="0" stop-color="#9a9895"></stop><stop offset="1" stop-color="#ebeaea"></stop></linearGradient></defs><path fill="url(#W)" d="M238 244c3-2 7-4 10-6 1-1 3-3 4-3-2 9-3 18-6 26l-5 16-17 41-5 16c-1 5-4 10-5 15-1 4-1 8 0 12 1 9-11 17-11 26 0 5 3 9 4 14 4-7 8-14 13-21 2-3 6-5 8-8v-2c-1 1-1 1-1 2-1 1-5 5-7 6v-1l8-8v-2c-3 1-5 2-7 4l-1-1c3-2 5-4 8-5l1-1h0-1l-6 3c2-2 3-3 5-4l1-1c-1 0-1 0-1 1-2 0-3 1-4 1 3-2 7-4 9-8l-8 5c2-2 5-4 7-6l-6 3v-1c2-1 5-3 6-5-2 1-4 2-5 3 2-2 5-4 7-7l-6 4v-1c3-2 6-6 9-7l1-1 1 1c-1 1-2 1-3 2l-2 2c1 0 2 0 3-1h0c0 1 0 1-1 2h1v1l-3 6c0 1 0 2-1 2v1c0 2 0 3-1 5h0c1 3-1 7-3 9-2 3-5 6-7 9-6 7-9 15-13 23-2 7-5 13-4 20 0 7 2 13 4 20 4 16 10 33 19 48 3 4 7 8 11 12-2-4-4-8-5-11s-1-7-2-10c3 3 2 6 7 8h0c0-5-2-9-4-13l-5-15c1-2 2-6 1-9-1-14 1-29 5-42l3-10c1-2 2-5 1-7l2-2v7c-1 6-4 13-5 20l-3 15c-1 8 1 16 1 24 0 1 1 2 1 3v1 1h0c0 2 1 3 1 4 1 2 1 3 2 5l4 7c2 5 4 12 7 17l2 4h0c6 6 12 11 18 16 3 2 7 2 10 4 4 2 7 5 11 6 2 1 4 2 5 2 3 1 8 3 11 5h1c5 2 8 3 13 2 8 3 22 2 30-1 5-1 9-3 13-5v1h0 1c5-2 8-5 11-8h2l-5 5c-1 0-1 1-2 1-3 2 1-1-1 1-1 1-2 1-3 2h0v1l4-3c4-3 8-7 11-10 2-3 3-6 6-7 2 0 1 0 2 1 2-1 2-1 3-2h0v-1-1c10-6 18-13 23-23 4-8 5-17 2-25s-9-14-17-18h-1c-6-2-10-1-16 0h0c-1 0-3 0-4-1l1-2c0-1 1-1 2-2l7-2c9-1 18 1 26 6 6 5 11 13 12 21v1c1 4 1 8 0 12-2 10-6 19-12 27-2 1-3 3-4 5 0 1-1 2-1 3-1 1-2 2-3 4-4 3-7 7-11 10-2 2-3 4-5 5-1 1-2 1-3 2l-1 1c-1 1 1 0-1 0-1 1-2 2-3 2l-1 1c0 1-1 2-2 2-4 1-7 4-11 6-5 3-10 5-15 7-14 6-29 7-43 15h0c-4 1-8 8-10 11h0c-2 2-6 3-8 4l-1 1v2l-1-1c-1 0-1 0-2 1h-4l-1-1-2 1c-1-1 0-1-1-1-2-1-4-2-5-3 1 0 3 0 5 1 1 0 1 1 2 2 3 0 5 0 8-2 4-3 6-8 7-13v-1l-1-1c-1-1-1-7-1-9-1-4-1-4-4-6l-4-2-2 1c-4-1-8-1-12-3-5-2-10-6-14-9-8-6-18-13-28-16l-3 1c-2 3-4 5-4 9l1 1c-1 1 0 3 0 4-3-3-4-7-4-12s5-11 3-17c0-2-2-4-3-6h0-1c-5-6-12-12-18-16h4v-1c-1-1-2-1-2-3-1 0-1 0-2-1l1-6c1-3 1-8 1-11-1-1-1-2-1-3l-1-1v-2c-5-12-7-25-11-37v-1c-2-2-1-9-2-12h0c0-5 0-11-1-16-2-8-10-11-16-15 3 1 6 2 9 2 2 1 5-1 6-2l1-1h1l1 1c4-4 7-11 11-13 2-3 3-6 3-9 1-2 2-5 2-7 0-1 0-2 1-3s1-5 1-6l-3 3c-1 2-2 3-3 4v-1h0c1-2 2-3 3-5v-1c2-2 1-4 2-6 1-4 2-8 4-12l2-3c1-6 4-12 7-16v-1h1l1-2v-1c2-5 3-10 6-13h1c1-1 1-3 2-4-1 0-2 0-3 1l3-10c1-3 0-6-2-8-2-3-5-6-8-6-2 0-3-1-4-1h-1l1-2v1l4-3c5-3 10-8 16-11z"></path><path d="M293 552c2 0 3 1 4 2 1 5 1 10 0 14-1-1-1-7-1-9-1-4-1-4-4-6l1-1z" class="U"></path><path d="M226 522v1c0 1-1 2-1 3-1 2-1 4-1 5l-1 1 1 3v-1c0-1 1-2 1-3v-2c1-2 2-3 3-4h1c-2 3-4 5-4 9l1 1c-1 1 0 3 0 4-3-3-4-7-4-12 2-1 3-3 4-5z" class="F"></path><path d="M262 536c10 6 21 10 31 16l-1 1-4-2c-7-2-13-5-19-9-2-1-7-5-7-6z"></path><path d="M208 347c-1 5-1 10-3 15-2 7-5 13-6 19l-1 14c-1 5-2 10-2 15 0 4 1 8 0 12l-1-3-1-2c0-2-1-3-1-4l2 1h1c0-2 0-4-1-5 0-3 0-6 1-9 0-5 2-11 3-17 0-6 2-11 3-17 2-3 3-6 3-9 1-2 2-5 2-7 0-1 0-2 1-3z" class="L"></path><path d="M383 445h0c-1 0-3 0-4-1l1-2c0-1 1-1 2-2l7-2-2 1c3 2 10 1 13 2 4 1 8 3 12 5v1c-4-1-7-4-10-4-2 0-3-1-4 0l1 1v1c-6-2-10-1-16 0z" class="B"></path><path d="M383 445c1-2 2-2 4-3 4-2 11-1 15 1-2 0-3-1-4 0l1 1v1c-6-2-10-1-16 0zm28 65h-1l1-1v-1l1-1v-1l2-1c0-1-1-2-1-3l-1 1h-1c2-3 5-6 6-9 0-2 1-3 2-5 1 0 1-1 1-1 0-1 1-1 1-1v-1h1c1-1 3-3 4-5 0-1 0-5 1-6v3c-2 10-6 19-12 27-2 1-3 3-4 5z" class="G"></path><path d="M251 502c6 6 12 11 18 16 3 2 7 2 10 4 4 2 7 5 11 6 2 1 4 2 5 2 3 1 8 3 11 5h-4c-3-1-3-1-6-1h-1c-14-5-28-15-38-26-2-1-5-3-6-6z" class="C"></path><path d="M354 553c1-1 1-2 3-3 9-4 17-9 25-15 3-2 5-5 8-7 3-3 6-5 9-8h3c-2 4-8 7-11 11v1c-1 1-2 1-3 2l-1 1c-1 1 1 0-1 0-1 1-2 2-3 2l-1 1c0 1-1 2-2 2-4 1-7 4-11 6-5 3-10 5-15 7z" class="G"></path><path d="M363 531v1c-3 2-6 3-9 4l-7 2v1c-18 4-34 2-51-5 3 0 3 0 6 1h4 1c5 2 8 3 13 2 8 3 22 2 30-1 5-1 9-3 13-5z" class="F"></path><path d="M222 303l1-2v-1c2-5 3-10 6-13h1l-21 54-3 3c-1 2-2 3-3 4v-1h0c1-2 2-3 3-5v-1c2-2 1-4 2-6 1-4 2-8 4-12l2-3c1-6 4-12 7-16v-1h1z" class="N"></path><path d="M214 320c1-6 4-12 7-16v-1h1c-1 1-1 1-1 3l-1 2h0l-4 9v1c-1 1-2 1-2 2z" class="C"></path><path d="M205 484c1 1 2 1 3 2h0c2 1 4 2 6 4h0c4 3 8 8 11 13l8 9c0 1 1 2 2 3h-1-1-2-1c-1 2-2 5-4 7-1 2-2 4-4 5 0-5 5-11 3-17 0-2-2-4-3-6h0-1c-5-6-12-12-18-16h4v-1c-1-1-2-1-2-3z" class="N"></path><path d="M205 484c1 1 2 1 3 2h0c0 1 1 2 2 2 2 2 3 4 6 5 1 1 1 2 2 3s1 2 2 3h0c1 1 1 1 1 2l2 2-1 1h0-1c-5-6-12-12-18-16h4v-1c-1-1-2-1-2-3zm27 40c2-2 4-2 7-3l3 1c1 1 2 1 3 1l9 6 6 5 2 2c0 1 5 5 7 6 6 4 12 7 19 9l-2 1c-4-1-8-1-12-3-5-2-10-6-14-9-8-6-18-13-28-16z" class="F"></path><defs><linearGradient id="X" x1="203.492" y1="388.854" x2="181.438" y2="392.019" xlink:href="#B"><stop offset="0" stop-color="#838281"></stop><stop offset="1" stop-color="#b2b0ae"></stop></linearGradient></defs><path fill="url(#X)" d="M190 378l1 1c4-4 7-11 11-13-1 6-3 11-3 17-1 6-3 12-3 17-1 3-1 6-1 9 1 1 1 3 1 5h-1l-2-1c0 1 1 2 1 4l1 2h-1c-1-1-1-3-2-5l-1-3c-1 3 0 8 1 11h0c-2-2-1-9-2-12h0c0-5 0-11-1-16-2-8-10-11-16-15 3 1 6 2 9 2 2 1 5-1 6-2l1-1h1z"></path><path d="M238 244c3-2 7-4 10-6 1-1 3-3 4-3-2 9-3 18-6 26v-3h1l-1-1 1-1v-2-1c1-1 1-1 1-2v-1-2c1-1 1-1 1-3h0c0-1 0-1 1-2v-1-2c1-1 0-1 1-2h-1c0 2-1 3-2 4-2 6-2 13-6 18l-1 1-1 2c0 1-1 1-1 2s-1 2-1 3-1 2-1 3c-1 2-2 3-2 4s-1 2-1 3c-1 1-1 3-2 4v1c-1 0-2 0-3 1l3-10c1-3 0-6-2-8-2-3-5-6-8-6-2 0-3-1-4-1h-1l1-2v1l4-3c5-3 10-8 16-11z" class="B"></path><path d="M240 250l-1-2 1-1c1 1 2 1 3 2l-2 2h-1v-1z" class="E"></path><path d="M243 249h1v-1c1-1 1-1 1-2l1 1v1c-1 1-1 2-1 3l-4 10-1 2c0 1-1 1-1 2h-1 0l1-3v-4h1v-1-2l-1 2h-1c2-2 3-3 3-6l2-2z" class="K"></path><path d="M238 244l2 1 4-2h0v1c-2 1-3 2-5 3-1 0-2 0-2 1v2h3v1c-1 1-3 4-5 4-3 2-9 0-13 0 5-3 10-8 16-11z" class="F"></path><path d="M237 250h3v1c-1 1-3 4-5 4l-2-1c-2 0-2 0-3-2 2-1 2-2 4-2h2 1z" class="K"></path><defs><linearGradient id="Y" x1="235.795" y1="271.365" x2="228.414" y2="253.192" xlink:href="#B"><stop offset="0" stop-color="#90908f"></stop><stop offset="1" stop-color="#adaaa7"></stop></linearGradient></defs><path fill="url(#Y)" d="M240 251h1c0 3-1 4-3 6h1l1-2v2 1h-1v4l-1 3h0 1c0 1-1 2-1 3s-1 2-1 3c-1 2-2 3-2 4s-1 2-1 3c-1 1-1 3-2 4v1c-1 0-2 0-3 1l3-10c1-3 0-6-2-8-2-3-5-6-8-6-2 0-3-1-4-1h-1l1-2v1l4-3c4 0 10 2 13 0 2 0 4-3 5-4z"></path><path d="M460 170c4-3 9-7 13-11 6-7 11-15 16-22 6-11 12-22 16-33l7-19c4 16 11 32 18 46l1-1c5 9 10 17 16 24l5 5c2 0 6-1 7-2 1 0 2-2 3-3 1 0 1 0 2 1l1-1c1-2 2-7 1-10l-1-2h0 1c2 2 3 3 3 6l2-1 1 1h0c7-7 14-12 24-12 5 0 9 2 13 5 3 3 4 7 5 11 1 5 1 10-2 14-2 3-5 5-8 6-3 0-5 0-8-2l-2-2h0 0c3 0 6 0 8-2 2-1 3-5 3-7 0-3-1-6-3-8-3-3-6-4-9-4-6 0-11 4-14 8-4 4-6 8-6 14v3 1c1 6 7 9 12 13 15 10 32 18 49 21 16 3 31 3 47 3h50 181c-2 2-5 4-7 6l-14 14-4 4v5c-1 6-2 13-5 19h-1c2-3 3-6 3-9 1-4 1-9 2-13 0-2 0-5 2-7h0c4-7 10-12 14-18l-183 1c-4-1-10 0-14 0h-30l-86-1-44 1c-5 0-12-1-18 0v2l-3-3c-3-6-6-11-7-17 0-2-1-4-1-5s2-3 2-4c1-2 2-4 2-6-1 2-3 5-5 6h-1v-13c-1 4-2 8-4 12-2-2-3-4-4-6l-1-1v1c-2 3 1 6 2 10 2 6-8 23-11 29l-1-5H282h-1l-93 1c1 1 2 5 3 6 2 1 2 2 3 4 2 1 4 5 6 6v2c1 1 3 1 5 2h8c3 1 8 1 12 0 2 0 6 0 9 1 6 1 13-1 19 0-3 1-7 0-10 0h-17c-5 0-12-1-16 1h-1c-1 0-2 1-3 1v1c1 1 2 1 3 2v1c-1-1-2-1-3-1v4l-1 1c0-1-1-1-2-2v1 3c-1 0-2-1-2-1l-1-1h-1v3c-1 0-1 0-2-1v1l-1 1c0 1 0 2-1 3-1-2-2-2-1-4h-3c2 4 3 10 2 14v1l-1 1c0 2-2 4-3 6h0c0-1 0-2 1-2l1-4 1-1v-1l1-1-1-3c-1 1-1 2-1 4-1 2-2 5-4 7-1 0-1 1-2 2-4 3-9 4-13 4s-13 0-17-2l1-1c-12-6-16-15-18-28 0-3-1-6-2-9v-1l-1 1c-3-4-6-7-9-10l-15-14 266-1c20-2 40-10 57-21 4-2 9-5 12-9 4-4 6-10 5-14-1-6-6-11-10-14-3-3-8-5-13-4-3 1-6 3-8 6s-2 7-1 10c0 2 2 4 4 5 2 0 3 1 5 0h1 0c-1 1-2 2-4 2-2 1-6 1-8-1-3-1-5-6-6-8-1-4-1-8 0-12h0v-2s0-1 1-2c3-4 7-7 12-8 7-2 14 0 20 4h0c3 2 5 5 7 7 1-2 1-4 3-6l1 1c-1 4-3 9-1 12l1 2v1l4 4c0 3 0 6 1 8z"></path><path d="M531 203c3 0 4-1 6 0-1 2-3 3-4 5-1-1-1-1-1-2l-1-3z" class="E"></path><path d="M564 195c3 2 6 5 10 7s11 3 15 6c-4-1-8-1-12-3l-14-9 1-1z" class="C"></path><path d="M451 196l16-10v2h1v-1 1c-5 4-12 7-18 10l1-2z" class="P"></path><path d="M546 202h0c2-1 3 0 5 0s3 0 4 1c1 0 2 0 4 1h0-12c-4 1-8 3-12 3 3-2 8-3 10-5h1z" class="O"></path><path d="M522 178l6 19c1 1 1 4 3 6h0l1 3c0 1 0 1 1 2h-2-1l-9-28 1-2z" class="D"></path><path d="M500 183l1 2-7 20v4l1 1h-1c-3-3-7-4-10-6 3 0 5 0 8-1 2-2 3-4 4-7l4-13z" class="B"></path><path d="M564 155l1-1c1-2 2-7 1-10l-1-2h0 1c2 2 3 3 3 6 2 5 0 8-2 13 0 1 0 3-1 4-1-1-2-2-3-2-3-1-7-1-10-2 4-1 8-1 11-5v-1z" class="Q"></path><path d="M459 202l7-1 11-1c3 0 9 1 11-3 3-4 4-10 6-15 3-7 5-14 8-20h1l-1 1-7 20c-2 4-3 10-6 15-1 1-1 2-3 3-4 1-11 1-15 1 3 1 7 1 11 2 2 1 5 3 8 5-4-2-7-3-10-4-8-1-15-1-23-1l2-2z" class="C"></path><path d="M513 126c1 1 1 1 1 2 2 4 3 8 4 13 1 2 2 4 2 6v4l-1 1-1-1h-1c2 9 2 19 5 27l-1 2c-4-13-5-27-7-40 0-5-1-9-1-14z" class="Q"></path><path d="M514 128c2 4 3 8 4 13 1 2 2 4 2 6v4l-1 1-1-1h-1 0c-1-3 0-7-1-11 0-4-2-8-2-12z" class="J"></path><path d="M525 139c6 11 11 23 18 33 4 5 9 9 14 13l25 17c4 2 9 4 14 7-2 0-4-1-6-1-4-2-9-5-12-8l-13-8-16-11c-2-2-5-4-7-6-6-6-9-16-12-24l-6-11 1-1z" class="K"></path><path d="M450 198c-4 2-8 4-11 6 18-5 37-14 47-32 1-2 2-3 3-5 1-3 3-6 4-8 4-7 8-13 11-20 0-1 0 0 1-1 0 1 0 2-1 3-2 5-6 10-8 15-5 8-8 16-13 24-2 3-6 7-9 9s-6 3-8 5-5 3-7 4l-12 5 1 1c3-1 7-3 11-2l-2 2-32 5 26-13-1 2z" class="F"></path><path d="M563 196l-12-7c-1-3-6-5-9-7-2-1-4-3-5-5-2-3-3-6-4-9l-7-18c-2-4-4-9-6-13l-3-11 8 13-1 1 6 11c3 8 6 18 12 24 2 2 5 4 7 6l-1 2 16 12-1 1z" class="B"></path><path d="M548 183c-3-2-7-4-9-7-6-8-8-19-12-27-1-3-5-8-4-11l1 2 6 11c3 8 6 18 12 24 2 2 5 4 7 6l-1 2z" class="L"></path><path d="M467 186c10-8 16-17 22-27 4-7 9-13 12-20 4-8 8-16 11-24l11 37c-1 0-1 0-1 1l-2-6c0-2-1-4-2-6-1-5-2-9-4-13 0-1 0-1-1-2l-1-1c-1 1-5 32-5 36l-4 15c-1 3-1 6-2 9l-1-2c4-12 6-25 7-38 1-4 1-9 2-13h0c-1 2-2 4-2 7 0 4-1 7-3 11l-3 6c-1 4-3 7-5 11-3 8-6 25-15 28-4 1-10 2-14 3l11-4c1 0 2 0 4-1 3-1 6-7 8-10l6-18c2-7 6-12 9-19 1-3 1-5 2-8l-1-1c0-3 2-7 3-10h0c-3 3-4 8-6 12l-14 24c-4 6-7 13-12 18-2 3-6 5-9 7v-1 1h-1v-2z" class="I"></path><path d="M411 147s0-1 1-2c3-4 7-7 12-8 7-2 14 0 20 4h0c3 2 5 5 7 7 1-2 1-4 3-6l1 1c-1 4-3 9-1 12l1 2v1c2 4 2 9 1 12-2 4-5 7-8 9h1c2-5 4-11 3-16-2-6-8-13-14-16-3-2-8-3-11-2-4 2-8 5-9 9-2 3-1 8 0 11 1 2 3 3 5 4l2 1h0c-2 1-6 1-8-1-3-1-5-6-6-8-1-4-1-8 0-12h0v-2z" class="E"></path><defs><linearGradient id="Z" x1="451.658" y1="151.895" x2="445.38" y2="142.417" xlink:href="#B"><stop offset="0" stop-color="#0c0d0d"></stop><stop offset="1" stop-color="#2f2c2c"></stop></linearGradient></defs><path fill="url(#Z)" d="M434 140c2 0 3 0 5 1 1 0 2 1 3 1l2-1h0c3 2 5 5 7 7 1-2 1-4 3-6l1 1c-1 4-3 9-1 12l1 2c-2-1-3-3-4-5-5-6-10-9-17-12z"></path><path d="M411 147s0-1 1-2c3-4 7-7 12-8 7-2 14 0 20 4l-2 1c-1 0-2-1-3-1-2-1-3-1-5-1-4-1-8-1-13 2-4 3-7 7-8 12 0 4 0 7 1 11-1-1-2-2-2-3v-5h-1v4c-1-4-1-8 0-12h0v-2z" class="H"></path><path d="M520 147l2 6c0-1 0-1 1-1 2 4 3 9 5 13 2 2 3 4 4 6 2 4 4 12 8 13 1 1 1 1 2 1s3 1 5 2c1 0 1 0 2 1-1 1-2 0-3 0-2-1-4-3-6-3 3 3 7 5 11 8 3 2 7 4 10 6 3 1 5 2 8 3s6 3 9 4c-2 0-5 0-6-1-3 1-9-1-13-1h0c-2-1-3-1-4-1-1-1-2-1-4-1s-3-1-5 0h0-2c-2 0-4 0-6-2h0c-9-6-11-21-15-30v-2c0-3-2-8-3-10s-1-4-1-6l1-1v-4z"></path><path d="M553 201c4 0 8 1 12 2 2 0 4 2 6 1l1 1c-3 1-9-1-13-1h0c-2-1-3-1-4-1-1-1-2-1-4-1l2-1z" class="C"></path><path d="M519 152l1-1c0 2 0 4 1 6 0 2 4 8 2 11l9 24c2 3 4 6 7 7 2 1 4 1 7 1 2 0 4 0 7 1l-2 1c-2 0-3-1-5 0h0-2c-2 0-4 0-6-2h0c-9-6-11-21-15-30v-2c0-3-2-8-3-10s-1-4-1-6z" class="E"></path><path d="M523 152c2 4 3 9 5 13 2 2 3 4 4 6 2 4 4 12 8 13 1 1 1 1 2 1s3 1 5 2c1 0 1 0 2 1-1 1-2 0-3 0-2-1-4-3-6-3 3 3 7 5 11 8 3 2 7 4 10 6 3 1 5 2 8 3s6 3 9 4c-2 0-5 0-6-1l-1-1-9-3c-4-2-6-4-10-6-5-3-10-6-15-11 0 1 0 2 1 3v3c5 3 11 5 16 8-4-1-10-3-14-5l-1-1v1l2 2h-1c-7-5-12-17-14-25-1-4-1-7-3-10-1-3-1-5-1-7 0-1 0-1 1-1z" class="B"></path><path d="M531 180l-3-6c0-2-2-4-2-6 1-2 0-4-1-6l4 7v4 1l2 2c0 2 1 3 0 4z"></path><path d="M529 169c4 4 5 11 8 15 0 1 0 2 1 3v3c-3-2-5-6-7-10 1-1 0-2 0-4l-2-2v-1-4z" class="H"></path><path d="M571 147l1 1h0c7-7 14-12 24-12 5 0 9 2 13 5 3 3 4 7 5 11 1 5 1 10-2 14-2 3-5 5-8 6-3 0-5 0-8-2l2-1h1c2 0 4-1 6-3 2-3 2-7 1-10 0-4-2-7-5-9-4-2-8-2-12-1-6 2-13 8-16 14-3 7-1 12 1 19l-2-4c-2-2-4-5-4-7-1-1 0-5 0-6l-1-1c2-5 4-8 2-13l2-1z" class="Q"></path><defs><linearGradient id="a" x1="603.974" y1="162.755" x2="606.163" y2="139.165" xlink:href="#B"><stop offset="0" stop-color="#373939"></stop><stop offset="1" stop-color="#636160"></stop></linearGradient></defs><path fill="url(#a)" d="M592 140h0c2 0 3-1 4-1 1-1 3-1 5-1l1 1h0c4 2 7 5 9 9 1 1 1 3 2 3l1 1c1 5 1 10-2 14-2 3-5 5-8 6-3 0-5 0-8-2l2-1v1h2c3 0 5 0 7-1 3-3 3-6 3-10 0-5-1-11-5-15s-8-4-13-4z"></path><defs><linearGradient id="b" x1="580.488" y1="154.359" x2="585.427" y2="134.495" xlink:href="#B"><stop offset="0" stop-color="#121414"></stop><stop offset="1" stop-color="#413f3f"></stop></linearGradient></defs><path fill="url(#b)" d="M571 147l1 1h0c7-7 14-12 24-12 5 0 9 2 13 5 3 3 4 7 5 11l-1-1c-1 0-1-2-2-3-2-4-5-7-9-9h0l-1-1c-2 0-4 0-5 1-1 0-2 1-4 1h0c-9 2-17 8-22 16-1 2-1 4-2 6l-1-1c2-5 4-8 2-13l2-1z"></path><path d="M460 170c4-3 9-7 13-11 6-7 11-15 16-22 6-11 12-22 16-33l7-19c4 16 11 32 18 46 5 9 11 18 18 26 4 4 9 7 13 11s8 8 12 11c16 12 35 23 54 28 4 1 9 1 13 3h0c-36-1-70-19-94-45-16-17-25-40-35-61v1c-2 8-7 15-11 22-10 20-21 40-38 54-7 5-14 10-21 13-17 9-38 16-57 15 6-1 13-2 19-4 16-6 34-15 47-26 4-2 7-6 10-9z" class="G"></path><path d="M136 233c-1-9-9-14-14-21h0 2c0 1 2 3 3 4 2 2 4 4 6 7 1 0 2 1 3 2-4-4-8-8-12-13h158-1l-93 1c1 1 2 5 3 6 2 1 2 2 3 4 2 1 4 5 6 6v2c1 1 3 1 5 2h8c3 1 8 1 12 0 2 0 6 0 9 1 6 1 13-1 19 0-3 1-7 0-10 0h-17c-5 0-12-1-16 1h-1c-1 0-2 1-3 1v1c1 1 2 1 3 2v1c-1-1-2-1-3-1v4l-1 1c0-1-1-1-2-2v1 3c-1 0-2-1-2-1l-1-1h-1v3c-1 0-1 0-2-1v1l-1 1c0 1 0 2-1 3-1-2-2-2-1-4h-3c2 4 3 10 2 14v1l-1 1c0 2-2 4-3 6h0c0-1 0-2 1-2l1-4 1-1v-1l1-1-1-3c-1 1-1 2-1 4-1 2-2 5-4 7-1 0-1 1-2 2-4 3-9 4-13 4s-13 0-17-2l1-1c-12-6-16-15-18-28 0-3-1-6-2-9v-1z" class="B"></path><path d="M176 238l4 1c-1 0-3 0-4 1l-1 1c1 0 2 0 2 1-1 1-2 0-3 0-1 1 0 1-2 1s-3 1-4 1h2 1c1 1 2 0 3 1h1c1 0 2 0 3 1h0v-1c2 0 2 1 3 2 1 2 2 5 1 7h0c-1-1-1-2-1-3 0 0-1-1-2-1 0-1 0-2-1-3-3-3-11-2-16-2h1c2-2 5-2 7-3v-2l1-1v-1h5z" class="E"></path><path d="M188 232h1c3 2 5 4 6 7l2 2c1 1 2 1 3 3h-1v3c-1 0-1 0-2-1v1l-1 1c0 1 0 2-1 3-1-2-2-2-1-4h-3l-2-3c-3-3-6-4-9-5l-4-1c1-1 3-2 5-2h1 1c0-1 1-1 2-1 2-1 2-1 3-3z" class="F"></path><path d="M197 241c1 1 2 1 3 3h-1v3c-1 0-1 0-2-1v-5z" class="D"></path><path d="M189 244l3-1h2l-1 1c1 1 2 0 3 2-1 0-1 1-2 1h-3l-2-3z" class="S"></path><path d="M189 232c3 2 5 4 6 7l-1 1-2-1h-3v-1h1l-1-1h-1l1-1v-1h-1l1-3z" class="D"></path><path d="M176 238c1-1 3-2 5-2h1 1c0-1 1-1 2-1l1 2h-2c0 1 0 1 1 1 2 1 4 2 7 3v2l-3 1c-3-3-6-4-9-5l-4-1z" class="H"></path><path d="M179 250c-1 2-2 4-3 5-2 2-5 3-7 3-5 1-9-2-12-5-6-6-7-15-7-23 13 2 25 0 38 2-1 2-1 2-3 3-1 0-2 0-2 1h-1-1c-2 0-4 1-5 2h-5v1l-1 1v2c-2 1-5 1-7 3h-1c5 0 13-1 16 2 1 1 1 2 1 3z"></path><path d="M162 245h-2v-1c3-3 6-5 11-6v1l-1 1v2c-2 1-5 1-7 3h-1z" class="H"></path><path d="M282 212h213l1 5c3-6 13-23 11-29-1-4-4-7-2-10v-1l1 1c1 2 2 4 4 6 2-4 3-8 4-12v13h1c2-1 4-4 5-6 0 2-1 4-2 6 0 1-2 3-2 4s1 3 1 5c1 6 4 11 7 17l3 3v-2c6-1 13 0 18 0l44-1 86 1h30c4 0 10-1 14 0l183-1c-4 6-10 11-14 18h0c-2 2-2 5-2 7-1 4-1 9-2 13 0 3-1 6-3 9h1c-1 5-7 10-12 13-6 3-19 4-25 1-4-1-8-4-9-7h-1c-1-2-1-3-1-4-1-1-1-1-2-1 0 1-1 1-1 2v1c1 0 1 1 1 2 1 0 1 1 1 1 0 1 1 1 0 2 2 2 4 5 7 6l-1 1 6 3c-3 0-6-1-9-2h-2l-1 1-6-3c-10-5-15-15-18-26-1-1-1-3-1-5 0-4 0-5 3-7v-2h-5-55c-3 1-8 0-11 0h-28-100-96-169-94c-6-1-13 1-19 0-3-1-7-1-9-1-4 1-9 1-12 0h-8c-2-1-4-1-5-2v-2c-2-1-4-5-6-6-1-2-1-3-3-4-1-1-2-5-3-6l93-1h1z" class="G"></path><path d="M719 212l183-1c-4 6-10 11-14 18h0c-2 2-2 5-2 7-1 4-1 9-2 13 0 3-1 6-3 9h1c-1 5-7 10-12 13-6 3-19 4-25 1-4-1-8-4-9-7h-1c-1-2-1-3-1-4-1-1-1-1-2-1 0 1-1 1-1 2v1c1 0 1 1 1 2 1 0 1 1 1 1 0 1 1 1 0 2 2 2 4 5 7 6l-1 1 6 3c-3 0-6-1-9-2h-2l-1 1-6-3c-10-5-15-15-18-26-1-1-1-3-1-5 0-4 0-5 3-7v-2h-5-55c5-1 9 0 14 0h25c2 0 6-1 8-1 4 1 7 0 10 0 1 1 1 0 2 0 1-1 3 0 5 0 1-2 3 0 6 0h0c2 1 3 2 6 3l1-1c1-1 3-3 4-3l1-1h2c1-1 3-1 4-1 2-1 11 0 13 0h1l-1-2c0-1 0-3-1-5h0c0-3 0-4 1-6l1-4c-4-1-9 0-13 0h-25-10-4c-1 0-1-1-2-1h-40c-13 0-26 1-39 0h-1z" class="B"></path><path d="M846 231l26-1c0 8 1 16-5 22-4 4-8 6-13 6-3 0-5-1-7-3s-3-4-3-6c1-1 1-2 2-2 3-3 10-3 14-3-3-1-6-3-9-5h1c3 0 4 2 7 3-1-2-4-3-6-4h-1c-5-1-10 0-14 2-5 3-8 8-9 14-1 4 1 9 3 13 1 0 1 0 1 1 2 2 4 5 7 6l-1 1 6 3c-3 0-6-1-9-2h-2l-1 1-6-3c-10-5-15-15-18-26-1-1-1-3-1-5 0-4 0-5 3-7v-2c2 0 6-1 8 0h0c1 1 1 1 1 2 0 3 0 7 1 9l1-2c0-1 0-4 2-4 2-1 2 0 3-2l2-2 6-3c3-1 7-1 11-1z"></path><path d="M835 232c3-1 7-1 11-1-3 2-7 2-11 2v-1z" class="L"></path><path d="M823 245c1 0 2-1 2-2 1-1-1-1 0-3l1 2v1l2 2v1h-1-1 0c1 3 1 7 1 11-2-4-2-8-4-12z" class="N"></path><path d="M827 237c0 2 0 2 1 3 2 2 3 4 4 7-2-2-4-3-6-5l-1-2c-1 2 1 2 0 3 0 1-1 2-2 2v-1l-1-1c0-1 0-4 2-4 2-1 2 0 3-2z" class="C"></path><path d="M829 235l6-3v1l-1 2c2 0 2 0 4 2h0l-1 1c-1-1-1-2-2-2l-1 1 2 1-1 1v-1l-4-1h0v1h1c1 0 1 1 2 2-1 1-1 1-2 1l-3-2-1 1c-1-1-1-1-1-3l2-2z" class="O"></path><path d="M827 237l2-2c0 2 1 2 1 3l-1 1-1 1c-1-1-1-1-1-3z" class="N"></path><path d="M826 263l-1-1c0-2-1-2-1-3l1-1c4 7 8 13 14 17l6 3c-3 0-6-1-9-2h-2c-2-1-4-1-6-3-1 0-2-1-3-2s-1-2-1-4h1v-1l-1-1c1-1 1-1 1-2h1z" class="B"></path><path d="M825 263h1c1 1 2 3 2 4v1h-1c0-1 0-1-1-1l-1-1-1-1c1-1 1-1 1-2zm0 8c-1-1-1-2-1-4h1l2 2h0c0 1 0 2 1 2v2c-1 0-2-1-3-2z" class="E"></path><defs><linearGradient id="c" x1="815.258" y1="256.368" x2="825.169" y2="258.211" xlink:href="#B"><stop offset="0" stop-color="#727172"></stop><stop offset="1" stop-color="#a5a4a3"></stop></linearGradient></defs><path fill="url(#c)" d="M811 234c2 0 6-1 8 0h0c1 1 1 1 1 2 0 3 0 7 1 9l1-2 1 1c-1 1-1 1-1 2 1 2 1 4 1 6 1 2 1 4 2 6l-1 1c0 1 1 1 1 3l1 1h-1c0 1 0 1-1 2l1 1v1h-1c0 2 0 3 1 4s2 2 3 2c2 2 4 2 6 3l-1 1-6-3c-10-5-15-15-18-26-1-1-1-3-1-5 0-4 0-5 3-7v-2z"></path><path d="M819 234c1 1 1 1 1 2 0 3 0 7 1 9-1 2-1 3-1 5v1c0 1 0 1 1 3v2c1 0 1 1 1 2h-1l-1-1c-2-5-2-8-2-13v-5s1 0 1-1c-1-2-1-2 0-4z" class="C"></path><path d="M822 243l1 1c-1 1-1 1-1 2 1 2 1 4 1 6 1 2 1 4 2 6l-1 1c0 1 1 1 1 3l1 1h-1l-1-1c-1-1-1-2-2-4 0-1 0-2-1-2v-2c-1-2-1-2-1-3v-1c0-2 0-3 1-5l1-2z" class="F"></path><path d="M811 236h0l1 2h1c1 0 2 1 3 2 1 0 1 0 1 1v3c-1 3-1 6-1 9 1 6 3 14 9 18 1 1 2 2 3 2 2 2 4 2 6 3l-1 1-6-3c-10-5-15-15-18-26-1-1-1-3-1-5 0-4 0-5 3-7z"></path><path d="M816 240c1 0 1 0 1 1v3c-1 3-1 6-1 9-1-1-2-3-2-4 0-3-1-7 1-9l1 1v-1z" class="L"></path><path d="M806 234h5v2c-3 2-3 3-3 7 0 2 0 4 1 5l-1 2 2 6h-1v-1c-2 0-4-1-6 0v1l1 2c-4 0-7 2-10 4l-7 5h-1l-1-2v-1l-2-2-8-18-2-3h0c0 6 4 12 6 18-3-2-4-8-5-11-1-2-1-4-2-5v-1l-1-2c0 3 0 5 1 7l3 9v5l8 27 12 30c3 7 4 15 6 22l4 13c2 5 2 10 3 14 1 5 4 9 6 14 1 3 1 7 2 11 0 1 0 3-1 5h0c-3 0-8-9-11-12v1c6 8 13 20 13 30 0 7-2 15-3 23-4 19-8 40-20 56-3 4-7 7-11 10 2-2 3-4 5-6l-1-4h-1l1-2c7-7 5-15 5-25h2c1-25-4-50-12-73-1-1-2-3-3-5-1-3 0-4 1-6 0-2 1-4 1-7l-3 8v-1c-2-6-5-12-8-17h0c-2-5-6-9-9-13l-10-14c-2-3-6-6-9-10l1-1c-1-1-1-2-1-2l-1-1h1l-1-1h0l-1 1c-1-1-1-2-3-3l-2 2-8-6-5-2c-1 0-1 1-3 1 0 0-1 0-2-1l-10-6-11-5-2-1h-1l-1-1c-2 0-3-1-4-2h-2l-2-1c-2 0-3-1-4-2 0 0-1 1-2 0l-6-2-7-2c-1 0-3 0-4-1l-26-4v1c-2 1-2 1-3 3h0l-3 1-58-2h-8-97-9-15-3c-17 0-34-1-50 1-26 3-50 13-72 26-15 8-28 17-39 29-23 23-39 57-38 89 0 27 10 54 30 72 3 4 14 12 18 13h1c1 0 2 1 3 1h-1-3c1 1 0 1 1 1 1 1 2 1 3 1l2 2h-4c-4-1-7-4-11-6-3-2-7-2-10-4-6-5-12-10-18-16h0l-2-4c-3-5-5-12-7-17l-4-7c-1-2-1-3-2-5 0-1-1-2-1-4h0v-1-1c0-1-1-2-1-3 0-8-2-16-1-24l3-15c1-7 4-14 5-20v-7l-2 2c1 2 0 5-1 7l-3 10c-4 13-6 28-5 42 1 3 0 7-1 9l5 15c2 4 4 8 4 13h0c-5-2-4-5-7-8 1 3 1 7 2 10s3 7 5 11c-4-4-8-8-11-12-9-15-15-32-19-48-2-7-4-13-4-20-1-7 2-13 4-20 4-8 7-16 13-23 2-3 5-6 7-9 2-2 4-6 3-9h0c1-2 1-3 1-5v-1c1 0 1-1 1-2l3-6v-1h-1c1-1 1-1 1-2h0c-1 1-2 1-3 1l2-2c1-1 2-1 3-2l-1-1-1 1c-3 1-6 5-9 7v1l6-4c-2 3-5 5-7 7 1-1 3-2 5-3-1 2-4 4-6 5v1l6-3c-2 2-5 4-7 6l8-5c-2 4-6 6-9 8 1 0 2-1 4-1 0-1 0-1 1-1l-1 1c-2 1-3 2-5 4l6-3h1 0l-1 1c-3 1-5 3-8 5l1 1c2-2 4-3 7-4v2l-8 8v1c2-1 6-5 7-6 0-1 0-1 1-2v2c-2 3-6 5-8 8-5 7-9 14-13 21-1-5-4-9-4-14 0-9 12-17 11-26-1-4-1-8 0-12 1-5 4-10 5-15l5-16 17-41 5-16c3-8 4-17 6-26-1 0-3 2-4 3-3 2-7 4-10 6-6 3-11 8-16 11l-4 3v-1-1c0-1 0-2-1-4h-1c-1 2-3 3-5 4l-4 5v-1l1-5c0-4 0-7-1-11h-2l1-1v-4c1 0 2 0 3 1v-1c-1-1-2-1-3-2v-1c1 0 2-1 3-1h1c4-2 11-1 16-1h17c3 0 7 1 10 0h94 169 96 100 28c3 0 8 1 11 0h55z"></path><path d="M255 293c0-1 0 0-1-1h0c1-2 3-2 5-3l3 1h0c-2 1-5 2-7 3z" class="L"></path><path d="M304 276c2-1 3-3 6-3l1 2c-2 1-4 2-6 2l-1-1z" class="K"></path><path d="M781 377l2-2h0v1 1h-1v1 2c1 2 1 5 0 7h-1c0-2 0-2-1-3 0-2 1-4 1-7z" class="J"></path><path d="M799 248l-5-9 9 9c-1-1-3-1-4 0z" class="C"></path><path d="M299 278l5-2 1 1-1 1h0c1 1 2 1 3 1 2 1 3 2 5 2v1l-10-3h-2l-1-1z" class="B"></path><path d="M780 384c1 1 1 1 1 3h1v5 3c-1-1-2-3-3-5-1-3 0-4 1-6z" class="L"></path><path d="M782 387v5l-1-2v-3h1z" class="H"></path><path d="M759 311h1c1 1 1 2 2 3-1 0-2 1-2 1-1 1-1 1-2 1 0-1 0-1 1-1v-1l-4 1c-1 0-2 0-3 1l-1-1h0l8-4z" class="F"></path><path d="M325 292c6-1 11-1 18 0-5 0-10 0-14 1h-2c-2 0-2 0-2-1z" class="B"></path><path d="M718 264l16-7v2c-2 2-2 3-5 2h-1c-1 0-4 1-6 2-1 1-2 1-4 1z" class="P"></path><path d="M264 310c1 0 4 2 5 3h2 0l-1 1h-1c-2 1-4 1-6 1l-1-2c0-1 1-2 2-3z" class="B"></path><path d="M794 399l2 3v2l1 2v2l1 4v2 1 2l-1 1-3-19z" class="D"></path><path d="M747 261h1l3-3h0c-1 4-4 6-7 9-2 2-4 3-6 4h-1c1-2 4-3 6-5h0l3-3 1-2z" class="M"></path><path d="M353 247s-2-2-3-2l-9-7c6 1 11 5 16 7-1 1-3 1-4 2z" class="D"></path><path d="M806 234h5v2c-3 2-3 3-3 7 0 2 0 4 1 5l-1 2h0c-2-5-2-10 0-15h0l-2-1z" class="O"></path><path d="M292 281l7-3 1 1h2 1c2 2 4 3 7 4v1c-5 0-8-4-13-3h-1-1-3z" class="Q"></path><path d="M283 265l-3-4c-2-3-4-4-7-6-1-1-2-2-2-3 5 2 9 7 14 9l1 1h1c-2 1-2 1-4 3z" class="B"></path><path d="M229 464l-5-18c2 4 3 8 6 11v-2c1 3 0 7-1 9z" class="C"></path><path d="M223 445l1 25h0c-1-2-1-4-1-6l-2-16 1 1h0l1-4z" class="R"></path><path d="M757 280h1c4 0 7-2 9-5 2-2 4-5 4-8h1l-1 2c0 4-3 9-6 11s-7 2-11 1c1 0 2 0 3-1z" class="D"></path><path d="M728 250l17-5c1-1 1-1 3-2h4 0c-1 1-1 1-2 1s-3 1-4 1c0 1-1 1-1 2-2 0-4 1-5 2-3 1-5 3-8 3l1-2h-5z" class="H"></path><path d="M239 396c-1-4-2-10-3-14l-1-5c0-1 0-2 1-3 1 6 3 12 5 18v2l-2 2z" class="D"></path><path d="M747 258l13-13 1 1a30.44 30.44 0 0 0-8 8c-1 2-4 4-6 5v2l-1 2-3 3v-2c0-1 1-2 2-4 0-1 1-1 2-2z" class="H"></path><path d="M268 282c-4 0-8 1-11-1-4-2-7-9-8-13v-1-1h0c1 2 2 4 3 7 4 7 7 7 14 8l2 1zm524 186h2c-2 11 1 21-6 31l-1-4h-1l1-2c7-7 5-15 5-25z" class="K"></path><path d="M205 244l1-1v-4c1 0 2 0 3 1 0 1 2 2 3 3 0 1 0 3 1 5 0 1-2 4-3 6v-1-1c0-2 0-7-2-9-1-1 0-1-1-1v2h-2z" class="M"></path><path d="M746 309l4-2h1l1-1h0c1 0 1 1 2 1l2-1 1 1-1 2h2l1 1c-1 1-2 1-3 2l-7 1h-1v-1l3-1 1-2h-3c-1 1-1 1-2 1l-1-1z" class="U"></path><path d="M216 409c0 1 1 2 1 2-3 11-2 23-3 33h0c-1-12 0-24 2-35zm7 9l1 1-1 26-1 4h0l-1-1 1-20c0-3 0-7 1-10z" class="C"></path><path d="M745 255l1-1 1 1c0 1-2 1 0 3-1 1-2 1-2 2-1 2-2 3-2 4v2h0c-2 2-5 3-6 5-1 1-1 1-2 1l-1-1 1-1h1c1-2 1-2 1-4 2-1 5-3 5-5l-1-1 1-1 3-3v-1z" class="J"></path><path d="M209 240v-1c-1-1-2-1-3-2v-1c1 0 2-1 3-1h1c4-2 11-1 16-1l-14 1h0c2 4 2 8 1 13-1-2-1-4-1-5-1-1-3-2-3-3z" class="U"></path><path d="M339 257l-46-6c2-1 7 1 10 1 12 1 24 3 36 3v1 1z" class="K"></path><path d="M244 273c1 4 1 6 4 9 3 4 6 4 10 4v1l-3 1c-1 1-3 2-5 2-2-1-4-4-5-7s-2-7-1-10z" class="C"></path><path d="M646 276c3-1 6 0 8 0h13-4c3 2 10 2 14 1l3 1h0l-4 1c-2 0-6-1-9-1-7 0-14 1-21-2zm83-2c4 2 8 2 11 3 5 1 11 3 16 1s8-7 11-12c0 1 0 2-1 3-2 4-6 9-10 11h1c-1 1-2 1-3 1-9-1-17-4-26-6l1-1z" class="K"></path><path d="M776 269c1 2 1 4 1 6-1 7-2 11-7 15l-8-2-2-1c4-1 9-1 13-5 3-4 3-8 3-13z" class="F"></path><path d="M250 335c1 2 0 3 0 4-1 1-6 5-6 7-1-1-1-1-2-1-1 1-1 0-2 1 0 1-1 3-1 3l-2 3v-1-2c1-1 1-1 1-2v-1l-2 1h0c-1 1-2 1-3 1l2-2c1-1 2-1 3-2l-1-1c-2 0-2 2-4 1 1-1 2-1 3-2l1-1c1-1 1-1 2-1l1-1v1 2h0c1 1 1 1 1 2 1 0 1 0 1-1h1c2-3 4-5 7-8z" class="L"></path><path d="M211 256c4-7 10-13 15-18l-4 9-1 3h0c-1 2-2 5-3 6 0-1 0-2-1-4h-1c-1 2-3 3-5 4z" class="E"></path><path d="M218 249h0c1-1 2-2 4-3v1l-1 3h0l-1-1h-2z" class="B"></path><path d="M218 249h2l1 1c-1 2-2 5-3 6 0-1 0-2-1-4h-1 0l2-3z" class="I"></path><path d="M287 262c2 1 5 2 7 4v1h1l7 3h4l1 1h1s1 0 1 1h-2c-4-1-7-1-10-1-5-1-10-4-14-6 2-2 2-2 4-3zm130-1v-1s-5-2-6-3c-3-2-5-5-7-7l-5-5c5 2 9 7 13 11h1l-15-17c2 0 4 3 6 5l6 7c3 2 7 5 9 8-1 1-1 1 0 2-2 1-1 1-2 0z" class="P"></path><path d="M648 252c7-2 14-3 20-6 2 0 6-3 8-3-2 2-5 3-7 4 3 0 7-2 10-2v1h-2c-3 2-6 2-9 3s-7 3-10 3l1 1h-1l-3 1h0-5c1-1 2-1 3-2-2 1-3 1-5 0z" class="I"></path><path d="M786 369c-1-3-3-7-5-10-4-3-6-9-8-14-2-3-5-6-7-9 0 0 1 1 2 1 2 2 6 6 7 9 1 2 5 8 6 11 0 1-1 0 0 1l1-1h0l7 10c-1 1-1 1-3 2z" class="B"></path><path d="M686 256c1-1 2-1 2-1h1c1 0 1-1 2-1h1 3v-1h3 4c3-1 6 0 8-1 2 0 5 0 7-1l11-1h5l-1 2c-5 0-12 0-17 1-2 1-5 0-7 1-5 0-9 0-14 1l-6 1h-2 0z" class="M"></path><path d="M789 367h1c2 2 4 3 6 4s3 3 5 4l-8-4 9 8-8-5c3 3 7 7 10 11v1c-3-4-6-7-10-9v-1c-1-1-3-3-4-3v2 2l-4-8c2-1 2-1 3-2z" class="E"></path><path d="M339 255c-2-2-6-1-9-2l-42-7h-3-3v-1c1 0 1 0 3 1h1 4l33 4c4 1 8 1 12 2 3 1 6 2 8 3-1 0-2 0-4 1v-1z" class="B"></path><path d="M266 281l2-1h-1c-4-1-6-3-8-6-1-2-4-7-4-9 1 1 2 3 3 5 2 3 4 7 8 8 1 1 4 1 6 1 7-1 13-3 19-4 3-1 6-2 8-2-1 2-26 8-31 9h0l-2-1z" class="F"></path><path d="M271 321l-1-1c-1 1 0 1-1 1l-2 1h0l-1-1 3-1 1-1c0-1 1-2 2-2 2-2 4-4 7-4h1c1-1 0-1 1-2h0l2-1h2c1-1 3-2 4-3s2-1 3-1c2-1 4-2 5-3l2-2c1 1 2 1 3 2-7 5-16 7-23 11-3 2-5 3-7 5 0 1-1 1-1 2z" class="J"></path><path d="M775 261l-4-12c-1-4-1-9-2-14 2 1 3 3 5 4l15 10c-2 0-2 0-4-1v-1l-4-2-5-4-1 1 1 1-1 1-2-3h0c0 6 4 12 6 18-3-2-4-8-5-11-1-2-1-4-2-5v-1l-1-2c0 3 0 5 1 7l3 9v5z" class="K"></path><defs><linearGradient id="d" x1="358.029" y1="243.635" x2="375.771" y2="256.966" xlink:href="#B"><stop offset="0" stop-color="#b8b6b6"></stop><stop offset="1" stop-color="#e2e1e0"></stop></linearGradient></defs><path fill="url(#d)" d="M357 245c7 4 14 6 22 7l-1 2c-1 1-2 2-4 2l-1-1h-3c-1-1-1-1-2-1h0c-5-3-10-4-15-7 1-1 3-1 4-2z"></path><defs><linearGradient id="e" x1="332.308" y1="252.904" x2="346.876" y2="246.516" xlink:href="#B"><stop offset="0" stop-color="#9d9e98"></stop><stop offset="1" stop-color="#cecbd0"></stop></linearGradient></defs><path fill="url(#e)" d="M347 255l-8-3c-8-3-13-6-21-7-1 0-1 0-1-1h3c3 1 7 1 10 2 7 1 13 4 21 6l-1 1 1 1v1h-4z"></path><path d="M797 418l1-1c1 1 0 3 1 5v2c0 2 0 5 1 7v1c1 3-1 33-2 35-1-1 0-6 0-7v-31c-1-4-1-7-1-11zm-50-89l1-1h0c8 9 18 15 26 24 2 2 3 5 5 7 2 3 4 5 5 9h1v8 11c-1-4-1-8-1-12v-7c-1-1-1-3-2-4-1-2-4-3-5-5-2-2-4-5-6-8-5-6-12-11-18-17-2-1-4-4-6-5z" class="B"></path><path d="M662 252c13-3 25-4 39-6 7 0 15-1 22-1h0c-2 0-4 0-5 1-3 0-6 1-9 1-13 2-26 2-38 6h0c-2 1-4 1-6 0-1 0-2-1-3-1z" class="I"></path><path d="M672 253h7l11-1c6 0 12-2 18-2h2c1-1 4-1 5-1-2 2-10 2-13 2-7 1-14 3-21 4h0c1 0 3 1 5 1h0l-28 2c2-1 3-1 6-1 1-1 3 0 5 0v-1h-9c2-1 4-1 7-1 2 0 5 1 7 0v-1l-2-1z" class="P"></path><path d="M727 319c2 0 2 0 3-1l5 4c0-1-1-3-2-4l-2-2c-2 0 0 1-2 0-2-2-4-4-6-5-1-1-2-1-3-2h1l2 1h0c2 1 2 1 3 2 2 1 3 2 4 2 1 1 2 1 3 1l1 1s0 1 1 1c2 0 4 2 5 4l1-1h1v1c1 1 4 5 6 6v1l-1 1c-1-1-2-1-3-2l-2-1-1-1h0l-1 1c-1-1-1-2-3-3l-2 2-8-6z" class="H"></path><path d="M737 323l-2-1 1-2-1-1 1-1 3 3c2 1 2 3 4 4 1 1 1 1 1 2l-2-1-1-1h0l-1 1c-1-1-1-2-3-3z" class="J"></path><path d="M796 402c1-2 1-9 1-11-1-1-1-2-2-4 0-1-3-5-2-6h1c2 5 5 11 6 17 1 4 0 8 1 13 0 5 1 11 0 16 0 2 0 3-1 5h0v-1c-1-2-1-5-1-7v-2c-1-2 0-4-1-5v-2-1-2l-1-4v-2l-1-2v-2z" class="C"></path><path d="M734 257c4-3 10-6 14-10 4-3 8-5 12-8l-21 20 6-4v1l-3 3-1 1 1 1-2 1-1 1s-1 1-2 1h-1-1c-1 0-1 1-2 1s-2 1-2 1v-1h1l-2-1c-1 0-2 0-2-1v-2h1c3 1 3 0 5-2v-2z" class="K"></path><path d="M734 259c1-1 2-1 4-1h1c0 2-2 3-2 5h-1l-1 1h0c-1 0-1 1-2 1s-2 1-2 1v-1h1l-2-1c-1 0-2 0-2-1v-2h1c3 1 3 0 5-2z" class="G"></path><defs><linearGradient id="f" x1="651.184" y1="269.221" x2="645.816" y2="256.279" xlink:href="#B"><stop offset="0" stop-color="#c0c0c5"></stop><stop offset="1" stop-color="#e5e3e2"></stop></linearGradient></defs><path fill="url(#f)" d="M658 260l32 1-42 3-33 2h1c3-1 8-2 12-2l30-4z"></path><defs><linearGradient id="g" x1="347.178" y1="273.04" x2="365.046" y2="280.152" xlink:href="#B"><stop offset="0" stop-color="#838180"></stop><stop offset="1" stop-color="#b4b3b1"></stop></linearGradient></defs><path fill="url(#g)" d="M352 275h21 0l2 2c1 0 1 1 1 2h1c2-1 6-1 9-1 8 0 16 0 24-1h10v1l-45 1 1 2-2-4-26 1c-9 1-18 2-27 0h-9c-1 0-3 0-4-1 2-1 4-1 6-1 2 1 4 1 7 1 6 1 14 2 20 0h0l-2-1h3c3-1 6-1 10-1z"></path><path d="M742 261c0 2-3 4-5 5 0 2 0 2-1 4h-1l-1 1 1 1c1 0 1 0 2-1h1c2-1 4-2 6-4l3 1h1l4-4 1 1c-2 2-5 5-8 6v1c-1 1-2 1-4 1l-2 2h-3l-1-1h-1-1l-1-1h-1-4l2 1-1 1c-1 0-2 0-3-1h0l-6-3h-6c1-1 1-1 2-1h3c1-1 1-1 3-1s4 0 6-1c0-1 1-1 1-1 1 0 2 0 3-1 0 0 1-1 2-1s1-1 2-1h1 1c1 0 2-1 2-1l1-1 2-1z" class="S"></path><path d="M742 261c0 2-3 4-5 5-3 3-9 5-14 5h-2c-1-1-1 0-2 0h-6c1-1 1-1 2-1h3c1-1 1-1 3-1s4 0 6-1c0-1 1-1 1-1 1 0 2 0 3-1 0 0 1-1 2-1s1-1 2-1h1 1c1 0 2-1 2-1l1-1 2-1z" class="I"></path><path d="M656 290c23 4 47 14 66 27-1 0-1 1-3 1 0 0-1 0-2-1l-10-6-11-5-2-1h-1l-1-1c-2 0-3-1-4-2h-2l-2-1c-2 0-3-1-4-2h0l-1-1-9-2-10-3c-2-1-3-1-5-1 1 0 1 0 2-1h-1v-1z" class="G"></path><path d="M312 281c5 0 9 1 13 1 3 1 6 0 8 1h8c7 0 13 0 20-1 3 0 6-1 9-1h4s0 1 1 1c2 1 4 0 6 0 3-1 6 0 10-1h1l1 1c-3 1-4 0-7 1-6 1-13 1-19 2-1 0-2 0-3 1h-5-2c-1 1-3 1-5 1-4 0-10 0-14 1-2 0-3 0-4 1l-1 1c-1 0-1 0-1-1v-1l-1-1c2-1 5 0 7 0 5-1 10-1 15-2h6 1v-1c-14 0-29 1-43-1-2-1-3 0-5-1v-1z" class="H"></path><path d="M790 377v-2-2c1 0 3 2 4 3v1h0c1 1 1 1 1 2 2 2 3 5 4 7 5 13 8 28 10 42 0 7 1 14 0 21h0c-1-2-1-6-1-8l-1-13c-1-14-4-28-9-41-1-2-2-5-4-6h-1c-1 1 2 5 2 6 1 2 1 3 2 4 0 2 0 9-1 11l-2-3s-1-11-2-13h0v9c-1-3-1-7-1-10s-1-5-1-8zm-563 1c1-2 1-2 2-3 1 2 0 4 1 6l-2 16-3 20-1 2-1-1c0-4 1-8 1-13l-1 5c-1 2-1 5-1 7 0-5 1-9 2-14l4-19c0-1 1-4 1-5-1 1-1 2-1 3 0 2-1 2-1 4-1 1-1 3-1 5-1 3-2 7-3 10l-4 14c1-10 4-18 6-27-2 2-3 6-3 9l-5 14s-1-1-1-2c0-3 1-5 1-7 1-7 3-14 7-19 1-2 2-3 3-5z" class="F"></path><path d="M217 402c1-7 3-14 7-19l1 1c0 1-1 3-2 5l-3 8c0 2-1 4-3 5z" class="N"></path><path d="M279 295h0l4-4h1l2-1 2-2h1 0v-1c1-1 3-3 5-3h1v-1l1 1v2c2 1 4 1 6 2-3 0-6-1-8-1h0 1c3 1 6 3 9 4 6 3 14 1 21 1 0 1 0 1 2 1h2l-17 3c-4 0-9-1-13-2-2-1-5-1-7-2-1-1-2-1-2-2-1 1-2 1-2 2 1 0 2-1 4 0v1c4 3 10 3 15 3v1h-7c-4-1-5-3-9-2-2 0-4-1-5-2-1 0-2 1-2 1h2 0c1 1 2 1 3 2h-2c-1 0-1-1-2-1 0 1-1 1-1 1l-1-1h-2v1h1c2 1 3 2 4 3h0l-6-2c-1 0-1 0-2 1 0-1 0-2 1-3z" class="C"></path><path d="M725 282c1 3 5 3 6 5l-1 1 1 1 1-1 3 3h1l-1-2c2 1 3 2 5 4h0l-2 1h0l1 1c1-1 1-1 2-1l1 1c1 1 1 1 2 1v1c-3 1-6 3-8 4-3 1-3 3-6 3v-1c2 0 4-2 6-3s3-2 4-3v-1h-1c-2-1-4 0-6 0 1-1 3-2 4-2l-1-1c-1 1-3 1-4 2v1c-2 2-9 4-11 4s-3 0-4-1h2v1c5-1 10-3 13-6l-14 4c3-2 5-2 7-3s4-3 6-2h1s1 0 1-1h1l-2-2h-2c0 1 1 1 2 1v1h-2c-4 0-7 2-10 3-2 1-5 2-8 2l16-7h1-1c-3-1-7 2-10 4 1-1 2-2 4-3l3-3c-2 0-3 1-4 2-3 2-5 4-8 5-4 0-8-1-12-1l-24-3h1l26 1c5 1 11 1 16-3 2-2 5-3 8-5-2 0-5 1-6 2-2 0-4 2-6 2 2-2 5-4 7-6h1 1z" class="E"></path><path d="M689 275c9 0 18 0 27 1 2 1 4 2 6 2 1 1 1 1 2 1v1h0v1l1 1h-1c-1 0-2-1-3 0-3 1-7 2-10 2-2-1-30 0-35 0s-11-1-16-2c-2 0-3 0-4-1h1c17 2 34 1 52 2h0c1-1 1-2 2-2 2-1 3-1 5-2h-3c-1 0-2 1-3 0h-9-25l4-1h0l-3-1c3-2 8 0 12-2z" class="C"></path><path d="M689 275c9 0 18 0 27 1-11 3-25 2-36 2l-3-1c3-2 8 0 12-2z"></path><path d="M730 314c0-1-1-2-2-2-4-2-8-5-10-7-1 0-2-1-3-1v-1c-3-1-6-2-8-4h0l4 1 1 1c2 0 3 1 5 1 1 0 2 1 2 1l6 3c2 0 4 1 6 2 1 0 1 0 1 1 1 0 2 0 3 1h1c2 1 3 2 4 2l6 3c0-1 0-1 1-2l-2-1 1-2h-4l1-1h1c1-1 1-1 2 0l1 1c1 0 1 0 2-1h3l-1 2-3 1v1h1l-1 1 2 2c-1 0-1 0-2 1 1 0 2 1 3 1l2 1 1 1 3 2c1 1 2 1 3 2 2 1 4 2 6 2 1 0 1 0 2 1v1h-1l-2-1c-1 0-2-1-3-1l-1-1h-2c-1-1-2-2-3-2-1-1-2-1-3-2-1 0-1-1-2-1h0c-1-1-2-1-3-2h-1v2h3l1 1h0 1l1 2h1 1l2 1 1 1h1l1 1c4 2 7 4 10 6 2 1 4 3 5 5 0 1 1 2 2 3l1 1 1 2c1 1 1 1 1 2v1h0c-1-1-5-5-5-6s0-1-1-2v-1c-2-2-4-3-6-5-1-1-2-2-3-2l-1-1c-3-2-6-2-8-4h-1l1 2h-1l-3-3c0-1-2-1-3-2 0 0-1 0-1-1l-2-1-2-1-2-1h-1v2l-1 1c-1-2-3-4-5-4-1 0-1-1-1-1l-1-1c-1 0-2 0-3-1z" class="M"></path><path d="M262 290l27-8 1 1h2 0c-2 1-3 2-5 2l-1 1h-2l-2 2c-2 1-5 3-8 4-3 3-9 7-11 11l-2 3c-1 1-2 2-2 3-2 2-4 4-5 6l-2 2c-1 1-1 2-2 3-2 3-5 7-7 11 0 1 0 1-1 2 0 1 0 0-1 1 0 1-1 2-1 2h-1c1-2 1-3 2-4l2-2c0-3 2-6 4-7 2-3 3-6 5-8 2-3 4-4 6-6v-1c1-1 1-2 2-3 0-1 2-3 4-5l9-9 3-1-1-1h0c-2 2-4 3-6 4h0l-13 12c-2 2-5 3-6 6-1 0-1 0-1 1-3 3-9 9-14 9 3-1 7-4 9-6 1-2 3-3 5-5-2 1-5 3-7 4-2 0-3 1-4 2l-1-1c2-2 4-2 6-3 1-1 3-2 4-2 5-2 8-6 11-9 4-4 7-8 11-11h1c-1 0-2 0-3 1l-2 2c-2 3-4 3-7 5-6 3-11 5-17 7 0-1 2-2 3-2h0c3-1 6-2 8-4 4-1 7-3 10-5-6 1-12 6-18 8 4-3 8-5 13-7 1-1 3-2 5-3 1 0 3-1 4-2-4 0-20 9-24 12-1 0-1 1-2 2l-1-1c2-1 3-2 5-4 3-2 7-4 11-6 2-1 5-2 7-3z" class="O"></path><path d="M775 244l1-1-1-1 1-1 5 4 4 2v1c2 1 2 1 4 1 4 2 9 5 13 7l-3-8c1-1 3-1 4 0 2 2 4 4 6 7-2 0-4-1-6 0v1l1 2c-4 0-7 2-10 4l-7 5h-1l-1-2v-1l-2-2-8-18z" class="Q"></path><path d="M710 271h1v1h1 3c-1 1-3 1-4 1h1c8 2 16 7 23 10 3 1 7 1 10 3 6 2 13 5 19 8 5 2 10 3 14 7-4-2-10-5-15-6-3-1-6-4-9-5l-8-3s-1-1-2-1l-1 1c-1 0-4-2-5-2h-1c2 1 3 2 5 2l1 1 1-1c5 3 11 4 15 7 2 2 4 3 6 5 3 2 5 4 8 5l12 6c-4-1-9-2-12-5l-6-3c-2-1-3-3-5-5l-6-3h0c1 1 2 2 3 2 2 2 4 4 6 5 2 2 5 4 8 6s6 3 9 5c-4 0-10-4-13-7l-6-3c-1-1-1-2-2-2-1-3-4-3-6-5-1-2-2-2-3-3h-1l6 6c6 3 9 10 13 15 1 2 3 3 3 4 1 2 3 3 4 4l10 8v1c-5-3-10-7-14-11s-6-9-10-14c-3-3-6-5-8-8-3-3-5-5-9-7 0-1-1-1-2-2v1c2 1 4 2 5 4l3 3 9 9 10 14h0c-1-1-1-1-2-1-3-3-5-7-8-10-3-4-8-8-11-13l-2-2c-4-2-7-5-11-6h0c2 2 4 3 6 5h0l-9-5c-3-2-6-4-9-5l-1-1v-1h0v-1c-1 0-1 0-2-1-2 0-4-1-6-2-9-1-18-1-27-1 4-2 11-1 16-2 2 0 3 0 5-2zm-421 11l3-1h3 1 1c3 1 9 3 12 5-3-1-7-2-10-2h-3l-1-1v1h-1c-2 0-4 2-5 3v1h0-1l-2 2-2 1h-1l-4 4h0l-1 1c-1 0-2 1-3 2-1 2-3 3-4 5 3 3 9 1 12 4h0c-4 1-9-1-12-3h-1v1c1 2 5 3 7 3 1 0 2 0 3 1-3 0-6 0-9-1-1-1-2-1-3-2v1c2 2 5 3 8 4-3 0-7 0-9-2l-2-2c3-4 8-8 11-12-1 0-2 1-3 2h-1c1-1 1-1 1-2v-1c-6 4-10 11-14 17-3 4-6 7-9 11l-6 10c-1 2-2 4-3 5-2 1-3 2-4 3-2 1-3 2-5 3-1 1-2 2-4 2l2-2c3-2 6-3 8-6l-9 5c3-3 7-6 10-8-2 0-5 2-7 4h0-1c2-2 3-3 5-4 2-2 3-1 5-3v-1l-8 4c1-3 8-3 10-7 3-5 6-10 10-15 1-2 4-4 6-6h-1c-4 3-10 8-12 13 0 1 0 1-1 2-2 4-8 6-12 7 4-2 10-5 12-9l-1-1c2-1 5-5 6-7 2-2 4-3 6-5l13-13c2-1 4-2 6-4h0l1 1-3 1-9 9c-2 2-4 4-4 5-1 1-1 2-2 3v1c-2 2-4 3-6 6-2 2-3 5-5 8-2 1-4 4-4 7l-2 2c-1 1-1 2-2 4h1s1-1 1-2c1-1 1 0 1-1 1-1 1-1 1-2 2-4 5-8 7-11 1-1 1-2 2-3l2-2c1-2 3-4 5-6 0-1 1-2 2-3l2-3c2-4 8-8 11-11 3-1 6-3 8-4l2-2h2l1-1c2 0 3-1 5-2h0-2l-1-1z" class="B"></path><path d="M273 294c2-2 4-3 7-4 3-2 6-3 9-4-2 1-4 2-6 4-2 1-4 4-7 5-1 0-2 1-3 2h-1c1-1 1-1 1-2v-1z" class="J"></path><defs><linearGradient id="h" x1="346.64" y1="277.326" x2="396.36" y2="241.674" xlink:href="#B"><stop offset="0" stop-color="#c2c3c6"></stop><stop offset="1" stop-color="#f1efec"></stop></linearGradient></defs><path fill="url(#h)" d="M355 252h0c-2-1-4-1-5-2-3-2-7-4-9-6 5 2 9 5 15 8 3 1 8 1 12 2h0c1 0 1 0 2 1h3l1 1c2 0 3-1 4-2l1-2 33 8-2 1h1c-1 0-3 0-4 1h-9 0c-3-1-8-1-12-2-10-1-20-1-31-1-9 1-19 2-29 1h-8c-1-1-3-1-4-1h-1 13c6 0 12-1 18-2-1-1-3 0-5 0v-1c2-1 3-1 4-1h4 4v-1l-1-1 1-1h4z"></path><path d="M351 252h4l1 2h-5l-1-1 1-1zm237 13c11-6 19-17 28-25h0l-1 1h0c-5 5-7 11-11 16 2-1 6-6 8-7 4-4 8-7 12-10-1 2-4 5-6 6-5 5-9 10-15 14 9-4 18-7 27-10 3-1 6-3 9-4v1c-4 2-8 4-12 5-8 3-15 6-22 10 1 0 4-1 5-1l25-7c4-1 9-1 13-2 2 1 3 1 5 0-1 1-2 1-3 2h5 0l3-1h1l3-1c1 0 2 1 3 1 2 1 4 1 6 0h1l2 1v1c-2 1-5 0-7 0-3 0-5 0-7 1h9v1c-2 0-4-1-5 0-3 0-4 0-6 1-19 2-37 6-56 9l-9 2h-2-3l-1 1-2-2 1-1 3-1-1-1z" class="G"></path><path d="M302 303c4-2 7-3 11-3-5 2-10 5-14 8-2 3-6 4-8 6h-2l-2 2c-1 2-3 3-4 4-3 3-5 6-8 9-2 1-4 2-5 3-7 6-14 11-20 17-3 3-4 6-6 8s-5 5-5 7c-4 6-7 10-9 17-1-2 0-4-1-6-1 1-1 1-2 3l-7 7c2-2 3-5 5-7s4-4 6-7c2-6 6-10 9-15 1-2 5-8 4-10 0-2 5-6 6-7 0-1 1-2 0-4-3 3-5 5-7 8h-1c3-5 8-11 13-13 3-1 5-3 8-4 2-1 3-2 5-3 1-1 2-2 3-2 0-1 1-1 1-2 2-2 4-3 7-5 7-4 16-6 23-11z" class="E"></path><path d="M250 335c3-2 6-4 10-6 1-1 3-1 4-2 3-2 7-6 11-6-1 2-5 3-7 5-2 1-5 4-7 6h-1c-3 1-7 4-10 7 0-1 1-2 0-4z" class="H"></path><path d="M261 332l1-1c3-1 5-3 9-5 1-2 4-3 6-4l1-1c0 1 0 1-1 1-1 2-3 2-5 4-1 1-2 3-4 4 3-2 7-4 10-7 2-2 4-5 6-7h1l-1 1-2 2c-1 2-2 4-4 5-2 2-4 5-7 6-3 2-5 3-7 5-5 4-8 8-12 11s-7 6-10 9h0c3-7 6-12 11-16 2-3 5-5 7-7h1z"></path><path d="M501 252c5-4 8-8 11-14 1 3 3 5 4 8 4 4 11 9 14 14 2 4 2 10 2 14 3-4 7-9 7-15l-2-10h0c0 1 1 2 1 3l3 11c2-7 0-14 1-21 1 1 0 3 0 5 1 4 2 7 1 11 0 1-1 2-1 3-1 2-1 3-1 5 2-1 5-11 6-14l5-13c0 2 0 5-1 7-2 6-4 11-6 16-2 3-4 5-5 8h1c4-5 9-10 13-16 2-4 3-8 5-11 0 3-1 5-2 7-5 12-14 19-21 29 11-4 21-8 28-18 1-2 2-3 3-5l1-3h0v-1c1 3-1 8-2 11l-2 3c5-2 9-6 13-10 4-3 9-6 12-10 2-3 3-6 4-9 0 3 0 7-2 10-2 9-12 16-19 21l-9 6c3-1 6-3 10-3l15-6 1 1-3 1-1 1 2 2 1-1h3l-18 4c-1 0-2 0-4 1 2 0 6-1 8-1l21-3c18-1 36 1 54 0 11 0 23-2 35-3 10-1 21-1 31-3 2 0 3 0 4-1 2-1 5-2 6-2v2c0 1 1 1 2 1l2 1h-1v1c-1 1-2 1-3 1 0 0-1 0-1 1-2 1-4 1-6 1s-2 0-3 1h-3c-1 0-1 0-2 1h-2-1c-2 2-3 2-5 2-5 1-12 0-16 2s-9 0-12 2c-4 1-11 1-14-1h4-13c-2 0-5-1-8 0l-11-1h-20c-3 0-7-1-11 1l44 3h1c-1 1-1 2-2 2 0-1 0-1-1-1-2-1-5-1-7-1h-11c-30-2-62-4-92 7l12-1c9-1 17 0 26 0 23-1 46-2 69 2 4 1 9 2 13 3v1h1c-1 1-1 1-2 1 2 0 3 0 5 1l10 3 9 2 1 1h0s-1 1-2 0l-6-2-7-2c-1 0-3 0-4-1l-26-4v1c-2 1-2 1-3 3h0l-3 1-58-2h-8-97-9-15-3v-1h3 8v1c2-1 4-2 6-2l1 1 2-2-5-1c5 0 10 0 14 1-1 0-1 1-2 0h-2c2 1 4 2 5 3 1 0 3-1 5-1s6 1 9 1c3-1 9 0 13-1v-5-4c1-2 2-2 3-2h1c0-2 0-3-1-4v-3c1-8 3-11 8-16h-1l1-2v-2c-2 0-2 0-3-1s-2-1-3-1z" class="T"></path><path d="M515 255c4 5 7 9 8 15 2 6 0 10-3 15v-2c2-9 0-14-4-22-1 2-1 5-2 6l-1-3c0-3 1-6 2-9zm-8 3l2-2c1 2 3 6 3 9 0 0 0 1-1 2h0l-3-5c-2 3-4 5-5 8v1c-2 5 0 11 1 16-2-5-4-8-5-13 1-8 3-11 8-16z"></path><path d="M635 275c1 0 3-1 4-1 2 1 4 1 6 1h1 5 7 10c1-1 4 0 6-1h3c1 0 0 0 1-1 1 0 2 1 4 0h3l1-1h4v1c2-2 4 0 7-1 1 0 1-1 2-1s3 1 4 0l-1-1c2-2 6 0 9-1 1 0 1-1 2-1 1 1 6 1 8 1-2 0-2 0-3 1h-3c-1 0-1 0-2 1h-2-1c-2 2-3 2-5 2-5 1-12 0-16 2s-9 0-12 2c-4 1-11 1-14-1h4-13c-2 0-5-1-8 0l-11-1z" class="G"></path><path d="M667 276l43-5c-2 2-3 2-5 2-5 1-12 0-16 2s-9 0-12 2c-4 1-11 1-14-1h4z" class="H"></path><path d="M548 285c9-1 17 0 26 0 23-1 46-2 69 2 4 1 9 2 13 3v1h1c-1 1-1 1-2 1 2 0 3 0 5 1l10 3 9 2 1 1h0s-1 1-2 0l-6-2-7-2c-1 0-3 0-4-1l-26-4c-8-1-17-1-25-1l-33-1c-6 0-14 1-20-1h0 15 2l1 1c1-1 3 0 4-1h7 1c4 0 10 0 14 1h4c1-1 1-1 2-1l1-1h-1-35c-5 0-10-1-15 0h-2c-2 0-4 0-7-1z" class="I"></path><defs><linearGradient id="i" x1="348.826" y1="534.428" x2="382.712" y2="237.489" xlink:href="#B"><stop offset="0" stop-color="#b4b4b3"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#i)" d="M419 259s1 1 2 1c-5-6-9-13-12-20 6 6 11 14 18 20 1 1 4 2 6 3 8 3 14 6 22 7-8-5-15-10-21-17-4-4-4-9-3-14 0 0 1 3 1 4 2 5 8 9 13 12 4 4 9 8 14 11 0-2-1-4-2-6 0-2 0-4 1-6 0 1 0 3 1 4 4 10 18 16 27 20l-1-1c-1-2-2-4-4-5-3-4-12-9-13-15 3 3 5 6 8 9-4-7-9-15-9-22 3 6 4 14 9 19 1 2 3 3 5 4l-3-6-3-17c-1-2-1-3-1-4 4 8 4 18 9 26 0-3 0-7-1-10 0-6 0-11 1-16l1 18c1-4 2-8 4-12 0 4-2 8-3 13 0 7 4 12 8 17l-1-6c-1-3-1-7 0-9l1-1c2-3 5-5 8-8 1 0 2 0 3 1s1 1 3 1v2l-1 2h1c-5 5-7 8-8 16v3c1 1 1 2 1 4h-1c-1 0-2 0-3 2v4 5c-4 1-10 0-13 1-3 0-7-1-9-1s-4 1-5 1c-1-1-3-2-5-3h2c1 1 1 0 2 0-4-1-9-1-14-1l5 1-2 2-1-1c-2 0-4 1-6 2v-1h-8-3v1c-17 0-34-1-50 1-26 3-50 13-72 26-15 8-28 17-39 29-23 23-39 57-38 89 0 27 10 54 30 72 3 4 14 12 18 13h1c1 0 2 1 3 1h-1-3c1 1 0 1 1 1 1 1 2 1 3 1l2 2h-4c-4-1-7-4-11-6-3-2-7-2-10-4-6-5-12-10-18-16h0l-2-4c-3-5-5-12-7-17l-4-7c-1-2-1-3-2-5 0-1-1-2-1-4h0v-1-1c0-1-1-2-1-3 0-8-2-16-1-24l3-15c1-7 4-14 5-20v-7-2c10-24 24-47 45-64 3-2 6-5 9-7s7-4 9-6c21-15 45-22 70-26s49-4 75-4c13 0 27 0 40 1-4-2-9-3-13-4-5-2-10-3-16-4-7-1-15-1-23-1-3 0-6-1-9 0h-1 0c-5-1-9-1-14-1h-27c-4 0-8 0-12-1h-1 0-21c-4 0-7 0-10 1h-3l-23-3h-2v1l-3 1-1-2v-1h-1c0-1-1-1-1-1h-1l-1-1h-4l-7-3h-1v-1c-2-2-5-3-7-4h-1l-23-21h1c13 10 27 19 43 24 8 2 16 2 24 3 25 2 50 2 75 2l43 3c-6-3-14-4-20-4l-22-3c-12-1-25-1-36-1-9-1-17 0-26 0 4-1 8-1 11-1 9-1 18-2 27-2h10c1 0 3 1 5 0h0 9c1-1 3-1 4-1h-1l2-1 5 1c1 1 0 1 2 0-1-1-1-1 0-2z"></path><path d="M294 266c1 0 1 0 2 1l4 1c2 1 4 1 6 1 1 0 1 1 2 1h4c3 0 6 1 9 1 10 1 20 4 31 4-4 0-7 0-10 1h-3l-23-3h-2v1l-3 1-1-2v-1h-1c0-1-1-1-1-1h-1l-1-1h-4l-7-3h-1v-1z" class="E"></path><path d="M374 289c-2 2-5 2-7 2 0 1 0 1 1 1h2c-1 1-2 1-4 1l-2 1h-2l-2 1h-2-2l-4 2-7 2c-2 1-4 2-6 2-2 1-4 2-7 3-1 1-3 1-5 2l-2 1-7 3-4 2-1 2h-1c-1 0-1 1-2 1-1 1-3 2-4 3l-6 4h0l-2 2-2 1-5 4-2 2-1-1c0-2 3-4 5-5l2-2 2-1c1-1 1-1 2-1v-1h-1c-1 1-2 1-3 1 3-2 7-4 9-6 21-15 45-22 70-26z" class="G"></path><path d="M466 293h97c1 6 2 12 2 18v61 151 30c0 2 1 6 0 8h-6l-3 1-4-2c1 1 1 2 1 4v1 1h1c-2 0-1-1-3-1v-1h-1c0 1-1 1-2 1h-1l5 7 9 9h1c1 1 1 1 2 1l1 1c1 2 0 100 1 109 0 8 0 16 2 24 2 13 7 25 13 37 10 20 24 39 43 51 4 3 9 7 14 9s10 4 14 6c-23 0-47 4-67 15-9 5-17 12-25 19-16 15-29 31-39 51-3 6-6 12-8 19-9-21-20-40-35-57-17-17-37-33-60-40-15-5-30-6-45-6l7-3c34-14 60-47 72-81 2-6 5-11 6-18 1-5 1-11 1-16v-23-84-23c0-3-1-6 0-9 2 2 3 3 6 3l-1 1-1 1h4c0-1 1 0 1 0l2-1-1-1-2 1v-1-1h-3v-1s-1-2-2-3c-1 0-1 1-2 0v-1h-1c-1-3 0-6 0-9v-16-74c0-12 1-26-1-38v-1c1-4 1-8 1-11v-23c0-10-1-90 2-94l5-1z"></path><path d="M551 715l4 3-1 2-5-3 2-2z" class="F"></path><path d="M549 643l9 1-8 2c-1-1-1-1-1-3z" class="K"></path><path d="M555 718l4 4-2 1-3-3 1-2z" class="O"></path><path d="M551 709l10 8-12-6 2-2z" class="D"></path><path d="M484 810c0-2 0-2 1-3s2-1 4 0l2 2h-3l-1 1-3-1v1z" class="M"></path><path d="M543 764v1c3 1 5 2 9 2-3 2-5 2-8 2v-1l-1-4z" class="N"></path><path d="M541 709h1l2 2c1 0 1 1 2 1h1l-1-1 1-1 4 5-2 2a30.44 30.44 0 0 1-8-8z" class="K"></path><path d="M484 702c-2 1-5 3-7 4 2-3 5-6 8-9 0 1 1 2 1 3s-1 1-2 2z" class="I"></path><path d="M534 795h0c2 2 4 2 6 2v1c0 1-2 2-4 2h-5l1-1c1 0 1-1 2-1v-3z" class="C"></path><path d="M559 722c4 2 7 5 9 9l-11-8 2-1z" class="U"></path><path d="M484 810v-1l3 1c2 1 3 2 5 2h-2c-1 2 0 2 0 3l-1 2-1-1c-3-1-4-3-4-6z" class="N"></path><path d="M494 829h3c0 2-2 2-3 3v2h1v1h-4c-1-2-2-4-2-6 2 0 3 1 5 0z" class="C"></path><path d="M518 525c0-2-1-3 1-4l3 11-1-1v6 2c-2-4-2-9-3-14z" class="T"></path><path d="M481 678c2 1 5 0 6-1-5 5-11 8-18 11 5-3 8-6 12-10z" class="D"></path><path d="M488 802h-1c0-1-1-2-1-3v-2h-2v-1-1c2 0 4-1 7-1 1 1 1 1 2 3h-2c-1 0-1-1-2-1-1 1-1 1-1 2l3 3h-1c0 1 0 1-1 0l-1 1z" class="R"></path><path d="M532 812v1c2 0 5 0 7-2l3-3h0c0 2 0 5-2 6-2 2-5 2-8 1h-1 0l1-1v-2z" class="F"></path><path d="M530 818c1 1 2 2 4 1 2 0 5-2 6-3-1 2-3 7-6 7-1 1-3 0-5 0 1-2 1-4 1-5z" class="N"></path><path d="M481 678l8-10v2c0 1 0 1 1 2h1l-4 5c-1 1-4 2-6 1z" class="I"></path><defs><linearGradient id="j" x1="464.636" y1="623.128" x2="471.364" y2="616.372" xlink:href="#B"><stop offset="0" stop-color="#807f82"></stop><stop offset="1" stop-color="#a1a09d"></stop></linearGradient></defs><path fill="url(#j)" d="M478 619c-5 2-12 5-18 2h1c5-1 9-2 14-4 1 1 2 1 3 2z"></path><path d="M496 836v1h3c0 2-1 6 1 7h1v1 2l-1-1-3 3v-2c-1 1-1 1-2 1v-1l2-2c1-1 1-2 1-3-1-2-3-2-5-3v-1l3-2zm-8-34l1-1c1 1 1 1 1 0h1l-3-3c0-1 0-1 1-2 1 0 1 1 2 1h2 1l2 4v2h0l-2 1h-2c-2 0-3-1-4-2z" class="C"></path><path d="M494 797l2 4c-2 0-6-2-6-3l1-1h2 1z" class="E"></path><path d="M516 526h1l3 25c-1-1-2-1-2-3 1-1 0-2 0-3 0 4 1 8 0 12-1-3-1-7-1-10l-1-21z" class="T"></path><path d="M543 675c7 5 15 8 21 12-5-1-10-3-14-5-4-1-8-3-11-5 2 1 3 1 4 1h1l1 1h1 1c-1-1-1-1-2-1l-2-2-1-1h1z" class="E"></path><path d="M540 670l15 4c3 1 7 1 10 3-3 0-6 1-10 0-2 0-10-3-12-2h-1l-2-2h2 0 1c1 1 1 1 2 1h2c1 1 2 1 4 1h0c0-1-1-1-2-1h-1c-1-1-3-1-4-2h-1c-1 0-2-1-2-1l-1-1z" class="B"></path><path d="M539 708c4 5 8 11 12 16 3 2 6 5 8 7-9-4-16-13-21-22l1-1z" class="D"></path><path d="M491 643l-1 1c-8 4-14 4-22 2 7 0 14-2 20-6 0 0 0 1-1 2h-1l-2 1c1 0 2 0 3 1h1c1-1 2-1 3-1z" class="C"></path><path d="M475 617c4-2 7-3 10-6 0 1 0 4-1 4 0 1-1 1-2 2l-1 2h0c1 0 1 0 2-1h0l1-1c2-2 3-2 5-3-4 5-10 9-16 12-1 0-2 1-3 1h-1 0c5-2 8-5 11-9h0l-2 1c-1-1-2-1-3-2z" class="E"></path><path d="M468 574c2 1 5-4 6-5h3c-5 6-11 14-19 19 2-5 7-9 10-14h0z" class="O"></path><path d="M544 697c4 4 8 7 13 9-3 1-8 0-10 0l4 3-2 2c-1-1-2-2-3-2l1 1-1 1-1-1c0-1 0-1-1-1v-1c-1-1-1-3 0-4 1 0 1-1 2-1 0-1 0-2-1-2-1-1-1-2-1-3v-1z" class="B"></path><path d="M493 700c0-2 6-12 6-15 2-3 2-7 4-10l-1 6c-1 2-1 2 0 3-3 10-7 20-12 29l-1-1 1-1c1-3 3-7 3-10v-1z" class="D"></path><path d="M491 614h0c3 0 6-7 8-10-1 7-10 18-14 22-3 3-7 4-10 6 1-2 3-3 5-4 3-3 5-6 7-9 1-1 3-5 4-5zm35 190c1 1 2 5 3 5l3 3v2l-1 1-2-1c0 1 1 2 1 4 0 1 0 3-1 5l-3-3c-1-2-1-4-1-6 0-3 1-7 1-10z" class="E"></path><path d="M485 671l-8 6c-4 3-7 6-11 7l11-9c-2 0-4 2-6 2-4 2-8 3-12 4 3-2 6-4 9-5 5-3 9-5 14-8-1 0-1 2-2 2h-1c0 1 0 1-1 1v1c-1 0-2 0-3 1l-3 2 1 1c1 0 2-1 4-2h0c1-1 1-1 2-1l1-1h0 2 1l2-1z" class="B"></path><path d="M526 823v1c1 2 2 3 4 4h4c1 2-2 7-3 8 0 1 0 1-1 1l-1-1c-1 0-2-1-3-1h0c-2-2-2-4-3-6l1-3h0l1 1 1-1v-3z" class="R"></path><path d="M523 829l1-3h0l1 1 1-1c0 3 2 5 3 7v3c-1 0-2-1-3-1h0c-2-2-2-4-3-6z" class="E"></path><path d="M530 792h1l1-1c1 0 2 1 2 2v2 3c-1 0-1 1-2 1l-1 1 1 1c1 1 4 1 6 1l2-1v1c-2 1-3 1-5 1s-4-1-6 0c0 3 2 4 4 6 1 0 2 1 3 0l4-2c0 1-1 1-1 2-2 2-3 2-5 2s-4-1-5-2c-1 0-2-4-3-5l3-8c1-2 1-3 1-4z" class="B"></path><path d="M501 844v-1c1 1 1 0 1 2-1 1 0 3 0 5 0 1 0 1 1 2h0v4c1 1 1 3 1 5v3h1 0c2-3 1-11 1-15l1 1c0 4 1 15-1 17l-1 1-1-2c-2-3-2-5-6-7-1-1-2-1-2-2 1 1 2 1 3 0v-1l-5-1c2-2 5-5 6-8h1v-2-1z" class="D"></path><defs><linearGradient id="k" x1="534.705" y1="553.681" x2="513.795" y2="541.819" xlink:href="#B"><stop offset="0" stop-color="#cccac9"></stop><stop offset="1" stop-color="#fafafb"></stop></linearGradient></defs><path fill="url(#k)" d="M521 539v-2-6l1 1 9 28-1 2h-1v1l1 1v1l-1-1c-1 1-1 1-1 2l-1-2c-1-1-1-4-2-6-1-6-3-13-4-19z"></path><path d="M527 564l1 2c0-1 0-1 1-2l1 1v-1l-1-1v-1h1l1-2c2 5 4 9 6 13 2 2 7 9 7 12-3-4-5-9-9-12l8 18v1c-3-4-4-8-6-12s-5-8-7-13l3 14 3 9h-1c-2-4-3-9-4-13-2-4-3-9-4-13z" class="P"></path><defs><linearGradient id="l" x1="478.046" y1="700.1" x2="486.9" y2="703.576" xlink:href="#B"><stop offset="0" stop-color="#afabac"></stop><stop offset="1" stop-color="#e4e3e2"></stop></linearGradient></defs><path fill="url(#l)" d="M494 682h0l1 1v3h0v1l-1 1h0c0 1-1 2-1 3v1l-1 1c-1 1 0 1-1 1-1 4-3 7-4 11 0 1-1 1-1 2-1 1-1 2-3 2-1 0-6 5-7 6-3 3-8 5-12 7-2 1-4 3-6 3 6-5 12-9 18-14 3-2 6-6 8-9 1-1 2-1 2-2s-1-2-1-3l9-15z"></path><defs><linearGradient id="m" x1="559.429" y1="752.034" x2="546.501" y2="736.151" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#m)" d="M529 719c6 11 15 23 28 26 6 2 10 1 17 0-5 3-13 6-19 5-10-3-19-10-24-19-2-3-3-7-5-10l2 1v1c0 1 1 1 1 2l1 1v2l1 1c1 1 2 4 3 5l8 8c0-1 0-2 1-2v-1c-1-2-4-4-5-6l-2-2-1-1-3-4s-1-1-1-2h0c-1-1-1-1-1-2-1-1-1-2-1-3z"></path><path d="M499 578v2h0c0 1-1 3-1 4v2l-1 2-1 6c0 1 0 2-1 4 0 2-6 10-8 11l-1 1c-2 1-3 1-5 1-4 1-8 2-12 0v-5c6 1 11 1 16-1 1-1 3-1 4-2 0-1 2-6 3-7v-1c3-6 4-12 7-17z" class="I"></path><path d="M460 560c3 0 5 1 8-1 1-1 2-2 2-4-3-3-5-6-8-10h0l6 3h1l-1 1c0 2 3 3 4 5 1 1 0 4 0 5l-4 4c0 1 1 1 2 1s2 1 4 1v1l3-2h0v1c3-1 4-6 7-6 1-3 3-6 4-8l3-2c-2 6-4 10-7 15 0-1 1-3 1-5-3 4-4 6-7 9l-1 1h-3c-1 1-4 6-6 5l1-2v-1c0 1 0 1-1 1h-3v-1h0l-3-1h0c1-1 1-1 2-1 2 0 3 0 4-1h0l2-1-1-1-2 1v-1-1h-3v-1s-1-2-2-3c-1 0-1 1-2 0v-1z" class="F"></path><defs><linearGradient id="n" x1="462.956" y1="751.404" x2="484.398" y2="739.082" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#a8a6a6"></stop></linearGradient></defs><path fill="url(#n)" d="M498 722c-1 2-2 6-3 8-5 9-10 16-20 20-7 2-11 0-18-2l-6-3h0c5 1 11 3 17 2 13-4 20-16 26-27v1c-1 1-1 2-1 3l-1 1c0 1-1 2-1 3h-1v1 1c-1 0-1 1-1 1l-1 1h0c-2 2 0 0-1 2l-1 1-2 2-1 1v1h0c2 1 1 2 4 2 0-1 1-2 1-3l2-2 1-2h1v-2c1-2 0 0 1-1l1-2c0-2 0 0 1-1v-1-1c1-1 1-2 1-2v-1c1 0 1 0 1-1h1z"></path><defs><linearGradient id="o" x1="513.988" y1="882.288" x2="503.094" y2="867.651" xlink:href="#B"><stop offset="0" stop-color="#646463"></stop><stop offset="1" stop-color="#8b8989"></stop></linearGradient></defs><path fill="url(#o)" d="M508 809l2 77 1 8v3c-1-1-1-1-1-2l-6-29 1 2 1-1c2-2 1-13 1-17l-1-1v-1c1-2 0-6 0-8v-1-22c1-1 1-1 1-2 1-2 1-4 1-6z"></path><defs><linearGradient id="p" x1="550.559" y1="625.514" x2="516.839" y2="621.951" xlink:href="#B"><stop offset="0" stop-color="#999897"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#p)" d="M520 601c2 14 6 29 19 38 3 3 6 4 10 4 0 2 0 2 1 3-5 1-9 1-14-2-11-8-15-22-18-35 1-3 0-5 2-8z"></path><defs><linearGradient id="q" x1="488.865" y1="824.188" x2="503.279" y2="826.203" xlink:href="#B"><stop offset="0" stop-color="#81807f"></stop><stop offset="1" stop-color="#b0aead"></stop></linearGradient></defs><path fill="url(#q)" d="M496 803h0c3 7 4 14 5 21l1 12c0 2 0 4 1 6v4h0v1 2 2 1h0c-1-1-1-1-1-2 0-2-1-4 0-5 0-2 0-1-1-2v1h-1c-2-1-1-5-1-7h-3v-1c1 0 2-1 2-2v-1l-3 1h-1v-2c1-1 3-1 3-3h-3c2-2 3-3 4-6h-1c-2 1-4 1-6 1-2-1-3-2-4-4v-1l2 1c2 1 3 1 5 0l3-3c-3 0-5 1-7 0h-1l1-2c0-1-1-1 0-3h2v-1c2-1 3-2 4-4l-1-1c0-1 1-2 1-3z"></path><defs><linearGradient id="r" x1="507.439" y1="869.921" x2="523.751" y2="861.481" xlink:href="#B"><stop offset="0" stop-color="#777877"></stop><stop offset="1" stop-color="#a09e9e"></stop></linearGradient></defs><path fill="url(#r)" d="M515 800v1c1 6 1 13 1 20 0 3-1 7 0 9 1 1 1 3 1 5 0 3 0 7 1 11v2 1c0 2 0 9 1 11v3c1-2-1-5 1-7 0-1 0-2 1-3 0-1 0-1 1-1v-2h1c0-1 0-1 1-2 0-1 0-1-1-1v-2c0-2 0-2-1-3 0-1 1-2 0-2v-1c1-3 0-7 1-11v1c1 2 1 4 3 6h0l3 5v3c-1-1-2-2-3-2 0 2 2 5 2 8l-4-7c1 5 1 8 4 12l1 1-1 1c-1 1-3 2-5 4-2 3-2 7-3 10l-5 21v-14-35-42z"></path><defs><linearGradient id="s" x1="563.149" y1="788.515" x2="531.47" y2="777.084" xlink:href="#B"><stop offset="0" stop-color="#6c6c6b"></stop><stop offset="1" stop-color="#a5a4a3"></stop></linearGradient></defs><path fill="url(#s)" d="M535 754c2 4 5 7 8 10l1 4v1c-2 0-4-1-6-2l13 13h1c3 0 6-1 8-3 0-1 1-1 2-1l6 7c-6 1-13 2-20 5-2 1-5 2-7 3-1 0-3 1-4 0-4-3-10-26-11-32v1l1 1c0 1 1 3 1 4v1c1 1 1 3 2 5v1 1l2 4 1-1c1 0 1-1 1-2h-1v-1-1-1h-1v-1-1-1h-1v-1-2c-1-1-1-2-1-3h0v-1-2c1 1 2 3 3 5 1 0 1 1 1 2 1 1 1 2 2 3 1 0 0 0 1-1v-4h1v-1h0c-1-2-1-2-1-3s0-1-1-2h0v-1l-1-1v-2z"></path><defs><linearGradient id="t" x1="534.706" y1="622.051" x2="532.496" y2="544.089" xlink:href="#B"><stop offset="0" stop-color="#bcbbbb"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#t)" d="M518 557c1-4 0-8 0-12 0 1 1 2 0 3 0 2 1 2 2 3l4 25c2 11 4 22 13 29 4 2 8 3 12 2l6-1c1-1 0-1 1-1-2 4-7 9-11 9h-3c3 3 12 5 16 5h5c-1 1-2 1-3 1-8 1-16-3-23-6 4 4 8 7 13 10l6 3c-2 0-4-1-5-2-5-2-10-6-15-10-3-3-5-6-7-9 4 9 9 18 17 23-2 1-5-2-7-3-2-2-4-4-6-7-3-4-6-8-7-13-3-6-4-13-5-19l-2-18c0-4-1-8-1-12z"></path><defs><linearGradient id="u" x1="457.206" y1="791.401" x2="492.501" y2="775.003" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#a3a2a1"></stop></linearGradient></defs><path fill="url(#u)" d="M493 756l2-6-1 7v3h-1v1 1 1c0 1 0 1-1 2v2c0 1 0 1-1 2v1h0v1c-1 2 0-1-1 2v1c1 0 2 1 3 1v-2c0-1 1-1 1-2s0-2 1-2c0-1 0-2 1-3-1-1-1-1 0-2v-1-1l1-2h0l1-2-6 23c-2 4-3 7-6 10-4 1-10-3-15-4-8-2-16-1-25-2 3 0 6 0 9-1 4-2 7-6 10-9l3 3c1 0 3 1 4 0 2 0 4-2 5-3 5-5 8-9 11-15-3 4-7 6-11 9l11-15 1-1c-1 1-1 2-1 3l-1 1v1h-1v1l-1 1c0 1-1 2-2 2l-1 2c1-1 5-4 5-5l1-1h2v2h0l-1 1c0 1-1 1-1 2s-1 1-1 2h-1v1 1l1 1h1c0-2 1-3 1-4l1-1v-3c1-2 1-2 2-3l1-1z"></path><path d="M493 756l2-6-1 7-6 18c-1 2-1 5-4 6l-1-1c0-2 3-6 4-8 2-5 4-10 6-16z"></path><path d="M466 293h97c1 6 2 12 2 18v61 151 30c0 2 1 6 0 8h-6l-3 1-4-2c1 1 1 2 1 4v1 1h1c-2 0-1-1-3-1v-1h-1c0 1-1 1-2 1h-1c-5-5-7-12-10-18 0 5 1 11 2 15l-23-58 3 17c-2 1-1 2-1 4l-1 1h-1l-1-9c-1 7 0 13 0 20l3 46c0 6 2 12 2 18-2 3-1 5-2 8v-1 1c1 14 0 34 12 45l6 3c-2 1-5 2-8 1h-1l-1-1h0c1 2 2 4 4 5 3 1 6 0 8-1 1-1 1-1 2-1l2-2c2 1 4 1 6 3h0c-2 0-3-1-5-1 1 1 2 1 2 2-3 3-6 5-10 7l5 1 1 1s1 1 2 1h1c1 1 3 1 4 2h1c1 0 2 0 2 1h0c-2 0-3 0-4-1h-2c-1 0-1 0-2-1h-1 0-2l2 2 1 1 2 2c1 0 1 0 2 1h-1-1l-1-1h-1c-1 0-2 0-4-1l-3-1h-2c-2-1-5-4-7-6 4 10 9 20 17 27v1c0 1 0 2 1 3 1 0 1 1 1 2-1 0-1 1-2 1-1 1-1 3 0 4v1c1 0 1 0 1 1l1 1 1 1h-1c-1 0-1-1-2-1l-2-2h-1c0-1-1-3-2-3l-7-11h0l7 13-1 1c-5-7-9-14-12-21-2-6-4-11-6-16 1 16 3 32 9 47 0 1 0 2 1 3 0 1 0 1 1 2h0c0 1 1 2 1 2l3 4 1 1 2 2c1 2 4 4 5 6v1c-1 0-1 1-1 2l-8-8c-1-1-2-4-3-5l-1-1v-2l-1-1c0-1-1-1-1-2v-1l-2-1-4-11c0 4 1 8 2 11 1 9 4 18 7 26 1 3 3 5 4 7v2l1 1v1h0c1 1 1 1 1 2s0 1 1 3h0v1h-1v4c-1 1 0 1-1 1-1-1-1-2-2-3 0-1 0-2-1-2-1-2-2-4-3-5v2 1h0c0 1 0 2 1 3v2 1h1v1 1 1h1v1 1 1h1c0 1 0 2-1 2l-1 1-2-4v-1-1c-1-2-1-4-2-5v-1c0-1-1-3-1-4l-1-1v-1h0l-3-12c0-1 0-4-1-5l1 26c0 5 0 9 1 13s4 7 6 11c0 1 0 2-1 4s-2 5-3 8c0 3-1 7-1 10 0 2 0 4 1 6v3 3l-1 1-1-1h0l-1 3v-1c-1 4 0 8-1 11v1c1 0 0 1 0 2 1 1 1 1 1 3v2c1 0 1 0 1 1-1 1-1 1-1 2h-1v2c-1 0-1 0-1 1-1 1-1 2-1 3-2 2 0 5-1 7v-3c-1-2-1-9-1-11v-1-2c-1-4-1-8-1-11 0-2 0-4-1-5-1-2 0-6 0-9 0-7 0-14-1-20v-1l-3-160c-2 11-2 23-2 34v53l-2 52v30c0 2 0 4-1 6 0 1 0 1-1 2v22 1c0 2 1 6 0 8v1c0 4 1 12-1 15h0-1v-3c0-2 0-4-1-5v-4-1-2-2-1h0v-4c-1-2-1-4-1-6l-1-12c-1-7-2-14-5-21v-2l-2-4c0-2-1-3-2-4l-1-1c2-2 2-2 4-2 3-3 5-7 6-11s1-7 1-11v-15l2-22-6 27-1 2h0l-1 2v1 1c-1 1-1 1 0 2-1 1-1 2-1 3-1 0-1 1-1 2s-1 1-1 2v2c-1 0-2-1-3-1v-1c1-3 0 0 1-2v-1h0v-1c1-1 1-1 1-2v-2c1-1 1-1 1-2v-1-1-1h1v-3l1-7-2 6-1 1c-1 1-1 1-2 3v3l-1 1c0 1-1 2-1 4h-1l-1-1v-1-1h1c0-1 1-1 1-2s1-1 1-2l1-1h0v-2h-2l-1 1c0 1-4 4-5 5l1-2c1 0 2-1 2-2 1-1-1 1 1-1v-1h1v-1l1-1c0-1 0-2 1-3 0-1 1-3 1-4l5-10c4-10 8-23 9-34l-6 17h-1c0 1 0 1-1 1v1s0 1-1 2v1 1c-1 1-1-1-1 1l-1 2c-1 1 0-1-1 1v2h-1l-1 2-2 2c0 1-1 2-1 3-3 0-2-1-4-2h0v-1l1-1 2-2 1-1c1-2-1 0 1-2h0l1-1s0-1 1-1v-1-1h1c0-1 1-2 1-3l1-1c0-1 0-2 1-3v-1c6-14 10-28 10-43l-2 7c-1-1-1-1 0-3l1-6c-2 3-2 7-4 10 0 3-6 13-6 15-5 11-11 22-22 29 5-6 11-13 15-20l-18 16 3-3c4-5 8-8 12-13 2 0 2-1 3-2 0-1 1-1 1-2 1-4 3-7 4-11 1 0 0 0 1-1l1-1v-1c0-1 1-2 1-3h0l1-1v-1h0v-3l-1-1h0c5-11 8-22 10-34l-10 20-3 4h-1c-1-1-1-1-1-2v-2-1l-4 4-2 1h-1-2 0l-1 1c-1 0-1 0-2 1h0c-2 1-3 2-4 2l-1-1 3-2c1-1 2-1 3-1v-1c1 0 1 0 1-1h1c1 0 1-2 2-2 1-1 3-2 4-4-4 1-7-1-10-3 1-1 4-2 6-2 3-1 5-3 7-4 15-11 16-27 19-44v-4 2c-2 13-7 26-17 34-1 0-2 0-3 1h-1c-1-1-2-1-3-1l2-1h1c1-1 1-2 1-2l2-2c7-7 11-17 14-26 2-8 1-15 2-24 1-19 3-39 3-59v1c-2 9-2 19-3 28-1 12-3 24-5 36 0 3-1 7-2 10-2 3-5 10-8 10h0c0-1 1-3 2-4l-4 4c-2 1-3 1-5 3l-1 1h0c-1 1-1 1-2 1h0l1-2c1-1 2-1 2-2 1 0 1-3 1-4l1-1 1-1c2-1 8-9 8-11 1-2 1-3 1-4l1-6 1-2v-2c0-1 1-3 1-4h0v-2c5-16 6-33 7-49 0-4 1-8 1-13l-3 14-5 23-3 15c-1 6-4 11-6 17 0 1-1 2-1 3v-1c1-2 1-5 2-6 3-9 5-15 6-24l-11 32c-1 1-1 1-1 2v-2-1l4-13c1-4 3-8 3-11h0l-4 7c0 1 0 2-1 3 0-2 1-3 1-5l6-14c5-14 9-29 12-44-2 2-3 6-5 9l-6 18c-2 4-4 8-4 11l-3 2c-1 2-3 5-4 8-3 0-4 5-7 6v-1h0l-3 2v-1c-2 0-3-1-4-1s-2 0-2-1l4-4c0-1 1-4 0-5-1-2-4-3-4-5l1-1h-1l-6-3h0c3 4 5 7 8 10 0 2-1 3-2 4-3 2-5 1-8 1h-1c-1-3 0-6 0-9v-16-74c0-12 1-26-1-38v-1c1-4 1-8 1-11v-23c0-10-1-90 2-94l5-1z" class="G"></path><path d="M494 668l-3 4h-1c-1-1-1-1-1-2h1l1-1c1 0 2 0 3-1zm-6-117c1-1 1-3 1-4l2-6 1 1c1-2 1-2 3-4-2 4-4 8-4 11l-3 2z" class="P"></path><path d="M525 814c0 2 0 4 1 6v3 3l-1 1-1-1h0l-1 3v-1c0-4 1-9 2-14z" class="K"></path><path d="M501 594c0 3-1 7-2 10-2 3-5 10-8 10h0c0-1 1-3 2-4v-2l5-9c1-2 2-3 2-5h1z" class="P"></path><path d="M512 322c1 7 2 14 5 20l4 13-8-14-1-1c-3 5-5 10-8 15 3-11 7-22 8-33zm0 29c2 6 2 13 2 19v23l1 32c1 16 1 34 3 50 1 7 3 13 4 20l-10-19c0-1 0-1-1-2-2 7-4 14-8 21 5-28 5-55 6-82l2-30c0-10 0-21 1-32zm-23 77l2-1h0l-16 37c3-3 5-6 7-10 5-9 10-19 14-29l7-13c-2 14-7 26-12 38-3 7-6 14-10 20h0c0 2-1 4-2 6h1l-1 1c-1 1-2 3-3 4h1v-1c1-1 3-2 4-2h1c-2 6-5 11-8 16 0 1 0 3-1 4l-2 4v1c1-1 1-1 1-2s1-1 1-1v-1c1-2 2-2 3-3l12-23h1l-3 6v2l2-3v2h-1v1h0 1 0c4-9 8-19 11-29 1-7 2-14 4-21 1 3 0 5 0 8l-3 19h1l1 1h0c0 11-2 22-4 34-2 10-4 20-8 31-1 3-3 7-5 10v2l1 1 1-1v2c-3 4-6 9-11 10-2 1-5 1-7 0h-1l-6-3h0c3 4 5 7 8 10 0 2-1 3-2 4-3 2-5 1-8 1h-1c-1-3 0-6 0-9v-16-74c0-12 1-26-1-38 3 2 11 4 14 4l1-1 9-5h2v1c-1 0-2 1-2 1 0 1 0 1 1 2h2c0 1 0 2 1 3l2-1-1 1h2z"></path><path d="M475 475l1 1c1 0 0 0 1 1v1c-2-1-2 0-4 1 0 0-1-1-1-2l3-2z" class="D"></path><path d="M473 479c-3 3-7 4-11 5l10-7c0 1 1 2 1 2z" class="C"></path><path d="M487 538l-1-1-1 1c-2 0-4 0-5-1-2 0-2-1-3-2 3 1 5 0 7-1h1v2l1 1 1-1v2zm2-110l-14 27h0l5-12c2-6 4-11 7-15h2z" class="I"></path><path d="M482 421h2v1c-1 0-2 1-2 1 0 1 0 1 1 2h2c0 1 0 2 1 3l-12 8c1-1 0-1 1-1l8-9-11 1 1-1 9-5z" class="B"></path><path d="M475 475c2-1 4-3 6-5h0c0 2-1 4-2 6h1l-1 1c-1 1-2 3-3 4h1v-1c1-1 3-2 4-2l-13 13c-1 2-3 4-5 4l14-17v-1c-1-1 0-1-1-1l-1-1z" class="Q"></path><path d="M476 496c-2 5-6 11-9 16-2 3-4 7-6 10 3-10 8-19 13-28 0 1 0 3-1 4l-2 4v1c1-1 1-1 1-2s1-1 1-1v-1c1-2 2-2 3-3z" class="D"></path><path d="M488 499l1-1h-1c0-1 1-2 1-3h1l1 2h0l1-2h1c-2 7-4 13-7 19-1 2-2 4-3 5 2-5 5-10 6-16-3 5-5 12-9 16 3-6 6-13 8-20zm-2-20v2l2-3v2h-1v1h0 1c-2 6-5 11-7 16-5 11-10 22-17 33h0l13-28c3-7 5-16 9-23z" class="I"></path><defs><linearGradient id="v" x1="482.034" y1="485.179" x2="507.868" y2="470.245" xlink:href="#B"><stop offset="0" stop-color="#c5c4c0"></stop><stop offset="1" stop-color="#f9f7fb"></stop></linearGradient></defs><path fill="url(#v)" d="M500 458h1l1 1c0 4-2 9-3 13-2 7-3 15-6 23h-1l-1 2h0l-1-2h-1c0 1-1 2-1 3h1l-1 1-11 20c0-2 2-6 3-9l10-21c4-10 7-20 10-31z"></path><path d="M466 293h97c1 6 2 12 2 18v61 151 30c0 2 1 6 0 8h-6l-3 1-4-2c-1-1-1-2-2-3 4-1 5-3 7-6-1 1-3 1-5 1-2-1-5-2-6-4-6-6-8-14-11-22s-5-17-7-26c-1-3-2-7-2-10l4 8h0c1 0 1 1 1 2 1 1 1 2 2 3h2v-1h0c-1-1 0-1-1-2v-1s-1-1-1-2h0v-4c-3-7-5-15-6-23-3-13-5-27-5-40 2 7 3 15 4 22 2 9 4 17 6 25 4 11 7 21 12 32-1-7-4-12-5-18-2-6-4-13-5-19-1-2-2-5-1-7 3 6 4 13 7 19v1h1v1l1 1 1 2 1 3s0 1 1 2v1c1 1 1 2 2 3h0l-1-1v-2l-1-1v-1h0v-2c-1-1-1-1-1-2l1-1-1-1v-2h1l-1-3h0l-1-1c0-1-1-2 0-3h1c0 1 1 1 1 2h1c0 1 1 1 1 2v1c1-1 1-1 0-2 0 0-1-1-1-2h0v-1-1c-3-5-6-10-8-16-7-15-12-32-16-49l17 35 6 12 1-1-11-26c3 3 14 25 15 25v-1c-1-5-5-11-8-16-2-4-4-9-8-12h0 13c-3-2-5-3-8-5h3l1-1-1-1-2-2c-2-1-2-2-3-4-3-3-5-5-8-9-5-8-9-19-7-29 1 3 1 7 2 10 1-1 1 0 1-1s1-3 2-4l1 1v1l1 1h1c1 1 2 4 4 5 1-1 1-1 1-2v-3l1-1 1 2h1l3 2c-2-2-3-3-3-5v-1-1c1-1 1-3 1-4l-1-1v-1-1c0-3-1-6 0-9 2-1 5-2 6-3 1-2 1-4 1-5l-1-1-1 1c-2 2-4 2-6 2-3 0-5 0-7-3-8-8-12-23-12-34l3 5c4 1 6 7 9 9l1-1-1-4v-1c-1-4 2-4 4-6 0-1 0-2 1-3 1 0 3-2 4-3s1-2 1-4v-1l-1 2c-1 2-2 3-3 4-3 1-5 0-7-1-9-3-13-14-17-22-3 8-8 17-17 21-2 1-3 1-5 1s-3-2-4-4v-1c0 3 0 8-1 10 0 1 0 2-2 2 0 1-1 1-2 1h2 1s1-1 2-1h1c1 2 2 3 4 3h1v-1c2-1 3-1 4-1 1 2 1 3 2 5h-1l2 2c0-1 0-2 1-3 0-1 1-2 2-2l3-7c1 8-2 18-5 26-2 2-3 6-5 8-1 2-3 3-6 4-2 0-6-1-8-2l-2-3c0 3 0 5 3 7 2 2 4 3 6 4-1 1-3 2-4 2l-3 1-2 3 1 1h2 2 0c-1 1-2 3-3 3h-1-1v2c1 0 2 0 3 1 1 0 2 1 2 2h0c1 1 2 2 2 4h1l1-1c1-1 2-1 3-2 0-1 0-1 1-1l1-1h2l1-3 1 1c0 5-1 9-3 14 0 3-1 3-3 5l-1 1c-1 0-2 1-3 1 0 1-1 1-1 2 1 0 1 0 2-1h1l1-1 2-2 2-2c1-1 1-1 2-1 3-6 4-13 7-19 0 13-3 28-13 38l-1 1h0 0c-1 2-1 2-3 3h-1-1c0 1-1 1-1 1l-2 1c-1 0-1 0-1 1v1c1 0 1 1 2 0 1 0 1 0 2-1l1 1c0 1 0 1-1 1h-2l-9 5-1 1c-3 0-11-2-14-4v-1c1-4 1-8 1-11v-23c0-10-1-90 2-94l5-1z"></path><path d="M546 377c3 0 7 0 10-1h0l-4 4c-1-1-1-2-2-3h-4 0z" class="R"></path><path d="M489 343c-4 0-6 0-10-3h1c3 1 4 2 7 1h1l1 2z" class="C"></path><path d="M537 340h1 5 1c0 1-1 2-2 2-2 1-4 1-5 0v-2z" class="F"></path><path d="M544 376l2 1h0 4c1 1 1 2 2 3h-9 1v-4z" class="K"></path><path d="M548 369h0c2-1 4-2 5-4l1-1v-2h1c-1 4-1 6-4 9l-3 1v-3zm6 53c4 0 7 1 10 0h0c-2 1-4 2-7 3h-1c-2 1-7 1-9 0h6c1-1 1-1 1-3z" class="C"></path><path d="M542 369h6v3c-2 1-4 1-6 2v-5z" class="D"></path><path d="M524 387c1-1 1 0 1-1s1-3 2-4l1 1v1 1 1c-1 3-1 8 0 11v1c-1-4-3-7-4-11z" class="B"></path><path d="M537 415c2 1 6 3 6 4 1 1 2 2 3 4v2l-4-2 1-1-1-1-2-2c-2-1-2-2-3-4z" class="P"></path><path d="M474 390c1 0 3 1 4 1 0 0 1 0 1-1l1 1-1 1 1 1 3-3 1 2c0 1-1 2-2 3h-1c-4 0-6-2-9-5h2z" class="N"></path><path d="M543 419c4 1 7 3 11 3 0 2 0 2-1 3h-6-1v-2c-1-2-2-3-3-4z" class="Q"></path><path d="M528 384l1 1h1c1 1 2 4 4 5 1-1 1-1 1-2v-3 1 4 3l-4 1-3-9v-1z" class="E"></path><path d="M472 390l-5-7c2 1 4 2 7 2 2 0 2-1 4-2 0 1 1 2 0 3-2 1-3 2-5 1 0 2 0 2 1 3h-2z" class="F"></path><path d="M477 379c-3 1-6 0-9-2-2-1-3-3-4-6h1l1 1c2 3 5 3 8 3h0c0 2 1 2 1 4h2z" class="O"></path><path d="M480 372c-2 0-4-1-5-2-4-2-5-6-6-10h1v2l2 2c2 3 6 3 9 4l-2 3 1 1z" class="R"></path><path d="M496 393l-2 2c-4 6-10 8-17 9 0-1 3-2 4-2 5-3 9-7 13-11 0 3-1 3-3 5l-1 1c-1 0-2 1-3 1 0 1-1 1-1 2 1 0 1 0 2-1h1l1-1 2-2 2-2c1-1 1-1 2-1z" class="D"></path><path d="M531 394l4-1v-3l1 1c0 7 3 10 9 14-6-2-11-4-13-10l-1-1z" class="C"></path><path d="M543 388c-2-1-2-1-3-2l1-1c0-1 1-2 1-2 0-1 0-2 1-3 0 2 0 2 1 4 2 1 4 1 7 1 2-1 4-3 6-5-1 2-3 5-6 7-2 1-5 1-7 1h-1z" class="E"></path><path d="M543 388h1c2 0 5 0 7-1v1c-1 1-1 2-2 3 0 2-2 3-3 4-2 1-4 1-5 1-3-2-3-5-4-8 2 2 3 4 5 6l1-1c-1-1-1-2-2-2l1-1c1 1 2 2 3 2l3-3-5-1z" class="O"></path><path d="M521 327c4 1 6 7 9 9l1-1c2 2 4 4 6 5v2c-7-2-13-8-16-15z" class="D"></path><defs><linearGradient id="w" x1="489.92" y1="339.892" x2="497.08" y2="334.608" xlink:href="#B"><stop offset="0" stop-color="#a6a5a4"></stop><stop offset="1" stop-color="#c9c8c7"></stop></linearGradient></defs><path fill="url(#w)" d="M486 325h1c1 2 2 3 4 3h1v-1c2-1 3-1 4-1 1 2 1 3 2 5h-1l2 2c0-1 0-2 1-3 0-1 1-2 2-2-2 6-6 12-11 14l-1 1h-1l-1-2c3-3 6-5 8-8 1-2 1-4 0-5v-1c-1 2-2 3-3 5h0v-4h0l-1 3-1-1-1-1-1-1c-2 0-3-1-3-3z"></path><path d="M479 379l1-1 6 6v1l-2 2c0 1 0 2-1 3l-3 3-1-1 1-1-1-1c0 1-1 1-1 1-1 0-3-1-4-1-1-1-1-1-1-3 2 1 3 0 5-1 1-1 0-2 0-3 1-2 1-3 1-4h0z" class="D"></path><path d="M479 379l1-1 6 6v1l-2 2c-1 0-2 0-4-1 0-1 0-2 1-3v-1l-2-3h0z" class="K"></path><path d="M482 372h2 0c-1 1-2 3-3 3h-1-1v2c1 0 2 0 3 1 1 0 2 1 2 2h0c1 1 2 2 2 4h1l1-1c1-1 2-1 3-2 0-1 0-1 1-1l1-1h2c-3 6-4 11-9 16 2-4 3-6 3-10h-1l-2 7v-7-1l-6-6-1 1h-2-2c0-2-1-2-1-4l5-1c1 0 3-1 4-1l-1-1z" class="I"></path><defs><linearGradient id="x" x1="541.883" y1="526.638" x2="537.158" y2="503.558" xlink:href="#B"><stop offset="0" stop-color="#a2a19f"></stop><stop offset="1" stop-color="#c7c6c7"></stop></linearGradient></defs><path fill="url(#x)" d="M533 493l17 42-6-9c-4-6-7-13-10-20-1-1-4-7-4-8h0c1 0 1 1 1 2 1 1 1 2 2 3h2v-1h0c-1-1 0-1-1-2v-1s-1-1-1-2h0v-4z"></path><defs><linearGradient id="y" x1="469.993" y1="425.389" x2="475.748" y2="417.793" xlink:href="#B"><stop offset="0" stop-color="#949394"></stop><stop offset="1" stop-color="#b8b7b6"></stop></linearGradient></defs><path fill="url(#y)" d="M466 423c5-1 9-4 13-6 4-1 7-2 10-4h0c-1 2-1 2-3 3h-1-1c0 1-1 1-1 1l-2 1c-1 0-1 0-1 1v1c1 0 1 1 2 0 1 0 1 0 2-1l1 1c0 1 0 1-1 1h-2l-9 5-1 1c-3 0-11-2-14-4v-1l1 1h7z"></path><path d="M458 422l1 1h7-3c1 1 2 1 4 1 2 1 5 1 7 0 3 0 6-3 8-3l-9 5-1 1c-3 0-11-2-14-4v-1z" class="N"></path><path d="M535 385l1-1 1 2h1l3 2c-2-2-3-3-3-5v-1-1c1-1 1-3 1-4l-1-1v-1-1c0-3-1-6 0-9l1 2c1 1 2 2 3 2v5 1l2 1v4h-1c-1 1-1 2-1 3 0 0-1 1-1 2l-1 1c1 1 1 1 3 2l5 1-3 3c-1 0-2-1-3-2l-1 1c1 0 1 1 2 2l-1 1c-2-2-3-4-5-6l-2-2v-1z" class="I"></path><path d="M468 326l-1-1h1l2 1c6 2 11 0 15-3 0 1 0 2-2 2 0 1-1 1-2 1h2 1s1-1 2-1c0 2 1 3 3 3l1 1 1 1 1 1c-2 2-4 5-6 8 1-3 3-6 3-9l-5 7 3-7-5 6c0-2 1-3 2-5h-1l-2 2h-1l-1-1c-2 1-3 1-5 0s-5-4-6-6z" class="K"></path><path d="M468 326c5 2 8 3 13 3v1c-1 1-1 1-1 2l1 1h-1l-1-1c-2 1-3 1-5 0s-5-4-6-6z" class="M"></path><path d="M546 477c3 5 7 12 9 18-4-4-7-10-10-14 1 4 3 7 5 11l8 20c1 2 3 6 3 8-2-1-2-3-3-4l-6-14c-2-4-4-7-6-12h-1l10 30-2-2-9-24c-1-3-3-7-4-9h1v1l1 1 1 2 1 3s0 1 1 2v1c1 1 1 2 2 3h0l-1-1v-2l-1-1v-1h0v-2c-1-1-1-1-1-2l1-1-1-1v-2h1l-1-3h0l-1-1c0-1-1-2 0-3h1c0 1 1 1 1 2h1c0 1 1 1 1 2v1c1-1 1-1 0-2 0 0-1-1-1-2h0v-1-1z" class="I"></path><defs><linearGradient id="z" x1="540.43" y1="331.269" x2="534.121" y2="322.782" xlink:href="#B"><stop offset="0" stop-color="#b6b4b4"></stop><stop offset="1" stop-color="#e2e0df"></stop></linearGradient></defs><path fill="url(#z)" d="M530 331v-1c-1-4 2-4 4-6 0-1 0-2 1-3 1 0 3-2 4-3l-3 5c5 4 10 4 16 3 2 0 4-2 6-3l1-1-3 5h-1c-2 0-3 2-5 3s-6 1-9 0h0v1c2 1 3 2 4 4-1 0-2-2-4-2l1 2c-2 0-5-1-6-2l-3-2c0 2 1 2 1 3 1 0 2 1 3 1-3 0-5-2-6-3s0-1-1-1z"></path></svg>