<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="168 120 724 808"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#242324}.C{fill:#c6c5c5}.D{fill:#b5b4b4}.E{fill:#2b2a2b}.F{fill:#545354}.G{fill:#161616}.H{fill:#d5d4d4}.I{fill:#dbdada}.J{fill:#e9e8e9}.K{fill:#383737}.L{fill:#0e0e0e}.M{fill:#4c4b4c}.N{fill:#f5f4f4}.O{fill:#060606}.P{fill:#b4b3b3}.Q{fill:#1a191a}.R{fill:#656464}.S{fill:#9f9e9e}.T{fill:#898888}.U{fill:#979595}.V{fill:#828181}.W{fill:#727071}.X{fill:#5f5d5e}.Y{fill:#eeeded}.Z{fill:#444344}.a{fill:#797879}.b{fill:#8f8e8e}.c{fill:#403f40}.d{fill:#6b6a6b}</style><path d="M717 417c-2 7-2 14-1 22h0l-2-3c-2-8 0-13 3-19z" class="H"></path><path d="M723 385c2 0 4 1 6 1l3 1 1-1 1 1h-2c-2 0-4 0-6 1h0l-8 3v-1c-1 0-2 0-2 1h-1 0c2-1 2-4 5-5 1 0 2 0 3-1z" class="K"></path><path d="M748 258l1 2c-1 2-2 3-2 5 0 1 1 1 1 2 1 1 4 3 6 3l3 3c-1 0-4-1-5-1-1-2-5-3-7-3h0l-1-2 1-3 3-6z" class="I"></path><path d="M334 416l1 1 3 6c0 5-1 10-3 15v2-8c0-6 0-11-1-16z" class="P"></path><path d="M270 254c7 0 15 3 21 6h-1c-2 0-6-2-8-3v1l2 1c2 1 2 2 3 3l1 2c-2-1-5-2-7-5-1 0-2-1-3-2l-8-3z" class="M"></path><path d="M429 179l1-1c2-3 6-5 9-5l-9 12h-2c0-2 2-4 1-6z" class="C"></path><path d="M720 412v1 23h-1v-1c-2-5-2-14-1-19 0-1 1-3 2-4zM254 221c1 1 4 2 5 2l5 3c2 0 6 2 7 4l-1 1c-7-4-15-6-23-9 2 0 5 0 7-1z" class="I"></path><path d="M330 412h0c4 5 4 15 3 20v4h-1 0l-1-2c-1-7 0-15-1-22zm548-187l1 1c3 4 5 8 6 14 0-2 1-3 0-4h1c1 5 1 13 0 18l-3-12v-1-1-1-1c-1-5-2-8-5-12v-1z" class="D"></path><defs><linearGradient id="A" x1="424.191" y1="635.727" x2="416.309" y2="630.273" xlink:href="#B"><stop offset="0" stop-color="#656768"></stop><stop offset="1" stop-color="#868282"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M427 628s1 1 1 2h1 0v1c-9 5-19 8-29 9l1-2c10-1 17-5 26-10z"></path><path d="M639 535h0c2-1 2-2 3-3 2-3 2-3 6-4-2 3-5 7-5 11 0 1 1 2 1 2l1 1c-4 1-7 2-10 4l4-11zM288 264l-1-2c-1-1-1-2-3-3l-2-1v-1c2 1 6 3 8 3h1c3 2 7 4 11 6h1c-2 2-3 3-5 3h-1c-2 0-4-1-6-2 0-1-2-2-3-3z" class="K"></path><path d="M648 528l5-1c-2 4-4 6-6 9v1h-1l1 1h0c4 0 7 1 10 2 2 1 2 1 3 2-1 0-2-1-4-1-3-1-8 0-11 1l-1-1s-1-1-1-2c0-4 3-8 5-11z" class="M"></path><path d="M601 480h1c0 3 0 6-1 9v2 2l-1 1v2l-1 1v1c0 1-1 1-1 2s0 1-1 2l-1 3h-1c0-1-1-1-1-1l-1 2-1-1c0-1 2-4 2-6 2-6 5-13 7-19z" class="J"></path><path d="M567 501c8 14 9 32 9 47-2-6-2-13-3-19 0-9-6-19-10-27h1c4 3 6 11 7 16h1c0-1 0-2-1-3l-1-3-1-3-1-5c-1-1-1-2-1-3z" class="P"></path><path d="M622 628c9 5 16 9 26 10 3 1 7 0 8 1l1 8c-1-2-2-3-4-5-4-2-10-3-14-4-6-2-14-5-19-9h2c1 1 2 2 4 2-1-1-4-1-4-3z" class="T"></path><defs><linearGradient id="C" x1="435.768" y1="627.894" x2="436.32" y2="619.817" xlink:href="#B"><stop offset="0" stop-color="#6f6f6f"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#C)" d="M443 615l1 1c-1 1-4 3-5 4h2 0c1 1 3 1 3 3v1c-5 1-10 4-15 7v-1h0-1c0-1-1-2-1-2l16-13z"></path><path d="M720 384l9-3c7-2 19 0 26 3l3 3h0 0c-3-1-7-2-10-3-7-2-14-1-20-1l5 3-1 1-3-1c-2 0-4-1-6-1l-3-1z" class="X"></path><path d="M753 265c3-3 7-4 11-6h2l4-2 6-2h1c1-1 1-1 3 0-3 1-6 2-9 4-2 0-5 1-6 3 1 0 4-2 5-2-1 1-9 5-9 7-2 1-5 2-7 3-2 0-5-2-6-3l2-1 3-1z" class="Q"></path><path d="M753 265c0 1 0 2-1 3-1 0-1 0-2-1v-1l3-1z" class="B"></path><path d="M395 527h0l8 5v1c-2 0-6-1-8-1l-3-1c-4-1-11 0-15 2h-1c-1 0-2 1-3 2h0c-5 2-8 6-9 10l-1 3v-2c-1-3 2-6 4-9 7-7 18-9 28-10z" class="H"></path><path d="M864 223c1 1 2 1 2 1l1 1c2-2 0-6 2-8 4 2 7 5 9 8v1c3 4 4 7 5 12v1 1 1 1c-5-9-10-13-19-19z" class="C"></path><path d="M516 866c2 5 5 10 9 13-1 5-1 12-2 17l-1 21c-1-4-1-8-1-11-1-14-1-27-6-39l1-1z" class="N"></path><path d="M271 230h0l9 6c8 5 15 11 20 18 3 3 5 5 6 8 0 2 0 3-1 4l-1 1h-1l1-2c0-1 0-2-1-3-4-7-19-23-27-26-4-1-10 4-13 6 2-4 7-5 8-9l-1-2 1-1z" class="Y"></path><path d="M791 225h3c-4 2-10 4-13 8 0 2 0 2 1 3 2 3 4 5 6 7-4-3-9-6-14-5-9 3-18 13-23 20l-2 2-1-2c4-7 12-15 20-20 4-2 8-3 11-7 3-3 8-4 12-6z" class="N"></path><path d="M731 261v1c0 1-1 2-2 3 4 3 9 4 14 4l1-2 1 2h0c2 0 6 1 7 3-5 0-9 1-12 4-1 1-5 7-5 7v-1c1-2 2-3 3-5 1 0 1 0 1-1l-4-1c-2 0-7-1-9-2 0-1 0-1-1-1s-1 2-3 2c3-4 5-9 9-13z" class="C"></path><defs><linearGradient id="D" x1="659.979" y1="528.7" x2="674.086" y2="530.172" xlink:href="#B"><stop offset="0" stop-color="#bebcbd"></stop><stop offset="1" stop-color="#e4e3e4"></stop></linearGradient></defs><path fill="url(#D)" d="M653 527c10 0 20 1 29 8 3 2 6 6 7 11v2c-2-5-3-8-8-11v-1c-3 0-5-2-8-2-7-2-15 0-23 1-1 1-2 1-3 2v-1c2-3 4-5 6-9z"></path><path d="M656 532h0v-1l3-1c1 0 1 1 1 2h-4z" class="D"></path><path d="M660 532c5-1 16-1 20 3l1 1c-3 0-5-2-8-2-7-2-15 0-23 1 1-1 2-1 3-2l3-1h4z" class="T"></path><path d="M291 267c-9-4-21-12-31-11-10 2-8 12-12 17s-13 4-18 5c-4 1-6 2-8 5-1 1 0 1-1 1 0-1 0-2 1-3 3-5 11-5 16-6 10-3 8-10 13-17 2-3 4-5 7-5 4-1 8 0 12 1l8 3c1 1 2 2 3 2 2 3 5 4 7 5 1 1 3 2 3 3zm489-12c4-2 9-2 14-1 3 0 6 3 8 6 4 7 3 13 11 15 6 2 13 1 16 7 1 1 1 2 1 3l-2-2c-6-8-16-3-23-8-2-2-2-4-3-6-2-6-3-11-10-12-10-1-22 6-31 10 0-2 8-6 9-7-1 0-4 2-5 2 1-2 4-3 6-3 3-2 6-3 9-4z" class="N"></path><defs><linearGradient id="E" x1="605.771" y1="428.292" x2="620.525" y2="433.309" xlink:href="#B"><stop offset="0" stop-color="#cececf"></stop><stop offset="1" stop-color="#f7f6f6"></stop></linearGradient></defs><path fill="url(#E)" d="M627 396c0 2 1 3 1 4h1v-2 3c-2 14-6 27-10 39-2 7-4 13-7 19 0 1-1 3-2 4s-5 1-6 0l-2-1-1 1h-2l-2-2c-3-2-6-4-8-5l13 4c1 0 5 1 6 0 5-4 18-55 19-64z"></path><defs><linearGradient id="F" x1="415.228" y1="537.151" x2="410.29" y2="539.517" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#3d3d3e"></stop></linearGradient></defs><path fill="url(#F)" d="M395 527c5 0 10 0 16 1h1c3 5 5 12 6 17-3 0-6-3-9-4s-7-1-11-1c-2 0-4 0-7 1 4-3 9-4 13-5h1 0c-3-2-5-2-8-2-8-1-16-1-24 1 1-1 2-2 3-2h1c4-2 11-3 15-2l3 1c2 0 6 1 8 1v-1c-3-2-5-3-8-5h0z"></path><path d="M398 540c1-1 2-1 2-2h1 3c1 0 1-1 1-1h2c1 1 1 2 1 2 1 1 2 0 2 2h-1c-3-1-7-1-11-1z" class="F"></path><path d="M712 377c16-2 33-3 46 8 4 3 9 8 9 13 1 1 0 1 0 2-2-5-4-10-9-13h0 0l-3-3c-7-3-19-5-26-3l-9 3c-7 2-13 7-19 11 3-4 4-9 7-13 1-2 3-3 4-4v-1z" class="N"></path><path d="M291 388c-4 4-6 7-8 12l-1-1c1-5 5-10 9-13 15-13 33-11 50-9 1 0 1 0 2 1l-2 1c0 3 5 9 7 13l-10-6c-5-2-9-3-14-4h-1l-2 2c-6-2-16-1-22 1-3 1-6 3-8 3z" class="J"></path><path d="M324 382h-1c-2-2-6-2-9-2h0l7-1h0-3l-3-1h5l6 1c6 0 9 3 13 7h-1c-5-2-9-3-14-4z" class="N"></path><path d="M291 388c1-2 4-3 6-4 5-3 16-5 21-3 1 1 3 1 4 1h1l-2 2c-6-2-16-1-22 1-3 1-6 3-8 3z" class="F"></path><defs><linearGradient id="G" x1="452.326" y1="242.746" x2="410.818" y2="234.472" xlink:href="#B"><stop offset="0" stop-color="#ceccce"></stop><stop offset="1" stop-color="#faf9f9"></stop></linearGradient></defs><path fill="url(#G)" d="M428 185h2c-4 7-8 14-10 22-7 22-6 46 0 68l3 6c11 1 21-2 32-5-8 6-26 10-27 22-1 3 0 6 0 9h0v-1c-1-1-1 0-1-1v-2c0-2-1-3 0-5v-1c0-1 0-2 1-3l1-1c1-2 3-5 5-6 1-1 3-2 3-3h-1-2-1c-3-1-8 1-11 0-2-2-3-5-4-8-6-21-9-49-1-70 3-8 7-14 11-21h0z"></path><defs><linearGradient id="H" x1="293.339" y1="236.223" x2="284.137" y2="245.338" xlink:href="#B"><stop offset="0" stop-color="#727071"></stop><stop offset="1" stop-color="#a9a8a8"></stop></linearGradient></defs><path fill="url(#H)" d="M259 223c2 0 2 0 4 1v-2c2 0 3 1 5 1l4 1c10 5 21 12 30 20 2 2 5 4 7 6l1 4c1 2 1 3 1 4h0 1c1 1 1 2 0 3 0 2 0 2-2 3-1 0-1 0-2-1-1 0-1 0-2-1-1-3-3-5-6-8-5-7-12-13-20-18l-9-6h0c-1-2-5-4-7-4l-5-3z"></path><defs><linearGradient id="I" x1="708.204" y1="394.98" x2="705.272" y2="391.879" xlink:href="#B"><stop offset="0" stop-color="#484748"></stop><stop offset="1" stop-color="#606061"></stop></linearGradient></defs><path fill="url(#I)" d="M697 378c1 1 13-1 15-1v1c-1 1-3 2-4 4-3 4-4 9-7 13 6-4 12-9 19-11l3 1c-1 1-2 1-3 1-3 1-3 4-5 5h0 1c0-1 1-1 2-1v1c-9 4-15 10-21 17l-5 5-1 1h0l-1-1 6-34 1-1z"></path><path d="M700 379c1-1 4 0 5 0-1 3-1 5-2 8-2 3-4 5-4 9 0 1-1 1-1 2h0l2-1c0 1-1 2-1 3-1 1-3 6-2 8l-5 5c1-3 3-7 3-10 0-2 1-3 1-5 0-3 0-7 2-10v-1h1c0-2 1-3 1-4l1-3h0l-1-1z" class="E"></path><path d="M696 379h4l1 1h0l-1 3c0 1-1 2-1 4h-1v1c-2 3-2 7-2 10 0 2-1 3-1 5 0 3-2 7-3 10l-1 1h0l-1-1 6-34z" class="B"></path><path d="M341 377c4 0 7 0 10 1h2l1-1 4 17 1 5 2 17c-2-1-3-3-4-5l-2-2c-4-5-8-10-13-14-5-3-14-9-20-9-2 0-5 1-8 1l7-3 2-2h1c5 1 9 2 14 4l10 6c-2-4-7-10-7-13l2-1c-1-1-1-1-2-1z" class="R"></path><path d="M355 409l1-1-2-4c-1-4-2-6-3-10l4 3c1 1 2 2 4 2l2 17c-2-1-3-3-4-5l-2-2z" class="L"></path><path d="M354 377l4 17 1 5c-2 0-3-1-4-2v-1c0-1-1-1-1-2-1-3-3-6-5-8-1-3-2-5-2-8h5-1 2l1-1z" class="B"></path><defs><linearGradient id="J" x1="750.676" y1="236.178" x2="759.995" y2="261.333" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#J)" d="M764 231h3l-1 1h1c1 0 2-1 3-1l2-1h0c3-2 5-2 8-2v1h0l-2 1 1 1c-3 4-7 5-11 7-8 5-16 13-20 20l-3 6-1 3-1 2c-5 0-10-1-14-4 1-1 2-2 2-3v-1c1-2 3-4 4-5 8-10 18-19 29-25z"></path><path d="M545 475c3 4 9 10 11 14l7 13c4 8 10 18 10 27l-2-3v-1 3c0 1-1 1-1 2v4 2l1 1-1 3c-1 5-5 8-5 14 0 2 0 5-1 6 1-22-3-43-13-63-3-6-7-12-11-18h1l4 5v-9z" class="D"></path><defs><linearGradient id="K" x1="563.973" y1="518.014" x2="567.027" y2="503.986" xlink:href="#B"><stop offset="0" stop-color="#4e4d51"></stop><stop offset="1" stop-color="#807f7c"></stop></linearGradient></defs><path fill="url(#K)" d="M570 540c-1-12-3-24-8-35-1-3-2-6-4-9-1-2-2-4-2-7l7 13c4 8 10 18 10 27l-2-3v-1 3c0 1-1 1-1 2v4 2l1 1-1 3z"></path><path d="M327 307c-1 0-2-2-3-3-2-2-4-3-7-4l-2-2h1 2c6 4 12 7 17 12h1v1c2 2 5 4 7 7l1 1c0 1 1 1 1 2l1 1 4 7c2 6 5 11 7 17 3 9 4 18 5 27l5 30c1 6 2 12 1 18s-3 12-5 18h-1c0 1-1 1-1 2l1-5-1-20-2-17-1-5-4-17c-3-14-7-28-13-41l-1 2v1c-3 5-9 9-14 11h0v-1c7-4 12-7 14-14-3-10-7-20-13-28z" class="H"></path><path d="M327 307c-1 0-2-2-3-3-2-2-4-3-7-4l-2-2h1 2c6 4 12 7 17 12 1 3 4 6 7 8 0 2 1 4 0 6-1 0-2 0-3-1s-2-3-3-4c-2-3-3-6-5-9-1-1-2-2-4-3z" class="M"></path><path d="M358 394l1-1h1c1 3 3 6 3 10l2 9c1 5 3 9 1 14l-1 1c0 2-1 5-3 6v3l-1-20-2-17-1-5z" class="D"></path><defs><linearGradient id="L" x1="636.713" y1="394.869" x2="588.473" y2="304.105" xlink:href="#B"><stop offset="0" stop-color="#bdbbbd"></stop><stop offset="1" stop-color="#f4f3f4"></stop></linearGradient></defs><path fill="url(#L)" d="M580 313c-9 3-14 6-21 12 6-9 17-17 29-19 10-1 20 1 27 7 9 6 16 16 19 26l3 12 1 10c1 3 0 3 3 5l-2 10c-1 1-1 3-1 4h-1c-1 1 0 1-1 2s-1 1-2 1l-2 1-3 17h0v-3 2h-1c0-1-1-2-1-4 0-3 1-7 1-10 0-20-3-39-14-56-7-9-14-14-26-16-2 0-5 0-7-1h-1z"></path><path d="M634 339l3 12c-1 0-1 0-2 1-2 0-3 1-4 2 0-4-1-7 1-11v4h0c0 1 0 1 1 2l2-2h0v-1l-1-1v-2-4z" class="S"></path><path d="M591 312c-2-1-4-1-6-1 3-1 6 1 9 0h-2c-2-1-3-2-5-2 6-1 12-1 17 2h0c1 2 2 2 3 4 1 0 2 1 2 1 0 1 1 1 0 2 2 2 4 3 6 6 1 1 2 2 3 4 2 3 6 8 6 11-2-3-4-7-6-10 0-1-1-2-2-2v-1c-6-9-15-12-25-14z" class="N"></path><path d="M591 312c10 2 19 5 25 14v1h-1c0-1-1-2-1-2-1-1-2-1-3-2-1 0-1-1-2-1l-3-3-1 1c1 1 1 0 2 1 0 0 1 1 2 1 1 1 2 3 4 4 1 2 1 2 1 4-7-9-14-14-26-16-2 0-5 0-7-1h-1 0c4-1 7-1 11-1z" class="U"></path><defs><linearGradient id="M" x1="641.391" y1="377.99" x2="627.51" y2="357.599" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#848182"></stop></linearGradient></defs><path fill="url(#M)" d="M637 351l1 10c1 3 0 3 3 5l-2 10c-1 1-1 3-1 4h-1c-1 1 0 1-1 2s-1 1-2 1l-2 1-1-30c1-1 2-2 4-2 1-1 1-1 2-1z"></path><path d="M638 361c1 3 0 3 3 5l-2 10-1-1v-14z" class="G"></path><defs><linearGradient id="N" x1="819.445" y1="266.581" x2="773.61" y2="214.852" xlink:href="#B"><stop offset="0" stop-color="#c2c0c2"></stop><stop offset="1" stop-color="#fbfbfb"></stop></linearGradient></defs><path fill="url(#N)" d="M815 200h5c18-2 38 2 52 13 6 5 13 14 14 23h-1c1 1 0 2 0 4-1-6-3-10-6-14l-1-1c-2-3-5-6-9-8-2 2 0 6-2 8l-1-1s-1 0-2-1c-22-8-46-6-68 2h-2-3c-4 2-9 3-12 6l-1-1 2-1h0v-1c-3 0-5 0-8 2h0l-2 1c-1 0-2 1-3 1h-1l1-1h-3c-11 6-21 15-29 25-1 1-3 3-4 5-4 4-6 9-9 13h0l-6 12v-1l3-6 1-1-1-1c1-2 3-4 3-7h0l-4 5-1-2c8-13 18-28 30-38 6-6 13-11 20-16l3-1 10-5 8-3 2-1 19-7h1c2 0 4 0 5-2z"></path><path d="M747 235c1 0 1 0 2-1l10-7v1 1c-3 2-6 3-8 6-2 1-4 4-6 5-2 3-6 5-8 8-6 6-10 14-15 22l-4 5-1-2c8-13 18-28 30-38z" class="H"></path><defs><linearGradient id="O" x1="794.809" y1="231.918" x2="800.194" y2="208.384" xlink:href="#B"><stop offset="0" stop-color="#9b999a"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#O)" d="M764 231c12-9 28-15 42-18l18-2c4-1 8-1 13-1h0c-3 1-7 0-9 1h-5-2c-1 0-3 1-4 1 1 1 2 1 3 3l-1 1v1c-3 0-5 1-7 1l-20 6-1 1c-4 2-9 3-12 6l-1-1 2-1h0v-1c-3 0-5 0-8 2h0l-2 1c-1 0-2 1-3 1h-1l1-1h-3z"></path><defs><linearGradient id="P" x1="844.551" y1="264.526" x2="803.298" y2="197.851" xlink:href="#B"><stop offset="0" stop-color="#8e8c8d"></stop><stop offset="1" stop-color="#dfdede"></stop></linearGradient></defs><path fill="url(#P)" d="M815 200h5c18-2 38 2 52 13 6 5 13 14 14 23h-1c1 1 0 2 0 4-1-6-3-10-6-14l-1-1c-2-3-5-6-9-8-5-3-10-6-16-8-3-1-5-2-8-3l-2 1c-1 0-1-1-2 0h-12c-2 0-3 0-4 1h-5c-6 1-11 1-16 2-6 1-11 3-17 5s-12 5-18 8c-3 2-7 5-10 6v-1-1l-10 7c-1 1-1 1-2 1 6-6 13-11 20-16l3-1 10-5 8-3 2-1 19-7h1c2 0 4 0 5-2z"></path><path d="M540 479c-11-14-25-28-34-44-2-6-5-12-5-17-1-10 0-18 4-26 5-14 13-25 11-41l-1-1c-1-8-5-16-9-24-5-14-6-29-3-44 1-4 3-9 5-12-1 5-2 11-2 16-1 16 5 30 13 42 5 8 9 15 10 24 3 17-8 32-12 48-1 5-2 11-1 16 2 9 8 18 13 25 1 1 1 1 3 2 1-1 2-3 3-4 5-9 10-18 19-24-6 8-16 20-15 30 0 3 1 4 1 7h1l1 3c1 4 4 8 6 11-2-7-5-13-6-20v-1c0-6 1-12 4-18 0 3-1 5-2 8-1 10 0 18 4 27 4 12 11 23 17 34 1 1 2 4 2 5s0 2 1 3l1 5 1 3 1 3c1 1 1 2 1 3h-1c-1-5-3-13-7-16h-1l-7-13c-2-4-8-10-11-14v9l-4-5h-1z" class="N"></path><defs><linearGradient id="Q" x1="511.757" y1="426.633" x2="509.035" y2="411.404" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#Q)" d="M508 407v-1h1v5l1 1v-2c1-2 0-3 1-5v-3l1 1c-2 9-1 16 2 24h-1-1c-1-2-2-5-3-8v-3c-1-3-1-6-1-9z"></path><defs><linearGradient id="R" x1="510.249" y1="424.175" x2="504.353" y2="408.104" xlink:href="#B"><stop offset="0" stop-color="#716f6f"></stop><stop offset="1" stop-color="#99989a"></stop></linearGradient></defs><path fill="url(#R)" d="M506 405h0v2l1 1 1-1c0 3 0 6 1 9v3l-1 2h1c0 1 0 3-1 5l-1 1c-2-6-3-16-1-22z"></path><defs><linearGradient id="S" x1="511.588" y1="377.324" x2="519.547" y2="378.901" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#cdcdcd"></stop></linearGradient></defs><path fill="url(#S)" d="M506 405c0-5 2-10 4-14 4-8 8-16 10-25 3-10 0-23-5-32l1-1c3 5 6 10 7 15v1c3 15-4 29-8 43-2 4-2 7-3 11l-1-1v3c-1 2 0 3-1 5v2l-1-1v-5h-1v1l-1 1-1-1v-2h0z"></path><defs><linearGradient id="T" x1="517.042" y1="458.422" x2="530.489" y2="436.202" xlink:href="#B"><stop offset="0" stop-color="#373639"></stop><stop offset="1" stop-color="#737270"></stop></linearGradient></defs><path fill="url(#T)" d="M507 427l1-1c1-2 1-4 1-5h-1l1-2 3 8h1 1c5 11 11 21 18 30 4 6 8 12 13 18v9l-4-5c-11-18-26-32-34-52z"></path><path d="M722 270h0c0 3-2 5-3 7l1 1-1 1-3 6v1l6-12h0c2 0 2-2 3-2s1 0 1 1c2 1 7 2 9 2l4 1c0 1 0 1-1 1-1 2-2 3-3 5v1l-6 12c0 2-2 4-2 5l1 1 2-1c1-1 2-1 4-1v2h0c-4 0-9 6-10 9-3 3-4 6-6 9 0 1-2 4-2 5-7 17-16 36-19 54l-1 1-6 34c-2 15-3 31 6 43 5 6 12 10 19 10 7 1 14-1 19-5 3-2 5-6 5-10 0-3-1-7-3-9-1-1-3-3-5-3s-5 1-6 2l-1 1v1h3c1 1 1 2 1 3s-1 3-2 4c-1 0-3 1-4 0-2-1-3-1-3-3-1-2-1-3 0-5 2-3 4-5 7-5 4-2 8-1 11 1 4 2 6 6 7 10 1 5 0 10-3 15l-7 7c-4 2-9 4-13 4-9 3-23-2-32-4-6-2-13-3-18 0-5 2-8 6-10 11l-1 1v-1h-5c-2-1-3-2-4-4 0-1-1-2-1-4-1-1-1-1-1-2 0 1-1 3-2 4l-1 1c3-8 8-16 13-22v-1-3c3-2 6-3 9-4 4 0 8 1 12 0l1-2c-2-1-2-5-2-7-3-13-3-26-2-40 0-3 2-12 0-14 0 0 1-2 2-2v-5h-2l1-2h-2c0-2 2-6 2-9 0-1-2-2-3-2l1-1 2 1h1c0-2-1-5-2-7l-7-5c-4-4-6-9-7-14v-11c1 0 1 0 2 1 0 7 2 17 8 22 2 2 8 5 12 4l1-1c3-7 4-15 6-22 6-19 14-40 25-57l1 2 4-5z" class="Y"></path><path d="M659 453l3-3c2 0 4-1 6-1-1 0-2 1-4 1h1c1 0 2 0 3 1h-3l-6 3v-1z" class="N"></path><path d="M716 470c3-1 6-1 9-1 1 0 3-1 4-1l-1 1c0 1-2 2-1 3h0c1-1 0 0 2-1 0 0 1 0 2-1h3c-4 2-9 4-13 4h-7c0-2 2-3 2-4z" class="C"></path><path d="M677 356c2 1 3 2 4 4 2 3 2 9 2 13-1 1-1 2-1 4l1 1-3 16v-11c-1-1-1-1-1-2v-5h-2l1-2h-2c0-2 2-6 2-9 0-1-2-2-3-2l1-1 2 1h1c0-2-1-5-2-7z" class="H"></path><path d="M679 381c0 1 0 1 1 2v11c-2 14-2 27 0 40 1 3 2 8 1 10-2-1-2-5-2-7-3-13-3-26-2-40 0-3 2-12 0-14 0 0 1-2 2-2z" class="D"></path><path d="M717 273l1 2c-3 5-6 11-9 17l-8 22c-3 8-6 15-8 23-1 3-3 7-2 10h0l1 1c0 1-1 2-2 3-2 1-3 1-4 1 3-7 4-15 6-22 6-19 14-40 25-57z" class="C"></path><defs><linearGradient id="U" x1="721.313" y1="311.335" x2="711.668" y2="314.101" xlink:href="#B"><stop offset="0" stop-color="#3c3d3e"></stop><stop offset="1" stop-color="#5e5b5c"></stop></linearGradient></defs><path fill="url(#U)" d="M728 301l2-1c1-1 2-1 4-1v2h0c-4 0-9 6-10 9-3 3-4 6-6 9 0 1-2 4-2 5l-1-1v1h-2l-1 1s-1 1-2 1c-1 1-5 1-6 0h-1c1-1 1-2 2-3 3-6 8-12 13-16l9-6v-1l1 1z"></path><path d="M728 301l2-1c1-1 2-1 4-1v2h0c-4 0-9 6-10 9-3 3-4 6-6 9 0 1-2 4-2 5l-1-1v1h-2l-1 1s-1 1-2 1c-1 1-5 1-6 0h-1c1-1 1-2 2-3l2 1c1-1 2-1 3-1 3 0 11-13 14-16l7-7c-1 1-3 2-4 3h-1v-1l2-1z" class="J"></path><defs><linearGradient id="V" x1="703.888" y1="453.697" x2="652.73" y2="480.721" xlink:href="#B"><stop offset="0" stop-color="#535252"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#V)" d="M665 451c2 0 4 1 7 1 6 2 11 6 17 10 8 4 18 8 27 8 0 1-2 2-2 4h7c-9 3-23-2-32-4-6-2-13-3-18 0-5 2-8 6-10 11l-1 1v-1h-5c-2-1-3-2-4-4 0-1-1-2-1-4-1-1-1-1-1-2 0 1-1 3-2 4l-1 1c3-8 8-16 13-22l6-3z"></path><path d="M646 476l1-1c1-1 2-3 2-4 0 1 0 1 1 2 0 2 1 3 1 4 1 2 2 3 4 4h5v1c-2 6-5 11-6 18 3 1 6 3 9 5 1 0 2 1 3 2-4-1-11-3-15 0-7 5-10 20-12 28l-4 11-5 19c-4 14-9 34-1 48 3 7 10 14 17 16 5 2 12 1 17-1 3-2 6-5 7-8 0-2 0-6-1-8-1-1-2-2-3-2h-2l-1 1c0 1 1 3 0 5-1 0-3 0-4-1s-2-2-2-3c0-2 0-4 2-5 1-2 3-3 6-3 2-1 5 0 7 2 3 3 4 7 4 12 0 2-1 5-1 8 6 3 12 4 19 1 5-2 8-7 10-11 0-1 0-1 1-1 0 3-2 6-4 8-5 5-11 7-18 7-5 0-8 0-12 2v-1l-1 1h-1c0 1-1 1-1 1h-1l-2 2h0c-1 0-2 1-3 1h-1c-1 1-2 1-4 2h-4c-2-1-3 0-5-1l-5-1h-2c-3-1-5-2-7-3-2 0-4-1-5-2l-2-1c-2-1-4-3-6-4 0 0-1-1-2-1v-1h-1l-1-1c-2-1 0 1-1 0l-2-2s-1 0-1-1c-1 0-1 0-1-1h-1l-1-1c-2-1-1 0-2-1l-2-2c-1 0-3-2-4-2h-1l1-3c3 3 7 6 11 9 3 1 4 2 7 2l1-1c1 0 1 1 2 1h1l-4-2-1 1s0-1-1-2v1c-2-2-4-5-6-7l-7-8c-3-2-6-5-7-8-2 3-4 7-7 9l-1 2c-1 2-1 4-2 7l-2 11-2-1v-1h-1-3-1l-2-2c0-2 3-7 4-10h-1l4-5 4-6c-1-2-1-2 0-4 0-1 1-1 1-2v-4-1h-4 0c-2-1-3-1-5-2l2-1v-2l-1-1h-4-1c0-1 0-1 1-2 1-3 4-6 5-9 1-2 1-7 3-8 1-1 3 0 5 0 1 0 1 0 2 1l5 2h0v-2c0-1 1 0 2-1 1 0 2-3 3-4l1-1c2-1 2-1 3-3h-1l-2 1c-1 0-1 1-2 1-1-4 1-9 2-12l3-6c1-2 3-5 3-7v-1-4l1-1 2-6c1-3 2-6 3-8l1-4h0c0-2 0-3 1-4l2-7c1-1 1-2 1-3h1c1-1 3-1 4-1l6-1h5c2-3 3-7 4-10l2-2 1 1 3-6z" class="J"></path><path d="M625 614c1 0 3 4 4 6s3 4 6 6c3 3 7 5 11 6 2 1 3 2 5 2 2 1 5 0 8 0l-6 1c-3 0-6 0-10-1-7-2-15-8-19-15h1s1 2 2 2v-3c-1-1-1-1-1-2s-1-1-1-2z" class="P"></path><path d="M613 557h1c-4 21-5 41 4 61v1c-2-2-4-5-6-7l1-1c0-1-1-2-1-3l-1-2v-1l-2-5v-2c0-2-1-4-2-6 2 1 3 4 4 6 0-3-1-4-1-6v-3l-1-1v-10-4c1-3 0-6 1-8 0-1 0-2 1-3 0-1 0-2 1-3l1-1v-2z" class="E"></path><path d="M640 483l2-2 1 1-10 27-2 5v-2l1-3v-1c1-1 1-2 2-3l1-4c0-1 1-2 1-4l1-1 1-1-1-1c-2 4-3 8-5 12-6 16-12 31-16 48v7l-1 1c0-3 0-6 1-9 0-1 0-2 1-3 1-3 1-6 2-9l1-6c1 0 1-1 1-2l11-28c0-2 1-3 1-4 0-4-1-4-3-6l-2 2-5-2h-1 3v-1l6-1h5c2-3 3-7 4-10z" class="N"></path><defs><linearGradient id="W" x1="623.952" y1="504.217" x2="629.453" y2="515.587" xlink:href="#B"><stop offset="0" stop-color="#9b9b9d"></stop><stop offset="1" stop-color="#bebcba"></stop></linearGradient></defs><path fill="url(#W)" d="M621 495c1-1 3-1 4-1v1h-3 1l5 2 2-2c2 2 3 2 3 6 0 1-1 2-1 4l-11 28c0 1 0 2-1 2l-1 6h-2l9-32c1-1 1-4 1-5 0-2-2-5-3-6l-3-3z"></path><path d="M621 495c1-1 3-1 4-1v1h-3 1l5 2v1 3 1h-1v-1c0-1-1-2-2-3h-1l-3-3z" class="b"></path><defs><linearGradient id="X" x1="653.755" y1="471.859" x2="646.924" y2="495.764" xlink:href="#B"><stop offset="0" stop-color="#888788"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#X)" d="M646 476l1-1c1-1 2-3 2-4 0 1 0 1 1 2 0 2 1 3 1 4 1 2 2 3 4 4h5v1c-2 6-5 11-6 18 3 1 6 3 9 5-2-1-4-1-6-1s-3 1-5 1v-1c2-1 4-1 6-1l-5-2v-3-1c-2 0-3-1-5-1h0c-3 0-6-1-8-2h0c-2 4-4 11-7 15l10-27 3-6z"></path><path d="M606 556c2 0 2 0 4-2 1 1 1 2 2 3h1v2l-1 1c-1 1-1 2-1 3-1 1-1 2-1 3-1 2 0 5-1 8v4 10l1 1v3c0 2 1 3 1 6-1-2-2-5-4-6 1 2 2 4 2 6-4-7-8-14-11-22l-1-1c-1 0-2-1-3-3-1-1-2-3-3-4-2-1-3 0-4 1l-1-1c0-1 0-2 1-3h5 0l5 2h0v-2c0-1 1 0 2-1 1 0 2-3 3-4l1-1c2-1 2-1 3-3z" class="B"></path><path d="M600 567h0l1-1c0-1 0-2 1-3h1v1c-1 1-1 2-1 3h0c-1 8 1 17 5 25h0c1 2 2 4 2 6-4-7-8-14-11-22 1-3 1-6 2-9z" class="H"></path><path d="M606 556c2 0 2 0 4-2 1 1 1 2 2 3h1v2l-1 1c-3 0-7 2-8 4l-1 2-1 1h0c0-1 0-2 1-3v-1h-1c-1 1-1 2-1 3l-1 1h0c-1 3-1 6-2 9l-1-1c-1 0-2-1-3-3-1-1-2-3-3-4-2-1-3 0-4 1l-1-1c0-1 0-2 1-3h5 0l5 2h0v-2c0-1 1 0 2-1 1 0 2-3 3-4l1-1c2-1 2-1 3-3z" class="Z"></path><path d="M612 557h1v2l-1 1c-3 0-7 2-8 4l-1 2-1 1h0c0-1 0-2 1-3v-1h-1c-1 1-1 2-1 3l-1 1h0c0-1 0-3 1-4 3-4 6-5 11-6z" class="C"></path><path d="M587 569l-1-1c0-1 0-2 1-3h5c1 2 3 3 4 5h-1v2h1v-2h1c1 1 0 4 0 5-1 0-2-1-3-3-1-1-2-3-3-4-2-1-3 0-4 1z" class="N"></path><path d="M620 495h1l3 3c1 1 3 4 3 6 0 1 0 4-1 5l-9 32-3 16h-1-1c-1-1-1-2-2-3-2 2-2 2-4 2h-1l-2 1c-1 0-1 1-2 1-1-4 1-9 2-12l3-6c1-2 3-5 3-7v-1-4l1-1 2-6c1-3 2-6 3-8l1-4h0c0-2 0-3 1-4l2-7c1-1 1-2 1-3z" class="B"></path><path d="M620 512c0-2 1-4 2-6h0c-1 11-6 23-8 34-1 3 0 7-1 9s-2 4-3 5c-2 2-2 2-4 2v-1l5-15c1-4 2-9 4-13l1-1c1-4 4-10 4-14z" class="M"></path><path d="M611 540c0 5 0 10-3 15h-2l5-15z" class="F"></path><defs><linearGradient id="Y" x1="603.122" y1="517.4" x2="618.105" y2="546.513" xlink:href="#B"><stop offset="0" stop-color="#474545"></stop><stop offset="1" stop-color="#868586"></stop></linearGradient></defs><path fill="url(#Y)" d="M616 509c1-2 2-3 2-5h1v7l1 1c0 4-3 10-4 14l-1 1c-2 4-3 9-4 13l-5 15v1h-1l-2 1c-1 0-1 1-2 1-1-4 1-9 2-12l3-6c1-2 3-5 3-7v-1-4l1-1 2-6c1-3 2-6 3-8l1-4h0z"></path><path d="M603 557l-1-1c0-2 0-2 1-3 1 1 1 1 2 3l-2 1z" class="b"></path><path d="M616 509c1-2 2-3 2-5h1v7l1 1c0 4-3 10-4 14l-1 1 3-12v-1-4h0l-3 3 1-4h0z" class="c"></path><path d="M580 587l2-1v-2l-1-1h-4-1c0-1 0-1 1-2 1-3 4-6 5-9 1-2 1-7 3-8 1-1 3 0 5 0 1 0 1 0 2 1h0-5c-1 1-1 2-1 3l1 1c1-1 2-2 4-1 1 1 2 3 3 4 1 2 2 3 3 3l1 1c3 8 7 15 11 22v2l2 5v1l1 2c0 1 1 2 1 3l-1 1-7-8c-3-2-6-5-7-8-2 3-4 7-7 9l-1 2c-1 2-1 4-2 7l-2 11-2-1v-1h-1-3-1l-2-2c0-2 3-7 4-10h-1l4-5 4-6c-1-2-1-2 0-4 0-1 1-1 1-2v-4-1h-4 0c-2-1-3-1-5-2z" class="O"></path><path d="M594 587c1 0 3-1 4-2 1 2 3 4 3 6l-1-1c-1-1-2-1-4-1h-2v-2z" class="C"></path><path d="M587 569c1-1 2-2 4-1 1 1 2 3 3 4l-3 1c-1-2-1-2-2-2v-2h1c-1 0-2 0-2 1-1 1-2 3-3 4h0c0-2 1-4 2-5z" class="G"></path><path d="M589 571c1 0 1 0 2 2l-1 5c0 1 0 4-1 5-1 0-2-1-3-2v-2c1-1 1-3 1-4v-1c0-1 1-2 2-3z" class="Z"></path><path d="M582 610h1l1-1v1h1l-2 4c0 1 0 1 2 2h0c2-3 2-4 2-7 0 0 1-3 1-4h3l-1 2c-1 2-1 4-2 7l-2 11-2-1v-1h-1-3-1l-2-2c0-2 3-7 4-10l1-1z" class="G"></path><path d="M580 587l2-1v-2l-1-1h-4-1c0-1 0-1 1-2 1-3 4-6 5-9 1-2 1-7 3-8 1-1 3 0 5 0 1 0 1 0 2 1h0-5c-1 1-1 2-1 3l1 1c-1 1-2 3-2 5-1 3-2 7-1 10 1 2 5 3 7 4 1 0 2 0 3-1v2 1c-1 2-1 5-3 8-2 4-6 7-9 12l-1 1h-1l4-5 4-6c-1-2-1-2 0-4 0-1 1-1 1-2v-4-1h-4 0c-2-1-3-1-5-2z" class="H"></path><path d="M589 589l3 1c0 3-1 6-3 8l-1 2c-1-2-1-2 0-4 0-1 1-1 1-2v-4-1z" class="Q"></path><path d="M591 605c3-2 5-6 7-9 1 3 4 6 7 8l7 8c2 2 4 5 6 7v-1c1 1 1 2 1 2l1-1 4 2h-1c-1 0-1-1-2-1l-1 1c-3 0-4-1-7-2-4-3-8-6-11-9l-1 3h1c1 0 3 2 4 2l2 2c1 1 0 0 2 1l1 1h1c0 1 0 1 1 1 0 1 1 1 1 1l2 2c1 1-1-1 1 0l1 1h1v1c1 0 2 1 2 1 2 1 4 3 6 4l2 1c1 1 3 2 5 2 2 1 4 2 7 3h2l5 1c2 1 3 0 5 1h4c2-1 3-1 4-2h1c1 0 2-1 3-1h0l2-2h1s1 0 1-1h1l1-1v1c-5 3-9 6-15 7-1-1-5 0-8-1-10-1-17-5-26-10 0 2 3 2 4 3-2 0-3-1-4-2h-2-1c-4-2-8-5-12-6-8 18-14 38-20 57-5 13-10 26-14 39 0 2-1 4 0 6 1 3 4 5 7 6 5 3 10 4 16 5-10 0-21-2-29 6-3 4-5 8-6 13-3 6-5 12-7 19h0c-2 7-5 15-8 22-4 13-7 25-10 38-1 6-3 12-3 19-1 5 0 10-2 15v1-4l1-10c1-7 0-16 0-23s0-13-1-19l-1-3c0-1 0-2-1-3v-4c0-3-1-6-2-9l1-2-2-6c1-1 0-3 1-4 1 1 1 2 2 3h0l2 3h0v2l1 2c1 0 4-11 4-13 2-4 3-8 5-11l12-33c-1 0-1-1-1-1-1 0-1 1-2 1l-1-1-1-1v2c-1 0-2 0-2 1-1 0-1-1-2-1v1c-1 0-2 0-3-1l-1-2c-1-2-1-2-3-2 0-1 1-2 1-3h3l1-4c0-5 1-9-1-13l1-1h1l2 1h0c0-2-2-4-3-5h-1v-1l-1-3c1 1 1 0 2 1h0v-2c-2 0-2 0-4-2l-1-2v-5c1-1 1-1 0-2l-1-10c-1-2-1-4-2-7-1-4-4-8-7-10l-8-8-5-9c-2-3-5-6-7-9-1-1-2-2-2-3h9 1l2-1c2-1 3-1 4-2h0 2c2 1 3 2 5 2h6v-1c2-1 2-3 2-5v-1l1-2v-1h0l1-1c1 2 2 4 3 4h1c1 3 2 5 4 8v-1c3 2 5 7 6 11v3h1c0 1 1 1 1 1h1c1-1 1-1 2-1l1-1c1-1 3-3 4-5 2-5 4-12 9-15 1 1 1 1 2 1 2 0 3 0 4 1 1 0 2 1 3 2 1-3 3-5 3-7h1v1l2 1 2-11c1-3 1-5 2-7l1-2z" class="N"></path><path d="M605 604l7 8c2 2 4 5 6 7v-1c1 1 1 2 1 2h0c-4-1-7-3-10-6 0-4-4-5-4-10z" class="T"></path><path d="M526 786c1-1 0-3 1-4 1 1 1 2 2 3h0l2 3h0v2l1 2c-2 8-2 13-1 21l-1-3c0-1 0-2-1-3v-4c0-3-1-6-2-9l1-2-2-6z" class="B"></path><path d="M526 786c1-1 0-3 1-4 1 1 1 2 2 3h0l2 3h0v2 1l-1 4h-1l-1-3-2-6z" class="L"></path><path d="M591 605c3-2 5-6 7-9 1 3 4 6 7 8 0 5 4 6 4 10h0c-6-4-8-7-11-13l-24 70-1 1c-1 2-2 5-3 7h0l-1-5c0 2-1 4-2 4h-3l-1-1v-3c-2 1-5 0-7-1l-5-1h0 0l-1-1c1-4 6-7 10-9 0-1-1-2-1-2 4-4 6-8 8-13h0l1-1h1l1-1c1-3 2-7 4-9v4h0c1-4 1-7 1-10l2 1v-3c1 0 2 1 3 2 1-3 3-5 3-7h1v1l2 1 2-11c1-3 1-5 2-7l1-2z" class="S"></path><path d="M590 607h1l1-1 1 1c0 2-2 5-2 8-1 3-2 7-4 11h-2l1-1 2-11c1-3 1-5 2-7z" class="d"></path><path d="M583 623h1v1l2 1-1 1c-1 6-3 12-5 18l-1-3c-1 0-1 1-2 1h0c0-2 0-3 1-5 0-2 0-4-1-6v-3c1 0 2 1 3 2 1-3 3-5 3-7z" class="B"></path><path d="M578 637l1-4c0-1 1-1 2-2v1c0 2-1 4-1 6 0 1-1 2-1 3-1 0-1 1-2 1h0c0-2 0-3 1-5z" class="G"></path><path d="M575 630l2 1c1 2 1 4 1 6-1 2-1 3-1 5h0c1 0 1-1 2-1l1 3-3 7h-2c0-1 0-1-1-2-2 1-2 2-3 3 0 2-1 2-2 3l5-15h0c1-4 1-7 1-10z" class="F"></path><path d="M577 642h0c1 0 1-1 2-1l1 3-3 7h-2c0-1 0-1-1-2 0-2 1-5 3-7z" class="Q"></path><path d="M569 655c1-1 2-1 2-3 1-1 1-2 3-3 1 1 1 1 1 2h2l-6 18c-1 1-2 4-2 5 0 2-1 4-2 4h-3l-1-1v-3c1-2 3-5 4-8-1-1-1-1-1-3s1-6 3-8z" class="L"></path><path d="M569 655c1-1 2-1 2-3 1-1 1-2 3-3-2 5-4 12-7 17-1-1-1-1-1-3s1-6 3-8z" class="M"></path><path d="M575 651h2l-6 18c-1 1-2 4-2 5 0 2-1 4-2 4h-3c1-1 1-1 1-2 1-3 2-5 3-7 2-6 4-12 7-18z" class="B"></path><defs><linearGradient id="Z" x1="567.263" y1="669.541" x2="558.202" y2="655.753" xlink:href="#B"><stop offset="0" stop-color="#403f3f"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#Z)" d="M568 646h1l1-1c1-3 2-7 4-9v4l-5 15c-2 2-3 6-3 8s0 2 1 3c-1 3-3 6-4 8-2 1-5 0-7-1l-5-1h0 0l-1-1c1-4 6-7 10-9 0-1-1-2-1-2 4-4 6-8 8-13h0l1-1z"></path><path d="M567 647c1 3-1 5-2 8s-3 5-5 7c0-1-1-2-1-2 4-4 6-8 8-13z" class="E"></path><path d="M559 660s1 1 1 2c-4 2-9 5-10 9l1 1h0 0l5 1c2 1 5 2 7 1v3l1 1h3c1 0 2-2 2-4l1 5h0c1-2 2-5 3-7l1-1c-1 3-1 5-1 8-2 2-3 3-3 6l-1 1c0 1-1 2-1 3v1l-8 21c0 8-2 15-5 22-1 1-1 2-2 2s-1-1-1-1c-1 0-1 1-2 1l-1-1-1-1v2c-1 0-2 0-2 1-1 0-1-1-2-1v1c-1 0-2 0-3-1l-1-2c-1-2-1-2-3-2 0-1 1-2 1-3h3l1-4c0-5 1-9-1-13l1-1h1l2 1h0c0-2-2-4-3-5h-1v-1l-1-3c1 1 1 0 2 1h0v-2c-2 0-2 0-4-2l-1-2v-5c1-1 1-1 0-2l-1-10c0 1 1 2 1 3v2l1 4v-6-5c0-2-1-7-1-9 0-1 3-1 3-1 7-1 13-4 19-8z" class="Y"></path><path d="M556 673c2 1 5 2 7 1v3l-14-2c-1 0-1 0-1-1l8-1zm-18 55h3c0 1 0 2 2 3l1-1c1 1 3 2 4 3v2c-1 0-2 0-2 1-1 0-1-1-2-1v1c-1 0-2 0-3-1l-1-2c-1-2-1-2-3-2 0-1 1-2 1-3z" class="G"></path><path d="M544 735c-1 0-1 0-2-1 0-1-1-1 0-3 1 0 1 1 1 1l1 1c2 1 2 1 4 0v2c-1 0-2 0-2 1-1 0-1-1-2-1z" class="L"></path><path d="M551 695c-1-2-3-4-4-6 1-3 2-7 4-10h3l-2 1c0 2 1 2 2 3l3 1 1 1c-3 0-4 0-6 2-1 1 1 5-1 7l1 1h-1z" class="G"></path><path d="M542 706l1-1c1 0 3 2 4 2l1-1v-5l1-1c0 1 1 1 1 1h2c1 4 1 7-1 10-1 2-2 3-4 4h-3c-1-2-1-3-1-5l2 1h0c0-2-2-4-3-5z" class="B"></path><path d="M550 716c1 0 3-1 5 0-2 5-4 9-7 13l-5-1 2-9c1-1 3-2 5-3z" class="L"></path><path d="M548 721h1l1 1-2 5h-2v-2c0-2 1-3 2-4z" class="O"></path><path d="M559 680c2 0 3 1 4 2 2 2 4 5 5 8l-8 21h0c-3 6-5 13-8 19-1 0-3 0-4-1 3-4 5-8 7-13 2-1 5-16 6-19-1-1-1-2-2-3v-1c1-2 2-3 4-5v-1c-1-3-2-5-4-7z" class="U"></path><path d="M559 694v-1c1-2 2-3 4-5 0 3-1 6-2 9-1-1-1-2-2-3z" class="G"></path><path d="M554 679c1 0 3 1 5 1 2 2 3 4 4 7v1c-2 2-3 3-4 5v1c1 1 1 2 2 3-1 3-4 18-6 19-2-1-4 0-5 0 2-2 3-5 4-8 1-5 0-8-3-13h1l-1-1c2-2 0-6 1-7 2-2 3-2 6-2l-1-1-3-1c-1-1-2-1-2-3l2-1z" class="B"></path><path d="M557 697l-2 3-1-1v-3c-1-1 0 0-1-2v-3s0-1 1-1c1-2 2-2 4-2 0 3-1 6-1 9z" class="M"></path><path d="M554 679c1 0 3 1 5 1 2 2 3 4 4 7v1c-2 2-3 3-4 5v1c0 3 0 5-1 7h-1v-4c0-3 1-6 1-9v-3l-1-1-3-1c-1-1-2-1-2-3l2-1z" class="O"></path><defs><linearGradient id="a" x1="543.077" y1="667.328" x2="556.817" y2="697.534" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#3f3e3f"></stop></linearGradient></defs><path fill="url(#a)" d="M559 660s1 1 1 2c-4 2-9 5-10 9l1 1h0 0l5 1-8 1c0 1 0 1 1 1-1 2-1 4-1 6-1 2-3 6-3 8 1 4 6 8 7 12h-2s-1 0-1-1l-1 1v5l-1 1c-1 0-3-2-4-2l-1 1h-1v-1l-1-3c1 1 1 0 2 1h0v-2c-2 0-2 0-4-2l-1-2v-5c1-1 1-1 0-2l-1-10c0 1 1 2 1 3v2l1 4v-6-5c0-2-1-7-1-9 0-1 3-1 3-1 7-1 13-4 19-8z"></path><path d="M538 678c1 5 0 11 1 16 1 3 2 5 3 7-2 0-2 0-4-2l-1-2v-5c1-1 1-1 0-2l-1-10c0 1 1 2 1 3v2l1 4v-6-5z" class="H"></path><defs><linearGradient id="b" x1="633.308" y1="664.859" x2="547.066" y2="684.747" xlink:href="#B"><stop offset="0" stop-color="#9b9b9c"></stop><stop offset="1" stop-color="#e2e1e0"></stop></linearGradient></defs><path fill="url(#b)" d="M554 774v-4l1-1c0-1 1-2 1-3 0-2-1 1 0-2l1-2c0-1 1-2 1-3h0l1-1v-1c0-1 0-2 1-2 0-1 0-2 1-3 0-1 1-2 1-3h0c1-2 1-3 2-3l1-1c0-2 1-3 3-4l3-3c-3 0-6-1-10 0h0c-1-1-2-1-2-2l-1-1c-1 3-2 6-3 10 0 1-1 6-3 7 1-2 2-5 2-7l6-17 16-47 4-13 12-35c1-5 2-10 4-14 1-3 3-4 5-6 4 3 9 6 13 10 3 1 6 3 8 5 0 2 3 2 4 3-2 0-3-1-4-2h-2-1c-4-2-8-5-12-6-8 18-14 38-20 57-5 13-10 26-14 39 0 2-1 4 0 6 1 3 4 5 7 6 5 3 10 4 16 5-10 0-21-2-29 6-3 4-5 8-6 13-3 6-5 12-7 19z"></path><path d="M537 624v-1h0l1-1c1 2 2 4 3 4h1c1 3 2 5 4 8v-1c3 2 5 7 6 11v3h1c0 1 1 1 1 1h1c1-1 1-1 2-1l1-1c1-1 3-3 4-5 2-5 4-12 9-15 1 1 1 1 2 1 2 0 3 0 4 1v3l-2-1c0 3 0 6-1 10h0v-4c-2 2-3 6-4 9l-1 1h-1l-1 1h0c-2 5-4 9-8 13-6 4-12 7-19 8 0 0-3 0-3 1 0 2 1 7 1 9v5 6l-1-4v-2c0-1-1-2-1-3-1-2-1-4-2-7-1-4-4-8-7-10l-8-8-5-9c-2-3-5-6-7-9-1-1-2-2-2-3h9 1l2-1c2-1 3-1 4-2h0 2c2 1 3 2 5 2h6v-1c2-1 2-3 2-5v-1l1-2z" class="N"></path><path d="M523 646l4 6c1-2 1-4 1-6 2 3 5 9 9 12h3c1 0 3 1 5 1 1 0 3-1 5-2h0l7-2c2-1 3-2 5-3h0 1c1 0 3-4 4-5h0c-2 5-4 9-8 13-6 4-12 7-19 8 2-2 4-2 6-3 3-1 6-2 9-4-2 1-3 2-5 2-1 1-3 1-5 1-2 1-2 2-5 2-1-1-2-1-3-1-1 1-2 1-3 1-3-4-6-9-8-13-1-3-3-5-3-7z" class="J"></path><path d="M557 655c2-1 3-2 5-3h0 1c-4 6-10 10-16 11h-8s2 0 2-1c2-1 1-1 3-2l7-2-1-1h0l7-2z" class="c"></path><path d="M551 658c2-1 5-2 8-2-4 3-9 6-13 5-1 0-1 0-2-1l7-2z" class="F"></path><defs><linearGradient id="c" x1="528.277" y1="654.595" x2="543.16" y2="655.14" xlink:href="#B"><stop offset="0" stop-color="#090908"></stop><stop offset="1" stop-color="#2a282a"></stop></linearGradient></defs><path fill="url(#c)" d="M528 646c2 3 5 9 9 12h3c1 0 3 1 5 1 1 0 3-1 5-2l1 1-7 2c-2 1-1 1-3 2 0 1-2 1-2 1h-1c-1-1-3-1-4-2-3-2-5-7-7-9 1-2 1-4 1-6z"></path><path d="M536 627c1 3 3 4 2 7 0 1-1 3-2 4h-2v2c1 2 1 3 0 5 0 2 0 2 1 4l-1-1-1 2c1 1 2 2 3 4 0 2 2 3 4 4h-3c-4-3-7-9-9-12 0 2 0 4-1 6l-4-6-9-12h1l2-1c2-1 3-1 4-2h0 2c2 1 3 2 5 2h6v-1c2-1 2-3 2-5z" class="B"></path><path d="M522 638c-1-2-1-2-1-4h1c2 1 3 2 3 3 1 2 2 4 2 7l-5-6z" class="O"></path><path d="M517 633c2-1 3-1 4-2h0 2c2 1 3 2 5 2h6c-2 1-4 2-6 4h-1l-1-1-1 1c0-1-1-2-3-3h-1c0 2 0 2 1 4h-1c0-1-1-3-1-3h-1c-2-1-3-1-4-1l2-1z" class="Q"></path><path d="M514 634h1c1 0 2 0 4 1h1s1 2 1 3l7 8c0 2 0 4-1 6l-4-6-9-12z" class="O"></path><path d="M536 627c1 3 3 4 2 7 0 1-1 3-2 4h-2v2c1 2 1 3 0 5 0 2 0 2 1 4l-1-1-1 2c-1-1-1-2-1-3h0c-3-4-4-6-5-10h1c2-2 4-3 6-4v-1c2-1 2-3 2-5z" class="R"></path><path d="M527 637h1c1 0 3 0 3 1 0 3 0 5 1 8h0l2 2-1 2c-1-1-1-2-1-3h0c-3-4-4-6-5-10z" class="M"></path><path d="M537 624v-1h0l1-1c1 2 2 4 3 4h1c1 3 2 5 4 8v-1c3 2 5 7 6 11v3h1c0 1 1 1 1 1h1c1-1 1-1 2-1l1-1c1-1 3-3 4-5 2-5 4-12 9-15 1 1 1 1 2 1 2 0 3 0 4 1v3l-2-1c0 3 0 6-1 10h0v-4c-2 2-3 6-4 9l-1 1h-1l-1 1c-1 1-3 5-4 5h-1 0c-2 1-3 2-5 3l-7 2h0c-2 1-4 2-5 2-2 0-4-1-5-1-2-1-4-2-4-4-1-2-2-3-3-4l1-2 1 1c-1-2-1-2-1-4 1-2 1-3 0-5v-2h2c1-1 2-3 2-4 1-3-1-4-2-7v-1l1-2z" class="T"></path><path d="M536 643c1-2 2-2 3-3 2 0 3 1 4 2 3 3 6 6 8 9-2 0-6 1-8 0l-2-2c-2-2-3-4-5-6z" class="D"></path><path d="M537 624v-1h0l1-1c1 2 2 4 3 4h1c1 3 2 5 4 8v-1c3 2 5 7 6 11v3l-2-1c-1-1-2-3-3-5-1-1-3-2-4-4s-2-5-4-7c-1-2-2-4-2-6z" class="B"></path><path d="M553 647v2h3c2 0 6-4 7-6 2-1 3-3 5-5 1-1 1-4 3-5l3-3h0 1c0 3 0 6-1 10h0v-4c-2 2-3 6-4 9l-1 1h-1c2-4 4-8 5-11l-1-1c-2 4-3 8-5 11-2 2-5 5-8 6h-2c-1-1-1 0-2 0s-3 0-3-1c-1-1-2-2-2-4l2 1h1z" class="R"></path><path d="M571 626c1 1 1 1 2 1 2 0 3 0 4 1v3l-2-1h-1 0l-3 3c-2 1-2 4-3 5-2 2-3 4-5 5-1 2-5 6-7 6h-3v-2c0 1 1 1 1 1h1c1-1 1-1 2-1l1-1c1-1 3-3 4-5 2-5 4-12 9-15z" class="E"></path><path d="M534 640c1 1 2 3 2 3 2 2 3 4 5 6v3c1 1 1 1 3 2 1 1 2 2 4 1h1c1 0 4-1 5-1 1 1 2 1 3 1l-7 2h0c-2 1-4 2-5 2-2 0-4-1-5-1-2-1-4-2-4-4-1-2-2-3-3-4l1-2 1 1c-1-2-1-2-1-4 1-2 1-3 0-5z" class="V"></path><path d="M534 648l1 1c2 2 4 6 7 7l1 1h7 0c-2 1-4 2-5 2-2 0-4-1-5-1-2-1-4-2-4-4-1-2-2-3-3-4l1-2z" class="X"></path><path d="M234 199h4l4 3c2 1 5 2 8 3s6 3 9 3h0c8 3 17 8 25 13l10 7c1 1 4 3 5 3 3 1 4 3 7 5 4 2 9 8 12 12h1 0c1 2 3 3 5 4v1l1-1c3 3 5 7 7 10 1 0 1-1 2-2 0-1 0-1 1-1v-1l6 9c2 2 3 4 4 6l3 4c3 4 4 10 9 12 2 2 3 5 6 6l2 1c1 1 1 1 2 1h1 3l1 1h4c1-1 1-1 1-3l1-1c2-2 4-7 7-8v4c1 1 1 2 0 3 0 1 1 1 1 2h0c1-1 2-2 2-3l2-1h1 2 0 0l1-1c2 0 5 0 7 1 3 2 5 11 6 15l3 10c1 3 10 30 12 32 4 13 10 26 15 38l18 39c1 2 1 6 3 8-3 2-4 3-8 3-1 0-1 1-2 0h-1c-1 0-1-1-2-1l-2 2 3 1h1 1 1 1 1c2 0 5-2 7-2 1 1 2 1 2 3 0 1 1 2 1 4 4 14 13 26 19 40l21 46 5 11c1 2 1 5 2 6l3 4c0 1 1 4 1 5 1 3 3 7 3 10h-2v-1c-1 0-1 0-2-1-1 0-2 1-3 1v-3h-1v-1h-1c-1-2-1-4-2-6l-6-20c-2-5-3-11-5-16l-1 1c0-1 0 0-1-1-1-2-2-3-3-4v3c-5-6-8-11-14-15-4-3-9-8-14-7-3 2-3 5-4 8l-1 5-1-7c0 1 0 2-1 3h-2l-5-16-1 1c-1-2-2-4-4-5-1 0-2 0-3 1-2 2-3 4-5 6-1 4-2 8-2 11v8c0 3 1 8 2 11v3 1h-1l-5-13v1c-1-1-1-3-2-3-1-4-2-7-4-10-1-4-4-9-5-13-2-2-3-5-4-7h0-1l-3-5c-1-1-2-1-2-2h-1c-2 4-7 6-11 7-1 0-2-1-2-2l-1 1-4-6c-10-10-25-3-36 0-6 1-11 2-16 2-7-1-12-1-17-5-3-1-5-3-6-5-4-4-6-10-5-16 0-4 2-8 5-10s8-3 11-3c3 1 6 3 8 6 2 2 2 3 1 6-1 1-2 3-4 3-1 0-2-1-3-2s-2-3-2-4l2-2 1-1c-1-1-1-2-3-2-2-1-4-1-6 0-2 2-4 4-5 6-1 4 0 8 2 12 3 4 7 6 12 7 7 2 15 1 22-3 7-5 11-13 13-21 0-1 1-1 1-2h1c2-6 4-12 5-18s0-12-1-18l-5-30c-1-9-2-18-5-27-2-6-5-11-7-17l-4-7-1-1c0-1-1-1-1-2l-1-1c-2-3-5-5-7-7v-1h-1c-5-5-11-8-17-12 2 0 3-1 4-2-2-5-4-10-7-15-4-5-7-9-13-10-3 0-6 1-9 2 1-1 3-3 5-4 2 0 3-1 5-3v1h1l1-1c1-1 1-2 1-4 1 1 1 1 2 1 1 1 1 1 2 1 2-1 2-1 2-3 1-1 1-2 0-3h-1 0c0-1 0-2-1-4l-1-4c-2-2-5-4-7-6-9-8-20-15-30-20l-4-1c-2 0-3-1-5-1v2c-2-1-2-1-4-1-1 0-4-1-5-2-2 1-5 1-7 1l-8-1c-18-4-43-5-59 5-9 7-13 16-16 27v-4-10c1-10 6-20 14-26 13-11 31-14 47-13 3 0 6 0 10 1h0l-1-2z" class="N"></path><path d="M406 368l2 2-4 4-2-5h2c1 0 1 0 2-1z" class="K"></path><path d="M398 445c2 2 3 4 3 6l-7-4 1-1c1 0 2 0 3-1zm-19-112c2 0 3 0 4 1v4h-1c-2-1-4-1-5-3l2-2z" class="B"></path><path d="M420 468h4c1 1 1 2 1 4l-2 1-2 1c-1-1-2-1-2-2 0-2 0-3 1-4z" class="G"></path><path d="M349 297h3l3 3-2 2-2 1c-1 0-1-1-2-1 0-2-1-3 0-5z" class="Q"></path><path d="M363 308l-2-1h1v-2h-1c-1 0 0 0-1-1 1-1 4 0 5 0s3 1 4 2h1s1 0 2 1v2c-3-1-6-2-9-1z" class="J"></path><path d="M353 304h4v1c0 3-1 5-1 7l-1 1c-2-2-4-5-3-8l1-1z" class="O"></path><path d="M433 491v-1c1-2 2-7 5-8 1-1 2-1 3-1 3 1 4 4 5 7l-1 1c-1-2-2-4-4-5-1 0-2 0-3 1-2 2-3 4-5 6z" class="X"></path><path d="M378 330c-2-1-2-2-3-3 0-1 0-2 1-3 1-2 2-2 4-3v1c1 1 2 1 3 2 0 2 1 3 0 5-2 1-2 1-4 1h-1z" class="L"></path><path d="M380 322c1 1 2 1 3 2 0 2 1 3 0 5-2 1-2 1-4 1h-1l1-1c1-1 2-4 2-4-1-2-1-1-1-3zm-56-65c1 0 2 1 4 3 1 1 2 5 4 4v-2c1 0 1-1 2-2l2 16-5-8c-2-4-5-8-7-11z" class="B"></path><path d="M397 442c-1-3 0-6 0-9h1c1 3 1 5 2 8 1 2 5 10 5 12-1-1-2-1-4-2 0-2-1-4-3-6l-1-3z" class="G"></path><path d="M389 448l-1 1-1-1h-1c-2-1-6-1-8-1-1 1-3 1-4 1h-1-3c-1-1-1-2-1-2 0-2-1-1-2-3 0 1 0 2-1 4l6-27c1 7 0 11-1 17-1 2-3 5-3 7h1c1 1 1 1 1 2v1c7 0 13-3 19 1h0zm-25-122c3 2 9 6 11 9 0 1 1 1 0 2l-1 1-2-2c-1 0-1 0-2 1l-1 2-1-1h-2c-1-3-1-7-3-10h0c1-1 0-1 1-2z" class="I"></path><path d="M371 372v1c0 2 3 5 5 7h-1l-1 1 2 1 4 3v2c-1 0-2-1-2-1-2-2-3-3-5-4l-1-1-1 1c0 3 0 5-1 8-1-5-1-9-2-13v-4l3-1z" class="H"></path><path d="M318 470c1 0 3 0 4 1l1 1v-1l-1-2-1 1-1-2v-1c2 0 4 1 6 2 4 0 7 0 11 1 0 1 1 3 1 4l-3 1c-7-1-12-1-17-5z" class="C"></path><path d="M250 215c6 2 16 4 22 9l-4-1c-2 0-3-1-5-1v2c-2-1-2-1-4-1-1 0-4-1-5-2l-4-2c-1-1-3-1-3-3l3-1z" class="P"></path><defs><linearGradient id="d" x1="399.48" y1="372.448" x2="392.239" y2="384.681" xlink:href="#B"><stop offset="0" stop-color="#211e20"></stop><stop offset="1" stop-color="#414342"></stop></linearGradient></defs><path fill="url(#d)" d="M395 368c3 8 5 15 5 23l-1 1c0 4 0 9-1 13v-5l-1-5c0-3-1-7-1-11l-2-6c0-3 0-5-1-7 1-1 2-2 2-3z"></path><path d="M367 352c-2-3-4-6-5-9s-1-6-2-9-2-6-2-9h1c2 0 3 0 5 1-1 1 0 1-1 2h0c2 3 2 7 3 10v8 4c0 1 1 1 1 2z" class="F"></path><path d="M359 325c2 0 3 0 5 1-1 1 0 1-1 2h0c2 3 2 7 3 10v8c-1-5-2-10-4-14-1-2-3-5-3-7z" class="C"></path><path d="M408 468l-6-12c2 1 5 2 7 4 4 4 6 10 7 15l3 10-1 1-1-1-1-2-5-11c-1-1-1-3-3-4z" class="Q"></path><path d="M454 501c1-4 1-7 4-9 3-1 5 0 7 1 10 5 17 11 23 21v3c-5-6-8-11-14-15-4-3-9-8-14-7-3 2-3 5-4 8l-1 5-1-7z" class="d"></path><path d="M391 335h3 1v1h1c-1 2-2 2-3 3 0 1 0 1-1 3l2 2 7 14 4 6 1 4c-1 1-1 1-2 1h-2c-2-6-5-13-8-18l-9-12 4-4h2z" class="B"></path><path d="M363 308c3-1 6 0 9 1v1l1 1c1 1 1 4 0 6s-4 4-6 4c-2 1-4 0-6-1-1-2-2-3-2-5 0-3 2-5 4-7z" class="O"></path><path d="M335 259v-1l6 9c3 9 7 20 14 26 3 2 6 3 8 4l4 2c3 1 5 2 8 3h3v1c-5 0-12-2-17-4-11-5-17-15-21-26l-5-14z" class="E"></path><path d="M382 316c4 4 9 10 15 10h5 4v-1c1 1 0 1 0 2h0c0 1-1 2-1 3-2 3-6 7-9 8h0c-1 1-2 2-2 4v2l-2-2c1-2 1-2 1-3 1-1 2-1 3-3h-1v-1h-1-3c0-1 2-2 3-2l1-1c-1-1-1-1-2-1h0c-1-1-1-1-2 0h0c0-1 1-2 2-3h0v-1c-1-1-1 0-2-1-2 0-2-2-3-3-2 0-4 0-5 1-1-1-2-1-3-2v-1c2-2 2-2 2-5z" class="J"></path><path d="M406 326v-1c1 1 0 1 0 2h0c0 1-1 2-1 3-2 3-6 7-9 8h0c-1 1-2 2-2 4v2l-2-2c1-2 1-2 1-3 1-1 2-1 3-3h-1v-1h-1 0c2-1 5-2 6-4-2 0-5 0-7-1 3 0 9 0 10-2 1 0 2-2 3-2z" class="E"></path><path d="M370 390c1-3 1-5 1-8l1-1 1 1c2 1 3 2 5 4 0 0 1 1 2 1l2 1h-1c-2-1-4-1-6-3v1 1l1 1-1 1v3c1 3 1 7 1 10 0 10 0 20-2 29h0l-1 1v1c0 1-1 3-2 4h0c1-6 2-10 1-17 1-3 0-6 0-9l-1-18-1-3z" class="V"></path><path d="M370 390c1-3 1-5 1-8l1-1 1 1-1 2c1 1 1 2 1 3v1c-2 2-2 3-2 5l-1-3z" class="b"></path><defs><linearGradient id="e" x1="411.053" y1="488.836" x2="430.747" y2="504.908" xlink:href="#B"><stop offset="0" stop-color="#7b797c"></stop><stop offset="1" stop-color="#979897"></stop></linearGradient></defs><path fill="url(#e)" d="M393 449c3 2 5 4 7 8l8 12v-1c2 1 2 3 3 4l5 11 1 2 1 1 1-1c0 1 0 1 1 2v-1l13 38v1h-1l-5-13v1c-1-1-1-3-2-3-1-4-2-7-4-10-1-4-4-9-5-13-2-2-3-5-4-7-4-7-7-13-11-19-3-4-6-8-9-11l1-1z"></path><path d="M341 267c2 2 3 4 4 6l3 4c3 4 4 10 9 12 2 2 3 5 6 6l2 1c1 1 1 1 2 1h1 3l1 1h4c1-1 1-1 1-3l1-1c2-2 4-7 7-8v4c1 1 1 2 0 3-1 3-3 8-6 10h-1v-1h-3c-3-1-5-2-8-3l-4-2c-2-1-5-2-8-4-7-6-11-17-14-26z" class="V"></path><path d="M367 299h9v1c1-1 2-1 3-2h0l1-1c0-1 1-3 2-4l2-2c1-1 0-1 1-1 1 1 1 2 0 3-1 3-3 8-6 10h-1v-1h-3c-3-1-5-2-8-3z" class="W"></path><path d="M394 364l1 4c0 1-1 2-2 3 1 2 1 4 1 7l2 6-2-2c-1-2 0-4-2-4-1-1-3 0-4 0l-1 1c0 1-1 1-2 2l-1 3 2 4-6-3-4-3-2-1 1-1h1c-2-2-5-5-5-7h2c3 2 5 2 8 1 6-2 10-5 13-10z" class="Q"></path><path d="M376 382l1-1h1c0-2-3-3-3-4l1-1c3 3 3 3 3 7h2v-3c1 1 2 1 3 3v1l2 4-6-3-4-3z" class="Z"></path><path d="M393 371c1 2 1 4 1 7l2 6-2-2c-1-2 0-4-2-4-1-1-3 0-4 0l-1 1c0 1-1 1-2 2l-1 3v-1c-1-2-2-2-3-3s-1-1-1-3c5-1 9-2 13-6z" class="M"></path><path d="M366 338h2l1 1 1-2c1 2 1 2 2 3-2 1-4 2-4 4-1 3 0 7 1 10 1 4 3 9 4 13l3 2 1-1v-2h1c1 1 3 1 4 1l1-1 2-1c2-1 3-2 4-5l2 4 2-3c0 1 1 2 1 3-3 5-7 8-13 10-3 1-5 1-8-1h-2v-1c0-1 1-1 1-2-1-1-1-3-1-5-1-1-1-2-1-4-1 0-1-1-1-2l-1-1-1-5c-1 0 0 0 0-1s-1-1-1-2v-4-8z" class="Y"></path><path d="M389 360l2 4c-3 3-8 6-13 6-1 1-3 0-4-1l-1-2 3 2 1-1v-2h1c1 1 3 1 4 1l1-1 2-1c2-1 3-2 4-5z" class="B"></path><path d="M420 486c0-2-1-6 0-8 1-1 2-1 3-1 2 0 2 0 4 1 3 3 1 15 2 20 0 4 1 8 2 12 0 3 1 8 2 11v3l-13-38z" class="O"></path><defs><linearGradient id="f" x1="397.915" y1="396.844" x2="386.585" y2="403.656" xlink:href="#B"><stop offset="0" stop-color="#868485"></stop><stop offset="1" stop-color="#b0afb0"></stop></linearGradient></defs><path fill="url(#f)" d="M384 384l1-3c1-1 2-1 2-2l1-1c1 0 3-1 4 0 2 0 1 2 2 4l2 2c0 4 1 8 1 11-2 3-1 7-2 10v1h-1v1c-1 3-2 5-3 7v-1c0-1 0-2-1-4h0v-1c1-2 0-2 0-4 1-2 1-3 1-4l-1-1v2h-1c1-3 1-5 1-8-1-3-2-4-4-5l-2-4z"></path><path d="M389 383c1 0 1 0 2 1 1 3 0 6 0 10v-2c-1-2-2-3-4-5v-1l2 1h0v-4z" class="D"></path><path d="M372 340c4 0 7 0 9 3 6 5 9 11 12 18l-2 3-2-4c-1 3-2 4-4 5l-2 1-1 1c-1 0-3 0-4-1h-1v2l-1 1-3-2c-1-4-3-9-4-13-1-3-2-7-1-10 0-2 2-3 4-4z" class="G"></path><path d="M374 358c-2-4-3-8-3-12 1-1 0-1 2-1 2-1 4-1 6 1-2 0-4 1-5 2l-1 1v5c1 1 1 3 1 4z" class="E"></path><path d="M379 346l2 2c3 3 5 6 7 9l1 3c-1 3-2 4-4 5l-2 1-1 1c-1 0-3 0-4-1h-1c-1-3-2-5-3-8 0-1 0-3-1-4v-5l1-1c1-1 3-2 5-2z" class="K"></path><path d="M381 348c3 3 5 6 7 9 0 2 0 3-1 5-2 1-3 3-5 4-1 0-2-1-2-2h1c2 0 4-1 5-3 0-4-3-7-6-10-2 0-2 0-4 1h0c-1-1-1-1-1-3 2-1 4-1 6-1z" class="F"></path><path d="M376 352c2-1 2-1 4-1 3 3 6 6 6 10-1 2-3 3-5 3h-1 0c-1-3-3-5-3-8 0-1-1-3-1-4z" class="R"></path><path d="M382 357c2 1 2 2 2 4l-1 1h-3l1-1c1-1 1-3 1-4z" class="d"></path><path d="M381 361l-3-7h3c1 1 1 2 1 3s0 3-1 4z" class="a"></path><defs><linearGradient id="g" x1="353.631" y1="452.494" x2="394.162" y2="486.369" xlink:href="#B"><stop offset="0" stop-color="#4e4d4d"></stop><stop offset="1" stop-color="#939393"></stop></linearGradient></defs><path fill="url(#g)" d="M337 470h1c7-1 14-3 20-6 8-4 26-18 34-11 5 4 8 9 12 15 1 2 3 4 4 7-1-1-2-1-2-2h-1c-2 4-7 6-11 7-1 0-2-1-2-2l-1 1-4-6c-10-10-25-3-36 0-6 1-11 2-16 2l3-1c0-1-1-3-1-4z"></path><defs><linearGradient id="h" x1="295.117" y1="291.459" x2="357.031" y2="299.777" xlink:href="#B"><stop offset="0" stop-color="#aba9aa"></stop><stop offset="1" stop-color="#fefefd"></stop></linearGradient></defs><path fill="url(#h)" d="M309 250c2 2 4 5 7 7 10 12 18 26 25 41 0-3-8-16-10-20 1 1 2 1 2 2 1 1 3 2 3 4h1c1 2 2 5 3 7l3 6c1 2 1 3 2 5v1c1 1 0 2 1 2v2c0-1 0-2 1-3 1 2-1 4 1 6v1c0 2-1 0 0 2 0 1 0 2 1 3 0 2-1 4 0 6 1 1 1 2 1 3 0 2 1 2 1 3l1 2 1 4v-1c-1-1-2-3-3-4l-4-7-1-1c0-1-1-1-1-2l-1-1c-2-3-5-5-7-7v-1h-1c-5-5-11-8-17-12 2 0 3-1 4-2-2-5-4-10-7-15-4-5-7-9-13-10-3 0-6 1-9 2 1-1 3-3 5-4 2 0 3-1 5-3v1h1l1-1c1-1 1-2 1-4 1 1 1 1 2 1 1 1 1 1 2 1 2-1 2-1 2-3 1-1 1-2 0-3h-1 0c0-1 0-2-1-4l-1-4z"></path><path d="M322 296c1 1 2 1 3 2 1 2 3 3 5 5 2 1 5 4 6 6v1h-1c-5-5-11-8-17-12 2 0 3-1 4-2z" class="N"></path><path d="M309 250c2 2 4 5 7 7 0 2 1 4 1 6 0 3-1 5-2 7l-13-1c-2 0-3 1-4 1 2 0 3 0 4 1-3 0-6 1-9 2 1-1 3-3 5-4 2 0 3-1 5-3v1h1l1-1c1-1 1-2 1-4 1 1 1 1 2 1 1 1 1 1 2 1 2-1 2-1 2-3 1-1 1-2 0-3h-1 0c0-1 0-2-1-4l-1-4z" class="P"></path><defs><linearGradient id="i" x1="373.141" y1="417.2" x2="391.456" y2="418.706" xlink:href="#B"><stop offset="0" stop-color="#050504"></stop><stop offset="1" stop-color="#2f2d2e"></stop></linearGradient></defs><path fill="url(#i)" d="M380 385l6 3c2 1 3 2 4 5 0 3 0 5-1 8h1v-2l1 1c0 1 0 2-1 4 0 2 1 2 0 4v1h0c1 2 1 3 1 4v1c1-2 2-4 3-7v-1h1v-1c1-3 0-7 2-10l1 5v5c1-4 1-9 1-13l1-1c1 8-1 16-2 25v17h-1c0 3-1 6 0 9l1 3c-1 1-2 1-3 1l-1 1h0l-1 1v1l-1 1-3-2h0c-6-4-12-1-19-1v-1c0-1 0-1-1-2h-1c0-2 2-5 3-7h0c1-1 2-3 2-4v-1l1-1h0c2-9 2-19 2-29 0-3 0-7-1-10v-3l1-1-1-1v-1-1c2 2 4 2 6 3h1l-2-1v-2z"></path><path d="M397 442l1 3c-1 1-2 1-3 1l-1 1h0c-2-2-4-3-6-4h3c2 1 3 1 5 1 1 0 1-1 1-2z" class="E"></path><path d="M371 437h0c1-1 2-3 2-4v-1l1-1-4 15c0-1 0-1-1-2h-1c0-2 2-5 3-7z" class="H"></path><path d="M380 385l6 3c2 1 3 2 4 5l-3-1c-4-2-7-3-11-4l-1-1v-1-1c2 2 4 2 6 3h1l-2-1v-2z" class="J"></path><path d="M393 449c-5-4-10-6-16-8l-1-1 1-1c4 1 8 3 11 4 2 1 4 2 6 4l-1 1v1z" class="I"></path><path d="M383 431v5h-1c0-4 0-7 1-10 0-11 2-22 1-33-1-1-1 0 0-1l1 1 1 15v-5c0-4 0-7 1-11l3 1c0 3 0 5-1 8h1v-2l1 1c0 1 0 2-1 4 0 2 1 2 0 4v1h0c1 2 1 3 1 4v1l-1 9c0 2-1 4-1 6s1 3 0 4h-1c-1-1-2-2-3-2v-5-3h0c-1 1-2 6-2 8z" class="D"></path><path d="M390 409c1 2 1 3 1 4v1l-1 9c0 2-1 4-1 6v1l-1-1c-1 1-1 0-1 1-1-1-1-2-1-4 1 1 0 1 1 1 0-5-1-13 3-18z" class="P"></path><path d="M397 395l1 5v5l-2 21c0 3-1 7-2 10 0 1-1 2-2 3s-2 1-3 1l-5-2-1-7c0-2 1-7 2-8h0v3 5c1 0 2 1 3 2h1c1-1 0-2 0-4s1-4 1-6l1-9c1-2 2-4 3-7v-1h1v-1c1-3 0-7 2-10z" class="T"></path><path d="M395 405c1 3 0 6 0 9s0 6-1 10c0 2-1 5-2 7v1c-1-1-1-2-2-3h0-1c0-2 1-4 1-6l1-9c1-2 2-4 3-7v-1h1v-1z" class="S"></path><path d="M493 518c-5-13-14-26-26-33-7-4-13-7-20-11-13-7-26-18-34-31-8-12-9-23-6-37 2 2 4 4 6 5h1l-2 2-1 5v1h-1c0 1 0 1 1 2v2l-2 2c0 1-1 6 0 7s1 1 1 2 1 2 2 3c6 10 15 20 25 27 2 1 12 6 12 7l5 1h6l1-1c0-4-1-7-2-10 0-3 0-6-2-8-1-2-5-2-8-3h0v-1l4-1 1 1h1c1 0 1 0 3-1 0 1 1 2 1 3h1v-4c-1-1-1-2-1-3l2-1c4 14 13 26 19 40l21 46 5 11c1 2 1 5 2 6l3 4c0 1 1 4 1 5 1 3 3 7 3 10h-2v-1c-1 0-1 0-2-1-1 0-2 1-3 1v-3h-1v-1h-1c-1-2-1-4-2-6l-6-20c-2-5-3-11-5-16z" class="G"></path><path d="M460 452c2 1 3 1 4 3 0 1 0 2 1 3 0 1 1 4 0 6h1v3c-3-4-4-9-6-13 1 0 0-1 0-2z" class="B"></path><path d="M465 471c0-1 0-3-1-4-1-3-4-9-4-13 2 4 3 9 6 13h0l6 15h0c-1 0-2-1-3-2-2-2-2-6-4-9z" class="N"></path><path d="M498 534c1 2 2 3 2 5l1 2v-1l1-1c1 2 2 5 2 7l4 8h0c1 0 1 0 2 1h0l-2-9 3 4c0 1 1 4 1 5 1 3 3 7 3 10h-2v-1c-1 0-1 0-2-1-1 0-2 1-3 1v-3h-1v-1h-1c-1-2-1-4-2-6l-6-20z" class="E"></path><path d="M507 561c1-5-3-10-3-15l4 8h0c1 0 1 0 2 1h0c1 1 1 2 1 4 0 0-1 1-1 2v1l-1 1c0-1-1-2-1-2h-1z" class="F"></path><path d="M508 546l3 4c0 1 1 4 1 5 1 3 3 7 3 10h-2v-1c-1 0-1 0-2-1-1 0-2 1-3 1v-3s1 1 1 2l1-1v-1c0-1 1-2 1-2 0-2 0-3-1-4l-2-9z" class="X"></path><path d="M449 449l4-1 1 1h1c1 0 1 0 3-1 0 1 1 2 1 3h1v1c0 1 1 2 0 2 0 4 3 10 4 13 1 1 1 3 1 4h0c-1-2-2-5-3-7 0 2 1 3 1 5l1 2s1 1 1 2c0 2 0 5-1 6-1 0-2-1-3-2-2-1-4-2-5-2-1-1-2-1-3-1v-1l-4-2 5 1h6l1-1c0-4-1-7-2-10 0-3 0-6-2-8-1-2-5-2-8-3h0v-1z" class="E"></path><path d="M455 449c1 0 1 0 3-1 0 1 1 2 1 3v2l-1-2h-1c-2 0-2-1-3-2h1z" class="G"></path><defs><linearGradient id="j" x1="430.633" y1="434.737" x2="436.867" y2="472.263" xlink:href="#B"><stop offset="0" stop-color="#2a2829"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#j)" d="M411 421l6 5h1c1 1 2 1 2 2 2 1 3 1 4 1l8 3c3 0 7 0 10-1l4-2v1c-1 2-2 2-3 4h-1 1 1c1-1 2-1 3-1l2-1c0 2-1 2-1 4h2c-1 0-1 1-2 0h-1c-1 0-1-1-2-1l-2 2 3 1h1 1 1 1 1c2 0 5-2 7-2 1 1 2 1 2 3 0 1 1 2 1 4l-2 1c0 1 0 2 1 3v4h-1c0-1-1-2-1-3-2 1-2 1-3 1h-1l-1-1-4 1v1h0c3 1 7 1 8 3 2 2 2 5 2 8 1 3 2 6 2 10l-1 1h-6l-5-1c0-1-10-6-12-7-10-7-19-17-25-27-1-1-2-2-2-3s0-1-1-2 0-6 0-7l2-2v-2z"></path><path d="M449 450h0c2 2 3 1 5 2 2 0 3 3 3 5v4c0-2-1-6-3-7 0-1-1-1-2-1-2-1-4 0-6 0h-12l-1 2-1-1s0-1 1-1h4c2 0 7 0 9-2 1 0 1 0 2-1h1z" class="M"></path><path d="M411 421l6 5h1c1 1 2 1 2 2 2 1 3 1 4 1l8 3v1h-1-4l-6-2-1-1c-1 0-2-1-3 0v1l-1 1v-1l-1 1 1 2h0c1 1 1 2 1 3l2 2c1 0 2 1 2 2h1l2 3c-2 0-3 0-4-2l-1-2c-3-3-4-6-6-10l1-1v-1c-1-2-2-3-3-5v-2z" class="d"></path><path d="M433 455l1-2h12c2 0 4-1 6 0 1 0 2 0 2 1 2 1 3 5 3 7s1 6 0 8h-1c-1 0-2 0-3-1h0c0-1 1-2 1-3 0-3 0-7-2-10-4-1-9 0-14 1h0c3 4 8 7 12 10-6-2-9-4-13-8l-4-3z" class="R"></path><path d="M450 466c-4-3-9-6-12-10h0c5-1 10-2 14-1 2 3 2 7 2 10 0 1-1 2-1 3l-3-2z" class="V"></path><path d="M417 431v-1c1-1 2 0 3 0l1 1 6 2h4 1v-1c3 0 7 0 10-1l4-2v1c-1 2-2 2-3 4h-1 1 1c1-1 2-1 3-1l2-1c0 2-1 2-1 4h2c-1 0-1 1-2 0h-1c-1 0-1-1-2-1l-2 2 3 1h1 1 1 1 1c2 0 5-2 7-2 1 1 2 1 2 3 0 1 1 2 1 4l-2 1c0 1 0 2 1 3v4h-1c0-1-1-2-1-3-2 1-2 1-3 1h-1l-1-1-4 1c-3 1-6 1-9 1-7 0-11-1-16-6l-2-3h-1c0-1-1-2-2-2l-2-2c0-1 0-2-1-3h0l-1-2 1-1v1l1-1z" class="W"></path><path d="M417 431v-1c1-1 2 0 3 0l1 1-1 1c4 4 7 7 9 11h-1l-1-1c-2-1-7-6-7-8l-3-3z" class="T"></path><path d="M450 438h1c2 0 5-2 7-2 1 1 2 1 2 3 0 1 1 2 1 4l-2 1c0 1 0 2 1 3v4h-1c0-1-1-2-1-3-2 1-2 1-3 1h-1l-1-1-4 1c-3 1-6 1-9 1-7 0-11-1-16-6l-2-3c2 1 3 2 4 4 3 2 7 3 10 3 5 1 11 0 15-3h0c0-3-2-4-4-6 1 0 1-1 2-1h1z" class="J"></path><path d="M450 438h1c2 0 5-2 7-2 1 1 2 1 2 3 0 1 1 2 1 4l-2 1c0 1 0 2 1 3v4h-1c0-1-1-2-1-3-2 1-2 1-3 1 2-3 2-6 3-9v-1c-2 2-2 5-4 8h-1l-3-9z" class="B"></path><path d="M432 432c3 0 7 0 10-1l4-2v1c-1 2-2 2-3 4h-1 1 1c1-1 2-1 3-1l2-1c0 2-1 2-1 4h2c-1 0-1 1-2 0h-1c-1 0-1-1-2-1l-2 2 3 1h1 1 1c-1 0-1 1-2 1h-1v1l-1-1h-1v1c0 1 0 1 1 2l1 1c-5 3-12 1-17 0-2-4-5-7-9-11l1-1 6 2h4 1v-1z" class="U"></path><path d="M441 441c-1 1-1 1-2 1-3-1-5-2-7-4v-1h2c1 1 3 1 4 1v2c1 0 2 1 3 1z" class="D"></path><path d="M434 437c2-1 7 0 9 0l3 1h1 1 1c-1 0-1 1-2 1h-1v1l-1-1h-1v1c0 1 0 1 1 2-2 0-2 0-4-1-1 0-2-1-3-1v-2c-1 0-3 0-4-1z" class="T"></path><path d="M432 432c3 0 7 0 10-1l4-2v1c-1 2-2 2-3 4h-1 1 1c1-1 2-1 3-1-1 1-2 2-3 2-6 0-11 0-17-2h4 1v-1z" class="R"></path><defs><linearGradient id="k" x1="191.053" y1="278.08" x2="276.783" y2="203.639" xlink:href="#B"><stop offset="0" stop-color="#b8b7b8"></stop><stop offset="1" stop-color="#e0dfe0"></stop></linearGradient></defs><path fill="url(#k)" d="M234 199h4l4 3c2 1 5 2 8 3s6 3 9 3h0c8 3 17 8 25 13l10 7c1 1 4 3 5 3 3 1 4 3 7 5 4 2 9 8 12 12h1 0c1 2 3 3 5 4v1l1-1c3 3 5 7 7 10v2c-2 1-3-3-4-4-2-2-3-3-4-3 2 3 5 7 7 11 0 1 0 3-1 4-2-3-3-6-5-9-5-8-13-15-20-22-2-1-3-3-5-4l-4-3c-4-3-6-5-10-8-3-2-6-3-10-4-5-3-11-6-17-8-5-1-10-2-14-4l-2-1h-10c-4-1-23-3-26-1h10c-2 2-6 0-8 1l22 1c2 1 5 1 7 1h3c2 1 4 2 5 3l4 1-3 1c0 2 2 2 3 3l4 2c-2 1-5 1-7 1l-8-1c-18-4-43-5-59 5-9 7-13 16-16 27v-4-10c1-10 6-20 14-26 13-11 31-14 47-13 3 0 6 0 10 1h0l-1-2z"></path><path d="M234 199h4l4 3c-1 0-1 0-2 1h0-2l-3-2-1-2z" class="N"></path><path d="M241 211c2 1 4 2 5 3v2c-1 1-1 2-2 3-2-1-3-1-4-1l-1-1c-1-1 0-1 0-2 0-2 1-3 2-4z" class="F"></path><path d="M299 231c3 1 4 3 7 5 4 2 9 8 12 12h1 0c1 2 3 3 5 4v1l1-1c3 3 5 7 7 10v2c-2 1-3-3-4-4-2-2-3-3-4-3-4-5-7-9-12-14-4-4-8-7-13-12h0zm95 59c2 0 5 0 7 1 3 2 5 11 6 15l3 10c1 3 10 30 12 32 4 13 10 26 15 38l18 39c1 2 1 6 3 8-3 2-4 3-8 3h-2c0-2 1-2 1-4l-2 1c-1 0-2 0-3 1h-1-1 1c1-2 2-2 3-4v-1l-4 2c-3 1-7 1-10 1-3-1-5-2-8-3-1 0-2 0-4-1 0-1-1-1-2-2h-1l-6-5c-1-1-1-1-1-2h1v-1l1-5 2-2 3 2h1l-3-3-2-1c-2-2-4-3-5-6-1-1-1-4-1-6l-1-11c0-3-1-6-1-9l-1-3 4-4-2-2-1-4-4-6-7-14v-2c0-2 1-3 2-4h0c3-1 7-5 9-8 0-1 1-2 1-3h0c0-1 1-1 0-2v1h-4-5c-6 0-11-6-15-10v-1c-1-3-2-9-1-12s4-6 5-8h0c1-1 2-2 2-3l2-1h1 2 0 0l1-1z" class="G"></path><path d="M405 377c2-2 3-4 5-5 0 2 0 2-1 4-2 0-2 0-3 1 1 3 2 4 2 6v1c0 1-1 2-2 2 0-3-1-6-1-9z" class="B"></path><path d="M401 291c3 2 5 11 6 15-3-2-4-5-5-8v-1c-1-2-1-4-1-6z" class="E"></path><path d="M405 315l8 22c-1-1-1-2-2-3s-2-2-3-4c0-2-1-2-2-3h0c0-1 1-1 0-2v1h-4v-1c-3 0-4 0-7-2 2 0 3 1 4 1 2 1 5 0 7 0l-1-4v-5z" class="Z"></path><path d="M408 370c2-2 3-2 6-1 5 1 7 3 12 2h1c-2 1-3 2-5 2h-1c-4 1-8-3-11-1-2 1-3 3-5 5l-1-3 4-4z" class="J"></path><path d="M447 428c1 1 1 1 1 2 2-2 3-3 4-5 0 2 1 3 2 4h1s1 1 1 2l-1 1c-2 1-1 0-3 2l1 1h-1c-1 0-1 1-2 1h-2c0-2 1-2 1-4l-2 1c-1 0-2 0-3 1h-1-1 1c1-2 2-2 3-4v-1l1-1z" class="L"></path><path d="M410 388c-1-3-2-8 1-11 0-1 1-2 1-2l1-2h1c3 1 8 2 10 1h1 1c3 3 6 10 7 15 0-1-1-2-2-3h0l-1-1c0-2-1-3-2-4s-2-3-3-5l-3 1c-3 1-5 1-9 0l-1-1c0 1-1 1-1 2-1 1 0 4-1 6v4z" class="M"></path><path d="M390 291h1 2 0 0l1-1c0 1 0 2 1 3h1 1v1c0 1 1 2 1 3 1 1 2 3 2 5l5 13v5l-1-2-6-15c-2-4-3-7-6-10 0-1-1-1-2-2z" class="X"></path><path d="M406 327c1 1 2 1 2 3 1 2 2 3 3 4s1 2 2 3c3 7 5 14 8 21 1 3 3 6 4 9v2c1 1 1 1 2 1v1h-1c-5 1-7-1-12-2-3-1-4-1-6 1l-2-2-1-4-4-6 5 3c2 2 3 3 5 3 1 0 2 0 3-1l2 1v1l2 1 1-1c1-2 1-2 0-4-4-9-8-19-11-29-1-1-2-1-3-2 0-1 1-2 1-3z" class="F"></path><path d="M401 358l5 3c2 2 3 3 5 3 1 0 2 0 3-1l2 1v1l2 1 1-1c1-2 1-2 0-4 1 2 2 3 2 5 0 1 1 1 1 2v1c-1 1-1 1-2 1l-8-3c-3-1-4 0-7-3l-4-6z" class="R"></path><path d="M405 330c1 1 2 1 3 2 3 10 7 20 11 29 1 2 1 2 0 4l-1 1-2-1v-1l-2-1c-1 1-2 1-3 1-2 0-3-1-5-3l-5-3-7-14v-2c0-2 1-3 2-4h0c3-1 7-5 9-8z" class="P"></path><path d="M402 339h1c1 1 2 3 2 5 2 4 4 8 4 12v1l-7-12c0-1-1-2-1-3 0-2 0-2 1-3z" class="C"></path><path d="M406 361l-6-12c-1-3-2-5-2-7 0-1 0-2 1-3s1-1 2-1c1-1 2-2 4-3-1 3-1 3-3 4-1 1-1 1-1 3 0 1 1 2 1 3l7 12c1 3 2 5 5 6-1 1-2 1-3 1-2 0-3-1-5-3z" class="S"></path><path d="M405 330c1 1 2 1 3 2 3 10 7 20 11 29 1 2 1 2 0 4l-1 1-2-1v-1c1 0 1-1 1-2 0-3-2-5-3-8 0-2-1-4-2-6-2-3-2-8-5-11h0l-1-1-1-1c-2 1-3 2-4 3-1 0-1 0-2 1s-1 2-1 3c0 2 1 4 2 7l6 12-5-3-7-14v-2c0-2 1-3 2-4h0c3-1 7-5 9-8z" class="a"></path><path d="M388 292l2-1c1 1 2 1 2 2 3 3 4 6 6 10l6 15 1 2 1 4c-2 0-5 1-7 0-1 0-2-1-4-1 3 2 4 2 7 2v1h-5c-6 0-11-6-15-10v-1c-1-3-2-9-1-12s4-6 5-8h0c1-1 2-2 2-3z" class="b"></path><path d="M392 303v-2c3 3 7 11 7 15 0 1 0 1-1 2s-2 0-3 0l-4-4-1-1c-1-2-1-4 0-5v-3-1h1l1 1v-2z" class="D"></path><path d="M392 303c1 1 1 2 2 3l2 3-1 1-2-2s-1 0-1-1v1 5l-1 1-1-1c-1-2-1-4 0-5v-3-1h1l1 1v-2z" class="P"></path><path d="M388 292v1 2c0 1 0 1 1 2 1 2 0 6 0 9v8h0c1 2 5 5 7 6s4 2 7 1c1-1 1-2 1-3l1 2 1 4c-2 0-5 1-7 0-1 0-2-1-4-1 0-1 0-1-1-1-3-2-7-6-8-10v-1c-1-3-1-8 0-10v-2-2l1-1-1-1c1-1 2-2 2-3z" class="W"></path><path d="M386 295l1 1-1 1v2 2c-1 2-1 7 0 10v1c1 4 5 8 8 10 1 0 1 0 1 1 3 2 4 2 7 2v1h-5c-6 0-11-6-15-10v-1c-1-3-2-9-1-12s4-6 5-8h0z" class="B"></path><path d="M422 377l3-1c1 2 2 4 3 5s2 2 2 4l1 1h0c1 1 2 2 2 3 3 4 5 9 7 14 3 6 6 13 7 19 0 1 1 2 1 2l2-2 1 1c-1 2-2 4-4 5l-1 1-4 2c-3 1-7 1-10 1-3-1-5-2-8-3-1 0-2 0-4-1 0-1-1-1-2-2h-1l-6-5c-1-1-1-1-1-2h1v-1l1-5 2-2 3 2h1l-3-3v-1h-1c-1-1-1-2-2-4-1-6-2-11-2-17v-4c1-2 0-5 1-6 0-1 1-1 1-2l1 1c4 1 6 1 9 0z" class="D"></path><path d="M418 399l3 9c1 3 3 5 3 8v1l-2 1h-1v-1l-2 2 1 1v-1l2 2c1 1 4 3 4 5-2-1-5-2-6-4-2-1-2-2-2-4 1-2 1-2 3-2-1-2-3-2-4-3h1l3 1h0c0-1-1-1-1-1-2-3-2-10-2-14z" class="P"></path><path d="M410 388v-4c1-2 0-5 1-6 0-1 1-1 1-2l1 1c4 1 6 1 9 0l2 2c0 1 1 2 1 3-1-1-2-2-4-2s-3 1-5 0v2s-1-1-1-2h-1c0 4 1 8 2 12 0 2 1 5 2 7 0 4 0 11 2 14 0 0 1 0 1 1h0l-3-1-3-3v-1h-1c-1-1-1-2-2-4-1-6-2-11-2-17z" class="U"></path><path d="M410 388v-4c1-2 0-5 1-6 0-1 1-1 1-2l1 1v3h-1c-1 4 2 11 2 16v9c0 1 1 3 1 4h-1c-1-1-1-2-2-4-1-6-2-11-2-17z" class="R"></path><path d="M422 377l3-1c1 2 2 4 3 5s2 2 2 4l1 1h0c1 1 2 2 2 3 3 4 5 9 7 14 3 6 6 13 7 19 0 1 1 2 1 2l2-2 1 1c-1 2-2 4-4 5l-1 1-4 2c-3 1-7 1-10 1-3-1-5-2-8-3-1 0-2 0-4-1 0-1-1-1-2-2h-1l-6-5c-1-1-1-1-1-2h1v-1l1-5 2-2 3 2c1 1 3 1 4 3-2 0-2 0-3 2 0 2 0 3 2 4 1 2 4 3 6 4h1c2 0 5 1 7 0 3 0 6 0 8-3 0-3-1-5-2-8-3-7-6-13-9-20l-6-13c0-1-1-2-1-3l-2-2z" class="b"></path><path d="M422 377l3-1c1 2 2 4 3 5h0l-1 1v2l3 8c1 1 1 2 1 3l-6-13c0-1-1-2-1-3l-2-2z" class="a"></path><path d="M428 381c1 1 2 2 2 4l1 1h0c1 1 2 2 2 3 3 4 5 9 7 14 3 6 6 13 7 19 0 1 1 2 1 2-1 3-3 5-7 5-1 0 0 0-1 1-1 0-3 0-4-1h2c3 0 5-2 7-3-2-12-7-21-12-32l-5-13h0z" class="F"></path><path d="M414 411l3 2c1 1 3 1 4 3-2 0-2 0-3 2 0 2 0 3 2 4 1 2 4 3 6 4h1c0 1 1 1 2 2h0c2 0 6 0 7 1s3 1 4 1c1-1 0-1 1-1 4 0 6-2 7-5l2-2 1 1c-1 2-2 4-4 5l-1 1-4 2c-3 1-7 1-10 1-3-1-5-2-8-3-1 0-2 0-4-1 0-1-1-1-2-2h-1l-6-5c-1-1-1-1-1-2h1v-1l1-5 2-2z" class="T"></path><path d="M391 479l1-1c0 1 1 2 2 2 4-1 9-3 11-7h1c0 1 1 1 2 2l3 5h1 0c1 2 2 5 4 7 1 4 4 9 5 13 2 3 3 6 4 10 1 0 1 2 2 3v-1l5 13h1v-1-3c-1-3-2-8-2-11v-8c0-3 1-7 2-11 2-2 3-4 5-6 1-1 2-1 3-1 2 1 3 3 4 5l1-1 5 16h2c1-1 1-2 1-3l1 7 1-5c1-3 1-6 4-8 5-1 10 4 14 7 6 4 9 9 14 15v-3c1 1 2 2 3 4 1 1 1 0 1 1l1-1c2 5 3 11 5 16l6 20c1 2 1 4 2 6h1v1h1v3c1 0 2-1 3-1 1 1 1 1 2 1v1h2c2 1 3 3 3 5v1l-2 2v1h0c-1 1-3 2-3 3s0 0-1 1c-3 2-6 5-10 7-1 1-5 2-5 3h0c-1 1-2 1-3 2h3 0 3c-3 1-6 2-8 3-4 0-7 0-10-1-2 0-3 0-4-1l-1 1c2 3 3 7 5 9s3 3 5 3h1c1 1 1 1 2 0 2 0 3-1 5 0h0c1 1 1 1 2 1h0l-1 1c-1 0-2 0-3 1s-3 1-4 2c-2 2-3 4-4 7 3 3 6 6 10 7 1 1 4 1 4 2h3c1 0 2 1 3 1h1v-1c2 0 3-1 4-2l2 1h1 1l2 2 3-2h2v1 1c2 2 4 3 6 4-1 1-3 1-4 1h-2 0c-1 1-2 1-4 2l-2 1h-1-9c0 1 1 2 2 3 2 3 5 6 7 9l5 9 8 8c3 2 6 6 7 10 1 3 1 5 2 7l1 10c1 1 1 1 0 2v5l1 2c2 2 2 2 4 2v2h0c-1-1-1 0-2-1l1 3v1h1c1 1 3 3 3 5h0l-2-1h-1l-1 1c2 4 1 8 1 13l-1 4h-3c0 1-1 2-1 3 2 0 2 0 3 2l1 2c1 1 2 1 3 1v-1c1 0 1 1 2 1 0-1 1-1 2-1v-2l1 1 1 1c1 0 1-1 2-1 0 0 0 1 1 1l-12 33c-2 3-3 7-5 11 0 2-3 13-4 13l-1-2v-2h0l-2-3h0c-1-1-1-2-2-3-1 1 0 3-1 4l2 6-1 2c1 3 2 6 2 9v4c1 1 1 2 1 3l1 3c1 6 1 12 1 19s1 16 0 23l-1 10v4l-6 10c-4-3-7-8-9-13l-1 1c-1-3-1-7-1-10 0-9 1-20-1-30l-15-54c-2-9-4-18-8-27l-2-3c-8-8-18-8-28-8 4-1 9-2 14-4 3-1 6-3 7-7-1-8-4-16-6-24l-20-55c-2-5-4-12-7-17-1-2-2-3-4-4v-1c0-2-2-2-3-3h0-2c1-1 4-3 5-4l-1-1-16 13c-9 5-16 9-26 10l-1 2h0c-2 2-5 6-6 9h0-1c0-3 2-6 3-9-3-1-6-2-9-4-3-1-6-3-9-4-9-3-19 0-26-8-3-2-5-6-5-10v1c2 4 4 7 7 10 7 6 16 4 24 3-2-4-3-9-2-13 0-4 2-8 5-10 2-1 5-2 7-1 2 0 4 2 5 4s2 3 1 5l-3 3c-2 0-2 0-4-1 0-2 1-3 1-4l-2-2c-1 0-2 0-3 1-2 1-3 4-3 6 0 3 1 6 3 8 4 4 9 6 14 6 8 1 15-3 21-8l2-3c9-10 9-21 8-33 0-4-1-8-4-11-4-6-10-7-17-8 5-1 11 2 15 0 1 0 1-1 2-2 0-1 0-3-1-5-1-5-3-10-5-15-1-5-3-12-6-17-2-7-5-14-9-20-5-3-10-4-15-3l5-3c2 0 5-2 5-4 2-5-4-15-7-19z" class="J"></path><path d="M437 546h1v1c1 1 1 4 3 5 1 4 1 9 2 13 0 2 0 4-1 6l-5-25z" class="F"></path><path d="M490 671c1 3 1 5 1 7l1 1c-1 3 1 8 2 11l1 3c-1 1 0 3-1 4-1-5-4-10-5-14l-4-10h3l2-2z" class="D"></path><path d="M513 827h1c1-2 0-6 1-8 0-2-1-10 1-11v5c-1 5-1 11-1 15l1 2v36l-1 1c-1-3-1-7-1-10 0-9 1-20-1-30zm-85-241c3 4 2 12 1 16v1c-1 5-3 11-6 15l1-1 1-1c0-1 1-2 2-3 0 0 1 0 1 1l1 1v1c1 0 1 0 2-1v-1c1-2 3-3 3-5s1-3 1-4c1-1 0-1 1-1v-1c0-2 0 0 1-2v-1c0 1 0 3-1 4-2 9-6 13-13 19-2 2-4 4-7 6s-8 4-11 5h-4c2-1 4-1 7-2h0c2-1 3-2 5-3s6-4 7-7v-1l-2 1 2-3c9-10 9-21 8-33z" class="C"></path><path d="M513 757l1 4c-1 0-1 1-1 2 1 3 1 9 0 12 0 2 0 3-1 4l-3-19 1-1 1 5v2c0 1 0 2 1 3v6h1c-1-4-1-10 0-14h0c0-2 0-3-1-5 0-1 0-3-1-4l-2-7c0-1-1-1-1-2v-1l-3-8c0-2-1-3-1-4l-3-6c-1-2-3-5-3-7-1-1-1-2-2-3 0-1-1-2-1-3l-1-2v-3l-1-1v-1c0-2 0-3-1-5 0-2-2-4-3-7l-3-6-1-4-3-9c-1-1-1-2-1-2h1c3 10 7 20 12 30l6 15c0 2 1 5 2 7 1 4 3 8 5 12l3 12c1 3 3 6 3 10z" class="N"></path><path d="M454 596c2 3 4 7 5 10 4 6 7 13 10 19l-1 1c-1 1 0 2 1 4l4 9v5l-20-43v-3l1-2z" class="U"></path><path d="M469 625l15 30c1 3 3 5 5 7v1l1 2c1 1 1 1 0 2v2 2l-2 2h-3l-2-6v-1c-1-1-2-4-3-6l-7-16v-5l-4-9c-1-2-2-3-1-4l1-1z" class="H"></path><path d="M483 667c1-2 1-3 2-4h1l4 4v2 2l-2 2h-3l-2-6z" class="O"></path><path d="M473 644v-5l-4-9c-1-2-2-3-1-4l17 35c-1 1-1 1-1 2-1 1-1 2-1 3-1-1-2-4-3-6l-7-16z" class="V"></path><path d="M494 697c1-1 0-3 1-4l10 27 3 9c1 1 1 2 3 3h2 4 3v1c-2 2-4 3-6 5-2 6 0 12 1 17l-2 2c0-4-2-7-3-10l-3-12c-2-4-4-8-5-12-1-2-2-5-2-7l-6-15v-2c-1-1-1-1 0-2z" class="C"></path><path d="M508 729c1 1 1 2 3 3h2 4v1l-2 2h-6c0-1-1-1-1-2-1-2-1-3 0-4z" class="I"></path><defs><linearGradient id="l" x1="533.24" y1="735.995" x2="503.615" y2="717.954" xlink:href="#B"><stop offset="0" stop-color="#050405"></stop><stop offset="1" stop-color="#313031"></stop></linearGradient></defs><path fill="url(#l)" d="M494 690c1 1 2 1 2 2 1 5 4 10 6 14 1 2 3 3 4 6l-1 1s1 1 1 2l1 2 2 2 1 1c1 2 3 3 3 6 1 1 1 1 3 1 4 2 10 3 15 2l2-1h5c0 1-1 2-1 3l-8 2c-2 0-4 0-5 1-1 0-2 1-2 2-1 1-2 2-2 4 0-1 0-2-1-3h-1l-1 2c-1 0-2 0-3-1 2-2 4-3 6-5v-1h-3-4-2c-2-1-2-2-3-3l-3-9-10-27-1-3z"></path><path d="M505 720c1 2 1 2 3 2 1 3 2 5 3 7 0 2 1 2 2 3h-2c-2-1-2-2-3-3l-3-9z" class="K"></path><defs><linearGradient id="m" x1="399.787" y1="474.551" x2="406.364" y2="492.869" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#m)" d="M391 479l1-1c0 1 1 2 2 2 4-1 9-3 11-7h1c0 1 1 1 2 2l3 5h1 0c1 2 2 5 4 7 1 4 4 9 5 13h0l-2-2v-1l-4-9c-1 0-1 0-2 1-2 3-6 5-10 6-2 0-3 0-4 1v4l-5 3c1 1 2 1 4 2l1-1 1 1c2 1 2 1 3 3-5-3-10-4-15-3l5-3c2 0 5-2 5-4 2-5-4-15-7-19z"></path><defs><linearGradient id="n" x1="509.304" y1="814.987" x2="533.12" y2="785.014" xlink:href="#B"><stop offset="0" stop-color="#676466"></stop><stop offset="1" stop-color="#b4b4b2"></stop></linearGradient></defs><path fill="url(#n)" d="M517 765c1 1 1 1 1 2 1 1 2 6 3 8l6 19c1 3 2 6 2 9v4c1 1 1 2 1 3v5 4c2 3 0 7 1 9 0 1 1 3 0 4-3-3 1-9-5-10h-4c-1 0-4 0-6 1l1-58z"></path><defs><linearGradient id="o" x1="525.077" y1="875.956" x2="520.898" y2="821.738" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#o)" d="M530 810l1 3c1 6 1 12 1 19s1 16 0 23l-1 10v4l-6 10c-4-3-7-8-9-13v-36-7c2-1 5-1 6-1h4c6 1 2 7 5 10 1-1 0-3 0-4-1-2 1-6-1-9v-4-5z"></path><defs><linearGradient id="p" x1="520.758" y1="769.216" x2="551.224" y2="750.794" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#282727"></stop></linearGradient></defs><path fill="url(#p)" d="M537 731c2 0 2 0 3 2l1 2c1 1 2 1 3 1v-1c1 0 1 1 2 1 0-1 1-1 2-1v-2l1 1 1 1c1 0 1-1 2-1 0 0 0 1 1 1l-12 33c-2 3-3 7-5 11 0 2-3 13-4 13l-1-2v-2h0l-2-3h0c-1-1-1-2-2-3-1 1 0 3-1 4l2 6-1 2-6-19c-1-2-2-7-3-8 0-1 0-1-1-2 0-2-1-6-2-9v-1c-1-5-3-11-1-17 1 1 2 1 3 1l1-2h1c1 1 1 2 1 3 0-2 1-3 2-4 0-1 1-2 2-2 1-1 3-1 5-1l8-2z"></path><path d="M525 760h4 0v3h-1c0 1-1 1-1 2l-1 1-1-6z" class="M"></path><path d="M539 748c2-1 1-1 3-3v1c0 1-1 1-1 2-1 1-1 1-1 2 0 2-2 4-2 6h0l-1-1v1h-1l3-8z" class="Q"></path><path d="M529 763l1 1c1-1 1 0 1-1v-1c1 0 2 1 2 1-1 3-2 7-4 10-1-3-2-5-3-7l1-1c0-1 1-1 1-2h1z" class="E"></path><path d="M536 768c1 0 2 0 2-1 0 2-1 5-2 7v5c0 2-3 13-4 13l-1-2v-2c1-7 2-14 5-20z" class="b"></path><path d="M534 753c2-2 3-4 4-6l1 1-3 8c-1 2-2 5-3 7 0 0-1-1-2-1v1c0 1 0 0-1 1l-1-1v-3l1-1v-3l1-2c1-1 1-1 1-2v-3l3-1-1 5z" class="F"></path><path d="M532 752v-3l3-1-1 5c0 1-2 5-3 6h-1v-3l1-2c1-1 1-1 1-2z" class="W"></path><path d="M548 733l1 1 1 1c1 0 1-1 2-1 0 0 0 1 1 1l-12 33c-2 3-3 7-5 11v-5c1-2 2-5 2-7 0 1-1 1-2 1l10-26c-1-2-1-3-1-5l-1-1v-1c1 0 1 1 2 1 0-1 1-1 2-1v-2z" class="C"></path><path d="M548 733l1 1-3 8c-1-2-1-3-1-5l-1-1v-1c1 0 1 1 2 1 0-1 1-1 2-1v-2z" class="B"></path><path d="M515 755c-1-5-3-11-1-17 1 1 2 1 3 1v3c1 1 1 3 1 4v1c1 2 1 3 1 4v1l2 5v2l3 7c0 1 1 3 0 3 1 2 2 4 2 6h0c1 2 2 3 2 5l1 5h0c-1-1-1-2-2-3-1 1 0 3-1 4l2 6-1 2-6-19c-1-2-2-7-3-8 0-1 0-1-1-2 0-2-1-6-2-9v-1z" class="K"></path><path d="M523 775l-1-2c-1-3-1-6 0-8h0l2 4h0c1 2 2 4 2 6h0 0c-1-1-1-1-1-2h0l-1-1-1 3z" class="E"></path><path d="M523 775l1-3 1 1h0c0 1 0 1 1 2h0c1 2 2 3 2 5l1 5h0c-1-1-1-2-2-3-1 1 0 3-1 4l-3-11z" class="B"></path><path d="M515 755c-1-5-3-11-1-17 1 1 2 1 3 1v3c1 1 1 3 1 4v1c1 2 1 3 1 4v1l-1 1 1 1v1l1 5c1 1 1 1 1 2-2-1-2-2-2-3h-1c-1-1-1-1-1-2l-2-1v-1z" class="E"></path><path d="M537 731c2 0 2 0 3 2l1 2c1 3 0 6-1 8l-1 2-1 2c-1 2-2 4-4 6l1-5-3 1v3c0 1 0 1-1 2l-1 2v3l-1 1h0-4l-2-5-1-2c0-1 0-2-1-2v-2l-1 1c0-1-1-2-2-3v-1c0-1 0-3-1-4v-3l1-2h1c1 1 1 2 1 3 0-2 1-3 2-4 0-1 1-2 2-2 1-1 3-1 5-1l8-2z" class="X"></path><path d="M529 737l2 1-2 1c0 1 0 3-1 4v3h2c0 2 0 4 2 6 0 1 0 1-1 2l-1 2v3l-1 1h0v-3c-1-3-1-6-2-8v-5c-1-1-1-1 0-2-1-1-1-2-1-3l3-2z" class="V"></path><path d="M529 737c1 0 2 0 2-1 3-1 4 0 7 0h1c1 3 0 6 0 9l-1 2c-1 2-2 4-4 6l1-5-3 1v3c-2-2-2-4-2-6h-2v-3c1-1 1-3 1-4l2-1-2-1z" class="U"></path><path d="M531 745c0-2 0-4 2-5h1v3c-1 2-1 2-2 3h-1v-1z" class="S"></path><path d="M531 738c1-1 3-2 5-1 1 0 1 1 2 2 0 1-1 2-2 3v-1c0-1 0-2 1-3h-5-1c-1 3-1 4 0 7v1h-1-2v-3c1-1 1-3 1-4l2-1z" class="b"></path><path d="M529 737c1 0 2 0 2-1 3-1 4 0 7 0h1c1 3 0 6 0 9l-1 2c-1 2-2 4-4 6l1-5 1-6c1-1 2-2 2-3-1-1-1-2-2-2-2-1-4 0-5 1l-2-1z" class="d"></path><path d="M537 731c2 0 2 0 3 2l1 2c1 3 0 6-1 8l-1 2c0-3 1-6 0-9-2-1-3-2-5-2-4 1-6 2-8 4s-3 4-3 7c-1 3 0 6 0 10l-1-2c0-1 0-2-1-2v-2l-1 1c0-1-1-2-2-3v-1c0-1 0-3-1-4v-3l1-2h1c1 1 1 2 1 3 0-2 1-3 2-4 0-1 1-2 2-2 1-1 3-1 5-1l8-2z" class="K"></path><path d="M517 739l1-2h1c1 1 1 2 1 3l2 13c0-1 0-2-1-2v-2l-1 1c0-1-1-2-2-3v-1c0-1 0-3-1-4v-3z" class="L"></path><defs><linearGradient id="q" x1="423.497" y1="658.996" x2="514.09" y2="694.556" xlink:href="#B"><stop offset="0" stop-color="#a09ea0"></stop><stop offset="1" stop-color="#e3e3e1"></stop></linearGradient></defs><path fill="url(#q)" d="M443 615c3-2 5-3 8-3 2 0 3 2 4 4 3 5 5 10 8 15l16 41 22 62 2 5 1 3-1 1c0-1 0-2-1-3l-1-4c0-1-1-2-1-3-2 2-3 4-5 5l-1 1c-2 2-4 2-5 4 1 2 1 2 1 3l-2-3c-8-8-18-8-28-8 4-1 9-2 14-4 3-1 6-3 7-7-1-8-4-16-6-24l-20-55c-2-5-4-12-7-17-1-2-2-3-4-4v-1c0-2-2-2-3-3h0-2c1-1 4-3 5-4l-1-1z"></path><path d="M484 640c-1-1-2-3-3-4 3 3 6 5 10 7l4-5c1 0 2 1 3 2l1 1c2 1 4 2 6 4l4 3h1c0 1 1 1 1 2h0c2 1 4 3 6 4l1 2 1-1 8 8c3 2 6 6 7 10 1 3 1 5 2 7l1 10c1 1 1 1 0 2v5l1 2c2 2 2 2 4 2v2h0c-1-1-1 0-2-1l1 3v1h1c1 1 3 3 3 5h0l-2-1h-1l-1 1c2 4 1 8 1 13l-1 4h-3-5l-2 1c-5 1-11 0-15-2-2 0-2 0-3-1 0-3-2-4-3-6l-1-1-2-2-1-2c0-1-1-2-1-2l1-1c-1-3-3-4-4-6-2-4-5-9-6-14 0-1-1-1-2-2-1-3-3-8-2-11l-1-1c0-2 0-4-1-7v-2-2c1-1 1-1 0-2l-1-2v-1c-2-2-4-4-5-7 0-1-1-2 0-3-2-2-1-4-2-6-1-1-1 0-1-1-1-2-1-3 0-4l1-1 1 2 1-2z" class="E"></path><path d="M505 683c3 0 4 1 7 1 1 1 0 1 1 0 4 3 6 4 8 9-4-2-5-6-10-6h-1c-1-2-3-3-5-4z" class="c"></path><path d="M506 679s2-1 2 0c2 0 3 1 5 2 2 0 4 2 5 3 2 2 2 3 3 5 0 1 1 1 1 2v2l1 1v3l-2-4c-2-5-4-6-8-9-1 0-1-1-2-1h-1c-2-1-3-2-4-4z" class="d"></path><path d="M490 665c1-1 3-2 4-3 3-1 6-3 10-3 0 1 1 1 1 2 7 0 13 2 18 7l1 2 1-1h0v-1c3 5 5 10 6 15-2-1-2 0-4-2-1-4-3-8-7-10-2-1-4-3-6-4-4-2-8-2-11-2-3 1-5 2-7 4-1 4 0 8 3 12l6 3h0l2 2c-2 0-1 0-2-1h-1c-2 0-4-3-6-2l-1-1c-4-3-5-7-6-12l-1-1v-2c1-1 1-1 0-2z" class="O"></path><path d="M490 665c1-1 3-2 4-3 3-1 6-3 10-3 0 1 1 1 1 2-4 1-11 3-13 7-1 0-1 1-1 2l-1-1v-2c1-1 1-1 0-2z" class="I"></path><path d="M516 693c3 4 3 7 3 11h0v-4c2 2 1 6 1 8 0 1 0 3 1 4 0 2-1 5 0 7l1 1 1 2c0 1 0 1 1 2 3-1 6 0 8-2 0-1 0-2 1-2 1-1 2-3 3-4l1-1c0-1 0-1-1-2l1-1-1-1c-1-1-1-2 0-3v1h1 1c1 0 2 1 3 2 2 4 1 8 1 13l-1 4h-3-5l-2 1c-5 1-11 0-15-2-1-3-1-6-1-9 1-7 2-16-1-23l2-2z" class="B"></path><path d="M536 711c-1-1-1-2 0-3v1h1 1c1 0 2 1 3 2 2 4 1 8 1 13l-1 4h-3-5l-2 1c-2-1-3-2-4-2 3-1 6-1 8-3s4-4 4-6c0-3 0-4-2-6l-1-1z" class="H"></path><path d="M540 723l2 1-1 4h-3-5c3-1 5-2 7-5z" class="B"></path><path d="M537 709h1c1 0 2 1 3 2 2 4 1 8 1 13l-2-1v-8c0-3-1-4-3-6z" class="K"></path><path d="M516 693c3 4 3 7 3 11-1 6-3 15-1 21 3 2 6 2 9 2 1 0 2 1 4 2-5 1-11 0-15-2-1-3-1-6-1-9 1-7 2-16-1-23l2-2z" class="Y"></path><path d="M505 683c-1 0-3-1-4-2-1-3-2-6-2-9 0-1 1-3 2-4 2-1 7-1 10-1l1 1c3 0 5 3 7 5l8 8c2 2 2 1 4 2l2 7 1 2c-1 0-1 0-2-1l-1 2c0 1 1 1 1 2s1 2 1 3c-3-2-6-7-9-7-1-1-1-2-1-3h0c-1-2-3-3-5-4-1-1-3-3-5-3-2-1-3-2-5-2 0-1-2 0-2 0 1 2 2 3 4 4h1c1 0 1 1 2 1-1 1 0 1-1 0-3 0-4-1-7-1z" class="Z"></path><path d="M506 679c-2-1-3-2-3-4v-4c1-1 1-1 3-1l8 3c2 1 4 3 5 5 5 4 9 9 12 14v1c0 1 1 1 1 2s1 2 1 3c-3-2-6-7-9-7-1-1-1-2-1-3h0c-1-2-3-3-5-4-1-1-3-3-5-3-2-1-3-2-5-2 0-1-2 0-2 0z" class="V"></path><path d="M518 684c2 1 4 2 5 4h0c0 1 0 2 1 3 3 0 6 5 9 7 0-1-1-2-1-3s-1-1-1-2l1-2c1 1 1 1 2 1l3 5 1 2c2 2 2 2 4 2v2h0c-1-1-1 0-2-1l1 3v1h1c1 1 3 3 3 5h0l-2-1h-1l-1 1c-1-1-2-2-3-2h-1-1v-1c-1 1-1 2 0 3l1 1-1 1c1 1 1 1 1 2l-1 1c-1 1-2 3-3 4-1 0-1 1-1 2-2 2-5 1-8 2-1-1-1-1-1-2-1-9 0-17 0-25v-3l-1-1v-2c0-1-1-1-1-2-1-2-1-3-3-5z" class="S"></path><path d="M531 693l1-2c1 1 1 1 2 1l3 5 1 2c2 2 2 2 4 2v2h0c-1-1-1 0-2-1l1 3v1h1c1 1 3 3 3 5h0l-2-1h-1c-1-1-2-3-3-3h-1v-1-1c0-1-2-4-3-4v1h-1c-1-1 0-1 0-2l-1-2c0-1-1-2-1-3s-1-1-1-2z" class="V"></path><g class="a"><path d="M531 693l1-2c1 1 1 1 2 1l3 5 1 2v1c-2-1-2-2-3-3 0 2 0 2-1 3l-1-2c0-1-1-2-1-3s-1-1-1-2z"></path><path d="M518 684c2 1 4 2 5 4h0c0 1 0 2 1 3h1c2 8-4 27 0 32 2 0 4 0 5-2h1v-1c0-1 0-1 1-2 1-2 1-5 2-6l2-1 1 1-1 1c1 1 1 1 1 2l-1 1c-1 1-2 3-3 4-1 0-1 1-1 2-2 2-5 1-8 2-1-1-1-1-1-2-1-9 0-17 0-25v-3l-1-1v-2c0-1-1-1-1-2-1-2-1-3-3-5z"></path></g><path d="M527 695h0c2 0 4 6 6 8h0v5c-1 0-1 1-2 1v1c-1 1-1 2-2 3v2 2c-1 1 0 1 0 2v1 1c-2 0-2 0-4-1v-11c2-5 0-10 2-14z" class="P"></path><path d="M527 700c1 0 0 0 1 1 0 4 1 11-1 15h0c-1-3 0-5 0-7v-9z" class="D"></path><path d="M490 669l1 1c1 5 2 9 6 12l1 1c3 3 8 4 12 6 2 1 4 3 6 4l-2 2c3 7 2 16 1 23 0 3 0 6 1 9-2 0-2 0-3-1 0-3-2-4-3-6l-1-1-2-2-1-2c0-1-1-2-1-2l1-1c-1-3-3-4-4-6-2-4-5-9-6-14 0-1-1-1-2-2-1-3-3-8-2-11l-1-1c0-2 0-4-1-7v-2z" class="G"></path><path d="M504 698l-1-2c0-1 1 0 2 0v-2c2-1 3 0 4 0l1 2h-1l-1-1h-1v1 2 1l1 3h0-1c-1 0-1 0-2 1 0-2 0-3-1-5z" class="X"></path><path d="M504 698c1 2 1 3 1 5 1-1 1-1 2-1h1c1 5 2 12 5 16-1-1-2-1-2-2-1-1-2-3-4-5h0c-2-4-3-8-3-13z" class="F"></path><path d="M490 669l1 1c1 5 2 9 6 12l1 1c3 3 8 4 12 6 2 1 4 3 6 4l-2 2c-1-1-1-2-3-3s-4-2-7-3l-3 3v-1c-1-1-2-2-2-3h0c-3-3-5-6-7-9l-1-1c0-2 0-4-1-7v-2z" class="J"></path><path d="M511 692c2 1 2 2 3 3 3 7 2 16 1 23 0 3 0 6 1 9-2 0-2 0-3-1 0-3-2-4-3-6l-1-1-2-2-1-2c0-1-1-2-1-2l1-1h0l1-1h0c2 2 3 4 4 5 0 1 1 1 2 2-3-4-4-11-5-16h0l-1-3v-1-2-1h1l1 1h1l-1-2h2v-2z" class="K"></path><path d="M507 699v-1-2-1h1l1 1h1c2 1 2 2 3 4 3 5 1 11 1 17-1-2 0-6 0-8-2-3-3-4-3-7 0-1 0-1-1-2l-2-1h-1z" class="R"></path><path d="M507 699h1l2 1c1 1 1 1 1 2 0 3 1 4 3 7 0 2-1 6 0 8v1h-1c-3-4-4-11-5-16h0l-1-3z" class="a"></path><path d="M507 699h1l2 1v2h-2l-1-3z" class="V"></path><path d="M484 640c-1-1-2-3-3-4 3 3 6 5 10 7l4-5c1 0 2 1 3 2l1 1c2 1 4 2 6 4l4 3h1c0 1 1 1 1 2h0c2 1 4 3 6 4l1 2 1-1 8 8c3 2 6 6 7 10 1 3 1 5 2 7l1 10c1 1 1 1 0 2v5l-3-5-1-2-2-7c-1-5-3-10-6-15v1h0l-1 1-1-2c-5-5-11-7-18-7 0-1-1-1-1-2-4 0-7 2-10 3-1 1-3 2-4 3l-1-2v-1c-2-2-4-4-5-7 0-1-1-2 0-3-2-2-1-4-2-6-1-1-1 0-1-1-1-2-1-3 0-4l1-1 1 2 1-2z" class="C"></path><path d="M533 690c1-4 0-6 0-10 0-1 0-1-1-1v-3c-1-2-2-5-2-6 0 0 0 1 1 2v1c1 1 0 2 2 3v2l2 7c0 2 1 4 2 5s1 1 0 2v5l-3-5-1-2z" class="I"></path><path d="M484 640v1c2 1 5 2 7 3l-2 6-2-1c-1 0-1 0-1 1-1-1-3-3-3-4v-4l1-2z" class="G"></path><path d="M511 650c2 1 4 3 6 4l1 2 1-1 8 8c0 2 1 4 3 5h-1l-1 1h0c-2-3-4-5-6-7-1-2-2-3-4-4-2-2-5-5-7-8z" class="S"></path><path d="M484 655c0-1-1-2 0-3-2-2-1-4-2-6-1-1-1 0-1-1-1-2-1-3 0-4l1-1 1 2v4c0 1 2 3 3 4 0-1 0-1 1-1l2 1v4c-1 2-1 4-1 6l1 2c-2-2-4-4-5-7z" class="O"></path><path d="M486 650c0-1 0-1 1-1l2 1v4c-2-1-3-2-3-4z" class="B"></path><defs><linearGradient id="r" x1="507.454" y1="648.761" x2="501.052" y2="666.342" xlink:href="#B"><stop offset="0" stop-color="#030202"></stop><stop offset="1" stop-color="#262526"></stop></linearGradient></defs><path fill="url(#r)" d="M493 645c1-1 1-1 3-1 8 1 19 10 24 17l5 7v1h0l-1 1-1-2c-5-5-11-7-18-7 0-1-1-1-1-2-4 0-7 2-10 3-1 1-3 2-4 3l-1-2c1-2 0-3 0-5s1-5 1-7c0-3 1-4 3-6z"></path><path d="M490 651c0-3 1-4 3-6v1c1 2 3 3 6 4h-2l-2-1-2 2c-1 0-1 0-2-1v1h-1z" class="G"></path><path d="M504 659c6-1 12 2 16 5 2 1 3 2 3 4-5-5-11-7-18-7 0-1-1-1-1-2z" class="J"></path><defs><linearGradient id="s" x1="476.281" y1="563.301" x2="433.785" y2="572.304" xlink:href="#B"><stop offset="0" stop-color="#0b0b0a"></stop><stop offset="1" stop-color="#2c2b2c"></stop></linearGradient></defs><path fill="url(#s)" d="M433 491c2-2 3-4 5-6 1-1 2-1 3-1 2 1 3 3 4 5l1-1 5 16h2c1-1 1-2 1-3l1 7 1-5c1-3 1-6 4-8 5-1 10 4 14 7 6 4 9 9 14 15v-3c1 1 2 2 3 4 1 1 1 0 1 1l1-1c2 5 3 11 5 16l6 20c1 2 1 4 2 6h1v1h1v3c1 0 2-1 3-1 1 1 1 1 2 1v1h2c2 1 3 3 3 5v1l-2 2v1h0c-1 1-3 2-3 3s0 0-1 1c-3 2-6 5-10 7-1 1-5 2-5 3h0c-1 1-2 1-3 2h3 0 3c-3 1-6 2-8 3-4 0-7 0-10-1-2 0-3 0-4-1l-1 1c2 3 3 7 5 9s3 3 5 3h1c1 1 1 1 2 0 2 0 3-1 5 0h0c1 1 1 1 2 1h0l-1 1c-1 0-2 0-3 1s-3 1-4 2c-2 2-3 4-4 7 3 3 6 6 10 7 1 1 4 1 4 2h3c1 0 2 1 3 1h1v-1c2 0 3-1 4-2l2 1h1 1l2 2 3-2h2v1 1c2 2 4 3 6 4-1 1-3 1-4 1h-2 0c-1 1-2 1-4 2l-2 1h-1-9c0 1 1 2 2 3 2 3 5 6 7 9l5 9-1 1-1-2c-2-1-4-3-6-4h0c0-1-1-1-1-2h-1l-4-3c-2-2-4-3-6-4l-1-1c-1-1-2-2-3-2l-4 5c-4-2-7-4-10-7 1 1 2 3 3 4l-1 2-1-2-1 1c-1 1-1 2 0 4 0 1 0 0 1 1 1 2 0 4 2 6-1 1 0 2 0 3l-15-30c-3-6-6-13-10-19-1-3-3-7-5-10l-1 2v3h-1c-2 3-4 5-7 7-3 3-5 5-8 7l-1 1c0-2 2-7 3-9 2-7 3-15 4-23l-1-13c1-2 1-4 1-6-1-4-1-9-2-13-2-1-2-4-3-5v-1h-1s0-2-1-3l-1-7-8-23v-1l5 13h1v-1-3c-1-3-2-8-2-11v-8c0-3 1-7 2-11z"></path><path d="M444 605l9-9h1l-1 2v3h-1c-4 0-5 4-8 4z" class="C"></path><path d="M443 565l1 5c0 3 0 6-1 10v4l-1-13c1-2 1-4 1-6z" class="K"></path><path d="M444 605c3 0 4-4 8-4-2 3-4 5-7 7-3 3-5 5-8 7 1-4 4-7 7-10z" class="S"></path><path d="M431 510v-8c1 7 1 14 3 21h0l7 29c-2-1-2-4-3-5v-1h-1s0-2-1-3l-1-7-8-23v-1l5 13h1v-1-3c-1-3-2-8-2-11z" class="W"></path><path d="M445 489l1-1 5 16c1 3 2 7 1 9 0 0 0-1-1-2v-2c-2-1-4 0-5 1l-1-1c-1-1-1-1-1-2v-1c-1-1-1-1-1-2l-1-1h0c0 1 0 1-1 2v-1-1l-1-1c-1-1-1-1-1-2-2-3-3-3-3-7 0-1 0-1 1-1 3-1 6 1 8 2h0 1c0-1 0-2-1-2v-3z" class="Q"></path><path d="M451 504h2c1-1 1-2 1-3l1 7 1-5c2 1 3 2 3 5 1 1 1 1 1 2 3 14 2 29 2 43 0 16 3 32 10 46l6 9 6 7 1 1c3 3 6 6 10 7 1 1 4 1 4 2h3c1 0 2 1 3 1h1v-1c2 0 3-1 4-2l2 1h1 1l2 2 3-2h2v1 1c2 2 4 3 6 4-1 1-3 1-4 1h-2 0c-1 1-2 1-4 2l-2 1h-1-9c0 1 1 2 2 3 2 3 5 6 7 9l5 9-1 1-1-2c-2-1-4-3-6-4h0c0-1-1-1-1-2h-1l-4-3c-2-2-4-3-6-4l-1-1c-1-1-2-2-3-2-2-1-4-3-6-4-4-4-8-8-12-13-15-21-21-44-23-69-1-14-1-30-8-42 1-1 3-2 5-1v2c1 1 1 2 1 2 1-2 0-6-1-9z" class="Y"></path><path d="M506 626v-1c2 0 3-1 4-2l2 1h1 1l2 2c-4 1-6 0-10 0z" class="B"></path><path d="M455 508l1-5c2 1 3 2 3 5 1 1 1 1 1 2-1 1-1 0-1 1v1h-2v3l-2 2h0-1c0-3 0-6 1-9z" class="K"></path><path d="M521 626c2 2 4 3 6 4-1 1-3 1-4 1h-2 0c-1 1-2 1-4 2-2-1-4-1-7-1h0 4c1-1 0-1 1-2h-1c-3-1-5-1-8 0h-1l1-1h0c1-1 1-1 2-1 2-1 5 1 7 1 2-1 5 0 6-3z" class="H"></path><path d="M505 645c1 0 2 0 3 1l6 3c-1-1-2-3-4-4-1-1-2-2-2-3h0l-2-2-2-4-1-2h-1l1-2c2 1 3 1 5 1 1 1 2 0 3 0l-1-1c3 0 5 0 7 1l-2 1h-1-9c0 1 1 2 2 3 2 3 5 6 7 9l5 9-1 1-1-2c-2-1-4-3-6-4h0c0-1-1-1-1-2h-1l-4-3z" class="I"></path><path d="M456 503c1-3 1-6 4-8 5-1 10 4 14 7 6 4 9 9 14 15v-3c1 1 2 2 3 4 1 1 1 0 1 1l1-1c2 5 3 11 5 16l6 20c1 2 1 4 2 6h1v1h1v3c1 0 2-1 3-1 1 1 1 1 2 1v1h2c2 1 3 3 3 5v1l-2 2v1h0c-1 1-3 2-3 3s0 0-1 1c-3 2-6 5-10 7-1 1-5 2-5 3h0c-1 1-2 1-3 2h3 0 3c-3 1-6 2-8 3-4 0-7 0-10-1-2 0-3 0-4-1l-1 1c2 3 3 7 5 9s3 3 5 3h1c1 1 1 1 2 0 2 0 3-1 5 0h0c1 1 1 1 2 1h0l-1 1c-1 0-2 0-3 1s-3 1-4 2c-2 2-3 4-4 7l-1-1-6-7-6-9c-7-14-10-30-10-46 0-14 1-29-2-43 0-1 0-1-1-2 0-3-1-4-3-5z" class="W"></path><path d="M470 528c0-2 0-10 1-11v-1l2-2c0 1 1 1 2 1 4 1 10 9 11 12l1 2c-2-1-2-2-3-4h-1v1l-5-5c0-2-3-3-4-4h-1l-1 1c-1 3-1 7-1 10h-1z" class="b"></path><path d="M483 526v-1h1c1 2 1 3 3 4l7 15c3 5 6 11 7 16-2-2-4-6-5-9l-13-25z" class="S"></path><path d="M506 560h1v1h1v3l-3 5 1 1-2 2c-2 1-5 4-7 5l-1 1-4 1c-6 1-13-4-19-7l-4-4c2 0 4 1 5 2v-1l9 4c3 2 6 2 10 2 3-1 7-4 9-6 0-1 0-2 1-2 1-2 2-4 3-7z" class="B"></path><path d="M495 575l10-6 1 1-2 2c-2 1-5 4-7 5 0-1-1-2-2-2z" class="I"></path><path d="M469 568c2 0 4 1 5 2 5 3 12 7 17 6l4-1c1 0 2 1 2 2l-1 1-4 1c-6 1-13-4-19-7l-4-4z" class="J"></path><path d="M508 564c1 0 2-1 3-1 1 1 1 1 2 1v1h2c2 1 3 3 3 5v1l-2 2v1h0c-1 1-3 2-3 3s0 0-1 1c-3 2-6 5-10 7-1 1-5 2-5 3l-9 2h-5c-3 0-7-1-9-3 0-2-1-3-1-4v-1c-1-2-1-5-2-8 0-2 1-1 2-2 6 3 13 8 19 7l4-1 1-1c2-1 5-4 7-5l2-2-1-1 3-5z" class="K"></path><path d="M506 570l2-2c1 1 1 5 1 6v2h-3c-1 1-1 1-2 1s-1 0-1-1 0-1 1-2h1c0-1 0-1-1-1v-1l2-2z" class="B"></path><path d="M478 582c3 0 7 1 11 1l1 1c1 0 1 1 2 1h1c-4 1-8 1-13 0h-1 0c-1-1-1-1-1-2v-1z" class="d"></path><path d="M501 579l2-1 1 1c-2 3-7 5-9 6h-2-1c-1 0-1-1-2-1l-1-1c0-1 0 0-1-1s0-1-1-2c5 1 9 1 14-1z" class="W"></path><path d="M473 572c6 3 13 8 19 7l4-1c2 1 3 1 5 1-5 2-9 2-14 1 1 1 0 1 1 2s1 0 1 1c-4 0-8-1-11-1v1c0 1 0 1 1 2h0l-1 1h-2 0c1 2 4 3 6 3l6 1h-5c-3 0-7-1-9-3 0-2-1-3-1-4v-1c-1-2-1-5-2-8 0-2 1-1 2-2z" class="c"></path><defs><linearGradient id="t" x1="477.096" y1="574.587" x2="483.286" y2="584.604" xlink:href="#B"><stop offset="0" stop-color="#5b5a5b"></stop><stop offset="1" stop-color="#7f7d7d"></stop></linearGradient></defs><path fill="url(#t)" d="M478 582c-2 0-3 0-4-1s-2-4-2-7c2 2 5 3 7 4s6 2 8 2c1 1 0 1 1 2s1 0 1 1c-4 0-8-1-11-1z"></path><path d="M508 564c1 0 2-1 3-1 1 1 1 1 2 1v1h2c2 1 3 3 3 5v1l-2 2v1h0c-1 1-3 2-3 3s0 0-1 1c-3 2-6 5-10 7-1 1-5 2-5 3l-9 2-6-1c-2 0-5-1-6-3h0 2c6 2 12 2 17 0 5-1 11-5 14-10v-2c0-1 0-5-1-6l-2 2-1-1 3-5z" class="J"></path><path d="M513 565h2c2 1 3 3 3 5v1l-2 2-3 2-1-1c-1-2-1-5-1-7 0-1 1-2 2-2z" class="O"></path><path d="M471 528c0-3 0-7 1-10l1-1h1c1 1 4 2 4 4l5 5 13 25c1 3 3 7 5 9l1 1c-1 3-2 6-4 8-3 2-7 4-11 4-1-1-3-1-4-2v2h0l-9-4c0-1-2-2-3-2-1-4-1-7-1-11 1-2 0-6 0-8v-20h1z" class="P"></path><path d="M471 528c0-3 0-7 1-10l1-1h1c1 1 4 2 4 4h-1v-1c-1-1-2-1-2-2h-1v1c0 1 0 0-1 1v7c-1 9 0 17 0 26 1 2 0 4 1 6 0 2-1 4 1 6l2 2c1 0 3 1 4 2 1 0 1 0 2 1s2 1 4 1v-1h1c4 1 6-1 8-3l6-6c-1 3-2 6-4 8-3 2-7 4-11 4-1-1-3-1-4-2v2h0l-9-4c0-1-2-2-3-2-1-4-1-7-1-11 1-2 0-6 0-8v-20h1z" class="S"></path><path d="M470 528h1l2 38c2 1 5 2 7 3 1 1 2 1 3 2v2h0l-9-4c0-1-2-2-3-2-1-4-1-7-1-11 1-2 0-6 0-8v-20z" class="a"></path><defs><linearGradient id="u" x1="458.96" y1="496.036" x2="497.775" y2="607.53" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#363636"></stop></linearGradient></defs><path fill="url(#u)" d="M456 503c1-3 1-6 4-8 5-1 10 4 14 7 6 4 9 9 14 15v-3c1 1 2 2 3 4 1 1 1 0 1 1l1-1c2 5 3 11 5 16l6 20h-1c-1-2-2-5-3-7h0c-1-1-2-2-2-3-1-2-2-3-3-4l-9-18v-1c-3-1-7-5-9-7-1-1-4-2-5-3-1 1-2 1-2 2-1 2-1 5-1 7l1 36h0c0 4 0 7 1 11 1 0 3 1 3 2v1c-1-1-3-2-5-2l4 4c-1 1-2 0-2 2 1 3 1 6 2 8v1c0 1 1 2 1 4 2 2 6 3 9 3h5l9-2h0c-1 1-2 1-3 2h3 0 3c-3 1-6 2-8 3-4 0-7 0-10-1-2 0-3 0-4-1l-1 1c2 3 3 7 5 9s3 3 5 3h1c1 1 1 1 2 0 2 0 3-1 5 0h0c1 1 1 1 2 1h0l-1 1c-1 0-2 0-3 1s-3 1-4 2c-2 2-3 4-4 7l-1-1-6-7-6-9c-7-14-10-30-10-46 0-14 1-29-2-43 0-1 0-1-1-2 0-3-1-4-3-5z"></path><path d="M488 514c1 1 2 2 3 4 1 1 1 0 1 1l1-1c2 5 3 11 5 16l6 20h-1c-1-2-2-5-3-7-2-10-7-22-12-30v-3z" class="H"></path><path d="M478 608h1l1 1 1 1v1c1 1 2 2 3 2v-1c0-1 1-1 1-2h-1c-1 0-2-1-3-2-2-2-3-4-4-7h1c1 1 3 0 4 0 2 2 3 3 5 3h1c1 1 1 1 2 0 2 0 3-1 5 0h0c1 1 1 1 2 1h0l-1 1c-1 0-2 0-3 1s-3 1-4 2c-2 2-3 4-4 7l-1-1-6-7z" class="B"></path><path d="M495 604h0c1 1 1 1 2 1h0l-1 1c-1 0-2 0-3 1s-3 1-4 2c-2 2-3 4-4 7l-1-1 2-5c1-3 5-5 9-6z" class="C"></path><path d="M469 568c-1-1-1 0-1-1l1-1-1-1c-1-12-1-24-1-35 0-6-1-12-1-18 1-2 1-3 2-5 1 0 2-1 3 0 7 1 12 9 15 15v-1c-3-1-7-5-9-7-1-1-4-2-5-3-1 1-2 1-2 2-1 2-1 5-1 7l1 36h0c0 4 0 7 1 11 1 0 3 1 3 2v1c-1-1-3-2-5-2z" class="F"></path><path d="M642 361l5-24c3-22 5-46 0-67-6-23-24-44-48-48-1-1-13-2-13-2l-1-2-4-7c-2-3-5-7-7-10-7-8-14-12-22-17 21 4 43 5 65 6l43 1 31 2h17c5-4 7-13 9-19l-1 12c-1 3-1 4 1 7 3 1 5 0 8 0 3-2 4-10 5-13 1 3-1 6 1 10 2 2 5 3 8 3 4 1 16 1 19-1 4-4 4-17 5-23 1 3 1 7 1 10 0 4-1 10 1 13 2 2 4 2 6 2 19-1 45-6 58-20 5-5 8-10 7-16 0-4-1-7-4-9-1-1-4-2-6-2-2 1-3 1-4 3v1c2 1 3 1 4 3 0 1 1 2 0 3-1 2-3 2-4 2-2 1-3 0-4-2-2-1-3-4-2-6 0-4 2-6 5-8s8-2 12-1 8 4 10 8c6 13-2 22-6 34h0c-3 1-7 6-10 8l-12 8c-1 2-3 2-5 2h-1l-19 7-2 1-8 3-10 5-3 1c-7 5-14 10-20 16-12 10-22 25-30 38-11 17-19 38-25 57-2 7-3 15-6 22l-1 1c-4 1-10-2-12-4-6-5-8-15-8-22-1-1-1-1-2-1v11c1 5 3 10 7 14l7 5c1 2 2 5 2 7h-1l-2-1-1 1c1 0 3 1 3 2 0 3-2 7-2 9h2l-1 2h2v5c-1 0-2 2-2 2 2 2 0 11 0 14-1 14-1 27 2 40 0 2 0 6 2 7l-1 2c-4 1-8 0-12 0-3 1-6 2-9 4v3 1c-5 6-10 14-13 22l-3 6-1-1-2 2c-1 3-2 7-4 10h-5l-6 1c-1 0-3 0-4 1h-1c0 1 0 2-1 3l-2 7c-1 1-1 2-1 4h0l-1 4c-1 2-2 5-3 8l-2 6-1 1v4 1c0 2-2 5-3 7l-3 6c-1 3-3 8-2 12 1 0 1-1 2-1l2-1h1c-1 2-1 2-3 3l-1 1c-1 1-2 4-3 4-1 1-2 0-2 1v2h0l-5-2c-1-1-1-1-2-1-2 0-4-1-5 0-2 1-2 6-3 8-1 3-4 6-5 9-1 1-1 1-1 2h1 4l1 1v2l-2 1c2 1 3 1 5 2h0 4v1 4c0 1-1 1-1 2-1 2-1 2 0 4l-4 6-4 5h1c-1 3-4 8-4 10l2 2h1 3c0 2-2 4-3 7-1-1-2-2-3-2-1-1-2-1-4-1-1 0-1 0-2-1-5 3-7 10-9 15-1 2-3 4-4 5l-1 1c-1 0-1 0-2 1h-1s-1 0-1-1h-1v-3c-1-4-3-9-6-11v1c-2-3-3-5-4-8h-1c-1 0-2-2-3-4l-1 1h0v1l-1 2v1c0 2 0 4-2 5v1h-6c-2 0-3-1-5-2 1 0 3 0 4-1-2-1-4-2-6-4v-1-1h-2l-3 2-2-2h-1-1l-2-1c-1 1-2 2-4 2v1h-1c-1 0-2-1-3-1h-3c0-1-3-1-4-2-4-1-7-4-10-7 1-3 2-5 4-7 1-1 3-1 4-2s2-1 3-1l1-1h0c-1 0-1 0-2-1h0c-2-1-3 0-5 0-1 1-1 1-2 0h-1c-2 0-3-1-5-3s-3-6-5-9l1-1c1 1 2 1 4 1 3 1 6 1 10 1 2-1 5-2 8-3h-3 0-3c1-1 2-1 3-2h0c0-1 4-2 5-3 4-2 7-5 10-7 1-1 1 0 1-1s2-2 3-3h0v-1l2-2v-1c0-2-1-4-3-5 0-3-2-7-3-10 0-1-1-4-1-5l-3-4c-1-1-1-4-2-6l-5-11-21-46c-6-14-15-26-19-40 0-2-1-3-1-4 0-2-1-2-2-3-2 0-5 2-7 2h-1-1-1-1-1l-3-1 2-2c1 0 1 1 2 1h1c1 1 1 0 2 0 4 0 5-1 8-3-2-2-2-6-3-8l-18-39c-5-12-11-25-15-38-2-2-11-29-12-32l-3-10c-1-4-3-13-6-15-2-1-5-1-7-1l-1 1h0 0-2-1l-2 1c0 1-1 2-2 3h0c0-1-1-1-1-2 1-1 1-2 0-3v-4c-3 1-5 6-7 8l-1 1c0 2 0 2-1 3h-4l-1-1h-3-1c-1 0-1 0-2-1l-2-1c-3-1-4-4-6-6-5-2-6-8-9-12l-3-4c-1-2-2-4-4-6l-6-9v1c-1 0-1 0-1 1-1 1-1 2-2 2-2-3-4-7-7-10l-1 1v-1c-2-1-4-2-5-4h0-1c-3-4-8-10-12-12-3-2-4-4-7-5-1 0-4-2-5-3l-10-7c-8-5-17-10-25-13h0c-3 0-6-2-9-3s-6-2-8-3l-4-3h-4c-7-5-14-10-20-16-1-1-2-3-3-5-3-7-5-13-5-20 0-5 2-10 6-13 3-3 8-4 12-4 4 1 7 2 10 6 1 1 2 4 1 6 0 2-1 4-3 5s-3 2-5 1c-1-1-2-1-2-3-1-4 2-2 4-5-1-2-1-3-3-3-2-1-4-1-6 0s-4 4-5 7 0 8 1 11c7 12 22 19 34 23 6 2 32 7 36 4 1-1 2-2 2-3 1-2 0-6 0-8-1-4-1-8-1-12 1 2 1 3 2 5 0 5 3 16 7 18 3 2 21 1 24 0 3 0 4-2 5-4 1-4-1-7-2-11 3 5 4 12 9 15 3 1 8 1 12 1 22 0 48-6 66-19 3-2 6-5 9-7 1-1 4-6 5-6 1-1 3-1 4-1h10l22-6c0 1-6 4-7 5-5 4-9 8-14 13-3 0-7 2-9 5l-1 1c1 2-1 4-1 6h0c-4 7-8 13-11 21-8 21-5 49 1 70 1 3 2 6 4 8 3 1 8-1 11 0h1 2 1c0 1-2 2-3 3-2 1-4 4-5 6l-1 1c-1 1-1 2-1 3v1c-1 2 0 3 0 5v2c0 1 0 0 1 1v1h0l127 286 1-2h0c0-2 1-3 2-4v-2l2-7c1 0 1-1 1-2l1-3v-1c0-2 0-4 1-5 0-2 0-4 1-7 1-1 1-4 1-6 0-6 4-9 5-14l1-3-1-1v-2-4c0-1 1-1 1-2v-3 1l2 3c1 6 1 13 3 19v4l16-47 1 1 1-2s1 0 1 1h1l1-3c1-1 1-1 1-2s1-1 1-2v-1l1-1v-2l1-1v-2-2c1-3 1-6 1-9h-1c-1-6-3-8-7-11 3 0 4 0 6-1 1-2 0-3-1-5h2l1-1 2 1c1 1 5 1 6 0s2-3 2-4c3-6 5-12 7-19 4-12 8-25 10-39h0l3-17 2-1c1 0 1 0 2-1s0-1 1-2h1c0-1 0-3 1-4l2-10 1-5z" class="Y"></path><path d="M683 208l1 1v3h1 0l-1 1c-3 0-5 4-8 4 1-3 5-6 7-9z" class="B"></path><path d="M613 223c-1 0-2-1-4-2v-2c1-1 2-1 3-2 2 1 3 1 4 2-1 2-1 3-3 4z" class="L"></path><path d="M510 542l7 19h0l-2-2c0-2-1-4-3-4 0-1-1-4-1-5l-1-2v-6z" class="T"></path><path d="M687 262h3c2 0 2 1 3 2 0 1-2 2-3 3l-1 1h-2-1v-1c-1-2 0-3 1-5z" class="B"></path><path d="M552 632c1-1 2-1 3-1v-3c0-1-1-1 0-2h0c1 2 1 4 1 6 3 0 4-4 6-6h0l-3 7c-1 2-2 3-3 5-1-1-3-5-4-6z" class="J"></path><path d="M671 278c1 3 2 6 4 8v2h1v-1c0-1 1-2 2-3l-1 7c-4-2-8-4-9-7l1-1 2 2v-1-6z" class="B"></path><path d="M595 539c0 1 0 2 1 3-1 2-1 3-1 5-1 4-3 8-4 12-1-1-1-1-2-1 0-6 4-14 6-19z" class="W"></path><path d="M683 279c1-3 1-5 3-8 2-1 2-1 4-1 1 1 0 1 0 2l2 2-1 1c-3 2-5 3-7 5l-1-1z" class="Q"></path><path d="M665 303c1 3 0 5-1 8l-1 8v7 11c0-2 0-4-1-6v1 2c-1 1-1 0-1 1-1-1-1-2-2-3l6-29zm-12-65c1 1 3 3 3 4v1l1 1 3 6c0 3 1 7 2 11 1 2 1 4 0 6-2-5-3-10-4-16-1-1-2-3-2-5 0-1 0-1-1-2-1-2-1-4-2-6z" class="C"></path><path d="M683 208c2-1 4 0 5 0 5 0 10 0 15 1h0c0 1 1 0 0 2-2-1-4 0-6 0l-12 1h-1v-3l-1-1z" class="F"></path><path d="M665 303s0-1 1-2v-2c0-2 0-4 1-6 0-2 1-2 3-3l1 1c-2 2-2 6-3 8-2 10-3 18-3 28-1-1-1-1-2-1v-7l1-8c1-3 2-5 1-8z" class="I"></path><path d="M662 267c1-2 1-4 0-6-1-4-2-8-2-11 1 2 4 15 5 16h1l3 17-1 1-6-17z" class="T"></path><path d="M540 608c5 7 9 16 12 24 1 1 3 5 4 6 0 2-1 3-2 4h0l-3-5-1-4c-1-2 0 0 0-2-1-3-3-5-4-8-1-1-1-3-2-5-1-1-2-3-3-5 0 0 0-1-1-2v-3zm36-15l1 1c-1 3-5 12-4 15-1 3-2 7-4 10-2 2-3 3-4 6l-4 8h-2l3-7c6-10 10-22 14-33z" class="X"></path><path d="M671 238c6 6 10 17 10 24v1 1c-1-1-1-2-2-3l-1-1c0-2 0-3-2-4v-2l-3-5c0-2-1-3-3-5v-1c1-1 1 0 1-1v-4z" class="E"></path><path d="M530 589l7 15c1 1 3 3 3 4v3c1 1 1 2 1 2 1 2 2 4 3 5 1 2 1 4 2 5 1 3 3 5 4 8 0 2-1 0 0 2l1 4c-7-9-11-20-15-30l1-1c-1-2-1-3-2-5l-1-2-4-9v-1z" class="Z"></path><path d="M616 219l1 1c9 6 19 10 26 19-5-2-8-6-13-8-1 0-1 0-2-1l-1-1c1 1 1 2 2 3h0c-5-4-10-6-16-9 2-1 2-2 3-4z" class="C"></path><path d="M631 437c1 1 1 1 1 3-1 3-2 7-4 11 0 2-1 4-1 7-1 6-5 12-5 19l-2 1h0c-1-2-1-2-1-4 1-5 3-11 5-16l7-21z" class="M"></path><path d="M685 212l12-1c2 0 4-1 6 0l-2 2c-2 1-5 1-8 2-5 2-10 3-15 6-1 0-1-2-2-3v-1c3 0 5-4 8-4l1-1h0z" class="O"></path><path d="M410 316c1 0 1-1 1-1 1-2-6-17-6-21h0v-4c-1-1-1-1-1-2h1l15 48c-1 4 1 8 3 11l-1 1c-2-2-11-29-12-32z" class="U"></path><path d="M360 219c-3-3-7-6-10-10l29-2h0c-6 0-10 1-14 6-2 2-3 4-5 6z" class="B"></path><path d="M356 212c2 0 3 0 5 1v2l-1 1-4-4z" class="L"></path><path d="M615 207h-3l57 1c1 1 1 0 1 1-1 1-1 0-1 1-1 0-1 1-2 0h-12c-9 0-19 0-28-1-2-1-3 0-4-2h-8z" class="F"></path><defs><linearGradient id="v" x1="530.147" y1="575.813" x2="512.815" y2="570.82" xlink:href="#B"><stop offset="0" stop-color="#736f70"></stop><stop offset="1" stop-color="#8f9292"></stop></linearGradient></defs><path fill="url(#v)" d="M512 555c2 0 3 2 3 4l2 2h0l10 20c1 2 2 5 3 8v1l4 9 1 2c1 2 1 3 2 5l-1 1c-1-1-8-18-9-20l-7-13c-2 1-5 3-8 4 1-1 1 0 1-1s2-2 3-3h0v-1l2-2v-1c0-2-1-4-3-5 0-3-2-7-3-10z"></path><path d="M684 251l-6-12c-2-4-2-7-1-11 2-4 8-6 12-8l-1 1c-1 1-3 2-4 3v1l3 1c-2 1-4 3-5 5l-1 1c-1 3 1 7 3 9 1 2 1 3 3 3h1c-1 1-1 2-2 3h-1 0c-1 1-1 1-1 2v2z" class="K"></path><path d="M616 484c2 0 3-1 5 0-5 7-7 15-10 23-1 1-3 5-3 7-3 7-6 15-9 23-1 3-2 7-4 10 0-2 0-3 1-5-1-1-1-2-1-3l21-55zm36-115c0 1 1 2 1 3-1 1 0 3-1 4h0l-25 82c0-3 1-5 1-7 2-4 3-8 4-11 0-2 0-2-1-3l12-36 4-13c0-2 0-5 1-7v-1l1-1v-3h1l1-1 1-6z" class="d"></path><path d="M653 228c2 3 3 5 4 8l1 1c1 0 2-1 3-1l1-1h1v-2h1c2 2 3 2 6 1l1-1v1l2 2-1 1h-2c-1 1-1 1-2 1-2 0-5 3-6 5l2 1v1c-1 1 0 1 0 2v1h-1c1 5 3 9 3 13v1 1 3h-1c-1-1-4-14-5-16l-3-6-1-1v-1c0-1-2-3-3-4h-1v-4c-1-2-2-3-3-5 1 0 2-1 4-1z" class="J"></path><path d="M658 237c1 0 2-1 3-1l1-1h1c-1 3-2 5-4 7l-2-1v-2c0-1 0-1 1-2z" class="G"></path><path d="M653 228c2 3 3 5 4 8l1 1c-1 1-1 1-1 2v2l2 1c-1 1 0 1-1 1l-6-9c-1-2-2-3-3-5 1 0 2-1 4-1z" class="K"></path><path d="M662 243l2 1v1c-1 1 0 1 0 2v1h-1c1 5 3 9 3 13v1 1 3h-1c0-7-4-14-6-21l1-1 2-1z" class="E"></path><path d="M662 243l2 1v1c-1 1 0 1 0 2v1h-1c-2-1-3-2-3-4l2-1z" class="L"></path><defs><linearGradient id="w" x1="249.717" y1="202.375" x2="252.515" y2="196.353" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#b2b1b1"></stop></linearGradient></defs><path fill="url(#w)" d="M235 197c-6-5-13-11-17-18h-1v-1c8 8 18 16 29 21 10 4 20 6 31 8v1 1l-3-1v1h-1c-1 0-2-1-3 0h0-1l1-1h-1v1l-3-1-2 1c-2 0-3-1-5-1h0c-3 0-6-2-9-3s-6-2-8-3l-4-3-3-2z"></path><path d="M235 197c5 1 9 3 13 5 6 3 12 5 18 6l-2 1c-2 0-3-1-5-1h0c-3 0-6-2-9-3s-6-2-8-3l-4-3-3-2z" class="D"></path><path d="M422 348l1-1c-2-3-4-7-3-11l19 47 14 32c2 6 6 13 7 19v1c-1-1-1-2-2-2h0c-2-2-2-6-3-8l-18-39c-5-12-11-25-15-38z" class="V"></path><path d="M379 207l24-2v1c-3 7-5 14-7 21-1 3-2 8-4 10 0 1-1 2-2 3-2 1-5 0-7 0l1-1c2-2 5-3 6-6l2-5c1-1 1-2 1-4l1-5c-4 0-8-1-13-2v-1l3-1-1-1h1c1 0 3-1 5-2l-1-1c-2-1-3-2-4-2s-2-1-3-2h-2 0z" class="G"></path><path d="M381 207l10 2c1 1 5 0 6 1-3 3-9 4-13 5l-1-1h1c1 0 3-1 5-2l-1-1c-2-1-3-2-4-2s-2-1-3-2z" class="J"></path><path d="M622 477c0 1 0 1 2 2h1c2 1 4 0 5 3l1 2c1 0 1 1 2 1 0 1 0 3-1 5l1 1h1c-1 1-2 2-3 2l-6 1c-1 0-3 0-4 1h-1c0 1 0 2-1 3l-2 7-3 2 1 1c-1 2-2 3-4 4-1 0-1 0-1 1s0 1-1 2v3c-1 1-2 3-2 5l-1 1-1 2c-1 1 0 1-1 1 0-4 5-9 4-13 0-2 2-6 3-7 3-8 5-16 10-23-2-1-3 0-5 0 0-1 1-5 1-5 1 0 2 0 3-1h0l2-1z" class="L"></path><path d="M622 477c0 1 0 1 2 2h1c2 1 4 0 5 3l1 2c1 0 1 1 2 1 0 1 0 3-1 5l1 1h1c-1 1-2 2-3 2l-6 1c-1 0-3 0-4 1h-1c1-2 2-4 4-5s5 1 8-2c-1-1 0-2-1-3l-2 2h-1c-2-2-4-3-7-3-2-1-3 0-5 0 0-1 1-5 1-5 1 0 2 0 3-1h0l2-1z" class="E"></path><path d="M620 478h0c1 1 3 3 5 4 1 1 5 1 6 3l-2 2h-1c-2-2-4-3-7-3-2-1-3 0-5 0 0-1 1-5 1-5 1 0 2 0 3-1z" class="I"></path><path d="M615 207h8c1 2 2 1 4 2 9 1 19 1 28 1h12c1 1 1 0 2 0 0 3 0 5-1 8h-5c-4 0-7 0-11-1h0-9s-1 1-1 2c-1 0-1 1-2 1-7-6-15-10-25-13z" class="O"></path><defs><linearGradient id="x" x1="487.421" y1="494.063" x2="484.736" y2="495.361" xlink:href="#B"><stop offset="0" stop-color="#5a5859"></stop><stop offset="1" stop-color="#757575"></stop></linearGradient></defs><path fill="url(#x)" d="M460 439c4 4 6 11 9 16l23 47 18 40v6l1 2-3-4c-1-1-1-4-2-6l-5-11-21-46c-6-14-15-26-19-40 0-2-1-3-1-4z"></path><path d="M739 209l39-3c9-1 18-3 27-7 12-5 24-15 32-26h1c-6 12-16 19-26 27l-3 2-19 7-2 1c-6 0-13 2-20 2h-2c-2-1-4-1-6-1-9-1-17-1-26-1l5-1z" class="T"></path><path d="M790 209h-1v-1h0l6-3c4-2 8-3 11-5s6-4 9-5v1c-1 1-3 2-3 4l-3 2-19 7z" class="U"></path><path d="M608 514c1 4-4 9-4 13 1 0 0 0 1-1l1-2 1-1c0-2 1-4 2-5v-3c1-1 1-1 1-2s0-1 1-1c2-1 3-2 4-4l-1-1 3-2c-1 1-1 2-1 4h0l-1 4c-1 2-2 5-3 8l-2 6-1 1v4 1c0 2-2 5-3 7l-3 6c-1 3-3 8-2 12 1 0 1-1 2-1l2-1h1c-1 2-1 2-3 3l-1 1c-1 1-2 4-3 4-1 1-2 0-2 1-2-2-5-3-7-5-1-1-1-1-1-2 1 0 1 0 2 1 1-4 3-8 4-12 2-3 3-7 4-10l9-23z" class="G"></path><path d="M602 537l8-19c0-2 1-4 2-6 0 0 2 0 2-1 1 0 1-1 2-2l-1 4c-1 2-2 5-3 8l-3 3c0 3-2 6-3 8h-2c0 2-1 4-2 5z" class="E"></path><path d="M602 537c1-1 2-3 2-5h2c1-2 3-5 3-8l3-3-2 6-1 1c-4 7-5 16-11 23-2 3 0 7-4 8 0-3 2-6 3-9l5-13z" class="K"></path><path d="M594 559c4-1 2-5 4-8 6-7 7-16 11-23v4 1c0 2-2 5-3 7l-3 6c-1 3-3 8-2 12 1 0 1-1 2-1l2-1h1c-1 2-1 2-3 3l-1 1c-1 1-2 4-3 4-1 1-2 0-2 1-2-2-5-3-7-5-1-1-1-1-1-2 1 0 1 0 2 1l2 1c1 0 0 0 1-1z" class="F"></path><path d="M640 220c1 0 1-1 2-1 0-1 1-2 1-2h9 0c4 1 7 1 11 1h5 1c3 0 5 1 8 3l-3 4c-1 1-1 3-2 4 0 1-1 3-1 4l-1 1c-3 1-4 1-6-1h-1v2h-1l-1 1c-1 0-2 1-3 1l-1-1c-1-3-2-5-4-8-2 0-3 1-4 1l-9-9z" class="a"></path><path d="M658 232c0 1 0 1-1 1v-2c-1-1-1-1-2-1v-1c0-1-2-4-3-5h0c0-2-1-2-2-2v-1c4-2 11-1 15 0l2 1h2l-2 2v-1c-2-1-6-2-7-1l-2 1h-4-1c0 1 0 1 1 2 0 1 1 1 2 2v1l2 2v2z" class="P"></path><path d="M640 220c1 0 1-1 2-1 0-1 1-2 1-2h9c-3 1-4 0-6 3v2c2 1 3 2 4 3l3 3c-2 0-3 1-4 1l-9-9z" class="B"></path><path d="M656 227c-1-1-2-1-2-2-1-1-1-1-1-2h1 4l2-1c1-1 5 0 7 1v1l1 3-1 1-1-1c-1-1-1 0-2-1-1 1-2 2-2 3-2 0-1 0-2-1h-1c-2 0-2-1-3-1z" class="I"></path><path d="M669 222c0 1 1 1 2 1 1 1 2 1 3 2-1 1-1 3-2 4 0 1-1 3-1 4l-1 1c-3 1-4 1-6-1h-1l-1-1h-4v-2l-2-2v-1c1 0 1 1 3 1h1c1 1 0 1 2 1 0-1 1-2 2-3 1 1 1 0 2 1l1 1 1-1-1-3 2-2z" class="C"></path><path d="M669 222c0 1 1 1 2 1 1 1 2 1 3 2-1 1-1 3-2 4l-1-1c1 0 0 0 1-1v-1h0c-2 0-2 0-4 1l-1-3 2-2z" class="D"></path><path d="M671 228l1 1c0 1-1 3-1 4l-1 1c-3 1-4 1-6-1 1-2 5-3 7-5z" class="B"></path><path d="M689 220c3-1 7-2 10-3l2 2c2 0 3-2 6-1-2 3-2 5-3 7v1l-1-1c0 6 0 9-4 12v1c-1 2-4 3-5 4-3 2-4 3-6 6v-4h-1c-2 0-2-1-3-3-2-2-4-6-3-9l1-1c1-2 3-4 5-5l-3-1v-1c1-1 3-2 4-3l1-1z" class="J"></path><path d="M689 220c3-1 7-2 10-3l2 2c2 0 3-2 6-1-2 3-2 5-3 7v1l-1-1c-1-1-3-2-5-3-4 1-7 1-11 4l-3-1v-1c1-1 3-2 4-3l1-1z" class="Q"></path><path d="M701 219c2 0 3-2 6-1-2 3-2 5-3 7v1l-1-1c-1-1-3-2-5-3h-4-2c2-2 7-2 9-3z" class="O"></path><path d="M662 243c1-2 4-5 6-5 1 0 1 0 2-1l1 1v4c0 1 0 0-1 1v1c2 2 3 3 3 5l3 5v2c2 1 2 2 2 4l1 1c1 1 1 2 2 3v-1c0 3-1 19-3 21-1 1-2 2-2 3v1h-1v-2c-2-2-3-5-4-8v6 1l-2-2c-1-5-2-11-3-17v-3-1-1c0-4-2-8-3-13h1v-1c0-1-1-1 0-2v-1l-2-1z" class="K"></path><path d="M662 243c1-2 4-5 6-5 1 0 1 0 2-1l1 1v4c0 1 0 0-1 1l-2-2-2 2c-1 0-2 1-2 2v2c0-1-1-1 0-2v-1l-2-1z" class="Q"></path><path d="M664 248c1 5 3 9 4 14 1 2 0 4 1 6l2 10v6 1l-2-2c-1-5-2-11-3-17v-3-1-1c0-4-2-8-3-13h1z" class="G"></path><path d="M676 256h0c1 5 2 10 1 14l-1 3c0 1 0 1-1 3l-1-1c-1-1-1-2-2-4s-2-5-2-8c0-4-2-7-3-11v-9l6 6 3 5v2z" class="D"></path><path d="M379 242h1c2 1 4 2 7 2s5 1 7 4c0 0 1 1 1 2 3 9 5 18 7 27 1 3 3 8 3 11h-1c0 1 0 1 1 2v4h0c0 4 7 19 6 21 0 0 0 1-1 1l-3-10c-1-4-3-13-6-15-2-1-5-1-7-1l-1 1h0 0-2-1l-2 1c0 1-1 2-2 3h0c0-1-1-1-1-2 1-1 1-2 0-3v-4c-3 1-5 6-7 8 0-2 1-4 2-5 1-3 4-4 5-6v-1c2-1 3-2 5-3h1c1 0 2 0 3-1h-2l2-1 1-1c1-4 0-8-1-12-1-2-2-4-3-5v-1l-2-1c0-1 0-1-1-2-1 0 0 0-1-1h2v-2l-2-1-2 1 1-1-3-3 3-1h0l-2-2c-2 0-4-1-5-3z" class="P"></path><path d="M386 247l1-1 1 2c0 1 0 2-1 3l-2 1 1-1-3-3 3-1h0z" class="K"></path><path d="M392 282c2 0 5-1 6 0s1 2 1 3-1 1-2 1l-2 2v-1-1c-1 0-1 0-1 1h-1 0-2s-1 0-1 1l-1 1v-2l-2-2c-1 0 0 0-1-1 2-1 4-2 6-2z" class="G"></path><path d="M570 540l1-3-1-1v-2-4c0-1 1-1 1-2v-3 1l2 3c1 6 1 13 3 19v4l-16 53-5-12 1-2h0c0-2 1-3 2-4v-2l2-7c1 0 1-1 1-2l1-3v-1c0-2 0-4 1-5 0-2 0-4 1-7 1-1 1-4 1-6 0-6 4-9 5-14z" class="L"></path><defs><linearGradient id="y" x1="741.929" y1="210.432" x2="739.807" y2="233.999" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#323334"></stop></linearGradient></defs><path fill="url(#y)" d="M723 209h3 13l-5 1c9 0 17 0 26 1 2 0 4 0 6 1h2c7 0 14-2 20-2l-8 3-10 5-3 1c-1 0-2 1-3 1h0l-1-1c-1 0-2 0-3 1h-1l-1-1c-2 0-3 0-6 1-4-1-9-1-14 0l-13-1c-2-1-5-1-8 0-2 0-4 2-5 3-3 3-4 5-4 9-1 0-2 0-2 1-1-2 0-4 0-6l-2-1c1-2 1-4 3-7-3-1-4 1-6 1l-2-2c3 0 5 0 7-1 4-2 7-4 10-5 2-1 5-1 7-2z"></path><path d="M707 218c2-1 3-2 5-2-3 3-5 6-6 10h0l-2-1c1-2 1-4 3-7z" class="H"></path><path d="M723 209h3 13l-5 1c9 0 17 0 26 1 2 0 4 0 6 1h2c7 0 14-2 20-2l-8 3c-2-1-5-1-7-1l-2 1c-7 2-16 2-23 2h-17c-5 0-10-1-15-1-1 0-2 1-3 2h-1c-2 0-3 1-5 2-3-1-4 1-6 1l-2-2c3 0 5 0 7-1 4-2 7-4 10-5 2-1 5-1 7-2z" class="M"></path><path d="M723 209h3 13l-5 1c9 0 17 0 26 1 2 0 4 0 6 1-16 1-30 2-45-1l2-2z" class="I"></path><defs><linearGradient id="z" x1="604.489" y1="286.662" x2="666.591" y2="310.368" xlink:href="#B"><stop offset="0" stop-color="#939193"></stop><stop offset="1" stop-color="#bab8b8"></stop></linearGradient></defs><path fill="url(#z)" d="M629 232h0c-1-1-1-2-2-3l1 1c1 1 1 1 2 1 5 2 8 6 13 8 6 7 11 14 14 22 2 7 3 14 3 21 0 8-2 15-3 23l-4 35c-1 5-3 9-4 14l-4 20h-1l1-1c0-1 0-3 1-4v-2c0-1 0-2 1-3l-1 1c0 1 0 1-1 1 0 1 0 2-1 2v3h0v1l-1 1v2h0v1 1h-1v-4-4h0c1-2 1-2 1-3l1-1v-1c5-17 8-35 9-52 0-16 0-33-4-47-4-13-11-23-20-33z"></path><path d="M641 366l1-5c0 1 1 1 0 2v2 1c0 1 0 1-1 3 0 1 1 1 0 2 0 1 0 1-1 2 0 1 1 1 0 2 0 1-1 4-1 4v1h1c0 1 0 1 1 2s1 1 1 2c1-1 1-2 1-3v-2c0-1 0-2 1-3v-2h1c-2 7-2 13-3 19s-3 12-5 18c-5 16-12 33-18 50l-6 18c-1 4-2 8-3 11 0-1 0-3 1-5 0-1 1-2 1-3 1-2 1-4 2-6l1-2c0-2 0-3 1-4v-1-1-1c0-2-1-5 0-6 0-2 1-3 1-4v-1l1-1v-1c0-2 0 1 1-1v-2h1v-2l1-1h0v2c0 1 0 1-1 2h0v4l-1 1 1 1v-1c0-1 0-1 1-2v-2l1-1v-2l1-2 1-2-1-1c0-1 1-2 1-4l1 1h0c-1 1-1 2-1 3l2-3v-1l1-4c0-1 1-2 1-3 1-1 1-3 2-4l1-4c1-2 2-4 2-5l2-6c0-1 1-2 1-3v-2l1-1c0-1 0-2 1-3h-1v1l-3 3v1 1h-1v1c0 1 0 2-1 3h0c1 0 0 1 0 1v2 1c-1 1-1 2-1 2 0 1 0 2-1 3h0l-2 6c0 1-1 2-1 3v-1c-1-1 0-1-1-1s0 0-1 1h0l-1 2h-1v1c0 1 0 1-1 1v1 3l-2 2-1 1v1c0 2 0 3-1 4v2l-1 1c0 1 0 3-1 4 0 1-1 1-1 2l-1-1v1 1 1h0l1 1h-1 0l-1 1c1 0 0 0 1 1h0-1c-1 1 0 1 0 2 0 0-1 0-1 1-1 1 0 1-1 2v2l-1 1v1 1c-1 1-1 2-1 3h-1c0 2-3 5-5 5v1 1c0 1 0 2-1 3l-1 3c1-3 1-6 1-9h-1c-1-6-3-8-7-11 3 0 4 0 6-1 1-2 0-3-1-5h2l1-1 2 1c1 1 5 1 6 0s2-3 2-4c3-6 5-12 7-19 4-12 8-25 10-39h0l3-17 2-1c1 0 1 0 2-1s0-1 1-2h1c0-1 0-3 1-4l2-10z" class="D"></path><path d="M599 463h2c2 1 4 2 5 5-1 1-1 2-2 2-2 0-2 0-3-1l-1-1c1-2 0-3-1-5z" class="Y"></path><path d="M632 384l2-1c1 0 1 0 2-1s0-1 1-2h1c-2 4-5 19-9 21l3-17z" class="B"></path><defs><linearGradient id="AA" x1="403.396" y1="228.832" x2="433.754" y2="239.988" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#AA)" d="M429 179c1 2-1 4-1 6h0c-4 7-8 13-11 21-8 21-5 49 1 70 1 3 2 6 4 8 3 1 8-1 11 0l-3 1h-5-2-1c-1 0-1-1-2-2l-1-1-2 2c0 1-1 1-1 2-1 1-2 2-4 3h0c-1 1-1 1 0 3v2c1 1 0 1 1 2v1 1c0 1 1 2 1 3l1 2v2c0 1 0 2 1 2 0 1 0 3 1 4v1c0 2 1 3 2 5v1c0 1 0 2 1 2v1c0 1 0 1 1 2 0 1 0 2 1 3s0 2 0 3l1 1v2h1v1h0v1l1 1v1h-1c-5-13-8-26-12-39-3-14-8-28-8-43-2-28 6-54 25-75z"></path><path d="M512 578c3-1 6-3 8-4l7 13c1 2 8 19 9 20 4 10 8 21 15 30l3 5h0c1-1 2-2 2-4 1-2 2-3 3-5h2l4-8c1-3 2-4 4-6 2-3 3-7 4-10-1-3 3-12 4-15l-1-1c1-2 2-5 3-7l1 1c2 1 3 1 5 2h0 4v1 4c0 1-1 1-1 2-1 2-1 2 0 4l-4 6-4 5h1c-1 3-4 8-4 10l2 2h1 3c0 2-2 4-3 7-1-1-2-2-3-2-1-1-2-1-4-1-1 0-1 0-2-1-5 3-7 10-9 15-1 2-3 4-4 5l-1 1c-1 0-1 0-2 1h-1s-1 0-1-1h-1v-3c-1-4-3-9-6-11v1c-2-3-3-5-4-8s-4-5-5-8c-2-5-4-9-7-14l-9-19c-1-2-2-2-4-2-5-1-10 3-14 6l-3 1h-3 0-3c1-1 2-1 3-2h0c0-1 4-2 5-3 4-2 7-5 10-7z" class="L"></path><path d="M559 633h2l4-8c1-3 2-4 4-6-2 5-10 25-14 26-1-1-1-2-1-3h0c1-1 2-2 2-4 1-2 2-3 3-5z" class="M"></path><path d="M576 593c1-2 2-5 3-7l1 1c2 1 3 1 5 2h0 4v1 4c0 1-1 1-1 2-1 2-1 2 0 4l-4 6-4 5h1c-1 3-4 8-4 10l2 2h1 3c0 2-2 4-3 7-1-1-2-2-3-2-1-1-2-1-4-1-1 0-1 0-2-1 2-2 2-5 3-7l7-19c0-3 2-5 2-7 1-2 1-3 0-4-1 1-2 3-3 4-3 5-5 10-7 16-1-3 3-12 4-15l-1-1z" class="a"></path><path d="M580 606h1l1-1v1h2l-4 5h0c-1 3-5 8-5 11 0 2 0 2-1 3h-1c1-1 1-3 2-5s2-4 2-7l3-7z" class="Q"></path><path d="M585 589h4v1 4c0 1-1 1-1 2-1 2-1 2 0 4l-4 6h-2v-1l-1 1h-1l2-8c2-3 3-6 3-9h0z" class="L"></path><path d="M708 231c0-4 1-6 4-9 1-1 3-3 5-3 3-1 6-1 8 0l13 1h8c-4 4-8 8-12 13l-16 18c-2 3-6 6-8 9-1 2-4 4-6 5h-2c-1 2-1 3-3 4l-1 1-3-3c1-2 1-2 2-3l-1-2c-2-1-3-2-5-3h-1c-1-1-2-1-3-1-1-1-3-5-3-7v-2c0-1 0-1 1-2h0 1c1-1 1-2 2-3v4c2-3 3-4 6-6 1-1 4-2 5-4v-1c4-3 4-6 4-12l1 1v-1l2 1c0 2-1 4 0 6 0-1 1-1 2-1z" class="N"></path><path d="M709 238c1-3-1-7 1-9v-1l2-3c2-2 4-3 6-4v1c5-1 8-1 12 0-2 1-5 2-8 1-1 0-2-1-3 0h-1-1c-3 2-5 5-6 8 0 1 1 3 1 5v1c1 3-2 7-3 10l-4 7h0l-1-1c-1-1 0-1-1-2s-2-1-3-2c1-1 2-3 3-3h1c1-1 2-2 2-3v-1l3-4z" class="I"></path><path d="M696 262c1-1 2-2 2-4l-3-2-1-1c0-1 1-2 1-2l1-1-1-1c1-4 9-7 11-9v1c0 1-1 2-2 3h-1c-1 0-2 2-3 3 1 1 2 1 3 2s0 1 1 2l1 1c-1 2-1 3-2 5 0 2-2 3-2 5h2l-1 1c-1 2-1 3-3 4l-1 1-3-3c1-2 1-2 2-3l-1-2z" class="C"></path><path d="M708 231c0-4 1-6 4-9 1-1 3-3 5-3 3-1 6-1 8 0l13 1h8c-4 4-8 8-12 13l-16 18c-2 3-6 6-8 9-1 2-4 4-6 5h-2l1-1 36-42h-9c-4-1-7-1-12 0v-1c-2 1-4 2-6 4l-2 3v1c-2 2 0 6-1 9l-1-7z" class="S"></path><path d="M703 225l1 1v-1l2 1c0 2-1 4 0 6 0-1 1-1 2-1l1 7-3 4c-2 2-10 5-11 9l1 1-1 1s-1 1-1 2l1 1 3 2c0 2-1 3-2 4-2-1-3-2-5-3h-1c-1-1-2-1-3-1-1-1-3-5-3-7v-2c0-1 0-1 1-2h0 1c1-1 1-2 2-3v4c2-3 3-4 6-6 1-1 4-2 5-4v-1c4-3 4-6 4-12z" class="E"></path><path d="M691 246c2-3 4-4 7-6l2 3-5 3h-2-2z" class="I"></path><path d="M691 246h2 2c-4 4-4 8-4 13h-1c-1-2-2-4-1-6v-1c0-2 1-4 2-6z" class="C"></path><path d="M704 225l2 1c0 2-1 4 0 6 0 2 1 4 0 6s-4 4-6 5l-2-3c2 0 3-1 4-2 3-3 2-8 2-12v-1z" class="Y"></path><path d="M706 232c0-1 1-1 2-1l1 7-3 4c-2 2-10 5-11 9l1 1-1 1s-1 1-1 2l1 1 3 2c0 2-1 3-2 4-2-1-3-2-5-3 0-5 0-9 4-13l5-3c2-1 5-3 6-5s0-4 0-6z" class="K"></path><path d="M360 219c2-2 3-4 5-6 4-5 8-6 14-6h2c1 1 2 2 3 2s2 1 4 2l1 1c-2 1-4 2-5 2h-1l1 1-3 1v1c5 1 9 2 13 2l-1 5c0 2 0 3-1 4l-2 5c-1 3-4 4-6 6l-1 1h-1-2-1l-2-2h-1-2c1 1 1 2 2 3h2l1 1h0c1 2 3 3 5 3l2 2h0l-3 1 3 3-1 1-1 1c-3-1-5-1-8-1 1 0 2 1 3 1h0l-1 1-1 1c-1 1-3 4-5 4l-1-1c-3 0-6-1-9-2-2 0-6-1-8-2l-4-2v-3l-1-2-1-1-3-3h0c-1-1-1-3-1-4 0-3 1-5 3-7-2-3 5-8 6-11h-1c-2-1-1-1-2-3s-4-5-5-7h0c3 3 7 6 11 8 1 0 2 1 3 2h2 1l-2-2z" class="H"></path><path d="M371 212c4-2 9-3 13-2 1 1 2 1 3 1h1l1 1c-2 1-4 2-5 2s-1-2-2-2h-5c-2 1-3 1-4 1s-2-1-2-1zm-17 34l-2-3c-1-2-2-3-2-5 0-4 5-14 8-17l2 1c-1 1-1 1-2 1v1c0 2 0 4-1 6-1 4-4 7-4 11l1 1c1 1 2 2 2 3v1c3 4 8 4 12 5h0c-6-1-9-1-14-5z" class="B"></path><path d="M345 211c3 3 7 6 11 8l-1 1c-1 2-2 4-3 7l-4 6c-1 2-1 3-3 5 1 1 0 1 1 1l2 1c1 3 2 6 4 8v-1l1-1h1c5 4 8 4 14 5 1 1 2 1 4 1h4c1 0 2 1 3 1h0l-1 1-1 1c-1 1-3 4-5 4l-1-1c-3 0-6-1-9-2-2 0-6-1-8-2l-4-2v-3l-1-2-1-1-3-3h0c-1-1-1-3-1-4 0-3 1-5 3-7-2-3 5-8 6-11h-1c-2-1-1-1-2-3s-4-5-5-7h0z" class="Z"></path><path d="M352 248v-1l1-1h1c5 4 8 4 14 5 1 1 2 1 4 1h4c1 0 2 1 3 1h0l-1 1c-9 0-19 0-26-6z" class="I"></path><path d="M371 212s1 1 2 1 2 0 4-1h5c1 0 1 2 2 2h-1l1 1-3 1v1c5 1 9 2 13 2l-1 5-1 1c-3 4-6 5-11 6-3 2-8 1-11 0l-3-3c-1-1-2-1-3-2l1-2c-1-1-2-2-2-3 0-2 1-3 2-4h0c1-3 4-4 6-5z" class="D"></path><path d="M380 230c3-1 5-2 7-3 1-1 3-2 4-3l1 1c-3 4-6 5-11 6-1 0-1-1-1-1z" class="J"></path><path d="M369 224c0-1 0-2 1-3h1c3-2 6-2 10-2 2 0 4 0 6 1v1s0 1-1 1c-3 1-11 1-13-1l-1 1 1 1h13 3v1c-1 1-4 3-6 3s-2 0-4 1v1l-1 1-2-1c-1-1-2 0-4-1-1-1-3-3-3-4z" class="H"></path><path d="M373 223h13l-2 2h0c-1 1-1 1-2 1h-1c-2 0-7 1-9 0v-1c0-1 0-1 1-2z" class="I"></path><path d="M371 212s1 1 2 1 2 0 4-1h5c1 0 1 2 2 2h-1v1c-4 1-8 1-12 2h-1c-1 1-2 3-2 5 0 1 0 1 1 2 0 1 2 3 3 4 2 1 3 0 4 1l2 1 1-1v-1c2-1 2-1 4-1-1 1-2 1-3 3 0 0 0 1 1 1-3 2-8 1-11 0l-3-3c-1-1-2-1-3-2l1-2c-1-1-2-2-2-3 0-2 1-3 2-4h0c1-3 4-4 6-5z" class="L"></path><path d="M365 224c2 2 3 4 6 4 2 1 3 1 5 1l2 1 1-1v-1c2-1 2-1 4-1-1 1-2 1-3 3 0 0 0 1 1 1-3 2-8 1-11 0l-3-3c-1-1-2-1-3-2l1-2z" class="I"></path><path d="M360 222c3 4 5 7 10 9 3 1 8 2 11 0 5-1 8-2 11-6l1-1c0 2 0 3-1 4l-2 5c-1 3-4 4-6 6l-1 1h-1-2-1l-2-2h-1-2c1 1 1 2 2 3h2l1 1h0c1 2 3 3 5 3l2 2h0l-3 1 3 3-1 1-1 1c-3-1-5-1-8-1h-4c-2 0-3 0-4-1h0c-4-1-9-1-12-5v-1c0-1-1-2-2-3l-1-1c0-4 3-7 4-11 1-2 1-4 1-6v-1c1 0 1 0 2-1z" class="D"></path><path d="M356 241l1-1 1 1-1 4h-1c0-1-1-2-2-3h0 1l1-1z" class="C"></path><path d="M371 242c1 1 2 1 3 2 3 1 6 3 9 4l3 3-1 1-1 1c-3-1-5-1-8-1h-4l1-1h7c-1-1-2-2-4-3h-1l-3-3h0c-1-2-1-2-1-3z" class="E"></path><path d="M366 244v-2c1-1 1-3 2-4 3-3 5 3 8 3h0 2l1 1h0c1 2 3 3 5 3l2 2h0l-3 1c-3-1-6-3-9-4-1-1-2-1-3-2l-1-2-1 1c1 1 1 1 1 2l-1 1h-1-2z" class="H"></path><path d="M357 230l2 1v-2h1 0c2 2 3 3 5 4 2 2 3 2 6 2 1 1 2 1 4 1h1l1 1c2 0 3-1 5 0l-2 1v1l2 1h-2-1l-2-2h-1-2c1 1 1 2 2 3h0c-3 0-5-6-8-3-1 1-1 3-2 4v2h-2c-1-1 0-1-1-1-2 0-1 0-2-1-1 0-1-1-1-2-1-1-1-1-2-1h0v-1l-2 1v2l-1 1h-1 0l-1-1c0-4 3-7 4-11z" class="C"></path><path d="M357 230l2 1v-2h1 0c2 2 3 3 5 4l-1 2c-2-1-2-2-3-2h-2l-1 1 1 1v1h0l-1 2-2 1v2l-1 1h-1 0l-1-1c0-4 3-7 4-11z" class="P"></path><path d="M360 222c3 4 5 7 10 9 3 1 8 2 11 0 5-1 8-2 11-6l1-1c0 2 0 3-1 4l-2 5c-1 3-4 4-6 6l-1 1h-1l-2-1v-1l2-1c-2-1-3 0-5 0l-1-1h-1c-2 0-3 0-4-1-3 0-4 0-6-2-2-1-3-2-5-4h0-1v2l-2-1c1-2 1-4 1-6v-1c1 0 1 0 2-1z" class="R"></path><path d="M382 237l1-1h0-2v-1c1 0 1 0 2-1s3-1 4-2l5-4-2 5c-1 3-4 4-6 6l-1 1h-1l-2-1v-1l2-1z" class="F"></path><path d="M503 589c4-3 9-7 14-6 2 0 3 0 4 2l9 19c3 5 5 9 7 14 1 3 4 5 5 8h-1c-1 0-2-2-3-4l-1 1h0v1l-1 2v1c0 2 0 4-2 5v1h-6c-2 0-3-1-5-2 1 0 3 0 4-1-2-1-4-2-6-4v-1-1h-2l-3 2-2-2h-1-1l-2-1c-1 1-2 2-4 2v1h-1c-1 0-2-1-3-1h-3c0-1-3-1-4-2-4-1-7-4-10-7 1-3 2-5 4-7 1-1 3-1 4-2s2-1 3-1l1-1h0c-1 0-1 0-2-1h0c-2-1-3 0-5 0-1 1-1 1-2 0h-1c-2 0-3-1-5-3s-3-6-5-9l1-1c1 1 2 1 4 1 3 1 6 1 10 1 2-1 5-2 8-3l3-1z" class="D"></path><path d="M517 588c4 5 10 16 10 22-1-1-2-2-2-4l-7-13c-1-2-1-3-1-5z" class="U"></path><path d="M503 589c3 0 5-2 8-3 2 0 3-1 5 0l1 2c-1 0-2 1-4 1-4 2-9 5-13 5h-1c-4 1-9 1-14 2l-1 1c2 3 7 4 10 5s6-1 8 2l-1-1h-1c-2 0-3 0-5 1h0c-2-1-3 0-5 0-1 1-1 1-2 0h-1c-2 0-3-1-5-3s-3-6-5-9l1-1c1 1 2 1 4 1 3 1 6 1 10 1 2-1 5-2 8-3l3-1z" class="W"></path><path d="M503 589c3 0 5-2 8-3 2 0 3-1 5 0l1 2c-1 0-2 1-4 1-4 2-9 5-13 5h0-1c-4 1-14 1-17-2 3 1 6 1 10 1 2-1 5-2 8-3l3-1z" class="b"></path><path d="M503 589c3 0 5-2 8-3 2 0 3-1 5 0-3 1-6 3-9 4-5 2-10 3-15 3 2-1 5-2 8-3l3-1z" class="W"></path><path d="M503 589c4-3 9-7 14-6 2 0 3 0 4 2l9 19c3 5 5 9 7 14 1 3 4 5 5 8h-1c-1 0-2-2-3-4l-1 1h0v1l-1 2v1c0 2 0 4-2 5v1h-6c-2 0-3-1-5-2 1 0 3 0 4-1-2-1-4-2-6-4v-1-1h-2v-1l3-3-2 1c-1 0-2 0-2-1l-2-2c0-1-1-1-1-2h-1l-1-2c-2 0-2-2-3-3 4 1 8 5 11 8 2-2 3-3 6-5 1 0 2 0 4 1h0l-4-5c0-6-6-17-10-22h0l-1-2c-2-1-3 0-5 0-3 1-5 3-8 3z" class="X"></path><path d="M530 604c3 5 5 9 7 14l-1 3h0l-1-1c0-1-1-2-1-3-1-2-2-3-2-5-1-2-1-5-2-8z" class="c"></path><path d="M522 620c2-2 3-4 5-4s3 0 4 1c2 1 3 2 4 3l1 1h0l1-3c1 3 4 5 5 8h-1c-1 0-2-2-3-4l-1 1h0v1l-1 2v1c0 2 0 4-2 5v1h-6c-2 0-3-1-5-2 1 0 3 0 4-1-2-1-4-2-6-4v-1-1h-2v-1l3-3z" class="I"></path><path d="M535 626h1v1c0 2 0 4-2 5v1h-6c3-1 4-2 6-4l1-3z" class="E"></path><path d="M531 617c2 1 3 2 4 3l1 1h0l1-3c1 3 4 5 5 8h-1c-1 0-2-2-3-4l-1 1h0v1l-1 2h-1c0-4-1-6-4-9z" class="K"></path><path d="M522 624c1-3 2-5 5-6 2 2 5 5 5 7v2l-3 3h-2c-2-1-4-2-6-4l1-2z" class="M"></path><path d="M522 624v1c1 0 1 0 2 1 2-2 3-1 6-1 1 1 1 0 2 2l-3 3h-2c-2-1-4-2-6-4l1-2z" class="Q"></path><path d="M495 604c2-1 3-1 5-1h1l1 1c1 1 2 2 4 3s3 2 4 4c1 1 1 3 3 3l1 2h1c0 1 1 1 1 2l2 2c0 1 1 1 2 1l2-1-3 3v1l-3 2-2-2h-1-1l-2-1c-1 1-2 2-4 2v1h-1c-1 0-2-1-3-1h-3c0-1-3-1-4-2-4-1-7-4-10-7 1-3 2-5 4-7 1-1 3-1 4-2s2-1 3-1l1-1h0c-1 0-1 0-2-1z" class="M"></path><path d="M496 606l1-1v1l2 2c-1 1-1 3-1 4 1 0 1 1 2 1s2 0 3-1l2 2s0-1 1-1v2h-2-1v-1c-2 1-3 1-5 0-2 0-1-1-1-3l-1-1h-1l-1-1c1-1 2-2 2-3z" class="F"></path><path d="M497 605c1 0 3-1 5 0 1 1 3 3 3 4s0 1 1 2v2c-1 0-1 1-1 1l-2-2c-1 1-2 1-3 1s-1-1-2-1c0-1 0-3 1-4l-2-2v-1h0z" class="R"></path><path d="M493 607c1-1 2-1 3-1 0 1-1 2-2 3l1 1h0c0 1 0 1 1 2 0 1 0 2 1 3 1 0 1 1 2 2h3v2c-1 1-1 1 0 1-1 1-2 1-3 1-3-1-6-3-7-6-1-1-1-2-2-3h0l1-2-2-1c1-1 3-1 4-2z" class="K"></path><path d="M493 607c1-1 2-1 3-1 0 1-1 2-2 3l1 1h0c0 1 0 1 1 2 0 1 0 2 1 3 1 0 1 1 2 2h3v2l-1-1h-1c-2 0-4-3-5-4l-1-1h0c-1-1-1-1-1-2-1-1 0-1-1-2 1 0 1-1 1-1v-1z" class="Z"></path><path d="M489 609l2 1-1 2h0c1 1 1 2 2 3 1 3 4 5 7 6 1 0 2 0 3-1l2-1v-1c2-1 3-1 5-1 0 3 0 4-2 5-2 3-4 3-8 3 0-1-3-1-4-2-4-1-7-4-10-7 1-3 2-5 4-7z" class="L"></path><path d="M495 604c2-1 3-1 5-1h1l1 1c1 1 2 2 4 3s3 2 4 4c1 1 1 3 3 3l1 2h1c0 1 1 1 1 2l2 2c0 1 1 1 2 1l2-1-3 3v1l-3 2-2-2h-1-1l-2-1c-1 1-2 2-4 2v1h-1c-1 0-2-1-3-1h-3c4 0 6 0 8-3 2-1 2-2 2-5s-2-6-4-8c0-1-2-3-3-4-2-1-4 0-5 0s-1 0-2-1z" class="H"></path><path d="M506 607c2 1 3 2 4 4 1 1 1 3 3 3l1 2h1c0 1 1 1 1 2l2 2c0 1 1 1 2 1l2-1-3 3v1l-3 2-2-2h-1-1l-2-1c1-1 2-2 2-4v-1c-1-5-3-8-6-11z" class="M"></path><path d="M512 619v1c1 1 1 2 0 3h1l2-2c1 0 2 1 4 2h0v1l-3 2-2-2h-1-1l-2-1c1-1 2-2 2-4z" class="c"></path><path d="M758 219l1 1h1c1-1 2-1 3-1l1 1h0c1 0 2-1 3-1-7 5-14 10-20 16-12 10-22 25-30 38-11 17-19 38-25 57-2 7-3 15-6 22l-1 1c-4 1-10-2-12-4-6-5-8-15-8-22 0-10 1-18 3-28 1-2 1-6 3-8 2 0 3 1 5 2 0 3-1 5-1 7l1 1v-1c1-2 1-3 2-4l2-1h0c0-1 0-1-1-2l3-7c1-2 0-5 1-7l1 1c2-2 4-3 7-5l1-1-2-2c0-1 1-1 0-2 1 0 2 0 3 1 0 1 0 2 1 4 0 0 0 1 1 1l3-6 1-1c2-1 2-2 3-4h2c2-1 5-3 6-5 2-3 6-6 8-9l16-18c4-5 8-9 12-13h-8c5-1 10-1 14 0 3-1 4-1 6-1z" class="L"></path><path d="M693 309c0-2 2-5 3-7 3-7 6-14 11-21-1 4-2 6-4 9l-4 7c0 1-1 3-1 4s-1 2-1 2c-2 5-3 10-5 15l1-9z" class="G"></path><path d="M752 220c3-1 4-1 6-1-3 2-6 5-9 8-16 13-27 28-37 46-2 2-4 5-5 8-5 7-8 14-11 21-1 2-3 5-3 7l-1 9-3 6-1-2-3 6c-1-2-1-4-1-5 5-17 13-33 23-48 12-21 26-41 45-55z" class="Y"></path><path d="M693 309l-1 9-3 6-1-2 5-13z" class="B"></path><path d="M680 295c1 2 0 4 0 6-1 3-2 7-2 10s0 6 1 9h1l1 4c1 1 1 2 3 3v-4c0 1 0 3 1 5l3-6 1 2-2 10c-1 4-2 9-5 12-2 0-3 1-5 1-2-1-3-1-3-3h0c-3-9-3-17-2-25 1-7 1-13 3-19l1 1v-1c1-2 1-3 2-4l2-1h0z" class="E"></path><path d="M679 320h1l1 4c1 1 1 2 3 3v-4c0 1 0 3 1 5l3-6 1 2-2 10c-2 3-3 8-6 10h-1v-1-1c-1-7-1-15-1-22z" class="R"></path><path d="M681 324c1 1 1 2 3 3v-4c0 1 0 3 1 5 0 5-2 10-4 14l-1 1v-1c2-4 1-7 3-10v-1c0-3 0-3-2-5v1-3z" class="F"></path><path d="M685 328l3-6 1 2-2 10c-2 3-3 8-6 10h-1v-1l1-1c2-4 4-9 4-14z" class="Z"></path><path d="M675 300l1 1v-1c1-2 1-3 2-4l2-1c-2 7-3 13-4 20-1 4-1 27-2 29-3-9-3-17-2-25 1-7 1-13 3-19z" class="H"></path><path d="M738 220c5-1 10-1 14 0-19 14-33 34-45 55-10 15-18 31-23 48v4c-2-1-2-2-3-3l-1-4h-1c-1-3-1-6-1-9s1-7 2-10c0-2 1-4 0-6 0-1 0-1-1-2l3-7c1-2 0-5 1-7l1 1c2-2 4-3 7-5l1-1-2-2c0-1 1-1 0-2 1 0 2 0 3 1 0 1 0 2 1 4 0 0 0 1 1 1l3-6 1-1c2-1 2-2 3-4h2c2-1 5-3 6-5 2-3 6-6 8-9l16-18c4-5 8-9 12-13h-8z" class="E"></path><path d="M684 301l3-2 2 1c-2 3-4 8-6 11-1-3 1-7 1-10z" class="F"></path><path d="M693 287v4l-4 9-2-1-3 2 1-2c1-1 2-2 2-3l6-9z" class="d"></path><path d="M691 275c0 1-1 2-2 3-2 3-3 7-3 11l-3 7c-3 7-5 17-3 24h-1c-1-3-1-6-1-9s1-7 2-10c0-2 1-4 0-6 0-1 0-1-1-2l3-7c1-2 0-5 1-7l1 1c2-2 4-3 7-5z" class="C"></path><path d="M690 270c1 0 2 0 3 1 0 1 0 2 1 4 0 0 0 1 1 1v2h0c1-1 2-3 3-4l1 1-2 4-1 1v1c-1 2-3 4-3 6l-6 9c0 1-1 2-2 3 1-3 2-4 2-7v-2h0l-1-1c0-4 1-8 3-11 1-1 2-2 2-3l1-1-2-2c0-1 1-1 0-2z" class="V"></path><path d="M690 270c1 0 2 0 3 1 0 1 0 2 1 4-1 2-1 3-2 5-1 1 0 0-1 2l-2-4c1-1 2-2 2-3l1-1-2-2c0-1 1-1 0-2z" class="Z"></path><path d="M689 278l2 4h1l-1 1-4 9v-2h0l-1-1c0-4 1-8 3-11z" class="B"></path><path d="M702 265h2c2-1 5-3 6-5 2-3 6-6 8-9 0 2-1 4-2 5-2 4-6 7-8 11-6 7-11 15-15 24v-4c0-2 2-4 3-6v-1l1-1 2-4-1-1c-1 1-2 3-3 4h0v-2l3-6 1-1c2-1 2-2 3-4z" class="a"></path><path d="M277 207l27 2c7 0 14 0 20 1 2 0 3 0 4 2l1 1 1 3 1 4 1 1h0c1-1 1-1 1-2 0-2 1-2 2-3 1-2 0-4 2-6 0 1 0 2 1 2 1 1 2 1 4 0 0-1 0-2 1-3h1l1 2h0c1 2 4 5 5 7s0 2 2 3h1c-1 3-8 8-6 11-2 2-3 4-3 7 0 1 0 3 1 4h0l3 3 1 1 1 2v3l4 2c2 1 6 2 8 2 3 1 6 2 9 2l1 1c2 0 4-3 5-4l1-1 1-1h0c-1 0-2-1-3-1 3 0 5 0 8 1l1-1 2-1 2 1v2h-2c1 1 0 1 1 1 1 1 1 1 1 2l2 1v1c1 1 2 3 3 5 1 4 2 8 1 12l-1 1-2 1h2c-1 1-2 1-3 1h-1c-2 1-3 2-5 3v1c-1 2-4 3-5 6-1 1-2 3-2 5l-1 1c0 2 0 2-1 3h-4l-1-1h-3-1c-1 0-1 0-2-1l-2-1c-3-1-4-4-6-6-5-2-6-8-9-12l-3-4c-1-2-2-4-4-6l-6-9v1c-1 0-1 0-1 1-1 1-1 2-2 2-2-3-4-7-7-10l-1 1v-1c-2-1-4-2-5-4h0-1c-3-4-8-10-12-12-3-2-4-4-7-5-1 0-4-2-5-3l-10-7c-8-5-17-10-25-13 2 0 3 1 5 1l2-1 3 1v-1h1l-1 1h1 0c1-1 2 0 3 0h1v-1l3 1v-1-1z" class="I"></path><path d="M335 216c1-2 0-4 2-6 0 1 0 2 1 2 1 1 2 1 4 0l1 1h0c1 3 4 5 5 8l-2 2h-1c0-2-1-2-2-3-2-1-5-1-7 0h-1v-4z" class="O"></path><path d="M345 243h0l3 3 1 1 1 2v3l4 2c2 1 6 2 8 2 3 1 6 2 9 2l1 1c2 0 4-3 5-4l1-1 1-1h0c-1 0-2-1-3-1 3 0 5 0 8 1l1-1 2-1 2 1v2h-2c1 1 0 1 1 1 1 1 1 1 1 2l2 1v1c1 1 2 3 3 5 1 4 2 8 1 12l-1 1-2 1s-1 0-1-1l-1-3-1 2s0 1 1 2c-1 1-2 1-3 1-1 1-5 4-6 3h0c-4-4-8-8-10-13-1-2-2-4-1-6s1-1 0-2l-12-3h-1c-3-1-5-3-8-5v-4h-1c0-3-3-3-3-6z" class="D"></path><path d="M375 271v-3c0-2 1-4 2-5l3-2 1 1v1l-1 1h1 1c1 1 2 1 3 2l-1 1c3 3 4 5 5 9 0 0 0 1 1 2-1 1-2 1-3 1-1 1-5 4-6 3h0v-1c2-2 2-4 2-6 1-1 1-1 2-1s1 0 2-1h-1l-1-1-1 1c-2-1-3-6-3-7h-3c-1 1-1 2-2 4l2 2c2 1 3 5 4 7l-3-3c-1-1-2-1-3-2s-1-2-1-3z" class="S"></path><path d="M377 257h1c1-1 3-2 4-2 2 0 6 2 7 2l2 1v1c1 1 2 3 3 5 1 4 2 8 1 12l-1 1-2 1s-1 0-1-1l-1-3-1 2c-1-4-2-6-5-9l1-1c-1-1-2-1-3-2h-1-1l1-1v-1l-1-1-3 2c-1 1-2 3-2 5v3l-3-5c1-5 1-6 5-9z" class="B"></path><path d="M385 266l1-2c2 3 3 7 4 10l-1 2c-1-4-2-6-5-9l1-1z" class="D"></path><path d="M375 271l-3-5c1-5 1-6 5-9 0 3-1 3-3 5h2c2-2 2-2 5-2 2 0 4 1 5 4l-1 2c-1-1-2-1-3-2h-1-1l1-1v-1l-1-1-3 2c-1 1-2 3-2 5v3z" class="F"></path><path d="M394 277c-1 0-1 0-2-1 0-2-1-9-2-10-2-2-3-5-5-7-1 0-2 0-2-1v-1h2l1 2 2-1 1 2c1 0 2 1 2 2 1 1 2 1 3 2 1 4 2 8 1 12l-1 1z" class="L"></path><path d="M325 252c-10-13-22-22-35-32l29-1c3 0 8 0 11 1h0 1l1 1v2 3h0l-1-1-3-1c-6-1-13-1-20-1h-7l5 5 1-1h0c2 1 4 3 6 4 5 5 11 10 14 15 1 2 3 4 5 5l2 1c1 0 2 0 3 2l1 1c2 2 4 4 4 7 4 8 8 15 12 22 5 6 13 10 20 12 1 0 2 0 3-1 0 2 0 2-1 3h-4l-1-1h-3-1c-1 0-1 0-2-1l-2-1c-3-1-4-4-6-6-5-2-6-8-9-12l-3-4c-1-2-2-4-4-6l-6-9v1c-1 0-1 0-1 1-1 1-1 2-2 2-2-3-4-7-7-10z" class="U"></path><path d="M309 232h0l-2-2c-1 0-1-1-2-1-2-1-3-3-4-4-1 0-1 0-2-1l-1-1c-1 0 0 0-1-1h4c1-1 2 0 4 0 1-1 14-1 16-1l2 1h8v2l1-1v3h0l-1-1-3-1c-6-1-13-1-20-1h-7l5 5 3 3v1z" class="D"></path><path d="M306 228l1-1h0c2 1 4 3 6 4 5 5 11 10 14 15 1 2 3 4 5 5l2 1c1 0 2 0 3 2l1 1c2 2 4 4 4 7-2-2-3-6-6-8l-1 1s-1-1-2-1l1 2h-1v-1c-3-1-2-4-5-5l-2-2c-2-3-4-4-6-6l-3-3h-1c0-1-1-2-2-2l-5-5v-1l-3-3z" class="H"></path><path d="M277 207l27 2c7 0 14 0 20 1 2 0 3 0 4 2l1 1 1 3 1 4h-1 0c-3-1-8-1-11-1l-29 1c13 10 25 19 35 32l-1 1v-1c-2-1-4-2-5-4h0-1c-3-4-8-10-12-12-3-2-4-4-7-5-1 0-4-2-5-3l-10-7c-8-5-17-10-25-13 2 0 3 1 5 1l2-1 3 1v-1h1l-1 1h1 0c1-1 2 0 3 0h1v-1l3 1v-1-1z" class="O"></path><path d="M266 208l3 1v-1h1l-1 1h1 0c1-1 2 0 3 0h1c3 0 8 0 11 3h-9l-12-3 2-1z" class="U"></path><path d="M276 212h9l44 1 1 3h-2l-35-1c-4 0-7 0-11-1h-1c-2 0-3-1-5-2z" class="c"></path><defs><linearGradient id="AB" x1="293.924" y1="197.126" x2="312.076" y2="222.874" xlink:href="#B"><stop offset="0" stop-color="#b5b3b1"></stop><stop offset="1" stop-color="#eae9ed"></stop></linearGradient></defs><path fill="url(#AB)" d="M277 207l27 2c7 0 14 0 20 1 2 0 3 0 4 2l1 1-44-1c-3-3-8-3-11-3v-1l3 1v-1-1z"></path><path d="M662 334v-2-1c1 2 1 4 1 6 1 5 3 10 7 14l7 5c1 2 2 5 2 7h-1l-2-1-1 1c1 0 3 1 3 2 0 3-2 7-2 9h2l-1 2h2v5c-1 0-2 2-2 2 2 2 0 11 0 14-1 14-1 27 2 40 0 2 0 6 2 7l-1 2c-4 1-8 0-12 0-3 1-6 2-9 4v3 1c-5 6-10 14-13 22l-3 6-1-1-2 2c-1 3-2 7-4 10h-5c1 0 2-1 3-2h-1l-1-1c1-2 1-4 1-5-1 0-1-1-2-1l-1-2c-1-3-3-2-5-3h-1c-2-1-2-1-2-2 0-7 4-13 5-19l25-82h0c1-1 0-3 1-4 0-1-1-2-1-3v-4l3-11 4-22c1 1 1 2 2 3 0-1 0 0 1-1z" class="L"></path><path d="M664 349c1 0 2 1 2 2h2l1 2c2 4 5 6 7 9l-1 1-4-2c-3-4-6-8-7-12z" class="I"></path><path d="M670 351l7 5c1 2 2 5 2 7h-1l-2-1c-2-3-5-5-7-9l1-2z" class="S"></path><path d="M662 334v-2-1c1 2 1 4 1 6 1 5 3 10 7 14l-1 2-1-2h-2c0-1-1-2-2-2-2-3-2-6-2-9h0v-6z" class="P"></path><path d="M659 332c1 1 1 2 2 3 0-1 0 0 1-1v6h0-1l-9 36h0c1-1 0-3 1-4 0-1-1-2-1-3v-4l3-11 4-22z" class="V"></path><path d="M647 410c0-2 0-3 1-5h1v-1-1-2h1v-1-1-3l1-1c0-3 1-6 2-9v-4h0c1-2 1-2 1-3 1-2 0-4 1-5v-1c1-1 0-2 1-2 1-3 2-5 2-8 1-2 2-6 4-7 1 1 3 2 4 3 2 1 3 3 5 4l3 4c0 2 0 4-1 6v2l-2 2 1-7c-2-1-4-2-5-3l-2-2c-1-1-1-1-2-1-2 1-2 4-3 6-2 4-2 8-4 11-1 1-1 1-1 2v1h-1v2 1c-1 1-1 3-1 4 0 2-1 3-1 4l-2 11c-1 2-2 3-3 4z" class="B"></path><path d="M656 381c2-3 2-7 4-11 1-2 1-5 3-6 1 0 1 0 2 1l2 2c1 1 3 2 5 3l-1 7c-5 10-15 16-17 27-1 0-1 0-2-1 1-2 2-4 2-5 1-6 1-9 3-14v-1l-1-2z" class="R"></path><path d="M657 383h0c2-2 2-5 3-7l1-4 2-2v-1h1c1 0 0 1 1 1 0 1 2 1 3 2s1 1 2 3c-2 3-4 5-6 8 0 1-1 2-1 3-2 2-4 3-6 5 0 1 0 2-1 3v1 2l-1-1 1-3v-2c1-2 1-5 1-7v-1z" class="W"></path><path d="M650 406l2-11c0-1 1-2 1-4 0-1 0-3 1-4v-1-2h1v-1c0-1 0-1 1-2l1 2v1c-2 5-2 8-3 14 0 1-1 3-2 5 1 1 1 1 2 1 0 1 0 2-1 3-2 7-1 16-1 24 0 3 1 5 0 8v1c-1 2-3 3-5 5h-4c-1 1 0 2-2 1l-1 2v-1 2c2 2 5 2 7 3v1h1c0 3-4 6-5 9-3 7-8 15-10 23-1 0-1-1-2-1l-1-2c-1-3-3-2-5-3 1-6 2-12 4-17 2-4 2-7 4-10 0-2 1-4 2-6l5-14 7-22c1-1 2-2 3-4z" class="M"></path><path d="M636 456c0 1 0 2-1 3-1 2-2 6-1 9 0 1-1 3-2 5l-1 3-1-1c0-3 1-6 2-8 1-4 2-7 4-11zm3-9c0-3 0-5 1-7 1-1 1-3 1-4l3-9c1-2 2-4 4-7-1 2-1 3-1 4l-4 16c-1 2-2 3-2 5v1l-1 2v-1h-1z" class="a"></path><path d="M639 447h1v2 1c1 1 2 2 3 2-4 3-7 11-9 16-1-3 0-7 1-9 1-1 1-2 1-3 1-2 1-3 1-5 1 0 1 0 1-1s0-2 1-3z" class="T"></path><path d="M647 424v1c1 2 1 5 1 7h2 0l2-1c0 3 1 5 0 8v1c-1 2-3 3-5 5h-4c-1 1 0 2-2 1v-1c0-2 1-3 2-5l4-16z" class="D"></path><path d="M643 440l1 1c1 1 1 1 1 3h-2c-1 1-1 0-2 1 0-2 1-3 2-5z" class="P"></path><defs><linearGradient id="AC" x1="647.773" y1="409.013" x2="652.227" y2="419.987" xlink:href="#B"><stop offset="0" stop-color="#6a6868"></stop><stop offset="1" stop-color="#8d8b8e"></stop></linearGradient></defs><path fill="url(#AC)" d="M650 406l2-11c0-1 1-2 1-4 0-1 0-3 1-4v-1-2h1v-1c0-1 0-1 1-2l1 2v1c-2 5-2 8-3 14 0 1-1 3-2 5 1 1 1 1 2 1 0 1 0 2-1 3-2 7-1 16-1 24l-2 1h0-2c0-2 0-5-1-7v-1c0-1 0-2 1-4-1-4 1-9 2-14z"></path><path d="M640 449c2 2 5 2 7 3v1h1c0 3-4 6-5 9-3 7-8 15-10 23-1 0-1-1-2-1l-1-2c-1-3-3-2-5-3 1-6 2-12 4-17 2-4 2-7 4-10 0 3-2 7-3 10 0 3-1 5-1 8-1 3-1 5 0 8h2v-2l1-3c1-2 2-4 2-5 2-5 5-13 9-16-1 0-2-1-3-2v-1z" class="c"></path><path d="M643 452c1 1 2 1 3 2-2 3-4 6-6 10-1 1-1 3-1 4-1 2-3 3-3 5-1 1-2 3-3 5v-4l-1-1c1-2 2-4 2-5 2-5 5-13 9-16z" class="X"></path><path d="M671 361l4 2c1 0 3 1 3 2 0 3-2 7-2 9h2l-1 2h2v5c-1 0-2 2-2 2 2 2 0 11 0 14-1 14-1 27 2 40 0 2 0 6 2 7l-1 2c-4 1-8 0-12 0-3 1-6 2-9 4v3 1c-5 6-10 14-13 22l-3 6-1-1-2 2c-1 3-2 7-4 10h-5c1 0 2-1 3-2h-1l-1-1c1-2 1-4 1-5 2-8 7-16 10-23 1-3 5-6 5-9h-1v-1c-2-1-5-1-7-3v-2 1l1-2c2 1 1 0 2-1h4c2-2 4-3 5-5v-1c1-3 0-5 0-8 0-8-1-17 1-24 1-1 1-2 1-3 2-11 12-17 17-27l2-2v-2c1-2 1-4 1-6l-3-4v-2z" class="G"></path><path d="M648 449c7-2 10-6 14-13v1c-1 2-1 3-2 5l2-2 1-1c-2 5-7 10-12 12-1 0-2 0-4 1-2-1-5-1-7-3v-2 1c2 1 3 2 5 2 0 0 3 0 3-1z" class="C"></path><path d="M659 450v3 1c-5 6-10 14-13 22l-3 6-1-1-2 2c-1 3-2 7-4 10h-5c1 0 2-1 3-2 1-2 2-6 3-8 5-11 11-25 21-31l1-2z" class="U"></path><path d="M659 450v3 1c-5 6-10 14-13 22l-3 6-1-1-2 2c3-7 6-14 10-20l1-1c2-3 6-7 7-10l1-2z" class="H"></path><path d="M664 404c1-2 2-4 4-4 1 0 2 0 3 2 2 3 2 7 2 10 0 4 0 9-1 13h-1l-2 2c-1 0-2-1-3-2v-1l-2-2c-2-4-2-6-2-9 0-4 1-6 2-9z" class="c"></path><path d="M662 413c0-3 1-5 3-6l2-2c2 1 2 4 3 6 0 4 0 9-3 13l-1 1v-1l-2-2c-2-4-2-6-2-9z" class="R"></path><path d="M664 422c0-3 0-6 1-9 1-1 1-2 2-3v-1c1 1 1 3 1 5 1 1 1 0 1 2l-1 1v1h0c0 1-1 2-1 2v1 2l-1 1-2-2z" class="W"></path><path d="M677 376h2v5c-1 0-2 2-2 2-2 5-8 9-12 13 0 2 0 3-1 4l-1 1c-1 1-2 5-3 7h1 1v-1l1-1h-1l2-2c-1 3-2 5-2 9 0 3 0 5 2 9l2 2v1c1 1 2 2 3 2l2-2c0 3 0 4-2 5l-4 2c0 2-1 5-2 7l-1 1-2 2c1-2 1-3 2-5v-1c0-5-4-10-6-15-2-8-1-16 3-23 4-6 9-9 13-15 2-2 4-5 5-7z" class="J"></path><path d="M665 396c0 2 0 3-1 4l-1 1c-1 1-2 5-3 7h1 1v-1l1-1h-1l2-2c-1 3-2 5-2 9 0 3 0 5 2 9l2 2v1c1 1 2 2 3 2-1 1-3 2-4 3-4-3-6-11-6-15-1-7 2-13 6-19z" class="B"></path><defs><linearGradient id="AD" x1="676.867" y1="364.767" x2="652.43" y2="398.407" xlink:href="#B"><stop offset="0" stop-color="#0f0e0e"></stop><stop offset="1" stop-color="#363434"></stop></linearGradient></defs><path fill="url(#AD)" d="M671 361l4 2c1 0 3 1 3 2 0 3-2 7-2 9h2l-1 2c-1 2-3 5-5 7-4 6-9 9-13 15-4 7-5 15-3 23 2 5 6 10 6 15-4 7-7 11-14 13 0 1-3 1-3 1-2 0-3-1-5-2l1-2c2 1 1 0 2-1h4c2-2 4-3 5-5v-1c1-3 0-5 0-8 0-8-1-17 1-24 1-1 1-2 1-3 2-11 12-17 17-27l2-2v-2c1-2 1-4 1-6l-3-4v-2z"></path><path d="M648 449l-1-1c3-1 5-2 7-3 0-2 2-3 3-5 0-1 1-4 2-5 0-2-2-6-3-8 0-2-1-4 0-6 2 5 6 10 6 15-4 7-7 11-14 13z" class="Q"></path></svg>