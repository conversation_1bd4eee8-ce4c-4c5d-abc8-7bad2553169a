<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="96 41 388 640"><!--oldViewBox="0 0 552 752"--><style>.B{fill:#353435}.C{fill:#232223}.D{fill:#959394}.E{fill:#2a292a}.F{fill:#3c3b3c}.G{fill:#4b4a4b}.H{fill:#1d1c1c}.I{fill:#f6f5f6}.J{fill:#848283}.K{fill:#2f2e2f}.L{fill:#757475}.M{fill:#676666}.N{fill:#abaaaa}.O{fill:#464545}.P{fill:#2a2a2a}.Q{fill:#515050}.R{fill:#5b5a5b}.S{fill:#424142}.T{fill:#565556}.U{fill:#151415}.V{fill:#bbb9ba}.W{fill:#6f6e6e}.X{fill:#c1c0c0}.Y{fill:#5e5e5e}.Z{fill:#e1e0e1}.a{fill:#8d8d8c}.b{fill:#c9c8c9}.c{fill:#dad8d9}.d{fill:#d1cfd0}.e{fill:#7d7c7d}.f{fill:#a3a1a2}.g{fill:#9d9b9d}.h{fill:#b1afb2}.i{fill:#181818}</style><path d="M190 227l-1-2 2-1v3h-1z" class="B"></path><path d="M404 200h1c1 0 1 0 1 1l-1 1h-1c-1-1-1-1 0-2zm-78 192c1 0 1 0 2 1 0 1 0 1-1 2h-1c-1-1 0-1-1-2l1-1z" class="I"></path><path d="M259 326h1c0 1 1 1 1 1l3 1h-2c-1 1-1 1-2 1-1-1-1-2-1-3z" class="C"></path><path d="M257 286h3c-1 1-2 3-3 4v-2-2z" class="P"></path><path d="M177 255v4c1 0 2 1 2 1l-1 2h0c-2-2-1-5-1-7z" class="B"></path><path d="M331 185h2v2h-1c-1 1-1 0-2 0v-1l1-1zm-27 274h1l1 2-1 1-1-1c-1 0-1 0-1-1l1-1zM130 345h1v3h-1-1v-2l1-1z" class="I"></path><path d="M276 273l2 2-2 1-2-1 2-2z" class="J"></path><path d="M276 273l2-2c1 1 1 1 3 1l-1 1-2 2-2-2z" class="D"></path><path d="M77 192h2v2h-1c-1 0-2 0-2-1v-1h1z" class="I"></path><path d="M206 374c1-1 1-2 3-3l-1 5h-1c-1 0 0-1-1-2z" class="P"></path><path d="M128 415c1 0 1 0 2 1 0 1 0 2-1 2h-1c-1-1-1-1-1-2l1-1zm259-143h1c1 1 1 1 1 2l-1 1h-1l-1-1c0-1 0-1 1-2z" class="I"></path><path d="M251 351l3-3v1l1-1h0c-1 2-1 3-1 5l-2-1c0-1 0-1-1-1z" class="C"></path><path d="M120 303c1 0 1 0 2 1 0 1 0 1-1 2l-1 1c-1-1-1-1-1-3l1-1z" class="I"></path><path d="M261 515c2-1 3 0 5 0v1c1 0 1 0 1 1h3-6c-1-1-2-1-3-2z" class="B"></path><path d="M197 148l2-1c1 0 1 1 2 2v1c-1 1-1 2-1 3l-1-2 1-1c-1-1-2-1-3-2z" class="K"></path><path d="M198 168s1 0 1-1c0 0 1-1 2-1v2s-1 1-1 2c0 0 1 0 0 1h-1c-1-1-1-2-1-3z" class="F"></path><path d="M270 279l4-4 2 1-5 4-1-1z" class="L"></path><path d="M197 206c2-2 2-2 4-3 0 1 0 3-1 4-1 0-1 0-3 1v-2z" class="C"></path><path d="M69 251l1-1c0 1 1 1 1 2 0 2 0 2-1 2l-1 1c-1-1-1-1-1-2s0-1 1-2z" class="I"></path><path d="M190 230c1 0 2 0 3-1 0 0 1 0 1-1s-1-2 0-3h1v3c0 1-1 2-1 3h-2l-1 1s-1-1-1-2z" class="C"></path><path d="M201 194v8l-1-1h-1c-1-1-1-2-1-3l-1-1v-1c2 0 2 0 4-2z" class="E"></path><path d="M138 106c-3 0-5-1-7-2 3-1 5-1 7-1 1 0 1 1 2 1v1 1l-2-2v2zm59 91l1 1c0 1 0 2 1 3h1l1 1v1c-2 1-2 1-4 3v-9z" class="F"></path><path d="M379 353v1c1 2 1 3 1 4-1 0-2 1-3 0h-1l1-1c-1 0-1 0-1-1v-1c1-2 2-2 3-2z" class="Z"></path><path d="M278 275l2-2c-1 2-4 5-6 6h2l-3 3c-1 0-1 0-1 1l-2-2 1-1 5-4 2-1z" class="B"></path><path d="M325 238h1l2 2c-1 2-1 2-2 3-1 0-2-1-2-1-1-1-1-1-1-2 1-2 1-2 2-2z" class="I"></path><path d="M266 310l1 1h1c0 4 2 7 4 9l-2-1c-1 0-1-2-2-2v-2 1s-1 0 0 1h0l1 1v1h0 0v2c-1-2-2-4-2-6-1-1-2-1-2-2 1-1 1-2 1-3z" class="B"></path><path d="M194 151c0-1 1-3 2-3h1c1 1 2 1 3 2l-1 1c-1 1-1 2-2 3h-1c1-1 1-1 1-2-1 0-2-1-3-1z" class="E"></path><path d="M274 420c1 1 1 4 1 5 1 2 0 13 0 15v-8-3l-1-2h0-1l-1-2h0c1-1 1-2 2-3v-2z" class="C"></path><path d="M274 422v5h-1l-1-2h0c1-1 1-2 2-3z" class="G"></path><path d="M270 369h3v1c0 1 1 1 1 2 0 0 0 2-1 2 0 1 0 1-1 2h-1v-2c0-2 0-3-1-5h0z" class="R"></path><path d="M250 319l1-1-1-2h0c1 1 2 1 3 2h1c1 0 1 0 1 1 1 1 2 1 2 2v1 1l-2-2h-2c-1 0-2-2-3-2z" class="C"></path><path d="M219 88h6v3h-6-2l1-3h1z" class="S"></path><path d="M218 88h1l3 1v1c-1 1-2 1-3 1h-2l1-3z" class="a"></path><path d="M153 109l1-1c0-1 1 0 1 0h0v2l1 1 2-2 1 2v3h-1 0v-3h-1l-2 3h-1c-1-1 0-1 0-1v-1c-1-1 0-2-1-3z" class="M"></path><path d="M224 326c1 0 1-1 2 0h3v-1c-1 0-1-1-2-1 0 1-1 1-1 2v-1h-1l2-2c1 0 2 1 3 2h0v1l1-1s1 0 1 1h4c1-1 2-2 3-2 1 1 1 2 2 2s4-1 4 0c-2 0-5 0-8 1h-2-2-1c-1 0-1-1-1-1-2-1-5 0-7 0z" class="C"></path><path d="M178 130l-4-2c0 1 0 1-1 1l-1-1c-1 0-1 0-1 1h-1 0l-1-1 1-1c2-1 4 0 7 0 1 0 3 0 4 1h0c-1 1-1 1-3 2z" class="h"></path><path d="M410 354h1c0 1 0 2-1 4h-6-1v-2c0-1 3 0 4-1l1-1h0 2z" class="Z"></path><path d="M272 376h2v17h-2l1-2c-1-3 0-6 0-8l-1-1c1 0 1-1 1-1l-1-2c0-1 0-1 1-2l-1-1z" class="G"></path><path d="M386 183c1 0 2 0 2 1 1 0 2 1 2 2-1 1-1 2-2 2h-2c-1 0-1 0-2-1v-2c1-1 1-2 2-2z" class="I"></path><path d="M256 305v7h0v2l-2 3v1h-1c-1-1-2-1-3-2 3 0 1-3 2-4 1 0 2 0 3-1 0 0 0-1 1-2 0-1-1-2 0-4z" class="B"></path><path d="M238 333v1h0v10c-2-2 0-6-1-8-1 0 0 1-1 2v2c-1 1-2 0-3 1v1-2h-1v-1c1 0 1 0 2-1l1-1h0c0-1 1-1 1-2h0l2-2z" class="P"></path><path d="M336 420c1-1 1-1 2 0s1 2 1 3c-1 2-1 2-2 2-2 0-2 0-3-1s-1-1-1-3c1-1 1-1 3-1z" class="I"></path><path d="M207 526c-1-13 0-27 0-40 0-2 0-7 1-9v13c-1 6-1 12-1 18v13l2-1c0 2-1 4 0 5-1 1-1 2-1 3l-1-2z" class="F"></path><path d="M268 361l2 2c1 0 1 1 2 1 1 1 1 3 1 5h-3c0-2-2-3-3-4v-2h0l1-1v-1z" class="P"></path><path d="M252 622h0l4-16v5c0-1 0-2 1-3l-6 21c0-2 0-5 1-7z" class="D"></path><path d="M282 250v1c1 3 1 7 1 11 0 2-1 3 0 5 1 0 2 1 3 1-1 2-4 4-5 4-2 0-2 0-3-1l4-4c1-2 0-14 0-17z" class="N"></path><path d="M267 344c4-2 8-3 12-3l-2 3h0c0-1 0-1-1-2h-2c-1 0-2 0-2 1h-1c-1 0-1 0-2 1-2 1-4 2-5 3l-1 1c-1 0-1 1-2 2h0 0c1-1 1 0 1-1 1 0 2-2 3-1l-5 4h-1v-2c2-1 3-2 4-3s3-2 4-3z" class="E"></path><path d="M177 215c2 3 4 5 5 8 0 1 0 0 1 1 0 1 0 1 1 2h1 1v3h1l1-1v2s0 1 1 1c0 1 1 1 1 2-1 1-3 0-4 0v-1c-1 0-2-4-3-5l-6-12z" class="C"></path><path d="M183 227l2-1v1s0 1 1 2c0 0 1 0 1 2h-1v1c-1 0-2-4-3-5z" class="B"></path><path d="M207 227v-13 1l-1-1c0-1-1-2 0-3 2-8 0-17 1-26l1 3v10c0 2-1 4 0 6s0 3 1 5c-1 1-1 2-1 4-1 5 0 10-1 14z" class="F"></path><path d="M379 354h1c0-1 1-3 2-3v3c1 0 1 0 1-1h4l-2 1v4c0 1 1 1 1 1v2h0c-1-1-3-2-3-4h0c-1 0-1 2-2 3 0 0 0-1-1-2h0 0c0-1 0-2-1-4z" class="b"></path><path d="M256 472l-2 2c-1 1-2 3-3 4 1 0 0-1 2 0v-2s1 0 1-1h1c-1 2-1 3-2 4 0 1-1 2-1 2h0v-2c-1 0-4 4-4 5-1 1-2 2-2 4v1c-1-1-2-2-2-3l1-2 1-1 3-4h0l3-4 4-3z" class="B"></path><path d="M402 354h0c2-5 3-8 6-12-1 4-4 7-4 11 2 1 4 0 6 1h-2 0l-1 1c-1 1-4 0-4 1v2h1 0c0 3 1 6 2 8s2 4 2 6c-2-3-3-5-4-8 0-1-1-5-2-6v-4z" class="b"></path><path d="M200 153c0-1 0-2 1-3l1 1h2c1 0 1 0 2 1l3 3-1 1v-2c-1 0-1 0-1 1-2 1-3 1-5 1l-1 1c0-1-1-1-2-1h0 0c0-1 0-2 1-3z" class="P"></path><path d="M202 153c1 0 2 0 3 1-1 0-2 1-4 1h0l1-2z" class="S"></path><path d="M204 151c1 0 1 0 2 1 0 0-1 1-1 2-1-1-2-1-3-1l2-2z" class="B"></path><path d="M200 153c0-1 0-2 1-3l1 1h2l-2 2-1 2-1 1h-1c0-1 0-2 1-3z" class="C"></path><path d="M268 311c0 2 2 3 3 4s1 2 2 3h1c1-1 1-2 1-3 1 1 1 2 3 3l1 1-1 2h-2l2 2v1c-3-1-4-2-6-4s-4-5-4-9z" class="h"></path><path d="M275 315c1 1 1 2 3 3-1 1 0 1-1 2-1 0-2-1-4-2h1c1-1 1-2 1-3z" class="V"></path><path d="M197 196c-1 0-1-1-2-2 0-3 1-5 2-8 0-1 0-3 1-4 0 1 0 1 1 2 1 0 2 1 2 1 1 1 1 1 1 2l-1 1-2-3c-1 3-3 7-2 10 3 0 3-4 5-5h0v2l-1 2c-2 2-2 2-4 2z" class="R"></path><path d="M269 500s3-1 3-2c1-1 0-3 0-5h0c1 1 1 2 1 3-1 1 0 2-1 3 0 0 1 0 1 1v3c-1 1-1 3-1 4h1c0 2-1 7 0 9 0 1 0 2-1 3-1-1-1-1-2-1v-1h-3c0-1 0-1-1-1v-1h7c-2-1-4-1-6-2h-1v-1c2 0 3 1 5 1 1-1 1-3 1-4v-9h-1-1-1z" class="O"></path><path d="M259 350c0-1-1-1-2-2l1-1c4-4 8-7 13-8-1 1-3 2-4 4v1c-1 1-3 2-4 3s-2 2-4 3z" class="c"></path><path d="M219 311v-2h1 2v1h0c0 1 0 2-1 2v3c0 1 1 1 1 2l1 1-2 1c0 1 0 1 1 2 0 1 0 2-1 4 0-1-1-1-1-1h0c-1-1-1-2-1-4h0v-1-1c0-1 0-2 1-4-1 0-2-1-3-1l2-1v-1z" class="B"></path><path d="M219 318h0c1 0 2 0 2-1h1c-1 2-2 2-2 4h0 1 1c0 1 0 2-1 4 0-1-1-1-1-1h0c-1-1-1-2-1-4h0v-1-1z" class="Q"></path><path d="M209 155v2h0l-2 2c1 1 1 1 2 3h-1c0 1 0 1-1 2 0 1-1 2-2 2v1h-3l-1 1c1 2 2 1 2 3h-3c1-1 0-1 0-1 0-1 1-2 1-2 0-1 0-1 1-1s1-1 1-1v-1c1-1 1-3 2-4 0-2 0-1 1-3l-1-1c1 0 2-1 3-1l1-1z" class="C"></path><defs><linearGradient id="A" x1="285.069" y1="323.946" x2="277.659" y2="321.271" xlink:href="#B"><stop offset="0" stop-color="#838283"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M279 319c2 2 5 3 8 3h1v3h-4-3c-1 0-2-1-3-1v-1l-2-2h2l1-2z"></path><path d="M287 322h1v3h-4-3 3c2-1 2-1 3-3z" class="L"></path><defs><linearGradient id="C" x1="216.67" y1="89.887" x2="207.151" y2="89.361" xlink:href="#B"><stop offset="0" stop-color="#949495"></stop><stop offset="1" stop-color="#c6c6c7"></stop></linearGradient></defs><path fill="url(#C)" d="M203 88h3 12l-1 3h-15c1 0 2 0 3-1 0-1 0-1-1-1 0-1-1-1-1-1z"></path><path d="M203 88h3c0 1 0 2-1 3h0v-1c0-1 0-1-1-1 0-1-1-1-1-1z" class="c"></path><path d="M387 353v1c2-1 4-5 5-7h0c0 1-2 5-1 6l1 1v1 2c0 1-1 1-1 2 0 2 2 5 2 7-2-2-2-5-4-7h-3s-1 0-1-1v-4l2-1z" class="I"></path><path d="M202 156c2 0 3 0 5-1 0-1 0-1 1-1v2c-1 0-2 1-3 1l1 1c-1 2-1 1-1 3-1 1-1 3-2 4v1s0 1-1 1-1 0-1 1v-2h0c-1-2-1-3 0-5 0 0 0-1-1-1v-3h-1-1l1-1h0c1 0 2 0 2 1l1-1z" class="K"></path><path d="M199 157h-1l1-1h0c1 0 2 0 2 1l1-1 2 1v1h0c-1 1-2 1-2 3h-1s0-1-1-1v-3h-1z" class="G"></path><path d="M392 355v1c1 0 1-2 2-3 2 0 2 0 3 1 2-1 3-1 5 0v4c-2 1-5 1-7 1-1 0-3-1-4 0 0-1 1-1 1-2v-2z" class="Z"></path><path d="M208 490h0v-5c1 2 1 5 1 7v28l-2 1v-13c0-6 0-12 1-18z" class="T"></path><path d="M179 117v-6c0-4 1-8 4-10 5-5 13-6 19-6-3 2-7 2-10 3-5 0-7 2-10 6l-1 2c-1 1-1 2-1 4v1h2v2h1v1h-2c-1 1-1 2-2 3z" class="W"></path><path d="M180 111h2v2h-2v-2z" class="H"></path><path d="M275 495v5c0 9 0 16-1 25l-1-5v-3h0v-2-2-8-4c0-2 0-3 1-5v4h1v-5z" class="O"></path><path d="M258 315v-3-2-3c0-4 0-7 1-11v4h1l1 2h-1c1 1 1 1 1 2 0-1 1-1 1-2h1v1 1l1 1h-1v3h1c-1 2-2 3-3 4h-1l-1 1v1l-1 1z" class="T"></path><path d="M261 304c0-1 1-1 1-2h1v1 1l1 1h-1v3h1c-1 2-2 3-3 4h-1v-1c1 0 1 0 1-1h-1l1-1c0-2-1-1-2-3h1v-2h1z" class="L"></path><path d="M261 304c0-1 1-1 1-2h1v1 1l1 1h-1-2v-1z" class="J"></path><path d="M188 228l2 2c0 1 1 2 1 2v2c-2 0-2 0-3 2h1c1 1 1 0 2 1v1h-1c0 2 0 2 1 3h2l1 1c0 1 0 1-1 2 0 1 1 1 1 2v1l-2-1c-1 1-1 1-1 2-1 1-1 1-1 2h1c0 2 0 2-1 3v-1-1l-1 1v-1h-1l-1-1 3-3-1-1 1-2c1 0 1 0 1-1-1-1-1-2-2-3 0-1-1-2-1-2-1-1-1-2-1-3h0c0-1 0-1-1-2 1 0 3 1 4 0 0-1-1-1-1-2-1 0-1-1-1-1v-2z" class="B"></path><path d="M246 350l3-1c1 0 1 1 2 2 1 0 1 0 1 1l2 1c1 1 3 2 4 3 1 0 1 0 2-1h1v1h-1l-1 1c1 0 1 1 2 1v1h-1s3 2 4 3h0l2-1c0-1-1-1-2-3h1c1 1 1 1 2 1h0 4 6-9v2 1l-1 1h0v2c-4-3-8-7-13-9-3-2-5-4-8-6z" class="K"></path><path d="M272 393h2v27 2c-1 1-1 2-2 3v-3c1-1 1-3 1-4 0-4-1-8-1-12v-4-2c0-2 1-5 0-7z" class="T"></path><path d="M194 151c1 0 2 1 3 1 0 1 0 1-1 2h1c1-1 1-2 2-3l1 2c-1 1-1 2-1 3h0l-1 1h1 1v3c1 0 1 1 1 1-1 2-1 3 0 5h0c-1 0-2 1-2 1 0 1-1 1-1 1 0-2 0-4-1-6l-1-3-1-3c-1-1-1-4-1-5z" class="M"></path><path d="M195 156v-1l3 2v1c-1 0-1 1-2 1l-1-3z" class="J"></path><path d="M199 157h1v3 1h-2v-1l1-3z" class="D"></path><defs><linearGradient id="D" x1="221.169" y1="578.596" x2="219.549" y2="591.998" xlink:href="#B"><stop offset="0" stop-color="#525453"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#D)" d="M215 565c1 1 1 3 1 4h1 1l1 1v2c-1 0-1 0-1 1h1l-1 1c1 2 2 5 3 7v3l1 1c1 0 1 0 1 1-1 1-1 1-1 2 1 1 0 1 0 2 1 1 1 2 1 4v2c1 1 2 7 1 8h0l-9-39z"></path><path d="M138 103l10 1h4s0 1 1 1c1 1 1 1 1 2h1c1 0 2 1 3 1v1l-2 2-1-1v-2h0s-1-1-1 0l-1 1v-1c-1-1-2 0-3-1h-6c-2 0-4 0-6-1v-2l2 2v-1-1c-1 0-1-1-2-1z" class="B"></path><defs><linearGradient id="E" x1="291.477" y1="325.541" x2="289.182" y2="316.298" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#6a6969"></stop></linearGradient></defs><path fill="url(#E)" d="M287 313l2 1v1h0 2l1 1h0s0-1 1-1v5h0l2 6c-3 0-8 0-11-1h4v-3c1-1 0-4-1-6v-3z"></path><path d="M293 315v5h0 0c-1 0-2-1-2-1-1-1-2-3-2-4h2l1 1h0s0-1 1-1z" class="J"></path><path d="M270 279l1 1-1 1 2 2c-1 0-2 2-2 3h0-2-1c0 1-1 1-2 1 0 1-1 2-1 3 0 0-1 0-2 1 0 2 0 6-2 8l1 2v1l-1-2h-1v-4-1c0-2 0-3 1-5 0-1 2-2 2-3 3-3 5-6 8-8z" class="R"></path><path d="M270 281l2 2c-1 0-2 2-2 3h0-2-1c0 1-1 1-2 1l-1-1c2-2 4-3 6-5z" class="E"></path><path d="M209 371c5-7 13-13 20-18 3-2 6-4 10-5v1l-10 6c-2 2-4 3-6 4a30.44 30.44 0 0 0-8 8l-1 1-2 5v-1l1-5c-2 3-3 5-4 8l-1 1 1-5z" class="M"></path><path d="M268 353h2 0 3l-1 1 1 1h0s1 0 1 1 0 1 1 2c1 0 3 1 4 0 0-2 0-3 1-4v3h0l-1 1h1v4c-1-1-1-2-1-3h-2-6-4 0c-1 0-1 0-2-1h-1-1c-1-2-1-2 0-3v-1c2-1 3-1 5-1z" class="b"></path><path d="M268 353h2 0c-1 0-1 0-1 1s1 1 1 2l-1 1 2 2h-4v-1c1-1 0-2 0-3s0-1 1-2z" class="V"></path><path d="M263 354c2-1 3-1 5-1-1 1-1 1-1 2s1 2 0 3v1h0c-1 0-1 0-2-1h-1-1c-1-2-1-2 0-3v-1z" class="M"></path><path d="M263 355h1c0 1 1 1 2 2l-1 1h-1-1c-1-2-1-2 0-3z" class="a"></path><path d="M267 315c0 2 1 4 2 6h0c1 2 2 2 1 4s-3 2-5 2c0 1 0 1-1 1l-3-1s-1 0-1-1h0 2v1c1-1 1-2 1-3h0c-1-1-1-2-1-2v-1h0v-2h-1v-1c1 0 2 0 3-1h0 0 2l1-2z" class="J"></path><path d="M262 321c1 1 2 1 2 1v1l2 1h2c-1 2-3 1-4 2v1h-3s-1 0-1-1h0 2v1c1-1 1-2 1-3h0c-1-1-1-2-1-2v-1z" class="D"></path><path d="M267 315c0 2 1 4 2 6h0-2c-1 0-1-1-2-1h0-1v-1c-1 0-1 1-2 2v-2h-1v-1c1 0 2 0 3-1h0 0 2l1-2z" class="M"></path><path d="M239 349h1v1h-1c-1 0-3 3-4 3-1 2-4 4-6 5l-4 4c-2 1-3 2-4 3l-3 3c0-1-1-1-1-1l-2 1h-1 0l1-1a30.44 30.44 0 0 1 8-8c2-1 4-2 6-4l10-6z" class="K"></path><path d="M206 374c1 1 0 2 1 2 1 3 0 6 0 8 0 8 0 15-2 23-2-6-1-14-1-21 1-4 0-8 2-12z" class="B"></path><path d="M120 234h2c1 0 3 0 4 1s1 3 1 5-1 3-3 4h-2c-2 0-3-1-4-2s-1-3 0-5c0-1 1-2 2-3z" class="I"></path><defs><linearGradient id="F" x1="217.327" y1="574.454" x2="226.468" y2="577.341" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#F)" d="M219 570l-1-1 2-2h1v-2h0 1c1 5 3 10 3 15v1 2h0 0-2l-1-1h0v3l-1-1v-3c-1-2-2-5-3-7l1-1h-1c0-1 0-1 1-1v-2z"></path><path d="M180 200v-2c0-1 1-1 2-2 1 0 2 1 2 2h1c1 0 0-1 1-1h1c1 1 1 2 3 3 1 1 1 1 1 3h0c0 1 0 1 1 2-1 1-1 1-2 1h1v1 4c-1-1-2-1-3-1v-1l1-1v-1-2h0c0-1 0-1-1-2s-2 0-3 0h-2 0v1 1c-1-1-2 0-3-1 0-1 0-1-1-2h0l1-2z" class="O"></path><path d="M180 200v-2c0-1 1-1 2-2 1 0 2 1 2 2h-1-1l1 1h1c1 0 2 1 2 1 0 1 0 1-1 1h-3l-1-1h-1z" class="H"></path><path d="M180 200h1l1 1h3v2h-2 0v1 1c-1-1-2 0-3-1 0-1 0-1-1-2h0l1-2z" class="P"></path><path d="M433 253c1 0 2 0 3 1 1 0 2 1 2 3 1 1 0 3 0 4-1 1-2 2-3 2-1 1-2 0-3 0s-2-1-3-2c0-2-1-3 0-5 1-1 2-2 4-3zm-210 92h1c-7 7-15 13-20 22v-1-1c1-1 0-1-1-2l-1 1v-1c3-5 8-10 13-13 3-2 6-3 8-5z" class="I"></path><path d="M275 314v-2c2 0 2 1 4 2 2 0 5 1 7 0 1 1 1 1 1 2 1 2 2 5 1 6h-1c-3 0-6-1-8-3l-1-1c-2-1-2-2-3-3v-1z" class="i"></path><defs><linearGradient id="G" x1="204.864" y1="414.288" x2="207.216" y2="441.72" xlink:href="#B"><stop offset="0" stop-color="#5c5b5d"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#G)" d="M205 421h0l1-8h0c2 3 0 6 1 9v5c0 2 1 4 1 7-1 1-1 1-1 3 3 5 10 8 14 12h-2l-3-3c-3-2-5-3-9-5v1h-1v3c-2-7-1-16-1-24z"></path><path d="M194 246l1 1-1 1c0 1 0 1 1 1h1v1c-1 2 0 4-1 5h-1 0c1 0 1 1 2 1l-1 1v6h0v-7h-1c-1 1 0 1 0 3v8c-1 1 0 2-1 3v1l-2-1h0c0-1 0-1 1-2l-1-1v-2c0-2 0-2 1-3-1-2-1-2-1-4v-1c-1 0-1-1-2-1v-1h2c-1-1-2-2-2-3l1-1v1 1c1-1 1-1 1-3h-1c0-1 0-1 1-2 0-1 0-1 1-2l2 1v-1z" class="H"></path><path d="M263 308c1 0 2-1 3-2v1 3c0 1 0 2-1 3 0 1 1 1 2 2l-1 2h-2 0 0c-1 1-2 1-3 1v1h1v2h0v1s0 1 1 2h0c0 1 0 2-1 3v-1h-2 0-1c0-2-1-3-1-5v-2c1-1 1-2 0-4l1-1v-1l1-1h1c1-1 2-2 3-4h-1z" class="L"></path><path d="M262 313c1 0 1 0 1 1s0 1-1 1c0 0 0 1-1 1h-1c1-2 1-2 2-3z" class="J"></path><path d="M263 308c1 0 2-1 3-2v1 3c0 1 0 2-1 3-1-1-1-1-2-1l-1 1c-1 1-1 1-2 3h0l-1-1h1l-1-1v-1l1-1h1c1-1 2-2 3-4h-1z" class="M"></path><path d="M263 273l1 1h1 0c0-1 1-2 2-2l1 1c-1 1-3 3-4 5 0 1 0 2-1 3h0 0v1l-3 4h-3v2 2c-2 3-1 7-1 10-1 0-1-1-1-2l-2 2-1-1h0c0-2 0-3-1-4 1-1 1-2 2-2l-1-1 4-6 1-1 2-2v-1h1v-1l2-3 1-2h-2v-1l1-1v-1h1z" class="G"></path><path d="M264 278c0 1 0 2-1 3h0 0v1l-3 4h-3c2-3 5-6 7-8z" class="K"></path><path d="M257 288v2c-2 3-1 7-1 10-1 0-1-1-1-2l-2 2-1-1s0-1 1-2h0c0-2 1-2 2-3v-1l-1-1c0-1 1-2 2-3 0 0 0-1 1-1z" class="F"></path><path d="M207 376h1l1-1c-1 13-1 26 0 39 0 2 0 5 1 7v1h-1c-2-2 0-6-2-8v8c-1-3 1-6-1-9h0l-1 8h0v-14c2-8 2-15 2-23 0-2 1-5 0-8z" class="T"></path><defs><linearGradient id="H" x1="261.753" y1="461.543" x2="250.128" y2="466.819" xlink:href="#B"><stop offset="0" stop-color="#2b2a2a"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#H)" d="M248 461c2 0 4 0 6-1l10-3v1c-1 1-2 1-2 2-3 4-4 8-6 11v1l-4 3v-2c1-1 1-1 1-2h0c1-1 1-1 1-2-1-3-3-5-6-7h0 1l-1-1z"></path><path d="M272 376h0l1 1c-1 1-1 1-1 2l1 2s0 1-1 1l1 1c0 2-1 5 0 8l-1 2c1 2 0 5 0 7v2 4c0 4 1 8 1 12 0 1 0 3-1 4v3h0l-1 1h-1-1c2-3 2-7 1-11 1-2 0-5 0-7v-11c1-2 1-5 1-7v-14h1z" class="M"></path><defs><linearGradient id="I" x1="196.951" y1="235.642" x2="208.336" y2="252.829" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#908f90"></stop></linearGradient></defs><path fill="url(#I)" d="M201 243c-1-3 0-6 0-9v-7-4c1 0 1-1 1-2h0c1 1 0 4 0 5-1 2 1 5 0 7h0l1-1 1-1v1h0v-4h1l-1-1v-7c1 0 1 0 1-1h0c1 1 0 5 0 7v3h0v5h0v7c-1 3 0 7-1 10v2h-1v4-2c-1 2-2 3-2 4v-16z"></path><path d="M282 250v-13-3c1 1 1 2 2 3s2 3 2 3v6 14 8c-1 0-2-1-3-1-1-2 0-3 0-5 0-4 0-8-1-11v-1zm140 51l26 2c-2 1-4 1-6 2l-20 5v-9z" class="I"></path><path d="M235 328h4c1 1 1 2 1 3h0v10c-1-2-1-6-1-9l-1 1-2 2h0c0 1-1 1-1 2h0l-1 1c-1 1-1 1-2 1v1h1v2h-1l-1-2h-1l-2 2c-1 0-1 1-2 2s-1 1-2 1h-1l2-2c1-2 2-3 4-4v-1c-1-1-1-1-1-2v-1h-1c-1 2-2 3-4 3h0c1-1 1-1 1-2 1-2 2-2 4-3 2 0 2-1 3-1l2-1c-1-1-1-1-1-2 1 0 2 0 3-1h0z" class="F"></path><path d="M233 331v-1h1 2v1c-1 1-2 2-3 4-1 1-2 1-2 2l-2 2v-1c-1-1-1-1-1-2v-1h-1c-1 2-2 3-4 3h0c1-1 1-1 1-2 1-2 2-2 4-3 2 0 2-1 3-1l2-1z" class="P"></path><path d="M231 332c1 0 1 1 0 1 0 1 0 1-1 2h-1l-1-2c2 0 2-1 3-1z" class="i"></path><path d="M245 326h6 4 1c-2 2-8 2-10 2h-1v1c1 3 7 6 5 9-1-1-2-3-3-3v1c1 1 2 3 2 4s1 1 1 2v1h-1c-1 0 0 0-1-1h-1-2c0-1-1-2-1-2v-3l-1-1c0-1 0-3-1-5v-1h0-1c1 0 1 0 1 1s1 9 0 11c-2-2-1-8-2-11h0c0-1 0-2-1-3h-4v-1h2c3-1 6-1 8-1z" class="Q"></path><path d="M244 337h0v-6c-1-1-1-2-1-3h1v1c0 1 1 2 1 3 1 2 0 4 1 7h1v-1c1 1 1 3 2 4v1c-1 0 0 0-1-1h-1-2c0-1-1-2-1-2v-3z" class="E"></path><defs><linearGradient id="J" x1="264.8" y1="489.425" x2="262.673" y2="498.849" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#J)" d="M252 481h0c1 1 1 2 2 3 0 2 1 3 2 4 0 1 0 1 1 1h0 1 3 4l1 1c2 0 3 1 5 2h1c0-2-1-9 0-10h1l-1 11h0c0 2 1 4 0 5 0 1-3 2-3 2h-3c-2-2-5-5-8-7-1-2-2-5-4-7-1-1-1-3-2-4h0v-1z"></path><path d="M181 106l1-2c3-4 5-6 10-6 0 1 1 1 2 1s2 0 3 1c-2 0-4 0-6 1l-1 1c-1 1-1 2-2 3 0 1 0 2-1 3h0 0l-1 1v1l1 1v2l1 1c-1 1-1 1-2 1h0-1-1l-1 1 3 3s0 1-1 1-1-2-2 0v1c-1-1-1-1-2-1l-1-1-1-1v-1c1-1 1-2 2-3h2v-1h-1v-2h-2v-1c0-2 0-3 1-4z" class="P"></path><path d="M181 106c1 0 2 1 2 2h0-1c0 1 0 1-1 1l-1 1c0-2 0-3 1-4z" class="C"></path><path d="M180 119h1c0-1 0-1 1-2v-2h1v1l3 3s0 1-1 1-1-2-2 0v1c-1-1-1-1-2-1l-1-1z" class="H"></path><path d="M184 115v-6-1c1-2 3-4 3-6 0-1 0-1 1-1h1s1 0 1 1c-1 1-1 2-2 3 0 1 0 2-1 3h0 0l-1 1v1l1 1v2l1 1c-1 1-1 1-2 1h0-1-1z" class="W"></path><defs><linearGradient id="K" x1="225.647" y1="583.088" x2="225.723" y2="607.7" xlink:href="#B"><stop offset="0" stop-color="#686867"></stop><stop offset="1" stop-color="#aeacae"></stop></linearGradient></defs><path fill="url(#K)" d="M225 580c1 2 1 5 1 8l4 22c-1 0-1-1-1-2v-1l-1 1v1 1 4 5l-3-7c0-3-1-6-1-8h0c1-1 0-7-1-8v-2c0-2 0-3-1-4 0-1 1-1 0-2 0-1 0-1 1-2 0-1 0-1-1-1v-3h0l1 1h2 0 0v-2-1z"></path><path d="M224 604h0c1-1 0-7-1-8v-2c0-2 0-3-1-4 0-1 1-1 0-2 0-1 0-1 1-2v2c1 2 1 4 1 5v1c1 1 1 2 1 3l3 17v5l-3-7c0-3-1-6-1-8z" class="M"></path><path d="M281 289h-1c0 1-1 2-2 3l-1 1v4c2-1 3-3 5-4l-6 6v1c-1 0-2 1-2 2-1 2-1 4-1 7 0 2 1 4 2 5v1c0 1 0 2-1 3h-1c-1-1-1-2-2-3s-3-2-3-4h-1v-7c1-5 5-8 9-11l5-4z" class="c"></path><path d="M273 309v4h0c1 1 0 2 1 4h-1v-1c-1 0-1-2-2-3h0c0-1-1-1-1-2l-1-4h-1 0c0-2 1-3 2-4 0-2 2-4 3-6h2c0 1 1 1 1 2v1c-1 0-2 1-2 2-1 2-1 4-1 7z" class="I"></path><defs><linearGradient id="L" x1="166.313" y1="311.604" x2="179.401" y2="290.448" xlink:href="#B"><stop offset="0" stop-color="#0c0d0c"></stop><stop offset="1" stop-color="#383739"></stop></linearGradient></defs><path fill="url(#L)" d="M171 196v-10c0-2 0-4 1-6v106c1 1 1 1 2 1l1-1-1 1h-1v1 1h1 1 1c1-1 1-1 2-1h0c0 1 0 2-1 3h-3c-1 1-1 2-1 3v3c1 0 1 0 1 1-2 3-1 9-1 13h0v4h-1v-2c-1-2-1-4-1-6l1-19-1-7v-56c0 1 0 2-1 3v3h-1v-13l2-22z"></path><path d="M171 196v29c0 1 0 2-1 3v3h-1v-13l2-22z" class="X"></path><path d="M279 341c2 0 5-1 7 0-1 0-2 1-2 2h0c-2 2-2 5-4 6-1 0 0 1-1 1 0 1 0 2-1 3h-5 0-3 0-2c-2 0-3 0-5 1 1-2 2-3 4-4v-1c0-1 1-1 2-2l1-2h1c0-1 1-1 2-1 0-1 1-1 1-2h2c1 1 1 1 1 2h0l2-3z" class="H"></path><path d="M272 345h1c1-1 1-2 2-2l1 1c0 1-1 3-2 4 0 1 0 3-1 4h-2c-1-2 0-5 1-7z" class="U"></path><path d="M269 347c1-1 2-1 3-2h0c-1 2-2 5-1 7 1 1 1 1 2 1h0-3 0-2c-2 0-3 0-5 1 1-2 2-3 4-4v-1c0-1 1-1 2-2z" class="R"></path><path d="M279 302h8v1 3 7 3c0-1 0-1-1-2-2 1-5 0-7 0-2-1-2-2-4-2v2c-1-1-2-3-2-5 0-3 0-5 1-7h5z" class="O"></path><path d="M287 303v3 7 3c0-1 0-1-1-2-2 1-5 0-7 0 1-1 2 0 3 0l1-1v1c1 0 1 0 2-1h-1 1v-6l1 1 1-5z" class="B"></path><path d="M279 302h8v1l-1 5-1-1c-3 0-8 0-10-2 0-1-1-1 0-2l4-1z" class="K"></path><path d="M224 296v-3c1 0 1 1 2 1 2 1 2 2 3 3l1 1h1l-1 1v2h1v2l1 1-1 3c1 2 1 5 2 6 0 1-1 1-2 2h1v2l2 1c1 1 3 1 4 2v1c-2 1-3 0-5 0h-1l1-2h0l-1-1c-1 1-1 2-1 3h-1v-1c0-1 0-1 1-2l-2-1c0-1-1-2-1-2-1 0-1 0-2 1l1 1h0l-1 1c0-1-1-1-1-2v-1c0-1 1-1 1-2s0-4-1-5h0v-2c0-1 0-1-1-2v1l-1-1v-1l2-1c0-1-1-4-1-6z" class="M"></path><path d="M230 301h1v2l1 1-1 3c1 2 1 5 2 6 0 1-1 1-2 2h-1c0-1 1-1 1-2h-2c0-5-1-8 1-12z" class="D"></path><path d="M224 296v-3c1 0 1 1 2 1 2 1 2 2 3 3l1 1h1l-1 1-1 1c-1-1-1-2-2-2v4 11c1 1 2 2 2 4 0-1-1-2-1-2-1 0-1 0-2 1l1 1h0l-1 1c0-1-1-1-1-2v-1c0-1 1-1 1-2s0-4-1-5h0v-2c0-1 0-1-1-2v1l-1-1v-1l2-1c0-1-1-4-1-6z" class="G"></path><path d="M178 206c0-2 0-3 1-4h0c1 1 1 1 1 2 1 1 2 0 3 1v-1-1h0 2c1 0 2-1 3 0s1 1 1 2h0v2 1l-1 1v1c1 0 2 0 3 1 0 2 0 5-1 7v2c0 1 0 2 1 4l-2 1 1 2c-1 0-2-1-3-2v-2l-1-3h0l-3-5c0-1-1-4-2-5s-2-1-2-2l-1-2z" class="P"></path><path d="M183 215c1 1 2 1 3 2v3l-3-5z" class="C"></path><path d="M186 220h1 0 3v2h0-1c-1 0-1 1-1 1h-1l-1-3z" class="K"></path><path d="M178 206c0-2 0-3 1-4h0c1 1 1 1 1 2 1 1 2 0 3 1l1 1-1 1-2-1v1l1 1v1c-1 0-1 0-1-1h0v1c0-1 0-1-1-1h0c0-1-1-2-1-2h-1z" class="C"></path><path d="M185 203c1 0 2-1 3 0s1 1 1 2h0v2 1l-1 1v1c0 1 1 1 1 2h-1s-1 0-1-2h0c-1-1-2-1-2-2h0-1-2 0l-1-1v-1l2 1 1-1-1-1v-1-1h0 2z" class="B"></path><path d="M183 204h1l2 2h1v-1l1 1v2h-1l-1-1h-2v-1l-1-1v-1z" class="R"></path><path d="M185 203c1 0 2-1 3 0v2h-1v1h-1l-2-2h-1v-1h0 2z" class="C"></path><path d="M265 287c1 0 2 0 2-1h1 2c0 1 0 2 1 2l1 1v3 1h0 4c-4 3-8 6-9 11v7l-1-1v-3-1c-1 1-2 2-3 2v-3h1l-1-1v-1-1h-1c0 1-1 1-1 2 0-1 0-1-1-2h1v-1l-1-2c2-2 2-6 2-8 1-1 2-1 2-1 0-1 1-2 1-3z" class="M"></path><path d="M271 288l1 1v1h-1c-1 0-2-1-2-2h2z" class="L"></path><path d="M269 295l-1 2c-1-1-3 0-4 0v-1l1-1h4 0z" class="T"></path><path d="M269 295l1-1s1 1 2 1c-1 1-2 2-3 2h-1l1-2z" class="O"></path><path d="M265 287c1 0 2 0 2-1h1 2c0 1 0 2 1 2h-2-1l-1 1c0 1 0 1 1 1h1 0l-1 1h-3v-1h-1c0-1 1-2 1-3z" class="W"></path><path d="M265 301c0-1 0-1-1-2l1-1h1c0 2 0 2 1 4h0v2 7l-1-1v-3-1c-1 1-2 2-3 2v-3h1l-1-1v-1l1-1 1-1z" class="Y"></path><path d="M264 290h1v1c0 1 0 1 1 1h1 0v1h-1l-1-1v1h-1-1v4c1 1 1 2 1 3l1 1-1 1-1 1v-1h-1c0 1-1 1-1 2 0-1 0-1-1-2h1v-1l-1-2c2-2 2-6 2-8 1-1 2-1 2-1z" class="L"></path><defs><linearGradient id="M" x1="221.133" y1="474.4" x2="228.518" y2="486.612" xlink:href="#B"><stop offset="0" stop-color="#3d3d3d"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#M)" d="M217 476s-1-1-1-2-2-5-2-7c1 0 1 0 1 1s1 3 1 4 1 3 2 4c0 0 0 1 1 2h0c1-2 3-4 5-5h0c0-1 1-1 1-1h0v-1h0c-1-1-3-3-4-5 0-1-1-1-2-3h1l1 2 1-1c1 2 3 4 4 5l-1 1h0l1 1h0c2 3 3 6 4 9h1c0 2 0 4-1 5-1 2-2 2-3 4v1 1h1c1 0 1 1 1 1h-3l-1 1c0-1 0-1-1-2v-1c-1-1-2-4-3-6-1-3-3-6-4-8z"></path><path d="M224 490v-1h2 0l1 1v1h1c1 0 1 1 1 1h-3l-1 1c0-1 0-1-1-2v-1z" class="L"></path><path d="M181 120c1 0 1 0 2 1 0 1-1 1-1 2l1 1c2 1 4 3 6 3 1 0 2-1 3 0v1c1 0 2 0 3-1 2 0 5 1 7 0h3 5 1 2c1 0 4 0 4 1v1c0 1 0 3-1 4-1 0-2-1-3-2h-1-10c-2 1-5 1-7 1-1 0-3 0-4 1h0v-1c-2 0-2 1-4 1v-1c-1-1-2-1-4-2v2l-7 1c1-1 2-1 3-2l-1-1c2-1 2-1 3-2h0 3 0 0 2l-1-1-1-1c-1 0-1 0-2-1h0c0-1 0-1-1-2-1 0 0-2 0-3z" class="N"></path><path d="M216 129h0 1c0 1 0 3-1 4-1 0-2-1-3-2 1 0 2 0 3-2z" class="c"></path><path d="M181 120c1 0 1 0 2 1 0 1-1 1-1 2l1 1c1 2 3 3 5 4 3 1 11 2 14 1h0 3 1v1h-5c-2 0-3 1-4 0h-1l-1 1h-1-3c-1-1-3-1-4-1-2 0-4-1-6-2h0 3 0 0 2l-1-1-1-1c-1 0-1 0-2-1h0c0-1 0-1-1-2-1 0 0-2 0-3z" class="K"></path><path d="M183 124c2 1 4 3 6 3 1 0 2-1 3 0v1c1 0 2 0 3-1 2 0 5 1 7 0h3 5 1 2c1 0 4 0 4 1v1h-1 0-3c-2-1-4 0-7 0h-1-3 0c-3 1-11 0-14-1-2-1-4-2-5-4z" class="V"></path><path d="M159 109c0-2 2-3 2-5 1-2 1-3 1-4v-3h0 1c1 1 3 2 4 3s2 2 2 3h1c1 0 1 1 1 2 1 2 1 4 0 5v1c0 1-1 1-2 2l-9 11h0c0-2 0-2 1-3h-4v-2h0c1-1 1-4 1-5h1v5c-1 0-1 1 0 1 0 0 0-1 1-1v-2-1c0-2 0-5-1-7z" class="T"></path><path d="M163 107h0v-6h1v3c0 2-1 4 0 6-1 0-1 0-1 1l1 1c-1 1-1 1-2 1l-1-1h1v-3h0c0-1 0-1 1-2z" class="F"></path><path d="M159 109c0-2 2-3 2-5 1-2 1-3 1-4v-3h0 1c1 1 3 2 4 3s2 2 2 3h1c1 0 1 1 1 2v1c-1 2-1 4-1 5-1 1-2 1-3 2v-1h0v-3-5-2h-1c0 3 0 7-1 8h0c0-2 1-7 0-9h-1v3-3h-1v6h0l-1-1v1c-1 1-2 1-3 2z" class="O"></path><path d="M167 104v-2c1 0 1 1 1 1v8h1c1-1 1-4 0-6v-1h1c0 1 0 2 1 2h0c-1 2-1 4-1 5-1 1-2 1-3 2v-1h0v-3-5z" class="P"></path><path d="M270 500h1 1v9c0 1 0 3-1 4-2 0-3-1-5-1v1h1c2 1 4 1 6 2h-7c-2 0-3-1-5 0-1 0-3-1-4-2-3 0-5-1-7-2v-2-2l3-1 1-1h2 0l1-1h2l1-1h1v1h0c3-1 6-2 9-4z" class="T"></path><path d="M261 513h0v-1c2-1 3-1 6-1v1h-1c-2 0-3 0-5 1z" class="W"></path><path d="M266 508h2v1 1h-3-3l1-1c1 0 2 0 3-1z" class="M"></path><path d="M257 510l2-2h3v1c-1 0-3 1-4 1l-1 1h2v1h-1c-1-1-3-1-4-1l1-1h2z" class="L"></path><g class="Y"><path d="M250 509c2 1 3 1 4 2 1 0 3 0 4 1h1v1c1 1 1 1 3 1v-1h-1c2-1 3-1 5-1-1 0-2 0-2 1l1 1s1 0 1-1h1c2 1 4 1 6 2h-7c-2 0-3-1-5 0-1 0-3-1-4-2-3 0-5-1-7-2v-2z"></path><path d="M270 500h1 1v9l-2-2h-9l1 1h0-3l-2 2h-2l-1 1c-1-1-2-1-4-2v-2l3-1 1-1h2 0l1-1h2l1-1h1v1h0c3-1 6-2 9-4z"></path></g><path d="M261 507v-1c2 0 3-1 5-2 0 0 2 0 3-1l1 1h0l-1 1h-3v1c2 0 3 0 5 1h-1-9z" class="L"></path><path d="M256 505h0l1 1h1 0c0 2-1 2-1 4h-2l-1 1c-1-1-2-1-4-2v-2l3-1 1-1h2z" class="W"></path><defs><linearGradient id="N" x1="176.189" y1="311.833" x2="164.011" y2="293.461" xlink:href="#B"><stop offset="0" stop-color="#363634"></stop><stop offset="1" stop-color="#666568"></stop></linearGradient></defs><path fill="url(#N)" d="M168 267c1 4 0 9 0 12h3 0v2l1 7-1 19c0 2 0 4 1 6-1 1-1 2-1 4h0c1 0 1 0 2-1v-1c1-1 2-3 3-3h0c0 1 0 1-1 2 1 0 2 1 2 1l-1 1 1 1-4 5c-1 1-1 2-1 2-1 1-1 2-2 2 0 1 0 3-1 4h0c-2-2-2-4-3-6v-13c1 2 0 3 1 5v4 1 1 1c1-1 1-2 1-3l2-2h0c-1 0-1 0-2-1v-1-2-36-11z"></path><path d="M175 314c1 0 2 1 2 1l-1 1 1 1-4 5c-1 1-1 2-1 2-1 1-1 2-2 2 0 1 0 3-1 4h0c-2-2-2-4-3-6h1 0c3-4 6-6 8-10z" class="H"></path><defs><linearGradient id="O" x1="172.213" y1="288.782" x2="166.154" y2="281.036" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#858485"></stop></linearGradient></defs><path fill="url(#O)" d="M168 267c1 4 0 9 0 12h3 0v2l1 7-4 5v-11-4-11z"></path><path d="M206 445v-3h1v-1c4 2 6 3 9 5l3 3h2c5 3 7 6 11 10 2 1 5 1 7 2v1c-2 0-6 0-8-1-5-1-10-2-15-5-4-2-7-3-10-7-1-1 0-2 0-4z" class="N"></path><path d="M206 449l1-1c6 0 13 7 19 10 1 1 3 1 5 2h1l-1 1c-5-1-10-2-15-5-4-2-7-3-10-7z" class="I"></path><defs><linearGradient id="P" x1="179.91" y1="249.543" x2="158.629" y2="242.795" xlink:href="#B"><stop offset="0" stop-color="#818383"></stop><stop offset="1" stop-color="#e0ddde"></stop></linearGradient></defs><path fill="url(#P)" d="M167 191h1 0v12 10c0 1 0 2 1 3v2 13h1v-3c1-1 1-2 1-3v56-2h0-3c0-3 1-8 0-12-1-2 0-5 0-7l-1-16v-53z"></path><defs><linearGradient id="Q" x1="225.549" y1="459.181" x2="239.099" y2="470.896" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#a5a4a5"></stop></linearGradient></defs><path fill="url(#Q)" d="M260 457c2 0 4-1 6-1v1h-2l-10 3c-2 1-4 1-6 1l-4 1h-3c-1 2 0 7 0 9h0-1c-2 0-5 2-7 3h-1v3l-6-8c-1-1-3-3-4-5-2-3-4-5-6-8 5 3 10 4 15 5 2 1 6 1 8 1 1-1 3 0 5-1l12-2 2-1 2-1z"></path><path d="M183 293v1h1v-1h1v1h3 0 1s1 1 2 1c0 1 0 1-1 2v4c-1 1-3 2-4 3s-2 2-2 4c0 1 0 1-1 2l-3 3-1 1s-2 2-2 3l-1-1 1-1s-1-1-2-1c1-1 1-1 1-2h0c-1 0-2 2-3 3v1c-1 1-1 1-2 1h0c0-2 0-3 1-4v2h1v-4h0c0-4-1-10 1-13 0-1 0-1-1-1 2-1 3-2 5-1 2-1 3-1 4-1 0-1 0-1 1-1v-1z" class="P"></path><path d="M174 308l2 2c-1 0-1 1-2 1h0v-3z" class="O"></path><path d="M186 304c-1 0-2 1-3 0 0-1 1-1 1-2h0l1-1c2-1 3-3 4-4h1v4c-1 1-3 2-4 3zm-13-7c2-1 3-2 5-1h-2s1 2 0 2v9c0 1 0 1-1 1-1-2 0-4 0-6h-1v6h0v3h-1 0c0-4-1-10 1-13 0-1 0-1-1-1z" class="K"></path><path d="M285 300c3-1 5-1 8-1v1h5c1 0 3-1 3 0 1 0 0 1 0 1-1 4-1 9 0 13l1 7h-8l-1-6v-5l-1 3 1 2c-1 0-1 1-1 1h0l-1-1h-2 0v-1l-2-1v-7-3-1l-1-2h-1z" class="J"></path><path d="M286 300h4 1l1 6v4 3l1 2c-1 0-1 1-1 1h0l-1-1h-2 0v-1l-2-1v-7-3-1l-1-2z" class="X"></path><path d="M287 306c0 3 2 4 2 7v1l-2-1v-7z" class="f"></path><path d="M292 310v3l1 2c-1 0-1 1-1 1h0l-1-1h-2 0v-2l3-3z" class="d"></path><path d="M290 300h1l1 6-1 1-2 2-1-1 1-1c2-3 0-5 1-7z" class="I"></path><path d="M298 300c1 0 3-1 3 0 1 0 0 1 0 1-1 4-1 9 0 13h-1l-1-1c-1 0-4 1-5 1-1-4 0-9-1-14h0 0 5z" class="Z"></path><defs><linearGradient id="R" x1="212.52" y1="489.152" x2="221.406" y2="502.232" xlink:href="#B"><stop offset="0" stop-color="#3c3b3b"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#R)" d="M217 476c1 2 3 5 4 8 1 2 2 5 3 6v1c1 1 1 1 1 2l2 5h0v1c-1 1-1 2-1 3 1 1 0 2 0 3l-2-1h-1c-1-1-2-1-4-2-1 0-3 0-4-1h-4c-1-1-1-1-1-3s0-4-1-6v-9-1c0-1 0-1 1-2v2c1 2 0 6 0 8h1s1-1 2-1h1v-1h2c0-1 1-1 1-2 1 0 1 0 2-1v-2l-3-6v-1c0 1 1 1 1 2l1 1c-1-1-1-2-1-3z"></path><path d="M221 484c1 2 2 5 3 6v1h0l-1 1c-1-2-2-4-3-5 0-1 0-2 1-3z" class="F"></path><path d="M224 504l-1-1h0 0c0-2 2-3 2-4s0-1 1-2l1 1v1c-1 1-1 2-1 3 1 1 0 2 0 3l-2-1z" class="D"></path><path d="M202 101c4-2 8-4 13-5-3 2-8 4-12 6 2 2 5 5 5 7h-1l-1 1c-1 1-1 2-2 3-1 0-3 0-3 1-2 1-4 2-6 1-1 0-2 1-3 0h-3l-1-1-1-1v-2l-1-1v-1l1-1h0 0c1-1 1-2 1-3 1-1 1-2 2-3l1-1c2-1 4-1 6-1s4 0 5 1z" class="N"></path><path d="M187 113c1 0 1-1 2-1l1 1 2 1v1h-3l-1-1-1-1z" class="E"></path><path d="M190 113c0-1 0-2-1-2v-1h1v-1l1 2 2 2c2 0 2-1 3-2 1 0 1-1 2-2l1 2-1 1s-1 1-2 1c0 1-1 2-1 2-1 0-2 1-3 0v-1l-2-1z" class="G"></path><path d="M193 104h2 1c1 1 1 2 1 3v2h-3v-1l-1 1c0 1 1 1 1 1 0 1 0 1-1 1-1-1-1-2-1-3s0-2-1-3l2-1z" class="S"></path><path d="M193 104h2c1 1 1 2 1 3-1 1-1 1-2 1v-4h-1z" class="D"></path><path d="M191 101c2-1 4-1 6-1s4 0 5 1c0 1-1 1-2 2h-2l-2 1h-1-2l-2 1h0v-4z" class="V"></path><path d="M202 108c-1-1-1-3-1-4h0c2 0 3 1 4 2s1 3 1 4c-1 1-1 2-2 3-1 0-3 0-3 1-2 1-4 2-6 1 0 0 1-1 1-2 1 0 2-1 2-1l1-1-1-2h0c1-1 1-2 0-3v-1h1c1 1 1 2 3 3z" class="S"></path><path d="M198 113l3-3h1c-1 2-1 3-3 4h-1v-1z" class="L"></path><path d="M198 109h0c1-1 1-2 0-3v-1h1c1 1 1 2 3 3h2v2c-1 0-1 1-2 1v-1h-1l-3 3v-1l1-1-1-2z" class="H"></path><path d="M219 320h0c0 2 0 3 1 4h0s1 0 1 1h0l1 1h2c2 0 5-1 7 0 0 0 0 1 1 1h1 2v1h0c-1 1-2 1-3 1 0 1 0 1 1 2l-2 1c-1 0-1 1-3 1-2 1-3 1-4 3 0 1 0 1-1 2h0c-1 2-2 2-2 4-1 0-1 1-1 1-2 0-3 1-4 1h-1c-1 0-1-1-2-1l-1-2-2-2 1-2v-2h0v-1-4c0-2 0-3 1-4h1v3c1-1 3-2 3-3v-2c1 0 1 1 2 1v-1c1-1 1-3 1-4z" class="e"></path><path d="M213 343c0-1 1-2 2-2l1-1h0c1 2 0 3 0 4h-1c-1 0-1-1-2-1z" class="T"></path><path d="M218 334h1 1v1h-1-1l1 2-2 1h-1c0-1 0-1 1-1 0-1-1-1-2-1l1-1s1 0 1-1h1z" class="L"></path><path d="M211 335c1-1 1-1 2-1v3h0c-1 1-1 3-1 4l-2-2 1-2v-2h0z" class="F"></path><path d="M211 330c0-2 0-3 1-4h1v3s1 1 1 2h-1v2 1c-1 0-1 0-2 1v-1-4z" class="R"></path><path d="M219 320h0c0 2 0 3 1 4h0s1 0 1 1h0c0 1-1 2-1 3-1 1-1 1-1 2h-1v2h-3 0c1-1 1-1 2-1v-1c-1 0-2 0-3 1 0-1-1-2-1-2 1-1 3-2 3-3v-2c1 0 1 1 2 1v-1c1-1 1-3 1-4z" class="M"></path><path d="M218 334h-3l1-1h4l1 1s1 0 1-1l1 1-1 1v1h1 0 1c0 1 0 1-1 2h0c-1 2-2 2-2 4-1 0-1 1-1 1-2 0-3 1-4 1 0-1 1-2 0-4h1 1c-1-1-1-2-2-2h1l2-1-1-2h1 1v-1h-1-1z" class="F"></path><path d="M219 337h1l1 1v1h0c-1 1-1 2-3 2l-1-1h1c-1-1-1-2-2-2h1l2-1z" class="G"></path><path d="M221 325l1 1h2c2 0 5-1 7 0 0 0 0 1 1 1h1 2v1h0c-1 1-2 1-3 1 0 1 0 1 1 2l-2 1c-1 0-1 1-3 1-2 1-3 1-4 3h-1 0-1v-1l1-1-1-1c0 1-1 1-1 1l-1-1v-1s-1 0-1-1h0v-1c0-1 0-1 1-2 0-1 1-2 1-3z" class="S"></path><path d="M227 330h2l-2 2c0 1-1 1-2 2l-2-2h1 1c1-1 1-2 2-2z" class="K"></path><path d="M223 332v-2l-1-2h3c1 0 2 0 3-1l1 1-2 2c-1 0-1 1-2 2h-1-1z" class="H"></path><path d="M274 185v8 14l-2 12v15 21 11 1l-1 1s-1 1-1 2l-1-1c-1 0-1 0-1-1v-3-8-4c0-1-2-1-3-1l1-2s1 0 1 1l1-1h0c1-4 0-8 0-12v-20-4c0-1-1-1-1-1h0v-3h1v2h1c1-1 2-3 3-4 0-2 0-3 1-5 1-6 1-12 1-18z" class="F"></path><path d="M195 263h1c-1 1-1 1 0 2v-1-3-6-14-4 8c1 0 0 1 0 2 1 2 1 3 1 5v4 5c0-2 1-3 2-4h1c0-2-1-7 0-9l1-5v16 13c0 4 2 5 4 8l-1 1c0 1-1 1-1 1 0 1-1 2-2 3h0c-1 0-2 0-3 1l-1 1h0l-2-1c-2 0-3 0-4-1h0l-1-2v-6h0c0-2 0-4 1-5v-2l2 1v-1c1-1 0-2 1-3v-8c0-2-1-2 0-3h1v7h0z" class="S"></path><path d="M191 272c1 0 1 0 2 1v5h-2v1 2h1 0v-2h1c0 1 0 1-1 2 0 1 1 3-1 4l-1-2v-6h0c0-2 0-4 1-5z" class="E"></path><path d="M195 272v-6c1 1 2 1 3 2h0l2 1c0 1 0 2 1 3 0 4 2 5 4 8l-1 1c0 1-1 1-1 1 0 1-1 2-2 3h0c-1 0-2 0-3 1l-1 1h0l-2-1c-2-2-1-6-1-9 0-2 0-3 1-5z" class="C"></path><path d="M194 277c0-2 0-3 1-5v3s1 0 1 1v1l-1 1-1-1z" class="P"></path><path d="M201 277l1 1c-1 1-1 2-2 4-1 1-1 2-2 4l-1 1v-2-2c1-2 1-2 2-3s2-2 2-3z" class="L"></path><path d="M198 268l2 1c0 1 0 2 1 3 0 4 2 5 4 8l-1 1c0 1-1 1-1 1 0 1-1 2-2 3h0c-1 0-2 0-3 1 1-2 1-3 2-4 1-2 1-3 2-4l-1-1c0-1-1-1-2-1l-1-8z" class="E"></path><path d="M231 480h-1c-1-3-2-6-4-9h0l-1-1h0l1-1 6 8s1 2 2 2c1 2 2 3 4 4h1v3 4l-1 5-3 11h0c0 1-1 1-1 2h-1c-1 1-2 1-3 1h-1l1-1-2-3h-2c0-1 1-2 0-3 0-1 0-2 1-3v-1h0l-2-5 1-1h3s0-1-1-1h-1v-1-1c1-2 2-2 3-4 1-1 1-3 1-5z" class="L"></path><path d="M227 489c1 0 1 0 2 1h1c1 0 1 1 2 1v5c0 1 1 3 0 4l-1-2c0-1-1-2-1-3l-1-3s0-1-1-1h-1v-1-1z" class="B"></path><path d="M231 480h1c1 3 0 7 0 11-1 0-1-1-2-1h-1c-1-1-1-1-2-1 1-2 2-2 3-4 1-1 1-3 1-5z" class="H"></path><path d="M231 480h-1c-1-3-2-6-4-9h0l-1-1h0l1-1 6 8s1 2 2 2c1 2 2 3 4 4 0 1 0 1-1 2 0 0-1-1-1-2h-1l-1 9c0 1 0 1-1 1 1-3 0-6 0-9v-4c0-1 0-1-1-1v1h-1z" class="B"></path><path d="M238 483h1v3 4l-1 5-3 11v-1c-1-1-1-3-1-4 1-3 0-6 0-9l1-9h1c0 1 1 2 1 2 1-1 1-1 1-2z" class="b"></path><path d="M238 495c-1-1-1-1-1-2v-4h1l1 1-1 5z" class="d"></path><path d="M225 493l1-1h3l1 3c0 1 1 2 1 3l1 2c0 3 2 5 2 8h-1c-1 1-2 1-3 1h-1l1-1-2-3h-2c0-1 1-2 0-3 0-1 0-2 1-3v-1h0l-2-5z" class="J"></path><path d="M226 505c0-1 1-2 0-3 0-1 0-2 1-3 1 2 1 4 3 6h-1-1-2z" class="V"></path><path d="M229 500s0-1 1-1l1-1 1 2c0 3 2 5 2 8h-1c-2-2-3-6-4-8z" class="d"></path><path d="M225 493l1-1h3l1 3c0 1 1 2 1 3l-1 1c-1 0-1 1-1 1l-1-2h-1l-2-5z" class="f"></path><path d="M230 495c0 1 1 2 1 3l-1 1c-1 0-1 1-1 1l-1-2c0-1 1-2 2-3z" class="N"></path><path d="M369 340h16 6c-1 2-12 1-15 1v1h0c0 3-2 8 0 10 1 1 2 1 3 1-1 0-2 0-3 2v1c0 1 0 1 1 1l-1 1v3c-1 0-1 0-2-1v-1h-5s-1-1-2-1c-1 1-1 2-1 2-1 0-1-1-2-2h0v-3l-1 1h-1c0-1 0-2-1-3l1-1c-1-2-2-3-3-4-1 0-2 0-3-1 0-1-1-1-1-2 2 0 2 0 3 1v-1-1l2 1h0l-1-2-1 1h-3 0c1-1 2-1 2-2h0s0-1 1-1c3 0 8 0 11-1z" class="g"></path><path d="M369 340h16 6c-1 2-12 1-15 1v1h-5c-2-1-7 0-9 0v1 3c1 0 1 0 1-1l1 1s-1 1-1 2h-1c-1-3 0-4-2-6h-2v1h1l-1 1h-3 0c1-1 2-1 2-2h0s0-1 1-1c3 0 8 0 11-1z" class="F"></path><path d="M371 342h5 0c0 3-2 8 0 10 1 1 2 1 3 1-1 0-2 0-3 2v1c0 1 0 1 1 1l-1 1v3c-1 0-1 0-2-1v-1h-5s-1-1-2-1c-1 1-1 2-1 2-1 0-1-1-2-2h0v-3-1-1c0-2 1-3 1-4h3v1c-1 1-1 1-1 2h2 3 1l1-1c0-1 0-2 1-3v-1l-1-1c1 0 1 0 1-1h0v-2c-1 1-1 2-1 3-1 0-2 0-3-1 0 0 0-1-1-1v-1l1-1z" class="h"></path><path d="M365 349h3v1c-1 1-1 1-1 2v2h-1l-1-1c1 0 1-1 1-2h0c0-1 0-1-1-2z" class="X"></path><path d="M367 358h0-1c0-1-1-1-1-2h0c2 0 3-1 4-1h2v1h1s0-1 1-2h0 0l2 1 1 1c0 1 0 1 1 1l-1 1v3c-1 0-1 0-2-1v-1h-5s-1-1-2-1z" class="Z"></path><path d="M375 355l1 1c0 1 0 1 1 1l-1 1v3c-1 0-1 0-2-1v-1h-5c1-1 2-1 3-1 0-1 2-2 3-3z" class="d"></path><defs><linearGradient id="S" x1="243.464" y1="462.881" x2="240.852" y2="470.836" xlink:href="#B"><stop offset="0" stop-color="#444343"></stop><stop offset="1" stop-color="#5a5b5b"></stop></linearGradient></defs><path fill="url(#S)" d="M244 462l4-1 1 1h-1 0c3 2 5 4 6 7 0 1 0 1-1 2h0c0 1 0 1-1 2v2l-3 4h0l-3 4-1 1-1 2c0 1 1 2 2 3 0 2 0 2-1 3-1-1-1-2-2-4-1-1-2-2-4-2v-3h-1c-2-1-3-2-4-4-1 0-2-2-2-2v-3h1c2-1 5-3 7-3h1 0c0-2-1-7 0-9h3z"></path><path d="M243 479c1 0 1 1 1 1 0 2 1 3 1 4l-1 2h-1l-1-1 1-6z" class="d"></path><path d="M241 480v-3h1v4c0 1-1 2-1 4h1l1 1h1c0 1 1 2 2 3 0 2 0 2-1 3-1-1-1-2-2-4-1-1-2-2-4-2v-3l1-2 1-1z" class="V"></path><defs><linearGradient id="T" x1="247.516" y1="471.754" x2="242.761" y2="476.152" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#b2b0b0"></stop></linearGradient></defs><path fill="url(#T)" d="M244 471c1-1 1-1 2-1l2 2h0s1 1 0 2h1v5h0l-3 4-1 1c0-1-1-2-1-4 0 0 0-1-1-1h0l1-8z"></path><path d="M246 480c1-1 1-1 1-2v-2l1 1s0 1 1 2l-3 4v-3z" class="f"></path><path d="M243 479h2l1-1v2 3l-1 1c0-1-1-2-1-4 0 0 0-1-1-1h0z" class="V"></path><path d="M241 471v6c-1 1-1 2 0 3l-1 1-1 2h-1c-2-1-3-2-4-4-1 0-2-2-2-2v-3h1c2-1 5-3 7-3h1 0z" class="N"></path><path d="M234 479v-1c2 0 4 0 6-1h0v4l-1 2h-1c-2-1-3-2-4-4z" class="I"></path><defs><linearGradient id="U" x1="251.378" y1="464.995" x2="245.554" y2="470.008" xlink:href="#B"><stop offset="0" stop-color="#5e5d5d"></stop><stop offset="1" stop-color="#8c8c8b"></stop></linearGradient></defs><path fill="url(#U)" d="M244 462l4-1 1 1h-1 0c3 2 5 4 6 7 0 1 0 1-1 2h0c0 1 0 1-1 2v2l-3 4v-5h-1c1-1 0-2 0-2h0l-2-2c-1 0-1 0-2 1v-9z"></path><defs><linearGradient id="V" x1="244.336" y1="488.047" x2="254.547" y2="501.334" xlink:href="#B"><stop offset="0" stop-color="#171617"></stop><stop offset="1" stop-color="#3d3d3d"></stop></linearGradient></defs><path fill="url(#V)" d="M248 484c0-1 3-5 4-5v2 1h0c1 1 1 3 2 4 2 2 3 5 4 7 3 2 6 5 8 7h3 1c-3 2-6 3-9 4h0v-1h-1l-1 1h-2l-1 1h0-2l-1 1-3 1v2 2h-1-1l-1-1c-1-1-2-1-2-2h-1l1-2-1-3h2v-4-3c-1-1-1-2-1-4 1-1 1-1 1-3v-1c0-2 1-3 2-4z"></path><path d="M249 507c0-1 1-3 1-4 1 0 1 1 2 1v1l1 1-3 1h-1z" class="N"></path><path d="M245 508v-1l1-1v1 1h1l2-1h1v2 2h-1-1l-1-1c-1-1-2-1-2-2z" class="T"></path><path d="M248 484c0-1 3-5 4-5v2 1c-2 3 0 8-2 11 0 3 0 5-1 7-1 1-1 3-2 4l-1-1v-4-3c-1-1-1-2-1-4 1-1 1-1 1-3v-1c0-2 1-3 2-4z" class="L"></path><path d="M248 484c0-1 3-5 4-5v2 1c-2 3 0 8-2 11 1-1 1-3 0-4h0c0 1-1 2-2 3v-6-2z" class="G"></path><path d="M248 484v2 10l-1 3h-1v-3c-1-1-1-2-1-4 1-1 1-1 1-3v-1c0-2 1-3 2-4z" class="d"></path><path d="M246 496v-3h1l1 3-1 3h-1v-3z" class="Z"></path><defs><linearGradient id="W" x1="256.331" y1="489.522" x2="255.328" y2="496.351" xlink:href="#B"><stop offset="0" stop-color="#424243"></stop><stop offset="1" stop-color="#656565"></stop></linearGradient></defs><path fill="url(#W)" d="M251 497h0c1-3 1-6 2-9 3 4 6 8 10 11l1 1 1 1-2 2h-2-1l-1 1h-2l-1 1h0-2l-1 1-1-1v-1c-1 0-1-1-2-1 1-1 1-2 1-3v-1-2z"></path><path d="M251 497c2 0 3 1 5 1h0 1 1s1 1 2 1h3l1 1h-7v1h0c-1 1-1 1-2 1h-1c0-2 0-3-1-4l-2 1v-2z" class="L"></path><path d="M257 501v-1h7l1 1-2 2h-2-1l-1 1h-1c-1-1-1-2-1-3h0z" class="e"></path><path d="M257 501h1c1 0 2 1 2 2l-1 1h-1c-1-1-1-2-1-3h0z" class="g"></path><path d="M251 499l2-1c1 1 1 2 1 4h1c1 0 1 0 2-1 0 1 0 2 1 3h1-2l-1 1h0-2l-1 1-1-1v-1c-1 0-1-1-2-1 1-1 1-2 1-3v-1z" class="J"></path><path d="M254 505h0c0-1 0-1 1-2h1v1 1h-2z" class="N"></path><path d="M251 500c1 0 2 1 2 2v1c0 1-1 1-1 2v-1c-1 0-1-1-2-1 1-1 1-2 1-3z" class="g"></path><path d="M267 213s1 0 1 1v4 20c0 4 1 8 0 12h0l-1 1c0-1-1-1-1-1l-1 2c1 0 3 0 3 1v4 8 3c0 1 0 1 1 1l1 1c0-1 1-2 1-2l1-1v-1c1 1 2 3 2 4-1 2-1 2-2 3-2 1-3 2-5 4-1 2-2 3-3 4 0 1-1 1-1 1v-1h0 0c1-1 1-2 1-3 1-2 3-4 4-5l-1-1c-1 0-2 1-2 2h0-1l-1-1v-6-13h0c-1 0-1-1-1-2l1-23c0-1 0-4-1-5 0 0 0-1-1-1l2-3 1-1c1-2 3-4 3-6z" class="C"></path><path d="M264 219l-1 35h0c-1 0-1-1-1-2l1-23c0-1 0-4-1-5 0 0 0-1-1-1l2-3 1-1z" class="B"></path><defs><linearGradient id="X" x1="283.969" y1="369.373" x2="282.722" y2="440.269" xlink:href="#B"><stop offset="0" stop-color="#a8a7a8"></stop><stop offset="1" stop-color="#f4f3f4"></stop></linearGradient></defs><path fill="url(#X)" d="M281 440v-71h5v72h-1v-1c-2 1-3 1-4 0z"></path><path d="M397 299l24 2c0 2 1 7 0 9-2 1-5 2-7 3h-2c-2 1-5 2-7 2-3 1-7 3-10 2v-1-1-9c1-2 0-3 2-5v-2z" class="I"></path><path d="M397 301v1 9 3c1 0 1 0 2-1 1 0 2 1 3 0h1 3 1c1-1 4-1 5-2h1l1-1v-2l7 1v1c-2 1-5 2-7 3h-2c-2 1-5 2-7 2-3 1-7 3-10 2v-1-1-9c1-2 0-3 2-5z" class="M"></path><path d="M414 308l7 1v1c-2 1-5 2-7 3h-2c-3 0-5 0-7 1-3 0-5 0-7 1h-1v-1-3 3c1 0 1 0 2-1 1 0 2 1 3 0h1 3 1c1-1 4-1 5-2h1l1-1v-2z" class="Z"></path><path d="M397 299l24 2c0 2 1 7 0 9v-1l-7-1v2l-1 1h-1c0-3-1-6 1-8l-1-1h-15v-1-2z" class="c"></path><path d="M414 308h-1c1-1 1-1 0-3 0 0 0-1 1-1v-1c1 0 2 0 3 1h0 1l2-2c1 1 1 5 1 7l-7-1z" class="I"></path><path d="M175 226c0-2 0-5 1-6h0c0-1-1-2-1-3 1 0 1 0 1-1s-2-3-2-4l1-1c0 1 1 1 1 1l1 3 6 12c1 1 2 5 3 5v1c1 1 1 1 1 2h0c0 1 0 2 1 3 0 0 1 1 1 2 1 1 1 2 2 3 0 1 0 1-1 1l-1 2c-1 0-1 1-1 1-1 1-1 1-1 2s-1 1-1 2l-3 3c-1 2-2 4-3 5 0 1 0 1-1 1h0s-1-1-2-1v-4-2c-1-5-1-11-1-16 0-4-1-8-1-11z" class="F"></path><g class="P"><path d="M180 249v-11c0-2-1-8 1-10v3 10 5c-1 1-1 2-1 3h0z"></path><path d="M181 231h1 0v3-3c1 1 1 2 1 3 1 0 2 1 2 2 0 2-1 3-1 4l1 1c1-2 0-4 2-6 0 1 0 2 1 3 0 0 1 1 1 2 1 1 1 2 2 3 0 1 0 1-1 1l-1 2c-1 0-1 1-1 1-1 1-1 1-1 2s-1 1-1 2l-3 3h0c-1 0-1 0-2-1v-1-6-5-10z"></path></g><path d="M184 248v-1c0-1 0-1 1-2h0c0 1 1 2 1 3 2-2 1-1 1-3v-3h1c1 0 1 1 2 2l-1 2c-1 0-1 1-1 1-1 1-1 1-1 2s-1 1-1 2l-3 3h0v-1-2h0c0-1 0-2 1-3z" class="H"></path><defs><linearGradient id="Y" x1="184.601" y1="242.718" x2="179.007" y2="240.986" xlink:href="#B"><stop offset="0" stop-color="#383a36"></stop><stop offset="1" stop-color="#4c484e"></stop></linearGradient></defs><path fill="url(#Y)" d="M181 231h1 0v3c2 3 0 8 1 12l1 2c-1 1-1 2-1 3h0v2 1c-1 0-1 0-2-1v-1-6-5-10z"></path><path d="M175 226h1c0-2 1-3 1-4h0c1 1 3 4 2 6v5c0 5 1 10 0 15 0 2 0 6 1 8v-7h0c0-1 0-2 1-3v6 1c1 1 1 1 2 1h0c-1 2-2 4-3 5 0 1 0 1-1 1h0s-1-1-2-1v-4-2c-1-5-1-11-1-16 0-4-1-8-1-11z" class="E"></path><defs><linearGradient id="Z" x1="182.156" y1="253.677" x2="176.524" y2="256.206" xlink:href="#B"><stop offset="0" stop-color="#232423"></stop><stop offset="1" stop-color="#3e3c3e"></stop></linearGradient></defs><path fill="url(#Z)" d="M177 253v-5h0l1 4v-2-9c0-1 0-2 1-3v10c0 2 0 6 1 8v-7h0c0-1 0-2 1-3v6 1c1 1 1 1 2 1h0c-1 2-2 4-3 5 0 1 0 1-1 1h0s-1-1-2-1v-4-2z"></path><path d="M272 425l1 2h1 0l1 2v3 8 8 4c-1 0-2 1-3 2-2 0-5 3-6 3v-1c-2 0-4 1-6 1l-2 1c-1-2 0-5 0-7h0c-1-1 0-1-1-1v-2-3c-1-1-1-2-1-3 5-5 11-10 15-16l1-1z" class="C"></path><path d="M261 448c0-2 0-5 1-7v13h1v1h-1v-7h-1z" class="K"></path><path d="M263 454c0-5 0-11 1-15v-1c1 1 0 4 0 6v10h-1z" class="B"></path><path d="M258 451c0-2 0-7 1-9v3c0 3 1 6 0 9v3h1l-2 1c-1-2 0-5 0-7z" class="Q"></path><path d="M273 427h1 0l1 2v3 8 8 4c-1 0-2 1-3 2-2 0-5 3-6 3v-1c-2 0-4 1-6 1h-1v-3l1 3 1-1v-8h1v7h1v-1h0 1v1h2v-3c1-1 3-1 5-1v-10c0-5 0-9 2-14z" class="F"></path><path d="M274 427l1 2v3 8 8 4l-1-1h-1-1v-8c1-6 0-10 2-16z" class="E"></path><defs><linearGradient id="a" x1="245.369" y1="611.309" x2="231.108" y2="671.643" xlink:href="#B"><stop offset="0" stop-color="#cfcfcf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#a)" d="M235 617c1-2 1-2 1-4 2-1 3 0 5-1h5 1v2 1 2c0 6-2 12-3 17-1 8-1 15-2 22v7l-1 18-3-29c0-4-1-9-1-13l-2-22z"></path><path d="M249 298h0c2-1 2-2 2-3 1 1 1 2 1 4h0l1 1 2-2c0 1 0 2 1 2 0 2-1 3 0 5-1 2 0 3 0 4-1 1-1 2-1 2-1 1-2 1-3 1-1 1 1 4-2 4h0l1 2-1 1-2-2-1 1 2 2-1 1c-1 0-1-1-2-1h-1l-1 1c-1 0-2-1-4 0h0-2v-1c-1-1-3-1-4-2l-2-1v-2h-1c1-1 2-1 2-2-1-1-1-4-2-6l1-1h1l1 2h0c0-1 0-3 1-4 1-2 1-2 3-3h0 3v-2-2l1 1v2h2 0l2-1 1 1h1v-2h1z" class="J"></path><path d="M245 314l2-1c0 1-1 2-2 4l-1 1h-1c-1 1-1 1-3 2v-1-2c1 1 1 1 1 2h0v-1l1-1 3-3z" class="N"></path><path d="M250 302l2 1c0 1 0 3-1 4 1 1 1 1 1 2s0 1-1 2c0 0-1 0-1 1h1l-1 1h0l-2 2h0c-1 1-1 1-2 1l-1 1c1-2 2-3 2-4v-1c1 0 1 0 1-1 2-2 2-6 2-9z" class="D"></path><path d="M249 298h0c2-1 2-2 2-3 1 1 1 2 1 4h0l1 1 2-2c0 1 0 2 1 2 0 2-1 3 0 5-1 2 0 3 0 4-1 1-1 2-1 2-1 0-2 0-2-2l1-1v-1-1l1-1c-1-1-2-1-3-2v-1c0-1-2-2-3-3v-1z" class="Q"></path><path d="M241 297l1 1v2h2 0l2-1 1 1c0 1-1 1-1 2l2 2h0 0v-3h0l2 1c0 3 0 7-2 9 0 1 0 1-1 1v-1c0 1 0 1-1 1 0-1 0-1-1-2h0 1c1-2 0-4 0-6-1-1-2-3-4-3h-1v-2-2z" class="d"></path><g class="c"><path d="M248 304h0l1 2h-1-1v-2h1z"></path><path d="M231 307l1-1h1l1 2h0c0 1 1 2 1 3 0 0 1 1 2 1 1 1 2 1 3 1 2 0 4-1 5-3 1 1 1 1 1 2 1 0 1 0 1-1v1 1l-2 1-3 3-1 1v1h0c0-1 0-1-1-2-2-1-4-2-7-4-1-1-1-4-2-6z"></path></g><path d="M237 312c1 1 2 1 3 1 2 0 4-1 5-3 1 1 1 1 1 2 1 0 1 0 1-1v1 1l-2 1s-1 1-2 1h0-2c-2 0-4-1-5-2l1-1z" class="I"></path><path d="M241 301h1c2 0 3 2 4 3 0 2 1 4 0 6h-1 0c-1 2-3 3-5 3-1 0-2 0-3-1-1 0-2-1-2-1 0-1-1-2-1-3s0-3 1-4c1-2 1-2 3-3h0 3z" class="F"></path><path d="M237 308c1-1 1-1 2-1v1h2 0 1v-1h1l1 1c-1 1-1 3-3 4l-2-1h0l1-1-1-1c-1-1-1-1-2-1z" class="C"></path><path d="M241 301h1c2 0 3 2 4 3 0 2 1 4 0 6h-1v-2h-1l-1-1h-1v1h-1 0-2v-1c-1 0-1 0-2 1l-1-2h1c1-1 2-2 2-3-1-1-1-1-1-2h0 0 3z" class="B"></path><path d="M285 339l81-1h42c5 0 10 0 15-1 4-1 8-2 12-2-3 1-5 3-7 3-5 2-10 2-14 2h-23-6-16c-3 1-8 1-11 1-1 0-1 1-1 1-1 0-2 0-4 1v3l-1-1v-1h0-2-1c1-1 2-1 3-2h-1c-2 0-2 0-4 2h1l-2 1-1-1c0-1-1-1-2-2h-4-10-3-2l-2 1c0-1-1-2-2-2h-22-12c-2-1-5 0-7 0-4 0-8 1-12 3v-1c1-2 3-3 4-4h14z" class="I"></path><path d="M271 339h14c-1 1-6 0-7 1-3 0-5-1-7 1h1 0c1 0 1-1 2-1h3c1 0 4 0 5-1 1 0 1 0 2 1h4 2c3 0 6 0 8 1h-12c-2-1-5 0-7 0-4 0-8 1-12 3v-1c1-2 3-3 4-4z" class="Z"></path><path d="M321 341c1-1 3-1 5-1h9 34c-3 1-8 1-11 1-1 0-1 1-1 1-1 0-2 0-4 1v3l-1-1v-1h0-2-1c1-1 2-1 3-2h-1c-2 0-2 0-4 2h1l-2 1-1-1c0-1-1-1-2-2h-4-10-3-2l-2 1c0-1-1-2-2-2h1z" class="O"></path><path d="M320 341h1 15c0 1-1 0-2 0-2 0-6 0-8 1h-2l-2 1c0-1-1-2-2-2z" class="H"></path><path d="M189 246l1 1-3 3 1 1h1v1c0 1 1 2 2 3h-2v1c1 0 1 1 2 1v1c0 2 0 2 1 4-1 1-1 1-1 3v2l1 1c-1 1-1 1-1 2h0v2c-1 1-1 3-1 5h0v6l1 2h0l-8-1h0c-1 1-4 1-5 1v3c-1 0-1 0-2 1h-1-1-1v-1-1h1l1-1v-6-5-1-2h0 0v-1c-1-1-1-2-1-2v-1-1c1-2 2-3 4-5l1-2h0c1 0 1 0 1-1 1-1 2-3 3-5l3-3c0-1 1-1 1-2s0-1 1-2c0 0 0-1 1-1z" class="S"></path><path d="M185 266h2v1l-1 2v1h0l-1-1v-3z" class="K"></path><path d="M177 265l1 2v2h-1c1 1 1 2 1 3l-3-3c0-2 0-2 2-4z" class="E"></path><path d="M189 277c-1 0-1-1-2-1v-1l1 1 1-1v-2l1-1-1-1v-2h1l1 1h0v2c-1 1-1 3-1 5h0 0-1z" class="G"></path><path d="M175 275v-1-2h0 0v-1c-1-1-1-2-1-2v-1-1c0 3 3 7 5 9h0l-2 2h0v-3c-1 0-1 0-1-1l-1 1z" class="E"></path><path d="M180 277l1-1v1c1 0 1 1 2 2 0 0 0 1 1 1h0c1 1 1 2 3 2l-1-1 1-1h-1c0-1 0-1 1-1l2-2h1 0v6l1 2h0l-8-1h1c1-3-3-5-4-7h0z" class="T"></path><path d="M187 280h1c1 0 1 1 1 1l-1 2-1-1-1-1 1-1z" class="K"></path><path d="M183 257c0 2 0 3-1 4l1 1h2v3 1 3h-1v-2h-1-3-2l-1-2h0c1-1 2-3 3-4s2-2 3-4z" class="G"></path><path d="M179 265c2 1 3 0 4-1v-1l1 1v3h-1-3l-2-1 1-1z" class="B"></path><path d="M183 257c0 2 0 3-1 4l1 1c-1 0-1 1-1 1-1 1-2 1-3 1l-1 1h1l-1 1 2 1h-2l-1-2h0c1-1 2-3 3-4s2-2 3-4z" class="F"></path><path d="M183 257v-1h1v1l1 1h1c1 0 1-1 2-2v1h1c0 1-1 1-1 2 1 0 2 0 3-1 0 2 0 2 1 4-1 1-1 1-1 3v-1c-1 0-1-1-2-1h-1v1c1 1 0 2 1 3v1c-1 0-1-1-1-1-1-2-1-4-1-5v-2h-1c0 2 1 4 0 5h-1v-3h-2l-1-1c1-1 1-2 1-4z" class="B"></path><path d="M189 246l1 1-3 3 1 1h1v1c0 1 1 2 2 3h-2v1c1 0 1 1 2 1v1c-1 1-2 1-3 1 0-1 1-1 1-2h-1v-1c-1 1-1 2-2 2h-1l-1-1v-1h-1v1c-1 2-2 3-3 4l-1-1c1 0 1 0 1-1 1-1 2-3 3-5l3-3c0-1 1-1 1-2s0-1 1-2c0 0 0-1 1-1z" class="G"></path><path d="M175 275l1-1c0 1 0 1 1 1v3h0l2-2 1 1h0c1 2 5 4 4 7h-1 0c-1 1-4 1-5 1v3c-1 0-1 0-2 1h-1-1-1v-1-1h1l1-1v-6-5z" class="O"></path><path d="M175 280h1v1 8h-1-1-1v-1-1h1l1-1v-6z" class="T"></path><path d="M179 276l1 1h0c1 2 5 4 4 7h-1-1c0-2-3-5-5-6l2-2z" class="C"></path><path d="M281 440c1 1 2 1 4 0v1h1v84c-2 3-4 7-5 10-1 1-2 3-3 4h-2 0c0-5 5-10 5-15 1-3 0-6 0-9v-17-58zm0-151c5-5 11-10 16-15l14-12 4-4h12 29 21l-32 26h-1c-1 1-2 1-3 2v1h-1 0c-1-2 2-3 3-5v-1l22-19h-34-9-4c-5 2-9 8-13 11-5 4-10 8-14 13-3 2-6 5-9 7-2 1-3 3-5 4v-4l1-1c1-1 2-2 2-3h1z" class="I"></path><path d="M208 109c0 1-1 2 0 3 6-1 11-4 16-6v2 3c-1 3-2 8-3 11v1c-1 2-1 5-2 7l-1 6v-3h-2c1-1 1-3 1-4v-1c0-1-3-1-4-1h-2-1-5-3c-2 1-5 0-7 0-1 1-2 1-3 1v-1c-1-1-2 0-3 0-2 0-4-2-6-3l-1-1c0-1 1-1 1-2v-1c1-2 1 0 2 0s1-1 1-1l-3-3 1-1h1 1 0c1 0 1 0 2-1l1 1h3c1 1 2 0 3 0 2 1 4 0 6-1 0-1 2-1 3-1 1-1 1-2 2-3l1-1h1z" class="C"></path><path d="M188 121h2l1 1c-1 1-1 1-2 1h-2v-1c0-1 1 0 1-1z" class="E"></path><path d="M194 119l1 1 1-1h1c0-1 0-1 1-1h4c0 1 0 1-1 2h0c-1 0-2 0-3 1h-3v2l2-1c0 1 1 1 1 2l-3 1c-1 0-1-1-2-1l-2 1-2-2c1 0 1 0 2-1l-1-1h-2l-1-1c2 0 3 0 4-1h3z" class="G"></path><path d="M191 119h3 0c0 1-1 1-2 1v1 1h-1l-1-1h-2l-1-1c2 0 3 0 4-1z" class="M"></path><path d="M188 114l1 1v2h5c2 0 1 1 2 1 2-2 5-2 7-3v2l-1 1h-4c-1 0-1 0-1 1h-1l-1 1-1-1h-3c-1 1-2 1-4 1l-1-1-3-3 1-1h1 1 0c1 0 1 0 2-1z" class="C"></path><path d="M188 114l1 1v2h5 0c-2 1-4 0-5 0 0 1 0 1 1 2h1c-1 1-2 1-4 1l-1-1-3-3 1-1h1 1 0c1 0 1 0 2-1z" class="T"></path><path d="M188 114l1 1v2h0c-2 0-3-1-4-2h1 0c1 0 1 0 2-1z" class="J"></path><path d="M203 115c1 0 3 0 4-1s4-1 5-1v1l-1 1h-3-1c0 1 0 1-1 2h0 0v3l-1 1h-1 0c-1 0-2 1-3 1l1 1h0c1-1 2-1 3-1v1c-1 0-1 0-2 1h-1-1-3c0-1-1-1-1-2l-2 1v-2h3c1-1 2-1 3-1h0c1-1 1-1 1-2l1-1v-2z" class="B"></path><path d="M208 109c0 1-1 2 0 3 6-1 11-4 16-6v2c-1 0-2 1-3 1l-4 2c-1 0-1 1-2 1s-2 0-3 1c-1 0-4 0-5 1s-3 1-4 1c-2 1-5 1-7 3-1 0 0-1-2-1h-5v-2h3c1 1 2 0 3 0 2 1 4 0 6-1 0-1 2-1 3-1 1-1 1-2 2-3l1-1h1z" class="L"></path><path d="M206 110l1-1h1l-1 3c-1 1-1 1-3 2v-1c1-1 1-2 2-3z" class="X"></path><path d="M221 109c1 0 2-1 3-1v3c-1 3-2 8-3 11v1c-1 2-1 5-2 7l-1 6v-3h-2c1-1 1-3 1-4v-1c0-1-3-1-4-1h-2-1c-1 0-2 0-3-1h0l-1-1-1 1h-1c1-1 1-2 2-2l-1-1v-1c-1 0-2 0-3 1h0l-1-1c1 0 2-1 3-1h0 1l1-1v-3h0 0c1-1 1-1 1-2h1 3l1-1v-1c1-1 2-1 3-1s1-1 2-1l4-2z" class="U"></path><path d="M221 109c-1 2-1 2-3 3 0 1-1 1-2 2v1c1 0 1 1 1 1h-2v-2h-3v-1c1-1 2-1 3-1s1-1 2-1l4-2z" class="E"></path><path d="M211 126c0-1 1-1 2-2 2 0 0-1 1-2h2l2 2h1c-1 1-1 2-3 3l-5-1z" class="H"></path><path d="M219 124c0-1 1-1 2-2v1c-1 2-1 5-2 7l-1 6v-3h-2c1-1 1-3 1-4v-1c0-1-3-1-4-1h-2v-1l5 1c2-1 2-2 3-3z" class="P"></path><path d="M206 117h1c0 1 1 1 1 2h4 1c0 1 1 1 1 2h-3c-1 0-2 2-3 2-1 1-1 1-2 1l-1-1v-1c-1 0-2 0-3 1h0l-1-1c1 0 2-1 3-1h0 1l1-1v-3h0 0z" class="E"></path><path d="M421 331l13 1c2 0 4-1 6 0v1c-1 1-3 1-5 1-4 1-9 1-14 1h-19-91-24c-5 0-11-1-16 0-2 0-4 1-6 2-3 2-6 3-9 6-1 0-2 1-3 1s-1-1-1-2v-1c1-1 3-2 4-3 4-2 8-5 13-6 6-1 12 0 18 0h29l105-1z" class="I"></path><path d="M298 300c5-2 11-1 16-1v1h3 3v3 8h1v-5h0l1 8v4-1c1-1 1-2 1-2h1v2h1v2h-1l1 1v3 1 1l-29 1-2-5h8l-1-7c-1-4-1-9 0-13 0 0 1-1 0-1 0-1-2 0-3 0z" class="T"></path><path d="M322 318v-1c1-1 1-2 1-2h1v2h1v2h-1v6c-1-3-2-5-2-7z" class="L"></path><path d="M302 318c-1-5-1-10-1-15 0 1 0 3 1 4 0-1 0-1 1-1h0v2 2 1h1 0c0 1 0 2 1 3-1 0-1 0-2 1v2l-1 1z" class="b"></path><defs><linearGradient id="b" x1="315.755" y1="321.124" x2="317.16" y2="316.679" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#858484"></stop></linearGradient></defs><path fill="url(#b)" d="M315 314v-3h1v4c1 0 1-1 2-2l2 2v1 2l1 1h0c0 1 1 2 1 3v1h-1-9c-2 0-4-1-5-1h0v-1c2 1 5 1 6 0h0c-1-1-1-1-2-1 1 0 2-1 3-1 1-2 1-3 1-5z"></path><path d="M321 323c-2-1-3-1-4-2v-1l3 1 1 1h1v1h-1z" class="L"></path><path d="M308 310c1 0 2 0 3-1 1 0 1 0 2 1s0 4 2 5v-1c0 2 0 3-1 5-1 0-2 1-3 1 1 0 1 0 2 1h0c-1 1-4 1-6 0v1l-1 1v-1h-1v1h-1v-2h0c-1-1-1-2-2-3l1-1v-2c1-1 1-1 2-1-1-1-1-2-1-3h0l1-1 1 2h2l-1-1 1-1z" class="D"></path><path d="M307 311l1-1v1h4v1l-1 1c-2 1-3 1-5 1-1-1-1-2-1-4l1 2h2l-1-1z" class="f"></path><path d="M304 321v-5-1c1 1 1 3 2 4 1-1 1-2 2-3h0v2 2h3c1 0 1 0 2 1h0c-1 1-4 1-6 0v1l-1 1v-1h-1v1h-1v-2h0z" class="W"></path><path d="M301 303l1-3c4-1 8-1 12 0h3 3v3 8h1v8h0l-1-1v-2-1l-2-2c-1 1-1 2-2 2v-4h-1v3 1c-2-1-1-4-2-5s-1-1-2-1c-1 1-2 1-3 1l-1 1 1 1h-2l-1-2-1 1h-1v-1-2-2h0c-1 0-1 0-1 1-1-1-1-3-1-4z" class="Z"></path><path d="M303 308c0-1 0-2 1-3h1v4l-2 1v-2z" class="I"></path><path d="M307 311v-1h0c1-1 1-1 2-1 0-1-2-2-2-2 0-1 1-1 1-2h0 1l1-1c1 0 2 1 2 2s0 2 1 3h0c2 0 2 1 3 2h1l2 2h0c1 0 1-1 1-1v-1h1v8h0l-1-1v-2-1l-2-2c-1 1-1 2-2 2v-4h-1v3 1c-2-1-1-4-2-5s-1-1-2-1c-1 1-2 1-3 1l-1 1z" class="b"></path><path d="M375 363h1v9c2 0 4-1 5 0h66 16c3 0 6 0 9 1 1 0 2 0 2 1s0 1-1 1c-2 1-7 0-9 0h-24l-79 1h-29c-5 0-11 0-16-1-1 0-3-1-4-2-2-2-5-4-8-5-4-1-8 0-11-1h0l3-1c4 1 7 0 11 1s8 4 12 5c2 1 3 0 4 0h9 23 5v-1h-3 0 0c2 0 3 0 4-1h14c1-1 0-5 0-7z" class="I"></path><defs><linearGradient id="c" x1="371.191" y1="374.718" x2="364.44" y2="367.398" xlink:href="#B"><stop offset="0" stop-color="#232124"></stop><stop offset="1" stop-color="#40403d"></stop></linearGradient></defs><path fill="url(#c)" d="M375 363h1v9c2 0 4-1 5 0h-26 5v-1h-3 0 0c2 0 3 0 4-1h14c1-1 0-5 0-7z"></path><defs><linearGradient id="d" x1="293.99" y1="369.306" x2="354.472" y2="362.275" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#313030"></stop></linearGradient></defs><path fill="url(#d)" d="M319 355l1 1h3l-1 2 1 1h2 4c1 0 2 1 3 0h3c1 0 2-1 3-1l1 1c2 1 4 1 6 0l1 1c0 1 0 1 1 1v1s0 1 1 1l-1 1h0c-1 0-1 0-2 1h-2v2l1 1 1-1 3 1v1h2v1c1 1 3 1 4 1h3 0 3v1h-5-23-9c-1 0-2 1-4 0-4-1-8-4-12-5s-7 0-11-1h-3v-7l8 1c2-1 3-1 5-1v-1c3-1 7-1 10 0l1 1 1 2h2c-1-1-1-2-2-2 0-1 1-3 1-4z"></path><path d="M319 362c1 1 2 3 3 4-1 0-3 0-5-1 1-1 1-2 2-3h0z" class="K"></path><path d="M306 358c3-1 7-1 10 0l1 1h0c-1 0-1 0-2 1h-14c2-1 3-1 5-1v-1z" class="h"></path><path d="M319 355l1 1h3l-1 2 1 1h2 4c1 0 2 1 3 0h3c1 0 2-1 3-1l1 1c2 1 4 1 6 0l1 1c0 1 0 1 1 1v1s0 1 1 1l-1 1h0c-1 0-1 0-2 1h-2v2l1 1 1-1 3 1v1h2v1h-10c-6 0-13 0-18-4-1-1-2-3-3-4l-1-1h2c-1-1-1-2-2-2 0-1 1-3 1-4z" class="e"></path><path d="M334 364c1 0 1 1 2 2v1h-3-2v-1l3-2z" class="B"></path><path d="M329 359c1 0 2 1 3 0h3c0 1-1 2-2 2v1h-1-5-1-1l1-1c0-1 2-1 3-2z" class="J"></path><path d="M319 355l1 1h3l-1 2 1 1h2 4c-1 1-3 1-3 2l-1 1h0l1 1c1 1 1 2 3 2 0 1 0 2 1 3h0-3c0-1 0-1-1-1-2 0-3-2-5-4-1 0-1-1-1-2-1-1-1-2-2-2 0-1 1-3 1-4z" class="Y"></path><path d="M321 361c-1-1-1-2-2-3h1s1 1 2 1v-1l1 1h2l-1 1v1h-3z" class="N"></path><path d="M325 359h4c-1 1-3 1-3 2l-1 1h0c-1 1-1 0-2 0v1c-1 0-1-1-2-2h3v-1l1-1z" class="D"></path><path d="M339 359c2 1 4 1 6 0l1 1c0 1 0 1 1 1v1s0 1 1 1l-1 1h0c-1 0-1 0-2 1h-2v2l1 1 1-1 3 1v1h-7-5v-2h0v-1c-1-1-1-2-2-2l1-1c1-1 2 0 3-1v-2h0l1-1z" class="G"></path><path d="M343 368h-2l-1-1-2-2h-2v-1l1-1v1h4c1 1 2 2 2 4z" class="B"></path><path d="M339 359c2 1 4 1 6 0l1 1c0 1 0 1 1 1v1s0 1 1 1l-1 1h0c-1 0-1 0-2 1h-2v2l1 1h-1c0-2-1-3-2-4l-1-1-2-3 1-1z" class="a"></path><path d="M340 363h4v-1c1 0 2 1 3 2h0c-1 0-1 0-2 1h-2v2l1 1h-1c0-2-1-3-2-4l-1-1z" class="M"></path><path d="M207 422v-8c2 2 0 6 2 8 4 9 14 17 21 23 3 1 5 3 8 4h0c1 0 2 1 3 1 0 0 1-1 2-1 3 0 7-3 10-5 1-1 2-2 3-2 0 1 0 2 1 3v3 2c1 0 0 0 1 1h0c0 2-1 5 0 7l-2 1-12 2c-2 1-4 0-5 1v-1c-2-1-5-1-7-2-4-4-6-7-11-10-4-4-11-7-14-12 0-2 0-2 1-3 0-3-1-5-1-7v-5z" class="H"></path><path d="M207 427c1 2 3 4 4 6 2 4 5 8 7 11-3-2-8-6-10-10 0-3-1-5-1-7z" class="R"></path><path d="M208 434c2 4 7 8 10 10 1 1 3 3 4 3v-1l-2-2h2 0c2 1 3 2 4 3h0c1 1 1 1 2 1 3 1 6 4 10 3v1l1-1c0 3 1 7 0 10-2-1-5-1-7-2-4-4-6-7-11-10-4-4-11-7-14-12 0-2 0-2 1-3z" class="J"></path><path d="M222 447v-1l-2-2h2 0c2 1 3 2 4 3h0c1 1 1 1 2 1 3 1 6 4 10 3v1 3 1c-6-2-12-5-16-9z" class="i"></path><path d="M256 442c0 1 0 2 1 3v3 2c1 0 0 0 1 1h0c0 2-1 5 0 7l-2 1-12 2c-2 1-4 0-5 1v-1c1-3 0-7 0-10l-1 1v-1c-4 1-7-2-10-3-1 0-1 0-2-1h1c3 1 8 3 10 3l1-1h0c1 0 2 1 3 1 0 0 1-1 2-1 3 0 7-3 10-5 1-1 2-2 3-2z" class="G"></path><path d="M239 451c1 0 4-1 5 0v10c-2 1-4 0-5 1v-1c1-3 0-7 0-10z" class="I"></path><path d="M252 446h1v1 8 1s0 1-1 2h0l-1-1c-1 1-1 1-1 2h-1c-1-1-1-1-2-1v-6h-1-2v-1h0c1 0 2 0 2-1h1c1-1 3-2 4-3l1-1z" class="F"></path><path d="M252 446h1v1 8 1s0 1-1 2h0v-10l-1-1 1-1z" class="H"></path><path d="M247 450c1-1 3-2 4-3l1 1h-2c0 3 0 6-1 8v1c0 1-1 1-1 1v-5l-2-1h-2v-1h0c1 0 2 0 2-1h1z" class="C"></path><path d="M247 450c1 2 1 3 1 5v2h1v-1h0v1c0 1-1 1-1 1v-5l-2-1h-2v-1h0c1 0 2 0 2-1h1z" class="E"></path><path d="M256 442c0 1 0 2 1 3v3 2c1 0 0 0 1 1h0c0 2-1 5 0 7l-2 1v-3l-1 1c-1 0-2-1-2-1v-1-8-1h-1l1-2c1-1 2-2 3-2z" class="K"></path><path d="M253 447h1c1 1 0 2 1 3v-1c1 0 1 0 1-1 1 2-1 6 0 8l-1 1c-1 0-2-1-2-1v-1-8z" class="F"></path><path d="M286 341h12 22c1 0 2 1 2 2-1 1-4 4-4 6h1c0 1 0 2-1 3v2h1v1c0 1-1 3-1 4 1 0 1 1 2 2h-2l-1-2-1-1c-3-1-7-1-10 0v1c-2 0-3 0-5 1l-8-1h-6c-2 0-5 1-7-1h-1l1-1h0v-3c-1 1-1 2-1 4-1 1-3 0-4 0-1-1-1-1-1-2s-1-1-1-1h0l-1-1 1-1h0 5c1-1 1-2 1-3 1 0 0-1 1-1 2-1 2-4 4-6h0c0-1 1-2 2-2z" class="U"></path><path d="M280 349l-1 3h25l2-4 1 1-1 3c3 0 7 1 11 0 0-1 1-2 1-3h1c0 1 0 2-1 3v2h1v1c0 1-1 3-1 4 1 0 1 1 2 2h-2l-1-2-1-1c-3-1-7-1-10 0v1c-2 0-3 0-5 1l-8-1h-6c-2 0-5 1-7-1h-1l1-1h0v-3c-1 1-1 2-1 4-1 1-3 0-4 0-1-1-1-1-1-2s-1-1-1-1h0l-1-1 1-1h0 5c1-1 1-2 1-3 1 0 0-1 1-1z" class="Z"></path><path d="M280 357l1-2h0c1 1 2 0 3 0s2-1 3 0h2v2c-3 0-7 1-9 0z" class="I"></path><path d="M289 355h0 2v1l-1 1v1c2 0 4 0 6-2l1 1v1h3l1-1v-1c1-1 3 0 4 0 1 1 1 1 1 2v1c-2 0-3 0-5 1l-8-1h-6c-2 0-5 1-7-1h-1l1-1h0c2 1 6 0 9 0v-2z" class="X"></path><path d="M209 209s1 1 1 2c-1 5 0 10 0 16 0 2-1 6-1 8 0 1 1 1 1 2h0c-1 1-1 3-1 4v12c-1 2 0 4 0 5l-1 10c0 1 0 3 1 4h0c0 1 0 1 1 1l5 5c3 1 6 3 8 5v1l2 2c0 2 2 2 2 4h1c0 1 1 2 2 2 0 1 1 2 1 3h0v2h-2c-1-1-1-2-3-3-1 0-1-1-2-1v3c0 2 1 5 1 6l-2 1v1l1 1v-1c1 1 1 1 1 2v2h0c1 1 1 4 1 5s-1 1-1 2c0-2 0-2-1-3v-2s-1 0-1-1h0l-1 1v-1h-2-1v2-1h-1-1c0-1 1-2 1-3-1 0-2-1-3-1-1-1-1-3-2-4l-2-4h2v-2l-2-1v-4h0 0l1-1c-1-1-1-2-2-2l-1-2-4-6h0c-2-3-4-4-4-8v-13c0-1 1-2 2-4v2-4h1v-2c1-3 0-7 1-10v5 9c1 0 0 5 0 6h1c0-2-1-15 0-16v-2s1 0 1-1v-9h-1c1-1 0-3 1-4v-2c1-4 0-9 1-14 0-2 0-3 1-4z" class="H"></path><path d="M227 290h1c0 1 1 2 2 2-1 1-2 1-2 1-1 0-1-1-1-2v-1z" class="C"></path><path d="M207 276l3 4 2 3-3 3-4-6 1-1 1-3z" class="N"></path><path d="M207 276l3 4h-2c0 1-1 0-1 0l-1-1 1-3z" class="D"></path><defs><linearGradient id="e" x1="208.986" y1="262.379" x2="198.632" y2="271.922" xlink:href="#B"><stop offset="0" stop-color="#8a8889"></stop><stop offset="1" stop-color="#abaaac"></stop></linearGradient></defs><path fill="url(#e)" d="M204 251l1 13c0 3 0 7 1 10 0 1 1 2 1 2h0l-1 3-1 1h0c-2-3-4-4-4-8v-13c0-1 1-2 2-4v2-4h1v-2z"></path><path d="M212 283c1 1 1 1 1 2v2c1 1 2 1 3 1h0c1 0 2 0 4 1 3 3 1 10 3 14h0v-2-1l1 1h0v-5c0 2 1 5 1 6l-2 1v1l1 1v-1c1 1 1 1 1 2v2h0c1 1 1 4 1 5s-1 1-1 2c0-2 0-2-1-3v-2s-1 0-1-1h0l-1 1v-1h-2-1v2-1h-1-1c0-1 1-2 1-3-1 0-2-1-3-1-1-1-1-3-2-4l-2-4h2v-2l-2-1v-4h0 0l1-1c-1-1-1-2-2-2l-1-2 3-3z" class="C"></path><path d="M221 294c0 2 1 4 1 7l-2-1h0c0-1 0-1-1-2v-1h-1l1-1v-1h0 1l1-1z" class="Q"></path><path d="M220 300l2 1v4h-2-2c1-1 1-1 1-2v-2h1v-1z" class="G"></path><path d="M220 301l1 1v1h-2v-2h1z" class="F"></path><path d="M218 305h2 2v4h-2-1v2-1h-1-1c0-1 1-2 1-3l-1-1 1-1z" class="S"></path><path d="M218 305h2l1 1-1 1h-2l-1-1 1-1z" class="B"></path><path d="M216 288h0c2 1 4 3 5 6l-1 1h-1 0v1h-1c-1-1-1-2-2-3h1c0-1-1-1-1-2l-1-1 1-2z" class="M"></path><path d="M218 297h1v1c1 1 1 1 1 2h0v1h-1v2c0 1 0 1-1 2l-1 1 1 1c-1 0-2-1-3-1-1-1-1-3-2-4l-2-4h2c2 0 3 0 5-1z" class="G"></path><path d="M219 298c1 1 1 1 1 2h0v1h-1-1c0-1 1-2 1-3z" class="Y"></path><path d="M213 302l1-1s0-1 1-1c0 1 0 1 1 1-1 1-1 2-1 3h1 1c0-1 1-2 1-3h1v2c0 1 0 1-1 2l-1 1 1 1c-1 0-2-1-3-1-1-1-1-3-2-4z" class="T"></path><path d="M212 283c1 1 1 1 1 2v2c1 1 2 1 3 1l-1 2 1 1c0 1 1 1 1 2h-1c1 1 1 2 2 3h1l-1 1c-2 1-3 1-5 1v-2l-2-1v-4h0 0l1-1c-1-1-1-2-2-2l-1-2 3-3z" class="L"></path><path d="M216 291l-1 1h-1 0v-4h1v2l1 1z" class="D"></path><path d="M211 291h0l1 1h0 1v1 2h0v1l-2-1v-4h0z" class="W"></path><path d="M212 283c1 1 1 1 1 2s0 2-1 3h-2l-1-2 3-3z" class="h"></path><path d="M213 296c1 0 1 0 2-1l-1-1 1-1h1c1 1 1 2 2 3h1l-1 1c-2 1-3 1-5 1v-2z" class="Y"></path><path d="M261 223c1 0 1 1 1 1 1 1 1 4 1 5l-1 23c0 1 0 2 1 2h0v13 6h-1v1l-1 1v1h2l-1 2-2 3v1h-1v1l-2 2-1 1h-1c0 1-1 2-2 2l-1 2-1-1v-2c-1 0-1 0-1 1h-1v-1-1-8-17-7-15c0-2 0-5-1-7 3-1 5-2 8-4h0s1 0 1-1l2-1 1-1 1-2z" class="H"></path><path d="M253 284l2 2h0c0 1-1 2-2 2v-4zm0-7c-1-1-1-1-1-2 1-1 2-1 3-1h1v1l-2 2h-1z" class="O"></path><path d="M254 277c0 1 0 4 1 5v4l-2-2v-7h1z" class="K"></path><path d="M256 275v6c0 1-1 3-1 4h2l-1 1h-1 0v-4c-1-1-1-4-1-5l2-2zm1-7v-1c0-1 0-1 1-2h0c0-1-1-1-2-1l-1-1c-1 1-2 1-2 1l-2-2h1 2v-1c1-2 1-4 1-7-1-1 0-2 0-3v1h1 2v3c1 1 1 2 1 4-1 1-1 2-1 3h-1-1v1c1 1 2 1 3 1v1c-1 1-2 2-2 3z" class="C"></path><defs><linearGradient id="f" x1="267.988" y1="247.82" x2="252.269" y2="252.169" xlink:href="#B"><stop offset="0" stop-color="#151516"></stop><stop offset="1" stop-color="#333232"></stop></linearGradient></defs><path fill="url(#f)" d="M261 223c1 0 1 1 1 1 1 1 1 4 1 5l-1 23c0 1 0 2 1 2h0v13 6h-1v1l-1 1v1h2l-1 2-2 3v1h-1v1l-2-1v-9-5c0-1 1-2 2-3v-1c-1 0-2 0-3-1v-1h1 1c0-1 0-2 1-3 0-2 0-3-1-4v-3h-2-1v-1h0v-5l1-12v-6h0s1 0 1-1l2-1 1-1 1-2z"></path><path d="M262 274h-1v-2s0-1 1-2v3 1z" class="P"></path><path d="M260 225v6c0 1-1 1-1 2v-3-4l1-1z" class="G"></path><path d="M259 265c0 2 0 3-1 5 0 1 0 2-1 3h0v-5c0-1 1-2 2-3z" class="O"></path><path d="M260 252c0 1-1 3 0 3v4l-1 1h0v-1c0-2 0-3-1-4v-3h2z" class="F"></path><path d="M260 281h-1c-1-1 0-3 0-5 1 0 2 1 2 1l1 1-2 3z" class="C"></path><path d="M258 250c1-2 0-16 1-20v3c0-1 1-1 1-2v13 8h-2-2-1v-1h0c1-1 2-1 3-1v1-1z" class="S"></path><path d="M259 226v4c-1 4 0 18-1 20v1-1c-1 0-2 0-3 1v-5l1-12v-6h0s1 0 1-1l2-1z" class="i"></path><path d="M257 227v16c0 2-1 5 0 7h1v1-1c-1 0-2 0-3 1v-5l1-12v-6h0s1 0 1-1z" class="B"></path><path d="M326 342h3 10 4c1 1 2 1 2 2l1 1 2-1h-1c2-2 2-2 4-2h1c-1 1-2 1-3 2h1 2 0v1l1 1v-3c2-1 3-1 4-1h0c0 1-1 1-2 2h0 3l1-1 1 2h0l-2-1v1 1c-1-1-1-1-3-1 0 1 1 1 1 2 1 1 2 1 3 1 1 1 2 2 3 4l-1 1c1 1 1 2 1 3h1l1-1v3h0c1 1 1 2 2 2 0 0 0-1 1-2 1 0 2 1 2 1h5v1c1 1 1 1 2 1 0 1-1 1-1 2 0 2 1 6 0 7h-14c-1 1-2 1-4 1h0-3c-1 0-3 0-4-1v-1h-2v-1l-3-1-1 1-1-1v-2h2c1-1 1-1 2-1h0l1-1c-1 0-1-1-1-1v-1c-1 0-1 0-1-1l-1-1c-2 1-4 1-6 0l-1-1c-1 0-2 1-3 1h-3c-1 1-2 0-3 0h-4-2l-1-1 1-2h-3l-1-1v-1h-1v-2c1-1 1-2 1-3h-1c0-2 3-5 4-6l2-1h2z" class="g"></path><path d="M340 349c1 1 2 1 3 1v-1l1 1v2h-4 0v-3z" class="J"></path><path d="M344 350h1 0c0 2 1 2 2 3l1 1c-1 0-1 1-1 1 0 1 0 2 1 3h0l2 2h-1c-1 0-1 0-2-1h-1-1c-2 1-4 1-6 0l-1-1v-2s1-1 1-2h4l1-1v-1-2z" class="I"></path><path d="M344 350h1 0c0 2 1 2 2 3l1 1c-1 0-1 1-1 1 0 1 0 2 1 3h0l2 2h-1c-1 0-1 0-2-1h-1v-2-3h-3l1-1v-1-2zm-15 4l2-1h2c1 0 2 0 3 1l1 1c-1 2-1 3-2 4h-2-1c-1 1-2 0-3 0h-4-2l-1-1 1-2c0-1 0-1 2-2 0 1 0 2 1 3v-1-2h3z" class="V"></path><path d="M330 356l1-2c1 0 1 0 3 1v1c0 1-1 2-1 2h-3v-2z" class="O"></path><path d="M326 354h3c1 1 1 1 1 2v2 1h-7l-1-1 1-2c0-1 0-1 2-2 0 1 0 2 1 3v-1-2z" class="c"></path><path d="M322 343l2-1c0 1 0 2 1 2 0 1 0 2 1 2h3c1-1 2 0 3 0h0l-1 2v1s-1 0-2 1v1h0c-1 0-1 0-1 1h-2v2 2 1c-1-1-1-2-1-3-2 1-2 1-2 2h-3l-1-1v-1h-1v-2c1-1 1-2 1-3h-1c0-2 3-5 4-6z" class="D"></path><path d="M331 348v1s-1 0-2 1v1h0c-1 0-1 0-1 1l-2-2v-2h5z" class="N"></path><path d="M318 352l1 1 2-1c0-1-1-1 0-2h1 1c-1 1-2 3-3 4h1l1-1s2-1 2-2v-1c1 1 1 3 1 4h0c-2 1-2 1-2 2h-3l-1-1v-1h-1v-2z" class="b"></path><path d="M326 342h3 10 4c1 1 2 1 2 2l1 1 2-1h-1c2-2 2-2 4-2h1c-1 1-2 1-3 2h1 2 0v1l1 1v-3c2-1 3-1 4-1h0c0 1-1 1-2 2h0 3l1-1 1 2h0l-2-1v1 1c-1-1-1-1-3-1 0 1 1 1 1 2 1 1 2 1 3 1 1 1 2 2 3 4l-1 1c-2-1-2-2-3-3l-1 1h-1c-1 1-3 1-3 3v1c1 0 1-1 2 0 0 0 0 1 1 1-1 1-2 1-3 2h-1s0 1-1 1h-1c-1-1-1-2-1-3l-1 2c-1-1-1-2-1-3 0 0 0-1 1-1l-1-1c-1-1-2-1-2-3h0-1l-1-1v1c-1 0-2 0-3-1 1-1 1-1 2-1l-1-1h-1c-1 1-1 1-1 2-1 0-1 0-1 1l-1-1c1-1 1-1 1-2h-1c-1 1-1 2-1 3h-3 0c0-1 1-1 1-2h-3l1-2h0c-1 0-2-1-3 0h-3c-1 0-1-1-1-2-1 0-1-1-1-2h2z" class="e"></path><path d="M348 350c0-1 0-1-1-2h1 0l1-1 2 1h0v-2h1 1c0 1-1 2-1 3l-1 1h-1c-1 0-1-1-2 0z" class="D"></path><path d="M349 356c1-2 2-4 4-5-1 2-2 3-2 5l1 2s0 1-1 1h-1c-1-1-1-2-1-3z" class="c"></path><path d="M326 342h3c1 1 0 1 0 2-1 1-1 1-3 1l-1-1h0c-1 0-1-1-1-2h2z" class="J"></path><path d="M341 347l-2-1h1l2-1-1-1 1-1c1 1 1 1 2 1v1l-1 1c0 1 1 2 1 3h-1v1c-1 0-2 0-3-1 1-1 1-1 2-1l-1-1z" class="G"></path><path d="M352 349c1-1 2-1 2-3l1 1v1h-1 0c-1 2-2 2-4 3-1 1-1 2-2 3h0l-1-1c-1-1-2-1-2-3h0-1l-1-1h1 2c1 0 1 1 2 1 1-1 1 0 2 0h1l1-1z" class="f"></path><path d="M357 351l1-1c1 1 1 2 3 3 1 1 1 2 1 3h1l1-1v3h0c1 1 1 2 2 2 0 0 0-1 1-2 1 0 2 1 2 1h5v1c1 1 1 1 2 1 0 1-1 1-1 2 0 2 1 6 0 7h-14c-1 1-2 1-4 1h0-3c-1 0-3 0-4-1v-1h-2v-1l-3-1-1 1-1-1v-2h2c1-1 1-1 2-1h0l1-1c-1 0-1-1-1-1v-1c-1 0-1 0-1-1l-1-1h1 1c1 1 1 1 2 1h1l-2-2h0l1-2c0 1 0 2 1 3h1c1 0 1-1 1-1h1c1-1 2-1 3-2-1 0-1-1-1-1-1-1-1 0-2 0v-1c0-2 2-2 3-3h1z" class="W"></path><path d="M359 365l-2-1-2 2c0-1-1-2-1-3h0c1 0 1 1 2 1h0v-1c1-1 1 0 2 0 1 1 1 1 1 2z" class="T"></path><path d="M358 363h1c0-1 0-1 1-1s1 2 2 3h-1l-1 2h0l-1-2c0-1 0-1-1-2z" class="Y"></path><path d="M357 358l2-2h1 1c-1 2-2 3-3 4h-3v-1h1c1 0 1 0 1-1z" class="V"></path><path d="M349 356c0 1 0 2 1 3h1 0-1c0 1 1 2 2 2l-1 1-1-1-1 1c-1 0-2 0-2-1-1 0-1 0-1-1l-1-1h1 1c1 1 1 1 2 1h1l-2-2h0l1-2z" class="g"></path><path d="M352 358h1c1 0 1 1 2 1v1h3v2h-3 0c-1 1-1 0-2 0v-2l-2-1h0c1 0 1-1 1-1z" class="D"></path><path d="M352 358h1c1 0 1 1 2 1v1h-2l-2-1h0c1 0 1-1 1-1z" class="b"></path><path d="M356 356c-1 0-1-1-1-1-1-1-1 0-2 0v-1c0-2 2-2 3-3h1l3 3h-1-2v1 3c0 1 0 1-1 1h-1c-1 0-1-1-2-1 1-1 2-1 3-2z" class="N"></path><path d="M356 356h0l1 1v-2 3c0 1 0 1-1 1h-1c-1 0-1-1-2-1 1-1 2-1 3-2z" class="h"></path><path d="M366 365h1v-2-1l1 1c1 0 1 0 2-1l-1-1v-1l2 2h0l1-2v1c1 0 1-1 2-1 1 1 1 1 2 1 0 1-1 1-1 2 0 2 1 6 0 7h-8v-2-2l-1-1z" class="M"></path><path d="M362 365c0-1 1-2 1-2 1 1 1 1 1 2h2l1 1v2 2h8-14c-1 1-2 1-4 1h0-3c-1 0-3 0-4-1v-1h-2v-1l-3-1-1 1-1-1v-2h2c1-1 1-1 2-1l1 1h1v-1h3l-1 1h-2c0 1 1 1 1 2-1 0-1 0-2 1h1 2v-1s1-1 2-1h2l-1 1h-2l1 1c1 0 1 0 3-1v1h1 2l1-1 1-2h1z" class="R"></path><path d="M354 371c1-1 3-1 4-1v-1c2 0 2 1 3 1-1 1-2 1-4 1h0-3z" class="T"></path><path d="M345 365c1 0 2 0 3 1h-1c-1 0-2 1-2 1l-1 1-1-1v-2h2z" class="L"></path><path d="M215 214c-1-2-1-20 0-21v13c0 3 0 6 1 9s3 5 4 8c1 1 2 3 4 3v1 1c-1 2 0 5 0 7v8 9 1 11c0 1 0 2 1 3h0v4c2 1 3-1 4 1v3c0 1 1 1 2 1v1 2l1 3c-1 1-1 3-1 5-1 2 0 4 0 5-1 0-1-1-1-1l-1-1h-1-1c0-2-2-2-2-4l-2-2v-1c-2-2-5-4-8-5l-5-5c-1 0-1 0-1-1h0c-1-1-1-3-1-4l1-10c0-1-1-3 0-5v-12c0-1 0-3 1-4h0c0-1-1-1-1-2 0-2 1-6 1-8 0-6-1-11 0-16h2s1 1 1 2v1h2z" class="E"></path><path d="M225 278v-4h1c0 1 0 1 1 2v1 1h-2z" class="G"></path><path d="M225 278h2v1 3h0v1h-1c0-2 0-3-1-5z" class="B"></path><path d="M224 252v1 11c0 1 0 2 1 3h0v4-1h-1v1h-1c0-2-1-6 0-8 0-3 0-8 1-11z" class="S"></path><path d="M229 279c1-1 1-2 2-2v2l1 3c-1 1-1 3-1 5-1 2 0 4 0 5-1 0-1-1-1-1v-3c0-1 0-1-1-1h0c-1-1-1-6-1-7v-1h1z" class="P"></path><path d="M229 279c1-1 1-2 2-2v2c0 1 0 1-1 1l-1-1z" class="O"></path><path d="M220 223c1 1 2 3 4 3v1 1c-1-1-1-1-1-2l-1 1c1 4 0 8 0 13l1 13c-2-2 0-4-1-6-1-1-1-2-1-2v3 9h0l-1-34zm-5-9c1 2 1 9 1 12l1-2v3-6h1c1 3 0 9 0 12v7c-1 1-2 1-3 2h-2c-1 0-1-1-2-1v-1c1-1 2 0 3-1v-11c0-1 1-3 0-4v-5l-1-5h2z" class="B"></path><path d="M210 211h2s1 1 1 2v1l1 5v5c1 1 0 3 0 4v11c-1 1-2 0-3 1v1c1 0 1 1 2 1h2c1-1 2-1 3-2v4 12 5 4h0l1 1s0 1-1 1c0 0 1 1 1 2 1 1 1 1 1 2h1 0l1 1v4l-1 1v-1c-1 1-1 2-1 3s1 2 2 3c3 1 5 6 7 8h-1-1c0-2-2-2-2-4l-2-2v-1c-2-2-5-4-8-5l-5-5c-1 0-1 0-1-1h0c-1-1-1-3-1-4l1-10c0-1-1-3 0-5v-12c0-1 0-3 1-4h0c0-1-1-1-1-2 0-2 1-6 1-8 0-6-1-11 0-16z" class="K"></path><path d="M214 219l-1 1-1-1c0-2 0-5-1-7 1 0 2 1 2 1v1l1 5z" class="E"></path><path d="M209 272h1c1 1 2 2 4 3 1 0 1-1 3 0h0c0 1 0 1-1 1 0 1 0 1-1 2l-5-5c-1 0-1 0-1-1z" class="O"></path><path d="M215 255c1 1 1 3 1 4l1 3 1-1v4h0l1 1s0 1-1 1c0 1 0 2-1 4l1 1h-2c-2 0-2 0-2-1-1 0-1-1-1-1-1 0-1-2-1-2 0-1-1-1-1-2h1c1 0 1 1 2 2h1c1-2 0-5 0-6v-7z" class="G"></path><path d="M217 262l1-1v4h0c-1 1-1 1-1 2l-1 1c0-2 0-4 1-6z" class="B"></path><path d="M218 265l1 1s0 1-1 1c0 1 0 2-1 4l1 1h-2 1c-1-2 0-3-1-4l1-1c0-1 0-1 1-2zm-3-23c1-1 2-1 3-2v4 12 5l-1 1-1-3c0-1 0-3-1-4v-1c-2 0-4-1-5-2h0 2c1 1 2 1 3 1v-1-6l-2-1c0-1 0-1 1-1v-1l-1-1h2z" class="F"></path><path d="M215 242c1-1 2-1 3-2v4h-1-1-1v-2z" class="K"></path><path d="M205 280h0l4 6 1 2c1 0 1 1 2 2l-1 1h0 0v4l2 1v2h-2l2 4c1 1 1 3 2 4 1 0 2 1 3 1 0 1-1 2-1 3h1 1v1 1l-2 1c1 0 2 1 3 1-1 2-1 3-1 4v1 1c0 1 0 3-1 4v1c-1 0-1-1-2-1v2c0 1-2 2-3 3v-3h-1c-1 1-1 2-1 4v4 1h0l-1-1-1-1-1 1v-1-1h-1c2-4 2-9 3-14h0l-1-1c-1-1-4-1-5 0 1 4 2 7 0 11 0-1-1-1-1-2 1 0 1-1 1-2v-1c0-1 0-1-1-2l-7-3 1-2h-3c-1-1-1-2-2-4h0s1 0 1-1v-1c1 0 1-1 1-2s0-1-1-2c-2 0-3 1-4 1v1l-1-1h0c-2 0-2 0-4 1 0-2 1-3 2-4s3-2 4-3v-4c1-1 1-1 1-2-1 0-2-1-2-1h-1 0-3v-1h-1v1h-1v-1 1c-1 0-1 0-1 1-1 0-2 0-4 1-2-1-3 0-5 1v-3c0-1 0-2 1-3h3c1-1 1-2 1-3h0v-3c1 0 4 0 5-1h0l8 1c1 1 2 1 4 1l2 1h0l1-1c1-1 2-1 3-1h0c1-1 2-2 2-3 0 0 1 0 1-1l1-1z" class="I"></path><path d="M215 306c1 0 2 1 3 1 0 1-1 2-1 3h1 1v1 1l-2 1-1 1c1-3-1-6-1-8z" class="e"></path><path d="M211 310c1 3 2 6 2 9-1-1-1-2-1-3h-2c0-1-1-1-2-3 1 0 1-1 2-1l1-2h0z" class="J"></path><path d="M216 314l1-1c1 0 2 1 3 1-1 2-1 3-1 4v1 1c0 1 0 3-1 4 0-1 0-3-1-5 0-2 0-4-1-5z" class="W"></path><path d="M197 293c3 2 7 4 9 7h-1l-2-1h-1l-5-4v-2z" class="a"></path><path d="M202 299h1l2 1h1c2 2 4 6 5 10h0l-1 2-2-1v-1c-1-1 0-1 0-2h0c-1-1-1-2-1-3h-1c-1 0 0-2-1-2 0-1-1-1-1-2-1 0-2-1-2-2z" class="g"></path><path d="M208 308v-1h1v2s1 1 2 1l-1 2-2-1v-1c-1-1 0-1 0-2z" class="N"></path><path d="M196 295h1l5 4c0 1 1 2 2 2 0 1 1 1 1 2 1 0 0 2 1 2h1c0 1 0 2 1 3-2 2-3 1-5 2 0-2 0-2 1-4v-1c-1 0-1-1-1-1l-4 1h-1c0 1-1 2-1 3s-1 1-1 1v-2-1-2c1-2 1-5 1-7-1 0-1-1-1-2z" class="K"></path><path d="M200 300h1v1c0 1 0 2-1 3h-1c0-1-1-1-1-1v-2l2-1z" class="C"></path><path d="M196 295h1l5 4c0 1 1 2 2 2l-1 1h-2v-1-1h-1c-1 0-1 0-1-1l-2-2c-1 0-1-1-1-2z" class="S"></path><path d="M205 280h0l4 6 1 2c1 0 1 1 2 2l-1 1h0 0v4l2 1v2h-2c-2-1-3-2-4-4-1-1-3-3-5-4s-3-2-5-3h0l1-1c1-1 2-1 3-1h0c1-1 2-2 2-3 0 0 1 0 1-1l1-1z" class="H"></path><path d="M205 280h0l4 6 1 2c1 0 1 1 2 2l-1 1h0c0-1 0-2-1-2 0-1-3-2-3-4l-1-1c-1-1-1-1-2-3l1-1z" class="B"></path><path d="M202 290v-1c1 0 1 0 1-1l1 1c1 2 3 2 4 4 0 1 0 1 1 1h1v-2l1-1v4l2 1v2h-2c-2-1-3-2-4-4-1-1-3-3-5-4z" class="S"></path><path d="M178 288v1h6c3 1 5 1 8 2 1 0 4 1 5 2v2h-1c0 1 0 2 1 2 0 2 0 5-1 7v2 1 2l-2 2h-1v-1c1 0 1-1 1-2s0-1-1-2c-2 0-3 1-4 1v1l-1-1h0c-2 0-2 0-4 1 0-2 1-3 2-4s3-2 4-3v-4c1-1 1-1 1-2-1 0-2-1-2-1h-1 0-3v-1h-1v1h-1v-1 1c-1 0-1 0-1 1-1 0-2 0-4 1-2-1-3 0-5 1v-3c0-1 0-2 1-3h3c1-1 1-2 1-3z" class="R"></path><path d="M178 289h6v1c-1 0-1 1-2 2h-3c-1-1-1-1-1-3z" class="L"></path><path d="M173 297v-3c0-1 0-2 1-3h3 0c-1 1-2 1-2 1h-1c1 1 2 1 2 2h1 1 0 1l1-1c1 0 1 0 1 1 1-1 1-2 1-2h1v1 1c-1 0-1 0-1 1-1 0-2 0-4 1-2-1-3 0-5 1z" class="G"></path><path d="M184 289c3 1 5 1 8 2 1 0 4 1 5 2v2h-1-1c-1-1-3-2-4-3h0-1v2c-1 0-1-1-1-2h-1-2-1l-1-2v-1z" class="J"></path><path d="M191 295h2c1 0 1 0 1 1s1 2 0 2c0 2 0 3 1 5l-3 3h1c-2 0-3 1-4 1v1l-1-1h0c-2 0-2 0-4 1 0-2 1-3 2-4s3-2 4-3v-4c1-1 1-1 1-2z" class="H"></path><path d="M194 296c0 1 1 2 0 2 0 2 0 3 1 5l-3 3h1c-2 0-3 1-4 1v1l-1-1h0c0-1 1-1 2-1v-1c1-1 2-2 3-2v-1c0-2 0-4 1-6h0z" class="B"></path><path d="M196 309s1 0 1-1 1-2 1-3h1l4-1s0 1 1 1v1c-1 2-1 2-1 4 2-1 3 0 5-2h0c0 1-1 1 0 2v1l2 1c-1 0-1 1-2 1 1 2 2 2 2 3h2c0 1 0 2 1 3v7h-1c-1 1-1 2-1 4v4 1h0l-1-1-1-1-1 1v-1-1h-1c2-4 2-9 3-14h0l-1-1c-1-1-4-1-5 0 1 4 2 7 0 11 0-1-1-1-1-2 1 0 1-1 1-2v-1c0-1 0-1-1-2l-7-3 1-2h-3c-1-1-1-2-2-4h0s1 0 1-1h1l2-2z" class="L"></path><path d="M210 318v-1h1 0v2c-1 0-1 0-1-1h0z" class="Y"></path><path d="M208 333c0-1 1-3 1-4 1-2 1-4 2-5v5 1 4 1h0l-1-1-1-1-1 1v-1z" class="X"></path><path d="M208 308h0c0 1-1 1 0 2v1l2 1c-1 0-1 1-2 1h0l-1 1-1 1c-1-1-2-2-2-3h-3 0v1h2v1h1c0 1 0 1-1 1v1h0c-1 1-1 1-2 1v-1h0-2 0c-1-2-1-5 0-6 0-1 0-2 2-2 0 1 1 1 2 2h0 0c2-1 3 0 5-2z" class="T"></path><path d="M208 308h0c0 1-1 1 0 2v1l2 1c-1 0-1 1-2 1h0c-1 0-2-1-4-1 0-1-1-1-1-1v-1h0c2-1 3 0 5-2z" class="J"></path><path d="M208 311l2 1c-1 0-1 1-2 1h0c-1 0-2-1-4-1l1-1h3z" class="D"></path><path d="M196 309s1 0 1-1 1-2 1-3h1l4-1s0 1 1 1v1c-1 2-1 2-1 4h0 0c-1-1-2-1-2-2-2 0-2 1-2 2-1 1-1 4 0 6h0 2 0v1c0 2 0 2 1 3 1 0 1 0 2 1h-1l-7-3 1-2h-3c-1-1-1-2-2-4h0s1 0 1-1h1l2-2z" class="C"></path><path d="M167 191l1-54c-7-4-13-9-19-13l-35-25c-4-2-8-4-11-8h-5c-3 0-6 0-8-1-1 0-1-1-1-1 0-1 2-1 3-1 13-1 27 0 40 0h64 7s1 0 1 1c1 0 1 0 1 1-1 1-2 1-3 1h-4-88l62 43h0v9 17 20c-1 2-1 4-1 6v10l-2 22v-2c-1-1-1-2-1-3v-10-12h0-1z" class="I"></path><path d="M196 88h7s1 0 1 1c1 0 1 0 1 1-1 1-2 1-3 1h-4 3c1-1 0-2 1-2l-6-1z" class="Z"></path><path d="M168 213v-3c1-1 1-3 1-4h0c0-2 1-7 0-8v-4c1-2-1-5 1-6v1c0-2 0-4 1-5l-1-1h1l-1-1 1-9v-10c0-1 0-2 1-3v20c-1 2-1 4-1 6v10l-2 22v-2c-1-1-1-2-1-3z" class="c"></path><path d="M193 306c1 1 1 1 1 2s0 2-1 2v1c0 1-1 1-1 1h0c1 2 1 3 2 4h3l-1 2 7 3c1 1 1 1 1 2v1c0 1 0 2-1 2 0 1 1 1 1 2 2-4 1-7 0-11 1-1 4-1 5 0l1 1h0c-1 5-1 10-3 14-1 2-2 5-5 7l-1 1s-1 0-1 1c-4 2-7 4-11 4-7 1-13-2-18-6l-2-2c-3-3-6-8-6-12-1-3-1-11 0-13 1-1 2-1 3-1v13c1 2 1 4 3 6h0c1-1 1-3 1-4 1 0 1-1 2-2 0 0 0-1 1-2l4-5c0-1 2-3 2-3l1-1 3-3c1-1 1-1 1-2 2-1 2-1 4-1h0l1 1v-1c1 0 2-1 4-1z" class="J"></path><path d="M193 318c0-1 0-1 1-1s1 0 1 1h1v1h-3-1l1-1z" class="D"></path><path d="M179 320v-3c1 1 1 2 1 2 0 1 1 1 1 1h0v-1c1-2 1-2 2-2h0c0 1 0 1-1 2v1l-1 1c2 1 5 1 6 1 2 1 2 1 3 1 1 1 2 1 3 1v1 1h-1c-1 0-1-1-2-1 0 0-1 0-2-1h0c-2 0-5-1-7-2-1-1-2-1-2-2z" class="g"></path><path d="M198 332h1l-1-1 3-3v-1c0-1 0-1 1-1h1c0 1 1 1 1 2-1 2-2 5-3 6s-2 1-2 2c-2-2-1-2-1-4z" class="T"></path><path d="M190 323v-1c1-1 1-2 3-2v1c1 0 1 1 1 1 1 0 2-1 2-1h1c0 1 0 1 1 1h0l2 2v2c-1 0-1 0-1 1l1 1-3 3c0 1-1 1-2 1v-1 1h-2l-2-2c1-1 2-2 2-3h-1c0 1 0 1-1 2h0c-2-1-2 2-4 2h0l3-3c-1-1-2 0-3 0v-1c-1 0-2 0-3-1h0c0 1 0 1 1 1v1h0c-1 0-2-1-4-2l-1-1v-1l1 1 1-1c1 0 3 0 4 1h3l-1-1c1 1 2 1 2 1 1 0 1 1 2 1h1v-1-1c-1 0-2 0-3-1h0z" class="e"></path><path d="M190 323h1s0-1 1-1v-1c1 1 2 1 2 2s-1 1-1 2v-1c-1 0-2 0-3-1h0z" class="a"></path><path d="M193 306c1 1 1 1 1 2s0 2-1 2v1c0 1-1 1-1 1h0c1 2 1 3 2 4h3l-1 2h-1c0-1 0-1-1-1s-1 0-1 1l-2-1v1c0 1-1 1-2 2h-1-1v1l-1-1h0c0-1 0-3-1-4h-1-1-1l1 1c-1 0-1 0-2 2v1h0s-1 0-1-1c0 0 0-1-1-2v3c0 1 1 1 2 2 2 1 5 2 7 2h0l1 1h-3c-1-1-3-1-4-1-2-1-3-2-4-4h0-1v-1l-1 1c-1 1-2 2-2 3l-1-1 4-5c0-1 2-3 2-3l1-1 3-3c1-1 1-1 1-2 2-1 2-1 4-1h0l1 1v-1c1 0 2-1 4-1z" class="M"></path><path d="M184 308c2-1 2-1 4-1h0c-1 1-1 1-1 2-1 0-3 1-4 1 1-1 1-1 1-2z" class="O"></path><path d="M189 320c-1-2-1-2 0-4h1l1 1v1c0 1-1 1-2 2z" class="D"></path><path d="M193 306c1 1 1 1 1 2s0 2-1 2c-1-1-2-2-4-2v-1c1 0 2-1 4-1z" class="P"></path><path d="M179 320c-1-1 0-2 0-3 1-2 3-3 4-5h2v1c-1 1-1 0-1 2 1 0 2-1 2-1 3 0 5 0 7 2h0c-1 0-3-1-4-1-1 1-1 1-2 1v4 1l-1-1h0c0-1 0-3-1-4h-1-1-1l1 1c-1 0-1 0-2 2v1h0s-1 0-1-1c0 0 0-1-1-2v3z" class="a"></path><path d="M173 322l1 1c0-1 1-2 2-3l1-1v1h1 0c1 2 2 3 4 4l-1 1-1-1v1l1 1c2 1 3 2 4 2h0v-1c-1 0-1 0-1-1h0c1 1 2 1 3 1v1c1 0 2-1 3 0l-3 3h0c2 0 2-3 4-2l-8 9c-3-1-6-2-8-4-1-1-2-3-3-3 0 0-1 0-1-1-1-1-1-2-1-4 1 0 1-1 2-2 0 0 0-1 1-2z" class="Y"></path><g class="J"><path d="M172 324h0v2 2c1 1 2 2 3 4l1 1-1 1c-1-1-2-3-3-3 0 0-1 0-1-1-1-1-1-2-1-4 1 0 1-1 2-2z"></path><path d="M177 320h1 0c1 2 2 3 4 4l-1 1-1-1v1l-1 1c1 0 1 0 1 1 1 0 2 1 3 1l-1 1-1-1c-1 0-1 0-2-1 1 1 1 1 1 2l-1 1c0 1-1 1-1 2l1 1v1h-1c0-1-1-1-1-2h-2v-1c0-2-1-5 1-7h0v7h1c1-4-1-7 0-11z"></path></g><path d="M180 325l1 1c2 1 3 2 4 2h0v-1c-1 0-1 0-1-1h0c1 1 2 1 3 1v1c1 0 2-1 3 0l-3 3h0c2 0 2-3 4-2l-8 9c-3-1-6-2-8-4l1-1v1c1 1 2 1 4 1h0v-3h0c0-1 0-1 1-1s1 0 2 1h1 0c0-1-2-2-3-2h-2l1-1c0-1 0-1-1-2 1 1 1 1 2 1l1 1 1-1c-1 0-2-1-3-1 0-1 0-1-1-1l1-1z" class="D"></path><path d="M169 337c-3-3-6-8-6-12-1-3-1-11 0-13 1-1 2-1 3-1v13c1 2 1 4 3 6h0c1-1 1-3 1-4 0 2 0 3 1 4 0 1 1 1 1 1 1 0 2 2 3 3 2 2 5 3 8 4l8-9h0c1-1 1-1 1-2h1c0 1-1 2-2 3l2 2h2v-1 1 1h1s1 0 2-1c0 2-1 2 1 4 0-1 1-1 2-2s2-4 3-6c2-4 1-7 0-11 1-1 4-1 5 0l1 1h0c-1 5-1 10-3 14-1 2-2 5-5 7l-1 1s-1 0-1 1c-4 2-7 4-11 4-7 1-13-2-18-6l-2-2z" class="I"></path><path d="M204 328c2-4 1-7 0-11 1-1 4-1 5 0l1 1h0c-1 5-1 10-3 14-1 2-2 5-5 7v-2c0-2 2-5 4-6h0l1-1v-1-1-1c1-1 0-2 1-2v-2c1-1 0-2 1-3v-3h-3v1 2c1 1 1 2 1 4l-1-1c0 2 0 3-1 4 0 2-2 5-3 7h-1c1-1 2-4 3-6z" class="Z"></path><path d="M169 330h0c1-1 1-3 1-4 0 2 0 3 1 4 0 1 1 1 1 1 1 0 2 2 3 3 2 2 5 3 8 4l8-9h0c1-1 1-1 1-2h1c0 1-1 2-2 3l2 2h2v-1 1 1h1s1 0 2-1c0 2-1 2 1 4-1 1-2 2-3 2-5 3-10 3-15 2-6-2-9-5-12-10z" class="E"></path><path d="M202 363v1l1-1c1 1 2 1 1 2v1 1c-1 2-2 4-2 6-1 4-1 8-1 11v114 20c0 3 0 6 1 9l-1-1h-3c0 1-1 1-2 2 1-6 1-11 1-17l-1-28 1-3c-1-1-3-1-4-2h-2c-2 0-3-1-5 0l-1-1v-1h4c1 1 3 0 4 1 1 0 1 0 2 1l1-1h0c-1 0-1-1-1-1h-1c-1 0 0-1-1-2 1 0 1 0 2 1h1l1-1c-1-1-1-1-2-1l1-1c1-1 1-2 0-3v-2h-2 0l2-1c0-1 0-1 1-2h0l-1-1v-2l1-1-1-1h-1-3-3-2-7-6-1v-1-19-2-8c0-2 0-2 1-3h0c3 2 6 4 8 6 5 4 10 7 15 11v-44c0-8-1-17 0-25 1-3 2-8 5-11z" class="h"></path><path d="M188 448v-1c1-1 2-1 3-2 0 0 1-1 1-2 1 1 1 1 2 1v1h-1v1h3v1c-1-1-2-1-3-1v1c0 1 0 2-1 3h-1c0-1 0-1-1-2h-2z" class="N"></path><path d="M191 450h1c1-1 1-2 1-3v-1c1 0 2 0 3 1-1 0-2 0-2 1h2 0c0 1 0 2 1 3h-2-1-1-3v-1h1z" class="J"></path><path d="M174 426h0v1l2 2c1 0 1 0 1 1h-1 0l-1 1c2 2 2 2 2 4v1h-1v-1-1l-1 1h0v1c-1-1-1-1-1-2v1h-1v2h0v-8c0-2 0-2 1-3z" class="e"></path><path d="M201 384v114c-1-2 0-6 0-8v-3c-1-2-1-4-1-6s1-4 0-5h0v13c-2-9-1-19-1-28v-24c0 2 1 3 1 5l-1 12h1v-5c1-1 0-5 0-6v-31h1v-4c0-5-1-10 0-15v-9z" class="f"></path><path d="M173 437h0v-2h1v-1c0 1 0 1 1 2v-1h0l1-1v1 1h1l-1 2c1 0 1 1 2 1h3 1c-1-1-2-1-3-1l-1-1 1-1c-1 0-1 0-1-1h1s0 1 1 1 2 1 3 2h1c-1-1-1-2-1-3h1c1 0 2 0 2 1v1c0 1-1 1-1 2-1 0-1 0-1 1h1c1 0 1 1 1 1v1 1c2 0 2-3 4-1-1 0-1 1-2 1l-2 2v1c0 1 1 2-1 3h0 0-3v-1c-1 0-1 0-1 1-1 0-2-1-2-1l-3 3-1 1s0 1-1 1l1 2h2s1 0 1 1c-2 1-3-1-5 2v-19-2z" class="J"></path><path d="M179 448h-2v-1l1-1 1 1c1 0 2 0 2-1s0-1-1-1v-1l2-1h1 0c1-1 1-1 1-2h1v1l1 1-1 2v3 1h-3v-1c-1 0-1 0-1 1-1 0-2-1-2-1z" class="N"></path><path d="M182 449c1-2 2-2 2-3v-1h1v3 1h-3z" class="h"></path><path d="M186 446c1 1 1 2 1 3h1v-1h2c1 1 1 1 1 2h-1v1h3 1 1l1 1 1 1-1 1h-2 0 0l2 1h0c-3 2-11-1-13 2 3 1 12-1 13 1-1 1-2 0-3 0 1 1 1 1 2 1h-3-3-2-7-6-1v-1c2-3 3-1 5-2 0-1-1-1-1-1h-2l-1-2c1 0 1-1 1-1l1-1 3-3s1 1 2 1c0-1 0-1 1-1v1h3 0 0c2-1 1-2 1-3z" class="g"></path><path d="M186 446c1 1 1 2 1 3h1c0 1-1 1-1 2-1 0-2 0-3-1l1-1h0c2-1 1-2 1-3z" class="N"></path><path d="M224 227l2 1 3 2 3 1c5 2 9 2 14 2l2-1h0c1 2 1 5 1 7v15 7 17 8 1 1h1c0-1 0-1 1-1v2l1 1 1-2c1 0 2-1 2-2h1l-4 6 1 1c-1 0-1 1-2 2 0 1 0 2-2 3h0-1v2h-1l-1-1-2 1h0-2v-2l-1-1v2 2h-3 0c-2 1-2 1-3 3-1 1-1 3-1 4h0l-1-2h-1l-1 1 1-3-1-1v-2h-1v-2l1-1h-1l-1-1h2v-2h0c0-1-1-2-1-3-1 0-2-1-2-2h1l1 1s0 1 1 1c0-1-1-3 0-5 0-2 0-4 1-5l-1-3v-2-1c-1 0-2 0-2-1v-3c-1-2-2 0-4-1v-4h0c-1-1-1-2-1-3v-11-1-9-8c0-2-1-5 0-7v-1z" class="U"></path><path d="M239 294c1 1 2 1 3 1 0 1-1 1-1 2v2c-1 0-2 0-3-1h0c-1-2-1-2-2-3 1 0 2 0 3-1z" class="a"></path><path d="M248 258l1 3v17l-1 1-1-1c1-1 1-2 1-3h0c0-1 0-2-1-2v-1-1c1-2 1-4 1-6v-7z" class="B"></path><path d="M232 282v9 3l2 1c-1 0-1 0-2 1v1l-1 1h-1l-1-1h2v-2h0c0-1-1-2-1-3-1 0-2-1-2-2h1l1 1s0 1 1 1c0-1-1-3 0-5 0-2 0-4 1-5zm16-50h0c1 2 1 5 1 7v15 7l-1-3v-17-8h-2l2-1z" class="F"></path><path d="M234 295h2c1 1 1 1 2 3h-2l-1 1c-2 1-3 2-3 4v1l-1-1v-2h-1v-2l1-1 1-1v-1c1-1 1-1 2-1z" class="J"></path><path d="M234 295h2c1 1 1 1 2 3h-2v-1c-1-1-2 0-4-1 1-1 1-1 2-1z" class="D"></path><path d="M238 298h0c1 1 2 1 3 1v2h-3 0c-2 1-2 1-3 3-1 1-1 3-1 4h0l-1-2h-1l-1 1 1-3v-1c0-2 1-3 3-4l1-1h2z" class="b"></path><path d="M238 298h0v2h-2l-1-1 1-1h2z" class="d"></path><path d="M238 298c1 1 2 1 3 1v2h-3v-1-2z" class="X"></path><path d="M243 260h0c1-1 0-3 0-4s1-1 1-2h-1c0-1 0-5 1-6l-1-1h1 0c1 2 1 4 1 6 0 1-1 3 0 4v6 16c-1 0-1-1-1-1-1-1 0-3 0-5 0-4 0-8-1-13z" class="G"></path><path d="M252 292l1 1c-1 0-1 1-2 2 0 1 0 2-2 3h0-1v2h-1l-1-1-2 1h0-2v-2l-1-1c0-1 1-1 1-2h2c0-2 0-2 1-3v3h2 1l1 1c1-1 1-1 2-1 0-1 1-2 1-3z" class="L"></path><path d="M242 295h2s1 0 1 1l-1 1v3h0-2v-2l-1-1c0-1 1-1 1-2z" class="N"></path><path d="M249 278v8 1 1h1c0-1 0-1 1-1v2l1 1 1-2c1 0 2-1 2-2h1l-4 6c0 1-1 2-1 3-1 0-1 0-2 1l-1-1v-6c0-3-1-5 0-8v-2l1-1z" class="E"></path><path d="M230 251l1 25c-1 0-2 0-2-1v-3c-1-2-2 0-4-1v-4-2-1h1 2c0 1-1 1-1 1h3v-14z" class="Q"></path><path d="M228 270l-2-1v-4h1l2 1h0v3l-1 1zm11 24v-3c1-1 0-2 0-3 0-3 1-6 0-9h0 0v-2-11-5h1l3-1c1 5 1 9 1 13 0 2-1 4 0 5 0 0 0 1 1 1v11 2c-1 1-1 1-1 3h-2c-1 0-2 0-3-1z" class="H"></path><path d="M224 227l2 1 3 2 3 1c-1 1-2 1-2 2v7 11 14h-3s1 0 1-1h-2-1v1 2h0c-1-1-1-2-1-3v-11-1-9-8c0-2-1-5 0-7v-1z" class="C"></path><path d="M224 227l2 1c-1 0-1 0-1 1v1 6l-1 17v-1-9-8c0-2-1-5 0-7v-1z" class="O"></path><path d="M228 264l1-1v-3h0c-1 0-2-1-2-1-1 0-1 0-1-1 1 0 2 1 3 0l-1-2-1-1c1-1 1-1 2-1l-1-2h0 1l-1-1c1-1 1-8 1-9s-1-1 0-2c-1-3 0-5 0-7h1v7 11 14h-3s1 0 1-1z" class="e"></path><path d="M249 511h1c2 1 4 2 7 2 1 1 3 2 4 2 1 1 2 1 3 2h-1v1c0 2 0 3 1 5-1 0-1 1 0 1 1 2-1 6 0 9h-1l1 1h1c0 1-1 2-1 3 1 1 1 5 1 7h0c0 1 0 1 1 2 0 0 1-1 1 1 0 1-1 3-1 4v4c0 1-1 1-1 2v2 2c0 1-1 1-1 1 1 1 0 1 0 2v2h0c0 1 0 4-1 5 0 1 1 3 0 4v4 1h0 0c0-1 0-1 1-2v-2l1-2v-1-1s1-1 1-2v-1c0-1 0-1 1-2v-2-2l1 1-5 18-1 3-5 23c-1 1-1 2-1 3v-5l-4 16h0c-1 2-1 5-1 7l-6 31c-1 3-1 8-2 11v-7-1h-1v-7c1-7 1-14 2-22 1-5 3-11 3-17l6-31c-3-1-6 0-10-1l2-16c0-3 1-5 2-8v-30-14-4h1v-2h1z" class="B"></path><path d="M264 537c1 1 1 5 1 7h0-2v-3c0-1 1-1 1-1v-3z" class="E"></path><path d="M259 579l4-18c0-1 2-6 1-6v-1l1-1h0v2c-1 1 0 2 0 3-2 7-2 14-3 21h-3z" class="G"></path><defs><linearGradient id="g" x1="253.142" y1="566.779" x2="262.274" y2="579.99" xlink:href="#B"><stop offset="0" stop-color="#3b3b3a"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#g)" d="M258 564l1-2v-1l1 1h2v-1h-1 0-1 0l1-1c1 0 1 0 1 1v3c-2 1-2 1-2 3l-3 20v-3h0-1-1l-1-2 4-18z"></path><defs><linearGradient id="h" x1="260.742" y1="580.52" x2="255.283" y2="610.572" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#989799"></stop></linearGradient></defs><path fill="url(#h)" d="M259 579h3v2c1 0 1 0 1 1l-1 3-5 23c-1 1-1 2-1 3v-5l3-27z"></path><path d="M260 580l1-1v1 1s-1-1-2-1h1z" class="R"></path><path d="M262 581c1 0 1 0 1 1l-1 3c-1-2-1-2-1-4h1z" class="Q"></path><defs><linearGradient id="i" x1="249.539" y1="585.34" x2="256.1" y2="611.713" xlink:href="#B"><stop offset="0" stop-color="#61615f"></stop><stop offset="1" stop-color="#b8b7b8"></stop></linearGradient></defs><path fill="url(#i)" d="M254 582l1 2h1 1 0v3l-4 28v-2h0l-4 1 5-32z"></path><path d="M254 555c1 0 2-1 2-1 1 3 1 5 0 7-1 5-1 10-1 15h0c0-1 1-5 1-6v-1l1-1v-4-3h1v3l-4 18-5 32-1 4c-2 9-3 18-4 27l-1 18v1-1h-1v-7c1-7 1-14 2-22 1-5 3-11 3-17l6-31 2-23h0v-1-5l-1-2z" class="E"></path><path d="M255 557s1 0 1 1 0 4-1 5h0v-1-5z" class="B"></path><defs><linearGradient id="j" x1="248.136" y1="568.918" x2="249.641" y2="585.506" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#j)" d="M255 563h0l-2 23c-3-1-6 0-10-1l2-16c1 0 1 0 1 1 1 0 2-1 4-1h3c2-1 1-3 2-4v-2z"></path><defs><linearGradient id="k" x1="255.944" y1="615.851" x2="238.229" y2="657.436" xlink:href="#B"><stop offset="0" stop-color="#d7d5d7"></stop><stop offset="1" stop-color="#fefdfd"></stop></linearGradient></defs><path fill="url(#k)" d="M249 614l4-1h0v2 2l-1 5c-1 2-1 5-1 7l-6 31c-1 3-1 8-2 11v-7-1l1-18c1-9 2-18 4-27l1-4z"></path><path d="M249 614l4-1h0v2 2l-1-2h0 0s-1 0-2 1l-1 1-1 1 1-4z" class="X"></path><path d="M249 511h1c2 1 4 2 7 2 1 1 3 2 4 2 1 1 2 1 3 2h-1v1h-1c-1 2 0 5-1 7v4 8l-2 16c-1 3-1 6-1 8h-1v3 4l-1 1v1c0 1-1 5-1 6h0c0-5 0-10 1-15 1-2 1-4 0-7 1-4 0-10-1-13l1-1c0-3-1-5 0-7v-8c-1 2-2 4-3 5v1h-1c-1-1-1-2-1-3v-7c-1-2 0-7-1-9-1 0-1 0-1-1h0z" class="H"></path><defs><linearGradient id="l" x1="259.044" y1="514.844" x2="241.168" y2="568.362" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#l)" d="M248 511h1 0c0 1 0 1 1 1 1 2 0 7 1 9v7c0 1 0 2 1 3h1v-1c1-1 2-3 3-5v8c-1 2 0 4 0 7l-1 1c1 3 2 9 1 13 0 0-1 1-2 1l1 2v5 1 2c-1 1 0 3-2 4h-3c-2 0-3 1-4 1 0-1 0-1-1-1 0-3 1-5 2-8v-30-14-4h1v-2z"></path><path d="M255 562l-1 1s-1 0-1-1v-1c1-1-1-16 0-19 0-1 1-2 0-2v-1h1l2 1-1 1c1 3 2 9 1 13 0 0-1 1-2 1l1 2v5z" class="R"></path><path d="M255 541c1 3 2 9 1 13 0 0-1 1-2 1 1-2 1-5 0-8 0-1 0-2 1-3v-2h0v-1z" class="K"></path><path d="M282 293c3-2 6-5 9-7 4-5 9-9 14-13 4-3 8-9 13-11h4 9 34l-22 19v1c-1 2-4 3-3 5h0 1v-1c1-1 2-1 3-2h1c-3 3-6 5-8 7l-1 1c-3 2-7 5-10 8v1l-1-1h-3l-1 6h0v5h-1v-8-3h-3-3v-1c-5 0-11-1-16 1h-5v-1c-3 0-5 0-8 1h1l1 2h-8-5c0-1 1-2 2-2v-1l6-6z" class="U"></path><path d="M276 300h1 8 1l1 2h-8-5c0-1 1-2 2-2z" class="h"></path><path d="M327 277h1v1c2 0 1-1 3-1h0 0c1 1 1 0 2 0v1h1l1-1h4 0c1 1 1 0 2 0h1c0 1-1 2-2 2h-6-3c-1 0-3 1-5 0 0-1 0-1 1-2z" class="W"></path><path d="M319 279l1-1v1 1c0 1 0 2-1 2v1s0 1-1 1v1c-1 1-2 1-2 2v1c-1 2-3 4-4 4h-3 0c-1 1-1 1-1 0l2-2c1 0 1-1 2-1l3-3c3-2 4-4 4-7z" class="C"></path><path d="M295 291c1-1 3-1 5-1 2 1 4 3 6 5v3h-1v1l-2-1s0-1-1-1h0c-1 1-3 0-4-1 0 0 0-1-1-1l-1 1h-1c-1-1-1-2-1-3s0-1 1-2z" class="G"></path><defs><linearGradient id="m" x1="322.46" y1="295.58" x2="339.811" y2="290.387" xlink:href="#B"><stop offset="0" stop-color="#a0a0a0"></stop><stop offset="1" stop-color="#d4d2d4"></stop></linearGradient></defs><path fill="url(#m)" d="M317 300h5c2-1 5-5 7-7l7-6c2-2 4-4 7-6v1c-1 2-4 3-3 5h0 1v-1c1-1 2-1 3-2h1c-3 3-6 5-8 7l-1 1c-3 2-7 5-10 8v1l-1-1h-3l-1 6h0v5h-1v-8-3h-3z"></path><path d="M356 291h17c2 0 6-1 8 0 3 1 6 4 9 6 1 1 3 2 5 2h1 1v2c-2 2-1 3-2 5v9 1 1c-3 3-7 4-10 5l-3 2v1h0-42-15v-1-1-3l-1-1h1v-2h-1v-2h-1s0 1-1 2v1-4l-1-8 1-6h3l1 1v-1c3-3 7-6 10-8l1-1h2 10 7z" class="U"></path><path d="M349 291h7-5v1h16c3 0 8 0 11 1h-35-6l-1-1 1-1h2 10z" class="D"></path><path d="M349 291h7-5v1h-6-1c2-1 4-1 5-1z" class="V"></path><defs><linearGradient id="n" x1="368.173" y1="295.316" x2="369.485" y2="289.248" xlink:href="#B"><stop offset="0" stop-color="#cbcccb"></stop><stop offset="1" stop-color="#eff0f4"></stop></linearGradient></defs><path fill="url(#n)" d="M356 291h17c2 0 6-1 8 0 3 1 6 4 9 6h-2c-1-1-1-1-2-1s-4-2-5-2c-1-1-2-1-3-1-3-1-8-1-11-1h-16v-1h5z"></path><path d="M336 299c1 1 6 0 7 0h34c-4 1-8 0-11 1h-29l1 12v3l-1 1v-6l-1 6h-1l-1-1v-1l-1 1v2h-1c0-2 0-3 1-5l-1-5h0c0-2 0-5 1-7h-3 5l1-1z" class="D"></path><path d="M335 300l1-1v9h-1c-1-2 0-6 0-8z" class="b"></path><path d="M333 300v1c0 1 0 3 1 5v6h-1l-1-5h0c0-2 0-5 1-7z" class="d"></path><path d="M330 300h0 3c-1 2-1 5-1 7h0l1 5c-1 2-1 3-1 5h1v-2l1-1v1l1 1h1l1-6v6h0l1 4c0 2 0 3 1 4l1 1h-15v-1h1c1 0 1 0 1-1h1 1v-3-6h1c0-3-1-7-1-10 0-2 0-3 1-4z" class="Y"></path><path d="M329 320v-6h1v1c0 2-1 5 0 8h0v1l-1-1v1l-1-1h1v-3z" class="R"></path><path d="M330 300h0 3c-1 2-1 5-1 7h0c-1 1 0 4 0 5 0 2-1 3-1 5h-1v-2-1c0-3-1-7-1-10 0-2 0-3 1-4z" class="X"></path><path d="M336 316l1-6v6h0l1 4c0 2 0 3 1 4l1 1h-15v-1h1c1 0 1 0 1-1h1l1 1v-1l1 1h1 3v-4l-1-5 1-1v1l1 1h1z" class="Q"></path><path d="M334 315l1 1h1c0 2 1 7 1 8h-1c-1-2-2-7-2-9z" class="L"></path><path d="M326 301v-1h1 3c-1 1-1 2-1 4 0 3 1 7 1 10h-1v6 3h-1-1c0 1 0 1-1 1h-1v-1-3l-1-1h1v-2h-1v-2h-1s0 1-1 2v1-4l-1-8 1-6h3l1 1z" class="f"></path><path d="M327 300l1 1c0 3 1 9 0 11h-1v-1c-1-2-1-8 0-11z" class="Z"></path><path d="M325 317c0-1 0-3 1-4h1c0 2 1 4 2 6v-15h0c0 3 1 7 1 10h-1v6 3h-1-1c0 1 0 1-1 1h-1v-1-3l-1-1h1v-2z" class="W"></path><path d="M325 323c1-1 1-3 2-4h0c0 1 0 3 1 4h0l1-3v3h-1-1c0 1 0 1-1 1h-1v-1z" class="Y"></path><path d="M321 306l1-6h3l1 1c-1 4-1 10-3 13h-1l-1-8zm17 6l-1-12h29-1l-1 1v11 1c-1-1-1-1-2-1-2-1-5-1-7-1h-12c-2 1-2 2-4 2h0c0 1-1 1-1 2v-3z" class="Z"></path><path d="M338 312h0v-1c1-1 2-1 3-2h0 6l-2 1c-1 0-2 1-2 1-2 1-2 2-4 2h0c0 1-1 1-1 2v-3z" class="d"></path><path d="M347 309h14s1 0 1 1v2c-2-1-5-1-7-1h-12s1-1 2-1l2-1z" class="c"></path><defs><linearGradient id="o" x1="365.063" y1="324.229" x2="365.062" y2="318.353" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#777576"></stop></linearGradient></defs><path fill="url(#o)" d="M386 296c1 0 1 0 2 1h2c1 1 3 2 5 2h1 1v2c-2 2-1 3-2 5v9 1 1c-3 3-7 4-10 5l-3 2v1h0-42l-1-1c-1-1-1-2-1-4l-1-4h0l1-1c0-1 1-1 1-2h0c2 0 2-1 4-2h12c2 0 5 0 7 1 1 0 1 0 2 1v-1-11l1-1h1c3-1 7 0 11-1l13 1-4-4z"></path><path d="M382 324h-4v-1h2c1-1 3-1 5-1l-3 2z" class="T"></path><path d="M364 314c1 0 1 1 1 2l-1 2c0 1 0 1-1 2h-1 0c-1 1-1 1-2 1l-1 1h-1-2c-2 0-4-1-6-1h0c-1 0-3 0-4-1h-2l-1-1h9-2c1 0 2 0 3 1h3c2 0 4-1 6-1 1-1 1-2 2-3v-2z" class="W"></path><path d="M343 319h9-2c1 0 2 0 3 1h3-10-2l-1-1z" class="g"></path><path d="M364 312v-11l1-1 1 7v8c1 1 2 1 3 1h1c2-1 7-1 9-1l6 1v1h1 2 2 1 0 2 1-2c-1 1-1 0-3 0v1h-2-8-6c-1 0-2 0-3 1h0 1 1l1 1h-5-3c-1-1-1-1-1-2l1-2c0-1 0-2-1-2v-2z" class="J"></path><path d="M366 307c0 3 0 3 2 5h2v-1h2 19l1 4h0l1-1c1 0 1 1 2 1v1 1h-1v-2c-1 0-1 1-1 1h-3-5l-6-1c-2 0-7 0-9 1h-1c-1 0-2 0-3-1v-8z" class="f"></path><path d="M366 307c0 3 0 3 2 5h2v-1h2-1v1h7 13v1h-2-7-11l-1 1c2 1 6-1 8 0 0 0 0 1 1 1-2 0-7 0-9 1h-1c-1 0-2 0-3-1v-8zm-23 4h12c2 0 5 0 7 1 1 0 1 0 2 1v-1 2 2c-1 1-1 2-2 3-2 0-4 1-6 1h-3c-1-1-2-1-3-1h2-9l1 1h-1-5l-1-4h0l1-1c0-1 1-1 1-2h0c2 0 2-1 4-2z" class="V"></path><path d="M359 315v-1h-4-10-3-1v-1h1 0 3 11 5v2c1 1 1 1 2 1h1c-1 1-1 2-2 3-2 0-4 1-6 1h-3c-1-1-2-1-3-1h2 1c2-1 4-1 5-2h-1c-1 0-4 0-5-1h7v-1z" class="f"></path><path d="M359 315v1h-7c1 1 4 1 5 1h1c-1 1-3 1-5 2h-1-9l1 1h-1-5l-1-4c3 0 5-1 7-1h15z" class="D"></path><path d="M357 317h1c-1 1-3 1-5 2h-1-9v-1c5-1 10-1 14-1z" class="L"></path><path d="M386 296c1 0 1 0 2 1h2c1 1 3 2 5 2h1 1v2c-2 2-1 3-2 5v9c-1 0-1-1-2-1l-1 1h0l-1-4h-19-2v1h-2c-2-2-2-2-2-5l-1-7h1c3-1 7 0 11-1l13 1-4-4z" class="I"></path><path d="M384 305h7c1 1 0 2 0 3h-1c-4-1-10 0-14 0l-2-1h6c1 0 2 0 3-1 0 0 0-1 1-1z" class="b"></path><path d="M376 308h-5v-3c3-2 9-1 13 0-1 0-1 1-1 1-1 1-2 1-3 1h-6l2 1z" class="d"></path><path d="M372 311v-1h-2l1-1h12 8l1 2h1v-1c1-1 1 0 2 0v-4 9c-1 0-1-1-2-1l-1 1h0l-1-4h-19z" class="b"></path><path d="M207 332h1v1 1l1-1 1 1 1 1v2l-1 2 2 2 1 2c1 0 1 1 2 1h1c1 0 2-1 4-1 0 0 0-1 1-1 0-2 1-2 2-4h0 0c2 0 3-1 4-3h1v1c0 1 0 1 1 2v1c-2 1-3 2-4 4l-2 2c-2 2-5 3-8 5-5 3-10 8-13 13-3 3-4 8-5 11-1 8 0 17 0 25v44c-5-4-10-7-15-11-2-2-5-4-8-6h0c-1 0-1 0-2-1v-29-12c-1-4 0-7 0-11v-18c0-4 0-11-2-14v-1l1-1c5 4 11 7 18 6 4 0 7-2 11-4 0-1 1-1 1-1l1-1c3-2 4-5 5-7z" class="U"></path><path d="M187 349h1c0 2 0 4-1 6h-1c-1-2 0-4 1-6z" class="B"></path><path d="M207 332h1v1 1c-1 3-3 6-5 9v-2c0-1 1 0 1-1v-1h1 0v-2c-1 1-2 3-4 3h0l1-1c3-2 4-5 5-7z" class="S"></path><path d="M183 361c1 1 1 1 2 1l-13 8c3-3 7-6 11-9z" class="g"></path><path d="M184 353v3c-1 1-1 2-3 3h0l-2 2-1-1v1c-2 1-3 1-4 1l-1-1c0-1 0-3 1-5 0-1 0-1 1-1 1-1-1-5 0-7v-2h1v2c0-1 0-1 1-1s1 0 2 1h1 1v1-1h2c1 0 1 0 1 1 1 1 1 3 0 4h0 0z" class="C"></path><path d="M177 348h1 0c0 3 0 11-1 13h-1c1-5 0-9 1-13z" class="O"></path><path d="M184 353c-1 2-2 4-3 7 0-3 0-9 1-11h1 1v4h0z" class="F"></path><defs><linearGradient id="p" x1="191.655" y1="346.069" x2="201.828" y2="351.665" xlink:href="#B"><stop offset="0" stop-color="#a9a9aa"></stop><stop offset="1" stop-color="#dedcdc"></stop></linearGradient></defs><path fill="url(#p)" d="M208 334l1-1 1 1 1 1v2l-1 2c-6 10-16 16-25 23-1 0-1 0-2-1 6-6 14-11 20-18 2-3 4-6 5-9z"></path><path d="M208 334l1-1 1 1 1 1v2c-1-1-1-1-1-2h-2v-1z" class="c"></path><path d="M169 337l2 2-1 1v1c2 3 2 10 2 14v18c0 4-1 7 0 11v12 29c1 1 1 1 2 1-1 1-1 1-1 3v8 2 19 1h1 6 7 2 3 3 1l1 1-1 1v2l1 1h0c-1 1-1 1-1 2l-2 1h0 2v2c1 1 1 2 0 3l-1 1c1 0 1 0 2 1l-1 1h-1c-1-1-1-1-2-1 1 1 0 2 1 2h1s0 1 1 1h0l-1 1c-1-1-1-1-2-1-1-1-3 0-4-1h-4v1l1 1c2-1 3 0 5 0h2c1 1 3 1 4 2l-1 3 1 28c0 6 0 11-1 17 1-1 2-1 2-2h3l1 1c-2 3-5 6-7 9l-13 20-14 18c-2 3-4 7-7 9 0 0-1 0-2 1v-1c-1-1 4-8 5-10 1-1 3-3 3-5 1-1 0-164 1-184v-35c-1-3-1-9 1-12z" class="I"></path><path d="M168 349h1c0 4 1 9 1 13 0 1 1 4 1 5-1 0-2-1-2-1 0-1-1-3 0-4v-2c0-1 0-2-1-3h0v10 11 6-35z" class="X"></path><defs><linearGradient id="q" x1="170.837" y1="341.944" x2="167.415" y2="346.9" xlink:href="#B"><stop offset="0" stop-color="#777677"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#q)" d="M169 337l2 2-1 1v1c2 3 2 10 2 14v18c0 4-1 7 0 11l-1 4v-2c0-1-1-1-1-1v-4-9h0l-1-1v-2-3s1 1 2 1c0-1-1-4-1-5 0-4-1-9-1-13h-1c-1-3-1-9 1-12z"></path><path d="M172 384v12 29c1 1 1 1 2 1-1 1-1 1-1 3v8 2 19 1h1 6 7 2 3 3 1l1 1-1 1v2l1 1h0c-1 1-1 1-1 2l-2 1h0 2v2c1 1 1 2 0 3l-1 1c1 0 1 0 2 1l-1 1h-1c-1-1-1-1-2-1 1 1 0 2 1 2h1s0 1 1 1h0l-1 1c-1-1-1-1-2-1-1-1-3 0-4-1h-4v1l1 1h0-1-3l-1 1h0c-1 0-1 0-1-1h-1l-1 1-1-1c-1 0-1 1-2 2-1 0-1 0-2-1v6c-1-2 0-5 0-8h-1v36 10 6l-1-141 1-4z" class="K"></path><path d="M172 425c1 1 1 1 2 1-1 1-1 1-1 3v8 2c-1 1 0 2-1 3h0c-1-5 0-11 0-17z" class="H"></path><path d="M180 459h7 2 3l-2 1c1 1 1 1 1 2h-3-12c2 0 6 1 8-1-3 0-7 1-10 0v-1h0 3 0-4l1-1h6z" class="B"></path><path d="M174 459h6c1 0 3 1 4 1h-7 0-4l1-1z" class="C"></path><path d="M192 459h3 1l1 1-1 1v2c-2 0-6 0-8 1h0c-3-1-8 0-11 0h-4c0-1 0-1 1-2h2 12 3c0-1 0-1-1-2l2-1z" class="D"></path><path d="M192 459h3 1l1 1-1 1c-1 1-2 1-3 1h-5 3c0-1 0-1-1-2l2-1z" class="O"></path><path d="M193 462l1-1c1-1 1-1 3-1l-1 1c-1 1-2 1-3 1z" class="Q"></path><path d="M196 463l1 1h0c-1 1-1 1-1 2l-2 1h0 2v2c-1 0-1-1-2-1l-1 1h-2 0-5-13v-4-1h4c3 0 8-1 11 0h0c2-1 6-1 8-1z" class="M"></path><path d="M196 463l1 1h0c-2 0-4 0-5 1l-1 1c-1 0-1-1-3 0v1h-1c0-1 1-2 0-2h-14v-1h4c3 0 8-1 11 0h0c2-1 6-1 8-1z" class="Y"></path><path d="M173 469h13 5 0 2l1-1c1 0 1 1 2 1 1 1 1 2 0 3l-1 1c1 0 1 0 2 1l-1 1h-1c-1-1-1-1-2-1 1 1 0 2 1 2h1s0 1 1 1h0l-1 1c-1-1-1-1-2-1-1-1-3 0-4-1h-4v1l1 1h0-1-3l-1 1h0c-1 0-1 0-1-1h-1l-1 1-1-1c-1 0-1 1-2 2-1 0-1 0-2-1v6c-1-2 0-5 0-8v-8z" class="J"></path><path d="M180 473c-2-1-4-1-5-1v-1c1 0 4 1 5 0h2v1c-1 0-1 1-2 1z" class="g"></path><path d="M182 471h4c2 1 5 0 6 0 1 1 1 1 2 1h0 2l-1 1c1 0 1 0 2 1l-1 1h-1c-1-1-1-1-2-1v-1h-2c-1-1-3 0-4 0-1-1-3-1-5-1v-1z" class="D"></path><path d="M182 472c2 0 4 0 5 1 1 0 3-1 4 0h2v1c1 1 0 2 1 2h1s0 1 1 1h0l-1 1c-1-1-1-1-2-1-1-1-3 0-4-1h-4v1l1 1h0-1-3l-1 1h0c-1 0-1 0-1-1h-1l-1 1-1-1 1-1h-1-1 0v-1c1 0 1 0 2-1h0c-1 0-2 0-3-1v-1h5 0c1 0 1-1 2-1z" class="a"></path><path d="M172 529v-6-10-36h1c0 3-1 6 0 8v-6c1 1 1 1 2 1 1-1 1-2 2-2l1 1 1-1h1c0 1 0 1 1 1h0l1-1h3 1 0c2-1 3 0 5 0h2c1 1 3 1 4 2l-1 3 1 28c0 6 0 11-1 17l-9 14c-6 7-11 14-15 22v-35z" class="U"></path><path d="M188 491l1-1 1 1v5l-1 1c-1 0-1 0-2-1 0-2 0-3 1-5z" class="H"></path><path d="M186 478c2-1 3 0 5 0h2c1 1 3 1 4 2l-1 3c-1 1-1 2-2 3-2 1-3 1-5 2l-5 3c-2 1-3 2-5 3h0c-1 1-2 1-3 2h-1l-1 2-1-1v-12-6c1 1 1 1 2 1 1-1 1-2 2-2l1 1 1-1h1c0 1 0 1 1 1h0l1-1h3 1 0z" class="g"></path><path d="M179 494l1-2c1 0 2-1 2-1 0-1 0-1-1-1h0v-2h2c0 1 0 1 1 2 0-1 1-1 1-1v-2l1-1-1-1c-1 0-1 0-2-1v-2l-2-2c1 0 2-1 3 0l2 1s1 1 1 2h-2 0c0 1 1 1 2 2 1 0 1 2 2 3l-5 3c-2 1-3 2-5 3z" class="N"></path><path d="M186 478c2-1 3 0 5 0h2c1 1 3 1 4 2l-1 3c-1 1-1 2-2 3-2 1-3 1-5 2-1-1-1-3-2-3-1-1-2-1-2-2h0 2c0-1-1-2-1-2v-1s-1-1-2-1h-1-2l1-1h3 1 0z" class="f"></path><path d="M239 486c2 0 3 1 4 2 1 2 1 3 2 4 0 2 0 3 1 4v3 4h-2l1 3-1 2h1c0 1 1 1 2 2l1 1v2h-1v4 14 30c-1 3-2 5-2 8l-2 16c4 1 7 0 10 1l-6 31v-2-1-2h-1-5c-2 1-3 0-5 1 0 2 0 2-1 4l2 22c0 4 1 9 1 13-1 2 0 6-1 7-1 0-1-1-1-1l-1-3v-2-4c-1-2-2-2-2-4s-1-5-1-8l-4-18v-5-4-1-1l1-1v1c0 1 0 2 1 2l-4-22c0-3 0-6-1-8 0-5-2-10-3-15h-1 0v2h-1l-2 2 1 1-1-1h-1-1c0-1 0-3-1-4-2-3-2-7-3-10l-4-17c-1-4-1-8-1-12l1 2c0-1 0-2 1-3-1-1 0-3 0-5v-28h0v6c1-2 0-4 0-6 1 2 1 4 1 6s0 2 1 3h4c1 1 3 1 4 1 2 1 3 1 4 2h1l2 1h2l2 3-1 1h1c1 0 2 0 3-1h1c0-1 1-1 1-2h0l3-11 1-5v-4z" class="H"></path><path d="M235 506v4c-5 1-9 3-13 4h-1c1-2 7-2 9-4h0v-1c1 0 2 0 3-1h1c0-1 1-1 1-2z" class="R"></path><path d="M217 542c0-1-1-1-1-2 1 0 1-1 2-1v1c1 2 1 4 0 6l-1 1h1v1c0 1-1 2 0 3h0s1-1 1-2h1v-3h0v4 1c-1 0-1 0-1 1h-1 0c-1 0-1-1-1-2h0l1-2-1-1h-2v-4c1-1 1-1 2-1z" class="B"></path><path d="M217 542c0-1-1-1-1-2 1 0 1-1 2-1v1c0 2 0 4-1 6h-1v-1c1-1 1-2 1-3z" class="F"></path><path d="M209 525v3h2l1-1 1 1-1 1c-1 0-1 1-1 1-1 1-2 0-2 2h1c0 1 0 1-1 2 1 0 1 1 2 1s1 0 2 1h0c-2 0-3 1-5 2-1-4-1-8-1-12l1 2c0-1 0-2 1-3z" class="P"></path><defs><linearGradient id="r" x1="222.528" y1="561.992" x2="228.206" y2="571.788" xlink:href="#B"><stop offset="0" stop-color="#313030"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#r)" d="M225 576c-1-5-1-11-1-15l2-1v2c0 4 2 7 3 10h-2l-2 4z"></path><path d="M230 510h0c-2 2-8 2-9 4h1c-2 1-5 2-7 3-2 0-3 1-5 1v-4h0 3v1h-3v1c3 0 3-1 5-2h3v-1h-2v-1h3c4 0 7-1 11-2z" class="G"></path><path d="M215 543h0c0-2 0-5-1-6v-1h1 1v-2-1h-1l1-2h0c-1-1-1-1-1-2 1-2 0-8 0-10l1-1 1 1c1 1 1 6 0 8v1l1 1h-1 1v2 8c-1 0-1 1-2 1 0 1 1 1 1 2-1 0-1 0-2 1z" class="E"></path><defs><linearGradient id="s" x1="223.275" y1="611.428" x2="236.05" y2="626.581" xlink:href="#B"><stop offset="0" stop-color="#b5b3b4"></stop><stop offset="1" stop-color="#e9eaeb"></stop></linearGradient></defs><path fill="url(#s)" d="M228 614v-4-1-1l1-1v1c0 1 0 2 1 2l4 34v1h-1c0-2-1-5-1-8l-4-18v-5z"></path><path d="M215 547h2l1 1-1 2h0c0 1 0 2 1 2h0 1c0-1 0-1 1-1v-1 5c0 1 1 2 1 3v4c1 1 1 2 1 3h-1 0v2h-1l-2 2 1 1-1-1-1-2h0v-1c-1-1 0-2 0-2-1-2 0-4-1-5v-1c0-4 0-7-1-11z" class="F"></path><path d="M221 562v2c-1-1-1-1-1-2s0-3 1-4v4z" class="G"></path><path d="M217 555l1-1h1v2l1 1v3h-1-1-1c-1 3 1 6 1 9l-1-2h0v-1c-1-1 0-2 0-2-1-2 0-4-1-5v-1h0v-3h1z" class="K"></path><path d="M216 558v-3h1l1 1v1c0 1-1 1-2 1z" class="G"></path><defs><linearGradient id="t" x1="232.372" y1="565.608" x2="235.226" y2="582.634" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#t)" d="M231 563h0v3h1 2c1 0 1-1 2-1 0-1 0-3-1-4v-2c0-1-1-2-1-3h0l1 1h0c0-1 0-2 1-3v7c0 7 1 14 1 21v1 1c-2 0-4 1-5 2l-1-1-1-13 1-2v1-8z"></path><path d="M231 585c2-2 3-2 6-3v1 1c-2 0-4 1-5 2l-1-1z" class="W"></path><path d="M239 542v-6c0-1 1-2 1-2 1-1 2 0 3-1v26l-1 1c1 1 1 2 1 3h-1v-2c-1 1-1 2-1 3h-1v-1c-1 0-1 1-1 1h-1v-5c0-1-1-2-1-4v-1c0-2 0-4 1-6h1c1-1 0-4 0-6z" class="L"></path><path d="M240 544v-6-3h1 0v1 2 3 7c-1 1 0 2 0 3h0c-1 1-1 1-1 2l1 1h0c-1 1-1 2-1 3 1 1 1 1 1 2v3h-1 0c0-1 0-2-1-3h0-1v-1h1v-9h0c1-2 1-3 1-5z" class="D"></path><path d="M239 542l1-3v5c0 2 0 3-1 5h0v9h-1v1h1 0c1 1 1 2 1 3h0 1v-3h2l-1 1c1 1 1 2 1 3h-1v-2c-1 1-1 2-1 3h-1v-1c-1 0-1 1-1 1h-1v-5c0-1-1-2-1-4v-1c0-2 0-4 1-6h1c1-1 0-4 0-6z" class="Y"></path><path d="M241 506h-1c1 0 1 0 1-1l3 2 1-1-1 2-1 13c0 3 1 6 0 8v4c-1 1-2 0-3 1 0 0-1 1-1 2v6c0 2 1 5 0 6h-1v-28-9c0-1 1-1 1-2h1v2h0 1c-1-2 0-3 0-5z" class="J"></path><path d="M243 529h0-1v3c0 1 0 1-1 1h0v-2-1c0-2 0-4 1-7h0c-1-1-1-2-1-3 1-2 1-5 1-7h-1-1c0 1 0 1 1 2h0c-1 2-1 5-1 6l-1-1c0-2 0-7 1-9 0 1 1 1 1 1 1-1 1-4 1-5 1 0 1 1 2 1l-1 13c0 3 1 6 0 8z" class="M"></path><path d="M239 486c2 0 3 1 4 2 1 2 1 3 2 4 0 2 0 3 1 4v3 4h-2l1 3-1 1-3-2c0 1 0 1-1 1h1c0 2-1 3 0 5h-1 0v-2h-1c0 1-1 1-1 2v9-10h-3v-4h0l3-11 1-5v-4z" class="F"></path><path d="M239 495l3-6 2 11v3l1 3-1 1-3-2c0 1 0 1-1 1h1c-1 1-2 1-4 2v-2c0-4 1-7 2-11z" class="Z"></path><path d="M239 495c0 1 1 2 2 3 0 1-1 2-1 3h1c-1 1-2 1-2 1-1 1-1 3-2 4 0-4 1-7 2-11z" class="V"></path><path d="M210 498c0 2 0 2 1 3h4c1 1 3 1 4 1 2 1 3 1 4 2h1l2 1h2l2 3-1 1h1v1c-4 1-7 2-11 2h-3v1h2v1h-3c-2 1-2 2-5 2v-1h3v-1h-3 0v-16z" class="M"></path><path d="M223 506l-1-1h1c2 1 4 1 6 2l-1 1c-1 0-2 1-3 1h-1 0l2-1v-1c-1 0-1 0-2 1h-3v-1h1l1-1z" class="a"></path><path d="M210 504h0c3 1 5 1 7 3h-2-3 0c0 1 1 1 1 2h-1c1 0 1 1 2 0h0c2-1 3-1 5-1v1c-1 0-2 0-3 1h3c-1 0-3 0-4 1-2 1-3 1-5 2v-8-1z" class="R"></path><path d="M210 498c0 2 0 2 1 3h4c1 1 3 1 4 1l-1 1h-2s0-1-1-1h-2-1s-1-1-2 0v2 1 8c2-1 3-1 5-2 1-1 3-1 4-1 2 0 4 0 6-1l1 1c-2 1-7 1-9 1v1h2-3v1h2v1h-3c-2 1-2 2-5 2v-1h3v-1h-3 0v-16z" class="Q"></path><defs><linearGradient id="u" x1="241.933" y1="584.472" x2="240.489" y2="612.728" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#u)" d="M237 582h0l1 11 1 12c0 1 0 4 1 5 2-8 2-17 3-25 4 1 7 0 10 1l-6 31v-2-1-2h-1-5c-2 1-3 0-5 1 0 2 0 2-1 4l-3-31c1-1 3-2 5-2v-1-1z"></path><path d="M226 560h0c1-4 0-9 0-13-1-3-1-7 0-10v6 5c1 1 0 3 1 5v5 2h1v-2-2-9c1 0 0-2 1-3s0-3 0-5c0-5 0-12 3-16 0 3-1 5-1 8v7 4c1 0 1 0 1 1-1 2 0 4-1 5v4 8 3h0v8-1l-1 2 1 13 1 1 3 31 2 22c0 4 1 9 1 13-1 2 0 6-1 7-1 0-1-1-1-1l-1-3v-2-4c0-4-1-7-1-11l-2-20-4-28-3-14 2-4h2c-1-3-3-6-3-10v-2z" class="E"></path><path d="M230 572l-1-17c0-5-1-9 2-13v3h0c0 2-1 4 0 5v1c-1 2 0 4 0 5 0 2-1 4 0 6v1h0v8-1l-1 2z" class="Q"></path><path d="M232 618c0-1 1-1 1-2h0 0l1 1c1 4 3 36 2 37l-1-1v-4c0-4-1-7-1-11l-2-20z" class="c"></path><defs><linearGradient id="v" x1="230.217" y1="590.008" x2="231.095" y2="615.1" xlink:href="#B"><stop offset="0" stop-color="#777778"></stop><stop offset="1" stop-color="#d2d1d2"></stop></linearGradient></defs><path fill="url(#v)" d="M228 590h0 1s1 1 2 1l3 26-1-1h0 0c0 1-1 1-1 2l-4-28z"></path><defs><linearGradient id="w" x1="226.729" y1="572.107" x2="228.905" y2="590.191" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#757575"></stop></linearGradient></defs><path fill="url(#w)" d="M225 576l2-4h2l2 19c-1 0-2-1-2-1h-1 0l-3-14z"></path><path d="M243 347h1l2 3c3 2 5 4 8 6 5 2 9 6 13 9 1 1 3 2 3 4h0c1 2 1 3 1 5v2 14c0 2 0 5-1 7v11c0 2 1 5 0 7 1 4 1 8-1 11h1 1c-4 6-10 11-15 16-1 0-2 1-3 2-3 2-7 5-10 5-1 0-2 1-2 1-1 0-2-1-3-1h0c-3-1-5-3-8-4-7-6-17-14-21-23h1v-1c-1-2-1-5-1-7-1-13-1-26 0-39 1-3 2-5 4-8l-1 5v1l2-5h0 1l2-1s1 0 1 1l3-3c1-1 2-2 4-3l4-4c2-1 5-3 6-5 1 0 3-3 4-3h1v-1h1l1-1 1-1z" class="U"></path><path d="M266 412c1 1 1 1 1 2h1s1 0 1 1h0v2h-3c-1-1-2-1-2-2v-2c1 0 1 0 2-1z" class="C"></path><path d="M258 410c2 0 6 0 8 1v1h0c-1 1-1 1-2 1h0l-10-1h4v-2z" class="T"></path><path d="M264 413c-1-1-1-1-2-1v-1h4v1h0c-1 1-1 1-2 1h0z" class="M"></path><path d="M243 410h9 6v2h-4-7-5c1-1 1-1 1-2z" class="Y"></path><path d="M269 408h1c0 2 1 5 0 7v-3c-1 0-3-1-4 0v-1c-2-1-6-1-8-1h-6-9v-1h5 11l10 1 1-1-1-1zm-26 33h0l1 1c1-1 1-1 2-1l2-2h0c3-1 4-3 6-5 2-1 4-3 6-5 2-1 4-3 5-5h0 1v1c-2 2-5 6-8 7-2 1-3 3-5 4-1 1-3 3-5 4-1 1-2 2-4 3h-1-1-3v-11c1 0 2 1 3 1-1 1-1 1-1 2 1 1 1 3 1 4v2h1z" class="E"></path><path d="M269 426h1 1c-4 6-10 11-15 16-1 0-2 1-3 2-3 2-7 5-10 5l-1-1c-1-1-2-1-2-1h1c2-1 4-2 6-2 6-3 10-6 15-11 3-2 5-5 7-8z" class="h"></path><path d="M209 422h1c2 5 6 8 10 12 5 5 10 9 17 12l3 1s1 0 2 1l1 1c-1 0-2 1-2 1-1 0-2-1-3-1h0c-3-1-5-3-8-4-7-6-17-14-21-23z" class="X"></path><path d="M209 375c1-3 2-5 4-8l-1 5v1c0 4-1 9 0 13v5c0 2 0 5 1 7h-1l-1 1c0 3-1 7 0 9 2 2 7 1 10 1h12c2-1 2-2 5-2h0 0c0-2 0-3 1-4h2c1 2 0 7 0 9 1 3 1 6 1 8 0 3-1 6 0 8h0v5c-1 0-2-1-3-1v-6-3-3-8h-8-14-1v1 1c-1 0-1 1-2 2 0 1-1 1-1 1h-1c0 1 0 2 1 2 1 2 2 4 4 6 3 3 6 5 8 8h0 0c-2-1-10-7-10-9-1-1-1-1-2-1s-2-3-3-4v2c-1-2-1-5-1-7-1-13-1-26 0-39z" class="H"></path><path d="M239 426l1-3h1v5h0 1v5c-1 0-2-1-3-1v-6z" class="C"></path><path d="M211 399c-1-2 0-3 0-5-1-6-1-12 0-18 0-1 0-3 1-4v1c0 4-1 9 0 13v5c0 2 0 5 1 7h-1l-1 1z" class="G"></path><path d="M217 412h-2l-2-1 1-1h18 6c1 1 1 1 1 2h-8-14z" class="L"></path><path d="M241 397c1-2 0-4 1-5v4 7c3-2 6-2 8-3 1-1 3-1 4-1 5-1 11-3 15-2h1 0 0v11h-1l1 1-1 1-10-1h-11-5v1c0 1 0 1-1 2 1 4 1 10 1 15-1 2 0 4-1 6h0c1 2 1 6 1 8h-1v-2c0-1 0-3-1-4 0-1 0-1 1-2v-5h0c-1-2 0-5 0-8 0-2 0-5-1-8 0-2 1-7 0-9h-2c-1 1-1 2-1 4h0 0c-3 0-3 1-5 2h-12c-3 0-8 1-10-1-1-2 0-6 0-9l1-1 1 1 4 1 2-1h3l1 1h1c1 0 5 0 6-1h3l1-2-1-1h2c-1-1-2-2-2-3 2 0 3 0 4 1 0-1 0-1 1-1h3v4z" class="J"></path><path d="M234 397l3 4h-9c-2-1-4-1-6-1h0v-1l1 1h1c1 0 5 0 6-1h3l1-2zm7-4v4 2 3c-3-1-5-4-6-6-1-1-2-2-2-3 2 0 3 0 4 1 0-1 0-1 1-1h3z" class="C"></path><path d="M241 393v4 2-1h-2c0-1 0-1-1-2 0-1-1-1-1-2s0-1 1-1h3z" class="K"></path><path d="M213 399l4 1h1c0 2 0 2 1 3s-1 0 0 3h1 2 1l-1-1h-2v-1c2 0 4 0 5 1 2-1 2 0 4 0h4 4v1c-1 0-2 0-2 1h-1c-2 2-8 1-10 1 1-1 3-2 4-1h2c1 0 2 0 3-1h0-4c-1 0-1-1-1-1-1 0-2 1-3 0h-2v1l1 1-1 1h-4-7l1-9zm57-2h0v11h-1c-1 0-2 0-3-1-1 0-1 1-2 1h-1v-2h-1 0v1 1l-1-1v-1l-1-1v1 2h-1v-1-1-1l-2 1c1 0 1 1 1 1v1h0c-2-1-4 0-5 0h-9c-1-1-2-3-2-4l1-1v1 1h0c1 0 2-1 3 0 0 0 1 0 1-1h1c0 1 0 1 1 1 2 0 2 0 3-1 2-1 1 0 2 0 2 1 4 0 5 0h3c0 1 1 1 2 1v-5h3c1-1 2-1 3-3z" class="U"></path><path d="M240 349h1l1-1 1-1c0 3 5 4 6 7h-2l-3-3-2-2-1 1c0 2 0 3 1 5l-1 38h-3c-1 0-1 0-1 1-1-1-2-1-4-1 0 1 1 2 2 3h-2l1 1-1 2h-3c-1 1-5 1-6 1h-1l-1-1h-3l-2 1-4-1-1-1h1c-1-2-1-5-1-7v-5c-1-4 0-9 0-13l2-5h0 1l2-1s1 0 1 1l3-3c1-1 2-2 4-3l4-4c2-1 5-3 6-5 1 0 3-3 4-3h1v-1z" class="E"></path><path d="M226 362l2 2v10 3h-1c-1-1-3-2-3-3l1-1c1-1 0-1 0-2 1-1 1-2 1-2l-1-1v-1-1s1-1 1-2l-1-1 1-1z" class="H"></path><path d="M224 383c1 1 1 1 1 2 1 1 2 1 2 2h1 0c-1-1-2-2-2-3 2-1 2-1 3-1v2h0v1h0l2-1v3l1 1h1c0 1 0 2-1 3l-1-1c-1-1-1-2-2-3h-1c0 1 0 1 1 1 0 1 0 1 1 2 0 1 0 1 1 2-3-1-2-3-4-4-1 0-1-1-2-1 0-2-1-3-1-5z" class="C"></path><path d="M231 388c1-1 2 0 3-2l1 2h0c1 1 2 0 3 2-1 1-1 1 0 3-1 0-1 0-1 1-1-1-2-1-4-1 0 1 1 2 2 3h-2 0l-1-1c-1-1-1-2-1-2-1-1-1-1-1-2-1-1-1-1-1-2-1 0-1 0-1-1h1c1 1 1 2 2 3l1 1c1-1 1-2 1-3h-1l-1-1z" class="F"></path><path d="M225 363l1 1c0 1-1 2-1 2v1 1l1 1s0 1-1 2c0 1 1 1 0 2l-1 1c-1-1-1-1-2-1h-1 0v-1h-1v1l1 1c0 1 0 1 1 2s2 2 2 3l-1 1h0c0-1-1-2-1-2h-1s-1-2-2-2v-1c-1-1-1-1-1-2s-1-1-1-2h1v-1l1-1c1-2 4-4 6-6z" class="C"></path><path d="M218 368l3-3c1-1 2-2 4-3l4-4c2-1 5-3 6-5 1 0 3-3 4-3h1c0 2 0 4-1 7v5c0 2-1 5 0 7 0 2 0 5-1 7v12h-2v-1l1-1h-2 0c-2 0-1-1-2-3v2c-1-1-1-3-2-5h0-1c0-1-1-2-1-3v-6-1-6c0-1-1-1-1-2 1 0 1-1 1-1-1 0-2 1-3 1h0l-1 1c-2 2-5 4-6 6l-1 1v1h-1c0-2 0-2 1-3z" class="B"></path><path d="M232 359c0-1 0-1 1-2 2 0 3-4 6-4v2c-1 0-1 1-1 1l-1-1c0 1 0 2-1 2-1 1-2 2-4 2z" class="E"></path><path d="M237 355h0l1-1 1 1c-1 0-1 1-1 1l-1-1z" class="C"></path><path d="M229 361l1-1h1v7c-1 4-1 8 0 13h-1c0-1-1-2-1-3v-6-1-6c0-1-1-1-1-2 1 0 1-1 1-1z" class="H"></path><path d="M237 355l1 1-1 20v7 2 1h-2 0c-2 0-1-1-2-3v-6c-1-6 0-12-1-18 2 0 3-1 4-2 1 0 1-1 1-2z" class="i"></path><path d="M214 368h0 1l2-1s1 0 1 1c-1 1-1 1-1 3 0 1 1 1 1 2s0 1 1 2v1c1 0 2 2 2 2 1 2 2 4 3 5 0 2 1 3 1 5 1 0 1 1 2 1 2 1 1 3 4 4 0 0 0 1 1 2l1 1h0l1 1-1 2h-3c-1 1-5 1-6 1h-1l-1-1h-3l-2 1-4-1-1-1h1c-1-2-1-5-1-7v-5c-1-4 0-9 0-13l2-5z" class="U"></path><path d="M213 398c4-1 7 0 11 0 2 1 4 1 6 1-1 1-5 1-6 1h-1l-1-1h-3l-2 1-4-1-1-1h1z" class="D"></path><defs><linearGradient id="x" x1="272.362" y1="388.779" x2="238.017" y2="375.867" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#x)" d="M243 347h1l2 3c3 2 5 4 8 6 5 2 9 6 13 9 1 1 3 2 3 4h0c1 2 1 3 1 5v2 14c0 2 0 5-1 7h0 0-1c-4-1-10 1-15 2-1 0-3 0-4 1-2 1-5 1-8 3v-7-4c-1 1 0 3-1 5v-4l1-38c-1-2-1-3-1-5l1-1 2 2 3 3h2c-1-3-6-4-6-7z"></path><path d="M250 390l-1 1h1c0-1 1-1 1-2s0-1 1-1h0l1 1-7 8v-2-5h1l1 3h0l1-1-1-1 1-2 1 1zm-8-35l1 1v26c0 5 0 10-1 14v-4c-1 1 0 3-1 5v-4l1-38z" class="W"></path><path d="M251 359c3 2 5 5 8 6 1 1 2 1 2 2 2 0 3 2 3 3v1h1c-1 3-1 4-4 5 0 2-1 3-3 5 0 1 0 1-1 2 0 1-1 1-2 2-1 0-1 0-2-1v-19c-1-2-2-4-2-6z" class="B"></path><path d="M261 367c2 0 3 2 3 3v1h-2v2h-1v-6z" class="K"></path><path d="M261 373h1v-2h2 1c-1 3-1 4-4 5v-3z" class="E"></path><path d="M256 369v-3h1 1v10c0 1-1 2 0 4v1c0 1 0 1-1 2 0-1-1-1-1-2v-3c0-3 1-6 0-9z" class="C"></path><defs><linearGradient id="y" x1="257.563" y1="373.543" x2="251.708" y2="376.314" xlink:href="#B"><stop offset="0" stop-color="#332e32"></stop><stop offset="1" stop-color="#3c413c"></stop></linearGradient></defs><path fill="url(#y)" d="M253 365c0 1 0 2 1 3v-3c1 1 1 3 1 4h1 0 0c1 3 0 6 0 9v3c0 1 1 1 1 2s-1 1-2 2c-1 0-1 0-2-1v-19z"></path><path d="M243 347h1l2 3c3 2 5 4 8 6 5 2 9 6 13 9 1 1 3 2 3 4h0c1 2 1 3 1 5-1-1-1-2-1-3h-4-1-1v-1c0-1-1-3-3-3 0-1-1-1-2-2-3-1-5-4-8-6l-1-1h0c-2 3-1 8-1 11v19l1 1v1l-1-1c-1 0-1-1-1-1v-11-19c-1-1-2-3-3-4l-2-2h0v4l-1-1c-1-2-1-3-1-5l1-1 2 2 3 3h2c-1-3-6-4-6-7z" class="e"></path><path d="M264 368l-3-3c-1 0-2 0-2-1l2-2c1 1 3 2 4 4 0 0 0 1-1 2z" class="F"></path><path d="M265 366c1 1 3 3 5 3 1 2 1 3 1 5-1-1-1-2-1-3h-4c0-1-1-2-2-3 1-1 1-2 1-2z" class="K"></path><path d="M249 354h0c1 1 3 2 4 3s3 1 4 2l-1 2c-1 0-2-2-3-2-2-1-3-3-4-4-1 0-1 0-2-1h2z" class="S"></path><defs><linearGradient id="z" x1="236.996" y1="226.01" x2="244.131" y2="134.435" xlink:href="#B"><stop offset="0" stop-color="#7d7d7d"></stop><stop offset="1" stop-color="#adacac"></stop></linearGradient></defs><path fill="url(#z)" d="M224 106l9-44 3-18c1-5 3-10 4-16V17v-2c1 4 0 9 1 13 1 8 3 16 5 24l7 34 7 35 2 12 3 13 6 29h1c0 2 0 4 1 6 0 1 0 2 1 4 0 6 0 12-1 18-1 2-1 3-1 5-1 1-2 3-3 4h-1v-2h-1v3h0c0 2-2 4-3 6l-1 1-2 3-1 2-1 1-2 1c0 1-1 1-1 1h0c-3 2-5 3-8 4h0l-2 1c-5 0-9 0-14-2l-3-1-3-2-2-1v-1c-2 0-3-2-4-3-1-3-3-5-4-8s-1-6-1-9v-13c-1 1-1 19 0 21h-2v-1c0-1-1-2-1-2h-2c0-1-1-2-1-2-1-2 0-3-1-5s0-4 0-6v-10l-1-3c1-5 1-10 3-15 0-1 1-1 1-2 1-1 1-3 1-4l6-28 1-6c1-2 1-5 2-7v-1c1-3 2-8 3-11v-3-2z"></path><path d="M212 164v28-5c-1 1-1 3-1 4h0v-21c-1 2-1 4-2 6v4 7-4c-1 1-1 1-1 2v3l-1-3c1-5 1-10 3-15 0-1 1-1 1-2 1-1 1-3 1-4z" class="T"></path><path d="M267 185c0-3-1-6-1-10-1-1 0-3-1-5s-2-4-4-6c-4-6-8-11-9-18 2 4 4 9 7 13s6 7 8 11c1 3 1 7 1 10v12h0v-10l-1 1v2h0z" class="Q"></path><path d="M252 161c2 1 3 4 5 6 1 2 4 5 5 8h0v9 15 9c-1 2-2 3-3 5-2 2-3 4-5 6l-1 1v-1c3-3 5-7 7-10 1-3 1-6 1-9v-14c0-4 0-9-1-12h0c-1-3-4-5-5-8-1-1-3-4-3-5z" class="F"></path><path d="M271 175h1c0 2 0 4 1 6 0 1 0 2 1 4 0 6 0 12-1 18-1 2-1 3-1 5-1 1-2 3-3 4l-1-1v-12c1 1 0 3 1 4v8c0-1 1-2 1-3 1 0 0 0 1-1v-2c1-2 0-5 0-7 0-4 1-8 1-12s-3-8-1-11z" class="L"></path><path d="M267 185v-2l1-1v10h0v7 12l1 1h-1v-2h-1v3h0c0 2-2 4-3 6l-1 1c0-5 2-9 2-14h1v-5-14c1 0 1-1 1-2h0z" class="W"></path><path d="M267 185v-2l1-1v10h0v7 12l1 1h-1v-2h-1v3-28z" class="O"></path><path d="M224 226v-1h1 1v-3c1 0 1-1 1-1 0-1 0-1 1-1 0-2 0-2 1-3h0c0 1 0 2-1 3l1 1h1 0c1 0 1-1 1-2 3-2 6-4 10-4 1 1 2 1 4 2h0c1 1 3 4 4 4v-1c0 1 1 1 2 1 0 0 1 1 1 2h0c1 1 2 3 2 4 1 0 1 1 2 1h0 0c-3 2-5 3-8 4h0c1-3-2-8-3-10v-1c-1-1-2-2-3-2-3-1-5 0-7 1s-3 2-3 4h0l-3 6-3-2-2-1v-1z" class="X"></path><defs><linearGradient id="AA" x1="237.824" y1="218.859" x2="239.077" y2="232.345" xlink:href="#B"><stop offset="0" stop-color="#c4c4c3"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AA)" d="M229 230l3-6h0c0-2 1-3 3-4s4-2 7-1c1 0 2 1 3 2v1c1 2 4 7 3 10l-2 1c-5 0-9 0-14-2l-3-1z"></path><path d="M221 123v1c1 0 1-1 1-1 1-2 1-3 2-4h0c1-1 1-1 2-1 0 0-1 1-1 2h0c1-1 3 0 4-1l1 1v1h0c2 0 4 0 5 1 2 1 2 0 2 2 0-1 1-2 1-2v17c-1 2-1 6-1 9h0v-5c-1-1-1-2-1-3-1-1-3-2-3-2-1 0-2 0-2-1l-1-1s0-1-1-1l-1 1h0-1 0c-1 0-3-1-5-2 0-1 0 0-1 0 0 0-1-1-1-2l-1-2c1-2 1-5 2-7zm22 0v6l1-4 1-1h5c0-1 0-1 1-2 0 0 1 0 1-1l6 9 1 1c0 1 1 3 2 3h1v-1l3 13v3c0-1 0-2-1-2h-1v1l-3-3-6-8c0-1 0-1-1-2h-1l1 1v2h0c-1 0-1 0-2 1-2 1-5 2-6 2h-1l-1 4c0-1 1-2 0-3v-19z" class="X"></path><path d="M238 122l2-60c2 6 1 14 2 21l1 40v19l1 42h0c3 1 6 3 8 7 2 3 3 7 2 11 0 4-3 7-6 9-1 0-1 0-2 1-1-1-1-1-2 0-2 0-5 0-8-1h0-2v-1c-2 0-4-3-5-4h0l-1-1c-2-4-1-9 0-12 1-4 5-7 9-9 1-5 0-11 0-16l1-29v-17z" class="H"></path><path d="M228 195c1-3 3-6 6-8s6-3 10-2c3 1 6 3 8 6 2 4 2 8 1 13-2 3-4 6-7 8-1-1-1-1-2 0-2 0-5 0-8-1h0-2v-1c-2 0-4-3-5-4-2-4-2-7-1-11z" class="U"></path><defs><linearGradient id="AB" x1="240.95" y1="196.742" x2="237.031" y2="211.007" xlink:href="#B"><stop offset="0" stop-color="#5d5c5c"></stop><stop offset="1" stop-color="#969696"></stop></linearGradient></defs><path fill="url(#AB)" d="M228 195v1l1-2c2 0 1 2 3 3v-1c0 3 0 6 3 9 1 2 3 2 6 2 2 0 4 0 6-2l1-2h1 2 0l2-1v2c-2 3-4 6-7 8-1-1-1-1-2 0-2 0-5 0-8-1h0-2v-1c-2 0-4-3-5-4-2-4-2-7-1-11z"></path><defs><linearGradient id="AC" x1="244.204" y1="185.84" x2="240.157" y2="200.067" xlink:href="#B"><stop offset="0" stop-color="#434343"></stop><stop offset="1" stop-color="#6a696a"></stop></linearGradient></defs><path fill="url(#AC)" d="M228 195c1-3 3-6 6-8s6-3 10-2c3 1 6 3 8 6 2 4 2 8 1 13v-2l-2 1h0-2-1c1-1 2-3 2-5-1-2-2-5-4-7-2-1-4-2-6-1-5 0-5 2-7 5l-1 1v1c-2-1-1-3-3-3l-1 2v-1z"></path><defs><linearGradient id="AD" x1="239.496" y1="124.218" x2="254.166" y2="30.459" xlink:href="#B"><stop offset="0" stop-color="#cecece"></stop><stop offset="1" stop-color="#fefefe"></stop></linearGradient></defs><path fill="url(#AD)" d="M224 106l9-44 3-18c1-5 3-10 4-16V17v-2c1 4 0 9 1 13 1 8 3 16 5 24l7 34 7 35 2 12v1h-1c-1 0-2-2-2-3l-1-1-6-9c0 1-1 1-1 1-1 1-1 1-1 2h-5l-1 1-1 4v-6l-1-40c-1-7 0-15-2-21l-2 60s-1 1-1 2c0-2 0-1-2-2-1-1-3-1-5-1h0v-1l-1-1c-1 1-3 0-4 1h0c0-1 1-2 1-2-1 0-1 0-2 1h0c-1 1-1 2-2 4 0 0 0 1-1 1v-1-1c1-3 2-8 3-11v-3-2z"></path><path d="M224 111h0v3 2c0-1 1-1 1-1 0-1 1-1 1-2h0c1-1 2-2 3-2v-1l1 1h-1v1c0 1 0 1-1 1-1 2-1 3-2 5h0c-1 0-1 0-2 1h0c-1 1-1 2-2 4 0 0 0 1-1 1v-1-1c1-3 2-8 3-11zm28 10c2 1 2 3 4 4l1-1c0-1 1-3 0-4l-1-2v-1l1 1c1 1 1 2 2 3h1l2 12v1h-1c-1 0-2-2-2-3l-1-1-6-9z" class="b"></path><path d="M253 86c1 1 2 1 3 2 4 0 7 0 11 1 11-2 23-1 34-1h51 68 37c3 0 8 0 10 1v1c-3 1-8 1-12 1v69l1 9c3 0 6 1 9 2-1 0-3 1-5 1-6 1-13 1-20 1h-33-92l-29-1c-2 1-6 0-8 0h-5c-1 0-1 0-2 1v1c1 0 1 1 1 1h-1l-6-29-3-13-2-12-7-35z" class="I"></path><path d="M455 160v-1-4-11-35-10c-1-2-2-5-2-7l1-1h1v69z" class="Z"></path><defs><linearGradient id="AE" x1="302.818" y1="163.52" x2="311.129" y2="163.146" xlink:href="#B"><stop offset="0" stop-color="#29292a"></stop><stop offset="1" stop-color="#434242"></stop></linearGradient></defs><path fill="url(#AE)" d="M312 145c2 2 2 7 2 10l-1 1v1h1c0 1 0 3 1 4-1 1-1 2-2 3l-2 2c-1 0-2 1-3 2h6 12 5l-31 1c2-1 5-3 7-5 2-3 5-8 6-12 0-2 0-5-1-7z"></path><path d="M314 157c0 1 0 3 1 4-1 1-1 2-2 3h-1c0-2 1-5 1-7h1z" class="R"></path><path d="M440 162l-1-1c1-1 3 0 4-1 1 0 1 0 2 1 0 0 1 0 1-1h3c1 1 1 1 2 0v8l-1 1h-20v-1h1 6v-1c1-2 1-3 1-5h2z" class="V"></path><path d="M437 167c1-2 1-3 1-5h2 1c0 1-1 1-2 2h0c2 1 5 1 7 2-2 1-4 0-5 1v1h6 3 1l-1 1h-20v-1h1 6v-1z" class="b"></path><path d="M295 92h49 0s-1 0-2 1c0 0-3 4-3 5l-1 1c-1 3-3 6-3 8 0 1 0 1-1 1v-1h-2c0 1-1 3-1 4h-3v-1l-1-1c-1 0-3 0-4 1h-1v1h-2l1-2h-1v1l-1-4v-2l-2-4h-1 0c-1-2-4-4-6-5h-1l1-1c-3-1-5 0-8 0-4 1-5 1-8 4h0l1-1v-1c-3 1-5 4-6 6l-2 3c-1 0-1-1-2-2 2-3 3-6 6-8l3-3h1z" class="i"></path><path d="M333 98c1-1 2-3 3-3h1l-1 2v1c0 1 0 2-1 3-1 0 0-1-1-2l-1-1z" class="W"></path><path d="M310 94c4 1 6 2 8 5 0 1-1 1-1 1h-1 0c-1-2-4-4-6-5h-1l1-1z" class="b"></path><path d="M318 99c2 3 2 5 3 8v2h-1v1l-1-4v-2l-2-4s1 0 1-1z" class="c"></path><path d="M332 98h1l1 1c1 1 0 2 1 2 1-1 1-2 1-3 1 1 1 1 2 1-1 3-3 6-3 8 0 1 0 1-1 1v-1h-2c0 1-1 3-1 4h-3v-1l-1-1c-1 0-3 0-4 1h-1v1h-2l1-2v-2h0l3-4h2c1 0 2 0 3-1 1 0 3-2 3-4z" class="R"></path><path d="M332 98h1l1 1c1 1 0 2 1 2 1-1 1-2 1-3 1 1 1 1 2 1-1 3-3 6-3 8 0 1 0 1-1 1v-1h-2-1c0-1 0-2 1-3s2-1 2-3c-1 0-1-2-2-3z" class="B"></path><path d="M324 103h2 0c1 1 2 1 3 1l-1 1v2h1 0 1 0c0 1 0 1 1 2h0v1h-3l-1-1c-1 0-3 0-4 1h-1v1h-2l1-2v-2h0l3-4z" class="F"></path><path d="M324 103l1 1c0 1 0 1-1 2l1 1h2c-2 2-5 1-6 2v-2h0l3-4z" class="L"></path><path d="M434 126c0-1 0-2 1-2h1c1 1 1 1 1 2v3c0 1 1 1 1 1 3 1 5 0 8 0h4v-1h0-1-4-7 0c0-1 0-1 1-1h5c1 0 1 0 2 1 1-1 3-1 5-1v6h-5c-2 0-6 0-8-1h0c2-1 7 0 9 0 1 0 1-1 2 0h1 0v-1h-9c-1 0-3-1-4 0v2c3 2 6 0 9 1h0c2-1 3 0 5 0v4h-6c-2 0-5 0-7-1h0-1v7h1 9 0 2v1h-1v1h2c0-1 0-1 1-1 1 1 0 5 0 6v8c-1 1-1 1-2 0h-3c0 1-1 1-1 1-1-1-1-1-2-1-1 1-3 0-4 1l1 1h-2c0 2 0 3-1 5v1h-6-1v-9-8-4h1l1 1h2v-5l-2-2v-2-9c0-1-1-1 0-2 0-1 1-1 2-1h1l-1-1z" class="U"></path><path d="M430 159v-8-4h1l1 1h0v1c0 1-1 4 0 5h1c1 1 1 5 1 7v1l-1 2c0 1-2 2-2 3v1h-1v-9z" class="C"></path><path d="M438 145h9 0 2v1h-1v1h2c0-1 0-1 1-1 1 1 0 5 0 6v8c-1 1-1 1-2 0h-3c0 1-1 1-1 1-1-1-1-1-2-1-1 1-3 0-4 1l1 1h-2c0 2 0 3-1 5v-7-3-2c0-1 0-2 1-3 1 1 2 0 3 0l10 1v-4h-5v1l-1-1h-7l1 1c2 0 4 1 6 0h0 1 4l1 1c-2 1-9 0-12 1h-2v-4-2h1v-1z" class="X"></path><path d="M423 121v-1c1-1 1-2 2-2 1 1 2 2 2 3h-2l2 2h0v-1h1 1v-1l1-1c0 2 1 3 2 4 1 0 1 0 2 1v1l1 1h-1c-1 0-2 0-2 1-1 1 0 1 0 2v9 2l2 2v5h-2l-1-1h-1v4 8h0v-11h-1v5 13 2c-1 1-8 1-10 1v-1-5-11-5h-3-1v-3h-1v-1c-1-4 0-7 0-10-1-1 0-1 0-1 0-1-1-2-1-3l1-1v-1l3-2h1c-1-1-1-2-1-2v-1c0-2 1-2 2-3l1 1c-1 1-1 1-1 3h1c1 0 1 0 1-1 0 1 1 1 2 2l1-2-1-1z" class="e"></path><path d="M426 128c0 1 0 3 1 4h2v4h0l-1-1-2 1c-1-2-1-6 0-8z" class="B"></path><path d="M426 128c0-1 1-1 2-1 1 1 1 3 1 5h-2c-1-1-1-3-1-4z" class="E"></path><path d="M417 123v-1c0-2 1-2 2-3l1 1c-1 1-1 1-1 3h1c1 0 1 0 1-1 0 1 1 1 2 2l1-2c1 1 2 2 2 3l-1 1h-1 0c0-1-1-2-1-2h-1c0 1 0 1 1 2s1 1 1 3h0c0-1 0-1-1-1h-2l1-1c0-1-1-1-1-2-1-1-1-1-3-1v1c-1-1-1-2-1-2z" class="Y"></path><path d="M426 136l2-1 1 1v10h-3-1v-2c1-3 0-6 1-8z" class="C"></path><path d="M426 147h2c1 2 1 3 1 5v10c0 1-1 3 0 4l-1 2h-2c0-7-1-14 0-21z" class="H"></path><path d="M422 147h2l1 21h1 2l1-2h0v2c-1 1-8 1-10 1v-1-5-11c0-1 1-2 1-4v2 1s0 1 1 1v-4l1-1z" class="U"></path><path d="M419 152c0-1 1-2 1-4v2 1s0 1 1 1l-1 16h5 1 2l1-2h0v2c-1 1-8 1-10 1v-1-5-11z" class="h"></path><path d="M423 121v-1c1-1 1-2 2-2 1 1 2 2 2 3h-2l2 2h0v-1h1 1v-1l1-1c0 2 1 3 2 4 1 0 1 0 2 1v1l1 1h-1c-1 0-2 0-2 1-1 1 0 1 0 2v9 2l2 2v5h-2l-1-1h-1v4 8h0v-11h-1v5 13h0c-1-1 0-3 0-4v-10c0-2 0-3-1-5h-2v-1h3v-10h0v-4c0-2 0-4-1-5l-2-2c0-1-1-2-2-3l-1-1z" class="D"></path><path d="M434 143h-1c0 1-1 1-1 2v1h-2v-14-5c0-1-2-1-2-2 1 0 2 1 2 2h1 1c-1-2-2-3-3-4 1 0 2 1 3 1h0c1 0 1 0 2 1v1l1 1h-1c-1 0-2 0-2 1-1 1 0 1 0 2v9 2l2 2z" class="E"></path><path d="M418 125v-1c2 0 2 0 3 1 0 1 1 1 1 2l-1 1h2c1 0 1 0 1 1h0v14 3l-1 1h-1l-1 1v4c-1 0-1-1-1-1v-1-2c0 2-1 3-1 4v-5h-3-1v-3h-1v-1c-1-4 0-7 0-10-1-1 0-1 0-1 0-1-1-2-1-3l1-1v-1l3-2h1z" class="D"></path><path d="M420 130c0 1 1 1 1 2h2v1c-1 1-1 2-2 2-1-1-1-3-1-5z" class="B"></path><path d="M421 128h2c1 0 1 0 1 1v2c-1 0-1 0-1 1h-2c0-1-1-1-1-2s1-2 1-2z" class="H"></path><path d="M424 129h0v14 3l-1 1c-1-1-2-1-2-1 0-3-1-8 0-11 1 0 1-1 2-2v-1c0-1 0-1 1-1v-2zm-6-4v-1c2 0 2 0 3 1-1 1-1 1-3 1 0 1 0 1 1 1v2h-1v1c1 2 1 5 1 7v9c-1 1-3 0-4 1h1-1v-3h-1v-1c-1-4 0-7 0-10-1-1 0-1 0-1 0-1-1-2-1-3l1-1v-1l3-2h1z" class="C"></path><path d="M418 130c1 2 1 5 1 7-1 0-2-1-2-2v-3s1 0 1-1v-1z" class="G"></path><path d="M414 132h0c1-1 1-1 1-2v3c-1 3 0 8 0 11h-1v-1c-1-4 0-7 0-10-1-1 0-1 0-1z" class="Y"></path><path d="M418 125v-1c2 0 2 0 3 1-1 1-1 1-3 1 0 1 0 1 1 1v2h-1l-1-1-2 2c0 1 0 1-1 2h0c0-1-1-2-1-3l1-1v-1l3-2h1z" class="f"></path><path d="M428 92c7 0 15-1 23 0h0v1l-3 1h-1c1 1 3 0 4 0v12c-2 0-5 0-8 1h-1-2-1 0c2 1 3 1 5 0l2 1c1-1 2-1 3-1h1l1-1h0c0 3 1 6-1 8-1 0 0 0-1 1v1h1v-2l1 1v2h-4c-1 0 0-1 0-2h-1c-2 2-2 2-5 2h-4c1 0 1 1 2 1 2 0 6 1 8 0h0 3l1-1v2 4 4h-9c-2 0-3 0-5-1 0-1 0-1-1-2h-1c-1 0-1 1-1 2v-1c-1-1-1-1-2-1-1-1-2-2-2-4l-1 1v1h-1-1v1h0l-2-2h2c0-1-1-2-2-3-1 0-1 1-2 2v1l1 1-1 2c-1-1-2-1-2-2 0 1 0 1-1 1h-1c0-2 0-2 1-3l-1-1c0-1 1-2 2-3v-4l1-1v-4c0-3 1-4 2-7h1v-1c0-1 1-1 1-2h1l2-2c1 0 3-1 4-2-1-1-4-1-5-1z" class="B"></path><path d="M443 99h1c-1 1-1 1-1 2v1l-1 1-1-1-1 1h-2v-1s1-1 2-1h1c0-1 1-2 2-2z" class="X"></path><path d="M430 97h1 0c1-1 2-1 3-2l-5 8c-1 2-1 2-1 4v1h-1c0-1-1-2-2-2 1-3 3-7 5-9z" class="W"></path><path d="M433 93h2 0c-2 1-4 2-5 4-2 2-4 6-5 9 0 1-1 4-1 4-1 1-1 1-2 1h0 0v-4c0-3 1-4 2-7h1v-1c0-1 1-1 1-2h1l2-2c1 0 3-1 4-2z" class="N"></path><path d="M437 120c1-1 7-1 9 0 1 0 4-1 5-1v4c-3 1-6 1-8 1h-1c-2 0-3 0-5-1v-1h0v-2z" class="b"></path><path d="M437 122c1 0 1 1 2 0h2 1v1l1 1h-1c-2 0-3 0-5-1v-1h0z" class="d"></path><path d="M437 122c-2-2-4-5-5-8-1-4 0-9 2-13l2-3c0-1 2-2 3-2h0c0 1-1 1-1 2-2 3-3 7-3 10 0 1 1 2 0 3h-1l-1 1s0 1 1 1l1-1 1 1-2 2v1l1 1c0-1 0-2 1-2l1 5v2h0z" class="I"></path><path d="M428 114l1 1c1 0 1 0 1 1h0l1 1c0 2 2 4 3 5l1 1h2c2 1 3 1 5 1h1c2 0 5 0 8-1v4h-9c-2 0-3 0-5-1 0-1 0-1-1-2h-1c-1 0-1 1-1 2v-1c-1-1-1-1-2-1-1-1-2-2-2-4h0c0-1-1-2-2-3h-1l1-1-1-1 1-1z" class="C"></path><path d="M428 114l1 1c0 1 0 2 1 3s2 2 2 3v1l-2-2h0c0-1-1-2-2-3h-1l1-1-1-1 1-1z" class="a"></path><path d="M428 107c0-1 1-2 1-3 1 0 1-1 1-1 1-2 2-5 4-6 1-1 3-2 3-4h3l-1 1c-1 0-2 2-3 3-2 1-3 2-4 4h0v1s-1 1-1 2h-1v3h0c-1 1-1 2-1 3 0 0 0 1-1 1v3l-1 1 1 1-1 1h1c1 1 2 2 2 3h0l-1 1v1h-1-1v1h0l-2-2h2c0-1-1-2-2-3-1 0-1 1-2 2v1l1 1-1 2c-1-1-2-1-2-2 0 1 0 1-1 1h-1c0-2 0-2 1-3l-1-1c0-1 1-2 2-3v-4l1-1h0 0c1 0 1 0 2-1 0 0 1-3 1-4 1 0 2 1 2 2h1v-1z" class="J"></path><path d="M421 122v-1c0-1 0-3 1-3v1h1v2h0l1 1-1 2c-1-1-2-1-2-2z" class="S"></path><path d="M378 126c2 0 3 1 5 1h0c2 0 4 0 5 1l5 2c0-2-1-2-2-3h3c2 1 4 2 6 2h5c2 0 4-1 6-1l3-1v1l-1 1c0 1 1 2 1 3 0 0-1 0 0 1 0 3-1 6 0 10v1h1v3h1 3v5 11 5 1l-40-1h-1c1-2 4-5 5-8s1-7 2-10l1-1c-1-1-1-2-2-2 0-1 0-2 1-3l-2-3h0c1-3 0-5-1-8 0 0 0-1-1-2l-1-1s-1-1-1-2h-1l-1-2h1z" class="X"></path><path d="M406 132l2-1v5c1 2 1 7 0 8v-5h-2 0c1-2 0-5 0-7z" class="K"></path><path d="M402 132h2 2c0 2 1 5 0 7h0 2v5c0 1 0 1-1 2h-1l-1-1h-1 0-1 0v-8-3c-1 0-1-1-1-2z" class="R"></path><path d="M404 132h2c0 2 1 5 0 7h0c-1 0-1 0-1 1v1c-1-2 0-5-1-6v-1-2z" class="C"></path><path d="M405 141v-1c0-1 0-1 1-1h2v5c0 1 0 1-1 2h-1l-1-1v-4z" class="H"></path><path d="M413 129c0 1 1 2 1 3 0 0-1 0 0 1 0 3-1 6 0 10v1c0 1 0 2-1 3-1-1-1-1-2-1h-1c-1-1 0-7 0-9-1-1 0-3-1-5v-1c1 0 2-1 3-1l1-1z" class="C"></path><path d="M388 128l5 2c2 1 3 1 5 1v4h1v-3h3c0 1 0 2 1 2v3 8c-1 0-1 1-2 0l-1-1h-1c0 1 0 1-1 1h-2l-1 1-1-1c0-1 0-2-1-3 0-1-1-3-2-4h0l-1-1v-2c-1-2-2-4-3-5v-1l1 1v-2z" class="H"></path><path d="M391 138h0l-1-1v-2c-1-2-2-4-3-5v-1l1 1c1 0 1 1 2 2h1c1 0 1 2 2 3s1 2 1 4c1 1 1 1 1 2-1 1-1 1-2 1 0-1-1-3-2-4z" class="K"></path><path d="M407 148c1 1 1 6 1 8 0 1 0 5 1 6 1-2 1-12 1-14l4-1v19h1v-7-12h1 3v5 11 5h-8-3-5v-12c0-2 0-6 1-7h2v-1h1z" class="i"></path><path d="M406 158h0l1 1c0 2 1 3-1 6h0-1l1-7z" class="Q"></path><path d="M408 168c1-2 1-3 2-5l1-1v6h-3z" class="E"></path><path d="M395 146l1-1h2c1 0 1 0 1-1h1l1 1c1 1 1 0 2 0h0 1 0 1l1 1h1c1 1 1 1 0 2h-1v1h-2c-1 1-1 5-1 7v12h5 3 8v1l-40-1h7c1-1 2-5 3-7 1 0 1-2 2-3 1-2 1-4 2-7v-1l1 1h0v-1c1-1 1-1 2-1v-1c-1-1-1-1-1-2z" class="U"></path><defs><linearGradient id="AF" x1="398.45" y1="154.913" x2="406.439" y2="161.784" xlink:href="#B"><stop offset="0" stop-color="#666967"></stop><stop offset="1" stop-color="#7e7a7e"></stop></linearGradient></defs><path fill="url(#AF)" d="M395 146l1-1h2c1 0 1 0 1-1h1l1 1c1 1 1 0 2 0h0 1 0 1l1 1h1c1 1 1 1 0 2h-1v1h-2c-1 1-1 5-1 7v12h5 3 8v1l-40-1h7c1-1 2-5 3-7 1 0 1-2 2-3 1-2 1-4 2-7v-1l1 1c-1 6-4 11-7 17h15c-2-6-1-12-3-19h-1-2v-1c-1-1-1-1-1-2z"></path><path d="M406 148h-1c-1 0-1-1 0-2h1 1c1 1 1 1 0 2h-1zm-11-2l1-1h2c1 0 1 0 1-1h1l1 1c1 1 1 0 2 0h0 1v1 2c-2 1-2 0-4 2l-1-1h-1 0-2v-1c-1-1-1-1-1-2z" class="d"></path><path d="M378 126c2 0 3 1 5 1h0c2 0 4 0 5 1v2l-1-1v1c1 1 2 3 3 5v2l1 1h0c1 1 2 3 2 4 1 1 1 2 1 3l1 1c0 1 0 1 1 2v1c-1 0-1 0-2 1v1h0l-1-1v1c-1 3-1 5-2 7-1 1-1 3-2 3-1 2-2 6-3 7h-7-1c1-2 4-5 5-8s1-7 2-10l1-1c-1-1-1-2-2-2 0-1 0-2 1-3l-2-3h0c1-3 0-5-1-8 0 0 0-1-1-2l-1-1s-1-1-1-2h-1l-1-2h1z" class="U"></path><path d="M389 161v-3h2c-1 1-1 3-2 3z" class="C"></path><path d="M385 144h0v1h3c1 1 0 2 0 4l-1 1h-2l1-1c-1-1-1-2-2-2 0-1 0-2 1-3z" class="Q"></path><path d="M390 150c1-1 1-1 2-1l2 1v1h0l-1-1v1c-1 3-1 5-2 7h-2c0-3 1-6 3-8h0-2z" class="P"></path><path d="M391 138c1 1 2 3 2 4 1 1 1 2 1 3l1 1c0 1 0 1 1 2v1c-1 0-1 0-2 1l-2-1c-1 0-1 0-2 1h-1v-5h3v-1c0-2 0-4-1-6z" class="N"></path><path d="M394 145l1 1c0 1 0 1 1 2v1c-1 0-1 0-2 1l-2-1h1v-2-1l1-1z" class="V"></path><path d="M378 126c2 0 3 1 5 1h0c2 0 4 0 5 1v2l-1-1v1c1 1 2 3 3 5v2l1 1h0c1 2 1 4 1 6h0c-1-1-3-3-3-4h0l-2-2-1 1v1c-1 2-1 3-1 4h0l-2-3h0c1-3 0-5-1-8 0 0 0-1-1-2l-1-1s-1-1-1-2h-1l-1-2h1z" class="C"></path><path d="M383 127h0c1 1 1 1 1 2v1s0 1 1 1h0c1 1 2 3 1 4h0v2h-1v-1s0-1-1-2v-1-1c-1-1-1-2-2-3 0-1 0-1 1-2z" class="i"></path><path d="M370 91c1 0 1 1 2 1 1-1 5 0 7 0h0c-1 2-3 4-5 6-1 3-4 11-3 14 0 0 1 0 1 1 0 0 1 1 1 2v1c1 2 3 5 4 7v1h-2c-1 0-3 0-4-1l-2 1v2h-3c-3-1-4-1-6 0-1-1-1-1-2-1-2 1-4 0-6 0h-6c-2-1-4 1-6 0l1-2c1 0 3 0 4 1 0-2 0-2-1-3h-2-1-4-7c-1 0-5 1-7 0l-3-3 1-2h-1c0 1-1 1-1 3h-1v-1c1-2 3-5 2-7h2v-1h1c1-1 3-1 4-1l1 1v1h3c0-1 1-3 1-4h2v1c1 0 1 0 1-1 0-2 2-5 3-8l1-1c0-1 3-5 3-5 1-1 2-1 2-1h0c2-1 6-1 9-1h5l3 1h0l-1 1 1 1 2-3h7z" class="U"></path><path d="M360 111v2c0 1 1 2 1 2-1 1-1 1-2 1 0 0 0-1-1-1 0-1 1-3 1-4l1-1h0c0-2-1-2-2-3l1-1c1 0 1 1 2 2h1c0 1-1 1-2 2v1z" class="B"></path><path d="M355 112c1 1 2 1 2 2s1 2 1 3c1 1 3 4 3 5h-1c-1-1-1-2-3-2-1-1-3-3-3-4l1-1v1l1 1h1c-1-1-2-3-3-4l1-1z" class="E"></path><path d="M360 93l1 1c-1 1-2 2-3 4-1 3-3 6-3 9v1c1 1 1 2 1 3 0 0 0 1-1 1l-1 1s-1-1-2-1v-4c3-3 3-6 5-10 1-1 2-3 3-5z" class="c"></path><path d="M344 112v-1c1 0 1-1 1-2s0-2 1-3v2l1-1h2c1 1 2 1 3 1v4-1c-2 1-2 1-4 1h-1c0 2 1 4 2 5 1 2 2 4 4 6h2c-2 0-6 1-8 1v-1h3v-2c-1-1-1-2-2-3 0-1-1-2-1-2-1-1-1-2-2-3 0-1 0-1-1-1z" class="R"></path><path d="M349 107c1 1 2 1 3 1v4-1c-2 1-2 1-4 1 0-1-1-1-2-2 0-1 0-1 1-2l2-1z" class="D"></path><path d="M355 123h10 6l-2 1v2h-3c-3-1-4-1-6 0-1-1-1-1-2-1-2 1-4 0-6 0h-6c-2-1-4 1-6 0l1-2c1 0 3 0 4 1h2c2 0 6-1 8-1z" class="X"></path><path d="M373 115v1c1 2 3 5 4 7v1h-2c-1 0-3 0-4-1h-6 3v-2h0c0-1 0-1-1-1-1-1-2-3-2-4h0 2c1 0 2 1 2 1v1c0-1 0-1 1-1 0-1 1-1 1-2h1v1l1-1z" class="H"></path><g class="U"><path d="M358 91l3 1h0l-1 1c-1 2-2 4-3 5-2 4-2 7-5 10-1 0-2 0-3-1h-2l-1 1v-2c2-5 4-9 8-14l4-1z"></path><path d="M353 91h5l-4 1c-4 5-6 9-8 14-1 1-1 2-1 3s0 2-1 2v1h-1-1c-1-1-3-1-4-1l-1-1h-1l-1-1h-1-1l1-1c1 0 1 0 1-1 0-2 2-5 3-8l1-1c0-1 3-5 3-5 1-1 2-1 2-1h0c2-1 6-1 9-1z"></path></g><path d="M338 99l1-1c0-1 3-5 3-5 1-1 2-1 2-1v1l-4 4c-1 3-1 5-2 7-1 1-1 2-1 3l2 1c-1 0-2 1-2 2h-1l-1-1h-1-1l1-1c1 0 1 0 1-1 0-2 2-5 3-8z" class="W"></path><path d="M353 91h5l-4 1c-4 5-6 9-8 14-1 1-1 2-1 3s0 2-1 2v1h-1-1c-1-1-3-1-4-1l-1-1c0-1 1-2 2-2h2c1-1 1-1 2 0h1c1-3 2-6 3-8l6-9z" class="b"></path><path d="M341 108c0 1 0 3 2 4h-1c-1-1-3-1-4-1l-1-1c0-1 1-2 2-2h2z" class="D"></path><path d="M370 91c1 0 1 1 2 1 1-1 5 0 7 0h0c-1 2-3 4-5 6-1 3-4 11-3 14 0 0 1 0 1 1 0 0 1 1 1 2l-1 1v-1h-1c0 1-1 1-1 2-1 0-1 0-1 1v-1s-1-1-2-1h-2 0l-1-3-1-1c-1 0-2-1-3-1v-1c1-1 2-1 2-2 1 0 1-2 1-3 1-1 1-2 1-4 1-3 4-6 6-9v-1z" class="U"></path><path d="M370 91c1 0 1 1 2 1-3 4-9 10-8 16h0c1 1 1 1 2 1v1c1-1 1-1 1-2h1v2l1 1-1 1c-1 0-1-1-2-1l-1 1v1 3h0l-1-3-1-1c-1 0-2-1-3-1v-1c1-1 2-1 2-2 1 0 1-2 1-3 1-1 1-2 1-4 1-3 4-6 6-9v-1z" class="D"></path><path d="M332 107h2v1l-1 1h1 1l1 1h1l1 1c1 0 3 0 4 1h1 1c1 0 1 0 1 1 1 1 1 2 2 3 0 0 1 1 1 2 1 1 1 2 2 3v2h-3v1h-2c0-2 0-2-1-3h-2-1-4-7c-1 0-5 1-7 0l-3-3 1-2h-1c0 1-1 1-1 3h-1v-1c1-2 3-5 2-7h2v-1h1c1-1 3-1 4-1l1 1v1h3c0-1 1-3 1-4z" class="H"></path><path d="M345 118h2 1c1 1 1 2 2 3-3 0-3-1-5-3z" class="B"></path><path d="M333 118h3c1 0 1 1 2 1h-2c-2 0-4 0-6-1h0 3z" class="P"></path><path d="M345 113c1 1 1 2 2 3 0 0 1 1 1 2h-1-2 0v-5z" class="K"></path><path d="M332 107h2v1l-1 1h1 1l1 1h1l1 1c1 0 3 0 4 1h-3s0 1-1 1l2 2v1h-1-1l2 1c0 1 0 1 1 2h-1s-1 1-2 0c-1 0-1-1-2-1h-3-3v-3l1-1v-3c0-1 1-3 1-4z" class="F"></path><path d="M334 113l-1-1v-1h5c1 0 3 0 4 1h-3s0 1-1 1-1-1-2-1h0v2c-1 0-1-1-2-1zm-3 1h0v1c1 0 2-1 3 0 1 0 1 1 2 1v2h-3-3v-3l1-1z" class="B"></path><path d="M330 115c1 1 2 1 3 1v1 1h-3v-3z" class="Y"></path><path d="M332 107h2v1l-1 1h1 1l1 1h1l1 1h-5v1l1 1-1 1h-2 0v-3c0-1 1-3 1-4z" class="L"></path><path d="M322 111v-1h1c1-1 3-1 4-1l1 1v1h3v3l-1 1v3h0-3c-1 0-1-1-2-2l-1 1c-2 0-2 0-4 1l1-2h-1c0 1-1 1-1 3h-1v-1c1-2 3-5 2-7h2z" class="K"></path><path d="M322 111v-1h1c1-1 3-1 4-1l1 1v1c0 1-2 2-2 2h-2l1 1c0 1 0 1-1 2-1-1-2-2-2-4l1-1h-1z" class="D"></path><path d="M379 92h1 7 32 9c1 0 4 0 5 1-1 1-3 2-4 2l-2 2h-1c0 1-1 1-1 2v1h-1c-1 3-2 4-2 7v4l-1 1v4c-1 1-2 2-2 3-1 1-2 1-2 3v1s0 1 1 2h-1l-3 2-3 1c-2 0-4 1-6 1h-5c-2 0-4-1-6-2h-3c1 1 2 1 2 3l-5-2c-1-1-3-1-5-1h0c-2 0-3-1-5-1v-2h-3 0 2v-1c-1-2-3-5-4-7v-1c0-1-1-2-1-2 0-1-1-1-1-1-1-3 2-11 3-14 2-2 4-4 5-6h0z" class="S"></path><path d="M414 98h0l1 1c-1 1-3 1-4 2l-1-1c1-1 2-2 4-2z" class="L"></path><path d="M412 106l1 1v2 3 1h-1v-1c0-2-1-4 0-6z" class="J"></path><path d="M414 98c1-1 2-1 4-1l3 1v1h-1l-1 1s0 1-1 2h-2c-2 1-4 3-5 4h-1c0-1 1-3 2-3 2-2 4-1 6-3v-1c-1 0-2-1-3-1v1l-1-1h0z" class="M"></path><path d="M410 100l1 1c-3 2-4 4-5 8 0 0 0 2 1 3l3 3-1 1c-2-2-3-4-5-5v-2c1-4 3-6 6-9z" class="V"></path><path d="M412 106l1-1h1c0 1 0 1 1 1 1 1 2 0 3 0l1-1h1 0c1 1 1 2 2 2v4l-1 1h0v-3c0-1-1-1-2-2l-3 3h1v1h-1v-1h-1v-1h-2v-2l-1-1z" class="M"></path><path d="M410 115h1c0 1 1 1 1 1h5c0-1 1-1 1-1h1c1-2 0-1 0-3h2 0v4c-1 1-2 2-2 3-1 1-2 1-2 3v1h-1l-1-1v-2l-1-1c1 0 2-1 3-1h0v-1c-2 0-4 1-5 0-1 0-2-1-3-1h0l1-1z" class="Q"></path><g class="F"><path d="M404 111c2 1 3 3 5 5h0c1 0 2 1 3 1 1 1 3 0 5 0v1h0c-1 0-2 1-3 1l1 1v2l-1 1-1-1h0c-1 0-2-1-3-1 0 0 0-1-1-1v-1h-1c-2-2-4-4-5-8h1zm1-12l3-3h1l1-1h1 0c2-1 4-1 5 0l4 1c1 1 1 1 1 2l-3-1c-2 0-3 0-4 1-2 0-3 1-4 2-3 3-5 5-6 9h-1v-1c-1-1-1-1 0-2v-3h0c0-1 0-2 1-2l1-2z"></path><path d="M422 95h1c1 0 1 1 2 1s1-2 3-2c0 1 1 1 1 1l-2 2h-1c0 1-1 1-1 2v1h-1c-1 3-2 4-2 7-1 0-1-1-2-2h0-1l-1 1c-1 0-2 1-3 0-1 0-1 0-1-1h-1l-1 1c-1 2 0 4 0 6h0-1c0-1-1-1-1-2 1-1 0-3 1-4s3-3 5-4h2c1-1 1-2 1-2l1-1h1v-1c0-1 0-1-1-2 1 0 1 0 2-1z"></path></g><path d="M418 102c1-1 1-2 1-2l1-1c1 1 1 2 1 3 0 0 0 1-1 2 0 1 0 0-1 1-1-1-1-2-1-3z" class="C"></path><path d="M420 96c1 0 1 0 2-1v1c1 1 2 2 2 3h-1l-1 1 1 1c-1 0-1 0-2 1 0-1 0-2-1-3h1v-1c0-1 0-1-1-2z" class="B"></path><path d="M403 103h0v3c-1 1-1 1 0 2v1h1v2h-1c1 4 3 6 5 8h1v1c1 0 1 1 1 1 1 0 2 1 3 1h0l1 1 1-1 1 1h1s0 1 1 2h-1l-3 2-3 1c-2 0-4 1-6 1h-5c0-1-1-1-1-2h-2c-1-2-4-3-5-6l-1-1c1 0 2-1 2-2h1 1v-1c-1-1-2-2-2-3v-1c2-1 1 0 2 1v-1l2-1c0-1 1-2 1-3v-1c1 0 1-1 2-1v-1h1c1-1 1-2 2-3z" class="S"></path><path d="M410 121c1 0 2 1 3 1h0l1 1 1-1 1 1h1s0 1 1 2h-1l-3 2-3 1c0-1 1-2 1-2v-1-1h-1-3-1l-1-1v-1h1l1 1h2v-2z" class="G"></path><path d="M415 122l1 1h1s0 1 1 2h-1c-1 0-2 1-3 0s0-2 0-2l1-1z" class="P"></path><path d="M403 103h0v3c-1 1-1 1 0 2v1h1v2h-1c1 4 3 6 5 8 0 0-1-1-2-1-3 0-2-2-4-5-1 0-2-1-3-2h-1 0v3h-1c0-1-1-1-2-1l2-1c0-1 1-2 1-3v-1c1 0 1-1 2-1v-1h1c1-1 1-2 2-3z" class="N"></path><path d="M403 103h0v3c-1 1-1 1 0 2v1 2h-3c-1-2 0-3 0-4v-1h1c1-1 1-2 2-3z" class="Z"></path><path d="M395 113c1 0 2 0 2 1h1v-3h0 1v4 1l1 1 1 1c0 1 1 2 2 3l1 1c-1 1-1 1-2 1l-1-1v2s1 1 2 1c0 1 0 1 1 1 0 1 1 1 2 1v-1l1 1s0 1-1 1h0c-1 0-1 1-1 1h-5c0-1-1-1-1-2h-2c-1-2-4-3-5-6l-1-1c1 0 2-1 2-2h1 1v-1c-1-1-2-2-2-3v-1c2-1 1 0 2 1v-1z" class="B"></path><g class="E"><path d="M393 118h1v2c-1 1-2 0-1 1l1 1h1 0c1 2 3 3 4 5h-2c-1-2-4-3-5-6l-1-1c1 0 2-1 2-2z"></path><path d="M387 92h32 9c1 0 4 0 5 1-1 1-3 2-4 2 0 0-1 0-1-1-2 0-2 2-3 2s-1-1-2-1h-1c-1 1-1 1-2 1l-4-1c-1-1-3-1-5 0h0-1l-1 1h-1l-3 3-1 2c-1 0-1 1-1 2-1 1-1 2-2 3h-1v1c-1 0-1 1-2 1v1c0 1-1 2-1 3l-2 1v1c-1-1 0-2-2-1v1c0 1 1 2 2 3v1h-1-1c0 1-1 2-2 2h-1v-1l1-1h0c0-1-1-1-1-2h-2c-1-1-1-2-1-3l2-1h-2s-1 1-2 1v-1c-1-1-1-1-1-2s0-1 1-1c0 0 0-1 1-1l-2-2c0-1 0-4 1-5s2-2 2-4c0-1 0-2 1-2-1-1-1-2-1-3z"></path></g><path d="M398 108l-1-2h0c1-3 2-4 3-6l3-3h0c-1 3-3 4-4 7 0-1 1-1 2-2h1s1-2 2-3h1l-1 2c-1 0-1 1-1 2-1 1-1 2-2 3h-1v1c-1 0-1 1-2 1z" class="B"></path><path d="M391 98c0 2-5 8-3 10l1-1v2 3h0-2s-1 1-2 1v-1c-1-1-1-1-1-2s0-1 1-1c0 0 0-1 1-1 1-4 3-8 5-10z" class="Z"></path><path d="M388 95h1c0-1 0-1 1-1v-1c1 0 1 0 1-1h3c0 2-1 3-2 4h0c-1 1-1 1-1 2-2 2-4 6-5 10l-2-2c0-1 0-4 1-5s2-2 2-4c0-1 0-2 1-2z" class="H"></path><path d="M389 109l2-2 1 1v-1c0-3 2-7 4-9 0-1 1-2 2-3s1-2 2-2c-1 3-4 4-5 7 0 2-2 4-1 7h0l1-1c2-2 3-5 5-8 1-2 3-3 4-4 1 0 2-1 2-1 1-1 1-1 2 0-2 1-4 2-5 3v1l-3 3c-1 2-2 3-3 6h0l1 2v1c0 1-1 2-1 3l-2 1v1c-1-1 0-2-2-1v1c0 1 1 2 2 3v1h-1-1c0 1-1 2-2 2h-1v-1l1-1h0c0-1-1-1-1-2h-2c-1-1-1-2-1-3l2-1h0v-3z" class="c"></path><path d="M389 112c1 0 1 0 2 1 0 0 1 1 1 2 1 1 1 2 1 3s-1 2-2 2h-1v-1l1-1h0c0-1-1-1-1-2h-2c-1-1-1-2-1-3l2-1h0z" class="C"></path><path d="M379 92h1 7c0 1 0 2 1 3-1 0-1 1-1 2 0 2-1 3-2 4s-1 4-1 5l2 2c-1 0-1 1-1 1-1 0-1 0-1 1s0 1 1 2v1c1 0 2-1 2-1h2l-2 1c0 1 0 2 1 3h2c0 1 1 1 1 2h0l-1 1v1h1l1 1c1 3 4 4 5 6h2c0 1 1 1 1 2-2 0-4-1-6-2h-3c1 1 2 1 2 3l-5-2c-1-1-3-1-5-1h0c-2 0-3-1-5-1v-2h-3 0 2v-1c-1-2-3-5-4-7v-1c0-1-1-2-1-2 0-1-1-1-1-1-1-3 2-11 3-14 2-2 4-4 5-6h0z" class="V"></path><path d="M373 114c0-1 0-1 1-2h2 0s1 1 2 1c0-1 0-1 1-1h0 1c1 1 1 1 1 2v1c0 1 0 1 1 2h1v1c1 1 3 3 4 5 0 0 1 1 1 2h0-1c-1 0-3 0-4-1 0-1 0-1-1-2h0c0 1 0 1-1 2h0c-1 0-3-1-3-2h-1c-2-3-2-5-4-8z" class="C"></path><path d="M373 114c0-1 0-1 1-2h2 0s1 1 2 1c0-1 0-1 1-1h0 1c1 1 1 1 1 2v1c0 1 0 1 1 2h1v1c-1 0-3-1-4-1 0 0 0 1-1 1-1-1-2-2-2-3h0c-1-1-2-1-3-1z" class="i"></path><path d="M385 112v1c1 0 2-1 2-1h2l-2 1c0 1 0 2 1 3h2c0 1 1 1 1 2h0l-1 1v1h1l1 1c1 3 4 4 5 6h2c0 1 1 1 1 2-2 0-4-1-6-2h-3-1c0-1-1-1-1-1-1 0-2 0-2-1h1 0c0-1-1-2-1-2-1-2-3-4-4-5v-1h-1c-1-1-1-1-1-2v-1c0-1 0-1-1-2h5z" class="e"></path><path d="M388 116h2c0 1 1 1 1 2h0l-1 1v1h1l1 1c1 3 4 4 5 6h2c0 1 1 1 1 2-2 0-4-1-6-2v-1c-1 0-2-1-3-2h-1c-1-1-3-3-3-4 1-2 1-1 2-2 0 0-1-1-1-2z" class="F"></path><path d="M380 92h7c0 1 0 2 1 3-1 0-1 1-1 2 0 2-1 3-2 4s-1 4-1 5l2 2c-1 0-1 1-1 1-1 0-1 0-1 1s0 1 1 2h-5-1l1-1-1-1v-3c0-1 1-2 0-3-1 1-1 2-1 3l-1 1h0l-1-1h-1c-1 1-1 1-2 0v-1c0-5 4-10 7-14z" class="U"></path><path d="M387 97c0 2-1 3-2 4s-1 4-1 5l2 2c-1 0-1 1-1 1-1 0-1 0-1 1s0 1 1 2h-5-1l1-1v-1c0-2 0-3 1-5 1-3 0-4 2-7v1c-1 2-1 5-2 7l1 1c1-1 1-4 2-5 1-2 1-3 3-5z" class="d"></path><path d="M330 121h7 4 1 2c1 1 1 1 1 3-1-1-3-1-4-1l-1 2c2 1 4-1 6 0h6c2 0 4 1 6 0 1 0 1 0 2 1 2-1 3-1 6 0h3v-2l2-1c1 1 3 1 4 1h0 3v2h-1l1 2h1c0 1 1 2 1 2l1 1c1 1 1 2 1 2 1 3 2 5 1 8h0l2 3c-1 1-1 2-1 3 1 0 1 1 2 2l-1 1c-1 3-1 7-2 10s-4 6-5 8h0c-3 0-7 1-10 0h-7-1c-2 1-4 0-5 0-5 1-10 1-15 0h-9-5-12-6c1-1 2-2 3-2l2-2c1-1 1-2 2-3h1c0-1 1-2 1-4 1-2 2-4 2-6l1-2h1l-1-1c1-2 2-2 3-2l-1-1v-1l-1-1 2-2c-1 0-2-1-3-2h0c0-1-1-2-1-3v-2h-1c0-2-2-3-3-4l-1-1v-1c-2-1-5-1-7-1v-2h-5l-1-1h6 0 5 9v-1h0 1l1-1c3 0 5 1 7-1z" class="O"></path><path d="M336 166l2-2h1c1 0 1 1 1 2l1 1h-1-3l-1-1z" class="B"></path><path d="M347 159c1-1 2-1 4-2h0c0 1-1 2-2 3h0c-2 0-3 1-5 1l-2-1v-1c1 1 4 1 5 0h0z" class="a"></path><path d="M334 158h0c1 1 1 2 1 3 1 1 2 2 3 2h3 0v1 1l1-1-2 2c0-1 0-2-1-2h-1l-2 2c-1-1 0-2-1-3h-1c0 1-1 1-1 2 0-2 1-3 0-5 0 0 1-1 1-2z" class="e"></path><path d="M336 155v1h1c1 0 2 1 2 2s0 4 1 4h0 1 0l1 1v1l-1 1v-1-1h0-3c-1 0-2-1-3-2 0-1 0-2-1-3h0c1-1 2-2 2-3z" class="D"></path><path d="M323 158h1c1 1 3 1 5 2h0-1l1 2h1 0 1 1l1-2c1 2 0 3 0 5v2h-1 0-2c-2-1-2-5-3-6v6h-1l-2-3h1 1v-4h-2c-1 0-2 1-3 2s-2 4-3 4l-1-1 3-3 3-4z" class="R"></path><path d="M333 160c1 2 0 3 0 5v2h-1 0v-2c-1-2-1-2 0-3l1-2z" class="F"></path><path d="M333 153c1 0 2 0 3 1v1c0 1-1 2-2 3 0 1-1 2-1 2l-1 2h-1-1 0-1l-1-2h1 0c-2-1-4-1-5-2l2-3h1c1 1 1 2 3 2 1 0 1-1 2-2h0c0-1 0-2 1-2z" class="f"></path><path d="M329 160h2v2h-1 0-1l-1-2h1z" class="S"></path><path d="M317 165l1 1c1 0 2-3 3-4s2-2 3-2h2v4h-1-1l2 3v1h-12c1-2 2-2 3-3z" class="B"></path><path d="M324 164s-1-1-1-2c0 0 1-1 1-2l1 1v3h-1z" class="L"></path><path d="M319 151l1-2h1 2l3 1v2c-1 1-1 2-2 2l1 1 1-1v1h0l-2 3h-1l-3 4-3 3c-1 1-2 1-3 3h-6c1-1 2-2 3-2l2-2c1-1 1-2 2-3h1c0-1 1-2 1-4 1-2 2-4 2-6z" class="H"></path><path d="M324 154l1 1c-1 0-2 1-3 2l-1 1v-3h1 1l1-1z" class="G"></path><path d="M321 158l1-1 1 1-3 4c0-2 0-2 1-4z" class="K"></path><path d="M325 155l1-1v1h0l-2 3h-1l-1-1c1-1 2-2 3-2z" class="F"></path><path d="M348 144h3v1 1c1 1 2 2 2 3h-3l-2 1h1c1 1 2 0 2 1v1l1 1v1 1l-5 2v2h0c-1 1-4 1-5 0v1l2 1c-1 0-2 0-3 1h-1 0c-1 0-1-3-1-4s-1-2-2-2l1-2c1 0 2 0 3 1v1h2v-1l-1-1v-1c0-1-1-1-1-2l1-1h-1l-2-2 1-3s1 0 1-1c1 1 2 1 4 0h2 1z" class="f"></path><path d="M342 159s1-1 1-2h4v2h0c-1 1-4 1-5 0z" class="K"></path><path d="M346 149h3 1l-2 1h1c1 1 2 0 2 1v1l1 1c-2 2-3 2-5 2h-4l-1-1v-1c0-1-1-1-1-2l1-1c1 0 3-1 4-1z" class="N"></path><path d="M351 152l1 1c-2 2-3 2-5 2h-4l-1-1v-1h5c2 0 3-1 4-1z" class="E"></path><path d="M348 144h3v1 1c1 1 2 2 2 3h-3-1-3c-1 0-3 1-4 1h-1l-2-2 1-3s1 0 1-1c1 1 2 1 4 0h2 1z" class="Z"></path><path d="M345 147l1-1c1 0 2 0 3-1h0 1l1 1c1 1 2 2 2 3h-3-1-3-1c0-1-1-1-1-2v-1l1 1z" class="c"></path><path d="M345 147l1-1c1 0 2 0 3-1h0 1v1c-1 0-2 1-3 2-1 0-1 0-2-1z" class="I"></path><path d="M334 136l3-2h2v1h0c1 2 1 2 2 2 1 1 2 1 3 1h1c0 2 0 5 1 6h1-2c-2 1-3 1-4 0 0 1-1 1-1 1l-1 3 2 2h1l-1 1c0 1 1 1 1 2v1l1 1v1h-2v-1c-1-1-2-1-3-1l-1 2h-1v-1-1c-1-1-2-1-3-1s-1 1-1 2h0c-1 1-1 2-2 2-2 0-2-1-3-2h-1 0v-1l-1 1-1-1c1 0 1-1 2-2v-2l-3-1h-2l-1-1c1-2 2-2 3-2l-1-1v-1l-1-1 2-2c-1 0-2-1-3-2h0c1-1 1-2 3-2h0l1 2h2c1-1 2-1 2-1 1 1 1 1 1 2h0 1c1-2 2-3 4-4z" class="e"></path><path d="M328 143l1-1 2 2h0l-3 1v-2z" class="G"></path><path d="M334 136l3-2h2v1h0c-1 1-1 1-1 2l-1 1h-1l-1 1c-1 1-1 1-2 1h0c1-1 1-3 1-4z" class="D"></path><path d="M328 138c1 1 1 1 1 2h-2v-1l-1 1c0 1 0 1 1 1 0 1 1 1 1 2v2c-1 0-2-1-3 0v-2h1 0l-2-4h2c1-1 2-1 2-1z" class="a"></path><path d="M320 139c1-1 1-2 3-2h0l1 2 2 4h0-1v2h-1l-1 1-1-1v-1l-1-1 2-2c-1 0-2-1-3-2h0z" class="B"></path><path d="M339 135c1 2 1 2 2 2 1 1 2 1 3 1h1c0 2 0 5 1 6h1-2c-2 1-3 1-4 0 0 1-1 1-1 1v-1h-2 0v-1h-2l1-1 2-2h0c-1 0-2 1-4 1v-1c1 0 1-1 2-2h0l1-1c0-1 0-1 1-2z" class="g"></path><path d="M345 138c0 2 0 5 1 6h1-2c-2 1-3 1-4 0 0 1-1 1-1 1v-1h-2 0v-1h-2l1-1c0 1 1 1 1 1 1 0 2-1 3-2l-1-1 1-1c0 1 1 1 1 1l1 1h1l1-3z" class="D"></path><path d="M338 144h0 2v1l-1 3h-1v1h-1-6-8-2l-1-1c1-2 2-2 3-2l1-1h1c1-1 2 0 3 0l3-1c2 1 5 1 7 0z" class="c"></path><path d="M338 144h2v1l-1 3h-1v-4z" class="M"></path><path d="M324 145v2c1 1 2 0 3 1 0 0 1 0 1 1h0 0 1c2 0 3-1 4 0h4-6-8-2l-1-1c1-2 2-2 3-2l1-1z" class="d"></path><path d="M338 148h1l2 2h1l-1 1c0 1 1 1 1 2v1l1 1v1h-2v-1c-1-1-2-1-3-1l-1 2h-1v-1-1c-1-1-2-1-3-1s-1 1-1 2h0c-1 1-1 2-2 2-2 0-2-1-3-2h-1 0v-1l-1 1-1-1c1 0 1-1 2-2v-2l-3-1h8 6 1v-1z" class="a"></path><path d="M338 148h1l2 2h1l-1 1c0 1 1 1 1 2v1-1c-1 0-2-1-3-2l-1 1-2-1h1l1-1c-1-1-5 0-7-1h6 1v-1z" class="D"></path><path d="M327 155h0 1c1-1 1-2 1-3h0 2c1 1 1 2 1 3-1 1-1 2-2 2-2 0-2-1-3-2z" class="P"></path><path d="M335 151h1l2 1 1-1c1 1 2 2 3 2v1l1 1v1h-2v-1c-1-1-2-1-3-1l-1 2h-1v-1-1c-1-1-2-1-3-1v-1-1h1 1z" class="N"></path><path d="M333 153v-1-1h1 1c1 2 1 2 3 3l-1 2h-1v-1-1c-1-1-2-1-3-1z" class="J"></path><path d="M330 121h7 4 1 2c1 1 1 1 1 3-1-1-3-1-4-1l-1 2c2 1 4-1 6 0h6c0 1 0 2 1 4h0l-1 1c1 2 2 3 3 5s2 5 2 7h-2v1 1c-2 1-3 1-4 1v-1h-3-1-1c-1-1-1-4-1-6h-1c-1 0-2 0-3-1-1 0-1 0-2-2h0v-1h-2l-3 2c-2 1-3 2-4 4h-1 0c0-1 0-1-1-2 0 0-1 0-2 1h-2l-1-2h0c-2 0-2 1-3 2 0-1-1-2-1-3v-2h-1c0-2-2-3-3-4l-1-1v-1c-2-1-5-1-7-1v-2h-5l-1-1h6 0 5 9v-1h0 1l1-1c3 0 5 1 7-1z" class="M"></path><path d="M331 133h0c1 1 2 1 2 2l-1 1-1 1v-4z" class="J"></path><path d="M332 129c1 0 2 0 3-1v1c0 1 0 2 1 2v1c-1 0-1 0-2-1-1 0-1-1-1-2h-1z" class="a"></path><path d="M326 137h0c1-1 1-1 1-2h2 0l-1 1c0 1 1 1 0 2 0 0-1 0-2 1h-2l-1-2h1 2z" class="T"></path><path d="M327 132c1-1 1-2 3-3h1v2c-1 0-1 1-2 1-1 1-3 2-3 3h-2v-1c1-1 2-1 3-2z" class="G"></path><path d="M348 136c1-1 2-1 2-3l1 1c1 0 0 1 0 2l-1 1c1 1 1 3 2 3 0-1-1-2 0-3 2 2 1 4 3 6l-1 1h-1l-3-3c0-1 0-2-1-2 0-1 0-2-1-3z" class="h"></path><path d="M337 127h4v1 1l-1 1v-1c-1 0-1 0-2-1h-3c-1 1-2 1-3 1h-3v-1c-1 0-1 0-2 1h-1-2-3l1-1c1 0 1-1 2 0 2 0 2 0 3-1h2c1 1 1 0 2 0h6z" class="D"></path><path d="M339 134l-1-1 1-1s0-1 1-1v-1c2 1 1 2 3 2v-1l-1-1h2c1 1 2 3 3 4l1 1h-2c0-1-1-1-1-2-1-1-1-1-2-1v1l1 5c-1 0-2 0-3-1-1 0-1 0-2-2h0v-1z" class="Q"></path><path d="M341 137h1v-1h-2v-1c1-1 2-2 3-2l1 5c-1 0-2 0-3-1z" class="a"></path><path d="M321 129h3 2 1v1h-1v1h1v1c-1 1-2 1-3 2v1h2v1 1h-2-1 0c-2 0-2 1-3 2 0-1-1-2-1-3v-2-1c1 0 2-1 3-1-1-1-1-2-1-3h0z" class="W"></path><path d="M322 132h0c-1 1-1 2-1 3l2 2c-2 0-2 1-3 2 0-1-1-2-1-3v-2-1c1 0 2-1 3-1z" class="O"></path><path d="M321 129h3 2 1v1h-1v1c-1 0-1 1-2 1h-2 0c-1-1-1-2-1-3h0z" class="G"></path><path d="M343 133v-1c1 0 1 0 2 1 0 1 1 1 1 2h2v1c1 1 1 2 1 3 1 0 1 1 1 2l3 3h-2-3-1-1c-1-1-1-4-1-6h-1l-1-5z" class="B"></path><path d="M347 140l2 1v2l-1 1-1-1v-1l-1-1 1-1z" class="C"></path><path d="M348 135v1c1 1 1 2 1 3l-1 1h-1c0-2 1-2-1-3v-2h2z" class="O"></path><path d="M349 139c1 0 1 1 1 2l3 3h-2-3l1-1v-2l-2-1h0 1l1-1z" class="F"></path><path d="M330 121h7 4 1 2c1 1 1 1 1 3-1-1-3-1-4-1l-1 2c2 1 4-1 6 0h6c0 1 0 2 1 4h0l-1 1c1 2 2 3 3 5s2 5 2 7h-2c0-1 0-1-1-2v-1c0-2 0-3-1-5-1 0-1 0-1-1s-1-2-1-2c-1-2-5-3-6-4-2-2-4-1-7-1h3v1h-4-6c-1 0-1 1-2 0h-2c-1 1-1 1-3 1-1-1-1 0-2 0l-1 1h0c0 1 0 2 1 3-1 0-2 1-3 1v1h-1c0-2-2-3-3-4l-1-1v-1c-2-1-5-1-7-1v-2h-5l-1-1h6 0 5 9v-1h0 1l1-1c3 0 5 1 7-1z" class="V"></path><path d="M328 126c1 0 1-1 2-1 2 0 4 0 6 1h-5v1c-1 0-1 1-2 0h-2v-1h1z" class="Q"></path><path d="M336 126h2 3v1h-4-6v-1h5z" class="O"></path><path d="M346 125h6c0 1 0 2 1 4h0l-1 1c-1-2-5-3-6-5z" class="K"></path><path d="M318 125c3 0 7 0 10 1h-1v1c-1 1-1 1-3 1-1-1-1 0-2 0l-1 1h0-1c0-1-1-2-2-2v-2z" class="R"></path><path d="M324 128l1-2h2v1c-1 1-1 1-3 1z" class="G"></path><path d="M307 125h11v2l-1 1c-1 0-1 0-2-1-1 1-1 1-1 2v-1c-2-1-5-1-7-1v-2z" class="S"></path><path d="M318 127c1 0 2 1 2 2h1c0 1 0 2 1 3-1 0-2 1-3 1v1h-1c0-2-2-3-3-4l-1-1c0-1 0-1 1-2 1 1 1 1 2 1l1-1z" class="M"></path><path d="M315 130c1-1 2-1 3-1l1 1-2 1 1 1c0-1 1-1 2-2v1c-1 1-2 1-2 3 0-2-2-3-3-4z" class="G"></path><path d="M330 121h7 4 1 2c1 1 1 1 1 3-1-1-3-1-4-1l-20 1v-1h0 1l1-1c3 0 5 1 7-1z" class="B"></path><path d="M371 123c1 1 3 1 4 1h0 3v2h-1l1 2h1c0 1 1 2 1 2l1 1c1 1 1 2 1 2 1 3 2 5 1 8h0l2 3c-1 1-1 2-1 3 1 0 1 1 2 2l-1 1c-1 3-1 7-2 10s-4 6-5 8h0c-3 0-7 1-10 0h-7-1c-2 1-4 0-5 0-5 1-10 1-15 0 3 0 4 0 6-2 5-4 8-10 8-16l-1-1c0-1-1-2-2-3v-1c1 0 2 0 4-1v-1-1h2c0-2-1-5-2-7s-2-3-3-5l1-1h0c-1-2-1-3-1-4 2 0 4 1 6 0 1 0 1 0 2 1 2-1 3-1 6 0h3v-2l2-1z" class="a"></path><path d="M377 145h0c2-1 3 0 5 0h1c0 1-1 1-1 2s0 1-1 2h0v-3h-1c-1 1-1 3-1 4-1 0-1-1-2-2v-3z" class="d"></path><path d="M371 145h0 3v1 4c-1-1-3-1-4-1h-1c-1-1 0-2-1-3v-1h3z" class="c"></path><path d="M377 127l1 1h1c0 1 1 2 1 2l1 1c1 1 1 2 1 2 1 3 2 5 1 8h0v3l-1 1h0c-2 0-3-1-5 0h0v2c-1-1-1-3-1-4-1-2-2-5-2-8v-1c-1-1-1-1-1-2h1c1-1 2-1 2-2 1-1 1-2 1-3z" class="i"></path><path d="M373 132h1c1-1 2-1 2-2l1 1h-1c0 2 0 3 1 5v4 1-2h-1v2h0c1 1 0 2 0 2-1-2-2-5-2-8v-1c-1-1-1-1-1-2z" class="P"></path><path d="M377 127l1 1h1c0 1 1 2 1 2l1 1c1 1 1 2 1 2 1 3 2 5 1 8h0v3-1c-2-2-3-4-3-6-1 0 0-1-1-2 0-1-1-2-2-4l-1-1c1-1 1-2 1-3z" class="F"></path><g class="B"><path d="M380 137h2v2c1 1 1 3 1 4-2-2-3-4-3-6z"></path><path d="M377 127l1 1h1l-1 2c2 1 2 3 3 4v1h-1v2c-1 0 0-1-1-2 0-1-1-2-2-4l-1-1c1-1 1-2 1-3z"></path></g><path d="M382 157l-1 2c-1 3-2 7-4 9-2 1-5 0-7 0 1-3 3-6 4-9s2-7 2-10l1-1c1 1 1 2 2 2h1 3 0v1c-1 2 0 4-1 6z" class="U"></path><path d="M380 150h3 0v1c-1 2 0 4-1 6l-1-1c1-1 1-2 1-3-1 1-1 2-1 3h-1c0-1 0-1 1-1v-2c0-1 0-2-1-3z" class="H"></path><path d="M370 149c1 0 3 0 4 1h1c0 2-1 3-1 5-1 4-3 9-6 13h-7c1-3 2-5 4-8l3-11h1 1z" class="U"></path><path d="M365 160l3-11h1 1l-1 2h0c1 1 0 4 0 5l-1 1c0 1-1 2-2 2l-1 1z" class="E"></path><path d="M371 123c1 1 3 1 4 1h0 3v2h-1l1 2-1-1c0 1 0 2-1 3 0 1-1 1-2 2h-1c0 1 0 1 1 2v1l-1 1c1 2 3 6 2 9h-4 0-2-1c0-2-1-4-2-6h0c-1 0-1-1-1-1 0-2-1-3-1-4v-1c-1-1-1-2-2-3v-1c1 0 0-1 0-2h-1c-1 0-1-1-1-1 2-1 3-1 6 0h3v-2l2-1z" class="i"></path><path d="M366 126h3l1 2 1-1c1 1 1 3 2 4v1c0 1 0 1 1 2v1l-1 1-1-2v1l1 5c-1-1-1-2-1-3-1-1-1-3-2-4v-1s-1-1-1-2-1-2 0-3v-1h-3z" class="B"></path><path d="M366 126h3l1 2c1 1 2 3 2 4l-2-2h-1c0-1-1-2 0-3v-1h-3z" class="K"></path><path d="M364 134v-1c-1-1-1-2-2-3v-1h3s1 2 2 2h0c0 1 1 3 1 4h-1c1 1 1 1 1 2s1 2 1 4v3 1h-1c0-2-1-4-2-6h0c-1 0-1-1-1-1 0-2-1-3-1-4z" class="B"></path><path d="M371 123c1 1 3 1 4 1h0 3v2h-1l1 2-1-1c0 1 0 2-1 3 0 1-1 1-2 2h-1v-1c-1-1-1-3-2-4l-1 1-1-2v-2l2-1z" class="H"></path><path d="M371 123c1 1 3 1 4 1h0 3v2h-1l1 2-1-1-1-1c-2-1-5-1-6 0l1 1-1 1-1-2v-2l2-1z" class="d"></path><path d="M352 125c2 0 4 1 6 0 1 0 1 0 2 1 0 0 0 1 1 1h1c0 1 1 2 0 2v1c1 1 1 2 2 3v1c0 1 1 2 1 4 0 0 0 1 1 1h0c1 2 2 4 2 6h1 2-3v1c1 1 0 2 1 3h-1l-3 11c-2 3-3 5-4 8h-1c-2 1-4 0-5 0-5 1-10 1-15 0 3 0 4 0 6-2 5-4 8-10 8-16l-1-1c0-1-1-2-2-3v-1c1 0 2 0 4-1v-1-1h2c0-2-1-5-2-7s-2-3-3-5l1-1h0c-1-2-1-3-1-4z" class="U"></path><path d="M366 139c1 2 2 4 2 6h1 2-3v1c1 1 0 2 1 3h-1l-3 11c-2 3-3 5-4 8h-1c2-4 7-14 7-19h-3v-4c1 0 2 0 3-1v-2c-1 0-1-2-1-3z" class="V"></path><path d="M355 142h2l1 2v1 1h1c0 1-1 2-1 2-1 8-4 15-11 19l-1 1h9c-5 1-10 1-15 0 3 0 4 0 6-2 5-4 8-10 8-16l-1-1c0-1-1-2-2-3v-1c1 0 2 0 4-1v-1-1z" class="X"></path><path d="M355 143c1 0 1 0 2 1v6c0 1-1 1-1 1-1 0-1 0-2-1l-1-1c0-1-1-2-2-3v-1c1 0 2 0 4-1v-1z" class="I"></path><path d="M352 125c2 0 4 1 6 0 1 0 1 0 2 1 0 0 0 1 1 1h1c0 1 1 2 0 2v1c1 1 1 2 2 3v1c0 1 1 2 1 4 0 0 0 1 1 1h0c0 1 0 3 1 3v2c-1 1-2 1-3 1v4h-2c0-1 0 0-1 0s-2 0-3-1c0 0 1-1 1-2h-1v-1-1l-1-2c0-2-1-5-2-7s-2-3-3-5l1-1h0c-1-2-1-3-1-4z" class="i"></path><path d="M359 134h1v1l2 2h-1v1l-2-2c0-1-1-1 0-2z" class="E"></path><path d="M357 135c0 1 1 2 1 3s0 1 1 2c0 2 0 2-1 4l-1-2c0-2-1-5-2-7h1c0 1 0 1 1 0z" class="B"></path><path d="M362 135l2-1c0 1 1 2 1 4 0 0 0 1 1 1h0c0 1 0 3 1 3h-2l-3-7z" class="E"></path><path d="M353 129l2 2h1v-1h1v1c-1 2-1 3 0 4-1 1-1 1-1 0h-1c-1-2-2-3-3-5l1-1z" class="S"></path><path d="M358 125c1 0 1 0 2 1 0 0 0 1 1 1h1c0 1 1 2 0 2v1c1 1 1 2 2 3v1l-2 1v-2c-1-1-2-1-3-2v-3-1l-1-2z" class="K"></path><path d="M358 145c0-1 1 0 2-1 0 0 0-1 1-1 1 1 1 1 1 2h2v4h-2c0-1 0 0-1 0s-2 0-3-1c0 0 1-1 1-2h-1v-1z" class="C"></path><path d="M358 146c2-1 2-1 3-1 1 2 1 3 0 4-1 0-2 0-3-1 0 0 1-1 1-2h-1z" class="D"></path><path d="M253 86c1 1 2 1 3 2 4 0 7 0 11 1-2 0-4 0-7 1l7 1h7c7 1 14 0 21 1h-1l-3 3c-3 2-4 5-6 8 1 1 1 2 2 2l2-3c1-2 3-5 6-6v1l-1 1h0c3-3 4-3 8-4 3 0 5-1 8 0l-1 1h1c2 1 5 3 6 5h0 1l2 4v2l1 4v-1h1l-1 2c1 2-1 5-2 7v1h1c0-2 1-2 1-3h1l-1 2 3 3c2 1 6 0 7 0-2 2-4 1-7 1l-1 1h-1 0v1h-9-5 0-6l1 1h5v2c2 0 5 0 7 1v1l1 1c1 1 3 2 3 4h1v2c0 1 1 2 1 3h0c1 1 2 2 3 2l-2 2 1 1v1l1 1c-1 0-2 0-3 2l1 1h-1l-1 2c0 2-1 4-2 6 0 2-1 3-1 4h-1c-1-1-1-3-1-4h-1v-1l1-1c0-3 0-8-2-10 1 2 1 5 1 7-1 4-4 9-6 12-2 2-5 4-7 5h-4c-3 1-8 1-11 2v1h1c-2 1-6 0-8 0h-5c-1 0-1 0-2 1v1c1 0 1 1 1 1h-1l-6-29-3-13-2-12-7-35z" class="H"></path><path d="M276 151c1 1 1 0 3 0l1 1-1 2-2 2v-1c0-1 0-2-1-2v-2z" class="P"></path><path d="M296 165c1-1 2-2 3-4l1 1h1l-4 4-1-1z" class="X"></path><path d="M276 151c0-2-1-5-1-8 1 0 2-1 2-2 0 2-1 3-1 5h0c1 1 1 1 1 2 1 1 1 1 1 2l1 1c-2 0-2 1-3 0z" class="O"></path><path d="M272 129c3-2 5-4 7-6v2h0l3-1c-2 3-5 4-8 6l-2-1z" class="N"></path><path d="M265 131c3 0 5-2 7-2l2 1c-3 2-5 3-8 3 0 0-1 0-1-1v-1z" class="V"></path><path d="M267 115h1c0 2 0 2 2 4h-1v1h2 2v1h0c-1 0-1 0-2 1l1 1-1 1c-1 1-2 1-3 1l2-1c-1-1-2-1-2-1v-1h0c1-1-1-2-2-3 1 0 1 1 2 1l1-1-1-1v-1c0-1-1-1-1-2z" class="B"></path><path d="M294 147c1 1 1 2 1 4 0 1-1 1-2 2h0l3 2c0 2 0 4-1 6v-1c-1-3-2-3-3-5 0-1 0-1 1-1l-2-2 2-1h0c1-2 1-3 1-4z" class="J"></path><path d="M256 88c4 0 7 0 11 1-2 0-4 0-7 1l7 1c-3 1-7 0-11 0v-3z" class="F"></path><path d="M273 172v-1h1c0-1-1-1-2-1v-1c2-1 3-1 5-1h10c-2 1-5 1-6 2-1 0-3 0-3 2h0-5z" class="Q"></path><path d="M274 91c7 1 14 0 21 1h-1l-3 3c-3 2-4 5-6 8v1c-1-1-1-1-1-2 1-4 5-8 8-10h-11-7v-1z" class="R"></path><path d="M286 115c1-1 1-3 2-4 0 2 0 3 1 5h1v-3 1s1 1 1 2c1 1 2 1 3 2l-1 1h-1v-2h-2v2c0 2 1 3 1 5-2-2-3-5-4-7h-1v-2zm10 10v-2c1-1 2-1 3-2v1h0v2h2l1 1h5v2c-1 0-3 0-4 1-2-1-3-2-4-2-1 1-1 1-2 1h-2 0c-1 0-2-1-2-2h0 2 1z" class="E"></path><path d="M296 125v-2c1-1 2-1 3-2v1h0v2h2l1 1h-5-1z" class="B"></path><path d="M280 149c0 1 1 2 2 2 1 1 2 1 3 1l1 1c1 1 1 1 1 2h-1c0 2-1 3-1 5-2-1-1-2-1-4-2 0-3 1-5 0h0l-1 1-1-1h0l2-2 1-2v-3z" class="Y"></path><path d="M279 154v1h1c0-1 1-2 2-2l1 1 1 2c-2 0-3 1-5 0h0l-1 1-1-1h0l2-2z" class="O"></path><path d="M285 160v2h-1c-1-1 0-2-1-3 0 0-1 0-1-1h0c-1 1-2 3-2 5-1-1 0-4-1-6h-1l-1 5c-3-4-5-7-8-12 0 0-1-1-1-2v-1l1 1 1 1c0 1 1 2 1 3 2 1 3 6 5 6l1-2 1 1 1-1h0c2 1 3 0 5 0 0 2-1 3 1 4z" class="K"></path><path d="M287 168c3 0 6 0 9-3l1 1-3 3h2c-3 1-8 1-11 2v1h1c-2 1-6 0-8 0h0c0-2 2-2 3-2 1-1 4-1 6-2z" class="a"></path><path d="M278 141c0-1 1-1 2-2 0-1 1-1 1-2 1 1 1 1 1 2l1 1h-1c1 1 1 3 1 4h0l-1 2c0 1-1 1-1 1h-1c-1 1-1 1-1 2h1v3l-1-1-1-1c0-1 0-1-1-2 0-1 0-1-1-2h0c0-2 1-3 1-5h1z" class="Q"></path><path d="M278 141c0-1 1-1 2-2 0-1 1-1 1-2 1 1 1 1 1 2l1 1h-1c0 1-1 1-2 1h0c1 2 1 2 1 4l-2-2-1-2h0z" class="B"></path><path d="M280 149h-1c0-1 0-1 1-2h1c0 1 0 2 1 2h1 3 4 0c0 1 1 1 2 2h1l-2 1 2 2c-1 0-1 0-1 1-1-1-2-1-3-1 0 1-1 3-1 3-1 0-1-1-1-2s0-1-1-2l-1-1c-1 0-2 0-3-1-1 0-2-1-2-2z" class="g"></path><path d="M292 151h1l-2 1 2 2c-1 0-1 0-1 1-1-1-2-1-3-1 0 1-1 3-1 3-1 0-1-1-1-2s0-1-1-2l-1-1c-1 0-2 0-3-1 2 0 4 1 7 0h3z" class="D"></path><path d="M267 99c1 2 0 3 2 4h1l1 1 1 1-2 1h2l-1 1c-1 1-1 1-1 3 0 0 0 1 1 1l-1 1v1h1c0 1-1 1-1 2h1v1 1l1 1-1 2h0-2v-1h1c-2-2-2-2-2-4h-1l-1-2v-2h1l-1-1c0-1 0-4 1-5v-2-2-2z" class="F"></path><path d="M267 99c-1 1-2 2-2 3h1 0c-1 1-2 1-3 1h1l-1-1s1 0 1-1c0 0 0-1 1-1h0v-1h-6c-1-1-1-4-2-6h1c2 1 5 1 7 0h3 1c1 1 1 1 2 1 2 1 4 1 6 2-1 1-2 1-3 2l-2-1c-2 1-7-1-9 1 1 1 2 0 3 0h0l1 1z" class="E"></path><path d="M272 97c-2-1-5-2-8-2 2-1 5-1 7-1 2 1 4 1 6 2-1 1-2 1-3 2l-2-1z" class="f"></path><path d="M277 119v3h1s1-1 1-2l1 1-1 2c-2 2-4 4-7 6-2 0-4 2-7 2 1 0 1-1 1-1l-1-1c-1-1-1-2 0-3s2-1 3-1 2 0 3-1l1-1-1-1c1-1 1-1 2-1h0v-1h1s1 0 1 1v-1c1 0 1-1 2-1z" class="F"></path><defs><linearGradient id="AG" x1="273.567" y1="124.066" x2="276.069" y2="121.409" xlink:href="#B"><stop offset="0" stop-color="#6c6a6c"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#AG)" d="M277 119v3h1l-8 6c-1 0-1 0-2 1l-1-1h1c4-2 4-4 7-7v-1c1 0 1-1 2-1z"></path><path d="M281 137c3-1 5-1 8 0 3 2 8 5 9 9v1c0 2 0 4-1 6h-1l-1 1 1 1-3-2h0c1-1 2-1 2-2 0-2 0-3-1-4l-1-1c-1-2-3-3-4-5-1-1-2-1-3-2-1 0-2 0-3 1l-1-1c0-1 0-1-1-2z" class="Q"></path><path d="M283 140c1-1 2-1 3-1 1 1 2 1 3 2 1 2 3 3 4 5l1 1c0 1 0 2-1 4h0-1c-1-1-2-1-2-2h0-4-3-1c-1 0-1-1-1-2 0 0 1 0 1-1l1-2h0c0-1 0-3-1-4h1z" class="e"></path><path d="M283 144h0v-3h3c0 2 0 3-1 4h-1v4h2-3-1c-1 0-1-1-1-2 0 0 1 0 1-1l1-2h0z" class="V"></path><path d="M283 144v3 2h-1c-1 0-1-1-1-2 0 0 1 0 1-1l1-2z" class="N"></path><path d="M285 145h2c1 0 1-1 1-1 1 0 2 1 3 1s2 1 2 1l1 1c0 1 0 2-1 4h0-1c-1-1-2-1-2-2h0-4-2v-4h1z" class="X"></path><path d="M280 98c1 2 2 3 3 4h1c0 1 0 1 1 2v-1c1 1 1 2 2 2v3 1s1 1 1 2c-1 1-1 3-2 4v2c-1 3-2 5-4 7l-3 1h0v-2l1-2h0c1-2 2-4 3-7 0-4-2-9-5-12 1-2 1-3 2-4z" class="b"></path><path d="M284 102c0 1 0 1 1 2v-1c1 1 1 2 2 2v3 1s1 1 1 2c-1 1-1 3-2 4v-5c-1-2-1-4-2-5l-1-3h1z" class="Q"></path><defs><linearGradient id="AH" x1="288.901" y1="148.947" x2="301.493" y2="139.123" xlink:href="#B"><stop offset="0" stop-color="#cdcdce"></stop><stop offset="1" stop-color="#fdfcfd"></stop></linearGradient></defs><path fill="url(#AH)" d="M299 161c3-5 5-10 3-16-1-4-5-8-9-10-4-3-8-2-13-3 6-2 11-2 16 1 4 2 8 7 9 12 2 4 1 10-2 15 0 1-1 2-2 2h-1l-1-1z"></path><path d="M287 105l2-3c1-2 3-5 6-6v1l-1 1h0l-1 3v2h1l1 1-1 3h0l2-1 1 1c1-1 1-2 2-2l-1 2c0 2-2 5-3 7v1c1 1 2 2 2 3l3 1h-3c-1 0-2-1-3-1-1-1-2-1-3-2 0-1-1-2-1-2v-1 3h-1c-1-2-1-3-1-5 0-1-1-2-1-2v-1-3z" class="R"></path><path d="M296 106l1 1c0 2-1 3-2 5-1 0-1 0-1-1-1-1-1-1-1-2 1-1 2-1 3-3z" class="a"></path><path d="M294 111h-1c0 1 0 2-1 2h-1v-6c0-1-1 0-1-2h1c1-1 1-1 2-1 0 2-1 4 0 5 0 1 0 1 1 2z" class="L"></path><path d="M277 96c1 1 3 2 3 2-1 1-1 2-2 4 3 3 5 8 5 12-1 3-2 5-3 7h0l-1-1c0 1-1 2-1 2h-1v-3c-1 0-1 1-2 1v1c0-1-1-1-1-1h-1-2 0l1-2-1-1v-1-1h-1c0-1 1-1 1-2h-1v-1l1-1c-1 0-1-1-1-1 0-2 0-2 1-3l1-1h-2l2-1-1-1-1-1h-1c-2-1-1-2-2-4h0l-1-1h0c-1 0-2 1-3 0 2-2 7 0 9-1l2 1c1-1 2-1 3-2z" class="G"></path><path d="M276 101c1 1 1 2 2 3v1h-1l1 2-2-1c-1-1-2-1-2-2l1-1s0-1 1-2z" class="e"></path><path d="M279 115c1-1 1-3 1-4h1c0 3 0 5-1 8l-1-1v-2-1z" class="g"></path><path d="M278 116h1v2l1 1-1 1c0 1-1 2-1 2h-1v-3l-1-1 2-2z" class="a"></path><path d="M277 96c1 1 3 2 3 2-1 1-1 2-2 4-1-2-3-3-4-4 1-1 2-1 3-2z" class="h"></path><path d="M278 107l-1-2h1c2 2 3 3 3 6h-1c0 1 0 3-1 4v-4c0-1 0-1 1-2l-1-1-1 1v-1-1h0z" class="D"></path><path d="M275 109c1 0 2-1 3-1v1l1-1 1 1c-1 1-1 1-1 2v4 1h-1v-3c0 1-1 2-2 3v-2l-1-1c0-1 1-1 1-2 0 0-1-1-1-2z" class="e"></path><path d="M275 109l1 1 1-1c0 1 0 1 1 2v2c0 1-1 2-2 3v-2l-1-1c0-1 1-1 1-2 0 0-1-1-1-2z" class="W"></path><path d="M278 111v2c0 1-1 2-2 3v-2l2-3z" class="Y"></path><path d="M276 116c1-1 2-2 2-3v3l-2 2 1 1c-1 0-1 1-2 1v1c0-1-1-1-1-1h-1-2 0l1-2-1-1v-1h1l1-1h1 0c0 1 0 1 1 2l1-1z" class="T"></path><path d="M272 118h3v2 1c0-1-1-1-1-1h-1-2 0l1-2z" class="Q"></path><path d="M276 116c1-1 2-2 2-3v3l-2 2 1 1c-1 0-1 1-2 1v-2h-1l1-1 1-1z" class="M"></path><path d="M268 99l1-1c3 0 5 1 7 3h0c-1 1-1 2-1 2v-1h-1-1-1c-1 1-1 1-2 1h-1c-2-1-1-2-2-4h0l-1-1c1 1 2 1 2 1z" class="L"></path><path d="M266 98c1 1 2 1 2 1h3v2l1 1c-1 1-1 1-2 1h-1c-2-1-1-2-2-4h0l-1-1z" class="S"></path><path d="M272 102h1 1 1v1l-1 1c0 1 1 1 2 2l2 1h0v1c-1 0-2 1-3 1 0 1 1 2 1 2 0 1-1 1-1 2l1 1v2l-1 1c-1-1-1-1-1-2h0-1l-1 1h-1v-1h-1c0-1 1-1 1-2h-1v-1l1-1c-1 0-1-1-1-1 0-2 0-2 1-3l1-1h-2l2-1-1-1-1-1c1 0 1 0 2-1z" class="G"></path><path d="M276 106l2 1h0v1c-1 0-2 1-3 1 0 1 1 2 1 2 0 1-1 1-1 2-1-1-2-1-2-2-1 0-1 0-2-1l2-1c0-1 1-1 1-1 1-1 2-1 2-2z" class="M"></path><path d="M273 111c-1 0-1 0-2-1l2-1v1h1 1c-1 1-1 1-2 1z" class="R"></path><path d="M295 127h2c1 0 1 0 2-1 1 0 2 1 4 2 1-1 3-1 4-1 2 0 5 0 7 1v1l1 1c1 1 3 2 3 4h1v2c0 1 1 2 1 3h0c1 1 2 2 3 2l-2 2 1 1v1l1 1c-1 0-2 0-3 2l1 1h-1l-1 2c0 2-1 4-2 6 0 2-1 3-1 4h-1c-1-1-1-3-1-4h-1v-1l1-1c0-3 0-8-2-10l-1-5c-4-7-9-9-16-13z" class="M"></path><path d="M316 161c-2-3-1-9-1-12 1-3 1-5 0-8 2 1 2 2 4 3v2h-2c-1 1-2 6-1 8v5l1-2h0c0 2-1 3-1 4z" class="L"></path><path d="M317 157h0l-1 2v-5c-1-2 0-7 1-8h2v5c0 2-1 4-2 6z" class="e"></path><path d="M307 127c2 0 5 0 7 1v1l1 1c1 1 3 2 3 4h1v2c0 1 1 2 1 3h0c1 1 2 2 3 2l-2 2v1 2h-1c0-4-4-8-6-11h0c-2-4-7-6-11-7 1-1 3-1 4-1z" class="H"></path><defs><linearGradient id="AI" x1="296.916" y1="129.939" x2="315.302" y2="137.915" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#AI)" d="M295 127h2c1 0 1 0 2-1 1 0 2 1 4 2 4 1 9 3 11 7h0v2 5c1 4 1 10 0 15h-1v-1l1-1c0-3 0-8-2-10l-1-5c-4-7-9-9-16-13z"></path><path d="M302 94c3 0 5-1 8 0l-1 1h1c2 1 5 3 6 5h0 1l2 4v2l1 4v-1h1l-1 2c1 2-1 5-2 7v1h1c0-2 1-2 1-3h1l-1 2 3 3c2 1 6 0 7 0-2 2-4 1-7 1l-1 1h-1 0v1h-9-5 0-6-2v-2h0v-1h0l1-1c-1 0-2 0-3-1h0 3l-3-1c0-1-1-2-2-3v-1c1-2 3-5 3-7l1-2c-1 0-1 1-2 2l-1-1-2 1h0l1-3-1-1h-1v-2l1-3c3-3 4-3 8-4z" class="J"></path><path d="M299 117h1s1 0 1-1c0 2-1 2-1 3 1 0 1 0 2-1v2c-1 0-1 0-2-1l-3-1 2-1z" class="Y"></path><path d="M295 114c2 0 2-1 3-1h0c-1 1-1 1-1 2l2 2-2 1c0-1-1-2-2-3v-1z" class="M"></path><path d="M302 108c0-1 1-1 1-2h1c1 2 1 3 1 4 1 0 1 0 1-1 0 1 0 1 1 2h1v2h-1c-2 0-2-2-3-3v3l-1-1s-1 0-1-1 0-2 1-3h0-1z" class="f"></path><path d="M312 110l1 4h0c0 1-1 2-1 3h0c-1 1-1 1-2 1h-1l2-1v-1h-5-1c1-1 3-1 5-3h0l2-3z" class="M"></path><path d="M302 120v1l1-1 1-1h1 0l-1 2h1l1-1h3v3c-1 1-1 1-2 1h0 0-6-2v-2h0v-1h0l1-1c-1 0-2 0-3-1h0 3c1 1 1 1 2 1z" class="E"></path><path d="M306 120h3v3c-1 1-1 1-2 1h0 0 0l1-1c-1-1-1 0-2-1v-2z" class="B"></path><path d="M319 104v2l1 4v-1h1l-1 2c1 2-1 5-2 7v1h1c0-2 1-2 1-3h1l-1 2 3 3c2 1 6 0 7 0-2 2-4 1-7 1l-1 1h-1 0v1h-9-5 0c1 0 1 0 2-1v-3s3-1 4-2 1-1 2-1v-2l2-2v-1c-1 0-2 1-3 2 0 1-1 2-2 3h0 0c0-1 1-2 1-3 2-1 2-3 2-4v-1h2 0c0-1 0-1-1-1 2-1 2-2 3-4z" class="S"></path><path d="M315 122c1 0 1-1 2-2 1 0 1 0 2-1h1c0 1 2 2 3 3h0l-1 1h-1v-2c-2 0-3 1-4 2h0l-2-1z" class="E"></path><path d="M315 122l2 1h0c1-1 2-2 4-2v2h0v1h-9 3l-1-1 1-1h0z" class="Q"></path><path d="M319 104v2l1 4v-1h1l-1 2c1 2-1 5-2 7h0c-2 0-4 2-5 3l-1 1c-1 0-1 0-2 1 0-2 4-4 5-5h0c2-2 2-4 3-6 0-2 1-2 0-4l-1 1c0-1 0-1-1-1 2-1 2-2 3-4z" class="d"></path><path d="M319 104v2c0 1 0 4-1 6 0-2 1-2 0-4l-1 1c0-1 0-1-1-1 2-1 2-2 3-4z" class="Q"></path><path d="M302 94c3 0 5-1 8 0l-1 1h1c2 1 5 3 6 5h0 1l2 4c-1 2-1 3-3 4 1 0 1 0 1 1h0-2v1c0 1 0 3-2 4h0l-1-4-2 3h-1l-1 1c-1 0-1-1-1-1h1v-2h-1c-1-1-1-1-1-2 0 1 0 1-1 1 0-1 0-2-1-4h-1c0 1-1 1-1 2l-1 2c0 1 0 0-1 1v-3c-1 0-1 0-2-1l1-2c-1 0-1 1-2 2l-1-1-2 1h0l1-3-1-1h-1v-2l1-3c3-3 4-3 8-4z" class="g"></path><path d="M298 98c2 2 2 2 2 4-1 1-1 1-1 2l-2-1c0-2 0-3 1-5z" class="J"></path><path d="M294 98c3-3 4-3 8-4 0 1 0 2-1 2-1 1-2 1-3 2-1 2-1 3-1 5l-1 1v-1c-1 0-1 0-1 1l-1-1h-1v-2l1-3z" class="B"></path><path d="M294 98c3-3 4-3 8-4 0 1 0 2-1 2h-1c-1 0-2 1-3 1-1 2-2 3-2 5-1 0-1 0-1 1h-1v-2l1-3z" class="J"></path><path d="M303 103c-1-1-1-1-2-1v-3c1-2 1-3 2-4h2v1c0 1 0 3 1 4v-1c1-1 1-3 1-4h3c2 1 5 3 6 5h0c-1 1-1 1-2 1v-1c-1-1-2-2-2-3l-2-1c0 1-1 1-1 1v1h0v4h1c-1 1-1 1-1 2h-2c0-2 2-4 1-7-1 1-1 1-1 2 0 0-1 1-1 2h0v2h-1c0-1-1-2-1-3l-1 1v2z" class="S"></path><path d="M303 103c-1-3-1-4 0-7 1 1 1 0 1 1v3l-1 1v2z" class="f"></path><path d="M310 102h-1v-4h0v-1s1 0 1-1l2 1c0 1 1 2 2 3v1c1 0 1 0 2-1h1l2 4c-1 2-1 3-3 4 1 0 1 0 1 1h0-2v1c0 1 0 3-2 4h0l-1-4c1-1 1-2 1-3-1-1-2-2-3-2v-1l1-1v-1h-1z" class="e"></path><path d="M316 100h1l2 4c-1 2-1 3-3 4 0-3-1-5-2-7 1 0 1 0 2-1z" class="G"></path><path d="M310 102h-1v-4h0v-1s1 0 1-1l2 1-1 1c1 3 4 6 4 9l-1 1-1-1c-1-1-2-2-3-2v-1l1-1v-1h-1z" class="O"></path><path d="M304 100c0 1 1 2 1 3h1v-2h0c0-1 1-2 1-2 0-1 0-1 1-2 1 3-1 5-1 7h2c0-1 0-1 1-2h1v1l-1 1v1c1 0 2 1 3 2 0 1 0 2-1 3l-2 3h-1l-1 1c-1 0-1-1-1-1h1v-2h-1c-1-1-1-1-1-2 0 1 0 1-1 1 0-1 0-2-1-4h-1c0 1-1 1-1 2l-1 2c0 1 0 0-1 1v-3c-1 0-1 0-2-1l1-2h1s0-1 1-1h0c1 0 1-1 1-1h1v-2l1-1z" class="N"></path><path d="M304 106v-2l2-1 1 2s0 1 1 2l-2 2c0 1 0 1-1 1 0-1 0-2-1-4z" class="V"></path><path d="M308 107c1 1 2 2 3 2l-2 4-1 1c-1 0-1-1-1-1h1v-2h-1c-1-1-1-1-1-2l2-2z" class="Y"></path></svg>