<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="72 77 608 596"><!--oldViewBox="0 0 732 752"--><style>.B{fill:#d3d2d3}.C{fill:#e1e0e1}.D{fill:#c8c7c8}.E{fill:#b5b3b4}.F{fill:#a2a0a1}.G{fill:#939292}.H{fill:#cecdcd}.I{fill:#ecebeb}.J{fill:#adadad}.K{fill:#1d1c1d}.L{fill:#dad9d9}.M{fill:#e7e5e6}.N{fill:#f3f1f2}.O{fill:#5b5b5b}.P{fill:#6a696a}.Q{fill:#bbbaba}.R{fill:#9b9a9a}.S{fill:#838182}.T{fill:#4e4e4e}.U{fill:#7d7c7c}.V{fill:#c0bec0}.W{fill:#2a2929}.X{fill:#2d2c2d}.Y{fill:#222223}.Z{fill:#c3c1c2}.a{fill:#403f3f}.b{fill:#262626}.c{fill:#323132}.d{fill:#a8a7a8}.e{fill:#8e8c8d}.f{fill:#3a3939}.g{fill:#474647}.h{fill:#545354}.i{fill:#767676}.j{fill:#373536}.k{fill:#f2f2f1}.l{fill:#626262}.m{fill:#717071}.n{fill:#474746}.o{fill:#888787}.p{fill:#131212}.q{fill:#070707}.r{fill:#181818}</style><path d="M584 169v1c1 0 1 0 1 1v1c-1 0-1 0-2-1v-1l1-1z" class="I"></path><path d="M626 334v-1l2 2-1 1h-1c-1-1-1-1 0-2z" class="N"></path><path d="M294 406l1-1c1 1 1 1 1 2l-1 1h-2c0-2 0-1 1-2z" class="Q"></path><path d="M664 422c1 0 1 1 2 1 0 1 0 1-1 2h-1 0c-1-2-1-2 0-3z" class="k"></path><path d="M75 234h2v2h-2c-1-1-1-1 0-2z" class="I"></path><path d="M436 103h2v1 1h-2v-2z" class="k"></path><path d="M669 441h1 1v3h-1c-1-1-1-1-1-2v-1z" class="T"></path><path d="M583 368h1 1v2l-1 1h-1c-1-1-1-1-1-2l1-1z" class="J"></path><path d="M521 399h2c0 2 0 2-1 3l-1-1-1-1c0-1 0-1 1-1z" class="I"></path><path d="M614 407h2c1 1 0 2 0 4h0c-1-1-2-3-2-4z" class="U"></path><path d="M457 475s0 1 1 1c0 1 0 1-1 3h0l-1-1c0-1 0-2 1-3z" class="D"></path><path d="M469 308h1 1c0 1 0 1-1 3h-1c0-1-1-1-1-2l1-1z" class="J"></path><path d="M189 94h1c1 1 1 1 1 2l-1 1h-1v-1c-1-1-1-1 0-2z" class="k"></path><path d="M380 92l1-1 1 1v2h-3c0-1 0-1 1-2z" class="V"></path><path d="M356 53h2v2 1h-1l-2-2 1-1z" class="k"></path><path d="M181 197c1 0 1 0 2 1v1l-1 1c-1 0-1-1-2-1 0-1 0-1 1-2z" class="N"></path><path d="M534 276h2 1v2l-1 1c-1 0-1 0-2-1v-2z" class="M"></path><path d="M579 381h1l1 1c0 1 0 1-1 2h-1l-1-1c0-1 0-1 1-2z" class="N"></path><path d="M463 489v-1c1 1 1 1 1 2s0 1-1 2c-1 0-1 0-2-1 0-1 1-2 2-2z" class="I"></path><path d="M533 507l1-1 1 1c1 1 1 1 0 2s-1 1-3 0c0-2 0-1 1-2z" class="k"></path><path d="M102 369h0c1 0 2 0 2 1s0 1-1 2h-1c-1-1-1-1-1-2l1-1z" class="F"></path><path d="M121 468h1c1 1 1 1 1 3l-1 1c-1 0-1-1-2-1 0-2 0-2 1-3z" class="C"></path><path d="M280 310h0c1-1 2 0 2 0 1 2 0 2 0 2-1 1-1 1-2 1-1-1-1-1-1-2l1-1z" class="N"></path><path d="M375 422l3-1c1 1 1 3 1 4-1 0-2 0-2-1-1 0-1 0-1-1h-1l1-1h-1z" class="I"></path><path d="M262 457c1-1 1-2 1-2h1l1-1h1c1 1 1 2 1 3h-3-2z" class="K"></path><path d="M283 409h1c1 0 1 0 1 1s-1 2-1 2c-1 0-2 0-2-1s0-1 1-2z" class="L"></path><path d="M607 341c1-1 1-1 2-1l1 1c0 1 0 1-1 3h-1c-1-1-2-1-2-2l1-1z" class="C"></path><path d="M395 210l1 1v6h-1-1c0-2 0-5 1-7z" class="n"></path><path d="M578 204h1l2 2-1 1h-1c-1 1-1 0-2 0 0-2 0-2 1-3z" class="k"></path><path d="M162 402l1-1 1 1c0 2 1 3 0 4-1 0-1 0-2-1v-3z" class="B"></path><path d="M136 369h1c1 0 1 0 1 1s0 1-1 2c-1 0-1 0-2-1v-1l1-1z" class="M"></path><path d="M430 213c1 0 2 0 3 1h0c-1 1-2 2-2 3h0v1h-1-1c1-2 1-4 1-5z" class="X"></path><path d="M646 478c1 0 1 1 2 1 0 2 0 2-1 3h-1c-1-1-2-1-2-2l2-2z" class="I"></path><path d="M548 391c1 0 1 0 2 1s1 1 3 2l-1 1h-2c-1 1-1 1-2 1 0-1 0-1 1-1 0-1 0-3-1-4z" class="V"></path><path d="M87 371h0c1 0 1 1 2 1 0 2 0 2-1 3h-1c-1 0-1-1-1-1 0-2 0-2 1-3z" class="F"></path><path d="M524 97h2v1c1 1 0 1 0 2h-2l-1-1c0-1 0-1 1-2z" class="k"></path><path d="M145 588c1-2 3-3 5-3 0 1 0 2-1 3h-2-2z" class="D"></path><path d="M641 258h0c1 0 1 0 2 1 0 1-1 1-1 2h-1c-1 0-1-1-2-1 1-2 1-2 2-2z" class="N"></path><path d="M597 257l1 1h1c1 1-1 2-1 3v1h-1l-2-2v-1c1 0 1-1 2-1v-1z" class="e"></path><path d="M209 272h1c1 1 1 1 1 2s0 1-1 2h-1c-1-1-1-1-1-2s0-1 1-2zm449 22h0c1 0 1 0 2 1 0 1 0 1-1 2h-1c-1 0-1 0-1-1s0-1 1-2z" class="k"></path><path d="M500 77c2 0 2 1 3 2l-1 1-1 1s-1 0-2-1v-1c0-1 0-1 1-2zm-38-17h1c1 1 1 1 1 2s0 1-1 2h-1c-1 0-1 0-2-2l2-2zM113 451l1 1c1 0 1 0 2 1l-2 2h-1c-1-1-1-1-1-2s0-1 1-2z" class="N"></path><path d="M306 350h1v3h-4-1v-1c1 0 3-1 4-2z" class="j"></path><path d="M646 314h2c1 0 1 0 1 1s0 2-1 2l-1 1-2-2c0-1 0-1 1-2z" class="M"></path><path d="M558 447h1c1 1 1 1 1 2s0 1-1 2c-2 0-2-1-3-2 1-2 1-2 2-2z" class="k"></path><path d="M210 89h1c1 0 1 1 2 1-1 2-1 2-2 3h-1c-1-1-1-1-1-2s0-1 1-2z" class="N"></path><path d="M524 534c2 0 4-1 6-2l2 1-1 1-2 1h-5 0v-1z" class="C"></path><path d="M479 396l-1-2h0l1-1c0-1 1-1 2-2 0 1 1 2 1 3v1c-1 1-2 1-3 1z" class="B"></path><path d="M142 459c1 1 1 1 1 2v1 2c0 2 0 3 1 5h0l1 1h-1-1c-2-3-1-8-1-11z" class="M"></path><path d="M251 381l2-1c1 1 1 1 1 3 0 1 0 1-2 2h0c-1-2-2-2-2-3l1-1z" class="N"></path><path d="M181 146c-3 0-7-1-10-1 3 0 6 0 9-1h1v2z" class="M"></path><path d="M111 236h2c1 0 1 1 2 2l-2 2h-1c-1-1-2-1-2-2s1-1 1-2zm505 165s0-1 1 0c0 1 1 3 1 4h-2-1c-1-1-1-1-1-2s0-1 2-2z" class="k"></path><path d="M385 436c-1 0-4 1-5 0v-2h0 1 4c1 0 1 1 2 1-1 1-1 1-2 1zm149-132h0c1-2 1-3 1-5s0-5 1-7c0 4 0 7 1 10v2h1l-1 1c-1-1-2-1-3-1z" class="C"></path><path d="M672 341h1c1 1 1 1 1 2s-1 2-2 2-1 0-2-1v-1c1-1 1-2 2-2z" class="L"></path><path d="M112 272h0l1 1s1 1 2 1h0l-1-1 1-1 1 1v1c-1 1-1 2-2 3-2-1-2-1-3-2 0-2 0-2 1-3z" class="N"></path><path d="M472 263h1c1 0 1 1 1 1 1 0 1 0 2 1-2 1-2 1-3 2-1 0-1-1-1-1-1-1-2-1-2-1v-1c1 0 1 0 2-1h0z" class="Z"></path><path d="M423 212h0c1 0 1 0 2 1v4h-3 0c0-2 0-4 1-5z" class="a"></path><path d="M280 440v-2c0-2 0-7 2-9v1 7c-1 1-1 1-2 3z" class="H"></path><path d="M97 577h3c0 1 1 1 2 2h1-2c-1 0-6 1-7 0 1-1 2-1 3-2z" class="Q"></path><path d="M495 472l1-2c1 0 2 1 3 1 2 0 4 0 5 1l-7 1h0l-2 2h-1v-1h0c1 0 1-1 1-1v-1h0z" class="M"></path><path d="M532 310h-1c-2 2-9 0-12 0l12-2 1 2z" class="D"></path><path d="M416 211h0l1 1v5h-1-2 0v-5c1 0 1 0 1-1h1z" class="T"></path><path d="M169 300h1c1 0 3 0 3 1 0 2-1 2-2 2l-1 1c-1 0-1 0-2-1 0-2 0-2 1-3z" class="k"></path><path d="M479 396c-1 0-2 0-3-1l1-1-1-1v1l-1-1c0-1 1-1 1-2 1 0 1 0 1-1l1-1 1 1v1h2c-1 1-2 1-2 2l-1 1h0l1 2z" class="K"></path><path d="M121 341c1-1 2-1 3-1 1 1 1 1 1 2-1 1-1 2-2 2 0 1-1 1-1 1-1-1-2-1-2-2s0-2 1-2z" class="I"></path><path d="M107 585s0 1-1 1c-4 2-11 0-15-2 1 0 5 0 6 1h6c2 0 3-1 4 0z" class="U"></path><path d="M615 356c1 0 2 1 3 2 0 1-1 2-2 3h0c-1 0-2-1-3-2 0-2 1-2 2-3z" class="k"></path><path d="M118 510h1c1 1 1 2 1 3s-1 1-2 2h-2v-2c0-2 1-2 2-3z" class="H"></path><path d="M141 281h2c1 1 1 2 1 3s0 1-1 2h-2c-1-1-1-1-1-2 0-2 0-2 1-3z" class="R"></path><path d="M137 257c1 0 1 0 2 1v1s-1 0-1 1v1c0 1-1 2-1 2l-1-1-1-1v1c-1 0-1 0-1-1 0-2 1-3 3-4z" class="N"></path><path d="M142 544h0c2 0 10 0 11 1h0-4l-1 1c-1 0-6 0-7-1l1-1z" class="S"></path><path d="M307 475h2c1 1 1 2 1 3s0 1-1 2h-2c-1-1-1-1-1-2 0-2 0-2 1-3z" class="a"></path><path d="M556 392c1-1 3-3 4-3l1 1h0c-1 0-1 0-2 1-1 0-4 3-5 4 0 1 1 4-1 5 0-2-1-3 0-4 0-1 0 0-1-1l1-1c1-1 2-2 3-2zm-408 85c2 0 3 1 4 3l1 3h0l-2 1v-2c-1-2-2 0-4-2l1-3z" class="D"></path><path d="M374 214h1c1 1 2 3 2 4 1 1 1 1 0 2h-1c-1 0-1 1-2 0 0-2-1-4 0-6z" class="O"></path><path d="M650 443h1l2 2-2 4c-1 0-1 0-2-1s-1-1-1-2 1-2 2-3z" class="N"></path><path d="M126 575c0-1 0-1 1-1l2 2c0 1 1 2 1 4-1-1-2-1-3-2v1h0c1 1 1 2 2 3h0c-1 0-2-1-3-2 0-1 0 0-1-1 0-1 0-3 1-4z" class="E"></path><path d="M504 300h1c0 1 1 2 2 3l1-1v1c-1 1-2 1-3 2l-1 1h0l-1-1c-1-1-2-1-3-1h0v-1c2 0 3-1 4-3h0z" class="k"></path><path d="M78 275c1 1 1 1 1 2 1 2 2 2 3 2v1h-2c-1 1-1 2-2 3h0c0-1-1-2-1-3-1 0-2 0-2-1 0 0 1-1 2-1h1v-3z" class="N"></path><path d="M411 108h1c2 3 5 8 5 12v1 1h0c-1-1-2-4-3-5h0c0-1 0-2-1-3 0-2-1-3-2-5v-1z" class="T"></path><path d="M157 501c1-1 2-1 3 0 0 1 1 1 2 1h0c1 1 1 0 1 1v2c1 2 1 4 1 5 0 2 0 3-1 4 0-2 0-5-1-7 0-3-2-4-5-6z" class="E"></path><path d="M105 421h4 1c0 1 1 3 1 5l-3-4h-1l-1 2v7c-1-3-1-6-1-9v-1z" class="D"></path><path d="M156 494l-1-1h0c3-1 8-1 11-1 1 0 1 0 2 1h0l2 1h-14z" class="e"></path><path d="M460 216h0s1 0 2-1 1-2 1-3l1-1 1 1c-1 2-1 4-3 5-1 1-1 0-1 1h-1-1l-2 1c-1-1-2-1-3-2v-1h4 1 1z" class="X"></path><path d="M305 452c1 0 2 0 3 1v1 1c1 0 1 0 1 1s-1 1-1 1h-4v-3c0-1 1-1 1-2z" class="N"></path><path d="M163 217v-1-1c1-1 1-1 1-2s0-5 1-6c0 3 1 6 0 9h0c1 1 1 0 2 0v1l1 1-2 2h0-1c-1-1-1-2-2-3z" class="C"></path><path d="M519 437h1c1 0 2 1 2 1v1c0 2-1 2-2 3-1 0-2 0-3-1s-1-1-1-2c1-1 2-1 3-2z" class="N"></path><path d="M156 559v-5l-1-4v-1c1-1 2-1 3-1v-1h-1c-1 0-1 0-1-1l1-1v1c1 0 1 0 2 1h5c1 1 1 0 1 1-1 0-1 0-2 1h-3-3-1c0 1 0 2 1 3l-1 1v2 4z" class="X"></path><path d="M535 313h2v1l-1 15c-1-2-1-5-1-7v-2-4c-1-2-1-2 0-3z" class="C"></path><path d="M101 584l-15-2c5-1 10-1 14 0 2 0 2 0 3 1v1h-2z" class="P"></path><path d="M162 171c1 1 1 3 2 3 1 1 2 0 2 1s-1 1-2 1c-1 1-1 2-2 3 0 0-1-2-1-3-1 0-3-1-5-1 2 0 4-1 5-1 1-1 1-2 1-3z" class="B"></path><path d="M181 146l1 1h2s1 0 2 1h0l-1 10h-1c0-2 0-6-1-8-1-1-1-1-3-2 0-1 0-1 1-2z" class="C"></path><path d="M407 59V45l7 2h0c-1 0-2 0-3-1v1c-1 1-1 0-2 1v7h0-1c0 2 0 3-1 4z" class="L"></path><path d="M142 525h0v2 1h1c2-1 3 0 6-1h2 1c-1 1-1 1-2 1v1h1v1c-3 1-7 1-9 1v-6z" class="i"></path><path d="M385 434v-2h1v1 1l1-1c1 0 2 1 3 0l1 1c1-1 1-1 2-1s0 0 1-1h1v3c-1 1-4 1-5 1h-1-4 0c1 0 1 0 2-1-1 0-1-1-2-1z" class="B"></path><path d="M488 471c1 0 2 2 3 3 0 0 1 1 2 1h0v5c0 1 0 1-1 1h0v-3-1c0-1-1-1-1-2-1 0-1-1-1-1-1-1-2-1-4-1-2 1-3 0-5-1 2 0 4 0 7-1z" class="L"></path><path d="M141 548c4-1 9 1 12-1h1v22c-1-3-1-6-1-9v-8-2h-1c-1-1 0-1-1-2h-1c-2 0-5 0-6 1v1 2c-1 0-1 0-1-1l-1-1c-1-1-1-1-1-2z" class="S"></path><path d="M132 574c1 0 1-1 2 0 2 0 3 1 4 2-1 2 0 3-1 4l-3-2v1c-1-1-2-4-2-5z" class="d"></path><path d="M282 437c1 4 0 7 0 11v1h-2c-1-2 0-8 0-9 1-2 1-2 2-3z" class="L"></path><path d="M441 208l10 3c-1 0-2 0-3 1l-11-2 1-1 3-1z" class="j"></path><path d="M628 375h2c1 1 3 2 4 3l-3 4 1 1h1v1l-2-1c-1-2-2-2-3-4v-2l-1-1 1-1z" class="I"></path><path d="M398 211v-1c1 0 3 0 3 1v1h1v5h-1v-1h-1v-1c0 1 0 1-1 1v1h-1l-1-1h0c1-2 1-4 1-5z" class="Y"></path><path d="M305 452l1-1h-1c0-1 0-1-1-2v-1-1h0c1 0 1 0 1 1v1h1l1-1h0 1v1c1 0 1-1 2 0 1 0 1 1 1 1v2h-1l-2 2v-1c-1-1-2-1-3-1z" class="K"></path><path d="M100 582h9 4v1c-2 0-4 1-6 2-1-1-2 0-4 0h-6c1 0 3 0 4-1h2v-1c-1-1-1-1-3-1z" class="h"></path><path d="M139 562h1v3 10h-1c-1-2-1-3-1-4l1-1h0 0v-2c-1 0-1 1-1 1v1c-1 0-2 1-2 2l-1-2h0l3-3c0-1 0-2 1-3v-2z" class="b"></path><path d="M104 183l1-1 2 2s0 1 1 1h1c0 1 0 0-1 1-1 0-1 1-1 2-1 1-2 1-3 1-1-1-2-3-2-5 1-1 1-1 2-1z" class="k"></path><path d="M170 491h-5c-3-1-6 0-9 0 0 0 0-1-1-1l1-1h11 2 3l1 1c-1 1-2 1-3 1z" class="G"></path><path d="M380 68l1 1v2h0l1-1h1v2h0l1-1 1 1h0l-2 1-2 1h1l1 1h-1-1c0 1-1 2-1 2l-1-1c1-1 1-1 0-2h-1c-1 0-1 0-2-1h1l1-1-2-1h2l1-1h0 1v-2z" class="k"></path><path d="M160 498c1 1 2 2 3 2v1c0 1 1 2 2 2v7h0-1c0-1 0-3-1-5v-2c0-1 0 0-1-1h0c-1 0-2 0-2-1-1-1-2-1-3 0-1 0-1-1-2-1v-1c1-1 0-1 1-1h0 3 0 1z" class="n"></path><path d="M460 413h3v-5c1 1 1 4 1 5 1 0 1 1 1 2h1v-1h1v1h0c1 1 1 1 1 2v2h-1v-1l-1-2c-1-1-1-1-3-1-1 1-1 1-2 3v1h-1l-1-1h0c0-2 0-3 1-5z" class="h"></path><path d="M460 413l2 1c0 1-2 2-3 4h0c0-2 0-3 1-5z" class="l"></path><path d="M380 410v-1h2 4v2h-3c-1 1 1 7-1 7h-2c-1-2 0-3 0-4v-1l2 1v-1h0l-2-1v-2z" class="D"></path><path d="M595 443c3-5 4-9 9-12l1 1-4 4c-2 3-3 6-4 8-1 0-2-1-2-1z" class="B"></path><path d="M495 472l-1 1c-2 0-2 0-4-1v-2-2l-1 1v2l-1-1v-1c2-2 2-1 5-2 0 1 1 1 1 1v-1l1-1 1 1-1 1v3 1h0z" class="N"></path><path d="M617 273h0l1 3c2 0 2 1 3 2h-1l-1 1h1v1h-2c0 1-1 2-1 3v-1s-1-2-1-3c-1 0-1 1-2 0l1-1c-1 0-1-1-1-1l-1-1h1 1c1-1 1-2 2-3h0z" class="k"></path><path d="M446 525c5-2 11-2 16 1l3 3h-2c-4-3-7-4-12-4-2 0-3 1-5 1h0 0v-1z" class="B"></path><path d="M395 217h-4v1l-1-1v-1c-1-1 0-4 0-5l5-1c-1 2-1 5-1 7h1z" class="K"></path><path d="M430 252c3 0 6 0 9 1h1v-2h0c1 1 1 2 2 3 1 0 2 2 3 2 1-1 1-1 1-3h1c0 1 1 1 0 2 0 1 0 1-1 2-3 0-6-2-9-3s-5-1-8-1l2-1h-1z" class="J"></path><path d="M548 391c-1-1-2-2-3-2-1-1-1 0-2-1h1 2v-2c-1 0-2 1-3 0 1-1 2-1 3-2h-1l1-1h1v2h0c0 2 1 3 2 5 1 1 3 2 5 2 1 0 1-1 2-1v1c-1 0-2 1-3 2-2-1-2-1-3-2s-1-1-2-1z" class="X"></path><path d="M380 434v-5-1c-1-1 0-4 0-6v-1l2 1c1 1 1 5 0 7v1l1 3c-1 0-1 0-2 1h-1zm-135-93h1c0 1 1 6 1 8-1 3-4 6-5 8l-1 4-1-2c0-2 1-3 1-5h0 0c1-1 2-3 3-4l1-1s1-2 0-3v-3-1-1h0z" class="L"></path><path d="M284 53h0l1 1v-1l2 2v1h5v1c-2 0-3 1-4 0h-1c-1 1-1 2-2 3h0c-1 0-1-2-2-3s-6 1-7-1c2 0 5-1 7 0h0c0-1 0-2 1-3z" class="I"></path><path d="M561 280c1 0 2 2 3 3 0 0 1 0 2 1l-2 1v1h2 0l-2 1c-1 0-2 2-2 3h-1v-2c-1-2-3-2-4-2l-1-1c2 0 2 1 4 0v-1h-1l-1-1h3v-3z" class="k"></path><path d="M109 574h1c1 0 3 1 4 3-1 0-1 0-2 1h1c1-1 2-1 3-1 0 1-1 2-2 2h-6c-1 0-2 0-2-1 1-1 2-3 3-4z" class="L"></path><path d="M626 183h0c0 2 0 4 2 5v2c1 1 2 1 3 2-1 1-3 1-4 1l1 1c-1 1-1 1-1 2h-1c0-1-1-1-1-2h0c-1-2-3-2-5-2 2-1 3-1 4-1 0-1 1-1 1-2h1v-6z" class="I"></path><path d="M595 443s1 1 2 1l-3 3c-2 1-4 1-6 3-2 1-4 4-6 6v-1 1h-1-1c2-3 4-6 6-7 3-2 6-3 9-6zm-444 41h-10c1-3 3-5 5-7h2l-1 3c2 2 3 0 4 2v2z" class="N"></path><path d="M280 283l1-1c2-1 3-3 4-4l3-4 4 2-3 3v-1-2c-1 1-5 6-5 7l-1 1h0c-1 1-3 2-4 3 0-1 1-2 2-2h0l1-1h0c0-1 1-1 1-2-1 1-3 3-5 3-1 0-1 0-2 1h0v-1h1c0-1 1-1 2-1l1-1z" class="C"></path><path d="M162 594c2 0 3 1 4 1 3 2 6 4 9 5 1 1 3 2 4 2l2 1 1 2-1 1c-4-3-8-4-12-7-3-1-5-3-7-5z" class="L"></path><path d="M374 434h4v2c-1 1-3 0-4 1 1 1 3 0 5 1l-2 2c0-1 0-1-1 0h0c0 1 1 0 2 0 0 1-1 1 0 2v1h-5c0-2-1-4 0-6v-1-2h1z" class="N"></path><path d="M104 592h-7-15c7-1 15-3 22-3v1h1-1l1 1-1 1z" class="E"></path><path d="M420 252l9 1c3 0 5 0 8 1s6 3 9 3h0-4-1c0 1 0 1 1 1l1 1-1 1-1-1c-1 0-2-2-3-3-1 0-3 0-5-1h-3c-1 0-2-1-3-1h-3c-1-1-3-1-4-2z" class="c"></path><path d="M398 109l1 1h0c-1 2 0 0-1 1v1l-1 1-1 2v1 1h0c-1 2-2 5-3 7s-2 7-3 10v-2h-1c0-3 2-7 2-9l-4-1h5c0-1 0-1 1-2v-2l3-6 1-1c1-1 1-1 1-2z" class="b"></path><path d="M131 153v4c1 1 2 1 2 2 2 2 0 4 0 6h0l-1 1h0-1c-1-1-1 0-1-1-2 0-2-1-2-3 0 0 1-2 2-2 1-1 2 0 3 1v-1l-1-1h-3c-1 1-2 2-2 4l-1 1c-1 0-2 0-3-1h-1 3l4-5c1 0 1-1 1-1 1-1 0-3 1-4z" class="k"></path><path d="M442 189c3-1 7 0 10 0h6-1c-4 1-8 0-13 1h-1 0 3c3 0 5 0 7 1h-10c0 4 0 9 1 14h-1-1c-1-3 0-12 0-16z" class="C"></path><path d="M455 210c2 1 4 2 5 4v2h-1-1-4c-1-1-2-2-3-2 0-1-2-2-3-2 1-1 2-1 3-1h2c1 0 1 0 2-1z" class="H"></path><path d="M451 211h2c1 0 2 1 4 2h-1 0-2c-1 0-2 0-3 1 0-1-2-2-3-2 1-1 2-1 3-1z" class="X"></path><path d="M250 327c1-1 2-2 3-4 3-3 4-6 8-7v1l-2 2c-2 1-3 4-4 5-2 2-4 3-5 6s-2 6-4 9v2h-1v-1h0c0-2 2-4 2-6 1-2 1-5 3-7z" class="Q"></path><path d="M553 607l3-1 1 1-20 11v-1c-1 0-2 1-3 1l9-6 10-5z" class="C"></path><path d="M307 519l1-1c1 2 2 3 4 4v1c0 1 2 3 3 3l1 1h1c1 0 1 0 1 1s0 1-1 1h0-8 0l-1-2h2s1 1 2 1v-1c-2-2-4-5-5-8z" class="K"></path><path d="M144 485h6l1 1c0 1 0 1-1 2 1 0 1 0 1 1 0 0 1 1 0 2 0 1 1 1 1 2v1h0-6c0-2 1-7 0-8l-2-1z" class="B"></path><path d="M142 525c2 0 2-2 3-3 2 0 4 0 7 1h-1v1h1v1l-1 1h1v1h-1-2c-3 1-4 0-6 1h-1v-1-2z" class="C"></path><path d="M146 526c-1 1-2 1-2 1h-1l2-2c0 1 1 1 1 1z" class="M"></path><path d="M145 525l1-2h3c0 1 1 1 2 1h1v1h-2-1l-3 1s-1 0-1-1z" class="I"></path><path d="M274 340h1c2 0 2 1 3 3v1 1h0c1-1 1-1 2-1h0l-2 2h-1-2c1 1 1 1 1 2l-1 1v2l-1 4c0-2 1-3 0-4v-3h-1-1l1-1v-1l-1-1c-1-1-1-1 0-3 0-1 1-1 2-2z" class="N"></path><path d="M403 404h20c6 0 11 0 17 1l-1 1h-1c-6 0-12-1-18-2-3 0-6 1-9 1h-17c3-1 6-1 8-1h1z" class="T"></path><path d="M143 446h0v-5l2 3v2c0 2 1 3 1 5h0c-1 3-1 5-1 7-1 2-2 4-2 6v-2-1c0-1 0-1-1-2l1-13z" class="C"></path><path d="M267 457v2l-1 1v1 1c2 1 2 2 2 3-1 0-2 1-3 1h0c0-1-1 0-2 0s0 0-1-1h0c1 0 1-1 2-1 0-1 0-2-1-2h-1v1h-1l-1-1-1-2s1-1 1-2l2 1v-1-1h2 3z" class="Y"></path><path d="M262 457h2c1 1 1 1 1 3-1 0-2-1-2-1l-1-1v-1z" class="T"></path><path d="M405 42h1v1c-1 3 0 6 0 9v19 6l-1 1v-1-32l-2-2 2-1z" class="F"></path><path d="M467 414l2 2c0 2 0 3-2 5 0 0-1 1-3 1 0 1 0 5-1 5v-4l-1-1c-2-1-3-2-3-4h0l1 1h1v-1c1-2 1-2 2-3 2 0 2 0 3 1l1 2v1h1v-2c0-1 0-1-1-2h0v-1z" class="i"></path><path d="M461 418c1-2 1-2 2-3 2 0 2 0 3 1l1 2h0l-3 3s-1 0-1-1c-2 0-2-1-2-2z" class="I"></path><path d="M437 222c0 1 2 1 3 2s1 3 2 4l1 6h0v1c-2 1-3 1-4 1-1-1-3-13-2-14z" class="D"></path><path d="M439 236v-3c1 0 3 0 4 1v1c-2 1-3 1-4 1z" class="N"></path><path d="M249 422c1 2 0 6 1 8v6 5h-1l-1 2 1 1-2-1c-1-1-2-2-3-4l1 1 1-1 1-1c1 0 1 0 1-1v-1c-1-1-1-2 0-3-2-2-2-2-2-4h0c0-1 1-2 2-2l1 3v-1c-1-2-1-4-1-7h1z" class="F"></path><path d="M248 427l1 3v1l1 1c-1 0-2 1-2 1-2-2-2-2-2-4h0c0-1 1-2 2-2z" class="E"></path><path d="M244 439l1 1 1-1 1-1c0 1 1 1 1 2v1 2l1 1-2-1c-1-1-2-2-3-4z" class="c"></path><path d="M104 589c1-1 3-1 5-2 3-1 6-3 10-3 0 1 0 1 1 1l5 2-1 1c-1 0-1 0-2 1h0c-2 0-3-2-5-1h-1c-2 0-2 0-4 1-2 0-4 0-7 1h-1v-1z" class="H"></path><path d="M463 529h2c1 1 1 2 2 2 2 0 4-1 6 0 3 0 6 2 8 4 1 1 2 2 2 4l-1 1-1-2c-3-3-6-5-9-6l-1 1v1h-5-2l-1-1h0c0-1 1-1 1-1l1-1-2-2z" class="N"></path><path d="M465 531c1 1 3 1 5 1h2l-1 1v1h-5-2l-1-1h0c0-1 1-1 1-1l1-1z" class="a"></path><path d="M443 234l1 5 1 5c1 1 1 3 1 4h-1c-2-2-3-2-5-3v-1-1c-1-2-1-4-1-5v-2c1 0 2 0 4-1v-1h0z" class="M"></path><path d="M443 238l1 1 1 5c-1-1-2-2-2-3h-2l-1-1h1 1l1-2z" class="L"></path><path d="M443 234l1 5-1-1-1-1c-1 0-2 0-3 1v-2c1 0 2 0 4-1v-1h0z" class="B"></path><path d="M178 492c1 0 3 0 4 1h0v1c-1 1-1 1-3 2h-1-12-11l1-1v-1h14c2 0 3 0 5-1h1v-1h2z" class="j"></path><path d="M178 492c1 0 3 0 4 1h0v1c-3-1-5 0-7-1h1v-1h2z" class="R"></path><path d="M575 586h0 1c0 2-1 4-2 6h1v1c-2 0-4 3-6 5-3 3-8 6-12 9l-1-1-3 1c6-4 12-8 16-12 3-3 4-6 6-9z" class="B"></path><path d="M298 289c1 1 1 3 2 5v4c1 0 1 1 1 2s0 2-1 3c-1 2-1 4-2 6-1-1-1-2-1-2l1-3h0c-1 0-2 1-2 1-1-2-1-1 0-3v-3l-1 1h0l-1-1 1-1 1-1 1-2 1-6z" class="f"></path><path d="M298 299v-1l2 2c0 2 0 2-1 3h-2l-1-1v-1c0-1 1-2 2-2z" class="k"></path><path d="M181 144v-1c0-1-1-1-1-2h1 1c2-2 1-7 1-9 0-1 0-2 1-3l1 9v3l1 1c1 0 2 0 2 1s-1 1 0 2h7c-2 1-6 2-8 1v1l2 2h0c-1 0-2-1-3-1-1-1-2-1-2-1h-2l-1-1v-2z" class="N"></path><path d="M373 422c0-1 1 0 2 0h1l-1 1h1c0 1 0 1 1 1 0 1 1 1 2 1 0 1 0 3-1 4l1 1c-1 1-1 2 0 3l-1 1c-1 0-3-1-4 0h-1c-1-1 0-2 0-3v-9z" class="C"></path><path d="M168 218l2 2v2c2-1 5 0 7 0-2 1-6 1-8 1h0c-1 0-2 1-4 1l1 1h0c0 1 0 1-1 2l-2-1c-1 0 0 1-1 0l1-1h2l-3-3h-4-8c3-1 8-2 10-1 1 0 1 0 1-1v-1c1-1 1-2 2-2 1 1 1 2 2 3h1 0l2-2z" class="I"></path><path d="M564 596h1 1c1-1 2-2 3-2v1c-4 4-10 8-16 12l-10 5h-1l3-3c1 0 1-1 2-2h-1 1c6-3 11-6 17-11z" class="W"></path><defs><linearGradient id="A" x1="121.35" y1="587.379" x2="118.734" y2="595.332" xlink:href="#B"><stop offset="0" stop-color="#dedfdc"></stop><stop offset="1" stop-color="#fcf9ff"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M105 591h20 7c2 0 7 0 8 1v1c-12-1-24 0-36-1l1-1z"></path><path d="M406 244h5c8 1 16 1 25 3 2 1 3 1 5 2 1 0 3 1 4 1l2 1h0c-2 1-6-1-8-2s-3-1-4-1c-6-1-11-2-16-2h-15-6-1s1 0 1-1h0l-1-1h9z" class="G"></path><path d="M406 244h5v1h4c-1 1-2 0-3 0-3 1-6 0-8 1h-6-1s1 0 1-1h0l-1-1h9z" class="F"></path><path d="M438 399h0 2v2 1s0 1 1 2h1v1h1c1 1 1 1 1 2h0c-2 0-4 0-6-1h1l1-1c-6-1-11-1-17-1h-20c2-1 4-1 6-1 1 0 1 0 2-1h7 3l9 1 9 1c0-2-1-3-1-5z" class="i"></path><path d="M411 402h7c-1 0-1 0-2 1-2 1-5 0-7 0 1 0 1 0 2-1z" class="o"></path><path d="M286 282c3-2 6-3 9-5 2-1 3-3 4-4s2-1 3-2c3 0 4-2 6-3 3-2 7-4 10-5 2-1 4-2 6-2v1c-4 3-10 4-14 7-2 1-4 2-5 3-2 1-4 2-5 3-2 1-3 3-5 4-2 2-5 2-8 4 0-1 0-1-1-1z" class="M"></path><defs><linearGradient id="C" x1="387.097" y1="211.415" x2="383.955" y2="204.545" xlink:href="#B"><stop offset="0" stop-color="#353136"></stop><stop offset="1" stop-color="#434340"></stop></linearGradient></defs><path fill="url(#C)" d="M364 210c3-1 7-1 10-2 6-1 12-1 18-1h13c2 0 4 0 6 1l-44 3v-1h2 2 3 1v-1h-4-2c-1 1-1 1-2 1h-3z"></path><path d="M298 495l9 24c1 3 3 6 5 8v1c-1 0-2-1-2-1h-2c-3-3-4-8-5-12l-6-19 1-1z" class="H"></path><path d="M303 515c2 4 5 8 7 12h-2c-3-3-4-8-5-12z" class="P"></path><path d="M163 419h0s-1 0-1-1c0-2 0-4 1-5v4h1v-4h0v-1c-1-1-1 0-1 0-1-2-1-2-1-4 1 0 0 0 1-1l2 2-1 1v-1h-1v1c0 1 1 0 2 2 1 0 0 1 1 1l1-1c0 3-1 5 0 7l2 5-3 1c0 2 0 2-1 2v-1c-1-1-3-1-4-1 0-2 1-4 2-6z" class="Y"></path><path d="M163 419l2 1c0 2 1 3 0 4l1 1c0 2 0 2-1 2v-1c-1-1-3-1-4-1 0-2 1-4 2-6z" class="B"></path><path d="M125 587c3 0 5 1 8 2h10c-1 1-2 1-3 1h-6-1 0l-1 1h-7-20l-1-1h1c3-1 5-1 7-1 2-1 2-1 4-1h1c2-1 3 1 5 1h0c1-1 1-1 2-1l1-1z" class="S"></path><path d="M106 410c1 0 1 1 1 2h1 1c1 0 1 1 2 3 0 1 0 3-1 4-2 1-4 1-6 1l-1-1v1l1 1c0 1-1 2-2 3v-1-3-1-2c0 1-1 1-1 1-1 1-3 1-4 1h0l2-1c1 0 2-1 3-2 0 0 1-2 1-3l3-3h0z" class="k"></path><path d="M453 455c1 1 1 1 0 2-1 2-3 3-6 4 0 0-3 0-3 1v5l1 21c-1-1-2-1-3-2v-24c2-2 6-2 8-4 2 0 2-1 2-2l-1-1h2z" class="M"></path><path d="M140 565c0 1 0 3 1 4v-1l1 1h1v-6h1v2 2 3h1s1 0 1 1c-1 2-2 3-1 4v3h-1v1c-1 2 0 3 0 5h0c-1 1-1 1-2 1 0 0-1 0-1-1-1-1-1-5-1-6 1-1 1-1 1-2l-1-1v-10z" class="h"></path><path d="M142 579h2c-1 2 0 3 0 5h0c-1 0-1 0-2-1v1-5z" class="l"></path><path d="M144 570h1s1 0 1 1c-1 2-2 3-1 4v3h-1v1h-2l-1-3c1-2 0-4 1-6h2 0z" class="O"></path><path d="M141 576h2c1 1 1 1 1 2v1h-2l-1-3z" class="m"></path><path d="M429 416v-1h1v1c1 0 1 0 2-1v7 8c1 2 1 5 0 6h-4l1-1h0v-1-1c-1-2 0-4 0-6v-11z" class="P"></path><path d="M431 430c0-1 0-2-1-2l1-1v-2c0-2 0-2 1-3v8h0-1z" class="e"></path><path d="M431 430h1 0c1 2 1 5 0 6h-4l1-1 1 1 1-1v-2c0-1-1-1-1-2l1-1z" class="F"></path><path d="M141 548c0 1 0 1 1 2l1 1c0 1 0 1 1 1h0v3 4 2 1 1h-1v6h-1l-1-1v1c-1-1-1-3-1-4v-3h-1c-1-1 0-4 0-5v-6l1 1c0-1 1-3 1-4z" class="O"></path><path d="M141 548c0 1 0 1 1 2l1 1c0 1 0 1 1 1h0v3c-1 1-1 1-1 2-1-1-1-2-1-4h0c-1 1-1 3-1 4v6h-1v-11c0-1 1-3 1-4z" class="P"></path><path d="M486 338h1l1 9c1 1 2 2 3 4h0c2-1 7 0 10 0-1 1-1 1-2 1h-5-2-1-1v2c-1 0-2 1-3 1v6h0c-1-2-1-4-1-5v-1h-1c-1-1-3-4-3-4-1 0-1 1-2 1-1 1-4 0-6-1l8-1h1v-2h1 1c1-3 1-7 1-10zm48-34c1 0 2 0 3 1l1-1 3 3v2 1h1c2-1 8-1 9 0l-7 1h-4c-1 0-1 1-2 2l1 1h0l-2-1h-2c-1 0-1 1-2 1h-1l-2-1h0c1-1 1-1 2-1h1l-1-2-1-2c1 0 0-1 0-2l1-1h2v-1z" class="k"></path><path d="M276 458c1-2 2-7 4-9h2c1 1 1 3 2 4l4 8c1 1 2 2 2 4l-6-1v-2l-1-1c0-1 0-2-1-4h-1-1-1c-1 1-2 1-3 1z" class="C"></path><path d="M437 437s1 0 2-1h0-2v-1c0-1-1-1 0-2l-1-1c1-1 1-1 1-2-1-2 0-5 0-7s-1-5 0-7h2c-1-1-1 0-2-1v-3h1 1v-1h0 1v6c1 6 0 13 0 20h-3z" class="V"></path><path d="M442 410c1-1 1 0 2 0v3 7 19 10h-2v-6-33z" class="C"></path><path d="M497 498c2 1 3 3 4 5l3-4 2 5 2-2c1 1 0 2 0 3 2 1 4 2 6 2-2 0-6 0-8 1l1 2h0-1c-1 0-1 0-2 1v3c-1-1 0-3-2-3 0-1-1-2-2-3h0c0 1 0 2-1 2 0 1-1 2-2 2v-1l2-3c-2-1-5 0-8 0 2-1 5-1 7-1h2c-2-1-4-2-6-4h0l3 1c0 1 1 1 2 1 0-1 1-1 1-2 0-2-2-3-3-5z" class="k"></path><path d="M389 399c1 0 1 0 2 1v-1c1 1 1 3 1 4l1-1h6v1h0c1 0 2 0 3 1-2 0-5 0-8 1l-24 2v-1-1-1h3c3-1 6-1 8-1l1-1h0 8l-1-1v-2z" class="R"></path><path d="M373 404c3-1 6-1 8-1h1v1l-12 1v-1h3z" class="a"></path><path d="M389 399c1 0 1 0 2 1v-1c1 1 1 3 1 4-2 1-7 1-10 1v-1h-1l1-1h0 8l-1-1v-2z" class="T"></path><path d="M389 399c1 0 1 0 2 1v1c0 1-1 1-1 1l-1-1v-2z" class="Q"></path><path d="M580 456h1 1v-1 1c0 1-1 3-2 4 0 3 0 6-1 9s-4 5-5 8v1c-1 3 0 7-1 10l-4 4c-4 4-7 7-9 11l-1 1-1 1-1-2h0c1-3 4-7 7-9 3-1 4-3 6-5 0 0 1-1 1-2 1-2 1-6 1-8 1-2 1-4 2-6s2-3 3-5c1-5 2-8 3-12z" class="M"></path><path d="M154 533h9 7l-2 1h-1 0c0 2 0 4 1 6h-3c-3 1-9 0-12 0v-1h0c0-2 0-4 1-6z" class="d"></path><path d="M154 533h9c-2 0-7 0-8 1v1h1v2l-1 1v2c-1 0-1 0-2-1 0-2 0-4 1-6z" class="a"></path><path d="M156 535h4v5h-5v-2l1-1v-2zm9 5c-2 0-3 0-4-1h0c0-2 0-3 1-4h1v2h1c0-1-1-1 0-2h1c0 1 0 1 1 2v-1-1l1-1c0 2 0 4 1 6h-3z" class="T"></path><path d="M155 496h11 0-3c0 1 0 1 1 1 1 1 1 2 2 3h0v3 2 6 12c0 2 0 6-1 8h-1c-1-1-1-4-1-5v-12c1-1 1-2 1-4h1 0v-7c-1 0-2-1-2-2v-1c-1 0-2-1-3-2s-2-1-4-2h-1z" class="d"></path><path d="M442 410v33 6h2l-1 1c-1-1-3 0-5-1v-1l1-1h0l-2 1v-1c0-1 0-1 1-2h-1c-1-1 0-4 0-6v-2h3c0-7 1-14 0-20v-6h1l1-1z" class="X"></path><path d="M441 446v-8h0c0 2 0 3 1 5h0v6h2l-1 1c-1-1-3 0-5-1h2l1-3z" class="Y"></path><path d="M437 437h3v9h1l-1 3h-2v-1l1-1h0l-2 1v-1c0-1 0-1 1-2h-1c-1-1 0-4 0-6v-2z" class="C"></path><path d="M623 459c2-1 3-1 5 0s5 3 5 6c1 2 1 5 0 7s-2 3-4 4c0 2 0 6-1 8 0 1-2 1-1 2 2 3 2 5 2 8s-1 5-1 7c2 1 3 1 4 2-1 1-2 1-3 1l-2-2c-1 0 0-1-1-2h-1l1-4-1-6h-1c1-1 1-1 2-1-1-1-1-2-1-2 2-3 2-6 2-8v-2l1-1c3-3 4-5 4-9 0-1-1-2-1-4l-2-2c-1-1-2-1-4-2h-2 0z" class="M"></path><path d="M626 496l1 1v5c-1 0 0-1-1-2h-1l1-4z" class="b"></path><path d="M626 489c1 1 2 4 1 6v2h0l-1-1-1-6h-1c1-1 1-1 2-1z" class="X"></path><path d="M129 559c0 2-1 4-2 5-1 2-1 3-1 5h-1 0v3h0v2l1 1c-1 1-1 3-1 4l-1-1c-1 0-1 0-2-1v-1l-1 1c1 0 1 1 2 2-1 0-1 0-1 1-2 0-3-1-4-1v-10c0-1 0-1 1-2 1 0 1-2 2-3v-1h1c1 1 1 1 2 1 2-1 3-2 4-3l1-2z" class="R"></path><path d="M124 569h1v3h0v2c0 1-1 1-1 2-2-2-1-3-2-5 1 0 2 0 2-1v-1zm-3-5l1 1v4 1-1c-1 1-1 2-1 3l-2 2v-1-2-3-1c1 0 1-2 2-3z" class="D"></path><path d="M129 559c0 2-1 4-2 5-1 2-1 3-1 5h-1 0-1v1c0 1-1 1-2 1v-1-1-4l-1-1v-1h1c1 1 1 1 2 1 2-1 3-2 4-3l1-2z" class="j"></path><path d="M122 565c1 0 1 0 2 1v3 1c0 1-1 1-2 1v-1-1-4z" class="L"></path><path d="M286 282c1 0 1 0 1 1-2 1-2 2-3 3-2 3-5 6-7 9l-6 6c-3 6-1 13-8 16h-2v-1l1-1c4-2 6-7 7-11-1-1-1-1-2-1 0-1 1-2 1-4 1 0 2-1 2-2l1-1h0-2-1v-1c1-1 3-1 4-1s1-1 2-2h-1c1 0 1 0 2-1h2 1c2-2 5-4 5-7h0 1l2-2z" class="D"></path><path d="M273 292c1 0 1 0 2-1h2c-1 4-4 5-6 9-1 1-2 2-2 4-1-1-1-1-2-1 0-1 1-2 1-4 1 0 2-1 2-2l1-1h0-2-1v-1c1-1 3-1 4-1s1-1 2-2h-1z" class="K"></path><path d="M604 431c3-2 8-6 12-5 2 0 3 1 4 2 1 2 1 4 1 6 0 1-1 3-2 4h1c1 2 1 3 1 5-1 4-5 9-3 12 1 2 3 3 5 4h-3l-3-4c-1-1 0-2 0-3l-1-1c0 1 0 3-1 3h0-3c1-1 2-2 2-3v-1c0-1 0-2 1-3v-1c2-2 3-5 3-7h0v1-3l1-3s1-1 1-2-1-3-2-4-2-1-3-1c-4 1-7 3-10 5l-1-1z" class="C"></path><path d="M615 446c2-2 3-5 3-7h0v1c0 1 1 2 1 2 0 3-2 7-2 10l-1-1c0 1 0 3-1 3h0-3c1-1 2-2 2-3v-1c0-1 0-2 1-3v-1z" class="X"></path><path d="M614 451l3-6c0 2 0 4-1 6 0 1 0 3-1 3h0-3c1-1 2-2 2-3z" class="Q"></path><path d="M547 385l4-4c1-2 2-3 2-5 1 1 1 1 1 2s2 2 3 3c2 2 3 3 4 5h7v1l-8 1c-1-2 0-2 0-3-1-1-3-2-4-3 1 2 3 3 3 5l-3 4c-1 0-1 1-2 1-2 0-4-1-5-2-1-2-2-3-2-5z" class="I"></path><path d="M370 410c3-1 7-2 10-2 1 1 1 1 2 0h4v1h-4-2v1h-1 0c0 2-1 3-1 4 1 1 1 3 1 4-1 1 0 1 0 2-1 1-2 1-3 1l-1 1c-1 0-2-1-2 0l-1-4h-1c1-1 1-3 0-4l-1 1v2l-2 1v-8h2z" class="e"></path><path d="M373 411c1 0 5-1 5 0v1c-1 1-3 1-5 1v-2z" class="I"></path><path d="M368 410h2v3h2v-2-1s1 0 1 1v2 1 7h3l-1 1c-1 0-2-1-2 0l-1-4h-1c1-1 1-3 0-4l-1 1v2l-2 1v-8z" class="c"></path><path d="M378 414c1 1 1 3 1 4-1 1 0 1 0 2-1 1-2 1-3 1h-3v-7c2-1 4 0 5 0zM329 70c1 2 0 5 0 7 0 1 2 2 2 2 1 1 2 2 2 3h1v2h0l-1-1h0c0 1 0 2-1 3v1h1c0-1 0-2 1-2 2 1 6 1 8 1-2 1-5 0-7 1-1 0-4 3-5 4l-1 5v-5c-1-1-3-2-4-3v-1c-2-1-7-1-9-1l8-1v-1h1v-3c0-1 2-2 3-2 0-3 0-6 1-9z" class="I"></path><path d="M423 238h1 0c0 1 1 3 1 4 2 1 4 1 6 1h2l1 1h5v1l-1 1h1 1v-1c2 1 3 1 5 3h1c0 1-1 1-1 1v1c-1 0-3-1-4-1-2-1-3-1-5-2-9-2-17-2-25-3h-5c-1-1-5 0-6-1h1 6c1-1 1-2 1-3h0v2l1 1 11-1c1 0 3 0 4-1 0-1 0-2-1-3z" class="f"></path><path d="M431 243h2l1 1h5v1l-1 1-8-2v-1h1z" class="p"></path><path d="M423 238h1 0c0 1 1 3 1 4 2 1 4 1 6 1h-1v1c-3 0-6-1-8 0h-1l-2-1h0l1-1c1 0 3 0 4-1 0-1 0-2-1-3z" class="W"></path><path d="M134 559v2 1h1c1-2 2-2 2-4l2-1c0 1-1 4 0 5v2c-1 1-1 2-1 3l-3 3h0l-2 2c-2 0-4 1-6-1v-1h-1l-1 2h0v-3h0 1c0-2 0-3 1-5 1-1 2-3 2-5h0c1-1 3-2 4-3l-2 3-1 1v1l1 1 1-1c0-1 1-1 1-2h1z" class="J"></path><path d="M130 567c0-1 1-1 2-1 1-1 0-1 2-1v2c-1 0-2 1-3 2h0c-1 0-1 0-2-1l1-1z" class="c"></path><path d="M134 559v2 1h1c1-2 2-2 2-4l2-1c0 1-1 4 0 5v2c-1 0-2-1-3-1h0v2h-1v-1l-2-1c0-1 0-3 1-4z" class="B"></path><path d="M129 566l2-2h0c-1 1-1 2-1 3l-1 1c1 1 1 1 2 1h0 1c1-1 2-1 3-2h0c0 1 0 2-1 2l1 1h0l-2 2c-2 0-4 1-6-1v-1h-1l-1 2h0v-3h0 1c0-2 0-3 1-5 0 1 1 1 2 2z" class="f"></path><path d="M127 564c0 1 1 1 2 2l-2 1v2l-1 1v-1c0-2 0-3 1-5z" class="G"></path><path d="M152 575h1l1-2-1 10h0c0 1-1 1-1 1 2 1 6 1 8 1l2-1 5 2-1 1h-3v1h-1-3c-1 0-1 0-2-1h-2v1h-6c1-1 1-2 1-3h1v-1l-1-1v-1h-1v1c-2 1-4 0-5 1 0-2-1-3 0-5v-1h1v-3h6 1z" class="i"></path><path d="M162 584l5 2-1 1h-3c-1-1-3-1-4-1l-1-1h0 2l2-1z" class="H"></path><path d="M152 575h1l1-2-1 10h0c-1-1 0-3-1-4v-1h-1v1c-1-1-1-1 0-2h1v-2z" class="E"></path><path d="M144 578h1 0c1 2 1 3 0 4h1c1 0 1 0 1-1l1 1-1 1h2c-2 1-4 0-5 1 0-2-1-3 0-5v-1z" class="g"></path><path d="M151 585h5 0l-1 1v1 1h-6c1-1 1-2 1-3h1z" class="B"></path><path d="M370 228v-2c1-1 2-2 4-3h0c11-4 24-5 35-3-3 0-8-1-10 0-1 1-2 1-2 1-1 1-1 1-1 2v1 1h-1c-1 1-1 1-2 1l-9 1v-4h0-1c-1 0-1 0-2 1l-1 1h0-2 0c-2 1-3 1-4 3v1h-1c-1-1 0-1 0-2-2 0-2 1-3 1z" class="e"></path><path d="M393 221h2v1 2 1h0c-1 1-1 1-2 1l-9 1v-4h0c1 0 1-1 1-1 1-1 6-1 8-1z" class="D"></path><path d="M384 223c1 0 1-1 1-1 1-1 6-1 8-1v1c-1 2-5 1-8 2l-1-1z" class="N"></path><path d="M418 205h14c8 2 16 2 23 5-1 1-1 1-2 1h-2l-10-3-3 1-1 1-12-1-14-1c-2-1-4-1-6-1h-13c2-1 5-1 7-1h6 15 5 2 3s1 1 2 1h1 3 0c-2-1-3 0-4-1h-1-3-4c-2-1-4-1-6-1z" class="L"></path><path d="M399 206c11 0 22 0 32 1l10 1-3 1-1 1-12-1-14-1c-2-1-4-1-6-1h-13c2-1 5-1 7-1z" class="O"></path><path d="M431 207l10 1-3 1-1 1-12-1c1-1 3 0 4-1h-2-4c2 0 3-1 5 0h0c1 0 2 0 3-1z" class="T"></path><path d="M446 526h0c2 0 3-1 5-1 5 0 8 1 12 4l2 2-1 1s-1 0-1 1c-1-1-1-2-2-3l-1 1-1-1 1-1h-1-1c-1 1-2 1-3 2v2c0 1-1 2-1 2h-1-1 0c-2-1-3 0-5-2v3l-1 2h1c-1 1-1 2-2 3v2 1l-1-18h2z" class="X"></path><path d="M447 536c-1-1-1 0-2 0v-7h0c0 1 0 2 1 3h0 0l1 1h0v3z" class="W"></path><path d="M453 526h1l1 1c1 0 2 0 3 1v1h0c-1 1-2 1-3 2v-2h-1-1-1c1 0 1-1 2-2l-1-1z" class="G"></path><path d="M447 528c2-2 4-1 6-2l1 1c-1 1-1 2-2 2h0c-1 0-2-1-3 0h-2v-1z" class="J"></path><path d="M446 532c0-1 0-3 1-4v1h2c1-1 2 0 3 0h0 1 1 1v2 2c0 1-1 2-1 2h-1-1 0c-2-1-3 0-5-2h0l-1-1h0z" class="R"></path><path d="M604 321c-1 1-1 3-1 5-1-1-1-2-1-3 1 0 1-1 1-1-1-1-3-3-3-4l-1-1c-1-1-8-1-10-1 3-1 7-1 10-1l-3-2c1 0 2 0 3 1 1-1 0-3 0-4 1 1 1 1 2 1l2-2h0c-1-2 0-3 0-4v-13l1 16c1-1 2-3 3-4h0c0 1-1 3-1 4l-1 1c0 1 1 1 1 2 0 0 0 1 1 1h0c2-2 3-4 5-7h0c0 2-2 5-3 6s-2 2-2 4h1c1-2 2-2 4-3 1-1 2-2 3-2-2 2-4 3-7 5l11 1c-4 1-9 1-13 1-1 2 0 3-2 4z" class="k"></path><path d="M604 321l-3-3v-1c2 0 3 1 4 0h1c-1 2 0 3-2 4z" class="M"></path><path d="M408 233c2 0 3-1 5 0h3 4 1 1l1 1c-1 0-1 0-1 1 0 0 0 1 1 1v2c1 1 1 2 1 3-1 1-3 1-4 1l-11 1-1-1v-2-2-2-1-1-1z" class="m"></path><path d="M423 236v2c1 1 1 2 1 3-1 1-3 1-4 1v-2h2c1-1 1-2 1-4z" class="O"></path><path d="M410 242c0-1 0-1 1-2h2c1 0 2 0 3-1 0-2-1-1-1-3 1 0 2 0 2-1h-1l-1-1h2v1h4c-1 1-3 1-4 1 1 1 2 1 3 1h1c-1 1-2 1-2 2l-1 1h-3c-1 1-2 2-3 2h-2z" class="G"></path><path d="M408 233c2 0 3-1 5 0h3 4 1v1h-4-2l1 1h1c0 1-1 1-2 1 0 2 1 1 1 3-1 1-2 1-3 1h-2c-1 1-1 1-1 2h-2v-2-2-2-1-1-1z" class="d"></path><path d="M408 238h3l-1 2h-2v-2z" class="D"></path><path d="M411 238c1 0 2 0 2 1v1h-3l1-2z" class="V"></path><path d="M408 233c2 0 3-1 5 0h3c-2 1-3 1-4 1h-4v-1z" class="M"></path><path d="M408 235h8c-2 1-5 2-8 1v-1z" class="H"></path><path d="M397 204h18l3 1c2 0 4 0 6 1h4 3 1c1 1 2 0 4 1h0-3-1c-1 0-2-1-2-1h-3-2-5-15-6c-2 0-5 0-7 1-6 0-12 0-18 1-3 1-7 1-10 2-4 0-8 1-12 4l-1 1h-1v-2c3-3 7-4 12-5l15-2 15-2h5z" class="I"></path><path d="M134 536v-2c3-1 5-1 7-1 4-1 9-1 13 0-1 2-1 4-1 6h0-2l-1 1h-3v1h4 1c-5 0-10 0-14 1-1 0-3 1-4 0v-2-4z" class="N"></path><path d="M137 536l-2-1h1c0-1 1-1 2-1h1l-1 3-1-1z" class="K"></path><path d="M137 536l1 1v2 1h-4v-4c0 1 1 2 1 3h0c1-1 0-1 1-2l1-1z" class="W"></path><path d="M140 534h5c1 1 1 2 1 3l-1 1-4-1h-1l2-2-2-1z" class="O"></path><path d="M138 539c4 1 9 0 13 0l-1 1h-3v1h4 1c-5 0-10 0-14 1-1 0-3 1-4 0v-2h4v-1z" class="H"></path><path d="M443 259c1 1 2 1 3 2v2c-1 1-1 1-2 1v1c1 1 1 3 1 5 1 2 2 4 4 5 1 1 1 1 1 2l-9-2-11-2v-1h1c1 1 2 1 4 1-1-1-2-1-4-1 1-1 2 0 3 0h2l1-2-2-2v-2c-1-1-1-2 0-3-1 0-1-1-1-2 1 0 2-1 2-1 1-1 2-1 3 0l1-1 1 1h1l1-1z" class="X"></path><path d="M440 259l1 1 3 3-1 1c1 2 0 4-1 6-1 0-1 1-2 1h-1 0c1-1 1-1 2-1 1-2 0-4 0-6h0v-1c0-1-1-2-2-3l1-1z" class="S"></path><path d="M445 270c1 2 2 4 4 5 1 1 1 1 1 2l-9-2c1 0 2 0 2-1s0-1-1-1v-1l3-2h0z" class="f"></path><path d="M445 270c1 2 2 4 4 5-2 0-3 0-5-1 0-1 1-3 1-4h0z" class="E"></path><path d="M435 263c-1 0-1-1-1-2 1 0 2-1 2-1 1-1 2-1 3 0s2 2 2 3v1h0c0 2 1 4 0 6-1 0-1 0-2 1-1 0-1-1-2-1l-2-2v-2c-1-1-1-2 0-3z" class="b"></path><path d="M435 263h0l3 3 2-1v1 1c-1 0-1 0-2-1-1 0-2 1-3 1v1-2c-1-1-1-2 0-3z" class="f"></path><path d="M435 263c-1 0-1-1-1-2 1 0 2-1 2-1 1-1 2-1 3 0s2 2 2 3v1h0l-1 1-2 1-3-3h0z" class="X"></path><path d="M435 263h1 0 2l1 1 1-1 1 1h0l-1 1-2 1-3-3z" class="K"></path><path d="M183 542l5-1h3c1 1 1 1 1 2h-1-1v1 1c1 0 1 0 2-1 1 0 2 1 3 1v1c-1 1-1 1-2 1h-6-2c-1-1-2-1-2-1h-3-3 0c0 1 0 1-1 1-2 0-3 0-5 1h-1-3-2c0-1 0 0-1-1h-5c-1-1-1-1-2-1v-1l1-1v1l2-1c0 1 1 1 2 1v-1c2 1 4 0 6 0 1 1 6 0 8 1h0v-1h0l-1-1h-5c-1 1-10 0-12 0-1 0-2 0-3-1h-1-7c-1 0-4 1-5 0h-4c4-1 9-1 14-1 2 1 4 1 6 1h8 3 14z" class="b"></path><path d="M169 542h14-2v3l-1-1h-3 0c-1-1-1-1-2-1-2-1-4 0-6-1z" class="j"></path><path d="M157 545c1 0 2 0 4 1v-1h4 10l1 1h1 3-3 0c0 1 0 1-1 1-2 0-3 0-5 1h-1-3-2c0-1 0 0-1-1h-5c-1-1-1-1-2-1v-1z" class="T"></path><path d="M417 433v-1l1 1v-2l1 1h0v3c2 1 7 1 9 1h4 0l-2 1v1 1h3 0v1h-1v1h0v1h-3v1c1 0 3 0 3 1l1 2h-2 0c-2 0-4-1-6 0h-2l-3-1c1 0 1 0 1-1h0l-1-1-1-1 1-1-1-1h-4-6l-1-1h0 4v-1c1 0 2 0 3-1h-3 0l-1-1-1-1c1 0 2 0 3-1h-1c2-1 3 0 5-1h0z" class="n"></path><path d="M413 434l1 1h0c0 1-1 1-2 2l-1-1-1-1c1 0 2 0 3-1z" class="S"></path><path d="M417 433v-1l1 1v-2l1 1c0 1 0 3-1 4-1 0-2 0-3-1l1-1h1v-1h0z" class="X"></path><path d="M415 437l1 1h5 1c-1 1-1 1-2 1h-7-5 4v-1c1 0 2 0 3-1z" class="G"></path><path d="M408 439h5 7 3c1 1 3 0 5 1h5-1v1h0-1c-3-1-6 0-8 0-1 0-2 1-3 2l-1-1 1-1-1-1h-4-6l-1-1h0z" class="b"></path><path d="M420 443c1-1 2-2 3-2 2 0 5-1 8 0h1v1h-3v1h0c-2 0-5-1-6 0v1 2l-3-1c1 0 1 0 1-1h0l-1-1z" class="n"></path><path d="M429 442h-6v-1h0 8 1v1h-3zm-6 4v-2-1c1-1 4 0 6 0h0c1 0 3 0 3 1l1 2h-2 0c-2 0-4-1-6 0h-2z" class="h"></path><path d="M534 618c1 0 2-1 3-1v1c-2 2-5 3-7 5-3 1-7 3-10 4-6 2-12 4-19 5-4 1-9 2-13 3h-1l1-1v-1h0c2 0 4-1 6-1 0-1 1-3 2-4 0 0 1-1 2-1l1 1h2c1 1 1 1 1 2l3-1c-1 0-1-1-1-1 1-2 3-2 4-3h4c4-1 11-1 14-4 0-1 0-1 1-1l1 1 6-3z" class="I"></path><path d="M499 628h2c1 1 1 1 1 2l-7 2v-1c3-1 3-1 4-3z" class="n"></path><path d="M498 627l1 1c-1 2-1 2-4 3v1h-1c0-1 1-3 2-4 0 0 1-1 2-1z" class="N"></path><path d="M526 621c0-1 0-1 1-1l1 1c-7 4-15 6-23 8-1 0-1-1-1-1 1-2 3-2 4-3h4c4-1 11-1 14-4z" class="W"></path><path d="M392 428c-1-2 0-5-1-7h-1c-1-1-1-5-1-7 1-1 1 0 2-1v-1c-1 0-1 0-2-1v-3c2-1 5 0 6 0 4-1 9 0 12 0 2 0 5-1 6 0-1 0-1 0-2 1h-1c0 1-1 1-1 1h-1c-1 1-3 1-5 1h0v1h-6-1v4l-1 1v1 13 1h-1-1-1v-4z" class="m"></path><path d="M391 408h3v3h-3v-3z" class="I"></path><path d="M392 419v-6h0 2c1 1 0 3 0 4 0 0 0 1-1 1l-1 1zm0 7h0c1 1 3 1 3 2v2 1 1h-1-1-1v-4-2z" class="C"></path><path d="M393 418c1 0 1 0 1 1 1 1 1 1 0 2h-1-1c1 1 2 1 2 1 1 1 1 3 1 4h-3 0c-1-1 0-3 0-4v-3l1-1z" class="I"></path><path d="M396 410l-1-2h1c2 0 8-1 10 1h0c1 0 1 0 2 1-1 1-3 1-5 1h0 0-7v-1z" class="D"></path><path d="M396 410c0-1 1 0 2 0s3-1 5 0v1h-7v-1zm240 110h2c0 3-1 6-2 9-2 6-8 10-13 14-2 2-5 4-6 6-3 4-4 8-6 11-3 5-7 9-11 13s-8 9-13 12c-4 3-7 6-12 8v-1l6-4c7-6 14-12 20-20a30.44 30.44 0 0 0 8-8c2-4 3-8 6-12 2-2 5-4 7-6 3-3 7-5 9-8 3-4 5-9 5-14z" class="M"></path><path d="M404 85c1-1 1-2 1-2 0-2-1-2 0-4h0 1v5c0 1 2 2 2 3 1 2 1 4 1 6 0 1 0 0 1 1v4h0 0c1 1 2 2 2 3h1v5h0c-1 0-1-1-1-1v-1-1h0c0-2-1-3-2-4-1 0-1 0-3 1l2 2c-1 1-3 1-4 1h0-6v3c-1 1-1 1-1 2l-1-2c-1-1-1-4 0-5l3-3c1-1 1-3 1-4v-2c1-2 1-3 2-5l1-1v-1h0z" class="f"></path><path d="M399 106l-2-1v-1c0-2 1-2 2-3h0v2 3z" class="K"></path><path d="M399 101c2 0 3-1 4-1 1 1 1 1 2 3h-6v-2z" class="R"></path><path d="M403 100h4l2 2c-1 1-3 1-4 1h0c-1-2-1-2-2-3z" class="G"></path><path d="M403 87l1-1v-1h0c1 0 1 1 2 2h-1 0v3 6l-1 1h0c-1-3-1-6 0-8l-1-2z" class="d"></path><path d="M404 85c1-1 1-2 1-2 0-2-1-2 0-4h0 1v5c0 1 2 2 2 3h0v1c-1 3 0 6-1 9l-1-1v-9c-1-1-1-2-2-2z" class="D"></path><path d="M483 539c1 0 1 1 2 2l2 4c4 0 6 0 10-1 1 0 3 1 4 1s1 0 2-1c6-1 11-8 17-10 1-1 3 0 4 0v1h0 5v1h0l1 1-5 4h-3l-1 1h-1c-1-1-1-2-2-4-3 4-7 5-11 7h-2l-3 1v2h-1-1-6l-5 1-1-1h-2c-1-3-3-5-4-8l1-1z" class="L"></path><path d="M488 548h0 1c4-2 9-2 13-2v2h-1-1-6l-5 1-1-1z" class="c"></path><path d="M524 535h5v1h0c-3 1-5 1-8 0-1 1-2 2-3 2-3 4-7 5-11 7h-2c3-1 6-3 8-5 3-1 5-4 8-5h3z" class="Y"></path><path d="M529 536l1 1-5 4h-3l-1 1h-1c-1-1-1-2-2-4 1 0 2-1 3-2 3 1 5 1 8 0z" class="Z"></path><path d="M518 538c1 0 2-1 3-2 1 1 1 2 2 3l1 1-2 1-1 1h-1c-1-1-1-2-2-4zm-242-80c1 0 2 0 3-1h1 1 1c1 2 1 3 1 4l1 1v2l6 1v1 9 8h-4v-1-2h0l-2-1v-1h-2v-4h0c0-2 0-6 1-8h0v-1h-1v-1l-2-1-1 1h-1 0-5c1-1 1-3 2-4l1-2z" class="B"></path><path d="M283 462c0-1 0-2-1-3v-2c1 2 1 3 1 4l1 1v2l6 1v1h-2c-2 3-1 11-1 14l1 2h-1c0-1 0-2-1-2l-2-1v-1h2 0v-8-4h-3 0v-1h-1v-1h0c1-1 1-1 1-2z" class="W"></path><path d="M283 466h3v4 8h0-2-2v-4h0c0-2 0-6 1-8z" class="D"></path><path d="M276 458c1 0 2 0 3-1h1 1 1v2c1 1 1 2 1 3s0 1-1 2h0l-2-1-1 1h-1 0-5c1-1 1-3 2-4l1-2z" class="L"></path><path d="M279 461l2 1h0 2c0 1 0 1-1 2h0l-2-1-1 1h-1c0-2 0-2 1-3z" class="C"></path><path d="M275 460h0 3 1v1h0c-1 1-1 1-1 3h0-5c1-1 1-3 2-4z" class="D"></path><path d="M167 586c3 1 7 1 10 1 0 2-1 2-3 3l-3 1c2 1 5 1 7 1h1 2c-1 1-4 0-6 0h-1v1c-1 1-3 0-4 0h-8-6-16v-1c-1-1-6-1-8-1l1-1h0 1 6c1 0 2 0 3-1 1 0 1 0 2-1h2 2 6v-1h2c1 1 1 1 2 1h3 1v-1h3l1-1z" class="P"></path><path d="M157 591l7 1h-3c-2 1-4 0-6 0-1 0-1 0-2-1h0 4z" class="i"></path><path d="M155 587h2c1 1 1 1 2 1h3c-2 2-4 1-7 2h0v1h0 2-4l-13-1c1 0 2 0 3-1 1 0 1 0 2-1h2 2 6v-1z" class="d"></path><path d="M167 586c3 1 7 1 10 1 0 2-1 2-3 3l-3 1h-4l-3 1-7-1h-2 0v-1h0c3-1 5 0 7-2h1v-1h3l1-1z" class="C"></path><path d="M162 588h1 2c-2 1-4 1-5 2 1 2 5 1 7 1l-3 1-7-1h-2 0v-1h0c3-1 5 0 7-2z" class="D"></path><path d="M167 586c3 1 7 1 10 1 0 2-1 2-3 3l-1-1c-3 0-5 0-8-1h-2v-1h3l1-1z" class="V"></path><path d="M605 432c3-2 6-4 10-5l1 1-1 1c-1 0-2 1-4 1-4 1-7 5-10 9 0 1 1 1 1 2l-2 3-2 3-1-2c-1 1-2 2-2 4-1 1-3 2-4 2v1c-2 2-3 3-4 5-2 2-2 4-4 4l-1 1-3 11c0 2-3 3-4 5 0 2 0 4-1 6 0 1 1 4 0 5 0 1-2 2-2 2l-7 6c-1 2-3 4-4 7-1 0-1 0-1-1 2-4 5-7 9-11l4-4c1-3 0-7 1-10v-1c1-3 4-5 5-8s1-6 1-9c1-1 2-3 2-4 2-2 4-5 6-6 2-2 4-2 6-3l3-3c1-2 2-5 4-8l4-4z" class="p"></path><path d="M601 439c0 1 1 1 1 2l-2 3-2 3-1-2 4-6z" class="H"></path><path d="M582 462c1-5 4-9 8-12 1-1 2-2 4-2 0 1-2 2-3 3v1c-2 2-3 3-4 5-2 2-2 4-4 4l-1 1z" class="J"></path><path d="M530 532c6-2 10-9 17-11 2 0 4 0 6-1 1-2 3-4 4-6 1-3 0-8 0-11l1 2 1-1v8l1 1-3 6c-1 2-2 5-4 6h1c-1 1-2 2-3 2v1h-1l-8 7c-1-2 0-2 0-3h0l-2 1c-1-1-1-2-2-2l-4 3c-1 1-2 2-3 2h-2v-1l2-1 1-1-2-1z" class="N"></path><path d="M531 534l8-5c2-2 5-5 7-6 3 0 6 0 8-1 2-2 4-7 5-10l1 1-3 6c-1 2-2 5-4 6h-7c-1 1-3 2-4 3s-2 2-4 3l-4 3c-1 1-2 2-3 2h-2v-1l2-1z" class="W"></path><path d="M542 528c1-1 3-2 4-3h7 1c-1 1-2 2-3 2v1h-1l-8 7c-1-2 0-2 0-3h0l-2 1c-1-1-1-2-2-2 2-1 3-2 4-3z" class="e"></path><path d="M542 528c1-1 3-2 4-3h7 1c-1 1-2 2-3 2s-2 0-3 1c-2 0-3 3-5 3-1-1-1-2-1-3z" class="N"></path><path d="M409 220h9 0c1 1 2 1 4 1 1 1 1 2 2 3l-1 1v3c0 2 0 5 1 7v3h-1v-2c-1 0-1-1-1-1 0-1 0-1 1-1l-1-1h-1-1-4-3c-2-1-3 0-5 0v-1-2-5-3h-1c-4-1-7-1-10-1 0 0 1 0 2-1 2-1 7 0 10 0z" class="U"></path><path d="M420 225v-1h1c0-1 0-1 1-1 1 2 1 3 1 5s0 5 1 7v3h-1v-2c-1 0-1-1-1-1 0-1 0-1 1-1l-1-1h-1-1c0-1 0-1 1-1h1v-1l-1-1h1v-1c0-2-1-3-2-4z" class="T"></path><path d="M410 228c2 0 4 0 5 1v1h-6 0c4 1 7 1 10 2h-6v1c-2-1-3 0-5 0v-1-2c0-1 0-1 1-2h1z" class="D"></path><path d="M414 224c1 1 1 1 2 1 1 1 2 1 3 2l2 3h-6v-1c-1-1-3-1-5-1h-1c-1 1-1 1-1 2v-5h3c2-1 2-1 3-1z" class="F"></path><path d="M411 225c2 0 4 1 6 2-2 0-5 0-7 1h-1c-1 1-1 1-1 2v-5h3z" class="V"></path><defs><linearGradient id="D" x1="420.279" y1="224.749" x2="407.594" y2="219.816" xlink:href="#B"><stop offset="0" stop-color="#7e7e7d"></stop><stop offset="1" stop-color="#999799"></stop></linearGradient></defs><path fill="url(#D)" d="M409 220h9 0c1 1 2 1 4 1 1 1 1 2 2 3l-1 1v3c0-2 0-3-1-5-1 0-1 0-1 1h-1v1h-1c-1 0-1 0-2-1h-1v-2h-5v1c1 1 2 1 3 1-1 0-1 0-3 1h-3v-3h-1c-4-1-7-1-10-1 0 0 1 0 2-1 2-1 7 0 10 0z"></path><path d="M416 222c2 0 4 0 6 1h0c-1 0-1 0-1 1h-1v1h-1c-1 0-1 0-2-1h-1v-2z" class="P"></path><path d="M414 47c5-1 9-1 12-4 1-2 3-3 4-5 0 2-1 3-2 4 0 3 1 8-1 10-4 4-10 4-14 6-2 0-4 1-6 2v-1c1-1 1-2 1-4h1 0v-7c1-1 1 0 2-1v-1c1 1 2 1 3 1h0z" class="k"></path><path d="M409 55v-7c1-1 1 0 2-1l2 1h1c1 0 2 1 2 2v2 1h0c-1 1-1 1-2 1s-1-1-2 0c-1 0-2 0-3 1z" class="D"></path><path d="M124 553c1-2 2-3 3-4s3-1 4-1c3 0 6 1 8 3v6l-2 1c0 2-1 2-2 4h-1v-1-2h-1c0 1-1 1-1 2l-1 1-1-1v-1l1-1 2-3c-1 1-3 2-4 3h0l-1 2c-1 1-2 2-4 3-1 0-1 0-2-1h-1v1c-1 1-1 3-2 3-1 1-1 1-1 2-1-4 1-6 2-9s2-5 4-7z" class="M"></path><path d="M124 553h1v-1l1-1 2 2v2c-1 2-2 3-3 5v1h1c1-1 1 0 2 0-1 1-2 2-4 3-1 0-1 0-2-1h-1v1c-1 1-1 3-2 3-1 1-1 1-1 2-1-4 1-6 2-9s2-5 4-7z" class="C"></path><path d="M120 560c0 1 1 2 1 3h1c3-2 3-7 5-9 0 0 0 1 1 1-1 2-2 3-3 5v1h1c1-1 1 0 2 0-1 1-2 2-4 3-1 0-1 0-2-1h-1v1c-1 1-1 3-2 3-1 1-1 1-1 2-1-4 1-6 2-9z" class="G"></path><path d="M444 296v-1-2l2-1v2h2 2v11 37 11c0 1 0 3 1 4l3 2v1l-6-2v-17c0-1 0-1-1-1v2-2h-3l-1-1h0c-1-1 0-3 0-4 1-1 2 0 4 0v-1-2-8-3-4-1-2l-1-3 1-2-1-3c1-2 1-4 1-7-1-1-2-2-3-2v-1h0z" class="D"></path><path d="M444 296v-1-2l2-1v2h2v1 2 5 27 12c0-1 0-1-1-1v2-2h-3l-1-1h0c-1-1 0-3 0-4 1-1 2 0 4 0v-1-2-8-3-4-1-2l-1-3 1-2-1-3c1-2 1-4 1-7-1-1-2-2-3-2v-1h0z" class="b"></path><path d="M444 296v-1-2l2-1v2c1 1 1 2 0 3 0 0-1-1-2-1z" class="J"></path><path d="M447 335v4h-3-1 0c-1-1 0-3 0-4 1-1 2 0 4 0z" class="D"></path><path d="M615 427c1 0 2 0 3 1s2 3 2 4-1 2-1 2l-1 3v3-1h0c0 2-1 5-3 7v-1c0-1 1-3 2-4l-1-1v1h-1c0 1 0 2-1 3l-3 3c-1 0-1 0-2-1h-1c-1 0-1 1-2 2h-2 0c0-1 0-2 1-2v-2h0l-1 1h-1v-1-2-1c1 0 1-1 1-1 1-1 1-1 1-2l-3 3c0-1-1-1-1-2 3-4 6-8 10-9 2 0 3-1 4-1l1-1-1-1z" class="C"></path><path d="M615 427c1 0 2 0 3 1s2 3 2 4-1 2-1 2l-1 3c-2 0-3 0-4-1v-2c0-1 1-1 1-2l1-1h0c-1 0-1 0-1 1-2 1-3 2-5 2 1-1 4-4 5-4h1v-1h-1l1-1-1-1z" class="b"></path><path d="M614 436v-2c0-1 1-1 1-2l1 2h1 2l-1 3c-2 0-3 0-4-1z" class="q"></path><path d="M608 440l2-1c1 0 1-1 2-1l2-2c1 1 2 1 4 1v3-1h0c0 2-1 5-3 7v-1c0-1 1-3 2-4l-1-1v1h-1c0 1 0 2-1 3l-3 3c-1 0-1 0-2-1h-1c-1 0-1 1-2 2h-2 0c0-1 0-2 1-2v-2h0l-1 1h-1v-1-2-1c1 0 1-1 1-1 1-1 1-1 1-2 1 0 1-1 2-2v1s-2 1-2 2l1 2h0l1 1v-1s1 0 1-1z" class="H"></path><path d="M611 441l1 1v-1c1 0 1-1 2-1h1v1c0 1 0 2-1 3v-2h-2l-1 1-1-1 1-1z" class="Z"></path><path d="M608 440l2-1c1 0 1-1 2-1l2-2c1 1 2 1 4 1v3-1h0c0 2-1 5-3 7v-1c0-1 1-3 2-4l-1-1v1h-1v-1h-1c-1 0-1 1-2 1v1l-1-1 1-1c1 0 1-1 1-1v-1l-1 1h-1l-1 1c-1 1-1 1-2 0z" class="Q"></path><path d="M561 504c1-3 3-5 4-7l1 1v3h3 0v2h0l1 1h1c-1 2-2 3-2 4v2h-1v1l-1 1-1 6c-1 1-2 2-2 3v1h0c1 0 0 0 1 1h0l-4 4c-1-1-1-1-3-1h0c-1 1-3 2-5 3 0-1-1-1-2-1v-1c1 0 2-1 3-2h-1c2-1 3-4 4-6l3-6-1-1v-8l1-1c0 1 0 1 1 1z" class="D"></path><path d="M566 507h2l1 1v2h-1v1l-1 1-1-1h0v-3-1z" class="H"></path><path d="M560 513l1-4c2-1 3-2 4-3l1 1-1 1s-1 0-2 1c1 1 2 0 2 2v1c-1 0-1 0-2-1h-1v4c-1 0-1 1-1 1-1 1 0 1-1 2 0 0-1 1-1 2-1 0-1-1-2-1l3-6z" class="L"></path><path d="M562 515v2 2h-1v1h1 0c1 1 1 2 1 3h1 1l-4 4c-1-1-1-1-3-1h0c-1 1-3 2-5 3 0-1-1-1-2-1v-1c1 0 2-1 3-2h-1c2-1 3-4 4-6 1 0 1 1 2 1 0-1 1-2 1-2 1-1 0-1 1-2 0 0 0-1 1-1z" class="T"></path><path d="M557 519c1 0 1 1 2 1 0 1 0 1-1 2v1c-1 1-1 2-2 3h-1v-1h-1-1c2-1 3-4 4-6z" class="D"></path><path d="M561 504c1-3 3-5 4-7l1 1v3h3 0v2h0l1 1h1c-1 2-2 3-2 4l-1-1h-2v1h-1l1-1-1-1c-1 1-2 2-4 3l-1 4-1-1v-8l1-1c0 1 0 1 1 1z" class="Y"></path><path d="M569 503h0l1 1h1c-1 2-2 3-2 4l-1-1h-2 1c0-1-1-1-1-2 1 0 3-2 3-2z" class="D"></path><path d="M561 504c1-3 3-5 4-7l1 1v3h3 0l-1 1-2 2h-1c-1 1-2 3-4 4v-3c2 0 2 0 3-2 0-1 1-2 1-3-1 1-2 4-3 4h-1z" class="L"></path><path d="M144 550v-1c1-1 4-1 6-1h1c1 1 0 1 1 2h1v2 8c0 3 0 6 1 9v4l-1 2h-1-1-6c-1-1 0-2 1-4 0-1-1-1-1-1h-1v-3-2-2-1-1-2-4-3h0v-2z" class="Z"></path><path d="M144 561c1-1 2-1 3-1v1 1h-3v-1z" class="L"></path><path d="M144 567c1 0 2 0 3-1v3c-1 0-1 0-2 1h-1v-3z" class="H"></path><path d="M144 562h3v1h0c0 1 0 1 1 2h-4v-2-1z" class="B"></path><path d="M144 550l1-1c0 1 1 1 1 1 1 0 2-1 3-1 0 2 0 2-1 3h-4 0v-2z" class="C"></path><path d="M153 560h-2v-4-1-2l1-1h1v8z" class="Q"></path><path d="M144 552h4v2h1l1-1c1 1 1 1 1 2l-1 1c0-1-1-1-1 0l-1 2-1 1h-3v-4-3z" class="H"></path><path d="M144 565h4 3c1 1 1 2 1 3s-1 1-1 1c-1 1-1 3-2 4h-1-1c0-2 1-5 0-7-1 1-2 1-3 1v-2z" class="Q"></path><path d="M472 532c3 1 6 3 9 6l1 2c1 3 3 5 4 8h2l1 1c-1 0-2 0-3 1 0 1 0 2 1 2h-1-2v2c1 1 1 1 1 2l-2 2h-1-1c1-1 1-1 1-2l-1-1h-2-1l-2 2h-2v1h-1c-1 0-2-1-2-2h-3v-1l1-1v-1l4-3h1v-1h0l1 1c1-2 1-4 1-6h0c0-2 0-3-2-4v-1l-1-1-1-1c-1-1-1-1-2-1h0l-1-2h-3 5v-1l1-1z" class="B"></path><path d="M482 551c1 0 1 0 2 1v2c1 1 1 1 1 2l-2 2h-1-1c1-1 1-1 1-2l-1-1h-2l1-2h1v1h2v-1l-1-2z" class="V"></path><path d="M481 548h2 0c1 1 1 2 1 4-1-1-1-1-2-1 0 1 0 1-1 1h-1l1-1h-2s0-1-1-2h1c1 0 1 0 2-1z" class="M"></path><path d="M481 548h2 0c-1 0-1 0-2 1v2h-2s0-1-1-2h1c1 0 1 0 2-1z" class="N"></path><path d="M479 551c-1 1-2 3-3 4-1-1 0-2 1-3 0-1-1-3-1-4v-1c1-1 1-1 1-2h0 2 0 1l-2 2c1 0 1 1 0 2h0c1 1 1 2 1 2z" class="M"></path><path d="M479 539h1c1 1 1 2 2 3-1 1-2 2-3 2h0-3 0c0-2 0-3-2-4h1l1-1h3z" class="E"></path><path d="M480 539c1 1 1 2 2 3-1 1-2 2-3 2h0s-1-2 0-3c0-1 1 0 1-2z" class="m"></path><path d="M472 532c3 1 6 3 9 6l1 2c1 3 3 5 4 8h2l1 1c-1 0-2 0-3 1 0 1 0 2 1 2h-1-2c0-2 0-3-1-4h0-2c0-1 1-2 1-3 1-1 0-2 0-3-1-1-1-2-2-3h-1-3l-1 1h-1v-1l-1-1-1-1c-1-1-1-1-2-1h0l-1-2h-3 5v-1l1-1z" class="W"></path><path d="M466 534h5c2 0 6 1 7 3v1h-2v1c2 0 2-1 3 0h-3l-1 1h-1v-1l-1-1-1-1c-1-1-1-1-2-1h0l-1-2h-3z" class="G"></path><path d="M165 548h2l-1 1v1h1 1v4 1h1c1 0 1 0 1 1h-2c-1 1-2 1-2 1-3 1-4 3-5 5s-1 5-1 6v6l1 1c1 1 1 0 2 1v2 2c0 1 1 1 0 2s-2 0-1 2l-2 1c-2 0-6 0-8-1 0 0 1 0 1-1h0l1 1c1 0 1 0 2-1h0v-1c-1-2 0-13 0-16h-1c1-1 1-2 1-3 1-1 0-2-1-2 0-1 1-2 1-2v-4-2l1-1c-1-1-1-2-1-3h1 3 3c1-1 1-1 2-1z" class="h"></path><path d="M158 578c0-4-1-9 0-12 1-2 1-3 2-4h1c-1 2-1 5-1 6l-1 10h-1z" class="o"></path><defs><linearGradient id="E" x1="156.799" y1="579.904" x2="162.836" y2="578.736" xlink:href="#B"><stop offset="0" stop-color="#272726"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#E)" d="M156 559v-4-2l1 3-1 2c1 0 1 1 1 2h0 1c0 1-1 2-1 3s0 2-1 3c0 1 0 2 1 3 0 3-1 6 0 10v1 3c1-2 1-3 1-5h1l1-10v6l1 1c1 1 1 0 2 1v2 2c0 1 1 1 0 2s-2 0-1 2l-2 1c-2 0-6 0-8-1 0 0 1 0 1-1h0l1 1c1 0 1 0 2-1h0v-1c-1-2 0-13 0-16h-1c1-1 1-2 1-3 1-1 0-2-1-2 0-1 1-2 1-2z"></path><path d="M158 578h1c0 2 0 4-1 5h-1c1-2 1-3 1-5z" class="U"></path><path d="M165 548h2l-1 1v1h1 1v4c-2 0-3 0-4 1-2 2-5 4-5 7-1 0-1 0-1 1h-1c0-1 1-2 1-3h-1 0c0-1 0-2-1-2l1-2-1-3 1-1c-1-1-1-2-1-3h1 3 3c1-1 1-1 2-1z" class="i"></path><path d="M167 550h1v4c-2 0-3 0-4 1-1 0-2 1-3 1l-1-1v-1h2l1-1 1-1h1 0l2-2z" class="d"></path><path d="M165 548h2l-1 1v1h1l-2 2h0-1 0 0v-1h-3-1v-2h0 3c1-1 1-1 2-1z" class="G"></path><path d="M166 550c-1 0-1 1-2 1v-2h2v1z" class="J"></path><path d="M156 553l1-1c-1-1-1-2-1-3h1 3 0v2h1c-1 1-1 2-1 2l-1 1h-1l-1 1c0 1 0 1 1 2-1 0-1 0-2 1l1-2-1-3z" class="l"></path><path d="M160 549v2h1c-1 1-1 2-1 2-1 0-2 0-2-1v-1h1c0-1 0-1 1-2z" class="o"></path><path d="M419 252h1c1 1 3 1 4 2h3c1 0 2 1 3 1h3c2 1 4 1 5 1 1 1 2 3 3 3l1 1h-1l-1-1-1 1c-1-1-2-1-3 0 0 0-1 1-2 1 0 1 0 2 1 2-1 1-1 2 0 3v2l2 2-1 2h-2l-5-1h-1l-1-2v1h-3v-1c0-1-1-1-1-2l-1-1v-4c0-1 1-1 0-2-1 0-1 0-2 1l-1-1h-1c-1 2 0 3-1 5v-4l-1-1h-1l-1-2c1 0 2 0 3-1h0v-2-1l-1-1h-1c2-1 3-1 4-1z" class="K"></path><path d="M427 269c2 0 2-1 3-2h1l-1 2c-1 0-1 1-1 2h-1l-1-2z" class="P"></path><path d="M430 269h1 1v1c1 1 2 1 3 1 0-2-2-3-2-4-1-1-1-2-1-3 0-3 2-4 3-5 2-1 3-1 5 0l-1 1c-1-1-2-1-3 0 0 0-1 1-2 1 0 1 0 2 1 2-1 1-1 2 0 3v2l2 2-1 2h-2l-5-1c0-1 0-2 1-2z" class="h"></path><path d="M422 260v-1c1 0 1 0 1 1l2-1h1 1s1 1 1 2h-1l1 1c1 0 1-1 2-1l1 2h-1v2 1l-2 1-1 1h-2l-1 1 3 1h-3v-1c0-1-1-1-1-2l-1-1v-4c0-1 1-1 0-2z" class="T"></path><path d="M425 264v-2c1 1 1 1 2 1 0 1 1 1 2 2v1h1l-2 1-3-3z" class="a"></path><path d="M422 260v-1c1 0 1 0 1 1l2-1h1l-1 3v2c-1-1-2-1-3-2 0-1 1-1 0-2z" class="K"></path><path d="M422 262c1 1 2 1 3 2l3 3-1 1h-2l-1 1 3 1h-3v-1c0-1-1-1-1-2l-1-1v-4z" class="X"></path><path d="M423 267v-1l1-1v1c1 1 2 1 3 2h-2l-1 1 3 1h-3v-1c0-1-1-1-1-2z" class="g"></path><path d="M417 255c1 0 1 1 3 1h1 1c2 0 5 1 6 0h0 1l1 1h1l1 1c-1 1-2 1-2 3-1 0-1 1-2 1l-1-1h1c0-1-1-2-1-2h-1-1l-2 1c0-1 0-1-1-1v1c-1 0-1 0-2 1l-1-1h-1c-1 2 0 3-1 5v-4l-1-1h-1l-1-2c1 0 2 0 3-1h0v-2z" class="W"></path><path d="M423 257h3c1 0 1 1 1 2h-1-1v-1c-1-1-1-1-2-1z" class="j"></path><path d="M417 257c0 1 1 2 0 3v1l-1-1h-1l-1-2c1 0 2 0 3-1h0z" class="f"></path><path d="M419 260v-3c2-1 2 0 4 0 1 0 1 0 2 1v1l-2 1c0-1 0-1-1-1v1c-1 0-1 0-2 1l-1-1z" class="h"></path><path d="M143 446l-1-11h1v1c1 4 5 8 8 12h0l1-1v2h1v1 2 2c1 1 1 1 1 3h0v3c-2 3-2 7-2 10 1 4 4 7 4 11h0v1c-1-1-1-2-2-3h0l-1-1c0-1-1-2-2-2-1-1-2-1-3-1h0-1v-2c0-2-1-2-2-3h0l-1-1h0c-1-2-1-3-1-5s1-4 2-6c0-2 0-4 1-7h0c0-2-1-3-1-5v-2l-2-3v5h0z" class="B"></path><path d="M146 451v3 2 4h1c1-1 1-3 1-5l-1-1h1 1c1 2-1 5 0 7h1v-3-2l1-1v6s-1 0-1 1c-2 1-4 2-4 4h-1v3 1h0l-1-1h0c-1-2-1-3-1-5s1-4 2-6c0-2 0-4 1-7z" class="K"></path><path d="M150 465c1-2 3-5 2-7v-2c0-1 0 0 1-1v2h1v3c-2 3-2 7-2 10 1 4 4 7 4 11h0v1c-1-1-1-2-2-3h0l-1-1c0-1-1-2-2-2-1-1-2-1-3-1h0-1v-2c0-2-1-2-2-3v-1-3h1c0-2 2-3 4-4v3z" class="f"></path><path d="M145 470v-1-3h1c0-2 2-3 4-4v3l-2 10h0-1v-2c0-2-1-2-2-3z" class="k"></path><path d="M421 409h1c3 0 7 1 10 1v5c-1 1-1 1-2 1v-1h-1v1 11c0 2-1 4 0 6v1 1h0l-1 1c-2 0-7 0-9-1v-3-4-1-17h0c1-1 1-1 2-1z" class="h"></path><path d="M421 409h1v1c0 1 0 1-1 1h0l-2-1h0c1-1 1-1 2-1z" class="O"></path><path d="M419 410l2 1-1 3 2 1-2 2c1 1 1 1 1 2h0s0 1-1 1l1 1h1v1h-1l-1 1c1 0 1 1 1 1v1c0 1 1 1 1 1v1h-3v-17z" class="J"></path><path d="M419 428c1 0 2 0 2 1h1l-2 1c0 1 1 1 1 2l-1 1v1h2c2 0 5-1 7 1h0l-1 1c-2 0-7 0-9-1v-3-4z" class="D"></path><path d="M422 434c0-1 0-1 1-2s0-19 0-21c2 0 3 0 5 1 1 0 2-1 3-1v1c-1 1-1 2-2 2-1 1 0 1 0 2v11c0 2-1 4 0 6v1 1c-2-2-5-1-7-1z" class="p"></path><path d="M439 306v-2h1c1 0 1-1 2-2v2c1 1 3 0 4 2h0l1 3-1 2 1 3v2 1 4 3 8 2 1c-2 0-3-1-4 0 0 1-1 3 0 4h-2c-2-3 0-2 0-4 0 0-1 0-1-1h1v-1-1h-1v-4-1h-1 0-1c1-2 1-3 0-4 0-1-1-2-2-3-1-2-3-2-4-2v-1c1-1 1-2 1-3s0-1 1-2h3l-1 1v1h1v1c1-1 0-1 1-1v-2l1-1c1-1 0-4 0-5z" class="K"></path><path d="M441 329c0-3-1-8 1-11 0 1 1 2 1 3h-1c0 1 0 1 1 1v3l-2 2c1 1 1 1 0 2z" class="S"></path><path d="M433 314c0-1 0-1 1-2h3l-1 1v1h1v1 1l-3-1h0v1l1 1h-3c1-1 1-2 1-3z" class="l"></path><path d="M442 318c0-1 0-1 1-1h4v4h-1c0 1 0 1-1 1l-1 1-1-1c-1 0-1 0-1-1h1c0-1-1-2-1-3h0z" class="J"></path><path d="M443 322c0-1 0-2 2-2 1 0 1 0 1 1s0 1-1 1l-1 1-1-1z" class="H"></path><path d="M443 325v3 1 3 2 1c0 1-1 3 0 4h-2c-2-3 0-2 0-4 0 0-1 0-1-1h1 1v-1c0-1-1-2-1-3v-1c1-1 1-1 0-2l2-2z" class="F"></path><path d="M446 321h1v3 8 2 1c-2 0-3-1-4 0v-1-2-3-1-3-3l1 1 1-1c1 0 1 0 1-1z" class="Q"></path><path d="M447 334c-1 0-2 0-3-1v-1h0 3v2z" class="H"></path><path d="M446 321h1v3c-1 1-1 2-1 3h-1c0 1-1 1-1 0-1-1 0-3 0-4l1-1c1 0 1 0 1-1z" class="Z"></path><path d="M439 306v-2h1c1 0 1-1 2-2v2c1 1 3 0 4 2h0l1 3-1 2 1 3v2 1h-4c-1 0-1 0-1 1 0-1-1-1-1-2h0l-2 1v-2c-1-1 0-1 0-2v-2c1-1 0-4 0-5z" class="f"></path><path d="M444 309v-1c1 0 2 0 3 1l-1 2h-3 2l-1-2z" class="D"></path><path d="M442 311h1 3l1 3v2c-1 1-4 0-5 0v-5z" class="J"></path><path d="M442 311h1 3l1 3-3-1-2-2z" class="B"></path><path d="M439 306v-2h1c1 0 1-1 2-2v2c1 1 3 0 4 2h0l1 3c-1-1-2-1-3-1v1s-1 1-2 1v-1c-1 1 0 1-1 2h-2c1-1 0-4 0-5z" class="X"></path><path d="M442 309v-4c1 0 3 0 4 1h0l1 3c-1-1-2-1-3-1v1s-1 1-2 1v-1z" class="Z"></path><path d="M418 220l8 1h4 4c0 1 2 1 3 1-1 1 1 13 2 14v2c0 1 0 3 1 5v1 1 1h-1-1l1-1v-1h-5l-1-1h-2c-2 0-4 0-6-1 0-1-1-3-1-4h0v-3c-1-2-1-5-1-7v-3l1-1c-1-1-1-2-2-3-2 0-3 0-4-1h0z" class="Y"></path><path d="M425 226h2c1 1 2 1 3 1h3v1h-7l-1 1v-1-2z" class="O"></path><path d="M430 221h4v4s0 1-1 1c1 1 2 1 1 2h-1v-1h-3c1-1 1-2 2-2v-1-1c-1 0-2 0-2-1v-1z" class="f"></path><path d="M418 220l8 1h4v1c0 1 1 1 2 1v1 1c-1 0-1 1-2 2-1 0-2 0-3-1h-2l-1-2c-1-1-1-2-2-3-2 0-3 0-4-1h0z" class="T"></path><path d="M418 220l8 1h2c-1 1-2 1-3 1h0c2 1 4 0 5 2h-1-5c-1-1-1-2-2-3-2 0-3 0-4-1h0z" class="l"></path><path d="M433 236c2 0 2 0 3 1h0c0 1 0 2 1 3-1 0-1 0 0 1v2h-4-2c-2 0-4 0-6-1 0-1-1-3-1-4h1 1v-1h1c2 0 3 1 4 0h3l-1-1z" class="h"></path><path d="M424 238h1 1 0l1 1h-1v3h-1c0-1-1-3-1-4z" class="o"></path><g class="l"><path d="M426 238c3 0 6 0 8 1v1l-8-1h1l-1-1z"></path><path d="M424 224l1 2v2 1c3-1 7-1 10 0v2 2c1 0 1 1 1 1v1h-4c1 0 1 1 1 1l1 1h-3c-1 1-2 0-4 0h-1v1h-1-1 0v-3c-1-2-1-5-1-7v-3l1-1z"></path></g><path d="M424 224l1 2v2 1h1 4c-1 1-4 0-5 2h2v1h-2v1h4v1c-2 0-3 0-4 1h0 1s1 0 1 1v1h-1v1h-1-1 0v-3c-1-2-1-5-1-7v-3l1-1z" class="U"></path><path d="M425 229c3-1 7-1 10 0v2 2c1 0 1 1 1 1v1-1h-4c-1-1-1-1-1-2h-4v-1h-2c1-2 4-1 5-2h-4-1z" class="a"></path><path d="M427 231h5v1h-1-4v-1z" class="P"></path><path d="M142 525v-38c0-1 1-1 2-2l2 1c1 1 0 6 0 8h6c-1 1-1 1-1 2h0c1 2 0 5 1 7 0 1-1 1 0 2 1 2-1 6 0 9h0l-2 1h0l2 1h0c0 1-1 1-1 2h1v1c-1 1-1 1-2 1h0l2 1v1 1c-3-1-5-1-7-1-1 1-1 3-3 3h0z" class="M"></path><path d="M146 494h6c-1 1-1 1-1 2h0c1 2 0 5 1 7 0 1-1 1 0 2h-2v-3c-1-1 0-3 0-4h-4c1-1 2-1 2-2h0-2v-2z" class="C"></path><defs><linearGradient id="F" x1="404.899" y1="253.632" x2="404.483" y2="249.357" xlink:href="#B"><stop offset="0" stop-color="#c1c0c0"></stop><stop offset="1" stop-color="#e5e4e5"></stop></linearGradient></defs><path fill="url(#F)" d="M397 246h1c-1 0-1 0-2 1v2h4c2 1 9-1 10 1l20 2h1l-2 1-9-1h-1c-1 0-2 0-4 1h1l1 1v1 2h0c-1 1-2 1-3 1l1 2h-1c-2 0-3-1-4 0h-1-1v4c0 1 0 1 1 1-1 1-1 1-2 1-1 1-1 2-3 2l-1-1h0c1-1 1-2 1-3v-1-3c0-1 0-1-1-1h-3c-1 0-1 0-2 1l-2-2v1l-1-1h-3-1v-1l-1-2c-1 0-1 0-1-1h0 3v-1h-4-5l-2-1h2l5-1c1-2 1-3 1-4l8-1z"></path><path d="M400 252h8 0-2v1h4-6-1c-1 0-2 0-3-1z" class="f"></path><path d="M408 252c4 0 8-1 11 0-1 0-2 0-4 1h1l1 1h-2l-5-1h-4v-1h2 0z" class="c"></path><path d="M410 253l5 1c0 1-1 1-2 1v1c-1 0-1 0-2-1-1 0-3 0-4 1-1-2-2-1-3-3h6z" class="P"></path><path d="M388 253c3-1 8-1 12-1 1 1 2 1 3 1v1c-4 0-9 0-13 1-1 0-1 0-1-1h0 3v-1h-4z" class="T"></path><path d="M397 246h1c-1 0-1 0-2 1v2h4c2 1 9-1 10 1-7 0-15-1-22 1 1-2 1-3 1-4l8-1z" class="b"></path><path d="M390 255c4-1 9-1 13-1 0 1 1 2 1 3h-1v1 1h-3c-1 0-1 0-2 1l-2-2v1l-1-1h-3-1v-1l-1-2z" class="V"></path><path d="M392 258l-1-2h3c1 0 2 1 3 2h-2-3z" class="D"></path><path d="M397 258c2-1 4-1 6-1v1 1h-3c-1 0-1 0-2 1l-2-2v1l-1-1h2z" class="G"></path><path d="M403 253h1c1 2 2 1 3 3 1-1 3-1 4-1 1 1 1 1 2 1v-1c1 0 2 0 2-1h2v1 2h0c-1 1-2 1-3 1l1 2h-1c-2 0-3-1-4 0h-1-1v4c0 1 0 1 1 1-1 1-1 1-2 1-1 1-1 2-3 2l-1-1h0c1-1 1-2 1-3v-1-3c0-1 0-1-1-1v-1-1h1c0-1-1-2-1-3v-1z" class="a"></path><path d="M404 264l2-1v-1c-1-1-1-1-2-1v-1c1 0 2 0 3 1v2l1 1c0 1 0 1 1 1-1 1-1 1-2 1-1 1-1 2-3 2l-1-1h0c1-1 1-2 1-3z" class="c"></path><path d="M415 254h2v1 2h0c-1 1-2 1-3 1-3 0-7 1-9 0v-1c1-1 2-1 2-1 1-1 3-1 4-1 1 1 1 1 2 1v-1c1 0 2 0 2-1z" class="G"></path><path d="M407 256c1-1 3-1 4-1 1 1 1 1 2 1h0c-3 1-5 1-8 1 1-1 2-1 2-1z" class="o"></path><path d="M415 254h2v1 2h0-1c-1 0-2 0-3-1h0v-1c1 0 2 0 2-1z" class="n"></path><path d="M197 218l1 2h0 1v3h0v1h-1c-1 11-5 22-4 33h1v-3h1v2c3 1 4-2 6-3h2c2-2 5-4 8-6s6-5 9-8c4-4 9-7 12-12 1-1 3-2 3-4h2 0 1c-2 3-4 6-6 8-5 6-11 11-16 16-6 6-12 12-18 17-2 1-3 3-5 2-1 0-1 0-2-1-1-2-1-7-1-10 1-7 2-15 4-23l2-14z" class="I"></path><path d="M236 223h2c-1 2-3 3-5 5a30.44 30.44 0 0 1-8 8l-10 9-6 7-2 2c-3 3-8 8-12 10l-2-2v-3-9c1-4 2-8 2-12 1-5 1-10 3-14-1 11-5 22-4 33h1v-3h1v2c3 1 4-2 6-3h2c2-2 5-4 8-6s6-5 9-8c4-4 9-7 12-12 1-1 3-2 3-4z" class="F"></path><path d="M196 256c3 1 4-2 6-3h2c-2 2-4 4-7 5h-1v-2z" class="N"></path><path d="M193 262l1-5c0 1 0 2 1 4 1-1 1-1 2-1h1c3-1 6-4 8-6h1c-3 3-8 8-12 10l-2-2z" class="X"></path><path d="M166 496h12 1l-1 1v1 1l-1 1 6-3c-1 1-2 2-4 3v1 2l-1 1c0 1-1 2-1 3-2 4 0 9-2 13v3 5 3c-1 1-3 0-4 0h-1l-1-1v-3c-1 1 0 2-1 3l-3 1c1-2 1-6 1-8v-12-6-2-3h0c-1-1-1-2-2-3-1 0-1 0-1-1h3 0z" class="m"></path><path d="M168 496h11c-3 2-5 3-6 6-1-1-1-1-1-2s1-1 2-2h-1c-2-1-2-1-4 0l-1-2z" class="D"></path><path d="M168 503s1 0 1 1c1 0 0 1 1 1v-1l1 1h1v2c-1 0-1-1-1-1h-1 0v2l-1 1c1 1 1 1 2 1-1 1-1 2-1 3v6h0 0l-2-1c-1-2-1-13 0-15z" class="E"></path><path d="M169 509l-1-1v-2h1 1v2l-1 1z" class="B"></path><path d="M166 500l1 3v14c0 4-1 8 1 12v-10h1 0c0 4 0 8 1 12l-1-1v-3c-1 1 0 2-1 3l-3 1c1-2 1-6 1-8v-12-6-2-3z" class="b"></path><path d="M166 496h2l1 2c2-1 2-1 4 0h1c-1 1-2 1-2 2s0 1 1 2c-1 1-2 1-3 2v1c-1 0 0-1-1-1 0-1-1-1-1-1h-1l-1-3h0c-1-1-1-2-2-3-1 0-1 0-1-1h3z" class="C"></path><path d="M177 500l6-3c-1 1-2 2-4 3v1 2l-1 1c0 1-1 2-1 3-2 4 0 9-2 13v3-1h-1-1c0-5-1-13 1-18 1-1 2-3 3-4z" class="F"></path><path d="M179 500v1 2l-1 1c0 1-1 2-1 3-2 4 0 9-2 13v3-1-1-6c0-1-1-3 0-4v-2-1c0-4 2-6 4-8z" class="f"></path><path d="M395 241h6l-1 1h2l-1 1h0-1c1 1 5 0 6 1h-9l1 1h0c0 1-1 1-1 1l-8 1c0 1 0 2-1 4l-5 1h-2l2 1h5 4v1h-3 0c0 1 0 1 1 1l1 2h-1 0-5-2v1h-2-2c-1 0-3 0-4 1h0c0 1 0 2-1 3h0c-2 0-3 0-5-1l1-1-3 1-2-1v-1-1c1-1 1-1 1-2s0-1-1-2h0v-1-1h-1 0c1-1 2-2 4-2v-2l-1-1h0 1l4-2c3-1 6-2 9-2 1 0 1-1 2 0h0v1c3 0 7-1 10-1l2-2z" class="K"></path><path d="M365 253c1 0 1 0 2 1 0 0 0 1 1 2l4-2c2-1 5-2 7-2h2v-1-1h1l1 2h-2l2 1-13 3c1 2 2 2 4 2-2 1-3 1-4 2l-3 1-2-1v-1-1c1-1 1-1 1-2s0-1-1-2h0v-1z" class="L"></path><path d="M365 259c2-1 3-2 5-3 1 2 2 2 4 2-2 1-3 1-4 2l-3 1-2-1v-1z" class="W"></path><path d="M383 253h5 4v1h-3 0c0 1 0 1 1 1l1 2h-1 0-5-2v1h-2-2c-1 0-3 0-4 1h0c0 1 0 2-1 3h0c-2 0-3 0-5-1l1-1c1-1 2-1 4-2-2 0-3 0-4-2l13-3z" class="j"></path><path d="M374 258c2 0 6-3 8-2l1 1c1 0 2-1 3-1s2 0 4 1h-5-2v1h-2-2c-1 0-3 0-4 1h0c0 1 0 2-1 3h0c-2 0-3 0-5-1l1-1c1-1 2-1 4-2z" class="i"></path><path d="M395 241h6l-1 1h2l-1 1h0-1c1 1 5 0 6 1h-9l1 1h0c0 1-1 1-1 1l-8 1c-8 0-17 1-24 5h0-1 0c1-1 2-2 4-2v-2l-1-1h0 1l4-2c3-1 6-2 9-2 1 0 1-1 2 0h0v1c3 0 7-1 10-1l2-2z" class="g"></path><path d="M395 241h6l-1 1h2l-1 1h-8l2-2z" class="U"></path><path d="M381 243c1 0 1-1 2 0h0v1c-2 1-5 1-8 2-2 1-5 2-7 2l-1-1h0 1l4-2c3-1 6-2 9-2z" class="m"></path><path d="M368 250c10-3 19-5 29-6l1 1h0c0 1-1 1-1 1l-8 1c-8 0-17 1-24 5h0-1 0c1-1 2-2 4-2z" class="H"></path><defs><linearGradient id="G" x1="421.707" y1="449.871" x2="421.576" y2="452.952" xlink:href="#B"><stop offset="0" stop-color="#c1bfc0"></stop><stop offset="1" stop-color="#f1f1f3"></stop></linearGradient></defs><path fill="url(#G)" d="M409 440h6 4l1 1-1 1 1 1 1 1h0c0 1 0 1-1 1l3 1h2c2-1 4 0 6 0h0v1h2l1 1c-1 1-4 0-6 0h0c-2 0-4 0-6 1h3c2 1 6 1 8 1s3 0 4-1v-1l2-1h0l-1 1v1c2 1 4 0 5 1l1 1c2 1 7 1 9 3v1h-2c-7-2-16-3-24-3-3 1-7 0-10 1l1 1h1 4 0-2c-1 0-2 0-2 1h-1-1v-2c-4-1-9 0-13 0h-3-4v-1l2-1-4 1c-1-1-1-1-2-1h0c-1-1-1 0-1-1h5v-1h-13 0 3 5v-1h-11 0c2-1 5 0 7 0l14-1v-1-1c0-1 1-1 1-1l1-1h3 2c1 0 2 0 3-1 0 0-1-1-1-2h-2 0z"></path><path d="M437 448l2-1h0l-1 1v1c2 1 4 0 5 1l1 1-11-1c2 0 3 0 4-1v-1z" class="K"></path><path d="M417 447h1c1 0 1 1 1 1h9 0c-2 0-4 0-6 1h3-15v-1c2 0 5 0 7-1h0z" class="X"></path><defs><linearGradient id="H" x1="421.777" y1="444.697" x2="422.931" y2="448.287" xlink:href="#B"><stop offset="0" stop-color="#323532"></stop><stop offset="1" stop-color="#444146"></stop></linearGradient></defs><path fill="url(#H)" d="M417 447c2 0 2-1 2-2h1l3 1h2c2-1 4 0 6 0h0v1h2l1 1c-1 1-4 0-6 0h-9s0-1-1-1h-1z"></path><path d="M402 447h0c3 1 5 1 8 1v1h-23 5v-1h-11 0c2-1 5 0 7 0l14-1z" class="W"></path><path d="M399 451c9 0 19 0 28 1-3 1-7 0-10 1l1 1h1 4 0-2c-1 0-2 0-2 1h-1-1v-2c-4-1-9 0-13 0h-3-4v-1l2-1z" class="K"></path><path d="M409 440h6 4l1 1-1 1 1 1 1 1h0c0 1 0 1-1 1h-1c0 1 0 2-2 2h0c-2 1-5 1-7 1-3 0-5 0-8-1h0v-1-1c0-1 1-1 1-1l1-1h3 2c1 0 2 0 3-1 0 0-1-1-1-2h-2 0z" class="G"></path><path d="M405 444h5c-1 1-2 1-3 2l-2-1v-1z" class="Z"></path><path d="M410 444h3l-1 1c-1 1-2 1-3 1h0v1h-3l1-1c1-1 2-1 3-2z" class="F"></path><path d="M403 444h2v1l2 1-1 1h-4 0v-1-1c0-1 1-1 1-1z" class="Q"></path><path d="M403 444h2v1l-3 1v-1c0-1 1-1 1-1z" class="B"></path><path d="M415 440h4l1 1-1 1 1 1 1 1h0c0 1 0 1-1 1h-1c0 1 0 2-2 2h0c-2 1-5 1-7 1-3 0-5 0-8-1h4 3 4c1-1 1-2 1-3 0 0-1 0-2-1 1 0 1-1 2-1s1 0 1-1v-1z" class="o"></path><path d="M420 441l-1 1 1 1 1 1h0c0 1 0 1-1 1h-1c0 1 0 2-2 2h0v-2h1v-1l-3-1h0c1-1 3-1 4-1l1-1z" class="O"></path><path d="M538 613l4-1h1l-9 6-6 3-1-1c-1 0-1 0-1 1-3 3-10 3-14 4h-4c-1 1-3 1-4 3 0 0 0 1 1 1l-3 1c0-1 0-1-1-2h-2l-1-1c-1 0-2 1-2 1-3-1-11 0-14 1v-2h0c-1-1 0-1 0-2h0c0-2 0-1 1-2 7-2 14-4 20-8l-16-1c0-1 2-1 3-2 5 0 10 0 15 1 1 0 3 0 4 1-1 0-2 1-3 2 1 0 1 0 1 1h-1l1 1c3 0 7-2 11-2h0l11-2h0c2 0 4-1 6-1h3z" class="K"></path><path d="M501 628c1-2 4-3 6-3h1c-1 1-3 1-4 3 0 0 0 1 1 1l-3 1c0-1 0-1-1-2z" class="j"></path><path d="M482 627h8c-2-1-4-2-5-4h3c0 1 2 2 3 3 2 0 5 0 7 1-1 0-2 1-2 1-3-1-11 0-14 1v-2h0z" class="Q"></path><path d="M507 617h-1l1 1c3 0 7-2 11-2-7 5-14 5-22 5 1 0 1 0 2-1 3-1 6-3 9-3z" class="S"></path><path d="M538 613l4-1h1l-9 6-6 3-1-1c-1 0-1 0-1 1-3 3-10 3-14 4h-4-1l7-3c-2 0-5 0-8 1h-7v-1c3 0 5 0 8-1h1l9-3c4-1 9-1 12-3v-1h0c2 0 4-1 6-1h3z" class="e"></path><path d="M538 613l4-1h1l-9 6-6 3-1-1c-1 0-1 0-1 1-2 0-4-1-6-2 3-1 6-1 9-2s6-3 9-4z" class="Y"></path><path d="M171 556l2 1c1 3 5 5 5 8v2 11c0 2 0 5 1 7h0c1 1 1 1 1 2h-3c-3 0-7 0-10-1l-5-2c-1-2 0-1 1-2s0-1 0-2v-2-2c-1-1-1 0-2-1l-1-1v-6c0-1 0-4 1-6s2-4 5-5c0 0 1 0 2-1h2 1z" class="K"></path><path d="M161 562c1-2 2-4 5-5 0 3-3 4-4 6v-1c-1 1-1 3-1 4 0 0 1 0 1-1h2l1-1c0-1 0-2 1-2s1 0 1-1h1 1 0 1 1 1c-1 1-1 1-1 2h-2c-1 0-2 0-3 1v1h-1c-1 1-1 3-1 4-1 1 0 2 0 3v1c0 2 1 3 1 5l1 1h-1 0c-1-1-1-1-2-1v-2c-1-1-1 0-2-1l-1-1v-6c0-1 0-4 1-6z" class="c"></path><path d="M172 561c0 1 2 2 2 2 1 1 1 4 1 5v2 1l-2 1h0l-2 2c-1 0-1 1-2 1v1h-1v-2c-1 1-2 3-3 4 0-2-1-3-1-5v-1c0-1-1-2 0-3 0-1 0-3 1-4h1v-1c1-1 2-1 3-1h2c0-1 0-1 1-2z" class="G"></path><path d="M170 570l1-4h1v1c1 2 0 3 1 5l-2 2c-1-2-1-2-1-4z" class="F"></path><path d="M166 566h1l2-2h1c0 2-1 3-2 5 0 1 1 1 1 2l1-1c0 2 0 2 1 4-1 0-1 1-2 1v1h-1v-2c-1 1-2 3-3 4 0-2-1-3-1-5v-1c0-1-1-2 0-3 0-1 0-3 1-4h1v1z" class="o"></path><path d="M166 566h1l2-2h1c0 2-1 3-2 5 0 1 1 1 1 2-1 0-2 1-2 2h-1c-1-2 0-5 0-7z" class="J"></path><path d="M178 567v11c0 2 0 5 1 7h0c1 1 1 1 1 2h-3c-3 0-7 0-10-1l-5-2c-1-2 0-1 1-2s0-1 0-2v-2c1 0 1 0 2 1h0 1l-1-1c1-1 2-3 3-4v2h1v-1c1 0 1-1 2-1l2-2h0v1l2 1 1-1v-2h1v-1c1-1 1-2 1-3z" class="b"></path><path d="M173 572h0v1l2 1h1c0 1 0 3-1 4h0c0-2-1-3-2-4l-2 1-1 1h-1v-1c1 0 1-1 2-1l2-2z" class="c"></path><path d="M171 575l2-1c1 1 2 2 2 4h0 0l1 3h-1c-1 0-2 0-3-1h0c0-2 0-3-1-5z" class="C"></path><path d="M175 578l1 3h-1c-1 0-2 0-3-1v-1h1c1 0 1 0 2-1h0z" class="I"></path><path d="M170 576l1-1c1 2 1 3 1 5h0c1 1 2 1 3 1h1c0 1 0 1-1 2v1c-2 1-2 0-3 0h-3c-1 0-1-1-2-2 1-1 1-3 2-4l1-2z" class="G"></path><path d="M170 576l1-1c1 2 1 3 1 5h0c1 1 2 1 3 1h1c0 1 0 1-1 2h0l-1-1c-1 0-1 0-2 1 0-1-1-2-2-3l-1-2 1-2z" class="d"></path><path d="M169 424l3 7c0 1 1 2 1 2l6 13c1 2 3 5 4 8h-2l1 2-1 1-1-2v1 1c-1-1-1-2-2-3v-1c-1 1-1 1-2 1v-2h-6-3-5c-2-1-3-1-4-1-2 1-3 1-4 2v-1h-1v-2-1h-1v-2c0-1 0-2 1-3 1-3 3-7 4-10l1-2 3-7c1 0 3 0 4 1v1c1 0 1 0 1-2l3-1z" class="a"></path><path d="M171 444l1-2c1 0 2 2 2 3h-2-1v-1z" class="r"></path><path d="M172 445h2l2 4v1c-1-1-2-1-2-3l-1-1-1-1z" class="b"></path><path d="M165 436h4 0l-1 1 2 2-1 1c1 1 1 1 1 3h-3l-1-1h-3l1-1c1-1 1-1 1-2v-1h0c1-1 0-1 0-2z" class="V"></path><path d="M173 433l6 13c1 2 3 5 4 8h-2c-1-1-2-1-3-2h1c0-2-2-5-3-6l-3-9c-1-2-1-2 0-4z" class="K"></path><path d="M161 425c1 0 3 0 4 1v1 1h1l1 1c-1 1-1 1-2 1v1c1-1 1-1 2-1 1 2 1 4 2 6h-4c-2-1-4-1-7-1v-1h-1l1-2 3-7z" class="E"></path><path d="M164 431c0 1 1 2 1 3-2 1-5 0-7 0h-1l1-2h2c1-1 1 0 2-1h2z" class="M"></path><path d="M161 425c1 0 3 0 4 1v1 1h1l1 1c-1 1-1 1-2 1l-1 1h-2c-1 1-1 0-2 1h-2l3-7z" class="L"></path><path d="M157 434h1v1c3 0 5 0 7 1 0 1 1 1 0 2h0v1c0 1 0 1-1 2l-1 1h3l1 1h3c1 0 1 1 1 1v1h1l1 1h-2c0 1 1 2 1 3h-7c-1 1-1 2-3 3-2-1-3-1-4-1-2 1-3 1-4 2v-1h-1v-2-1h-1v-2c0-1 0-2 1-3 1-3 3-7 4-10z" class="B"></path><path d="M156 449h9c-1 1-1 2-3 3-2-1-3-1-4-1-2 1-3 1-4 2v-1h-1v-2-1h3z" class="X"></path><path d="M153 452v-2h4c1 0 1 1 1 1-2 1-3 1-4 2v-1h-1z" class="r"></path><path d="M153 444c1-1 2-1 3-1v1h1c1 0 3 0 4 1h-1-1v3c-1 0-2 1-3 1h0-3-1v-2c0-1 0-2 1-3z" class="I"></path><path d="M153 449c1-2 5-3 7-4h-1v3c-1 0-2 1-3 1h0-3zm4-15h1v1c3 0 5 0 7 1 0 1 1 1 0 2h0v1c0 1 0 1-1 2l-1 1v-1l-1 1h1l2 2v1h-4c-1-1-3-1-4-1h-1v-1c-1 0-2 0-3 1 1-3 3-7 4-10z" class="C"></path><path d="M458 529h1 1l-1 1 1 1 1-1c1 1 1 2 2 3h0l1 1h2 3l1 2h0c1 0 1 0 2 1l1 1 1 1v1c2 1 2 2 2 4h0c0 2 0 4-1 6l-1-1h0v1h-1l-4 3v1l-1 1c-1 0-1 1-2 1h0c-1 0-2-1-3-2h-1l-1-2c-1-1-2-1-3-2h-2c-2 1-4 1-5 2-2 1-3 1-4 2s-2 1-3 3c0-1 0-1-1-1v-6-5-1 1h1v-1h1v-1-2c1-1 1-2 2-3h-1l1-2v-3c2 2 3 1 5 2h0 1 1s1-1 1-2v-2c1-1 2-1 3-2z" class="e"></path><path d="M458 529h1 1l-1 1-4 3v-2c1-1 2-1 3-2z" class="E"></path><path d="M457 540h1c1 0 1 0 2-1 1 1 1 0 3 1l-2 2h-1v-1c-1 0-1 1-2 1 0-1 0-1-1-1h0-1l1-1z" class="d"></path><path d="M457 540c1-2 3-3 4-4l1 2h1 1c-1 1-1 1-1 2-2-1-2 0-3-1-1 1-1 1-2 1h-1z" class="E"></path><path d="M464 534h2 3l1 2h0c1 0 1 0 2 1-2-1-3-1-4-1h-1c-1 1-1 1-2 1l-1 1h-1-1l-1-2c1 0 1-1 2-1l1-1z" class="V"></path><path d="M461 536c1 0 1-1 2-1l2 1-2 2h-1l-1-2z" class="B"></path><path d="M460 531l1-1c1 1 1 2 2 3h0c0 1 0 1-1 1l-6 5c-1 0 0-3 0-4 1-2 3-3 4-4z" class="D"></path><path d="M463 540c0 1 2 1 2 1l1 1h2l1 1h1l-2 2c1 1 0 2 1 3l1 1h4v1h-1-1c-1 0-1 0-2 1h-2c1-1 1-1 1-2h-2v-1h-2l2-2h-1s0-1 1-2v-1h-2c0 1-1 1-1 1-1 0-1 0-2-1h-2v-1h1l2-2z" class="V"></path><path d="M465 537c1 0 1 0 2-1h1c1 1 2 2 3 2 0 2 0 3-1 4l-1 1-1-1h-2l-1-1s-2 0-2-1 0-1 1-2l1-1z" class="C"></path><path d="M464 538l1-1h1c0 1 1 1 1 1h1c-1 1-1 1-2 1v1h0c1 1 2 1 2 2h-2l-1-1s-2 0-2-1 0-1 1-2z" class="H"></path><path d="M461 545c1 0 2 0 4-1v2h1 1l-2 2h2v1h2c0 1 0 1-1 2h2c1-1 1-1 2-1h1l-4 3v1l-1 1c-1 0-1 1-2 1h0c-1 0-2-1-3-2h-1c2-1 4-2 5-3l-1-1-1 1v-1h1v-1c-1 0-2 0-3-1h-1 0l2-1v-1l-3-1z" class="i"></path><path d="M468 536c1 0 2 0 4 1l1 1 1 1v1c2 1 2 2 2 4h0c0 2 0 4-1 6l-1-1h0-4l-1-1c-1-1 0-2-1-3l2-2h-1l1-1c1-1 1-2 1-4-1 0-2-1-3-2z" class="I"></path><path d="M468 536c1 0 2 0 4 1l1 1 1 1c-1 1-2 1-3 2v1c0 1-1 2-2 4 2 1 4-1 5 2h0v1h0-4l-1-1c-1-1 0-2-1-3l2-2h-1l1-1c1-1 1-2 1-4-1 0-2-1-3-2z" class="L"></path><path d="M447 536v-3c2 2 3 1 5 2h0 1 1v1s-1 1 0 2c0 1 0 1 1 2h1l-1 2c1 1 2 1 3 0v2 2h1 0c1-1 1-1 2-1l3 1v1l-2 1h0 1c1 1 2 1 3 1v1h-1v1l1-1 1 1c-1 1-3 2-5 3l-1-2c-1-1-2-1-3-2h-2c-2 1-4 1-5 2-2 1-3 1-4 2s-2 1-3 3c0-1 0-1-1-1v-6-5-1 1h1v-1h1v-1-2c1-1 1-2 2-3h-1l1-2z" class="n"></path><path d="M462 548c-1 1-1 1-3 1 0-2 1-1 2-2h-1c-1 0-2-1-3-1v-3h-2v-1c1 1 2 1 3 0v2 2h1 0c1-1 1-1 2-1l3 1v1l-2 1h0z" class="P"></path><path d="M452 542v-1c1 0 1 0 1 1 1 0 1 1 1 2l1 1 2 2-1 3h-1c-1 0-1-1-1-1l-2-3-1-1-1-2s1 0 2-1z" class="K"></path><path d="M452 542c0 1 1 1 1 2s0 1-1 2l-1-1-1-2s1 0 2-1z" class="r"></path><path d="M447 538l1-1c1 0 1 1 2 1s1-1 2 0c0 2-2 4-3 5h0-1c-1 1-2 2-3 2l-1-1h1v-1-2c1-1 1-2 2-3z" class="p"></path><path d="M448 543h2l1 2 1 1 2 3s0 1 1 1h1c-2 1-4 1-5 2-2 1-3 1-4 2s-2 1-3 3c0-1 0-1-1-1v-6-5-1 1h1v-1l1 1c1 0 2-1 3-2z" class="J"></path><path d="M448 543h2l1 2c-2 0-3 1-4 2s-1 3-2 3l-1-1h1c0-2-1-2-1-3l1-1c1 0 2-1 3-2z" class="B"></path><path d="M454 549s0 1 1 1h1c-2 1-4 1-5 2-2 1-3 1-4 2s-2 1-3 3c0-1 0-1-1-1v-6 3c1-1 3-3 5-3 1 0 0 0 1 1h0c1-1 1-1 2-1 1-1 2-1 3-1z" class="g"></path><path d="M443 553c1-1 3-3 5-3 1 0 0 0 1 1h0c-1 2-4 3-5 4h-1v-2z" class="U"></path><path d="M391 257v1h1 3l1 1v-1l2 2c1-1 1-1 2-1h3c1 0 1 0 1 1v3 1c0 1 0 2-1 3h0l1 1c2 0 2-1 3-2 1 0 1 0 2-1-1 0-1 0-1-1v-4h1 1c1-1 2 0 4 0h1 1l1 1v4c1-2 0-3 1-5h1l1 1c1-1 1-1 2-1 1 1 0 1 0 2v4l1 1c0 1 1 1 1 2v1h3v-1l1 2h1l5 1c-1 0-2-1-3 0 2 0 3 0 4 1-2 0-3 0-4-1h-1v1l-10-1h-6 1c0 1 0 1-1 1-4 1-9 0-13 1h-10-6-2l1-1h0l-6 1c0-1 0-1 1-1l-1-1h0c0-1 1-2 0-3l-1 1h-1l1-3h-1l-3-1h0v-2l1-1h1l-1-1h0c1-1 1-2 1-3h0c1-1 3-1 4-1h2 2v-1h2 5 0 1z" class="T"></path><path d="M427 270v-1l1 2h1l5 1c-1 0-2-1-3 0 2 0 3 0 4 1-2 0-3 0-4-1h-1v1l-10-1h-1l-1-1h-1-5 12v-1h3z" class="f"></path><path d="M427 270v-1l1 2h-4v-1h3z" class="O"></path><path d="M422 260c1 1 0 1 0 2v4l-1 1c1 1 1 1 1 2h-3v-2h0-3c1-1 1-2 1-2 1-2 0-3 1-5h1l1 1c1-1 1-1 2-1z" class="K"></path><path d="M422 260c1 1 0 1 0 2v4l-1 1c0-1-1-3-1-5v-1c1-1 1-1 2-1zm-6 0l1 1v4s0 1-1 2h-1v1c1 1 2 1 3 1s4 0 5 1h-7-1-4-2 0 0c-1-1-3-2-5-2 2 0 2-1 3-2 2 0 4 1 5 1 1-1 1-1 3-1v-1c1 0 1 0 1-1s-1-1-1-3h1v-1z" class="O"></path><path d="M404 268c2 0 2-1 3-2 2 0 4 1 5 1l-1 2h2l1-1c1 0 2 1 3 1l-1 1h-1-4-2 0 0c-1-1-3-2-5-2z" class="U"></path><path d="M410 260c1-1 2 0 4 0h1 1v1h-1c0 2 1 2 1 3s0 1-1 1v1c-2 0-2 0-3 1-1 0-3-1-5-1 1 0 1 0 2-1-1 0-1 0-1-1v-4h1 1z" class="P"></path><path d="M408 260h1 1c0 1 0 1 1 1l1 1-1 2h-2-1v-4z" class="E"></path><path d="M410 260c1-1 2 0 4 0h1 1v1h-1c0 2 1 2 1 3s0 1-1 1l-1-1h-3l1-2-1-1c-1 0-1 0-1-1z" class="F"></path><path d="M410 260c1-1 2 0 4 0v2h-1-1l-1-1c-1 0-1 0-1-1z" class="J"></path><path d="M384 273c1-1 3-1 4-1 9-1 17-1 26 0h1c0 1 0 1-1 1-4 1-9 0-13 1h-10-6-2l1-1h0z" class="L"></path><path d="M396 259v-1l2 2c1-1 1-1 2-1h3c1 0 1 0 1 1v3 1c0 1 0 2-1 3h0l1 1c2 0 4 1 5 2h0 0-11-2-4 0c1-1 2-1 3-1l-1-1 1-3h0c-1-1 0-2 0-3l-1-1 1-1v-1h1z" class="J"></path><path d="M402 264h1v1c-2 1-3 0-5 0h1c0-1 2-1 3-1z" class="B"></path><path d="M395 269h1 5v1h-3-2-4 0c1-1 2-1 3-1z" class="E"></path><path d="M395 262c1 1 2 1 4 1h0 2 1v1c-1 0-3 0-3 1h-1-3 0c-1-1 0-2 0-3z" class="Q"></path><path d="M396 259v-1l2 2c1-1 1-1 2-1h3c1 0 1 0 1 1v3h-2-1-2 0c-2 0-3 0-4-1l-1-1 1-1v-1h1z" class="D"></path><path d="M400 259h3c1 0 1 0 1 1l-1 1v1c-1 1-1 0-1 0l-1-1c1-1 0-1 0-2h-1z" class="H"></path><path d="M396 259v-1l2 2c1-1 1-1 2-1h1c0 1 1 1 0 2-2 0-3 0-4-1h-2v-1h1z" class="N"></path><path d="M391 257v1h1 3l1 1h-1v1l-1 1 1 1c0 1-1 2 0 3h0l-1 3 1 1c-1 0-2 0-3 1h0 4-1c-3 1-6 1-8 1h-3-1 0c-1 0-1 0-2 1h-3c0-1 1-2 0-3l-1 1h-1l1-3h-1l-3-1h0v-2l1-1h1l-1-1h0c1-1 1-2 1-3h0c1-1 3-1 4-1h2 2v-1h2 5 0 1z" class="F"></path><path d="M387 260c1 0 3-1 5-1 0 1 0 2-1 3v1c-1 0-2-1-2-1l-2-2z" class="d"></path><path d="M382 266c1-1 1-1 2 0h0v1c1 0 2 0 3 1h0c-1 0-2 0-3 1l-1 2h0c-1-1-1-2-1-3v-2z" class="f"></path><path d="M382 261h1 0c1 2 2 2 3 2 0 2-1 3-2 3h0c-1-1-1-1-2 0h-1l1-1v-2-2z" class="P"></path><path d="M379 258h2v2l1 1h0v2 2l-1 1c-1-2-1-3-2-5v-3z" class="G"></path><path d="M379 261c1 2 1 3 2 5h1v2h0c-2-1-4-1-5-1h-1v-2h2 1v-3-1z" class="h"></path><path d="M374 263h2s0-1 1-2c1 0 1 1 2 1v3h-1-2v2l-3-1h0v-2l1-1z" class="P"></path><path d="M374 262h0c1-1 1-2 1-3h0c1-1 3-1 4-1v3 1c-1 0-1-1-2-1-1 1-1 2-1 2h-2 1l-1-1z" class="j"></path><path d="M377 267c1 0 3 0 5 1h0c0 1 0 2 1 3-1 0-1 0-2 1h-3c0-1 1-2 0-3l-1 1h-1l1-3z" class="R"></path><path d="M378 269c2 1 3 2 3 3h-3c0-1 1-2 0-3z" class="U"></path><path d="M392 259v-1c1 0 2 0 3 1h0v1l-1 1 1 1c0 1-1 2 0 3h0l-1 3c-1 0 0 0-1-1h-2v-1c1-1 1-1 1-2v-1h1l-2-1c1-1 1-2 1-3z" class="X"></path><path d="M391 257v1h1 3l1 1h-1 0c-1-1-2-1-3-1v1c-2 0-4 1-5 1-2 0-3 0-4 1h0-1 0l-1-1v-2h2v-1h2 5 0 1z" class="S"></path><path d="M391 257v1h-1c-2 1-4 1-5 1l-1-1 1-1h5 0 1z" class="d"></path><path d="M397 221c3 0 6 0 10 1h1v3 5 2 1 1 1 1 2 2h0c0 1 0 2-1 3h-6 0l1-1h-2l1-1h-6l-2 2c-3 0-7 1-10 1v-1h0c-1-1-1 0-2 0-3 0-6 1-9 2v-4h-4v-3-1c1-3 1-6 2-9 1 0 1-1 3-1 0 1-1 1 0 2h1v-1c1-2 2-2 4-3h0 2 0l1-1c1-1 1-1 2-1h1 0v4l9-1c1 0 1 0 2-1h1v-1-1c0-1 0-1 1-2z" class="C"></path><path d="M384 227l9-1h1 0-1c0 1 0 1-1 1s-2 0-4 1h-4v-1z" class="I"></path><path d="M397 221c3 0 6 0 10 1l-1 3c-3-1-7 0-10 0v-1-1c0-1 0-1 1-2z" class="Q"></path><path d="M407 222h1v3 5 2 1 1 1 1 2 2h0c0 1 0 2-1 3h-6 0l1-1h-2l1-1h-6c-3 0-6 1-9 1 1-1 2-1 4-1 1 0 2 0 3-1v-2h-4 0l1-1h4l1-1h-1l1-2v-1l-1-1 1-1 1 1-1 1v2 1c2 2 8 0 11 1h1v-1-2-1c-1 0-2 0-3-1h0 0c-1 1-2 1-4 0 2 0 5 1 7 0v-2h-2v-1h1c1-2 1-3 0-4l1-3z" class="m"></path><path d="M386 242c1-1 2-1 4-1 1 0 2 0 3-1v-2h-4 0l1-1h4v2 1c1-1 1-1 1-2 2-1 9-1 11 0h1v3h-6-6c-3 0-6 1-9 1z" class="C"></path><path d="M383 223h1 0v4 1 1c3 0 5-1 8-1h2v1 1l1 1-1 1 1 1v1l-1 2h1l-1 1h-4l-1 1h0 4v2c-1 1-2 1-3 1-2 0-3 0-4 1 3 0 6-1 9-1l-2 2c-3 0-7 1-10 1v-1h0c-1-1-1 0-2 0v-1h1v-3-1c-1 0-1 0-1-1h2l-1-3 1-2h-1-2v-1h3v-2h-1-1c1-1 1-1 2-1v-5z" class="k"></path><path d="M383 223h1 0v4 1 1 2h2 0c-1 0-1 1-2 1v5 2c-1 1-1 2-1 2l1 1h2c3 0 6-1 9-1l-2 2c-3 0-7 1-10 1v-1h0c-1-1-1 0-2 0v-1h1v-3-1c-1 0-1 0-1-1h2l-1-3 1-2h-1-2v-1h3v-2h-1-1c1-1 1-1 2-1v-5z" class="S"></path><path d="M370 228c1 0 1-1 3-1 0 1-1 1 0 2h1v-1c1-2 2-2 4-3h0 2 0l1-1c1-1 1-1 2-1v5c-1 0-1 0-2 1h1 1v2h-3v1h2 1l-1 2 1 3h-2c0 1 0 1 1 1v1 3h-1v1c-3 0-6 1-9 2v-4h-4v-3-1c1-3 1-6 2-9z" class="H"></path><path d="M368 238c2 0 3-2 4-1v4h0 0-4v-3z" class="M"></path><path d="M382 229h1v2h-3s-1 1-2 1h0-3l-1 1c-1-1-1-1 0-2s3-1 5-1c1 0 1-1 2-1h1z" class="I"></path><path d="M374 233l1-1h3 0c1 0 2-1 2-1v1h2 1l-1 2 1 3h-2c0 1 0 1 1 1v1 3h-1v1c-3 0-6 1-9 2v-4h0 1l1-1c-1-1-1-1-1-2 1-1 1-1 1-2l-1-1 1-1v-1z" class="C"></path><path d="M382 238v1 3h-1c-1 0-1 0-2-1-1 1-2 1-3 0h0c0-1 0-1 1-1h1c1-1 2-1 3-1l1-1zm0-6h1l-1 2v1c-1 1-2 1-3 1-2 0-3 1-5 2v-2c1-1 4-1 5-2 1 0 1-1 1-1s1-1 2-1z" class="I"></path><defs><linearGradient id="I" x1="439.305" y1="288.372" x2="380.402" y2="266.16" xlink:href="#B"><stop offset="0" stop-color="#343535"></stop><stop offset="1" stop-color="#524f50"></stop></linearGradient></defs><path fill="url(#I)" d="M414 272h6l10 1 11 2 9 2c4 2 9 3 11 7v3l-3 3h-1c1-2 1-2 1-4-1-3-5-5-8-6h0c-1 0-2 0-2-1h-2-1 0l-1 1h1l1 1c2 2 6 2 8 5 0 1-1 1-1 2-1 0-1 1-2 1s-2-1-3-2h-2v-1c-1 0-2-1-3-1h-1v-1h0v-1l-25-4h-2c-4 0-9 1-13 0h-1-5c-6 1-12 1-18 2-3 1-7 2-10 4l-1 1-3 3c-1 0-1 1-1 1l-1 1h-1c0-2 1-2 2-2l-1-1h0-1c-1 1-1 2-2 2h-1c1-1 1-1 1-2-1 0-1 0-2 1h0c-2 0-3-1-4-1-1-1-1-1-1-2v-2c1-3 7-5 10-6l16-4 6-1h0l-1 1h2 6 10c4-1 9 0 13-1 1 0 1 0 1-1h-1z"></path><path d="M368 278h1c3-1 5-1 7 0h-1l-3 2h0-2 0l-2-2z" class="P"></path><path d="M388 276c4-1 9-1 13 0h-1l1 1h-7v1c2 0 5-1 7 1h-5 0c-2-1-7-1-8-1-2 1-4 1-5 1h-1v-1h2c-1-1-2-1-3-1l7-1z" class="i"></path><path d="M381 277l7-1h6v1c-2 0-5 0-7 1h1c-2 1-4 1-5 1h-1v-1h2c-1-1-2-1-3-1z" class="S"></path><path d="M378 274l6-1h0l-1 1h2 6l-10 1c-7 1-13 2-20 5-3 1-6 3-9 6h0v-2c1-3 7-5 10-6l16-4z" class="D"></path><path d="M376 278c2 0 3-1 5-1 1 0 2 0 3 1h-2v1h1c1 0 3 0 5-1 1 0 6 0 8 1h0c-6 1-12 1-18 2-3 1-7 2-10 4l-1-1c1-1 2-1 2-2 1-1 2-1 4-1v-1h-1 0l3-2h1z" class="R"></path><path d="M376 278c2 0 3-1 5-1 1 0 2 0 3 1h-2v1h1 0l-5 1h-1c0-1-1-1-1-1l-1-1h1z" class="G"></path><path d="M368 278l2 2h0 2 1v1c-2 0-3 0-4 1 0 1-1 1-2 2l1 1-1 1-3 3c-1 0-1 1-1 1l-1 1h-1c0-2 1-2 2-2l-1-1h0-1c-1 1-1 2-2 2h-1c1-1 1-1 1-2-1 0-1 0-2 1h0c-2 0-3-1-4-1l1-2c2-4 10-6 14-8z" class="U"></path><path d="M370 280h2 1v1c-2 0-3 0-4 1 0 1-1 1-2 2l1 1-1 1-3 3c-1 0-1 1-1 1l-1 1h-1c0-2 1-2 2-2l-1-1h0l-1-1h1c0-1-1-1-2-2h0c-1 1-2 1-3 1l1-1c0-1 2-2 3-2l9-3h0z" class="F"></path><path d="M360 285h1l4-2c1-1 1-1 2-1v1c-1 0-2 0-2 1v1l-3 3h0l-1-1h1c0-1-1-1-2-2z" class="G"></path><path d="M367 284l1 1-1 1-3 3c-1 0-1 1-1 1l-1 1h-1c0-2 1-2 2-2l-1-1 3-3 2-1z" class="m"></path><path d="M414 272h6l10 1 11 2 9 2c4 2 9 3 11 7v3l-3 3h-1c1-2 1-2 1-4-1-3-5-5-8-6h0c-2-1-4-2-7-3-13-4-28-3-42-3 4-1 9 0 13-1 1 0 1 0 1-1h-1z" class="V"></path><path d="M402 279v-1-1h25l2 1c3 2 5 1 9 1 1 0 3 1 4 1 1-1 1-1 1-3 3 1 5 2 7 3-1 0-2 0-2-1h-2-1 0l-1 1h1l1 1c2 2 6 2 8 5 0 1-1 1-1 2-1 0-1 1-2 1s-2-1-3-2h-2v-1c-1 0-2-1-3-1h-1v-1h0v-1l-25-4h-2c-4 0-9 1-13 0z" class="c"></path><path d="M451 289h0l-5-6h0 1 1c1 1 3 2 3 3s1 1 1 1l1 1c-1 0-1 1-2 1z" class="Y"></path><path d="M182 605h2l2 2c3 3 7 5 11 7 3 2 6 4 10 6 3 2 7 3 10 4 4 2 8 4 12 5l15 3c6 2 11 6 17 8 2 0 4 1 7 1h1 4c4 0 7 1 11 1 3 1 6 2 9 2l10 2 3 1c4 1 9 2 12 2 1 0 2 0 3 1h1l2 1h-2l7 1v-1c4 0 7 1 10 1 12 2 25 2 37 2h9c1-1 2-1 4-1v1c8 0 16-1 24-2 3-1 7-2 11-2 4-1 8 0 12-1 5-1 9-2 14-4l12-2c8-2 15-5 23-8 0-1 2-1 3-2v1l-1 1h1l-3 1c0 1-2 2-3 2-10 5-22 7-33 10-4 1-7 2-11 3-5 1-9 0-14 1l-15 3c-15 1-29 1-44 1-6 0-13 1-20 0-7 0-14-1-21-2l-13-2c-5-1-10-3-15-4-11-1-21-2-32-5l-9-3-12-6c-5-1-11-2-17-3-3-1-6-3-9-5-5-2-10-3-14-6-3-1-5-3-8-4l-7-5c-2-2-4-3-7-5l1-1z" class="M"></path><path d="M306 647c4 1 9 2 12 2 1 0 2 0 3 1h1l2 1h-2c-6-1-13-1-19-3l1-1h2z" class="Y"></path><defs><linearGradient id="J" x1="277.874" y1="645.822" x2="282.514" y2="639.571" xlink:href="#B"><stop offset="0" stop-color="#110f15"></stop><stop offset="1" stop-color="#23251f"></stop></linearGradient></defs><path fill="url(#J)" d="M273 641c4 0 7 1 11 1 3 1 6 2 9 2l10 2 3 1h-2l-1 1c-1 0-4-1-5-1-9-2-19-3-28-5l-2-1h1 4z"></path><path d="M268 641h1c1 0 2 0 2 1h-1l-2-1z" class="q"></path><path d="M329 652v-1c4 0 7 1 10 1 12 2 25 2 37 2h9c1-1 2-1 4-1v1h-4c-7 1-14 1-20 1h-17l-19-3z" class="r"></path><path d="M407 408l6-1c1 0 2 1 4 0 1 0 1 0 2 1 1 0 1 1 2 1-1 0-1 0-2 1h0v17 1 4h0l-1-1v2l-1-1v1h0c-2 1-3 0-5 1h1c-1 1-2 1-3 1l1 1 1 1h0 3c-1 1-2 1-3 1v1h-4 0l1 1h0 2c0 1 1 2 1 2-1 1-2 1-3 1h-2-3l-1 1s-1 0-1 1v1 1l-14 1c-2 0-5-1-7 0h-8v-5h5v-1c-1-1 0-1 0-2-1 0-2 1-2 0h0c1-1 1-1 1 0l2-2h0c2-1 4-1 6-1v-1h4 1c1 0 4 0 5-1v-3-1-13-1l1-1v-4h1 6v-1h0c2 0 4 0 5-1h1s1 0 1-1h1c1-1 1-1 2-1-1-1-4 0-6 0z" class="c"></path><path d="M411 427c0-1 1-1 1-2v-1h1c1 1 1 1 2 1 0 0 1 0 1 1h1c-2 1-3 1-5 1h-1z" class="U"></path><path d="M406 415h2 0c3 1 8 0 10 2h-1c-2 0-4 1-7 0h0v-1c-2-1-7 0-9 0 2-1 3-1 5-1z" class="R"></path><path d="M395 417l1-1v-4c1 0 3 1 4 0-1 1-2 1-2 1 0 1-1 1-1 2h1-2c1 1 3 1 5 1h0c2 0 7-1 9 0v1l-7 1c-2 0-5 0-8-1z" class="B"></path><path d="M403 412h1 3v2c0 1 0 0-1 1-2 0-3 0-5 1h0c-2 0-4 0-5-1h2-1c0-1 1-1 1-2 0 0 1 0 2-1-1 1-3 0-4 0h1 6z" class="D"></path><path d="M400 412l2 1v1c-1 1-2 1-3 1h-1-1c0-1 1-1 1-2 0 0 1 0 2-1z" class="C"></path><path d="M407 408l6-1c1 0 2 1 4 0 1 0 1 0 2 1 1 0 1 1 2 1-1 0-1 0-2 1h0c0 1 0 3-1 3v2h-4 0-4-2-2c1-1 1 0 1-1v-2h-3-1v-1h0c2 0 4 0 5-1h1s1 0 1-1h1c1-1 1-1 2-1-1-1-4 0-6 0z" class="g"></path><path d="M417 407c1 0 1 0 2 1 1 0 1 1 2 1-1 0-1 0-2 1h0c0 1 0 3-1 3h-1v-1c0-2 0-3 1-4l-1-1z" class="K"></path><path d="M408 410h1s1 0 1-1h1c1-1 1-1 2-1v2c1 0 2 0 2 1h-3c-3 0-6 1-9 0 2 0 4 0 5-1z" class="S"></path><path d="M404 412c3 0 8-1 11 2h0l-1 1h-4-2-2c1-1 1 0 1-1v-2h-3z" class="R"></path><path d="M407 412l3 1v2h-2-2c1-1 1 0 1-1v-2z" class="J"></path><path d="M395 431v-13-1c3 1 6 1 8 1h0c2 0 5 0 7 1h1l1-1c2 0 3 1 5 1l1 1h-2l1 1v1c-1 0-1 0-2 1h1l-7-1h-4c-3 0-7-1-9 0v3 2h0v5h0c0 1 0 3 1 4h2 0l-1 1-19 1c2-1 4-1 6-1v-1h4 1c1 0 4 0 5-1v-3-1z" class="O"></path><path d="M403 418c2 0 5 0 7 1l1 1-1 1h-9c1-1 2-1 3-1 0-1-1-1-1-2z" class="B"></path><path d="M401 421h-5v-3h0c2-1 6 0 7 0 0 1 1 1 1 2-1 0-2 0-3 1z" class="M"></path><path d="M396 425v-3c2-1 6 0 9 0h4l7 1h1v1h-4-1v1c0 1-1 1-1 2l-1 1h2v1h-2v-1c-1 0-3 0-3-1l-1 3-9 1-1 1v-5h0v-2z" class="S"></path><path d="M405 422h4v1c1 1 1 1 1 3-1 1-5 0-7 0 1-1 1-1 1-2s1-2 1-2z" class="V"></path><path d="M405 422h4v1c-1 0-2 1-2 1h-3c0-1 1-2 1-2z" class="D"></path><path d="M397 431l-1-1v-2c2-1 4-1 6-1 1 0 1 1 2 0h3l-1 3-9 1z" class="C"></path><path d="M396 425v-3c2-1 6 0 9 0 0 0-1 1-1 2s0 1-1 2h-7v-1z" class="B"></path><path d="M407 427c0 1 2 1 3 1v1h2v-1c2 0 3 0 5 1v1h0-4v1c1 1 3 1 4 2h0c-2 1-3 0-5 1h1c-1 1-2 1-3 1l1 1 1 1h0 3c-1 1-2 1-3 1v1h-4 0l1 1h0 2c0 1 1 2 1 2-1 1-2 1-3 1h-2-3l-1 1s-1 0-1 1l-1-1c-1 0-1 0-2-1 0-1 1 0 1 0 1-1 1-1 1-2l-1-1v-3h-2l1-1h0-2c-1-1-1-3-1-4h0l1-1 9-1 1-3z" class="i"></path><path d="M407 427c0 1 2 1 3 1v1h2 1l-1 1h-6l1-3z" class="Z"></path><path d="M398 432c3-1 8-1 11 0l2 1h0c-1 1-3 1-4 1h-2l1-1v-1h-8z" class="Q"></path><path d="M398 432h8v1l-1 1h-2-3-1c1 1 1 1 2 1h2c0 2 0 2-1 2s-2 0-2-1h-1-2c-1-1-1-3-1-4h2z" class="L"></path><path d="M399 436h1c0 1 1 1 2 1s1 0 1-2h-2c-1 0-1 0-2-1h1 3c1 1 2 1 3 1 0 1 0 1-1 2 2 1 5 0 7 1v1h-4 0l1 1h0 2c0 1 1 2 1 2-1 1-2 1-3 1h-2-3l-1 1s-1 0-1 1l-1-1c-1 0-1 0-2-1 0-1 1 0 1 0 1-1 1-1 1-2l-1-1v-3h-2l1-1h0z" class="Z"></path><path d="M407 443c1 0 2 0 3-1h-1-2 0c0-1 1-1 1-2h0v-1l1 1h0 2c0 1 1 2 1 2-1 1-2 1-3 1h-2z" class="F"></path><path d="M379 438l19-1h2v3l1 1c0 1 0 1-1 2 0 0-1-1-1 0 1 1 1 1 2 1l1 1v1 1l-14 1c-2 0-5-1-7 0h-8v-5h5v-1c-1-1 0-1 0-2-1 0-2 1-2 0h0c1-1 1-1 1 0l2-2h0z" class="I"></path><path d="M379 438l19-1h2v3h-7-13-3l2-2h0z" class="C"></path><path d="M482 629c3-1 11-2 14-1-1 1-2 3-2 4-2 0-4 1-6 1h0c-1 1-3 1-3 2-8 3-15 6-23 8l-12 2c-5 2-9 3-14 4-4 1-8 0-12 1-4 0-8 1-11 2-8 1-16 2-24 2v-1h2c3-1 7 0 11-1 3 0 6 0 9-1h3l10-2-1-1h0c-2-1-2-1-3-2h-5 2v-1h-2-3c-1-1-1-1-2-1-3 0-6 0-9-1-1 0-3 0-4-1h1l-1-1h1 0-3 0l3-2c-1-1-1-1-2-1h0l-1-1c4 0 19 1 21 0l-1-2h1s1 0 2 1c0-1 1-1 1-1h3c2 0 5 0 7-1 1 1 2 1 3 1l1 1h2c0-1 0-1 1-1 6 0 14 0 21-2h3v-1h1l8-1v-1c4 0 7 0 10-1h3z" class="K"></path><path d="M434 646l6-1h1c1 0 0 0 1 1 1 0 1 0 2-1h0c-2 1-4 2-6 2s-3 0-4-1z" class="E"></path><path d="M420 646h4 1c2 1 7 0 9 0 1 1 2 1 4 1-4 1-7 1-11 1-1 0-2 1-3 1l-1-1h0c-2-1-2-1-3-2z" class="J"></path><path d="M439 644l27-4c-4 2-9 3-13 4-3 0-6 2-9 1h0c-1 1-1 1-2 1-1-1 0-1-1-1h-1l1-1h-2z" class="Z"></path><path d="M417 645h6c0-1 1-1 1-1h1c1 0 1-1 2 0h3 9 2l-1 1-6 1c-2 0-7 1-9 0h-1-4-5 2v-1z" class="L"></path><defs><linearGradient id="K" x1="447.529" y1="630.406" x2="436.919" y2="647.61" xlink:href="#B"><stop offset="0" stop-color="#b4b1ae"></stop><stop offset="1" stop-color="#e2e1e7"></stop></linearGradient></defs><path fill="url(#K)" d="M485 633c-3 3-10 4-14 4l-9 3-26 3h-4c-2 0-3 0-5-1-1-1-2-1-4-2l-1-1 1-1 5 1h0 0 1c2 0 4 1 7 0l20-1c3 0 7 0 11-1h2l2-1c2 0 5-1 8-2 2 0 4-1 6-1z"></path><path d="M428 639h1c-1 1-2 1-3 1h0l2-1z" class="C"></path><path d="M482 629c3-1 11-2 14-1-1 1-2 3-2 4-2 0-4 1-6 1-1 0-1 0-2-1h0l-1 1c-2 0-4 1-6 1-3 1-6 2-8 2h-1c-1-1-1 0-1-1h-1v-1h1-2c-2 0-4 0-5-1h-2v-1h1l8-1v-1c4 0 7 0 10-1h3z" class="r"></path><path d="M469 630c4 0 7 0 10-1h1c1 1 1 1 2 1h0c-1 1-2 1-3 1-2 1-5 1-6 1v-1h-4v-1z" class="l"></path><path d="M474 633c3-1 6-1 9-2h0c-1 3-6 3-9 3l-5 1h-1v-1h1-2l1-1h6z" class="G"></path><path d="M461 632l8-1h4v1l-3 1h0 4-6l-1 1c-2 0-4 0-5-1h-2v-1h1z" class="m"></path><path d="M457 633h3 2c1 1 3 1 5 1h2-1v1h1c0 1 0 0 1 1h1l-2 1h-2c-4 1-8 1-11 1l-20 1c-3 1-5 0-7 0h-1 0 0c0-1 0-2 1-2h-4c-2 0-5 0-7-1 0-1 1-1 1-1h3c2 0 5 0 7-1 1 1 2 1 3 1l1 1h2c0-1 0-1 1-1 6 0 14 0 21-2z" class="h"></path><path d="M429 637c1 0 1 0 2 1v1h-3 0c0-1 0-2 1-2z" class="f"></path><path d="M422 635c2 0 5 0 7-1 1 1 2 1 3 1l1 1h2l-2 1h1c1 0 1 0 2 1h3-8c-1-1-1-1-2-1h-4c-2 0-5 0-7-1 0-1 1-1 1-1h3z" class="V"></path><defs><linearGradient id="L" x1="450.213" y1="629.817" x2="446.578" y2="641.138" xlink:href="#B"><stop offset="0" stop-color="#a4a5a3"></stop><stop offset="1" stop-color="#cbc8cb"></stop></linearGradient></defs><path fill="url(#L)" d="M457 633h3 2c1 1 3 1 5 1h2-1v1c-3 0-7 0-11 1-5 0-12 2-18 2h-3c-1-1-1-1-2-1h-1l2-1c0-1 0-1 1-1 6 0 14 0 21-2z"></path><path d="M415 635h1s1 0 2 1c2 1 5 1 7 1h4c-1 0-1 1-1 2l-5-1-1 1 1 1c2 1 3 1 4 2 2 1 3 1 5 1l-2 1h-3c-1-1-1 0-2 0h-1s-1 0-1 1h-6-2-3c-1-1-1-1-2-1-3 0-6 0-9-1-1 0-3 0-4-1h1l-1-1h1 0-3 0l3-2c-1-1-1-1-2-1h0l-1-1c4 0 19 1 21 0l-1-2z" class="R"></path><path d="M425 637h4c-1 0-1 1-1 2l-5-1h-3v-1h5z" class="T"></path><path d="M417 641c1 0 2 0 3 1h1c1 0 1 0 2 1-2 1-2 0-3 1h-2c-1-1-3 0-4-1h-1l-1 1c1 0 2 0 3 1h-3c-1-1-1-1-2-1-3 0-6 0-9-1h0c1-1 2-1 3-1 1 1 3 0 4 1h7 1c1 0 1-1 1-1v-1z" class="F"></path><path d="M396 638c1 0 3 0 4 1h0 5 1c0 1 2 1 3 1l8 1v1s0 1-1 1h-1-7c-1-1-3 0-4-1-1 0-2 0-3 1h0c-1 0-3 0-4-1h1l-1-1h1 0-3 0l3-2c-1-1-1-1-2-1z" class="Z"></path><path d="M158 451c1 0 2 0 4 1h5 3 6v2c1 0 1 0 2-1v1c1 1 1 2 2 3v-1-1l1 2 1-1-1-2h2l2 12c-2 0-2 1-3 2v2h-1l-1 1h-1c0 1 0 3 1 4v1c1 1 2 3 3 5h0c1 2 1 3 2 5h-1v1c-1 0 0 0-1 1h1c0 1-1 1-1 1-1 1-1 1-1 2h-5l1 1h-2v1h-1c-2 1-3 1-5 1l-2-1h0v-1l2-1h0c1 0 2 0 3-1l-1-1h-3v-1c-1 0-1 0-2-1v-1c-1 1 0 1-1 2h-1-5-2-1c-1 0-1 0-1-1v-1c0-1 0-2 1-3l-1-1v-1h0c0-4-3-7-4-11 0-3 0-7 2-10v-3h0c0-2 0-2-1-3v-2h1v1c1-1 2-1 4-2z" class="F"></path><path d="M168 464v-1c0-2 1-2 2-3l-1 7v3c-1-1-1-4-1-6z" class="E"></path><path d="M154 457c0-1 1-2 1-3l2-1h0c0 1-1 1-1 2s0 2 1 2l-1 2-2 1v-3h0z" class="Y"></path><path d="M167 452h3 0v1h1c0 2-1 4-1 6v1c-1 1-2 1-2 3v1 3l-1-1c0-2 0-4 1-6v-1h-1v-1-5h1-1v-1z" class="O"></path><path d="M167 452h3 0v1h1c0 2-1 4-1 6-1-1-1-4-2-6h-1v-1z" class="J"></path><path d="M162 457h0v-2h1l1 1c1-1 1-3 3-4-1 2-3 5-2 7h0c1 1 1 0 2 1l-1 2h0c-1 3 0 7-1 10h0c0-2 0-5-1-7v-5h-1l-1-1v-2z" class="T"></path><path d="M164 465c1 2 1 5 1 7h0c1-3 0-7 1-10h0l1 25v-1c-1 1 0 1-1 2h-1-5v-3h0l1 2h0c1 0 2 1 3 0v-3-19z" class="U"></path><path d="M161 471c0-3-1-9 1-11h1 1v5 19 3c-1 1-2 0-3 0h0v-16z" class="M"></path><path d="M161 471v5c1 1 2 1 2 3s0 7 1 8v-3 3c-1 1-2 0-3 0h0v-16z" class="D"></path><defs><linearGradient id="M" x1="164.692" y1="469.087" x2="152.851" y2="479.725" xlink:href="#B"><stop offset="0" stop-color="#949995"></stop><stop offset="1" stop-color="#bdb3b8"></stop></linearGradient></defs><path fill="url(#M)" d="M158 459l1 2c1-2 0-4 1-6h1c0 1 0 2 1 2v2l1 1h-1c-2 2-1 8-1 11v16l-1-2h0v3h-2-1c-1 0-1 0-1-1v-1c0-1 0-2 1-3l-1-1v-1h0c0-4-3-7-4-11 0-3 0-7 2-10l2-1 1-2h1v2z"></path><path d="M157 457h1v2 8c0-1 0-1-1-2v-1 1c0-2 0-5-1-6l1-2z" class="j"></path><path d="M157 465v-1 1c1 1 1 1 1 2v21h-1c-1 0-1 0-1-1v-1c0-1 0-2 1-3h0v-18z" class="c"></path><path d="M156 459c1 1 1 4 1 6v18h0l-1-1v-1h0c0-4-3-7-4-11 0-3 0-7 2-10l2-1z" class="M"></path><path d="M170 452h6v2c1 0 1 0 2-1v1c1 1 1 2 2 3v-1-1l1 2 1-1-1-2h2l2 12c-2 0-2 1-3 2v2h-1l-1 1h-1c0 1 0 3 1 4v1c1 1 2 3 3 5h0c1 2 1 3 2 5h-1v1c-1 0 0 0-1 1h1c0 1-1 1-1 1-1 1-1 1-1 2h-5l1 1h-2v1h-1c-2 1-3 1-5 1l-2-1h0v-1l2-1h0c1 0 2 0 3-1l-1-1h-3v-1h0 2l-1-1v-4l-1 4v-20l1-7v-1c0-2 1-4 1-6h-1v-1h0z" class="n"></path><path d="M170 470l1 5 1 1v6l-1 1v5l-1-1v-4-4-9z" class="D"></path><path d="M170 479h1v4 5l-1-1v-4-4z" class="B"></path><path d="M170 452h6v2c1 0 1 0 2-1v1c1 1 1 2 2 3h-1-1v-1-1h-1l-1 7-1-1c0-1-1-3 0-4v-3h-1v1c0 1 0 1-1 2v-2l-2-2h-1v-1h0z" class="G"></path><path d="M176 462l1-7h1v1 1c-1 5-1 10 0 15 0 1 0 2-1 2 0-1 0-2-1-3h0v-1l-1 1v-3-7l1 1z" class="g"></path><path d="M175 461l1 1v9h0v-1l-1 1v-3-7z" class="R"></path><path d="M170 470v-9c1 0 2-2 3-2l1 1-1 26h-1v-4-6l-1-1-1-5z" class="C"></path><path d="M174 460c1 2-1 7 0 8h1v3l1-1v1h0c1 1 1 2 1 3 1 0 1-1 1-2v7h0c-2 2 0 5-1 7v1c-1-1-1-4-1-5-1 1 0 3-1 4h-1 0v-1l-1 1 1-26z" class="o"></path><path d="M174 486v-8l1-1v4 5h-1 0z" class="R"></path><path d="M183 454l2 12c-2 0-2 1-3 2v2h-1l-1 1h-1c0 1 0 3 1 4v1c0 1 0 1-1 2h0v1h-1v-7c-1-5-1-10 0-15h1 1v-1-1l1 2 1-1-1-2h2z" class="I"></path><path d="M183 454l2 12c-2 0-2 1-3 2v2h-1c2-5 1-8-2-13h1v-1-1l1 2 1-1-1-2h2z" class="j"></path><path d="M180 476c1 1 2 3 3 5h0c1 2 1 3 2 5h-1v1c-1 0 0 0-1 1h1c0 1-1 1-1 1-1 1-1 1-1 2h-5l1 1h-2v1h-1c-2 1-3 1-5 1l-2-1h0v-1l2-1h0c1 0 2 0 3-1l-1-1h-3v-1h0 2v-5l1-1v4h1l1-1v1h0 1c1-1 0-3 1-4 0 1 0 4 1 5v-1c1-2-1-5 1-7h0 1v-1h0c1-1 1-1 1-2z" class="l"></path><path d="M168 492h8v1h-1c-2 1-3 1-5 1l-2-1h0v-1z" class="J"></path><path d="M180 476c1 1 2 3 3 5h0c1 2 1 3 2 5h-1l-1 1s0 1-1 1c-1-1-1 1-3 1h-1v-7-3h0 1v-1h0c1-1 1-1 1-2z" class="e"></path><path d="M178 483h2v-2c0-1 1-1 1-1h1v1 1 3h0-3l-1-2z" class="E"></path><path d="M180 476c1 1 2 3 3 5h-1v-1h-1s-1 0-1 1v2h-2v-1-3h0 1v-1h0c1-1 1-1 1-2z" class="R"></path><path d="M602 441l3-3c0 1 0 1-1 2 0 0 0 1-1 1v1 2 1h1l1-1h0v2c-1 0-1 1-1 2h0 2c1-1 1-2 2-2h1c-2 2-5 4-7 6s-3 3-4 6c-1 0-2 1-2 2l1 1v2l1-1 2-2h0c1 1 1 1 2 1v1l-7 15c-1 2-2 5-3 6v1c-1 1-2 3-3 5 0 1-1 2-2 3h0c0 2-2 5-2 7l1-1 1-1c0 3-3 4-4 6 1 0 2 0 3-1h1l-5 4c-2 1-5 3-7 5-3 3-7 8-11 11h0v-1c0-1 1-2 2-3l1-6 1-1v-1h1v-2c0-1 1-2 2-4h-1l-1-1h0v-2h0-3v-3l-1-1 7-6s2-1 2-2c1-1 0-4 0-5 1-2 1-4 1-6 1-2 4-3 4-5l3-11 1-1c2 0 2-2 4-4 1-2 2-3 4-5v-1c1 0 3-1 4-2 0-2 1-3 2-4l1 2 2-3 2-3z" class="I"></path><path d="M572 498v1l-1 1 2 2-2 2h-1l-1-1h0v-2h0l3-3z" class="H"></path><path d="M602 441l3-3c0 1 0 1-1 2 0 0 0 1-1 1v1 2 1h1l1-1h0v2c-1 0-1 1-1 2h0 2c1-1 1-2 2-2h1c-2 2-5 4-7 6s-3 3-4 6c-1 0-2 1-2 2-1 2-3 4-5 5 1-2 3-4 5-6 0-1 0-2-1-4h-1l-5 5h-1l4-5-1-1v-2-1c1 0 3-1 4-2 0-2 1-3 2-4l1 2 2-3 2-3z" class="G"></path><path d="M597 445l1 2-3 3c0 1-1 2-2 4-1 0-1 1-1 1l-1-1v-2-1c1 0 3-1 4-2 0-2 1-3 2-4z" class="Z"></path><path d="M590 476l2-2h1l-1 2c1 1 2 1 3 1-1 2-2 5-3 6v1c-1 1-2 3-3 5 0 1-1 2-2 3h0c0 2-2 5-2 7l1-1 1-1c0 3-3 4-4 6 1 0 2 0 3-1h1l-5 4c-2 1-5 3-7 5-3 3-7 8-11 11h0v-1c0-1 1-2 2-3l1-6 1-1v-1h1v-2c0-1 1-2 2-4l2-2-2-2 1-1v-1h0c1 0 1-1 2-1l1-1c0-2 1-2 2-3s1-2 2-3v-1c1-1 1-1 1-2s1-1 1-2c2-1 3-3 3-5 1-1 1-2 1-3l1-1c1-1 1-1 1-2l1 1v1h2z" class="C"></path><path d="M575 503c1 1 0 1 1 1 2-2 3-4 4-6 0-1 1-2 2-4v1c0 1-1 2-1 4l-1 1s-1 1-1 2h1l2-1c-1 3-3 4-5 6h-1l-2 3h-1v-2c2-1 1-3 2-5z" class="D"></path><path d="M585 495c0-1 1-2 1-3h1c0 2-2 5-2 7l1-1 1-1c0 3-3 4-4 6 1 0 2 0 3-1h1l-5 4c-2 1-5 3-7 5l-1-1 2-3h1c2-2 4-3 5-6 2-2 2-4 3-6z" class="O"></path><path d="M590 476l2-2h1l-1 2c1 1 2 1 3 1-1 2-2 5-3 6v1c-1 1-2 3-3 5 0 1-1 2-2 3h0-1c0 1-1 2-1 3 0-1 0-1-1-2h-1l-1 1h-1l1-3c1-1 1-3 2-4h0c0-1 1-2 1-3 1 0 1 0 1-1 1-2 2-3 3-5l1-2z" class="J"></path><path d="M586 485c1 0 2 0 2 1v1 1h-2l-1-1c1-1 1-1 1-2z" class="C"></path><path d="M588 482c0 1 0 2 1 2l2 1c-1 1-2 1-3 2v-1c0-1-1-1-2-1 1 0 1-1 1-1 1-1 1-1 1-2z" class="B"></path><path d="M593 474l-1 2c1 1 2 1 3 1-1 2-2 5-3 6 0 1-1 1-1 2l-2-1c-1 0-1-1-1-2 1-1 1-2 2-2v-2l1-1 1-1 1-2z" class="L"></path><path d="M579 490h0v2c-1 1-1 1-1 2 0 3-3 4-2 7l-1 2c-1 2 0 4-2 5v2h1l1 1c-3 3-7 8-11 11h0v-1c0-1 1-2 2-3l1-6 1-1v-1h1v-2c0-1 1-2 2-4l2-2-2-2 1-1v-1h0c1 0 1-1 2-1l1-1c0-2 1-2 2-3s1-2 2-3z" class="E"></path><path d="M574 497c1 0 1-1 3-1-1 1-2 3-3 5-1 0-1 0-1 1l-2-2 1-1v-1h0c1 0 1-1 2-1z" class="C"></path><path d="M568 511c0 2-1 5-1 6 2-2 3-4 4-6s1-3 2-4v1 2h1l1 1c-3 3-7 8-11 11h0v-1c0-1 1-2 2-3l1-6 1-1zM389 637h4 2l1 1h0c1 0 1 0 2 1l-3 2h0 3 0-1l1 1h-1c1 1 3 1 4 1 3 1 6 1 9 1 1 0 1 0 2 1h3 2v1h-2 5c1 1 1 1 3 2h0l1 1-10 2h-3c-3 1-6 1-9 1-4 1-8 0-11 1h-2c-2 0-3 0-4 1h-9c-12 0-25 0-37-2-3 0-6-1-10-1v1l-7-1h2l-2-1h-1c-1-1-2-1-3-1l10-1h-3c-2-1-4-1-6-2h-4l-1-1h1 4v-1h0 1 9 2 2v-1h4 8c-2 0-3 0-4-1h0l2-1h5 13c3 0 7 0 9-1h1l1-1c2 0 2 0 4-1 1 0 1 1 3 1v1c2 0 3-1 4-1h2l-1-1h1 3s0-1 1-1z" class="g"></path><path d="M355 646c4 0 9 0 13 1 4 2 9 1 13 1-1 1-2 0-3 1h-2 0 0c-2 0-6-1-7 0h0c3 1 6 0 9 0 1 1 2 0 3 1h5-35l2-1h1c-3-1-6-1-9-2 3-1 6 0 9-1h1z" class="N"></path><path d="M333 644h6l1 1c1 0 2 0 3 1h7 5-1c-3 1-6 0-9 1 3 1 6 1 9 2h-1l-2 1h-5l-18-2h-3c-2-1-4-1-6-2h-4l-1-1h1 4v-1h0 1 9 2 2z" class="P"></path><path d="M319 645l8 1h-8-4l-1-1h1 4z" class="o"></path><path d="M333 644h6v1l-2 1h-1v1c-3-1-6-1-8-2l1-1h2 2z" class="p"></path><path d="M339 644l1 1c1 0 2 0 3 1h7 5-1c-3 1-6 0-9 1 3 1 6 1 9 2h-1c-1-1-3 0-5 0-1 0-1-1-2-1h-3c-2-1-4 0-7-1v-1h1l2-1v-1z" class="O"></path><path d="M404 650h3c1 0 1 1 1 1h3c-3 1-6 1-9 1-4 1-8 0-11 1h-2c-2 0-3 0-4 1h-9c-12 0-25 0-37-2 2-1 7-1 9-1h20 12c4 0 7 1 11 0h-1c1-1 3 0 3 0l11-1z" class="V"></path><path d="M392 640h2c0 1 0 1 1 1h3 0-1l1 1h-1c1 1 3 1 4 1 3 1 6 1 9 1 1 0 1 0 2 1h3 2v1h-2 5c1 1 1 1 3 2h0l1 1-10 2h-3-3s0-1-1-1h-3l-11 1s-2-1-3 0c-1 0-3 0-4-1h-5c-1-1-2 0-3-1-3 0-6 1-9 0h0c1-1 5 0 7 0h0 0 2c1-1 2 0 3-1h0l1-1c0-1 2 0 3-1 1 0 2 0 3-1h-1v-1c-1 0-1 0-2-1 2-1 4-2 7-3z" class="M"></path><path d="M395 645l2 1v1c-2 1-3 1-5 0l1-1 2-1z" class="G"></path><path d="M388 645h7l-2 1-1 1c-3-1-7 0-11 1l1-1c0-1 2 0 3-1 1 0 2 0 3-1z" class="F"></path><path d="M400 648c-1 0-1-1-1-1l1-1c1 1 2 1 3 1h5l-1-1c1-1 1-1 2-1 1 1 4 1 6 1h0 5c1 1 1 1 3 2h0l1 1-10 2h-3-3s0-1-1-1h-3c2 0 2 0 4-1-3 0-6 0-8-1z" class="E"></path><path d="M408 649c2 0 4 1 6 1v1h-3-3s0-1-1-1h-3c2 0 2 0 4-1z" class="L"></path><path d="M400 648c-1 0-1-1-1-1l1-1c1 1 2 1 3 1h5l-1-1c1-1 1-1 2-1 1 1 4 1 6 1v1h2 1v1c-1 0-5 1-6 1-4-1-8-2-12-1h0z" class="N"></path><path d="M389 637h4 2l1 1h0c1 0 1 0 2 1l-3 2h0c-1 0-1 0-1-1h-2c-3 1-5 2-7 3 1 1 1 1 2 1v1h1c-1 1-2 1-3 1-1 1-3 0-3 1l-1 1h0c-4 0-9 1-13-1-4-1-9-1-13-1h-5-7c-1-1-2-1-3-1l-1-1h-6v-1h4 8c-2 0-3 0-4-1h0l2-1h5 13c3 0 7 0 9-1h1l1-1c2 0 2 0 4-1 1 0 1 1 3 1v1c2 0 3-1 4-1h2l-1-1h1 3s0-1 1-1z" class="g"></path><path d="M363 643h5 3l1 1-8 1-1-1v-1z" class="D"></path><path d="M380 642l1 1c-3 1-6 1-9 1l-1-1h-3l6-1h6z" class="V"></path><path d="M379 639v1c2 0 3-1 4-1l2 2-5 1h-6 3l2-3z" class="g"></path><path d="M385 643c1 1 1 1 2 1v1h1c-1 1-2 1-3 1-1 1-3 0-3 1-4 0-9-1-13-1 3-1 6 0 9-1 2-1 4-1 6-2h1z" class="H"></path><path d="M389 637h4 2l1 1h0c1 0 1 0 2 1l-3 2h0c-1 0-1 0-1-1h-2c-2 0-4 2-6 2-2 1-3 1-5 1l-1-1 5-1-2-2h2l-1-1h1 3s0-1 1-1z" class="C"></path><path d="M389 637h4c-1 1-3 2-4 3-1 0-3 1-4 1l-2-2h2l-1-1h1 3s0-1 1-1z" class="O"></path><path d="M384 638h1c2 0 2 0 4 2-1 0-3 1-4 1l-2-2h2l-1-1z" class="l"></path><defs><linearGradient id="N" x1="355.796" y1="648.542" x2="364.414" y2="632.381" xlink:href="#B"><stop offset="0" stop-color="#050203"></stop><stop offset="1" stop-color="#282828"></stop></linearGradient></defs><path fill="url(#N)" d="M372 639c2 0 2 0 4-1 1 0 1 1 3 1l-2 3h-3l-6 1h-5v1l1 1-14 1h-7c-1-1-2-1-3-1l-1-1h-6v-1h4 8c-2 0-3 0-4-1h0l2-1h5 13c3 0 7 0 9-1h1l1-1z"></path><defs><linearGradient id="O" x1="349.58" y1="640.325" x2="351.951" y2="647.402" xlink:href="#B"><stop offset="0" stop-color="#a19f9c"></stop><stop offset="1" stop-color="#c2c1c6"></stop></linearGradient></defs><path fill="url(#O)" d="M333 644v-1h4 8 18v1l1 1-14 1h-7c-1-1-2-1-3-1l-1-1h-6z"></path><path d="M180 546h3s1 0 2 1h2 6c1 0 1 0 2-1h0c1 1 2 1 3 1l2-1h1v7c1 4 0 8 0 12v4 13 4c-1 0-2-1-3-1l-1-2h-1v1 2 1c1 0 1 0 2 1l-1 1h-3v1c1 0 1 0 2 1h-3c-2 0-4 0-6 1h-2-4-2-1c-2 0-5 0-7-1l3-1c2-1 3-1 3-3h3c0-1 0-1-1-2h0c-1-2-1-5-1-7v-11-2c0-3-4-5-5-8l-2-1h-1c0-1 0-1-1-1h-1v-1-4h-1-1v-1l1-1h3 1c2-1 3-1 5-1 1 0 1 0 1-1h0 3z" class="n"></path><path d="M183 571c1 0 2-1 3 0 1 0 1 0 2 1v1h-2c1 2 2 1 2 3l-1-1v-1h-3c1 0 0 0 1-1-1 0-1 0-2-1v-1z" class="U"></path><path d="M189 561c1-1 1-1 1-2s1-1 1-1v12 2-2-4c-1 0-1 1-1 1v1h-1c0-2-1-3 0-5v-2h0z" class="T"></path><path d="M189 561l1 1v3 1c-1-1-1-2-1-3v-2z" class="l"></path><path d="M182 563c2 1 2 1 3 1v2h0 3v2 1l-1 1h-4c0-2 0-5-1-7z" class="e"></path><path d="M185 566h3v2h-1l-1 1v-1h-3l1-1 1-1z" class="F"></path><path d="M187 556h1v10h-3 0v-2c-1 0-1 0-3-1v-1-1h4 1v-3l-1-1v-1h1z" class="o"></path><path d="M182 562h5v1s-1 0-1 1h-1c-1 0-1 0-3-1v-1z" class="F"></path><path d="M191 570v2l-1 3c1 1 1 0 2 1 0 1 0 2-1 3l1 1-2 2-2-1h0c0-2 0-2-1-3h0l1-1v-1c0-2-1-1-2-3h2 0l1-1c1 0 1-1 1-1l1-1z" class="j"></path><path d="M188 581l1-2h2l1 1-2 2-2-1z" class="W"></path><path d="M183 581c0-1 0-3 1-3v-1h-1v-3h1 3v1l1 1v1l-1 1h0c1 1 1 1 1 3h0l2 1-1 1h-3v-1-1h-2-1z" class="d"></path><path d="M184 581h2c1-1 1 0 2 0h0l2 1-1 1h-3v-1-1h-2z" class="c"></path><path d="M181 570h2c0 1 1 0 0 1v1c1 1 1 1 2 1-1 1 0 1-1 1h-1v3h1v1c-1 0-1 2-1 3-1 0-1 0-2-1-1 2-1 3-2 5-1-2-1-5-1-7h1c1-1 1-1 1-2v-2l1-1v-3z" class="f"></path><path d="M178 578h1c1-1 1-1 1-2v-2l1-1v7c-1 2-1 3-2 5-1-2-1-5-1-7z" class="S"></path><path d="M171 556v-1c2 0 2 1 4 1v-1 1c1 0 2 0 2 1l2 1 2 2 1 1v1 1c1 2 1 5 1 7h0-2v3l-1 1v2c0 1 0 1-1 2h-1v-11-2c0-3-4-5-5-8l-2-1z" class="U"></path><path d="M173 557c3 1 5 3 6 6 1 2 1 3 1 5-1-1-1-2-1-2l-1-1c0-3-4-5-5-8z" class="c"></path><path d="M175 555v1c1 0 2 0 2 1l2 1 2 2 1 1v1 1c1 2 1 5 1 7h0-2v-6c0-1-1-4-3-5 0 0-1 0-1-1l-2-2v-1z" class="a"></path><path d="M192 580v1c2-1 2-1 3 0 0 1 0 1-1 2 1 0 1 0 2 1v2 1c1 0 1 0 2 1l-1 1h-3v1c1 0 1 0 2 1h-3c-2 0-4 0-6 1h-2-4-2-1c-2 0-5 0-7-1l3-1c2-1 3-1 3-3h3c0-1 0-1-1-2h0c1-2 1-3 2-5 1 1 1 1 2 1h1 2v1 1h3l1-1 2-2z" class="S"></path><path d="M183 585c1-1 2-1 3-1h2v1c-1 0-2 1-3 2 0 0-1 1-2 1 0-1 0-1 1-2v-1h-1z" class="V"></path><path d="M180 587s2-1 3-2h1v1c-1 1-1 1-1 2 0 0-1 1-2 1h-2l1-2z" class="B"></path><path d="M181 580c1 1 1 1 2 1h1 2v1 1 1c-1 0-2 0-3 1s-3 2-3 2c0-1 0-1-1-2h0c1-2 1-3 2-5z" class="W"></path><path d="M192 580v1c2-1 2-1 3 0 0 1 0 1-1 2 1 0 1 0 2 1v2 1h-1v-1h-1v1h-1l-2-1s-1 1-2 1v-1l1-1h-1-1v-1h-2v-1h3l1-1 2-2z" class="f"></path><path d="M189 583c2 0 3 0 5 1l1 1v1c-1 0-1-1-2-1h0-4-1v-1h-2v-1h3z" class="J"></path><path d="M189 585h4 0c1 0 1 1 2 1v-1l1 1v1h-1v-1h-1v1h-1l-2-1s-1 1-2 1v-1l1-1h-1z" class="G"></path><path d="M193 587h1v-1h1v1h1c1 0 1 0 2 1l-1 1h-3v1c1 0 1 0 2 1h-3c-2 0-4 0-6 1h-2-4-2-1c-2 0-5 0-7-1l3-1c2-1 3-1 3-3h3l-1 2h2l1 1 2-1s1-1 2-1l1 1c2-1 4 0 6-2z" class="e"></path><path d="M177 587h3l-1 2h-1l-1 1c1 1 3 1 5 1h1c1-1 3-1 4-1 2 0 4 0 7-1v1c1 0 1 0 2 1h-3c-2 0-4 0-6 1h-2-4-2-1c-2 0-5 0-7-1l3-1c2-1 3-1 3-3z" class="c"></path><path d="M180 546h3s1 0 2 1h2 0v4h0v2h0v3h-1v1l1 1v3h-1-4l-1-1-2-2-2-1c0-1-1-1-2-1v-1 1c-2 0-2-1-4-1v1h-1c0-1 0-1-1-1h-1v-1-4h-1-1v-1l1-1h3 1c2-1 3-1 5-1 1 0 1 0 1-1h0 3z" class="E"></path><path d="M179 552v-4c1 0 2 0 3 1v1l-1 1-2 1z" class="B"></path><path d="M182 551c1-1 2-1 3-2l1 1c1 0 1 1 1 1h-2-3z" class="H"></path><path d="M167 548h3l1 1v2h-1c-1-1-1-1-2-1h-1-1v-1l1-1z" class="Q"></path><path d="M180 546h3s1 0 2 1h2 0l-16 1c2-1 3-1 5-1 1 0 1 0 1-1h0 3z" class="O"></path><path d="M168 550c1 0 1 0 2 1s1 2 2 3c1 0 2 1 3 1v1c-2 0-2-1-4-1v1h-1c0-1 0-1-1-1h-1v-1-4z" class="j"></path><path d="M182 551h3 2v2h0v3h-1v1l1 1v3h-1-4l-1-1-2-2-2-1c0-1-1-1-2-1v-1c-1 0-2-1-3-1 1-1 1-1 2-1l-1-1 1-1c1 0 4 0 5 1l2-1h1z" class="F"></path><path d="M179 558c1-1 1-1 2-1v1c0 1 1 1 1 2h0-1l-2-2z" class="E"></path><path d="M185 551h2v2l-6-1h0c1-1 2 0 4-1z" class="G"></path><path d="M183 556l1 1c1 1 1 2 1 3h-2v-4z" class="Q"></path><path d="M186 556h-2v-2h-2v-1h5v3h-1z" class="E"></path><path d="M172 554c1-1 1-1 2-1h4 3l1 3h-3c-1 0-1 1-2 1 0-1-1-1-2-1v-1c-1 0-2-1-3-1z" class="H"></path><path d="M172 554c1-1 1-1 2-1h4l-1 2s-1 0-2 1v-1c-1 0-2-1-3-1z" class="B"></path><path d="M198 547l2-1h1v7c1 4 0 8 0 12v4 13 4c-1 0-2-1-3-1l-1-2h-1v1c-1-1-1-1-2-1 1-1 1-1 1-2-1-1-1-1-3 0v-1l-1-1c1-1 1-2 1-3-1-1-1 0-2-1l1-3v-2-12s-1 0-1 1 0 1-1 2v-5h-1-1v-3h0v-2h0v-4h0 6c1 0 1 0 2-1h0c1 1 2 1 3 1z" class="Z"></path><path d="M198 557h2v1 1l-2-1h0v-1z" class="H"></path><path d="M197 555h1l1 1-1 1v1c-1-1-1-2-1-3z" class="J"></path><path d="M198 547l2-1c0 1 0 2-1 3h-1 0v-2zm-5 4c0-1 1-1 3-3h0l-1 1 1 1v1h-3z" class="B"></path><path d="M191 555c1-1 1-1 1-2s0-1-1-1c0-2 1-3 1-5 1 1 0 3 1 4 1 2 0 3 0 5v1 2 7 3l-2 1v-12h0c1-1 1-2 0-3z" class="i"></path><path d="M187 547h6-1c0 2-1 3-1 5 1 0 1 0 1 1s0 1-1 2c1 1 1 2 0 3h0s-1 0-1 1 0 1-1 2v-5h-1-1v-3h0v-2h0v-4h0z" class="h"></path><path d="M187 551h0c1 1 1 2 2 2l1-1v1 2h1c1 1 1 2 0 3h0s-1 0-1 1 0 1-1 2v-5h-1-1v-3h0v-2z" class="l"></path><path d="M194 562l1-1c0-1 1-4 0-5v-1h0l2 1v-1c0 1 0 2 1 3h0l2 1-1 1c-1 1 0 2 0 4h-2c0 1-1 1-1 1-1 1 0 2 0 3v1h1c-1 1-2 1-1 2v2 1 2l-1 1-1-1v-3l-1-1v-3-3-7c1 1 1 2 1 3z" class="E"></path><path d="M198 558l2 1-1 1c-1 1 0 2 0 4h-2v-1c0-2 0-3 1-5zm-5 1c1 1 1 2 1 3s-1 6 0 7v1 1c0 1 0 1 1 1v1h1v1 2l-1 1-1-1v-3l-1-1v-3-3-7z" class="J"></path><path d="M201 553c1 4 0 8 0 12v4 13 4c-1 0-2-1-3-1l-1-2h-1v1c-1-1-1-1-2-1 1-1 1-1 1-2-1-1-1-1-3 0v-1l-1-1c1-1 1-2 1-3-1-1-1 0-2-1l1-3v-2l2-1v3l1 1v3l1 1 1-1v-2-1-2c-1-1 0-1 1-2h-1v-1c0-1-1-2 0-3 0 0 1 0 1-1h2c0-2-1-3 0-4v1l1-1h0v1l1-1v-7z" class="e"></path><path d="M199 573h2c0 1 0 3-1 4h0c-1-1-1-1-2-1h-1l1-1v-1h-2l3-1z" class="R"></path><path d="M199 560v1l1-1h0v1l1-1v13h-2l-1-1 2-1c-1-1-1-2-2-2 1-1 1-1 1-2 1-1 0-2 0-3 0-2-1-3 0-4z" class="Q"></path><path d="M197 564h2c0 1 1 2 0 3 0 1 0 1-1 2 1 0 1 1 2 2l-2 1 1 1-3 1h0v-1-2c-1-1 0-1 1-2h-1v-1c0-1-1-2 0-3 0 0 1 0 1-1z" class="F"></path><path d="M191 570l2-1v3l1 1v3l1 1 1-1c1 1 2 2 2 3l1 1v2l1 2h1v-2 4c-1 0-2-1-3-1l-1-2h-1v1c-1-1-1-1-2-1 1-1 1-1 1-2-1-1-1-1-3 0v-1l-1-1c1-1 1-2 1-3-1-1-1 0-2-1l1-3v-2z" class="O"></path><path d="M198 579c-1-1-2 0-4 0v-2l-1-1h-1v-4h1l1 1v3l1 1 1-1c1 1 2 2 2 3z" class="S"></path><path d="M401 279h1c4 1 9 0 13 0h2l25 4v1h0v1h1c1 0 2 1 3 1v1 2l-1 1h0 1v2h0l-2 1v2 1h0v1c1 0 2 1 3 2 0 3 0 5-1 7h0c-1-2-3-1-4-2v-2c-1 1-1 2-2 2h-1v2c0 1 1 4 0 5l-1 1v2c-1 0 0 0-1 1v-1h-1v-1l1-1h-3c-1 1-1 1-1 2-1 1-4 0-5 2h0v1l-2 1-1 2h0l-2 4-1 3v2l-1-1h-1v-1h-1v-4l-1 2-1-12v-6-10-3-1c1-1 0-4 0-5 0 0-1 0-1-1h-1v1h-1v-2l-1 1-1-1-1 1v1h0l-1-1c0-2-1-4 0-6l-1 1h-2l1 1c0 1-2 1-3 1h-2-3c-1 1-1 0 0 1h0l-17 1c-1 1-3 2-5 1h-3v-1c-2 0-3 1-4 2l-2 1-1 1v1h1l1 1-3 1v-1c1-1 1-1 1-2-1-1-1 0-2 0h0l-1-1 1-1h0 1v-1-1l1-1c3-2 7-3 10-4 6-1 12-1 18-2h5z" class="K"></path><path d="M442 285h1c1 0 2 1 3 1v1 2c-2 0-3 0-4-1s0-2 0-3z" class="o"></path><path d="M395 283c4-1 8-1 12-1l1 1c0 1-2 1-3 1h-2-3-4c-1 0-1-1-1-1z" class="L"></path><path d="M375 286c3-2 7-2 11-3-1 2-2 2-3 3s-3 2-5 1h-3v-1z" class="R"></path><path d="M410 281l7 1h1v2l-1-1h-6v1h0c1 1 2 1 4 1l2-1c1 1 0 3 0 4 0 0-1 0-1-1h-1v1h-1v-2l-1 1-1-1-1 1v1h0l-1-1c0-2-1-4 0-6z" class="D"></path><path d="M386 283c2 0 6-1 8 0h1s0 1 1 1h4c-1 1-1 0 0 1h0l-17 1c1-1 2-1 3-3z" class="E"></path><path d="M420 286c2-1 2-3 3-3 4 0 7 0 11 1 1 1 2 1 2 2s-1 0-1 2h0c1 1 2 1 3 2s1 1 0 2l-1 1c1 0 1 1 2 1l-1 1s-1 0-1-1h0c-2-1-3-2-5-2 0-1 0 0-1-1h4 0l-1-1c-1-1-1-2-1-2 0-1 0-1 1-2-2 0-2 0-3-1h-2-4-1c-2 0-2 0-4 1z" class="a"></path><path d="M435 288c1 1 2 1 3 2s1 1 0 2c0-2-1-1-2-1l-1-3z" class="b"></path><path d="M442 302v-2h1l-1-1c0-1-1-1 0-2l1-1c-1-1-1-1-2-1l2-2h-1c0-1-1-2 0-3 1 0 3 1 4 2h0l-2 1v2 1h0v1c1 0 2 1 3 2 0 3 0 5-1 7h0c-1-2-3-1-4-2v-2z" class="G"></path><path d="M420 286c2-1 2-1 4-1h1 4 2c1 1 1 1 3 1-1 1-1 1-1 2 0 0 0 1 1 2-1 0-3-1-4-1h-2c-2 1-4 1-7 1l1 1c-1 0-2 1-3 2 0-2 0-2 1-3-1-1-1-2 0-4z" class="O"></path><path d="M421 290h-1l1-1h7c-2 1-4 1-7 1z" class="R"></path><path d="M420 287h4 3 3v1c-3 0-8 1-10 0v-1z" class="S"></path><path d="M420 286c2-1 2-1 4-1h1 4 0l-2 2h-3-4v-1z" class="P"></path><path d="M428 289h2c1 0 3 1 4 1l1 1h0-4c1 1 1 0 1 1 2 0 3 1 5 2h0-2l-1 1h-1c-1 0-3 0-4-1h0-4-5c-1 0-1-1-1-1 1-1 2-2 3-2l-1-1c3 0 5 0 7-1z" class="i"></path><path d="M429 294c2 0 5-1 6 0l-1 1h-1c-1 0-3 0-4-1z" class="h"></path><path d="M422 291h7l1 1h0v1c-1 0-1 0-1 1h-4-5c-1 0-1-1-1-1 1-1 2-2 3-2z" class="e"></path><path d="M429 291l1 1h0v1c-1 0-1 0-1 1h-4c1 0 1 0 1-1h0c1-1 1 0 1 0 1 0 2-1 2-1v-1z" class="U"></path><path d="M425 294h4 0c1 1 3 1 4 1h1l1-1h2c0 1 1 1 1 1l1-1v2h0v1h0l-1 2-1-1c-1-1-2-1-4-1-1-1-2-1-4 0-1 0-2 2-2 3-1 1-1 2-1 3h0 0-1c-2-1-4-1-6-1v-3c1-1 0-2 0-3l1-1h0v-1h5z" class="d"></path><path d="M425 294h4 0c1 1 3 1 4 1h1l1-1h2c0 1 1 1 1 1l1-1v2h0v1h0l-1 2-1-1c-1-1-2-1-4-1-1-1-2-1-4 0-1 0-2 2-2 3-1 1-1 2-1 3v-4h0l-1 1h-1c1-1 2-3 3-3h1v-1c-2-1-5 0-7-1h-1 0v-1h5z" class="P"></path><path d="M435 294h2c0 1 1 1 1 1l1-1v2h0v1h0-1c-1 0-3-2-4-2l1-1z" class="c"></path><path d="M429 297c2-1 3-1 4 0 2 0 3 0 4 1l1 1v4h0c-1 0-1 1-1 1 0 1 0 1 1 1v1h1c0 1 1 4 0 5l-1 1v2c-1 0 0 0-1 1v-1h-1v-1l1-1s-1-2-1-3h0l-1 1-1 1-1-1h-1-4l-1-1h0 0c-1-1-1-5-1-6h0c0-1 0-2 1-3 0-1 1-3 2-3z" class="r"></path><path d="M429 306v-1c0-1 0-2 1-3h0c1 1 1 2 1 3s0 3-1 4l1 1 1-1 1-1v2h-1-4l-1-1h0 0v-2c1 0 1-1 2-1z" class="T"></path><path d="M429 306l1 1c0 1-1 2-1 2h-2 0v-2c1 0 1-1 2-1z" class="K"></path><path d="M426 303c0-1 0-2 1-3 0-1 1-3 2-3v2c1 1 2 1 2 1l1 1c-1 0-1 0-2 1h0c-1 1-1 2-1 3v1c-1 0-1 1-2 1v2c-1-1-1-5-1-6h0z" class="b"></path><path d="M433 297c2 0 3 0 4 1l1 1v4h0c-1 0-1 1-1 1 0 1 0 1 1 1v1h1c0 1 1 4 0 5l-1 1v2c-1 0 0 0-1 1v-1h-1v-1l1-1s-1-2-1-3h0l-1 1c0-3-1-7 0-10h0c0-1-1-2-2-3z" class="n"></path><path d="M438 306h1c0 1 1 4 0 5l-1 1v-6z" class="p"></path><path d="M419 302c2 0 4 0 6 1h1 0c0 1 0 5 1 6h0 0l1 1h4 1l1 1 1-1 1-1h0c0 1 1 3 1 3h-3c-1 1-1 1-1 2-1 1-4 0-5 2h0v1l-2 1-1 2h0l-2 4-1 3v2l-1-1h-1v-1h-1v-4-3c0-1 1-1 1-2 0 0-1-1-1-2 1 0 1-1 1-1h1l-1-1c-1-2-1-4-1-6v-2-4z" class="U"></path><path d="M421 322c0-1 0-1-1-1h0v-1-1c1 0 1 1 2 1h3l-2 4c-1 0-1-1-2-2z" class="C"></path><path d="M426 318h-2c-2 0-2 0-3-1v-1h6l1 1-2 1z" class="B"></path><path d="M421 322c1 1 1 2 2 2l-1 3v2l-1-1h-1v-1-1c0-1 0-1 1-2h-1v-1h1v-1z" class="H"></path><path d="M419 308l1 1h0v2h5 4l-1-1h4 1l1 1h0c-1 1-2 0-4 1 0 2-1 2-2 3-3 0-5-1-7 0l-1-1c-1-2-1-4-1-6z" class="o"></path><path d="M428 310h4 1l1 1h0c-1 1-2 0-4 1h0l-1-1-2 1-1 1h-3c-1 0-2-1-3-2h5 4l-1-1z" class="E"></path><path d="M419 302c2 0 4 0 6 1h1 0c0 1 0 5 1 6h0 0l1 1 1 1h-4-5v-2h0l-1-1v-2-4z" class="R"></path><path d="M424 309c1 0 1 0 2-1h0v1h1l1 1 1 1h-4v-2h-1z" class="H"></path><path d="M419 306h3l1 1v1l-3 1-1-1v-2z" class="l"></path><path d="M420 309h4 1v2h-5v-2z" class="N"></path><path d="M236 366c1-3 2-8 4-11 0-1 1-1 1-1 0 2-1 3-1 5l1 2h0c-1 2-2 3-2 5-1 2 0 5 0 7 1 5 1 10 1 15 1 2 2 5 2 7s0 4-1 5c3 4 6 7 5 11 0 4 3 7 3 11h-1c0 3 0 5 1 7v1l-1-3c-1 0-2 1-2 2h0c0 2 0 2 2 4-1 1-1 2 0 3v1c0 1 0 1-1 1l-1 1-1 1-1-1c1 2 2 3 3 4-2 0-3-2-4-4l-5-3c-2-2-4-5-5-7l-1-2c0-1-1-1 0-2 0-1 0-1-1-2h0c-1-1-2-4-2-6 1-1 2-3 3-4v-3c0-1 0-3-1-4v-3h-1c-1-1-1-1 0-2h-1l-1 1v-2h-1l-2-1-3-2v-1c0-1-1-2-1-4-1 0 0 0 0-1h0l-3-3v2 1l-2-3-1-1h-1v1h-1l-1-4 1-1v-2l1-1h2l1-1 1-2c0-1 0-3-1-4 1-1 1-2 1-3v-1h2c1-1 2-2 3-4v1h0c1 0 2-1 3-1v4h1 2v3l1 1v-1c1-1 1-1 1-2h1l4-4z" class="B"></path><path d="M240 395h1 0c0 3 0 4-1 6 1 2 2 3 3 4l-1 2c-1-2-2-4-4-5l-1-1c1-2 2-3 2-4l1-2z" class="b"></path><path d="M236 366c1-3 2-8 4-11 0-1 1-1 1-1 0 2-1 3-1 5l-3 7c-1 3 1 8 1 11 0 2-1 4 0 6s1 5 2 8c0 1 1 3 1 4h-1l-1-2-2-7c-2-3-1-4-1-7 1-2 0-4 0-5v-1h0v-5-2z" class="c"></path><path d="M238 402c2 1 3 3 4 5l1-2c1 2 2 4 2 6l-1 3c2 3 3 5 4 8 0 3 0 5 1 7v1l-1-3c-1-2-1-3-2-5v-1l-3-1h-1v-1-3c0-1 0-1-1-1l-1-4v-1c-1-1-1-2-2-3v-1c-1-2-1-3 0-4z" class="K"></path><path d="M243 420c0-1 0-3 1-4 2 1 2 3 2 5l-3-1z" class="E"></path><path d="M238 402c2 1 3 3 4 5 1 1 1 2 1 4h0c-1 0-2 0-3-1h0c-1-1-1-2-2-3v-1c-1-2-1-3 0-4z" class="C"></path><path d="M236 366v2 5h0v1c0 1 1 3 0 5 0 3-1 4 1 7l2 7h-1l-2-1v-3l-1-1-2-1v1l-1-1c0-3-1-6-1-9h0-1l-1-1c0-2-1-1-1-2v-2l1 1h0v-2l1 1v-1c1-1 1-1 1-2h1l4-4z" class="L"></path><path d="M231 375c1 1 1 2 1 3h1v-1c1 1 2 2 2 3h0c-1 2-1 4-1 5s1 2 1 3l-2-1v1l-1-1c0-3-1-6-1-9v-3z" class="D"></path><path d="M232 378h1 0c0 1 0 3-1 4h0v-4z" class="L"></path><path d="M236 366v2 5h0v3h-1v-1c-1 1-1 2-2 2h0v1h-1c0-1 0-2-1-3v3h0-1l-1-1c0-2-1-1-1-2v-2l1 1h0v-2l1 1v-1c1-1 1-1 1-2h1l4-4z" class="B"></path><path d="M233 377l-1-1 1-2c0-1 1-1 2-2l1 1h0v3h-1v-1c-1 1-1 2-2 2z" class="M"></path><path d="M236 366v2c-2 1-3 3-4 5-1 0-1 1-1 2v3h0-1l-1-1c0-2-1-1-1-2v-2l1 1h0v-2l1 1v-1c1-1 1-1 1-2h1l4-4z" class="O"></path><path d="M229 377l1 1h1 0c0 3 1 6 1 9l1 1v-1l2 1 1 1v3l2 1h1l1 2-1 2c0 1-1 2-2 4l1 1c-1 1-1 2 0 4v1h-1c-1 1-1 1-1 2v-1c0-1-1-1-1-2h0c0 1 0 3-1 4v1c-1 0-1-1-1-1h-1c0-1 0-3-1-4v-3h-1c-1-1-1-1 0-2h-1l-1 1v-2h-1l1-1c0-1 0-3-1-4l1-1v1l1-1c-1-3-3-5-3-8 0-2 1-3 1-4h1v-1l1-4z" class="U"></path><path d="M236 396c0 2 0 4-1 5h0c0-1-1-2-1-4h2v-1z" class="G"></path><path d="M239 393l1 2-1 2-3-2v-1h0 2v-1h0 1z" class="H"></path><path d="M236 395l3 2c0 1-1 2-2 4 0 1 0 1-1 2l-1-1v-1c1-1 1-3 1-5v-1z" class="J"></path><path d="M235 401v1l1 1c1-1 1-1 1-2l1 1c-1 1-1 2 0 4v1h-1c-1 1-1 1-1 2v-1c0-1-1-1-1-2v-5h0z" class="F"></path><path d="M231 403h0c1-1 1-2 0-3l1-1c1 2 1 3 3 4v3c0 1 0 3-1 4v1c-1 0-1-1-1-1h-1c0-1 0-3-1-4v-3z" class="P"></path><path d="M231 406c2 0 2 0 3 1v3 1c-1 0-1-1-1-1h-1c0-1 0-3-1-4z" class="h"></path><path d="M229 377l1 1h1 0c0 3 1 6 1 9l-2 2c1 1 2 4 3 4v-1c1 0 1 0 1 1v1 3c-1-1-2-1-2-1h-1c-1 0-1-1-2-2-1-3-3-5-3-8 0-2 1-3 1-4h1v-1l1-4z" class="P"></path><path d="M231 378c0 3 1 6 1 9l-2 2v-4c-1-1-1-2-1-3l1-1c0-2 0-2 1-3h0z" class="G"></path><path d="M226 386l1-1c2 2 3 5 4 8 0 1 1 1 1 2h0v1h-1c-1 0-1-1-2-2-1-3-3-5-3-8z" class="E"></path><path d="M223 366h0c1 0 2-1 3-1v4h1 2v3 2h0l-1-1v2c0 1 1 0 1 2l-1 4v1h-1c0 1-1 2-1 4 0 3 2 5 3 8l-1 1v-1l-1 1c1 1 1 3 1 4l-1 1-2-1-3-2v-1c0-1-1-2-1-4-1 0 0 0 0-1h0l-3-3v2 1l-2-3-1-1h-1v1h-1l-1-4 1-1v-2l1-1h2l1-1 1-2c0-1 0-3-1-4 1-1 1-2 1-3v-1h2c1-1 2-2 3-4v1z" class="c"></path><path d="M222 368c1 0 1 0 2 1h-2l-1 4-2-2c0-1-1-1 0-1l3-1v-1z" class="n"></path><path d="M219 371l2 2v3c2 1 2 3 4 5l-2-1c-1 2 0 3 0 5-1-1-1-2-2-3h0c0-1-1-2-1-4-1-2-1-4-1-7z" class="O"></path><path d="M221 376c2 1 2 3 4 5l-2-1c-1 2 0 3 0 5-1-1-1-2-2-3 0-1 0-2 1-4 0-1-1-2-1-2z" class="P"></path><path d="M223 385c0-2-1-3 0-5l2 1h0 1l1 1c0 1-1 2-1 4 0 3 2 5 3 8l-1 1v-1l-1 1c0-1-1-2-2-4 0-1-1-3-1-5l-1-1h0z" class="O"></path><path d="M223 391h0 1 1c1 2 2 3 2 4 1 1 1 3 1 4l-1 1-2-1-3-2v-1c0-1-1-2-1-4-1 0 0 0 0-1 0 1 1 1 2 1v-1z" class="K"></path><path d="M224 391h1c1 2 2 3 2 4 1 1 1 3 1 4-1-1 0-1-1-1-1-1-1-2-1-3-1-1-1-2-2-4z" class="W"></path><path d="M221 391c0 1 1 1 2 1 0 3 1 5 2 7l-3-2v-1c0-1-1-2-1-4-1 0 0 0 0-1z" class="b"></path><path d="M217 379l1-2 1 5c1 3 2 6 4 9v1c-1 0-2 0-2-1h0l-3-3v2 1l-2-3-1-1h-1v1h-1l-1-4 1-1v-2l1-1h2l1-1z" class="l"></path><path d="M217 379l1-2 1 5c-1-1-2-3-2-3v1 2c-2 2-1 4-1 6l-1-1h-1v1h-1l-1-4 1-1v-2l1-1h2l1-1z" class="h"></path><path d="M214 380h2v3h-1c0-1-1-1-2-2l1-1z" class="n"></path><path d="M213 383h0c1 1 1 2 2 2v2h-1v1h-1l-1-4 1-1z" class="g"></path><path d="M223 366h0c1 0 2-1 3-1v4h1 2v3 2h0l-1-1v2c0 1 1 0 1 2l-1 4v1h-1l-1-1h-1 0c-2-2-2-4-4-5v-3l1-4h2c-1-1-1-1-2-1 1-1 1-1 1-2z" class="U"></path><path d="M223 366h0c1 0 2-1 3-1v4h1 2v3 2h0l-1-1v-1h-1-1c0-2-1-2-2-3s-1-1-2-1c1-1 1-1 1-2z" class="a"></path><path d="M228 381h-1v-4h-1v3h-1c0-1 0-2-1-2l-1-1v-4c1 0 1-1 2-1h1s0 1 1 1v1l1-1v2c0 1 1 0 1 2l-1 4z" class="J"></path><path d="M235 406c0 1 1 1 1 2v1c0-1 0-1 1-2h1c1 1 1 2 2 3v1l1 4c1 0 1 0 1 1v3 1h1l3 1v1c1 2 1 3 2 5-1 0-2 1-2 2h0c0 2 0 2 2 4-1 1-1 2 0 3v1c0 1 0 1-1 1l-1 1-1 1-1-1c1 2 2 3 3 4-2 0-3-2-4-4l-5-3c-2-2-4-5-5-7l-1-2c0-1-1-1 0-2 0-1 0-1-1-2h0c-1-1-2-4-2-6 1-1 2-3 3-4v-3h1s0 1 1 1v-1c1-1 1-3 1-4h0z" class="e"></path><path d="M238 425c1 0 1 0 2 1-1 1 0 1-2 1v1 2h-1s-1 0 0-1v-3c0-1 0-1 1-1z" class="R"></path><path d="M239 422h3 0v3c-1 1-1 1-2 1-1-1-1-1-2-1 0-1 0-2 1-3z" class="E"></path><path d="M234 420l1 1c0 1 0 1-1 2 0 1 0 1 1 2 0 1-1 2-1 3l-1 1-1-2c0-1-1-1 0-2 0-1 0-1-1-2 1 0 1 0 2-1 0 0 1-1 1-2z" class="T"></path><path d="M246 422c1 2 1 3 2 5-1 0-2 1-2 2h0c0 2 0 2 2 4-1 1-1 2 0 3v1c0 1 0 1-1 1l-1 1-1 1-1-1c-1-1-2-2-2-3-1-1-2-1-3-2h1 3v1l1-1c-1-1-2-1-2-2-1-2-2-3-4-4v-1c2 0 1 0 2-1 1 0 1 0 2-1v-3h0 1v1h1 1c1 0 1-1 1-1z" class="K"></path><path d="M246 422c1 2 1 3 2 5-1 0-2 1-2 2h0c-1-1-1-2-1-3l-1-1v2l-1-1v-3h1 1c1 0 1-1 1-1z" class="R"></path><path d="M235 406c0 1 1 1 1 2v1c0-1 0-1 1-2h1c1 1 1 2 2 3v1l1 4c1 0 1 0 1 1v3 1h1l3 1v1s0 1-1 1h-1-1v-1h-1-3v-1h-1s0 1-1 1v2h-2l1-2h0c-1-1-1 0-1-1v-1h-1c0 1-1 2-1 2-1 1-1 1-2 1h0c-1-1-2-4-2-6 1-1 2-3 3-4v-3h1s0 1 1 1v-1c1-1 1-3 1-4h0z" class="J"></path><path d="M237 407l1 1v1l-1 1h-1v-1c0-1 0-1 1-2z" class="Z"></path><path d="M235 406c0 1 1 1 1 2v1 1h0c-1 2 0 3-1 4l-2 1c0-1 1-3 1-4v-1c1-1 1-3 1-4h0z" class="R"></path><path d="M232 410h1s0 1 1 1c0 1-1 3-1 4-1 2-1 4-1 7h1c-1 1-1 1-2 1h0c-1-1-2-4-2-6 1-1 2-3 3-4v-3z" class="O"></path><path d="M239 421l2-2h0v-1-1h-1v1h-1v-1l-1 1c0 1 0 0-1 1v-1-4c1 0 1-1 1-1l-1-1 2-1h1l1 4c1 0 1 0 1 1v3 1h1l3 1v1s0 1-1 1h-1-1v-1h-1-3v-1z" class="H"></path><path d="M393 370v1l1 2c2 1 4 0 5 0 1 1 0 0 1 0 2 2 10 1 12 1l21 1h1l6 1 7 1h5c2 1 7 1 8 4 1 0 1 1 1 2-2 2-5 3-7 4-6 5-9 11-11 18h-1v-1h-1c-1-1-1-2-1-2v-1-2h-2 0c0 2 1 3 1 5l-9-1-9-1h-3-7c-1 1-1 1-2 1-2 0-4 0-6 1h-1c-1-1-2-1-3-1h0v-1h-6l-1 1c0-1 0-3-1-4 0-1-1-2 0-3v-2c-1-1-2-1-4-1l-1-2 4-2c-2-1-2-2-2-4v-1h-1c-1-2-1-3-2-4 1-1 1-1 1-2l1-1c1 0 1 0 2-1h6c-3 0-8 1-10 0 1-1 2-1 3-1h-5v-1h3 0 1l1-1v-1c1 1 1 1 2 1s1 1 2 0c0-1 1-2 1-3z" class="r"></path><path d="M440 376l7 1-1 1-13-1c1-1 2-1 3-1h4z" class="C"></path><path d="M417 394c1-4 5-6 7-9v2c0 2-2 4-3 5l-1 1h-1c-1 1-2 1-2 1z" class="Y"></path><path d="M385 380c1-1 1-1 1-2l1-1 2 3c0 1 1 2 1 3 0 0 0 1 1 2 0 0 0 1-1 2v2h0c-2-1-2-2-2-4v-1h-1c-1-2-1-3-2-4z" class="c"></path><path d="M438 399v-1c0-1 0-1-1-1h0-1v-1c-2 0-1 0-3 1h0v-1c0-1 1-1 1-2s0-2 1-3v-1c1-1 1-2 1-2 0-1 0-1 1-1v-1-1l1-1 3-3h1 1 1 0c0 1-2 4-3 4v-1c-1 0-1 1-2 1v2c0 3-2 8 0 10 0 1 1 1 1 1v1h-2 0z" class="K"></path><path d="M415 392l-2 3h1c1 0 1-1 1-2 1-1 2-3 3-4 4-4 8-9 13-11-1 2-5 5-7 7-2 3-6 5-7 9-1 1-2 3-3 5v1c-1 1-1 1-2 0h-1 0v-3h1c-1-2-1-5-1-7 1 0 1 0 1 1h0l1-2h1v1s1 1 1 2z" class="B"></path><path d="M428 393l2-11 1-1h1c0 2-1 3-2 5 2-1 3-4 3-6h0l1-1 1 1-3 6h1s1-1 1-2c1-2 2-3 4-4-1 2-4 4-5 6l-2 7h0-1c0 1 1 2 1 3h0c0 1-1 1-1 2v1c1 1 0 2 0 3-1-1-2-1-2-2v-7z" class="E"></path><path d="M421 398c-1-1-2-1-2-2h0c1-1 2-1 2-1 2-1 2-2 4-2l1 1c1 0 2-1 2-1v7c0 1 1 1 2 2v1l-9-1h-1l-2-2h1l2-2z" class="Y"></path><path d="M421 398v-1c1 0 1 0 1 1 1 0 1 1 2 1v1 1c-1 0-2-1-3-1v1c-1 0-1 0-2-1l2-2z" class="h"></path><path d="M447 377h5c2 1 7 1 8 4 1 0 1 1 1 2-2 2-5 3-7 4-6 5-9 11-11 18h-1v-1c2-8 5-15 13-19 1-1 4-2 5-3-2-3-10-3-14-4l1-1z" class="B"></path><path d="M411 385v-3c1-1 2-1 3-1h0l-1 2h1c1-1 2-3 4-4-1 2-4 6-4 8 1 0 1-1 1-1 1-2 4-7 6-7 1-1 2-1 3-1l-6 7c3-2 6-6 9-8-2 4-6 8-9 11-1 1-2 2-3 4 0-1-1-2-1-2v-1h-1v-1h-1-1c-1-1 0-2 0-3z" class="V"></path><path d="M393 370v1l1 2c2 1 4 0 5 0 1 1 0 0 1 0 2 2 10 1 12 1l21 1h1l6 1h-4c-1 0-2 0-3 1-3 0-7-1-10-1h-28c-3 0-8 1-10 0 1-1 2-1 3-1h-5v-1h3 0 1l1-1v-1c1 1 1 1 2 1s1 1 2 0c0-1 1-2 1-3z" class="B"></path><path d="M393 370v1l1 2c2 1 4 0 5 0 1 1 0 0 1 0 2 2 10 1 12 1l-24 1h-5v-1h3 0 1l1-1v-1c1 1 1 1 2 1s1 1 2 0c0-1 1-2 1-3z" class="j"></path><path d="M444 381h3v1c1 0 2-1 2 0h0c2 0 2-1 3 0h0 1 2v1c-6 5-12 10-15 18v-2-1s-1 0-1-1c-2-2 0-7 0-10v-2c1 0 1-1 2-1v1c1 0 3-3 3-4z" class="e"></path><path d="M394 386c0-1-1-1 0-2 0-1 1-2 2-3 0 0 3-1 4-1h2c1 0 1-1 2 0l-2 2h0c-1 1-1 2-2 2v1h0c1 1 1 0 2 0 2-1 4-3 6-3h0-1c0 1 0 2 1 3h3c0 1-1 2 0 3h1 1v1l-1 2h0c0-1 0-1-1-1 0 2 0 5 1 7h-1v3h0 1c1 1 1 1 2 0v-1l1 1h3l2 2h1-3-7c-1 1-1 1-2 1-2 0-4 0-6 1h-1c-1-1-2-1-3-1h0v-1h-6l-1 1c0-1 0-3-1-4 0-1-1-2 0-3v-2c-1-1-2-1-4-1l-1-2 4-2h0v-2l1 1h1c1 0 1-1 2-2z" class="W"></path><path d="M391 396h1 0 2v3s1 0 2 1h-1-1c0 1 0 2-1 2l-1 1c0-1 0-3-1-4 0-1-1-2 0-3z" class="K"></path><path d="M394 386c1-1 1 0 1-1l2-2c1 0 1 0 1 1 1-1 2-1 2-2l1 1h0c-2 1-4 3-5 5h0c-1 1-1 3-2 4v1 1c-1 0-1 1-1 1 0 1-1 1-1 1h-1v-2c-1-1-2-1-4-1l-1-2 4-2h0v-2l1 1h1c1 0 1-1 2-2z" class="X"></path><path d="M390 389h0v-2l1 1v4l-1-1c0 1 0 2 1 3-1-1-2-1-4-1l-1-2 4-2z" class="P"></path><path d="M414 399l1 1h3l2 2h1-3-7c-1 1-1 1-2 1-2 0-4 0-6 1h-1c-1-1-2-1-3-1h0v-1h-6c1 0 1-1 1-2h1 1 9c1 0 3-1 4 0h2 1c1 1 1 1 2 0v-1z" class="f"></path><path d="M399 402h12c-1 1-1 1-2 1-2 0-4 0-6 1h-1c-1-1-2-1-3-1h0v-1z" class="F"></path><path d="M402 385c2-1 4-3 6-3h0-1c-1 2-1 2-1 4v2c2 1 2 3 3 5-1 0-1 0-1 1h0l2 2h0c-3 1-6 0-8 1h-1c-2 0-5 0-6-1v-1c0-2 2-5 3-6 1 0 2-1 3-2l1-2z" class="E"></path><path d="M403 394c-1-1-2 0-4-1h-1c0-1 0-1 1-1h7 0l1 1c-1 1-2 0-4 1z" class="D"></path><path d="M402 385c2-1 4-3 6-3h0-1c-1 2-1 2-1 4v2c2 1 2 3 3 5-1 0-1 0-1 1h0c-1 0-3 1-5 0 2-1 3 0 4-1l-1-1-1-3-1-1c-1 0-2 0-3-1h0l1-2z" class="n"></path><path d="M265 292h2v1c1 0 2 0 3-1h1 2 1c-1 1-1 2-2 2s-3 0-4 1v1h1 2 0l-1 1c0 1-1 2-2 2 0 2-1 3-1 4 1 0 1 0 2 1-1 4-3 9-7 11l-1 1c-4 1-5 4-8 7-1 2-2 3-3 4-2 2-2 5-3 7 0 2-2 4-2 6h0v1h0v1 1 3c1 1 0 3 0 3l-1 1c-1 1-2 3-3 4h0 0s-1 0-1 1c-2 3-3 8-4 11l-4 4h-1c0 1 0 1-1 2v1l-1-1v-3h-2-1v-4c-1 0-2 1-3 1h0v-1c-1 2-2 3-3 4h-2v1c0 1 0 2-1 3 1 1 1 3 1 4l-1 2-1 1h-2l-1 1v2l-1 1-2-3c-1-1 0-2 0-3h0l2-2c2-3 5-7 4-11 0-2-1-3-2-5s-2-4-1-7c0-3 1-7 2-10 2-4 6-5 8-8l1-1 3-6c0-2 2-3 3-5 1-3 0-6 1-8 1-3 5-3 7-4 1-1 3-1 4-2h2c2 0 6-3 8-4l-1-1 2-2 3-4 3-3c2-1 4-3 6-3z" class="K"></path><path d="M214 380c0-1 1-3 2-4l1 3-1 1h-2z" class="f"></path><path d="M217 373c1 1 1 3 1 4l-1 2-1-3 1-3z" class="g"></path><path d="M217 356v-2h1c0 3 0 6 1 8l-1 1v-1c-1-2-2-3-1-6z" class="f"></path><path d="M217 356c-1 3 0 4 1 6v1l1-1v1c1 1 1 2 1 3s0 1-1 2l-1-1v-3c-1-1-1-2-2-3h0v-1c0-1 0-2 1-4z" class="X"></path><path d="M233 323c0-2 0-3 2-5 0 0 2-2 3-2 0-1 1-1 2-1l-6 7c0 1-1 1-1 1z" class="T"></path><path d="M241 329l3-2 1 2 2-1v2c-1 0-1 0-2-1-1 1-1 2-1 3l-4 4c-3 1-5 2-6 5l-1-1 1-2c1 0 1 0 1-1h2c0-1 0 0 1-1h0c1-1 1-1 2-1 0-1 1-2 2-3 0-1-1-1-2-1 0 1 0 2-1 3h-2c-1 1-2 1-3 2h0v-1h-1l2-4 2-2 3 2 1-2z" class="c"></path><path d="M235 331l2-2 3 2c-2 1-4 3-6 4h-1l2-4z" class="E"></path><path d="M228 344v-1l2-2h2s0-1 1-1l1 1h0c-2 2-2 5-3 7v1l-2 2h0v-3h-1l-1 3-3 1h0l-1-1-1 1-1 3v-2l-1 1v-1c0-1 1-2 2-3 2-1 5-5 6-6z" class="n"></path><path d="M228 344v-1l2-2h2s0-1 1-1l1 1h0-3v1s1 1 0 2l-2 2v-1s0-1-1-1z" class="Y"></path><path d="M234 327c1-1 1-1 1-2 1 1 1 2 1 3l-2 2 1 1-2 4h1v1l-3 2h0c-3 3-6 5-10 7 2-2 4-3 6-5 0-1 0-1-1-1l2-2c1-2 2-4 2-7h0c0-2 1-3 2-4h1 0c1 0 1 0 1 1z" class="f"></path><path d="M234 327c1-1 1-1 1-2 1 1 1 2 1 3l-2 2 1 1-2 4h1v1l-3 2c0-1 1-2 1-3s0-2 1-4c0-1 0-2-1-3-1 1-1 2-2 2 0-2 1-3 2-4h1 0c1 0 1 0 1 1z" class="P"></path><path d="M234 327c1-1 1-1 1-2 1 1 1 2 1 3l-2 2v-3z" class="e"></path><path d="M240 315c1 0 1 0 1 1h1c0-1 1-1 2-2l1-1c0 2-1 4 0 6-1 1-1 2-2 3l-3 3-3 4-2 2-1-1 2-2c0-1 0-2-1-3 0 1 0 1-1 2 0-1 0-1-1-1h0-1l1-3s1 0 1-1l6-7h0z" class="m"></path><path d="M238 321l1-1 1 1h0c0 1-1 2 0 3v1l-3 4-2 2-1-1 2-2c0-1 0-2-1-3 1-2 2-3 3-4z" class="o"></path><path d="M238 321h1v1h-1c0 1 0 1-1 2l1 2c0 1-1 1-2 2 0-1 0-2-1-3 1-2 2-3 3-4z" class="R"></path><path d="M247 325c1 1 1 1 3 2-2 2-2 5-3 7 0 2-2 4-2 6h0c-1 1-2 2-2 3l-1-1v1c-1 1-1 2-1 3-1-1-1-1-1-2l-1 1h-1l-2 1c0 1 0 1-1 2h0c-1 1-1 1-2 1h-1-1 0v-1c1-2 1-5 3-7h0c1-3 3-4 6-5l4-4c0-1 0-2 1-3 1 1 1 1 2 1v-2l-2 1-1-2c1-1 1-1 2-1l1-1z" class="E"></path><path d="M240 337c1-1 1-2 3-2h0c0 1-1 1-2 2s-3 4-4 6c0 1-1 2-1 3h0c0 1 0 1-1 2h0c-1 1-1 1-2 1h-1-1 0v-1h0c3 0 7-9 9-11z" class="o"></path><path d="M247 325c1 1 1 1 3 2-2 2-2 5-3 7 0 2-2 4-2 6h0c-1 1-2 2-2 3l-1-1c1-1 1-2 1-4h0l3-3c0-2 1-3 1-4s1-2 1-2v-1h0-1l-2 1-1-2c1-1 1-1 2-1l1-1z" class="X"></path><path d="M240 336v1c-2 2-6 11-9 11h0c1-2 1-5 3-7h0c1-3 3-4 6-5z" class="D"></path><path d="M245 340v1h0v1 1 3c1 1 0 3 0 3l-1 1c-1 1-2 3-3 4h0 0s-1 0-1 1c-2 3-3 8-4 11l-4 4h-1c0 1 0 1-1 2v1l-1-1v-3h-2-1v-4c-1 0-2 1-3 1h0v-1c-1-1-1-2-2-3-2-3-2-5-1-8h0l1-1v2l1-3 1-1 1 1h0l3-1 1-3h1v3h0l2-2h0 1 1c1 0 1 0 2-1h0c1-1 1-1 1-2l2-1h1l1-1c0 1 0 1 1 2 0-1 0-2 1-3v-1l1 1c0-1 1-2 2-3z" class="P"></path><path d="M227 359h1v1c0 1 0 2-1 3h-1c0-2 0-2 1-4z" class="S"></path><path d="M231 356v2c0 1-1 2-2 2h0v-4l1 1h0l1-1z" class="g"></path><path d="M222 352l1-1 1 1h0l3-1c-1 2-2 3-3 5 0 1 0 1-1 2-1-2 0-4-1-6z" class="T"></path><path d="M227 351l1-3h1v3c-1 0-2 2-2 3v1 2c-1 0-1 1-2 2-1-1-1-2-1-3 1-2 2-3 3-5z" class="i"></path><path d="M220 354l1-1v2 2c0 1 1 1 1 2v2l1 2h3v2c-1 0-2 1-3 1h0v-1c-1-1-1-2-2-3-2-3-2-5-1-8h0z" class="h"></path><path d="M220 354l1-1v2 2h-1v-3h0z" class="O"></path><path d="M229 351h0l2-2h0 1 1l1 1c-1 1-2 1-2 3l1 1-2 2-1 1h0l-1-1h0l-1-1h-1v-1c0-1 1-3 2-3z" class="h"></path><path d="M232 353l1 1-2 2-1 1h0c0-2 1-3 2-4z" class="j"></path><path d="M238 345c0 1 1 2 1 3v1l2-2h0c0 1-2 3-2 4l-1 1-2 3h0c-2 0-2 1-3 2-1 0-1 1-2 1v-2l2-2-1-1c0-2 1-2 2-3l-1-1c1 0 1 0 2-1h0c1-1 1-1 1-2l2-1z" class="O"></path><path d="M234 350l1 1c0 1-1 2-2 3l-1-1c0-2 1-2 2-3z" class="g"></path><path d="M238 345c0 1 1 2 1 3v1h0c0 1-1 2-2 2-1-1-1-2-2-3h0c1-1 1-1 1-2l2-1z" class="U"></path><path d="M245 340v1h0v1 1 3c1 1 0 3 0 3l-1 1c-1 1-2 3-3 4h0 0s-1 0-1 1c-2 3-3 8-4 11l-4 4h-1c0 1 0 1-1 2v1l-1-1v-3c-1-1-1-2-1-3 0-2 1-4 2-5h1c2-1 4-4 5-6h0l2-3 1-1c0-1 2-3 2-4h0l-2 2v-1c0-1-1-2-1-3h1l1-1c0 1 0 1 1 2 0-1 0-2 1-3v-1l1 1c0-1 1-2 2-3z" class="g"></path><path d="M228 366h2 1c-1 2-1 4-1 6v1l-1-1v-3c-1-1-1-2-1-3z" class="U"></path><path d="M238 352v3l-1 1c0 1-1 2-2 4l-4 6h-1-2c0-2 1-4 2-5h1c2-1 4-4 5-6h0l2-3z" class="d"></path><path d="M238 356v-1c1 0 1 0 1-1v-1h0c1-2 2-2 3-4l1 1-2 2h0v2h0 0s-1 0-1 1c-2 3-3 8-4 11l-4 4h-1c1-4 4-7 5-11 1-1 1-2 2-3z" class="B"></path><path d="M245 340v1h0v1 1 3c1 1 0 3 0 3l-1 1c-1 1-2 3-3 4v-2h0l2-2-1-1c-1 2-2 2-3 4h0v1c0 1 0 1-1 1v1h-1l1-1v-3l1-1c0-1 2-3 2-4h0l-2 2v-1c0-1-1-2-1-3h1l1-1c0 1 0 1 1 2 0-1 0-2 1-3v-1l1 1c0-1 1-2 2-3z" class="R"></path><path d="M245 340v1h0v1 1s-1 1-1 2 0 1-1 2h-1c0-1 0-3 1-4 0-1 1-2 2-3z" class="B"></path><path d="M265 292h2v1c1 0 2 0 3-1h1 2 1c-1 1-1 2-2 2s-3 0-4 1v1h1 2 0l-1 1c0 1-1 2-2 2 0 2-1 3-1 4 1 0 1 0 2 1-1 4-3 9-7 11l-1 1c-4 1-5 4-8 7-1 2-2 3-3 4-2-1-2-1-3-2l-1 1c-1 0-1 0-2 1l-3 2-1 2-3-2 3-4 3-3c1-1 1-2 2-3-1-2 0-4 0-6l-1 1c-1 1-2 1-2 2h-1c0-1 0-1-1-1h0c2-2 4-3 6-5 1 0 2 0 3-1l3-2c1-2 3-4 4-6h-1c-1 2-2 2-3 3v1l-1-1 2-2 3-4 3-3c2-1 4-3 6-3z" class="O"></path><path d="M268 297h2c0 1-1 2-2 2h-1c-1 1-1 2-1 3h0-1c-1 1-2 1-3 1-1 1-1 2-1 3h-1l-1-1s0-1 1-1c1-1 1-1 1-2l-2-1 2-1 1-1v1 1h1s1-1 1-2c1-1 3-1 4-2z" class="o"></path><path d="M256 298l2 1c0 2-2 4-3 6 2-1 3-3 4-4l2 1c0 1 0 1-1 2-1 0-1 1-1 1l1 1h-1-2 0c-1 0-3 1-4 1h-1c1-2 3-4 4-6h-1c-1 2-2 2-3 3v1l-1-1 2-2 3-4z" class="i"></path><path d="M265 292h2v1c1 0 2 0 3-1h1 2 1c-1 1-1 2-2 2s-3 0-4 1v1h1 2 0l-1 1h-2l-1-1c-2 0-2 1-3 1l-1 1c-1-1-1 0-1-1l-2 2v-1c0-1 0-2-1-3h0c2-1 4-3 6-3z" class="c"></path><path d="M265 292l1 1c-2 0-3 1-4 2 0 1 0 1 1 2h1l-1 1c-1-1-1 0-1-1l-2 2v-1c0-1 0-2-1-3h0c2-1 4-3 6-3z" class="n"></path><path d="M260 306h1c0-1 0-2 1-3 1 0 2 0 3-1h1c-1 2-2 3-4 5-2 1-4 1-4 4h0c0-1 0-1-1-2l1-1c-2-1-5 1-7 2-3 0-4 1-6 3l-1 1c-1 1-2 1-2 2h-1c0-1 0-1-1-1h0c2-2 4-3 6-5 1 0 2 0 3-1l3-2h1c1 0 3-1 4-1h0 2 1z" class="O"></path><path d="M251 310c2-1 5-3 7-2l-1 1c0 1-1 2-2 3h-2 0c-1 2-2 3-4 4v1h-1l-1 1h0s-1 0-1 1h-1c-1-2 0-4 0-6 2-2 3-3 6-3z" class="N"></path><path d="M245 313c2-2 3-3 6-3-1 0-1 1-2 2 0 1-2 2-2 2v1h0l2-2h1c0 2-1 3-2 4l-1 1h0s-1 0-1 1h-1c-1-2 0-4 0-6z" class="J"></path><path d="M266 302h0c0-1 0-2 1-3h1c0 2-1 3-1 4 0 3-2 8-4 10-1 1-3 1-4 2h0l-3-1c0-1 0-1-1-2 1-1 2-2 2-3 1 1 1 1 1 2h0c0-3 2-3 4-4 2-2 3-3 4-5z" class="H"></path><path d="M257 309c1 1 1 1 1 2h0c1 1 1 1 2 1 1-1 1-1 2-1-1 1-4 2-4 3l1 1h0l-3-1c0-1 0-1-1-2 1-1 2-2 2-3z" class="W"></path><path d="M267 303c1 0 1 0 2 1-1 4-3 9-7 11l-1 1c-4 1-5 4-8 7-1 2-2 3-3 4-2-1-2-1-3-2l3-2c2-1 3-3 4-5l-1-1h0l3-3 3 1h0c1-1 3-1 4-2 2-2 4-7 4-10z" class="Y"></path><path d="M253 312h0 2c1 1 1 1 1 2l-3 3h0l1 1c-1 2-2 4-4 5l-3 2-1 1c-1 0-1 0-2 1l-3 2-1 2-3-2 3-4 3-3c1-1 1-2 2-3h1c0-1 1-1 1-1h0l1-1h1v-1c2-1 3-2 4-4z" class="Z"></path><path d="M253 312h0 2c1 1 1 1 1 2l-3 3-1-1c0-1 0-1 1-2v-2z" class="H"></path><path d="M247 320l2-1h1l-1 1c0 1 0 2 1 3l-3 2-1 1 1-1v-3c-1-1 0-1 0-2z" class="G"></path><path d="M247 320c1-1 1-2 2-2h1l1-1h2l1 1c-1 2-2 4-4 5-1-1-1-2-1-3l1-1h-1l-2 1z" class="d"></path><path d="M240 325l3-3h1v3l-3 3v1l-1 2-3-2 3-4z" class="L"></path><path d="M241 328c-1-1-1-1-1-2 1 0 1-1 1-1h1c1-1 1 0 2 0l-3 3z" class="C"></path><path d="M556 578v2h1 6l-2 2c-2 0-3 1-5 2h0v1h1l1 1 5-1v1h1c0 1 0 1-1 2v2l1-1h1c0 1 1 1 1 0v2h1c0 1-3 4-3 5-6 5-11 8-17 11h-1 1c-1 1-1 2-2 2l-3 3-4 1h-3c-2 0-4 1-6 1h0l-11 2h0c-4 0-8 2-11 2l-1-1h1c0-1 0-1-1-1 1-1 2-2 3-2-1-1-3-1-4-1-5-1-10-1-15-1h0l-1-1h0l5-1h1l-7-1s1-1 2-1c-1-1-2-1-3-1l-2-1 2-1v-2l-3-1-5 2-4 1-3 3v1l-14-1-6-1v-1c1-2 3-3 5-4h0c1 0 2-1 3-1h1c1 0 2-1 2-1v-1h2 2 5c1 1 2 1 3 1l17-3c3 0 6-1 9-1 5-1 9-3 14-5 7-2 14-4 20-7 4-3 8-4 13-4h5c0-1 1-1 2-1h0l1-1z" class="I"></path><path d="M465 599h2 5c1 1 2 1 3 1l-10 2-1-1-2 1-1-1c1 0 2-1 2-1v-1h2z" class="f"></path><path d="M465 599h2v1s-1 1-2 1h-1l-2 1-1-1c1 0 2-1 2-1v-1h2z" class="G"></path><path d="M532 599l-1-1s-1 0-1 1h-2-1 0c1-1 2-1 2-2h-2-1 0l1-1c0-1-1-1-1-2l1-1h0c2-1 6-2 9-1h0 2c-1 2-6 3-6 4h1v2l-1 1h0z" class="B"></path><path d="M541 590c2 1 4 0 6-1 0 1-1 2-2 3 2 0 2 0 3 1l-1 1c-4 1-11 5-14 4v-2h-1c0-1 5-2 6-4h-2 0l5-2z" class="M"></path><path d="M541 590c2 1 4 0 6-1 0 1-1 2-2 3l-12 4h-1c0-1 5-2 6-4h-2 0l5-2z" class="F"></path><path d="M556 578v2h1 6l-2 2c-2 0-3 1-5 2h0v1h1l1 1c-3 1-5 2-8 4 0 1-1 2-2 3-1-1-1-1-3-1 1-1 2-2 2-3-2 1-4 2-6 1 5-2 8-5 11-9l-4-1h5c0-1 1-1 2-1h0l1-1z" class="T"></path><path d="M556 585h1l1 1c-3 1-5 2-8 4 0 1-1 2-2 3-1-1-1-1-3-1 1-1 2-2 2-3 3-2 6-3 9-4z" class="L"></path><path d="M556 578v2h1 6l-2 2c-2 0-3 1-5 2h0c-2 0-3 1-4 1v-1l2-3 1-2 1-1z" class="H"></path><path d="M556 578v2h1c-1 1-1 2-2 2l-1-1 1-2 1-1z" class="C"></path><defs><linearGradient id="P" x1="467.645" y1="608.933" x2="472.35" y2="597.642" xlink:href="#B"><stop offset="0" stop-color="#241d22"></stop><stop offset="1" stop-color="#373c38"></stop></linearGradient></defs><path fill="url(#P)" d="M465 602l-2 1h1c3 0 6 1 8 0 4 0 8-2 12-2h8l12-1c-1 2-5 2-7 2h-13l-5 2-4 1-3 3v1l-14-1-6-1v-1c1-2 3-3 5-4h0c1 0 2-1 3-1h1l1 1 2-1 1 1z"></path><path d="M461 601l1 1c-2 1-5 2-6 3 1 1 1 1 2 1v2l-6-1v-1c1-2 3-3 5-4h0c1 0 2-1 3-1h1z" class="C"></path><path d="M458 606h4l13-1-3 3v1l-14-1v-2z" class="Q"></path><path d="M504 600h2v1 1c2 0 2 0 3 1h12c-1 1-1 1-1 3h3 0l5 1h4c-1 0-2 1-3 1-2 0-4 0-5 1s-2 1-3 1l-1 1c-1 2-1 2-3 3l-2 2h3 0c-4 0-8 2-11 2l-1-1h1c0-1 0-1-1-1 1-1 2-2 3-2-1-1-3-1-4-1-5-1-10-1-15-1h0l-1-1h0l5-1h1l-7-1s1-1 2-1c-1-1-2-1-3-1l-2-1 2-1v-2l-3-1h13c2 0 6 0 7-2z" class="X"></path><path d="M487 607c5 0 10 1 15 0 4 0 9-2 13 0-3 1-7 2-10 2-2 0-5 1-7 1-1 0-2-1-3 0l-7-1s1-1 2-1c-1-1-2-1-3-1z" class="F"></path><path d="M508 610c2-1 2 0 4 0 1-1 3-1 4-2-1 1-1 2-2 2-2 1-4 1-6 2h-1v1h3 1l-1 1h-1c-1-1-3-1-4-1-5-1-10-1-15-1h0l-1-1h0l5-1h1c1-1 2 0 3 0 2 0 5-1 7-1v1h3z" class="P"></path><path d="M505 610h3 0c-2 1-4 2-6 2-1 0 0 0-1-1 1 0 1 0 2-1h2z" class="S"></path><path d="M504 600h2v1 1c2 0 2 0 3 1-2 0-3-1-5 0l-2 1h0c1 1 1 0 2 0 2 0 4 1 6 1-6 2-13 1-19 0v-1c-2 0-3 0-4 1v-2l-3-1h13c2 0 6 0 7-2z" class="Q"></path><path d="M504 600h2v1 1c2 0 2 0 3 1-2 0-3-1-5 0l-2 1h-5c-1 0-2 0-2-1s1 0 2-1c2 0 6 0 7-2z" class="C"></path><path d="M523 606h0l5 1h4c-1 0-2 1-3 1-2 0-4 0-5 1s-2 1-3 1l-1 1c-1 2-1 2-3 3l-2 2h3 0c-4 0-8 2-11 2l-1-1h1c0-1 0-1-1-1 1-1 2-2 3-2h1l1-1h-1-3v-1h1c2-1 4-1 6-2 1 0 1-1 2-2 3-1 4-2 7-2z" class="F"></path><path d="M509 614h1l1-1h-1-3v-1h1c2 1 6 0 8 0h0c-2 2-3 3-6 4-1 0-2 0-3 1 0-1 0-1-1-1 1-1 2-2 3-2z" class="i"></path><path d="M514 610c3 0 6-1 10-1-1 1-2 1-3 1l-1 1c-1 2-1 2-3 3 1-1 1-1 1-3-1 0-1 0-2 1-2 0-6 1-8 0 2-1 4-1 6-2z" class="Q"></path><path d="M523 606h0l5 1h4c-1 0-2 1-3 1-2 0-4 0-5 1-4 0-7 1-10 1 1 0 1-1 2-2 3-1 4-2 7-2z" class="i"></path><path d="M563 585v1h1c0 1 0 1-1 2v2l1-1h1c0 1 1 1 1 0v2h1c0 1-3 4-3 5-6 5-11 8-17 11h-1 1c-1 1-1 2-2 2l-3 3-4 1h-3c-2 0-4 1-6 1h0l-11 2h-3l2-2c2-1 2-1 3-3l1-1c1 0 2 0 3-1s3-1 5-1c1 0 2-1 3-1h-4l-5-1h0-3c0-2 0-2 1-3 4-1 8-1 11-4h0l1-1c3 1 10-3 14-4l1-1c1-1 2-2 2-3 3-2 5-3 8-4l5-1z" class="p"></path><path d="M550 600h0c0 1 0 1-1 2l-4 3h1 1 1l-1 1v1h-1v-2c-1 1-3 2-5 3l4-4h-1l1-1v-1c1-1 3-2 5-2z" class="O"></path><path d="M523 606c5-1 10-2 16-2h0l-1 1h0c-2 0-3 0-5 1l-1 1h-4l-5-1z" class="m"></path><path d="M548 593c1-1 2-2 2-3 1 1 2 1 2 2v1 1h0l2 2-3 2c-1 0-1 1-1 1v1c-2 0-4 1-5 2h-2c1-1 3-2 4-3v-2s1 0 1-1v-1c0-1-1-1-1-1l1-1z" class="i"></path><path d="M548 593c1-1 2-2 2-3 1 1 2 1 2 2v1 1h0l-5 3s1 0 1-1v-1c0-1-1-1-1-1l1-1z" class="b"></path><path d="M532 607l1-1c2-1 3-1 5-1h0c-2 2-4 4-7 5-2 1-4 1-5 2h-1 1 0c2-1 5-1 8-2 2 0 5-2 8-2h1l2 1-3 3-4 1h-3c-2 0-4 1-6 1h0l-11 2h-3l2-2c2-1 2-1 3-3l1-1c1 0 2 0 3-1s3-1 5-1c1 0 2-1 3-1z" class="H"></path><path d="M563 585v1h1c0 1 0 1-1 2v2l1-1h1c0 1 1 1 1 0v2h1c0 1-3 4-3 5-6 5-11 8-17 11v-1l1-1h-1-1-1l4-3c1-1 1-1 1-2h0v-1s0-1 1-1l3-2-2-2h0v-1-1c0-1-1-1-2-2 3-2 5-3 8-4l5-1z" class="S"></path><path d="M558 594c2 0 5-2 7-3-2 2-4 3-5 6h-1c-2 1-3 3-5 4-2 2-5 3-7 4h-1-1l4-3c1-1 1-1 1-2h0v-1c3-1 5-2 7-4l1-1z" class="G"></path><path d="M557 595v1c-2 3-4 5-8 6 1-1 1-1 1-2h0v-1c3-1 5-2 7-4z" class="P"></path><path d="M563 585v1h1c0 1 0 1-1 2v2c-1 1-3 2-5 4l-1 1c-2 2-4 3-7 4 0 0 0-1 1-1l3-2-2-2h0v-1-1c0-1-1-1-2-2 3-2 5-3 8-4l5-1z" class="J"></path><path d="M552 594c2-1 4-3 5-4 0 1 0 2-1 4l-2 2-2-2h0z" class="G"></path><path d="M563 585v1c-1 0-5 4-6 4-1 1-3 3-5 4v-1-1c0-1-1-1-2-2 3-2 5-3 8-4l5-1z" class="X"></path><path d="M279 464l1-1 2 1v1h1v1h0c-1 2-1 6-1 8h0v4h2v1l2 1h0v2 1h4v1l1 1h1c1 2 0 6 0 8h1c1 0 2 0 3 1 1 0 1 1 2 1l-1 1 6 19c1 4 2 9 5 12l1 2h0c-2 1-3 0-5 0v1h-3-9-6-7-2c-2-2 0-4-2-6v3c-1 1-2 1-4 1h-7c-1 0-2-1-2 0-1-2-1-5-1-7v-8h1v-3-8-1c-1-1-2-1-3-1v-2c1-1 0-1 0-2s1-1 0-2v-2-4-2-2h0l1-2v-1-1c0-1 1-2 1-3h1l1-2c2-1 2-2 2-4h1l-2-3 1-2h0c1 0 2-1 3-1v1s1 0 1 1v1c1 2 1 4 1 5v1 1c1 1 2 0 2 0 1 0 0 0 0-1s0-1 1-2h1c1 1 0 1 1 1v-2c-1 0-2-1-2-1-1-2 0-2 0-3 1-1 0-2 0-3h5 0 1z" class="W"></path><path d="M270 477c1 1 3 3 3 5l-3-1v-1l-1-1c1-1 1-1 1-2z" class="J"></path><path d="M285 524v-1-3h1c1-1 1-1 1-2 0 3 0 5-1 7v3-2s-1 0-1-1v-1z" class="S"></path><path d="M262 485h1-1l-1-1c2-1 3-1 5-1h5v1l-1 1c-2 0-5 0-6 1l-2-1z" class="D"></path><path d="M290 517v4c0 1 0 1 1 2v1h1v-4h1v2l1-1 1 1c0 1-1 1-1 1v3l-1 1h0v-4h-1v2c0 1 0 1-1 2h-1c-1-3-1-7 0-10z" class="T"></path><path d="M291 504h1c0 3 1 6 0 8v2 2c0 2 1 3 1 4h-1v4h-1v-1c-1-1-1-1-1-2v-4-5-2c1-2 1-4 1-6z" class="e"></path><path d="M263 478c2-1 3-2 4-3l1 1h2v1c0 1 0 1-1 2l1 1v1h0c-3 1-7 1-9 1v-1c1-1 1-2 2-3z" class="M"></path><path d="M261 482v-1c1-1 1-2 2-3 1 1 1 2 2 3h5c-3 1-7 1-9 1z" class="H"></path><path d="M292 516l2 1v1h3 2c1 1 1 1 0 3h-1l2 2v1h-1c-1 0-2 1-2 2h0 0c-1 0-1 0-2-1l-1 1v-3s1 0 1-1l-1-1-1 1v-2c0-1-1-2-1-4z" class="J"></path><path d="M294 518h3v1 1c-1 0-2-1-2-1h-1v-1z" class="Z"></path><path d="M292 516l2 1v1 1h1c0 1 0 2 1 4v1c0 1 0 1 1 2h0 0 0c-1 0-1 0-2-1l-1 1v-3s1 0 1-1l-1-1-1 1v-2c0-1-1-2-1-4z" class="g"></path><path d="M268 465v1s1 0 1 1v1c1 2 1 4 1 5l-1 1-1-1c0 1-1 1-1 2h0c-1 1-2 2-4 3-1 1-1 2-2 3v1-1h-1v-1c0-1 1-2 1-3h1l1-2c2-1 2-2 2-4h1l-2-3 1-2h0c1 0 2-1 3-1z" class="f"></path><path d="M268 465v1s1 0 1 1v1l-1 1s0 1-1 2h-1 0l-2-3 1-2h0c1 0 2-1 3-1z" class="K"></path><path d="M273 464h5v2c-1 3-2 6-1 8 1 3 1 6 1 8h1l1 1h2v-5h2v1l2 1h0v2 1h-4c-2 0-4 0-5-1v-1c0-2 0-5-1-6-1 0-1 1-1 2l1 1c0 1 0 1-1 2 0-1-1-1-1-2v-1c-1 0-1-1-2-2 1 0 0 0 0-1s0-1 1-2h1c1 1 0 1 1 1v-2c-1 0-2-1-2-1-1-2 0-2 0-3 1-1 0-2 0-3z" class="R"></path><path d="M273 464h5v2l-5 1c1-1 0-2 0-3z" class="Y"></path><path d="M282 478h2v1c0 1 1 3 1 4h-3v-5z" class="B"></path><path d="M279 464l1-1 2 1v1h1v1h0c-1 2-1 6-1 8h0v4 5h-2l-1-1h-1c0-2 0-5-1-8-1-2 0-5 1-8v-2h0 1z" class="n"></path><path d="M279 467c0 2 0 3 2 4h0v4c0 1-1 1-1 1h-1 0 0v-1c-1-2-1-5 0-8z" class="Q"></path><path d="M282 474v4 5h-2l-1-1v-6h0 1s1 0 1-1v2l1-3z" class="E"></path><path d="M279 464l1-1 2 1v1h1v1h0c-1 2-1 6-1 8h0l-1 3v-2-4h0c-2-1-2-2-2-4h0 0c-1-1 0-2 0-3z" class="Y"></path><path d="M279 467h2c1 0 0 0 0 1v3h0c-2-1-2-2-2-4h0z" class="Z"></path><path d="M293 493c1 0 2 0 3 1 1 0 1 1 2 1l-1 1-2-2v1c1 2 1 4 1 7 1 3 2 7 2 11 0 0 0 1-1 1h1c1 1 1 1 1 2v2h-2-3v-1l-2-1v-2-2c1-2 0-5 0-8h-1c0-1 1-4 1-5v-3c1 0 1-2 1-3z" class="J"></path><path d="M292 499l2 13 1 1v1h2 1c1 1 1 1 1 2v2h-2-3v-1l-2-1v-2-2c1-2 0-5 0-8h-1c0-1 1-4 1-5z" class="T"></path><path d="M295 514h2 1c1 1 1 1 1 2-2 1-3 1-4 0-1 0-1-1-1-2h1z" class="B"></path><path d="M279 489v-3c0-1-2 0-2-1v-1h4 9l1 1h1c1 2 0 6 0 8l-1 1v-1c-3 0-12 1-14 0v-2h0l1 1h1v-3z" class="H"></path><path d="M279 489v-3c0-1-2 0-2-1v-1h4 9l1 1c0 1 0 1-1 2h-1 0c0 1 1 1 1 2h-1l1 2h0c-1 1-2 0-3 0h0-1 0v1h-1c-1-2 1-5 0-6s-1 0-1-1l-1 1v1h0l-1-1-1 1v1 1 1l-1-1h-1z" class="Z"></path><path d="M297 514c1 0 1-1 1-1 0-4-1-8-2-11 0-3 0-5-1-7v-1l2 2 6 19c1 4 2 9 5 12l1 2h0c-2 1-3 0-5 0 1 0 0 0 0-1-1 0-1 1-2 1s-2-1-2-2c-1 0-1 1-2 1l-1-2h0 0c0-1 1-2 2-2h1v-1l-2-2h1c1-2 1-2 0-3v-2c0-1 0-1-1-2h-1z" class="p"></path><path d="M300 523c0 1 1 3 0 4-1 0-1 1-2 1l-1-2h0 0c0-1 1-2 2-2h1v-1z" class="F"></path><path d="M275 504v10 10 3c-1 1-2 1-4 1h-7c-1 0-2-1-2 0-1-2-1-5-1-7v-8h1v-3h7l1-2h1-1c1-2 2-1 4-1v-1c0-1 0-2 1-2z" class="L"></path><path d="M266 513h3v1 1h-1c-1 0-2 0-2-1v-1z" class="M"></path><path d="M263 511h4 0l-1 1h-3-1l1-1z" class="I"></path><path d="M275 504v10 10 3c-1 1-2 1-4 1h-7c-1 0-2-1-2 0-1-2-1-5-1-7v-8h1c0 2-1 6 0 7v1 5h4c1 0 1-1 1-1 1-1 0-1 1-1 0 1 0 2 1 3h1c0-1 1-1 1-2v-1h0c1 1 1 2 2 2h0c0-1 1-3 0-4v-1h1l-1-1h-4v-1h4 0v-3h0v-1c-1 1-2 1-3 0h0l1-1h2v-1h-3l-1-1h4v-1h-3v-1c1 0 1 0 2-1h0 0l-1-1h-1c1-2 2-1 4-1v-1c0-1 0-2 1-2z" class="F"></path><path d="M270 485c1 0 2 1 3 2v1h-1v1h1v2h0c1 0 1 0 1 1-1 0-1 0-2 1 1 0 1 1 2 1v1h-1v1h1c1 2 0 5 1 7h-1v1h1c-1 0-1 1-1 2v1c-2 0-3-1-4 1h1-1l-1 2h-7v-8-1c0-2-1-7 0-8h0v-1c-1-1 0-1 0-2s-1-2-1-3 1-1 1-2l2 1c1-1 4-1 6-1z" class="C"></path><path d="M269 498v-1-1-1h4v1 1c-1 1-3 1-4 1zm1 9l-1-1h-2 0c0-1 1-1 1-2-1-1-4 1-6 0l1-1h5v1l1-1 1 1c1 0 1 0 2 1 0 0-1 0-2 1v1z" class="B"></path><path d="M273 496h1c1 2 0 5 1 7h-1v1h1c-1 0-1 1-1 2v1c-2 0-3-1-4 1h1-1v-1-1c1-1 2-1 2-1-1-1-1-1-2-1l-1-1h3v-1h-2l-1-1h1c1 0 1 0 2-1h-1c-1 0-1-1-2-1v-1c1 0 3 0 4-1v-1z" class="E"></path><path d="M292 493h1c0 1 0 3-1 3h0c-1 1-1 4-2 5l-2 12-1 4v1c0 1 0 1-1 2h-1v3 1 1c0 1 1 1 1 1v2h-2-6v-4-16-2-2-4c1 0 2 0 3-1h-3v-3c2-1 6-1 9-1 1 0 1 0 2 1v2h1l1-5v1l1-1z" class="U"></path><path d="M281 518h1v1l1 1c0 1 0 1-1 1 0 0-1 0-2-1 0-1 0-1 1-2z" class="d"></path><path d="M280 523h2v1l1 1c1 0 1-1 2-1v1c0 1 1 1 1 1v2h-2c-2-1-3-1-5-1h0c1-1 0-2 1-3s0-1 0-1z" class="e"></path><path d="M287 495c1 0 1 0 2 1v3h-2-3 0c-2-1-2-1-3-2l1-1 5-1z" class="H"></path><path d="M281 517c2 0 3-1 5 0h0 1 0v1c0 1 0 1-1 2h-1v3 1c-1 0-1 1-2 1l-1-1v-1h-2v-1h0 1 1v-1c1 0 1 0 1-1l-1-1v-1h-1v-1z" class="R"></path><path d="M282 522h3l-2 2h-1v-1h-2v-1h0 1 1z" class="J"></path><path d="M281 517h-1v-1l1-1c-1-1-1-1-1-2v-2-1-1-1l1-1c0-1-1-1-1-3h2 0v-1c0-1-1-1-1-2 1-1 1-1 2-1h2 3v1 1c1 0 1-1 2-1l-2 12-1 4h0-1 0c-2-1-3 0-5 0z" class="Z"></path><path d="M286 508c1 0 1 0 2 1-2 1-3-1-4 1v2h0c-1 0-2 0-3-1v-1h2l1-1h-3v-1h1 3 1z" class="H"></path><path d="M286 508l-1-1v-1l2 1h1v-6 1c1 0 1-1 2-1l-2 12-1-3h-3c1-2 2 0 4-1-1-1-1-1-2-1z" class="d"></path><path d="M428 317v-1h0c1-2 4-1 5-2 0 1 0 2-1 3v1c1 0 3 0 4 2 1 1 2 2 2 3 1 1 1 2 0 4h1 0 1v1 4h1v1 1h-1c0 1 1 1 1 1 0 2-2 1 0 4h2 0l1 1h3v2-2c1 0 1 0 1 1v17l6 2v-1 1h0c0 1-1 1-1 2s1 13 1 14v1h-1-1-5l-7-1-6-1h-1l-21-1c-2 0-10 1-12-1-1 0 0 1-1 0-1 0-3 1-5 0l-1-2v-1h0c1-2 1-6 0-7 0-1-1-2-1-2v-1l1 1h1v-3h0v-1h2v-1c-1 0-2 0-3-1h0l-14 1h0c-2 0-4 1-6 1l1-1h1v-3-1c0-1 0-1 1-1 2 0 2 0 3 1l1 1h1l-1 2h4 0c3-1 9 0 11-2h1 0 5 14v-1c1 1 2 1 3 0v-3c0-2 0-2-1-2 0-1-1-1-2-1h2c1-1 1-2 1-4h-1l-1-1h1 1v-2l-1-1h0c-1 0-2 0-3-1v-3c1 0 2 1 2 0h0 2v-4c-1-1-2-1-3-2h3v-3l1-2v4h1v1h1l1 1v-2l1-3 2-4h0l1-2 2-1z" class="K"></path><path d="M434 372c2-1 2-1 2-3v3h0c1 1 1 1 1 2-1 1-2 1-3 1h-1v-2c1 0 1 0 1-1z" class="p"></path><path d="M434 366c0-1 0-2 2-2v2 2 1c0 2 0 2-2 3v-3c-1-1 0-1 0-1v-2z" class="q"></path><path d="M413 369h3 1s1 0 1 1 0 2-1 3h-4v-2-2z" class="V"></path><path d="M414 370h1v2h-1v-2z" class="Z"></path><path d="M446 366l2-1v2 3c1 0 1 1 1 1 0 2 0 3-1 4l-1 1v-1l-1-2c0-1 0-1 1-2l-1-1v-4z" class="q"></path><path d="M443 359c1 1 2 1 3 2v2h-1l1 3h0v4l1 1c-1 1-1 1-1 2v2c-1 0-2 0-2-1-1-3-1-6-1-9v-6z" class="S"></path><path d="M446 366l-1 1c0-1 0-1-1-1v-1c-1-1-1-2 0-3h0l1 1 1 3z" class="B"></path><path d="M429 370v-1-6c0-2 0-3 1-5h2l2 1c1 1 0 2 0 3v4h0v2s-1 0 0 1v3c0 1 0 1-1 1s-3 0-4-1v-2z" class="o"></path><path d="M429 370v-1-6c0-2 0-3 1-5h2v1 1 5h0c-1 1-1 1-1 2v1s1 0 1-1l1 1c-1 0-1 1-2 1s-1 1-2 1z" class="U"></path><path d="M447 342v-2c1 0 1 0 1 1v17l6 2v-1 1h0c0 1-1 1-1 2s1 13 1 14v1h-1c-1 0-2-1-2-1-1-1-1-1-1-2s0-2-1-3c0 0 0-1-1-1v-3-2l-2 1h0l-1-3h1v-2c-1-1-2-1-3-2h0v-1s-1-1-2-1l6 1v-1-1h-1v-1h1v-3c0-1 0-4-1-5 0-1-1-1-1-1s1 0 2-1v-1h-1l-1-1v-1h2z" class="Y"></path><path d="M451 365c-1 0-1 1-2 1l-1-1v-3c1-1 1-1 2-1s2 0 3 1c0 1 1 13 1 14v1h-1c-1 0-2-1-2-1-1-1-1-1-1-2s0-2-1-3c0 0 0-1-1-1v-3c2 0 2 0 3-1v-1z" class="B"></path><path d="M451 365v-2h1v3l-1 1c0 2 0 3-1 4 1 1 1 1 2 1v1c-1 0-1 1-2 1 0-1 0-2-1-3 0 0 0-1-1-1v-3c2 0 2 0 3-1v-1z" class="U"></path><defs><linearGradient id="Q" x1="421.059" y1="368.513" x2="409.719" y2="348.501" xlink:href="#B"><stop offset="0" stop-color="#0f1012"></stop><stop offset="1" stop-color="#2d2b2a"></stop></linearGradient></defs><path fill="url(#Q)" d="M375 353v-1c0-1 0-1 1-1 2 0 2 0 3 1l1 1h1l-1 2h4 0c3-1 9 0 11-2h1 0 5 14l9 1c2 0 4 1 6 1h7l9 1h1v1 1l-6-1-10-1 1 1c-2 1-4 1-6 1l-1 8h0l-1-1h-1l1 3v2l-1-1c-1 0-2 1-3 1 0 1 0 1-2 1h0v-1c0-1-1-1-1-1h-1-3v2 2c-1 0-1 0-2-1-1-2-1-5 0-8v-3c-1 1-1 1-1 2l-1 1v5c0 1 0 2-1 4h-2v-2h0l-2 2h-1c-1 0-1 0-2-1l-1 1h0c-1 0 0 1-1 0-1 0-3 1-5 0l-1-2v-1h0c1-2 1-6 0-7 0-1-1-2-1-2v-1l1 1h1v-3h0v-1h2v-1c-1 0-2 0-3-1h0l-14 1h0c-2 0-4 1-6 1l1-1h1v-3z"></path><path d="M424 368c-1 0-1 0-2-1h0l-1 1h0-1v-7-3h1 2v3h0l1 1v-1-3h2l-1 8h0l-1-1h-1l1 3z" class="U"></path><path d="M401 353h14l9 1c2 0 4 1 6 1h7l9 1h1v1 1l-6-1-10-1-29-1c1-1 6 0 8-1-2-1-7 0-9 0v-1z" class="D"></path><path d="M403 369l1-1h0v-2c1-2 0-2 0-4 0-1 0-4 1-5h1 1c1 0 2 0 3 1v3 2l-1 1v5c-2 0-4 1-6 0z" class="B"></path><path d="M409 364c-1 0-1-1-2-1 0-1 0-2 1-2 1-1 1-1 2 0v2l-1 1z" class="M"></path><path d="M375 353v-1c0-1 0-1 1-1 2 0 2 0 3 1l1 1h1l-1 2h4 0c3-1 9 0 11-2h1 0 5v1c2 0 7-1 9 0-2 1-7 0-8 1h-9l-14 1h0c-2 0-4 1-6 1l1-1h1v-3z" class="H"></path><path d="M375 353v-1c0-1 0-1 1-1 2 0 2 0 3 1l1 1h1l-1 2c0-1 0 0-1-1h0c-1 0-2 1-3 1-1-1-1-1-1-2z" class="k"></path><path d="M411 361v-4h3c2 0 3 0 4 1 0 2 0 5-1 6 1 1 1 2 1 3-1 1-2 0-4 0h0-1v2 2 2c-1 0-1 0-2-1-1-2-1-5 0-8v-3z" class="H"></path><path d="M414 357c2 0 3 0 4 1 0 2 0 5-1 6h0c-1 0-1 0-2-1l1-1c0-1 1-1 1-2l-1-1c0 1-1 2-1 2v1h-1 0v-3-2z" class="C"></path><path d="M411 361v-4h3v2 3h-1c0 1 0 2-1 3l-1-1v-3z" class="B"></path><path d="M396 357h2 6c-1 3-2 9-1 12 2 1 4 0 6 0 0 1 0 2-1 4h-2v-2h0l-2 2h-1c-1 0-1 0-2-1l-1 1h0c-1 0 0 1-1 0-1 0-3 1-5 0l-1-2v-1h0c1-2 1-6 0-7 0-1-1-2-1-2v-1l1 1h1v-3h0v-1h2z" class="C"></path><path d="M396 357h2c-1 1-1 4-1 6 2 0 2-1 4 0-2 2-4 0-4 4 0 1 0 1 1 2h-1c-1 1 0 2 0 4h1l2-3c1 1 0 1 0 1l-1 2h0 1 0c-1 0 0 1-1 0-1 0-3 1-5 0l-1-2v-1h0c1-2 1-6 0-7 0-1-1-2-1-2v-1l1 1h1v-3h0v-1h2z" class="U"></path><path d="M428 317v-1h0c1-2 4-1 5-2 0 1 0 2-1 3v1c1 0 3 0 4 2 1 1 2 2 2 3 1 1 1 2 0 4h1 0 1v1 4h1v1 1h-1c0 1 1 1 1 1 0 2-2 1 0 4h2 0l1 1h3v2h-2v1l1 1h1v1c-1 1-2 1-2 1s1 0 1 1c1 1 1 4 1 5v3h-1v1l-9-1h-7c-2 0-4-1-6-1l-9-1v-1c1 1 2 1 3 0v-3c0-2 0-2-1-2 0-1-1-1-2-1h2c1-1 1-2 1-4h-1l-1-1h1 1v-2l-1-1h0c-1 0-2 0-3-1v-3c1 0 2 1 2 0h0 2v-4c-1-1-2-1-3-2h3v-3l1-2v4h1v1h1l1 1v-2l1-3 2-4h0l1-2 2-1z" class="p"></path><path d="M420 350v-1c1 0 4 1 4 1 0 1 0 2-1 2l-1-1v1h-1-1v-2z" class="E"></path><path d="M433 319c2 1 3 1 4 2v1 2h1v-1c1 1 1 2 0 4h1 0 1v1c-2 1-2 2-3 4v3h-1v-4h-1c1-1 1-2 0-3v1 1l-2-1v-2-1h1l2 1v-1-3-1c-1-1-1-1-2 0v-2l-1-1z" class="O"></path><path d="M432 318c1 0 3 0 4 2 1 1 2 2 2 3v1h-1v-2-1c-1-1-2-1-4-2-1 0-3 0-4 1s-1 1-1 2c0 0-1 0-1 1h1 0c1 0 1 0 1 1h-1-2c0 1 0 1 1 1l-1 1c1 2 1 3 1 4l2 1h-2v-1c-1 0-2-1-3-1v-4h0c0-2 0-3 1-3 2-3 4-4 7-4z" class="E"></path><path d="M424 329c1 0 2 1 3 1v1s-1 0 0 1c1 3 0 6 0 9v12l-1 1h0c-1-2-1-4-1-5v-4-1c1-2 0-3 0-5 0-1 0-1 1-1 0-2 0-2-1-3 0-1 0-1 1-2l-1-1c0-1-1-2-1-3h0z" class="G"></path><path d="M435 330v-1-1c1 1 1 2 0 3h1v4h1c1 2 0 3 0 5v8 6 1h-7c1 0 2 0 3-1v-3-1c1-1 1-4 1-6-1-4 0-9-1-13h0l2-1z" class="X"></path><path d="M434 344c0 2 0 4 1 6 0 1 0 3-1 4h0c-1-1-1-2-1-4 1-1 1-4 1-6z" class="R"></path><path d="M436 335h1c1 2 0 3 0 5v8 6h-1v-3h0v-9-7z" class="P"></path><path d="M435 330v-1-1c1 1 1 2 0 3v14 5c-1-2-1-4-1-6-1-4 0-9-1-13h0l2-1z" class="J"></path><path d="M437 335v-3c1-2 1-3 3-4v4h1v1 1h-1c0 1 1 1 1 1 0 2-2 1 0 4h2 0l1 1h3v2h-2v1l1 1h1v1c-1 1-2 1-2 1s1 0 1 1c1 1 1 4 1 5v3h-1v1l-9-1v-1-6-8c0-2 1-3 0-5z" class="q"></path><path d="M444 346l-4-2h0v-1h1c1 1 2 0 2 0l3 1h1v1c-1 1-2 1-2 1h-1z" class="L"></path><path d="M446 355c-2 0-3 0-4-1v-1l-1-1 1-1h2v1h3v3h-1zm-2-15h3v2h-2v1l1 1-3-1v-1h-2c0-1-1-1-1-2h3 1z" class="N"></path><path d="M444 352l-1-2h-1c-1 0-1-1-1-3l-1-1v-1l3 2 1-1h1s1 0 1 1c1 1 1 4 1 5h-3z" class="B"></path><path d="M428 317v-1h0c1-2 4-1 5-2 0 1 0 2-1 3v1c-3 0-5 1-7 4-1 0-1 1-1 3h0v4h0c0 1 1 2 1 3l1 1c-1 1-1 1-1 2 1 1 1 1 1 3-1 0-1 0-1 1 0 2 1 3 0 5v1 4l-1-1c-1 1-4 0-5 0 1-2 1-3 1-4h0c-1-2-1-3-1-4h-1v1-2l-1-1h0c-1 0-2 0-3-1v-3c1 0 2 1 2 0h0 2v-4c-1-1-2-1-3-2h3v-3l1-2v4h1v1h1l1 1v-2l1-3 2-4h0l1-2 2-1z" class="P"></path><path d="M420 344c0-2-1-4 0-5h2l2 1c0 1 0 1-1 1v1c0 1 1 0 1 2h0-2v-1l-1-1-1 2 1 1c1 0 2 1 3 1v2c-1 1-4 0-5 0 1-2 1-3 1-4h0z" class="F"></path><path d="M418 325l1-2v4l1 1-1 1v1h1 1v1c-1 0-1 0-2 1v2c1 1 0 2 0 4h1 3 1v1h-2-2c-1 1 0 3 0 5-1-2-1-3-1-4h-1v1-2l-1-1h0c-1 0-2 0-3-1v-3c1 0 2 1 2 0h0 2v-4c-1-1-2-1-3-2h3v-3z" class="b"></path><path d="M416 334l2 1v2l-1 1c-1 0-2 0-3-1v-3c1 0 2 1 2 0z" class="M"></path><path d="M358 452c8-2 17-3 26-3h0 13v1h-5c0 1 0 0 1 1h0c1 0 1 0 2 1l4-1-2 1v1h4 3c4 0 9-1 13 0v2h1 1c0-1 1-1 2-1h2 0-4-1l-1-1c3-1 7 0 10-1 8 0 17 1 24 3l1 1c0 1 0 2-2 2-2 2-6 2-8 4-1 1-1 2-1 3h-1v2l1 6v9c-1 0-1 1-1 1l-3-1v-1l-1 1v5c-1 0-1-1-1-1h-3-3c-2 0-5-1-8-1-4-1-9 0-14 0-4 0-10-1-14 0h0v1h2v1h-9-3c-3 0-7 0-9 1-1 1-2 2-3 2l-1-1h0 1v-1h-1 0c-1-1-1-1-2-1v-27c-3-2-6-2-9-3-2-1-3-1-4-3 1-2 1-2 2-3l1 1h0z" class="r"></path><path d="M437 468c0-1 0-3 1-4l2 1v2c-1 1-2 1-3 1z" class="i"></path><path d="M437 468c1 0 2 0 3-1l1 6c-1 0-1-1-1-2h-1l-2 2v4 4 1-1c0-4-1-9 0-13z" class="G"></path><path d="M438 456h1c1-1 3-1 4 1l-3 4c0 1 0 0-1 1 0 1 0 1-1 1l-1-1v-2h2 0c-1-1-1-1-1-2-1 0-1-1 0-2z" class="W"></path><path d="M397 459h11 5l6 1c-5 1-11 1-16 1-2-1-5 0-7-1v-1h1z" class="S"></path><path d="M397 459h11c-1 1-2 1-3 1h-9v-1h1z" class="R"></path><path d="M439 471h1c0 1 0 2 1 2v9c-1 0-1 1-1 1l-3-1v-1-4-4l2-2z" class="Z"></path><path d="M437 477h2c0 2-1 2-2 4v-4z" class="R"></path><path d="M437 477v-4l2-2v6h-2z" class="d"></path><path d="M421 461l1-4h1v1c0 1 0 2 1 3v-1-1c1 1 1 3 1 4h-2v1h2v5c0 3 1 7 0 10l-4 1v-14-5z" class="S"></path><path d="M404 453c4 0 9-1 13 0v2h1c0 1 1 1 1 2-1 2-5 0-6 2h-5-11v-1l-2-1v-1-1c2-2 4-2 6-2h3z" class="g"></path><path d="M401 453h3 11c-1 1-4 1-5 2l2 1h-9c-2 0-6-1-8 0v-1c2-2 4-2 6-2z" class="l"></path><defs><linearGradient id="R" x1="409.603" y1="451.881" x2="402.383" y2="461.37" xlink:href="#B"><stop offset="0" stop-color="#848784"></stop><stop offset="1" stop-color="#aea7ac"></stop></linearGradient></defs><path fill="url(#R)" d="M395 456c2-1 6 0 8 0h9 5v1c-2 1-6 1-8 1h-12l-2-1v-1z"></path><path d="M430 474v-1-12-1c2 2 0 5 2 6h0v-5h1 0v1c0 1 0 0 1 1v3 7 1h0 0v1 5 2h-4-1l-1 1c2 1 3 0 4 2-1 0-2 0-3 1-2 0-5-1-8-1-4-1-9 0-14 0-4 0-10-1-14 0h0v1h2v1h-9-3c-3 0-7 0-9 1l-1-1v-1-1h7l17-1c4 1 9 0 14 0 3 0 6 0 9-2h1v-1-1l4-1h5v-5z" class="a"></path><path d="M430 474v-3h1c1 1 0 3 0 5v3 2c-1 1-7 0-9 0h-1v-1l4-1h5v-5z" class="D"></path><path d="M393 485h0v1h2v1h-9-3c-3 0-7 0-9 1l-1-1v-1h1c7 0 13-1 19-1z" class="E"></path><defs><linearGradient id="S" x1="407.478" y1="474.102" x2="396.453" y2="473.976" xlink:href="#B"><stop offset="0" stop-color="#8f8d8e"></stop><stop offset="1" stop-color="#acaaab"></stop></linearGradient></defs><path fill="url(#S)" d="M419 463h2v-2 5 14 1 1h-1c-3 2-6 2-9 2-5 0-10 1-14 0l-2-1 1-1v-1-3l-1-2c1-1 1-3 1-4v-3-1-2-1-3h2c2-1 5 0 7 0h2 8l4 1z"></path><path d="M404 477v-2c1 0 3-1 4-1v2l1 1h0-5z" class="e"></path><path d="M397 472v-1h4 5l1 1c-3 0-7 1-10 0z" class="M"></path><path d="M395 476l9 1h5 2c-1 1-2 1-2 1h-6-7l-1-2z" class="C"></path><path d="M409 474v-2h2 8c-1 1-2 1-2 1 0 1 1 1 2 1v1h-1v1 1l1 1h0l-2 1c-1-1-3-1-4-1h0-4s1 0 2-1h-2 0l-1-1v-2h1z" class="S"></path><path d="M409 474h0c1 0 1 1 1 1h6c-1 0-3 1-4 1v1h-3l-1-1v-2h1z" class="P"></path><path d="M419 474v1h-1v1 1h-6v-1c1 0 3-1 4-1h2l1-1z" class="T"></path><path d="M411 472h8c-1 1-2 1-2 1 0 1 1 1 2 1l-1 1v-1c-2 0-5 0-7-1v-1z" class="O"></path><path d="M409 477h3 6l1 1h0l-2 1c-1-1-3-1-4-1h0-4s1 0 2-1h-2 0z" class="S"></path><path d="M405 469l1-1h6c2 1 4 0 7 0 0 1 0 2-1 3h1-3-4c-2 1-3 1-5 1h0l-1-1h-5-4v1h-1v-3h3 6z" class="d"></path><path d="M399 469h5l1 1h-3l-1 1h-4v1h-1v-3h3z" class="B"></path><path d="M405 469l1-1h6c2 1 4 0 7 0 0 1 0 2-1 3h1-3-4-5v-1h3v-1c-1-1-3 0-4 0h-1z" class="h"></path><path d="M419 463h2v-2 5 14 1 1h-1-10v-1h3 1l1-1c-1-1-1 0-2-2 1 0 3 0 4 1l2-1h0l-1-1v-1-1h1v-1c-1 0-2 0-2-1 0 0 1 0 2-1h0v-1h-1c1-1 1-2 1-3v-1-1c0-1 1-2 0-3z" class="b"></path><path d="M413 478c1 0 3 0 4 1h0l2 1v1h-6 1l1-1c-1-1-1 0-2-2z" class="m"></path><path d="M396 478h7 6 4 0c1 2 1 1 2 2l-1 1h-1-3v1h10c-3 2-6 2-9 2-5 0-10 1-14 0l-2-1 1-1v-1-3z" class="i"></path><path d="M396 482c5 0 9-1 14 0l3 1c-1 0-1 0-2 1-5 0-10 1-14 0l-2-1 1-1z" class="R"></path><path d="M396 478h7v1c2 0 5 0 7 1-4 1-10 2-14 1v-3z" class="Z"></path><path d="M398 462c2-1 5 0 7 0h2 8l4 1c1 1 0 2 0 3v1 1c-3 0-5 1-7 0h-6l-1 1h-6-3v-1-2-1-3h2z" class="G"></path><path d="M396 466c1 0 2-1 3 0h3c3 0 5 0 8 1-2 1-4 1-6 1h-8v-2z" class="C"></path><path d="M396 466v-1-3h2 0 2c2 0 2 1 3 1v1c0 1-1 1-2 1l1 1h0-3c-1-1-2 0-3 0z" class="Z"></path><path d="M405 462h2 8l4 1c1 1 0 2 0 3v1 1c-3 0-5 1-7 0h0 3c-2-2-4-1-6-2v-2c-1 0-2-1-3-2 0 0 0 1-1 0h0z" class="l"></path><path d="M415 462l4 1c1 1 0 2 0 3v1h-2l1-1c-1-1-1-1-2-1-1-1-1-2-1-3z" class="g"></path><path d="M405 462h2c2 0 2 1 3 2h3c1 0 1 1 2 1v1l-1 1h3v1h-2c-2-2-4-1-6-2v-2c-1 0-2-1-3-2 0 0 0 1-1 0h0z" class="U"></path><defs><linearGradient id="T" x1="356.421" y1="452.906" x2="386.345" y2="470.365" xlink:href="#B"><stop offset="0" stop-color="#0a0a0a"></stop><stop offset="1" stop-color="#282729"></stop></linearGradient></defs><path fill="url(#T)" d="M358 452c8-2 17-3 26-3h0 13v1h-5c0 1 0 0 1 1h0c1 0 1 0 2 1l4-1-2 1v1h4c-2 0-4 0-6 2v1 1l2 1v1h-1v1c2 1 5 0 7 1h0s1 0 2 1c-2 0-5-1-7 0h-2v3 1 2 1 3c0 1 0 3-1 4l1 2v3 1l-1 1 2 1-17 1h-7v1 1l1 1c-1 1-2 2-3 2l-1-1h0 1v-1h-1 0c-1-1-1-1-2-1v-27c-3-2-6-2-9-3-2-1-3-1-4-3 1-2 1-2 2-3l1 1h0z"></path><path d="M373 466c2-1 1 0 3 0l1-1h2c0 1 0 2-1 3h-2l2 1h-5v-2-1z" class="D"></path><path d="M374 460c1 1 2 2 4 3h1v2h-2l-1 1c-2 0-1-1-3 0v-3c1-1 1-2 1-3z" class="R"></path><path d="M370 462c-1-1 0-3-1-4l-1-1c0-1 1 0 1 0 1-1 3-1 4-1s1 0 2 1c0 1 0 1-1 2h0v1c0 1 0 2-1 3v3 1-1h-2l1-1h0v-1h-1c0-1 0-1-1-2z" class="Y"></path><path d="M370 462l2-2h1c1 1 0 2 0 3v3 1-1h-2l1-1h0v-1h-1c0-1 0-1-1-2z" class="p"></path><path d="M378 469h1v2h0v1 2h0c0 1-1 1-1 2-1 1-3 0-5 0v-2-5h5z" class="B"></path><g class="C"><path d="M379 471v1 2h0c0 1-1 1-1 2-1 1-3 0-5 0v-2c1 0 1 1 2 0s1-1 1-2c1 0 2 0 2-1h1z"></path><path d="M370 488v-17c0-2 0-5 1-7l1 1-1 1h2v1 2 5 2 3h-1v-3-1 13h-1-1z"></path></g><path d="M371 466h2v1 2 5 2 3h-1v-3-1l-1-9z" class="q"></path><path d="M358 452c8-2 17-3 26-3h0 13v1h-5c0 1 0 0 1 1h0c1 0 1 0 2 1h-24c-4 1-8 1-12 1l-1-1z" class="M"></path><path d="M390 458c1 0 1 0 1 1v8 2 2h1s-1-2 0-2v-1s-1-1 0-2c1-3-1-6 1-9h1c1 4 1 8 0 11h0c1 2 1 8 1 10-1 1-2 0-3 0v-1l-1 1h-1v-10-10z" class="C"></path><path d="M395 457l2 1v1h-1v1c2 1 5 0 7 1h0s1 0 2 1c-2 0-5-1-7 0h-2v3 1 2 1 3c0 1 0 3-1 4l1 2v3 1l-1 1 2 1-17 1h-7v1 1l1 1c-1 1-2 2-3 2l-1-1h0 1v-1h1v-13 1 3h1v-3c2 0 4 1 5 0 0-1 1-1 1-2h1v5h1 11v-1c1 0 2 1 3 0 0-2 0-8-1-10h0c1-3 1-7 0-11h1z" class="Y"></path><path d="M373 485v-2c3-1 4 0 6 0l1 2h-7z" class="C"></path><path d="M379 483h16l2 1-17 1-1-2z" class="D"></path><path d="M378 476c1 2 1 4 1 6h-6v-3h0v-3c2 0 4 1 5 0z" class="I"></path><path d="M381 479h11 3v2l-1 1c-2 0-12 0-14-1v-2h1z" class="B"></path><path d="M380 474v-2-15h8 0 1 1v1 10 10h1l1-1v1 1h-11-1v-5z" class="p"></path><path d="M380 474v-2-15h8 0 1l-1 1h-2-5v2h1v-1l1 1v3 4 8 2l-1 1h-1v1h-1v-5z" class="Q"></path><path d="M208 512c8-1 14-2 21-1l3 1c1 2 3 4 4 7 2 3 3 5 3 9l-1 2-1 1h3 5l6-1h1 7c1-1 2 0 3-1v-1c0-1 1 0 2 0h7c2 0 3 0 4-1v-3c2 2 0 4 2 6h2 7 6 9 3v-1c2 0 3 1 5 0h8l10 1c1 0 3 1 4 2h0v6 1c-7-1-14 0-20 0-1 1-1 3-1 4v1 2h0l-1 1h-1l-1-1h-5-2-6-16c1 0 2 0 3-1h0s0-1 1-2v-1-2c-1 0-2 0-3-1h-3c-1 1-1 1-1 2h0-6-2v2c0 1-1 2-2 2v1h0-4c-1-1 0-3-1-5h-1v1 5h-1c-2-1-5-1-7-1-1 1-4 0-5 0h-4v-1-1h0c-1-1-2-1-3-1l-1 1v-1c-1-1-3-1-5-1v-1c-1-1-3 0-5 0-3 0-6 0-9-1h0l-15 1h-3-1l1 5h-1l-2 1c-1 0-2 0-3-1h0v-1c-1 0-2-1-3-1-1 1-1 1-2 1v-1-1h1 1c0-1 0-1-1-2h-3l-5 1h-14-3-8c-2 0-4 0-6-1h-1-4v-1h3l1-1h2v1c3 0 9 1 12 0h3c-1-2-1-4-1-6h0 1l2-1h6l13-1h1 7l4-1h0l1-9 2-2h0l1-1v-2c1 0 1 0 2-1 0 0 1 0 2-1h-1-1v-1l1-2z" class="L"></path><path d="M188 534c1 0 1-1 2 0v4c-1 1-1 1-2 1v-5z" class="c"></path><path d="M222 538c2-1 1-4 1-6h2c1 2 1 4 1 6h-3-1z" class="E"></path><path d="M219 540h7 12v1c0 1 1 1 1 2l-1 1v-1c-1-1-3-1-5-1v-1c-1-1-3 0-5 0-3 0-6 0-9-1h0z" class="g"></path><path d="M196 533h2l1 1v1 3c-1 0-2 1-3 1-1-1-2-1-3-2h1l-1-1 1-2c1 0 1 0 2-1z" class="Q"></path><path d="M238 540h9v1l-1 1 1 2c-1 1-1 1-3 1l-2-1h0c-1-1-2-1-3-1 0-1-1-1-1-2v-1z" class="f"></path><path d="M242 544v-1c1-1 3-1 4-1l1 2c-1 1-1 1-3 1l-2-1h0z" class="Y"></path><path d="M188 541c5-1 11 0 16 0h-3-1l1 5h-1l-2 1c-1 0-2 0-3-1h0v-1c-1 0-2-1-3-1-1 1-1 1-2 1v-1-1h1 1c0-1 0-1-1-2h-3z" class="j"></path><path d="M195 546h1c1-1 1-1 1-2h-2-1c1-2 2-1 4-1 0 0 2-1 2-2l1 5h-1l-2 1c-1 0-2 0-3-1z" class="T"></path><path d="M201 531l15 1v1c0 1 0 1-1 2 0-1-1-1-2-2h0l-1 1h-1v2c0 1 0 1-1 2v-1-3h-1c0 1 0 2-1 3-1-1 0-2-1-3h-1c-1 1 0 2-1 3v-1-1l-1-1c-1 1 0 1 0 2l-1 1c-1 0 0-2-1-3-2 1 0 1-2 3-1-1 0-2 0-3h-1l-1-1h-2-7v-1h1 7l4-1z" class="H"></path><path d="M251 530h1c-1 2-1 4-1 6l1 1v1c-2 0-3-1-5 0h-5s0-1-1-1v1c-1 0-1 0-2-1-1 1-1 1-2 1h-1-2c-1 0-1 0-2-1v-4-1h-1c2-1 7-1 9-1h5l6-1z" class="G"></path><path d="M176 533l13-1v1h7c-1 1-1 1-2 1l-1 2v-2h-3c-1-1-1 0-2 0v5c-2 0-4 1-6 0l-9 1h-4-1c-1-2-1-4-1-6h0 1l2-1h6z" class="F"></path><path d="M176 533l13-1v1h7c-1 1-1 1-2 1l-1 2v-2h-3c-1-1-1 0-2 0h-4c-1 1 0 2 0 4h-1c0-1 1-1 0-2v-2h-6 0-1l-1 1h-1l-1-1c-1 2 0 4 0 6h-4-1c-1-2-1-4-1-6h0 1l2-1h6z" class="V"></path><path d="M167 534h0 1c1 2 0 4 1 6h-1c-1-2-1-4-1-6z" class="D"></path><path d="M247 540l29-1c-1 1-1 1-1 2h0-6-2v2c0 1-1 2-2 2v1h0-4c-1-1 0-3-1-5h-1v1 5h-1c-2-1-5-1-7-1-1 1-4 0-5 0h-4v-1-1l2 1c2 0 2 0 3-1l-1-2 1-1v-1z" class="j"></path><path d="M247 541c2-1 3-1 5 0h0l-1 1v1h-3l-1 1-1-2 1-1z" class="X"></path><path d="M252 541c2 1 3 1 5 1l1-1h1v1 5h-1c-2-1-5-1-7-1-1 1-4 0-5 0h-4v-1-1l2 1c2 0 2 0 3-1l1-1h3v-1l1-1z" class="K"></path><path d="M259 542v5h-1c-2-1-5-1-7-1h1c1 0 2 0 3-1-1 0-1 0-1-1l1-1c1 1 2 1 3 2h1v-3z" class="a"></path><path d="M251 543c0 1 0 1 1 2h0c1 0 1 0 2-1 0 1 0 1 1 1-1 1-2 1-3 1h-1c-1 1-4 0-5 0h-4v-1-1l2 1c2 0 2 0 3-1l1-1h3z" class="j"></path><path d="M222 516c3 1 5 3 7 5 1 2 2 4 2 6 1 1 1 3 2 3l1 1h3 3c-2 0-7 0-9 1v2h-1v-2h-5-2c0 2 1 5-1 6v-5-1h-2v1l1 1v4h-1v-5c-1-1-2-1-2-1l-1 1 1 1c1 1 1 2 1 4h0-1v-3h-1v2 1h-4l-1-4 1-1h0c1 1 2 1 2 2 1-1 1-1 1-2v-1h2v-1c0-1 0-1 1-1l-1-2 1-1 1 1v-1 1h2c1 0 1-1 1-2-1-1 0-1 0-1 0-2-1-5-1-6l-1-1c1-1 1-1 1-2z" class="g"></path><path d="M228 530l1-1v-6c1 1 1 2 1 4 1 1 1 2 0 4h-5-1v-1h3 1z" class="e"></path><path d="M222 516c3 1 5 3 7 5h-1c-1 0-2-1-2-2-1-1-1-1-2-1h-1c1 2 2 2 3 4h0c1 2 0 6 1 7 0 0 1 0 1 1h-1-2v-1c0-1 1-2 0-3h-2c-1-1 0-1 0-1 0-2-1-5-1-6l-1-1c1-1 1-1 1-2z" class="p"></path><path d="M275 524c2 2 0 4 2 6h0 0-4v1h0c-1 1-2 1-2 1v4h0l-1 1h-1-1v-1c-1 1-2 1-4 1v-3 1c-1 0-1 1-1 1-2 2-6 1-8 2h-3v-1l-1-1c0-2 0-4 1-6h7c1-1 2 0 3-1v-1c0-1 1 0 2 0h7c2 0 3 0 4-1v-3z" class="H"></path><path d="M254 536v-1h1v-1c0-1 0-1 1-2h2l1 3-1 1h-4z" class="I"></path><path d="M275 524c2 2 0 4 2 6h0 0c-3-1-15 0-18 0 1-1 2 0 3-1v-1c0-1 1 0 2 0h7c2 0 3 0 4-1v-3z" class="a"></path><path d="M259 535v-2-1-1h6c-1 1-1 2-1 3v1c-1 0-1 1-1 1-2 2-6 1-8 2h-3v-1c1 0 1 0 2-1h4l1-1z" class="Q"></path><path d="M265 531h8c-1 1-2 1-2 1v4h0l-1 1h-1-1v-1c-1 1-2 1-4 1v-3c0-1 0-2 1-3z" class="U"></path><path d="M268 533l2 2 1 1-1 1h-1-1v-4z" class="m"></path><path d="M268 536l-2-2c-1-1-1-1 0-2h2 0v1 4-1z" class="E"></path><path d="M208 512c8-1 14-2 21-1l3 1c1 2 3 4 4 7 2 3 3 5 3 9l-1 2-1 1h-3l-1-1c-1 0-1-2-2-3 0-2-1-4-2-6-2-2-4-4-7-5 0 1 0 1-1 2l1 1c0 1 1 4 1 6 0 0-1 0 0 1 0 1 0 2-1 2h-2v-1 1l-1-1-1 1 1 2c-1 0-1 0-1 1v1h-2l-15-1h0l1-9 2-2h0l1-1v-2c1 0 1 0 2-1 0 0 1 0 2-1h-1-1v-1l1-2z" class="X"></path><path d="M231 516c1 1 2 2 3 4h-2l-3-3 2-1z" class="H"></path><path d="M213 521l3-3v1h0c-1 1-1 2-2 3v2c0 2 0 2-1 3h0c0-1 0-1-1-2v-1l1-3z" class="p"></path><path d="M226 513c1 1 4 2 5 3l-2 1c-2-1-3-2-5-2 0-1 0-1-1-1h0 0l3-1z" class="D"></path><path d="M221 516h1c0 1 0 1-1 2l1 1c0 1 1 4 1 6 0 0-1 0 0 1 0 1 0 2-1 2h-2v-1-1-5c1-1 0-2 0-3l1-1-1-1h1z" class="U"></path><path d="M208 515c0-1 1-1 1-1h3v-1c2 0 2 0 3 1-3 2-6 4-7 7l-1-1c-1-1-1-2 0-4 0 0 1 0 2-1h-1z" class="H"></path><path d="M204 520h0l1-1v-2c1 0 1 0 2-1-1 2-1 3 0 4l1 1-1 2c0 2 0 6-1 8-1-1-1-1-2-1s-2 0-3 1l1-9 2-2z" class="J"></path><path d="M204 523c1 0 1 0 2 1v3-1c-1 0-2-2-2-3z" class="Q"></path><path d="M208 512c8-1 14-2 21-1l3 1c1 2 3 4 4 7l-1 1c-2-3-4-5-6-6s-5-2-7-2c-1-1-5 1-7 2-1-1-1-1-3-1v1h-3s-1 0-1 1h-1v-1l1-2z" class="L"></path><path d="M212 525c0 1 1 4 0 5h-1v-2h-1v2h-1-1v-5c1-4 4-8 7-10 4-2 7-3 11-2l-3 1h0 0c1 0 1 0 1 1h0c-1 0-2 0-3 1h0-1c-1 1-3 1-4 2l-3 3-1 3v1z" class="E"></path><path d="M212 524l-2-1v-1c1 0 1-1 2-1v-1h0v1h1l-1 3z" class="V"></path><path d="M224 515c2 0 3 1 5 2l3 3h2l1 1v-1l1-1c2 3 3 5 3 9l-1 2-1 1h-3l-1-1c-1 0-1-2-2-3 0-2-1-4-2-6-2-2-4-4-7-5h-1 0c1-1 2-1 3-1h0z" class="j"></path><path d="M233 525c1 2 1 4 1 6l-1-1c-1 0-1-2-2-3l2-2z" class="Z"></path><path d="M224 515c3 1 6 4 7 6 1 1 1 2 2 3v1l-2 2c0-2-1-4-2-6-2-2-4-4-7-5h-1 0c1-1 2-1 3-1zm12 4c2 3 3 5 3 9l-1 2h-1c-2-1-2-3-2-5-1-2-2-3-3-5h2l1 1v-1l1-1z" class="Q"></path><path d="M317 529l10 1c1 0 3 1 4 2h0v6 1c-7-1-14 0-20 0-1 1-1 3-1 4v1 2h0l-1 1h-1l-1-1h-5-2-6-16c1 0 2 0 3-1h0s0-1 1-2v-1-2c-1 0-2 0-3-1h8c-1 0-3-1-4-1l-1 1c-2 0-5 0-7-1-1 0-3 0-4-1v-1-4s1 0 2-1h0v-1h4 0 0 2 7 6 9 3v-1c2 0 3 1 5 0h8z" class="C"></path><path d="M277 530h2v2h0c1 0 0 0 1 1h-1v1 2l-1 1h-2l1-7h0z" class="f"></path><path d="M292 530h9 0-6l1 1h7v6h-1-1-1-1-2c-1 0-1 0-2-1v-4h-1c0 2 0 4-1 5 0-2 0-4 1-5 0-1-1-1-2-1v-1z" class="Q"></path><path d="M299 537v-5l1-1 1 1c0 1-1 3-1 5h-1zm-4-5l1-1 2 1h-1v4 1c-1 0-1 0-2-1v-4z" class="a"></path><path d="M286 530h6v1c1 0 2 0 2 1-1 1-1 3-1 5h-3 0l-1-1-1 1h-4-2-4l1-1v-2-1h1c-1-1 0-1-1-1h0v-2h7z" class="J"></path><path d="M288 537h-3c0-2 1-4 1-5h1 0v1 1h1c1-1 1-2 3-2h0v2 2 1h-1l-1-1-1 1z" class="l"></path><path d="M279 530h7l-1 1c-1 1-1 1-1 2h0c0 2 0 2-1 4v-2h-1v1c-1 0-1 0-1 1-1 0-1 0-2-1h0v-2-1h1c-1-1 0-1-1-1h0v-2z" class="F"></path><path d="M317 529l10 1c1 0 3 1 4 2h0c-2 0-3 1-4 1-1 1 0 3-1 4l-1-1c0-1 1-3 0-4h-3c-1 1 0 2-1 3v1h0c-1 0-1-1-1-2v2l-4-1h-2c-1 0-1 0-2-1-1 2 0 2-1 3h-2v-7h-4-1v-1c2 0 3 1 5 0h8z" class="Q"></path><path d="M311 537v-1-2-1s0-1 1-1h4v3h-2c-1 0-1 0-2-1-1 2 0 2-1 3z" class="B"></path><path d="M316 532h4v2 2l-4-1v-3z" class="I"></path><path d="M287 539c2 1 5 0 8 0h16c-1 1-1 3-1 4v1 2h0l-1 1h-1l-1-1h-5-2-6-16c1 0 2 0 3-1h0s0-1 1-2v-1-2c-1 0-2 0-3-1h8z" class="W"></path><path d="M299 543c1-1 3-1 5-1h1s0 1 1 1c0 0 0 1 1 1h0l-4 1c-1-1 0-1-1-2-1 1 0 1-1 1 0 1-1 1-1 1l-1-1v-1z" class="U"></path><path d="M282 542c1-1 1-1 2-1l1 1h1c1 0 1 1 1 1l1 1 1-1h0c1 1 2 1 3 0h0 1 1 1c0 1 0 1 1 0h0c1 1 1 1 2 1l1-1v1l1 1v1h-6-16c1 0 2 0 3-1h0s0-1 1-2v-1z" class="c"></path><defs><linearGradient id="U" x1="293.662" y1="568.718" x2="262.244" y2="566.339" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#U)" d="M311 539c6 0 13-1 20 0h1 1c1 1 1 1 1 2 0 2-2 3-3 5-2 1-2 0-4 0h-2v-1c-1-1 0-1-1-1h0c-1 8 0 17-1 25h0c1 0 2-1 4-1h0v3c-1 1-2 1-2 3h1 0c3-1 5-1 7-2v1 1c-1 0-1 1-1 2-1 1-2 1-2 2-1 1-2 2-2 3v1l-1 2c-1 1-2 2-2 4h-1c-1 1-1 2-3 3-1 0-1 0-2-1h-3 0c-1 1-1 1-1 0h-4c-1 0-3 1-4 1s-4 0-5 1c-3 1-6 0-9 1h0c-2 1-4 1-5 2 0 1 0 1 1 1 0 0 1 1 2 1h-1-6-1c-1-1-1 0-2 0v-1h-2c-2 1-3 0-4 1-2 0-4-1-5-1-2 0-4 0-6-1-1 0-2 0-3-1-1-2-3 0-4-1v-2h-6 1c1-1 2-1 3-2h1l2-2v-1l1-1c0-1 0-2-1-3v-3h0v-4-2h0v-4-3-6-3-1-1c0-1-1 0-2-1l2-1v-1-2-1-1h0v-1h1v-5-1h1c1 2 0 4 1 5h4 0v-1c1 0 2-1 2-2v-2h2 6 0c0-1 0-1 1-2h3c1 1 2 1 3 1v2 1c-1 1-1 2-1 2h0c-1 1-2 1-3 1h16 6 2 5l1 1h1l1-1h0v-2-1c0-1 0-3 1-4z"></path><path d="M288 568v-3l1-1 1 1c0 2-1 2-2 3z" class="i"></path><path d="M279 583l1-1v1 7h-1-1v-1h1v-6z" class="j"></path><path d="M291 581c-1 1-2 1-3 1v-1c0-2-1-5 0-7v5h2c0 1 0 1 1 2z" class="P"></path><path d="M278 564v11h1v1h1c0 1 0 0-1 1v1h-1v-3c0-3-1-8 0-11z" class="O"></path><path d="M287 590c-1 0-2 0-2-1h0c2-1 5-1 7 0l1 1h-6z" class="P"></path><path d="M278 564c1 0 1 0 2 1v1h0c-1 2-1 5-1 6v3h-1v-11z" class="m"></path><path d="M278 575v3c0 2 0 3 1 5v6h-1v1h1-1-1c-1-5 0-10 1-15z" class="c"></path><path d="M288 573c1 0 1-1 3 0v4h0v4c-1-1-1-1-1-2h-2v-5-1z" class="S"></path><path d="M290 565c2 2 1 9 1 12h0 0v-4c-2-1-2 0-3 0v-5c1-1 2-1 2-3z" class="P"></path><path d="M276 594h2c1 1 1 0 2 1h1c1-1 1-1 2-1l1 1c-1 1-1 1-2 1h-1-2c-2 1-3 0-4 1-2 0-4-1-5-1-2 0-4 0-6-1h12v-1z" class="d"></path><path d="M258 587h2v2h0v1h1c1 0 1-1 2-2 0 2-1 5-2 6-1-2-3 0-4-1v-2h-6 1c1-1 2-1 3-2h1l2-2z" class="n"></path><path d="M258 587h2v2h0l-1 1h-1l-2-1 2-2z" class="T"></path><path d="M275 541c0 2 1 18 0 20v-3-5c0-2 0-6-1-8-1 1-3 1-4 1h0-5v-1c1 0 2-1 2-2v-2h2 6z" class="a"></path><path d="M267 543l1 1h0 3v1l-1 1h0-5v-1c1 0 2-1 2-2z" class="f"></path><path d="M301 587h1v1h1v1c-1 1-2 0-2 2v1h-6c-3-1-6 0-8 0v-1h1c1-1 2 0 3 0v-1c-2 1-2 1-4 0h6l-1-1 1-1 1 1c1-1 2-1 2-1 2-1 3 1 5-1h0z" class="K"></path><path d="M287 592c2 0 5-1 8 0h6 1c-3 1-6 0-9 1h0c-2 1-4 1-5 2 0 1 0 1 1 1 0 0 1 1 2 1h-1-6-1c-1-1-1 0-2 0v-1h1c1 0 1 0 2-1l-1-1c-1 0-1 0-2 1h-1c-1-1-1 0-2-1h-2v-1c2-1 8-1 11-1z" class="E"></path><path d="M279 572c1-1 0-5 1-6 1 1 1 1 1 3h0c1-1 0-2 0-3h0 0c2 1 1 3 1 5 0-1 0-3 1-4v-1 20h0c0-1 0-1-1-2v-1h-2v-1l-1 1c-1-2-1-3-1-5h1v-1c1-1 1 0 1-1h-1v-1-3z" class="T"></path><path d="M280 582v-2-1c1 1 2 2 2 3v1h-2v-1z" class="X"></path><path d="M271 569h0c1-1 0-3 0-4h0c1-2 0-4 0-5h1l1 1h1v-3h0 1v3 15 10h-3v-4l-1 1c-2-2-1-7-3-10h-1c0-1 0-2 1-3h2l1-1z" class="D"></path><path d="M272 575c1 0 1 0 1-1h-1c0-1 0-2-1-2h0v-2h1v-1l1 1 1-1c0-2 1-4 0-7l1-1v15 10h-3v-4-7z" class="Q"></path><path d="M272 575h1v1 2 3h1 0c0-2 0-4 1-5v10h-3v-4-7z" class="G"></path><path d="M261 571h3v1c1 2 1 3 1 5h1 0l2 1v-5c2 3 1 8 3 10l1-1v4h3c0 3 0 4-1 6l-1 1h-9v-6c-1 0-3 0-4 1v1-2h-2v-1l1-1c0-1 0-2-1-3v-3h0v-4-2l2-1h1v-1z" class="i"></path><path d="M264 572c1 2 1 3 1 5h1v3 2 4 1c1 1 2 0 2 1v2h0c0 1-1 1-2 2v-1-2-7c-1 0-2-1-2-2v-4-4z" class="o"></path><path d="M268 573c2 3 1 8 3 10l1-1v4 1c1 1 1 2 0 3v1c-2 0-2-1-4-1h0v-2c0-1-1 0-2-1v-1-4-2-3h0l2 1v-5z" class="d"></path><path d="M266 577l2 1v4l-1 1-1-1v-2-3h0z" class="D"></path><path d="M266 586l1 1v-1c1 0 1-1 2-2h0v-1h1v3l-1 1 2 2c0-1 1-1 1-2 1 1 1 2 0 3v1c-2 0-2-1-4-1h0v-2c0-1-1 0-2-1v-1z" class="e"></path><path d="M261 571h3v1 4 4c0 2-1 5 0 7-1 0-3 0-4 1v1-2h-2v-1l1-1c0-1 0-2-1-3v-3h0v-4-2l2-1h1v-1z" class="a"></path><path d="M259 585h0c1 1 0 2 1 2h-2v-1l1-1z" class="g"></path><path d="M258 575h1v3l1 2c1 0 1-1 2-1 0 1-1 1-1 2h0c-1 1-1 1-1 2v1l2-1v2h-3 0c0-1 0-2-1-3v-3h0v-4z" class="T"></path><path d="M261 571h3v1 4 4c-1-1-1-2-1-3h-1v1 1c-1 0-1 1-2 1l-1-2v-3h-1v-2l2-1h1v-1z" class="h"></path><path d="M261 572c1 0 1 0 2 1v2h-1v-1h-1s0-1-1-2h1z" class="T"></path><path d="M258 573l2-1c1 1 1 2 1 2l1 1h-1c0 1-1 1-1 2v1h1 1v1c-1 0-1 1-2 1l-1-2v-3h-1v-2z" class="l"></path><path d="M259 541h1c1 2 0 4 1 5h4 0 5 0c1 0 3 0 4-1 1 2 1 6 1 8v5h-1 0v3h-1l-1-1h-1c0 1 1 3 0 5h0c0 1 1 3 0 4h0l-1 1h-2c-1 1-1 2-1 3h1v5l-2-1h0-1c0-2 0-3-1-5v-1h-3v1h-1l-2 1h0v-4-3-6-3-1-1c0-1-1 0-2-1l2-1v-1-2-1-1h0v-1h1v-5-1z" class="N"></path><path d="M270 546c1 0 3 0 4-1 1 2 1 6 1 8h-3 0v1l1 1h0v3h-2v-1c1-2 0-2 0-4 0 0 0-1 1-1-1-2-1-4-1-5l-1-1h0 0z" class="Z"></path><path d="M271 557v1h2v-3h0l-1-1v-1h0 3v5h-1 0v3h-1l-1-1h-1c0 1 1 3 0 5h0c0 1 1 3 0 4h0-1v-6h-1v-5-3s0-1 1-1c1 1 0 2 1 4v-1z" class="H"></path><path d="M265 546h5 0-4v3h2v1c-1 1-1 2-2 3l1 1v1l-1 1 1 1v1c0 1-1 1 0 2 0 1-1 2-1 3l1 1c0 1-1 1-1 2-1 1 0 5 0 6v5h0-1c0-2 0-3-1-5v-1c1-5 0-12 0-17 0-2-1-6 1-8h0z" class="J"></path><path d="M259 541h1c1 2 0 4 1 5h4c-2 2-1 6-1 8 0 5 1 12 0 17h-3v1h-1l-2 1h0v-4-3-6-3-1-1c0-1-1 0-2-1l2-1v-1-2-1-1h0v-1h1v-5-1z" class="T"></path><path d="M261 570v-1h0v-1c-1-1-1-1-1-2 1-1 1-2 2-3v6l1 1h-2z" class="O"></path><path d="M258 548h1 0l1-1c2 3 0 6 2 9-1 1-1 2-1 4 1 1 1 0 1 2v1c-1 1-1 2-2 3 0 1 0 1 1 2v1h0v1h-1l1 1v1h-1l-2 1h0v-4-3-6-3-1-1c0-1-1 0-2-1l2-1v-1-2-1-1h0z" class="P"></path><path d="M278 546h16 6 2 5l1 1v6h-2c0 2 0 3 1 4v1h1v11 1l1 1c-1 0-1 0-2 1h0 1l-1 2c-1 1-1 2-2 2v4c0 2 0 3-1 5h0-1v2l-1 1v-1h-1 0c-2 2-3 0-5 1 0 0-1 0-2 1l-1-1h0c-1-2 0-5 0-7 0-7 0-13-1-19 0-2 0-3-1-4-2-1-2-1-4-1-3 3-4 5-4 9v1c-1 1-1 3-1 4 0-2 1-4-1-5h0 0c0 1 1 2 0 3h0c0-2 0-2-1-3-1 1 0 5-1 6 0-1 0-4 1-6h0v-1c-1-1-1-1-2-1 0-1-1-3 0-4v-2-5s-1-1-1-2c1-1 1-2 0-4l1-1z" class="O"></path><path d="M294 564h1v2 3h-1v-5z" class="X"></path><path d="M278 560l1 1c-1 0-1 0-1 1l1 1c1-1 1-1 1-2 1 1 1 1 1 2s1 1 1 2h0 0-2c-1-1-1-1-2-1 0-1-1-3 0-4h0z" class="T"></path><path d="M294 564c-1-1-1-4-1-5l-1-1v-2c0-1 0-1-1-2-1 1-1 1-2 1h0v-1c1 0 1-1 2-1h1v-3h1 1v1 1h0v2c1 1 1 1 0 2h-1 0v1c1 0 1 1 2 1 0 1-1 1 0 2 0 0 1 0 1 1s-1 2-1 3h-1z" class="K"></path><path d="M278 546h16 6 2v1h-1-5-1s-1 1-2 1v1-1l-1 1s0 1-1 1-2 1-3 2l-1 1c-1 1-3 2-4 3-2 2-3 3-3 5 0 1 0 1-1 2l-1-1c0-1 0-1 1-1l-1-1h0v-2-5s-1-1-1-2c1-1 1-2 0-4l1-1z" class="m"></path><path d="M282 549v-1c-1 1-1 1-2 1 0-1 0-1 1-2h4l1 1c-2 1-2 2-3 3 0 0 0-1-1-2z" class="Q"></path><path d="M285 547h7 3s-1 1-2 1v1-1l-1 1-1-1h-3-1-1l-1-1z" class="E"></path><path d="M282 549c1 1 1 2 1 2v1h2c1 0 2 0 2 1-1 1-3 2-4 3l-1-1h1v-2c-1-1-2-1-3-1v-1c1 0 2 0 2-2z" class="F"></path><path d="M278 560v-2c1-1 1-1 2-1l1-1-1-1v-1c1 0 1 0 2 1l1 1c-2 2-3 3-3 5 0 1 0 1-1 2l-1-1c0-1 0-1 1-1l-1-1z" class="J"></path><path d="M287 548h1 3l1 1s0 1-1 1-2 1-3 2l-1 1c0-1-1-1-2-1h-2v-1c1-1 1-2 3-3h1z" class="Q"></path><path d="M286 548h1v1c0 2-1 2-2 3h-2v-1c1-1 1-2 3-3z" class="B"></path><path d="M294 554c2 1 4 2 4 3v1 2c1 2 1 5 2 8v6 13h1c-2 2-3 0-5 1 0 0-1 0-2 1l-1-1h0c1-2 1-17 1-19h1v-3-2c0-1 1-2 1-3s-1-1-1-1c-1-1 0-1 0-2-1 0-1-1-2-1v-1h0 1c1-1 1-1 0-2z" class="l"></path><path d="M294 554c2 1 4 2 4 3v1 2c1 2 1 5 2 8v6l-1 5h0c0-4 0-16-2-20h-2v1c-1-1 0-1 0-2-1 0-1-1-2-1v-1h0 1c1-1 1-1 0-2z" class="Y"></path><path d="M295 566c1 5 1 9 1 14 0 1 1 2 1 3s1 2 2 3v-6-1h0l1-5v13h1c-2 2-3 0-5 1 0 0-1 0-2 1l-1-1h0c1-2 1-17 1-19h1v-3z" class="f"></path><path d="M299 580c0 2 1 5 0 7h-4v-5l1-2c0 1 1 2 1 3s1 2 2 3v-6z" class="g"></path><path d="M302 546h5l1 1v6h-2c0 2 0 3 1 4v1h1v11 1l1 1c-1 0-1 0-2 1h0 1l-1 2c-1 1-1 2-2 2v4c0 2 0 3-1 5h0-1v2l-1 1v-1h-1 0-1v-13-6c-1-3-1-6-2-8v-2-1c0-1-2-2-4-3v-2h0v-1-1l-1-1v-1c1 0 2-1 2-1h1 5 1v-1z" class="h"></path><path d="M307 572h1l-1 2c-1 1-1 2-2 2 0-1-1-3 0-4h2z" class="V"></path><path d="M300 568c2 2 1 7 1 11 0 2-1 6 0 8h0-1v-13-6z" class="l"></path><path d="M303 587c-1 0-1 0-1-1v-4-14c-1-2-1-4-1-6 0-1-1-2-1-3v-1h1c3 4 2 21 2 26v1 2z" class="o"></path><path d="M300 553c2 1 4 1 6 0 0 2 0 3 1 4v1h1v11 1h0c-1 1-3 1-4 0v-1-7l-1-1v-1c0-2-2-3-3-4v-1-2z" class="R"></path><path d="M303 560c1-1 1-2 2-2l1 1c0 1-1 1-3 2v-1z" class="J"></path><path d="M304 562h0c1 0 2-1 3-1s0 2 1 3c-1 0-2 0-2 1h-1l-1 4v-7z" class="d"></path><path d="M300 553c2 1 4 1 6 0 0 2 0 3 1 4v1c-1-1-3-1-3-1h-2v-1-1l-2 1v-1-2z" class="D"></path><path d="M306 565c0-1 1-1 2-1v3 2 1h0c-1 1-3 1-4 0v-1l1-4h1z" class="V"></path><path d="M306 565c0-1 1-1 2-1v3h-2l-1-1 1-1z" class="H"></path><path d="M302 546h5l1 1v6h-2c-2 1-4 1-6 0v2c-1-1-3-2-4-3 0 0-1-1-2-1v-1l-1-1v-1c1 0 2-1 2-1h1 5 1v-1z" class="R"></path><path d="M299 550s1 0 2-1h2c-1 1-1 1-1 2-1 0-1 0-1 1h-1c0-1 0-1-2-2h1zm-3 2c1 0 2 0 4 1h0 0v2c-1-1-3-2-4-3z" class="J"></path><path d="M302 547h2c0 1 1 1 2 1v1l-1 1h-1c-1 0-1-1-1-2h0v1h-2c-1 1-2 1-2 1h-1l-1 1c-1-1-2-1-3-1l-1-1v-1c1 0 2-1 2-1h1 5 1z" class="D"></path><path d="M295 547h1v1h2l1 1v1h-1l-1 1c-1-1-2-1-3-1l-1-1v-1c1 0 2-1 2-1z" class="B"></path><path d="M311 539c6 0 13-1 20 0h1 1c1 1 1 1 1 2 0 2-2 3-3 5-2 1-2 0-4 0h-2v-1c-1-1 0-1-1-1h0c-1 8 0 17-1 25h0c1 0 2-1 4-1h0v3c-1 1-2 1-2 3h1 0c3-1 5-1 7-2v1 1c-1 0-1 1-1 2-1 1-2 1-2 2-1 1-2 2-2 3v1l-1 2c-1 1-2 2-2 4h-1c-1 1-1 2-3 3-1 0-1 0-2-1h-3 0c-1 1-1 1-1 0h-4c-1 0-3 1-4 1s-4 0-5 1h-1v-1c0-2 1-1 2-2v-1h-1l1-1v-2h1 0c1-2 1-3 1-5v-4c1 0 1-1 2-2l1-2h-1 0c1-1 1-1 2-1l-1-1v-1-11h-1v-1c-1-1-1-2-1-4h2v-6h1l1-1h0v-2-1c0-1 0-3 1-4z" class="U"></path><path d="M315 549v-3c1 0 1 0 2-1 0 1 1 1 2 1h1l-1 2h0l-1 1c-1-1-1-1-2-1v1h-1z" class="H"></path><path d="M310 546c1-1 2 0 3-1v-2l1-1v3l-1 4 1 1-1 1c-1 1-1 1-2 1l-1-1c0-1 1-3 0-5z" class="X"></path><path d="M310 565c0-2 1-5 0-7v-1-6l1 1c1 0 1 0 2-1v1l1 1c0 1-1 2-1 2v2l-1 1c1 0 2-1 2 1h0v6 1h-1-1v-2-1c-1 1-1 2-2 2z" class="f"></path><path d="M310 546h0c1 2 0 4 0 5v6 1c1 2 0 5 0 7v2c0 1 0 1-1 1 0 2 0 3-1 4h0-1 0c1-1 1-1 2-1l-1-1v-1-11h-1v-1c-1-1-1-2-1-4h2v-6h1l1-1z" class="O"></path><path d="M306 553h2v5h-1v-1c-1-1-1-2-1-4z" class="d"></path><path d="M310 546h0c1 2 0 4 0 5v6 1c1 2 0 5 0 7v2c0 1 0 1-1 1v-21l1-1z" class="i"></path><path d="M311 539c6 0 13-1 20 0h1 1c1 1 1 1 1 2 0 2-2 3-3 5-2 1-2 0-4 0h-2v-1c-1-1 0-1-1-1h0l-1-2h0c-1 1-1 2-1 3l-2-2h-3v-1h3 1v-1h-6l-1 1-1 1v2c-1 1-2 0-3 1h0 0v-2-1c0-1 0-3 1-4z" class="K"></path><path d="M322 545c0-1 0-2 1-3h0l1 2c-1 8 0 17-1 25h0c1 0 2-1 4-1h0v3c-1 1-2 1-2 3h1 0c3-1 5-1 7-2v1 1c-1 0-1 1-1 2-1 1-2 1-2 2-1-1-1-2-2-2-3-1-8 1-11 3 0 1 0 1-1 1 0-1-1-2-1-4h0l-1-1c0-1 0-1 2-1l1-1v-1h-1c-1-1-1-3-1-4v-1h0v-6-3-6-3h1v-1c1 0 1 0 2 1l1-1h0l1-2h1 1v-1z" class="Q"></path><path d="M318 565v-2h1v4c-1 0-1 1-2 1l-1-1v-1c1-1 1-1 2-1h0z" class="B"></path><path d="M320 561v-3c-1-2-1-3 0-5h1v9c-1 0-1 0-1-1z" class="H"></path><path d="M321 564c1 1 1 3 1 5h1 0l-2 2c-1 1-1 2-1 3l-2 3v1h-1v-2-3-1l4-4c1-1 0-3 0-4z" class="c"></path><path d="M327 568h0v3c-1 1-2 1-2 3h1-1l-1 1h-1c-1 1-3 2-5 2l2-3c0-1 0-2 1-3l2-2c1 0 2-1 4-1z" class="B"></path><path d="M333 572v1 1c-1 0-1 1-1 2-1 1-2 1-2 2-1-1-1-2-2-2-3-1-8 1-11 3 0 1 0 1-1 1 0-1-1-2-1-4h0l-1-1c0-1 0-1 2-1l1-1v3 2h1v-1c2 0 4-1 5-2h1l1-1h1 0c3-1 5-1 7-2z" class="O"></path><path d="M322 545c0-1 0-2 1-3h0l1 2c-1 8 0 17-1 25h-1c0-2 0-4-1-5-1-3 1-8 0-11h-1c-1 2-1 3 0 5v3c-1 1-1 1-2 1h-1v2l1 1h0c-1 0-1 0-2 1v1l1 1h-1l-1-1h0v-6-3-6-3h1v-1c1 0 1 0 2 1l1-1h0l1-2h1 1v-1z" class="d"></path><path d="M317 558v-1c-1-1 0-1 0-2l1-1 1 3c-1 1-1 1-2 1z" class="N"></path><path d="M315 558v-6h1v1h2v1l-1 1c0 1-1 1 0 2v1h-2z" class="Q"></path><path d="M320 546h1v6h-1c-1-1 0-3-1-4h0l1-2z" class="C"></path><path d="M315 567c1-3 0-5 0-8h1 1v3 2l1 1h0c-1 0-1 0-2 1v1l1 1h-1l-1-1h0z" class="Z"></path><path d="M316 549v-1c1 0 1 0 2 1l1-1c1 1 0 3 1 4h-4-1v-3h1z" class="L"></path><path d="M318 549l1-1c1 1 0 3 1 4h-4-1v-3h1v3h2v-3z" class="V"></path><path d="M310 565c1 0 1-1 2-2v1 2h1 1c0 1 0 1 1 2 0 1 0 3 1 4h1v1l-1 1c-2 0-2 0-2 1l1 1h0c0 2 1 3 1 4 1 0 1 0 1-1 3-2 8-4 11-3 1 0 1 1 2 2-1 1-2 2-2 3v1l-1 2c-1 1-2 2-2 4h-1c-1 1-1 2-3 3-1 0-1 0-2-1h-3 0c-1 1-1 1-1 0h-4c-1 0-3 1-4 1s-4 0-5 1h-1v-1c0-2 1-1 2-2v-1h-1l1-1v-2h1 0c1-2 1-3 1-5v-4c1 0 1-1 2-2l1-2h0c1-1 1-2 1-4 1 0 1 0 1-1v-2z" class="c"></path><path d="M309 568c1 0 1 0 1-1 0 3 0 5-1 7v3h1 2 0l-1 1h0c-1 0-2 0-2 1l-1-1c1-1 1-2 1-3-1-1-1-1-1-3 1-1 1-2 1-4z" class="l"></path><path d="M309 587h0-2v-1c2-2 4-3 7-4l-1 1v1h0c0 1 0 1 1 1l-1 1h-1c-1 0-1 0-1-1l-1 1-1 1z" class="J"></path><path d="M313 566h1c0 1 0 1 1 2 0 1 0 3 1 4h1v1l-1 1c-2 0-2 0-2 1-1-2 0-4-1-6v1l-1-1v-3h1z" class="g"></path><path d="M312 566h1v3 1l-1-1v-3z" class="b"></path><path d="M310 565c1 0 1-1 2-2v1 2 3 4c-1 1-2 1-3 1 1-2 1-4 1-7v-2z" class="h"></path><path d="M308 572h0c0 2 0 2 1 3 0 1 0 2-1 3l1 1-1 4c-1 1-2 0-2 1s0 1-1 2v-1h-1c1-2 1-3 1-5v-4c1 0 1-1 2-2l1-2z" class="S"></path><path d="M305 576c1 0 1-1 2-2 0 4 1 5-1 8-1 0-1-1-1-2v-4z" class="J"></path><path d="M316 580c1 0 1 0 1-1 3-2 8-4 11-3 1 0 1 1 2 2-1 1-2 2-2 3v1l-1 2c-1 1-2 2-2 4h-1c-1 1-1 2-3 3-1 0-1 0-2-1h-3 0c-1 1-1 1-1 0h-4c-1 0-3 1-4 1-1-1-2 0-3-1v-1c1-1 2 0 4 0l1-1h2v-1h-2l1-1 1-1c0 1 0 1 1 1h1l1-1c-1 0-1 0-1-1h0v-1l1-1c1 0 1-1 2-2z" class="S"></path><path d="M316 588c1 0 3-1 4-2l-1 4v-1h0c-2 0-4 1-6 0 1-1 2-1 3-1z" class="D"></path><path d="M320 586h1c1 0 2-1 4-2v1h0v3h-1c-1 0-2 0-3 1s-1 1-2 1l1-4z" class="N"></path><path d="M315 587c1 0 1-1 2-2s2-1 3-2h3c1 0 1-1 2-1l2 2c-1 1-2 2-2 4v-3h0v-1c-2 1-3 2-4 2h-1c-1 1-3 2-4 2l-1-1z" class="R"></path><path d="M313 584c4-2 9-4 13-4 1 0 1 0 2 1v1l-1 2-2-2c-1 0-1 1-2 1h-3c-1 1-2 1-3 2s-1 2-2 2v-2h-1c-1 0-1 0-1-1h0z" class="F"></path><path d="M316 580c1 0 1 0 1-1 3-2 8-4 11-3 1 0 1 1 2 2-1 1-2 2-2 3-1-1-1-1-2-1-4 0-9 2-13 4v-1l1-1c1 0 1-1 2-2z" class="M"></path><path d="M407 282h2l1-1c-1 2 0 4 0 6l1 1h0v-1l1-1 1 1 1-1v2h1v-1h1c0 1 1 1 1 1 0 1 1 4 0 5v1 3 10 6l1 12v3h-3c1 1 2 1 3 2v4h-2 0c0 1-1 0-2 0v3c1 1 2 1 3 1h0l1 1v2h-1-1l1 1h1c0 2 0 3-1 4h-2c1 0 2 0 2 1 1 0 1 0 1 2v3c-1 1-2 1-3 0v1h-14-5 0-1c-2 2-8 1-11 2h0-4l1-2h-1l-1-1c-1-1-1-1-3-1-1 0-1 0-1 1v1 3h-1l-1 1c-2 0-3 0-5 1h-2c-1 0-3 0-5 1v-23-5-11c0-2 0-4 1-6v3h4v6c0 1 0 2 1 3 1-2 0-5 0-7v-1c0-2 1-4 0-6 0-2 0-2 1-4l-1-1v-3-1c1-1 1-1 2-1h0-1c-1-1 0-2-1-4v-5l3-1-1-1h-1v-1l1-1 2-1c1-1 2-2 4-2v1h3c2 1 4 0 5-1l17-1h0c-1-1-1 0 0-1h3 2c1 0 3 0 3-1l-1-1z" class="W"></path><path d="M410 292v-2c1-1 5 0 7 0v3 1h0l-1-1h-1v-2l-1-1c-1 0-2 0-3 1l-1 1z" class="D"></path><path d="M410 292l1-1c1-1 2-1 3-1l1 1v2h-5v-1z" class="L"></path><path d="M400 284h3c1 1 5 1 5 1s1 2 1 3c-1 1-2 0-3 1l1-1c-1-1-1-1-3-2-1-1-3-1-4-1h0c-1-1-1 0 0-1z" class="J"></path><path d="M392 318c2 1 3 2 4 4l1 1c-1 1-2 1-3 1h0c-1-2-2-3-4-5 1 0 1 0 2-1z" class="H"></path><path d="M411 346c0-1 0-2-1-4h1 3 3 1c0 2 0 3-1 4h-2-4z" class="M"></path><path d="M411 346c0-1 0-2-1-4h1 3l1 1v2c-2 0-3 0-4 1z" class="I"></path><path d="M410 339v-2h1 3c1 1 2 1 3 1h0l1 1v2h-1-1l1 1h-3-3l1-1h-1l-1-2z" class="D"></path><path d="M410 339h1s1 0 1-1c1 1 1 1 2 1v1 1h-3l-1-2z" class="C"></path><path d="M397 290c4 0 8-1 12 0v3c-1 1-5 0-7 1h0-3c1-1 2-1 2-1 0-1 0-1-1-1 0-1 0 0-1-1 0-1-1-1-2-1z" class="I"></path><path d="M410 311c1 0 2 1 3 1h0c-1 1-1 1-2 1v2l1 1v1h-1v1l1 1 1 2c1 0 1 1 2 2v2h-3v2l1 1h0-3v-17z" class="B"></path><path d="M413 321c1 0 1 1 2 2v2h-3v-1c0-2 0-2 1-3zm-3-19v-4c0-1 0-3 1-4h5 1 0v3c-1 1-2 0-3 1v4h-1-2-1zm2 50l-1 1v-1c0-1-1-5 0-6l6 1c1 0 1 0 1 2v3c-1 1-2 1-3 0h-3z" class="M"></path><path d="M412 352c1 0 1 0 2-1s2-2 4-2v3c-1 1-2 1-3 0h-3z" class="B"></path><path d="M400 334h1c2-1 6-1 8-1l-1 1c0 1 2 2 1 4h-5 1 0c2 0 3 0 4 1v2c-1 1-4 0-6 0v-1-1h0l-1-1h0 1v-1-2h0-3-1l1-1z" class="L"></path><path d="M402 328c2 1 2 1 4 1h3c1 1 0 3 0 4-2 0-6 0-8 1h-1-3v-1-3c1-2 3-1 5-2z" class="N"></path><path d="M396 347h1s0-1 1-1h5l6 1h1c-1 1-1 2-1 2h-10c3 0 7 0 10 1v2c-1 1-9 1-11 0h-1v-3-1c-1 1-1 3-1 5h-1c-1-1-1-1-2-1v-2h1l1-1h-1-1v-1h0v-1h2 1z" class="I"></path><path d="M397 322h0c-1-2-3-3-5-5h7 5l1 1c1-1 3-1 4 0v6 3c-1 1-5 1-7 1h-4v-3-1l-1-2z" class="C"></path><path d="M404 319h1c1 0 1 0 1 1s0 2-1 3h-1c0-1 0-1-1-2v-1l1-1z" class="O"></path><path d="M397 322l1-2c1 0 2 0 3 1 0 0 1 1 1 2s1 1 1 1h2s1 0 2-1v1h2v3c-1 1-5 1-7 1h-4v-3-1l-1-2z" class="k"></path><path d="M417 297v10 6l1 12v3h-3c1 1 2 1 3 2v4h-2 0c0 1-1 0-2 0v3h-3-1v-1-3-4h3v-1h0l-1-1v-2h3v-2c-1-1-1-2-2-2l-1-2-1-1v-1h1v-1l-1-1v-2c1 0 1 0 2-1h0c-1 0-2-1-3-1 0-1 0-2 1-3 0 0 1 0 2-1h-2c-1-1-1-3-1-4v-1h1 2 1v-4c1-1 2 0 3-1z" class="L"></path><path d="M412 325h3l-1 3h-1l-1-1v-2zm0 4h3c0 2 0 2-1 4h-1l-1-1h0 0 0c0-1 0-1 1-1 0-1 0-1-1-2zm-2 7c0-1 0-1 1-1h0 1c1 0 1-1 2-1v3h-3-1v-1zm0-33h4c1 2 0 4 0 6 0 1 1 1 1 2l-1 1v1 3 1h0v1h1c0-1 1-1 1-1 1 1 1 3 1 5l-1 1h-1c-1-1-1-2-2-2l-1-2-1-1v-1h1v-1l-1-1v-2c1 0 1 0 2-1h0c-1 0-2-1-3-1 0-1 0-2 1-3 0 0 1 0 2-1h-2c-1-1-1-3-1-4z" class="I"></path><path d="M397 323v1h1v1 3h4 0c-2 1-4 0-5 2v3 1h3l-1 1h1 3 0v2 1h-1 0l1 1h0v1 1l1 1h5v3c-1 1-4 1-6 1h0-5c-1 0-1 1-1 1h-1-1-2v1h0v1h1 1l-1 1h-1v2h0c-1-1-1-1-1-2v-2h-1c0 1 1 4 0 5v-7-16l-1-1 2-3 1 1c1-1 1-1 1-3 1 0 2 0 3-1z" class="j"></path><path d="M395 339v-1h-2v-3c1 0 2-1 3 0v5c0-1-1-1-1-1z" class="L"></path><path d="M393 329h3c1 1 0 3 1 4v1h-4v-1l1-1v-1c-1 0-1 0-1-2z" class="B"></path><g class="I"><path d="M402 338h-3-2v-3l1-1 1 1h1 3 0v2 1h-1z"></path><path d="M403 341h-1c-2 0-4 1-5 0 0-1 0-2 1-2 1-1 2-1 4-1l1 1h0v1 1z"></path></g><path d="M395 347c0-1 0-1-1-1h-1v-3c1 0 1 0 1-1h1 0l-2-1v-2h2s1 0 1 1c0 2 1 5 0 7h0-1z" class="C"></path><path d="M397 323v1h1v1 3h4 0c-2 1-4 0-5 2v3c-1-1 0-3-1-4h-3c-1 1-1 1-2 1l-1-1 2-3 1 1c1-1 1-1 1-3 1 0 2 0 3-1z" class="K"></path><path d="M397 323v1 4h-3l-1-1c1-1 1-1 1-3 1 0 2 0 3-1z" class="E"></path><path d="M403 346c-1 0-4 0-6-1 0-1 0-2 1-3 0 0 1-1 2 0h4 5v3c-1 1-4 1-6 1z" class="k"></path><path d="M400 285c1 0 3 0 4 1 2 1 2 1 3 2l-1 1c-3 0-19 0-21 1h1 11c1 0 2 0 2 1 1 1 1 0 1 1 1 0 1 0 1 1 0 0-1 0-2 1h3 0 0c2 0 5 0 7 1v2l-3 1h-8c-2 0-3-1-4 0h0-1c-2 1-3 0-5 1-1 0-2-1-3 0h-2c-2 0-3 0-4 1h-3-2c0-1 0-2 1-3s3-1 4-1c0-1-1-1-1 0h-3l-1-1c1-1 1-2 1-3 3-1 6-1 8-1v-1l-7 1-1-1 1-1 1-1h-1l-1-1h3c2 1 4 0 5-1l17-1z" class="N"></path><path d="M400 285c1 0 3 0 4 1 2 1 2 1 3 2l-1 1c-3 0-19 0-21 1h-1v1c0 1 1 2 1 3 2 0 10-1 10 0-1 0-1 0-2 1-1 0-3-1-4 0h-2c-2 0-5 0-6 1h-2c0-1-1-1-1 0h-3l-1-1c1-1 1-2 1-3 3-1 6-1 8-1v-1l-7 1-1-1 1-1 1-1h-1l-1-1h3c2 1 4 0 5-1l17-1z" class="I"></path><path d="M380 318l1 1c2-1 5-2 7-2h1 1c1 0 1 0 2 1-1 1-1 1-2 1 2 2 3 3 4 5h0c0 2 0 2-1 3l-1-1-2 3 1 1v16 7c1-1 0-4 0-5h1v2c0 1 0 1 1 2h0c1 0 1 0 2 1-2 2-8 1-11 2h0-4l1-2h-1l-1-1c-1-1-1-1-3-1h1l-1-1-1 1v-1-6-1-1-2-2-3-1-3-1h0c-1-1 0-1 0-2h1l-1-1s0-1-1-1l1-1v-1s0-1-1-1c1-1 1-1 1-2v-1h1l-1-1h2 1c1-1 1-1 2-1z" class="p"></path><path d="M389 346h2v7h-1c-1-1-1-5-1-7zm-4 3c0 1 0 4-1 5v1h0-4l1-2v-2c0-1 0-1 1-1l-1-1h4z" class="E"></path><path d="M390 329l1 1v16h-2v-8c0-4 0-6 1-9z" class="D"></path><path d="M380 318l1 1c2-1 5-2 7-2h1 1c1 0 1 0 2 1-1 1-1 1-2 1s-2 0-4 1c-1 1-3 2-3 4v1 1h-1-1c1-2 1-4 2-5l1-1c-1 0-2 2-3 3v3 1c-1-1-2-1-3-1v1c-1 0-1 1-2 1l-1-1s0-1-1-1l1-1v-1s0-1-1-1c1-1 1-1 1-2v-1h1l-1-1h2 1c1-1 1-1 2-1z" class="L"></path><path d="M376 320h4-2v1h0 0 2v1c-1 0-1 1-2 1 0 0-1 0-1 1h-2s0-1-1-1c1-1 1-1 1-2v-1h1z" class="M"></path><path d="M378 327v-1c1 0 2 0 3 1v1h2 0c0 1-1 2-1 2l1 1 1-1c1 2 1 5 1 7v5 7h-4l1 1c-1 0-1 0-1 1v2h-1l-1-1c-1-1-1-1-3-1h1l-1-1-1 1v-1-6-1-1-2-2-3-1-3-1h0c-1-1 0-1 0-2h1c1 0 1-1 2-1z" class="F"></path><path d="M375 335c1 0 3-1 4 0h2v2h-2 0c-2 0-3 0-4 1v-3z" class="N"></path><path d="M378 327s1 1 2 1v2h-5 0c-1-1 0-1 0-2h1c1 0 1-1 2-1z" class="I"></path><path d="M375 331h5c0 1 0 1 1 2-1 1-2 1-3 1h-3v-3z" class="M"></path><path d="M381 347h0c1 0 0 0 1-1l1-1h0v-1l2-2v7h-4v-2z" class="d"></path><path d="M375 338c1-1 2-1 4-1v1h2v3h-1v1h-2s0-1-1 0c-1 0-1 0-2 1v-1-2-2z" class="C"></path><path d="M375 343c1-1 1-1 2-1 1-1 1 0 1 0h2v-1h1v6 2l1 1c-1 0-1 0-1 1v2h-1l-1-1c-1-1-1-1-3-1h1l-1-1-1 1v-1-6-1zm1-43h3c1-1 2-1 4-1h2c1-1 2 0 3 0 2-1 3 0 5-1h1 0c1-1 2 0 4 0h8s3 0 3 1v2c-1 1-3 1-5 1h1c1 1 2 1 4 1v3c-1 1-1 1-2 1-2 1-7 0-9 0l-1 1c4 0 8-1 12 0 1 1 0 3 0 4h-4c-2 0-5 0-8 1h12c1 1 0 2 0 3-1 1-3 0-4 0l-13 1c-1 0-2 0-3-1l-9 2c-1 0-1 0-2 1h-1-2l-1-1c0-1 1-3 2-4h-1v-4l5-1 1-1c-2 0-5 1-6 1-1-1-1-2 0-3l1-1h0l-1-1c-1-1-1-1 0-2l1-1v-1z" class="I"></path><path d="M376 314h2l1-1h0 1c2-1 4 1 5 0l1-1h1 5v1c-1 1-3 0-5 0l1 1-1 1h0 0v1h0 2l-9 2c-1 0-1 0-2 1h-1-2l-1-1c0-1 1-3 2-4zm22-10l-1-1c-2 0-6 1-9 0h0l1-1h5 10 1c1 1 2 1 4 1v3c-1 1-1 1-2 1-2 1-7 0-9 0-1-1 0-2 0-3z" class="C"></path><path d="M406 306h-2v-1-1c1 1 2 1 3 2h-1 0z" class="I"></path><path d="M398 304h0c1 0 5 0 5 1 1 0 1 1 1 1h2 0l1 1c-2 1-7 0-9 0-1-1 0-2 0-3z" class="L"></path><path d="M370 292l-1-1h-1v-1l1-1 2-1c1-1 2-2 4-2v1l1 1h1l-1 1-1 1 1 1 7-1v1c-2 0-5 0-8 1 0 1 0 2-1 3l1 1h3c0-1 1-1 1 0-1 0-3 0-4 1s-1 2-1 3h2v1l-1 1c-1 1-1 1 0 2l1 1h0l-1 1c-1 1-1 2 0 3 1 0 4-1 6-1l-1 1-5 1v4h1c-1 1-2 3-2 4l1 1 1 1h-1v1c0 1 0 1-1 2 1 0 1 1 1 1v1l-1 1c1 0 1 1 1 1l1 1h-1c0 1-1 1 0 2h0v1 3 1 3 2 2 1 1 6 1l1-1 1 1h-1c-1 0-1 0-1 1v1 3h-1l-1 1c-2 0-3 0-5 1h-2c-1 0-3 0-5 1v-23-5-11c0-2 0-4 1-6v3h4v6c0 1 0 2 1 3 1-2 0-5 0-7v-1c0-2 1-4 0-6 0-2 0-2 1-4l-1-1v-3-1c1-1 1-1 2-1h0-1c-1-1 0-2-1-4v-5l3-1z" class="I"></path><path d="M366 328c1 3 0 6 0 8 0 1 0 2 1 3l1-1v6 3 5c0 2-1 4 0 6h-2v-11-19z" class="f"></path><path d="M368 347h0c1-1 1-1 2-1h1 3v4 1h0v1 4l-1 1c-2 0-3 0-5 1-1-2 0-4 0-6v-5z" class="I"></path><path d="M368 347h0c1-1 1-1 2-1h1 3v4 1h-2c-1 0-1 0-2 1h-1-1v-5z" class="C"></path><path d="M368 337v-5h4c0-1 0-1 1-1l1 3v12h-3-1c-1 0-1 0-2 1h0v-3-6-1z" class="N"></path><path d="M367 318c2-1 2-1 4-1 1-1 2-1 2-1v6c0 1 1 2 0 3 0 2 1 4 1 6v3l-1-3c-1 0-1 0-1 1h-4v5 1l-1 1c-1-1-1-2-1-3 0-2 1-5 0-8v-5c0 1 0 2 1 3 1-2 0-5 0-7v-1z" class="I"></path><path d="M373 325c-1 0-1-1-2 0h-1-2v-2h1l1-1h3c0 1 1 2 0 3z" class="k"></path><path d="M366 323c0 1 0 2 1 3 1-2 0-5 0-7l1 18v1l-1 1c-1-1-1-2-1-3 0-2 1-5 0-8v-5z" class="X"></path><path d="M370 292l-1-1h-1v-1l1-1 2-1c1-1 2-2 4-2v1l1 1h1l-1 1-1 1 1 1 7-1v1c-2 0-5 0-8 1 0 1 0 2-1 3l1 1h3c0-1 1-1 1 0-1 0-3 0-4 1s-1 2-1 3h2v1l-1 1c-1 1-1 1 0 2l1 1h0l-1 1c-1 1-1 2 0 3 1 0 4-1 6-1l-1 1-5 1v4h1c-1 1-2 3-2 4l1 1 1 1h-1v1c0 1 0 1-1 2 1 0 1 1 1 1v1l-1 1c1 0 1 1 1 1l1 1h-1c0 1-1 1 0 2h0v1 3 1 3 2 2 1 1 6 1l1-1 1 1h-1c-1 0-1 0-1 1v1 3h-1v-4-1h0v-1-4-12-3c0-2-1-4-1-6 1-1 0-2 0-3v-6s-1 0-2 1c-2 0-2 0-4 1 0-2 1-4 0-6 0-2 0-2 1-4l-1-1v-3-1c1-1 1-1 2-1h0-1c-1-1 0-2-1-4v-5l3-1z" class="W"></path><path d="M368 308c2 0 3-1 5-1 0 1 1 3 0 4h0c1 1 0 2 0 3l1 1-1 1s-1 0-2 1c-2 0-2 0-4 1 0-2 1-4 0-6 0-2 0-2 1-4zm2-16l3-1c1 1 0 3 0 4s-1 1-1 1l-1 1h2c1 1 0 2 0 4h-1l-1 1h2v2 1 1c-1 1-4 1-6 1v-3-1c1-1 1-1 2-1h0-1c-1-1 0-2-1-4v-5l3-1z" class="N"></path><path d="M440 465h1c0-1 0-2 1-3v24c1 1 2 1 3 2v12c-1 6-1 12 0 18 0 2 0 5 1 7v1h0-2l1 18h-1v1h-1v-1 1 5 6c-1-2 0-4-1-6 0 1-1 1-1 2h0v-3c-2 1-2 1-3 2v3l-1-2v-2-2h-1c-1 0-2 0-2-1-1-1-2-1-2-1h-7c-3 2-17 0-21 2l-7 2c-4 0-8 0-12-1l-5 1h0c-2 0-5 0-7-1v-1-2h0v-1-2-2c0-2-1-5 0-7h2 0l-2-1v-1c0 1 0 1-1 2h0v-4-11-10c-1-2 0-5 0-7v-2-4-1-3-1c-1 0-2 0-2-1h1c1 0 2-1 3-2 2-1 6-1 9-1h3 9v-1h-2v-1h0c4-1 10 0 14 0 5 0 10-1 14 0 3 0 6 1 8 1h3 3s0 1 1 1v-5l1-1v1l3 1s0-1 1-1v-9l-1-6v-2z" class="K"></path><path d="M437 544c0-1 0-2 1-3h0l1 1h1c1 1 1 2 1 2h-4z" class="G"></path><path d="M440 483s0-1 1-1v3l-3 1 2 1v1c-1 0-2 0-3-1v-1h1l-1-1v-1l2-1h0 1z" class="J"></path><path d="M437 548v-2h1l1-1 2 1v2 1c-2 1-2 1-3 2v3l-1-2v-2-2z" class="f"></path><path d="M424 541h7v2 1c-2 0-8 1-10-1h0l1-1 2-1zm13-43v-1c0-1-1-2 0-4 0-1-1-2 0-3s2 0 3 0v1h-1l1 1v3 3h-3z" class="F"></path><path d="M432 535h0 1v-2-1l1-1v6c0 2 1 6 0 7-1 0-1-1-2-2h0l-1 1v-2-1c-1-2-1-3 0-5h1z" class="m"></path><path d="M432 535h0 1v-2-1l1-1v6 1c-1 0-1-1-1-1h-1c-1 1 0 3 0 5l-1 1v-2-1c-1-2-1-3 0-5h1z" class="U"></path><path d="M437 498h3v9 2h-2 0l-1-1v-1-3-1-1h0v-2-1-1z" class="Q"></path><path d="M411 540l1 1c1 0 2 1 3 1l1 1h1v1h-3-2c-1 0-3 1-4 1-3 0-7-1-9 0h3c2 0 6-1 8 0s4 1 6 1h-31l1-1c-2 0-5 0-6-1h15 1 2 7 3c1 0 2 0 3-1l-1-1c1 0 1-1 1-2z" class="g"></path><path d="M411 540l1 1c1 0 2 1 3 1l1 1h1v1h-3-2-7 3c1 0 2 0 3-1l-1-1c1 0 1-1 1-2z" class="i"></path><path d="M431 518c1 0 2 0 3 1v6 4 2l-1 1v1 2h-1 0-1c-1-2-1-15-1-17h1z" class="T"></path><path d="M430 518h1c1 1 0 2 0 3v3l1 1-1 1s0 1 1 2v5c-1 1 0 1 0 2h-1c-1-2-1-15-1-17z" class="P"></path><path d="M393 485c4-1 10 0 14 0 5 0 10-1 14 0 3 0 6 1 8 1h3c1 0 1 0 2 1-6 0-13-1-19-1s-13 1-20 1v-1h-2v-1h0z" class="m"></path><path d="M393 485h20c-1 1-4 1-5 1h-13-2v-1z" class="D"></path><path d="M430 508h2c1 2 1 2 1 4-1 1-2 1-3 1h-1l-1 1c0-1-1-1-1-1-1 0-1-2-2-1h-1c-3 3-5 2-9 3h-3c2 0 7-1 9 0 2 0 6 0 7 1 2 0 5-1 6 0v1h-8-2c0 1-1 1-2 1l-1-1v1c-1 1-1 2-1 2l-2 2 1-1v-1c-1-2-1-2 0-3l-1-1h-5c-1 0-2 0-3-1 2-1 5-1 7-1 3 0 5 0 7-2 1-1 1-1 2-1l1 1v1c2 0 2-1 3-1 1-1 1-2 2-2v-1h0-1l-1-1z" class="c"></path><path d="M416 489l11 1h7c0 1 1 1 1 2h-1c-1 0-1 1-2 1h-1-5v1h2 4v1h-2 0c-1 1-2 1-3 0h-3-2-3l1-1v-1h-4l-1-1h3l3-1h0c-1-1-3-1-5-2z" class="n"></path><path d="M434 490c0 1 1 1 1 2h-1c-1 0-1 1-2 1h-1c1 0 1 0 1-1h-2c0-1 3-1 4-2z" class="b"></path><path d="M416 489l11 1c0 1 0 1 1 2-1 1-2 1-3 1-1 1-3 1-4 1v1h1-3l1-1v-1h-4l-1-1h3l3-1h0c-1-1-3-1-5-2z" class="O"></path><path d="M374 488c2-1 6-1 9-1v1c-1 1-2 0-4 1 7 0 14-1 20 0h-4v3h-2-4c-3 0-5-1-9-1 0 1 0 2-1 2v-2-1l-1 1h-4 0l-1 1h-1v-1c-1 0-2 0-2-1h1c1 0 2-1 3-2z" class="Q"></path><path d="M374 488c2-1 6-1 9-1v1c-1 1-2 0-4 1h0c-2 1-3 0-5 1 1 1 1 0 2 1l1-1 1 1h-4 0l-1 1h-1v-1c-1 0-2 0-2-1h1c1 0 2-1 3-2z" class="q"></path><path d="M397 538c1-1 2-1 3-1 1 1 2 2 3 2s1 0 1 1h3 0 4c0 1 0 2-1 2l1 1c-1 1-2 1-3 1h-3-7-2-1c-1-1 0-4 0-6v3h0c1-1 1-1 1-2s0-1 1-1z" class="e"></path><path d="M397 538c1-1 2-1 3-1 1 1 2 2 3 2s1 0 1 1c-3 0-5 0-8-1 0-1 0-1 1-1z" class="V"></path><path d="M396 544v-3-1l1 1c3 0 8-1 10 1 1 1 1 1 1 2h-3-7-2z" class="Z"></path><path d="M397 541c3 0 8-1 10 1 1 1 1 1 1 2h-3-7 5v-1l-1-2h-5z" class="F"></path><path d="M437 516h1 2c1 1 0 3 0 4 1 2 0 4 1 7v6 2c0 2 0 5-1 7h-1l-1-1h0c-1 1-1 2-1 3v-12-16z" class="S"></path><path d="M438 541l2-1c-1-1-1-2-2-4h1 1c1 1 1 2 1 3l-1 3h-1l-1-1zm3-14v6 2h-1c-2-1-2-4-3-5l1-1v-1l1 1h1v1l1-3z" class="J"></path><path d="M438 516h2c1 1 0 3 0 4 1 2 0 4 1 7l-1 3v-1h-1l-1-1c1-2 0-1 0-3l1-1c0-1-1-1-1-2l1-1-1-1c0-1 0-1 1-2-1 0-1-1-1-2h0z" class="H"></path><path d="M420 507h2c0 1 1 1 2 1h2 4l1 1h1 0v1c-1 0-1 1-2 2-1 0-1 1-3 1v-1l-1-1c-1 0-1 0-2 1-2 2-4 2-7 2v-1-1l1-1h-1-1-7-2c0-1 1-1 1-2 1 0 1 0 2-1l1 1h1l1-2h5 2z" class="g"></path><path d="M426 508h4l1 1-1 1-1 1c0-1-1-1-1-1h-1v-1l-1-1z" class="T"></path><path d="M420 507h2c0 1 1 1 2 1h2l1 1v1h-3c-2-1-5 0-7 0 1-1 1-2 1-3h2z" class="h"></path><path d="M413 507h5c0 1 0 2-1 3h-6l5 1h-7-2c0-1 1-1 1-2 1 0 1 0 2-1l1 1h1l1-2z" class="P"></path><defs><linearGradient id="V" x1="445.293" y1="510.398" x2="437.3" y2="508.86" xlink:href="#B"><stop offset="0" stop-color="#1d1c18"></stop><stop offset="1" stop-color="#36373a"></stop></linearGradient></defs><path fill="url(#V)" d="M440 490h1 1v42s-1 0-1 1v-6c-1-3 0-5-1-7 0-1 1-3 0-4h-2-1v-3-2-2h1 2v-2-9-3-3l-1-1h1v-1z"></path><path d="M437 513v-2-2h1 2c0 2 1 4 0 6h-2v-1c0-1-1-1-1-1z" class="H"></path><path d="M420 494l-1 1h3 2 3c1 1 2 1 3 0l2 1v1l-1 1 1 1-2 1c-2-1-6 0-9 0h-7v1h-1-1l-5-1h-2c3-1 6 0 9-1v-1c-1-1-2-1-2-2l-4-1h0-1 0 4c3-1 6-1 9-1z" class="P"></path><path d="M420 494l-1 1h3 2c0 1-1 1-1 2h-3-6c-1-1-1-1-2-1l-4-1h0-1 0 4c3-1 6-1 9-1z" class="O"></path><path d="M420 494l-1 1h3 2c0 1-1 1-1 2h-3v-1h-1-2-1l-1-1h-4c3-1 6-1 9-1z" class="l"></path><path d="M421 518v-1l1 1c1 0 2 0 2-1v15c0 2 0 4 1 7 0 1 0 1-1 2l-2 1-1 1h0v-1h-1c-1 1-1 2-2 2h-4 3v-1h-1v-2-2h1l2-1h1v-6-12s0-1 1-2z" class="n"></path><path d="M419 538h1v2h-3v-1l2-1z" class="Y"></path><path d="M421 518v-1l1 1c0 1 0 2 1 4-1 0-1 1-1 1v2 4c0-2 0-3-1-4v1-8z" class="e"></path><path d="M421 526v-1c1 1 1 2 1 4v10 2 1l-1 1h0v-1-16z" class="R"></path><path d="M422 518c1 0 2 0 2-1v15c0 2 0 4 1 7 0 1 0 1-1 2l-2 1v-1-2-10-4-2s0-1 1-1c-1-2-1-3-1-4z" class="P"></path><path d="M422 529v-4c1 2 0 5 1 7h0c0 1 1 1 1 1v4h-1l-1 1c1 0 2 1 3 1 0 1 0 1-1 2l-2 1v-1-2-10z" class="i"></path><path d="M407 500l5 1h1 3v1c0 1 0 2-1 2v1c1 2 3 0 5 2h-2-5l-1 2h-1l-1-1c-1 1-1 1-2 1 0 1-1 1-1 2h-1-8v-1c1-1 1-2 1-3h2 2v-2h-1l-1-1 2-2h1c1 0 2 0 3-1v-1z" class="V"></path><path d="M403 505h3v1 1h-3v-2z" class="L"></path><path d="M407 500l5 1-1 1h-7c1 0 2 0 3-1v-1z" class="B"></path><path d="M412 501h1 3v1c0 1 0 2-1 2v1c1 2 3 0 5 2h-2-5 0l1-1c-2-1-3-1-5-1v-1c1 0 1-1 2-2l1-1z" class="S"></path><path d="M412 501h1 3v1l-2 1c-1 1-4 1-5 1 1 0 1-1 2-2l1-1z" class="E"></path><defs><linearGradient id="W" x1="408.782" y1="508.906" x2="399.407" y2="507.817" xlink:href="#B"><stop offset="0" stop-color="#8f8d8f"></stop><stop offset="1" stop-color="#adabac"></stop></linearGradient></defs><path fill="url(#W)" d="M401 507h12 0l-1 2h-1l-1-1c-1 1-1 1-2 1 0 1-1 1-1 2h-1-8v-1c1-1 1-2 1-3h2z"></path><path d="M398 510c3 0 4-1 6-1h4c0 1-1 1-1 2h-1-8v-1z" class="S"></path><path d="M414 501v-1h7c3 0 7-1 9 0l1 1h1l1 1h-2v1h2v1h-2c0 1 1 1 1 2 1 0 1 0 2 1h0l-2 1h-2-4-2c-1 0-2 0-2-1h-2c-2-2-4 0-5-2v-1c1 0 1-1 1-2v-1h-3 1z" class="i"></path><path d="M414 501v-1h7l3 1h-1c-3 1-6 0-9 0z" class="j"></path><path d="M421 500c3 0 7-1 9 0l1 1h1l1 1h-2v1h0c-1 0-2-1-3 0h-1-2v-2h-2 1l-3-1z" class="O"></path><path d="M415 504c4 0 11 1 15 0h3-2c0 1 1 1 1 2 1 0 1 0 2 1h0l-2 1h-2-4-2c-1 0-2 0-2-1h-2c-2-2-4 0-5-2v-1z" class="l"></path><path d="M432 506c1 0 1 0 2 1h0l-2 1h-2-4-2c-1 0-2 0-2-1 3 0 8 0 10-1z" class="g"></path><defs><linearGradient id="X" x1="417.06" y1="492.981" x2="395.397" y2="489.988" xlink:href="#B"><stop offset="0" stop-color="#afaeaf"></stop><stop offset="1" stop-color="#dfdede"></stop></linearGradient></defs><path fill="url(#X)" d="M399 489h12c2 0 4 1 5 0 2 1 4 1 5 2h0l-3 1h-3l1 1h4v1c-3 0-6 0-9 1h-4 0 1 0-1-4-1c0-1 0-1-1-1-2-1-4-1-5 0-1 0-1 0-1 1h-1c-1 0-3 1-4-1h4l1-1h0l-2-1h2v-3h4z"></path><defs><linearGradient id="Y" x1="413.716" y1="494.955" x2="405.724" y2="490.093" xlink:href="#B"><stop offset="0" stop-color="#9e9e9f"></stop><stop offset="1" stop-color="#bcbaba"></stop></linearGradient></defs><path fill="url(#Y)" d="M403 495c1-1 2-1 3-1v-1c-1 0-2 0-3-1l-1-1c2 1 5 0 7 0s3 1 5 1h1c1-1 2 0 3 0h-3l1 1h4v1c-3 0-6 0-9 1h-4 0 1 0-1-4z"></path><path d="M373 546h12 31 9c-3 2-17 0-21 2l-7 2c-4 0-8 0-12-1l-5 1h0c-2 0-5 0-7-1v-1-2h0z" class="U"></path><path d="M373 546h8 14 1l-1 1h-16 0c0 1 1 1 2 1h8c-1 1-3 1-4 1h4c-1 1-3 0-4 0l-5 1h0c-2 0-5 0-7-1v-1-2z" class="e"></path><path d="M424 517h2l4 1h0c0 2 0 15 1 17-1 2-1 3 0 5v1h-7c1-1 1-1 1-2-1-3-1-5-1-7v-15z" class="p"></path><path d="M442 490c1-1 1-1 2-1v3c0 3 0 5 1 8-1 6-1 12 0 18 0 2 0 5 1 7v1h0-2l1 18h-1v1h-1v-1 1l-2-1s0-1-1-2c1-2 1-5 1-7v-2c0-1 1-1 1-1v-42z" class="I"></path><path d="M441 533c0-1 1-1 1-1l1 12v1l-2-1s0-1-1-2c1-2 1-5 1-7v-2z" class="X"></path><defs><linearGradient id="Z" x1="403.35" y1="516.387" x2="402.434" y2="513.565" xlink:href="#B"><stop offset="0" stop-color="#554f53"></stop><stop offset="1" stop-color="#5a5c59"></stop></linearGradient></defs><path fill="url(#Z)" d="M395 507h4c0 1 0 2-1 3v1h8 1 2 7 1 1l-1 1v1 1c-2 0-5 0-7 1 1 1 2 1 3 1h-9-1-6l1 1h-2-8-6c2-1 4-1 6-1h0l-1-2h1c1 0 1 0 2-1l-1-1c0 1-1 1-1 1h-1v-2-3h7v-1h1z"></path><path d="M416 511h1l-2 2c-1 0-2 0-3 1h-6c2-1 5-1 6-2h0l-3-1h7z" class="U"></path><path d="M406 511h1 2l3 1h0c-1 1-4 1-6 2h-2l2-1v-1-1z" class="R"></path><path d="M388 516c2-1 6 0 9 0l1 1h-2-8-6c2-1 4-1 6-1h0z" class="Q"></path><path d="M398 511h8v1 1l-2 1h-7l1-1c1-1 1-1 1-2h-1z" class="E"></path><path d="M395 507h4c0 1 0 2-1 3v1h1c0 1 0 1-1 2l-1 1h-6l-1-1 1-1c1 0 2 0 3-1h0-1c-1-1-4-1-5 0h0l1 1c0 1-1 1-1 1h-1v-2-3h7v-1h1z" class="B"></path><path d="M379 493c1 0 1-1 1-2 4 0 6 1 9 1h4l2 1h0l-1 1h-4c1 2 3 1 4 1h1c0-1 0-1 1-1 1-1 3-1 5 0 1 0 1 0 1 1h1 4 1l4 1c0 1 1 1 2 2v1c-3 1-6 0-9 1h2v1c-1 1-2 1-3 1h-1s-1 0-2 1c-1-1-1-2-1-3l-1-1h-2-2-1-6c-1-1-2 0-3-1h-7-3c0-1 0 0 1-1h0v-1c1-1 1-1 2-1s1 0 1-1v-1z" class="I"></path><path d="M396 494c1-1 3-1 5 0 1 0 1 0 1 1h-5l-1-1z" class="Z"></path><path d="M394 499c1 0 1 0 1-1l1-1h4l1 1s-1 1-1 2l-1-1h-2-2-1z" class="B"></path><path d="M378 498h7c1 1 2 0 3 1h6 1 2 2l1 1c0 1 0 2 1 3 1-1 2-1 2-1l-2 2 1 1h1v2h-2-2-4-1-11-2-2l-1 1h-5v-4c1-1 5-1 6-1h0c-1-1-1-1-2-1h-1v-1h2l1-1s-1 0-1-1v-1z" class="H"></path><path d="M394 505c0 1 0 1 1 2h-1-11-2-2v-1c0-1 6 0 7 0l1-1v1c2 0 6 0 7-1z" class="L"></path><path d="M394 505v-1h-6v-1h5c1 0 3-1 5-1l2 1h1c1-1 2-1 2-1l-2 2 1 1h1v2h-2-2-4c-1-1-1-1-1-2h0z" class="N"></path><path d="M401 503c1-1 2-1 2-1l-2 2 1 1h-6l1-1c1 0 2 0 4-1h-1 1z" class="B"></path><path d="M394 505c1 0 1-1 2 0h6 1v2h-2-2-4c-1-1-1-1-1-2h0z" class="M"></path><path d="M378 498h7c1 1 2 0 3 1 0 1 0 1 1 1s3 0 4 1h1v1c-4 1-10-1-14 1h-1 0c-1-1-1-1-2-1h-1v-1h2l1-1s-1 0-1-1v-1z" class="C"></path><path d="M378 491l1-1v1 2 1c0 1 0 1-1 1s-1 0-2 1v1h0c-1 1-1 0-1 1h3v1c0 1 1 1 1 1l-1 1h-2v1h1c1 0 1 0 2 1h0c-1 0-5 0-6 1v4h5l1-1h2 2 11v1h-7v3 2h1s1 0 1-1l1 1c-1 1-1 1-2 1h-1l1 2h0c-2 0-4 0-6 1h6c-2 1-4 0-6 1h-2v15 5c-1 2-1 4 0 6 1 1 4 1 6 1l-1 1h-12v-1-2-2c0-2-1-5 0-7h2 0l-2-1v-1c0 1 0 1-1 2h0v-4-11-10c-1-2 0-5 0-7v-2-4-1-3h1l1-1h0 4z" class="Y"></path><path d="M375 499c-1 0-1-1-2-1h0c0-1 0-2 1-3h4c-1 0-1 0-2 1v1h0c-1 1-1 0-1 1v1z" class="C"></path><path d="M378 491l1-1v1 2 1c-2 0-4 1-6 0l1-3h0 4z" class="N"></path><path d="M375 498h3v1c0 1 1 1 1 1l-1 1h-2v1h1c1 0 1 0 2 1-2-1-5 0-6-1v-2l2-1h0v-1z" class="B"></path><path d="M385 514l-12 1v-1l2-2h-2v-1l1-1h1c2 1 3 1 4 1 0 1-1 1-1 2 1 0 1 0 2-1v1h5 0 2 1s1 0 1-1l1 1c-1 1-1 1-2 1h-1-2z" class="C"></path><path d="M385 514h2l1 2h0c-2 0-4 0-6 1h-3c-1 1 0 2 0 4v1h-1 0-2-3v-4-2c2-1 3 0 5 0 0-1 1-1 2-1h5v-1z" class="D"></path><path d="M373 518l5-1v2c0 1-1 1-1 1h-1c-1 0-2-1-3-2z" class="N"></path><path d="M373 518h0c1 1 2 2 3 2h1s1 0 1-1l1 2v1h-1 0-2-3v-4z" class="L"></path><path d="M379 507h2 2 11v1h-7v3 2h-2 0-5v-1c-1 1-1 1-2 1 0-1 1-1 1-2-1 0-2 0-4-1h3c-1 0-3 0-5-1v-1h5l1-1z" class="D"></path><path d="M379 507h2v1h3 1v1h1c-1 1-2 0-3 1h-5c-1 0-3 0-5-1v-1h5l1-1z" class="B"></path><path d="M379 521c0-2-1-3 0-4h3 6c-2 1-4 0-6 1h-2v15 5c-1 2-1 4 0 6 1 1 4 1 6 1l-1 1h-12v-1-2-2c0-2-1-5 0-7h2 0l-2-1v-1-1h2l-1-1v1c-1-1-1-3-1-4v-1-4h3 2 0 1v-1z" class="j"></path><path d="M375 534h3c1 1 1 1 0 2l-1 1c-1-1-2-1-3-1l-1-2h2z" class="C"></path><path d="M373 522h3c1 1 3 2 4 3-2 0-5 0-7 1v-4z" class="I"></path><path d="M373 527c2-1 4-1 6-1v4h-3 0l3 1c0 1 0 1-1 2-1 0-2 1-3 1l-2-1v-1-1h2l-1-1v1c-1-1-1-3-1-4zm0 14c0-2-1-5 0-7l1 2c1 0 2 0 3 1h1l1 1s-1 1-1 2l1 1-1 1v1s0 1-1 1c-1 1-3 0-4 1v-2-2z" class="C"></path><path d="M373 541c0-2-1-5 0-7l1 2c1 0 2 0 3 1h1c-1 0-2 0-3 1v2s-1 0-2 1z" class="B"></path><path d="M397 516h6 1 9 5l1 1c-1 1-1 1 0 3v1l-1 1 2-2v12 6h-1l-2 1h-1v2 2l-1-1c-1 0-2-1-3-1l-1-1h-4 0-3c0-1 0-1-1-1s-2-1-3-2c-1 0-2 0-3 1-1 0-1 0-1 1s0 1-1 2h0v-3c0 2-1 5 0 6h-15c-1-2-1-4 0-6v-5-15h2c2-1 4 0 6-1h8 2l-1-1z" class="P"></path><path d="M391 518c1 0 1 0 2 1l1-1h1v5l-1-2h-1c0-1 0-1-1-1-1-1-1-1-1-2z" class="E"></path><path d="M414 533c-1-1-2-1-4-1h0l9-1-1 1h-1v1h-3z" class="g"></path><path d="M411 531h-6-4c-2 0-4 1-5 0v-2h6v1l1-1h3l5 1v1z" class="L"></path><path d="M413 520h6v1l-1 1h0-2v1c1 0 2 0 3 1 0 1 0 1-1 1-2 1-3 1-5 1v1c-1 0-3 0-4-1 2 0 3-1 5-1h0v-2h-2l-1-1c-1-1-2-1-3-1v-1h5z" class="T"></path><path d="M418 522l2-2v12 6h-1v-3h-1-3c-1 0 0 0-1-1h4v-1h-1v-1h1l1-1h0l-1-1c-1 0-1-1-2-1-1-1-2-1-3-2v-1c2 0 3 0 5-1 1 0 1 0 1-1-1-1-2-1-3-1v-1h2 0z" class="X"></path><path d="M401 525h6-2c1 1 2 0 3 2v1h1c1 0 1 1 2 1 1 1 3 1 4 2h-4 0v-1l-5-1h-3l-1 1v-1c-2-2-4 0-6-2v-1l1-1h0 4z" class="G"></path><path d="M396 526c2 0 6 0 7 1 0 1 0 1-1 2h1l-1 1v-1c-2-2-4 0-6-2v-1z" class="Z"></path><path d="M396 520h10 2v1c1 0 2 0 3 1 0 2 0 2-1 3h-2-1-6-4l-1-1v-2-2z" class="V"></path><path d="M396 522l3-1 2 1s1 1 1 2l-1 1h-4l-1-1v-2z" class="L"></path><path d="M396 520h10 2v1c1 0 2 0 3 1 0 2 0 2-1 3h-2v-1-1c-1-1-2-1-3-2h-6l-3 1v-2z" class="o"></path><path d="M391 519v-1c0 1 0 1 1 2 1 0 1 0 1 1h1l1 2v1 8c0 1 0 3-1 4l-2 1v-1l-2-1v-4c0-4 0-8 1-12z" class="S"></path><path d="M393 521h1l1 2v1h-1-3v-1h1v-2h1z" class="H"></path><path d="M394 524h1v8c0 1 0 3-1 4l-2 1v-1-2-3-2h1v-1h-1v-2l2-1v-1z" class="V"></path><path d="M397 516h6 1 9 5l1 1c-1 1-1 1 0 3h-6-5-2-10v-2-1h2l-1-1z" class="F"></path><path d="M397 516h6l-1 1c-1 0-3 1-4 0l-1-1z" class="E"></path><path d="M396 518c4 0 7 1 10 1v1h-10v-2z" class="C"></path><path d="M404 516h9 5l1 1c-1 1-1 1 0 3h-6c0-2-2-1-3-2 0-1-1-1-1-1l-5-1z" class="U"></path><path d="M396 534v-1c3-1 6-1 8-1 4 0 7 1 10 1h3 1v1h-4c1 1 0 1 1 1h3 1v3l-2 1h-1v2 2l-1-1c-1 0-2-1-3-1l-1-1h-4 0-3c0-1 0-1-1-1s-2-1-3-2c-1 0-2 0-3 1v-1s-1 0-1-1v-2z" class="R"></path><path d="M396 534v-1c3-1 6-1 8-1 2 1 3 1 4 1l1 1h-9c2 1 5 1 7 1v1c-3 0-8 1-11 0v-2z" class="L"></path><path d="M414 533h3 1v1h-4c1 1 0 1 1 1h3 1v3l-2 1h-1v2 2l-1-1c-1 0-2-1-3-1l-1-1h-4 0c1 0 1 0 1-1v-1l-1-1-1-1c2 0 5 0 7-1-2 0-3 0-4-1h5v-1z" class="P"></path><path d="M415 535h3 1v3l-2 1h-1v2 2l-1-1c-1 0-2-1-3-1 0-1 1-2 1-3v-1h-1 0c2-1 4 0 5-1h0l-2-1z" class="O"></path><path d="M418 535h1v3l-2 1h-1v2 2l-1-1v-1c-1-1 0-2 1-4 1 0 1 0 2-1v-1z" class="g"></path><path d="M382 518s1 0 2 1h6 1c-1 4-1 8-1 12v4l2 1v1l2-1c0 1 0 1 1 1v1c0 2-1 5 0 6h-15c-1-2-1-4 0-6v-5-15h2z" class="p"></path><path d="M386 542c1-1 1-1 1-2v-1-8h2v1l1-1v4c0 2 0 5 1 7h-5z" class="K"></path><path d="M382 518s1 0 2 1l-2 1h0 1v2c-1 5 1 10 0 16-1 1 0 1 0 3h-1l-1-1v1c2 1 4 1 5 1h5c-1-2-1-5-1-7l2 1v1l2-1c0 1 0 1 1 1v1c0 2-1 5 0 6h-15c-1-2-1-4 0-6v-5-15h2z" class="B"></path><path d="M390 535l2 1v1l2-1c0 1 0 1 1 1h-2v1 4h-2c-1-2-1-5-1-7z" class="F"></path><path d="M224 449h3v3h0 2 1 1l2-2c4 2 9 3 13 5l9 6 1 1c1 1 2 1 2 2h1c1 1 2 1 3 2l2 2 2 3h-1c0 2 0 3-2 4l-1 2h-1c0 1-1 2-1 3v1 1l-1 2h0v2 2 4 2c1 1 0 1 0 2s1 1 0 2v2c1 0 2 0 3 1v1 8 3h-1v8c0 2 0 5 1 7v1c-1 1-2 0-3 1h-7-1l-6 1h-5-3l1-1 1-2c0-4-1-6-3-9-1-3-3-5-4-7l-3-1c-7-1-13 0-21 1l-1 2v1h1 1c-1 1-2 1-2 1-1 1-1 1-2 1v2l-1 1h0l-2 2-1 9h0l-4 1h-7-1l-13 1h0c-1-2-1-3-1-5v-5-3c2-4 0-9 2-13 0-1 1-2 1-3l1-1v-2-1c2-1 3-2 4-3l-6 3 1-1v-1-1l1-1c2-1 2-1 3-2v-1h0c-1-1-3-1-4-1l-1-1h5c0-1 0-1 1-2 0 0 1 0 1-1h-1c1-1 0-1 1-1v-1h1c-1-2-1-3-2-5h0c-1-2-2-4-3-5v-1c-1-1-1-3-1-4h1l1-1h1v-2c1-1 1-2 3-2h0v2l1 1v1c1 2 2 5 3 8 1-3 3-6 4-10l1 1c1-3 3-6 4-9l1 1v-1c2-4 7-8 11-10l2 2c1 1 3 1 4 2 1-1 1-2 3-2 0-1 0-1 1-1 0 0 0-1 1-1 0-1 2-1 3-1z" class="r"></path><path d="M196 495c1 0 1 0 1 1 1 1 1 3 0 4h0 0c-1-1-2-1-3-2 0-1 1-1 1-2l1-1z" class="O"></path><path d="M196 495c1 0 1 0 1 1v1h-2v-1l1-1z" class="P"></path><path d="M207 512h1l-1 2v1h1 1c-1 1-2 1-2 1-1 1-1 1-2 1v2l-1 1h0-2 0v-2h1v-1c-1 0-1 0-1-1v-2h0l1 1 1-1v-1l3-1z" class="V"></path><path d="M198 460l1 1h0c0 1-1 2-2 3 0 2-1 2-1 4 1-1 1-1 2-1h0v1c1 1 1 2 1 4h-1l1 1v1c0 1-1 1-1 1h0 0l2 2-1 1c-2 0-3 0-5-1 1-1 2-1 3-2-2 0-3-1-4-1l1-5c1-3 3-6 4-9z" class="S"></path><path d="M194 498c1 1 2 1 3 2h0l-1 2h0c1 1 0 2 0 3v5 20h0-1v-10h-2v-12c1-2 1-4 1-5h-1v-1c0-1 1-2 1-3v-1z" class="P"></path><path d="M194 503l1-1c1 2 0 14 0 18h-2v-12c1-2 1-4 1-5z" class="K"></path><path d="M211 462l1-1h0v1c-1 1-1 2-1 3h0 1l1-3h1c0 1 1 1 0 1-3 4-5 9-6 14 0 1 0 1 2 1 0-1 1-1 1-2 0 1 1 2 0 3l2-1-1 2-2 1c0 1-1 1-2 2v1l-1-1v1c-1 1-2 2-3 4h0v1l-1-1c-1 1-1 3-1 4v-7c0-2 0-5 1-6v-3h0l2-9h1v-1c0-2 3-3 5-4z" class="T"></path><path d="M203 488c1-3 1-5 3-7 0 0 0 1 1 2v1c-1 1-2 2-3 4h0v1l-1-1z" class="G"></path><path d="M205 467h1v-1c0-2 3-3 5-4 0 1-1 2-2 3v2h1v2c-1 0-2 1-2 2s0 2-1 3v1l-1 1h0l1-3c1-2 1-3 2-5-1-1-1 0-2 0s-1 0-2-1z" class="W"></path><path d="M208 477c0 1 0 1 2 1 0-1 1-1 1-2 0 1 1 2 0 3l2-1-1 2-2 1c0 1-1 1-2 2v1l-1-1c-1-1-1-2-1-2h1l-1-1c0-1 1-3 2-3z" class="E"></path><path d="M208 477c0 1 0 1 2 1 0-1 1-1 1-2 0 1 1 2 0 3-1 0-3 1-4 2l-1-1c0-1 1-3 2-3z" class="W"></path><path d="M205 467c1 1 1 1 2 1s1-1 2 0c-1 2-1 3-2 5l-1 3h0l1-1v1h-4 0l2-9z" class="a"></path><path d="M188 492c1-3 2-7 3-10 1-2 1-3 2-5h1 0c2 1 3 1 5 1h-1c1 1 0 1 1 1l1 1v1l-1 1v1l-1 1h0-2v1c2 0 2 0 3 1v2c0 2-1 4-1 5h-3c0 1 0 1 1 2l-1 1c0 1-1 1-1 2v1c0 1-1 2-1 3v1h1c0 1 0 3-1 5v-3l-1-1h0c-1 0-2 0-2-1h-1v1 1l-3 3c-1-1-2-1-3-2h0c0-1 1-3 1-4v-1c1 1 1 1 1 2 2-3 2-7 3-11z" class="W"></path><path d="M198 484c-1 0-1 0-2-1v-1h0 3v1l-1 1zm-5-3c1 1 1 1 1 2s0 2-1 3h-1l1-5z" class="l"></path><path d="M191 497h1c1 0 1 1 1 2s-1 2-2 2c0 0-1 0-1-1s1-2 1-3h0zm2-18h1c1 1 3 0 5 0l1 1v1h-2-2-2l-1-2z" class="m"></path><path d="M184 501c1 1 1 1 1 2v3h1c1 0 1-1 3-1l-3 3c-1-1-2-1-3-2h0c0-1 1-3 1-4v-1z" class="i"></path><path d="M192 487h1v1c-1 1-1 1-1 3h3c0 2 0 3-1 4h0c-1 0-1 1-2 2h-1c1-1 1-2 1-2 1-1 0-2 0-3h-1v1h0c0 1-1 1-1 2v1c0 1 0 1-1 2v-1-2c1 0 1-1 1-2h0v-3h0c1-1 1-2 2-3z" class="P"></path><path d="M188 492c1-3 2-7 3-10 1-2 1-3 2-5h1 0c2 1 3 1 5 1h-1c1 1 0 1 1 1-2 0-4 1-5 0h-1v2l-1 5v1c-1 1-1 2-2 3h0v3h0c0 1 0 2-1 2h-1v-3z" class="Y"></path><path d="M223 464c5 4 9 9 14 15 3 3 6 7 8 12l-2-2h-1c0-1-1-2-2-3s-2-1-2-3c-2-2-3-4-5-6v4 1h0c-1-1-2-1-3-2-1 1-2 1-3 3h-1v1c-1-2-1-2-3-2 0-1-1-1-1-2l-4 2v-1h-1-1c-1 0-1 0-1-1h-3l1-2-2 1c1-1 0-2 0-3h1c0-1 2-2 3-3 1-2 2-3 3-4 0-1 1-2 1-3v-1h1l1 1v2c0 1 1 1 1 2v3c0-2 1-3 0-4l1-1c1 0 1 0 2 1v1c1 0 1 1 1 1l1 1 2 3h0c0 1 1 1 1 2h2 1c-1-2-4-5-6-6v-2l-3-3-1-2h0z" class="j"></path><path d="M229 475h-6v-7c1 1 2 3 4 4l2 3z" class="F"></path><path d="M218 469c0-1 1-2 1-3v-1h1c1 2 0 2 0 4h0c1 2 1 3 2 5-1 1-3 0-4 0h0c-2 1-4 2-6 2h0c0-1 2-2 3-3 1-2 2-3 3-4z" class="B"></path><path d="M218 469c1 1 2 1 2 3h0-1v-1h-1c-1 1-1 1 0 3-2 1-4 2-6 2h0c0-1 2-2 3-3 1-2 2-3 3-4z" class="E"></path><path d="M217 477c4-1 8-1 13 0l3 4v1h0c-1-1-2-1-3-2-1 1-2 1-3 3h-1v1c-1-2-1-2-3-2 0-1-1-1-1-2l-4 2v-1h-1-1c-1 0-1 0-1-1h-3l1-2 4-1z" class="M"></path><path d="M213 478l4-1c-1 1-1 2-2 3h-3l1-2z" class="D"></path><path d="M185 466h0v2l1 1v1c1 2 2 5 3 8 1-3 3-6 4-10l1 1-1 5c1 0 2 1 4 1-1 1-2 1-3 2h0-1c-1 2-1 3-2 5-1 3-2 7-3 10-1 4-1 8-3 11 0-1 0-1-1-2v1c0 1-1 3-1 4-1 0-1 0-1 1l-1 1 1-4h-1c-1 0-2-1-3 0l1-1v-2-1c2-1 3-2 4-3l-6 3 1-1v-1-1l1-1c2-1 2-1 3-2v-1h0c-1-1-3-1-4-1l-1-1h5c0-1 0-1 1-2 0 0 1 0 1-1h-1c1-1 0-1 1-1v-1h1c-1-2-1-3-2-5h0c-1-2-2-4-3-5v-1c-1-1-1-3-1-4h1l1-1h1v-2c1-1 1-2 3-2z" class="K"></path><path d="M185 466v2l1 1v1 2c1 2 2 5 2 7 0 1 0 2-1 2h0c-1 0-1-1-1-1v-1-1-1c0-1-1-2-1-4h0c-1-2-1-5 0-7z" class="M"></path><path d="M184 501c1-3 0-7 2-10h0c0-1 1-2 1-3 1-3 3-6 4-9 0-2 1-4 2-4v2c-1 2-1 3-2 5-1 3-2 7-3 10-1 4-1 8-3 11 0-1 0-1-1-2z" class="F"></path><path d="M185 466h0c-1 2-1 5 0 7h0c0 2 1 3 1 4v1 1 1l-1-1-2 2h0c-1-2-2-4-3-5v-1c-1-1-1-3-1-4h1l1-1h1v-2c1-1 1-2 3-2z" class="C"></path><path d="M180 476v-1c-1-1-1-3-1-4h1l4 6 1 2-2 2h0c-1-2-2-4-3-5z" class="W"></path><path d="M189 505v-1-1h1c0 1 1 1 2 1h0l1 1v3 12h2v10h1v1l1 1h-7-1l-13 1h0c-1-2-1-3-1-5v-5-3c2-4 0-9 2-13 0-1 1-2 1-3 1-1 2 0 3 0h1l-1 4 1-1c0-1 0-1 1-1h0c1 1 2 1 3 2l3-3z" class="K"></path><path d="M183 506h0v3 1c1 1 0 4 0 6v15h0-1c-1-6 0-12 0-18-2 3-1 12-1 16v2h-1-1l1-7-1-2v-1c-1-2 0-6 0-9h1c0-2 0-3 1-4l1-1c0-1 0-1 1-1z" class="n"></path><path d="M179 512h1v12h0l-1-2v-1c-1-2 0-6 0-9z" class="W"></path><path d="M178 504c1-1 2 0 3 0h1l-1 4c-1 1-1 2-1 4h-1c0 3-1 7 0 9v6c0 1 0 2-1 4h0l-1-1c0 1 1 2 0 2 0 0-1 0-1 1-1-2-1-3-1-5v-5-3c2-4 0-9 2-13 0-1 1-2 1-3z" class="a"></path><path d="M175 520h2 0v10c0 1 1 2 0 2 0 0-1 0-1 1-1-2-1-3-1-5v-5-3z" class="q"></path><path d="M178 504c1-1 2 0 3 0h1l-1 4c-1 1-1 2-1 4h-1c0-2 1-4 1-6l-1-1c-1 1-1 3-1 4l-1 11h0-2c2-4 0-9 2-13 0-1 1-2 1-3zm11 1v-1-1h1c0 1 1 1 2 1h0l1 1v3 12h2v10h1v1l1 1h-7c-2 0-2 0-4-1v-1-9-1c-1-1 0-4 0-6-1-1-1-4-1-6h1l3-3z" class="p"></path><path d="M192 504l1 1v3 12c0 3 1 7 0 10h-1c-1-2 0-5 0-7v-19z" class="T"></path><path d="M212 480h3c0 1 0 1 1 1h1 1v1l4-2c0 1 1 1 1 2 2 0 2 0 3 2-1 2 0 5 0 8v10h4 1 2v1c0 1-1 1-2 1v1h2l3 2v2h-3 0c-2 0-3 0-4 1h-1 1v1c-7-1-13 0-21 1h-1v-1l-2-1c-1 1-3 1-4 2v-6c0-4 0-8 1-13v-1c0-1 0-3 1-4l1 1v-1h0c1-2 2-3 3-4v-1l1 1v-1c1-1 2-1 2-2l2-1z" class="P"></path><path d="M204 500h1v2c-1 0-2-1-2-2h1z" class="i"></path><path d="M202 492c0-1 0-3 1-4l1 1v-1 1c0 1 0 3 1 4l-1 1c1 2 1 2 1 4v1 1h-1c-1-1-1-1-1-2h-1v-5-1z" class="O"></path><path d="M202 492c0-1 0-3 1-4l1 1v-1 1l-1 9h-1v-5-1z" class="S"></path><path d="M224 501c1 0 1 0 2 1h4 1 2v1c0 1-1 1-2 1h-14l-9 1c-1 0-3 1-4 0h0l1-1c1 0 2 0 3-1v1h4l-2-1c1 0 1 0 2-1h1c1 0 2 1 3 0l1-1 1 1h2 2l2-1z" class="L"></path><defs><linearGradient id="a" x1="204.104" y1="487.531" x2="214.748" y2="498.371" xlink:href="#B"><stop offset="0" stop-color="#bdb7bb"></stop><stop offset="1" stop-color="#dcdfdc"></stop></linearGradient></defs><path fill="url(#a)" d="M208 484v-1c1-1 2-1 2-2 1 1 2 1 3 1-1 0-2 0-2 1-1 0-1 1-1 2-1 4-1 8-1 12 0 2 0 4 1 6l2 1h-4v-1c-1 1-2 1-3 1h-1l1-2v-2-1-1c0-2 0-2-1-4l1-1c-1-1-1-3-1-4v-1h0c1-2 2-3 3-4v-1l1 1z"></path><path d="M207 483l1 1c-1 4 0 9-1 14v-7h-1l-1 2c-1-1-1-3-1-4v-1h0c1-2 2-3 3-4v-1z" class="f"></path><path d="M204 488c1-2 2-3 3-4v4c-1 0-1 1-2 0h-1zm1 5l1-2h1v7l1 5c-1 1-2 1-3 1h-1l1-2v-2-1-1c0-2 0-2-1-4l1-1z" class="b"></path><path d="M211 510h-5v-1l1-1h-2c-1 0-1 0-1 1v-1c0-1 0-1 1-1h0c3-1 6-2 9-2 6-1 11-1 17 0h2l3 2v2h-3 0c-2 0-3 0-4 1h-1 1v1c-7-1-13 0-21 1h-1v-1l-2-1h6z" class="C"></path><path d="M211 510c1-1 3-1 4-1h18 0c-2 0-3 0-4 1h-1 1v1c-7-1-13 0-21 1h-1v-1l-2-1h6z" class="W"></path><path d="M212 480h3c0 1 0 1 1 1h1 1v1l4-2c0 1 1 1 1 2 2 0 2 0 3 2-1 2 0 5 0 8v10c-1-1-1-1-2-1l-2 1h-2-2l-1-1-1 1c-1 1-2 0-3 0h-1c-1 1-1 1-2 1-1-2-1-4-1-6 0-4 0-8 1-12 0-1 0-2 1-2 0-1 1-1 2-1-1 0-2 0-3-1l2-1z" class="d"></path><path d="M215 490h1c0 3-1 7 0 9v3h-1v-12z" class="X"></path><path d="M210 485c0-1 0-2 1-2 0-1 1-1 2-1v1 10l-1-2h0v-2-1c0 1 0 1-1 2v-2h-1v-3z" class="f"></path><path d="M211 490c1-1 1-1 1-2v1 2h0l1 2v9h-1-2c1-2 1-4 1-6s-1-4 0-6z" class="b"></path><path d="M210 503c-1-2-1-4-1-6 0-4 0-8 1-12v3h1v2c-1 2 0 4 0 6s0 4-1 6h2c-1 1-1 1-2 1z" class="U"></path><path d="M212 480h3c0 1 0 1 1 1h1c0 2 0 4-1 6v12c-1-2 0-6 0-9h-1v-4l-1-1-1-2v-1c-1 0-2 0-3-1l2-1z" class="j"></path><path d="M212 480h3c0 1 0 1 1 1l-1 5-1-1-1-2v-1c-1 0-2 0-3-1l2-1z" class="H"></path><path d="M217 481h1v1 10 10l-1-1-1 1v-3-12c1-2 1-4 1-6z" class="V"></path><path d="M218 482l4-2c0 1 1 1 1 2 2 0 2 0 3 2-1 2 0 5 0 8v10c-1-1-1-1-2-1l-2 1h-2-2v-10-10z" class="b"></path><path d="M220 502v-18h1 0c0 4 0 6 1 10 0 2 0 3 1 5v1c-1 0-1 1-1 2h-2z" class="F"></path><path d="M223 482c2 0 2 0 3 2-1 2 0 5 0 8v10c-1-1-1-1-2-1v-5h-2v-3l2 1v-1-2c-1-2 0-3 0-4 0-2 0-4-1-5z" class="Z"></path><path d="M233 481v-4c2 2 3 4 5 6 0 2 1 2 2 3s2 2 2 3h1l2 2c5 6 8 16 14 21 1 0 2 0 2 1v8c0 2 0 5 1 7v1c-1 1-2 0-3 1h-7-1l-6 1h-5-3l1-1 1-2c0-4-1-6-3-9-1-3-3-5-4-7l-3-1v-1h-1 1c1-1 2-1 4-1h0 3v-2l-3-2h-2v-1c1 0 2 0 2-1v-1h-2-1-4v-10c0-3-1-6 0-8v-1h1c1-2 2-2 3-3 1 1 2 1 3 2h0v-1z" class="p"></path><path d="M239 506l1-1 2 2 1 1-1 1h-9 0 3 4v-1c0-1 0-1-1-2z" class="d"></path><path d="M233 505c2 0 4 0 6 1 1 1 1 1 1 2v1h-4v-2l-3-2z" class="D"></path><path d="M240 512h2l3 3v2l-3 2c0-1-1-2-1-3v-1h0v-1c-1-1-1-1-1-2z" class="X"></path><path d="M259 527v1h1c1-1 1-4 1-5-1-1-1-1-1 0h0l-1-1c1-1 1-1 2-1 0 2 0 5 1 7v1c-1 1-2 0-3 1h-7-1l1-1s1 0 2-1h2 2l1-1z" class="c"></path><path d="M245 517v9 3h-1l-1-1h-1c1-3 2-4 1-6 0-1 0-3-1-3l3-2z" class="a"></path><path d="M233 482h0c1 1 1 2 1 3l2 2h-1c0 1 0 2 1 3h0v1h1c0-1-1-1-1-2 1 0 1 0 1 1 0 0 0 1 1 1 0 1-1 1 0 2 0 2 0 10 1 10 1 1 2 1 2 1-1 1-5 0-6 0-1-4-1-8-1-12v-6-1c-1-1-1-2-1-3z" class="B"></path><path d="M252 529v-16h0l1 2c1 1 3 3 3 4s1 3 1 4c1 1 1 2 1 3l1 1-1 1h-2-2c-1 1-2 1-2 1z" class="G"></path><path d="M256 528c-1-1-1-1-1-2h3l1 1-1 1h-2z" class="Z"></path><path d="M253 515c1 1 3 3 3 4l-2 2h-1v-6z" class="P"></path><path d="M253 521h1l2-2c0 1 1 3 1 4l-1-1-1 2-1 1h-1v-4z" class="S"></path><path d="M232 512c2-1 4-1 6 0h2c0 1 0 1 1 2v1h0v1c0 1 1 2 1 3 1 0 1 2 1 3 1 2 0 3-1 6h1l1 1h1v1h0 0v1h-5-3l1-1 1-2c0-4-1-6-3-9-1-3-3-5-4-7h0z" class="b"></path><path d="M237 517c1 0 2 0 2 1l1 1-1 1h1v2l1 2c1 0 1-1 2-2 1 2 0 3-1 6l-1 1h-1c-1-1 0-3-1-4v-1c0-1-1-3-1-4l-1-3z" class="l"></path><path d="M232 512c2-1 4-1 6 0h2c0 1 0 1 1 2v1h0v1c0 1 1 2 1 3 1 0 1 2 1 3-1 1-1 2-2 2l-1-2v-2h-1l1-1-1-1c0-1-1-1-2-1h0c-1-1-1-1-1-2l-2-1c-1-1-1-2-2-2z" class="T"></path><path d="M226 484v-1h1c1-2 2-2 3-3 1 1 2 1 3 2 0 1 0 2 1 3v1 6c0 4 0 8 1 12 1 0 5 1 6 0l1 1h-2l-1 1c-2-1-4-1-6-1h-2v-1c1 0 2 0 2-1v-1h-2-1-4v-10c0-3-1-6 0-8z" class="b"></path><path d="M231 490c0 1 1 1 1 1v2c0 2-1 6 0 7 0 1-1 1-1 2h-1v-3h0c0-1 0-1 1-2v-1c-1 0-1-1-1-2 1-1 1-1 1-2h-1l1-2z" class="K"></path><path d="M230 492h-1l-1-1v-2-3c0-1 0-2 1-2s1 0 2 1c0 1 1 2 0 2 0 1-1 1-1 2 0 0 1 0 1 1l-1 2z" class="S"></path><path d="M224 449h3v3h0 2 1 1l2-2c4 2 9 3 13 5l9 6 1 1c1 1 2 1 2 2h1c1 1 2 1 3 2l2 2 2 3h-1c0 2 0 3-2 4l-1 2h-1c0 1-1 2-1 3v1 1l-1 2h0v2 2 4 2c1 1 0 1 0 2s1 1 0 2v2c1 0 2 0 3 1v1 8 3h-1c0-1-1-1-2-1-6-5-9-15-14-21-2-5-5-9-8-12-5-6-9-11-14-15-4-4-9-8-14-10-3 2-6 6-9 9h-1c3-4 7-9 11-11 6 2 11 6 16 11v-1l-2-1-1-1-1-2-6-4c1-1 1-2 3-2 0-1 0-1 1-1 0 0 0-1 1-1 0-1 2-1 3-1z" class="C"></path><path d="M241 464h2l1 1c-2 1-3 1-5 1v-2h2z" class="I"></path><path d="M235 467c2 0 3 2 5 2l1 1h-2c-2 0-3 0-5-2l1-1z" class="k"></path><path d="M255 503c2-1 2-2 4-2l1 1v1c0 1 0 1-1 2h1c0 1 0 2-1 3l-4-5z" class="X"></path><path d="M223 460c0-1 1-1 1-1h3c1-1 0-1 2-1 0 1 0 1 1 2 1 0 1 1 1 2h-2c-1-1-2-1-2-2l-3 1-1-1z" class="I"></path><path d="M239 476l-2-2v-1h1v1c0 1 1 1 2 1s2 1 3 1h1v3l-1 1h0c-1-1-3-4-4-4z" class="B"></path><path d="M260 502h2v8 3h-1c0-1-1-1-2-1 1-1 1-3 0-4 1-1 1-2 1-3h-1c1-1 1-1 1-2v-1z" class="Y"></path><path d="M255 473v1c-1 0-3-1-4-2h0v-1h0l2 1h5c1 0 0 0 2-1v1h4l1-1c0 2 0 3-2 4l-1 2h-1-2v-1h-1c-2 0-2-2-3-3z" class="B"></path><path d="M255 473c2 1 2 0 4 2v1h-1c-2 0-2-2-3-3z" class="I"></path><path d="M262 477c-1-1-2-2-2-4h2l1 2-1 2z" class="M"></path><path d="M239 476c1 0 3 3 4 4s1 2 3 3h0v1l1 2 4 5h0c0-1 0-1 1-2h0c2 0 5-1 7-1v4 2c1 1 0 1 0 2s1 1 0 2v2c1 0 2 0 3 1v1h-2l-1-1c-2 0-2 1-4 2-5-10-10-19-16-27z" class="K"></path><path d="M253 495l6 1c0 1 1 1 0 2v2h0c-1 0-3 1-4 0 0-2-1-3-2-5z" class="e"></path><path d="M256 497h3v1c-1 0-2 1-3 0v-1z" class="F"></path><path d="M252 489c2 0 5-1 7-1v4 2c1 1 0 1 0 2l-6-1c0-1-1-2-1-3l-1-1h0c0-1 0-1 1-2h0z" class="J"></path><path d="M252 492c1 0 1 0 2 1h1 2c1-1 1-1 2-1v2c1 1 0 1 0 2l-6-1c0-1-1-2-1-3z" class="F"></path><path d="M244 476v-1l-2-2 1-1c1 1 2 1 2 2v5c2 0 4-1 6 0 0 0 1 0 1-1 1 1 1 3 2 3 2 0 4 0 6-1v1 1l-1 2h0v2 2c-2 0-5 1-7 1h0c-1 1-1 1-1 2h0l-4-5-1-2v-1h0c-2-1-2-2-3-3h0l1-1v-3z" class="D"></path><path d="M248 482v-1-1h3c1 0 1 1 1 1h-1c-1 1-2 1-3 1z" class="I"></path><path d="M248 482c1 0 2 0 3-1v1c1 1 2 1 4 0h1 0 4l-1 2h0c-1 0-1 1-2 1h-2-2-1c-2 1-3 0-5 1l-1-2v-1h2v-1z" class="B"></path><path d="M247 486c2-1 3 0 5-1h1 2 2c1 0 1-1 2-1v2 2c-2 0-5 1-7 1h0c-1 1-1 1-1 2h0l-4-5z" class="Q"></path><path d="M259 486v2c-2 0-5 1-7 1 1-1 1-2 1-3h3 3z" class="D"></path><path d="M224 449h3v3h0 2 1 1l2-2c4 2 9 3 13 5l9 6 1 1c1 1 2 1 2 2h1c1 1 2 1 3 2l-3 1v1c1 0 1 1 2 1s1 1 2 1c-1 1-5-1-6-1s-2 1-3 1h0l1-2c-1-1-5-1-6-1l-1-1c-1 0-2-1-3-2v1 1h0-1v-1l-1-1h-2l-3-1v-1h3v-1h-1c-1 0-2 0-3-1-1 0-2-1-3-1v1c-1 0-2-1-3-1l-1 1c-1-1-1-1-1-2-2 0-1 0-2 1h-3s-1 0-1 1l-1-2-6-4c1-1 1-2 3-2 0-1 0-1 1-1 0 0 0-1 1-1 0-1 2-1 3-1z" class="Z"></path><path d="M244 462v-1h3c0 1 0 1-1 2 0 0-1 1-1 0l-1-1z" class="C"></path><path d="M227 457h2l2-2c1 1 1 1 1 2-1 0-1 0-2 1 0 1 0 1 1 1l-1 1c-1-1-1-1-1-2-2 0-1 0-2 1h-3s-1 0-1 1l-1-2 2-1c1 1 2 1 3 0h0z" class="B"></path><path d="M224 449h3v3l-1-2h0l-1 1c0 1-1 1-2 1 1 1 0 1 1 1 1 1 1 1 1 2h0c-1 0-2-1-3-2h0-2l-1-1c0-1 0-1 1-1 0 0 0-1 1-1 0-1 2-1 3-1z" class="E"></path><path d="M216 454c1-1 1-2 3-2l1 1h2 0c1 1 2 2 3 2l2 2h0c-1 1-2 1-3 0l-2 1-6-4z" class="I"></path><path d="M256 462c1 1 2 1 2 2h1c0 1 0 2-1 3h-2-1s-1 0-1-1h-2c-1 0-1 0-2-1h-2v-1c1 0 1-1 2-1v-1c2 1 4 1 6 1v-1z" class="B"></path><path d="M258 464h1c0 1 0 2-1 3h-2v-3h2z" class="C"></path><path d="M233 450c4 2 9 3 13 5l9 6 1 1v1c-2 0-4 0-6-1l-2-1-1-1h0v1h-3v1h-1c0-1-1-1-2-2h0v-2c-2 0-1 1-2 2-2-1-5-2-6-3l1-1-1-1-1-2-4 1-1-1v-1h2 1 1l2-2z" class="B"></path><path d="M241 458v-1c1 0 1 0 2 1v1c0 1-1 1-2 1h0v-2zm7 3l1-1c3 0 3 2 6 1l1 1v1c-2 0-4 0-6-1l-2-1zm-21-9h2 1 1 2c1 1 2 1 3 1v1c-1 0-2 1-3 1l-1-2-4 1-1-1v-1z" class="C"></path><path d="M328 582h2 1l1 1h0c0 1 0 2 1 2l-1 2c1 0 2-1 3-1h0v1h2c0 1-1 1-1 2h1l1-1v1l-1 1v1c2-1 4-3 6-4l1 1-1 1c0 1 0 1-2 2-1 1-2 1-2 2l5-1v2h2c3-1 7 0 10 0s5-1 8-1 7 1 10-1c1 1 2 1 4 2 3 0 6 0 9 1h1v1c1 1 1 1 2 1h3c0 1 1 1 2 1l4 1c6 1 12 0 19 0 4 0 7 0 11 2h1 1 9c1 1 3 1 4 2h2c2-1 5 0 7 0s3-1 4-1c-2 1-4 2-5 4v1l6 1 14 1v-1l3-3 4-1 5-2 3 1v2l-2 1 2 1c1 0 2 0 3 1-1 0-2 1-2 1l7 1h-1l-5 1h0l1 1h0c-1 1-3 1-3 2l16 1c-6 4-13 6-20 8-1 1-1 0-1 2h0c0 1-1 1 0 2h0v2h-3c-3 1-6 1-10 1v1l-8 1h-1v1h-3c-7 2-15 2-21 2-1 0-1 0-1 1h-2l-1-1c-1 0-2 0-3-1-2 1-5 1-7 1h-3s-1 0-1 1c-1-1-2-1-2-1h-1l1 2c-2 1-17 0-21 0h-2-4c-1-1-3-2-4-3-3-1-7-1-10-1-6-1-11-1-17-3l-11-2c-3 0-7 0-10-2-2-1-7-1-10-1-3-1-7 0-10-1h-9c-1-1-1-1-2-1l1-1v-1c-1 1-3 1-4 1v-1c-1 0-2 1-3 0h0c-1 0-2-1-3-1h-1-3v1h-1c-5 0-10 0-16-1h-1c-2 0-3 0-5-1h-7c2 0 4 0 6-1 0 0 1 0 1-1-1-1-2-1-3-1l-4-1c2-1 4-1 6-2h1l-1-1-2-2-2-1h-3-1c-3-1-5-1-8-2 2 0 3-1 4-1l1-1h0c3-1 5-1 9 0 0-1 0-1 1-1 2-1 9-1 11 0 3-2 6-2 8-2v-1h3l3-1h0c-1-2-3 0-4-1v-2h1 1c-1 0-2-1-2-1-1 0-1 0-1-1 1-1 3-1 5-2h0c3-1 6 0 9-1 1-1 4-1 5-1s3-1 4-1h4c0 1 0 1 1 0h0 3c1 1 1 1 2 1 2-1 2-2 3-3h1c0-2 1-3 2-4l1-2z" class="I"></path><path d="M380 623c2-1 2-1 3 0h1c-1 1-2 1-4 2 0-1 0-1-1-2h1z" class="F"></path><path d="M390 623h2c0 1-1 1-1 2l-1 1c-1 1-3 1-4 1 2-1 3-2 4-4z" class="C"></path><path d="M389 621l-2-1h0c3 0 7 1 10 2l-5 1h-2-1l1-2h-1z" class="G"></path><path d="M389 621h1l-1 2h1c-1 2-2 3-4 4h-3v-1c1 0 2-1 3-2h0l3-2v-1z" class="J"></path><path d="M391 634c-2-1-2-1-3-2v-1c-1 0-4-1-6-2 3 0 6 0 9 1h-1v1c1 1 3 1 3 2-1 0-2 0-2 1z" class="Q"></path><path d="M395 608c2 0 3 0 4-1l1 1h3l-1 1h1l-3 1h-6s1-1 1-2z" class="n"></path><path d="M400 608h3l-1 1h1l-3 1c-1-1-1 0-1-1l1-1z" class="a"></path><path d="M290 597h1 0c2 1 5 1 7 3-1 1-2 1-4 2-1-1-2-1-4-1l3-1h0c-1-2-3 0-4-1v-2h1z" class="P"></path><path d="M391 630c3 0 7 0 8 1l-3 2-2 1h-3c0-1 1-1 2-1 0-1-2-1-3-2v-1h1z" class="S"></path><path d="M297 607c3 0 21 1 22 1l-2 1c1 0 1 0 1 1l-12-1-8-1-1-1z" class="a"></path><path d="M287 601h3c2 0 3 0 4 1-2 0-4 1-6 2h0 0c-3 0-6 1-9 0 3-2 6-2 8-2v-1z" class="U"></path><path d="M394 634l2-1h1c1 1 4 0 5 0l12 1s1 1 2 1h-1c0 1-3 0-3 0-6 0-12 0-18-1z" class="J"></path><path d="M408 604c2 0 5 1 6 2-3 3-7 3-11 3h-1l1-1h-3l-1-1c3-1 6-2 9-2v-1z" class="X"></path><path d="M408 605c1 0 1 0 2 1-1 1-5 2-7 2h-3l-1-1c3-1 6-2 9-2z" class="E"></path><path d="M412 630h8c0 1 1 1 1 1l1 1h6-4c1 1 3 1 5 1v1h-6c-1 0-2-1-3 0 0 0 1 0 2 1h-3s-1 0-1 1c-1-1-2-1-2-1-1 0-2-1-2-1 2-1 4-1 6-2-2 0-9 0-10-1v-1h2z" class="Q"></path><path d="M340 616h1c6 0 10 1 16 2-4 2-8 2-12 3 0-1 0-1-1-1h0c1-1 2-1 3-1v-1c-3 0-6-1-10-1 1 0 2-1 3-1z" class="g"></path><path d="M462 621h4c0 1 0 2-1 2h-1-14c-4 0-7 1-11 0h5c2 0 3 0 5-1h1c1 0 2 1 4 0h-9-9c2 0 5-1 7-1h19z" class="E"></path><path d="M371 626c-4 1-9 0-13-1h1c2-1 3-1 4-1 5-2 12-2 17-1h-1c1 1 1 1 1 2-3 0-6 1-9 1z" class="V"></path><path d="M379 623c1 1 1 1 1 2-3 0-6 1-9 1h-5c1-1 4-1 6-1l7-2z" class="R"></path><defs><linearGradient id="b" x1="381.243" y1="607.577" x2="393.424" y2="609.49" xlink:href="#B"><stop offset="0" stop-color="#1e1c1c"></stop><stop offset="1" stop-color="#3a393a"></stop></linearGradient></defs><path fill="url(#b)" d="M391 607c2 0 3 0 4 1 0 1-1 2-1 2l-23 3c3-2 8-2 11-2v-1h-3s-1-1-2-1l10-1c1 0 3 0 4-1z"></path><path d="M328 600l4 1h3l2 1c0 1 0 2 1 3-4 0-7 0-10-1h-1-3-8v-1h0 6 4l1 1c-1-1-2-2-3-2 1-1 2 0 3 0l1-2z" class="E"></path><path d="M328 600l4 1v1h-4-1l1-2z" class="L"></path><defs><linearGradient id="c" x1="379.758" y1="620.669" x2="376.851" y2="615.308" xlink:href="#B"><stop offset="0" stop-color="#d4d0d7"></stop><stop offset="1" stop-color="#e6e7e2"></stop></linearGradient></defs><path fill="url(#c)" d="M377 616h0c2-1 6 0 8 0h2s1 0 1 1h0-1c-1 2-2 3-4 4-2 0-3 0-4-1h-4c-1-1-3-1-5-1 2-1 4-3 6-3h1z"></path><path d="M334 617h3c4 0 7 1 10 1v1c-1 0-2 0-3 1h0c1 0 1 0 1 1-2 0-6 2-8 2-3-1-5 0-8 0l-1-2h3 3s1 0 2-1h-1v-2l-1-1z" class="O"></path><path d="M344 620c1 0 1 0 1 1-2 0-6 2-8 2-3-1-5 0-8 0l-1-2h3 3c3 0 7-1 10-1z" class="q"></path><path d="M319 608l11 1h2c-1 1-2 1-3 2-1 0-3 1-4 1-4 1-8 0-12 1v-1c-1 0-2 0-2-1h-7v-2h2l12 1c0-1 0-1-1-1l2-1z" class="j"></path><path d="M306 609l12 1h3l-10 1h-7v-2h2z" class="D"></path><path d="M369 604l8 1h-2 1v1l1 1h1 13 0c-1 1-3 1-4 1l-10 1c1 0 2 1 2 1-1 0-15-2-17-2 0 0-1 0-1-1 1-1 2-1 4-1 0-1 0-1 1-1 0 0 1 0 1 1l2-1h-1l1-1z" class="l"></path><path d="M369 604l8 1h-2 1v1l1 1h1-10c-1 0-2 0-3-1h2 0l2-1h-1l1-1z" class="h"></path><path d="M339 593l5-1v2h2c0 1 0 1-1 1 0 1 0 1 1 1h2v1h-5c-2 0-4 0-6 1v1h0 1l-1 1h-6c2 0 4-1 5-1l-4-2-3-1s3-1 4-2h0l-3-1h3c2 0 4-1 6 0h0z" class="U"></path><path d="M339 593l5-1v2h2c0 1 0 1-1 1-2-1-5-1-7-1l1-1h0z" class="c"></path><path d="M332 597h1c2-2 6-1 9-2 1 0 1 0 2 1h2 0 2v1h-5c-2 0-4 0-6 1v1h0 1l-1 1h-6c2 0 4-1 5-1l-4-2zm-13 18l8-1 1-1c1 1 5 0 7 0h8 0v1l-2 1v1h-1c-1 0-2 1-3 1h-3-2-4l-2-1v-1l-4 1h0 0v1c-3-1-7-1-10-1v-1c1-1 4 0 6 0h1z" class="Q"></path><path d="M336 615v-1h7l-2 1v1h-1l-4-1z" class="D"></path><path d="M312 616v-1c1-1 4 0 6 0h1 12c-2 1-3 0-5 0l-4 1h0 0v1c-3-1-7-1-10-1z" class="W"></path><path d="M331 615h5l4 1c-1 0-2 1-3 1h-3-2-4l-2-1v-1c2 0 3 1 5 0z" class="q"></path><defs><linearGradient id="d" x1="456.924" y1="621.753" x2="442.968" y2="617.459" xlink:href="#B"><stop offset="0" stop-color="#474545"></stop><stop offset="1" stop-color="#767476"></stop></linearGradient></defs><path fill="url(#d)" d="M454 618h6l-2 1v1c1 0 2 0 4 1h-19c-2 0-5 1-7 1-3 0-7 1-10 0 2-2 9-1 11-1v-1h-4-5l1-1 25-1z"></path><path d="M322 616h0 0l4-1v1l2 1h4 2l1 1v2h1c-1 1-2 1-2 1h-3v-1h-11-5c-2 0-4 0-7-1h0-2 0c2 0 3 0 5-1h0-6c-1-1-1-1-1-2v-1c3 0 6 0 8 1 3 0 7 0 10 1v-1z" class="D"></path><path d="M322 616h0 0l4-1v1l2 1h4l-1 1h-8c-4 0-8 1-11 0 4 0 8 1 12-1h-2v-1z" class="F"></path><path d="M322 616h0 0l4-1v1l2 1-6-1z" class="K"></path><path d="M332 617h2l1 1v2h1c-1 1-2 1-2 1h-3v-1h-11-5c-2 0-4 0-7-1h0-2 0c2 0 3 0 5-1h0 1c3 1 7 0 11 0h8l1-1z" class="S"></path><path d="M311 618h1c3 1 7 0 11 0l8 1h0c-5 1-9 1-14 0h-9 0-2 0c2 0 3 0 5-1h0z" class="O"></path><path d="M315 620h5 11v1h-3l1 2c3 0 5-1 8 0h-1c0 1-1 1-1 2h1c1 0 2 0 4 1 2 0 3 0 5 1l2 1c-3 0-7 0-10-2-2-1-7-1-10-1-3-1-7 0-10-1h-9c-1-1-1-1-2-1l1-1h3 0v-1h2 4l-1-1z" class="E"></path><path d="M318 622c2-1 8-1 10-1l1 2c3 0 5-1 8 0h-1-15l-3-1z" class="K"></path><path d="M310 622h5c1 0 2 1 3 0l3 1h1v1c-1 1-3 0-5 0h-9c-1-1-1-1-2-1l1-1h3z" class="C"></path><path d="M315 620h5 11v1h-3c-2 0-8 0-10 1-1 1-2 0-3 0h-5 0v-1h2 4l-1-1z" class="J"></path><path d="M315 620h5 2v1h-6v1h-1-5 0v-1h2 4l-1-1z" class="E"></path><path d="M267 605c0-1 0-1 1-1 2-1 9-1 11 0 3 1 6 0 9 0h0c3 1 6 1 9 3l1 1 8 1h-2v2l-11-2-14-1v-1l-12-2z" class="j"></path><path d="M279 607c2 1 5 1 7 1 4 0 8-1 12 0l8 1h-2v2l-11-2-14-1v-1z" class="J"></path><path d="M406 628h-1c-2-1-4-1-5-1h-2c1-1 13-1 16-1h0-9-1-8c2-1 14-1 17-1h-2c-2 0-5 0-6-1h0 15c1 1 3 1 5 1 1 1 2 1 3 0 1 0 4 1 5 2h6c1 0 3 0 4 1-1 1-4 1-6 1h-1-6c-1 0-2 0-2 1-3 0-6-1-8 0h0-8 6c1 0 2 0 3-1h0-3s0-1-1-1h-2c-3-1-6 0-9 0z" class="L"></path><path d="M406 628l1-1h8c2 0 3-1 3 0h7c2 0 3 1 5 2-1 0-2 0-2 1-3 0-6-1-8 0h0-8 6c1 0 2 0 3-1h0-3s0-1-1-1h-2c-3-1-6 0-9 0z" class="D"></path><path d="M293 609l11 2h7c0 1 1 1 2 1v1h0c1 1 3 1 5 2-2 0-5-1-6 0v1c-2-1-5-1-8-1l-1 1v1c-5-1-9-2-13-2l-1-1h-5v-1h0c3 0 5 0 7-1l-6-1v-1h-1c3-1 6 0 9-1z" class="U"></path><path d="M291 612c5 0 9 1 13 1l-1 1h-4-4-6-5v-1h0c3 0 5 0 7-1z" class="R"></path><path d="M293 609l11 2h7c0 1 1 1 2 1v1h0-9c-4 0-8-1-13-1l-6-1v-1h-1c3-1 6 0 9-1z" class="X"></path><path d="M446 607s1-1 2 0h-1l1 1v1 1l3 1h-2v1h-6l-1 2v1c-1 0-2 0-3-1-1 0-3 0-4 1h-4 1v-1l-1-1h0 2v-1h-5-1c-2 0-3-1-5-1l-2-2h-1-1c1-1 3-1 4-1l15 1c1-1 2 0 4-1l1-1c1-1 3 0 4 0z" class="o"></path><defs><linearGradient id="e" x1="429.253" y1="612.466" x2="424.249" y2="607.708" xlink:href="#B"><stop offset="0" stop-color="#afaaac"></stop><stop offset="1" stop-color="#c2c4c1"></stop></linearGradient></defs><path fill="url(#e)" d="M419 609c5 0 9 0 13 1 1 0 3 0 4 1 2 1 3 0 5 1h-14c-2 0-3-1-5-1l-2-2h-1z"></path><path d="M427 612h14 2l-1 2v1c-1 0-2 0-3-1-1 0-3 0-4 1h-4 1v-1l-1-1h0 2v-1h-5-1z" class="Z"></path><path d="M446 607s1-1 2 0h-1l1 1v1 1l3 1h-2-2c-3-1-7-1-10-2 1-1 2 0 4-1l1-1c1-1 3 0 4 0z" class="R"></path><path d="M428 620h-1c-3-2-7-1-11-1l-17-1c-2 0-5 1-6 0 1-1 8-1 11-1h0 2 16c-4-3-8-2-13-2h1c2-1 8-2 10 0h5 4 2 4c1-1 3-1 4-1 1 1 2 1 3 1h5-3v1h-4l12 1h5v1h-3l-25 1-1 1z" class="Q"></path><path d="M431 615h4c1-1 3-1 4-1 1 1 2 1 3 1h5-3v1h-4-11v-1h2z" class="h"></path><path d="M439 623c4 1 7 0 11 0h14l-3 1c-2 1-5 1-7 2 2 0 6 0 8 1h2 2l1 1-2 1-1-1h-1-3c-1 0-3 0-5 1h-5 0-6v1h-9v-1h1 1c2 0 5 0 6-1-1-1-3-1-4-1h-6c-1-1-4-2-5-2-1 1-2 1-3 0-2 0-4 0-5-1h5c2 0 3 1 5 1 3-1 6-2 9-1v-1z" class="F"></path><path d="M439 623c4 1 7 0 11 0h14l-3 1c-2 1-5 1-7 2 2 0 6 0 8 1h2 2l1 1-2 1-1-1h-1-3c-1 0-3 0-5 1h-5 0-6 1v-1c1 0 2 1 3 0h4 1 1l1-1h-2v-2h-2-2c-3 0-6 0-10-1h0v-1z" class="i"></path><path d="M464 627h2l1 1-2 1-1-1h-1-3c2-1 3-1 4-1z" class="V"></path><defs><linearGradient id="f" x1="276.661" y1="606.141" x2="272.156" y2="610.719" xlink:href="#B"><stop offset="0" stop-color="#4c4e4c"></stop><stop offset="1" stop-color="#646064"></stop></linearGradient></defs><path fill="url(#f)" d="M258 605h0c3-1 5-1 9 0l12 2v1l14 1c-3 1-6 0-9 1h1v1l6 1c-2 1-4 1-7 1h0v1h-2c-1-1-1-1-2-1s-3 0-4 1h-2l-1-1-1 1c-1 0-2 0-3-1h1l-1-1-2-2-2-1h-3-1c-3-1-5-1-8-2 2 0 3-1 4-1l1-1z"></path><path d="M258 605l1 1h-1l1 1 6 1h2c1 1 0 1 1 1 0 1 3 1 4 1h-5l-2-1h-3-1c-3-1-5-1-8-2 2 0 3-1 4-1l1-1z" class="G"></path><path d="M258 605h0c3-1 5-1 9 0l12 2v1l-20-2-1-1z" class="H"></path><defs><linearGradient id="g" x1="285.573" y1="607.156" x2="272.987" y2="615.32" xlink:href="#B"><stop offset="0" stop-color="#878786"></stop><stop offset="1" stop-color="#b2adb0"></stop></linearGradient></defs><path fill="url(#g)" d="M272 610l13 1 6 1c-2 1-4 1-7 1h0v1h-2c-1-1-1-1-2-1s-3 0-4 1h-2l-1-1-1 1c-1 0-2 0-3-1h1l-1-1-2-2h5z"></path><defs><linearGradient id="h" x1="468.091" y1="606.479" x2="457.005" y2="622.498" xlink:href="#B"><stop offset="0" stop-color="#151811"></stop><stop offset="1" stop-color="#4d4850"></stop></linearGradient></defs><path fill="url(#h)" d="M464 612h5 4v1h0 7l2 1-2 2-4 1c-1-1-6 0-8 0l-8 1h-6 3v-1h-5l-12-1h4v-1h3-5v-1l1-2h6 15z"></path><path d="M467 616h0c2 0 5-1 8-1h4s1 0 1 1l-4 1c-1-1-6 0-8 0h2v-1h-3z" class="l"></path><path d="M452 617l15-1h3v1h-2l-8 1h-6 3v-1h-5z" class="o"></path><defs><linearGradient id="i" x1="462.026" y1="611.631" x2="442.07" y2="614.192" xlink:href="#B"><stop offset="0" stop-color="#afaeaf"></stop><stop offset="1" stop-color="#dddbdc"></stop></linearGradient></defs><path fill="url(#i)" d="M464 612h5 4v1h0-4l-22 2h-5v-1l1-2h6 15z"></path><path d="M464 612h5 4v1h0-4-5v-1z" class="V"></path><path d="M361 599c1 0 4 1 5 0l10 2c2 0 4 0 6 1h1l17 1c2 0 7 1 8 1v1c-3 0-6 1-9 2-1 1-2 1-4 1-1-1-2-1-4-1h0-13-1l-1-1v-1h-1 2l-8-1-7-1v-1l-4-1h-5v-1h5 0c1-1 2-1 3-1z" class="E"></path><path d="M379 603h0v-1l3 1v-1h-3 3 1l17 1-3 2c-6-1-12-1-18-2z" class="X"></path><path d="M361 599c1 0 4 1 5 0l10 2c2 0 4 0 6 1h-3 3v1l-3-1v1h0c-6 0-12-3-17-1l-4-1h-5v-1h5 0c1-1 2-1 3-1z" class="T"></path><path d="M358 600c2 0 3-1 4 0l1 1c-2 1-4 0-5 0h-5v-1h5z" class="c"></path><defs><linearGradient id="j" x1="391.586" y1="608.514" x2="390.098" y2="601.921" xlink:href="#B"><stop offset="0" stop-color="#181517"></stop><stop offset="1" stop-color="#4c4b4b"></stop></linearGradient></defs><path fill="url(#j)" d="M400 603c2 0 7 1 8 1v1c-3 0-6 1-9 2-1 1-2 1-4 1-1-1-2-1-4-1h0-13-1l-1-1v-1h-1 2c6 0 13 1 19 0h1l3-2z"></path><defs><linearGradient id="k" x1="440.159" y1="628.551" x2="440.148" y2="631.268" xlink:href="#B"><stop offset="0" stop-color="#acaaab"></stop><stop offset="1" stop-color="#cfcfd0"></stop></linearGradient></defs><path fill="url(#k)" d="M444 629h6 0 5c2-1 4-1 5-1h3 1l1 1 4 1v1l-8 1h-1v1h-3c-7 2-15 2-21 2-1 0-1 0-1 1h-2l-1-1c-1 0-2 0-3-1-2 1-5 1-7 1-1-1-2-1-2-1 1-1 2 0 3 0h6v-1c-2 0-4 0-5-1h4-6l-1-1s-1 0-1-1h0c2-1 5 0 8 0 0-1 1-1 2-1h6-1v1h9v-1z"></path><path d="M454 631c3-1 5 0 8 0l-1 1h-1-6v-1z" class="j"></path><path d="M464 628l1 1 4 1v1l-8 1 1-1c1 0 1 0 2-1-1-1-1 0-2-1l2-1z" class="p"></path><path d="M428 632l26-1v1h6v1h-3-20c-2 0-4-1-6 0-1 0-1 1-2 1-2 1-5 1-7 1-1-1-2-1-2-1 1-1 2 0 3 0h6v-1c-2 0-4 0-5-1h4z" class="f"></path><path d="M437 633h20c-7 2-15 2-21 2-1 0-1 0-1 1h-2l-1-1c-1 0-2 0-3-1 1 0 1-1 2-1 2-1 4 0 6 0z" class="R"></path><path d="M431 633c2-1 4 0 6 0l5 1h-10l-1-1z" class="D"></path><path d="M348 596h2v1c1 0 2 0 3 1l8 1c-1 0-2 0-3 1h0-5v1h5l4 1v1l7 1-1 1h1l-2 1c0-1-1-1-1-1-1 0-1 0-1 1-2 0-3 0-4 1 0 1 1 1 1 1l-15-1c-1 0-2-1-3-2h-1-5c-1-1-1-2-1-3l-2-1h-3l-4-1c-2 0-3-1-4-2 2 0 4 1 7 2h6l1-1h-1 0v-1c2-1 4-1 6-1h5v-1z" class="e"></path><path d="M362 603l7 1-1 1h1l-2 1c0-1-1-1-1-1-3 0-5 0-7-1v-1h3zm-14-7h2v1c1 0 2 0 3 1l8 1c-1 0-2 0-3 1h0-5v1l-5-1c-2 1-4 1-6 0h-5l1-1h-1 0v-1c2-1 4-1 6-1h5v-1z" class="O"></path><path d="M337 600l1-1h-1 0v-1c2-1 4-1 6-1l-3 1c1 1 2 1 3 1l5 1c-2 1-4 1-6 0h-5z" class="R"></path><path d="M335 601h7c2 1 9 1 10 2v1h1 2v1l-1 1h-1c-2 0-4 0-6 1-1 0-2-1-3-2h-1-5c-1-1-1-2-1-3l-2-1z" class="F"></path><path d="M345 605h-3v-1c1-1 3-1 4-1s1 1 2 1l-1 1h-2z" class="e"></path><path d="M347 605c2-1 3-1 5 0h1 0v1h0c-2 0-4 0-6 1-1 0-2-1-3-2h-1 2 2z" class="C"></path><defs><linearGradient id="l" x1="358.902" y1="597.932" x2="358.346" y2="592.989" xlink:href="#B"><stop offset="0" stop-color="#1f1c1f"></stop><stop offset="1" stop-color="#3d3e3c"></stop></linearGradient></defs><path fill="url(#l)" d="M374 592c1 1 2 1 4 2 3 0 6 0 9 1h1v1c1 1 1 1 2 1h3c0 1 1 1 2 1-4 0-8-1-11 2l-1 1v1h-1c-2-1-4-1-6-1l-10-2c-1 1-4 0-5 0l-8-1c-1-1-2-1-3-1v-1h-2-2c-1 0-1 0-1-1 1 0 1 0 1-1 3-1 7 0 10 0s5-1 8-1 7 1 10-1z"></path><path d="M350 596l22 2h-5c0 1-1 1-1 1-1 1-4 0-5 0l-8-1c-1-1-2-1-3-1v-1z" class="J"></path><path d="M387 595h1v1c1 1 1 1 2 1h3c0 1 1 1 2 1-4 0-8-1-11 2l-1 1v1h-1c-2-1-4-1-6-1l-10-2s1 0 1-1h5c3 0 9 0 12-1h1s1 0 1-1c1 0 1-1 1-1z" class="D"></path><path d="M374 592c1 1 2 1 4 2 3 0 6 0 9 1 0 0 0 1-1 1 0 1-1 1-1 1h-1c-3-1-8 0-12-1-2 1-8 0-10-1h-6v-1c3 0 5-1 8-1s7 1 10-1z" class="g"></path><path d="M372 596h0c2-1 8-2 10-1 1 0 3 0 4 1 0 1-1 1-1 1h-1c-3-1-8 0-12-1z" class="Q"></path><path d="M328 582h2 1l1 1h0c0 1 0 2 1 2l-1 2c1 0 2-1 3-1h0v1h2c0 1-1 1-1 2h1l1-1v1l-1 1v1c2-1 4-3 6-4l1 1-1 1c0 1 0 1-2 2-1 1-2 1-2 2h0c-2-1-4 0-6 0h-3c-4 1-7 2-10 2-5 1-10 0-16 0h-5c-1 0 0 0-1-1h0l-1-1h-4c3-1 6 0 9-1 1-1 4-1 5-1s3-1 4-1h4c0 1 0 1 1 0h0 3c1 1 1 1 2 1 2-1 2-2 3-3h1c0-2 1-3 2-4l1-2z" class="Y"></path><path d="M331 590c-1 1-3 1-4 1h-2 0c2-1 3-3 4-5v1c1 0 1 1 2 1h1c1 0 0 1 1 1 1-1 1-1 2-1l1 2h-5z" class="O"></path><path d="M343 587l1 1-1 1c0 1 0 1-2 2-1 1-2 1-2 2h0c-2-1-4 0-6 0l1-1v-1c0-1-2-1-3-1h5l1 1h0c2-1 4-3 6-4z" class="f"></path><path d="M332 583h0c0 1 0 2 1 2l-1 2c1 0 2-1 3-1h0v1h2c0 1-1 1-1 2h1l1-1v1l-1 1v1h0l-1-1-1-2c-1 0-1 0-2 1-1 0 0-1-1-1h-1c-1 0-1-1-2-1v-1c1-1 2-1 3-3z" class="P"></path><path d="M307 591c1 0 3-1 4-1h4c0 1 0 1 1 0l1 2c-1 1-1 1-2 1-5 2-11 0-16 2-1 0 0 0-1-1h0l-1-1h-4c3-1 6 0 9-1 1-1 4-1 5-1z" class="C"></path><path d="M269 613c1 1 2 1 3 1l1-1 1 1h2c1-1 3-1 4-1s1 0 2 1h2 5l1 1c4 0 8 1 13 2v-1l1-1v1c0 1 0 1 1 2h6 0c-2 1-3 1-5 1h0 2 0c3 1 5 1 7 1l1 1h-4-2v1h0-3v-1c-1 1-3 1-4 1v-1c-1 0-2 1-3 0h0c-1 0-2-1-3-1h-1-3v1h-1c-5 0-10 0-16-1h-1c-2 0-3 0-5-1h-7c2 0 4 0 6-1 0 0 1 0 1-1-1-1-2-1-3-1l-4-1c2-1 4-1 6-2z" class="J"></path><path d="M276 614c1-1 3-1 4-1s1 0 2 1h2 5l1 1c-4 0-8 1-11 0l-3-1z" class="e"></path><path d="M270 617h3l2 1c3 0 6-1 9 0-3 0-7-1-10 1v1h2-1c-2 0-3 0-5-1h-7c2 0 4 0 6-1 0 0 1 0 1-1zm38 2h0c3 1 5 1 7 1l1 1h-4-2v1h0-3v-1l-3-1h-1c1-1 4-1 5-1z" class="B"></path><path d="M284 618c3 0 6 0 9 2v1h-1c-5 0-10 0-16-1h-2v-1c3-2 7-1 10-1z" class="I"></path><defs><linearGradient id="m" x1="470.505" y1="611.612" x2="470.061" y2="606.253" xlink:href="#B"><stop offset="0" stop-color="#151314"></stop><stop offset="1" stop-color="#323031"></stop></linearGradient></defs><path fill="url(#m)" d="M446 603c2-1 5 0 7 0s3-1 4-1c-2 1-4 2-5 4v1l6 1 14 1v-1l3-3 4-1 5-2 3 1v2l-2 1 2 1c1 0 2 0 3 1-1 0-2 1-2 1l7 1h-1l-5 1h0l1 1h0c-1 1-3 1-3 2l-1-1h-1c0 1-1 1-1 2-1 0-2 0-2-1l-2-1h-7 0v-1h-4-5-15v-1h2l-3-1v-1-1l-1-1h1s1 0 2-1c1 0 1-1 1-1v-1h-5c1 0 3 0 5-1h0-5z"></path><path d="M488 609l7 1h-1l-5 1h0-6 5v-1-1c-3 0-7 1-10 0h-1 11z" class="W"></path><path d="M483 611h6l1 1h0c-1 1-3 1-3 2l-1-1h-1c0 1-1 1-1 2-1 0-2 0-2-1l-2-1h-7 0v-1h-4 0l14-1z" class="E"></path><path d="M480 613l1-1h1 1 3 1l-1 1h-1c0 1-1 1-1 2-1 0-2 0-2-1l-2-1z" class="C"></path><path d="M446 603c2-1 5 0 7 0s3-1 4-1c-2 1-4 2-5 4v1h-2v1l3 1c3 2 8 0 11 3h5 0-5-15v-1h2l-3-1v-1-1l-1-1h1s1 0 2-1c1 0 1-1 1-1v-1h-5c1 0 3 0 5-1h0-5z" class="S"></path><path d="M479 604l5-2 3 1v2l-2 1 2 1c1 0 2 0 3 1-1 0-2 1-2 1h-11-5v-1l3-3 4-1z" class="H"></path><path d="M475 605l4-1h1c1 1 2 1 4 1h1l-2 2c-2 0-6 0-8 1h-3l3-3z" class="N"></path><path d="M482 614c0 1 1 1 2 1 0-1 1-1 1-2h1l1 1 16 1c-6 4-13 6-20 8-1 1-1 0-1 2h0c0 1-1 1 0 2h0v2h-3c-3 1-6 1-10 1l-4-1 2-1-1-1h-2-2c-2-1-6-1-8-1 2-1 5-1 7-2l3-1h1c1 0 1-1 1-2h-4c-2-1-3-1-4-1v-1l2-1 8-1c2 0 7-1 8 0l4-1 2-2z" class="B"></path><path d="M472 623c2 0 8-1 10-2 1 0 2 0 2-1 3-1 8-2 11-3v1c-3 2-8 2-12 4v1c-1 1-1 0-1 2h0-1s-1-1-2-1c-2-1-7 0-9 1-2 0-3 1-4 2h-2-2c-2-1-6-1-8-1 2-1 5-1 7-2l3-1h1c1 0 1-1 1-2h3c0 2-2 1-1 2h4z" class="T"></path><path d="M466 627c1-1 2-2 4-2 2-1 7-2 9-1 1 0 2 1 2 1h1c0 1-1 1 0 2h0v2h-3c-3 1-6 1-10 1l-4-1 2-1-1-1z" class="G"></path><path d="M481 625h1c0 1-1 1 0 2h0c-5 1-9 2-13 1v-1c4-1 8-1 12-2z" class="c"></path><path d="M469 628c4 1 8 0 13-1v2h-3c-3 1-6 1-10 1l-4-1 2-1h2z" class="H"></path><path d="M468 617c2 0 7-1 8 0l-7 2h0c5 1 10-1 15-2h4 2c-2 1-3 1-5 1 0 1-1 1-1 2l-12 3h-4c-1-1 1 0 1-2h-3-4c-2-1-3-1-4-1v-1l2-1 8-1z" class="f"></path><defs><linearGradient id="n" x1="416.197" y1="605.61" x2="416.417" y2="599.434" xlink:href="#B"><stop offset="0" stop-color="#cccbcc"></stop><stop offset="1" stop-color="#f2f1f2"></stop></linearGradient></defs><path fill="url(#n)" d="M383 602v-1l1-1c3-3 7-2 11-2l4 1c6 1 12 0 19 0 4 0 7 0 11 2h1 1 9c1 1 3 1 4 2h2 5 0c-2 1-4 1-5 1h5v1s0 1-1 1c-1 1-2 1-2 1-1-1-2 0-2 0-1 0-3-1-4 0l-1 1c-2 1-3 0-4 1l-15-1v-1c-1 0-2 0-3-1h-5c-1-1-4-2-6-2-1 0-6-1-8-1l-17-1z"></path><defs><linearGradient id="o" x1="435.17" y1="609.926" x2="431.923" y2="603.995" xlink:href="#B"><stop offset="0" stop-color="#a0a1a1"></stop><stop offset="1" stop-color="#c5c2c3"></stop></linearGradient></defs><path fill="url(#o)" d="M422 607c1 0 3 0 4-1h4c1 1 2 1 3 1 3 0 5-1 7-1s4 0 6 1c-1 0-3-1-4 0l-1 1c-2 1-3 0-4 1l-15-1v-1z"></path><path d="M430 601h1 9c1 1 3 1 4 2h2 5 0c-2 1-4 1-5 1-7 1-14 0-20 0v-1h2l2-1h0v-1z" class="i"></path><path d="M430 601h1 9c1 1 3 1 4 2h-16l2-1h0v-1z" class="J"></path><path d="M204 541l15-1h0c3 1 6 1 9 1 2 0 4-1 5 0v1c2 0 4 0 5 1v1l1-1c1 0 2 0 3 1h0v1 1h4c1 0 4 1 5 0 2 0 5 0 7 1v1h0v1 1 2 1l-2 1c1 1 2 0 2 1v1 1 3 6 3 4h0v2 4h0v3c1 1 1 2 1 3l-1 1v1l-2 2h-1c-1 1-2 1-3 2h-1 6v2c1 1 3-1 4 1 1 1 2 1 3 1 2 1 4 1 6 1 1 0 3 1 5 1 1-1 2 0 4-1h2v1c1 0 1-1 2 0h1 6-1v2c1 1 3-1 4 1h0l-3 1h-3v1c-2 0-5 0-8 2-2-1-9-1-11 0-1 0-1 0-1 1-4-1-6-1-9 0h0l-1 1c-1 0-2 1-4 1 3 1 5 1 8 2h1 3l2 1 2 2 1 1h-1c-2 1-4 1-6 2l4 1c1 0 2 0 3 1 0 1-1 1-1 1-2 1-4 1-6 1h7c2 1 3 1 5 1h1c6 1 11 1 16 1h1v-1h3 1c1 0 2 1 3 1h0c1 1 2 0 3 0v1c1 0 3 0 4-1v1l-1 1c1 0 1 0 2 1h9c3 1 7 0 10 1 3 0 8 0 10 1 3 2 7 2 10 2l11 2c6 2 11 2 17 3 3 0 7 0 10 1 1 1 3 2 4 3-1 0-1 1-1 1h-3-1l1 1h-2c-1 0-2 1-4 1v-1c-2 0-2-1-3-1-2 1-2 1-4 1l-1 1h-1c-2 1-6 1-9 1h-13-5l-2 1h0c1 1 2 1 4 1h-8-4v1h-2-2-9-1 0v1h-4-1l1 1h4c2 1 4 1 6 2h3l-10 1c-3 0-8-1-12-2l-3-1-10-2c-3 0-6-1-9-2-4 0-7-1-11-1h-4-1c-3 0-5-1-7-1-6-2-11-6-17-8l-15-3c-4-1-8-3-12-5-3-1-7-2-10-4-4-2-7-4-10-6-4-2-8-4-11-7l-2-2h-2l-1-2-2-1c-1 0-3-1-4-2-3-1-6-3-9-5-1 0-2-1-4-1v-1h8c1 0 3 1 4 0v-1h1c2 0 5 1 6 0h4 2c2-1 4-1 6-1h3c-1-1-1-1-2-1v-1h3l1-1c-1-1-1-1-2-1v-1-2-1h1l1 2c1 0 2 1 3 1v-4-13-4c0-4 1-8 0-12v-7l-1-5h1 3z" class="h"></path><path d="M315 637l1-1c1-1 1-1 2 0h10c-2 1-3 1-4 2h-3c-2 0-4-1-6-1zm-11-8l5 1c1 0 1 1 2 2h-2v1 1c-1 0-2-1-4-1v-1h-3c-2 1-4 1-7 1l2-2c1 0 2-1 3-1h3c0-1 0 0 1-1h0z" class="E"></path><path d="M311 632l7 1h8c2-1 4-1 6-1l10 1h2v1c-5 1-10 1-15 1-5-1-10-1-14-1-2 1-4 1-6 0v-1-1h2z" class="Q"></path><path d="M311 632l7 1h5c-2 1-10 1-12 0h0-2v-1h2z" class="D"></path><path d="M269 627c5 1 10-1 16-1v1c3 0 7-1 10 0h3l1 1h3l2 1h0c-1 1-1 0-1 1h-3c-1 0-2 1-3 1l-2 2c-3 0-6 0-9-1-2 0-5-1-7-1-3-1-6-1-10-1h0c-1-1-2-1-3-1s-2 0-3-1h-1 4v-1l2 1 1-1z" class="B"></path><path d="M302 628l2 1h0c-1 1-1 0-1 1h-3c-1 0-2 1-3 1l-2 2c-3 0-6 0-9-1h0 4c2-1 4-1 6-2h0c1 0 2 0 3-1h-3c2 0 4 0 6-1z" class="Q"></path><path d="M269 627c5 1 10-1 16-1v1c3 0 7-1 10 0h3l1 1h3c-2 1-4 1-6 1h0-1 0v1c-1 0-2 0-3-1h-9-1s-1 0-2-1c-4-1-8 0-12 0l1-1z" class="E"></path><path d="M295 627h3l1 1h3c-2 1-4 1-6 1h0-1 0v1c-1 0-2 0-3-1 0 0 0-1-1-1h-1c1-1 4-1 5-1z" class="D"></path><path d="M358 630c6 2 11 2 17 3 3 0 7 0 10 1 1 1 3 2 4 3-1 0-1 1-1 1h-3-1l1 1h-2c-1 0-2 1-4 1v-1c-2 0-2-1-3-1-2 1-2 1-4 1 1 0 1-1 1-2h-1-15c-3-1-6 0-10-1 2 0 4 1 5 0h-3 11l-5-1v-1h-2c1-2 4 0 5-1v-1h-5v-1h3c1 0 1 1 3 0 0 0 0-1-1-1z" class="a"></path><path d="M373 637c4 1 8 0 11 1l1 1h-2c-1 0-2 1-4 1v-1c-2 0-2-1-3-1-2 1-2 1-4 1 1 0 1-1 1-2z" class="S"></path><path d="M355 634h10 7c5 1 10 0 16 2-4 1-9 0-12 0h-16l-5-1v-1z" class="J"></path><path d="M328 636c3 0 6 1 10 1h19 15 1c0 1 0 2-1 2l-1 1h-1c-2 1-6 1-9 1h-13-5-6-12-6v-1h2v-1-1h3c1-1 2-1 4-2z" class="m"></path><path d="M337 640h13c0 1-1 1-2 1h-5-6 3 0-2c0-1-1-1-1-1z" class="F"></path><path d="M324 638h13l-2 1h5-10c-1 0-3 0-4 1h11s1 0 1 1h2 0-3-12-6v-1h2v-1-1h3z" class="J"></path><path d="M324 638h13l-2 1h-14v-1h3z" class="H"></path><defs><linearGradient id="p" x1="354.58" y1="642.553" x2="352.501" y2="633.97" xlink:href="#B"><stop offset="0" stop-color="#a9a7a7"></stop><stop offset="1" stop-color="#cfcdd0"></stop></linearGradient></defs><path fill="url(#p)" d="M328 636c3 0 6 1 10 1h19 15 1c0 1 0 2-1 2h-32-5l2-1h-13c1-1 2-1 4-2z"></path><path d="M328 636c3 0 6 1 10 1h19 15c-4 1-7 1-11 1l-16-1c-2 0-6 0-8 1h-13c1-1 2-1 4-2z" class="d"></path><path d="M327 625c3 0 8 0 10 1 3 2 7 2 10 2l11 2c1 0 1 1 1 1-2 1-2 0-3 0h-3v1c-1 1-7 2-9 2v-1h-2l-10-1c-2 0-4 0-6 1h-8l-7-1c-1-1-1-2-2-2l-5-1-2-1h-3l-1-1h-3c-3-1-7 0-10 0v-1c10 0 20 2 30 3l1-2c3 1 5 1 8 1l-3-1c-1 0-2 0-3-1h3v-1h6z" class="m"></path><path d="M309 630l23 2c-2 0-4 0-6 1h-8l-7-1c-1-1-1-2-2-2z" class="d"></path><path d="M316 627c3 1 5 1 8 1s6 1 8 1l11 1h3c3 0 5 1 7 1v1c-1 1-7 2-9 2v-1h-2l4-1-5-1-26-2 1-2z" class="D"></path><path d="M346 630c3 0 5 1 7 1v1c-1 1-7 2-9 2v-1h-2l4-1-5-1c2 0 5 1 7 0-1 0-1-1-2-1z" class="E"></path><path d="M327 625c3 0 8 0 10 1 3 2 7 2 10 2l11 2c1 0 1 1 1 1-2 1-2 0-3 0h-3c-2 0-4-1-7-1h-3l-11-1c-2 0-5-1-8-1l-3-1c-1 0-2 0-3-1h3v-1h6z" class="c"></path><path d="M321 626h6c1 1 2 1 3 2 1 0 2 0 2 1-2 0-5-1-8-1l-3-1c-1 0-2 0-3-1h3z" class="S"></path><defs><linearGradient id="q" x1="303.391" y1="612.244" x2="266.351" y2="637.663" xlink:href="#B"><stop offset="0" stop-color="#c6c3c3"></stop><stop offset="1" stop-color="#e5e5e7"></stop></linearGradient></defs><path fill="url(#q)" d="M293 620h3 1c1 0 2 1 3 1h0c1 1 2 0 3 0v1c1 0 3 0 4-1v1l-1 1c1 0 1 0 2 1h9c3 1 7 0 10 1h-6v1h-3c1 1 2 1 3 1l3 1c-3 0-5 0-8-1l-1 2c-10-1-20-3-30-3-6 0-11 2-16 1l-7-1 1-1h-1l1-1c-3 0-5 0-7-1l2-1c2-2 5 0 7-1h1 1c-1 0-2 0-3-1 2 0 4 1 5 1h3 2l1-1h1c6 1 11 1 16 1h1v-1z"></path><path d="M266 623l1-1 41 2h9c3 1 7 0 10 1h-6v1h-3c1 1 2 1 3 1l3 1c-3 0-5 0-8-1-2 0-3 0-4-1-1 0-3 0-4-1-5-1-9-1-13-1h-14l-15-1z" class="l"></path><path d="M308 625l10 1c1 1 2 1 3 1l3 1c-3 0-5 0-8-1-2 0-3 0-4-1-1 0-3 0-4-1z" class="F"></path><path d="M293 620h3 1c1 0 2 1 3 1h0c1 1 2 0 3 0v1c1 0 3 0 4-1v1l-1 1c1 0 1 0 2 1l-41-2-1 1c-1 0-2 0-3-1h-5c2-2 5 0 7-1h1 1c-1 0-2 0-3-1 2 0 4 1 5 1h3 2l1-1h1c6 1 11 1 16 1h1v-1z" class="D"></path><path d="M240 628c4-1 8 1 12 2l8 1h2c2-1 3-1 5-1 0 0 1 1 2 0h0c4 0 7 1 11 2l21 3c4 0 10 0 14 2 2 0 4 1 6 1v1 1h-2v1h6 12 6l-2 1h0c1 1 2 1 4 1h-8-4v1h-2-2-9-1 0v1h-4-1l1 1h4c2 1 4 1 6 2h3l-10 1c-3 0-8-1-12-2l-3-1-10-2c-3 0-6-1-9-2-4 0-7-1-11-1h-4-1c-3 0-5-1-7-1l2-2c-2 0-3-1-5-2l-5-3-9-3c-1-1-2-1-4-2z" class="i"></path><path d="M267 636c3-1 9 0 12 1h-5v1h0v1l-9-2v-1h2z" class="F"></path><path d="M274 638c6 0 12 0 17 1h-3v1h4-6l-12-1v-1h0z" class="d"></path><defs><linearGradient id="r" x1="255.493" y1="635.475" x2="264.69" y2="633.892" xlink:href="#B"><stop offset="0" stop-color="#878386"></stop><stop offset="1" stop-color="#9fa09e"></stop></linearGradient></defs><path fill="url(#r)" d="M253 633c3-1 7 0 9 1s3 1 5 2h-2v1l-7-1-5-3z"></path><path d="M280 635h6l8 1c1 0 2 0 3 1 1 0 1 1 2 1-7 1-13-1-19-3z" class="G"></path><defs><linearGradient id="s" x1="274.642" y1="642.237" x2="282.766" y2="638.07" xlink:href="#B"><stop offset="0" stop-color="#727273"></stop><stop offset="1" stop-color="#8f8f8f"></stop></linearGradient></defs><path fill="url(#s)" d="M258 636l7 1 9 2 12 1c2 1 2 1 4 1v1c1 0 2-1 3 0 0 0 1 0 2 1l-11-1c-4 0-7-1-11-1h-4-1c-3 0-5-1-7-1l2-2c-2 0-3-1-5-2z"></path><path d="M263 638l10 3h-4-1c-3 0-5-1-7-1l2-2z" class="p"></path><path d="M252 630l8 1h2c2-1 3-1 5-1 0 0 1 1 2 0h0c4 0 7 1 11 2l-1 1 4 1c0 1 1 1 2 1h1-6c-7 0-14 0-20-2l-6-1-2-2z" class="D"></path><path d="M252 630l8 1h2c4 1 8 2 12 2v1c-1 0-1-1-2-1h-8c-1-1-3-1-4 0l-6-1-2-2z" class="o"></path><path d="M262 631c2-1 3-1 5-1 0 0 1 1 2 0h0c4 0 7 1 11 2l-1 1h-5c-4 0-8-1-12-2z" class="M"></path><defs><linearGradient id="t" x1="299.395" y1="639.548" x2="298.613" y2="630.921" xlink:href="#B"><stop offset="0" stop-color="#bebebf"></stop><stop offset="1" stop-color="#e4e1e0"></stop></linearGradient></defs><path fill="url(#t)" d="M280 632l21 3c4 0 10 0 14 2 2 0 4 1 6 1v1l-22-1c-1 0-1-1-2-1-1-1-2-1-3-1l-8-1h-1c-1 0-2 0-2-1l-4-1 1-1z"></path><defs><linearGradient id="u" x1="310.733" y1="634.142" x2="300.434" y2="649.63" xlink:href="#B"><stop offset="0" stop-color="#919092"></stop><stop offset="1" stop-color="#b3b0b0"></stop></linearGradient></defs><path fill="url(#u)" d="M291 639h2 13c1 0 3 1 4 1 3 1 7 1 9 0v1h6 12 6l-2 1h0c1 1 2 1 4 1h-8-4v1h-2-2-9-1 0v1h-4-1l1 1h4c2 1 4 1 6 2h3l-10 1c-3 0-8-1-12-2l-3-1-10-2c-3 0-6-1-9-2l11 1c-1-1-2-1-2-1-1-1-2 0-3 0v-1c-2 0-2 0-4-1h6-4v-1h3z"></path><path d="M291 639h2l4 1h-5-4v-1h3z" class="Q"></path><path d="M331 644c-4-1-8-1-11-3h5 12 6l-2 1h0c1 1 2 1 4 1h-8-4v1h-2z" class="W"></path><path d="M284 642l11 1c3 0 8 0 12 1h8c2 0 4-1 5 0h-1 0v1h-4-1l1 1c-1 0-3 0-4-1h-12c-2 0-4-1-6-1-3 0-6-1-9-2z" class="i"></path><path d="M293 644c2 0 4 1 6 1h12c1 1 3 1 4 1h4c2 1 4 1 6 2h3l-10 1c-3 0-8-1-12-2l-3-1-10-2z" class="O"></path><path d="M186 607c2 0 5 1 7 1 3 1 5 4 9 4l1 1 1-1c2 0 5-1 7-1h2l-2-1c2 0 4 1 5 1l6 1h4c3-1 7 1 10 0h7 3 2 0 1c1 1 6 1 7 0h0c1-1 3-1 4-1v-1c1 0 1 0 1-1h1 3l2 1 2 2 1 1h-1c-2 1-4 1-6 2l4 1c1 0 2 0 3 1 0 1-1 1-1 1-2 1-4 1-6 1h7c2 1 3 1 5 1l-1 1h-2-3c-1 0-3-1-5-1 1 1 2 1 3 1h-1-1c-2 1-5-1-7 1l-2 1c2 1 4 1 7 1l-1 1h1l-1 1 7 1-1 1-2-1v1h-4 1c1 1 2 1 3 1s2 0 3 1h0 0 0c-1 1-2 0-2 0-2 0-3 0-5 1h-2l-8-1c-4-1-8-3-12-2 2 1 3 1 4 2l9 3 5 3c2 1 3 2 5 2l-2 2c-6-2-11-6-17-8l-15-3c-4-1-8-3-12-5-3-1-7-2-10-4-4-2-7-4-10-6-4-2-8-4-11-7z" class="Y"></path><defs><linearGradient id="v" x1="210.202" y1="619.097" x2="215.268" y2="616.489" xlink:href="#B"><stop offset="0" stop-color="#6c6a6b"></stop><stop offset="1" stop-color="#828282"></stop></linearGradient></defs><path fill="url(#v)" d="M205 616c1 0 2 0 3 1h0 1 6c3 1 5 1 8 2h-3v1h2c-2 0-4-1-7 0-3-1-8-2-10-4z"></path><path d="M229 624l5 1c3 0 10-1 13 1h-9-3l5 2c2 1 3 1 4 2-2 0-6 0-8-1h0 3c-2-2-6-1-9-3 0 0-1-1-1-2z" class="a"></path><path d="M205 616h0-1c-1-1-2-1-3-3 3 0 6 1 8 1h1 0l5 1 7 1c-2 1-4 1-7 1h-6-1 0c-1-1-2-1-3-1z" class="O"></path><path d="M215 620c3-1 5 0 7 0 3 0 5 1 8 1 1 1 2 0 3 0h7v1h0-5v1h3v1h-4 0v1l-5-1-5-1h6v-1c-5 0-10 0-15-2z" class="U"></path><defs><linearGradient id="w" x1="207.84" y1="614.189" x2="212.124" y2="610.381" xlink:href="#B"><stop offset="0" stop-color="#7c7b7a"></stop><stop offset="1" stop-color="#999899"></stop></linearGradient></defs><path fill="url(#w)" d="M211 610c2 0 4 1 5 1 2 2 3 2 5 2h1l1 1h-2l3 2h-2l-7-1-5-1-7-1 1-1c2 0 5-1 7-1h2l-2-1z"></path><path d="M215 615l-1-2h0l7 1 3 2h-2l-7-1z" class="G"></path><defs><linearGradient id="x" x1="241.696" y1="631.189" x2="260.14" y2="624.951" xlink:href="#B"><stop offset="0" stop-color="#8e8b8d"></stop><stop offset="1" stop-color="#b8b7b7"></stop></linearGradient></defs><path fill="url(#x)" d="M243 623h5l7 1c2 1 5 1 7 2l7 1-1 1-2-1v1h-4 1c1 1 2 1 3 1s2 0 3 1h0 0 0c-1 1-2 0-2 0-2 0-3 0-5 1h-2l-8-1c-4-1-8-3-12-2l-5-2h3 9c-3-2-10-1-13-1v-1h0 4v-1h5z"></path><path d="M243 623h5l7 1c2 1 5 1 7 2l7 1-1 1-2-1h-4-2c-3-1-5-1-8-1h-1c-1 1-3 1-4 0-3-2-10-1-13-1v-1h0 4v-1h5z" class="e"></path><path d="M261 609h1 3l2 1 2 2 1 1h-1c-2 1-4 1-6 2l4 1c1 0 2 0 3 1 0 1-1 1-1 1-2 1-4 1-6 1h7c2 1 3 1 5 1l-1 1h-2-3c-1 0-3-1-5-1 1 1 2 1 3 1h-1-1c-2 1-5-1-7 1l-2 1c2 1 4 1 7 1l-1 1h1l-1 1c-2-1-5-1-7-2l-7-1h-5-5-3v-1h5 0v-1h-7c-1 0-2 1-3 0-3 0-5-1-8-1h-2v-1h3c-3-1-5-1-8-2 3 0 5 0 7-1h2l-3-2h2l-1-1h-1c-2 0-3 0-5-2l6 1h4c3-1 7 1 10 0h7 3 2 0 1c1 1 6 1 7 0h0c1-1 3-1 4-1v-1c1 0 1 0 1-1z" class="d"></path><path d="M240 619v-1h4 5c-2 0-3 0-5 1l-1 1h1c-1 1-2 1-3 1h-8l1-1c2-1 5 1 7 0 1 0 1 0 1-1h-1-1z" class="Q"></path><path d="M230 617h5v1c2 1 3 1 5 1h1v1c-4 0-9 0-12-1 1-1 3-1 4-1h1v-1h-3-1z" class="G"></path><defs><linearGradient id="y" x1="221.839" y1="620.348" x2="226.759" y2="614.871" xlink:href="#B"><stop offset="0" stop-color="#7b7578"></stop><stop offset="1" stop-color="#858886"></stop></linearGradient></defs><path fill="url(#y)" d="M222 616h2c2 1 4 1 6 1h1 3v1h-1c-1 0-3 0-4 1h-6c-3-1-5-1-8-2 3 0 5 0 7-1z"></path><path d="M221 614h2c1 0 3 1 4 1 3 0 5 0 7 1s6 0 8 0l5 1 16 2h7c2 1 3 1 5 1l-1 1h-2-3c-1 0-3-1-5-1-5 0-10-1-15-2h-5-4v1c-2 0-3 0-5-1v-1h-5c-2 0-4 0-6-1l-3-2z" class="d"></path><path d="M249 618c5 1 10 2 15 2 1 1 2 1 3 1h-1-1c-2 1-5-1-7 1l-2 1c2 1 4 1 7 1l-1 1h1l-1 1c-2-1-5-1-7-2l-7-1h-5c0-1 0-1-1-1v-1h-1c1 0 2 0 3-1h-1l1-1c2-1 3-1 5-1z" class="L"></path><path d="M244 620l6 1c-1 1-2 1-3 1 0 1 1 1 1 1h-5c0-1 0-1-1-1v-1h-1c1 0 2 0 3-1z" class="E"></path><path d="M250 621c1 0 3 0 4 1l1 1h0v1h0l-7-1s-1 0-1-1c1 0 2 0 3-1zm-34-10l6 1h4c3-1 7 1 10 0 4 1 10 0 15 1-1 1-2 1-3 2 0 0-1 1-1 2l-5-1c-2 0-6 1-8 0s-4-1-7-1c-1 0-3-1-4-1l-1-1h-1c-2 0-3 0-5-2z" class="H"></path><path d="M216 611l6 1c4 0 7 2 11 2 3 0 5 0 7 2h2c-2 0-6 1-8 0s-4-1-7-1c-1 0-3-1-4-1l-1-1h-1c-2 0-3 0-5-2z" class="E"></path><path d="M261 609h1 3l2 1 2 2 1 1h-1c-2 1-4 1-6 2l4 1c1 0 2 0 3 1 0 1-1 1-1 1-2 1-4 1-6 1l-16-2c0-1 1-2 1-2 1-1 2-1 3-2-5-1-11 0-15-1h7 3 2 0 1c1 1 6 1 7 0h0c1-1 3-1 4-1v-1c1 0 1 0 1-1z" class="C"></path><path d="M265 609l2 1 2 2h-3l-1-3z" class="V"></path><path d="M262 609h3l1 3h-2-1c-1 0-1-1-1-1v-2z" class="B"></path><path d="M253 582h1c1 0 1-1 2-2 0-1 1-1 2-1v3c1 1 1 2 1 3l-1 1v1l-2 2h-1c-1 1-2 1-3 2h-1 6v2c1 1 3-1 4 1 1 1 2 1 3 1 2 1 4 1 6 1 1 0 3 1 5 1 1-1 2 0 4-1h2v1c1 0 1-1 2 0h1 6-1v2c1 1 3-1 4 1h0l-3 1h-3v1c-2 0-5 0-8 2-2-1-9-1-11 0-1 0-1 0-1 1-4-1-6-1-9 0h0l-1 1c-1 0-2 1-4 1 3 1 5 1 8 2 0 1 0 1-1 1v1c-1 0-3 0-4 1h0c-1 1-6 1-7 0h-1 0-2-3-7c-3 1-7-1-10 0h-4l-6-1c-1 0-3-1-5-1l2 1h-2c-2 0-5 1-7 1l-1 1-1-1c-4 0-6-3-9-4-2 0-5-1-7-1l-2-2h-2l-1-2-2-1c-1 0-3-1-4-2-3-1-6-3-9-5-1 0-2-1-4-1v-1h8c1 0 3 1 4 0v-1h1c2 0 5 1 6 0h4 2c2-1 4-1 6-1h3c4-1 9-1 13-3 2-1 5-2 7-3l1-1h5c1 1 2 1 3 2l3 1 1-1 2-2 4-1v-1c1 0 1 0 1-1 1 1 1 1 2 1l2 2c1-1 1-1 1-2 1-1 3-1 4 0 1 0 1-1 2-1l1 1h1 3 1z" class="C"></path><path d="M233 606v-1c-3-1-5-2-7-3 4 0 8 1 12 2v1c-2 0-3 0-5 1z" class="R"></path><path d="M187 596h2 7 0c-2 0-3 0-5 1h0 1c2 2 7 1 10 1 2 0 4 0 5 1v1c-2 0-5 0-7 1h-4l1 1 1-1s1 0 1 1h8l12 1h0c-3 0-20 0-21 1h-1-3c-4 0-9-1-13-1l-2-1c1-1 2-1 3-1v-1l2-1h-4c1-1 1-1 2-1l1-1v-1h4z" class="E"></path><path d="M187 596h2l-7 1c2 0 5 1 7 2s4 1 6 1l-6 1v1h2-3l-1-1v-1h0c-1 0-2 0-3-1h-4c1-1 1-1 2-1l1-1v-1h4z" class="l"></path><path d="M184 599c1 1 2 1 3 1h0v1l1 1h3l6 1h0v1h-3c-4 0-9-1-13-1l-2-1c1-1 2-1 3-1v-1l2-1z" class="W"></path><path d="M184 599c1 1 2 1 3 1h0v1l1 1c-2 0-4-1-6-1v-1l2-1z" class="h"></path><defs><linearGradient id="z" x1="165.287" y1="592.913" x2="192.57" y2="595.912" xlink:href="#B"><stop offset="0" stop-color="#191718"></stop><stop offset="1" stop-color="#383738"></stop></linearGradient></defs><path fill="url(#z)" d="M185 592h2c2-1 4-1 6-1 3 0 6 0 9 1-2 0-3 0-5 1v1h0l2 1h0c-1 1-2 1-3 1h-7-2-4v1l-1 1c-1 0-1 0-2 1h4l-2 1v1c-1 0-2 0-3 1-1 0-3-1-4-2-3-1-6-3-9-5-1 0-2-1-4-1v-1h8c1 0 3 1 4 0v-1h1c2 0 5 1 6 0h4z"></path><path d="M193 594h4l2 1h0c-1 1-2 1-3 1h-7-2c2-1 4 0 6-1v-1z" class="n"></path><path d="M181 592h4l-3 1h0c1 0 2 1 3 1l-11-1v-1h1c2 0 5 1 6 0z" class="S"></path><path d="M185 592h2c2-1 4-1 6-1 3 0 6 0 9 1-2 0-3 0-5 1v1h0-4-8c-1 0-2-1-3-1h0l3-1z" class="E"></path><path d="M166 595h9 5l3 1v1l-1 1c-1 0-1 0-2 1h4l-2 1v1c-1 0-2 0-3 1-1 0-3-1-4-2-3-1-6-3-9-5z" class="j"></path><path d="M180 595l3 1v1l-1 1c-1 0-1 0-2 1l-4-1v-1c1 0 2-1 4-2z" class="T"></path><path d="M222 584c1 1 2 1 3 2l3 1s0 1 1 1c0 1 1 2 1 2v1h-2-4c1 0 2 0 2 1l1 1 6-1c2 0 5 0 7 1h0c-1 0-1 1-1 1-1 0-3 1-4 0l-5 2h-4l-5-1h-3c-4-1-10-1-14 0-2 1-5 1-8 1h0c1 0 2 0 3-1h0l-2-1h0v-1c2-1 3-1 5-1-3-1-6-1-9-1h3c4-1 9-1 13-3 2-1 5-2 7-3l1-1h5z" class="R"></path><path d="M197 594v-1c2-1 3-1 5-1 1 1 2 1 2 2-1 1-5 0-7 0z" class="C"></path><path d="M226 596c-1-1 0-1-1-1 2-1 4-1 6-1h4l-5 2h-4z" class="f"></path><path d="M207 594h-2v-2-1l2-1h1 4c-1 1 0 1-1 1-1 1 0 1-1 1l-4 1 1 1h0z" class="o"></path><path d="M212 590h2c1 1 0 1 2 1h1l1 1 6-1c1 0 2 0 2 1l1 1c-4 0-7 1-11 1h-9 0l-1-1 4-1c1 0 0 0 1-1 1 0 0 0 1-1z" class="W"></path><path d="M212 590h2c1 1 0 1 2 1h1c-3 1-4 1-7 1 1 0 0 0 1-1 1 0 0 0 1-1z" class="G"></path><path d="M222 584c1 1 2 1 3 2l3 1s0 1 1 1c0 1 1 2 1 2v1h-2-4l-6 1-1-1h-1c-2 0-1 0-2-1h-2c-1-1-2-1-3-2 2-1 5-2 7-3l1-1h5z" class="D"></path><path d="M215 590c0-1 1-2 1-2 1-1 2-1 3-2h1 0c1 1 0 1 1 1-1 1-1 1-2 1h0l-4 2z" class="B"></path><path d="M222 584c1 1 2 1 3 2h0c-1 0-2 1-3 1-1 1-2 1-3 2v-1h0c1 0 1 0 2-1-1 0 0 0-1-1h0v-1l-3-1h5z" class="Q"></path><path d="M225 586l3 1s0 1 1 1c0 1 1 2 1 2v1h-2-4l-6 1-1-1h-1l-1-1 4-2v1c1-1 2-1 3-2 1 0 2-1 3-1h0z" class="S"></path><path d="M219 588v1c1-1 2-1 3-2 1 1 1 1 2 1v1h-3c-1 0-1 0-2 1 0 1 0 1-1 1v1l-1-1h-1l-1-1 4-2z" class="F"></path><path d="M225 586l3 1s0 1 1 1c0 1 1 2 1 2v1h-2c-1-1-1-1-2-1h0-1c0-1 0-2 1-3l-1-1h0z" class="R"></path><path d="M253 582h1c1 0 1-1 2-2 0-1 1-1 2-1v3c1 1 1 2 1 3l-1 1v1l-2 2h-1c-1 1-2 1-3 2h-1 0c-1 1-3 1-5 1s-4 0-6 1c-2-1-5-1-7-1l-6 1-1-1c0-1-1-1-2-1h4 2v-1s-1-1-1-2c-1 0-1-1-1-1l1-1 2-2 4-1v-1c1 0 1 0 1-1 1 1 1 1 2 1l2 2c1-1 1-1 1-2 1-1 3-1 4 0 1 0 1-1 2-1l1 1h1 3 1z" class="J"></path><path d="M244 585h2c1 1 1 2 1 3h-1c-1 0-2 0-3-1 0-1 0-1 1-2z" class="H"></path><path d="M241 582c1-1 3-1 4 0 1 0 1-1 2-1l1 1h-2v1c1 1 3 1 5 1h-2c-2 1-9 1-11 0-1-1-2-1-3-1h0v-1c1 0 1 0 1-1 1 1 1 1 2 1l2 2c1-1 1-1 1-2zm-12 4l1 1 4-2h0c-1 1-1 2-3 2v1c1 0 2 0 3-1 1 1 1 1 2 1l1-1h1v1c-1 1-3 2-5 2h0v2l-6 1-1-1c0-1-1-1-2-1h4 2v-1s-1-1-1-2c-1 0-1-1-1-1l1-1z" class="X"></path><path d="M237 587c1-1 1-1 2-1v1 1c2 0 3 0 4 1v1h1 2c1-1 2-1 3-1l-1 1c-1 0-2 1-3 1h0 6c-1 1-3 1-5 1s-4 0-6 1c-2-1-5-1-7-1v-2h0c2 0 4-1 5-2v-1h-1z" class="a"></path><path d="M237 587c1-1 1-1 2-1v1 1c2 0 3 0 4 1v1h1c-1 1-2 1-3 1-3 0-6 0-8-1h0c2 0 4-1 5-2v-1h-1z" class="F"></path><path d="M253 582h1c1 0 1-1 2-2 0-1 1-1 2-1v3c1 1 1 2 1 3l-1 1v1l-2 2h-1c-1 1-2 1-3 2h-1 0-6 0c1 0 2-1 3-1l1-1h1c1-1 2-1 3-2v-2c-1-1-1-1-2-1-2 0-4 0-5-1v-1h2 1 3 1z" class="b"></path><path d="M258 582c1 1 1 2 1 3l-1 1c-1-1-1-2-3-2l1-2h2z" class="a"></path><path d="M253 582h1c1 0 1-1 2-2 0-1 1-1 2-1v3h-2l-1 2-2-2z" class="P"></path><defs><linearGradient id="AA" x1="225.959" y1="604.463" x2="226.447" y2="613.961" xlink:href="#B"><stop offset="0" stop-color="#cecdcd"></stop><stop offset="1" stop-color="#f4f2f3"></stop></linearGradient></defs><path fill="url(#AA)" d="M181 603c4 0 9 1 13 1h0-3c-1 1-3 1-5 0h-2-1 0 1 1 0 1c1 1 2 1 3 1h1 4c2 1 4 1 6 1 5 0 10 0 14 1 2 0 4 1 6 1h3c2 1 3 0 5 1l5-1 2-1h0l-2-1c2-1 3-1 5-1v-1h1 4 0 4c3 0 5 0 8 1l2 1c-1 0-2 1-4 1 3 1 5 1 8 2 0 1 0 1-1 1v1c-1 0-3 0-4 1h0c-1 1-6 1-7 0h-1 0-2-3-7c-3 1-7-1-10 0h-4l-6-1c-1 0-3-1-5-1l2 1h-2c-2 0-5 1-7 1l-1 1-1-1c-4 0-6-3-9-4-2 0-5-1-7-1l-2-2h-2l-1-2z"></path><path d="M184 605c3-1 7 1 10 1h-2c0 1 1 1 1 1 1 0 2 0 3 1h0 3-6c-2 0-5-1-7-1l-2-2z" class="Q"></path><path d="M247 604c3 0 5 0 8 1l2 1c-1 0-2 1-4 1-3-1-8 0-10-3h0 4z" class="O"></path><defs><linearGradient id="AB" x1="197.27" y1="608.641" x2="209.564" y2="611.861" xlink:href="#B"><stop offset="0" stop-color="#565455"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#AB)" d="M193 608h6l12 2 2 1h-2c-2 0-5 1-7 1l-1 1-1-1c-4 0-6-3-9-4z"></path><path d="M238 604h1c2 1 3 2 4 3l4 1c1 0 2 1 3 1v1h-14c-2-1-4 0-6-1h-2 0l5-1 2-1h0l-2-1c2-1 3-1 5-1v-1z" class="d"></path><path d="M243 607l4 1-2 2c-1 0-2 0-2-1v-2z" class="O"></path><path d="M233 608h3c1-1 1-1 2-1v2c-2 1-6 0-8 0h-2 0l5-1z" class="e"></path><path d="M251 591h6v2c1 1 3-1 4 1 1 1 2 1 3 1 2 1 4 1 6 1 1 0 3 1 5 1 1-1 2 0 4-1h2v1c1 0 1-1 2 0h1 6-1v2c1 1 3-1 4 1h0l-3 1h-3v1c-2 0-5 0-8 2-2-1-9-1-11 0-1 0-1 0-1 1-4-1-6-1-9 0h0l-1 1-2-1c-3-1-5-1-8-1h-4 2v-1c-2 0-3 0-4-1s-1-2-2-2v-2h-1l-3-1c-2 0-4 0-5-1l5-2c1 1 3 0 4 0 0 0 0-1 1-1h0c2-1 4-1 6-1s4 0 5-1h0z" class="Z"></path><path d="M246 595h2l2 1h0c-2 1-4 1-5 1h-4v-1c2 0 3 0 5-1z" class="T"></path><path d="M238 598c1 0 4-1 5 0 0 2-1 3-2 4-1-1-1-2-2-2v-2h-1z" class="L"></path><path d="M245 604h1v-1s-1 0-2-1l1-1c1 0 2 0 3 1h0c1 1 2 0 3 0 0 1 0 1-1 2h-3-4 2z" class="E"></path><path d="M240 593c2-1 4-1 6-1s3 0 4 1c2 0 3 1 5 1 1 1 2 1 3 1v1c-2 1-5 0-7 1h-1v-1l-2-1h-2c-2 1-3 1-5 1h-1c-2 0-3 0-5 1-2 0-4 0-5-1l5-2c1 1 3 0 4 0 0 0 0-1 1-1h0z" class="B"></path><path d="M246 595h-1c-1-1-3-1-4-2 2-1 7 0 10 0v1s0 1-1 1h-2-2z" class="C"></path><path d="M275 597c1-1 2 0 4-1h2v1c1 0 1-1 2 0h1 6-1v2c1 1 3-1 4 1h0l-3 1h-3l-12-1-15-2c-3 0-5 0-8-1l18 1h5v-1h0z" class="O"></path><path d="M275 597c1 0 2 0 2 1h0-7 5v-1z" class="E"></path><path d="M275 597c1-1 2 0 4-1h2v1c1 0 1-1 2 0h1l-3 1c1 0 2 0 3 1h0c-3 0-5 0-7-1h0c0-1-1-1-2-1h0z" class="J"></path><path d="M260 598l15 2 12 1v1c-2 0-5 0-8 2-2-1-9-1-11 0-1 0-1 0-1 1-4-1-6-1-9 0h0l-1 1-2-1c-3-1-5-1-8-1h3c1-1 1-1 1-2h1v-1c2-1 3-1 4 0h3 0 0l1-1-1-1 1-1z" class="F"></path><path d="M260 598l15 2h2c-1 1-1 1-2 1-4 0-10-1-12 2h-3v-1h2v-1h-3 0l1-1-1-1 1-1z" class="Q"></path><path d="M204 541l15-1h0c3 1 6 1 9 1 2 0 4-1 5 0v1c2 0 4 0 5 1v1l1-1c1 0 2 0 3 1h0v1 1h4c1 0 4 1 5 0 2 0 5 0 7 1v1h0v1 1 2 1l-2 1c1 1 2 0 2 1v1 1 3 6 3 4h0v2 4h0c-1 0-2 0-2 1-1 1-1 2-2 2h-1-1-3-1l-1-1c-1 0-1 1-2 1-1-1-3-1-4 0 0 1 0 1-1 2l-2-2c-1 0-1 0-2-1 0 1 0 1-1 1v1l-4 1-2 2-1 1-3-1c-1-1-2-1-3-2h-5l-1 1c-2 1-5 2-7 3-4 2-9 2-13 3-1-1-1-1-2-1v-1h3l1-1c-1-1-1-1-2-1v-1-2-1h1l1 2c1 0 2 1 3 1v-4-13-4c0-4 1-8 0-12v-7l-1-5h1 3z" class="F"></path><path d="M220 545l1-1c1 0 1 1 1 1 0 1 0 1 1 1v1h-6c1-1 2-1 3-2z" class="h"></path><path d="M252 548c2 0 5-1 6 0v1h-6v-1z" class="L"></path><path d="M247 549c0-1 0-1 1-1 1-1 3-1 4 0v1c-1 1-3 1-5 0z" class="C"></path><path d="M226 548h1v3h1c1 0 1 0 2 1v1l-1 1-2-1h0c0-1 0-1-1-2h-1v-1s1-1 1-2z" class="a"></path><path d="M218 549c3-1 5-2 8-1 0 1-1 2-1 2h-7v-1z" class="B"></path><path d="M238 548h6c2 0 2 0 2 1v1h-9c-3 0-6 1-9-1v-1h10z" class="C"></path><path d="M230 552v-1h10c2 0 4 0 6 1h0c-1 1-4 1-5 2-2-1-2-1-3 0s-1 1-1 2v1l-4-4c-1 0-2 0-3-1h0z" class="B"></path><path d="M233 542c2 0 4 0 5 1v1l1-1c1 0 2 0 3 1h0v1 1h4c-2 0-7 0-8 1h0c-4-1-8-1-11 0h-4v-1c-1 0-1 0-1-1h1l2-2c2 0 3 0 5 1 1 0 1-1 2-1s1-1 1-1z" class="a"></path><path d="M239 543c1 0 2 0 3 1h0v1h-1-3v-1l1-1z" class="i"></path><path d="M233 542c2 0 4 0 5 1v1 1h-1c-2 0-4-1-5-2 1 0 1-1 1-1z" class="Y"></path><path d="M206 553h-3v-1l1-1c0-1-1-1-1-1v-2c2-1 3-1 5-1h0c1 0 2 1 2 1 2 1 5-1 7 0l1 1v1h7v1h1c1 1 1 1 1 2l1 2c-1 1-2 0-3 0h-3c-1 1-2 1-3 1h-1c-1 0-1 0-1-1h0v-1-1h-6-1l-1-1h-1c0 1 0 1 1 2l-1 1c0-1 0-1-1-2h-1z" class="J"></path><path d="M209 552c1-1 2-1 3 0 3 0 7-1 10-1v1c-1 1-3 2-5 3v-1-1h-6-1l-1-1z" class="D"></path><path d="M208 547h0c1 0 2 1 2 1 2 1 5-1 7 0l1 1v1h-4-2 0-6v-1l2-2z" class="B"></path><path d="M217 548l1 1v1h-4c1 0 0 0 1-1h0c1-1 1-1 2-1z" class="C"></path><path d="M225 551h1c1 1 1 1 1 2l1 2c-1 1-2 0-3 0h-3c-1 1-2 1-3 1h-1c-1 0-1 0-1-1h0c2-1 4-2 5-3h1c0-1 1-1 2-1z" class="m"></path><path d="M225 551h1c1 1 1 1 1 2l1 2c-1 1-2 0-3 0 0-2-1-2-2-3 0-1 1-1 2-1z" class="W"></path><path d="M252 549h6v1 2 1l-2 1c1 1 2 0 2 1v1 1h-8l-2 1-1 1 1 1h-1 0-5-3 0c-1-1-1-2-2-3h0v-1c0-1 0-1 1-2s1-1 3 0h0 2 4v-3h0v-2c2 1 4 1 5 0z" class="J"></path><path d="M239 560c0-1 1-1 1-2 2 0 4 1 6 1l1 1h-5-3 0z" class="H"></path><path d="M243 554c1 0 2 0 3 1v1 1c-2 0-6 1-7 0l-1-1c1-1 2-1 3-2h2z" class="M"></path><path d="M252 549h6v1 2 1l-2 1c1 1 2 0 2 1v1 1h-8s-1 0-1-1h-1v-1c1-1 5-1 6-1h0v-1c-2 0-5 1-6 0l-1-2v-2c2 1 4 1 5 0z" class="C"></path><path d="M252 549h6v1 2 1-2c-1 0-3-1-4 0h-7v-2c2 1 4 1 5 0z" class="d"></path><path d="M258 557v3 6 3h-3c-2 0-3-1-5-1h-6-3v4h-1v-3-2-4l-1-3h3 5 0 1l-1-1 1-1 2-1h8z" class="F"></path><path d="M239 560h3v2h-2v1l-1-3z" class="G"></path><path d="M254 566h0c1-1 0-1 0-2 1-1 2-1 3-1h1v3c-1 1-2 1-2 1h-2v-1z" class="J"></path><path d="M258 557v3c-3 0-7-1-10 0l-1-1 1-1 2-1h8z" class="I"></path><path d="M240 562h6l2 1c1 0 3 0 5 1h1v2 1h2s1 0 2-1h0v3h-3c-2 0-3-1-5-1h-6-3v4h-1v-3-2-4-1z" class="Z"></path><path d="M242 565c0-1 0-2 1-2h2c1 0 2 1 2 1v3h-2 0c-2-1-2-1-3 0l-1-1 1-1z" class="B"></path><path d="M242 565c0-1 0-2 1-2h2v3h-1c-1 0-1-1-2-1z" class="I"></path><path d="M253 564h1v2 1h2s1 0 2-1h0v3h-3c-2 0-3-1-5-1h-6-3v4h-1v-3-2h5 0 1c2 1 5 1 7 0v-1l-1-1c-1 0-1 1-2 1l-1-1c1-1 3-1 4-1z" class="R"></path><path d="M241 572v-4h3 6c2 0 3 1 5 1h3v4h0v2 4h0c-1 0-2 0-2 1-1 1-1 2-2 2h-1-1-3-1l-1-1c-1 0-1 1-2 1-1-1-3-1-4 0v-2c-1-1-1-3 0-4-1-2-1-3-1-4h1z" class="d"></path><path d="M258 569v3l-1 1-2-1h0 1v-2l-1-1h0 3z" class="B"></path><path d="M252 576c2-1 4-1 5 0h-1 0c-1 1-1 2-2 3h0-1l-2-2 1-1z" class="e"></path><path d="M252 576h2v1h-1l-1-1z" class="S"></path><path d="M258 573h0v2 4h0c-1 0-2 0-2 1-1 1-1 2-2 2h-1-1v-1c1 0 2-1 3-1l-1-1h0c1-1 1-2 2-3h0 1l1-1s0-1-1-1v-1h1z" class="i"></path><path d="M244 572h4 0c1-2 1-2 2-3l3 3h1v1c0 1 0 1-1 1l-1-1h-2c0 1-1 0-2 0-1 1-1 1-2 1l-1-1c-1 0-3 0-4 1 0 1 1 0 1 1s0 1-1 1c-1-2-1-3-1-4h1 3z" class="m"></path><path d="M241 572v-4h3 6c2 0 3 1 5 1h0l1 1v2h-1 0-1-1l-3-3c-1 1-1 1-2 3h0-4-3z" class="D"></path><path d="M255 569l1 1v2h-1-2c1-2 1-2 2-3z" class="M"></path><path d="M241 572v-4h3c1 1 2 1 3 2v1c-1 1-2 1-3 1h-3z" class="N"></path><path d="M241 576v1c1 0 2 1 3 1 0-1 1-2 1-3v-1l2 1h1c1 0 2 0 2 1l1 1 2 2h1l1 1c-1 0-2 1-3 1v1h-3-1l-1-1c-1 0-1 1-2 1-1-1-3-1-4 0v-2c-1-1-1-3 0-4z" class="U"></path><path d="M254 579l1 1c-1 0-2 1-3 1v1h-3l1-1c1-1 2-1 3-2h1z" class="m"></path><path d="M247 575h1c1 0 2 0 2 1v1c-2 0-2 1-3 2h-1l-1-1c1-1 1-2 2-3z" class="G"></path><path d="M248 575c1 0 2 0 2 1v1h-2v-2z" class="R"></path><defs><linearGradient id="AC" x1="213.113" y1="538.79" x2="219.706" y2="568.038" xlink:href="#B"><stop offset="0" stop-color="#272626"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#AC)" d="M204 541l15-1h0c3 1 6 1 9 1 2 0 4-1 5 0v1s0 1-1 1-1 1-2 1c-2-1-3-1-5-1l-2 2h-1s0-1-1-1l-1 1c-1 1-2 1-3 2h-5-3-1 0c-2 0-3 0-5 1v2s1 0 1 1l-1 1v1h3 1c1 1 1 1 1 2l1-1c-1-1-1-1-1-2h1l1 1h1 6v1 1h0c0 1 0 1 1 1h1l-2 2c-1 0-1 0-2 1-1 2-2 5-2 8v1c0 3-1 6 0 9l-1 2v2l1-1h0c1 1 0 2 1 3l2-1v3c-2 1-5 2-7 3-4 2-9 2-13 3-1-1-1-1-2-1v-1h3l1-1c-1-1-1-1-2-1v-1-2-1h1l1 2c1 0 2 1 3 1v-4-13-4c0-4 1-8 0-12v-7l-1-5h1 3z"></path><path d="M209 547c1-1 1-1 1-2s1-1 1-2c1 1 1 1 2 1h1c0 2-1 2-2 3h-3z" class="c"></path><path d="M214 544h2c1 0 1-1 2-1 0 0 1 0 1 1v1h1c-1 1-2 1-3 2h-5c1-1 2-1 2-3z" class="n"></path><path d="M203 560h2v1c0 1 0 1-1 2h0v1h0v1c2 1 4 1 6 1v1l-2 2-1 1h0-3l-1-1v-1c1-1 0-1 0-2-1-1 0-3 0-5h-1l1-1z" class="S"></path><path d="M203 569h1c1 0 1-1 2-1 0 1 0 1 1 2h-3l-1-1z" class="e"></path><path d="M201 569c0 1 0 2 1 2v1l1-1c1 0 3 1 4 0h-1c-1 0-3-1-4-1h0 2 3 0l1 1h2 1c0 1-1 1-1 1-2 1-3 0-4 2h0-2v4l-1-1v2-1h-1c0 1 0 3-1 4v-13z" class="a"></path><path d="M203 577v-5h7c-2 1-3 0-4 2h0-2v4l-1-1z" class="S"></path><path d="M210 567l1 1h1 1c0 3-1 6 0 9l-1 2h-1s0 1-1 2h0v1l-2 2h-3v-6h-1v-4h2 0c1-2 2-1 4-2 0 0 1 0 1-1h-1-2l-1-1 1-1 2-2z" class="G"></path><path d="M206 574h2v1c0 1 0 1-1 1h-2v-1l1-1h0z" class="d"></path><path d="M211 579h0c-1-1-1-1-2-1v-2h1l3 1-1 2h-1z" class="F"></path><path d="M204 578v-4h2l-1 1v1c1 1 1 1 1 2v1 3h1 1c1-1 1-1 2-1v1l-2 2h-3v-6h-1z" class="e"></path><path d="M203 577l1 1h1v6h3l2-2v-1h0c1-1 1-2 1-2h1v2l1-1h0c1 1 0 2 1 3l2-1v3c-2 1-5 2-7 3-4 2-9 2-13 3-1-1-1-1-2-1v-1h3l1-1c-1-1-1-1-2-1v-1-2-1h1l1 2c1 0 2 1 3 1v-4c1-1 1-3 1-4h1v1-2z" class="Y"></path><path d="M203 577l1 1h1v6h3l2-2v-1h0c1-1 1-2 1-2h1v2l1 1c-1 1-1 1-2 1l-1-1v1 2c-1 1-1 1-2 0h0c-1 1-2 1-3 1s-1-1-1-2v-3c-1-1-1-1-1-2v-2z" class="h"></path><path d="M208 555l1-1c-1-1-1-1-1-2h1l1 1h1 6v1 1h0c0 1 0 1 1 1h1l-2 2c-1 0-1 0-2 1-1 2-2 5-2 8v1h-1-1l-1-1v-1c-2 0-4 0-6-1v-1h0v-1h0c1-1 1-1 1-2v-1h-2v-5c1-1 2-1 3-1v-1h1c1 1 1 1 1 2z" class="V"></path><path d="M213 556v-1c1-1 3-1 4-1v1h0l-1 2c-1-1-2-1-3-1z" class="B"></path><path d="M206 553h1c1 1 1 1 1 2h-2c1 0 1 0 2 1h0v2l1 1h-1c-1 0-2 0-2 1l-2-1c0 1 0 1 1 1h-2v-5c1-1 2-1 3-1v-1z" class="G"></path><path d="M208 556c-1 1-2 1-3 2v-1-1l1-1c1 0 1 0 2 1z" class="H"></path><path d="M217 555c0 1 0 1 1 1h1l-2 2c-1 0-1 0-2 1-1 2-2 5-2 8v1h-1-1l-1-1v-1c-2 0-4 0-6-1v-1h0v-1l2 1c1-1 1-1 2-3h3l1 1c-1 0-1 0-2 1h1 2 0v-1-2l-2-1h0 1c1 0 1 0 2-1h0l-2-1v1-3l1 1c1 0 2 0 3 1l1-2z" class="F"></path><path d="M210 566v-1l1-1 1 1v3h-1l-1-1v-1z" class="Q"></path><path d="M230 552h0c1 1 2 1 3 1l4 4h0c1 1 1 2 2 3h0l1 3v4 2 3c0 1 0 2 1 4-1 1-1 3 0 4v2c0 1 0 1-1 2l-2-2c-1 0-1 0-2-1 0 1 0 1-1 1v1l-4 1-2 2-1 1-3-1c-1-1-2-1-3-2h-5l-1 1v-3l-2 1c-1-1 0-2-1-3h0l-1 1v-2l1-2c-1-3 0-6 0-9v-1c0-3 1-6 2-8 1-1 1-1 2-1l2-2c1 0 2 0 3-1h3c1 0 2 1 3 0l-1-2h0l2 1 1-1v-1z" class="r"></path><path d="M220 579l1-2v6l-1-1v-3z" class="i"></path><path d="M231 584v-3h0c1 1 2 1 3 1h1v1l-4 1z" class="b"></path><path d="M220 572l1 1v4l-1 2v-2-1c-1-1-1-2 0-4z" class="P"></path><path d="M232 568v5c0 2 1 4 0 6h-1c0-2 1-6 0-8h-2-1 0l1-1c1-1 1 0 2 0v-1l1-1z" class="b"></path><path d="M222 558v1h0c-2 3-1 10-1 14l-1-1c-1-3 0-5 0-8 0-1 0-3 1-4v-2h0 1z" class="O"></path><path d="M225 562l1 1v6 5 2-2h-2l-1 3c0-4 0-10 2-15z" class="m"></path><path d="M228 571h1 2c1 2 0 6 0 8-1 0-1 1-2 1h-2v-4h1c1 0 1 0 2-1h-3v-3l1-1z" class="l"></path><path d="M223 577l1-3h2v2-2h1v2h0v4h2c-1 2-2 2-3 3h-2c-1-1-1-4-1-6z" class="U"></path><path d="M226 574h1v2h0v4h2c-1 2-2 2-3 3v-5-2-2z" class="c"></path><path d="M239 569s0-1 1-2h0v2 3c0 1 0 2 1 4-1 1-1 3 0 4v2c0 1 0 1-1 2l-2-2c-1 0-1 0-2-1v-12h3 0z" class="O"></path><path d="M239 569s0-1 1-2h0v2 3c0 1 0 2 1 4-1 1-1 3 0 4v2c0 1 0 1-1 2l-2-2c0-1 0-2 1-3h0v-2-8z" class="b"></path><path d="M222 555v3h-1 0v2c-1 1-1 3-1 4 0 3-1 5 0 8-1 2-1 3 0 4v1h-1v-1l-1-1v-3h-1v-6c-1 0 0-2-1-2 0-1-1-1-1-1v-1c2-1 1-3 2-4l2-2c1 0 2 0 3-1z" class="a"></path><path d="M217 566c0-1 0-3 1-4 0-2 0-2 1-3v4c-1 3-1 6-1 9h-1v-6z" class="P"></path><path d="M220 564c0 3-1 5 0 8-1 2-1 3 0 4v1h-1v-1l-1-1v-3c0-3 0-6 1-9l1 1z" class="h"></path><path d="M225 562c0-1 1-2 2-3 1 1 2 1 3 2s1 2 1 4v1s1 1 1 2l-1 1v1c-1 0-1-1-2 0l-1 1h0l-1 1v3h3c-1 1-1 1-2 1h-1 0v-2h-1v-5-6l-1-1z" class="f"></path><path d="M228 564l-1-1c0-1-1-1 0-2h1l1 1 1 1-1 1h-1z" class="K"></path><path d="M230 561c1 1 1 2 1 4v1c0-1-1-1-2-1s0 1-2 1h0v-1l1-1h1l1-1-1-1 1-1z" class="h"></path><path d="M227 566h0c2 0 1-1 2-1s2 0 2 1c0 0 1 1 1 2l-1 1v1c-1 0-1-1-2 0l-1 1-1-1v-1c1-1 1-1 1-2l-1-1zm3-14h0c1 1 2 1 3 1l4 4h0c1 1 1 2 2 3h0l1 3v4 2-2h0c-1 1-1 2-1 2h0-3c0-2 0-5-1-7 0-3-2-4-5-6l-2-1-1-2h0l2 1 1-1v-1z" class="n"></path><path d="M231 556c2 1 5 4 6 7v1c0 1 0 2 1 3 0 1 1 1 1 2h-3c0-2 0-5-1-7 0-3-2-4-5-6h1z" class="m"></path><path d="M230 552h0c1 1 2 1 3 1l4 4h0v2h0c-2-1-4-3-6-4h0v1h-1l-2-1-1-2h0l2 1 1-1v-1zm-17 15c0-3 1-6 2-8 1-1 1-1 2-1-1 1 0 3-2 4v1s1 0 1 1c1 0 0 2 1 2v6h1v3l1 1v1h1v2 3l1 1-1 1h2-5l-1 1v-3l-2 1c-1-1 0-2-1-3h0l-1 1v-2l1-2c-1-3 0-6 0-9v-1z" class="O"></path><path d="M218 572v3l1 1v1h1v2 3c-1-1-1-2-1-2-1 0-1 0-2 1l-1-1v-4c1-2 0-3 1-4h1z" class="l"></path><path d="M213 567c1-1 1-2 2-3 0 3 0 7-1 9v9h1c1-1 1-1 2-1h0c1-1 1-1 2-1 0 0 0 1 1 2l1 1-1 1h2-5l-1 1v-3l-2 1c-1-1 0-2-1-3h0l-1 1v-2l1-2c-1-3 0-6 0-9v-1z" class="g"></path><path d="M425 546h7s1 0 2 1c0 1 1 1 2 1h1v2 2l1 2v-3c1-1 1-1 3-2v3h0c0-1 1-1 1-2 1 2 0 4 1 6 1 0 1 0 1 1 1-2 2-2 3-3s2-1 4-2c1-1 3-1 5-2h2c1 1 2 1 3 2l1 2h1c1 1 2 2 3 2h0c1 0 1-1 2-1v1h3c0 1 1 2 2 2h1v-1h2l2-2h1 2l1 1c0 1 0 1-1 2h1 1l2-2c0-1 0-1-1-2v-2h2 1c-1 0-1-1-1-2 1-1 2-1 3-1l5-1h6 1c0 2-1 2-2 3l-2 1 2 2c-1 0-3 1-4 2v1c-2 1-3 2-4 3l-1 3 1 1 1-1c0 1-1 1 0 2 1 0 1 0 2-1h2l-1 1 1 1h2c1 0 1 0 1 1 0 2 0 2-1 3h0c2 0 2 0 3-1h4c-1 0-1 0-1 1v1c2 1 5 3 6 4l2 2 1-2h1c2 0 3-1 5-2h2l1-1c2 0 3-1 5-2h0c0 2-2 2-2 3h0c2 0 2 0 3-1 0-1 1-1 1-1l-4 4-1 1c2 0 4 1 6 0l1 1c-1 0-1 1-1 1h2 2c1 0 2-1 4-2h2v-1c1 0 1-1 2-1 1-1 1-1 1-2 1 1 1 2 1 3v1c0 1 0 1 1 1-2 1-3 2-5 3s-3 2-5 3v1c-6 3-13 5-20 7-5 2-9 4-14 5-3 0-6 1-9 1l-17 3c-1 0-2 0-3-1h-5-2-2v1s-1 1-2 1h-1c-1 0-2 1-3 1h0c-1 0-2 1-4 1s-5-1-7 0h-2c-1-1-3-1-4-2h-9-1-1c-4-2-7-2-11-2-7 0-13 1-19 0l-4-1c-1 0-2 0-2-1h-3c-1 0-1 0-2-1v-1h-1c-3-1-6-1-9-1-2-1-3-1-4-2-3 2-7 1-10 1s-5 1-8 1-7-1-10 0h-2v-2l-5 1c0-1 1-1 2-2 2-1 2-1 2-2l1-1-1-1c-2 1-4 3-6 4v-1l1-1v-1l-1 1h-1c0-1 1-1 1-2h-2v-1h0c-1 0-2 1-3 1l1-2c-1 0-1-1-1-2h0l-1-1h-1-2v-1c0-1 1-2 2-3 0-1 1-1 2-2 0-1 0-2 1-2v-1-1c-2 1-4 1-7 2h0-1c0-2 1-2 2-3v-3h0v-2c0-1 0 0 1-1h0c2 0 3-1 4-2 1 0 1 1 2 0l1-1c1 0 2-1 3-1 2 0 2 0 4-1h0 2v1 1h1 0c2 0 3-1 4-1h0 1c1 0 1 0 2-1s2-2 4-3c1 0 2-1 3 0l2 2v1h0v-1c0-1 0-1-1-2h0v-1c1 0 1 0 2 1l1 1v-1h2v-1h1c0-1 0-1 1-1 1-1 2-1 2-1 1-1 1-1 1-2l-1-1c0-1 0-1 1-2l1-1h2v1c2 1 5 1 7 1h0l5-1c4 1 8 1 12 1l7-2c4-2 18 0 21-2z" class="r"></path><path d="M466 575c0-3 1-3 3-5h0l1 1v1l-4 3z" class="W"></path><path d="M443 567l-1 1-1-1c1-1 2-3 3-4 0 1 1 2 2 2h0c-1 1-2 1-3 2z" class="R"></path><path d="M391 593c2 0 4 1 6 1h0l-6 1h-2v-1c1 0 1 0 2-1h0z" class="D"></path><path d="M437 593l1-1c2-1 7 0 10 0l2 1h-4l-1 1h-2c-2 0-4-1-6-1z" class="d"></path><path d="M443 594v-1h3l-1 1h-2z" class="J"></path><path d="M443 588v1h1v-1c1 0 2-1 4-1 1 0 1 0 2 1l-2 1c-2 1-5 1-7 1l-1-1v-1h3z" class="O"></path><path d="M445 577l3-2c0 1 0 1-1 3l1 1c-1 1-3 1-4 2v-1c0-1 0-2 1-3z" class="e"></path><path d="M449 559c0 2-1 3 0 4-1 0-2 1-3 2-1 0-2-1-2-2 1-1 2-1 3-2 1 0 1-1 2-2z" class="J"></path><path d="M459 572h2 1v1l1 1c-2 2-5 2-7 3h-1l1-1c1 0 2-2 3-3v-1z" class="D"></path><path d="M470 572c1 0 1-1 1-2 1 0 2-1 3-1 0 1 0 1 1 2v1c-4 1-6 3-10 4h0l1-1 4-3z" class="m"></path><path d="M397 594c2 0 4 0 6-1 3 0 8 0 11 1h-4 0c1 1 2 1 3 2h-1c-1 0-3 0-4-1h-2c-1 0-1-1-2 0l-7-1h0z" class="P"></path><path d="M424 589c2 0 6-2 8-2l1 1c-3 1-7 3-10 4h-1-2l3-3h1z" class="F"></path><path d="M414 594h5v2 2c-2-1-4-1-6-1l-1-1h1c-1-1-2-1-3-2h0 4z" class="J"></path><defs><linearGradient id="AD" x1="398.762" y1="598.478" x2="391.253" y2="592.777" xlink:href="#B"><stop offset="0" stop-color="#cbc5cd"></stop><stop offset="1" stop-color="#e9ece8"></stop></linearGradient></defs><path fill="url(#AD)" d="M397 594l7 1c-1 1-1 1-3 1-3 0-6 0-8 1h-3l-1-1c1-1 1-1 2-1l6-1z"></path><path d="M387 592l4 1h0c-1 1-1 1-2 1v1h2c-1 0-1 0-2 1l1 1c-1 0-1 0-2-1v-1h-1c-3-1-6-1-9-1v-1c2 0 3-1 5-1h1 3z" class="L"></path><path d="M443 588v-1h-3c1-1 2-1 3-1l1-1h-2c0-1 1-2 2-2v1c2 0 3-1 4-1-1 1-2 1-3 2 1 1 2 2 3 1 2 0 3-1 5 0l-3 2c-1-1-1-1-2-1-2 0-3 1-4 1v1h-1v-1z" class="m"></path><path d="M397 583l1 1v1h0c-1 2-2 2-4 3-1 0-2 0-2 1h0s-1 0-1 1l-1-1v-1h-2v1c-1 0-3 0-4-1 1-1 2 0 4 0v-1h3l1-1c1-1 1-1 2-1l1-1h1l1-1z" class="K"></path><path d="M444 557c1-2 2-2 3-3 0 1 1 1 2 1v1h2 0l1 1c0 1-1 1-2 2h-1c-1 1-1 2-2 2v-3c-1 0-3 1-4 1v-1l1-1z" class="m"></path><path d="M447 558h0v-1c1 0 2 0 2-1h2l1 1c0 1-1 1-2 2h-1c-1 1-1 2-2 2v-3z" class="P"></path><path d="M461 568h3 2 0c0 1-1 2-2 2-1 1-1 2-1 3v1l-1-1v-1h-1-2-2c0-1 0-1-1-2h0v-1c2 0 3-1 4-1h1 0z" class="G"></path><path d="M461 568h3 2 0c0 1-1 2-2 2h-1l-2 1h0c-1 0-2 0-2-1s0-1 1-2h1 0z" class="h"></path><path d="M438 554v-3c1-1 1-1 3-2v3 1c-1 0 0 1 0 1 0 1-1 2-1 2v3h0l-1 1v2 2h-1v-1-2-2l-1-1h1v-4z" class="J"></path><path d="M419 594h13l-1 1h-1c0 1 1 1 2 2l-4 1h0-9v-2-2h0z" class="i"></path><path d="M419 594h0l2 1h1c2 2 4 1 6 3h-9v-2-2z" class="G"></path><path d="M419 594h0l2 1h1c-1 1-2 1-2 1h-1v-2z" class="F"></path><path d="M439 578l1-2h1c1 1 1 2 0 3-2 4-5 6-8 9l-1-1c-2 0-6 2-8 2l5-3c1 0 3-2 5-3l2-2h0c2-1 2-2 3-3z" class="C"></path><path d="M458 583v1l1-1c1 0 2-1 3-1l-2 2-3 3-5 3h5l1 1c-1 0-3-1-3 1v1h-5l-2-1v-1c1 0 1-1 2-1h1c0-1-2-1-3-1l2-1 3-2c1-1 1-1 2-1 1-1 2-2 3-2z" class="W"></path><path d="M374 592l-9-3c2 0 3-1 5-1h0c1 0 2 0 3 1h4 2l8 3h-3-1c-2 0-3 1-5 1v1c-2-1-3-1-4-2z" class="F"></path><path d="M379 589l8 3h-3-1-1-5v-1l2-2z" class="H"></path><path d="M446 565v1h0l-1 1v1c1 0 2 1 2 1l1 1 1-1c1 0 2 0 2 1 2-1 3-1 4-1-2 2-3 3-5 4l-2 2-3 2c0-1 0-1 1-2 0-1 0-1 1-2-1 0-1 0-2 1h-1v-1h-1l-2-2c1 0 1-1 1-1 1-2 1-2 2-3h0-1c1-1 2-1 3-2z" class="U"></path><path d="M448 570l1-1c1 0 2 0 2 1 2-1 3-1 4-1-2 2-3 3-5 4l-1-1h-2 0l-1-1c0-1 1 0 1-2l1 1z" class="S"></path><path d="M448 570l1-1c1 0 2 0 2 1s-1 1-2 1l-1-1z" class="F"></path><path d="M455 569h1v1h0c1 1 1 1 1 2h2v1c-1 1-2 3-3 3l-1 1h1c-2 0-3 1-5 1-1 0-2 0-3 1l-1-1c1-2 1-2 1-3l2-2c2-1 3-2 5-4z" class="V"></path><path d="M456 570h0c1 1 1 1 1 2h2v1h-4v-1l1-2z" class="R"></path><path d="M444 583l6-3h1 5l1 1 1 1v1c-1 0-2 1-3 2-1 0-1 0-2 1-2-1-3 0-5 0-1 1-2 0-3-1 1-1 2-1 3-2-1 0-2 1-4 1v-1z" class="J"></path><path d="M456 580l1 1v1c-2 0-4 1-5 2l-1 1-1-1v-1h1c0-1 0-1-1-2h1 1l2-1h2z" class="M"></path><path d="M413 580l-4 4c-2 1-4 2-5 3l-5 3c2 0 5 0 8 1h5c0-1 2-1 3-2-1 1-1 2-2 2h0l1 1h-9-2c-3-1-8 0-10-1v-1-1h-1 0c0-1 1-1 2-1 2-1 3-1 4-3h0v-1l-1-1h0 1c1 0 2-1 3-1l1-1h1c0 1-1 1-1 2h1 1c0 1 0 1 1 2 3-2 5-3 8-5z" class="O"></path><path d="M392 589h0c0-1 1-1 2-1 2-1 3-1 4-3v2s-1 0-1 1v2h-1-3v-1h-1z" class="a"></path><path d="M401 582l1-1h1c0 1-1 1-1 2h1 1c0 1 0 1 1 2l-6 3c1-1 3-3 3-4h-2l2-2h-1z" class="e"></path><path d="M404 595c1-1 1 0 2 0h2c1 1 3 1 4 1l1 1c2 0 4 0 6 1h9 0v1h2 1v2h-1-1c-4-2-7-2-11-2-7 0-13 1-19 0l-4-1c-1 0-2 0-2-1 2-1 5-1 8-1 2 0 2 0 3-1z" class="l"></path><path d="M401 598v-1c2-1 4-1 7-2 1 1 3 1 4 1l1 1c-4 1-8 1-12 1z" class="B"></path><path d="M404 595c1-1 1 0 2 0h2c-3 1-5 1-7 2v1h-4 0c1 1 1 1 2 1l-4-1c-1 0-2 0-2-1 2-1 5-1 8-1 2 0 2 0 3-1z" class="e"></path><path d="M491 564l1-1c0 1-1 1 0 2 1 0 1 0 2-1h2l-1 1 1 1c-1 2-3 3-5 4-1 1-5 2-6 2l-3-1v-1h-1-2l-4 2v-1c-1-1-1-1-1-2h1c2-1 5-3 8-4v1h4c1 0 3-2 4-2z" class="G"></path><path d="M474 569h1l1 1h0c1-1 2-1 3-1h-1v1h1l-4 2v-1c-1-1-1-1-1-2z" class="U"></path><path d="M495 565l1 1c-1 2-3 3-5 4-1 1-5 2-6 2l-3-1v-1h-1 0l6-3c-2 2-4 3-5 4 1 0 2-1 4-2 1 0 2 0 4-1 1 0 3-2 5-3z" class="g"></path><path d="M429 556c1 0 2 0 3 1-1 0-4 0-5 1h5v1h-2v1h0c1 0 1 0 2 1v2h-7l-2-1c0 1 0 2 1 2h-1c-1 0-3-1-4-1h-2 0-3v-1c-1 0-2-1-2-2v-1l-1-1 1-1c2 1 4 1 6 0 1 0 1 0 2-1s7 0 9 0z" class="h"></path><path d="M412 559h7v1h-1c-2 0-2 0-3 1 0 1 0 1 1 1l1 1h-3v-1c-1 0-2-1-2-2v-1z" class="U"></path><path d="M418 560l1 2h0c1-1 1-1 1-2h5 0c-1 1-2 1-3 1v1h1c0 1 0 2 1 2h-1c-1 0-3-1-4-1h-2 0l-1-1c-1 0-1 0-1-1 1-1 1-1 3-1z" class="X"></path><path d="M417 580l1 1h1 0c1 1 1 1 1 2h0v4h2c1 0 0 1 1 1s1 0 2-1h0c1-1 2-1 4-1l-5 3h-1c-1 0-3 1-5 2-1 0-3 0-4 1l-1-1h0c1 0 1-1 2-2-1 1-3 1-3 2h-5c-3-1-6-1-8-1l5-3c1-1 3-2 5-3h0c3 0 5-1 7-3l1-1z" class="o"></path><path d="M408 587c0 1 0 1 1 2-1 0-1 1-2 1h-3 0l1-2 3-1z" class="J"></path><path d="M418 581h1 0c-1 1-1 2-3 3h0c-3 2-5 4-7 5-1-1-1-1-1-2 3-2 7-4 10-6z" class="F"></path><path d="M417 580l1 1c-3 2-7 4-10 6l-3 1-1-1c1-1 3-2 5-3h0c3 0 5-1 7-3l1-1z" class="P"></path><path d="M420 583v4h2c-2 1-3 1-4 2h-2 0c1-1 2-2 3-2l-1-1-3 3h0c-1 1-3 1-3 2h-3l1-1c1 0 3-1 4-2l4-4h1l1-1z" class="F"></path><path d="M422 587c1 0 0 1 1 1s1 0 2-1h0c1-1 2-1 4-1l-5 3h-1c-1 0-3 1-5 2-1 0-3 0-4 1l-1-1h0c1 0 1-1 2-2h0l3-3 1 1c-1 0-2 1-3 2h0 2c1-1 2-1 4-2z" class="G"></path><path d="M435 568h2c1 1 1 2 2 3h0v3h0c1 0 2 1 2 1v1h-1l-1 2c-1 1-1 2-3 3h0l-2 2c-2 1-4 3-5 3-2 0-3 0-4 1h0c-1 1-1 1-2 1s0-1-1-1h-2v-4h0c0-1 0-1-1-2h0-1l-1-1 8-5c2-2 3-3 4-5v-1 1l2 1c1-1 3-2 4-3z" class="B"></path><path d="M439 571h0v3h0c1 0 2 1 2 1v1h-1l-1 2c-2 1-4 1-6 2v-1c1-3 3-3 5-5l-1-2 2-1z" class="P"></path><path d="M435 568h2c1 1 1 2 2 3l-2 1-5 4c-1 1-3 2-5 3h0v-1-1-1c2-2 3-3 4-5 1-1 3-2 4-3z" class="M"></path><path d="M431 571c1-1 3-2 4-3h0c-1 2-1 3-2 4l-3 3c-1 1-2 2-3 2v-1c2-2 3-3 4-5z" class="B"></path><path d="M429 569v1l2 1c-1 2-2 3-4 5v1 1c-1 0-2 1-2 2l-1 2h1l2-2h1v2c-1 1-2 1-3 1v1c0 1 1 1 1 2h1 1c2-1 4-3 6-4v1c-2 1-4 3-5 3-2 0-3 0-4 1h0c-1 1-1 1-2 1s0-1-1-1h-2v-4h0c0-1 0-1-1-2h0-1l-1-1 8-5c2-2 3-3 4-5v-1z" class="E"></path><path d="M423 579c0 1 1 2 1 2-1 1-2 2-4 2 0-1 0-1-1-2h0c1-1 2-2 4-2z" class="G"></path><path d="M429 569v1l2 1c-1 2-2 3-4 5l-4 3c-2 0-3 1-4 2h-1l-1-1 8-5c2-2 3-3 4-5v-1z" class="g"></path><path d="M462 582c2-1 3-4 5-5 3-1 5-1 7-2 2 0 4-1 5-2h2l5 2 1 1h1v1h-1c-2 0-1 1-3 0-1 0-3 1-3 1-1 1-1 2-1 3l-1 1-5 4h0-1l-3 3c1 1 1 0 1 1 1 0 2-1 3-2-1 1-1 2-1 2h0c-1 1-1 1-2 1v2h-4c-1 0-2 0-4 1h0c-3-1-5-1-8-1v-1c0-2 2-1 3-1l-1-1h-5l5-3 3-3 2-2z" class="I"></path><path d="M481 573l5 2h-1l-4 1v-3z" class="L"></path><path d="M462 587c3-3 6-5 8-7 1-1 1-1 2-1h0c-1 2-3 3-4 5l1 1-1 2c-1 0-1 0-1 1-1 1-2 2-4 2l-1-1h0c-1 1-1 1-2 1v-1h0l1-1c-1 0-3 1-3 2h-1-5l5-3 3-3c1 1 1 2 2 3z" class="H"></path><path d="M457 587l3-3c1 1 1 2 2 3h-1v1c-2 0-2-1-4-1z" class="B"></path><path d="M469 585c1-1 2-3 3-3h3c0-1 0 0 1-1l1-1v1l-1 1-1 2-1 2h-1l-3 3c1 1 1 0 1 1 1 0 2-1 3-2-1 1-1 2-1 2h0c-1 1-1 1-2 1v2h-4c-1 0-2 0-4 1h0c-3-1-5-1-8-1v-1c0-2 2-1 3-1l-1-1h1c0-1 2-2 3-2l-1 1h0v1c1 0 1 0 2-1h0l1 1c2 0 3-1 4-2 0-1 0-1 1-1l1-2z" class="Y"></path><path d="M469 585c1-1 2-3 3-3h3c0-1 0 0 1-1l1-1v1l-1 1-1 2v-1l-1 1c-2 3-6 5-9 8h-1c0-1 2-2 3-4 0-1 0-1 1-1l1-2z" class="V"></path><path d="M450 593h5c3 0 5 0 8 1h0c2-1 3-1 4-1s2 0 3 1h2l-7 1c1 0 1 0 2 1s4 1 5 1h0c1 0 1 1 2 1-1 0-1 1-2 1h-5-2-2v1s-1 1-2 1h-1c-1 0-2 1-3 1h0c-1 0-2 1-4 1s-5-1-7 0h-2c-1-1-3-1-4-2h-9v-2h-1-2v-1l4-1c-1-1-2-1-2-2h1l1-1h4c1 0 1-1 1-1 2 0 4 1 6 1h2l1-1h4z" class="p"></path><path d="M450 593h5c3 0 5 0 8 1h0c2-1 3-1 4-1s2 0 3 1h2l-7 1c-2-1-4-1-6-1h-10-4l1-1h4z" class="Q"></path><defs><linearGradient id="AE" x1="441.292" y1="592.369" x2="434.978" y2="598.788" xlink:href="#B"><stop offset="0" stop-color="#333231"></stop><stop offset="1" stop-color="#4d4d4e"></stop></linearGradient></defs><path fill="url(#AE)" d="M437 593c2 0 4 1 6 1h2 4c-2 0-4 0-6 1h0 3l3 1-15 2c-1-1-1-1-2-1-1-1-2-1-2-2h1l1-1h4c1 0 1-1 1-1z"></path><path d="M432 594h4c1 0 1 0 0 1h-5l1-1z" class="O"></path><path d="M432 597c1 0 1 0 2 1 5 0 11 0 16 1h12 3-2v1s-1 1-2 1h-1c-1 0-2 1-3 1h0c-1 0-2 1-4 1s-5-1-7 0h-2c-1-1-3-1-4-2h-9v-2h-1-2v-1l4-1z" class="T"></path><path d="M452 601h2 6c-1 0-2 1-3 1h0c-1 0-2 1-4 1s-5-1-7 0h-2c-1-1-3-1-4-2h12z" class="G"></path><path d="M450 599h12 3-2v1s-1 1-2 1h-1-6-2c-1-1-1-1-3-1-1 0-2 0-3-1h4z" class="O"></path><path d="M462 599h3-2v1s-1 1-2 1h-1-6c1 0 2 0 3-1-1 0-1 0-1-1h6z" class="S"></path><path d="M456 550h2c1 1 2 1 3 2l1 2h1c1 1 2 2 3 2h0c1 0 1-1 2-1v1h3c0 1 1 2 2 2h1c-2 1-3 2-5 1h-1 0v1c1 2 1 3 2 4-1 2-2 3-4 4h-2-3 0-1c-1 0-2 1-4 1h-1c-1 0-2 0-4 1 0-1-1-1-2-1l-1 1-1-1s-1-1-2-1v-1l1-1h0v-1h0c1-1 2-2 3-2-1-1 0-2 0-4h1c1-1 2-1 2-2l-1-1h0-2v-1c-1 0-2 0-2-1 1-1 2-1 4-2 1-1 3-1 5-2z" class="G"></path><path d="M464 558s1-1 2-1v1l1 1-1 1h0-2l-1 1v-2l1-1z" class="C"></path><path d="M460 557s1 0 1 1c-2 1-5 1-6 2l-2 1c-2 0-2 1-3 2h-1c-1-1 0-2 0-4h1c2 0 3-1 4-2 1 0 1 1 2 0h4z" class="D"></path><path d="M453 561c-1 0-1 0-1-1v-1h2l1 1-2 1z" class="B"></path><path d="M456 550h2c1 1 2 1 3 2l1 2h1c1 1 2 2 3 2h0c1 0 1-1 2-1v1h3c0 1 1 2 2 2h1c-2 1-3 2-5 1h-1 0c0-1 1-1 1-2v-1h-1l-2 2v-1c-1 0-2 1-2 1h0c-1-1-1-1-1-2h-1c-1 1-1 1-1 2 0-1-1-1-1-1h-4c-1 1-1 0-2 0-1 1-2 2-4 2 1-1 2-1 2-2l-1-1h0-2v-1c-1 0-2 0-2-1 1-1 2-1 4-2 1-1 3-1 5-2z" class="Z"></path><path d="M454 556l4-1c1 0 0 0 1 1l1 1h-4c-1 1-1 0-2 0v-1z" class="Q"></path><path d="M456 550h2c1 1 2 1 3 2-1 0-2 0-3 1s-4 1-5 3h1v1c-1 1-2 2-4 2 1-1 2-1 2-2l-1-1h0-2v-1c-1 0-2 0-2-1 1-1 2-1 4-2 1-1 3-1 5-2z" class="G"></path><path d="M456 550h2v1c-1 1-3 2-5 3-1-1-1-1-2-1v-1c1-1 3-1 5-2z" class="D"></path><path d="M468 560c1 2 1 3 2 4-1 2-2 3-4 4h-2-3 0-1c-1 0-2 1-4 1h-1c-1 0-2 0-4 1 0-1-1-1-2-1v-1c1 0 2-1 2-3l-1-1c1-1 4-2 6-3l1 1c1-1 2-1 4-1 1 0 3 1 4 2h1v-2l2-1z" class="M"></path><path d="M468 560c1 2 1 3 2 4-1 2-2 3-4 4h-2l2-2-1-1c-1 0-1 0-2 1h-2v-1h2l2-2h0 1v-2l2-1z" class="C"></path><path d="M449 569v-1c1 0 2-1 2-3l-1-1c1-1 4-2 6-3l1 1h-1c-1 1-1 2-2 2-1 1-1 1-2 1 1 1 1 2 2 2 0 0 1 1 2 1 1-1 3-2 5-2v1 1h0-1c-1 0-2 1-4 1h-1c-1 0-2 0-4 1 0-1-1-1-2-1z" class="E"></path><path d="M494 548h6 1c0 2-1 2-2 3l-2 1 2 2c-1 0-3 1-4 2v1c-2 1-3 2-4 3l-1 3 1 1c-1 0-3 2-4 2h-4v-1c-3 1-6 3-8 4h-1c-1 0-2 1-3 1 0 1 0 2-1 2v-1l-1-1h0 0c-1-1-1-2-3-2h0c2-1 3-2 4-4-1-1-1-2-2-4v-1h0 1c2 1 3 0 5-1v-1h2l2-2h1 2l1 1c0 1 0 1-1 2h1 1l2-2c0-1 0-1-1-2v-2h2 1c-1 0-1-1-1-2 1-1 2-1 3-1l5-1z" class="D"></path><path d="M477 564c0-1 0-1 1-2v-1l4-1v2h0c-1 1-2 1-3 2h-2z" class="M"></path><path d="M500 548h1c0 2-1 2-2 3l-2 1c0 1-1 1-2 2l-1-1c0-1 0-1 1-2 1-2 2-3 5-3z" class="C"></path><path d="M476 566l11-6c2 0 3-1 4-1 2-1 3-2 4-3v1c-2 1-3 2-4 3l-1 3 1 1c-1 0-3 2-4 2h-4v-1c-3 1-6 3-8 4h-1c-1 0-2 1-3 1 0 1 0 2-1 2v-1l-1-1h0 0l1-1c2 0 5-2 6-3z" class="g"></path><path d="M483 565c3-1 5-3 8-5l-1 3 1 1c-1 0-3 2-4 2h-4v-1z" class="V"></path><path d="M489 549l5-1c-1 1-2 2-2 4h0v1 2c1 0 2-1 3 0-1 1-2 1-3 1l-7 3c-1 0-1 0-2-1l2-2c0-1 0-1-1-2v-2h2 1c-1 0-1-1-1-2 1-1 2-1 3-1z" class="E"></path><path d="M484 552h2 1c-1 2-1 3-2 4v1h3c2 0 3-1 4-1l-7 3c-1 0-1 0-2-1l2-2c0-1 0-1-1-2v-2z" class="Y"></path><path d="M479 555h2l1 1c0 1 0 1-1 2h1 1c1 1 1 1 2 1-1 0-2 0-3 1l-4 1v1c-1 1-1 1-1 2h-1c0 1 0 1-1 2h1c-1 1-4 3-6 3l-1 1c-1-1-1-2-3-2h0c2-1 3-2 4-4-1-1-1-2-2-4v-1h0 1c2 1 3 0 5-1v-1h2l2-2h1z" class="R"></path><path d="M478 555l2 1c-1 1-2 1-2 3-1 0-2 1-3 2 0 1-1 2-2 2v1l-1-1c0-1 0-1 1-1s1 0 2-1c0 0 0-1 1-2 0-1-1-1-1-1l-1-1h2l2-2z" class="a"></path><path d="M474 558v-1l1 1s1 0 1 1c-1 1-1 2-1 2-1 1-1 1-2 1s-1 0-1 1l1 1-2 2v1 1l-1 1-1 1c-1-1-1-2-3-2h0c2-1 3-2 4-4-1-1-1-2-2-4v-1h0 1c2 1 3 0 5-1z" class="W"></path><path d="M417 563h0 2c1 0 3 1 4 1h1c1 2 5 2 6 4l-1 1v1c-1 2-2 3-4 5l-8 5-1 1c-2 2-4 3-7 3h0l4-4-8 5c-1-1-1-1-1-2h-1-1c0-1 1-1 1-2h-1l-1 1c-1 0-2 1-3 1h-1 0l-1 1h-1v-1c-2 0-4 1-6 1h0l1-2-1-1-1 1-1-2h-2v-1-3l-2 1v-1l1-1h0c1-2 1-2 1-4v-1c-1-1-1-2-1-3s0-2-1-2c1-1 1-1 1-2h1c2 0 4 0 6 1h3v2h1 6 4 1c1 0 1 0 2-1h3 0l3-2h3z" class="M"></path><path d="M417 563h0 2c-3 1-5 3-7 4-1-1-1-1-1-2h0l3-2h3z" class="j"></path><path d="M406 566c1 0 1 0 2-1h3c0 1 0 1 1 2-1 0-2 1-3 1-5 1-7 5-10 7h-1c0-1-1-2 0-3l2-1c1 0 2-1 4-3h-1l2-2h1z" class="a"></path><defs><linearGradient id="AF" x1="404.431" y1="582.117" x2="410.943" y2="579.781" xlink:href="#B"><stop offset="0" stop-color="#9c9a9b"></stop><stop offset="1" stop-color="#b8b6b6"></stop></linearGradient></defs><path fill="url(#AF)" d="M393 579l-2 3h0c1 0 2 1 3 0 2 0 3-1 5-1 1-1 3-2 5-3s3-2 5-3c-2 2-3 3-4 6l2-1c2-1 4-3 7-5v1 1h1c-1 1-2 1-2 2v1l-8 5c-1-1-1-1-1-2h-1-1c0-1 1-1 1-2h-1l-1 1c-1 0-2 1-3 1h-1 0l-1 1h-1v-1c-2 0-4 1-6 1h0l1-2-1-1h0c1-1 2-2 4-2z"></path><defs><linearGradient id="AG" x1="386.721" y1="573.306" x2="393.003" y2="571.221" xlink:href="#B"><stop offset="0" stop-color="#979595"></stop><stop offset="1" stop-color="#b1b0b1"></stop></linearGradient></defs><path fill="url(#AG)" d="M393 571h7l-2 1c-1 1 0 2 0 3h1-1l-3-1c-1 0-1 0-2 1h-1c0 1 0 2-1 2 0 1 0 1 1 1h1v1h0 0c-2 0-3 1-4 2h0l-1 1-1-2h-2v-1-3l-2 1v-1l1-1h0c1-2 1-2 1-4h8z"></path><path d="M391 574h0c1-1 2-1 4-1 1-1 1-1 3-1-1 1 0 2 0 3h1-1l-3-1c-1 0-1 0-2 1l-2 1v-2z" class="T"></path><path d="M384 575h0c1 1 2 1 2 1 1 0 4-1 5-2v2l-6 3v-3l-2 1v-1l1-1h0z" class="j"></path><path d="M393 575h-1c0 1 0 2-1 2 0 1 0 1 1 1h1v1h0 0c-2 0-3 1-4 2h0l-1 1-1-2h-2v-1l6-3 2-1z" class="H"></path><path d="M385 563c2 0 4 0 6 1h3v2h1 6 4l-2 2h1c-2 2-3 3-4 3h-7-8v-1c-1-1-1-2-1-3s0-2-1-2c1-1 1-1 1-2h1z" class="E"></path><path d="M394 566c-3 0-7-1-10-1 2-1 4-1 7-1h0 3v2z" class="C"></path><path d="M393 571c-1 0-1-1-2-2-1 0-3 0-4-1 1 0 1-1 2-1h7c3 0 5-1 7 1h1c-2 2-3 3-4 3h-7zm30-7h1c1 2 5 2 6 4l-1 1v1c-1 2-2 3-4 5l-8 5-1 1c-2 2-4 3-7 3h0l4-4v-1c0-1 1-1 2-2h-1v-1-1c1 0 2-1 3-2l1 1v-1h1 1l1-1c-2-1-2-1-4-1l-1 1h-3-1l1-2c1-1 3-2 4-2h1c1-1 2-3 4-3h0l1-1z" class="B"></path><path d="M415 577l3-2h1 0l-5 5v1h2c-2 2-4 3-7 3h0l4-4v-1c0-1 1-1 2-2z" class="J"></path><path d="M423 564h1c1 2 5 2 6 4l-1 1v1c-1 2-2 3-4 5 0-1-1-2-1-3h0-1v-1c1-1 2-2 3-4-1 0-2 0-3 1-2 1-2 2-5 2v-1h0-1v2h0l-1 1h-3-1l1-2c1-1 3-2 4-2h1c1-1 2-3 4-3h0l1-1z" class="d"></path><path d="M423 571h1 0c1 0 1 0 1-1h1c1-1 1-1 3 0-1 2-2 3-4 5 0-1-1-2-1-3h0-1v-1z" class="D"></path><defs><linearGradient id="AH" x1="425.97" y1="546.113" x2="404.751" y2="551.293" xlink:href="#B"><stop offset="0" stop-color="#403f3f"></stop><stop offset="1" stop-color="#5f5e60"></stop></linearGradient></defs><path fill="url(#AH)" d="M425 546h7s1 0 2 1c0 1 1 1 2 1h1v2 2h-1v1h1v1h-1-3-1c-1 1-1 1-3 1h0v1c-2 0-8-1-9 0s-1 1-2 1c-2 1-4 1-6 0l-1 1 1 1v1c0 1 1 2 2 2v1l-3 2h0-3c-1 1-1 1-2 1h-1-4-6-1v-2h-3c-2-1-4-1-6-1h-1c0 1 0 1-1 2l-1-2-1 1-2-2c3-1 1-1 2-3s-1-1-1-3c0 0 0-1 1-1 0-1 0-1 1-1l-1-1-3-1c-1 0-2 0-3-1h3c0-1 2 0 2-1h0 0l5-1c4 1 8 1 12 1l7-2c4-2 18 0 21-2z"></path><path d="M432 546s1 0 2 1c0 1 1 1 2 1-2 0-13 0-14-1h4 1c1 0 3 0 5-1z" class="b"></path><defs><linearGradient id="AI" x1="421.873" y1="544.974" x2="418.499" y2="555.485" xlink:href="#B"><stop offset="0" stop-color="#121010"></stop><stop offset="1" stop-color="#2b2b2c"></stop></linearGradient></defs><path fill="url(#AI)" d="M407 550h16c4 0 7-1 10-1h3l1 1v2h-1v1h1v1h-1-3-1v-2h-1c-3-1-6-1-9-1s-9 0-11 1l-6-1h0 0c1 0 2-1 2-1z"></path><path d="M422 551c3 0 6 0 9 1h1v2c-1 1-1 1-3 1h0v1c-2 0-8-1-9 0s-1 1-2 1v-3h-8-8-4c4-2 9-1 13-2h0c2-1 8-1 11-1z" class="O"></path><path d="M422 551c3 0 6 0 9 1h1v2c-1 1-1 1-3 1h0c-1 0-2 0-3-1h0 2v-1h-3 1v-1c1 0 3 1 4 1-1-1-3-1-5-1l-3-1z" class="a"></path><path d="M431 552h1v2c-1 1-1 1-3 1 1-1 1-2 2-2v-1h0z" class="W"></path><path d="M425 553h3v1h-2 0c1 1 2 1 3 1v1c-2 0-8-1-9 0s-1 1-2 1v-3c2-1 4-1 7-1z" class="Y"></path><path d="M385 549c4 1 8 1 12 1h10s-1 1-2 1h0 0l6 1h0c-4 1-9 0-13 2h4 4c-2 2-6 1-8 1s-3 1-4 1l-1 1h-7c-1 0-1 0-2 1l-1 1v1h3v1h-2l-1 1c0 1 0 1 1 1 0 1 0 1-1 2l-1-2-1 1-2-2c3-1 1-1 2-3s-1-1-1-3c0 0 0-1 1-1 0-1 0-1 1-1l-1-1-3-1c-1 0-2 0-3-1h3c0-1 2 0 2-1h0 0l5-1z" class="L"></path><path d="M385 549c4 1 8 1 12 1h10s-1 1-2 1h0 0-27c0-1 2 0 2-1h0 0l5-1z" class="c"></path><path d="M381 553h8c2 0 4 0 6 1h-1c-2 0-8 0-11 1 0 1 0 2 1 3l-1 1v1h3v1h-2l-1 1c0 1 0 1 1 1 0 1 0 1-1 2l-1-2-1 1-2-2c3-1 1-1 2-3s-1-1-1-3c0 0 0-1 1-1 0-1 0-1 1-1l-1-1z" class="i"></path><path d="M402 554h8 8v3c-2 1-4 1-6 0l-1 1 1 1v1c0 1 1 2 2 2v1l-3 2h0-3c-1 1-1 1-2 1h-1-4-6-1v-2h-3c-2-1-4-1-6-1h-1c-1 0-1 0-1-1l1-1h2v-1h-3v-1l1-1c1-1 1-1 2-1h7l1-1c1 0 2-1 4-1s6 1 8-1h-4z" class="Z"></path><path d="M406 560h2 0 4c0 1 1 2 2 2v1l-3 2h0-3c-1 1-1 1-2 1v-1h0v-1c-1-1-1-1-1-2h2 1v-1c-1 0-1 0-2-1h0z" class="E"></path><path d="M408 560h0 4c0 1 1 2 2 2v1l-3 2c-1-3-2-3-3-5z" class="R"></path><path d="M384 558c1-1 1-1 2-1h7l1 1h2c1 1 2 1 2 1h1c1 0 2-1 3 0h3 5v1h-2 0-2-20-3v-1l1-1z" class="I"></path><path d="M388 561h13 1l1 1c0 1-1 1-2 2h-3s0 1-1 1c0 0-2 0-2 1h-1v-2h-3c-2-1-4-1-6-1h-1c-1 0-1 0-1-1l1-1h4z" class="B"></path><path d="M384 563c-1 0-1 0-1-1l1-1h4 0c4 1 7 0 10 1l-1 1h-8-4-1z" class="M"></path><path d="M402 554h8 8v3c-2 1-4 1-6 0l-1 1 1 1v1h-4 2v-1h-5-3c-1-1-2 0-3 0h-1s-1 0-2-1h-2l-1-1 1-1c1 0 2-1 4-1s6 1 8-1h-4z" class="E"></path><path d="M396 558v-1c1-2 6-1 8-1h2c0 1-1 1-2 2l1 1h-3c-1-1-2 0-3 0h-1s-1 0-2-1z" class="C"></path><path d="M410 554h8v3c-2 1-4 1-6 0l-1 1h-2v-1s-1 0-1-1h-1v-1h3c1 0 0 0 1-1h-1z" class="G"></path><path d="M501 569h4c-1 0-1 0-1 1v1c2 1 5 3 6 4l2 2 1-2h1c2 0 3-1 5-2h2l1-1c2 0 3-1 5-2h0c0 2-2 2-2 3h0c2 0 2 0 3-1 0-1 1-1 1-1l-4 4-1 1c2 0 4 1 6 0l1 1c-1 0-1 1-1 1h2 2c1 0 2-1 4-2h2v-1c1 0 1-1 2-1 1-1 1-1 1-2 1 1 1 2 1 3v1c0 1 0 1 1 1-2 1-3 2-5 3s-3 2-5 3v1c-6 3-13 5-20 7-5 2-9 4-14 5-3 0-6 1-9 1l-17 3c-1 0-2 0-3-1 1 0 1-1 2-1-1 0-1-1-2-1h0c-1 0-4 0-5-1s-1-1-2-1l7-1h-2c-1-1-2-1-3-1h4v-2c1 0 1 0 2-1h0s0-1 1-2c-1 1-2 2-3 2 0-1 0 0-1-1l3-3h1 0l5-4 1-1c0-1 0-2 1-3 0 0 2-1 3-1 2 1 1 0 3 0h1v-1h-1c1-1 2-1 3-2l4-1c0-1 1-2 2-2l2-1h0c2 0 2 0 3-1z" class="K"></path><path d="M514 579v2l-2 2-1-1 1-2c1 0 1 0 2-1z" class="B"></path><path d="M472 597h0c1-1 3-1 4-1 4-1 8-1 12-1v1l-16 1z" class="g"></path><defs><linearGradient id="AJ" x1="474.145" y1="599.485" x2="486.636" y2="595.201" xlink:href="#B"><stop offset="0" stop-color="#1b1a1a"></stop><stop offset="1" stop-color="#3c3d3e"></stop></linearGradient></defs><path fill="url(#AJ)" d="M472 597l16-1v1c1 1 2 0 4 0l-17 3c-1 0-2 0-3-1 1 0 1-1 2-1-1 0-1-1-2-1h0z"></path><path d="M525 585c1 0 2-1 3-1l-1-1h0l3-2-4 1c-1 1-2 1-3 1 3-2 5-3 7-4 1 1 2 1 2 1l1-1v1s-1 1-2 1v1c2 0 5-1 7-2h2c-2 1-3 2-5 3-3 1-7 3-10 3v-1z" class="e"></path><path d="M512 583l1 1c1 0 2 0 3 1v1h-2c-3-1-8 2-11 3h0c-2-1-3-1-5 0 0-1 1-1 1-2l9-3 4-1z" class="C"></path><path d="M543 572c1 1 1 2 1 3v1c0 1 0 1 1 1-2 1-3 2-5 3h-2c-2 1-5 2-7 2v-1c1 0 2-1 2-1v-1l-1 1s-1 0-2-1l2-1h2c1 0 2-1 4-2h2v-1c1 0 1-1 2-1 1-1 1-1 1-2z" class="F"></path><path d="M524 576c2 0 4 1 6 0l1 1c-1 0-1 1-1 1-3 1-6 4-9 4-2 0-4 0-6-1 0-2-1-2 0-3 3-2 6-1 9-2h0z" class="I"></path><path d="M479 588h0v-1l1 1v-1c1 1 1 1 2 1l-1-1v-1h1c2 2 4 2 6 2h2v1h7 0 1c2-1 3-1 5 0h0c-1 1-3 1-4 2-2 1-3 1-5 2-4-2-9-4-13-2l-9 3h-2l1-1c3 0 5-2 8-4v-1z" class="H"></path><defs><linearGradient id="AK" x1="505.028" y1="596.169" x2="520.991" y2="585.584" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#4f4f4f"></stop></linearGradient></defs><path fill="url(#AK)" d="M525 585v1c3 0 7-2 10-3v1c-6 3-13 5-20 7-5 2-9 4-14 5v-2l1-1h1c3-2 8-5 11-4 3 0 7-2 11-4z"></path><path d="M501 569h4c-1 0-1 0-1 1v1c2 1 5 3 6 4l2 2h0l2 2c-1 1-1 1-2 1l-1 2 1 1h0l-4 1-9 3c0 1-1 1-1 2h-1 0-7v-1h-2c-2 0-4 0-6-2h-1v1l1 1c-1 0-1 0-2-1v1l-1-1v1h0v1c-3 2-5 4-8 4l-1 1c-1-1-2-1-3-1h4v-2c1 0 1 0 2-1h0s0-1 1-2c-1 1-2 2-3 2 0-1 0 0-1-1l3-3h1 0l5-4 1-1c0-1 0-2 1-3 0 0 2-1 3-1 2 1 1 0 3 0h1v-1h-1c1-1 2-1 3-2l4-1c0-1 1-2 2-2l2-1h0c2 0 2 0 3-1z" class="C"></path><path d="M512 577l2 2c-1 1-1 1-2 1h-1-1c-1-1-2-1-3-2 2 0 3-1 5-1z" class="k"></path><path d="M479 582c2-1 4-3 5-3 0 1-1 1-1 1-1 1-2 1-2 2-2 1-5 3-5 5l1-1v1 1 1l2-1v1c-3 2-5 4-8 4l-1 1c-1-1-2-1-3-1h4v-2c1 0 1 0 2-1h0s0-1 1-2c-1 1-2 2-3 2 0-1 0 0-1-1l3-3h1 0l5-4z" class="O"></path><path d="M500 579h0c1 0 1 1 1 1 2 0 4-2 5-1v2c1 0 1-1 3 0v2h0c1 0 2 0 2-1l1 1h0l-4 1-9 3c0 1-1 1-1 2h-1l1-1c0-1-1-1-1-3h1c2-2 2-3 2-5v-1z" class="F"></path><path d="M499 587h-1c1-1 2-2 2-3 2-1 6-2 8-1v1l-9 3z" class="c"></path><path d="M501 569h4c-1 0-1 0-1 1v1c2 1 5 3 6 4-1 1-1 1-2 1-2 0-4 0-6 1h-3l-1 1h0v1l-1 1v1h-1c-1-1-1-1 0-2 0-1 1-1 1-2l1-1v-1c-1 0-1 1-2 1h0-1c-2 0-2 1-3 2l-1 1v-1c-1-1-1-2-1-4l4-1c0-1 1-2 2-2l2-1h0c2 0 2 0 3-1z" class="I"></path><path d="M501 569h4c-1 0-1 0-1 1v1c-3 0-7 2-10 2 0-1 1-2 2-2l2-1h0c2 0 2 0 3-1z" class="a"></path><path d="M487 580c1 0 2-1 2-1 1 0 1 0 2 1v-1c1 0 2-1 4 0h0v1c0 1 1 2 1 2 2-1 2-2 4-3v1c0 2 0 3-2 5h-1c0 2 1 2 1 3l-1 1h0-7v-1h-2c-2 0-4 0-6-2 1 0 1 0 1-1 1-2 3-3 4-5z" class="X"></path><path d="M490 589l1-2h3 0c0-1 0-1 1-1v1h1c0 1 1 1 1 2h-7z" class="K"></path><path d="M487 580c1 0 2-1 2-1 1 0 1 0 2 1v-1c1 0 2-1 4 0h0v1c0 1 1 2 1 2l-1 1c-1 1-1 1-2 1s-1 1-2 1-1 1-2 1c-1-1-2-1-4-1v-1c2 0 2 0 3-1v-3h-1z" class="Z"></path><path d="M371 548h2v1c2 1 5 1 7 1h0c0 1-2 0-2 1h-3c1 1 2 1 3 1l3 1 1 1c-1 0-1 0-1 1-1 0-1 1-1 1 0 2 2 1 1 3s1 2-2 3l2 2 1-1 1 2c1 0 1 1 1 2s0 2 1 3v1c0 2 0 2-1 4h0l-1 1v1l2-1v3 1h2l1 2c-2 2-4 3-5 4h-1c-2 1-4 1-7 2h-3c2 0 3 0 5 1h-4c-1-1-2-1-3-1h0c-2 0-3 1-5 1l9 3c-3 2-7 1-10 1s-5 1-8 1-7-1-10 0h-2v-2l-5 1c0-1 1-1 2-2 2-1 2-1 2-2l1-1-1-1c-2 1-4 3-6 4v-1l1-1v-1l-1 1h-1c0-1 1-1 1-2h-2v-1h0c-1 0-2 1-3 1l1-2c-1 0-1-1-1-2h0l-1-1h-1-2v-1c0-1 1-2 2-3 0-1 1-1 2-2 0-1 0-2 1-2v-1-1c-2 1-4 1-7 2h0-1c0-2 1-2 2-3v-3h0v-2c0-1 0 0 1-1h0c2 0 3-1 4-2 1 0 1 1 2 0l1-1c1 0 2-1 3-1 2 0 2 0 4-1h0 2v1 1h1 0c2 0 3-1 4-1h0 1c1 0 1 0 2-1s2-2 4-3c1 0 2-1 3 0l2 2v1h0v-1c0-1 0-1-1-2h0v-1c1 0 1 0 2 1l1 1v-1h2v-1h1c0-1 0-1 1-1 1-1 2-1 2-1 1-1 1-1 1-2l-1-1c0-1 0-1 1-2l1-1z" class="r"></path><path d="M374 561v-1c1 1 1 2 1 3-2 1-2 1-4 1l2-2 1-1z" class="W"></path><path d="M359 586h0l1 1c1-1 2-1 4-2 0-1 1 0 2 0-2 2-3 2-6 3v1c-2 0-5 1-6 0 2-1 3-3 5-3z" class="n"></path><path d="M381 564l1-1 1 2c1 0 1 1 1 2s0 2 1 3v1c0 2 0 2-1 4h0v-3c0-1 0-2-1-3l-2 1v-2h1v-2l-1-1v-1z" class="W"></path><path d="M378 552l3 1 1 1c-1 0-1 0-1 1-1 0-1 1-1 1 0 2 2 1 1 3s1 2-2 3l-1-1h-1 0c1-1 2-1 2-1 0-1 0-2-1-3v-1c1-1 0-3 0-4z" class="T"></path><path d="M381 565l1 1v2h-1l-1 1c-1 1-2 2-4 2h-1s0-1-1-2c2-2 4-3 7-4z" class="N"></path><path d="M375 571v-1c1-1 3-1 4-2l1 1c-1 1-2 2-4 2h-1z" class="M"></path><path d="M385 580h2l1 2c-2 2-4 3-5 4-1-1-1-2-1-2h-3-1c2-2 4-3 6-4h1z" class="R"></path><path d="M385 580h2l1 2c-2 0-3-1-4-2h1z" class="L"></path><path d="M370 588c3-1 6-2 8-4h1 3s0 1 1 2h-1c-2 1-4 1-7 2h-3c2 0 3 0 5 1h-4c-1-1-2-1-3-1h0z" class="P"></path><path d="M344 588c2 0 4-1 6-1 0 1-2 4-4 4 0 0-1 1-2 1l-5 1c0-1 1-1 2-2 2-1 2-1 2-2l1-1z" class="G"></path><path d="M380 569l1-1v2c0 1 0 2-1 2 0 1-1 1-2 1s-2 1-3 1-5 1-5 2h-1c-1 0-1 0-2-1 0 0-1 0-1-1 3-1 7-2 8-5 1 1 1 2 1 2h1c2 0 3-1 4-2z" class="D"></path><path d="M342 584c2-1 2 0 3-2 3 0 5-1 8-3 0 1 0 1 1 2v1l-2 1 1 1-1 1h-1 0l-1 2c-2 0-4 1-6 1l-1-1h0 0c0-1 0-1-1-2h0v-1z" class="V"></path><path d="M352 583l1 1-1 1h-1 0l-1 2c-2 0-4 1-6 1l-1-1h0c3-2 6-3 9-4z" class="b"></path><path d="M370 576c0-1 4-2 5-2s2-1 3-1v1c0 1 0 2-1 2-2 2-4 5-6 6-1 0-2-1-3-1v1h-2c-1 0 0 0-1 1l-1-1 2-2c1-1 1-1 1-2l3-1v-1z" class="R"></path><path d="M375 574h2v1l-1 1c-2 1-4 3-6 4l-2 1v-1c1 0 2-1 2-1v-2c1-1 4-2 5-3z" class="B"></path><path d="M362 577c1-1 2-2 4-3 0 1 1 1 1 1 1 1 1 1 2 1h1v1l-3 1c0 1 0 1-1 2l-2 2 1 1c1-1 0-1 1-1h2v-1c1 0 2 1 3 1l-4 2-1 1c-1 0-2-1-2 0-2 1-3 1-4 2l-1-1h0l1-1c0-1 0-1-1-1v-1l2-1v-1h0v-2-1l1-1z" class="P"></path><path d="M362 577c1-1 2-2 4-3 0 1 1 1 1 1 1 1 1 1 2 1-2 1-4 2-5 1h-1s-1 0-1 1v-1z" class="E"></path><path d="M367 584c-2-1-3 1-6 0 1-1 1-1 1-2h1c0-1 1-1 1-2 1-1 2-1 3-2 0 1 0 1-1 2l-2 2 1 1c1-1 0-1 1-1h2v-1c1 0 2 1 3 1l-4 2z" class="S"></path><path d="M357 572c0 1 0 1 1 2v1l-1 3c0 1-2 3-3 4v-1c-1-1-1-1-1-2-3 2-5 3-8 3-1 2-1 1-3 2l-1-1 1-1c0-1-1-1-2-1v-1c2-1 4-2 7-2 1 0 1-1 2-1 3-1 5-3 8-5z" class="i"></path><path d="M353 576l1-1c1-1 2-1 4-1v1l-1 3c0 1-2 3-3 4v-1c-1-1-1-1-1-2v-1l-3 1v-1l3-2z" class="B"></path><path d="M353 576l1-1c1-1 2-1 4-1v1l-1 3h-2l-2-2z" class="C"></path><path d="M369 555l3-3h1c1 1 1 2 0 3v1 1l1-1c1 2 1 3 0 4v1l-1 1-2 2-1 1v1l1 1h0l-2 1v2h0-2c-2 2-4 2-6 3 0-2 1-3 2-4 2-2-1-7 1-9v-1c2-2 3-3 5-4z" class="V"></path><path d="M369 562l2-2c0-1 1 0 3 0v1l-1 1c-1 0-2 1-3 2-1 0-2 1-2 1h-1v-1-1c1 0 1 0 2-1h0z" class="I"></path><path d="M364 560c0 1 0 3 1 4l-1 2 1 1 1-1v2h1 0l3-3v1l1 1h0l-2 1v2h0-2c-2 2-4 2-6 3 0-2 1-3 2-4 2-2-1-7 1-9z" class="O"></path><path d="M369 555l3-3h1c1 1 1 2 0 3v1 1l1-1c1 2 1 3 0 4v1-1c-2 0-3-1-3 0l-2 2h0c-1 1-1 1-2 1v1l-1-1h1l-1-1h-1c0-1-1-1-1-3 2-2 3-3 5-4z" class="D"></path><path d="M368 561l-1 1-1-1v-2h1l1 2z" class="M"></path><path d="M369 558h0v-1h-1c1-1 0-1 1-1h1 1 0 2l-1 1-2 2h-1v-1z" class="B"></path><path d="M373 556h0v1l1-1c1 2 1 3 0 4v1-1c-2 0-3-1-3 0l-2 2c0-1-1-1-1-1l-1-2 1-1h1v1h1l2-2 1-1z" class="Z"></path><path d="M369 554v1c-2 1-3 2-5 4v1c-2 2 1 7-1 9-1 1-2 2-2 4h-1 0v-1 1c-1 0-1 0-2 1v1-1c-1-1-1-1-1-2-3 2-5 4-8 5-1 0-1 1-2 1-3 0-5 1-7 2v1c1 0 2 0 2 1l-1 1 1 1v1h0c1 1 1 1 1 2h0 0c-2 1-4 3-6 4v-1l1-1v-1l-1 1h-1c0-1 1-1 1-2h-2v-1h0c-1 0-2 1-3 1l1-2c-1 0-1-1-1-2h0l-1-1h-1-2v-1c0-1 1-2 2-3 0-1 1-1 2-2 0-1 0-2 1-2v-1-1c-2 1-4 1-7 2h0-1c0-2 1-2 2-3v-3h0v-2c0-1 0 0 1-1h0c2 0 3-1 4-2 1 0 1 1 2 0l1-1c1 0 2-1 3-1 2 0 2 0 4-1h0 2v1 1h1 0c2 0 3-1 4-1h0 1c1 0 1 0 2-1s2-2 4-3c1 0 2-1 3 0l2 2v1h0v-1c0-1 0-1-1-2h0v-1c1 0 1 0 2 1l1 1v-1h2v-1h1c0-1 0-1 1-1 1-1 2-1 2-1z" class="h"></path><path d="M356 568c2-1 3-4 5-4h0c0 2-1 4-2 6-1-1-2-2-3-2z" class="E"></path><path d="M339 574c1-1 2-2 3-2 0 1 0 2 1 3l-1 1-3 1h-1v-3h1z" class="V"></path><path d="M339 574c1-1 2-2 3-2 0 1 0 2 1 3l-1 1c-1-1-1-2-3-2z" class="Q"></path><path d="M361 559v1 1c-1 0-2-1-2 0-1 1-2 1-4 2-1 0-2 1-3 2v1h1l-1 1c-1-1-1-1-1-2v-1c1-1 1-2 2-2 2 0 4-2 5-3 1 1 1 1 2 1l1-1z" class="E"></path><path d="M336 574c1 0 1 0 2 1v-1 3h1c-1 1-2 1-3 1l-3 1h0l1-1v-2h1l-1-1 2-1z" class="d"></path><path d="M336 574c1 0 1 0 2 1-1 1-2 1-3 1l-1-1 2-1z" class="n"></path><path d="M332 576c0-1 1-1 2-1l1 1h-1v2l-1 1h0v1c0 1 0 1 1 2l-2 1h0l-1-1h-1-2v-1c0-1 1-2 2-3 0-1 1-1 2-2z" class="T"></path><path d="M333 580c0 1 0 1 1 2l-2 1h0l-1-1h-1c1-1 1-1 0-1 1-1 2-1 3-1z" class="X"></path><path d="M347 574c1 0 2-1 3-1 2-2 4-3 6-5 1 0 2 1 3 2-1 1-1 2-2 2-3 2-5 4-8 5h-3-1c1-1 2-2 2-3z" class="D"></path><path d="M369 554v1c-2 1-3 2-5 4v1c-2 2 1 7-1 9-1 1-2 2-2 4h-1 0v-1 1c-1 0-1 0-2 1v1-1c-1-1-1-1-1-2 1 0 1-1 2-2 1-2 2-4 2-6 0-1 0-2-1-3h1v-1-1c0-1 0-1-1-2h0v-1c1 0 1 0 2 1l1 1v-1h2v-1h1c0-1 0-1 1-1 1-1 2-1 2-1z" class="Y"></path><path d="M347 574c0 1-1 2-2 3h1 3c-1 0-1 1-2 1-3 0-5 1-7 2v1c1 0 2 0 2 1l-1 1 1 1v1h0c1 1 1 1 1 2h0 0c-2 1-4 3-6 4v-1l1-1v-1l-1 1h-1c0-1 1-1 1-2h-2v-1h0c-1 0-2 1-3 1l1-2c-1 0-1-1-1-2l2-1c-1-1-1-1-1-2v-1l3-1 1 1v1h0l6-3c2-1 3-2 4-3z" class="d"></path><path d="M337 580h0 2c0 1-1 2-1 2 1 1 2 1 2 3l-4 1h0c0-1 1-2 1-2v-1l-1 1h-1c0-1 1-2 2-4h0z" class="D"></path><path d="M333 579l3-1 1 1v1h0c-1 2-2 3-2 4h1l1-1v1s-1 1-1 2h-1 0c-1 0-2 1-3 1l1-2c-1 0-1-1-1-2l2-1c-1-1-1-1-1-2v-1z" class="G"></path><path d="M333 579l3-1 1 1v1h0l-3 2c-1-1-1-1-1-2v-1z" class="j"></path><path d="M349 561h1c1 0 1 0 2-1s2-2 4-3c1 0 2-1 3 0l2 2-1 1c-1 0-1 0-2-1-1 1-3 3-5 3-1 0-1 1-2 2v1c0 1 0 1 1 2l1-1 2-1h0c-1 3-5 6-7 7l-5 3c-1-1-1-2-1-3-1 0-2 1-3 2h-1v1c-1-1-1-1-2-1l-2 1c-1 0-2 0-2 1 0-1 0-2 1-2v-1-1c-2 1-4 1-7 2h0-1c0-2 1-2 2-3v-3h0v-2c0-1 0 0 1-1h0c2 0 3-1 4-2 1 0 1 1 2 0l1-1c1 0 2-1 3-1 2 0 2 0 4-1h0 2v1 1h1 0c2 0 3-1 4-1h0z" class="I"></path><path d="M343 570l1-1v1l1 2v1l1-1h2l-5 3c-1-1-1-2-1-3l1-1v-1z" class="B"></path><path d="M326 574c1-1 1-2 2-3 0-1 0-1 1-1h1l1 1c1 0 1 0 2 1h0 0c-2 1-4 1-7 2z" class="D"></path><path d="M334 563h3l1 1v1l-11 3h0v-2c0-1 0 0 1-1h0c2 0 3-1 4-2 1 0 1 1 2 0z" class="g"></path><path d="M333 572c1-1 1-1 2-1 0 1 0 0 1 1 1-2 3-3 4-4v1h0v1h3 0v1l-1 1c-1 0-2 1-3 2h-1v1c-1-1-1-1-2-1l-2 1c-1 0-2 0-2 1 0-1 0-2 1-2v-1-1h0z" class="Q"></path><path d="M343 570v1l-1 1c-1 0-2 1-3 2h-1v1c-1-1-1-1-2-1 2-2 4-3 7-4h0z" class="T"></path><path d="M349 561h1c1 0 1 0 2-1s2-2 4-3c1 0 2-1 3 0l2 2-1 1c-1 0-1 0-2-1-1 1-3 3-5 3-1 0-1 1-2 2v1h0v-2l-3-1c0 1-1 1-2 2h-1c-2 0-3 0-4 1h-1l-1-1-1 1v-1l-1-1h-3l1-1c1 0 2-1 3-1 2 0 2 0 4-1h0 2v1 1h1 0c2 0 3-1 4-1h0z" class="B"></path><path d="M334 563l1-1c1 0 2-1 3-1 2 0 2 0 4-1l1 1c0 1-2 2-4 3l-1 1v-1l-1-1h-3z" class="m"></path><path d="M615 441h1v-1l1 1c-1 1-2 3-2 4v1 1c-1 1-1 2-1 3v1c0 1-1 2-2 3h3 0c1 0 1-2 1-3l1 1c0 1-1 2 0 3l3 4h3 0 0 2c2 1 3 1 4 2l2 2c0 2 1 3 1 4 0 4-1 6-4 9l-1 1v2c0 2 0 5-2 8 0 0 0 1 1 2-1 0-1 0-2 1h1l1 6-1 4h1c1 1 0 2 1 2l2 2c1 0 2 0 3-1 3 3 5 7 6 11 0 2 1 4 0 6h-2c0 5-2 10-5 14-2 3-6 5-9 8-2 2-5 4-7 6-3 4-4 8-6 12a30.44 30.44 0 0 1-8 8c-6 8-13 14-20 20l-6 4h-1c1-2 2-4 2-6h-1 0c-2 3-3 6-6 9v-1c-1 0-2 1-3 2h-1-1c0-1 3-4 3-5h-1v-2c0 1-1 1-1 0h-1l-1 1v-2c1-1 1-1 1-2h-1v-1l-5 1-1-1h-1v-1h0c2-1 3-2 5-2l2-2h-6-1v-2l-1 1h0c-1 0-2 0-2 1h-5c-5 0-9 1-13 4v-1c2-1 3-2 5-3s3-2 5-3c-1 0-1 0-1-1v-1c0-1 0-2-1-3 0 1 0 1-1 2-1 0-1 1-2 1v1h-2c-2 1-3 2-4 2h-2-2s0-1 1-1l-1-1c-2 1-4 0-6 0l1-1 4-4s-1 0-1 1c-1 1-1 1-3 1h0c0-1 2-1 2-3h0c-2 1-3 2-5 2l-1 1h-2c-2 1-3 2-5 2h-1l-1 2-2-2c-1-1-4-3-6-4v-1c0-1 0-1 1-1h-4c-1 1-1 1-3 1h0c1-1 1-1 1-3 0-1 0-1-1-1h-2l-1-1 1-1h-2c-1 1-1 1-2 1-1-1 0-1 0-2l-1 1-1-1 1-3c1-1 2-2 4-3v-1c1-1 3-2 4-2l-2-2 2-1c1-1 2-1 2-3h1v-2l3-1h2c4-2 8-3 11-7 1 2 1 3 2 4h1l1-1h3l5-4-1-1h0 2c1 0 2-1 3-2l4-3c1 0 1 1 2 2l2-1h0c0 1-1 1 0 3l8-7h1c1 0 2 0 2 1 2-1 4-2 5-3h0c2 0 2 0 3 1l4-4h0c-1-1 0-1-1-1h0 0c4-3 8-8 11-11 2-2 5-4 7-5l5-4h-1c-1 1-2 1-3 1 1-2 4-3 4-6l-1 1-1 1c0-2 2-5 2-7h0c1-1 2-2 2-3 1-2 2-4 3-5v-1c1-1 2-4 3-6l7-15v-1c-1 0-1 0-2-1h0l-2 2-1 1v-2l-1-1c0-1 1-2 2-2 1-3 2-4 4-6s5-4 7-6c1 1 1 1 2 1l3-3c1-1 1-2 1-3z" class="p"></path><path d="M623 522c1 2 1 3 0 5h0-1-1c1-2 2-3 2-5z" class="a"></path><path d="M574 578v1c1 1 1 2 2 3v1h-1-1 0c-2 0-3 2-5 4v-2l3-3c1-1 1-2 2-4z" class="b"></path><path d="M607 468v-1c2-1 3-2 4-3h1c-1 2-2 4-3 7-1 0-2 1-3 1l1-4z" class="I"></path><path d="M623 459h0 0 2c2 1 3 1 4 2l2 2c-1 0-2 0-2-1-1-1-1-1-2-1h0 0c1 0 1 1 2 1l-1 1v-1c-2-1-2-1-4-1-1 0-3 0-4-1v-1h3z" class="Y"></path><path d="M624 490h1l1 6-1 4h0c-1-1-1-3-1-4v-2c-1 0-1-1-2-1l2-3z" class="G"></path><path d="M628 462v1c0 2 0 2-1 3h-1c-2 1-4 3-6 4h0c1-1 1-2 1-3 1-1 6-4 7-5z" class="P"></path><path d="M607 530h1l2-1h0v2c1 0 2 0 2 1l2-2v2h-1v2h-1-1v-1c-1 0-2 0-2 1-1-1-1-1-2-1h-2c1-1 1-2 2-3z" class="j"></path><path d="M618 535v-1c1 0 2-1 2-1h1l2-2v1c-1 1-1 1-1 2s-1 1-1 2c-2 1-2 3-4 4h-1c0-2 1-4 2-5z" class="W"></path><path d="M624 515c2-3 3-5 3-8 1-1 1-1 2 0v2 2h1v1l-1 1-1 1h-1-1c0 1-1 1-2 1h0z" class="i"></path><path d="M571 573s1 0 1-1c1 0 1 1 2 1l1-1 1 1 2 2h2v4h1v-1h0l2-1h0c0 1-1 1-1 3l-2 2c-1-1-2-3-1-4 0-1 0-1-1-2 0-1-1-1-2-1-1-1-1 0-1 0 0 1 0 2-1 3 0-2 0-2-1-3 0-1-1-1-2-2z" class="Y"></path><path d="M617 523v-1c0-1 1-2 2-3 0 1 0 2 1 2-1 2-3 9-6 11v-2l-2 2c0-1-1-1-2-1v-2c1 1 1 1 1 2 1-1 1-1 2-1 1-1 3-3 3-5 0 0 1-1 1-2z" class="g"></path><path d="M585 579c2 0 4-4 5-5 1-2 3-2 4-4 2-1 3-3 5-4h1c-5 6-10 11-15 15v-2z" class="P"></path><path d="M632 503c3 3 5 7 6 11 0 2 1 4 0 6h-2c1-3 0-6-1-9s-3-5-6-7c1 0 2 0 3-1z" class="L"></path><path d="M618 517l2-2 1 1h2l1 1h3c0 1-1 2-2 3h0c-1-1-2 0-4 0l-1 1c-1 0-1-1-1-2 1 0 1-1 2-2h-1l-6 3h-2v-1c1 0 1 0 3-1 1 0 2 0 3-1z" class="a"></path><path d="M620 517l1-1 1 1h0c-1 1-1 2-1 3l-1 1c-1 0-1-1-1-2 1 0 1-1 2-2h-1z" class="h"></path><path d="M584 570h1s0 1 1 1l-1 2c-1 1-2 3-2 4l-2 1h0v1h-1v-4h-2l-2-2h0c1 0 1-1 2-1l1 1c1-1 1-1 2-1h1l2-2h0z" class="c"></path><path d="M584 570h1s0 1 1 1l-1 2c-1 1-2 3-2 4l-2 1v-1l1-2 2-4h1l-1-1h0z" class="T"></path><path d="M629 507c1 0 3 0 3 1 1 1 1 2 1 3 1 1 2 3 1 4 0 1 0 0-1 1h0l-1-1-1 1h-1-2v-2l1-1 1-1v-1h-1v-2-2z" class="U"></path><path d="M633 511c1 1 2 3 1 4 0 1 0 0-1 1h0l-1-1-1 1h-1-2v-2l1-1v1h3 0v-1-1c0-1 0-1 1-1z" class="f"></path><path d="M615 465l2 1v2c2 0 1-2 3-1-1 1-2 1-2 3l3-3c0 1 0 2-1 3h0 0c-1 2-3 3-5 4l-1-1v-4-1c0-1 0-2 1-3zm6 62h1 1v1l2-1c0 1 2 3 2 3h2l-1 2c-1 1-1 2-2 2l-3-2h0v-1l-2 2h-1s-1 1-2 1v1l1-3c0-1 1-2 1-3s1-2 1-2z" class="c"></path><path d="M620 529c1 1 1 1 0 3l-1 1v-1c0-1 1-2 1-3zm3-1l2-1c0 1 2 3 2 3h2l-1 2c-1 0-1 0-2-1s-1-1-3-1l-1-1 1-1z" class="a"></path><path d="M569 585v2s-1 1 0 1c0 1 0 2-1 3 2-1 3-3 5-4 0-1 1-1 2-1-2 3-3 6-6 9v-1c-1 0-2 1-3 2h-1-1c0-1 3-4 3-5h-1v-2c0 1-1 1-1 0h-1l4-4h1z" class="j"></path><path d="M632 467c0 4-1 6-4 9l-1 1h-2c-1-1-1-1-1-2l-2-1c2-3 5-5 8-6 1 0 1-1 2-1z" class="Y"></path><path d="M630 468v2c-1 2-4 4-6 5l-2-1c2-3 5-5 8-6z" class="B"></path><path d="M580 565v-1 1c1 1 0 2 1 2h2v1c1 0 1 0 1 1l1-1v1l-1 1h0l-2 2h-1c-1 0-1 0-2 1l-1-1c-1 0-1 1-2 1h0l-1-1-1 1c-1 0-1-1-2-1l4-3 1-1 3-3z" class="m"></path><path d="M580 565v-1 1c1 1 0 2 1 2h2v1c1 0 1 0 1 1l1-1v1h-1c-1-1-1-1-2-1s-2 1-2 1c-1 1-1 2-2 2l-1-2v-1l3-3z" class="U"></path><path d="M620 460c1 1 3 1 4 1 2 0 2 0 4 1-1 1-6 4-7 5l-3 3c0-2 1-2 2-3-2-1-1 1-3 1v-2l-2-1c1-2 3-3 5-5z" class="R"></path><path d="M620 460c1 1 3 1 4 1l-1 1c-1 1-2 1-4 1l-1 1c0 1 0 1 1 2 1 0 2-1 3-2h0c0 1-1 2-2 3h0c-2-1-1 1-3 1v-2l-2-1c1-2 3-3 5-5z" class="S"></path><path d="M584 578c0-1 3-3 4-4l14-15v5c0 1-1 1-2 2h0-1c-2 1-3 3-5 4-1 2-3 2-4 4-1 1-3 5-5 5l-1-1z" class="c"></path><path d="M612 454h3 0l-4 8 1 1v1h-1c-1 1-2 2-4 3v1l-2 1-1-1h0l-1-1c0-1 0-1 1-2 3-3 6-7 8-11z" class="D"></path><path d="M604 468l7-6 1 1v1h-1c-1 1-2 2-4 3v1l-2 1-1-1h0z" class="W"></path><path d="M603 545c1 1 1 1 0 3h0c-1 5-5 7-9 10h0c-1 0-2-1-2-1-1 0-1 1-2 1h0l1-2-1-1h0v-1c1 0 2-1 3-1l1-1c1 0 2-1 3-2 2 0 4-3 6-4v-1z" class="o"></path><path d="M593 553c1 0 2 1 3 1-1 1-2 1-3 1v-1c-1 0-1 0-2 1h-1v-1c1 0 2-1 3-1z" class="U"></path><path d="M603 545c1 1 1 1 0 3h0c0 1-1 1-1 2h-2c-1 1-2 2-3 4h-1c-1 0-2-1-3-1l1-1c1 0 2-1 3-2 2 0 4-3 6-4v-1z" class="G"></path><path d="M606 472c1 0 2-1 3-1 0 2 0 4-2 6-2 1-3 4-5 6-2 1-7 2-7 3-1 0-1 2-1 3s-1 3-1 5c-1-1-2-1-2-2h-4c1-1 2-2 2-3 1-2 2-4 3-5l3-2c3 0 5-1 6-3h0c1-1 2-2 2-3l3-4z" class="C"></path><path d="M591 489h1v2l-1 1c-1 0-1 0-2-1 1-1 1-2 2-2z" class="I"></path><path d="M620 517h1c-1 1-1 2-2 2-1 1-2 2-2 3v1c0 1-1 2-1 2 0 2-2 4-3 5-1 0-1 0-2 1 0-1 0-1-1-2h0l-2 1h-1 0-1v-1c1-1 2-2 2-3h0c-1 0-2 1-2 2h-2v-2h-1-1v-1l3-2 2-1c1 0 3 0 4-1h2l1-1 6-3z" class="U"></path><path d="M614 526c0-1 0-2 1-3h2c0 1-1 2-1 2h0c-1 1-1 1-2 1z" class="O"></path><path d="M613 521h1v2l-1 1h-2l-2 2c0-1 0-1-2-1v-1h1l-1-1h1l-1-1c1 0 3 0 4-1h2z" class="R"></path><path d="M611 521v2c-1 0-2 1-3 1l-1-1h1l-1-1c1 0 3 0 4-1z" class="S"></path><path d="M609 527c2 0 2-1 4-2v1h0 1c1 0 1 0 2-1h0c0 2-2 4-3 5-1 0-1 0-2 1 0-1 0-1-1-2h0l-2 1h-1 0l2-3z" class="l"></path><path d="M605 523l2-1 1 1h-1l1 1h-1v1c2 0 2 0 2 1v1l-2 3h-1v-1c1-1 2-2 2-3h0c-1 0-2 1-2 2h-2v-2h-1-1v-1l3-2z" class="G"></path><path d="M597 523h0c2 0 2 1 3 1 2 0 3-2 4-3 0 1 1 1 1 2l-3 2v1h1 1v2h2c0-1 1-2 2-2h0c0 1-1 2-2 3v1h1 0c-1 1-1 2-2 3 0 1-1 2-1 2-1 0-2 1-3 1 0-2-1-3 0-5h0l-1-1h-1c-1 1-1 2-2 3h0v-1c-1-1-3 0-4 0 0-1 0-2 1-3h0c1-1 2-2 2-3h0c-1 0-1 0-1 1h-1c-1 1-2 1-3 1v-1c1 0 2-1 3-1l3-3z" class="a"></path><path d="M606 528c0-1 1-2 2-2h0c0 1-1 2-2 3v1h1 0c-1 1-1 2-2 3h-1l1-3h0c-1 0-2 0-3 1h0 0v-2c-1-1-1-1-2-1 0 0 1-1 1 0 1 0 1-1 2-1v1l1 1h1l1-1z" class="P"></path><path d="M597 523h0c2 0 2 1 3 1 2 0 3-2 4-3 0 1 1 1 1 2l-3 2v1h1 1v2h2l-1 1h-1l-1-1v-1c-1 0-1 1-2 1 0-1-1 0-1 0-1 1-2 2-3 2h-1-1v-1c1 0 2-1 3-2l-1-1h-1 0c-1 0-1 0-1 1h-1c-1 1-2 1-3 1v-1c1 0 2-1 3-1l3-3z" class="W"></path><path d="M602 525v1h1 1v2h2l-1 1h-1l-1-1v-1c-1 0-1 1-2 1 0-1-1 0-1 0-1 1-2 2-3 2h-1l3-3c1-1 2-2 3-2z" class="U"></path><path d="M573 575c1 1 1 1 1 3h0c-1 2-1 3-2 4l-3 3h-1l-4 4-1 1v-2c1-1 1-1 1-2h-1v-1l-5 1-1-1h-1v-1h0c2-1 3-2 5-2l2-2c1-1 3-2 4-2 2-1 4-2 6-2h0v-1z" class="T"></path><path d="M556 584h1c1 0 4 0 6-1v-1l2 1v1c-1 0-1 1-2 1l-5 1-1-1h-1v-1h0z" class="E"></path><path d="M567 582v1c2-1 2-1 3-2l2 1-3 3h-1l-4 4-1 1v-2c1-1 1-1 1-2h-1v-1c1 0 1-1 2-1v-1c1-1 1-1 2-1z" class="R"></path><path d="M567 582v1c2-1 2-1 3-2l2 1-3 3h-1c-1 0-2 0-2-1l1-2z" class="G"></path><path d="M573 575c1 1 1 1 1 3h0c-1 2-1 3-2 4l-2-1 1-2v-1l-1 1h-2c-2 0-2 1-3 2-2 0-3 0-4 1l2-2c1-1 3-2 4-2 2-1 4-2 6-2h0v-1z" class="F"></path><path d="M628 514v2h2 1l1-1 1 1h0c1-1 1 0 1-1v5 2l-4 7-1 1h-2s-2-2-2-3l-2 1v-1h0c1-2 1-3 0-5l1-1 1-1h0c1-1 2-2 2-3h-3l-1-1 1-1h0c1 0 2 0 2-1h1 1z" class="l"></path><path d="M627 522l1 1c1-1 2-2 3-2s2 0 3-1v2h-2 0c-1 1-1 1-1 2l-1 1-1-1s-1-1-2-1v-1z" class="i"></path><path d="M623 522l1-1c0 2 1 2 1 3l1 1c0 1 1 2 2 3h0l1-1c0 1 1 2 1 2l-1 1h-2s-2-2-2-3l-2 1v-1h0c1-2 1-3 0-5z" class="T"></path><path d="M631 516l1-1 1 1h0c1-1 1 0 1-1v5c-1 1-2 1-3 1s-2 1-3 2l-1-1v-2c1-2 2-3 3-4h1z" class="U"></path><path d="M630 516h1 1c0 2-2 3-2 5h-1c-1 0-1 0-2-1 1-2 2-3 3-4z" class="e"></path><path d="M605 460l1-2h1v2c-1 2-3 3-3 5-1 1-1 1-1 2l1 1h0l1 1 2-1-1 4-3 4c0 1-1 2-2 3h0c-1 2-3 3-6 3l-3 2v-1c1-1 2-4 3-6l7-15h0l2-3h0l1 1z" class="f"></path><path d="M605 460l1-2h1v2c-1 2-3 3-3 5-1 1-1 1-1 2l1 1h0c-3 3-5 7-8 10l3-6c1-3 2-5 3-8 1-1 2-3 3-4z" class="E"></path><path d="M605 469l2-1-1 4-3 4c0 1-1 2-2 3h0c-1 2-3 3-6 3 1-1 1-1 1-2 2-2 3-3 4-5 2-2 3-4 5-6z" class="V"></path><path d="M587 492h0 4c0 1 1 1 2 2h0l-5 6c2 0 3-1 4-2 3-1 6-2 9-2l-1 1 1 2h0c1 0 3-1 4-2h1l-1 1c0 1 0 1 1 2-1 0-2 0-2 1l-1 1s0 1-1 1v1c-2 0-3 2-5 3-1-1-1-1-2 0l-2-2-1 1v-3c-4 1-2 0-4-1h-1-1c-1 1-2 1-3 1 1-2 4-3 4-6l-1 1-1 1c0-2 2-5 2-7z" class="B"></path><path d="M588 494c1 0 1-1 2 0v1l-1 1c-1 0-1 0-1 1-1-2 0-2 0-3z" class="k"></path><path d="M592 503l1-1s1 0 1-1v-1h1 1c0 2 0 3-1 4h-1c0 1-1 1-1 1l-1 1v-3z" class="C"></path><path d="M604 501l-1 1s0 1-1 1v1l-1-2c-1 1-3 2-4 2v1c-1 0-1 1-2 0l-1-1h1l6-3h3z" class="G"></path><path d="M594 504l1 1c1 1 1 0 2 0v-1c1 0 3-1 4-2l1 2c-2 0-3 2-5 3-1-1-1-1-2 0l-2-2s1 0 1-1z" class="R"></path><path d="M615 441h1v-1l1 1c-1 1-2 3-2 4v1 1c-1 1-1 2-1 3v1c0 1-1 2-2 3-2 4-5 8-8 11 0-2 2-3 3-5v-2h-1l-1 2-1-1h0l-2 3h0v-1c-1 0-1 0-2-1h0l-2 2-1 1v-2l-1-1c0-1 1-2 2-2 1-3 2-4 4-6s5-4 7-6c1 1 1 1 2 1l3-3c1-1 1-2 1-3z" class="P"></path><path d="M603 457c1-1 2-3 4-4-1 3-1 4-3 6h0c0-1-1-1-1-2z" class="E"></path><path d="M600 460l3-3c0 1 1 1 1 2l-2 3h0v-1c-1 0-1 0-2-1h0z" class="D"></path><path d="M602 452c2-2 5-4 7-6 1 1 1 1 2 1-1 2-2 3-3 4s0 2 0 3h-1c0-1 0-1-1-2h-1c-1 0-2 0-2 1l-1-1z" class="C"></path><path d="M602 452l1 1c0-1 1-1 2-1h1c-2 2-8 7-8 10l-1 1v-2l-1-1c0-1 1-2 2-2 1-3 2-4 4-6z" class="L"></path><path d="M608 537h2c2 1 2 4 3 6-2 4-3 9-6 12l-1-1 1-1h-1-1c0-1 1-2 1-3 0-2 1-4 0-6-1-1-1-1-3-1h0v2 1c-2 1-4 4-6 4-1 1-2 2-3 2h-1-2v-1-1l3-3v-1c1-1 2-2 2-4h2l10-5z" class="m"></path><path d="M596 542h2c1 0 1 0 1 2l-5 3v-1c1-1 2-2 2-4z" class="V"></path><path d="M606 550c0-1 1-1 1-2l1-1c-1-1-1-1 0-1l1-2 1 1c0 3-2 5-4 7v1h-1c0-1 1-2 1-3z" class="o"></path><path d="M608 537h2v3c-1 1-2 2-3 2h-1-1l-1-1h0c-1 1-1 1-2 1s-2 1-3 2c0-2 0-2-1-2l10-5z" class="E"></path><path d="M595 548c2-1 5-4 8-5v2 1c-2 1-4 4-6 4-1 1-2 2-3 2h-1-2v-1c2-1 3-2 4-3z" class="L"></path><path d="M591 551c2-1 3-2 4-3l-1 2 1 1 2-1c-1 1-2 2-3 2h-1-2v-1z" class="D"></path><path d="M583 541h0c1 0 2 0 3-1v1 1c1 0 1 0 2-1h0c2 1 3 0 4 1l1 1 3-1c0 2-1 3-2 4v1l-3 3v1 1h2 1l-1 1c-1 0-2 1-3 1-2 0-3 0-5-1h-1l-2 1-1 1h-2l1-1h0c-1-1-1-1-2-1-2 1-3 2-5 2 2-2 4-5 5-7l2-1c0-2 0-3 1-4 1 0 2 0 2-1v-1z" class="Z"></path><path d="M589 546h0c-1 1-1 2-2 3h1 2c-3 2-7 2-8 5l-1 1h-2l1-1h0c-1-1-1-1-2-1 0-1 1-1 2-1l1 1c0-1 0-1 1-1 1-1 4-2 5-4 0 0 1-1 1-2h1z" class="N"></path><path d="M593 543l3-1c0 2-1 3-2 4l-1 1h-1c-1 1-2 1-2 2h0-2-1c1-1 1-2 2-3h0 0l1-1 1-1c0-1 0-1 2-1z" class="M"></path><path d="M590 549h0c0-1 1-1 2-2h1l1-1v1l-3 3v1 1h2 1l-1 1c-1 0-2 1-3 1-2 0-3 0-5-1h-1l-2 1c1-3 5-3 8-5z" class="H"></path><path d="M585 553l1-1c2-1 4-1 5-2v1 1h2 1l-1 1c-1 0-2 1-3 1-2 0-3 0-5-1z" class="E"></path><path d="M583 541h0c1 0 2 0 3-1v1 1c1 0 1 0 2-1h0c2 1 3 0 4 1l1 1c-2 0-2 0-2 1l-1 1-1 1c0-1 0-1-1-1h-1c0 1-1 1-1 1h-1l-1 1h-1c0 1 0 1-1 1h0l-2 2h-1l-1 1v-1-2l2-1c0-2 0-3 1-4 1 0 2 0 2-1v-1z" class="B"></path><path d="M583 541h0c1 0 2 0 3-1v1 1c1 0 1 0 2-1h0c2 1 3 0 4 1-5 0-8 1-12 5 0-2 0-3 1-4 1 0 2 0 2-1v-1z" class="g"></path><path d="M601 496c5-1 13-2 17 1 1 1 2 1 2 2s0 1-1 2h-1c-1 1-1 1-1 3h-3c-1 1-2 1-3 2l-6 3h-3 0c-1 1 0 1-1 1v1h-2v-1-1h-1-1v-2c2-1 3-3 5-3v-1c1 0 1-1 1-1l1-1c0-1 1-1 2-1-1-1-1-1-1-2l1-1h-1c-1 1-3 2-4 2h0l-1-2 1-1z" class="I"></path><path d="M605 497c1-1 1-1 2-1 1 1 2 1 2 2 0 2 0 3-1 4s-1 1-2 1v-1-1l1-1h-1c-1-1-1-1-1-2l1-1h-1z" class="M"></path><path d="M606 500h1l-1 1v1 1c-3 1-4 3-6 4l-1 1 2 1-1 1h1v1h-2v-1-1h-1-1v-2c2-1 3-3 5-3v-1c1 0 1-1 1-1l1-1c0-1 1-1 2-1z" class="D"></path><path d="M601 557h1c1 1 1 1 0 2l-14 15c-1 1-4 3-4 4l-2 2c0-2 1-2 1-3h0c0-1 1-3 2-4l1-2c-1 0-1-1-1-1h-1l1-1v-1l-1 1c0-1 0-1-1-1v-1h-2c-1 0 0-1-1-2v-1c1-1 2-2 4-3v-1c1 1 0 1 1 1 2-1 3-3 5-3h0c1 0 1-1 2-1 0 0 1 1 2 1h0c1 0 2 0 3 1 1 0 2-1 4-2z" class="P"></path><path d="M590 568c1-1 2-1 3-1l-10 10h0c0-1 1-3 2-4l1-2c-1 0-1-1-1-1h-1l1-1v-1h1 4z" class="a"></path><path d="M586 568h4l-4 3c-1 0-1-1-1-1h-1l1-1v-1h1z" class="O"></path><path d="M594 558c1 0 2 0 3 1 1 0 2-1 4-2v1h-1c-3 2-4 6-7 8l-1-1h-1c-1 1-3 2-4 1h0c1-1 2-2 4-3v-1s0-1 1-2l2-2h0z" class="U"></path><path d="M592 563h1 0l-1 1h-1v-1h1z" class="o"></path><path d="M590 558h0c1 0 1-1 2-1 0 0 1 1 2 1l-2 2c-1 1-1 2-1 2v1c-2 1-3 2-4 3h0c-1 1-1 1-1 2h-1l-1 1c0-1 0-1-1-1v-1h-2c-1 0 0-1-1-2v-1c1-1 2-2 4-3v-1c1 1 0 1 1 1 2-1 3-3 5-3z" class="G"></path><path d="M580 565l4 2c0-1 1-2 2-2v1h1c-1 1-1 1-1 2h-1l-1 1c0-1 0-1-1-1v-1h-2c-1 0 0-1-1-2z" class="S"></path><path d="M587 566c0-1-1-1-1-1v-1-1l1-1 1-1c1 1 1 0 2 0l1 2c-2 1-3 2-4 3z" class="R"></path><path d="M621 474h1l2 1c0 1 0 1 1 2h2v2h0l-1 2c-1 2-2 4-3 5s-1 2-2 2c0 2-1 4-3 5-1 1 0 0-1 0l-1 1c-1 0-1-1-2-1-1 1-2 1-2 0-1-1-4 0-5 0 0 0 0-1-1-1v-1l1-1h-1c-1 1-1 2-2 3h-1c0-1 3-3 3-5-1 0-1 0-2-1-1 2-2 3-4 4-1 1-2 2-3 2h-1c1-1 2-1 2-2 0-2 1-3 3-4l1 1 2-2 7-8c1 0 0 0 1 1 1-1 2-1 3-1 2-1 4-2 6-4z" class="j"></path><path d="M616 485c-1 0-3 1-3 2s1 1 0 2c0-1-1-1-1-1-1 1-3 2-4 2l6-7 2 2z" class="S"></path><path d="M621 474l2 2c-1 1-2 1-2 2-2 3-3 5-5 7l-2-2c1-1 3-2 4-4h0l-4 3c-1-1 1-3 1-4 2-1 4-2 6-4z" class="J"></path><path d="M619 486l2-1h1l-1 2v1c0 2-1 4-3 5-1 1 0 0-1 0l-1 1c-1 0-1-1-2-1h0c-1-1-1-1-1-2l2-1s2-1 2-2h0c1-1 1-2 2-2z" class="b"></path><path d="M625 477h2v2h0l-1 2c-1 2-2 4-3 5s-1 2-2 2v-1l1-2h-1l-2 1v-1c0-1 1-3 2-4 1-2 2-3 4-4z" class="E"></path><path d="M624 480c0-1 1-2 1-2h1l1 1-1 2h-2v-1z" class="H"></path><path d="M624 480v1h2c-1 2-2 4-3 5l-1-1c1 0 1 0 2-1-1-1-1-1-2-1h0c0-2 0-2 1-3h0 1z" class="D"></path><path d="M604 486l7-8c1 0 0 0 1 1 1-1 2-1 3-1 0 1-2 3-1 4l-4 4c-2 1-3 3-4 4 1-3 3-5 3-7l-4 3h-1z" class="G"></path><path d="M620 499c1 2 1 3 1 5v1 1c0 1 0 2 1 3 0 1 0 1 1 2-1 2-2 3-3 4l-2 2c-1 1-2 1-3 1-2 1-2 1-3 1v1h2l-1 1h-2c-1 1-3 1-4 1l-2 1c0-1-1-1-1-2-1 1-2 3-4 3-1 0-1-1-3-1h0l-2-1c2-1 5-2 7-3l2-1c-1-1-1-2-1-2v-1-1h-1c-1-1-2-1-4-1h-1v-1h1l1-1h2v-1c1 0 0 0 1-1h0 3l6-3c1-1 2-1 3-2h3c0-2 0-2 1-3h1c1-1 1-1 1-2z" class="U"></path><path d="M617 504c0-2 0-2 1-3h1v2c-1 3-2 4-3 6h0v-1c0-1 0-2 1-3v-1z" class="H"></path><path d="M620 499c1 2 1 3 1 5v1c-1 2-1 4-3 5 0 1 0 1-1 2l-1 1h-1v-1c2-3 5-6 5-11h-1v2-2c1-1 1-1 1-2z" class="E"></path><path d="M616 513l1-1c1-1 1-1 1-2 2-1 2-3 3-5v1c0 1 0 2 1 3 0 1 0 1 1 2-1 2-2 3-3 4l-2 2c-1-1-1-2-1-3v-1h-1z" class="G"></path><path d="M613 512c0 2-1 2-1 3 1 0 2 0 3-1l1-1v1c-1 1-2 3-2 4h1c-2 1-2 1-3 1v1h2l-1 1h-2c-1 1-3 1-4 1l-2 1c0-1-1-1-1-2-1 1-2 3-4 3-1 0-1-1-3-1h0l-2-1c2-1 5-2 7-3l2-1c2-1 3-1 4-2h1l2-2c0-1 1-1 2-2z" class="Y"></path><path d="M613 512c0 2-1 2-1 3 1 0 2 0 3-1l1-1v1c-1 1-2 3-2 4h1c-2 1-2 1-3 1v1h2l-1 1h-2c-1 1-3 1-4 1l-2 1c0-1-1-1-1-2h2c2-1 2-1 3-2 1 0 1 0 2-1 1 0 1 0 2-1l-1-1h-1-2l2-2c0-1 1-1 2-2z" class="g"></path><path d="M617 504v1c-1 1-1 2-1 3v1h0l-3 3c-1 1-2 1-2 2l-2 2h-1c-1 1-2 1-4 2-1-1-1-2-1-2v-1-1h-1c-1-1-2-1-4-1h-1v-1h1l1-1h2v-1c1 0 0 0 1-1h0 3l6-3c1-1 2-1 3-2h3z" class="E"></path><path d="M603 514c1 2 5 1 5 2-1 1-2 1-4 2-1-1-1-2-1-2v-1-1z" class="D"></path><path d="M601 511c1 0 2-1 2 0h2v1c0 1-2 1-2 2h-1c-1-1-2-1-4-1h-1v-1h1l1-1h2z" class="m"></path><path d="M617 504v1c-1 1-1 2-1 3v1h0l-3 3c-1 1-2 1-2 2h-6c3-2 6-3 8-6-1 0-2 1-3 1h-2c1-1 2-2 3-2v-1c1-1 2-1 3-2h3z" class="B"></path><path d="M578 553c1 0 1 0 2 1h0l-1 1h2l1-1 2-1h1c2 1 3 1 5 1v1h0l1 1-1 2c-2 0-3 2-5 3-1 0 0 0-1-1v1c-2 1-3 2-4 3v1l-3 3-1 1-4 3c0 1-1 1-1 1 1 1 2 1 2 2v1h0c-2 0-4 1-6 2-1 0-3 1-4 2h-6-1v-2l-1 1h0c-1 0-2 0-2 1h-5c-5 0-9 1-13 4v-1c2-1 3-2 5-3s3-2 5-3c-1 0-1 0-1-1v-1c0-1 0-2-1-3 2-1 4-3 5-4l2 1 1-1c1-1 2-1 4-1v1c2 0 3-1 4-2l9-6c2-1 3-3 5-5 2 0 3-1 5-2z" class="G"></path><path d="M564 572l1 1-3 2-2-2 2-1h1 1z" class="f"></path><path d="M549 575v1c1-1 1-1 2-1 1-1 6-4 7-3h0c1 1 1 1 2 0v1l2 2c-2 1-4 1-5 3v-4h0c-2 0-3 1-4 2l-3 1h-1-1v-1l1-1zm29-11c1 0 1 0 2 1l-3 3-1 1-4 3c0 1-1 1-1 1h-6l-1-1c1 0 2-1 2-1h2c2-2 4-3 7-4 0-1 1-2 2-3h1z" class="a"></path><path d="M568 571c2 0 3 0 4-1l3-1h1l-4 3c0 1-1 1-1 1h-6l-1-1c1 0 2-1 2-1h2z" class="K"></path><path d="M565 573h6c1 1 2 1 2 2v1h0c-2 0-4 1-6 2-1 0-3 1-4 2h-6-1v-2h1c1-2 3-2 5-3l3-2z" class="M"></path><path d="M550 572h0l-1 3-1 1v1h1 1l3-1c1-1 2-2 4-2h0v4h-1l-1 1h0c-1 0-2 0-2 1h-5c-5 0-9 1-13 4v-1c2-1 3-2 5-3s3-2 5-3c-1 0-1 0-1-1v-1l4-2 2-1z" class="Y"></path><path d="M585 553c2 1 3 1 5 1v1h0l1 1-1 2c-2 0-3 2-5 3-1 0 0 0-1-1v1c-2 1-3 2-4 3v1c-1-1-1-1-2-1h-1s-2 0-2 1c-1 0-1 1-2 2h-3c-1 1 0 1-1 1v-1l1-2v-1h1l1-1c3-2 5-5 7-8h2l1-1 2-1h1z" class="F"></path><path d="M582 559c1 0 1 1 2 2-2 1-3 2-4 3v1c-1-1-1-1-2-1l4-5z" class="h"></path><path d="M582 554l2-1c1 2 1 1 1 3l-1 1-1-1c-1 1-1 2-2 3h-1l-1-2 2-2 1-1z" class="G"></path><path d="M579 555h2l-2 2h0c-2 3-2 6-5 8h-1c-1 1-1 1-2 1h0v-1-1l1-1c3-2 5-5 7-8z" class="D"></path><path d="M585 553c2 1 3 1 5 1v1h0l1 1-1 2c-2 0-3 2-5 3-1 0 0 0-1-1v1c-1-1-1-2-2-2l2-2 1-1c0-2 0-1-1-3h1z" class="O"></path><path d="M590 555l1 1-1 2c-2 0-3 2-5 3-1 0 0 0-1-1 2-2 4-3 6-5z" class="J"></path><path d="M578 553c1 0 1 0 2 1h0l-1 1c-2 3-4 6-7 8l-1 1h-1c-2 2-5 6-8 7l-1-1v-1h-1c-1 1-3 2-5 2l-2 1c-1-1 0-1 0-2h0c-1 0-3 1-3 2l-2 1-4 2c0-1 0-2-1-3 2-1 4-3 5-4l2 1 1-1c1-1 2-1 4-1v1c2 0 3-1 4-2l9-6c2-1 3-3 5-5 2 0 3-1 5-2z" class="M"></path><path d="M548 573l-1-1h0l2-2h1c1-1 2 0 3-1h1 1 2 1 1v-1c4 1 6-1 9-4 0 0 1-1 2-1 0-1 2 0 2 0l-1 1h-1c-2 2-5 6-8 7l-1-1v-1h-1c-1 1-3 2-5 2l-2 1c-1-1 0-1 0-2h0c-1 0-3 1-3 2l-2 1z" class="N"></path><path d="M592 506l1-1 2 2c1-1 1-1 2 0v2h1 1v1 1l-1 1h-1v1h1c2 0 3 0 4 1h1v1 1s0 1 1 2l-2 1c-2 1-5 2-7 3l2 1-3 3c-1 0-2 1-3 1v1c1 0 2 0 3-1h1c0-1 0-1 1-1h0c0 1-1 2-2 3h0c-1 1-1 2-1 3 1 0 3-1 4 0v1h0c0 3-1 4-3 5l-6 3h0c-1 1-1 1-2 1v-1-1c-1 1-2 1-3 1h0v1c0 1-1 1-2 1-1 1-1 2-1 4l-2 1c-1 2-3 5-5 7s-3 4-5 5l-9 6c-1 1-2 2-4 2v-1c-2 0-3 0-4 1l-1 1-2-1c-1 1-3 3-5 4 0 1 0 1-1 2-1 0-1 1-2 1v1h-2c-2 1-3 2-4 2h-2-2s0-1 1-1l-1-1c2 0 5 0 7-1 1-1 0-2 1-3 0-1 2-3 3-4 1 0 1-1 2-1v1l1-1v-2h-2c1-1 3-3 4-5 0-1 0 0 1-1 0-1 1-2 1-2 1-2 0 0 1-1v-1-1c1 0 1 0 1-1s1-1 1-1l2-2 1-1h0l1-1-1-1v-1c-1 0-1 1-2 1h-2v-1h1 1c1-1 2-2 4-3l2-1 3-3 16-15-1-1-1 1-1-1s-1 0-1-1l1-2h0c2-2 4-4 7-5l1-1c2-1 3-2 4-2s3-1 4-2h1c0-2-1-2 0-3 1 0 1-1 1-1z" class="I"></path><path d="M561 539l2 1c-1 2-2 3-4 4h-1v-2l3-3z" class="B"></path><path d="M556 543l2-1v2l-4 3v-1c-1 0-1 1-2 1h-2v-1h1 1c1-1 2-2 4-3z" class="D"></path><path d="M555 548c1-1 1-2 3-2l1 1c1 0 2-1 3-1v1c-1 0-1 1-2 1l-1-1c-1 1-2 1-2 2-1 1-2 2-2 3l-2-2 1-1h0l1-1z" class="E"></path><defs><linearGradient id="AL" x1="574.597" y1="537.405" x2="570.835" y2="546.351" xlink:href="#B"><stop offset="0" stop-color="#b4b3b2"></stop><stop offset="1" stop-color="#d5d4d6"></stop></linearGradient></defs><path fill="url(#AL)" d="M565 547c1-1 3-2 5-3 3-3 5-6 8-8v1l1-1 1 1s-1 1-1 2c-3 2-7 4-9 7-1 0-1 0-2 1-1 0-1 0-2 1l-1-1z"></path><path d="M555 552c0-1 1-2 2-3 0-1 1-1 2-2l1 1c-2 2-3 3-4 5l-3 2 1 1 1 1c1-1 3-2 4-3v1c-1 1-3 4-5 5-1 2-3 3-5 4v1c-1 0-2 0-3 1l-2 1v-2h-2c1-1 3-3 4-5 0-1 0 0 1-1 0-1 1-2 1-2 1-2 0 0 1-1v-1-1c1 0 1 0 1-1s1-1 1-1l2-2 2 2z" class="F"></path><path d="M553 550l2 2-2 2-2-2 2-2z" class="Z"></path><path d="M551 552l2 2-3 3h0c-1-1-1-1-1-2v-1c1 0 1 0 1-1s1-1 1-1z" class="Q"></path><path d="M544 565v-1c1-1 2-2 4-3 0 2-1 3-1 4l-1 1-2 1v-2z" class="P"></path><path d="M548 561c1 0 4-2 5-3-1 2-3 3-4 5 2-1 3-2 4-3h1c-1 2-3 3-5 4v1c-1 0-2 0-3 1l1-1c0-1 1-2 1-4z" class="i"></path><path d="M590 520v1s0 1-1 2v1c2 0 5-2 6-2l2 1-3 3c-1 0-2 1-3 1v1c1 0 2 0 3-1h1c0-1 0-1 1-1h0c0 1-1 2-2 3h0c-1 1-1 2-1 3h-1v-1h-1c-1 0-2-1-3 0h-1s-2 1-3 1h-1c-1 0-2 1-3 2h0c0 1 0 2-1 2l-1 1v-1c0-2-1-2-1-4l6-6c3-2 4-4 7-6z" class="K"></path><path d="M588 531c1-1 2 0 3 0h1v1h1c1 0 3-1 4 0v1h0c0 3-1 4-3 5l-6 3h0c-1 1-1 1-2 1v-1-1c-1 1-2 1-3 1h0l-2-1h0c-1 0-2 0-3 1 0-1 1-1 1-2s1-2 1-2l-1-1c1 0 1-1 1-2h0c1-1 2-2 3-2h1c1 0 3-1 3-1h1z" class="G"></path><path d="M593 532c1 0 3-1 4 0v1 1h-1c-2 0-4 3-6 4v-1l3-3v-1h0l1-1h-2 1z" class="H"></path><path d="M580 537l3-3h1v1c-1 0-1 1-2 1v1c1 0 1 0 1 1l-2 2h0c-1 0-2 0-3 1 0-1 1-1 1-2s1-2 1-2z" class="D"></path><path d="M583 538l3-2h0l-1 2c1 0 1 1 1 2 1-1 1-1 2-1v1l-1 1h1c-1 1-1 1-2 1v-1-1c-1 1-2 1-3 1h0l-2-1 2-2z" class="P"></path><path d="M588 531c1-1 2 0 3 0h1v1h2l-1 1h0l-1 1-2 2c-1 0-1 1-2 0-1 0 0-1 0-2h-1l-1 1h-1l2-4h1z" class="B"></path><path d="M588 531c1-1 2 0 3 0h1c-1 2-1 2-2 3-1 0-1 0-2-1v-2z" class="C"></path><path d="M592 506l1-1 2 2c1-1 1-1 2 0v2h1 1v1 1l-1 1h-1v1h1c2 0 3 0 4 1h1v1 1s0 1 1 2l-2 1c-2 1-5 2-7 3-1 0-4 2-6 2v-1c1-1 1-2 1-2v-1h0c1-1 1-1 1-2h0l-1-1-1-1h-1l-1-2c-2 0-3 2-4 3-2 2-4 5-6 7l-1-1-1 1-1-1s-1 0-1-1l1-2h0c2-2 4-4 7-5l1-1c2-1 3-2 4-2s3-1 4-2h1c0-2-1-2 0-3 1 0 1-1 1-1z" class="d"></path><path d="M581 515l1-1c2-1 3-2 4-2-2 3-4 4-6 6h-1c1-1 1-2 2-3h0 0z" class="B"></path><path d="M581 515h0 0c-1 1-1 2-2 3h1c-1 2-2 2-4 3-1 0-1 0-2-1 2-2 4-4 7-5z" class="V"></path><path d="M592 506l1-1 2 2c1-1 1-1 2 0v2h1 1v1 1l-1 1h-1v1h1c-1 1-2 1-3 1h0c-1 1-2 1-3 2l-1-1h1v-1h-1l-1 1c-1-1-1-1-2-1 1-1 1-2 2-4h1c0-2-1-2 0-3 1 0 1-1 1-1z" class="G"></path><path d="M592 506l1-1 2 2c1-1 1-1 2 0v2h1 1v1 1l-1 1c-1 0-1 0-2-1 0 1-1 1-1 1-1-1-1 0-2-1v-1c2 0 2 0 3-1v-1h-2 0c-2 0-2 1-3 2 0-2-1-2 0-3 1 0 1-1 1-1z" class="d"></path><path d="M598 513c2 0 3 0 4 1h1v1 1s0 1 1 2l-2 1c-2 1-5 2-7 3-1 0-4 2-6 2v-1c1-1 1-2 1-2v-1h0c1-1 1-1 1-2h0l-1-1-1-1h-1c1 0 2-1 2-1l1 1h1c1-1 2-1 3-2h0c1 0 2 0 3-1z" class="B"></path><path d="M597 517l6-1s0 1 1 2l-2 1-2-2-1 1-1 1-1-1v-1z" class="E"></path><path d="M598 513c2 0 3 0 4 1h1v1 1l-6 1h-4-2v1l-1-1-1-1h-1c1 0 2-1 2-1l1 1h1c1-1 2-1 3-2h0c1 0 2 0 3-1z" class="M"></path><defs><linearGradient id="AM" x1="554.597" y1="553.533" x2="567.632" y2="558.311" xlink:href="#B"><stop offset="0" stop-color="#c5c4c5"></stop><stop offset="1" stop-color="#f5f3f4"></stop></linearGradient></defs><path fill="url(#AM)" d="M579 539c0 1-1 1-1 2 1-1 2-1 3-1h0l2 1v1c0 1-1 1-2 1-1 1-1 2-1 4l-2 1c-1 2-3 5-5 7s-3 4-5 5l-9 6c-1 1-2 2-4 2v-1c-2 0-3 0-4 1l-1 1-2-1c-1 1-3 3-5 4 0 1 0 1-1 2-1 0-1 1-2 1v1h-2c-2 1-3 2-4 2h-2-2s0-1 1-1l-1-1c2 0 5 0 7-1 1-1 0-2 1-3 0-1 2-3 3-4 1 0 1-1 2-1v1l1-1 2-1c1-1 2-1 3-1v-1c2-1 4-2 5-4 2-1 4-4 5-5v-1h0c2-2 3-5 6-7l1 1c1-1 1-1 2-1 1-1 1-1 2-1 2-3 6-5 9-7z"></path><path d="M564 559c1 1 1 1 2 1h2l-9 6-5 1c3-3 7-4 10-8h0z" class="g"></path><path d="M549 565h0l2-1c3-1 6-4 8-6 1-1 2-3 4-4l-3 3c-4 4-7 8-12 11-1 1-3 3-5 4 0 1 0 1-1 2-1 0-1 1-2 1v1h-2c-2 1-3 2-4 2h-2-2s0-1 1-1l-1-1c2 0 5 0 7-1 1-1 0-2 1-3 0-1 2-3 3-4 1 0 1-1 2-1v1l1-1 2-1c1-1 2-1 3-1z" class="G"></path><path d="M541 568c1 0 1-1 2-1v1c-1 2-4 6-6 7 1-1 0-2 1-3 0-1 2-3 3-4z" class="l"></path><path d="M579 539c0 1-1 1-1 2 1-1 2-1 3-1h0l2 1v1c0 1-1 1-2 1-1 1-1 2-1 4l-2 1c-1 2-3 5-5 7s-3 4-5 5h-2c-1 0-1 0-2-1 1-1 2-1 2-3l-1 1c1-2 4-5 5-6 0-1 0-1-1-2 0-1 1-2 2-3 1 0 1-1 2-1-1 0-2 1-3 1h0c2-3 6-5 9-7z" class="b"></path><path d="M578 541c1-1 2-1 3-1l-7 7s-1 0-1-1c1-2 3-4 5-5z" class="E"></path><path d="M579 539c0 1-1 1-1 2-2 1-4 3-5 5 0 1 1 1 1 1l-4 4c0-1 0-1-1-2 0-1 1-2 2-3 1 0 1-1 2-1-1 0-2 1-3 1h0c2-3 6-5 9-7z" class="N"></path><path d="M587 502h1c2 1 0 2 4 1v3s0 1-1 1c-1 1 0 1 0 3h-1c-1 1-3 2-4 2s-2 1-4 2l-1 1c-3 1-5 3-7 5h0l-1 2c0 1 1 1 1 1l1 1 1-1 1 1-16 15-3 3-2 1c-2 1-3 2-4 3h-1-1v1h2c1 0 1-1 2-1v1l1 1-1 1h0l-1 1-2 2s-1 0-1 1 0 1-1 1v1 1c-1 1 0-1-1 1 0 0-1 1-1 2-1 1-1 0-1 1-1 2-3 4-4 5h2v2l-1 1v-1c-1 0-1 1-2 1-1 1-3 3-3 4-1 1 0 2-1 3-2 1-5 1-7 1-2 1-4 0-6 0l1-1 4-4s-1 0-1 1c-1 1-1 1-3 1h0c0-1 2-1 2-3h0c-2 1-3 2-5 2l-1 1h-2c-2 1-3 2-5 2h-1l-1 2-2-2c-1-1-4-3-6-4v-1c0-1 0-1 1-1h-4c-1 1-1 1-3 1h0c1-1 1-1 1-3 0-1 0-1-1-1h-2l-1-1 1-1h-2c-1 1-1 1-2 1-1-1 0-1 0-2l-1 1-1-1 1-3c1-1 2-2 4-3v-1c1-1 3-2 4-2l-2-2 2-1c1-1 2-1 2-3h1v-2l3-1h2c4-2 8-3 11-7 1 2 1 3 2 4h1l1-1h3l5-4-1-1h0 2c1 0 2-1 3-2l4-3c1 0 1 1 2 2l2-1h0c0 1-1 1 0 3l8-7h1c1 0 2 0 2 1 2-1 4-2 5-3h0c2 0 2 0 3 1l4-4h0c-1-1 0-1-1-1h0 0c4-3 8-8 11-11 2-2 5-4 7-5l5-4z" class="I"></path><path d="M551 528c1 0 2 0 2 1l-2 1c0-1 0-1-1-2h1z" class="a"></path><path d="M567 525c-1-1-1-1-1-2v-1h1l1 1-1 2z" class="k"></path><path d="M570 522l3-2h1l-1 2c-1 1 0 1-1 1l-2-1z" class="C"></path><path d="M570 522l2 1-2 2c-1 1-2 1-4 2l1-2 1-2 2-1z" class="H"></path><path d="M549 542h0c1 1 2 1 4 1l-2 2v-1h-2s-1 1-2 1h0c-1 1-1 2-2 2l3-5h1z" class="L"></path><path d="M553 542c0-1-1-2-1-3v-1h0 2c1-1 2-1 3-1l-4 5z" class="C"></path><path d="M546 540l3-3h1 0c0 1 1 2 1 2 0 1-1 2-2 3h0v-1h-1l-2-1z" class="E"></path><path d="M541 543c2-2 4-5 7-8-1 3-4 5-5 7l1 1v1l-2 1h0l-1-2z" class="R"></path><path d="M527 559l1-2c1 1 1 1 1 2h0 1c1-1 2-1 3-1 0 1-1 2-2 3h0c-1 1-1 1-2 1 0-1 0-1-1-1l-1 1h-1c0-1 0-1 1-2v-1z" class="B"></path><path d="M572 523c1 0 0 0 1-1 0 1 1 1 1 1l1 1-4 3v-1l-2 1c-1 0-2 1-3 1v-1c2-1 3-1 4-2l2-2z" class="E"></path><path d="M558 536c0 1 0 1-1 2 0 1-1 4-1 5-2 1-3 2-4 3h-1v-1l2-2v-1l4-5 1-1z" class="g"></path><path d="M546 540l2 1h1v1h-1l-3 5-2 1c0-1 0-2-1-3l2-1v-1l2-3z" class="P"></path><path d="M546 540l2 1h1v1h-1 0c-1 0-2 1-2 2l-1 1-1-1v-1l2-3z" class="e"></path><path d="M587 502h1c2 1 0 2 4 1v3s0 1-1 1c0-1 1-1 1-2l-1-1c-2 1-3 4-5 4-1-1-2-2-4-2l5-4z" class="M"></path><path d="M541 543l1 2h0c1 1 1 2 1 3-1 1-3 2-5 3v-1l1-1c-1-1-1 0-2 0v-1l1-2 3-3z" class="S"></path><path d="M541 543l1 2c-1 1-1 2-3 2l-1-1 3-3z" class="G"></path><path d="M518 552v1c3 1 6-2 8 1 1 1 1 4 1 5v1c-1 1-1 1-1 2h1l1-1c1 0 1 0 1 1l3 2-1 1h-1l-2-2c-2 0-2 1-2 2h-2c1-3 2-6 2-9-1-1-1-1-1-2h-2-1-2 0l-2-1v-1z" class="J"></path><path d="M533 558l3-3c1-1 2-1 3-1 0 1-1 2-2 3v1c1-1 1-1 2-1 0 1-1 1-1 1-2 2-3 3-4 5l-2 1-3-2c1 0 1 0 2-1h0c1-1 2-2 2-3z" class="D"></path><path d="M546 551v1c0 1-1 2-2 3h0c0 2-2 3-3 4h-1c-2 1-3 4-5 4h-1c1-2 2-3 4-5 0 0 1 0 1-1-1 0-1 0-2 1v-1c1-1 2-2 2-3l4-1c1 1-1 1 0 2 0 0 1-1 1-2 0 0 0-1 1-1l1-1z" class="E"></path><path d="M550 528c1 1 1 1 1 2l-17 15c0-2 1-3 3-4v-1h-2l7-5 8-7zm2 19c1 0 1-1 2-1v1l1 1-1 1h0l-1 1-2 2s-1 0-1 1 0 1-1 1v1 1c-1 1 0-1-1 1 0 0-1 1-1 2-1 1-1 0-1 1-1-1-2 0-3 0h-1c-1 0 0 0-1-1 1-1 3-2 3-4h0c1-1 2-2 2-3v-1l2-1c1-1 0-1 1-2h0c1 0 2 0 3-1z" class="f"></path><path d="M552 547c1 0 1-1 2-1v1l1 1-1 1-4 2c0-1-1-1-1-2v-1c1 0 2 0 3-1z" class="V"></path><path d="M546 551l2-1c1-1 0-1 1-2h0v1c0 1 1 1 1 2-3 3-5 6-7 9h-1c-1 0 0 0-1-1 1-1 3-2 3-4h0c1-1 2-2 2-3v-1z" class="D"></path><path d="M534 563h1c2 0 3-3 5-4h1l-3 3c-3 3-5 6-7 8-1 1-1 1-2 1 0 0-1 0-1 1-1 1-1 1-3 1h0c0-1 2-1 2-3h0c-2 1-3 2-5 2l-1 1h-2c2-3 5-5 5-8h2c0-1 0-2 2-2l2 2h1l1-1 2-1z" class="P"></path><path d="M575 524l1-1 1 1-16 15-3 3-2 1c0-1 1-4 1-5 1-1 1-1 1-2s1-2 2-3c1-2 2-3 4-4 0-1 1-1 2-1s2-1 3-1l2-1v1l4-3z" class="F"></path><path d="M566 528c1 0 2-1 3-1l2-1v1c-1 1-4 5-6 6v-1c-2 0-2 3-4 4h-1v-1-2c1-2 2-3 4-4 0-1 1-1 2-1z" class="C"></path><path d="M529 571c1 0 1 0 2-1 2-2 4-5 7-8l3-3c1 1 0 1 1 1h1c1 0 2-1 3 0-1 2-3 4-4 5h2v2l-1 1v-1c-1 0-1 1-2 1-1 1-3 3-3 4-1 1 0 2-1 3-2 1-5 1-7 1-2 1-4 0-6 0l1-1 4-4z" class="b"></path><path d="M542 565h2v2l-1 1v-1c-1 0-1 1-2 1s-3 2-4 3-2 2-3 2c1-2 3-3 4-5 1-1 2-2 4-3z" class="T"></path><path d="M529 571c1 0 1 0 2-1 2-2 4-5 7-8l3-3c1 1 0 1 1 1-1 2-4 4-6 7-2 2-5 5-8 7-1 1-1 1-3 1l4-4z" class="E"></path><path d="M538 531c1 0 1 1 2 2l2-1h0c0 1-1 1 0 3l-7 5h2v1c-2 1-3 2-3 4-3 2-5 3-8 3-1 1-7 1-7 2-1 1-1 2-1 2v1l2 1h0 2 1 2c0 1 0 1 1 2 0 3-1 6-2 9 0 3-3 5-5 8-2 1-3 2-5 2h-1l-1 2-2-2c-1-1-4-3-6-4v-1c0-1 0-1 1-1h-4c-1 1-1 1-3 1h0c1-1 1-1 1-3 0-1 0-1-1-1h-2l-1-1 1-1h-2c-1 1-1 1-2 1-1-1 0-1 0-2l-1 1-1-1 1-3c1-1 2-2 4-3v-1c1-1 3-2 4-2l-2-2 2-1c1-1 2-1 2-3h1v-2l3-1h2c4-2 8-3 11-7 1 2 1 3 2 4h1l1-1h3l5-4-1-1h0 2c1 0 2-1 3-2l4-3z" class="I"></path><path d="M498 556c-1 1-2 2-4 2 0 1-2 2-2 2v2c1 0 1-1 2-1v-1h3 1 1l-4 3-1 1c-1 1-1 1-2 1-1-1 0-1 0-2l-1 1-1-1 1-3c1-1 2-2 4-3 1-1 2-1 3-1z" class="N"></path><path d="M505 569h2 0v1c2 1 4 1 5 1 0 1-1 1-2 2h1l4-1c-1 1-2 1-3 2v1h2-1l-1 2-2-2c-1-1-4-3-6-4v-1c0-1 0-1 1-1z" class="c"></path><path d="M505 559l1-1h1c-1 1-2 1-2 2h0l1 1c0 1-1 1-2 2h0 2v1h1c0 1-1 2-2 2l-1 1-3 1v1c-1 1-1 1-3 1h0c1-1 1-1 1-3 0-1 0-1-1-1h-2l-1-1 1-1h-2l1-1 4-3h0c1 0 3-2 4-2s2 1 2 1z" class="O"></path><path d="M499 560h0c1 0 3-2 4-2s2 1 2 1l-9 5h-2l1-1 4-3z" class="B"></path><path d="M507 545c4-2 8-3 11-7 1 2 1 3 2 4h1c-1 1-2 2-4 3 0 0-1 1-2 1v1c1 0 1 1 2 0h1c1 0 1 0 2 1 1-1 1-2 2-2v1h2 1l-1 1h1 1c-1 1-7 1-7 2-1 1-1 2-1 2v1l2 1h0-4c0-1 0-1 1-2v-2c-1-1-5-2-6-2-4 1-6 3-10 5l-3 3c-1 0-2 0-3 1v-1c1-1 3-2 4-2l-2-2 2-1c1-1 2-1 2-3h1v-2l3-1h2z" class="K"></path><path d="M502 546l3-1h2v1l1 1h0c-3 2-6 5-9 7l-2-2 2-1c1-1 2-1 2-3h1v-2z" class="F"></path><path d="M507 545c4-2 8-3 11-7 1 2 1 3 2 4h1c-1 1-2 2-4 3 0 0-1 1-2 1v1h-1v-1c1-1 1-2 2-3l-1-1c-2 3-3 4-7 5h0l-1-1v-1z" class="D"></path><path d="M538 531c1 0 1 1 2 2l2-1h0c0 1-1 1 0 3l-7 5h2v1c-2 1-3 2-3 4-3 2-5 3-8 3h-1-1l1-1h-1-2v-1c-1 0-1 1-2 2-1-1-1-1-2-1h-1c-1 1-1 0-2 0v-1c1 0 2-1 2-1 2-1 3-2 4-3l1-1h3l5-4-1-1h0 2c1 0 2-1 3-2l4-3z" class="U"></path><path d="M536 534l1-1h1v2c-1 1-1 2-3 1 0 0 1-1 1-2z" class="S"></path><path d="M538 531c1 0 1 1 2 2l-2 2v-2h-1l-1 1h-2l4-3z" class="J"></path><path d="M525 543c1-1 2-1 3-2 1 0 2-1 2-1-1 2-3 4-5 6 0 0-1 0-1 1h-2v-1l3-3z" class="e"></path><path d="M534 534h2c0 1-1 2-1 2-2 2-3 3-5 4 0 0-1 1-2 1-1 1-2 1-3 2v-1l6-6c1 0 2-1 3-2z" class="E"></path><path d="M535 540h2v1c-2 1-3 2-3 4-3 2-5 3-8 3h-1-1l1-1h-1c0-1 1-1 1-1 3-1 7-4 10-6z" class="W"></path><path d="M529 536h2l-6 6v1l-3 3c-1 0-1 1-2 2-1-1-1-1-2-1h-1c-1 1-1 0-2 0v-1c1 0 2-1 2-1 2-1 3-2 4-3l1-1h3l5-4-1-1h0z" class="d"></path><path d="M522 541h3l-5 4c-1 0-2 1-2 2h-1c-1 1-1 0-2 0v-1c1 0 2-1 2-1 2-1 3-2 4-3l1-1z" class="Q"></path><path d="M161 402v4h-1c-1-2 0-11 0-13 1-22 7-44 17-63 8-14 20-27 32-37 24-22 56-33 88-34l11 1h6c2 0 4 1 6 1-4 2-8 3-11 5-3 1-5 4-8 5-1 1-2 1-3 1h-2c-2 1-5 2-8 2-3 2-5 5-8 9l-1 1c-1 0-2 0-2 1h-1v1h0c1-1 1-1 2-1 2 0 4-2 5-3 0 1-1 1-1 2h0l-1 1h0c-1 0-2 1-2 2 1-1 3-2 4-3 0 3-3 5-5 7h-1-2c-1 1-1 1-2 1h-2-1c-1 1-2 1-3 1v-1h-2c-2 0-4 2-6 3l-3 3-3 4-2 2 1 1c-2 1-6 4-8 4h-2c-1 1-3 1-4 2-2 1-6 1-7 4-1 2 0 5-1 8-1 2-3 3-3 5l-3 6-1 1c-2 3-6 4-8 8-1 3-2 7-2 10-1 3 0 5 1 7s2 3 2 5c1 4-2 8-4 11l-2 2h0c0 1-1 2 0 3l2 3 1 4h1v-1h1l1 1 2 3v-1-2l3 3h0c0 1-1 1 0 1 0 2 1 3 1 4v1l3 2 2 1h1v2l1-1h1c-1 1-1 1 0 2h1v3c1 1 1 3 1 4v3c-1 1-2 3-3 4 0 2 1 5 2 6h0c1 1 1 1 1 2-1 1 0 1 0 2l1 2c1 2 3 5 5 7l5 3c1 2 2 4 4 4l2 1-1-1 1-2h1v4c2 3 4 6 5 10v2c0 1 1 1 1 2l2 1h1l1 2 1 1h1v-1h1c1 0 1 1 1 2-1 0-1 1-2 1h0c1 1 0 1 1 1s2-1 2 0l-1 2-2-2c-1-1-2-1-3-2h-1c0-1-1-1-2-2l-1-1-9-6c-4-2-9-3-13-5l-2 2h-1-1-2 0v-3h-3c-1 0-3 0-3 1-1 0-1 1-1 1-1 0-1 0-1 1-2 0-2 1-3 2-1-1-3-1-4-2l-2-2c-4 2-9 6-11 10v1l-1-1c-1 3-3 6-4 9l-1-1c-1 4-3 7-4 10-1-3-2-6-3-8v-1l-1-1v-2h0l-2-12c-1-3-3-6-4-8l-6-13s-1-1-1-2l-3-7-2-5c-1-2 0-4 0-7v-3-3h-1v-1l-1-3h-1l-1-1-1 1h-1z" class="k"></path><path d="M186 352h1c1 1 0 1 0 2v1c-1 0-1 0-2-1 0-1 0-1 1-2z" class="X"></path><path d="M198 341h1c1 1 1 1 1 2l-1 1c-1 0-1 0-2-1v-1l1-1z" class="g"></path><path d="M209 348l2-1v1 4l-2-1v-3z" class="E"></path><path d="M202 334h1l1 1c0 1 0 2-1 3h-1l-1-1c0-1 0-2 1-3z" class="h"></path><path d="M206 443h1l1 1-2 2h-2v-1c0-1 1-1 2-2z" class="n"></path><path d="M183 407h1c1 0 1 0 1 1 0 2 0 2-1 2h-1c-1 0-1 0-2-1 1-1 1-2 2-2z" class="g"></path><path d="M207 389v2 1l1 1c1 2 1 3 0 5h0c0-2-1-3-3-4 1-1 0-2 0-3 1 0 1-1 2-2z" class="B"></path><path d="M235 306h1v-1c1 0 1-1 2-1 2 1 1 1 2 2-3 2-3 3-7 3h0v-1c1-1 1-2 2-2z" class="H"></path><path d="M197 440c1 0 1 0 1 1l1 4-1 1-1 1h2v1c0 1-1 2-1 2-1 0-1 0-2-1v-3l2-2-2-2v-1l1-1z" class="q"></path><path d="M203 377c1 2 0 4 1 6 0 1 0 1-1 2v1l1 1c1 1 2 1 3 2-1 1-1 2-2 2 0 1 1 2 0 3 0-1-1-2-1-2-3-4-2-10-1-15z" class="G"></path><path d="M205 391c0-1 0-1-1-1-1-2-1-2-1-4v-1 1l1 1c1 1 2 1 3 2-1 1-1 2-2 2z" class="F"></path><path d="M161 402c0-1 1-2 1-4 1-1 0-4 0-5 0-2 0-3 1-5h0c1 1 1 1 1 2l-1 1v1l1 1h0v5h0c1 1 1 2 1 4h-1l-1-1-1 1h-1z" class="q"></path><path d="M212 362c1 1 1 3 2 5h-1v4c-1 1-2 1-3 1 0-1-1-2-1-3l1-1v-1c-1-1-1-1 0-2h0l1 1 1-1v-2-1z" class="Z"></path><path d="M251 300l2 2-2 2 1 1c-2 1-6 4-8 4h-2c3-2 8-6 9-9z" class="f"></path><path d="M193 468c2-2 4-6 3-9v-1-1c-1-1-1-1-1-2 1-2 2-3 3-4h0c1 1 0 2 0 3l1 1c0 2 0 4-1 5-1 3-3 6-4 9l-1-1z" class="Y"></path><path d="M211 433l2-1s1 1 1 2h0l1 1c1 2 1 4 1 6 0 0 0 1-1 1l1 1v1c-1 1-1 1-3 2l-1-1c1-1 1-1 1-2v-1c1-1 0-3 0-4 0-2-1-3-2-5z" class="D"></path><path d="M258 277h2v1l-3 3-4 1c0 1-1 1-1 1l-3 2s-1 0-1 1l-1 1c-1 0-1 1-2 1l-2 2h0l-1-1 2-2c1 0 2-1 2-1l1-1c1 0 0 1 1 0s3-2 4-3v-1c-2 1-3 1-4 2h0l1-2h1c2-2 5-3 8-4z" class="N"></path><path d="M224 317c0-1 1-3 1-4v-1c1 0 1-1 1-1l1-1 1-1v1h0c0 1-1 2-1 2v2h-1v1c1 1 1 1 2 1v3 1c-1 1-2 3-2 4l-1 1c-1 1-1 1-1 2-1-1-1-1-1-2s0-1 1-2v-1c0-1 0-1 1-2v-2l-1-1z" class="B"></path><path d="M202 361h-1v-1c1-2 2-4 2-5 1-2 0-4 1-5s1 0 2 0c3-3 1-7 3-11h0c1 2 0 3 0 4v5 3l2 1-1 4-1-1c-1 0-1 0-1-1l-2-2-3 6c0 1 0 1-1 2l1 1h-1z" class="R"></path><path d="M235 306l3-5 1-1c1-1 1-2 2-3v-1h0l-2 1h0-1l4-4h0c1 0 1 0 1-1 1-1 1-1 2-1h1c-1 1-1 1-1 2s-1 1-1 2v1c1 0 1 1 2 0h1c0 1 0 2-1 2l1 1c1-1 1-1 2 0h0c-1 1-2 2-3 2-2 1-4 3-6 5-1-1 0-1-2-2-1 0-1 1-2 1v1h-1z" class="E"></path><path d="M244 295v1c1 0 1 1 2 0h1c0 1 0 2-1 2l1 1c1-1 1-1 2 0h0c-1 1-2 2-3 2v-1c-1 0-1 1-2 1h-2c0-1 1-2 2-3h0v-1h-1l1-2zm-20 22l1 1v2c-1 1-1 1-1 2v1c-1 1-1 1-1 2s0 1 1 2c-1 0-1 0-2 1 0 3-1 4-3 5-1 1-1 2-2 2l-1 1c-1 2-2 3-4 5-1-2 1-3 2-4s1-2 1-3c0-2 2-9 3-10 3-2 3-5 6-7z" class="V"></path><path d="M217 335h-1c-1-2 0-2 0-4h0l1-1c1-2 2-3 4-4h1c-1 1-1 2-1 3s0 1-1 2-1 1-1 2h0c-1 1-1 2-2 2z" class="M"></path><path d="M207 413c3 4 5 10 7 14 0 2 1 3 1 4 2 0 2 1 3 2l2 3c0 1 0 4-1 5v2 3h3c-1 1-1 1-2 1-2 0-3 1-4 1v-2c1-1 1-1 1-2h-1v-1l-1-1c1 0 1-1 1-1 0-2 0-4-1-6l-1-1h0c0-1-1-2-1-2l-2 1c0-2 0-4-1-6-1-5-4-9-3-14z" class="R"></path><path d="M216 443c0-1 1-2 2-3v3l-1 1v1c1 0 1 1 1 1 1-1 1-2 1-3v3h3c-1 1-1 1-2 1-2 0-3 1-4 1v-2c1-1 1-1 1-2h-1v-1zm2-10l-1 1 1 1v2 2c-1-1-1-3-2-4 0 0 0-1-1-1 0-2-2-3-3-5h1c0 1 1 1 2 2h0c2 0 2 1 3 2z" class="L"></path><path d="M202 361h1l-1-1c1-1 1-1 1-2l3-6 2 2c0 1 0 1 1 1l1 1h-1c1 2 1 1 2 3 0 0 1 2 1 3v1 2l-1 1-1-1h0c-1 1-1 1 0 2v1l-1 1h-1c0 1-1 2-1 2h0l-1-1h0c-2-2-3-2-3-5h0l-1-4z" class="E"></path><path d="M210 365v-2h-1v-7c1 2 1 1 2 3 0 0 1 2 1 3v1 2l-1 1-1-1z" class="L"></path><path d="M205 363c0-2-1-3 0-4h0l1 4h2l1 1 1 1c-1 1-1 1 0 2v1l-1 1h-1c0 1-1 2-1 2h0l-1-1h0c-2-2-3-2-3-5h0c1-1 1-2 2-2z" class="G"></path><path d="M205 363h0c1 1 0 2 0 3l-1 1c0-1 0-1-1-2h0c1-1 1-2 2-2z" class="U"></path><path d="M206 363h2l1 1 1 1c-1 1-1 1 0 2v1l-1 1h-1c0-1 0-2-1-4-1 0-1-1-1-2z" class="R"></path><path d="M283 284c0 3-3 5-5 7h-1-2c-1 1-1 1-2 1h-2-1c-1 1-2 1-3 1v-1h-2c-2 0-4 2-6 3l-3 3-3 4-2-2c0-1 0-2 1-4 0-1 2-2 3-3 5-3 9-4 15-4 4-1 6 0 9-2 1-1 3-2 4-3z" class="r"></path><path d="M208 369h1c0 1 1 2 1 3 0 0 0 1 1 2v1l1 1-2 2h0c0 1-1 2 0 3l2 3 1 4h1 0l-2 1c0 2 0 2-1 4h-2 0-1l-1-1v-1-2c-1-1-2-1-3-2l-1-1v-1c1-1 1-1 1-2-1-2 0-4-1-6 1 0 1-1 1-2l2-5 1 1h0s1-1 1-2z" class="o"></path><path d="M208 369h1c0 1 1 2 1 3 0 0 0 1 1 2v1l1 1-2 2h0c-1 0-2 0-3 1v-3l2-2-1-1c0 1 0 1-1 1 0 1 0 1-1 2v-1c1-1 0-2 1-4 0 0 1-1 1-2z" class="F"></path><path d="M207 379c1-1 2-1 3-1 0 1-1 2 0 3l2 3 1 4h1 0l-2 1c0 2 0 2-1 4h-2l1-1v-1l1-2c0-3-3-4-3-6l-1-4zm-4-2c1 0 1-1 1-2h0c1 1 0 1 1 1 1 2 1 4 1 6l3 7v2h0-2v-2c-1-1-2-1-3-2l-1-1v-1c1-1 1-1 1-2-1-2 0-4-1-6z" class="C"></path><path d="M214 388v-1h1l1 1 2 3c1 2 2 4 1 6 0 1-1 1-1 2v1s-1 1-1 2c0-1 0-1-1-2-2 2-3 4-2 6v5c-1 3-1 6 0 10 0 2 1 4 2 5 3 4 7 7 7 11 0 3-1 6-1 9h-3v-3-2c1-1 1-4 1-5l-2-3c-1-1-1-2-3-2 0-1-1-2-1-4-2-4-4-10-7-14l1-7c0-1-1-4-1-5s1-2 1-3h0c1-2 1-3 0-5h1 0 2c1-2 1-2 1-4l2-1h0z" class="M"></path><path d="M217 398s0 1 1 1v1s-1 1-1 2c0-1 0-1-1-2l1-2z" class="c"></path><path d="M214 388v-1h1l1 1 2 3c1 2 2 4 1 6 0 1-1 1-1 2-1 0-1-1-1-1-1-4-2-7-3-10h0z" class="W"></path><path d="M208 393h1c1 2 1 4 2 5v-2-1h0c1 1 2 2 1 4l-1 2h0v-3c-1 1-2 2-2 4h1 1c0 2 0 2-1 3v1-1c0-1 0-2-1-3-1 1-1 2 0 3v2 2l-1-1v-2c0-1-1-4-1-5s1-2 1-3h0c1-2 1-3 0-5z" class="T"></path><defs><linearGradient id="AN" x1="213.136" y1="443.7" x2="238.019" y2="429.393" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#AN)" d="M218 391v-1-2l3 3h0c0 1-1 1 0 1 0 2 1 3 1 4v1l3 2 2 1h1v2l1-1h1c-1 1-1 1 0 2h1v3c1 1 1 3 1 4v3c-1 1-2 3-3 4 0 2 1 5 2 6h0c1 1 1 1 1 2-1 1 0 1 0 2l1 2c1 2 3 5 5 7l5 3c1 2 2 4 4 4l2 1-1-1 1-2h1v4c2 3 4 6 5 10v2c0 1 1 1 1 2l2 1h1l1 2 1 1h1v-1h1c1 0 1 1 1 2-1 0-1 1-2 1h0c1 1 0 1 1 1s2-1 2 0l-1 2-2-2c-1-1-2-1-3-2h-1c0-1-1-1-2-2l-1-1-9-6c-4-2-9-3-13-5l-2 2h-1-1-2 0v-3h-3c-1 0-3 0-3 1-1 0-1 1-1 1-1 0-1 0-1 1-2 0-2 1-3 2-1-1-3-1-4-2l-2-2c2-1 4-1 6-2h0c1 0 2-1 4-1 1 0 1 0 2-1 0-3 1-6 1-9 0-4-4-7-7-11-1-1-2-3-2-5-1-4-1-7 0-10v-5c-1-2 0-4 2-6 1 1 1 1 1 2 0-1 1-2 1-2v-1c0-1 1-1 1-2 1-2 0-4-1-6z"></path><path d="M229 440c1 0 1 1 1 2l2 1h-2v1l-1-1c-1-1 0-2 0-3z" class="a"></path><path d="M227 449l6 1-2 2h-1-1-2 0v-3z" class="F"></path><path d="M226 427c1 0 1 0 2-1l1 1c1 0 1 1 1 2h0l-1 1h-1v1h0c-1-1-2-2-2-4z" class="i"></path><path d="M228 431h0c0 1 1 2 2 2h0 0c1 1 2 1 3 2l1 1h-2-1-1c-1 0-1 0-2-1v2c-1 0-1-1-2-1h0c0-2-1-3-1-4h0c1 0 2 1 2 1l1-2z" class="X"></path><path d="M226 436c1 0 1 1 2 1v-2c1 1 1 1 2 1h1 1l-1 1c0 1 0 2 1 3l1 1h1l1-1h0v2l-2-1v1 1h-1l-2-1c0-1 0-2-1-2-1-1-2-2-3-4z" class="o"></path><path d="M210 450c2-1 4-1 6-2 0 1 0 1 1 1h2 5c-1 0-3 0-3 1-1 0-1 1-1 1-1 0-1 0-1 1-2 0-2 1-3 2-1-1-3-1-4-2l-2-2z" class="p"></path><path d="M212 452c3-1 6-2 9-2-1 0-1 1-1 1-1 0-1 0-1 1-2 0-2 1-3 2-1-1-3-1-4-2z" class="N"></path><path d="M218 416l1-3v2h1 1l-1 3c1 1 1 3 1 5 0 0-1 0-1 1v3h0l-2-2c0-1-1-4-1-5l1-4z" class="h"></path><path d="M218 416l1-3v2h1 1l-1 3c1 1 1 3 1 5 0 0-1 0-1 1-1-3 0-6-2-8z" class="X"></path><path d="M232 427l1 2c1 2 3 5 5 7-1 0-1 0-2 1h0l-1-1h0-1l-1-1c-1-1-2-1-3-2h0 0c-1 0-2-1-2-2v-1h1l1-1h0l1-1 1-1z" class="c"></path><path d="M230 429c2 2 2 4 4 6 1 0 1 1 1 1h0-1l-1-1c-1-1-2-1-3-2h0 0c-1 0-2-1-2-2v-1h1l1-1z" class="S"></path><path d="M233 443v-1-1l2 1 1 2s1 1 2 0c1 2 4 4 4 6h0c-1 0-1 0-2-1h-3c-1 0 0 0-1-1h-2c-1-1-2-1-3-1h0c-1-1-1-2-1-3v-1h2 1z" class="a"></path><path d="M232 443h1l2 3h0-2c-1 0-1 0-2 1h0c-1-1-1-2-1-3v-1h2z" class="j"></path><path d="M230 443l2 1v1 1c-1 0-1 0-1 1-1-1-1-2-1-3v-1z" class="X"></path><path d="M234 436h1 0l1 1h0c1-1 1-1 2-1l5 3-1 1v1h-1c0 1 1 2 2 3h0 0c-1 1-2 1-2 2l-1-1s-1 0-2-1h0c-1 1-2 0-2 0l-1-2v-2h0l-1 1h-1l-1-1c-1-1-1-2-1-3l1-1h2z" class="O"></path><path d="M238 436l5 3-1 1h-2c-1 0-3-2-4-3 1-1 1-1 2-1z" class="Y"></path><path d="M232 436l3 3c1 1 3 2 4 4-1 0-2 0-3 1l-1-2v-2h0l-1 1h-1l-1-1c-1-1-1-2-1-3l1-1z" class="Q"></path><path d="M243 439c1 2 2 4 4 4l2 1-1-1 1-2h1v4c2 3 4 6 5 10v2c0 1 1 1 1 2l2 1h1l1 2h-1c-2-2-4-4-6-5-3-3-7-5-11-7h0c0-2-3-4-4-6h0c1 1 2 1 2 1l1 1c0-1 1-1 2-2h0 0c-1-1-2-2-2-3h1v-1l1-1z" class="H"></path><path d="M243 439c1 2 2 4 4 4l2 1c1 3 3 7 4 10h-1c-2-1-3-4-4-6s-3-3-5-4h0c-1-1-2-2-2-3h1v-1l1-1z" class="b"></path><path d="M218 391v-1-2l3 3h0c0 1-1 1 0 1 0 2 1 3 1 4v1l3 2 2 1h1v2l1-1h1c-1 1-1 1 0 2h1v3c1 1 1 3 1 4v3c-1 1-2 3-3 4 0 2 1 5 2 6h0c1 1 1 1 1 2-1 1 0 1 0 2l-1 1-1 1c0-1 0-2-1-2l-1-1c-1 1-1 1-2 1l-1-1h-2v-3-2c-1 1-1 1-1 2h-1c0-2 0-4-1-5l1-3h-1-1v-2l-1 3-1 4v-2c-1-1-1-1-1-2-1-1 0-3 0-4 0-2-1-6 0-8l1-2c0-1 1-2 1-2v-1c0-1 1-1 1-2 1-2 0-4-1-6z" class="f"></path><path d="M217 418c-1-3 0-8 1-10h1c1 2 0 4 0 5l-1 3-1 4v-2z" class="K"></path><path d="M221 403s0-1-1-1v-1-3l2-2v1h-1v4c1 0 1 0 2 1v3c0 1 1 3 1 4-1 0-1 1-2 1 0-1 0-3-1-4v-3z" class="P"></path><path d="M218 391v-1-2l3 3h0c0 1-1 1 0 1 0 2 1 3 1 4l-2 2v3 1c1 0 1 1 1 1-1 1-1 2-2 2v-1-2c-1-1-1-1-1-2v-1c0-1 1-1 1-2 1-2 0-4-1-6z" class="h"></path><path d="M223 405c2 0 2 1 3 2 0 1 0 1-1 1 1 1 1 2 2 2l-1 1h0c-1 1-2 3-1 4 0 1 0 1 1 1h-1c-1 1-2 1-2 1-1 1-1 2-1 2 0 1 1 1 1 2-1 1-1 1-1 2h-1c0-2 0-4-1-5 1-1 1-1 1-2 1 0 2-1 2-3 0-1 0 0 1-1v-3c0-1-1-3-1-4z" class="G"></path><path d="M223 405v-3c-1-1-1-1-2-1v-4h1l3 2 2 1h1v2c-1 1-1 2-1 3 1 2 2 3 2 5h-1-1c-1 0-1-1-2-2 1 0 1 0 1-1-1-1-1-2-3-2z" class="N"></path><path d="M228 402l1-1h1c-1 1-1 1 0 2h1v3c1 1 1 3 1 4v3c-1 1-2 3-3 4 0 2 1 5 2 6h0c1 1 1 1 1 2-1 1 0 1 0 2l-1 1-1 1c0-1 0-2-1-2l-1-1c-1 1-1 1-2 1l-1-1h-2v-3-2c0-1-1-1-1-2 0 0 0-1 1-2 0 0 1 0 2-1h1c-1 0-1 0-1-1-1-1 0-3 1-4h0l1-1h1 1c0-2-1-3-2-5 0-1 0-2 1-3z" class="K"></path><path d="M226 416c-1 0-1 0-1-1-1-1 0-3 1-4 0 1 0 1 1 2-1 1-1 2 0 3s1 2 1 3l-1 1v1h2v-3-1c0 2 1 5 2 6h0c1 1 1 1 1 2-1 1 0 1 0 2l-1 1-1 1c0-1 0-2-1-2l1-1c-1-1-1-1-2-1h0c-1-1 0-2 0-3v-1l-1 1v-4l-1-2z" class="b"></path><path d="M225 416h1l1 2v4l1-1v1c0 1-1 2 0 3h0c1 0 1 0 2 1l-1 1-1-1c-1 1-1 1-2 1l-1-1h-2v-3-2c0-1-1-1-1-2 0 0 0-1 1-2 0 0 1 0 2-1z" class="h"></path><path d="M225 416h1l1 2c0 2 0 3-1 4h0-1c-1 0-1 1-1 2-1 0-1 0-1-1v-2c0-1-1-1-1-2 0 0 0-1 1-2 0 0 1 0 2-1z" class="F"></path><path d="M225 416l1 1c0 1 0 1-1 1-1 1-1 1-3 1 0 0 0-1 1-2 0 0 1 0 2-1z" class="Z"></path><path d="M407 100c2-1 2-1 3-1 1 1 2 2 2 4h0v1 1s0 1 1 1c-1 1-1 1-1 2h-1v1c1 2 2 3 2 5 1 1 1 2 1 3h0c1 1 2 4 3 5h0c6 1 13 0 19 0h37 10c1 0 3 0 4 1h32c5 0 10-1 15 0 1 0 2 0 2 1 1 1 2 2 2 3l3 13 8 39 11 52 4 19c1 3 2 7 1 11 0 2 0 3-2 4 0 1-1 1-1 1-2 0-3-1-4-2-4-4-8-9-12-13l-13-12c-3-3-7-7-9-10-1-2-2-4-2-6 0-1 1-2 2-3 2 0 4-1 5-1-5-5-11-8-17-11-18-9-39-16-59-17-2-1-4-1-7-1h-3 0 1c5-1 9 0 13-1h1-6c-3 0-7-1-10 0 0-1 0-1 1-2s2-1 3-1l6-1c-2-1-4-1-5-1l-2-1h0c-1 0-2 1-3 1l-2-2-1 1c-1 2-1 4-1 5 1 2 0 8 0 10v5h0 0c-1 0-1-1-2-1h0c-1 1-2 2-3 2h-1v1h-14l-3-1h-18-5l-15 2v-2c0-1 1-1 2-1v-7-3c-1-1-1-1-2 0v-1h2l1-1-2-1 1-1c1-1 1-1 2-1l-2-1c2-1 5-2 7-2-1-1-1-1-2-1l-1 1c-1 0-1 0-2-1 0-1 0-2 1-3v-2-1c1-1 1-3 1-4v-1c0-2 1-2 3-3h-2l-1-2-3-4v-1-1l1-1c1-1 4-3 6-3l-1-1h-2l2-1c0-2 0-2 1-3-1-1 0-1-1-1h-1 0c3-2 3-7 4-10l1-8c1-3 2-8 3-10s2-5 3-7h0v-1-1l1-2 1-1v-1c1-1 0 1 1-1h0l-1-1v-1h0c0-1 0-1 1-2v-3h6 0c1 0 3 0 4-1l-2-2z" class="N"></path><path d="M445 183c6-1 12-1 19-1 1 1 3 0 4 1-3 0-7 1-10 0-2 1-4 2-6 2-2-1-4-1-5-1l-2-1h0z" class="e"></path><path d="M438 188c1 2 0 8 0 10v5h0 0c-1 0-1-1-2-1h0c-1 1-2 2-3 2v-1h1l-1-1s-1 0-1 1c-1 0-1 0-1 1h-1c0-2 0-4-1-6h0c1-1 1-2 1-3v1c1 2 0 4 1 6h4c0-1 1-2 1-2v-2h1v-1c-1 0-1 0-2-1 1-1 1 0 3-1h0c-1 0-1-1-1-1-1 0-1-1-2-1h0v-1h1l2-1v-1h-1v-1h1v-1z" class="F"></path><path d="M424 166s1 1 2 1v-1c2 4 2 10 3 15 0 1 1 1 1 2 1 1 4 3 3 4 0 1-1 2-1 2l-1-1v-1c-2-2-5-3-8-3l3-1h0 1 0v-2-2-2c-1-1-1-2-1-3v-2-2h-1c-1-1-1-1-1-2v-2z" class="Y"></path><path d="M452 185c2 0 4-1 6-2-1 1-1 1-1 3h2 4c2 1 3 1 5 1 5 1 9 2 14 4 2 0 4 1 5 2l1 1c11 4 22 8 32 15h0c-19-10-41-19-62-20h-6c-3 0-7-1-10 0 0-1 0-1 1-2s2-1 3-1l6-1z" class="n"></path><path d="M463 186c2 1 3 1 5 1l-2 2-7-1c1-1 3-1 4-2z" class="E"></path><path d="M452 185c2 0 4-1 6-2-1 1-1 1-1 3h2 4c-1 1-3 1-4 2-1 0-3 0-4-1h-4c-2 0-4 0-5-1l6-1z" class="J"></path><path d="M459 186h4c-1 1-3 1-4 2-1 0-3 0-4-1h-4l2-1h6z" class="F"></path><defs><linearGradient id="AO" x1="475.875" y1="186.685" x2="477.14" y2="194.209" xlink:href="#B"><stop offset="0" stop-color="#b6b7b5"></stop><stop offset="1" stop-color="#d5d6d7"></stop></linearGradient></defs><path fill="url(#AO)" d="M468 187c5 1 9 2 14 4 2 0 4 1 5 2l1 1-22-5 2-2z"></path><path d="M562 247v3c0 3 2 10 0 13-1 0-2-1-3-1-3-3-6-7-9-10l-18-17c-2-3-8-8-8-12h1c2 0 3 0 4-1h2c-1 1-2 1-3 2h-1c1 0 1 1 2 2s2 3 3 4c2 2 4 4 4 7 5 5 10 10 16 15h0c1-1 1-1 2-1l1-1h1c0 1 0 1 1 2v-1c2-2 2-4 3-7v-1h1c0 1 1 2 1 4z" class="q"></path><path d="M536 237l-5-5c-1-2-5-5-5-8h1c1 0 1 1 2 2s2 3 3 4c2 2 4 4 4 7z" class="E"></path><path d="M560 244v-1h1c0 1 1 2 1 4h0l-1 13-4-2c2-2 3-5 3-7v-7z" class="Q"></path><path d="M555 250h1c0 1 0 1 1 2v-1c2-2 2-4 3-7v7c0 2-1 5-3 7l-3-3-2-3h0c1-1 1-1 2-1l1-1z" class="H"></path><path d="M552 252h0c1-1 1-1 2-1l1-1v4l-1 1-2-3z" class="M"></path><path d="M414 117c1 1 2 4 3 5h0c6 1 13 0 19 0h37 10c1 0 3 0 4 1h-69c2 6 4 12 5 19l1 7s0 2 1 2c0 1 1 1 2 2 2 2 3 6 4 9-1 1-3 3-5 4v1c-1 0-2-1-2-1-1 0-1-1 0-1v-1-3c2 0 3 1 4 1h1l-1-1v-2-1c-1-1-1-1-1-2h0c-1-1-1-2-2-3l-2 1c-1-1-1-1-1-2h-2c1 1 1 1 1 2-1 1-1 1-2 0l-2-10c0-2-1-4-1-7v-3c-1-1 0-3-1-4v-4l-1-9z" class="K"></path><path d="M417 130v-2h1l3 13c1 5 1 9 4 12l-2 1c-1-1-1-1-1-2v-3l-5-19z" class="R"></path><path d="M414 117c1 1 2 4 3 5 0 2 1 4 1 6h-1v2l5 19v3h-2c1 1 1 1 1 2-1 1-1 1-2 0l-2-10c0-2-1-4-1-7v-3c-1-1 0-3-1-4v-4l-1-9z" class="S"></path><path d="M420 149h2v3h-2v-3z" class="K"></path><path d="M420 149l-3-16v-3c0-1-1-3-1-4h1v4h0l5 19h-2z" class="X"></path><path d="M420 152h2c0 1 0 1 1 2l2-1c1 1 1 2 2 3h0c0 1 0 1 1 2v1 2l1 1h-1c-1 0-2-1-4-1v3 1c-1 0-1 1 0 1v2c0 1 0 1 1 2h1v2 2c0 1 0 2 1 3v2 2 2h0-1 0l-3 1-14-2c-4 0-9-1-13 0h-1v-3h0c0-1 1-1 0-2l-4 1c-3 0-5 1-8 2v-1h-1v-1c1-1 1-3 1-4v-1c0-2 1-2 3-3h-2l-1-2-3-4v-1-1l1-1c1-1 4-3 6-3l-1-1h1c5-1 11-2 16-2h13 0 1 1 0l1-1c1 1 1 1 2 0 0-1 0-1-1-2z" class="p"></path><path d="M387 158c3-1 5-1 8-1h4l1 1c-8 0-13 1-20 4l1-1c1-1 4-3 6-3z" class="Q"></path><path d="M420 152h2c0 1 0 1 1 2l2-1c1 1 1 2 2 3h0c0 1 0 1 1 2v1 2l1 1h-1c-1 0-2-1-4-1-8-3-16-3-24-3l-1-1h-4c-3 0-5 0-8 1l-1-1h1c5-1 11-2 16-2h13 0 1 1 0l1-1c1 1 1 1 2 0 0-1 0-1-1-2z" class="d"></path><path d="M419 157h4c1 0 1 1 2 1l2 1h0 1v2c-3-2-6-2-10-3l1-1z" class="Y"></path><path d="M403 155h13c1 0 2 1 3 2l-1 1c-6-1-12-2-19-2-1 0-3 0-4 1-3 0-5 0-8 1l-1-1h1c5-1 11-2 16-2z" class="X"></path><path d="M420 152h2c0 1 0 1 1 2l2-1c1 1 1 2 2 3h0c0 1 0 1 1 2v1h-1 0l-2-1c-1 0-1-1-2-1h-4c-1-1-2-2-3-2h0 1 1 0l1-1c1 1 1 1 2 0 0-1 0-1-1-2z" class="a"></path><path d="M396 161c1 0 1 0 2 1h2c1-1 3-1 5-1v1c2 0 4 0 6 1h0c1-1 2 0 3 0h2l1 1h0c-1 1-3 1-4 1h-6 2 0c1 1 2 1 2 1h0 4c-2 1-3 1-4 1l2 2-4-1h-1l-1 1-3-1c-2 0-4 0-6 1-1 0-3 0-4 1-2 1-4 1-6 1h-1l1-1h-1-1-2l-1-2-3-4h3c5-2 8-3 13-3z" class="K"></path><path d="M398 162h2c0 1 1 1 1 2h0c-1 1-4 1-5 0v-2h2z" class="N"></path><path d="M405 162c2 0 4 0 6 1h0c1-1 2 0 3 0l-1 1h-2-1-1-3l-1-2z" class="F"></path><path d="M383 164c5-2 8-3 13-3-1 1-1 1-2 1-2 0-3 0-5 2l-1 1v1h1c1 0 2-2 3-3v1 2c-1 1-4 1-5 1h-1v-1h0c1-1 0-1 1-1h-1-1-2v-1z" class="P"></path><path d="M390 169c2-1 4-2 6-2h1c1 0 1-1 2-1h0 12 4c-2 1-3 1-4 1l2 2-4-1h-1l-1 1-3-1c-2 0-4 0-6 1-1 0-3 0-4 1-2 1-4 1-6 1h-1l1-1h-1c0-1 3-1 3-1z" class="h"></path><path d="M408 168c-2 0-7 1-8 0 2-2 6 0 8-1h3l2 2-4-1h-1z" class="j"></path><path d="M390 169h4 2 2c-1 0-3 0-4 1-2 1-4 1-6 1h-1l1-1h-1c0-1 3-1 3-1z" class="S"></path><path d="M415 166c1 0 1 0 2 1l1 1c1 1 3 2 5 2h1v-2c0 1 0 1 1 2h1v2 2c0 1 0 2 1 3v2 2 2h0-1 0l-3 1-14-2c-4 0-9-1-13 0h-1v-3h0c0-1 1-1 0-2l-4 1c-3 0-5 1-8 2v-1h-1v-1c1-1 1-3 1-4v-1c0-2 1-2 3-3h1 1l-1 1h1c2 0 4 0 6-1 1-1 3-1 4-1 2-1 4-1 6-1l3 1 1-1h1l4 1-2-2c1 0 2 0 4-1z" class="m"></path><path d="M408 168h1l2 1h0l-2 1v1h0c1 0 1 1 2 2 0 1 0 1-1 2h-2-2c-1 0-1 0-1-1h3v-1c0-1 0-1-1-2 0 0 1 0 1-1-1 0 0-1-1-1l1-1z" class="F"></path><path d="M415 166c1 0 1 0 2 1l1 1c-1 1 0 2-1 3v2 3h-6l4 1c1 0 1 0 2 1v3h-1l-3-3c0-1-3-1-3-2h0c1 0 3 0 4-1v-3c-1-1-2-1-4-2h6v-1h-2-1l-2-2c1 0 2 0 4-1z" class="n"></path><path d="M415 166c1 0 1 0 2 1l1 1c-1 1 0 2-1 3v2l-2-2h-1c1-1 1 0 2-1h0v-1h0-2-1l-2-2c1 0 2 0 4-1z" class="W"></path><path d="M396 177h6 6l1-1 1 1v1c-1 1-1 2-1 2v1h2s-1 0-2 1c-4 0-9-1-13 0h-1v-3h0l1 1v-3z" class="E"></path><path d="M396 177c2 1 2 1 3 1l1 2-1 1h-2l-1-1v-3z" class="Q"></path><path d="M403 180l2-2h3v2l1 1h2s-1 0-2 1c-4 0-9-1-13 0h-1v-3h0l1 1 1 1h2c1-1 3-1 4-1z" class="P"></path><path d="M403 180l2-2h3l-1 1v1c-1 1-3 1-4 0z" class="F"></path><path d="M424 168c0 1 0 1 1 2h1v2 2c0 1 0 2 1 3v2 2 2h0-1 0l-3 1-14-2c1-1 2-1 2-1h5 1v-3c-1-1-1-1-2-1l-4-1h6v-3-2c1-1 0-2 1-3 1 1 3 2 5 2h1v-2z" class="K"></path><path d="M418 168c1 1 3 2 5 2v1c-2 1-4 1-6 0 1-1 0-2 1-3zm2 9c0-1-1-1-2-1v-3h1c2 0 3 0 5 1 0 1-1 1-1 2l1 1h-1-3z" class="h"></path><path d="M419 173c2 0 3 0 5 1 0 1-1 1-1 2h-1c-2-1-3-2-3-3z" class="g"></path><path d="M425 170h1v2 2c0 1 0 2 1 3v2 2 2h0-1c-2-1-7-1-8-2 1-1 0-2 0-3l2-1h0 3 1l-1-1c0-1 1-1 1-2h1v-3-1z" class="j"></path><path d="M425 171v3h0c0 1 0 2 1 2v1h-2l-1-1c0-1 1-1 1-2h1v-3z" class="Y"></path><path d="M420 177h3 1 2v4h-2c0-1 0-2-1-2-1-1-2-1-3-2h0z" class="b"></path><path d="M398 169c2-1 4-1 6-1l3 1c1 0 0 1 1 1 0 1-1 1-1 1 1 1 1 1 1 2v1h-3c0 1 0 1 1 1h-3c-1 1-1 1-2 0l-5 1h-1l-4 1v1c-3 0-5 1-8 2v-1h-1v-1c1-1 1-3 1-4v-1c0-2 1-2 3-3h1 1l-1 1h1c2 0 4 0 6-1 1-1 3-1 4-1z" class="G"></path><path d="M389 173h6v2-1c-2 1-3 1-4 1 0 0 0-1-1-1 0 0-1 0-1-1z" class="B"></path><path d="M394 170l2 1c-1 0-1 1-2 1-2 0-5 0-7 1h-1l2-2c2 0 4 0 6-1z" class="D"></path><path d="M396 176v-1l1-1v-2c1 1 2 1 3 2h1 1c1 1 1 0 1 1h-1-1l-5 1z" class="V"></path><path d="M398 169c2-1 4-1 6-1v2h-2l-5 1-1-1v1l-2-1c1-1 3-1 4-1z" class="F"></path><path d="M386 170h1 1l-1 1h1l-2 2h1l1 1 1-1c0 1 1 1 1 1 1 0 1 1 1 1 1 0 2 0 4-1v1 1l-4 1v1c-3 0-5 1-8 2v-1h-1v-1c1-1 1-3 1-4v-1c0-2 1-2 3-3z" class="C"></path><path d="M388 174l1-1c0 1 1 1 1 1 1 0 1 1 1 1-2 0-5 1-7 1h0c1-1 1-1 1-2h3z" class="E"></path><path d="M386 170h1 1l-1 1h1l-2 2h1l1 1h-3c0 1 0 1-1 2h0l-1 2h1l7-1v1c-3 0-5 1-8 2v-1h-1v-1c1-1 1-3 1-4v-1c0-2 1-2 3-3z" class="e"></path><path d="M391 178l4-1c1 1 0 1 0 2h0v3h1c4-1 9 0 13 0l14 2c3 0 6 1 8 3v1l1 1c-1 1-2 1-2 2v4c0 1 0 2-1 3h0c1 2 1 4 1 6h1c0-1 0-1 1-1 0-1 1-1 1-1l1 1h-1v1h-1v1h-14l-3-1h-18-5l-15 2v-2c0-1 1-1 2-1v-7-3c-1-1-1-1-2 0v-1h2l1-1-2-1 1-1c1-1 1-1 2-1l-2-1c2-1 5-2 7-2-1-1-1-1-2-1l-1 1c-1 0-1 0-2-1 0-1 0-2 1-3v-2h1v1c3-1 5-2 8-2z" class="K"></path><path d="M389 186c7-1 14-2 21 0 2 0 4-1 5 0v1h-6c-3 0-6-2-9-1-2 1-3 1-5 1-2-1-6 0-9 0v-1h3z" class="n"></path><path d="M381 188c1 0 2-1 4-2h4-3v1c3 0 7-1 9 0h0-4v1l1 1h-1v1c-1 1-2 1-3 0h-2 0l-1 1v-1c-2-1-4-1-6-1 1-1 1-1 2-1z" class="c"></path><path d="M409 182l14 2c3 0 6 1 8 3v1c-6-4-14-4-21-4-2-1-4-1-5-1h-9v-1c4-1 9 0 13 0z" class="R"></path><path d="M419 194c0-1-1-1-1-2 1-1 0-2 0-3l1-1c1 1 2 1 3 1h1v1c0 1 0 1 1 2v-1c1 1 1 1 1 2h1v-2-1h0 1l1 1v1h0l2-1v4c0 1 0 2-1 3 0-1 0-2-1-3h0v2l-1-1c-1 0-2 1-3 1h0-1c-1 0-2 0-3-1h-2c0-1 1-1 1-2z" class="W"></path><path d="M419 194h1v2h-2c0-1 1-1 1-2z" class="T"></path><path d="M391 178l4-1c1 1 0 1 0 2h0v3h1v1h9c1 0 3 0 5 1l-24 1c-1-1-1-1-2-1l-1 1c-1 0-1 0-2-1 0-1 0-2 1-3v-2h1v1c3-1 5-2 8-2z" class="S"></path><path d="M386 180h6 1c0 1 1 1 1 1-2 0-4 1-6 1-2 1-4 1-5 1 1-1 2-1 3-2v-1z" class="D"></path><path d="M391 178l4-1c1 1 0 1 0 2h0v2h-1s-1 0-1-1h-1-6c-1 1-2 1-3 1v-1c3-1 5-2 8-2z" class="N"></path><path d="M395 187c2 0 3 0 5-1 3-1 6 1 9 1h6-1v2h1c0 1 1 2 2 2v1h-2v3c-1-1-1-1-1-2v-1c-3-1-8-1-10-1l-1 1c-1 0-4 0-5-1v-3c-2 1-4 1-6 1l-1-1v-1h4 0z" class="W"></path><path d="M398 188h7c1 1 1 1 1 2-1 1-2 1-2 1l-1 1c-1 0-4 0-5-1v-3z" class="B"></path><path d="M406 190v1h2c0-2 0-2 1-3 0 1 4 1 5 1h1c0 1 1 2 2 2v1h-2v3c-1-1-1-1-1-2v-1c-3-1-8-1-10-1 0 0 1 0 2-1z" class="a"></path><path d="M428 197v-2h0c1 1 1 2 1 3h0c1 2 1 4 1 6h1c0-1 0-1 1-1 0-1 1-1 1-1l1 1h-1v1h-1v1h-14l-3-1h-1v-1h0 1c1 0 2-1 3-1h0c0-2 1-3 2-4-1-1-2 0-2-1v-1h2c1 1 2 1 3 1h1 0c1 0 2-1 3-1l1 1z" class="b"></path><path d="M420 198l1 1v2s-1 0-1 1h-2c0-2 1-3 2-4z" class="l"></path><path d="M425 200l1-1h2 0v4h-1c-1-1-1-1-2-3z" class="e"></path><path d="M424 197h0c1 0 2-1 3-1l1 1v2h-2l-1 1c0 1 0 1-1 2v-2-3z" class="j"></path><path d="M428 197v-2h0c1 1 1 2 1 3h0c1 2 1 4 1 6v1l-1-1c-2-1 0-3-1-5h0v-2z" class="K"></path><path d="M418 196h2c1 1 2 1 3 1h1v3 2c-2 0-1-3-3-4v1l-1-1c-1-1-2 0-2-1v-1z" class="h"></path><path d="M392 189c2 0 4 0 6-1v3c1 1 4 1 5 1l1-1c2 0 7 0 10 1v1c0 1 0 1 1 2 0 1 1 1 0 2v1 1c0 1 0 2-1 4h0v1h1-18-5l-15 2v-2c0-1 1-1 2-1v-7-3c-1-1-1-1-2 0v-1h2l1-1-2-1 1-1c2 0 4 0 6 1v1l1-1h0 2c1 1 2 1 3 0v-1h1z" class="X"></path><path d="M380 191l1 1v11h-1-1v-7-3c-1-1-1-1-2 0v-1h2l1-1z" class="B"></path><path d="M379 189c2 0 4 0 6 1v1c0 1-2 1-2 2-1 2-1 4-1 5v4c-1 0 0-8-1-10l-1-1-2-1 1-1z" class="q"></path><path d="M382 198c0-1 0-3 1-5v4c1 1 4 0 6 0 1-1 3-1 5 0h-3 0c1 1 3 1 4 1h2 1v4h-7c0-1 0-2-1-3h-1v3h0c-2 1-5 1-6 1l-1-5z" class="N"></path><path d="M413 195h-1v-1h0l2-1c0 1 0 1 1 2 0 1 1 1 0 2v1 1c0 1 0 2-1 4h0v1h1-18-5 0v-1l-2 1v-1h2-1v-1h7v-4h-1-2c-1 0-3 0-4-1h0 3l19-1v-1z" class="T"></path><path d="M398 202c1 0 2 1 3 1s3-1 4 0h-5l-2 1h-1-5 0v-1l-2 1v-1h2-1v-1h7z" class="a"></path><path d="M404 197l2 1v1 2c-2 1-4 1-6 1v-1c0-1 0-1-1-2h0v-1c2 0 3 0 5-1z" class="H"></path><path d="M399 199v-1c2 0 3 0 5-1-1 2 0 2-2 3-1 0-1-1-3-1h0z" class="C"></path><path d="M413 195l1 1c-1 0-1 0-2 1 1 1 3 0 2 3h0l-2 1s-1-1-2 0l1 1c-1 0-2-1-3-1h0c-1-1-1-2-1-2h-1v-1l-2-1c-2 1-3 1-5 1v1l-1-1h-1-2c-1 0-3 0-4-1h0 3l19-1v-1z" class="E"></path><path d="M392 189c2 0 4 0 6-1v3c1 1 4 1 5 1l1-1c2 0 7 0 10 1v1l-2 1h0v1h1v1l-19 1c-2-1-4-1-5 0-2 0-5 1-6 0v-4c0-1 2-1 2-2l1-1h0 2c1 1 2 1 3 0v-1h1z" class="M"></path><path d="M407 100c2-1 2-1 3-1 1 1 2 2 2 4h0v1 1s0 1 1 1c-1 1-1 1-1 2h-1v1c1 2 2 3 2 5 1 1 1 2 1 3h0l1 9v4c1 1 0 3 1 4v3c0 3 1 5 1 7l2 10-1 1h0-1-1 0-13c-5 0-11 1-16 2h-1-2l2-1c0-2 0-2 1-3-1-1 0-1-1-1h-1 0c3-2 3-7 4-10l1-8c1-3 2-8 3-10s2-5 3-7h0v-1-1l1-2 1-1v-1c1-1 0 1 1-1h0l-1-1v-1h0c0-1 0-1 1-2v-3h6 0c1 0 3 0 4-1l-2-2z" class="l"></path><path d="M403 130h2l1 1-1 2h-3 1v-3h0z" class="B"></path><path d="M407 146h1c1 1 3 1 4 2 1 0 1 0 0 1h-5 0v-3z" class="F"></path><path d="M402 139h0l-1-2h-1l1-1c1 0 2 0 2 1h1 2c0 2 0 2-1 2-1 1-1 1-2 0h-1z" class="H"></path><path d="M407 146v-4c2 1 3 1 5 1h0 1 0c-1 1-1 1-1 2l1 1h-1-2-2-1z" class="d"></path><path d="M401 141l2-1 1 1v1c0 1 0 1 1 2h0 0-1c0 1-1 1-2 2h-1l-1-1v-1-1-1h1v-1z" class="L"></path><path d="M413 143h1c1 1 0 1 1 2 1 0 2 0 2-1l2 10-1 1c-1-1 0-1-1-1-1-1-1-1-1-2h0c-1-1-2-1-2-2l2-1h1-1c-2-1-2-1-4-1-1-1-3-1-4-2h2 2 1l-1-1c0-1 0-1 1-2h0z" class="b"></path><path d="M400 148h0l1-1h3v2h-1l-2 2c-1 1-1 1-2 1l1 1h-4c-2 0-3 0-4 1l1-2c1 0 1-1 2-2-1 0-1 0-1-1l1-2h1c2 0 3 0 4 1z" class="N"></path><path d="M400 148h0l1-1h3v2h-1l-2 2c-1 1-1 1-2 1h0c0-1-1-1-1-1-1 0-1 0-1-1 1 0 2-1 3-2h0z" class="Z"></path><path d="M404 147h1c1 1 1 1 1 2v3h1c0-1 0-2 1-2h2c0 1 0 1 1 2l-1 1s-1 0-1 1h0l1 1h-4-3c-5 0-11 1-16 2h-1-2l2-1 1-1c1 0 3-1 4-1h1c1-1 2-1 4-1h4l-1-1c1 0 1 0 2-1l2-2h1v-2z" class="F"></path><path d="M399 152c1 0 1 0 2-1l2-2 1 1v2l1 1h-1-4l-1-1z" class="Q"></path><path d="M387 155v1h2c0-1 1-1 1-1h2 0l3-1c2 0 8-1 11 1h-3c-5 0-11 1-16 2h-1-2l2-1 1-1z" class="Z"></path><path d="M411 117c1-2 1-2 2-3 1 1 1 2 1 3h0l1 9v4c1 1 0 3 1 4v3c0 3 1 5 1 7 0 1-1 1-2 1-1-1 0-1-1-2h-1-1l-1-1h1 1c-2 0-5 0-6-1v-1-1-2h2l1-1h-3v-1l1-1-1-1h-1 0v-1-1l-1-1h3 1c1-1 2-1 3-1h0l1-1c-1-1-2-1-3-2l2-1v-2c0-2-1-4-1-6z" class="K"></path><path d="M407 135l2-1h0c0 1 1 1 2 1h0 2l1 1h-4-3v-1zm2 2c1 0 3 0 3 1h2v1h-2-5v-2h2z" class="V"></path><path d="M407 139h5l-2 1c1 1 3 1 4 1v1h-1c-2 0-5 0-6-1v-1-1z" class="J"></path><path d="M409 130c2 0 3 0 4 2v1l-2 2h0c-1 0-2 0-2-1h0l-2 1 1-1-1-1h-1 0v-1-1l-1-1h3 1z" class="E"></path><path d="M391 144h1v-1c-1-1 0-2 0-3v-1c1-1 1-2 1-3s0-1 1-2v-2s1-1 1-2h2c0 1 0 1 1 2h0c0-1 1-1 1-2h4 0v3h-1l-1 1c2 0 4-1 5 1-1 1-2 1-3 2 0-1-1-1-2-1l-1 1h1l1 2h0c-1 1-1 1-1 2v1h-1v1 1 1c-1 1-3 1-5 2l-1-1h0v1c0 1-1 3-1 5l-1 2h-1c-1 0-3 1-4 1l-1 1c0-2 0-2 1-3h0c0-1 1-2 1-3h0c1-1 1-2 2-3 0 0 1-2 1-3z" class="C"></path><path d="M387 153h0c0-1 1-2 1-3h0c1-1 1-2 2-3 0 1 0 2-1 3l1 1v-1c1-1 1-2 1-2h1v1c-1 0-1 1-1 2-1 1-1 2-1 3h1c-1 0-3 1-4 1l-1 1c0-2 0-2 1-3z" class="H"></path><path d="M407 100c2-1 2-1 3-1 1 1 2 2 2 4h0v1 1s0 1 1 1c-1 1-1 1-1 2h-1v1c1 2 2 3 2 5-1 1-1 1-2 3 0 2 1 4 1 6v2l-2 1c1 1 2 1 3 2l-1 1h0c-1 0-2 0-3 1h-1-3-2-4c0 1-1 1-1 2h0c-1-1-1-1-1-2h-2c0 1-1 2-1 2v2c-1 1-1 1-1 2s0 2-1 3v1c0 1-1 2 0 3v1h-1c0 1-1 3-1 3-1 1-1 2-2 3h0c0 1-1 2-1 3h0c-1-1 0-1-1-1h-1 0c3-2 3-7 4-10l1-8c1-3 2-8 3-10s2-5 3-7h0v-1-1l1-2 1-1v-1c1-1 0 1 1-1h0l-1-1v-1h0c0-1 0-1 1-2v-3h6 0c1 0 3 0 4-1l-2-2z" class="e"></path><path d="M395 127h3v2h-3v-2z" class="L"></path><path d="M398 122v1c1 0 1 1 0 2 0 1 0 1-1 1h-1v-3h2v-1zm3-6h3c1 1 0 1 0 2h-4c0-1 1-1 1-1v-1z" class="D"></path><path d="M400 118h4v2c-1 1-3 1-4 1v-2-1z" class="L"></path><path d="M400 122h3c1 1 1 2 1 2v1c-2 0-3 1-4 0v-3zm-1 4h6c0 1 0 2-1 3h-5v-3z" class="N"></path><path d="M406 116h2c1 1 1 3 0 5h0-2v-2-3z" class="V"></path><path d="M411 109c1 2 2 3 2 5-1 1-1 1-2 3v-1h-2c0-1 0-1 1-1v-2h-1l-1-1h1 0c1-1 2-2 2-3z" class="b"></path><path d="M407 122c2 0 2 0 4 1h1v2l-2 1c1 1 2 1 3 2l-1 1h0c-1 0-2 0-3 1h-1c0-1 0-1-1-1h-1 0v-4-3h1z" class="J"></path><path d="M407 122c1 1 1 1 1 3h-2v-3h1zm-11-5h1v1l-1 1h0c-1 3-1 6-2 8l-3 11c0 2 0 3-1 4v2h1c0 1-1 3-1 3-1 1-1 2-2 3h0c0 1-1 2-1 3h0c-1-1 0-1-1-1h-1 0c3-2 3-7 4-10l1-8c1-3 2-8 3-10s2-5 3-7z" class="B"></path><path d="M407 100c2-1 2-1 3-1 1 1 2 2 2 4h0v1 1s0 1 1 1c-1 1-1 1-1 2h-1v1c0 1-1 2-2 3h0-1-2v4h0c-1 0-1 0-2-1-1 0-2 0-3 1v1s-1 0-1 1v1h0v-3c1-2 0-4 0-6l-4 7v-1-1l1-2 1-1v-1c1-1 0 1 1-1h0l-1-1v-1h0c0-1 0-1 1-2v-3h6 0c1 0 3 0 4-1l-2-2z" class="Y"></path><path d="M406 112c0-1 0-2 1-3h1l1 3h-1-2z" class="F"></path><path d="M401 117v-4h2 0v-2c0-1 0-1 1-1h0c1 1 0 2 1 3v1l-1 1c-1 0-2 0-3 1v1z" class="Q"></path><path d="M408 109v-1-2h3v1h0v1 1c0 1-1 2-2 3h0l-1-3z" class="p"></path><path d="M387 122l4 1c0 2-2 6-2 9h1v2l-1 8c-1 3-1 8-4 10h0 1c1 0 0 0 1 1-1 1-1 1-1 3l-2 1h2l1 1c-2 0-5 2-6 3l-1 1v1 1l3 4 1 2h2c-2 1-3 1-3 3v1c0 1 0 3-1 4v1 2c-1 1-1 2-1 3 1 1 1 1 2 1l1-1c1 0 1 0 2 1-2 0-5 1-7 2l2 1c-1 0-1 0-2 1l-1 1 2 1-1 1h-2v1c1-1 1-1 2 0v3 7c-1 0-2 0-2 1v2l-15 2c-5 1-9 2-12 5h-1 0v-1c-1 1-1 2 0 3 3 6 16 7 21 9-2 4-3 8-3 13v1l1-1h0v1 3h4v4l-4 2h-1 0l1 1v2c-2 0-3 1-4 2h0 1v1 1h0c1 1 1 1 1 2s0 1-1 2v1 1l2 1 3-1-1 1c2 1 3 1 5 1l1 1h-1l-1 1v2h0l3 1h1l-1 3h1l1-1c1 1 0 2 0 3h0l1 1c-1 0-1 0-1 1l-16 4c-3 1-9 3-10 6v2c0 1 0 1 1 2 1 0 2 1 4 1h0c1-1 1-1 2-1 0 1 0 1-1 2h1c1 0 1-1 2-2h1 0l1 1c-1 0-2 0-2 2h1l1-1s0-1 1-1l3-3v1 1h-1 0l-1 1 1 1h0c1 0 1-1 2 0 0 1 0 1-1 2v1 5c1 2 0 3 1 4h1 0c-1 0-1 0-2 1v1 3l1 1c-1 2-1 2-1 4 1 2 0 4 0 6v1c0 2 1 5 0 7-1-1-1-2-1-3v-6h-4v-3c-1 2-1 4-1 6v11 5 23c2-1 4-1 5-1h2c2-1 3-1 5-1s4-1 6-1h0l14-1h0c1 1 2 1 3 1v1h-2v1h0v3h-1l-1-1v1s1 1 1 2c1 1 1 5 0 7h0c0 1-1 2-1 3-1 1-1 0-2 0s-1 0-2-1v1l-1 1h-1 0-3v1h5c-1 0-2 0-3 1 2 1 7 0 10 0h-6c-1 1-1 1-2 1l-1 1c0 1 0 1-1 2 1 1 1 2 2 4h1v1c0 2 0 3 2 4l-4 2 1 2c2 0 3 0 4 1v2c-1 1 0 2 0 3v1c-1-1-1-1-2-1v2l1 1h-8 0l-1 1c-2 0-5 0-8 1h-3 0-2-1c-1 0-1 0-2 1-2 1-4 1-5 3 0 2 1 2 2 2 2 1 4 0 6 0v8l2-1v-2l1-1c1 1 1 3 0 4h1l1 4v9c0 1-1 2 0 3v2 1c-1 2 0 4 0 6v5h8 0 11v1h-5-3c-9 0-18 1-26 3h0l-1-1c-1 1-1 1-2 3 1 2 2 2 4 3 3 1 6 1 9 3v27c1 0 1 0 2 1h0 1v1h-1 0l1 1h-1c0 1 1 1 2 1v1 3 1 4 2c0 2-1 5 0 7v10 11 4h0c1-1 1-1 1-2v1l2 1h0-2c-1 2 0 5 0 7v2 2 1h0v2h-2l-1 1c-1 1-1 1-1 2l1 1c0 1 0 1-1 2 0 0-1 0-2 1-1 0-1 0-1 1h-1v1h-2v1l-1-1c-1-1-1-1-2-1v1h0c1 1 1 1 1 2v1h0v-1l-2-2c-1-1-2 0-3 0-2 1-3 2-4 3s-1 1-2 1h-1 0c-1 0-2 1-4 1h0-1v-1-1h-2 0c-2 1-2 1-4 1-1 0-2 1-3 1l-1 1c-1 1-1 0-2 0-1 1-2 2-4 2h0c-1 1-1 0-1 1v2c-2 0-3 1-4 1h0c1-8 0-17 1-25h0c1 0 0 0 1 1v1h2c2 0 2 1 4 0 1-2 3-3 3-5 0-1 0-1-1-2h-1-1v-1-6h0c-1-1-3-2-4-2v-2c1-3 0-7 0-10v-19-66-236-4l-3-3h-9c-20 1-38 8-56 17-1 0-3 1-4 1v1c-2 1-5 3-8 4-6 3-12 7-16 12-2 1-3 2-3 4 2-2 5-4 8-6 0 2-2 3-3 4-3 5-8 8-12 12-3 3-6 6-9 8s-6 4-8 6h-2c-2 1-3 4-6 3v-2h-1v3h-1c-1-11 3-22 4-33h1v-1h0v-3h-1 0l-1-2 14-66 4-17c1-3 1-9 3-11 1-1 2-1 3-1 5-1 11-1 16-1h31 76 43z" class="k"></path><path d="M298 187c1-2 1-2 3-3v2l-3 1z" class="C"></path><path d="M255 206l2-1c0 1 1 1 2 2h0c-1 0-3 1-4 1l1-1-1-1z" class="N"></path><path d="M367 237v1l1-1h0v1 3l-1 6h0l-1-2 1-8z" class="c"></path><path d="M368 241h4v4l-4 2h-1l1-6z" class="N"></path><path d="M343 303c3 0 6 0 8 2 1 0 1 1 1 1v5-3-1h-4l1-1c-1-1-4-1-6-1l1-1-1-1z" class="W"></path><path d="M351 545c0 1 2 4 3 5 1 0 2 0 3 1h-1l-2 1-2 1c-1-1-1-1-1-3h0c-1-1-1-2-2-3v-1l2-1z" class="f"></path><path d="M351 550h1c1 0 1 0 1 1l1 1-2 1c-1-1-1-1-1-3z" class="X"></path><path d="M366 245l1 2 1 1v2c-2 0-3 1-4 2h0 1v1 1h0c1 1 1 1 1 2s0 1-1 2h-1c1-2 0-2-1-3-2-3 1-4 2-6 1-1 1-3 1-4z" class="b"></path><path d="M327 530v-2c2 1 4 1 5 2s1 1 1 3v6h-1-1v-1-6h0c-1-1-3-2-4-2z" class="Y"></path><path d="M327 530v-2c2 1 4 1 5 2v1l-1-1h-1 0l1 1v1c-1-1-3-2-4-2z" class="K"></path><path d="M349 212c0-1 1-2 1-4h0 1v1h1 1c0-1 0-2 1-3 1 0 0 1 1 2 1 0 2 0 3-1l1-4c1 1 1 2 1 4 0 0 1 0 2 1-5 1-9 2-12 5h-1 0v-1z" class="q"></path><path d="M385 152h1c1 0 0 0 1 1-1 1-1 1-1 3l-2 1h2l1 1c-2 0-5 2-6 3l-1 1v1h-1c0-2 1-3 1-4l3-6c1 0 1-1 2-1h0z" class="b"></path><path d="M385 152h1c1 0 0 0 1 1-1 1-1 1-1 3l-2 1-1 1h0c0-2 1-4 2-6z" class="M"></path><path d="M389 132h1v2l-1 8c-1 3-1 8-4 10-1 0-1 1-2 1 1-2 2-3 3-5 0-2 0-4 1-6l2-10z" class="q"></path><defs><linearGradient id="AP" x1="313.524" y1="183.493" x2="301.381" y2="183.663" xlink:href="#B"><stop offset="0" stop-color="#b5b4b5"></stop><stop offset="1" stop-color="#dbdadb"></stop></linearGradient></defs><path fill="url(#AP)" d="M301 184c1 0 3-1 4-1 6-2 12-2 18-2-2 0-2 1-4 2-1 0-3 1-4 1-5 1-9 1-14 2v-2z"></path><path d="M336 547l3-3c1-2 2-3 4-4 3 0 6 3 8 5l-2 1v1l-2 1v-3h-1-3v1c-1 1-1 1-3 2h-1l-1-1-2 3v-1-1h0v-1z" class="c"></path><path d="M338 547c2-2 3-4 5-6 2 1 4 2 5 3l-1 1h-1-3v1c-1 1-1 1-3 2h-1l-1-1z" class="M"></path><path d="M377 193c1-1 1-1 2 0v3 7c-1 0-2 0-2 1v2l-15 2c-1-1-2-1-2-1s3-1 4-1c1-1 0-3 1-4 1 1 0 2 1 4 1-1 3-1 4-2 0-1 0-1 1-2h0v2h1 4l1-11z" class="K"></path><path d="M383 168l1 2h2c-2 1-3 1-3 3v1c0 1 0 3-1 4v1 2c-1 1-1 2-1 3 1 1 1 1 2 1l1-1c1 0 1 0 2 1-2 0-5 1-7 2l2 1c-1 0-1 0-2 1l-1 1s-2-2-3-2l5-12c0-2 1-5 2-6l1-1v-1z" class="b"></path><path d="M383 174c0 1 0 3-1 4v1 2c-1 1-1 2-1 3 1 1 1 1 2 1l1-1c1 0 1 0 2 1-2 0-5 1-7 2h-1 0c2-4 2-9 5-13z" class="Z"></path><defs><linearGradient id="AQ" x1="318.979" y1="185.002" x2="312.482" y2="184.316" xlink:href="#B"><stop offset="0" stop-color="#5d5b5c"></stop><stop offset="1" stop-color="#767776"></stop></linearGradient></defs><path fill="url(#AQ)" d="M323 181l2 1c-1 0-1 1-2 1 0 1-1 1-1 2l-2 1c-10 1-20 2-30 5l1-1c0-1-1-1-1-1h-1l9-2 3-1c5-1 9-1 14-2 1 0 3-1 4-1 2-1 2-2 4-2z"></path><path d="M337 328v1 5c1 0 1 0 1-1v2c2 0 5-1 7 0h0c1 0 1 0 1 1-2 0-6-1-7 0v2 2 6 3 2 1 2 1 1 3 1 1 2s0 1 1 2l-2 1v1c0 1 0 1-1 1v1c-1-1 0-6 0-8v-33z" class="Y"></path><path d="M337 361c0-1 1-1 1-2v1h0l1 1v2s0 1 1 2l-2 1v1c0 1 0 1-1 1v1c-1-1 0-6 0-8z" class="K"></path><path d="M338 547l1 1h1c2-1 2-1 3-2v-1h3 1v3l-3 3-2 1c-1 1-2 2-3 2l-1 1v1h-1l1 1v1l-1 2c-1-1-2-2-3-2h-1v-1c1-3 2-5 3-7l2-3z" class="n"></path><path d="M338 555v1h-1l1 1v1l-1 2c-1-1-2-2-3-2 1-2 3-2 4-3z" class="Z"></path><path d="M338 547l1 1h1c2-1 2-1 3-2v-1h3l-8 7c-1 2-3 3-4 4l-1 1c1-3 2-5 3-7l2-3z" class="H"></path><path d="M337 328c0-2-1-7 0-9 0-2 1-3 2-5 1-3-1-6 0-9 1-1 2-1 4-2l1 1-1 1c2 0 5 0 6 1l-1 1h-1v1h1c1 1 1 1 1 2h-1c0 1 1 1 1 1 0 1-1 1-2 1s-3 0-4 1h-3v3c1 1 2 0 3 0 0 1-1 1-2 1 0 0-2 0-2 1-2 2-1 4-1 6v6 3c0 1 0 1-1 1v-5-1z" class="q"></path><path d="M343 305c2 0 5 0 6 1l-1 1h-1v1h-3l-2 2c-1 0-1 1-2 1v-4-1c1-1 2-1 3-1z" class="V"></path><path d="M347 308h1c1 1 1 1 1 2h-1c0 1 1 1 1 1 0 1-1 1-2 1s-3 0-4 1h-3v-2c1 0 1-1 2-1l2-2h3z" class="B"></path><path d="M350 383h1c5 1 10 6 13 10l1 3c1 2 2 3 2 6h1v2h-1c-1 0-1 0-2 1 0-1-1-3-2-4-4-6-9-11-14-16l1-1v-1z" class="W"></path><path d="M350 383h1c5 1 10 6 13 10l1 3c1 2 2 3 2 6h1v2h-1c-1-2-2-5-3-7-3-5-9-11-14-14z" class="B"></path><path d="M325 182c1 0 2 0 2 2h0l1 6c-6-3-12-2-18-2-12 2-24 5-35 9l-18 8-2 1-15 8h0c15-10 32-18 50-23 10-3 20-4 30-5l2-1c0-1 1-1 1-2 1 0 1-1 2-1z" class="K"></path><path d="M325 182c1 0 2 0 2 2h0c-1 0-1 0-2 1s-4 1-5 1l2-1c0-1 1-1 1-2 1 0 1-1 2-1z" class="g"></path><path d="M360 305c-1-1-1-3-1-4l1-7c-2-1-3-1-4-2-2-1-5-3-5-5-2-5 3-6 6-9 1-1 2-3 3-5l1 3h1v2c-3 1-9 3-10 6v2c0 1 0 1 1 2 1 0 2 1 4 1h0c1-1 1-1 2-1 0 1 0 1-1 2h1c1 0 1-1 2-2h1 0l1 1c-1 0-2 0-2 2h1l1-1c1 1 2 2 2 3v1c0 1 1 2 1 3v3 1h-1l1 1c0 1 0 1-1 2h-2-2-1v1z" class="r"></path><path d="M366 297c-1 0-4 1-5 0v-3c2-1 3-1 4 0 0 1 1 2 1 3z" class="N"></path><path d="M361 304c0-1-1-2 0-4 0-1 1-1 2-2h3v3h-1l1 1c0 1 0 1-1 2h-2-2z" class="C"></path><path d="M362 276c1 0 2 0 3-1v-10c0-2 0-2-1-4h-1c0-2 1-2 1-3h1v1 1l2 1 3-1-1 1c2 1 3 1 5 1l1 1h-1l-1 1v2h0l3 1h1l-1 3h1l1-1c1 1 0 2 0 3h0l1 1c-1 0-1 0-1 1l-16 4v-2z" class="c"></path><g class="E"><path d="M370 268h2l-1 1v1h0v1c0 2-2 2-3 3l-1-1s0-1 1-1v-1c1-1 2-2 2-3z"></path><path d="M370 260l-1 1-1 1c1 2 0 2 0 4v1h1v-2c1 0 2 0 2 1h1l-2 2c-1 0-1 1-2 2h0-1c0-1 0-1 1-3h-1c0-2-1-5 0-6l3-1z"></path></g><path d="M369 261c2 1 3 1 5 1l1 1h-1l-1 1v2h-1-1c0-1-1-1-2-1v2h-1v-1c0-2 1-2 0-4l1-1z" class="W"></path><path d="M372 266h1 0l3 1h1l-1 3h1c0 1 0 1-1 2s-1 1-2 1h-1 0c-2 0-1-2-2-3h0v-1l1-1h-2 0l2-2z" class="K"></path><path d="M373 266l3 1h1l-1 3h1c0 1 0 1-1 2s-1 1-2 1h-1l2-2c1 0 1-1 1-2-2-1-2-1-3-3z" class="O"></path><path d="M349 547c1 1 1 2 2 3h0c0 2 0 2 1 3l2-1 2-1c0 1 1 2 2 3-1 1-3 2-4 3-2 1-3 2-5 4h0c-1 0-2 1-4 1h0-1v-1-1h-2 0c-2 1-2 1-4 1-1 0-2 1-3 1l-1 1c-1 1-1 0-2 0 1-1 3-2 5-3l1-2v-1l-1-1h1v-1l1-1c1 0 2-1 3-2l2-1 3-3 2-1z" class="B"></path><path d="M344 551c1 0 1 0 1 1s0 1-1 2c-1 0-1-1-2-2l2-1z" class="C"></path><path d="M351 550h0c0 2 0 2 1 3l-8 4h0c-2 1-4 2-6 4-1 0-2 1-3 1l-1 1c-1 1-1 0-2 0 1-1 3-2 5-3l1-2v-1l-1-1h1v1h1c1 0 1-1 2-1v-1h1v1h1c1-1 2-2 3-2s1 0 2-1c1 0 2-1 3-3z" class="T"></path><path d="M354 552l2-1c0 1 1 2 2 3-1 1-3 2-4 3-2 1-3 2-5 4h0c-1 0-2 1-4 1h0-1v-1-1h-2 0c-2 1-2 1-4 1 2-2 4-3 6-4h0l8-4 2-1z" class="k"></path><path d="M354 552l2-1c0 1 1 2 2 3-1 1-3 2-4 3 0-1 0-1-1-2 0-1 0 0-1-1 0 1-1 1-2 2h-2 0c-1 0-2 0-2 1h-2 0l8-4 2-1z" class="C"></path><path d="M363 290s0-1 1-1l3-3v1 1h-1 0l-1 1 1 1h0c1 0 1-1 2 0 0 1 0 1-1 2v1 5c1 2 0 3 1 4h1 0c-1 0-1 0-2 1v1 3l1 1c-1 2-1 2-1 4 1 2 0 4 0 6v1c0 2 1 5 0 7-1-1-1-2-1-3v-6h-4v-3c-1 2-1 4-1 6v11 5c-1 4 0 9 0 12 0 1 0 4-1 4v-3h0v-31-13-1h1 2 2c1-1 1-1 1-2l-1-1h1v-1-3c0-1-1-2-1-3v-1c0-1-1-2-2-3z" class="q"></path><path d="M362 314c0-1 0-1 1-2h-1c-1-1-1-1-1-2 1-1 0-2 0-4 1 0 3-1 5-1v3h-1c0 1 0 1 1 1v3h-1l1 1v4h-4v-3z" class="N"></path><path d="M370 417v-2l1-1c1 1 1 3 0 4h1l1 4v9c0 1-1 2 0 3v2 1c-1 2 0 4 0 6v5h8 0 11v1h-5-3c-9 0-18 1-26 3h0l-1-1c4-1 7-2 11-2v-5-14-10-2l2-1z" class="q"></path><path d="M368 420c1 0 1 0 1 1 1 0 1 1 1 2v7c-1-2 0-5-1-6 0 2 0 4-1 6h0v-10z" class="p"></path><path d="M370 417v-2l1-1c1 1 1 3 0 4 1 1 1 2 0 4v5 11c0 3 1 8-1 10h0c-1-2 0-4 0-6v-12-7c0-1 0-2-1-2 0-1 0-1-1-1v-2l2-1z" class="N"></path><path d="M368 418l2-1v6c0-1 0-2-1-2 0-1 0-1-1-1v-2z" class="b"></path><path d="M334 541v1c1 0 0 1 0 1l1 1v2l1 1v1h0v1 1c-1 2-2 4-3 7v1h1c1 0 2 1 3 2-2 1-4 2-5 3s-2 2-4 2h0c-1 1-1 0-1 1v2c-2 0-3 1-4 1h0c1-8 0-17 1-25h0c1 0 0 0 1 1v1h2c2 0 2 1 4 0 1-2 3-3 3-5z" class="Y"></path><path d="M332 554c-1 1-1 3-2 4h-1v-2l-1-1v-1h1l-1-1v-3l2 2c1 0 1 1 2 2z" class="F"></path><path d="M333 558h1c1 0 2 1 3 2-2 1-4 2-5 3s-2 2-4 2h0c1-2 3-5 5-7z" class="Q"></path><path d="M334 541v1c1 0 0 1 0 1l1 1v2l1 1v1l-3 4-1 2c-1-1-1-2-2-2l-2-2 1-1h-1v-1-1l-1-1c2 0 2 1 4 0 1-2 3-3 3-5z" class="J"></path><path d="M334 541v1c1 0 0 1 0 1l1 1v2l1 1v1l-3 4h-1c0-2 0-3 1-4l1-3h0c-1 0-2 1-2 2l-1-1c1-2 3-3 3-5z" class="V"></path><path d="M368 487c1 0 1 0 2 1h0 1v1h-1 0l1 1h-1c0 1 1 1 2 1v1 3 1 4 2c0 2-1 5 0 7v10 11 4h0c1-1 1-1 1-2v1l2 1h0-2c-1 2 0 5 0 7v2 2 1h0v2h-2l-1 1c-1 1-1 1-1 2l1 1c0 1 0 1-1 2 0 0-1 0-2 1-1 0-1 0-1 1h-1v1h-2v1l-1-1c-1-1-1-1-2-1v1h0c1 1 1 1 1 2v1h0v-1l-2-2c-1-1-2 0-3 0-2 1-3 2-4 3s-1 1-2 1h-1c2-2 3-3 5-4 1-1 3-2 4-3-1-1-2-2-2-3h1c2-2 4-4 8-4h3v-1c1-1 0-3 0-5l1-10v-39c-1-1-1-1-1-2-1-1 0-2 0-3z" class="W"></path><path d="M360 553c-1 0-1 0-1-1 1-2 4-4 6-4v1h-1 0c0 2-1 2-3 3h-1v1z" class="E"></path><g class="B"><path d="M360 553v-1h1c2-1 3-1 3-3h0 1c1 0 3 2 4 2l1 1c0 1 0 1-1 2 0 0-1 0-2 1-1 0-1 0-1 1h-1-2v-2h0l-1 1h0-1s0-1-1-1v-1z"></path><path d="M370 530h2v4h0c1-1 1-1 1-2v1l2 1h0-2c-1 2 0 5 0 7v2 2 1h0v2h-2l-1 1v-1-5-12-1z"></path></g><path d="M372 534h0c1-1 1-1 1-2v1l2 1h0-2c-1 2 0 5 0 7v2 2 1h0v2h-2l1-14z" class="X"></path><path d="M368 487c1 0 1 0 2 1h0 1v1h-1 0l1 1h-1c0 1 1 1 2 1v1 3 1 4 2c0 2-1 5 0 7v10 11h-2v1c-1 1 0 3 0 4h-1v-9-22c0-4 1-9 0-12-1-1-1-1-1-2-1-1 0-2 0-3z" class="p"></path><path d="M370 501h0c1 1 1 2 1 2h0l1-1c0 2-1 5 0 7l-2 1v-9z" class="D"></path><path d="M370 510l2-1v10c-1 0-2 1-2 1v-10z" class="H"></path><path d="M370 520s1-1 2-1v11h-2c-1-3 0-7 0-10z" class="L"></path><path d="M370 501h0c0-2 0-8 1-10l1 1v3 1 4 2l-1 1h0s0-1-1-2h0z" class="B"></path><path d="M348 307h4v1 3h0l3 9v13l1 26h4v-10h0v3c1 0 1-3 1-4 0-3-1-8 0-12v23c2-1 4-1 5-1h2c2-1 3-1 5-1s4-1 6-1h0l14-1h0c1 1 2 1 3 1v1h-2v1h0v3h-1l-1-1v1s1 1 1 2c1 1 1 5 0 7h0c0 1-1 2-1 3-1 1-1 0-2 0s-1 0-2-1v1l-1 1h-1 0-3v1h5c-1 0-2 0-3 1 2 1 7 0 10 0h-6c-1 1-1 1-2 1l-1 1c0 1 0 1-1 2 1 1 1 2 2 4h1v1c0 2 0 3 2 4l-4 2 1 2c2 0 3 0 4 1v2c-1 1 0 2 0 3v1c-1-1-1-1-2-1v2l1 1h-8 0l-1 1c-2 0-5 0-8 1h-3 0-2v-2h-1c0-3-1-4-2-6l-1-3c-3-4-8-9-13-10h-1v1l-1 1-2-1h-1l-3-3v-3c0-1 1-2 1-3-2-4-4-3-7-5v-1-1c1 0 1 0 1-1v-1l2-1c-1-1-1-2-1-2v-2-1-1-3-1-1-2-1-2-3-6-2-2c1-1 5 0 7 0 0-1 0-1-1-1h0c-2-1-5 0-7 0v-2-3-6c0-2-1-4 1-6 0-1 2-1 2-1 1 0 2 0 2-1-1 0-2 1-3 0v-3h3c1-1 3-1 4-1s2 0 2-1c0 0-1 0-1-1h1c0-1 0-1-1-2h-1v-1h1z" class="p"></path><path d="M339 338c1 0 2 0 3 1 1 0 3 0 4-1l4 1v1h-4-7v-2z" class="k"></path><path d="M346 336l4 1h0 0c-2 1-3 1-4 1h0c-1 1-3 1-4 1-1-1-2-1-3-1v-2c1-1 5 0 7 0z" class="I"></path><path d="M346 364c3 0 5 1 7 2-4 0-11 0-15 1v-1l2-1 3-1h3zm7 13c0-2 0-3 1-4 2-3 3-5 3-8v-1h1l1 10c-1 0-1 1-2 2s-3 1-4 1z" class="M"></path><path d="M338 330c2 0 8-1 10 0h1v1h-2c-1 0-1 0-1 1h1 3v2l-5 1c-2-1-5 0-7 0v-2-3z" class="N"></path><path d="M338 324h2 1 0c2-1 3-1 4-1h3v1h2 0c-1 1-2 1-3 2h2c0 1-1 1-1 2h2v1l-2 1h1-1c-2-1-8 0-10 0v-6z" class="C"></path><path d="M383 375h5c-1 0-2 0-3 1 2 1 7 0 10 0h-6l-12 1h-6 0l-20 3c-2 0-5 1-6 0v-1c2-1 6-1 8-1 2-1 4-1 6-1l13-1 11-1z" class="E"></path><path d="M366 367h1c1 0 0-3 1-4v10 1l1 1c0-1 1-1 1-2 1 1 0 1 1 2h1v1l-13 1c-2 0-4 0-6 1v-1c1 0 3 0 4-1s1-2 2-2h1v-2h2v-3l1 1h0c1 0 1 0 1 1 1-1 1 0 1-1s-1-1-1-2h0l1 1 1-1v-1z" class="W"></path><path d="M362 369l1 1h0c1 0 1 0 1 1 1-1 1 0 1-1 1 1 1 3 1 5l-1 1c-1 0-2 0-3-1v-3-3z" class="L"></path><path d="M368 362v-2c2 0 4-1 5 0h2c0 1-1 3 0 4 0 2-1 4 0 6h-2v1h0c-1 1-1 2-1 3v1h-1c-1-1 0-1-1-2 0 1-1 1-1 2l-1-1v-1-10-1z" class="I"></path><path d="M368 362v-2c2 0 4-1 5 0h2c0 1-1 3 0 4 0 2-1 4 0 6h-2v1h0s-1-1-1-2c0 0 1 0 1-1v-2c-1 0-1-1-2-1 0-1 0-1-1-2h0v-1h-2z" class="L"></path><path d="M373 360h2c0 1-1 3 0 4 0 2-1 4 0 6h-2v1h0s-1-1-1-2c0 0 1 0 1-1v-2h0v-6z" class="b"></path><path d="M339 355h3v-1h2c1 0 0 0 1 1 1-1 1-1 2 0h4v1c-1 1-1 1-2 1 1 1 1 1 2 1v1h-3c1 1 1 1 2 1v1h-5v1h0c1 0 3 0 4 1-1 0-1 0-2 1h-1-3l-3 1c-1-1-1-2-1-2v-2-1-1-3-1z" class="D"></path><path d="M339 355h3v-1h2c1 0 0 0 1 1 1-1 1-1 2 0h4v1h-12v-1z" class="N"></path><path d="M347 312l3 3c-1 0-2 1-3 1 2 1 3 1 5 2l-1 1c-2 0-5-1-7 0h-1 1c2 0 3 1 5 1v1h-3l2 1v1h-3c-1 0-2 0-4 1h0-1-2c0-2-1-4 1-6 0-1 2-1 2-1 1 0 2 0 2-1-1 0-2 1-3 0v-3h3c1-1 3-1 4-1z" class="M"></path><path d="M347 312l3 3c-1 0-2 1-3 1h-1c0-1 0 0 1-1-1-1-1-1-1-2h0-3c1-1 3-1 4-1z" class="L"></path><path d="M371 377h6l12-1c-1 1-1 1-2 1l-1 1c0 1 0 1-1 2 1 1 1 2 2 4h1v1c0 2 0 3 2 4l-4 2c0-1 0-2-1-3 0-1-1-2-2-3-1 1-1 2-2 2 0-1-1-1-1-2-1 1-2 1-3 1l-1-1-1 1v-1c2-2 3-1 5-1v-1c0-1-2-1-3-2 0-1 0-2-1-2-1-2-3-1-5-2z" class="K"></path><path d="M383 385c-1-2-3-4-6-6h0v-1h1c1 1 4 3 5 5l1 1h1c-1-2-3-4-5-5v-1h1l1 1c1 0 2 1 3 1 1 1 1 2 2 4h1v1c0 2 0 3 2 4l-4 2c0-1 0-2-1-3 0-1-1-2-2-3z" class="S"></path><path d="M339 340h7l1 1c1 0 2 1 3 1v1c-1 0-1 0-2 1h2v1c-1 1-3 1-5 1h0 1c2 0 4 1 6 2-2 0-5 1-7 1h-1c2 0 4 0 5 1l3 1-7 1c1 0 4 0 5 1v1c-1 0-2 0-3 1-1-1-1-1-2 0-1-1 0-1-1-1h-2v1h-3v-1-2-1-2-3-6z" class="I"></path><path d="M360 349h0v3c1 0 1-3 1-4 0-3-1-8 0-12v23c2-1 4-1 5-1h2c2-1 3-1 5-1s4-1 6-1v1c1 0 2 0 2-1l1 1h-1l-1 1c-1 1-1 1-2 1-1-1-2 0-3 0v1h-2c-1-1-3 0-5 0v2 1c-1 1 0 4-1 4h-1v1l-1 1-1-1h0c0 1 1 1 1 2s0 0-1 1c0-1 0-1-1-1h0l-1-1v3h-2v2h-1l-1-10h1v-2-1c-1 0-2 1-4 0 0-1 0-1 1-2h4v-10z" class="Y"></path><path d="M362 369l-1-6h0c1-1 1-2 2-2l1 2 1-1c-1 0-1 0-1-1h2v4h-2c1 0 1 1 2 2h0v1l-1 1-1-1h0c0 1 1 1 1 2s0 0-1 1c0-1 0-1-1-1h0l-1-1z" class="I"></path><path d="M365 396l1-1-1-2c-1 0-1-1-1-1l-1-2-5-5-3-2s-1 0-1-1h0c1 0 3-1 4 0h2 2 0 1 2c1-1 4-1 5-1h1c0 1-1 1-1 2h2l-1 2c1 1 1 1 1 2 0 0 0 1-1 1l1 1c1 1 0 3 0 5v6 2h1v2h-3 0-2v-2h-1c0-3-1-4-2-6z" class="K"></path><path d="M370 381h1c0 1-1 1-1 2h2l-1 2c1 1 1 1 1 2 0 0 0 1-1 1l1 1c1 1 0 3 0 5v6 2h1v2h-3 0c0-2 0-2 1-4h-1-1v-4h2v-1l-1 1c-1-1-1-1-1-2h1v-1h-1v-5-6l1-1z" class="V"></path><path d="M383 385c1 1 2 2 2 3 1 1 1 2 1 3l1 2c2 0 3 0 4 1v2c-1 1 0 2 0 3v1c-1-1-1-1-2-1v2l1 1h-8 0l-1 1c-2 0-5 0-8 1v-2h-1v-2-6l1 3h1v-2h0v-1-3h0c0-2 1-3 1-5l1-1 1 1c1 0 2 0 3-1 0 1 1 1 1 2 1 0 1-1 2-2z" class="e"></path><path d="M387 396h-1c-1 0-4 1-5 0v-1h5l1 1z" class="D"></path><path d="M374 391c1-1 2-1 3-1v2h1c-1 1-2 1-3 1l-1-2h0z" class="d"></path><path d="M385 392l1 2h-4l-1-2h4z" class="F"></path><path d="M377 390h4 3l1 2h-4 0v-1l-3 1h-1v-2z" class="V"></path><path d="M374 395c2-1 4 0 6 0l1 1-1 1h0-6v-2h0z" class="Z"></path><path d="M374 398c2 0 3 0 5-1v1c2 0 6-1 8-1v1c-2 1-4 1-5 1-2 1-6 1-8 1v-2z" class="D"></path><path d="M375 386l1-1 1 1c1 0 2 0 3-1 0 1 1 1 1 2 2 1 2 2 3 3h-3-4c-1 0-2 0-3 1 0-2 1-3 1-5z" class="S"></path><path d="M383 385c1 1 2 2 2 3 1 1 1 2 1 3l1 2c2 0 3 0 4 1v2c-1 1 0 2 0 3v1c-1-1-1-1-2-1v2c0-1-1-1-1-2l-6 1v-1c1 0 3 0 5-1h0v-2l-1-1v-1l-1-2-1-2c-1-1-1-2-3-3 1 0 1-1 2-2z" class="b"></path><path d="M387 393c2 0 3 0 4 1v2c-1 1 0 2 0 3v1c-1-1-1-1-2-1v-1c-1-2-1-3-2-5z" class="o"></path><path d="M372 394l1 3h1v-2 2 1 2c2 0 6 0 8-1v1l6-1c0 1 1 1 1 2l1 1h-8 0l-1 1c-2 0-5 0-8 1v-2h-1v-2-6z" class="E"></path><path d="M372 394l1 3h1v-2 2 1 2c2 0 6 0 8-1v1l-1 1-1-1c-1 0-4 0-5 1-1 0-2 1-2 1h-1v-2-6z" class="Y"></path><path d="M379 356h0l14-1h0c1 1 2 1 3 1v1h-2v1h0v3h-1l-1-1v1s1 1 1 2c1 1 1 5 0 7h0c0 1-1 2-1 3-1 1-1 0-2 0s-1 0-2-1v1l-1 1h-1 0-3v1l-11 1v-1-1c0-1 0-2 1-3h0v-1h2c-1-2 0-4 0-6-1-1 0-3 0-4v-1c1 0 2-1 3 0 1 0 1 0 2-1l1-1h1l-1-1c0 1-1 1-2 1v-1z" class="L"></path><path d="M375 370c0 1-1 3 0 4h4c1-1 0 0 1 0s2-1 2-1v-2h1v2h2 0c-1-1-1-3-2-3 1 0 2 0 2 2l1 2h0-3v1l-11 1v-1-1c0-1 0-2 1-3h0v-1h2z" class="K"></path><path d="M380 358c1 2 0 4 0 5v1c-1 0-2 0-2 1v3c-1 0-1-1-1-1h-1v2h0c-1 0-1 0-1-1v-4c-1-1 0-3 0-4v-1c1 0 2-1 3 0 1 0 1 0 2-1z" class="M"></path><path d="M379 356h0l14-1h0c1 1 2 1 3 1v1h-2-1-4-1-2l-1 1h-1v1c0 2 0 5-2 6l-1 1c0 2 0 4-1 7h0v-1c0-2 1-5 0-8h0v-1c0-1 1-3 0-5l1-1h1l-1-1c0 1-1 1-2 1v-1z" class="W"></path><path d="M385 358l1-1h2 1 4 1v1h0v3h-1l-1-1v1s1 1 1 2c1 1 1 5 0 7h0c0 1-1 2-1 3-1 1-1 0-2 0s-1 0-2-1v1l-1-1c0-1-1-3 0-4v-3-1c0-2-1-4-1-6h-1z" class="c"></path><path d="M387 364v-3h1v4h-1v-1z" class="X"></path><path d="M393 363c1 1 1 5 0 7h0c0 1-1 2-1 3-1 1-1 0-2 0s-1 0-2-1v-1c0-2 1-2 2-3l1 1v-4l1 1h1v-3z" class="D"></path><path d="M394 357v1h0v3h-1l-1-1v1s1 1 1 2v3h-1l-1-1c0-1 0-1-1-1v4h-1c-2-2 1-3 1-5 0-1-2-4-2-5l5-1h1z" class="C"></path></svg>