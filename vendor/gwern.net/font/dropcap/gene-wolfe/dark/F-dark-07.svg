<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="288 84 555 852"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#282828}.C{fill:#3a3b3a}.D{fill:#575757}.E{fill:#2c2c2b}.F{fill:#434342}.G{fill:#323232}.H{fill:#121212}.I{fill:#1c1b1b}.J{fill:#aca292}.K{fill:#5f5e5d}.L{fill:#202121}.M{fill:#8e8778}.N{fill:#2d2e2d}.O{fill:#494641}.P{fill:#6e6a61}.Q{fill:#857f78}.R{fill:#080808}.S{fill:#181816}.T{fill:#a3937b}.U{fill:#ccbea8}.V{fill:#a89b86}.W{fill:#1c1a18}.X{fill:#e0d5c4}.Y{fill:#4e4e4f}.Z{fill:#676561}.a{fill:#c6b69f}.b{fill:#7a7772}.c{fill:#4d4d49}.d{fill:#101011}.e{fill:#7d725f}.f{fill:#bbb1a2}.g{fill:#bbac96}.h{fill:#97948d}.i{fill:#e3d9c9}.j{fill:#e0d2bc}.k{fill:#6b6a6a}.l{fill:#726d66}.m{fill:#d2bea4}.n{fill:#bda98e}.o{fill:#d8d1c8}.p{fill:#e1d8c9}.q{fill:#a08f74}.r{fill:#dad5cb}.s{fill:#8e7e66}.t{fill:#665a44}.u{fill:#f8f7f4}</style><path d="M565 220c-1-1-1 0-2-1v-1c1-1 2-1 3-1l-1 3z" class="H"></path><path d="M612 409l2-2h2l-1 5h-1v-1l-2-2z" class="N"></path><path d="M421 473c-1 0-3 1-4 1v-1c0-2 0-2 2-3l2 2v1z" class="J"></path><path d="M418 480l1 1h2c1 1 1 1 1 2l-1 1h-3v-4z" class="G"></path><path d="M466 866c0-1 0-2 1-3h1c1 1 0 1 1 1l3 3c-2 0-4 0-6-1z" class="W"></path><path d="M588 317l1 1c0 3-1 7 0 10 0 1 1 2 1 3-1 0 0 0-1 1h-1 0v-4-2h0v-3h0v-3-3z" class="H"></path><path d="M546 163h2 3v2l-1 1h0-4c-1-1-1-1-1-2l1-1z" class="W"></path><path d="M472 867c2-1 2-2 4-2l1 1-1 2h0c1 1 1 2 1 3l1 1v1l-2-1c-1-3-2-3-4-5z" class="H"></path><path d="M598 446c2 1 3 2 5 3h0v1 3c-2 0-4-1-6-2v-1c1 0 2 0 3 1v-2h1v-1c-1 0-2 0-3-1v-1z" class="G"></path><path d="M594 466h1l6 2-1 1c-1-1-2-1-3-1-1 1-1 1-1 3-1 0-3-1-4-1l2-4z" class="D"></path><path d="M445 625h6v1 1l-1-1c-2 1-3 1-4 1s-1 0-2 1c-1-1-2-1-4-1v-2h5zm-27-145v-1c1-1 1-1 2-1h1 2c2 1 1 2 1 4-1 1-1 1-2 1 0-1 0-1-1-2h-2l-1-1z" class="I"></path><path d="M417 381c3 1 5 1 8 1l-10 5-1-1h1l-1-1 1-3 2-1z" class="n"></path><path d="M570 816c1 1 1 2 1 4-1 1-1 3-2 4s-1 2-2 3h0-1v-1h1l-1-1-1 1c0 1-1 1-2 2h0c2-2 4-5 5-7s2-3 2-5z" class="d"></path><path d="M512 89c1 1 2 0 2 1 1 1 1 7 1 8l-1 1h-2v-1c1-3 0-7 0-9z" class="e"></path><path d="M419 470l6-3c1 1 1 2 2 3v1h0c-2 0-4 1-6 2v-1l-2-2z" class="n"></path><path d="M568 638l1 2h1c0-2 0-2 1-3l1 1v2h-2c1 3 1 6 1 8l-2 2v-8c0-1-2-1-3-2l2-2z" class="H"></path><path d="M430 625h10v2h-2c-3 0-4 0-6-1h-1c-1 4 0 8-1 12h0l-1-1 1-1v-11z" class="W"></path><path d="M454 816c1 0 1 0 1-1 1-1 1-1 3-2 0 1 1 1 2 1l1 1c-2 1-3 1-4 2 0 1 0 2 1 3v1c1 1 1 1 1 2l1 1c-4-2-5-4-6-8z" class="N"></path><path d="M562 439h3l-1 2v1h1c1 0 2 0 4 1l-3 1h-7c1-2 1-3 1-5h2z" class="D"></path><path d="M457 603v-1-3l3 3v4 4c1 0 1 1 2 1 0 2 0 6-1 7l-2-1 1-2c-1-3 0-6-1-9 0-2 0-2-2-3z" class="S"></path><path d="M563 571l1 1v-1h3l2 1v7h-1l-1-1c-2-2-2-3-2-5h-2v-2z" class="B"></path><path d="M400 892c0-1 1-2 1-3l-1-1 1-1h4c0 1-1 1-1 3h3c2 0 2 1 3 1v1h-10z" class="h"></path><path d="M569 443c4 0 10 0 14 2h0l-1 1-1-1 1 1h1l-1 1-16-3 3-1z" class="Y"></path><path d="M460 789c1 4 0 10 1 13s1 9 1 12l-1 1-1-1v-13-12z" class="d"></path><path d="M448 714l1-2c0 1 1 2 1 4v3l1-1v9l-1 1-1-1c0-1 0-2-1-4v-9z" class="O"></path><path d="M429 415c1-1 2-1 3-1l1 1c-4 2-8 3-11 5-2 1-4 3-5 4h-1l-1-2c2-3 11-5 14-7h0z" class="Q"></path><path d="M565 221c0 1 1 2 0 3l-3 3v1l-6 4h-2v-1c2-1 3-2 4-4 0-1 1-3 2-4v3h1c2-1 3-3 4-5z" class="K"></path><path d="M585 560c1 6 1 13 1 19l-1-1-2 1c0-4 1-8 1-12 0-1-1-5 0-6 0 0 0-1 1-1z" class="Y"></path><path d="M591 686v-2c1 0 2 0 2-1v-1c-1 0-2 0-3 1h0-1l1-1h1c0-1 0-1 1-1h1v-2h1 0v6l-1-1v2c-1 1-1 2-1 3v1l-1 13h0c-1-1 0-4 0-5v-12z" class="E"></path><path d="M596 471c0-2 0-2 1-3 1 0 2 0 3 1l1-1c2 1 3 2 4 3l1 3c-2 0-8-2-10-3z" class="F"></path><path d="M431 482c-1-1 0-1-2-1-1 0-1 0-2-1h1c0-2 0-4 1-5l5-3v1 4c-1 1-2 1-3 1h0v4z" class="H"></path><path d="M435 645s1-1 1-2h0c1-1 1-2 1-4h0 1v14l-1 1h-1c-1-2-1-6-1-9z" class="P"></path><path d="M511 103c2-1 3-1 4 0v4c-1 1 0 3 0 4 1 1 1 1 2 1h1c-1 1-2 1-4 1h-1c0-1-1-1-1-1v-2l-1-7z" class="h"></path><path d="M593 618v10c1 2 0 4 1 5-1 0 0-1-2-1 0 2 0 4-1 6v-1c0-4-2-9 0-13h1c1-2 1-4 1-6z" class="B"></path><path d="M566 682l1-2h0c0 2 0 3 2 5 0 1 0 2-1 3-2 1-4 0-6 0 0-3 0-6 1-9v5h1v-1h2v-1z" class="E"></path><path d="M566 682h-1-1l2-4c0-3 0-5 1-8l1-1h1v16c-2-2-2-3-2-5h0l-1 2z" class="l"></path><path d="M653 146h19l-3 1c1 1 2 1 3 2h-3l-13-1c-1 0-2-1-3-2z" class="f"></path><path d="M547 183h3c3 5 5 11 7 16v3 1 1 2 1c-2-6-4-12-7-18l-3-6z" class="S"></path><path d="M578 790v-4c1-1 1-2 2-3-1-1-1-1-2-1l1-1h3c1-1 1-2 2-3l1 1c0 1-1 2-2 3h1v1h4c-2 2-5 4-7 4l-3 3z" class="I"></path><path d="M581 560c1 0 3-1 4 0-1 0-1 1-1 1-1 1 0 5 0 6 0 4-1 8-1 12v1c-1 0 0 1-1 1l-1-2v-13-6h0z" class="B"></path><path d="M615 544c2 0 3 0 5 1h6l4-1 2 1c-2 1-3 1-5 1v1c-3 0-8 0-10 1l-12 1v-1c1-1 2 0 3-1 4 0 10 0 13-1h-1c-2-1-3-1-5-2z" class="N"></path><path d="M507 94l2-5v-2h0l1-1h2v3c0 2 1 6 0 9h-4v-2l-1-2h0z" class="o"></path><path d="M562 612c0 1 0 1 1 2v1c-1 6 0 13 0 19h4 2l-1 4-2 2h-3c-1-3-1-7-1-9v-13h1c0-1 0-2-1-3v-3z" class="E"></path><path d="M591 624v-4c-1-2 0-4 0-6-1-4-1-7 0-11h2v-1 1 15c0 2 0 4-1 6h-1z" class="L"></path><path d="M563 667l3 1c1-1 2-1 3-2v3h-1l-1 1c-1 3-1 5-1 8l-2 4h1 1v1h-2v1h-1v-5-12z" class="Z"></path><path d="M584 720l1-17c0-3-1-7 0-9 2 1 1 4 2 6h0v9c0 3 0 7-1 10l-2 1z" class="G"></path><path d="M435 645v-16c3 0 5 1 8 1h6c-2 1-7 1-9 1h-1l-1 1v7h-1 0c0 2 0 3-1 4h0c0 1-1 2-1 2z" class="Z"></path><path d="M414 359l3-2 2 3 2-1-1 2c2 2 4 0 6 1-5 2-9 3-13 5 0 0-1-1 0-2 0-2 1-2 2-3l-1-3z" class="j"></path><path d="M414 359l3-2 2 3c-1 1-3 2-4 2l-1-3z" class="o"></path><path d="M587 696c0-4 1-8 2-12h0v9h0c1-2 1-5 2-7v12c-1 3-1 6-2 9h0l-1 3v-15l-1 1z" class="G"></path><path d="M561 812v1l1 1h5l1 1v1 4 1c-1 2-3 5-5 7-1 0-2 1-3 1v-3h2c1-3 0-5 1-8v1l1-1 1 1v1c1-1 1-2 1-3v-2h-2-1c-1 0-1 0-2-1v-2z" class="L"></path><path d="M435 462l8-2v1c-2 1-3 1-5 2-2 0-3 0-4 1h0l-2 2v1h2l1 1-8 3v-1c-1-1-1-2-2-3l7-3-8 2c1-1 2-2 4-2s2 0 4-1c1 0 1 0 3-1z" class="a"></path><path d="M576 630c2 0 6-1 8 0h1c1 2 1 20 1 23l-1 1h-1c-1-7-1-15 0-22v-2l-1 1c-3 0-6 1-8-1h1z" class="F"></path><path d="M572 713l1 1v-1l1 3h0c0-2 0-4 1-5v-2h0v-2c0-2 1-2 2-3v1l1-1c1-1 1-2 2-3v1c0 1-1 2-1 2-1 1-2 2-2 4-2 3-4 10-3 14l1 1c1 1 3 1 4 1-2 1-5 4-8 4v-1l1-14h0z" class="d"></path><path d="M571 477l1 2 2-1v-4h1c0 2 0 3 2 4h0c1 1 1 0 2 1v1h-2 3 1v-1h0v-2h0c0 2 1 2 2 3l1-1 1 1v-2c0-1 1-2 1-2 1-2 1-2 1-3h0c1 3 1 7 1 10-5-1-10-3-15-3-1-1-1-1-2-3z" class="B"></path><path d="M464 845h-1v-1h2s1-1 1-2h0-3l1-1h1v-1c0-1 0-1 1-2 3 8 9 13 13 20-2-1-2-2-4-4-1-1-3-2-4-4 0-1-2-2-3-4 0-1 0-1-1-1v2l1 1c-1 1-2 1-3 1v-1-2l-1-1z" class="S"></path><path d="M561 775h0l1 1c2 2-1 7 2 10 1 0 1 0 2-1 1 0 1 0 2 1h2 1 2v1c1 0 1-1 3 0-1 1-1 1-1 2-3-2-3-1-5-1v-1l-1 1s-1 1-2 1h-1l-1-2-1 1v2l-3 1h0l-1-1c1-1 1-2 1-3h0v-12z" class="W"></path><path d="M464 845l1 1v2c-1 0-2 1-3 1 0 1 0 1-1 2-1-1-1 0-1-1-1 0-1 1-1 2-1 1-4 3-5 3-2 0-2 0-3 1h-4l1-1v-1s1 0 2-1h0 1l3-3h1c1 0 2-2 2-3h1c1 0 0 0 1-1h1c0 1 0 1 1 1h1 1l1-2z" class="E"></path><path d="M616 526c1 0 1-1 2-1h1c4-1 9-1 14 0h4c2 0 5 3 8 4h1l1 2c-2 0-3-1-5-1l-3-1c-5-1-9-3-14-3h-9z" class="Q"></path><path d="M464 370l1 1c1-4 1-8 2-11v6l3-1c2-1 2-1 3-3h0c0 3 0 5-1 8v3l-3-2-2 2-1-1c-1 1 0 0 0 1-1 1-1 2-1 3l-1-1v-5z" class="c"></path><path d="M594 331c1-2 1-4 1-6 2 0 2-1 4-2-1-1-1-2 0-3v-1c0 2 1 7 1 9s-3 5-4 7l-2 2h-1l-2-5c1-1 1-2 2-2l1 1h0z" class="Y"></path><path d="M593 330l1 1h0l-1 6-2-5c1-1 1-2 2-2z" class="F"></path><path d="M640 866l1-1c4 3 8 5 13 6l-2 2c-1 0-2-1-2-1l-3-1-2 2h-1l-2-1h-2-1c-1-3 0-4 1-6z" class="i"></path><path d="M640 866c2 2 1 4 2 6h-2-1c-1-3 0-4 1-6z" class="r"></path><path d="M456 789h2 0c1 0 1 0 2-1h0v1 12 13c-1 0-2 0-2-1 0-4-1-9 0-13 0-2 0-9-1-10l-1-1zm111-583h1l1-1v1c1 2 2 3 3 5h0l1 1-1 1h-2v2l-1 1c0 1 1 1 0 2h-2c0 1-1 2-1 4l2 2c-1 1-4 2-5 4h-1v-1l3-3c1-1 0-2 0-3v-1l1-3c0-1 1-2 1-3 1-3 1-5-1-8h1z" class="D"></path><path d="M567 218c0-2 1-5 1-7l2-2 2 2 1 1-1 1h-2v2l-1 1c0 1 1 1 0 2h-2z" class="S"></path><path d="M418 519c2 0 3 0 5-1v1l1 1-1 1c2 0 4 0 5-1l1 2v1h2 3 1l-2 1-14 3c0-2 0-5-1-7v-1z" class="a"></path><path d="M610 360c3 2 5 4 7 6 0 3 0 6-1 8-1-3-5-6-8-8 0-2 1-2 0-4l2-2z" class="N"></path><path d="M562 776h12l-1 2h-3-3l-1 1c0 1 0 2 1 3v1s1 1 2 1l1-1h1v2h1v-1l1-1 1 1c0 1-1 1-1 2h-2-1-2c-1-1-1-1-2-1-1 1-1 1-2 1-3-3 0-8-2-10z" class="E"></path><path d="M512 114v13h-6v-1c0-3 0-10 1-11h0l1 10h0v-11h1l1 1v1c2-1 1-1 2-2z" class="o"></path><path d="M587 462h1v2c1 1 4 1 6 2l-2 4-14-3v-1-1h3l-1-1c1-1 1-1 1-2l6 1v-1z" class="K"></path><path d="M672 146h26v1c-2 1-5 1-7 1-6 1-13 1-19 1-1-1-2-1-3-2l3-1z" class="r"></path><path d="M441 354h0c3 1 5 0 8 1-3 1-7 2-9 4l-14 3c-2-1-4 1-6-1l1-2s0-1 1-1c1-1 1 0 2 0 1 1 2 1 4 1 2 1 12-1 14-3l-1-1v-1z" class="X"></path><path d="M637 539c-4-1-9-1-14-1h-5v-3c1-1 3 0 5 0h15l-1 4z" class="L"></path><path d="M587 443l2 1h2l1 1h1c2 0 3 1 5 1v1c1 1 2 1 3 1v1h-1v2c-1-1-2-1-3-1v1c-5-1-10-3-15-4l1-1h-1l-1-1 1 1 1-1 4 1h0 1c-1-1-1-1-1-2v-1z" class="C"></path><path d="M593 643c2 4 1 10 1 14 0 5 0 10 1 14l-2-1-1 1c0-3-1-6-1-10 0-1 1-2 1-3 0-5-1-10 1-15z" class="F"></path><path d="M593 670h0c2-5-1-11 0-16 1-1 1-3 1-4v6 1c0 5 0 10 1 14l-2-1z" class="N"></path><path d="M447 856h4c1-1 1-1 3-1l-2 2c1 1 2 1 3 0l1-1c2 2 2 3 2 6-1 0-2-1-3 0v1h-1-2l-6-3-3-3h0c2-1 3-1 4-1z" class="H"></path><path d="M636 542h1l-1 1v4c1 1 2 1 3 1-2 0-3 0-4-1v1c1 1 3 1 4 2 3 0 10 2 11 5l-14-5c-2-1-3-1-5-1h-1l-13-1c2-1 7-1 10-1v-1c2 0 3 0 5-1l-2-1v-1l5 1 1-2z" class="F"></path><path d="M406 386h0c1-3 4-4 6-5h5 0l-2 1-1 3 1 1h-1l1 1c-3 2-5 4-8 6h-1v-3c1-1 1-1 1-2v-1l-1-1z" class="T"></path><path d="M406 386h0c1-3 4-4 6-5h5 0l-2 1-1 3h-1l-1-2h0c-3 1-4 2-6 3z" class="m"></path><path d="M662 468l1 1c1-1 2-1 3-1-1 0-1 1-2 2l-1 1c-3 2-5 4-7 5-2 2-5 2-7 3-6 3-14 7-20 7h-2c14-3 24-9 35-18z" class="M"></path><path d="M417 357l1-1c-1-1-1-1-1-2v-6c3-2 5-3 8-4-1 1-1 3-2 4 0 3 0 6-2 9h0v2l-2 1-2-3z" class="i"></path><path d="M413 367l-8 7c0-2 0-5 1-7v-1-1c1-3 6-5 8-6l1 3c-1 1-2 1-2 3-1 1 0 2 0 2z" class="m"></path><path d="M349 887l3 1c3 0 5 1 7 0h3 0c2 1 3 1 4 0h1 1c1 1 0 2 0 3l-2 1h-19c1-1 1-2 2-3h1l-1-1v-1z" class="p"></path><path d="M542 178c-1-2-2-3-4-5 2 1 7 7 8 9l1 1 3 6c3 6 5 12 7 18 1 5 4 11 3 16-1 1-2 3-2 4-2-3-1-9-1-13-1-13-7-26-15-36z" class="Y"></path><path d="M565 439c3 0 5 0 8 1h5c2 0 3 1 4 2 2 0 4 0 5 1v1c0 1 0 1 1 2h-1 0l-4-1h0c-4-2-10-2-14-2-2-1-3-1-4-1h-1v-1l1-2z" class="Z"></path><path d="M557 428c1 2 1 10 1 12l2-1c0 2 0 3-1 5h-1v8c0 1 0 1 1 1 0 2 0 9 1 11h-2c-1 2-1 6 0 9l1 1c0 3 0 7-1 10v1l-1-1v-41-3-12zm63 118h-1c-1 0-3 1-4 0s-2-1-4-1v1l-1-1v-1-2c0-1 0-2 1-3 0-1-1-2-1-3v-1-1-1l1-2h1c1-1 0-1 1 0l-1 11h6l8 1h4v1l-4 1h-6c-2-1-3-1-5-1 2 1 3 1 5 2z" class="H"></path><path d="M618 542l8 1h4v1l-4 1h-6c-2-1-3-1-5-1-2 1-4 0-6-1l9-1z" class="C"></path><path d="M416 398h0c2 1 4 0 5 1v1c-1 1-2 1-4 2h1l-3 3h-1-2v1h2l-1 1c-1 0-2 1-3 3 0 1-3 3-4 4l2-9-2 2v-1c1-3 7-6 10-8z" class="m"></path><path d="M722 225l1-1v-1l-4-3v-1c2 1 4 3 6 4h0 1c1-1 1-2 1-3v-1c0 1 1 1 1 2l1 1 1 1h0l1 1 1 1c2 0 3 0 4 1l2 1h0v1c1 1 1 1 2 3l-4-1h0v1c0 1 0 2-1 3h-1c1-1 1-2 1-4-4-1-8-2-11-4h1c-1-1-2-1-3-1z" class="b"></path><path d="M727 219c0 1 1 1 1 2l1 1 1 1v2c-2 0-3-1-4-2 1-1 1-2 1-3v-1z" class="j"></path><path d="M569 454l1-2h1l1 2h2v1s0 1 1 2h4 1c1 0 2 0 2-1l2 2c-2 0-4-1-6 0h3c0 1 1 1 1 1h2 0v-2l1-1 1 1h0c0-1 0 0-1-1 1-1 1-1 3-1v6 1h-1v1l-6-1c-3-1-6-1-8-2h0c-1-2-1-4-2-5 0-1-1-1-2-1z" class="L"></path><path d="M573 460l2-2 5 1c1 0 1 0 2 1 0 0 1 0 2 1l2 1h0 1v1l-6-1c-3-1-6-1-8-2z" class="G"></path><path d="M422 574l1 1c1 2 0 4 1 7v13h1c1 0 2 0 3 1l2 2 1-1v3l-2 2h0c-1-1-5-1-7-1-2-8 0-18 1-25l-1-2z" class="q"></path><path d="M465 630h0l1 57c-1 4 0 8 0 13-1-3 0-6-1-8-1-3-1-5-1-7 0-5-1-10 0-15 0-1 0-3 1-4v-10l-1-1h1v-16l-1-1v-3l1-2c0-1-1-1 0-3z" class="S"></path><path d="M507 94h0l1 2v2h4v1l-1 4 1 7v2s1 0 1 1h-1v1c-1 1 0 1-2 2v-1l-1-1h-1c-1-1-2 0-3-1 0-2 1-3 2-4h1v-4l-1 2c-1-2-1-2 0-4h1v-4l-1-1c-1 0 0-3 0-4z" class="r"></path><path d="M509 114c0-2-1-3 1-5l2 1v2s1 0 1 1h-1v1c-1 1 0 1-2 2v-1l-1-1z" class="p"></path><path d="M588 461l2 1c2 0 6-1 8 0h-1l-1-1h1c-1-1-2-1-3-1s-2-1-3-1v1l-1 1c-1-1-1-2-1-4l1-1v-2c0 2 0 2 2 3 1 1 3 2 4 2s1 1 3 1l1 1v-1c1 0 1 0 2 1 2 2 3 2 3 5l-1 1c-2-1-2-2-3-3 0-1 0-1 1-2h-1c-1 2-1 2-1 3-2 0-3 0-5 1h-1c-2-1-5-1-6-2v-2-1z" class="H"></path><path d="M519 169v-5c-1-1-1-2-1-3v-3-1l1-1c-1-1 0-1-1-1v-1-1c2-1 3-2 5-2 1 0 1 1 2 2l-4 3v1c2 2 2-1 4 0l1 1-2 2-1-1c-1 0 0 0-1-1-1 0-2 0-2-1l-1 1c3 1 5 3 7 5h-1l4 4v1c-2-1-3-3-4-4l-1 1v1 1l3 3-1 1-6-6v2l-1 1 1 1h-1z" class="D"></path><path d="M624 853l17 12-1 1c-1 2-2 3-1 6l-1-1c0-1 0-2-1-3s-2-1-3-1h-1c0-2 0-2-1-3h0l-1 1s-1 0-1-1h-1c-1 0-1-1-2-2h0c0-1-1-1-2-2h-2l1-1v-3h1l-1-1v-2z" class="X"></path><path d="M627 862c1-1 1-1 2-1l1-1 1 1c0 1 1 2 0 4 0 0-1 0-1-1h-1c-1 0-1-1-2-2z" class="r"></path><path d="M561 791l3-1v-2l1-1 1 2v2h0c-1 1 0 1-1 2v1 4c-1 4-1 7-1 11v2l1 3h2-5l-1-1v-1-13-8h0z" class="G"></path><path d="M563 803c0 2 0 6 1 8l1 3h2-5l-1-1c3-3 2-7 2-10z" class="C"></path><path d="M561 791l3-1v-2l1-1 1 2v2h0c-1 1 0 1-1 2v1 4c-1 4-1 7-1 11v2c-1-2-1-6-1-8v-10l-2-2z" class="O"></path><path d="M790 193l8 4c1 1 2 1 3 2l1-1c0-1 0-1 1-2l2 29h0l-1 2v-1c-1 0-1 0-1 1h0c-1 0-1-1-1-1 0-1 1-1 1-2h1c-1-1-1-2-1-2-1-2-1-4-1-6v-4-2c0-2 0-6-1-7-2-2-3-3-5-4-2-2-5-3-6-6z" class="X"></path><path d="M424 634h0v2 1l2-1v1l2-1v1l-1 2c-2 0-3-1-4-2-2-2-1-18 0-22-1 0-1-1-2-1l2-1c0-2 0-6-1-8v-1c1-1 2-2 4-2l-2 32z" class="T"></path><path d="M569 721c0-3-2-10 0-12 4-6 10-11 14-17v-1l1 1h-1c-1 1 0 1 0 2-1 1-1 0-1 2-1 2-3 4-4 6l-1 1h-1c0 2-1 3-2 4v2c0 2-1 3-2 4h0l-1 14v1h-1v-2h-1v-5z" class="N"></path><path d="M572 713l-2-2c2-3 3-6 6-8 0 2-1 3-2 4v2c0 2-1 3-2 4h0zm19-146c1-1 2-1 3-2 0 9-1 18 0 27v1l-1 1h-1v-2h1l-1-1-1 1v1 6l1 1c1 0 1-1 2-1 0-1-1-1-1-2h1c1 0 1 1 2 1l-1 2c1 1 1 1 1 3l-1-1-1-1h-1c-1 1-1 0-2 0-2-3-1-7 0-10v-2-11-11z" class="B"></path><path d="M527 170l-3-3v-1-1l1-1c1 1 2 3 4 4v-1l-4-4h1l16 16c2 3 5 6 6 10h0l-4-3c0-3-1-5-4-7h0l1 2v1l-3-3c-1-1-1 0-2 0l-3-3s-1 0-2-1c-1-2-2-3-4-5z" class="K"></path><path d="M581 525v1l1 1c2-1 0-3 2-3 1 2 1 4 1 6 0 5 0 11 1 16v1l2 1 1-1h0v-2c0 2 1 3 0 4h-3c-2 1-3 1-4 0h-2c-2 0-5 0-7-1h0c1-1 6 0 8 0v-22-1z" class="L"></path><path d="M574 770v-1h2c3 2 8 5 11 5h4c0 1 1 1 2 2v4c0 2-1 3-2 4-2 2-3 4-5 6h-2v-1c-2 0-2 2-3 3v1l-2-1v1l2 2v1l-1-1h-2-1-1l1-3 1-2 3-3c2 0 5-2 7-4s2-2 2-4c-2-2-3-3-5-4-3-2-7-3-11-5z" class="D"></path><path d="M577 792c5-5 10-6 15-12h0 1c0 2-1 3-2 4-2 2-3 4-5 6h-2v-1c-2 0-2 2-3 3v1l-2-1v1l2 2v1l-1-1h-2-1-1l1-3z" class="W"></path><path d="M542 178c8 10 14 23 15 36 0 4-1 10 1 13-1 2-2 3-4 4-1-3 0-8 0-12s-1-8-1-12c0-7-1-12-5-18-1-4-4-7-6-10v-1z" class="H"></path><path d="M461 825l1 1c0 2 1 2-1 4 0 1 0 2 1 3s2 0 3 0l1-2-1 1-1-1c-1-1-1-1 0-2v-7c-1-4 0-8 0-11h0c1 1 1 3 1 5-1 1-1 3-1 5 1 1 1 5 1 7h0c1-2 0-4 0-5l1-1-1-13c-1-1 0-2 0-3v-3c1 5 1 10 1 15v20c-1-1-3-4-4-4l-1 1 1 1-2 1v-1c0-1 0-1 1-2h0v-1c0-1 0-1-1-2l-2 2c-1-1 0-2-1-4-3 1-6 1-9 1v-3h4 7 1 1l-2-2c-2-1-4-2-5-4 2 0 3 3 5 4h2z" class="S"></path><path d="M593 643v-1c1-1 2-1 3-1l1 1v-1c2 2 0 13 2 14h1 0l-1 1-1-1c-1 1 0 2 0 3v12 2c-2 1-3 1-4 1v-1l1 1c2-1 2-2 3-3l-1-1-2 2c-1-4-1-9-1-14 0-4 1-10-1-14z" class="h"></path><path d="M438 394l6-1c-2 1-3 2-4 4l-1 2-9 2c-8 2-14 4-20 9 1-2 2-3 3-3l1-1h-2v-1h2 1l3-3h-1c2-1 3-1 4-2v-1c4 0 8-3 12-4l5-1z" class="U"></path><path d="M438 394l6-1c-2 1-3 2-4 4l-1 2-9 2 2-3h-1v-1c3 0 5-2 7-3z" class="m"></path><path d="M551 161c9 1 18 1 27 0 4 0 6 0 10-1-2 2-4 4-7 5-2 0-3 0-5 1l-2-1v1c-1 0-2 1-3 1h-1l-1 1c0 1 0 2-1 3h-3c-1 0-1 0-2 1h-2 0-3v1l-1-1c-1-1-1 0-2-1-1 0-1-1-2-1v-1c1 1 1 1 2 1v-1c-1 0-1 0-2-1h0l-2 1v-1h-4c1-1 3-1 4-1v-1h2 1 3c1 0 2 0 2 1 1 1 1 2 1 3v1h0l1-1c1-2 0-3 1-5h5 0c-1 0-2 0-3 1l1 1h-1v4l1-1h2l2-1c-1-1-1-2 0-3s1 0 3 0c1-1 1-1 2-1h0c2-1 4-1 6-2h1 0c1 0 2 0 2-1h0-1-1-1-4-3c-2 1-6 1-8 0-4 0-9 1-12 0h-4l2-1z" class="L"></path><path d="M593 355l2 1c0 1 0 1-1 1 0 1 1 1 2 2l1 1c1 2 4 3 6 3 2 1 3 2 5 3 3 2 7 5 8 8h-1c-2-1-4-3-5-5h-1c-7-9-33-11-44-12h-1c0-1 0 0 1-1h4 9c3 1 7 1 10 1l1-1c1 0 3 0 4-1z" class="b"></path><path d="M705 213l1 1c1 2 2 3 3 3l1 2 1 1 1-1c0 1 1 2 2 2l2 1 6 3c1 0 2 0 3 1h-1l-11-3h0l-5 10c-1-2 3-7 3-10l-24-3 2-2c1 1 1 1 2 1l1-1c1 1 1 0 2 1h4l3-1v-1l2-2v-1l2-1z" class="M"></path><path d="M705 213l1 1c1 2 2 3 3 3l1 2h-2c-1-1-1-1-1-2h0-1c-1 0-1 1-2 1h-1l1-2-1-1v-1l2-1z" class="D"></path><path d="M604 520l5 1c5 1 10 1 15 2h6c1 0 3 1 5 1l2 1h-4c-5-1-10-1-14 0h-1c-1 0-1 1-2 1l-1 1h0-2-2c0 1-1 1-2 1h-1l-2 2v1c-1-1-1-2-2-3h0l-2-1 1-1c1-1 1-5 1-6z" class="d"></path><path d="M465 796l1-1c1 1 0 5 1 5 0 10-1 20 0 29h1v-7-6l1 1c1 5-1 11 1 17v1c-1 0-1 0-2 1v-1l-1-1c-1 2 0 3-1 4s-1 1-1 2v1h-1l-1 1h3 0c0 1-1 2-1 2h-2v1h1l-1 2h-1-1c-1 0-1 0-1-1h-1c-1 1 0 1-1 1v-2h1c-1-1-1-1-1-2l2-2h1c0-1 1-1 2-2 0-1-1-2-1-3l-1-1 1-1c1 0 3 3 4 4v-20c0-5 0-10-1-15v-7z" class="C"></path><path d="M558 382l1 1 1-1c2 0 5 0 7 1v-1c3-1 5 0 8 0h1c3 0 5 1 7 1l-3 1c-1 0-1 1-1 1-1 1-1 2-2 3v2l-2-1c-3 0-5-1-8-2 0-1 0-1-1-2v1 1h-4c-1-1-1-1-2-1-1 1-1 3-1 4l-1 2v3-13z" class="B"></path><path d="M574 384h1c1 1 1 3 2 4h-3v-4z" class="E"></path><path d="M427 750v-1-5-14c1 2 1 5 1 7v13l2 2v18 4l-1 1h0v1h1c-1 2-1 1-1 3h0c-1 2-2 6-4 6l1-4c0-1 0-1-1-1v-1c3-4 2-22 1-28l-2-1h2 1z" class="J"></path><path d="M428 750l2 2v18l-1 1c0 1 0 1-1 1-1-7 0-15 0-22z" class="Q"></path><path d="M455 401l3-1c0 2 1 3 0 4 1 1 1 2 2 3h-1-4-2c-2 1-5-1-6 1-2 0-3-1-4 0-1 0-3 0-4 1h-3c-2 0-2 0-3 1h-3c-1 0-2 0-3 1h-2l-2 1c-2 1-3 2-5 3v-3h-1c0-1 0-1 1-2 2 1 9-2 12-3 4-1 9-1 14-2 3 0 6-1 8 0l1 1 1-5h1z" class="b"></path><path d="M455 401l3-1c0 2 1 3 0 4 1 1 1 2 2 3h-1-4c1-2 1-2 2-3 0-1-1-2-2-3z" class="Z"></path><path d="M630 549h1c2 0 3 0 5 1l14 5c2 1 5 4 7 4h3c1 0 3-1 4-1 0-2 0-3-2-5l3-1 4 4c4 4 7 8 9 13h0l-2-2c-1 0-1 0-2-1h0c-2-1-1-2-2-4h-1v1h-2c-1-1-3-2-4-2h0c0 3 1 4 3 5 2 2 3 4 4 5v1l-4-5c-3-2-7-6-10-6l-1-1-2 3c-1-1 0-3 0-4-7-5-16-8-25-10z" class="Q"></path><path d="M563 708c-1 6-2 13 0 20 1 6 1 14 2 20l-1 12c0 1 1 3 0 4l-3 2h0v-35c0-5-1-12-1-17 1-3 1-4 3-6z" class="G"></path><path d="M436 526l1 1v6h1c0 3 0 7 1 9v1l1 1-1 4v1c-1 0-2-1-3-1l-1 1-1 1h2 2l1 1h-7-1v-11-5h-1-1c0-2-1-4-1-7 1 1 1 0 1 1 1 0 1 1 2 1h1v4c1 2 1 5 1 8h1v-11c1-1 1-3 2-4v-1z" class="d"></path><path d="M436 526l1 1v6h1c0 3 0 7 1 9v1l1 1-1 4v1c-1 0-2-1-3-1l-1 1c0-3-1-6 0-8 1-4 1-9 1-14v-1z" class="c"></path><path d="M451 648h1l1 1c0 3-1 8 1 11v2l1 1c0 1-1 3-2 3l-1 1v3c-1 5 0 12 0 18-1-1 0-1-1-1s-1 0-2-1v-3-1c-1-1-1-2 0-3 0-1 0-1 1 0s0 3 1 5v-7-1c0-2 1-5 0-7l-1 1v2 1c-1 1 0 1-1 3v-1c-1-3 0-5 1-7 1-1 0-1 0-2v-1c0-1-1-2-1-3 1-1 1-1 1-2l-1-2c-1 1-3 1-4 1h-13l-1-1v-2h17l1-1c2-1 0-3 2-5v1-3z" class="B"></path><path d="M452 648l1 1c0 3-1 8 1 11v2l1 1c0 1-1 3-2 3l-1 1v-19z" class="Q"></path><path d="M532 864c2-1 2-2 3-4s6-10 8-11c0 1 0 1-1 2v1c-3 4-6 7-8 12-1 2-3 5-4 8v1h0c2-3 5-10 8-12l1 1-1 1v1c-3 6-7 12-10 18-2 6-3 12-6 17 0 2-1 6-3 7h0c0-2 1-5 2-7s0-5 1-7 1-3 1-5l7-20 2-3z" class="C"></path><path d="M594 565v-1h2c1 0 1 0 2 2 1 4 1 10 1 14 0 6 1 13 0 19h0c0-1-1-1-1-2v-5-10c0-2 1-4 0-6-1 6-1 12-1 18v1c-1 1-3 0-4 0l-1-1h1l1-1v-1c-1-9 0-18 0-27z" class="k"></path><path d="M563 640h3c1 1 3 1 3 2v8 16c-1 1-2 1-3 2l-3-1v-27z" class="P"></path><path d="M417 412h1v3c2-1 3-2 5-3l2-1h2c1-1 2-1 3-1h3c1-1 1-1 3-1h3c1-1 3-1 4-1 1-1 2 0 4 0 1-2 4 0 6-1h2 4c0 2 0 2 1 3h-7l-14 3c-2 0-5 0-6 2h0l-1-1c-1 0-2 0-3 1-4 1-8 2-12 4-1-2 0-5 0-7zm176 190h2l1 1h1l1-1 1 1h0c-1 4-1 7-1 10 1 1 1 1 2 1 0 1-1 1-1 2-1 0 0 2 0 3 0 5 2 15-1 18h-1-1c1-1 1-2 1-2 0-1 0-2-1-3h-1l-1 1c-1-1 0-3-1-5v-10-15-1z" class="M"></path><path d="M593 603c2 4 2 9 2 13 0 2 1 5 0 7v1c-1 2-1 5-1 7l1 1-1 1c-1-1 0-3-1-5v-10-15z" class="k"></path><path d="M597 603l1-1 1 1h0c-1 4-1 7-1 10 1 1 1 1 2 1 0 1-1 1-1 2-1 0 0 2 0 3 0 5 2 15-1 18h-1-1c1-1 1-2 1-2 0-1 0-2-1-3 1 0 1-1 1-1 0-8-1-15-1-22v-4c0-1 0-1 1-1h0v1c0 1 0 5 1 7v-8l-1-1z" class="c"></path><defs><linearGradient id="A" x1="456.445" y1="377.123" x2="439.108" y2="370.585" xlink:href="#B"><stop offset="0" stop-color="#978974"></stop><stop offset="1" stop-color="#d9c2a8"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M449 368c4-2 9-2 14-1l1 1h-1c1 1 1 1 1 2v5c-2 1-4 0-5 1h-1l-22 3 1-1h-1l1-1c3-1 6-4 9-5l12-3c-3 0-7 1-9 0v-1z"></path><path d="M458 376c1-2 2-3 2-5l1-2h0-2c-1 0 0 0-1-1h0 5c1 1 1 1 1 2v5c-2 1-4 0-5 1h-1z" class="M"></path><path d="M549 830l1 1c1 3 0 7-1 11l-1 1h1c2-2 3-6 3-9 1 1 2 1 3 2l1 2c-3 5-7 10-11 14-2 4-5 8-7 12v-1l1-1-1-1c-3 2-6 9-8 12h0v-1c1-3 3-6 4-8 2-5 5-8 8-12v-1c1-1 1-1 1-2-2 1-7 9-8 11s-1 3-3 4l1-3c4-6 8-12 12-17 1-1 2-2 2-3 2-2 2-3 2-5v-6z" class="Y"></path><path d="M562 610c1-3 0-5 2-8 2 0 2 0 4 1 0 1 1 2 1 4 1 1 0 3 0 4s0 1 1 2c-1 0-1 1-2 1 0 3-1 8 1 10v10h-2-4c0-6-1-13 0-19v-1c-1-1-1-1-1-2v-2z" class="b"></path><path d="M567 627v-2c-1-1-1-2-1-3 0-2-1-6 0-8 2-2 1-5 1-8h0l1-1v2h1c1 1 0 3 0 4s0 1 1 2c-1 0-1 1-2 1 0 3-1 8 1 10v10h-2c1 0 1-1 1-2 0-2 1-4-1-5z" class="P"></path><path d="M563 615c4 5 2 12 3 18v-1c1-1 1-3 1-5 2 1 1 3 1 5 0 1 0 2-1 2h-4c0-6-1-13 0-19z" class="k"></path><path d="M586 217c4 1 11 2 15 1h6c0 2 0 4-1 5h0c0-2 0-3-1-4-4-1-10 0-14-1h-2l-1 2c0 1 0 1 1 2h1 1 0 1l1 1-2 1v3l1 1v4h1v-6l1-1 1 1c-1 1-1 5-1 6l1 1v-1c0-2 0-4 1-5 1 1 1 10 1 12-1 2-2 3-3 4h-1c1-1 0-1 0-2v1l-1-2v2h0c-1 1-2 2-3 2-1-1-1-1 0-2v-3c-1-1-1-2-1-3l1-2c-1-1-1-1-2-1h0c0-1 0-1 1-1h0c0-1 0-2 1-3v-2c-3 0-4 5-7 5h-3-1l1-1h4c1-1 2-3 3-4l2-3v-1l-2 2h0v-8z" class="I"></path><defs><linearGradient id="C" x1="566.179" y1="465.165" x2="574.114" y2="459.544" xlink:href="#B"><stop offset="0" stop-color="#515253"></stop><stop offset="1" stop-color="#696969"></stop></linearGradient></defs><path fill="url(#C)" d="M559 453c2 0 5 0 5 1h5c1 0 2 0 2 1 1 1 1 3 2 5h0c2 1 5 1 8 2 0 1 0 1-1 2l1 1h-3v1 1l-14-2-4-1c-1-2-1-9-1-11z"></path><path d="M573 460h-1c-2-1-3-1-4-2l1-2-3-1h5c1 1 1 3 2 5z" class="M"></path><path d="M559 453c2 0 5 0 5 1h0v1c1 2 0 7 0 10l-4-1c-1-2-1-9-1-11z" class="F"></path><path d="M511 919v-1h-1v3c-1 0-1 0-2-1v-7c0-6 0-12-2-18h0c1-1 2-1 3-1l1 1v3h1v-2c1-1 2-2 3-2h0c1 8 0 17-1 25l1 2-1 1v1h2v2c-2 2-5 1-7 1l-1-1c0-1 1-2 1-3h2c1-1 1 0 1-2v-1z" class="b"></path><path d="M511 919v-16h1l1 9v6 1l1 2-1 1v1h2v2c-2 2-5 1-7 1l-1-1c0-1 1-2 1-3h2c1-1 1 0 1-2v-1z" class="Q"></path><path d="M583 383c5 2 18 3 21 8v1c1 2 1 4 0 6-2-1-5-3-7-4-4-1-7-2-11-3l-9-1v-2c1-1 1-2 2-3 0 0 0-1 1-1l3-1z" class="C"></path><path d="M577 388c1-1 1-2 2-3 0 2 0 3 2 4h0c2 0 4 1 5 2l-9-1v-2z" class="H"></path><path d="M583 383c5 2 18 3 21 8v1h-1c-3-1-5-3-8-3h0l-2-2v1c-5-1-9-3-14-3 0 0 0-1 1-1l3-1z" class="N"></path><path d="M567 218h2c1-1 0-1 0-2l1-1c1 1 1 2 1 3l-3 2c0 2 2 2 2 4l2 3c-1 2-3 3-4 4l-8 6c-3 2-6 5-10 8l-2-1c0-3 0-4 2-6-1-1-2-3-3-5 2 0 3 1 4 2l1-1 2-2h2l6-4h1c1-2 4-3 5-4l-2-2c0-2 1-3 1-4z" class="E"></path><path d="M554 232h2l-6 6c-1-1-2-3-3-5 2 0 3 1 4 2l1-1 2-2z" class="k"></path><path d="M587 696l1-1v15 11c-1 4-5 6-9 8-3 1-5 2-8 3l1 34h1v3c-1-1-2-1-3-2v1h-1c1-4 1-7 1-10l-1-19v-8c0-1 1-3 1-5v2h1c3 0 6-3 8-4s4-2 5-4l2-1c1-3 1-7 1-10v-9-4z" class="Y"></path><path d="M592 812c2 1 3 1 4 3s1 3 2 5v1 2 1h-1-3-1 0-2c-1 0-3 1-4 1l-2 1-2-1-1 2h-2l-1-2c-1 0-1 1-2 2v1s-1 1-2 1h-2c-2 2-3 1-5 2l2-2c1 0 1-1 1-2 1 0 2 0 2-1h1c1 1 1 1 2 1v-2-1h1c0-1 1-2 1-4h0 1 1c0 1 1 1 2 1 0-1 1-1 2-2h4l1-1h-2-3l-1-1h0l1-1c1 0 2-1 2-2h1c2 0 3-1 5-2z" class="d"></path><path d="M592 812c2 1 3 1 4 3s1 3 2 5v1c-2-1-4-3-5-4-2-1-6 0-9 0h-1l1-1c1 0 2-1 2-2h1c2 0 3-1 5-2z" class="D"></path><path d="M577 824c0-1 1-2 2-2h2c1 1 1 2 3 2v-1l-2-1c2-2 3-2 5-2 1 0 2 1 3 1l1 2v-2l2 2v1h0-2c-1 0-3 1-4 1l-2 1-2-1-1 2h-2l-1-2c-1 0-1 1-2 2v1s-1 1-2 1h-2c-2 2-3 1-5 2l2-2c1 0 1-1 1-2 1 0 2 0 2-1h1c1 1 1 1 2 1v-2-1h1z" class="G"></path><path d="M342 878c3-1 9-2 11-4v-2l2 2 2 2c1 1 3 2 4 4l1 1s1 1 1 2l1 2 2 2h-1c-1 1-2 0-3 1h-3c-2 1-4 0-7 0l-3-1c-1-1-1-2-1-4h0c0-1-1-2-2-3 0-1-3-1-4-2h0z" class="i"></path><path d="M567 578l1 1h1c-1 3-1 6-1 9l1 1c2 0 9-1 11 0 1 0 2 1 4 0h4l-1-1h2c-2-2-6 0-8-2v-7l1 2c1 0 0-1 1-1v-1l2-1 1 1c0 2-1 3-1 5-1 0-1 0-1 1l1 1h1c1 1 2 1 4 1v1h1l-1-1v-3l-1 1v-1l1-1c0-1-1-3 0-4l1-1v11 2c-2-1-3 0-5-1h-6-12v11h2c-1 1-1 2-2 2-2-1-2-1-4-1-2 3-1 5-2 8v-4h-1c1-1 1-2 1-3 1-1 1-1 1-2s0-2 2-3l1 1-1-2c-1 0 0 0-1-1 0-1 0-2-1-3h-1c1-2 1-4 1-6h0c0-2 1-4 0-6v-1c1-1 2-1 2-1 1-1 1-1 2-1z" class="H"></path><path d="M583 579l2-1 1 1c0 2-1 3-1 5-1-2-2-3-2-4v-1z" class="K"></path><path d="M568 579h1c-1 3-1 6-1 9-1-1-2-2-3-2h-1v-5c1-1 3-2 4-2z" class="J"></path><path d="M426 679c2 0 5 0 6 1h1c1 3 0 6 1 9v1h1v1 2c0 1 1 2 2 3s1 2 1 3v1 4 1 5 4c0 3 1 6 1 9 0-1-1-2-2-2l-2-2c-2 1-2 1-3 3v-1c-1-3 0-8 0-11v-22-7h-3-2l-1-2z" class="C"></path><path d="M430 640h0c0 4 1 9 0 13 0 1 0 1-1 2l-1-1c-1 4 2 13-1 17v1h0c1 0 2-1 2-1v-3c0-3-1-10 1-12v5 9c-1 2-2 3-4 4-1 0-2-1-3-1-2-4 0-14 0-18l-1-1c2-2 1-9 1-12l5-1c1 1 0 5 1 7 1-1-1-6 1-8z" class="f"></path><path d="M594 343l2 1 3 1 5 2 1 10c2 1 4 2 5 3l-2 2c1 2 0 2 0 4-2-1-3-2-5-3-2 0-5-1-6-3l-1-1c-1-1-2-1-2-2 1 0 1 0 1-1l-2-1h-1c0-1 1-1 1-1v-2c0-3 0-6 1-9z" class="Y"></path><path d="M599 346c1 0 2 1 3 1 1 1 1 3 1 4h-1l1-1c-2 0-2 0-3-1s-1-1-1-3z" class="F"></path><path d="M605 357c2 1 4 2 5 3l-2 2h-1c-2-1-2-2-3-3l1-2z" class="E"></path><path d="M594 343l2 1 3 1c-1 0-2 1-2 2s0 2-1 4v4c0 1 1 2 1 2 2 2 5 4 6 6-2 0-5-1-6-3l-1-1c-1-1-2-1-2-2 1 0 1 0 1-1l-2-1h-1c0-1 1-1 1-1v-2c0-3 0-6 1-9z" class="K"></path><path d="M596 344l3 1c-1 0-2 1-2 2s0 2-1 4v4c0 1 1 2 1 2l-2-1c0-1 1-1 1-2-1-3-1-8 0-10z" class="D"></path><path d="M559 841c0-1 0-3 1-4 0-2 2-2 4-3l2 3c3 2 5 7 9 7-1 0-2 1-2 2l-2 3s-1 1-1 2 1 1 0 2h-1-5v-1c-1-1-6 0-8 0 0-5 1-8 3-11z" class="C"></path><path d="M563 842c2-1 4 1 6 3v-1-1l1-1c1 1 2 2 2 3v1h-1v1c-2 0-6-4-8-5z" class="Y"></path><path d="M559 841c0-1 0-3 1-4 0-2 2-2 4-3l2 3c-2 0-3 0-5 1l-1 1c1 1 1 2 2 2v1h-1c-1-1-1-1-2-1z" class="E"></path><path d="M553 789c1 1 1 2 1 3 0 2 0 1 1 2 0-2 0-6 1-8v-1 53l-1-2c-1-1-2-1-3-2 0 3-1 7-3 9h-1l1-1c1-4 2-8 1-11l-1-1 1-1h0c2-2 2-20 1-23v-5c1-1 2-2 2-4v-5h0v-3z" class="C"></path><path d="M553 797l-1 37c0 3-1 7-3 9h-1l1-1c1-4 2-8 1-11l-1-1 1-1h0c2-2 2-20 1-23v-5c1-1 2-2 2-4z" class="L"></path><path d="M476 872l2 1c2 3 0 7 2 10 1 2 4 4 5 5l3 2h0c-3 2-7 1-9 1-7 0-14 1-20 0v-1h-4v-1h1c1 0 2 0 3-1l1-1v1h8c3-3 4-10 6-14l-1-1c1 0 2-1 2-1h1z" class="N"></path><path d="M569 788l1-1v1l4 21c0 6-1 10-5 15 1-1 1-3 2-4 0-2 0-3-1-4 0 2-1 3-2 5v-1-4-1l-1-1h-2l-1-3v-2c0-4 0-7 1-11v-4-1c1-1 0-1 1-2h0v-2h1c1 0 2-1 2-1z" class="E"></path><path d="M569 788l1 1-1 1c0 4 1 7 1 10 0-1-1-1-2-2v4h0-2c-1-1-1-2-1-4v-4-1c1-1 0-1 1-2h0v-2h1c1 0 2-1 2-1z" class="K"></path><path d="M565 798c0 2 0 3 1 4h2 0v-4c1 1 2 1 2 2 1 4 0 6 0 10v5 1c0 2-1 3-2 5v-1-4-1l-1-1h-2l-1-3v-2c0-4 0-7 1-11z" class="Y"></path><path d="M570 815c-1-1-1-5-1-7h-1v4h-3v-3c1-1 1-1 1-2s0-1 1-2c2 0 1 0 3 1v4 5z" class="D"></path><defs><linearGradient id="D" x1="420.284" y1="826.886" x2="404.285" y2="826.141" xlink:href="#B"><stop offset="0" stop-color="#96928c"></stop><stop offset="1" stop-color="#bebcb7"></stop></linearGradient></defs><path fill="url(#D)" d="M422 801l1 1-1 1v2l1 1v1h1 1l1 1h0c-1 1-1 2-1 3-1 1-1 1-1 2v1c0 1-1 1-1 2-1 1-1 1-1 2v1l-1 1c1 1 1 2 1 3s-1 2-1 2l-1-1-1 1-1 1h1l1 2-1 1c-1 0-1 0-1-1l-1 2v1c-1 1-2 2-3 2-1 1-1 1-1 2v1h0l-1 1c-2 2-3 3-4 5h-1c-1 1-1 1-1 2l-2 1c0-1-1-2-1-3 9-12 14-26 19-41z"></path><path d="M615 842l2 3 3 3c1 2 2 4 4 5v2l1 1h-1v3l-1 1h2v1h-3v-2l-1 1c-1 1-1 1-3 2l1 4h-1v-1l-1-1c-3 0-2 1-5 2h0-2s1-2 0-3c0-1-1-1-1-3v-4l1-3h0l1-1-1-2 1-1c0-2 0-3 1-4h1c1-1 1-2 2-3z" class="o"></path><path d="M611 859c0-1-1-2 0-3 1-3 2-4 4-5l2 1v4l1-1 1 1c-1 1-1 1-2 1l-1 1c-1 1 0 1-1 0h-1l-1 1h-2z" class="i"></path><path d="M611 852h0c2-2 3-2 6-2v2h0l-2-1c-2 1-3 2-4 5-1 1 0 2 0 3 1 2 2 2 3 3l2 1c1 0 1 0 2-1h0l1 4h-1v-1l-1-1c-3 0-2 1-5 2h0-2s1-2 0-3c0-1-1-1-1-3v-4l1-3h0l1-1z" class="L"></path><path d="M615 842l2 3 3 3c1 2 2 4 4 5v2l1 1h-1v3l-1 1-1-2v-2l-1-1c-1-1-1-1-2-1-1-1-1-2-2-2v-2c-3 0-4 0-6 2h0l-1-2 1-1c0-2 0-3 1-4h1c1-1 1-2 2-3z" class="i"></path><path d="M615 842l2 3 3 3h-1-3-1-1c-1 0-1 1-2 2h0l-1-1c0-2 0-3 1-4h1c1-1 1-2 2-3z" class="X"></path><path d="M615 842l2 3-2 2h-1l-1-2c1-1 1-2 2-3z" class="p"></path><path d="M454 788l1 1h1l1 1c1 1 1 8 1 10-1 4 0 9 0 13-2 1-2 1-3 2 0 1 0 1-1 1 1 4 2 6 6 8 0 0 1 0 1 1h-2c-2-1-3-4-5-4 1 2 3 3 5 4l2 2h-1-1l-3-1c-3-1-4-3-5-6l-2-3v-2-6l2-3c0-2 1-4 0-7 0-2 0-5-1-8v-1h2l1-2h1z" class="c"></path><path d="M449 815l2 1v4l-2-3v-2z" class="F"></path><path d="M453 788h1c1 2 1 6 0 8 0 3 0 10-1 12v7h-1c-1-1 0-5-1-6v-3c0-2 1-4 0-7 0-2 0-5-1-8v-1h2l1-2z" class="D"></path><path d="M455 789h1l1 1c1 1 1 8 1 10-1 4 0 9 0 13-2 1-2 1-3 2 0 1 0 1-1 1v2-1c1-5 0-12 1-18v-10z" class="M"></path><path d="M455 789h1l1 1c0 1 0 1-1 2l1 1v1 1 2 1c-1 1-1 2-1 4h-1v-3-10z" class="J"></path><path d="M467 800c1 1 1 2 1 3l1 1h1c0 1 0 0 1 1 0 1 0 1-1 2l-1-1v-1l-1 1v2l1 1 1-1c1 0 1 0 2-1h1l2-6c1 2 3 3 3 6v2c-1 5 0 11-3 16-1 3-3 9-5 11v-1-1c-2-6 0-12-1-17l-1-1v6 7h-1c-1-9 0-19 0-29z" class="B"></path><path d="M472 813l1 3-1 1h-1c0-1 0-2-1-2l2-2z" class="N"></path><path d="M470 834c0-1 1-2 1-2l2-6v2 1l1-1v-1-1-1h1c-1 3-3 9-5 11v-1-1z" class="L"></path><path d="M468 808l1 1 1-1c1 0 1 0 2-1 0 3-1 4 0 6l-2 2-1-1c-1-1-1-4-1-6z" class="K"></path><path d="M427 685v-1l3 1c1 1 1 3 1 5l-1 42v12 5 3l-2-2v-13c0-2 0-5-1-7v14 5 1-60l-1-2 1-3z" class="M"></path><path d="M427 685h1c1 3 0 7 0 10 0 2 0 4-1 6v1-12l-1-2 1-3z" class="h"></path><path d="M447 338l2-1c0 1 1 1 1 2v2c0 3 0 6 1 9h1c1 1 2 1 3 1l4 5v1h-3l-6 1-10 1c2-2 6-3 9-4-3-1-5 0-8-1h0c-2-3-1-7-2-11 0-2-1-2-2-3l1-1h4-1c1 0 2-1 2-1h4z" class="a"></path><path d="M447 338l2-1c0 1 1 1 1 2v2c0 3 0 6 1 9h1c1 1 2 1 3 1l4 5v1h-3v-3l-1-1c-2-1-4-1-6-2v-3h0c-1 0-1 0-1-1-1-1-1-3 0-5v-4h-1z" class="n"></path><path d="M442 339v1h0c-1 4 1 8 1 11 2 2 3 2 6 2h1 3l1 1-2 2-2 2-10 1c2-2 6-3 9-4-3-1-5 0-8-1h0c-2-3-1-7-2-11 0-2-1-2-2-3l1-1h4z" class="j"></path><path d="M456 603h1c2 1 2 1 2 3 1 3 0 6 1 9l-1 2v12c0 2 0 4-1 6h-3-2l-1 1c1-3 0-7 0-10l-1-1h-6 0v-1-1l1-1h-1c1-1 6 0 7 0l1-8h-2c0-3 0-5 1-7v-2c1-1 2-2 4-2z" class="s"></path><path d="M456 603h1c2 1 2 1 2 3v6 1c-1 0-1 1-1 2h-2v-1-1c0-1 0-2-1-3l-2 2h-1c0-1 0-2 1-2 0-1 1-1 1-2l-2-1v-2c1-1 2-2 4-2z" class="V"></path><path d="M459 606c1 3 0 6 1 9l-1 2v12c0 2 0 4-1 6h-3v-5c1-2 1-4 1-6-1-2-1-7 0-9h2c0-1 0-2 1-2v-1-6z" class="Q"></path><path d="M582 654c1 2 5 0 6 2l1 2h0c-4 0-11 1-14-1 1-1 10 0 12 0-1-2-11 0-13-1v-1h2 4 0l-6-1-1-24c1-1 2 0 3 0h-1c2 2 5 1 8 1-1 6 0 11-1 17v6z" class="E"></path><path d="M577 633c1 1 1 1 2 3l-1 9c0 1 1 3 1 4-1 2-1 3-1 5h-1c-2-7-1-14 0-21z" class="F"></path><path d="M577 633v-1h4c2 4 1 11 1 16v6h-4c0-2 0-3 1-5 0-1-1-3-1-4l1-9c-1-2-1-2-2-3z" class="D"></path><path d="M577 633v-1h4c-2 3-1 4-1 7-1-1-1-2-1-3h0c-1-2-1-2-2-3z" class="K"></path><path d="M573 537c1-1 1-4 1-5 1-3 2-4 3-6v-1c1-2 1-1 3-1l1 1v1 22c-2 0-7-1-8 0l-4-1h-1 0v-5-7l-1-1h-3c1-1 1-2 1-3s-1-1-2-2c2 0 3-1 4 0l2 2v1l2 1c0 2-1 2 0 4l1 1 1-1z" class="G"></path><path d="M572 538l1-1c0 2-1 3-2 5v1c-1-1-1-2-1-3s1-2 2-2z" class="L"></path><path d="M563 529c2 0 3-1 4 0l2 2v1l2 1c0 2-1 2 0 4l1 1c-1 0-2 1-2 2-1-1 0-1 0-2l-1-1v-2 4c0-2 0-3-1-4l-1-1h-3c1-1 1-2 1-3s-1-1-2-2z" class="B"></path><path d="M569 547l1-1c1 1 1 0 2 1h6c0-1-2-1-3-2 2-5 2-10 2-15 1-2 2-3 4-4v22c-2 0-7-1-8 0l-4-1z" class="Z"></path><path d="M412 381l4-3v-1c3-3 5-4 9-5 5-1 9-2 13-3h5c2 1 2 1 4 0 0-1 0-1 1-2l1 1v1c2 1 6 0 9 0l-12 3c-3 1-6 4-9 5l-1 1h1l-1 1-11 3c-3 0-5 0-8-1h0-5z" class="U"></path><path d="M437 377l1-1c1-1 3-4 5-4h3c-3 1-6 4-9 5z" class="m"></path><path d="M587 588h-13c-1 0-3 1-4 0 0-1 1-2 1-2 2 0 2 1 3 0l-1-3c-1-3 0-6 0-10 0-3-2-11 0-14h2c1 0 2 0 3 1h3 0v6 13 7c2 2 6 0 8 2h-2z" class="E"></path><path d="M578 560h3 0c-1 1-2 1-3 2-1 4 0 7 0 10 0 4-1 9 0 13-1-1-1-4-1-6-1-3-1-7-2-11 0-3 0-5 1-8h2z" class="F"></path><path d="M575 568c1 1 1 2 2 3 1 3 1 5 0 8-1-3-1-7-2-11z" class="C"></path><path d="M578 585c-1-4 0-9 0-13 0-3-1-6 0-10 1-1 2-1 3-2v6 13 7h-1c-1 0-1-1-2-1z" class="Y"></path><path d="M580 586v-8-16l1-1v5 13 7h-1z" class="Z"></path><path d="M564 534h3l1 1v7 5h0v5c1 1 2 1 3 1h1 1 0-3l-1-1c1-1 4-1 5-1h14 0v4h-5l-10-1h-5l1 18-2-1h-3v1l-1-1v-5-9l-1-22 1-1h1z" class="P"></path><path d="M567 571c0-5 0-10-1-15 0-1 0 0 1-1h0l1 1v-1-1l1 18-2-1z" class="K"></path><path d="M564 534h3l1 1v7 5c-1-1-1-3-2-3l-1 1h0l-1-1c-1 1 0 5 0 7v19 1 1l-1-1v-5-9l-1-22 1-1h1z" class="b"></path><path d="M564 534h3l1 1v7c-1-1-1-2-1-3h-2c0-1 0-1-1-2 0-1 0-2-1-3h1z" class="J"></path><path d="M421 206c2 3 4 6 4 9v1h0c0 1 0 2 1 3 1 2 3 4 3 6l-1 1h-1v1l1 1c1 1 1 1 1 2 0 5 2 10 3 16v3l1 1h0c-1 0-1 0-2-1h-3l-2-1v1h0c-2-1-3-1-4-2v-8l-7-1h0l7-1-1-14c1 0 2 0 3 1s0 1 1 0h0c0-2-1-3-2-4l-1-1v-4c-1-4-4-4-3-8l1-1 1 1v-1z" class="m"></path><path d="M426 246l-2-1 2-8c1 3 2 7 0 10v-1z" class="X"></path><path d="M426 247c1 0 2 0 3 1v-2c1-4-2-11-3-15 0-2 0-3-1-5-1-1-1 0-1-1 1 0 2 1 3 1v1l1 1c1 1 1 1 1 2 0 5 2 10 3 16v3l1 1h0c-1 0-1 0-2-1h-3l-2-1v-2 1z" class="T"></path><path d="M425 344l12-4c1 1 2 1 2 3 1 4 0 8 2 11v1l1 1c-2 2-12 4-14 3-2 0-3 0-4-1-1 0-1-1-2 0-1 0-1 1-1 1v-2h0c2-3 2-6 2-9 1-1 1-3 2-4z" class="u"></path><path d="M425 344l12-4c1 1 2 1 2 3 1 4 0 8 2 11v1h-2v-1l-1-10-1-1c-2 1-3 1-4 1-2 1-4 2-5 3-2 2-3 7-4 9v2c-1 0-1-1-2 0-1 0-1 1-1 1v-2h0c2-3 2-6 2-9 1-1 1-3 2-4z" class="r"></path><path d="M544 878s0-1 1-2h-1v-5l1-2c0-1 0-1-1-2l1-1h1 1v-1c1 0 1-1 2-2l-1-1h-2l-1-1 2-2c1 0 0-1 1-2s1-1 2-1h0v4c2 0 2 1 4 0 1-1 1-2 1-3l-1-1c0 1-1 1-2 2 0-3 2-3 3-5 0 4 1 8 3 12 0 1 1 2 2 3 2 3 4 6 7 7-1 2-3 1-5 1-3 0-5 1-8 1l-10 1z" class="d"></path><path d="M423 533v-3c2-1 3-2 5-2 0 3 1 5 1 7h1 1v5 11h1c-1 1-2 2-2 3l1 1v1c0 1-1 3-1 4h-1c-1-1-2-1-3-1h0c-2 0-3-2-3-2-1 2-1 2-1 4l1 1c0 1 0 0 1 1v1c-1-1-2-1-3-2-1-2-1-6 0-9v-8c0-3-1-8 0-10 0-1 1-2 2-2z" class="m"></path><path d="M423 533v-3c2-1 3-2 5-2 0 3 1 5 1 7h1 1v5l-1-2h0l-1 1h-1v-1h0l-2 2c-1-3 0-7 0-10h-1v2l-2 1z" class="q"></path><path d="M426 540l2-2h0v1h1l1-1h0l1 2v11h1c-1 1-2 2-2 3l1 1v1c0 1-1 3-1 4h-1c-1-1-2-1-3-1h0c-2 0-3-2-3-2 0-1 1-2 2-2 1-5-3-10 1-14v-1z" class="n"></path><path d="M426 540l2-2h0v1h1l1 3c-1 1-2 1-2 2-1 1 0 9 0 11-3-4 0-10-2-14v-1z" class="T"></path><path d="M423 557c0-1 1-2 2-2 1-5-3-10 1-14 2 4-1 10 2 14l1 3v2c-1-1-2-1-3-1h0c-2 0-3-2-3-2z" class="J"></path><path d="M431 444c1 0 1 1 2 1 1 1 2 1 3 2l-2 1c0 1 1 3 1 3v2 9c-2 1-2 1-3 1-2 1-2 1-4 1s-3 1-4 2h0l-1 1h-4l-2-2 1-2v-1c1 0 2-1 2-2 1-2 0-4 1-6v-1l-1-1h-1v-3c2-2 6-3 9-4h0c1 0 2 0 3-1z" class="H"></path><path d="M435 453v9c-2 1-2 1-3 1-2 1-2 1-4 1 0-1-1-2-1-2-2 0-2 1-4 0l1-1 1-1c1-1 3-1 4-3h1c2 0 3-2 5-4z" class="B"></path><path d="M431 444c1 0 1 1 2 1 1 1 2 1 3 2l-2 1-14 4h-1v-3c2-2 6-3 9-4h0c1 0 2 0 3-1z" class="q"></path><path d="M566 725h0l2 1h1 1c0 2-1 4-1 5v8l1 19c0 3 0 6-1 10h1v-1c1 1 2 1 3 2l1 1v5 1h-12l-1-1h0v-1c-1-1-2-2-2-4s1-2 2-4l3-2c1-1 0-3 0-4l1-12v-6c1-2 1-4 1-6v-9-2z" class="P"></path><path d="M561 774v-1s0-1-1-2l3-3 4 1h3v1l1-1 1 1v3c-1 0-2 0-3 1h3v1c-2 0-4 0-6-1v-1l-1-2h1l-1-1c0 1-1 1-1 2l1 2s-3 1-4 1h0v-1z" class="E"></path><path d="M566 727c2 3 1 6 1 9l1 21v7l-1 1c-1 0-2 0-3-1 1-1 0-3 0-4l1-12v-6c1-2 1-4 1-6v-9z" class="D"></path><path d="M581 793v-1c1-1 1-3 3-3v1h2c2 2 2 4 3 7s2 6 4 9c0 2 1 3 2 4v2l-1-1-2 1c-2 1-3 2-5 2h-1c0 1-1 2-2 2l-1 1h0l-1-1c0-1-1-1-2-1v-2c-1-4-2-9-3-13 0-2-1-4-1-5h1 1 2l1 1v-1l-2-2v-1l2 1z" class="F"></path><path d="M580 813c2-1 2-2 4-2v1l-1 3 1 1 1-1 1-1c0 1-1 2-2 2l-1 1h0l-1-1c0-1-1-1-2-1v-2z" class="E"></path><path d="M589 806h0c1 2 1 3 1 4 1 1 2 1 4 1h0l-2 1c-2 1-3 2-5 2h-1l-1 1c0-2 1-3 1-4 1-2 2-1 3-1v-4z" class="S"></path><path d="M581 793v-1c1-1 1-3 3-3v1h2c2 2 2 4 3 7s2 6 4 9c0 2 1 3 2 4v2l-1-1h0c-2 0-3 0-4-1 0-1 0-2-1-4h0c-1-1-1-1-1-2v-1l-1 1-1-1c1 0 1 0 2-1l-1-1c-1-1-2-1-4-1h0 0l1-1h2c0-1-1-2-1-2-2 1-3 1-4 2h0c1-2 2-3 3-4h1v-1c-1-1-2-1-3-1v1h0l-1-1z" class="R"></path><defs><linearGradient id="E" x1="422.165" y1="209.435" x2="415.774" y2="215.608" xlink:href="#B"><stop offset="0" stop-color="#d7bba1"></stop><stop offset="1" stop-color="#e6e3d3"></stop></linearGradient></defs><path fill="url(#E)" d="M376 163s1 1 2 1 1 0 2 1c3 2 7 3 10 6 0 1 1 1 1 2l3 4 7 7h1v1l3 3 1 1c0 1 1 3 2 4 2 3 3 7 5 9 1-1 1-1 0-2l1-1c-1-2-2-2-2-5 0 0 1 0 1-1v-1c1 1 1 1 1 2 1 0 3 0 4-1v2c0 1 0 1-1 2 0 1 1 2 1 3l3 6v1l-1-1-1 1c-1 4 2 4 3 8v4l1 1c1 1 2 2 2 4h0c-1 1 0 1-1 0s-2-1-3-1-3 0-3-1c1 0 1 0 2-1l-1-1c-1-6-4-12-7-17-6-12-14-22-25-29-4-3-10-6-14-8a30.44 30.44 0 0 0-8 8h0c1-2 4-5 5-8 0-1 1-1 1-2h2c1-1 2 0 3-1z"></path><path d="M414 194c1 0 3 0 4-1v2c0 1 0 1-1 2l-2 1c-1-1-1-1-2-3l1-1z" class="H"></path><path d="M593 686v-2l1 1 1 105c-1 3 2 9 3 13 0 1 0 3 1 5h-2l-1 1-2-2c0-1 0-1-1-1-2-3-3-6-4-9s-1-5-3-7c2-2 3-4 5-6 1-1 2-2 2-4v-4-90z" class="K"></path><path d="M586 790c2-2 3-4 5-6 0 4 3 7 1 10l1 1c1 2 1 4 1 6 1 1 1 1 1 2l1 1 1 4-1 1-2-2c0-1 0-1-1-1-2-3-3-6-4-9s-1-5-3-7z" class="F"></path><path d="M474 797c1-1 3-2 4-4 1-1 1-2 2-3v3c0 1 1 1 1 2s-1 5 0 6v27 8l-1 1c-1 2-4 4-4 7l-1-2-1 1c1 1 2 2 3 4 0 1 0 2 1 3h0c-1 0-3-2-3-3 0-2 0-2-1-3v-1c-2-2-4-4-4-7 2-2 4-8 5-11 3-5 2-11 3-16v-2c0-3-2-4-3-6l-2 6h-1c-1 1-1 1-2 1l-1 1-1-1v-2l1-1v1l1 1c1-1 1-1 1-2-1-1-1 0-1-1h-1l2-2c1 0 1 0 1-1 2-1 2-1 2-3v-1z" class="C"></path><path d="M451 646l1-4c1-1 4-2 6-2 2 2 1 6 1 9v1c0 3-1 15 0 17v1 1 11c0 2 0 4 1 6h-1c-2 1 0 4-4 3-1 0-2-1-3-1 0-6-1-13 0-18v-3l1-1c1 0 2-2 2-3l-1-1v-2c-2-3-1-8-1-11l-1-1h-1v-1-1z" class="T"></path><path d="M454 660l1-2v-2c0-1 1-2 1-3v12l-1-2-1-1v-2z" class="s"></path><path d="M451 646l1 1c1 0 2 1 3 1l1 1v4c0 1-1 2-1 3v2l-1 2c-2-3-1-8-1-11l-1-1h-1v-1-1z" class="M"></path><path d="M452 667l1-1c1 0 2-2 2-3l1 2-1 9c0 1 0 1 1 2v2h-2c0 2 0 5 1 6h2v-1h-1-1v-1h1 2c0-4 0-9 1-13v11c0 2 0 4 1 6h-1c-2 1 0 4-4 3-1 0-2-1-3-1 0-6-1-13 0-18v-3z" class="e"></path><path d="M695 200l7 4v-1h1c1 0 2 1 3 1h0c2 2 4 2 7 3h2l4 1h-1c1-1 2-2 2-3l1 1v1c2 1 3 0 4 1s1 7 1 7l1 2v2 1c0 1 0 2-1 3h-1 0c-2-1-4-3-6-4v1l4 3v1l-1 1-6-3-2-1c-1 0-2-1-2-2l-1 1-1-1-1-2c-1 0-2-1-3-3h2v-2c-1-1-2-2-3-4l-1-1-7-4-3-2 1-1z" class="a"></path><path d="M703 203c1 0 2 1 3 1h0c2 2 4 2 7 3h2l4 1 1 1v1l-1 1c-1-1-2-1-4-1v1c-1 0-2-1-3-1-2-1-4-3-5-4 0-1 0 0-1-1h-1c-2-1-2-1-2-2z" class="U"></path><path d="M722 212v-1h1v-3l-2 1-1-1v-1h1c2 1 3 0 4 1s1 7 1 7l1 2v2 1c0 1 0 2-1 3h-1l-6-5c2-2 1-4 3-6z" class="n"></path><path d="M719 218c2-2 1-4 3-6v1c1 1 1 1 1 2s1 2 2 3c0 1 0 1 1 2h1c0 1 0 2-1 3h-1l-6-5z" class="U"></path><path d="M704 207l15 11 6 5h0c-2-1-4-3-6-4v1l4 3v1l-1 1-6-3-2-1c-1 0-2-1-2-2l-1 1-1-1-1-2c-1 0-2-1-3-3h2v-2c-1-1-2-2-3-4l-1-1z" class="C"></path><path d="M709 217l1-1c2 0 2 1 4 2 0 0 1 1 1 2h0-1c-1-1-1-1-2-1l-1 1-1-1-1-2z" class="E"></path><path d="M595 877c4-1 6-3 9-5l2 1 2 3c2 1 5 3 6 4l-4-1c-3 0-5 0-6 1-3 2-5 5-8 7-2 1-6 2-6 4 2 1 5 1 7 1-7 1-16 0-24 0l-43-1c2-2 5-4 7-6-1 2-2 3-4 5 10 1 20 0 30-1 5 0 12 1 17 0 1 0 2-1 3-2 2-1 3-2 5-3 1-1 3-2 5-3 1-1 2-2 2-3v-1z" class="O"></path><path d="M559 474l2-1v-1c3 1 6 1 9 1 1 1 1 1 1 2v2c1 2 1 2 2 3 5 0 10 2 15 3v1 2c1 1 2 1 4 2 3 1 9 1 12 3v1h-3 0c1 1 2 0 3 1v1l-33-8c-3 0-6-1-9-1h-4v-1c1-3 1-7 1-10z" class="K"></path><path d="M559 474l2-1v-1c3 1 6 1 9 1 1 1 1 1 1 2h0c-4 0-7 0-11-1 0 2 1 9 2 11h-4v-1c1-3 1-7 1-10z" class="B"></path><path d="M573 480c5 0 10 2 15 3v1 2c1 1 2 1 4 2 3 1 9 1 12 3v1h-3c-2-1-3-1-4-2-2 0-3-1-5-1-3-1-6-1-8-2-2 0-3 0-5-1-1-1-1-1-3-1h-2c1-1 1-1 1-2h-1c0-1 1-1 1-2h-2v-1z" class="P"></path><defs><linearGradient id="F" x1="560.999" y1="852.095" x2="569.114" y2="873.385" xlink:href="#B"><stop offset="0" stop-color="#393b3d"></stop><stop offset="1" stop-color="#6a6968"></stop></linearGradient></defs><path fill="url(#F)" d="M556 852c2 0 7-1 8 0v1h5 1c0 3 0 5 2 8v1h1v1 1l2 1h1c2 1 4 2 6 2h3l-1 2h0-1l-1-1h-2-1c-1 1-3 1-5 1l-3 3v2 3l-4-2c-3-1-5-4-7-7-1-1-2-2-2-3-2-4-3-8-3-12l1-1z"></path><path d="M558 865c1-1 2-1 3-1 1 2 1 2-1 4h0c-1-1-2-2-2-3z" class="Y"></path><path d="M575 865h1c2 1 4 2 6 2h3l-1 2h0-1l-1-1h-2-1c-1 0-3-1-4 0h0l-2-1h-1c2-1 2-1 3-2z" class="K"></path><path d="M430 774c3-1 5-2 9-2h1l1-1 2 2c-2 1-3 1-5 1l2 2c0 2 3 3 2 4l-1 1h0c0 1 0 1-1 2-1 0-1 1-1 2l-2 1c-1-1-1-1-2-1 0 1 0 2-1 2-1 1-1 1-1 3v1c0 2-1 3-1 5-1 4-7 12-5 15l-1 2h-2 0c0-1 0-1 1-2 0-1 0-2 1-3h0l-1-1h-1-1v-1l-1-1v-2l1-1-1-1 3-16c2 0 3-4 4-6h0c0-2 0-1 1-3h-1v-1h0l1-1z" class="V"></path><path d="M435 785c-1 0-1 0-1-1-2-2-3-3-3-6 0-1 1-1 1-2 1-1 5-2 6-2l2 2c0 2 3 3 2 4l-1 1h0c0 1 0 1-1 2-1 0-1 1-1 2l-2 1c-1-1-1-1-2-1z" class="L"></path><path d="M429 779c1 1 1 1 1 2-1 1-1 2-2 2l2 2-2 2 1 1 1-1 1 1-4 7c0 1-1 2-1 3h1 0 2v-1c1-1 1-1 2-1h0l-1 2c-1 2-3 2-4 2-1 1-1 2-2 3l-1 3-1-1v-2l1-1-1-1 3-16c2 0 3-4 4-6z" class="h"></path><path d="M547 168h4v1l2-1h0c1 1 1 1 2 1v1c-1 0-1 0-2-1v1c1 0 1 1 2 1 1 1 1 0 2 1l1 1v-1h3 0 2v1h1l-1 2c1 0 1 0 2 1 0 0 0 1 1 2h0c1 0 2 1 2 2 1 0 1 0 1 1l1 2c-1 1-2 2-3 2l2 2h1 0l-1 4v-1c-1 0-1 1-2 2h0v3l1 1h0c1 2 1 4 2 5l-1 1c1 1 1 2 2 3v1l1 1c0 1 1 2 0 4-1-2-2-3-3-5v-1l-1 1h-1-1c-5-11-10-26-18-35-1-1-1-2-2-3h1z" class="B"></path><path d="M565 202c1-3-1-5-2-8 0-1-1-2-1-3-1-1-1-1-1-2v-1l2 2 1 2v-1l3-3h-1-2v-1h2c1-1 2 0 3 0 0 1-1 2-2 3-1 0-1 0-2 1 0 1 0 2 1 3l1 1 1 1h0c1 2 1 4 2 5l-1 1c1 1 1 2 2 3v1l1 1c0 1 1 2 0 4-1-2-2-3-3-5v-1l-1 1h-1c0-2-1-3-2-4z" class="C"></path><path d="M547 168h4v1l2-1h0c1 1 1 1 2 1v1c-1 0-1 0-2-1v1c1 0 1 1 2 1 1 1 1 0 2 1l1 1v-1h3 0 2v1c-1 0-2 0-3 1 1 1 3 2 4 3l-2 2v1l1-1 1 1c-1 1-1 1-3 2h-4c1-2 1-2 1-3l-1 1-2-2 1-2h-2c-1-2 0-3-3-3h0l14 29c1 1 2 2 2 4h-1c-5-11-10-26-18-35-1-1-1-2-2-3h1z" class="k"></path><path d="M451 625l1 1c0 3 1 7 0 10l1-1h2 3c-1 1 0 3 0 5-2 0-5 1-6 2l-1 4v1 1 3-1c-2 2 0 4-2 5l-1 1h-17v-1l8-1v-2l1-20h-2l1-1h1c2 0 7 0 9-1 1-1 1-2 2-3v-1-1z" class="F"></path><path d="M449 655h-3c1-1 1-1 1-2s1-1 1-2 1-2 1-3h2v3-1c-2 2 0 4-2 5z" class="L"></path><path d="M455 635h3c-1 1 0 3 0 5-2 0-5 1-6 2l-1 4v1h-2c-1-4 0-6 1-9 0 1 0 1 2 1l2-2v2-1l-1-3h2z" class="H"></path><defs><linearGradient id="G" x1="452.677" y1="628.751" x2="449.323" y2="633.249" xlink:href="#B"><stop offset="0" stop-color="#29291e"></stop><stop offset="1" stop-color="#343435"></stop></linearGradient></defs><path fill="url(#G)" d="M451 625l1 1c0 3 1 7 0 10l1-1 1 3v1-2l-2 2c-2 0-2 0-2-1l-2-2v-3l1-1h-3-2-4-2l1-1h1c2 0 7 0 9-1 1-1 1-2 2-3v-1-1z"></path><path d="M448 636l1-3c1 1 1 1 1 2 1 1 2 1 3 2h1l-2 2c-2 0-2 0-2-1l-2-2z" class="R"></path><path d="M444 632h2c0 4 0 8 1 12v4l-1 3c0 1-1 2-2 2v-2c-1-5-2-13 0-18v-1z" class="O"></path><path d="M444 651c-1-5-2-13 0-18 0 1 1 3 2 4 0 2-1 5-1 8 0 2 0 3-1 5v1h0z" class="D"></path><path d="M440 632h4v1c-2 5-1 13 0 18v2c-1 1-3 1-4 1l-1-2 1-20z" class="T"></path><path d="M591 703h0l1-13v-1c0-1 0-2 1-3v90c-1-1-2-1-2-2h-4c2-2 2-3 2-5v-5l1-12c0-2 1-4 1-7h0v-3-3h-1l-2 3-3-1c1-1 1-1 2-1l1-1h-6l-1-2 5 1c-1-4-1-8-1-11-1 1-2 3-4 4l-2-2c4-2 8-4 9-8v-11l1-3h0c1-3 1-6 2-9 0 1-1 4 0 5z" class="F"></path><path d="M590 752c1 1 1 2 2 4h-1v5c0 3-1 6-2 8v-5l1-12zm-2-42l1-3h0c1-3 1-6 2-9 0 1-1 4 0 5l-1 12c-1 2 0 4-1 6h-1v-11z" class="C"></path><path d="M588 721h1l-1 16c-1 1-1 1-2 1-1-4-1-8-1-11-1 1-2 3-4 4l-2-2c4-2 8-4 9-8z" class="W"></path><path d="M591 567c-1-2 0-4-1-6v-2-3c1-2 1-3 0-5-1-1 0-6 0-7-1-1-1-1-1-2 3-4-1-11 2-14h1 1c3 0 4 2 5 4 1 0 2 1 3 2v6 18c0 2 0 3-2 5h-3v1h-2v1c-1 1-2 1-3 2z" class="D"></path><path d="M599 536l-1 1c-2 1-1 5-1 7 0 1-1 3-2 4v-5c1-2 2-5 1-7 0-1 0-1 2-2l1 1v1z" class="K"></path><path d="M599 535h1v16c0 3 1 8-1 11h-6-1c-1-2-1-3-1-5 3 0 6 1 8 0v-21-1z" class="H"></path><path d="M591 567c-1-2 0-4-1-6v-2-3c1-2 1-3 0-5-1-1 0-6 0-7-1-1-1-1-1-2 3-4-1-11 2-14h1 1c1 7 1 14 1 21 0 1-1 3-1 5v2l-1-1v-4-5c-1-2-1-4-1-5h0v16c0 2 0 3 1 5h1v1h6-3v1h-2v1c-1 1-2 1-3 2z" class="d"></path><path d="M599 808c-1-2-1-4-1-5-1-4-4-10-3-13 2 4 2 8 4 12l3 12c3 8 6 16 10 24l3 4c-1 1-1 2-2 3h-1c-1 1-1 2-1 4l-1 1-1-2-2-4h0c-1-2-2-4-4-5v-1l-5-5c-2-2-4-3-7-4v-1-4h2 0 1 3 1v-1-2-1c-1-2-1-3-2-5s-2-2-4-3l2-1 1 1v-2c-1-1-2-2-2-4 1 0 1 0 1 1l2 2 1-1h2z" class="U"></path><path d="M612 845c-1-2-2-2-2-4 1-1 1-2 2-3l3 4c-1 1-1 2-2 3h-1z" class="i"></path><path d="M593 806c1 0 1 0 1 1l2 2 1-1h2c1 4 4 10 4 15l-1 1h-2l-2-4c-1-2-1-3-2-5s-2-2-4-3l2-1 1 1v-2c-1-1-2-2-2-4z" class="Z"></path><path d="M595 810c1 0 1 0 2 1 2 3 3 8 5 12v1h-2l-2-4c-1-2-1-3-2-5s-2-2-4-3l2-1 1 1v-2z" class="G"></path><path d="M598 820l2 4-2 1v1 1c1 0 1 1 2 0h1c2 1 3 3 4 4 1 2 2 4 2 6l-1 3v1c0 1 1 1 1 3-1-2-2-4-4-5v-1l-5-5c-2-2-4-3-7-4v-1-4h2 0 1 3 1v-1-2-1z" class="a"></path><path d="M593 824h1l3 3-1 1h-2c-1-1-1-2-1-3v-1h0z" class="g"></path><path d="M558 395v-3l1-2c0-1 0-3 1-4 1 0 1 0 2 1 0 3 0 7-2 9h0-2c0 1 0 3 1 4h9c0 1 1 1 1 2v3c1 1 2 1 3 0l1-1c1 0 1 1 2 1 1 1 2 0 4 0 0 0 0 1 1 1 2 0 4 0 6 1h2c1 0 2 0 3 1 3 0 6 2 9 2l2 2h1v-2h1c1 3 1 6 1 9-3-1-6-3-9-4-10-2-19-3-29-5-3 0-8-1-9 0h-1v-6c1-3 0-6 1-9z" class="K"></path><defs><linearGradient id="H" x1="558.913" y1="401.338" x2="566.194" y2="406.437" xlink:href="#B"><stop offset="0" stop-color="#3d3d3d"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#H)" d="M558 395v-3l1-2c0-1 0-3 1-4 1 0 1 0 2 1 0 3 0 7-2 9h0-2c0 1 0 3 1 4h9c0 1 1 1 1 2-1 0-1 0-2 1 0 1 0 2 1 3-2 2-6 1-8 1l-2 1c-1-1-1-2-1-4 1-3 0-6 1-9z"></path><path d="M458 706c1 1 3 3 3 4 1 2 1 4 1 5 0 4 0 8-1 12-1 12 0 24 0 36h0c-1-1-1-2-2-3l-2 2c0 1-1 2-1 3-1-1 0-2-1-3v-9c-1-1-1-1-2-1-1-3-1-21 0-24v-9l1-5 1 1c0-1 0-1 1-1 1-1 0-1 1-2h0c1 2 0 4 2 5h0c0-2 1-6 0-7-1-2-1-3-1-4z" class="F"></path><path d="M457 712h0v16h-4v-9l1-5 1 1c0-1 0-1 1-1 1-1 0-1 1-2z" class="J"></path><path d="M456 738c2 3 1 8 1 12 1-2 0-8 1-8 0 3 0 8 1 11v2h0c1 2 0 3 0 5h0l-2 2c0 1-1 2-1 3-1-1 0-2-1-3 1-3 0-6 1-9 1-5 0-10 0-15z" class="O"></path><path d="M455 762c1-3 0-6 1-9 2 3 2 6 1 9 0 1-1 2-1 3-1-1 0-2-1-3z" class="Z"></path><path d="M453 728h4c-1 4-1 7-1 10 0 5 1 10 0 15-1 3 0 6-1 9v-9c-1-1-1-1-2-1-1-3-1-21 0-24z" class="Q"></path><path d="M549 376c0-1 1-2 1-3 1-1 3-1 4-2l1 2c1 1 1 2 2 3 0 2 1 4 1 6v13c-1 3 0 6-1 9v6 18 12 3-2h-1l-1-1v-1l-1 2v-7c0-2 0-4-1-7v-5-2c1-2 0-6 1-8l1-3c-2-1-4-1-6-2v-31z" class="k"></path><path d="M555 409l1 1v22 6c0 1 1 1 1 2v3-2h-1l-1-1v-1l-1 2v-7c0-2 0-4-1-7v-5-2c1-2 0-6 1-8l1-3z" class="K"></path><path d="M540 366c1-1 2-1 3-1l1-1c1 1 1 3 1 4l-1 2 4 4 1-3v5 31c2 1 4 1 6 2l-1 3c-1 2 0 6-1 8v2 5c-2-4 2-12-1-15-1 1-1 1-1 3h-1c0-2 0-4-2-6l-2-1-1-1-1 1h-1c1-1 1-3 1-4l-2-2s0-1 1-1l-2-2-1-1v-1l-2-3-1-1v-4l1-2 3-19-1-2z" class="I"></path><path d="M547 383c2 3 1 18 1 23-1 0 0 0-1-1l-1-1v-6c2-5 0-10 1-15z" class="G"></path><path d="M540 366c1-1 2-1 3-1l1-1c1 1 1 3 1 4l-1 2 4 4c-1 3-1 6-1 9-1 5 1 10-1 15v-9l-1-1c-1-3 0-6 0-9v-3c0-2 1-1 1-3l-2-1c-1-1-2-2-3-4l-1-2z" class="Y"></path><path d="M541 368c1 2 2 3 3 4l2 1c0 2-1 1-1 3v3c0 3-1 6 0 9l-7-1 3-19z" class="F"></path><path d="M538 387l7 1 1 1v9 6l1 1-3-1-2-2s0-1 1-1l-2-2-1-1v-1l-2-3-1-1v-4l1-2z" class="K"></path><path d="M537 393c1 0 1-1 2-2h1l1 1c1 1 1 2 2 3 0 1 1 2 1 2l2 1v6l1 1-3-1-2-2s0-1 1-1l-2-2-1-1v-1l-2-3-1-1z" class="k"></path><path d="M537 393c1 0 1-1 2-2h1l1 1c0 2 0 4-1 5l-2-3-1-1z" class="D"></path><defs><linearGradient id="I" x1="597.944" y1="410.508" x2="571.552" y2="383.977" xlink:href="#B"><stop offset="0" stop-color="#4f4f51"></stop><stop offset="1" stop-color="#807f7d"></stop></linearGradient></defs><path fill="url(#I)" d="M562 387h4v-1-1c1 1 1 1 1 2 3 1 5 2 8 2l2 1 9 1c4 1 7 2 11 3 2 1 5 3 7 4s3 2 5 3c2 0 5 2 6 4 1 1 1 1 1 2h-2l-2 2 2 2v1c-3-2-5-5-9-7-11-7-29-7-42-9h-3 0c2-2 2-6 2-9z"></path><path d="M609 401c2 0 5 2 6 4 1 1 1 1 1 2h-2l-2 2-1-1c-1-1-3-2-4-3-1 0-1-1-2-1-1-1-1 0-1-1h1c1-1 1 0 3-1l1-1z" class="F"></path><path d="M562 387h4v-1-1c1 1 1 1 1 2 3 1 5 2 8 2-1 1-1 1-2 1 0 2-1 2-1 2h-2l-1-1h-1v1c-1-1 0-1-2-2 0 2-1 2-2 3 0 2 0 2-1 3h-3 0c2-2 2-6 2-9z" class="b"></path><path d="M466 687c1 2 1 3 1 5 0 1 0 2 1 3 1 5 0 11 0 16l1 14c0 3-1 9 1 12 1 3 0 7 0 10v22c0 6 1 14-1 20h2c1 1 1 2 1 3-2 1-3 1-3 3v7c1 0 1 0 2-1l3-3c0 2 0 2-2 3 0 1 0 1-1 1l-2 2-1-1c0-1 0-2-1-3-1 0 0-4-1-5l-1 1h0v-12c0-3 0-7 1-9v-75c0-5-1-9 0-13z" class="N"></path><path d="M466 775v1c3-4 0-11 1-15 1 2 1 3 1 5v10c0 2-1 4-1 6 1 2 0 4 0 6-2-1-1-3-2-4 0-3 0-7 1-9z" class="B"></path><path d="M369 881s0-1 1-2c2-1 4 0 6 0l1 1h0 2c2 1 2 1 4 1l1 1h0c1 2 1 2 3 2 1-1 2-2 2-3l1-1h1 1 6c2 1 4 1 5 1h4l2 2c-1 0-1 0-1 1l1 1h0c0 1-1 1-1 2h-1-2-4l-1 1 1 1c0 1-1 2-1 3h-6-6-22l2-1c0-1 1-2 0-3h-1-1c-1 1-2 1-4 0h0c1-1 2 0 3-1h1l-2-2c1-1 1-2 2-3h0c1-1 1 0 2 0l1-1z" class="m"></path><path d="M369 881s0-1 1-2c2-1 4 0 6 0l1 1h0 2c2 1 2 1 4 1l1 1h0c1 2 1 2 3 2v1c-2 0-2 0-3-1h-1l-2 1h-4c-2 0-5 1-7 0v-2l-2-1 1-1z" class="a"></path><path d="M379 880c2 1 2 1 4 1l1 1-7 1-1-1 1-2h2z" class="N"></path><path d="M369 881s0-1 1-2c2-1 4 0 6 0l1 1h0l-1 2 1 1v1h-3c0-1 0-2-1-3h-4z" class="U"></path><path d="M392 880h6c2 1 4 1 5 1h4l2 2c-1 0-1 0-1 1l1 1h0c0 1-1 1-1 2h-1-2-4l-1 1 1 1c0 1-1 2-1 3h-6-6v-1h5 1 2v-1h-1v-2l4-4c-1-1-1-1-2-1-1-1-1-1-2-1-1-1-2-1-4-2h1z" class="n"></path><path d="M403 881h4l2 2c-1 0-1 0-1 1l1 1h0c0 1-1 1-1 2h-1-2-4l-1 1 1 1c0 1-1 2-1 3h-6c2-1 4-1 5-1 0-2-1-3 0-5 2-1 3-2 5-2h0c1-2 0-2-1-3z" class="T"></path><path d="M588 484h4v-1c-2 0-2 0-3-1 3 0 8 1 12 1 1 0 1 1 2 1 0-2-1-3-2-4l1-1h1c1 3 1 5 1 7 5 0 11-1 15 0v1h6c-2 1-4 1-6 2h0l4 1h0c-1 0-2 1-3 1h-1l-1 1h5 1c0 2-1 3-2 4-2 0-2 0-4 1h-7l1 1h2v3 3c1 2 1 4 1 6l-1 1c-2-1-4-2-6-2h0l-1 1c0 2 0 5-1 7l-1-1c0-1-1-2-1-4v-3-11-4h0v-1c-1-1-2 0-3-1h0 3v-1c-3-2-9-2-12-3-2-1-3-1-4-2v-2z" class="B"></path><g class="R"><path d="M618 492h5 1c0 2-1 3-2 4-2 0-2 0-4 1h-7-1v-1l-1-1c1-1 1-2 1-3h7l-1 3v1h1c0-1 0-2 1-4h0z"></path><path d="M604 494h0c1-2 0-3 1-5 1 0 2 1 3 1v1c0 1 1 3 1 4-1 1-1 2-1 3 1 3 1 8 0 11l-1 1c0 2 0 5-1 7l-1-1c0-1-1-2-1-4v-3-11-4z"></path></g><path d="M571 337h6l1 1h3l3 1h0l10 4c-1 3-1 6-1 9v2s-1 0-1 1h1c-1 1-3 1-4 1l-1 1c-3 0-7 0-10-1h-9-4c-1 1-1 0-1 1-1-1-1-5-1-7l1-1 1 1c2 0 3 0 5-1v-5c1-2 1-5 1-7h0z" class="D"></path><path d="M584 339l10 4c-1 3-1 6-1 9v2s-1 0-1 1h1c-1 1-3 1-4 1l-1 1c-3 0-7 0-10-1h-9 0c-1-1-1-1-2-1 0-1-2-2-2-3 7-1 13 1 20 2 0-3 1-11 0-13l-1-1v-1z" class="M"></path><path d="M569 356l4-1v-1h1c1 0 2 0 4 1v1h-9 0z" class="Q"></path><defs><linearGradient id="J" x1="548.846" y1="196.545" x2="537.152" y2="200.091" xlink:href="#B"><stop offset="0" stop-color="#454547"></stop><stop offset="1" stop-color="#5b5c5c"></stop></linearGradient></defs><path fill="url(#J)" d="M520 167v-2l6 6 1-1c2 2 3 3 4 5 1 1 2 1 2 1l3 3c1 0 1-1 2 0l3 3v-1l-1-2h0c3 2 4 4 4 7l4 3h0c4 6 5 11 5 18 0 4 1 8 1 12s-1 9 0 12v1l-2 2c-1 0 0 0-1-1l2-1-1-1-1-1v-6h-1c0-4-2-6-3-9-4-10-6-22-12-31-2-3-4-5-6-8l-3-3h-1c-1-2-3-5-5-6z"></path><path d="M549 217h2v2h-1l-1-2z" class="F"></path><path d="M553 232v-2c-1-5-1-7 1-11 0 4-1 9 0 12v1l-2 2c-1 0 0 0-1-1l2-1z" class="C"></path><path d="M544 186l4 3h0c4 6 5 11 5 18-1-1-1-3-1-4-2-5-6-10-9-15l1-2z" class="F"></path><path d="M466 838c1-1 0-2 1-4l1 1v1c1-1 1-1 2-1v1c0 3 2 5 4 7v1c1 1 1 1 1 3 0 1 2 3 3 3h0c-1-1-1-2-1-3-1-2-2-3-3-4l1-1 1 2 1 1 3 3 6 10c1 2 2 4 4 5h1c0 1 1 1 2 2h2l1 5c0 1 2 2 3 2 1 2 1 5 2 7 1 1 2 1 2 2 1 0 1 2 1 2h-1v7c-1 1-1 3 0 4l-1 1-1-1h-1l1 2c-1 1 0 0-1 0h-1c2 4 3 7 4 10h-1l-1-3c0-2-1-3-2-5-2-3-2-7-4-10-3-11-8-21-16-30-4-7-10-12-13-20z" class="Y"></path><path d="M490 863h1c0 1 1 1 2 2h2l1 5c0 1 2 2 3 2 1 2 1 5 2 7 1 1 2 1 2 2 1 0 1 2 1 2h-1v7c-1 1-1 3 0 4l-1 1-1-1h-1l1 2c-1 1 0 0-1 0h-1 0c-1-2-3-7-2-9v-3l-2-9c0-2-1-3-1-4l-1-2-3-6z" class="R"></path><path d="M496 870c0 1 2 2 3 2 1 2 1 5 2 7 1 1 2 1 2 2 1 0 1 2 1 2h-1v7c-1 1-1 3 0 4l-1 1-1-1h-1l1 2c-1 1 0 0-1 0h-1 0c-1-2-3-7-2-9v-3c2 3 2 7 4 9 1-1 1-3 0-5-1-6-3-12-5-18z" class="B"></path><path d="M563 708c4-7 12-12 15-19v-1c1-3 1-6 1-9v-17h6v19c0 3-1 6-1 9l-1 1h0v1c-4 6-10 11-14 17-2 2 0 9 0 12v5h-1l-2-1h0v2 9c0 2 0 4-1 6v6c-1-6-1-14-2-20-2-7-1-14 0-20z" class="F"></path><path d="M571 703c-1 1-1 2-2 3v1c-1 1-2 2-2 3l-2 8v6h-1c0-3 0-5-1-8 0-3 1-6 2-9 2-1 3-3 5-4h1z" class="D"></path><path d="M570 703c0-2 2-3 3-4 1-2 3-3 4-5l1-2c1-1 1-1 1-2l2-3v-1-4c1-2 0-3 1-5h1v5c-1 1-1 2-1 4l1 1-1 2c1-2 0-4 1-5 1-2 0-3 1-4h0l1 1c0 3-1 6-1 9l-1 1h0v1c-4 6-10 11-14 17-2 2 0 9 0 12v5h-1l-2-1h0c0-4 0-8 1-11l1-2-1-1c0-2 3-6 5-8h1c1-3 4-4 5-7l-7 7h-1z" class="Q"></path><path d="M566 725l2-2c0-1 0-1 1-2v5h-1l-2-1z" class="M"></path><path d="M666 468h2c-1 1-3 2-4 3l1 1-5 5c1 2 1 2 1 5v-1c-2 0-3 0-5-1v1h-1 0c1 1 1 1 2 1h1l2 2c-2 2-4 3-6 4h-1v1l-1 2 1 1c0 2-1 2-3 2h-2-1l-2-1c-1 1-2 2-4 2-4 1-9 2-13 2h-2-1c-3-1-5 0-7 0 2-1 2-1 4-1 1-1 2-2 2-4h-1-5l1-1h1c1 0 2-1 3-1h0l-4-1h0c2-1 4-1 6-2h-6v-1h8 2c6 0 14-4 20-7 2-1 5-1 7-3 2-1 4-3 7-5l1-1c1-1 1-2 2-2z" class="Y"></path><path d="M635 493c0-1 0-3 1-4h2v1c0 2-1 3-2 4l-1-1z" class="d"></path><path d="M625 494v-1l1-2 1-1c1 2 1 1 1 2s0 0 1 1c0 1-1 2-1 3h-2l-1-2z" class="R"></path><path d="M666 468h2c-1 1-3 2-4 3l1 1-5 5c1 2 1 2 1 5v-1c-2 0-3 0-5-1v1h-1 0c1 1 1 1 2 1h1l2 2c-2 2-4 3-6 4h-1s-1 0-2-1h-1c0-1-1-2-2-3l-2 2-2-1h0-1c1 1 1 1 1 2h-1v-1h-1-2l-3 1c-3 1-5 1-8 1v-1c3 0 6 0 9-2 6-3 13-5 19-8 2-1 5-4 6-6l1-1c1-1 1-2 2-2z" class="Q"></path><path d="M656 480c1-1 3-2 4-3 1 2 1 2 1 5v-1c-2 0-3 0-5-1z" class="W"></path><path d="M651 487l-1-4h0c1-2 2-2 3-3l1 1v6 1h-1s-1 0-2-1z" class="d"></path><path d="M654 481h1 0c1 1 1 1 2 1h1l2 2c-2 2-4 3-6 4v-1-6z" class="B"></path><path d="M642 486h1v1h1c0-1 0-1-1-2h1 0l2 1 2-2c1 1 2 2 2 3h1c1 1 2 1 2 1v1l-1 2 1 1c0 2-1 2-3 2h-2-1l-2-1c-1 1-2 2-4 2-4 1-9 2-13 2h-2-1c-3-1-5 0-7 0 2-1 2-1 4-1 1-1 2-2 2-4l1 2 1 2h2c0-1 1-2 1-3h1c0-1 0-1 1-3l1 1c2 1 2 1 3 2l1 1c1-1 2-2 2-4l1 1 1-2c1-1 1-2 2-3z" class="I"></path><path d="M630 493c0-1 0-1 1-3l1 1 1 2-1 1c-1 0-2-1-2-1z" class="H"></path><path d="M625 497c6-1 13-2 19-5 3-1 5-3 9-3l-1 2 1 1c0 2-1 2-3 2h-2-1l-2-1c-1 1-2 2-4 2-4 1-9 2-13 2h-2-1z" class="Q"></path><path d="M506 127h6l-2 1h1c1 1 1 1 1 3h1l1-1c1 3 0 6 0 9l1 2h-1c0 2 0 13 1 15l1-1v3l-1-1v9 1c-1 2-1 5-1 7h-1l-1-4h-1v3h-1c0-1-1-2-1-4v1h0c-1 1-1 1-1 2v1c-1 1-2 1-2 2l-1-1-1-1v-4l-2 1c0-1-1-2-1-3l2-3c0-1 1-2 0-3v-2c1-1 0-1-1-2s-1-1-3-1l-2-1v-1l1-2c1-1 2-2 3-2l3 1c1 0 1-1 2-1 0-2 1-6 0-7 0-1-1-1-1-2 0-3 0-11 2-12l1 2v-3l-2-1z" class="J"></path><path d="M508 132v4c1 1 2 1 2 2s0 3-1 4v1 1l-2-2v-6c1-2 1-3 1-4z" class="a"></path><path d="M499 156l-2-1v-1l1-2c1-1 2-2 3-2l3 1c1 0 1-1 2-1l-1 24-1-1v-4l-2 1c0-1-1-2-1-3l2-3c0-1 1-2 0-3v-2c1-1 0-1-1-2s-1-1-3-1z" class="S"></path><path d="M506 127h6l-2 1h1c1 1 1 1 1 3h1l1-1c1 3 0 6 0 9l1 2h-1c-1 1-2 1-3 2h1v6c0 5 1 12-1 16v1h0-1l-1-3c-1 0-1-2-1-3v-11-5h1v-1-1c1-1 1-3 1-4s-1-1-2-2v-4-1-3l-2-1z" class="o"></path><path d="M506 127h6l-2 1h1c1 1 1 1 1 3h1v9l-1 1c-1-1-1-1-1-3h-1 0c0-1-1-1-2-2v-4-1-3l-2-1z" class="X"></path><path d="M639 529l3 1c2 0 3 1 5 1 2 1 6 2 7 3s1 1 1 2c1 1 6 4 6 5l-1 1-1 1-1-1c-1 0-1 0-1 1h-3c1 1 3 2 3 3 1 2-1 4 2 4h1c1 0 1 0 3 1l1-1 1 2-3 1c2 2 2 3 2 5-1 0-3 1-4 1h-3c-2 0-5-3-7-4-1-3-8-5-11-5-1-1-3-1-4-2v-1c1 1 2 1 4 1-1 0-2 0-3-1v-4l1-1h-1c0-1 1-2 1-3l1-4c0-2 0-4 1-6z" class="B"></path><path d="M655 554c2 1 4 2 6 1v-2h1c2 2 2 3 2 5-1 0-3 1-4 1l-5-5z" class="D"></path><path d="M650 555c-1-3-8-5-11-5-1-1-3-1-4-2v-1c1 1 2 1 4 1 4 1 12 4 16 6l5 5h-3c-2 0-5-3-7-4zm-9-10l3-6c4 1 7 3 10 4 1 1 3 2 3 3 1 2-1 4 2 4h1c1 0 1 0 3 1h-2 0c-3 1-5-1-8-2-3-2-8-4-12-4z" class="K"></path><path d="M642 530c2 0 3 1 5 1 2 1 6 2 7 3s1 1 1 2c1 1 6 4 6 5l-1 1-1 1-1-1c-1 0-1 0-1 1h-3c-3-1-6-3-10-4l-3 6v1c-1 0-1 0-3-1 0-2 1-4 2-6 1-3 1-6 2-9z" class="d"></path><defs><linearGradient id="K" x1="577.468" y1="835.88" x2="590.336" y2="832.485" xlink:href="#B"><stop offset="0" stop-color="#555657"></stop><stop offset="1" stop-color="#777571"></stop></linearGradient></defs><path fill="url(#K)" d="M587 825c1 0 3-1 4-1v4 1c3 1 5 2 7 4l5 5c-3 2-9 5-8 9v1h0-1c-1-2-3-4-5-5l-2-1c-2 1-5 1-7 2h-1l-1-1-3 1c-4 0-6-5-9-7l-2-3 4-3c2-1 3 0 5-2h2c1 0 2-1 2-1v-1c1-1 1-2 2-2l1 2h2l1-2 2 1 2-1z"></path><path d="M587 825c1 0 3-1 4-1v4 1h-1c-1 0-2-1-3-1v-3z" class="J"></path><path d="M591 829c3 1 5 2 7 4-1 1-2 1-3 1l1-1c-1 0-2 2-3 2-2-2-3-3-3-6h1z" class="M"></path><defs><linearGradient id="L" x1="592.514" y1="845.117" x2="596.345" y2="835.246" xlink:href="#B"><stop offset="0" stop-color="#676560"></stop><stop offset="1" stop-color="#807c74"></stop></linearGradient></defs><path fill="url(#L)" d="M593 835c1 0 2-2 3-2l-1 1c1 0 2 0 3-1l5 5c-3 2-9 5-8 9v1h0-1c-1-2-3-4-5-5l-2-1c0-4 2-3 4-4 1-1 1-2 2-3z"></path><path d="M593 835c1 0 2-2 3-2l-1 1-5 7c-1 1-1 1-1 2l-2-1c0-4 2-3 4-4 1-1 1-2 2-3z" class="D"></path><path d="M573 829l-1 3 1 1v-3h3c0 2 0 4 1 6h0c0 1 1 2 1 3 1 1 1 1 1 2h-1v2l-3 1c-4 0-6-5-9-7l-2-3 4-3c2-1 3 0 5-2z" class="Y"></path><path d="M438 662h5 0v1c-1 2-1 13 0 15v1c0 2 1 4 2 6 3 8 7 14 13 21 0 1 0 2 1 4 1 1 0 5 0 7h0c-2-1-1-3-2-5h0c-1 1 0 1-1 2-1 0-1 0-1 1l-1-1-1 5v-2c-1-2-1-3-2-4v5l-1 1v-3c0-2-1-3-1-4l-1 2v-2h-1c-2-1-3-1-4-3h0c-2-1-1 0-3 0h-1v3l1 1-1 1c-1-2 0-4 0-6 1 0 1 0 1-1h3c0-1-1-1-1-2l1-1h0l-1-1 1-1c1 1 1 2 3 2l-2-2-2-2c-1-1 0 0-1-2-1-1-3-3-4-5h0c0-2-1-3-1-4v-2-3c0-2 0-8 1-9v-13h1z" class="E"></path><path d="M448 712c-1-2-4-4-5-6l1-1 5 6 1-1c1 1 1 2 1 3v5l-1 1v-3c0-2-1-3-1-4l-1 2v-2z" class="N"></path><path d="M437 675v-13h1c-1 4 0 12 0 16v4c1 2 0 3 1 4v1c1 3 2 5 4 7l5 6v1c-2 0-2-3-4-3l-1 1c-2-2-4-4-5-6-2-5-1-13-1-18z" class="X"></path><path d="M444 689c1 3 3 6 5 9 3 5 6 8 8 14-1 1 0 1-1 2-1 0-1 0-1 1l-1-1-1 5v-2c-1-2-1-3-2-4 0-1 0-2-1-3l-1-2v-1l2 1c-3-4-6-6-8-9l1-1c2 0 2 3 4 3v-1l-5-6 1-5z" class="g"></path><path d="M450 710l-1-2v-1l2 1c2 2 2 3 3 6l-1 5v-2c-1-2-1-3-2-4 0-1 0-2-1-3z" class="t"></path><path d="M438 662h5 0v1c-1 2-1 13 0 15v1c0 2 1 4 2 6 3 8 7 14 13 21 0 1 0 2 1 4 1 1 0 5 0 7h0c-2-1-1-3-2-5h0c-2-6-5-9-8-14-2-3-4-6-5-9l-1 5c-2-2-3-4-4-7v-1c-1-1 0-2-1-4v-4c0-4-1-12 0-16z" class="O"></path><path d="M438 678l1 1c0-5 0-10 1-14l1-2c1 2 1 4 1 6l1 15 1 5-1 5c-2-2-3-4-4-7v-1c-1-1 0-2-1-4v-4z" class="g"></path><path d="M439 687l1-1c0-1-1-4 0-6 1 1 1 1 1 2l1 1 1 1 1 5-1 5c-2-2-3-4-4-7z" class="a"></path><path d="M347 892h-67l53-12-2-5c1 0 3 3 3 5 3 0 5-2 8-2h0c1 1 4 1 4 2 1 1 2 2 2 3h0c0 2 0 3 1 4v1l1 1h-1c-1 1-1 2-2 3z" class="u"></path><path d="M334 880c3 0 5-2 8-2h0c1 1 4 1 4 2 1 1 2 2 2 3h0c0 2 0 3 1 4v1h0c-2-2-4-5-5-6-2-1-5 0-7-1-1 0-2-1-3-1z" class="o"></path><path d="M587 842l2 1c2 1 4 3 5 5h1l1 1c-1 1-1 2 0 3h0c1 1 0 1 0 2h0v2 1c0 3-2 5-4 6-2 3-4 3-7 4h-3c-2 0-4-1-6-2h-1l-2-1v-1-1h-1v-1c-2-3-2-5-2-8 1-1 0-1 0-2s1-2 1-2l2-3c0-1 1-2 2-2l3-1 1 1h1c2-1 5-1 7-2z" class="O"></path><path d="M586 865v-1-1h2l1-2-1-2 1-1h1c2 2 0 3 1 5l4-4v-2h1c0 3-2 5-4 6-2 0-3 1-4 0l-2 2z" class="C"></path><path d="M582 845c1-1 3-2 5-2 3 2 5 3 6 6 1 2 0 1 1 2-1 0-1 0-1-1h-1c-1-1-1-1-3-2s-5-2-7-3z" class="b"></path><path d="M579 864c1 1 2 1 4 1l1-1c0-1-1-1-1-1 1-2 2-2 3-4v1c0 1 0 2-1 4v1h1l2-2c1 1 2 0 4 0-2 3-4 3-7 4h-3c-2 0-4-1-6-2h-1l-2-1v-1-1h-1v-1h0l2-1c1 1 1 2 3 2 0 1 1 1 2 2z" class="B"></path><path d="M587 842l2 1c2 1 4 3 5 5l-1 1c-1-3-3-4-6-6-2 0-4 1-5 2h-2c0 1-1 1 0 2h0c1 2 3 3 4 5 1 1 1 0 3 1l1 1c0 2 0 2-1 3h-3c-1 0-2 0-3 1s-2 2-3 4h1v2c-1-1-2-1-2-2-2 0-2-1-3-2l-2 1h0c-2-3-2-5-2-8 1-1 0-1 0-2s1-2 1-2l2-3c0-1 1-2 2-2l3-1 1 1h1c2-1 5-1 7-2z" class="G"></path><path d="M573 849l2-3h4v2l1 1v1l-2 1h-1v-1c-1 1-4 3-4 5h0l1 1c2-2 1-4 5-4 1 2 1 3 1 5-1 1-3 2-5 1h-1l-1 1 1 1-2 1h0c-2-3-2-5-2-8 1-1 0-1 0-2s1-2 1-2h2z" class="F"></path><path d="M571 849h2l-1 3c-1 1 0 2 0 3v2c0 1 1 1 1 2l1 1-2 1h0c-2-3-2-5-2-8 1-1 0-1 0-2s1-2 1-2z" class="E"></path><path d="M570 601v-1c0-2 1-4 1-6 4-1 9 0 14 0-1 2-2 7 0 9l1-1v5 14c1 1 2 1 3 1v2c0 1-1 1-1 1h-3c-4 0-8 1-12 0h-1-2c-1-1 0-1-1-1-2-2-1-7-1-10 1 0 1-1 2-1-1-1-1-1-1-2s1-3 0-4c0-2-1-3-1-4 1 0 1-1 2-2z" class="L"></path><path d="M579 596h3v25c1 1 3 0 4 0 1 1 2 1 3 1l-1 1c-6 0-13 1-18 0h0c1-2 5-1 7-1-1-4-1-8-1-12l1-14h2z" class="D"></path><path d="M579 596h3v25c-1 0-3 0-4-1v-3c1-4 1-10 1-14 0-3-1-5 0-7z" class="K"></path><path d="M534 151c2 3 2 7 4 9v1h0c-2-1-2-3-3-5l-4-10h119v1l-1 1c0 1-1 2-1 2l-4 4-3 3 1 1 2-1s1 0 1 1l-1 1c-1 0 0 0-1-1h-1l-1 1c-1 0-2 0-3 1l2 2-2 1-2-1c-1 2-2 2-4 3-1 0-1 1-2 2h2v1l-2 2c0 1 0 1 1 2l1 2-1 1-1-1v-1l-1-2-3-3-1 1v1c-1 0-3-2-3-2h0c-2-1-3-2-3-4 2-4 7-7 11-9l-3-4v-2c-2 2-3 2-4 2v-3h-90c0 1 0 2 1 3z" class="g"></path><path d="M627 167c0-1 0-1-1-1 0-1-1-1-1-2 1 0 1 0 2-1-1 0 0 0-1-1 1-1 3-2 5-3h-1c0 1 0 2-1 2l1 1 1 1c-1 1-2 3-4 4z" class="J"></path><path d="M631 159v-1c1 0 1-1 2-1l1-2c1-1 2-1 3 0l-1 2 1 1c0 1-1 1-1 2v2c-1 2-2 2-4 3 1-1 1-2 1-3l1-2-1-1c-2 1-2 1-3 3l-1-1c1 0 1-1 1-2h1z" class="h"></path><path d="M630 162c1-2 1-2 3-3l1 1-1 2c0 1 0 2-1 3-1 0-1 1-2 2h2v1l-2 2c0 1 0 1 1 2l1 2-1 1-1-1v-1l-1-2-3-3 1-1c2-1 3-3 4-4l-1-1z" class="Q"></path><path d="M637 155c1 0 3-1 3-2 2-1 2 0 3 0 2-1 3-3 5-3l-4 4-3 3 1 1 2-1s1 0 1 1l-1 1c-1 0 0 0-1-1h-1l-1 1c-1 0-2 0-3 1l2 2-2 1-2-1v-2c0-1 1-1 1-2l-1-1 1-2z" class="e"></path><path d="M623 148h25c-7 2-12 4-18 7l-3-4v-2c-2 2-3 2-4 2v-3z" class="l"></path><path d="M567 875l4 2h1c3 1 6 2 10 3h5l8-2c0 1-1 2-2 3-2 1-4 2-5 3-2 1-3 2-5 3-1 1-2 2-3 2-5 1-12 0-17 0-10 1-20 2-30 1 2-2 3-3 4-5 0-1 1-2 2-2 1-2 3-3 5-5l10-1c3 0 5-1 8-1 2 0 4 1 5-1z" class="L"></path><path d="M451 713c1 1 1 2 2 4v2 9c-1 3-1 21 0 24 1 0 1 0 2 1v9c1 1 0 2 1 3 0-1 1-2 1-3l2-2c1 1 1 2 2 3h0l1 3-1 1-1-1v1l2 1v6l-1 1c-1 1-1 1-1 3v6l-2 2c1 1 2 1 2 2h0c-1 1-1 1-2 1h0-2-1l-1-1h-1-5c-1-1-1-1-1-2h2l-1-1v-3h5l1 1v-1l2-2c1 0 0-1 0-2h0c-1 1-1 2-2 2h-1c1-1 1-2 1-2h-1-3v-1h2v-1h-4l1-1h1l-1-1c0-1 0-1-1-2v-1c-1-1-1-2 0-3l1-1-1-2c-1 1-1 2-2 2v-2l1-1c1 1 1 1 2 1v-8-13-4l-1-2-2-1c-2-2-6-2-7-5-1 0-1 0-2-1v-1-1l-1-2h1l-5-5c1-2 1-2 3-3l2 2c1 0 2 1 2 2l10 4 1 1 1-1v-9-5z" class="P"></path><path d="M448 788c-1-1-1-1-1-2h2l1 1h4 4v-1c1 1 2 1 2 2h0c-1 1-1 1-2 1h0-2-1l-1-1h-1-5z" class="W"></path><path d="M458 775h3c-1 1-1 1-1 3v6l-2 2v1c1-3 0-9 0-12z" class="L"></path><path d="M437 727c4 2 8 4 12 5v8l-1-2-2-1c-2-2-6-2-7-5-1 0-1 0-2-1v-1-1l-1-2h1z" class="R"></path><path d="M453 752c1 0 1 0 2 1v9c1 1 0 2 1 3 0-1 1-2 1-3l2-2c1 1 1 2 2 3h0l1 3-1 1-1-1v1l2 1v6l-1 1h-3-6-1l2-1c-1 0-1 0-2-1l-1-3v1 1l-1-1 1-1v-1c1-2 1-2 3-2v-15z" class="r"></path><path d="M457 762l2-2c1 1 1 2 2 3l-3 3c-1-1-2-1-2-1 0-1 1-2 1-3z" class="E"></path><path d="M450 770c3-2 6-2 9-1 1 1 2 1 2 3l-1 1v-1c-3 1-2 2-5 2-1-1-1 0-2 0s-1 0-2-1l-1-3z" class="W"></path><path d="M562 439c-1-1 0-2 0-4-1 0-1 0-1-1-1-2 0-18 1-19s3-1 4-1l1 1c0-1 0-1 1-2 1 2 2 0 4 2 0 2 1 4 0 6v1c1 1 0 3 0 5 1 2 1 5 1 7v3 1h-1c-1 1-2 1-3 1h-1 2 3v-2-1h0v-1c0-1 1-2 2-2h1v-1l-1-1c-1 0-1 0-2-1l2-2v-1l-1 1h0l1-2 1-1c-1 0-1-1-1-1h-1v-1h1v-1h-1l1-1v-1h0v-2c1-2 1-2 1-3h0l1 1 1 1 1-1c1-1 2-1 3 0l1 3v2c0 4 0 8 2 11 0 3 0 5-1 8 1 1 3 1 4 2 2-2 0-5 2-6l-1-2 1-2h1v1l-1 1c2 1 5-1 7 0 0 1 1 1 1 1v1h-1c1 2 1 0 2 2l-1 1v-1l-1 1 2 2h-2l1 1s1 0 1 1v2h-2c-1-1-2-1-3-1h-1 0v1h-1l-1-1h-2l-2-1c-1-1-3-1-5-1-1-1-2-2-4-2h-5c-3-1-5-1-8-1h-3z" class="I"></path><path d="M583 421h-3c1-1 0-1 1-2l-2-1v-1h2c0 1 0 0 1 1l1 1v2z" class="H"></path><defs><linearGradient id="M" x1="353.567" y1="164.774" x2="298.805" y2="129.466" xlink:href="#B"><stop offset="0" stop-color="#dbd3c6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#M)" d="M359 163c-15-3-29-7-44-10l-21-4c-4-1-9-1-13-2 5-1 10-1 14-1h20 45l1 1h4c-2 4-5 5-7 9v1c-1 1-2 3-1 4 0 1 2 1 3 1l-1 1z"></path><path d="M464 375l1 1c-1 2-1 4-1 5 0 5-1 10 0 14v7l-2-2c2-1 1-2 1-3l-1-1-9 1-9 1-5 1 1-2c1-2 2-3 4-4l-6 1-5 1c-4 1-8 4-12 4-1-1-3 0-5-1h0l-1-7c4-3 8-4 14-6 4-2 9-2 14-3 2 1 3 1 6 1l1-1v1h1v-1h1c2-1 4-1 6-1 1 1 3 1 4 1v-1-1l-2 1-1-1c2-1 3-2 4-4h-4c1-1 3 0 5-1z" class="I"></path><path d="M452 384v-2c3 0 6 0 8 1v1c0 1 1 2 2 2h-3c-1-1-4-1-5-1h-1s0-1-1-1z" class="B"></path><path d="M436 392l10-2-2 3h0l-6 1-5 1c1 0 0 0 1-1 1 0 2-1 3-1l-1-1z" class="j"></path><path d="M462 386h1l1-5c0 5-1 10 0 14v7l-2-2c2-1 1-2 1-3l-1-1c0-1-1-1-1-2-1-1 0-3 0-4 1-1 1-2 1-3h-4c-2 0-3 1-5 0v-2h1c1 0 4 0 5 1h3z" class="t"></path><path d="M458 387h4c0 1 0 2-1 3 0 1-1 3 0 4 0 1 1 1 1 2l-9 1v-2s0-1 1-1c0-1 1-2 2-2 2-2 2-3 2-5z" class="s"></path><path d="M416 398c1 0 3-1 3-2 1 0 1-1 2-2 2-1 5-2 8-2h0c-1 0 0 0-1 1l-3 1h0l11-2 1 1c-1 0-2 1-3 1-1 1 0 1-1 1-4 1-8 4-12 4-1-1-3 0-5-1z" class="X"></path><path d="M452 384c1 0 1 1 1 1v2c2 1 3 0 5 0 0 2 0 3-2 5-1 0-2 1-2 2-1 0-1 1-1 1v2l-9 1-5 1 1-2c1-2 2-3 4-4h0l2-3c0-1 5-2 6-2-1-2 0-3 0-4z" class="q"></path><path d="M444 393c2 0 4-1 5-1-2 2-4 3-5 6l-5 1 1-2c1-2 2-3 4-4h0z" class="J"></path><path d="M452 388h3l1 1c-1 2-4 3-7 3-1 0-3 1-5 1l2-3c0-1 5-2 6-2z" class="a"></path><defs><linearGradient id="N" x1="453.403" y1="878.723" x2="451.616" y2="865.914" xlink:href="#B"><stop offset="0" stop-color="#414140"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#N)" d="M446 860l6 3h2 1v-1c1-1 2 0 3 0 1 1 2 1 3 1 2 1 4 2 5 3 2 1 4 1 6 1h0c2 2 3 2 4 5h-1s-1 1-2 1l1 1c-2 4-3 11-6 14h-8v-1c-2-1-5-3-7-4-3-1-5 0-7-2 1-2 1-2 2-3h0c-1-1-1-1-2-1s-1 0-2-1h0-1c-2 0-3-1-4-2l1-1h-3v-1c0-1-1-1-1-2l3-1 2-2c1 0 1-1 1-1l1-1v1h0l1 1v-2h1c0-2 0-4 1-5z"></path><path d="M459 882v-1c2 0 2 0 3-1-1-1-1 0-3-1-1 0-2-1-2-2 1-1 1-2 2-3h2c2 0 2 1 3 2s0 1 0 2l-2 2c0 1-1 1-2 2h-1z" class="c"></path><path d="M454 863h1v-1c1-1 2 0 3 0 1 1 2 1 3 1 2 1 4 2 5 3 2 1 4 1 6 1h0c2 2 3 2 4 5h-1l-14-6c-1-1-2-2-4-2-1 0-2 0-3-1z" class="E"></path><path d="M446 860l6 3v1h1l-1 1-1-1h-1l-1 1c1 1 2 2 1 3-1 0-2 1-3 1l-2 2h-2 0-2-3l-1 1c0-1-1-1-1-2l3-1 2-2c1 0 1-1 1-1l1-1v1h0l1 1v-2h1c0-2 0-4 1-5z" class="D"></path><path d="M473 873l1 1c-2 4-3 11-6 14h-8v-1c-2-1-5-3-7-4-3-1-5 0-7-2 1-2 1-2 2-3l1 1c2 0 3 0 4 1h3l-1 1h2l2 1h1c1-1 2-1 2-2l2-2 2 1c1-1 1-2 2-2l-2-3 3 1 4-2z" class="H"></path><path d="M464 878l2 1c1-1 1-2 2-2l-2-3 3 1-1 1 1 1h0c-1 4-1 7-5 9h-5c-3-1-5-4-7-5-1 0-2-1-3-2 2 0 3 0 4 1h3l-1 1h2l2 1h1c1-1 2-1 2-2l2-2z" class="C"></path><defs><linearGradient id="O" x1="472.128" y1="376.14" x2="482.594" y2="392.633" xlink:href="#B"><stop offset="0" stop-color="#4f4e4d"></stop><stop offset="1" stop-color="#6c6a62"></stop></linearGradient></defs><path fill="url(#O)" d="M480 367c1 5 1 11 2 16 0 3 0 6 1 9h0l2-2c0 1 0 2-1 3 0 2-1 4-2 6s-4 4-6 6l-1 1c-1 0-1 0-2 1h-1c-1 0-2 1-3 1-1 1-3 1-4 2h-3-2c-1-1-1-1-1-3h1c-1-1-1-2-2-3 1-1 0-2 0-4h4l2 2v-7c-1-4 0-9 0-14 0-1 0-3 1-5 0-1 0-2 1-3 0-1-1 0 0-1l1 1 2-2 3 2v1l2-3 1 1 5-5z"></path><path d="M474 371l1 1-2 2v1c2 2 1 7 1 10 0 5 0 11 1 16v3l-2 2h-1v-3c0-9-1-20 0-29l2-3z" class="B"></path><path d="M465 376c0-1 0-2 1-3 0-1-1 0 0-1l1 1 2-2 3 2v1c-1 9 0 20 0 29v3h1v1h-1c-1 0-2 1-3 1-1 1-3 1-4 2h-3-2c-1-1-1-1-1-3h1c-1-1-1-2-2-3 1-1 0-2 0-4h4l2 2v-7c-1-4 0-9 0-14 0-1 0-3 1-5z" class="b"></path><path d="M464 395c0 2 1 4 1 6v2c1-1 1 0 2-1l1 1h2 2v3h1v1h-1c-1 0-2 1-3 1-1 1-3 1-4 2h-3-2c-1-1-1-1-1-3h1c-1-1-1-2-2-3 1-1 0-2 0-4h4l2 2v-7z" class="M"></path><path d="M464 406v2c-1 0-1 1-2 2h-2c-1-1-1-1-1-3h1c1-1 3-1 4-1z" class="b"></path><path d="M458 400h4l2 2v4c-1 0-3 0-4 1-1-1-1-2-2-3 1-1 0-2 0-4z" class="D"></path><path d="M618 862c2-1 2-1 3-2l1-1v2h3v-1c1 1 2 1 2 2h0c1 1 1 2 2 2h1c0 1 1 1 1 1l1-1h0c1 1 1 1 1 3h1c1 0 2 0 3 1s1 2 1 3l1 1h1 2l2 1h1c5 3 7 4 10 8-1 2-1 3-3 3-6 1-15 0-21-2-2-1-4-2-6-2h-3c-2 1-5 1-7 0h-1c-1-1-4-3-6-4l-2-3-2-1c2-2 3-4 5-6h1 2 0c3-1 2-2 5-2l1 1v1h1l-1-4z" class="C"></path><g class="p"><path d="M625 860c1 1 2 1 2 2h0c1 1 1 2 2 2h1c0 1 1 1 1 1l1-1h0c1 1 1 1 1 3h1c0 1 1 1 1 2 0 0-1 0-1 1s1 2 1 3l-1 1c0 1 0 1-1 2v1l-2 2c-2-1-3 0-4-2l-1-2c0-1 0-1 1-2h1c0-1 0-2-1-3h2v-1h-1v-3l-1-1h-1c0-1 0-1-1-2v-2-1z"></path><path d="M634 867c1 0 2 0 3 1s1 2 1 3l1 1h1c1 1 2 1 3 2l1 2c2 0 3 0 4 2 0 0 1 0 1 1s1 1 0 3c1 0 2-1 3 0v1h-3c0-1 0-1-1-1-4 0-8 0-12-1-1-1-1-1-1-2l-2-2v-1c1-1 1-1 1-2l1-1c0-1-1-2-1-3s1-1 1-1c0-1-1-1-1-2z"></path></g><path d="M644 876c2 0 3 0 4 2 0 0 1 0 1 1s1 1 0 3h-2 0c0-1 1-1 1-1v-1h-5-3l-1-1 1-1h2l2-2z" class="o"></path><path d="M634 867c1 0 2 0 3 1s1 2 1 3l1 1h1c1 1 2 1 3 2-1 1-1 2-2 3l-3-3-3 5-2-2v-1c1-1 1-1 1-2l1-1c0-1-1-2-1-3s1-1 1-1c0-1-1-1-1-2z" class="O"></path><path d="M618 862c2-1 2-1 3-2l1-1v2h3v2c1 1 1 1 1 2h1l1 1v3h1v1h-2-1l-1 2c0 2-2 3-3 4-2 1-3 2-4 2-1-1-1-1-1-2v-1h-1v2c1 1 0 1 1 2-1 1-1 1-2 1h-1c-1-1-4-3-6-4l-2-3-2-1c2-2 3-4 5-6h1 2 0c3-1 2-2 5-2l1 1v1h1l-1-4z" class="r"></path><path d="M617 876c1-1 1 0 1-1h1c-1-2-1-1-2-2h1 0c2-1 2-1 5-1v-1c1 1 1 1 2 1 0 2-2 3-3 4-2 1-3 2-4 2-1-1-1-1-1-2z" class="i"></path><path d="M610 866h2 0c0 2-1 3-1 6h0c1 0 2 0 3-1l-2 2h-1l3 3 1 1h1c1 1 0 1 1 2-1 1-1 1-2 1h-1c-1-1-4-3-6-4l-2-3-2-1c2-2 3-4 5-6h1z" class="S"></path><path d="M606 873l1-2h1l2 1c0 2 0 3-2 4l-2-3zm-27-144l2 2c2-1 3-3 4-4 0 3 0 7 1 11l-5-1 1 2h6l-1 1c-1 0-1 0-2 1l3 1 2-3h1v3 3h0c0 3-1 5-1 7l-1 12v5c0 2 0 3-2 5-3 0-8-3-11-5h-2v1h0l-1-1v-3h-1l-1-34c3-1 5-2 8-3z" class="R"></path><path d="M585 741c-1 1-1 1-2 1-2 0-8-1-9-2v-3l1-1c2 0 4 1 6 1l1 2h6l-1 1c-1 0-1 0-2 1z" class="C"></path><path d="M588 742l2-3h1v3 3h0l-3 4c-4 3-9 4-14 5 0-2 4-1 5-3-2 1-4 1-6 1v-1c3 0 5 0 7-2-2 1-4 1-6 1v-1c3 0 6-1 9-2 0-1 0-1 2-1h0c2-1 2-2 3-3v-1z" class="K"></path><path d="M573 766v1l1 1c1 0 1 0 2-1 1 0 1 1 2 1v-2-2c0-1 1-1 1-2l2 1h0c1 0 1 0 2 1v-2h-1l-2-1 1-1h1l1 1 1-1c0 1 1 1 1 1v-1-2h2c2 1 1 3 1 4s1 1 1 2v5c0 2 0 3-2 5-3 0-8-3-11-5h-2v1h0l-1-1v-3z" class="d"></path><defs><linearGradient id="P" x1="691.4" y1="210.566" x2="686.81" y2="218.431" xlink:href="#B"><stop offset="0" stop-color="#6e6b66"></stop><stop offset="1" stop-color="#8a7f72"></stop></linearGradient></defs><path fill="url(#P)" d="M665 188l12 6 17 7 3 2 7 4 1 1c1 2 2 3 3 4v2h-2l-1-1-2 1v1l-2 2v1l-3 1h-4c-1-1-1 0-2-1l-1 1c-1 0-1 0-2-1l-2 2h-1c0 1 0 1-1 2v-2l-1-1h1v-1c-1 0-2 0-3-1-1 0-1 0-2-1s-2-2-2-3l-2-3v-2c-1 0-1-1-1-2-1-1-1-1-1-2h-5l-1-2 1-1-1-1-1 1-2-1 1-2-1-1h-1c-1 0-1 0-2-1l-1-1h0c-1 0-2-1-3-1h0c1-2 1-3 1-6 1 1 1 1 2 1l1 1h1l2-2z"></path><path d="M695 210c0 1 1 2 2 3l1-1 1 1h-1c-1 2-1 2-1 4l-3-3c0-2 0-3 1-4z" class="Z"></path><path d="M668 200l1-1c1 1 1 2 2 3h4c1 0 0 1 1 1 1-1 1-1 2-1l1 2c0 1-1 1-2 2h-1v1c1 1 2 1 4 1v1h-1l-1 1 2 2 3-2v1c-1 0-2 1-3 3l1 1-1 1c-1-1-2-2-2-3l-2-3v-2c-1 0-1-1-1-2-1-1-1-1-1-2h-5l-1-2 1-1-1-1z" class="Q"></path><path d="M659 188c1 1 1 1 2 1l1 1v2l3 1 1 1c9 6 22 6 29 14l3 3c1 0 1 0 1 1v1l-1-1-1 1c-1-1-2-2-2-3l-1-1-2 2c-1-1-1-2-1-3-3-2-7-3-11-4v1 1h-3c1-1 2-1 2-2l-1-2c-1 0-1 0-2 1-1 0 0-1-1-1h-4c-1-1-1-2-2-3l-1 1-1 1-2-1 1-2-1-1h-1c-1 0-1 0-2-1l-1-1h0c-1 0-2-1-3-1h0c1-2 1-3 1-6z" class="D"></path><path d="M659 188c1 1 1 1 2 1l1 1v2l3 1 1 1c9 6 22 6 29 14h-3v-1c-1-2-10-4-12-6l-7-2c-2-1-5-3-8-4l-1 1c-1 0-1-1-2-1v-1l-1 1c-1 0-2-1-3-1h0c1-2 1-3 1-6z" class="O"></path><path d="M665 188l12 6 17 7 3 2 7 4 1 1c1 2 2 3 3 4v2h-2l-1-1-2 1v1l-2 2v1l-3 1-1-2c0-2 0-2 1-4h1v-1c0-1 0-1-1-1l-3-3c-7-8-20-8-29-14l-1-1-3-1v-2h1l2-2z" class="F"></path><path d="M697 217c0-2 0-2 1-4l1 4h2v1l-3 1-1-2z" class="K"></path><path d="M697 203l7 4 1 1c1 2 2 3 3 4v2h-2l-1-1c-1-1-3-3-5-4-3-2-5-2-7-5 1 1 3 1 4 2v-3zm-32-15l12 6h-3c-1-1-2-1-3-1 1 1 3 1 4 2 3 1 5 2 7 3 3 1 6 3 8 5-4-1-8-3-12-5s-7-3-11-5l-4-3 2-2z" class="G"></path><path d="M690 203c-2-2-5-4-8-5-2-1-4-2-7-3-1-1-3-1-4-2 1 0 2 0 3 1h3l17 7 3 2v3c-1-1-3-1-4-2-1 0-2-1-3-1z" class="B"></path><path d="M570 304h0c1 1 3 3 3 5v2h0v5c3 0 6 0 9 1 2 0 4-1 6 0h-3l-1 2v20l-3-1h-3l-1-1h-6 0c0 2 0 5-1 7v5c-2 1-3 1-5 1l-1-1-1 1c0 2 0 6 1 7h1c-1 1-2 0-2 1l-2 3v-1c1-3 1-5 1-9v-1c-1-10-1-21 0-31v-7c0-1 1-2 2-2l2-4c1 0 2-1 4-2z" class="H"></path><path d="M570 304c0 2 1 3 0 5-1 0-2 0-3 1h-3l2-4c1 0 2-1 4-2z" class="R"></path><path d="M568 328l1 1c1 0 1-1 2-1v1 4c1 0 1 0 1 1l-1 3h0c0 2 0 5-1 7v5c-2 1-3 1-5 1l-1-1h1v-3c0-1 0 0 1-1h0l-1-1c0-2 0-2 1-3-1-1 0-1-1-1h-1c1-1 1-2 0-3h0l1-1-1-2v-1c1 1 1 1 2 1l-2-2 1-1h-1c0-2 0-2 1-3h3z" class="B"></path><path d="M573 321c0-1 0-2 1-3h2c1 1 1 1 2 1h1c1 0 1 1 3 2h1v-2h1v20l-3-1h-3l-1-1h-6l1-3c0-1 0-1-1-1v-4-1c-1 0-1 1-2 1l-1-1v-3c-2 0-2 0-3-1 0-1-1-2 1-3v1c2 0 2 0 3-1h4z" class="F"></path><path d="M578 328c0-1 1-1 1-1 1 0 1 0 2-1h1v1 1c-1 1-1 1-1 2-1 0-2-1-2-2h-1z" class="B"></path><path d="M578 332c1 0 1 1 1 2h1c1-1 1 0 1-1l1 1-2 3 1 1h-3c-1-3-1-4 0-6z" class="E"></path><path d="M578 328h1c-1 1-1 2-1 2v1 1c-1 2-1 3 0 6l-1-1-1-1s0-1-1-1c-1-1 0-1-1-2h2c-1-1-1-1-2-1h-1v-2-2h5z" class="O"></path><path d="M568 328v-3c-2 0-2 0-3-1 0-1-1-2 1-3v1c2 0 2 0 3-1h4l1 1h0 4l2 1 1-1h1l1 2-1 1h-1v-1h0v-1c-1 1-1 1-2 1-2 0-3-1-5 0-1 1-1 3-1 5v1 2h1c1 0 1 0 2 1h-2c1 1 0 1 1 2 1 0 1 1 1 1l1 1h-6l1-3c0-1 0-1-1-1v-4-1c-1 0-1 1-2 1l-1-1z" class="W"></path><path d="M595 848h0v-1c-1-4 5-7 8-9v1c2 1 3 3 4 5h0l2 4 1 2 1 2-1 1h0l-1 3v4c0 2 1 2 1 3 1 1 0 3 0 3h-1c-2 2-3 4-5 6-3 2-5 4-9 5v1l-8 2h-5c-4-1-7-2-10-3h-1v-3-2l3-3c2 0 4 0 5-1h1 2l1 1h1 0l1-2c3-1 5-1 7-4 2-1 4-3 4-6v-1-2h0c0-1 1-1 0-2h0c-1-1-1-2 0-3l-1-1z" class="Q"></path><path d="M583 877h1c1-1 2-1 3-2h1v-4h1c1 1 2 3 3 3h1c0 1 0 3 1 3h1v1l-8 2c-2 0-3 0-4-1v-2zm19-20l2-1c2-1 2-1 5 0v4c0 2 1 2 1 3 1 1 0 3 0 3h-1-3v2c-1 0-1 1-2 2l-1-1v-3h-1 1v-3c-1 0-2-1-2-2-1-2 0-3 1-4z" class="M"></path><path d="M584 869h2v-1c2 0 3 0 4-1s2-1 3-1l2 1v-1c-1 0-1-1-1-2h1c1-2 3-3 3-4v-2l2-1v1c-1 1-1 1-1 2-1 1-1 2-2 3v1c0 1-1 1-1 2l-1 1-1 1v1h0l-2-2h-1c-1 1-1 1-3 2v1h-1c-1 1-2 1-3 2v1c0 1-1 3-1 4v2c1 1 2 1 4 1h-5c-4-1-7-2-10-3h-1v-3-2l3-3c2 0 4 0 5-1h1 2l1 1h1 0z" class="b"></path><path d="M579 868h1 2l1 1h1l-1 2h0c0-1-1-1-1-2h-3c-2 0-6 2-8 3l3-3c2 0 4 0 5-1z" class="k"></path><path d="M572 877c0-1 1-1 1-3v3c1-1 0-2 2-3 0 0 1 0 2-1l1 1c1-2 1-2 3-3h1c0 2 1 7 0 9-4-1-7-2-10-3z" class="Q"></path><path d="M595 848h0v-1c-1-4 5-7 8-9v1c2 1 3 3 4 5h0l2 4 1 2 1 2-1 1h0l-1 3c-3-1-3-1-5 0l-2 1h-2l-2 1v2c0 1-2 2-3 4h-1c0 1 0 2 1 2v1l-2-1c-1 0-2 0-3 1s-2 1-4 1v1h-2l1-2c3-1 5-1 7-4 2-1 4-3 4-6v-1-2h0c0-1 1-1 0-2h0c-1-1-1-2 0-3l-1-1z" class="Z"></path><path d="M599 848c1 0 2 0 4-1 0-1 1-1 2-1v1l-1 1c1 1 3 0 5 0l1 2 1 2-1 1h0-2v-2 1l1-1c0-1-1-2-1-2-2 0-1 0-2 1h-2c-1 0-2 2-3 3h-3l1 1h2v1c-1 0-2 0-3 1h-2v-2h0c0-1 1-1 0-2h0c-1-1-1-2 0-3s2-1 3-1z" class="D"></path><path d="M595 848h0v-1c-1-4 5-7 8-9v1c2 1 3 3 4 5h0l2 4c-2 0-4 1-5 0l1-1v-1c-1 0-2 0-2 1-2 1-3 1-4 1s-2 0-3 1l-1-1z" class="l"></path><path d="M603 839c2 1 3 3 4 5h0c-2 0-2-1-3 0h-2v-1c-1-1-1 0-3-1l2-1c1-1 2-1 2-2z" class="b"></path><path d="M595 848h0v-1c-1-4 5-7 8-9v1c0 1-1 1-2 2l-2 1c-1 1-1 2-2 3l1 1 1-1v1l-1 2h1c-1 0-2 0-3 1l-1-1z" class="Z"></path><path d="M558 365h0c3 0 4 0 6-2 1 0 1 0 2-1 1 0 2-1 3-1 1 1 1 3 1 4v1c1 1 1 1 3 1 1 0 2 0 4 1l2-2c0-1 1-1 2-1h1c1 0 5-1 7 0 1 0 2 0 3 1h2c1 0 1 0 2 1h2 0c3 1 4 2 7 3v1 7c3 2 8 4 10 7 1 2 1 4 0 6v2c-3 0-6-4-9-6-4-3-9-4-14-6l-12-3-16-2c-2 0-4 0-5-1-1 0-1 0-2-1 1-2 4-3 5-6h-2v-1h-2v-2z" class="I"></path><path d="M579 370l-1-2 1-1 1 1v1l2-1h1 4c4 0 13 2 15 5l2 2v-5h1v1 7c3 2 8 4 10 7v1h-1c-1-2-3-3-5-5l-6-3-3-1 1-1c-4-1-9-4-13-5h0c-3-1-6-1-9-1z" class="K"></path><path d="M573 369c2 0 5 0 6 1 3 0 6 0 9 1h0c4 1 9 4 13 5l-1 1c-2-1-5-2-7-2l5 2v1c-1 1-1 1-2 1-2 1-3 1-4 2l-12-3h2 1c1 0 2 0 3-1v-2c-2 0-5-1-6-2v-1h-1c-1 0-1 0-2-1h-1v-1h-2-1l-1-1h0 1z" class="Z"></path><path d="M592 381c1-1 2-1 4-2 1 0 1 0 2-1v-1l-5-2c2 0 5 1 7 2l3 1 6 3c2 2 4 3 5 5h1v-1c1 2 1 4 0 6v2c-3 0-6-4-9-6-4-3-9-4-14-6z" class="c"></path><path d="M603 378l6 3c-1 2-1 3-1 4l-1 1c-1-1-2-1-3-3s-2-2-1-5z" class="G"></path><path d="M609 381c2 2 4 3 5 5h1v-1c1 2 1 4 0 6l-1-1c-2-1-3-4-6-5 0-1 0-2 1-4z" class="B"></path><path d="M560 367c2 0 7 0 9-1h0c1 1 1 1 1 2h3v1h-1 0l1 1h1 2v1h1c1 1 1 1 2 1h1v1c1 1 4 2 6 2v2c-1 1-2 1-3 1h-1-2l-16-2c-2 0-4 0-5-1-1 0-1 0-2-1 1-2 4-3 5-6h-2v-1z" class="k"></path><path d="M565 371c3-1 6-1 10 0v2h0c2 0 4 0 6 2l-1 1h-5v-1c-2 0-4 0-5-1l-1-1h-2-3l1-2z" class="b"></path><path d="M560 367c2 0 7 0 9-1h0c1 1 1 1 1 2h3v1h-1 0l1 1h1 2v1h-1c-4-1-7-1-10 0-1 1-2 1-3 2 1 0 1 1 2 1-1 0-1 1-2 0v1h2v1c-2 0-4 0-5-1-1 0-1 0-2-1 1-2 4-3 5-6h-2v-1z" class="K"></path><defs><linearGradient id="Q" x1="442.779" y1="875.423" x2="432.047" y2="897.345" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#4c4d4d"></stop></linearGradient></defs><path fill="url(#Q)" d="M426 867h1c1-1 1 0 2-1h0v2c3 2 4 1 7 2 0 1 1 1 1 2v1h3l-1 1c1 1 2 2 4 2h1 0c1 1 1 1 2 1s1 0 2 1h0c-1 1-1 1-2 3 2 2 4 1 7 2 2 1 5 3 7 4l-1 1c-1 1-2 1-3 1h-1v1h4v1h-39l-10 1v-1c-1 0-1-1-3-1h-3c0-2 1-2 1-3h2 1c0-1 1-1 1-2h0l-1-1c0-1 0-1 1-1l-2-2h2 0c1 0 2 0 3-1l1 1v-1h1c1-1 1-2 3-3h0l-1-2h1 3c1 1 2 0 3 0l1 1c1 0 1 0 1-1 1-1 1 0 1-1l1-2c-1-1-1-1-1-2h-1c1-2 1-2 1-3z"></path><path d="M423 882l1-1 2-2 1 1v1c-1 1-1 2-1 3-1 1-1 1-2 1 0-1 0-2-1-3z" class="Y"></path><path d="M423 882c1 1 1 2 1 3-2 1-4 2-5 4 0-1-1-1 0-1-1 0-3 1-4 1l4 1h-8c1-1 2-2 3-2v-1-1l1 1v1h1l2-1 2-2c1-1 1-2 3-3h0z" class="D"></path><path d="M438 875l2 1c-1 1 0 2-2 3-2 2-7 2-10 1h-1l-1-1c1 0 1-1 2-1 4 0 7-2 10-3z" class="H"></path><path d="M409 885c2-1 2-1 4 0l1 1v1 1c-1 0-2 1-3 2h8c10 0 19-1 28 1h-18-9l-10 1v-1c-1 0-1-1-3-1h-3c0-2 1-2 1-3h2 1c0-1 1-1 1-2h0z" class="l"></path><path d="M409 885c2-1 2-1 4 0l1 1-2 2h-3v-3h0z" class="L"></path><path d="M426 867h1c1-1 1 0 2-1h0v2c3 2 4 1 7 2 0 1 1 1 1 2v1h3l-1 1-1 1c-3 1-6 3-10 3-1 0-1 1-2 1l-2 2-1 1h0c-2 1-2 2-3 3l-2 2-2 1h-1v-1l-1-1v1-1l-1-1c-2-1-2-1-4 0l-1-1c0-1 0-1 1-1l-2-2h2 0c1 0 2 0 3-1l1 1v-1h1c1-1 1-2 3-3h0l-1-2h1 3c1 1 2 0 3 0l1 1c1 0 1 0 1-1 1-1 1 0 1-1l1-2c-1-1-1-1-1-2h-1c1-2 1-2 1-3z" class="M"></path><path d="M409 881c1 0 2 0 3-1l1 1h3l1 1c-1 1-2 2-3 2v2 1-1l-1-1c-2-1-2-1-4 0l-1-1c0-1 0-1 1-1l-2-2h2 0z" class="V"></path><path d="M415 887l6-7c3-2 4-2 7-2-1 0-1 1-2 1l-2 2-1 1h0c-2 1-2 2-3 3l-2 2-2 1h-1v-1z" class="E"></path><path d="M428 878c0-1 1-2 1-2v-1c1-2 2 0 3-2-1-1-1 1-3 0l1-1c0-2 0-2-1-3v-1c3 2 4 1 7 2 0 1 1 1 1 2v1h3l-1 1-1 1c-3 1-6 3-10 3z" class="l"></path><path d="M520 167c2 1 4 4 5 6h1l3 3c2 3 4 5 6 8 6 9 8 21 12 31 1 3 3 5 3 9h1v6l1 1 1 1-2 1c1 1 0 1 1 1l-1 1c-1-1-2-2-4-2h0l-1-1c0-1-1-1-1-2l-1 3-2-2c0-1-1-2-1-4 0-3-3-6-4-9-1-4-2-8-3-11-2-8-7-16-11-22l-2-1-2-3-2-2-3-1c-1-1-2-1-3-1-1 1-2 1-3 2l-2-2c1-2 1-1 2-1 0-1 0-1 1-2l-1-1v-1c0-1 0-1 1-2h0v-1c0 2 1 3 1 4h1v-3h1l1 4h1c0-2 0-5 1-7 0 2 0 5 1 6 1 0 1-1 1-2v-1l2-1h1l-1-1 1-1z" class="d"></path><path d="M519 172h0v-1l1-1c2 3 3 6 5 9 4 7 9 14 12 22l-1 2c-3-7-6-13-9-19-3-4-6-7-8-12z" class="K"></path><path d="M537 201v1c1 1 1 2 1 3 2 6 4 12 7 18v1c2 2 4 5 6 7v-1l1 1 1 1-2 1c1 1 0 1 1 1l-1 1c-1-1-2-2-4-2h0l-10-27c-1-1-1-2-1-3l1-2z" class="Y"></path><defs><linearGradient id="R" x1="533.6" y1="197.965" x2="526.412" y2="202.231" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#33373a"></stop></linearGradient></defs><path fill="url(#R)" d="M508 173v-1c0-1 0-1 1-2h0v-1c0 2 1 3 1 4h1v-3h1l1 4h1c0-2 0-5 1-7 0 2 0 5 1 6 1 0 1-1 1-2l2 1c2 5 5 8 8 12 3 6 6 12 9 19 0 1 0 2 1 3l10 27-1-1c0-1-1-1-1-2l-1 3-2-2c0-1-1-2-1-4 0-3-3-6-4-9-1-4-2-8-3-11-2-8-7-16-11-22l-2-1-2-3-2-2-3-1c-1-1-2-1-3-1-1 1-2 1-3 2l-2-2c1-2 1-1 2-1 0-1 0-1 1-2l-1-1z"></path><path d="M422 247c1 1 2 1 4 2h0v-1l2 1c4 2 6 4 8 6 1-1 2-1 2-2h1 0c0 1 0 2 1 3-2 3-4 6-7 8h-1c-1 1-2 2-3 2v23c1 6 0 12 2 18v6 1h7l2 2h0l-3 1h-5l1 13c-2 1-3 2-4 3-1 3-2 5-1 8h-1 0c0-2 0-3-1-4-3-3-3-5-4-9-1-1-1-2-1-3h0c1-2 2-47 1-51h-2l2-1c1-9 0-18 0-26z" class="g"></path><path d="M426 337c2-2 1-8 1-11v-38-14-7h0l1-1h1v23c1 6 0 12 2 18v6 1h7l2 2h0l-3 1h-5l1 13c-2 1-3 2-4 3-1 3-2 5-1 8h-1 0c0-2 0-3-1-4z" class="e"></path><path d="M429 289c1 6 0 12 2 18v6 1h7l2 2h0l-3 1h-5l1 13c-2 1-3 2-4 3-1-1-1-1-1-2 1-4 1-9 1-13v-16-13z" class="g"></path><path d="M431 591h5l-1 1c2 0 3 0 5 2v-1h3c1 1 0 1 1 1 2-1 6-1 8 0l1 1v2h1l1-1v2 3c1 1 1 1 1 2-2 0-3 1-4 2v2c-1 2-1 4-1 7h2l-1 8c-1 0-6-1-7 0h1l-1 1v1 1h0-5-10v11l-1 1v-8h-1v5l-1 1c0-2 0-2-1-3 0 1 0 2-1 3l-1-1 2-32h3 0l2-2v-3-6z" class="o"></path><path d="M431 600v1c1 0 1 1 2 1h0l1-1v2 4h1v8 5l1 1 1-2 1 1v1l-6 1c-1 0-1 0-2 1 0 0 0 1-1 2v-1c-1-2 0-20 0-22l2-2z" class="I"></path><path d="M432 622l-2-2c1-2 2-6 2-9 0 3 1 6 2 8l1 1 1 1 1-2 1 1v1l-6 1z" class="R"></path><path d="M435 615h0c1-1 2-1 2-1v-1l1-1v-1-1l1 4 1-3c1 0 1 0 2 1l1 1v7l-1 1c1 1 2 1 3 1h1l-1 1v1 1h0-5-10-1c1-1 1-2 1-2 1-1 1-1 2-1l6-1v-1l-1-1-1 2-1-1v-5z" class="g"></path><path d="M435 615h0c1-1 2-1 2-1v-1l1-1v-1-1l1 4v6l2 1h-3v-1l-1-1-1 2-1-1v-5z" class="O"></path><path d="M431 591h5l-1 1c2 0 3 0 5 2-1 1-1 2-1 3v6c0 2 0 5-1 7v1 1l-1 1v1s-1 0-2 1h0v-8h-1v-4-2l-1 1h0c-1 0-1-1-2-1v-1-3-6z" class="L"></path><path d="M437 604c0-3 0-4 2-7v6l-2 1z" class="C"></path><path d="M435 601h1 0v10h1v-7l2-1c0 2 0 5-1 7v1 1l-1 1v1s-1 0-2 1h0v-8-6z" class="t"></path><path d="M431 591h5l-1 1c0 2-1 7 0 9v6h-1v-4-2l-1 1h0c-1 0-1-1-2-1v-1-3-6z" class="R"></path><path d="M440 593h3c1 1 0 1 1 1 2-1 6-1 8 0l1 1v2h1l1-1v2 3c1 1 1 1 1 2-2 0-3 1-4 2v2c-1 2-1 4-1 7h2l-1 8c-1 0-6-1-7 0-1 0-2 0-3-1l1-1v-7l-1-1c-1-1-1-1-2-1l-1 3-1-4c1-2 1-5 1-7v-6c0-1 0-2 1-3v-1z" class="K"></path><path d="M445 619c0-3 0-7 1-11 1 0 2 0 3 1-3 3 1 9-4 10z" class="O"></path><path d="M440 611c-1-4-1-10 0-14h3v2 14l-1-1c-1-1-1-1-2-1z" class="J"></path><path d="M440 593h3c1 1 0 1 1 1 2-1 6-1 8 0l1 1v2h1l1-1v2 3c1 1 1 1 1 2-2 0-3 1-4 2v2c-1 2-1 4-1 7h2l-1 8c-1 0-6-1-7 0-1 0-2 0-3-1l1-1 2-1c5-1 1-7 4-10h1v-1l-1-1c-1-1 0-7 0-9h-2c1-1 1-1 2-1l-1-1h-2s-3 1-3 0c0 0 0-1-1-1l-2-2z" class="C"></path><path d="M450 616h1v5h-2-1c0-2 1-3 2-5z" class="W"></path><path d="M426 688c0-3-1-6 0-9l1 2h2 3v7 22c0 3-1 8 0 11v1l5 5h-1l1 2v1 1c1 1 1 1 2 1 1 3 5 3 7 5l2 1 1 2v4 13 8c-1 0-1 0-2-1l-1 1v2c1 0 1-1 2-2l1 2-1 1c-1 1-1 2 0 3v1c1 1 1 1 1 2l1 1h-1l-1 1h-1v-3h-1c1-2 1-2 1-3-1 0-1 2-1 3v1c0 2 0 1-1 2s-1 2-1 3c-1 2-1 2-3 2h0l1-1c1-1-2-2-2-4l-2-2c2 0 3 0 5-1l-2-2-1 1h-1c-4 0-6 1-9 2v-4-18-3-5-12l1-42c0-2 0-4-1-5l-3-1v1l-1 3z" class="E"></path><path d="M443 773c0-1 1-2 1-3h-2l1-1h1l1 2c0 1 0 1-1 2h2v1c0 2 0 1-1 2s-1 2-1 3c-1 2-1 2-3 2h0l1-1c1-1-2-2-2-4l-2-2c2 0 3 0 5-1z" class="W"></path><path d="M448 738l1 2v4l-3 2-2-2h0 1l-2-1h-7-1 0c1-1 0-1 1-1h1l1-1h9c0-1 0-2 1-3z" class="I"></path><path d="M426 688c0-3-1-6 0-9l1 2h2 3v7 22c0 3-1 8 0 11v1l5 5h-1l-1 1c0 1 0 8-1 9h-1c-1-1 0-3 0-4 0-3 1-8-1-10v1c0 2 0 3-1 5v-10c0-10 1-20 0-29 0-2 0-4-1-5l-3-1v1l-1 3z" class="S"></path><path d="M433 737h1c1-1 1-8 1-9l1-1 1 2v1 1c1 1 1 1 2 1 1 3 5 3 7 5l2 1c-1 1-1 2-1 3h-9c0-1 1-1 2-1h0l-1-1c-2-1-4 0-6-1v-1z" class="F"></path><path d="M437 729v1 1c1 1 1 1 2 1 1 3 5 3 7 5-2 0-5 0-7-1h0-2-1c1-3 1-5 1-7z" class="W"></path><path d="M444 761l-1 1h-1-1-1c0-1 0-1-1-2h0c0 1 0 2-1 3h0v-1-1-1h-1-1v4 1h0c0 1 0 1 1 2v2c-1 1-2 1-3 1-2-1-1-4-1-5l-1-14 8 3c1 1 2 1 4 1 0 1-1 2 0 3v3z" class="H"></path><path d="M443 743l2 1h-1 0l2 2 3-2v13 8c-1 0-1 0-2-1l-1 1-2-1h-1v-1l1-1v-1-3c-1-1 0-2 0-3-2 0-3 0-4-1-2-3-4-2-7-4-1-1-2-3-3-5 2 2 4 4 6 5l1 1c1 0 1 0 2 1h1l4 1c1 1 2 1 4 1v-1c-1-2-2-1-4-2 2 0 3 1 4 0-2-1-3-2-5-2-1 0-2-1-3-1l-2-1c-1 0 0 0 0-1 0 0 1-2 2-2 1-1 2 1 3-1z" class="S"></path><path d="M444 755c1 0 1-1 3 0v4h-1l-1 1 1 1h1l1 3h-1 0l-1 1-2-1h-1v-1l1-1v-1-3c-1-1 0-2 0-3zm-1-12l2 1h-1 0l2 2 1 1v1c-1 0-2 0-3-1h-2 0l-2 1-2-1c-1 0 0 0 0-1 0 0 1-2 2-2 1-1 2 1 3-1z" class="R"></path><path d="M804 239c0 1 0 1 1 2 2 4-1 10 1 14l1 1h0l1-2 1 12c2 0 4 0 7 1h-7l2 33-27-36c-5-5-10-11-16-15l-2 2h-1l2-2c-1-2-2-2-3-3l-8-5c1 0 2 0 3 1l1-1 2 1c1 1 2 1 2 1l2 2h1l1 1h2c1 1 2 4 4 4 1 0 1 0 2 1 2 1 4 2 7 2h3c3 1 7 0 10-2 5-3 6-7 7-12h1z" class="g"></path><path d="M804 239c0 1 0 1 1 2l-1 1v5c-3 4-5 6-9 8-2 1-3 2-4 3h-1c-3-1-5 1-8-1-1-1-1-1-2-1l-1-1h-1c-3-1-5-4-8-6-3-3-7-5-11-7l1-1 2 1c1 1 2 1 2 1l2 2h1l1 1h2c1 1 2 4 4 4 1 0 1 0 2 1 2 1 4 2 7 2h3c3 1 7 0 10-2 5-3 6-7 7-12h1z" class="F"></path><path d="M446 773c0-1 0-3 1-3 0 1 0 1-1 3h1v3h1 4v1h-2v1h3 1s0 1-1 2h1c1 0 1-1 2-2h0c0 1 1 2 0 2l-2 2v1l-1-1h-5v3l1 1h-2c0 1 0 1 1 2h5l-1 2h-2v1c1 3 1 6 1 8 1 3 0 5 0 7l-2 3v6 2l2 3c1 3 2 5 5 6l3 1h-7-4v3h-2-1l-4-1h-3-2s-1 1-2 1v-1l1-1-2-1v-1-1l3-3v-1h2l-1-3c-2 0-3-1-4-1-3-2-4-4-6-6-2-3 4-11 5-15 0-2 1-3 1-5v-1c0-2 0-2 1-3 1 0 1-1 1-2 1 0 1 0 2 1l2-1c0-1 0-2 1-2 1-1 1-1 1-2 2 0 2 0 3-2 0-1 0-2 1-3s1 0 1-2v-1z" class="H"></path><path d="M448 804c0 2 0 3 1 5v6 2l-2-4 1-9z" class="E"></path><path d="M448 788h5l-1 2h-2v1c1 3 1 6 1 8 1 3 0 5 0 7l-2 3c-1-2-1-3-1-5 0-5 1-11 0-16z" class="F"></path><path d="M440 818l3-3c0-1 0-1 1-2l1-1v-1h1l1 3v-1l2 4 2 3c1 3 2 5 5 6l3 1h-7-4v3h-2-1l-4-1h-3-2s-1 1-2 1v-1l1-1-2-1v-1-1l3-3v-1h2l-1-3h3 0z" class="I"></path><path d="M445 830c1-1 1-1 1-2h0c1-1 1-1 2-1v3h-2-1z" class="H"></path><path d="M440 818l3 1v-2h0c1 1 1 1 1 2s1 2 2 3c-2 3-3 5-5 7h-3-2s-1 1-2 1v-1l1-1-2-1v-1-1l3-3v-1h2l-1-3h3z" class="l"></path><path d="M435 785c1 0 1 0 2 1 2 0 2 1 4 2 1 1 3 2 3 5 0 1 0 2 1 4h0c-1 3-1 7-3 10v1l-2 5c-1 0-1 1-2 2l1 1-1 1 2 1h0-3c-2 0-3-1-4-1-3-2-4-4-6-6-2-3 4-11 5-15 0-2 1-3 1-5v-1c0-2 0-2 1-3 1 0 1-1 1-2z" class="B"></path><path d="M433 791v-1c0-2 0-2 1-3 2 2 4 4 5 4l-1 1h-1c-1-1-1 0-2 0l-2-1z" class="R"></path><path d="M422 845c2-2 4-6 6-9 1-2 4-5 4-8h-1v-1h2l2 1-1 1v1c1 0 2-1 2-1h2 3l4 1h1 2c3 0 6 0 9-1 1 2 0 3 1 4l2-2c1 1 1 1 1 2v1h0c-1 1-1 1-1 2v1l2-1c0 1 1 2 1 3-1 1-2 1-2 2h-1l-2 2c0 1 0 1 1 2h-1v2h-1c0 1-1 3-2 3h-1l-3 3h-1 0c-1 1-2 1-2 1v1l-1 1c-1 0-2 0-4 1h0l3 3c-1 1-1 3-1 5h-1v2l-1-1h0v-1l-1 1s0 1-1 1l-2 2-3 1c-3-1-4 0-7-2v-2h0c-1 1-1 0-2 1h-1v-1h0l-2-2 2-3h0c2-2 3-3 4-5l-1-1c-2 1-3 2-5 3l-2 3-2-2-2-2-2 1h0c-1-1-1-1-2-1l-1 1c-2 0-2-1-2-2h1l2 1c1-2 3-4 4-6l4-6z" class="P"></path><path d="M418 857h1c1-1 2-2 3-2 1 1 1 2 2 3l-2 3-2-2-2-2z" class="N"></path><path d="M422 855h1c1 0 2-1 3-1h0c2 0 3-1 6-1 2 1 3 1 5 0l2 2 1 1 2 2c2 1 2 1 3 3v4h-1v2l-1-1h0v-1l-1 1s0 1-1 1v-6c-1-1-2-1-2-2v-1h1l-1-1h-3l-1-1h0c-2-1-4-1-6-1-2 1-3 2-5 3-1-1-1-2-2-3z" class="O"></path><path d="M429 855c2 0 4 0 6 1h0l1 1h3l1 1h-1v1c0 1 1 1 2 2v6l-2 2-3 1c-3-1-4 0-7-2v-2h0c-1 1-1 0-2 1h-1v-1h0l-2-2 2-3h0c2-2 3-3 4-5l-1-1z" class="T"></path><path d="M433 860h3c1 1 1 1 1 2h-2l-1 1h1 1l-1 2h2v1l2 3-3 1c-3-1-4 0-7-2v-2c1 0 1 1 2 1v-1c0-1 1 0 0-1h0l2-2h0v-3z" class="U"></path><path d="M429 855c2 0 4 0 6 1h0v1c-1 0-1 1-1 1-1 1-1 1-1 2v3h0l-2 2h0c1 1 0 0 0 1v1c-1 0-1-1-2-1h0c-1 1-1 0-2 1h-1v-1h0l-2-2 2-3h0c2-2 3-3 4-5l-1-1z" class="f"></path><path d="M441 829l4 1h1 2c3 0 6 0 9-1 1 2 0 3 1 4l2-2c1 1 1 1 1 2v1h0c-1 1-1 1-1 2v1l2-1c0 1 1 2 1 3-1 1-2 1-2 2h-1l-2 2c0 1 0 1 1 2h-1v2h-1c0 1-1 3-2 3h-1l-3 3h-1 0c-1 1-2 1-2 1v1l-1 1c-1 0-2 0-4 1h0l3 3c-1 1-1 3-1 5v-4c-1-2-1-2-3-3l-2-2-1-1h2 0l1-1c-1-1-2-1-3-2h-1 1l1 1v-2l2 1 1-1-1-1-2-1-2-2h-1l1 1-2 1h-1c1 1 1 2 2 3-1-1-1-1-2-1 0-1-1-2-2-3h0v-2c0-2 0-3 1-5 0-1 1-2 1-3 1-4-2-2-3-4 2-2 4-3 6-5h3z" class="F"></path><path d="M441 829l4 1h1v1s0 1 1 1l-1 2c-2 0-2-1-3 0-1 0-2 1-2 1 0 1-1 2 0 3l3 2 2-2h1 0l-1 1 1 2c-2 1-4-1-5 2v1c-2 0-3-4-5-2-1 1 0 1-1 2 1 1 1 1 3 1l-1-1c2 0 3 2 5 3v1l-1-1h-2-1-1-1l1 1-2 1h-1c1 1 1 2 2 3-1-1-1-1-2-1 0-1-1-2-2-3h0v-2c0-2 0-3 1-5 0-1 1-2 1-3 1-4-2-2-3-4 2-2 4-3 6-5h3z" class="g"></path><defs><linearGradient id="S" x1="546.633" y1="153.577" x2="622.452" y2="155.437" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#5a5a5a"></stop></linearGradient></defs><path fill="url(#S)" d="M534 151c-1-1-1-2-1-3h90v3c1 0 2 0 4-2v2l3 4c-4 2-9 5-11 9-2-1-3-1-4 0-1 0-2-1-2-1h-3c-5-1-11 0-15-3-1-1-3-2-4-2s-2 1-3 2c-4 1-6 1-10 1-9 1-18 1-27 0l-2 1h-2c-3-1-4-3-6-5s-5-4-7-7v1z"></path><path d="M595 160h22c1 1 1 1 0 2s-1 0-2 0-2 1-2 1h-3c-5-1-11 0-15-3z" class="I"></path><path d="M623 151c1 0 2 0 4-2v2l3 4c-4 2-9 5-11 9-2-1-3-1-4 0-1 0-2-1-2-1s1-1 2-1 1 1 2 0 1-1 0-2c1-1 2-1 4-1l1-1v-1c0-2 1-2 1-4v-2z" class="B"></path><path d="M623 151c1 0 2 0 4-2v2l-1 3 1 1h0l-1 1h-1v-1c-1 0-2 1-3 2 0-2 1-2 1-4v-2z" class="K"></path><path d="M534 150h1l2 1h0l-1-1 1-1h10v1h-6v1c1 1 7 0 9 0-3 2-4 2-8 2h0c1 1 2 1 4 1 1 0 2 0 2-1l1 1c-1 0-2 1-3 1v1c1 0 2 0 3-1h1l-1 2h-2l-1 2c2 1 4 1 5 1-1 1-3 1-4 1h0 4 0l-2 1h-2c-3-1-4-3-6-5s-5-4-7-7z" class="O"></path><path d="M465 630l1-1c1 2 0 3 1 5 1 6-1 13 0 19h1v-5 1c1 1 1 1 1 2v3c1-1 0-3 0-4 0-5 0-10 1-15v26c1 3 0 7 1 10v-6c2 1 1 2 1 4l1 1v11 1 23c1-2 3-3 4-5l2-1v-9c1 13 0 26 0 39v34h0l-1 28c-1 0-1 0-2 1h0c1 1 0 2-1 3 0 1-1 1-2 2h1v1l-3 3c-1 1-1 1-2 1v-7c0-2 1-2 3-3 0-1 0-2-1-3h-2c2-6 1-14 1-20v-22c0-3 1-7 0-10-2-3-1-9-1-12l-1-14c0-5 1-11 0-16-1-1-1-2-1-3 0-2 0-3-1-5l-1-57z" class="C"></path><path d="M473 790v-1c2-2 1-6 1-8l1-16c0-1-1-1-1-2 1-1 1-2 1-2 2 2 1 13 1 16l-1 14-1 1c0-1-1-2-1-2z" class="N"></path><path d="M471 671v-6c2 1 1 2 1 4l1 1v11l-1 46c-1-4 0-9 0-13 0-2 0-4-1-5l-1 1 1-39z" class="I"></path><path d="M470 710l1-1c1 1 1 3 1 5 0 4-1 9 0 13l1 60v3s1 1 1 2c1 2 0 3-1 5h1v1l-3 3c-1 1-1 1-2 1v-7c0-2 1-2 3-3 0-1 0-2-1-3h-2c2-6 1-14 1-20v-22c0-3 1-7 0-10v-27z" class="H"></path><path d="M465 630l1-1c1 2 0 3 1 5 1 6-1 13 0 19h1v-5 1c1 1 1 1 1 2v3c1-1 0-3 0-4 0-5 0-10 1-15v26c1 3 0 7 1 10l-1 39v27c-2-3-1-9-1-12l-1-14c0-5 1-11 0-16-1-1-1-2-1-3 0-2 0-3-1-5l-1-57z" class="F"></path><defs><linearGradient id="T" x1="614.699" y1="508.622" x2="645.825" y2="501.827" xlink:href="#B"><stop offset="0" stop-color="#25282a"></stop><stop offset="1" stop-color="#616160"></stop></linearGradient></defs><path fill="url(#T)" d="M663 482l1 1-1 1h-1v2h0v2l-1 2v1c-1 2-1 2 0 3l1 1c0 1-1 3-2 5l1 1c0 1 0 1-1 1l-4 4h0v1c-1 1-2 1-2 3 0 1-1 1-2 2 0 2 1 2 0 3h-1-1c-1 0-1 1-2 1 0 2-2 3-3 3h-2 0c-2 1-5 2-7 2s-5 1-6 2h-6c-5-1-10-1-15-2v-1c-2-1-4-1-6-3 1-2 1-3 1-5 0 2 1 3 1 4l1 1c1-2 1-5 1-7l1-1h0c2 0 4 1 6 2l1-1c0-2 0-4-1-6v-3-3h-2l-1-1h7c2 0 4-1 7 0h1 2c4 0 9-1 13-2 2 0 3-1 4-2l2 1h1 2c2 0 3 0 3-2l-1-1 1-2v-1h1c2-1 4-2 6-4l3-2z"></path><path d="M641 495c2 0 3-1 4-2l2 1-1 1h0c-2 1-3 2-5 1h0v-1z" class="b"></path><path d="M648 512l5-4 1 2c0 1-1 1-2 2 0 2 1 2 0 3h-1-1c-1 0-1 1-2 1 0 2-2 3-3 3h-2 0c-2 1-5 2-7 2-1-1-1-1-2-1-3-1-4-2-6-3-3-1-6-3-9-3-2-1-3-2-4-3l12 3h7 1c4 0 7 0 11-1 1 0 1 0 2-1z" class="K"></path><path d="M639 518c2-1 3-3 6-4l2 2c-2 2-5 4-8 3v-1z" class="c"></path><path d="M648 512l5-4 1 2c0 1-1 1-2 2-1 2-3 3-5 4l-2-2 1-1c1 0 1 0 2-1z" class="E"></path><path d="M635 514c4 0 7 0 11-1l-1 1c-3 1-4 3-6 4h-3c-1-1-1-3-1-4z" class="b"></path><path d="M627 514h7 1c0 1 0 3 1 4h-2c-2-1-3-1-5-2-1 0-2-1-4-1h0l2-1z" class="k"></path><defs><linearGradient id="U" x1="606.355" y1="515.974" x2="625.65" y2="514.597" xlink:href="#B"><stop offset="0" stop-color="#202122"></stop><stop offset="1" stop-color="#3a3c3c"></stop></linearGradient></defs><path fill="url(#U)" d="M608 509h0c2 0 4 1 6 2h1c1 1 2 2 4 3 3 0 6 2 9 3 2 1 3 2 6 3 1 0 1 0 2 1-2 0-5 1-6 2h-6c-5-1-10-1-15-2v-1c-2-1-4-1-6-3 1-2 1-3 1-5 0 2 1 3 1 4l1 1c1-2 1-5 1-7l1-1z"></path><path d="M608 509c2 0 4 1 6 2h1c1 1 2 2 4 3 2 2 4 3 7 5h-1c-2 1-5-1-7-2-1-1-2-2-3-2v-1l-1-1c-1-2-4-3-6-4z" class="C"></path><path d="M619 514c3 0 6 2 9 3 2 1 3 2 6 3 1 0 1 0 2 1-2 0-5 1-6 2h-6c-5-1-10-1-15-2v-1h1c5 1 13 2 17 0l-1-1c-3-2-5-3-7-5z" class="D"></path><path d="M663 482l1 1-1 1h-1v2h0v2l-1 2v1c-1 2-1 2 0 3l1 1c0 1-1 3-2 5l1 1c0 1 0 1-1 1l-4 4h0v1c-1 1-2 1-2 3l-1-2-5 4h-1l2-2c-1-1-1-2-2-3h0l-1-1 1-2c-2-1-2-1-3-3 0-2 1-2 2-4v-2h0l1-1h1 2c2 0 3 0 3-2l-1-1 1-2v-1h1c2-1 4-2 6-4l3-2z" class="l"></path><path d="M663 482l1 1-1 1h-1v2c-1 1-1 1-2 3 0 1-1 3-2 5l-1-1c1-2 1-2 1-3l-1-1-2 2-2 1-1-1 1-2v-1h1c2-1 4-2 6-4l3-2z" class="M"></path><path d="M655 491l2-2 1 1c0 1 0 1-1 3l1 1c0 2-1 5-1 7l-4 7-5 4h-1l2-2c1-1 1-2 2-3 0-1 0-1 1-1h1l1-2 1-1c0-2-1-4-1-5 0-2 0-3 1-4s0-2 0-3z" class="Q"></path><path d="M655 491c0 1 1 2 0 3s-1 2-1 4c0 1 1 3 1 5l-1 1-1 2h-1c-1 0-1 0-1 1-1 1-1 2-2 3-1-1-1-2-2-3h0l-1-1 1-2c-2-1-2-1-3-3 0-2 1-2 2-4v-2h0l1-1h1 2c2 0 3 0 3-2l2-1z" class="k"></path><path d="M647 503h0c-1-1-2-1-2-2 1-2 2-2 3-3l1 1c0 1 1 2 0 3-1 0-1 1-2 1z" class="C"></path><path d="M651 498h3c0 1 1 3 1 5l-1 1-1 2h-1c-1 0-1 0-1 1 0-2 0-3-1-5 0 1-1 1-2 2l-1-1c1 0 1-1 2-1 1-1 0-2 0-3v-1h2z" class="l"></path><path d="M651 498h3c0 1 1 3 1 5l-1 1c-1 0-2 0-3-1 0-2 1-2 2-3v-1h-1l-1-1z" class="P"></path><path d="M557 484l1 1h4c3 0 6 1 9 1l33 8h0v4 11 3c0 2 0 3-1 5 2 2 4 2 6 3v1l-5-1c0 1 0 5-1 6l-1 1c-5-2-10-3-16-4-5-1-11-2-16-2 0 2 0 5-1 7v-7c0-2-1-4-1-6v-2c-1-1-1-1-1-3-1 0-1 0-2-1l-2-2v-1h-6v-22z" class="d"></path><path d="M575 494s1 0 2 1h0l-2 2-1 1v-2l1-2z" class="R"></path><path d="M573 510h2v-1h1v4h-1l1 2v1h-3v-6z" class="E"></path><path d="M576 515c2-1 4-2 6-4 1 2 1 2 1 4l-1 1 1 1h-1-1l-5-1v-1z" class="L"></path><path d="M585 507l2-2c0 2 0 3-1 4v2 7c-1 0-3 0-4-1h1l-1-1 1-1c0-2 0-2-1-4 1-1 2-2 3-4z" class="B"></path><path d="M590 508v5 5h-3l-1-1c1-2 1-4 0-6 1-1 2-2 4-3z" class="k"></path><path d="M588 494l1 1c0 2-1 6 1 8h0c-1 1-1 1-1 2 1 1 0 2 1 3-2 1-3 2-4 3h0v-2c1-1 1-2 1-4 0-1 0-2-1-3h0c1-1 1-2 2-3v-2-3z" class="O"></path><path d="M588 494v3 2c-1 1-1 2-2 3h0c1 1 1 2 1 3l-2 2-1-2c0 1-1 2-1 2l-2-1c1-2 1-2 3-4h-1c1-1 0-1 1-2l1-1-1-1h2l-1-2 1-1 2-1zm16 4v11 3c0 2 0 3-1 5 2 2 4 2 6 3v1l-5-1v-1c-1 0-2 1-3 1h-2c0-2 0-2-1-4h2v-5-1c0-1 0-2-1-3l1-1 1 1h1l-1-1v-3c0-1 0-1 2-2 0-2 0-2 1-3z" class="L"></path><path d="M601 520c-1-1-1-2 0-3h0v-1-1-1-3c0-2 0-1 1-2h2v3c0 2 0 3-1 5 2 2 4 2 6 3v1l-5-1v-1c-1 0-2 1-3 1z" class="F"></path><path d="M569 504l4 1v5 6h3l5 1c0 1 0 2-1 3-3 0-7-2-10-1v2c0 2 0 5-1 7v-7c0-2-1-4-1-6v-2l1-6c-1-1 0-2 0-3z" class="K"></path><path d="M569 507c1 2 1 5 1 8 0 2-1 2 0 4v2c0 2 0 5-1 7v-7c0-2-1-4-1-6v-2l1-6z" class="G"></path><path d="M586 511h0c1 2 1 4 0 6l1 1h3l9 2h2c1 0 2-1 3-1v1c0 1 0 5-1 6l-1 1c-5-2-10-3-16-4-5-1-11-2-16-2v-2c3-1 7 1 10 1 1-1 1-2 1-3h1c1 1 3 1 4 1v-7z" class="D"></path><path d="M557 484l1 1h4c3 0 6 1 9 1l1 4c1 3-1 6 0 9 1 1 1 5 1 6l-4-1c0 1-1 2 0 3l-1 6c-1-1-1-1-1-3-1 0-1 0-2-1l-2-2v-1h-6v-22z" class="R"></path><path d="M572 490c1 3-1 6 0 9 1 1 1 5 1 6l-4-1-1-3-2-2h0l1-1 1-1c1 1 1 2 3 2l1-9z" class="B"></path><path d="M566 499l2 2 1 3c0 1-1 2 0 3l-1 6c-1-1-1-1-1-3-1 0-1 0-2-1l-2-2v-1-1l3-6z" class="J"></path><path d="M654 871c2 1 6 3 8 3l4-5-3 6 51 12c9 1 18 2 27 5H620h-16v7c0-1-1-6-1-7h-6c-2 0-5 0-7-1 0-2 4-3 6-4 3-2 5-5 8-7 1-1 3-1 6-1l4 1h1c2 1 5 1 7 0h3c2 0 4 1 6 2 6 2 15 3 21 2 2 0 2-1 3-3-3-4-5-5-10-8l2-2 3 1s1 1 2 1l2-2z" class="u"></path><path d="M655 881c1 1 1 2 1 3-2 1-2 1-2 2 1 1 0 1 1 1-1 1-1 1-2 1l-1-1h-5c-3 1-6 0-9-1h-5c-2-1-5 0-7 1h-1c-2 2-4 1-6 3h0c0-2-1-3 0-4h-2c3-1 5-3 7-3 2-1 5-1 7-1 6 2 15 3 21 2 2 0 2-1 3-3z" class="X"></path><path d="M597 892c-2 0-5 0-7-1 0-2 4-3 6-4 3-2 5-5 8-7 1-1 3-1 6-1l4 1h1c2 1 5 1 7 0h3c2 0 4 1 6 2-2 0-5 0-7 1-2 0-4 2-7 3h-2v1 1h0l-2 1c-1 0-3-1-5-1v-1h2c2 1 2 1 3 1l1-1c-1-1-2-1-3-2-1 1-3 2-5 2v1h-1c0 1-1 1-1 2l1-1 2 2c2 0 5-1 8 0l5 1h-16v7c0-1-1-6-1-7h-6z" class="j"></path><path d="M662 488c1 1 2 2 3 4v1c0 1-1 3 0 4l2-2v1 2c3-1 5-1 9-1 0 1 1 1 2 1s0 0 1 1h2c2 2 5 4 7 6h0l3 8 8-3c0 1-1 2-1 3l-1-1c-2 1-3 1-4 2l-1 1-1-2v7h-1l-1 2c-1 2-2 5-3 7h0l-3 3v3h0c-1 0-2 0-3 1s-2 1-3 1l-1 1h-4c-1 1-1 1-2 1l-2 1h-1-1c-1 0-1-1-3 0l-2-2c-2-1-4-3-7-4-1-1-5-2-7-3l-1-2h-1c-3-1-6-4-8-4l-2-1c-2 0-4-1-5-1 1-1 4-2 6-2s5-1 7-2h0 2c1 0 3-1 3-3 1 0 1-1 2-1h1 1c1-1 0-1 0-3 1-1 2-1 2-2 0-2 1-2 2-3v-1h0l4-4c1 0 1 0 1-1l-1-1c1-2 2-4 2-5l-1-1c-1-1-1-1 0-3v-1l1-2z" class="U"></path><path d="M676 510c-1-1-3-2-3-3l1-1v1c1 2 2 2 4 2 1-1 2-1 3-1l1 1-1 1c0 1 0 1 1 2h1l-1 2c3 2 5 0 5 4l-1-1c-1-1-2-1-4-1v1c-1-1-1-1-2-1v1h-1l-1-3v-1h1c-1-2-1-2-3-3z" class="i"></path><path d="M662 515c1-3 3-2 5-3v-1c1-1 1-1 2-1v1h1 2 0v-1h-1v-1h3c0 1 1 1 2 1h0c2 1 2 1 3 3h-1v1l-1 2-1-2h0-1c-1-1-3-1-4-1 0 1 0 1 1 2l-2 2h-1c-1 0-2 1-2 1-2 1-2 0-3 1h-3l1-1-1-1 1-1h0v-1z" class="X"></path><path d="M679 499h2c2 2 5 4 7 6h0l3 8 8-3c0 1-1 2-1 3l-1-1c-2 1-3 1-4 2l-1 1-1-2v7h-1v-1c-1 0-1-1-2-1h-1c0-4-2-2-5-4l1-2h-1c-1-1-1-1-1-2l1-1c0 1 1 1 1 2h0l1 1 1-1c-1-1-2-2-3-4l-2-2 1-1c2 1 2 1 3 3 0 1 0 2 2 3l1-1c0 1 1 1 1 2s0 3 1 4h0c0-3 0-5-1-8l-1-1c-1-2-1-2-3-3l-1-1c0-1-1-1-3-2l-1-1z" class="j"></path><path d="M662 488c1 1 2 2 3 4v1c0 1-1 3 0 4l2-2v1 2l-1 1h1 4c1 0 3-1 5 0h1v1h-1c-3-1-7 0-10 0v1c2 1 2 1 4 0v1h-2-1c-1-1-1 0-2-1-2 2-4 3-7 4 0 1-1 2 0 2 0 1 1 0 1 0l-1 2h1l2-1v1c0 1-1 3-3 4v1c-1 1-1 3-1 5h-1c-1-1-1-1-2-1v-2h-1l-1 4c-1 0-2 1-3 1-2 0-4 0-6 1-1 1-2 1-3 1-2 0-3-1-5 0h0v1c-2 0-4-1-5-1 1-1 4-2 6-2s5-1 7-2h0 2c1 0 3-1 3-3 1 0 1-1 2-1h1 1c1-1 0-1 0-3 1-1 2-1 2-2 0-2 1-2 2-3v-1h0l4-4c1 0 1 0 1-1l-1-1c1-2 2-4 2-5l-1-1c-1-1-1-1 0-3v-1l1-2z" class="Q"></path><defs><linearGradient id="V" x1="660.254" y1="528.47" x2="653.434" y2="531.677" xlink:href="#B"><stop offset="0" stop-color="#9f8d77"></stop><stop offset="1" stop-color="#afa694"></stop></linearGradient></defs><path fill="url(#V)" d="M640 523c1 0 2 0 3-1 2-1 4-1 6-1 1 0 2-1 3-1 2 4 4 9 8 12h0 3v1h-1c1 2 2 3 4 4l2 2c1 0 1 0 2-1h2c-1 1-1 1-2 1l-2 1h-1-1c-1 0-1-1-3 0l-2-2c-2-1-4-3-7-4-1-1-5-2-7-3l-1-2h-1c-3-1-6-4-8-4l-2-1v-1h0c2-1 3 0 5 0z"></path><path d="M645 529c-1-1-1-2-2-2v-1-1c2 1 4 1 6 2 1 1 2 1 3 2l-1 1h0l-5-1h-1z" class="j"></path><path d="M635 524v-1h0c2-1 3 0 5 0 3 2 6 1 9 2v2c-2-1-4-1-6-2v1 1c1 0 1 1 2 2-3-1-6-4-8-4l-2-1z" class="U"></path><path d="M661 519h3c1-1 1 0 3-1 0 0 1-1 2-1h1l2-2c-1-1-1-1-1-2 1 0 3 0 4 1h1 0l1 2 1-2 1 3h1v-1c1 0 1 0 2 1v-1c2 0 3 0 4 1l1 1h1c1 0 1 1 2 1v1l-1 2c-1 2-2 5-3 7h0l-3 3v3h0c-1 0-2 0-3 1s-2 1-3 1l-1 1h-4-2c-1 1-1 1-2 1l-2-2c-2-1-3-2-4-4h1v-1h-3 0c-4-3-6-8-8-12l1-4h1v2c1 0 1 0 2 1h1l2-1v-2c0-1 1-1 2-2l1 1v1h0l-1 1 1 1-1 1z" class="r"></path><path d="M657 519l2-1v-2c0-1 1-1 2-2l1 1v1h0l-1 1 1 1-1 1c-1 2-1 2-1 4l1 1h0c1 1 2 2 2 3 1 0 4 0 5 1v-1l-1-1 1-2h3 1c-1 1 0 1-1 1l-2 2h0l1 2c-1 1-1 1 0 3h-2l-1-2c-2 0-3 0-4 1v1h-3 0c-4-3-6-8-8-12l1-4h1v2c1 0 1 0 2 1h1z" class="i"></path><path d="M677 516l1-2 1 3h1v-1c1 0 1 0 2 1v-1c2 0 3 0 4 1l1 1h1c1 0 1 1 2 1v1l-1 2c-1 2-2 5-3 7h0l-3 3v3h0c-1 0-2 0-3 1s-2 1-3 1l-1 1h-4-2c-1 1-1 1-2 1l-2-2c-2-1-3-2-4-4h1v-1-1c1-1 2-1 4-1l1 2h2 3 0 2c1 0 1 0 2-1h2 0c1-1 2-1 3-2h1v-1h-2c-2-1-1-4-2-6 0 1 0 1-1 1v-1-1-2c-1-1-1-1-1-2v-1z" class="j"></path><path d="M687 518h1c1 0 1 1 2 1v1l-1 2h-3v-5l1 1z" class="p"></path><path d="M683 532v3h0c-1 0-2 0-3 1s-2 1-3 1l-1 1h-4-2c-1 1-1 1-2 1l-2-2c-2-1-3-2-4-4h1c1 0 3 1 4 1 3 1 6 1 9 0l7-2z" class="e"></path><path d="M667 534c3 1 6 1 9 0l-1 2-1 1h-5c-2-1-2-2-2-3z" class="t"></path><path d="M761 226l3-3c2-2 4-4 4-6l-1-1c1-2 2-6 3-7l1 1 1 1v2s0 1 1 1c3 1 7 1 10 2l-1 2c1 0 1 1 2 1l1-1h1 1c2 0 3 0 4 1h-1l1 2c2 1 3 1 4 1l2-2c2 1 3 2 5 3 0 1 0 0 1 1 0 1-1 1-1 2 0 0 0 1 1 1h0c0-1 0-1 1-1v1l1-2h0c1 1 2 1 3 1-1 1-1 1-2 1v9l2 18-1 2h0l-1-1c-2-4 1-10-1-14-1-1-1-1-1-2h-1c-1 5-2 9-7 12-3 2-7 3-10 2h-3c-3 0-5-1-7-2-1-1-1-1-2-1-2 0-3-3-4-4h-2l-1-1h-1l-2-2s-1 0-2-1l-2-1-1 1c-1-1-2-1-3-1h-1c-1 1-2 1-3 2h0l2-3c0-1-2-2-4-2-4-3-9-5-14-7v-1h0l4 1 1 1v-1-1h4 0v-1h2 1c1 1 2 1 3 1l2-3v1c1-1 2-1 3-1l1 1 4-2z" class="a"></path><path d="M788 245l2 3h1c-1 1-1 1-1 2l-2-1c-1-1-2-1-2-2l2-2z" class="j"></path><path d="M783 253v-2c0-1-2-1-2-3l1-1h1l2 2c0 1 0 3-1 4h-1z" class="X"></path><path d="M803 239h-1c-2 0-2 0-2-1s1-3 3-3h0v2l1 2h-1z" class="i"></path><path d="M795 240l1-1 1 1v1h1c1 1 1 2 1 4l-1 1c-1 0-2-2-2-2-1 0-2 1-2 0v-2l1-2z" class="f"></path><path d="M785 231v-1h0l1-2h1v2l2 1h0c4 0 5-1 8 1v1h-1c-1-1-1-2-2-2-2 1-2 2-2 3-1 2-2 3-3 4 1 1 2 0 3 1 1 0 2 1 3 1l-1 2c-1 1-2 2-3 4v1l-1 1-2-3c-2-1-3-3-5-4l1-1v-1h-2 0l1-1c0-1 0-1 1-2h1c0-2 0-3-1-4l1-1z" class="q"></path><path d="M784 219l1-1h1 1c2 0 3 0 4 1h-1l1 2c2 1 3 1 4 1l2-2c2 1 3 2 5 3 0 1 0 0 1 1 0 1-1 1-1 2 0 0 0 1 1 1v2l-1 1c1 1 1 2 1 3v1c-2-2-2-4-5-5-2-1-3-2-5-2h-1-1-2v4h0l-2-1v-2h-1l-1 2h0v1c-2-1-2-1-3 0v-2c-2 1-1 2-2 4v-1h-1c0-1 0-1-1-2 0 1-1 2-1 2-1-1-1-2-1-3l-1-1v5l-1-1v-5c1 0 2-1 3-1 0-2 0-1 1-2h1 1c2 0 1-2 2-3 1 0 1 0 2-1v-1z" class="f"></path><path d="M795 222c1 1 1 2 2 2 0 1 0 1-1 2h-1c-1 0-2-1-2-1 1-1 1-2 2-3h0z" class="a"></path><path d="M784 220l2 1v2h-1c-2 0-3 1-4 2v1c-1 0-1-1-2-2h1c2 0 1-2 2-3 1 0 1 0 2-1z" class="M"></path><path d="M795 222l2-2c2 1 3 2 5 3 0 1 0 0 1 1 0 1-1 1-1 2-3-1-3-2-4-4h-1v2c-1 0-1-1-2-2h0z" class="j"></path><path d="M784 219l1-1h1 1c2 0 3 0 4 1h-1l1 2v1h-1l-1-1h0c-1-1-2-1-2-2l-1 1v1l-2-1v-1zm-15 13h3v-1-1c0-2 0-2 1-3h1v5l1 1v-5l1 1c0 1 0 2 1 3 0 0 1-1 1-2 1 1 1 1 1 2h1v1c1-2 0-3 2-4v2c1-1 1-1 3 0l-1 1c1 1 1 2 1 4h-1c-1 1-1 1-1 2l-1 1h0 2v1l-1 1c0 1 0 2-1 3v1h0c-1 1-1 1-2 1l-2 1-1 1-1 1c-1-1-2-1-3-2l-2-2-1 1h-2c-1-3-1-5-2-8 1-2 2-2 4-3v-1l-2-2h1z" class="V"></path><path d="M773 242c2 1 2 2 3 3h3c1 0 1-1 2-1 0 0 0 1 1 1-1 1-1 1-2 1l-2 1-1 1-1 1c-1-1-2-1-3-2v-5z" class="J"></path><path d="M770 235l1 1c0-1 0 0 1-1l1 1c0-1 1-1 1-2h1v1l-1 1 1 1 1-1h0 2v1l-1 1c-1 0-2 1-3 2h-1v2 5l-2-2-1 1h-2c-1-3-1-5-2-8 1-2 2-2 4-3z" class="U"></path><path d="M761 226l3-3c2-2 4-4 4-6l-1-1c1-2 2-6 3-7l1 1 1 1v2s0 1 1 1c3 1 7 1 10 2l-1 2c1 0 1 1 2 1v1c-1 1-1 1-2 1-1 1 0 3-2 3h-1-1c-1 1-1 0-1 2-1 0-2 1-3 1h-1c-1 1-1 1-1 3v1 1h-3-1l2 2v1c-2 1-3 1-4 3 1 3 1 5 2 8l-1-1h-1l-2-2s-1 0-2-1l-2-1-1 1c-1-1-2-1-3-1h-1c-1 1-2 1-3 2h0l2-3c0-1-2-2-4-2-4-3-9-5-14-7v-1h0l4 1 1 1v-1-1h4 0v-1h2 1c1 1 2 1 3 1l2-3v1c1-1 2-1 3-1l1 1 4-2z" class="M"></path><path d="M767 245h0c-1-2-2-2-4-3v-1l2-2v-1l-1 1h-1c1-2 2-2 4-3l-1-1c1-2 1-2 2-3l2 2v1c-2 1-3 1-4 3 1 3 1 5 2 8l-1-1z" class="L"></path><path d="M751 230l2-3v1c1-1 2-1 3-1l1 1 4-2c0 2-1 3-3 4l-3 3h-1-3-2c0-1-1-1-1-2h3v-1z" class="e"></path><path d="M741 232v-1-1h4 0v-1h2 1c1 1 2 1 3 1v1h-3l-1 1v1c2 2 6 2 9 2 0 1-1 1-1 2l1 2c2-1 2-2 4-2l-2 2c-1 1-1 1-2 1l-1-1-7-4c-2-1-5-3-7-3z" class="P"></path><path d="M771 210l1 1v2s0 1 1 1c3 1 7 1 10 2l-1 2c1 0 1 1 2 1v1c-1 1-1 1-2 1-1 1 0 3-2 3h-1-1c-1 1-1 0-1 2-1 0-2 1-3 1h-1c-1 1-1 1-1 3v1 1h-3 0c0-2 1-2 2-4h0v-1l-3 3-1-1 4-3c0-1 0-1 1-1 1-1 2-2 2-4l-6 3c-1 1-1 1-2 1 2-1 4-4 5-6v-3-2l-1 1-1-1 1-1c0-1 0-2 1-3z" class="e"></path><path d="M573 227l5 4h0-2l3 3c2 2 3 3 6 4v1c-1 0-2-1-4-1h-1l1 1h0 3c0 1 0 1 1 2-2 0-2-1-3 0l2 2h-1v2h0c1 2 2 3 2 5h1c0-3-3-4-2-6l1-1c1 2 2 4 2 6l1 1h1 0c3-1 7-4 10-5v63c2 0 4 0 6 1l-6 1c0 2 1 8 0 9v1c-1 1-1 2 0 3-2 1-2 2-4 2 0 2 0 4-1 6h0l-1-1c-1 0-1 1-2 2l-1-1c0-1-1-2-1-3-1-3 0-7 0-10l-1-1c-2-1-4 0-6 0-3-1-6-1-9-1v-5h0v-2c0-2-2-4-3-5h0c-2 1-3 2-4 2l7-13c0-1 1-2 1-3v-1l1-3c1-2 1-3 1-5 1-7 1-15 1-22v-10-1c-2-4-1-7-1-11-1-2-1-5-1-7h-1v1h-1v-1-3z" class="C"></path><path d="M576 237c1 2 1 3 1 5l1 1c0 2-1 3-1 5-2-4-1-7-1-11z" class="H"></path><path d="M588 265h0 1c0 1 1 1 2 2 1 2 1 9 0 12l1 32v1c-2 1-2 0-4 0v-18-6-23z" class="D"></path><path d="M588 265h0 1c0 1 1 1 2 2 1 2 1 9 0 12v-2h0c-2 2-1 7-1 9-1 0-1-1-1-2v1c0 3 0 6-1 9v-6-23z" class="F"></path><path d="M592 311c1 3 1 6 1 9v10c-1 0-1 1-2 2l-1-1c0-1-1-2-1-3-1-3 0-7 0-10l-1-1c-2-1-4 0-6 0-3-1-6-1-9-1v-5h0c3 0 5-1 8 0 2 0 5 0 7 1 2 0 2 1 4 0v-1z" class="O"></path><path d="M589 328l1-2 2-2v-3-1h1v10c-1 0-1 1-2 2l-1-1c0-1-1-2-1-3z" class="Y"></path><path d="M577 249c2 2 3 4 4 6 2 3 5 7 7 10v23 6 18c-2-1-5-1-7-1-3-1-5 0-8 0v-2c0-2-2-4-3-5h0c-2 1-3 2-4 2l7-13c0-1 1-2 1-3v-1l1-3c1-2 1-3 1-5 1-7 1-15 1-22v-10z" class="R"></path><path d="M585 278c0-3 0-6 1-9l1 25c0-2 0-4 1-6v6 18c-2-1-5-1-7-1-3-1-5 0-8 0v-2c2 0 4 2 5 1h1c2-1 4-2 5-4 1 0 2-2 3-4 0-1-1-5-1-6v-1c0-3 0-5-2-7h1v-10z" class="L"></path><defs><linearGradient id="W" x1="573.962" y1="270.292" x2="582.602" y2="275.15" xlink:href="#B"><stop offset="0" stop-color="#0d0d0f"></stop><stop offset="1" stop-color="#2d2d2e"></stop></linearGradient></defs><path fill="url(#W)" d="M577 259v1 3l5 3c1 2 2 5 2 7s0 4 1 5v10h-1c2 2 2 4 2 7v1l-3-6v1c-1 0-1 1-2 1h-1l-1 1c-1 0-1-1-2-1h-3v1h-1c0-1 1-2 1-3v-1l1-3c1-2 1-3 1-5 1-7 1-15 1-22z"></path><path d="M582 266c1 2 2 5 2 7s0 4 1 5v10c-1-3-1-6-1-9h-1v3h0c-1-2-1-4-1-6v-10z" class="I"></path><path d="M574 292c1-2 2-5 2-7 0-1 1-1 1-2 1 0 3 2 3 2 0 1-1 1 0 2l1 1c0 1-1 2 0 3h0v-1l1-1 1 1v1c-1 0-1 1-2 1h-1l-1 1c-1 0-1-1-2-1h-3z" class="Y"></path><path d="M579 293l1-1h1c1 0 1-1 2-1v-1l3 6c0 1 1 5 1 6-1 2-2 4-3 4-1 2-3 3-5 4h-1c-1 1-3-1-5-1 0-2-2-4-3-5h0c-2 1-3 2-4 2l7-13h1v-1h3c1 0 1 1 2 1z" class="I"></path><path d="M571 303l-1-1c0-1 2-5 3-6h1 1c0 1 1 1 2 1v1h-1l-1 1 3 2h-2v1l1 1h0 0c0 1 0 1 1 2h-1c-2 1-1 0-2-1h-3l-1-1z" class="d"></path><path d="M579 293l1-1h1c1 0 1-1 2-1v-1l3 6c0 1 1 5 1 6-1 2-2 4-3 4-1 2-3 3-5 4h-1c-1 1-3-1-5-1 0-2-2-4-3-5l1-1 1 1h3c1 1 0 2 2 1h1l1-1 2-2c1-1 1-1 1-2 1-1 1-2 1-3l-4-4z" class="Z"></path><path d="M571 303l1 1h3c1 1 0 2 2 1l1 1 1 1h-2v1l1 1 1 1h-1c-1 1-3-1-5-1 0-2-2-4-3-5l1-1z" class="Y"></path><path d="M572 304h3c1 1 0 2 2 1l1 1 1 1h-2v1h-1v-1h-2l-2-3z" class="G"></path><path d="M441 526l1-1h0c1 1 2 1 3 1l2 4v-1c1 3 2 6 2 9l2 1c0-1 1-2 1-2l1-2h0l1-1h2l1-1v1l1-1 1-1h1v3h1c0 1 1 2 1 3h2l2 1 1 2-1 1v1c-2 0-3-1-4 0v6c0 1-1 1 0 3h1l-1 1h-3v5 8c0 3 0 5-1 7l-1 3h0c1 1 2 1 3 2v1c-1 3 0 5-1 8 0 2 1 3 1 5 1 1 1 2 1 3-2 1-3 1-4 1v3 3 1h-1c0-1 0-1-1-2v-3-2l-1 1h-1v-2l-1-1c-2-1-6-1-8 0-1 0 0 0-1-1h-3v1c-2-2-3-2-5-2l1-1h-5v6l-1 1-2-2c-1-1-2-1-3-1h-1v-13c-1-3 0-5-1-7l-1-1h-1l2-1-1-1c2-3 0-6 2-8h0v-1c-1-1-1 0-1-1l-1-1c0-2 0-2 1-4 0 0 1 2 3 2h0c1 0 2 0 3 1h1c0-1 1-3 1-4v-1l-1-1c0-1 1-2 2-3h7l-1-1h-2-2l1-1 1-1c1 0 2 1 3 1v-1l1-4-1-1v-1c-1-2-1-6-1-9 0-2 0-4 1-6l1-1h1z" class="o"></path><path d="M438 590h10 6c0 2 0 3-1 4v1l-1-1c-2-1-6-1-8 0-1 0 0 0-1-1h-3v1c-2-2-3-2-5-2l1-1h-5c1-1 5-1 7-1z" class="W"></path><path d="M438 590h10 6c0 2 0 3-1 4v1l-1-1v-2c-1-1-1-1-3-1-2-1-8 0-11 0v-1z" class="S"></path><path d="M424 564h2v5 12c0 1-1 3 0 5 0 2 1 5 1 8h-1-1v1h-1v-13c-1-3 0-5-1-7l-1-1h-1l2-1-1-1c2-3 0-6 2-8z" class="U"></path><path d="M431 556c2 1 4 1 6 1l1 1h0v1c1 2 1 4 1 6 0 3 0 6-1 9 0 3 1 8-1 11l2 1h-1l-8 1-1-24h-5c-1-1-1 0-1-1l-1-1c0-2 0-2 1-4 0 0 1 2 3 2h0c1 0 2 0 3 1h1c0-1 1-3 1-4z" class="R"></path><path d="M438 558h0v1c1 2 1 4 1 6 0 3 0 6-1 9 0 3 1 8-1 11 0 0 0-1-1-1v-18c0-3 0-6 2-8z" class="e"></path><path d="M438 558c2 1 4 1 6 1 0 1-1 2-1 4h5 1c1-1 2-1 4-2 0 2 0 2-1 3l1 6v4l1 1 2-1h0l2-1-1 3h0c1 1 2 1 3 2v1c-1 3 0 5-1 8 0 2 1 3 1 5 1 1 1 2 1 3-2 1-3 1-4 1v3 3 1h-1c0-1 0-1-1-2v-3-2l-1 1h-1v-2-1c1-1 1-2 1-4h-6c-1-1-2-1-3-1h0v-3h-7 1l-2-1c2-3 1-8 1-11 1-3 1-6 1-9 0-2 0-4-1-6v-1z" class="T"></path><path d="M444 575v7h0l-1-1h-1c0-1 0-2 1-3v-3h1z" class="h"></path><path d="M438 558c2 1 4 1 6 1 0 1-1 2-1 4h5v1h-2v20l-2 1v-3-7c-1-4 0-7-1-10 0-2 0-1-1-2s0-1-2-2c0-1-1-1-2-2v-1z" class="P"></path><path d="M448 563h1c1-1 2-1 4-2 0 2 0 2-1 3l1 6h-1c-1-1-1-2-2-3h0v-1h-1c-1 2 0 7 0 10 0 2 0 3-1 4v4h-2v-20h2v-1z" class="F"></path><path d="M438 559c1 1 2 1 2 2 0 7-1 16 0 23 1 1 2 1 4 1l2-1h2v-4c0 2 0 3 1 5h1l1-1c0-2-1-3 0-5h2 1c0 2 1 3 2 5l-1 2s-1 0-2 1c-2 0-6 0-8-1h-7 1l-2-1c2-3 1-8 1-11 1-3 1-6 1-9 0-2 0-4-1-6z" class="N"></path><path d="M453 579h1c0 2 1 3 2 5l-1 2s-1 0-2 1v-8z" class="a"></path><path d="M448 580c1-1 1-2 1-4 0-3-1-8 0-10h1v1h0c1 1 1 2 2 3h1v4l1 1 2-1h0l2-1-1 3h0c1 1 2 1 3 2v1c-1 3 0 5-1 8 0 2 1 3 1 5 1 1 1 2 1 3-2 1-3 1-4 1v3 3 1h-1c0-1 0-1-1-2v-3-2l-1 1h-1v-2-1c1-1 1-2 1-4h-6c-1-1-2-1-3-1h0v-3c2 1 6 1 8 1 1-1 2-1 2-1l1-2c-1-2-2-3-2-5h-1-2c-1 2 0 3 0 5l-1 1h-1c-1-2-1-3-1-5z" class="S"></path><path d="M456 579l2-1 1 2h0v3 5l-2 1v-1l-2-2 1-2c-1-2-2-3-2-5h2z" class="N"></path><path d="M456 579c2 2 1 6 1 9l-2-2 1-2c-1-2-2-3-2-5h2z" class="V"></path><path d="M453 587c1-1 2-1 2-1l2 2v1l-2 2h3v1c-1 1 0 2 0 3h1c1-1 1-2 1-3 1 1 1 2 1 3-2 1-3 1-4 1v3 3 1h-1c0-1 0-1-1-2v-3-2l-1 1h-1v-2-1c1-1 1-2 1-4h-6c-1-1-2-1-3-1h0v-3c2 1 6 1 8 1z" class="K"></path><path d="M441 526l1-1h0c1 1 2 1 3 1l2 4v-1c1 3 2 6 2 9l2 1c0-1 1-2 1-2l1-2h0l1-1h2l1-1v1l1-1 1-1h1v3h1c0 1 1 2 1 3h2l2 1 1 2-1 1v1c-2 0-3-1-4 0v6c0 1-1 1 0 3h1l-1 1h-3v5 8c0 3 0 5-1 7l-2 1h0l-2 1-1-1v-4l-1-6c1-1 1-1 1-3-2 1-3 1-4 2h-1-5c0-2 1-3 1-4-2 0-4 0-6-1h0l-1-1c-2 0-4 0-6-1v-1l-1-1c0-1 1-2 2-3h7l-1-1h-2-2l1-1 1-1c1 0 2 1 3 1v-1l1-4-1-1v-1c-1-2-1-6-1-9 0-2 0-4 1-6l1-1h1z" class="T"></path><path d="M454 534c2 3 0 10 0 14h-1v-13l1-1z" class="X"></path><path d="M459 558h0v8c0 3 0 5-1 7l-2 1v-2c1-2 1-4 1-6 0-1 0-3 1-5h0l1-3z" class="e"></path><path d="M457 533v1h2l-1 12v4c-1 0-2 0-2-1 0-4-1-8 0-12 0-1 0-2-1-2l1-1 1-1z" class="q"></path><path d="M453 535v13h-3-1c-1-1-2-1-3-1l1-1c-1-2-1-4-1-6h2c0 1 1 1 2 1l1-1v-1c0-1 1-2 1-2l1-2h0z" class="e"></path><path d="M453 548h1l1 11c0 2-1 5 0 8v1h0 1c1-1 0-1 1-2 0 2 0 4-1 6v2h0l-2 1-1-1v-4l-1-6c1-1 1-1 1-3v-13z" class="M"></path><path d="M450 548h3v13c-2 1-3 1-4 2h-1-5c0-2 1-3 1-4h4c0-2 0-3 1-4l3-1v-2-1h0c-1-1-1-2-2-3z" class="C"></path><path d="M449 555l3-1v2l-1 1v2h-3c0-2 0-3 1-4z" class="R"></path><path d="M457 534l1-1 1-1h1v3h1c0 1 1 2 1 3h2l2 1 1 2-1 1v1c-2 0-3-1-4 0v6c0 1-1 1 0 3h1l-1 1h-3v5h0l-1-12 1-12h-2z" class="I"></path><path d="M462 538h2l2 1 1 2h-5 0c-1-1-1-2 0-3z" class="R"></path><path d="M449 548h1c1 1 1 2 2 3h0v1 2l-3 1c-1 1-1 2-1 4h-4c-2 0-4 0-6-1h0l-1-1c-2 0-4 0-6-1v-1l-1-1c0-1 1-2 2-3h7l-1-1c2 0 4-1 5-2h6z" class="V"></path><path d="M449 548h1c1 1 1 2 2 3h0v1h-6l-2-1h-5l-1-1c2 0 4-1 5-2h6z" class="W"></path><path d="M446 552h6v2l-3 1c-1 1-1 2-1 4h-4c-2 0-4 0-6-1h0l-1-1c-2 0-4 0-6-1v-1h8l1-1 1-1h1c1 0 1 1 3 1 1-1 1-1 1-2z" class="I"></path><path d="M446 552h6v2l-3 1h-10l1-1 1-1h1c1 0 1 1 3 1 1-1 1-1 1-2z" class="h"></path><path d="M441 526l1-1h0c1 1 2 1 3 1l2 4v-1c1 3 2 6 2 9l2 1v1l-1 1c-1 0-2 0-2-1h-2c0 2 0 4 1 6l-1 1c1 0 2 0 3 1h-6c-1 1-3 2-5 2h-2-2l1-1 1-1c1 0 2 1 3 1v-1l1-4-1-1v-1c-1-2-1-6-1-9 0-2 0-4 1-6l1-1h1z" class="V"></path><path d="M441 526h0c-1 6-1 12-1 18l-1-1v-1c-1-2-1-6-1-9 0-2 0-4 1-6l1-1h1z" class="D"></path><path d="M444 533c2 2 2 4 2 7 0 2 0 4 1 6l-1 1c1 0 2 0 3 1h-6c1 0 1 0 1-1h-1c1-2 1-5 1-7h-1c0 1 0 2-1 3 0-3 1-7 2-10z" class="b"></path><path d="M441 526l1-1h0c1 1 2 1 3 1l2 4v-1c1 3 2 6 2 9l2 1v1l-1 1c-1 0-2 0-2-1h-2c0-3 0-5-2-7v-3l-1-1v5h-1c0-2 0-6-1-8h0z" class="P"></path><path d="M427 811c2 2 3 4 6 6 1 0 2 1 4 1l1 3h-2v1l-3 3v1 1h-2v1h1c0 3-3 6-4 8-2 3-4 7-6 9l-4 6c-1 2-3 4-4 6l-2-1h-1c0 1 0 2 2 2l1-1c1 0 1 0 2 1h0l2-1 2 2 2 2 2-3c2-1 3-2 5-3l1 1c-1 2-2 3-4 5h0l-2 3 2 2h0v1c0 1 0 1-1 3h1c0 1 0 1 1 2l-1 2c0 1 0 0-1 1 0 1 0 1-1 1l-1-1c-1 0-2 1-3 0h-3-1l1 2h0c-2 1-2 2-3 3h-1v1l-1-1c-1 1-2 1-3 1h0-2-4c-1 0-3 0-5-1h-6-1-1l-1 1c0 1-1 2-2 3-2 0-2 0-3-2h0l-1-1c-2 0-2 0-4-1h-2 0l-1-1c-2 0-4-1-6 0-1 1-1 2-1 2l-1 1c-1 0-1-1-2 0h0c-1 1-1 2-2 3l-1-2c0-1-1-2-1-2l-1-1c-1-2-3-3-4-4l-2-2c4-3 11-4 16-7 11-5 21-12 29-21 1-1 2-3 3-4 0 1 1 2 1 3l2-1c0-1 0-1 1-2h1c1-2 2-3 4-5l1-1h0v-1c0-1 0-1 1-2 1 0 2-1 3-2v-1l1-2c0 1 0 1 1 1l1-1-1-2h-1l1-1 1-1 1 1s1-1 1-2 0-2-1-3l1-1v-1c0-1 0-1 1-2 0-1 1-1 1-2v-1h0 2l1-2z" class="o"></path><path d="M387 869l2-1 2 3-2 1c-1 0-2 1-3 1l-1-1-1-1c0-1 2-2 3-2z" class="B"></path><path d="M387 869l2-1 2 3-2 1c-1-1-2-2-2-3z" class="H"></path><path d="M375 870l2-1c2-1 3-3 4-4h1c0 1-1 2-1 3v4l-1 1c-1-1-1-1-2-1l-3-2z" class="p"></path><path d="M400 863l-1 1v2l-1 2c-2 1-5 2-7 3h0l-2-3 11-5z" class="C"></path><path d="M414 850h0 1v-1c1 0 2 1 3 2-1 2-3 4-4 6l-2-1h-1-2v-1l3-3c0-1 1-2 2-2z" class="X"></path><path d="M372 875c0-1 0-2 1-2v-2c1-1 0 0 2-1l3 2c1 0 1 0 2 1-1 1-2 2-2 4v1l-1 2h0l-1-1-1-3-1-1h-2z" class="j"></path><path d="M372 875h2l1 1 1 3c-2 0-4-1-6 0-1 1-1 2-1 2l-1 1c-1 0-1-1-2 0h0c-1 1-1 2-2 3l-1-2c0-1-1-2-1-2l-1-1c-1-2-3-3-4-4h0l2-1h7v-1c2 1 4 1 6 1z" class="n"></path><path d="M399 864c1 2 1 3 2 5s1 5 3 6v2l-3 2h0-2l-1 1h-6-1-1l-1 1c0 1-1 2-2 3-2 0-2 0-3-2h0l-1-1c-2 0-2 0-4-1h-2l1-2v-1c0-2 1-3 2-4l1-1 3-1 1 1 1 1c1 0 2-1 3-1l2-1h0c2-1 5-2 7-3l1-2v-2z" class="Z"></path><path d="M391 877v-1h2c0 2-1 2-1 4h-1-1l1-3z" class="l"></path><path d="M384 879v-1h3c1 0 2-1 4-1l-1 3-1 1-1-1c-1-2-3 0-4-1z" class="K"></path><path d="M391 871h0c0 2-2 4-4 5-2 2-5 2-7 2l3-3 3-2c1 0 2-1 3-1l2-1z" class="D"></path><path d="M399 866c0 3 0 7 1 10h-1v1l-1 1c-1 0-2 1-3 0 1-2 1-5 2-7 1-1 1-1 1-2v-1l1-2z" class="b"></path><path d="M384 879c1 1 3-1 4 1l1 1c0 1-1 2-2 3-2 0-2 0-3-2h0l-1-1c-2 0-2 0-4-1 1-1 3-1 5-1z" class="Y"></path><path d="M388 880l1 1c0 1-1 2-2 3-2 0-2 0-3-2 2 0 2-1 4-2z" class="g"></path><path d="M399 864c1 2 1 3 2 5s1 5 3 6c-2 2-3 2-5 2v-1h1c-1-3-1-7-1-10v-2z" class="o"></path><path d="M384 871l1 1 1 1-3 2-3 3h-2v-1c0-2 1-3 2-4l1-1 3-1z" class="I"></path><path d="M383 875l-1-1 1-1 2-1 1 1-3 2z" class="S"></path><path d="M424 858c2-1 3-2 5-3l1 1c-1 2-2 3-4 5h0l-2 3 2 2h0v1c0 1 0 1-1 3h1c0 1 0 1 1 2l-1 2c0 1 0 0-1 1 0 1 0 1-1 1l-1-1c-1 0-2 1-3 0h-3-1l1 2h0c-2 1-2 2-3 3h-1v1l-1-1c-1 1-2 1-3 1h0-2-4c-1 0-3 0-5-1l1-1h2 0l3-2v-2c-2-1-2-4-3-6s-1-3-2-5l1-1c2-2 7-2 11-3 1-1 3-2 5-2h0l2-1 2 2 2 2 2-3z" class="c"></path><path d="M418 865h1c0 2 0 3 1 4v1c0 2 2 3 4 5h1c0 1 0 1-1 1l-1-1c-1 0-2 1-3 0h-3l1-2s1-1 2-1l-2-2v-5z" class="V"></path><path d="M402 868c1 1 2 2 2 3l1 1c-1 2 1 4-1 5 2 2 3 1 6 1 0 2 0 2-1 3h0-2-4c-1 0-3 0-5-1l1-1h2 0l3-2v-2c-2-1-2-4-3-6l1-1z" class="O"></path><path d="M401 879c2 0 7 1 8 2h-2-4c-1 0-3 0-5-1l1-1h2 0z" class="F"></path><path d="M424 858c2-1 3-2 5-3l1 1c-1 2-2 3-4 5h0l-2 3 2 2h0v1c0 1 0 1-1 3h1c0 1 0 1 1 2l-1 2h-1c-3-2-4-3-5-6s1-5 2-7l2-3z" class="a"></path><path d="M424 864h-1v-2l1-1h2l-2 3z" class="X"></path><path d="M416 858l2-1 2 2c-3 4-5 3-8 4s-4 2-6 4h-1c-1 0-2 0-3 1l-1 1c-1-2-1-3-2-5l1-1c2-2 7-2 11-3 1-1 3-2 5-2h0z" class="d"></path><path d="M416 858h0v2c-1 0-2 1-4 0h-1c1-1 3-2 5-2z" class="S"></path><path d="M410 878c-1-1-2-2-2-3v-2c2-4 6-6 10-8v5l2 2c-1 0-2 1-2 1l-1 2h-1l1 2h0c-2 1-2 2-3 3h-1v1l-1-1c-1 1-2 1-3 1 1-1 1-1 1-3z" class="h"></path><path d="M413 880l-1-1v-4c0-1-1-1-2-1 1-1 2-2 2-3h3c1-1 1-1 2-1v3h1l-1 2h-1l1 2h0c-2 1-2 2-3 3h-1z" class="J"></path><path d="M416 875h-2 0l1-2h2 1l-1 2h-1z" class="a"></path><path d="M427 811c2 2 3 4 6 6 1 0 2 1 4 1l1 3h-2v1l-3 3v1 1h-2v1h1c0 3-3 6-4 8-2 3-4 7-6 9-1-1-1-1-1-2h1v-2h-2c-1 2-1 4-3 5 0-1-1-1-1-2h1l1-1v-1c-2 1-2 1-3 2l-1 2v1h-1c0 1 0 1-1 2v1c-1 1-2 2-2 3-1 0-1 0-1 1h-2l-1 1c-2 2-4 1-5 3h-1l-3-2 1-1v-1l-2 2c-1 1-1 1-2 1l-1 1h0c2-3 4-5 7-8-1-2 0-2 0-4 1-1 2-3 3-4 0 1 1 2 1 3l2-1c0-1 0-1 1-2h1c1-2 2-3 4-5l1-1h0v-1c0-1 0-1 1-2 1 0 2-1 3-2v-1l1-2c0 1 0 1 1 1l1-1-1-2h-1l1-1 1-1 1 1s1-1 1-2 0-2-1-3l1-1v-1c0-1 0-1 1-2 0-1 1-1 1-2v-1h0 2l1-2z" class="J"></path><path d="M403 842c0 1 1 2 1 3-1 2-3 4-4 5-1-2 0-2 0-4 1-1 2-3 3-4zm24-31c2 2 3 4 6 6 1 0 2 1 4 1l1 3h-2v1l-3 3v1 1h-2v1h1c0 3-3 6-4 8-2 3-4 7-6 9-1-1-1-1-1-2h1v-2h-2l1-2c1-1 2-2 2-3s1-1 2-2v-3h-1l-1 1v3h-1c0 1 0 2-1 3h0c-2 0-2-1-3-2v-1h-1l1-1c0-1 1-1 1-2l1-1 1-2c0-1 1-2 1-2l1-1 1-2 1-1v-3c1-1 2-2 1-4h-1v2c-1 2-2 3-3 5 0-1 0-2-1-3l1-1v-1c0-1 0-1 1-2 0-1 1-1 1-2v-1h0 2l1-2z" class="f"></path><path d="M644 154h1c3-2 4-4 7-5h1l7 6c2 0 3 1 5 2l4 2 3 1c1 1 2 1 3 2 2 1 6 2 8 2 1 1 1 1 2 1l4 2c2 0 3 1 4 1v1c1 0 3 1 4 2h2v1l1 2c-2 0-4-1-7 0l3 1h3l1 1c2 1 4 1 5 3v2l-1 1c1 0 2-1 3 0s1 0 1 1l-1 6v2l1-1c0-1 0-2 2-3h5 0c1 1 2 1 2 1 4 2 5 3 7 6 0 2-1 5-3 6-1 2-1 5-4 6v1c1 0 1 0 1 1h1l-4-1h-2c-3-1-5-1-7-3h0c-1 0-2-1-3-1h-1v1l-7-4-1 1-17-7-12-6-2 2h-1l-1-1c-1 0-1 0-2-1-1 0-2 0-3-1l-2-2c-1 0-1-1-2-1 0-2 0-3 1-4-1-2-3-3-4-5l-6-9-3-4-2-2c1-1 2-1 3-1l1-1h1c1 1 0 1 1 1l1-1c0-1-1-1-1-1l-2 1-1-1 3-3z" class="V"></path><path d="M713 195l2 2c0 2-1 3-2 4h-1l-1-3 2-3z" class="p"></path><path d="M691 191h0c1 0 2 1 2 1 2 1 2 2 4 2v-2h1l-1 2c1 2 2 2 4 3h-3l6 5h0c-3-1-6-4-8-5-2 0-3-1-4-2h1c0-1 0-1-1-1l-2-2 1-1z" class="m"></path><path d="M703 193h1l2-2h1c2 2 4 3 6 4l-2 3h-1l-1 1h-1l-1-1c-1 0-1 0-2-1h-1c-1 0-2-1-3-1h-1v-1c1-1 2-2 3-2z" class="a"></path><path d="M705 197c0-1 0-2 1-2h2c2 1 2 1 2 3h0l-1 1h-1l-1-1c-1 0-1 0-2-1z" class="j"></path><path d="M708 195c2 1 2 1 2 3h-2v-2-1z" class="X"></path><path d="M694 184c3 1 5 2 8 1 1 1 2 2 3 4l-1 1v2l-1 1c-1 0-2 1-3 2v1h1c1 0 2 1 3 1 1 1 0 0 0 1v1c-1-1-2-1-3-2-2-1-3-1-4-3l1-2v-1c1 0 2 0 2-1-1-1-2 0-4 0 0 0-1-1-1-2h-2c0-2 0-2 1-4z" class="M"></path><path d="M680 187h0c3 1 7 2 10 2l1 2-1 1 2 2c1 0 1 0 1 1h-1-3l-3-1-1 1c0-1-1-1-2-1h0 0l-3-1c-1 0-1-1-2-1l-3-1c-1-1-2-2-2-4l2 2 2-1c1 0 1 0 3-1z" class="U"></path><path d="M673 187l2 2c2 0 4 1 5 2h2c2 1 5 2 7 4l-3-1-1 1c0-1-1-1-2-1h0 0l-3-1c-1 0-1-1-2-1l-3-1c-1-1-2-2-2-4z" class="T"></path><path d="M681 178l1 1h0l-1 1h0c-1 1-1 2-2 2v1 2c2 1 3 1 5 1h1 1c1-1 1-2 2-3l1 1-1 1 1 1v-1l1-1 1 1 2-1h1c-1 2-1 2-1 4h2c0 1 1 2 1 2 2 0 3-1 4 0 0 1-1 1-2 1v1h-1v2c-2 0-2-1-4-2 0 0-1-1-2-1h0l-1-2c-3 0-7-1-10-2h0c-2 0-2-1-3-2v-3c1 0 2-1 2-2l2-2z" class="q"></path><path d="M708 190c0-1 0-2 2-3h5 0c1 1 2 1 2 1 4 2 5 3 7 6 0 2-1 5-3 6v-1c-1-2-2-3-3-4h-1c0 1 1 1 2 3-1 0-2 1-3 1l1-2s-1-1-2-1c-2-2-6-4-7-6z" class="a"></path><path d="M669 159l3 1c1 1 2 1 3 2 2 1 6 2 8 2 1 1 1 1 2 1l4 2c2 0 3 1 4 1v1c1 0 3 1 4 2h2v1l1 2c-2 0-4-1-7 0l3 1h3l1 1c2 1 4 1 5 3v2l-1 1c1 0 2-1 3 0s1 0 1 1l-1 6v2h0-1l-2 2h-1l1-1v-2l1-1c-1-2-2-3-3-4-3 1-5 0-8-1h-1l-2 1-1-1-1 1v1l-1-1 1-1-1-1c-1 1-1 2-2 3h-1-1c-2 0-3 0-5-1v-2-1c1 0 1-1 2-2h0l1-1h0l-1-1 1-1h0c1 1 2 1 3 1s0 0 1 1h1l1-1-16-7c-3-2-7-3-10-4l1-1c2 1 2 1 4 1l-1-1c1-1 1-2 2-3 1 0 2 0 3-1l-2-3z" class="J"></path><path d="M695 177h0c2 0 4 0 6 2l-3 3h0l-1-1-1-1c0 1 0 1-1 2 0 1 0 1-1 2v-1-1c0-1 1-1 1-2l-1-2 1-1z" class="f"></path><path d="M685 165l4 2c2 0 3 1 4 1v1c1 0 3 1 4 2h2v1l1 2c-2 0-4-1-7 0h-2s-2-1-3-1v-1c-1 0-2-1-2-1l-2-2v-1l1-1v-2z" class="U"></path><path d="M686 171h3l1 1c2 1 2 0 4 0 1 0 1 1 3 1v-1c-1 0-2-1-3-1l-2-1 1-1c1 0 3 1 4 2h2v1l1 2c-2 0-4-1-7 0h-2s-2-1-3-1v-1c-1 0-2-1-2-1z" class="f"></path><path d="M669 159l3 1c1 1 2 1 3 2 2 1 6 2 8 2 1 1 1 1 2 1v2l-1 1v1l-1 1c0 1 1 2 1 3h0c-2 0-2-1-4-1l-1-1-3-1h-1c-2-1-2-1-4-1 0 1 1 1 1 2-3-2-7-3-10-4l1-1c2 1 2 1 4 1l-1-1c1-1 1-2 2-3 1 0 2 0 3-1l-2-3z" class="m"></path><path d="M675 170v-3h2 2l1 2-1 1-1-1-2 1h-1z" class="i"></path><path d="M669 159l3 1c1 1 2 1 3 2 2 1 6 2 8 2 1 1 1 1 2 1v2l-1 1h0c-1-1-2-1-2-1-1-1-1 0-2-1l-1-1c-2-1-2-1-3 0l-2-1h-1v1c0 2 0 2-1 3-2-1-2-1-2-2-1 0-2 1-2 1h-1l-1-1c1-1 1-2 2-3 1 0 2 0 3-1l-2-3z" class="X"></path><path d="M644 154h1c3-2 4-4 7-5h1l7 6c2 0 3 1 5 2l4 2 2 3c-1 1-2 1-3 1-1 1-1 2-2 3l1 1c-2 0-2 0-4-1l-1 1c3 1 7 2 10 4l16 7-1 1h-1c-1-1 0-1-1-1s-2 0-3-1h0l-1 1-2 2c0 1-1 2-2 2v3c1 1 1 2 3 2-2 1-2 1-3 1l-2 1-2-2c0 2 1 3 2 4l3 1c1 0 1 1 2 1l3 1 12 6-1 1-17-7-12-6-2 2h-1l-1-1c-1 0-1 0-2-1-1 0-2 0-3-1l-2-2c-1 0-1-1-2-1 0-2 0-3 1-4-1-2-3-3-4-5l-6-9-3-4-2-2c1-1 2-1 3-1l1-1h1c1 1 0 1 1 1l1-1c0-1-1-1-1-1l-2 1-1-1 3-3z" class="J"></path><path d="M663 162c2 1 3 1 5 1h0c-1 1-1 2-2 3h-2l-2-2 1-2z" class="a"></path><path d="M660 155c2 0 3 1 5 2l4 2 2 3c-1 1-2 1-3 1h0c-2 0-3 0-5-1l1-1c-1-2-1-2-1-3-1-1-2-3-3-3z" class="g"></path><path d="M665 157l4 2 2 3c-1 1-2 1-3 1h0c-1-3-2-4-3-6z" class="U"></path><path d="M647 155h2c1 0 1 0 2-1 0-1 0-1-1-1l1-1v-2h1v3l2 2-1 1h1 0c0-1 1-1 1-1h1l1 1c2 0 4 1 5 3h-2-2c-1 1-1 2-2 3-3-1-4-1-7-1l-5-2 1-1 2-3z" class="T"></path><path d="M647 155c1 1 1 1 1 2h0c1 1 1 3 1 4l-5-2 1-1 2-3z" class="P"></path><path d="M641 159l1-1h1c1 1 0 1 1 1l5 2c3 0 4 0 7 1 1-1 1-2 2-3l2 1v2c1 1 0 1 0 2h-1c-1-1-2-1-3-1h0l6 4c3 1 7 2 10 4l16 7-1 1h-1c-1-1 0-1-1-1s-2 0-3-1h0l-3-1c-4-3-7-4-11-5h0c1 1 1 1 2 1v1h-1l-1 1h-2-1l-1-1s-1 0-1-1h-1c-1 1 0 0-1 0l-1-1c-1 1-2 1-3 2v-2h0c-1-1 0-1-1-1l-1-1c-1 1-1 2-2 3h-1c-1-1-2-1-3-3 0-1 0 0-1-1h0l-1-2-1-1c-1-1-1-2-2-3l-3-3z" class="t"></path><path d="M658 159l2 1v2l-1 1-3-1c1-1 1-2 2-3z" class="a"></path><path d="M649 169l3-2h-1v-2h-1c-1-1-1 0-1-1-1 0-2-1-2-1-1 0-2-1-3-2h1 0c5 3 11 4 17 7h0c2 0 4 2 6 3h0c1 1 1 1 2 1v1h-1l-1 1h-2-1l-1-1s-1 0-1-1h-1c-1 1 0 0-1 0l-1-1c-1 1-2 1-3 2v-2h0c-1-1 0-1-1-1l-1-1c-1 1-1 2-2 3h-1c-1-1-2-1-3-3z" class="V"></path><path d="M668 171c4 1 7 2 11 5l3 1-1 1-2 2c0 1-1 2-2 2v3c1 1 1 2 3 2-2 1-2 1-3 1l-2 1-2-2v-1h-2c-1 1-1 1-2 0h1l-1-1c-1-1-2-1-3-2-2 0-2-1-4-2-1-1-2-1-3-2v-1c1 0 1-1 1-2h-1-1l-1-1v-2h0c1-1 2-1 3-2l1 1c1 0 0 1 1 0h1c0 1 1 1 1 1l1 1h1 2l1-1h1v-1c-1 0-1 0-2-1h0z" class="g"></path><path d="M679 176l3 1-1 1-2 2c-1 0-2-1-2-1 0-1 1-2 2-3z" class="U"></path><path d="M673 186c1 0 1 1 2 0v-1c0-1 1-2 2-3v3c1 1 1 2 3 2-2 1-2 1-3 1l-2 1-2-2v-1z" class="i"></path><path d="M640 162l-2-2c1-1 2-1 3-1l3 3c1 1 1 2 2 3l1 1 1 2h0c1 1 1 0 1 1 1 2 2 2 3 3h1c1-1 1-2 2-3l1 1c1 0 0 0 1 1h0v2h0v2l1 1h1 1c0 1 0 2-1 2v1c1 1 2 1 3 2 2 1 2 2 4 2 1 1 2 1 3 2l1 1h-1c1 1 1 1 2 0h2v1c0 2 1 3 2 4l3 1c1 0 1 1 2 1l3 1 12 6-1 1-17-7-12-6-2 2h-1l-1-1c-1 0-1 0-2-1-1 0-2 0-3-1l-2-2c-1 0-1-1-2-1 0-2 0-3 1-4-1-2-3-3-4-5l-6-9-3-4z" class="U"></path><path d="M675 191c-2-1-5-2-7-3-1-1-2-1-4-2h1c2 0 2 0 3 1l1-1c1 1 1 1 2 0h2v1c0 2 1 3 2 4z" class="J"></path><path d="M657 173v2l1 1h1 1c0 1 0 2-1 2v1l-1 1-2-2-1 1c-1-1-2-1-2-2v-3h1l1 2h1 0v-2h0l1-1z" class="j"></path><path d="M659 179c1 1 2 1 3 2 2 1 2 2 4 2 1 1 2 1 3 2l1 1h-1l-1 1c-1-1-1-1-3-1 0-1-1-1-2-2h0l-1-1c0-1-1-1-2-1s-2-1-2-2l1-1z" class="X"></path><path d="M659 188c-1 0-2 0-3-1l-2-2c-1 0-1-1-2-1 0-2 0-3 1-4 4 3 7 6 12 8l-2 2h-1l-1-1c-1 0-1 0-2-1z" class="B"></path><defs><linearGradient id="X" x1="405.39" y1="163.077" x2="400.667" y2="139.772" xlink:href="#B"><stop offset="0" stop-color="#b9ac9a"></stop><stop offset="1" stop-color="#dbd0c1"></stop></linearGradient></defs><path fill="url(#X)" d="M360 146h130l-1 5-2 2c0 2-2 6-3 7v-1l-1-1v1l-2-1-1-2-1-1h-2c-1 0-3-1-4-1-2 0-4 1-6 3-1-1-2-1-2-2l-2 1c-1-1-2-1-3-1 0 0-1 1-3 1l-3-1-1-2c1 0 1 0 1-1-2 0-3 0-5-1l-2-2c-1 0-2 0-3 1l-2-1h0 0-9l-1 2v1s0 1-1 1l-1 3-4 4v1c-1 1-1 2-1 3l-1 1-1 1-1 1h-1v3l-2 2c1 1 0 1 1 3h-1v3c1 1 2 1 3 3l-1 1v1l-2 2-1 1c-1 0-3 1-4 1v1h4 0l1 1h-2v3l-1 1h1 1c-1 1-3 1-4 1 0-1 0-1-1-2v1c0 1-1 1-1 1 0 3 1 3 2 5l-1 1c1 1 1 1 0 2-2-2-3-6-5-9-1-1-2-3-2-4l-1-1-3-3v-1h-1l-7-7-3-4c0-1-1-1-1-2-3-3-7-4-10-6-1-1-1-1-2-1s-2-1-2-1c-1 1-2 0-3 1h-2s-1 0-1 1c-3 0-8-1-11-2l1-1c-1 0-3 0-3-1-1-1 0-3 1-4v-1c2-4 5-5 7-9h-4l-1-1z"></path><path d="M408 151l1-1c3-1 6-1 9-1l-3 3v2c-1-2-2-3-3-4-1 1-2 1-3 1h-1z" class="f"></path><path d="M408 151h1c1 0 2 0 3-1 1 1 2 2 3 4l-3 4c0 1-1 1-2 2 0-2 0-2-1-3-1-2-1-3-2-5h1v-1z" class="q"></path><path d="M381 160v-1-1h1 0c1 0 2 0 3 1s1 2 2 2c3 3 6 6 8 10h-3c0 2 1 2 2 4l1 1-1 1-3-4c0-1-1-1-1-2-3-3-7-4-10-6-1-1-1-1-2-1s-2-1-2-1c1-1 1-1 2-1h3 0c2 0 3 1 4 2s1 0 1 1h1l2 2 1-2-2-2h-2-1v-1c-1-2-2-2-4-2z" class="J"></path><path d="M447 149c3-1 6-1 9-1h15c5 0 11-1 16 0h1 1c-1 2-2 4-2 5 0 2-2 6-3 7v-1l-1-1v1l-2-1-1-2-1-1h-2c-1 0-3-1-4-1-2 0-4 1-6 3-1-1-2-1-2-2l-2 1c-1-1-2-1-3-1 0 0-1 1-3 1l-3-1-1-2c1 0 1 0 1-1-2 0-3 0-5-1l-2-2z" class="l"></path><path d="M487 148h1 1c-1 2-2 4-2 5 0 2-2 6-3 7v-1l-1-1v1l-2-1-1-2-1-1h-2c-2-2-2-2-3-2l-1-1h2l1-2h5c2 0 4 0 6-1v-1z" class="F"></path><path d="M476 150h5c0 1 0 2 2 3h2v1c-2 1-3 1-6 1h-2c-2-2-2-2-3-2l-1-1h2l1-2z" class="D"></path><path d="M459 154h-2v-2c-1-1-4 0-5-1v-1c4 0 9-1 13 1 2 0 5-1 7-1h4l-1 2h-2l1 1c1 0 1 0 3 2-1 0-3-1-4-1-2 0-4 1-6 3-1-1-2-1-2-2l-2 1c-1-1-2-1-3-1l1-1h-2z" class="Z"></path><path d="M465 151c2 0 5-1 7-1-1 1-1 3-3 3l-1-1c-1 0-2-1-3-1z" class="P"></path><path d="M459 154v-2s1-1 2-1c1 1 2 1 4 2h0 1 1l-1 2c-1 0-1-1-2-1s-1 0-1-1c-1 0-2 1-2 1h-2z" class="D"></path><path d="M381 160l-1-1h-3c-2-3-3-4-3-7l2-1h5c3 0 5 1 8 1h1c2 1 5 0 7 0h0c1 0 2 0 4 1 2-1 4-1 7-1h-1c1 2 1 3 2 5 1 1 1 1 1 3 0 1-1 2-1 3v3 1c-2 2-7 5-7 8-1-1-1-2-1-2h-1l-1 1 1 1v2 1h1v2c-1-1-3-2-3-3-1-2-1-3-2-5l-1-1c-2-4-5-7-8-10-1 0-1-1-2-2s-2-1-3-1h0-1v1 1z" class="a"></path><path d="M401 163h-1c-1-2-1-2 0-4h2c0 2 0 3-1 4z" class="U"></path><path d="M408 152h-1c-1 2-1 3-2 5-1 3-1 6-4 8l-2-1 2-1c1-1 1-2 1-4v-1c-1-2-2-2-3-2h-1v-1h-1l-1 1-1-1c0-1 1-1 2-2h3 1c2-1 4-1 7-1z" class="n"></path><path d="M407 152c1 2 1 3 2 5 1 1 1 1 1 3 0 1-1 2-1 3v3 1c-2 2-7 5-7 8-1-1-1-2-1-2h-1l-1 1 1 1v2 1h1v2c-1-1-3-2-3-3-1-2-1-3-2-5l-1-1c-2-4-5-7-8-10-1 0-1-1-2-2 4 1 6 2 9 4 2 0 3 1 5 1l2 1c3-2 3-5 4-8 1-2 1-3 2-5z" class="O"></path><path d="M407 152c1 2 1 3 2 5 1 1 1 1 1 3 0 1-1 2-1 3l-1 1-1-1-1-1c1-1 2-2 3-2l-1-1h-1c-1-1-1-1-2-1v-1c1-2 1-3 2-5z" class="t"></path><path d="M408 164l1-1v3 1c-2 2-7 5-7 8-1-1-1-2-1-2h-1l-1 1 1 1v2 1h1v2c-1-1-3-2-3-3-1-2-1-3-2-5v-1c0-3-1-4-3-5v-1-1c1 1 1 2 3 3v-1h1c0 1 1 2 1 3 1-1 1-2 2-2v1c1 2 1 0 3 1l-1 1h1c2-2 4-3 5-6z" class="J"></path><path d="M426 149c2 0 5-1 7 0l-1 2v1s0 1-1 1l-1 3-4 4v1c-1 1-1 2-1 3l-1 1-1 1-1 1h-1v3l-2 2c1 1 0 1 1 3h-1v3c1 1 2 1 3 3l-1 1v1l-2 2-1 1c-1 0-3 1-4 1v1h4 0l1 1h-2v3l-1 1h1 1c-1 1-3 1-4 1 0-1 0-1-1-2v1c0 1-1 1-1 1 0 3 1 3 2 5l-1 1c1 1 1 1 0 2-2-2-3-6-5-9-1-1-2-3-2-4l-1-1-3-3v-1h-1l-7-7 1-1-1-1c-1-2-2-2-2-4h3l1 1c1 2 1 3 2 5 0 1 2 2 3 3v-2h-1v-1-2l-1-1 1-1h1s0 1 1 2c0-3 5-6 7-8v-1-3c0-1 1-2 1-3 1-1 2-1 2-2l3-4v-2l3-3 3 2s1-1 1-2h4z" class="e"></path><path d="M412 158c1 0 2 1 4 1-3 3-4 5-7 8v-1-3c0-1 1-2 1-3 1-1 2-1 2-2z" class="o"></path><path d="M419 154l2 2c1 0 3 0 5 2-1 1-1 1-1 2-1 1-1 1-1 2h-1c-1-2-1-2-2-3-1 1-1 0-1 1l-1-1 2-2h0l-1-1-1 1-1-1h0l1-2z" class="s"></path><path d="M418 149l3 2-2 1 1 1-1 1-1 2-2 3c-2 0-3-1-4-1l3-4v-2l3-3z" class="r"></path><path d="M418 149l3 2-2 1-2 2-2-2 3-3z" class="p"></path><path d="M403 179v-1-2-1c1-3 4-5 6-7l2-2c2-1 3-2 4-4l1 1c-2 2-2 4-5 4v1c0 1-1 1-2 2l1 1h1c-2 2-3 4-5 7-1 1-1 1-3 1z" class="T"></path><path d="M426 149c2 0 5-1 7 0l-1 2v1s0 1-1 1l-1 3-4 4v-1l-1 1c0-1 0-1 1-2-2-2-4-2-5-2l-2-2 1-1-1-1 2-1s1-1 1-2h4z" class="J"></path><path d="M426 149c2 0 5-1 7 0l-1 2c-2 0-3 1-5 1l-1-3z" class="g"></path><path d="M420 153h5 4 2l-1 3-4 4v-1l-1 1c0-1 0-1 1-2-2-2-4-2-5-2l-2-2 1-1z" class="q"></path><path d="M425 160l1-1v1 1c-1 1-1 2-1 3l-1 1-1 1-1 1h-1v3l-2 2c1 1 0 1 1 3h-1s-1 1-1 2c-1 1 1 2-1 3 0 1-1 1-1 2v1h-1c-2-1-4 1-6 0h0 1c1 0 2-1 3-1l1-2c-1 1-2 1-2 2-2-1-3-1-5-2 0-1 0-2-1-2 2-3 3-5 5-7h-1l-1-1c1-1 2-1 2-2v-1c3 0 3-2 5-4h0c1 1 1 0 1 1h3 1c0-1 1-1 2-2h0 1c0-1 0-1 1-2z" class="t"></path><path d="M395 171l1 1c1 2 1 3 2 5 0 1 2 2 3 3v-2l1 1v2h0l6 7v-1c0-3-1-4-3-6l-1-1-1-1c2 0 2 0 3-1 1 0 1 1 1 2 2 1 3 1 5 2 0-1 1-1 2-2l-1 2c-1 0-2 1-3 1h-1 0c2 1 4-1 6 0h1v-1c0-1 1-1 1-2 2-1 0-2 1-3 0-1 1-2 1-2v3c1 1 2 1 3 3l-1 1v1l-2 2-1 1c-1 0-3 1-4 1v1h4 0l1 1h-2v3l-1 1h1 1c-1 1-3 1-4 1 0-1 0-1-1-2v1c0 1-1 1-1 1 0 3 1 3 2 5l-1 1c1 1 1 1 0 2-2-2-3-6-5-9-1-1-2-3-2-4l-1-1-3-3v-1h-1l-7-7 1-1-1-1c-1-2-2-2-2-4h3z" class="f"></path><path d="M419 178c1 1 2 1 3 3l-1 1v1l-2 2-1-2-1-1 2-4z" class="N"></path><path d="M418 183l1-2c1 0 1 1 2 1v1l-2 2-1-2z" class="C"></path><path d="M417 182l1 1 1 2-1 1c-1 0-3 1-4 1v1l-2 2-1-1h1c-1-1-1-2-2-3h1c2-1 4-1 6-3v-1z" class="L"></path><path d="M550 415h1c0-2 0-2 1-3 3 3-1 11 1 15 1 3 1 5 1 7v7l1-2v1l1 1h1v2 41 22h6v1l2 2c1 1 1 1 2 1 0 2 0 2 1 3v2c0 2 1 4 1 6v7 3l-2-2c-1-1-2 0-4 0 1 1 2 1 2 2s0 2-1 3h-1l-1 1 1 22v9h0-1v-1 2c-1-1-1-2-1-3h1v-3c-1-1-3 0-5-1 0 0-1-1-2-1 1-1 2-2 4-3h1l-1-2v-3h-3c-1 1-1 2-3 2v1l-3-1c0 2 0 5-1 7l1 38-1 1v5c0 5-1 9-1 14v1 3c0 4-1 9-1 13l-1 2-1-10h0v4 4l-1 1v2c0-3-1-7 0-10v-12-6c-1-2 0-5-1-8v-43-41c1-6 0-13 0-19v-45l-1-1v-7c0-1-2-3-3-4 2-1 2-1 3-3l1-1v-7l4 1h0 2l1 1v-3c0-5-1-10 0-15z" class="h"></path><path d="M546 434h0v3l1-1h0v6l-1 1-1-1h0c0-3 0-5 1-8z" class="f"></path><path d="M542 439l1-1v16l-1-1v-7c0-1-2-3-3-4 2-1 2-1 3-3z" class="N"></path><path d="M545 627c0-1 0-3 1-4v-1s0-1-1-2v-1l1 1c2 2 1 6 1 8v-2c1-2 0-3 1-4 0 4-1 9-1 13l-1 2-1-10z" class="M"></path><path d="M549 516l1 14v4l1-3v2l1-1 4 2 1 1c1-1 2-1 3 0v1l-1 1h-4-1l-2 1h1v5h-1c-1-1-1-1-2 0l-1 1v-28z" class="O"></path><path d="M550 530v4l1-3v2l1-1 4 2 1 1c1-1 2-1 3 0v1c-3-2-7 0-10-1v-5z" class="B"></path><defs><linearGradient id="Y" x1="526.292" y1="502.218" x2="573.708" y2="466.282" xlink:href="#B"><stop offset="0" stop-color="#222226"></stop><stop offset="1" stop-color="#3e3d3a"></stop></linearGradient></defs><path fill="url(#Y)" d="M547 432h2l1 1c3 4 1 11 3 15-3 4-1 12-1 17l1 9c-1 3-1 12 0 14-1 8-1 16-1 24 0 7-1 13 0 20l-1 1v-2l-1 3v-4l-1-14v-82l-2-2h0z"></path><defs><linearGradient id="Z" x1="539.962" y1="484.361" x2="565.997" y2="477.286" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#595a5a"></stop></linearGradient></defs><path fill="url(#Z)" d="M550 415h1c0-2 0-2 1-3 3 3-1 11 1 15 1 3 1 5 1 7v7l1-2v1l1 1h1v2 41 22 17c0 4 1 8 0 12l-1-1-4-2c-1-7 0-13 0-20 0-8 0-16 1-24-1-2-1-11 0-14l-1-9c0-5-2-13 1-17-2-4 0-11-3-15v-3c0-5-1-10 0-15z"></path><path d="M553 448c1 3 0 6 0 10 1 4 2 10 0 14v2l-1-9c0-5-2-13 1-17z" class="O"></path><path d="M557 506h6v1l2 2c1 1 1 1 2 1 0 2 0 2 1 3v2c0 2 1 4 1 6v7 3l-2-2c-1-1-2 0-4 0 1 1 2 1 2 2s0 2-1 3h-1l-1 1 1 22v9h0-1v-1 2c-1-1-1-2-1-3h1v-3c-1-1-3 0-5-1 0 0-1-1-2-1 1-1 2-2 4-3h1l-1-2v-3h-3c-1 1-1 2-3 2v1l-3-1c0 2 0 5-1 7v-16l1-1c1-1 1-1 2 0h1v-5h-1l2-1h1 4l1-1v-1c-1-1-2-1-3 0 1-4 0-8 0-12v-17z" class="H"></path><path d="M550 553v-5h1c1 1 1 1 3 1 1 0 1 0 2-1l1 1v1h-6v1h5c-1 1-1 2-3 2v1l-3-1z" class="C"></path><path d="M549 544l1-1c1-1 1-1 2 0h1v-5h-1l2-1h1l1 1c1 0 0 0 1 1h0c1 1 1 1 1 2v1h-1v-1h-1v2l1 1 1-1c0 1 0 2 1 3v1c-1 1-1 1-3 1h-5-1v5c0 2 0 5-1 7v-16z" class="F"></path><path d="M563 507l2 2c1 1 1 1 2 1 0 2 0 2 1 3v2c0 2 1 4 1 6v7 3l-2-2c-1-1-2 0-4 0v-19-3z" class="D"></path><path d="M566 521h1v7l-1 1c-1-3 0-6 0-8z" class="Q"></path><path d="M563 507l2 2c1 1 1 1 2 1 0 2 0 2 1 3v2c0 2 0 4-1 6h-1c-1-2-1-3-1-5h1 1v-2h-1-1v-3l-2-1v-3z" class="P"></path><defs><linearGradient id="a" x1="655.09" y1="460.762" x2="718.952" y2="441.056" xlink:href="#B"><stop offset="0" stop-color="#998b78"></stop><stop offset="1" stop-color="#d8cab4"></stop></linearGradient></defs><path fill="url(#a)" d="M693 419l5-24c2-5 3-10 5-15 1 14 0 29 0 44v8 2l1 1h1-2v56c2 0 8-1 10 0l-10 1c0 1 1 7 0 8 0 1-1 2-1 3h-1v-1l-1 8h-1l-8 3-3-8h0c-2-2-5-4-7-6h-2c-1-1 0-1-1-1s-2 0-2-1c-4 0-6 0-9 1v-2-1l-2 2c-1-1 0-3 0-4v-1c-1-2-2-3-3-4v-2h0v-2h1l1-1-1-1-3 2-2-2h-1c-1 0-1 0-2-1h0 1v-1c2 1 3 1 5 1v1c0-3 0-3-1-5l5-5-1-1c1-1 3-2 4-3h-2c-1 0-2 0-3 1l-1-1c3-3 6-5 8-7 6-6 10-13 14-19l9-23z"></path><path d="M673 494v-1l1-1 1 1-1 1c-1 0 0 1-1 0z" class="q"></path><path d="M681 499c1 0 3 1 3 1h3c1 0 2 1 3 1 0 2-1 3-2 4-2-2-5-4-7-6z" class="f"></path><path d="M675 493l1 1v-1l1 1v1s-1 1-1 2c-4 0-6 0-9 1v-2h1c1-1 1-1 2-1 1-1 1 0 2 0 1 1 1 1 2 1l1-1c-1 0-1 0-2-1 1 1 0 0 1 0l1-1z" class="h"></path><path d="M664 483h1c1 0 1 1 2 2l3 3c-1 0-2 0-2 1l-3 3c-1-2-2-3-3-4v-2h0v-2h1l1-1z" class="M"></path><path d="M664 483h1c1 0 1 1 2 2l-2 2h-1c-1-1-1-1-2-1h0v-2h1l1-1z" class="T"></path><path d="M693 419v2h1 0c1 0 0-1 1 0-3 17-9 36-20 49a30.44 30.44 0 0 1-8 8c-1 2-3 3-4 4l-3 2-2-2h-1c-1 0-1 0-2-1h0 1v-1c2 1 3 1 5 1v1c0-3 0-3-1-5l5-5-1-1c1-1 3-2 4-3h-2c-1 0-2 0-3 1l-1-1c3-3 6-5 8-7 6-6 10-13 14-19l9-23z" class="B"></path><path d="M661 482c2-1 3-2 5-4h1c-1 2-3 3-4 4l-3 2-2-2h-1c-1 0-1 0-2-1h0 1v-1c2 1 3 1 5 1v1z" class="F"></path><path d="M685 448c0 2-1 4-2 7s-3 6-6 8c-2 1-2 3-4 2h-1 1c4-4 7-9 10-14l2-3z" class="c"></path><defs><linearGradient id="b" x1="690.502" y1="439.791" x2="684.839" y2="429.338" xlink:href="#B"><stop offset="0" stop-color="#8f8c8c"></stop><stop offset="1" stop-color="#b0aea7"></stop></linearGradient></defs><path fill="url(#b)" d="M693 419v2h1 0c1 0 0-1 1 0-1 1-1 2-2 3l-3 12c-2 4-4 8-5 12l-2 3c0-3 1-5 2-8-1 0-1 0-1-1l9-23z"></path><path d="M662 468c3-3 6-5 8-7 6-6 10-13 14-19 0 1 0 1 1 1-1 3-2 5-2 8-3 5-6 10-10 14h-1v1h0c1 1 1 2 2 4l-1 1h-1c-1-1-2-2-3-2h-1l-3 3-1-1c1-1 3-2 4-3h-2c-1 0-2 0-3 1l-1-1z" class="h"></path><defs><linearGradient id="c" x1="667.245" y1="568.414" x2="704.548" y2="570.154" xlink:href="#B"><stop offset="0" stop-color="#ab9b84"></stop><stop offset="1" stop-color="#c9b9a3"></stop></linearGradient></defs><path fill="url(#c)" d="M699 510h1l1-8v1h1c0-1 1-2 1-3v66h9 1l-10 1-1 76c-6-14-9-29-15-44s-15-28-29-38c3 0 7 4 10 6l4 5v-1c-1-1-2-3-4-5-2-1-3-2-3-5h0c1 0 3 1 4 2h2v-1h1c1 2 0 3 2 4h0c1 1 1 1 2 1l2 2h0c-2-5-5-9-9-13l-4-4-1-2-1 1c-2-1-2-1-3-1h-1c-3 0-1-2-2-4 0-1-2-2-3-3h3c0-1 0-1 1-1l1 1 1-1 1-1c0-1-5-4-6-5 0-1 0-1-1-2 3 1 5 3 7 4l2 2c2-1 2 0 3 0h1 1l2-1c1 0 1 0 2-1h4l1-1c1 0 2 0 3-1s2-1 3-1h0v-3l3-3h0c1-2 2-5 3-7l1-2h1v-7l1 2 1-1c1-1 2-1 4-2l1 1c0-1 1-2 1-3z"></path><path d="M672 538h4l1-1c1 0 2 0 3-1s2-1 3-1c-1 2-2 3-3 4h-1c-1 1-2 1-3 2-2 0-5 1-8 0l-1-1h1l2-1c1 0 1 0 2-1zm19-18l1 1h-1s0 2-1 3h1c1-2 2-2 4-2-1 2-2 5-5 6-1 1-3 1-4 1 1-2 2-5 3-7l1-2h1z" class="T"></path><path d="M686 529v1c1 1 2 1 3 3-2 2-3 4-6 5-1 1-1 2-3 1 1-1 2-2 3-4h0v-3l3-3z" class="V"></path><path d="M686 529v1c0 2 0 3-2 5h-1v-3l3-3z" class="T"></path><path d="M691 520v-7l1 2h4c0 2 1 3 0 5l-1 1v1c-2 0-3 0-4 2h-1c1-1 1-3 1-3h1l-1-1z" class="n"></path><path d="M665 561h0c1 0 3 1 4 2s1 2 2 2c1 3 4 5 6 6h1c1 2 1 3 1 4 0 2 1 3 0 5h0l-7-8v-1c-1-1-2-3-4-5-2-1-3-2-3-5z" class="T"></path><path d="M665 561h0c1 0 3 1 4 2s1 2 2 2c1 3 4 5 6 6h1c1 2 1 3 1 4l-8-7c-2-3-4-5-6-7z" class="a"></path><defs><linearGradient id="d" x1="665.58" y1="541.699" x2="684.026" y2="594.917" xlink:href="#B"><stop offset="0" stop-color="#101112"></stop><stop offset="1" stop-color="#393938"></stop></linearGradient></defs><path fill="url(#d)" d="M654 534c3 1 5 3 7 4l2 2c3 2 5 4 7 6 13 12 19 32 23 49 1 3 3 8 3 11v1c-3-6-5-12-7-18-3-7-7-14-11-20-2-5-5-9-9-13l-4-4-1-2-1 1c-2-1-2-1-3-1h-1c-3 0-1-2-2-4 0-1-2-2-3-3h3c0-1 0-1 1-1l1 1 1-1 1-1c0-1-5-4-6-5 0-1 0-1-1-2z"></path><path d="M654 534c3 1 5 3 7 4l2 2c3 2 5 4 7 6h0c-2 0-3-2-5-3-2 0-5 0-7 1h-1v1c1 1 1 1 2 1 4 0 8 3 10 5h0-2l1 1c1 1 1 2 1 3v1h0l-4-4-1-2-1 1c-2-1-2-1-3-1h-1c-3 0-1-2-2-4 0-1-2-2-3-3h3c0-1 0-1 1-1l1 1 1-1 1-1c0-1-5-4-6-5 0-1 0-1-1-2z" class="I"></path><defs><linearGradient id="e" x1="533.052" y1="677.655" x2="573.122" y2="671.472" xlink:href="#B"><stop offset="0" stop-color="#131316"></stop><stop offset="1" stop-color="#373838"></stop></linearGradient></defs><path fill="url(#e)" d="M556 551h3v3l1 2h-1c-2 1-3 2-4 3 1 0 2 1 2 1 2 1 4 0 5 1v3h-1c-1 0-2 0-3-1l1-1-1-1c-1 1-1 1-1 2-1 4-1 9-1 13l1 27-1 104v29c0 6 1 12 0 18v25c0 1 1 5 0 6v1c-1 2-1 6-1 8-1-1-1 0-1-2 0-1 0-2-1-3v3h0-2l-2 2h0-1c-2-2-4-4-5-7-1-5 0-11 0-16v-25c0-1-1-2-1-3v-10c1-3 1-6 1-8l-1-1c0-3-1-6-1-10v1c-1-1-1-29-1-33 0-3-1-9 0-11l1 1v13l1 1c0-3-1-11 1-13h0v6c0-3 0-5 2-8l-1-2c-1-4-1-7-1-10v-14c0-14-1-29 0-43 1 3 0 6 1 8v6 12c-1 3 0 7 0 10v-2l1-1v-4-4h0l1 10 1-2c0-4 1-9 1-13v-3-1c0-5 1-9 1-14v-5l1-1-1-38c1-2 1-5 1-7l3 1v-1c2 0 2-1 3-2z"></path><path d="M552 788l1 1v3h0-2l-1-1c0-1 1-2 2-3z" class="k"></path><path d="M552 788c1-3 0-8 0-11 2 1 1 8 1 10 0 0 1 1 1 2h1l-1-4c0-12 1-23 2-34v3 25c0 1 1 5 0 6v1c-1 2-1 6-1 8-1-1-1 0-1-2 0-1 0-2-1-3l-1-1zm4-237h3v3l1 2h-1c-2 1-3 2-4 3l-3 1v16c0 7 0 14-1 21l-1 1-1-38c1-2 1-5 1-7l3 1v-1c2 0 2-1 3-2z" class="B"></path><path d="M552 560c0-1 0-2 1-4h6c-2 1-3 2-4 3l-3 1z" class="I"></path><path d="M556 551h3v3h-6v-1c2 0 2-1 3-2z" class="c"></path><path d="M550 626v-1c1-6 0-13 0-19 1-1 2 0 3 0v1c-1 6 0 11 0 17l-1 20v12 16c0 1-1 2-1 3v9 2l-1 1c-1-2 0-6 0-8v-18-1l-1-13 1-21z" class="L"></path><defs><linearGradient id="f" x1="508.532" y1="750.342" x2="585.468" y2="679.158" xlink:href="#B"><stop offset="0" stop-color="#2a3033"></stop><stop offset="1" stop-color="#6d6c6b"></stop></linearGradient></defs><path fill="url(#f)" d="M548 622v-3-1c0-5 1-9 1-14v-5c1 8 0 18 1 27l-1 21 1 13v41l-1 52v23c0 5 1 10 0 15v3h0-1c-2-2-4-4-5-7-1-5 0-11 0-16v-25c0-1-1-2-1-3v-10c1-3 1-6 1-8l-1-1c0-3-1-6-1-10v1c-1-1-1-29-1-33 0-3-1-9 0-11l1 1v13l1 1c0-3-1-11 1-13h0v6c0-3 0-5 2-8l-1-2c-1-4-1-7-1-10v-14c0-14-1-29 0-43 1 3 0 6 1 8v6 12c-1 3 0 7 0 10v-2l1-1v-4-4h0l1 10 1-2c0-4 1-9 1-13z"></path><path d="M548 622v-3-1c0-5 1-9 1-14v-5c1 8 0 18 1 27l-1 21c0-1 0-2-1-3h0v-6l-1 1v5h0v-7h-1v10-10l1-2c0-4 1-9 1-13z" class="b"></path><path d="M540 682c0-3-1-9 0-11l1 1v13l1 1c0-3-1-11 1-13h0v6 67c0-1-1-2-1-3v-10c1-3 1-6 1-8l-1-1c0-3-1-6-1-10v1c-1-1-1-29-1-33z" class="S"></path><defs><linearGradient id="g" x1="729.163" y1="186.629" x2="769.082" y2="127.858" xlink:href="#B"><stop offset="0" stop-color="#a39178"></stop><stop offset="1" stop-color="#c1b4a1"></stop></linearGradient></defs><path fill="url(#g)" d="M650 146h3c1 1 2 2 3 2l13 1h3c6 0 13 0 19-1 2 0 5 0 7-1v-1l101 1 2 18 2 1c-1 1-1 1-2 1v4l2 1h-1c-1 2 0 5 0 7l1 17c-1 1-1 1-1 2l-1 1c-1-1-2-1-3-2l-8-4c0-1 0 0-1-1-2-2-4-3-6-5-1-1-2-2-3-2 0-1-2 0-2 0-4-4-8-7-12-11h-4c1-1 2-1 4-1v-1l-4-1c-2 0-5 0-6-1-3-1-4-1-7 0-3-1-5-2-7-2h-1c-6-1-11 0-17 0h-1c-2 1-8 3-9 4h-3c-2 0-4 0-6-1 0 1 0 2-1 3-1 0-1-1-2-1-1-1-2-1-3-1v-1h-2c-1-1-3-2-4-2v-1c-1 0-2-1-4-1l-4-2c-1 0-1 0-2-1-2 0-6-1-8-2-1-1-2-1-3-2l-3-1-4-2c-2-1-3-2-5-2l-7-6h-1c-3 1-4 3-7 5h-1l4-4s1-1 1-2l1-1v-1z"></path><path d="M724 168l-4-2c14 0 28 0 42 5-2 0-5 0-6-1-3-1-4-1-7 0-3-1-5-2-7-2h-1c-6-1-11 0-17 0z" class="G"></path><path d="M656 148l13 1c4 1 8 2 12 2 6 2 13 3 20 6h-8-3c-1-1-3-1-4-1-4-1-8-3-12-4-2 0-5 0-7-1-3 0-5-1-7-1l-4-2z" class="N"></path><defs><linearGradient id="h" x1="780.649" y1="177.943" x2="767.001" y2="180.034" xlink:href="#B"><stop offset="0" stop-color="#2f2d2d"></stop><stop offset="1" stop-color="#454645"></stop></linearGradient></defs><path fill="url(#h)" d="M766 172c10 5 18 12 26 19 2 2 5 4 6 6l-8-4c0-1 0 0-1-1-2-2-4-3-6-5-1-1-2-2-3-2 0-1-2 0-2 0-4-4-8-7-12-11h-4c1-1 2-1 4-1v-1z"></path><path d="M693 157h8c6 2 12 7 18 9 1 1 2 2 4 2-2 1-8 3-9 4h-3c-2 0-4 0-6-1 0-3-3-6-4-9h-1c-2-2-4-4-7-5z" class="D"></path><path d="M701 162l1-1c2 3 4 6 6 7s3 2 3 4c-2 0-4 0-6-1 0-3-3-6-4-9z" class="O"></path><path d="M650 146h3c1 1 2 2 3 2l4 2c2 0 4 1 7 1 2 1 5 1 7 1 4 1 8 3 12 4 1 0 3 0 4 1h3c3 1 5 3 7 5h1c1 3 4 6 4 9 0 1 0 2-1 3-1 0-1-1-2-1-1-1-2-1-3-1v-1h-2c-1-1-3-2-4-2v-1c-1 0-2-1-4-1l-4-2c-1 0-1 0-2-1-2 0-6-1-8-2-1-1-2-1-3-2l-3-1-4-2c-2-1-3-2-5-2l-7-6h-1c-3 1-4 3-7 5h-1l4-4s1-1 1-2l1-1v-1z" class="p"></path><path d="M660 150c2 0 4 1 7 1 2 1 5 1 7 1 4 1 8 3 12 4 1 0 3 0 4 1h-2 0l2 2c-1-1-3-1-4-1l-1 1c2 1 3 2 5 3h0l2 2-20-8c-4-2-8-3-12-6z" class="Q"></path><path d="M690 159l-2-2h0 2 3c3 1 5 3 7 5h1c1 3 4 6 4 9 0 1 0 2-1 3-1 0-1-1-2-1-1-1-2-1-3-1v-1h-2c-1-1-3-2-4-2v-1h1 0l-1-1c0-1-1-2-1-3l-2-2h0c-2-1-3-2-5-3l1-1c1 0 3 0 4 1z" class="U"></path><path d="M698 161c2 2 5 6 6 9-1 0-2 0-3-1s-2-1-3-2-1-1-1-2c-1-1-1-1-1-2 0 0 1 0 1-1l1-1z" class="B"></path><path d="M690 162h0c-2-1-3-2-5-3l1-1c1 0 3 0 4 1 3 0 5 1 8 2l-1 1c0 1-1 1-1 1 0 1 0 1 1 2 0 1 0 1 1 2s2 1 3 2l-2 1 1 1h-1-2c-1-1-3-2-4-2v-1h1 0l-1-1c0-1-1-2-1-3l-2-2z" class="F"></path><path d="M690 162l3 1v1c2 1 3 2 5 3 1 1 2 1 3 2l-2 1 1 1h-1-2c-1-1-3-2-4-2v-1h1 0l-1-1c0-1-1-2-1-3l-2-2z" class="j"></path><path d="M570 224l3 3v3 1h1v-1h1c0 2 0 5 1 7 0 4-1 7 1 11v1 10c0 7 0 15-1 22 0 2 0 3-1 5l-1 3v1c0 1-1 2-1 3l-7 13-2 4c-1 0-2 1-2 2v7c-1 10-1 21 0 31v1c0 4 0 6-1 9v1h-1-2v4 2h2v1h2c-1 3-4 4-5 6 1 1 1 1 2 1l-2 1c-1-1-1-2-2-3l-1-2c-1 1-3 1-4 2 0 1-1 2-1 3v-5l-1 3-4-4 1-2c0-1 0-3-1-4l-1 1c-1 0-2 0-3 1l1 2-3 19-1 2h-2v1l-2-2v-4c1 0 1-4 1-5l4-22c2-12 5-26 4-38v-5l1-1v-1c-2-1-3-3-4-4 0-1-1-2-1-2 1-3 3-5 4-8 1-5 2-11 2-16h-1v-6h0v-28c0-3 0-6-1-9h-1v-6-3-3c0 2 1 3 1 4l2 2 1-3c0 1 1 1 1 2l1 1h0c1 2 2 4 3 5-2 2-2 3-2 6l2 1c4-3 7-6 10-8l8-6c1-1 3-2 4-4l-2-3z" class="D"></path><path d="M549 360v-2h9 2v1 2h-2v4 2h2v1h2c-1 3-4 4-5 6 1 1 1 1 2 1l-2 1c-1-1-1-2-2-3l-1-2c-1 1-3 1-4 2 0 1-1 2-1 3v-5c-1-3-1-6-1-9v-1h1v-1z" class="B"></path><path d="M549 360v-2h9v2c-2 2-2 4-2 7h-1v-2h-2-1c-1-1-2-3-3-5z" class="R"></path><path d="M549 360c1 2 2 4 3 5h1c2 2 1 5 2 8l-1-2c-1 1-3 1-4 2 0 1-1 2-1 3v-5c-1-3-1-6-1-9v-1h1v-1z" class="F"></path><path d="M549 360c1 2 2 4 3 5h1c2 2 1 5 2 8l-1-2v-1c-1-1-2-3-4-4h0c-1-1-1-4-1-5v-1z" class="G"></path><path d="M541 227c0 2 1 3 1 4l2 2 1-3c0 1 1 1 1 2l1 1h0c1 2 2 4 3 5-2 2-2 3-2 6v117 1c0 3 0 6 1 9l-1 3-4-4 1-2c0-1 0-3-1-4l-1 1c-1 0-2 0-3 1l1 2-3 19-1 2h-2v1l-2-2v-4c1 0 1-4 1-5l4-22c2-12 5-26 4-38v-5l1-1v-1c-2-1-3-3-4-4 0-1-1-2-1-2 1-3 3-5 4-8 1-5 2-11 2-16h-1v-6h0v-28c0-3 0-6-1-9h-1v-6-3-3z" class="R"></path><path d="M534 379c1 2 1 4 1 6v4 1l-2-2v-4c1 0 1-4 1-5z" class="B"></path><path d="M540 366c1-2 1-7 3-9l2 1v1 9c0-1 0-3-1-4l-1 1c-1 0-2 0-3 1zm1-139c0 2 1 3 1 4l2 2v15h-1c0-3 0-6-1-9h-1v-6-3-3z" class="C"></path><path d="M543 248h1c1 11 1 23 0 34h-1v-6h0v-28zm2 111l1-1c1 1 1 1 1 2v2h1c0 3 0 6 1 9l-1 3-4-4 1-2v-9z" class="G"></path><path d="M724 168c6 0 11-1 17 0h1c2 0 4 1 7 2 3-1 4-1 7 0 1 1 4 1 6 1l4 1v1c-2 0-3 0-4 1h4c4 4 8 7 12 11 0 0 2-1 2 0 1 0 2 1 3 2 2 2 4 3 6 5 1 1 1 0 1 1 1 3 4 4 6 6 2 1 3 2 5 4 1 1 1 5 1 7v2 4c0 2 0 4 1 6 0 0 0 1 1 2h-1c-1-1-1 0-1-1-2-1-3-2-5-3l-2 2c-1 0-2 0-4-1l-1-2h1c-1-1-2-1-4-1h-1-1l-1 1c-1 0-1-1-2-1l1-2c-3-1-7-1-10-2-1 0-1-1-1-1v-2l-1-1-1-1c-1 1-2 5-3 7l1 1c0 2-2 4-4 6l-3 3-4 2-1-1c-1 0-2 0-3 1v-1l-2 3c-1 0-2 0-3-1h-1-2v1h0-4v1 1l-1-1c-1-2-1-2-2-3v-1h0l-2-1c-1-1-2-1-4-1l-1-1-1-1h0l-1-1-1-1c0-1-1-1-1-2v-2l-1-2s0-6-1-7-2 0-4-1v-1l-1-1c0 1-1 2-2 3 0-1 0-1-1-1v-1c3-1 3-4 4-6 2-1 3-4 3-6-2-3-3-4-7-6 0 0-1 0-2-1h0-5c-2 1-2 2-2 3l-1 1v-2l1-6c0-1 0 0-1-1s-2 0-3 0l1-1v-2c-1-2-3-2-5-3l-1-1h-3l-3-1c3-1 5 0 7 0l-1-2c1 0 2 0 3 1 1 0 1 1 2 1 1-1 1-2 1-3 2 1 4 1 6 1h3c1-1 7-3 9-4h1z" class="J"></path><path d="M749 198c1 0 1 0 2 2v1 1l-1 2h-1l-1-1c-1-1 0-2 0-3l2 1v-1c-1-1-1-1-1-2z" class="U"></path><path d="M749 198l1-2h3c0 2 0 3-1 5h0l-1 1v-1-1c-1-2-1-2-2-2z" class="a"></path><path d="M744 196l1 1c1 1 1 1 1 2v1c0 1 0 1-1 3h-2c-1-2 1-5 1-7z" class="f"></path><path d="M755 183h0c2-1 4-1 6-1 0 1 0 2 1 3s3 2 4 4l-1 1-3-3c-2-1-4-2-6-2 0 0-1-1-2-1l1-1z" class="m"></path><path d="M738 208v-3-2h2c1 2 2 3 4 3h2l1 1h1l-1 1-1 1 2 2 1-1v-1c1-1 3-1 4-1l-2 1v1c0 1-1 2-1 3h1c0 2-1 3-1 5l-1 1-2-1v1l-1-1c-1-1-2-1-3-2l-1-2c-1 1-2 1-2 2l-3-2c-1 0-1-1-1-1 0-1 0-2 1-3 1 0 0 0 1-1h0 1l-1-1z" class="g"></path><path d="M735 187c1 0 2 1 4 1 1-1 3-1 6-2 2-1 4-1 6-1l8 5c-3 0-6-1-9 0h0-1c-2 0-3 0-4-1h0c1 1 1 1 3 2v1c-1 1-2 0-3 1h-1l-1 2c-1 2-1 3-2 4h-1l-1 1c0 1 0 2 1 3h0-2v2 3c-3-3-5-7-7-10-1 0-1-1-2-2-1-2-2-3-3-5-1 0-1 0-1-1h2l1 1c0-1 0-1 1-1h0l1-1h-2c0-1 1-1 1-2h2l1-1h1l2 2v-1z" class="M"></path><path d="M729 196l2-2c-1-1-1-1-1-2l1-1v1l2-1 2 1v1h-1v1h1 1v1 1l-1 1h1v1c-1 1-2 1-3 1l-1-1h-1c-1 0-1-1-2-2z" class="T"></path><path d="M736 196h2c1 0 1 0 1-1 1-1 0 0 2-1l1 1h1c-1 2-1 3-2 4h-1l-1 1c0 1 0 2 1 3h0-2v2 3c-3-3-5-7-7-10h1l1 1c1 0 2 0 3-1v-1h-1l1-1z" class="V"></path><path d="M766 197c0-1 0-2-1-3h1l2 1v-1l1 2c-1 1-1 3-1 5 0 1 0 1 1 1l1-1 1 1h0v1l-2 2h0v3h0 1v1c-1 1-2 5-3 7l1 1c0 2-2 4-4 6l-3 3-4 2-1-1c-1 0-2 0-3 1v-1l-2 3c-1 0-2 0-3-1 0-1-1-2-1-3l1-1c1 0 1-1 2-2 0-1 1-2 2-3l-3-1h0l1-1c0-2 1-3 1-5h-1c0-1 1-2 1-3v-1l2-1 1-1-2-1v-1l1-2 1 1 1-1h0c1-3 3-3 5-5l3-2h1v3l2 1c-1-1-1-2-1-3h1z" class="U"></path><path d="M757 202c1-1 2-1 3-2l1 1c-2 2-2 3-2 6l-2-1v-4z" class="i"></path><path d="M757 202v4l2 1h0v3 1l-2-2c-1-1-1-1-1-2h-2 0l-2-1v-1c1 0 2-1 3 0h1c1-1 1-1 1-3z" class="j"></path><path d="M766 200c-1 1-1 2-1 3-1 0-1-1-2 0v2l1 2h0c0 1 0 3-1 4h-1l1-4-2-2c0-2 1-2 1-3 1-1 1-2 1-3h1l2 1z" class="p"></path><path d="M751 213h0c1-1 1-2 2-2l1-2v2l-1 1v1l2 2v1c-2 1-2 2-3 4l-3-1h0l1-1c0-2 1-3 1-5z" class="n"></path><path d="M752 220c1-2 1-3 3-4 1 2 1 3 3 5h0c-1 0-2 1-3 2l-1 1-1 3-2 3c-1 0-2 0-3-1 0-1-1-2-1-3l1-1c1 0 1-1 2-2 0-1 1-2 2-3z" class="j"></path><path d="M766 197c0-1 0-2-1-3h1l2 1v-1l1 2c-1 1-1 3-1 5 0 1 0 1 1 1l1-1 1 1h0v1l-2 2h0v3h0 1v1c-1 1-2 5-3 7l1 1c0 2-2 4-4 6l-3 3-4 2-1-1c-1 0-2 0-3 1v-1l1-3 1-1c1-1 2-2 3-2 3-2 5-3 7-5 2-5 3-14 1-19z" class="G"></path><path d="M755 223c1 0 2 1 3 0 2 0 3-2 5-3v1c0 1-1 2-2 3-2 1-2 1-4 1-2-1-1-1-2-1h-1l1-1z" class="L"></path><path d="M708 183c1-2 1-3 1-5h1l1 1h-1c0 2 0 3-1 5 2 1 3 1 6 2s6 3 9 4v-1c3-2 5-3 8-3l-1 1h-2c0 1-1 1-1 2h2l-1 1h0c-1 0-1 0-1 1l-1-1h-2c0 1 0 1 1 1 1 2 2 3 3 5 1 1 1 2 2 2 2 3 4 7 7 10l1 1h-1 0c-1 1 0 1-1 1-1 1-1 2-1 3 0 0 0 1 1 1l3 2c0-1 1-1 2-2l1 2c1 1 2 1 3 2l1 1v-1l2 1h0l3 1c-1 1-2 2-2 3-1 1-1 2-2 2l-1 1c0 1 1 2 1 3h-1-2v1h0-4v1 1l-1-1c-1-2-1-2-2-3v-1h0l-2-1c-1-1-2-1-4-1l-1-1-1-1h0l-1-1-1-1c0-1-1-1-1-2v-2l-1-2s0-6-1-7-2 0-4-1v-1l-1-1c0 1-1 2-2 3 0-1 0-1-1-1v-1c3-1 3-4 4-6 2-1 3-4 3-6-2-3-3-4-7-6 0 0-1 0-2-1h0-5c-2 1-2 2-2 3l-1 1v-2l1-6z" class="E"></path><path d="M720 205c2-4 4-7 6-10 1 2 1 3 2 5h1c-2 1-2 2-3 4v1l-1-1h0v-1l-1-1c-1 2-2 3-3 4l-1-1z" class="U"></path><path d="M729 200c0 1 1 2 1 3l1 1c1 1 1 1 1 2l3 6-5 11h0l-1-1-1-1c0-1-1-1-1-2v-2l-1-2s0-6-1-7-2 0-4-1v-1c1-1 2-2 3-4l1 1v1h0l1 1v-1c1-2 1-3 3-4z" class="X"></path><path d="M726 215c1-1 1-1 1-2l2 2 1-1v1l-1 1 1 2h0c-1 0-2-1-3-1l-1-2z" class="U"></path><path d="M729 200c0 1 1 2 1 3l1 1c1 1 1 1 1 2-1 1-1 2-1 3v1h0l2 2-1 1c-1 0-2 0-3-1v-1c0-1 1-2 0-3 0-1-2-2-3-3v-1c1-2 1-3 3-4z" class="i"></path><path d="M721 206c1-1 2-2 3-4l1 1v1h0l1 1c1 1 3 2 3 3 1 1 0 2 0 3l-2 2c0 1 0 1-1 2 0 0 0-6-1-7s-2 0-4-1v-1z" class="m"></path><path d="M737 214l3 2c0-1 1-1 2-2l1 2c1 1 2 1 3 2l1 1v-1l2 1h0l3 1c-1 1-2 2-2 3-1 1-1 2-2 2l-1 1c0 1 1 2 1 3h-1-2v1h0-4v1 1l-1-1c-1-2-1-2-2-3v-1h0l-2-1c-1-1-2-1-4-1l-1-1h2c2-1 4-1 6-1-1-1-2-1-3-1v-1h2 1v-3h0l-2-2h0v-2z" class="J"></path><path d="M739 223h2v1c2 0 3 0 4 1 1-1 0 0 0-1l1-2c1 1 1 2 2 3l-1 1c0 1 1 2 1 3h-1-2v1h0-4v1 1l-1-1c-1-2-1-2-2-3v-1h0l-2-1c-1-1-2-1-4-1l-1-1h2c2-1 4-1 6-1z" class="a"></path><path d="M738 227c2 0 7 1 9 2h-2v1h0-4v1 1l-1-1c-1-2-1-2-2-3v-1h0z" class="e"></path><path d="M724 168c6 0 11-1 17 0h1c2 0 4 1 7 2 3-1 4-1 7 0 1 1 4 1 6 1l4 1v1c-2 0-3 0-4 1h4l-1 1c-1 2-3 4-3 6l1 2v1c-1 0-1 0-1 1-1-1-1-2-1-3-2 0-4 0-6 1h0l-1 1c1 0 2 1 2 1l2 2c-1 0-1 0-2-1s-3-1-5-1h0c-2 0-4 0-6 1-3 1-5 1-6 2-2 0-3-1-4-1v1l-2-2h-1c-3 0-5 1-8 3v1c-3-1-6-3-9-4s-4-1-6-2c1-2 1-3 1-5h1l-1-1h-1c0 2 0 3-1 5 0-1 0 0-1-1s-2 0-3 0l1-1v-2c-1-2-3-2-5-3l-1-1h-3l-3-1c3-1 5 0 7 0l-1-2c1 0 2 0 3 1 1 0 1 1 2 1 1-1 1-2 1-3 2 1 4 1 6 1h3c1-1 7-3 9-4h1z" class="g"></path><path d="M745 179l1 1 1 1-1 1h-2v-1l1-2z" class="a"></path><path d="M723 174h1c1 1 2 1 3 2l-1 1-1 1-2-4z" class="V"></path><path d="M705 171c2 1 4 1 6 1h3l-1 1h-1c-1 0-2 0-3 1h-4-1c1-1 1-2 1-3z" class="a"></path><path d="M757 176c0 2 1 4 2 5h2l1 1h-1c-2 0-4 0-6 1h0c-1 0-2-1-4-1h0v-1c1 0 2 0 4-1 0 0 0-1 1-2 0-1 0-1 1-2z" class="X"></path><path d="M749 170c3-1 4-1 7 0 1 1 4 1 6 1l4 1v1c-2 0-3 0-4 1h0c-2 0-4 0-6-1s-5-2-7-3z" class="O"></path><path d="M714 175h1v1 2l1 1c1 0 1 0 1 1h1l3 3c1 1 1 2 3 3h0v2h-1c-2-1-2-1-2-3-2-1-3-4-4-4h-1-1l-1 1c1 0 1 1 2 2h-2c-1-1-2-2-2-3l1-1v-1-2-1l1-1z" class="U"></path><path d="M735 187c1-1 3-2 4-2l3-1c4-1 8-2 12 0 1 0 2 1 2 1l2 2c-1 0-1 0-2-1s-3-1-5-1h0c-2 0-4 0-6 1-3 1-5 1-6 2-2 0-3-1-4-1zm27-13h4l-1 1c-1 2-3 4-3 6l1 2v1c-1 0-1 0-1 1-1-1-1-2-1-3h1l-1-1h-2c-1-1-2-3-2-5l5-2h0z" class="i"></path><path d="M766 174c4 4 8 7 12 11 0 0 2-1 2 0 1 0 2 1 3 2 2 2 4 3 6 5 1 1 1 0 1 1 1 3 4 4 6 6 2 1 3 2 5 4 1 1 1 5 1 7v2 4c0 2 0 4 1 6 0 0 0 1 1 2h-1c-1-1-1 0-1-1-2-1-3-2-5-3l-2 2c-1 0-2 0-4-1l-1-2h1c-1-1-2-1-4-1h-1-1l-1 1c-1 0-1-1-2-1l1-2c-3-1-7-1-10-2-1 0-1-1-1-1v-2l-1-1-1-1v-1h-1 0v-3h0l2-2v-1h0l-1-1-1 1c-1 0-1 0-1-1 0-2 0-4 1-5l-1-2v1l-2-1 1-1c-1 0-1 0-2-1l1-1-1-1 1-1c-1-2-3-3-4-4 0-1 0-1 1-1v-1l-1-2c0-2 2-4 3-6l1-1z" class="g"></path><path d="M775 194l1-1v2h1 1l1 3h-1 0-3v-1-3z" class="U"></path><path d="M797 209c2 0 3 2 5 3v4c-2-2-4-4-5-7z" class="i"></path><path d="M776 209c-1-2-1-3-1-5 1-1 1-1 2-1s1 3 1 4-1 1-1 1v1h-1z" class="a"></path><path d="M783 216h1 2c2 0 7 1 9 2h0c-1 1-3 1-4 1-1-1-2-1-4-1h-1-1l-1 1c-1 0-1-1-2-1l1-2z" class="q"></path><path d="M766 191h1 1c1-1 1-2 3-2 1 0 2 1 3 2l1 3v3l-3 1c1 0 1 0 2 1 1 3-1 7 1 10l-1 1h1l1-1h1c2 0 6 1 7 3 1 0 1 1 2 1l1 1c-3 0-6-1-9 0-1 0-1 0-2-1h-3v1c-1 0-1-1-1-1v-2l-1-1-1-1v-1h-1 0v-3h0l2-2v-1h0l-1-1-1 1c-1 0-1 0-1-1 0-2 0-4 1-5l-1-2v1l-2-1 1-1c-1 0-1 0-2-1l1-1z" class="T"></path><path d="M766 174c4 4 8 7 12 11 0 0 2-1 2 0 1 0 2 1 3 2 2 2 4 3 6 5 1 1 1 0 1 1 1 3 4 4 6 6 2 1 3 2 5 4 1 1 1 5 1 7v2c-2-1-3-3-5-3l-1-1-3-3c0 1 0 2 1 3 1 2 0 4 1 7v1h-1l-1-1c-1 0-1-1-2-1h-1l-1-1c-1-1-1-2-2-4v-1c-1 0-1-1-1-1 0-1-1-2-1-3s1-2 1-3v-1h-1-1c-1 0-1-1-2-2 0-2-1-3-1-4l-1-1-3-3c-1-2-4-7-6-7v1l-1 1v1c0 1-1 2-2 2l-1 2-1-1c-1-2-3-3-4-4 0-1 0-1 1-1v-1l-1-2c0-2 2-4 3-6l1-1z" class="c"></path><path d="M789 205l1 4h-3v-1c0-1 1-2 2-3z" class="p"></path><path d="M787 209h3l1 5h-1l-1-1c-1-1-1-2-2-4z" class="X"></path><path d="M781 194s1 0 1 1 1 2 1 3l1-1 1 1h1 0c-1-2-2-3-3-5 0-1-1-1-1-2l-1-1h1c4 5 5 9 7 15-1 1-2 2-2 3-1 0-1-1-1-1 0-1-1-2-1-3s1-2 1-3v-1h-1-1c-1 0-1-1-2-2 0-2-1-3-1-4z" class="j"></path><path d="M793 215h0c-3-8-4-15-8-23 2 1 4 3 6 4 0 3 0 4 2 6h-1c0 1 0 2 1 2v1c0 1 0 2 1 3 1 2 0 4 1 7v1h-1l-1-1z" class="V"></path><path d="M791 196c1 1 3 3 5 3 2 1 3 2 5 4 1 1 1 5 1 7v2c-2-1-3-3-5-3l-1-1-3-3v-1c-1 0-1-1-1-2h1c-2-2-2-3-2-6z" class="U"></path><path d="M801 203c1 1 1 5 1 7v2c-2-1-3-3-5-3l-1-1c1-1 1-1 1-2l-1-1h1c0-1 0-1 1-2l2 1s0-1 1-1z" class="m"></path><path d="M766 174c4 4 8 7 12 11 1 1 3 3 4 5h-1l1 1c0 1 1 1 1 2 1 2 2 3 3 5h0-1l-1-1-1 1c0-1-1-2-1-3s-1-1-1-1l-1-1-3-3c-1-2-4-7-6-7v1l-1 1v1c0 1-1 2-2 2l-1 2-1-1c-1-2-3-3-4-4 0-1 0-1 1-1v-1l-1-2c0-2 2-4 3-6l1-1z" class="X"></path><path d="M433 149h9 0 0l2 1c1-1 2-1 3-1l2 2c2 1 3 1 5 1 0 1 0 1-1 1l1 2 3 1c2 0 3-1 3-1 1 0 2 0 3 1l2-1c0 1 1 1 2 2 2-2 4-3 6-3 1 0 3 1 4 1h2l1 1 1 2 2 1v-1l1 1v1c1-1 3-5 3-7l2-2c0 2 0 3-1 4v2c0 1-2 3-3 4v2 1l1 1h1 0 1 1c1-2 4-5 6-6 1-1 3-2 4-3 2 0 2 0 3 1s2 1 1 2v2c1 1 0 2 0 3l-2 3c0 1 1 2 1 3l-3 3c-3 6-7 12-9 19-3 4-6 9-7 14-2 6-2 12-4 18h-1v-5c-1 2-1 4-3 5l-3 6v1h-1 0-2c1 1 1 2 2 3l-1 1c-1 0-1 0-2-1-2-2-6-5-9-7-1-1-2-2-3-4h1v-1h-1-1v-2c0-1-2-3-3-3h-1-2-1c-4-1-6-5-9-8l-2-2-1 1c-1 2-1 4-3 6s-4 2-7 2h-1v-1c0-3-2-6-4-9l-3-6c0-1-1-2-1-3 1-1 1-1 1-2v-2h-1-1l1-1v-3h2l-1-1h0-4v-1c1 0 3-1 4-1l1-1 2-2v-1l1-1c-1-2-2-2-3-3v-3h1c-1-2 0-2-1-3l2-2v-3h1l1-1 1-1 1-1c0-1 0-2 1-3v-1l4-4 1-3c1 0 1-1 1-1v-1l1-2z" class="S"></path><path d="M457 190l1 1c0 1 0 1 1 2 1 0 1 1 2 2h0l-1 2h1l-1 1c-1-1-2-1-3-1h0l-1-2h0-1v1l-3-2v-1c2-1 4-2 5-3z" class="N"></path><path d="M452 194l3 2v-1h1 0l1 2h0c0 1 1 1 0 2l2 1h1c-1 3-1 5-3 8-1-2-1-5-3-6-1-1-2 0-3 0l-1-4c1-2 1-3 2-4z" class="Z"></path><path d="M459 200c-1 1-1 1-1 2l-1 1-1-1c-1-2-1-3-1-5 1-1 1 0 2 0h0c0 1 1 1 0 2l2 1z" class="B"></path><path d="M448 200c2 0 2-1 2-2l1 4c1 0 2-1 3 0 2 1 2 4 3 6h-1v4c-1 3 0 6 0 8l1 1-2 1v-2c0-1-2-3-3-3-1-2-1-4-1-6h-2v-1l-1-1v-9z" class="P"></path><path d="M448 200c2 0 2-1 2-2l1 4v9h-2v-1l-1-1v-9z" class="G"></path><path d="M451 165c1 1 1 1 2 1h0l2 1 3-2v2h1c1 1 1 1 2 1 0 0 0-1 1-1s2 0 3 1h0v2l1 1h1v1h-1l-1 2 2 2 1-1h2l1 1c-1 1-2 2-3 4 0 2 0 1-1 3l-1 2c0 1-1 2-1 3 0 0-1 1-1 2v1h-3c-1-1 0-2-2-3l-1 3-1-1-1-1v-1c-1 0-2 1-2 1l-4 2-1-2 3-1v-1-1c0-1-1-2-2-4l-2-3c-2 1-3 0-5 0h0 5c3-3 4-6 6-9 1 0 2-1 2-2l-1 1c-1-1-1-1-2-1h0c-2-1-1-1-2-3z" class="B"></path><path d="M459 188c-1-1 0-1-2-1 0-1 0-1 1-2 1 0 2 0 4-1v2c-1 0-1 1-2 2l1 1h2c-1 1-2 1-2 2-1-1 0-2-2-3z" class="W"></path><path d="M458 170c0-1 1-1 1-1h1 2l2 2v3h-3-1c-1-2-2-2-2-4z" class="M"></path><path d="M451 165c1 1 1 1 2 1h0l2 1 3-2v2h1v2s-1 0-1 1c0 2 1 2 2 4h1 3l-1 2c0 2-1 3-3 5 0 1-1 2-2 3l-1 1-5 3v-1-1c0-1-1-2-2-4l-2-3c-2 1-3 0-5 0h0 5c3-3 4-6 6-9 1 0 2-1 2-2l-1 1c-1-1-1-1-2-1h0c-2-1-1-1-2-3z" class="Z"></path><path d="M448 179c1 0 2 1 3 2 0 1 2 2 3 2 1 1 2 1 3 2l-5 3v-1-1c0-1-1-2-2-4l-2-3c-2 1-3 0-5 0h0 5z" class="D"></path><path d="M458 170c0 2 1 2 2 4h1 3l-1 2c0 2-1 3-3 5 0 1-1 2-2 3l-2-1v-1-2h-2-1l1-2h-2-1l3-3c1-2 2-3 3-5h1z" class="e"></path><path d="M454 178h1l-1-1v-1c1-1 2-2 3-2 1-1 1 0 2 0l-1 4v2c-1 0-1 0-1-1l-1 1h-2-1l1-2z" class="P"></path><path d="M458 178c1-1 1-2 3-3v1h2c0 2-1 3-3 5 0 1-1 2-2 3l-2-1v-1-2l1-1c0 1 0 1 1 1v-2z" class="Q"></path><path d="M467 157c2-2 4-3 6-3 1 0 3 1 4 1h2l1 1 1 2 2 1v-1l1 1v1c0 1-1 2-2 3v1c-1 1-2 2-4 3l-7 7h-1v-1l-2 2-1 1-2-2 1-2h1v-1h-1l-1-1v-2h0c-1-1-2-1-3-1s-1 1-1 1c-1 0-1 0-2-1h-1v-2l-3 2-2-1h0c-1 0-1 0-2-1 0-1 0 0-1-1v1l-1-1c0-1 1-2 3-2 0 0 0 1 1 1h1l-1-1c-1-1 0-2 0-3h0 1l2-2-2-2h0l3 1c2 0 3-1 3-1 1 0 2 0 3 1l2-1c0 1 1 1 2 2z" class="N"></path><path d="M481 158v2l-1 1-1-1s0-1-1-1v-2l2-1 1 2z" class="I"></path><path d="M466 161l1-2c1-1 2-3 5-4v1c1 1 1 1 2 1 1 1 2 1 3 2v2c0 1-1 2-2 2-1 1-2 1-3 0l-1 1 1 1c-1 0-1 0-2 1-1-1-1-2-2-2 0-1-1-1-2-2v-1z" class="c"></path><path d="M466 161l1-1c1 0 2 0 3-1h1v2c-1 1-2 1-3 3 0-1-1-1-2-2v-1z" class="E"></path><path d="M467 157c2-2 4-3 6-3l-1 1c-3 1-4 3-5 4l-1 2v1c1 1 2 1 2 2 1 0 1 1 2 2l1 1-3 3-3-2h0c-1-1-2-1-3-1s-1 1-1 1c-1 0-1 0-2-1h-1v-2l-3 2-2-1h0c-1 0-1 0-2-1 0-1 0 0-1-1v1l-1-1c0-1 1-2 3-2 0 0 0 1 1 1h1l-1-1c-1-1 0-2 0-3h0 1l2-2-2-2h0l3 1c2 0 3-1 3-1 1 0 2 0 3 1l2-1c0 1 1 1 2 2z" class="e"></path><path d="M467 157c2-2 4-3 6-3l-1 1c-3 1-4 3-5 4l-1 2v1c1 1 2 1 2 2 1 0 1 1 2 2l1 1-3 3-3-2h0c0-2 0-2-1-3 0-2 0-2 1-4v-1l-2 2c-1 1-2 1-2 2-1 1-1 1-2 1 0-1 1-1 2-2v-1c2-2 3-4 6-5z" class="J"></path><path d="M454 155l3 1c2 0 3-1 3-1 1 0 2 0 3 1l2-1c0 1 1 1 2 2-3 1-4 3-6 5v1l-1-2v2c-2-1-2-1-3 0h-3l-1-1c-1-1 0-2 0-3h0 1l2-2-2-2h0z" class="s"></path><path d="M454 155l3 1h1l-1 1v3h-3v-1l2-2-2-2h0z" class="V"></path><path d="M460 155c1 0 2 0 3 1l2-1c0 1 1 1 2 2-3 1-4 3-6 5v1l-1-2c1-1 2-2 3-4h0l-3 2h0v-2h0-1l-1-1h-1c2 0 3-1 3-1z" class="c"></path><path d="M433 149h9 0 0l2 1c1-1 2-1 3-1l2 2c2 1 3 1 5 1 0 1 0 1-1 1l1 2h0l2 2-2 2h-1 0c0 1-1 2 0 3l1 1h-1c-1 0-1-1-1-1-2 0-3 1-3 2l1 1v-1c1 1 1 0 1 1 1 2 0 2 2 3h0c1 0 1 0 2 1l1-1c0 1-1 2-2 2-2 3-3 6-6 9h-5-3l1-2c-1-1-2-1-2-2l-2 1-1 1-1-2h-1v-1l-1 1-1-1h0l1-1-1-1 1-1v-2h-1c-1 0-2-1-2-2h1l1 1 1-1-1-1v-2h0v-2c0-1 1-1 0-3 0-1 0-2-1-3h-1l1-3c1 0 1-1 1-1v-1l1-2z" class="M"></path><path d="M435 175c0-2-1-3 0-5v-2l1-2 1 1h1l1-1c-1 2-2 4-1 6l4 4h3c-2 1-2 1-4 1h0c-1-1-2-1-2-2l-2 1-1 1-1-2z" class="E"></path><path d="M445 161c1 1 1 2 2 2s1 0 2 1l1 1v-1c1 1 1 0 1 1 1 2 0 2 2 3h0c1 0 1 0 2 1l1-1c0 1-1 2-2 2-2 3-3 6-6 9h-5-3l1-2h0c2 0 2 0 4-1s3-3 5-5c0-3-1-4-3-7-1 0-1 0-2-1h-2v-1l2-1z" class="C"></path><path d="M433 149h9 0 0l2 1c1-1 2-1 3-1l2 2c2 1 3 1 5 1 0 1 0 1-1 1l1 2h0l2 2-2 2h-1 0c0 1-1 2 0 3l1 1h-1c-1 0-1-1-1-1-2 0-3 1-3 2-1-1-1-1-2-1s-1-1-2-2v-3c-1 0-1 1-2 1 0 1-2 1-3 1l-1 1h-1v-2c-1 1-1 1-1 2l-1-1h-1c1 1 1 3 2 4h-1l-3 3-1-1v-2h0v-2c0-1 1-1 0-3 0-1 0-2-1-3h-1l1-3c1 0 1-1 1-1v-1l1-2z" class="g"></path><path d="M444 150c1-1 2-1 3-1l2 2h-2v2c-1 0-1 1-3 0 0 0-1-1-1-2l1-1z" class="p"></path><path d="M442 149l2 1-1 1c0 1 1 2 1 2-1 0-3 1-4 1l-1-1v-2h-1v-1l4-1z" class="U"></path><path d="M445 158h1c0-1 0-1 1-2 0 0 3-1 4-1 0 1 0 1 1 2 1-1 1-1 1-2h1l2 2-2 2h-1 0c0 1-1 2 0 3l1 1h-1c-1 0-1-1-1-1-2 0-3 1-3 2-1-1-1-1-2-1s-1-1-2-2v-3z" class="T"></path><path d="M445 161h1l1-2h1 5c0 1-1 2 0 3l1 1h-1c-1 0-1-1-1-1-2 0-3 1-3 2-1-1-1-1-2-1s-1-1-2-2z" class="M"></path><path d="M433 149h9 0 0l-4 1v1h1v2h-2c0 1 1 2 1 3h1l1-1v2l-1 1c0 2 0 2 1 2l-1 1h-1v-2c-1 1-1 1-1 2l-1-1h-1c1 1 1 3 2 4h-1l-3 3-1-1v-2h0v-2c0-1 1-1 0-3 0-1 0-2-1-3h-1l1-3c1 0 1-1 1-1v-1l1-2z" class="V"></path><path d="M434 159c0-3 0-3 2-5 0 1 1 3 1 4l-1 1h-2z" class="U"></path><path d="M432 152c2 0 3 1 4 2-2 2-2 2-2 5l-1-1-1 1c0-1 0-2-1-3h-1l1-3c1 0 1-1 1-1z" class="n"></path><path d="M499 156c2 0 2 0 3 1s2 1 1 2v2c1 1 0 2 0 3l-2 3c0 1 1 2 1 3l-3 3c-3 6-7 12-9 19-3 4-6 9-7 14-2 6-2 12-4 18h-1v-5c-1 2-1 4-3 5l-3 6v1h-1 0-2c1 1 1 2 2 3l-1 1c-1 0-1 0-2-1-2-2-6-5-9-7-1-1-2-2-3-4h1v-1h-1-1l2-1c1 1 1 2 2 3h1c1-4 1-9 2-12v-1c1-8 5-18 10-24 5-8 13-15 20-21-1 0-1 0-1-1 1-1 2-1 3-2h-1c-1 1-2 2-4 2h0c1-2 4-5 6-6 1-1 3-2 4-3z" class="M"></path><path d="M484 189c-1-2-1-3-2-4h0l2-6c1 1 1 3 3 4l-3 6z" class="h"></path><path d="M468 212c0 3 0 5-1 7h1 2c1 0 1 0 1-1h1v1c-1 1-1 2-1 4l-2 2c-2 0-1 0-2-1 0-4 0-8 1-12z" class="e"></path><path d="M490 171c0 1-3 5-3 6h4l-4 6c-2-1-2-3-3-4 1-1 1-2 2-3v-1c0-2 3-3 4-4z" class="V"></path><path d="M472 219h1v1c1 1 2 2 2 4h0l-3 6v1h-1 0l-2-2-1 1-1-6c1 1 0 1 2 1l2-2c0-2 0-3 1-4z" class="I"></path><path d="M473 220c1 1 2 2 2 4h0-2v-4z" class="S"></path><path d="M467 224c1 1 0 1 2 1l2-2v7h1v1h-1 0l-2-2-1 1-1-6z" class="P"></path><path d="M465 218c1-8 2-19 7-25-2 6-5 13-4 19-1 4-1 8-1 12l1 6c-1 1-1 0-2 1l-1-2c1-4 0-7 0-11z" class="N"></path><path d="M472 187h1l1-2 1 1c0 1-1 5-3 6v1c-5 6-6 17-7 25v4h-1v-2c-1-2 0-4 0-5 1-3 1-6 1-9v-1l-1 3c0 1 0 1-1 2l-1 1c1-8 5-18 10-24z" class="Q"></path><path d="M462 211l1-1c1-1 1-1 1-2l1-3v1c0 3 0 6-1 9 0 1-1 3 0 5v2h1v-4c0 4 1 7 0 11l1 2c1-1 1 0 2-1l1-1 2 2h-2c1 1 1 2 2 3l-1 1c-1 0-1 0-2-1-2-2-6-5-9-7-1-1-2-2-3-4h1v-1h-1-1l2-1c1 1 1 2 2 3h1c1-4 1-9 2-12v-1z" class="e"></path><path d="M499 156c2 0 2 0 3 1s2 1 1 2v2c1 1 0 2 0 3l-2 3c-4 3-7 7-10 10h-4c0-1 3-5 3-6l9-7v-1c-2 0-4 3-6 4l-1-1c-1 0-1 0-1-1 1-1 2-1 3-2h-1c-1 1-2 2-4 2h0c1-2 4-5 6-6 1-1 3-2 4-3z" class="f"></path><path d="M499 156c2 0 2 0 3 1s2 1 1 2v2c1 1 0 2 0 3v-2h-2l1-1v-1l-9 6h-1c-1 0-1 0-1-1 1-1 2-1 3-2h-1c-1 1-2 2-4 2h0c1-2 4-5 6-6 1-1 3-2 4-3z" class="K"></path><path d="M501 167c0 1 1 2 1 3l-3 3c-3 6-7 12-9 19-3 4-6 9-7 14-2 6-2 12-4 18h-1v-5c-1 2-1 4-3 5h0c0-2-1-3-2-4v-1l2-5 9-25 3-6 4-6c3-3 6-7 10-10z" class="E"></path><path d="M475 214l3-2 1 1v2c-1 1-1 1-1 2v2h0c-1 2-1 4-3 5h0c0-2-1-3-2-4v-1l2-5z" class="W"></path><path d="M430 156h1c1 1 1 2 1 3 1 2 0 2 0 3v2h0v2l1 1-1 1-1-1h-1c0 1 1 2 2 2h1v2l-1 1 1 1-1 1h0l1 1 1-1v1h1l1 2 1-1 2-1c0 1 1 1 2 2l-1 2h3 0c2 0 3 1 5 0l2 3c1 2 2 3 2 4v1 1l-3 1 1 2 4-2s1-1 2-1v1l1 1c-1 1-3 2-5 3v1c-1 1-1 2-2 4 0 1 0 2-2 2v9l1 1v1h2c0 2 0 4 1 6h-1-2-1c-4-1-6-5-9-8l-2-2-1 1c-1 2-1 4-3 6s-4 2-7 2h-1v-1c0-3-2-6-4-9l-3-6c0-1-1-2-1-3 1-1 1-1 1-2v-2h-1-1l1-1v-3h2l-1-1h0-4v-1c1 0 3-1 4-1l1-1 2-2v-1l1-1c-1-2-2-2-3-3v-3h1c-1-2 0-2-1-3l2-2v-3h1l1-1 1-1 1-1c0-1 0-2 1-3v-1l4-4z" class="M"></path><path d="M437 176l2-1c0 1 1 1 2 2l-1 2c-1 0-2 1-3 2 1-1 0-3 0-5z" class="B"></path><path d="M424 177l1-1c1 4 4 7 7 10h-1v1c-1-1-2-2-3-2h-1c-1-1-2-3-3-4v-1-3z" class="C"></path><path d="M427 185h1c1 0 2 1 3 2v-1h1l8 3h3l3 1h-6v1l-3 1v1h0c-3-1-4-2-6-4-1-1-2-2-3-2l-1-2z" class="G"></path><path d="M431 189c2 0 3 0 5 1h1c1 1 1 1 0 2v1h0c-3-1-4-2-6-4z" class="E"></path><path d="M428 187c1 0 2 1 3 2 2 2 3 3 6 4h0v1l-1 1c-1 0-1 0-2-1v1 1 1c0 1-1 2-1 3v-2-1l-2-1c0-1-1-2-1-3l-4-5c1 0 1 0 2 1 0 0 1 1 2 1-1-1-1-2-2-3z" class="O"></path><path d="M436 177l-1 1v1h-2v2c1 1 1 1 2 1v1h-1l1 1-1 1-3-3c-1-1-2-3-4-4v-2h0l-1-2c0-1 0-1-1-2h0v-1h1v-2h1l1 1h2l-1 2h3l1 1-1 1h0l1 1 1-1v1h1l1 2z" class="T"></path><path d="M419 175h1c-1-2 0-2-1-3l2-2v-3h1l1-1 1-1v2c0 3 0 6 1 9l-1 1v3 1c1 1 2 3 3 4l1 2c1 1 1 2 2 3-1 0-2-1-2-1-1-1-1-1-2-1s0 0-1-1-1-2-3-3h0l-1-1v-1l1-1c-1-2-2-2-3-3v-3z" class="O"></path><path d="M422 181v-1-6h1l1 3v3 1c1 1 2 3 3 4l1 2c1 1 1 2 2 3-1 0-2-1-2-1-1-1-1-1-2-1s0 0-1-1-1-2-3-3h0l-1-1v-1l1-1z" class="D"></path><path d="M430 156h1c1 1 1 2 1 3 1 2 0 2 0 3v2h0v2l1 1-1 1-1-1h-1c0 1 1 2 2 2h1v2l-1 1h-3l1-2h-2c-1-1-1-2-2-3h-2v-2l1-1c0-1 0-2 1-3v-1l4-4z" class="q"></path><path d="M430 156h1c1 1 1 2 1 3 1 2 0 2 0 3v2l-2-3c-1 1-1 1-2 1h0l-1-1c0 1 0 0-1 1v-1-1l4-4z" class="f"></path><path d="M431 156c1 1 1 2 1 3-1 1-2 1-3 1 1-1 1-2 2-4z" class="m"></path><path d="M440 179h3 0c2 0 3 1 5 0l2 3c1 2 2 3 2 4v1 1l-3 1h-1l-2 1-3-1h-3c0-1 0-3-1-4 0 1-1 1-2 2v-1c-1-2 0-3 0-5 1-1 2-2 3-2z" class="T"></path><path d="M444 182l2-1c0 1 1 1 1 2l1-1h2c1 2 2 3 2 4v1 1l-3 1h-1l-2 1-3-1-2-2 1-1h2 0v-4z" class="h"></path><path d="M444 182l2-1c0 1 1 1 1 2s1 3 1 4c-1 0-2-1-2-1 0-1 0-1-1-1-1-1 0-2-1-3z" class="g"></path><path d="M450 182c1 2 2 3 2 4v1 1l-3 1h-1v-2c0-1-1-3-1-4l1-1h2z" class="Q"></path><path d="M450 191l4-2s1-1 2-1v1l1 1c-1 1-3 2-5 3v1c-1 1-1 2-2 4 0 1 0 2-2 2v9h-2c-2 0-2 0-3-1v-3l-1-1c-2 0-3 2-4 2-2-2-2-3-3-5-1 0-1-1-1-1h-1c0-1 1-2 1-3v-1-1-1c1 1 1 1 2 1l1-1v-1-1l3-1v-1h6l2-1h1l1 2z" class="E"></path><path d="M442 204v-1h-2l-1-1 1-1 2 2c1-1 1-2 1-3 1 0 2-1 2-1 1 1 1 3 1 5v1 4c-2 0-2 0-3-1v-3l-1-1z" class="t"></path><path d="M448 189h1l1 2h-1c-2 0-4 0-6 2 0 0-1 0-1 1l1 1c1 1 1 2 1 3v1h-1l-1-1h-3l1 2-2 2-1-1h0l2-3-2-4h0v-1-1l3-1v-1h6l2-1z" class="N"></path><path d="M440 191l2 1c-1 1-1 2-3 2h-2 0v-1-1l3-1zm10 0l4-2s1-1 2-1v1l1 1c-1 1-3 2-5 3v1c-1 1-1 2-2 4 0 1 0 2-2 2 0-1-1-1-1-2-1 0-2 0-3 1v-1c0-1 0-2-1-3l-1-1c0-1 1-1 1-1 2-2 4-2 6-2h1z" class="I"></path><path d="M447 198c1-1 2-2 2-4h1v1c1-1 1-2 2-2v1c-1 1-1 2-2 4 0 1 0 2-2 2 0-1-1-1-1-2z" class="B"></path><path d="M421 183l1 1h0c2 1 2 2 3 3s0 1 1 1l4 5c0 1 1 2 1 3l2 1v1 2h1s0 1 1 1c1 2 1 3 3 5 1 0 2-2 4-2l1 1v3c1 1 1 1 3 1h2l1 1v1h2c0 2 0 4 1 6h-1-2-1c-4-1-6-5-9-8l-2-2-1 1c-1 2-1 4-3 6s-4 2-7 2h-1v-1c0-3-2-6-4-9l-3-6c0-1-1-2-1-3 1-1 1-1 1-2v-2h-1-1l1-1v-3h2l-1-1h0-4v-1c1 0 3-1 4-1l1-1 2-2z" class="X"></path><path d="M427 206c1 0 1 0 1 1 1 0 1 1 2 1h3c1 2 0 4-1 5l-1 1h-2c0-1 0-2-1-3 0-1-1-2-1-3v-2z" class="u"></path><path d="M418 193h-1-1l1-1v-3h2v3h2c5 2 8 8 12 12l3 3c-3 0-4 0-6-2l-1-3c-3-2-5-5-8-7l-1 1 1 1c-1 1-1 2-2 2l-1 1c0-1-1-2-1-3 1-1 1-1 1-2v-2z" class="j"></path><path d="M421 183l1 1h0c2 1 2 2 3 3s0 1 1 1l4 5c0 1 1 2 1 3h0c-2-1-3-1-5-3v1c2 3 6 6 7 10-4-4-7-10-12-12h-2v-3l-1-1h0-4v-1c1 0 3-1 4-1l1-1 2-2z" class="V"></path><path d="M433 204c-1-4-5-7-7-10v-1c2 2 3 2 5 3h0l2 1v1 2h1s0 1 1 1c1 2 1 3 3 5 1 0 2-2 4-2l1 1v3c1 1 1 1 3 1h2l1 1v1h2c0 2 0 4 1 6h-1-2-1c-4-1-6-5-9-8l-2-2-1 1v-1l-3-3z" class="Z"></path><path d="M449 217l-3-2c0-2 1-3 1-4l1-1v1h1 2c0 2 0 4 1 6h-1-2z" class="C"></path><defs><linearGradient id="i" x1="593.393" y1="221.316" x2="647.301" y2="194.496" xlink:href="#B"><stop offset="0" stop-color="#2b2c2e"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#i)" d="M588 160c1-1 2-2 3-2s3 1 4 2c4 3 10 2 15 3h3s1 1 2 1c1-1 2-1 4 0 0 2 1 3 3 4h0s2 2 3 2v-1l1-1 3 3 1 2v1l1 1 1-1-1-2c-1-1-1-1-1-2l2-2v-1h-2c1-1 1-2 2-2 2-1 3-1 4-3l2 1 2-1 3 4 6 9c1 2 3 3 4 5-1 1-1 2-1 4 1 0 1 1 2 1l2 2c1 1 2 1 3 1 0 3 0 4-1 6h0c1 0 2 1 3 1h0l1 1c1 1 1 1 2 1h1l1 1-1 2 2 1 1-1 1 1-1 1 1 2h5c0 1 0 1 1 2 0 1 0 2 1 2v2l2 3c0 1 1 2 2 3s1 1 2 1c1 1 2 1 3 1v1h-1-1l-76-1h-6c-4 1-11 0-15-1l-4-1c-1-1-3-1-4-1-2-1-3-2-5-3l-1-1h0c1-2 0-3 0-4l-1-1v-1c-1-1-1-2-2-3l1-1c-1-1-1-3-2-5h0l-1-1v-3h0c1-1 1-2 2-2v1l1-4h0-1l-2-2c1 0 2-1 3-2l-1-2c0-1 0-1-1-1 0-1-1-2-2-2h0c-1-1-1-2-1-2-1-1-1-1-2-1l1-2h-1v-1c1-1 1-1 2-1h3c1-1 1-2 1-3l1-1h1c1 0 2-1 3-1v-1l2 1c2-1 3-1 5-1 3-1 5-3 7-5z"></path><path d="M615 193l1 1c0 1 1 2 1 3-2 3-4 3-6 4l-5-3v-2h0c2 2 3 2 6 2 2-2 2-2 3-5z" class="I"></path><path d="M616 177v-1h0c1 2 1 4 1 6v1 1c0 4 1 10 0 13 0-1-1-2-1-3l-1-1-3-3h-1c-1 0-2-1-2-1l2-2 1 1h1v-1c1 0 2 0 2-1v-2h1v-3l-1-1-1-1h1l1-2z" class="B"></path><path d="M609 189s1 1 2 1h1l3 3c-1 3-1 3-3 5-3 0-4 0-6-2-1-3 0-4 1-6 1 0 0 0 1-1h1z" class="P"></path><path d="M572 211c1-2 0-3 0-4l-1-1v-1c-1-1-1-2-2-3l1-1c1 2 2 4 4 5l3 4h1c4 3 6 6 11 7h1 37 1c-9 0-18 0-27 1-4 1-11 0-15-1l-4-1c-1-1-3-1-4-1-2-1-3-2-5-3l-1-1h0z" class="c"></path><path d="M567 195v-3h0c1-1 1-2 2-2v1l3 6v-2h3l1-2 1 1v1 1c2 1 2 2 4 1-1 2-1 3-1 4 1 2 2 3 4 4l1 1 1 1h-3l4 3 2 2c0 2 1 3 1 5h-1c-5-1-7-4-11-7h-1l-3-4c-2-1-3-3-4-5-1-1-1-3-2-5h0l-1-1z" class="L"></path><path d="M568 196c2 1 3 2 4 3v1l-2 1c-1-1-1-3-2-5z" class="W"></path><path d="M570 201l2-1c1 2 1 4 3 6h0c1 1 3 2 3 4h-1l-3-4c-2-1-3-3-4-5z" class="B"></path><path d="M576 193l1 1v1 1c2 1 2 2 4 1-1 2-1 3-1 4 1 2 2 3 4 4l1 1 1 1h-3c-4-3-7-7-11-10v-2h3l1-2z" class="c"></path><path d="M610 163h3s1 1 2 1c1-1 2-1 4 0 0 2 1 3 3 4h0s2 2 3 2v-1l1-1 3 3 1 2v1l1 1 1-1-1-2 2-2c0 1 1 2 1 3l-1 1v1h0v2l2 1-1 2v2h-2-1l-1-1v1l1 3 2 2v1h0l-3-3h-1l1 1-1 1c0 1 0 0-1 1-1-1-2-1-3-1h0c-2-2-2-2-2-4h0c0-1-1-2-1-2-3 0-3 0-5 2v-1c0-2 0-4-1-6h0v1h-3-1v-1l1-1 2 1v-1c-1-1-1-1-3-2-1 0-2-1-2-2h-1v-1l-1 1c-2-1-2-2-3-3h1v-1l1-2 1 1 2-2v-1z" class="D"></path><path d="M623 183c0-2 0-2 1-3 0 1 1 1 1 2l-1 1h-1 0z" class="K"></path><path d="M610 164c4 1 7 4 9 7-2 0-2-1-3-1l-1 1h0c-1-1-2-1-2-2h2l-3-3v1 1h0c-2 0-3-1-4-2l2-2z" class="b"></path><path d="M610 163h3s1 1 2 1c1-1 2-1 4 0 0 2 1 3 3 4h0s2 2 3 2v-1l1-1 3 3 1 2v1l1 1 1-1-1-2 2-2c0 1 1 2 1 3l-1 1v1h0v2l2 1-1 2v2h-2-1l-1-1v1l1 3c-2-3-5-5-7-8-2-2-3-4-5-6-2-3-5-6-9-7v-1z" class="O"></path><path d="M622 168s2 2 3 2v-1l1-1 3 3c-1 1-2 1-3 2 0 1 0 2 1 3-1-1-2-1-2-1-2-1-3-3-4-4h1c1 1 1 2 3 3 0-2-2-3-3-4v-2z" class="M"></path><path d="M631 172l2-2c0 1 1 2 1 3l-1 1v1h0v2l2 1-1 2v2h-2-1l1-2c-1-1-2-1-4-2v-2h-1c-1-1-1-2-1-3 1-1 2-1 3-2l1 2v1l1 1 1-1-1-2z" class="J"></path><path d="M633 187c1-1 2-1 3-1l8 8c3 3 5 5 7 8h1v-1l1-2h0l1-1c0 1 1 1 1 1v-1h2c-1-1-1-1 0-1l1-1v-2c1 0 2 1 3 1h0l1 1c1 1 1 1 2 1h1l1 1-1 2 2 1 1-1 1 1-1 1 1 2h5c0 1 0 1 1 2 0 1 0 2 1 2v2l2 3c0 1 1 2 2 3s1 1 2 1c1 1 2 1 3 1v1h-1-1l-76-1h-6c9-1 18-1 27-1h33c-2-2-6-5-7-7v-1c-1-2-4-4-6-6l-10-11c-2-1-3-3-5-4v-1z" class="T"></path><path d="M671 209c1 0 1 0 2 1v2h-1l-1-2v-1z" class="V"></path><path d="M676 210l2 3c0 1 1 2 2 3s1 1 2 1c1 1 2 1 3 1v1h-1-1l-2-1c-1 0-4-1-4-2v-1l-3-3 2-2z" class="h"></path><defs><linearGradient id="j" x1="660.933" y1="196.297" x2="658.447" y2="208.281" xlink:href="#B"><stop offset="0" stop-color="#887863"></stop><stop offset="1" stop-color="#9d917e"></stop></linearGradient></defs><path fill="url(#j)" d="M658 194c1 0 2 1 3 1h0l1 1c1 1 1 1 2 1h1l1 1-1 2 2 1v1l1 1-4 5c-2 0-1-1-3-1v4l-9-9v-1l1-2h0l1-1c0 1 1 1 1 1v-1h2c-1-1-1-1 0-1l1-1v-2z"></path><path d="M633 187c1-1 2-1 3-1l8 8c3 3 5 5 7 8h1l9 9c1 1 4 3 5 6h-4l-1-1c-3-2-5-5-7-7-1-2-4-4-6-6l-10-11c-2-1-3-3-5-4v-1z" class="B"></path><path d="M651 202h1l9 9c1 1 4 3 5 6h-4l-1-1 1-1c-1-1-1-2-2-3-4-2-7-6-9-10z" class="C"></path><path d="M636 162l2 1 2-1 3 4 6 9c1 2 3 3 4 5-1 1-1 2-1 4 1 0 1 1 2 1l2 2c1 1 2 1 3 1 0 3 0 4-1 6h0v2l-1 1c-1 0-1 0 0 1h-2v1s-1 0-1-1l-1 1h0l-1 2v1h-1c-2-3-4-5-7-8l-8-8c-1 0-2 0-3 1l-2-2-1-3v-1l1 1h1 2v-2l1-2-2-1v-2h0v-1l1-1c0-1-1-2-1-3l-2 2c-1-1-1-1-1-2l2-2v-1h-2c1-1 1-2 2-2 2-1 3-1 4-3z" class="s"></path><path d="M634 182v-2l1-2c2 2 4 4 6 7l-1 1c1 1 2 0 3 1 0 0 1 1 2 1v1l-1 1c-2-1-3-3-5-4-1 0-2-2-3-3v-1h-2z" class="h"></path><path d="M646 192l1-1h1v1 1l2 2c1 1 1 1 1 2h2 0c1 1 0 1 1 1l-1 1h0l-1 2v1h-1c-2-3-4-5-7-8l2-2z" class="Q"></path><path d="M631 185l-1-3v-1l1 1h1 2 2v1c1 1 2 3 3 3 2 1 3 3 5 4 0 1 1 1 2 2l-2 2-8-8c-1 0-2 0-3 1l-2-2z" class="e"></path><path d="M631 185l-1-3v-1l1 1h1c2 1 3 3 4 4-1 0-2 0-3 1l-2-2z" class="N"></path><path d="M636 162l2 1 2-1 3 4c-1 1-2 1-2 2h1c0 1-1 1-1 3l1 1c1-1 1-2 2-2l1 1-2 1c0 1 1 1 1 2-2 0-7-1-8 1l1 1c1-1 3-1 5-1v1h0l-3 3h-1c-1-1-3-4-5-4v-1l1-1c0-1-1-2-1-3l-2 2c-1-1-1-1-1-2l2-2v-1h-2c1-1 1-2 2-2 2-1 3-1 4-3z" class="D"></path><path d="M644 174c0-1-1-1-1-2l2-1-1-1c-1 0-1 1-2 2l-1-1c0-2 1-2 1-3h-1c0-1 1-1 2-2l6 9c1 2 3 3 4 5-1 1-1 2-1 4 1 0 1 1 2 1l2 2c1 1 2 1 3 1 0 3 0 4-1 6-2-1-3-2-4-3-1-2-4-3-5-6 0-1 0 0-1-1v-1s0-1 1-2h0-1l-1 1 1 2v1h0c-2 0-2-1-3-2l1-1-1-1c0-1 0-1-1-1v1l1 1h-3v1l-2-1v-1c1 0 1 0 2-1l-1-2 2-1c0-1 0-2 1-3z" class="c"></path><path d="M588 160c1-1 2-2 3-2s3 1 4 2c4 3 10 2 15 3v1l-2 2-1-1-1 2v1h-1c1 1 1 2 3 3l1-1v1h1c0 1 1 2 2 2 2 1 2 1 3 2v1l-2-1-1 1v1h1 3l-1 2h-1l1 1 1 1v3h-1v2c0 1-1 1-2 1v1h-1l-1-1-2 2h-1c-1 1 0 1-1 1-1 2-2 3-1 6h0v2h-4v2c1 2 0 1 1 2 1 2 1 6 1 7-1 1-4 3-5 3h-2c-2 2-3 1-5 1h0l-1 1-2-2-2-2-4-3h3l-1-1-1-1c-2-1-3-2-4-4 0-1 0-2 1-4-2 1-2 0-4-1v-1-1l-1-1-1 2h-3v2l-3-6 1-4h0-1l-2-2c1 0 2-1 3-2l-1-2c0-1 0-1-1-1 0-1-1-2-2-2h0c-1-1-1-2-1-2-1-1-1-1-2-1l1-2h-1v-1c1-1 1-1 2-1h3c1-1 1-2 1-3l1-1h1c1 0 2-1 3-1v-1l2 1c2-1 3-1 5-1 3-1 5-3 7-5z" class="C"></path><path d="M603 195v-2h1v1c1 1 1 2 2 2v2h-4l1-3z" class="E"></path><path d="M598 171c1 1 3 1 4 2v3c-3 1-4 1-7 1v-3h1c0 1 0 1 1 1l1-1-1-2 1-1z" class="M"></path><path d="M605 184c1 0 1-1 2-2l2 3h0c-1 1-1 1-2 1-3 2-1 3-3 5l-2-2v-4h0 2l1-1zm-21 11l1 1v1 1l1 2-1 1h1v2l2-1v3c-1 0-1 1-2 2l-1-1-1-1c-2-1-3-2-4-4 0-1 0-2 1-4h3v-2z" class="D"></path><path d="M605 184v-1s1-1 1-2c0 0-1-1-1-2l3-2s1-1 1-2c0 0 1-1 2-1s0 1 1 2v1h1 3l-1 2h-1l1 1 1 1v3l-1-1c-1 0-1-1-2-1-1-1-1-1-3-1v1c0 1 0 2 1 3l-1 1h0l-1-1-2-3c-1 1-1 2-2 2z" class="l"></path><path d="M605 168c1 1 1 2 3 3l1-1v1h1c0 1 1 2 2 2 2 1 2 1 3 2v1l-2-1-1 1c-1-1 0-2-1-2s-2 1-2 1c0 1-1 2-1 2l-3 2c0 1 1 2 1 2 0 1-1 2-1 2v1l-1 1h-2c0-2 1-3 1-5 0-1 0-2 1-3 1-2 1-6 1-9z" class="Z"></path><path d="M587 176c1-2 2-3 3-4 2 0 3-1 4 0v1h1l2-1 1 2-1 1c-1 0-1 0-1-1h-1l-1 1c-1 0-2 2-3 3l1 1h4c1-1 1 0 2 0 2 0 2 0 3 1v3h-3l-1-1-1 1v1h3v1c-1 0-1 1-2 1v1c2 0 1-2 3-1v1h0c-1 0-1 0-2 1-2 0-3 0-4-1h-2c-1 0-2-1-3-2v-1c1-1 1-1 3-1-1-1-1-1-2-1v-1-1l-3-1v-3z" class="Q"></path><path d="M580 183c1-3 0-8-1-11h0c0-2 1-2 2-3h2c0 1 2 3 2 4 0 2 0 4 1 6h-1v5 6 1c0 1-1 2-2 3l-1 1 1 1 1-1v2h-3c-2 1-2 0-4-1v-1-1l1-1c3-2 2-7 2-10z" class="k"></path><path d="M585 190c-1-1-2-2-3-2v-1-1c1 0 2 0 3-1v-1 6z" class="b"></path><path d="M585 191c0 1-1 2-2 3l-1 1 1 1 1-1v2h-3c-2 1-2 0-4-1v-1h1c3 0 5-2 7-4z" class="B"></path><path d="M588 205c2-1 0-4 2-5 1 1 2 1 3 2v-1l-1-1c-1-1-1-1-2-1v-1c-3-3-2-3-3-6v-2-1c0-2 0-7 1-8h0v4l1 1-1 1v1l2-1c1 1 1 2 3 2h2c1 0 1 1 2 1l1-1h1c1 1 1 2 1 4l3 2-1 3v2c1 2 0 1 1 2 1 2 1 6 1 7-1 1-4 3-5 3h-2c-2 2-3 1-5 1h0l-1 1-2-2-2-2-4-3h3c1-1 1-2 2-2z" class="F"></path><path d="M587 210c1-1 1-1 3-1 0 1 1 2 1 3l1 1h0l-1 1-2-2-2-2z" class="c"></path><path d="M600 193l3 2-1 3v2c1 2 0 1 1 2 1 2 1 6 1 7-1 1-4 3-5 3h-2c-2 2-3 1-5 1l-1-1h2c2 0 4 0 5-2 1-1 3-4 3-5l-3-2 1-1 1 1 1-1-2-1-1-1 2-2h0v-1h-2c2-2 2-2 2-4z" class="B"></path><path d="M588 160c1-1 2-2 3-2s3 1 4 2c4 3 10 2 15 3v1l-2 2-1-1-1 2v1h-1c0 3 0 7-1 9-1 0-2-1-2-1v-3c-1-1-3-1-4-2l-1 1-2 1h-1v-1c-1-1-2 0-4 0-1 1-2 2-3 4v-5l-2-1-1-1v1l-1-2v1h-2c-1 1-2 1-2 3h0c1 3 2 8 1 11 0 3 1 8-2 10l-1 1-1-1-1 2h-3v2l-3-6 1-4h0-1l-2-2c1 0 2-1 3-2l-1-2c0-1 0-1-1-1 0-1-1-2-2-2h0c-1-1-1-2-1-2-1-1-1-1-2-1l1-2h-1v-1c1-1 1-1 2-1h3c1-1 1-2 1-3l1-1h1c1 0 2-1 3-1v-1l2 1c2-1 3-1 5-1 3-1 5-3 7-5z" class="P"></path><path d="M572 193l2-2c0 1 1 3 1 4h-3v-2z" class="Y"></path><path d="M572 183v-2l1 3c1 1 1 1 2 3h-1v4l-2 2v-10z" class="D"></path><path d="M569 187c0-1 2-1 2-1v-1c0-1 1-1 1-2v10 2 2l-3-6 1-4h0-1z" class="G"></path><path d="M577 186c-1-1-2-1-2-2 0-3-1-7 0-10l2-2v14z" class="Z"></path><path d="M577 172s0-1 1-1c0 5-1 11 0 16 2-1 1-3 2-4 0 3 1 8-2 10l-1 1-1-1c1-2 1-5 1-7v-14z" class="G"></path><path d="M578 171c1-2 3-3 4-4l3-3c1 1 0 1 0 2l-2 2v1h-2c-1 1-2 1-2 3h0c1 3 2 8 1 11-1 1 0 3-2 4-1-5 0-11 0-16z" class="O"></path><path d="M601 168l-1-2c0-1 0-1 1-2 2 0 3 1 6 1l-1 2v1h-1c0 3 0 7-1 9-1 0-2-1-2-1v-3c-1-1-3-1-4-2h0c1-1 1-2 3-2h0v-1z" class="G"></path><path d="M587 165c1-1 2-2 3-2h1 2l1 1h-1l-1 1 2 1c-1 0-1 0-1 1h-2l-1-1v-1h1v-1h-2v2c1 2 2 3 3 4 2-1 1-1 2-2h6 1v1h0c-2 0-2 1-3 2h0l-1 1-2 1h-1v-1c-1-1-2 0-4 0-1 1-2 2-3 4v-5l-2-1-1-1v1l-1-2 2-2 2-1z" class="O"></path><path d="M587 165l1 1c0 2-1 3-1 5l-2-1-1-1v1l-1-2 2-2 2-1z" class="B"></path><path d="M576 166c2-1 3-1 5-1-2 1-7 4-8 7l-1 9v2c0 1-1 1-1 2v1s-2 0-2 1l-2-2c1 0 2-1 3-2l-1-2c0-1 0-1-1-1 0-1-1-2-2-2h0c-1-1-1-2-1-2-1-1-1-1-2-1l1-2h-1v-1c1-1 1-1 2-1h3c1-1 1-2 1-3l1-1h1c1 0 2-1 3-1v-1l2 1z" class="C"></path><path d="M569 168l1-1h1c1 0 2-1 3-1v-1l2 1h-1c-1 1-1 1-2 3 0 1-1 1-2 1h-1c0-1 0-1-1-2z" class="B"></path><path d="M536 439l3 3c1 1 3 3 3 4v7l1 1v45c0 6 1 13 0 19v41 43c-1 14 0 29 0 43v14c0 3 0 6 1 10l1 2c-2 3-2 5-2 8v-6h0c-2 2-1 10-1 13l-1-1v-13l-1-1c-1 2 0 8 0 11 0 4 0 32 1 33v12c-1 1-1 2-1 4 0 3 0 7-1 11h0l-1-1v2c-1-4-1-9-1-13 0-6 2-16 0-22h0c0-2-1-3-1-4l-2-34 1-15-1-11c0-3 1-8-1-11h0l-13 1c-2 3 0 7-1 10l-1-21v-4-25c1-4 1-9 0-13l-1-2c1-1 1-2 1-3l-1-12c0-1 0-1 1-2l-1-1-1-17c1-2 1-6 1-9h-1c-1-1-2-2-3-4-2 1-4 1-6 1v1l-1-3v4c-2 0-5-1-7 0-1 1-2 1-2 1-1 0-1 0-2-1v2h-2v-2c0-1 0-4-1-5 0-1 0-1-1-2 2-1 2-2 4-2h0 2c2-1 4-1 6-1h9 6 3 1l1-1v-15-2-2c1-1 0-1 0-2l1-1v-2c1-1 0-1 0-2 1-2 1-4 1-5h1v-1-1c0-2 1-4 0-5h0l1-3v1c1-1 1-5 1-7 1-4 2-23 5-26l3 2c1-3 1-7 0-10v-2-1z" class="R"></path><path d="M519 572l1-1c1-1 0 1 0-1l1 1c2-1 3-1 5-1-1 1-3 2-3 3-1 0 0 0-1-1h-3z" class="H"></path><path d="M525 534h4v1l-1 1h-1c-1 1-1 1-2 1-1-1 0-1 0-3z" class="d"></path><path d="M525 516h2v-2h3v4c-2 0-4 0-5-1v-1z" class="W"></path><path d="M524 616v-1c1-2 1-5 1-7l1-1v7c0 2 0 2 1 3v3h1l-1 2c-1 0-1 1-3 1l-1-1c0-1 0-1 1-2 0-1 1-2 1-3l-1-1z" class="E"></path><path d="M524 541h1 2c1-1 0-1 1 0h0c-1 1-2 1-3 1 1 1 0 1 1 1l1 1v8l-1 1v1c1 0 1 0 2 1h0l-3 1-1-1v-11-3zm-1 32c2 1 3 1 6 1l1-1c1-1 1-2 1-3 0 1 1 4 0 5l-6 1c2 1 0 0 2 0 1 0 2 1 3 1h-6-5c0 2 0 3-1 4l-1-2c1-1 1-2 1-3h1v-4h3c1 1 0 1 1 1z" class="S"></path><path d="M520 570c1-1 1-3 1-3 0-2 2-2 3-4s0-4 1-6c1 0 2 0 3 1l-1 1c1 0 0 0 1 1h-2v1h2l-1 1h0c1 1 1 1 2 1v1 1c-1 1-1 2-2 3h1l1-1v1 1c-1 1-2 1-3 1-2 0-3 0-5 1l-1-1z" class="I"></path><path d="M529 565h-1l-1 1c-2 1-2 3-4 3l-1-1c0-1 0-1 1-2v1h1v-2c1 0 2 0 2-1h-1l2-2h0c1 1 1 1 2 1v1 1z" class="H"></path><path d="M530 577h1l-1 1 1 1h2c1 2 1 3 1 5l-1 1v2 1h-1-5-1c-1-1-1-2-1-3l-2 1v-1c-1-1-1-1-1-2v-4l2-2h6z" class="O"></path><path d="M522 583l3-3v1 4l-2 1v-1c-1-1-1-1-1-2z" class="E"></path><path d="M531 581l1-1c1 2 1 3 0 5v1l-2-1c-1 0-1 0-2 1l-1-1v-1c0-1 0-2 1-2l2 1c1-1 1-1 1-2z" class="c"></path><path d="M530 577h1l-1 1 1 1h2c1 2 1 3 1 5l-1 1h-1c1-2 1-3 0-5l-1 1h-6v-1l-3 3v-4l2-2h6z" class="L"></path><path d="M530 577h1l-1 1c-2 0-3 1-5 2l-3 3v-4l2-2h6z" class="d"></path><defs><linearGradient id="k" x1="534.61" y1="462.904" x2="538.536" y2="481.279" xlink:href="#B"><stop offset="0" stop-color="#313232"></stop><stop offset="1" stop-color="#5e5e5e"></stop></linearGradient></defs><path fill="url(#k)" d="M535 455c1 1 1 2 1 3 0 2 1 2 2 3h0v4l1 30h-1v-13c-2-1-2-1-2-2-2 5 1 13-1 17v-42z"></path><path d="M516 535l1-3h1v2c1-1 1-1 1-2 1 1 1 1 1 3v3c1 0 1 1 2 1 1 2 1 5 1 8v14c-2 1-2 1-5 1h0l-1-1-1-17c1-2 1-6 1-9h-1z" class="I"></path><path d="M519 540h0c1-1 1 0 1-1 1 3 2 19 1 22-1-2-1-4-1-6h0-1c-1-2 0-4 0-6v-9z" class="l"></path><defs><linearGradient id="l" x1="522.83" y1="545.386" x2="513.237" y2="553.393" xlink:href="#B"><stop offset="0" stop-color="#6e6f67"></stop><stop offset="1" stop-color="#857e7a"></stop></linearGradient></defs><path fill="url(#l)" d="M516 535l1-3h1v2c1-1 1-1 1-2 1 1 1 1 1 3-1 0-2 0-3 1h1c1 1 1 2 1 4h0v9c0 2-1 4 0 6h1 0c0 2 0 4 1 6l-3 1h0l-1-1-1-17c1-2 1-6 1-9h-1z"></path><path d="M518 581c1-1 1-2 1-4h5l-2 2v4c0 1 0 1 1 2v1c0 1 0 2 1 3v1c0 1 1 1 1 2s0 2-1 4v2c2 2 5 0 7 1h0l2 2v4h-6c-1 0-2 0-3-1h-3 0-1v5h-1v-5c-1 7 1 13-1 19v-4-25c1-4 1-9 0-13z" class="N"></path><path d="M522 524l1-1v-15-2-2c1-1 0-1 0-2l1-1v-2c1-1 0-1 0-2 1-2 1-4 1-5h1v-1-1c0-2 1-4 0-5h0l1-3v1l-1 18c0 5 1 10-1 15v1 1c-1 2-1 4-1 6 1 1 2 2 3 2 1 1 2 0 3 1-3 1-6 1-9 1v-1h-10-1c-2 1-4 1-6 1 0 1 0 1 1 2h1v4c-2 0-5-1-7 0-1 1-2 1-2 1-1 0-1 0-2-1v2h-2v-2c0-1 0-4-1-5 0-1 0-1-1-2 2-1 2-2 4-2h0 2c2-1 4-1 6-1h9 6 3 1z" class="B"></path><path d="M497 525c2-1 4-1 6-1h9 6 3 1l1 1c-1 0-1 1-2 0h-8c1 0 3 0 4 1 2 0 5-1 7 1h-3-10c-5 0-10 1-15 0l-1-2h2z" class="h"></path><path d="M495 525h0l1 2c5 1 10 0 15 0h-1c-2 1-4 1-6 1 0 1 0 1 1 2h1v4c-2 0-5-1-7 0-1 1-2 1-2 1-1 0-1 0-2-1v2h-2v-2c0-1 0-4-1-5 0-1 0-1-1-2 2-1 2-2 4-2z" class="L"></path><path d="M495 534v-2h1 2c1 0 2-1 2-1l1-1h5v4c-2 0-5-1-7 0-1 1-2 1-2 1-1 0-1 0-2-1z" class="E"></path><path d="M518 623c2-6 0-12 1-19v5h1v-5h1 0 3c1 1 2 1 3 1l-1 2-1 1c0 2 0 5-1 7v1l1 1c0 1-1 2-1 3-1 1-1 1-1 2l1 1c2 0 2-1 3-1l1-2c2-2 3-6 4-9 1 3 0 5 1 8v11 3h0l-13 1c-2 3 0 7-1 10l-1-21z" class="W"></path><path d="M521 604h0c0 1 1 2 2 3v2 1l-1 1v1c0 1 0 2-1 3v1 1l-1-1c0-3 0-9 1-12z" class="H"></path><path d="M522 628l2-1h6l-1 2 2 1h1v-1l1 1v3h0l-13 1v-4c1-1 1-1 2-1v-1z" class="C"></path><path d="M522 628c1 1 2 1 3 2-1 0-2 1-3 2h1 1 2l1-1h0v-1h1 1l-1 1 1 1c1 0 2 0 3 1h1l-13 1v-4c1-1 1-1 2-1v-1z" class="Y"></path><path d="M532 611c1 3 0 5 1 8v11l-1-1v1h-1l-2-1 1-2 1-1h-3-2-1c-2 0-3 1-5-1 0-4 3-6 4-9l1 1c0 1-1 2-1 3-1 1-1 1-1 2l1 1c2 0 2-1 3-1l1-2c2-2 3-6 4-9z" class="B"></path><path d="M536 439l3 3c1 1 3 3 3 4v7l1 1v45c0 6 1 13 0 19v41 43c-1 14 0 29 0 43v14c0 3 0 6 1 10l1 2c-2 3-2 5-2 8v-6h0c-2 2-1 10-1 13l-1-1v-13l-1-1c-1 2 0 8 0 11 0 4 0 32 1 33v12c-1 1-1 2-1 4 0 3 0 7-1 11h0l-1-1v2c-1-4-1-9-1-13 0-6 2-16 0-22h0c0-2-1-3-1-4l-2-34 1-15-1-11c0-3 1-8-1-11v-3-11c-1-3 0-5-1-8 0-2 1-3 2-5l1-109c2-4-1-12 1-17 0 1 0 1 2 2v13h1l-1-30v-4h0c-1-1-2-1-2-3 0-1 0-2-1-3 0-1 1-1 1-3 1-3 1-7 0-10v-2-1z" class="Q"></path><path d="M532 611c0-2 1-3 2-5v38c0-3 1-8-1-11v-3-11c-1-3 0-5-1-8z" class="W"></path><defs><linearGradient id="m" x1="546.168" y1="713.756" x2="527.244" y2="706.212" xlink:href="#B"><stop offset="0" stop-color="#1a1c1e"></stop><stop offset="1" stop-color="#5b5a59"></stop></linearGradient></defs><path fill="url(#m)" d="M539 647c1 11-1 23 1 35 0 4 0 32 1 33v12c-1 1-1 2-1 4 0 3 0 7-1 11h0l-1-1v2c-1-4-1-9-1-13 0-6 2-16 0-22h0c0-2-1-3-1-4l-2-34 1-15v6 13c0 1 1 2 1 3 0 2-1 6 0 8 1 1 2 1 3 0v-6-3-5h-1v-12-6c1-2 1-4 1-6z"></path><path d="M536 439l3 3c1 1 3 3 3 4v7l1 1v45c0 6 1 13 0 19v41 43c-1 14 0 29 0 43v14c0 3 0 6 1 10l1 2c-2 3-2 5-2 8v-6h0c-2 2-1 10-1 13l-1-1v-13l-1-1c-1 2 0 8 0 11-2-12 0-24-1-35 1-5 0-12 0-18v-32-102l-1-30v-4h0c-1-1-2-1-2-3 0-1 0-2-1-3 0-1 1-1 1-3 1-3 1-7 0-10v-2-1z" class="B"></path><path d="M543 559c0-1 0-1-1-2v-7-19c0-3-1-7 0-10 0-1 0-2 1-3v41z" class="S"></path><path d="M536 439l3 3c1 1 3 3 3 4v7 6c-2-2-3-4-5-6l-1-1c1-3 1-7 0-10v-2-1z" class="K"></path><path d="M539 449c1 0 1 1 2 2l-1 1h-1c-1-1 0-1 0-3z" class="Z"></path><path d="M490 192v1c-2 6-6 13-7 20 0 1-2 8-1 9-1 0-1 1-1 2-2 4-3 9-4 14-2 4-2 10-2 14l2 15c0 10 0 20 2 30h0c0 2 0 5 1 6s4 3 6 3l1-1 1 2h0l-5 3-4 2c0 1 0 1 1 2v18l6 44v3c0 1 0 2-1 3v-3 1c-1 1-1 2-1 3s0 2 1 3v2 1h-2v-4c-1-1-1-3-1-4h0v2c-1-5-1-11-2-16l-5 5-1-1-2 3v-1-3c1-3 1-5 1-8h0c-1-1 0-2 0-4-4 0-8 0-11 1h-1l-1-2h-1v-1l-4-5c-1 0-2 0-3-1h-1c-1-3-1-6-1-9v-2c0-1-1-1-1-2l-2 1h-4s-1 1-2 1h1-4v-6l-1-3c-1-1-1-1-3-1l-1 1-1-13h5l3-1h0l-2-2h-7v-1-6c-2-6-1-12-2-18v-23c1 0 2-1 3-2h1c3-2 5-5 7-8-1-1-1-2-1-3h0-1c0 1-1 1-2 2-2-2-4-4-8-6h3c1 1 1 1 2 1h0l-1-1v-3c-1-6-3-11-3-16 0-1 0-1-1-2l-1-1v-1h1l1-1c0-2-2-4-3-6-1-1-1-2-1-3h0 1c3 0 5 0 7-2s2-4 3-6l1-1 2 2c3 3 5 7 9 8h1 2 1c1 0 3 2 3 3v2h1 1v1h-1c1 2 2 3 3 4 3 2 7 5 9 7 1 1 1 1 2 1l1-1c-1-1-1-2-2-3h2 0 1v-1l3-6c2-1 2-3 3-5v5h1c2-6 2-12 4-18 1-5 4-10 7-14z" class="M"></path><path d="M452 217c1 0 3 2 3 3v2h1l-3 5c-1 1-3 2-4 4-2 2-4 4-6 5 1-2 1-4 3-6v-2c1-1 2-2 4-3 0 1 0 1 1 1l1-1v-2-1-2c1 0 1-1 1-1 0-1-1-2-2-2h1z" class="F"></path><path d="M439 209c3 3 5 7 9 8h1 2c1 0 2 1 2 2 0 0 0 1-1 1v2 1 2l-1 1c-1 0-1 0-1-1-2 1-3 2-4 3h-1-1v3l-1 1-2-1 1-1c1-2 1-6 0-8v-1c-1-3-1-4-2-6h-1v-6z" class="P"></path><path d="M439 209c3 3 5 7 9 8h1 2c1 0 2 1 2 2 0 0 0 1-1 1v2 1 2l-1 1c-1 0-1 0-1-1l-1-1c-1-1-1-1 0-2-2-1-3-1-4-3l-2-4c-1-1-2-3-3-3v3h-1v-6z" class="O"></path><path d="M454 227l2-2c5 5 11 10 16 14 1 0 4-13 8-15h1c-2 4-3 9-4 14-2 4-2 10-2 14v-2-1h-1l-1-3c1-1 1-2 1-3-1-1 0-1-1-1l-4-3h0l2 3-9-7v1l5 4h-1c-3-3-6-6-10-8-1-1-3-2-3-3l1-2z" class="N"></path><path d="M490 192v1c-2 6-6 13-7 20 0 1-2 8-1 9-1 0-1 1-1 2h-1c-4 2-7 15-8 15-5-4-11-9-16-14l-2 2h-1l3-5h1v1h-1c1 2 2 3 3 4 3 2 7 5 9 7 1 1 1 1 2 1l1-1c-1-1-1-2-2-3h2 0 1v-1l3-6c2-1 2-3 3-5v5h1c2-6 2-12 4-18 1-5 4-10 7-14z" class="f"></path><path d="M436 208l1-1 2 2v6h1c1 2 1 3 2 6v1c1 2 1 6 0 8 0-1 0-2-1-3h-2c-1 1-2 1-3 1l-7-1-1-1 1-1c0-2-2-4-3-6-1-1-1-2-1-3h0 1c3 0 5 0 7-2s2-4 3-6z" class="C"></path><path d="M435 215h0c1-1 1-2 2-4v-1c1 0 1 1 1 2s0 1-1 2v2c-1 1-2 2-3 2s-1 0-2-1c1 0 2-1 3-2z" class="W"></path><path d="M433 214l2 1c-1 1-2 2-3 2 1 1 1 1 2 1l-3 1-1 1-4-1c-1-1-1-2-1-3h0 1c3 0 5 0 7-2z" class="L"></path><path d="M434 220c1 0 3-1 5 0 1 2 1 5 0 7-1 1-2 1-3 1-1-1-1-3-1-4 0-2-1-3-1-4z" class="P"></path><path d="M426 219l4 1h4c0 1 1 2 1 4 0 1 0 3 1 4l-7-1-1-1 1-1c0-2-2-4-3-6z" class="V"></path><path d="M427 226h1l1 1 7 1c1 0 2 0 3-1h2c1 1 1 2 1 3l-1 1v2c-2 4-3 8-5 12 0 1 0 2-1 3l2 1v-1c1-1 1-1 2-3 0-1-1 0 0-1h1 1c0-1 1-1 1-2 1 0 2-1 3-1v1 3c-2 0-2 0-3 1 1 1 1 0 2 0 1 1 1 6 1 7-1 0-2 0-3-1v1c-1 1-1 2-2 3-1-1-1-2-1-3h0-1c0 1-1 1-2 2-2-2-4-4-8-6h3c1 1 1 1 2 1h0l-1-1v-3c-1-6-3-11-3-16 0-1 0-1-1-2l-1-1v-1z" class="e"></path><path d="M435 248l2 1v-1c1-1 1-1 2-3 0-1-1 0 0-1h1c-2 3-2 7-3 10l-3-3 1-3z" class="q"></path><path d="M439 227h2c1 1 1 2 1 3l-1 1v2c-1 0-2-1-3-1l-1-1-8-3v-1l7 1c1 0 2 0 3-1z" class="S"></path><path d="M427 226h1l1 1v1h0c1 1 1 2 2 3l1 2c1 3 2 7 2 10l-2 3c-1-6-3-11-3-16 0-1 0-1-1-2l-1-1v-1z" class="s"></path><path d="M456 306l3 4c1 2 1 4 1 6v10c1 3 1 6 1 10l-1 17v1c-1-1-1-3-1-4-3-1-6 0-8 0-1-3-1-6-1-9v-2c0-1-1-1-1-2l-2 1h-4s-1 1-2 1h1-4v-6l-1-3c-1-1-1-1-3-1l-1 1-1-13h5l3-1c2 1 6 1 8 1 0-2 0-3-1-5h4l1-1h1 2 1c0-2-1-3 0-4v-1z" class="t"></path><path d="M460 340v10l-1-1h-2l-1-1 2-1c1-1 0-2 0-4h1v-2l1-1z" class="I"></path><path d="M451 329c0-2 2 0 4-1l1-1v-4c1-1 1-1 1-2-1-1-1-1-2-1v-1h1l-1-1h-1c1-1 2-1 2-2v-3l1-1 1 1v2c-1 2 0 4 0 6v3l1 1c0-3 0-7 1-9v10c0 4 1 9 0 14l-1 1v2h-1c0 2 1 3 0 4l-2 1v-1h-2 0v-1l-2-1v-2-3h1c1 0 2-1 3-1v-3h-2v-1l2-1c0-2-1-1-2-3l2 1v-1-2h-5z" class="L"></path><path d="M456 347h-1c1-2 0-3 1-4h2c0 2 1 3 0 4l-2 1v-1z" class="H"></path><path d="M456 306l3 4c1 2 1 4 1 6-1 2-1 6-1 9l-1-1v-3c0-2-1-4 0-6v-2l-1-1-1 1v3c0 1-1 1-2 2h1l1 1h-1v1c1 0 1 0 2 1 0 1 0 1-1 2v4l-1 1c-2 1-4-1-4 1l-1 1v1h2l-2 2v2c-1 0-1 1-1 1v-1l-1-1v-3l1-1h-2l1-1c-2-2-2-3-4-5h2c1-1 0-4 0-6h-2l-1-1h-5-1l3-1c2 1 6 1 8 1 0-2 0-3-1-5h4l1-1h1 2 1c0-2-1-3 0-4v-1z" class="C"></path><path d="M433 330l-1-13h5 1 5l1 1h2c0 2 1 5 0 6h-2c2 2 2 3 4 5l-1 1h2l-1 1-1 1v-1c-1 1-1 1-1 2l1 1c-1 2-2 3-4 4 0 0-1 1-2 1h1-4v-6l-1-3c-1-1-1-1-3-1l-1 1z" class="b"></path><path d="M437 330c0-4 0-9 2-12h0l1 1c1 1 1 2 2 3h1l1-3 1 1c0 1 0 2-1 3s-1 1-2 1h-1v-1l-2 1c1 1 1 2 1 3v4h0c-1-1-1-1-1-2-1 1-1 3-1 4l-1-3z" class="P"></path><path d="M441 339v-3l1-1v-2c-1-2-1-4-1-7 1-2 1-1 3-2 2 2 2 3 4 5l-1 1h2l-1 1-1 1v-1c-1 1-1 1-1 2l1 1c-1 2-2 3-4 4 0 0-1 1-2 1z" class="e"></path><path d="M433 330l-1-13h5 1 5l1 1v1l-1 3h-1c-1-1-1-2-2-3l-1-1h0c-2 3-2 8-2 12-1-1-1-1-3-1l-1 1zm41-81h1v1 2l2 15c0 10 0 20 2 30h0c0 2 0 5 1 6s4 3 6 3l1-1 1 2h0l-5 3-4 2c0 1 0 1 1 2v18l6 44v3c0 1 0 2-1 3v-3 1c-1 1-1 2-1 3s0 2 1 3v2 1h-2v-4c-1-1-1-3-1-4h0v2c-1-5-1-11-2-16l-5 5-1-1-2 3v-1-3c1-3 1-5 1-8h0c-1-1 0-2 0-4h0c1-3 1-7 1-11v-18-80z" class="S"></path><path d="M472 370l1 1c1-2 1-6 2-9l3-7c1 2 1 5 2 8v4l-5 5-1-1-2 3v-1-3z" class="O"></path><path d="M480 363v4l-5 5-1-1 3-2c0-1 0-2 1-3 0-1 1-1 1-1l1-2z" class="C"></path><path d="M440 256c1-1 1-2 2-3v-1c1 1 2 1 3 1v1c0 6-1 13 0 18 1 3 1 8 1 11l-1-2c-1 0-1 0-2 1h-1l3 3v1c1-1 1-1 1-2 0 1 1 1 1 2 2 8 5 14 9 20v1c-1 1 0 2 0 4h-1-2-1l-1 1h-4c1 2 1 3 1 5-2 0-6 0-8-1h0l-2-2h-7v-1-6c-2-6-1-12-2-18v-23c1 0 2-1 3-2h1c3-2 5-5 7-8z" class="R"></path><path d="M445 254c0 6-1 13 0 18-1 3-1 5-1 8l-1-1v-5c0-6 0-14 2-20z" class="B"></path><path d="M438 314v-1h-6c0-4 0-45 1-46v45h3c0-2 0-4-1-7h2l3 3c0 1 0 1 1 2h1v1l1 1h0c1-1 1-1 1-3h1v3h2c1 2 1 3 1 5-2 0-6 0-8-1h0l-2-2z" class="Q"></path><path d="M429 266c1 0 2-1 3-2h1v3c-1 1-1 42-1 46h6v1h-7v-1-6c-2-6-1-12-2-18v-23z" class="X"></path><path d="M445 272c1 3 1 8 1 11l-1-2c-1 0-1 0-2 1h-1l3 3v1c1-1 1-1 1-2 0 1 1 1 1 2 2 8 5 14 9 20v1c-1 1 0 2 0 4h-1-2-1l-1 1h-4-2v-3h-1c0 2 0 2-1 3h0l-1-1v-1h-1c-1-1-1-1-1-2l-3-3h-2l1-23 1-1h6l1-1c0-3 0-5 1-8z" class="s"></path><path d="M445 312l2-1v-3c1 0 1 0 2-1h0c2 1 1 2 2 3 0 1 1 1 0 2h-4-2z" class="S"></path><path d="M444 299v1l-3-3c1-4 1-8 1-13 0 2 1 4 1 6v5l-1 3 2 1z" class="P"></path><path d="M443 295h2v1l-1 2h1 1c0-1 1-1 1-2h0c2 2 2 6 2 8l-1 2h0c-1-1-2-2-2-3v-2l-2-2-2-1 1-3z" class="T"></path><path d="M435 305l1-23 1-1v2s2 1 2 2v5 4h-1c-1 1 0 1-1 2 0 2 0 3 1 5v3c-1 1-1 0-1 1h-2z" class="g"></path><path d="M437 283s2 1 2 2v5l-1 1h-1v-8z" class="V"></path><path d="M447 286c2 8 5 14 9 20v1c-1 1 0 2 0 4h-1-2-1l-1 1c1-1 0-1 0-2-1-1 0-2-2-3h-1-1c-1 0-1 0-2-2 1 1 1 1 3 1h0l1-2c0-2 0-6-2-8h0v-5-5z" class="I"></path><path d="M447 296h1l1 1 1 1 1 3c-1 1-1 1-1 2h1 1c1 1 0 1 1 3h-5l1-2c0-2 0-6-2-8z" class="W"></path><path d="M498 289c3-5 6-8 11-10l4 1c3 0 6 2 8 4 4 7 6 15 12 22v5l1-5 3 3v1 1c1 0 1 0 2 1 1 0 1 0 2 1h-1c1 2 1 4 1 6h1c1 12-2 26-4 38l-4 22c0 1 0 5-1 5v4l2 2-4 13v-3c-1 1-2 2-2 3-1 1-2 1-2 2-1 3-3 4-4 6 0 2-2 4-4 5-1 1-1 2-2 3 0 1 0 2 1 3h0 1l-1 1-3 2c-2 2-3 2-6 1h0c-3-1-5-3-8-5-1 0-2-2-3-3-3-5-5-8-7-14-1-5-3-10-4-15v-1c-1-2-2-4-2-6 1-1 1-2 1-3v-3l-6-44v-18c-1-1-1-1-1-2l4-2 5-3h0l-1-2c0-1 3-4 4-5 2-4 4-7 6-11h1z" class="C"></path><path d="M516 403c3 0 5-2 7-4v1c0 1-2 2-3 3v1c1 0 1 1 1 2l1 1c-2 2-5 5-8 6v-6-1-3h2z" class="G"></path><defs><linearGradient id="n" x1="520.797" y1="385.125" x2="521.176" y2="399.363" xlink:href="#B"><stop offset="0" stop-color="#343433"></stop><stop offset="1" stop-color="#4c4d4e"></stop></linearGradient></defs><path fill="url(#n)" d="M522 387c1-1 3-2 3-3 1-1 2-2 3-4l3-3c0-1 0-2 1-3h0l-1 3c0 1 0 2-1 2v1l1 1c-1 2-3 3-4 5 0 1 0 2-1 4-1 1-1 1-1 2 0 2-3 4-5 6h0 0c-2 1-3 3-5 3v-6h2v-1h-2 0v-1c3-1 5-3 7-5v-1z"></path><path d="M533 311l1-5 3 3v1 1c1 0 1 0 2 1 1 0 1 0 2 1h-1l-5-2c-2 13-5 24-14 33-1 1-3 3-5 3h-1-2l1-1c1-1 2-1 4-2s3-3 5-6c6-8 8-17 10-27z" class="f"></path><path d="M521 344h1l3-3 1 1c-1 1-5 3-5 4v2c1 3 0 4-2 6 0 1-1 1-1 2l10-8h1l-1 1h1l2-1h0c-1 2-3 3-3 4v1l5-4h0l-2 3c-5 4-8 9-13 12-1 1-1 2-2 3l-1 1c0 1 0 1 1 2h-1v-10-13h1c2 0 4-2 5-3z" class="D"></path><path d="M528 349h1l2-1h0c-1 2-3 3-3 4v1h-1c-3 4-7 9-11 10v-1c1-2 1-2 1-4 1-1 3-2 4-3 2-2 5-4 7-6z" class="C"></path><path d="M529 348h0c3-2 5-5 7-8 0-1 1-1 1-2 1 2 0 2 1 3v1c-1 0-1 1-1 2h0c0 2 0 4-1 5-1 2 0 4-1 7 0 1 0 2-1 3l1 1v1c-1 1-1 2-1 4-1 1-1 2-1 4h0v3l-1 1v1h0c-1 1-1 2-1 3l-3 3c-1 2-2 3-3 4 0 1-2 2-3 3l-4 4c-2 0-2 0-3-1v-3h2l3-3v-1l1-1c2-4 6-8 8-12v-1l-2 4-1-1h0c1-2 2-3 2-5v-1c1-1 2-3 3-5v-1c-1 1-1 2-2 2h0v-1c1-1 1-1 1-2 1-3 2-5 4-7l-1-1c-1 1-1 1-2 1l2-3h0l-5 4v-1c0-1 2-2 3-4h0l-2 1h-1l1-1z" class="G"></path><path d="M531 352c1 0 1 0 2-1l1 1c-2 2-3 4-4 7 0 1 0 1-1 2v1h0c1 0 1-1 2-2v1c-1 2-2 4-3 5v1c0 2-1 3-2 5h0l1 1 2-4v1c-2 4-6 8-8 12l-1 1v1l-3 3h-2v-2l2-1v-1l-2 1v-5l1 1 2-2v-1l-2 2-1-1v-5c1-2 2-2 2-4l-1 1c-1-1-1-1-1-2l1-1c1-1 1-2 2-3 5-3 8-8 13-12z" class="C"></path><path d="M513 347h2v13 10h1l1-1c0 2-1 2-2 4v5l1 1 2-2v1l-2 2-1-1v5l2-1v1l-2 1v2 3c1 1 1 1 3 1l4-4v1c-2 2-4 4-7 5v1h0 2v1h-2v6c2 0 3-2 5-3h0l-2 3c-1 1-1 2-2 2h-2v3h-1c0 2 0 3-1 5-1-1 0-4 0-5l-2-1v-7-8c0-3 1-6 1-9-1-5-2-9-2-14 0-6 1-13 2-18 1-2 1-2 2-2z" class="K"></path><path d="M513 347h2v13 10h1l1-1c0 2-1 2-2 4v5l1 1 2-2v1l-2 2-1-1v5l2-1v1l-2 1v2 3c1 1 1 1 3 1l4-4v1c-2 2-4 4-7 5v1h0 2v1h-2v6c2 0 3-2 5-3h0l-2 3c-1 1-1 2-2 2h-2c-1-2 0-3 0-6v-19-20c0-4 0-8-1-11z" class="M"></path><path d="M509 347c2 0 4-1 5-1l-1 1c-1 0-1 0-2 2-1 5-2 12-2 18 0 5 1 9 2 14 0 3-1 6-1 9v8 7l2 1c0 1-1 4 0 5 1-2 1-3 1-5h1v1 6c3-1 6-4 8-6 7-7 8-15 11-23h0v4l2 2-4 13v-3c-1 1-2 2-2 3-1 1-2 1-2 2-1 3-3 4-4 6 0 2-2 4-4 5-1 1-1 2-2 3 0 1 0 2 1 3h0 1l-1 1-3 2c-2 2-3 2-6 1h0c-3-1-5-3-8-5h1c1 1 2 1 3 2v-2-1l-2-2h6 0v-5h0c-1-3-1-5 0-8-2-2-1-18-1-21v-22-6-6-1l1-2z" class="l"></path><path d="M509 405c0 2 1 4 1 6 1 2 1 3 1 4l-2-2h0c-1-3-1-5 0-8z" class="P"></path><path d="M533 388l2 2-4 13v-3c-1 1-2 2-2 3-1 1-2 1-2 2-1 3-3 4-4 6l-1 1-1-1c1-1 2-2 2-3l1-1c0-1 1-1 1-2 4-5 6-11 8-17z" class="C"></path><defs><linearGradient id="o" x1="520.151" y1="416.43" x2="505.911" y2="421.237" xlink:href="#B"><stop offset="0" stop-color="#3c3c3f"></stop><stop offset="1" stop-color="#5e5c56"></stop></linearGradient></defs><path fill="url(#o)" d="M523 408c0 1-1 2-2 3l1 1 1-1c0 2-2 4-4 5-1 1-1 2-2 3 0 1 0 2 1 3h0 1l-1 1-3 2c-2 2-3 2-6 1h0c-3-1-5-3-8-5h1c1 1 2 1 3 2v-2-1l-2-2h6 2c2 0 3-2 5-3 2-2 5-4 7-7z"></path><path d="M487 377v-2c0-1 0-1 1-2h0c0 1 1 2 2 3v-2h1v1 1l2 2c1 1 1 2 2 2 2 1 3 2 5 3v-1c1 2 2 3 3 3h2v3h1v-6l2 2c0 3-1 19 1 21-1 3-1 5 0 8h0v5h0-6l2 2v1 2c-1-1-2-1-3-2h-1c-1 0-2-2-3-3-3-5-5-8-7-14-1-5-3-10-4-15v-1c-1-2-2-4-2-6 1-1 1-2 1-3v-3l1 1z" class="Z"></path><path d="M486 379l1 10v-1c-1-2-2-4-2-6 1-1 1-2 1-3z" class="L"></path><path d="M491 404h2v1c1 3 4 5 6 7 1 2 2 4 4 6l2 2v1 2c-1-1-2-1-3-2h-1c-1 0-2-2-3-3-3-5-5-8-7-14z" class="O"></path><path d="M490 388c4 11 9 18 17 25 1 2 2 3 1 4l-1-1c-5-3-11-9-14-14-2-4-3-9-4-13l1-1z" class="f"></path><path d="M490 388v-1c1 0 1 0 1 1 1 1 2 3 3 4 1 2 3 4 4 6l2 2c1 1 2 2 4 3l2 2c0 1 0 3 1 4v4c-8-7-13-14-17-25z" class="P"></path><path d="M487 377v-2c0-1 0-1 1-2h0c0 1 1 2 2 3v-2h1v1 1l2 2c1 1 1 2 2 2 2 1 3 2 5 3v-1c1 2 2 3 3 3h2v3h1v-6l2 2c0 3-1 19 1 21-1 3-1 5 0 8h0v5l-1-1c1-1 0-2-1-4v-4c-1-1-1-3-1-4l-2-2c-2-1-3-2-4-3l-2-2c-1-2-3-4-4-6-1-1-2-3-3-4 0-1 0-1-1-1v1l-1 1v-6c0-2-1-5-2-6z" class="J"></path><path d="M494 392v-2h0c1 1 1 1 3 1 1 1 2 2 3 5l-2 2c-1-2-3-4-4-6z" class="V"></path><path d="M487 377v-2c0-1 0-1 1-2h0c0 1 1 2 2 3v-2h1v1c0 2 0 3 1 4v1c1 2 3 3 4 4l1 2h-1l-2-2h0c-1 1-1 1 0 2 1 2 1 3 3 5-2 0-2 0-3-1h0v2c-1-1-2-3-3-4 0-1 0-1-1-1v1l-1 1v-6c0-2-1-5-2-6z" class="g"></path><path d="M491 375v1l2 2c1 1 1 2 2 2 2 1 3 2 5 3v-1c1 2 2 3 3 3h2v3h1v-6l2 2c0 3-1 19 1 21-1 3-1 5 0 8h0v5l-1-1c1-1 0-2-1-4v-4c-1-1-1-3-1-4l-2-2c-2-1-3-2-4-3l3-2-2-3v-2l2 1 1-1c-1 0-1 0-1-1h2l-1-1c-2-1-4-4-5-5s-1-2-2-2h-1c-1-1-3-2-4-4v-1c-1-1-1-2-1-4z" class="V"></path><path d="M503 398l-2-3v-2l2 1v1c1 0 1 0 1 1h1c0 1 0 2 1 3 0 2 0 4 1 5l-1 1-2-2c-2-1-3-2-4-3l3-2z" class="T"></path><path d="M503 398c1 2 1 3 1 5-2-1-3-2-4-3l3-2z" class="V"></path><path d="M480 314c-1-1-1-1-1-2l4-2c3 7 3 14 6 21l3 6c1 1 2 3 4 5l4 4c1-1 2 0 3 0l1-2 5 3-1 2v1 6 6 22l-2-2v6h-1v-3h-2c-1 0-2-1-3-3v1c-2-1-3-2-5-3-1 0-1-1-2-2l-2-2v-1-1h-1v2c-1-1-2-2-2-3h0c-1 1-1 1-1 2v2l-1-1-6-44v-18z" class="a"></path><path d="M491 351l6 6c1 0 2-1 2-1h0l1 2v1 1h0v1c-1 0-1 0-2-1h-2l-1-1c-1-1-1-2-2-2l-2-2 1-1-1-3z" class="U"></path><path d="M508 356v6c-1 4-1 7-2 10v-1c0-1 0-2-1-4v-2c-1-1-1-2-1-3l-2-2-2-2-1-2c2 1 3 2 3 3l3 3c2-2 0-2 1-4 0-1 2-2 2-2z" class="n"></path><path d="M506 371v1c1-3 1-6 2-10v22l-2-2v6h-1v-3c-1-2-2-3-4-4-1-2-4-5-6-7-1 0-2-1-3-2 1-1 1-1 1-2 2 2 5 4 7 5 2 2 3 4 5 4 1-1 1-2 0-4h0l1-1h-1 0l-2-2 1-1h2z" class="T"></path><path d="M495 374h0c3 2 5 5 8 6 2 0 2 1 3 2h0v6h-1v-3c-1-2-2-3-4-4-1-2-4-5-6-7z" class="g"></path><path d="M480 332l2-2c0 2 0 7 1 9 0 1 2 2 2 2 2 3 4 5 6 7 0 2 0 2 2 3 1 0 3 2 5 3l1 2s-1 1-2 1l-6-6 1 3-1 1-1-1h-1c-1 1 1 2 0 3-2-2-3-4-3-6s-1-4-2-6v3c0 2 0 3 1 5v1c1 1 1 1 1 2v1l1 1v1c1 2 2 4 2 5l3 8c1 1 2 2 3 2 2 2 5 5 6 7 2 1 3 2 4 4h-2c-1 0-2-1-3-3v1c-2-1-3-2-5-3-1 0-1-1-2-2l-2-2v-1-1h-1v2c-1-1-2-2-2-3h0c-1 1-1 1-1 2v2l-1-1-6-44z" class="J"></path><path d="M490 354c0-1-1-3-1-4-1-1-1-1 0-2h1c0 1 0 2 1 3h0l1 3-1 1-1-1z" class="X"></path><path d="M489 364l3 8c1 1 2 2 3 2 2 2 5 5 6 7 2 1 3 2 4 4h-2c-1 0-2-1-3-3l-3-2c-4-3-7-11-8-16z" class="e"></path><path d="M480 314c-1-1-1-1-1-2l4-2c3 7 3 14 6 21l3 6c1 1 2 3 4 5l4 4c1-1 2 0 3 0l1-2 5 3-1 2v1 6s-2 1-2 2c-1 2 1 2-1 4l-3-3c0-1-1-2-3-3h0l-1-2c-2-1-4-3-5-3-2-1-2-1-2-3-2-2-4-4-6-7 0 0-2-1-2-2-1-2-1-7-1-9l-2 2v-18z" class="a"></path><path d="M498 351l2-1c2 1 3 3 4 5l1 1v1l-1-1-2-2c-2 0-3-1-4-3z" class="V"></path><path d="M505 349h3v1c-1 1-1 3-1 5h-1c-1-1-1-4-1-6z" class="m"></path><path d="M504 344l5 3-1 2h-3c-1-1-3-2-5-3 1-1 2 0 3 0l1-2z" class="i"></path><path d="M480 314c0 2-1 5 1 7 1-2 0-6 0-8l1-1c1 2 0 3 1 5 1 1 1 2 1 3v1c0 1 0 1 1 2v2l1 2v2l1 2v1c0 1 1 2 1 3l1 2 2 2v1l3 3c3 5 9 6 10 12-1-2-2-4-4-5l-2 1h0l-1-1c-1-1-2-2-4-2h0c-1 1-1 0-2 0-2-2-4-4-6-7 0 0-2-1-2-2-1-2-1-7-1-9l-2 2v-18z" class="n"></path><path d="M482 330v-6c2 5 1 10 4 15 2 4 4 6 7 9h0c-1 1-1 0-2 0-2-2-4-4-6-7 0 0-2-1-2-2-1-2-1-7-1-9z" class="i"></path><path fill="#fb8c16" d="M498 289c3-5 6-8 11-10l4 1c3 0 6 2 8 4 4 7 6 15 12 22v5c-2 10-4 19-10 27-2 3-3 5-5 6s-3 1-4 2c-1 0-3 1-5 1l-5-3-1 2c-1 0-2-1-3 0l-4-4c-2-2-3-4-4-5l-3-6c-3-7-3-14-6-21l5-3h0l-1-2c0-1 3-4 4-5 2-4 4-7 6-11h1z"></path><path fill="#ff7502" d="M525 305l1 1c0 2 0 2-1 4l-1-1c0-2 0-2 1-4z"></path><path d="M491 300c2-4 4-7 6-11h1c-2 6-4 13-9 17l-1 1-1-2c0-1 3-4 4-5z" class="C"></path><path d="M493 328c2 6 5 12 11 16l-1 2c-1 0-2-1-3 0l-4-4c-1-4-5-10-3-14z" class="o"></path><path d="M489 306c0 7 2 15 4 22-2 4 2 10 3 14-2-2-3-4-4-5l-3-6c-3-7-3-14-6-21l5-3h0l1-1z" class="X"></path><path d="M483 310l5-3c-2 6-1 9 0 15l4 15-3-6c-3-7-3-14-6-21z" class="p"></path><path d="M502 170l2-1v4l1 1 1 1c0-1 1-1 2-2l1 1c-1 1-1 1-1 2-1 0-1-1-2 1l2 2c1-1 2-1 3-2 1 0 2 0 3 1l3 1 2 2 2 3 2 1c4 6 9 14 11 22 1 3 2 7 3 11 1 3 4 6 4 9v3 3 6h1c1 3 1 6 1 9v28h0v6h1c0 5-1 11-2 16-1 3-3 5-4 8 0 0 1 1 1 2 1 1 2 3 4 4v1l-1 1v5h-1c0-2 0-4-1-6h1c-1-1-1-1-2-1-1-1-1-1-2-1v-1-1l-3-3-1 5v-5c-6-7-8-15-12-22-2-2-5-4-8-4l-4-1c-5 2-8 5-11 10h-1c-2 4-4 7-6 11-1 1-4 4-4 5l-1 1c-2 0-5-2-6-3s-1-4-1-6h0c-2-10-2-20-2-30l-2-15c0-4 0-10 2-14 1-5 2-10 4-14 0-1 0-2 1-2-1-1 1-8 1-9 1-7 5-14 7-20v-1c2-7 6-13 9-19l3-3z" class="u"></path><path d="M493 262l-1-2c-1 2-6 8-8 8h0c0-2 0-2 2-3 1-3 7-8 10-10 0 1 0 2-1 3v1c0 1-1 2-2 3z" class="i"></path><path d="M496 255c1-2 4-3 6-4l1 1v3 1s-1 1-1 2c-3 1-6 4-9 6-1 0-1 0-2 1 1-1 2-2 2-3h1-1c1-1 2-2 2-3v-1c1-1 1-2 1-3z" class="X"></path><path d="M508 179c1-1 2-1 3-2l1 1v1 19c-1 7 0 14-2 21l-1-40c-1 1-3 2-5 2-1 1-3 5-4 6-1 2-2 1-3 3-2 2-3 5-5 7l-1 1c2-5 4-8 7-12 2-3 4-5 7-6l3-1zm-17 86c1-1 1-1 2-1 3-2 6-5 9-6v2l-1 1v2h0l-2 2c-1 3-4 4-6 5-2 2-4 4-6 5h0c-2 0-1-1-2-2v-1c0-1 0-1 1-2l1-1v-1c2-1 2-3 4-3z" class="i"></path><defs><linearGradient id="p" x1="484.276" y1="195.859" x2="498.724" y2="192.141" xlink:href="#B"><stop offset="0" stop-color="#2b2727"></stop><stop offset="1" stop-color="#444643"></stop></linearGradient></defs><path fill="url(#p)" d="M502 170l2-1v4l1 1 1 1c0-1 1-1 2-2l1 1c-1 1-1 1-1 2-1 0-1-1-2 1l2 2-3 1c-3 1-5 3-7 6-3 4-5 7-7 12-4 8-7 15-9 24h0c-1-1 1-8 1-9 1-7 5-14 7-20v-1c2-7 6-13 9-19l3-3z"></path><path d="M503 177c0 1 0 1 1 2l1 1c-3 1-5 3-7 6v-1c0-2 0-3 1-4 0-1 1-1 1-3 0-1 0 0 1-1h2z" class="K"></path><path d="M502 170l2-1v4l1 1 1 1c0-1 1-1 2-2l1 1c-1 1-1 1-1 2-1 0-1-1-2 1l2 2-3 1-1-1c-1-1-1-1-1-2h-2v-2-1l-1 1-1 1c0 4-4 8-6 12-1 2-2 4-3 5v-1c2-7 6-13 9-19l3-3z" class="Z"></path><path d="M502 170l2-1v4l1 1 1 1c0-1 1-1 2-2l1 1c-1 1-1 1-1 2-1 0-1-1-2 1l2 2-3 1-1-1c-1-1-1-1-1-2-1-1-1-2-1-3h-1c0-1 1-2 1-2v-2z" class="L"></path><path d="M510 219c2-7 1-14 2-21v22l1 8-2 43h-2-2 0l1-27 1-8c0-4-1-11 1-16v-1h0z" class="U"></path><path d="M512 220l1 8-2 43h-2c0-1 1-1 1-2v-1-2-3c1-8 0-16 1-24h0c-1-2 0-5 0-7 0-4 0-8 1-12z" class="n"></path><path d="M492 251c0 1-4 5-5 6v1c-1 2-2 2-3 2-1-1-1-2-1-3l-1-1v-2 1h1c-1-2-1-3 0-4v-1c1-6 5-9 8-14 1-1 1-2 2-3 1 0 1 0 2-1l4-3c1-1 2-1 2-2h1 1c1 3-1 3-2 5l1 1v2 3 2c0 1-1 1-1 3l1 1v1c-1 1-2 1-3 1l-1 1-4 3c-1 0-1 1-2 1z" class="o"></path><path d="M492 251c0-1 1-2 1-3h0l-2-2c1-1 1-1 1-2h-1-1l1-1c0-1 1-2 2-2l1-1 2-2c1-1 2-2 3-2l2-1v-1l1 1v3 2c0 1-1 1-1 3l1 1v1c-1 1-2 1-3 1l-1 1-4 3c-1 0-1 1-2 1z" class="r"></path><path d="M502 245l-1-1h-2-1-1c0 1-1 1-1 2h-3c1-1 1-1 1-2v-1c1 0 1 0 2-1s1 0 2-1h1v-1c2-1 2-1 3-2v2c0 1-1 1-1 3l1 1v1z" class="o"></path><path d="M482 222h0c0 2 0 3-1 6-1 2-1 4-1 6 1 2 0 5-1 8 0 4 0 17 3 20h1c-2 3 0 7-1 11 0 1 0 0 1 1v5s0-1 1-1c1-1 2-1 3-3h1l1 1c2-1 3-3 4-4l1-1c2 0 3-1 4-1 1-1 1-2 3-3l1 1v2h0l1 1c1-1 2 0 4 0h0c-8 4-11 7-15 14-2 5-4 10-8 14-1 1-1 2-1 3s0 1 2 2c2-1 4-3 5-5l1 1c-1 1-4 4-4 5l-1 1c-2 0-5-2-6-3s-1-4-1-6h0c-2-10-2-20-2-30l-2-15c0-4 0-10 2-14 1-5 2-10 4-14 0-1 0-2 1-2z" class="p"></path><path d="M475 252c0-4 0-10 2-14 1 2 1 3 1 6l-1 1c0 4 1 7 1 11v5c0 1 0 2-1 3v3l-2-15z" class="l"></path><path d="M478 256c2 9 1 20 2 29 0 5 1 10 1 14v3c-1-2 0-4-2-6v1h0c-2-10-2-20-2-30v-3c1-1 1-2 1-3v-5z" class="e"></path><defs><linearGradient id="q" x1="491.547" y1="287.177" x2="487.251" y2="275.072" xlink:href="#B"><stop offset="0" stop-color="#c0b09c"></stop><stop offset="1" stop-color="#e3d3bd"></stop></linearGradient></defs><path fill="url(#q)" d="M493 272l1-1c2 0 3-1 4-1 1-1 1-2 3-3l1 1v2h0l1 1c1-1 2 0 4 0h0c-8 4-11 7-15 14-2 5-4 10-8 14-1 1-1 2-1 3s0 1 2 2c2-1 4-3 5-5l1 1c-1 1-4 4-4 5l-1 1c-2 0-5-2-6-3s-1-4-1-6v-1c2 2 1 4 2 6v-3c1-1 1-6 0-8-1-4 0-9 0-12h0c1 2 1 5 1 7h1c1-2 0-5 1-7h2c1-1 2-2 2-3v-1l1 1c2-1 3-3 4-4z"></path><path d="M484 299v-1c2-5 3-11 6-16v6-1c0-1 1-1 2-2-2 5-4 10-8 14z" class="r"></path><path d="M493 272l1-1c2 0 3-1 4-1 1-1 1-2 3-3l1 1v2h0l1 1c1-1 2 0 4 0h0c-8 4-11 7-15 14-1 1-2 1-2 2v1-6c1 0 2-4 3-5l6-6c-2 1-4 1-6 1z" class="X"></path><path d="M511 177c1 0 2 0 3 1l3 1 2 2 2 3 2 1c4 6 9 14 11 22 1 3 2 7 3 11 1 3 4 6 4 9v3 3 6h1c1 3 1 6 1 9v28h0v6h1c0 5-1 11-2 16-1 3-3 5-4 8 0 0 1 1 1 2 1 1 2 3 4 4v1l-1 1v5h-1c0-2 0-4-1-6h1c-1-1-1-1-2-1-1-1-1-1-2-1v-1-1l-3-3-1 5v-5c-6-7-8-15-12-22-2-2-5-4-8-4l-4-1c-5 2-8 5-11 10h-1c-2 4-4 7-6 11l-1-1c-1 2-3 4-5 5-2-1-2-1-2-2s0-2 1-3c4-4 6-9 8-14 4-7 7-10 15-14h2 2l2-43-1-8v-22-19-1l-1-1z" class="D"></path><path d="M535 260c1 1 1 3 1 5 0 1 0 2-1 3-1-3-3-5-4-8h2 2z" class="Q"></path><path d="M542 239c1 3 1 6 1 9v28h0c0-2 1-7 0-9 0 2 0 4-1 7h0v-35z" class="N"></path><path d="M528 252c2 1 3 2 3 3 2 2 3 3 4 5h-2-2c-2-1-4-3-6-4l1-2c1 0 1-1 2-1v-1z" class="J"></path><path d="M514 244c1 0 3-1 4 0l1 1c1 0 1 1 2 1 1 1 2 1 3 2v1l2 2 2 1v1c-1 0-1 1-2 1l-1 2-1-2c-3-1-5-3-8-3v1l-1 1h-1v-6-3z" class="f"></path><path d="M524 254v-1l-4-2 2-1h2l1 1h1l2 1v1c-1 0-1 1-2 1l-1 2-1-2z" class="V"></path><path d="M525 236l3 3h1 1c1-1 2-1 2-2l2 1h0 0c1 1 2 2 3 4h0v1c1 3 1 5 2 8v1-5c-1-2 0-4 1-5l-1 27h-1l-1 1-2-2c1-1 1-2 1-3 0-2 0-4-1-5-1-2-2-3-4-5 0-1-1-2-3-3l-2-1-2-2v-1h1v-1h-1l1-2h1v-1c-1-1-2-1-3-2h0c-1-1-3-2-3-3l1-1 1 1 1 1 1-1c-1 0 0 0-1-1l2-2z" class="k"></path><path d="M533 251h1l4 4v-6c1 4 1 8 0 12h-1v-1c0-1-1-2-2-3s-2-1-2-2l1-1-2-1c1-1 1-2 1-2z" class="Q"></path><path d="M523 242h3l1 1h1c0 3 1 4 3 5 1 1 2 2 2 3 0 0 0 1-1 2l2 1-1 1c-2-2-6-6-9-6v-1h1v-1h-1l1-2h1v-1c-1-1-2-1-3-2z" class="J"></path><path d="M525 236l3 3h1 1c1-1 2-1 2-2l2 1h0l3 8c1 1 1 2 1 3v6l-4-4h-1c0-1-1-2-2-3-2-1-3-2-3-5h-1l-1-1h-3 0c-1-1-3-2-3-3l1-1 1 1 1 1 1-1c-1 0 0 0-1-1l2-2z" class="M"></path><path d="M525 236l3 3h1 1c1-1 2-1 2-2l2 1h0l3 8h-1c-2-2-3-4-5-6h-1c0 1 1 1 1 2l-1 1-1-2-1 1c1 0 1 1 1 1h-1-1l-1-1h-3 0c-1-1-3-2-3-3l1-1 1 1 1 1 1-1c-1 0 0 0-1-1l2-2z" class="h"></path><path d="M513 228c1-1-1-5 0-6l1 1c1 1 2 2 3 2 1 1 2 1 3 2 3 3 7 5 10 8l2 2c0 1-1 1-2 2h-1-1l-3-3-2 2c1 1 0 1 1 1l-1 1-1-1-1-1-1 1c0 1 2 2 3 3h0c1 1 2 1 3 2v1h-1l-1 2h1v1h-1c-1-1-2-1-3-2-1 0-1-1-2-1l-1-1c-1-1-3 0-4 0v3 6 4l-1 14h-2l2-43z" class="C"></path><path d="M514 223c1 1 2 2 3 2 1 1 2 1 3 2 3 3 7 5 10 8l2 2c0 1-1 1-2 2h-1c-2-3-6-5-9-8-2 0-4-2-5-2h0c-1-1-1-4-1-6z" class="X"></path><path d="M514 244v-15l11 7-2 2c1 1 0 1 1 1l-1 1-1-1-1-1-1 1c0 1 2 2 3 3h0c1 1 2 1 3 2v1h-1l-1 2h1v1h-1c-1-1-2-1-3-2-1 0-1-1-2-1l-1-1c-1-1-3 0-4 0z" class="a"></path><defs><linearGradient id="r" x1="528.136" y1="274.251" x2="515.405" y2="264.338" xlink:href="#B"><stop offset="0" stop-color="#69696b"></stop><stop offset="1" stop-color="#95938f"></stop></linearGradient></defs><path fill="url(#r)" d="M516 252h2c2 1 4 2 6 4l6 6c3 2 5 6 5 9 1 1 0 2 0 3l-1 1-1 1c0-1-1-1-2-2h-1c-2 0-5 0-8-1l-1 1 1 1h0c-3-1-6-3-9-4l1-14v-4h1l1-1z"></path><path d="M514 257v7 5c1 1 6 3 8 3v1l-1 1 1 1h0c-3-1-6-3-9-4l1-14z" class="b"></path><path d="M514 253h1v2c1 0 1 0 1 2l-1 1c1 0 2 0 2 1 2 0 4 2 6 3v1h0c1 2 3 4 4 5-2-1-3-2-5-3h0l2 3c-1 1-2 0-3 0l-4-1-1-1v-2h-2v-7-4z" class="h"></path><path d="M516 252h2c2 1 4 2 6 4l6 6c3 2 5 6 5 9 1 1 0 2 0 3h0c-2-1-4-4-5-5-2-3-5-5-7-6v-1c-2-1-4-3-6-3 0-1-1-1-2-1l1-1c0-2 0-2-1-2v-2l1-1z" class="U"></path><path d="M518 252c2 1 4 2 6 4l-1 1h-1-2c-1 0-2 0-3-1 0-1 0-2 1-4z" class="V"></path><path d="M530 269c0-3-5-6-7-8v-1h0c2 0 3 2 5 4v-1-1-1l2 2v-1c3 2 5 6 5 9 1 1 0 2 0 3h0c-2-1-4-4-5-5z" class="Q"></path><path d="M542 274h0c1-3 1-5 1-7 1 2 0 7 0 9v6h1c0 5-1 11-2 16-1 3-3 5-4 8 0 0 1 1 1 2 1 1 2 3 4 4v1l-1 1v5h-1c0-2 0-4-1-6h1c-1-1-1-1-2-1-1-1-1-1-2-1v-1-1l-3-3-1 5v-5c-6-7-8-15-12-22-2-2-5-4-8-4l-4-1c-5 2-8 5-11 10h-1c-2 4-4 7-6 11l-1-1c-1 2-3 4-5 5-2-1-2-1-2-2s0-2 1-3c4-4 6-9 8-14 4-7 7-10 15-14h2 2 2c3 1 6 3 9 4 2 3 5 6 7 9 3 6 4 13 10 18 3-9 3-18 3-28z" class="S"></path><defs><linearGradient id="s" x1="510.999" y1="274.822" x2="513.53" y2="303.193" xlink:href="#B"><stop offset="0" stop-color="#2b2d30"></stop><stop offset="1" stop-color="#5e5c58"></stop></linearGradient></defs><path fill="url(#s)" d="M490 299l3-6c2-6 5-12 11-16l5-3c10 2 15 8 19 17 2 3 3 7 5 9 1 2 4 3 5 5 1-2 2-5 3-7 2-5 2-10 2-16h1c0 5-1 11-2 16-1 3-3 5-4 8 0 0 1 1 1 2 1 1 2 3 4 4v1l-1 1v5h-1c0-2 0-4-1-6h1c-1-1-1-1-2-1-1-1-1-1-2-1v-1-1l-3-3-1 5v-5c-6-7-8-15-12-22-2-2-5-4-8-4l-4-1c-5 2-8 5-11 10h-1c-2 4-4 7-6 11l-1-1z"></path><defs><linearGradient id="t" x1="537.543" y1="227.119" x2="527.938" y2="231.067" xlink:href="#B"><stop offset="0" stop-color="#595a5c"></stop><stop offset="1" stop-color="#8c8a85"></stop></linearGradient></defs><path fill="url(#t)" d="M511 177c1 0 2 0 3 1l3 1 2 2 2 3 2 1c4 6 9 14 11 22 1 3 2 7 3 11 1 3 4 6 4 9v3 3 6c0 1-1 1-1 3-1 1-2 3-1 5v5-1c-1-3-1-5-2-8v-1h0c-1-2-2-3-3-4h0 0l-2-1-2-2c-3-3-7-5-10-8-1-1-2-1-3-2-1 0-2-1-3-2l-1-1c-1 1 1 5 0 6l-1-8v-22-19-1l-1-1z"></path><path d="M517 179l2 2 2 3-2 2 1 7v1c1 2 1 4 2 6l1 4c0 1 0 2 1 3h0c1 3 1 6 2 8l1 2v1 1c1 1 1 1 1 2h0c-2-1-2-4-3-7 0 3 0 5 1 7l-1-1c0-1 0-1-1-2s-1-2-2-3l-1-1c0-1-1-3-2-4l-1-2c0-6-2-11-2-16 1-4 0-9 1-13z" class="M"></path><path d="M519 181l2 3-2 2 1 7v1c1 2 1 4 2 6l1 4c0 1 0 2 1 3h0c1 3 1 6 2 8l1 2v1 1c1 1 1 1 1 2h0c-2-1-2-4-3-7h0c-1-1-1-3-1-4-1-1-1-2-2-3-1-3-2-7-3-10 0-2 1-3 0-5v-2-1c0-1-1-2-1-3l1-1v-4z" class="Q"></path><defs><linearGradient id="u" x1="531.621" y1="201.804" x2="526.036" y2="203.444" xlink:href="#B"><stop offset="0" stop-color="#404243"></stop><stop offset="1" stop-color="#626362"></stop></linearGradient></defs><path fill="url(#u)" d="M521 184l2 1c4 6 9 14 11 22 1 3 2 7 3 11 1 3 4 6 4 9v3 3l-3-9-1-3c-3-2-3-8-7-11-1-1-2-3-3-4v-1-5-1c0-1-1-3-2-4v5 2c-1 1-1 2-1 4v1h0 0c-1-1-1-2-1-3l-1-4c-1-2-1-4-2-6v-1l-1-7 2-2z"></path><path d="M521 184l2 1-2 1c0 3 0 4 1 7v1c1 2 1 5 1 7-1-3-1-5-3-7v-1l-1-7 2-2z" class="P"></path><path d="M511 177c1 0 2 0 3 1l3 1c-1 4 0 9-1 13 0 5 2 10 2 16l1 2c1 1 2 3 2 4 0 4 4 7 5 11v1 2l1 1h-1-1c2 2 4 4 6 5v1h-1c-3-3-7-5-10-8-1-1-2-1-3-2-1 0-2-1-3-2l-1-1c-1 1 1 5 0 6l-1-8v-22-19-1l-1-1z" class="J"></path><path d="M514 202c0 2-1 4 1 5v-2c3 3 2 6 4 10l1 2c1 1 2 3 3 5h-1v2l1 1h0c1 1 2 2 2 3-4-3-8-5-11-8-1-2 0-4 0-6v-12z" class="f"></path><path d="M514 202c0 2-1 4 1 5v-2c3 3 2 6 4 10l1 2c1 1 2 3 3 5h-1v2l1 1h0c-1 0-1-1-2-1v-3h-1l1-1h0c-1-2-3-4-3-5-1-1-1-1-1-2-2-2-2-4-3-6v13c-1-2 0-4 0-6v-12z" class="V"></path><path d="M511 177c1 0 2 0 3 1-1 2 0 5-1 7 0 6 0 12 1 17v12c0 2-1 4 0 6 3 3 7 5 11 8v1c2 2 4 4 6 5v1h-1c-3-3-7-5-10-8-1-1-2-1-3-2-1 0-2-1-3-2l-1-1c-1 1 1 5 0 6l-1-8v-22-19-1l-1-1z" class="M"></path><path d="M533 633h0c2 3 1 8 1 11l1 11-1 15 2 34c0 1 1 2 1 4h0c2 6 0 16 0 22 0 4 0 9 1 13v-2l1 1h0c1-4 1-8 1-11 0-2 0-3 1-4v-12-1c0 4 1 7 1 10l1 1c0 2 0 5-1 8v10c0 1 1 2 1 3v25c0 5-1 11 0 16 1 3 3 5 5 7h1 0l2-2h2v5c0 2-1 3-2 4v5c1 3 1 21-1 23h0l-1 1v6c0 2 0 3-2 5 0 1-1 2-2 3-4 5-8 11-12 17l-1 3-2 3v-3h1v-1-1h-1l1-1 1-1-1-1v1l-1 1-1-1-1-3v4h1l-1 1h0v2c-1 0-1 1-1 2-1 1-2 1-2 2v1c-2 5-2 10-5 14v1c-2 3 0 5-1 7 0 1-1 1-1 1-1-1-1 0-1-2l-2 2v1h-1c-1-1 0-1-1-1h-1c-2-1 0-2-2-4v1c1 0 1 1 1 2-1 0-1 1-2 1-1 1-1 0-2 0-1-2-1-5-1-7l-1-1-1-1s0-2-1-2c0-1-1-1-2-2-1-2-1-5-2-7-1 0-3-1-3-2l-1-5h-2c-1-1-2-1-2-2h-1c-2-1-3-3-4-5l-6-10-3-3-1-1c0-3 3-5 4-7l2 1c-1 1-1 1-1 2l2 2 1-3c1 0 0-3 0-4 0-4 0-9 1-13v-43-4-13-4c0-1 1-1 1-1h1v1c0-1 2-2 2-3 1-2 0-6 1-8v-1l-1-23c0-4 0-11 2-14l-1-1v-1c2-4 1-8 3-12h0l1-1c0-2 0-9-1-11l-1-1h1 2c1 0 2 0 3-1v-1c1-2 1-4 3-6 1 0 1-1 2-1l1-1v2c1 2 1 5 2 7 4 0 8 0 12-1h0c1-2 1-3 1-5v-3-7-11-10c1-3-1-7 1-10l13-1z" class="d"></path><path d="M524 770h2l1 1v1l-2 1c-1-1-1-1-1-3z" class="R"></path><path d="M524 779c1 0 2-1 3 0v2l-2 1c-1-1-1-2-1-3z" class="F"></path><path d="M495 814l3 3c0 1 0 2-1 3 0 0-1-1-1-2-1-1-1-3-1-4z" class="I"></path><path d="M477 845l2-1s1 0 2 1c-1 1-1 2-1 3l-3-3z" class="W"></path><path d="M519 783v-6l1-1c0 3 1 7 0 9h-1v-1-1z" class="E"></path><path d="M498 811v6l-3-3 1-3h1l1 1v-1z" class="B"></path><path d="M518 727v1c1 3 0 6 0 10h-1c0-4 0-7 1-11z" class="C"></path><path d="M516 817v-4c1 0 2 0 2 1 1 1 1 2 1 4h0l-3-1z" class="O"></path><path d="M519 783l-1-2c0-2 0-4 1-5l-1-2c0-1-1-3 0-3 0-1 0-1 1-1l-1-1c0-1 1-3 1-3 0 1-1 3 0 4 1 2 1 4 1 6l-1 1v6z" class="I"></path><path d="M527 781v3l-6 1 1-6h2c0 1 0 2 1 3l2-1z" class="C"></path><path d="M509 797c2 1 3 1 5 1h1 1 0v1c-1 1-1 2-2 3h-5v-2-3z" class="H"></path><path d="M493 759c2 0 3 0 5-1h2v6l-1 1c-1-2-1-3-2-3l-4 1v-4z" class="L"></path><path d="M533 709s2 1 2 2v8 4l1 1c0 2-1 3-2 5l-1-17v-3z" class="I"></path><path d="M500 798v1 1 3l-1 1c0 2 0 3-1 5v2 1l-1-1h-1c0-2 0-2 1-3 0-3 0-6 1-9l2-1z" class="B"></path><path d="M497 808h0c2-2 1-4 1-6l1-1v3c0 2 0 3-1 5v2 1l-1-1h-1c0-2 0-2 1-3z" class="I"></path><path d="M490 785l2 3 1-4-1 16c0 2 0 7-1 9 0-8 0-16-1-24z" class="r"></path><path d="M533 861l1-3s-1-1-1-2c2-1 3-2 3-3 1-1 0-1 1-2l1 1 1-1-1-1c3-2 2-4 3-6h2 2c-4 5-8 11-12 17z" class="L"></path><path d="M500 798l9-1v3 2h-6l-1-1-2 2v-3-1-1z" class="I"></path><path d="M509 802h5 5c1 3 0 6 1 9-2 1-3 1-4 1h-2c0-2 1-6 2-8h2 0v-1h-11c-1 0-3 0-4-1h6z" class="K"></path><path d="M515 716l10-1c1 0 2 1 3 2l-1 3h-5l1 1c-1 1-3 0-5 0 0 0 0-1-1-1s0 0-1-1l-1-2v-1z" class="R"></path><path d="M515 717c2 1 4 0 5 2l1 1h1l1 1c-1 1-3 0-5 0 0 0 0-1-1-1s0 0-1-1l-1-2z" class="W"></path><path d="M536 828h1v14 6 1l-1 1h0l-1-1c-2 0-2 1-3 2l-2-1c0-1 1-3 1-4 1-1 1-2 2-2l1 1 1-1 1-16zm-29-44c-1 1 0 1-1 1-2-4-1-12-1-17 0-2 0-5 1-8l1 1h1v16c0 2-1 4 0 7h-1z" class="B"></path><path d="M507 761h1v16c0 2-1 4 0 7h-1c-1-4 0-11 0-16 0-2-1-5 0-7z" class="E"></path><path d="M517 743c3 0 8-1 10 0 1 1 1 3 1 4h-3l-1 1h-1-6c-2-2-4 0-6-1v-2l1-1v-1h4 1z" class="R"></path><path d="M504 716h11v1l1 2c1 1 0 1 1 1s1 1 1 1h-20-1v-1h2c1 0 3 0 5-1v-3zm3 87h11v1h0-2c-1 2-2 6-2 8h-1 0-4v2c0 1-1 2-1 3-2 0-2-1-3-2l1-2 1 1c1-2 1-2 1-3v-4c-1-1-1-2-1-3v-1z" class="B"></path><path d="M508 811c1-2 2-4 3-5l1 1v1c-1 1-1 2-1 2h2v2h-4v2c0 1-1 2-1 3-2 0-2-1-3-2l1-2 1 1c1-2 1-2 1-3z" class="c"></path><path d="M494 751c2 0 6-1 8 0 3 1 6 1 9 1v1c-2 0-4 0-5-1h-3c1 1 4 1 6 1-1 1 0 1-1 1-2 2-2 3-4 4h0-3-1-2c-2 1-3 1-5 1v-7l1-1z" class="R"></path><path d="M494 698c1 5 0 11 0 16h3c2 0 3-1 5-1v1h1v-1-2h5 3 3v2h3c1-1 0-1 2 0l2-1s1 1 2 1c-1 0-1 0-2 1h4c1-1 1-1 3-1v1l-3 1-10 1h-11l-10-1v5l-1-5 1-17z" class="Z"></path><path d="M511 711h3v2h-11v-2h5 3z" class="C"></path><path d="M487 810h0c0 3-1 10 0 12s0 2 2 3h1v1 1c-2 2-1 5-2 8 0 2 1 3 0 4s-1 1-1 2l1 1h1v1c0 2 0 4 1 5v1 1l-2-2v2c-1 0-2-1-2-1v2h-1c-1-1-1-1-1-3v-9c1 0 0-3 0-4 0-4 0-9 1-13 0 2-1 6 0 8l1-1v-3c0-5-1-12 1-16z" class="B"></path><defs><linearGradient id="v" x1="482.973" y1="763.059" x2="499.027" y2="769.941" xlink:href="#B"><stop offset="0" stop-color="#dcdfd7"></stop><stop offset="1" stop-color="#faf3f7"></stop></linearGradient></defs><path fill="url(#v)" d="M490 749c0-1 1-2 1-3h0v1 1c1-1 1-2 1-4h1c0 2-1 6 0 8v7 4 21l-1 4-2-3v-2-10-24z"></path><path d="M523 817l1-1v-5h-1 0c-1 1-2 1-3 1l1-1c0-2 0-3 1-4 1 1 1 1 1 2l2 1 1 25c0 2 1 6 0 8h-1l-1-1c-2-1-2-1-4 0v-2c1 0 1 0 2-1l-1-2c-1-4 1-9-1-12-1-2 0-6 0-8h3z" class="E"></path><path d="M524 835h1v2c1 1 0 2-1 3l-1-1c0-2 0-2 1-4z" class="B"></path><path d="M520 817h3v2 1c-1 1-1 3-1 4-2 3 0 9-1 13-1-4 1-9-1-12-1-2 0-6 0-8z" class="G"></path><defs><linearGradient id="w" x1="496.213" y1="806.935" x2="479.707" y2="799.748" xlink:href="#B"><stop offset="0" stop-color="#020501"></stop><stop offset="1" stop-color="#322d30"></stop></linearGradient></defs><path fill="url(#w)" d="M487 778h0l1 7h1v-2h1v2c1 8 1 16 1 24-1 1-1 2-1 4v1c0 2 1 9-1 11-2-1-1-1-2-3s0-9 0-12h0v-32z"></path><path d="M533 781h2c1 1 1 28 1 32 0 2-1 4 0 6l1 1v8h-1c0-2-1-5-1-8 0-5 1-12 0-17h0c0-1 0-2-1-3-1 4 1 11-1 14l-2-1-1-1v-19l1 1v-2h1c0-4 1-7 1-11z" class="C"></path><path d="M514 727h0v1 4 1c-1 2-1 3 0 4l3 3h0c-1 2-3 1-4 1h-3c-2 1-4 0-5 0-1-1-4 0-5 0-2-1-3 0-4-1v-1h2c0-4-1-8 1-11h0l1 2c0 2-1 7 0 8l1-2v1h1c0-2-1-7 0-9h2c0 1 0 1 1 2 0-1 0-1 1-2v1 8l1 2h1c0-1 2-2 2-2v-9c2-1 1 1 3 1l1-2z" class="I"></path><path d="M509 759h1v-4h1c0 2 0 4 1 5 1 0 1 0 1-1v1h0l1 1-1 1 1 7h2l-2 2c-1 1 1 10 0 13 0 0-1 1-1 2h-4l-1-2c-1-3 0-5 0-7v-16c0-1 1-1 1-2z" class="D"></path><path d="M509 759h1v-4h1c0 2 0 4 1 5 1 0 1 0 1-1v1h0l1 1-1 1c-1 3 0 6 0 9 0 0-1 1-1 2v7c-1-3 0-6 0-9s-1-6-1-9v-1l-2-2z" class="c"></path><path d="M512 780v-7c0-1 1-2 1-2 0-3-1-6 0-9l1 7h2l-2 2c-1 1 1 10 0 13 0 0-1 1-1 2h-4l-1-2c-1-3 0-5 0-7 0 2-1 5 1 6h0c1 1 2 1 3 1 1-1 0-2 0-4z" class="G"></path><path d="M490 708v-1c2-4 1-8 3-12h0l1 3-1 17 1 5v31l-1 1c-1-2 0-6 0-8h-1c0 2 0 3-1 4v-1-1h0c0 1-1 2-1 3v-2h0v-1l-1-23c0-4 0-11 2-14l-1-1z" class="f"></path><path d="M493 715l1 5v31l-1 1c-1-2 0-6 0-8h-1c0 2 0 3-1 4v-1-1h0c0 1-1 2-1 3v-2c1-4 0-8 0-11 1-1 1-2 1-3v-1c0-1 1-2 1-4 0-4-1-8 0-12l1 4h0v-5z" class="r"></path><path d="M533 747c2 0 3-1 4 1v4 68l-1-1c-1-2 0-4 0-6 0-4 0-31-1-32h-2v-8-20-6z" class="D"></path><path d="M533 747c2 0 3-1 4 1v4h0-1c-1 1 0 8 0 9-1 0-1-5-1-6s-1-1-2-2v-6z" class="G"></path><path d="M490 747h0v2 24 10h-1v2h-1l-1-7h0v32c-2 4-1 11-1 16v3l-1 1c-1-2 0-6 0-8v-43-4-13-4c0-1 1-1 1-1h1v1c0-1 2-2 2-3 1-2 0-6 1-8z" class="K"></path><path d="M487 778h0c0-1-1-2-1-3v-2c1-2 1-6 1-8v-4c1 2 1 5 1 8-1 2 0 2 0 4 1-1 1-3 1-4v-1c0 2 1 4 0 7h-1v1h1c1 1 1 1 0 2h-1-1 0z" class="Y"></path><path d="M520 842c2-1 2-1 4 0l1 1h1v1h-1c-1-1-2 0-3 0 0 1 0 1 1 2l3-1c1 2 0 13 0 16 0 1 0 2-1 3 0 2 0 3-1 5h1c-2 5-2 10-5 14v-1l-1 2-1 1v-4c0-1-1-2-1-4h-2l1-2v-1l1-5v4c2-2 2-2 2-4v1h-1c4-7 1-19 2-28h0z" class="O"></path><path d="M523 846l3-1c1 2 0 13 0 16 0 1 0 2-1 3s-2 2-2 3c-1 2-2 6-4 7h-1l1-2c2-1 3-4 3-6 1-2 1-5 2-8 0-4-1-8 1-11 0 0-1-1-2-1z" class="F"></path><path d="M518 874h1c2-1 3-5 4-7 0-1 1-2 2-3 0 2 0 3-1 5h1c-2 5-2 10-5 14v-1l-1 2-1 1v-4c0-1-1-2-1-4h-2l1-2v-1h2z" class="L"></path><path d="M518 874h1c2-1 3-5 4-7 0-1 1-2 2-3 0 2 0 3-1 5 0 1-1 0-1 2 0 0-1 1-1 2-1 2-1 3-2 5h0l-4-3v-1h2z" class="G"></path><defs><linearGradient id="x" x1="523.866" y1="752.882" x2="537.634" y2="755.618" xlink:href="#B"><stop offset="0" stop-color="#ccd2c8"></stop><stop offset="1" stop-color="#e9dfdf"></stop></linearGradient></defs><path fill="url(#x)" d="M533 712l1 17-1 18v6 20 8c0 4-1 7-1 11h-1v2l-1-1v-77h1v3h1c0-2 0-5 1-7h0z"></path><path d="M500 803l2-2 1 1c1 1 3 1 4 1v1c0 1 0 2 1 3v4c0 1 0 1-1 3l-1-1-1 2-1 2h0c-1 2-1 8-1 10v6c0 1 1 2 1 2 0 3 0 6 1 9v2c-1-1-1-2-1-2l-2 3h0-1c0-2-1-4-1-6l1-1c-1-1-1-1-1-2v-1c-2 1-4 1-6 1h-1c2-1 3 0 5-1 0-1 0-2-1-3h0c-1-1-1-2-2-2l-1-1c1 0 1-1 2-1v-1l1 1v1l1-1v-4h-2v-1h2v-2h-1v1h-1v-1-1l1-2c1-1 1-2 1-3v-6-2c1-2 1-3 1-5l1-1z" class="G"></path><path d="M503 817c0-1 0-2-1-3h0c-2-1-2-1-2-2l1-1 1 1 1-2c1 1 2 1 3 3l-1 2-1 2h-1z" class="R"></path><path d="M503 810v-3l1-1 1-2h2c0 1 0 2 1 3v4c0 1 0 1-1 3l-1-1c-1-2-2-2-3-3z" class="Z"></path><path d="M502 847v-26-2c0-1-1-1 0-2h1 1 0c-1 2-1 8-1 10v6c0 1 1 2 1 2 0 3 0 6 1 9v2c-1-1-1-2-1-2l-2 3h0z" class="k"></path><path d="M543 787c1 3 3 5 5 7h1 0l2-2h2v5c0 2-1 3-2 4v5c1 3 1 21-1 23h0l-1 1v6c0 2 0 3-2 5l-4-3c0-1-2-2-2-3-1-2 0-6 0-8v-23c0-3 0-6 1-8v-6h0c0-1 1-2 1-3z" class="N"></path><path d="M549 836c-1-3-1-7-2-10 0-2-2-4-3-7h1c2 2 4 7 5 10l-1 1v6z" class="H"></path><path d="M543 787c1 3 3 5 5 7h1c-1 2-1 4-1 6l-1-2-2-2c0-1-1-1-1-2-1-1-1-2-2-4h0c0-1 1-2 1-3z" class="L"></path><path d="M551 792h2v5c0 2-1 3-2 4l-3-1c0-2 0-4 1-6h0l2-2z" class="d"></path><path d="M505 815c1 1 1 2 3 2v1c3 1 4 1 7 0l1-1 3 1h0l1-1c0 2-1 6 0 8 2 3 0 8 1 12l1 2c-1 1-1 1-2 1v2h0c-1 9 2 21-2 28h1v-1c0 2 0 2-2 4v-4c-1-1 0-3 0-4 0-3 0-6-1-9-1-4-2-8-5-10h-2c-1 1-2 3-3 4h0l-1-1c-1-1-1-1 0-3v-2c-1-3-1-6-1-9 0 0-1-1-1-2v-6c0-2 0-8 1-10h0l1-2z" class="K"></path><path d="M515 818l1-1 3 1v3c0 1-1 1-1 1h-1v-4h-2z" class="D"></path><path d="M519 818l1-1c0 2-1 6 0 8v7l-1-11v-3h0z" class="N"></path><path d="M520 825c2 3 0 8 1 12l1 2c-1 1-1 1-2 1-1-2-1-5 0-8v-7z" class="F"></path><path d="M504 817l2 1v1 6c0 5-1 10-1 15 0 2 0 3 1 5v5l-1-1c-1-1-1-1 0-3v-2c-1-3-1-6-1-9 0 0-1-1-1-2v-6c0-2 0-8 1-10z" class="b"></path><path d="M493 838h1c2 0 4 0 6-1v1c0 1 0 1 1 2l-1 1c0 2 1 4 1 6h1 0l2-3s0 1 1 2c-1 2-1 2 0 3l1 1h0c1-1 2-3 3-4h2c3 2 4 6 5 10 1 3 1 6 1 9 0 1-1 3 0 4l-1 5v1l-1 2h2c0 2 1 3 1 4v4l1-1 1-2v1 1c-2 3 0 5-1 7 0 1-1 1-1 1-1-1-1 0-1-2l-2 2v1h-1c-1-1 0-1-1-1h-1c-2-1 0-2-2-4v1c1 0 1 1 1 2-1 0-1 1-2 1-1 1-1 0-2 0-1-2-1-5-1-7l-1-1-1-1s0-2-1-2c0-1-1-1-2-2-1-2-1-5-2-7-1 0-3-1-3-2l-1-5v-14l-1-6c2-1 2-2 3-4h-4v-3z" class="c"></path><path d="M502 847l2-3s0 1 1 2c-1 2-1 2 0 3l1 1h0c-2 7-1 11-1 17h-1v1l-1-1-1-20z" class="P"></path><path d="M515 877h2c0 2 1 3 1 4-1 0-2-1-3-1v-2h-1l-1 1c0 1 0 2 1 4-1 1-2 1-3 2 0 1 0 1-1 1l-2-1v-8h7z" class="R"></path><path d="M514 883c-1-2-1-3-1-4l1-1h1v2c1 0 2 1 3 1v4l1-1 1-2v1 1c-2 3 0 5-1 7 0 1-1 1-1 1-1-1-1 0-1-2l-2 2v1h-1v-1c0-3 1-7 0-9z" class="H"></path><path d="M493 838h1c2 0 4 0 6-1v1c0 1 0 1 1 2l-1 1c0 2 1 4 1 6v1c-1 5 1 10-2 15 0 2 0 5 1 7 0 2 2 3 2 5s0 3 1 5h1v-3 1 1c1 0 1-1 2-1l1 3v1 10c-1-2-1-5-1-7l-1-1-1-1s0-2-1-2c0-1-1-1-2-2-1-2-1-5-2-7-1 0-3-1-3-2l-1-5v-14l-1-6c2-1 2-2 3-4h-4v-3z" class="C"></path><path d="M493 838h1c2 0 4 0 6-1v1c0 1 0 1 1 2l-1 1c0 2 1 4 1 6v1c-1 5 1 10-2 15 0-2 0-4-1-6-1-3 1-7-1-10h-1c0 4 1 8-1 12v-7-1l-1-6c2-1 2-2 3-4h-4v-3z" class="D"></path><path d="M497 841l2 2v3h-1-1c-1-1-2-1-3-1 2-1 2-2 3-4z" class="W"></path><path fill="#fb8c16" d="M506 850c1-1 2-3 3-4h2c3 2 4 6 5 10 1 3 1 6 1 9 0 1-1 3 0 4l-1 5v1l-1 2h-7-1c-2-1-2-8-2-10 0-6-1-10 1-17z"></path><path d="M533 633h0c2 3 1 8 1 11l1 11-1 15 2 34c0 1 1 2 1 4-2-1-3-1-4 0v1 3h0c-1 2-1 5-1 7h-1v-3h-1l-1-1-1-1v-1c-2 0-2 0-3 1h-4c1-1 1-1 2-1-1 0-2-1-2-1l-2 1c-2-1-1-1-2 0h-3v-2h-3-3-5v2 1h-1v-1c-2 0-3 1-5 1h-3c0-5 1-11 0-16l-1-3 1-1c0-2 0-9-1-11l-1-1h1 2c1 0 2 0 3-1v-1c1-2 1-4 3-6 1 0 1-1 2-1l1-1v2c1 2 1 5 2 7 4 0 8 0 12-1h0c1-2 1-3 1-5v-3-7-11-10c1-3-1-7 1-10l13-1z" class="H"></path><path d="M532 668h1c0 2 0 3-1 4-3 0-5-1-7-2-1 0-2 0-2-1v-1h9 0z" class="B"></path><path d="M519 675l1-1h1v1c0 1 1 2 1 3l1 3c1 0 1 1 2 2h-4c-1 0-2 0-3 1s-1 1-1 2h-2l-1-1c1-1 1-1 3-1l1-1c1-1 1-1 2-1l-2-2c1-2 1-3 1-5z" class="O"></path><path d="M523 681c1 0 1 1 2 2h-4c1 0 1 0 0-1h0c1-1 2-1 2-1z" class="F"></path><path d="M530 654l1 1v2 1l-2-1v1c0 1-1 3 0 3h1 3v3h-1c-3 0-7 0-9-2 0-1 1-2 2-3h0v-2c-1-1-2-1-2-2l1-1 1 1c1-1 3 0 4-1h1z" class="B"></path><path d="M531 688v-3h-1-1 0v-2h3c0-2 0-2-1-3l-2-1h0c-1-1-2-1-3-1l-1-1c1-1 2-1 3-1v1c2 0 4-1 6 1 0 1 0 2-1 4s0 4-1 6c0 2 0 3 1 4v7l1 4v2h1c-1-1-1-2 0-3v3l1-1c0 1 1 2 1 4-2-1-3-1-4 0l-1-6c0-4 0-11-1-14z" class="E"></path><path d="M531 688c1 3 1 10 1 14l1 6v1 3h0c-1 2-1 5-1 7h-1v-3h-1l-1-1-1-1v-1c-2 0-2 0-3 1h-4c1-1 1-1 2-1-1 0-2-1-2-1 2 0 4 1 6-1v-11c0-2 1-3 2-4l2-8z" class="B"></path><path d="M531 688c1 3 1 10 1 14h-1v2h-1v-1c-1 2-1 6-1 9-1-5 0-11 0-16l2-8z" class="Z"></path><path d="M529 712c0-3 0-7 1-9v1h1v-2h1l1 6v1 3h0c-1 2-1 5-1 7h-1v-3h-1l-1-1v-3z" class="J"></path><defs><linearGradient id="y" x1="522.067" y1="632.926" x2="536.844" y2="657.031" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242424"></stop></linearGradient></defs><path fill="url(#y)" d="M533 633h0c2 3 1 8 1 11l1 11-1 15v-5-9c-1 1-1 3-1 4v1h-3-1c-1 0 0-2 0-3v-1l2 1v-1-2l-1-1c1-1 1-1 1-2v-1h-2c-1 0-3 0-4-1h-1c-1 0-1 0-2-1-1 0-1-2-1-3l-1 1v7h-1v-10c1-3-1-7 1-10l13-1z"></path><path d="M521 646c3-1 8-1 10 0v2 1 2h-2c-1 0-3 0-4-1h-1c-1 0-1 0-2-1-1 0-1-2-1-3z" class="E"></path><path d="M525 683l1 2h-1c-1 4 0 10 0 14v4 1l-1 8c-1-2-1-5-1-6h-1v2c-1 0-1 0-1-1l-1 3h-2c0 1 0 2 1 3-2-1-1-1-2 0h-3v-2h-3-3v-7-2c0-1 0 0-1-1h-1 0c1-1 1-2 2-3v1h3c1 0 2 0 3-1h0l-1-1c1-1 1-2 1-3v-2-4c0-1 0-1 1-2h2c0-1 0-1 1-2s2-1 3-1h4z" class="G"></path><path d="M525 703l-2-1h0v-2c0-1 0-3 1-3v1l1 1v4z" class="B"></path><path d="M518 710v-2c1-1 1-2 2-2l1 1-1 3h-2z" class="d"></path><path d="M511 699c1 0 2 0 3-1-1 1-1 3-1 3 0 2 1 1 1 3-1 2 0 4-1 6l2 1h-1-3l1-1v-3-4c0-1-1-2-2-2s0-1-1-1l2-1z" class="B"></path><path d="M508 698v1h3l-2 1c1 0 0 1 1 1s2 1 2 2v4 3l-1 1h-3v-7-2c0-1 0 0-1-1h-1 0c1-1 1-2 2-3z" class="N"></path><path d="M504 672v2c1 2 1 5 2 7 4 0 8 0 12-1h0l2 2c-1 0-1 0-2 1l-1 1c-2 0-2 0-3 1l1 1c-1 1-1 1-1 2v4 2c0 1 0 2-1 3l1 1h0c-1 1-2 1-3 1h-3v-1c-1 1-1 2-2 3h0 1c1 1 1 0 1 1v2 7h-5v2 1h-1v-1c-2 0-3 1-5 1h-3c0-5 1-11 0-16l-1-3 1-1c0-2 0-9-1-11l-1-1h1 2c1 0 2 0 3-1v-1c1-2 1-4 3-6 1 0 1-1 2-1l1-1z" class="G"></path><path d="M497 683h1c1 1 0 3 1 4 0 0 2 1 3 1h0c0 2 0 5-1 7h-1c-3-2-1-7-3-10h-1l1-2z" class="D"></path><path d="M500 710h2v-2c0-3-1-7 0-10l1 1v11c1 0 2 0 3-1 1-2 0-5 0-8h1c1 1 1 0 1 1v2 7h-5v2 1h-1v-1c-2 0-3 1-5 1 1-2 2-2 4-3l-1-1z" class="E"></path><path d="M504 672v2 3c-1 1-1 1-1 2l-1 1 1-1v2 4c-1 1-1 2-1 3-1 0-3-1-3-1-1-1 0-3-1-4h-1-1l-1-1c1 0 2 0 3-1v-1c1-2 1-4 3-6 1 0 1-1 2-1l1-1z" class="b"></path><path d="M502 680l1-1v2 4c-1 1-1 2-1 3-1 0-3-1-3-1-1-1 0-3-1-4h-1-1l-1-1c1 0 2 0 3-1 1 1 1 3 2 4l2-1v-4h0z" class="k"></path><path d="M493 683l-1-1h1 2l1 1c-1 2-1 5 0 8 0 5 1 9 1 13v5h-2v1h5l1 1c-2 1-3 1-4 3h-3c0-5 1-11 0-16l-1-3 1-1c0-2 0-9-1-11z" class="I"></path><defs><linearGradient id="z" x1="507.732" y1="695.231" x2="503.898" y2="685.956" xlink:href="#B"><stop offset="0" stop-color="#535353"></stop><stop offset="1" stop-color="#6a6966"></stop></linearGradient></defs><path fill="url(#z)" d="M502 680l1-1c0-1 0-1 1-2v-3c1 2 1 5 2 7 4 0 8 0 12-1h0l2 2c-1 0-1 0-2 1l-1 1c-2 0-2 0-3 1l1 1c-1 1-1 1-1 2v4 2c0 1 0 2-1 3l1 1h0c-1 1-2 1-3 1h-3v-1-1c-1 1-1 1-2 1v1l-1-1h-2v-2-11-4-2l-1 1z"></path><path d="M502 680l1-1c0-1 0-1 1-2v-3c1 2 1 5 2 7h0-1v3l-1 1c-1-1 0-3-1-4v-2l-1 1z" class="P"></path><path d="M503 696c2 0 4 0 6-1 0 1 0 0 1 1l1 1v1h-1c-1-1 0-1-1 0l-1 1v-1-1c-1 1-1 1-2 1v1l-1-1h-2v-2z" class="O"></path><path d="M514 694l-3 1h0l-1-2c1-1 1-3 1-4h1l1 1h0l1-2v4 2z" class="c"></path><path d="M518 680h0l2 2c-1 0-1 0-2 1l-1 1c-2 0-2 0-3 1l-1-1v1c-1 1-2 1-3 1 0-1 1-1 1-2-1 0-1-1-1-2 2 0 6 1 7 0s1-1 1-2z" class="Y"></path><path d="M482 383v-2h0c0 1 0 3 1 4v4h2v-1-2c-1-1-1-2-1-3s0-2 1-3v-1 3c0 2 1 4 2 6v1c1 5 3 10 4 15 2 6 4 9 7 14 1 1 2 3 3 3 3 2 5 4 8 5h0c3 1 4 1 6-1l3-2 1-1h-1 0c-1-1-1-2-1-3 1-1 1-2 2-3 2-1 4-3 4-5 1-2 3-3 4-6 0-1 1-1 2-2 0-1 1-2 2-3v3l4-13v-1h2v4l1 1 2 3v1l1 1 2 2c-1 0-1 1-1 1l2 2c0 1 0 3-1 4h1l1-1 1 1 2 1c2 2 2 4 2 6-1 5 0 10 0 15v3l-1-1h-2 0l-4-1v7l-1 1c-1 2-1 2-3 3l-3-3v1 2c1 3 1 7 0 10l-3-2c-3 3-4 22-5 26 0 2 0 6-1 7v-1l-1 3h0c1 1 0 3 0 5v1 1h-1c0 1 0 3-1 5 0 1 1 1 0 2v2l-1 1c0 1 1 1 0 2v2 2 15l-1 1h-1-3-6-9c-2 0-4 0-6 1h-2 0c-2 0-2 1-4 2 1 1 1 1 1 2 1 1 1 4 1 5v2h2v-2c1 1 1 1 2 1 0 0 1 0 2-1 2-1 5 0 7 0v-4l1 3v-1c2 0 4 0 6-1 1 2 2 3 3 4h1c0 3 0 7-1 9l1 17 1 1c-1 1-1 1-1 2l1 12c0 1 0 2-1 3l1 2c1 4 1 9 0 13v25 4l1 21v10 11 7 3c0 2 0 3-1 5h0c-4 1-8 1-12 1-1-2-1-5-2-7v-2l-1 1c-1 0-1 1-2 1-2 2-2 4-3 6v1c-1 1-2 1-3 1h-2-1l1 1c1 2 1 9 1 11l-1 1h0c-2 4-1 8-3 12v1l1 1c-2 3-2 10-2 14l1 23v1c-1 2 0 6-1 8 0 1-2 2-2 3v-1h-1s-1 0-1 1v4 13 4 43c-1 4-1 9-1 13 0 1 1 4 0 4l-1 3-2-2c0-1 0-1 1-2l-2-1 1-1v-8-27c-1-1 0-5 0-6s-1-1-1-2v-3c-1 1-1 2-2 3-1 2-3 3-4 4h-1c1-1 2-1 2-2 1-1 2-2 1-3h0c1-1 1-1 2-1l1-28h0v-34c0-13 1-26 0-39v9l-2 1c-1 2-3 3-4 5v-23-1-11l-1-1c0-2 1-3-1-4v6c-1-3 0-7-1-10v-26c-1 5-1 10-1 15 0 1 1 3 0 4v-3c0-1 0-1-1-2v-1 5h-1c-1-6 1-13 0-19-1-2 0-3-1-5l-1 1h0v-26c0-1 0-1-1-2h-1c0-3 1-5 1-8v-4c1-4 0-6 1-9v-11-8c-1-1-4 0-5 0v4h-1v-8-5h3l1-1h-1c-1-2 0-2 0-3v-6c1-1 2 0 4 0v-1l1-1-1-2-2-1h-2c0-1-1-2-1-3h-1v-3h-1l-1 1-1 1v-1l-1 1h-2l-1 1h0l-1 2s-1 1-1 2l-2-1c0-3-1-6-2-9v1l-2-4c-1 0-2 0-3-1h0l-1 1h-1l-1 1c-1 2-1 4-1 6h-1v-6l-1-1-1-2h-2l2-1h-1-3-2v-1l-1-2c-1 1-3 1-5 1l1-1-1-1v-1c-2 1-3 1-5 1v-1c-1 0 0-1 0-2l1-1c-1-3 0-6-1-9 0-2 1-5 1-8 0-1 0-2-1-3v-1c-1-1 0-2 0-3l1-1-1-1v-1c2 0 3-2 4-3 1 0 2-1 4-1 2 1 3 0 5-1v-1-4h0c1 0 2 0 3-1v-4-1-1h-2c-1 0-2 1-3 1s-1 0-2-1h0l8-3-1-1h-2v-1l2-2h0c1-1 2-1 4-1 2-1 3-1 5-2v-1l-8 2v-9-2s-1-2-1-3l2-1c-1-1-2-1-3-2-1 0-1-1-2-1-1 1-2 1-3 1h0-3c-1 1-2 1-2 1-1-2-1-5-1-7v-8-7c1 0 1 0 2-1 1 0 1 0 2-1 1 0 1 0 2-1h1v-1l1-1c1-1 1-1 2-1v-1c-2-1-3 0-4 1-2 0-2 1-3 2s-2 0-3 0c3-2 7-3 11-5h0c1-2 4-2 6-2l14-3h7 2 3c1-1 3-1 4-2 1 0 2-1 3-1h1c1-1 1-1 2-1l1-1c2-2 5-4 6-6s2-4 2-6c1-1 1-2 1-3l-2 2h0c-1-3-1-6-1-9z" class="H"></path><path d="M495 519l2 2v4h-2 0v-1-5z" class="S"></path><path d="M492 575h2c-1 2-1 4-2 5l-2-1v-4h2z" class="Y"></path><path d="M495 560h-3c0-2-1-2 0-3 1 0 2-1 3 0h3l-2 2-1 1z" class="C"></path><path d="M433 415c1-2 4-2 6-2l14-3c-1 1-3 3-4 3-3 0-6-1-9 0l-1 1-1 1c-2 0-2 0-3-1l-2 1z" class="W"></path><path d="M495 606c-1 1-1 2-2 3l-1 1v-1c1-3 0-5 2-6 3 0 5-1 7 1v1c-1 0-1 0 0 1v1h0-1c-2-1-3-1-5-1z" class="B"></path><path d="M436 447h2c0 2 0 3 1 5v1 1c1 2 1 2 1 4 1-1 0 0 2-1h1v1h0-4l-1-2-2-1v-4h-1s-1-2-1-3l2-1z" class="E"></path><path d="M451 414c3-1 6 0 9 0 0 2 0 4-1 6h-1l-1-1c-1 0-1 1-2 1l1-1-1-1-1-1h-1c0-1 0-2-1-2l-1-1z" class="S"></path><path d="M491 675c2 0 3-1 4-1 2 0 3-1 6 0-2 2-2 4-3 6-2-2-5-1-7-2v-3z" class="B"></path><path d="M460 473h3c1 2 0 9 0 12l-10 1v-1l-1-1c3 0 5-1 8-1 1 0 1 0 1-1-1-2-1-3-1-6l2-2h0l-2-1z" class="s"></path><path d="M490 633v-1c-1-2-1-14 0-16 0-1-1-2 0-3h1c0 1 1 2 0 3v8l1 1v-4 1c0 2 0 5 2 7h0v1h-1v1l1 1c-1 0-2 0-3 1h-1z" class="S"></path><path d="M435 451h1v4l2 1 1 2h4 0c1-1 2-1 4-1 0 1 0 1 1 1v-2-1 4c-1 0-3 1-5 1l-8 2v-9-2z" class="G"></path><path d="M494 575c2-1 5 0 7 0v7l-1 1-1-1h-7-1c1-2 6-1 9-1v-1h-8 0c1-1 1-3 2-5z" class="D"></path><path d="M454 473c2 0 1 1 2 1s3-1 4-1l2 1h0l-2 2c0 1-1 1-2 2h-3c-1 1-3 2-4 3l-2 1-1-1c0-2 0-3 1-4s2-1 3-1c1-1 1-1 2-3z" class="J"></path><path d="M449 477c2 0 4 1 6 1-1 1-3 2-4 3l-2 1-1-1c0-2 0-3 1-4z" class="X"></path><path d="M493 683c1 2 1 9 1 11l-1 1h0c-2 4-1 8-3 12v1h-1c0-1 0-3 1-4 0-1 0-1-1-1 1-3 1-6 1-8 0-3 0-6 1-9 0-1 1-1 1-2l1-1z" class="N"></path><path d="M452 465c1-2 4-7 6-8s3-2 5-3v9c-3 2-7 1-11 2z" class="T"></path><path d="M442 417c3-2 6-3 9-3l1 1c1 0 1 1 1 2h1l1 1 1 1-1 1c1 0 1-1 2-1l1 1-1 1-1 2v1l-1-2h-3c-1 1-2 1-2 3v1c-1-1-1-2-2-3 0-2-1-3 0-5-2-1-4-1-6-1z" class="I"></path><path d="M455 478h3c1-1 2-1 2-2 0 3 0 4 1 6 0 1 0 1-1 1-3 0-5 1-8 1l1 1v1c-1 0-3 0-5 1-1 0-2 0-3 1-3 0-5 1-8 1 1-1 1-1 1-2 2 0 4 0 5-1 1 0 2-1 3-1h2v-1c1 0 2 0 3-1v-2c1-1 3-2 4-3z" class="q"></path><path d="M496 462c0-12-3-24-2-36 1 4 1 10 1 14 1 9 3 17 3 26-1 7 0 14 0 20 0 2 1 3 0 4v1c-2-10 0-20-2-29z" class="Q"></path><path d="M435 468h2l-2 2c0 1 0 2 1 3l2-1v1c1 0 1 1 2 1 2 0 5-1 7-1l1 1-1 1 1 1c1 0 1-1 1-2l1 1 3-1 1-1c-1 2-1 2-2 3-1 0-2 0-3 1s-1 2-1 4c-1-1-1-1-2-1l-2-1c-2 0-3 0-4 1l-1-1-1-4-1-1c0 1 0 2-1 2l-1-4-1 1v-1-1h-2c-1 0-2 1-3 1s-1 0-2-1h0l8-3z" class="L"></path><path d="M422 439l1-2v1 1c0 1 0 2 1 3 1 0 1 0 2-1h-2l1-1-1-1 1-1c2 0 3-1 5-1v-1h-1-1c-1 1-1 1-2 1h-2l1-1 3-1h2v-1h-1v-1l2-1h1-2-1l2-2h-2c1-1 1-2 2-2l1-1h-1c0 1-1 1-2 1v-1l2-1h1l1 1v4 4c-1 1-1 1-1 3v2c-1 2-2 2-1 4-1 1-2 1-3 1h0-3c-1 1-2 1-2 1-1-2-1-5-1-7z" class="P"></path><path d="M498 557c0-1-1-1-1-2h1v-1c-2 0-3-1-5-1h0l-1-1h3v-1l-3-1h0l1-1h1c-1-2-1-3-1-5h-1l1-2c2 0 4 0 6 1 0 1 0 2 1 2v1 10c1 0 1 2 1 3v2c-2 0-4-1-6-1l1-1 2-2z" class="F"></path><path d="M453 501l2-1h2c2 2 2 5 2 7h2l1 1-1 1c-1 1-1-1-2 1l-1 2v17c-1 0-1 1-2 1 0-1-1-1-1-2-1-5 0-10-1-15h0c0-1 0-3 1-4v-2-1c1 0 1 0 2-1-1 0-2-1-2-2v-1c0-1-1-1-2-1z" class="T"></path><path d="M455 528c2-2 1-6 1-9 0 0 0-1 1-2v2-2l1-1v-4 17c-1 0-1 1-2 1 0-1-1-1-1-2z" class="J"></path><path d="M492 621l1-1c1 1 1 0 2 1v4l-1 1c0 1 0 2 1 2s1 0 1-1c1-2-1-5 2-6 1 1 0 3 0 4h2v-1h1l-1-1 1-1h2v5 7h-1-1-3c-3-1-5 0-8-1h1c1-1 2-1 3-1l-1-1v-1h1v-1h0c-2-2-2-5-2-7v-1z" class="C"></path><path d="M498 625h2v-1h1l-1-1 1-1h2v5 7h-1v-2c-1 0-2-1-3-2l1-1-1-2c-1 1-1 1-2 1 1-1 1-2 1-2v-1z" class="I"></path><path d="M495 534c1 1 1 1 2 1 0 0 1 0 2-1 2-1 5 0 7 0 0 1 0 2-1 3v16 4 5h-2l-2-1v-2c0-1 0-3-1-3v-10-8-2h-1c-1 0-1 1-2 1h-4v-1h2v-2z" class="e"></path><path d="M500 556l3-3h0c1-1 1-2 1-3h0l1 3v4 5h-2l-2-1v-2c0-1 0-3-1-3z" class="M"></path><path d="M501 559c2 0 2-1 4-2v5h-2l-2-1v-2z" class="T"></path><path d="M495 534c1 1 1 1 2 1 0 0 1 0 2-1 2-1 5 0 7 0 0 1 0 2-1 3-2 2 0 5-2 8v1h-1-1c0-2 0-6-1-8v-2h-1c-1 0-1 1-2 1h-4v-1h2v-2z" class="D"></path><path d="M508 560h3c1 1 1 1 1 2-1 1-3 3-4 3h0l-1-1c-1 2-1 4-3 5 0 1 0 1-1 1l-2 1v4c-2 0-5-1-7 0h-2l1-1h-1v-1-1c-1-1-1-2-1-3 2 0 4 1 6-1h0l-1-1h-2-1c-1-1-2-1-3-2h1v-1-1h3c3 0-1 0 2 0h1c0 1 0 1 1 1 0-1 1-1 2-2-2-1-3-1-4-2h-1c2 0 4 1 6 1l2 1h2l1-1 1 1v1l1-1-1-1 1-1z" class="E"></path><path d="M508 560h3c1 1 1 1 1 2-1 1-3 3-4 3h0l-1-1c-1 2-1 4-3 5 0 1 0 1-1 1l-2 1v-7-1l2-1h2l1-1 1 1v1l1-1-1-1 1-1z" class="H"></path><path d="M461 453h2v1c-2 1-3 2-5 3s-5 6-6 8l-8 2c-2 0-5 0-7 1h-2l-1-1h-2v-1l2-2h0c1-1 2-1 4-1 2-1 3-1 5-2v-1c2 0 4-1 5-1v-4c2 0 4-1 6-1h0l7-1z" class="V"></path><path d="M454 454h0l7-1c-1 2-2 2-3 3-2 1-4 3-5 4s-2 1-3 2c1-2 3-4 5-7h0l-1-1z" class="J"></path><path d="M448 455c2 0 4-1 6-1l1 1h0c-2 3-4 5-5 7h-2l-1-1h-4v-1c2 0 4-1 5-1v-4z" class="f"></path><path d="M448 455c2 0 4-1 6-1l1 1h0l-2 1v1c-1 1-2 2-4 2h-1v-4z" class="U"></path><path d="M434 464c4 0 7-1 11-2l1 1c-1 1 0 1-1 1s0 0-1 1v2c-2 0-5 0-7 1h-2l-1-1h-2v-1l2-2z" class="X"></path><path d="M434 467c2-2 4-1 6-2 1-1 2 0 4 0v2c-2 0-5 0-7 1h-2l-1-1z" class="f"></path><path d="M496 462c2 9 0 19 2 29v6c0 8 0 16 2 25 0 0-1 1 0 1 1 1 2 1 3 1-2 0-4 0-6 1v-4l-2-2c0-4-1-8-1-12v-11c-1-2-1-4-1-6 0-4 1-11-1-15v-3l1-1c1 0 1 1 2 1 2-2 1-7 1-10z" class="G"></path><path d="M494 496h0c1 2 1 1 1 2v2l1 1 1 20-2-2c0-4-1-8-1-12v-11z" class="I"></path><path d="M487 467c0-2 0-8 1-9v20 28 16l-1-3v-4-7-3c-1-1-1-2-2-2-1-2 1-5 0-7-1 1-1 6-1 8v10c0 4 0 8-1 12v-27c0-4 0-8-1-11 0-8 0-17 1-25h0c0-3 2-4 3-5 0 2 0 7 1 8v1z" class="D"></path><path d="M487 467c0-2 0-8 1-9v20l-1-1v16c0 1 0 3-1 4l-1-15v-6c2-2 2-6 2-9z" class="G"></path><path d="M482 488c0-8 0-17 1-25h0c0-3 2-4 3-5 0 2 0 7 1 8v1c0 3 0 7-2 9v6c0-2 0-4-1-6 0 8 0 16-1 23 0-4 0-8-1-11z" class="B"></path><path d="M484 476c-1-3 0-7 1-11v-3 14 6c0-2 0-4-1-6z" class="F"></path><path d="M434 473l1-1 1 4c1 0 1-1 1-2l1 1 1 4 1 1c1-1 2-1 4-1l2 1c1 0 1 0 2 1l1 1 2-1v2c-1 1-2 1-3 1v1h-2c-1 0-2 1-3 1-1 1-3 1-5 1 0 1 0 1-1 2l-11 3-8 3v-1c-1-1 0-2 0-3l1-1-1-1v-1c2 0 3-2 4-3 1 0 2-1 4-1 2 1 3 0 5-1v-1-4h0c1 0 2 0 3-1v-4z" class="a"></path><path d="M434 473l1-1 1 4c1 0 1-1 1-2l1 1 1 4 1 1c1-1 2-1 4-1l2 1-4 1c-3 1-6 2-8 2-1 1-1 2-1 3-2 1-4 2-6 2l-9 3 1-1-1-1v-1c2 0 3-2 4-3 1 0 2-1 4-1 2 1 3 0 5-1v-1-4h0c1 0 2 0 3-1v-4z" class="c"></path><path d="M492 671l-1-2 1-1h10v-5c-1 1-2 0-3 0-2 0-6 1-7-1 1-2 1-2 1-4-1-1-1-1-2-1l1-1h5v-1c1 1 2 1 4 1 1 0 1 1 1 2v-5-2l-1 1h-3c-2-1-4 0-6-1-1 0-1-1-1-2h1v-1h1v-2h-2c1-1 1-2 2-3l-1-1v-1h11v-4 21c0 5 0 10 1 14l-1 1c-1 0-1 1-2 1-3-1-4 0-6 0-1 0-2 1-4 1 1-1 2-1 2-1 1-1 2 0 2-1s0 0-1-1h0 1c-1-1-2-1-2-1h-1z" class="E"></path><path d="M492 671h11v2c-1 0-1 1-2 1-3-1-4 0-6 0-1 0-2 1-4 1 1-1 2-1 2-1 1-1 2 0 2-1s0 0-1-1h0 1c-1-1-2-1-2-1h-1z" class="W"></path><path d="M476 407h0c1-1 0-1 1-2l3-3h1l-1 1h1v1c1 2 1 4 2 6s1 3 1 5h1v3c0-1 0-1 1-2 1 1 1 2 1 3v1h2l1 1c-1 1-1 2-2 3l-9 7v4h0c0-2 0-3-1-4l-6 3v-1l-1 1c0-2 0-3 1-5v-1c0-1 0-1 1-1h1l-1-1-1 1c-1-2 0-6 0-8-1-2 0-2-1-3 0-1-1-2-1-2h-2-1-1l2-1c-1-2-1-2-1-3h1c0 1 0 1 1 2h0c1 0 1 0 1-1h1s0 1 1 1v1c1 0 0-1 2-2 0 1 0 1 1 2 0-2 0-4 1-6z" class="Y"></path><path d="M483 410c1 2 1 3 1 5h1v3c0-1 0-1 1-2 1 1 1 2 1 3v1c-1 2-2 2-3 3 0-4-1-8-1-13z" class="E"></path><path d="M476 407l1 1v5 14l-1 1v2h0c-1-1-1-2-1-3h-1v4l-2 2-1 1c0-2 0-3 1-5v-1c0-1 0-1 1-1h1l-1-1-1 1c-1-2 0-6 0-8-1-2 0-2-1-3 0-1-1-2-1-2h-2-1-1l2-1c-1-2-1-2-1-3h1c0 1 0 1 1 2h0c1 0 1 0 1-1h1s0 1 1 1v1c1 0 0-1 2-2 0 1 0 1 1 2 0-2 0-4 1-6z" class="Z"></path><path d="M503 570h1 1v4l1 1v-1l2-1v6 3c0 2 0 3-1 5v7l-1 21h-1c-1-3 1-12-1-14-1 2 0 4 0 6v12c0 2 0 5-1 8v-5-5l-1-2c-1 0-2-1-2-2h-1c0-1-2-3-3-4h-1v-1-1-1c2 0 3 0 5 1h1 0v-1c-1-1-1-1 0-1v-1-1-1h-4l-1-1h-4l-1-1v-1-1c1-3 6-1 8-2h1 1v-3h-2v-1h-1l1-1h2v-1-1h-1c-3-2-7 0-9-2 1-1 1 0 3 0 0 0 0-1 1-1v-2h-4l1-2h7l1 1 1-1v-7-4l2-1z" class="S"></path><path fill="#442009" d="M504 570h1v4l1 1v-1l2-1v6 3c-3-1-1-2-3-4v1l-2 1v5c-1-2 0-4-1-6 0-3 0-6 2-9z"></path><path fill="#4a230a" d="M506 574l2-1v6 3c-3-1-1-2-3-4v1c0-1 0-1-1-2h0v-1h1l1-1v-1z"></path><path fill="#a03d0f" d="M505 579v-1c2 2 0 3 3 4 0 2 0 3-1 5v7l-1 21h-1c-1-3 1-12-1-14-1 2 0 4 0 6v12c0 2 0 5-1 8v-5-5-21-11-5l2-1z"></path><path d="M498 466h0c1-1 1-5 1-7-1-1-1-4-1-5 0-5-2-10-2-14l-1-20h1v1c0 3 1 6 1 9h1l-1-10h1l1 3c3 3 4 9 5 14 0 2 1 8 2 9v9 1c-1 1-1 3-1 5h0c0 2 0 5-1 6v-4h-1l-1 7c1 3 1 7 1 11h0l-1 10c0-1 0-3-1-4v1h-1c0 1 0 2-1 3v4l-1 2v-6-1c1-1 0-2 0-4 0-6-1-13 0-20z" class="P"></path><path d="M500 458c0 2 0 6 1 7 0 1 1 1 1 2v3c1 3 1 7 1 11h-1v1c-2-1-1-3-1-5-1-6-1-13-1-19z" class="Z"></path><path d="M498 430c2 7 2 15 3 22l-1 2h0l-2-9c0-4-2-10-1-15h1z" class="B"></path><path d="M498 430l-1-10h1l1 3c3 3 4 9 5 14 0 2 1 8 2 9v9 1c-1 1-1 3-1 5h0c0 2 0 5-1 6v-4h-1l-1 7v-3c0-1-1-1-1-2-1-1-1-5-1-7v-4h0l1-2c-1-7-1-15-3-22z" class="W"></path><path d="M502 448c0 5 1 10 1 15l-1 7v-3c0-1-1-1-1-2-1-1-1-5-1-7v-4h0l1-2h0l1-4z" class="E"></path><path d="M498 430l-1-10h1l1 3 3 25-1 4h0c-1-7-1-15-3-22z" class="D"></path><path d="M506 530l1 3v-1c2 0 4 0 6-1 1 2 2 3 3 4h1c0 3 0 7-1 9l1 17 1 1c-1 1-1 1-1 2l1 12c0 1 0 2-1 3v-4l-1-1v1l-1 1c0-2 0-2-1-4-1 1-2 1-3 1v1c-1 0-1 0-2-1h-1l-2 1v1l-1-1v-4h-1-1c1 0 1 0 1-1 2-1 2-3 3-5l1 1h0c1 0 3-2 4-3 0-1 0-1-1-2h-3l-1 1 1 1-1 1v-1l-1-1-1 1v-5-4-16c1-1 1-2 1-3v-4z" class="P"></path><path d="M508 553h2c1 1 0 3 1 5h-1-2v2l-1 1-1-1 1-3h1v-4z" class="J"></path><path d="M511 556v-1c1-1 1-3 1-5h0c1 2 1 2 1 3v2c1 1 2 2 2 3s0 2-1 3l-1-1h-2-3v-2h2 1v-2z" class="b"></path><path d="M508 553c-1-1 0-9 0-12 1 1 2 3 2 4 1 3 1 8 1 11v2c-1-2 0-4-1-5h-2z" class="Q"></path><path d="M515 542l1 2h0l1 17 1 1c-1 1-1 1-1 2h-4c-2 2-2 3-2 5l-2 2h-1v-6c1 0 3-2 4-3 0-1 0-1-1-2h2l1 1c1-1 1-2 1-3v-16z" class="W"></path><path fill="#442009" d="M508 565v6h1l2-2c0-2 0-3 2-5h4l1 12c0 1 0 2-1 3v-4l-1-1v1l-1 1c0-2 0-2-1-4-1 1-2 1-3 1v1c-1 0-1 0-2-1h-1l-2 1v1l-1-1v-4h-1-1c1 0 1 0 1-1 2-1 2-3 3-5l1 1h0z"></path><path d="M503 570c1 0 1 0 1-1 2-1 2-3 3-5l1 1c-2 2 0 5-2 8v1 1l-1-1v-4h-1-1zm3-40l1 3v-1c2 0 4 0 6-1 1 2 2 3 3 4h1c0 3 0 7-1 9h0l-1-2c0-1 0-1-1-2-2-1-4-1-6-1l-1-1c-2 3-1 16-1 20v2l1 1 1 1-1 1v-1l-1-1-1 1v-5-4-16c1-1 1-2 1-3v-4z" class="W"></path><path d="M507 538c0-1 0-1 1-2h8l1-1c0 3 0 7-1 9h0l-1-2c0-1 0-1-1-2-2-1-4-1-6-1l-1-1z" class="R"></path><path d="M442 417c2 0 4 0 6 1-1 2 0 3 0 5 1 1 1 2 2 3v-1c0-2 1-2 2-3h3l1 2v-1l1-2h0 2c1 2 0 4 0 6 1 3 1 7 0 10v2h1 3c1 1 1 2 1 4-3 1-18 3-22 3h0l-4 1h-2c-1-1-2-1-3-2-1 0-1-1-2-1-1-2 0-2 1-4v-2c0-2 0-2 1-3v-4c1 3 0 8 1 11 2-1 2-1 3-3v-1c-1-3-1-7 0-10 1-1 1-1 1-2 1-3 1-6 1-9h3 0z" class="G"></path><path d="M431 444c-1-2 0-2 1-4v-2c0-2 0-2 1-3v8c2 0 3 0 4-1 0 2-1 2-1 3v2c-1-1-2-1-3-2-1 0-1-1-2-1z" class="V"></path><path d="M442 417c2 0 4 0 6 1-1 2 0 3 0 5h-1-2v-1c1 0 1 0 2-1l-1-1v1h-6c0-2 3 0 4-1-1-2-3 0-4-1v-1h1l1-1h0z" class="B"></path><path d="M437 442h3c1 0 1 0 2 1v3h0l-4 1h-2v-2c0-1 1-1 1-3z" class="T"></path><path d="M440 442c1 0 1 0 2 1v3h0c-1 0-1-1-2-1h-2c1-1 1-1 1-2l1-1z" class="V"></path><path d="M440 442c0-1 2-1 3-1s0 0 1-1c-1 0-1 0-2 1h-1-2l1-1c0-2 0-3 1-4h2l1-1-1-1c-1 1-2 2-3 2l-1-1c2-1 2-1 3-1l1-1h-3c1-1 2-1 3-2h-3c1-1 1-1 3-1 0-2 0-2 1-3h1v2c1 0 0 0 1-1h1c0 1-2 2-2 3h2l-1 1 1 1c0 2 1 5-1 7 4 1 10 0 14-1h0 3c1 1 1 2 1 4-3 1-18 3-22 3v-3c-1-1-1-1-2-1z" class="T"></path><path d="M457 421h0 2c1 2 0 4 0 6 1 3 1 7 0 10v2h1 0c-4 1-10 2-14 1 2-2 1-5 1-7l-1-1 1-1h-2c0-1 2-2 2-3l-1-1 1-1-1-1h0v-1l1-1h1c1 1 1 2 2 3v-1c0-2 1-2 2-3h3l1 2v-1l1-2z" class="N"></path><path d="M454 433h1v1c-1 0-2 0-3 1v1l-2 2h-1c-1-2 0-2 0-3 2-1 3-2 5-2z" class="L"></path><defs><linearGradient id="AA" x1="473.473" y1="472.904" x2="462.549" y2="471.969" xlink:href="#B"><stop offset="0" stop-color="#686761"></stop><stop offset="1" stop-color="#838179"></stop></linearGradient></defs><path fill="url(#AA)" d="M465 429v-19h2c0 1 0 1 1 3l-2 1h1 1 2s1 1 1 2c1 1 0 1 1 3 0 2-1 6 0 8l1-1 1 1h-1c-1 0-1 0-1 1v1c-1 2-1 3-1 5l-1 21c0 3 0 7 1 9s0 6 1 8v22c1 9 0 19 0 28 0 6 1 12 0 18h-1v-2c-1 0-2 0-3 1l-1 1 2 1h0l-3 1 1-1-1-2-2-1h-2c0-1-1-2-1-3 2 0 3 0 4-1 1-2 0-4 0-6v-16-83z"></path><path d="M465 429v-19h2c0 1 0 1 1 3l-2 1h1 1c0 7-1 15 0 22v-1l-1 1v-8l-1-1v3h-1v-1z" class="Q"></path><path d="M471 464c1 2 0 6 1 8v22c1 9 0 19 0 28 0 6 1 12 0 18h-1v-2c-1 0-2 0-3 1l-1 1 2 1h0l-3 1 1-1-1-2-2-1c2-1 5-1 7-1 0-2 0-2-1-3h-2l1-1c2-3 1-8 1-11v-1c1-2 0-6 0-8l1-23v-26z" class="N"></path><path d="M471 464c1 2 0 6 1 8v22c-1 6 0 14 0 20h-1c0-7 1-16 0-23v-1-26z" class="d"></path><path d="M437 489c3 0 5-1 8-1 1-1 2-1 3-1 1 1 1 2 2 3 2 3 3 3 3 6 3 0 3 1 5 3h0l-1 1h-2l-2 1c1 0 2 0 2 1v1c0 1 1 2 2 2-1 1-1 1-2 1v1 2c-1 1-1 3-1 4h0c1 5 0 10 1 15 0 1 1 1 1 2l-2 1c1 0 2 2 3 2l-1 1h-2l-1 1h0l-1 2s-1 1-1 2l-2-1c0-3-1-6-2-9v1l-2-4c-1 0-2 0-3-1h0l-1 1h-1l-1 1c-1 2-1 4-1 6h-1v-6l-1-1-1-2h-2l2-1h-1-3-2v-1l-1-2c-1 1-3 1-5 1l1-1-1-1v-1c-2 1-3 1-5 1v-1c-1 0 0-1 0-2l1-1c-1-3 0-6-1-9 0-2 1-5 1-8 0-1 0-2-1-3l8-3 11-3z" class="B"></path><path d="M432 514v1 3l2 1 1-1h2c1 1 1 0 0 2v1h0c-2 0-4 1-6 1v1h-2v-1l-1-2c-1 1-3 1-5 1l1-1-1-1c2 0 5-1 8-1v-1h0l-1-1h1l1-2z" class="n"></path><path d="M418 506c2 1 2 0 3 2l-1 1h1l1-1v3h1v3l-1 1h1v3h0c-2 1-3 1-5 1v-1c-1 0 0-1 0-2l1-1c-1-3 0-6-1-9z" class="U"></path><path d="M433 493l1 1v3c1 0 1 0 2 1h1l-2 2c-2 0-2 0-3 1h1v3c-1 2-1 2-1 3 0 2-1 4-1 6l-3 1h-1l1-1h1l-1-1c2-2 2-5 2-7l-3 6h-1c0-2 0-2 1-3l1-1-1-1h0l2-1v-2c0-2 0-2 1-3h1v-1c-2 0-2 1-4 1v2h-1l-2-1h2c-1-1 0-1-1-2 2 0 4-1 6-1h2v-5z" class="C"></path><path d="M432 507c0-1 0-1 1-3v-3h-1c1-1 1-1 3-1v1l1 1c-1 0-1 0-2 1l1 1c2 1 3 2 3 5v1c1 1 1 1 3 1l-2 2h1c-1 2-2 2-2 4l-1 1h-2l-1 1-2-1v-3-1l-1-1c0-2 1-4 1-6z" class="D"></path><path d="M432 507l3-1c1 1 1 2 1 3 0 2-1 2-1 3 0 0 1 1 1 2 0 0 0 3-1 4l-1 1-2-1v-3-1l-1-1c0-2 1-4 1-6z" class="m"></path><path d="M436 502l1 1h0c1-1 3-1 5-1l1 1v1h0 5c-1 1-1 3-1 5h1l-1 2c0 1 0 2 1 3l-1 1v1c-2 0-4 1-5 0l-1 1h-3c0-2 1-2 2-4h-1l2-2c-2 0-2 0-3-1v-1c0-3-1-4-3-5l-1-1c1-1 1-1 2-1z" class="F"></path><path d="M443 504h5c-1 1-1 3-1 5h1l-1 2-2-1c0-2-1-3-2-4s0-1 0-2z" class="G"></path><path d="M448 492l1 1h0c1 1 1 2 2 3v1h1l1-1c3 0 3 1 5 3h0l-1 1h-2l-2 1-1 1c-1 2-2 3-3 4h0 0l-1-1 2-2c-1-1-1-1-1-2v-3h0c-1 1-1 1-1 2-1 1-1 1-1 2l1 1v1h-5 0v-1l-1-1c-2 0-4 0-5 1h0l-1-1-1-1v-1l2-2h-1c-1-1-1-1-2-1v-3l-1-1h2c0 1 1 1 2 2 3-1 7-1 9-2h0c1 0 2 0 2-1z" class="B"></path><path d="M435 501h1c1-1 2-1 2-2l1-1c2-1 3-1 5 0h2v1h-3c0 2 1 2 0 4l-1-1c-2 0-4 0-5 1h0l-1-1-1-1z" class="C"></path><path d="M437 489c3 0 5-1 8-1 1-1 2-1 3-1 1 1 1 2 2 3 2 3 3 3 3 6l-1 1h-1v-1c-1-1-1-2-2-3h0l-1-1c0 1-1 1-2 1h0c-2 1-6 1-9 2-1-1-2-1-2-2h-2v5h-2c-2 0-4 1-6 1 1 1 0 1 1 2h-2-1c0-1 0-2-1-3h0c-1-1-2-1-3-1 0 1 0 1 1 2l-1-1c0-1 0-2-1-3l8-3 11-3z" class="E"></path><path d="M418 495l8-3 1 1c1 1 2 0 4 0l1 1v1c-1 0-2 0-3 1-1 0-2 0-3 1h-2c-1 0-1 0-2 1-1-1-2-1-3-1 0 1 0 1 1 2l-1-1c0-1 0-2-1-3z" class="H"></path><path d="M435 493v-1c1-1 3-1 5-1l1-1c1-1 4-1 6-1h1 0v3c0 1-1 1-2 1h0c-2 1-6 1-9 2-1-1-2-1-2-2z" class="R"></path><path d="M448 504v-1l-1-1c0-1 0-1 1-2 0-1 0-1 1-2h0v3c0 1 0 1 1 2l-2 2 1 1h0 0c1-1 2-2 3-4l1-1c1 0 2 0 2 1v1c0 1 1 2 2 2-1 1-1 1-2 1v1 2c-1 1-1 3-1 4h0c1 5 0 10 1 15 0 1 1 1 1 2l-2 1c1 0 2 2 3 2l-1 1h-2l-1 1h0l-1 2s-1 1-1 2l-2-1c0-3-1-6-2-9v1l-2-4c-1 0-2 0-3-1h0l-1 1h-1l-1 1c-1 2-1 4-1 6h-1v-6l-1-1-1-2h-2l2-1h-1-3v-1c2 0 4-1 6-1h0v-1c1-2 1-1 0-2l1-1h3l1-1c1 1 3 0 5 0v-1l1-1c-1-1-1-2-1-3l1-2h-1c0-2 0-4 1-5z" class="e"></path><path d="M438 517h3l1-1c1 1 3 0 5 0l1 1h-1c0 1-1 1-1 2-3 2-6 1-9 2h0v-1c1-2 1-1 0-2l1-1z" class="T"></path><path d="M453 501c1 0 2 0 2 1v1c0 1 1 2 2 2-1 1-1 1-2 1v1 2c-1 1-1 3-1 4h0v1 16l-1-1v-10-11c-1-1-2-1-3-2h-1c1-1 2-2 3-4l1-1z" class="J"></path><path d="M448 504v-1l-1-1c0-1 0-1 1-2 0-1 0-1 1-2h0v3c0 1 0 1 1 2l-2 2 1 1h0 0 1c1 1 2 1 3 2v11h-2-5c0-1 1-1 1-2h1l-1-1v-1l1-1c-1-1-1-2-1-3l1-2h-1c0-2 0-4 1-5z" class="s"></path><path d="M448 509c1 1 1 1 1 2l1 1v1c-1 1-1 1-2 1-1-1-1-2-1-3l1-2z" class="J"></path><path d="M447 515h3v2c0 1 0 1 1 2h0-5c0-1 1-1 1-2h1l-1-1v-1z" class="V"></path><path d="M435 523l15-2 1 1v3 1c0 2 1 3 0 5l2 1c0-1 1-1 1-1 1 0 2 2 3 2l-1 1h-2l-1 1h0l-1 2s-1 1-1 2l-2-1c0-3-1-6-2-9v1l-2-4c-1 0-2 0-3-1h0l-1 1h-1l-1 1c-1 2-1 4-1 6h-1v-6l-1-1-1-2h-2l2-1z" class="L"></path><path d="M446 525h-2v-1c1-1 3-1 4-1h1v1 3c-1-1-2-1-3-2z" class="R"></path><path d="M451 525v1c0 2 1 3 0 5l2 1c0-1 1-1 1-1 1 0 2 2 3 2l-1 1h-2l-1 1h0l-1 2s-1 1-1 2l-2-1c0-3-1-6-2-9 1-1 0-2-1-4 1 1 2 1 3 2v2l1-1c0-1 0-2 1-2v-1z" class="S"></path><path fill="#ff7502" d="M508 573h1c1 1 1 1 2 1v-1c1 0 2 0 3-1 1 2 1 2 1 4l1-1v-1l1 1v4l1 2c1 4 1 9 0 13v25 4l1 21v10 11 7 3c0 2 0 3-1 5h0c-4 1-8 1-12 1-1-2-1-5-2-7v-2c-1-4-1-9-1-14v-21-3-7c1-3 1-6 1-8v-12c0-2-1-4 0-6 2 2 0 11 1 14h1l1-21v-7c1-2 1-3 1-5v-3-6z"></path><path fill="#e95a03" d="M516 676c2-3 0-7 3-11v7c-1 1-1 2-2 3l-1 1z"></path><path fill="#e45708" d="M508 573h1c1 1 1 1 2 1v-1c1 0 2 0 3-1 1 2 1 2 1 4l1-1v-1l1 1v4l1 2c1 4 1 9 0 13v25c-1-1-1-2-1-2 0-6-1-11-2-16v-8c-1-1-1-2-1-3v-2c0-1-1-1-1-2-2 1-2 2-2 4h-2c0 2 0 2-1 4h-1v-7c1-2 1-3 1-5v-3-6z"></path><path fill="#a03d0f" d="M508 573h1c1 1 1 1 2 1v-1c1 0 2 0 3-1 1 2 1 2 1 4l1-1v-1l1 1v4l1 2c1 4 1 9 0 13v-6l-1-3c-1 1-1 0-2 1v-5l-1-1c-3 0-4 0-6-1h0v-6z"></path><path fill="#e45708" d="M503 627c1-3 1-6 1-8v-12c0-2-1-4 0-6 2 2 0 11 1 14h1 1c0 4 1 18-1 22h1c1 3 0 8 0 10 0 6 0 12 1 18 0 4 0 8 1 12h5c1-1 1-1 2-1l1-1c1-1 1-2 2-3v3c0 2 0 3-1 5h0c-4 1-8 1-12 1-1-2-1-5-2-7v-2c-1-4-1-9-1-14v-21-3-7z"></path><path d="M479 481l1 3v-5h0c1 3 0 8 1 11v-8l1 1v5h0 0c1 3 1 7 1 11v27c1-4 1-8 1-12v-10c0-2 0-7 1-8 1 2-1 5 0 7 1 0 1 1 2 2v3 7 4l1 3-1 142v23c0 6 2 15 0 21h0l2 1c0 3-1 13 0 14l1 23v1c-1 2 0 6-1 8 0 1-2 2-2 3v-1h-1s-1 0-1 1v4 13 4 43c-1 4-1 9-1 13 0 1 1 4 0 4l-1 3-2-2c0-1 0-1 1-2l-2-1 1-1v-8-27c-1-1 0-5 0-6s-1-1-1-2v-3c-1 1-1 2-2 3-1 2-3 3-4 4h-1c1-1 2-1 2-2 1-1 2-2 1-3h0c1-1 1-1 2-1l1-28h0v-34c0-13 1-26 0-39v-23-15-26-18-8c0-1 0-1-1-2h0c1-3 1-6 1-10v-16-6-12-52-7-14z" class="S"></path><path d="M479 763v6h1c0-3-1-7 0-10 1 2 0 5 1 6v-6c1 2 1 6 1 8-1 4-1 8-1 13l-1 10h0c-1 1-1 2-2 3-1 2-3 3-4 4h-1c1-1 2-1 2-2 1-1 2-2 1-3h0c1-1 1-1 2-1l1-28h0z" class="E"></path><path d="M486 724c1 3 0 7 0 10-1 3-1 6-1 9 0 2 0 3 1 5 1-1 2-1 4-2v1c-1 2 0 6-1 8 0 1-2 2-2 3v-1h-1s-1 0-1 1v4 13 4l-2-50 3-5z" class="Y"></path><path d="M487 708l2 1c0 3-1 13 0 14l1 23c-2 1-3 1-4 2-1-2-1-3-1-5 0-3 0-6 1-9 0-3 1-7 0-10-1-4-1-12 1-16z" class="R"></path><path d="M483 640v1l1 53 1 1c0-1 0 0 1-1l-1-2v-2c1 3 1 6 1 9h1v-22-13 23c0 6 2 15 0 21h0c-2 4-2 12-1 16l-3 5c-2-15 0-31 0-45v-44z" class="F"></path><defs><linearGradient id="AB" x1="480.767" y1="594.886" x2="488.742" y2="595.147" xlink:href="#B"><stop offset="0" stop-color="#676868"></stop><stop offset="1" stop-color="#858483"></stop></linearGradient></defs><path fill="url(#AB)" d="M483 526c1-4 1-8 1-12v-10c0-2 0-7 1-8 1 2-1 5 0 7 1 0 1 1 2 2v3 7 4l1 3-1 142v13 22h-1c0-3 0-6-1-9v2l1 2c-1 1-1 0-1 1l-1-1-1-53v-1-114z"></path><defs><linearGradient id="AC" x1="446.624" y1="571.632" x2="503.233" y2="559.687" xlink:href="#B"><stop offset="0" stop-color="#5c5b65"></stop><stop offset="1" stop-color="#b9b6a2"></stop></linearGradient></defs><path fill="url(#AC)" d="M488 424c1 2 0 4 1 7v5c1 4 1 10-1 13s-4 5-6 7c-1 0-3 2-3 2v4 19 14 7 52 12 6 16c0 4 0 7-1 10h0c1 1 1 1 1 2v8 18 26 15 23 9l-2 1c-1 2-3 3-4 5v-23-1-11l-1-1c0-2 1-3-1-4v6c-1-3 0-7-1-10v-26c-1 5-1 10-1 15 0 1 1 3 0 4v-3c0-1 0-1-1-2v-1 5h-1c-1-6 1-13 0-19-1-2 0-3-1-5l-1 1h0v-26c0-1 0-1-1-2h-1c0-3 1-5 1-8v-4c1-4 0-6 1-9v-11-8c-1-1-4 0-5 0v4h-1v-8-5h3l1-1h-1c-1-2 0-2 0-3v-6c1-1 2 0 4 0v-1l3-1h0l-2-1 1-1c1-1 2-1 3-1v2h1c1-6 0-12 0-18 0-9 1-19 0-28v-22c-1-2 0-6-1-8s-1-6-1-9l1-21 1-1v1l6-3c1 1 1 2 1 4h0v-4l9-7z"></path><path d="M477 541c-1-7 0-15 0-22 0-8-1-14 1-21 0 1 1 3 0 4v18c0 1 1 5 0 6 0 1 0 3-1 5v10z" class="h"></path><path d="M472 433v1 38c-1-2 0-6-1-8s-1-6-1-9l1-21 1-1z" class="I"></path><path d="M473 670v4c2 8 0 17 1 25 1-1 2-1 3-2-1-2-2-2-3-4 0-1 0-3 1-4 0-2 0-3 1-5 1-3 1-5 1-8v-4l-1-1v-2c1-2 1-2 3-2v23 9l-2 1c-1 2-3 3-4 5v-23-1-11z" class="D"></path><path d="M478 502h1v52 12 6 16c0 4 0 7-1 10h0c0 1 0 1-1 2v11 3l-1-13-1-1v-24c0-5-1-11 1-15v6c0 4 1 9 0 14 0 2-1 4-1 7 0 1 1 1 1 2v9h1l-1-23c0-3 0-6 1-9h0v-2c1-3 1-8 1-11-2-2-1-10-1-13v-10c1-2 1-4 1-5 1-1 0-5 0-6v-18z" class="J"></path><path d="M477 600c0-5-1-18 1-21 1-2 0-5 1-7v16c0 4 0 7-1 10h0c0 1 0 1-1 2z" class="M"></path><path d="M471 540h1l1 104v19c0 1 0 5-1 6 0-2 1-3-1-4v6c-1-3 0-7-1-10v-26-21c0-2 0-2 1-4 0-1 0-1-1-2 2-3 1-9 1-13v-24-13-2-11-5z" class="E"></path><path d="M488 424c1 2 0 4 1 7v5c1 4 1 10-1 13s-4 5-6 7c-1 0-3 2-3 2v4 19 14 7h-1c1-1 0-3 0-4l1-63h0v-4l9-7z" class="C"></path><path d="M488 424c1 2 0 4 1 7v5c-1 2-3 4-5 6-2 1-2 2-4 3h-1l1-1v-3c0-1 1-2 1-3v-5c0-1 1-1 1-3l-2 1v8h-1v-4-4l9-7z" class="S"></path><path d="M466 542l3-1h0l-2-1 1-1c1-1 2-1 3-1v2 5 11 2 13 24c0 4 1 10-1 13 1 1 1 1 1 2-1 2-1 2-1 4v21c-1 5-1 10-1 15 0 1 1 3 0 4v-3c0-1 0-1-1-2v-1 5h-1c-1-6 1-13 0-19-1-2 0-3-1-5l-1 1h0v-26c0-1 0-1-1-2h-1c0-3 1-5 1-8v-4c1-4 0-6 1-9v-11-8c-1-1-4 0-5 0v4h-1v-8-5h3l1-1h-1c-1-2 0-2 0-3v-6c1-1 2 0 4 0v-1z" class="Y"></path><path d="M464 548l3 1c1 1 1 1 1 3h-1-4-1c-1-2 0-2 0-3l2-1z" class="d"></path><path d="M466 542l3-1h0l-2-1 1-1c1-1 2-1 3-1v2 5h-1c-2 0-3 0-5-1h-2v1c2 1 5 0 7 2h-5l-1-1-1 1 1 1-2 1v-6c1-1 2 0 4 0v-1z" class="b"></path><path d="M459 558v-5h3v2c2 2 5 0 7 3 0 1 0 2-2 2h-6c-1 1-1 1-1 2v4h-1v-8z" class="S"></path><path d="M465 570l1 2h0c1-2 0-6 0-9h1l-1 34-1 6v1c0-1 0-1-1-2h-1c0-3 1-5 1-8v-4c1-4 0-6 1-9v-11z" class="E"></path><path d="M465 604v-1l1-6c1 1 1 2 1 3 1 2 1 5 1 7v19 22 5h-1c-1-6 1-13 0-19-1-2 0-3-1-5l-1 1h0v-26z" class="C"></path><path d="M535 389h2v4l1 1 2 3v1l1 1 2 2c-1 0-1 1-1 1l2 2c0 1 0 3-1 4h1l1-1 1 1 2 1c2 2 2 4 2 6-1 5 0 10 0 15v3l-1-1h-2 0l-4-1v7l-1 1c-1 2-1 2-3 3l-3-3v1 2c1 3 1 7 0 10l-3-2c-3 3-4 22-5 26 0 2 0 6-1 7v-1l-1 3h0c1 1 0 3 0 5v1 1h-1c0 1 0 3-1 5 0 1 1 1 0 2v2l-1 1c0 1 1 1 0 2v2 2 15l-1 1h-1-3-6-9c-1 0-2 0-3-1-1 0 0-1 0-1-2-9-2-17-2-25l1-2v-4c1-1 1-2 1-3h1v-1c1 1 1 3 1 4l1-10h0c0-4 0-8-1-11l1-7h1v4c1-1 1-4 1-6h0c0-2 0-4 1-5v-1-9c-1-1-2-7-2-9-1-5-2-11-5-14l-1-3v-2c1 1 2 3 3 3 3 2 5 4 8 5h0c3 1 4 1 6-1l3-2 1-1h-1 0c-1-1-1-2-1-3 1-1 1-2 2-3 2-1 4-3 4-5 1-2 3-3 4-6 0-1 1-1 2-2 0-1 1-2 2-3v3l4-13v-1z" class="H"></path><path d="M503 481c1 4 1 7 1 10 0 1 0 3-1 4v3 1l-1-8 1-10z" class="Z"></path><defs><linearGradient id="AD" x1="508.263" y1="506.012" x2="495.816" y2="510.948" xlink:href="#B"><stop offset="0" stop-color="#5b5b4d"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#AD)" d="M504 491c1 7 1 13 1 20 0 4 0 8 1 12l6 1h-9c-1 0-2 0-3-1-1 0 0-1 0-1 0-1 1-2 1-2 0-1-1-2 0-2 0-1 1-1 1-2 1-2 0-9 0-11l1-6v-1-3c1-1 1-3 1-4z"></path><path d="M512 430h0 2c1 1 0 2 0 3v11c1 2 0 4 0 7s2 19 1 21l-1-2c-1 2-1 4-1 6l-1-1v-1c1-6 1-13 0-20v-24z" class="C"></path><defs><linearGradient id="AE" x1="506.124" y1="495.396" x2="516.84" y2="488.296" xlink:href="#B"><stop offset="0" stop-color="#262828"></stop><stop offset="1" stop-color="#494848"></stop></linearGradient></defs><path fill="url(#AE)" d="M512 474v1l1 1c0-2 0-4 1-6l1 2-1 21c-1 2 0 9-1 11h-1c-1 0-1-1-2-2s1-24 2-28z"></path><path d="M499 491c1-1 1-2 1-3h1v-1c1 1 1 3 1 4l1 8-1 6c0 2 1 9 0 11 0 1-1 1-1 2-1 0 0 1 0 2 0 0-1 1-1 2-2-9-2-17-2-25l1-2v-4z" class="l"></path><path d="M499 491c1-1 1-2 1-3h1v-1c1 1 1 3 1 4l1 8-1 6h0v6h-1c0-7 1-14-2-20z" class="b"></path><defs><linearGradient id="AF" x1="502.465" y1="480.622" x2="516.37" y2="475.928" xlink:href="#B"><stop offset="0" stop-color="#4b4b4d"></stop><stop offset="1" stop-color="#716f6c"></stop></linearGradient></defs><path fill="url(#AF)" d="M511 455l1-1c1 7 1 14 0 20-1 4-3 27-2 28s1 2 2 2c-1 2 0 5-1 7h-1v1c1 0 2 0 3 1v1 2 1h0c-2-1-3-1-5-2v-2c1-2 1-3 1-5l-1-14-1-22v-6l1-1 1-4h0v-2c1 1 1 2 2 2v-6z"></path><path d="M523 419c1-1 1-1 2-1l1 3c-1 1-2 4-2 5-2 0-2 0-4 2v1l-1 3c-1 4 0 9-1 13s0 8-1 12v-2l-1 1c-1-2 0-7 0-10v-11l-1 3-1-5c0-1 1-2 0-3h-2 0v-1l-1 1-1 1c0-1 0-1-1-2h-1l-1 1-1-1h-1v5c0 1 0 2-1 3-1-5-2-11-5-14l-1-3v-2c1 1 2 3 3 3 3 2 5 4 8 5h0c3 1 4 1 6-1l3-2 1-1 4-3z" class="R"></path><path d="M508 429h1c1 1 1 1 1 2l1-1 1-1v1 24l-1 1v6c-1 0-1-1-2-2v2h0l-1 4-1 1v6c-1-5-1-12-1-17v-9c-1-1-2-7-2-9 1-1 1-2 1-3v-5h1l1 1 1-1z" class="K"></path><path d="M511 430l1-1v1 24l-1 1c-1-2 0-5 0-7v-18z" class="l"></path><path d="M505 434v-5h1l1 1 1-1v5l1 27-1 4-1 1v6c-1-5-1-12-1-17v-9c-1-1-2-7-2-9 1-1 1-2 1-3z" class="C"></path><path d="M505 434v-5h1l1 1 1-1v5 1l-2-4h0l-1 3h0z" class="G"></path><path d="M505 434h0l1 12c-1-1-2-7-2-9 1-1 1-2 1-3zm30-45h2v4l1 1 2 3v1l1 1 2 2c-1 0-1 1-1 1l2 2c0 1 0 3-1 4h1l1-1 1 1 2 1c2 2 2 4 2 6-1 5 0 10 0 15v3l-1-1h-2 0l-4-1v7l-1 1c-1 2-1 2-3 3l-3-3v1 2c1 3 1 7 0 10l-3-2c-3 3-4 22-5 26 0 2 0 6-1 7v-1l-1 3h0c1 1 0 3 0 5v1 1h-1c0 1 0 3-1 5 0 1 1 1 0 2v2l-1 1c0 1 1 1 0 2v2 2 15l-1 1h-1-3c-1-2-1-7-1-9 0-17 0-34 3-50v-7l4-32c0-1 1-4 2-5l-1-3c-1 0-1 0-2 1l-4 3h-1 0c-1-1-1-2-1-3 1-1 1-2 2-3 2-1 4-3 4-5 1-2 3-3 4-6 0-1 1-1 2-2 0-1 1-2 2-3v3l4-13v-1z" class="H"></path><path d="M526 421l1-4h0l-5 39v2h-2l4-32c0-1 1-4 2-5z" class="N"></path><path d="M533 435l3 3v1 1 2c1 3 1 7 0 10l-3-2v-6-9z" class="E"></path><path d="M523 411c1-2 3-3 4-6 0-1 1-1 2-2 0-1 1-2 2-3v3c-2 6-5 11-8 16l-4 3h-1 0c-1-1-1-2-1-3 1-1 1-2 2-3 2-1 4-3 4-5z" class="G"></path><defs><linearGradient id="AG" x1="535.124" y1="439.614" x2="514.877" y2="459.885" xlink:href="#B"><stop offset="0" stop-color="#111717"></stop><stop offset="1" stop-color="#655f60"></stop></linearGradient></defs><path fill="url(#AG)" d="M521 466l5-38h1c1 1 1 1 1 2v-1h1l-3 33c-1 4-1 9-2 13v-6c-1-1-1-1-2-1l-1 2v-4z"></path><path d="M538 394l2 3v1l1 1 2 2c-1 0-1 1-1 1l2 2c0 1 0 3-1 4v1c-2 1-1 7-1 9 0 1 0 1-1 1l-3 3-1-1-1-1h-1l-2 1v-3l1-2v-2c1-1 1-3 1-3 1-2 1-3 1-4v-1c0-1 0-1 1-2 0-2 1-4 1-6s-1-1 0-4z" class="N"></path><path d="M535 420c1-1 2-3 2-5 1-3 4-12 3-14-1-1-1-1-1-2l1-1 1 1 2 2c-1 0-1 1-1 1h-1v4 3l-1 3c0 3-1 6-4 8h0-1z" class="C"></path><path d="M536 420h0c3-2 4-5 4-8l1-3v-3-4h1l2 2c0 1 0 3-1 4v1c-2 1-1 7-1 9 0 1 0 1-1 1l-3 3-1-1-1-1z" class="F"></path><path d="M520 458h2l-1 7v1 4l1-2c1 0 1 0 2 1v6l-2 28v14c0 3 0 5-1 7h-3c-1-2-1-7-1-9 0-17 0-34 3-50v-7z" class="c"></path><path d="M521 470l1-2c1 0 1 0 2 1v6l-2 28c-1 3 0 6-1 8h-1c-1-13-1-28 1-41z" class="P"></path><path d="M544 408l1-1 1 1 2 1c2 2 2 4 2 6-1 5 0 10 0 15v3l-1-1h-2 0l-4-1v7l-1 1c-1 2-1 2-3 3l-3-3v-1l-3-3h0c1-1 1-2 1-3h1 0v-2c-1-2-1-4-1-6l-1-3 2-1h1l1 1 1 1 3-3c1 0 1 0 1-1 0-2-1-8 1-9v-1h1z" class="C"></path><path d="M544 417h2c0 4 0 8-1 11l-1 1c-1-3 0-9 0-12z" class="P"></path><path d="M544 408l1-1 1 1 2 1v2h0l-1-1c-1 1-1 4-1 6v1h0-2v-9z" class="b"></path><path d="M542 418c0-2-1-8 1-9 0 6 1 14 0 21-3-1-5-2-7-4h-1c0-1 0-2-1-2l-1-3 2-1h1l1 1 1 1 3-3c1 0 1 0 1-1z" class="k"></path><path d="M535 420h1l1 1-1 1c1 1 1 2 2 3-1 0-1 0-2 1h-1c0-1 0-2-1-2l-1-3 2-1zm3 2l3-3c1 0 1 0 1-1v5c-2 0-3 0-4-1z" class="D"></path><path d="M548 409c2 2 2 4 2 6-1 5 0 10 0 15v3l-1-1h-2l-3-3 1-1c1-3 1-7 1-11h0v-1c0-2 0-5 1-6l1 1h0v-2z" class="M"></path><path d="M548 423h1c1 2 0 5 1 7v3l-1-1-1-1v-3c0-1 0-3 1-4l-1-1z" class="P"></path><path d="M548 409c2 2 2 4 2 6-1 5 0 10 0 15-1-2 0-5-1-7h-1c-1-3 0-9 0-12h0v-2z" class="l"></path><path d="M534 424c1 0 1 1 1 2h1c2 2 4 3 7 4-1 1-1 2-1 3-1 2-2 2-1 5l1 1c-1 2-1 2-3 3l-3-3v-1l-3-3h0c1-1 1-2 1-3h1 0v-2c-1-2-1-4-1-6z" class="I"></path><path d="M535 426h1c2 2 4 3 7 4-1 1-1 2-1 3-1 2-2 2-1 5h-1c-3-3-3-5-4-8 0-2-1-3-1-4z" class="B"></path></svg>