<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="159 84 749 868"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#191718}.C{fill:#2a2a2a}.D{fill:#38383a}.E{fill:#ede4c7}.F{fill:#1d1c1d}.G{fill:#c3bca7}.H{fill:#393e3f}.I{fill:#d6cfb7}.J{fill:#575651}.K{fill:#232323}.L{fill:#b5ae9c}.M{fill:#cdc6b1}.N{fill:#989280}.O{fill:#2d3131}.P{fill:#50504d}.Q{fill:#96927d}.R{fill:#827d6e}.S{fill:#68665c}.T{fill:#aaa391}.U{fill:#787469}.V{fill:#d7d1bd}</style><path d="M438 835l3 1-3 5c0-1-1-2-2-2 0-2 1-3 2-4z" class="L"></path><path d="M461 863l2 3 1 1c0 1 0 2-1 3-3-1-6-3-8-6 1 1 2 1 4 1h1l1 1c-1-1 0-2 0-2v-1z" class="P"></path><path d="M441 828v2c1 0 2 1 3 1h1 0l-4 5-3-1 3-7z" class="Q"></path><path d="M312 488l3 1c1 1 1 1 2 1h2c1 1 1 1 2 1h1c1 1 1 1 2 1l1 1h1c0 1 1 1 1 1h0l-1 1h0c1 1 1 0 2 1l1 1c1 0 1 1 2 1 1 2 0 0 1 1l2 2v1l-1-1h-1l-2-2c-3-3-6-4-10-6l-1 1v-2h1l-3-1c-2 0-3-1-5-3z" class="F"></path><path d="M452 902c3 1 6 3 9 3 5 1 10 0 15 0-1 1-3 1-4 1h-1c-7 2-15 1-22-2l3-2z" class="U"></path><path d="M210 503l4 1c-1 3-2 7-2 10-1 2-1 5-1 7h0c-1 1-1 2-1 3v1h-1v-1l1-21z" class="M"></path><path d="M250 591v-1c-1-3-1-8 1-10 1-1 2-2 4-2s3 1 4 2h-2c-1 1-1 2-1 3-1 1 0 1-1 1-1 1-1 2-1 2-2 2-3 3-4 5z" class="I"></path><path d="M385 168v1c1 1 0 2 0 3l1 1 1-1c0-1 0 0 1-1h0c-2 6-6 14-9 19l-1-1c0-1 0 0-1-1l8-20z" class="T"></path><path d="M814 392c4-3 9-4 14-5v1c-2 3-9 6-12 8-2 0-2 0-3-1 0-2 0-3 1-3z" class="F"></path><path d="M765 233h1c1 1 1 1 3 1l-4 3c-2 2-6 5-7 8 0 1 0 3 1 4h-1c-1-1-3-1-4-3 3-5 6-10 11-13z" class="B"></path><path d="M711 505h6c4 1 7 5 9 8-1 0-2-1-3-1-6-2-10-2-16-2l4-5z" class="S"></path><path d="M711 593h-1l3-3c0-1 1-1 1-2l1-1v-2c1-1 1-2 1-4h1v-4-1-3c-1-1-1-1-1-2-1-2-1-4-2-5l-1-1c-1-2-2-3-4-4l-1-1h-1l-1 1-3 3h-1c-1 0-3-1-4-1h-1c-1 0-3-1-4-1-1 1-2 0-3 0h0c2-1 2-2 4-2-1-1-1-2-1-3l1-1v-3c0-1 0-1 1-2h0v-1l2-1 1 1c-3 2-3 3-4 7v2c2 1 6 4 9 4 1-1 2-3 3-4s1-1 2 0c4 1 7 7 8 10 2 5 2 12 0 17-1 3-3 5-5 7z" class="C"></path><path d="M814 392l-1-1c-2-1-2-1-3-3 1-2 2-2 3-3 3-1 9-3 12-1 1 0 2 1 3 3-5 1-10 2-14 5z" class="J"></path><path d="M187 484c3 3 5 5 6 9 2 5 1 11 1 15v1c-1-6-5-13-9-19 2-1 2-4 2-6z" class="N"></path><path d="M448 768l1-1c2 3 3 6 4 9v1c0 3 0 8-2 11-2 2-6 5-9 6-1 0-3 0-5-1 4-2 9-5 12-10 2-5 1-10-1-15z" class="B"></path><path d="M608 843l2 2 2 6c0 3 0 6 1 9 2 9 0 19 0 28h-1v4c0-2 0-8-1-10v-3l-1 1-1 1c1-5 2-9 2-13 1-9-1-16-3-25z" class="C"></path><path d="M846 510c-1-1-1-3-1-4 0-10 1-17 9-24 0 3-2 7 0 9-4 6-6 12-8 19z" class="R"></path><path d="M482 866h1c-1 2-2 3-4 4-3 2-5 4-8 5-1 1-2 1-3 1-6 0-8 0-12-4l-2-3v-1c3 0 5 2 8 3 2 1 3 1 6 1 5-2 9-4 14-6z" class="P"></path><defs><linearGradient id="A" x1="233.472" y1="688.562" x2="235.236" y2="680.303" xlink:href="#B"><stop offset="0" stop-color="#434445"></stop><stop offset="1" stop-color="#64635d"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M221 687h-3c1 0 2-1 2 0h4c1 0 2 0 3 1v-1c-1-1-1-2-2-3 1-1 4-1 5-1 5-1 9-2 13-4l-2 2h0c1 1 2 1 3 1l1-1h1v1h2 0l-13 6c-4 2-9 0-14-1z"></path><path d="M328 504c3 0 7 1 9 2l1 5c-5-1-10-1-15 0-3 0-6 1-10 2 4-4 9-8 15-9z" class="U"></path><path d="M757 445c1-1 2-1 3 0v1s0 1-1 2h0c-1 2-3 4-4 4l-1 1c-1 0-1 0-1 1h-1c-1 0-1 0-2 1h-1 0-5c-1 0-1-1-2-1-2-1-3-1-4-3 0 0 0-1 1-2h2 5l1 1h1c2-1 3-2 5-3 1-1 3-2 4-2z" class="B"></path><path d="M432 715h1c2 3 1 6 2 9 0 2 2 5 3 7h0l-8 1c-2 0-3 0-4 1h-1 0c1-6 3-13 7-18z" class="S"></path><path d="M256 735l1 1c0 2-3 6-5 8-3 5-11 10-18 11-5 1-13 0-18-3-3-3-4-6-6-10h2l2 5c2 3 6 6 10 6 7 2 13 0 18-4h1c5-4 10-9 13-14z" class="B"></path><path d="M829 743v1c-1 1-1 3-2 4-3 4-7 7-11 7l-1 1c-3 0-7 0-11-1-9-3-15-10-20-18 0-1-1-1-1-2-1-2-1-2 0-3l3 4c4 7 13 15 20 17 5 2 10 1 15-1 3-2 5-4 7-7l1-2z" class="F"></path><defs><linearGradient id="C" x1="441.455" y1="903.479" x2="438.501" y2="883.728" xlink:href="#B"><stop offset="0" stop-color="#85806e"></stop><stop offset="1" stop-color="#b3aa9a"></stop></linearGradient></defs><path fill="url(#C)" d="M426 876c1 2 2 3 2 4 1 1 1 2 1 3 1 0 1 1 1 2h4v-3c5 10 9 15 18 20l-3 2c-10-4-15-10-20-19l-3-9z"></path><path d="M417 797c2-2 7-1 10-1 3-1 6-2 9-2h1 6c-2 4-6 8-10 9l-4 1h-1c-4-3-7-5-11-7z" class="D"></path><path d="M854 482c4-4 10-8 15-7 3 0 5 1 6 3 1 1 1 3 1 4-2-1-4-1-6-1l-1 1c-6 1-11 5-15 9-2-2 0-6 0-9zm-691 0c1-2 1-3 3-5s5-2 8-2c5 1 10 5 13 9 0 2 0 5-2 6-2-2-4-4-7-6-5-3-9-3-15-2zm138 97h-1c0-2-2-3-3-4-3 0-6-1-8-2 0-3 0-4 1-6 2-2 4-2 6-2s5 0 6 2c3 3 2 8 2 11l-1 8-2-2v-5z" class="E"></path><path d="M301 579v5l2 2 1 8c2 15 9 26 22 35l-3-1c-5-1-8-4-11-7-12-12-12-26-11-42z" class="R"></path><path d="M280 460l1-1h1l1 1c0 3 3 5 5 7l12 12 12 9c2 2 3 3 5 3l3 1h-1v2 1h-4l-1-1h0 0c-4-4-9-5-14-9-7-5-12-12-18-19-1-2-2-3-2-5v-1z" class="B"></path><defs><linearGradient id="D" x1="276.207" y1="373.677" x2="270.696" y2="404.305" xlink:href="#B"><stop offset="0" stop-color="#848070"></stop><stop offset="1" stop-color="#b7af9b"></stop></linearGradient></defs><path fill="url(#D)" d="M264 406c0-1 1-3 1-5a369.08 369.08 0 0 1 13-29c1 2 4 7 4 8s-3 6-3 7c-3 6-5 12-7 19l-1-1-3 1v-1l-2-1-1 2h-1z"></path><path d="M778 364c2 4 5 8 8 12l8 13c8 16 16 32 23 49h-1v-1l-7-15c-4-8-9-16-14-24-6-10-12-19-20-27l3-7z" class="S"></path><path d="M824 515l1 1c1 1 2 4 3 6v1l-1-9h1c0 2 1 3 1 5 0-1 0-2 1-3v26c-1 2-1 5-1 7v2c-1-3-1-6-1-9-1-5-2-10-5-13-3-4-7-6-11-7l-5-1c2-2 4-3 6-5 3 1 7 2 10 3l-2-4c2 1 3 3 5 5-1-1-1-2-2-3v-1-1z" class="G"></path><path d="M212 387v-1h0c3-1 6-1 9-1 5-1 11-3 15-5 1-1 2-1 3-1v2c-1 1-1 2-2 3l-6 9c-1 1-2 1-3 2l-1 1c-5-1-11-2-14-6-1-1-1-2-1-3z" class="J"></path><path d="M212 387c4 1 7 3 10 4 5 1 9-1 12-4 1-1 1-3 2-3h1l-6 9c-1 1-2 1-3 2l-1 1c-5-1-11-2-14-6-1-1-1-2-1-3zm336 521v-1c-10-7-14-20-16-31-2 6-6 15-11 20-1 0-2 1-3 0-2 0-3-1-4-3-2-3-4-8-5-12-1-3-2-6-3-8l-1-1h1 1v1l1 3 1 2v1c0 1 0 1 1 2v1s0 1 1 2c0 1 0 1 1 2 0 1 1 2 1 3s0 1 1 1v2h1c0 2 0 1 1 2 1 2 2 1 4 2l2-3c1 0 1-1 2-2l5-9c0-2 2-4 2-6v-2h1v2h0l1-1v-2l1-1v-1c1-2 1-3 2-4v-1c0-2 1-4 2-6 0-2 2-4 3-6l3-6h1c0-1 0-2 1-2 1-1 1-2 1-2l3-6 1-1v-3c1-2 3-5 4-7l1 1c-2 3-4 6-5 10l-3 6c-6 8-14 21-12 31v2 1c2 9 8 21 17 26l2 2c-2 0-5 1-7 2z" class="B"></path><path d="M295 591c1 2 0 4 2 6l-1 4v1 5l2 7c2 5 6 9 9 13-4-2-7-3-10-6-3-2-7-4-10-6-1 0-2-1-2-1l-1-1c-2-1-3-3-5-4 0-1-1-1-1-2l-1-1-1-3c-1 0-1-1-2-2h1c1 1 2 1 3 2l1 1h1 0c2 0 5 1 7 0h0c1-1 2-1 2-1l1-1c1 0 1 0 2-1 3-3 3-6 3-10z" class="H"></path><path d="M255 731c2 0 2-2 3-3 2-1 3-4 5-5v1l-2 3c-2 2-4 5-5 8-3 5-8 10-13 14h-1c-5 4-11 6-18 4-4 0-8-3-10-6l-2-5v-1c5 3 11 6 17 5 11 0 19-8 26-15z" class="D"></path><defs><linearGradient id="E" x1="439.946" y1="864.768" x2="425.097" y2="860.293" xlink:href="#B"><stop offset="0" stop-color="#bbb19f"></stop><stop offset="1" stop-color="#e4dcbf"></stop></linearGradient></defs><path fill="url(#E)" d="M433 837c0 2 0 3 2 3l1-1c1 0 2 1 2 2-4 9-7 18-6 28 0 4 1 9 2 13v3h-4c0-1 0-2-1-2 0-1 0-2-1-3 0-1-1-2-2-4-3-10 0-20 3-30l4-9z"></path><path d="M338 576h0c-1 2-2 6-1 8 1 3 4 5 7 6 1 1 1 1 3 2 1 0 2 1 3 2-1 0 0 0-1 1-2 1-6 1-8 0h-1l-6-3h-1c-3-2-6-5-7-9-1-2-1-3 0-5 0-3 1-6 4-8v-1c4-2 6-2 11-2-3 2-2 6-3 9z" class="H"></path><path d="M270 783h1v-1-1c1 1 1 2 1 3h0v1c0 2 0 6-1 9s-3 6-5 9c-1 4-2 10-4 14-1 1-2 0-3 1-2-2-3-4-4-7l-5-13c-1-4 0-7 0-11 2 5 3 12 8 15 1-1 1 0 1-1 2-3 2-9 3-12 2 0 4 1 6 0 1-1 2-4 2-6z" class="B"></path><defs><linearGradient id="F" x1="276.812" y1="608.526" x2="250.865" y2="587.988" xlink:href="#B"><stop offset="0" stop-color="#a49a89"></stop><stop offset="1" stop-color="#dad2ba"></stop></linearGradient></defs><path fill="url(#F)" d="M250 591c1-2 2-3 4-5 0 0 0-1 1-2 1 0 0 0 1-1 0-1 0-2 1-3h2c2 3 4 6 5 10s1 9 1 13c2 8 7 14 13 19-13-6-23-17-28-31z"></path><defs><linearGradient id="G" x1="780.294" y1="355.12" x2="803.383" y2="386.249" xlink:href="#B"><stop offset="0" stop-color="#868271"></stop><stop offset="1" stop-color="#b9b09b"></stop></linearGradient></defs><path fill="url(#G)" d="M799 391l-19-33 4-8 9 12c0 1 2 5 4 6l2 4c3 5 6 10 8 15v-1c-1-1-1 0-2-1-2 1-4 4-6 6z"></path><path d="M769 776c0 4 0 10 2 13 1 1 3 1 4 0h2l1 1c0 3-1 9 1 11l1 1c2-1 4-3 5-5s2-5 4-7c1 2 0 6 0 9h-1c-1 4-2 7-3 10s-2 6-4 8c-1 1-2 1-3 0-4-2-3-9-5-13-2-3-4-5-6-8 0-3-1-6-1-8 0-5 0-8 3-12z" class="B"></path><defs><linearGradient id="H" x1="223.388" y1="717.786" x2="252.273" y2="735.758" xlink:href="#B"><stop offset="0" stop-color="#7f7667"></stop><stop offset="1" stop-color="#98947e"></stop></linearGradient></defs><path fill="url(#H)" d="M271 709l-16 22c-7 7-15 15-26 15-6 1-12-2-17-5-1-1-2-1-3-3 5-1 10 1 15 2 13 0 21-8 30-16l13-12 4-3z"></path><path d="M694 226c-1-2-2-4-3-5-3-4-3-13-3-18h0c1-5 4-10 5-15 0-3 0-4-2-7l-2-2c2-1 3 0 4 1 2 1 5 5 6 8 0 1-1 3-1 4-1 4-3 7-3 12-1 2 0 6 1 8h1l1-2c2-2 5-3 8-3v1c0 1 0 2-1 2l-1 1c1 2 0 3 0 5l-5 7v7 1c-2-2-4-3-5-5z" class="R"></path><path d="M704 211c1 2 0 3 0 5l-5 7c0-2 0-4 1-6 0-3 2-4 4-6z" class="B"></path><path d="M262 733c0 5 1 11 2 16 2 8 5 15 6 24v10c0 2-1 5-2 6-2 1-4 0-6 0 0-2-1-5-1-8-1-6-2-12-2-18-1-6-1-26 3-30zm517-2c5 16 3 31 0 47 0 3-1 8-2 11h-2c-1 1-3 1-4 0-2-3-2-9-2-13 1-4 1-8 3-13 1-5 3-10 5-15 1-5 1-11 2-17z" class="E"></path><path d="M318 497c1 0 1 1 1 2h-1 0c0 1-1 2-1 3h-1v1l-1 1c0 1-1 1-1 2l-1 1v1h-1l-1 2h0l-1 2h-1v1l-2 2c0 1-1 1-1 1v1l-1 1c-1 1-1 1-1 2-1 1-2 1-2 2-2 1-1 0-1 1l-3 3c0 1 0 1-1 2l-2 2v1l3-3h1l1-1 1-1 6-3 2-1c1-1 2-1 3-1 1-1 1-1 2-1l1-1 2-1h2l5-2h2c1-1 2-1 3-1h8c1 1 2 0 2 1 2 0 4 1 5 3 0 1 1 1 2 2v1h0c1 1 1 1 1 2h0c1 1 1 2 2 3v1 1h1v2c0 1 0-1 0 1v5h0c1 2-1 4-1 6-1 3-4 3-4 5-1 1-1 2-2 2h-12c-2 0-3 1-4 2l1-3 1-1c2-2 2-2 2-5 2 1 4 1 7 1 4 0 6-2 9-5 1-1 2-2 2-4l-1-3c-3-6-8-9-14-11-12-3-24 5-35 10h-1c0 1-1 1-1 1l3-3v-1c-5 3-10 8-14 12h-1c12-14 25-27 33-43z" class="B"></path><defs><linearGradient id="I" x1="345.764" y1="532.995" x2="300.382" y2="518.954" xlink:href="#B"><stop offset="0" stop-color="#979080"></stop><stop offset="1" stop-color="#dacfb5"></stop></linearGradient></defs><path fill="url(#I)" d="M300 528c8-6 25-13 35-12 4 0 6 1 9 4 3 5 5 10 5 15l-1-3c-3-6-8-9-14-11-12-3-24 5-35 10h-1c0 1-1 1-1 1l3-3v-1z"></path><path d="M446 868c2 6 4 12 9 17 2 1 4 2 7 2 1-1 1-2 1-3l1-1c2 1 3 4 3 6h1l1-1c5-3 11-9 14-14l1-1v1c1 2 1 4 0 6h0c-4 7-14 20-22 21-3 1-7 0-9-2-7-6-7-22-7-31z" class="B"></path><path d="M793 362c0-4-6-10-8-13 1-2 2-5 4-7 0 0 1 0 2 1l4 2c8 3 13-6 20-6 1 0 2 0 3 1l-1 1c0 2 0 3-1 4-3 3-5 7-7 10l-3 6c-1 4-4 8-7 11l-2-4c-2-1-4-5-4-6z" class="J"></path><path d="M817 341c0 2 0 3-1 4-3 3-5 7-7 10l-3 6c-1 4-4 8-7 11l-2-4s1 0 1 1h1c2-2 6-9 6-11h-2 0l1-1c5-5 6-12 13-16z" class="F"></path><defs><linearGradient id="J" x1="284.23" y1="423.022" x2="257.845" y2="442.392" xlink:href="#B"><stop offset="0" stop-color="#ccc4ab"></stop><stop offset="1" stop-color="#f5edd1"></stop></linearGradient></defs><path fill="url(#J)" d="M264 406h1l1-2 2 1v1l3-1 1 1c-3 15-3 29 3 43 2 4 5 8 5 11v1c-3 1-5 1-7 0-4-2-7-5-9-9-7-13-4-32 0-46z"></path><path d="M210 503c1-2 2-5 2-8 2-10 5-20 13-28s14-9 25-9l-7 6 1 1c2 1 3 4 4 6-14 5-27 13-32 28l-2 5-4-1zm617 11c-2-16-9-29-23-38-3-2-8-4-11-6 0-4 2-5 4-7l-8-5v-1c4-1 8-1 11 0l1 1c8 1 15 9 19 16 7 12 10 29 10 42-1 1-1 2-1 3 0-2-1-3-1-5h-1zM527 219c2-9 2-18 4-27 4-10 11-21 20-27 4-4 10-7 15-10l-1 7-1 3h0l-2 2v1c-6 8-13 15-19 23-3 4-4 8-6 11-2 4-4 8-6 11 0 3-1 5-1 8 0-1-1-1-1-2l1-1v-5c-1 1-1 2-1 4-1 0-1 1-1 1v1 1l-1-1z" class="E"></path><path d="M707 237c2-1 4 0 6 0 4 0 7 1 11 2 3 1 7 3 11 3 0 0 1 0 2-1 5-1 9-5 13-8 3-2 5-4 8-5 6-2 11-1 17 1-4 1-7 2-10 4h0c-5 3-8 8-11 13 1 2 3 2 4 3l1 1 2 1c-8-1-17 0-25 0-4 0-9 1-13 0h0c-7-5-13-9-19-15 1 0 2 0 3 1z" class="R"></path><path d="M704 236c1 0 2 0 3 1 5 2 10 5 15 8 3 2 6 3 9 5l-8 1h0c-7-5-13-9-19-15z" class="C"></path><defs><linearGradient id="K" x1="770.778" y1="411.962" x2="821.722" y2="431.038" xlink:href="#B"><stop offset="0" stop-color="#696963"></stop><stop offset="1" stop-color="#847d6b"></stop></linearGradient></defs><path fill="url(#K)" d="M775 378c1 0 1 1 2 1 15 21 26 45 34 70 0 3 3 7 2 10-4-3-7-7-10-12l-6-10c2 6 3 10 6 15-1 0-3-1-5-2-3-1-6-2-9-2-1-1-1 0-2-1 1-8 3-16 3-24 0-15-7-32-15-45z"></path><path d="M497 926h1 0l1 1c0 2 1 4 0 7 0 3-2 6-5 8-7 5-20 4-28 2-13-2-26-7-33-18l-1-1-2-3c-3-7-5-15-5-23 0-2 0-4 1-7 0 1 0 2 1 3 0 10 4 20 12 27 11 10 30 15 44 14 4 0 9-1 12-4 1-1 2-4 2-6z" class="B"></path><path d="M315 495h4l-1 2c-8 16-21 29-33 43h1c4-4 9-9 14-12v1l-3 3s1 0 1-1h1c-12 10-25 25-27 41-1 8 0 15 4 21l6 6c-5-2-11-3-14-9-1-2-1-4-2-6 1-17 6-31 15-46 5-7 10-14 15-22h0c2-1 10-14 12-17s3-4 7-4z" class="I"></path><path d="M612 892v-4h1v7c2 12-1 23-9 32-10 13-26 17-41 19-6 0-13 0-18-4-3-2-4-4-5-7h0c-1-2 0-5 0-7v-1l1 1c1 1 1 3 2 4 3 3 7 5 11 5 15 1 34-5 45-15 9-8 12-18 13-30z" class="B"></path><defs><linearGradient id="L" x1="446.261" y1="791.291" x2="450.071" y2="830.359" xlink:href="#B"><stop offset="0" stop-color="#444549"></stop><stop offset="1" stop-color="#7f7c6d"></stop></linearGradient></defs><path fill="url(#L)" d="M440 818v-1c-1 0-1 0-1-1s0-1-1-1l1-7c-2 2-4 3-6 5-2 1-3 3-5 4 2-3 4-7 6-10 4-4 10-7 15-11 3-2 8-6 12-7 2 2 4 4 5 8 0 11-12 25-20 33l-1 1h0-1c-1 0-2-1-3-1v-2l-3 7c-1 1-2 2-2 4l-1 1c-2 0-2-1-2-3l2-8c-1-1-1-1 0-2h0v-1l1-1v-1-2c1-1 1-3 1-5 0 2 0 3 1 4h0l2-3z"></path><defs><linearGradient id="M" x1="441.992" y1="806.014" x2="449.894" y2="833.589" xlink:href="#B"><stop offset="0" stop-color="#1b191b"></stop><stop offset="1" stop-color="#3f4040"></stop></linearGradient></defs><path fill="url(#M)" d="M440 818l6-9c4-5 7-7 14-8-2 9-8 14-14 21-2 2-4 4-5 6l-3 7c-1 1-2 2-2 4l-1 1c-2 0-2-1-2-3l2-8c-1-1-1-1 0-2h0v-1l1-1v-1-2c1-1 1-3 1-5 0 2 0 3 1 4h0l2-3z"></path><path d="M314 572l1-3c1-9 6-15 13-20l-1 3c1-1 2-2 4-2-6 4-11 8-13 15-1 5-1 11-1 17h1c2 13 11 24 22 31 10 7 21 13 31 20 11 8 20 18 28 29s15 22 17 36l1 16h0l-1-1 1-1c-1-1-1-8-1-9v-1-2l-1-2c0-1 0-2-1-4v-1-3l-2-3c0-2-1-2-1-4 0-1-2-3-2-5l-1-1-1-2-1-3-4-5-5-7-2-2c0-1-1-2-2-3l-3-3c-3-5-9-9-12-12l-1-1-2-2h0-1l-2-2-5-3-4-3c-3-2-6-3-8-5h-1 0v1h-2 0-2l-1-1h-4v-1h-2l-4-2c-2 0-3-1-4-1l-1-1c-2 0-1 1-2 0l-1-1h-1c0-1 0 0-1-1h0c-1-1-2-1-3-1v-1c-1 0-2-1-2-1h-1l-1-2c-2-1-5-3-7-5 0-2 0-2-1-3h0c-1-1-2-2-2-3l-1-1c-1-1-1-2-1-3-1 0-1-1-1-1 0-1-1-2-1-3h0l-1-2h0v-3c0-1 0-2-1-4 0-2 0-5 1-7v-1-2c1 0 1-2 2-3 1 1 3 1 5 1z" class="F"></path><defs><linearGradient id="N" x1="331.691" y1="592.109" x2="319.198" y2="608.092" xlink:href="#B"><stop offset="0" stop-color="#aea693"></stop><stop offset="1" stop-color="#e4dcbf"></stop></linearGradient></defs><path fill="url(#N)" d="M309 571c1 1 3 1 5 1v5c0 5 1 9 3 14 5 16 22 26 36 33-13-2-29-8-38-19-6-9-9-18-7-29l-1-2c1 0 1-2 2-3z"></path><path d="M309 571c1 1 3 1 5 1v5h-4s-1 0-2-1h0l-1-2c1 0 1-2 2-3z" class="N"></path><path d="M276 367h1v2h-1c-1 2 1 1 0 2-1 0-2 1-2 2l-1 1-1 1-2 2-1 2-1 1v1l-2 2v1c-1 1-2 2-2 4-3 3-4 7-6 10v2c-1 1-1 1-2 3v1c-1 1-1 2-1 3-1 1-1 1-1 2v2l-1 1v1c0 1-1 3-1 5v4l-1 1v6 4 1c1 2 0 3 1 4v4c0 1 1 1 1 2v4l-1 1c-2 0-4 0-5 1h0-1l-1 1h-1-3v1h-1-1c-1 1-1 1-2 1v-1l-3 3c-2 1-3 2-4 3l-2 1c0 1-1 0-1 0v-1-3l1-3 2-6c1-2 1-3 2-4h0v-2c1-1 1-2 2-3l1-4c0-1 1-2 1-3v-1l1-1v-1l1-1c0-1 0-2 1-3 0-1 1-1 1-3 1-4 5-10 7-14l2-2 1-2 2-5 4-5 1-1v-2l2-2 1-1c1-1 2-2 2-3l1-1 1-2h1l1-2 3-3c0-1 1-2 2-2 0-1 1-2 2-3 1-2 1-2 3-3z" class="K"></path><defs><linearGradient id="O" x1="272.632" y1="407.351" x2="218.408" y2="438.344" xlink:href="#B"><stop offset="0" stop-color="#69685d"></stop><stop offset="1" stop-color="#89826f"></stop></linearGradient></defs><path fill="url(#O)" d="M270 375h0c1 0-6 9-6 10-7 11-12 24-13 36-1 10 0 18 1 27l-14 3c4-3 4-8 5-13-3 3-4 8-6 11-2 4-6 7-10 10l6-20c9-23 20-45 37-64z"></path><path d="M455 864h-1c-2-3-3-6-2-10 1-5 4-9 8-11 6-4 15-4 22-2 8 3 13 9 17 17 4 7 4 15 2 23-3 10-11 19-19 25-2 1-3 1-4 0 4-3 8-8 12-13 4-7 9-18 7-27 0 2 0 3-1 5l-4-5c-2-2-4-4-6-5-5-3-11-4-16-2h-1c-2 1-3 1-4 3-2 1-2 2-2 4l-2-3v1s-1 1 0 2l-1-1h-1c-2 0-3 0-4-1z" class="S"></path><path d="M461 863c-2-3-2-5-1-8s3-6 6-7c5-2 10-2 15-1 6 3 11 7 14 13 1 2 1 4 2 6 0 2 0 3-1 5l-4-5c-2-2-4-4-6-5-5-3-11-4-16-2h-1c-2 1-3 1-4 3-2 1-2 2-2 4l-2-3zM279 675l7 1c1 1 1 1 0 2v3c3-1 6-1 10 0v1c1 1 3 1 2 3-3 1-5 1-8 2-5 2-11 5-16 8-1 1-3 2-5 4 1 2 4 5 4 7-1 1-2 2-2 3l-4 3h-3c-4-1-7-2-10-5h0c-1-1-1-2-1-2-2-4 0-15 2-19h1c-1-1-17 7-20 8-2 1-4 1-6 1-5-1-13-2-18-5v-1h3l1-1c2-1 4-1 5-1 5 1 10 3 14 1l13-6c6-2 12-4 17-5l2-1 12-1z" class="B"></path><path d="M279 675l7 1c1 1 1 1 0 2v3h-1c-6 1-11 6-14 11-2 2-3 5-5 7-1 0-3 1-4 1-2 0-4-1-5-3-2-1-2-3-2-5 1-7 6-11 10-15l2-1 12-1z" class="Q"></path><path d="M333 212v-1c2-1 3-1 5-1 3 0 5 3 8 4 0-3 1-7 0-10-2-8-9-17 0-24l3-2c-2 4-4 8-2 13 1 3 4 6 5 9 1 4 1 14-1 19h0c-1 3-3 6-6 8h2c-8 9-17 16-27 23l-1 1c-5 1-10 0-15 0h-21v-1-1l3-4c-3-4-6-7-9-10-1-2-3-2-4-4h1c-1-1-2 0-3-1 1-1 2-2 4-2 12-4 22 10 31 14 0 0 1 1 2 1 0 0 2-1 3-1l9-3c5-2 10-2 15-4 3-1 4-6 5-9v-4l-2-2c-2-2-5-4-5-8z" class="R"></path><path d="M333 212c4 2 6 5 8 9 0 1 0 3-1 5v-4l-2-2c-2-2-5-4-5-8z" class="F"></path><path d="M274 231c5 1 9 3 12 8 1 2 2 4 2 6-1 2-3 3-5 5v-1l3-4c-3-4-6-7-9-10-1-2-3-2-4-4h1z" class="B"></path><path d="M345 227h2c-8 9-17 16-27 23l-1 1-8-1c8-5 18-10 27-16 2-2 5-4 7-7zm352 322h3 1 5 1l1 1h1v-6h-1c-2 1-6 1-9 1l-3-1h-1-1l-1-2v-1l7-3 2-1h2l2-1h4c2-1 8-1 10 0 1 0 2 0 4 1h3l1 1c1 0 2 0 3 1l1 1 2 1c1 0 0 0 2 1 0 0 1 0 2 1l3 2 1 1c1 0 1 1 2 2h0c2 1 3 2 5 3 1 1 1 2 2 3s3 3 3 4c1 1 2 1 2 3h1v1h1c0 2 1 3 2 4l1 2 3 7c1 1 0 1 1 2v1 1 1c1 1 0 2 0 3v1l-1-1h1v-3c-3-1-8-4-9-6l-1-2-3-7c-6-11-18-15-30-18l3-4v-2l-5 5 3 9c3 6 7 11 8 18l1 2c-1 1-3 2-5 2h-1l-1 2c-2 15-9 26-21 35-3 2-7 4-10 7-2 1-4 2-5 3h-1 0c-1-1-1-1-2-1 8-6 17-11 24-18 8-8 12-19 12-30 0-8-2-15-8-21-5-4-11-5-16-4l-1-1z" class="F"></path><path d="M710 541c-2 2-3 3-5 3-4 0-8 0-11-1v-1c10-6 21-7 33-4 2 0 5 2 7 3-3 0-5-1-8-1h-6-9l-1 1z" class="D"></path><defs><linearGradient id="P" x1="714.596" y1="539.399" x2="726.036" y2="571.555" xlink:href="#B"><stop offset="0" stop-color="#646259"></stop><stop offset="1" stop-color="#948f7e"></stop></linearGradient></defs><path fill="url(#P)" d="M710 541l1-1h9l4 1h1l-5 5 3 9c3 6 7 11 8 18l1 2c-1 1-3 2-5 2h-1l-1 2c0-4 0-7-1-11-2-9-7-14-14-19-1-2 0-5 0-8z"></path><path d="M726 577v-5l3-1h1c0 1 0 1 1 2l1 2c-1 1-3 2-5 2h-1z" class="N"></path><path d="M720 540h6c3 0 5 1 8 1 9 6 18 14 24 24 3 5 6 9 7 15-3-1-8-4-9-6l-1-2-3-7c-6-11-18-15-30-18l3-4v-2h-1l-4-1z" class="J"></path><path d="M771 420v5c-1-1-1-1-1-2v-6-4-1l-1-2v-2c-1-1-1-2-1-3h0c0-2-1-4-2-5s-1-2-1-3v-1c-1 0-1-1-1-2l1-1v-1c1-2 2-4 3-5v-2l1-1c1 0 1-1 2 1h0c0 1 1 4 2 6h0l1 2v1h0l1 1v1 1 1s1 1 1 2 0 2 1 3h0v2h0c0 1 1 1 1 2l1 2v1 1 3c1 1 0 2 1 3v4c1 2 0 3 1 5 1 1 0 3 0 4v6 2c-1 2 0 3-1 5h0v2l-1 2v2c-1 0-1 1-1 1v2l-2 2v1l-1 1c0 1-1 1-2 2s0 1-1 2h-1l-1 1h0c-1 0-2 1-3 1h-1c-1 0-2 0-2 1l-1 1h0c-1 1 0 1-1 1v3l-1 1v1 2c0 1 1 1 2 1h1c1 1 3 0 4-1 2-1 4-3 5-5l2-1c2-3 3-6 4-9l1-1c0-1 0-1 1-1h1l1 1h1l2 3v4l-2 3v1h0c-2 1-1 4-1 6l1 1v2h1c3 3 7 4 11 5 1 1 2 1 3 2l2 1 4 3h1c1 2-1 0 1 1 2 2 6 6 7 8l3 3v1c1 1 1 0 1 1l3 3v2c-3-4-5-8-9-11-3-4-8-9-13-11-3-1-5-2-7-3-3-2-8-3-9-7-2-3-1-6 1-9 1-2 2-3 1-6l-4-4-4 8c-1 3-5 9-9 10-2 1-5 1-6 1-1-1-2-1-2-2 0-4 2-6 2-9h-2v1c-2 3-4 6-6 10-5 6-9 12-15 17l-9 6c-1-1-3-2-5-2-1 0-2 0-3 2l3 6v1c-1-1-2-3-3-4v1c-1-1-1-3-2-4 0-1 0-1-1-2 0-1 0-1 1-1 0-1 2-1 3-2 3 0 6-2 9-4 9-6 15-12 21-20 2-2 5-7 7-8v2c0-6 5-11 7-16 3-8 5-18 4-26z" class="B"></path><defs><linearGradient id="Q" x1="764.448" y1="392.25" x2="775.858" y2="401.284" xlink:href="#B"><stop offset="0" stop-color="#8e8876"></stop><stop offset="1" stop-color="#bcb4a2"></stop></linearGradient></defs><path fill="url(#Q)" d="M770 412c-1-6-2-11-5-17l4-11c3 8 7 17 8 25h-1v-1-1c-1-1-1-2-1-3h-1c-1 0-3 2-4 3h0c1 2 1 4 0 5z"></path><defs><linearGradient id="R" x1="757.971" y1="425.488" x2="781.978" y2="440.697" xlink:href="#B"><stop offset="0" stop-color="#c8bda4"></stop><stop offset="1" stop-color="#f0ebce"></stop></linearGradient></defs><path fill="url(#R)" d="M770 412c1-1 1-3 0-5h0c1-1 3-3 4-3h1c0 1 0 2 1 3v1 1h1c4 13 5 32-1 44-2 3-5 7-8 8-2 0-6 1-8 1 0-6 5-11 7-16 3-8 5-18 4-26l-1-8z"></path><path d="M725 503l-3-6c1-2 2-2 3-2 2 0 4 1 5 2l33 51c2 4 3 7 5 11l2 7c1 1 1 3 1 3 2 7 3 16-1 22-1 2-2 3-4 5 0 2 0 3-1 5h0c-2 1-4 3-6 3h-1c-1 0-2 1-3 0-5-1-7-3-10-7 0-1 0 0-1-1s-1-1-1-3c1-1 1-2 2-3l1 1c1-1 1-2 1-3 0 2 1 3 1 5 3 2 4 4 8 4 2-1 5-2 7-4v-1c1-1 2-3 2-5h1v-3l1-2v-2-3c-1-1-1-2-1-3v-2h0v-1l-1-1v-1-1-2h-1c0-1 0-1-1-2s0-1 0-3l-2-2v-1l-3-6h-1c-1-1-1-2-2-3v-1l-2-1c-1-2-2-3-3-4-1-2-3-4-4-5-2-1-3-2-5-3v-1l-2-2h-1l-1-1h-1c-2-2-3-2-4-3-3-2-6-3-9-4h0-1c-1-1-1 0-2-1-1 0-2-1-3-1h0c-3-1-4 0-6 0h-2-1-1l-4 2c-1 0-1 1-2 1 0 1-2 1-2 1l-1-1c1-1 1-2 1-2l2-4h1c0-1 0-2 1-2h1c1-1 2-2 3-2h1 1 3c1 0 1 1 2 1l6 1 1 1h2c1 1 3 2 4 2 1 1 2 1 3 2 0 0 1 0 1 1h1 1l1 1h1l1 1h0l2 2h1l1 1 1 1h1 0l2 2h0v-1l-3-3c-1-2-2-2-3-3 0-1-1-2-2-3 0-1 0 0-1-1v-1h0c-2-2-2-3-3-4l-2-2-3-6c-1 0-1-1-1-1-1-1-1-1-1-2v-1z" class="F"></path><defs><linearGradient id="S" x1="706.217" y1="514.843" x2="738.566" y2="539.876" xlink:href="#B"><stop offset="0" stop-color="#958f82"></stop><stop offset="1" stop-color="#cdcab3"></stop></linearGradient></defs><path fill="url(#S)" d="M747 538c-10-8-18-14-30-17-7-1-12 0-17 4 0-3 2-6 4-8 2-1 4-1 7-1 10 1 22 8 30 14 2 2 5 4 8 6h-1c-1-1-2-1-4-2 1 1 2 2 3 2v2z"></path><path d="M747 538v-2c-1 0-2-1-3-2 2 1 3 1 4 2h1l4 4c1 2 3 3 4 5l1 1c1 1 2 3 3 4l2-2c2 4 3 7 5 11l2 7c1 1 1 3 1 3 2 7 3 16-1 22-1 2-2 3-4 5-2 1-4 2-6 2h-1c5-5 8-10 9-17 0-17-10-32-21-43z" class="E"></path><path d="M725 503l-3-6c1-2 2-2 3-2 2 0 4 1 5 2l33 51-2 2c-1-1-2-3-3-4l-1-1c-1-2-3-3-4-5-2-5-9-11-13-16-5-7-10-13-15-21z" class="I"></path><defs><linearGradient id="T" x1="650.466" y1="613.112" x2="703.276" y2="631.867" xlink:href="#B"><stop offset="0" stop-color="#6b675e"></stop><stop offset="1" stop-color="#9c9981"></stop></linearGradient></defs><path fill="url(#T)" d="M698 550c5-1 11 0 16 4 6 6 8 13 8 21 0 11-4 22-12 30-7 7-16 12-24 18-6 3-12 7-18 11-10 8-18 17-26 27-2 1-2 2-4 4l-4 7-5 9v1 1 1c-1 0-1 2-2 3h-1l-1-2 4-9 6-13h2 1 0c1-1 2-2 2-3 0-2 1-4 2-6 6-9 12-18 19-27 8-10 15-20 24-29 2 1 4 3 7 3 7 0 14-4 19-8 2-2 4-4 5-7 2-5 2-12 0-17-1-3-4-9-8-10-1-1-1-1-2 0s-2 3-3 4c-3 0-7-3-9-4v-2c1-4 1-5 4-7z"></path><path d="M427 895c0-1 0-2-1-3v-1c1-1 1-2 1-3h0c-1 0-1 0-1 2h0v-12c0 1 0 2 1 3v1 1h0c0 1 0 1 1 2h1c5 9 10 15 20 19 7 3 15 4 22 2h1c2 1 4 0 6 0 1 1 2 1 4 0h1c1 1 3 1 4 1h1 0c1 1 1 1 2 1h0c4 2 6 5 6 9l1 7c-4 2-8 5-12 6h-3c-3 1-6 0-9 0-9-2-16-4-23-9l-1-1c-1-1-1-1-2-1l-1-1h-1-1c1 1 1 2 3 2 3 3 6 5 11 6l1 1 4 1v1c6 2 13 3 19 2h1c2 0 4-1 6-2 2 0 7-4 8-3 0 2-1 5-2 6-3 3-8 4-12 4-14 1-33-4-44-14-8-7-12-17-12-27z" class="H"></path><path d="M481 922c-3 0-6 1-8-1v-1l1-1c2 0 3 0 5 1l2 2z" class="K"></path><path d="M479 920h0c2-1 3-1 4-3v-7c-3-1-5-2-8-2 2 0 3-1 5-1h2l1 1h2l1 1h1c2 1 4 3 5 5 1 1 0 3-1 4-2 2-4 3-7 4h-3l-2-2z" class="C"></path><path d="M609 881l1-1 1-1v3c1 2 1 8 1 10-1 12-4 22-13 30-11 10-30 16-45 15-4 0-8-2-11-5-1-1-1-3-2-4v-1l1 1 1 2v1h1 1-1l1-1 1-1c-1-1-2-1-3-2h0v-1l2 1h0c-1-1-1-2-2-3-2-2-2-6-1-8 0-4 3-6 6-8 2-1 5-2 7-2h6 1l1 1c6 0 13 1 19-1h0c4-1 7-2 10-4l2-1 1-1 3-2c3-2 6-6 8-9v-1l3-7z" class="H"></path><path d="M561 919h2c1 0 3 0 4 1l-1 1c-1 0-2 1-3 1-1-1-1-2-2-3z" class="F"></path><path d="M600 907h1c-1 2-1 3-2 4v1c-3 5-8 8-12 11-9 5-22 11-33 8h0c-2 0-4-1-6-2l-1-1c7 3 13 4 20 2 13-3 25-11 33-23z" class="B"></path><defs><linearGradient id="U" x1="299.105" y1="529.772" x2="319.896" y2="585.23" xlink:href="#B"><stop offset="0" stop-color="#121112"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#U)" d="M299 531c11-5 23-13 35-10 6 2 11 5 14 11l1 3c0 2-1 3-2 4-3 3-5 5-9 5-3 0-5 0-7-1 0 3 0 3-2 5l-1 1c-7 5-12 11-13 20l-1 3c-2 0-4 0-5-1l3-9h-1c1-1 1-1 1-2v-1c1-1 3-3 3-5 0-1 1-1 1-2l1-1v-1-2h0c1-2 0-3 0-4-1-1-1-1-3-1 0 1 0 1 1 2 0 0 0 1 1 2l-1 1h-3-1c0 1-1 1-2 1h-1l-3 1c-1 0-1 1-2 1l-1 1c-3 1-5 2-7 4h-1c-1 0-1 1-2 1-2 2-5 4-6 6v1c-1 1-2 2-2 3v1l-1 1v1c0 1 0 3 1 5l3 5c0 1 1 2 2 3 2-1 3 0 4-1h4c1 2 1 3 1 5l1 1c0 1 0 4-1 5v3c0 1 0 1-1 2v2 2l-1-1 1-4c-2-2-1-4-2-6 0 4 0 7-3 10-1 1-1 1-2 1l-1 1s-1 0-2 1h0c-2 1-5 0-7 0h0-1l-1-1h0 3c1 1 4 1 5 0h1 1l1-1c1 0 1 0 2-1s3-3 3-6c1-1 0-2 0-3 1-1 0-1 0-2 0 1-1 1-1 2s0 2-1 3h0c-1 2-2 2-3 3l-1 1c-1 0-3 1-5 1h-2c-1-1-2-1-3-1h-1l-1-1h0l-2-1-2-1-3-3h0l-1-1-1-1v-1h0l-1-1v-1-2-1c-1-1-1-3 0-4v3c1 2 1 4 2 6 3 6 9 7 14 9l-6-6c-4-6-5-13-4-21 2-16 15-31 27-41z"></path><path d="M295 591h0c-1-3-1-5-2-7 1-1 1-1 3-1 2 1 1 3 1 6v8c-2-2-1-4-2-6z" class="J"></path><path d="M310 539c6-4 20-4 27-2 3 1 7 3 10 2-3 3-5 5-9 5-3 0-5 0-7-1l-3-2c-4-2-9-2-14-2h-4z" class="D"></path><defs><linearGradient id="V" x1="318.135" y1="539.187" x2="320.534" y2="570.93" xlink:href="#B"><stop offset="0" stop-color="#69675d"></stop><stop offset="1" stop-color="#9a9581"></stop></linearGradient></defs><path fill="url(#V)" d="M314 539c5 0 10 0 14 2l3 2c0 3 0 3-2 5l-1 1c-7 5-12 11-13 20l-1 3c-2 0-4 0-5-1l3-9c2-4 4-7 5-12 1-2 2-4 1-6-2-2-3-2-5-2h-2v-1l3-2z"></path><path d="M310 539h4l-3 2v1h2v1c0 1 1 2 1 3l1 1c-1 1-1 0-2 0-9 1-19 7-25 12-3 4-6 8-6 13v1c-1 1-2 1-3 2-2 1-3 3-5 5 1-5 2-10 4-15 7-12 18-22 32-26z" class="J"></path><path d="M555 827l1-1c2-8 7-14 10-21 0-1 0-1 1-2l3-9c0-1 1-1 1-2l3-5c1-3 3-6 4-9s3-6 4-9l1-1v-2c1-1 1-1 1-2l3-6c0-2 2-4 3-6l8-16c1-2 1-2 2-3l1 1h0l1 1c0 1 1 1 2 2 2 1 3 2 5 2l3 1c2 1 3 1 4 1s2 1 3 1l-2-3-1-1c0-1 0-1-1-2l-1-1c0-1 1 0-1-1-1-2-8-2-11-1 0-2 1-3 2-5v-1c0-1 1-2 1-3l2-6c0-1 1-2 1-3h2c1 1 0 1 1 2 0 1 0 2 1 3v2c0 1 1 1 1 2v2l1 1v2l1 1c0 1 0 2 1 2h0v-1l-1-4c-1-1-1-2-1-3v-3c-1-2-1 0-1-2v-2c-1-1-1-4 0-5v-3c1-1 1-3 2-5-1-1-1-2-1-3 2-1 2-2 3-3v-1c1 0 1 0 2-1v-1c2-3 3-5 4-8v-1l1-1c0-2 1-3 1-4h0l1-2v-2c1-2 4-6 4-8 0-1 1-1 1-2l8-15c1-2 2-5 3-7 1-3 3-5 4-8 0-1 0-1 1-2l1-3 1-1v-2c1-1 1-1 1-2l3-6c0-2 2-4 3-6 2-4 3-7 5-10l2-3s1-1 1-2c1-1 1 0 2-1 0-2 2-3 3-4 0-1 1-2 2-3 0-1 1-2 2-3 1-2 0 0 1-1 4-4 8-9 9-14l1-2c3-3 5-5 9-6h2l2-1h0c1 1 2 1 3 1v1h7l1 1c1 0 2 0 2 1 1 2 1 2 2 3h0c1 1 1 2 1 3 1 2 2 3 1 5v2c-1 2-1 3-2 4-1 3-4 5-6 6v1c-1 0-2 1-3 1l-1 1h-3v1h-4v-1c-2 1-3 1-5 0v-1l2-1c1 0 1-1 2-1 2-1 5-3 6-5v-1c1-1 2-2 2-3v-1-1c1-2 1-4 0-6h-1c-5 7-13 9-19 15-15 16-26 35-36 54-1 3-4 8-4 10h0c-1 2-2 4-2 6 0 1-1 2-2 3h0-1-2l-6 13h-1c-3 7-5 14-9 21-1 2-2 4-4 5 1 3-1 7-2 10-1 11 6 27 13 34h-1-2v1c-2 0-4-1-5 0h-1c-2 0-2 0-3 1h-3l-1 1h-2-1c-3 1-8 4-10 7v1 1l-1 2c0 1 0 0-1 1v2c-1 4 0 6 1 10h-1 0c-1-3-2-6-3-8-2 4-4 9-2 14 1 6 7 11 13 14l3 2c-2 1-5 1-7 1h-1-3 0-1l1-1c0-1 1-1 2 0 1 0 2 1 3 1 1-1 2-1 3-1h1c-1-1-2-1-3-2-3-2-7-4-9-7v-1c-1-1-2-3-3-5h0c0-1-1-2-1-3v-3l-2 6c0 3 0 6 1 9l2 2c1 2 3 4 5 6h0c-3-2-5-4-6-6h-1l1 1c1 1 1 2 2 3h-1l-2-2-1 1-5-5c-2 2-4 5-5 7s0 3-1 5l-1-1c0-1 0-1-1-2l-1-4c-1 8-2 15 1 23 1 3 3 6 5 9h-1c-2-1-4-6-4-8l-1-2-1-1v-1c0-1 0-2-1-4v-9h1v-4h0v-1c-3 8-7 15-11 23-1 2-2 7-4 9l-1-1z" class="C"></path><path d="M609 716h0c3 5 4 11 5 16-1 0-2 0-3-1l-8 1c2-5 4-10 6-16z" class="J"></path><path d="M700 568c2 0 6-1 7 0 3 2 6 6 6 10 1 3-1 7-3 10-4 4-9 6-15 8v-1c-2 1-3 1-5 0v-1l2-1c1 0 1-1 2-1 2-1 5-3 6-5v-1c1-1 2-2 2-3v-1-1c1-2 1-4 0-6s-2-5-2-7z" class="H"></path><defs><linearGradient id="W" x1="639.384" y1="612.398" x2="696.207" y2="617.42" xlink:href="#B"><stop offset="0" stop-color="#5b5a57"></stop><stop offset="1" stop-color="#847e69"></stop></linearGradient></defs><path fill="url(#W)" d="M628 676l14-29c6-13 11-26 18-38 3-5 7-11 11-16s9-9 11-15l3-6c3-3 6-5 10-6 2 0 3 1 5 2 0 2 1 5 2 7h-1c-5 7-13 9-19 15-15 16-26 35-36 54-1 3-4 8-4 10h0c-1 2-2 4-2 6 0 1-1 2-2 3h0-1-2l-6 13h-1z"></path><path d="M734 663c13-3 27-6 39-13 4-2 8-6 13-9 2-1 3 0 4 0v1c-3 2-5 5-7 8-4 6-11 10-18 12-4 2-8 3-12 4-2 1-6 1-6 2l-2 1h0l2 1 7 1v4c1 1 1 0 1 1h-1c-1 0-1 1-2 1-3 0-8-3-10 1v2c2 2 10 3 13 5 4 2 15 9 16 13-1 3-2 6-4 8 2 2 4 3 6 4v2c5 3 9 6 13 10 6 6 12 13 21 16 3 1 7 1 10 1s12-2 14-1l-3 3 1 2-1 2c-2 3-4 5-7 7-5 2-10 3-15 1-7-2-16-10-20-17l-3-4c-1-2-3-4-5-5l1 4c-1 6-1 12-2 17-2 5-4 10-5 15-2 5-2 9-3 13-3 4-3 7-3 12 0 2 1 5 1 8h0c-1-1-1-2-1-3v-1c-1-2 0-4-1-5v-5h0l-1 1c0 1-1 3-1 4s-1 1-1 2h0v1c-1 0-1 1-1 2l-2 3-4 5-2 2h0c4-6 8-12 10-18 1-3 4-13 3-15l-1 1v-1c2-5 5-9 5-14 1-2 1-3-1-5 3-10 0-21-4-31-6-16-17-27-34-34 1-4 3-8 2-12 2-4 2-6 1-10z" class="H"></path><path d="M747 670l7 1v4c1 1 1 0 1 1h-1c-1-1-2-1-4-1-1-2-1-4-3-5z" class="S"></path><path d="M773 712c5 3 9 6 13 10 6 6 12 13 21 16 3 1 7 1 10 1s12-2 14-1l-3 3c-1 0-3 2-3 2-3 2-6 3-8 3-8 2-15-1-21-5-8-6-14-15-19-23-1-2-3-4-4-6z" class="N"></path><path d="M488 182c-4-5-7-9-9-15 3 1 7 3 10 4 8 6 15 14 20 22h0c-5-9-11-17-17-24-6-6-12-11-17-18 6 1 11 3 17 4 12 0 24-2 35-2 6 0 11 1 16 1-7 3-14 7-18 14 5-3 10-8 15-10 6-2 12-3 18-3-14 9-23 22-28 38l-5 29c0 2-1 4-1 6s0 8-1 9v-5c-1-4-1-8-1-11l-1-15-1 6v5l-2-1c-2-2-2-6-4-8h0v3 2c-4-9-7-16-14-23 0-1-1-2-2-3v1 1l-2-1s-1 0-1 1l-7-7z" class="E"></path><defs><linearGradient id="X" x1="412.777" y1="639.061" x2="333.6" y2="655.728" xlink:href="#B"><stop offset="0" stop-color="#69655d"></stop><stop offset="1" stop-color="#9e9b82"></stop></linearGradient></defs><path fill="url(#X)" d="M331 550h12c2 2 3 2 3 5 0 2 0 3-1 5-1 1-4 2-5 2-2 0-3 0-4-1l-3-4c-5 3-9 8-11 13-2 3-1 8-1 12h0c1 6 6 11 10 13 5 4 12 6 18 5l6-3c17 17 30 35 43 55h1c-9-18-19-37-32-52-3-4-7-9-10-12-6-5-13-7-19-12 1-3 0-7 3-9 5-1 9-1 13 3 2 1 2 3 3 5 2 6 8 11 12 16 4 4 8 9 11 14 10 15 16 33 24 50l8 15 9 20c2 2 5 6 6 9 0 1-1 3-1 3 0 4 1 7 1 11-1 8-5 19-10 26-2 3-3 5-6 7l-2 2c1-3 3-6 4-8 3-8 4-18 4-26l-1-16c-2-14-9-25-17-36s-17-21-28-29c-10-7-21-13-31-20-11-7-20-18-22-31h-1c0-6 0-12 1-17 2-7 7-11 13-15z"></path><path d="M244 343c2 0 5 0 7-1h0l13-3c-9-21-25-37-46-45-13-6-27-8-41-10-5 0-15 0-18-2-4-3-3-11-4-15v-6c0-2 0-4 2-6 1 7 1 13 3 19l27 3c27 4 52 14 69 37 7 10 12 22 18 33l14 31 43 97 71 155 79 175 27 63 6 15c1 1 2 4 3 5 0 1 1 1 2 1h3c2-4 3-8 5-12l11-24 44-94 59-124 92-190 33-69 16-33c4-9 9-18 15-26 10-12 24-24 38-30 8-4 17-6 25-8 5-1 11-1 16-2l14-1c2-1 5-1 7-2 0-3 1-6 1-8 0 1 1 2 1 3v1l-1 1v2c0 1-1 1-1 2-1 1-1 0-2 1h-1-3c-1 0 0 0-1 1h-4-2-3c-1 0 0 0-2 1h-4-2c-1 0-3 1-5 1h0c-1 0-2 0-3 1h-2-2-2l-1 1h-1c-1 0-2 0-4 1-1 0-1 0-2 1h-3l-7 2c-1 1-2 2-3 2h0c-1 1-2 1-2 1-1 0-1 0-2 1-1 0-1 0-2 1h-1c-2 1-2 1-3 2h0c-2 0-4 2-5 3s-1 0-1 1c-1 1-1 0-2 1-1 0-1 1-2 2h0c-2 1-4 2-5 4h0l-3 3h-1c-1 1-8 7-8 8l-3 4h-1l-1 2h0l-1 1c0 1 0 2-1 2l-1 1v1h-1v1c-1 1-1 1-1 2l-1 1c-1 1-1 1-1 2l-2 4c-2 3-4 8-6 12-1 2-3 4-3 6 0 1-1 1-1 2h0c-1 1-1 2-1 2v1c-1 1-1 2-2 3h0v1c-2 2-4 6-4 8-1 1-1 1-1 2v1c-2 1-4 5-4 7v1l-2 3-2 4-2 4v1l-6 12c-1 1-1 1-1 2-1 1-2 3-2 4-1 1-1 1-1 2v1c-1 1-3 4-3 5v1l-1 1c0 1-2 4-3 6 0 1-1 1-1 2s-1 2-1 3l-3 6h0v1h-1v1 1l-2 3-1 2v1h0c-1 1-1 1-1 2l-1 1v1c-1 1-1 2-1 2l-2 5h-1v1c-1 1-2 3-2 4v1c-1 1-2 3-3 5v1c0 1-1 1-1 2v1c-2 2-3 3-3 5s-1 3-2 4-2 3-2 5l-1 1v1 1l-1 1h0l-6 13h-1v2h-1v1l-3 6v1h0c-1 1-1 1-1 2s-2 2-2 3 1 1 0 2c-1 0-1 1-2 2 0 1 0 1-1 2-1 2 1-1 0 1l-1 2-2 5h-1v1c0 1-1 1-1 2l-3 6v1h0l-1 1v2l-1 1h0l-5 9v2l-1 1c0 1-1 1-1 2l-1 1v2c-2 2-4 5-4 8 0 0-1 1-1 2l-5 9c-1 2-1 3-2 5v1c-1 2-2 3-3 5 0 1 0 2-1 2v1 1c-1 0-1 1-1 2l-4 7v1l-1 1h0c0 1-1 2-1 2-1 2-6 10-5 12h-1v1c-1 1-1 1-1 2v1l-1 1-1 1c-2 2-3 7-5 10l-1 3-6 12c0 1-1 2-1 3-1 4-5 8-6 12v1l-1 1-6 13c-1 1 0 1-1 2 0 1 0 1-1 2l-1 3c-1 1-2 3-3 4 0 4-3 6-4 10h0v1l-1 2-5 11c-1 1-2 3-3 4v2c-1 1-3 4-3 6l-1 1c0 1-1 2-1 3s-1 2-2 4h0c-1 1-1 2-1 3-1 1-1 1-1 2l-5 9v1h0c-1 1-1 1-1 2-1 3-4 5-4 8v1c-2 2-4 5-4 7-1 3-4 6-4 8 0 1 0 1-1 2v2c-1 1-2 3-2 4l-1 1c0 1 0 1-1 2v2c-1 0-1 0-1 1-1 2-3 5-3 6v1c-2 2-3 4-3 6-1 1 0 0-1 2l-2 4v1l-2 2c-2 4-4 9-6 14h0v1c-1 0-1 1-1 1v1l-2 4c-1 1-1 1-1 2l-1 1-3 7h0v1h-1c0 2-2 4-2 6 0 0 0 1-1 1v1 1c-2 2-2 4-3 6l-2 3c-1 1-1 2-1 3l-1 1-3 6v1c-1 1-1 2-2 3 0 2-1 4-1 5-1 1-1 1-1 2v1h-1c0 1-1 1-1 2-3 0-4 1-6-1-1-1 0-1-1-2l-1-3-1-1s0-1-1-1v-2c0-1 0-1-1-2 0-1 0-1-1-2v-1h0c-1-1-1-2-1-3-1-1-1-1-1-2l-1-2h0c0-1-1-3-2-4h0c0-1-1-2-1-2-1-1-1-3-1-4l-1-2c0-1-2-2-2-3s1 0 0-1v-1l-1-1c0-1 0 1 0-1 0-1-1-1-1-2h0l-1-1v-1-1c-1-1-1-1-1-2s0-1-1-1v-1-1h0c-1-1-1-1-1-2-1-1-1-3-2-4s-1-2-1-2l-3-8c-1-2-2-3-3-4 0-2 0-2-1-4l-6-15-2-3c0-1 0 0-1-1 0-2-1-3-1-4l-1-3c-1-1-1-1-1-2-1-2-2-3-3-5v-2l-1-1c0-1 0 0-1-1v-1c0-1-1-1-1-2s0-2-1-3-1-2-2-3c0-4-3-8-4-11l-1-2c-1-1-1-2-1-2-1-2-2-3-2-5l-2-5-2-2v-1l-1-1v-1c0-1-1-2-1-4l-2-2c0-1 0-2-1-2 0-2 0-2-1-3v-1c0-1-1-1-1-2s-1-1-1-2c-1-2-2-4-3-5-1-2-1-4-2-6 0-1 0-1-1-2 0-1 0-1-1-1v-1c0-1-1-2-1-3-1-1-1-3-2-4 0-2-1-3-2-4l-1-2v-1c0-1-1-1-1-2h0l-1-1v-2c0-1-1-1-1-1l-1-2c0-1 0-1-1-2 0-1-1-3-2-4v-2l-1-1c0-2-2-3-2-5 0-1-1-1-1-2h0l-1-1v-1c0-1-1-1-1-2s0 0-1-1v-2l-3-6c0-1-1-2-1-3l-1-2c0-1-1-1-1-2h0l-1-1v-1h0v-1l-1-2c-1-1-1-2-1-2-1-2-2-3-2-4v-1h-1v-2c-1-1-1-1-1-2-1-2-2-3-2-5-1-1-2-4-3-4v-1-1l-1-1c0-1 0-1-1-2v-2l-1-1h0l-1-1v-1c0-1 0-1-1-1v-2h-1v-1c0-1 0-1-1-1v-1c0-1-1-3-1-3l-1-1v-1c0-1 0-2-1-2v-1h-1v-2c0-1-1-1-1-2v-1l-2-2c0-1 1-1 0-2s-1-1-1-3c-1 0-1-1-1-1-1-1-1-2-1-3l-1-1v-1c0-1-1-1-1-1l-1-2h0v-2l-2-2v-1-1h-1c0-1 0-1-1-2v-1c0-1-1-1-1-2l-1-1v-1-1h0l-1-1c0-1-1-2-1-3h0c-1-1 0-1-1-2v-1c-1-1-1 0-1-1s-1-1-1-2v-1-1h-1c0-1-1-2-1-3h0c0-2-2-4-2-5-1-1-1-1-1-2s-1-3-2-4v-1c-1-1-1-1-1-2-1-1-1-2-2-3h0v-2c-1-1-1 0-1-1-1-1 0 0-1-2 0-1-1-2-1-3-1-1-1-1-1-2-1 0-1-1-1-2-1-1-1-2-2-2v-1c-1-2-2-4-2-6-1-1-1-2-2-2v-2c-1-1-1-2-1-2l-1-1c0-1 0-2-1-3h0c0-1-1-2-1-2-1-1-1-2-1-3s-1-2-2-3c0-1 0-2-1-2 0-2 0-2-1-2v-2l-1-1v-1c0-1-1-3-2-4l-1-1v-2h0c-1-1-1-2-2-2v-2l-1-1c0-2-2-3-2-5 0-1-1-2-1-3-1-1-1-2-1-3l-2-2v-2h-1c0-1-1-2-1-3 0-2-1-3-2-4 0-1-1-3-1-4l-1-1s0-1-1-2h0v-1c0-1-1-2-1-3-1 0-1-1-1-2-2-3-2-5-4-8h0c0-2-2-4-2-5-1-1-1-1-1-2-1-2-2-3-2-5-1-2-2-4-3-5 0-1 0-1-1-2 0-1 0-2-1-2v-1c-1-1-1-1-1-2s-1-2-1-3h-1v-1l-1-3c-1-2-1-4-3-6h0l-1-3v-1c-1 0-1-1-1-2l-1-1c0-1-1-1-1-3 0-1-1-1-1-2-1-1-2-3-2-5 0-1-1-2-2-3 0-1-1-2-1-3-1-1-1-2-1-2l-1-2v-1l-4-8-2-5-2-3c0-1 0-2-1-2v-2c-1-1-2-3-2-4-1-2-2-3-3-4v-2l-1-1v-1l-1-2h0c-1-1-1-2-2-2 0-2-1-3-2-4 0-2-1-2-1-3v-1l-1-1v-1c-1-1-1-1-2-1v-1h0v-1l-1-1h0l-1-1-1-2s-1-1-1-2c-1 0-2-1-2-2l-11-10c0-1-1-1-1-1l-2-2-1-1-5-3h0l-4-2-4-2c-1-1-2-1-3-2-1 0-1 0-2-1h-2c0-1-1-1-1-1h-2 0l-1-1h-1-1 0l-1-1c-2 0-4-1-5-1-1-1-3-1-4-1h0-1c-1 0-1 0-1-1h-1-2l-1-1h-1-1c-1 0 1 0-1 0h-2c-1-1-1-1-2-1h-1-2c-3-1-5-1-8-1-2 0-4 0-5-1h-1-4-2c-1 0-3 0-4-1h0c-2 0-1 0-2-1v-1l-1-2v-1-2-2l-1-2v-2-2l-1-3v3c-1 2 0 3 0 4 1 2 0 4 1 6v5 3c1 1 2 3 3 3 1 1 2 1 3 1s0 0 1 1l1-1c1 0 2 1 4 1 2 1 3-1 5 1 1 0 3-1 5 0 1 0 2 0 3 1h4c1 0 2 0 3 1h3c1 0 2 0 3 1h2c1 0 2 0 3 1h3l14 4c5 2 11 5 16 9 5 3 10 7 15 12 1 1 3 3 4 5l2 2 1 1v1l2 2v1c2 3 4 6 6 10l1 3v1c-1 1-1 1-2 1h3c2 2 2 6 4 9h0l1 1v1 2c0 1-1 1-1 2-1 1-1 1-1 2l-1 1-3 3s0 1-1 1v1h0c-1 1-2 3-3 4l-1 2h-1v1l-2 2v1c-1 1 0 1-1 2h-1l-3 5v2h-1c-1 1-1 1-1 2s0 1-1 1v2h-1c-1 1 0 1-1 2 0 1-1 1-1 2l-1 2c-1 1-1 1-1 2l-1 1c0 1 0 1-1 1 0 1-1 2-1 3v1c-3 3-5 9-7 13-2 2-3 4-4 6 0 2 0 2-1 3 0 1 0 2-1 3v1l-1 1-1 2v1c0 1-1 1-1 2v1h0c-1 1-1 1-1 2v1h-1l-1 5h0v1h0 0l1-1c0-1 1-3 2-4 0-2 0-2 1-3l1-2v-2h1c0-1 1-3 1-4l3-6c1-1 1-2 2-3l5-11 6-10c0-2 1-3 2-4l1-2c1-1 1-2 2-2l2-4 4-7 3-4 4-5c1-1 0-1 1-2l1-1c0-1 1-1 1-2 2-1 0 1 1-1l2-1v-1c1-1 1-1 1-2l2-2h2v1c0 1 1 2 2 3v1 1c0 1 1 0 0 2h0l-1 1-6 6c-1 2-3 3-4 5l-1 1c-1 1-1 2-2 2 0 1-1 1-1 2-1 0-1 1-1 1l-2 2c0 1-1 1-1 2l-11 16-1 1s0 1-1 2-3 4-3 5v1l-1 1-1 1s-1 1-1 2l-13 26-1 2v-1l1-2-1-1c0 2-1 3-2 4v2h-1v-1l-1 1v2 1h-1v1 1 1h-1v1h0c-1 1 0 2-1 2v1 1 1c-1 1-1 1-1 2s-1 2-1 3v1c-1 1-1 1-1 2 0 2 0-1 0 2l-1 1v1 1c0 2 0-2 0 1l-1 2v1 2l-1 2v2h0v1c0 1-1 2-1 3v2c-1 1-1 3-1 4v4c-1 2 0 3-1 5v7c0 3 0 9-1 13-2-29 7-60 17-87l8-18v-1c0-2 1-3 2-5s0 1 1-1l2-5c1-1 1-2 2-3v-1l1-1c0-1 0-1 1-1v-1h0v-1c1-2 2-3 3-4v-1c-1-2-4-3-6-4-1 1-2 3-3 4-3 4-5 8-7 13 0 1-1 3-3 4v1l7-15 6-9c1-1 1-2 2-3l8-12-1-2c0 1 0 1-1 1-4-6-6-13-9-18-2-3-6-6-6-9l1-1h-1c1-2 2-2 4-3 4 1 7 3 10 6z" class="K"></path><defs><linearGradient id="Y" x1="276.771" y1="374.872" x2="238.564" y2="385.398" xlink:href="#B"><stop offset="0" stop-color="#565249"></stop><stop offset="1" stop-color="#787469"></stop></linearGradient></defs><path fill="url(#Y)" d="M271 358h1l3 6c-2 3-5 5-7 7-3 3-6 6-8 9-14 17-25 37-34 56l-1 2h-1c11-29 28-56 47-80z"></path><defs><linearGradient id="Z" x1="271.649" y1="354.317" x2="234.543" y2="382.965" xlink:href="#B"><stop offset="0" stop-color="#8c8976"></stop><stop offset="1" stop-color="#bfb39d"></stop></linearGradient></defs><path fill="url(#Z)" d="M266 345l3 7c-10 14-20 29-28 44l-6 11c-1 3-2 5-4 7 0-2 1-3 2-5s0 1 1-1l2-5c1-1 1-2 2-3v-1l1-1c0-1 0-1 1-1v-1h0v-1c1-2 2-3 3-4v-1c-1-2-4-3-6-4-1 1-2 3-3 4-3 4-5 8-7 13 0 1-1 3-3 4v1l7-15 6-9c1-1 1-2 2-3l8-12 19-24z"></path><path d="M231 340h-1c1-2 2-2 4-3 4 1 7 3 10 6 4 1 8 1 12 0 1 0 7-3 8-2 0 0 1 1 1 2l1 2-19 24-1-2c0 1 0 1-1 1-4-6-6-13-9-18-2-3-6-6-6-9l1-1z" class="J"></path><path d="M231 340c6 4 7 11 13 15v1c-1 0-2-1-4-1 0 2 2 4 3 5 1 2 2 5 3 7 0 1 0 1-1 1-4-6-6-13-9-18-2-3-6-6-6-9l1-1z" class="B"></path><path d="M224 408v-1c2-1 3-3 3-4 2-5 4-9 7-13 1-1 2-3 3-4 2 1 5 2 6 4v1c-1 1-2 2-3 4v1h0v1c-1 0-1 0-1 1l-1 1v1c-1 1-1 2-2 3l-2 5c-1 2 0-1-1 1s-2 3-2 5v1l-8 18c-10 27-19 58-17 87l1 14c1 2 1 4 3 6v8l1 1c1 8 5 17 10 25 0 1 1 3 2 4 5 10 14 19 22 27h0c10 8 22 16 34 21 27 11 56 11 82 24 2 1 5 2 7 3 6 4 12 8 17 13l-1 2h-4l-13-10-1-1c-3-3-21-9-25-9-3-1-6-2-9-2l-18-2c-29-3-54-15-75-34-20-18-34-41-39-67-2-10-3-19-4-29 0-10 1-19 2-28 3-21 9-41 17-60 2-6 5-12 9-18z" class="E"></path><path d="M368 653c6 4 12 8 17 13l-1 2h-4l-13-10 1-1c1 1 2 1 3 2 1 0 1 1 2 2 1 0 2 1 3 1v1c2 0 2 0 3-1-3-2-6-5-8-6s-2-1-3-2v-1z" class="L"></path><path d="M799 391c2-2 4-5 6-6 1 1 1 0 2 1v1c3 3 5 8 7 12 12 24 22 51 26 77l3 21c1 4 0 7 1 11 0 10-1 20-3 31-4 25-18 50-37 68-22 20-48 34-78 37-13 2-27 2-39 8-4 1-8 3-12 5-2 1-6 5-7 5l-2 1h-1-3c1-1 1-2 2-3h0c1-1 3-2 3-3 5-3 11-7 17-9 8-5 20-7 29-9 26-6 52-11 73-28a107.02 107.02 0 0 0 26-26c7-9 13-22 17-34v-2c11-36 2-78-10-113l-13-30c-3-5-5-10-7-15z" class="E"></path><path d="M799 391c2-2 4-5 6-6 1 1 1 0 2 1v1c3 3 5 8 7 12-3-2-4-7-6-10l-2 1c-2 1-3 3-5 4 2 3 6 9 5 12-3-5-5-10-7-15zM664 660c1-1 3-2 3-3 5-3 11-7 17-9v1c-3 3-12 4-13 8l2 1c0-1 1-1 2-1 0-1 1-2 2-2l3-1 1-1c2-1 4-2 6-1-4 1-8 3-12 5-2 1-6 5-7 5l-2 1h-1-3c1-1 1-2 2-3h0z" class="L"></path><defs><linearGradient id="a" x1="629.649" y1="849.004" x2="629.797" y2="770.48" xlink:href="#B"><stop offset="0" stop-color="#1b1819"></stop><stop offset="1" stop-color="#3c4343"></stop></linearGradient></defs><path fill="url(#a)" d="M621 752c5-3 9-2 15 0l2 1c6 4 9 8 12 15h0s-1 1-2 1v-1c-1 0-2 0-2 2v2c4 9 7 17 6 27-2 8-7 16-14 21-4 3-9 4-14 5h-2 0c1 1 1 1 2 1v1h-1-1c-4-1-8-3-12-5-2 3-3 4-2 8 1 2 5 5 7 6h1c5 3 13 4 19 4 11-1 20-6 27-14 9-11 11-23 9-37 2 2 2 2 3 4 1 3 3 5 5 7h-1c-1-1-2-1-2-3h-1v2-1h-1c0 2 0 5 1 7 0 2 0 5-1 7-2 11-5 20-13 27l-1 1c-9 7-20 11-32 10-6-1-12-3-18-5l-2-2c-1-2-2-4-4-7 0 2-1 3-1 4l-3 1h0l-5-7c-6-7-12-14-16-22-2-3-3-7-4-11 1-2 0-3 1-5s3-5 5-7l5 5 1-1 2 2h1c-1-1-1-2-2-3l-1-1h1c1 2 3 4 6 6h0c-2-2-4-4-5-6l-2-2c-1-3-1-6-1-9l2-6v3c0 1 1 2 1 3h0c1 2 2 4 3 5v1c2 3 6 5 9 7 1 1 2 1 3 2h-1c-1 0-2 0-3 1-1 0-2-1-3-1-1-1-2-1-2 0l-1 1h1 0 3 1c2 0 5 0 7-1l-3-2c-6-3-12-8-13-14-2-5 0-10 2-14 1 2 2 5 3 8h0c2 6 6 11 12 13 5 3 11 4 17 2 2-1 5-3 6-5 2-2 2-4 3-7 0-4-1-9-4-13-5-6-10-6-17-7 2-2 4-2 6-3l3-1z"></path><defs><linearGradient id="b" x1="592.568" y1="790.546" x2="592.405" y2="833.488" xlink:href="#B"><stop offset="0" stop-color="#46474a"></stop><stop offset="1" stop-color="#726f65"></stop></linearGradient></defs><path fill="url(#b)" d="M575 801c1-2 0-3 1-5s3-5 5-7l5 5c7 6 15 10 21 17 2 2 3 5 5 7-4-2-7-5-11-7-1 1-1 2-1 3 1 1 1 3 1 5-1 2 1 5 1 7s1 4 1 6c0 1 0 3 1 4 0 2-1 3-1 4l-3 1h0l-5-7c-6-7-12-14-16-22-2-3-3-7-4-11z"></path><defs><linearGradient id="c" x1="586.388" y1="800.487" x2="596.612" y2="833.513" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#3a3a3b"></stop></linearGradient></defs><path fill="url(#c)" d="M597 829l-13-17c-2-4-4-7-3-11h1c8 3 14 12 18 20v-7h0c1 1 1 3 1 5-1 2 1 5 1 7s1 4 1 6c0 1 0 3 1 4 0 2-1 3-1 4l-3 1h0l-5-7c1-1 2-2 2-3v-2z"></path><path d="M597 829c2 2 4 5 4 8 1 1 1 2 2 3l-3 1h0l-5-7c1-1 2-2 2-3v-2z" class="R"></path><path d="M621 752c5-3 9-2 15 0l2 1c6 4 9 8 12 15h0s-1 1-2 1v-1c-1 0-2 0-2 2v2c4 9 7 17 6 27-1 0-1 0-1 1h-1c-1-2-1-4-1-6-1-6-3-14-6-18-1-2-1-3-2-4v-1 2 2c1 7-2 17-7 23-6 7-17 8-25 8l1-1c5-4 10-6 15-9-3-1-5 0-8 0s-7 0-11-1l-3-2c-6-3-12-8-13-14-2-5 0-10 2-14 1 2 2 5 3 8h0c2 6 6 11 12 13 5 3 11 4 17 2 2-1 5-3 6-5 2-2 2-4 3-7 0-4-1-9-4-13-5-6-10-6-17-7 2-2 4-2 6-3l3-1z" class="B"></path><defs><linearGradient id="d" x1="634.498" y1="749.984" x2="635.659" y2="769.098" xlink:href="#B"><stop offset="0" stop-color="#84816f"></stop><stop offset="1" stop-color="#afa692"></stop></linearGradient></defs><path fill="url(#d)" d="M621 752c5-3 9-2 15 0l2 1c6 4 9 8 12 15h0s-1 1-2 1v-1c-1 0-2 0-2 2v2c-6-9-11-15-22-18-2-1-4 0-6-1l3-1z"></path><defs><linearGradient id="e" x1="606.341" y1="759.838" x2="622.65" y2="794.949" xlink:href="#B"><stop offset="0" stop-color="#4d4c4a"></stop><stop offset="1" stop-color="#68665c"></stop></linearGradient></defs><path fill="url(#e)" d="M630 783c2-2 2-4 3-7 0-4-1-9-4-13-5-6-10-6-17-7 2-2 4-2 6-3 2 1 4 0 6 1h-7l-4 1 7 1v1c6 1 13 5 16 10s3 9 1 15c-1 6-5 10-10 12l-2 2c-3-1-5 0-8 0s-7 0-11-1l-3-2c-6-3-12-8-13-14-2-5 0-10 2-14 1 2 2 5 3 8h0c2 6 6 11 12 13 5 3 11 4 17 2 2-1 5-3 6-5z"></path><path d="M730 497l9-6c6-5 10-11 15-17 2-4 4-7 6-10v-1h2c0 3-2 5-2 9 0 1 1 1 2 2 1 0 4 0 6-1 4-1 8-7 9-10l4-8 4 4c1 3 0 4-1 6-2 3-3 6-1 9 1 4 6 5 9 7 2 1 4 2 7 3 5 2 10 7 13 11 1 1 1 2 2 3s2 2 2 3c1 1 1 2 2 3h-1c-2-1-3-4-4-5-1 0-2-1-3-1v-1h-1l-1-1h0l-1-1c-1 0-1 0-2-1h-1v-1c-2-1 0 0-1 0-2-1-3-1-4-2l-1-1-1 1-1-1c-2-1-3-1-4-1-1-1-1-1-2-1h-1-1v-1h-3v-1c-1-1-1 0-2 0l-1-1c-1 0-3 0-4 1h0l-2 1c17 1 31 6 43 20 2 2 4 5 5 8v1 1c1 1 1 2 2 3-2-2-3-4-5-5l2 4c-3-1-7-2-10-3-2 2-4 3-6 5l5 1c-4 0-7-1-10 2-1 2-2 4-2 5l1 1c-3 1-4 2-5 4-1 0-1 2-1 3h0l2 1c-2 3-3 5-3 9h0c-1 4 1 10-2 12-4 2-7 2-10 5l-1 1-3 6-2 3c-2-1-3-3-3-5h-2s0-2-1-3l-2-7c-2-4-3-7-5-11l-33-51z" class="H"></path><path d="M758 511c2 0 4 2 6 3 0 1-1 3 0 5h-1c0-3-4-6-5-8z" class="F"></path><path d="M758 511c-2-2-5-7-7-9 0-2 0-3 1-5h0c1-1 2-2 2-3 2-2 3-3 6-3h0c-2 1-4 2-5 4l-1 4s0 1-1 2c2 3 6 7 10 7l3 1c0 2-1 3-2 5-2-1-4-3-6-3z" class="C"></path><path d="M779 527c2-5 4-10 8-14s9-6 14-7c8 0 14 3 20 9l2 4c-3-1-7-2-10-3-5-2-13-2-18 0h-1c-5 1-8 4-10 8-2 3-3 6-2 9 0 6 5 11 2 17-1 1-1 2-2 3h-3c-3-1-5-5-7-6-4-4-8-7-11-12 0-6 0-11 2-16h1c1 3 1 8 4 11h1c2-2 2-7 3-10l1 1c0 1 0 3 1 4 0 0 1 0 1 1 1 0 2 1 3 2l1-1z" class="B"></path><path d="M776 487c17 1 31 6 43 20 2 2 4 5 5 8v1 1c1 1 1 2 2 3-2-2-3-4-5-5-6-6-12-9-20-9-5 1-10 3-14 7s-6 9-8 14l-1 1c-1-1-2-2-3-2 0-1-1-1-1-1-1-1-1-3-1-4l-1-1c-1 3-1 8-3 10h-1c-3-3-3-8-4-11-1-2 0-4 0-5 1-2 2-3 2-5l-3-1c-4 0-8-4-10-7 1-1 1-2 1-2l1-4c1-2 3-3 5-4 1 1 1 2 2 2 1-1 1-2 2-2 3-4 7-4 12-4z" class="E"></path><path d="M784 500c5-1 10-1 15 0-9 3-18 8-23 17l-3 6 6 4-1 1c-1-1-2-2-3-2 0-1-1-1-1-1-1-1-1-3-1-4l-1-1c1-3 2-5 4-8h0c2-3 5-7 8-8l4-2v-1-1h-4z" class="C"></path><path d="M754 499l1-4c1-2 3-3 5-4 1 1 1 2 2 2 0 2 0 5 2 7h1c6 1 13 0 19 0h4v1c-8 0-15 4-22 8l-3-1c-4 0-8-4-10-7 1-1 1-2 1-2z" class="B"></path><path d="M754 499l1-4c1-2 3-3 5-4 1 1 1 2 2 2 0 2 0 5 2 7h1c-1 1-2 1-2 2-1 1-2 1-3 1-3 0-4-2-6-4z" class="D"></path><path d="M766 509c7-4 14-8 22-8v1l-4 2c-3 1-6 5-8 8h0c-2 3-3 5-4 8s-1 8-3 10h-1c-3-3-3-8-4-11-1-2 0-4 0-5 1-2 2-3 2-5z" class="P"></path><path d="M578 826c-2-3-4-6-5-9-3-8-2-15-1-23l1 4c1 1 1 1 1 2l1 1c1 4 2 8 4 11 4 8 10 15 16 22l5 7h0l3-1c0-1 1-2 1-4 2 3 3 5 4 7 2 9 4 16 3 25 0 4-1 8-2 13l-3 7v1c-2 3-5 7-8 9l-3 2-1 1-2 1c-3 2-6 3-10 4h0c-6 2-13 1-19 1l-1-1h-1-6l-2-2c-9-5-15-17-17-26v-1-2c-2-10 6-23 12-31l3-6c1-4 3-7 5-10 2-2 3-7 4-9 4-8 8-15 11-23v1h0v4h-1v9c1 2 1 3 1 4v1l1 1 1 2c0 2 2 7 4 8h1z" class="O"></path><defs><linearGradient id="f" x1="579.724" y1="905.709" x2="601.414" y2="890.181" xlink:href="#B"><stop offset="0" stop-color="#6d6960"></stop><stop offset="1" stop-color="#a6a08c"></stop></linearGradient></defs><path fill="url(#f)" d="M598 890v2c2 1 3 0 4-1l-1 2v1c-5 5-11 8-17 10s-11 2-17 2l14-3c7-3 12-7 17-13z"></path><path d="M558 871c-2-2-2-2-2-5 2 1 4 3 6 4 5 2 11 3 16 1 4-1 7-4 9-7v-1l1 1v1l-1 1c0 1-1 1-1 2-2 3-5 6-9 7-3 1-5 1-8 1h0c-4-1-7-3-10-4-1-1 0-1-1-1z" class="P"></path><defs><linearGradient id="g" x1="604.89" y1="891.361" x2="602.624" y2="872.256" xlink:href="#B"><stop offset="0" stop-color="#a09785"></stop><stop offset="1" stop-color="#d6cdb6"></stop></linearGradient></defs><path fill="url(#g)" d="M605 869v4c2-1 3-2 5-1h0v-2c0-1 1-1 1-2 0 4-1 8-2 13l-3 7c-1 2-3 5-5 6v-1l1-2c-1 1-2 2-4 1v-2c4-7 6-13 7-21z"></path><path d="M578 826c-2-3-4-6-5-9-3-8-2-15-1-23l1 4c1 1 1 1 1 2l1 1c1 4 2 8 4 11 4 8 10 15 16 22l5 7h0l3-1c0-1 1-2 1-4 2 3 3 5 4 7 2 9 4 16 3 25 0 1-1 1-1 2v2h0c-2-1-3 0-5 1v-4-4c-2 2-1 4-1 7h-1v2h0v-3-5c-1-4-2-7-3-11-3-7-8-12-13-17l-9-12z" class="B"></path><path d="M603 840c0-1 1-2 1-4 2 3 3 5 4 7 2 9 4 16 3 25 0 1-1 1-1 2v2h0c-2-1-3 0-5 1v-4-4c0-8-2-16-5-24l3-1z" class="E"></path><path d="M588 865h0c2-2 3-5 3-8 0 1 0 2 1 3v1 6c0 11-3 23-11 31-2 2-4 3-6 4h-1c-2 0-5-1-7-2-5-3-8-10-10-15-1-2-3-5-4-8 0-1 0-4 1-5h0c1-1 1-1 1-2 1 1 1 1 2 1h1c1 0 0 0 1 1 3 1 6 3 10 4h0c3 0 5 0 8-1 4-1 7-4 9-7 0-1 1-1 1-2l1-1z" class="B"></path><path d="M588 865h0c2-2 3-5 3-8 0 1 0 2 1 3v1 6l-1-4v1c0 8-6 16-11 21-2 1-3 2-5 2l-2-4h-1c-1 2-2 4-2 6-6-3-14-10-15-16l-1-1c1-1 1-1 1-2 1 1 1 1 2 1h1c1 0 0 0 1 1 3 1 6 3 10 4h0c3 0 5 0 8-1 4-1 7-4 9-7 0-1 1-1 1-2l1-1z" class="D"></path><defs><linearGradient id="h" x1="555.105" y1="840.954" x2="569.897" y2="901.341" xlink:href="#B"><stop offset="0" stop-color="#68665d"></stop><stop offset="1" stop-color="#827d6f"></stop></linearGradient></defs><path fill="url(#h)" d="M548 844c8-4 16-7 25-5 5 2 9 4 11 8 2 3 3 8 2 11s-2 5-4 6-3 1-5 1h0v-2h-1c0-1-1-1-2-2-3-3-6-3-10-3s-9 1-12 3-6 5-8 7c-1 1-1 2-2 3l-1-1c0 10 5 22 12 30 2 2 4 3 7 5l1 1h-6l-2-2c-9-5-15-17-17-26v-1-2c-2-10 6-23 12-31z"></path><path d="M541 870c0-7 3-13 7-17 5-5 11-8 18-8 4 0 8 2 11 5 2 3 2 5 2 8 0 2-1 4-2 5h-1c0-1-1-1-2-2-3-3-6-3-10-3s-9 1-12 3-6 5-8 7c-1 1-1 2-2 3l-1-1zM420 750l-2-1c4-1 9-1 13-1v1c5 0 8 3 10 7h1l1 1h0l1 1h1c0 1 0 1 1 1 1 1 1 3 1 4h0v1h-1v4l1-2c1 0 1 1 1 2 2 5 3 10 1 15-3 5-8 8-12 10l-1 1c-3 0-6 1-9 2-3 0-8-1-10 1 4 2 7 4 11 7-3 2-10 0-13 0-7-2-11-6-14-12-3-5-3-11-3-17-4 7-8 16-8 24-1 3 0 6 0 8v1l1 1 2 2c1 1 0 1 1 2 0 0 1 1 2 1v1l1 1 1 1c1 0 2 1 3 1 1 1 1 2 3 2 0 0 1 1 2 1 3 2 7 2 11 2v-1h2l2-1h3 0l4-3 1-1c1 0 1-1 2-1 1-2 3-3 4-4l1-1h1s1 1 0 2v4h0c0 2 0 4-1 5v2 1l-1 1v1h0c-1 1-1 1 0 2l-2 8-4 9c-5 1-10 3-15 4-12 3-22 0-31-7-4-2-6-4-9-7-10-13-10-28-9-44h1c0-1 1-1 2-2l1-7 1 1c1-2 1-5 2-7l5-13c1 0 2-1 3-1l1 1 6-3h1l-1 3v6 3c1-2 2-3 3-5 3-7 9-13 16-16 3-1 6-1 9-1h-4v-1h1c3 0 5 1 8 0z" class="B"></path><path d="M405 824c3 0 4 0 6 1 1 0 2 0 3-1 1 0 2 0 4 1 0 1 0 1 1 2 0-1 1-1 2-2 0 0 1 0 2-1h1c1-1 2-1 3-2h0c1-1 3-1 4 0 2 2 2 5 2 7 0 1-1 2-2 2h-4c2 0 3-1 4-2v-4c-3-1-4 2-6 2-1 0-3 0-4-1l-5 2c-7 2-15 0-22-3l3-1-1-1v-1c5 2 9 4 14 4h0-1c-1-1-2-1-4-2z" class="C"></path><path d="M421 826c2-1 6-4 9-4 1 1 2 2 2 4 0 1 0 2-1 3v-4c-3-1-4 2-6 2-1 0-3 0-4-1z" class="N"></path><defs><linearGradient id="i" x1="413.846" y1="749.234" x2="399.123" y2="768.243" xlink:href="#B"><stop offset="0" stop-color="#86826e"></stop><stop offset="1" stop-color="#a79f8e"></stop></linearGradient></defs><path fill="url(#i)" d="M415 751c3 1 8 3 10 5v1c-5-2-11-2-17 0-3 2-5 4-8 6l-2 2c-1-1-3-1-5-1v1l-3 3c3-7 9-13 16-16 3-1 6-1 9-1z"></path><defs><linearGradient id="j" x1="406.236" y1="810.073" x2="375.14" y2="824.544" xlink:href="#B"><stop offset="0" stop-color="#918a7c"></stop><stop offset="1" stop-color="#b2ab99"></stop></linearGradient></defs><path fill="url(#j)" d="M369 783l1 1c-1 4-1 11 0 16 1 12 7 22 17 30 6 5 16 9 24 8l5-1c1 0 3-1 4-1h3c1-1 2-1 3-1v1h0c-8 4-18 6-27 4h0c-11-3-19-9-25-18-7-10-8-21-6-32l1-7z"></path><defs><linearGradient id="k" x1="404.263" y1="789.903" x2="380.144" y2="796.665" xlink:href="#B"><stop offset="0" stop-color="#b2a995"></stop><stop offset="1" stop-color="#dfd7bf"></stop></linearGradient></defs><path fill="url(#k)" d="M390 768l3-3v-1c2 0 4 0 5 1-7 10-13 23-11 35 1 9 6 17 13 22 2 1 4 2 5 2 2 1 3 1 4 2h1 0c-5 0-9-2-14-4-4-3-7-6-10-10-7-10-7-23-5-35 1-1 1 0 1-1l1-1v1c1-1 1-1 1-2 1-1 1-2 2-2v2h0l1-1c1-2 2-3 3-5z"></path><path d="M417 797l-6-3c-5-2-8-6-9-11l-1-1c0-2-1-4-1-7 1-4 3-8 7-11 3-3 7-6 12-5-5 2-9 4-12 9-2 4-2 8-1 13 1 3 3 5 6 6l1 1c1 0 2 1 3 1 6 1 14-1 19-4l2-2c4-3 9-10 9-15l1-2c1 0 1 1 1 2 2 5 3 10 1 15-3 5-8 8-12 10l-1 1c-3 0-6 1-9 2-3 0-8-1-10 1z" class="S"></path><path d="M420 750l-2-1c4-1 9-1 13-1v1c5 0 8 3 10 7h1l1 1h0l1 1h1c0 1 0 1 1 1 1 1 1 3 1 4h0v1h-1v4c0 5-5 12-9 15l-2 2c-5 3-13 5-19 4-1 0-2-1-3-1l-3-3c-1-3 0-5 1-7v-1c1-2 3-4 5-5s6-1 8-1l1 1c3 0 6 2 9 4l1-3c1-6-1-10-4-14h-2c-1-1-3-1-4-2v-1c-2-2-7-4-10-5h-4v-1h1c3 0 5 1 8 0z" class="D"></path><path d="M425 782h6 0c-2 3-4 3-7 3-2 1-5 1-7 0v-1c2-2 6-2 8-2z" class="J"></path><path d="M415 751h-4v-1h1c3 0 5 1 8 0 4 2 8 5 11 9h-2c-1-1-3-1-4-2v-1c-2-2-7-4-10-5z" class="K"></path><path d="M442 756l1 1h0l1 1h1c0 1 0 1 1 1 1 1 1 3 1 4h0v1h-1v4c0 5-5 12-9 15l-2 2c0-1 6-7 6-8l1-1 1-2c2-6 1-12-1-18z" class="F"></path><defs><linearGradient id="l" x1="369.713" y1="786.847" x2="396.679" y2="793.34" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#2d2d2c"></stop></linearGradient></defs><path fill="url(#l)" d="M377 764c1 0 2-1 3-1l1 1 6-3h1l-1 3v6 3l-1 1h0v-2c-1 0-1 1-2 2 0 1 0 1-1 2v-1l-1 1c0 1 0 0-1 1-2 12-2 25 5 35 3 4 6 7 10 10v1l1 1-3 1c7 3 15 5 22 3l5-2c1 1 3 1 4 1 2 0 3-3 6-2v4c-1 1-2 2-4 2h4c-1 2-3 4-5 5v-1c-1 0-2 0-3 1h-3c-1 0-3 1-4 1l-5 1c-8 1-18-3-24-8-10-8-16-18-17-30-1-5-1-12 0-16 1-2 1-5 2-7l5-13z"></path><path d="M388 820c3 1 5 2 7 3h1l1 1-3 1c-1-1-2-1-3-2l-3-3z" class="H"></path><path d="M387 764v6 3l-1 1h0v-2c-1 0-1 1-2 2 0 1 0 1-1 2v-1l-1 1c0 1 0 0-1 1 1-5 3-9 6-13zm1 58l-1-1c-2-1-3-2-4-3-5-7-7-15-7-23 2 10 5 17 12 25l3 3c-1 0-2-1-3-1z" class="L"></path><path d="M382 822c-3-4-7-9-8-14-1-3-1-7-1-10h0c0 3 1 6 2 9 2 5 6 12 12 15h1c1 0 2 1 3 1 1 1 2 1 3 2 7 3 15 5 22 3l5-2c1 1 3 1 4 1 2 0 3-3 6-2v4c-1 1-2 2-4 2h4c-1 2-3 4-5 5v-1c-1 0-2 0-3 1h-3c-1 0-3 1-4 1l-1-1-3 1c-4 0-9-1-12-2-5-1-11-5-14-9-2 0-3-3-4-4z" class="N"></path><g class="Q"><path d="M382 822h3c1 1 2 3 4 4l3 3-1-1h-3l-2-2c-2 0-3-3-4-4z"></path><path d="M421 826c1 1 3 1 4 1 2 0 3-3 6-2v4c-1 1-2 2-4 2h4c-1 2-3 4-5 5v-1c-1 0-2 0-3 1h-3c-1 0-3 1-4 1l-1-1-3 1c-4 0-9-1-12-2-5-1-11-5-14-9l2 2h3l1 1c3 2 7 3 9 3h2 3l1 1h9 4v-1c1-1 1-2 0-3-1 0-2 0-3-1h-1l5-2z"></path></g><path d="M427 831h4c-1 2-3 4-5 5v-1c-1 0-2 0-3 1h-3c-1 0-3 1-4 1l-1-1c4-1 6-2 10-4 1 0 1 0 2-1z" class="U"></path><path d="M386 826l2 2h3l1 1c3 2 7 3 9 3l1 1h3c2 1 5 1 6 2h-5l-2-1c-2 0-3 0-4 1-5-1-11-5-14-9z" class="T"></path><path d="M163 482c6-1 10-1 15 2 3 2 5 4 7 6 4 6 8 13 9 19v3h1v-5l1-1c0 3-1 5 0 8 1 10 2 19 4 29 5 26 19 49 39 67 21 19 46 31 75 34l18 2c3 0 6 1 9 2v1h0c2 0 4 1 6 1l2 1c-1 0-1 0-2-1h-1c-2 0-3-1-5-1-1 0-2 0-3 1-9-1-16-1-24 3-1 1-2 1-3 2-2 3-4 5-5 9-7-2-14-3-20-5-7-3-14-6-20-9-3-2-6-4-10-5-1-1-2-1-3 0 6 10 13 18 26 21l3 1c1 0 2 1 3 2v1c-2 0-3 1-4 1h13l2 1h1l1 1c0 1 0 1-1 1-4 1-7 1-11 2l-7-1-12 1-2 1c-5 1-11 3-17 5h0-2v-1h-1l-1 1c-1 0-2 0-3-1h0l2-2 5-1v-1c-1-1-12-1-14-2-8-2-15-4-21-9-4-1-7-5-9-8-3-5-1-8 1-13 1-3 1-5 2-7 2-2 5-2 8-3v-7c1-2 1-5 2-7 0-2 1-3 2-5s4-4 6-6v-1c0-1-2-3-2-4l-6-8c-2-3-4-7-6-11-7-14-12-27-14-42-2-11-2-21-6-31-3-9-7-16-16-20l-1-1c-2-1-4-2-6-2s-4 0-6-1c0-3 1-5 1-7z" class="B"></path><path d="M296 672h1l1 1c0 1 0 1-1 1-4 1-7 1-11 2l-7-1h3v-1h-2v-1c5-2 11 1 16-1z" class="K"></path><path d="M248 678c6-1 13-1 19-2l-2 1c-5 1-11 3-17 5h0-2v-1h-1l-1 1c-1 0-2 0-3-1h0l2-2 5-1z" class="S"></path><path d="M311 655l-3 1c-2 0-6-1-8-1-16-4-30-10-42-19-7-4-13-10-19-16 3 1 7 5 9 7l7 5c3 2 6 4 10 6 14 8 32 13 49 15-1 1-2 1-3 2z" class="H"></path><path d="M225 610c1 1 2 1 3 1l-3 6c-2 3-2 6-3 9-2 8 2 18 6 24 6 10 14 15 24 20-12-3-23-6-33-13-2-1-5-3-7-5v-2c1-1 3-1 5-1h3c-2-5-4-9-5-14v-7c1-2 1-5 2-7 0-2 1-3 2-5s4-4 6-6z" class="P"></path><path d="M281 671c-12 0-24-2-35-9-10-6-19-16-21-27-1-4-1-8 2-11 1-2 2-3 4-3 8-1 9 11 13 16 2 3 6 5 9 8 6 10 13 18 26 21l3 1c1 0 2 1 3 2v1c-2 0-3 1-4 1z" class="Q"></path><defs><linearGradient id="m" x1="740.697" y1="734.164" x2="614.136" y2="751.7" xlink:href="#B"><stop offset="0" stop-color="#141112"></stop><stop offset="1" stop-color="#2e2f30"></stop></linearGradient></defs><path fill="url(#m)" d="M641 704c7-22 21-38 42-48 11-6 25-9 38-5 6 2 10 6 13 12 1 4 1 6-1 10 1 4-1 8-2 12-3-1-7-1-11-2-13 1-25 3-37 10 0-1 0-1 1-2-1-2-1-3-2-5-1-1-2-2-4-2-5 1-7 4-9 8-5 8-6 19-7 28 0 13 0 27 3 40 1 5 3 9 5 14-1-1 0-1-1 0 0 0 0 1 1 2v3 4c1 2 1 4 1 6 2 14 0 26-9 37-7 8-16 13-27 14-6 0-14-1-19-4h-1c-2-1-6-4-7-6-1-4 0-5 2-8 4 2 8 4 12 5h1 1v-1c-1 0-1 0-2-1h0 2c5-1 10-2 14-5 7-5 12-13 14-21 1-10-2-18-6-27v-2c0-2 1-2 2-2v1c1 0 2-1 2-1h0c-3-7-6-11-12-15v-1h1l1 1h0v-1-1l1 1v-1c-5-12-8-27-5-39h1c1-5 2-10 4-15 3-7 7-13 12-19l1-1c2-3 5-6 8-9h1c-3 3-6 6-8 9l-2 3c-1 0-2 1-2 2-1 1-1 1-1 2-1 1-3 3-3 5l-2 4-1 2s-1 1-1 2v1c-1 1-1 2-2 4v2h0z"></path><path d="M669 783l-3-12c-1-2-2-3-3-5 0-1 0-3-1-4v-1h1 0l1-2v2c1 3 2 7 4 11 1 2 1 4 2 6v1 4h-1z" class="H"></path><path d="M639 830c6-3 10-6 15-10 0 2-1 3-3 4 0 0-1 2-2 2 0 1 1 1 1 2l-5 3c-3-1-7 2-10 2-1 0-2 1-3 1h-3l-1-1c4 0 8-1 11-3z" class="T"></path><path d="M654 820l2-2c5-7 7-13 9-22v5c-2 11-5 20-15 26v1c0-1-1-1-1-2 1 0 2-2 2-2 2-1 3-2 3-4zm-8 2c10-7 14-17 17-28h0c-1 11-5 21-13 28l-5 4c0-2 0-3 1-4z" class="N"></path><path d="M665 801c1 0 1 0 2 1v1 3c-2 8-8 18-16 22l-6 3 5-3v-1c10-6 13-15 15-26z" class="K"></path><path d="M623 827c9 2 16-1 23-5-1 1-1 2-1 4-2 1-4 2-6 4-3 2-7 3-11 3l1 1h3c-1 1-3 1-4 1l-1-1h-5 0-1l-1 1h-1-4l2 1h-1-1c-2-1-6-4-7-6-1-4 0-5 2-8 4 2 8 4 12 5h1z" class="Q"></path><defs><linearGradient id="n" x1="639.829" y1="794.214" x2="656.554" y2="797.365" xlink:href="#B"><stop offset="0" stop-color="#c4b9a0"></stop><stop offset="1" stop-color="#e9e2c7"></stop></linearGradient></defs><path fill="url(#n)" d="M650 768l3 4c1-2 0-6 0-8 5 11 8 26 3 38-3 8-9 17-17 20-4 2-10 4-15 3h0c5-1 10-2 14-5 7-5 12-13 14-21 1-10-2-18-6-27v-2c0-2 1-2 2-2v1c1 0 2-1 2-1z"></path><path d="M680 680c6-4 16-8 23-7 8 1 10 9 17 10-13 1-25 3-37 10 0-1 0-1 1-2-1-2-1-3-2-5-1-1-2-2-4-2h1 0l-2-1h-1c1-1 3-2 4-3z" class="H"></path><path d="M678 684h1 0l-2-1h-1c1-1 3-2 4-3 0 1-1 2-1 2h0l1 1h3c0 1 1 1 1 2v2 3 1c-1-2-1-3-2-5-1-1-2-2-4-2z" class="C"></path><defs><linearGradient id="o" x1="613.595" y1="818.158" x2="672.4" y2="813.104" xlink:href="#B"><stop offset="0" stop-color="#928b75"></stop><stop offset="1" stop-color="#b2ab9a"></stop></linearGradient></defs><path fill="url(#o)" d="M669 783h1c1 2 1 4 1 6 2 14 0 26-9 37-7 8-16 13-27 14-6 0-14-1-19-4h1l-2-1h4 1l1-1h1 0 5l1 1c1 0 3 0 4-1 1 0 2-1 3-1 3 0 7-3 10-2l6-3c8-4 14-14 16-22 2-7 3-15 2-23z"></path><defs><linearGradient id="p" x1="653.472" y1="754.931" x2="707.513" y2="645.139" xlink:href="#B"><stop offset="0" stop-color="#7a756a"></stop><stop offset="1" stop-color="#9a9780"></stop></linearGradient></defs><path fill="url(#p)" d="M641 704c7-22 21-38 42-48 11-6 25-9 38-5 6 2 10 6 13 12 1 4 1 6-1 10-2 3-4 4-8 4-3 0-5 0-7-2-6-6-8-12-17-12-13-1-27 9-35 18-14 14-22 33-22 52 0 10 3 17 7 26-3-3-5-6-6-9-6-11-7-24-6-36l2-10z"></path><path d="M854 491c4-4 9-8 15-9l1-1c2 0 4 0 6 1 1 2 1 4 1 6-2 2-6 2-9 3-2 0-4 2-6 3h0a30.44 30.44 0 0 0-8 8c-5 7-6 13-8 21-1 1-1 1-1 2v1c-1 1 0 2-1 4v5 1c-1 0-1 0-1 1 1 3 0 5 0 8-3 15-8 29-15 43l-9 15c-1 1-3 4-4 6 0 0 1 0 1 1l1 1h0c3 4 5 7 7 12h0c1 4 1 8 1 12 3 1 7 1 9 3 1 3 1 5 1 7 1 2 2 4 3 7-2 8-6 12-13 16s-15 7-23 8l-11 1c3 1 6 2 8 4 6 2 11 5 16 6v2h-1l-4 1h5l3-1c2 0 4-2 6-1l-2 2h1 2v1c-4 3-13 5-17 5-7 0-15-7-22-10 2 6 4 12 2 18v1c-1 2-5 5-7 6-2 0-5 0-8-1-2-1-4-2-6-4 2-2 3-5 4-8-1-4-12-11-16-13-3-2-11-3-13-5v-2c2-4 7-1 10-1 1 0 1-1 2-1h1c0-1 0 0-1-1v-4l-7-1-2-1h0l2-1c0-1 4-1 6-2 4-1 8-2 12-4 7-2 14-6 18-12 2-3 4-6 7-8v-1c-1 0-2-1-4 0-5 3-9 7-13 9-12 7-26 10-39 13-3-6-7-10-13-12-13-4-27-1-38 5-21 10-35 26-42 48h0v-2c1-2 1-3 2-4v-1c0-1 1-2 1-2l1-2 2-4c0-2 2-4 3-5 0-1 0-1 1-2 0-1 1-2 2-2l2-3c2-3 5-6 8-9h-1c-3 3-6 6-8 9l-1 1c-5 6-9 12-12 19-2 5-3 10-4 15h-1c3-21 16-37 32-50 1 0 5-4 7-5 4-2 8-4 12-5 12-6 26-6 39-8 30-3 56-17 78-37 19-18 33-43 37-68 2-11 3-21 3-31v-5l1 1v8h1v-2c2-7 4-13 8-19z" class="B"></path><path d="M754 671l6 1-1 1c0 2 1 4 1 6l-5-3c0-1 0 0-1-1v-4z" class="R"></path><path d="M810 690c-6 0-13-6-19-8-5-2-10-4-14-7 4 0 10 2 14 2 3 1 6 2 8 4 6 2 11 5 16 6v2h-1l-4 1z" class="P"></path><path d="M799 621h1c-1 1-3 2-4 3-3 3-5 6-9 9l-16 11c-10 5-21 9-32 11-3 0-6 1-9 0-1 0-2-1-2-2 6-2 12-2 18-4 10-3 21-7 30-12 8-5 16-10 23-16z" class="H"></path><path d="M760 672c10 1 18 3 24 12 2 3 2 7 1 10 0 2-1 3-2 4-2 1-5 2-7 1s-2-2-3-4c-3-6-6-12-13-16 0-2-1-4-1-6l1-1z" class="Q"></path><path d="M825 635c-1 5-3 10-5 14l9 1c-10 12-25 16-40 18 8-4 14-8 20-15 5-7 10-17 9-27h0c-1-3-1-5-2-8-1-2-4-6-4-7 2-1 3 0 5 0 3 4 5 7 7 12h0c1 4 1 8 1 12z" class="J"></path><path d="M790 641c2-1 5-3 6-5 3-4 4-16 11-15 3 0 4 1 6 4 2 2 2 5 2 8-2 9-9 18-16 24-16 11-34 11-52 11 0-1 4-1 6-2 4-1 8-2 12-4 7-2 14-6 18-12 2-3 4-6 7-8v-1z" class="Q"></path><defs><linearGradient id="q" x1="243.585" y1="462.804" x2="295.593" y2="549.953" xlink:href="#B"><stop offset="0" stop-color="#2b2c2c"></stop><stop offset="1" stop-color="#3e4647"></stop></linearGradient></defs><path fill="url(#q)" d="M256 469c0-1-1-2-1-3l-3-3-1-1c-1-3 4-6 5-8h-1-1-2c1 0 1-1 2-1h1c1-1 3-1 4 0v2l1 1 1 2v1c1 2 1 3 3 5h0c1 2 2 2 2 3l1 1 1 1c1 2 2 4 4 5h1c1 1 2 1 4 1l3-3c0-2-1-3-1-4-1-2-1-2-2-3s-1-1-1-2c-1 0-2 0-2-1l-1-1c2 1 4 1 7 0 0 2 1 3 2 5 6 7 11 14 18 19 5 4 10 5 14 9h0 0l1 1c-4 0-5 1-7 4s-10 16-12 17c-1-1 0-1-1 0-2 1-3 3-4 5-1 1-2 2-3 4-2 2-6 5-7 8l-3 3c0 1-1 1-1 2l-1 1c-1 1-2 2-2 4-1 3-3 7-4 10-3 6-4 12-7 18-1 0-3 0-4-1h0c1-1 1-3 0-4h0v-1c-1-1-2-1-3-2v1l-1 1c-6 1-11 0-15 6-2 3-2 8-1 12 0 3 2 7 4 10 1 3 4 7 6 10 1 1 2 3 2 4l-2-2h-2c-1-1-1-1-2 0h0c-8-8-17-17-22-27-1-1-2-3-2-4-5-8-9-17-10-25l-1-1v-8c-2-2-2-4-3-6 1-3 2-7 2-10v1h1v-1c0-1 0-2 1-3h0c0-2 0-5 1-7 0-3 1-7 2-10l2-5c5-15 18-23 32-28 1-1 2-1 4-2h4z"></path><path d="M239 530c1 0 3 0 4 2 1 1 1 1 1 2-1 2-2 3-4 3h-1l-3-2v-1l1-2c0-1 1-1 2-2z" class="N"></path><path d="M237 532c2 1 2 1 3 3 0 1 0 1-1 1v1l-3-2v-1l1-2z" class="T"></path><path d="M256 469c2 2 2 4 1 7-2 4-16 8-21 10-5 3-9 7-12 11-2 2-3 4-4 6 1 0 2 1 3 1-5 5-9 10-12 17h0c0-2 0-5 1-7 0-3 1-7 2-10l2-5c5-15 18-23 32-28 1-1 2-1 4-2h4zm-41 51c4-6 9-11 16-13 6-1 12-1 17 3 6 3 9 9 11 16 0 1 0 2 1 3h2l3-3 1-1c0-1 1-2 1-3l1 5c1 2 1 4 2 5h1c4-5 4-9 5-14l1 2c1 4 2 7 1 11v1c-1 2-1 4-3 6l-2 3c-3 3-10 12-14 12-2-2-4-4-4-7 0-1 0-2 1-3 1-4 2-9 1-13s-3-9-7-12c-8-5-18-3-27-1-3 1-5 2-8 3z" class="B"></path><path d="M223 504l8-7c8-5 16-8 25-9 4-1 9-1 13-1 3 1 5 2 7 5 2 0 4 1 6 3h1c2 1 4 4 5 7 0 3-2 4-3 6l-7 6-3 3v1 4l-1 1v2l-1 1v2l-3 3v-1c0-1-1-2-2-3l-1-5c0 1-1 2-1 3l-1 1-3 3h-2c-1-1-1-2-1-3-2-7-5-13-11-16-5-4-11-4-17-3-7 2-12 7-16 13-1 1-2 2-3 2l-1-1c3-7 7-12 12-17z" class="E"></path><path d="M259 526l7-5-1-3c-5-9-15-15-24-18 4 0 8-1 12 0l-1 1 1 1h0c5 2 9 7 10 11 2 3 3 6 4 9 0 1-1 2-1 3l-1 1-3 3h-2c-1-1-1-2-1-3z" class="K"></path><path d="M276 492c2 0 4 1 6 3h1c2 1 4 4 5 7 0 3-2 4-3 6l-7 6-3 3v1 4l-1 1v2l-1 1v2l-3 3v-1c0-1-1-2-2-3l-1-5c-1-3-2-6-4-9-1-4-5-9-10-11h0l-1-1 1-1c7 0 14 1 21-1l1-1c2-1 1-4 1-6z" class="F"></path><path d="M276 492c2 0 4 1 6 3h1c0 1 1 2 1 4 0 1 0 2-1 3-2 1-3 1-4 0-2 0-3-1-5-2v-1l1-1c2-1 1-4 1-6z" class="H"></path><path d="M252 501c8 1 12 2 18 7 1 1 2 2 4 1l1 1-1 1c0 2 1 4 1 6v1 4l-1 1v2l-1 1v2l-3 3v-1c0-1-1-2-2-3l-1-5c-1-3-2-6-4-9-1-4-5-9-10-11h0l-1-1z" class="P"></path><defs><linearGradient id="r" x1="247.127" y1="550.712" x2="226.982" y2="594.38" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#353434"></stop></linearGradient></defs><path fill="url(#r)" d="M223 517c4 0 6 0 9 3v1h-2c3 1 4 1 5 3 2 2 3 4 4 6-1 1-2 1-2 2l-1 2v1l3 2h1c2 2 5 3 6 6v4 1 1c-1 3 0 7 2 10 2 1 3 2 6 3l-1 1c1 0 2 0 3 1l-1 1c-6 1-11 0-15 6-2 3-2 8-1 12 0 3 2 7 4 10 1 3 4 7 6 10 1 1 2 3 2 4l-2-2h-2c-1-1-1-1-2 0h0c-8-8-17-17-22-27-1-1-2-3-2-4-5-8-9-17-10-25l-1-1v-8c-2-2-2-4-3-6 1-3 2-7 2-10v1h1v-1c0-1 0-2 1-3h0 0l1 1c1 0 2-1 3-2 3-1 5-2 8-3z"></path><path d="M249 605c-4-3-7-6-9-9-5-7-9-14-7-22 0-4 2-8 6-10 4-3 9-2 14-1 1 0 2 0 3 1l-1 1c-6 1-11 0-15 6-2 3-2 8-1 12 0 3 2 7 4 10 1 3 4 7 6 10 1 1 2 3 2 4l-2-2z" class="E"></path><defs><linearGradient id="s" x1="236.417" y1="566.847" x2="224.657" y2="538.51" xlink:href="#B"><stop offset="0" stop-color="#cac2ac"></stop><stop offset="1" stop-color="#f3ebd1"></stop></linearGradient></defs><path fill="url(#s)" d="M236 535l3 2h1c2 2 5 3 6 6v4c-3 2-6 6-9 7h0c0-2 1-4 0-5-1-2-3-2-5-2s-3 0-4 1c-3 3-4 9-4 13 0 6 2 12 3 18h0l-2-2c-2-4-3-8-4-13l-2-1c0-2 0-1-1-2-1 0-1-2-1-3 0-2 0-5 1-8 0-2 2-4 4-5 1-2 2-5 4-7 3-3 6-3 10-3z"></path><path d="M217 558c0-2 0-5 1-8 0-2 2-4 4-5-2 6-2 12-1 19l-2-1c0-2 0-1-1-2-1 0-1-2-1-3z" class="D"></path><defs><linearGradient id="t" x1="218.69" y1="524.753" x2="236.664" y2="574.066" xlink:href="#B"><stop offset="0" stop-color="#131112"></stop><stop offset="1" stop-color="#313030"></stop></linearGradient></defs><path fill="url(#t)" d="M223 517c4 0 6 0 9 3v1h-2c3 1 4 1 5 3 2 2 3 4 4 6-1 1-2 1-2 2l-1 2v1c-4 0-7 0-10 3-2 2-3 5-4 7-2 1-4 3-4 5-1 3-1 6-1 8 0 1 0 3 1 3 1 1 1 0 1 2l2 1c1 5 2 9 4 13v1 1l-1-1h-1c-1-1-2-3-2-4-5-8-9-17-10-25l-1-1v-8c-2-2-2-4-3-6 1-3 2-7 2-10v1h1v-1c0-1 0-2 1-3h0 0l1 1c1 0 2-1 3-2 3-1 5-2 8-3z"></path><path d="M217 558c-1-2-1-3-1-5 1-3 0-7 2-11 1-4 5-8 9-10 3-1 6 1 9 2v1c-4 0-7 0-10 3-2 2-3 5-4 7-2 1-4 3-4 5-1 3-1 6-1 8z" class="C"></path><path d="M223 517c4 0 6 0 9 3v1h-2c-5 0-10 2-14 6-3 4-5 8-6 13-2-2-2-4-3-6 1-3 2-7 2-10v1h1v-1c0-1 0-2 1-3h0 0l1 1c1 0 2-1 3-2 3-1 5-2 8-3z" class="G"></path><defs><linearGradient id="u" x1="650.692" y1="587.207" x2="726.662" y2="725.381" xlink:href="#B"><stop offset="0" stop-color="#0a0607"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#u)" d="M812 522c4 1 8 3 11 7 3 3 4 8 5 13 0 3 0 6 1 9-4 12-10 25-17 34a107.02 107.02 0 0 1-26 26c-21 17-47 22-73 28-9 2-21 4-29 9-6 2-12 6-17 9 0 1-2 2-3 3h0c-1 1-1 2-2 3h3 1l2-1c-16 13-29 29-32 50-3 12 0 27 5 39v1l-1-1v1 1h0l-1-1h-1v1l-2-1c-6-2-10-3-15 0l-3 1c-2 1-4 1-6 3 7 1 12 1 17 7 3 4 4 9 4 13-1 3-1 5-3 7-1 2-4 4-6 5-6 2-12 1-17-2-6-2-10-7-12-13h1c-1-4-2-6-1-10v-2c1-1 1 0 1-1l1-2v-1-1c2-3 7-6 10-7h1 2l1-1h3c1-1 1-1 3-1h1c1-1 3 0 5 0v-1h2 1c-7-7-14-23-13-34 1-3 3-7 2-10 2-1 3-3 4-5 4-7 6-14 9-21h1l-4 9 1 2h1c1-1 1-3 2-3v-1-1-1l5-9 4-7c2-2 2-3 4-4 8-10 16-19 26-27 6-4 12-8 18-11 1 0 1 0 2 1h0 1c1-1 3-2 5-3 3-3 7-5 10-7 12-9 19-20 21-35l1-2h1c2 0 4-1 5-2l-1-2c-1-7-5-12-8-18l-3-9 5-5v2l-3 4c12 3 24 7 30 18l3 7c0 3 0 4-2 7-1 1-3 2-5 3l-1 6c0 1 0 2-1 3l-1-1c-1 1-1 2-2 3 0 2 0 2 1 3s1 0 1 1c3 4 5 6 10 7 1 1 2 0 3 0h1c2 0 4-2 6-3h0c1-2 1-3 1-5 2-2 3-3 4-5 4-6 3-15 1-22h2c0 2 1 4 3 5l2-3 3-6 1-1c3-3 6-3 10-5 3-2 1-8 2-12h0c0-4 1-6 3-9l-2-1h0c0-1 0-3 1-3 1-2 2-3 5-4l-1-1c0-1 1-3 2-5 3-3 6-2 10-2z"></path><path d="M743 593c0-3-2-8 0-10 1-1 2-1 4 0 0 2-1 5-2 7-1 1-1 2-2 3zM607 781h0c1 0 2 1 3 1 4 2 10 2 14 0 1 0 2-1 3-3 1 1 0 1 1 2l-1 1h0l-1 1c-2 3-6 4-9 4-2 0-4 0-5-2h-1c-1 0-3 0-4-1v-3z" class="P"></path><path d="M735 581c1-2 0-6 0-8-1-2 0-5 2-6 1-2 3-3 6-3 2 0 4 1 5 3 2 1 2 2 2 4s-8 4-11 6v4l-2 2h-1c0-1 0-1-1-2z" class="E"></path><path d="M739 581c1 11 1 24-6 34-5 7-11 13-19 14h-1 1c11-8 18-18 21-32 1-6 1-11 0-16 1 1 1 1 1 2h1l2-2z" class="N"></path><path d="M744 596c1 1 1 0 1 1 3 4 5 6 10 7 1 1 2 0 3 0h1c2 0 4-2 6-3-4 7-8 13-15 17-4 1-6 5-9 6v1l-7 2c1-2 4-5 5-8 5-7 7-14 5-23z" class="D"></path><defs><linearGradient id="v" x1="622.158" y1="719.305" x2="676.903" y2="684.708" xlink:href="#B"><stop offset="0" stop-color="#6c695d"></stop><stop offset="1" stop-color="#a49f8f"></stop></linearGradient></defs><path fill="url(#v)" d="M640 751l-4-9-3-9c-2-7-2-14-1-21l1-5c4-15 11-28 22-39 3-3 6-6 9-8h0c-1 1-1 2-2 3h3 1l2-1c-16 13-29 29-32 50-3 12 0 27 5 39v1l-1-1z"></path><defs><linearGradient id="w" x1="765.565" y1="616.633" x2="787.465" y2="580.385" xlink:href="#B"><stop offset="0" stop-color="#a59c8b"></stop><stop offset="1" stop-color="#dbd2b9"></stop></linearGradient></defs><path fill="url(#w)" d="M783 578c1 0 2 0 3 1 1 0 1 0 2 1 2 3 1 7 1 10-3 12-13 23-23 30l-6 3c8-6 14-14 14-24 1-6 0-9 3-14 1-3 3-6 6-7z"></path><defs><linearGradient id="x" x1="707.06" y1="587.507" x2="719.034" y2="610.17" xlink:href="#B"><stop offset="0" stop-color="#b3a893"></stop><stop offset="1" stop-color="#e1d8bd"></stop></linearGradient></defs><path fill="url(#x)" d="M732 575c1 10 0 19-6 28-9 12-23 18-37 21 1-1 3-2 5-3 3-3 7-5 10-7 12-9 19-20 21-35l1-2h1c2 0 4-1 5-2z"></path><path d="M629 676l-4 9 1 2h1c1-1 1-3 2-3v-1-1-1l5-9 4-7c2-2 2-3 4-4-14 21-23 46-18 72 1 4 3 8 5 13h-3 0c-7-7-14-23-13-34 1-3 3-7 2-10 2-1 3-3 4-5 4-7 6-14 9-21h1z" class="P"></path><path d="M607 781c-1-1-2-1-3-2v-1c-1-1-1-2-1-3h0c-1-2-1-4-1-5 1-3 0 0 0-2 1-1 0-1 1-1v-1c0-1 1-3 2-4v-1s1-1 1-2h1c1-2 2-3 3-4h1c1-1 1-2 2-2h1c1-1 2-2 4-2h1l1-1 1 1v1l-3 1c-2 1-4 1-6 3 7 1 12 1 17 7 3 4 4 9 4 13-1 3-1 5-3 7l-1 1h-1l-3 3h0c-2 0-3 1-5 1h0c-1 1-3 0-4 0 3 0 7-1 9-3 2-1 2-1 2-3l1-1c-1-1 0-1-1-2-1 2-2 3-3 3-4 2-10 2-14 0-1 0-2-1-3-1h0z" class="B"></path><path d="M607 781c-1-1-2-1-3-2v-1c-1-1-1-2-1-3h0c-1-2-1-4-1-5 1-3 0 0 0-2 1-1 0-1 1-1v-1c0-1 1-3 2-4v-1s1-1 1-2h1c1-2 2-3 3-4h1c1-1 1-2 2-2h1c1-1 2-2 4-2h1l1-1 1 1c-7 1-12 6-16 12-1 1-1 2-2 4v1c0 3 0 7 1 9 1 1 1 1 2 1 0-1-1-2 0-4 3-3 6-4 9-4 4 0 8 1 10 3s3 4 3 6v2c-1-1 0-1-1-2-1 2-2 3-3 3-4 2-10 2-14 0-1 0-2-1-3-1h0z" class="O"></path><defs><linearGradient id="y" x1="786.698" y1="571.979" x2="811.345" y2="573.779" xlink:href="#B"><stop offset="0" stop-color="#0a0809"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#y)" d="M800 551c2-2 4-5 8-5 1 0 3 0 4 2 4 4 4 10 3 15 0 6-1 11-3 16l1 1c0 1-1 2-1 3v1 1a107.02 107.02 0 0 1-26 26h0c0-2 0-2 1-3 1-2 3-4 4-6 5-7 10-15 10-24 1-3 0-6-2-8-3-3-8-4-12-4-1 0-2 0-3-1h-1s-1 0-1-1c3-3 6-3 10-5 3-2 1-8 2-12h0c2 1 4 3 6 4h0z"></path><path d="M813 557l1 6h1c0 6-1 11-3 16h0-1 0c-1-4 1-8 2-11v-11z" class="C"></path><path d="M800 551c2-2 4-5 8-5 1 0 3 0 4 2 4 4 4 10 3 15h-1l-1-6s0-1-1-2c-1-2-3-1-4-2-3 1 0 3-2 4v-3l-5-2-1-1z" class="F"></path><path d="M783 565c3-2 7-4 11-3 3 0 7 1 9 4 4 4 4 9 4 15-1 9-8 17-15 23l-5 4c1-2 3-4 4-6 5-7 10-15 10-24 1-3 0-6-2-8-3-3-8-4-12-4-1 0-2 0-3-1h-1z" class="E"></path><defs><linearGradient id="z" x1="820.559" y1="526.013" x2="799.371" y2="572.887" xlink:href="#B"><stop offset="0" stop-color="#131212"></stop><stop offset="1" stop-color="#313031"></stop></linearGradient></defs><path fill="url(#z)" d="M812 522c4 1 8 3 11 7 3 3 4 8 5 13 0 3 0 6 1 9-4 12-10 25-17 34v-1-1c0-1 1-2 1-3l-1-1c2-5 3-10 3-16 1-5 1-11-3-15-1-2-3-2-4-2-4 0-6 3-8 5h0c-2-1-4-3-6-4 0-4 1-6 3-9l-2-1h0c0-1 0-3 1-3 1-2 2-3 5-4l-1-1c0-1 1-3 2-5 3-3 6-2 10-2z"></path><path d="M797 538l-2-1h0c0-1 0-3 1-3 1-2 2-3 5-4v1l3 3c1 1 2 1 4 1 4 1 7 4 9 8 5 10 1 22-3 32 0 2-1 3-1 5l-1-1c2-5 3-10 3-16 1-5 1-11-3-15-1-2-3-2-4-2-4 0-6 3-8 5h0c-2-1-4-3-6-4 0-4 1-6 3-9z" class="E"></path><path d="M797 538l-2-1h0c0-1 0-3 1-3 1-2 2-3 5-4v1l3 3c1 1 2 1 4 1-5 0-7 1-11 3z" class="Q"></path><path d="M720 683c4 1 8 1 11 2 17 7 28 18 34 34 4 10 7 21 4 31 2 2 2 3 1 5 0 5-3 9-5 14v1l1-1c1 2-2 12-3 15-2 6-6 12-10 18-1 0-2 1-2 1l-6 6c-6 4-13 6-20 8-12 1-26-2-36-9-3-2-6-5-10-7v-1c-2-2-4-4-5-7-1-2-1-2-3-4 0-2 0-4-1-6v-4-3c-1-1-1-2-1-2 1-1 0-1 1 0-2-5-4-9-5-14-3-13-3-27-3-40 1-9 2-20 7-28 2-4 4-7 9-8 2 0 3 1 4 2 1 2 1 3 2 5-1 1-1 1-1 2 12-7 24-9 37-10z" class="E"></path><g class="B"><path d="M765 770l1-1c1 2-2 12-3 15-2 6-6 12-10 18-1 0-2 1-2 1l-6 6c-6 4-13 6-20 8-12 1-26-2-36-9-3-2-6-5-10-7v-1c-2-2-4-4-5-7-1-2-1-2-3-4 0-2 0-4-1-6v-4-3c-1-1-1-2-1-2 1-1 0-1 1 0 2 3 4 7 6 11 6 9 16 16 26 19 11 4 25 4 36-2 11-5 20-16 24-27l3-5z"></path><path d="M668 741c1-12 10-26 19-35 8-6 17-11 26-13 11-2 23 0 32 6 11 7 18 20 21 33 1 5 1 10-1 15-1-2 0-6-1-8 0-3-1-5-2-7 0-3-1-5-3-7-4-8-8-12-17-15-7-2-17-1-24 3s-11 11-13 19c-2 9 1 20 6 28 4 6 9 10 16 13 5 1 9 2 14 2h1c2-1 4-1 6-2 10-4 17-13 21-23 2 2 2 3 1 5 0 5-3 9-5 14v1l-3 5c-1 0-1 1-2 1-9 10-17 17-31 20h-1-1c-1-1-4-2-6-3s-5-3-7-6c-4-4-4-9-3-15v-2c-2-1-3-4-4-6-6-10-6-25-2-36 2-7 8-14 14-17 11-5 20-3 31 1-3-3-11-7-15-8h-1c-7-2-15-2-22 0-12 2-25 11-32 21-5 8-8 15-11 24l-1-8z"></path></g><path d="M765 769v1l-3 5c-1 0-1 1-2 1-2 0-5 3-6 4-5 5-16 6-23 5h0l-4-1c-7-1-13-6-16-12v-2c5 0 11 6 15 8 6 2 11 3 17 3 9 0 16-5 22-12z" class="Q"></path><path d="M711 772c3 6 9 11 16 12l4 1h0c7 1 18 0 23-5 1-1 4-4 6-4-9 10-17 17-31 20h-1-1c-1-1-4-2-6-3s-5-3-7-6c-4-4-4-9-3-15z" class="B"></path><path d="M668 741l1 8c3-9 6-16 11-24 7-10 20-19 32-21 7-2 15-2 22 0h1c4 1 12 5 15 8-11-4-20-6-31-1-6 3-12 10-14 17-4 11-4 26 2 36 1 2 2 5 4 6v2c-1 6-1 11 3 15 2 3 5 5 7 6s5 2 6 3h1 1l-2 1c-16 1-27-5-33-20-2-4-3-8-7-11-1-1-4-1-5-1-2 1-3 1-4 3-2 4-1 8 0 12-7-11-12-25-10-39z" class="H"></path><path d="M727 773c-7-3-12-7-16-13-5-8-8-19-6-28 2-8 6-15 13-19s17-5 24-3c9 3 13 7 17 15 2 2 3 4 3 7 1 2 2 4 2 7 1 2 0 6 1 8-1 7-7 14-13 18s-13 6-21 4h-1c-1 1-2 3-3 4z" class="Q"></path><path d="M716 757c-1-3-2-7-2-10 0-4 1-7 4-9 4-5 8-5 14-5 3-1 6-1 8-3s2-3 2-5c-1-3-6-3-9-5v-1l2-2c3-2 6-3 10-2 6 1 10 5 13 10l1 1c0 2 2 3 2 6-6-4-10-6-17-8-1 1-1 3 0 5 0 2 1 5-1 8s-5 5-8 6c-5 1-13 0-16 4-2 2-2 6-2 8v1l-1 1z" class="B"></path><path d="M716 757l1-1v-1c0-2 0-6 2-8 3-4 11-3 16-4 3-1 6-3 8-6s1-6 1-8c-1-2-1-4 0-5 7 2 11 4 17 8 0-3-2-4-2-6l-1-1h1c2 2 3 4 3 7 1 2 2 4 2 7 1 2 0 6 1 8-1 7-7 14-13 18s-13 6-21 4h-1c-6-3-11-6-14-12z" class="H"></path><defs><linearGradient id="AA" x1="331.882" y1="814.564" x2="284.275" y2="635.804" xlink:href="#B"><stop offset="0" stop-color="#141112"></stop><stop offset="1" stop-color="#424949"></stop></linearGradient></defs><path fill="url(#AA)" d="M253 645c1-1 2-1 3 0 4 1 7 3 10 5 6 3 13 6 20 9 6 2 13 3 20 5 1-4 3-6 5-9 1-1 2-1 3-2 8-4 15-4 24-3 1-1 2-1 3-1 2 0 3 1 5 1h1c1 1 1 1 2 1l-2-1c-2 0-4-1-6-1h0v-1c4 0 22 6 25 9l1 1 13 10h4l1-2c14 14 24 32 25 53 0 7-1 14-3 21v2 1c0 1 0 1-1 2-1 0-1 2-2 3 0 1-1 1-1 2-1 0-1 1-1 1 0 1-1 1-2 2v1h0 1c1-1 3-2 5-2h0c-7 3-13 9-16 16-1 2-2 3-3 5v-3-6l1-3h-1l-6 3-1-1c-1 0-2 1-3 1l-5 13c-1 2-1 5-2 7l-1-1-1 7c-1 1-2 1-2 2h-1c-2 2-3 5-5 8-3 3-6 5-9 7-10 8-25 11-37 9h-1-1c-12-3-23-9-30-19-3-5-5-10-7-15-1-3-1-6-2-7s-2-2-3-2c-1-9-4-16-6-24-1-5-2-11-2-16 0-1 0-1-1-1v-2h1v-2c-1 0-1 0-2 2h-1c-1 2 0 0 0 2h-1c-1 1-1 1-1 2v2l-1-1c1-3 3-6 5-8l2-3v-1c-2 1-3 4-5 5-1 1-1 3-3 3l16-22c0-1 1-2 2-3 0-2-3-5-4-7 2-2 4-3 5-4 5-3 11-6 16-8 3-1 5-1 8-2 1-2-1-2-2-3v-1c-4-1-7-1-10 0v-3c1-1 1-1 0-2 4-1 7-1 11-2 1 0 1 0 1-1l-1-1h-1l-2-1h-13c1 0 2-1 4-1v-1c-1-1-2-2-3-2l-3-1c-13-3-20-11-26-21z"></path><path d="M282 667c4 1 9 1 13 2v1l-1 1h-13c1 0 2-1 4-1v-1c-1-1-2-2-3-2z" class="S"></path><path d="M376 761c0 1 1 2 1 3l-5 13c-1 2-1 5-2 7l-1-1c0-2 1-5 2-8l5-14z" class="H"></path><path d="M278 777h0l-4-9c6 5 10 10 18 12 10 3 18 1 26-6 3-2 5-4 8-6h1c1 1 1 2 1 4-2 6-7 10-13 12-9 4-19 2-28-3-1 0-3-1-4-2l-1-1h-1s-1-1-2-1h-1z" class="Q"></path><path d="M327 768v-1l1 1c0 1 0 1 1 2 1-2 2-3 3-4v-1l1 1-1 1c0 1-2 2-3 4 0 1 1 5 1 7-1 8-9 15-16 17v1c3 1 8 0 11-1h1c-4 2-8 2-13 2-13-1-23-10-32-19h1l1 1c1 1 3 2 4 2 9 5 19 7 28 3 6-2 11-6 13-12 0-2 0-3-1-4z" class="B"></path><path d="M320 683c1 0 2-1 2-2 3-2 5-4 7-6 5-3 10-2 15-1s15 4 18 8c4 2 6 4 9 8 6 12 7 24 7 37 0 11 0 21-2 31-6 18-16 35-33 43-11 6-25 7-37 3-10-3-20-11-25-21l-3-6h1c1 0 2 1 2 1 9 9 19 18 32 19 5 0 9 0 13-2 2 0 3-1 4-1 9-5 13-13 16-23l-1-1v-1c1-1 1 0 1-1 1-2 2-3 5-4h1v-1c2 0 3 0 5 1 1 0 2 0 3 2h1c1 2 1 4 2 6 0 1 1 2 1 3 5-9 8-18 8-28 0-13-6-26-14-36-9-10-23-18-37-19-11-1-22 3-30 10-10 9-17 22-18 35-1 3 0 8 1 11h0c2 8 8 14 15 18 5 3 13 5 19 3l4-2h4c0 1 1 1 1 2v1c-1 1-1 0-1 1l-3 1c-7 3-16 4-23 0-9-3-16-12-19-20-4-11 1-27 6-37 6-14 18-24 32-30 4-1 7-1 11-2z" class="E"></path><path d="M346 771l-1-1v-1c1-1 1 0 1-1 1-2 2-3 5-4h1v-1c2 0 3 0 5 1 1 0 2 0 3 2h1c1 2 1 4 2 6 0 1 1 2 1 3l-4 5c1-2 2-5 1-7h0c-1-3-1-5-4-7-1-1-4-1-6-1-3 1-4 4-5 6z" class="C"></path><path d="M320 683c1 0 2-1 2-2 3-2 5-4 7-6 5-3 10-2 15-1s15 4 18 8c-2 1-4 1-6 2-1 2-1 4 0 6v2c-12-6-23-8-36-9z" class="H"></path><path d="M341 648c4 0 22 6 25 9l1 1 13 10h4l1-2c14 14 24 32 25 53 0 7-1 14-3 21v2 1c0 1 0 1-1 2-1 0-1 2-2 3 0 1-1 1-1 2-1 0-1 1-1 1 0 1-1 1-2 2v1h0 1c1-1 3-2 5-2h0c-7 3-13 9-16 16-1 2-2 3-3 5v-3-6l1-3h-1l-6 3-1-1c-1 0-2 1-3 1 0-1-1-2-1-3v-3c2-10 2-20 2-31 0-13-1-25-7-37-3-4-5-6-9-8-3-4-13-7-18-8s-10-2-15 1c-2 2-4 4-7 6 0 1-1 2-2 2-4 1-7 1-11 2-1 0-1 0-1-1-2-2-2-7-2-10-1-3-1-7 0-10 1-4 3-6 5-9 1-1 2-1 3-2 8-4 15-4 24-3 1-1 2-1 3-1 2 0 3 1 5 1h1c1 1 1 1 2 1l-2-1c-2 0-4-1-6-1h0v-1z" class="Q"></path><path d="M341 648c4 0 22 6 25 9l-2 2c-9-5-17-8-26-9 1-1 2-1 3-1 2 0 3 1 5 1h1c1 1 1 1 2 1l-2-1c-2 0-4-1-6-1h0v-1z" class="K"></path><path d="M366 657l1 1 13 10c4 3 7 6 10 10h0-1v-1c-1-1-2-1-3-2h0l1 1c1 1 1 2 2 3h0-1c-7-9-15-15-24-20l2-2zm-60 17c3 2 6 4 10 3 6-1 9-9 14-12 11-8 30 3 39 10 15 11 25 28 28 46 0 5 1 11 0 16-1-4-1-7-1-10-3-10-6-18-13-26-2-3-4-6-7-8-1-1-2-2-2-3h-3c-3-4-5-6-9-8-3-4-13-7-18-8s-10-2-15 1c-2 2-4 4-7 6 0 1-1 2-2 2-4 1-7 1-11 2-1 0-1 0-1-1-2-2-2-7-2-10z" class="B"></path><defs><linearGradient id="AB" x1="405.85" y1="716.206" x2="367.583" y2="725.938" xlink:href="#B"><stop offset="0" stop-color="#1c1d1e"></stop><stop offset="1" stop-color="#3b3d3d"></stop></linearGradient></defs><path fill="url(#AB)" d="M385 666c14 14 24 32 25 53 0 7-1 14-3 21v2 1c0 1 0 1-1 2-1 0-1 2-2 3 0 1-1 1-1 2-1 0-1 1-1 1 0 1-1 1-2 2v1h0 1c1-1 3-2 5-2h0c-7 3-13 9-16 16-1 2-2 3-3 5v-3-6l1-3h-1l-6 3-1-1c-1 0-2 1-3 1 0-1-1-2-1-3v-3c2-10 2-20 2-31 0-13-1-25-7-37h3c0 1 1 2 2 3 3 2 5 5 7 8 7 8 10 16 13 26 0 3 0 6 1 10 0 6-3 13-5 19 6-5 9-14 10-22 3-20-2-39-14-55h1 0c-1-1-1-2-2-3l-1-1h0c1 1 2 1 3 2v1h1 0c-3-4-6-7-10-10h4l1-2z"></path><defs><linearGradient id="AC" x1="410.126" y1="708.172" x2="378.726" y2="704.966" xlink:href="#B"><stop offset="0" stop-color="#767364"></stop><stop offset="1" stop-color="#a9a293"></stop></linearGradient></defs><path fill="url(#AC)" d="M385 666c14 14 24 32 25 53 0 7-1 14-3 21-2 6-4 9-8 13 0-3 3-6 4-10 2-7 4-15 3-22 0-12-4-25-10-36-2-2-4-5-6-7h0c-3-4-6-7-10-10h4l1-2z"></path><path d="M273 737c1-13 8-26 18-35 8-7 19-11 30-10 14 1 28 9 37 19 8 10 14 23 14 36 0 10-3 19-8 28 0-1-1-2-1-3-1-2-1-4-2-6h-1c-1-2-2-2-3-2-2-1-3-1-5-1v1h-1c-3 1-4 2-5 4 0 1 0 0-1 1v1l1 1c-3 10-7 18-16 23-1 0-2 1-4 1h-1c-3 1-8 2-11 1v-1c7-2 15-9 16-17 0-2-1-6-1-7 1-2 3-3 3-4l1-1c2-2 3-5 3-8 2-9 2-17 0-26l-1 2c0 11-2 21-10 30-3 3-6 6-9 7 0-1 0 0 1-1v-1c0-1-1-1-1-2h-4l-4 2c-6 2-14 0-19-3-7-4-13-10-15-18h0c-1-3-2-8-1-11z" class="H"></path><path d="M273 737c1-13 8-26 18-35 8-7 19-11 30-10 14 1 28 9 37 19 8 10 14 23 14 36-1 1-1 2-1 4-1-2-1-4-1-6-4-14-13-27-25-34l-3-2c-10-6-22-9-34-6-9 2-15 6-21 11 4-1 7-4 11-5 7-2 16-1 23 3 9 4 12 11 15 20l-1 2-1-3c-2-7-6-14-13-18s-16-5-24-3c-9 3-14 8-18 16-2 3-3 10-5 13v1l-1-3z" class="B"></path><path d="M279 726c4-8 9-13 18-16 8-2 17-1 24 3s11 11 13 18l1 3c0 11-2 21-10 30-3 3-6 6-9 7 0-1 0 0 1-1v-1c0-1-1-1-1-2h-4l-4 2c-6 2-14 0-19-3-7-4-13-10-15-18h0c-1-3-2-8-1-11l1 3v-1c2-3 3-10 5-13z" class="Q"></path><path d="M280 726c4-5 8-9 15-11 3 0 7 0 10 2 0 1 1 1 1 2l1 1c-2 2-9 2-10 4 0 3 1 5 3 6 6 5 15 1 21 7 2 2 4 4 4 7 1 5 0 9-2 13v-1c0-2 0-5-1-7-3-7-13-4-19-7-2-1-6-4-7-6s1-6 1-7c0-2 0-3-1-5h-1c-6 1-11 4-17 8 0-1 1-4 2-5v-1z" class="B"></path><path d="M280 726v1c-1 1-2 4-2 5 6-4 11-7 17-8h1c1 2 1 3 1 5 0 1-2 5-1 7s5 5 7 6c6 3 16 0 19 7 1 2 1 5 1 7v1c-2 4-6 7-11 10l-4 2c-6 2-14 0-19-3-7-4-13-10-15-18h0c-1-3-2-8-1-11l1 3v-1c2-3 3-10 5-13h1z" class="H"></path><path d="M348 250h11c3 1 7 1 10 1 7 0 14 1 20 0h29c0 7 0 16-2 23-3 2-6 2-9 2-4 0-7 1-10 1-12 3-29 10-30 24 0 6 3 13 5 18l6 14 46 103 74 163 41 93 11 24c2 3 3 8 5 11 1 0 1 1 2 1 3-1 4-4 6-7l19-41 31-64 65-135 46-96 14-29c3-6 6-13 9-21s3-21 0-30c-3-10-13-17-22-22-8-4-18-7-26-8-4 0-8 0-12-1-1-7-2-15-1-22 3-2 12-1 16-1h10 2 9 0c4 1 9 0 13 0 8 0 17-1 25 0h88 31 16c1 0 2 1 3 1 1 3 0 8-1 11v3c0 2-1 5-1 8-2 1-5 1-7 2l-14 1c-5 1-11 1-16 2-8 2-17 4-25 8-14 6-28 18-38 30-6 8-11 17-15 26l-16 33-33 69-92 190-59 124-44 94-11 24c-2 4-3 8-5 12h-3c-1 0-2 0-2-1-1-1-2-4-3-5l-6-15-27-63-79-175-71-155-43-97-14-31c-6-11-11-23-18-33-17-23-42-33-69-37l-27-3c-2-6-2-12-3-19v-2c1-1 1-2 2-2h4 15 69c12 0 25 1 36 0h21c5 0 10 1 15 0l1-1c3 1 7 1 10 1h3 9 4l2-1z" class="E"></path><path d="M567 161c4-19 8-41 22-55 6-6 13-11 17-18 4-6 4-16 9-20 3-2 7-3 10-2 3 0 6 3 8 6 3 6 5 13 7 19 3 8 6 17 9 26 3 14 4 31 6 46h0c2 6 3 11 5 17 6 16 16 28 28 40l6 6c1 2 3 3 5 5 2 1 3 4 5 5 6 6 12 10 19 15h-9-2-10c-4 0-13-1-16 1-1 7 0 15 1 22 4 1 8 1 12 1 8 1 18 4 26 8 9 5 19 12 22 22 3 9 3 22 0 30s-6 15-9 21l-14 29-46 96-65 135-31 64-19 41c-2 3-3 6-6 7-1 0-1-1-2-1-2-3-3-8-5-11l-11-24-41-93-74-163-46-103-6-14c-2-5-5-12-5-18 1-14 18-21 30-24 3 0 6-1 10-1 3 0 6 0 9-2 2-7 2-16 2-23h-29c-6 1-13 0-20 0-3 0-7 0-10-1h-11l-2 1h-4-9-3c-3 0-7 0-10-1 10-7 19-14 27-23 1-1 3-3 5-4 10-11 18-22 25-35 1 1 1 0 1 1l1 1c3-5 7-13 9-19h0c-1 1-1 0-1 1l-1 1-1-1c0-1 1-2 0-3v-1c4-15 4-30 6-45 2-12 7-24 11-35 2-5 3-11 5-15 1-3 3-5 6-6 2-2 5-2 7-1 4 1 6 3 8 6 3 6 4 12 8 18 8 11 19 20 26 32 7 13 11 28 15 42 2 6 3 12 5 18v-4h0c-1-2-1-3-1-4 0 1 1 1 1 2l2 3-1 1 2 2c1 0 1 0 2 1l1-1 7 7c0-1 1-1 1-1l2 1v-1-1c1 1 2 2 2 3 7 7 10 14 14 23v-2-3h0c2 2 2 6 4 8l2 1v-5l1-6 1 15c0 3 0 7 1 11v5c1-1 1-7 1-9s1-4 1-6l1-1 1-2 1 1v-1-1s0-1 1-1c0-2 0-3 1-4v5l-1 1c0 1 1 1 1 2 0-3 1-5 1-8 2-3 4-7 6-11 2-3 3-7 6-11 6-8 13-15 19-23v-1l2-2h0l1-3 2-1z" class="B"></path><path d="M504 580l1 1v4s-1-1-2-1c1-2 1-3 1-4z" class="K"></path><path d="M410 235c1 0 2-1 3-2l1 4h-7c1-1 2-1 3-2z" class="L"></path><path d="M392 177l2-5c1 3 2 7 0 10l-2-5z" class="K"></path><path d="M472 322l2 4c-1 1-4 1-6 2 2-2 3-4 4-6z" class="I"></path><path d="M487 467l7 2-2 2-5-3v-1z" class="V"></path><path d="M492 224h1c2 2 3 4 5 6v-1-2h0c0 1 2 3 2 4l-1 3c-2-4-5-7-7-10z" class="F"></path><path d="M453 398c1 3 2 4 3 7 2 0 1 0 2-1l1 3c-1 0-1 1-1 2l-1 1c-1-1-1-2-1-3l-4-8 1-1z" class="I"></path><path d="M526 645c-2-2-3-5-4-8 1 0 3 1 4 2v1h-1c1 1 3 2 4 3v1 1c-1 0-1-1-2-1l-1-1v2z" class="C"></path><path d="M504 313h0 1v2c1 2 1 6 1 8l-1 1v3c0 1 0 1-1 1-1-5 0-10 0-15z" class="F"></path><path d="M585 253c-5 0-9 1-14 3 4-2 8-5 12-6l2 3z" class="N"></path><path d="M376 251c1-2 2-2 4-3 4 0 7-1 10-2l-5 4c-1 0 0 0-1 1h-8z" class="H"></path><path d="M477 295c3 2 7 4 9 7l-1 2c-3-2-6-5-9-7l1-2z" class="L"></path><path d="M685 241c2 0 3 1 5 2h1c1 0 3 2 3 2 1 1 5 3 5 4 0 0-1 1-2 0l-2-1c-4-2-7-4-10-7z" class="P"></path><path d="M478 186h0v5l3 2c1-2 0-2 0-3 0 0 1-1 2-1 0 3-1 6-2 9h0l-4-3c1-3 1-6 1-9z" class="L"></path><path d="M447 190c0 2 0 3 1 4l2-1c-2 5-4 9-8 12 0-1 1-2 2-3l3-12z" class="P"></path><path d="M506 264l1-1v1h1c0 7-2 13-4 20 1-5 0-9 1-14 0-2 1-3 1-5v-1z" class="L"></path><path d="M565 317h1l1 1v1c0 2 2 4 4 5l2 1h-3c-2-1-4-1-6-3-1-1-1-1-1-3l2-2z" class="E"></path><path d="M494 469c5 1 9 4 14 5 1 0 1 0 2 1-4 1-14-2-18-4l2-2z" class="I"></path><path d="M560 469v3c-2 4-12 6-16 7 3-2 6-3 9-5 3-1 5-3 7-5z" class="M"></path><path d="M583 250c6-1 12-2 18 0h-12v2h5l2 1h-11l-2-3z" class="T"></path><path d="M560 334c6-2 12-2 17-2-2 3-5 2-8 2-2 0-4 1-6 1-1 0-2-1-3-1z" class="K"></path><path d="M474 261c1 1 3 2 4 3 3 3 5 6 8 9l1 1h0c-4-3-8-6-13-9 0-1-1-2-2-3l2-1h0z" class="M"></path><path d="M589 326l1-1c2 0 3-1 4-2v1c0 2-2 3-4 5-2 1-6 2-9 2-1 0-4 0-5-1h2c1-1 3 0 4 0 2 0 3-2 5-3 1 0 2 0 2-1z" class="V"></path><path d="M445 372h1c0 1 0 1 1 2-1 3-3 7-2 11h0c0 2 0 3 1 4v1 1c-1-1-1-2-1-3-1-2-3-6-2-9 0-2 1-5 2-7z" class="G"></path><path d="M509 458s1-1 2-1c2 1 2 1 3 2 0 2 1 3 0 4-1 0-2 1-3 0-1 0-2-1-3-2 0-2 0-2 1-3z" class="E"></path><path d="M395 158v-3c1-5 1-10 2-15 1 3 0 5 1 7s1 4 1 6c0 1 0 1-1 2v1h-1l-1 1-1 1z" class="O"></path><path d="M589 362c7 1 15 1 21 4-1 1-1 1-2 1-2-1-4-1-5-1-2-1-6-1-8-1-1-1-2-1-3-1h-1l-2-2z" class="S"></path><path d="M533 430h5c4 2 9 4 11 8-1 0-2 1-3 1-2-1-1-1-3-1 0-1-1-3-1-4-2-2-4-3-6-3-1 0-2 0-2-1h-1z" class="L"></path><path d="M625 546c8-1 12-5 19-9-2 2-3 5-5 7v1c-3 0-5 0-7 1h-2c-1 0-3 1-4 1l-1-1z" class="G"></path><path d="M714 352l1 1c1 3 0 6 1 9h0c0-2 0-4 1-6v-2h0c1 7 0 15-2 23-1-3 0-11 0-14l-1-11z" class="P"></path><path d="M530 457h5c1 1 1 1 2 3l-3 3h-2c-2 0-2 0-3-2 0-2 0-2 1-4z" class="E"></path><path d="M588 185c2 2 3 4 5 5 0 1 1 3 1 4s-1 2 0 3l2 4-1 1c-3-6-6-10-7-17z" class="N"></path><path d="M484 468h0c4 3 8 5 13 6 2 1 5 2 7 3-3 0-7-1-10-2-4-1-9-2-12-5l2-2z" class="M"></path><path d="M597 417l1 2c2 0 4-1 7-1-3 1-5 2-7 3-3 2-6 3-8 6l-1-1c2-3 4-6 8-9z" class="D"></path><path d="M584 629v5c1 1 0 3 0 4l-1 10c-1-3-1-5-2-8 1-2 1-3 1-6h-1v-5h0c1 1 1 2 2 3v1h0c1-1 1-3 1-4z" class="J"></path><path d="M644 446c4-1 8-10 10-13 1 2 0 4 0 6-3 4-6 8-10 10l-1-1 1-2z" class="G"></path><path d="M490 489c3 4 6 7 10 10h0 1c-1 1-2 1-3 2-4-3-7-6-9-11l1-1z" class="R"></path><path d="M472 322l2-6c3 2 4 4 6 7l-6 3-2-4z" class="M"></path><path d="M395 185h1v1c0 6-4 15-8 19h-1l8-20z" class="C"></path><path d="M512 504h1c0-1 1-1 1-1l2 1h2l1 1v-1h2l1 1v-1h2l1 1c1-1 1-1 3-1 0 1 0 2-1 2h-1l-1 1c-1 1-3 1-5 0h-1c-2 0-3-1-5-1v-1h-1l-1-1z" class="M"></path><path d="M608 403h1c2 1 4 3 5 5 0 1 1 2 1 3 2 4 3 7 3 11l-3-6c-1-2-2-5-4-7-1-2-3-4-3-6z" class="K"></path><path d="M422 226l-1 1c0 1-1 1-2 2s-1 2-1 4v1 1h0c1 1 2 1 3 1l-7 1-1-4 4-4c-2 1-4 2-5 1l10-4z" class="G"></path><path d="M420 169c-3-1-9-7-10-10l2 1c2 2 5 2 7 4h0v1c3 1 5 3 8 6h-1c-2-1-4-2-6-2zm148 389h2l11 28c-5-8-10-18-13-28z" class="P"></path><path d="M597 417c2-1 6-4 9-3 2 0 3 1 3 2 1 1 2 2 2 3-1 0-1 0-2-1h-4c-3 0-5 1-7 1l-1-2z" class="C"></path><path d="M698 282h9 1c5 1 11 3 16 6h-4l-1-1c-8-3-18-3-26-4 1-1 4-1 5-1z" class="H"></path><path d="M556 467v3c-4 3-10 6-16 7h0-6l1-1h0 0l-1-1h1-1c1-1 3-1 5-2 1 1 1 2 2 2 5 0 11-5 15-8z" class="M"></path><path d="M534 475c1-1 3-1 5-2 1 1 1 2 2 2-2 1-4 1-6 1h0l-1-1h1-1z" class="F"></path><path d="M393 339v1c2-1 4-3 6-4v1h0l-3 2v1 1h0v2l-6 7h-1v-3c1-2 4-5 5-7l-2 2-3 3h-1l1-3c1-1 2-2 4-3z" class="O"></path><path d="M540 516v-1l15-14c0 2-1 4-2 7-4 0-9 7-12 10l-2 1h-1c1-1 2-2 2-3h0z" class="K"></path><path d="M463 387c3-6 8-11 14-15l-4 5c-3 3-5 8-7 13l-1 1h-1v-1h-2l1-2v-1z" class="M"></path><path d="M544 292l1 1v-1l1 1c-2 3-2 5-2 8v1l-4 14c-1-9 1-16 4-24zm-59 12l1-2c7 7 11 12 13 22-1-2-2-5-4-7 0-1-1-3-2-3-2-4-5-7-8-10z" class="G"></path><path d="M451 194c1 3 1 4 3 5v1c-4 6-12 8-19 9l6-3h1c5-2 7-7 9-12z" class="J"></path><path d="M527 219l1 1v-1-1s0-1 1-1c0-2 0-3 1-4v5l-1 1c0 1 1 1 1 2v2c-2 6-2 12-4 17v-19l1-2z" class="L"></path><path d="M605 313v-3-1c6 4 11 8 15 13h-2l-1-1c-2 0-3-1-5-2-2-2-5-4-7-6z" class="D"></path><path d="M568 558c-4-9-6-20-6-30h0c1 2 1 4 1 6l3 11c1 4 3 8 4 13h-2z" class="H"></path><path d="M693 383h1c0-2-1-3-1-4h2c0 1 1 2 1 3 2 9 0 18 0 28h-1c1-9 0-18-2-27z" class="T"></path><path d="M404 104h0c0 3-1 6 0 8v4c2-1 1-6 1-8h1v5 4c0 1 0 2 1 3v4c0 1 0 1 1 3 0 1 1 2 1 3v1l-2-3c0 1-1 1-1 2l-1-2c-1-1-2-2-2-4h1 0v-2c-1-2 0-6 0-7-1-2-1-5-1-7 1-1 1-3 1-4z" class="K"></path><path d="M395 288c5-2 10-1 15-2 4-1 8-3 12-4-2 2-3 3-4 5l-1-1h0c-5 2-11 4-17 4v-1c-2 0-3-1-5-1z" class="J"></path><path d="M395 337c-2 0-3 1-5 2-1 1-2 1-3 2h0-1v-1c3-7 20-10 26-12h0 1c-3 1-5 3-8 3l-5 2v1c-1 1-3 2-5 3z" class="C"></path><path d="M455 326c-5-4-8-9-9-15 0-2 1-2 3-2 1 1 2 7 3 9s3 4 4 6c0 1 1 2 1 2h-2z" class="M"></path><path d="M578 567c1 1 2 1 2 2 1 1 2 4 2 4h2c1 1 1 4 2 5 3 9 6 19 6 28h-1c-1-14-7-27-13-39z" class="G"></path><path d="M648 463l-2 2h1l2-2c1 0 1-1 1-2h1c-1 3-5 6-7 8l-5 5c-3 4-4 9-8 13 0-3 1-4 2-6v-2l1-1-1-1c3-6 10-10 15-14z" class="H"></path><path d="M556 251c4-7 10-11 16-15l2 3c-4 2-8 5-11 8l-5 5c-1-1-1-1-2-1z" class="G"></path><path d="M414 366v1l1-1c1-3 2-5 4-7h1c-1 1-1 2-1 4l-3 8c-3 8-3 17-1 25l-1-1v-2l-1-1c-1-9-1-17 1-26h0z" class="J"></path><defs><linearGradient id="AD" x1="587.873" y1="622.728" x2="577.627" y2="609.772" xlink:href="#B"><stop offset="0" stop-color="#7c7a6b"></stop><stop offset="1" stop-color="#9f9787"></stop></linearGradient></defs><path fill="url(#AD)" d="M579 611s0-1 1-1c0-1 0-2-1-4l1-1c3 5 6 10 6 17 0 3 0 6-1 10-1-8-2-14-6-21z"></path><path d="M433 210h1l-2 2c-8 7-18 9-26 15h-1c0-1 1-4 2-5s5-2 6-2c7-3 15-5 20-10z" class="O"></path><path d="M418 280c-2 2-4 3-6 4-3 1-7 1-10 1h-15c3-1 7-2 10-2 7-1 13-1 20-3h1z" class="J"></path><path d="M648 435h1c4-5 7-13 9-19 1-3 1-6 3-8h0c-1 10-6 20-11 29-1 2-2 4-3 5h-1c-1 0 0 0-1-1 0-1 1-2 2-3 0-2 0-2 1-3z" class="I"></path><path d="M565 346c-3 0-7 0-9 2h-2l1-1c7-6 19-7 28-8l-1 2-12 3c-2 0-4 1-5 2z" class="N"></path><path d="M610 366c5 2 12 6 15 12 1 1 3 4 3 6-6-6-10-11-18-14l-2-2c-2 0-4-1-5-2 1 0 3 0 5 1 1 0 1 0 2-1z" class="D"></path><path d="M556 251c1 0 1 0 2 1-9 10-16 23-19 36-1-5 5-18 7-24 3-5 6-9 10-13z" class="M"></path><path d="M484 217c4 2 6 5 8 7 2 3 5 6 7 10 1 2 3 5 3 7-1-2-3-4-4-6-4-4-8-7-11-9v-3c0-2 0-2-1-3h0l-1-2-1-1z" class="G"></path><path d="M617 118c5-6 8-11 8-20 1-3 0-5 1-8 2 10 1 21-4 29l-5 6v-1l2-3c2-2 3-4 4-6l-1-1-2 2c0 1-1 2-2 3l-1-1z" class="Q"></path><path d="M654 433c4-9 7-17 8-27 1 13-1 23-8 33 0-2 1-4 0-6z" class="T"></path><path d="M383 216c3-5 4-11 10-14-2 3-4 5-5 7h0v1c-1 1-2 4-3 5 0 1 0 1 1 2-2 2-4 5-5 7-2 3-4 7-7 9h-1c1-3 4-7 5-10 1-1 1-2 2-3s2-2 3-4z" class="O"></path><path d="M638 284c2 0 3 0 4 1h1c1 1 2 2 3 4l24 21-12-8c-5-4-10-8-15-13l-5-5z" class="G"></path><path d="M638 284c2 0 3 0 4 1h1c1 1 2 2 3 4 1 0 1 1 2 2h-1c-1-1-2-2-4-2l-5-5z" class="M"></path><path d="M464 402c2-11 7-21 16-27l2-1c-3 3-6 6-8 10-4 5-6 11-7 18h-3z" class="G"></path><path d="M403 371c-3-7-4-12 0-19v-1c2-4 5-7 8-10-3 6-6 12-8 18l1 3h0v8l-1 1z" class="O"></path><path d="M403 359l1 3h0v8h-1c-1-4 0-8 0-11z" class="K"></path><path d="M685 241c-11-7-25-18-29-30h0c3 6 8 11 14 16 7 6 15 10 21 16h-1c-2-1-3-2-5-2z" class="S"></path><path d="M417 343l5-5c4-3 7-4 11-5 1 0 1 0 2-1h-3v-1c3 0 6 0 8 1 0 1 0 1 1 1-1 1-2 1-3 1v1 2c-7 0-14 1-20 6h0-1z" class="Q"></path><path d="M392 353h0l1-2c3-4 9-9 13-10-1 2-3 5-5 7-4 5-5 9-6 15-1-3-2-5-3-7-1-1 0-2 0-3z" class="C"></path><path d="M438 330h-5-1l-1 1c-3 1 1 0-1 0l-2 1h0-1c2-1 5-2 6-4 2-1 4-1 6-2v-1l-4-3 3-6c2 4 5 7 7 11h-5c-1 1-1 2-2 3z" class="P"></path><path d="M456 324c5 4 9 6 15 6 2 0 6-2 8-2 1 0 3 2 4 3 2 1 3 2 5 4-3-1-5-2-8-3-10-1-17 1-25-6h2s-1-1-1-2z" class="G"></path><path d="M589 426l1 1c2-3 5-4 8-6v1h2 0 3c1-1 4-1 5 0 0 1 0 0-1 1-6 1-10 4-14 8h-3l-1 3-1-2c1-1 1-2 1-3v-1c-1-1-1 0-1-1l1-1z" class="D"></path><path d="M589 426l1 1c2-3 5-4 8-6v1h2 0 3l-6 2c-3 1-6 3-7 7l-1 3-1-2c1-1 1-2 1-3v-1c-1-1-1 0-1-1l1-1z" class="K"></path><path d="M603 135c0-1 5-7 7-7 2-2 5-4 6-6l2-2 1 1-2 3v1c-3 5-9 10-13 14-1 0-1 0-1 1h-1l-1-1c1-1 3-2 4-4 1 0 1 0 1-1-1 0-2 1-3 1z" class="U"></path><path d="M556 308v-1c0-2 1-2 2-3h-1l-1 1v1h-2c-1 0-1 1-2 2h0l-1-1 1-1c1-2 2-3 3-4 2-2 4-4 7-6 1-1 2-1 3-2s3-2 4-2l1-1h3c-2 2-5 4-7 6-4 3-7 7-10 11z" class="D"></path><path d="M373 300c11-9 25-6 38-10 2-1 4-2 6-4l1 1c-6 5-14 7-21 8-9 0-17 0-25 7l1-2z" class="P"></path><path d="M438 246h2c1 0 2-1 3 0h0c-2 1-4 1-5 2h0 3 1v1h-2c-1 0-2 0-2 1l-6 1s-1 1-2 1l-5 1h-1v1h0c2 1 2 1 3 2h-1-2c-1-2-3-4-5-6 2 0 5 0 7-1l12-3z" class="L"></path><path d="M453 398c0-1-1-2-1-3 0-5-1-8 1-13 3-9 11-16 19-20l1 1h-1c-1 1-2 2-3 2-1 1-2 1-2 2-4 2-7 4-9 8-1 2-2 3-3 5h0c0 2-1 3-1 5 1-1 1-2 2-2v-2c0-1 1-1 1-2h1l7-9h1l1-1s1 0 2-1c1 0 2-1 3-1h0c-8 5-16 12-18 22-1 2-1 4-1 6v3z" class="O"></path><path d="M458 260c10 2 20 8 26 16l1 1h0c-7-7-16-9-25-13-1-1-2-1-3-1 0-1 0-2 1-3z" class="G"></path><path d="M416 118c-5-4-7-9-8-15 0-3 0-11 2-14v6c0 6 2 11 6 15h0v1c1 2 3 4 5 6h-1s-1 0-1-1l-1 1 1 2h0c-2-1-3-2-4-4 0-1-1-1-1-2-1-1-2-4-3-4 0 1 0 1 1 2v1c0 1 1 2 2 3l2 3h0z" class="U"></path><path d="M427 136c8-1 17 2 23 8 6 5 9 15 10 23-1-2-2-5-4-7-2-3-5-6-7-9 1 1 3 1 5 2v-2c0-1 0-1-1-2-5-7-15-11-23-13h-3z" class="R"></path><path d="M526 640v-1h1c11 5 26 3 35 12l3 3v1c-1 0-1-1-1-1-10-9-26-8-38-14z" class="D"></path><path d="M463 387v1l-1 2h2v1h1c-1 6-2 12-2 18-1 1 0 1-1 2v1h-1c0-1-1-1-1-2v-1c0-1-1-3-1-5 0-5 1-12 4-17z" class="G"></path><path d="M545 263l1 1c-2 6-8 19-7 24v3c-1 4-1 8-4 11v-7c1-1 1-4 1-5l2-13 1 1v1c2-5 3-10 6-16z" class="O"></path><path d="M622 335c10 5 18 11 24 20 1 2 3 3 3 5v1-1l-2-3h-1c1 2 2 4 2 6l-3-6c-4-6-9-11-16-14-1-2-5-4-8-5l1-1c0 1 1 1 1 1 2 1 3 1 4 1-2-2-3-1-5-4zm-50-99c3-2 6-3 10-5 10-3 25-4 35-1l1 1c-2 0-3 0-4-1h-17-1c-7 1-16 5-22 9l-2-3z" class="L"></path><path d="M633 477l1 1-1 1v2c-1 2-2 3-2 6-2 10-3 20-1 30v1l-1-1-2-6v-2c-2-11 0-22 6-32z" class="P"></path><path d="M485 229c3 2 5 5 7 8 5 8 10 17 13 26v6h-1 0c-1-16-13-27-22-38 3 0 4 3 6 5h1l-1-1v-1c-1-2-2-3-3-4v-1z" class="G"></path><path d="M605 313c2 2 5 4 7 6 2 1 3 2 5 2l1 1h2c3 2 7 4 10 7l-1 1c-3-1-6-4-9-5-1-1-4-1-5-2h-3c0-1 0 0-1-1h0v-1h2c-4-3-6 2-10 1 0-1 1-8 2-9z" class="J"></path><path d="M445 242c-3 1-15 5-17 5-3-1-7 0-10 0h-14c4-1 8-3 13-3 2 0 5 1 8 0 6 0 12-2 18-4l2 2z" class="L"></path><path d="M593 431h0l2-1c2-1 5 0 7 1v1h-1c-1 1-2 2-3 4l-5 8h-1c2-4 3-7 6-10l1-1c-2 0-3 1-4 2-2 2-3 4-4 6 0 1 0 0-1 1l1 1-1 1c0 1 0 4-1 6-1-5-1-10 0-15v-1l1-3h3zM392 177l2 5c-1 2-1 5-2 7-2 5-7 13-6 18h0c-2 3-3 6-3 9-1 2-2 3-3 4l3-12c1-4 1-7 3-11 1-1 1-2 2-4 0-3 2-5 3-8 1-2 1-5 1-8z" class="C"></path><path d="M728 310c4 2 7 4 10 8 3 7 5 11 4 19l-1 1h0c-2-11-7-19-13-28z" class="O"></path><path d="M603 142c3-3 8-7 12-7 3 0 7-1 10 1h0c-4 1-8 2-12 5-3 2-5 4-8 6l-1-1c0 1-1 2-2 2 1-1 1-2 2-2l-1-1 1-1v-1l-1-1z" class="S"></path><path d="M617 118l1 1c1-1 2-2 2-3l2-2 1 1c-1 2-2 4-4 6l-1-1-2 2c-1 2-4 4-6 6-2 0-7 6-7 7 0 2 0 1-1 2h-1l-3 3c-1-1-1-1-1-3l-1-1c6-6 14-12 21-18z" class="R"></path><path d="M420 169c2 0 4 1 6 2 6 5 10 9 12 17v10 2h0v-1c-1-7-6-17-11-23l-7-7z" class="J"></path><path d="M495 189c0-1 1-1 1-1l2 1v-1-1c1 1 2 2 2 3 1 3 4 5 5 8 5 9 10 19 11 30-2-3-3-8-4-11-4-10-9-20-17-28h0z" class="I"></path><path d="M486 226c3 2 5 5 7 8 6 7 13 16 14 26l1 4h-1v-1l-1 1c-3-13-12-26-21-36l1-2z" class="G"></path><path d="M562 168v-1l2-2c-1 5-2 9-4 13-7 10-17 17-22 28l-1 2c0-2 1-4 2-6 2-5 5-9 8-13 5-7 13-13 15-21z" class="V"></path><path d="M440 264c2 0 3 0 5-1 3 0 7-1 10 1 1 1 0 0 1 0 9 2 21 7 27 14l-2 1c0-1-1-1-2-2-2-3-7-4-10-6-10-3-18-6-29-7z" class="M"></path><path d="M503 584c-3-4-5-8-8-12-5-9-12-17-16-27 1 1 2 2 3 4 4 7 10 13 15 20 3 3 5 7 7 11 0 1 0 2-1 4z" class="D"></path><path d="M438 444c9 5 14 11 16 22 1 3 1 6 2 9 1 1 1 2 1 3h-1c-4-5-5-13-8-18-2-6-6-11-10-16z" class="O"></path><path d="M410 235c-3 0-5 1-7 2-8 2-15 5-21 9 1-2 2-3 4-4 7-6 17-10 26-12 1 1 3 0 5-1l-4 4c-1 1-2 2-3 2z" class="N"></path><path d="M544 222c-6 8-11 17-14 26h-1c2-10 6-19 10-28 3-5 6-12 11-16 0 0-3 5-4 6v1l-1 2-1 1-2 4c0 1 0 2-1 3h0l1 1 1-1h0c0 1 1 1 1 1z" class="V"></path><path d="M530 492h0c1-2 2-4 3-5h7c-1 2-2 3-2 5l1 1v-1-1c0-1 1-2 2-3 2 0 4 0 6-1h3c2 0 4-1 7-1h-1c-4 3-10 6-15 8h-6 0v-2c-1 1-1 2-2 3h-3v-2-1z" class="R"></path><path d="M530 221c0-3 1-5 1-8 2-3 4-7 6-11 2-3 3-7 6-11 6-8 13-15 19-23-2 8-10 14-15 21-3 4-6 8-8 13-1 2-2 4-2 6v4h-1c0 2 0 3-1 4v-1c0-1-1-1-2-2l-1 1 2 2c-1 0-1 1-1 1-1-1 0-1-1-1 0 1 0 2-1 3 0 2-1 3-1 4v-2z" class="C"></path><path d="M404 362c3-8 5-14 13-19h1c-10 11-11 23-11 37l-4-9 1-1v-8z" class="D"></path><path d="M700 289c-3 0-8-4-11-5 3 1 6 1 9 1 13 1 28 4 38 12 3 3 8 8 8 11-7-9-20-17-31-19h-1c-2-1-3-1-5-1h0c-2 1-5 0-7 1h0z" class="H"></path><defs><linearGradient id="AE" x1="643.577" y1="161.227" x2="622.496" y2="169.95" xlink:href="#B"><stop offset="0" stop-color="#2a2a2b"></stop><stop offset="1" stop-color="#464646"></stop></linearGradient></defs><path fill="url(#AE)" d="M642 152v2c0 5 0 15-4 18-5 3-12 4-16 7-3 2-5 5-7 7 0-2 1-3 2-4 5-6 12-8 17-14 4-4 5-10 7-15l1-1z"></path><path d="M594 194c1 3 3 6 5 8h1c2 2 4 5 7 7 6 6 15 10 23 13-3 1-6 0-8-1-9-2-22-10-27-19l1-1-2-4c-1-1 0-2 0-3z" class="J"></path><path d="M560 334l-5 1c1-2 4-8 7-9h2c7 2 12 3 18-1v3 2c-1 0-3-1-4 0h-2c1 1 4 1 5 1l-4 1c-5 0-11 0-17 2z" class="I"></path><path d="M369 251c5-7 10-15 18-16l1-1h4l-9 9c-3 3-6 4-9 7l2 1h8 5c-6 1-13 0-20 0z" class="U"></path><path d="M529 644v-1c13 6 30 3 38 18l1 1c0 1 0 1 1 3h0v2c0-1-1-2-1-3-2-4-8-8-12-10-10-3-18-4-27-10z" class="O"></path><path d="M645 461l1 1c-3 2-6 4-8 6-3 2-5 4-7 6-6 7-8 17-8 26 0 5 1 9 2 14l2 4 3 9c-2-2-3-5-4-8-1-4-3-7-5-11-1-5-1-11 0-16 3-14 13-23 24-31z" class="D"></path><path d="M495 561c-2-2-5-5-5-8-2-1-3-2-4-4-1-1-1-2-2-3s-1-2-2-3c-6-7-10-16-15-24-1-1-1-2-2-3l1-1c1 2 2 3 3 5l21 30c5 7 11 12 15 18l-1 1c-3-3-5-6-7-9h-1v1h-1z" class="C"></path><path d="M578 312c1 0 3-1 4 0 1 0 0 0 1 1 0 1 0 5-1 7-2 2-4 3-7 4l-2-1c-2-1-3-2-3-4-1-2-1-3 0-5 2-1 5-2 8-2z" class="E"></path><path d="M671 339v-1c-1-1-1-1-1-2h2c1 0 1 1 2 1h0c3 2 4 6 6 9 2 5 5 10 6 15 2 7 4 14 5 20 0 1 1 3 1 3h-1v-1c-5-16-11-31-20-44z" class="I"></path><path d="M556 351c1-2 2-2 3-3 2 1 3 2 4 2 2 1 4 1 6 2 2 0 5 0 6 1 2 2 7 2 10 3 6 1 15 2 21 5l3 1-28-4c-7-1-14-2-21-6h0c-1-1-2-1-4-1z" class="N"></path><path d="M400 290c6 0 12-2 17-4h0c-2 2-4 3-6 4-13 4-27 1-38 10v-1l-1-1 2-2c6-6 14-7 21-8 2 0 3 1 5 1v1z" class="F"></path><path d="M374 296c6-6 14-7 21-8 2 0 3 1 5 1v1c-9 0-18 0-25 6h-1z" class="S"></path><path d="M692 231c-5-2-10-4-13-7-12-10-21-25-22-40l3 9c5 12 13 23 24 31 1 2 3 4 6 5h0c1 0 2 1 2 1v1z" class="N"></path><path d="M542 584h0c4 12 13 25 24 31 2 1 7 4 9 4h1l3 3h1 1 0c0-1 0-2-1-3 0-2-1-2-1-4v-1c3 5 4 10 5 15 0 1 0 3-1 4h0v-1c-1-1-1-2-2-3-1-2-2-4-3-5-3-3-9-4-12-6-6-3-11-9-15-14-3-5-6-9-8-15l-1-5z" class="R"></path><path d="M413 88l2 4c6 10 17 13 26 19 4 2 6 5 9 8 2 3 4 5 6 9h-1c-7-12-17-14-28-21-8-4-11-10-14-19z" class="I"></path><defs><linearGradient id="AF" x1="438.108" y1="340.08" x2="453.399" y2="354.913" xlink:href="#B"><stop offset="0" stop-color="#b2aa9e"></stop><stop offset="1" stop-color="#c7c2a7"></stop></linearGradient></defs><path fill="url(#AF)" d="M436 345c7-2 17-4 24-3 2 1 3 2 4 2s1 0 2 1h0c-15 1-32 0-43 12l-4 6c0-2 0-3 1-4h1c2-4 7-11 12-13h2l1-1z"></path><defs><linearGradient id="AG" x1="418.091" y1="191.795" x2="398.452" y2="188.213" xlink:href="#B"><stop offset="0" stop-color="#505152"></stop><stop offset="1" stop-color="#8a8472"></stop></linearGradient></defs><path fill="url(#AG)" d="M398 156v-1c2 7 3 13 6 19 1 2 3 4 4 7 2 0 3 2 4 3l-1 1-2-2h0c2 7 7 12 7 19 1 5 0 9-3 12h-1c8-11-6-27-10-38-3-6-4-13-4-20z"></path><defs><linearGradient id="AH" x1="425.346" y1="354.869" x2="425.154" y2="344.122" xlink:href="#B"><stop offset="0" stop-color="#968b80"></stop><stop offset="1" stop-color="#bcb7a1"></stop></linearGradient></defs><path fill="url(#AH)" d="M416 358c1-3 2-5 4-7 7-8 18-9 28-10v1h-4l-8 2v1l-1 1h-2c-5 2-10 9-12 13h-1-1c-2 2-3 4-4 7l-1 1v-1c0-3 1-6 2-8z"></path><path d="M416 358h1c4-3 8-9 13-11h1c1-1 3-2 5-3v1l-1 1h-2c-5 2-10 9-12 13h-1-1c-2 2-3 4-4 7l-1 1v-1c0-3 1-6 2-8z" class="R"></path><path d="M386 308c5-2 9 0 13 2h1l-1 1h-1c-4 1-7 2-11 3-2 1-5 3-7 5-1 0-3 2-4 2l-1-1v-4c3-4 6-7 11-8z" class="J"></path><path d="M386 308c5-2 9 0 13 2h1l-1 1h-1l-1-1c-3 0-10-1-12 1h0l-7 5-1-1c2-2 4-3 6-4l3-3h0z" class="P"></path><defs><linearGradient id="AI" x1="589.371" y1="367.896" x2="561.121" y2="352.007" xlink:href="#B"><stop offset="0" stop-color="#656159"></stop><stop offset="1" stop-color="#9a9381"></stop></linearGradient></defs><path fill="url(#AI)" d="M556 351c2 0 3 0 4 1h0c5 6 14 8 22 9 2 0 4 1 7 1h0l2 2h1c1 1 1 2 2 2v1c-14-2-29-4-39-15l1-1z"></path><path d="M592 364c1 0 2 0 3 1 2 0 6 0 8 1 1 1 3 2 5 2l-3 1 6 3c9 4 13 11 15 20l-2-4c-5-7-15-14-23-18-2-1-5-2-7-3v-1c-1 0-1-1-2-2z" class="D"></path><path d="M595 365c2 0 6 0 8 1 1 1 3 2 5 2l-3 1c-3 0-5-1-8-2h-1c-1-1 0-1-1-2z" class="F"></path><defs><linearGradient id="AJ" x1="512.101" y1="584.27" x2="510.898" y2="609.254" xlink:href="#B"><stop offset="0" stop-color="#39393d"></stop><stop offset="1" stop-color="#5e5e5a"></stop></linearGradient></defs><path fill="url(#AJ)" d="M505 581c4 5 6 9 9 15 1 2 2 4 3 7h0c0 2 1 4 2 6l3 12c-6-7-9-16-12-24-2-4-3-7-5-12v-4z"></path><path d="M582 325l1-1c3-4 4-9 6-14h8c-1 3-1 9-4 12-1 1-3 2-4 4 0 1-1 1-2 1-2 1-3 3-5 3v-2-3z" class="E"></path><path d="M639 545c-6 4-11 8-18 8s-14-5-19-10c-2-2-4-5-4-7 6 8 11 12 21 13h3c-5-2-11-3-15-6 1 0 0-1 1 0 6 3 11 3 17 3l1 1c1 0 3-1 4-1h2c2-1 4-1 7-1z" class="L"></path><path d="M725 311c2 2 5 3 6 6l3 6c2 3 4 7 5 11 2 6 0 12-3 18l-2 2h0c0-4 1-8 1-12 0-5-2-10-3-15-2-6-5-11-7-16z" class="O"></path><path d="M457 313c2-1 5-1 7 0 2 0 5 0 7 3 0 3-2 6-3 9l-2 2c-2-1-3-1-5-2-2-2-4-4-4-6-1-3-1-4 0-6z" class="E"></path><path d="M609 397c5 1 9 3 11 9 3 5 5 13 3 20-1 3-3 5-5 8 3-9 3-16-2-25h0c-2-4-5-6-8-9l2-1c0-2 0-2-1-2z" class="D"></path><defs><linearGradient id="AK" x1="588.265" y1="629.163" x2="606.602" y2="598.559" xlink:href="#B"><stop offset="0" stop-color="#68635e"></stop><stop offset="1" stop-color="#9e9a88"></stop></linearGradient></defs><path fill="url(#AK)" d="M605 590c2 14-4 32-11 44-2 1-4 6-6 6 3-6 7-13 9-20s3-15 3-22v4l1-4c0-1 1-1 2-1h1v-1c0-2 0-4 1-6z"></path><path d="M616 107h1c0 2-15 8-18 11-11 8-18 21-23 34l-1 1c0-4 3-11 5-14 4-12 11-22 23-27 4-2 8-3 13-5h0z" class="E"></path><path d="M601 546c6 4 10 8 17 8 7 1 15 0 20-5h1 0l-2 3c-1 1-3 2-4 3-5 3-14 6-20 5-7-2-9-8-12-14zM445 223c5-1 10 1 14 2l1 1c3 1 6 2 8 4l5 3 1 1 8 6c1 2 3 3 4 5-2-1-4-3-6-4s-4-3-7-4c-5-3-11-5-17-6l2-2c0-1 0-1-1-1h-1c-5-1-13-2-18-1h0-1l1-1h1c2-1 2-1 4-1 1 0 1 0 2-1v-1z" class="L"></path><path d="M445 223c5-1 10 1 14 2l1 1c3 1 6 2 8 4-1 0-1 0-2-1-1 0-2 0-3-1l-6-2h-3 0c-2-1-9-1-11-1 1 0 1 0 2-1v-1z" class="G"></path><path d="M400 334c2-1 4-2 6-2 4-1 9-3 13-4l-13 13c-4 1-10 6-13 10l-1 2h0 0c0-5 4-9 8-11v-2c-1 1-3 2-4 3v-2h0v-1-1l3-2h0v-1c-2 1-4 3-6 4v-1c0-1 2-2 2-2 2-1 4-2 5-3z" class="D"></path><path d="M435 292c0-1 1-2 1-2l1 1h1 0l-1 1 1 1c-1 2-2 4-4 6-2 3-5 7-9 10-1 1-3 2-5 3l-9 3-2-2h0c0-1 1-1 2-2 6-4 13-8 19-13 1-2 4-4 5-6z" class="G"></path><path d="M438 293c-1 2-2 4-4 6-2 3-5 7-9 10-1 1-3 2-5 3l-9 3-2-2h0c0-1 1-1 2-2 12-1 19-9 27-18z" class="C"></path><path d="M542 584c-1-2-1-3-2-5h0l1-1s0 1 1 1v-3c-1-2 0-4 0-6h0v3 1l1 1v-1l1 1c1 3 1 6 2 9 2 5 4 10 7 15 0 0 1 2 2 3 3 3 5 6 9 8 4 3 8 6 11 9-2 0-7-3-9-4-11-6-20-19-24-31h0z" class="F"></path><defs><linearGradient id="AL" x1="496.19" y1="208.46" x2="502.833" y2="203.119" xlink:href="#B"><stop offset="0" stop-color="#c3bba8"></stop><stop offset="1" stop-color="#e1dcc6"></stop></linearGradient></defs><path fill="url(#AL)" d="M485 182c1 0 1 0 2 1 11 13 24 27 26 44 1 7 1 14 1 21v1-1c0-1 0-2-1-3 0-3 0-7-1-11-2-11-6-23-14-32-4-7-11-13-13-20z"></path><path d="M597 244c11 0 21 1 31 4 6 2 11 5 16 8l-14-3c-14-5-32-9-48-4h0v-1h1c5-2 9-3 14-4z" class="J"></path><path d="M420 312l1 1c2 0 3-2 4-1-1 1-1 1-2 1v1c-1 1-3 2-4 2l-3 1c-2 1-6 2-7 3-2 1-4 2-6 2-4 1-9 2-12 4 1 0 2 0 3-1h0 4 0 2l2-1h2 1c1-1 2 0 3-1v1h0l-26 5c3-3 6-5 9-7 6-3 13-6 20-7l9-3z" class="N"></path><path d="M420 312l1 1c2 0 3-2 4-1-1 1-1 1-2 1v1c-1 1-3 2-4 2-3-1-2 0-5 0h-1l-1 1h-2c-1 1-4 1-5 2h-2l-6 2c-1 1-2 1-3 2h-1l-2-1c6-3 13-6 20-7l9-3z" class="T"></path><path d="M646 299h0v-1l4 3v-1l9 7c7 6 16 11 25 16 4 3 8 5 12 9-12-5-24-11-34-18-6-4-11-9-16-15z" class="G"></path><path d="M539 278c2-5 2-10 4-15 3-8 9-14 15-20 3-3 5-6 9-8 3-1 5-3 8-4 11-5 24-6 36-4 2 0 3 0 4 1-8 0-16-1-25 0-11 3-22 8-30 16-3 2-4 5-6 7-2 3-4 5-6 7l-3 5c-3 6-4 11-6 16v-1z" class="I"></path><path d="M514 213v-2-3h0c2 2 2 6 4 8l2 1v-5c1 10 2 20 2 29 1 7 0 15-1 22-1 3-1 6-2 9v-2-31c-1-8-1-17-5-26h0z" class="M"></path><defs><linearGradient id="AM" x1="557.026" y1="569.73" x2="541.151" y2="581.509" xlink:href="#B"><stop offset="0" stop-color="#63635c"></stop><stop offset="1" stop-color="#8c887a"></stop></linearGradient></defs><path fill="url(#AM)" d="M543 574c-1-9-1-17 1-27 0-2 1-4 2-6l-1 4c-2 14 1 31 7 44 1 2 2 5 3 7h0 1c1 2 2 5 3 7h-1l-2-2h0l-1 1c-1-1-2-3-2-3-3-5-5-10-7-15-1-3-1-6-2-9l-1-1z"></path><path d="M645 461l2-1h0v1 1h0l2-2c0 2 0 1-1 3h0c-5 4-12 8-15 14-6 10-8 21-6 32v2c-1 3 0 4 0 7l-2-4c-1-5-2-9-2-14 0-9 2-19 8-26 2-2 4-4 7-6 2-2 5-4 8-6l-1-1z" class="F"></path><defs><linearGradient id="AN" x1="575.105" y1="637.412" x2="570.129" y2="618.396" xlink:href="#B"><stop offset="0" stop-color="#63645d"></stop><stop offset="1" stop-color="#878172"></stop></linearGradient></defs><path fill="url(#AN)" d="M581 640c-5-9-14-11-21-17l-6-6v-1c2 2 4 5 7 6h0c0-1-1-1-1-2l2 1h1l-5-6c3 2 6 4 10 5 3 1 7 3 10 5 1 1 2 3 3 4v5h1c0 3 0 4-1 6z"></path><path d="M472 367c2 0 4-1 6-2-4 4-9 7-12 11-4 6-7 13-8 20v8c-1 1 0 1-2 1-1-3-2-4-3-7v-3c0-2 0-4 1-6 2-10 10-17 18-22z" class="M"></path><defs><linearGradient id="AO" x1="592.109" y1="157.499" x2="596.775" y2="138.31" xlink:href="#B"><stop offset="0" stop-color="#474748"></stop><stop offset="1" stop-color="#767467"></stop></linearGradient></defs><path fill="url(#AO)" d="M603 135c1 0 2-1 3-1 0 1 0 1-1 1-1 2-3 3-4 4l1 1h1c0-1 0-1 1-1-2 2-4 4-4 6h0l3-3 1 1v1l-1 1 1 1c-1 0-1 1-2 2 1 0 2-1 2-2l1 1-1 1c-4 3-7 3-10 5s-5 4-7 6v1c0-10 3-17 9-24l1 1c0 2 0 2 1 3l3-3h1c1-1 1 0 1-2z"></path><path d="M423 138h2 3c-2-2-4 0-5-1 0-1 0-1 1-1 1-1 2-1 3 0h3c8 2 18 6 23 13 1 1 1 1 1 2v2c-2-1-4-1-5-2-11-7-21-10-33-11 2-1 4-1 7-2z" class="N"></path><path d="M423 138h2 3c-2-2-4 0-5-1 0-1 0-1 1-1 1-1 2-1 3 0h3c8 2 18 6 23 13 1 1 1 1 1 2l-2-2c-7-8-19-11-29-11z" class="C"></path><defs><linearGradient id="AP" x1="583.401" y1="535.416" x2="565.099" y2="548.084" xlink:href="#B"><stop offset="0" stop-color="#84826c"></stop><stop offset="1" stop-color="#c7c0b3"></stop></linearGradient></defs><path fill="url(#AP)" d="M578 567c-11-23-17-51-8-76 1-5 3-10 6-14h0c0 2-2 5-3 7-1 4-3 8-4 13-3 18 0 39 6 56l9 20h-2s-1-3-2-4c0-1-1-1-2-2z"></path><path d="M667 438c-3 18-18 33-26 50-3 8-5 16-6 24-1-3-1-7 0-10 1-22 16-36 27-54 2-3 3-7 5-10z" class="E"></path><path d="M622 266c3 2 5 3 6 5 4 4 7 10 10 13l5 5c5 5 10 9 15 13v1l-1 1 2 3-9-7v1l-4-3v1h0a57.31 57.31 0 0 1-11-11c-4-4-6-8-9-12v-3l-5-4s1 0 2 1h0 1l1 1c2 1 3 3 5 4h1c-1 0-1-1-2-1-1-2-2-4-4-5-1-1-2-1-2-2l-1-1z" class="K"></path><path d="M626 273c5 4 8 10 12 15l8 8 4 4v1l-4-3v1h0a57.31 57.31 0 0 1-11-11c-4-4-6-8-9-12v-3z" class="I"></path><path d="M513 562l-1-4-1 2h0c0-8 0-16 3-23l1-1c0-2 1-4 3-6 2-3 8-9 12-10v1c-2 1-4 2-5 4v1c-3 2-5 6-7 10-2 3-5 7-5 11h1l2-6c3-7 7-12 13-18 1-1 3-3 5-3 3-1 4-2 6-4h0c0 1-1 2-2 3h1l-11 11c-1 2-3 3-4 5-2 2-3 3-4 5-3 4-6 9-7 13v9z" class="C"></path><path d="M585 433h0l-6 22c-3 10-8 18-17 23-8 5-14 4-22 2h-2l1-1c2 0 6 1 8 0 16-3 25-15 30-30 2-5 4-11 7-16h1zm76-109h-1 0c6 10 14 21 19 33 6 12 9 26 9 40v-2c-3-29-17-54-35-76-6-9-14-17-17-27 2 2 3 4 5 6 3 4 7 8 10 11 4 4 7 8 11 11 3 2 7 5 10 7h-2c-1-1-2-1-3-2l-9-7c0 3 2 3 3 6h0z" class="E"></path><path d="M607 222l15 5h1c2 0 6 2 7 3v1c-1 1-3 0-5 0-3-1-5-1-8-2 0 0-1-1-2-1-1-1-2-1-4-1-12-2-25-1-36 4-3 1-5 3-8 4-4 2-6 5-9 8-6 6-12 12-15 20-2 5-2 10-4 15l-1-1 3-13c6-16 17-27 32-34 11-4 19-6 30-5l4-3z" class="C"></path><path d="M607 222l15 5c-3 1-5-1-8-1l-11-1 4-3z" class="L"></path><path d="M619 242v-1c-2-1-2-1-4 0l1-2c-1 0-2 0-2-1h1 1 1c14 1 28 3 41 4 7 1 14 1 21 0-13 5-25 5-39 3-7 0-14-1-21-3z" class="I"></path><defs><linearGradient id="AQ" x1="420.845" y1="171.706" x2="411.155" y2="182.294" xlink:href="#B"><stop offset="0" stop-color="#928c7e"></stop><stop offset="1" stop-color="#b9b3a2"></stop></linearGradient></defs><path fill="url(#AQ)" d="M412 184l2 2v-1c-3-4-6-9-8-13-3-7-5-14-6-20 0-4-1-8-1-11 3 9 5 20 10 29 3 4 6 8 10 12 6 7 16 14 12 25h0v-1c0-5-1-8-5-11-4-4-10-6-15-10l1-1z"></path><path d="M528 504v-1h1c0 1 0 1 1 1l2-2 1 2c1-1 1-2 2-2l1 1c0-1 1-1 1-2h1 1l2-2 1 1s1 0 1-1v-1l2 1v-1c1 0 2 0 2-1 2-2 3-4 5-5h3c0 1 0 2-1 3-2 2-4 4-6 5-6 5-14 9-22 10-10 2-20-3-28-9 1-1 2-1 3-2l1 1h1c1 0 1 1 1 2 2 0 1-2 2-1l1 1h0 1c1 0 1 1 2 1l1-1c0 1 1 1 1 2l1 1h1v1c2 0 3 1 5 1h1c2 1 4 1 5 0l1-1h1c1 0 1-1 1-2z" class="V"></path><defs><linearGradient id="AR" x1="571.001" y1="383.734" x2="595.061" y2="380.341" xlink:href="#B"><stop offset="0" stop-color="#c2b5a2"></stop><stop offset="1" stop-color="#dcd6bc"></stop></linearGradient></defs><path fill="url(#AR)" d="M560 363h1c4 2 8 2 12 3 8 1 14 4 19 9 4 5 6 9 5 15v1c-1 4-4 9-8 12v-1-1c2-4 2-12 1-16h0c-2-10-15-15-23-18l-6-3-1-1z"></path><path d="M592 375c4 5 6 9 5 15v1l-1-3v-2c-1-3-3-6-5-9 1-1 1-1 1-2z" class="M"></path><path d="M463 424c0 4 3 9 4 13 3 7 3 16 6 24 1 2 3 5 4 8 6 7 14 11 23 12-3 1-8 0-11 0-5-2-8-4-12-8s-7-9-8-15c-3-10-3-20-8-29s-14-15-19-23l1-1c3 5 7 7 11 10 3 2 6 5 8 7l1 1v1h0 0z" class="I"></path><path d="M419 164l7 3c4 2 7 5 11 5 2 0 3 0 4 1l1 2 2 1 1 1v1l-1-1c0 1 1 2 1 3 0 7 1 15-3 21 0 1 0 1-1 2h-1v-3c0-1-1-2-1-3v-1h0l-1 2v-10c-2-8-6-12-12-17h1c-3-3-5-5-8-6v-1h0z" class="F"></path><defs><linearGradient id="AS" x1="446.252" y1="188.903" x2="439.098" y2="174.209" xlink:href="#B"><stop offset="0" stop-color="#66685f"></stop><stop offset="1" stop-color="#827b6d"></stop></linearGradient></defs><path fill="url(#AS)" d="M437 175l-1-2c1 0 1 0 2 1 2 0 3 0 4 1l2 1 1 1v1l-1-1c0 1 1 2 1 3 0 7 1 15-3 21-1-3 1-5 1-8 1-8-1-13-6-18z"></path><defs><linearGradient id="AT" x1="438.099" y1="174.873" x2="426.897" y2="177.861" xlink:href="#B"><stop offset="0" stop-color="#6e695e"></stop><stop offset="1" stop-color="#7e7d6d"></stop></linearGradient></defs><path fill="url(#AT)" d="M419 164l7 3c4 2 7 5 11 5 2 0 3 0 4 1l1 2c-1-1-2-1-4-1-1-1-1-1-2-1l1 2s4 6 4 7c1 3 1 11-1 14 0-3 0-7-1-10-1-6-7-12-12-15h0c-3-3-5-5-8-6v-1h0z"></path><path d="M495 561h1c5 6 9 13 15 19 5 5 11 9 16 14 7 7 10 13 10 22-4-6-7-11-12-15-3-2-7-5-9-8-5-6-10-14-14-21-3-4-5-7-7-11z" class="D"></path><path d="M700 289h0c2-1 5 0 7-1 12 2 25 9 33 19 5 6 5 11 5 19-1-4-3-7-5-10-6-9-19-18-29-22l-11-5z" class="P"></path><defs><linearGradient id="AU" x1="388.759" y1="200.568" x2="409.241" y2="189.432" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#727066"></stop></linearGradient></defs><path fill="url(#AU)" d="M395 158l1-1c1 10 4 20 9 29 2 5 5 10 7 15v1c5 15-8 12-9 23 0 1 0 0-1 1-1-1-1-2-2-3-1-4 5-8 8-11v-3c0-7-5-14-8-21-1-4-2-7-3-11-1-6-1-13-2-19z"></path><path d="M489 490c0-1 0-4-1-5l1-1h1c4 3 10 3 15 3 2 0 5-1 8 0s8 0 11 0h2l1 1c-1 0-1 1-1 2 1-2 0-3 2-3h3c0 2-1 3-1 5v1h0c-1 0-1 1-1 1l-1 1h-3c-2-2-2-2-2-4l-2 1v1c0 1 0 1-1 2h-3-3v-1l-1 1c-2 0-4 0-5-1 1-1 1-3 2-4 0-1 1-2 1-2h-1l-1 1c0 2-1 5-3 5l-1 1-1-1c-4 0-9-3-12-5-1-1-1-2-3-3l1 3-1 1z" class="J"></path><path d="M505 495l1-5c1-1 2-2 3-2v1c0 2-1 5-3 5l-1 1zm-13-6c2-1 4-1 6-1v2h1v-1h1v2h1l1-2c0 2 0 2-1 3l1 1c1-1 2-3 2-4l1 1c0 1-1 3-1 4-4 0-9-3-12-5z" class="R"></path><path d="M617 191c2-3 4-6 7-8 3-4 9-7 14-7 3 0 6 2 9 3-10 4-23 12-28 22-1 1-2 3-2 4l-1 8-1-6c-1-5-1-11 2-16z" class="U"></path><path d="M617 191l1 1c2-3 4-5 7-6v-1c3-1 5-3 8-4h1 1l-5 3c-2 1-4 2-6 4-2 1-4 4-6 5-1 4-3 8-2 11l1 1-1 8-1-6c-1-5-1-11 2-16z" class="J"></path><defs><linearGradient id="AV" x1="487.625" y1="214.896" x2="492.392" y2="208.324" xlink:href="#B"><stop offset="0" stop-color="#c2b89e"></stop><stop offset="1" stop-color="#e2dac1"></stop></linearGradient></defs><path fill="url(#AV)" d="M498 227h0c-2-4-5-7-7-10-6-7-10-12-17-17-3-2-6-3-9-5 16 5 30 10 38 25 3 5 6 13 7 19l-3-6c-2-2-3-3-4-5v1 2c-1-2-1-3-2-4 0-1-1 0-2 0l3 7h0l-2-3c0-1-2-3-2-4z"></path><path d="M499 227c-3-5-5-10-8-15l3 3c4 4 7 8 10 12l3 6c-2-2-3-3-4-5v1 2c-1-2-1-3-2-4 0-1-1 0-2 0z" class="C"></path><path d="M468 501l4 2c19 14 31 34 35 57-2-1-4-5-5-7l-7-14c-8-13-17-26-27-38z" class="D"></path><path d="M702 251l2-1c-5-5-11-10-16-14-6-4-12-7-16-12-10-8-15-18-17-30 2 4 3 8 5 12 5 8 12 15 20 20 4 3 8 5 12 7 8 5 14 11 20 18h-10z" class="N"></path><path d="M388 209h1a30.44 30.44 0 0 1 8-8c2 1 4 2 4 4 0 4-7 12-10 14l-3 2-2 2h1l6-4 1 6c-6 3-12 5-17 10-2 1-4 4-5 6-2 3-4 5-6 8h0l-1-1h1v-2c2-3 5-6 6-8 1-1 1-2 1-3l1-1h-1v-1h1c3-2 5-6 7-9 1-2 3-5 5-7-1-1-1-1-1-2 1-1 2-4 3-5v-1z" class="C"></path><path d="M445 327c6 5 11 7 18 9 3 2 7 3 11 4h11c-4 4-8 7-14 9l13-9c-6 2-13 6-18 5h0c-1-1-1-1-2-1s-2-1-4-2h0c-6-2-15-4-22-5v-2-1c1 0 2 0 3-1-1 0-1 0-1-1h4 0c-2-1-4-2-6-2 1-1 1-2 2-3h5z" class="G"></path><defs><linearGradient id="AW" x1="454.219" y1="366.231" x2="447.298" y2="351.768" xlink:href="#B"><stop offset="0" stop-color="#b4aa99"></stop><stop offset="1" stop-color="#cdc9b7"></stop></linearGradient></defs><path fill="url(#AW)" d="M467 351h8 16c-9 3-19 4-28 5-5 0-11 1-16 2-10 2-16 7-21 16-3 5-4 10-5 17v-3c-1-10 3-20 10-27 9-10 23-9 36-10z"></path><path d="M482 231c-4-4-8-8-13-11-7-4-15-6-23-9 12-1 29-2 38 6l1 1 1 2h0c1 1 1 1 1 3v3l-2-1 1 1-1 2c-1-2-3-4-5-5h0v1c2 1 4 3 5 5h0v1c1 1 2 2 3 4v1l1 1h-1c-2-2-3-5-6-5z" class="I"></path><defs><linearGradient id="AX" x1="459.3" y1="280.913" x2="461.341" y2="271.59" xlink:href="#B"><stop offset="0" stop-color="#b9b09a"></stop><stop offset="1" stop-color="#dbd4bf"></stop></linearGradient></defs><path fill="url(#AX)" d="M417 280c5-4 9-7 14-9 7-3 14-3 22-2 9 2 17 4 26 8 1 1 2 1 2 2l2-1c6 5 10 10 14 16-9-8-19-15-30-18-8-2-15-2-23-2-9 0-18 2-26 6h-1z"></path><path d="M445 372s-1 1-2 1c-2 4-2 8-2 11l1 3c-2-2-2-4-3-7 0-4 1-7 3-10 4-4 9-4 13-5l1 1c1 0 2-1 3-1-2 2-4 4-5 7-3 3-7 10-6 15v1c1 1 1 3 2 5 0-2-1-4-1-6-1-6 4-14 8-18 7-9 23-14 34-15l-18 8h-1c-8 4-16 11-19 20-2 5-1 8-1 13 0 1 1 2 1 3l-1 1c-2-5-5-9-6-15 0-8 4-12 9-17v-1c-4 3-6 5-8 8-1-1-1-1-1-2h-1z" class="M"></path><path d="M422 226c9-3 18-6 28-5 9 1 16 4 23 10 3 2 6 4 8 6-2-1-4-2-6-4-6-4-11-8-18-9-5-2-11-3-16 0l4-1v1c-1 1-1 1-2 1-2 0-2 0-4 1h-1l-1 1h1 0c5-1 13 0 18 1h1c1 0 1 0 1 1l-2 2c-5-1-9-1-13 0-8 1-14 4-22 5-1 0-2 0-3-1h0v-1-1c0-2 0-3 1-4s2-1 2-2l1-1z" class="I"></path><path d="M428 437l12 1c2 0 5 1 8 2 4 2 8 4 10 9 2 2 2 6 2 9 0 4 0 9 2 13 2 8 7 15 11 22-3-1-12-11-13-14-3-6-3-12-5-18-3-10-10-15-18-19 7 2 13 3 18 9h0 1c-5-7-11-8-19-11l-9-3z" class="O"></path><path d="M442 248c2-1 2-1 3 0h1 3c8-2 16 1 22 6 7 4 12 10 17 16 1 1 1 1 1 2v1l-1-1c0-1 0 0-1-1-1 1 0 1-1 2-3-3-5-6-8-9-1-1-3-2-4-3-4-2-8-4-13-6-7-2-17-1-25 0l-10 2v-1h1c-1-1-1-1-3-2h0v-1h1l5-1c1 0 2-1 2-1l6-1c0-1 1-1 2-1h2v-1z" class="V"></path><path d="M427 256c11-1 21-5 31-2 4 0 7 2 10 3 2 0 3 1 6 2v-1l-1-1c0-1-1-1-2-2v-1c7 4 12 10 17 16 1 1 1 1 1 2v1l-1-1c0-1 0 0-1-1-1 1 0 1-1 2-3-3-5-6-8-9-1-1-3-2-4-3-4-2-8-4-13-6-7-2-17-1-25 0l-10 2v-1h1z" class="C"></path><path d="M471 254c7 4 12 10 17 16-2 0-5-4-6-5-4-3-9-6-14-8 2 0 3 1 6 2v-1l-1-1c0-1-1-1-2-2v-1z" class="G"></path><defs><linearGradient id="AY" x1="329.589" y1="214.782" x2="371.009" y2="223.238" xlink:href="#B"><stop offset="0" stop-color="#8c8470"></stop><stop offset="1" stop-color="#ada691"></stop></linearGradient></defs><path fill="url(#AY)" d="M377 188c1 1 1 0 1 1l1 1c-8 17-20 32-33 45-5 5-10 11-16 16-3 0-7 0-10-1 10-7 19-14 27-23 1-1 3-3 5-4 10-11 18-22 25-35z"></path><path d="M426 257l10-2c8-1 18-2 25 0 5 2 9 4 13 6h0l-2 1c1 1 2 2 2 3-7-3-11-7-19-6l-1 1h4c-1 1-1 2-1 3 1 0 2 0 3 1h-5c-3-2-7-1-10-1-2 1-3 1-5 1-6 0-12 0-18 1l1-6c1-2 1-2 3-2z" class="I"></path><path d="M416 118h0l-2-3c-1-1-2-2-2-3v-1c-1-1-1-1-1-2 1 0 2 3 3 4 0 1 1 1 1 2 1 2 2 3 4 4h0l-1-2 1-1c0 1 1 1 1 1h1c-2-2-4-4-5-6v-1c4 3 7 5 11 6 4 2 9 2 13 4 7 2 13 10 15 17l-1-1c0 2 0 3 1 4h0c-6-4-12-7-18-10-7-4-16-5-21-12z" class="S"></path><defs><linearGradient id="AZ" x1="620.572" y1="331.685" x2="583.66" y2="330.989" xlink:href="#B"><stop offset="0" stop-color="#9f9886"></stop><stop offset="1" stop-color="#cec5ae"></stop></linearGradient></defs><path fill="url(#AZ)" d="M603 322c4 1 6-4 10-1h-2v1h0c1 1 1 0 1 1h3c1 1 4 1 5 2 3 1 6 4 9 5v1c-2 0-5-3-7-3-1 0-3 0-4 1 2 0 5 0 7 2h0c-2 0-6-1-7-1l2 2c-2 0-5-2-7-1 3 1 6 2 9 4 2 3 3 2 5 4-1 0-2 0-4-1 0 0-1 0-1-1l-1 1-9-2c-6-1-11 1-17 3-2 0-2 0-5 1-2 1-5 1-8 1l1-2 4-1c6-3 14-9 16-16z"></path><path d="M443 240c14-3 27 0 38 7s17 17 20 29c-4-5-7-10-11-14-2-3-5-4-7-6-7-4-15-9-23-10-3 0-7 0-10 1l-5 1c-1-1-1-1-3 0h-1-3 0c1-1 3-1 5-2h0c-1-1-2 0-3 0h-2c2-1 5-2 8-4h-1l-2-2z" class="I"></path><defs><linearGradient id="Aa" x1="605.137" y1="514.557" x2="568.47" y2="533.648" xlink:href="#B"><stop offset="0" stop-color="#645f54"></stop><stop offset="1" stop-color="#aea899"></stop></linearGradient></defs><path fill="url(#Aa)" d="M590 463c0 2-2 4-3 6l-3 8c-4 9-8 19-9 30-1 13 3 28 7 40 5 15 11 29 13 45v4-1c-1-4-2-9-3-13-4-11-9-21-13-31-5-12-8-26-8-39 0-16 5-29 14-42 1-2 3-5 5-7z"></path><defs><linearGradient id="Ab" x1="562.285" y1="525.539" x2="548.321" y2="596.186" xlink:href="#B"><stop offset="0" stop-color="#62615c"></stop><stop offset="1" stop-color="#aea792"></stop></linearGradient></defs><path fill="url(#Ab)" d="M553 590c-1 0-2-6-3-7-5-21-4-46 7-65l-3 15c-2 15-1 30 5 45 0 2-3 5-2 8v1 2c1 3 1 5 1 8v2c-1-2-2-7-5-9z"></path><defs><linearGradient id="Ac" x1="577.75" y1="571.833" x2="605.075" y2="547.133" xlink:href="#B"><stop offset="0" stop-color="#9c9987"></stop><stop offset="1" stop-color="#e2d7c2"></stop></linearGradient></defs><path fill="url(#Ac)" d="M600 598c-1-12-4-23-7-34-6-17-14-34-13-52 1 5 2 10 4 15l11 25c2 6 4 13 6 19 1 6 3 13 4 19-1 2-1 4-1 6v1h-1c-1 0-2 0-2 1l-1 4v-4z"></path><defs><linearGradient id="Ad" x1="546.463" y1="611.305" x2="544.037" y2="646.195" xlink:href="#B"><stop offset="0" stop-color="#b9b09f"></stop><stop offset="1" stop-color="#f6f2d4"></stop></linearGradient></defs><path fill="url(#Ad)" d="M522 621l-3-12c-1-2-2-4-2-6 6 8 12 16 20 22 8 4 16 5 24 9 4 3 8 8 10 12 1 4 2 8 2 12h0c-1-2-1-3-1-5l-6-9c-5-5-13-6-20-8-9-2-19-7-24-15z"></path><defs><linearGradient id="Ae" x1="681.228" y1="438.979" x2="707.125" y2="374.555" xlink:href="#B"><stop offset="0" stop-color="#868074"></stop><stop offset="1" stop-color="#b4ab98"></stop></linearGradient></defs><path fill="url(#Ae)" d="M704 368c9 28-7 59-20 83-4 7-8 14-14 19 3-6 7-10 10-16 14-24 22-51 17-78 3 0 5-3 7-3h1l-1-1v-1-3z"></path><path d="M615 530l-2 1c-7-3-14-8-18-14-6-9-9-18-7-29 1-10 4-20 10-28 0 4-1 8-2 12-1 10-1 21 1 31 1 3 2 6 4 9 2 6 5 10 9 14h1l4 4z" class="D"></path><path d="M601 239c-5 0-11 1-16 3-16 5-27 17-34 31l1-2c3-12 13-26 23-32 23-13 47-6 71 0h-1c-4 0-7 0-10-1-5 0-10 0-14-1l-4 1h-1-1-1c0 1 1 1 2 1l-1 2c2-1 2-1 4 0v1l-18-3z" class="I"></path><path d="M601 239l-1-1c1-1 2 0 3 0h10c0-1-1-1 0-1h8l-4 1h-1-1-1c0 1 1 1 2 1l-1 2c2-1 2-1 4 0v1l-18-3z" class="G"></path><defs><linearGradient id="Af" x1="676.657" y1="231.661" x2="696.105" y2="190.406" xlink:href="#B"><stop offset="0" stop-color="#8c8873"></stop><stop offset="1" stop-color="#aba194"></stop></linearGradient></defs><path fill="url(#Af)" d="M649 161c1 1 2 2 2 3l2 5 1 2h2c0-1-1-1-1-2v-1-2l-1-1v-2h1 0c2 6 3 11 5 17 6 16 16 28 28 40l6 6c1 2 3 3 5 5 2 1 3 4 5 5 6 6 12 10 19 15h-9c-8-7-15-14-22-20v-1s-1-1-2-1c-12-11-22-20-28-35-2-5-3-10-5-15-3-6-7-11-8-18z"></path><path d="M508 434h1v-1h1c2-1 5-1 6 0h2 0l1 2h1c4 0 11-1 15 0v-1l1 1c1 0 1 1 2 1l1-1c2 1 1 3 4 3 2 0 1 0 3 1 1 0 2-1 3-1 1 2 2 5 2 7l-34 1c-7 0-14 0-22 2 1-2 3-5 4-6v-1c0-1 0-3 1-3 1-1 2-1 3-1 2-1 3-3 5-3z" class="G"></path><path d="M508 434h1v-1h1c2-1 5-1 6 0h2 0l1 2h1c4 0 11-1 15 0v-1l1 1 1 1v1h0c-1 0-1 1-2 1-3 2-5 1-7 2h0-1-1-3v-1h0c-5 1-8 1-12 1h-1l-1-1h-1l-1-2c0-1 1-2 1-3z" class="L"></path><path d="M508 434h1v-1h1c2-1 5-1 6 0h2 0l1 2c-1 0-2 1-4 1v-1c-2-1-5 0-6 1l-1 1v2l-1-2c0-1 1-2 1-3z" class="K"></path><path d="M559 578c5 10 11 17 19 25v-1c-7-9-17-18-18-31v1l6 12c3 5 7 9 11 14 1 2 3 4 3 6v1l-1 1c1 2 1 3 1 4-1 0-1 1-1 1-2-3-7-9-11-11 3 4 7 7 10 12l1 2v1c0 2 1 2 1 4 1 1 1 2 1 3h0-1-1l-3-3h-1c-3-3-7-6-11-9-4-2-6-5-9-8l1-1h0l2 2h1c-1-2-2-5-3-7l-3-6c3 2 4 7 5 9v-2c0-3 0-5-1-8v-2-1c-1-3 2-6 2-8z" class="N"></path><path d="M555 602l1-1h0l2 2h1c3 1 5 4 7 6s4 3 7 3c0 0 1 1 2 1h2v1h-2c-1-1-2-1-4-1-1 0-1-1-2 0h0l7 6h-1c-3-3-7-6-11-9-4-2-6-5-9-8z" class="Q"></path><defs><linearGradient id="Ag" x1="681.916" y1="378.348" x2="680.215" y2="316.871" xlink:href="#B"><stop offset="0" stop-color="#b3aa97"></stop><stop offset="1" stop-color="#f0e8cb"></stop></linearGradient></defs><path fill="url(#Ag)" d="M661 324h0c-1-3-3-3-3-6l9 7c1 1 2 1 3 2h2c15 10 28 22 32 40v1 3 1l1 1h-1c-2 0-4 3-7 3-1-6-2-15-6-21h0c2 9 5 18 5 27 0-1-1-2-1-3h-2c0 1 1 2 1 4h-1v-3l-2-9c-4-14-10-28-19-40l-1 1 3 5h0c-1 0-1-1-2-1h-2c0 1 0 1 1 2v1l-6-9c-2-2-3-4-4-6z"></path><path d="M544 292c1-4 3-8 5-11 4-7 13-13 19-16 11-4 25-5 37-2l15 6h1l5 4v3c-8-7-23-8-33-6-6 0-11 1-16 3-2 1-5 3-7 3v-1c-3 2-7 4-10 7-7 5-11 12-16 20v-1c0-3 0-5 2-8l-1-1v1l-1-1z" class="M"></path><path d="M526 645v-2l1 1c1 0 1 1 2 1 2 2 4 3 6 4 7 3 14 3 21 7 6 3 10 10 11 17 1 5 1 11 0 17v1c0 3-1 5-2 8h0c-1-2-1-4-1-5 0-5-1-9-2-13-2-5-5-11-10-15-4-3-10-6-15-8l10 7c5 2 8 6 11 11 4 8 5 18 3 27-1 4-2 6-5 9 0-2 1-5 1-8 2-8 1-18-4-25h-1c-4-7-12-12-16-19l-10-15z" class="D"></path><path d="M564 694v-1c1-2 1-8 1-10l-2-5v-1l-1-2v-1c-1-1-1-2-1-2l-1-1-2-3v-1c2 2 4 4 5 7 1 0 1 1 1 2v1l1 2c1 1 1 2 1 3 0 2 0 7 1 9h0c0 3-1 5-2 8h0c-1-2-1-4-1-5z" class="C"></path><defs><linearGradient id="Ah" x1="430.955" y1="196.254" x2="433.833" y2="145.348" xlink:href="#B"><stop offset="0" stop-color="#6a685e"></stop><stop offset="1" stop-color="#b0a995"></stop></linearGradient></defs><path fill="url(#Ah)" d="M412 156c5 3 10 4 16 5 3 1 5 1 8 1-2-1-4-1-6-2-5-1-12-4-16-7l20 5c-2-1-5-2-7-4-8-4-19-7-25-14l15 6c16 5 31 7 38 24 3 7 3 15 2 23h0l-3 6c-2-1-2-2-3-5 1-1 1-3 1-5 1-4 2-11-1-14h0c1 6 0 13-1 18l-2 1c-1-1-1-2-1-4 0-3-1-6-2-10 0-1-1-2-1-3l1 1v-1l-1-1-2-1-1-2c-1-1-2-1-4-1-4 0-7-3-11-5l-7-3c-2-2-5-2-7-4 0-1 0-1-1-2-1 0-1-1-1-2h0v-1-1l1 1 1 1z"></path><path d="M412 160c0-1 0-1-1-2-1 0-1-1-1-2h0v-1-1l1 1 1 1c5 5 14 8 21 10 4 2 7 3 9 6 1 1 2 2 2 4l-2-1-1-2c-1-1-2-1-4-1-4 0-7-3-11-5l-7-3c-2-2-5-2-7-4z" class="F"></path><path d="M434 168c4 1 6 2 7 5-1-1-2-1-4-1l-3-4z" class="C"></path><path d="M426 167c3-1 5 0 8 1h0l3 4c-4 0-7-3-11-5z" class="O"></path><path d="M596 253l-2-1h-5v-2h12c7 0 14 3 21 6 13 6 25 15 38 21l14 6c1 1 3 1 3 2l1 1c2 2 6 3 9 5 0 2-1 2-2 4l-1 1h0c-1 2-3 3-4 4-2 0-5-4-7-5l-3-2h-1c-7-4-12-9-18-13h-1c3 3 7 5 9 9 5 6 12 11 18 16h0c-6-4-13-9-19-14l-11-10c-2-1-3-3-5-4-1-1-3-2-4-3 2 1 3 1 5 2-9-7-17-14-27-19 4 4 11 7 14 13 4 5 9 10 13 15h-1c-1-1-2-1-4-1-3-3-6-9-10-13-1-2-3-3-6-5h0c-8-6-16-10-26-13z" class="I"></path><path d="M333 251c4-4 7-7 11-10 8-8 17-17 24-27 4-5 7-11 10-17 2-3 4-7 6-10v1h1c1 0 1 0 2-1l1 1c0 1-1 2-1 3l-1 2h-1v1c-2 4-2 9-3 14-2 7-4 14-7 20-3 4-5 8-8 12l-5 7c-1 1-3 2-3 3h-11l-2 1h-4-9z" class="Q"></path><path d="M384 188h1c1 0 1 0 2-1l1 1c0 1-1 2-1 3l-1 2h-1c-3 3-6 12-8 16l-6 12c-6 11-15 20-23 29l-2 1c-1 0-2 0-3-1 5-4 9-9 13-13 5-7 11-14 16-22s8-18 12-27z" class="F"></path><path d="M348 250c8-9 17-18 23-29l6-12c2-4 5-13 8-16v1c-2 4-2 9-3 14-2 7-4 14-7 20-3 4-5 8-8 12l-5 7c-1 1-3 2-3 3h-11z" class="Q"></path><defs><linearGradient id="Ai" x1="607.591" y1="190.113" x2="612.427" y2="143.372" xlink:href="#B"><stop offset="0" stop-color="#a8a090"></stop><stop offset="1" stop-color="#d8d0b7"></stop></linearGradient></defs><path fill="url(#Ai)" d="M588 185c-1-3-1-6 0-9 3-25 35-23 51-36-5 8-13 15-18 22-6 7-10 16-15 24 0-4 1-9 3-12 3-3 5-7 6-11-2 2-3 5-5 8-1 2-2 3-3 4-2 2-2 6-3 8v3c0 1 0 3-1 4l-1 1v3c0 2 0 4 1 5l-1 1-1-2-1 1c-1 0 0 1-1 0s-1 0-1-1l2 4h-1c-2-2-4-5-5-8 0-1-1-3-1-4-2-1-3-3-5-5z"></path><path d="M595 193c-2-7-1-13 2-20v4 7c-2 3 0 6-2 9z" class="H"></path><path d="M595 193c2-3 0-6 2-9 0 2 0 5 1 7v1c1 1 1 1 1 2l1-1c-1-1-1-2 0-3v1h1l1 3c0 2 0 4 1 5l-1 1-1-2-1 1c-1 0 0 1-1 0s-1 0-1-1c-1 0-2-4-3-5z" class="C"></path><path d="M609 161h0c1-1 3-3 4-3l1 1h1l1 1h0l-1 1c-5 5-12 13-11 21v1 3c0 1 0 3-1 4l-1 1v3l-1-3h-1v-1c-1 1-1 2 0 3l-1 1c0-1 0-1-1-2l1-1c-1-4-2-10-1-14 1-6 6-13 11-16z" class="D"></path><path d="M604 168c1 0 1 0 2-1v1c0 1-1 1-1 2h1c-4 8-5 13-5 21h-1v-2c-1-6 1-16 4-21z" class="L"></path><path d="M609 161h0c1-1 3-3 4-3l1 1h1l1 1h0l-1 1c-4 2-7 6-9 9h-1c0-1 1-1 1-2v-1c-1 1-1 1-2 1 1-3 3-5 5-7z" class="G"></path><path d="M606 170c2-3 5-7 9-9-5 5-12 13-11 21v1 3c0 1 0 3-1 4l-1 1v3l-1-3c0-8 1-13 5-21z" class="K"></path><path d="M539 519l2-1c3-3 8-10 12-10-5 8-9 16-15 24-3 5-7 8-9 13-5 8-6 13-5 22 1-8 3-14 7-21 1-1 2-3 3-4h1c-3 6-6 11-7 18v12h1c0-3 0-6 1-10s3-7 5-11c-2 8-2 17-1 26 0 9 3 18 3 28-2-4-3-9-5-13-6-10-19-17-19-30v-9c1-4 4-9 7-13 1-2 2-3 4-5 1-2 3-3 4-5l11-11z" class="D"></path><defs><linearGradient id="Aj" x1="416.05" y1="187.922" x2="466.214" y2="90.178" xlink:href="#B"><stop offset="0" stop-color="#c5bdaa"></stop><stop offset="1" stop-color="#f1e8cc"></stop></linearGradient></defs><path fill="url(#Aj)" d="M385 168c4-15 4-30 6-45 2-12 7-24 11-35 2-5 3-11 5-15 1-3 3-5 6-6 2-2 5-2 7-1 4 1 6 3 8 6 3 6 4 12 8 18 8 11 19 20 26 32 7 13 11 28 15 42 2 6 3 12 5 18 0 2 1 4 1 7-1 0-2 1-2 1 0 1 1 1 0 3l-3-2v-5h0c-1-6-4-11-5-16-2-7-3-14-5-21-3-10-6-18-11-26-8-13-22-22-28-35-3-5-4-15-9-17-1-1-3-1-4-1-6 2-7 12-9 17s-5 11-7 16-3 10-4 15c-2 11-3 21-4 31 0 8-1 15-4 22h0 0c-1 1-1 0-1 1l-1 1-1-1c0-1 1-2 0-3v-1z"></path><path d="M633 331h1c2 1 4 5 6 6 11 12 23 25 31 39 12 19 17 42 11 64-1 6-4 11-6 16s-5 11-9 15c-3 5-7 9-11 13-6 7-11 15-14 25h0l-1 7c-1-1 0-4 0-6-1-20 17-33 26-49 5-9 8-19 8-29 1-8 0-16-2-24-5-29-21-55-40-77z" class="E"></path><path d="M435 292c-5 3-9 7-15 9l10-7c2-2 4-5 7-5l2-2h0-1c-1 1-2 1-3 1-2 1-4 3-6 4-1 1-2 2-3 2h-1l4-2c1-1 1-2 2-3 3-2 6-2 8-5l1-1c-3 0-5 1-8 2-4 2-7 6-10 9 1-2 7-9 10-10 3-2 7-2 12-2 9 0 18 0 27 4 11 4 21 13 26 24l1 3c-10-12-24-26-41-27 7 3 13 5 20 9l-1 2c3 2 6 5 9 7 3 3 6 6 8 10 1 0 2 2 2 3h0c0 2 1 3 1 4h-1v2h1v1c-1 0-1 1-1 2 2 5 2 11 2 16-4-13-8-27-20-35-8-5-20-8-29-5-3 1-7 1-10 3s-5 5-7 7c-6 6-14 10-23 12v-1c-1 1-2 0-3 1h-1-2l-2 1h-2 0-4 0c-1 1-2 1-3 1 3-2 8-3 12-4 2 0 4-1 6-2 1-1 5-2 7-3l3-1c1 0 3-1 4-2v-1c1 0 1 0 2-1-1-1-2 1-4 1l-1-1c2-1 4-2 5-3 4-3 7-7 9-10 2-2 3-4 4-6l-1-1 1-1h0-1l-1-1s-1 1-1 2z" class="M"></path><path d="M495 326c-4-14-12-22-23-31l1-1 3 3c3 2 6 5 9 7 3 3 6 6 8 10 1 0 2 2 2 3h0c0 2 1 3 1 4h-1v2h1v1c-1 0-1 1-1 2z" class="O"></path><defs><linearGradient id="Ak" x1="731.651" y1="372.405" x2="676.97" y2="299.386" xlink:href="#B"><stop offset="0" stop-color="#605f57"></stop><stop offset="1" stop-color="#beb6a0"></stop></linearGradient></defs><path fill="url(#Ak)" d="M677 285c22 10 38 24 46 46 6 16 4 32-3 47-1 4-3 7-5 11-1 1-2 3-4 5 2-3 3-7 4-10-1-3 0-5 0-7 2-8 3-16 2-23h0v2c-1 2-1 4-1 6h0c-1-3 0-6-1-9l-1-1c-1-4-1-8-2-11-3-11-8-22-16-30h0c9 11 14 24 14 38 1 3 1 6 1 10-1-3-1-5-1-8-1-4-2-9-3-14s-5-10-8-15c-6-8-12-14-19-20-4-3-8-6-11-9h0 1l3 2c2 1 5 5 7 5 1-1 3-2 4-4h0l1-1c1-2 2-2 2-4-3-2-7-3-9-5l-1-1z"></path><path d="M717 354c1-7 0-14-2-21h0c4 13 5 25 3 39-1 4-1 8-3 12-1-3 0-5 0-7 2-8 3-16 2-23z" class="F"></path><path d="M523 319c-1-3-1-7-1-10 0-9 0-18 1-27 3-17 11-29 21-42 4-4 7-9 12-12 3-2 7-4 11-5 13-4 26-4 40-1l-4 3c-11-1-19 1-30 5-15 7-26 18-32 34l-3 13-2 13c0 1 0 4-1 5v7c-1 4-1 8 0 13 0 3 1 7 2 11-3-7-6-12-10-18l-1 1c7 12 10 25 17 37 3 5 6 10 10 14-1 0-2-1-3-2l-1 1h0l-9-12 1-1-2-4-2-3-3-3v-1c-1-1-1-1-1-2h-1l-3-7c-1-1-1-2-2-3v-1l-1-2c0-1-1-2-1-3-1-1-1-2-2-3v5z" class="I"></path><path d="M533 313v2c0-1 0-1-1-1-1-5-1-11-1-15 0-8 0-15 2-23 2-9 6-19 11-27 5-7 13-18 21-20l-1 1c-14 8-23 24-27 39-2 7-4 14-4 21 1 3 1 5 1 7h0l1-2v7c-1 4-1 8 0 13l-1 1v-2l-1-1z" class="F"></path><path d="M533 290c1 3 1 5 1 7h0l1-2v7c-1 4-1 8 0 13l-1 1v-2l-1-1v-9-14z" class="V"></path><defs><linearGradient id="Al" x1="538.684" y1="253.427" x2="558.461" y2="274.11" xlink:href="#B"><stop offset="0" stop-color="#ccc2a3"></stop><stop offset="1" stop-color="#e6e0dc"></stop></linearGradient></defs><path fill="url(#Al)" d="M564 230l1 1c2 1 2 1 4 0l1-1c1-1 2 0 3 0-15 7-26 18-32 34l-3 13-2 13c0 1 0 4-1 5l-1 2h0c0-2 0-4-1-7 0-7 2-14 4-21 4-15 13-31 27-39z"></path><path d="M648 363c0-2-1-4-2-6h1l2 3v1c7 10 11 24 11 37 0 3 0 6-1 10v2 1c-2 8-7 17-11 24-1 1-1 1-1 3-1 1-2 2-2 3 1 1 0 1 1 1h1l-3 4-1 2 1 1c-4 2-7 4-10 7-11 9-18 21-21 34-2 11-1 25 5 34a53.56 53.56 0 0 0 15 15c-2 1-4 0-5-1-5-1-9-5-13-8l-4-4h-1c-4-4-7-8-9-14h0l2 3v1l1 1h0v-2l-1-1c0-1 0-2-1-2v-4h-1l1-1h-1v-3c1 1 1 3 2 4 0-5-1-10 0-16 3-21 17-36 29-52v3c6-7 10-14 14-21 9-20 9-39 2-59z" class="E"></path><path d="M632 440v3c-15 18-30 38-28 63 1 7 3 14 7 20h-1c-4-4-7-8-9-14h0l2 3v1l1 1h0v-2l-1-1c0-1 0-2-1-2v-4h-1l1-1h-1v-3c1 1 1 3 2 4 0-5-1-10 0-16 3-21 17-36 29-52z" class="F"></path><path d="M573 291l10-4h0c-8 0-20 6-26 11-4 4-6 8-9 13-2 4-2 9-5 12 2-12 7-27 18-35 12-8 26-11 40-8 13 2 26 12 34 23v1h-1-1l-2-1c-1-1-2-2-3-2h-1c-1-1-2-1-3-1l9 7c2 1 3 3 4 4-5-3-10-7-16-10 6 6 13 10 19 17 7 10 12 20 20 30h0l-14-14c-14-16-29-35-53-36-12 0-22 4-30 12-5 4-8 9-11 15-1 2-2 4-3 7 0 1 0 3-1 4-1-9 4-19 9-25 7-8 14-14 23-18-1 0-5 2-7 2-8 4-13 10-19 16h0l2-3c3-4 6-8 10-11 2-2 5-4 7-6z" class="I"></path><path d="M623 296l6 3c2 1 4 3 5 4h1v1h-1-1l-2-1c-1-1-2-2-3-2h-1c-1-1-2-1-3-1s-3-2-4-2c1-1 2-1 3-2z" class="K"></path><path d="M620 298c-1 0-4-3-6-3-4-3-9-5-14-7-2 0-7-1-8-2 3 0 7 1 10 1 7 2 14 4 21 9-1 1-2 1-3 2z" class="D"></path><defs><linearGradient id="Am" x1="627.897" y1="194.075" x2="560.964" y2="114.007" xlink:href="#B"><stop offset="0" stop-color="#cac2b1"></stop><stop offset="1" stop-color="#efe6c7"></stop></linearGradient></defs><path fill="url(#Am)" d="M567 161c4-19 8-41 22-55 6-6 13-11 17-18 4-6 4-16 9-20 3-2 7-3 10-2 3 0 6 3 8 6 3 6 5 13 7 19 3 8 6 17 9 26 3 14 4 31 6 46h-1v2l1 1v2 1c0 1 1 1 1 2h-2l-1-2-2-5c0-1-1-2-2-3l-1-17c-1-10-2-20-5-30-2-8-6-17-9-25-2-5-4-14-7-17-2-1-3-2-5-2-2 1-3 2-5 3-2 2-2 7-3 10 0 3-2 7-4 10-3 5-8 9-12 14-5 5-10 11-14 18-6 11-10 26-13 39-1 5-1 11-2 16-2 6-4 13-7 18 6-2 10-3 16-3-9 3-16 7-20 15l-1 2c-2 5-3 11-6 16v-1c1-4 4-10 4-15 0 2-2 3-2 5-1 2-1 4-3 5 0-1 1-2 1-4-1 1-2 2-3 4h0c0 2-1 3-1 4h-1c0-1 1-2 1-3h0c-1 0-2 2-3 3l1 1h0-1-1c-1 3-3 6-6 8 2-4 4-7 7-11 1-2 3-4 5-6h-1l-4 4s-1 0-1-1h0l-1 1-1-1h0c1-1 1-2 1-3l2-4 1-1 1-2v-1c3-3 5-5 7-8 2-1 4-3 5-4 2-2 2-6 3-8l6-29z"></path><path d="M595 339c6-2 11-4 17-3l9 2c3 1 7 3 8 5 7 3 12 8 16 14l3 6c7 20 7 39-2 59-4 7-8 14-14 21v-3c-12 16-26 31-29 52-1 6 0 11 0 16-1-1-1-3-2-4v-7c-1-4 0-7 0-11 2-23 20-35 31-54 7-11 10-24 7-37-2-7-5-13-9-19-8-10-20-16-32-19-7-2-15-3-23-4-1-1-4-1-6-1-2-1-4-1-6-2-1 0-2-1-4-2l6-2c1-1 3-2 5-2l12-3c3 0 6 0 8-1 3-1 3-1 5-1z" class="E"></path><path d="M595 339c6-2 11-4 17-3l9 2c3 1 7 3 8 5 7 3 12 8 16 14l3 6c7 20 7 39-2 59-4 7-8 14-14 21v-3c0-1 2-3 3-4 2-3 3-6 5-9 6-12 6-28 1-41-5-14-16-25-30-32-4-2-8-3-11-5 2 0 6 2 9 3l2-3-6-3c6 1 11 4 17 7 2 2 4 4 7 5h0c3 2 5 4 7 6-6-9-17-19-27-21h-1c-5-1-10-1-15-1l-1-1 3-2z" class="B"></path><path d="M612 336l9 2c3 1 7 3 8 5h0c-3-1-5-3-8-3h0v1c-2-2-6-2-8-4l-1-1z" class="K"></path><path d="M611 349c7 3 16 9 20 15h0c-4-4-9-8-14-9l-8-3 2-3z" class="I"></path><path d="M609 341c3 0 8 1 11 2 10 2 17 11 22 20-1-1-3-4-4-5-3-2-5-5-8-7-4-3-10-7-14-8-2-1-5-1-6-1l-1-1z" class="G"></path><path d="M495 363c3-11 12-19 16-29 3-7 3-16 5-24l6 51c0-3 0-7 1-10v-32-5c1 1 1 2 2 3 0 1 1 2 1 3l1 2v1c1 1 1 2 2 3l3 7h1c0 1 0 1 1 2v1l3 3 2 3 2 4-1 1 9 12h0l1-1c1 1 2 2 3 2l1 1c2 0 3 0 5 1h1v1l1 1 6 3c8 3 21 8 23 18h0c1 4 1 12-1 16v1 1l-1 2h0c5-5 9-11 17-13 4-2 9-2 13 0 1 1 2 1 2 2h-1c-6-1-10 0-15 3h5c1 0 1 0 1 2l-2 1c-1 0-3-1-4-1-12 9-20 25-25 39-4 12-7 26-19 34v-3c1-1 3-2 3-4l-7 5v-3c-4 3-10 8-15 8-1 0-1-1-2-2-2 1-4 1-5 2l-1-1h0c4-1 9-2 12-4 2-1 3-4 4-5 3-5 6-10 7-15s-1-10-4-14c-2-4-6-6-11-6h-3-5 1c0 1 1 1 2 1 2 0 4 1 6 3 0 1 1 3 1 4-3 0-2-2-4-3l-1 1c-1 0-1-1-2-1l-1-1v1c-4-1-11 0-15 0h-1l-1-2h0-2c-1-1-4-1-6 0h-1v1h-1c-2 0-3 2-5 3-1 0-2 0-3 1-1 0-1 2-1 3v1c-1 1-3 4-4 6h-2c-1-1 0-4 0-5v-1-1l1-1c-1-1-1-1-2-1v-1h-1s0 1-1 1v1c-2 8-2 16 3 24l3 5c-6-2-11-3-15-10 0-1-1-2-1-4v-1c-1-3-2-6-2-9v-1c0 5 1 15 5 20 1 1 3 2 4 3v1c-2 0-2-1-4-2l-1-1h0l2 3-2 2c-3-3-7-6-8-10-3-5-3-11-4-16-2-7-4-13-7-20h0 0v-1l-1-1c-3-7-9-12-14-17l-9-9c-2-2-5-3-8-4 3 0 6 0 8 1 7 2 12 8 17 14 0 1 0 2 1 3l1-1c0-1 0-2 1-2l1 2v1c0 1 1 1 1 2h1v-1c1-1 0-1 1-2 0 1 0 1 1 2v-9h3v4c1-2 1-5 2-8 2-6 5-11 9-15-1 7-4 13-6 20s-2 15-1 22c1-11 3-24 11-32l-1 3c-1 2-4 9-3 11l5-14c0-3 2-6 3-9 4-7 7-13 11-19l-1-1-1 1h-1l1-2z" class="E"></path><path d="M539 473c4-1 8-1 11-3l1-1c3-1 5-4 8-6h0c0 2-2 3-3 4-4 3-10 8-15 8-1 0-1-1-2-2z" class="C"></path><path d="M504 362c2-2 5-4 8-6 1 1 1 1 1 2v1c-1 2-3 3-4 4-3 2-4 5-7 6 0-2 2-4 3-6l-1-1z" class="J"></path><path d="M495 363h0c1-1 1-1 1-2 2-4 8-11 12-13-3 6-7 12-11 17l-1-1-1 1h-1l1-2z" class="D"></path><path d="M504 362l1 1c-1 2-3 4-3 6l-5 5c-4 4-6 9-10 13 2-4 3-7 5-11 3-5 8-10 12-14z" class="O"></path><path d="M500 412c-2 4-5 8-7 12-4 5-7 11-8 18v-2c-1-11 8-21 15-28z" class="H"></path><path d="M547 360l-13-16c-2-2-7-11-7-14 3 7 8 12 13 17l9 12c-1 0-1 1-2 1z" class="F"></path><path d="M525 426c5-2 13-4 19-2v1l4 1v1c-2 0-4 0-6-1l3 3h-1-3-1c-1-1-1-1-2-1h0c-3 0-5 0-7-1h-2c-2 0-2 0-4-1z" class="C"></path><path d="M531 427l11-1 3 3h-1-3-1c-1-1-1-1-2-1h0c-3 0-5 0-7-1z" class="B"></path><path d="M604 399l-1-1c-2 1-4 4-7 6-4 3-8 8-11 13-4 6-7 15-10 22v-2c3-11 9-22 17-31 3-3 7-7 12-9h5c1 0 1 0 1 2l-2 1c-1 0-3-1-4-1z" class="J"></path><path d="M538 373h1l7 6c5 5 10 12 13 19 1 2 2 5 2 7-4-7-8-15-14-21-3-4-6-7-10-10l1-1z" class="O"></path><path d="M544 424c3 0 6 2 8 3l-9-15v-1c8 9 15 18 14 30-1-1-2-3-3-4-3-4-6-6-10-8h1l-3-3c2 1 4 1 6 1v-1l-4-1v-1z" class="F"></path><path d="M514 425l4 1c0-1-1-16 0-18 0-7 1-13 2-20l1-14c1 10 0 19 0 29 0 6 0 13 1 20h0c-1-1-1-2-1-3l-1-1v7h-1c0 1-1 1-1 3 0-1 0-1-1-1-1-1-2-1-3-3z" class="K"></path><path d="M540 357c1 0 2 0 3 1 4 4 7 9 11 14 8 14 13 30 14 46-4-13-7-27-13-39-2-4-5-8-7-11-3-4-6-7-8-11z" class="C"></path><path d="M520 426v-7l1 1c0 1 0 2 1 3h0c1 1 1 2 2 3h1c2 1 2 1 4 1h2c2 1 4 1 7 1h0c1 0 1 0 2 1h1v1h-3-5 1c0 1 1 1 2 1 2 0 4 1 6 3 0 1 1 3 1 4-3 0-2-2-4-3l-1 1c-1 0-1-1-2-1l-1-1v1c-4-1-11 0-15 0v-9z" class="D"></path><path d="M524 426h1c2 1 2 1 4 1h2c2 1 4 1 7 1h0c1 0 1 0 2 1h1v1h-3-5 0c-2 0-7 0-9 1v1h-1v-2c-1-2 0-2 1-4z" class="F"></path><path d="M515 368h1c0 2-6 6-8 8-8 10-13 21-18 33 5-8 14-15 22-20-5 7-12 11-17 18-7 8-10 17-14 27 0-9 2-18 5-26 5-11 10-23 19-32 3-4 6-6 10-8zm-25 72v-1c1-5 4-10 8-12 5-3 11-3 16-2 1 2 2 2 3 3 1 0 1 0 1 1 0-2 1-2 1-3h1v9h-1l-1-2h0-2c-1-1-4-1-6 0h-1v1h-1c-2 0-3 2-5 3-1 0-2 0-3 1-1 0-1 2-1 3v1c-1 1-3 4-4 6h-2c-1-1 0-4 0-5v-1-1l1-1c-1-1-1-1-2-1v-1h-1s0 1-1 1v1z" class="C"></path><path d="M518 429c0-2 1-2 1-3h1v9h-1l-1-2-1-1c-1-2-5-1-7-1l1-1h3 4 0v-1z" class="B"></path><path d="M510 431c2 0 6-1 7 1l1 1h0-2c-1-1-4-1-6 0h-1v1h-1c-2 0-3 2-5 3-1 0-2 0-3 1-1 0-1 2-1 3v1c-1 1-3 4-4 6h-2c-1-1 0-4 0-5v-1c1-1 2-2 3-4 2-2 8-6 12-6 1 0 0 0 1-1h1z" class="T"></path><path d="M558 375v-1h2c2 2 4 5 7 6l3 3 3 3c7 8 5 18 5 28l-1 6c-3 8-3 15-5 22 0-11 0-23-2-35-2-11-7-22-12-32z" class="B"></path><path d="M570 383l3 3c7 8 5 18 5 28l-1 6c-2-2-1-8-1-10-1-2-1-3-1-4v-3c-1-2-1-2-1-3v-2c-1-1-1-1-1-2v-2c-1-1-1-1-1-2-1-2 0-3 0-4-1-2-2-3-2-4v-1z" class="C"></path><defs><linearGradient id="An" x1="564.523" y1="376.024" x2="582.647" y2="368.91" xlink:href="#B"><stop offset="0" stop-color="#1f211f"></stop><stop offset="1" stop-color="#3d3a3c"></stop></linearGradient></defs><path fill="url(#An)" d="M549 359l1-1c1 1 2 2 3 2l1 1c2 0 3 0 5 1h1v1l1 1 6 3c8 3 21 8 23 18h0c1 4 1 12-1 16v1 1l-1 2h0c0 1-1 1-1 2l-1 1c0 1-1 1-1 2l-1-1v-3-6h-1v2h0l-1 4c0 3 0 5-1 7-1-2 0-3-1-5l-1 6h-1c0-10 2-20-5-28l-3-3-3-3c-3-1-5-4-7-6h-2v1c-1-1-10-13-11-15 1 0 1-1 2-1h0z"></path><path d="M549 359l1-1c1 1 2 2 3 2l1 1c3 3 6 6 10 8 7 4 14 6 21 11 2 2 3 3 4 5v1c1 1 1 3 1 4-1-1-2-2-2-3 0 0 0-1-1-2 0-1 0-1-1-1-3-1-4-4-6-5h-1c-1 0-3-2-4-3 0 1-1 1-2 2h0c-5-2-9-5-13-8a57.31 57.31 0 0 1-11-11z" class="M"></path><path d="M573 378h0c1-1 2-1 2-2 1 1 3 3 4 3h1c2 1 3 4 6 5 1 0 1 0 1 1 1 1 1 2 1 2 0 1 1 2 2 3-1 4-2 9-1 12v1l-1 2h0c0 1-1 1-1 2l-1 1c0 1-1 1-1 2l-1-1v-3-6h-1v2h0c-1-1 0-4-1-6 0-4-1-9-4-13-1-2-4-3-5-5z" class="L"></path><path d="M586 384c1 0 1 0 1 1 1 1 1 2 1 2l-1 2 1 1c0 1 0 2-1 4v6l-1-1c0-1 0-1-1-2v-3h-1v-2h-1c0-2 0-3-1-5h0v-1l2 1 2-3z" class="G"></path><path d="M573 378h0c1-1 2-1 2-2 1 1 3 3 4 3h1c2 1 3 4 6 5l-2 3-2-1v1h0c1 2 1 3 1 5h1v2 4c0 2 1 6 0 8v-6h-1v2h0c-1-1 0-4-1-6 0-4-1-9-4-13-1-2-4-3-5-5z" class="I"></path><path d="M549 359h0a57.31 57.31 0 0 0 11 11c4 3 8 6 13 8 1 2 4 3 5 5 3 4 4 9 4 13 1 2 0 5 1 6l-1 4c0 3 0 5-1 7-1-2 0-3-1-5l-1 6h-1c0-10 2-20-5-28l-3-3-3-3c-3-1-5-4-7-6h-2v1c-1-1-10-13-11-15 1 0 1-1 2-1z" class="G"></path><path d="M567 380v-1l-1-1h1c1 0 1 1 2 1h0c2 2 3 4 4 5v2l-3-3-3-3z" class="T"></path><path d="M549 359h0a57.31 57.31 0 0 0 11 11c4 3 8 6 13 8 1 2 4 3 5 5 3 4 4 9 4 13 1 2 0 5 1 6l-1 4h0c1-4 0-11-1-15-2-6-6-10-11-13l-1 1h0c-1 0-1-1-2-1h-1l1 1v1c-3-1-5-4-7-6h-2v1c-1-1-10-13-11-15 1 0 1-1 2-1z" class="O"></path><path d="M560 374c2 0 8 2 10 4h0l-1 1h0c-1 0-1-1-2-1h-1l1 1v1c-3-1-5-4-7-6z" class="L"></path></svg>