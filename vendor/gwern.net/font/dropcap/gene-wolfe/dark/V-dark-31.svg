<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="126 71 781 876"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#2e2e2f}.C{fill:#c8c7c8}.D{fill:#a7a5a6}.E{fill:#c0bebf}.F{fill:#b8b6b7}.G{fill:#d7d6d7}.H{fill:#8f8d8e}.I{fill:#b1afb0}.J{fill:#212122}.K{fill:#1a1a1b}.L{fill:#d0cecf}.M{fill:#f8f7f7}.N{fill:#39393a}.O{fill:#dfdedf}.P{fill:#666566}.Q{fill:#e5e4e5}.R{fill:#a2a0a1}.S{fill:#151516}.T{fill:#f4f4f4}.U{fill:#747374}.V{fill:#0d0d0e}.W{fill:#4b4a4b}.X{fill:#575657}.Y{fill:#807f7f}</style><path d="M332 463c1 0 3 1 4 2l-4 3-2-2 2-3z" class="H"></path><path d="M326 418c4-2 7-1 11-1l-10 3-1-2z" class="B"></path><path d="M247 422c1 1 1 1 1 2s-1 1 0 2h0l2-2c-1 1-1 2-2 4 1 0 1 0 2 1l-1 1c-1 0-3 2-4 3l2-11z" class="G"></path><path d="M169 213c4-1 9 0 13 1-1 1-1 1-2 1l-8-1h-2c-1 0-1-1-1-1z" class="D"></path><path d="M767 391v-1h-1v-1c-1-1-2-1-3-1l-1-1h0 1c4 2 9 3 14 5h-2s0 1-1 1c-2 0-5-1-7-2z" class="B"></path><path d="M282 148c1-2 2-3 3-4 2 2 4 4 5 6h-1 0l-1 1v-1h-1-1c-2 0-3 0-5-1l1-1h0z" class="C"></path><path d="M314 424c3-3 8-5 12-6l1 2c-2 1-5 4-7 4h-6z" class="W"></path><path d="M665 484c-2-3-4-5-8-7v-1h1c1 1 2 2 3 2v-4-1c0-1 0-1-1-2h0c1 0 2 1 4 2v1c1 3 2 7 1 10z" class="N"></path><path d="M339 466c1 1 2 1 3 1l1 1-1 1-2 2v2l-1 1c0 2 0 5-1 6h-2c0-5 1-9 3-14z" class="I"></path><path d="M281 470c0 4 0 8 2 11 0 3 1 5 3 8h0-1s-1 0-2-1c-2-2-4-7-4-11h1c1-2 0-4 1-7z" class="E"></path><path d="M777 407c3-3 6-4 9-7 0-1 1-3 2-3 1-1 2-1 3 0l-3 2 1 1c1 0 1 0 2-1v1l-10 7h-4z" class="R"></path><path d="M759 391h1c-1-2-3-4-3-6h1c0 1 0 1 1 2h0c0 1 0 1 1 2l1 2c0 1 1 2 1 3l1 1c0 1 1 2 2 3-1 0-2 0-3 1-4 1-11 5-15 4 0 0 1-1 2-1s1 0 2-1c2-1 7-4 9-4l3 1-1-1c-1-2-2-3-2-5l-1-1z" class="J"></path><path d="M706 423h-1l-17-7h1c1 0 1 1 2 1 5 0 9 1 13 2l4 3c-1 0-2 1-2 1z" class="B"></path><path d="M342 469c-1 7-3 13-2 20l-1 4v-1c-1-1-1-2 0-3v-3-3c-1 1-1 1-2 1v9c0 2 0 3-1 4-1-5 0-12 0-17h2c1-1 1-4 1-6l1-1v-2l2-2z" class="C"></path><path d="M282 431c0 2 1 3 0 4v1c-1 1-1 2-1 3v1c-1 2-2 5-3 7-2 1-3 1-4 0l8-16z" class="T"></path><path d="M685 462h1c3 7 4 13 4 21 0 3 1 6 0 10v-5c-1-2 0-4-1-6l-1 2c0 3-1 5-1 7v-10c0-3 1-6 0-9l-1-8-1-2z" class="F"></path><path d="M280 379h1c3 1 6 1 9 2 0 1 2 1 2 1l3 3-21-5h-2-1l2-1c1-1 5 0 7 0z" class="D"></path><path d="M821 265c0-2 1-3 3-5-1 6-3 13 1 19 1 2 3 3 5 4l-2 1c-3-1-5-4-6-6v-1c-2-3-2-8-1-12z" class="G"></path><path d="M746 438c0-1-1-1-1-2-1-1-1-2-1-4 0-1 0-1 1-2 3 6 6 11 7 17v1h-2c-2-2-2-6-4-9v-1z" class="O"></path><path d="M342 454c2 0 3 0 5-2 0 2-2 4-3 5-2 2-4 4-6 5l-2 3c-1-1-3-2-4-2l2-2 8-7z" class="P"></path><path d="M334 461c2 0 3 0 4 1l-2 3c-1-1-3-2-4-2l2-2z" class="U"></path><path d="M788 370l4 1h1c3 3 6 8 6 12 0 2 0 3-1 5h0c-1-1-1-2 0-3v-1c-2-4-4-8-9-10-1-2-1-3-1-4z" class="P"></path><path d="M338 446l10-5h0v1h0v2c-1 0-3 2-3 2h-1l-4 4-4 4h2c-3 2-5 5-7 5 3-4 7-7 8-12l-1-1z" class="J"></path><path d="M241 236l2-4c2-6 8-10 14-12v1c-2 1-6 2-7 4 1 0 1 0 2-1h2 0c-2 1-4 2-5 4-1 1-1 1-1 2h2c-3 2-7 5-7 8v1h0c-1-1-2-1-3-2l1-1z" class="G"></path><path d="M225 118c1 2 0 4 0 6-1 4-1 8 0 12 0 2 0 3-1 5h-1l-1-1c-1-7-1-15 3-22z" class="E"></path><path d="M126 102l-1-6V84l3 12c2 4 5 8 7 11-1 0-2-1-2-2l-1-1c-1 0-1 0-1 1h0c1 2 1 3 1 5l-5-10h-1l1 2h-1z" class="D"></path><path d="M837 213c5 2 10 0 15-1 5 0 10 0 15 1-1 1-2 1-2 2h-1c-2-1-5-1-8-1-3 1-6 1-9 1-4 0-7 1-11 1l1-3z" class="H"></path><defs><linearGradient id="A" x1="270.915" y1="149.929" x2="269.576" y2="140.005" xlink:href="#B"><stop offset="0" stop-color="#bfbdbf"></stop><stop offset="1" stop-color="#e2e1e2"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M258 144v-4h0 1c6 6 15 7 23 8h0l-1 1c-2 0-5-1-7 0-5 0-10-1-14-4l-1-1h-1z"></path><path d="M244 405c2 0 5 3 7 5 0 4 0 7 1 11-1 1-2 1-2 3l-2 2h0c-1-1 0-1 0-2s0-1-1-2v-7c1-4 0-7-3-10z" class="L"></path><defs><linearGradient id="C" x1="690.354" y1="461.618" x2="698.643" y2="469.307" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#888789"></stop></linearGradient></defs><path fill="url(#C)" d="M692 460c4 3 7 7 9 11h0l-1 1v1c1 2 1 4 1 5l-3-5v-1h-1c-3-4-5-7-8-9 0-2 2-3 3-3z"></path><path d="M663 457l2-1v-2c-1-3-4-6-6-9l11 8c-1-2-2-3-4-4h1l5 4 3 5h-7-4l-1-1z" class="K"></path><defs><linearGradient id="D" x1="253.788" y1="219.38" x2="244.247" y2="214.459" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#D)" d="M241 208c6 4 11 8 17 11-3 0-6 2-10 2-1 0-3-2-4-2v-1c1-1 0-2 0-4-1-1-1-1 0-2v-1c-1-1-2-1-2-2l-1-1z"></path><path d="M761 391c0 1 1 1 1 2 2-2 2-2 5-2 2 1 5 2 7 2 1 0 1-1 1-1h2l6 1c-1 1-2 2-4 1-2 0-3 1-6 2v1h0c-1 1-1 1-3 1v-1l-5 1c-1-1-2-2-2-3l-1-1c0-1-1-2-1-3z" class="N"></path><path d="M830 283h0c3 0 5 0 7-1s3-4 3-6c1-4-1-8-2-12 3 3 5 7 4 12 0 4-2 8-6 11l-2 1c-2 0-3 0-4-1l2-1v-1c-1 0-2 0-4-1h0l2-1z" class="O"></path><path d="M891 111c3-5 8-10 10-17 1-3 2-6 2-9 1 5 1 11-1 16s-4 10-7 14v-2l1-1 2-3c-2 0-2-1-3-1-1 2-2 2-3 3h-1z" class="F"></path><path d="M238 402c1-1 1-2 2-2h1c1 1 2 1 2 1 4 1 8 1 11 2v1s0 1-1 1v3 10l-1 3c-1-4-1-7-1-11-2-2-5-5-7-5-1 0-5-3-6-3z" class="H"></path><path d="M723 486c1 9 3 20 10 26 4 3 9 4 14 4-5 2-10 3-15 0l1-1c-1-3-4-5-6-8-1-1-2-3-2-5-1-1-2-4-2-6-1-1-1-4-1-6s0-2 1-4z" class="O"></path><path d="M351 446l3-2v1h1 0l-1 1h0l-1 1 1 1-1 1 2-1v1l3-3 1 1c-1 1-3 2-3 4h0l-1 2h0c1 1 2 2 2 3v2c-2-1-2-2-4-3l-3 4h1c0 1 0 1 1 2-2 0-3-1-4-1 1-2 2-3 3-4v-1l-1-3c-2 2-4 3-6 6v-1c1-1 3-3 3-5-2 2-3 2-5 2 2-3 6-5 9-8z" class="K"></path><path d="M350 452c1-1 2-2 4-2 0 2-1 3-3 5l-1-3z" class="B"></path><defs><linearGradient id="E" x1="719.11" y1="408.882" x2="728.135" y2="417.862" xlink:href="#B"><stop offset="0" stop-color="#2b2b2c"></stop><stop offset="1" stop-color="#5a595a"></stop></linearGradient></defs><path fill="url(#E)" d="M715 412h2v-1c1-1 3-2 4-3l-4-4c-2-1-3-3-4-3v-1h0c4 3 8 7 12 10 3 3 7 4 9 7-1 0-2-1-3-2-1 2-3 4-4 4-2-2-4-3-6-4l-1-1c-1 0-2-1-3-1l-3 3v-1l1-1v-2z"></path><path d="M190 284c2 0 3 1 6 2 2 1 4 1 6 2 3 0 5 2 6 3h0l11 11c2 1 4 2 5 4v2c-5-5-10-9-15-13-7-4-14-5-19-11z" class="I"></path><path d="M361 472c2 0 2-2 3-4 0-1 0-1 1-1v-2h1c1 0 1 0 1-1l1 1c-2 2-3 4-4 6v1l1-2 1 1h-1v1l-1 1v1c1 0 1 1 1 2 0-1 1-1 1-2l1 1c1-1 1-1 2 0h1c1 1 1 1 1 2 1 1 3 1 3 3 0 1 1 1 1 2l2 2c0 2 1 3 2 4h2v1h-6v-1l-2-2-1-2c-2-2-4-3-7-3l1-2c-1-1-1-2-1-3l-2 2v-3c0-2-1-2-2-3zm348-54c-3-2-5-3-9-4 1-1 1 0 2 0h1l2 1h0c1 1 2 1 3 2l2-2 1-1c1-1 1-1 4-2v2l-1 1v1l3-3c1 0 2 1 3 1l1 1c2 1 4 2 6 4 4 4 7 9 9 15 1 2 2 5 3 6 1 0 1-2 1-3v3h1c0 3 0 5 1 7-2-1-3-4-5-6-1-3-2-7-3-10-3-7-8-11-14-15-1 0-3 1-4 1-3 1-4 0-6 2l-1-1z" class="B"></path><path d="M281 470c0-4 2-8 4-12 1-3 6-9 9-10v1l-2 2v1c-5 9-8 18-9 29-2-3-2-7-2-11z" class="I"></path><path d="M195 210v-1c0-2 1-4 1-7 2 0 2 0 5 1 1 1 2 1 3 1s2 1 2 1l2 1h1l-1 1 1 1 2 1c1 0 1 1 2 1 2 3 4 1 6 3h2c-5 0-11-1-16-1v-1c-2-1-8-1-10-1z" class="N"></path><path d="M810 298c1-2 12-10 15-12 1 1 3 1 5 1h0c1 1 2 1 4 1l-1 1c-3 1-7 2-10 4-7 4-14 9-20 16v-2l1-2c1 0 6-5 6-6h1l-1-1z" class="E"></path><defs><linearGradient id="F" x1="263.142" y1="383.384" x2="261.972" y2="396.402" xlink:href="#B"><stop offset="0" stop-color="#151415"></stop><stop offset="1" stop-color="#3e3e3f"></stop></linearGradient></defs><path fill="url(#F)" d="M272 380h2c-1 1-1 2-1 3l-1 1h0c-3 4-5 8-8 12-1 1-2 1-4 1h0-1l-1-1h2l-1-1c0-1 0-2-1-2h-2c-2 0-4 0-6-1h0c9-3 16-4 22-12z"></path><path d="M741 440c0-3 1-6 1-9-1-4-2-7-3-10-3-6-8-9-12-14v1c6 2 13 8 15 13l3 9c-1 1-1 1-1 2 0 2 0 3 1 4 0 1 1 1 1 2-1 0-1-1-2-2 0 3-1 7 0 10v6l-2-1v-2-2c-1-2-1-4-1-7z" class="I"></path><defs><linearGradient id="G" x1="293.758" y1="421.053" x2="281.147" y2="425.984" xlink:href="#B"><stop offset="0" stop-color="#9b979a"></stop><stop offset="1" stop-color="#c2c2c3"></stop></linearGradient></defs><path fill="url(#G)" d="M282 431c1-4 2-9 5-13 3-7 13-11 20-15-5 5-11 8-15 13-5 5-6 11-7 18l1 2-1 11v2l-1 1h0 0l-1-1c0-2 0-7 1-9s1-4 0-5c0 2-1 3-3 4 0-1 0-2 1-3v-1c1-1 0-2 0-4z"></path><defs><linearGradient id="H" x1="677.094" y1="451.166" x2="689.98" y2="459.531" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#H)" d="M679 456l-4-4v1l-1-1v-1-1l1-1c1 0 2 1 2 1 0-1-1-1-1-2v-1l-1-1c-1 0-2-1-2-1l-2-1 1-1c2 1 3 3 5 4 6 4 10 8 15 13-1 0-3 1-3 3l-3-1h-1c-1 0 0 0-1-1-1-2-3-5-5-5z"></path><path d="M356 451l2-2c1 1 1 2 1 3v2l2 2h1 1l2 1-1 1v2c1 0 1-1 2-1h1c-2 1-3 2-5 4-2 1-8 2-9 4l1 1v1c1 1 2 1 2 2v1h0l-4-4 1-2 2-2-1-1 1-1-1-2-1 1h-1c-1-1-1-1-1-2h-1l3-4c2 1 2 2 4 3v-2c0-1-1-2-2-3h0l1-2z" class="J"></path><path d="M352 461c-1-1-1-1-1-2h-1l3-4c2 1 2 2 4 3h2c1 1 2 1 3 1l1 1c-1 0-1 1-2 1s-1 0-2 1-3 1-4 2l-1-1 1-1-1-2-1 1h-1z" class="B"></path><path d="M731 441h0 6c2 2 3 5 5 6v2 2h0l-1 2c1 1 2 3 1 5-2-3-6-9-9-10-1 0-2 0-3 1-1 0-1 0-2 1-1 0-2 0-3 1v-2c1-2 2-4 3-5 2-1 4-1 7-1h-1c-2-1-2-1-3-2z" class="M"></path><path d="M731 441h0 6c2 2 3 5 5 6v2 2h0c-1-3-5-5-7-8h-1c-2-1-2-1-3-2z" class="H"></path><path d="M314 424h6c-11 6-15 15-19 27-1 0 0 0-1-1 1-1 1-4 1-6-1 0-2 0-2-1h-1c2-2 2-5 3-7 2-3 5-6 8-9l5-3z" class="C"></path><path d="M732 382c7-1 13-4 20-6h2c6 4 15-2 21 1h2l1 1c-11 0-22 1-32 4l-9 1c-1 1-3 2-5 2l-2-2h0 1l1-1z" class="E"></path><path d="M368 544c-2-4-2-8-1-12 0-1 0-2 1-3h2c2 10 8 17 14 24l-9-5h-1c-2-1-4-3-6-4z" class="T"></path><path d="M286 436v3c1-1 1-3 1-5 1-8 3-16 11-21 1 2 0 4-1 6s-2 3-2 6c-3 6-8 14-10 21v1l1-11z" class="H"></path><defs><linearGradient id="I" x1="337.275" y1="475.405" x2="318.791" y2="491.404" xlink:href="#B"><stop offset="0" stop-color="#9c999e"></stop><stop offset="1" stop-color="#d7d6d6"></stop></linearGradient></defs><path fill="url(#I)" d="M330 466l2 2c-3 6-5 13-5 20 0 2-1 5 0 7 0 2 1 3 2 3v2h-4l-1-2c0-1 0-1-1-2-1-10 2-22 7-30z"></path><defs><linearGradient id="J" x1="250.925" y1="394.217" x2="240.47" y2="401.333" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#838182"></stop></linearGradient></defs><path fill="url(#J)" d="M238 394l7-1c2 1 3 2 4 3 2 0 4 0 6 1 1 1 2 3 1 4 0 0 0 1-1 1v3l-1-1v-1c-3-1-7-1-11-2 0 0-1 0-2-1h-1c-1 0-1 1-2 2v-1c-1-1 0-1 0-3l-3-3h0l3-1z"></path><path d="M238 394l7-1c2 1 3 2 4 3-2 0-5 0-7-1-1-1-3 0-4-1z" class="P"></path><defs><linearGradient id="K" x1="725.955" y1="423.018" x2="741.95" y2="429.536" xlink:href="#B"><stop offset="0" stop-color="#5a595b"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#K)" d="M727 419c1 0 3-2 4-4 1 1 2 2 3 2 5 6 6 14 6 20 0 1 0 3-1 3-1-1-2-4-3-6-2-6-5-11-9-15z"></path><path d="M730 449c1-1 2-1 3-1 3 1 7 7 9 10 4 6 5 14 4 21 0 1-1 4-1 5l1 1-1 1c-1 2-2 3-4 3 1-1 2-3 2-4 1-4 1-8 0-11-1-9-5-20-13-25z" class="F"></path><path d="M300 411c2 1 4 2 4 5l1-1c3-1 5-3 8-2l1 1-1 1h0-3 0c-2 1-4 2-5 3-1 0-1 1-2 1-5 2-9 11-11 15l-4 12-3 3v-2-1c2-7 7-15 10-21 0-3 1-4 2-6s2-4 1-6c0-1 2-2 2-2z" class="N"></path><path d="M300 411c2 1 4 2 4 5-3 2-6 5-9 9 0-3 1-4 2-6s2-4 1-6c0-1 2-2 2-2z" class="P"></path><defs><linearGradient id="L" x1="713.827" y1="427.248" x2="726.986" y2="444.357" xlink:href="#B"><stop offset="0" stop-color="#aeadae"></stop><stop offset="1" stop-color="#deddde"></stop></linearGradient></defs><path fill="url(#L)" d="M708 422c8 4 14 8 18 16 0 1 1 3 2 4l3-1c1 1 1 1 3 2h1c-3 0-5 0-7 1-1 1-2 3-3 5-3-5-4-11-8-16-3-4-7-7-11-10 0 0 1-1 2-1z"></path><path d="M657 525c2 2 2 2 2 4l1 2c0 8 0 11-6 17-1 1-1 1-3 1v-1c-3 2-7 3-10 5 9-9 14-16 16-28z" class="M"></path><path d="M748 147h1c5 0 11-1 16-4 2-1 3-2 5-3v4c-2 1-2 1-3 2l1 1-1 1v1c-2 2-7 2-9 4h-1c-1-2-2-2-5-2-1 0-2 0-3 1v2h0c-2-1-1-1-2-1h-1-1-2l-4 1v-1h-1c1-1 1-2 2-2 2 0 5 1 7 0l1-1v-3z" class="L"></path><path d="M752 151l1-1v-1c1 0 3 1 4 0h3c1-1 1-1 2-1 2 0 3-1 5-2l1 1-1 1v1c-2 2-7 2-9 4h-1c-1-2-2-2-5-2z" class="F"></path><path d="M696 445h-3c-4 1-9-2-12-5h0l2 1s1 1 2 1v-2c-1-2-1-4-1-6h1v-3l1-1c0-1 1-2 2-3h-1c0-1-1-1-1-1-1-1-1-1-2-1v-1h0c1 0 2 1 3 1s1 0 2 1h1 0c0-1 0-1 1-2l1 1 1-1 2-1h0c-1 0-2 0-2-1-1-1-2-1-3-1h0 0c5-1 9 4 12 7-2 0-4-1-6 0 0 1 0 2 1 3-2 0-4-3-6-1-1 1-2 3-3 5 1 1 1 6 2 7l1 1 1 1 4 1z" class="B"></path><path d="M606 614c0-5 2-12 5-16v1c0 2-2 5-3 7-1 3 0 6-1 8v4c0-2 1-3 1-5v-1c0-1 0 0 1-1v-2c0-1 0-2 1-3 1-2 0-3 2-4-3 10-7 21-1 31 2 5 7 7 12 9-7 0-10-1-15-7-2-2-4-6-4-10h0c-1-4-1-8 0-13 0 0 0-1 1-1h0c0 1 0 2 1 3z" class="O"></path><path d="M604 612s0-1 1-1h0c0 1 0 2 1 3v10c0 1 0 1-1 2v-1h-1 0c-1-4-1-8 0-13z" class="H"></path><defs><linearGradient id="M" x1="776.093" y1="372.989" x2="777.306" y2="382.708" xlink:href="#B"><stop offset="0" stop-color="#cfcdce"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#M)" d="M754 376c10-1 20-5 31-3l4 1c5 2 7 6 9 10v1l-2-2c-4-4-12-5-18-5l-1-1h-2c-6-3-15 3-21-1z"></path><path d="M742 451l2 1v-6c-1-3 0-7 0-10 1 1 1 2 2 2v1c3 12 5 35 0 46l-1-1c0-1 1-4 1-5 1-7 0-15-4-21 1-2 0-4-1-5l1-2h0z" class="Q"></path><defs><linearGradient id="N" x1="352.446" y1="459.446" x2="338.054" y2="468.554" xlink:href="#B"><stop offset="0" stop-color="#3c3c3d"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#N)" d="M350 452l1 3v1c-1 1-2 2-3 4 1 0 2 1 4 1h1l1-1 1 2-1 1 1 1-2 2-5 2c-1 0-3 1-3 3l-1 2c0 1-1 2-1 2-2 2-2 8-3 11v3c-1-7 1-13 2-20l1-1-1-1c-1 0-2 0-3-1 1-3 3-5 5-8h0c2-3 4-4 6-6z"></path><path d="M344 458c1 1 1 1 1 2 0 3-2 5-2 8l-1-1c-1 0-2 0-3-1 1-3 3-5 5-8z" class="U"></path><defs><linearGradient id="O" x1="732.033" y1="388.584" x2="756.271" y2="406.113" xlink:href="#B"><stop offset="0" stop-color="#080809"></stop><stop offset="1" stop-color="#2e2e2f"></stop></linearGradient></defs><path fill="url(#O)" d="M747 403c-6 0-12 1-17-1-3-1-5-2-7-3 3-1 7 0 10 0 6 0 13-1 18-4 3-1 5-3 7-4h1l1 1c0 2 1 3 2 5l1 1-3-1c-2 0-7 3-9 4-1 1-1 1-2 1s-2 1-2 1z"></path><defs><linearGradient id="P" x1="694.945" y1="478.758" x2="706.425" y2="497.254" xlink:href="#B"><stop offset="0" stop-color="#c2c0c2"></stop><stop offset="1" stop-color="#e5e4e4"></stop></linearGradient></defs><path fill="url(#P)" d="M702 470c3 6 4 12 5 18l1-1v1c-1 3-3 6-4 9 0 1 0 0-1 1v4l-1 1c0-1 0-2-1-3s-4-1-5-1h0l3-3c1-2 1-5 1-6 0-7-1-12-3-18h1v1l3 5c0-1 0-3-1-5v-1l1-1h0l1-1z"></path><path d="M702 470c3 6 4 12 5 18l1-1v1c-1 3-3 6-4 9 0 1 0 0-1 1 1-3 1-7 1-10-1-6-1-11-3-17h0l1-1z" class="K"></path><defs><linearGradient id="Q" x1="368.579" y1="485.628" x2="362.246" y2="497.303" xlink:href="#B"><stop offset="0" stop-color="#535354"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#Q)" d="M365 481c3 0 5 1 7 3l1 2 2 2v1c-1 1-2 1-3 3v4c-1-1-1-2-2-2h-1l-2 1c-1 2-2 4-3 7v-3l1-1v-1c-1-1-2-1-2-1h-1c-2-1-2-1-4 0v-2c0-1 1-3 2-4 1-3 3-7 5-9z"></path><defs><linearGradient id="R" x1="285.619" y1="391.494" x2="276.881" y2="405.006" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#313132"></stop></linearGradient></defs><path fill="url(#R)" d="M264 396c3-1 4-5 7-4 2 1 4 3 6 4 6 3 16 4 23 2 1 0 2 0 4-1h1 0l1 1c-4 1-9 4-14 4h-1c-6 1-12 1-18-1-4-1-8-3-13-4 2 0 3 0 4-1z"></path><defs><linearGradient id="S" x1="654.722" y1="482.428" x2="660.147" y2="491.649" xlink:href="#B"><stop offset="0" stop-color="#3b3a3b"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#S)" d="M654 495c0-1 0-3-1-4s-3-2-5-2c-1 0 0 0-1-1 2-1 2-2 4-3h1v-2c1-1 2-2 2-3l1-1v-1c-2-1-3-1-4-1h-1c1-1 1-1 2 0 3 0 5 1 6 2 5 2 8 9 9 14h0l-1-2-3-1-4 4c-1 1-3 1-4 2h-1v-1z"></path><path d="M654 495l1-1h3c1-2 3-4 5-4l-4 4c-1 1-3 1-4 2h-1v-1z" class="P"></path><defs><linearGradient id="T" x1="314.553" y1="409.085" x2="309.972" y2="416.328" xlink:href="#B"><stop offset="0" stop-color="#29292a"></stop><stop offset="1" stop-color="#464546"></stop></linearGradient></defs><path fill="url(#T)" d="M300 411c5-4 9-8 14-12 1-1 3-2 4-2h1v1h-1c-1 0-3 1-4 3-1 0-2 1-3 2h-1l-3 3c2 1 3 1 5 2 1 0 2 1 3 1l1 1c1 0 1 1 2 1l1 1 2 2 2 1 1-1h1c1 0 3-1 4-1 2-1-1 0 2-1h3 4 0c-6 1-12 1-16 4-3 2-6 4-8 6 0-1 0-2 1-2l1-1c-2-1-4 0-6-2-3 0-3 1-6 2h-1c1 0 1-1 2-1 1-1 3-2 5-3h0 3 0l1-1-1-1c-3-1-5 1-8 2l-1 1c0-3-2-4-4-5z"></path><path d="M672 453v-2l1 1c0 1 1 1 1 2l2 2h1 1v1h0c2 1 3 2 4 3h0 0l-2-2-1-2c2 0 4 3 5 5 1 1 0 1 1 1l1 2v1 1h-1c-3-2-5-4-8-6-2-1-2-1-4 0-1 1-1 1-1 3l-3-2v1h0c2 1 3 2 4 4l1 1c1 1 1 2 2 3h0-2l-3 1v1c-1-2-2-3-2-5h0c-2-2-3-2-4-3h-1v-1h-1-1 0l1 4h0v5c-1-2-4-5-4-7v-1h-1v-4c1-1 1-1 2-1h1c1 1 1 1 2 1-3-2-5-3-7-6l7 3 1 1h4 7l-3-5z" class="B"></path><path d="M819 262c1 1 1 2 2 3-1 4-1 9 1 12v1c1 2 3 5 6 6h0c2 1 3 1 4 1v1l-2 1h0c-2 0-4 0-5-1-3 2-14 10-15 12l1 1h-1c0 1-5 6-6 6v-1h0c-1 0-2 0-3 1l-1 1h0c-1 0-2 1-3 2h-1l-1 1-2 2c-1 0-1 0-2 1s-1 1-2 1l14-14 12-10c3-1 5-2 7-4l-2-6c-2-6-1-11-1-17z" class="T"></path><path d="M801 305c2-3 6-6 9-7l1 1h-1c0 1-5 6-6 6v-1h0c-1 0-2 0-3 1z" class="M"></path><path d="M338 454l2-2v-1c1-1 2-2 3-2 0-1 1-2 2-2l1-1h0c2-1 4-4 5-4l-3 4c-1 1-1 2-3 2-1 2-3 3-4 5h1l3-3 1-1 1-1c1-1 2-1 4-2-3 3-7 5-9 8l-8 7-2 2-2 3c-5 8-8 20-7 30h0c-3-5-6-11-8-16-2-8-3-18 1-25h1l-1 10c-1 8 0 16 4 23 2-10 3-21 11-29 2 0 4-3 7-5z" class="S"></path><path d="M465 166c1-1 3-2 4-2 0 0 1 1 2 1 0 0 2-1 3-1l8-3h1l-2 6c-14 2-33 5-43 17-6 7-9 15-12 23-1-1-1-1 0-2v-2h0l1-1v-1h0v-1l1-1v-1c1-1 1 0 1-1h-2c2-5 3-9 6-13 5-7 14-13 23-14 1-1 3-1 4-2 1 0 2 1 3 0 2 0 4 0 5-1l-3-1z" class="T"></path><path d="M783 393l7 1-3 1c-4 1-8 0-12 2l-1 1c0 1 0 2-1 3l1 1v1l-1-1c-2 2-3 3-5 4s-4 1-6 2l-11 1-1 1c-2 1-4 0-5 0-6 0-11-1-16-3 7-1 15-1 22-3s13-5 19-6c2 0 2 0 3-1h0v-1c3-1 4-2 6-2 2 1 3 0 4-1z" class="X"></path><path d="M702 522h1l-1 5h1l1-3v-2h1c0-1 1-2 1-3l1-3v-2h0 2l-1 6c-2 10-5 20-11 28-4 5-8 9-14 12-1 1-2 2-3 2-3 0-7 2-9 3l-13 3c-3 1-6 2-8 5h0v1h-1v-1c2-5 12-6 17-8 4-1 7-3 10-4 12-7 20-16 24-29 1-3 1-7 2-10h0z" class="I"></path><path d="M697 548l1 2c-5 6-21 15-21 23 1 2 2 4 2 6 1 4 0 8-1 12h-1v-1c1-4 2-9-1-13s-6-4-10-5c-4 0-11 0-13 2-3 2-3 4-3 7h-1c0-3-1-5 0-7h1v-1h0c2-3 5-4 8-5l13-3c2-1 6-3 9-3 1 0 2-1 3-2 6-3 10-7 14-12z" class="L"></path><path d="M650 573h0c2-3 5-4 8-5l2 1v1l2-1h1c1 1 2 0 3 0h1l-1 1c-5 0-12 0-16 3z" class="B"></path><path d="M667 569c1 0 3 0 5-1 2 1 3 2 4 3v4c-2-3-7-3-10-5l1-1z" class="H"></path><path d="M658 568l13-3c2-1 6-3 9-3-3 3-4 5-4 9-1-1-2-2-4-3-2 1-4 1-5 1h-1c-1 0-2 1-3 0h-1l-2 1v-1l-2-1z" class="N"></path><defs><linearGradient id="U" x1="274.249" y1="441.048" x2="293.619" y2="466.071" xlink:href="#B"><stop offset="0" stop-color="#d4d2d3"></stop><stop offset="1" stop-color="#fcfbfb"></stop></linearGradient></defs><path fill="url(#U)" d="M281 439c2-1 3-2 3-4 1 1 1 3 0 5s-1 7-1 9l1 1h0 0l1-1 3-3c1-1 3-3 5-4 1 0 1 0 1-1h1c1 0 2 1 3 1v1h1c0 1 1 1 2 1 0 2 0 5-1 6l-3-1c-2 1-3 2-5 3v-1l2-2v-1c-3 1-8 7-9 10-2 4-4 8-4 12-1 3 0 5-1 7h-1c-2-8-1-20 0-28 1-3 2-6 2-9v-1z"></path><defs><linearGradient id="V" x1="315.729" y1="419.382" x2="288.659" y2="435.59" xlink:href="#B"><stop offset="0" stop-color="#4c4c4d"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#V)" d="M303 419h1c3-1 3-2 6-2 2 2 4 1 6 2l-1 1c-1 0-1 1-1 2l-6 4 1 1c-3 3-6 6-8 9-1 2-1 5-3 7v-1c-1 0-2-1-3-1h-1c0 1 0 1-1 1-2 1-4 3-5 4l4-12c2-4 6-13 11-15z"></path><path d="M298 442v-2l-1-1c1-5 7-10 11-13l1 1c-3 3-6 6-8 9-1 2-1 5-3 7v-1z" class="B"></path><defs><linearGradient id="W" x1="263.838" y1="399.773" x2="261.723" y2="406.061" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#707070"></stop></linearGradient></defs><path fill="url(#W)" d="M250 392h0c2 1 4 1 6 1h2c1 0 1 1 1 2l1 1h-2l1 1h1l-1 1c3 1 6 1 9 2s6 3 9 4c6 2 15 3 22 1h2c-6 3-13 4-20 5-1 0-3 1-4 0h0c-1-1-1-1-2-1-7-1-12 1-17 5v1l-1-1c2-2 3-3 6-4l1-1-1-1c1 0 1 0 1-1-3-2-5-4-9-5 1 0 1-1 1-1 1-1 0-3-1-4-2-1-4-1-6-1-1-1-2-2-4-3 2 0 3-1 5-1z"></path><path d="M225 118c2-5 7-6 11-8-2 2-5 4-5 7 0 7 9 15 14 20-3 0-4-2-5-4-2 2-3 5-5 8l1 1-1 1c-2 1-2 3-4 4-1 0-1-1-2-1 4-3 7-9 9-13-4 5-7 7-13 8h-1c1-2 1-3 1-5-1-4-1-8 0-12 0-2 1-4 0-6z" class="D"></path><path d="M331 548v1l6 6c3 3 7 5 11 8h0c2 0 4 1 6 1l1-1c3 2 7 3 11 4 3 1 8 2 10 4 2 1 3 3 3 5s0 4-1 6v-2c-1-2-1-5-3-6-4-3-12-2-17-1-3 0-5 1-7 4-3 4-2 10-1 16-2-4-3-9-2-14 0-2 1-4 2-5 1-9-16-18-21-23 2 0 2 1 4 2h0 0c-1-1-2-2-2-4v-1z" class="C"></path><path d="M348 563c2 0 4 1 6 1 6 2 13 4 19 6 1 1 3 2 4 3h0-1c-2-1-5-2-7-2h-8c-3 0-6 1-9 1h0c-1-4-2-6-4-9z" class="J"></path><path d="M352 572c1-1 2-3 3-4 5 1 9 1 14 3h-8c-3 0-6 1-9 1z" class="D"></path><path d="M845 318l1 1c-2 6-9 10-15 12s-12 3-17 8c-1 0-2-1-2-1l3-4 5-6h-7-1-4c-2-1-3-2-6-2h0-1c1-2 4-2 6-3 1-1 2-1 4-1h3c2 1 6 1 8 1 0-1 2-3 3-4 1 2 1 3 2 4 3 2 7 1 9 1 4-1 7-3 9-6z" class="M"></path><path d="M868 141c13-7 22-17 30-29 1-2 3-7 5-8-2 12-8 23-15 32-2 4-5 8-8 11l-9 9c-1-1-2-2-3-2 4-3 7-6 10-10l4-6c-2 1-3 3-4 4l-2-2v-2h1-2c-2 2-4 3-6 4l-1-1z" class="T"></path><path d="M868 154c4-3 7-6 10-10 0 1 0 2-1 2-1 2-2 3-2 5 1-2 3-3 4-4h1l-9 9c-1-1-2-2-3-2z" class="C"></path><defs><linearGradient id="X" x1="690.361" y1="436.014" x2="706.859" y2="440.466" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#8d8c8d"></stop></linearGradient></defs><path fill="url(#X)" d="M692 444l-1-1-1-1c-1-1-1-6-2-7 1-2 2-4 3-5 2-2 4 1 6 1-1-1-1-2-1-3 2-1 4 0 6 0 2 1 4 3 5 5-1 2-1 3 0 6v1c1 2 4 3 4 6-1-1-1-2-2-3l-4-3c2 2 3 4 4 7l1 2c1 3 2 5 2 7-3-4-5-8-9-11h-3-4l-4-1z"></path><path d="M692 444c0-1 0-3 1-3v-2h1 0c0 2 1 3 1 4 2 0 4 1 5 2h-4l-4-1z" class="P"></path><path d="M785 136c4-4 13-12 13-18 0-2-1-3-2-5l-2-1-2-2c6 2 10 4 13 9 3 7 2 15 1 22-3 0-6-1-9-2h-1-2c-1-2-2-4-4-6 0 2 3 5 3 6l-1 1-1-2v-1l-2-3c-1 0-2 1-4 2h0z" class="D"></path><path d="M867 213c4 2 9 4 12 8-10-3-20-3-29 2-11 6-18 17-23 28l-3 9c-2 2-3 3-3 5-1-1-1-2-2-3 2-12 9-25 18-33 3-2 6-4 8-7l3-4h1c-1-1-1-1-2-3 3 0 6 0 9-1 3 0 6 0 8 1h1c0-1 1-1 2-2z" class="M"></path><path d="M847 215c3 0 6 0 9-1l-2 1h0c1 0 2 0 3 1h1c-3 1-6 1-9 2-1-1-1-1-2-3z" class="G"></path><defs><linearGradient id="Y" x1="708.636" y1="423.468" x2="737.675" y2="432.536" xlink:href="#B"><stop offset="0" stop-color="#3a393b"></stop><stop offset="1" stop-color="#7a7a7a"></stop></linearGradient></defs><path fill="url(#Y)" d="M710 419c2-2 3-1 6-2 1 0 3-1 4-1 6 4 11 8 14 15 1 3 2 7 3 10h-6 0l-3 1c-1-1-2-3-2-4-4-8-10-12-18-16l-4-3h2c1 0 2 0 3-1l1 1z"></path><path d="M709 418l1 1h0c3 4 16 12 16 18v1c-4-8-10-12-18-16l-4-3h2c1 0 2 0 3-1z" class="J"></path><path d="M127 102c1 3 3 6 5 9 5 10 13 19 22 26 1 1 1 2 2 2s2 1 3 1l2 2h-1l-1 1h1c-1 1-1 0-2 0h0l-1-1c-1 0-3-1-4-2-1 0-2 1-3 0h-1l1 2h0c1 2 1 3 2 4s2 2 2 3c1 2 3 3 5 4h-1l-1 1c-1 0-1 0-1-1-2-1-2-3-4-3-2-1-4-3-5-5-8-9-14-20-18-31-2-4-2-8-3-12h1zm96 211c0 1-1 2-1 3-1 2-2 3-2 5 3 3 8 0 11 5v1c-4 0-10 0-12 4-1 1-1 2-2 3-1 0-2-1-4-2h0v1l-2 2c-6-3-13-4-20-7-5-3-7-5-9-10l1-2c2 4 4 6 9 8 2 0 7 1 9 0s2-5 3-4h0c3 2 4 3 7 2h6l3-3c1-2 1-5 3-6z" class="M"></path><path d="M223 313c0 1-1 2-1 3-1 2-2 3-2 5 3 3 8 0 11 5v1c-4 0-10 0-12 4-1 1-1 2-2 3-1 0-2-1-4-2h0c-2-1-4-3-5-4 3-3 9-2 12-5v-1h-3l3-3c1-2 1-5 3-6z" class="C"></path><path d="M305 494c1 1 1 2 1 3 0 2-1 3-1 4h1 0l1 1v1c3 3 7 4 10 6l1 5v2c1 1 0 3 0 4 2 10 7 20 13 28v1c0 2 1 3 2 4h0 0c-2-1-2-2-4-2h0c-5-6-9-13-12-20-2-5-3-11-6-16-1-2-2-4-4-5-5 0-8 4-12 6-6 3-10 2-16 0 5 0 8-1 13-2 0 0 1 0 2-1 4-2 7-7 9-11v-1c1-1 1-2 1-4 1 0 1-1 1-2v-1z" class="T"></path><path d="M305 501h1 0l1 1v1c3 3 7 4 10 6l1 5v2c1 1 0 3 0 4-2-3-2-7-5-9-2-2-3-3-5-4-2 0-2-1-3-2v-1l-1-1 1-2z" class="G"></path><defs><linearGradient id="Z" x1="265.207" y1="368.206" x2="240.625" y2="388.592" xlink:href="#B"><stop offset="0" stop-color="#bcbbbb"></stop><stop offset="1" stop-color="#f0eff2"></stop></linearGradient></defs><path fill="url(#Z)" d="M236 371h0c1 0 2 0 2-1 7-2 17-2 24 1l4 1c3 2 6 3 9 4 2 1 4 2 5 3-2 0-6-1-7 0l-2 1c-10-2-20-3-30-1-3 1-6 1-8 3-1 1-1 2-2 2 0 2 0 3-1 4h0c0-2-1-4 0-6 0-4 2-9 6-11z"></path><path d="M262 371l4 1c3 2 6 3 9 4-3 1-6-1-10-2-5-1-10-1-15-1 2-1 4-1 6-1 2-1 4-1 6-1z" class="D"></path><defs><linearGradient id="a" x1="235.994" y1="383.333" x2="249.849" y2="365.28" xlink:href="#B"><stop offset="0" stop-color="#696b6b"></stop><stop offset="1" stop-color="#858284"></stop></linearGradient></defs><path fill="url(#a)" d="M236 371h0c1 0 2 0 2-1 7-2 17-2 24 1-2 0-4 0-6 1-2 0-4 0-6 1-5 1-14 2-17 6-1 2-2 4-2 5 0 2 0 3-1 4h0c0-2-1-4 0-6 0-4 2-9 6-11z"></path><path d="M255 402c4 1 6 3 9 5 0 1 0 1-1 1l1 1-1 1c-3 1-4 2-6 4l1 1v-1c5-4 10-6 17-5 1 0 1 0 2 1h0c-6 1-15 4-19 10 0 1-1 1 0 2l2 2h0l-3 1c-2 6-4 19-2 25 1 3 4 5 7 7 2 0 3 1 4 1-6-1-10-2-13-7-3-4-4-11-3-17l-1-4 1-1c-1-1-1-1-2-1 1-2 1-3 2-4 0-2 1-2 2-3l1-3v-10-3c1 0 1-1 1-1l1 1v-3z" class="C"></path><path d="M253 418v4c-1 3-2 6-2 9 0 2-1 2-1 3l-1-4 1-1c-1-1-1-1-2-1 1-2 1-3 2-4 0-2 1-2 2-3l1-3z" class="Q"></path><path d="M255 402c4 1 6 3 9 5 0 1 0 1-1 1-2 1-3 2-5 3l-2 2c0-3 0-5-1-8h0v-3z" class="K"></path><defs><linearGradient id="b" x1="661.841" y1="463.589" x2="679.195" y2="494.39" xlink:href="#B"><stop offset="0" stop-color="#302f31"></stop><stop offset="1" stop-color="#8b8b8b"></stop></linearGradient></defs><path fill="url(#b)" d="M663 472v-5h0l-1-4h0 1 1v1h1c1 1 2 1 4 3h0c0 2 1 3 2 5h1c1 0 4 0 5 1 1 0 2 2 2 3 3 4 3 10 3 15 0 2 0 4-1 6h-1-6-3v1c-1 0-1 0-1-1h0l-5-13c1-3 0-7-1-10v-1l-1-1z"></path><path d="M674 497c0-1 0-1 2-2l1 1c1 0 2-1 4-2v-2l1-1c0 2 0 4-1 6h-1-6z" class="H"></path><defs><linearGradient id="c" x1="229.482" y1="215.839" x2="224.212" y2="220.656" xlink:href="#B"><stop offset="0" stop-color="#272628"></stop><stop offset="1" stop-color="#424141"></stop></linearGradient></defs><path fill="url(#c)" d="M195 210c2 0 8 0 10 1v1 1 1-1c3 0 25 2 27 3 0 1 0 1 1 2h0c1 1 0 3 1 5 0 1 0 2 1 3s2 2 3 4c1-1 2-1 2-2s0-2 1-3l1 1h0c-1 1-1 1-1 2v1 2l-1 1v4c0 2-1 4-2 5v1l-1-2v-1c0-1 1-3 1-4-1-2-4-3-6-4l-10-4-8-5c-2 0-4-2-6-3l-2-1-5-1 1-1h1 1c-1-1-1-1-1-2-4 0-8 0-12-1 1-1 2-2 4-3z"></path><path d="M206 218s0-1 1-1l-1-1v-1c3 1 6 1 9 3 2 0 4 2 5 3 5 3 10 5 16 7 1 2 1 2 1 4-2 0-5-2-7-3-2 0-3-1-4-2-2-1-5-2-7-3-1-1-2-2-3-2h-2c-2 0-4-2-6-3l-2-1z" class="P"></path><defs><linearGradient id="d" x1="342.436" y1="437.391" x2="335.593" y2="438.106" xlink:href="#B"><stop offset="0" stop-color="#2b2b2c"></stop><stop offset="1" stop-color="#49494a"></stop></linearGradient></defs><path fill="url(#d)" d="M322 429c3-2 8-8 12-8 1 0 2 0 3 1 0 1 0 1-1 2l-1 1h0v2h1l1-1h1 0c1-1 0-1 1-1h1c3-1 5-2 9-2h0c-1 0-2 1-3 1h-2l-3 2c0 1 0 2 2 3l1-1h1 0l1-1 2-2h1c-1 1-1 2-2 2-2 1-3 3-4 4 0 1-1 1-1 3l1 3v1 3c2-1 3-2 5-3 0-1 0 0 1-1 1 0 2-2 3-3v1c-1 1-1 2-2 3-3 3-10 6-13 6-4 1-8 1-11 0-1 0-3 1-4 1 0-2 0-3-1-4h-1c-1 0-1 1-2 1h-1l1-1c2-2 3-4 5-5h0l3-3c1 0 1-1 2-2v-1h-1-1-2c-1 0-1 0-2-1z"></path><defs><linearGradient id="e" x1="331.537" y1="438.317" x2="323.262" y2="435.409" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#888787"></stop></linearGradient></defs><path fill="url(#e)" d="M323 436h1l7-5c1-1 2-1 3-1 2 1 3 1 3 3 0 3-3 6-5 10h-2l-4 1c-1 0-3 1-4 1 0-2 0-3-1-4h-1c-1 0-1 1-2 1h-1l1-1c2-2 3-4 5-5z"></path><defs><linearGradient id="f" x1="330.509" y1="466.576" x2="316.27" y2="463.224" xlink:href="#B"><stop offset="0" stop-color="#656465"></stop><stop offset="1" stop-color="#939292"></stop></linearGradient></defs><path fill="url(#f)" d="M338 446l1 1c-1 5-5 8-8 12-8 8-9 19-11 29-4-7-5-15-4-23l1-10 3-6c4-5 12-2 18-3h0z"></path><defs><linearGradient id="g" x1="649.723" y1="508.729" x2="671.367" y2="540.852" xlink:href="#B"><stop offset="0" stop-color="#959495"></stop><stop offset="1" stop-color="#bbb9ba"></stop></linearGradient></defs><path fill="url(#g)" d="M680 516c1-2 1-4 2-6 1-4 1-6 3-9-1 9-4 21-9 30-2 3-5 7-7 10s-4 6-7 8h0c-1 0-2 0-2 1-3 2-6 2-9 3-2 0-3 1-5 2h-1l6-6c2 0 2 0 3-1 6-6 6-9 6-17l-1-2c0-2 0-2-2-4v-5-7c0-2-1-5-2-8l2-2h1l1-1c2 7 3 13 4 20 1 5 1 10 2 15 1 0 1-1 2-1l7-10 2-1c2-3 3-6 4-9z"></path><path d="M657 513c2 2 3 5 3 7h-3v-7z" class="F"></path><path d="M657 520h3v11l-1-2c0-2 0-2-2-4v-5z" class="L"></path><path d="M672 463c0-2 0-2 1-3 2-1 2-1 4 0 3 2 5 4 8 6h1v-1-1l1 8c1 3 0 6 0 9v10l-2 10c-2 3-2 5-3 9-1 2-1 4-2 6-1 3-2 6-4 9l-2 1-7 10-1-1 1-1c6-9 10-20 13-31 0-2 1-4 1-6h-1 1c1-2 1-4 1-6 0-5 0-11-3-15 0-1-1-3-2-3-1-1-4-1-5-1h-1v-1l3-1h2 0c-1-1-1-2-2-3l-1-1c-1-2-2-3-4-4h0v-1l3 2z" class="K"></path><path d="M681 507v3c0 1-1 2-1 2v1 3c-1 3-2 6-4 9l-2 1c2-7 5-12 7-19z" class="D"></path><path d="M685 488v-6c0-5 0-8-1-13v-1l3 3v1c1 3 0 6 0 9v10l-2 10c-2 3-2 5-3 9-1 2-1 4-2 6v-3-1s1-1 1-2v-3l4-19z" class="H"></path><defs><linearGradient id="h" x1="674.158" y1="465.323" x2="683.888" y2="467.467" xlink:href="#B"><stop offset="0" stop-color="#494949"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#h)" d="M672 463c0-2 0-2 1-3 2-1 2-1 4 0 3 2 5 4 8 6h1v-1-1l1 8v-1l-3-3v1c1 5 1 8 1 13v6c0-4 0-10-2-14h0c0-1-1-2-1-3-2-2-5-5-7-5l-3-3z"></path><path d="M672 463c0-2 0-2 1-3 2-1 2-1 4 0l-2 1c-1 0-1 1-1 1 0 1 1 3 1 4l-3-3z" class="N"></path><path d="M792 232c0-2 0-4 1-7 0-2 0-3 2-4v-1c8-7 18-6 28-7v-1h-12 0 4c2-1 4 0 5-1h1l4-2 3-3h1c1-1 2-1 3-2v4l2 3 3 2h0l-1 3-11 2c-1 1-3 1-4 2l-2 2h-4l-23 10z" class="X"></path><path d="M834 211l3 2h0l-1 3-11 2h1 0c2-3 5-6 8-7z" class="P"></path><path d="M792 232c0-2 0-4 1-7 0-2 0-3 2-4v-1c8-7 18-6 28-7v-1h-12 0 4c2-1 4 0 5-1h1l4-2 3-3h1c1-1 2-1 3-2v4c-2 1-6 4-8 5v2c-1 1-1 2-2 2h-1c1-1 1-2 2-3h-1-1c-2 1-5 1-7 2-6 3-12 6-17 10-2 0-4 1-4 3l-1 2h1l12-5c5-3 10-6 15-8l1 1c-2 1-4 1-6 3l-23 10z" class="B"></path><defs><linearGradient id="i" x1="624.991" y1="563.649" x2="653.241" y2="555.875" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#9d9b9d"></stop></linearGradient></defs><path fill="url(#i)" d="M645 555h1c2-1 3-2 5-2 3-1 6-1 9-3 0-1 1-1 2-1-4 4-8 7-12 10-4 0-6 2-10 4s-8 3-11 5c-1 2-1 4-3 5v1c1 0 1 0 1 1-6 5-10 10-14 17-1 2-2 4-2 6-3 4-5 11-5 16-1-1-1-2-1-3h0c-1 0-1 1-1 1 0-3 1-7 2-10h-1l8-28c3 0 4-1 6-2 1-1-1-2-2-4 4 0 7-3 10-4 6-3 12-5 18-9z"></path><defs><linearGradient id="j" x1="608.823" y1="587.447" x2="623.002" y2="590.021" xlink:href="#B"><stop offset="0" stop-color="#787675"></stop><stop offset="1" stop-color="#9fa0a3"></stop></linearGradient></defs><path fill="url(#j)" d="M629 568c-1 2-1 4-3 5v1c1 0 1 0 1 1-6 5-10 10-14 17-1 2-2 4-2 6-3 4-5 11-5 16-1-1-1-2-1-3h0c-1 0-1 1-1 1 0-3 1-7 2-10 1-4 2-8 3-11 2-5 6-10 9-15s6-6 11-8z"></path><path d="M712 456c0-2-1-4-2-7l-1-2c-1-3-2-5-4-7l4 3c1 1 1 2 2 3s1 2 2 3h1c1 1 1 2 2 3 0 1 1 2 1 3h-1c2 7 4 14 2 21l-1 1c0 1 0 2-1 3 0 2 0 3-1 4-1 3-1 6-1 8v13h-1c-1 0-2 1-3 2l-1 3v4h-2 0v2l-1 3c0 1-1 2-1 3h-1v2l-1 3h-1l1-5h-1v-19l1-1v-4c1-1 1 0 1-1 1-3 3-6 4-9l2-4c5-10 5-18 2-28z" class="C"></path><path d="M716 480c0 2 0 3-1 4-1 3-1 6-1 8v13h-1c-1 0-2 1-3 2l-1 3v4h-2c1-4 1-8 1-12 1-8 4-15 8-22z" class="W"></path><defs><linearGradient id="k" x1="356.999" y1="471.872" x2="347.116" y2="496.158" xlink:href="#B"><stop offset="0" stop-color="#484748"></stop><stop offset="1" stop-color="#818081"></stop></linearGradient></defs><path fill="url(#k)" d="M353 466l-1 2 4 4h0 1 4c1 1 2 1 2 3v3l2-2c0 1 0 2 1 3l-1 2c-2 2-4 6-5 9-1 1-2 3-2 4l-1 1h-1 0l-5 2v1l-1 1-2-2h-2v2l-1-1h-1c-2-7-2-15-1-23 0 0 1-1 1-2l1-2c0-2 2-3 3-3l5-2z"></path><path d="M363 478l2-2c0 1 0 2 1 3l-1 2c-2 2-4 6-5 9-1 1-2 3-2 4l-1 1c0-5 4-12 6-17z" class="V"></path><path d="M353 466l-1 2 4 4h-5c-5 4-6 10-6 16-1 3 0 6 0 10h-1c-2-7-2-15-1-23 0 0 1-1 1-2l1-2c0-2 2-3 3-3l5-2z" class="S"></path><path d="M213 333c3 3 6 7 9 10 1 2 2 2 4 3l3 3c6 2 10 4 15 7 4 3 9 8 14 9l10 3v1h-1c-2-1-6-2-9-3l-1 3h2 1c1 1 1 1 2 1l4 1v1l-4-1c-7-3-17-3-24-1 0 1-1 1-2 1h0c2-2 5-5 8-7 0-2 0-3-1-4-3-4-8-5-12-6-7-1-13-2-18 2-1 1-1 1-2 1v-3c-3-1-6 0-9 1-8 2-12 8-16 15l-1-1c2-9 9-14 17-17 6-3 10-4 14-10h0c-1-4-2-5-5-7l2-2z" class="M"></path><path d="M226 346l3 3-12-1 1-1c2-1 5-1 8-1z" class="C"></path><path d="M213 333c3 3 6 7 9 10 1 2 2 2 4 3-3 0-6 0-8 1 0-2 0-2-1-4h1l-1-2-1 1h0c-1-4-2-5-5-7l2-2z" class="Q"></path><defs><linearGradient id="l" x1="698.099" y1="466.858" x2="710.682" y2="463.97" xlink:href="#B"><stop offset="0" stop-color="#6a6869"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#l)" d="M703 445c4 3 6 7 9 11 3 10 3 18-2 28l-2 4v-1l-1 1c-1-6-2-12-5-18-4-9-10-14-17-20v-2h1 1v-1c1-1 4 0 5 0 4 0 7-1 10-1l1-1z"></path><path d="M703 445c4 3 6 7 9 11 3 10 3 18-2 28l-2 4v-1c3-10 6-18 2-29-1-4-3-10-8-12l1-1z" class="S"></path><path d="M160 214c3-1 6-1 9-1 0 0 0 1 1 1h2l1 2h1c1 0 3 0 5 1v1c2 2 3 4 6 5 7 6 13 13 18 21v1c5 11 10 22 6 34-1 2-4 5-3 7l1 1c1 0 1 0 2 1h0c2 1 4 3 6 4-3 0-4-2-7-1h0c-1-1-3-3-6-3-2-1-4-1-6-2-3-1-4-2-6-2-2-3-3-5-3-8 0-5 1-8 4-11-1 2-2 5-2 8s0 6 2 8c2 1 4 2 6 2 3 0 5-2 7-4l1-1c2-4 1-8 1-12l-1-4c-4-14-14-32-27-39-9-6-19-5-29-2 3-3 7-5 11-7z" class="M"></path><path d="M160 214c3-1 6-1 9-1 0 0 0 1 1 1h2l1 2h1c1 0 3 0 5 1v1l-7-2h-1c-4-1-8 0-11-2z" class="C"></path><path d="M206 266h1c0 4 3 5 1 9-1 3-2 8-4 10h0v-1l1-3c0-2 0-1-1-2l1-1c2-4 1-8 1-12z" class="G"></path><path d="M845 318v-5h1c2 3 2 6 1 9-1 4-5 7-8 10-4 2-8 4-12 7-1 0-3 1-3 2v2h1l3-1c9 4 15 12 18 21 3 10 2 18-1 27v-11c-1-2-2-4-2-7l1-1c-1-2-1-5-2-6-3-7-10-11-16-13-3-1-6-2-8-3s-4-4-5-6h-1c0-2 1-3 2-4 5-5 11-6 17-8s13-6 15-12l-1-1zm-659 52c0 1-2 5-2 6v1c-1 4-1 8-1 13-3-9-4-19 0-28 2-8 8-16 16-20 1-1 2-1 4 0l1-1c-2-4-10-7-14-9-3-2-5-5-7-7-2-3-3-6-2-9 0-2 1-3 2-4 0 1 1 3 0 4h0l-1 2c2 5 4 7 9 10 7 3 14 4 20 7 3 2 4 3 5 7h0c-4 6-8 7-14 10-8 3-15 8-17 17l1 1z" class="D"></path><path d="M790 394l3 1c0 1-1 1-2 2-1-1-2-1-3 0-1 0-2 2-2 3-3 3-6 4-9 7h4v1c-3 7-1 18 1 25l-4-3c0 8 0 18-6 24-2 2-6 4-9 4h-1c4-2 8-4 10-8 3-5 1-18-1-23-1-1-1-2-2-2 0 0-1-1-2-1 1-1 2-1 3-2 0-1-1-2-2-3-4-5-12-8-18-9l1-1 11-1c2-1 4-1 6-2s3-2 5-4l1 1v-1l-1-1c1-1 1-2 1-3l1-1c4-2 8-1 12-2l3-1z" class="C"></path><path d="M762 408c2-1 4-1 6-2s3-2 5-4l1 1-4 5-1 1 1 2 2 3v1c-3-3-6-5-10-7z"></path><path d="M790 394l3 1c0 1-1 1-2 2-1-1-2-1-3 0-1 0-2 2-2 3-3 3-6 4-9 7h0c-1 5-1 10-1 15-2-4-1-9-1-14-2 0-2 1-3 1l-2 2-1-2 1-1 4-5v-1l-1-1c1-1 1-2 1-3l1-1c4-2 8-1 12-2l3-1z" class="U"></path><defs><linearGradient id="m" x1="659.33" y1="491.623" x2="678.847" y2="525.356" xlink:href="#B"><stop offset="0" stop-color="#818080"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#m)" d="M663 490l3 1 1 2h0l3 4c0 1 0 1 1 1v-1h3 6 1c0 2-1 4-1 6-3 11-7 22-13 31l-1 1 1 1c-1 0-1 1-2 1-1-5-1-10-2-15-1-7-2-13-4-20l-1 1h-1l-2 2-1-2c-1-1-3-3-4-5 2 0 3 0 4-1v-1h1c1-1 3-1 4-2l4-4z"></path><path d="M655 496h2 1l1 1v-1l2-2h1l1 1c-1 1-3 2-3 3h-1v4l-1 1h-1l-2 2-1-2c-1-1-3-3-4-5 2 0 3 0 4-1v-1h1z" class="U"></path><path d="M654 503c1-2 1-4 2-6 2 0 2 0 3 1v4l-1 1h-1l-2 2-1-2z" class="Y"></path><path d="M659 498h1c1 3 2 7 2 10 1 4 3 26 4 27l1 1c-1 0-1 1-2 1-1-5-1-10-2-15-1-7-2-13-4-20v-4z"></path><path d="M809 340c1 0 2-1 3-2 0 0 1 1 2 1-1 1-2 2-2 4h1c1 2 3 5 5 6s5 2 8 3c6 2 13 6 16 13 1 1 1 4 2 6l-1 1c-3-7-7-13-15-16-2-1-9-3-11-2l-1 3c-1-1-3-2-4-3-7-2-18 0-24 3-1 1-3 2-3 4 0 1 0 3 1 4 2 2 5 4 7 6h-1l-4-1c0 1 0 2 1 4l-4-1c-11-2-21 2-31 3h-2c-7 2-13 5-20 6 2-1 4-2 7-2 1-1 2-1 4-2 1 0 2-1 3-1l1-1h1c1 0 1 0 2-1 0 0 1-1 2-1v-1c4-2 9-3 12-5 4-2 8-3 12-5 3-2 5-4 8-6 3-3 8-5 12-7l8-5h0l5-5z" class="M"></path><path d="M804 345c0 1 0 1-1 2l1 1c0-1 1-1 2-1 0-1 1-1 2-1-2 2-3 3-6 4h-1c-2 0-3 1-5 0l8-5z" class="L"></path><path d="M809 340c1 0 2-1 3-2 0 0 1 1 2 1-1 1-2 2-2 4h1c-2 1-3 2-5 3-1 0-2 0-2 1-1 0-2 0-2 1l-1-1c1-1 1-1 1-2h0l5-5z" class="C"></path><defs><linearGradient id="n" x1="782.496" y1="365.099" x2="760.899" y2="381.109" xlink:href="#B"><stop offset="0" stop-color="#7f7d7e"></stop><stop offset="1" stop-color="#989897"></stop></linearGradient></defs><path fill="url(#n)" d="M752 376c3-1 5-3 8-4 9-3 19-5 28-2 0 1 0 2 1 4l-4-1c-11-2-21 2-31 3h-2z"></path><path d="M244 218l-77-46c-8-6-16-14-22-22-10-13-19-28-22-45 3 4 4 9 5 14 3 6 7 12 11 18 11 16 26 30 42 39 5 2 9 4 14 6l18 10c10 5 20 10 28 16l1 1c0 1 1 1 2 2v1c-1 1-1 1 0 2 0 2 1 3 0 4zm605-44c16-9 30-22 41-37l6-9c4-7 7-15 10-22-5 28-28 53-50 69l-75 46-1-1c1-2 3-3 4-5-1 0-1-1-2-1h0c2-1 3-2 4-4 12-11 29-19 43-26 7-3 14-6 20-10z" class="E"></path><defs><linearGradient id="o" x1="634.852" y1="536.747" x2="661.145" y2="562.75" xlink:href="#B"><stop offset="0" stop-color="#d0ced0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#o)" d="M687 491c0-2 1-4 1-7l1-2c1 2 0 4 1 6v5c0 8-2 16-4 24l-3 9c-1 1-2 3-2 4 1 2 1 3 2 4l4 4c-3-1-5-1-7-2-1-1-1-1-2-1-6 11-13 19-23 27-9 6-19 11-28 18-7 6-11 13-15 22-2 1-1 2-2 4-1 1-1 2-1 3v2c-1 1-1 0-1 1v1c0 2-1 3-1 5v-4c1-2 0-5 1-8 1-2 3-5 3-7v-1c0-2 1-4 2-6 4-7 8-12 14-17 0-1 0-1-1-1v-1c2-1 2-3 3-5 3-2 7-3 11-5s6-4 10-4c4-3 8-6 12-10h0c3-2 5-5 7-8s5-7 7-10c5-9 8-21 9-30l2-10z"></path><path d="M629 568c3-2 7-3 11-5s6-4 10-4c-8 5-16 10-23 16 0-1 0-1-1-1v-1c2-1 2-3 3-5z" class="R"></path><path d="M707 433c3 3 7 7 8 11 3 4 6 9 7 14 3 9 1 19 1 28-1 2-1 2-1 4s0 5 1 6c0 2 1 5 2 6 0 2 1 4 2 5 2 3 5 5 6 8l-1 1c-3-1-9-6-13-5-3 1-4 4-5 6-4 12-7 23-16 33l-1-2c6-8 9-18 11-28l1-6v-4l1-3c1-1 2-2 3-2h1v-13c0-2 0-5 1-8 1-1 1-2 1-4 1-1 1-2 1-3l1-1c2-7 0-14-2-21h1c0-1-1-2-1-3-1-1-1-2-2-3h-1c-1-1-1-2-2-3 0-3-3-4-4-6v-1c-1-3-1-4 0-6z" class="M"></path><path d="M707 433c3 3 7 7 8 11h0c-2-1-2-3-3-4l-1-1-1 2c1 2 3 4 3 6l1 2h-1c-1-1-1-2-2-3 0-3-3-4-4-6v-1c-1-3-1-4 0-6z" class="F"></path><path d="M709 510l3-3h1l2-2c2-1 4-1 6-2l1 2c-1 0-1 1-2 1-4 1-6 3-8 7-1 2-2 5-4 7l1-6v-4z" class="C"></path><path d="M716 480c1-1 1-2 1-3l1-1c2-7 0-14-2-21h1c1 4 3 9 3 13 0 5-1 11-1 16 0 4 1 9 1 13 0 2 0 5 1 6-2 1-4 1-6 2l-2 2h-1l-3 3 1-3c1-1 2-2 3-2h1v-13c0-2 0-5 1-8 1-1 1-2 1-4z" class="D"></path><defs><linearGradient id="p" x1="253.103" y1="127.929" x2="267.16" y2="167.021" xlink:href="#B"><stop offset="0" stop-color="#959394"></stop><stop offset="1" stop-color="#cecccd"></stop></linearGradient></defs><path fill="url(#p)" d="M231 147c2-1 2-3 4-4l1-1-1-1c2-3 3-6 5-8 1 2 2 4 5 4l13 7h0 1l1 1c4 3 9 4 14 4 2-1 5 0 7 0 2 1 3 1 5 1h1 1v1l1-1h0 1 1c0 1 1 2 1 4h0v3l-3-2h-2c-1-1-2-1-3-1-1 1-1 2-2 3-2 1-4 2-5 3s-2 1-3 1-2 0-3 1v1l3 3h0 0l-2 1-1 1-3-2-25-13c-4-2-8-3-12-6z"></path><path d="M279 155c1 0 2 0 3-1h2c-1 1-1 2-2 3-2-1-2 0-3-2z" class="C"></path><path d="M271 163v1l-1-1v-3c1-1 2-3 4-3l1-1 1-1c1 0 2-1 3 0 1 2 1 1 3 2-2 1-4 2-5 3s-2 1-3 1-2 0-3 1v1z" class="F"></path><defs><linearGradient id="q" x1="221.045" y1="245.579" x2="198.547" y2="207.28" xlink:href="#B"><stop offset="0" stop-color="#b0aeaf"></stop><stop offset="1" stop-color="#cfcecf"></stop></linearGradient></defs><path fill="url(#q)" d="M182 214c3 0 6-1 9-1 4 1 8 1 12 1 0 1 0 1 1 2h-1-1l-1 1 5 1 2 1c2 1 4 3 6 3l8 5 10 4c2 1 5 2 6 4 0 1-1 3-1 4h-1c-2-2-4-2-7-1l-6 3c-2 1-6 1-8 1-1 2-1 3-2 5-1-1-1-1-2-1v-1c-1 0-2 1-3 1l-7-7c1 2 2 3 2 5h0c-5-8-11-15-18-21-3-1-4-3-6-5v-1c-2-1-4-1-5-1h-1l-1-2 8 1c1 0 1 0 2-1z"></path><path d="M179 217l9 3c-1 1-3 1-3 3h0c-3-1-4-3-6-5v-1z" class="V"></path><path d="M196 217h5l5 1 2 1c2 1 4 3 6 3l8 5c-3 0-5-1-7-2l-13-5c-3-1-4-2-6-3z" class="R"></path><defs><linearGradient id="r" x1="194.842" y1="210.156" x2="188.093" y2="219.433" xlink:href="#B"><stop offset="0" stop-color="#818181"></stop><stop offset="1" stop-color="#aaa7a9"></stop></linearGradient></defs><path fill="url(#r)" d="M182 214c3 0 6-1 9-1 4 1 8 1 12 1 0 1 0 1 1 2h-1-1l-1 1h-5c-2-1-4-1-6-1-3 0-8 1-10-1 1 0 1 0 2-1z"></path><path d="M188 220c7 1 13 4 19 7 3 1 6 2 8 5v2h-1c0-1-1-1-3-2-6-3-13-7-20-9-2 0-4-1-6 0 0-2 2-2 3-3z" class="K"></path><path d="M185 223c2-1 4 0 6 0 7 2 14 6 20 9 2 1 3 1 3 2h1v8c-1 2-1 3-2 5-1-1-1-1-2-1v-1c-1 0-2 1-3 1l-7-7c1 2 2 3 2 5h0c-5-8-11-15-18-21h0z" class="R"></path><path d="M215 234v8c-1 2-1 3-2 5-1-1-1-1-2-1 0-4 2-8 3-12h1z" class="B"></path><path d="M336 497c1-1 1-2 1-4v-9c1 0 1 0 2-1v3 3c-1 1-1 2 0 3v1c3 22 12 46 30 61l32 22c0-1-1-2-1-3-3-4-8-5-12-9-2-2-3-5-5-6-1-1-2-2-2-3 5 4 11 6 16 8 3 2 5 4 8 5h4 0l-1 1c-1 1-1 1-2 3 2 1 3 1 5 1 0 1 1 2 2 3 1 4 2 9 3 13 3 9 7 19 9 29-2 1-1 6-1 8-1 4-3 7-5 10-4 5-8 6-14 6 0 0 1-1 2-1s3-1 4-2c4-2 7-7 8-12 1-6 0-13-2-19-4-15-12-25-25-33-8-5-16-9-23-15-9-7-15-15-21-24h-1c-2 1-5 2-7 1 2-1 4-3 6-5-4-11-8-23-10-35z" class="M"></path><defs><linearGradient id="s" x1="408.71" y1="612.66" x2="396.89" y2="555.82" xlink:href="#B"><stop offset="0" stop-color="#6d6b6e"></stop><stop offset="1" stop-color="#9d9c9c"></stop></linearGradient></defs><path fill="url(#s)" d="M381 555c5 4 11 6 16 8 3 2 5 4 8 5h4 0l-1 1c-1 1-1 1-2 3 2 1 3 1 5 1 0 1 1 2 2 3 1 4 2 9 3 13 3 9 7 19 9 29-2 1-1 6-1 8l-3-14c-4-15-9-26-20-36 0-1-1-2-1-3-3-4-8-5-12-9-2-2-3-5-5-6-1-1-2-2-2-3z"></path><defs><linearGradient id="t" x1="762.805" y1="141.225" x2="770.848" y2="167.978" xlink:href="#B"><stop offset="0" stop-color="#979595"></stop><stop offset="1" stop-color="#d6d4d6"></stop></linearGradient></defs><path fill="url(#t)" d="M785 136h0c2-1 3-2 4-2l2 3v1l1 2 1-1c0-1-3-4-3-6 2 2 3 4 4 6l5 8c-9 6-20 11-30 16-4 2-8 4-11 6s-6 4-8 6c-4 3-7 7-11 9l-3-3h-1l-1-1 1-2c1-1 1-1 1-2l2-2v-2c1-1 1-1 1-2v-1c-2 1-3 1-4 1s-2 1-2 1v-1c0-1 0-1 1-2 0-2 1-3 1-5l1-2-1-1c0-2 1-4 1-6l2 1 1-1 4-1h2 1 1c1 0 0 0 2 1h0v-2c1-1 2-1 3-1 3 0 4 0 5 2h1c2-2 7-2 9-4v-1l1-1-1-1c1-1 1-1 3-2 5-1 10-5 15-8z"></path><path d="M741 162c1-1 1-1 2-1-1 0-1 1-2 1l-1 2c1 0 1 0 2-1 1 0 1 0 2-1v2c-1 1-3 0-4 2v1 1c-1 0-1 0-2-1h-2l1 1-1 1h-2v-1c0-2 1-3 1-5h1 1c2 0 2 0 4-1z" class="D"></path><path d="M749 154v-2c1-1 2-1 3-1 3 0 4 0 5 2h1c2 1 4 3 6 4-2 0-2-1-3 0-2 0-4-1-6-1l3 3-1 1-3-3-1-1c-2-1-3-2-4-2z" class="G"></path><path d="M743 153h2 1 1c1 0 0 0 2 1h0c1 0 2 1 4 2l1 1 3 3h1v1c0 1-1 2-2 2h0l-2 2v1h0-2c-1 1-1 1-2 1h0l-1-1 1-1h1c0-1 0-2-1-3l-1-1-1-1c-2 0-3 0-5 1-1 0-1 0-2 1-2 1-2 1-4 1h-1-1l1-2-1-1c0-2 1-4 1-6l2 1 1-1 4-1z" class="I"></path><path d="M752 166v-3h0l1-1 1-1c1 1 2 1 2 2l-2 2v1h0-2z" class="C"></path><path d="M736 161v-2h1 2c1 1 1 2 2 3-2 1-2 1-4 1h-1-1l1-2z" class="E"></path><path d="M743 153h2 1 1c1 0 0 0 2 1h0c1 0 2 1 4 2l1 1v2c-2-1-3-1-4 0l-1 1c-1 0-2-1-3-1l1-1c-2-1-5-1-7-2v-1c1 0 2 0 2-1l1-1z" class="C"></path><path d="M793 139c0-1-3-4-3-6 2 2 3 4 4 6l5 8c-9 6-20 11-30 16-4 2-8 4-11 6s-6 4-8 6c-4 3-7 7-11 9l-3-3h-1l-1-1 1-2c1-1 1-1 1-2l2-2v-2c1-1 1-1 1-2v-1c-2 1-3 1-4 1s-2 1-2 1v-1c0-1 0-1 1-2v1h2l1-1-1-1h2c1 1 1 1 2 1v-1-1c1 0 2 1 2 1 1 2 1 4 0 6 0 1-1 1-1 2s0 2-1 4h1c4-1 9-7 13-9l43-23c0-1-4-6-4-8z" class="W"></path><defs><linearGradient id="u" x1="354.99" y1="505.878" x2="302.974" y2="495.41" xlink:href="#B"><stop offset="0" stop-color="#a09e9f"></stop><stop offset="1" stop-color="#cecccd"></stop></linearGradient></defs><path fill="url(#u)" d="M322 429c1 1 1 1 2 1h2 1 1v1c-1 1-1 2-2 2l-3 3h0c-2 1-3 3-5 5l-1 1h1c1 0 1-1 2-1h1c1 1 1 2 1 4l-6 9v1c-4 7-3 17-1 25 2 5 5 11 8 16h0c1 1 1 1 1 2l1 2c-1 6-1 12-1 17 1 9 3 19 7 27 4 6 11 11 17 15 2 2 5 3 7 4l-1 1c-2 0-4-1-6-1h0c-4-3-8-5-11-8l-6-6v-1c-6-8-11-18-13-28 0-1 1-3 0-4v-2l-1-5c-3-2-7-3-10-6v-1l-1-1h0-1c0-1 1-2 1-4 0-1 0-2-1-3v1c0 1 0 2-1 2 0 2 0 3-1 4v1c-2 4-5 9-9 11-1 1-2 1-2 1l1-2c10-10 10-23 10-36 0-5 0-10 1-15 2-12 9-23 18-32z"></path><path d="M317 509c0-2 0-4-1-6h-1c0-5-1-10-2-15 0-2-2-5-2-7 0 0 1 0 1-1 2 6 5 11 6 17v11 6l-1-5z" class="B"></path><path d="M317 442h1c1 0 1-1 2-1h1c1 1 1 2 1 4l-6 9v-1c0-2 0-3 1-5v-1c0 1 0 1 1 2v-1c1-1 1-1 1-3v-1h-1c0 1-1 2-2 3s-1-1-2 1l-2 5v1l-1 2v1c-1 2-1 4-2 6v1c0 1 0 3-1 5v4c-1 1 0 3 0 4s-1 2-2 2c0-14 3-26 11-37z" class="D"></path><path d="M311 478l1 2c0 1-1 1-1 1 0 2 2 5 2 7 1 5 2 10 2 15h1c1 2 1 4 1 6-3-2-7-3-10-6v-1l1-1v-6c1-5 0-12 3-17z" class="H"></path><path d="M308 469c1 1 1 2 2 4 0 2 0 3 1 5-3 5-2 12-3 17v6l-1 1-1-1h0-1c0-1 1-2 1-4 0-1 0-2-1-3v1c0 1 0 2-1 2 0 2 0 3-1 4v1c-2 4-5 9-9 11-1 1-2 1-2 1l1-2c10-10 10-23 10-36 0 1 1 2 1 3 1 1 1 5 2 5v-5h0c1 0 2-1 2-2s-1-3 0-4v-4z" class="E"></path><path d="M305 494c0-2 0-3 1-5l1-1c1 3 0 7 1 11v2l-1 1-1-1h0-1c0-1 1-2 1-4 0-1 0-2-1-3z" class="C"></path><defs><linearGradient id="v" x1="315.926" y1="456.623" x2="305.189" y2="455.022" xlink:href="#B"><stop offset="0" stop-color="#b9b7b8"></stop><stop offset="1" stop-color="#f8f7f8"></stop></linearGradient></defs><path fill="url(#v)" d="M322 429c1 1 1 1 2 1h2 1 1v1c-1 1-1 2-2 2l-3 3h0c-2 1-3 3-5 5l-1 1c-8 11-11 23-11 37h0v5c-1 0-1-4-2-5 0-1-1-2-1-3 0-5 0-10 1-15 2-12 9-23 18-32z"></path><defs><linearGradient id="w" x1="387.589" y1="515.193" x2="357.37" y2="540.699" xlink:href="#B"><stop offset="0" stop-color="#767475"></stop><stop offset="1" stop-color="#bab9ba"></stop></linearGradient></defs><path fill="url(#w)" d="M340 489v-3c1-3 1-9 3-11-1 8-1 16 1 23h1l1 1v-2h2l2 2 1-1v-1l5-2h0 1l1-1v2c2-1 2-1 4 0h1s1 0 2 1v1l-1 1v3c1-3 2-5 3-7l2-1h1c1 0 1 1 2 2v1l2 1h1c-1 2-3 4-4 6-3 5-3 11-3 16 1 3 1 6 2 9h-2c-1 1-1 2-1 3-1 4-1 8 1 12 2 1 4 3 6 4h1c0 2 4 5 6 7 0 1 1 2 2 3 2 1 3 4 5 6 4 4 9 5 12 9 0 1 1 2 1 3l-32-22c-18-15-27-39-30-61l1-4z"></path><path d="M367 495l2-1h1c1 0 1 1 2 2v1l2 1h-3c-1-1-1-1-3-1v1c0 1 0 1-1 2v-5z" class="U"></path><path d="M368 544h0l-2-2v-1c-1-2-1-4-1-6 1-1 1-2 1-3l-1-1v-7c1-2 1-4 3-5v1c1 3 1 6 2 9h-2c-1 1-1 2-1 3-1 4-1 8 1 12z" class="F"></path><path d="M367 495v5c-2 12-5 25-5 38l-2-1 1-1c0-11 2-23 3-34 1-3 2-5 3-7z"></path><path d="M345 498l1 1c2 10 6 18 10 27 2 3 3 7 5 10l-1 1v-1c-2-1-3-4-4-6-5-10-11-21-12-32h1z" class="K"></path><path d="M358 494v2c2-1 2-1 4 0h1s1 0 2 1v1l-1 1v3c-1 11-3 23-3 34-2-3-3-7-5-10-4-9-8-17-10-27v-2h2l2 2 1-1v-1l5-2h0 1l1-1z" class="H"></path><path d="M358 494v2c-1 1-3 4-5 4h-2l-1-1 1-1v-1l5-2h0 1l1-1z" class="Y"></path><defs><linearGradient id="x" x1="811.828" y1="236.262" x2="834.116" y2="257.829" xlink:href="#B"><stop offset="0" stop-color="#999798"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient></defs><path fill="url(#x)" d="M836 216c4 0 7-1 11-1 1 2 1 2 2 3h-1l-3 4c-2 3-5 5-8 7-9 8-16 21-18 33 0 6-1 11 1 17l-1-1c-1 0-2-2-3-2-1-1-2 0-3-1l-1 1h-2l-2 1h-1c-1 1-2 1-3 2l-5 2c0 1-6 2-7 2l2-1c3-2 7-3 10-5h1v-1c2-1 4-4 5-6h0c1-5 0-10-2-14v-2c0-4 0-8-3-11-2-2-9-4-12-3-1 0-1-1-2-1v-1l-5-11 1-1c2 1 2 1 3 3 0 2 0 3 1 4l1-1 23-10h4l2-2c1-1 3-1 4-2l11-2z"></path><path d="M836 216c4 0 7-1 11-1 1 2 1 2 2 3h-1l-3 4c-6 1-12 3-18 5l-13 5c-1 5 5 11 2 15v1c-1 0-2 0-3 1h-1v-1c0-2 1-4-1-6-3-6-13-4-18-6-1-1-1-2-2-3l1-1 23-10h4l2-2c1-1 3-1 4-2l11-2z" class="F"></path><path d="M813 231l1 1c-1 5 5 11 2 15h-2l-2-15 1-1z" class="S"></path><path d="M813 231h0c3-2 6-3 8-4 7-3 13-5 19-7 3-1 6-2 8-2l-3 4c-6 1-12 3-18 5l-13 5-1-1z" class="V"></path><path d="M701 159c5-10 16-10 26-14 2-1 5-3 7-4l-2 2-1 1h0c5-3 9-8 11-13h1 0l1 4c-2 4-5 8-6 12 2-1 3-3 5-3 1 1 2 3 5 3v3l-1 1c-2 1-5 0-7 0-1 0-1 1-2 2h1v1l-1 1-2-1c0 2-1 4-1 6l1 1-1 2c0 2-1 3-1 5-1 1-1 1-1 2v1s1-1 2-1 2 0 4-1v1c0 1 0 1-1 2v2l-2 2c0 1 0 1-1 2l-1 2 1 1h1l3 3-7 1c0 2-1 3-3 5v-1l-3-2-1 1h-1 0l-3-1c-1-1-2-2-4-2 0-1-1-2-2-2s-4-2-5-2c-4-3-9-7-11-11h1c-1-2-2-4-2-6 0-1 0-1 1-2h0c2-1 2-2 3-3h-1z" class="Q"></path><path d="M707 168c1 1 3 1 4 1v2h-3l-1-3z" class="G"></path><path d="M716 173c1 0 3-1 5 0v1l-1 1h-3c-1 0-1 0-2-1l1-1z" class="M"></path><path d="M723 155l1 1h-1c-1 0 0 0-1 1 1 0 1 0 2-1 2 0 3 1 4 2 1 2 1 2 1 3v1l-1 1c-1 1-1 2-3 2s-3 1-5-1v-1l-1-1h-1l1 2 1 1-1 1s-1-1-3-1c0-1-1-2-1-3 0-2 1-3 1-4h2 1c1-2 2-2 4-3z" class="G"></path><path d="M735 160l1 1-1 2c0 2-1 3-1 5-1 1-1 1-1 2v1s1-1 2-1 2 0 4-1v1c0 1 0 1-1 2v2l-2 2c0 1 0 1-1 2l-1 2 1 1h1l3 3-7 1c0 2-1 3-3 5v-1l-3-2-1 1h-1 0l-3-1c-1-1-2-2-4-2 0-1-1-2-2-2s-4-2-5-2c-4-3-9-7-11-11h1c-1-2-2-4-2-6 0-1 0-1 1-2l3 5c3 4 8 8 14 9 2 0 5 0 7-2v-1h2c3-1 7-3 8-6v-1c1-2 2-4 2-6z" class="B"></path><path d="M733 171s1-1 2-1 2 0 4-1v1c0 1 0 1-1 2-2 2-3 3-5 4-1 1-3 1-5 2l-2 1h0-1c-2 0-4 0-5-1 0-2 2-1 3-2l1 1 1-1c1 0 2 0 3-1 2-2 4-2 5-4z" class="P"></path><path d="M738 172v2l-2 2c0 1 0 1-1 2l-1 2 1 1h1l3 3-7 1-2-1-4-1-7-3c2 0 3 0 4 1 1 0 2 1 3 1l-1-1 1-2h0l2-1c2-1 4-1 5-2 2-1 3-2 5-4z" class="J"></path><path d="M728 178h3v1c-1 2-2 2-3 3-2-1-2-1-2-3h0l2-1z" class="S"></path><path d="M730 184h4c0-2-1-3 0-4l1 1h1l3 3-7 1-2-1z" class="N"></path><path d="M700 170c6 6 12 8 19 10l7 3 4 1 2 1c0 2-1 3-3 5v-1l-3-2-1 1h-1 0l-3-1c-1-1-2-2-4-2 0-1-1-2-2-2s-4-2-5-2c-4-3-9-7-11-11h1z" class="I"></path><path d="M715 183v-1c1 0 4 0 5 1v1c1 1 1 2 1 3-1-1-2-2-4-2 0-1-1-2-2-2z" class="E"></path><path d="M720 183c2 1 4 2 6 2v-2l4 1 2 1c0 2-1 3-3 5v-1l-3-2-1 1h-1 0l-3-1c0-1 0-2-1-3v-1z" class="L"></path><path d="M701 159c5-10 16-10 26-14 2-1 5-3 7-4l-2 2-1 1h0c5-3 9-8 11-13h1 0l1 4c-2 4-5 8-6 12v1c-1 1-1 1-1 2v1s-1 1-1 2v1l-1-1h-1v1c0 2-1 2-1 3v3l1 1-1 1h-1c-1-2-1-5-3-6l-1-1c-1-1-2-1-4-1l-1 1c-2 1-3 1-4 3h-1-2c-3 0-5 0-8-1l-1 2c0 1 2 1 3 2s1 6 1 8c-1 0-3 0-4-1l-2-2-1 1h-1-1l-3-5h0c2-1 2-2 3-3h-1z" class="M"></path><path d="M703 159l21-10h0c-5 3-11 5-16 8l-1 2c0 1 2 1 3 2s1 6 1 8c-1 0-3 0-4-1l-2-2-1 1h-1-1l-3-5h0c2-1 2-2 3-3h1z" class="D"></path><path d="M702 159h1c0 3 1 4 2 7l-1 1h-1-1l-3-5h0c2-1 2-2 3-3z" class="L"></path><defs><linearGradient id="y" x1="650.633" y1="89.258" x2="630.122" y2="171.679" xlink:href="#B"><stop offset="0" stop-color="#b1afb1"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#y)" d="M536 146l-1-2c1-1 2-1 3-1h97c21 0 42 1 63-1 9-1 19-2 27-6 5-3 10-7 12-13s1-11-2-16h0l1 1c2 1 3 2 4 4 4 6 5 16 4 23l-1-4h0-1c-2 5-6 10-11 13h0l1-1 2-2c-2 1-5 3-7 4-10 4-21 4-26 14h1c-1 1-1 2-3 3h0c-1 1-1 1-1 2 0 2 1 4 2 6h-1l-2-2h-1c-2 2-1 5-1 7l1 1v2 1l-3-5-1-4c-3 3-5 6-7 9h0v-1-1c-1 1 0 1-1 1v-2c1-1 2-3 3-4 0-1 0-2-1-3v-1-1l1-1 2-3c1-2 1-2 1-4 1-2 3-4 2-7v-1c-7-1-15 0-22 0h-39-65-16c-2 0-5 0-7 1-2-1-3-2-3-3-2-2-2-3-4-3z"></path><path d="M685 177c3-7 9-14 16-18h1c-1 1-1 2-3 3h0c-1 1-1 1-1 2 0 2 1 4 2 6h-1l-2-2h-1c-2 2-1 5-1 7l1 1v2 1l-3-5-1-4c-3 3-5 6-7 9h0v-1-1z" class="G"></path><path d="M154 137l20 11c11 4 21 6 33 6 5 0 11-1 16 0-2 0-3 1-5 1l2 2c1 1 1 1 1 2v1 3l3 1h-1l2 2c1 1 1 1 2 1 2 1 3 2 4 3h1c1 2 2 3 2 5 1 2 0 3-1 5v1l-2 3c0 1 0 2-1 3 0 1-1 2-1 3 0 2 4 8 2 9l-22-11-26-13c-11-7-22-15-31-25 2 0 2 2 4 3 0 1 0 1 1 1l1-1h1c-2-1-4-2-5-4 0-1-1-2-2-3s-1-2-2-4h0l-1-2h1c1 1 2 0 3 0 1 1 3 2 4 2l1 1h0c1 0 1 1 2 0h-1l1-1h1l-2-2c-1 0-2-1-3-1s-1-1-2-2z" class="O"></path><path d="M187 162l-4-2 1-1c1 0 3 0 5 1l5 2h1c0 1 1 1 2 1l1 1h0-2-1-3l-3-1-2-1z" class="T"></path><path d="M179 161v-1l-2-1h1c3 1 5 3 9 3l2 1 3 3c2 1 4 2 7 3l-1 1c-1 0-1 0-2-1h-2c-1-1-2-1-4-2v2h-2c-1-1-1-1-2-1l-8-3-2-2-5-3v-1c1 1 2 1 2 1 1 1 1 1 3 1v-1-1l3 2z" class="E"></path><path d="M179 161v-1l-2-1h1c3 1 5 3 9 3l2 1 3 3-8-3c-2 0-4 0-5-2zm-25-24l20 11c0 1 0 1 1 2 1 0 1 0 2 1h-1c-1 0-4 0-5-1l-3-1-2-1v1 1h0c-1 0-1 0-2-1-2-1-4-3-6-4h-1l-2-2c-1 0-1 0-2-1s-2 0-3 0l-1-2h1c1 1 2 0 3 0 1 1 3 2 4 2l1 1h0c1 0 1 1 2 0h-1l1-1h1l-2-2c-1 0-2-1-3-1s-1-1-2-2z" class="L"></path><path d="M216 166l1-1 3 1v1c2 0 4 2 4 4v4c-1 1-1 1-1 2l-1 1h0c-1 2-2 3-3 4 0 1 0 1 1 2h0c-2 0-3-1-5-2h-1-1c-4-2-9-4-12-7l-1-1c-2 0-2 0-3-1 0 0-1-1-2-1l-5-3v-2c2 1 3 1 4 2h2c1 1 1 1 2 1l1-1v1c3 0 10 4 12 5s4 1 6 2c1 0 1 1 2 1 1-3 2-6 2-9l-2-2-3-1z" class="D"></path><path d="M190 169v-2c2 1 3 1 4 2h2c1 1 1 1 2 1l1-1v1l1 1h0c3 1 13 6 15 9h-1c-1-1-2-1-3-1l-1-1-10-4v-1h-2-1s-1-1-2-1l-5-3z" class="G"></path><path d="M190 158c2 0 3 0 4 1h1c3 1 7 1 10 3l8 2c1 1 2 1 3 2l3 1 2 2c0 3-1 6-2 9-1 0-1-1-2-1-2-1-4-1-6-2s-9-5-12-5v-1c-3-1-5-2-7-3l-3-3 3 1h3 1 2 0l-1-1c-1 0-2 0-2-1h-1c-1-1-1-2-2-2s-1 0-2-1c-1 0 0 0-1-1h1z" class="Q"></path><path d="M190 158l-5-2h0 2l3 1h2c1 0 1 0 2 1h4l1 1h1c1 0 2 0 3-1l20 6 2 2c1 1 1 1 2 1 2 1 3 2 4 3h1c1 2 2 3 2 5 1 2 0 3-1 5v1l-2 3c0 1 0 2-1 3 0 1-1 2-1 3 0-4 0-6 1-9h-1c-1 1-2 2-2 3-1 1 0 2-2 3-1 0-2 0-2-1h-1c0-1-1-1-1-2h-1 0c-1-1-1-1-1-2 1-1 2-2 3-4h0l1-1c0-1 0-1 1-2v-4c0-2-2-4-4-4v-1l-3-1-1 1c-1-1-2-1-3-2l-8-2c-3-2-7-2-10-3h-1c-1-1-2-1-4-1z" class="I"></path><path d="M221 184l2 1 3-3v-1c1-1 2-2 2-3 1-1 1-1 2-1l1 1c0 2 0 2-1 3h-1c-1 1-2 2-2 3-1 1 0 2-2 3-1 0-2 0-2-1h-1c0-1-1-1-1-2h0z" class="E"></path><path d="M213 164c0-1-1-1-1-2 1 0 1 0 3 1 1 0 2 0 3 1 4 1 8 4 9 8v4c-2 3-4 5-6 8h0-1 0c-1-1-1-1-1-2 1-1 2-2 3-4h0l1-1c0-1 0-1 1-2v-4c0-2-2-4-4-4v-1l-3-1-1 1c-1-1-2-1-3-2z" class="C"></path><defs><linearGradient id="z" x1="236.789" y1="219.492" x2="258.191" y2="170.097" xlink:href="#B"><stop offset="0" stop-color="#aba9aa"></stop><stop offset="1" stop-color="#d3d2d3"></stop></linearGradient></defs><path fill="url(#z)" d="M223 154l2 1 14 7c8 4 18 8 25 14l19 20-1 1h1c1 0 3-1 4-2l1-1 1 1c-1 1-1 0-1 1v1 2l-1 1v1c2 0 2 0 4 1l-1 1h0c0 1 0 2 1 3l-1 1v2c-1 1-1 1-2 1l-8 5c-4 0-8 1-11 2-1 1-3 2-4 3-1 0-2 1-3 1-1 1-1 1-2 1h-1c-4 1 1 0-2 1h-2l-1 1h-2c-1 1-1 1-2 1 1-2 5-3 7-4v-1h0c1-1 2-1 3-2-2-2-4-2-7-3-1-1-3-2-4-3 6 1 10 1 17-1h-13c-7-2-16-7-22-12 2-1-2-7-2-9 0-1 1-2 1-3 1-1 1-2 1-3l2-3v-1c1-2 2-3 1-5 0-2-1-3-2-5h-1c-1-1-2-2-4-3-1 0-1 0-2-1l-2-2h1l-3-1v-3-1c0-1 0-1-1-2l-2-2c2 0 3-1 5-1z"></path><path d="M288 197v2l-1 1v1c2 0 2 0 4 1l-1 1-4 1c-1 1-3 0-5 0h0c-3 1-7 0-9 1-1 0-1 0-2-1l4-1 6-1c1-1 3-1 4-2h0c2-1 3-2 4-3z" class="D"></path><path d="M283 197c1 0 3-1 4-2l1-1 1 1c-1 1-1 0-1 1v1c-1 1-2 2-4 3h0c-1 1-3 1-4 2l-6 1-4 1h-2l-1 1c-3 0-9 0-12-1l-1-1h10c7-1 12-4 18-6h1z" class="O"></path><defs><linearGradient id="AA" x1="266.545" y1="204.915" x2="248.899" y2="181.223" xlink:href="#B"><stop offset="0" stop-color="#8c8b8c"></stop><stop offset="1" stop-color="#b4b2b3"></stop></linearGradient></defs><path fill="url(#AA)" d="M264 176l19 20-1 1h1-1c-6 2-11 5-18 6h-10c0-1-3-1-4-1-5-2-10-6-13-10 0-2 0-3 1-5 0-2 1-3 2-4 5-5 10-5 17-5h5c1 0 1-1 2-2z"></path><path d="M257 190h0l1 1c1 1 1 2 0 3l-2 1-2-2c1-2 1-2 3-3z" class="V"></path><path d="M251 184h2l-1 1c2 1 1 0 3 0h-1c-1 1-4 4-6 3h1 0v-1l-2 1h-1l-1 1h0-1s-1-1-1-2c1-1 2-1 4-2l-1 1h2c1 0 2-1 3-2z" class="E"></path><path d="M238 187c1 1 2 1 3 2 0 3 0 5 2 8 6 5 14 6 21 5v1h-10c0-1-3-1-4-1-5-2-10-6-13-10 0-2 0-3 1-5z" class="K"></path><path d="M238 187c0-2 1-3 2-4 5-5 10-5 17-5-3 1-5 1-8 3-2 1-5 2-6 4 0 1-1 3-2 4-1-1-2-1-3-2z" class="R"></path><path d="M127 102l-1-2h1l5 10c0-2 0-3-1-5h0c0-1 0-1 1-1l1 1c0 1 1 2 2 2 14 18 31 35 55 38 11 1 23 0 34-2 4-1 7-3 10-4l-6 7c5 4 11 6 16 9l24 13v-2l3 2 1-1 2-1h0 0l-3-3v-1c1-1 2-1 3-1s2 0 3-1 3-2 5-3c1-1 1-2 2-3 1 0 2 0 3 1h2l3 2c1 1 1 2 1 3v1c2 6 3 8 9 11 2 2 6 4 9 4 4-1 7-3 10-5l3-3 1-1c1-2 2-3 3-5h1c0 3-1 5-2 7l-4 4c-2 1-4 2-4 3-1 2-3 4-4 5-1 0-1 0-2 1-3 1-9 3-12 6l-1-1-2 2s0 1 1 1c2 2 5 4 8 6l-3-1h-1v1l-1-1c-1-1-2-1-4-1h0-1 0-1l1 2h0c-1-1-1-2-3-3l-1 1 1 1v1c-1-1-2-2-3-2-1-1-3-1-4-1-1 2-3 3-5 4l1-1-19-20c-7-6-17-10-25-14l-14-7-2-1c-5-1-11 0-16 0-12 0-22-2-33-6l-20-11c-9-7-17-16-22-26-2-3-4-6-5-9z" class="O"></path><path d="M127 102l-1-2h1l5 10c5 8 10 15 17 21 10 9 23 14 35 18 5 1 10 2 15 2 5 1 11 0 17 0 2 0 6 0 8 1v1h0c1 0 1 0 2 1l-1 1-2-1c-5-1-11 0-16 0-12 0-22-2-33-6l-20-11c-9-7-17-16-22-26-2-3-4-6-5-9z" class="J"></path><defs><linearGradient id="AB" x1="244.687" y1="177.416" x2="262.038" y2="164.024" xlink:href="#B"><stop offset="0" stop-color="#000203"></stop><stop offset="1" stop-color="#2a2827"></stop></linearGradient></defs><path fill="url(#AB)" d="M224 152c5 2 10 5 15 7 11 5 22 10 32 18l9 9c2 1 3 3 5 4l2 3c-1 2-3 3-5 4l1-1-19-20c-7-6-17-10-25-14l-14-7 1-1c-1-1-1-1-2-1h0v-1z"></path><path d="M283 196l1-1v-2c-1-1-1-1-1-2l2-1 2 3c-1 2-3 3-5 4l1-1z" class="J"></path><path d="M284 154c1 0 2 0 3 1h2l3 2c1 1 1 2 1 3v1c2 6 3 8 9 11 2 2 6 4 9 4 4-1 7-3 10-5l3-3 1-1c1-2 2-3 3-5h1c0 3-1 5-2 7l-4 4c-2 1-4 2-4 3l-14 6c-2 1-5 2-8 2-2 1-7 1-9 0-2 0-4-2-5-3l-8-8c-2-2-4-4-7-5v-2l3 2 1-1 2-1h0 0l-3-3v-1c1-1 2-1 3-1s2 0 3-1 3-2 5-3c1-1 1-2 2-3z" class="B"></path><path d="M291 172l-1-1c1-1 1-2 2-3l2 2c2 4 8 7 12 8l1 1h-2c-1 0-2-1-4-1h-1c-2 0-2 0-4 2l-6-7 1-1z" class="D"></path><path d="M291 172l9 6c-2 0-2 0-4 2l-6-7 1-1z" class="J"></path><path d="M284 154c1 0 2 0 3 1h2l3 2c1 1 1 2 1 3v1 4c1 1 1 1 1 2-2 0-3 1-4 1h-1l1-1v-1l-1 1h-1c1-2 0-3 0-4l1-1c-1-1-2-2-4-2-3-1-4-1-7 1h-4c1 0 2 0 3-1s3-2 5-3c1-1 1-2 2-3z" class="E"></path><path d="M278 161c-1 1-2 2-2 4 1 1 2 2 3 2l1-1h0-1c-1-1-1-1-1-2 1-2 1-2 3-3s3 0 5 0v1c-1 1-2 2-2 4 1 0 1 0 2-1 0-1 0-1 1-1 0 2 0 3-1 4v1c-1 1-2 1-3 1h-1c-2 1-4-1-7 1l-4-3 1-1 2-1h0 0l-3-3v-1c1-1 2-1 3-1h4z" class="L"></path><defs><linearGradient id="AC" x1="296.09" y1="185.173" x2="283.37" y2="176.083" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#444443"></stop></linearGradient></defs><path fill="url(#AC)" d="M268 166l3 2 4 3c3-2 5 0 7-1h1c1 0 2 0 3-1v-1l1 4h0c1 0 2 1 3 1l6 7c2-2 2-2 4-2h1c2 0 3 1 4 1v3c-2 1-5 2-8 2-2 1-7 1-9 0-2 0-4-2-5-3l-8-8c-2-2-4-4-7-5v-2z"></path><path d="M300 178h1l1 1-1 1-1 2c-1 0-1 0-2-1h0l-2-1c2-2 2-2 4-2z" class="V"></path><path d="M275 171c3-2 5 0 7-1h1c1 0 2 0 3-1 0 2 1 4 0 6 0 2 1 3 1 5-1-1-3-2-4-3l-4-3-4-3z" class="F"></path><path d="M279 174c1-1 3-1 4 0s1 2 0 3l-4-3z" class="L"></path><defs><linearGradient id="AD" x1="294.882" y1="176.863" x2="287.081" y2="177.157" xlink:href="#B"><stop offset="0" stop-color="#626061"></stop><stop offset="1" stop-color="#7c7b7c"></stop></linearGradient></defs><path fill="url(#AD)" d="M286 169v-1l1 4h0c1 0 2 1 3 1l6 7 2 1-1 1c-2-2-4 0-6-1-2 0-3 0-4-1 0-2-1-3-1-5 1-2 0-4 0-6z"></path><defs><linearGradient id="AE" x1="770.608" y1="151.113" x2="787.726" y2="190.226" xlink:href="#B"><stop offset="0" stop-color="#d8d7d8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AE)" d="M902 101c1 1 1 1 2 1l-1 2c-2 1-4 6-5 8-8 12-17 22-30 29-3 1-6 3-9 4-2 1-4 2-5 3-11 3-23 7-35 6v-1h-4-5v-1h6l1 1c1 0 2 0 2-1l-2-1h-9-3c-6 3-11 6-16 9l-17 8c-3 1-5 2-7 4h-1v1c-1 1-2 1-3 2 1 0 1 1 1 0 1-1 2-1 3-1h0l-1 1c1 2 2 3 4 4 6-1 11-2 16 2 1 1 2 2 2 3h0c1 1 2 1 2 2v6l-1 3c-2 2-4 4-6 5l-6 3c-4 2-9 2-13 2-6-1-18-4-22-8v-1h-1c1 2 2 3 3 4-1 1-2 1-3 1h0l1 1v1c0 2 1 3 1 5h0l-1 2c-1 1-2 1-4 1l-1 1-6-1c-5-2-12-2-16-2s-7 2-10 2c-2 1-5 1-7 2-5 2-8 6-13 7s-12 5-15 9c-2 2-3 3-3 6l1 1v1l-1 1c1 1 2 2 2 4l-1 1h0c-1-2-3-6-3-9l-1-1 1-1 1-1v-3l12-9c4-2 8-3 12-5s8-5 12-7c3-2 7-3 11-5l9-6 9-6h0c2-2 3-3 3-5l7-1c4-2 7-6 11-9 2-2 5-4 8-6s7-4 11-6c10-5 21-10 30-16-2-3-3-5-5-8h2c4 3 9 4 14 5 8 1 17 2 25 1h1c7-1 14-3 21-5 8-4 16-10 23-17 4-3 7-8 11-12h1c1-1 2-1 3-3 1 0 1 1 3 1l-2 3-1 1v2c3-4 5-9 7-14z"></path><path d="M836 145c7-1 14-3 21-5-2 3-5 3-8 5h-1c-4 1-6 2-11 1h0l-1-1z" class="Q"></path><path d="M902 101c1 1 1 1 2 1l-1 2c-2 1-4 6-5 8-8 12-17 22-30 29-3 1-6 3-9 4-2 1-4 2-5 3-11 3-23 7-35 6v-1h-4-5v-1h6l1 1c1 0 2 0 2-1l-2-1c7 1 14 0 21-1 23-5 43-15 57-35 3-4 5-9 7-14z" class="K"></path><path d="M730 199h0l4-3v-1l2-2v-1l2-2v-1c1-2 1-2 3-2v1c-1 1-1 3-1 4v1l1-2c0-1 1-2 2-3 1 0 1 0 2-1h-2c1-2 4-3 7-3-3 2-8 6-9 9 1 2 2 3 4 4 8 5 19 7 28 6 1 0 1-1 2 0-4 2-9 2-13 2-6-1-18-4-22-8v-1h-1c1 2 2 3 3 4-1 1-2 1-3 1h0l1 1v1c0 2 1 3 1 5h0l-1 2c-1 1-2 1-4 1l-1 1-6-1c-5-2-12-2-16-2 5-3 12-5 17-10z" class="O"></path><path d="M736 202v-2c0-1-1-1-2-3h2l1 1c0 1 1 2 1 3h1l1 1v1c0 2 1 3 1 5h0l-1 2c-1 1-2 1-4 1l-1 1-6-1c1 0 1 0 2-1h4c2-1 3-1 5-2-2-2-3-4-4-6z" class="R"></path><path d="M713 209c5-3 12-5 17-10l-1 3h2c1 0 2-1 4-2l1 2c1 2 2 4 4 6-2 1-3 1-5 2h-4c-1 1-1 1-2 1-5-2-12-2-16-2z" class="K"></path><path d="M713 209c5-3 12-5 17-10l-1 3h2l2 2-1 1-2-1h-1v1h0c-2 0-2 0-2-1l-7 4h7c3 1 5 1 8 2h-4c-1 1-1 1-2 1-5-2-12-2-16-2z" class="W"></path><defs><linearGradient id="AF" x1="768.672" y1="201.881" x2="766.466" y2="180.112" xlink:href="#B"><stop offset="0" stop-color="#9c9a9b"></stop><stop offset="1" stop-color="#bebcbd"></stop></linearGradient></defs><path fill="url(#AF)" d="M750 184c4-5 8-9 14-12v1c-1 1-2 1-3 2 1 0 1 1 1 0 1-1 2-1 3-1h0l-1 1c1 2 2 3 4 4 6-1 11-2 16 2 1 1 2 2 2 3h0c1 1 2 1 2 2v6l-1 3c-2 2-4 4-6 5l-6 3c-1-1-1 0-2 0-9 1-20-1-28-6-2-1-3-2-4-4 1-3 6-7 9-9z"></path><path d="M769 193h-1-1l-1-1c0-1 0-1 1-2l2 2v1z" class="R"></path><path d="M769 192l1-2h2c1 1 2 2 2 3v1c-1 1-2 1-3 1-1-1-2-1-2-2v-1z" class="K"></path><path d="M782 184l6 8-1 3-2-1c-2-3-2-5-3-8v-2z" class="H"></path><path d="M783 181l3 3h0c1 1 2 1 2 2v6l-6-8v-1l-1-1c1 0 0 0 1-1h1z" class="F"></path><path d="M785 194l2 1c-2 2-4 4-6 5h-5v-1c3-2 6-3 9-5z" class="I"></path><path d="M750 184c4-5 8-9 14-12v1c-1 1-2 1-3 2 1 0 1 1 1 0 1-1 2-1 3-1h0l-1 1c1 2 2 3 4 4 6-1 11-2 16 2 1 1 2 2 2 3h0 0l-3-3c-3-2-5-3-9-2-1 0-1 0-2 1h0c-4 2-5 5-8 7-1 0-1 1-2 1-1 1 0 1-1 1-1 1-1 0-1 1-1 1-2 2-4 2h0c-2 1-5 0-7 0l-4 3v2c-2-1-3-2-4-4 1-3 6-7 9-9z" class="D"></path><path d="M750 184c4-5 8-9 14-12v1c-1 1-2 1-3 2 1 0 1 1 1 0 1-1 2-1 3-1h0l-1 1c-3 2-4 4-6 6l-9 11-4 3v2c-2-1-3-2-4-4 1-3 6-7 9-9z"></path><defs><linearGradient id="AG" x1="750.972" y1="218.181" x2="857.697" y2="133.532" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#dddcdd"></stop></linearGradient></defs><path fill="url(#AG)" d="M868 141l1 1c2-1 4-2 6-4h2-1v2l2 2c1-1 2-3 4-4l-4 6c-3 4-6 7-10 10 1 0 2 1 3 2-8 7-16 12-26 18h4c-6 4-13 7-20 10-14 7-31 15-43 26-1 2-2 3-4 4h0c1 0 1 1 2 1-1 2-3 3-4 5l1 1v1c0 1 1 2 3 3h1s1 1 2 1l-1 1 5 11h-2l-3-6-5-7-1 1-6-3c-3 0-6-2-8-3-1-1-2-1-3-1l-11-4c-1 0-1 0-2-1l-2-1h-1c-2-2-4-4-6-5 0-2-1-3-1-5v-1l-1-1h0c1 0 2 0 3-1-1-1-2-2-3-4h1v1c4 4 16 7 22 8 4 0 9 0 13-2l6-3c2-1 4-3 6-5l1-3v-6c0-1-1-1-2-2h0c0-1-1-2-2-3-5-4-10-3-16-2-2-1-3-2-4-4l1-1h0c-1 0-2 0-3 1 0 1 0 0-1 0 1-1 2-1 3-2v-1h1c2-2 4-3 7-4l17-8c5-3 10-6 16-9h3 9l2 1c0 1-1 1-2 1l-1-1h-6v1h5 4v1c12 1 24-3 35-6 1-1 3-2 5-3 3-1 6-3 9-4z"></path><path d="M742 200l6 2-1 1c-1 0-2 1-2 1-2 1-4-1-5-1v-1l-1-1h0c1 0 2 0 3-1z" class="D"></path><path d="M767 216c4 0 7 2 11 3 1 0 1 0 2 1l1 1v1c0 1 1 2 3 3h1s1 1 2 1l-1 1 5 11h-2l-3-6-5-7c-4-5-9-6-14-9z" class="B"></path><path d="M788 186c2 2 3 3 3 6-2 4-5 7-10 9-3 2-6 3-10 4-8 2-15 0-23-3l-6-2c-1-1-2-2-3-4h1v1c4 4 16 7 22 8 4 0 9 0 13-2l6-3c2-1 4-3 6-5l1-3v-6z" class="J"></path><path d="M868 141l1 1c2-1 4-2 6-4h2-1v2l2 2-8 9-2-2c-3 0-4 2-6 3l-6 3h-1v-1c1-1 3-2 5-2 1-1 2-1 3-2h-1l-8 3-4 1c2-3 7-3 10-5 1 0 0 0 1-1h-3 2c0-1 1-1 1-1h1c1-1 2-2 4-2v-1s0-1-1 0h-2c-2 1-6 4-8 4h-1c1-1 3-2 5-3 3-1 6-3 9-4z" class="F"></path><path d="M764 172h1c2-2 4-3 7-4l17-8c5-3 10-6 16-9h3 9l2 1c0 1-1 1-2 1l-1-1h-6v1h5 4v1h-1c-3 0-9-1-12-1-4 2-9 6-13 8-6 3-13 6-20 9-1 1-6 4-8 4h0c-1 0-2 0-3 1 0 1 0 0-1 0 1-1 2-1 3-2v-1z" class="S"></path><path d="M878 142c1-1 2-3 4-4l-4 6c-3 4-6 7-10 10-8 4-14 10-22 13h-4c-4 1-8 4-11 6-7 4-15 10-23 11-4-2-7-4-8-8 0-2 0-4 1-6h1c-1 2-2 4 0 7 1 2 3 4 6 5 2 1 3 0 4-1l25-13 2-1v-1h2v-1h-1l2-1c1 0 1 0 2-1 2-2 5-3 7-4h1l-1-1c-1 0-1 0-2 1h0l-1-1c2-1 4-3 7-4v1h1l6-3c2-1 3-3 6-3l2 2 8-9z" class="N"></path><path d="M851 159l2 2-16 7 2-1v-1h2v-1h-1l2-1c1 0 1 0 2-1 2-2 5-3 7-4z" class="L"></path><path d="M855 154v1h1l6-3c2-1 3-3 6-3l2 2c-3 2-6 4-10 6l-7 4-2-2h1l-1-1c-1 0-1 0-2 1h0l-1-1c2-1 4-3 7-4z" class="G"></path><path d="M852 159l5-3c1 0 2 0 3 1l-7 4-2-2h1z" class="C"></path><path d="M767 212c4 0 9-1 13-2 9-4 18-11 27-16l38-20h4c-6 4-13 7-20 10-14 7-31 15-43 26-1 2-2 3-4 4h0c1 0 1 1 2 1-1 2-3 3-4 5-1-1-1-1-2-1-4-1-7-3-11-3l-1-1v-2c-1-1-2-1-3-1 2 0 3-1 4 0z" class="K"></path><path d="M767 212c1 1 4 2 6 1h4v1h-2v1h2c3-1 6-4 9-5-1 2-2 3-4 4h0c1 0 1 1 2 1-1 2-3 3-4 5-1-1-1-1-2-1-4-1-7-3-11-3l-1-1v-2c-1-1-2-1-3-1 2 0 3-1 4 0z" class="D"></path><path d="M850 154l4-1 8-3h1c-1 1-2 1-3 2-2 0-4 1-5 2-3 1-5 3-7 4l1 1h0c1-1 1-1 2-1l1 1h-1c-2 1-5 2-7 4-1 1-1 1-2 1l-2 1h1v1h-2v1l-2 1-25 13c-1 1-2 2-4 1-3-1-5-3-6-5-2-3-1-5 0-7v-1l3-3v-1c1-1 3-2 5-3h1 1c1 0 1 0 2-1 2 0 3 0 4-1l3-1c1 1 1 2 3 2l4-1 4-2c3 0 5 0 7-1l2-1h2l2-1h2c2 0 2-1 3-1z" class="O"></path><path d="M811 166c-2 1-4 2-5 4h1c2 0 5-1 7-2l16-6c2-1 5-2 7-2h0c-6 3-13 5-20 8-3 1-5 2-7 4-1 2-1 4-1 6h-2c-1-2-2-3-3-4h0v-1c0-1 0-3 1-4 1-2 4-3 5-4l1 1z" class="F"></path><path d="M802 170v-1l3-3v-1c1-1 3-2 5-3h1 1c1 0 1 0 2-1 2 0 3 0 4-1l3-1c1 1 1 2 3 2-2 1-4 1-6 2h-1l-1 1-4 1-1 1-1-1c-1 1-4 2-5 4-1 1-1 3-1 4v1h0c1 1 2 2 3 4 1 1 2 2 4 3h1c-1 1-2 2-4 1-3-1-5-3-6-5-2-3-1-5 0-7z" class="L"></path><defs><linearGradient id="AH" x1="827.985" y1="177.456" x2="826.486" y2="166.837" xlink:href="#B"><stop offset="0" stop-color="#a8a7ae"></stop><stop offset="1" stop-color="#dad8d6"></stop></linearGradient></defs><path fill="url(#AH)" d="M809 178s1 0 2 1c1-1 3-2 5-3 3-2 8-4 11-7 3-1 6-3 9-4h4 1v1h-2v1l-2 1-25 13h-1c-2-1-3-2-4-3h2z"></path><defs><linearGradient id="AI" x1="370.649" y1="207.332" x2="390.46" y2="121.034" xlink:href="#B"><stop offset="0" stop-color="#aaa8a9"></stop><stop offset="1" stop-color="#d2d1d2"></stop></linearGradient></defs><path fill="url(#AI)" d="M285 136c-1-1-1-1-1-2 1-2 0-5 0-7 0-9 2-14 8-20-2 6-4 10-1 17 3 6 8 10 14 13 18 7 39 6 58 6h39 90l-4 8h-2-2c-3-1-7 0-11 0h-21-85-20c-4 0-7-1-10 0-1 0-1 0-1 1-1 1-1 2-1 4 0 1 1 2 1 3s1 2 1 4c1 2 3 4 4 6 5 6 8 12 13 17 3 4 6 7 5 12v1c0 2-1 3-2 4v1h0l-1 1h0c-2 1-3 1-5 1h0c-3 1-11 1-14-1-2-1 0 0-1 0l-4-3-1-1c-2-1-2-1-3-2s-3-1-4-2-2-1-3-1h0l1 1c-3 0-4-1-7-1h0-2c-1 0-1-1-2-1l-1 1c-4-1-8-6-11-6-1 0-1-1-1-1l2-2 1 1c3-3 9-5 12-6 1-1 1-1 2-1 1-1 3-3 4-5 0-1 2-2 4-3l4-4c1-2 2-4 2-7h-1c-1 2-2 3-3 5l-1 1-3 3c-3 2-6 4-10 5-3 0-7-2-9-4-6-3-7-5-9-11v-1c0-1 0-2-1-3v-3h0c0-2-1-3-1-4l-6-14z"></path><path d="M307 147c7 1 12 2 17 7 1 1 2 1 3 2s2 1 3 2 2 3 2 4c2 2 3 4 4 7v1h-4v-1h-1-1-3c1-2 2-4 2-7h-1c-1 2-2 3-3 5v-2l-1-2c-1-5-6-7-9-10 0 0-9-3-10-4v-1l2-1z" class="C"></path><path d="M307 147c7 1 12 2 17 7l5 6-1 1h0l-2 1c0-1-1-1-1-2-2-2-4-4-6-5-1-2-2-2-4-2 0 0-9-3-10-4v-1l2-1z" class="T"></path><path d="M327 169h3 1v1c0 1-1 2-2 3l-1 1h0c-1 1-1 1-1 2v1c-1 3-7 7-10 9 0 0-1 0-1 1h-1c0 1-1 2-2 2l6-3c-1 2 0 1-1 2 1 1 1 1 1 2v3h1l1 2v1h0l1 1c-3 0-4-1-7-1h0-2c-1 0-1-1-2-1l-1 1c-4-1-8-6-11-6-1 0-1-1-1-1l2-2 1 1c3-3 9-5 12-6 1-1 1-1 2-1 1-1 3-3 4-5 0-1 2-2 4-3l4-4z" class="H"></path><path d="M327 169h3 1v1c-2 1-4 4-6 5-1 1-2 2-3 2v1c-2 1-3 1-4 2s-1 2-2 3h-1 0c-1 1-2 1-3 1l1-2c1-1 1-1 2-1 1-1 3-3 4-5 0-1 2-2 4-3l4-4z" class="D"></path><path d="M299 190c-1 0-1-1-1-1l2-2 1 1c3-3 9-5 12-6l-1 2c-1 1-1 1-2 1-2 1-3 1-4 3h0c1 1 2 1 3 1v1c1 0 2 1 3 1 3 1 6 3 9 4v1h0l1 1c-3 0-4-1-7-1h0-2c-1 0-1-1-2-1l-1 1c-4-1-8-6-11-6z" class="I"></path><path d="M328 190h-1v-1c0-2 4-8 5-10l1-1c1-1 1-2 2-2 1-2 1-3 3-5l1 1c3 2 4 6 7 8 2 2 3 4 5 6h3c3 4 6 7 5 12v1c0 2-1 3-2 4v1h0l-1 1h0c-2 1-3 1-5 1h0c-3 1-11 1-14-1-2-1 0 0-1 0l-4-3-1-1c-2-1-2-1-3-2h5c2 1 2 1 4 1 0 0 1 0 1 1h4c-4-1-8-3-11-5-2-2-3-3-3-6z" class="C"></path><path d="M328 190l1-1c0 3 0 4 3 5 3 3 8 5 12 4 3 0 5-2 7-3l1 2c0 1 0 1-1 2h-1-2l-2 2h-4c-4-1-8-3-11-5-2-2-3-3-3-6z" class="B"></path><path d="M351 186h3c3 4 6 7 5 12v1c0 2-1 3-2 4v1h0l-1 1h0c-2 1-3 1-5 1h0c-3 1-11 1-14-1-2-1 0 0-1 0l-4-3-1-1c-2-1-2-1-3-2h5c2 1 2 1 4 1 0 0 1 0 1 1h4 4l2-2h2 1c1-1 1-1 1-2l-1-2c1 0 2 0 3 1 0-1 0-1-1-2h-2c-1 1-2 1-3 2h0c-2 0-3 1-5 0h0c-2-1-3-1-4-2v-1l1 1h2c2 1 5 0 7 0l-1-1c0-2 1-1 2-2h-4v-1c0-2 1-2 2-3 2 0 2 0 4 1l-1 1h2 0l-2-3z" class="I"></path><path d="M351 195c1 0 2 0 3 1s1 1 1 3c-3 2-6 2-9 2l2-2h2 1c1-1 1-1 1-2l-1-2z" class="W"></path><path d="M285 136l1-1c4 5 8 8 14 11 2 1 5 0 7 1l-2 1v1c1 1 10 4 10 4 3 3 8 5 9 10l1 2v2l-1 1-3 3c-3 2-6 4-10 5-3 0-7-2-9-4-6-3-7-5-9-11v-1c0-1 0-2-1-3v-3h0c0-2-1-3-1-4l-6-14z" class="Q"></path><path d="M306 169c1 0 4 1 6 2 3 0 5-2 8-1h-1c-1 1-2 1-3 1-1 2-2 2-3 3-3 0-4-1-6-2 0-2-1-2-2-3h0 1z" class="T"></path><path d="M305 153c-1-1-2-3-2-4h2c1 1 10 4 10 4 3 3 8 5 9 10l1 2v2l-1 1-3 3-1-1c-3-1-5 1-8 1-2-1-5-2-6-2l-1-1-3-2-2-2c-1-2-1-4-1-6 0-1 1-1 2-2h4l1-1-1-2z" class="G"></path><path d="M301 156c3 0 6 0 8 2 1 1 1 2 1 3 0 2-1 4-2 5h-1l-2 2-3-2-2-2c-1-2-1-4-1-6 0-1 1-1 2-2z" class="U"></path><path d="M302 166c0-1 0-2-1-3h3c1 1 1 0 2 1l2-2 1 1c0 1-1 2-2 3h0l-2 2-3-2z" class="Y"></path><path d="M305 153c-1-1-2-3-2-4h2c1 1 10 4 10 4 3 3 8 5 9 10l1 2v2l-1 1c0-1-1-1-1-1l-2 1c-1 0-2-1-3-2v-3h-1l1-1h0c1 1 2 2 4 2v-1c0-3-2-4-4-6l-1 2h-5c-1-1-2-2-2-3h-1c-1-1-3-2-4-4v1z" class="H"></path><path d="M305 152l-1-2c2 0 5 2 7 2l-1 4h-1c-1-1-3-2-4-4z" class="T"></path><path d="M311 152c3 2 5 3 7 5l-1 2h-5c-1-1-2-2-2-3l1-4z" class="Q"></path><path d="M400 531v-1c-1-7-4-15-6-23-2-5-3-11-6-16l-4-14c0-1-1-3-1-5-1 0-1-1-1-2h1c1 1 1 2 3 2 1 0 2 1 3 1 0 1 0 1 1 2 0 1 0 1 1 2l-1 1h1c0 2 1 3 1 4s0 1 1 2h0v2c1 1 1 2 1 4l1 1v1c1 2 2 5 2 8 1 2 4 7 3 10h1l2 6c0 2 0 3 1 5 1 1 0 1 1 2 0 2 2 6 2 8l1 1 44 142c0 1 1 2 1 3l4 15 54 184 71-232c0-1 0-2 1-3l2-8 2-5 2-9 1-2 4-13c1-1 1-2 1-3l20-62c2-2 2-4 3-6l4-15c2-6 4-13 6-19 1 1 2 1 3 0l1-1c1 0 1-1 2-1 1-1 1-1 2-1l-1 1 1 1c-1 0-1 0-1 1s0 1-1 2v1c0 1 0 2-1 3v1h0c-1 2-1 3-2 4v1 1c0 1-1 1-1 3v1c-1 1-1 3-2 5v1h0l-1 1v3h-1l1 1-1 1v1l-1 1 1 1h-1v1c-1 2 0-1 0 1-1 1-1 1-1 2v2 1l-6 21c-2 4-3 10-5 15l-8 28-93 319-87-303c-2-10-6-20-9-29-1-4-2-9-3-13l-13-45z" class="M"></path><path d="M252 229c14-6 33-5 47 0 18 7 33 25 41 44v1c3 5 6 13 8 18l18 55 11 53 8 32 37 136 22 76 6 22c1 2 3 6 2 8l-44-142-1-1c0-2-2-6-2-8-1-1 0-1-1-2-1-2-1-3-1-5l-2-6h-1c1-3-2-8-3-10 0-3-1-6-2-8v-1l-1-1c0-2 0-3-1-4v-2h0c-1-1-1-1-1-2s-1-2-1-4h-1l1-1c-1-1-1-1-1-2-1-1-1-1-1-2-1 0-2-1-3-1-2 0-2-1-3-2h-1c0 1 0 2 1 2 0 2 1 4 1 5l4 14c3 5 4 11 6 16 2 8 5 16 6 23v1c-1-1-6-18-7-21l-14-45c-4-13-8-25-14-36-4-9-11-18-18-23-15-12-35-15-52-21l-3-3s-2 0-2-1c-3-1-6-1-9-2h-1c-1-1-3-2-5-3-3-1-6-2-9-4v-1l-4-1c-1 0-1 0-2-1h-1-2l1-3c3 1 7 2 9 3h1v-1l-10-3c-5-1-10-6-14-9-5-3-9-5-15-7l-3-3c-2-1-3-1-4-3-3-3-6-7-9-10v-1h0c2 1 3 2 4 2 1-1 1-2 2-3 2-4 8-4 12-4v-1c-3-5-8-2-11-5 0-2 1-3 2-5 0-1 1-2 1-3h2 1c0-2-1-4-2-5v-2c-1-2-3-3-5-4l-11-11c3-1 4 1 7 1-2-1-4-3-6-4h0c-1-1-1-1-2-1l-1-1c-1-2 2-5 3-7 4-12-1-23-6-34v-1h0c0-2-1-3-2-5l7 7c1 0 2-1 3-1v1c1 0 1 0 2 1 1-2 1-3 2-5 2 0 6 0 8-1l6-3c3-1 5-1 7 1h1v1l1 2v-1c1-1 2-3 2-5h1l-1 1c1 1 2 1 3 2h0v-1c0-3 4-6 7-8l2-1z" class="V"></path><path d="M256 341c1 0 2 1 3 2l2 1h1c0 1 1 1 1 1l1-1c2 0 2 0 4 2 0 1 0 2 1 3s1 1 1 2l-8-5c-1-1-3-3-5-3l-1-2z" class="B"></path><path d="M245 337c2 0 5 1 8 1l2 3h1l1 2h0c-2 0-2-1-3-2h-1c-1 1 0 1-1 0v1l-3-1c-1 1-1 2-1 4l-3-8z" class="Y"></path><path d="M354 394c1-1 2-3 4-4 3 4 7 8 9 12-1 0-1-1-2-2l-1-1v-1c-1-1-1 0-1-1s-1-1-1-1h-1c0 1-1 1-1 2l2 3h0c-2-2-5-5-8-7z" class="B"></path><path d="M349 337c1 2 2 4 1 6l-3 5c-2 2-3 4-3 7h0c-1-1-1-1 0-2v-4c0-2 2-3 2-5-1 1-1 1-2 1l-1-1h0l3-3 3-4z" class="N"></path><path d="M349 337c1 2 2 4 1 6l-3 5c0-3 1-4 1-7h0c1-1 1 0 1-1l-3 1 3-4z" class="B"></path><path d="M244 356c2 0 4 1 5 2l4 3c1 1 2 1 3 1v-1l3 1v-1l11 5c2 1 5 2 7 4-5-1-11-4-16-5h-3c-5-1-10-6-14-9z" class="U"></path><path d="M343 356c-1-1-1-2-1-3 0-2 0-3 1-4h1v4c-1 1-1 1 0 2v1h1c2 2 4 2 6 3-1 2-1 2-2 2h-4c3 2 6 3 8 5l-1 1c-3-2-5-4-8-5h0-2l-2-2h0c0-1 1-2 1-3v-1h2z" class="W"></path><path d="M341 357v-1h2l1 2-1 1h-1c-1 0-1 1-2 1h0c0-1 1-2 1-3z" class="X"></path><defs><linearGradient id="AJ" x1="287.439" y1="349.415" x2="278.061" y2="351.585" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#9c9a9b"></stop></linearGradient></defs><path fill="url(#AJ)" d="M272 338c3 2 8 5 10 8 1 4 11 7 9 11l-16-10h2v-2c0-1-1-1-2-2-1-2-2-3-3-5z"></path><path d="M241 336c3 4 5 9 8 13 3 5 6 9 10 12v1l-3-1v1c-1 0-2 0-3-1l-4-3v-1c0-2-1-2 0-3l-1-1c0-2-2-3-2-4s0-1-1-2h0v-2c-1-2-1-4-3-6-1-1-1-2-1-3z" class="H"></path><path d="M249 354c1 1 4 4 4 6v1l-4-3v-1c0-2-1-2 0-3z" class="D"></path><defs><linearGradient id="AK" x1="276.21" y1="341.849" x2="263.707" y2="339.709" xlink:href="#B"><stop offset="0" stop-color="#adabac"></stop><stop offset="1" stop-color="#d3d1d2"></stop></linearGradient></defs><path fill="url(#AK)" d="M263 331l9 7c1 2 2 3 3 5 1 1 2 1 2 2v2h-2l-16-10c2 0 2 1 4 1l1 1c0-1 1-2 1-2l1-1-1-1c-2-1-2-2-2-4z"></path><defs><linearGradient id="AL" x1="343.232" y1="348.214" x2="337.222" y2="348.844" xlink:href="#B"><stop offset="0" stop-color="#4c4b4d"></stop><stop offset="1" stop-color="#686768"></stop></linearGradient></defs><path fill="url(#AL)" d="M343 344l1 1c1 0 1 0 2-1 0 2-2 3-2 5h-1c-1 1-1 2-1 4 0 1 0 2 1 3h-2v1c0 1-1 2-1 3l-2-1c0-1 0-1 1-1-1-2-2-3-2-5v2c-1 0-1 0-1-1-4-2-1-3-2-6h-3c4-1 8-2 12-4z"></path><path d="M337 353l1-1v1c1 2 1 3 3 4 0 1-1 2-1 3l-2-1c0-1 0-1 1-1-1-2-2-3-2-5z" class="P"></path><path d="M335 360c2 0 5 2 7 2h2 0v1c2 2 6 3 8 6v1l2 2h0c1 1 1 1 2 1 1 1 1 2 2 3l1 1h-1l-2-2c-2-2-5-3-7-5h-1c1 1 2 2 4 3 0 1 0 2 1 3 1 0 1 0 2-1 1 1 2 2 3 4v1l3 2-1 1c-3-4-7-6-10-9-1-1-5-3-5-4v-1c-2-2-4-3-6-5-2-1-3-3-4-4z" class="J"></path><path d="M315 360v-1c1 0 2 1 3 1h4c4-1 7-1 11 0h2c1 1 2 3 4 4 2 2 4 3 6 5v1c-6-3-13-5-20-7-3-1-8-1-10-3z" class="W"></path><path d="M333 360h2c1 1 2 3 4 4l-4-1c-1 0-1 0-2-1v-2z" class="B"></path><path d="M344 355v-1c3-2 5-7 9-8 2 0 3 2 4 3 1 2 1 4 1 6l-3 3-7-1c-2-1-2-1-4-1v-1h0z" class="J"></path><path d="M282 346h1v1l15 9c9 5 17 11 26 16-3-1-7-2-11-3-8-3-15-7-22-12h0c2-4-8-7-9-11z" class="P"></path><defs><linearGradient id="AM" x1="352.73" y1="393.612" x2="340.393" y2="379.528" xlink:href="#B"><stop offset="0" stop-color="#353536"></stop><stop offset="1" stop-color="#666566"></stop></linearGradient></defs><path fill="url(#AM)" d="M330 377c9 2 21 6 28 13-2 1-3 3-4 4-8-7-16-11-27-12 1-2 2-3 3-5z"></path><defs><linearGradient id="AN" x1="335.846" y1="359.898" x2="324.945" y2="350.108" xlink:href="#B"><stop offset="0" stop-color="#6b6b6b"></stop><stop offset="1" stop-color="#a09fa0"></stop></linearGradient></defs><path fill="url(#AN)" d="M330 348h1 3c1 3-2 4 2 6 0 1 0 1 1 1v-2c0 2 1 3 2 5-1 0-1 0-1 1l2 1h0l2 2c-2 0-5-2-7-2h-2c-4-1-7-1-11 0h-4c-1 0-2-1-3-1v1l-1-1h0c3-4 9-5 12-9 1-1 2-1 3-2h1z"></path><path d="M329 348h1c1 1 1 0 1 2-1 1-1 1-3 2v-1c0-1 1-1 1-2v-1z" class="D"></path><path d="M315 359c4-3 9-4 14-4 3 1 6 3 9 4l2 1h0l2 2c-2 0-5-2-7-2h-2c-4-1-7-1-11 0h-4c-1 0-2-1-3-1z" class="V"></path><path d="M223 313h2l-1 4h0c2 0 6-1 7-1 2 1 5 2 6 4 1 1 3 2 4 3h0-1l-1-1v1 1c1 0 1 1 2 2v1c3 4 7 7 12 11-3 0-6-1-8-1-4-6-7-9-14-10v-1c-3-5-8-2-11-5 0-2 1-3 2-5 0-1 1-2 1-3z" class="X"></path><path d="M223 313h2l-1 4h0c2 0 6-1 7-1 2 1 5 2 6 4 1 1 3 2 4 3h0-1l-1-1v1 1c1 0 1 1 2 2v1c-2-2-3-4-6-5v-1c-4-1-8-1-12-1-1-1-1-3-1-4s1-2 1-3z" class="S"></path><path d="M343 329v-1c0-1-1-1-1-2l-1-1c2 0 2 1 3 2 3 3 4 7 5 10l-3 4-3 3h0c-4 2-8 3-12 4h-1-1c-1 1-2 1-3 2v-3c-2-1-4-3-5-6l6 1c2 0 4-1 6-1s4-1 5-2l3-3 2-2v-5z" class="J"></path><defs><linearGradient id="AO" x1="283.578" y1="370.351" x2="270.942" y2="351.701" xlink:href="#B"><stop offset="0" stop-color="#2b2b2c"></stop><stop offset="1" stop-color="#5d5d5e"></stop></linearGradient></defs><path fill="url(#AO)" d="M270 351c9 4 15 8 22 14l6 6-1 1c-2-1-2-2-4-3-1 0-1 0-2-1-2-2-4-2-7-3-2-1-3-2-5-2-6-1-12-1-18-4 1 0 2-1 3-1 2 0 2 0 4-1 2-2 2-3 2-6z"></path><defs><linearGradient id="AP" x1="270.179" y1="352.143" x2="246.897" y2="347.29" xlink:href="#B"><stop offset="0" stop-color="#606060"></stop><stop offset="1" stop-color="#939292"></stop></linearGradient></defs><path fill="url(#AP)" d="M248 345c0-2 0-3 1-4l3 1v-1c1 1 0 1 1 0h1c1 1 1 2 3 2h0c2 0 4 2 5 3l8 5h0c0 3 0 4-2 6-2 1-2 1-4 1-1 0-2 1-3 1-6-3-10-8-13-14z"></path><defs><linearGradient id="AQ" x1="295.733" y1="284.739" x2="271.267" y2="292.761" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#cac9ca"></stop></linearGradient></defs><path fill="url(#AQ)" d="M247 240l1-1c3 7 7 12 11 18 3 3 4 7 7 9 9 19 26 39 45 50h1 0v1l-4-1h-1c-8-2-16-8-21-14-2-1-3-3-4-4-2-1-3-4-5-6-4-7-9-13-13-21-1-2-2-8-5-9l-1-1v-1l-1-1v2 1c-1-1-1-2-1-2-2-7-6-14-9-20z"></path><path d="M219 333l2-2c2-2 6-3 9-3 4 1 8 3 11 7v1c0 1 0 2 1 3 2 2 2 4 3 6v2h0c1 1 1 1 1 2s2 2 2 4l1 1c-1 1 0 1 0 3v1c-1-1-3-2-5-2-5-3-9-5-15-7l-3-3c-2-1-3-1-4-3 0-2-3-3-3-6 0 1 1 2 2 2-1-2-2-4-2-6z" class="R"></path><path d="M228 337c1-1 2-1 3-1h1c0 2 0 3-1 4h-2c-1-1-1-1-1-3z" class="J"></path><path d="M219 333l2-2c2-2 6-3 9-3 1 1 1 2 2 3l1 2v3h-1-1c-1 0-2 0-3 1h-1l-8-4z" class="F"></path><defs><linearGradient id="AR" x1="344.692" y1="285.625" x2="323.018" y2="289.189" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#AR)" d="M324 270v3c1 2 1 4 3 6 0 2 1 3 3 4 1 1 3 1 4 1 2-1 3-2 4-3 0-3 0-4-1-6l2-1 1 1v-1c3 5 6 13 8 18-1 0-1 0-1-1h0c-1 1-1 3-2 5-1 4-2 6-5 8-1 1-2 2-4 2-3-1-5-3-7-5-5-7-9-17-8-25l1-1c0-2 1-4 2-5z"></path><path d="M244 242c1-1 1-3 1-4h1l1 2c3 6 7 13 9 20 0 0 0 1 1 2v-1-2l1 1v1l1 1c3 1 4 7 5 9 4 8 9 14 13 21 2 2 3 5 5 6l1 3c-1 0-4-3-5-3v-1l-2-2c-1 1-2 1-2 3l-3 3c-1 0-1 0-2 1 0 1 0 2-1 2l-2-8v-1h-1 0c-1-1-2-2-2-3l-4-14-5-13c-1-1-2-1-3-2-3-2-6-5-7-8-1-5-2-11-1-16h0c0 2 0 2 1 3z" class="T"></path><path d="M266 296l3 2h1c1-2 2-4 4-5 1 0 1 1 2 2-1 1-2 1-2 3l-3 3c-1 0-1 0-2 1 0 1 0 2-1 2l-2-8z" class="Q"></path><path d="M244 242c1-1 1-3 1-4h1l1 2c3 6 7 13 9 20 0 0 0 1 1 2l9 33h-1 0c-1-1-2-2-2-3l-4-14-5-13c-1-1-2-1-3-2-3-2-6-5-7-8-1-5-2-11-1-16h0c0 2 0 2 1 3z" class="K"></path><path d="M243 239h0c0 2 0 2 1 3 5 6 7 16 10 23-1-1-2-1-3-2-3-2-6-5-7-8-1-5-2-11-1-16z" class="X"></path><path d="M248 235v-1c5 1 37 43 45 50 9 11 24 22 37 27l8 3v1h-1c-22-4-40-17-55-34l-14-17h-1-1 0c-1-1-1-2-2-3 0 2 1 3 2 4v1c-3-2-4-6-7-9-4-6-8-11-11-18v-2-2z" class="M"></path><path d="M248 237c5 6 8 13 13 19 1 2 6 6 6 8h-1 0c-1-1-1-2-2-3 0 2 1 3 2 4v1c-3-2-4-6-7-9-4-6-8-11-11-18v-2z" class="K"></path><path d="M289 331l4 3c3 1 5 2 7 3 6-1 11-2 18-2 0 1 0 1-1 2h1c3 0 7 0 11-1 1 1 3 1 3 2 1 1 1 2 1 3-2 0-4 1-6 1l-6-1c1 3 3 5 5 6v3c-3 4-9 5-12 9h0-1c-4-2-7-4-11-6l-19-11v-4l3 1 2 2h1c-1-3 0-5 0-8v-2z" class="V"></path><path d="M329 336c1 1 3 1 3 2 1 1 1 2 1 3-2 0-4 1-6 1l-6-1c-8 0-12 1-19 4l-1-1v-2-1c1-1 5 0 6-1 4 0 7-1 10-3h1c3 0 7 0 11-1z" class="C"></path><path d="M329 336c1 1 3 1 3 2 1 1 1 2 1 3-2 0-4 1-6 1v-3c-2-1-4 0-5-1-1 0-3 0-4-1 3 0 7 0 11-1z" class="F"></path><path d="M283 338l3 1 2 2h-1c5 4 9 4 14 6 3 0 6 1 9 2 2 1 3 1 5 3 0 2-4 4-3 5l2 2h0-1c-4-2-7-4-11-6l-19-11v-4z" class="N"></path><path d="M268 304c1 0 1-1 1-2 1-1 1-1 2-1l3-3c0-2 1-2 2-3l2 2v1c1 0 4 3 5 3h1c2 2 3 5 5 7 6 5 13 8 21 11-3 2-10-1-14-1h0l-3 2c-4 0-7 1-11 2v2c1 1 2 3 3 4l1-1 1 2c0 1 0 1 2 2h0v2c0 3-1 5 0 8h-1l-2-2-3-1c-6-5-10-13-13-21l-1 1c-1-1-2-2-2-3v-1c-1-1-1-2-2-4l1-1c1-1 1-3 2-4v-1z" class="L"></path><path d="M266 309c1-1 1-3 2-4l1 6h0l1 6-1 1c-1-1-2-2-2-3v-1c-1-1-1-2-2-4l1-1z" class="N"></path><path d="M273 309c1-1 9-3 10-3 0 2-1 4-2 6h2c1-1 1-1 2-1-2 2-5 3-8 4-2-1-2-2-3-3h1 1l2-1c-1-1-3-2-5-2z" class="X"></path><path d="M273 308c1-2 4-6 6-7l2 1c1 1 0 1 1 1 2 1 5 4 7 6-1 0-3 2-4 2s-1 0-2 1h-2c1-2 2-4 2-6-1 0-9 2-10 3v-1z" class="P"></path><path d="M273 308c-2-1-2-2-2-4 2-2 4-6 7-6 1 0 4 3 5 3h1c2 2 3 5 5 7 6 5 13 8 21 11-3 2-10-1-14-1h0c0-2 0-3-1-5h-1l-1 1c-5 0-9 3-13 4-3 0-4-1-6-2h2c3 1 6-1 9-1h1c2-2 3-3 4-6h-1c-2-2-5-5-7-6-1 0 0 0-1-1l-2-1c-2 1-5 5-6 7z" class="E"></path><path d="M270 311l1-1 3 6c2 1 3 2 6 2 4-1 8-4 13-4l1-1h1c1 2 1 3 1 5l-3 2c-4 0-7 1-11 2v2c1 1 2 3 3 4l1-1 1 2c0 1 0 1 2 2h0v2c0 3-1 5 0 8h-1l-2-2-3-1c-6-5-10-13-13-21l-1-6h1z" class="D"></path><path d="M277 325v-1c0-1 0-2 1-3 0-1 1-1 3-2l5-2c1 0 2 0 2 1 2 0 3 0 5 2-4 0-7 1-11 2h-1c-1 2 1 5 1 7v3c-2-2-4-5-5-7z" class="H"></path><path d="M282 332v-3c0-2-2-5-1-7h1v2c1 1 2 3 3 4l1-1 1 2c0 1 0 1 2 2h0v2c0 3-1 5 0 8h-1l-2-2c0-2-3-5-4-7z" class="U"></path><path d="M269 311h1c2 4 4 10 7 14 1 2 3 5 5 7 1 2 4 5 4 7l-3-1c-6-5-10-13-13-21l-1-6z" class="T"></path><defs><linearGradient id="AS" x1="316.225" y1="333.466" x2="312.24" y2="322.571" xlink:href="#B"><stop offset="0" stop-color="#6c6b6c"></stop><stop offset="1" stop-color="#8f8d8e"></stop></linearGradient></defs><path fill="url(#AS)" d="M296 318h0c4 0 11 3 14 1l7 2c4 1 11 3 16 2 2 0 6 0 7 2 1 1 2 3 3 4v5l-2 2-3 3c-1 1-3 2-5 2 0-1 0-2-1-3 0-1-2-1-3-2-4 1-8 1-11 1h-1c1-1 1-1 1-2-7 0-12 1-18 2-2-1-4-2-7-3l-4-3h0c-2-1-2-1-2-2l-1-2-1 1c-1-1-2-3-3-4v-2c4-1 7-2 11-2l3-2z"></path><path d="M328 328l-1-1h1c0-1 0-1 1-1l1 1 1-1c2 0 3 0 4-1l1 1c-1 1-1 2-1 4-2-2-3-1-4-2-1 0 0 0-2 1v-1h-1z" class="U"></path><path d="M287 329c1-1 2-2 2-3h1c1 0 2 1 3 2h1 6l1 1h-1c-2 1-3 1-5 2-1 1-1 1-2 3l-4-3h0c-2-1-2-1-2-2z" class="H"></path><path d="M328 328h1v1c0 1 1 6 0 7h0c-4 1-8 1-11 1h-1c1-1 1-1 1-2 3 0 5 0 7-3 1-1 2-3 3-4z" class="N"></path><path d="M329 329c2-1 1-1 2-1 1 1 2 0 4 2l1 2c0 2 0 3 1 4s1 2 1 3c-1 1-3 2-5 2 0-1 0-2-1-3 0-1-2-1-3-2h0c1-1 0-6 0-7z" class="Y"></path><path d="M333 323c2 0 6 0 7 2 1 1 2 3 3 4v5l-2 2-3 3c0-1 0-2-1-3s-1-2-1-4l-1-2c0-2 0-3 1-4l-1-1 1-1c-1-1-2-1-3-1z" class="N"></path><path d="M336 326h1c1 1 1 3 1 5h1c-2 1-2 1-3 1l-1-2c0-2 0-3 1-4z" class="B"></path><path d="M338 331l2-2c1 0 2 1 2 3 1 0 1 1 1 2l-2 2c-1-1-1-2-2-4v-1h-1z" class="P"></path><path d="M339 331v1c1 2 1 3 2 4l-3 3c0-1 0-2-1-3s-1-2-1-4c1 0 1 0 3-1z" class="U"></path><path d="M296 318c4 0 11 3 14 1l7 2v1c-2 2-5 5-8 5-2 0-3-1-5-1h-3l1-1h-1c1-1 1-2 1-3-1-2-2-2-3-3l-1 1-2-2z" class="D"></path><path d="M296 318h0l2 2 1-1c1 1 2 1 3 3 0 1 0 2-1 3-2 2-4 2-7 3h-1c-1-1-2-2-3-2h-1c0 1-1 2-2 3l-1-2-1 1c-1-1-2-3-3-4v-2c4-1 7-2 11-2l3-2z" class="C"></path><path d="M296 318h0l2 2v2c-2 1-3 1-5 1h-4c-2 1-2 2-3 4h0l-1 1c-1-1-2-3-3-4v-2c4-1 7-2 11-2l3-2z" class="B"></path><defs><linearGradient id="AT" x1="330.389" y1="285.524" x2="289.815" y2="277.421" xlink:href="#B"><stop offset="0" stop-color="#505051"></stop><stop offset="1" stop-color="#908f8f"></stop></linearGradient></defs><path fill="url(#AT)" d="M317 257l1-1c1-1 2-2 4-3v1c-1 1-2 3-3 5l-2 2c2 1 6 2 7 4h0l-2 4c-4 12-2 22 5 33 2 3 3 5 3 9-13-5-28-16-37-27 0-2-2-3-3-5 1-1 3-3 5-4 1-1 3-2 3-3 2-1 2-2 2-4 1-1 3-2 5-4l8-10c2 1 3 2 4 3z"></path><path d="M317 257l1-1c1-1 2-2 4-3v1c-1 1-2 3-3 5l-2 2-9 14-1-1c4-6 6-11 10-17z" class="V"></path><path d="M313 272c1 0 2-1 3-2 0 2-1 5-1 7l-2 10c-3-2-6-5-8-7v-1l1-1c0-1 1-2 2-2 2-1 3-2 5-4z" class="H"></path><path d="M317 261c2 1 6 2 7 4h0l-2 4-1-1 1-1-1-1c-2 0-2 1-3 2s-1 2-1 4c0 1 0 1-1 2l-1 3c0-2 1-5 1-7-1 1-2 2-3 2-2 2-3 3-5 4-1 0-2 1-2 2l-1-1c1-1 1-2 2-3l1 1 9-14z" class="X"></path><path d="M313 272c0-1 1-2 1-4 1-1 1-2 2-3 1 1 1 1 2 3-1 1-1 2-1 4 0 1 0 1-1 2l-1 3c0-2 1-5 1-7-1 1-2 2-3 2z" class="P"></path><path d="M266 372v-1l-4-1c-1 0-1 0-2-1h-1-2l1-3 9 3h1v-1c12 4 24 13 38 12 1-1 1-1 1-3 0-1-1-2-2-3l-1-1 26 4c-1 2-2 3-3 5-5 0-9 1-13 3 6 1 12 2 18 4 12 4 24 12 31 23 11 14 17 32 22 48l15 46c3 8 6 17 8 26l-1-1c0-2-2-6-2-8-1-1 0-1-1-2-1-2-1-3-1-5l-2-6h-1c1-3-2-8-3-10 0-3-1-6-2-8v-1l-1-1c0-2 0-3-1-4v-2h0c-1-1-1-1-1-2s-1-2-1-4h-1l1-1c-1-1-1-1-1-2-1-1-1-1-1-2-1 0-2-1-3-1-2 0-2-1-3-2h-1c0 1 0 2 1 2 0 2 1 4 1 5l4 14c3 5 4 11 6 16 2 8 5 16 6 23v1c-1-1-6-18-7-21l-14-45c-4-13-8-25-14-36-4-9-11-18-18-23-15-12-35-15-52-21l-3-3s-2 0-2-1c-3-1-6-1-9-2h-1c-1-1-3-2-5-3-3-1-6-2-9-4z" class="L"></path><defs><linearGradient id="AU" x1="257.478" y1="304.987" x2="234.731" y2="316.399" xlink:href="#B"><stop offset="0" stop-color="#706f70"></stop><stop offset="1" stop-color="#b2b0b1"></stop></linearGradient></defs><path fill="url(#AU)" d="M209 279v1c0 1 1 2 0 4v2h1l1 2c2 1 3 1 5 2 0 1 1 1 2 1l1 1h2l1 1c1 0 3-1 4 0 2 0 4 0 5 1 2 0 4-1 5 0h4l-4-1c1 0 4-1 5 0h1c2 0 4 1 5 2l4 2c0 2 0 3 1 4 2-1 2-2 5-1h0c2 1 3 2 4 4 2 2 4 3 4 6 1 2 1 3 2 4v1c0 1 1 2 2 3l1-1c3 8 7 16 13 21v4l-1 2h0l1 2h-1c-2-3-7-6-10-8l-9-7c0 2 0 3 2 4l1 1-1 1s-1 1-1 2l-1-1c-2 0-2-1-4-1-1 0-2-1-3-1-6-4-10-8-15-13h0c-1-1-3-2-4-3-1-2-4-3-6-4-1 0-5 1-7 1h0l1-4h1c0-2-1-4-2-5v-2c-1-2-3-3-5-4l-11-11c3-1 4 1 7 1-2-1-4-3-6-4h0c-1-1-1-1-2-1l-1-1c-1-2 2-5 3-7z"></path><path d="M257 300c2 1 3 2 4 4 2 2 4 3 4 6 1 2 1 3 2 4v1l-7 3c1-1 2-2 2-4l-1-1c-2 1-5 0-7 0h0c4 0 6 0 9-3 0-1 0-1-1-3v-2h0c-3 0-4-3-5-5z" class="P"></path><path d="M236 293c1 0 4-1 5 0h1c2 0 4 1 5 2l4 2c0 2 0 3 1 4-1 2-1 5 0 7 1 1 2 2 4 3 1 0 3 0 4-1s2-2 2-3c1 2 1 2 1 3-3 3-5 3-9 3-1-1-3-2-4-4-1-3-1-6 0-9v-2c-3-2-6-4-10-4l-4-1z" class="K"></path><path d="M252 301c2-1 2-2 5-1h0c1 2 2 5 5 5h0v2c0 1-1 2-2 3s-3 1-4 1c-2-1-3-2-4-3-1-2-1-5 0-7z" class="U"></path><path d="M260 318l7-3c0 1 1 2 2 3l1-1c3 8 7 16 13 21v4l-1 2h0l1 2h-1c-2-3-7-6-10-8l-9-7v-1c1 1 2 2 3 2l1-1c-3-4-4-8-8-10-1 0-1 0-1-1l2-2z" class="S"></path><path d="M259 321c2 0 6-1 7 1 2 1 2 5 3 7 0 1 2 2 3 3l7 7h1l1 2v1s0 1 1 2l1 2h-1c-2-3-7-6-10-8l-9-7v-1c1 1 2 2 3 2l1-1c-3-4-4-8-8-10z" class="W"></path><path d="M263 330c1 1 2 2 3 2 4 4 8 5 12 7h1 1l1 2v1s0 1 1 2l1 2h-1c-2-3-7-6-10-8l-9-7v-1z" class="J"></path><defs><linearGradient id="AV" x1="250.697" y1="306.997" x2="224.606" y2="320.548" xlink:href="#B"><stop offset="0" stop-color="#d8d7d7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AV)" d="M208 291c3-1 4 1 7 1l15 15 1-1-13-12h1c7 6 14 13 21 19l15 12c3 2 5 5 8 5v1c0 2 0 3 2 4l1 1-1 1s-1 1-1 2l-1-1c-2 0-2-1-4-1-1 0-2-1-3-1-6-4-10-8-15-13h0c-1-1-3-2-4-3-1-2-4-3-6-4-1 0-5 1-7 1h0l1-4h1c0-2-1-4-2-5v-2c-1-2-3-3-5-4l-11-11z"></path><path d="M240 236h1l-1 1c1 1 2 1 3 2-1 5 0 11 1 16 1 3 4 6 7 8 1 1 2 1 3 2l5 13 4 14c0 1 1 2 2 3h0 1v1l2 8v1c-1 1-1 3-2 4l-1 1c0-3-2-4-4-6-1-2-2-3-4-4h0c-3-1-3 0-5 1-1-1-1-2-1-4l-4-2c-1-1-3-2-5-2h-1c-1-1-4 0-5 0l4 1h-4c-1-1-3 0-5 0-1-1-3-1-5-1-1-1-3 0-4 0l-1-1h-2l-1-1c-1 0-2 0-2-1-2-1-3-1-5-2l-1-2h-1v-2c1-2 0-3 0-4v-1c4-12-1-23-6-34v-1h0c0-2-1-3-2-5l7 7c1 0 2-1 3-1v1c1 0 1 0 2 1 1-2 1-3 2-5 2 0 6 0 8-1l6-3c3-1 5-1 7 1h1v1l1 2v-1c1-1 2-3 2-5z" class="V"></path><path d="M257 300v-2c-1-2-4-4-5-5 2 0 3 1 4 2 2 1 4 3 5 5v-3c2 2 4 5 5 8v4l-1 1c0-3-2-4-4-6-1-2-2-3-4-4h0z" class="J"></path><defs><linearGradient id="AW" x1="262.483" y1="308.48" x2="252.783" y2="278.882" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#6c6b6c"></stop></linearGradient></defs><path fill="url(#AW)" d="M243 283h2 3c3 0 7-2 9-5h0 1 1l4 14c0 1 1 2 2 3h0 1v1l2 8v1c-1 1-1 3-2 4v-4c-1-3-3-6-5-8-6-6-11-10-18-14z"></path><defs><linearGradient id="AX" x1="223.204" y1="259.423" x2="201.878" y2="257.919" xlink:href="#B"><stop offset="0" stop-color="#969495"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#AX)" d="M215 242c2 0 6 0 8-1l6-3c3-1 5-1 7 1-2 0-2 0-4 1 0 1 0 6-1 7h-2 0c-2 1-6 4-7 6-3 5-5 11-3 16v2c3 5 8 8 13 10 1 1 3 2 4 3-3-1-5-2-8-2l-1-1-11-1c-2-1-3-1-5 0-1 1-1 2-1 4 2 3 7 5 11 6l15 3 4 1h-4c-1-1-3 0-5 0-1-1-3-1-5-1-1-1-3 0-4 0l-1-1h-2l-1-1c-1 0-2 0-2-1-2-1-3-1-5-2l-1-2h-1v-2c1-2 0-3 0-4v-1c4-12-1-23-6-34v-1h0c0-2-1-3-2-5l7 7c1 0 2-1 3-1v1c1 0 1 0 2 1 1-2 1-3 2-5z"></path><path d="M229 247h0c-2 0-3 1-5 2l-1-1c1-2 1-3 2-4 2-2 4-4 7-4 0 1 0 6-1 7h-2z" class="P"></path><path d="M240 236h1l-1 1c1 1 2 1 3 2-1 5 0 11 1 16 1 3 4 6 7 8 1 1 2 1 3 2l5 13h-1-1 0c-2 3-6 5-9 5h-3-2c-5-2-11-4-15-7h-1c-3-1-5-4-6-7l-1 2v-1l-1 1v-2c-2-5 0-11 3-16 1-2 5-5 7-6h0 2c1-1 1-6 1-7 2-1 2-1 4-1h1v1l1 2v-1c1-1 2-3 2-5z" class="I"></path><path d="M242 258l1-1h1c2 3 4 5 6 7-3 2-6 5-10 5-1 0-3 0-4-1-1-2-5-4-5-6 1-2 1-2 2-3 1 1 1 2 2 3v1c1 2 2 3 5 4 3 0 5-1 8-3l-6-6z" class="S"></path><path d="M219 271v-2c-2-5 0-11 3-16 1-2 5-5 7-6 3 1 5 1 7 2v1c-3 0-6-1-8 1-4 2-6 8-7 12v6l-1 2v-1l-1 1z" class="J"></path><path d="M240 236h1l-1 1c1 1 2 1 3 2-1 5 0 11 1 16l-1 1v1l-1 1c-2-3-3-6-6-8v-1c-2-1-4-1-7-2h0 2c1-1 1-6 1-7 2-1 2-1 4-1h1v1l1 2v-1c1-1 2-3 2-5z" class="B"></path><path d="M232 240c2-1 2-1 4-1h1v1l-1 1v7 1c-2-1-4-1-7-2h0 2c1-1 1-6 1-7z" class="N"></path><path d="M240 237c1 1 2 1 3 2-1 5 0 11 1 16l-1 1-2-4c-2-5-2-9-1-15z" class="Q"></path><defs><linearGradient id="AY" x1="254.415" y1="280.472" x2="241.964" y2="270.461" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#9a9899"></stop></linearGradient></defs><path fill="url(#AY)" d="M244 255c1 3 4 6 7 8 1 1 2 1 3 2l5 13h-1-1 0c-2 3-6 5-9 5h-3-2c-5-2-11-4-15-7h1l1 1c1 0 2 0 3 1h0 1l1 1 4-4c1-1 2-1 2-2h1c0-1 1-2 1-3h-1-1c-2 0-3 0-4-1l-1-1c1 1 3 1 4 1 4 0 7-3 10-5-2-2-4-4-6-7h-1v-1l1-1z"></path><path d="M252 229c14-6 33-5 47 0 18 7 33 25 41 44v1 1l-1-1-2 1c1 2 1 3 1 6-1 1-2 2-4 3-1 0-3 0-4-1-2-1-3-2-3-4-2-2-2-4-3-6v-3-1c1-2 4-3 6-3l1-1-1-1c-2 0-4 1-6 1h0c-1-2-5-3-7-4l2-2c1-2 2-4 3-5v-1c-2 1-3 2-4 3l-1 1c-1-1-2-2-4-3l-8 10c-2 2-4 3-5 4 0 2 0 3-2 4 0 1-2 2-3 3-2 1-4 3-5 4 1 2 3 3 3 5-8-7-40-49-45-50v1 2 2l-1 1-1-2h-1c0 1 0 3-1 4-1-1-1-1-1-3v-1c0-3 4-6 7-8l2-1z" class="S"></path><path d="M330 266c1 1 2 1 3 2v1c-1-1-1-2-3-2 0 2 0 3-1 5l1 1 1-1v1l-4 6c-2-2-2-4-3-6v-3-1c1-2 4-3 6-3z" class="B"></path><path d="M250 230l2-1h3l1 1c1 0 5-1 6 0 1 0 3 0 5 1h3c1 0 1 0 2 1h2c2 1 4 1 5 3 0 1-1 3-2 4-1-1-1-2-2-3-4-3-11-3-15-4-2 0-5-1-7-1-2 1-5 1-6 3l1 1v2 2l-1 1-1-2h-1c0 1 0 3-1 4-1-1-1-1-1-3v-1c0-3 4-6 7-8z" class="J"></path><defs><linearGradient id="AZ" x1="303.153" y1="250.127" x2="279.792" y2="251.98" xlink:href="#B"><stop offset="0" stop-color="#9c9b9c"></stop><stop offset="1" stop-color="#b8b7b7"></stop></linearGradient></defs><path fill="url(#AZ)" d="M302 247c3 0 6 1 8 4-1 3-5 8-7 10-1 1-3 2-5 2-5-1-12-9-16-13-2-2-4-6-3-9 0-2 1-4 3-6 4 1 7 2 9 5 3-1 5-1 8-1l3 3v5z"></path><path d="M291 240c3-1 5-1 8-1l3 3v5c0 1 0 1-1 2 0 2-2 3-4 4-1 0-3 0-4-1-2-2-4-5-4-7s1-3 2-5z"></path><path d="M293 241h2c1-1 3 0 4 1s1 3 1 4c0 2-1 3-2 4h-1c-2 0-3 0-5-1-1-1-2-3-1-5 0-2 1-2 2-3z" class="X"></path><defs><linearGradient id="Aa" x1="293.921" y1="275.34" x2="258.18" y2="230.287" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#bab8b9"></stop></linearGradient></defs><path fill="url(#Aa)" d="M248 235l-1-1c1-2 4-2 6-3 2 0 5 1 7 1 4 1 11 1 15 4 1 1 1 2 2 3-1 2-2 4-1 6 4 9 13 19 21 24 1 0 2 0 3-1 0 2 0 3-2 4 0 1-2 2-3 3-2 1-4 3-5 4 1 2 3 3 3 5-8-7-40-49-45-50v1z"></path><path d="M735 212l1-1c2 0 3 0 4-1l1-2h0c2 1 4 3 6 5h1l2 1c1 1 1 1 2 1l11 4c1 0 2 0 3 1 2 1 5 3 8 3l6 3 1-1 5 7 3 6h2v1c1 0 1 1 2 1 3-1 10 1 12 3 3 3 3 7 3 11v2c2 4 3 9 2 14h0c-1 2-3 5-5 6v1h-1c-3 2-7 3-10 5l-2 1c1 0 7-1 7-2l5-2c1-1 2-1 3-2h1l2-1h2l1-1c1 1 2 0 3 1 1 0 2 2 3 2l1 1 2 6c-2 2-4 3-7 4l-12 10-14 14c1 0 1 0 2-1s1-1 2-1l2-2 1-1h1c1-1 2-2 3-2h0l1-1c1-1 2-1 3-1h0v1l-1 2v2c-1 2 1 5 2 7 1-1 0-1 0-3 3 2 4 7 6 9-2 0-3 0-4 1-2 1-5 1-6 3h1 0c3 0 4 1 6 2h4 1 7l-5 6-3 4c-1 1-2 2-3 2l-5 5h0l-8 5c-4 2-9 4-12 7-3 2-5 4-8 6-4 2-8 3-12 5-3 2-8 3-12 5v1c-1 0-2 1-2 1-1 1-1 1-2 1h-1l-1 1c-1 0-2 1-3 1-2 1-3 1-4 2-3 0-5 1-7 2l-1 1h-1 0l2 2c-19 5-40 9-55 23-6 5-10 11-13 17-8 13-13 28-18 42l-15 47c-2 8-4 16-7 24v-1-2c0-1 0-1 1-2 0-2-1 1 0-1v-1h1l-1-1 1-1v-1l1-1-1-1h1v-3l1-1h0v-1c1-2 1-4 2-5v-1c0-2 1-2 1-3v-1-1c1-1 1-2 2-4h0v-1c1-1 1-2 1-3v-1c1-1 1-1 1-2s0-1 1-1l-1-1 1-1c-1 0-1 0-2 1-1 0-1 1-2 1l-1 1c-1 1-2 1-3 0-2 6-4 13-6 19l-4 15c-1 2-1 4-3 6l5-17h-1v-1h0c-1 1-1 1-2 1l62-228-1 1v-1l1-1v-2c1-2 1-2 1-3l1-1v-1l1-1v-1l1-1v-1-1l1-1h0v-2l2-2v-2c1-1 1-1 1-2v-1h1c0-1 0-2 1-2v-1-1h1v-1-1h-1l-2-2c-1-1 0-1-1-1l-5 12h-1c-3 4-4 10-6 14v-1c-1-2 0-4 1-6-1-3-1-7-3-10v1c-1 1-1 2-1 4v-2l-1 1h0c-2-7-4-14-10-19v2h0l-1-1c-1 2-1 2-1 4l1 2v3c0-1-1-1-1-2-3-4-6-9-9-13-2-1-4-3-6-4v-1c1-2 4-6 6-7l6-4c3-2 6-4 9-7v3l-1 1-1 1 1 1c0 3 2 7 3 9h0l1-1c0-2-1-3-2-4l1-1v-1l-1-1c0-3 1-4 3-6 3-4 10-8 15-9s8-5 13-7c2-1 5-1 7-2 3 0 6-2 10-2s11 0 16 2l6 1z" class="V"></path><path d="M800 328c3 0 7 1 9 3l-1 1c-3 0-6-1-9-2 0-1 1-2 1-2z" class="I"></path><path d="M690 345c2 0 3 0 5 1 3 0 6-1 8 0l-1 2-1 1h0c-4-2-8-2-11-4z" class="B"></path><path d="M750 372c4-1 9-3 14-4-3 2-8 3-12 5v1c-1 0-2 1-2 1-2 1-4 1-6 1h-1c-4 2-13 6-18 4 9-1 17-4 25-8z" class="Q"></path><path d="M698 314c-5 1-11 2-15 5-2 1-4 3-6 5l4-6c3-5 11-5 15-6v1h1c0 1 0 0 1 1z" class="U"></path><path d="M674 368l-9 6c1-2 2-4 4-6l2-1h-2l1-1v-2l2-2 2-2h-1l6-2v1l-3 3c1 0 4-1 5-1l-1 1c-2 2-6 3-7 6h1z" class="B"></path><defs><linearGradient id="Ab" x1="692.707" y1="269.526" x2="701.677" y2="268.194" xlink:href="#B"><stop offset="0" stop-color="#282829"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#Ab)" d="M692 273c1-2 1-4 3-5 1-1 3-2 6-2 2 0 3 1 4 3-3 2-3 7-3 11h-1c0-3 0-7-3-9h-2c-1 1-2 1-3 1l-1 1z"></path><path d="M688 339h0c-2-1-4-4-5-6 0-2 1-3 1-5 2-2 3-4 6-4l2 1c0 2 1 3 0 5 0 1-1 2-2 2h-1-2c0 1-1 2 0 4l1 3z" class="B"></path><path d="M689 332l-1-2v-3l1-1v-1h3c0 2 1 3 0 5 0 1-1 2-2 2h-1z" class="N"></path><path d="M673 347c2 0 3 1 5 1v1l2-1c1 2 1 3 1 6-1 0-2 1-3 2-1 0-2 1-3 1-2 0-3 0-4-1 0-1-1-2-1-3l3-6z" class="B"></path><defs><linearGradient id="Ac" x1="659.06" y1="410.522" x2="667.638" y2="393.092" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#4e4d4f"></stop></linearGradient></defs><path fill="url(#Ac)" d="M671 396c-7 7-12 16-18 25 0-2 1-3 2-4l1-1c0-1 0-2 1-2v-1h0v-1l1-1c0-1-1-1-1-2-3 3-4 7-6 10v-1c0-1 1-3 2-4 2-5 6-9 9-14 3-4 7-9 11-12 0 2-5 5-4 6l2 1v1z"></path><defs><linearGradient id="Ad" x1="691.3" y1="279.603" x2="699.5" y2="275.498" xlink:href="#B"><stop offset="0" stop-color="#0f0f10"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#Ad)" d="M692 273l1-1c1 0 2 0 3-1h2c3 2 3 6 3 9 0 1 0 1-1 2s-2 2-4 3c-1 0-3-1-4-2-2-2-3-3-3-6h1c0 2 0 2 1 4l1-1h0c0-2-1-5 0-7h0z"></path><defs><linearGradient id="Ae" x1="669.675" y1="394.98" x2="688.772" y2="379.823" xlink:href="#B"><stop offset="0" stop-color="#4a4a4b"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#Ae)" d="M673 388l1-1c4-4 11-8 17-9 2 1 6 4 8 4-3 1-6 1-9 2-7 2-13 7-19 12v-1l-2-1c-1-1 4-4 4-6z"></path><path d="M705 269c3 6 2 14 0 20-2 4-4 9-7 12v-1h-1c0-4-1-8 0-11v-1c1-1 1-2 1-3 1 0 1 0 2-1s1-2 2-4c0-4 0-9 3-11z" class="P"></path><defs><linearGradient id="Af" x1="681.243" y1="339.096" x2="686.812" y2="349.33" xlink:href="#B"><stop offset="0" stop-color="#424142"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#Af)" d="M681 347c-1-2-2-3-4-5l2-6c3 4 6 7 10 9h1c3 2 7 2 11 4h-1c-1 0-6 0-6 1l-2 2c-1 1-2 3-2 5v1l-3 1h-1c-2 2-3 2-5 2h0c-1 0-4 1-5 1l3-3v-1c1 0 4-3 5-4l-3-7z"></path><path d="M681 347c2 1 3 1 3 3 1 1 2 4 2 5-1 1 0 2 0 3l-1 1h1c-2 2-3 2-5 2h0c-1 0-4 1-5 1l3-3v-1c1 0 4-3 5-4l-3-7z" class="W"></path><path d="M701 349h0c1 1 2 2 2 3h1 5 3v1c1 2 2 3 4 4l-1 1h-1c-1 1-2 1-3 2-1 0-3 1-4 1v-1h1 1c-1-1 0-1-1 0-2 0-4-1-5-2-2-1-5-1-7-1-3 1-4 2-6 3-1 1-3 2-5 3-3 2-6 4-9 4l-2 1h-1c1-3 5-4 7-6l1-1h0c2 0 3 0 5-2h1l3-1v-1c0-2 1-4 2-5l2-2c0-1 5-1 6-1h1z" class="S"></path><path d="M686 359h1c-3 4-7 5-11 8l-2 1h-1c1-3 5-4 7-6l1-1h0c2 0 3 0 5-2z" class="N"></path><path d="M701 349h0c1 1 2 2 2 3 3 2 7 5 9 7-1 0-3-1-4-2-4-2-9-3-13-1l-5 2v-1c0-2 1-4 2-5l2-2c0-1 5-1 6-1h1z" class="D"></path><path d="M743 338c1-1 3-2 4-2 0 0 0 1-1 2l-3 3c-9 7-19 14-29 19h-2-1c1-1 2-1 3-2h1l1-1c-2-1-3-2-4-4v-1l12-5v-1h1c1-1 1-1 1-3l-2-2h3c2 0 4 0 7-1v-1h1c3 1 4 0 6-1h2z" class="B"></path><path d="M724 341h3c2 0 4 0 7-1v-1h1c3 1 4 0 6-1 0 3-4 5-6 6l-8 4-3-1h0v-1h1c1-1 1-1 1-3l-2-2z" class="K"></path><defs><linearGradient id="Ag" x1="693.259" y1="323.934" x2="695.911" y2="334.279" xlink:href="#B"><stop offset="0" stop-color="#706f70"></stop><stop offset="1" stop-color="#888788"></stop></linearGradient></defs><path fill="url(#Ag)" d="M696 323h3l5 2-1 1c0 1 0 2 1 3h0-1l-3-2v8s-1 1-1 2h0 4c2 1 3 0 5 0l2 1 1 1c1 1 1 1 3 1h-1l-1 1h1l-8 1c-4 0-9 0-14-2l-2-1h-1l-1-3c-1-2 0-3 0-4h2 1c1 0 2-1 2-2 1-2 0-3 0-5l-2-1 6-1z"></path><path d="M699 337l-1-1c1-3 1-6 1-9v-1l1 1v8s-1 1-1 2z" class="B"></path><path d="M712 341c-4 0-7 0-11-1-2 0-4 0-6-1v-1l1-1h3 4c2 1 3 0 5 0l2 1 1 1c1 1 1 1 3 1h-1l-1 1z" class="C"></path><defs><linearGradient id="Ah" x1="653.072" y1="394.621" x2="701.382" y2="357.898" xlink:href="#B"><stop offset="0" stop-color="#111112"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#Ah)" d="M712 360c-8 4-18 5-26 9-10 5-19 13-26 22-2 3-5 7-7 9 0-3 2-5 3-8 2-4 5-8 8-11 3-2 4-5 7-7 6-5 13-9 20-13 2 0 4-1 5-1 4 0 7 2 11 1 1 0 3-1 4-1h1z"></path><defs><linearGradient id="Ai" x1="731.112" y1="370.895" x2="752.014" y2="356.596" xlink:href="#B"><stop offset="0" stop-color="#161618"></stop><stop offset="1" stop-color="#4c4c4d"></stop></linearGradient></defs><path fill="url(#Ai)" d="M757 350c2 0 3 0 4 2 2 2 2 3 5 4l1-1h1 1 4c-2 1-5 3-7 4-7 3-14 3-21 5-5 2-9 5-13 7h-1c-1 1-2 1-4 1l11-11c4-3 7-5 11-7 3-1 6-2 8-4z"></path><defs><linearGradient id="Aj" x1="751.543" y1="358.043" x2="763.246" y2="352.065" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#7b7a7b"></stop></linearGradient></defs><path fill="url(#Aj)" d="M757 350c2 0 3 0 4 2 2 2 2 3 5 4l1-1h1 1c-3 2-6 3-10 4-2 0-4 1-5 0-2 0-3-1-3-3-1 0-1-1-1-1l-1-1c3-1 6-2 8-4z"></path><defs><linearGradient id="Ak" x1="684.158" y1="290.585" x2="697.069" y2="292.819" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#Ak)" d="M698 301c-1 2-3 4-6 4-2 0-5-1-6-3-2-3-3-5-3-9v-4-1h1v-3l1-1v-1l1-1v4l3-5v-1-1c-1-1 0-1 0-2 0 3 1 4 3 6 1 1 3 2 4 2 2-1 3-2 4-3s1-1 1-2h1c-1 2-1 3-2 4s-1 1-2 1c0 1 0 2-1 3v1c-1 3 0 7 0 11h1v1z"></path><defs><linearGradient id="Al" x1="725.266" y1="369.311" x2="726.286" y2="350.747" xlink:href="#B"><stop offset="0" stop-color="#5c5e60"></stop><stop offset="1" stop-color="#7c7a7a"></stop></linearGradient></defs><path fill="url(#Al)" d="M761 333c-1 1-2 3-2 5l-1 1c-1 1-2 3-3 5l1 1c-3 2-7 4-10 6-12 9-26 17-41 21l44-31 4-2 8-6z"></path><path d="M761 333c-1 1-2 3-2 5l-1 1c-1 1-2 3-3 5l1 1c-3 2-7 4-10 6 1-4 5-9 7-12l8-6z" class="C"></path><defs><linearGradient id="Am" x1="754.316" y1="329.481" x2="761.711" y2="331.267" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#Am)" d="M761 308l1 1 1-1 1 1c2 2 5 3 8 4l4-1-1 1s0 1-1 1c0 1 0 1 1 2v2c-2 2-3 5-4 8-3 2-7 4-10 7l-8 6-4 2c1-3 4-5 6-9 2-2 3-5 4-8l1-4h-1l-3 2v-1c2-2 3-6 3-8l1-4 1-1z"></path><path d="M761 308l1 1 1-1 1 1c2 2 5 3 8 4l1 2c-1 1-2 2-2 3v2c-1 1 0 1-1 2 0-1 0-3-1-3h0-1c-3 1-7 3-9 5l1-4h-1l-3 2v-1c2-2 3-6 3-8l1-4 1-1z" class="U"></path><path d="M761 308l1 1-2 6c3 1 7 2 9 4h0-1c-3 1-7 3-9 5l1-4h-1l-3 2v-1c2-2 3-6 3-8l1-4 1-1z" class="K"></path><path d="M767 259l-1 3-3 8-7 12-3 4v1l-6 9c0 1-1 2-2 3h-1l-3 5c-7 7-13 12-22 15l-8 3-12 1h-3c2-1 4-1 5-1l-1-1c4-2 9-2 13-4 1-1 2-1 4-1 3-1 7-5 9-7 13-10 23-22 32-36 3-4 5-9 9-14z" class="F"></path><path d="M717 316l-1 2h1c8-2 19-9 24-16l3-3h0l-3 5c-7 7-13 12-22 15l-8 3-12 1h-3c2-1 4-1 5-1l-1-1c4-2 9-2 13-4 1-1 2-1 4-1z" class="B"></path><defs><linearGradient id="An" x1="768.44" y1="316.975" x2="794.06" y2="330.045" xlink:href="#B"><stop offset="0" stop-color="#d6d4d5"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#An)" d="M801 305c1-1 2-1 3-1h0v1l-1 2v2c-1 2 1 5 2 7l-1 1c-4-1-6-2-9 0-6 2-11 9-16 13-4 3-8 6-13 8-3 2-7 5-10 7l-1-1c1-2 2-4 3-5l1-1c0-2 1-4 2-5 3-3 7-5 10-7 4-2 8-5 11-7 2-2 4-5 7-6 1 0 1 0 2-1s1-1 2-1l2-2 1-1h1c1-1 2-2 3-2h0l1-1z"></path><defs><linearGradient id="Ao" x1="766.934" y1="338.897" x2="778.532" y2="347.028" xlink:href="#B"><stop offset="0" stop-color="#706f70"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#Ao)" d="M805 316c1-1 0-1 0-3 3 2 4 7 6 9-2 0-3 0-4 1-2 1-5 1-6 3h1 0c3 0 4 1 6 2h4 1 7l-5 6c-6-4-13-10-21-7-5 2-9 6-11 10-1 2-1 6-2 8-1 4-5 7-8 10h-4-1-1l-1 1c-3-1-3-2-5-4-1-2-2-2-4-2 3-2 6-3 9-5 5-3 10-8 15-12 4-3 8-10 13-12 4-1 9 0 13-1l-3-3 1-1z"></path><path d="M749 271c2-1 3-4 5-6l18-21c1-2 3-5 6-5-3 4-5 9-8 13v1c-16 20-31 41-55 54-5 3-11 5-17 7-1-1-1 0-1-1h-1v-1l1-1c4 0 8-3 11-5 16-9 29-22 41-35z" class="M"></path><defs><linearGradient id="Ap" x1="773.387" y1="370.973" x2="796.943" y2="328.415" xlink:href="#B"><stop offset="0" stop-color="#7e7d7e"></stop><stop offset="1" stop-color="#aeacad"></stop></linearGradient></defs><path fill="url(#Ap)" d="M750 372s0-1 1-2c0 0 5-2 6-2 6-3 11-5 15-9s8-7 10-12 3-9 6-13c4-4 7-6 12-6 0 0-1 1-1 2 3 1 6 2 9 2l1-1 1 1c0 2-2 4-1 6v2l-5 5h0l-8 5c-4 2-9 4-12 7-3 2-5 4-8 6-4 2-8 3-12 5-5 1-10 3-14 4z"></path><path d="M797 336h2c1 0 1 0 2 1-1 2-1 2-2 3h-2c-1-2-1-2-1-3l1-1z" class="B"></path><path d="M799 330c3 1 6 2 9 2l-1 2c-1 1-1 1-3 2-3-2-5-2-7-4l2-2z" class="E"></path><defs><linearGradient id="Aq" x1="755.503" y1="272.999" x2="762.201" y2="232.785" xlink:href="#B"><stop offset="0" stop-color="#8c8b8b"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#Aq)" d="M763 233c7-1 11-1 18 1l1 1-4 4c-3 0-5 3-6 5l-18 21c-2 2-3 5-5 6h-2v1c-2-4-3-8-6-10 3-3 6-7 8-10 1-2 4-6 4-8v-4c-1-2-1-2 0-3 2-3 6-4 10-4z"></path><defs><linearGradient id="Ar" x1="747.637" y1="326.873" x2="731.071" y2="310.749" xlink:href="#B"><stop offset="0" stop-color="#949394"></stop><stop offset="1" stop-color="#bab8b9"></stop></linearGradient></defs><path fill="url(#Ar)" d="M747 296v1c0 1-2 3-3 4v1l3-3 2-1c2 0 4-2 6-3 1 3 1 6 1 9 0 2-1 4 0 6l-1 1v5c1 1 1 1 1 3v2 1c-2 3-3 6-5 9-1 2-3 3-4 5-1 0-3 1-4 2h-2c-2 1-3 2-6 1 2-2 5-5 5-7 1-1 1-2 2-2-1-2-1-3-2-5h0l-1-2h-1c-2 1-3 0-5 0l-5-2h-1v-1l-1-1c-2 0-5 1-7 0 9-3 15-8 22-15l3-5h1c1-1 2-2 2-3z"></path><path d="M728 321c2-1 3-3 6-2l1 2-1 2h-1l-5-2z" class="V"></path><path d="M734 319c4 1 9 1 12 3l1 1c-1 2-3 6-5 7-1-2-1-3-2-5h0l-1-2h-1c-2 1-3 0-5 0h1l1-2-1-2z" class="N"></path><path d="M735 321c1-1 1-1 2 0 2 0 3 1 4 2 0 1 0 1-1 2h0 0l-1-2h-1c-2 1-3 0-5 0h1l1-2z" class="P"></path><path d="M742 330c2-1 4-5 5-7v8c-1 2-3 5-4 6v1h-2c-2 1-3 2-6 1 2-2 5-5 5-7 1-1 1-2 2-2z" class="Y"></path><defs><linearGradient id="As" x1="744.931" y1="325.867" x2="755.571" y2="325.166" xlink:href="#B"><stop offset="0" stop-color="#cfcdcd"></stop><stop offset="1" stop-color="#f3f1f4"></stop></linearGradient></defs><path fill="url(#As)" d="M755 316c1 1 1 1 1 3v2 1c-2 3-3 6-5 9-1 2-3 3-4 5-1 0-3 1-4 2v-1c1-1 3-4 4-6 3-4 4-8 6-13h0l2-2z"></path><path d="M747 296v1c0 1-2 3-3 4v1l3-3 2-1c2 0 4-2 6-3 1 3 1 6 1 9 0 2-1 4 0 6l-1 1v5l-2 2h0c-1-2-4-1-6-3l1-1s1 1 2 1h2l1-1c0-1-1-1-2-1s-2 0-3-1v-1h1c3-1 4-3 6-5v-4l-3-3c-1 0-2 1-4 2h0l-1 1c-1 1-2 1-4 2h-1 0-1l3-5h1c1-1 2-2 2-3z" class="E"></path><defs><linearGradient id="At" x1="717.935" y1="335.065" x2="720.369" y2="319.212" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#a6a4a4"></stop></linearGradient></defs><path fill="url(#At)" d="M711 322l8-3c2 1 5 0 7 0l1 1v1h1l5 2c2 0 3 1 5 0h1l1 2h0c1 2 1 3 2 5-1 0-1 1-2 2 0 2-3 5-5 7h-1v1c-3 1-5 1-7 1h-3l2 2c0 2 0 2-1 3h-1c-3-3-7-4-11-5h-1l1-1h1c-2 0-2 0-3-1l-1-1-2-1c-2 0-3 1-5 0h-4 0c0-1 1-2 1-2v-8l3 2h1 0c-1-1-1-2-1-3l1-1-5-2 12-1z"></path><path d="M711 322l8-3c2 1 5 0 7 0l-1 1c-5 1-8 5-13 2h-1z" class="D"></path><path d="M727 321h1l5 2c2 0 3 1 5 0h1c0 2 0 3-1 4-4 0-8-2-11-5v-1z" class="C"></path><path d="M700 327l3 2v1c2 3 4 4 7 4v1h-1l-2 2h-4-4 0c0-1 1-2 1-2v-8z" class="X"></path><path d="M708 337c3-3 9-3 14-1 1 0 3 1 4 1s3-1 4-1c3-1 5-5 7-5 1-1 2 0 3 1 0 2-3 5-5 7h-1v1c-3 1-5 1-7 1h-3l2 2c0 2 0 2-1 3h-1c-3-3-7-4-11-5h-1l1-1h1c-2 0-2 0-3-1l-1-1-2-1z" class="V"></path><path d="M710 338l14 3 2 2c0 2 0 2-1 3h-1c-3-3-7-4-11-5h-1l1-1h1c-2 0-2 0-3-1l-1-1z" class="F"></path><path d="M766 262c2-1 2-2 4-2h0c2 0 2 0 3 2v-1l1-1v4c1-1 2-2 3-4v2l3 3c4 3 6 4 10 5h0-4-1c0 2 3 5 5 6h1l2 2 1 1-13 6c-6 3-10 7-14 12-3 3-5 8-6 11l-1 1-1 4c0 2-1 6-3 8v-2c0-2 0-2-1-3v-5l1-1c-1-2 0-4 0-6 0-3 0-6-1-9-2 1-4 3-6 3l-2 1-3 3v-1c1-1 3-3 3-4v-1l6-9v-1l3-4 7-12 3-8z" class="T"></path><path d="M770 260c2 0 2 0 3 2v-1l1-1v4c-1 4-10 31-12 32l8-36h0z" class="S"></path><path d="M763 270v1c0 1-1 2-2 4l1 1-4 13c-2 3-3 6-1 9 0 1 1 3 1 4 0 2-1 4-1 6 1-1 1-2 1-3 1 0 1-1 1-1 0-2 1-3 2-4-1 4-2 7-3 11l2-2-1 4c0 2-1 6-3 8v-2c0-2 0-2-1-3v-5l1-1c-1-2 0-4 0-6 0-3 0-6-1-9-2 1-4 3-6 3l-2 1-3 3v-1c1-1 3-3 3-4v-1l6-9v-1l3-4 7-12z" class="O"></path><defs><linearGradient id="Au" x1="756.14" y1="299.377" x2="776.11" y2="278.386" xlink:href="#B"><stop offset="0" stop-color="#353435"></stop><stop offset="1" stop-color="#6f6e6e"></stop></linearGradient></defs><path fill="url(#Au)" d="M774 264c1-1 2-2 3-4v2l3 3h-1c-1 0-1-1-2-1h0v1c0 1-1 2-2 4l-1 1h0v-1l-1 1c-1 2-1 3-1 5 1 4 2 7 6 9 1 1 2 1 3 1-6 3-10 7-14 12-3 3-5 8-6 11l-1 1-2 2c1-4 2-7 3-11l1-4c2-1 11-28 12-32z"></path><defs><linearGradient id="Av" x1="772.769" y1="278.645" x2="787.264" y2="269.199" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#Av)" d="M781 285c-1 0-2 0-3-1-4-2-5-5-6-9 0-2 0-3 1-5l1-1v1h0l1-1c1-2 2-3 2-4v-1h0c1 0 1 1 2 1h1c4 3 6 4 10 5h0-4-1c0 2 3 5 5 6h1l2 2 1 1-13 6z"></path><defs><linearGradient id="Aw" x1="698.369" y1="306.628" x2="739.042" y2="260.34" xlink:href="#B"><stop offset="0" stop-color="#515051"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#Aw)" d="M722 275c-1-5-6-9-8-15 1-2 1-3 3-4 1 0 6 7 7 8 2 2 4 4 7 6 3-2 7-5 10-8 3 2 4 6 6 10v-1h2c-12 13-25 26-41 35-3 2-7 5-11 5h1c0-2 0-3 1-5 5-8 7-16 9-25v-9-4c1 0 1 0 2 1h1l1 1 1-2 4 1c2 2 3 4 5 6z"></path><defs><linearGradient id="Ax" x1="707.844" y1="268.832" x2="710.97" y2="275.258" xlink:href="#B"><stop offset="0" stop-color="#454445"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#Ax)" d="M708 272v-4c1 0 1 0 2 1h1l1 1 1 9v1c-1-1-1-1-1-2l-2-2h-1 0c0 2 0 4-1 5v-9z"></path><path d="M712 270l1-2 4 1c2 2 3 4 5 6l1 1c0 1 1 1 2 2h1v1c-5 3-8 6-12 10l-1-10h0l-1-9z" class="H"></path><defs><linearGradient id="Ay" x1="788.922" y1="275.374" x2="802.168" y2="253.085" xlink:href="#B"><stop offset="0" stop-color="#a3a1a2"></stop><stop offset="1" stop-color="#c0bebf"></stop></linearGradient></defs><path fill="url(#Ay)" d="M780 231l3 2c2 0 2 0 3-1l3 6h2v1c1 0 1 1 2 1 3-1 10 1 12 3 3 3 3 7 3 11v2c2 4 3 9 2 14h0c-1 2-3 5-5 6v1l-3-1c-2 1-5 2-8 3l-1-1-2-2h-1c-2-1-5-4-5-6h1 4 0c-4-1-6-2-10-5l-3-3v-2c-1 2-2 3-3 4v-4l-1 1v1c-1-2-1-2-3-2h0c-2 0-2 1-4 2l1-3 3-6v-1c3-4 5-9 8-13l4-4-1-1c-7-2-11-2-18-1l2-1c1-1 2-1 3-1 3 0 9 1 12 0z"></path><path d="M804 252l4 4c2 4 3 9 2 14h0c-1 2-3 5-5 6v1l-3-1c3-3 5-5 6-9 1-6-2-10-5-14l1-1z" class="J"></path><defs><linearGradient id="Az" x1="796.418" y1="243.294" x2="804.477" y2="251.985" xlink:href="#B"><stop offset="0" stop-color="#575657"></stop><stop offset="1" stop-color="#717172"></stop></linearGradient></defs><path fill="url(#Az)" d="M791 238v1c1 0 1 1 2 1 3-1 10 1 12 3 3 3 3 7 3 11v2l-4-4-1 1c-3-2-5-3-9-3-2 0-4 1-5 3-2 3-4 7-7 10h-1l3-6c4-6 6-12 5-19h2z"></path><g class="K"><path d="M791 238v1 2h1c2 0 4 1 6 2-1 1-1 1-2 1-1-1-2 0-3 0-1 2-2 2-2 4 2 1 5 0 7 1 2 0 4 2 5 2l1 1-1 1c-3-2-5-3-9-3-2 0-4 1-5 3-2 3-4 7-7 10h-1l3-6c4-6 6-12 5-19h2z"></path><path d="M780 231l3 2c2 0 2 0 3-1l3 6c1 7-1 13-5 19l-3 6c1 2 3 4 5 4 2 1 3 1 4 0 2-1 3-3 4-5 1-1 1-2 1-3h1c1 2 1 2 1 4-1 3-3 4-6 6l-1 1h0c-4-1-6-2-10-5l-3-3v-2c-1 2-2 3-3 4v-4l-1 1v1c-1-2-1-2-3-2h0c-2 0-2 1-4 2l1-3 3-6v-1c3-4 5-9 8-13l4-4-1-1c-7-2-11-2-18-1l2-1c1-1 2-1 3-1 3 0 9 1 12 0z"></path></g><path d="M784 238h0c0 2 0 3-1 5-1 4-1 7-2 11-1 2-3 5-4 8v-2c-1 2-2 3-3 4v-4c1-8 5-16 10-22z" class="X"></path><path d="M786 232l3 6c1 7-1 13-5 19l-3 6-1-1c-1-3 2-8 3-12 2-3 3-7 3-10s-1-5-3-7c2 0 2 0 3-1z" class="C"></path><path d="M782 235h1v1c0 1 1 1 1 2-5 6-9 14-10 22l-1 1v1c-1-2-1-2-3-2h0c-2 0-2 1-4 2l1-3 3-6v-1c3-4 5-9 8-13l4-4z"></path><path d="M770 252h2c-1 2-1 5-2 8h0c-2 0-2 1-4 2l1-3 3-6v-1z" class="O"></path><path d="M799 281l5-2c1-1 2-1 3-2h1l2-1h2l1-1c1 1 2 0 3 1 1 0 2 2 3 2l1 1 2 6c-2 2-4 3-7 4l-12 10-14 14c-3 1-5 4-7 6-3 2-7 5-11 7 1-3 2-6 4-8v-2c-1-1-1-1-1-2 1 0 1-1 1-1l1-1-4 1c-3-1-6-2-8-4l-1-1-1 1-1-1c1-3 3-8 6-11 4-5 8-9 14-12l13-6c3-1 6-2 8-3l3 1h-1c-3 2-7 3-10 5l-2 1c1 0 7-1 7-2z" class="V"></path><path d="M764 309c1 0 2 0 3 1h1 1c1 1 2 1 4 1l1-1h0 1 2v1l-1 1-4 1c-3-1-6-2-8-4z"></path><path d="M799 281l5-2c1-1 2-1 3-2h1l2-1h2l1-1c1 1 2 0 3 1 1 0 2 2 3 2l1 1 2 6c-2 2-4 3-7 4l-12 10-14 14c-3 1-5 4-7 6-3 2-7 5-11 7 1-3 2-6 4-8v-2c-1-1-1-1-1-2 1 0 1-1 1-1l1-1 1-1c1-1 1-1 1-2 2-2 1-6 0-9v-3c2 0 3-1 5-1h1c0-1 1-1 2-1 3 0 7-2 10-3 4-1 8-1 12-2s8-3 10-7c0-1 0-2-1-3-5-1-12 1-18 1z" class="D"></path><path d="M691 378c10-3 22-4 33-5-2 1-4 3-4 5 0 1 1 2 2 2h3c5 2 14-2 18-4h1c2 0 4 0 6-1-1 1-1 1-2 1h-1l-1 1c-1 0-2 1-3 1-2 1-3 1-4 2-3 0-5 1-7 2l-1 1h-1 0l2 2c-19 5-40 9-55 23-6 5-10 11-13 17-8 13-13 28-18 42l-15 47c-2 8-4 16-7 24v-1-2c0-1 0-1 1-2 0-2-1 1 0-1v-1h1l-1-1 1-1v-1l1-1-1-1h1v-3l1-1h0v-1c1-2 1-4 2-5v-1c0-2 1-2 1-3v-1-1c1-1 1-2 2-4h0v-1c1-1 1-2 1-3v-1c1-1 1-1 1-2s0-1 1-1l-1-1 1-1c-1 0-1 0-2 1-1 0-1 1-2 1l-1 1c-1 1-2 1-3 0-2 6-4 13-6 19l-4 15c-1 2-1 4-3 6l5-17c1-2 2-6 3-8l5-16 17-49c6-14 11-28 21-40 8-11 20-19 33-22 5-1 10-2 15-2-5-2-10-3-15-3-2 0-6-3-8-4z" class="C"></path><path d="M701 252c8-12 22-22 36-25 12-3 32-3 43 4-3 1-9 0-12 0-1 0-2 0-3 1l-2 1c-4 0-8 1-10 4-1 1-1 1 0 3v4c0 2-3 6-4 8-2 3-5 7-8 10s-7 6-10 8c-3-2-5-4-7-6-1-1-6-8-7-8-2 1-2 2-3 4 2 6 7 10 8 15-2-2-3-4-5-6l-4-1-1 2-1-1h-1c-1-1-1-1-2-1v4c-2-4-4-6-7-9-3 0-7 1-9 2 2-4 5-9 9-13z" class="S"></path><path d="M719 251c-2-1-4-4-4-6 0-1 2-3 3-3 5-5 14-10 21-10 4 0 6 1 8 4-3 0-6 1-10 2v1c-2-1-4-1-6 0s-4 3-6 4v4c-1 0-2 1-2 2-2 1-3 1-4 2z" class="B"></path><defs><linearGradient id="BA" x1="696.91" y1="257.193" x2="710.787" y2="264.801" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#BA)" d="M701 252c2 2 3 1 5 2 2 0 10 13 11 15l-4-1-1 2-1-1h-1c-1-1-1-1-2-1v4c-2-4-4-6-7-9-3 0-7 1-9 2 2-4 5-9 9-13z"></path><defs><linearGradient id="BB" x1="724.69" y1="253.152" x2="750.106" y2="249.303" xlink:href="#B"><stop offset="0" stop-color="#a1a0a1"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#BB)" d="M747 236c2 2 3 4 3 7-1 7-10 14-15 18-1 1-4 2-5 2-2-1-3-2-5-3l-6-9c1-1 2-1 4-2 0-1 1-2 2-2v-4c2-1 4-3 6-4s4-1 6 0v-1c4-1 7-2 10-2z"></path><path d="M725 247v-4c2-1 4-3 6-4s4-1 6 0c1 2 2 4 2 6 0 3-2 5-4 7-1 0-2 1-4 1s-4-2-5-4l-1-2z" class="K"></path><path d="M732 242h1c1 0 2 1 3 1 1 2 1 2 1 3-1 2-2 3-3 4h-1c-1 0-2 0-3-1s-1-2-1-3c0-2 2-3 3-4z" class="P"></path><path d="M735 212l1-1c2 0 3 0 4-1l1-2h0c2 1 4 3 6 5h1l2 1c1 1 1 1 2 1l11 4c1 0 2 0 3 1 2 1 5 3 8 3l6 3 1-1 5 7c-1 1-1 1-3 1l-3-2c-11-7-31-7-43-4-14 3-28 13-36 25-4 4-7 9-9 13-6 9-9 20-13 29l-1 1v-1l1-1v-2c1-2 1-2 1-3l1-1v-1l1-1v-1l1-1v-1-1l1-1h0v-2l2-2v-2c1-1 1-1 1-2v-1h1c0-1 0-2 1-2v-1-1h1v-1-1h-1l-2-2c-1-1 0-1-1-1l-5 12h-1c-3 4-4 10-6 14v-1c-1-2 0-4 1-6-1-3-1-7-3-10v1c-1 1-1 2-1 4v-2l-1 1h0c-2-7-4-14-10-19v2h0l-1-1c-1 2-1 2-1 4l1 2v3c0-1-1-1-1-2-3-4-6-9-9-13-2-1-4-3-6-4v-1c1-2 4-6 6-7l6-4c3-2 6-4 9-7v3l-1 1-1 1 1 1c0 3 2 7 3 9h0l1-1c0-2-1-3-2-4l1-1v-1l-1-1c0-3 1-4 3-6 3-4 10-8 15-9s8-5 13-7c2-1 5-1 7-2 3 0 6-2 10-2s11 0 16 2l6 1z" class="I"></path><path d="M653 247c-1-1-1-2-1-3v-1c-1-1-1-1-3-2l1-1c1 0 2 1 4 1l3 3-3-1c0 2 1 3 1 4h-2z" class="R"></path><path d="M674 231h1v2h0c0 5-3 9-5 13h-1c0-4-1-9 2-12l3-3z" class="G"></path><path d="M655 247c0-1-1-2-1-4l3 1c2 2 7 7 10 8 0-1 0-1 1-2 1 1 1 4 3 5h1c1-2 1-6 2-8v-1c0 7 0 15-1 23h-1v2 1c-1 1-1 2-1 4v-2l-1 1h0c-2-7-4-14-10-19-2-3-5-5-7-9h2z" class="U"></path><path d="M653 247h2l1 1v-1c4 2 8 6 10 9l1 1c2 4 4 8 5 12v2 1c-1 1-1 2-1 4v-2l-1 1h0c-2-7-4-14-10-19-2-3-5-5-7-9z" class="S"></path><path d="M664 228v3l-1 1-1 1 1 1c0 3 2 7 3 9h0l1-1c0-2-1-3-2-4l1-1v-1l-1-1c0-3 1-4 3-6 3-4 10-8 15-9s8-5 13-7c2-1 5-1 7-2 3 0 6-2 10-2s11 0 16 2l6 1 1 1c-1 0-3 0-4 1v1h-3c-1-1-2-1-4-1-2-1-7 0-10-1-2 0-6-1-7 0-4 2-7 1-11 3v1c-4 1-6 2-9 3-3 2-7 3-10 4-1 1-3 3-5 4s-5 5-6 8v2 1 11c-1-5-3-12-6-16h0v2c2 4 4 9 5 13-4-3-9-8-13-10h-4l6-4c3-2 6-4 9-7z" class="O"></path><defs><linearGradient id="BC" x1="691.547" y1="265.014" x2="686.296" y2="227.877" xlink:href="#B"><stop offset="0" stop-color="#505051"></stop><stop offset="1" stop-color="#9b999a"></stop></linearGradient></defs><path fill="url(#BC)" d="M695 220l4-1c1-1 2-1 3-1 2 0 4-1 7 0h5c1 0 1 0 2 1v1c-3 2-6 4-9 7-6 5-13 13-17 21-3 4-5 9-7 13-3 7-6 13-8 20-1-3-1-7-3-10v-2h1c1-8 1-16 1-23v-2c0-4 5-4 7-6l-3-2-1-1c-1-2-2-3-1-5 0-2 3-4 5-5s3-1 5-2l1-1h2c0-1 1-1 2-2h1 0c1 0 2 0 2-1h1v1z"></path><path d="M683 226c2 1 3 2 3 4 0 1 0 4-1 5l-2 2c-1 0-2 0-2 1l-3-2h3c1 0 1-1 2-2s1-2 1-3c0-2 0-3-1-4v-1z" class="J"></path><path d="M683 237l1 1c-2 2-5 5-8 7v2l-1-3h-1c0-4 5-4 7-6 0-1 1-1 2-1z" class="H"></path><path d="M681 225l2 1v1c1 1 1 2 1 4 0 1 0 2-1 3s-1 2-2 2h-3l-1-1c-1-2-2-3-1-5 0-2 3-4 5-5z" class="R"></path><path d="M681 225l2 1v1c-2 0-3 0-4 1s-1 2-1 4c0 1 1 1 2 2h3c-1 1-1 2-2 2h-3l-1-1c-1-2-2-3-1-5 0-2 3-4 5-5z" class="B"></path><path d="M695 220l4-1c1-1 2-1 3-1 2 0 4-1 7 0h5c1 0 1 0 2 1-2 1-3 2-5 2s-4 0-6 1h-1c-2 1-5 1-7 2-2 0-7 2-9 1 1-2 5-3 7-5h0z" class="E"></path><path d="M716 219v1c-3 2-6 4-9 7-6 5-13 13-17 21-3 4-5 9-7 13-3 7-6 13-8 20-1-3-1-7-3-10v-2h1c1-8 1-16 1-23v-2h1l1 3v1 7c1 4 1 7 1 11v5c3-5 4-10 6-15 5-13 16-27 28-35 2 0 3-1 5-2z" class="J"></path><path d="M676 248v7c1 4 1 7 1 11v5l-1 4h-1v-3-8c0-4 1-9 1-12v-4z" class="W"></path><path d="M735 212l1-1c2 0 3 0 4-1l1-2h0c2 1 4 3 6 5h1l2 1c1 1 1 1 2 1l11 4c1 0 2 0 3 1 2 1 5 3 8 3l6 3 1-1 5 7c-1 1-1 1-3 1l-3-2c-11-7-31-7-43-4-14 3-28 13-36 25-4 4-7 9-9 13-6 9-9 20-13 29l-1 1v-1l1-1v-2c1-2 1-2 1-3l1-1v-1l1-1v-1l1-1v-1-1l1-1h0v-2l2-2v-2c1-1 1-1 1-2v-1h1c0-1 0-2 1-2v-1-1h1v-1-1h-1l-2-2c-1-1 0-1-1-1l-5 12h-1c-3 4-4 10-6 14v-1c-1-2 0-4 1-6 2-7 5-13 8-20 2-4 4-9 7-13 4-8 11-16 17-21 3-3 6-5 9-7v-1c-1-1-1-1-2-1l-2-1c-2-1-7-1-9 0h-6v-1c4-2 7-1 11-3 1-1 5 0 7 0 3 1 8 0 10 1 2 0 3 0 4 1h3v-1c1-1 3-1 4-1l-1-1z" class="M"></path><path d="M735 212l1-1c2 0 3 0 4-1l1-2h0c2 1 4 3 6 5h1l2 1c1 1 1 1 2 1l11 4c1 0 2 0 3 1 2 1 5 3 8 3l6 3c1 0 1 1 1 2h0c-1-1-3-2-5-3-7-3-16-3-24-6-5-1-10-4-15-6h-1l-1-1z" class="F"></path><defs><linearGradient id="BD" x1="685.66" y1="257.242" x2="719.41" y2="212.743" xlink:href="#B"><stop offset="0" stop-color="#949292"></stop><stop offset="1" stop-color="#d0cecf"></stop></linearGradient></defs><path fill="url(#BD)" d="M697 217v-1c4-2 7-1 11-3 1-1 5 0 7 0 3 1 8 0 10 1 2 0 3 0 4 1h3l2 2-2 1-3 1-6 3c-21 10-35 31-43 52-3 4-4 10-6 14v-1c-1-2 0-4 1-6 2-7 5-13 8-20 2-4 4-9 7-13 4-8 11-16 17-21 3-3 6-5 9-7v-1c-1-1-1-1-2-1l-2-1c-2-1-7-1-9 0h-6z"></path><path d="M729 219l-1-1c-4 1-7 1-11 3l4-5c3-1 5-1 8 0 2 0 3 0 3 2l-3 1z" class="E"></path><path d="M536 146c2 0 2 1 4 3 0 1 1 2 3 3 2-1 5-1 7-1h16 65 39c7 0 15-1 22 0v1c1 3-1 5-2 7 0 2 0 2-1 4l-2 3-1 1v1 1c1 1 1 2 1 3-1 1-2 3-3 4v2c1 0 0 0 1-1v1 1h0c2-3 4-6 7-9l1 4 3 5v-1-2l-1-1c0-2-1-5 1-7h1l2 2c2 4 7 8 11 11 1 0 4 2 5 2s2 1 2 2c2 0 3 1 4 2l3 1h0 1l1-1 3 2v1h0l-9 6-9 6c-4 2-8 3-11 5-4 2-8 5-12 7s-8 3-12 5l-12 9c-3 3-6 5-9 7l-6 4c-2 1-5 5-6 7v1c2 1 4 3 6 4 3 4 6 9 9 13 0 1 1 1 1 2v-3l-1-2c0-2 0-2 1-4l1 1h0v-2c6 5 8 12 10 19h0l1-1v2c0-2 0-3 1-4v-1c2 3 2 7 3 10-1 2-2 4-1 6v1c2-4 3-10 6-14h1l5-12c1 0 0 0 1 1l2 2h1v1 1h-1v1 1c-1 0-1 1-1 2h-1v1c0 1 0 1-1 2v2l-2 2v2h0l-1 1v1 1l-1 1v1l-1 1v1l-1 1c0 1 0 1-1 3v2l-1 1v1l1-1-62 228c1 0 1 0 2-1h0v1h1l-5 17-20 62c0 1 0 2-1 3l-4 13-1 2-2 9-2 5-2 8c-1 1-1 2-1 3l-71 232-54-184-4-15c0-1-1-2-1-3 1-2-1-6-2-8l-6-22-22-76-37-136-8-32-11-53-18-55c-2-5-5-13-8-18v-1c-8-19-23-37-41-44-14-5-33-6-47 0l-2 1h-2c0-1 0-1 1-2 1-2 3-3 5-4h0l1-1h2c3-1-2 0 2-1h1c1 0 1 0 2-1 1 0 2-1 3-1 1-1 3-2 4-3 3-1 7-2 11-2l8-5c1 0 1 0 2-1v-2l1-1c-1-1-1-2-1-3h0l1-1c-2-1-2-1-4-1v-1l1-1v-2-1c0-1 0 0 1-1l-1-1-1 1c-1 1-3 2-4 2h-1c2-1 4-2 5-4 1 0 3 0 4 1 1 0 2 1 3 2v-1l-1-1 1-1c2 1 2 2 3 3h0l-1-2h1 0 1 0c2 0 3 0 4 1l1 1v-1h1l3 1c-3-2-6-4-8-6 3 0 7 5 11 6l1-1c1 0 1 1 2 1h2 0c3 0 4 1 7 1l-1-1h0c1 0 2 0 3 1s3 1 4 2 1 1 3 2l1 1 4 3c1 0-1-1 1 0 3 2 11 2 14 1h0c2 0 3 0 5-1h0l1-1h0v-1c1-1 2-2 2-4v-1c1-5-2-8-5-12-5-5-8-11-13-17-1-2-3-4-4-6 0-2-1-3-1-4s-1-2-1-3c0-2 0-3 1-4 0-1 0-1 1-1 3-1 6 0 10 0h20 85 21c4 0 8-1 11 0h2 2c0 1-2 5-2 5l-3 5h0-1c-2 1-5 2-8 3-1 0-3 1-3 1-1 0-2-1-2-1-1 0-3 1-4 2l3 1c-1 1-3 1-5 1-1 1-2 0-3 0-1 1-3 1-4 2-9 1-18 7-23 14-3 4-4 8-6 13h2c0 1 0 0-1 1v1l-1 1v1h0v1l-1 1h0v2c-1 1-1 1 0 2-4 16-5 30-3 46 3 18 10 36 14 54l26 96 40 143 8 26c1 4 2 9 4 13h1l7-22h0c0-2 1-4 1-6l4-12 10-38 13-50 34-127 11-44c4-13 8-26 9-39 1-6 1-13 1-19-2-15-5-26-13-39-7-9-14-13-24-17-8-2-15-4-23-5l-2-5-1-2-2-4-5-10z" class="T"></path><path d="M444 156h6l-6 3c-1-1-2-1-3-2l3-1z" class="H"></path><path d="M307 205l10 5h-5c-3-1-4 0-6-2h2v-1l-2-1-1-1h2z" class="J"></path><path d="M623 156l58 1c-5 1-12 3-17 3 2-1 4-1 6-2h1 1v-1h-3c-15-2-31 0-46-1z" class="G"></path><path d="M389 223l-4-12c-1-3-1-6-2-8s-4-4-6-6l-12-14v-1c5 4 10 8 14 13 2 2 4 4 5 7 2 4 2 8 3 11 1 4 2 7 3 10 0 1 0 2-1 3l-1 1v-2l1-1v-1z" class="U"></path><path d="M363 263c1-1 2-1 3-2s2-2 3-2v1h0c3-2 6-4 9-7 0 2 0 3-1 4l-1 1v-1l1-1h0c-1 0-2 1-3 2h1 0c-2 1-3 2-4 4v-1l2-2c-1 0-2 1-3 2l-1 1c-1 2-3 3-4 5h0c-1 1-1 2-2 3v2h0v-3-1c0-1 0-1 1-2-1 0-1 0-2 1h0l-1 4c-1 1-1 3-1 4h0c0 2-1 2-1 3v3c-1 2 0 3-1 5v8 1c-1 0-2-1-2-1v-2l-2-2v-2c-1-1-1-1-1-2-1-2-1-3-2-5l1-2c2 5 4 9 5 14 1-11 1-20 6-30z" class="C"></path><path d="M645 234c2-1 3-2 3-3-2-3-7-4-10-6-1-2 6-22 8-25 1-2 2-4 3-5 2-3 5-4 7-6 3-2 6-7 9-8-1 2-2 3-4 5-5 5-12 11-15 17-2 4-3 8-4 13-1 2-3 6-3 9l3 1c1 0 2 0 3 1h2v1c2 0 3 0 4 1 1-1 2-1 4-1-3 3-5 5-8 6h-2z" class="H"></path><path d="M521 752v-1c1-2 1-7 0-9s0-5-1-7v-1-1c-1-1-1-1-1-2v-1-1-1c-1-1-1-1-1-2v-1c-1-1 0-4 0-6-1-1-1-2-1-3l1-1c1 1 0 2 1 4 0 1 0 1 1 2v3c0 1 0 3 1 4 0 1-1 3 0 4 0 1 0 2 1 3v2c1 1 0 2 1 3v2c0 1 1 2 1 3 1 2 0 4 2 6v1l1 1c1 3 0 7-2 10h0c0-1-1-2-1-4 1-3 0-5 0-8l-2 5c0-1 0 0-1-1v1c0 1 0 2-1 2 0 2 0 2-1 3v-1h0l1-2c0-2 0-3 1-5v-1z" class="C"></path><path d="M594 166v-1c-1-1-1-2-2-2-2-1-3-3-5-4 3 0 5 0 7 1h-1c1 2 5 4 7 6 8 5 15 14 19 23-1 0-1-2-2-3v2h-1c-1-3-3-6-6-7-4-6-9-11-16-15z" class="R"></path><path d="M290 209c2 1 4 1 6 1l6 1-9 2c-1 0-3 1-3 1-3 3-6 3-10 5-8 2-17 3-26 5h0l1-1h2c3-1-2 0 2-1h1c1 0 1 0 2-1 1 0 2-1 3-1 1-1 3-2 4-3 3-1 7-2 11-2l8-5c1 0 1 0 2-1z" class="C"></path><path d="M290 209c2 1 4 1 6 1l6 1-9 2c-1 0-3 1-3 1h-4c-2 1-5 1-7 2-3 1-6 3-10 2l11-3 8-5c1 0 1 0 2-1z" class="I"></path><path d="M320 232c-9-6-16-12-26-15 5-3 14-4 21-4v1c-5 1-12 1-16 4 0 0 1 0 2 1h1c1 1 1 1 3 1h1c-1 0-2-1-2-1 1 0 2 1 4 1 0 1 0 1 1 1v-1h1 1v-1h1l13 11-1 4h-1l-3-2h0z" class="C"></path><defs><linearGradient id="BE" x1="341.709" y1="247.828" x2="330.246" y2="248.173" xlink:href="#B"><stop offset="0" stop-color="#717170"></stop><stop offset="1" stop-color="#918f92"></stop></linearGradient></defs><path fill="url(#BE)" d="M325 230l5 6c8 10 13 22 19 34l3 9-1 2c-1-3-2-6-4-9-3-9-8-18-13-25-3-4-7-7-10-11h0c-2-1-3-2-4-4h0l3 2h1l1-4z"></path><path d="M325 230l5 6-1 2-1 1v-1c-1-1-2-1-3-2h-1c-2-1-3-2-4-4h0l3 2h1l1-4z" class="D"></path><path d="M643 224l-1-1c-1-1-1-2 0-3v-1c0-1 0-1 1-2l3-6 3-5 5-7c1-2 1-3 3-4v1h0c0 1 0 1-1 2l-1 2c-1 1-2 2-3 4v1l-4 6-1 2-1 1v1s0 1-1 2l-1 1v2 3h3v-1-1c0-1 0-1 1-2h0v-2l2-5h1v-2h0c1-3 3-9 5-10l1-1v3h1c0 2-1 4-1 6h0c0 2-1 6-1 8v1c0 1 0 1-1 2v1c0 1-1 2-1 3l-1 1c0 1-1 1-1 2h-2-3v-1c-2 0-2-1-4-1z" class="Q"></path><path d="M656 200l1-1v3h1c0 2-1 4-1 6h0c0 2-1 6-1 8v1c0 1 0 1-1 2v1c0 1-1 2-1 3l-1 1c0 1-1 1-1 2h-2l2-4c2-4 3-10 3-14h-1l-2 6c0 1-1 2-1 2l-1 3c-1 1-1 3-2 5v-1c0-2 0-4 1-6l1-2 1-1v-1c1-5 3-9 5-13z" class="G"></path><path d="M513 713c0 1 1 2 1 3-1 1-1 2-1 3l-1 1v2l-8 33v1c1 1 2 2 2 4h0c-1-1-2-2-2-3h-1c0 2 1 4 2 6h1c1 2 5 6 8 7 1 0 2 0 3-1 2-2 3-4 4-7 1-2 1-4 1-6l2-5c0 3 1 5 0 8 0 2 1 3 1 4l-3 6c-1 1-2 2-2 4 0 1-1 1-2 1h-1-3l-1-1h-2c-4-2-8-7-9-11h-1v-2c-1-1 0-5 1-6h1l4-19c0-1 0-1 1-2 0-1 0-2 1-3v-2l1-2v-2l1-2v-2l1-2c0-2 0-3 1-5z" class="R"></path><path d="M287 193c1 0 3 0 4 1 1 0 2 1 3 2v-1l-1-1 1-1c2 1 2 2 3 3 2 2 4 5 6 6s3 2 4 3h-2l1 1 2 1v1h-2c2 2 3 1 6 2-3 0-8 0-10 1l-6-1c-2 0-4 0-6-1v-2l1-1c-1-1-1-2-1-3h0l1-1c-2-1-2-1-4-1v-1l1-1v-2-1c0-1 0 0 1-1l-1-1-1 1c-1 1-3 2-4 2h-1c2-1 4-2 5-4z" class="K"></path><path d="M296 210c4-1 6-2 10-2 2 2 3 1 6 2-3 0-8 0-10 1l-6-1z" class="N"></path><path d="M287 193c1 0 3 0 4 1 1 0 2 1 3 2v-1l-1-1 1-1c2 1 2 2 3 3 2 2 4 5 6 6s3 2 4 3h-2c-3-2-7-3-10-5h0c0 2-1 2 0 4h0c-2 0-2 2-4 1l1-1-1-2c-2-1-2-1-4-1v-1l1-1v-2-1c0-1 0 0 1-1l-1-1-1 1c-1 1-3 2-4 2h-1c2-1 4-2 5-4zm28 20c2-1 10-1 13 0h1 1c1 1 5 2 6 3 2 2 5 3 6 5v1h0c0 2 0 2-1 3-3 0-6-2-10-3s-9-1-14-2c-2-1-3-1-5-1h-1v1h-1-1v1c-1 0-1 0-1-1-2 0-3-1-4-1 0 0 1 1 2 1h-1c-2 0-2 0-3-1h-1c-1-1-2-1-2-1 4-3 11-3 16-4v-1z" class="G"></path><path d="M311 219h-1v-1h1s1 0 2-1c2-1 5 0 7-1l1 1c1 0 5-1 6 0 1 0 2 0 4 1h2 0c3 1 6 3 9 4 0 2 0 2-1 3-3 0-6-2-10-3s-9-1-14-2c-2-1-3-1-5-1h-1z" class="F"></path><defs><linearGradient id="BF" x1="575.383" y1="649.265" x2="564.685" y2="625.254" xlink:href="#B"><stop offset="0" stop-color="#afaeaf"></stop><stop offset="1" stop-color="#d2d0d0"></stop></linearGradient></defs><path fill="url(#BF)" d="M568 620l4-15 3-12c0 1 1 1 0 2v2 1l-1 1c0 2-1 5-1 7v1l-1 1v1h0c0 1 0 2-1 2v1 1h0l-1 2v1h0v2l-1 3-1 1v1 1h2c2-1 3-2 4-3l1-2c0-1 1-3 1-4v-1s0-1 1-1v-2l1-2v-1c0-2 1-1 1-3 1-1-1 0 1-1v-1-3h1v-1l1-1c-1 5-3 10-4 15l-8 30c-1 3-1 8-2 12-2-1-3-3-4-5-3-8 2-22 4-30z"></path><path d="M465 446l10 34 8 31c1 3 2 7 3 10 0 3-1 6 0 9 0 1 1 3 0 4l-21-78c1-2 0-8 0-10z" class="D"></path><path d="M370 199v-1-1c1 0 2 1 3 1 2 2 5 5 6 8h0c1 1 1 1 1 2l3 5c1 1 1 2 1 3 1 1 1 1 1 2 1 1 1 2 2 3 0 1 1 2 2 2v1l-1 1c-2 0-8 2-10 3v1c-4-3-5-7-6-11 1-1 1-2 1-3-1-1-1-4-1-6v-1-2c-1-2-1-3-1-5 0-1 0-1-1-2z" class="O"></path><path d="M370 199c3 2 5 4 5 8 1 3 0 7 1 11v3c0-1-1-2-1-3s0-2-1-2c-1-3 0-7-1-9v-2c0-1-1-1-1-2s0-1-1-2c0-1 0-1-1-2z" class="G"></path><path d="M371 201c1 1 1 1 1 2s1 1 1 2v2c1 2 0 6 1 9 1 0 1 1 1 2s1 2 1 3c1 1 2 1 3 2v1c3 1 4 0 7 0h1c0-1-2-4-2-5v-1c1 1 1 2 2 3 0 1 1 2 2 2v1l-1 1c-2 0-8 2-10 3v1c-4-3-5-7-6-11 1-1 1-2 1-3-1-1-1-4-1-6v-1-2c-1-2-1-3-1-5z" class="E"></path><path d="M336 152h0c3 4 6 9 9 13 7 9 15 18 20 29 1 2 2 4 2 6h0c-1-1-3-6-4-8l-2-2c1 2 1 3 2 4v3h-1v1c-1 3-2 5-4 6-1 1-1 1-2 1h0l1-1h0v-1c1-1 2-2 2-4v-1c1-5-2-8-5-12-5-5-8-11-13-17-1-2-3-4-4-6 0-2-1-3-1-4s-1-2-1-3c0-2 0-3 1-4z" class="Y"></path><path d="M336 152h0v3c1 1 1 2 2 4 0 2 3 5 4 7l13 18c3 4 5 8 7 12h0l1-2v3h-1v1c-1 3-2 5-4 6-1 1-1 1-2 1h0l1-1h0v-1c1-1 2-2 2-4v-1c1-5-2-8-5-12-5-5-8-11-13-17-1-2-3-4-4-6 0-2-1-3-1-4s-1-2-1-3c0-2 0-3 1-4z" class="R"></path><path d="M515 803l-1 1h-2c-1-1-1-2-2-3v-1l-2-5c-1-3-1-8-3-11v-1-2l-1-1 1-1h1c2 1 3 1 5 1h2 4 1 2c1-1 1-1 2-1 1 1 1 2 0 3h0l-1 3-2 8v2c-1 2-2 7-4 8z" class="F"></path><path d="M511 780h2 4 1 2c1-1 1-1 2-1 1 1 1 2 0 3h0v-1l-1 2h0c-1 0-2 1-3 2-2 1-4 1-6 1v1c-1 1-1 1-1 2-1-1-1-1-1-2-1-1-1-1-2-1-1-2-2-3-2-5 2 0 3 0 5-1z" class="O"></path><path d="M521 783l1-2v1l-1 3-2 8v2c-1 2-2 7-4 8h-1c-2-2-1-4-1-6l-1-1c0-4 0-6 2-9-1-1-1 0-2 0v-1c2 0 4 0 6-1 1-1 2-2 3-2h0z" class="I"></path><path d="M512 787v-1c2 0 4 0 6-1 1-1 2-2 3-2h0c-1 1-1 2-1 4-1 1-1 2-2 3s-2 3-4 5c0-2 1-6 0-8-1-1-1 0-2 0z" class="C"></path><defs><linearGradient id="BG" x1="593.601" y1="135.714" x2="658.128" y2="177.936" xlink:href="#B"><stop offset="0" stop-color="#8f898f"></stop><stop offset="1" stop-color="#bcbdb9"></stop></linearGradient></defs><path fill="url(#BG)" d="M594 166l-18-11 47 1c15 1 31-1 46 1h3v1h-1-1c-2 1-4 1-6 2h-70c-2-1-4-1-7-1 2 1 3 3 5 4 1 0 1 1 2 2v1z"></path><path d="M425 306c1 0 1 0 1 1v1s0 1 1 1c0 1 0 2 1 3 0 1 1 2 1 4l5 18v3l4 12 2 10 2 7s0 1 1 2v1l1 2c0 1 0 3 1 4 0 2 1 4 1 6l5 18s0 1 1 1v1c0 1 1 1 1 2s0 1 1 2v2c0 1 0 1 1 2v2c1 1 1 2 1 2 0 1 0 2 1 2v2l1 2v2l1 5s1 1 1 2l1 3v2l1 1v2l1 2v2h1v1c0 1 1 2 1 3v2c0 2 1 8 0 10l-40-150z" class="R"></path><path d="M536 146c2 0 2 1 4 3 0 1 1 2 3 3h0c6 2 13 3 20 4 11 3 23 9 31 17 6 6 10 12 14 20 2 5 4 11 6 16h-1c-3-10-9-23-17-29-11-11-26-17-41-21-4-2-9-3-14-3l-5-10z" class="D"></path><path d="M582 598c-1-1-1-1-1-2s1-3 1-4v-2h1v-1c0-1 0-2 1-3h0v-1c0-1 0-2 1-3v-1-2c1-1 1-2 1-2v-2c0-1 0-1 1-1v-1-2c1-1 1-1 1-2v-1c1-1 1-3 2-4v-2-2c1-1 1-1 1-2v-1l1-1v-1-2c1-2 1-1 1-2v-1-2c1-1 1-1 1-2h0v-1c1-1 1-1 1-2v-3c1-1 1-1 1-2v-3c-1 1-1 3-2 4v-3h1c0-2 0-3 1-4h0c0-1 0-2 1-2v-2l1-4v-1l1-2v-1-2c0-1 1-2 1-2l1-5v-2l1-1v-2l1-2v-2c1-1 1-2 1-3l1-2v-1-2l1-2 2-7v-3l1-1v-2l1-2v-1-2l1-2v-2c1-2 1-5 2-6l1-1c0-2 0-3 1-4v-2l1-5 1-2c0-2 0-3 1-4h0v-1l1-1c0-2 0-2 1-3-1 2-1 4-1 6l-1 1v2 1c0 1 0 1-1 2l-2 7v1l-1 3c0 2 0 3-1 4 0 1 0 1-1 2v3h-1v4c0 3-1 5-1 8v2c0 1 0 1-1 3v2c0 1 0 1-1 2h2l-19 70-5 20c-2 5-3 10-4 14z" class="L"></path><path d="M370 243c2-2 2-4 2-6l1 1v1 1h2l1-2 3 2c0 2 0 4-1 7 1 2 1 3 1 5l-1 1c-3 3-6 5-9 7h0v-1c-1 0-2 1-3 2s-2 1-3 2c-5 10-5 19-6 30-1-5-3-9-5-14l-3-9h1c1 2 2 5 3 6l2 1v-1c1-1 1-3 1-5 0-3 1-5 2-7 0-6 2-10 5-15l2-2 1-1h1l3-3z" class="K"></path><path d="M363 249c3-1 9-2 11-4l1-1v3c-3 0-7 4-9 6-3 3-5 7-8 11 0-6 2-10 5-15z" class="W"></path><path d="M370 243c2-2 2-4 2-6l1 1v1 1h2l1-2 3 2c0 2 0 4-1 7 1 2 1 3 1 5l-1 1c-3 3-6 5-9 7h0v-1c-1 0-2 1-3 2s-2 1-3 2l3-6 7-6c0-1 2-2 2-2v-2-3l-1 1c-2 2-8 3-11 4l2-2 1-1h1l3-3z" class="F"></path><path d="M692 152c1 3-1 5-2 7 0 2 0 2-1 4l-2 3-1 1v1 1l-2 2-2 2c-1 2-2 3-3 4l-1 1v2h-1l-2 4c-1 1-2 3-3 4l-1 2h0l-2 2c-1 2-2 3-2 4s0 2-1 3v2l-1 2v1c-1 1-1 2-1 4-1 1-1 2-1 4l-5 11h-1v2c-1 0-1 0-1 1l-1 1v1c-2 0-3 0-4 1-1-1-2-1-4-1v-1h-2c-1-1-2-1-3-1l1-2c2 0 2 1 4 1v1h3 2c0-1 1-1 1-2l1-1c0-1 1-2 1-3v-1c1-1 1-1 1-2v-1c0-2 1-6 1-8h0c0-2 1-4 1-6s0-3 1-4c1 1 0 3 1 4v-1c1-2 1-3 2-4l3-8c4-7 10-14 15-20 4-6 7-12 12-17z" class="I"></path><path d="M658 202c0-2 0-3 1-4 1 1 0 3 1 4v-1c1 3 2 8 0 11l-1 1c-1-1-1-2-1-3s0-1-1-2c0-2 1-4 1-6z" class="E"></path><path d="M692 152c1 3-1 5-2 7-3 4-6 9-9 13-5 7-12 15-15 23-1 2-3 3-3 5h-1v-3c1-3 2-5 3-8 4-7 10-14 15-20 4-6 7-12 12-17z" class="P"></path><defs><linearGradient id="BH" x1="410.079" y1="135.4" x2="379.474" y2="179.524" xlink:href="#B"><stop offset="0" stop-color="#8e8b8d"></stop><stop offset="1" stop-color="#c4c2c2"></stop></linearGradient></defs><path fill="url(#BH)" d="M412 183c5-10 14-17 22-23-5-1-10-1-15 0h-27-16c-10 0-19-2-28-3l96-1-3 1c1 1 2 1 3 2-9 5-17 9-24 18-2 2-3 5-5 8h-1l1-1 1-2 1-2 1-1 2-4h0c-2 0-3 1-4 3l-3 4v1h-1z"></path><path d="M642 302c1 0 1 1 2 0v1c1 2 2 5 1 7l-52 203-17 62c-4 16-8 32-13 48-3 6-6 13-11 18 0 2 1 3 2 5l5 11c0 2 2 4 2 7v1c-1-1-2-3-2-5l-4-9c-1-3-4-7-5-10l2-2c5-5 8-11 10-18 6-17 10-35 15-52l22-84 32-122c4-16 8-30 10-46 1-5 2-10 1-15z" class="I"></path><path d="M384 303c0 2 0 2 1 4h0l38 154 3 14 8 30 15 56 10 37 7 22c1 5 3 10 6 14 2 3 3 5 5 7-1 2-2 5-3 7l-6 13c-1 3-2 5-4 7l8-18c1-3 2-6 4-8v-1c-5-5-8-10-10-17-5-13-8-26-12-39l-19-72-20-75-13-53-6-22-6-26-5-19c-1-5-2-9-1-15z" class="F"></path><path d="M513 713v-1l1-1v2c1 0 1 0 1-1 2 2 1 3 1 5h1v-5l1-1v1 3l-1 1c0 1 0 2 1 3 0 2-1 5 0 6v1c0 1 0 1 1 2v1 1 1c0 1 0 1 1 2v1 1c1 2 0 5 1 7s1 7 0 9v1 1c-1 2-1 3-1 5l-1 2h0v1c1-1 1-1 1-3 1 0 1-1 1-2v-1c1 1 1 0 1 1 0 2 0 4-1 6-1 3-2 5-4 7-1 1-2 1-3 1-3-1-7-5-8-7h-1c-1-2-2-4-2-6h1c0 1 1 2 2 3h0c0-2-1-3-2-4v-1l8-33v-2l1-1c0-1 0-2 1-3 0-1-1-2-1-3z" class="O"></path><path d="M513 727l1-2c1 2 1 4 1 6l1 1v1 2 1 3l-1-1c-1 1 0 2 0 4v12c0 1-1 3-1 5h2 1l-1 1c0 1-1 3-3 3v-1c-4-3-3-7-3-12-1-1-1-2-1-3l1-1v-4c0-1 1-3 1-4 1-2 0-5 1-6v-3c1-1 1 0 1-2h0z" class="T"></path><path d="M513 713v-1l1-1v2c1 0 1 0 1-1 2 2 1 3 1 5h1v-5l1-1v1 3l-1 1c0 1 0 2 1 3 0 2-1 5 0 6v1c0 1 0 1 1 2v1 1 1c0 1 0 1 1 2v1 1c1 2 0 5 1 7s1 7 0 9v1-1c0-1 0-2-1-3v-1c0-2 1-5 0-7-1 1-1 1-1 2-1-1-1 0-1-1s0-1-1-2v2 1c1 2 0 5 0 7 0 3 1 7 0 10h0-1-2c0-2 1-4 1-5v-12c0-2-1-3 0-4l1 1v-3-1-2-1l-1-1c0-2 0-4-1-6l-1 2v-2c1-1 1-1 1-2v-1l1-1c0-2-1-3-1-5 0-1-1-2-1-3z" class="M"></path><path d="M413 166c0 1-1 2-2 3l-2 1c0 1 0 2-1 3-1 2-3 3-4 5l-1 1-1 1 1 1s0 1-1 1c0 1-1 2-3 2v1c-6 2-11-1-17-1-1 0-2 1-3 1l-2-2h-2c0-1-1-2-2-2-1-1-2-1-2-2l-2-1c-2-2-8-6-9-9 0-1 0 0 1-1h2 0c1 0 1 0 2-1l1 1h1l1-1h2l3-1h1c1-1 2-1 3-1h1c1-1 0-1 1-1l1 1 1-1h-1-3c-2 1-5 1-7 1v-1h4c1-1 1-1 2-1s4-1 5 0c1 0 1 1 3 1l1-1h4c1 1 1 0 2 0h4v-1h6s1 0 2 1h0 7 1 1c1 1 1 0 3 0v1l-1 1-1 1z" class="O"></path><path d="M379 170h3v1l-2 3h-1c-1-1-2-2-2-3l2-1z" class="M"></path><path d="M413 166l-2 2c-2 1-4 4-6 5-1 0-1 1-2 1-1 2-4 6-6 7l-2 2h-1c-2 1-3 0-4 0h-3c-1-1-1-1-2-1l-2-1-2-2v-1c1 2 3 2 4 2l1 1h1c2 0 2 1 3 0h1 1l-1-1h-2-1c-1-1-2-1-3-1l1-1h1c1 1 2 1 4 1h1c0-1 1-1 2-1v-1c2-1 3-2 5-4l3-5v-2h-1c-2-1-3-1-5-2h1 8 6 1c1 1 1 1 2 1l-1 1z" class="L"></path><path d="M613 165l-1-1-3-2h1l2 1h2 1c2 0 6 0 7 1 1 0 3 0 4-1h1c4 1 8 1 13 0h5c1 1 1 1 3 1l-1-1 1-1 1 1h2c1 0 1 1 2 2h2c1 0 1 0 2 1h2l2 1c3 1 5 0 8 1 0 2-1 2-2 3v1c-3 5-11 13-17 14-2 0-5 0-8-1-4 0-9 2-13 1 0-1-1-2-1-4h-1v-2c-4-6-10-11-14-15z" class="G"></path><path d="M613 165l2 1c1-1 3 0 4 1 1 0 2 1 3 2-1 0-1 0-2 1 2 1 3 2 4 4 2 3 5 7 9 7l1 1c9 0 14-4 21-8 4-2 7-3 11-4h-1c-2 2-5 3-7 5-3 2-7 5-10 7-4 1-11 2-15 1-1 0-2 1-3 0 0 0-3-2-3-3-4-6-10-11-14-15z" class="N"></path><path d="M615 166c1-1 3 0 4 1 1 0 2 1 3 2-1 0-1 0-2 1 2 1 3 2 4 4-3-3-6-5-9-8z" class="E"></path><path d="M626 165h5c2-1 4 0 6 0h1c3 0 8 0 12 1h1l2 1 3 1v1h-2l-2 1c-2 2-3 3-5 4-1 1-2 2-3 2-2 1-2 2-4 2l-1 1c-2 1-2 1-3 1h-1 0l2 1-3 1-1-1c-4 0-7-4-9-7-1-2-2-3-4-4 1-1 1-1 2-1h1v-1c0-1 1-2 1-3h2z" class="Q"></path><path d="M622 169h1v-1c0-1 1-2 1-3h2c-1 1-2 2-2 3v1c1-1 2-1 3-1s5 0 6 1c-1 1-3 0-4 1h-1c-1 0 0 0-1 1 0 1 0 2 1 3s2 1 3 2l1 1c1 1 2 1 3 2h1 3c-2 1-2 1-3 1h-1 0l2 1-3 1-1-1c-4 0-7-4-9-7-1-2-2-3-4-4 1-1 1-1 2-1z" class="L"></path><path d="M628 174v-1-1h3v-1c3 0 5 0 8 1h1l2 1c1 0 2-1 3-2s5-1 7-1c-2 2-3 3-5 4-1 1-2 2-3 2-2 1-2 2-4 2l-1 1h-3-1c-1-1-2-1-3-2l-1-1c-1-1-2-1-3-2z" class="M"></path><defs><linearGradient id="BI" x1="612.655" y1="371.61" x2="643.69" y2="381.844" xlink:href="#B"><stop offset="0" stop-color="#a6a3a4"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#BI)" d="M642 302c1-4 2-7 2-10 1-6 0-11 2-16h0c-1-2-1-2-2-3 1-2 2-3 3-4h3c5 2 8 7 10 11 2 12-3 24-4 35l-33 127-9 36c-2 5-2 11-4 16h-2c1-1 1-1 1-2v-2c1-2 1-2 1-3v-2c0-3 1-5 1-8v-4h1v-3c1-1 1-1 1-2 1-1 1-2 1-4l1-3v-1l2-7c1-1 1-1 1-2v-1-2l1-1c0-2 0-4 1-6 0-2 1-5 1-6l8-32 12-43c6-23 14-47 15-71 0-4 1-8-2-11h-3c-1 0-2 0-2 1-1 1-1 3-1 4 0 4 0 11-2 15-1 1-2 2-2 4h0c-1 1-1 0-2 0z"></path><path d="M478 714l-3-15c-1-9-2-27 5-35 1-2 3-3 5-4 0 1 1 1 1 2-1 1-3 4-4 5-2 4-3 9-4 13-1 13 2 26 5 38l17 65c1 2 2 5 3 8 1 5 2 11 4 16l6 16 8-25c0-2 1-3 2-4 0-1 0-2 1-3 0-1 0-2 1-3l2-6 15-49c4-11 7-21 9-33 1-6 1-15-1-22h0c-2-7-6-11-9-17l3 1 2 1c3 2 6 7 6 11l1 1c2 11 1 22 0 34h0c-2 4-2 9-4 13l-13 39-5 13-5 14-10 34c-1 3-2 8-3 12-4-19-10-37-16-54l-12-40-5-19c-1-2-1-6-2-7z" class="C"></path><path d="M299 190c3 0 7 5 11 6l1-1c1 0 1 1 2 1h2 0c3 0 4 1 7 1l-1-1h0c1 0 2 0 3 1s3 1 4 2 1 1 3 2l1 1 4 3c1 0-1-1 1 0 3 2 11 2 14 1h0c2 0 3 0 5-1 1 0 1 0 2-1 2-1 3-3 4-6v-1h1v-3c-1-1-1-2-2-4l2 2c1 2 3 7 4 8h0c1 1 1 2 2 3 0 2 2 8 2 10l1 5c1 4 2 8 6 11v-1c2-1 8-3 10-3v2c-2 0-4 2-6 3-1 0-2 0-2 1 4 8 9 15 11 25h0c0 4-1 9-1 13-1-7-2-12-4-18h-5s-1 1-2 1c0-2 0-3-1-5 1-3 1-5 1-7l-3-2-8-5c-4-4-9-9-14-12-4-3-8-4-12-7-4-1-7-4-11-6s-8-4-13-6c-4-2-7-4-11-6-3-2-6-4-8-6z" class="X"></path><path d="M379 240c2 1 3 3 4 5l3 6h-5s-1 1-2 1c0-2 0-3-1-5 1-3 1-5 1-7z" class="E"></path><path d="M381 251c0-2-1-3 0-5l2-1 3 6h-5z" class="G"></path><path d="M364 218c3 1 4 3 5 5 2 2 2 4 4 6h1v1c2 2 5 4 7 7s4 6 5 10h1l1 5c1 1 1 1 1 2v1 2l1 1h-1v-1-2l-1-1v-1-1c-2-6-5-11-10-15-2-1-4-2-5-4v-1c0-2-2-3-2-5-2-3-3-5-6-8l-1-1z" class="C"></path><path d="M363 194c-1-1-1-2-2-4l2 2c1 2 3 7 4 8h0c1 1 1 2 2 3 0 2 2 8 2 10-1 2 1 9 2 11 0 2 1 3 1 5h-1c-2-2-2-4-4-6-1-2-2-4-5-5-2-1-5-5-7-7h0c-1-1-2-3-3-3-1-1-2-1-2-1l-1-1h0c2 0 3 0 5-1 1 0 1 0 2-1 2-1 3-3 4-6v-1h1v-3z" class="E"></path><path d="M363 194c-1-1-1-2-2-4l2 2c1 2 3 7 4 8h0c1 1 1 2 2 3h-1v4c0 2-1 4 0 7v4l-3-9c0-3 0-8-2-11h-1v-1h1v-3z" class="D"></path><path d="M368 218v-4c-1-3 0-5 0-7v-4h1c0 2 2 8 2 10-1 2 1 9 2 11 0 2 1 3 1 5h-1l-5-11z" class="R"></path><path d="M310 196l1-1c1 0 1 1 2 1h2 0c3 0 4 1 7 1l-1-1h0c1 0 2 0 3 1s3 1 4 2 1 1 3 2l1 1 4 3c1 0-1-1 1 0 3 2 11 2 14 1l1 1s1 0 2 1c1 0 2 2 3 3h0c2 2 5 6 7 7l1 1c3 3 4 5 6 8 0 2 2 3 2 5v1c-7-5-13-11-19-15-3-2-7-3-11-5-3-2-6-4-10-6l-17-8-6-3z" class="O"></path><path d="M365 219v1h-1c-3-1-6-4-7-8v-1c2 2 5 6 7 7l1 1z" class="Q"></path><path d="M310 196l1-1c1 0 1 1 2 1h2 0c3 0 4 1 7 1l-1-1h0c1 0 2 0 3 1s3 1 4 2 1 1 3 2l1 1c-6-2-10-4-16-3l-6-3z" class="E"></path><path d="M486 151h2c0 1-2 5-2 5l-9 3c-10 3-20 5-29 9l-9 6c-3 2-8 6-10 9-8 13-12 26-14 41 0 3-1 8 0 11 0 3 0 5 1 7 0 2 0 5 1 7v1c0 2 1 3 1 5v2 2c1 0 1 1 1 2s1 3 1 4v2 2c1 1 1 2 1 4l1 4c1 0 1 0 1 1v3 1l5 19 2 9 5 17 2 9 17 65 8 31c1 4 3 8 3 12 0-1-1-2-1-3v-1h-1v-2l-1-2v-2l-1-1v-2l-1-3c0-1-1-2-1-2l-1-5v-2l-1-2v-2c-1 0-1-1-1-2 0 0 0-1-1-2v-2c-1-1-1-1-1-2v-2c-1-1-1-1-1-2s-1-1-1-2v-1c-1 0-1-1-1-1l-5-18c0-2-1-4-1-6-1-1-1-3-1-4l-1-2v-1c-1-1-1-2-1-2l-2-7-2-10-4-12v-3l-5-18c0-2-1-3-1-4-1-1-1-2-1-3-1 0-1-1-1-1v-1c0-1 0-1-1-1-1-1-1-3-1-5l-3-10c-6-24-13-48-9-73 2-9 4-18 8-26 9-21 24-32 46-36 6-2 13-2 19-5h1z" class="F"></path><path d="M412 218c2 2 1 4 1 6v1c-1 3 0 7 0 10 1 6 1 12 2 18v2c1 4 2 9 3 14l6 26c1 3 3 9 3 12l1 1v1c0 2 1 3 1 5h1v2c0 1 1 1 1 2l1 5 1 4 1 2v2 2l1 1v2c0 1 0 1 1 2v2 1h1v2c0 1 0 1 1 2l1 3v2c0 1 0 1 1 2l1 3v3l1 3c0 1 1 2 1 2v3l1 1v2 1l1 2 1 3 4 16 2 8 1 4c0-1-1-1-1-2v-1c-1 0-1-1-1-1l-5-18c0-2-1-4-1-6-1-1-1-3-1-4l-1-2v-1c-1-1-1-2-1-2l-2-7-2-10-4-12v-3l-5-18c0-2-1-3-1-4-1-1-1-2-1-3-1 0-1-1-1-1v-1c0-1 0-1-1-1-1-1-1-3-1-5l-3-10c-6-24-13-48-9-73z" class="D"></path><defs><linearGradient id="BJ" x1="330.997" y1="264.259" x2="342.157" y2="224.404" xlink:href="#B"><stop offset="0" stop-color="#535254"></stop><stop offset="1" stop-color="#b1afaf"></stop></linearGradient></defs><path fill="url(#BJ)" d="M336 216l12 4c1 1 4 3 4 5 2 2 6 3 8 5h1c1 1 0 1 1 2s2 1 3 2c2 0 2 0 3-1l8 5-1 2h-2v-1-1l-1-1c0 2 0 4-2 6l-3 3h-1l-1 1-2 2c-3 5-5 9-5 15-1 2-2 4-2 7 0 2 0 4-1 5v1l-2-1c-1-1-2-4-3-6h-1c-6-12-11-24-19-34l-5-6-13-11c2 0 3 0 5 1 5 1 10 1 14 2s7 3 10 3c1-1 1-1 1-3h0v-1c-1-2-4-3-6-5z"></path><path d="M348 225h1c2 3 4 5 4 9l-3 2c-1 1-1 1-2 1-1-1-2-1-3-1 0-1-1-1-1-1-1 0-1-1-1-2-1 0-1 0-1-1v-4c2-1 2-1 4-1v1h2 1c0-1-1-1-1-3z" class="R"></path><path d="M312 219c2 0 3 0 5 1 16 13 27 30 33 50h-1c-6-12-11-24-19-34l-5-6-13-11z" class="S"></path><defs><linearGradient id="BK" x1="350.687" y1="256.593" x2="358.91" y2="244.976" xlink:href="#B"><stop offset="0" stop-color="#898789"></stop><stop offset="1" stop-color="#c1bfbf"></stop></linearGradient></defs><path fill="url(#BK)" d="M350 236c1 1 4 1 4 3 1 1 2 1 3 2 1 2 1 4 4 5v-1h1c2-1 3-1 5-3 1 0 1 0 3 1l-3 3h-1l-1 1-2 2c-3 5-5 9-5 15-1 2-2 4-2 7 0 2 0 4-1 5v1l-2-1h0c-1-10-1-22 0-32h0c1-2 0-3 0-4h-2c-2 1-3 1-5 0-1-1-2-1-2-3h4c1 0 1 0 2-1z"></path><path d="M353 276h0c-1-10-1-22 0-32h0l2 21v11 1l-2-1z" class="S"></path><path d="M336 216l12 4c1 1 4 3 4 5 2 2 6 3 8 5h1c1 1 0 1 1 2s2 1 3 2c2 0 2 0 3-1l8 5-1 2h-2v-1-1l-1-1c0 2 0 4-2 6-2-1-2-1-3-1-2 2-3 2-5 3h-1v1c-3-1-3-3-4-5-1-1-2-1-3-2 0-2-3-2-4-3l3-2c0-4-2-6-4-9h-1c0-1-2-1-3-1-1-1-2-1-3-2v-1c-1-2-4-3-6-5z" class="O"></path><path d="M349 225c2 1 3 1 4 2 0 0 2 2 3 2h1l2 2 2 2c1 2 2 5 2 7 0 1 0 2-1 2v3h-1v1c-3-1-3-3-4-5-1-1-2-1-3-2 0-2-3-2-4-3l3-2c0-4-2-6-4-9z" class="L"></path><path d="M354 239c0-2 0-3 1-5h0l1-1c0-1 0-1 1 0l1 1c0 2 1 4 2 6v2c0 1 1 2 1 3v1c-3-1-3-3-4-5-1-1-2-1-3-2z" class="G"></path><path d="M696 168h1l2 2c2 4 7 8 11 11 1 0 4 2 5 2s2 1 2 2c2 0 3 1 4 2l3 1h0 1l1-1 3 2v1h0l-9 6-9 6c-4 2-8 3-11 5-4 2-8 5-12 7s-8 3-12 5l-12 9c-3 3-6 5-9 7l-6 4c-2 1-5 5-6 7-3 6-4 12-6 19-1-10 1-17 7-25v-1c0-2 1-2 0-4h0l1-1h2c3-1 5-3 8-6v-1l1-1c0-1 0-1 1-1v-2h1l5-11c0-2 0-3 1-4 0-2 0-3 1-4v-1l1-2v-2c1-1 1-2 1-3s1-2 2-4l2-2h0l1-2c1-1 2-3 3-4l2-4h1v-2l1-1c1-1 2-2 3-4l2-2 2-2c1 1 1 2 1 3-1 1-2 3-3 4v2c1 0 0 0 1-1v1 1h0c2-3 4-6 7-9l1 4 3 5v-1-2l-1-1c0-2-1-5 1-7z" class="O"></path><path d="M691 203h2c-3 3-6 3-10 4-1 0-1-1-2-1-1 1-2 1-3 1-2 0-2 0-3 1-3 1-6 0-9 1-1 0-2 2-3 3 0-2 0-3 1-4 0-1 2-3 3-3h3c2 1 5 1 7 1 5 0 10-2 14-3z" class="L"></path><path d="M713 192c2-1 3-2 5-3-1-1-1-2-2-2 0-2 0-2 1-2 2 0 3 1 4 2l3 1h0 1l1-1 3 2v1h0c-3 0-6 3-8 4-3 2-7 5-11 6l2-2c2-1 3-2 5-3h-1c-1 0-2 0-3 1-3 1-9 4-12 4h-1c1-1 3-2 5-3 1 0 2-1 3-2h0l5-3z" class="I"></path><path d="M713 192c2-1 3-2 5-3-1-1-1-2-2-2 0-2 0-2 1-2 2 0 3 1 4 2l3 1-16 7h0l5-3z" class="B"></path><path d="M710 200c4-1 8-4 11-6 2-1 5-4 8-4l-9 6-9 6c-4 2-8 3-11 5-4 2-8 5-12 7s-8 3-12 5l-12 9c-3 3-6 5-9 7l-6 4c-2 1-5 5-6 7-3 6-4 12-6 19-1-10 1-17 7-25v-1c0-2 1-2 0-4h0l1-1h2c3-1 5-3 8-6v-1l1-1c0-1 0-1 1-1v-2h1v2c-1 2-2 3-2 5h3c6-5 11-10 17-13 4-2 8-3 12-5l9-6c5-2 9-4 13-6z" class="N"></path><path d="M658 223v2c-1 2-2 3-2 5h3c-2 2-4 4-6 5-3 2-7 3-9 5v-1c0-2 1-2 0-4h0l1-1h2c3-1 5-3 8-6v-1l1-1c0-1 0-1 1-1v-2h1z" class="E"></path><defs><linearGradient id="BL" x1="708.264" y1="194.657" x2="694.052" y2="186.528" xlink:href="#B"><stop offset="0" stop-color="#9f9d9e"></stop><stop offset="1" stop-color="#bdbcbc"></stop></linearGradient></defs><path fill="url(#BL)" d="M696 168h1l2 2c2 4 7 8 11 11 1 0 4 2 5 2s2 1 2 2c-1 0-1 0-1 2 1 0 1 1 2 2-2 1-3 2-5 3l-5 3h0c-1 1-2 2-3 2-2 1-4 2-5 3-2 1-5 2-7 3h-2v-1l5-4-1-1c2-1 3-2 5-4v-4l-2-3c-1-1-2-3-3-5h-1c-1-3-1-4-1-7l3 5v-1-2l-1-1c0-2-1-5 1-7z"></path><path d="M696 198c2 0 5-1 7-2h1c2-1 3-1 4-1h0c-1 1-2 2-3 2-2 1-4 2-5 3-2 1-5 2-7 3h-2v-1l5-4zm0-30h1l2 2c2 4 7 8 11 11 1 0 4 2 5 2s2 1 2 2c-1 0-1 0-1 2 1 0 1 1 2 2-2 1-3 2-5 3l-1-1c-1 1-2 1-4 2l-1-1c1-1 1-2 2-3l1 1c1 0 2 0 3-1v-1h-2c-2-1-3-2-4-4-6-4-9-9-11-16z" class="H"></path><path d="M686 169c1 1 1 2 1 3-1 1-2 3-3 4v2c1 0 0 0 1-1v1 1h0c2-3 4-6 7-9l1 4c0 3 0 4 1 7h1c1 2 2 4 3 5l2 3v4c-2 2-3 3-5 4l1 1-5 4v1c-4 1-9 3-14 3-2 0-5 0-7-1h-3c-1 0-3 2-3 3 0-2 0-3 1-4v-1l1-2v-2c1-1 1-2 1-3s1-2 2-4l2-2h0l1-2c1-1 2-3 3-4l2-4h1v-2l1-1c1-1 2-2 3-4l2-2 2-2z" class="L"></path><path d="M672 197l2-2 6 2-1 4c-1 0-3 0-4-1-2-1-2-2-3-3z" class="S"></path><path d="M674 193c0-3 5-6 7-8l2 3h0l1 3c-1 1-1 1-3 1l-7 1z" class="I"></path><path d="M683 188l1 3c-1 1-1 1-3 1l-1-1c-1 0-2-1-3-1l1-1h4l1-1z" class="C"></path><path d="M698 192c1-1 1-1 2-3v4c-2 2-3 3-5 4l-8 3-8 1 1-4h1c6 1 12-1 17-5z" class="B"></path><path d="M681 185c1-2 2-4 4-4 2 2 3 4 4 7v4h-1s-1 1-2 1-1 0-1-1l-1-1-1-3h0l-2-3z" class="Q"></path><path d="M681 185c1-2 2-4 4-4 2 2 3 4 4 7h-1l-3-2c-1-1-1-1-2-1v3l-2-3z" class="G"></path><path d="M685 179c2-3 4-6 7-9l1 4c0 3 0 4 1 7h1c1 2 2 4 3 5l2 3c-1 2-1 2-2 3-1 0-1-1-1-2v-1h-3l-1 1h-2s-1 1-2 1v1-4c-1-3-2-5-4-7v-2z" class="I"></path><defs><linearGradient id="BM" x1="685.064" y1="199.55" x2="671.939" y2="187.446" xlink:href="#B"><stop offset="0" stop-color="#999798"></stop><stop offset="1" stop-color="#c5c3c4"></stop></linearGradient></defs><path fill="url(#BM)" d="M686 169c1 1 1 2 1 3-1 1-2 3-3 4v2c1 0 0 0 1-1v1 1h0v2c-2 0-3 2-4 4-2 2-7 5-7 8-1 1-2 2-2 4h0c1 1 1 2 3 3 1 1 3 1 4 1l8-1c3-1 5-2 8-3l1 1-5 4v1c-4 1-9 3-14 3-2 0-5 0-7-1h-3c-1 0-3 2-3 3 0-2 0-3 1-4v-1l1-2v-2c1-1 1-2 1-3s1-2 2-4l2-2h0l1-2c1-1 2-3 3-4l2-4h1v-2l1-1c1-1 2-2 3-4l2-2 2-2z"></path><path d="M687 200l8-3 1 1-5 4-7 2c-1 0-4 1-5 0 0-2 3-1 4-1 1-1 3-2 4-3z" class="R"></path><defs><linearGradient id="BN" x1="441.725" y1="453.326" x2="399.133" y2="467.24" xlink:href="#B"><stop offset="0" stop-color="#afaead"></stop><stop offset="1" stop-color="#dbd9da"></stop></linearGradient></defs><path fill="url(#BN)" d="M384 303h0c0-2 0-3-1-5-4-7-1-16-7-21-1 0-1-1-3 0-1 2-1 5-1 7l3 23c2 13 5 25 8 38l13 49 9 31c1 1 1 1 0 1l1 1v2l1 1 1 5c1 1 1 2 1 2l1 5 2 7 5 15 3 13 2 7 1 2 2 8 19 70 13 49c3 9 6 18 7 27 1 3 1 6 0 9h0c-1 1-2 3-3 4h-1c-2-1-2-2-2-4-2-14-7-28-11-42l-50-195-14-55c-4-15-8-30-11-46l-2-12c-1-7-4-18 1-24 1-2 5-5 8-6 1 0 2 0 4 1 1 2 0 7 0 10 0 6 2 12 2 17 1 4 1 7 0 10h0c-1-2-1-2-1-4z"></path><defs><linearGradient id="BO" x1="561.495" y1="507.406" x2="528.351" y2="494.648" xlink:href="#B"><stop offset="0" stop-color="#7e7e7e"></stop><stop offset="1" stop-color="#c2c0c1"></stop></linearGradient></defs><path fill="url(#BO)" d="M580 381c1 0 1-1 1-1l3-6 1 1-71 259-15-52-13-48c1-1 0-3 0-4-1-3 0-6 0-9 2 7 3 16 7 23v-4c-1-1-1-3-1-4 1 1 1 2 1 3l1 2v2c3 8 4 17 6 25l8 30c1 3 2 5 2 8 1 1 2 2 2 4h1v-2-1-2-1c-1-2 1-6 2-7v-2c1-3 2-6 2-9l-2-1h0 1l7-22h0c0-2 1-4 1-6l4-12 10-38v4l2-2v-1l3-4c-1-1 0-2 0-3 3-7 5-16 7-23l10-33c3-14 8-27 11-40 4-4 4-10 6-15l3-9z"></path><path d="M577 390l-11 43c-2 4-3 8-6 12 3-14 8-27 11-40 4-4 4-10 6-15z" class="C"></path><path d="M486 521c2 7 3 16 7 23l9 38h-1c-1-2-2-5-2-8h-1c0 1 1 2 1 3 1 2 0 0 0 2 0 1 1 1 1 2v1h-1l-13-48c1-1 0-3 0-4-1-3 0-6 0-9z" class="I"></path><path d="M560 445c3-4 4-8 6-12-1 4-2 8-3 11l-8 29-10 36-6 20c-1 2-2 5-2 8-1 3-2 6-3 10-1 6-4 13-6 20l-4 12-4 9c-2 5-2 9-3 14 0 3 0 6-1 8v1c-1 2-1 3-2 4v-1-2h0l-1-2v-2-1-2-1c-1-2 1-6 2-7v-2c1-3 2-6 2-9l-2-1h0 1l7-22h0c0-2 1-4 1-6l4-12 10-38v4l2-2v-1l3-4c-1-1 0-2 0-3 3-7 5-16 7-23l10-33z" class="F"></path><path d="M538 507v4l2-2v-1l3-4-10 33-6 18c0 2-2 5-3 7v2l-1-1c0-2 1-4 1-6l4-12 10-38z" class="G"></path><path d="M541 156c5 0 10 1 14 3 15 4 30 10 41 21 8 6 14 19 17 29h1c1 4 2 8 2 12 3 21-2 40-7 60l-10 40-5 19-9 35-1-1-3 6s0 1-1 1l-3 9c-2 5-2 11-6 15-3 13-8 26-11 40l-10 33c-2 7-4 16-7 23 0 1-1 2 0 3l-3 4v1l-2 2v-4l13-50 34-127 11-44c4-13 8-26 9-39 1-6 1-13 1-19-2-15-5-26-13-39-7-9-14-13-24-17-8-2-15-4-23-5l-2-5-1-2-2-4z" class="Q"></path><path d="M571 405c0-3 2-6 2-10l9-30 8-28c2-5 3-10 5-15 0 3-1 5-1 8l-1 1v1c0 1-1 2-1 4-2 6-4 13-5 20-1 3-6 19-6 20l-1 5-3 9c-2 5-2 11-6 15z" class="L"></path><path d="M544 162c2 0 4 0 7 1 2 1 5 1 8 2 4 2 8 3 12 5 3 1 6 2 9 4 6 3 13 9 16 15v1h-2l3 5 3 5h0v2c1 1 2 3 2 5 1 1 1 3 2 4 0 2 0 3 1 4v3c1 3 2 7 1 10-2-15-5-26-13-39-7-9-14-13-24-17-8-2-15-4-23-5l-2-5z" class="T"></path><path d="M569 172c3-1 9 1 13 4l3 3c4 3 7 5 8 9v1c-7-9-14-13-24-17z" class="M"></path><path d="M541 156c5 0 10 1 14 3 15 4 30 10 41 21 8 6 14 19 17 29h1c1 4 2 8 2 12 3 21-2 40-7 60l-10 40-5 19-9 35-1-1-3 6s0 1-1 1l1-5c0-1 5-17 6-20 1-7 3-14 5-20 0-2 1-3 1-4v-1l1-1c0-3 1-5 1-8 0-1 1-3 1-4l3-12 9-33 2-12v-1l1-1c1-4 2-9 2-14 1-13 0-25-3-37-2-5-3-9-6-13-5-9-14-19-23-24-10-5-22-9-33-12-2 0-3-1-5 1l-2-4z" class="E"></path><path d="M581 376h0c3-5 4-10 5-16l1-1v-2c1-2 1-4 2-5l4-14c1 1 1 1 1 2l-9 35-1-1-3 6s0 1-1 1l1-5z" class="F"></path><path d="M465 444c0-4-2-8-3-12l-8-31-17-65-2-9-5-17-2-9-5-19v-1-3c0-1 0-1-1-1l-1-4c0-2 0-3-1-4v-2-2c0-1-1-3-1-4s0-2-1-2v-2-2c0-2-1-3-1-5v-1c-1-2-1-5-1-7-1-2-1-4-1-7-1-3 0-8 0-11 2-15 6-28 14-41 2-3 7-7 10-9l9-6c9-4 19-6 29-9l9-3-3 5h0-1c-2 1-5 2-8 3-1 0-3 1-3 1-1 0-2-1-2-1-1 0-3 1-4 2l3 1c-1 1-3 1-5 1-1 1-2 0-3 0-1 1-3 1-4 2-9 1-18 7-23 14-3 4-4 8-6 13h2c0 1 0 0-1 1v1l-1 1v1h0v1l-1 1h0v2c-1 1-1 1 0 2-4 16-5 30-3 46 3 18 10 36 14 54l26 96 40 143 8 26c1 4 2 9 4 13h0l2 1c0 3-1 6-2 9v2c-1 1-3 5-2 7v1 2 1 2h-1c0-2-1-3-2-4 0-3-1-5-2-8l-8-30c-2-8-3-17-6-25v-2l-1-2c0-1 0-2-1-3 0 1 0 3 1 4v4c-4-7-5-16-7-23-1-3-2-7-3-10l-8-31-10-34v-2z" class="Q"></path><path d="M465 444c0-4-2-8-3-12l-8-31-17-65-2-9-5-17-2-9-5-19v-1-3c0-1 0-1-1-1l-1-4c0-2 0-3-1-4v-2-2c0-1-1-3-1-4s0-2-1-2v-2-2c0-2-1-3-1-5v-1c-1-2-1-5-1-7-1-2-1-4-1-7-1-3 0-8 0-11 2-15 6-28 14-41 2-3 7-7 10-9l9-6c9-4 19-6 29-9l9-3-3 5h0-1c-2 1-5 2-8 3-1 0-3 1-3 1-1 0-2-1-2-1-1 0-3 1-4 2-12 1-24 7-31 16-15 16-17 40-16 60 1 4 1 8 1 11 1 3 2 6 2 9 1 3 2 6 2 9 0 2 0-1 1 2v1c0 3 0 0 1 2v1l2 6v2l1 5 1 2v2c0 1 1 2 1 3v1 1l1 1c0 1 0 3 1 4v2 2l2 4v2 2c1 0 1 1 1 2v2l1 2v2l2 4v3 2c1 1 1 2 2 3v2l2 7c1 1 0 1 1 2v2l1 4v1l1 2v2l1 1v2s1 1 1 2v2 1l1 2v2l1 2v1l1 2v1c0 1 0 2 1 3v2l1 2 1 6v1l1 3c0 1 0 1 1 2 0 1 0 2 1 3v2 2c1 0 1 1 1 2l1 4 1 2v1c0 1 0 1 1 2l1 4v2c0 1 1 1 1 2v1c0 1 1 2 1 4 1 3 2 6 3 10v2c0 1 0 1 1 2v2h0c0 1 0 1 1 2v1 2c1 1 1 2 1 3 0 2 1 4 2 7l3 11c1 1 0 1 1 2v3c1 1 1 1 1 2v2l1 2v2c1 1 1 3 2 4v2c0 1 0 1 1 2v2c1 1 1 4 2 5v2 2l2 4v2 2l1 2 1 4v1l1 2v2c1 1 1 1 1 2s0 1 1 2v2 1l1 3 1 1v2 1l1 1v2c0 1 0 3 1 4v4c-4-7-5-16-7-23-1-3-2-7-3-10l-8-31-10-34v-2z" class="G"></path><defs><linearGradient id="BP" x1="510.801" y1="170.508" x2="532.457" y2="665.824" xlink:href="#B"><stop offset="0" stop-color="#b4b2b2"></stop><stop offset="1" stop-color="#deddde"></stop></linearGradient></defs><path fill="url(#BP)" d="M412 183h1v-1l3-4c1-2 2-3 4-3h0l-2 4-1 1-1 2-1 2-1 1h1c-7 12-11 25-13 39-1 12 1 25 3 37 6 33 15 66 24 98l33 124 26 95 11 38c2 8 4 16 7 23 2 7 6 13 11 19 1-2 2-5 2-7l7-24 18-65 45-167 32-124c5-22 8-45 0-66-2-9-6-16-11-24 3 1 5 4 6 7h1v-2c1 1 1 3 2 3 3 3 5 10 7 14 5 17 6 32 4 49-3 17-8 34-12 50l-13 48-55 205-20 73-8 26c-1 5-2 10-5 15-3-9-10-16-13-25l-19-66-48-174-27-103c-5-18-11-38-13-57-2-17 2-35 9-50 1-4 3-8 6-11z"></path><defs><linearGradient id="BQ" x1="374.899" y1="386.624" x2="695.552" y2="645.718" xlink:href="#B"><stop offset="0" stop-color="#bab8b9"></stop><stop offset="1" stop-color="#e0dfdf"></stop></linearGradient></defs><path fill="url(#BQ)" d="M660 256c6 5 8 12 10 19h0l1-1v2c0-2 0-3 1-4v-1c2 3 2 7 3 10-1 2-2 4-1 6v1c2-4 3-10 6-14h1l5-12c1 0 0 0 1 1l2 2h1v1 1h-1v1 1c-1 0-1 1-1 2h-1v1c0 1 0 1-1 2v2l-2 2v2h0l-1 1v1 1l-1 1v1l-1 1v1l-1 1c0 1 0 1-1 3v2l-1 1v1l1-1-62 228c1 0 1 0 2-1h0v1h1l-5 17-20 62c0 1 0 2-1 3l-4 13-1 2-2 9-2 5-2 8c-1 1-1 2-1 3l-71 232-54-184-4-15c0-1-1-2-1-3 1-2-1-6-2-8l-6-22-22-76-37-136-8-32-11-53h0c1 1 1 2 2 3v3c0 1 0 0 1 1v2 2h1v1 2l1 3v1 3c1 1 1 3 1 5h0c0 1 0 1 1 2h0c-1 1 0 1 0 2v2c0 1 0 0 1 1v1 2c0 1 0 2 1 3v1l1-1h0c1 0 2 0 3 1h1 1c1 1 2 1 3 1v-3-2l44 172 26 92 14 42 6 18c1 2 2 5 3 7h1c1 1 1 5 2 7l5 19 12 40c6 17 12 35 16 54 1-4 2-9 3-12l10-34 5-14 5-13 13-39c2-4 2-9 4-13h0 0 1l97-363c0-7 2-15 4-22 0-2 2-6 1-9 1-11 6-23 4-35 1 1 1 2 1 3v-2c-1-1 0-5 0-6-3-2-4-7-5-10l-3-2 1-1 2 2 1 1 1-1c0 1 1 1 1 2v-3l-1-2c0-2 0-2 1-4l1 1h0v-2z"></path><path d="M672 271c2 3 2 7 3 10-1 2-2 4-1 6v1l-2 6c0-7-1-13-2-19l1-1v2c0-2 0-3 1-4v-1z"></path><path d="M617 522c1 0 1 0 2-1h0v1h1l-5 17-20 62c-1-4 2-10 3-14l8-25c4-13 7-27 11-40z" class="K"></path><path d="M658 264c0 1 1 1 1 2v-3l-1-2c0-2 0-2 1-4l1 1 1 1c0 1 2 3 3 5 1 3 1 5 2 8s3 6 4 9l-1 2h0l-1-1v-2l-2-6s0-1-1-1c0 2 1 4 2 7h0v4c1 2 0 5 0 7v-6c0-3-1-7-2-9-1 3 0 6 0 10s-2 6-3 10c-1 1-1 3-2 5-1-1 0-1-1-1l-1 8-1 3h1c0 5-2 11-3 16-1 4-2 9-2 13-1 2-1 4-2 6 0-7 2-15 4-22 0-2 2-6 1-9 1-11 6-23 4-35 1 1 1 2 1 3v-2c-1-1 0-5 0-6-3-2-4-7-5-10l-3-2 1-1 2 2 1 1 1-1z" class="O"></path><path d="M656 265c1 2 3 3 4 6l1 1v3l1 1v6l-1 1v-2c-1-1 0-5 0-6-3-2-4-7-5-10z" class="Q"></path></svg>