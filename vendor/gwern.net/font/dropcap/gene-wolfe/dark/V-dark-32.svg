<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="120 84 802 868"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#434548}.C{fill:#282829}.D{fill:#36383b}.E{fill:#1c1c1e}.F{fill:#2e2f31}.G{fill:#37383b}.H{fill:#4c4d51}.I{fill:#232324}.J{fill:#626367}.K{fill:#3d3f42}.L{fill:#a6a5a7}.M{fill:#737476}.N{fill:#3a3b3d}.O{fill:#515255}.P{fill:#a2a1a3}.Q{fill:#2b2d31}.R{fill:#323235}.S{fill:#48494c}.T{fill:#88898b}.U{fill:#7c7d7e}.V{fill:#55575b}.W{fill:#121213}.X{fill:#969596}.Y{fill:#aeaeae}.Z{fill:#171719}.a{fill:#1d1f22}.b{fill:#5b5c60}.c{fill:#333539}.d{fill:#b7b6b6}.e{fill:#6c6e70}.f{fill:#8f8f91}.g{fill:#696a6c}.h{fill:#282a2d}.i{fill:#818386}.j{fill:#c2c0c0}.k{fill:#5f5f60}.l{fill:#c0c0c1}.m{fill:#0e0e0e}.n{fill:#c9c8c8}.o{fill:#e7e5de}.p{fill:#d7d4d4}.q{fill:#c97a2d}.r{fill:#d17e2d}.s{fill:#c06f23}.t{fill:#aa937b}.u{fill:#e29c55}.v{fill:#ad5f19}.w{fill:#e4d5c2}.x{fill:#a67d56}.y{fill:#e4b37c}.z{fill:#f3cc93}.AA{fill:#e0ccb1}.AB{fill:#9d612d}.AC{fill:#583d2a}.AD{fill:#e29140}.AE{fill:#b59a7e}.AF{fill:#d98b3d}.AG{fill:#453c34}.AH{fill:#a1866a}.AI{fill:#ebb674}.AJ{fill:#9f5a1d}.AK{fill:#6e4321}.AL{fill:#5c4f44}</style><path d="M581 224c1 0 2 1 3 2l-1-3 2 4h1v5c-2-3-3-6-5-8z" class="D"></path><path d="M448 120c1 0 2 1 3 1h0c0 1-1 2-1 3l-1 1h-1c-1 0-2 0-3-1l3-4z" class="i"></path><path d="M445 124c1 1 2 1 3 1l-2 3-2 3h-1c-1 0-2-1-3-1 2-1 3-4 5-6z" class="l"></path><path d="M896 266h3c-2 3-4 4-8 6-1-1 0-1-1-1l-1-1h1c1-1 2-1 3-3h2l1-1z" class="q"></path><path d="M435 80l1-1-1-1h0 2c2 2 4 2 6 3v5l-8-6z" class="x"></path><path d="M432 304c1-2 3-5 5-5-1 2-2 5-2 7 2 2 5 4 7 6h0c-1 0-2 0-2-1l-3-2c-1-1-5-3-6-5h1z" class="D"></path><path d="M572 279c4-3 6-5 9-8h1c-1 2-2 3-3 6l-1 1c1 0 1 1 2 1l2 2c-2 0-3-1-4-1h0-2v-2c-1 0-2 1-3 1h-1z" class="R"></path><path d="M421 342h1l2-1 1 1c-2 2-3 4-3 7h-2-1l-1 1v-1c0-3 2-5 3-7z" class="c"></path><path d="M880 146h6c-5 2-10 4-14 7v-1h-1c-2 1-3 1-4 1h-1l6-3c1-1 3-2 4-2 2 0 3-1 4-2z" class="AH"></path><path d="M911 244c1-2 2-2 5-3-2 1-3 3-4 4-2 2-2 4-3 6l-4 9c-2-2-1-2-1-5 3-3 3-5 4-9l1 1c1-1 1-2 2-3z" class="s"></path><path d="M542 479c-1-1-1-4-1-6v-1c1 2 2 3 4 5 5 0 13-3 18-4l1 1h-1c-2 1-4 1-6 2-4 1-8 2-11 3-1 0-2 0-3 2h0 0c0-1-1-1-1-2z" class="L"></path><path d="M449 375h0l3-3c1-2 2-3 4-4-2 6-6 11-8 18-1-1-1-1-1-3h0c0-2 1-2-1-4 1-1 2-3 3-4z" class="N"></path><path d="M449 375c1 3 0 6-2 8h0c0-2 1-2-1-4 1-1 2-3 3-4z" class="h"></path><path d="M469 158c2 1 4 1 7 1-3 2-6 5-9 7h-2c0-3 0-5 2-7v-1h2z" class="f"></path><path d="M441 414l14 3c-3 1-6 2-9 4l-1 1c-1-1-3-7-4-8z" class="B"></path><path d="M200 332v1c2 0 3 0 4 1 4 0 7 2 9 5 2 2 3 5 4 8v-1h-1c-1 0-1-1-2-2v-1c-1-1-3-4-5-5h0c-1 0-2-1-3-1h0l-4-3-2 1v-3z" class="AK"></path><path d="M124 251l1 3c2 3 3 6 6 9 2 3 4 4 7 6-1 0-1 1-2 1-7-4-11-11-14-18h1l1-1z" class="r"></path><path d="M447 383h0c0 2 0 2 1 3l-5 17-3-12h0 1l2 6c0-3 1-5 2-8v-1c1-2 1-3 2-5z" class="Q"></path><path d="M200 335l2-1 4 3h0c1 0 2 1 3 1h0c2 1 4 4 5 5v1c-1-1-2-2-4-2-1 0-2 1-2 2l-4-3-2-2-4-2c1 0 0 0 1-1 1 0 1 0 1-1z" class="s"></path><path d="M514 923l2 4 4-11c0 1 0 2 1 3h1l-6 16-3-7v-1c0-2 1-2 1-4z" class="r"></path><path d="M910 234c3 0 7 1 10 3h0c-2 1-3 2-4 3h1v1h-1c-3 1-4 1-5 3-1-3-1-6-2-9l1-1z" class="a"></path><path d="M442 246v-2l2-1c-2 12 2 21 8 31l-1-1c-5-3-8-12-10-17l1-10z" class="o"></path><path d="M477 148v5h1 3l-5 6c-3 0-5 0-7-1l2-2v-1c-1 1-2 1-3 2l-1 1-1-1c4-3 7-5 11-9z" class="L"></path><path d="M442 312c11 11 17 22 17 37l-1 6c0-1 0-3-1-4-1-12-4-26-12-35-1-1-2-3-3-4h0z" class="d"></path><path d="M866 153h1c1 0 2 0 4-1h1v1c-5 4-8 7-11 13h0 0l-4 1v-1c1-2 2-2 2-4l1-3s0-1 1-1c2-1 3-4 5-5z" class="t"></path><path d="M114 241l-3-2c-1 0-2-1-2-2 3-2 9-2 13-3-1 3-1 5-2 8-2 0-4-1-6-1z" class="W"></path><path d="M507 904c3 6 5 13 7 19 0 2-1 2-1 4v1l-9-22 2 1c0 1 0 3 1 4 1-1 1 0 1-1l-1-1v-1-4z" class="y"></path><path d="M147 194v1c-1 2-4 4-5 7l1 1c2 0 3 2 3 3l-9 10c0-2 1-4 2-6 0-1 1-1 1-2-1-2-2-2-3-2 3-4 6-8 10-12z" class="z"></path><path d="M903 253l1 2c0 3-1 3 1 5l-6 6h-3c-1 0-1 0-2-1 1 0 2-1 3-2v-1-4l2-4c1 1 1 1 2 1 0 0 1-1 2-1v-1z" class="r"></path><path d="M137 206c1 0 2 0 3 2 0 1-1 1-1 2-1 2-2 4-2 6l-3 4-3 5c0-2 0-4 1-6 1-1 2-3 2-5h-1c-2 2-3 5-4 7l-1-3 9-12z" class="AA"></path><defs><linearGradient id="A" x1="584.875" y1="415.557" x2="588.115" y2="425.624" xlink:href="#B"><stop offset="0" stop-color="#76777a"></stop><stop offset="1" stop-color="#939394"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M589 430c-2-3-1-7-3-10-2-2-7-2-9-2 2-1 6-3 9-3 1 0 4 1 5 2s1 1 0 2v4l-2 7z"></path><path d="M244 332c8 3 15 9 21 14h0c-1 1-1 0-2 1h0l-1 1c-6-5-13-9-21-13 4 0 6 1 10 3h0c0-3-5-3-6-5l-1-1z" class="t"></path><path d="M577 357c0 4 2 7 3 11 0 3 2 6 4 9s4 6 5 10c1 1 2 1 2 3 0 1 0 3 1 4l-1 7h-1l-1-10c-1-2-1-5-2-7-3-8-10-14-12-22v-3h1l1-2z" class="c"></path><path d="M451 102c1 0 1 1 2 1 1 1 1 3 1 4 1 1 2 1 3 1 0 2-1 3-1 4 1 1 2 3 2 4v1c-1 1-1 2-1 3h-1l-1-2-3 3h-1 0c-1 0-2-1-3-1 3-7 4-11 3-18z" class="AL"></path><path d="M455 118h0c-1-2-1-2 0-3v-1c1 1 1 2 2 2h1v1c-1 1-1 2-1 3h-1l-1-2z" class="C"></path><path d="M453 103c1 1 1 3 1 4 1 1 2 1 3 1 0 2-1 3-1 4h-2-1c-1-2 0-2 0-3v-6z" class="AK"></path><path d="M128 218l1 3c-4 8-7 19-5 28v1 1l-1 1h-1c-1-2-2-6-3-8-1-1-4-2-5-3 2 0 4 1 6 1 1-3 1-5 2-8 1-6 3-11 6-16z" class="v"></path><defs><linearGradient id="C" x1="559.482" y1="488.971" x2="546.018" y2="492.529" xlink:href="#B"><stop offset="0" stop-color="#838486"></stop><stop offset="1" stop-color="#9e9ea0"></stop></linearGradient></defs><path fill="url(#C)" d="M542 479c0 1 1 1 1 2h0 0c1-2 2-2 3-2l-2 2c2 2 6 1 8 2 6 3 10 13 13 19l-3-1h-1c-1-3-3-7-5-10-3-4-8-5-13-6l-1-6z"></path><path d="M147 194c1-1 3-2 4-3-1 1-1 2-1 3l1 1c1-1 2-1 3-1 2 1 3 1 4 3h0l-12 9c0-1-1-3-3-3l-1-1c1-3 4-5 5-7v-1z" class="y"></path><path d="M423 352c2 0 5 0 7-2 1-2-1-3 2-4h1l1 1c1 1 1 1 1 2v1c-1 1-2 1-3 2s-1 3-1 4v1c1 1 1 2 1 3-1 1-1 2-1 3-2 1-1 3-2 4v-2l-6-7v-1l-1-3h-1l1-1 1 1c1 0 1-1 2-1-1-1-1-1-2-1z" class="C"></path><path d="M433 346l1 1c1 1 1 1 1 2h-1c-1-1-2-1-2-3h1z" class="F"></path><path d="M169 169v-1c0-2 0-3-1-4-5-11-15-14-25-18h7c1 1 2 1 4 2 1 0 2 1 4 1l5 3c3 3 7 5 9 8 1 2 2 3 3 4v2l-1-1c-2 1-3 2-5 4z" class="x"></path><path d="M877 189c8 6 15 13 20 21v2c-3-2-5-5-6-7l-3 2-3-3-6-6-2-4c-1 0-1 0-2-1v-1l1-1 1-1v-1z" class="AI"></path><path d="M877 194c1 0 1 0 2 1 3 1 7 3 8 6 1 1 3 2 4 4l-3 2-3-3-6-6-2-4z" class="z"></path><path d="M885 204c0-2-1-2 0-3h2c1 1 3 2 4 4l-3 2-3-3z" class="AA"></path><path d="M467 282h1v1c-2 1-5 1-8 2-9 2-19 5-23 14h0c-2 0-4 3-5 5h-1l-2-1 3-5h0c3-6 9-10 15-12l6-2h3c1-1 3-1 4-1h1 1l1-1h4z" class="Q"></path><path d="M432 298c1 2 0 4 0 6h-1l-2-1 3-5z" class="E"></path><defs><linearGradient id="D" x1="532.289" y1="564.594" x2="549.654" y2="555.407" xlink:href="#B"><stop offset="0" stop-color="#8d8c8d"></stop><stop offset="1" stop-color="#bfbfc0"></stop></linearGradient></defs><path fill="url(#D)" d="M535 567l9-21c2 2 2 3 3 5l-4 10c-2 6-4 13-7 18-1-1 0-1 0-2-1-1-1-1-1-2l-1-5 1-3z"></path><path d="M534 570l1-3c1 3 1 5 1 7l-1 1-1-5z" class="T"></path><path d="M129 221c1-2 2-5 4-7h1c0 2-1 4-2 5-1 2-1 4-1 6l-3 10c-1 4-1 9 0 13v3 3h-3l-1-3v-1-1c-2-9 1-20 5-28z" class="o"></path><path d="M493 188l15 5h-3c-3 3-7 3-10 4l-11 4c-6 2-12 5-17 9v-1c2-5 8-9 13-10 1 0 1 0 2-1h0v-2c3 0 5-1 7-2l3-1 1-1c0-1 0 0-1-1v-1h3 0c0-1-1-1-2-2z" class="k"></path><path d="M493 188l15 5h-3c-7 0-16 2-23 5v-2c3 0 5-1 7-2l3-1 1-1c0-1 0 0-1-1v-1h3 0c0-1-1-1-2-2z" class="o"></path><path d="M572 279h1c1 0 2-1 3-1v2h2c1 1 4 2 6 4 4 2 8 5 11 8v3c0 1 1 1 1 2l-2-2v1h-1c-1-2-5-5-7-5-4-1-8-4-11-5s-6-1-8-2c-2 0-3 0-5-1h0c1-1 2-1 2-1 2-1 3-1 4-1 1-1 1-1 2-1l2-1z" class="h"></path><path d="M576 280h2c1 1 4 2 6 4 4 2 8 5 11 8v3c0 1 1 1 1 2l-2-2c-2-3-6-7-9-9s-6-3-9-6z" class="c"></path><path d="M443 81c5 4 11 8 13 14l2 3c0 1 1 3 1 4s0 2 1 3c0 1 0 2-1 2 0 2-1 3-1 4-1-1-1-2-1-3h0c-1 0-2 0-3-1 0-1 0-3-1-4-1 0-1-1-2-1-1-4-2-7-4-11l-4-5v-5z" class="q"></path><path d="M456 105v-1c1-3-1-7 0-9l2 3c0 1 1 3 1 4s0 2 1 3c0 1 0 2-1 2 0 2-1 3-1 4-1-1-1-2-1-3l-1-3z" class="d"></path><path d="M447 91h1c2 3 4 5 5 7s2 5 3 7l1 3h0c-1 0-2 0-3-1 0-1 0-3-1-4-1 0-1-1-2-1-1-4-2-7-4-11z" class="AB"></path><defs><linearGradient id="E" x1="516.871" y1="591.947" x2="538.129" y2="578.553" xlink:href="#B"><stop offset="0" stop-color="#56575a"></stop><stop offset="1" stop-color="#7a7c7e"></stop></linearGradient></defs><path fill="url(#E)" d="M534 570l1 5c0 1 0 1 1 2 0 1-1 1 0 2-3 9-12 15-16 24-1-3-1-5-1-8 0-4 0-9 1-13h1c0 1 1 2 2 2 1 1 2 1 3 0 4-2 7-11 8-14z"></path><path d="M543 561l2 1 3 3-1 1h-1c-1 2-1 4-2 6-1 1-1 3-2 5h0l-1-2v1l-1 1c-1 1-1 2-1 3l-1 4c-1 1-2 3-3 4-2 3 0 6-1 9l-1 3v-7h-1v1c0 2-5 6-7 8-1 1-1 3-2 4l-1-1h-1l-1-2c4-9 13-15 16-24 3-5 5-12 7-18z" class="Z"></path><path d="M436 359l1-1c1-1 2-2 3-2 0 1 1 1 1 1l4 3c-1 2-3 4-5 5-3 1-7 4-9 6l2 3c1 1 1 2 1 3h1l1 3 2 7h1v4c1 2 1 3 1 5-1 1 0 1-1 2h-2c-1-2-2-5-3-6v-2l-2-5h0l1-1c0-1 0-1-1-2h0c-1-1-1-2-2-4l-1-1v-1h0v-2-1c1-1 0-1 2-2v-1c-1-1-1-2 0-3v-1-2c1-2 2-3 3-5l1 1 1-1z" class="E"></path><path d="M436 359c1 1 0 2 0 4-1 1-2 1-3 1 0-1 0-2 1-3l1-1 1-1z" class="Q"></path><path d="M586 291c2 0 6 3 7 5h1c2 3 4 7 6 10l-6 4a30.44 30.44 0 0 0-8 8c-1 1-1 4-1 5-1 4-4 7-5 11-1 3-2 6-4 10l-1 2v2c1 0 1 1 2 2-1 2-1 4-1 6l1 1-1 2h-1v3c-1-2-2-5-2-7s-1-9 0-11c1-1 0-3 1-4v-1c0-2 1-3 1-5 1-4 3-8 5-11h1l2-3c3-5 9-9 14-13-1-3-4-3-5-5-2-1-2-6-3-8 0-1-2-2-3-3z" class="D"></path><path d="M586 318c-1 1-1 4-1 5-1 4-4 7-5 11-1 3-2 6-4 10l-1 2v-4-3l2-5v-1c0-1 1-2 2-4l1-2v-2c1 0 1 0 1-1l4-6h1z" class="Q"></path><path d="M429 303h0l2 1c1 2 5 4 6 5l3 2c0 1 1 1 2 1 1 1 2 3 3 4 8 9 11 23 12 35v1l-1-2-4-5c1-2-1-4-1-7-1-3-3-6-5-9-3-6-6-11-9-16h-1c-1-2-3-3-4-5-1-1-2-1-3-2l1-1-1-2z" class="E"></path><path d="M446 329c0-3-1-4-1-7 2 3 4 6 5 9 1 2 2 3 3 5s0 4 1 6c0 3 2 5 2 8l-4-5c1-2-1-4-1-7-1-3-3-6-5-9z" class="D"></path><path d="M429 303h0l2 1c1 2 5 4 6 5l3 2c0 1 1 1 2 1 1 1 2 3 3 4h-1c-3-1-4-4-7-4h0c3 3 6 6 8 10h0c0 3 1 4 1 7-3-6-6-11-9-16h-1c-1-2-3-3-4-5-1-1-2-1-3-2l1-1-1-2z" class="Q"></path><path d="M599 77h2l-1 1c-9 7-17 15-19 27v3h-1c0-1-1-1-2-1l-2 1v-3c-1-1-1-2-2-3h0-1c1-3 2-6 4-9 1 0 1-1 1-2 2-1 3-3 4-5 2-3 2-4 6-6 1 0 2 0 3-1h1 0v-2h7z" class="z"></path><path d="M576 97c1 3 1 5 0 8-1-1-1-2-2-3 1-2 2-3 2-5z" class="w"></path><path d="M592 79c2-1 4-1 5-1-1 1-2 1-3 2-2 1-4 3-5 4-5 4-11 8-13 13 0 2-1 3-2 5h0-1c1-3 2-6 4-9 1 0 1-1 1-2 2-1 3-3 4-5 2-3 2-4 6-6 1 0 2 0 3-1h1 0z" class="v"></path><defs><linearGradient id="F" x1="860.418" y1="132.507" x2="833.331" y2="160.733" xlink:href="#B"><stop offset="0" stop-color="#99938d"></stop><stop offset="1" stop-color="#bfc2cb"></stop></linearGradient></defs><path fill="url(#F)" d="M793 146h87c-1 1-2 2-4 2-1 0-3 1-4 2l-11-1h-71c1 0 2-1 3-1 2 1 4 0 7 0h5c6 1 11 1 17 1-5-4-21-2-28-2l-1-1z"></path><path d="M561 501h1l3 1v3c0 2-2 7-3 10l-9 20h0l-2-2h-1c1 1 3 4 2 4 0 3-2 7-3 9l-2 5c-1-2-1-3-3-5l12-31c2-4 5-10 5-14z" class="j"></path><path d="M594 296v-1l2 2c2 3 5 6 5 10h0c-1 1-2 2-2 4h-1c-2 2-4 4-5 6-5 6-9 14-11 21v1c2-2 2-4 4-6 1-2 3-4 4-6h1l2 2c3-1 4-2 6-3l4-2c3-1 6-2 8-3l1 1c-2 0-4 1-5 2-3 0-5 2-7 3-4 2-10 5-13 9-2 1-4 3-6 5-1 2-3 6-4 9-1-1-1-2-2-2v-2l1-2c2-4 3-7 4-10 1-4 4-7 5-11 0-1 0-4 1-5a30.44 30.44 0 0 1 8-8l6-4c-2-3-4-7-6-10z" class="E"></path><path d="M449 280s1-1 2-1h1c1-1 1-1 2-1 1 1 2 0 3 0v-1h0l4 2h0l2 1 4 2h-4l-1 1h-1-1c-1 0-3 0-4 1h-3l-6 2c-6 2-12 6-15 12h0l-3 5h0l-3-1c-1-1-3-2-4-3v-1c-1 0-5-1-6-1h-5l2-1c2 0 6 0 8 1h1 0v-1h1l1-1h1-1l1-1 1 1-2 2h0l1 1c3-3 6-6 9-10 0-1 1-1 1-2h1l2-2 5-2c1 0 2-1 3-1 1-1 1-1 3-1z" class="S"></path><path d="M441 287c1-2 0-3 3-4 0-1 2-1 3-1 1 1 2 0 3 1-3 1-6 2-9 4z" class="Q"></path><path d="M449 280s1-1 2-1h1c1-1 1-1 2-1 1 1 2 0 3 0v-1h0l4 2h0l-1 1 1 1c-1 1-4 1-5 1l-6 1c-1-1-2 0-3-1l2-2z" class="D"></path><path d="M443 282c1 0 2-1 3-1 1-1 1-1 3-1l-2 2c-1 0-3 0-3 1-3 1-2 2-3 4h0c-5 4-10 9-14 14l-5-3c-1 0-5-1-6-1h-5l2-1c2 0 6 0 8 1h1 0v-1h1l1-1h1-1l1-1 1 1-2 2h0l1 1c3-3 6-6 9-10 0-1 1-1 1-2h1l2-2 5-2z" class="a"></path><defs><linearGradient id="G" x1="178.924" y1="131.155" x2="214.328" y2="162.128" xlink:href="#B"><stop offset="0" stop-color="#847d7b"></stop><stop offset="1" stop-color="#b0b4b8"></stop></linearGradient></defs><path fill="url(#G)" d="M167 146c2-1 4 0 6 0h12 49c-2 1-3 1-5 1l-3 1v1c-5 1-12 0-17 0h-38-13c-2 0-3-1-4-1-2-1-3-1-4-2h17z"></path><path d="M150 146h17 1l-5 2h7 0l1 1h-13c-2 0-3-1-4-1-2-1-3-1-4-2z" class="X"></path><defs><linearGradient id="H" x1="449.16" y1="258.561" x2="442.898" y2="277.295" xlink:href="#B"><stop offset="0" stop-color="#10100f"></stop><stop offset="1" stop-color="#2c2d2f"></stop></linearGradient></defs><path fill="url(#H)" d="M437 264l3-8c0-2 0-4 1-5v4 1c2 5 5 14 10 17l1 1c2 0 4 2 5 3h0v1c-1 0-2 1-3 0-1 0-1 0-2 1h-1c-1 0-2 1-2 1-2 0-2 0-3 1-1 0-2 1-3 1v-2c-1 0-1 0-2-1-1 0-1 1-1 0-1 0-1-1-1-2l-4-2c0-1 1-1 0-2l-1-1v-1h1c0-1-1-1-1-2l2-4 1-1z"></path><path d="M435 273l1-2v1c1 0 1 0 2 1l2 1 4 2v1l2 1v1h-1c-1 0-1 1-2 1s-1 0-2-1c-1 0-1 1-1 0-1 0-1-1-1-2l-4-2c0-1 1-1 0-2z" class="D"></path><path d="M439 277h2l1 2 1-1v-1h1l2 1v1h-1c-1 0-1 1-2 1s-1 0-2-1c-1 0-1 1-1 0-1 0-1-1-1-2z" class="a"></path><path d="M460 96h1l1 1v1c3 3 3 7 4 10l1-3 1 2 2 3 2 4-1 1v1h-3-1-1l-1 1 1 2h-1c0 2 0 3-1 5l-1-2-2 1c0 2-1 6-1 8h0v1l-1-1c-1-1-2 0-3 0v-1l-1-2c0-3-1-5-3-7l3-3 1 2h1c0-1 0-2 1-3v-1c0-1-1-3-2-4 0-1 1-2 1-4h0c0 1 0 2 1 3 0-1 1-2 1-4 1 0 1-1 1-2-1-1-1-2-1-3s-1-3-1-4c1 0 1-1 2-2z" class="K"></path><path d="M466 108l1-3 1 2 2 3 2 4-1 1h-1l1-2h-1l-2-1-2-4z" class="J"></path><path d="M459 102c2 1 3 3 4 5s1 5 3 6c1 1 3 1 4 2h1v1h-3c-3-1-5-3-7-4-1-1-1-3-2-5 1 0 1-1 1-2-1-1-1-2-1-3z" class="Y"></path><path d="M457 108c0 1 0 2 1 3 0-1 1-2 1-4 1 2 1 4 2 5 2 1 4 3 7 4h-1-1l-1 1 1 2h-1c0 2 0 3-1 5l-1-2v-2l-2 2h-2c0-2 0-3-1-5v-1c0-1-1-3-2-4 0-1 1-2 1-4h0z" class="L"></path><path d="M459 107c1 2 1 4 2 5 1 2 1 3 1 5h-1l-1-3c-1-1-1-2-2-3 0-1 1-2 1-4z" class="j"></path><path d="M461 112c2 1 4 3 7 4h-1-1l-1 1 1 2h-1c0 2 0 3-1 5l-1-2v-2l-1-3c0-2 0-3-1-5z" class="S"></path><path d="M458 117c1 2 1 3 1 5h2l2-2v2l-2 1c0 2-1 6-1 8h0v1l-1-1c-1-1-2 0-3 0v-1l-1-2c0-3-1-5-3-7l3-3 1 2h1c0-1 0-2 1-3z" class="X"></path><path d="M458 117c1 2 1 3 1 5v5h0l-2-4s0-2-1-3h1c0-1 0-2 1-3z" class="K"></path><path d="M452 121l3-3 1 2c1 1 1 3 1 3-1 2-1 5-1 7l-1-2c0-3-1-5-3-7z" class="k"></path><defs><linearGradient id="I" x1="851.648" y1="173.712" x2="878.054" y2="186.885" xlink:href="#B"><stop offset="0" stop-color="#c59665"></stop><stop offset="1" stop-color="#f5b86e"></stop></linearGradient></defs><path fill="url(#I)" d="M857 167l4-1h0c0 5 0 9 3 12 3 4 9 8 13 11v1l-1 1-1 1v1c1 1 1 1 2 1l2 4c-1 0-2-1-2-1-3-1-4-2-7-2l-20-15-1-2c3-1 5-2 6-3l1-1-3-3h0l-1-1 5-3z"></path><path d="M548 842c1 2 0 3-1 5l-4 9c0 1-1 2-1 4-1 0-1 1-1 1v1h2 0l1-1 3-6v-1c1-2 1-3 1-4 1-1 1 0 1-2h0l1-1c0-2 1-4 2-5 0-1 1-1 2-2l-32 79h-1c-1-1-1-2-1-3v-1c1-3 5-9 4-12-1 2-1 4-3 7l-4 11-1-2 11-26 21-51z" class="q"></path><path d="M772 340c3-1 6-2 8-4 2 0 3-1 5-2 1-1 3-2 5-2h1l-1 1h-1-1l-1 1s-1 1-2 1h-1c-1 1-2 1-3 2v1c-1 0-1 0-2 1v1c-4 3-9 6-13 9-12 10-22 23-30 37l-7 14c-1 2-2 5-3 7v-1l-2 3c0 1 0 2-1 3l-1-1c-1-4 4-7 3-12h0c1-4 3-7 5-10 7-14 16-26 27-36h0c5-5 10-9 15-13z" class="X"></path><path d="M891 205c1 2 3 5 6 7v-2c5 7 10 14 12 23l1 1-1 1c1 3 1 6 2 9-1 1-1 2-2 3l-1-1c-1 4-1 6-4 9l-1-2v-5l-1 1h0l-1-1c0-2 0-5-1-7v-1c1-2-2-6-3-8v-2-3c-1-1-2-2-2-3l1-4 1 1c0-5-5-10-9-14l3-2z" class="o"></path><path d="M907 235h2c1 3 1 6 2 9-1 1-1 2-2 3l-1-1-1-11z" class="v"></path><path d="M897 210c5 7 10 14 12 23l1 1-1 1h-2c-2-8-5-17-10-23v-2z" class="AB"></path><path d="M896 220l1 1c4 9 7 17 6 27l-1 1h0l-1-1c0-2 0-5-1-7v-1c1-2-2-6-3-8v-2-3c-1-1-2-2-2-3l1-4z" class="s"></path><path d="M169 169c2-2 3-3 5-4l1 1 1 1v1c1 2 1 4 2 6h0l1 3c1 1 0 1 1 1 2 1 3 2 3 3v1l-2 1h-2c-6 3-11 7-16 11-1 1-4 3-5 3h0c-1-2-2-2-4-3-1 0-2 0-3 1l-1-1c0-1 0-2 1-3s3-2 4-3c3-3 7-5 9-8 4-3 5-6 5-11z" class="u"></path><path d="M465 166h2c2 8 13 15 20 19 1 0 6 2 6 3 1 1 2 1 2 2h0-3v1c1 1 1 0 1 1l-1 1-3 1-1-1c0-1-1-1-2-1l-6-2c-1-1-2-2-4-2-1 0-1-1-2-2-1 0-2 1-3 1l-1-3 1-2c-1 0-4 0-5 1l-1-1-2-1h-1c-1-1-1-2-2-2-3-2-7-3-10-3h-3c4-1 8-2 12-4h0l1-2c1-1 4-3 5-4z" class="L"></path><g class="Z"><path d="M472 182c6 3 14 6 20 10v1l-3 1-1-1c0-1-1-1-2-1l-6-2c-1-1-2-2-4-2-1 0-1-1-2-2-1 0-2 1-3 1l-1-3 1-2h1z"></path><path d="M460 170c4 5 7 9 12 12h-1c-1 0-4 0-5 1l-1-1-2-1h-1c-1-1-1-2-2-2-3-2-7-3-10-3h-3c4-1 8-2 12-4h0l1-2z"></path></g><path d="M460 170c4 5 7 9 12 12h-1c-1 0-4 0-5 1l-1-1h2l1-1c-2-3-6-5-7-8h-1l-1-1 1-2z" class="I"></path><path d="M526 192l11-4c1 1 1 1 3 1h5l-5 2c2 1 4 2 7 3l3 1h2c1 1 5 2 6 3v2c1 0 2 1 3 2l9 5 4 2c3 2 5 5 9 6h0l-1 1s1 2 0 2v2c0 1 1 2 1 3l1 3c-1-1-2-2-3-2-1-1-3-4-4-5-14-15-38-24-59-25l8-2z" class="S"></path><path d="M526 192l11-4c1 1 1 1 3 1h5l-5 2-1 1c-3 0-11 1-13 0z" class="L"></path><path d="M539 192l1-1c2 1 4 2 7 3l3 1h2c1 1 5 2 6 3v2c1 0 2 1 3 2-4-1-7-4-11-6-4-1-8-2-11-4z" class="X"></path><path d="M577 219c1 0 2-1 2-2-1-1-1-1-1-2s0-1-1-2v1c-2-1-3-3-4-4l1-1c3 2 5 5 9 6h0l-1 1s1 2 0 2v2c0 1 1 2 1 3l1 3c-1-1-2-2-3-2-1-1-3-4-4-5z" class="K"></path><defs><linearGradient id="J" x1="522.813" y1="913.624" x2="513.107" y2="890.633" xlink:href="#B"><stop offset="0" stop-color="#151617"></stop><stop offset="1" stop-color="#323334"></stop></linearGradient></defs><path fill="url(#J)" d="M501 880l1-2 7 13c0 1 2 3 2 4 1 2 1 3 3 4 0 1 1 1 2 2h1 1c0 1 0 1 1 1 2-4 4-8 5-12 0 1 1 2 0 3v2h0l2-2h1l-11 26 1 2 4-11c2-3 2-5 3-7 1 3-3 9-4 12v1l-4 11-2-4c-2-6-4-13-7-19 0-1-2-4-2-5l-6-15 3 1c-1-2-1-4-1-5z"></path><path d="M511 895c1 2 1 3 3 4 0 1 1 1 2 2h1 1c0 1 0 1 1 1-1 1-2 2-2 3-1 0-2 0-3-1s-3-7-3-9z" class="U"></path><path d="M501 880c2 4 5 8 7 12v2 2 3c-2-5-5-9-6-14-1-2-1-4-1-5z" class="C"></path><path d="M499 884l3 1c1 5 4 9 6 14l4 10c1 2 2 5 3 7 1 1 0 2 1 3h0l1 2 4-11c2-3 2-5 3-7 1 3-3 9-4 12v1l-4 11-2-4c-2-6-4-13-7-19 0-1-2-4-2-5l-6-15z" class="AC"></path><path d="M477 476c3 0 9 2 11 0 1 0 1-1 2-2 1 1 1 1 2 3l-1 2c0 2 0 4-1 5l-1 1c-4 1-8 1-11 5-1 1-2 3-2 5-1 1-2 2-2 3-1 4 2 9-1 11v1h0c-2-2-4-7-4-10l2-8c-1 1-1 1-1 2l-2 2-7 2c1-1 1-2 2-2 1-2 2-2 2-4 0-1 0-1-1-2 1-1 1-2 2-4 1-3-1-4-3-6l-2-4 10 3v-2l-3-1h5 1 3z" class="C"></path><path d="M477 476c3 0 9 2 11 0 1 0 1-1 2-2 1 1 1 1 2 3l-1 2c-7 1-12 0-20-2l-3-1h5 1 3zm-4 33c-1-3-3-8-3-11 1-4 3-9 7-12 3-1 6-1 8-2 2 0 3 0 5-1v1l-1 1c-4 1-8 1-11 5-1 1-2 3-2 5-1 1-2 2-2 3-1 4 2 9-1 11z" class="Y"></path><path d="M461 476l10 3 11 3h-1c-2 1-4 2-6 4h-1l-3 6c-1 1-1 1-1 2l-2 2-7 2c1-1 1-2 2-2 1-2 2-2 2-4 0-1 0-1-1-2 1-1 1-2 2-4 1-3-1-4-3-6l-2-4z" class="f"></path><defs><linearGradient id="K" x1="595.487" y1="114.705" x2="541.866" y2="119.731" xlink:href="#B"><stop offset="0" stop-color="#a5a4a5"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#K)" d="M555 158c-9-12-15-25-13-40 2-12 9-24 19-32s25-11 38-9h-7v2h0c-5 1-9 1-14 2-9 3-17 10-22 17-7 9-11 20-9 30 1 6 3 11 6 16l-1 1-1-2c0 4 8 10 6 14l-2 1z"></path><defs><linearGradient id="L" x1="466.874" y1="79.57" x2="459.026" y2="151.139" xlink:href="#B"><stop offset="0" stop-color="#ababac"></stop><stop offset="1" stop-color="#dddbdb"></stop></linearGradient></defs><path fill="url(#L)" d="M435 80l-5-3c16-2 32 0 44 11 10 7 16 18 17 30 1 13-3 25-10 35h-3-1v-5l6-12c1-2 2-6 3-9 1-10-2-20-8-28-10-15-24-18-41-21h-2 0l1 1-1 1z"></path><defs><linearGradient id="M" x1="859.517" y1="368.791" x2="870.498" y2="398.315" xlink:href="#B"><stop offset="0" stop-color="#d67c26"></stop><stop offset="1" stop-color="#ef9f4b"></stop></linearGradient></defs><path fill="url(#M)" d="M898 376h0c5 0 7-1 11-3-2 2-5 4-7 6s-4 5-6 6c-9 8-20 14-31 16s-22 1-31-5c-2-3-4-4-5-7h0c-2-1-3-5-5-6 1-1 2 0 3 1h1 1c6 5 11 8 19 10h2c12 2 25-1 35-8 3-2 7-4 10-7 2-1 2-1 3-3z"></path><path d="M552 195c0-1 1-2 2-3 3 1 5 3 8 4l1-1 6 3c6 3 12 3 19 3h1l1-1c1 1 2 1 4 1h1c2-1 3-1 5-1 1 1 3 1 4 2v2c-1 1-5 4-5 5l-6 6c-1 1-1 1-2 1s-1 1-2 2l-1 1h-2v2 6h-1l-2-4c0-1-1-2-1-3v-2c1 0 0-2 0-2l1-1h0c-4-1-6-4-9-6l-4-2-9-5c-1-1-2-2-3-2v-2c-1-1-5-2-6-3z" class="R"></path><path d="M558 198c5 3 11 5 16 8-1 1-3 1-4 1l-9-5c-1-1-2-2-3-2v-2z" class="P"></path><path d="M574 206c2 2 5 3 7 4s5 1 7 1l1 1c-1 0-3 0-4 1h3v1c-1 1-3 1-5 1h0c-4-1-6-4-9-6l-4-2c1 0 3 0 4-1z" class="X"></path><path d="M563 195l6 3c6 3 12 3 19 3v1l2 2-1 2h1l1 1-4 2c-3-1-6-2-9-4-1-1-3-2-4-3-4-1-8-4-12-6l1-1z" class="P"></path><path d="M590 200c1 1 2 1 4 1h1c2-1 3-1 5-1 1 1 3 1 4 2v2c-1 1-5 4-5 5l-6 6c-1 1-1 1-2 1s-1 1-2 2l-1 1h-2v2 6h-1l-2-4c0-1-1-2-1-3v-2c1 0 0-2 0-2l1-1c2 0 4 0 5-1v-1h-3c1-1 3-1 4-1l-1-1c1 0 1-1 1-1l-2-1 4-2-1-1h-1l1-2-2-2v-1h1l1-1z" class="i"></path><path d="M593 206c2 0 3-1 4 0 0 1 0 2-1 2s-1 1-2 1c-1-1-1-1-1-2v-1z" class="f"></path><path d="M590 200c1 1 2 1 4 1h1c2-1 3-1 5-1 1 1 3 1 4 2v2c-2-1-5-2-7-1-1 0-3 1-4 2v1 1c-1 0-1 1-2 1-1 1-2 1-2 2l-2-1 4-2-1-1h-1l1-2-2-2v-1h1l1-1z" class="g"></path><path d="M588 214h1c3-1 4-2 7-3l-5 5c-1 0-1 1-2 2l-1 1h-2v2 6h-1l-2-4c0-1-1-2-1-3v-2c1 0 0-2 0-2l1-1c2 0 4 0 5-1z" class="C"></path><path d="M453 152c1 1 1 2 3 2h1l1 1h0l-2 1 1 1c3 1 6 2 9 0h0l1 1 1-1c1-1 2-1 3-2v1l-2 2h-2v1c-2 2-2 4-2 7-1 1-4 3-5 4l-1 2h0c-4 2-8 3-12 4l-1-1c-2 2-6 3-9 3-1 0-1 0-2-1h-1l-2-1 2-1h-1-2l1-2v-4c2-5 6-8 10-12l2-1v1l1-1 1 1c3 0 4-3 6-4 0 1 0 2 1 2v1-4z" class="J"></path><path d="M444 156v1l1 2-2 2h0v-2h0-2c-1 2-2 3-2 4l1 1 8-5h0l-6 6c-2 2-4 2-6 4-1 0-1 0-1 1l-3 3v-4c2-5 6-8 10-12l2-1z" class="N"></path><path d="M439 169c2-2 5-3 7-5 2-1 3-3 5-4v-1l2 1c0-1 1-1 2-2v2c-1 1-2 1-3 1h0c-1 2-5 5-4 6 1 0 1 0 2-1l1 1 3-1v1c-2 1-3 1-5 2l-9 3c-3 1-5 2-7 3h-2l1-2 3-3 4-1z" class="D"></path><path d="M439 169c2 0 3 0 5-1l1-1h0l1 1c-1 0-5 2-5 3 2 0 5-2 7-3h1v1l-9 3c-3 1-5 2-7 3h-2l1-2 3-3 4-1z" class="C"></path><path d="M453 152c1 1 1 2 3 2h1l1 1h0l-2 1 1 1c3 1 6 2 9 0h0l1 1 1-1c1-1 2-1 3-2v1l-2 2h-2v1c-5 2-8 6-13 8v-1l-3 1-1-1c-1 1-1 1-2 1-1-1 3-4 4-6h0c1 0 2 0 3-1v-2-1c-1 0-1-1-2-1v-4z" class="Q"></path><path d="M450 166c1-1 3-2 4-3 2 0 4-1 6-1-2 2-5 3-6 4l-3 1-1-1z" class="H"></path><path d="M453 152c1 1 1 2 3 2h1l1 1h0l-2 1 1 1c3 1 6 2 9 0h0l1 1 1-1c1-1 2-1 3-2v1l-2 2h-2l-4 2-8-3c-1 0-1-1-2-1v-4z" class="M"></path><defs><linearGradient id="N" x1="431.587" y1="170.978" x2="464.591" y2="167.963" xlink:href="#B"><stop offset="0" stop-color="#4e4f53"></stop><stop offset="1" stop-color="#7c7c7e"></stop></linearGradient></defs><path fill="url(#N)" d="M454 167c5-2 8-6 13-8-2 2-2 4-2 7-1 1-4 3-5 4l-1 2h0c-4 2-8 3-12 4l-1-1c-2 2-6 3-9 3-1 0-1 0-2-1h-1l-2-1 2-1h-1c2-1 4-2 7-3l9-3c2-1 3-1 5-2z"></path><path d="M446 175h0c1-1 2-2 3-2s1-1 2-1c3 1 5-1 8 0-4 2-8 3-12 4l-1-1z" class="k"></path><defs><linearGradient id="O" x1="587.652" y1="98.187" x2="543.783" y2="107.508" xlink:href="#B"><stop offset="0" stop-color="#101213"></stop><stop offset="1" stop-color="#2c2d2d"></stop></linearGradient></defs><path fill="url(#O)" d="M553 144c-3-5-5-10-6-16-2-10 2-21 9-30 5-7 13-14 22-17 5-1 9-1 14-2h-1c-1 1-2 1-3 1-4 2-4 3-6 6-1 2-2 4-4 5l-3 3h-1l-1-2c-1-1-3-1-5-1-1 1-3 2-3 3 1 2 0 3 0 5 0 0 0 1-1 1v-1h-1-1c-1 0-2 1-3 1v1h-2c0 2 0 2-1 3-3 3-5 8-4 12 2 8 7 17 11 25l2 3h-1 0l3 6c-2-1-3-3-5-4-2 0-3-2-5-3-1 1-1 2-2 3l2 2c0 1 1 2 1 3-2-3-3-5-5-7z"></path><path d="M565 94c1 2 0 3 0 5 0 0 0 1-1 1v-1h-1-1c-1 0-2 1-3 1l6-6z" class="M"></path><path d="M568 91c2-2 5-4 7-5 1-1 2-1 3-2h3v1l1 1c-1 2-2 4-4 5l-3 3h-1l-1-2c-1-1-3-1-5-1z" class="I"></path><path d="M562 146l-10-9c0-2-1-5-2-8 0-3 1-7 1-11 2 4 3 9 5 13 2 5 6 9 8 13h0l3 6c-2-1-3-3-5-4z" class="P"></path><path d="M885 282c1-3 0-5 2-7l1 2c7 10 8 24 6 37-2 8-7 12-11 19-7 10-9 23-3 33 5 6 11 8 18 10-1 2-1 2-3 3-4-1-8-2-11-4-7-4-10-10-12-18-2-10 3-19 9-27 2-2 4-4 5-7 5-7 3-21 1-28-1-3-2-6-4-9 1-2 1-3 1-4h1z" class="AD"></path><path d="M885 282c1-3 0-5 2-7l1 2-1 1 3 6c1 5 2 9 3 13 0 2 1 5 1 7h-3c-1-1-1-3 0-4l-1-1-2-8c0 1-1 2-1 3v1c-1-3-2-6-4-9 1-2 1-3 1-4h1z" class="o"></path><path d="M884 282h1l3 9c0 1-1 2-1 3v1c-1-3-2-6-4-9 1-2 1-3 1-4z" class="q"></path><defs><linearGradient id="P" x1="469.597" y1="136.511" x2="455.258" y2="135.357" xlink:href="#B"><stop offset="0" stop-color="#5e6063"></stop><stop offset="1" stop-color="#77797b"></stop></linearGradient></defs><path fill="url(#P)" d="M466 119l-1-2 1-1h1c1 1 3 1 4 2v1h1l1-1c1 1 1 1 1 2 1 2 1 4 2 7 0 5-1 7-4 11-1 2-1 4-2 6v1 1c-3 3-8 6-12 9l-1-1h-1c-2 0-2-1-3-2v4-1c-1 0-1-1-1-2-2 1-3 4-6 4l-1-1-1 1v-1l2-2-1-1c3-3 6-8 7-13 1-2 2-3 3-5h0l1-4c1 0 2-1 3 0l1 1v-1h0c0-2 1-6 1-8l2-1 1 2c1-2 1-3 1-5h1z"></path><path d="M472 119l1-1c1 1 1 1 1 2 1 2 1 4 2 7 0 5-1 7-4 11-1 2-1 4-2 6h-1-1c-1-1-1-2-1-3h1c0-2 0-3 1-4 0 1 0 0 1 1v-2c1-1 1-2 2-3v-1c-2-2-1-10-1-13h1z" class="J"></path><path d="M472 119c2 4 0 9 0 13-2-2-1-10-1-13h1z" class="O"></path><path d="M464 124c1-2 1-3 1-5h1c2 4 1 9 0 13s-2 8-4 12c-1 2-1 3-2 4 0-2 1-4 1-6l-1-1v-2l1-3-1-1c1-2 1-3 1-4h-1 0c0-2 1-6 1-8l2-1 1 2z" class="G"></path><path d="M463 122l1 2v6c0 3-1 8-3 12l-1-1v-2l1-3-1-1c1-2 1-3 1-4h-1 0c0-2 1-6 1-8l2-1z" class="e"></path><path d="M460 131h1c0 1 0 2-1 4l-7 17v4-1c-1 0-1-1-1-2-2 1-3 4-6 4l-1-1-1 1v-1l2-2-1-1c3-3 6-8 7-13 1-2 2-3 3-5h0l1-4c1 0 2-1 3 0l1 1v-1z" class="P"></path><path d="M456 131c1 0 2-1 3 0l1 1v1c-2 1-3 2-4 3l-1-1 1-4z" class="L"></path><path d="M460 131h1c0 1 0 2-1 4l-7 17v4-1c-1 0-1-1-1-2-2 1-3 4-6 4l-1-1-1 1v-1l2-2c4-2 5-7 8-10h1c2-4 3-7 5-11v-1-1z" class="V"></path><defs><linearGradient id="Q" x1="485.869" y1="525.996" x2="479.904" y2="527.853" xlink:href="#B"><stop offset="0" stop-color="#8a8b8d"></stop><stop offset="1" stop-color="#a7a7a8"></stop></linearGradient></defs><path fill="url(#Q)" d="M473 509c3-2 0-7 1-11 0-1 1-2 2-3 0 7 3 13 6 20l6 14 24 53 2 1c-2 1-4 3-6 4h-2-1l-1 1v1l-1-2h0c-1-2-1-3-1-5-1-4-4-9-5-13v3h-1c-1-1-1-2-1-3 0-2-1-4 0-6 0-2-2-5-3-7 0-3-2-5-1-7-1-3-3-6-4-9l-3-6c-1-1-1-3-2-4v-2l-2-2c-1-4-2-7-4-10-1-2-1-5-3-6v-1z"></path><path d="M482 515l6 14v5-2c-1 0-1-1-1-1l-1-1c0-2-1-3-2-5s-2-4-2-5v-1c0-1-1-1-1-1 0-1 0-2 1-3z" class="f"></path><path d="M492 556c0-3-2-5-1-7 2 5 4 10 6 16 1 4 3 10 5 14 0 2 2 3 3 5h0v3l-1 1v1l-1-2h0c-1-2-1-3-1-5-1-4-4-9-5-13v3h-1c-1-1-1-2-1-3 0-2-1-4 0-6 0-2-2-5-3-7z" class="C"></path><path d="M503 587c0-1 1-2 2-3v3l-1 1v1l-1-2h0z" class="I"></path><path d="M495 569c0-2-1-4 0-6 1 2 1 5 2 6v3h-1c-1-1-1-2-1-3z" class="e"></path><defs><linearGradient id="R" x1="513.881" y1="561.719" x2="486.381" y2="558.081" xlink:href="#B"><stop offset="0" stop-color="#3b3a38"></stop><stop offset="1" stop-color="#7e8289"></stop></linearGradient></defs><path fill="url(#R)" d="M488 529l24 53 2 1c-2 1-4 3-6 4h-2-1v-3h0c0-1 0-1-1-2-2-4-3-9-5-14-1-3-2-7-3-11-1-2-1-4-2-6 0-3-2-6-3-9-1-2-2-5-3-8v-5z"></path><defs><linearGradient id="S" x1="820.367" y1="322.406" x2="820.905" y2="329.214" xlink:href="#B"><stop offset="0" stop-color="#de8e40"></stop><stop offset="1" stop-color="#b38255"></stop></linearGradient></defs><path fill="url(#S)" d="M832 318l4 1c2 0 4 1 6 0h4 0l2 1 2 2h2 0 1l-1 2c-1 1-1 1-2 1l2 3c-1 2-1 2-2 2l-2 1c3 3 6 5 7 8l2 2c0 1 0 1-1 2l-1-1c-1 1-2 1-4 0v1l-2-2c-1-1-2-3-4-4h-1l-5-1c-3-1-5-1-7-1 0-1 0-2 1-3h-2-1c-3-2-8-3-11-4l-7-1c-8 2-16 4-24 8-3 1-6 3-9 5v-1c1-1 1-1 2-1v-1c1-1 2-1 3-2h1c1 0 2-1 2-1l1-1h1 1l1-1h-1c-2 0-4 1-5 2-2 1-3 2-5 2-2 2-5 3-8 4 3-2 6-4 9-5 10-6 21-9 32-12v-1l15-2v-1-1h3 1z"></path><path d="M832 318l4 1c2 0 4 1 6 0h4 0l2 1 2 2c-13-2-25-1-37 1v-1l15-2v-1-1h3 1z" class="K"></path><path d="M812 327c5-1 11-2 17-2 7-1 14 0 21 0l2 3c-1 2-1 2-2 2l-2 1c-2 0-3-1-5-2 0 0-1 0-2-1h-1l-1 1-4-1c-1 0-2 0-3 1v1l1 1v1h-2-1c-3-2-8-3-11-4l-7-1z" class="m"></path><path d="M819 328c5-1 11-1 16 0-1 0-2 0-3 1v1l1 1v1h-2-1c-3-2-8-3-11-4z" class="AC"></path><path d="M839 329l1-1h1c1 1 2 1 2 1 2 1 3 2 5 2 3 3 6 5 7 8l2 2c0 1 0 1-1 2l-1-1c-1 1-2 1-4 0v1l-2-2c-1-1-2-3-4-4h-1l-5-1c-3-1-5-1-7-1 0-1 0-2 1-3v-1l-1-1v-1c1-1 2-1 3-1l4 1z" class="q"></path><path d="M850 336l2 2-1 3c-2-1-2-2-4-3v-1l3-1z" class="v"></path><path d="M839 329l1-1h1c1 1 2 1 2 1 2 1 3 2 5 2 3 3 6 5 7 8l2 2c0 1 0 1-1 2l-1-1c0-1-2-3-3-4l-2-2-6-4c-1-1-3-2-5-3z" class="I"></path><path d="M835 328l4 1c2 1 4 2 5 3l-1 1 1 2-1 1 1 1-5-1c-3-1-5-1-7-1 0-1 0-2 1-3v-1l-1-1v-1c1-1 2-1 3-1z" class="s"></path><path d="M835 328l4 1c2 1 4 2 5 3l-1 1c-3-1-7-1-10-1v-1l-1-1v-1c1-1 2-1 3-1z" class="AK"></path><path d="M558 151c0-1-1-2-1-3l-2-2c1-1 1-2 2-3 2 1 3 3 5 3 2 1 3 3 5 4l-3-6h0 1c2 3 4 6 7 8l2 3c1-1 1-2 2-3 2 0 2 0 3-1h1c3 2 5 4 7 6 1 1 3 2 4 3l2 2c1 1 2 0 3 1 1 0 2 1 2 2l1 3c0 1 1 3 1 4h1c0-2-1-2-1-4v-2c1 0 1 1 2 2v2 3 1c1 2 2 4 4 5h-4c0-1-1-1-1-1-2-1-4-2-5-4v1c-6-2-11-4-17-6-2-1-4-2-5-4-2-1-4-3-6-4-2 1-2 1-4 0l-2-1h-1c-1 1-1 2-1 3l-5-5 2-1c2-4-6-10-6-14l1 2 1-1c2 2 3 4 5 7z" class="c"></path><path d="M574 155c1-1 1-2 2-3 2 0 2 0 3-1h1v1 1 1c3 3 7 8 11 10 1 1 2 2 3 2l1 1s0 1 1 1c1 1 2 2 2 4v1c-2-2-4-6-7-7-2-1-3-3-5-4h-1l1 1c2 1 2 3 5 3 1 1 4 4 4 6 0-1-1-1-1-1l-4-4c-4-1-8-6-11-9l-2-2c-1 0-2-1-3-1z" class="T"></path><path d="M598 173v-1c0-2-1-3-2-4-1 0-1-1-1-1l-1-1c-1 0-2-1-3-2-4-2-8-7-11-10v-1-1-1c3 2 5 4 7 6 1 1 3 2 4 3l2 2c1 1 2 0 3 1 1 0 2 1 2 2l1 3c0 1 1 3 1 4h1c0-2-1-2-1-4v-2c1 0 1 1 2 2v2 3 1c1 2 2 4 4 5h-4c0-1-1-1-1-1-2-1-4-2-5-4l-3-2c2 0 3 1 5 2h0v-1z" class="e"></path><path d="M558 151c0-1-1-2-1-3l-2-2c1-1 1-2 2-3 2 1 3 3 5 3 2 1 3 3 5 4l-3-6h0 1c2 3 4 6 7 8l2 3c1 0 2 1 3 1l2 2h0c1 3 3 4 5 6 0 1 1 1 2 2s2 1 2 3v1h-1c-1-1-1-2-2-3-2-1-3-3-6-3-1 0-2 0-3-1h0-1c-1-1-2-1-3-1-1-1-2-1-3-2h-4 0l3 1c-2 1-2 1-4 0l-2-1h-1c-1 1-1 2-1 3l-5-5 2-1c2-4-6-10-6-14l1 2 1-1c2 2 3 4 5 7z" class="i"></path><path d="M576 159c1 0 1 0 2 1v1c1 0 1 1 1 2h-1-2 0-1c-1-1-2-1-3-1-1-1-2-1-3-2l7-1z" class="Q"></path><path d="M555 158l2-1c2-4-6-10-6-14l1 2 1-1c2 2 3 4 5 7s5 6 8 7c2 1 4 1 6 0 1 0 2 0 4 1l-7 1h-4 0l3 1c-2 1-2 1-4 0l-2-1h-1c-1 1-1 2-1 3l-5-5z" class="L"></path><path d="M784 291c2 1 3 3 6 3 2 0 2 0 3 2l1 1c2 1 3 2 4 3l3 3v1l1 1c1 1 2 2 4 2l1 1h-1l1 2 4 1 1 1c1 0 2 0 3 1l1 1h1l1 1c3 2 6 3 10 4v1l-15 2v1c-11 3-22 6-32 12-3 1-6 3-9 5-5 4-10 8-15 13h0c-1-1 0-1-1-1l-17 19c1-4 6-8 8-11l8-9h-1c3-4 9-8 13-12 2-1 3-2 5-4l2-1h0l3-2 2-1v-1c2-1 2-2 3-5 1 2 1 2 3 2 1 0 2 0 3-1v-1c4 0 8-1 12-2h0l2-1c1 0 2-1 3-2h-1c-3-2-8-6-10-8v-2l-6-6-2-4 1-1 2-1c-1-2-2-3-3-4s-2-2-2-3h0z" class="b"></path><path d="M779 331h1c1 0 1-1 2-1l2 1-6 3h-1-3 0l3-2 2-1z" class="S"></path><path d="M782 325c1 2 1 2 3 2 1 1 2 1 3 1-1 1-2 2-4 2v1l-2-1c-1 0-1 1-2 1h-1v-1c2-1 2-2 3-5z" class="O"></path><path d="M816 314h1l1 1c3 2 6 3 10 4v1l-15 2c-2 1-3 1-4 1l-1 1h-2-2c-1 1-2 1-4 1 0 0-1 1-2 1h-1c-1 1-1 1-2 1l-2 1c-1 0-2 1-3 1l-1-1h-1c-1 0-2 0-3-1 1 0 2 0 3-1v-1c4 0 8-1 12-2h0l2-1c1 0 2-1 3-2 1 1 1 2 3 2h1l13-2c-2-2-4-3-5-5l-1-1z" class="M"></path><path d="M805 320c1 1 1 2 3 2l-8 2v-1h0l2-1c1 0 2-1 3-2z" class="K"></path><path d="M788 325c4 0 8-1 12-2v1c-3 2-8 3-11 4h-1c-1 0-2 0-3-1 1 0 2 0 3-1v-1z" class="S"></path><path d="M804 313h-1c1-1 1 0 1-1s0-2 1-3l1-1 1 2 4 1 1 1c1 0 2 0 3 1l1 1 1 1c1 2 3 3 5 5l-13 2c1-1 1-1 1-3h-1c-1-1-1-3-1-4l-4-2z" class="G"></path><path d="M804 313h-1c1-1 1 0 1-1s0-2 1-3l1-1 1 2 4 1h-5l-1 1c1 1 2 1 3 2 0 1 1 1 0 1l-4-2z" class="H"></path><path d="M784 291c2 1 3 3 6 3 2 0 2 0 3 2l1 1c2 1 3 2 4 3l3 3v1l1 1c1 1 2 2 4 2l1 1h-1l-1 1c-1 1-1 2-1 3s0 0-1 1h1l4 2c0 1 0 3 1 4h1c0 2 0 2-1 3h-1c-2 0-2-1-3-2h-1c-3-2-8-6-10-8v-2l-6-6-2-4 1-1 2-1c-1-2-2-3-3-4s-2-2-2-3h0z" class="c"></path><path d="M784 291c2 1 3 3 6 3 2 0 2 0 3 2l1 1c2 1 3 2 4 3l3 3v1l1 1c1 1 2 2 4 2l1 1h-1l-1 1c-1 1-1 2-1 3s0 0-1 1h1l-1 1h0l-6-6c-1-1-3-2-4-4 0-1 0-1-1-2h0c-1-2-2-3-3-4-1-2-2-3-3-4s-2-2-2-3h0z" class="B"></path><path d="M797 308c1 0 2-1 3-2l2 1h4l1 1h-1l-1 1c-1 1-1 2-1 3s0 0-1 1h1l-1 1h0l-6-6z" class="N"></path><path d="M208 344c0-1 1-2 2-2 2 0 3 1 4 2s1 2 2 2h1v1c2 6 3 11 2 18v1c-2 11-10 21-19 27l-2 2v-1h-2v1c-1 0-1 0-2 1l-3 2c-2 0-2 0-3 1h-1-1-1l-1 1h-1-1c-1 1-1 1-2 0h0l-1 1c-1 0-1 0-3-1l1-1c2-1 5-2 6-4l-1-1 2-1h-1c3-1 9-4 11-4l1 2h0c1-2 0-1 0-3l1-1c6-4 12-9 15-15h-1v1h-1c1-2 2-4 2-7l-1-1c-2 2-6 2-8 2l-1 2-1-1c-1 0-2-1-3-1 0-2 0-3 1-5h-2c0-1 1-3 1-3 1-1 1-2 2-3s2-2 3-2v-2h0v-1h-2 0l1-1s0-1 1-2l2 2h5l2-2-3-4z" class="AD"></path><path d="M211 348c2 7 4 15 1 22l-1 2h-1v1h-1c1-2 2-4 2-7l-1-1v-1-3-1c0-2-1-5-2-7-1-1-1 0-2-1v-1h-2-1l1-1h5l2-2z" class="R"></path><path d="M200 351l1-1s0-1 1-2l2 2-1 1h1 2v1c1 1 1 0 2 1 1 2 2 5 2 7v1 3 1c-2 2-6 2-8 2l-1 2-1-1c-1 0-2-1-3-1 0-2 0-3 1-5h-2c0-1 1-3 1-3 1-1 1-2 2-3s2-2 3-2v-2h0v-1h-2 0z" class="K"></path><path d="M202 355l3-1c1 2 2 2 2 4l-3 2c-1-1-3-2-3-4l1-1z" class="k"></path><path d="M200 351l1-1s0-1 1-2l2 2-1 1h1 2v1c1 1 1 0 2 1 1 2 2 5 2 7v1 3l-2-1c-2 0-3 0-4-2 1 0 3-1 4-2l-1-1c0-2-1-2-2-4l-3 1v-1-2h0v-1h-2 0z" class="H"></path><path d="M831 332h2c-1 1-1 2-1 3v1c-2 1-5 4-7 5l-1 1c-2 1-3 3-4 6l1 1c1 2 1 3 1 5l1-1h0c2 1 4 1 6 1v-1l2-1c0 2-1 2-1 3l1 3c1 1 1 0 1 1l2 6 1 2c-1 0-2 0-3 2h1c1 1 1 2 1 3v1c1 1 1 2 1 3h0-1c-1-1-2-1-3-1l2 2h-1c-2-1-3-2-4-3h-1l-1 1-1 3-1 1s4 5 5 5h-1-1c-1-1-2-2-3-1 2 1 3 5 5 6h0c1 3 3 4 5 7l-7-5c-8-7-13-16-16-27-2-9 1-15 4-22 0-2 1-4 2-5 0-1 1-1 2-1l-1 1h0c3 0 2-1 4-2 0 0 2-1 3-1h1c2-1 3-1 5-2z" class="AJ"></path><path d="M820 339l1-1 1-1h2c1 0 1-1 2-1h1 2c2-1 1-1 3 0-2 1-5 4-7 5l-1-1-1-1-1 1c-1 0-2-1-2-1z" class="v"></path><path d="M820 339s1 1 2 1l1-1 1 1 1 1-1 1c-2 1-3 3-4 6l-1 2c-1-1-1-3 0-4v-1c-1-1-2 0-3 0 1-2 2-4 4-6z" class="s"></path><path d="M816 345c1 0 2-1 3 0v1c-1 1-1 3 0 4l-2 7c-1 3-1 4-3 5h-1c-1-7 0-11 3-17z" class="AD"></path><path d="M813 362h1c2-1 2-2 3-5v5c1 7 2 12 7 17 0 0 4 5 5 5h-1-1c-1-1-2-2-3-1 2 1 3 5 5 6h0c1 3 3 4 5 7l-7-5v-1-1l-2-4c-2-3-4-5-6-8s-4-8-6-12v-3z" class="q"></path><path d="M813 362h1c2-1 2-2 3-5v5c0 3 0 4-1 5l-3-2v-3z" class="r"></path><path d="M820 348l1 1c1 2 1 3 1 5l1-1h0c2 1 4 1 6 1v-1l2-1c0 2-1 2-1 3l1 3c1 1 1 0 1 1l2 6 1 2c-1 0-2 0-3 2h1c1 1 1 2 1 3v1c1 1 1 2 1 3h0-1c-1-1-2-1-3-1l2 2h-1c-2-1-3-2-4-3h-1l-1 1-1 3-1 1c-5-5-6-10-7-17v-5l2-7 1-2z" class="C"></path><path d="M822 354l1-1v3c0 1-1 1-1 1l2 2h-1v5c-1-1-2-2-2-3-1-3 0-5 1-7z" class="D"></path><path d="M822 371h-1c-1-2-1-2 0-4 1-1 3 0 5 0l2 4-1 1h-1c-1-1-2-1-4-1z" class="E"></path><path d="M828 371h1c0 1 1 2 1 3l1 1 2 2h-1c-2-1-3-2-4-3h-1l-1 1-1 3-1-1c-1-2-2-4-2-6 2 0 3 0 4 1h1l1-1z" class="S"></path><path d="M829 353l2-1c0 2-1 2-1 3l1 3c1 1 1 0 1 1l2 6 1 2c-1 0-2 0-3 2h1c1 1 1 2 1 3v1c1 1 1 2 1 3h0-1c-1-1-2-1-3-1l-1-1c0-1-1-2-1-3h-1l-2-4-3-3v-5h1l-2-2s1 0 1-1v-3h0c2 1 4 1 6 1v-1z" class="G"></path><path d="M829 353l2-1c0 2-1 2-1 3l1 3c1 1 1 0 1 1l2 6h-2v-1c0-1-1-2-1-2h-1c0-1 1-1 0-2-1 0-2 0-3-1h-3l-2-2s1 0 1-1v-3h0c2 1 4 1 6 1v-1z" class="J"></path><path d="M754 351h1l-8 9c-2 3-7 7-8 11l17-19c1 0 0 0 1 1-11 10-20 22-27 36-2 3-4 6-5 10h0l-15 38v-1c0-1-1-2 0-3v-1h0c-1 1-2 2-2 3-1 2-2 4-2 6h-2l-1 1c0-1 0-2-1-3l-3-1c1-1 2-1 2-2l-1-2 2-2-1-1c-1 0-1 0-2-1l3-3-2-3v-3l-1-1c0-1 1-2 0-3v-1l3-8h0l1-4 3-6h1c0-2 1-5 2-7l-1-1 1-3-1-1c0-1 1-3 2-3l1-3c2-1 2-2 3-4 1 0 2 1 3 0l-4 7 1 1v1h1c-2 3-3 5-3 8l1 1c-1 2-1 3 0 5 2 1 3 1 5 1s2-1 3-3h2c0-1 1-3 2-4l6-12 10-16c5-4 8-9 13-14z" class="R"></path><path d="M704 417c2 2 3 2 4 4v1c2-1 3-2 5-3-1 4-2 9-5 12l-4 10-1 1c0-1 0-2-1-3l-3-1c1-1 2-1 2-2l-1-2 2-2-1-1c-1 0-1 0-2-1l3-3-2-3v-3h0l1-1 3-3z" class="P"></path><path d="M702 427l2 1-2 1 1 1h1c-1 1-1 2-2 2l-1-1c-1 0-1 0-2-1l3-3z" class="d"></path><path d="M704 430h2v4c0-1 1-2 2-3l-4 10-1 1c0-1 0-2-1-3l-3-1c1-1 2-1 2-2l-1-2 2-2c1 0 1-1 2-2z" class="j"></path><path d="M704 417c2 2 3 2 4 4v1c1 2 0 3-1 5 0 1 0 1-1 2-1 0-1 0-2-1l-2-1-2-3v-3h0l1-1 3-3z" class="I"></path><path d="M703 420c1-1 2-1 3 0s1 2 0 3l-1 1h-1c-1 0-2 0-3-1v-1l2-2zm11-44c1 0 2 1 3 0l-4 7 1 1v1h1c-2 3-3 5-3 8l1 1c-1 2-1 3 0 5 2 1 3 1 5 1s2-1 3-3h2l-4 8-6 14c-2 1-3 2-5 3v-1c-1-2-2-2-4-4l-3 3-1 1h0l-1-1c0-1 1-2 0-3v-1l3-8h0l1-4 3-6h1c0-2 1-5 2-7l-1-1 1-3-1-1c0-1 1-3 2-3l1-3c2-1 2-2 3-4z" class="d"></path><path d="M708 415h1v4h-1c-1-2 0-2 0-4z" class="L"></path><path d="M721 397h2l-4 8c-1-1 0-1 0-3h-1c-1 3-3 6-5 9l-6-2c0-2 1-5 1-6 1-2 2-2 4-3v2c1 1 1 2 2 3v-4l-1-1v-1c2 1 3 1 5 1s2-1 3-3z" class="j"></path><path d="M714 376c1 0 2 1 3 0l-4 7 1 1v1h1c-2 3-3 5-3 8l1 1c-1 2-1 3 0 5v1l1 1v4c-1-1-1-2-2-3v-2h-4c-1 1-1 1-1 3v2l-1 1v1c0 1 0 2-1 3s-1 3-1 5l1 2h-1 0l-3 3-1 1h0l-1-1c0-1 1-2 0-3v-1l3-8h0l1-4 3-6h1c0-2 1-5 2-7l-1-1 1-3-1-1c0-1 1-3 2-3l1-3c2-1 2-2 3-4z" class="Y"></path><path d="M707 398c0 2 0 2-1 4s-1 6-2 9c-1 1-2 3-3 5-1 1-1 1-1 2l1 2-1 1h0l-1-1c0-1 1-2 0-3v-1l3-8h0l1-4 3-6h1z" class="X"></path><path d="M714 376c1 0 2 1 3 0l-4 7-4 8-1-1 1-3-1-1c0-1 1-3 2-3l1-3c2-1 2-2 3-4z" class="I"></path><defs><linearGradient id="T" x1="336.963" y1="135.094" x2="339.306" y2="155.419" xlink:href="#B"><stop offset="0" stop-color="#cdcbca"></stop><stop offset="1" stop-color="#f3f2f4"></stop></linearGradient></defs><path fill="url(#T)" d="M440 130c1 0 2 1 3 1-6 5-13 10-21 13-5 2-10 3-15 5-4-1-10 1-14 1-4-1-9 0-13 0h-19-74l-42-1c-6 1-13 0-19 0v-1l3-1c2 0 3 0 5-1h22c4-1 8 0 12 0h23l75-1c14 0 30 1 44-2 11-2 21-6 30-13z"></path><path d="M234 146h22c-3 1-6 0-8 0-2 1-3 1-4 1-3 0-6-1-8 1h-1v1h10 0c-6 1-13 0-19 0v-1l3-1c2 0 3 0 5-1z" class="d"></path><path d="M439 331h1c5 4 9 8 12 14l4 5 1 2v-1c1 1 1 3 1 4 1 4 0 9-2 13-2 1-3 2-4 4l-3 3h0c-1 1-2 3-3 4 2 2 1 2 1 4-1 2-1 3-2 5v1c-1 3-2 5-2 8l-2-6h-1 0-1v-4h-1l-2-7-1-3h-1c0-1 0-2-1-3l-2-3c2-2 6-5 9-6 2-1 4-3 5-5l-4-3s-1 0-1-1c-1 0-2 1-3 2l-1 1-1 1-1-1v-2c-1 0-1 1-1 1h-1v2c0-1 0-2-1-3v-1c0-1 0-3 1-4s2-1 3-2v-1c0-1 0-1-1-2l-1-1h-1c-3 1-1 2-2 4-2 2-5 2-7 2l-1-1v-2c0-3 1-5 3-7v-1c2-1 4-1 6 0h1c3 1 6 1 8 3 2 1 3 2 4 3v-1-1l2 2c2 0 4 4 5 6v-1c-1-3-3-5-5-8 0-1 0-2-1-3v-2l-2-2c0-3-4-4-4-6z" class="W"></path><path d="M446 379c2 2 1 2 1 4-1 2-1 3-2 5v-3l-1 1h-1c0-1 0-2 1-3s2-2 2-4z" class="a"></path><path d="M439 331h1c5 4 9 8 12 14l4 5 1 2-1 3-1-2c-2-6-6-10-10-14l-2-2c0-3-4-4-4-6z" class="k"></path><path d="M457 351c1 1 1 3 1 4 1 4 0 9-2 13-2 1-3 2-4 4l-3 3h0c-1 1-2 3-3 4 0 2-1 3-2 4 1-6 6-9 9-15 2-4 2-8 3-12v-1l1-3v-1z" class="H"></path><path d="M446 347c2 0 4 4 5 6 2 3 2 7 1 11-3 7-8 10-14 13l2-3c2-5 7-7 9-12 1-3 0-7-1-10l-2-5z" class="D"></path><defs><linearGradient id="U" x1="874.078" y1="193.605" x2="856.638" y2="206.124" xlink:href="#B"><stop offset="0" stop-color="#1a1818"></stop><stop offset="1" stop-color="#292f35"></stop></linearGradient></defs><path fill="url(#U)" d="M845 182c1-2 2-3 4-4l1 2 20 15c3 0 4 1 7 2 0 0 1 1 2 1l6 6 3 3c4 4 9 9 9 14l-1-1-1 4-4-4 3 1v-1s-1-1-1-2-2-2-3-3l-1 2-1-1c-2-1-2-1-3-2l-9-2h-4-3v-1c-1 0-1-1-2-1l-3-1v-1c-1 0-3-1-4-2-2 1-3 1-5 2s-8-2-10-3l-3-1c-1-1-2-3-4-3v-1c0-2 0-2 1-4h0c-1-2-1-3-1-5l-1-1c2-1 3-2 4-3 0-2 0-2 1-3h2l1-2z"></path><path d="M843 187l3 2c0 2 0 3-1 5h-1l-1-1v-6z" class="x"></path><path d="M845 182c1-2 2-3 4-4l1 2c-2 1-2 3-3 5l-1 4-3-2 1-3 1-2z" class="AE"></path><path d="M845 182c1 1 2 2 2 3l-1 4-3-2 1-3 1-2z" class="AH"></path><path d="M846 195l1-1v-2c3 1 3 4 4 4 1 1 2 0 3 0 4 0 7-2 10 2h-1v-1l-1 2h0 0c1 1 1 1 2 1h0c1 0 1 0 2 1h1 0-3c-1-1-3-1-4-1 0-1-2-1-3-1-4-1-7-2-11-4z" class="G"></path><path d="M864 201h3c6 2 14 5 17 11 1 1 2 1 3 2s1 0 1 1v1c-2-1-2-1-3-2v-1c-2-1-6-4-8-4s-4-1-6-2h0c-1-1-1-1-3-1-1 0 0 0-1-1 1-1 3 0 5-1h-1c-2 0-4-1-6-2h-1v-1z" class="K"></path><path d="M864 208l1-1-1-1h0 2c2 1 4 1 5 1 2 1 4 2 6 2s6 3 8 4v1l-9-2h-4-3v-1c-1 0-1-1-2-1l-3-1v-1z" class="J"></path><path d="M864 208l1-1-1-1h0 2v1c2 2 4 3 7 3 0 1 0 1-1 2h-3v-1c-1 0-1-1-2-1l-3-1v-1z" class="S"></path><path d="M870 195c3 0 4 1 7 2 0 0 1 1 2 1l6 6 3 3c4 4 9 9 9 14l-1-1c-6-11-16-17-26-25z" class="u"></path><path d="M841 187c0-2 0-2 1-3h2l-1 3v6l-1 1c1 1 3 1 4 1 4 2 7 3 11 4 1 0 3 0 3 1 1 0 3 0 4 1v1h1c2 1 4 2 6 2h1c-2 1-4 0-5 1 1 1 0 1 1 1 2 0 2 0 3 1h0c-1 0-3 0-5-1h-2 0l1 1-1 1c-1 0-3-1-4-2-2 1-3 1-5 2s-8-2-10-3l-3-1c-1-1-2-3-4-3v-1c0-2 0-2 1-4h0c-1-2-1-3-1-5l-1-1c2-1 3-2 4-3z" class="F"></path><path d="M840 189h1c1 1 1 2 0 3h-1v-3zm22 15l-1-2h1 2l1 3h-1c-1 0-1 0-2-1z" class="I"></path><path d="M860 200v1l-12-3 6 3c2 0 2 0 2 1l-14-5c-1-1-2-2-2-3h2c1 1 3 1 4 1 4 2 7 3 11 4 1 0 3 0 3 1z" class="J"></path><path d="M839 196c1 1 3 2 4 3 2 0 4 0 5 1l12 6c-2 1-3 1-5 2s-8-2-10-3l-3-1c-1-1-2-3-4-3v-1c0-2 0-2 1-4z" class="i"></path><path d="M839 196c1 1 3 2 4 3 2 0 4 0 5 1v1c-3 0-4 0-6-1v1c1 1 2 2 3 4l-3-1c-1-1-2-3-4-3v-1c0-2 0-2 1-4z" class="U"></path><path d="M843 199c2 0 4 0 5 1v1c-3 0-4 0-6-1h-1-1-1l1-1h3 0z" class="e"></path><defs><linearGradient id="V" x1="464.214" y1="80.196" x2="465.048" y2="135.755" xlink:href="#B"><stop offset="0" stop-color="#151415"></stop><stop offset="1" stop-color="#3f4245"></stop></linearGradient></defs><path fill="url(#V)" d="M437 78c17 3 31 6 41 21 6 8 9 18 8 28-1 3-2 7-3 9l-6 12c-4 4-7 6-11 9h0c-3 2-6 1-9 0l-1-1 2-1h0c4-3 9-6 12-9v-1-1c1-2 1-4 2-6 3-4 4-6 4-11-1-3-1-5-2-7 0-1 0-1-1-2l-1 1h-1v-1c-1-1-3-1-4-2h1 3v-1l1-1-2-4-2-3-1-2-1 3c-1-3-1-7-4-10v-1l-1-1h-1c-1 1-1 2-2 2l-2-3c-2-6-8-10-13-14-2-1-4-1-6-3z"></path><path d="M454 84h2l1 1-1 2h-1c-1 0-1 0-2-1l1-2z" class="o"></path><path d="M469 97c3 1 6 3 8 6v1h-3v1h-2l-1 1v-3-1c0-2-1-3-2-5z" class="N"></path><path d="M460 96v-2-1h4c1 0 2 1 3 2l2 2c1 2 2 3 2 5v1c-2 1-2 2-3 4l-1-2-1 3c-1-3-1-7-4-10v-1l-1-1h-1z" class="b"></path><path d="M467 100v-5l2 2c1 2 2 3 2 5-1 0-1 1-2 1l-1-3h-1z" class="B"></path><path d="M460 96v-2-1h4c1 0 2 1 3 2v5c0 1 0 2-1 2-1-1-1-2-2-3 0-1-2-2-2-2l-1-1h-1z" class="Q"></path><defs><linearGradient id="W" x1="483.652" y1="123.936" x2="473.075" y2="142.609" xlink:href="#B"><stop offset="0" stop-color="#47484b"></stop><stop offset="1" stop-color="#5f6063"></stop></linearGradient></defs><path fill="url(#W)" d="M470 145c0-1 1-2 1-3 1-2 2-3 3-5 2-2 2-5 3-7 2-3 4-5 6-8 0 3 0 5-1 8l-1 4c0 1-1 2-2 3-1 2-1 3-2 4l-1 2c-2 2-4 4-7 6v-1c1-1 3-2 4-4h-1l-2 2v-1z"></path><path d="M476 143l1-2c1-1 1-2 2-4 1-1 2-2 2-3l1-4c0 2 0 5 1 6l-6 12c-4 4-7 6-11 9h0c-3 2-6 1-9 0l-1-1 2-1h0c4-3 9-6 12-9l2-2h1c-1 2-3 3-4 4v1c3-2 5-4 7-6z" class="S"></path><path d="M476 143c0 2 0 3-2 4-1 2-3 4-5 5-1 0-2 1-2 1-2 2-4 3-6 3h-1c1-1 1-1 2-1s2-1 3-1v-1c-2 0-5 2-7 2h0c4-3 9-6 12-9l2-2h1c-1 2-3 3-4 4v1c3-2 5-4 7-6z" class="D"></path><path d="M468 107c1-2 1-3 3-4v3l1-1h2v-1h3c1 2 2 3 2 5 0 1 1 1 1 2 1 1 1 2 1 3-1 4-2 7-4 10v1l-1 2c-1-3-1-5-2-7 0-1 0-1-1-2l-1 1h-1v-1c-1-1-3-1-4-2h1 3v-1l1-1-2-4-2-3z" class="J"></path><path d="M474 104h3c1 2 2 3 2 5 0 1 1 1 1 2 1 1 1 2 1 3l-1 1c-1 1-1 2-2 3v-2-5c-1-1-2-2-2-3v-1c-1-2-1-2-2-3z" class="B"></path><path d="M468 107c1-2 1-3 3-4v3l1-1h2v3c0 2 0 3 1 4v4l1 5c0 1 1 2 1 3v1l-1 2c-1-3-1-5-2-7 0-1 0-1-1-2l-1 1h-1v-1c-1-1-3-1-4-2h1 3v-1l1-1-2-4-2-3z" class="N"></path><path d="M468 107c1-2 1-3 3-4v3l1-1h2v3h-3c0 1-1 2-1 2l-2-3z" class="B"></path><path d="M206 317c1 1 1 1 2 1l1 1h2 3c3 1 8 0 11 2h0l2 1 1-1c1 0 3 1 4 0l5-1h3 2v1h-1c-1 0-3 0-4 1l-1 1c3 0 4 1 7-1v-1l2-1v1c-1 1 0 0-1 0-1 1-1 2-2 3h1l1-1 2 2c1 1 2 1 4 1l1 2 1-1c1 1 2 1 3 1l-2 1v1c2 0 5-1 7-1 1-1 1 0 1 0v1l1 1h-1c-1 0-2 0-3-1-1 0-1 0-2 2 0 1 0 1 2 1 0 1 0 1 1 1l1 1-1 1-1-1c-1 0-2-1-3-1l2 1c0 1 0 2 1 3h-1l10 8c1 1 1 1 1 2l-3-2c-6-5-13-11-21-14l1 1c1 2 6 2 6 5h0c-4-2-6-3-10-3-8-4-16-6-25-8-1 0-1 0-2 1-1 0-3 0-4 1h-1c-3 1-6 1-9 2v1h0v3c0 1 0 1-1 1-1 1 0 1-1 1h-1-3-4c-1 0-3 1-5 2-1-1-2-2-3-2l2-2-2-1h-2c-1 2-3 4-4 6l-3-7c-1-3-1-5 0-9h0c0-1 0-1 1-2 0-1 0-1 1-2h4v-1h0l1 1h1c4-1 8-1 12-1 3 0 6 0 9-1 2 0 2 0 4-1z" class="F"></path><path d="M232 321l5-1h3 2v1h-1c-1 0-3 0-4 1l-1 1h-1 0l1 1c1 0 0 0 1 1 1 0 1 1 3 1l2 1c3 1 5 1 7 3h0c-8-2-15-6-22-8l1-1c1 0 3 1 4 0z" class="U"></path><path d="M206 317c1 1 1 1 2 1l1 1c-5 3-27-1-29 3-1 0-1-1-1-2v-1h0l1 1h1c4-1 8-1 12-1 3 0 6 0 9-1 2 0 2 0 4-1z" class="H"></path><path d="M236 323c3 0 4 1 7-1v-1l2-1v1c-1 1 0 0-1 0-1 1-1 2-2 3h1l1-1 2 2c1 1 2 1 4 1l1 2 1-1c1 1 2 1 3 1l-2 1v1c2 0 5-1 7-1 1-1 1 0 1 0v1l1 1h-1c-1 0-2 0-3-1-1 0-1 0-2 2 0 1 0 1 2 1 0 1 0 1 1 1l1 1-1 1-1-1c-1 0-2-1-3-1s-5-3-6-4h0c-2-2-4-2-7-3l-2-1c-2 0-2-1-3-1-1-1 0-1-1-1l-1-1h0 1z" class="M"></path><path d="M189 321c19-1 39 2 55 11l1 1c1 2 6 2 6 5h0c-4-2-6-3-10-3-8-4-16-6-25-8-1 0-1 0-2 1-1 0-3 0-4 1v-1c-2 0-3-2-4-3h-10l-4-1h-7c-1 0-1 0-3 1v-2h0 3c1-1 2 0 4-1v-1z" class="AD"></path><path d="M206 325l10 2c-1 0-1 0-2 1-1 0-3 0-4 1v-1c-2 0-3-2-4-3z" class="h"></path><path d="M175 320h4c0 1 0 2 1 2 3 1 6 0 9-1v1c-2 1-3 0-4 1h-3 0v2c2-1 2-1 3-1h7l4 1h10c1 1 2 3 4 3v1h-1c-3 1-6 1-9 2v1h0v3c0 1 0 1-1 1-1 1 0 1-1 1h-1-3-4c-1 0-3 1-5 2-1-1-2-2-3-2l2-2-2-1h-2c-1 2-3 4-4 6l-3-7c-1-3-1-5 0-9h0c0-1 0-1 1-2 0-1 0-1 1-2z" class="C"></path><path d="M180 334l2-2c2-2 5-3 8-4h1c5 0 9 0 14 1-3 0-5 0-7 1h-2v1c-2 2-4 2-7 2l-5 2-2-1h-2 0z" class="AC"></path><path d="M188 330c2 0 6-1 8 1-2 2-4 2-7 2 0-1-1-2-1-3z" class="C"></path><path d="M182 334c2-1 4-3 6-4 0 1 1 2 1 3l-5 2-2-1z" class="h"></path><path d="M205 329h4 0c-3 1-6 1-9 2v1h0v3c0 1 0 1-1 1-1 1 0 1-1 1h-1-3-4c-1 0-3 1-5 2-1-1-2-2-3-2l2-2 5-2c3 0 5 0 7-2v-1h2c2-1 4-1 7-1z" class="s"></path><path d="M200 332h0v3c0 1 0 1-1 1-1 1 0 1-1 1h-1v-1l1-1c1-1 1-2 2-3z" class="v"></path><path d="M175 320h4c0 1 0 2 1 2 3 1 6 0 9-1v1c-2 1-3 0-4 1h-3 0v2c2-1 2-1 3-1h7l4 1c-4 0-14-1-17 2 0 2 1 4 1 7h0c-1 2-3 4-4 6l-3-7c-1-3-1-5 0-9h0c0-1 0-1 1-2 0-1 0-1 1-2z" class="r"></path><path d="M466 183c1-1 4-1 5-1l-1 2 1 3c1 0 2-1 3-1 1 1 1 2 2 2 2 0 3 1 4 2l6 2c1 0 2 0 2 1l1 1c-2 1-4 2-7 2v2h0c-1 1-1 1-2 1-5 1-11 5-13 10v1c-9 6-16 14-20 24-2 3-2 6-3 9l-2 1v2l-1-1 1-3c-1-6-1-10-4-16l-2-2c-1-1 0 0-1-2h0c0-2-2-4-2-5 1 0 2 1 4 0-1-1-2-2-3-4-1-1-1-1-1-3l-1-1c1-1 2-2 3-1l1 1h0c1-1 3-1 4-2l4-1h0l-1-1h-1l-1-1c4-1 8-3 11-4l1-2c0-1 2-2 2-3l11-12z" class="M"></path><path d="M433 210h1c3 2 7 4 11 5h0c3 1 6 0 9 1-3 4-6 7-8 12v-2h0c1-3 2-5 4-8-2 0-6 1-8 0s-2 0-4-1h-1c-1-1-2-2-3-4-1-1-1-1-1-3z" class="F"></path><defs><linearGradient id="X" x1="436.924" y1="228.034" x2="449.777" y2="220.756" xlink:href="#B"><stop offset="0" stop-color="#232427"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#X)" d="M437 217h1c2 1 2 0 4 1s6 0 8 0c-2 3-3 5-4 8h0v2l-4 14c-1-6-1-10-4-16l-2-2c-1-1 0 0-1-2h0c0-2-2-4-2-5 1 0 2 1 4 0z"></path><path d="M480 190l6 2c1 0 2 0 2 1l1 1c-2 1-4 2-7 2-4 2-8 3-11 5-6 3-12 9-19 10-5 1-11-1-16-2 1-1 3-1 4-2 1 1 2 1 4 2 6-1 12-4 17-8 0-1 0-1 1-1v-1c2 0 4-1 5-2 4-3 8-6 13-7z" class="D"></path><path d="M480 190l6 2v1c-1 1-3 2-4 2-4 2-12 6-16 6v-2l1-2c4-3 8-6 13-7z" class="J"></path><path d="M466 199c1 0 2-1 3-1 2-2 7-5 10-5h0l1 1h-2l1 1 1-1 2 1c-4 2-12 6-16 6v-2z" class="e"></path><path d="M466 183c1-1 4-1 5-1l-1 2 1 3c1 0 2-1 3-1 1 1 1 2 2 2 2 0 3 1 4 2-5 1-9 4-13 7-1 1-3 2-5 2v1c-1 0-1 0-1 1-5 4-11 7-17 8-2-1-3-1-4-2l4-1h0l-1-1h-1l-1-1c4-1 8-3 11-4l1-2c0-1 2-2 2-3l11-12z" class="W"></path><path d="M452 200h3c-3 2-7 5-11 6l-1-1h-1l-1-1c4-1 8-3 11-4z" class="Q"></path><path d="M474 186c1 1 1 2 2 2 2 0 3 1 4 2-5 1-9 4-13 7-1 1-3 2-5 2 3-4 7-6 11-10h-1c1-1 2-2 2-3z" class="C"></path><path d="M466 183c1-1 4-1 5-1l-1 2 1 3c1 0 2-1 3-1 0 1-1 2-2 3l-17 11h-3l1-2c0-1 2-2 2-3l11-12z" class="D"></path><path d="M466 183c1-1 4-1 5-1l-1 2c-6 6-10 11-17 14 0-1 2-2 2-3l11-12z" class="n"></path><defs><linearGradient id="Y" x1="699.776" y1="125.173" x2="696.274" y2="155.977" xlink:href="#B"><stop offset="0" stop-color="#c7c6c7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Y)" d="M576 108l2-1c1 0 2 0 2 1h1c0 9 3 15 9 21 9 8 20 12 31 14s21 2 32 2h32l108 1 1 1c7 0 23-2 28 2-6 0-11 0-17-1h-5c-3 0-5 1-7 0-1 0-2 1-3 1h0l-47 1H638v2l2 2 4 3c-3 2-5 1-8 1-1 0-3-2-3-2-4-3-8-6-13-8h-2l-9-3c-4-2-9-4-13-7-2-1-5-3-8-5h1l-3-2c-1-2-2-3-4-3l-2-2v-1l-1-1c0-1 0-2-1-3 0-2-1-3-1-4 0-2 0-2-1-3v-1c-2 1-3 3-4 4-1-1-2-1-3-2l3-5 4-2z"></path><path d="M576 108l2-1c1 0 2 0 2 1s0 4 1 5h-1 0c0-2 0-1-1-2 0 2-1 4-2 6 0-2 0-2-1-3v-1c-2 1-3 3-4 4-1-1-2-1-3-2l3-5 4-2z" class="p"></path><path d="M576 113l-1-1h0c-1-1 0-1-1-2l1-1h2c1 0 1 1 2 2 0 2-1 4-2 6 0-2 0-2-1-3v-1z" class="o"></path><path d="M620 148c3 0 4 0 6 1 4 1 9 1 12 1v2l2 2 4 3c-3 2-5 1-8 1-1 0-3-2-3-2-4-3-8-6-13-8z" class="U"></path><path d="M640 154c-1 1-1 1-2 1-2 0-3-1-4-2h0c1 0 3 0 4-1h0l2 2z" class="g"></path><path d="M551 481c2-1 4-1 7-1l11-2h5 2c-1 1-3 2-3 3-2 2-3 3-3 5 1 3 2 5 4 7h1c1 1 2 2 4 2 1 1 1 1 2 1h1c1 1 1 3 1 4l2-1c2 7 4 13 5 20 0 2 1 6 0 7-2-1-7-3-9-2-4 0-7 2-10 4-2 1-3 3-3 5-1 2-2 3-4 4v1c-1 1-2 1-3 2l-3-3c-2-1-3 0-5-2l9-20c1-3 3-8 3-10v-3c-3-6-7-16-13-19-2-1-6 0-8-2h4 3z" class="I"></path><path d="M566 533h2c-1 2-2 3-4 4v1c-1 1-2 1-3 2l-3-3c4-1 6-2 8-4z" class="i"></path><path d="M562 526c2 0 4 0 6 1l1 1c-1 2-2 2-3 3l-1 1h-1c-1 0-2-1-3-2l1-4z" class="g"></path><path d="M562 526c0-3 1-5 2-8 1-2 1-3 3-5-1 3-2 8-1 11l1 1 1 1v1c-2-1-4-1-6-1z" class="D"></path><path d="M551 481c2-1 4-1 7-1l11-2h5 2c-1 1-3 2-3 3-2 2-3 3-3 5 1 3 2 5 4 7h0c1 4 3 8 2 12l-1 2h-3c-2 0-2-1-3-2v-1c-3-6-6-13-10-19-2-3-5-3-8-4z" class="i"></path><defs><linearGradient id="Z" x1="589.695" y1="517.326" x2="568.716" y2="516.712" xlink:href="#B"><stop offset="0" stop-color="#37383b"></stop><stop offset="1" stop-color="#62656b"></stop></linearGradient></defs><path fill="url(#Z)" d="M574 493h1c1 1 2 2 4 2 1 1 1 1 2 1h1c1 1 1 3 1 4l2-1c2 7 4 13 5 20 0 2 1 6 0 7-2-1-7-3-9-2-4 0-7 2-10 4-2 1-3 3-3 5h-2l-1-1 1-1c1-1 2-1 3-3l-1-1v-1l-1-1-1-1c-1-3 0-8 1-11s1-6 2-9v1c1 1 1 2 3 2h3l1-2c1-4-1-8-2-12h0z"></path><path d="M569 504v1 4l1 1c0 4-1 8 0 11h0l1-5h1c1 2 0 8-1 10v2c-2 1-3 3-3 5h-2l-1-1 1-1c1-1 2-1 3-3l-1-1v-1l-1-1-1-1c-1-3 0-8 1-11s1-6 2-9z" class="O"></path><path d="M574 493h1c1 1 2 2 4 2 1 1 1 1 2 1h1c1 1 1 3 1 4l2-1c2 7 4 13 5 20h-1v-1c-1 0-1 0-2-1l-3-3v2l1 1h1v1c1 1 0 1 0 2-4-5-8-8-11-13l1-2c1-4-1-8-2-12h0z" class="N"></path><path d="M577 506v-2l2 1c3 4 7 8 10 12-3-2-11-7-12-11z" class="f"></path><path d="M574 493h1c1 1 2 2 4 2 1 1 1 1 2 1h1c1 1 1 3 1 4l1 1c0 2 2 4 1 6-2 1-3 0-5-1l-1-1h0l-2-1v2l-1-1c1-4-1-8-2-12h0z" class="V"></path><path d="M151 264l3-3c1 1 1 2 1 4v1h1l-2 3-1 1-2 2c0 1 0 1-1 2 0 0-1 1-2 1l-1 3 1 1 1-1v1l-3 6v1c1 1 1 0 1 1l-2 4c-3 8-5 22-2 30 1 3 4 5 6 8 6 7 9 13 10 22 0 3 0 7-2 10-3 8-7 13-14 16l-6 2c3 3 6 6 10 8 9 6 19 8 30 7 1 0 2 0 4-1h2 1l-2 1 1 1c-1 2-4 3-6 4l-1 1c2 1 2 1 3 1l1-1h0c1 1 1 1 2 0h1 1l1-1h1 1 1c1-1 1-1 3-1l3-2c1-1 1-1 2-1v-1h2v1h-1c-7 6-20 8-28 7-16-1-29-11-40-22-2-2-5-4-7-7l3 2s1 1 2 1c5 1 12-1 17-4s8-8 9-14c2-8 0-16-4-23-2-3-5-7-8-10-4-6-4-12-5-19 0-4 0-9 1-13 0-4 2-8 3-13 0-1 2-3 3-4h1v-2l-1-1-7-3c1 0 1-1 2-1-3-2-5-3-7-6 1 0 2 1 3 1v-2l3 2h2c3 0 9 3 11 3v-3h1z" class="u"></path><path d="M138 269h4c1 2 1 2 1 4l-7-3c1 0 1-1 2-1z" class="AF"></path><path d="M151 264l3-3c1 1 1 2 1 4v1h1l-2 3-1 1-2 2c0 1 0 1-1 2 0 0-1 1-2 1 0-1 0-2 1-2l-1-2c1 0 1 0 2-1v-1l-4-1c0 1-1 1-1 2-2-1-2-1-2-3-3 0-4-1-6-2v-1h2c3 0 9 3 11 3v-3h1z" class="s"></path><path d="M144 290l-3 11c-1 2 0 3-2 5 0-1-1-1-1-2-2-4 4-20 5-25 0-1 0-1 1-2h1v3l1 1v-1c0-1 0-1 1-2l1 1 1-1v1l-3 6v1c1 1 1 0 1 1l-2 4-1-1z" class="z"></path><path d="M146 281v-1c0-1 0-1 1-2l1 1 1-1v1l-3 6v1c1 1 1 0 1 1l-2 4-1-1c1-3 1-6 2-9z" class="r"></path><path d="M571 528c3-2 6-4 10-4 2-1 7 1 9 2v1l1 1v8h0c-2 3 0 7-2 10h0c0 1 0 2-1 4h0c-2 2-3 4-6 5-1-1-2-1-2-2-1 0-1 0-1 1s0 1 1 2v3l-1 1v1c-1 1-5 5-7 5h-1c-1 1-2 1-3 1-6 0-10-5-14-9h-2l-3-2c-2 2-2 3-2 4v1l-1 1h-1l-2-1 4-10 2-5c1-2 3-6 3-9 1 0-1-3-2-4h1l2 2h0c2 2 3 1 5 2l3 3c1-1 2-1 3-2v-1c2-1 3-2 4-4 0-2 1-4 3-5z" class="a"></path><path d="M554 544l1-2 1-1 2 2c-2 1-2 1-4 1z" class="F"></path><path d="M553 535h0c2 2 3 1 5 2l3 3c-2 0-3 0-5-1s-3-2-3-4z" class="M"></path><path d="M571 528c3-2 6-4 10-4 2-1 7 1 9 2v1c-6-2-11-1-17 3-3 2-5 6-9 8v-1c2-1 3-2 4-4 0-2 1-4 3-5z" class="X"></path><path d="M558 543l9-2-1 1c-2 3-5 5-7 10 0 1-1 2-2 3l1 1-2 1-1-1-4-2-1-1c0-2 1-3 2-5 0-1 1-2 2-4 2 0 2 0 4-1z" class="K"></path><path d="M552 548c1 1 1 1 1 2 1 1 0 3 1 4 0 1 1 0 1 1v1l-4-2-1-1c0-2 1-3 2-5z" class="O"></path><path d="M570 538c3-3 8-6 12-7 1 0 3 0 4 1-1 5-6 9-10 11l-3-1-2 1c0-1 1-2 2-3-2 1-4 2-5 2 1-2 2-2 2-4z" class="b"></path><path d="M573 540h0c1-1 2-2 3-2 3-3 7-5 10-6-1 1-3 5-5 5h-1l-6 4s-1 0-1 1l-2 1c0-1 1-2 2-3z" class="N"></path><path d="M570 538c0 2-1 2-2 4 1 0 3-1 5-2-1 1-2 2-2 3l2-1 3 1c-2 2-5 3-8 4-1 1-1 2-1 3-1 0-2 1-2 2h0c-2 2-7 6-10 6h-1v-1h0l-2-2-1-1 4 2 1 1 2-1-1-1c1-1 2-2 2-3 2-5 5-7 7-10l1-1 3-3z" class="g"></path><path d="M568 542c1 0 3-1 5-2-1 1-2 2-2 3h-1c-1 1-2 1-3 1l1-2z" class="V"></path><path d="M558 556c1-1 2-3 3-4 2-2 4-3 7-5-1 1-1 2-1 3-1 0-2 1-2 2h0c-2 2-7 6-10 6h-1v-1h0l-2-2-1-1 4 2 1 1 2-1z" class="h"></path><path d="M565 552c8-4 15-7 21-13 1-2 3-3 3-5l1-1c0 1 1 2 1 3h0c-2 3 0 7-2 10h0c0 1 0 2-1 4h0c-2 2-3 4-6 5-1-1-2-1-2-2-1 0-1 0-1 1s0 1 1 2v3l-1 1v1c-1 1-5 5-7 5h-1c-1 1-2 1-3 1-6 0-10-5-14-9h0 1c3 0 8-4 10-6z" class="K"></path><path d="M577 555h1v1c-1 1-1 3-2 4l-1 1c-1 2-3 3-6 4l-1-1c0-1 0-2 1-2 1-3 5-6 8-7z" class="B"></path><path d="M568 91c2 0 4 0 5 1l1 2h1l3-3c0 1 0 2-1 2-2 3-3 6-4 9h1 0c1 1 1 2 2 3v3l-4 2-3 5c1 1 2 1 3 2 1-1 2-3 4-4v1c1 1 1 1 1 3 0 1 1 2 1 4 1 1 1 2 1 3l1 1v1l2 2c2 0 3 1 4 3v3 1l-1-1-1 1c1 3 1 6 2 9 0 3 2 6 4 8v1l2 2h-3v1l3 3h1l1-1 1 2v2c1 0 1 0 2-1 0 1 1 2 1 4 0-1-1-2-2-2-1-1-2 0-3-1l-2-2c-1-1-3-2-4-3-2-2-4-4-7-6h-1c-1 1-1 1-3 1-1 1-1 2-2 3l-2-3c-3-2-5-5-7-8l-2-3c-4-8-9-17-11-25-1-4 1-9 4-12 1-1 1-1 1-3h2v-1c1 0 2-1 3-1h1 1v1c1 0 1-1 1-1 0-2 1-3 0-5 0-1 2-2 3-3z" class="j"></path><path d="M559 123v1c1-1 1-1 1-2 1-2 3-3 4-5h1v1c-3 3-2 12-2 17v1c-2-1-1-3-2-4-2-2-2-4-2-6 0-1-1-1-1-2l1-1z" class="P"></path><path d="M563 141c0-2 1-2 0-3v-1l2-1 1 1-1 2c0 1 0 1 1 2 0-2 1-3 1-4l-1-1c-1-3 0-8 0-11 0-2-1-4-1-5 0 0 0-1 1-1l1-1c0 4 0 7 1 11v2c0 3 1 5 1 8s0 5 1 8h0c0 2 1 3 2 4v1c-3-2-5-5-7-8l-2-3z" class="Y"></path><path d="M568 113c1-4 3-8 5-11h1 0c1 1 1 2 2 3v3l-4 2-3 5c1 1 2 1 3 2-1 4-2 8-2 12v3l-2-1v-2c-1-4-1-7-1-11 0-1 0-2 1-2v-3z" class="T"></path><path d="M574 102c1 1 1 2 2 3v3l-4 2 2-8h0z" class="AA"></path><path d="M569 115c1 1 2 1 3 2-1 4-2 8-2 12l-2-1c0-3 0-6 1-9v-4z" class="d"></path><path d="M559 100c1 0 2-1 3-1h1 1v1c-1 3-2 7-3 10 0 2 0 2-1 3 1 1 1 1 2 0 1-2 3-5 5-7 0 2 0 3-1 4l-2 2c0 1 1 2 2 2-1 1-2 1-3 2-1 2-3 2-4 4h-1v-1h-1l1 1v1c0 1 0 1 1 2l-1 1c-2-6-3-11-2-16v-3-1c1-1 1-1 1-3h2v-1z" class="G"></path><path d="M560 113c1 1 1 1 2 0 1-2 3-5 5-7 0 2 0 3-1 4l-2 2c0 1 1 2 2 2-1 1-2 1-3 2h0c-2 1-4 2-6 1-1-2 0-4 0-6v1c1 1 1 1 1 2h1c0-1 0 0 1-1z" class="l"></path><path d="M559 100c1 0 2-1 3-1h1 1v1c-1 3-2 7-3 10 0 2 0 2-1 3s-1 0-1 1h-1c0-1 0-1-1-2v-1c0-1 1-3 1-5 1-1 1-3 1-5v-1z" class="i"></path><path d="M568 91c2 0 4 0 5 1l1 2h1l3-3c0 1 0 2-1 2-2 3-3 6-4 9-2 3-4 7-5 11l-2 1c-1 0-2-1-2-2l2-2c1-1 1-2 1-4-2 2-4 5-5 7-1 1-1 1-2 0 1-1 1-1 1-3 1-3 2-7 3-10 1 0 1-1 1-1 0-2 1-3 0-5 0-1 2-2 3-3z" class="H"></path><path d="M561 110c1-1 2-2 2-3 1-1 1-1 1-2 1-1 2-3 3-5 0-1 1-3 2-5 1-1 2 0 3 0v1c-1 1-2 2-2 4h0c-1 2-3 4-3 6-2 2-4 5-5 7-1 1-1 1-2 0 1-1 1-1 1-3z" class="k"></path><path d="M578 91c0 1 0 2-1 2-2 3-3 6-4 9-2 3-4 7-5 11l-2 1c-1 0-2-1-2-2l2-2c1-1 1-2 1-4s2-4 3-6 3-4 5-6l3-3z" class="p"></path><path d="M572 117c1-1 2-3 4-4v1c1 1 1 1 1 3 0 1 1 2 1 4 1 1 1 2 1 3l1 1v1l2 2c2 0 3 1 4 3v3 1l-1-1-1 1c1 3 1 6 2 9 0 3 2 6 4 8v1l2 2h-3v1l3 3h1l1-1 1 2v2c1 0 1 0 2-1 0 1 1 2 1 4 0-1-1-2-2-2-1-1-2 0-3-1l-2-2c-1-1-3-2-4-3-2-2-4-4-7-6h-1c-1 1-1 1-3 1-1 1-1 2-2 3l-2-3v-1c-1-1-2-2-2-4h0c-1-3-1-5-1-8s-1-5-1-8l2 1v-3c0-4 1-8 2-12z" class="j"></path><path d="M576 114c1 1 1 1 1 3 0 1 1 2 1 4-1 0-2 0-2 1 0 2 0 1-1 2v3l-1 1c0 1 0 1 1 3v2l1 3-1 1c0-1-1-1-1-2 0-2-1-4-1-7l-2 1c0-2 0-4 1-6s3-4 4-6v-3z" class="n"></path><path d="M582 128c2 0 3 1 4 3v3 1l-1-1-1 1c1 3 1 6 2 9 0 3 2 6 4 8v1 1c-2-2-2-2-4-2v-1c0-1 0-2-1-3s-4-4-4-5h0c-1-2-1-3-1-5v-1c0-4 0-6 2-9z" class="Y"></path><path d="M572 117c1-1 2-3 4-4v1 3c-1 2-3 4-4 6s-1 4-1 6c0 3 1 6 2 8 1 3 3 5 4 8l2 5h-1-2l-1-1-1-1c0-1-1-1-1-2h0c-1-3-2-5-3-7h-1c0-3-1-5-1-8l2 1v-3c0-4 1-8 2-12z" class="K"></path><path d="M568 131l2 1c1 3 1 5 2 8h1c1 4 3 7 5 10h-2l-1-1-1-1c0-1-1-1-1-2h0c-1-3-2-5-3-7h-1c0-3-1-5-1-8z" class="f"></path><path d="M569 139h1c1 2 2 4 3 7h0c0 1 1 1 1 2l1 1 1 1h2 1l-2-5c2 1 3 2 4 3s2 2 3 2 1 1 2 1v1c2 0 2 0 4 2v-1l2 2h-3v1l3 3h1l1-1 1 2v2c1 0 1 0 2-1 0 1 1 2 1 4 0-1-1-2-2-2-1-1-2 0-3-1l-2-2c-1-1-3-2-4-3-2-2-4-4-7-6h-1c-1 1-1 1-3 1-1 1-1 2-2 3l-2-3v-1c-1-1-2-2-2-4h0c-1-3-1-5-1-8z" class="L"></path><path d="M623 658l1 4h1l1-2-72 180c-1 1-2 1-2 2-1 1-2 3-2 5l-1 1h0c0 2 0 1-1 2 0 1 0 2-1 4v1l-3 6-1 1h0-2v-1s0-1 1-1c0-2 1-3 1-4l4-9c1-2 2-3 1-5l26-63c-2 0-2 1-3 3v-2c1-2 1-3 1-4v-1c1-2 1-5 1-8 4-2 6-9 7-12-2-2-3-2-4-5 0-1-1-2-1-3l1-1-1-1v-1c-1 0-1 1-2 2v-2c1-1 0-2 0-3 0 1-1 2-2 3l5-14c-1-1-2 0-4 0l3-3 1-1h4c1 1 1 2 1 3l-1 2c1 1 2 2 4 2 2-2 2-5 4-7h0v3l4-7 1-3c0-1 1-2 1-3h1v4c1-3 5-14 7-15 1 1-1 5-1 7v1l14-36 3-6 5-13z" class="u"></path><defs><linearGradient id="a" x1="583.272" y1="760.369" x2="575.345" y2="759.22" xlink:href="#B"><stop offset="0" stop-color="#322d2c"></stop><stop offset="1" stop-color="#414446"></stop></linearGradient></defs><path fill="url(#a)" d="M595 716v4c1-3 5-14 7-15 1 1-1 5-1 7v1l-7 17c-1 1-1 4-2 5l-14 33-3 9c-1 0-1 1-1 2-2 0-2 1-3 3v-2c1-2 1-3 1-4v-1c1-2 1-5 1-8 4-2 6-9 7-12-2-2-3-2-4-5 0-1-1-2-1-3l1-1-1-1v-1c-1 0-1 1-2 2v-2c1-1 0-2 0-3 0 1-1 2-2 3l5-14c-1-1-2 0-4 0l3-3 1-1h4c1 1 1 2 1 3l-1 2c1 1 2 2 4 2 2-2 2-5 4-7h0v3l4-7 1-3c0-1 1-2 1-3h1z"></path><path d="M595 716v4c1-3 5-14 7-15 1 1-1 5-1 7v1l-7 17c-1 1-1 4-2 5-2 0-4 6-5 8h-1c1-5 4-11 6-15l-1-1c-2 2-2 5-3 8h0l-1-1-2 2h0c1-2 2-4 3-7h0l4-7 1-3c0-1 1-2 1-3h1z" class="G"></path><defs><linearGradient id="b" x1="577.56" y1="728.974" x2="586.763" y2="750.122" xlink:href="#B"><stop offset="0" stop-color="#2f2f32"></stop><stop offset="1" stop-color="#5b5e63"></stop></linearGradient></defs><path fill="url(#b)" d="M580 731c1 1 2 2 4 2 2-2 2-5 4-7h0v3h0c-1 3-2 5-3 7h0l2-2 1 1h0l-8 20c-2-2-3-2-4-5 0-1-1-2-1-3l1-1-1-1v-1c-1 0-1 1-2 2v-2c1-1 0-2 0-3 0 1-1 2-2 3l5-14c-1-1-2 0-4 0l3-3 1-1h4c1 1 1 2 1 3l-1 2z"></path><path d="M576 726h4c1 1 1 2 1 3l-1 2v-1c-1-1-1-2 0-3l-1-1c-1 1-2 3-3 4-1-1-2 0-4 0l3-3 1-1z" class="I"></path><path d="M757 327c2-1 3-1 5-1l15 6-3 2h0l-2 1c-2 2-3 3-5 4-4 4-10 8-13 12-5 5-8 10-13 14l-10 16-6 12c-1 1-2 3-2 4h-2c-1 2-1 3-3 3s-3 0-5-1c-1-2-1-3 0-5l-1-1c0-3 1-5 3-8h-1v-1l-1-1 4-7 1-1c0-2 2-4 2-5 0 0-1-1-2-1l1-1c0-1-1-1-3-1l1-1c-1-1 0-1-1-1v-1c0-1 1 0 2-1 1-2 1-3 3-4l1-1h0v-2l2-2h1 0l2 2h0c1-1 2-2 2-3 1-2 1-3 3-4 1-1 2-1 4-1l5-6c1-1 2-1 3-2h1v-2c3-4 8-8 12-11z" class="n"></path><path d="M742 348h1 0l-2 5h-1c-1 2-1 4 0 6v1l1 1h0-1c-2-2-3-4-3-7-1-1-1 0-2 0h-1c0-1 1-2 2-4 3 0 4 0 6-2z" class="T"></path><path d="M720 393l3-1 2-4c-1 0-1-1-2-1v-1c1-2 2-2 4-3h1c1-1 2-1 3-2l-6 12c-1 1-2 3-2 4h-2c0-1 0-3-1-4z" class="l"></path><path d="M713 394l2-3c3 0 3 0 5 2 1 1 1 3 1 4-1 2-1 3-3 3s-3 0-5-1c-1-2-1-3 0-5z" class="C"></path><path d="M717 382c1-3 3-7 5-9 0-1 1-1 1-2 1-1 1-2 2-2v1c-1 1-1 1-1 2 2-1 3-2 5-2 1 1 2 1 3 3 0 1 1 3 0 5l-3 3-2-1c-2 1-5-1-6-1-1 1-3 2-4 3z" class="P"></path><path d="M724 372c2-1 3-2 5-2 1 1 2 1 3 3 0 1 1 3 0 5l-3 3-2-1c-1 0-2 0-2-1-1-1-1-2-1-4l2 3h2c1-1 2-3 3-5l-1-1c-1-1-1-1-2-1s-2 2-3 3h-1v-2z" class="C"></path><path d="M744 340h1v-2c3-4 8-8 12-11 0 3-1 3-3 5l1 2v1c-1 2-1 2 0 4l-2-2c-2 1-5 5-6 4h-1c1 1 1 3 1 4h0l-2 1c1 2 2 1 2 2 2 1 3 3 2 5l-1 1-1 1-2 2c-2-1-3-2-4-4l2-5h0-1c-2 2-3 2-6 2-1 2-2 3-2 4-2 2-4 7-6 8-1 3-2 4-3 7-1 0-1 1-2 2 0 1-1 1-1 2-2 2-4 6-5 9l-2 3h-1v-1l-1-1 4-7 1-1c0-2 2-4 2-5 0 0-1-1-2-1l1-1c0-1-1-1-3-1l1-1c-1-1 0-1-1-1v-1c0-1 1 0 2-1 1-2 1-3 3-4l1-1h0v-2l2-2h1 0l2 2h0c1-1 2-2 2-3 1-2 1-3 3-4 1-1 2-1 4-1l5-6c1-1 2-1 3-2z" class="M"></path><path d="M736 350l4-4c1 2 1 2 2 2-2 2-3 2-6 2z" class="U"></path><path d="M744 348h2v1c2 2 2 2 2 4v1c-1 0-2-1-3-1-1-2-1-3-1-5z" class="l"></path><path d="M743 348h1c0 2 0 3 1 5 1 0 2 1 3 1h0l-1 1-2 2c-2-1-3-2-4-4l2-5z" class="F"></path><path d="M754 332l1 2v1c-1 2-1 2 0 4l-2-2c-2 1-5 5-6 4h-1c1-2 3-3 4-5 1-1 2-3 4-4z" class="X"></path><path d="M718 375c2-2 3-4 4-6s4-5 6-7c-1 3-2 4-3 7-1 0-1 1-2 2 0 1-1 1-1 2-2 2-4 6-5 9l-2 3h-1v-1l-1-1 4-7 1-1z" class="f"></path><path d="M732 349c1-1 2-1 4-1-6 6-12 14-16 22 0 0-1-1-2-1l1-1c0-1-1-1-3-1l1-1c-1-1 0-1-1-1v-1c0-1 1 0 2-1 1-2 1-3 3-4l1-1h0v-2l2-2h1 0l2 2h0c1-1 2-2 2-3 1-2 1-3 3-4z" class="I"></path><path d="M724 354h1 0v1c-1 1-1 3-1 4 0 2-1 4-3 6-1 0-1 0-2 1v1h-3l1-1c-1-1 0-1-1-1v-1c0-1 1 0 2-1 1-2 1-3 3-4l1-1h0v-2l2-2z" class="E"></path><path d="M718 363c1-2 1-3 3-4 0 1-1 2-1 4h-2z" class="I"></path><path d="M757 327c2-1 3-1 5-1l15 6-3 2h0l-2 1c-2 2-3 3-5 4-4 4-10 8-13 12-5 5-8 10-13 14-1 0-1 0-2 1h0c1-2 2-4 4-6 1-1 4-2 4-5l1-1 1-1c1-2 0-4-2-5 0-1-1 0-2-2l2-1h0c0-1 0-3-1-4h1c1 1 4-3 6-4l2 2c-1-2-1-2 0-4v-1l-1-2c2-2 3-2 3-5z" class="d"></path><path d="M756 341l2 1 1 2c-2 2-4 3-6 4 1-1 1-2 2-2 1-2 1-3 1-5h0z" class="j"></path><path d="M762 326l15 6-3 2h0l-2 1c-2 2-3 3-5 4 0-1 0-1 1-2 0-1 0-1 1-2 0-2-1-1-2-3 0-1 1-2 0-3h-2l1 1c-1 1-1 2-2 3l1-1c0-1-1-1 0-2h0v-1l-2 1h-3-1c1-1 2-1 2-2s0-1 1-2z" class="Y"></path><path d="M772 335c-1-1-1-1-1-2l1-1c1 0 1 0 2 2l-2 1z" class="d"></path><path d="M757 327c2-1 3-1 5-1-1 1-1 1-1 2s-1 1-2 2h1 3l2-1v1h0c-1 1 0 1 0 2l-1 1c-1-1-1-1-3-1 2 2 3 2 3 4-1 3-2 4-4 6h-2l-2-1c0-1 0-1-1-2-1-2-1-2 0-4v-1l-1-2c2-2 3-2 3-5z" class="i"></path><path d="M759 330h1 3l2-1v1h0c-1 1 0 1 0 2l-1 1c-1-1-1-1-3-1-1 1-2 1-3 1l1-2h-2v-1h2z" class="P"></path><path d="M877 221c6 1 11 3 16 6h4v3 2c1 2 4 6 3 8v1c1 2 1 5 1 7l1 1h0l1-1v5 1c-1 0-2 1-2 1-1 0-1 0-2-1l-2 4v4 1c-1 1-2 2-3 2 1 1 1 1 2 1l-1 1h-2c-1 2-2 2-3 3h-1l1 1c1 0 0 0 1 1l-4 2v1c-2 2-1 4-2 7h-1c0 1 0 2-1 4l-2-5c-1-2-2-3-2-4-3-3-7-5-8-9-1-1-1-4-3-5-1-1-1-3-2-4-1 0-1 0-1-1 1-5 3-9 6-13v-2l-1-1 1-1c0-2 2-4 3-6l-2 1c1-2 2-4 1-6h-3v-2h0l2-4c2-1 3 0 4 0h1v-1l1-1h0l-1-1z" class="W"></path><path d="M901 248l1 1h0l1-1v5 1c-1 0-2 1-2 1-1 0-1 0-2-1l2-6z" class="q"></path><path d="M880 242v1l-2 2-1-1c-3 0-5 3-7 5 0 1-1 2-1 2l-1 1c-1 2-2 4-1 7h-1c-1 0-1 0-1-1 1-5 3-9 6-13 1-1 3-2 4-2 2-1 3-1 5-1z" class="E"></path><path d="M875 260v-1c2 3 4 5 6 7h4c-1 1-1 2-2 2h-1c-1-1-2 0-4-1-1 0-1-1-2-2l-1 1v2h-1l-1-1v-3l1-1 1-1v1l1-1h0l-2-2h1z" class="r"></path><path d="M867 259l8 1h-1l2 2h0l-1 1v-1l-1 1-1 1v3c-1 1-1 1-2 1-1-1-1-4-3-5-1-1-1-3-2-4h1z" class="AJ"></path><path d="M877 221c6 1 11 3 16 6h4v3 2c1 2 4 6 3 8v1h-1c-1-1 0-1 0-2l-1 2v-5h-1v4c-1 0-1 0-2 1l-1 1c1 4 3 5 1 9l-2 4c-2 0-4 0-5 1 2-2 4-4 5-7 0-3-1-5-3-8h0l-3-2c0-1 0-2-1-2 0-1 0-1 1-2 1 1 1 1 3 1v-1l1-1h0l3-1c-1-1-1-2-1-3-2-4-6-5-10-6l-5-2h0l-1-1z" class="F"></path><path d="M893 227h4v3 2l-4-5z" class="r"></path><path d="M891 234l3-1c1 2 1 2 1 5l-1 1h-2v2h-2 0l-3-2c0-1 0-2-1-2 0-1 0-1 1-2 1 1 1 1 3 1v-1l1-1h0z" class="H"></path><path d="M885 266h0c5-2 9-4 12-8v4 1c-1 1-2 2-3 2 1 1 1 1 2 1l-1 1h-2c-1 2-2 2-3 3h-1l1 1c1 0 0 0 1 1l-4 2v1c-2 2-1 4-2 7h-1c0 1 0 2-1 4l-2-5c-1-2-2-3-2-4-3-3-7-5-8-9 1 0 1 0 2-1l1 1h1v-2l1-1c1 1 1 2 2 2 2 1 3 0 4 1h1c1 0 1-1 2-2z" class="s"></path><path d="M888 268c1 0 2-1 3-1h2c-1 2-2 2-3 3l-2-2zm-9 9h2c0-1-1-1 1-2l1 1h-1v1c0 2 1 3 2 5 0 1 0 2-1 4l-2-5c-1-2-2-3-2-4h0z" class="r"></path><path d="M882 276c1 1 2 1 3 1v-4c1-2 2-4 3-5l2 2h-1l1 1c1 0 0 0 1 1l-4 2v1c-2 2-1 4-2 7h-1c-1-2-2-3-2-5v-1z" class="AF"></path><path d="M873 267l1 1h1v-2l1-1c1 1 1 2 2 2 2 1 3 0 4 1l-1 1c-1 0-2 0-4-1v3h2l-1 2c0 1 1 2 1 2v2h0c-3-3-7-5-8-9 1 0 1 0 2-1z" class="AB"></path><path d="M878 222l5 2c4 1 8 2 10 6 0 1 0 2 1 3l-3 1h0l-1 1v1c-2 0-2 0-3-1-1 1-1 1-1 2h-1c-1-1-2-1-3-1h-2l-4-1h-2l-2 1c1-2 2-4 1-6h-3v-2h0l2-4c2-1 3 0 4 0h1v-1l1-1z" class="g"></path><path d="M876 235c3-1 8-2 10 0h1c-1 1-1 1-1 2h-1c-1-1-2-1-3-1h-2l-4-1z" class="B"></path><path d="M886 229l-1 1h0-1c-3 0-5-1-8-1h0c2-1 3-2 4-3l-1-2h1l3 1h1v1h-1l1 1 2 2z" class="I"></path><path d="M883 224c4 1 8 2 10 6 0 1 0 2 1 3l-3 1v-2l-1-2c-2-1-2-1-4-1l-2-2-1-1h1v-1h-1v-1z" class="S"></path><path d="M874 235h2l4 1h2c1 0 2 0 3 1h1c1 0 1 1 1 2l3 2h0c2 3 3 5 3 8-1 3-3 5-5 7h-4c-2 0-4-2-5-4s-1-4-1-7l2-2v-1c-2 0-3 0-5 1-1 0-3 1-4 2v-2l-1-1 1-1c0-2 2-4 3-6z" class="f"></path><path d="M880 241c3 0 5 0 7 2l2 1c1 3 1 5 1 8-1 1-2 3-4 3s-4 0-5-2c-1 0-1-1-1-1v-1h1l1 2c1 0 2 1 2 1 1 0 2 0 2-1 3-2 3-3 3-6-1-1-2-3-3-4-2-1-4 0-6 0v-1-1z" class="M"></path><path d="M889 244h0c0-2-2-2-3-3l1-1 2 2 1-1h0c2 3 3 5 3 8-1 3-3 5-5 7h-4c-2 0-4-2-5-4h1s0 1 1 1c1 2 3 2 5 2s3-2 4-3c0-3 0-5-1-8z" class="X"></path><path d="M880 251c-1-1-1-1-1-2v-1-1c1-1 1-2 2-2l1-1c1 1 1 0 1 1v3h1v-1l2-2 2 3-2 2 1 1-1 1-1-1c-1 0-2 1-3 2l-1-2h-1z" class="U"></path><path d="M874 235h2l4 1h2c1 0 2 0 3 1h1c1 0 1 1 1 2l3 2-1 1-2-2-1 1c1 1 3 1 3 3h0l-2-1c-2-2-4-2-7-2v1c-2 0-3 0-5 1-1 0-3 1-4 2v-2l-1-1 1-1c0-2 2-4 3-6z" class="P"></path><path d="M875 243l1-1c1-1 1-1 1-2h3v1 1c-2 0-3 0-5 1z" class="f"></path><path d="M874 235h2l4 1h2c1 0 2 0 3 1h1c1 0 1 1 1 2-4-2-8-2-12 0l-4 2c0-2 2-4 3-6z" class="K"></path><path d="M560 163c0-1 0-2 1-3h1l2 1c2 1 2 1 4 0 2 1 4 3 6 4 1 2 3 3 5 4 6 2 11 4 17 6v-1c1 2 3 3 5 4 0 0 1 0 1 1h-1v1c1 1 1 2 1 3 0 0 0 1-1 1h-4c1 1 2 1 3 3h-1c1 1 1 1 1 2 1 0 1 0 2-1 1 0 0 0 1 1l-1 1-1 2c-1 2-2 4-4 5l-2 2-5 1-1 1h-1c-7 0-13 0-19-3l-6-3-1 1c-3-1-5-3-8-4-1 1-2 2-2 3h-2l-3-1c-3-1-5-2-7-3l5-2h-5c-2 0-2 0-3-1l3-1c8-2 16-8 22-14l3-5-5-5z" class="Z"></path><path d="M574 188c1-2 2-4 4-5 0 2-1 5-1 7l4 4c-3-1-5-3-7-6z" class="B"></path><path d="M589 180h1l5 2v3l-1 1c-1-1-1-1-3-1h0c-2-2-3-2-5-2h-1c1 0 2 0 4-1l-3-1h3 0v-1z" class="W"></path><defs><linearGradient id="c" x1="572.484" y1="180.42" x2="575.342" y2="189.785" xlink:href="#B"><stop offset="0" stop-color="#36393d"></stop><stop offset="1" stop-color="#5c5d62"></stop></linearGradient></defs><path fill="url(#c)" d="M565 183c2-1 5-2 7-3 3-1 11-2 14-1l1 1h-2c-2 0-3 1-5 3h-2c-2 1-3 3-4 5v3h-1 1v2c-1 0 0 0-1-1-2-1-4 0-6-1-1-1-2-1-3-1l-1-1c2-3 8-6 10-7-2 0-5 2-7 1h-1z"></path><path d="M595 182v1l2 1c1 1 2 1 3 3h-1c0 1-1 2-3 3h0c0 1-2 2-2 3-2 2-2 3-5 3s-5 0-7-2h0 3c1 1 4 1 6 1l-2-1 1-1c-2 0-2 1-4 0-2 0-4-1-5-2 0-2-1-3 0-4 0-2 3-3 5-4 2 0 3 0 5 2h0c2 0 2 0 3 1l1-1v-3z" class="C"></path><path d="M591 185h0l2 2-2 2c-1 1-1 2-2 3l-1-1-1-1v-1h1v-2c-1 0-2 1-3 0l1-2h5 0z" class="F"></path><path d="M595 182v1l2 1c1 1 2 1 3 3h-1c0 1-1 2-3 3h0c-1 1-1 1-2 1v-3l-1-1-2-2c2 0 2 0 3 1l1-1v-3z" class="Z"></path><path d="M595 183l2 1c1 1 2 1 3 3h-1c0 1-1 2-3 3l1-3c-1-1-1-3-2-4z" class="C"></path><path d="M565 183h1c2 1 5-1 7-1-2 1-8 4-10 7l1 1 3 3-1 1c1 0 3 1 4 2 0 1 0 2-1 2l-6-3-1 1c-3-1-5-3-8-4-1 1-2 2-2 3h-2l-3-1c-3-1-5-2-7-3l5-2c2 0 6-2 8-3l-1 1-1 1h3c1-1 2 0 3 0 2-1 5-3 7-4l1-1z" class="Q"></path><path d="M557 191c3 0 6 1 9 3 1 0 3 1 4 2 0 1 0 2-1 2l-6-3-6-4z" class="K"></path><path d="M547 194v-1c3-1 7-3 10-2l6 4-1 1c-3-1-5-3-8-4-1 1-2 2-2 3h-2l-3-1z" class="g"></path><path d="M599 187c1 1 1 1 1 2 1 0 1 0 2-1 1 0 0 0 1 1l-1 1-1 2c-1 2-2 4-4 5l-2 2-5 1-1 1h-1c-7 0-13 0-19-3 1 0 1-1 1-2-1-1-3-2-4-2l1-1-3-3c1 0 2 0 3 1 2 1 4 0 6 1 1 1 0 1 1 1v-2h-1 1v-3c2 3 4 5 7 6h1 0c2 2 4 2 7 2s3-1 5-3c0-1 2-2 2-3h0c2-1 3-2 3-3z" class="R"></path><path d="M601 192c-1 2-2 4-4 5l-2 2-5 1-1 1c-1 0-2-1-2-1h2l-4-1c3 0 5 0 8-2l3-1c2-1 3-2 5-4z" class="a"></path><path d="M599 187c1 1 1 1 1 2 1 0 1 0 2-1 1 0 0 0 1 1l-1 1-1 2c-2 2-3 3-5 4l-3 1h-3c1 0 2-1 3-1 1-1 1-2 1-3s2-2 2-3h0c2-1 3-2 3-3z" class="B"></path><path d="M574 188c2 3 4 5 7 6h1 0c2 2 4 2 7 2s3-1 5-3c0 1 0 2-1 3-1 0-2 1-3 1-2 1-5 2-8 1-3 0-6-1-8-2l-7-3-3-3c1 0 2 0 3 1 2 1 4 0 6 1 1 1 0 1 1 1v-2h-1 1v-3z" class="J"></path><path d="M574 188c2 3 4 5 7 6h1 0c2 2 4 2 7 2s3-1 5-3c0 1 0 2-1 3-1 0-2 1-3 1-2 1-5 2-8 1h3l-1-1c-4-1-6-3-10-4v-2h-1 1v-3z" class="V"></path><defs><linearGradient id="d" x1="587.789" y1="190.188" x2="556.522" y2="169.536" xlink:href="#B"><stop offset="0" stop-color="#646668"></stop><stop offset="1" stop-color="#8c8d8f"></stop></linearGradient></defs><path fill="url(#d)" d="M560 163c0-1 0-2 1-3h1l2 1c2 1 2 1 4 0 2 1 4 3 6 4 1 2 3 3 5 4 6 2 11 4 17 6v-1c1 2 3 3 5 4 0 0 1 0 1 1h-1v1c1 1 1 2 1 3 0 0 0 1-1 1h-4l-2-1v-1l-5-2h-1l-2-1-13-6c-1-1-3-2-3-1-3 1-4 4-6 6-4 3-8 5-12 8h0c-2 1-6 3-8 3h-5c-2 0-2 0-3-1l3-1c8-2 16-8 22-14l3-5-5-5z"></path><path d="M589 178l5 1 1 1h-5-1l-2-1 2-1z" class="b"></path><path d="M574 173c2 0 3 0 5 1 1 1 2 1 2 1h0c1 1 2 1 3 1h0c2 0 4 0 5 2l-2 1-13-6z" class="J"></path><path d="M596 174c1 2 3 3 5 4 0 0 1 0 1 1h-1v1c1 1 1 2 1 3 0 0 0 1-1 1h-4l-2-1v-1l-5-2h5l-1-1c2 0 2 0 4-2l-2-2v-1z" class="H"></path><path d="M598 177c2 2 2 2 3 4-1 1-1 1-2 1-2 0-3-1-4-2l-1-1c2 0 2 0 4-2z" class="J"></path><path d="M562 173v2 1c-1 1-2 2-3 4-2 1-5 3-8 5-2 2-7 4-9 3-1 0-2 0-2-1 8-2 16-8 22-14z" class="U"></path><defs><linearGradient id="e" x1="658.099" y1="528.168" x2="687.22" y2="543.735" xlink:href="#B"><stop offset="0" stop-color="#c4bdb3"></stop><stop offset="1" stop-color="#ffe3b9"></stop></linearGradient></defs><path fill="url(#e)" d="M725 399c1 5-4 8-3 12l1 1c1-1 1-2 1-3l2-3v1L626 660l-1 2h-1l-1-4 47-120h0c-1 0-2 4-3 5h-1l3-9h-1l6-17c1 0 2-2 2-2l1-4c2-7 7-14 8-21 1-3 1-4 3-6 2-5 4-10 6-14 2-6 5-12 7-18 0-2 3-6 2-9v-1l1-1h2c0-2 1-4 2-6 0-1 1-2 2-3h0v1c-1 1 0 2 0 3v1l15-38z"></path><path d="M704 441h2c0-2 1-4 2-6 0-1 1-2 2-3h0v1c-1 1 0 2 0 3v1l-21 52c-2 7-4 13-7 19-1 3-2 6-4 9l-2 7-6 14h0c-1 0-2 4-3 5h-1l3-9h-1l6-17c1 0 2-2 2-2l1-4c2-7 7-14 8-21 1-3 1-4 3-6 2-5 4-10 6-14 2-6 5-12 7-18 0-2 3-6 2-9v-1l1-1z" class="AG"></path><path d="M678 512v5l-2 7c-1-1-1-2-2-3l3-6 1-3z" class="B"></path><path d="M674 521c1 1 1 2 2 3l-6 14h0c-1 0-2 4-3 5h-1l3-9c1-4 3-8 5-13z" class="H"></path><path d="M678 512l6-15c1-2 2-5 2-7v-1l1 2c-1 1-1 3-1 4h0l3-6h0c-2 7-4 13-7 19-1 3-2 6-4 9v-5z" class="AL"></path><path d="M759 252c2-2 3-3 5-3h0c-1 1-2 2-4 3l1 1h0c2 0 4 1 5 2h0v1h0v4h0 1l1 1c1 1 2 3 4 4l-1 2h1l3 3c1 3 3 8 2 10l7 11h0c0 1 1 2 2 3s2 2 3 4l-2 1-1 1 2 4 6 6v2c2 2 7 6 10 8h1c-1 1-2 2-3 2l-2 1h0c-4 1-8 2-12 2v1c-1 1-2 1-3 1-2 0-2 0-3-2l-5-3-4-2c-1-1-4-2-5-3 0-1-1-1-1-2-2 0-3 0-4 1l-2-2 1-3c-1 0-1-1-2-1-2-2-3-4-3-6-1-3-1-5-1-8v-6l2 3 1-3h0c0-1 1-2 1-4 2-1 2-3 3-5-1 0-1-1-2-1v-3h-2c2-3 3-8 2-11h-2c-1-2-2-1-4-2h0c1-1 2-1 3-2 0-3 0-4-1-6h-1l3-4z" class="a"></path><path d="M756 290l2 3c1 1 2 1 2 3 0-1-1-1-2-1l-1-1-1 2v-6z" class="K"></path><path d="M768 298h1c1 1 1 3 0 4v2h-2c1-3 0-4 1-6z" class="D"></path><path d="M767 304c-3-3-4-4-5-8 1 1 2 3 4 4l1-2h1c-1 2 0 3-1 6z" class="R"></path><path d="M756 296l1-2 1 1v1l1 1v2c-1 1-1 0-1 1 1 2 2 5 4 7 0 1 3 2 5 2 2 1 5 1 7 2l-4 1c0 1-1 2-1 2l-2 1c-2 0-3 0-4 1l-2-2 1-3c-1 0-1-1-2-1-2-2-3-4-3-6-1-3-1-5-1-8z" class="S"></path><path d="M762 311c3 1 5 2 7 3l-2 1c-2 0-3 0-4 1l-2-2 1-3z" class="Z"></path><path d="M767 309h-3c-1 1-2 0-3-1-1 0-1-1-2-1l-1-2c0-1-1-3 0-5 1 2 2 5 4 7 0 1 3 2 5 2z" class="K"></path><path d="M774 311c3 1 5 2 7 4 2 1 3 2 5 3h0l-1 1v1 2l3 3v1c-1 1-2 1-3 1-2 0-2 0-3-2l-5-3-4-2c-1-1-4-2-5-3 0-1-1-1-1-2l2-1s1-1 1-2l4-1z" class="M"></path><path d="M774 311c3 1 5 2 7 4h-2c-3-1-4 0-7 0-1-1-1-1-2-1h-1s1-1 1-2l4-1z" class="J"></path><path d="M769 314h1c3 2 7 4 10 6l-3 2-4-2c-1-1-4-2-5-3 0-1-1-1-1-2l2-1z" class="c"></path><path d="M780 320l5 2 3 3v1c-1 1-2 1-3 1-2 0-2 0-3-2l-5-3 3-2z" class="C"></path><path d="M782 302l1-1 1 1 1-1 3 3 6 6v2c2 2 7 6 10 8h1c-1 1-2 2-3 2l-2 1h0c-4 1-8 2-12 2l-3-3v-2-1l1-1h0c1-1 2-1 4 0 1 0 2 1 3 1-1-1-3-3-5-4h0l-6-3c-2-1-4-3-6-4h-1 0l4-2 3-3v-1z" class="B"></path><path d="M779 306h2v-1h1 2 0l-2 1c0 2 1 2 2 4l1 1c-2-1-5-3-6-5z" class="J"></path><path d="M785 311l-1-1c-1-2-2-2-2-4l2-1c1 2 3 3 5 4 1 1 2 2 2 3 2 2 3 4 5 5h0-2c-3-1-6-3-9-6z" class="e"></path><path d="M786 318h1l9 3c1 1 3 1 4 2h0c-4 1-8 2-12 2l-3-3v-2-1l1-1z" class="f"></path><path d="M787 318l9 3h-1c-2 1-6 0-8 0v-3z" class="i"></path><path d="M782 302l1-1 1 1 1-1 3 3 6 6v2c2 2 7 6 10 8-1 0-2 0-3 1l-3-2c-2 0-2 0-4-2h2 0c-2-1-3-3-5-5 0-1-1-2-2-3-2-1-4-2-5-4h0-2-1v1h-2l3-3v-1z" class="V"></path><path d="M782 303l2-1v1c2 2 3 4 5 6-2-1-4-2-5-4h0-2-1v1h-2l3-3z" class="k"></path><path d="M759 252c2-2 3-3 5-3h0c-1 1-2 2-4 3l1 1h0c2 0 4 1 5 2h0v1h0v4h0 1l1 1c1 1 2 3 4 4l-1 2h1l3 3c1 3 3 8 2 10l7 11h0c0 1 1 2 2 3s2 2 3 4l-2 1-1 1 2 4-3-3-1 1-1-1-1 1h-1l-7-7c0-2 2-5 1-7-1 1-1 4-1 5l-2-1c-2-2-3-4-3-7-1-1-1-1-1-2h0c1-3-2-4-2-6 1-1 1-1 1-2 1-1 0-3 0-4l1-2-2-2c0 2 0 5-1 7 0 2 0 4-1 6l-1 1h0c-1 0-1-1-2-1v-3h-2c2-3 3-8 2-11h-2c-1-2-2-1-4-2h0c1-1 2-1 3-2 0-3 0-4-1-6h-1l3-4z" class="Q"></path><path d="M769 268c2 2 3 4 4 6l-2-1h-3c0-2 1-3 1-5z" class="N"></path><path d="M786 300h0c-1-1-1-1-2-1h0l-2-1v-3h-1l1-1c2 1 4 2 5 5l-1 1z" class="W"></path><path d="M764 262h0c-1-2-1-2 0-4 2 3 5 7 7 9h1v1c1 2 2 4 2 6h-1c-1-2-2-4-4-6s-3-5-5-6z" class="T"></path><path d="M772 267l3 3c1 3 3 8 2 10l7 11h0c-3-2-5-5-6-8-1 0-1 3-1 4h-1c0-4-1-8-2-13 0-2-1-4-2-6v-1z" class="J"></path><path d="M759 252c2-2 3-3 5-3h0c-1 1-2 2-4 3l1 1h0c2 0 4 1 5 2h0v1h0v4h0 1l1 1c1 1 2 3 4 4l-1 2c-2-2-5-6-7-9-1 2-1 2 0 4h0c2 5 1 12 0 17l-1 2h0c-1 0-1-1-2-1v-3h-2c2-3 3-8 2-11h-2c-1-2-2-1-4-2h0c1-1 2-1 3-2 0-3 0-4-1-6h-1l3-4z" class="C"></path><defs><linearGradient id="f" x1="759.731" y1="272.237" x2="766.725" y2="266.3" xlink:href="#B"><stop offset="0" stop-color="#6f7176"></stop><stop offset="1" stop-color="#8c8c8e"></stop></linearGradient></defs><path fill="url(#f)" d="M762 275c1-7 1-13 0-20 0 1 1 2 2 3-1 2-1 2 0 4h0c2 5 1 12 0 17l-1 2h0c-1 0-1-1-2-1v-3l1-2z"></path><path d="M761 277l1-2c0 2 0 2 2 4l-1 2h0c-1 0-1-1-2-1v-3z" class="J"></path><path d="M790 149h71l11 1-6 3c-2 1-3 4-5 5-1 0-1 1-1 1l-1 3c0 2-1 2-2 4v1l-5 3v-1h-3l-15-1h-1c-3-1-6-1-10-2l-9-4c-6-1-12-1-18 0v-1h-4-3l-2 1c-4 2-6 4-7 7l1 1v2 2h1l1 1 3 2s1 0 1 1l-4 1h-1l-1-1h-1c-2 1-5 2-7 3l1-3h1-3v-2l3-8c0-2 0-3 2-4 1-2 3-4 4-5-1 0-1 0-2 1-3 1-6 3-10 3h-1l-6 2c-2 0-3 0-5 1-1 0-1 0-1-1l7-5c1-1 3-2 3-4h0c5-3 9-5 15-5-1 0-1-1-1-1h-5-20c-4 0-8 1-12 0l47-1h0z" class="I"></path><path d="M781 151l-1 1h0c-6 1-11 6-17 8 1-1 3-2 3-4h0c5-3 9-5 15-5z" class="O"></path><path d="M768 163c3-2 6-3 9-5 3-1 7-4 11-5 1 0 1 0 2 1h0c-3 1-4 2-6 4v1c-3 2-7 5-9 9 0-2 0-3 2-4 1-2 3-4 4-5-1 0-1 0-2 1-3 1-6 3-10 3h-1z" class="B"></path><path d="M799 156h5l1-1 8-2c3-1 6-1 9-1 2 1 6 1 8 1h-1c-3 1-6 1-9 1l-8 3 2 1h1s1 1 2 1v1c-6-2-12-3-18-4z" class="N"></path><path d="M830 153h3 0l-2 2c0 2 0 2 1 3h0l1 1h-7c-1 0-2 0-3 1v1 1h-2c0-2-1-2-2-3h-2c-1 0-2-1-2-1h-1l-2-1 8-3c3 0 6 0 9-1h1z" class="O"></path><path d="M819 159v-1h2c2-1 5-1 7-1 1 1 3 1 4 1h0l1 1h-7c-1 0-2 0-3 1v1 1h-2c0-2-1-2-2-3z" class="J"></path><path d="M785 158h0c3-2 11-2 14-2 6 1 12 2 18 4v-1h2c1 1 2 1 2 3l-2-1c-1 1-1 1-2 1h-3c-6-1-12-1-18 0v-1h-4-3l-2 1v-2-1l-2-1z" class="H"></path><path d="M792 161c6-1 14-2 20-1l7 1c-1 1-1 1-2 1h-3c-6-1-12-1-18 0v-1h-4z" class="a"></path><path d="M785 158l2 1v1 2c-4 2-6 4-7 7l1 1v2 2h1l1 1 3 2s1 0 1 1l-4 1h-1l-1-1h-1c-2 1-5 2-7 3l1-3h1-3v-2l3-8c2-4 6-7 9-9l1-1z" class="j"></path><path d="M779 171l1-2 1 1v2 2h1l1 1 3 2s1 0 1 1l-4 1h-1l-1-1h-1c-2 1-5 2-7 3l1-3h1 0c0-2 1-2 2-3v-3l2-1z" class="J"></path><path d="M775 178c0-2 1-2 2-3v-3l2-1c0 2 0 3 1 5h-1c-2 1-3 1-4 2z" class="d"></path><defs><linearGradient id="g" x1="855.925" y1="162.666" x2="840.125" y2="149.814" xlink:href="#B"><stop offset="0" stop-color="#2d2d30"></stop><stop offset="1" stop-color="#505153"></stop></linearGradient></defs><path fill="url(#g)" d="M790 149h71l11 1-6 3c-2 1-3 4-5 5-1 0-1 1-1 1l-1 3c0 2-1 2-2 4v1l-5 3v-1h-3l-15-1h-1c-3-1-6-1-10-2l-9-4h3c1 0 1 0 2-1l2 1h2v-1-1c1-1 2-1 3-1h7l-1-1 5-5c1-1 4-1 6-1 4 0 10 0 14-1h1 1c2-1 2-1 4-1h0l-73-1h0z"></path><path d="M840 160h2c1-1 1-3 3-2 1 1 1 2 1 4l1 1c-3-2-5-2-7-3z" class="V"></path><path d="M861 149l11 1-6 3c-2 1-3 4-5 5-1 0-1 1-1 1v-2h0c0-2-1-3-2-4v-1h0c2 1 6-1 7-2l-4-1z" class="H"></path><path d="M846 162c1-1 2-1 3-2 0 2 1 2 2 3h0c1-2 0-1-1-2v-2l2-2v1l1 1 1-2c2 0 2 0 3 1h2v2l1-1-1 3c-2 1-3 3-6 3-2 0-5-1-6-2l-1-1z" class="R"></path><path d="M833 159c1 1 2 1 3 0l1 1c1 0 2-1 2-2 1 1 1 1 1 2 2 1 4 1 7 3 1 1 4 2 6 2 3 0 4-2 6-3 0 2-1 2-2 4v1l-5 3v-1h-3l-15-1h-1c-3-1-6-1-10-2l-9-4h3c1 0 1 0 2-1l2 1h2v-1-1c1-1 2-1 3-1h7z" class="M"></path><path d="M847 167l10-1v1l-5 3v-1h-3v-1l-2-1z" class="O"></path><path d="M826 163h4c3 1 7 1 9 0 1 0 3 0 4 1s1 1 1 2v1c-6-1-12-2-18-4z" class="f"></path><path d="M819 161l2 1h2c1 0 2 1 3 1 6 2 12 3 18 4h3l2 1v1l-15-1h-1c-3-1-6-1-10-2l-9-4h3c1 0 1 0 2-1z" class="C"></path><path d="M428 175c1-1 2-3 2-4s1-2 1-3l1 1v4l-1 2h2 1l-2 1 2 1h1c1 1 1 1 2 1 3 0 7-1 9-3l1 1h3c3 0 7 1 10 3 1 0 1 1 2 2h1l2 1 1 1-11 12c0 1-2 2-2 3l-1 2c-3 1-7 3-11 4l1 1h1l1 1h0l-4 1c-1 1-3 1-4 2h0l-1-1c-1-1-2 0-3 1l1 1c0 2 0 2 1 3 1 2 2 3 3 4-2 1-3 0-4 0h-1l-3-5h0v2h-1l-3 2c-1-1-2-1-3-2-4-3-9-6-11-11-2 0-3 0-4 1l-1 1-1 1v-2 1c-1 0-1 0-2 1v-4l-1-3-1-1-1-1 3-6 3 1 1-1-1-2 1-1c1-1 1-2 2-3 0-3 3-5 5-7 2 0 3 0 5-1h1l1-1c1 0 4-3 6-4v1 1l1 1z" class="W"></path><path d="M429 194v-1c2 0 2 1 3 1l2 1h0 2l1 1h0l4 4h-3c-3-1-6-4-9-6z" class="K"></path><path d="M428 175c1-1 2-3 2-4s1-2 1-3l1 1v4l-1 2h2 1l-2 1 2 1h1c1 1 1 1 2 1l-3 1-10 2c1-1 1 0 2-1h0c0-2 1-2 1-3 1-1 0-1 1-2z" class="V"></path><defs><linearGradient id="h" x1="415.469" y1="187.696" x2="416.145" y2="199.853" xlink:href="#B"><stop offset="0" stop-color="#232327"></stop><stop offset="1" stop-color="#3e4347"></stop></linearGradient></defs><path fill="url(#h)" d="M411 198c1-5 3-8 7-10l4-3c-2 5-6 9-5 14-1 0-2 1-3 2h0-1v-1l-2-2z"></path><path d="M421 176c1 0 4-3 6-4v1 1l1 1c-1 1 0 1-1 2 0 1-1 1-1 3h0c-1 1-1 0-2 1-1 2-5 2-8 3-1 1-2 2-4 3l8-10 1-1z" class="E"></path><path d="M421 176c1 0 4-3 6-4v1 1l-4 4c-3 1-5 4-7 6-1 1-2 2-4 3l8-10 1-1z" class="D"></path><defs><linearGradient id="i" x1="412.699" y1="205.017" x2="426.829" y2="209.196" xlink:href="#B"><stop offset="0" stop-color="#42474b"></stop><stop offset="1" stop-color="#6c6c70"></stop></linearGradient></defs><path fill="url(#i)" d="M411 198l2 2v1h1 0c1-1 2-2 3-2 1 2 2 3 3 5 3 2 7 5 9 8h0v2h-1l-3 2c-1-1-2-1-3-2-4-3-9-6-11-11v-5z"></path><path d="M407 188c1-1 1-2 2-3 0-3 3-5 5-7 2 0 3 0 5-1h1l-8 10-6 15c0 1-1 2-1 2v1c-1 0-1 0-2 1v-4l-1-3-1-1-1-1 3-6 3 1 1-1-1-2 1-1z" class="Y"></path><path d="M407 188c1 2 1 2 2 3v1c-1 1-1 3-2 4 0 2-1 4-2 6h1c0 1-1 2-1 2v1c-1 0-1 0-2 1v-4l2-3c1-3 0-5 1-7l1-1-1-2 1-1z" class="T"></path><path d="M400 197l3-6 3 1c-1 2 0 4-1 7l-2 3-1-3-1-1-1-1z" class="Y"></path><path d="M420 204v-1-1h2l-1-2 1-1h4l3 3c4-1 6 3 11 2h1l1 1h1l1 1h0l-4 1c-1 1-3 1-4 2h0l-1-1c-1-1-2 0-3 1l1 1c0 2 0 2 1 3 1 2 2 3 3 4-2 1-3 0-4 0h-1l-3-5c-2-3-6-6-9-8z" class="B"></path><path d="M437 207c2 0 4-2 7-1l-4 1c-1 1-3 1-4 2h0l-1-1c-1-1-2 0-3 1-1-1-3-2-4-3 2-1 3-1 5 0 1 1 2 2 4 1z" class="b"></path><path d="M429 202c4-1 6 3 11 2h1l1 1h1l1 1h0c-3-1-5 1-7 1-2-2-6-3-8-5z" class="F"></path><path d="M420 204v-1-1h2l-1-2 1-1h4c-2 1-3 1-4 2 1 2 4 4 6 5 1 1 3 2 4 3l1 1c0 2 0 2 1 3 1 2 2 3 3 4-2 1-3 0-4 0h-1l-3-5c-2-3-6-6-9-8z" class="G"></path><path d="M446 175l1 1h3c3 0 7 1 10 3 1 0 1 1 2 2h1l2 1 1 1-11 12c0 1-2 2-2 3l-1 2c-3 1-7 3-11 4h-1l1-1-3-3h3l-4-4h0l-1-1h-2 0l-2-1c-1 0-1-1-3-1v1l-2-1v-8c1-1 2-2 4-3 2 0 3-2 5-2l1-1h-2-1l3-1c3 0 7-1 9-3z" class="E"></path><path d="M454 185h0v1l1 1c0 2-1 4-3 5l-1 1h0c0-2 2-4 2-6l1-2z" class="W"></path><path d="M451 180c2 0 4 1 5 2h0v1 2h-1-1 0c-1-2-2-3-3-5z" class="R"></path><path d="M436 181c2 0 3 0 4-1v1h0c-1 1-1 2-2 2v1c-2 2-4 4-5 6v1h-1l-2-2v-4c2-2 4-3 6-4z" class="G"></path><path d="M433 190l-2-2v-1c1-2 3-2 4-3h3c-2 2-4 4-5 6z" class="H"></path><path d="M432 194c1 0 1-1 2-1 2-1 3-2 4-3 2 1 4 4 7 4h1 1 0l2 1-1 2c-4 0-7 0-11-1l-1-1h-2 0l-2-1z" class="Q"></path><path d="M446 175l1 1h3c3 0 7 1 10 3 1 0 1 1 2 2h-1v1c0 1-1 2-2 2s-2-1-3-2h0c-1-1-3-2-5-2h-5l-8 3c1 0 1-1 2-2h0v-1c-1 1-2 1-4 1v-1l1-1h-2-1l3-1c3 0 7-1 9-3z" class="N"></path><path d="M446 180c2-2 3-3 6-3 3 1 5 2 8 4h1v1c0 1-1 2-2 2s-2-1-3-2h0c-1-1-3-2-5-2h-5z" class="Q"></path><path d="M462 181h1l2 1 1 1-11 12c0 1-2 2-2 3l-1 2c-3 1-7 3-11 4h-1l1-1-3-3h3l-4-4h0c4 1 7 1 11 1l1-2 2-2 1-1c2-1 3-3 3-5l-1-1v-1h1 1v-2-1c1 1 2 2 3 2s2-1 2-2v-1h1z" class="S"></path><path d="M438 200h3 1c1 1 2 1 3 1 0 1 0 1-1 1s-2 1-3 1l-3-3z" class="D"></path><path d="M455 187c1 2 0 3 0 5l-1 1v1c-1 1-2 2-3 2l-1 1h-2l1-2 2-2 1-1c2-1 3-3 3-5z" class="G"></path><path d="M462 181h1l-1 1 1 1c-1 1-1 2-2 2h-1c-2 0-4 1-6 1v-1h1 1v-2-1c1 1 2 2 3 2s2-1 2-2v-1h1z" class="K"></path><path d="M668 534h1l-3 9h1c1-1 2-5 3-5h0l-47 120-5 13-3 6-14 36v-1c0-2 2-6 1-7-2 1-6 12-7 15v-4l4-9-1-3c0-2 0-3-1-5l3-9c-2-2-3-3-3-5h-1v-1-4h-1l4-9 4-10 1-5 2-4 5-14 2-4s1-1 1-2c1 0 2-1 2-2h1 0v2l1 1c3-5 2-8 4-13h0c1-1 1-2 2-3v1 2h1v-1c1 0 2 1 4 1h0v-1h1l1 1v1l-1 3h1 0v2 1l19-49 6-14 9-22 3-8z" class="G"></path><path d="M599 707c2-8 6-15 9-22 1-3 2-6 4-8h0c0 1-1 3-1 5-1 2-2 5-3 7h1v-1c2-4 3-8 5-11h1l-14 36v-1c0-2 2-6 1-7-2 1-6 12-7 15v-4l4-9z" class="Q"></path><path d="M603 661l1 1c-1 1-1 3-1 4l1-1c0 1 1 2 1 2l3 1c2 0 3 0 4 1-2 1-5 0-8-1 0 3-1 6-2 10v3c0 3-1 5-1 7-1 0-1 1-1 2-2-2-3-3-3-5h-1v-1-4h-1l4-9 4-10z" class="a"></path><path d="M596 680c1-2 2-3 3-5v4c-1 2 0 3 0 5 1 1 1 2 1 3l1 1c-1 0-1 1-1 2-2-2-3-3-3-5h-1v-1-4z" class="Q"></path><path d="M603 661l1 1c-1 1-1 3-1 4-1 3-2 6-4 9-1 2-2 3-3 5h-1l4-9 4-10z" class="K"></path><path d="M614 632c1 0 2-1 2-2h1 0v2l1 1c3-5 2-8 4-13h0c1-1 1-2 2-3v1 2h1v-1c1 0 2 1 4 1h0v-1h1l1 1v1l-1 3h1 0v2 1l-9 23c-3 6-5 12-7 18l-3 6v-5h0c-1-1-2-1-4-1l-3-1s-1-1-1-2l-1 1c0-1 0-3 1-4l-1-1 1-5 2-4 5-14 2-4s1-1 1-2z" class="O"></path><path d="M630 624c-3 3-3 8-5 11l1-6c0-1 0-1 1-2-1 0-1 0-2-1l1-1h1c0-1 1-1 2-2 0-1 1-1 2-2l-1 3z" class="S"></path><path d="M617 637h2c1 1 1 2 2 3v3c-1 1-1 1-3 1h-1c-2-1-3-1-3-3-1-1 0-2 1-3l2-1zm-11 15c2 0 2-3 4-4 0 1 0 1 1 2 2 3 2 5 3 8l1 4-1-1c-2-2-5-3-7-3l-1 2-2 1v1l-1-1 1-5 2-4z" class="K"></path><path d="M611 650c2 3 2 5 3 8l-3-1c-1 0-1 0-2-1l-1-1h1v-1l1-2 1-2z" class="G"></path><path d="M610 652l2 2-1 1v2c-1 0-1 0-2-1l-1-1h1v-1l1-2z" class="N"></path><path d="M606 652c2 0 2-3 4-4 0 1 0 1 1 2l-1 2-1 2c-1 1-2 3-3 4s-1 0-2 3v1l-1-1 1-5 2-4z" class="B"></path><path d="M606 660l1-2c2 0 5 1 7 3l1 1c-1 2-2 5-3 7h0c-1-1-2-1-4-1l-3-1s-1-1-1-2l-1 1c0-1 0-3 1-4v-1l2-1z" class="C"></path><path d="M604 661l2-1c0 2-1 4-2 5l-1 1c0-1 0-3 1-4v-1z" class="D"></path><path d="M287 150h74 19c4 0 9-1 13 0l1 1c-1 2-3 3-4 5l-8 7h0c-3 1-6 5-7 8h-1 0c-4 1-7-1-10 0-5 1-7 4-10 7h-2v-1l-2 2c0 1 0 1-1 2h-1l-2 3c-1 0-2 1-4 1h-1-3c0-2 1-2 1-4h-1l-4 5h0-1-1c-1-1-2-2-4-3s-3-2-4-4h0l3-3 1-1c2-2 5-4 7-7 1-1 2-2 2-3-1-1-3-2-5-2h-1c-6-2-13-1-19 0v-1h2c-2-1-7-1-9-1-1 0-1-1-2-2h0c-2 1-3 2-3 3l-1-1-4-3-4-5c0-1-1-1-1-2h0c-1-1-2-1-3-1z" class="m"></path><path d="M326 157h0c-2 0-3-1-4-2 1-1 1-1 2-1 3-1 4-1 7 1-2 2-2 2-5 2z" class="a"></path><path d="M300 153h5c1 1 0 1 1 2l1 2c-1 1-2 2-4 2h0l-1-1-2-1c-1-1-1-2 0-4z" class="G"></path><path d="M306 155l1 2c-1 1-2 2-4 2h0l-1-1c2 0 3-1 4-3z" class="Z"></path><path d="M291 153h3s1 0 2 1h3l1-1c-1 2-1 3 0 4l2 1 1 1c-2 1-3 2-3 3l-1-1-4-3-4-5z" class="a"></path><path d="M295 158l2-1c2 0 2 1 3 3l-1 1-4-3z" class="Q"></path><path d="M337 156c-2-1-3-2-4-4l17 2c-1 1-3 1-4 2l-2 1h1c-1 2-2 2-4 3 0 0-1-1-2-1l-3-2 1-1z" class="c"></path><path d="M337 156c3 1 4 2 8 1-1 2-2 2-4 3 0 0-1-1-2-1l-3-2 1-1z" class="Z"></path><path d="M350 154c1 1 1 1 2 1l1-1h3c4-1 8-1 12 0v1c3 0 12 3 13 5 0 1-1 2-1 3l-3 3c-2 2-2 3-3 5h0 0v-2l-4-1c1-1 3-1 5-1 1-1 2-2 2-3l-1-1 1-2v-1c-3 0-5-1-7-2-2 0-4 0-6-1h-11l-5 1-1-1-1-1c1-1 3-1 4-2z" class="h"></path><path d="M350 154c1 1 1 1 2 1l1-1h3c-3 2-6 2-9 3l-1-1c1-1 3-1 4-2z" class="Z"></path><path d="M307 157c1 1 2 2 3 2v-1c-1-2-2-3-2-5h0c1 0 3-1 4 0 1 0 3 3 5 4h4c2 0 4 1 6 2 0-1 0-1-1-2 3 0 3 0 5-2l2 1 3 1 3 2c1 0 2 1 2 1l1 1c-3 1-5 2-8 1l-1-1c-1 0-3 1-5 1-3 0-6-1-9-1-1 0-4 1-5 1-2-1-7-1-9-1-1 0-1-1-2-2 2 0 3-1 4-2z" class="h"></path><path d="M331 155l2 1c-1 0-1 1-1 2v3c-2-1-3-2-5-2 0-1 0-1-1-2 3 0 3 0 5-2z" class="Z"></path><path d="M345 157h-1l2-1 1 1 1 1 5-1h11c2 1 4 1 6 1 2 1 4 2 7 2v1l-1 2 1 1c0 1-1 2-2 3-2 0-4 0-5 1l4 1v2c-4 1-7-1-10 0-5 1-7 4-10 7h-2v-1l-2 2c0 1 0 1-1 2h-1l-2 3c-1 0-2 1-4 1h-1-3c0-2 1-2 1-4h-1l-4 5h0-1-1c-1-1-2-2-4-3s-3-2-4-4h0l3-3 1-1c2-2 5-4 7-7 1-1 2-2 2-3-1-1-3-2-5-2h-1c-6-2-13-1-19 0v-1h2c1 0 4-1 5-1 3 0 6 1 9 1 2 0 4-1 5-1l1 1c3 1 5 0 8-1l-1-1c2-1 3-1 4-3z" class="K"></path><path d="M345 157h-1l2-1 1 1 1 1 5-1h11c2 1 4 1 6 1 2 1 4 2 7 2v1l-1 2c-4-2-7-3-11-4h-1c-6-1-15-1-21 3l-2 1-2 2v1l-2-1c-1-1-3-2-5-2h-1c-6-2-13-1-19 0v-1h2c1 0 4-1 5-1 3 0 6 1 9 1 2 0 4-1 5-1l1 1c3 1 5 0 8-1l-1-1c2-1 3-1 4-3z" class="Z"></path><path d="M345 157h-1l2-1 1 1 1 1-4 2c-1 1-2 1-2 1l-1-1c2-1 3-1 4-3z" class="a"></path><path d="M339 165h3c3 0 5 1 8 1h1c1 1 5 1 7 1 0 2-2 2-2 4-1 2-3 5-4 6l-2 2c0 1 0 1-1 2h-1l-2 3c-1 0-2 1-4 1h-1-3c0-2 1-2 1-4h-1l-4 5h0-1-1c-1-1-2-2-4-3s-3-2-4-4h0l3-3 1-1c2-2 5-4 7-7 1-1 2-2 2-3l2 1v-1z" class="H"></path><path d="M356 171c-1 2-3 5-4 6l-2 2c0 1 0 1-1 2h-1c0-2 0-2-1-4h-1v-1c4-1 6-4 10-5z" class="Z"></path><path d="M347 177c1 2 1 2 1 4l-2 3c-1 0-2 1-4 1h-1c-1-1-1-2-1-3l2-1h0l-1-2h1c0-1 1-1 2-1s2 0 3-1z" class="O"></path><path d="M328 175c1 1 1 2 2 2s2 1 4 2l1 1c0 1-1 4-1 6h0-1-1c-1-1-2-2-4-3s-3-2-4-4h0l3-3 1-1z" class="V"></path><path d="M328 175c1 1 1 2 2 2s2 1 4 2h-4l-1 1c-2 0-3-1-5-1h0l3-3 1-1z" class="e"></path><path d="M387 285h0c1 0 2 1 2 2 2 2 4 3 7 4 4 0 8-1 12 0 2 1 3 2 5 2 2 1 9 2 11 2h1-1l-1 1h-1v1h0-1c-2-1-6-1-8-1l-2 1h5c1 0 5 1 6 1v1c1 1 3 2 4 3l3 1 1 2-1 1c1 1 2 1 3 2 1 2 3 3 4 5h1c3 5 6 10 9 16 2 3 4 6 5 9 0 3 2 5 1 7-3-6-7-10-12-14h-1c0 2 4 3 4 6l2 2v2c1 1 1 2 1 3 2 3 4 5 5 8v1c-1-2-3-6-5-6l-2-2v1 1c-1-1-2-2-4-3-2-2-5-2-8-3h-1c-2-1-4-1-6 0v1l-1-1-2 1h-1l2-3v-1h-5-6-2c-1 0-1-1-1-1 2-3 2-1 5-2 1 0 2-1 3-1h4-2c-2 0-7 1-9 0h-1-1l-1-2h0l-1 1 1 1v1c1 1 1 2 2 4l2 4 1 2h0v2c1 1 1 2 2 3l1 2v2l1 2c1 1 1 2 2 3 1 2 1 4 2 6l1 1v1l1 2h0c0 1 1 3 2 4 0 1 1 2 1 3v1h1c0 1 1 2 1 3v1l4 8c0 1 0 2 1 2v2h0l-45-108z" class="m"></path><path d="M408 313c-2-1-4-2-4-5l-1-1c-1-1-1-2-1-4h1c0 1 0 1 1 3l1 1 3 6zm10 14h0-4l-1-1h0c-2-1-4-1-5-1-2 0-2-1-3-2 2-1 5-1 7-1h1l-1 1c2 2 5 1 7 3h0c-1 0-1 0-1 1h0z" class="Z"></path><path d="M411 297h5c1 0 5 1 6 1v1c1 1 3 2 4 3h-3c-6-3-14-4-21-3-2 1-4 2-6 2h0c5-3 10-4 15-4z" class="N"></path><path d="M417 320v-1c4 1 7 2 10 4s6 3 9 5l3 3c0 2 4 3 4 6l2 2v2c1 1 1 2 1 3 2 3 4 5 5 8v1c-1-2-3-6-5-6l-2-2c-1-3-2-5-4-7-3-2-6-5-9-6-2-1-3-2-5-2-3-1-5-2-8-3h0c0-1 0-1 1-1h0c-2-2-5-1-7-3l1-1c4 0 7 0 10 1h1c-2-1-5-2-7-3z" class="Q"></path><path d="M417 320v-1c4 1 7 2 10 4s6 3 9 5l3 3c0 2 4 3 4 6-3-1-7-4-9-8-2-2-7-5-10-6-2-1-5-2-7-3z" class="B"></path><path d="M409 334l-1-1c1-2 11-1 14-2-3-1-6-2-9-2h-2s1 0 2-1h-3c2-1 5 0 7 0l4 1h2c1 1 1 1 2 1h1c2 0 3 1 5 2 3 1 6 4 9 6 2 2 3 4 4 7v1 1c-1-1-2-2-4-3-2-2-5-2-8-3h-1c-2-1-4-1-6 0v1l-1-1-2 1h-1l2-3v-1h-5-6-2c-1 0-1-1-1-1 2-3 2-1 5-2 1 0 2-1 3-1h4-2c-2 0-7 1-9 0h-1z" class="E"></path><path d="M412 338v-1c4-2 9-2 14-1-3 0-6 1-8 2h-6z" class="R"></path><path d="M418 338c2-1 5-2 8-2 7 2 12 4 18 10v1c-1-1-2-2-4-3-2-2-5-2-8-3h-1c-2-1-4-1-6 0v1l-1-1-2 1h-1l2-3v-1h-5z" class="b"></path><path d="M425 341c0-1 0-2 1-3l2 1c1 0 3 0 3 2-2-1-4-1-6 0z" class="J"></path><defs><linearGradient id="j" x1="411.856" y1="315.981" x2="438.181" y2="314.017" xlink:href="#B"><stop offset="0" stop-color="#23252c"></stop><stop offset="1" stop-color="#404246"></stop></linearGradient></defs><path fill="url(#j)" d="M423 302h3l3 1 1 2-1 1c1 1 2 1 3 2 1 2 3 3 4 5h1c3 5 6 10 9 16 2 3 4 6 5 9 0 3 2 5 1 7-3-6-7-10-12-14h-1l-3-3c-3-2-6-3-9-5s-6-3-10-4v1c-2 1-9-1-10-2v-1l6 1v-1l-5-4-3-6-1-1c-1-2-1-2-1-3 2-1 6-2 8-1 5 0 9 1 14 3v-1l-2-2z"></path><path d="M430 315c2 2 2 2 3 4v3l-2-2-1-5zm-14-2c2-1 3 0 5 0l2 1c1 0 2 0 3 1l1 1-1 1h-2c-2-2-5-3-8-4z" class="c"></path><path d="M423 302h3l3 1 1 2-1 1c1 1 2 1 3 2l-1 2c-2-2-4-4-6-5v-1l-2-2z" class="a"></path><path d="M435 325c2 1 3 1 5 3h1l-1-4v-1l4 9-3-1h-1 0-1l-3-3c-3-2-6-3-9-5 3 0 5 2 8 2z" class="E"></path><path d="M431 310l1-2c1 2 3 3 4 5h1c3 5 6 10 9 16 2 3 4 6 5 9 0 3 2 5 1 7-3-6-7-10-12-14h0 1l3 1 5 5c-1-3-3-6-5-9-4-6-8-13-13-18z" class="Z"></path><path d="M403 303c2-1 6-2 8-1v1h2v1h-3v1c1 1 3 2 4 3v1h-1-1c1 1 2 3 4 4 3 1 6 2 8 4l1 1c1 1 2 1 3 2h2c0-1 0-2-1-3v-1c-2-3-5-5-7-8h1l1 1c2 1 5 4 6 6l1 5 2 2c0 1 1 2 2 3-3 0-5-2-8-2-3-2-6-3-10-4v1c-2 1-9-1-10-2v-1l6 1v-1l-5-4-3-6-1-1c-1-2-1-2-1-3z" class="G"></path><path d="M425 318l4 3v1c-2-1-4-3-7-4s-7-3-10-6c-2-2-4-5-6-7v-1l3 1c1 2 1 3 3 4 1 1 2 3 4 4 3 1 6 2 8 4l1 1z" class="W"></path><path d="M814 260c1 1 0 1 0 3 0 0 2 2 2 3 2 2 3 4 5 6h0c-1 2-1 3-1 5h-1c1 1 2 1 1 3l3 1 7 2c4 1 7 3 10 5l4-1c1 2 2 2 2 4l-1 1 5 5c2 3 6 8 6 11l2 5v15l-3 1c-1-1-2-1-3-1h0l-2-3c1 0 1 0 2-1l1-2h-1 0-2l-2-2-2-1h0-4c-2 1-4 0-6 0l-4-1h-1-3v1c-4-1-7-2-10-4l-1-1h-1l-1-1c-1-1-2-1-3-1l-1-1-4-1-1-2h1l-1-1c-2 0-3-1-4-2l-1-1v-1l-3-3c-1-1-2-2-4-3l-1-1c-1-2-1-2-3-2-3 0-4-2-6-3l-7-11c1-2-1-7-2-10l-3-3h-1l1-2c3 0 8 1 11 0h8c3-1 5-1 8 0 3 0 6 0 9 1h1 0c3-1 4-4 5-6z" class="AF"></path><path d="M844 287c1 2 2 2 2 4l-1 1-5-4 4-1z" class="E"></path><path d="M856 308l2 5v15l-3 1c-1-1-2-1-3-1h0l-2-3c1 0 1 0 2-1h2c1 0 1 1 3 1v-2c0-2 0-5-1-7 0 0 0-1-1-1 0-3 0-4 1-7z" class="q"></path><path d="M814 282l1-1c3 0 5 0 8 1 8 2 17 9 21 16 5 8 8 15 8 24h0-2l-2-2-2-1h0-4c-3-1-5-2-8-4 2-1 6-1 8-1l2 1c1 0 2 1 4 1h0c-1-4-2-8-5-11-1-1-3-2-5-3 1-1 2-1 3-1l1-1-1-1c-1-2-2-3-4-5-3-3-7-6-11-8v-1c-3-2-7-2-11-2l-1-1z" class="I"></path><path d="M819 277c1 1 2 1 1 3l3 1v1c-3-1-5-1-8-1l-1 1 1 1c4 0 8 0 11 2v1c4 2 8 5 11 8 2 2 3 3 4 5-1 1-4-1-6-1-1 0-3 0-5-1-3-2-6-3-9-4-1 1-1 1-2 1v-1-1c-2-1-4-1-7-1-1 0-3 0-4-1l-3-2v-1h-3v-1l1-2c1-1 1-2 2-3 3-2 3-2 6-2h1l5 1v-1h1l1-2z" class="V"></path><path d="M803 284l2 1h2v-1h1c2 1 4 0 6-2l1 1c-2 1-4 2-5 3 2 2 4 0 6 2h-1 1c1 1 1 1 2 1h-2l-3 1-1 1c-1 0-3 0-4-1l-3-2v-1h-3v-1l1-2z" class="B"></path><path d="M802 287h1c1 0 3-1 5 0h0v2h5v1l-1 1c-1 0-3 0-4-1l-3-2v-1h-3z" class="F"></path><path d="M819 277c1 1 2 1 1 3l3 1v1c-3-1-5-1-8-1l-1 1c-2 2-4 3-6 2h-1v1h-2l-2-1c1-1 1-2 2-3 3-2 3-2 6-2h1l5 1v-1h1l1-2z" class="H"></path><path d="M819 277c1 1 2 1 1 3h-3v-1h1l1-2z" class="Z"></path><path d="M826 286c4 2 8 5 11 8 2 2 3 3 4 5-1 1-4-1-6-1-1 0-3 0-5-1-3-2-6-3-9-4-1 1-1 1-2 1v-1-1c-2-1-4-1-7-1l1-1 3-1h2 5l6 1c1 1 1 1 1 0h0l-3-3c-1 0-1 0-1-1z" class="J"></path><path d="M813 290l3-1c2 2 4 0 7 2-1 1-2 1-2 2-1 1-1 1-2 1v-1-1c-2-1-4-1-7-1l1-1z" class="O"></path><g class="M"><path d="M835 298c-1-1-3-3-5-3-1-1-2-1-3-2v-1c3 1 6 2 10 2h0c2 2 3 3 4 5-1 1-4-1-6-1z"></path><path d="M809 294c3-2 4 0 7 0l3-1v1c1 0 1 0 2-1 3 1 6 2 9 4 2 1 4 1 5 1 2 0 5 2 6 1l1 1-1 1c-1 0-2 0-3 1 2 1 4 2 5 3 3 3 4 7 5 11h0c-2 0-3-1-4-1l-2-1c-2 0-6 0-8 1 3 2 5 3 8 4-2 1-4 0-6 0l-4-1h-1-3v1c-4-1-7-2-10-4l-1-1h-1l-1-1c-1-1-2-1-3-1l-1-1-4-1-1-2h1l-1-1c-2 0-3-1-4-2l-1-1v-1l-3-3c-1-1-2-2-4-3 1-1 1-1 0-2h2l5-1c2-1 5 0 8 0z"></path></g><path d="M816 306c3 1 6 1 9 3h-4v2l-1-1c-1 0-2-2-3-2h-1v-2z" class="G"></path><path d="M832 311l10 3c-2 0-6 0-8 1l-5-2h1l2-2z" class="F"></path><path d="M825 309l7 2-2 2h-1c-2-1-5-2-8-2v-2h4z" class="D"></path><path d="M794 297c1-1 1-1 0-2h2c3 1 5 3 7 5l2 3h-1-2-1l-3-3c-1-1-2-2-4-3z" class="K"></path><path d="M805 298l1-1 1 1s2-1 3 0 2 1 3 2c5 1 10 2 15 5h3l1-1h0c1-1 1-1 2-1v1c2 0 3 0 5 1l1-1c1 1 2 1 3 2v-1c3 3 4 7 5 11h0c-2 0-3-1-4-1v-1c-2-3-6-5-10-6h-1v-1c-3-1-5-1-8-2-2 0-3-1-5-1 0 0-1 1-1 0-2-1-5 0-7-1-1 0-5-4-6-5h-1z" class="S"></path><path d="M832 304h0c1-1 1-1 2-1v1c2 0 3 0 5 1l1-1c1 1 2 1 3 2v-1c3 3 4 7 5 11h0c-2 0-3-1-4-1v-1l2 1h1c-1-2-1-2-2-3s-2-1-3-2h0l-11-5 1-1z" class="V"></path><path d="M832 304l3 1c2 1 6 2 7 4v1l-11-5 1-1z" class="M"></path><path d="M803 300c3 2 6 4 8 6l2 1h0l-1-2c1 0 4 1 4 1v2h1c1 0 2 2 3 2l1 1c3 0 6 1 8 2l5 2c3 2 5 3 8 4-2 1-4 0-6 0l-4-1h-1-3v1c-4-1-7-2-10-4l-1-1h-1l-1-1c-1-1-2-1-3-1l-1-1-4-1-1-2h1l-1-1c-2 0-3-1-4-2l-1-1v-1h1 2 1l-2-3z" class="O"></path><path d="M803 300c3 2 6 4 8 6-1 1 0 1-1 2h0-1v-1c-1 0-2-1-3-2-1 0-2-1-4-2h2 1l-2-3z" class="B"></path><path d="M819 312l13 6h-1-3v1c-4-1-7-2-10-4 2 0 4 1 6 2h0c-2-2-4-3-6-4l1-1z" class="N"></path><path d="M820 310l1 1c3 0 6 1 8 2l5 2c3 2 5 3 8 4-2 1-4 0-6 0l-4-1-13-6-1-1 2-1z" class="M"></path><path d="M809 294c3-2 4 0 7 0l3-1v1c1 0 1 0 2-1 3 1 6 2 9 4 2 1 4 1 5 1 2 0 5 2 6 1l1 1-1 1c-1 0-2 0-3 1 2 1 4 2 5 3v1c-1-1-2-1-3-2l-1 1c-2-1-3-1-5-1v-1c-1 0-1 0-2 1h0l-1 1h-3c-5-3-10-4-15-5-1-1-2-1-3-2s-3 0-3 0l-1-1-1 1-4-4c2-1 5 0 8 0z" class="J"></path><path d="M823 301v-1h0c3 0 7 0 10 1h-1-2c-2 1-5 0-7 0z" class="O"></path><path d="M813 296l4 1 6 3h0v1c-2 0-3-1-4-1h-1l-1-1h-2l-2-1c-1-1-1-1 0-2z" class="b"></path><path d="M813 295c2 1 5 1 8 1 6 1 12 3 17 6 2 1 4 2 5 3v1c-1-1-2-1-3-2-3-1-5-3-7-3-3-1-7-1-10-1l-6-3-4-1v-1z" class="M"></path><path d="M809 294c3-2 4 0 7 0l3-1v1c1 0 1 0 2-1 3 1 6 2 9 4 2 1 4 1 5 1 2 0 5 2 6 1l1 1-1 1c-1 0-2 0-3 1-5-3-11-5-17-6-3 0-6 0-8-1-1 0-3-1-4-1z" class="D"></path><path d="M814 260c1 1 0 1 0 3 0 0 2 2 2 3 2 2 3 4 5 6h0c-1 2-1 3-1 5h-1l-1 2h-1v1l-5-1h-1c-3 0-3 0-6 2-1 1-1 2-2 3l-1 2v1h3v1l3 2c1 1 3 1 4 1 3 0 5 0 7 1v1l-3 1c-3 0-4-2-7 0-3 0-6-1-8 0l-5 1h-2c1 1 1 1 0 2l-1-1c-1-2-1-2-3-2-3 0-4-2-6-3l-7-11c1-2-1-7-2-10l-3-3h-1l1-2c3 0 8 1 11 0h8c3-1 5-1 8 0 3 0 6 0 9 1h1 0c3-1 4-4 5-6z" class="c"></path><path d="M814 260c1 1 0 1 0 3 0 0 2 2 2 3 2 2 3 4 5 6h0c-1 2-1 3-1 5h-1l-1 2h-1v1l-5-1h-1l-2-3c2-1 3-3 5-3h0v-1l-2-2c1 1 2 2 4 2v-1c-2-2-4-3-7-5h0c3-1 4-4 5-6z"></path><path d="M812 279c0-1 1-1 1-2h2s1 1 2 1l1 1h-1v1l-5-1z" class="W"></path><path d="M783 279c1 0 2 0 4-1v-1l1 1c3 0 7-1 10-1 4 0 7 0 11-1l2 3c-3 0-3 0-6 2-1 1-1 2-2 3l-1 2v1h3v1l3 2c1 1 3 1 4 1 3 0 5 0 7 1v1l-3 1c-3 0-4-2-7 0-3 0-6-1-8 0l-5 1h-2c1 1 1 1 0 2l-1-1c-1-2-1-2-3-2-3 0-4-2-6-3l-7-11c1-1 2-1 3-2h1 1l1 1z" class="a"></path><path d="M783 279c1 0 2 0 4-1v-1l1 1c3 0 7-1 10-1 4 0 7 0 11-1l2 3c-3 0-3 0-6 2-1-1-2-2-2-3h0c-2 0-2 1-3 2-1 0-2 1-2 0-1 0-2 0-3-1l-1 2c-1-1-3-1-4 0h-1-1c-2-1-3 0-5 1-1 0-2 0-4-1v-1c1-1 3-1 4-1z" class="W"></path><defs><linearGradient id="k" x1="757.87" y1="311.388" x2="730.134" y2="305.023" xlink:href="#B"><stop offset="0" stop-color="#191a1c"></stop><stop offset="1" stop-color="#3d3f44"></stop></linearGradient></defs><path fill="url(#k)" d="M756 289v1 6c0 3 0 5 1 8 0 2 1 4 3 6 1 0 1 1 2 1l-1 3 2 2c1-1 2-1 4-1 0 1 1 1 1 2 1 1 4 2 5 3l4 2 5 3c-1 3-1 4-3 5v1l-2 1-15-6c-2 0-3 0-5 1-4 3-9 7-12 11v2h-1c-1 1-2 1-3 2l-5 6c-2 0-3 0-4 1-2 1-2 2-3 4 0 1-1 2-2 3h0l-2-2h0-1l-2 2v2h0l-1 1c-2 1-2 2-3 4-1 1-2 0-2 1v1c1 0 0 0 1 1l-1 1h-8l-1 1c-7 1-13 5-18 9 0-3 0-5 1-8h-1c0-7 0-11 4-17 0-1 0-2 1-4l1-2 1-2 3-1 1 1c2-1 3-1 5-2-1 0 0 0-1-1l1-1c1-1 2-1 3-2 2-1 3-1 5-2 2 0 4 1 5 0l-1-3v-1-1l-3-4c-1-1-2-2-3-4 3-2 5-4 7-7 3-4 5-9 9-13l3-1 4-4v-1c1-2 2-2 4-2 0 1 1 1 2 2h3l1 1 4-1c2-1 2-2 2-3l3 3c1-2 1-3 0-4v-1l3-3z"></path><path d="M734 316c1-2 4-2 6-3 3-1 9-2 12 0h-4 0-3c-1 0-1 0-2 1 3-1 7-1 10 0l1 1-1 1h-4l-4-1c-2 0-3 0-5 1h-1l-1-1c-2 1-3 1-4 1z" class="E"></path><path d="M753 314l1 1-1 1h-4l-4-1c2-1 5-1 8-1z" class="C"></path><path d="M756 289v1 6c0 3 0 5 1 8 0 2 1 4 3 6 1 0 1 1 2 1l-1 3c-1-1-1-2-2-3v1c-1 1-1 2-1 2h-1l1-1-1-1c1-1 1-1 1-2-2-2-3-3-3-5 0-1 0-2-1-3h0l-1-1v-1-1h-1v-1l1-1c1-2 1-3 0-4v-1l3-3z" class="I"></path><path d="M745 315l4 1-4 3v1c2 0 3-1 5 0l1 1c-1 1-3 2-4 2-2 1-4 0-6 0h0-5l1-1c1 0 1 0 2-1h1c0-2 1-3 0-5 2-1 3-1 5-1z" class="c"></path><path d="M730 302l4-4v-1c1-2 2-2 4-2 0 1 1 1 2 2h3l1 1c-5 2-10 6-14 11v1c-1 2-2 4-3 7l-1-1v-2l-1-1c-2 2-3 3-4 5v1c-2 2-3 3-4 5s-2 2-3 3c-1-1-2-2-3-4 3-2 5-4 7-7 3-4 5-9 9-13l3-1z" class="Z"></path><defs><linearGradient id="l" x1="713.924" y1="334.471" x2="719.307" y2="344.951" xlink:href="#B"><stop offset="0" stop-color="#505156"></stop><stop offset="1" stop-color="#707276"></stop></linearGradient></defs><path fill="url(#l)" d="M721 318c1-2 2-3 4-5l1 1v2l1 1-1 3 2-2h2c1 0 2-2 3-2h1c1 0 2 0 4-1l1 1h1c1 2 0 3 0 5h-1c-1 1-1 1-2 1l-1 1c-2 0-6 1-7 3 0 1 1 3 1 4l1 1c2 4 3 6 7 8l-3 2-3 2c-2-1-6 0-9 0h-4-10c2 0 4 0 6-1l-1-1c-3 0-6 0-9 1-1 0 0 0-1-1l1-1c1-1 2-1 3-2 2-1 3-1 5-2 2 0 4 1 5 0l-1-3v-1-1l-3-4c1-1 2-1 3-3s2-3 4-5v-1z"></path><defs><linearGradient id="m" x1="719.658" y1="328.833" x2="728.103" y2="336.836" xlink:href="#B"><stop offset="0" stop-color="#474b50"></stop><stop offset="1" stop-color="#64676c"></stop></linearGradient></defs><path fill="url(#m)" d="M719 334l1-4 1-1 1-2c1-1 1-1 3 0l2 5c1 2 1 3 2 5l-1 2h-1l-2-2-1 1h1v1h-1c-1-1-2-2-4-2v-1l-1-1h1l-1-1z"></path><path d="M721 318c2 0 3 0 4 1v5 3c-2-1-2-1-3 0l-1 2-1 1-1 4 1 1h-1l-2-3v-1l-3-4c1-1 2-1 3-3s2-3 4-5v-1z" class="D"></path><path d="M719 334v-7c2-1 3-3 5-4l1 1v3c-2-1-2-1-3 0l-1 2-1 1-1 4z" class="B"></path><path d="M714 327c1-1 2-1 3-3s2-3 4-5c-2 4-4 8-4 12l-3-4z" class="a"></path><path d="M721 318c1-2 2-3 4-5l1 1v2l1 1-1 3 2-2h2c1 0 2-2 3-2h1c1 0 2 0 4-1l1 1h1c1 2 0 3 0 5h-1c-1 1-1 1-2 1l-1 1c-2 0-6 1-7 3 0 1 1 3 1 4l1 1c2 4 3 6 7 8l-3 2-3 2c-2-1-6 0-9 0 3 0 7 0 9-1 0-2-2-4-3-5-1-2-1-3-2-5l-2-5v-3-5c-1-1-2-1-4-1z" class="B"></path><path d="M731 331c2 4 3 6 7 8l-3 2c-2-1-4-3-5-6-1-1 0-2 1-4z" class="b"></path><path d="M721 318c1-2 2-3 4-5l1 1v2l1 1-1 3c0 4 0 7 1 11v1l-2-5v-3-5c-1-1-2-1-4-1z" class="G"></path><path d="M734 316c1 0 2 0 4-1l1 1h1c1 2 0 3 0 5h-1c-1 1-1 1-2 1-3 0-4-1-6 1l-1-1 1-2c0-1 1-2 2-3v-1h1z" class="I"></path><path d="M733 317l1 3v1h-2l-1-1c0-1 1-2 2-3z" class="Z"></path><path d="M739 316h1c1 2 0 3 0 5h-1c-1-1-1-2-2-2h-1v-1c1-1 2-1 3-2h0z" class="B"></path><path d="M757 314h1s0-1 1-2v-1c1 1 1 2 2 3l2 2c1-1 2-1 4-1 0 1 1 1 1 2 1 1 4 2 5 3l4 2 5 3c-1 3-1 4-3 5v1l-2 1-15-6c-2 0-3 0-5 1-4 3-9 7-12 11v2h-1c0-1 0-2-1-3h-1c-2 0-3 1-4 2-4-2-5-4-7-8l-1-1c0-1-1-3-1-4 1-2 5-3 7-3h5 0c2 0 4 1 6 0 1 0 3-1 4-2l2-1h1l3-6z" class="F"></path><path d="M743 330c2-1 4-1 6-3 0 2-1 3 0 4-1 1-1 0-1 1-2-1-3-1-5-2z" class="C"></path><path d="M748 332h0c-1 2-2 2-3 2-2-1-4 0-5 0l-4-4h7c2 1 3 1 5 2z" class="a"></path><path d="M730 330c2-2 3-3 6-4h1c2 0 3-1 5-1l-3 2c-2 1-5 0-6 3l1 1c1 1 1 2 1 3 1 0 1 1 2 1 1 1 2 1 3 2h2c-2 0-3 1-4 2-4-2-5-4-7-8l-1-1z" class="E"></path><path d="M736 323h5 0c2 0 4 1 6 0l1 2h-6c-2 0-3 1-5 1h-1c-3 1-4 2-6 4 0-1-1-3-1-4 1-2 5-3 7-3z" class="W"></path><path d="M757 314h1s0-1 1-2v-1c1 1 1 2 2 3l2 2c1-1 2-1 4-1 0 1 1 1 1 2h-1c-3 0-4 3-6 4l-2 2c-1 0-2 1-3 1-1 1-2 2-2 3-1 0-2-1-2 0-1 1-1 0-1 1-1 2-1 2-2 3-1-1 0-2 0-4 1 0 2-1 3-2h-3-1l-1-2c1 0 3-1 4-2l2-1h1l3-6z" class="I"></path><path d="M757 314h1s0-1 1-2v-1c1 1 1 2 2 3l2 2c1-1 2-1 4-1 0 1 1 1 1 2h-1c-3 0-4 3-6 4-1-2 1-2-1-4h-1v1c-1 2-3 2-5 2l3-6z" class="C"></path><path d="M761 321c2-1 3-4 6-4h1c1 1 4 2 5 3l4 2 5 3c-1 3-1 4-3 5-3-2-8-4-11-5l-4-1s-4-1-5-1l2-2z" class="T"></path><path d="M761 321c2-1 3-4 6-4l-1 1-1 2h1l1 1h-2l2 2h-1c-1 0-1 0-2 1l-5-1 2-2z" class="U"></path><path d="M773 320l4 2 5 3c-1 3-1 4-3 5-3-2-8-4-11-5l1-1c2 1 4 1 6 3h1c0-1-1-1 0-2v-1l-1-1c-2 0-3 0-4-2h0s1 0 2-1h0z" class="X"></path><path d="M742 337h1c1 1 1 2 1 3-1 1-2 1-3 2l-5 6c-2 0-3 0-4 1-2 1-2 2-3 4 0 1-1 2-2 3h0l-2-2h0-1l-2 2v2h0l-1 1c-2 1-2 2-3 4-1 1-2 0-2 1v1c1 0 0 0 1 1l-1 1h-8l-1 1c-7 1-13 5-18 9 0-3 0-5 1-8h-1c0-7 0-11 4-17 0-1 0-2 1-4l1-2 1-2 3-1 1 1c2-1 3-1 5-2 3-1 6-1 9-1l1 1c-2 1-4 1-6 1h10 4c3 0 7-1 9 0l3-2 3-2c1-1 2-2 4-2z" class="m"></path><path d="M722 353c2-1 4-1 7 0 0 1-1 2-2 3h0l-2-2h0-1c-1 0-2-1-2-1z" class="F"></path><path d="M732 349l5-5h1c1-1 2-1 3-2l-5 6c-2 0-3 0-4 1z" class="E"></path><path d="M715 354h3 0c1-1 2-1 4-1 0 0 1 1 2 1l-2 2v2h0-2l-5-4z" class="I"></path><path d="M723 343c3 0 7-1 9 0-2 1-4 1-6 1l-10 1h-3c-1-1-1-1-2-1s-2 0-3-1h1 10 4z" class="U"></path><path d="M695 350v-1c5-3 8-5 13-6 1 1 2 1 3 1s1 0 2 1h3c-7 1-14 2-21 5z" class="J"></path><path d="M705 342c3-1 6-1 9-1l1 1c-2 1-4 1-6 1h-1c-5 1-8 3-13 6v1c0 1-1 2-2 2 0-1 0-2 1-4l1-2 1-2 3-1 1 1c2-1 3-1 5-2z" class="N"></path><path d="M696 344l3-1 1 1c-2 1-4 4-6 4l1-2 1-2z" class="G"></path><path d="M690 369l3-6c1-2 3-6 6-7 1-1 3-2 5-3l-1 1 1 1c1-1 3-2 5-2h1c2 0 3 0 5 1l5 4h2l-1 1c-2 1-2 2-3 4-1 1-2 0-2 1v1c1 0 0 0 1 1l-1 1h-8l-1 1c-7 1-13 5-18 9 0-3 0-5 1-8z" class="D"></path><path d="M704 355c1-1 3-2 5-2h1c2 0 3 0 5 1l5 4h-1c-2-1-4-1-6-1h-4-1c-4 1-7 3-10 6l-1-1c1-1 1-1 1-2 1-1 4-4 6-5z" class="H"></path><path d="M572 767h1c0 3 0 6-1 8v1c0 1 0 2-1 4v2c1-2 1-3 3-3l-26 63-21 51h-1l-2 2h0v-2c1-1 0-2 0-3-1 4-3 8-5 12-1 0-1 0-1-1h-1-1c-1-1-2-1-2-2h3l2-3h0-2 0c0-1-1-3-2-3-2-2-3-3-4-6 1 1 1 1 2 3 1 1 2 1 3 1 0 0 1-1 1-2l2-8-1 1v-4-1c0-2 0-3-1-5l1-8v-8l1-18 1-5 1-3c-1-2 0-6-1-9v-3c-1-4-1-7 0-10s1-5 3-7h0l3-6c1 1 1 2 2 4v-1c1 0 2-1 2-2l1 3 2 3v-1c1 2 2 3 3 4 0 2 0 3 1 5l2 1c0 1 0 0 1 1l5-10c1-2 3-4 3-7l3-9c2 1 3 1 4 3l1 2c1-4 2-7 4-11 0-1 0-1 1-1v2h2c1-4 2-9 2-14l4 2c1 0 2-1 3-2z" class="N"></path><path d="M543 833h2 3 1c0 2-2 3-2 4h-2c-1-1-1-2-2-3h0v-1z" class="U"></path><path d="M553 820c0 2-2 5-3 7h-1-1v-4c2-1 4-2 5-3z" class="O"></path><path d="M546 822c1-1 3-2 4-3 2-1 3-1 4-1l-1 2c-1 1-3 2-5 3s-4 2-6 2v-1-1c1-1 2-1 4-1z" class="n"></path><path d="M540 837c2 1 2 5 3 7 1 5-16 38-19 46-1 4-3 8-5 12-1 0-1 0-1-1h-1-1c-1-1-2-1-2-2h3l2-3c1 0 1 0 1-1l17-37 3-9c2-4 1-8 0-12z" class="X"></path><path d="M542 828l2-1c-1 1-1 1-1 2s-1 3 0 4v1l-1 1v-1c-1 1-1 1-2 1v-1l-1 1v2h1c1 4 2 8 0 12l-3 9c-1-2-1-3 0-4v-4c0-1 0-2-1-3-1 1-1 1-2 3l-1-2c0-1 0-2-1-4l-1 4-2 1 2-5c1-1 1-2 2-3s2-2 2-3l3-3v-2l1-1 1 1 2-1v-4z" class="C"></path><path d="M533 848c0-1 0-3 1-4 0 0 1-2 2-2l1-1c1 2 2 5 2 8h1l-3 9c-1-2-1-3 0-4v-4c0-1 0-2-1-3-1 1-1 1-2 3l-1-2z" class="G"></path><path d="M572 767h1c0 3 0 6-1 8v1c0 1 0 2-1 4v2c0 1 0 1-1 2-2 4-3 8-6 11v-1-4c0-3 1-5 1-8l-2-1c1-4 2-9 2-14l4 2c1 0 2-1 3-2z" class="J"></path><path d="M572 776c0 1 0 2-1 4v2c0 1 0 1-1 2-2 4-3 8-6 11v-1c1-3 2-6 3-10 2-3 3-6 5-8z" class="G"></path><defs><linearGradient id="n" x1="519.04" y1="864.281" x2="531.49" y2="872.237" xlink:href="#B"><stop offset="0" stop-color="#34393f"></stop><stop offset="1" stop-color="#4f5154"></stop></linearGradient></defs><path fill="url(#n)" d="M529 849l2-1 1-4c1 2 1 3 1 4l1 2c1-2 1-2 2-3 1 1 1 2 1 3v4c-1 1-1 2 0 4l-17 37c0 1 0 1-1 1h0-2 0c0-1-1-3-2-3-2-2-3-3-4-6 1 1 1 1 2 3 1 1 2 1 3 1 0 0 1-1 1-2l2-8v-1l4-13c0-1 1-3 1-4l5-14z"></path><path d="M523 867c1 1 1 2 1 4 0 1-1 2-1 4-1 1-2 4-4 5l4-13z" class="B"></path><path d="M534 850c1-2 1-2 2-3 1 1 1 2 1 3l-3 8c-1-3-1-6 0-8z" class="V"></path><defs><linearGradient id="o" x1="567.567" y1="797.469" x2="539.938" y2="804.053" xlink:href="#B"><stop offset="0" stop-color="#717479"></stop><stop offset="1" stop-color="#89898a"></stop></linearGradient></defs><path fill="url(#o)" d="M556 791c1-4 2-7 4-11 0-1 0-1 1-1v2h2l2 1c0 3-1 5-1 8v4 1l-6 15c-1 2-2 6-4 8-1 0-2 0-4 1-1 1-3 2-4 3-2 0-3 0-4 1v1h-1v-7h-1-1c0-1 0-3 1-5h0l5-10c1-2 3-4 3-7l3-9c2 1 3 1 4 3l1 2z"></path><path d="M545 802c1 0 2-1 3 0 0 1 1 2 2 4-1 0-2-1-3-1v-1c-1 1-1 2-2 4 0 2-1 3-2 5-1 1 0 6 0 7l3 2c-2 0-3 0-4 1v1h-1v-7h-1-1c0-1 0-3 1-5h0l5-10z" class="M"></path><path d="M540 812v2l2-1v1c0 2 0 5 1 7 0 1-1 1-1 2v1h-1v-7h-1-1c0-1 0-3 1-5z" class="g"></path><path d="M551 786c2 1 3 1 4 3l1 2c0 1 1 2 0 3v2h1c1 2 1 3 0 5s-2 4-5 5h-2c-1-2-2-3-2-4-1-1-2 0-3 0 1-2 3-4 3-7l3-9z" class="K"></path><path d="M550 799c0-1 2-2 2-3 2 0 2 0 4 1l1-1c1 2 1 3 0 5s-2 4-5 5h-2c-1-2-2-3-2-4v-1c1-1 1-2 2-3v1z" class="I"></path><path d="M550 799c0-1 2-2 2-3 2 0 2 0 4 1-1 2-2 4-4 5-2-1-2-2-2-3z" class="L"></path><path d="M526 795c1 1 1 2 2 4v-1c1 0 2-1 2-2l1 3 2 3v-1c1 2 2 3 3 4 0 2 0 3 1 5l2 1c0 1 0 0 1 1h0c-1 2-1 4-1 5h1 1v7h1v1 3 4l-2 1-1-1-1 1v2l-3 3c0 1-1 2-2 3s-1 2-2 3l-2 5-5 14c0 1-1 3-1 4l-4 13v1l-1 1v-4-1c0-2 0-3-1-5l1-8v-8l1-18 1-5 1-3c-1-2 0-6-1-9v-3c-1-4-1-7 0-10s1-5 3-7h0l3-6z" class="W"></path><path d="M517 872l1-8c0 1 0 3 1 4 1 2-1 7 0 10h-1v-1c0-2 0-3-1-5z" class="E"></path><defs><linearGradient id="p" x1="534.998" y1="820.848" x2="534.533" y2="836.153" xlink:href="#B"><stop offset="0" stop-color="#3b3a3e"></stop><stop offset="1" stop-color="#4c5154"></stop></linearGradient></defs><path fill="url(#p)" d="M535 810h2l2 1c0 1 0 0 1 1h0c-1 2-1 4-1 5h1 1v7h-1c-1 2-2 5-4 7-4 7-10 12-14 17 0-2 1-3 2-5 2-5 6-9 9-13 2-2 3-4 3-7h0l3-3c-2-3-1-4-1-6-1-1-1-2-2-2l-1-2z"></path><path d="M524 826v1 2c-1 2-1 3-1 5h2 1 2v-1c2-1 3-2 5-3-3 4-7 8-9 13-1 2-2 3-2 5l-1 2h0l-3 6 1-18 1-5 1-3c1-1 2-3 3-4z" class="G"></path><path d="M528 834c0 1-2 2-2 3-2 2-3 4-4 7-1 0-1 1-2 2 1-5 2-8 5-12h1 2z" class="H"></path><defs><linearGradient id="q" x1="517.581" y1="827.852" x2="536.851" y2="805.699" xlink:href="#B"><stop offset="0" stop-color="#080807"></stop><stop offset="1" stop-color="#2a2c2f"></stop></linearGradient></defs><path fill="url(#q)" d="M528 799v-1c1 0 2-1 2-2l1 3 2 3v-1c1 2 2 3 3 4 0 2 0 3 1 5h-2l1 2c1 0 1 1 2 2 0 2-1 3 1 6l-3 3h0c0 3-1 5-3 7-2 1-3 2-5 3v1h-2-1-2c0-2 0-3 1-5v-2-1c2-1 3-2 4-2h2c-1-7 2-12-1-18v-1l-2-2c0-1 0-3 1-4z"></path><path d="M535 810c-1-2-2-4-3-5-1-3-1-3-1-6l2 3v-1c1 2 2 3 3 4 0 2 0 3 1 5h-2z" class="B"></path><path d="M526 795c1 1 1 2 2 4-1 1-1 3-1 4l2 2v1c3 6 0 11 1 18h-2c-1 0-2 1-4 2-1 1-2 3-3 4-1-2 0-6-1-9v-3c-1-4-1-7 0-10s1-5 3-7h0l3-6z" class="C"></path><path d="M524 813h0v3l-1 1-1-1c0-2 1-2 2-3zm2-18c1 1 1 2 2 4-1 1-1 3-1 4l-1 4-2 3v1c-1-1-1-1-1-2 0-2-1-3-1-4l1-4 3-6z" class="Z"></path><path d="M520 808c1-3 1-5 3-7h0l-1 4c-1 4-3 12 0 16v1h1v-1-1h2c2-4 2-10 4-14 3 6 0 11 1 18h-2c-1 0-2 1-4 2-1 1-2 3-3 4-1-2 0-6-1-9v-3c-1-4-1-7 0-10z" class="d"></path><defs><linearGradient id="r" x1="381.833" y1="257.486" x2="432.151" y2="266.012" xlink:href="#B"><stop offset="0" stop-color="#010201"></stop><stop offset="1" stop-color="#202226"></stop></linearGradient></defs><path fill="url(#r)" d="M427 218l1 1c1 0 2 0 3 1s2 1 3 1l1 1h0c1 2 0 1 1 2l2 2c3 6 3 10 4 16l-1 3 1 1-1 10v-1-4c-1 1-1 3-1 5-1 3-2 5-3 8l-1 1-2 4c0 1 1 1 1 2h-1v1l1 1c1 1 0 1 0 2l4 2c0 1 0 2 1 2 0 1 0 0 1 0 1 1 1 1 2 1v2l-5 2-2 2h-1c0 1-1 1-1 2-3 4-6 7-9 10l-1-1h0l2-2-1-1-1 1c-2 0-9-1-11-2-2 0-3-1-5-2-4-1-8 0-12 0-3-1-5-2-7-4 0-1-1-2-2-2h0c-1 0-2-3-2-4s0-2-1-3c-2-6-2-12-3-18 0-2 0-5 1-7 3-1 6-1 8-1 7 0 14 0 20 1-1-1-3-2-3-4l-1-1-1-1c-3-2-4-6-6-9l1-1c6 0 13-1 19 1 2 0 4 2 6 3l-1-1c-2-5 1-16 3-22z"></path><path d="M405 247c1 0 1-1 1-2v-3c3 0 5-1 8-1-2 2-5 4-7 6l-1 1-1-1z" class="a"></path><path d="M426 252c-2-1-3-2-4-2-1-2-1-2-1-4 0 1 0 1 1 2h2v-1h0-1l1-1c2 1 3 2 5 2h0l3-1c-2 3-3 5-6 6v-1h0z" class="K"></path><path d="M415 266h1l1 1c2-1 1-2 4-1-1 1-2 1-2 2 0 2-1 3-1 4l-3 2-2-2c-1 0-1 1-2 1h-1c1-3 1-5 2-8l1 2 2-1z" class="W"></path><path d="M415 266h1l1 1c2-1 1-2 4-1-1 1-2 1-2 2 0 2-1 3-1 4l-3 2-2-2c0-1 1-1 1-1 1-1 1 0 2-1v-2l-1-2z" class="E"></path><path d="M414 241c1 0 2 0 4 1 0 1 0 2-1 3-1 2-2 4-4 4-2 1-4 0-5 0l-1-2c2-2 5-4 7-6z" class="m"></path><path d="M426 252h0v1c2 2 2 2 4 2 0 1 0 2-1 3 0 2-5 6-7 8h-1c-3-1-2 0-4 1l-1-1h-1l-2 1-1-2c0-1-1-2-2-3v-2l11-6h2c1 0 2-1 3-2z" class="G"></path><defs><linearGradient id="s" x1="408.869" y1="277.695" x2="433.224" y2="273.327" xlink:href="#B"><stop offset="0" stop-color="#313438"></stop><stop offset="1" stop-color="#494d55"></stop></linearGradient></defs><path fill="url(#s)" d="M435 260l2 4h0l-1 1-2 4c0 1 1 1 1 2h-1v1l1 1c1 1 0 1 0 2l-1 2h-1-2l-1 1c-1 0-2 0-2 1-1 1-3 4-3 4-1 0-2-1-2-1-2 0-7 4-9 5-1 1-5 2-7 1h-2c0-1-1 0-1-1h-1c0-1 1-2 0-4 0-2 0-2 1-3 2 0 2 0 3 1l-2 3c1 1 0 1 1 1 11-4 18-11 26-19l3-6z"></path><path d="M435 260l2 4h0l-1 1h-1v1c-1 1-2 2-2 3-1-2 1-2-1-3l3-6z" class="B"></path><path d="M434 269c0 1 1 1 1 2h-1v1l1 1c1 1 0 1 0 2l-1 2h-1-2l-1 1c-1 0-2 0-2 1-1 1-3 4-3 4-1 0-2-1-2-1-2 0-7 4-9 5-1 1-5 2-7 1h-2c0-1-1 0-1-1h-1c0-1 1-2 0-4 0-2 0-2 1-3 2 0 2 0 3 1l-2 3c1 1 0 1 1 1l-2 1v1h1c2 0 4-1 5-1 9-3 15-7 21-14l3-3z" class="E"></path><path d="M434 269c0 1 1 1 1 2h-1v1l-1 2h-1l-1-2 3-3z" class="G"></path><path d="M435 275l4 2c0 1 0 2 1 2 0 1 0 0 1 0 1 1 1 1 2 1v2l-5 2-2 2h-1c0 1-1 1-1 2-3 4-6 7-9 10l-1-1h0l2-2-1-1-1 1c-2 0-9-1-11-2-2 0-3-1-5-2-4-1-8 0-12 0-3-1-5-2-7-4 0-1-1-2-2-2h0c-1 0-2-3-2-4 6 4 11 6 18 6h1c0 1 1 0 1 1h2c2 1 6 0 7-1 2-1 7-5 9-5 0 0 1 1 2 1 0 0 2-3 3-4 0-1 1-1 2-1l1-1h2 1l1-2z" class="C"></path><path d="M421 286h1 0c-1 2 0 2-1 3 0 1-2 3-2 4-2 0-5 0-6-1s-2-1-3-2c3 0 8-3 11-4z" class="m"></path><path d="M427 218l1 1c1 0 2 0 3 1s2 1 3 1l1 1h0c1 2 0 1 1 2l2 2c3 6 3 10 4 16l-1 3 1 1-1 10v-1-4c-1 1-1 3-1 5-1 3-2 5-3 8h0l-2-4v-1c-2 1-4 3-5 5s-3 2-4 3c-2 2-4 5-7 6h-1v-1c0-1 1-2 1-4 0-1 1-1 2-2h1c2-2 7-6 7-8 1-1 1-2 1-3-2 0-2 0-4-2 3-1 4-3 6-6l-3 1v-1c0-2-2-4-3-5l-1-1-1-1c-2-5 1-16 3-22z" class="W"></path><path d="M431 230c1 0 2-1 3-1 0-1-1-1 0-1s1 0 1 1c1 2 2 3 3 4v5c-1-2-1-3-2-4-1-3-2-3-5-4z" class="I"></path><path d="M427 230h4c3 1 4 1 5 4 1 1 1 2 2 4 0 3 0 5-1 8h0v-2h-1v-5c0-1-1-4-2-4v1c-1 0-1 0-1 1l-2-1c-1-2-3-3-4-4v-2z" class="b"></path><path d="M435 259c1-3 3-6 4-8 0-3-1-7 1-9 0 1 0 2 1 3l1 1-1 10v-1-4c-1 1-1 3-1 5l-3 8h0l-2-4v-1z" class="K"></path><path d="M433 237c0-1 0-1 1-1v-1c1 0 2 3 2 4v5c-1 3-2 9-5 11h-1c-2 0-2 0-4-2 3-1 4-3 6-6 1-3 2-7 1-10z" class="T"></path><path d="M427 218l1 1v2h1c1 1 1 1 1 2 1 1 3 2 3 4h-1c-1 0-3 0-4-1l-2 2v1c1 1 0 1 1 1v2c1 1 3 2 4 4l2 1c1 3 0 7-1 10l-3 1v-1c0-2-2-4-3-5l-1-1-1-1c-2-5 1-16 3-22z" class="C"></path><path d="M431 236l2 1c1 3 0 7-1 10l-3 1v-1c0-2-2-4-3-5 1 0 2 0 3 1l1 1h1v-6l-1-1 1-1z" class="F"></path><path d="M583 615h3c1 1 1 2 3 3h1l3 1c-3 2-5-1-9 1 1 1 2 1 3 1 2 1 6 4 6 5 1 1 2 1 3 1s2-1 3-1c2 0 2 0 4-1h1v-1 1l10-2h1v2l2 1-3 6c0 1-1 2-1 2l-2 4-5 14-2 4-1 5-4 10-4 9h1v4 1h1c0 2 1 3 3 5l-3 9c1 2 1 3 1 5l1 3-4 9h-1c0 1-1 2-1 3l-1 3-4 7v-3h0c-2 2-2 5-4 7-2 0-3-1-4-2l1-2c0-1 0-2-1-3h-4l-1 1-3 3c-4 1-8 2-12 2v-1-3c1-1 1-2 2-3v-1c2 1 3 2 5 3 1-1 2-2 4-3l-3-2c4-7 6-13 9-20h0v-2c0-1-1-2 0-3v-2l-1 1v-1c1 0 1 0 2-1 0-1 1-3 1-4v-3c-1-2-2-3-2-4v-8-1c-2-5-5-8-9-10-2-1-4-1-6-1l1-1h2v-1h2-1c-2 0-4-1-5-1-1-1-2-1-2-2l-1 1h0c-1 2-2 2-3 4h-2 0c-1 1-2 2-2 3v1l-4 1v-1c4-2 7-7 10-10v-4c-2 1-3 2-4 3 0-3 0-5-1-8-1-2-4-4-5-7l4 1 3 3c1-1 2-1 3-1 1 1 2 2 3 2v-2c1 0 1 0 1 1 1 1 2 2 3 2 2 0 4-1 6-2h1c2-1 3-3 5-5l1-3-1-3c-1 0-1-1-2-1h0v-1c0-2 0-2 2-3h1c1 1 0 1 2 1v-2c0-4 1-7 2-11l2-2z" class="m"></path><path d="M589 657c1 5 1 13 0 18 0 1-1 3-1 5s0 4-1 6l-1 1v-2-2-3l2-11h0l1-12z" class="Z"></path><path d="M577 723c-1 1-2 3-2 4l-3 3c-4 1-8 2-12 2v-1-3c1-1 1-2 2-3v-1c2 1 3 2 5 3l-2 1h1c4 1 8-3 11-5z" class="O"></path><path d="M562 725c1 2 1 3 0 4l-2 2v-3c1-1 1-2 2-3z" class="I"></path><path d="M595 680h1v4c-3 10-7 20-12 29-3 5-5 9-8 13l-1 1c0-1 1-3 2-4 9-12 12-29 18-43z" class="B"></path><path d="M580 679v1h0c1 1 1 4 1 5h3l2-2v2 2c-1 6-2 11-4 16-3 7-6 15-11 21l-3-2c4-7 6-13 9-20h0v-2c0-1-1-2 0-3v-2l-1 1v-1c1 0 1 0 2-1 0-1 1-3 1-4v-3c0-3-1-7 1-8z" class="k"></path><path d="M579 697h1l3-8c0 5-1 10-4 15 0-3 1-4 1-6-1 1-2 3-2 4h-1 0l2-5z" class="U"></path><path d="M580 679v1h0c1 1 1 4 1 5h3l2-2v2l-3 4-3 8h-1l-2 5v-2c0-1-1-2 0-3v-2l-1 1v-1c1 0 1 0 2-1 0-1 1-3 1-4v-3c0-3-1-7 1-8z" class="f"></path><path d="M580 679v1c0 6 0 11-1 17l-2 5v-2c0-1-1-2 0-3v-2l-1 1v-1c1 0 1 0 2-1 0-1 1-3 1-4v-3c0-3-1-7 1-8z" class="F"></path><path d="M577 702h1c0-1 1-3 2-4 0 2-1 3-1 6 0 1-1 3-1 4v1c1-1 1-1 1-3 1-1 1-2 3-3h0c-3 7-6 15-11 21l-3-2c4-7 6-13 9-20z" class="g"></path><path d="M596 684v1h1c0 2 1 3 3 5l-3 9c1 2 1 3 1 5l1 3-4 9h-1c0 1-1 2-1 3l-1 3-4 7v-3h0c-2 2-2 5-4 7-2 0-3-1-4-2l1-2c0-1 0-2-1-3h-4c3-4 5-8 8-13 5-9 9-19 12-29z" class="E"></path><path d="M584 724c-2 1-2 1-3 1l-1-1c1-1 1-1 2-1l2 1z" class="I"></path><path d="M582 723l1-2c2 0 3 0 5 1h1l1 1c-2 1-5 0-6 1l-2-1zm4-7c4 0 4 1 7 3l-1 3c-2-1-6-2-7-3l1-3h0z" class="m"></path><path d="M586 716c0-2 9-15 11-17 1 2 1 3 1 5l1 3-4 9h-1c0 1-1 2-1 3-3-2-3-3-7-3z" class="c"></path><path d="M598 704l1 3-4 9h-1l4-12z" class="S"></path><defs><linearGradient id="t" x1="571.77" y1="655.375" x2="588.377" y2="659.536" xlink:href="#B"><stop offset="0" stop-color="#969695"></stop><stop offset="1" stop-color="#cecccc"></stop></linearGradient></defs><path fill="url(#t)" d="M579 633h1v1c1 1 2 1 3 2h1c0 2 2 10 3 10 0 2 0 4 1 6l1 5-1 12h0l-2 11v3l-2 2h-3c0-1 0-4-1-5h0v-1-1l1-1c-1-1-1-1-1-2 2-3 1-7 0-10 0 0-4-8-4-9l-1-4v-1-3h-2l-2-3c2-1 3-3 5-5l1-3-1-3h1c1 0 2-1 2-1z"></path><path d="M584 680h2v3l-2 2v-5z" class="d"></path><path d="M579 633h1v1l3 6-6-6c1 0 2-1 2-1z" class="X"></path><path d="M584 680c2-5 3-12 3-18l1 7-2 11h-2z" class="Y"></path><path d="M580 665c1 0 1 0 2 1 1 2 0 5 1 7 0 2 0 5-1 7-1-1-1-2-1-3-1-1-1-1-1-2 2-3 1-7 0-10z" class="T"></path><path d="M580 634c1 1 2 1 3 2h1c0 2 2 10 3 10l-2 3-2-9-3-6z" class="G"></path><path d="M587 646c0 2 0 4 1 6l1 5-1 12h0l-1-7c1-4-1-9-2-13l2-3z" class="F"></path><path d="M575 651c1 1 2 3 2 4l3 2 1-1c1 2 0 4 2 6v-1c1 4 1 8 0 12-1-2 0-5-1-7-1-1-1-1-2-1 0 0-4-8-4-9l-1-4v-1z" class="M"></path><path d="M577 637c1 1 2 5 2 7l4 17v1c-2-2-1-4-2-6l-1 1-3-2c0-1-1-3-2-4v-3h-2l-2-3c2-1 3-3 5-5l1-3z" class="O"></path><path d="M576 640v1l-1 2v1c1 2 0 2 0 4h-2l-2-3c2-1 3-3 5-5z" class="F"></path><path d="M577 655l1-2 1-1c0 2 1 2 2 3 1-3-3-7-2-11l4 17v1c-2-2-1-4-2-6l-1 1-3-2z" class="G"></path><path d="M547 641l4 1 3 3c1-1 2-1 3-1 1 1 2 2 3 2v-2c1 0 1 0 1 1 1 1 2 2 3 2 2 0 4-1 6-2h1l2 3h2v3 1l1 4c0 1 4 9 4 9 1 3 2 7 0 10 0 1 0 1 1 2l-1 1v1c-2 1-1 5-1 8-1-2-2-3-2-4v-8-1c-2-5-5-8-9-10-2-1-4-1-6-1l1-1h2v-1h2-1c-2 0-4-1-5-1-1-1-2-1-2-2l-1 1h0c-1 2-2 2-3 4h-2 0c-1 1-2 2-2 3v1l-4 1v-1c4-2 7-7 10-10v-4c-2 1-3 2-4 3 0-3 0-5-1-8-1-2-4-4-5-7z" class="C"></path><path d="M570 653c1 0 3 0 5-1l1 4h-1v2l-3-3c-1-1-2-1-2-2z" class="R"></path><path d="M557 653h1c3 1 5 2 7 4h-8v-4z" class="N"></path><path d="M547 641l4 1-2 1h0l1 1c2 2 4 6 7 8l1 1h-1c-2 1-3 2-4 3 0-3 0-5-1-8-1-2-4-4-5-7z" class="D"></path><path d="M560 644c1 0 1 0 1 1 1 1 2 2 3 2 2 0 4-1 6-2h1l2 3h2v3 1c-2 1-4 1-5 1h-3c-5-2-9-4-13-8 1-1 2-1 3-1 1 1 2 2 3 2v-2z" class="j"></path><path d="M560 644c1 0 1 0 1 1 1 1 2 2 3 2 2 0 4-1 6-2h1l2 3c-1 0-1 1-2 1-4 1-8-1-11-3v-2z" class="G"></path><path d="M557 657h8c6 2 10 4 12 10 2 2 3 5 3 8 0 1 0 1 1 2l-1 1v1c-2 1-1 5-1 8-1-2-2-3-2-4v-8-1c-2-5-5-8-9-10-2-1-4-1-6-1l1-1h2v-1h2-1c-2 0-4-1-5-1-1-1-2-1-2-2l-1 1h0c-1 2-2 2-3 4h-2 0c-1 1-2 2-2 3v1l-4 1v-1c4-2 7-7 10-10z" class="t"></path><path d="M577 675c1-2 0-4 0-6l3 9v1c-2 1-1 5-1 8-1-2-2-3-2-4v-8z" class="k"></path><path d="M583 615h3c1 1 1 2 3 3h1l3 1c-3 2-5-1-9 1 1 1 2 1 3 1 2 1 6 4 6 5 1 1 2 1 3 1s2-1 3-1c2 0 2 0 4-1h1v-1 1l10-2h1v2l2 1-3 6c0 1-1 2-1 2l-2 4-5 14-2 4-1 5-4 10c-1-2 0-4-2-6s-3-6-4-9c-2-2-2-4-2-6l-1-1c1 0 0-5 0-6v-1c1-1 1-1 1-2 1-1 0-1 0-2-1 0 0 0-1 1v-1c-1 2-1 2-3 3l-1-1c0 1 1 6 1 7 1 1 1 2 1 4v1c-1-2-1-4-1-6-1 0-3-8-3-10h-1c-1-1-2-1-3-2v-1h-1s-1 1-2 1h-1c-1 0-1-1-2-1h0v-1c0-2 0-2 2-3h1c1 1 0 1 2 1v-2c0-4 1-7 2-11l2-2z" class="E"></path><path d="M583 636c2 0 3 0 5 1l1 1-2 2c-1-1-2-2-3-4h-1z" class="R"></path><path d="M585 633h2c-2-2-4-2-5-4 1 0 6 3 7 3 1 1 1 2 1 3-2 1-4 0-6 0l-1-1 2-1z" class="D"></path><path d="M579 628c2 2 4 4 6 5l-2 1c-1 0-2-1-3-1h-1s-1 1-2 1h-1c-1 0-1-1-2-1h0v-1c0-2 0-2 2-3h1c1 1 0 1 2 1v-2z" class="B"></path><path d="M574 633h0v-1c0-2 0-2 2-3l3 4s-1 1-2 1h-1c-1 0-1-1-2-1z" class="T"></path><path d="M584 620c1 1 2 1 3 1 2 1 6 4 6 5 1 1 2 1 3 1s2-1 3-1c2 0 2 0 4-1h1v-1 1c-2 2-5 3-6 5l-1 1c-3 5-4 10-5 15l-1 1c0-3 0-6 1-8s2-4 2-5c0-4-4-6-7-8-1-1-4-2-4-4l1-2z" class="K"></path><path d="M615 625l2 1-3 6c0 1-1 2-1 2l-2 4-5 14-2 4h-5c-2 0-3-1-5-2v-4l1-1c0-1 0-1 1-2 1 0 1 0 1-1v-1-2h-2l-1-1 2-1v-1c0-3 3-7 5-9h1c4-3 8-5 13-6z" class="Q"></path><path d="M610 635c1-1 2-1 3-1l-2 4h-2c1-1 1-2 1-3z" class="E"></path><path d="M610 635c0 1 0 2-1 3-3 1-5 1-8 2-1 1-3 1-4 1 4-3 9-4 13-6z" class="W"></path><defs><linearGradient id="u" x1="782.75" y1="185.325" x2="783.222" y2="201.206" xlink:href="#B"><stop offset="0" stop-color="#0e0a0b"></stop><stop offset="1" stop-color="#2c3132"></stop></linearGradient></defs><path fill="url(#u)" d="M787 162l2-1h3 4v1c6-1 12-1 18 0l9 4c4 1 7 1 10 2h1l15 1h3v1l1 1h0l3 3-1 1c-1 1-3 2-6 3-2 1-3 2-4 4l-1 2h-2c-1 1-1 1-1 3-1 1-2 2-4 3l1 1c0 2 0 3 1 5h0c-1 2-1 2-1 4v1c2 0 3 2 4 3l-2 1h0-1-3v-1l-1 1c-2 2-3 3-6 4-9 1-16-5-25-7v-1h-7l1 1h-1c-4-2-7-3-12-1-1 1-2 1-2 2-1 1-2 1-4 1l-1 2c-3 2-6 4-7 7h0l-1 2c-2 0-4-1-5-1s-2 2-3 3c-2 0-2 0-4-1v-1c0-1-1-2-1-2-3-2-5-3-7-4l-3-2h0c0-1-1-1-2-2l-3 1v-1l6-3h-1l-2-1h0l7-5 19-12c4-2 7-4 11-5h1l4-1c0-1-1-1-1-1l-3-2-1-1h-1v-2-2l-1-1c1-3 3-5 7-7z"></path><path d="M768 192l1-2h1c1-1 1-1 1-2 3-1 2 0 4 1 1 0 3 0 4-1h1c2-2 4-2 7-2h1c-1 0-3 1-3 2h0c0 1 0 0 1 1-8 2-16 4-23 8h-1l8-5h-2z" class="l"></path><path d="M768 192h2l-8 5h1c-5 3-10 4-15 5h-1l-2-1h0l7-5h3v2c2-2 2-2 5-2 1-1 2-2 4-3 1-1 2-1 4-1z" class="P"></path><path d="M767 200c1-1 3-1 4-2l1 1h1c1 2 3 3 4 5l1 2c-3 2-6 4-7 7-2-1-2-2-3-3h0l-2-3c-1-2-3-4-3-5l4-2h0z" class="X"></path><path d="M767 200h0c1 1 2 0 4 0h0l2 2v1c-2 0-2 0-3-1-2 0-3-1-3-2z" class="Y"></path><path d="M745 205c5-2 10-2 16-2l1 1v2h0 2l2 3-2 2v1c2-1 3-2 4-2 1 1 1 2 3 3h0l-1 2c-2 0-4-1-5-1s-2 2-3 3c-2 0-2 0-4-1v-1c0-1-1-2-1-2-3-2-5-3-7-4l-3-2h0c0-1-1-1-2-2z" class="T"></path><path d="M750 209c2 0 2 0 4 1h1c0 1 1 1 2 2h1l2 2h1c1-1 2-1 2-3 0-1-1-3-1-3h-2c-1 0-3-2-3-3 1 0 3 1 5 1h0 2l2 3-2 2v1c2-1 3-2 4-2 1 1 1 2 3 3h0l-1 2c-2 0-4-1-5-1s-2 2-3 3c-2 0-2 0-4-1v-1c0-1-1-2-1-2-3-2-5-3-7-4z" class="P"></path><path d="M787 178c2 1 4 2 5 3 1 0 2 2 3 3v1h-1c-2 0-2-1-4-2h-2l3 2-1 1h-2-1c-3 0-5 0-7 2h-1c-1 1-3 1-4 1-2-1-1-2-4-1 0 1 0 1-1 2h-1l-1 2c-2 0-3 0-4 1-2 1-3 2-4 3-3 0-3 0-5 2v-2h-3l19-12c4-2 7-4 11-5h1l4-1z" class="p"></path><path d="M778 183h4l1 1c-1 1-2 1-3 2h0c-2 0-2 0-3-1l1-2z" class="d"></path><path d="M787 178c2 1 4 2 5 3 1 0 2 2 3 3v1h-1c-2 0-2-1-4-2h-2l3 2-1 1h-2-1l1-2-1-1c-2 0-3 0-4 1l-1-1h-4-1l-2 2c-2 0-2-1-4-1 4-2 7-4 11-5h1l4-1z" class="j"></path><path d="M787 178c2 1 4 2 5 3 1 0 2 2 3 3v1h-1c-2 0-2-1-4-2h-2c-1-1-2-1-4-1v1l-2-2h1c1-1 1-1 1-2h-1l4-1z" class="P"></path><path d="M823 166c4 1 7 1 10 2h1l15 1h3v1l1 1h0l3 3-1 1c-1 1-3 2-6 3-2 1-3 2-4 4l-1 2h-2c-1 1-1 1-1 3-3-1-7-2-10-2-7 0-15 2-22 3 2-2 5-1 7-3h-3-5l-22 4c-1-1-1 0-1-1h0c0-1 2-2 3-2h2l1-1-3-2h2c2 1 2 2 4 2h1 1l1-1c3-1 7-1 10-2 3 0 5 0 7-1v-1l1-1v-1h2 1l1-1c0 1 1 2 1 2h1c-2-3-2-6-3-9-1-1-1-3-1-4h1c2 1 4 0 5 0z" class="L"></path><path d="M819 177c0 1 1 2 1 2-1 1-2 2-3 2h-2v-2-1h2 1l1-1z" class="n"></path><path d="M808 185c5-2 9-2 14-3 0 1-2 2-2 2-1 1-3 0-4 1h-3-5z" class="E"></path><path d="M821 171l6 3h2 0c4 0 7 1 11 0h4c1 0 0 0 1 1-3 0-5 3-9 3h-12l-1-1c0-1-1-1-1-2v-1c-1-2-1-1-1-3z" class="n"></path><path d="M827 171c1 1 3 1 4 2h4l13-1 1 1v1l-1 1c0 1 1 1 1 2-7 5-18 4-27 5 1 0 1-1 1-2h2c5-3 12 1 16-1 2-1 4-1 5-2 0 0 1-2 2-2l-1-1c-1-1-2 0-3 0h-4c-4 1-7 0-11 0h0-2v-3z" class="t"></path><path d="M823 166c4 1 7 1 10 2h1l15 1h3c-1 1-3 2-3 3v1l-1-1-13 1h-4c-1-1-3-1-4-2v3l-6-3s0-1-1-2l-1 1-1-1v1c-1-1-1-3-1-4h1c2 1 4 0 5 0z" class="Y"></path><path d="M834 168l15 1h3c-1 1-3 2-3 3v1l-1-1v-1c-2-1-5-1-7-2-3 0-5 1-7-1zm-11-2c4 1 7 1 10 2-2 0-3 0-4 1 0 1 0 1 1 1 2 1 3 1 5 3h-4c-1-1-3-1-4-2v3l-6-3s0-1-1-2l-1 1-1-1v1c-1-1-1-3-1-4h1c2 1 4 0 5 0z" class="X"></path><path d="M827 171c-4 0-5-1-7-4 3 1 6 1 9 2 0 1 0 1 1 1 2 1 3 1 5 3h-4c-1-1-3-1-4-2z" class="L"></path><path d="M852 169v1l1 1h0l3 3-1 1c-1 1-3 2-6 3-2 1-3 2-4 4l-1 2h-2c-1 1-1 1-1 3-3-1-7-2-10-2-7 0-15 2-22 3 2-2 5-1 7-3 1-1 3 0 4-1 0 0 2-1 2-2h0c9-1 20 0 27-5 0-1-1-1-1-2l1-1v-1-1c0-1 2-2 3-3z" class="Z"></path><path d="M852 169v1l1 1h0l3 3-1 1c-1-1-4 1-6 2 0-1-1-1-1-2l1-1v-1-1c0-1 2-2 3-3z" class="AH"></path><path d="M787 162l2-1h3 4v1c6-1 12-1 18 0l9 4c-1 0-3 1-5 0h-1c0 1 0 3 1 4 1 3 1 6 3 9h-1s-1-1-1-2l-1 1h-1-2v1l-1 1v1c-2 1-4 1-7 1-3 1-7 1-10 2l-1 1h-1v-1c-1-1-2-3-3-3-1-1-3-2-5-3 0-1-1-1-1-1l-3-2-1-1h-1v-2-2l-1-1c1-3 3-5 7-7z" class="L"></path><path d="M814 167l2 1 1 4c-1-1-1-1-2-1-1-1-1-2-1-4z" class="P"></path><path d="M787 162l2-1h3 4v1c6-1 12-1 18 0l9 4c-1 0-3 1-5 0h-1c0 1 0 3 1 4 1 3 1 6 3 9h-1s-1-1-1-2-2-4-2-5l-1-4-2-1-1-1c-4-1-8-1-12-1-3 0-6 1-8 0l-5 2h-2c-1 0-1 0-2 1h-1l-2 2-1-1c1-3 3-5 7-7z" class="U"></path><path d="M813 166v-2h2c0 1 1 2 1 3v1l-2-1-1-1z" class="X"></path><path d="M787 162l2-1h3 4v1c-5 1-9 3-13 6l-2 2-1-1c1-3 3-5 7-7z" class="F"></path><path d="M781 173c2 0 4-1 6 0l1 1c2-1 6-1 8-2 3-1 5 0 8-1 2 0 6-1 7 0 2 1 3 3 3 5v2h1v1l-1 1v1c-2 1-4 1-7 1-3 1-7 1-10 2l-1 1h-1v-1c-1-1-2-3-3-3-1-1-3-2-5-3 0-1-1-1-1-1l-3-2-1-1h-1v-2 1z" class="i"></path><path d="M781 172v1c2 1 3 1 4 1 2 0 5 4 7 4 1 0 1-1 2-1l-1 1v1l4 5-1 1h-1v-1c-1-1-2-3-3-3-1-1-3-2-5-3 0-1-1-1-1-1l-3-2-1-1h-1v-2z" class="S"></path><path d="M793 178h1l7-1c3-1 9-2 11 0 1 1 1 2 2 3v1c-2 1-4 1-7 1-3 1-7 1-10 2l-4-5v-1z" class="n"></path><path d="M806 178h0c2 0 3 0 4 1v2c-1 0-2-1-4-2v-1z" class="o"></path><path d="M809 188c7-1 15-3 22-3 3 0 7 1 10 2-1 1-2 2-4 3l1 1c0 2 0 3 1 5h0c-1 2-1 2-1 4v1c2 0 3 2 4 3l-2 1h0-1-3v-1l-1 1c-2 2-3 3-6 4-9 1-16-5-25-7v-1h-7l1 1h-1c-4-2-7-3-12-1-1 1-2 1-2 2-1 1-2 1-4 1l-1 2-1-2c-1-2-3-3-4-5h-1 1v-2l2-1h1c1-1 2-1 3-1l3-1h3c2 0 3-1 4-1 4-1 7-2 11-3 3-1 6-1 9-2z" class="h"></path><path d="M823 206h5c2 1 3 0 6-1v-1l1 1c-2 2-3 3-6 4-2-2-4-2-6-3z" class="O"></path><path d="M803 195h3c2 0 3 1 5 1l-6 1-10-1h-3c1-1 9-1 11-1z" class="a"></path><path d="M838 191c0 2 0 3 1 5h0c-1 2-1 2-1 4v1c2 0 3 2 4 3l-2 1-3-3c-2-4-1-7 1-11z" class="J"></path><path d="M803 195c2-2 5-2 8-3 1 0 3-1 4-2 5-1 9-2 14-2 2 0 4 0 6 1-3 0-5 0-7 1-1 1-1 0-2 0-4 1-9 2-12 4v1h-8-3z" class="O"></path><path d="M835 189v1c-1 3-2 3-4 4h-10l-7 1v-1c3-2 8-3 12-4 1 0 1 1 2 0 2-1 4-1 7-1z" class="J"></path><path d="M821 194h-3c2-1 3-1 5-1v-1c1 0 1 0 2-1h1 2l3 3h-10z" class="e"></path><path d="M805 197h22c2 0 5-1 6-1s1 1 1 1v1h-2c-3 0-7 1-10 1-2 1-2 1-4 1-1 0-1 0-2 1l-5-1h0c-2 0-3-1-5-1-1 1-1 1-2 0h1c-1 0-2-1-3 0-3 0-5-1-8-2h11z" class="J"></path><path d="M805 199c4-1 7-1 11 0 2 0 4-1 6 0-2 1-2 1-4 1-1 0-1 0-2 1l-5-1h0c-2 0-3-1-5-1-1 1-1 1-2 0h1z" class="B"></path><path d="M822 199c3 0 7-1 10-1 0 2 1 3 2 4v1c-1 0-3 2-3 2-2 1-8 0-10 0 1-1 1-1 2-1l1-1h-1-4c-1-1-3-1-5-1-1 0-3 0-4-1l1-1 5 1c1-1 1-1 2-1 2 0 2 0 4-1z" class="J"></path><path d="M822 199c3 0 7-1 10-1 0 2 1 3 2 4-6 0-12 0-18-1 1-1 1-1 2-1 2 0 2 0 4-1z" class="c"></path><path d="M782 194c3 0 6 1 8 2h2 3c-2 0-5 0-7 1v1l-3 3c-1 1-2 1-2 2-1 1-2 1-4 1l-1 2-1-2c-1-2-3-3-4-5h-1 1v-2l2-1h1c1-1 2-1 3-1l3-1z" class="M"></path><path d="M782 194c3 0 6 1 8 2h2 3c-2 0-5 0-7 1v1l-3 3c-1 1-2 1-2 2-1 1-2 1-4 1l6-6c-1 0-3 0-4-1-2 0-3 0-6-1h1c1-1 2-1 3-1l3-1z" class="R"></path><path d="M785 201l3-3v-1c2-1 5-1 7-1l10 1h0-11c3 1 5 2 8 2 1-1 2 0 3 0h-1c1 1 1 1 2 0 2 0 3 1 5 1h0l-1 1c1 1 3 1 4 1 2 0 4 0 5 1h4 1l-1 1c-1 0-1 0-2 1-1 0-2 0-3-1h-2l7 2c2 1 4 1 6 3-9 1-16-5-25-7v-1h-7l1 1h-1c-4-2-7-3-12-1z" class="V"></path><defs><linearGradient id="v" x1="580.679" y1="583.49" x2="561.599" y2="576.439" xlink:href="#B"><stop offset="0" stop-color="#2b2c2e"></stop><stop offset="1" stop-color="#494c51"></stop></linearGradient></defs><path fill="url(#v)" d="M580 559v-3c-1-1-1-1-1-2s0-1 1-1c0 1 1 1 2 2 3-1 4-3 6-5h0c1-2 1-3 1-4h0c2-3 0-7 2-10 0 3 1 8 3 10 1 1 1 1 1 2l-19 48 4 3 3 2 1-1c0 1 1 2 2 2h1l2 1c-1 1-1 2-2 2l-4 10-2 2c-1 4-2 7-2 11v2c-2 0-1 0-2-1-1-4-2-8-2-12v-2c-2 1-2 2-3 4v1h-1c0-1-1-2-1-4-2 3-3 6-5 8l-1 3-2-1c-2 2-4 3-6 3-3 2-8 2-12 2l-8-2c-7-5-12-16-15-24h1l1 1c1-1 1-3 2-4 2-2 7-6 7-8v-1h1v7l1-3c1-3-1-6 1-9 1-1 2-3 3-4l1-4c0-1 0-2 1-3l1-1v-1l1 2h0c1-2 1-4 2-5 1-2 1-4 2-6h1l1-1-3-3h1l1-1v-1c0-1 0-2 2-4l3 2h2c4 4 8 9 14 9 1 0 2 0 3-1h1c2 0 6-4 7-5v-1l1-1z"></path><path d="M580 599l3 2 1-1c0 1 1 2 2 2-2 1-4 1-5 3 0-2-1-4-1-6z" class="U"></path><path d="M560 604c0 1 1 2 2 4h0c2 2 2 3 3 6v6c-1 1-1 3 0 4l-1 3-2-1c-2 2-4 3-6 3l1-1c1 0 2-1 3-1 1-1 1-1 1-2 1-1 1-1 1-2 1-1 0-2 0-4v-3-3l-2-6v-3z" class="G"></path><path d="M560 604c0 1 1 2 2 4h0l1 3c1 4 1 7 0 10 0 2 0 4-1 5-2 2-4 3-6 3l1-1c1 0 2-1 3-1 1-1 1-1 1-2 1-1 1-1 1-2 1-1 0-2 0-4v-3-3l-2-6v-3z" class="C"></path><path d="M570 616c2-4 3-8 4-12 0-2 0-5 2-6h1c2 2 3 6 4 9v1c-2 3-3 5-6 7-2 1-2 2-3 4v1h-1c0-1-1-2-1-4z" class="p"></path><path d="M586 602h1l2 1c-1 1-1 2-2 2l-4 10-2 2c-1 4-2 7-2 11v2c-2 0-1 0-2-1-1-4-2-8-2-12v-2c3-2 4-4 6-7v-1-2c1-2 3-2 5-3z" class="b"></path><path d="M586 602h1c-1 1-1 2-2 3l-4 3v-1-2c1-2 3-2 5-3z" class="T"></path><path d="M581 617v-3c1-1 0-1 0-2 1-2 4-6 6-7l-4 10-2 2z" class="H"></path><path d="M546 562l1-1v-1c0-1 0-2 2-4l3 2h2c4 4 8 9 14 9 1 0 2 0 3-1h1c2 0 6-4 7-5v-1l1-1c0 3-1 5-3 7 0 1-1 2-1 2v1l-4 4-2 1c-1 0-1 1-2 2h-1l1-2h0-1-1v1l-1 1c-1-1-1-1-1-2h-1l-1 11c2-2 4-5 6-5-2 4-6 7-7 12h0c-1 3 1 7-1 10h0-1l1 2v3l2 6v3c0 2 0 5-2 6-2 3-4 4-7 5-1-1-2-3-3-5-2-7 2-16 4-23v-14c0-7-1-15-6-20l-3-3h1z" class="C"></path><path d="M567 574c-4-1-5-3-8-7 2 1 4 3 6 3 4 2 7 1 10-1l1-1v1l-4 4-2 1c-1 0-1 1-2 2h-1l1-2h0-1z" class="B"></path><path d="M546 562l1-1v-1c0-1 0-2 2-4l3 2c1 2 3 3 5 5v1c1 1 1 2 1 3v1c-1-1-1-2-1-3l-3-3h0 0c2 4 2 8 2 12v-2l-2-3-1-4c-1-2-2-2-4-2-1 0-2-1-3-1h0z" class="V"></path><defs><linearGradient id="w" x1="559.122" y1="604.858" x2="552.964" y2="620.081" xlink:href="#B"><stop offset="0" stop-color="#333537"></stop><stop offset="1" stop-color="#505155"></stop></linearGradient></defs><path fill="url(#w)" d="M554 623c-1-3-1-6-1-8l1-1c0-2 1-5 2-7 0-1 0-3 1-4 0-2 1-2 2-2l1 1h-1l1 2v3l2 6v3c0 2 0 5-2 6h0l-2 2c-1 1-2 1-4 1l-1-2h1z"></path><path d="M554 623l1-1c0-1 1-2 1-3 0-2-1-5 0-6l1-1v2c1-1 1-1 1-2h0 2l1 1v1l1-1v3c0 2 0 5-2 6h0l-2 2c-1 1-2 1-4 1l-1-2h1z" class="N"></path><path d="M546 562h0c1 0 2 1 3 1 2 0 3 0 4 2l1 4 2 3v2l1 4c1 9-1 20-3 29 0 3-2 6-2 9 0 2 0 5 1 7l1 2c2 0 3 0 4-1l2-2h0c-2 3-4 4-7 5-1-1-2-3-3-5-2-7 2-16 4-23v-14c0-7-1-15-6-20l-3-3h1z" class="g"></path><defs><linearGradient id="x" x1="564.698" y1="600.039" x2="540.121" y2="596.95" xlink:href="#B"><stop offset="0" stop-color="#1a1918"></stop><stop offset="1" stop-color="#363b42"></stop></linearGradient></defs><path fill="url(#x)" d="M548 565c5 5 6 13 6 20v14c-2 7-6 16-4 23 1 2 2 4 3 5 3-1 5-2 7-5 2-1 2-4 2-6v3c0 2 1 3 0 4 0 1 0 1-1 2 0 1 0 1-1 2-1 0-2 1-3 1l-1 1c-3 2-8 2-12 2l-8-2c-7-5-12-16-15-24h1l1 1c1-1 1-3 2-4 2-2 7-6 7-8v-1h1v7l1-3c1-3-1-6 1-9 1-1 2-3 3-4l1-4c0-1 0-2 1-3l1-1v-1l1 2h0c1-2 1-4 2-5 1-2 1-4 2-6h1l1-1z"></path><path d="M543 623l1-5h0c0 3 0 7 1 10h-1c-1-2-1-3-1-5z" class="B"></path><path d="M562 616v3c-1 4-2 6-6 8-1 1-2 0-3 1h-2c-1 0-1 0-2-1-1-2-1-3-1-4l1-1 1 1v-1c1 2 2 4 3 5 3-1 5-2 7-5 2-1 2-4 2-6z" class="F"></path><path d="M542 577c1-2 1-4 2-5l-1 8c-1 5 0 10 1 14v10c0 4 1 8-1 12 0-7 0-14-2-21v-1c0-2-1-4-1-6s0-6-1-8c0-1 0-2 1-3l1-1v-1l1 2h0z" class="I"></path><path d="M539 580c0-1 0-2 1-3l1-1v-1l1 2h0c-1 3 0 7-2 11 0-2 0-6-1-8z" class="E"></path><defs><linearGradient id="y" x1="547.001" y1="623.66" x2="524.875" y2="602.939" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#343539"></stop></linearGradient></defs><path fill="url(#y)" d="M539 580c1 2 1 6 1 8s1 4 1 6v1c2 7 2 14 2 21-1 2 0 3-1 5v2h1c0 2 0 3 1 5h-1 0c1 1 1 1 2 1 0 0-1 0-2 1h4l-3 1-8-2c-7-5-12-16-15-24h1l1 1c1-1 1-3 2-4 2-2 7-6 7-8v-1h1v7l1-3c1-3-1-6 1-9 1-1 2-3 3-4l1-4z"></path><path d="M533 600l1-3 1 6v4c0 4 2 10 0 13-2-6-1-14-2-20z" class="m"></path><path d="M539 580c1 2 1 6 1 8s1 4 1 6v1c2 7 2 14 2 21-1 2 0 3-1 5v2h1c0 2 0 3 1 5h-1c-2-1-2-1-2-2 1-7-1-14-2-21 0 3 0 7-1 10 0 1-1 2-1 4h0v-2c-1-4-1-7-2-10v-4l-1-6c1-3-1-6 1-9 1-1 2-3 3-4l1-4z" class="N"></path><path d="M535 588l1 2 1 7-1 1v-1c0 2 0 4-1 6l-1-6c1-3-1-6 1-9z" class="B"></path><path d="M623 291c6 0 9 1 14 4 1 2 2 3 4 5l1 2-33 82-2 4-9 20c-2 1-3 1-4 3v2l-2-2c-1-3-2-6-2-10h1l1-7c-1-1-1-3-1-4 0-2-1-2-2-3-1-4-3-7-5-10s-4-6-4-9c-1-4-3-7-3-11l-1-1c0-2 0-4 1-6 1-3 3-7 4-9 2-2 4-4 6-5 3-4 9-7 13-9 2-1 4-3 7-3 1-1 3-2 5-2l-1-1c-2 1-5 2-8 3l-4 2c-2 1-3 2-6 3l-2-2h-1c-1 2-3 4-4 6-2 2-2 4-4 6v-1c2-7 6-15 11-21 1-2 3-4 5-6h1c0-2 1-3 2-4h0c0-4-3-7-5-10 0-1-1-1-1-2v-3c4 3 8 4 9 9v2c1 0 2 0 3-1s5-3 6-3v-2c2-4 6-4 10-5v-1z" class="Z"></path><path d="M631 313l1 1c0 2 0 2-2 4h-2v-1h0c2 0 3-2 3-4z" class="a"></path><path d="M616 321c1-1 2-1 3-1s2 0 3 1c-2 1-3 2-6 2v-2z" class="D"></path><path d="M626 317v-1c2-2 3-3 5-3 0 2-1 4-3 4h0-2z" class="C"></path><path d="M620 307c2 0 3-1 5 0v1c-2 1-3 2-5 2l1-2-1-1h0z" class="a"></path><path d="M626 317h2v1c-2 1-3 1-4 2s-1 0-2 1c-1-1-2-1-3-1 2-2 5-2 7-3z" class="h"></path><path d="M623 329h3v2l-1 1h-1-3v1h1v1c-2 0-4 1-6 1 1-1 3-4 5-4h4v-1c-1 0-2-1-2-1h0z" class="I"></path><path d="M617 317l3 1-6 3v1h-2l-1-1c1-1 2-1 2-2h1v-1c1-1 2-1 3-1z" class="c"></path><path d="M613 326l2-1h5l3-1c2 0 3 0 6 1v1c-4 2-9 0-13 1-2 0-4 1-6 2h-1l1-1 1-1c1 0 1-1 2-1z" class="Q"></path><path d="M617 317c5-6 9-9 15-12l-8 8c-1 2-3 3-4 5l-3-1z" class="K"></path><path d="M611 341c2 2 4 4 5 6 0 2-1 4-2 5l-1 1h-1v1c-1-1-1-2-2-2 1-1 1-2 1-3 1-3 0-4-1-6 0 0 1-1 1-2z" class="H"></path><path d="M595 292c4 3 8 4 9 9v2c1 0 2 0 3-1s5-3 6-3c2 0 3-1 4-1h2 2 0c-1 1-1 1-2 1s-1 0-2 1h-2c-4 1-11 4-14 7h0c0-4-3-7-5-10 0-1-1-1-1-2v-3z" class="F"></path><path d="M610 329c2-1 4-2 6-2v1c2 0 4 0 7 1h0s1 1 2 1v1h-4c-2 0-4 3-5 4h-3 0c1 1 3 2 4 3l-3-1c-1 0-2-1-3-2h0l-1-1h4l-5-1c-1 0-3 1-4 0h2v-1c1-1 4-1 5-2h-3l1-1z" class="h"></path><path d="M610 329c2-1 4-2 6-2v1c1 1 2 1 3 1v1h-7-3l1-1z" class="a"></path><path d="M605 352l3-3h0c0 1 0 3-1 4 1 0 2 0 3-1 1 0 1 1 2 2h-1c0 1-1 3-3 3 0-1 0 0 1-1 0-1 1-1 1-2l-1-1h0c-1 1-2 1-2 1-1 0-2 1-2 2l1 2c-1 1-2 1-3 2v1c1 1 1 1 1 2l1-1v1c2 1 3 2 4 4v2 3h-1l-4-5c-1 2 0 4 0 6-1-1-2-3-2-4-1-1-1-2-1-3-1-2-1-5-2-7-1 0-1-1-1-2h1l1 1v-4h2l3-2z" class="G"></path><path d="M600 354h2l1 1h1c-1 1-1 2-3 3h-1v-4z" class="S"></path><path d="M604 367c-1-2-3-4-2-6 1 0 0 0 1 1v2c2 0 4 1 5 2 0 1 1 2 1 3v3h-1l-4-5z" class="D"></path><defs><linearGradient id="z" x1="605.95" y1="318.71" x2="595.96" y2="341.25" xlink:href="#B"><stop offset="0" stop-color="#3e4245"></stop><stop offset="1" stop-color="#5b5e66"></stop></linearGradient></defs><path fill="url(#z)" d="M614 322l2-1v2l-4 3h1c-1 0-1 1-2 1l-1 1-1 1h1l-1 1h3c-1 1-4 1-5 2-3 1-4 1-6 0-5 3-10 5-15 8 0-1 0-1 1-2v-2c3-4 9-7 13-9 2-1 4-3 7-3 1-1 3-2 5-2h2z"></path><path d="M612 326h1c-1 0-1 1-2 1l-1 1-1 1h1l-1 1h3c-1 1-4 1-5 2-3 1-4 1-6 0 4-1 8-4 11-6z" class="G"></path><path d="M605 312c4-3 8-5 13-6l2 1h0l1 1-1 2c-2 1-3 3-4 4-2 2-6 4-9 5h0l-2 1c3 0 6-1 9-2v1h-1c0 1-1 1-2 2-2 1-5 2-8 3l-4 2c-1 0-2 0-3-1h1l-2-1c5-4 6-8 10-12z" class="B"></path><path d="M605 312c4-3 8-5 13-6l2 1h0c-3 2-7 3-10 4-1 1-3 1-3 2-1 1-2 3-3 4s-1 2-2 3c-2 0-3 1-4 3 0 1-1 1-1 2l-2-1c5-4 6-8 10-12z" class="E"></path><defs><linearGradient id="AA" x1="618.828" y1="311.772" x2="609.672" y2="301.728" xlink:href="#B"><stop offset="0" stop-color="#2a2a2f"></stop><stop offset="1" stop-color="#4b4f52"></stop></linearGradient></defs><path fill="url(#AA)" d="M599 311c6-4 14-8 21-9 5-1 10 0 14 1h-2c-1 0-3 0-4 1-4 1-7 1-10 2-5 1-9 3-13 6-4 4-5 8-10 12l2 1h-1c1 1 2 1 3 1-2 1-3 2-6 3l-2-2h-1c-1 2-3 4-4 6-2 2-2 4-4 6v-1c2-7 6-15 11-21 1-2 3-4 5-6h1z"></path><path d="M600 314c1-1 1-1 2-1 1-1 1-1 3-1-4 4-5 8-10 12l2 1h-1c1 1 2 1 3 1-2 1-3 2-6 3l-2-2h-1c1-3 3-6 5-9 2-2 3-4 5-4z" class="Q"></path><path d="M600 314c1-1 1-1 2-1 1-1 1-1 3-1-4 4-5 8-10 12 0 1-1 1-2 2 0-4 5-9 7-12z" class="H"></path><path d="M586 340c5-3 10-5 15-8 2 1 3 1 6 0v1h-2c1 1 3 0 4 0l5 1h-4l1 1h0c1 1 2 2 3 2l3 1h2c-2 0-5 1-7 0l-2 1 1 2c0 1-1 2-1 2 1 2 2 3 1 6 0 1 0 2-1 3s-2 1-3 1c1-1 1-3 1-4h0l-3 3-3 2h-2v4l-1-1h-1c0 1 0 2 1 2 1 2 1 5 2 7 0 1 0 2 1 3 0 1 1 3 2 4l2 8c1 2 1 2 3 3l-2 4-9 20c-2 1-3 1-4 3v2l-2-2c-1-3-2-6-2-10h1l1-7c-1-1-1-3-1-4 0-2-1-2-2-3-1-4-3-7-5-10s-4-6-4-9c-1-4-3-7-3-11l-1-1c0-2 0-4 1-6 1-3 3-7 4-9 2-2 4-4 6-5v2c-1 1-1 1-1 2z" class="Z"></path><path d="M606 381c1 2 1 2 3 3l-2 4-2-5 1-2z" class="C"></path><path d="M605 339c2 0 4-1 7-1l-2 1 1 2c0 1-1 2-1 2-3-2-5-2-8-3l3-1z" class="T"></path><path d="M591 390l1-1c0 2 0 4 1 5 0 4-1 8-1 11 0 2 1 5 0 6-1-3-2-6-2-10h1l1-7c-1-1-1-3-1-4z" class="h"></path><path d="M601 347l2-2 1 1c1 0 1 0 1 2v4l-3 2h-2c0-1 1-2 0-3l-3 3h0c0-2 1-3 1-4 2-1 3-2 3-3z" class="K"></path><path d="M601 347l2-2 1 1c1 0 1 0 1 2l-1 1c0 1-1 1-2 2 0-2-1-3-1-4zm-9 2l-1-1v1c-4 8-1 16 2 24-3-3-5-5-5-9-1-6-1-12 2-17 2-1 3-2 5-2v1l-1 1-2 2z" class="H"></path><path d="M586 372v-1l3 3c2 1 3 2 4 3 1 3 1 7 1 10 1 5 3 8 3 13l-4-6c-1-1-1-3-1-5 1-3 1-6 1-9-2-3-5-6-7-8z" class="O"></path><path d="M597 354h0l3-3c1 1 0 2 0 3v4l-1-1h-1c0 1 0 2 1 2 1 2 1 5 2 7 0 1 0 2 1 3 0 1 1 3 2 4l2 8-1 2-6-16c0-2-1-5-2-7 0-1-1-3-1-4s0-2 1-2z" class="c"></path><path d="M586 372c-2-2-3-4-4-6-2-5-2-11 1-16h0c2-3 4-6 7-8h1c-3 3-7 7-7 12v1c0 6 0 10 4 15l1 1v3l-3-3v1z" class="B"></path><path d="M592 349l2-2v1c-5 13 8 28 7 42-1-1-1-1-1-2-1-11-8-19-10-29v-1c0-3 0-6 2-9z" class="b"></path><defs><linearGradient id="AB" x1="580.048" y1="369.45" x2="617.656" y2="347.302" xlink:href="#B"><stop offset="0" stop-color="#0e0f10"></stop><stop offset="1" stop-color="#2e2f33"></stop></linearGradient></defs><path fill="url(#AB)" d="M586 340c5-3 10-5 15-8 2 1 3 1 6 0v1h-2c1 1 3 0 4 0l5 1h-4l1 1h0c1 1 2 2 3 2l3 1h2c-2 0-5 1-7 0-3 0-5 1-7 1l-3 1c-4 0-8 0-11 2h-1c-3 2-5 5-7 8h0c-3 5-3 11-1 16 1 2 2 4 4 6s5 5 7 8c0 3 0 6-1 9l-1 1c0-2-1-2-2-3-1-4-3-7-5-10s-4-6-4-9c-1-4-3-7-3-11l-1-1c0-2 0-4 1-6 1-3 3-7 4-9 2-2 4-4 6-5v2c-1 1-1 1-1 2z"></path><path d="M600 337h14l3 1h2c-2 0-5 1-7 0-3 0-5 1-7 1s-3-1-5-2z" class="f"></path><defs><linearGradient id="AC" x1="596.69" y1="337.255" x2="601.81" y2="340.745" xlink:href="#B"><stop offset="0" stop-color="#77787b"></stop><stop offset="1" stop-color="#8b8e8f"></stop></linearGradient></defs><path fill="url(#AC)" d="M590 342l1-1c3-2 5-3 9-4 2 1 3 2 5 2l-3 1c-4 0-8 0-11 2h-1z"></path><path d="M587 336v2c-1 1-1 1-1 2-4 5-8 9-10 16 0-2 0-4 1-6 1-3 3-7 4-9 2-2 4-4 6-5z" class="e"></path><path d="M630 245c2-2 3-5 3-8 6 0 12 0 17 2h1c2 1 3 2 4 4v3c1 1 2 1 3 2 1 3 3 6 3 9 1 9-2 18-5 27-5 13-11 25-16 38l-1 3h5l6-1 1 1v2l2 1c-2 1-4 1-5 2h1l-3 3h-1c-2 0-2 0-4-1l-1 1c1 1 2 1 2 1 0 2-2 2-2 3l1 1c1 1 1 2 1 3 1 2 2 4 2 5 1 0 2 0 3 1l-1 3-1 2-1-1v1c0 1 0 2-1 3v2s0 1-1 2l-1 1 2 2-1 1c-1 1-1 2-2 3l1 1 1 2c-1 1-2 3-3 4l-5 10-1-1-1 2h-1c0 1-1 2-1 3l-1-3-1-2v-1c-1 0-1-1-2-2h0c-1-1-2-1-3-2l-2 3c-1 1-1 2-2 4l-4 11-3 7c-1 0-1 1-1 2 1 0 0 0 1-1 2-1 5-2 7-3h1c0 2-1 3-1 5 0 1 0 2-1 3v2h3v3l-1 2c0 4-2 8-4 12 0 2 0 3-2 5h1v2c0 2 1 5 2 7v1l-3 3c-2 1-7 6-8 9l-3 3-1-1c-6 6-11 12-12 20l-1 1c-1-1-2-1-3-2l-4 1c-2 0-4 1-6 2h-2-5l-11 2c-3 0-5 0-7 1h-3-4l2-2c3-1 7-2 11-3 2-1 4-1 6-2h1l-1-1c3-1 6-2 8-4 2-1 3-2 3-3 3-4 5-10 7-15 3-7 6-14 8-21l2-7v-4c1-1 1-1 0-2 1-1 1 0 2-1 0 0 0-2 1-3v-2c1-2 2-2 4-3l9-20 2-4 33-82 1-1c3-9 5-18 7-28 0-3 2-7 1-10-1-4-1-7-4-10-2-1-4-1-6-1-7 0-13 0-19 1 1-1 2-2 2-3 1-3 1-4 0-6v-1-1l2 2c2 1 2 1 3 0l1 1z" class="n"></path><path d="M594 413v-2c1-2 2-2 4-3l-7 15v-4c1-1 1-1 0-2 1-1 1 0 2-1 0 0 0-2 1-3z" class="I"></path><path d="M630 245c2-2 3-5 3-8 6 0 12 0 17 2h1c2 1 3 2 4 4v3c-5 1-10 1-15 1-3 0-8 0-11-1l1-1z" class="m"></path><path d="M650 239h1c2 1 3 2 4 4h-4c-1 0-2 0-3-1l2-3z" class="E"></path><path d="M650 324l1 1v2l2 1c-2 1-4 1-5 2h1l-3 3h-1c-2 0-2 0-4-1l-1 1c1 1 2 1 2 1 0 2-2 2-2 3l1 1h-3c-1 2-1 4-1 6l-1 1c-1 1-1 1-1 3-2 3-5 7-6 10l-2 4c-1 1-2 2-2 3l-1 1v3h-1-1c0-1 1-2 1-3 0-4 2-8 4-11l12-30h5l6-1z" class="F"></path><path d="M651 327l2 1c-2 1-4 1-5 2l-8 2 1-2c2-2 3-2 5-2s3-1 5-1z" class="H"></path><path d="M640 332l8-2h1l-3 3h-1c-2 0-2 0-4-1l-1 1c1 1 2 1 2 1 0 2-2 2-2 3l1 1h-3l2-6z" class="U"></path><path d="M638 338h3c1 1 1 2 1 3 1 2 2 4 2 5 1 0 2 0 3 1l-1 3-1 2-1-1v1c0 1 0 2-1 3v2s0 1-1 2l-1 1 2 2-1 1c-1 1-1 2-2 3l1 1 1 2c-1 1-2 3-3 4l-5 10-1-1-1 2h-1c0 1-1 2-1 3l-1-3-1-2v-1c-1 0-1-1-2-2h0c-1-1-2-1-3-2l-2-1c-1 0-1 1-2 2 0 1-1 1-2 2 1-5 3-10 6-14 0 1-1 2-1 3h1 1v-3l1-1c0-1 1-2 2-3l2-4c1-3 4-7 6-10 0-2 0-2 1-3l1-1c0-2 0-4 1-6z" class="S"></path><path d="M635 361l2-12c1 4 1 9 1 13l-2 1-1-2z" class="P"></path><path d="M629 372c2-3 1-5 1-8h2c0-2 0-4 1-6l1-1c0 4-1 8-2 11-1 4-2 8-2 12-1-1-1-2-1-3-1-2-1-3 0-5z" class="M"></path><path d="M623 366c0 1-1 2-1 3h1 1v-3l1-1c0-1 1-2 2-3 0 2-1 4-2 6s-2 2-3 3v1l1 1c0 1 0 1-1 2l-1 1c-1 0-1 1-2 2 0 1-1 1-2 2 1-5 3-10 6-14z" class="D"></path><path d="M632 368h1c0 5 0 11-2 16 0 1-1 2-1 3l-1-3c1-2 1-3 1-4 0-4 1-8 2-12z" class="G"></path><path d="M629 372c-1-3-1-5 0-8 0-3 2-8 4-10l2-2c0 2 0 4-1 5l-1 1c-1 2-1 4-1 6h-2c0 3 1 5-1 8z" class="J"></path><path d="M635 361l1 2 2-1-1 4-3 10c-1 2-1 4-1 6l-1 2h-1c2-5 2-11 2-16l2-7z" class="L"></path><path d="M637 366l1-1c1-1 2-2 4-2-1 1-1 2-2 3l1 1 1 2c-1 1-2 3-3 4l-5 10-1-1c0-2 0-4 1-6l3-10z" class="B"></path><path d="M637 366l1-1c1-1 2-2 4-2-1 1-1 2-2 3-2 2-3 5-3 7-1 1-1 2-1 3h-2l3-10z" class="G"></path><path d="M638 338h3c1 1 1 2 1 3 1 2 2 4 2 5 1 0 2 0 3 1l-1 3-1 2-1-1v1c0 1 0 2-1 3v2s0 1-1 2l-1-2c-1-5-4-8-5-12l1-1c0-2 0-4 1-6z" class="g"></path><path d="M644 346c1 0 2 0 3 1l-1 3h-1l-1-4z" class="M"></path><path d="M637 344c1 0 1 0 2 1v-1h1l1 2v4l2 1v1h-1c0 2 1 3 0 5h-1c-1-5-4-8-5-12l1-1z" class="L"></path><path d="M617 380c1-1 2-1 2-2 1-1 1-2 2-2l2 1-2 3c-1 1-1 2-2 4l-4 11-3 7c-1 0-1 1-1 2 1 0 0 0 1-1 2-1 5-2 7-3h1c0 2-1 3-1 5 0 1 0 2-1 3l-1-1 1-1v-1c-4 1-4 5-7 6h0v-2c-3 3-5 6-6 10l-3 8v2c0 3-2 7-3 9l-5 10c-1 2-2 3-3 5-5 6-6 15-10 22l1 1c-2 0-4 1-6 2h-2-5l-11 2c-3 0-5 0-7 1h-3-4l2-2c3-1 7-2 11-3 2-1 4-1 6-2h1c3-1 6-3 10-4 1 0 2 0 3 1h1l6-12 7-17 16-39 10-23z" class="C"></path><path d="M581 475l1 1c-2 0-4 1-6 2h-2-5c3-2 8-4 12-3z" class="U"></path><path d="M619 400h1c0 2-1 3-1 5 0 1 0 2-1 3l-1-1 1-1v-1c-4 1-4 5-7 6h0v-2c1-1 2-3 3-4h2 0c1-1 2-1 2-2v-1l-5 1c-1 1-1 1-2 1 1 0 0 0 1-1 2-1 5-2 7-3z" class="N"></path><path d="M602 427v2c0 3-2 7-3 9l-5 10c-1 2-2 3-3 5 1-6 3-11 6-17 2-3 3-6 5-9z" class="b"></path><path d="M563 474h1c1 0 2 0 3-1 3-1 6-2 9-2-1 1-4 2-6 3-3 1-7 3-10 4l-12 3h-4l2-2c3-1 7-2 11-3 2-1 4-1 6-2z" class="f"></path><defs><linearGradient id="AD" x1="580.928" y1="458.702" x2="617.537" y2="446.153" xlink:href="#B"><stop offset="0" stop-color="#9e9fa0"></stop><stop offset="1" stop-color="#c9c7c9"></stop></linearGradient></defs><path fill="url(#AD)" d="M611 409v2h0c3-1 3-5 7-6v1l-1 1 1 1v2h3v3l-1 2c0 4-2 8-4 12 0 2 0 3-2 5h1v2c0 2 1 5 2 7v1l-3 3c-2 1-7 6-8 9l-3 3-1-1c-6 6-11 12-12 20l-1 1c-1-1-2-1-3-2l-4 1-1-1c4-7 5-16 10-22 1-2 2-3 3-5l5-10c1-2 3-6 3-9v-2l3-8c1-4 3-7 6-10z"></path><path d="M610 427v1c0 4 1 8-1 13v-1-4-1l-1-1c0-1 1-6 2-7z" class="B"></path><path d="M602 447c2-3 4-6 5-11h2v4 1c-3 11-17 17-22 28h-1c-1-4 13-13 14-18 0-1 1-2 2-4z" class="V"></path><path d="M594 448c0 2 0 8-2 10 0 1-1 1-1 2l4-4 1-1h0c0-2 0-1 1-2v-1c1-2 2-3 4-4l1-1c-1 2-2 3-2 4-1 5-15 14-14 18h1-1c0 2-1 3-2 4l2 2-4 1-1-1c4-7 5-16 10-22 1-2 2-3 3-5z" class="i"></path><path d="M611 409v2h0c3-1 3-5 7-6v1l-1 1 1 1v2h3v3l-1 2c0 4-2 8-4 12 0 2 0 3-2 5h1v2c0 2 1 5 2 7v1l-3 3c-2 1-7 6-8 9l-3 3-1-1c4-4 9-9 11-15 1-4 2-9-1-13h0l1-3h0l-3 3v-1l4-6c1-2 2-3 2-5-1-1-2-2-4-3-1 1-2 2-3 4-2 0-3 1-4 2 1-4 3-7 6-10z" class="O"></path><path d="M617 407l1 1v2c-1 0-2 0-3-1 0-1 1-1 2-2z" class="Q"></path><path d="M617 414l2-1h2l-1 2-3 3v-4z" class="B"></path><path d="M617 414l-3-3 1-2c1 1 2 1 3 1h3v3h-2l-2 1z" class="H"></path><defs><linearGradient id="AE" x1="602.013" y1="413.515" x2="604.349" y2="458.811" xlink:href="#B"><stop offset="0" stop-color="#585a5d"></stop><stop offset="1" stop-color="#94959b"></stop></linearGradient></defs><path fill="url(#AE)" d="M605 419c1-1 2-2 4-2 1-2 2-3 3-4 2 1 3 2 4 3 0 2-1 3-2 5l-4 6c-1 1-2 6-2 7l1 1v1h-2c-1 5-3 8-5 11l-1 1c-2 1-3 2-4 4v1c-1 1-1 0-1 2h0l-1 1-4 4c0-1 1-1 1-2 2-2 2-8 2-10l5-10c1-2 3-6 3-9v-2l3-8z"></path><path d="M742 205v1l3-1c1 1 2 1 2 2h0l3 2c2 1 4 2 7 4 0 0 1 1 1 2v1c2 1 2 1 4 1 1-1 2-3 3-3s3 1 5 1l1-2v4 1c0 4 0 5 2 7 0 1 1 1 1 2h0c-3 0-3-2-5-1s-3 3-5 4h-1l1 2c3 1 4 2 7 2h2 1v-1c3-1 5 0 8-1v-2h3c1 1 2 1 3 1 2 1 3 2 4 3l3 2-3 2c-2-1-3-2-5-3-2 1-3 3-5 3-2 3-4 3-6 5l-2-1v1l-12 2c-1 1-2 1-2 2-1 1-2 1-3 2 0 1 0 1 1 2l-2 2 3-1-3 4h1c1 2 1 3 1 6-1 1-2 1-3 2h0c2 1 3 0 4 2h2c1 3 0 8-2 11h2v3c1 0 1 1 2 1-1 2-1 4-3 5 0 2-1 3-1 4h0l-1 3-2-3v-1l-3 3v1c1 1 1 2 0 4l-3-3c0 1 0 2-2 3l-4 1-1-1h-3c-1-1-2-1-2-2-2 0-3 0-4 2v1l-4 4-3 1c-4 4-6 9-9 13-2 3-4 5-7 7l-1 2-5 3h0v-4l2-1c0-1 1-2 2-3h0c4-3 7-7 10-11-1-1-1-1-1-2l-1-4c-1-2-1-4-1-6s0-4-1-5c1-3 1-5 2-8v-2-1c0-3-1-5-2-8v-6c-1-2-1-3-2-4h-2v-2h-1v-1c0-2 0-2-1-4 0 0-1-1-1-2s1-3 0-5h-1l-3 3h-1c2-2 4-4 5-6v-1l1-1-1-1v-3h0v-1c1-2 1-3 0-5h1l1-1h1l2-2h1c1 0 1-1 2-1l1-1c1-1 2-2 3-4 3-2 5-5 8-8 4-4 9-10 14-12z" class="W"></path><path d="M742 220c-1-1-1-1-1-2 1-1 2-1 4-2v4l-1 3-2-3z" class="E"></path><path d="M742 220v-1c1 1 2 1 3 1l-1 3-2-3z" class="C"></path><path d="M755 264c2 1 3 0 4 2-1 0-3 0-5 1-1 0-2 0-3 1h-4c-3 2-6 2-9 4 0 0 0-1 1-1h1c0-1 1-2 1-3h0c3-2 6 0 8-2 0-1 1-1 1-2 2 1 3 1 5 1v-1z" class="I"></path><path d="M745 216l4 1h2c2 1 4 2 5 5h-1 0l-4-4h-1 0c-2 3-2 4-2 7h0l-1 1h-1 0c-1-1-1-2-2-3l1-3v-4z" class="F"></path><path d="M728 241l2-3 5-5c-3 5-4 9-5 15l-1 10-1-1v-2-4-2h0c-1-3 1-6 0-8z" class="K"></path><path d="M736 288c0-1 1-2 2-3h2c-1 1-1 2-1 3l1 1v3h-1c0 1 0 1 1 2h-1l-1 1c-2 0-3 0-4 2v1l-4 4c1-3 3-5 4-8 0-1 0-1 1-1v-1-1c1-1 1-2 1-3z" class="a"></path><path d="M726 236h0c0 2-3 4-3 7-1 1-2 2-2 4-1 2-2 5-2 7l-1-1v-2h-1c0 1-1 2-2 3 1-3 1-8 4-11l7-7z" class="I"></path><path d="M750 218c2 2 3 4 3 7-1 1-1 4-1 6v1h-1c-1 0-1 0-2-1l-1 1c-1-2-1-4-2-6h0 1l1-1h0c0-3 0-4 2-7z" class="K"></path><path d="M746 226h0 1c1 0 2 1 3 2s1 2 2 3v1h-1c-1 0-1 0-2-1l-1 1c-1-2-1-4-2-6z" class="H"></path><path d="M743 246l1 2v1h1l-5 4c1 3 2 3 5 5l2 2 1 1c-1 1-4-2-6-2-1 0-1-1-2-1h-7l-1-1 1-2h1c0-1 1-2 2-3l3-2 4-4z" class="E"></path><path d="M743 246l1 2v1c-2 1-3 2-5 4v-3l4-4z" class="J"></path><path d="M739 250v3l-2 2 1 1c2 2 4 0 4 3-1 0-1-1-2-1h-7l-1-1 1-2h1c0-1 1-2 2-3l3-2z" class="F"></path><path d="M736 252v3 1l-1 1-1-2c0-1 1-2 2-3z" class="K"></path><path d="M739 250v3l-2 2h-1v-3l3-2z" class="O"></path><path d="M715 254c1-1 2-2 2-3h1v2l1 1c0 1 0 2 1 3h0v2c-1 2 0 4 0 5v6c0 3 0 3-2 5 0 1 0 0-1 2v5-1c0-3-1-5-2-8v-6c0-4-1-9 0-13z" class="D"></path><path d="M718 275c-1-1-1-2-1-3v-1c-1-4-1-8-1-12 0-1 0-2 1-3h0v3c1 1 1 2 1 3v1 3h1v-2h1v6c0 3 0 3-2 5z" class="S"></path><path d="M757 213s1 1 1 2v1c2 1 2 1 4 1 1-1 2-3 3-3s3 1 5 1l1-2v4 1c-1 2-2 2-2 4h0v2c-1 1-2 1-3 2l-1-1c0 1-1 1-2 1-3-1-4-6-5-9h-1v-4z" class="f"></path><path d="M771 213v4 1c-1 2-2 2-2 4h0v2c-1 1-2 1-3 2l4-11 1-2z" class="C"></path><defs><linearGradient id="AF" x1="732.423" y1="220.144" x2="723.315" y2="237.455" xlink:href="#B"><stop offset="0" stop-color="#2b2c2e"></stop><stop offset="1" stop-color="#4a4d51"></stop></linearGradient></defs><path fill="url(#AF)" d="M728 225h1l3-3c2-1 2-1 4-1 0 3-1 6-3 9l-3 2-4 4h0-2s-1 1-2 1c0 1 0 0-1 0v-3l-1-1c1-2 2-3 4-5l1-1 1-1 2-1z"></path><defs><linearGradient id="AG" x1="720.909" y1="233.299" x2="707.727" y2="237.58" xlink:href="#B"><stop offset="0" stop-color="#4d5157"></stop><stop offset="1" stop-color="#707276"></stop></linearGradient></defs><path fill="url(#AG)" d="M717 229c1 0 3-2 4-3h1c-1 1-1 2-1 2-1 1-1 1-1 2h1l2-2h1c-2 2-3 3-4 5l1 1v3c1 0 1 1 1 0 1 0 2-1 2-1h2l-7 7h-1v-2c-1-1-1-1-2-1-3 0-4 1-6 3l-2 3v-1l1-1-1-1v-3h0v-1c1-2 1-3 0-5h1l1-1h1l2-2h1c1 0 1-1 2-1l1-1z"></path><defs><linearGradient id="AH" x1="719.883" y1="275.604" x2="729.617" y2="251.896" xlink:href="#B"><stop offset="0" stop-color="#27272a"></stop><stop offset="1" stop-color="#3f4144"></stop></linearGradient></defs><path fill="url(#AH)" d="M728 241c1 2-1 5 0 8h0v2 4 2l1 1c0 2-1 5-1 7l-3 16-1-3c-1-5-1-9-1-13 1-8 1-17 5-24z"></path><path d="M728 249h0v2 4c-2 3-1 6-2 9v-5c0-4 1-7 2-10z" class="F"></path><path d="M726 264c1-3 0-6 2-9v2c0 4 0 7-1 11h-1v-4z" class="C"></path><path d="M731 278h1c1 0 1 0 2-1h1v1h-1l1 1h1c2-1 2-1 4-1 1-2-1-3 1-4h0c-1 3 0 7 0 10h0l-1 1h-2c-1 1-2 2-2 3-1 2-3 4-5 5-1 0-2 0-3-1-1-2-2-5-1-7 0-2 2-6 4-7z" class="Q"></path><path d="M710 243c2-2 3-3 6-3 1 0 1 0 2 1v2h1c-3 3-3 8-4 11-1 4 0 9 0 13-1-2-1-3-2-4h-2v-2h-1v-1c0-2 0-2-1-4 0 0-1-1-1-2s1-3 0-5h-1l-3 3h-1c2-2 4-4 5-6l2-3z" class="S"></path><path d="M716 240c1 0 1 0 2 1v2l-1 1h-1c-1-2-1-3 0-4z" class="N"></path><path d="M710 243h2c1 1 1 1 1 2v1c-1 3-1 8-2 11 0 1 0 3-1 4v-1c0-2 0-2-1-4 0 0-1-1-1-2s1-3 0-5h-1l-3 3h-1c2-2 4-4 5-6l2-3z" class="V"></path><path d="M733 255c0-1 0-1 1-2h-2v-3c0-5 3-7 5-12 1-3 3-7 5-9h1l1 4v1c2 1 2 1 3 1 0 4-1 8-4 11l-4 4-3 2c-1 1-2 2-2 3h-1z" class="I"></path><path d="M743 236h0 1c0 2 0 3-1 4l-1 1c-1 1-1 2-2 4h-1v-2c2-2 3-4 4-7z" class="R"></path><path d="M752 243l1 2h2 1 6c-1 1-2 1-2 2-1 1-2 1-3 2 0 1 0 1 1 2l-2 2 3-1-3 4h1c1 2 1 3 1 6-1 1-2 1-3 2l-8-4-2-2c-3-2-4-2-5-5l5-4h-1v-1l8-5z" class="B"></path><path d="M752 243l1 2h2 1 6c-1 1-2 1-2 2-1 1-2 1-3 2 0 1 0 1 1 2l-2 2-1 2h-1l-2-2v-1c-2-2-5-3-7-3h-1v-1l8-5z" class="N"></path><path d="M753 245h2 1-1c0 1-1 1-2 1l-1 3-2-1v-1l3-2z" class="Q"></path><path d="M756 245h6c-1 1-2 1-2 2-1 1-2 1-3 2 0 1 0 1 1 2l-2 2-1 2h-1l-2-2 2 1 1-1-3-3c-1 0 0 0-1-1h1l1-3c1 0 2 0 2-1h1z" class="V"></path><path d="M742 205v1l3-1c1 1 2 1 2 2h0l3 2c2 1 4 2 7 4v4c-2-2-6-4-9-5-4 0-7 1-11 4h0c0 2 0 3-1 5-2 0-2 0-4 1l-3 3h-1l-2 1-1 1-1 1h-1l-2 2h-1c0-1 0-1 1-2 0 0 0-1 1-2h-1c-1 1-3 3-4 3 1-1 2-2 3-4 3-2 5-5 8-8 4-4 9-10 14-12z" class="B"></path><path d="M728 225c3-3 6-7 9-9 0 2 0 3-1 5-2 0-2 0-4 1l-3 3h-1z" class="E"></path><path d="M742 206l3-1c1 1 2 1 2 2h0l3 2c2 1 4 2 7 4v4c-2-2-6-4-9-5h-4v-1h3v-1c-2 0-3 0-4-1 1 0 2 0 3 1 0-2 0-2-1-3h-2c-1 0 0 1-1 0v-1z" class="V"></path><defs><linearGradient id="AI" x1="756.198" y1="278.623" x2="745.424" y2="269.168" xlink:href="#B"><stop offset="0" stop-color="#34363b"></stop><stop offset="1" stop-color="#53555a"></stop></linearGradient></defs><path fill="url(#AI)" d="M759 266h2c1 3 0 8-2 11-6 2-12 4-18 7h0c0-3-1-7 0-10h0c-2 1 0 2-1 4-2 0-2 0-4 1h-1l-1-1h1v-1h-1c-1 1-1 1-2 1h-1l7-6c3-2 6-2 9-4h4c1-1 2-1 3-1 2-1 4-1 5-1z"></path><path d="M751 268c1-1 2-1 3-1l1 1h-2l1 1h2l-1 1h-1c-1 1-3 1-5 1h0v-1h0c1-1 1-2 2-2z" class="H"></path><path d="M759 277h2v3c1 0 1 1 2 1-1 2-1 4-3 5 0 2-1 3-1 4h0l-1 3-2-3v-1l-3 3v1c1 1 1 2 0 4l-3-3c0 1 0 2-2 3l-4 1-1-1h-3c-1-1-2-1-2-2l1-1h1c-1-1-1-1-1-2h1v-3l-1-1c0-1 0-2 1-3l1-1c6-3 12-5 18-7z" class="Q"></path><path d="M756 289l1-1c1 0 1 1 2 2l-1 3-2-3v-1z" class="N"></path><path d="M743 297c2-1 3-2 5-2h0l1-2 1 1c0 1 0 2-2 3l-4 1-1-1z" class="E"></path><path d="M761 280c1 0 1 1 2 1-1 2-1 4-3 5-1-1 0-4 1-6h0z" class="V"></path><path d="M744 287h0c5-1 9-3 13-3v1c-1 1-2 1-2 1-1 0-1 2-1 2-1 1-5 1-6 1h-3-1v-2z" class="a"></path><path d="M759 277h2v3h0c-2 1-2 1-3 2h-1c-5 1-10 4-14 4h0l1 1v2h1 3c-2 0-4 1-5 2 0 1-1 2-2 2-1-1-1 0-1-1v-3l-1-1c0-1 0-2 1-3l1-1c6-3 12-5 18-7z" class="W"></path><path d="M720 270c0-1 1-3 1-4h0c1-1 1-1 2-1 0 4 0 8 1 13l1 3c-1 2-1 4-2 6l4 16c-4 4-6 9-9 13-2 3-4 5-7 7l-1 2-5 3h0v-4l2-1c0-1 1-2 2-3h0c4-3 7-7 10-11-1-1-1-1-1-2l-1-4c-1-2-1-4-1-6s0-4-1-5c1-3 1-5 2-8v-2-5c1-2 1-1 1-2 2-2 2-2 2-5z" class="I"></path><path d="M717 284c1 5 2 14 0 19-1-2-1-4-1-6s0-4-1-5c1-3 1-5 2-8z" class="S"></path><path d="M719 309c1-2 3-4 4-6 1-3 0-6-1-9 0-6-2-12-2-18l3 11 4 16c-4 4-6 9-9 13-2 3-4 5-7 7l-1 2-5 3h0v-4l2-1c0-1 1-2 2-3h0c4-3 7-7 10-11z" class="p"></path><path d="M753 225c1 1 1 2 2 3 3 0 4 2 7 4l1 1 1-1c3 1 4 2 7 2h2 1v-1c3-1 5 0 8-1v-2h3c1 1 2 1 3 1 2 1 3 2 4 3l3 2-3 2c-2-1-3-2-5-3-2 1-3 3-5 3-2 3-4 3-6 5l-2-1v1l-12 2h-6-1-2l-1-2-8 5-1-2c3-3 4-7 4-11l1-3 1-1c1 1 1 1 2 1h1v-1c0-2 0-5 1-6z" class="D"></path><path d="M764 232c3 1 4 2 7 2h2 1 6 3-1c1 1 4 1 5 1-2 1-3 3-5 3h-1c0-1 1-1 1-2h0c-3 0-11 0-14-1-1-1-3-1-5-2l1-1z" class="Q"></path><path d="M782 230h3c1 1 2 1 3 1 2 1 3 2 4 3l3 2-3 2c-2-1-3-2-5-3-1 0-4 0-5-1h1-3-6v-1c3-1 5 0 8-1v-2z" class="K"></path><path d="M783 234c2-1 5-1 7-1l2 1 3 2-3 2c-2-1-3-2-5-3-1 0-4 0-5-1h1z" class="a"></path><path d="M753 225c1 1 1 2 2 3 3 5 6 6 6 13h4v-1c1 0 2 0 4-1h5c-4 3-9 3-13 3h0c-1-3-1-5-2-7h-1c-1-2-2-2-3-3l-1 1c0 1 0 2-1 3 0-1 0-2-1-4v-1c0-2 0-5 1-6z" class="B"></path><path d="M774 239l5-1-5 4v1l-12 2h-6-1v-1c0-1 2-3 3-4 1 1 1 1 1 2h2c4 0 9 0 13-3z" class="J"></path><path d="M748 232l1-1c1 1 1 1 2 1h1c1 2 1 3 1 4 1-1 1-2 1-3l1-1c1 1 2 1 3 3h1c1 2 1 4 2 7h0-2c0-1 0-1-1-2-1 1-3 3-3 4v1h-2l-1-2-8 5-1-2c3-3 4-7 4-11l1-3z" class="T"></path><path d="M750 243h-2v-3c1-1 2-1 2-1l1 1v2l-1 1z" class="P"></path><path d="M754 233l1-1c1 1 2 1 3 3h1c1 2 1 4 2 7h0-2c0-1 0-1-1-2-1 1-3 3-3 4v1h-2l-1-2h2c1-2 1-3 1-5h0c-1 0-1 1-1 2l-3 3h-1l1-1c1-1 2-3 2-6 1-1 1-2 1-3z" class="V"></path><path d="M754 233l1-1c1 1 2 1 3 3h1c1 2 1 4 2 7h0-2c0-1 0-1-1-2-1 1-3 3-3 4v1h-2l-1-2h2l1-1 2-1 1-2v-2c-1-2-2-3-4-4z" class="O"></path><path d="M586 131l3 2h-1l8 5c4 3 9 5 13 7l9 3h2c5 2 9 5 13 8 0 0 2 2 3 2 3 0 5 1 8-1l2 3 3 3c1 1 3 4 4 5 2 2 4 5 5 8 4 4 6 11 9 16 2 5 3 10 4 16v1l-1-2h-1v1c-1 1-1 2-2 3v3l1 8c0 3-1 5-2 7h0l-3 3c-3-1-8-3-10-6-1 0-3 0-4 1l-1 1v1h-6v-1l2-2h-2l-1 2h-2l-1-2-1-1c0 2 0 3-1 4h-1l1-2v-3-2c1-1 1-1 1-2v-6c-2 2-4 3-6 5h-2v-1l-1-1c-1 1-1 2-1 3h-3c0 1 0 1-1 1s-1-1-2-1c-1-1-2-1-2-1h-3c-2 0-2 0-3 1h-4v1l-2-1 1-1c-1-1-2-1-3-2v-1c-1-1-2-2-2-4 0-1-1-1-2-2h0v-1h-2c0-1 4-4 5-5v-2c-1-1-3-1-4-2-2 0-3 0-5 1h-1c-2 0-3 0-4-1l5-1 2-2c2-1 3-3 4-5l1-2 1-1c-1-1 0-1-1-1-1 1-1 1-2 1 0-1 0-1-1-2h1c-1-2-2-2-3-3h4c1 0 1-1 1-1 0-1 0-2-1-3v-1h1 4c-2-1-3-3-4-5v-1-3-2c-1-1-1-2-2-2v2c0 2 1 2 1 4h-1c0-1-1-3-1-4l-1-3c0-2-1-3-1-4-1 1-1 1-2 1v-2l-1-2-1 1h-1l-3-3v-1h3l-2-2v-1c-2-2-4-5-4-8-1-3-1-6-2-9l1-1 1 1v-1-3z" class="E"></path><path d="M586 131l3 2h-1v1c0 2 0 3 1 6l6 17c-5-4-7-10-9-15v-7-1-3z" class="l"></path><path d="M620 155h0 2c4 2 8 3 11 6 2 1 3 3 4 4 4 4 8 7 11 12h-2c-2-3-5-5-7-7s-4-3-5-4-3-2-4-3l-12-6 2-2z" class="S"></path><path d="M644 157l2 3 3 3c1 1 3 4 4 5 2 2 4 5 5 8 4 4 6 11 9 16 2 5 3 10 4 16v1l-1-2h-1v1c-1 1-1 2-2 3l-1-5v-2c-1-4-1-7-3-10l-1-3c-1 0-1 0-2-1s-1-2-1-3c-1 0-1-1-1-1v-1-2s-2-3-2-4c-1 0-1-1-1-2a170.44 170.44 0 0 0-19-19c3 0 5 1 8-1z" class="p"></path><defs><linearGradient id="AJ" x1="673.158" y1="202.592" x2="657.692" y2="190.094" xlink:href="#B"><stop offset="0" stop-color="#919295"></stop><stop offset="1" stop-color="#c0bfc1"></stop></linearGradient></defs><path fill="url(#AJ)" d="M658 176c4 4 6 11 9 16 2 5 3 10 4 16v1l-1-2h-1v1c-1 1-1 2-2 3l-1-5v-2c-1-4-1-7-3-10l-1-3c-1 0-1 0-2-1s-1-2-1-3c-1 0-1-1-1-1v-1-2s-2-3-2-4c2 1 2 2 3 3 0 1 1 2 1 2v1h2v-2l-4-6v-1z"></path><path d="M658 183c2 2 3 5 4 8-1 0-1 0-2-1s-1-2-1-3c-1 0-1-1-1-1v-1-2z" class="F"></path><path d="M648 177l3 3v1l-1 1h0c0 1 0 2 1 3h3l1 1c-1 1-1 2-2 2h-3 0c0 1 0 1 1 2s1 1 2 1l7-1c1 1 1 1 2 1l1 3c2 3 2 6 3 10v2c-1 1-1 2-2 2l-1 1h-1-1v-2l-3-3c-1-1-2-2-4-3-1-1-2-1-3-2l2-1c-1 0-2 0-2-1l-2 1-2 1h-1c0 1 0 1-1 1h-3l1-1v-2h0l-1-2-2 1v-4-4c1-1 3-3 3-4v-1-3l3-3h2z" class="i"></path><path d="M640 188c1 3 2 5 2 7l-2 1v-4-4z" class="M"></path><path d="M649 190c-1 0 0 3 0 4l-1 1c-1-2-1-4-1-6 0-3-1-4 1-6 1 2 1 4 1 6v1z" class="e"></path><path d="M648 177l3 3v1l-1 1c-2 0-4-1-5 0l-2 2v-1-3l3-3h2z" class="F"></path><path d="M648 183l2-1c0 1 0 2 1 3h3l1 1c-1 1-1 2-2 2h-3 0c0 1 0 1 1 2s1 1 2 1l7-1c1 1 1 1 2 1l1 3c-1 0-2-1-3-2l-1 1h0c0 2-1 3-1 4h-3l2 2-1 1c-1-2-2-1-3-2-1 0-2 0-2-1 2-1 6-3 8-5h-1c-2 2-4 2-6 2l-3-4v-1c0-2 0-4-1-6z" class="I"></path><path d="M660 192c1 1 2 2 3 2 2 3 2 6 3 10v2c-1 1-1 2-2 2l-1 1h-1-1v-2l-3-3c-1-1-2-2-4-3-1-1-2-1-3-2l2-1c1 1 2 0 3 2l1-1-2-2h3c0-1 1-2 1-4h0l1-1z" class="W"></path><path d="M660 192c1 1 2 2 3 2 2 3 2 6 3 10-1-1-3-3-3-4-1-2 0-4-2-6 0-1 0-1-1-2z" class="Z"></path><path d="M657 199h1c0-1 1-1 1-2 1 1 1 2 2 3 1 2 1 2 1 4s1 3 1 5h-1-1v-2l-3-3c-1-1-2-2-4-3-1-1-2-1-3-2l2-1c1 1 2 0 3 2l1-1z" class="F"></path><path d="M592 155v1c1 0 2 1 3 1 0 1 0 1 1 1l-1-1c0-4-2-8-3-11-1-4-2-8-3-11 4 3 5 5 7 9l1-1c1 0 1 0 2 1 3 3 9 4 12 7v2c1 0 1-1 2-1v-1c2 0 4 1 6 1l1 1v2l-2 2 12 6c1 1 3 2 4 3v1c1 2 3 2 2 5-1 1-4 2-6 2h-1l-1-1h-2c-1-1-1-1-3-1-2 1-4 0-6 1-1-1-1-1-3-1l6 6-1 1v1l-2 2c-1-1-2-1-4-1l-1-1h-1l-1 1 1 2c-1 0-1 0-2-1-1-2-1-2-3-3s-3-3-4-5v-1-3-2c-1-1-1-2-2-2v2c0 2 1 2 1 4h-1c0-1-1-3-1-4l-1-3c0-2-1-3-1-4-1 1-1 1-2 1v-2l-1-2-1 1h-1l-3-3v-1h3z" class="l"></path><path d="M597 150l2 2-1 3h-1v-3s0-1-1-2h0 1z" class="o"></path><path d="M599 152c2 0 1 1 3 2 1 2 1 2 1 4l1 1h0c2 0 3 1 3 2l1 1h1l1-1h1l1 3-1 1c-1-1-2-2-3-2l-1-1-1 1c-1-1-1-1-1-2-1 0-1-1-2-1h-3v-4l-2-1 1-3z" class="n"></path><path d="M612 179h2l-2-7h0l1-1c0 1 0 1 1 1h0l6 6-1 1v1l-2 2c-1-1-2-1-4-1l-1-1h-1l1-1z" class="j"></path><path d="M612 180c2-1 5-1 7-1v1l-2 2c-1-1-2-1-4-1l-1-1z" class="p"></path><path d="M602 168c4 3 7 7 10 11l-1 1-1 1 1 2c-1 0-1 0-2-1-1-2-1-2-3-3s-3-3-4-5v-1-3-2z" class="E"></path><path d="M596 144l1-1c1 0 1 0 2 1 3 3 9 4 12 7v2c1 0 1-1 2-1v-1c2 0 4 1 6 1l1 1v2l-2 2s-1-1-1-2c-2 1-5 1-6 2h-1v-1h-1c0-1 0-2-1-2h-2-2l-1-2-2-2c0 1 0 2 1 3v1h0c-2-1-1-2-3-2l-2-2c0-2-1-4-1-6z" class="M"></path><path d="M634 166c1 1 3 2 5 4s5 4 7 7l-3 3v3 1c0 1-2 3-3 4v4 4l2-1 1 2h0v2l-1 1h3c1 0 1 0 1-1h1l2-1 2-1c0 1 1 1 2 1l-2 1c1 1 2 1 3 2 2 1 3 2 4 3l3 3v2h1 1l1-1c1 0 1-1 2-2l1 5v3l1 8c0 3-1 5-2 7h0l-3 3c-3-1-8-3-10-6-1 0-3 0-4 1l-1 1v1h-6v-1l2-2h-2l-1 2h-2l-1-2-1-1c0 2 0 3-1 4h-1l1-2v-3-2c1-1 1-1 1-2v-6c0-1 1-2 1-2 0-2-1-3-1-4l-1-6c-1-2-1-4-1-6v-2c0-1-1-1-1-2l-2 2-5-8c-3-5-7-9-10-13 2-1 4 0 6-1 2 0 2 0 3 1h2l1 1h1c2 0 5-1 6-2 1-3-1-3-2-5v-1z" class="b"></path><path d="M637 225v-1c1-1 1-3 1-4s0-1 1-2v-3l1-1v1 4c-1 3-1 4 0 6l2 1-1 2h-2l-1-2-1-1z" class="H"></path><path d="M653 219c-1 1-2 1-3 1-3-2-3-2-4-4l1-1-1-1c1-1 0-1 2-2l-1 2 1 1c2 0 3 0 5-1h1v4l-1 1zm-2 2h4c0 1 1 1 1 2-1 1-1 2-2 3h-1c-1 0-3 0-4 1l-1 1v1h-6v-1l2-2h0l2-2v-1c1-1 4-2 5-2z" class="E"></path><path d="M658 204l3 3v2h1 1l1-1c1 0 1-1 2-2l1 5v3l1 8c0 3-1 5-2 7h0l-3 3c-3-1-8-3-10-6h1c1-1 1-2 2-3 0-1-1-1-1-2h-4c1 0 2 0 3-1l-1-1 1-1v-4h-1l-1-1c0-1-1-1-1-2l3 1 1-1-2-2 1-1v-1l-2-2 6-1z" class="U"></path><path d="M653 214l-1-1c0-1-1-1-1-2l3 1 1-1c1 3 3 8 1 11v1c0-1-1-1-1-2h-4c1 0 2 0 3-1l-1-1 1-1v-4h-1z" class="F"></path><path d="M663 220v1 5l1 1v2h2l-3 3c-3-1-8-3-10-6h3 1l2-2c0-1 0 0 1-1 0 1 1 1 1 2h-2c0 1-1 2-1 3 1 0 2 1 2 1l2-2c1-2 1-4 1-7z" class="C"></path><path d="M666 206l1 5v3l1 8c0 3-1 5-2 7h0-2v-2l-1-1v-5-1l-1-9c1 1 2 1 4 2v-1l-1-1c-1 0-1-1-2-1l-1-1h1l1-1c1 0 1-1 2-2z" class="E"></path><path d="M634 166c1 1 3 2 5 4s5 4 7 7l-3 3v3 1c0 1-2 3-3 4v4 4l2-1 1 2h0v2l-1 1h3c1 0 1 0 1-1h1l2-1 2-1c0 1 1 1 2 1l-2 1c1 1 2 1 3 2 2 1 3 2 4 3l-6 1 2 2v1l-1 1 2 2-1 1-1-2c-1-1-1-1-3-1l-1-2h0v-1-5l-1-1v4h-1v-3h-1c-1 2 0 6-1 8h-1c-1-3-3-6-1-8h0-1-2c1 2 0-1 1 1v4h-1v-1c-1-3-2-4-2-7v-1h-1c0 2 0 2 1 4 1 3 1 7 0 11 0-2-1-3-1-4l-1-6c-1-2-1-4-1-6v-2c0-1-1-1-1-2l-2 2-5-8c-3-5-7-9-10-13 2-1 4 0 6-1 2 0 2 0 3 1h2l1 1h1c2 0 5-1 6-2 1-3-1-3-2-5v-1z" class="e"></path><path d="M649 207c1 0 1-1 2-2 1 1 2 3 2 4l2 2-1 1-1-2c-1-1-1-1-3-1l-1-2z" class="D"></path><path d="M654 201c2 1 3 2 4 3l-6 1-2-2 1-1c1 0 2 0 3-1z" class="T"></path><path d="M636 191v-1c0-4 4-8 7-10v3 1c0 1-2 3-3 4v4l-1 4c-1 0-1-1-2-1v-1l2-1c-1-1-2-1-3-2h0z" class="H"></path><path d="M617 173c2-1 4 0 6-1 2 0 2 0 3 1h2l1 1h-1c0 1-1 1-2 1 1 2 4 4 4 6h-1c0-1-1-1-1-2l-4-4h-1c1 2 4 4 5 6 3 3 4 7 6 11h0l-2 2-5-8c-3-5-7-9-10-13z" class="j"></path><path d="M634 166c1 1 3 2 5 4s5 4 7 7l-3 3c-3 2-7 6-7 10v1h-1l-3-5-2-5c0-2-3-4-4-6 1 0 2 0 2-1h1 1c2 0 5-1 6-2 1-3-1-3-2-5v-1z" class="L"></path><path d="M633 186v-3h1l1 1v1l-2 1z" class="Y"></path><path d="M635 184c0-1 0-1 1-2v1c1-1 2-1 2-3l-3-3v-2l1-1c1 1 2 3 3 4h1v-1l-1-1c1 0 1 0 2-1v-1l-1 1c-1 0-1-1-1-1-1-2-1-3 0-4 2 2 5 4 7 7l-3 3c-3 2-7 6-7 10v1h-1l-3-5h1l2-1v-1z" class="T"></path><path d="M614 172c2 0 2 0 3 1 3 4 7 8 10 13l5 8 2-2c0 1 1 1 1 2v2c0 2 0 4 1 6l1 6c0 1 1 2 1 4 0 0-1 1-1 2-2 2-4 3-6 5h-2v-1l-1-1c-1 1-1 2-1 3h-3c0 1 0 1-1 1s-1-1-2-1c-1-1-2-1-2-1h-3c-2 0-2 0-3 1h-4v1l-2-1 1-1c-1-1-2-1-3-2v-1c-1-1-2-2-2-4 0-1-1-1-2-2h0v-1h-2c0-1 4-4 5-5v-2c-1-1-3-1-4-2-2 0-3 0-5 1h-1c-2 0-3 0-4-1l5-1 2-2c2-1 3-3 4-5l1-2 1-1c-1-1 0-1-1-1-1 1-1 1-2 1 0-1 0-1-1-2h1c-1-2-2-2-3-3h4c1 0 1-1 1-1 0-1 0-2-1-3v-1h1 4c2 1 2 1 3 3 1 1 1 1 2 1l-1-2 1-1h1l1 1c2 0 3 0 4 1l2-2v-1l1-1-6-6z" class="V"></path><path d="M617 210l1 2c-1 1-1 2-2 2h-2-1l4-4z" class="B"></path><path d="M632 194l2-2c0 1 1 1 1 2v2c0 2 0 4 1 6l1 6c0 1 1 2 1 4 0 0-1 1-1 2-2 2-4 3-6 5h-2v-1l1-1c3-1 4-1 6-4 1-5-2-13-4-19z" class="d"></path><path d="M620 178l3 5 3 3v1s-1 1-1 2c-2 0-3 0-4-1-2-2-2-3-3-4l-1-2 2-2v-1l1-1z" class="l"></path><path d="M619 180l1 2c0 1-1 1-2 2l-1-2 2-2z" class="o"></path><path d="M623 183l3 3h-2c-2 0-3-1-4-2 1-1 2-1 3-1z" class="j"></path><path d="M617 209c2 1 4 2 6 1 2 1 3 1 4 3 0 1 1 1 2 2h-1c-1 1-1 0-2 0h0l-1 1v3h-1-2 0c-2-2-3-5-4-7l-1-2v-1z" class="J"></path><path d="M626 187c2 2 4 5 4 9v1c-1 0-2 1-4 1 0 1 0 0-1 1v-1l-1 1c0-1-1-3-1-4l-3-6 1-1c1 1 2 1 4 1 0-1 1-2 1-2z" class="Y"></path><path d="M625 193l1-2c1 0 1 0 2 2 0 0 0 1-1 1l1 1c1 0 1 0 2 1v1c-1 0-2 1-4 1 0 1 0 0-1 1v-1l-1 1c0-1-1-3-1-4h1c-1-1-1-1-1-2h2z" class="l"></path><path d="M623 195h1c-1-1-1-1-1-2h2c1 1 1 2 0 4v1l-1 1c0-1-1-3-1-4z" class="P"></path><path d="M630 197l1 2-1 1h1v3l1 1c0-2 0-4-1-6l1-1c1 2 2 8 2 11-1-1-1-1-1-2l-1-1-1 1-1-1v1c2 1 2 1 2 3v1h2c0 1-1 2-2 3h-1l-1 1-2-2-1-6c-1-2-1-5-3-7l1-1v1c1-1 1 0 1-1 2 0 3-1 4-1z" class="L"></path><path d="M611 183l-1-2 1-1h1l1 1c2 0 3 0 4 1l1 2c1 1 1 2 3 4l-1 1 3 6c0 1 1 3 1 4 2 2 2 5 3 7l1 6-1 1c-1-2-2-2-4-3-2 1-4 0-6-1h1c1-1 2-4 3-5s2-2 2-3v-3l-1-1-1-1c-1-1-1-3-2-5-2-3-5-7-8-8z" class="F"></path><path d="M613 181c2 0 3 0 4 1l1 2c1 1 1 2 3 4l-1 1c-2-3-5-6-7-8z" class="j"></path><path d="M623 210l-1-1h2c0-1-1-1-3-2 1-1 2-2 2-4h1c0 2 0 2 1 4l2-1 1 6-1 1c-1-2-2-2-4-3z" class="Z"></path><path d="M597 184h4c1 0 1-1 1-1 0-1 0-2-1-3v-1h1 4c2 1 2 1 3 3 1 1 1 1 2 1 3 1 6 5 8 8 1 2 1 4 2 5l1 1 1 1v3c0 1-1 2-2 3s-2 4-3 5c-1-1-1-2-1-3l-1 1c-1-1-1-1-1-2-1-1-2-2-3-4v-1h0c1-1 1-2 1-4l-1-1c-1-3-2-4-4-6l-4-1-1 1c-1-1 0-1-1-1-1 1-1 1-2 1 0-1 0-1-1-2h1c-1-2-2-2-3-3z" class="W"></path><path d="M605 184c3 1 10 6 11 9l1 2c-1-1-2-1-3-2-2-1-3-4-5-4l-3-2-1-3z" class="I"></path><path d="M609 189c2 0 3 3 5 4 1 1 2 1 3 2 1 2 3 3 3 6h-1l-1-1s-1 0-2-1l-3-4v1l-1-1c-1-3-2-4-4-6h1z" class="C"></path><path d="M613 196v-1l3 4c1 1 2 1 2 1l1 1-1 2h0l-1 3-1 1c-1-1-1-1-1-2-1-1-2-2-3-4v-1h0c1-1 1-2 1-4z" class="N"></path><path d="M612 200c2 1 2 1 3 3 1-1 1-1 2-1l1 1-1 3-1 1c-1-1-1-1-1-2-1-1-2-2-3-4v-1h0z" class="H"></path><path d="M597 184h4c1 0 1-1 1-1 0-1 0-2-1-3v-1h1 4c2 1 2 1 3 3-1-1-2-1-3-1h-2c-1 1-1 1-1 2l2 1 1 3 3 2h-1l-4-1-1 1c-1-1 0-1-1-1-1 1-1 1-2 1 0-1 0-1-1-2h1c-1-2-2-2-3-3z" class="G"></path><path d="M603 183l2 1 1 3c-1-1-3-1-4-2 1-1 1-1 1-2z" class="F"></path><path d="M603 189l1-1 4 1c2 2 3 3 4 6l1 1c0 2 0 3-1 4h0v1c1 2 2 3 3 4 0 1 0 1 1 2l1-1c0 1 0 2 1 3h-1v1l-4 4c-2 1-3 2-5 4v1c-1-1-2-1-3-2v-1c-1-1-2-2-2-4 0-1-1-1-2-2h0v-1h-2c0-1 4-4 5-5v-2c-1-1-3-1-4-2-2 0-3 0-5 1h-1c-2 0-3 0-4-1l5-1 2-2c2-1 3-3 4-5l1-2 1-1z" class="c"></path><path d="M597 197h1c2 0 3-1 4-2h1c0 2 1 3 3 4 0 1 1 1 2 1v3h-1l-1-1c-1 0-1-1-2-2l-1-1c-1-1-2-1-3-1-2 1-3 1-4 1h-1l2-2z" class="B"></path><path d="M603 189l1-1c0 2 1 4 1 6 2 1 3 1 4 3l2 2v1h1v1l-2 2-1 2h-1v-1c-2 1-3 3-5 4 0 1-1 1-2 2v-1l6-6h1 0c1-1 2-3 2-4-2-1-3-2-5-3h-1v-2c0-1-1-1-1-2v-1h-1v-1l1-1z" class="I"></path><path d="M595 199h1c1 0 2 0 4-1 1 0 2 0 3 1l1 1c1 1 1 2 2 2l1 1-6 6h-2c0-1 4-4 5-5v-2c-1-1-3-1-4-2-2 0-3 0-5 1h-1c-2 0-3 0-4-1l5-1z" class="O"></path><path d="M604 188l4 1c2 2 3 3 4 6l1 1c0 2 0 3-1 4h0-1v-1l-2-2c-1-2-2-2-4-3 0-2-1-4-1-6z" class="Z"></path><path d="M612 201c1 2 2 3 3 4 0 1 0 1 1 2l1-1c0 1 0 2 1 3h-1v1l-4 4c-2 1-3 2-5 4v1c-1-1-2-1-3-2v-1c-1-1-2-2-2-4 0-1-1-1-2-2h0c1-1 2-1 2-2 2-1 3-3 5-4v1h1l1-2 2-2z" class="b"></path><path d="M612 201c1 2 2 3 3 4 0 1 0 1 1 2l-2 1v-1h-1c-1-1-2-2-3-4l2-2z" class="O"></path><path d="M606 216l-1-2c1-1 1-3 1-4 0 0-1-1-1-2h2c1 2 1 3 3 4v1l-4 3z" class="U"></path><path d="M616 207l1-1c0 1 0 2 1 3h-1v1l-4 4c-2 1-3 2-5 4v1c-1-1-2-1-3-2v-1h1l4-3c2-1 3-3 4-5l2-1z" class="I"></path><path d="M599 209h2v1h0c1 1 2 1 2 2 0 2 1 3 2 4v1c1 1 2 1 3 2l-1 1 2 1v-1h4c1-1 1-1 3-1h3s1 0 2 1c1 0 1 1 2 1s1 0 1-1h3c0-1 0-2 1-3l1 1v1h2c2-2 4-3 6-5v6c0 1 0 1-1 2v2 3l-1 2h1c1-1 1-2 1-4l1 1 1 2h2l1-2h2l-2 2v1h6v-1l1-1c1-1 3-1 4-1 2 3 7 5 10 6 1 1 1 1 2 0 1 0 2 1 3 1 0 0 1-2 2-2h1l-4 4-1 1-2 2-3 4c-1 2-3 4-3 6-1-1-2-1-3-2v-3c-1-2-2-3-4-4h-1c-5-2-11-2-17-2 0 3-1 6-3 8l-1-1c-1 1-1 1-3 0l-2-2v1 1c1 2 1 3 0 6 0 1-1 2-2 3 6-1 12-1 19-1 2 0 4 0 6 1 3 3 3 6 4 10 1 3-1 7-1 10-2 10-4 19-7 28l-1 1-1-2c-2-2-3-3-4-5-5-3-8-4-14-4v1c-4 1-8 1-10 5v2c-1 0-5 2-6 3s-2 1-3 1v-2c-1-5-5-6-9-9-3-3-7-6-11-8-2-2-5-3-6-4h0c1 0 2 1 4 1l-2-2c-1 0-1-1-2-1l1-1c1-3 2-4 3-6h-1l1-1c0-1 1-3 2-4l1-2c1-2 2-5 3-7v-2h1v-4c0-7-1-13-3-19v-5-6-2h2l1-1c1-1 1-2 2-2s1 0 2-1l6-6z" class="W"></path><path d="M605 248h1v1l2 2h-1-2v1h-1c0-2 0-1-1-2v-1h0l2-1z" class="h"></path><path d="M609 241h2l-3 3c1 1 1 2 3 2h2l5 1h-4c-1 0-1 1-1 1-1 1-5 1-7 1v-1h-1-1c-1-1-4-2-4-4l1-1 1 2h1c3 0 4-2 6-4z" class="a"></path><path d="M605 248h-1c-1-1-4-2-4-4l1-1 1 2c2 1 6 1 9 2h1l-1 1h-5-1z" class="B"></path><path d="M628 288c4 0 8 1 12-2 3-1 4-4 5-7v-1c1 3-1 5-2 8l-1 1c-1 1-1 2-3 3-2 0-2 1-3 1 3 3 4 4 6 8 0 1 0 1 1 2l-1 1-1-2c-2-2-3-3-4-5-5-3-8-4-14-4-1 0-2 0-3-1 3-2 6 0 8-2z" class="c"></path><path d="M642 287c-1 1-1 2-3 3-2 0-2 1-3 1-2-1-4-1-6-1h-1v-1c5 0 9-1 13-2z" class="E"></path><defs><linearGradient id="AK" x1="624.108" y1="256.354" x2="606.338" y2="263.881" xlink:href="#B"><stop offset="0" stop-color="#111216"></stop><stop offset="1" stop-color="#393c42"></stop></linearGradient></defs><path fill="url(#AK)" d="M603 250c1 1 1 0 1 2h1l4 2c3 0 5 1 8 4h-1c-1-2-2-2-4-3h-1c2 2 3 3 4 5s3 4 5 4 5-1 7-2h1c-1 2-1 2-2 3l1 1 1 1-1 1h-1c-1 0 0 0-1 1h2l-1 1c-4 0-7 0-11-2-2-1-5-1-7-3l-3-2-2-2v-2l1-1h-1c-1-1-1-1 0-2-3-1 0-3 0-5v-1z"></path><path d="M607 261c1 2 1 2 1 4l-3-2 2-2z" class="c"></path><path d="M603 261c1-1 1-1 3 0h0 1l-2 2-2-2z" class="N"></path><path d="M603 250c1 1 1 0 1 2h1l4 2-4-1-2 3c-3-1 0-3 0-5v-1z" class="F"></path><defs><linearGradient id="AL" x1="610.22" y1="233.478" x2="593.172" y2="244.412" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#3b4044"></stop></linearGradient></defs><path fill="url(#AL)" d="M604 221c1 3 1 7 2 10l1 2-2 1c0 1-1 2-1 2 0 2 0 2-1 4 0 0-1 1-1 2s1 2 1 3h-1l-1-2-1 1c0 2 3 3 4 4h1l-2 1h0v1 1c0 2-3 4 0 5-1 1-1 1 0 2h-1-1c-2-1-4-5-5-8v-2l-1-3c-1-3-1-6 0-9 0-3 2-5 1-8 0 0 0-1 1-1h2v1c3-1 3-4 5-6v-1z"></path><path d="M601 243c0-2 0-5 1-7s1-5 1-8c1 1 1 5 1 7 0-1 0 0 1-1-1-1-1-2-1-3h1v3c0 1-1 2-1 2 0 2 0 2-1 4 0 0-1 1-1 2s1 2 1 3h-1l-1-2z" class="E"></path><path d="M596 228s0-1 1-1h2c0 2 0 3-1 5-1 4 1 9-1 13v2l-1 1-1-3c-1-3-1-6 0-9 0-3 2-5 1-8z" class="B"></path><path d="M609 229l1 3h38v2h1 1v1c2 1 3 2 4 3v2l-1-1-1-1-1 1h-1c-5-2-11-2-17-2 0 3-1 6-3 8l-1-1c2-2 2-4 2-7-2-1-6-1-8-1-1 2-1 3-2 4-1-1 0-2-2-2h0c-2 1-7 2-8 3h-2c-2 2-3 4-6 4 0-1-1-2-1-3s1-2 1-2c1-2 1-2 1-4 0 0 1-1 1-2l2-1-1-2 3-2z" class="p"></path><path d="M610 238v-1l-1-1c2-1 4 0 5 0h1-3v1c-1 1-1 1-2 1z" class="n"></path><path d="M607 233v6l-3-3s1-1 1-2l2-1z" class="F"></path><path d="M604 236l3 3c-1 2-1 2-3 3h-2c0-1 1-2 1-2 1-2 1-2 1-4zm8 1l11-1c-1 2-1 3-2 4-1-1 0-2-2-2h0c-2 1-7 2-8 3h-2l1-3c1 0 1 0 2-1z" class="I"></path><path d="M599 209h2v1h0c1 1 2 1 2 2 0 2 1 3 2 4v1c1 3 4 9 4 12l-3 2c-1-3-1-7-2-10v1c-2 2-2 5-5 6v-1h-2c-1 0-1 1-1 1l-1 1v1c-2 0-2 1-4 0h-2-1l3 17c0 2 0 4-1 6s-1 5-2 7v1c0 1-1 2-1 3s-1 2-2 4h0l-3 3h-1l1-1c0-1 1-3 2-4l1-2c1-2 2-5 3-7v-2h1v-4c0-7-1-13-3-19v-5-6-2h2l1-1c1-1 1-2 2-2s1 0 2-1l6-6z" class="N"></path><path d="M588 230v-9h1c0-2 1-2 2-3l2 3-3 5-1 4h-1z" class="F"></path><path d="M601 211c0 2 0 2 1 3v3h-1l-1-1c-1 1-2 1-2 2v1l1 1c1-1 1 0 3 0l-6 5h-2l-1 1h-3l3-5c2-4 5-7 8-10z" class="P"></path><path d="M601 210c1 1 2 1 2 2 0 2 1 3 2 4v1c1 3 4 9 4 12l-3 2c-1-3-1-7-2-10 0-1 0-2-1-2l-1 1c-2 0-2-1-3 0l-1-1v-1c0-1 1-1 2-2l1 1h1v-3c-1-1-1-1-1-3v-1z" class="l"></path><path d="M602 220l1-1c1 0 1 1 1 2v1c-2 2-2 5-5 6v-1h-2c-1 0-1 1-1 1l-1 1v1c-2 0-2 1-4 0h-2l1-4h3l1-1h2l6-5z" class="E"></path><path d="M590 226h3l1-1h2l-5 5h-2l1-4z" class="l"></path><path d="M637 214v6c0 1 0 1-1 2v2 3l-1 2h1c1-1 1-2 1-4l1 1 1 2h2l1-2h2l-2 2v1h6v-1l1-1c1-1 3-1 4-1 2 3 7 5 10 6 1 1 1 1 2 0 1 0 2 1 3 1 0 0 1-2 2-2h1l-4 4-1 1-2 2-3 4c-1 2-3 4-3 6-1-1-2-1-3-2v-3c-1-2-2-3-4-4l1-1 1 1 1 1v-2c-1-1-2-2-4-3v-1h-1-1v-2h-38l-1-3c0-3-3-9-4-12 1 1 2 1 3 2l-1 1 2 1v-1h4c1-1 1-1 3-1h3s1 0 2 1c1 0 1 1 2 1s1 0 1-1h3c0-1 0-2 1-3l1 1v1h2c2-2 4-3 6-5z" class="N"></path><path d="M613 220c1-1 1-1 3-1h3c-1 1-2 1-3 2h0l-1-1-1 1-1 1v1c-1 1-1 2-3 3v-1c0-3 1-3 3-5z" class="R"></path><path d="M622 223h1c2 2 2 5 5 6-4 0-7 0-10-1 2-1 3-3 4-5z" class="F"></path><path d="M627 220c0-1 0-2 1-3l1 1v1h2c1 1 1 2 1 3l-1 1c-1 3 1 5-2 7l-1-1c1 0 1 0 1-1 0-2 0-3 1-5v-1h-4l1-2z" class="c"></path><path d="M626 222h4v1c-1 2-1 3-1 5 0 1 0 1-1 1h0c-3-1-3-4-5-6l3-1z" class="G"></path><path d="M619 219s1 0 2 1c1 0 1 1 2 1s1 0 1-1h3l-1 2-3 1h-1c-1 2-2 4-4 5-1 0-2-1-3-2-2-1-1-3-1-5l1-1 1 1h0c1-1 2-1 3-2z" class="I"></path><path d="M619 219s1 0 2 1c1 0 1 1 2 1s1 0 1-1h3l-1 2-3 1h-1c-1 0-1-1-2-1v-1c-1 1-1 2-2 3-1 0-1 0-2-1v-2h0c1-1 2-1 3-2z" class="B"></path><path d="M649 227c1-1 3-1 4-1 2 3 7 5 10 6 1 1 1 1 2 0 1 0 2 1 3 1 0 0 1-2 2-2h1l-4 4-1 1-2 2-3 4c0-2 0-3 1-4v-2l-1-1c-3-2-6-4-10-6l-3-1 1-1z" class="T"></path><path d="M649 227h1l2 1-1 1-3-1 1-1z" class="X"></path><path d="M648 232c5 1 9 3 14 4v2c-1 1-1 2-1 4-1 2-3 4-3 6-1-1-2-1-3-2v-3c-1-2-2-3-4-4l1-1 1 1 1 1v-2c-1-1-2-2-4-3v-1h-1-1v-2z" class="d"></path><path d="M655 237h2l1 2-1 1-1 1h0l-1-1v-2-1z" class="L"></path><path d="M591 247c0 1 0 2 1 4 0 1 0 2 1 4v1h1c2 1 3 2 4 4l3 6c3 5 7 7 11 11 3 2 7 3 10 5h1 0c1-1 1 0 2-1 1 0 2-1 4 0v1c0 1 0 2-1 3s0 2 0 3c-2 2-5 0-8 2 1 1 2 1 3 1v1c-4 1-8 1-10 5v2c-1 0-5 2-6 3s-2 1-3 1v-2c-1-5-5-6-9-9-3-3-7-6-11-8-2-2-5-3-6-4h0c1 0 2 1 4 1l-2-2c-1 0-1-1-2-1l1-1c1-3 2-4 3-6l3-3h0c1-2 2-3 2-4s1-2 1-3v-1c1-2 1-5 2-7s1-4 1-6z" class="E"></path><path d="M596 271c1 0 0 0 1 1l-1 2c-1 1-1 1-1 2v1h-1l-2-1v-1l4-4z" class="K"></path><path d="M597 274h1c2 1 2 3 3 4v1l4-1c1 1 2 2 3 4v1h-2l-1 1c-1 1-2 2-3 2s-1-1-1-2c-2-2-2-3-4-5h-1c0-3 0-3 1-5z" class="F"></path><path d="M597 274h1c2 1 2 3 3 4h-1c-1 0-1 1-2 1 0-1-1-1-1-2l1-1-1-2z" class="C"></path><path d="M601 279l4-1c1 1 2 2 3 4h-2l-2 2-1-1c-1-1-2-3-2-4z" class="G"></path><path d="M603 283v-2c2 0 2 0 3 1l-2 2-1-1zm9-6c-1 1-1 1-1 2-1 0-1 0-2-1h0c-2-1-2-2-3-3-2 0-3-1-3-2l-1-1-2-2c-1-1-2-2-2-3l-5-8c0-2-1-2-1-3l1-1v1h1c2 1 3 2 4 4l3 6c3 5 7 7 11 11z" class="I"></path><path d="M580 279h0v-1c0-2 1-4 2-5 0 1 0 2 1 3v-2l1-1c4 2 7 5 10 8h1c2 2 4 4 6 5h1c1 0 2-1 3-2l1-1h2 1c1 0 2 0 3 1 3 1 6 1 9 2h1 0c-1-1-2-1-3-2-1 0-2-1-3-2h1 0l4 2h2v-2h0c1-1 1 0 2-1 1 0 2-1 4 0v1c0 1 0 2-1 3s0 2 0 3c-2 2-5 0-8 2 1 1 2 1 3 1v1c-4 1-8 1-10 5v2c-1 0-5 2-6 3s-2 1-3 1v-2c-1-5-5-6-9-9-3-3-7-6-11-8-2-2-5-3-6-4h0c1 0 2 1 4 1l-2-2z" class="C"></path><path d="M607 287h1c0-1 1-2 1-2 1 0 1 0 2 1l-2 1v1h4c-1 1-2 1-3 1-2 1-3 3-4 4l-1-1 2-2v-1-2z" class="Q"></path><path d="M583 274l1-1c4 2 7 5 10 8s5 6 9 7v1h2c1-1 1-2 2-2v2l-1 1c-2 0-5 1-6 1-2-2-4-4-6-5-3-3-6-5-9-7-1-2-1-4-2-5z" class="K"></path><path d="M650 364h3c2-1 4-1 7-1 2 1 4 1 6 2l1 1-1 2c-1 2-2 4-2 6l-1 1c0 1 0 2-1 3l1 3-4 9c0 1-1 2-1 3l-6 14-20 53-1 1-6 13c-1 3-3 6-3 9l-6 12v2l-16 40c-2 3-3 7-5 11 0-1 0-1-1-2-2-2-3-7-3-10h0v-8l-1-1v-1c1-1 0-5 0-7-1-7-3-13-5-20l-2 1c0-1 0-3-1-4h-1c-1 0-1 0-2-1-2 0-3-1-4-2h-1c-2-2-3-4-4-7 0-2 1-3 3-5 0-1 2-2 3-3 2-1 4-2 6-2l4-1c1 1 2 1 3 2l1-1c1-8 6-14 12-20l1 1 3-3c1-3 6-8 8-9l3-3v-1c-1-2-2-5-2-7v-2h-1c2-2 2-3 2-5 2-4 4-8 4-12l1-2v-3h-3v-2c1-1 1-2 1-3 0-2 1-3 1-5h-1c-2 1-5 2-7 3-1 1 0 1-1 1 0-1 0-2 1-2l3-7 4-11c1-2 1-3 2-4l2-3c1 1 2 1 3 2h0c1 1 1 2 2 2v1l1 2 1 3c0-1 1-2 1-3h1l1-2 1 1 5-10 2 1c2-2 4-4 5-7l2-1 2-2z" class="P"></path><path d="M631 412c1-1 2-2 3-2v-1l1 1c0 4 0 7-1 11v1c-1 2-1 3-2 5h-1c0-2 1-3 1-5v-1l-1 1c0 1 0 2-1 4h-1l1-2c0-4 0-8 1-12z" class="d"></path><path d="M629 426h1c1-2 1-3 1-4l1-1v1c0 2-1 3-1 5h1c-1 2-2 5-4 8l-4 4h-1c-2 1-5 4-6 5h-1l-2 1 3-3c6-5 9-9 12-16z" class="Y"></path><path d="M637 386h2v-1c1-1 1-1 2-1l1 2h-1-1v17h-1l-1 1v2-4-9c-1 4-3 7-4 11l-1 1c-1-1 0-4 0-5 1-5 2-10 4-14z" class="T"></path><path d="M634 427c1 4-2 9-4 11l-2 2-2 3c0 1 0 1 1 1 1 2 2 3 2 4 0 2 0 4-1 5l-1 1c-2 0-3 1-5 2-1 1-2 0-3 0-1-2-1-3-1-6v-3-1c7-5 13-10 16-19z" class="N"></path><path d="M640 403v-17h1 1l1 1c2 5 9 6 9 13l-1 1-1-2-1 1v4c-1 7-4 12-7 19l-4 8c0-2 0-2 1-3l-1-1c-1 0-1 1-1 2-1 1-1 2-2 3-1 2-3 6-5 6h0c2-2 5-7 4-11 0 0 1-1 1-2l2-7c0-3 0-6 1-8v1c1-2 1-5 1-8h1z" class="b"></path><path d="M646 396c1 1 2 2 2 3l1 1v4c-1-1-1-3-3-4 0-1-1-1-1-2l1-2z" class="B"></path><path d="M641 400c1 2 1 5 0 7-1 5-1 11-3 15l-3 10c-1 2-3 6-5 6h0c2-2 5-7 4-11 0 0 1-1 1-2l2-7c0-3 0-6 1-8v1c1-2 1-5 1-8h1 0c1-1 1-2 1-3z" class="M"></path><path d="M640 403v-17h1 1l1 1c2 5 9 6 9 13l-1 1-1-2-1 1-1-1c0-1-1-2-2-3s-2-1-3-2l-1-1h-1v7c0 1 0 2-1 3h0z" class="G"></path><path d="M642 386l1 1c2 5 9 6 9 13l-1 1-1-2c-2-4-9-9-9-13h1z" class="P"></path><path d="M649 400l1-1 1 2v6l1-1v1l-20 53-1 1-6 13c-1 3-3 6-3 9l-6 12h-1c0-2 0-3-1-5 0-1 5-10 5-10 0-1-1-1-1-1v-3c3-3 4-7 6-12h0v-2l-2 1c-2 1-2 2-4 3l-2-1h1v-1c2 0 3-2 4-3 2-1 4-2 5-3 0 0 1-3 1-4l1-1c1-1 1-3 1-5 0-1-1-2-2-4-1 0-1 0-1-1l2-3 2-2h0c2 0 4-4 5-6 1-1 1-2 2-3 0-1 0-2 1-2l1 1c-1 1-1 1-1 3l4-8c3-7 6-12 7-19v-4z" class="F"></path><path d="M628 440s1 1 2 1c1 3 3 4 2 7l-1 5c-1-2-1-3-2-5 0-1-1-2-2-4-1 0-1 0-1-1l2-3z" class="T"></path><path d="M635 432c1-1 1-2 2-3 0-1 0-2 1-2l1 1c-1 1-1 1-1 3l-5 12c-1-1-2-1-2-2h-1c-1 0-2-1-2-1l2-2h0c2 0 4-4 5-6z" class="V"></path><path d="M629 448c1 2 1 3 2 5 0 3-3 5-4 8l-3 3h0 0v-2l-2 1c-2 1-2 2-4 3l-2-1h1v-1c2 0 3-2 4-3 2-1 4-2 5-3 0 0 1-3 1-4l1-1c1-1 1-3 1-5z" class="b"></path><path d="M650 364h3c2-1 4-1 7-1 2 1 4 1 6 2l1 1-1 2c-1 2-2 4-2 6l-1 1c0 1 0 2-1 3l1 3-4 9c0 1-1 2-1 3l-6 14v-1l-1 1v-6l1-1c0-7-7-8-9-13l-1-1-1-2c-1 0-1 0-2 1v1h-2c-2 4-3 9-4 14-1-1-1-1-1-2 0-2 0-4-1-6 0-3 2-6 3-9l5-10 2 1c2-2 4-4 5-7l2-1 2-2z" class="k"></path><path d="M650 370v-1l1-1c1 1 2 1 2 1 1 0 2 1 3 1l5-1 2 1c-1 1-1 0-2 1 0 2 0 4-1 6 0-2 0-3-2-5v-1c-1 3 1 6-1 10 0 2-2 4-4 7 0 1-1 2-3 2l-1 1c-2-1-3-3-4-5h1 4 1c1 0 2 0 2-1 2-1 4-4 4-7 0-2-1-3-2-5l-2-1c-1-1-2-2-3-2z" class="S"></path><path d="M650 364h3c2-1 4-1 7-1 2 1 4 1 6 2l1 1-1 2c-1 2-2 4-2 6-1-2-1-3-1-5v-1l-2 1-5 1c-1 0-2-1-3-1 0 0-1 0-2-1l-1 1v1c-1 0-1 1-2 1l-1-1h0v-1l2-2-1-1 2-2z" class="L"></path><path d="M651 368c5-2 10-2 15 0-1 2-2 4-2 6-1-2-1-3-1-5v-1l-2 1-5 1c-1 0-2-1-3-1 0 0-1 0-2-1z" class="E"></path><path d="M661 369l2-1v1c0 2 0 3 1 5l-1 1c0 1 0 2-1 3l1 3-4 9c0 1-1 2-1 3l-6 14v-1l-1 1v-6l1-1 8-23c1-2 1-4 1-6 1-1 1 0 2-1l-2-1z" class="N"></path><path d="M648 366l1 1-2 2v1h0l1 1c1 0 1-1 2-1s2 1 3 2l2 1c1 2 2 3 2 5 0 3-2 6-4 7 0 1-1 1-2 1h-1-4-1l-2 1-1-1-1-2c-1 0-1 0-2 1v1h-2c-2 4-3 9-4 14-1-1-1-1-1-2 0-2 0-4-1-6 0-3 2-6 3-9l5-10 2 1c2-2 4-4 5-7l2-1z" class="d"></path><path d="M650 370c1 0 2 1 3 2l2 1c0 2 1 2-1 4h0l-2-3c-3-1-5-1-7-1l2-3 1 1c1 0 1-1 2-1z" class="P"></path><path d="M650 370c1 0 2 1 3 2h-5v-1c1 0 1-1 2-1z" class="j"></path><path d="M650 375c2 1 3 2 4 4 0 1 0 3-1 4 0 1-1 2-2 3h-1-4c1-1 2-1 3-2 0-2 0-2-1-3l1-1s1 1 2 1l1-2-3-3c1 0 1 0 1-1z" class="K"></path><path d="M652 379v3l-2 4h-4c1-1 2-1 3-2 0-2 0-2-1-3l1-1s1 1 2 1l1-2z" class="D"></path><path d="M643 376l2-1c2-1 4-1 5 0 0 1 0 1-1 1l3 3-1 2c-1 0-2-1-2-1l-1 1-2 1v-3h-3c0-1 1-1 1-1 0-1 0-1-1-2h0z" class="F"></path><path d="M649 376l3 3-1 2c-1 0-2-1-2-1-1-1-1 0-1-1l-2-2h2l1-1z" class="I"></path><path d="M637 386c2-4 3-7 6-10 1 1 1 1 1 2 0 0-1 0-1 1h3v3l2-1c1 1 1 1 1 3-1 1-2 1-3 2h-1l-2 1-1-1-1-2c-1 0-1 0-2 1v1h-2z" class="G"></path><path d="M643 379h3v3c-1 0-2 0-4 1h0-1c0-2 1-3 2-4z" class="C"></path><path d="M648 366l1 1-2 2v1h0l-2 3-2 1h0v2h0c-3 3-4 6-6 10s-3 9-4 14c-1-1-1-1-1-2 0-2 0-4-1-6 0-3 2-6 3-9l5-10 2 1c2-2 4-4 5-7l2-1z" class="j"></path><path d="M623 377c1 1 2 1 3 2h0c1 1 1 2 2 2v1l1 2 1 3c0-1 1-2 1-3h1l1-2 1 1c-1 3-3 6-3 9 1 2 1 4 1 6 0 1 0 1 1 2 0 1-1 4 0 5l1-1h1v6l-1-1v1c-1 0-2 1-3 2-1 4-1 8-1 12l-1 2c-3 7-6 11-12 16v-1c-1-2-2-5-2-7v-2h-1c2-2 2-3 2-5 2-4 4-8 4-12l1-2v-3h-3v-2c1-1 1-2 1-3 0-2 1-3 1-5h-1c-2 1-5 2-7 3-1 1 0 1-1 1 0-1 0-2 1-2l3-7 4-11c1-2 1-3 2-4l2-3z" class="J"></path><path d="M619 405l2 1v4h-3v-2c1-1 1-2 1-3z" class="G"></path><path d="M619 400l1-1h1v7l-2-1c0-2 1-3 1-5h-1z" class="O"></path><path d="M626 402v11c1 3 1 6 0 9v-8c-1-1-1-1-1-2v-1c-1-2-1-5-1-7l2-2z" class="K"></path><path d="M612 402l3-7 1 1 2-1 2 2c-1 2-3 3-5 3l-3 2z" class="S"></path><path d="M626 402c1-1 2-2 3-2 0 2 1 3 1 4l1 8c-1 4-1 8-1 12l-1 2c-3 7-6 11-12 16v-1l3-3c0-3 2-5 4-8 1-3 1-6 2-8 1-3 1-6 0-9v-11h0z" class="F"></path><path d="M623 377c1 1 2 1 3 2h0c1 1 1 2 2 2v1l1 2 1 3c0-1 1-2 1-3h1l1-2 1 1c-1 3-3 6-3 9 1 2 1 4 1 6 0 1 0 1 1 2 0 1-1 4 0 5l1-1h1v6l-1-1v1c-1 0-2 1-3 2l-1-8c0-1-1-2-1-4-1 0-2 1-3 2l-1-1-1-2-1-1v-3c0-3 0-5-1-8v-1l-1-2h-2c1-2 1-3 2-4l2-3z" class="O"></path><path d="M621 380h3v1c-1 1-2 1-2 2v3l-1-2h-2c1-2 1-3 2-4z" class="V"></path><path d="M623 377c1 1 2 1 3 2h0c1 1 1 2 2 2v1c-2 2 0 7-2 11v-8c-1-1-2-2-2-4v-1h-3l2-3z" class="N"></path><path d="M626 393c2-4 0-9 2-11l1 2 1 3c0 5-4 9-4 14h-1l-1-2c1-2 1-4 2-6z" class="C"></path><path d="M633 382l1 1c-1 3-3 6-3 9-1 5-1 8-1 12 0-1-1-2-1-4-1 0-2 1-3 2l-1-1h1c0-5 4-9 4-14 0-1 1-2 1-3h1l1-2z" class="V"></path><path d="M630 404c0-4 0-7 1-12 1 2 1 4 1 6 0 1 0 1 1 2 0 1-1 4 0 5l1-1h1v6l-1-1v1c-1 0-2 1-3 2l-1-8z" class="Y"></path><path d="M606 454c1-3 6-8 8-9l2-1 2 2v1 3c0 3 0 4 1 6 1 0 2 1 3 0 2-1 3-2 5-2 0 1-1 4-1 4-1 1-3 2-5 3-1 1-2 3-4 3v1h-1l2 1c2-1 2-2 4-3l2-1v2h0c-2 5-3 9-6 12v3s1 0 1 1c0 0-5 9-5 10 1 2 1 3 1 5h1v2l-16 40c-2 3-3 7-5 11 0-1 0-1-1-2-2-2-3-7-3-10h0v-8l-1-1v-1c1-1 0-5 0-7-1-7-3-13-5-20l-2 1c0-1 0-3-1-4h-1c-1 0-1 0-2-1-2 0-3-1-4-2h-1c-2-2-3-4-4-7 0-2 1-3 3-5 0-1 2-2 3-3 2-1 4-2 6-2l4-1c1 1 2 1 3 2l1-1c1-8 6-14 12-20l1 1 3-3z" class="H"></path><path d="M611 460c-1 0-3 0-4-1 2 0 3-1 3-3l1-1c1 0 1-1 2-1l1-1 1 1v3c-1 1-2 1-3 3h-1z" class="U"></path><path d="M615 454l1-1h0c0-2 1-2 2-3 0 3 0 4 1 6 1 0 2 1 3 0v1c-2 1-4 1-6 2l-4 1c1-2 2-2 3-3v-3z" class="G"></path><path d="M606 454c1-3 6-8 8-9l2-1 2 2v1l-5 3c-2 1-6 5-8 8-1 2-2 3-3 4-1-4 3-5 4-8z" class="i"></path><path d="M612 460l4-1c2-1 4-1 6-2l-3 2v2c-1 0-4 1-5 1-2 2-2 2-4 3-1 0-2 0-2 1-3 1-5 2-7 4v1l1 1c-1 1-3 2-3 4v4l-1 1c-1 0-1 2-1 3v-6h-1v-1c0-3 3-10 6-13 0 0 2-3 3-3 2-1 4 0 6 0v-1h1z" class="f"></path><path d="M601 471c0 1-1 2-2 3l1-4c3-6 13-8 19-11v2c-1 0-4 1-5 1-2 2-2 2-4 3-1 0-2 0-2 1-3 1-5 2-7 4v1z" class="N"></path><path d="M622 456c2-1 3-2 5-2 0 1-1 4-1 4-1 1-3 2-5 3-1 1-2 3-4 3v1h-1l2 1v1c-2 1-3 2-4 4-2 2-5 4-7 7-2 1-4 4-6 6h-1v-8h-1c0-2 2-3 3-4l-1-1v-1c2-2 4-3 7-4 0-1 1-1 2-1 2-1 2-1 4-3 1 0 4-1 5-1v-2l3-2v-1z" class="e"></path><path d="M622 456c2-1 3-2 5-2 0 1-1 4-1 4-1 1-3 2-5 3-1 1-2 3-4 3v1h-1c-1 1-2 1-3 1-2 0-7 2-9 4 0 1-1 1-1 2 0-2 0-2 1-3l1-1c1-1 2-1 3-2 0-1 1-1 2-1 2-1 2-1 4-3 1 0 4-1 5-1v-2l3-2v-1z" class="H"></path><path d="M606 454c-1 3-5 4-4 8-4 5-6 10-7 16v2c1 0 1-1 1-2h1v6c0 5-2 9-2 14v1c-1 1-1 1-3 2h0c0 1 1 1 1 2l-1 1c1 0 1 0 2 1h-1-1c-1 0-1 0-1 1l-2-6c0-2-1-3-2-5 2-2 3-4 3-6v-1l1-1 1 1v-2l-1-1c0-3 0-6-1-9 1-8 6-14 12-20l1 1 3-3z" class="L"></path><path d="M602 456l1 1c-2 2-4 5-6 8a24.56 24.56 0 0 0-5 15h0v6l-1-1c0-3 0-6-1-9 1-8 6-14 12-20z" class="F"></path><path d="M592 486v-6c1 4 0 9 0 14 0 2-1 5 0 7h0c0 1 1 1 1 2l-1 1c1 0 1 0 2 1h-1-1c-1 0-1 0-1 1l-2-6c0-2-1-3-2-5 2-2 3-4 3-6v-1l1-1 1 1v-2z" class="G"></path><path d="M590 489c1 1 1 4 1 6l-1 3c0 1-1 1-1 2 0-2-1-3-2-5 2-2 3-4 3-6z" class="H"></path><path d="M586 475c1 1 2 1 3 2l1-1c1 3 1 6 1 9l1 1v2l-1-1-1 1v1c0 2-1 4-3 6 0 0-2 1-2 2-1 1 0 2 0 2l-2 1c0-1 0-3-1-4h-1c-1 0-1 0-2-1-2 0-3-1-4-2h-1c-2-2-3-4-4-7 0-2 1-3 3-5 0-1 2-2 3-3 2-1 4-2 6-2l4-1z" class="B"></path><path d="M586 475c1 1 2 1 3 2l1-1c1 3 1 6 1 9l1 1v2l-1-1-1 1v-2l-1 1-1-1 1-1v-1-4h-1c0-2 0-2-1-3s-3-1-4-1l-1 1h-2l-1 1h-1c-2 1-3 2-5 3h0c0-1 2-2 3-3 2-1 4-2 6-2l4-1z" class="K"></path><path d="M578 478c0 2-1 4 0 7v1 1l1 1 2 1-2 2-2-1-1 1c-2-1-3-2-4-4s0-4 1-6c2-1 3-2 5-3z" class="H"></path><path d="M573 481h0c-1 2-2 4-1 6s2 3 4 4l1-1 2 1 2-2h3c1-1 2-1 4-1 0 0 1 0 1-1l1-1v2 1c0 2-1 4-3 6 0 0-2 1-2 2-1 1 0 2 0 2l-2 1c0-1 0-3-1-4h-1c-1 0-1 0-2-1-2 0-3-1-4-2h-1c-2-2-3-4-4-7 0-2 1-3 3-5z" class="E"></path><path d="M581 489h3c1-1 2-1 4-1-2 2-3 3-5 4h-1c-1 1-3 0-5 0 0 0 0-1-1-1l1-1 2 1 2-2z" class="D"></path><path d="M622 463l2-1v2h0c-2 5-3 9-6 12v3s1 0 1 1c0 0-5 9-5 10 1 2 1 3 1 5h1v2l-1-1h-1c-2-2-5-4-8-4l-1-1h-1v2s0 1-1 1l-1-1-3 3 1 1v2h-3-2v-1c0-5 2-9 2-14 0-1 0-3 1-3l1-1v-4h1v8h1c2-2 4-5 6-6 2-3 5-5 7-7 1-2 2-3 4-4v-1c2-1 2-2 4-3z" class="S"></path><path d="M618 466c2-1 2-2 4-3 0 1-1 3-3 3l-1 1v-1z" class="O"></path><path d="M624 464h0c-2 5-3 9-6 12h-1l-2-1 9-11z" class="X"></path><path d="M607 478c2-3 5-5 7-7 0 1 0 3-1 5s-4 3-6 4c-1 1-1 2-2 3-2 2-4 3-6 5h-1c1-2 2-2 3-3 2-2 5-5 6-7z" class="J"></path><path d="M595 498c2-1 2-5 3-7 4-4 9-8 14-12 1-2 2-2 3-4l2 1v1c-3 3-6 6-9 8-2 2-4 3-5 5h3l-1 1h-1v2s0 1-1 1l-1-1-3 3 1 1v2h-3-2v-1z" class="T"></path><path d="M600 493c2-2 2-2 4-2v2s0 1-1 1l-1-1h-2z" class="Q"></path><path d="M600 493h2l-3 3 1 1v2h-3c0-2 1-4 3-6z" class="D"></path><path d="M618 476v3s1 0 1 1c0 0-5 9-5 10 1 2 1 3 1 5h1v2l-1-1h-1c-2-2-5-4-8-4l-1-1 1-1h-3c1-2 3-3 5-5 3-2 6-5 9-8v-1h1z" class="B"></path><path d="M606 490h1c2 1 3 2 5 2h0c-1-1-2-1-3-2-1 0-2-1-3-1 1-1 0-1 2-1h3c1 1 2 1 3 2 1 2 1 3 1 5h1v2l-1-1h-1c-2-2-5-4-8-4l-1-1 1-1z" class="M"></path><path d="M604 491h1l1 1c3 0 6 2 8 4h1l1 1-16 40c-2 3-3 7-5 11 0-1 0-1-1-2-2-2-3-7-3-10h0v-8l-1-1v-1c1-1 0-5 0-7-1-7-3-13-5-20 0 0-1-1 0-2 0-1 2-2 2-2 1 2 2 3 2 5l2 6c0-1 0-1 1-1h1 1c-1-1-1-1-2-1l1-1c0-1-1-1-1-2h0c2-1 2-1 3-2h2 3v-2l-1-1 3-3 1 1c1 0 1-1 1-1v-2z" class="C"></path><path d="M587 495c1 2 2 3 2 5l2 6 4 20c1 3 2 6 3 10v2c-2-4-3-7-5-11 0 6 0 13 1 19-2-2-3-7-3-10h0v-8l-1-1v-1c1-1 0-5 0-7-1-7-3-13-5-20 0 0-1-1 0-2 0-1 2-2 2-2z" class="T"></path><path d="M604 491h1l1 1 1 1c1 0 2 1 4 1v1c0 1 0 2-1 3 0 2-1 5-2 7-1 4-3 7-4 10s-2 6-3 8c0 1-1 5-2 6l-3-12c-1-4-1-10-4-12h1 1c-1-1-1-1-2-1l1-1c0-1-1-1-1-2h0c2-1 2-1 3-2h2 3v-2l-1-1 3-3 1 1c1 0 1-1 1-1v-2z" class="V"></path><path d="M605 497l1-1h0c2 0 3 0 4 2 0 2-1 5-2 7 0-1-1-1-1-2 0-2 1-4 0-6h-2zm-9 20c2-3 2-8 2-11v-2h2l-1 12c-1 2-1 3-1 4 0 2 1 4 1 6v-1l1-2h1c0 1-1 5-2 6l-3-12z" class="S"></path><path d="M604 491h1l1 1 1 1c1 0 2 1 4 1v1c0 1 0 2-1 3-1-2-2-2-4-2h0l-1 1-5 7h-2l6-9-1-1c1 0 1-1 1-1v-2z" class="D"></path><path d="M602 493l1 1 1 1-6 9v2c0 3 0 8-2 11-1-4-1-10-4-12h1 1c-1-1-1-1-2-1l1-1c0-1-1-1-1-2h0c2-1 2-1 3-2h2 3v-2l-1-1 3-3z" class="O"></path><path d="M595 499h2 3c-1 1-1 3-2 4l-1-1h-1l-1 2-1 1c-1-1-1-1-2-1l1-1c0-1-1-1-1-2h0c2-1 2-1 3-2z" class="F"></path><defs><linearGradient id="AM" x1="255.754" y1="137.742" x2="239.477" y2="180.157" xlink:href="#B"><stop offset="0" stop-color="#030404"></stop><stop offset="1" stop-color="#363638"></stop></linearGradient></defs><path fill="url(#AM)" d="M171 149h38c5 0 12 1 17 0 6 0 13 1 19 0l42 1c1 0 2 0 3 1h0c0 1 1 1 1 2l4 5 4 3 1 1c0-1 1-2 3-3h0c1 1 1 2 2 2 2 0 7 0 9 1h-2v1c6-1 13-2 19 0h1c2 0 4 1 5 2 0 1-1 2-2 3-2 3-5 5-7 7l-1 1-3 3-1 2c-2 2-3 4-4 7-1 1-1 2-2 3 1 1 3 2 5 4l1 1h0l-2 2 1 1 3 3v1l3 2c1 1 3 2 5 4h-4l-1-1-3 1 2 2-1 1 1 2h-2-2c-2-1-4-1-5-1l-5-1-2-1h0l-6-6 1-1-3-3-5-6-5-6h-3c0-1-1-2-2-2s-2 1-3 1c-2-1-5-3-8-4l-2-1c-2-1-4-1-6-2h-1c-2 0-4-1-6-2l-1-2c-2 1-2-1-4 1 0 1 0 2-1 2h-1c-1 0-2-1-3-2h-4-2l-1-2h-2c-2 1-4 2-5 3l-1 1v1h-7c-1 1-1 1-1 2h-4c-1-1-3-1-5-1-2-1-4-1-7-1-1 0-2 1-3 1-4 0-20-1-23 1 0 0-1-1-2-1h-2v-1c0-1-1-2-3-3-1 0 0 0-1-1l-1-3h0c-1-2-1-4-2-6v-1l-1-1v-2c-1-1-2-2-3-4-2-3-6-5-9-8l-5-3h13z"></path><path d="M195 154c2-1 3-1 6-1l1 2-5 1-2-2z" class="W"></path><path d="M266 159l9 4v1h-1c-2 0-3-1-5-1h0-1c-1-1-2-3-2-4z" class="G"></path><path d="M201 153h-1v-1h1l18 1-1 1h-2c-3 0-6 1-9 0-1 0-3 1-5 1l-1-2z" class="R"></path><path d="M256 161l8 4 2 2-2 1c2 2 2 0 2 2h-3c-1 0-1-2-2-2-1-2-5-5-5-7z" class="U"></path><path d="M269 163h0c2 0 3 1 5 1h1v-1c3 1 5 3 8 4h-2-2v1c-1 0-2 0-3-1h-1 0l-3-3h-1c1 2 4 4 5 6-2 0-2-1-4-1l1-1-1-1c-1 0-2-1-2-2l-1-1v-1z" class="B"></path><path d="M183 153c3 1 9 0 12 1h0l2 2v1c-3 0-9 2-10 0h-1c-1-1-2-3-3-4z" class="G"></path><path d="M163 152c5 0 15-1 20 1 1 1 2 3 3 4l-1 1h0c-3-2-4-3-7-3-1 0-2 0-3 2l-3 3c-2-3-6-5-9-8z" class="S"></path><defs><linearGradient id="AN" x1="249.928" y1="152.658" x2="267.163" y2="160.552" xlink:href="#B"><stop offset="0" stop-color="#212326"></stop><stop offset="1" stop-color="#3c3d3f"></stop></linearGradient></defs><path fill="url(#AN)" d="M269 164h0c-1 0-2-1-3-1-6-1-10-7-16-9-2-1-3-1-5-1h3v-1-1l7 1c4 2 7 4 11 7 0 1 1 3 2 4h1v1z"></path><path d="M172 160l3-3c1-2 2-2 3-2 3 0 4 1 7 3h0v1c-1 2-2 3-3 4-2 1-4 2-7 1-1-1-2-2-3-4z" class="E"></path><path d="M178 155c3 0 4 1 7 3h0v1h-2v1h-2l-2 1-1-1c-1-1-1-1-1-2s1-1 1-2v-1z" class="I"></path><path d="M207 163v-1h2l1-1c1 0 1 0 2-1h1c4-2 8-5 12-5h1c3-1 6-1 8-1 1-1 1-1 2-1h1 1l1 1c1 1 1 1 3 1l1 1v1c-5-1-8-1-13 0l1 1c-1 0-2 0-3 1-2 1-4 1-6 1h-1l-1-1-6 2-7 2z" class="E"></path><path d="M220 159l10-2 1 1c-1 0-2 0-3 1-2 1-4 1-6 1h-1l-1-1zm56 11c-1-2-4-4-5-6h1l3 3h0 1c1 1 2 1 3 1v-1h2 2c3 2 9 5 12 8 1 0 1 1 2 2l1 2h-1v1l-1 2c-1 0-1 0-2-1l-1-1-1-1c-1-2-2-2-3-3s-2-1-2-1c-2 0-7-3-8-4s-2-1-3-1z" class="O"></path><path d="M287 173v-2l3 2c2 1 3 2 5 2 1 0 1 1 2 2l1 2h-1v1c-2-1-3-1-4-2-2-2-4-3-6-5z" class="F"></path><path d="M275 167h1c1 1 2 1 3 1v-1h2 2c3 2 9 5 12 8-2 0-3-1-5-2l-3-2v2c-3 0-9-4-12-6z" class="G"></path><defs><linearGradient id="AO" x1="305.682" y1="154.222" x2="330.934" y2="172.5" xlink:href="#B"><stop offset="0" stop-color="#282a2e"></stop><stop offset="1" stop-color="#464a50"></stop></linearGradient></defs><path fill="url(#AO)" d="M303 159h0c1 1 1 2 2 2 2 0 7 0 9 1h-2v1c6-1 13-2 19 0h1c2 0 4 1 5 2 0 1-1 2-2 3-12 0-26 1-35-6 0-1 1-2 3-3z"></path><path d="M238 153c2 0 4 0 5 1l2 1c3 0 8 3 11 5v1c0 2 4 5 5 7 0 1-1 2-1 4l1 1c1 2 2 3 4 4 3 2 8 3 10 6-2-1-4-1-6-2h-1c-2 0-4-1-6-2l-1-2c-2 1-2-1-4 1 0 1 0 2-1 2h-1c-1 0-2-1-3-2l2-1c1-2 1-5 0-7l-3-4 3-3-2-2h-1c-2-2-5-3-8-4v-1l-1-1c-2 0-2 0-3-1l-1-1z" class="D"></path><path d="M238 153c2 0 4 0 5 1l2 1h-1c3 2 6 3 9 5-1 1 0 1-1 1h-1c-2-2-5-3-8-4v-1l-1-1c-2 0-2 0-3-1l-1-1z" class="Q"></path><defs><linearGradient id="AP" x1="253.822" y1="165.087" x2="259.218" y2="176.138" xlink:href="#B"><stop offset="0" stop-color="#828386"></stop><stop offset="1" stop-color="#a1a1a4"></stop></linearGradient></defs><path fill="url(#AP)" d="M254 163c4 5 5 8 7 14-2 1-2-1-4 1 0 1 0 2-1 2h-1c-1 0-2-1-3-2l2-1c1-2 1-5 0-7l-3-4 3-3z"></path><path d="M254 170c2 1 3 2 3 4v2c-1 1-1 2-1 3l-1 1c-1 0-2-1-3-2l2-1c1-2 1-5 0-7z" class="T"></path><path d="M302 179c0-1-1-1-2-2h0c0-1 8 0 10 0s2-1 4-1c3-1 8 0 12 0h1l-3 3-1 2c-2 2-3 4-4 7-1 1-1 2-2 3l-15-12z" class="W"></path><defs><linearGradient id="AQ" x1="198.574" y1="153.91" x2="197.01" y2="160.724" xlink:href="#B"><stop offset="0" stop-color="#414142"></stop><stop offset="1" stop-color="#56585c"></stop></linearGradient></defs><path fill="url(#AQ)" d="M202 155c2 0 4-1 5-1 3 1 6 0 9 0l1 1c-1 1-2 1-3 1l-2 2-5 2c-2 2-4 2-6 3-5 2-11 3-17 3l-8 1-1-1v-2c3 1 5 0 7-1 1-1 2-2 3-4v-1l1-1h1c1 2 7 0 10 0v-1l5-1z"></path><path d="M212 158c-1-1-2-1-3-1v-1h0c2-1 3-1 5 0h0l-2 2z" class="K"></path><path d="M184 166v-1h-1l1-1c1-1 2-2 4-2l20-4c-2 1-3 2-5 2-1 1-2 1-3 2 2-1 5-2 7-2-2 2-4 2-6 3-5 2-11 3-17 3z" class="M"></path><path d="M264 165c3 0 5 3 8 4 2 0 2 1 4 1 1 0 2 0 3 1s6 4 8 4c0 0 1 0 2 1s2 1 3 3l1 1 1 1c1 1 1 1 2 1l1-2v-1h1v2h2c0 2 0 5 1 6v2l-1-1c0 1-1 1-2 1l-1-1-1-1-1 1 1 1c1 1 2 3 2 5v1h0l-5-6h-3c0-1-1-2-2-2s-2 1-3 1c-2-1-5-3-8-4l-2-1c-2-3-7-4-10-6-2-1-3-2-4-4l-1-1c0-2 1-3 1-4 1 0 1 2 2 2h3c0-2 0 0-2-2l2-1-2-2z" class="e"></path><path d="M272 176c1-1 3-2 4-2s2 2 3 2c0 1 0 1-1 2-1 0-2 0-2 1-2-1-3-1-4-3h0z" class="X"></path><path d="M285 177c1-1 3-1 4-1 1 1 2 1 3 3l1 1-1 2-2-1-5-4z" class="B"></path><path d="M279 176c2 1 3 2 5 3 0 1 1 1 1 2v1 1l-1 1-8-5c0-1 1-1 2-1 1-1 1-1 1-2z" class="U"></path><path d="M293 189c-1-2-3-4-4-7l3 3v-2l4 4-1 1 1 1c1 1 2 3 2 5v1h0l-5-6z" class="L"></path><path d="M264 165c3 0 5 3 8 4 2 0 2 1 4 1 1 0 2 0 3 1s6 4 8 4c0 0 1 0 2 1-1 0-3 0-4 1l-6-5-1 1c-1-1-1-1-2-1-2-1-5-1-6-3-1-1-3-2-4-2l-2-2z" class="H"></path><path d="M297 179h1v2h2c0 2 0 5 1 6v2l-1-1c0 1-1 1-2 1l-1-1-1-1-4-4-2-2 2 1 1-2 1 1c1 1 1 1 2 1l1-2v-1z" class="c"></path><path d="M297 179h1v2h2c0 2 0 5 1 6v2l-1-1c0 1-1 1-2 1l-1-1h1l-1-9z" class="X"></path><path d="M261 168c1 0 1 2 2 2h3c1 1 2 1 3 2h2c2 1 1 1 1 4h0c1 2 2 2 4 3l8 5 4 3c-1 0-2 1-3 1-2-1-5-3-8-4l-2-1c-2-3-7-4-10-6-2-1-3-2-4-4l-1-1c0-2 1-3 1-4z" class="C"></path><path d="M261 168c1 0 1 2 2 2h3c1 1 2 1 3 2h2c2 1 1 1 1 4h0v-3l-3 3c-1 1-1 1-2 1-2-1-1 0-2-2 1-1 1-1 1-3-2 0-3 0-5 1l-1-1c0-2 1-3 1-4z" class="K"></path><defs><linearGradient id="AR" x1="321.1" y1="204.925" x2="303.409" y2="204.575" xlink:href="#B"><stop offset="0" stop-color="#8b8c8e"></stop><stop offset="1" stop-color="#b2b1b1"></stop></linearGradient></defs><path fill="url(#AR)" d="M297 177l5 2 15 12c1 1 3 2 5 4l1 1h0l-2 2 1 1 3 3v1l3 2c1 1 3 2 5 4h-4l-1-1-3 1 2 2-1 1 1 2h-2-2c-2-1-4-1-5-1l-5-1-2-1h0l-6-6 1-1-3-3-5-6h0v-1c0-2-1-4-2-5l-1-1 1-1 1 1 1 1c1 0 2 0 2-1l1 1v-2c-1-1-1-4-1-6h-2v-2l-1-2z"></path><path d="M301 187c1 1 1 1 2 1l1 3 1 1c2 0 2 1 3 3-1 0-2-1-3 0 1 1 1 2 2 3v1c-2-2-3-4-5-6 0-1-1-4-1-4v-2z" class="D"></path><path d="M308 195c1 1 2 2 2 3 1 3 2 4 4 5h0l3 3 1 1c-4-2-8-5-11-8v-1c-1-1-1-2-2-3 1-1 2 0 3 0z" class="H"></path><path d="M296 187l1 1 1 1c1 0 2 0 2-1l1 1s1 3 1 4l-1 1c1 2 3 2 3 5l-1 2-5-6h0v-1c0-2-1-4-2-5l-1-1 1-1z" class="l"></path><path d="M306 204c6 5 11 6 18 9l1 1h-2c-2-1-4-1-5-1l-5-1-2-1h0l-6-6 1-1z" class="R"></path><path d="M310 198c3 1 4 2 6 4l1 1c1 0 1 0 1-1 1 1 1 2 2 2v-4l2-1 3 3v1l3 2c1 1 3 2 5 4h-4l-1-1-3 1 2 2-1 1 1 2h-2l-1-1 1-1c-2-2-5-4-7-5l-1-1-3-3h0c-2-1-3-2-4-5z" class="V"></path><path d="M328 205c1 1 3 2 5 4h-4l-1-1c-2 0-4-1-6-2 2 0 4-1 6-1z" class="g"></path><path d="M322 199l3 3v1l3 2c-2 0-4 1-6 1l-2-2v-4l2-1z" class="M"></path><path d="M297 177l5 2 15 12c1 1 3 2 5 4l1 1h0l-2 2 1 1-2 1v4c-1 0-1-1-2-2 0 1 0 1-1 1l-1-1c-2-2-3-3-6-4 0-1-1-2-2-3-1-2-1-3-3-3l-1-1-1-3c-1 0-1 0-2-1s-1-4-1-6h-2v-2l-1-2z" class="R"></path><path d="M305 192v-3c3 1 5 3 7 4 2 2 3 4 4 6 1 1 2 2 2 3s0 1-1 1l-1-1c-2-2-3-3-6-4 0-1-1-2-2-3-1-2-1-3-3-3z" class="M"></path><path d="M297 177l5 2 15 12c1 1 3 2 5 4l1 1h0l-2 2c-3-4-8-6-12-8l-9-9h-2v-2l-1-2z" class="Y"></path><path d="M230 157c5-1 8-1 13 0 3 1 6 2 8 4h1l2 2-3 3 3 4c1 2 1 5 0 7l-2 1h-4-2l-1-2h-2l-7-1c-5-1-9-2-14-2h-6c-2 0-3-1-3-2l1-2 2-2-1-1 3-2 5-1h-2c-3-1-5-1-7-2l6-2 1 1h1c2 0 4 0 6-1 1-1 2-1 3-1l-1-1z" class="P"></path><path d="M236 175c3-1 5 0 8 0 2 0 3-1 6 0l-5 1h-2l-7-1z" class="X"></path><path d="M243 163l3 3h0 0c1 1 2 1 2 2-8-3-19-5-27-3h-1c-1 0-3 1-4 2l-1-1 3-2 5-1h1c6-1 12 0 19 0z" class="G"></path><defs><linearGradient id="AS" x1="244.664" y1="176.74" x2="251.842" y2="167.477" xlink:href="#B"><stop offset="0" stop-color="#242529"></stop><stop offset="1" stop-color="#3e3e3f"></stop></linearGradient></defs><path fill="url(#AS)" d="M249 164l2 2 3 4c1 2 1 5 0 7l-2 1h-4-2l-1-2 5-1c1 0 1-1 2-2 0-2-2-3-4-5 0-1-1-1-2-2h0 0l3-2z"></path><path d="M228 159c6 0 10 1 15 4-7 0-13-1-19 0h-1-2c-3-1-5-1-7-2l6-2 1 1h1c2 0 4 0 6-1z" class="M"></path><defs><linearGradient id="AT" x1="237.992" y1="154.463" x2="243.008" y2="163.537" xlink:href="#B"><stop offset="0" stop-color="#46474c"></stop><stop offset="1" stop-color="#646465"></stop></linearGradient></defs><path fill="url(#AT)" d="M230 157c5-1 8-1 13 0 3 1 6 2 8 4h1l2 2-3 3-2-2-3 2-3-3c-5-3-9-4-15-4 1-1 2-1 3-1l-1-1z"></path><path d="M251 161h1l2 2-3 3-2-2-3-2c2 0 3-1 5-1z" class="M"></path><path d="M231 158c1 0 2 1 3 1 3 0 12 3 12 3l3 2-3 2-3-3c-5-3-9-4-15-4 1-1 2-1 3-1z" class="R"></path><path d="M207 163l7-2c2 1 4 1 7 2h2l-5 1-3 2 1 1-2 2-1 2c0 1 1 2 3 2h6c5 0 9 1 14 2l7 1c-2 1-4 2-5 3l-1 1v1h-7c-1 1-1 1-1 2h-4c-1-1-3-1-5-1-2-1-4-1-7-1-1 0-2 1-3 1-4 0-20-1-23 1 0 0-1-1-2-1h-2v-1c0-1-1-2-3-3-1 0 0 0-1-1l-1-3h0c-1-2-1-4-2-6l23-3h0c3 0 5-1 8-2z" class="S"></path><path d="M178 174l29-1h1l-1 3-1 1h-24-1-2l-1-3z" class="X"></path><path d="M182 177c2-1 4-1 6-1 3-1 6 0 9 0 2 0 6-1 8-1 0 0 1 1 2 1l-1 1h-24z" class="Y"></path><path d="M207 163l7-2c2 1 4 1 7 2h2l-5 1-3 2h-1c-2 1-3 3-5 4-1 1-3 1-4 1-6 1-24 1-27 3-1-2-1-4-2-6l23-3h0c3 0 5-1 8-2z" class="f"></path><path d="M207 163l7-2c2 1 4 1 7 2h2l-5 1c-3-2-8 1-12 1-1 1-2 1-3 1l1-1h-5 0 0c3 0 5-1 8-2z" class="U"></path><path d="M206 178c3-1 2-3 4-4v1h2l1 1c2-2 7-1 10 0 3 0 13 1 15 3l-1 1v1h-7c-1 1-1 1-1 2h-4c-1-1-3-1-5-1-2-1-4-1-7-1-1 0-2 1-3 1-4 0-20-1-23 1 0 0-1-1-2-1h-2v-1c0-1-1-2-3-3-1 0 0 0-1-1h2 1 24v1z" class="AE"></path><path d="M223 176c3 0 13 1 15 3l-1 1c-1-1-3 0-5 0-3 0-8 0-11-1-4-1-9 0-11-2h3c3 0 7 0 9-1h1z" class="P"></path><path d="M179 177h2 1 24v1h0c-4 1-9-1-14 0l1 1h5l2 1 1-1v1h6c1 0 2 1 3 1h3c-1 0-2 1-3 1-4 0-20-1-23 1 0 0-1-1-2-1h-2v-1c0-1-1-2-3-3-1 0 0 0-1-1z" class="AH"></path><path d="M179 177h2c1 1 3 2 4 2l1 1c0 1-1 2-1 2h-2v-1c0-1-1-2-3-3-1 0 0 0-1-1z" class="x"></path><path d="M861 233v-1l-1-1h2c-1-1 0-1-1-2 1 0 2 1 3 2h1 3 0l2-1h3c1 2 0 4-1 6l2-1c-1 2-3 4-3 6l-1 1 1 1v2c-3 4-5 8-6 13 0 1 0 1 1 1 1 1 1 3 2 4 2 1 2 4 3 5 1 4 5 6 8 9 0 1 1 2 2 4l2 5c2 3 3 6 4 9 2 7 4 21-1 28-1 3-3 5-5 7-6 8-11 17-9 27 2 8 5 14 12 18 3 2 7 3 11 4-3 3-7 5-10 7-10 7-23 10-35 8h-2c-8-2-13-5-19-10-1 0-5-5-5-5l1-1 1-3 1-1h1c1 1 2 2 4 3h1l-2-2c1 0 2 0 3 1h1 0c0-1 0-2-1-3v-1c0-1 0-2-1-3h-1c1-2 2-2 3-2l-1-2-2-6c0-1 0 0-1-1l-1-3c0-1 1-1 1-3l-2 1v1c-2 0-4 0-6-1h0l-1 1c0-2 0-3-1-5l-1-1c1-3 2-5 4-6l1-1c2-1 5-4 7-5v-1c2 0 4 0 7 1l5 1h1c2 1 3 3 4 4l2 2v-1c2 1 3 1 4 0l1 1c1-1 1-1 1-2l-2-2c-1-3-4-5-7-8l2-1c1 0 1 0 2-2h0c1 0 2 0 3 1l3-1v-15l-2-5c0-3-4-8-6-11l-5-5 1-1c0-2-1-2-2-4l-4-8 1-2c-1-1-1-2 0-4v-7c0-3 0-5 1-7h1v4l1-1v-2c1 1 2 3 3 3 1 2 2 2 4 3h1 1l1-3 3-7 2-6v-3h0l1-4c1-1 2-2 2-3 0-2 0-5-1-7z" class="a"></path><path d="M846 377l4 1-2 2v1h-7l1-1c1-1 2-1 4-3z" class="O"></path><path d="M865 369h5l2 2c2 1 3 2 4 3-1 1-2 1-3 2l-2-2s0-1-1-1h-2 0l-2-3-1-1h0z" class="K"></path><path d="M868 373h2c1 0 1 1 1 1l2 2c1-1 2-1 3-2 1 2 1 3 1 5h-1v-1c-1 0-1 0-2 1-2 1-3 2-5 3-1-1 0-1-1-2h0 1c1-1 2-2 2-3 0-2-1-3-3-4z" class="c"></path><path d="M861 373l2-1c0 2-1 2-1 4-4 2-9 4-14 5v-1l2-2c1 0 4-2 5-3v1c3 0 4-1 6-3z" class="e"></path><path d="M865 320l1-1v-5-4c-1-2-2-4-2-7h1l1 5c2 4 3 15 2 20v-1c-1-3 0-6-1-9 0 2 1 5 0 6h-1v-4c-1 2-1 11 0 13v1 1 1c1 1 0 2 0 3-1-6-1-12-1-19z" class="Q"></path><path d="M864 366c0 1 0 2 1 3l1 1 2 3h0c2 1 3 2 3 4 0 1-1 2-2 3h-1 0l-6-4c0-2 1-2 1-4l-2 1 2-3 1-1v-3h0z" class="b"></path><path d="M866 370l2 3h0c2 1 3 2 3 4 0 1-1 2-2 3h-1c1-1 1-2 2-3v-1c-1-1-3-1-5-2 0-2 0-2 1-4z" class="V"></path><defs><linearGradient id="AU" x1="860.553" y1="356.48" x2="851.555" y2="378.065" xlink:href="#B"><stop offset="0" stop-color="#33373a"></stop><stop offset="1" stop-color="#4e4f55"></stop></linearGradient></defs><path fill="url(#AU)" d="M864 355c1 3 1 8 1 11v3h0c-1-1-1-2-1-3h0v3l-1 1-2 3c-2 2-3 3-6 3v-1c-1 1-4 3-5 3l-4-1s1 0 1-1c3-2 4-3 6-6 4-3 8-10 11-15z"></path><path d="M861 368c1-1 2-5 3-5v3h0v3l-1 1v-1l-2-1z" class="g"></path><path d="M861 368l2 1v1l-2 3c-2 2-3 3-6 3v-1c2-2 4-4 6-7z" class="U"></path><defs><linearGradient id="AV" x1="866.591" y1="331.883" x2="873.409" y2="314.117" xlink:href="#B"><stop offset="0" stop-color="#373a40"></stop><stop offset="1" stop-color="#525457"></stop></linearGradient></defs><path fill="url(#AV)" d="M864 300c2 1 4 3 6 4h0l2 1 1 2c1 0 2 1 2 2v1l-2 1c0 5 1 12-1 17v1c-1 2-2 6-3 8v-1-2c-1 0-1-1-1-1v-3-2c1-5 0-16-2-20l-1-5v-2l-1-1z"></path><path d="M864 300c2 1 4 3 6 4h0l2 1 1 2c-1 1-1 2-1 4h0l-3-3c0-1 0-1-1-2l-3-5-1-1z" class="M"></path><path d="M865 301l3 5c1 6 2 12 1 18l-1 6v-2c1-5 0-16-2-20l-1-5v-2z" class="K"></path><path d="M868 263c2 1 2 4 3 5 1 4 5 6 8 9 0 1 1 2 2 4l-1-1c-1 0-1-1-2-2h0c-2-1-2-3-4-4h-2l-1 1c2 1 3 1 4 3l1 2 1 1 1 1s0 1 1 1l1 3c2 4 3 8 3 13 1 2 1 7 1 10-1-1-1-1-1-2l1-1h0c-1-2-1-3-1-4h0c-1 5-1 9-3 14v1l-4 9c0-4 2-14-1-16v-1c0-1-1-2-2-2l-1-2h1v-3l-1-3c-3-3-4-6-6-9l-2-2v2c-1-3-1-5-1-8v-1-3c1-1 1-7 2-9l1 1c1 0 1 1 2 0l-1-6v-1h1z" class="c"></path><path d="M880 286c2 4 3 8 3 13v-1c-2-3-4-9-3-12z" class="N"></path><path d="M877 282c2 4 0 7 1 11l1 5h-1-1c-1 0-1-1-2-2 0-2 0-6 1-8 0-1 0-1-1-2h1 1v-4z" class="D"></path><path d="M866 290l-2-5h1l1 1h4 1 4c1 1 1 1 1 2-1 2-1 6-1 8 1 1 1 2 2 2v2c1 0 1 0 2-1v12l-1-3h-1c0-1-1-1-1-2s-2-3-3-4l-1-3c-3-3-4-6-6-9z" class="K"></path><path d="M866 290l-2-5h1l1 1h4 1c0 4 0 6 2 9v3l-1 1c-3-3-4-6-6-9z" class="c"></path><path d="M866 286h4v2l-2 1c-1-1-1-2-2-3z" class="h"></path><path d="M867 264c1 1 2 2 3 4v1 1c-1 2 0 4 1 6 2 2 4 3 6 6v4h-1-1-4-1-4l-1-1h-1l2 5-2-2v2c-1-3-1-5-1-8v-1-3c1-1 1-7 2-9l1 1c1 0 1 1 2 0l-1-6z" class="H"></path><path d="M863 282c5 1 9 2 13 4h-1-4-1-4l-1-1h-1l2 5-2-2v2c-1-3-1-5-1-8z" class="W"></path><path d="M865 269l1 1c1 0 1 1 2 0 1 1 1 0 1 1 0 2-1 4-1 5-1 2-1 3-2 4h-1c-1 0-1 0-2 1v-3c1-1 1-7 2-9z" class="V"></path><path d="M861 233v-1l-1-1h2c-1-1 0-1-1-2 1 0 2 1 3 2h1 3 0l2-1h3c1 2 0 4-1 6l2-1c-1 2-3 4-3 6l-1 1 1 1v2c-3 4-5 8-6 13 0 1 0 1 1 1 1 1 1 3 2 4h-1v1l1 6c-1 1-1 0-2 0l-1-1c-1 2-1 8-2 9v3 1c0 3 0 5 1 8v-2l2 2c2 3 3 6 6 9l1 3v3h-1l-2-1h0c-2-1-4-3-6-4 0 0-1-1-1-2-2-2-3-5-4-7s-1-3-2-5c0-1-1-1-1-2h0l-1-1h-1c-1-3-1-6-1-9-1-2 0-2 0-3l1-3-1 1c-1 1-1 2-1 3h0l-2 1 1-1c0-2 1-4 2-6l1-3 3-7 2-6v-3h0l1-4c1-1 2-2 2-3 0-2 0-5-1-7z" class="S"></path><path d="M861 261c0 1 1 2 0 3h1v-1l1-1v1l-2 11-1 3c0 1-1 2-2 2h-1l4-18z" class="l"></path><path d="M857 279h1c1 0 2-1 2-2l1-3c0 4-1 8 0 12 0 2 1 4 2 7-1 1 0 3 0 5-2-2-3-5-4-7s-1-3-2-5c-1-3-1-5 0-7z" class="e"></path><path d="M870 242l1 1v2c-3 4-5 8-6 13l-2 5v-1l-1 1v1h-1c1-1 0-2 0-3 1-8 4-13 9-19z" class="Y"></path><defs><linearGradient id="AW" x1="860.339" y1="268.25" x2="866.351" y2="266.348" xlink:href="#B"><stop offset="0" stop-color="#2c292e"></stop><stop offset="1" stop-color="#323736"></stop></linearGradient></defs><path fill="url(#AW)" d="M865 258c0 1 0 1 1 1 1 1 1 3 2 4h-1v1l1 6c-1 1-1 0-2 0l-1-1c-1 2-1 8-2 9s-1 1-2 3v5c-1-4 0-8 0-12l2-11 2-5z"></path><path d="M865 269c0-2 1-4 2-6v1l1 6c-1 1-1 0-2 0l-1-1z" class="J"></path><path d="M861 286v-5c1-2 1-2 2-3v3 1c0 3 0 5 1 8v-2l2 2c2 3 3 6 6 9l1 3v3h-1l-2-1h0c-2-1-4-3-6-4 0 0-1-1-1-2 0-2-1-4 0-5-1-3-2-5-2-7z" class="I"></path><path d="M863 293l7 11h0c-2-1-4-3-6-4 0 0-1-1-1-2 0-2-1-4 0-5zm-2-60v-1l-1-1h2c-1-1 0-1-1-2 1 0 2 1 3 2h1 3 0l2-1h3c1 2 0 4-1 6-11 9-12 20-15 33 0 2-1 9-2 10-1-8 2-16 4-23 0-3 2-6 2-9l-2 3v-3h0l1-4c1-1 2-2 2-3 0-2 0-5-1-7z" class="U"></path><path d="M861 233v-1l-1-1h2c-1-1 0-1-1-2 1 0 2 1 3 2h1 3v1c0 1 0 3-1 4-1 3-7 9-6 11l-2 3v-3h0l1-4c1-1 2-2 2-3 0-2 0-5-1-7z" class="j"></path><path d="M841 273v-7c0-3 0-5 1-7h1v4l1-1v-2c1 1 2 3 3 3 1 2 2 2 4 3h1 1c-1 2-2 4-2 6l-1 1 2-1h0c0-1 0-2 1-3l1-1-1 3c0 1-1 1 0 3 0 3 0 6 1 9h1l1 1h0c0 1 1 1 1 2 1 2 1 3 2 5s2 5 4 7c0 1 1 2 1 2l1 1v2h-1c0 3 1 5 2 7v4 5l-1 1c0 7 0 13 1 19 0 2 1 6 0 9 0 2-3 5-2 7-3 5-7 12-11 15-2 3-3 4-6 6 0 1-1 1-1 1-2 2-3 2-4 3l-1 1c-1 0-2 0-3-1-3 0-4-2-6-3h1l-2-2c1 0 2 0 3 1h1 0c0-1 0-2-1-3v-1c0-1 0-2-1-3h-1c1-2 2-2 3-2l-1-2-2-6c0-1 0 0-1-1l-1-3c0-1 1-1 1-3l-2 1v1c-2 0-4 0-6-1h0l-1 1c0-2 0-3-1-5l-1-1c1-3 2-5 4-6l1-1c2-1 5-4 7-5v-1c2 0 4 0 7 1l5 1h1c2 1 3 3 4 4l2 2v-1c2 1 3 1 4 0l1 1c1-1 1-1 1-2l-2-2c-1-3-4-5-7-8l2-1c1 0 1 0 2-2h0c1 0 2 0 3 1l3-1v-15l-2-5c0-3-4-8-6-11l-5-5 1-1c0-2-1-2-2-4l-4-8 1-2c-1-1-1-2 0-4z" class="W"></path><path d="M852 289c1 1 2 1 3 2-1 2-2 2-4 2h0l-2-1c1-2 2-2 3-3z" class="L"></path><path d="M858 313c1 4 2 7 1 11v1c-1 2 0 4-1 6v-3-15z" class="C"></path><path d="M835 361l2-2c1 2 2 3 4 5h0c1 2 2 2 4 2l-1 1 1 1h-3c-5-1-6-4-8-7h1z" class="AB"></path><path d="M852 328h0c1 0 2 0 3 1l3-1v3c0 3-1 5-3 8-1-3-4-5-7-8l2-1c1 0 1 0 2-2z" class="s"></path><path d="M851 343v-1c2 1 3 1 4 0l1 1c0 2 1 3 2 5 1-1 2-4 3-4 1 2 0 6-1 8l-1 1h-4-1c-1-4-2-7-3-10z" class="r"></path><path d="M854 353h1 4c-2 5-5 11-10 13-2 1-3 1-4 2l-1-1 1-1c-2 0-3 0-4-2 3 0 6 0 9-2s4-5 4-9z" class="q"></path><path d="M835 367c0 1 0 3 2 4 3 1 6 1 10 1h0c2-1 4-1 6-2-2 3-3 4-6 6 0 1-1 1-1 1-2 2-3 2-4 3l-1 1c-1 0-2 0-3-1-3 0-4-2-6-3h1l-2-2c1 0 2 0 3 1h1 0c0-1 0-2-1-3v-1c0-1 0-2-1-3h-1c1-2 2-2 3-2z" class="D"></path><path d="M838 380l3-1c1-1 2-2 5-2-2 2-3 2-4 3l-1 1c-1 0-2 0-3-1z" class="H"></path><path d="M835 367c0 1 0 3 2 4 3 1 6 1 10 1-2 0-3 1-4 1h-1-4c-1 1-1 2-1 3 1 1 1 2 2 3h-2c-1 0-1-1-2-1l-2-1-2-2c1 0 2 0 3 1h1 0c0-1 0-2-1-3v-1c0-1 0-2-1-3h-1c1-2 2-2 3-2z" class="R"></path><path d="M841 273v-7c0-3 0-5 1-7h1v4l1-1v-2c1 1 2 3 3 3 1 2 2 2 4 3h1 1c-1 2-2 4-2 6l-1 1 2-1h0c0-1 0-2 1-3l1-1-1 3c0 1-1 1 0 3 0 3 0 6 1 9h1l1 1h0c0 1 1 1 1 2 1 2 1 3 2 5s2 5 4 7c0 1 1 2 1 2l1 1v2h-1c0 3 1 5 2 7v4 5l-1 1c0-3 0-6-1-10 0-3-1-6-2-8l-1-1v-2c-1-2-4-7-6-8-1-1-2-1-3-2-1 1-2 1-3 3l2 1-2 1-1-2-1 1c2 1 3 1 3 4l-5-5 1-1c0-2-1-2-2-4l-4-8 1-2c-1-1-1-2 0-4z" class="F"></path><path d="M854 283h1l1 1h0c0 1 1 1 1 2 1 2 1 3 2 5-2-1-3-2-3-3-2-2-2-3-2-5z" class="G"></path><path d="M850 273l2-1h0c0-1 0-2 1-3l1-1-1 3c0 1-1 1 0 3h0c-1 2-1 4-1 6 1 1 1 2 1 3-2-1-4-6-5-8 1-1 2-1 2-2z" class="K"></path><path d="M844 260c1 1 2 3 3 3 1 2 2 2 4 3h1 1c-1 2-2 4-2 6l-1 1c0 1-1 1-2 2 0-5-2-9-4-13v-2z" class="V"></path><path d="M841 273v-7c0-3 0-5 1-7h1v4c2 7 5 15 8 21 0 2 0 3 1 5-1 1-2 1-3 3l2 1-2 1-1-2-1 1c2 1 3 1 3 4l-5-5 1-1c0-2-1-2-2-4l-4-8 1-2c-1-1-1-2 0-4z" class="N"></path><path d="M840 279l1-2c-1-1-1-2 0-4 0 2 1 5 2 7s3 4 4 7c1 2 2 2 2 5h0l2 1-2 1-1-2-1 1c2 1 3 1 3 4l-5-5 1-1c0-2-1-2-2-4l-4-8z" class="F"></path><path d="M847 287c1 2 2 2 2 5l-1-1c-1-2-1-2-1-4z" class="D"></path><path d="M832 335c2 0 4 0 7 1l5 1h1c2 1 3 3 4 4l2 2c1 3 2 6 3 10 0 4-1 7-4 9s-6 2-9 2h0c-2-2-3-3-4-5l-2 2h-1l-1-2h-1c0-1 0 0-1-1l-1-3c0-1 1-1 1-3l-2 1v1c-2 0-4 0-6-1h0l-1 1c0-2 0-3-1-5l-1-1c1-3 2-5 4-6l1-1c2-1 5-4 7-5v-1z" class="Z"></path><path d="M845 337c2 1 3 3 4 4-1 0-2-1-4-1v2c-2-1-3 0-5-1 0 0 1-1 2-1l3-3z" class="C"></path><path d="M839 336l5 1h1l-3 3c-1 0-2 1-2 1h-1v-5z" class="m"></path><path d="M845 355c1 0 1 1 2 1v2c-1 1-1 1-3 2h-1c-1-1-1-1-1-2 1-2 2-2 3-3z" class="d"></path><path d="M842 346h1c1 0 2 0 2-1l1-1c1 1 1 2 2 3v2c-1 0-3 1-3 1-3-1-4 1-5 1s-1-1-3-1l-1 2-1-1v-1l1-1c2-2 3-2 6-3z" class="H"></path><path d="M835 351v-1 1l1 1 1-2c2 0 2 1 3 1s2-2 5-1c-3 2-7 3-8 6v3l-2 2h-1l-1-2h-1c0-1 0 0-1-1l-1-3c0-1 1-1 1-3l1-2 2 2s0-1 1-1z" class="O"></path><path d="M832 350l2 2c-1 1-1 3-1 4 1 1 2 0 3 2l-1 3h-1l-1-2h-1c0-1 0 0-1-1l-1-3c0-1 1-1 1-3l1-2z" class="b"></path><path d="M838 341h1 1c2 1 3 0 5 1v1 1c-1 1-2 2-3 2-3 1-4 1-6 3l-1 1v1c-1 0-1 1-1 1l-2-2c-1-1-2-1-3-2h0c1-1 1-2 2-2l2-2h0l1-1c1 0 1 0 1-1h1l2-1z" class="e"></path><path d="M836 342l2-1 1 3-1 1-1-1-1-2z" class="U"></path><path d="M833 344l1-1c1 1 1 2 2 3l-1 2h-1c-1-1-1-3-1-4z" class="M"></path><path d="M838 341h1 1c2 1 3 0 5 1v1c-3 0-4 0-6 1l-1-3z" class="O"></path><path d="M833 344h0c0 1 0 3 1 4l2 1-1 1v1c-1 0-1 1-1 1l-2-2c-1-1-2-1-3-2h0c1-1 1-2 2-2l2-2z" class="g"></path><path d="M833 344h0c0 1 0 3 1 4l2 1-1 1v1c-2-1-3-3-4-5l2-2z" class="k"></path><path d="M832 335c2 0 4 0 7 1v5h-1l-2 1h-1c0 1 0 1-1 1l-1 1h0l-2 2c-1 0-1 1-2 2h0c1 1 2 1 3 2l-1 2-2 1v1c-2 0-4 0-6-1h0l-1 1c0-2 0-3-1-5l-1-1c1-3 2-5 4-6l1-1c2-1 5-4 7-5v-1z" class="D"></path><path d="M826 346l-2-2 1-1h1c1 0 3-1 4-1s2 2 3 2l-2 2c-1 0-1 1-2 2h0l-3-2z" class="O"></path><path d="M832 335c2 0 4 0 7 1v5h-1l-2 1h-1c-1 0-1 0-2-1v-3h0v-1l-3 2c-1 1-2 1-3 1v1c-1 1-1 1-3 1l1-1c2-1 5-4 7-5v-1z" class="h"></path><path d="M826 346l3 2c1 1 2 1 3 2l-1 2-2 1v1c-2 0-4 0-6-1h0l-1 1c0-2 0-3-1-5 0 0 0-1 1-2h3l1-1z" class="X"></path><path d="M821 349s0-1 1-2h3l-2 2c1 2 4 3 6 4v1c-2 0-4 0-6-1h0l-1 1c0-2 0-3-1-5z" class="H"></path><path d="M515 582h5c-1 4-1 9-1 13 0 3 0 5 1 8l1 2c3 8 8 19 15 24l8 2c4 0 9 0 12-2 2 0 4-1 6-3l2 1 1-3c2-2 3-5 5-8 0 2 1 3 1 4h1v-1c1-2 1-3 3-4v2c0 4 1 8 2 12h-1c-2 1-2 1-2 3v1h0c1 0 1 1 2 1l1 3-1 3c-2 2-3 4-5 5h-1c-2 1-4 2-6 2-1 0-2-1-3-2 0-1 0-1-1-1v2c-1 0-2-1-3-2-1 0-2 0-3 1l-3-3-4-1c1 3 4 5 5 7 1 3 1 5 1 8 1-1 2-2 4-3v4c-3 3-6 8-10 10v1h-1l-3 6-2-2c-1 1-2 1-3 1h0c-2 1-3 2-4 4h-1c0 1 0 1 1 2 0 2 0 2-1 4l-1-1-1 1c-2-4-5-7-8-10-1-1-2-1-2-2v-2h0c-7 4-13 7-21 9h-4 0-7c3 6 5 12 7 18v2l-8 3c-3-9-6-20-11-30-1 1-2 1-2 1-2-1-6-2-7-3-2-1-3-2-4-2l-1-2h-1l-1 1h-1l-2 2-1-2-3 5v-3-1l-1-2 1-1c0-2 1-2 1-4-1 1-3 5-4 5 1-3 1-6 3-10 1-2 2-4 3-5 5-5 13-6 20-6l-4-12v-1l-2-4h1v-1l2-2 2 1c1 0 2 0 3 1l1 1 2 2h1l7-1s1 0 2 1l1 1 3-3 1-1 2-2 3-4c2 1 3 1 5 1-1-1-1-2-3-2l1-1v-1h-2l-1-1v-10s1-1 1 0v-5h1l1 1v-1c1-2 1-3 1-5-1-1-2-4-2-5v-2-1l1-1h1 2c2-1 4-3 6-4l1-1z" class="I"></path><path d="M490 628s1 0 2 1l1 1c-2 1-3 1-4 2-4 1-4-2-6-3l7-1z" class="D"></path><path d="M500 639c3 2 5 4 7 5l1 1c0 1 1 2 2 3-6 0-8-2-12-6 3 0 2 2 4 2h2c1 1 0 1 2 1l-2-2-3-3-1-1z" class="G"></path><path d="M474 624l2 1c1 0 2 0 3 1l1 1 2 2-5 1-4 1-2-4h1v-1l2-2z" class="w"></path><path d="M477 630l1-3h2l2 2-5 1z" class="p"></path><path d="M482 646c-2-1 0-5-1-6l-2-2c1 0 3-1 4-1s2 0 3 1h1 0c3 1 6 2 8 5l1 1h0-2v-1l-1-1c-1-1-3-1-5-1 0 0-3-1-4-1h-2v1l1 4c-1 0-1 1-1 1z" class="D"></path><path d="M497 626h1 1c2-1 3-1 5 0-3 0-5 2-7 3-1 1 0 2 0 3 1 3 3 4 5 5v1h0c1 1 1 2 3 3v1c1 0 1 0 2 1v1c-2-1-4-3-7-5l-6-4c-2 0-4-2-5-3 1-1 2-1 4-2l3-3 1-1z" class="g"></path><path d="M493 630l3-3c-2 2-3 4-2 7v1c-2 0-4-2-5-3 1-1 2-1 4-2z" class="O"></path><path d="M486 638c5 0 8 1 12 4 4 4 6 6 12 6 4-1 8-1 11-4l1 1c-4 4-13 6-18 6h-1c-3-1-4-4-7-6v-1l-1-1c-2-3-5-4-8-5h0-1z" class="L"></path><path d="M482 646s0-1 1-1l-1-4v-1h2c1 0 4 1 4 1 2 0 4 0 5 1l1 1v1h2 0v1c3 2 4 5 7 6l1 3-4-1c-1 1 0 3-1 4h0v-2c-1-2-2-3-3-4l-3-2s-1 1-2 1h0c-1 1-2 1-3 2l-1-1c-1-1-1-2-2-3l-3-2z" class="T"></path><path d="M493 645v2l-2 3c-1 1-2 1-3 2l-1-1 1-1c0-2 0-2-1-3h1c2 0 2 0 4-2v1l1-1z" class="M"></path><path d="M482 646s0-1 1-1l-1-4v-1h2c0 2 1 4 1 6l2 1c1 1 1 1 1 3l-1 1c-1-1-1-2-2-3l-3-2z" class="g"></path><path d="M488 641c2 0 4 0 5 1l1 1v1h2 0v1c3 2 4 5 7 6l1 3-4-1c-1 1 0 3-1 4h0v-2c-1-2-2-3-3-4l-3-2s-1 1-2 1h0l2-3v-2l-1-1c0-2-2-2-4-3z" class="G"></path><defs><linearGradient id="AX" x1="473.647" y1="653.99" x2="488.982" y2="652.334" xlink:href="#B"><stop offset="0" stop-color="#393a3d"></stop><stop offset="1" stop-color="#5c5b5c"></stop></linearGradient></defs><path fill="url(#AX)" d="M468 649h3l1-1c2-1 5 0 7 0h1c2 1 3 1 5 0 1 1 1 2 2 3l1 1c1-1 2-1 3-2h0c1 0 2-1 2-1l3 2c1 1 2 2 3 4v2h0v1c0 3-4 4-6 6l-4-3c-2-1-2-2-3-4v-2c0-1-1-1-2-2-6-3-10-4-16-4z"></path><path d="M496 651c1 1 2 2 3 4v2c-2 1-1 0-3 0 0 1 0 1 1 2v1c-1 1-3 2-4 2s-3-2-3-4c-1-1-1-3 0-5 2-1 4-1 6-2z" class="f"></path><path d="M506 596c1 2 1 5 1 8l1 1v-1h1c0 2 1 4 1 6 0 1 1 1 1 3h1l1 1c2 3 4 7 6 10l1-1c1 2 2 5 4 7 2 4 4 7 4 12-2 2-3 4-5 7-5 3-9 4-15 5h-2-2l-1-3h1c5 0 14-2 18-6l-1-1c-3 3-7 3-11 4-1-1-2-2-2-3l-1-1v-1c-1-1-1-1-2-1v-1c-2-1-2-2-3-3h0v-1c-2-1-4-2-5-5 0-1-1-2 0-3 2-1 4-3 7-3-2-1-3-1-5 0h-1-1l2-2 3-4c2 1 3 1 5 1-1-1-1-2-3-2l1-1v-1h-2l-1-1v-10s1-1 1 0v-5h1l1 1v-1c1-2 1-3 1-5h0z" class="T"></path><path d="M511 625c1 1 2 1 3 2l-1 3c-2-1-3-2-4-3 0-1 1-1 1-1l1-1z" class="H"></path><path d="M513 614c2 3 4 7 6 10l2 4c1 1 1 2 2 3l-1 1-1-1-1 2 1 1c0 2-1 3-1 4h-1c0-2-1-3-2-5v-2c-1 0-1-1-1-2-3-4-3-11-3-15z" class="S"></path><path d="M520 633v-1l-2-2c-1-2-1-4-1-6h1c0 2 1 3 2 4h1c1 1 1 2 2 3l-1 1-1-1-1 2z" class="c"></path><path d="M504 626c1 0 2 1 4 2l1-1c1 1 2 2 4 3h1c0 1-1 2-1 3h-4-1c-2 1-4 1-6 1v4-1c-2-1-4-2-5-5 0-1-1-2 0-3 2-1 4-3 7-3z" class="X"></path><path d="M502 638v-1c-2-1-4-2-5-5 0-1-1-2 0-3 1 1 2 2 3 2l1 1c2 1 5 1 8 1h-1c-2 1-4 1-6 1v4z" class="M"></path><path d="M517 633c1 2 2 3 2 5h1c0 1 1 2 1 3 1 1 1 1 2 3v1h-1l-1-1c-3 3-7 3-11 4-1-1-2-2-2-3l-1-1v-1c-1-1-1-1-2-1v-1c-2-1-2-2-3-3 3 2 5 4 8 5h0c1-1 1-1 1-2 2-1 3-2 4-4 0-1 1-2 2-3v-1z" class="k"></path><path d="M519 638h1c0 1 1 2 1 3 1 1 1 1 2 3v1h-1l-1-1c-3 3-7 3-11 4-1-1-2-2-2-3l3 1c3-1 4-3 6-5h0l-2 3h1c2-1 3-3 3-6z" class="O"></path><path d="M519 638h1c0 1 1 2 1 3 1 1 1 1 2 3v1h-1l-1-1v-1h-1l-1 1c-2 0-2 1-4 1v-1h1c2-1 3-3 3-6z" class="b"></path><path d="M503 606v-5h1l1 1 1 1c0 2 0 5 1 8 1 2 3 4 4 7h0c1 2 1 5 0 7l-1 1s-1 0-1 1l-1 1c-2-1-3-2-4-2-2-1-3-1-5 0h-1-1l2-2 3-4c2 1 3 1 5 1-1-1-1-2-3-2l1-1v-1h-2l-1-1v-10s1-1 1 0z" class="X"></path><path d="M502 606s1-1 1 0c1 1 1 3 1 4 0 3 0 5 1 7h-2l-1-1v-10z" class="f"></path><path d="M507 621l1-1v1c0 1-1 1-2 2-1 0 0 0-1 1h0c2 1 4 1 5 2 0 0-1 0-1 1l-1 1c-2-1-3-2-4-2-2-1-3-1-5 0h-1-1l2-2 3-4c2 1 3 1 5 1z" class="U"></path><path d="M519 624l1-1c1 2 2 5 4 7 2 4 4 7 4 12-2 2-3 4-5 7-5 3-9 4-15 5h-2-2l-1-3h1c5 0 14-2 18-6h1v-1c-1-2-1-2-2-3 0-1-1-2-1-3s1-2 1-4l-1-1 1-2 1 1 1-1c-1-1-1-2-2-3l-2-4z" class="C"></path><path d="M521 634c1 2 4 5 3 7 0 1 0 2-1 4v-1c-1-2-1-2-2-3 0-1-1-2-1-3s1-2 1-4z" class="N"></path><defs><linearGradient id="AY" x1="514.249" y1="664.169" x2="507.131" y2="656.782" xlink:href="#B"><stop offset="0" stop-color="#474649"></stop><stop offset="1" stop-color="#65676a"></stop></linearGradient></defs><path fill="url(#AY)" d="M523 649c2 1 2 1 3 2-2 6-6 10-10 13h0c2 0 4-1 6-2 2-2 1-4 4-5l1 1c0 3-2 5-4 7l-2 1v3h0c-7 4-13 7-21 9h-4 0-7c3 6 5 12 7 18v2l-8 3c-3-9-6-20-11-30-1 1-2 1-2 1-2-1-6-2-7-3-2-1-3-2-4-2l-1-2h-1l-1 1h-1l-2 2-1-2-3 5v-3-1l-1-2 1-1c0-2 1-2 1-4v-1c3-5 7-8 13-10 6 0 10 1 16 4 1 1 2 1 2 2v2c1 2 1 3 3 4l4 3c2-2 6-3 6-6v-1c1-1 0-3 1-4l4 1h2 2c6-1 10-2 15-5z"></path><path d="M507 658c0 3-2 5-3 7 1-1 1 0 2-2h1c0-1 0-2 1-3v1c0 1 0 2-1 2h2 0c-3 2-5 3-8 4-4 0-14 3-17 0-2 0-3 1-5 2-1 0-1-1-2-1l-1-5c1 1 1 2 2 3h0 2l1-1c2 0 2-2 3-3h0 2c-1 2-1 2 0 4h3 10 0c2 0 3-1 4-2l4-6z" class="U"></path><path d="M463 665c3 1 6 3 10 4 0 0 1 1 2 1s1-1 2-2c1 0 1 1 2 1 2-1 3-2 5-2 3 3 13 0 17 0l-1 1c-1 0-5 0-5 1h3v1 1c-4 1-9 1-13 0-2 0-5-1-8 0h0c-1 1-2 1-2 1-2-1-6-2-7-3-2-1-3-2-4-2l-1-2z" class="k"></path><path d="M463 665c3 1 6 3 10 4 0 0 1 1 2 1s1-1 2-2c1 0 1 1 2 1 5 2 10 1 14 2-2 1-5 0-8 0-2 0-5-1-8 0h0c-1 1-2 1-2 1-2-1-6-2-7-3-2-1-3-2-4-2l-1-2z" class="I"></path><path d="M466 659c2-1 4-2 6 0 2 0 3 3 4 4l1 5c-1 1-1 2-2 2s-2-1-2-1c-4-1-7-3-10-4h-1c0-2 2-5 4-6z" class="m"></path><path d="M499 657c1-1 0-3 1-4l4 1h2l2 2-1 2-4 6c-1 1-2 2-4 2h0-10-3c-1-2-1-2 0-4h1c-1 0-1-1-1-1l-3-3 1-1c1 0 1 1 2 1v-1c1 2 1 3 3 4l4 3c2-2 6-3 6-6v-1z" class="T"></path><path d="M508 656l-1 2-4 6h-1v-1c0-2 1-3 2-4h0c1-2 2-3 4-3z" class="X"></path><path d="M468 649c6 0 10 1 16 4 1 1 2 1 2 2v2 1c-1 0-1-1-2-1l-1 1 3 3s0 1 1 1h-1-2 0c-1 1-1 3-3 3l-1 1h-2 0c-1-1-1-2-2-3s-2-4-4-4c-2-2-4-1-6 0s-4 4-4 6l-1 1h-1l-2 2-1-2-3 5v-3-1l-1-2 1-1c0-2 1-2 1-4v-1c3-5 7-8 13-10z" class="P"></path><path d="M484 653c1 1 2 1 2 2s0 1-1 1-1 0-3-1c0-1 1-2 2-2z" class="L"></path><path d="M457 666c3-3 5-5 9-7-2 1-4 4-4 6l-1 1h-1l-2 2-1-2z" class="C"></path><path d="M516 664h0c2 0 4-1 6-2 2-2 1-4 4-5l1 1c0 3-2 5-4 7l-2 1v3h0c-7 4-13 7-21 9h-4 0-7c3 6 5 12 7 18v2l-8 3c-3-9-6-20-11-30h0c3-1 6 0 8 0 4 1 9 1 13 0 6 0 13-4 18-7z" class="o"></path><path d="M515 582h5c-1 4-1 9-1 13 0 3 0 5 1 8l1 2c3 8 8 19 15 24l8 2c4 0 9 0 12-2 2 0 4-1 6-3l2 1 1-3c2-2 3-5 5-8 0 2 1 3 1 4h1v-1c1-2 1-3 3-4v2c0 4 1 8 2 12h-1c-2 1-2 1-2 3v1h0c1 0 1 1 2 1l1 3-1 3c-2 2-3 4-5 5h-1c-2 1-4 2-6 2-1 0-2-1-3-2 0-1 0-1-1-1v2c-1 0-2-1-3-2-1 0-2 0-3 1l-3-3-4-1c1 3 4 5 5 7 1 3 1 5 1 8 1-1 2-2 4-3v4c-3 3-6 8-10 10v1h-1l-3 6-2-2c-1 1-2 1-3 1h0c-2 1-3 2-4 4h-1c0 1 0 1 1 2 0 2 0 2-1 4l-1-1-1 1c-2-4-5-7-8-10-1-1-2-1-2-2v-2h0 0v-3l2-1c2-2 4-4 4-7l-1-1c-3 1-2 3-4 5-2 1-4 2-6 2h0c4-3 8-7 10-13-1-1-1-1-3-2 2-3 3-5 5-7 0-5-2-8-4-12-2-2-3-5-4-7l-1 1c-2-3-4-7-6-10l-1-1h-1c0-2-1-2-1-3 0-2-1-4-1-6h-1v1l-1-1c0-3 0-6-1-8h0c-1-1-2-4-2-5v-2-1l1-1h1 2c2-1 4-3 6-4l1-1z" class="AA"></path><path d="M523 649c2-3 3-5 5-7 0 3-1 6-2 9-1-1-1-1-3-2z" class="D"></path><path d="M538 643c0 1 1 1 0 2v1l-3 6v2 1c0 1-2 2-2 4h-2-1c1-1 1-2 2-2 2-3 2-8 3-12 1 0 2-1 3-2z" class="k"></path><path d="M536 635c3 1 6 2 10 2-1 1-2 3-4 3h-1v2l-2 1h-1c-1 1-2 2-3 2 1-1 1-2 1-3 1-1 1-2 1-4l-1-3z" class="j"></path><path d="M536 642c2-2 2-2 5-2v2l-2 1h-1c-1 1-2 2-3 2 1-1 1-2 1-3z" class="w"></path><path d="M521 618h1c2 3 4 8 8 10l1 1h0 5l8 2c4 0 9 0 12-2 2 0 4-1 6-3l2 1c-3 3-6 6-9 7-2 2-4 2-7 3h-2c-4 0-7-1-10-2-5-3-7-5-11-9 0-2-3-6-4-8z" class="S"></path><path d="M562 626l2 1c-3 3-6 6-9 7-2 0-4 1-6 1 2-2 6-3 8-4h-3c-4 1-8 2-12 1l-2-1h-2c-2-1-3 0-4-1-1 0-2-1-3-1h5l8 2c4 0 9 0 12-2 2 0 4-1 6-3z" class="O"></path><defs><linearGradient id="AZ" x1="564.534" y1="632.809" x2="555.77" y2="627.793" xlink:href="#B"><stop offset="0" stop-color="#939297"></stop><stop offset="1" stop-color="#b6b1a6"></stop></linearGradient></defs><path fill="url(#AZ)" d="M565 624c2-2 3-5 5-8 0 2 1 3 1 4l-3 10-8 14v2c-1 0-2-1-3-2-1 0-2 0-3 1l-3-3-4-1c-2 0-4 0-6 1v-2h1c2 0 3-2 4-3h2c3-1 5-1 7-3 3-1 6-4 9-7l1-3z"></path><path d="M548 637c2 1 3 2 5 3 1 0 1 0 2 1v1l2 2c-1 0-2 0-3 1l-3-3-4-1c-2 0-4 0-6 1v-2h1c2 0 3-2 4-3h2z" class="d"></path><defs><linearGradient id="Aa" x1="567.337" y1="634.355" x2="554.974" y2="639.568" xlink:href="#B"><stop offset="0" stop-color="#87878a"></stop><stop offset="1" stop-color="#a19e9b"></stop></linearGradient></defs><path fill="url(#Aa)" d="M555 642l1-1v-1c0-4 8-9 10-11l2 1-8 14v2c-1 0-2-1-3-2l-2-2z"></path><path d="M575 615v2c0 4 1 8 2 12h-1c-2 1-2 1-2 3v1h0c1 0 1 1 2 1l1 3-1 3c-2 2-3 4-5 5h-1c-2 1-4 2-6 2-1 0-2-1-3-2 0-1 0-1-1-1l8-14 3-10h1v-1c1-2 1-3 3-4z" class="m"></path><path d="M574 633c1 0 1 1 2 1l1 3-1 3c-2 2-3 4-5 5h-1c-2 1-4 2-6 2-1 0-2-1-3-2h1 0c1 0 2 1 2 1 2 0 4-2 5-3 3-2 5-6 5-10z" class="U"></path><path d="M515 582h5c-1 4-1 9-1 13 0 3 0 5 1 8l1 2c3 8 8 19 15 24h-5 0l-1-1c-4-2-6-7-8-10h-1c1 2 4 6 4 8h0c0 2 3 5 5 6l-1 1-3-3h0v1h-1c0-1 0-1-1-2v1c-2-2-3-5-4-7l-1 1c-2-3-4-7-6-10l-1-1h-1c0-2-1-2-1-3 0-2-1-4-1-6h-1v1l-1-1c0-3 0-6-1-8h0c-1-1-2-4-2-5v-2-1l1-1h1 2c2-1 4-3 6-4l1-1z" class="R"></path><path d="M514 583l1-1v1c-3 1-4 5-5 8v1h0c-1 7 1 14 2 21h-1c0-2-1-2-1-3 0-2-1-4-1-6h-1v1l-1-1c0-3 0-6-1-8h0c-1-1-2-4-2-5v-2-1l1-1h1 2c2-1 4-3 6-4z" class="f"></path><path d="M504 589v-1l1-1h1 2c-1 3-2 6-2 9h0c-1-1-2-4-2-5v-2z" class="D"></path><path d="M504 589v-1l1-1h1c0 2-1 3-2 4v-2z" class="E"></path><path d="M520 623c-3-5-5-9-6-14-2-5-5-15-2-19 0 4 1 9 2 13 2 4 4 9 6 13h1v2h0c1 2 4 6 4 8h0c0 2 3 5 5 6l-1 1-3-3h0v1h-1c0-1 0-1-1-2v1c-2-2-3-5-4-7z" class="j"></path><path d="M515 582h5c-1 4-1 9-1 13 0 3 0 5 1 8l1 2c3 8 8 19 15 24h-5 0l-1-1c-4-2-6-7-8-10h-1 0v-2c-1-2-3-5-4-8-1-4-2-9-3-14 0-3-1-7 1-11h0v-1z" class="l"></path><path d="M515 582h5c-1 4-1 9-1 13h-1 0-1c0-4 0-8 1-11l-1-1h-2 0v-1z" class="L"></path><path d="M541 642c2-1 4-1 6-1 1 3 4 5 5 7 1 3 1 5 1 8 1-1 2-2 4-3v4c-3 3-6 8-10 10v1h-1l-3 6-2-2c-1 1-2 1-3 1h0c-2 1-3 2-4 4h-1c0 1 0 1 1 2 0 2 0 2-1 4l-1-1-1 1c-2-4-5-7-8-10-1-1-2-1-2-2v-2h0 0l3-3 6-7h1 2c0-2 2-3 2-4v-1-2l3-6v-1c1-1 0-1 0-2h1l2-1z" class="H"></path><path d="M535 654l1-1c1 0 2 0 3 1s2 3 4 3 4-1 5-2l1 1c-1 2-3 3-5 4h-4c-2-2-4-3-5-5v-1z" class="C"></path><path d="M545 644c1 1 3 1 4 3 0 1 0 1-1 2-1 2 0 4-2 5-1 0-3 1-4 0-2 0-3-4-3-5l1-1h0c0-2 1-3 2-3h1l2-1z" class="k"></path><path d="M541 642c2-1 4-1 6-1 1 3 4 5 5 7 1 3 1 5 1 8-2 2-5 7-9 7h-4l1-1-1-2h4c2-1 4-2 5-4l-1-1h0s1-1 1-2c1-2 0-3-1-4 1-1 1-1 1-2-1-2-3-2-4-3h-1c-2 0-3-1-5-1l2-1z" class="G"></path><path d="M553 656c1-1 2-2 4-3v4c-3 3-6 8-10 10-3-1-8-1-11-1-1-1-1-1-2-1-2-1-3-1-4-2 0-2 0-2 2-3l-1-1h2c0-2 2-3 2-4 1 2 3 3 5 5l1 2-1 1h4c4 0 7-5 9-7z" class="E"></path><path d="M535 655c1 2 3 3 5 5l1 2-1 1c-3-1-5-1-8-3l-1-1h2c0-2 2-3 2-4z" class="O"></path><path d="M530 659h1l1 1c-2 1-2 1-2 3 1 1 2 1 4 2 1 0 1 0 2 1 3 0 8 0 11 1v1h-1l-3 6-2-2c-1 1-2 1-3 1h0c-2 1-3 2-4 4h-1c0 1 0 1 1 2 0 2 0 2-1 4l-1-1-1 1c-2-4-5-7-8-10-1-1-2-1-2-2v-2h0 0l3-3 6-7z" class="R"></path><path d="M521 669c2 0 5 2 6 4h-4c-1-1-2-1-2-2v-2z" class="AE"></path><path d="M527 673c1 2 3 3 4 5h1c0 1 1 3 0 4l-1 1c-2-4-5-7-8-10h4z" class="t"></path><path d="M530 659h1l1 1c-2 1-2 1-2 3 0 1 0 2 1 3-1 0-1 1-3 1v3l-1 1c0-1-1-1-1-1v-2c0-1-2-1-2-2l6-7z" class="K"></path><path d="M533 677h-1c0-2 0-2 1-3v-2-1h-1c-1-2 1-2 1-4v-1h3c3 0 8 0 11 1v1h-1l-3 6-2-2c-1 1-2 1-3 1h0c-2 1-3 2-4 4h-1z" class="G"></path><path d="M541 672v-2c1-2 3-2 5-2l-3 6-2-2z" class="h"></path><path d="M785 201c5-2 8-1 12 1h1l-1-1h7v1c9 2 16 8 25 7 3-1 4-2 6-4l1-1v1h3 1 0l2-1 3 1c2 1 8 4 10 3s3-1 5-2c1 1 3 2 4 2v1l3 1c1 0 1 1 2 1v1h3 4l9 2c1 1 1 1 3 2l1 1 1-2c1 1 3 2 3 3s1 2 1 2v1l-3-1 4 4c0 1 1 2 2 3h-4c-5-3-10-5-16-6l1 1h0l-1 1v1h-1c-1 0-2-1-4 0l-2 4h0v2l-2 1h0-3-1c-1-1-2-2-3-2 1 1 0 1 1 2h-2l1 1v1c1 2 1 5 1 7 0 1-1 2-2 3l-1 4h0v3l-2 6-3 7-1 3h-1-1c-2-1-3-1-4-3-1 0-2-2-3-3v2l-1 1v-4h-1c-1 2-1 4-1 7v7c-1 2-1 3 0 4l-1 2 4 8-4 1c-3-2-6-4-10-5l-7-2-3-1c1-2 0-2-1-3h1c0-2 0-3 1-5h0c-2-2-3-4-5-6 0-1-2-3-2-3 0-2 1-2 0-3-1 2-2 5-5 6h0-1c-3-1-6-1-9-1-3-1-5-1-8 0h-8c-3 1-8 0-11 0-2-1-3-3-4-4l-1-1h-1 0v-4h0v-1h0c-1-1-3-2-5-2h0l-1-1c2-1 3-2 4-3h0c-2 0-3 1-5 3l-3 1 2-2c-1-1-1-1-1-2 1-1 2-1 3-2 0-1 1-1 2-2l12-2v-1l2 1c2-2 4-2 6-5 2 0 3-2 5-3 2 1 3 2 5 3l3-2-3-2c-1-1-2-2-4-3-1 0-2 0-3-1h-3v2c-3 1-5 0-8 1v1h-1-2c-3 0-4-1-7-2l-1-2h1c2-1 3-3 5-4s2 1 5 1h0c0-1-1-1-1-2-2-2-2-3-2-7v-1-4h0c1-3 4-5 7-7l1-2c2 0 3 0 4-1 0-1 1-1 2-2z" class="W"></path><path d="M832 237c1-1 3-2 5-3h1c0 2-1 4-2 5v1 1h-3l-1-1h1v-1-1l-1-1z" class="F"></path><path d="M835 225c3-2 7-4 11-3h1l-6 6 1-2c-1-1 0 0-1 0-2 0-4-1-6-1z" class="Q"></path><path d="M784 222c0-1 0-2 1-4 3 0 6 2 10 3 1 1 3 1 5 2-2 1-4 0-6 0-4 0-6 0-10-1z" class="G"></path><path d="M869 217c5-1 11-1 16 1h-4l1 1h-3c-2-1-4 1-6 1l-6 1c1-1 1-1 1-2l1-2h0z" class="AF"></path><path d="M781 226h-2c-1-1-1-1-2-1h-3l-1-2 1-1 1-1v2h1c2-1 5 1 7 1 1-1 1-1 1-2 4 1 6 1 10 1 2 0 4 1 6 0l1 1c-4 0-8 0-11 2-2 0-3 0-5 1-1 0-2-1-4-1z" class="R"></path><path d="M885 218c2 0 4 1 6 2l4 4c0 1 1 2 2 3h-4c-5-3-10-5-16-6-1-1-3-1-4-1 2 0 4-2 6-1h3l-1-1h4z" class="q"></path><path d="M843 234c9-9 14-15 26-17h0l-1 2c0 1 0 1-1 2-7 2-12 5-17 11h-3c-1 0-2 3-4 3v-1z" class="u"></path><defs><linearGradient id="Ab" x1="793.854" y1="209.551" x2="777.788" y2="214.214" xlink:href="#B"><stop offset="0" stop-color="#373a3f"></stop><stop offset="1" stop-color="#54565b"></stop></linearGradient></defs><path fill="url(#Ab)" d="M782 207c4 0 9 2 12 5 2 1 3 3 3 5l-3-2c-3-1-5-2-8-2-4-1-8-1-11-1 2-2 4-4 7-5z"></path><path d="M842 204l3 1c2 1 8 4 10 3s3-1 5-2c1 1 3 2 4 2v1l3 1c1 0 1 1 2 1v1h3 4c-6 0-12 1-18 2-2 0-6 1-8 0l-3-3-1-1-6-5h0l2-1z" class="K"></path><path d="M860 206c1 1 3 2 4 2v1h-5l-4-1c2-1 3-1 5-2z" class="J"></path><path d="M842 204l3 1c2 1 8 4 10 3l4 1h-5l-1 1h-5-2l-6-5h0l2-1z" class="O"></path><path d="M771 217c2-1 4-3 6-3l1 1v1c-1 1-1 3-2 4l-1 1-1 1-1 1 1 2h3c1 0 1 0 2 1h2l-3 1-1 2c2 1 3 1 5 1v2c-3 1-5 0-8 1v1h-1-2c-3 0-4-1-7-2l-1-2h1c2-1 3-3 5-4s2 1 5 1h0c0-1-1-1-1-2-2-2-2-3-2-7v-1z" class="D"></path><g class="Q"><path d="M764 230c2-1 3-3 5-4 0 1 1 2 2 3 2 1 5 1 7 1l-1 1c-1 0-5 1-6 0-1 0-1-1-1-1h-5-1z"></path><path d="M801 224l1 1c2 0 5-1 7 0v1h-2c-4 3-8 7-12 9v1l-3-2c-1-1-2-2-4-3-1 0-2 0-3-1h-3c-2 0-3 0-5-1l1-2 3-1c2 0 3 1 4 1 2-1 3-1 5-1 3-2 7-2 11-2z"></path></g><path d="M801 224l1 1c0 1-1 1-2 2-3 0-7-1-10-1 3-2 7-2 11-2z" class="D"></path><path d="M781 226c2 0 3 1 4 1 4 1 8 3 11 2 1 1 1 1 0 2v2c0 1 0 1-1 2v1l-3-2c-1-1-2-2-4-3-1 0-2 0-3-1h-3c-2 0-3 0-5-1l1-2 3-1z" class="S"></path><path d="M873 220c1 0 3 0 4 1l1 1h0l-1 1v1h-1c-1 0-2-1-4 0l-2 4h0v2l-2 1h0-3-1c-1-1-2-2-3-2 1 1 0 1 1 2h-2l1 1v1l-2 1c-1-1-1-1-3-2h-1c0 2 1 3 1 4-2 0-3 1-4 1l-1-1h-1l-4 1 4-5c5-6 10-9 17-11l6-1z" class="R"></path><path d="M878 222h0l-1 1v1h-1c-1 0-2-1-4 0h-4l2-2h8z" class="N"></path><path d="M868 224h4l-2 4h0v2l-2 1h0-3-1c-1-1-2-2-3-2 1 1 0 1 1 2h-2l1 1v1l-2 1c-1-1-1-1-3-2h-1c0 2 1 3 1 4-2 0-3 1-4 1 0-1 0-2 1-3l1 1h0c0-1-1-1-1-2 2-2 3-3 5-4 2 0 4-2 5-3 1 1 2 3 4 4-1-2-2-3-2-5l3-1z" class="M"></path><path d="M785 201c5-2 8-1 12 1h1l-1-1h7v1c9 2 16 8 25 7 3-1 4-2 6-4l1-1v1h3 1l6 5 1 1c-1 1-1 2-2 2-9 7-21 8-32 8l-11-10c-2 0-3-2-5-3s-5-2-7-3l-7-2c0-1 1-1 2-2z" class="d"></path><path d="M806 207l5 3h0c1 2 4 2 4 4 1 1 1 2 2 3 2 0 3 0 4 1h4 0c-2 1-4 1-5 0-2 0-5 1-7-1h1 1l1-1-6-3c-2-1-2-2-3-4h1c-2-1-2-1-2-2z" class="L"></path><path d="M811 210l8 3c3 0 5 1 8 0l2 1c3 1 7-1 10-2h0 2l1 1h-3-1c-1 1-1 1-2 1-2 0-3 2-4 2h-1c-2 1-3 1-4 1s-1 0-2 1h0-4c-1-1-2-1-4-1-1-1-1-2-2-3 0-2-3-2-4-4z" class="n"></path><path d="M785 201c5-2 8-1 12 1h1c1 1 7 3 8 5 0 1 0 1 2 2h-1c-2 0-5-2-7-2 2 2 4 5 7 6 2 2 5 5 8 6l1 1h0c-2 0-3 0-4-1l-10-8h0c-2 0-3-2-5-3s-5-2-7-3l-7-2c0-1 1-1 2-2z" class="f"></path><path d="M785 201c5-2 8-1 12 1h0-4l-1 1h0l2 2h0c-1 0-2-1-4-1v1l-7-2c0-1 1-1 2-2z" class="i"></path><path d="M798 202l-1-1h7v1c9 2 16 8 25 7 3-1 4-2 6-4l1-1v1h3 1l6 5 1 1c-1 1-1 2-2 2-1-1-3-1-3-2-1-1-2-1-2-2-2 0-4 1-6 2s-5 2-7 2c-3 1-5 0-8 0l-8-3h0l-5-3c-1-2-7-4-8-5z" class="I"></path><path d="M798 202l-1-1h7v1 1l-1-1h-3c1 1 3 1 4 2l1-1c1 1 2 2 2 3h1c2 0 5 3 7 4h1l3 3-8-3h0l-5-3c-1-2-7-4-8-5z" class="F"></path><path d="M843 235c2 0 3-3 4-3h3l-4 5 4-1h1l1 1c1 0 2-1 4-1 0-1-1-2-1-4h1c2 1 2 1 3 2l2-1c1 2 1 5 1 7 0 1-1 2-2 3l-1 4h0v3l-2 6-3 7-1 3h-1-1c-2-1-3-1-4-3-1 0-2-2-3-3v2l-1 1v-4h-1c-1 2-1 4-1 7v7c-1 2-1 3 0 4l-1 2 4 8-4 1c-3-2-6-4-10-5l-7-2-3-1c1-2 0-2-1-3h1c0-2 0-3 1-5h0c-2-2-3-4-5-6 0-1-2-3-2-3 0-2 1-2 0-3v-1h-1c-1-2-1-4-1-6 1 0 2 0 3-1h0c3-5 7-10 12-12 1-1 2-1 3-1v-1l2-1 1 1v1 1h-1l1 1h3c1-1 2-1 3-1l1-1c0-1 0-1 1-2 0 0 0-1 1-1 0-1 0-1 1-2v1z" class="Z"></path><path d="M815 252c1 1 2 2 4 3-1 1-2 4-3 4v-2h-1l-1 2h-1c-1-2-1-4-1-6 1 0 2 0 3-1h0z" class="S"></path><path d="M828 261c2-2 4-4 7-5h1l-1 2c-1 2-2 3-3 5-2 2-4 4-4 7-1-1-2-4-2-6l2-3z" class="g"></path><defs><linearGradient id="Ac" x1="818.417" y1="268.538" x2="827.57" y2="265.231" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#2e2f31"></stop></linearGradient></defs><path fill="url(#Ac)" d="M816 266c2-1 3-1 4-2l1 2h0c1-3 2-6 4-9 2-2 4-3 6-4 2 0 4-1 5 0l-1 1c-1 0-3 1-4 2h0l-2 2c-1 1-1 1-1 3l-2 3c0 2 1 5 2 6v5 1l-2-3-1 1c-2-1-2 0-3 0l-1-2h0c-2-2-3-4-5-6z"></path><path d="M826 264c0 2 1 5 2 6v5 1l-2-3c-1-3-1-5 0-9z" class="k"></path><path d="M832 237l1 1v1c-2 0-3 0-4 2-1 1-1 2-1 3l1 1c2 1 3 3 5 6-3-1-6-1-8-1l-1 1c-3 0-5 2-6 4-2-1-3-2-4-3 3-5 7-10 12-12 1-1 2-1 3-1v-1l2-1z" class="b"></path><path d="M835 258l1 1c1 5 1 11 3 16 0 1 1 2 1 4l4 8-4 1c-3-2-6-4-10-5l-7-2-3-1c1-2 0-2-1-3h1c0-2 0-3 1-5l1 2c1 0 1-1 3 0l1-1 2 3v-1-5c0-3 2-5 4-7 1-2 2-3 3-5z" class="W"></path><path d="M822 274c1 0 1-1 3 0l1-1 2 3v1l-2 1-1 1h-1c-1-2-1-3-2-5z" class="C"></path><path d="M825 274l1-1 2 3v1l-2 1c-1-2-1-3-1-4z" class="I"></path><path d="M832 263v1l2-2h0c0 1-1 2-1 3-2 5 1 14 3 19-3-1-6-3-7-6l-1-1v-1-1-5c0-3 2-5 4-7z" class="H"></path><defs><linearGradient id="Ad" x1="847.449" y1="251.522" x2="859.069" y2="246.199" xlink:href="#B"><stop offset="0" stop-color="#969697"></stop><stop offset="1" stop-color="#c6c5c6"></stop></linearGradient></defs><path fill="url(#Ad)" d="M843 235c2 0 3-3 4-3h3l-4 5 4-1h1l1 1c1 0 2-1 4-1 0-1-1-2-1-4h1c2 1 2 1 3 2l2-1c1 2 1 5 1 7 0 1-1 2-2 3l-1 4h0v3l-2 6-3 7-1 3h-1-1c-2-1-3-1-4-3-1 0-2-2-3-3v2l-1 1v-4h-1c-1 2-1 4-1 7v7c-1 2-1 3 0 4l-1 2c0-2-1-3-1-4-2-5-2-11-3-16 1-7 2-19 7-24z"></path><path d="M850 248l3-5c0 4 0 7-2 10 0-1 0-1-1-2v-1-2z" class="L"></path><path d="M851 236l1 1h0l2 2-1 4-3 5-1-2c0-1 0-2 1-2v-2l2-2-1-1-2 1 2-4z" class="U"></path><path d="M856 236c0-1-1-2-1-4h1c2 1 2 1 3 2l2-1c1 2 1 5 1 7 0 1-1 2-2 3v1c-1 0-1 1-2 1l-1-1v-2c-1-2 0-4-1-6z" class="l"></path><path d="M856 236c0-1-1-2-1-4h1c2 1 2 1 3 2l-1 4v6h-1v-2c-1-2 0-4-1-6z" class="n"></path><path d="M849 240l2-1 1 1-2 2v2c-1 0-1 1-1 2l1 2v2l-1 1c-1 3-1 5 0 8l2 2c1-1 2-1 3-2l1-1c0-1 1-1 2-2l-3 7-1 3h-1-1c-2-1-3-1-4-3-1 0-2-2-3-3-1-3-1-5 0-8 1-2 1-4 2-6h2v-1l-1-1c0-1 1-3 2-4z" class="e"></path><path d="M854 263h-3c-2 0-3-2-4-3-2-2-2-4-2-6l1-1c1 0 2-1 3-2-1 3-1 5 0 8l2 2c1-1 2-1 3-2l1-1c0-1 1-1 2-2l-3 7z" class="i"></path><defs><linearGradient id="Ae" x1="839.385" y1="250.913" x2="847.108" y2="254.441" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#2d3337"></stop></linearGradient></defs><path fill="url(#Ae)" d="M843 235c2 0 3-3 4-3h3l-4 5 4-1h1l-2 4c-1 1-2 3-2 4l1 1v1h-2c-1 2-1 4-2 6-1 3-1 5 0 8v2l-1 1v-4h-1c-1 2-1 4-1 7v7c-1 2-1 3 0 4l-1 2c0-2-1-3-1-4-2-5-2-11-3-16 1-7 2-19 7-24z"></path><path d="M843 235c2 0 3-3 4-3h3l-4 5c-6 8-7 16-7 27v8 3c-2-5-2-11-3-16 1-7 2-19 7-24z" class="x"></path><path d="M809 225c4-1 8 0 12 0 1 0 4-1 5 0 1 0 1 0 1 1h0c-1 2-1 2-1 3 3-1 6-3 9-4 2 0 4 1 6 1 1 0 0-1 1 0l-1 2-8 7c-1 1-2 2-3 2v1 1c-1 0-2 0-3 1-5 2-9 7-12 12h0c-1 1-2 1-3 1 0 2 0 4 1 6h1v1c-1 2-2 5-5 6h0-1c-3-1-6-1-9-1-3-1-5-1-8 0h-8c-3 1-8 0-11 0-2-1-3-3-4-4l-1-1h-1 0v-4h0v-1h0c-1-1-3-2-5-2h0l-1-1c2-1 3-2 4-3h0c-2 0-3 1-5 3l-3 1 2-2c-1-1-1-1-1-2 1-1 2-1 3-2 0-1 1-1 2-2l12-2v-1l2 1c2-2 4-2 6-5 2 0 3-2 5-3 2 1 3 2 5 3l3-2v-1c4-2 8-6 12-9h2v-1h0z" class="N"></path><path d="M807 244c1 0 2 1 3 2l-1 2c-1 0-2 1-3 1l-1-2c0-2 1-2 2-3z" class="J"></path><path d="M809 225h0c3 0 7 1 9 1h5 0c1 1 1 1 2 1l-1 1-1 1c-1 1-2 1-3 2l-1-1v1c-1 1-3 1-4 2h-2c1-3 6-3 7-5l-1-1h-4-3-1c-1 0-1 0-2-1h0v-1z" class="H"></path><path d="M813 233h2c1-1 3-1 4-2v-1l1 1-8 7c-2 2-8 5-9 8l2 4h-1l-2-2c-1-2-5-2-7-2l-1-1c2 0 3 0 5-2h2l4-4 1-1 6-6 1 1z" class="b"></path><path d="M809 226h0c1 1 1 1 2 1h1 3 4l1 1c-1 2-6 2-7 5l-1-1-6 6c-1-1-4 0-5 0l-6 3-1-1h-3v-1l1-1 3-2v-1c4-2 8-6 12-9h2z" class="V"></path><path d="M801 238c2-2 4-3 7-4l4-4c1 0 1-1 2 0l-2 2-6 6c-1-1-4 0-5 0z" class="S"></path><path d="M807 226c-1 5-7 9-10 11-1 1-2 1-4 1l1 2h-3v-1l1-1 3-2v-1c4-2 8-6 12-9z" class="U"></path><path d="M835 225c2 0 4 1 6 1 1 0 0-1 1 0l-1 2-8 7c-1 1-2 2-3 2l-7 3c-1 0-2 0-3 1l-8 10c0 1 0 1-1 2-2-1-4-3-5-4 1 0 2-1 3-1l1-2c-1-1-2-2-3-2 5-2 8-7 11-10 2-1 6-2 8-5 3-1 6-3 9-4z" class="H"></path><path d="M810 246l1 1v-1-1l1-2h1l-1 2v2l-1 1c0 1-1 0 0 1 0 1 1 1 1 2s0 1-1 2c-2-1-4-3-5-4 1 0 2-1 3-1l1-2z" class="V"></path><path d="M823 240h-1c3-2 4-6 8-6 1 0 2 1 3 1-1 1-2 2-3 2l-7 3z" class="J"></path><path d="M787 235c2 1 3 2 5 3l-1 1v1h3l1 1 6-3c1 0 4-1 5 0l-1 1-4 4h-2c-2 2-3 2-5 2l1 1c2 0 6 0 7 2l2 2h1c1 1 4 2 5 4-2 1-4 1-6 0h-1c-1 0-3-1-4-1-3-1-5-1-8-2l-4 1h1c-3 1-5 1-8 0h-2c-2-1-4-2-6-4v-1h2l-1-1c0-2 2-2 3-3 2-2 4-2 6-5 2 0 3-2 5-3z" class="h"></path><path d="M792 245c2-1 5-4 7-4 2-1 1 0 2-1s3-1 4-1l-4 4h-2c-2 2-3 2-5 2h0-2z" class="K"></path><path d="M787 235c2 1 3 2 5 3l-1 1v1h3l1 1c-2 1-3 1-5 2-6 2-10 5-16 4l-1-1c0-2 2-2 3-3 2-2 4-2 6-5 2 0 3-2 5-3z" class="H"></path><path d="M792 245h2 0l1 1c2 0 6 0 7 2l2 2h1c1 1 4 2 5 4-2 1-4 1-6 0h-1c-1 0-3-1-4-1-3-1-5-1-8-2l-11-1c4-3 7-4 12-5z" class="S"></path><path d="M774 242l2 1c-1 1-3 1-3 3l1 1h-2v1c2 2 4 3 6 4h2c3 1 5 1 8 0h-1l4-1c3 1 5 1 8 2 1 0 3 1 4 1 2 2 6 4 6 6 1 2 0 3-1 4v1l1 1h0-1c-3-1-6-1-9-1-3-1-5-1-8 0h-8c-3 1-8 0-11 0-2-1-3-3-4-4l-1-1h-1 0v-4h0v-1h0c-1-1-3-2-5-2h0l-1-1c2-1 3-2 4-3h0c-2 0-3 1-5 3l-3 1 2-2c-1-1-1-1-1-2 1-1 2-1 3-2 0-1 1-1 2-2l12-2v-1z" class="Z"></path><path d="M791 251c3 1 5 1 8 2 1 0 3 1 4 1 2 2 6 4 6 6 1 2 0 3-1 4h-1-1l-3-5c-3-2-9-4-13-4h-3-1l-6-2v-1c3 1 5 1 8 0h-1l4-1z" class="C"></path><path d="M780 252c3 1 5 1 8 0h0c1 1 2 1 3 1-2 1-4 1-5 2l-6-2v-1z" class="F"></path><path d="M791 251c3 1 5 1 8 2 1 0 3 1 4 1 2 2 6 4 6 6 1 2 0 3-1 4h-1c-1-2-1-5-3-7 0-1-3-2-5-3h0c-4-1-7-1-11-2h0-1l4-1z" class="W"></path><path d="M774 242l2 1c-1 1-3 1-3 3l1 1h-2v1c2 2 4 3 6 4h2v1l-8-2h-5v1h0l2 1 21 6c-4 1-15 4-19 1l-2-1-3-3h0v-1h0c-1-1-3-2-5-2h0l-1-1c2-1 3-2 4-3h0c-2 0-3 1-5 3l-3 1 2-2c-1-1-1-1-1-2 1-1 2-1 3-2 0-1 1-1 2-2l12-2v-1z" class="K"></path><path d="M766 256h1c2 1 3 1 5 0v3c-1 0-1-1-2-1l-1 1-3-3h0z" class="R"></path><path d="M772 248c2 2 4 3 6 4h2v1l-8-2c-2-1-3-1-5-1l1-1c1 0 3 0 4-1zm2-6l2 1c-1 1-3 1-3 3l1 1h-2-1c-2 0-2 0-4-1-2 0-4 1-6 2l-3 3c-1-1-1-1-1-2 1-1 2-1 3-2 0-1 1-1 2-2l12-2v-1z" class="O"></path><path d="M774 242l2 1c-1 1-3 1-3 3l1 1h-2-1c-2 0-2 0-4-1h3c2-1 2-2 4-3v-1z" class="R"></path><path d="M677 235v2s-1 0-2 1l3 1h0l-2 2-1 2c0 3-1 6-1 9l1 1c-1 0-3 1-4 1v1c7 0 15 0 21 4h1c-1-1-1-2-2-3 0-2 1-3 2-4 0 0 1 0 2 1 1 0 1-2 2-1 2 1 3 0 5 1l1-1h1l3-3h1c1 2 0 4 0 5s1 2 1 2c1 2 1 2 1 4v1h1v2h2c1 1 1 2 2 4v6c1 3 2 5 2 8v1 2c-1 3-1 5-2 8 1 1 1 3 1 5s0 4 1 6l1 4c0 1 0 1 1 2-3 4-6 8-10 11h0c-1 1-2 2-2 3l-2 1c0-1 0-2-1-3-1-2-3-2-5-3l-2 1c-2 7-5 15-8 22-1 3-2 7-4 10l-5 12-1-1h-6c-2 1-2 3-4 4l-1 3-1-3-1-1c-2-1-4-1-6-2-3 0-5 0-7 1h-3l-2 2-2 1c-1 3-3 5-5 7l-2-1c1-1 2-3 3-4l-1-2-1-1c1-1 1-2 2-3l1-1-2-2 1-1c1-1 1-2 1-2v-2c1-1 1-2 1-3v-1l1 1 1-2 1-3c-1-1-2-1-3-1 0-1-1-3-2-5 0-1 0-2-1-3l-1-1c0-1 2-1 2-3 0 0-1 0-2-1l1-1c2 1 2 1 4 1h1l3-3h-1c1-1 3-1 5-2l-2-1v-2l-1-1-6 1h-5l1-3c5-13 11-25 16-38 3-9 6-18 5-27 0-3-2-6-3-9 0-2 2-4 3-6l3-4 2-2 1-1c3 0 7 1 10 0z" class="I"></path><path d="M666 236h0c2 1 4 1 6 2h-8l2-2z" class="J"></path><path d="M687 287l1-2h1l-2-2 1-1 2-1c-1 1-1 1 0 2s3 2 4 4c-2 0-5 1-7 0z" class="D"></path><path d="M677 235v2s-1 0-2 1h-3c-2-1-4-1-6-2h0l1-1c3 0 7 1 10 0z" class="V"></path><path d="M671 252v-6c1-2 2-5 4-6v3c0 3-1 6-1 9l1 1c-1 0-3 1-4 1v-2z" class="B"></path><path d="M691 262c1 0 2 0 3 1h1 1c2 2 4 4 5 7 1 1 1 2 2 3l1 2h0l1 2-3 2c-1 0-1 0-1-1-3-7-5-11-10-16z" class="P"></path><path d="M704 275l-2 2-1-2 2-2 1 2z" class="Y"></path><path d="M671 255c7 0 15 0 21 4 1 2 3 2 4 4h-1-1c-1-1-2-1-3-1s-8-4-9-4c-5-2-13-2-17 1h-2l-1-1c3-2 6-2 9-3z" class="e"></path><path d="M658 291c1 3 2 5 2 9v1l-1 1 1 1-3 2c-2 1-3 2-5 2v-1l1-1-1-1c2-4 3-9 6-13z" class="B"></path><path d="M659 302c-1-1-1-1-2-1l-1-1c1 0 1-1 2-1v-1h-1l-1-1h3 0l1 3v1l-1 1z" class="O"></path><path d="M660 303l1 1c1 3 0 5 0 8h-2c-1 1-7 1-8 1v-2l-2-2c0-1 2-4 3-5l1 1-1 1v1c2 0 3-1 5-2l3-2z" class="H"></path><path d="M662 258v-2c1-3 0-6 0-9 1-2 2-4 4-5 1-1 4-1 5-1 0 2 0 4-1 6v2c0 1 0 2 1 3v2 1c-3 1-6 1-9 3z" class="K"></path><path d="M668 249l1-4h1v2 2h-1v-1l-1 1z" class="B"></path><path d="M668 249l1-1v1c0 2 1 4 0 5-1-1-1-4-1-5z" class="H"></path><path d="M704 275l1 1-1-4v-1c1 1 3 4 4 5l-1 1 1 1c2 0 3 0 4 1h-1-1c-1 0-2 0-3 1-3 1-5 3-7 5v4c0 1 0 1-1 2h1c0 1 0 1 1 2h0-3l-4-1v-1-4c-1-2-3-3-4-4s-1-1 0-2c2 1 5 1 7 1 2-1 3-2 4-4 0 1 0 1 1 1l3-2-1-2z" class="f"></path><path d="M698 283l1 1c-1 1-1 1-2 3 1 0 1 1 2 2h0l-1 1c-2 0-2 0-3-1 1-1 1-2 1-3h1c0-1 0-2 1-3z" class="L"></path><path d="M713 263c1 1 1 2 2 4v6c1 3 2 5 2 8v1 2c-1 3-1 5-2 8 0-3 1-5 0-7 0-1-3-4-4-5h2l-1-1c-1-1-2-1-4-1l-1-1 1-1c-1-1-3-4-4-5v1l1 4-1-1h0l-1-2c-1-1-1-2-2-3h3l-2-2h5c2 0 3-2 4-3h1 0c0 1 0 1 1 2v-4z" class="O"></path><path d="M711 265h1 0c0 1 0 1 1 2l1 7v3l1 2h-1c-2-1-6-3-8-6 0-1-1-2-2-3h0l-2-2h5c2 0 3-2 4-3z" class="T"></path><path d="M714 277c-1-1-4-2-5-3 0-1 1-1 1-1 2-1 2 0 4 1v3z" class="P"></path><path d="M649 309l2 2v2c1 0 7 0 8-1h2c0 2 0 5-1 7 0 1 0 4-1 5-2 1-5 2-6 4l-2-1v-2l-1-1-6 1h-5l1-3c2 0 3-1 4-2 2-3 4-7 5-11z" class="e"></path><path d="M644 320l8-1c-1 1-1 1-3 1v1c-1 0-1 1-2 1s-3 0-5 1h0l-1 1c1 1 1 1 3 1h-5l1-3c2 0 3-1 4-2z" class="V"></path><path d="M650 324c3 0 6-1 8-2 1-1 1-2 2-3 0 1 0 4-1 5-2 1-5 2-6 4l-2-1v-2l-1-1z" class="K"></path><path d="M703 252h1l3-3h1c1 2 0 4 0 5s1 2 1 2c1 2 1 2 1 4v1h1v2h2v4c-1-1-1-1-1-2h0-1c-1 1-2 3-4 3h-5l2 2h-3c-1-3-3-5-5-7-1-2-3-2-4-4h1c-1-1-1-2-2-3 0-2 1-3 2-4 0 0 1 0 2 1 1 0 1-2 2-1 2 1 3 0 5 1l1-1z" class="g"></path><path d="M701 259h2 0l-1 1s0 1-1 2c1 1 1 1 3 2h1l1-1v-1l-1 1c-1-1-1-1-2-1 1-2 1-3 2-3 2 0 3 0 4 2 1 1 1 2 2 4-1 1-2 3-4 3h-5v-1l-2-2c-1-2 0-4 1-6z" class="C"></path><path d="M693 252s1 0 2 1c1 0 1-2 2-1 2 1 3 0 5 1h0v1l-2 2c-1 1-1 1-1 3h2c-1 2-2 4-1 6l2 2v1l2 2h-3c-1-3-3-5-5-7-1-2-3-2-4-4h1c-1-1-1-2-2-3 0-2 1-3 2-4z" class="H"></path><path d="M693 252s1 0 2 1c1 0 1-2 2-1 2 1 3 0 5 1h0l-2 2c-1 1-3 2-4 3v2l-3-1c-1-1-1-2-2-3 0-2 1-3 2-4z" class="M"></path><path d="M696 258c-1 0-1 0-1-1 0-2 1-3 2-4 1 0 2 1 3 2-1 1-3 2-4 3z" class="U"></path><path d="M712 279l1 1h-2c1 1 4 4 4 5 1 2 0 4 0 7 1 1 1 3 1 5s0 4 1 6l1 4c0 1 0 1 1 2-3 4-6 8-10 11h0c-1 1-2 2-2 3l-2 1c0-1 0-2-1-3-1-2-3-2-5-3h0l-1-1c0-2 1-3 2-5v-4-1c0-3-2-8-4-9h0c-2-2-2-2-4-3l1-1c1 0 3 0 5-1h0 3 0c-1-1-1-1-1-2h-1c1-1 1-1 1-2v-4c2-2 4-4 7-5 1-1 2-1 3-1h1 1z" class="M"></path><path d="M701 289h1c1 1 1 0 2 1 1-1 1-1 3 0h1l1 1v1h-1-1c0 3 2 7 3 11v6c-2-2-3-6-4-8 0-3-2-5-3-8 0-2-1-3-2-4z" class="L"></path><path d="M707 306v-2c-1-1-1-2-1-3 1 2 2 6 4 8l1 4h1c1-1 2-1 2-2 2-2 3-2 4-4 0 1 0 1 1 2-3 4-6 8-10 11v-1l-1-6c0-2-1-5-1-7z" class="H"></path><path d="M712 279l1 1h-2c1 1 4 4 4 5 1 2 0 4 0 7 1 1 1 3 1 5h-1-1c0 2 1 4 1 6v5c-1-2-2-5-2-7 0-1 0-2-1-2s-1 1-2 1v-3h2l-1-1-1-1c-1-1-2-2-2-3h1v-1l-1-1h-1c-2-1-2-1-3 0-1-1-1 0-2-1h-1v-1l-1 1v-4c2-2 4-4 7-5 1-1 2-1 3-1h1 1z" class="T"></path><path d="M701 288c0-1 0-2 1-3 1-2 4-3 6-3 1 0 2 0 3 1l-1 1-1-1c-1 1-1 2-1 3l1 2v1l-2-2v1l1 2h-1c-2-1-2-1-3 0-1-1-1 0-2-1h-1v-1z" class="P"></path><path d="M712 279l1 1h-2c1 1 4 4 4 5 1 2 0 4 0 7 1 1 1 3 1 5h-1-1c0-4 0-10-3-14-1-1-2-1-3-1-2 0-5 1-6 3-1 1-1 2-1 3l-1 1v-4c2-2 4-4 7-5 1-1 2-1 3-1h1 1z" class="k"></path><path d="M701 288v1c1 1 2 2 2 4 1 3 3 5 3 8 0 1 0 2 1 3v2c0 2 1 5 1 7l1 6v1h0c-1 1-2 2-2 3l-2 1c0-1 0-2-1-3-1-2-3-2-5-3h0l-1-1c0-2 1-3 2-5v-4-1c0-3-2-8-4-9h0c-2-2-2-2-4-3l1-1c1 0 3 0 5-1h0 3 0c-1-1-1-1-1-2h-1c1-1 1-1 1-2l1-1z" class="W"></path><path d="M703 298c1 2 2 4 2 6 1 2 1 4 1 7l-1 1-2-1c-1 2-2 4-2 6l-2 1-1-1c0-2 1-3 2-5l-1 4h1 0c0-1 1-2 1-3h0v-1c1 0 1-1 1-2 1-1 2-2 2-3 0-2-2-5-2-8l1-1z" class="E"></path><path d="M705 304c1 2 1 4 1 7l-1 1-2-1c1-2 1-5 2-7z" class="C"></path><path d="M701 288v1c1 1 2 2 2 4 1 3 3 5 3 8 0 1 0 2 1 3v2h-1v5c0-3 0-5-1-7 0-2-1-4-2-6l-5-5h0 3 0c-1-1-1-1-1-2h-1c1-1 1-1 1-2l1-1z" class="e"></path><path d="M706 311v-5h1c0 2 1 5 1 7l1 6v1h0c-1 1-2 2-2 3l-2 1c0-1 0-2-1-3-1-2-3-2-5-3h0l2-1c0-2 1-4 2-6l2 1 1-1z" class="Y"></path><path d="M708 313l1 6c-1 0-1 1-2 1h0c-1-3 0-5 1-7z" class="l"></path><path d="M703 311l2 1c-1 2-2 3-4 5 0-2 1-4 2-6z" class="D"></path><path d="M665 268v-4c5-1 9-1 14 0 4 1 10 3 13 7l1 1-1 1 1 2h-1c-2 1-2 2-3 3h1l1 1h-1c-2 0-3 1-5 3v1c0 1 0 2 1 3h0v1h1c2 1 5 0 7 0v4 1l4 1h0c-2 1-4 1-5 1l-1 1v1l-6 3c-1 0-3 1-4 2l-1 1c-3 0-5 3-7 3l-1-2c-1 1-1 2-2 3l-1 2-1 3c-1-1-1-2-1-3v-3-3l-1-1c0-3 0-6 1-9h-1c0 1-1 1-1 2 0 2-1 3-1 4-2 1-2 0-3 0-1-1-1-1-1-2-1-1-1-3-1-4l2 3 1 1c1-1 1-2 1-3-2-4-2-9-2-14l3-11z" class="S"></path><path d="M672 295h1v-1c1-2 0-2 2-3v1c0 2-1 5-2 7 0 1-1 3-1 4h0c-1-2-1-5 0-8z" class="M"></path><path d="M685 278l1-1h0v1 1c-4 4-6 10-8 15 1-2 2-3 3-5l1 1-1 3-3 4-3 3h0v-2c0-3 1-7 3-9 0 0 1 0 1-1l4-8 2-2z" class="b"></path><path d="M686 279l6-6 1 2h-1c-2 1-2 2-3 3h1l1 1h-1c-2 0-3 1-5 3v1c0 1 0 2 1 3h0v1l-2 3c0 1-2 2-3 3l1-3-1-1c-1 2-2 3-3 5 2-5 4-11 8-15z" class="F"></path><path d="M686 286h0v1l-2 3c0 1-2 2-3 3l1-3-1-1v-1l2-2h2 1z" class="B"></path><path d="M683 276c1-2 2-4 3-5l1-1h2c1 0 1 1 2 1h1l1 1-1 1-6 6v-1-1h0l-1 1c-3 2-4 4-6 6 0-1 2-4 2-5l-1-1-1-2c0-1 0 0 1-1l2 2h0l1-1z" class="J"></path><path d="M676 278h1v2c1 0 1-1 2-2h1l-3 9c0 1-1 3-2 5v-1c-2 1-1 1-2 3v1h-1v-1c1-1 0-1 0-2l-1-1c0-2 0-4 1-6 0-1 0-2 1-3v3h1c1-3 1-5 2-7z" class="e"></path><path d="M668 292h0v-5c1-3 1-10 4-12 0 2 1 4 2 6 0 0 0 1-1 1-1 1-1 2-1 3-1 2-1 4-1 6l1 1c0 1 1 1 0 2v1c-1 3-1 6 0 8-2 2-2 3-3 4 0 1 0 1-1 1v-3-3l-1-1c0-3 0-6 1-9z" class="F"></path><path d="M671 291l1 1c0 1 1 1 0 2v1c-1 3-1 6 0 8-2 2-2 3-3 4 0 1 0 1-1 1v-3c2-4 2-10 3-14z" class="f"></path><path d="M694 287v4 1l4 1h0c-2 1-4 1-5 1l-1 1v1l-6 3c-1 0-3 1-4 2l-1 1c-3 0-5 3-7 3l-1-2c3-2 5-4 8-6h-3l3-4c1-1 3-2 3-3l2-3h1c2 1 5 0 7 0z" class="b"></path><path d="M694 287v4 1l4 1h0c-2 1-4 1-5 1l-1 1v1-1h-2v-1l2-1v-1c-2 0-4-1-6 0-2 2-4 3-5 5h-3l3-4c1-1 3-2 3-3l2-3h1c2 1 5 0 7 0z" class="N"></path><path d="M694 287v4c0-1-1-2-1-2l-2 2c-1 0-2 1-3 0l-4-1 2-3h1c2 1 5 0 7 0z" class="E"></path><path d="M665 268v-4c5-1 9-1 14 0 4 1 10 3 13 7h-1c-1 0-1-1-2-1h-2l-1 1c-1 1-2 3-3 5l-1 1h0l-2-2c-1 1-1 0-1 1l1 2h-1c-1 1-1 2-2 2v-2h-1c-1 2-1 4-2 7h-1v-3c1 0 1-1 1-1-1-2-2-4-2-6-3 2-3 9-4 12v5h0-1c0 1-1 1-1 2 0 2-1 3-1 4-2 1-2 0-3 0-1-1-1-1-1-2-1-1-1-3-1-4l2 3 1 1c1-1 1-2 1-3-2-4-2-9-2-14l3-11z" class="H"></path><path d="M672 275h2c0 1 0 1 1 1 1-1 1-2 1-4l1-2c0-2-1-3 0-4 0 0 0 1 1 2-1 3-2 6-2 10-1 2-1 4-2 7h-1v-3c1 0 1-1 1-1-1-2-2-4-2-6z" class="a"></path><path d="M662 279l3-11v4h0c0 2 1 9 1 10h-1-1-1v-2l-1-1z" class="B"></path><path d="M662 279l1 1v2h1 1c1 4 2 7-1 11-2-4-2-9-2-14z" class="M"></path><path d="M678 268v-1c2 0 3 1 4 1 1 2 0 6 1 8l-1 1h0l-2-2c-1 1-1 0-1 1l1 2h-1c-1 1-1 2-2 2v-2h-1c0-4 1-7 2-10z" class="b"></path><path d="M658 291c1-2 0-4 2-7 1 1 1 3 1 4v2c0 2 1 3 1 5h0l-2-3c0 1 0 3 1 4 0 1 0 1 1 2 1 0 1 1 3 0 0-1 1-2 1-4 0-1 1-1 1-2h1c-1 3-1 6-1 9l1 1v3 3c0 1 0 2 1 3l1-3 1-2c1-1 1-2 2-3l1 2c2 0 4-3 7-3l1-1c1-1 3-2 4-2l6-3v-1c2 1 2 1 4 3h0c2 1 4 6 4 9v1 4c-1 2-2 3-2 5l1 1h0l-2 1c-2 7-5 15-8 22-1 3-2 7-4 10l-5 12-1-1h-6c-2 1-2 3-4 4l-1 3-1-3-1-1c-2-1-4-1-6-2-3 0-5 0-7 1h-3l-2 2-2 1c-1 3-3 5-5 7l-2-1c1-1 2-3 3-4l-1-2-1-1c1-1 1-2 2-3l1-1-2-2 1-1c1-1 1-2 1-2v-2c1-1 1-2 1-3v-1l1 1 1-2 1-3c-1-1-2-1-3-1 0-1-1-3-2-5 0-1 0-2-1-3l-1-1c0-1 2-1 2-3 0 0-1 0-2-1l1-1c2 1 2 1 4 1h1l3-3h-1c1-1 3-1 5-2 1-2 4-3 6-4 1-1 1-4 1-5 1-2 1-5 1-7 0-3 1-5 0-8l-1-1-1-1 1-1v-1c0-4-1-6-2-9z" class="D"></path><path d="M657 336c1-2 2-4 3-7 1 1 1 1 3 2-1 3-2 7-3 11l-1-1c-1-1-1-2-2-3v-2z" class="T"></path><path d="M660 329l4-15 1 2-2 15c-2-1-2-1-3-2z" class="P"></path><path d="M659 351c6-6 7-12 8-21v5c1 2 0 6 0 9l1 1c-1 1-2 2-2 3l-2 1h-1l-1 1-2 2-1-1z" class="L"></path><path d="M657 338c1 1 1 2 2 3l1 1c-2 5-7 7-10 11-1 2-2 3-3 4-2 3-3 6-5 8-1 1-1 1-1 2l-1-1c1-1 1-2 2-3l1-1c2-4 3-8 6-11 4-3 5-8 8-12v-1z" class="e"></path><path d="M657 330v6 2 1c-3 4-4 9-8 12-3 3-4 7-6 11l-2-2 1-1c1-1 1-2 1-2v-2c1-1 1-2 1-3v-1l1 1 1-2 1-3 1-1 5-9c2-2 3-4 4-7z" class="D"></path><path d="M658 291c1-2 0-4 2-7 1 1 1 3 1 4v2c0 2 1 3 1 5h0l-2-3c0 1 0 3 1 4 0 1 0 1 1 2 1 0 1 1 3 0 0-1 1-2 1-4 0-1 1-1 1-2h1c-1 3-1 6-1 9v8l-2 7-1-2-4 15c-1 3-2 5-3 7v-6c1-2 2-4 2-6 1-1 1-4 1-5 1-2 1-5 1-7 0-3 1-5 0-8l-1-1-1-1 1-1v-1c0-4-1-6-2-9z" class="R"></path><path d="M664 302l1 1v2h1 0l1 4-2 7-1-2v-2c1-3 0-6 0-10z" class="Y"></path><path d="M662 298c1 0 1 1 3 0 0-1 1-2 1-4 0-1 1-1 1-2h1c-1 3-1 6-1 9v8l-1-4h0-1v-2l-1-1-2-4z" class="L"></path><path d="M653 328c1-2 4-3 6-4 0 2-1 4-2 6-1 3-2 5-4 7l-5 9-1 1c-1-1-2-1-3-1 0-1-1-3-2-5 0-1 0-2-1-3l-1-1c0-1 2-1 2-3 0 0-1 0-2-1l1-1c2 1 2 1 4 1h1l3-3h-1c1-1 3-1 5-2z" class="T"></path><path d="M648 346v-2c-1-3 3-7 5-9v2l-5 9z" class="U"></path><path d="M653 328c1-2 4-3 6-4 0 2-1 4-2 6-1 3-2 5-4 7v-2-2c1-1 1-3 0-4h-1c0 1 0 2-1 3l-2 1h-1c-1 1-1 1-2 3 0 0 0 1-1 2 0-2 0-4 1-5l3-3h-1c1-1 3-1 5-2z" class="M"></path><path d="M642 369c4-6 8-12 14-16h0v-1c2-2 3-3 4-5 1-1 2-4 3-4 0 1-1 2-1 4-2 1-3 2-3 4l1 1 2-2 1-1h1l2-1v2c1-1 2-3 3-4 1 2 1 3 0 4-1 3-4 5-6 7l2 1-9 5h4c-3 0-5 0-7 1h-3l-2 2-2 1c-1 3-3 5-5 7l-2-1c1-1 2-3 3-4z" class="f"></path><path d="M669 346c1 2 1 3 0 4-1 3-4 5-6 7l2 1-9 5h4c-3 0-5 0-7 1h-3l-2 2-2 1c2-4 16-14 20-17 1-1 2-3 3-4z" class="C"></path><path d="M663 357l2 1-9 5h4c-3 0-5 0-7 1h-3c3-2 5-4 8-5l5-2zm10-54l1 2-2 2v1h2 2c2-1 3-2 6-2-3 2-3 5-4 7l-3 6h1l-1 3h1 3 1c-1 2-2 3-4 4-2 4-3 8-4 13-1 2-2 5-3 7-1 1-2 3-3 4v-2c0-1 1-2 2-3l-1-1c0-3 1-7 0-9v-5c0-4 1-10 2-15h0v-1c0-1-1-2 0-3l1-3 1-2c1-1 1-2 2-3z" class="e"></path><path d="M675 319h1l-1 3h1 3 1c-1 2-2 3-4 4l-1-1c0 1 0 1-1 1v-2c0-2 0-3 1-5z" class="D"></path><path d="M669 315l4-6c0 3 0 7-1 11-1-1-1-4-3-5h0z" class="Y"></path><path d="M669 315c2 1 2 4 3 5 0 8-1 17-4 25l-1-1c0-3 1-7 0-9v-5c0-4 1-10 2-15z" class="l"></path><path d="M692 295c2 1 2 1 4 3h0c2 1 4 6 4 9v1c0 1 0 2-1 3-1 0-2-1-2-1-3 1-7 5-8 8l-2-1h0l-2 5h0l-2-2c-1 0-1 1-2 2h-1-1-3-1l1-3h-1l3-6c1-2 1-5 4-7-3 0-4 1-6 2h-2-2v-1l2-2c2 0 4-3 7-3l1-1c1-1 3-2 4-2l6-3v-1z" class="H"></path><path d="M679 322c2-2 3-3 5-4h2l1-1-2 5h0l-2-2c-1 0-1 1-2 2h-1-1z" class="a"></path><path d="M681 302h1c1 0 2 0 2-1h1c2 2 4 3 5 6 0 1 0 2-1 3h-1v-4c-1-2-2-2-3-3-2 1-2 2-2 3h-1c-3 0-4 1-6 2h-2-2v-1l2-2c2 0 4-3 7-3z" class="Q"></path><path d="M682 306h1c0-1 0-2 2-3 1 1 2 1 3 3v4h1c-3 3-6 5-10 7l-3 2h-1l3-6c1-2 1-5 4-7z" class="R"></path><path d="M685 303c1 1 2 1 3 3v4c-2 0-1 0-2-1-1-2-1-4-1-6z" class="L"></path><path d="M692 295c2 1 2 1 4 3h0c2 1 4 6 4 9v1c0 1 0 2-1 3-1 0-2-1-2-1-3 1-7 5-8 8l-2-1 6-6-1-1-1 1v-1h0c1-3-1-7-2-9l-2-2h-1l6-3v-1z" class="Q"></path><path d="M689 318c1-3 5-7 8-8 0 0 1 1 2 1 1-1 1-2 1-3v4c-1 2-2 3-2 5l1 1h0l-2 1c-2 7-5 15-8 22-1 3-2 7-4 10l-5 12-1-1h-6c-2 1-2 3-4 4l-1 3-1-3-1-1c-2-1-4-1-6-2h-4l9-5-2-1c2-2 5-4 6-7 1-1 1-2 0-4 1-2 2-5 3-7 1-5 2-9 4-13 2-1 3-2 4-4h1c1-1 1-2 2-2l2 2h0l2-5h0l2 1z" class="R"></path><defs><linearGradient id="Af" x1="677.022" y1="351.789" x2="677.486" y2="361.38" xlink:href="#B"><stop offset="0" stop-color="#707376"></stop><stop offset="1" stop-color="#86888b"></stop></linearGradient></defs><path fill="url(#Af)" d="M676 349v3c3 0 6 0 8-1h0l1-1v1l-5 12-1-1h-6c-2 1-2 3-4 4l7-17z"></path><path d="M681 322c1 1 1 3 2 5 0 2-7 19-9 21l-1 2c-3 2-5 7-8 8l-2-1c2-2 5-4 6-7 1-1 1-2 0-4 1-2 2-5 3-7 1-5 2-9 4-13 2-1 3-2 4-4h1z" class="K"></path><defs><linearGradient id="Ag" x1="691.413" y1="309.958" x2="682.625" y2="352.857" xlink:href="#B"><stop offset="0" stop-color="#343539"></stop><stop offset="1" stop-color="#6b6d71"></stop></linearGradient></defs><path fill="url(#Ag)" d="M689 318c1-3 5-7 8-8 0 0 1 1 2 1 1-1 1-2 1-3v4c-1 2-2 3-2 5l1 1h0l-2 1c-2 7-5 15-8 22-1 3-2 7-4 10v-1l-1 1h0c-2 1-5 1-8 1v-3l9-22v-5h0l2-5h0l2 1z"></path><path d="M687 317l2 1-4 9v-5h0l2-5h0z" class="E"></path><defs><linearGradient id="Ah" x1="250.8" y1="187.384" x2="246.928" y2="222.072" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#3d4044"></stop></linearGradient></defs><path fill="url(#Ah)" d="M243 176h2l1 2h2 4c1 1 2 2 3 2h1c1 0 1-1 1-2 2-2 2 0 4-1l1 2c2 1 4 2 6 2h1c2 1 4 1 6 2l2 1c3 1 6 3 8 4 1 0 2-1 3-1s2 1 2 2h3l5 6 5 6 3 3-1 1 6 6h0l2 1 5 3h2c1 2 3 4 4 5 1 2 2 7 4 7 1 1 2 2 2 3 1 1 2 1 2 3h1c2 0 4 2 5 4h-5v1h-1c0-1 0-1-1-2l-3 1c0 2 0 3 1 4h-1c-1 0-2-1-3-1h-1 0c-1-1-2-4-2-5h1l1-1-2-2-1 1v1l-1-1-3-3 3 10h0l-2-2c0-1-1-3-3-4-2 0-2-3-4-4l-1 1h0l-13-12h-1c-3-3-8-8-12-7-4 2-8 3-11 6-1 2-2 5-4 7h-3c-1 1 0 1-1 1l-1-3-1 2h-1l-2-1h-1l-1-1c-1-1-1-2-2-3l-1-1v3 1h-2 0c-4 0-7-2-11-2-3-1-5 0-7 0-2 1-5 2-7 3-1 1-2 1-3 1l-1 1h-1s-1 1-2 1l-2-1c-1-1-2 0-4 0h0c-3-1-8-1-12-1-1 1-3 1-5 1l-2-1c-1 1-1 2 0 3l1 4h0c2 1 3 3 5 5l2 1v1l-3-1h-2l2 2c0 1 0 2-1 3h-1l-3-2-2-2c-2-1-4-2-5-4s-1-2-3-3c-3-4-6-7-10-10 0 1 0 2-1 3 1 1 6 4 6 6h-1s0-1-1-1c-1-1-3-1-4-1-2 1-4 2-5 3v-1c-2 1-3 2-3 4l1 4h0l2 4h0l-2-3-1 1c-2-3-4-5-7-7 0 2 3 3 3 5-1 0-1-1-2-1l-2-2c-2 0-3 0-5-1v2c-2 1-5 0-7 0h-6l-2 1c-1 1-2 1-3 2l-2-2v-1l1-1-2-2v-1h-2c-1 1-1 1-3 2l3-10 3-5 3-4 9-10 12-9c1 0 4-2 5-3 5-4 10-8 16-11h2l2-1h2c1 0 2 1 2 1 3-2 19-1 23-1 1 0 2-1 3-1 3 0 5 0 7 1 2 0 4 0 5 1h4c0-1 0-1 1-2h7v-1l1-1c1-1 3-2 5-3z"></path><path d="M204 188c5 1 10 1 16 2-2 1-2 1-4 1l-1 1c-1 0-6-1-7-2-1 0-1 0-1-1-1 0-3 0-3-1z" class="O"></path><path d="M269 215l6-3c1 0 1-1 2-1h2l1-1c2 0 3 1 4 2-4 2-8 3-11 6 0 0-1-2 0-2 0-1 1-1 1-2h-1l-2 3-2-2z" class="b"></path><path d="M213 181c3 0 5 0 7 1 2 0 4 0 5 1h4 4c2 0 3 1 4 1l-2 2c-8-2-17-3-25-4 1 0 2-1 3-1z" class="t"></path><path d="M258 198l3 1 2 1c1 1 3 1 4 2-1 3-3 7-7 8v1c-1-2-1-2-1-4 0-1 0-2 1-3v-1l-1-1-1-4z" class="U"></path><path d="M259 207l2-1c1 1 1 1 1 3l-2 1v1c-1-2-1-2-1-4zm-1-9l3 1 2 1h0c-1 1-1 1 0 2l1 2v1h-1-1c0-1-1-1-2-1h0v-1l-1-1-1-4z" class="T"></path><path d="M258 198l3 1c0 2 0 3-1 4l-1-1-1-4z" class="k"></path><path d="M263 210h1c2-2 4-4 5-7l9 5c-3 3-7 4-10 6-1-2-2-3-4-3l-1-1z" class="e"></path><path d="M252 197c2 0 4 1 6 1l1 4 1 1v1c-1 1-1 2-1 3 0 2 0 2 1 4h-1l-6-6c-2-2-3-3-4-5-1-1-1 0-1-1 1-1 2-2 4-2z" class="M"></path><path d="M252 197c2 0 4 1 6 1l1 4h-3c-2 0-2 1-3 0-1 0-1-1-2-2h-2c-1-1-1 0-1-1 1-1 2-2 4-2zm11 13l1 1c2 0 3 1 4 3l1 1 2 2 2-3h1c0 1-1 1-1 2-1 0 0 2 0 2-1 2-2 5-4 7h-3c-1 1 0 1-1 1l-1-3c0-2-2-7-3-9l-1-1h0c1-2 2-2 3-3z" class="i"></path><path d="M260 213c2 0 3 1 5 1v2l-1 1-2-2c0-1 0 0-1-1l-1-1z" class="U"></path><path d="M263 210l1 1c0 1 1 2 1 3h0 0c-2 0-3-1-5-1h0c1-2 2-2 3-3z" class="f"></path><path d="M264 211c2 0 3 1 4 3l1 1 2 2c0 1 0 2-1 3h-1c-1-2-3-4-4-6h0c0-1-1-2-1-3z" class="P"></path><path d="M271 217l2-3h1c0 1-1 1-1 2-1 0 0 2 0 2-1 2-2 5-4 7h-3c0-1-1-2 0-4h2 1v-1h1c1-1 1-2 1-3z" class="X"></path><path d="M227 191c2 0 4 0 6 1h7l1 1h3c1 1 3 1 4 1s4 0 5 2l-1 1c-2 0-3 1-4 2 0 1 0 0 1 1 1 2 2 3 4 5h-2l-2-1c-2-1-3-2-5-4 0-1-1-2-2-3h-2c-3 1-6 1-8 1l-8 1h1 1c1-1 2-1 4-1h3c1-1 2 0 4-2h-3v-1h-3v-1h1c-3-2-3-1-5-3z" class="G"></path><path d="M237 184l17 6h2 2c2 1 5 1 7 1l3 1c4 3 9 5 14 8v1c0 1 0 0 1 1l1 1c-1 0-3 0-4-1-3-2-8-3-12-5-10-4-21-8-33-11l2-2z" class="U"></path><defs><linearGradient id="Ai" x1="281.858" y1="204.633" x2="284.343" y2="193.238" xlink:href="#B"><stop offset="0" stop-color="#989697"></stop><stop offset="1" stop-color="#c6c8ca"></stop></linearGradient></defs><path fill="url(#Ai)" d="M266 188c10 4 19 10 28 15 1 2 1 2 3 3l1 1-1 1h-1-2c4 3 7 4 10 7 1 2 0 1 1 2s2 2 2 4c-7-8-14-13-23-18l-1-1c-1-1-1 0-1-1v-1c-5-3-10-5-14-8v-1c-1-2-1 0-3-1 0-1 1-2 1-2z"></path><defs><linearGradient id="Aj" x1="313.416" y1="217.975" x2="294.805" y2="219.25" xlink:href="#B"><stop offset="0" stop-color="#5e6065"></stop><stop offset="1" stop-color="#9d9f9f"></stop></linearGradient></defs><path fill="url(#Aj)" d="M294 203l12 9h1c1 1 3 3 4 5 1 0 1 1 1 2v1c0 3 5 7 7 10l2 3v1l-1-1-3-3 3 10h0l-2-2c0-1-1-3-3-4v-1c-2-2-3-4-4-6-1-3-3-4-4-6 0-2-1-3-2-4s0 0-1-2c-3-3-6-4-10-7h2 1l1-1-1-1c-2-1-2-1-3-3z"></path><path d="M315 233c0-2 0-4-1-5v-1l1-1c1 2 1 3 2 4l3 10h0l-2-2c0-1-1-3-3-4v-1z" class="M"></path><path d="M237 181l2-1c4-2 11 1 15 3 2 0 4 1 6 2l6 3s-1 1-1 2c2 1 2-1 3 1v1l-3-1c-2 0-5 0-7-1h-2-2l-17-6c-1 0-2-1-4-1h-4c0-1 0-1 1-2h7z" class="Y"></path><path d="M254 183c2 0 4 1 6 2-2 1-3 1-4 1h-1-2l-3-1v-1c1 1 1 1 2 1h2v-1h-1-1c1-1 1 0 2-1h0z" class="d"></path><path d="M260 185l6 3s-1 1-1 2c2 1 2-1 3 1v1l-3-1c-2-1-4-1-6-2-1-1-3-1-5-2h1l1-1h-1 1c1 0 2 0 4-1z" class="l"></path><path d="M243 176h2l1 2h2 4c1 1 2 2 3 2h1c1 0 1-1 1-2 2-2 2 0 4-1l1 2c2 1 4 2 6 2h1c2 1 4 1 6 2l2 1-1 1c2 3 4 3 6 5s3 4 5 5c1 1 1 2 1 3 7 5 13 8 19 14h-1l-12-9c-9-5-18-11-28-15l-6-3c-2-1-4-2-6-2-4-2-11-5-15-3l-2 1v-1l1-1c1-1 3-2 5-3z" class="M"></path><path d="M256 180c1 0 1-1 1-2 2-2 2 0 4-1l1 2c2 1 4 2 6 2l-1 2v1c-4-1-7-2-11-4z" class="l"></path><path d="M262 179c2 1 4 2 6 2l-1 2c-1-1-2-1-4-2l-1-2z" class="P"></path><path d="M268 181h1c2 1 4 1 6 2l2 1-1 1c2 3 4 3 6 5s3 4 5 5c1 1 1 2 1 3l-8-4c-2-4-5-5-9-8l-4-2v-1l1-2z" class="T"></path><path d="M268 181h1v1c1 1 2 2 4 3v1h-2l-4-2v-1l1-2z" class="X"></path><path d="M243 176h2l1 2h2l-3 1c2 1 2 1 4 1h1c6 2 12 4 17 6 4 3 9 6 13 8l8 4c7 5 13 8 19 14h-1l-12-9c-9-5-18-11-28-15l-6-3c-2-1-4-2-6-2-4-2-11-5-15-3l-2 1v-1l1-1c1-1 3-2 5-3z" class="Q"></path><path d="M192 193v-2c0-2 1-3 2-4h3c2 1 3 1 4 1h1 2c0 1 2 1 3 1 0 1 0 1 1 1 1 1 6 2 7 2l1-1c2 0 2 0 4-1l7 1c2 2 2 1 5 3h-1v1h3v1h3c-2 2-3 1-4 2h-3c-2 0-3 0-4 1h-1-1c-2 1-4 1-6 1h-5c-4 1-13 2-17-1v-4l-1-2v-1l-2-2-1 3z" class="c"></path><path d="M197 195c2-1 5 0 7 0 4 1 8 1 12 2h-3c-5 0-13 1-16-2z" class="J"></path><path d="M220 190l7 1c2 2 2 1 5 3h-1c-2 1-4 1-6 1h-9 2c1-1 2-1 3-1l1-1-7-1 1-1c2 0 2 0 4-1z" class="K"></path><path d="M222 193l6 1h0l-3 1h-9 2c1-1 2-1 3-1l1-1z" class="V"></path><path d="M216 195c-3 0-7-1-10-1s-7 0-9-2c-1-1-1-2-1-3l1-1h5 2c0 1 2 1 3 1 0 1 0 1 1 1 1 1 6 2 7 2l7 1-1 1c-1 0-2 0-3 1h-2z" class="J"></path><path d="M240 197h2c1 1 2 2 2 3 2 2 3 3 5 4-5 0-10 2-15 4v-1l-2 1 4-3h0v-1c-2 1-2 1-3 1s-2 1-2 2l-5 2c-1 0-2 0-3-1 1-1 3-2 5-3l-3-1c-2 0-3 1-5 1h-1l-3 3-6 3h-3-1v-1l2-1-6-1c-3 0-5-3-7-4v-1c1 0 2-1 3 0 1 0 1 0 2-1l-5-1 1-2c4 3 13 2 17 1h5c2 0 4 0 6-1l8-1c2 0 5 0 8-1z" class="g"></path><path d="M219 203c1 0 2-1 4-1v1h0c-1 1-2 1-3 2h-1l1-1-1-1z" class="M"></path><path d="M225 204h0c2-2 3-2 6-2l1 1h1c-2 1-4 2-5 2l-3-1z" class="S"></path><path d="M240 197h2c1 1 2 2 2 3-2 0-5 2-8 2-1 1-2 2-3 1h-1l-1-1 1-1-1-1c2 0 7-1 9-3z" class="O"></path><path d="M244 200c2 2 3 3 5 4-5 0-10 2-15 4v-1l-2 1 4-3h0v-1c-2 1-2 1-3 1s-2 1-2 2l-5 2c-1 0-2 0-3-1 1-1 3-2 5-3 1 0 3-1 5-2 1 1 2 0 3-1 3 0 6-2 8-2z" class="f"></path><path d="M200 202h12v1c-1 0-4 0-5 1h3v1h-8v1c4 0 7 0 11-1h1l3-2h1 1l1 1-1 1-3 3-6 3h-3-1v-1l2-1-6-1c-3 0-5-3-7-4v-1c1 0 2-1 3 0 1 0 1 0 2-1z" class="b"></path><path d="M218 203h1l1 1-1 1-3 3-6 3h-3-1v-1l2-1c2 0 2-1 3-2h2c2-1 4-3 5-4z" class="U"></path><path d="M277 184c3 1 6 3 8 4 1 0 2-1 3-1s2 1 2 2h3l5 6 5 6 3 3-1 1 6 6h0l2 1 5 3h2c1 2 3 4 4 5 1 2 2 7 4 7 1 1 2 2 2 3 1 1 2 1 2 3h1c2 0 4 2 5 4h-5v1h-1c0-1 0-1-1-2l-3 1c0 2 0 3 1 4h-1c-1 0-2-1-3-1h-1 0c-1-1-2-4-2-5h1l1-1-2-2-1 1-2-3c-2-3-7-7-7-10v-1c0-1 0-2-1-2-1-2-3-4-4-5-6-6-12-9-19-14 0-1 0-2-1-3-2-1-3-3-5-5s-4-2-6-5l1-1z" class="M"></path><path d="M288 187c1 0 2 1 2 2l1 1c1 2 3 3 3 6l-9-8c1 0 2-1 3-1z" class="Q"></path><path d="M290 189h3l5 6 5 6 3 3-1 1c-2-1-4-3-5-4-2-2-4-3-6-5 0-3-2-4-3-6l-1-1z" class="B"></path><path d="M300 201c1 1 3 3 5 4l6 6h0l-1 1c1 2 3 3 4 4 2 3 3 6 6 8 1 1 4 3 4 5a30.44 30.44 0 0 1-8-8l-3-4c-4-5-9-10-14-14l1-2z" class="H"></path><path d="M311 211l2 1 5 3h2c1 2 3 4 4 5 1 2 2 7 4 7 1 1 2 2 2 3l-1 1h0l-1 1c-2-1-3-2-4-3 0-2-3-4-4-5-3-2-4-5-6-8-1-1-3-2-4-4l1-1z" class="e"></path><path d="M318 215h2c1 2 3 4 4 5h-4c-1-2-2-3-2-5z" class="J"></path><defs><linearGradient id="Ak" x1="323.107" y1="223.983" x2="317.261" y2="234.76" xlink:href="#B"><stop offset="0" stop-color="#6b6e71"></stop><stop offset="1" stop-color="#848486"></stop></linearGradient></defs><path fill="url(#Ak)" d="M313 217l3 4a30.44 30.44 0 0 0 8 8c1 1 2 2 4 3l1-1h0l1-1c1 1 2 1 2 3h1c2 0 4 2 5 4h-5v1h-1c0-1 0-1-1-2l-3 1c0 2 0 3 1 4h-1c-1 0-2-1-3-1h-1 0c-1-1-2-4-2-5h1l1-1-2-2-1 1-2-3c-2-3-7-7-7-10v-1c0-1 0-2-1-2h2z"></path><path d="M333 233c2 0 4 2 5 4h-5v1h-1c0-1 0-1-1-2l-3 1c0-1 0-2 1-4h1l3 3h1 0l-1-3z" class="T"></path><path d="M183 182h2c1 0 2 1 2 1 1 3 1 6 1 9v2l1 1c2-1 2-1 3-2l1-3 2 2v1l1 2v4l-1 2 5 1c-1 1-1 1-2 1-1-1-2 0-3 0v1c2 1 4 4 7 4l6 1-2 1v1h1 3l6-3 3-3h1c2 0 3-1 5-1l3 1c-2 1-4 2-5 3 1 1 2 1 3 1l5-2c0-1 1-2 2-2s1 0 3-1v1h0l-4 3 2-1v1c5-2 10-4 15-4l2 1h2l6 6 1 2h0l1 1c1 2 3 7 3 9l-1 2h-1l-2-1h-1l-1-1c-1-1-1-2-2-3l-1-1v3 1h-2 0c-4 0-7-2-11-2-3-1-5 0-7 0-2 1-5 2-7 3-1 1-2 1-3 1l-1 1h-1s-1 1-2 1l-2-1c-1-1-2 0-4 0h0c-3-1-8-1-12-1-1 1-3 1-5 1l-2-1c-1 1-1 2 0 3l1 4h0c2 1 3 3 5 5l2 1v1l-3-1h-2l2 2c0 1 0 2-1 3h-1l-3-2-2-2c-2-1-4-2-5-4s-1-2-3-3c-3-4-6-7-10-10 0 1 0 2-1 3 1 1 6 4 6 6h-1s0-1-1-1c-1-1-3-1-4-1-2 1-4 2-5 3v-1c-2 1-3 2-3 4l1 4h0l2 4h0l-2-3-1 1c-2-3-4-5-7-7 0 2 3 3 3 5-1 0-1-1-2-1l-2-2c-2 0-3 0-5-1v2c-2 1-5 0-7 0h-6l-2 1c-1 1-2 1-3 2l-2-2v-1l1-1-2-2v-1h-2c-1 1-1 1-3 2l3-10 3-5 3-4 9-10 12-9c1 0 4-2 5-3 5-4 10-8 16-11h2l2-1z" class="Z"></path><path d="M238 215c-1 1-3 2-4 3 0-2 1-4 2-6 1 1 1 2 2 3z" class="B"></path><path d="M151 211c-1 1-1 1-2 1l-1 1h-4v-1l1-1h6 0z" class="R"></path><path d="M151 211c1-1 3-1 4-1s1 1 2 1 2 1 3 1h0c-3 0-6 0-9-1h0z" class="K"></path><path d="M253 214l1 1c2 1 2 2 3 4l-1 1-1-1v3 1h-2 0l1-1 1 1v-1-1c-1-3-2-4-2-6v-1z" class="Q"></path><path d="M202 208l6 1-2 1v1h1c-1 1-1 1-3 1l-2 1h-2l-1-2h4 1 0c-1-2-1-2-2-3z" class="I"></path><path d="M167 194h0l7-3v1l-2 1 1 1 1 2-2 2-1-1c-1-1-3-2-4-3z" class="c"></path><path d="M193 231c-2 0-3-1-4-2l1-1 1 1h0c0-1 0-2-1-2 0-1 0-2-1-2-1-1-2-1-2-2s1-1 2-2c2 1 3 1 5 2-1 2-1 4 0 6h0-1v1 1z" class="Q"></path><path d="M194 223l2 2c-1 1-1 2 0 3l1 4h0c2 1 3 3 5 5l2 1v1l-3-1h-2c-2-2-4-4-6-7v-1-1h1 0c-1-2-1-4 0-6z" class="c"></path><path d="M236 212c1-1 4-2 6-3h1c2-1 4-1 7-1 1 1 2 1 3 3v1h-1c-1 1-1 1-3 1-4-1-7 0-11 2-1-1-1-2-2-3z" class="H"></path><path d="M142 219c2-1 5-2 8-3h0c8-1 19 1 25 5l1 1c0 1 0 2-1 3l-6-3c-1 0-3-1-5-1-1-1-1-2-2-2-2-1-9-2-11-2-3 1-5 2-8 2h-1z" class="u"></path><path d="M142 219h1c3 0 5-1 8-2 2 0 9 1 11 2 1 0 1 1 2 2l-2-1-8 1c-4 1-7 2-10 3l-2 1h0l-1-1c1-1 2 0 3-1v-1c-1 0-1-1-2-1-2 0-3 0-5 1h0l-1-1 6-2z" class="AD"></path><path d="M144 222l1-1c4-2 7-2 11-1h6l-8 1c-4 1-7 2-10 3l-2 1h0l-1-1c1-1 2 0 3-1v-1z" class="r"></path><path d="M131 225l3-5 2 1h0l1 1h0c2-1 3-1 5-1 1 0 1 1 2 1v1c-1 1-2 0-3 1l1 1h0l-3 2-5 4-1 2h-2c-1 1-1 1-3 2l3-10z" class="q"></path><path d="M136 221l1 1h0c2-1 3-1 5-1 1 0 1 1 2 1v1c-1 1-2 0-3 1l1 1h0l-3 2-5 4c2-2 4-4 5-6h0c-1 1-2 1-4 1v-1-1h-1v-1l2-2h0z" class="s"></path><path d="M183 182h2c1 0 2 1 2 1 1 3 1 6 1 9v2l1 1h0-2l-4 2c-2-1-2-1-3-2-3 1-3 1-5 1h-1l-1-2 1-1h1l1-1c1 0 3 0 4 1h0l1-1-2-1h0c1-1 1-1 2 0l1 1c1 1 1 2 3 2h0c-1-3-1-5-3-7v-1l-3-3h2l2-1z" class="Q"></path><path d="M175 196l-1-1 1-1 1-1c2 0 3 0 4 1v1c-3 1-3 1-5 1z" class="D"></path><path d="M183 182h2c1 0 2 1 2 1 1 3 1 6 1 9 0 1-1 1-1 2-2-1-2-5-3-7-1-1-2-3-3-4l2-1z" class="t"></path><path d="M253 205l6 6 1 2h0l1 1c1 2 3 7 3 9l-1 2h-1l-2-1h-1l-1-1c-1-1-1-2-2-3l1-1c-1-2-1-3-3-4l-1-1-1-2h1v-1c-1-2-2-2-3-3 1 0 2-1 2-2l-1-1h2z" class="E"></path><path d="M253 212c1-1 2-1 4-1 1 1 1 2 1 3l-2 1h-2l-1-1-1-2h1z" class="m"></path><path d="M254 215h2c2 2 4 3 4 6 0 1-1 2-1 3l-1-1c-1-1-1-2-2-3l1-1c-1-2-1-3-3-4z" class="R"></path><path d="M155 203c2-2 3-2 5-3 3 1 7 2 11 3 1 1 1 1 2 1l1-1h2c0 1 1 2 2 3l1 1 1 1h1l1-1c0 2-1 3-2 4 1 0 1 1 2 1-2 2-5 2-7 2l-15-2h0c-1 0-2-1-3-1s-1-1-2-1-3 0-4 1h-6l4-3c2-2 5-3 6-5z" class="S"></path><path d="M149 208c2-2 5-3 6-5l2 1c-1 0-1 1 0 1l-2 1v2l1 1c-2 0-5-1-7-1z" class="K"></path><path d="M157 205l19 4h0l-1 1c-2 0-10-1-12-1-2-1-5-1-8-1v-2l2-1z" class="k"></path><path d="M157 204c5 1 11 0 17 2h1l3 2-2 1-19-4c-1 0-1-1 0-1z" class="h"></path><path d="M155 203c2-2 3-2 5-3 3 1 7 2 11 3 1 1 1 1 2 1l1-1h2c0 1 1 2 2 3l1 1 1 1h1l1-1c0 2-1 3-2 4 1 0 1 1 2 1-2 2-5 2-7 2l-15-2h0v-1h3c1 1 0 1 1 1h1 1 8l2-1h1c1 0 1-1 2-2l-1-1-3-2h-1c-6-2-12-1-17-2l-2-1z" class="J"></path><path d="M192 193l1-3 2 2v1l1 2v4l-1 2-6 5c-3 2-4 5-7 6-1 0-1-1-2-1 1-1 2-2 2-4l-1 1h-1l-1-1-1-1c-1-1-2-2-2-3h-2l-1 1c-1 0-1 0-2-1h1l-2-2c-2-1-5-2-6-4v-1c0-1 1-1 2-1l1-1c1 1 3 2 4 3l1 1 2-2h1c2 0 2 0 5-1 1 1 1 1 3 2l4-2h2 0c2-1 2-1 3-2z" class="H"></path><g class="J"><path d="M192 193l1-3 2 2v1l-3 3c-1 1-2 1-4 2h0-1 0c1-1 1-2 2-3h0c2-1 2-1 3-2z"></path><path d="M187 195h2c-1 1-1 2-2 3h0c-1 1-2 1-3 1l-1 1h-2-1c-2 0-3 1-5 1-1 1-1 1-2 1l-1-1h-2c-2-1-5-2-6-4h0c1 0 2 0 4 1 1 1 4 3 5 3s8-4 10-4l4-2z"></path></g><path d="M187 195h2c-1 1-1 2-2 3h0c-1 1-2 1-3 1l-1 1h-2c2-2 4-2 6-4v-1z" class="V"></path><path d="M164 197v-1c0-1 1-1 2-1l1-1c1 1 3 2 4 3l1 1 2-2h1c2 0 2 0 5-1 1 1 1 1 3 2-2 0-9 4-10 4s-4-2-5-3c-2-1-3-1-4-1h0z" class="G"></path><path d="M166 195l1-1c1 1 3 2 4 3l1 1-1 1c-1-1-3-2-5-3h-1l1-1z" class="N"></path><path d="M196 195v4l-1 2-6 5c-3 2-4 5-7 6-1 0-1-1-2-1 1-1 2-2 2-4l-1 1h-1l-1-1-1-1c-1-1-2-2-2-3l11-3c2-1 4-2 6-2l3-3z" class="e"></path><path d="M196 195v4l-1 2-6 5h-1c1-1 1-1 1-2-2 1-3 2-5 3v1l-1-1 1-1h-2l10-7 1-1 3-3z" class="X"></path><path d="M225 204l3 1c-2 1-4 2-5 3 1 1 2 1 3 1l5-2c0-1 1-2 2-2s1 0 3-1v1h0l-4 3 2-1v1l-1 1c-4 2-7 6-11 9-1 1-3 2-5 3-1 1-3 0-5 0-5 0-11 0-16-2-2-1-9-3-11-5h1c1 0 3 1 4 1l-3-2v-1-1h0l2-2 2 1c1-1 2-1 4-1h0c2 1 3 1 4 2l1 2h2l2-1c2 0 2 0 3-1h3l6-3 3-3h1c2 0 3-1 5-1z" class="P"></path><path d="M231 207c-1 2-5 5-7 7-1 1-2 1-4 2l-3 2c-3 1-6 1-9 0h-8l-2-1c-2 0-4-1-6-2h-2l-3-2v-1h2l2 1h2 1l1 1c9 3 17 2 25-1l1 1 5-5 5-2z" class="l"></path><path d="M208 218c3-1 7-1 10-3l1-1 1 1-4 2 1 1c-3 1-6 1-9 0z" class="Y"></path><path d="M225 204l3 1c-2 1-4 2-5 3 1 1 2 1 3 1l-5 5-1-1c-8 3-16 4-25 1l-1-1h-1-2l-2-1h-2v-1h0l2-2 2 1c1-1 2-1 4-1h0c2 1 3 1 4 2l1 2h2l2-1c2 0 2 0 3-1h3l6-3 3-3h1c2 0 3-1 5-1z" class="L"></path><path d="M191 210c1-1 2-1 4-1h0c2 1 3 1 4 2l1 2-9-3z" class="C"></path><path d="M225 204l3 1c-2 1-4 2-5 3-4 2-8 5-13 6-3 0-5 0-8-1l2-1c2 0 2 0 3-1h3l6-3 3-3h1c2 0 3-1 5-1z" class="N"></path><path d="M216 208c1 0 3 0 4-1-2 2-4 4-7 4l-3 3c-3 0-5 0-8-1l2-1c2 0 2 0 3-1h3l6-3z" class="F"></path><path d="M162 220l2 1c2 0 4 1 5 1l6 3c1 1 6 4 6 6h-1s0-1-1-1c-1-1-3-1-4-1-2 1-4 2-5 3v-1c-2 1-3 2-3 4l1 4h0l2 4h0l-2-3-1 1c-2-3-4-5-7-7 0 2 3 3 3 5-1 0-1-1-2-1l-2-2c-2 0-3 0-5-1v2c-2 1-5 0-7 0h-6l-2 1c-1 1-2 1-3 2l-2-2v-1l1-1-2-2v-1l1-2 5-4 3-2 2-1c3-1 6-2 10-3l8-1z" class="H"></path><path d="M158 226l2 1-2 3 1 2v2l-2-2c0-2 0-4 1-6z" class="V"></path><path d="M155 223h4 4l-2 2-1 2-2-1-3-3z" class="M"></path><path d="M154 221c1 1 3 1 5 2h-4c-2 0-4 1-6 2-2 0-3 0-5-1 3-1 6-2 10-3z" class="I"></path><path d="M163 223l1 1v1c1 1 1 1 2 1 0 1 0 1-1 1-3 1-4 2-6 5l-1-2 2-3 1-2 2-2z" class="S"></path><path d="M163 223l1 1v1c0 1-1 1-1 2l-2-2 2-2z" class="O"></path><path d="M162 220l2 1c2 0 4 1 5 1l-2 1-2 1h-1l-1-1h-4c-2-1-4-1-5-2l8-1z" class="C"></path><path d="M169 222l6 3c1 1 6 4 6 6h-1s0-1-1-1c-1-1-3-1-4-1-2 1-4 2-5 3v-1c-2 1-3 2-3 4l1 4h0l2 4h0l-2-3-1 1c-2-3-4-5-7-7h-1v-2c2-3 3-4 6-5 1 0 1 0 1-1-1 0-1 0-2-1v-1h1l2-1 2-1z" class="i"></path><path d="M171 226h2v1l-1 1-1 1-1 1c2 0 3-2 5-2v1c-2 1-4 2-5 3v-1c-2 1-3 2-3 4l1 4h0c-1-2-3-3-4-5v-1c-1-1-1-1-1-3h3l1 1c2-1 3-3 4-5z" class="X"></path><path d="M169 222l6 3c1 1 6 4 6 6h-1s0-1-1-1c-1-1-3-1-4-1v-1c-2 0-3 2-5 2l1-1 1-1 1-1v-1h-2c-2 0-3-1-5 0-1 0-1 0-2-1v-1h1l2-1 2-1z" class="D"></path><path d="M142 225l2-1c2 1 3 1 5 1v2l2 1-1 1c1 2 1 2 1 4v1h0c-2 0-4 0-6 1h0c-2 1-3 1-4 2l-2 1c-1 1-2 1-3 2l-2-2v-1l1-1-2-2v-1l1-2 5-4 3-2z" class="b"></path><path d="M134 231l5-4 1 3c0 1 0 2-1 3h-1c-1 0-1 0-1 1s-1 1-1 2v1c-1 1-1 0-2 1v-1l1-1-2-2v-1l1-2z" class="F"></path><path d="M141 229h1 2 1l2 2v2h-1c-2 0-3 2-6 2 0-1-1-1-1-2 1-1 1-2 1-3l1-1z" class="H"></path><path d="M142 225l2-1c2 1 3 1 5 1v2l2 1-1 1c-1 1-1 3-3 4v-2l-2-2h-1-2-1l-1 1-1-3 3-2z" class="Q"></path><path d="M142 225l2-1c2 1 3 1 5 1v2c-2-1-2-1-4 0h-3l-1 2-1 1-1-3 3-2z" class="D"></path><path d="M451 121h1c2 2 3 4 3 7l1 2v1l-1 4h0c-1 2-2 3-3 5-1 5-4 10-7 13l1 1-2 2-2 1c-4 4-8 7-10 12l-1-1c0 1-1 2-1 3s-1 3-2 4l-1-1v-1-1c-2 1-5 4-6 4l-1 1h-1c-2 1-3 1-5 1-2 2-5 4-5 7-1 1-1 2-2 3l-1 1 1 2-1 1-3-1-3 6 1 1 1 1 1 3v4c1-1 1-1 2-1v-1 2l1-1 1-1c1-1 2-1 4-1 2 5 7 8 11 11 1 1 2 1 3 2l3-2h1v-2h0l3 5h1c0 1 2 3 2 5l-1-1c-1 0-2 0-3-1s-2-1-3-1l-1-1c-2 6-5 17-3 22l1 1c-2-1-4-3-6-3-6-2-13-1-19-1h-14-1c-2-1-1-1-2-1-1 1-2 1-2 1l-2-1v-1h4v-1h-2-4-2l-2 1-1-1v2l1 1c0 2 1 4 0 6l-1 1v11c-1 1-1 1-1 2-4-1-7-1-11-2-9 0-14 2-21 8h0c-1 0-2 1-3 1v-3c0-1 1-1 2-2h2v-1-1c0-1-1-1 0-2l1 1h2v-1c-2-4-5-8-9-11-1-2-2-2-3-4 0 0 1-1 1-2h1v-1h5c-1-2-3-4-5-4h-1c0-2-1-2-2-3 0-1-1-2-2-3-2 0-3-5-4-7-1-1-3-3-4-5h-2l-5-3 5 1c1 0 3 0 5 1h2 2l-1-2 1-1-2-2 3-1 1 1h4c-2-2-4-3-5-4l-3-2v-1l-3-3-1-1 2-2h0l-1-1c-2-2-4-3-5-4 1-1 1-2 2-3 1-3 2-5 4-7l1-2h0c1 2 2 3 4 4s3 2 4 3h1 1 0l4-5h1c0 2-1 2-1 4h3 1c2 0 3-1 4-1l2-3h1c1-1 1-1 1-2l2-2v1h2c3-3 5-6 10-7 3-1 6 1 10 0h0 1c1-3 4-7 7-8h0l8-7c1-2 3-3 4-5l-1-1c4 0 10-2 14-1 5-2 10-3 15-5 8-3 15-8 21-13h1l2-3 2-3h1l1-1c0-1 1-2 1-3z" class="I"></path><path d="M370 231c0-2 1-3 1-4h6c-2 2-5 4-7 4h0z" class="N"></path><path d="M363 222h2l4 7c1 1 0 1 1 2h0l-1 1c-3-1-4-3-5-5l-1-5z" class="R"></path><path d="M377 227c-1-3-3-2-5-4-1-1-3-4-3-6v-2-3-1-3-1-2h3v-1-1l1 2-1 1h-1l-1 1 2 2c-1 2-2 6-1 8l2 3 2 2c1 1 2 3 4 3l1 1-3 1z" class="N"></path><path d="M374 187l1 1 1 2 1 1v1c2 1 3 2 6 2l1-1h1l-1 2c-3 0-4-1-6 1-1 2-2 3-2 4-1 1-1 2-2 3v1c1-1 2-1 2-1h1l-1 2h0l-3 3-1 1-2-2 1-1h1l1-1-1-2v-1c0-2 0-3 1-5 0 0 1-1 2-1l1-1 1-1v-1l-1-1c-1-2-2-3-2-5z" class="S"></path><path d="M387 172v-2c4-4 10-8 15-12 1-1 3-3 5-4 5-2 10-4 14-6-4 7-15 6-19 12-2 2-5 4-7 6l-1 1c-2 0-5 3-6 5h-1z" class="B"></path><path d="M375 185c0-1 1-3 2-4 1 0 1 0 1-1 1-1 1-1 3-1 1 1 1 1 3 2v1h1l2-1c1 1 2 2 2 4s-1 4-2 6l-1 1-1 1h-1l-1 1c-3 0-4-1-6-2v-1l-1-1-1-2-1-1c0-1 0-1 1-2z" class="R"></path><path d="M375 188c1-1 1-3 3-3v1c1 0 1 1 2 2-1 1-2 1-4 2l-1-2z" class="E"></path><path d="M375 185h1l2-3h5l-1 1c-2 0-3 1-4 3v-1c-2 0-2 2-3 3l-1-1c0-1 0-1 1-2z" class="C"></path><path d="M375 185c0-1 1-3 2-4 1 0 1 0 1-1 1-1 1-1 3-1 1 1 1 1 3 2l-1 1h-5l-2 3h-1z" class="K"></path><path d="M385 182l2-1c1 1 2 2 2 4s-1 4-2 6l-1 1-1 1h-1l-1 1c-3 0-4-1-6-2v-1c2 1 3 2 6 1 1-1 3-3 3-5s-1-4-2-5h1zm43-37h8v3h-1c-1 1-2 3-3 4l-4 5c0 1-2 3-3 3 0 1-2 3-2 3l-3 2h0c0-2 1-2 2-4h0l-1-2h0c-1-1-1-1-1-2l-1-1c-6 3-13 5-17 12v2 1h-1l-2 5v-1h-1-1-1c-2 1-5 0-7 0h0c-1-1-2-1-3-2l1-1h1c1-2 4-5 6-5l1-1c2-2 5-4 7-6 4-6 15-5 19-12h1l6-3z" class="i"></path><path d="M389 175l1-2 1-1c2-1 8-1 10-1l-2 5v-1h-1-1-1c-2 1-5 0-7 0z" class="P"></path><path d="M428 145h8v3h-1c-1 1-2 3-3 4l-4 5c0 1-2 3-3 3h-1l1-1c0-1 1-1 1-2h0c2-1 3-3 4-4v-1c-1 1-3 0-4 2v1h0l-1-1h-1v-1h1c0-1 0-1-1-2-1 1-2 1-2 2-8 3-14 5-20 9-3 2-4 2-6 4-2 3-5 5-8 6 1-2 4-5 6-5l1-1c2-2 5-4 7-6 4-6 15-5 19-12h1l6-3z" class="J"></path><path d="M428 145h8v3h-1c-1 1-2 3-3 4v-1c1-1 1-2 1-3-3 0-7 2-10 2-2 0-5 2-7 3-5 3-10 6-14 7 4-6 15-5 19-12h1l6-3z" class="V"></path><path d="M428 145h8v3h-1c-1 1-2 3-3 4v-1c1-1 1-2 1-3l-1-1c-3 0-7 1-10 1l6-3z" class="H"></path><path d="M373 208l3-3h0l1-2h-1s-1 0-2 1v-1c1-1 1-2 2-3 0-1 1-2 2-4 2-2 3-1 6-1l-1 2h-1c-1 0-1-1-1-1h-1c0 1 0 2 2 2 1 1 3 1 4 1v-1l1 1v2h0v6 2l2 1v2l3 2 1 12 1 5h-11c-3 1-6 0-9 1l-2 2v2l1 1c0 2 1 4 0 6l-1 1c-2-4-4-6-7-7v-1c1-2 2-3 4-4l1-1c2 0 5-2 7-4h0l3-1-1-1c-2 0-3-2-4-3l-2-2-2-3c-1-2 0-6 1-8l1-1z" class="e"></path><path d="M378 209l5-4c1 2 1 4 1 6v1l-2-2c-1 0-2 0-3 1 0-1 0-1-1-2z" class="J"></path><path d="M372 209l1-1 2 3h-1c-1 3-2 5-2 8l1 1-2-3c-1-2 0-6 1-8z" class="V"></path><path d="M379 211c1-1 2-1 3-1l2 2v-1c2 2 2 3 2 5l-1 2h-2 0c-1-1-1-1-2-1h0c-2-2-2-2-2-4h0v-2z" class="I"></path><path d="M379 211c1-1 2-1 3-1l2 2v1c-1 1-2 1-4 1l-1-1h0v-2z" class="X"></path><path d="M378 209c1 1 1 1 1 2v2h0c0 2 0 2 2 4h0c1 0 1 0 2 1h0 2 0c-1 2 0 2 0 3 0 0-1 1-1 2l-1 1h0c-2 0-3 0-4 1-2 0-3-2-4-3v-2c-1-1-2-2-1-4 0-3 2-5 4-7z" class="C"></path><path d="M379 219l-2-2v-4h2 0c0 2 0 2 2 4h0c1 0 1 0 2 1h0c-1 1-2 1-4 1z" class="Z"></path><path d="M383 218h2 0c-1 2 0 2 0 3 0 0-1 1-1 2l-1 1h0c-2 0-3 0-4 1-2 0-3-2-4-3v-2l3 2v-1l-2-1 1-1h2c2 0 3 0 4-1z" class="F"></path><path d="M375 220l3 2c2 0 4 1 6 1l-1 1h0c-2 0-3 0-4 1-2 0-3-2-4-3v-2z" class="Y"></path><path d="M385 218c1 1 2 1 2 3v3 3h3 0l-1-1h-1l1-1 3 3h0l1-1v-1l1 5h-11c-3 1-6 0-9 1l-2 2v2l1 1c0 2 1 4 0 6l-1 1c-2-4-4-6-7-7v-1c1-2 2-3 4-4l1-1c2 0 5-2 7-4h0l3-1-1-1c1-1 2-1 4-1h0l1-1c0-1 1-2 1-2 0-1-1-1 0-3z" class="H"></path><path d="M379 225c1-1 2-1 4-1h-1c-2 3-4 4-7 7 2 1 5 0 8 0-3 1-6 0-9 1l-2 2v2l1 1c0 2 1 4 0 6l-1 1c-2-4-4-6-7-7v-1c1-2 2-3 4-4l1-1c2 0 5-2 7-4h0l3-1-1-1z" class="L"></path><path d="M372 236c-2 0-3 0-5-1 1 0 1-1 2-1 2-1 3-1 4-3l1 1-2 2v2z" class="Y"></path><path d="M402 171v-1-2c4-7 11-9 17-12l1 1c0 1 0 1 1 2h0l1 2h0-2l-1 1v2 1h-1l-1-1h-1v1c1 1 1 1 1 3-1 0-1 1-1 2l-4 5c-4 5-7 10-9 16l-3 6h0-1l1-2c-1-1-1-1-1-2-2 4-4 8-5 12v3c-1-1-1-2-1-3l-2 1h0c-2 1-1 1-2 1l-1-4h0v4h-1v-6h0v-2l-1-1v1c-1 0-3 0-4-1-2 0-2-1-2-2h1s0 1 1 1h1l1-2 1-2 1-1 1-1c1-2 2-4 2-6s-1-3-2-4l-2 1h-1v-1c-2-1-2-1-3-2l5-6c1 1 2 1 3 2h0c2 0 5 1 7 0h1 1 1v1l2-5h1z" class="L"></path><path d="M400 180h2c-1 1-1 3-2 4l-2-1c0-1 0-1 1-3h1z" class="d"></path><path d="M392 179l2-1v1l-1 1h1 0l3 3c0 1 0 1-2 2-1 0-2-1-3-1l-1-3c0-1 0-1 1-2z" class="J"></path><path d="M381 179l5-6c1 1 2 1 3 2h0c2 0 5 1 7 0h1 1v1c1 1 1 1 1 2l1 2h-1-1-2-2 0-1l1-1v-1l-2 1c-1 1-1 1-1 2h-1-3l-2 1h-1v-1c-2-1-2-1-3-2z" class="M"></path><path d="M385 182l-1-1 1-1c1 0 2-1 3 0h1c1-1 1-1 3-1-1 1-1 1-1 2h-1-3l-2 1z" class="e"></path><path d="M387 181h3 1l1 3c-2 0-2 0-3 2v4c0 2-1 3-2 4v1h1c2-2 1-4 3-6 0 0 2-1 2-2 1 0 2-1 3-1 0 2-1 1-1 3-1 1 0 3 0 4-1 3-1 6-1 9v3 3c-1-1-1-2-1-3l-2 1h0c-2 1-1 1-2 1l-1-4h0v4h-1v-6h0v-2l-1-1v1c-1 0-3 0-4-1-2 0-2-1-2-2h1s0 1 1 1h1l1-2 1-2 1-1 1-1c1-2 2-4 2-6s-1-3-2-4z" class="P"></path><path d="M402 171v-1-2c4-7 11-9 17-12l1 1c0 1 0 1 1 2h0l1 2h0-2l-1 1v2 1h-1l-1-1h-1v1c1 1 1 1 1 3-1 0-1 1-1 2l-4 5c-4 5-7 10-9 16l-3 6h0-1l1-2c-1-1-1-1-1-2-2 4-4 8-5 12v-3c0-3 0-6 1-9 0-1 1-3 1-4v-1c1-1 2-1 3-2 0 2-1 3-1 5h0c1-1 2-2 2-3v-1c1-1 1-2 2-3 0-1 0-2 1-3s2-2 2-3c1-2 2-3 3-4h0c-3 1-4 3-5 5l-1 1h-2l-1-2c0-1 0-1-1-2v-1h1v1l2-5h1z" class="j"></path><path d="M399 193c2-6 6-10 9-15 1-2 3-3 4-5h-1v-1h-1c0-1 0-2-1-3h0 1c1 1 1 2 2 3 2 0 3-1 4-2l-4 5c-4 5-7 10-9 16l-3 6h0-1l1-2c-1-1-1-1-1-2z" class="H"></path><path d="M402 171v-1-2c4-7 11-9 17-12l1 1c0 1 0 1 1 2h0l-2 2h-1v2h-1v-2c-1 1-1 0-1 2l-2 2h-1l-1-1 2-1c1-1 1-1 1-2h1l-1-1-2 1c-1 1-3 1-4 2-2 1-3 3-5 5v2l-2 1z" class="L"></path><path d="M451 121h1c2 2 3 4 3 7l1 2v1l-1 4h0c-1 2-2 3-3 5-1 5-4 10-7 13l1 1-2 2-2 1c-4 4-8 7-10 12l-1-1c0 1-1 2-1 3s-1 3-2 4l-1-1v-1-1c-2 1-5 4-6 4l-1 1h-1c-2 1-3 1-5 1-2 2-5 4-5 7-1 1-1 2-2 3l-1 1 1 2-1 1-3-1c2-6 5-11 9-16l4-5c0-1 0-2 1-2 0-2 0-2-1-3v-1h1l1 1h1v-1-2l1-1h2c-1 2-2 2-2 4h0l3-2s2-2 2-3c1 0 3-2 3-3l4-5c1-1 2-3 3-4h1v-3h-8l10-3c0-2 0-4 1-6h2v1c2 0 3 0 5-1v-1c-1-2-1-3-2-4l2-3 2-3h1l1-1c0-1 1-2 1-3z" class="d"></path><path d="M428 157c0 2 1 3 0 4l-3 3h0c0 2 0 2-2 3l-1-1c0-1 0-1 1-2v-1s2-2 2-3c1 0 3-2 3-3z" class="L"></path><path d="M428 145l10-3c-1 4-2 9-4 12h0c-1-2 1-4 1-6h1v-3h-8z" class="B"></path><path d="M431 163l1 1c0 1 0 3-1 4 0 1-1 2-1 3s-1 3-2 4l-1-1v-1-1c-2 1-5 4-6 4 2-5 7-8 10-13z" class="R"></path><path d="M416 170c0-1 0-2 1-2 0-2 0-2-1-3v-1h1l1 1h1v-1-2l1-1h2c-1 2-2 2-2 4h0l3-2v1c-1 1-1 1-1 2l1 1h0l-2 3c-2 0-2-1-4 0 0 2 0 3-1 4s-1 2-2 2h-1c-1-1 0-1-1-1l4-5z" class="Y"></path><path d="M441 137c2 0 3 0 5-1v4c0 1-1 4 0 5-3 4-7 9-11 11-1-1 4-4 4-6s1-4 2-7v-6z" class="D"></path><path d="M451 140h1c-1 5-4 10-7 13l1 1-2 2-2 1c-4 4-8 7-10 12l-1-1c1-1 1-3 1-4l-1-1-1 1v-1l1-1c0-1 0-2 1-2 1-1 2-1 3-1v-3c4-2 8-7 11-11-1-1 0-4 0-5 1 1 2 3 2 5l1-2 2-3z" class="j"></path><path d="M442 157v-2c1-1 1-2 3-2l1 1-2 2-2 1zm4-17c1 1 2 3 2 5 0 1-1 2-2 4l-1-1 1-2v-1c-1-1 0-4 0-5z" class="Y"></path><path d="M451 121h1c2 2 3 4 3 7l1 2v1l-1 4h0c-1 2-2 3-3 5h-1l-2 3-1 2c0-2-1-4-2-5v-4-1c-1-2-1-3-2-4l2-3 2-3h1l1-1c0-1 1-2 1-3z" class="F"></path><path d="M444 131l2-3c1 3 2 6 2 9 1 2 1 4 1 6l-1 2c0-2-1-4-2-5v-4-1c-1-2-1-3-2-4z" class="L"></path><path d="M451 121h1c2 2 3 4 3 7l1 2v1l-1 4h0c-1 2-2 3-3 5h-1l1-1c0-5-2-9-3-14l1-1c0-1 1-2 1-3z" class="P"></path><path d="M454 134l1 1c-1 2-2 3-3 5h-1l1-1v-4c1-1 1-1 2-1z" class="Y"></path><path d="M451 121h1c2 2 3 4 3 7l1 2v1l-1 4h0l-1-1c-1-4-2-7-4-10 0-1 1-2 1-3z" class="I"></path><path d="M394 205c1-4 3-8 5-12 0 1 0 1 1 2l-1 2h1 0l1 1 1 1 1 3v4c1-1 1-1 2-1v-1 2l1-1 1-1c1-1 2-1 4-1 2 5 7 8 11 11 1 1 2 1 3 2l3-2h1v-2h0l3 5h1c0 1 2 3 2 5l-1-1c-1 0-2 0-3-1s-2-1-3-1l-1-1c-2 6-5 17-3 22l1 1c-2-1-4-3-6-3-6-2-13-1-19-1h-14-1c-2-1-1-1-2-1-1 1-2 1-2 1l-2-1v-1h4v-1h-2-4-2l-2 1-1-1 2-2c3-1 6 0 9-1h11l-1-5-1-12-3-2v-2l-2-1v-2h1v-4h0l1 4c1 0 0 0 2-1h0l2-1c0 1 0 2 1 3v-3z" class="K"></path><path d="M394 205c1-4 3-8 5-12 0 1 0 1 1 2l-1 2c-2 5-2 10-2 15 0 2-1 4-1 6h-1c0-3 1-5 1-7h-1l-1 7c-1-3-1-5 0-9v-1-3z" class="g"></path><path d="M387 207h1v-4h0l1 4c1 0 0 0 2-1h0l2-1c0 1 0 2 1 3v1c-1 4-1 6 0 9 0 5 1 9 2 13h2-4l-1-5-1-12-3-2v-2l-2-1v-2z" class="T"></path><path d="M419 222v3l-1 1v3c1-1 1-2 2-3 0 2-1 4-3 5s-12 1-14 0l-1-1v-2c0-2-1-2-2-3l1-1 1 1 1 1c0-1 0-1 1-2 0 1 0 1 1 2l1 2h5 0 2c2 0 3-1 3-2l2-2c1-1 1-1 1-2z" class="D"></path><path d="M400 197h0l1 1 1 1 1 3v4c1-1 1-1 2-1v-1 2h0c0 1 1 2 1 3l1 1c-1 1-3 1-5 1v3l-1 2h1c1 2 1 3 1 5-1 1-2 1-3 2l-1-1v3h-1c-1-1-1-2-1-3h0c0-8 2-17 3-25z" class="L"></path><path d="M402 211c0-1 1-2 1-3 1 0 1-1 2-1v-1c0 1 1 2 1 3l1 1c-1 1-3 1-5 1z" class="G"></path><path d="M411 203c2 5 7 8 11 11 1 1 2 1 3 2-3 3-2 7-5 10-1 1-1 2-2 3v-3l1-1v-3c0 1 0 1-1 2l-2 2c0 1-1 2-3 2h-2c-2-1-3-2-4-4-1 0-2-1-3-1l-1 1-2-1h-1c1-1 2-1 3-2 0-2 0-3-1-5h-1l1-2v-3c2 0 4 0 5-1l-1-1c0-1-1-2-1-3h0l1-1 1-1c1-1 2-1 4-1z" class="c"></path><path d="M402 214l2-1c2 2 2 3 2 5h-1v-1l-3-1h-1l1-2z" class="h"></path><path d="M402 216l3 1v1h1v1c0 1 0 2-1 3h1 1v2c-1 0-2-1-3-1l-1 1-2-1h-1c1-1 2-1 3-2 0-2 0-3-1-5z" class="F"></path><path d="M415 223l2-1h2c0 1 0 1-1 2l-2 2c0 1-1 2-3 2h-2c-2-1-3-2-4-4v-2c1 0 2 1 3 1h1l2 1c1 0 2 0 2-1z" class="C"></path><defs><linearGradient id="Al" x1="413.364" y1="202.911" x2="416.191" y2="228.644" xlink:href="#B"><stop offset="0" stop-color="#0d0e11"></stop><stop offset="1" stop-color="#4d4f51"></stop></linearGradient></defs><path fill="url(#Al)" d="M411 203c2 5 7 8 11 11 1 1 2 1 3 2-3 3-2 7-5 10-1 1-1 2-2 3v-3l1-1v-3h-2l-2 1c0-2 0-2-1-3h-1v-2l-1-1h0l5-1c0 1 1 1 1 1h1c-3-2-5-5-7-6-2-2-3-1-5-1l-1-1c0-1-1-2-1-3h0l1-1 1-1c1-1 2-1 4-1z"></path><path d="M425 216l3-2h1v-2h0l3 5h1c0 1 2 3 2 5l-1-1c-1 0-2 0-3-1s-2-1-3-1l-1-1c-2 6-5 17-3 22l1 1c-2-1-4-3-6-3-6-2-13-1-19-1h-14-1c-2-1-1-1-2-1-1 1-2 1-2 1l-2-1v-1h4v-1h-2-4-2l-2 1-1-1 2-2c3-1 6 0 9-1h11 4 5c2 1 12 1 14 0s3-3 3-5c3-3 2-7 5-10z" class="p"></path><path d="M427 218l1-2h1c2 1 4 3 5 5-1 0-2 0-3-1s-2-1-3-1l-1-1z" class="F"></path><defs><linearGradient id="Am" x1="394.783" y1="210.379" x2="366.966" y2="171.91" xlink:href="#B"><stop offset="0" stop-color="#797a7b"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#Am)" d="M393 150c4 0 10-2 14-1-14 10-29 22-37 39-4 7-6 14-7 22-1 4 0 8 0 12l1 5c1 2 2 4 5 5-2 1-3 2-4 4v1c3 1 5 3 7 7v11c-1 1-1 1-1 2-4-1-7-1-11-2-9 0-14 2-21 8h0c-1 0-2 1-3 1v-3c0-1 1-1 2-2h2v-1-1c0-1-1-1 0-2l1 1h2v-1c-2-4-5-8-9-11-1-2-2-2-3-4 0 0 1-1 1-2h1v-1h5c-1-2-3-4-5-4h-1c0-2-1-2-2-3 0-1-1-2-2-3-2 0-3-5-4-7-1-1-3-3-4-5h-2l-5-3 5 1c1 0 3 0 5 1h2 2l-1-2 1-1-2-2 3-1 1 1h4c-2-2-4-3-5-4l-3-2v-1l-3-3-1-1 2-2h0l-1-1c-2-2-4-3-5-4 1-1 1-2 2-3 1-3 2-5 4-7l1-2h0c1 2 2 3 4 4s3 2 4 3h1 1 0l4-5h1c0 2-1 2-1 4h3 1c2 0 3-1 4-1l2-3h1c1-1 1-1 1-2l2-2v1h2c3-3 5-6 10-7 3-1 6 1 10 0h0 1c1-3 4-7 7-8h0l8-7c1-2 3-3 4-5l-1-1z"></path><path d="M358 230l6 5h-1-4v-1-1c-1-1-1-2-1-3z" class="F"></path><path d="M364 227c1 2 2 4 5 5-2 1-3 2-4 4l-1-2 1-1v-1l-1-1c-1-2 0-3 0-4z" class="M"></path><path d="M374 171h1c1-3 4-7 7-8l-13 17h0c1-2 2-4 3-5h-1l-2-1h3v-1c-2-3-5-1-8-2h0c3-1 6 1 10 0h0z" class="E"></path><path d="M364 171h0c3 1 6-1 8 2v1h-3l2 1h1c-1 1-2 3-3 5h0c-3 7-7 14-9 22 0 3-1 7-2 11v-5h0c0-3 0-6-1-8l1-1v-4c-3-2-6-3-9-4l-1-1c-2-1-2-4-2-6l2-3h1c1-1 1-1 1-2l2-2v1h2c3-3 5-6 10-7z" class="W"></path><path d="M364 171h0c3 1 6-1 8 2v1h-3l2 1c-2 2-4 5-6 6l-5 5c-4-2-6-4-10-7l2-2v1h2c3-3 5-6 10-7z" class="G"></path><path d="M364 171h0c3 1 6-1 8 2v1h-3c-3 1-7 2-10 3-2 1-2 2-5 1 3-3 5-6 10-7z" class="W"></path><path d="M339 202c1-4 1-6 4-8 2-3 3-3 6-3 3 1 6 2 9 4v4l-1 1c1 2 1 5 1 8h0v5l-1 4 1 7c-4-2-6-5-9-8l-5-4h-1v-1h1c0-2-1-3-2-4s-2-1-3-3c-1-1-1-1 0-2z"></path><path d="M344 212c3 1 6 3 10 4l2 5 1-4 1 7c-4-2-6-5-9-8l-5-4z" class="E"></path><path d="M339 202c1-4 1-6 4-8 2-3 3-3 6-3 3 1 6 2 9 4v4l-1 1c0-2-1-2-2-3-3-2-7-1-10 0 0 1-2 2-2 3v1c-1 1-1 2-1 2v4c-1-1-2-1-3-3-1-1-1-1 0-2z" class="B"></path><path d="M341 198c1-2 2-3 4-3h0v1 1c0 1-2 2-2 3l-2-2z" class="R"></path><path d="M341 198l2 2v1c-1 1-1 2-1 2v4c-1-1-2-1-3-3-1-1-1-1 0-2l1 1c0-2 0-3 1-5h0z" class="G"></path><defs><linearGradient id="An" x1="340.782" y1="210.487" x2="334.854" y2="183.471" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#292a2e"></stop></linearGradient></defs><path fill="url(#An)" d="M323 181l1-2h0c1 2 2 3 4 4s3 2 4 3h1 1 0l4-5h1c0 2-1 2-1 4h3 1c2 0 3-1 4-1 0 2 0 5 2 6l1 1c-3 0-4 0-6 3-3 2-3 4-4 8-1 1-1 1 0 2 1 2 2 2 3 3s2 2 2 4h-1v1l-16-14-4-2h0l-1-1c-2-2-4-3-5-4 1-1 1-2 2-3 1-3 2-5 4-7z"></path><path d="M323 181l1-2h0c1 2 2 3 4 4l2 4c1 1 1 1 1 3v1l1 1-1 1v1 2l-1 1-2-2-1 1v2l-4-2h0l-1-1c-2-2-4-3-5-4 1-1 1-2 2-3 1-3 2-5 4-7z" class="G"></path><path d="M325 189c0 2 1 4 0 6l-2 1-1-1 3-6z" class="E"></path><path d="M331 194l-1-1h-1-1l1-2 1-1c0-1 0-2-1-3h1c1 1 1 1 1 3v1l1 1-1 1v1z" class="I"></path><path d="M323 181s1 1 1 2c1 1 3 0 2 3l-1 3-3 6c-2-2-4-3-5-4 1-1 1-2 2-3 1-3 2-5 4-7z" class="N"></path><path d="M323 196l4 2 16 14h1l5 4-1 2h-2l1 2c2 2 5 4 7 6l4 4c0 1 0 2 1 3v1 1h4 1v-1l1 2v1c3 1 5 3 7 7v11c-1 1-1 1-1 2-4-1-7-1-11-2-9 0-14 2-21 8h0c-1 0-2 1-3 1v-3c0-1 1-1 2-2h2v-1-1c0-1-1-1 0-2l1 1h2v-1c-2-4-5-8-9-11-1-2-2-2-3-4 0 0 1-1 1-2h1v-1h5c-1-2-3-4-5-4h-1c0-2-1-2-2-3 0-1-1-2-2-3-2 0-3-5-4-7-1-1-3-3-4-5h-2l-5-3 5 1c1 0 3 0 5 1h2 2l-1-2 1-1-2-2 3-1 1 1h4c-2-2-4-3-5-4l-3-2v-1l-3-3-1-1 2-2z" class="B"></path><path d="M336 261c0-1 1-1 2-2h2v-1l1 2-2 3c-1 0-2 1-3 1v-3z" class="T"></path><path d="M364 234l1 2v1h-2-1l1 1c1 2 2 3 3 4v1h-1l-1-2c-1 2 1 3 0 5-1-1-2-2-2-3h0l-1 1 1 1-1 1 1 1v4c0 1 0 2-1 2h0v-1c0-1 0-2-1-3l-1 1v-2l-3-9h1c1 2 1 5 2 8h1c0-4-2-7-2-11l1-1h4 1v-1z" class="g"></path><path d="M333 238v-1h5l4 5h0l2 4c1 0 1 1 1 2s1 2 1 3l-3 2c-3-5-7-10-10-15z" class="X"></path><path d="M339 235c1 2 2 3 4 5v1c2 1 3 4 4 5h1l-4-6c0-1 0 0 1-1l3 5 1-1c1 2 1 3 2 5v1c1 1 1 2 1 4l1-1c2 0 1 1 2 1h3l1-1v1c-3 1-7 0-10 2l-2 1c-1 0 0 0-1-1h0-2l-1-2 3-2c0-1-1-2-1-3s0-2-1-2l-2-4h0c0-1 0-1-1-2s-2-3-2-5z" class="O"></path><path d="M346 251c1 1 1 3 1 5-1 0 0 0-1-1h0-2l-1-2 3-2z" class="U"></path><path d="M339 235c1 2 2 3 4 5v1c2 1 3 4 4 5h1l-4-6c0-1 0 0 1-1l3 5c2 3 3 6 3 9h-2c-2-1-4-9-7-11h0c0-1 0-1-1-2s-2-3-2-5z" class="T"></path><path d="M348 236v-1h1l1 3 4 5c0-2-1-2-1-3-1-1-1-1-2-3l2-1c2 5 5 10 5 15l1 1-1 1h-3c-1 0 0-1-2-1l-1 1c0-2 0-3-1-4v-1c-1-2-1-3-2-5l-2-2v-1-1c0-1 1-2 1-3z" class="M"></path><path d="M348 236l3 5-1 1c-1-1-2-1-2-2l-1 1v-1-1c0-1 1-2 1-3zm5 16v-2-2c1 0 1 1 3 1h1l1 2 1 1-1 1h-3c-1 0 0-1-2-1z" class="b"></path><path d="M347 220c2 2 5 4 7 6l4 4c0 1 0 2 1 3v1l-1-1-4 1c-1 1-1 1-1 2l-2 1c1 2 1 2 2 3 0 1 1 1 1 3l-4-5-1-3-1-2v-3c-1-4-1-7-1-10z" class="D"></path><path d="M351 228c3 1 4 4 7 5l-4 1-2-2v-1c-1-1-1-2-1-3z" class="b"></path><path d="M350 238c2-4-2-5-1-8l1-2h1c0 1 0 2 1 3v1l2 2c-1 1-1 1-1 2l-2 1c1 2 1 2 2 3 0 1 1 1 1 3l-4-5z" class="H"></path><path d="M363 238l-1-1h1 2c3 1 5 3 7 7v11c-1 1-1 1-1 2-4-1-7-1-11-2l1-2c1 0 1-1 1-2v-4l-1-1 1-1-1-1 1-1h0c0 1 1 2 2 3 1-2-1-3 0-5l1 2h1v-1c-1-1-2-2-3-4z" class="D"></path><path d="M363 238c2 0 5 1 6 3 1 1 2 3 2 5l-1 2c-1-1-2-3-3-4 0-1 0-1-1-2h0c-1-1-2-2-3-4z" class="O"></path><path d="M362 251v-4l-1-1 1-1-1-1 1-1h0c0 1 1 2 2 3 1-2-1-3 0-5l1 2h1l3 6v1h-2v3l1 1v1c-1 0-2 0-2-1-1-2-1-4-1-5h-1c0 2 1 4 1 5-1 0-1 0-2-1v-3l-1 1z" class="J"></path><path d="M323 196l4 2 16 14h1l5 4-1 2h-2l1 2c0 3 0 6 1 10v3l1 2h-1v1c0 1-1 2-1 3v1 1l2 2-1 1-3-5c-1 1-1 0-1 1l4 6h-1c-1-1-2-4-4-5v-1c-2-2-3-3-4-5 0 2 1 4 2 5s1 1 1 2l-4-5c-1-2-3-4-5-4h-1c0-2-1-2-2-3 0-1-1-2-2-3-2 0-3-5-4-7-1-1-3-3-4-5h-2l-5-3 5 1c1 0 3 0 5 1h2 2l-1-2 1-1-2-2 3-1 1 1h4c-2-2-4-3-5-4l-3-2v-1l-3-3-1-1 2-2z" class="K"></path><path d="M333 221c-3-3-7-5-11-7 7 1 15 4 19 10h1c1 2 1 5 1 7v-1l-6-6c-1-1-3-2-4-3z" class="U"></path><path d="M345 218h1l1 2c0 3 0 6 1 10v3l1 2h-1v1c0 1-1 2-1 3l-1-2c-1-2-2-4-1-5-2-4-1-7-2-10v-2l2-2z" class="g"></path><defs><linearGradient id="Ao" x1="339.144" y1="212.669" x2="330.205" y2="216.619" xlink:href="#B"><stop offset="0" stop-color="#2b2c2a"></stop><stop offset="1" stop-color="#393840"></stop></linearGradient></defs><path fill="url(#Ao)" d="M333 209c1 1 3 2 4 4 1 1 2 2 2 3 1 1 1 1 3 2v1 2l-2-2h-2c-1-1-1-1-2-3-1 0-1-1-2-1l-3-1c-1 0-2 0-2-1v-2h-2l-2-2 3-1 1 1h4z"></path><path d="M333 209c1 1 3 2 4 4l-2-1c-3 0-4-1-6-3h4z" class="G"></path><path d="M323 196l4 2 16 14h1l5 4-1 2h-2-1l-2 2v2h0l-1-1v-2-1c-2-1-2-1-3-2 0-1-1-2-2-3-1-2-3-3-4-4-2-2-4-3-5-4l-3-2v-1l-3-3-1-1 2-2z" class="l"></path><path d="M325 202c7 4 14 10 20 16l-2 2v2h0l-1-1v-2-1c-2-1-2-1-3-2 0-1-1-2-2-3-1-2-3-3-4-4-2-2-4-3-5-4l-3-2v-1z" class="D"></path><path d="M327 225v-1c-1-4-3-6-6-9l3 1c1 1 2 1 3 1v1c2 0 4 3 6 3h0c1 1 3 2 4 3 1 2 3 4 4 7h1v3h0c1 1 1 2 2 3l1 2c-1 1-1 0-1 1l4 6h-1c-1-1-2-4-4-5v-1c-2-2-3-3-4-5 0 2 1 4 2 5s1 1 1 2l-4-5c-1-2-3-4-5-4h-1c0-2-1-2-2-3 0-1-1-2-2-3l-1-2z" class="e"></path><path d="M327 225l3-1c1 1 2 1 3 3l6 8h0c0 2 1 4 2 5s1 1 1 2l-4-5c-1-2-3-4-5-4h-1c0-2-1-2-2-3 0-1-1-2-2-3l-1-2z" class="M"></path><path d="M638 150h105c4 1 8 0 12 0h20 5s0 1 1 1c-6 0-10 2-15 5h0c0 2-2 3-3 4l-7 5c0 1 0 1 1 1 2-1 3-1 5-1l6-2h1c4 0 7-2 10-3 1-1 1-1 2-1-1 1-3 3-4 5-2 1-2 2-2 4l-3 8v2h3-1l-1 3c2-1 5-2 7-3h1l1 1c-4 1-7 3-11 5l-19 12-7 5h0l2 1h1l-6 3c-5 2-10 8-14 12-3 3-5 6-8 8-1 2-2 3-3 4l-1 1c-1 0-1 1-2 1h-1l-2 2h-1l-1 1h-1c1 2 1 3 0 5v1h0v3l1 1-1 1v1c-1 2-3 4-5 6l-1 1c-2-1-3 0-5-1-1-1-1 1-2 1-1-1-2-1-2-1-1 1-2 2-2 4 1 1 1 2 2 3h-1c-6-4-14-4-21-4v-1c1 0 3-1 4-1l-1-1c0-3 1-6 1-9l1-2 2-2h0l-3-1c1-1 2-1 2-1v-2c-3 1-7 0-10 0l4-4h-1c-1 0-2 2-2 2-1 0-2-1-3-1-1 1-1 1-2 0l3-3h0c1-2 2-4 2-7l-1-8v-3c1-1 1-2 2-3v-1h1l1 2v-1c-1-6-2-11-4-16-3-5-5-12-9-16-1-3-3-6-5-8-1-1-3-4-4-5l-3-3-2-3-4-3-2-2v-2z" class="Z"></path><path d="M731 172c1 1 3 2 4 3l2 2-1 3v1 1h-1v-3c-1-1-1-1-1-2l-3 2v-1l2-2-2-2v-2z" class="C"></path><path d="M731 172h-1c-2-2-5-2-8-2h0 0c2 0 4-1 6 0h0l-1-1v-1l1 1c3 1 6 2 8 4l1 2h-2c-1-1-3-2-4-3z" class="m"></path><path d="M675 153c2 1 4 1 6 2h1v-1c1-1 1-2 3-2 3 0 10 0 12 1l1 1c-4-1-12-2-15 0v1h0-8v-2z" class="Q"></path><path d="M683 155h0v-1c3-2 11-1 15 0 1 0 1 1 1 2h-1l-2 2h-2c-1-1-2-1-3-2-3 0-5 0-8-1z" class="D"></path><path d="M766 156c0 2-2 3-3 4l-7 5c-5 2-10 5-14 8-1 1-3 2-5 2 1-2 6-4 8-6 7-4 13-9 21-13z" class="V"></path><path d="M647 159c5-2 11-4 17-5h1c3-1 6-1 10-1v2c-7 1-15 2-20 5l2 1-5 2h-3l-3-3 1-1z" class="N"></path><path d="M647 159l2 2c2 0 4 0 6-1l2 1-5 2h-3l-3-3 1-1z" class="E"></path><path d="M675 155h8c3 1 5 1 8 1 1 1 2 1 3 2v1h-4-2c-8-1-20-1-28 1-1 0-2 1-3 1l-2-1c5-3 13-4 20-5z" class="Z"></path><path d="M699 156v1c3-1 5-3 8-4 2-1 3-1 5 0 0 2-1 2-3 3l1 1c2-1 3-1 6-1h0c2-2 4-2 6-3v1l2 4h2l1-1-1-1h1v2c-2 1-2 2-4 3 2 1 4 1 6 1h1c1-1 2-3 4-3h0c-2 3-5 5-9 7l-10 2c-4 0-10 0-14-1-2 0-3-1-5-1h-2l-1-1 1-1v-1l1-1h3c-1-1-2-1-3-1l-1-1c-1 0-2 0-3-1h-1 4v-1h2l2-2h1z" class="C"></path><path d="M710 157c-1 1-2 2-4 3h-4l7-4 1 1z" class="a"></path><path d="M703 163l12-3c1 0 3-1 5-1v1c-2 0-4 1-5 1 1 1 1 0 3 1l1 1h9v1c-1 0 0 0-1 1l-2 1-10 2c-4 0-10 0-14-1-2 0-3-1-5-1h-2l-1-1 1-1v-1l1-1h3c1 0 3 0 5 1z" class="K"></path><path d="M698 162c1 0 3 0 5 1-3 1-6 1-9 1v-1l1-1h3z" class="G"></path><path d="M660 160c8-2 20-2 28-1h2 1c1 1 2 1 3 1l1 1c1 0 2 0 3 1h-3l-1 1v1l-1 1 1 1v1c2 1 5 3 7 5 2 1 3 2 5 4l-8 1-8 2h-2c-1 0-2 1-3 0l-1 1h0v-1l-1 1-1-1h-1l-2 1-1-1h-1c0 1-1 2-1 3l-2 1c-1 2-3 3-5 4-1 0-1-1-2-3l-1-1c-1-2-1-3-3-4h0v1 1c0 2 2 4 3 5s2 4 1 6c-3-5-5-12-9-16-1-3-3-6-5-8-1-1-3-4-4-5h3l5-2c1 0 2-1 3-1z" class="b"></path><path d="M649 163h3l3 3h4c-2 1-5 1-6 2-1-1-3-4-4-5z" class="C"></path><path d="M664 165c-2-1-2 0-3 0l1-1v-1h-1 0c4-1 8-2 12-2 1 1 2 3 3 4-1 0-2 1-4 1l-3 1-1-1h-2c-1-1-1-1-2-1z" class="J"></path><path d="M664 165c1 0 1 0 2 1h2l1 1 3-1h3c0 1-1 2-1 2l1 1 2-1v1c-1 0-2 1-2 2v1h-2-3-1c-3-1-7-1-10-1h0c1-1 2-1 4-1-1-1-2-2-2-4l3-1z" class="U"></path><path d="M672 166h3c0 1-1 2-1 2l-8 1c1-1 2-2 3-2l3-1z" class="a"></path><path d="M664 165c1 0 1 0 2 1h2l1 1c-1 0-2 1-3 2s-2 1-3 1c-1-1-2-2-2-4l3-1z" class="M"></path><path d="M660 160c8-2 20-2 28-1h2 1c1 1 2 1 3 1l1 1c1 0 2 0 3 1h-3l-1 1v1l-1 1 1 1v1c-3 0-6-1-9-1h-2 0c-1-2-4-1-6-1 1-1 1-1 2-1 1-1 2-2 4-2h0v-1l-9-1h-1c-3-1-5 0-7 0h-6z" class="S"></path><path d="M688 159h2 1c1 1 2 1 3 1l1 1c1 0 2 0 3 1h-3l-1 1v1l-1 1 1 1v1c-3 0-6-1-9-1h-2 0l4-1v-1h-2v-1l5-1 1-1-3-2z" class="N"></path><path d="M676 165h1c2 0 5-1 6 1h0 2l4 1 2 2-3-1c-1 1-3 1-4 1-1 1-1 1-1 2 1 0 1 1 2 1 0 1-1 1-2 1 2 3 2 4 1 7h0v-1l-1 1-1-1h-1l-2 1-1-1c-1-2-2-3-4-5-1-1-2-1-4-2h3 2v-1c0-1 1-2 2-2v-1l-2 1-1-1s1-1 1-2h-3c2 0 3-1 4-1z" class="M"></path><path d="M674 174c3 0 3 0 4 2 1 1 1 0 0 1l1 2h0l1-1v-2l1 3-2 1-1-1c-1-2-2-3-4-5z" class="g"></path><path d="M682 172c-1 0-1-1-2-1-1 1-1 1-2 1-1-1-1-1-1-2 1-1 2-1 4-1l2 2c1 0 1 1 2 1 0 1-1 1-2 1l-1-1z" class="U"></path><path d="M682 172l1 1c2 3 2 4 1 7h0v-1l-1 1-1-1h-1l-1-3v-4h2z" class="C"></path><path d="M676 165h1c2 0 5-1 6 1h0 2l4 1 2 2-3-1-2-1c-3 0-7-1-9 1l-2 1-1-1s1-1 1-2h-3c2 0 3-1 4-1z" class="O"></path><defs><linearGradient id="Ap" x1="696.543" y1="178.77" x2="689.794" y2="166.318" xlink:href="#B"><stop offset="0" stop-color="#414348"></stop><stop offset="1" stop-color="#626467"></stop></linearGradient></defs><path fill="url(#Ap)" d="M685 166c3 0 6 1 9 1 2 1 5 3 7 5 2 1 3 2 5 4l-8 1-8 2h-2c-1 0-2 1-3 0l-1 1c1-3 1-4-1-7 1 0 2 0 2-1-1 0-1-1-2-1 0-1 0-1 1-2 1 0 3 0 4-1l3 1-2-2-4-1z"></path><path d="M688 179l-2-1v-1l1-1 3 3h-2z" class="B"></path><path d="M685 166c3 0 6 1 9 1 2 1 5 3 7 5-4 0-7-2-10-3l-2-2-4-1z" class="S"></path><path d="M659 166h1 0c-1 1-2 3-3 4l1 1h1c3 0 7 0 10 1h1c2 1 3 1 4 2 2 2 3 3 4 5h-1c0 1-1 2-1 3l-2 1c-1 2-3 3-5 4-1 0-1-1-2-3l-1-1c-1-2-1-3-3-4h0v1 1c0 2 2 4 3 5s2 4 1 6c-3-5-5-12-9-16-1-3-3-6-5-8 1-1 4-1 6-2z" class="I"></path><path d="M674 183h-1c-1-1-1-1-1-2l-1-1 1-1h1c1 1 2 1 3 3l-2 1z" class="B"></path><path d="M659 171c3 0 7 0 10 1-1 1-1 1-1 3 0 0 1 1 1 2-1 0-2 0-3-1l1-2c-2-1-6 1-8 0-1-1-1-1-1-3h1z" class="W"></path><path d="M706 176c1 1 2 2 2 3l2 2c1 3 3 7 3 9v1 1l-6 5-18 14-11 10c-1 1-4 3-5 3 0-1 0-4-1-6 0-3 0-7-1-10-1-6-2-11-4-16 1-2 0-5-1-6s-3-3-3-5v-1-1h0c2 1 2 2 3 4l1 1c1 2 1 3 2 3 2-1 4-2 5-4l2-1c0-1 1-2 1-3h1l1 1 2-1h1l1 1 1-1v1h0l1-1c1 1 2 0 3 0h2l8-2 8-1z" class="m"></path><path d="M683 190l4 1-3 3v1h0c-4 0-7 0-11 2v1c-1-2 0-2 1-4l6-3c1 0 2-1 3-1z" class="J"></path><path d="M683 185v1l1-1h1c2 1 3 1 5 4-2 0-5 0-7 1-1 0-2 1-3 1l-6 3c0-1 0-1 1-2s2-2 2-3l1-1h0 1c2-1 3-2 4-3z" class="I"></path><path d="M706 176c1 1 2 2 2 3l2 2c1 3 3 7 3 9v1 1l-6 5-18 14c0-1 0-3 1-4 0-3 0-5-1-7l-2-2-3-3h0v-1l3-3-4-1c2-1 5-1 7-1-2-3-3-3-5-4h-1l-1 1v-1c0-1 1-2 1-3v-2h0l1-1c1 1 2 0 3 0h2l8-2 8-1z" class="E"></path><path d="M696 195h2c0 1 0 2-1 3h-2v2h-1l1-5h1z" class="I"></path><path d="M690 194c1 2 2 3 2 5l-3 1-2-2c1-2 2-3 3-4z" class="G"></path><path d="M687 191l3 3c-1 1-2 2-3 4l-3-3h0v-1l3-3z" class="V"></path><path d="M692 199c1 3 1 7-1 9v1c-1-1-1-1-1-2 0-3 0-5-1-7l3-1z" class="R"></path><path d="M694 186c2-1 6-2 9-2h1l1-1v1l-1 1-1 2h0l2-1 1 1h-1v3h-1v-2c-1 2-1 3-3 4l-1-1h-1v2h-1c-1-2-3-4-4-7z" class="B"></path><path d="M707 179l1 1c-2 1-4 2-5 4-3 0-7 1-9 2v-1l-1 1 3 9h-1c-2-3-3-5-5-6-2-3-3-3-5-4h-1l-1 1v-1c0-1 1-2 1-3 1 1 3 2 4 2 2 1 4 0 6 0 4 1 8-2 12-4l1-1z" class="F"></path><path d="M707 179h1l2 2c1 3 3 7 3 9v1 1l-6 5c1-1 1-2 1-3 1-1 1-1 1-2-1-2-2-4-3-5l-1-1-2 1h0l1-2 1-1v-1l-1 1h-1c1-2 3-3 5-4l-1-1z" class="V"></path><path d="M707 179h1l2 2-2 2 1 3h0c-2 0-2 0-3-1h-2l1-1v-1l-1 1h-1c1-2 3-3 5-4l-1-1z" class="g"></path><path d="M706 176c1 1 2 2 2 3h-1l-1 1c-4 2-8 5-12 4-2 0-4 1-6 0-1 0-3-1-4-2v-2h0l1-1c1 1 2 0 3 0h2l8-2 8-1z" class="J"></path><path d="M706 176c1 1 2 2 2 3h-1l-1 1c-2-1-5-1-8-2v-1l8-1z" class="M"></path><path d="M768 163h1c4 0 7-2 10-3 1-1 1-1 2-1-1 1-3 3-4 5-2 1-2 2-2 4l-3 8v2h3-1l-1 3c2-1 5-2 7-3h1l1 1c-4 1-7 3-11 5l-19 12-7 5h0l2 1h1l-6 3c-5 2-10 8-14 12-3 3-5 6-8 8-1 2-2 3-3 4l-1 1c-1 0-1 1-2 1h-1l-2 2h-1l-1 1h-1c1 2 1 3 0 5v1h0v3l1 1-1 1v1c-1 2-3 4-5 6l-1 1c-2-1-3 0-5-1-1-1-1 1-2 1-1-1-2-1-2-1-1 1-2 2-2 4 1 1 1 2 2 3h-1c-6-4-14-4-21-4v-1c1 0 3-1 4-1l-1-1c0-3 1-6 1-9l1-2 2-2h0l-3-1c1-1 2-1 2-1v-2c-3 1-7 0-10 0l4-4h-1c-1 0-2 2-2 2-1 0-2-1-3-1-1 1-1 1-2 0l3-3h0c1-2 2-4 2-7l-1-8v-3c1-1 1-2 2-3v-1h1l1 2v-1c1 3 1 7 1 10 1 2 1 5 1 6 1 0 4-2 5-3l11-10 18-14 6-5 18-13 3-2c0 1 0 1 1 2v3h1v-1-1l1-3-2-2h2 0c2 0 4-1 5-2 4-3 9-6 14-8 0 1 0 1 1 1 2-1 3-1 5-1l6-2z" class="F"></path><path d="M743 181v1h0c-1 1-1 2-2 2l1 1c1-2 3-4 5-4-3 3-6 5-8 8-3 4-6 8-9 11 0-5 4-7 6-11 0 0 0-1-1-1l1-2 7-5z" class="P"></path><path d="M743 181v-1c1-2 6-5 8-6 2 0 4-2 7-2 1-1 1-1 2-1 3-2 5-2 8-2l1 3-1 1c-1-1-2-3-3-2h-3c-3 1-6 4-7 5-3 2-5 4-8 5-2 0-4 2-5 4l-1-1c1 0 1-1 2-2h0v-1z" class="T"></path><path d="M746 188c4-4 9-7 15-8h2l2-1h1c0-1-1-1-1-2-2 0-2 0-3-2v-2l1 1 3 3c1-1 2-1 3-3 1 1 2 1 3 3v-1 2h3-1l-1 3-6 3c-1-1-3-1-4 0h0l-3-1c-2 0-5 2-7 2-2 1-4 3-7 3z" class="g"></path><path d="M772 177v-1 2h3-1l-1 3-6 3c-1-1-3-1-4 0l5-4c2-1 3-2 4-3z" class="Y"></path><path d="M772 177v-1 2h3-1c-1 1-2 1-3 2h-3c2-1 3-2 4-3z" class="d"></path><path d="M728 192c1 1 1 1 1 2h0c2-1 4-8 4-10h0 1l-1 6h0l2-2c1 0 1 1 1 1-2 4-6 6-6 11-2 2-5 5-8 7-2 2-6 3-9 4-9 2-17 5-23 12h-1c-1-2 0-4 0-5l3-3 8-4 17-5c6-2 9-9 11-14z" class="Y"></path><path d="M768 163h1c4 0 7-2 10-3 1-1 1-1 2-1-1 1-3 3-4 5-2 1-2 2-2 4l-3 8v1c-1-2-2-2-3-3l-1-1 1-1-1-3c-3 0-5 0-8 2-1 0-1 0-2 1-3 0-5 2-7 2-2 1-7 4-8 6v1l-7 5-1 2-2 2h0l1-6h0l1-5v3h1v-1-1l1-3-2-2h2 0c2 0 4-1 5-2 4-3 9-6 14-8 0 1 0 1 1 1 2-1 3-1 5-1l6-2z" class="J"></path><path d="M777 164c-2 1-2 2-2 4l-3 8v1c-1-2-2-2-3-3l-1-1 1-1-1-3h1c0-1 1-1 1-1 3-2 4-3 7-4z" class="O"></path><path d="M736 180c1-2 2-3 3-4s6-3 8-3c1-1 2-1 3-1-2 2-5 4-8 7-1 1-4 2-5 3 0 1-1 3-1 4l-1 2-2 2h0l1-6h0l1-5v3h1v-1-1z" class="N"></path><path d="M756 165c0 1 0 1 1 1 2-1 3-1 5-1l-9 6c-1 0-2 0-3 1-1 0-2 0-3 1-2 0-7 2-8 3s-2 2-3 4l1-3-2-2h2 0c2 0 4-1 5-2 4-3 9-6 14-8z" class="R"></path><path d="M731 179l3-2c0 1 0 1 1 2l-1 5h0-1 0c0 2-2 9-4 10h0c0-1 0-1-1-2-2 5-5 12-11 14l-17 5-8 4-3 3c-2 1-4 3-5 5l-2 4c-1 3-4 5-5 8-3 1-7 0-10 0l4-4h-1c-1 0-2 2-2 2-1 0-2-1-3-1-1 1-1 1-2 0l3-3h0c1-2 2-4 2-7l-1-8v-3c1-1 1-2 2-3v-1h1l1 2v-1c1 3 1 7 1 10 1 2 1 5 1 6 1 0 4-2 5-3l11-10 18-14 6-5 18-13z" class="i"></path><path d="M668 222c1 1 2 6 2 7v1h1c1-1 3-2 4-4h1c1 0 2-2 3-3s1-1 3-1c-3 2-5 5-8 7-1 0-2 1-3 2h-1c-1 0-2 2-2 2-1 0-2-1-3-1-1 1-1 1-2 0l3-3h0c1-2 2-4 2-7z" class="M"></path><path d="M671 208c1 3 1 7 1 10 1 2 1 5 1 6-1 1-1 1-2 1-1-1 0-5 0-7h-1c-1 1-1 1-1 3v-1c0-3 0-4-2-6v-3c1-1 1-2 2-3v-1h1l1 2v-1z" class="T"></path><path d="M717 194h1c0 2-1 3-2 4-3 3-7 5-10 8-2 1-5 3-6 5l-8 4-1-1c3-3 6-6 10-8l16-12z" class="h"></path><path d="M691 214l1 1-3 3c-2 1-4 3-5 5l-2 4c-1 3-4 5-5 8-3 1-7 0-10 0l4-4c1-1 2-2 3-2 3-2 5-5 8-7l9-8z" class="F"></path><defs><linearGradient id="Aq" x1="713.453" y1="192.081" x2="719.199" y2="203.136" xlink:href="#B"><stop offset="0" stop-color="#2e2f30"></stop><stop offset="1" stop-color="#47494e"></stop></linearGradient></defs><path fill="url(#Aq)" d="M717 194l15-13-4 10v1c-2 5-5 12-11 14l-17 5c1-2 4-4 6-5 3-3 7-5 10-8 1-1 2-2 2-4h-1z"></path><path d="M773 181c2-1 5-2 7-3h1l1 1c-4 1-7 3-11 5l-19 12-7 5h0l2 1h1l-6 3c-5 2-10 8-14 12-3 3-5 6-8 8-1 2-2 3-3 4l-1 1c-1 0-1 1-2 1h-1l-2 2h-1l-1 1h-1c1 2 1 3 0 5v1h0v3l1 1-1 1v1c-1 2-3 4-5 6l-1 1c-2-1-3 0-5-1-1-1-1 1-2 1-1-1-2-1-2-1-1 1-2 2-2 4 1 1 1 2 2 3h-1c-6-4-14-4-21-4v-1c1 0 3-1 4-1l-1-1c0-3 1-6 1-9l1-2 2-2h0l-3-1c1-1 2-1 2-1v-2c1-3 4-5 5-8l2-4c1-2 3-4 5-5 0 1-1 3 0 5h1c0 1-7 10-7 13l5-5c2-3 4-7 8-10 5-4 12-5 18-7l-11 13h0l6-6c2-2 4-5 6-8 3-2 8-3 11-5 7-6 12-15 19-19l1-1c3 0 5-2 7-3 2 0 5-2 7-2l3 1h0c1-1 3-1 4 0l6-3z" class="M"></path><path d="M711 225v-2c1-2 1-2 2-3l2 1-4 4z" class="J"></path><path d="M677 235c1-3 4-5 5-8l2-4 2 4c-2 3-3 6-6 8h-3z" class="b"></path><path d="M716 221c2 0 1 0 2 1h1c2-1 3-2 3-3 1-1 1-2 2-2 4-2 6-5 9-8l1 1c-2 1-5 4-6 6v1c-3 3-5 6-8 8-2 0-2 0-3-2l1-1h-1l-1-1z" class="J"></path><path d="M715 221h0l3-3c1-3 3-4 5-6 1-1 2-3 3-4 5-3 8-8 13-10-3 3-6 5-10 8-2 2-3 5-5 7-3 2-5 3-6 6 0 1-1 2-2 2l1 1h1l-1 1-2 2h-1l-2 1c-3 2-5 5-8 7h0l-1-1 8-7 4-4z" class="K"></path><defs><linearGradient id="Ar" x1="714.685" y1="223.392" x2="709.713" y2="233.778" xlink:href="#B"><stop offset="0" stop-color="#4e5155"></stop><stop offset="1" stop-color="#686a6d"></stop></linearGradient></defs><path fill="url(#Ar)" d="M704 233c3-2 5-5 8-7l2-1h1l2-2c1 2 1 2 3 2-1 2-2 3-3 4l-1 1c-1 0-1 1-2 1h-1l-2 2h-1l-1 1h-1c-2 0-3 0-4-1z"></path><path d="M773 181c2-1 5-2 7-3h1l1 1c-4 1-7 3-11 5l-19 12-7 5c-3 1-6 2-8 3 1-1 3-2 4-4h1c0-2 1-2 2-4h-1v-1l3-2h-1c-2 1-3 2-5 3 0-2 4-4 5-6v-1l1-1c3 0 5-2 7-3 2 0 5-2 7-2l3 1h0c1-1 3-1 4 0l6-3z" class="K"></path><path d="M763 184c1-1 3-1 4 0l-5 2c-1 0-1-1-2-1l3-1h0z" class="d"></path><path d="M744 196h0c1-2 3-4 5-5l2 2-9 7c0-2 1-2 2-4z" class="X"></path><path d="M749 191c4-1 7-5 11-6 1 0 1 1 2 1-4 3-8 5-11 7l-2-2z" class="Y"></path><path d="M746 188c3 0 5-2 7-3 2 0 5-2 7-2l3 1-3 1c-4 1-7 5-11 6-2 1-4 3-5 5h0-1v-1l3-2h-1c-2 1-3 2-5 3 0-2 4-4 5-6v-1l1-1zm-56 50h1v-2h-1l2-2v1h1l1 1 5-5h0 1l-5 5-2 2h1l1-1v1c-1 1-1 3-2 4l6-6c1-1 1-2 3-3v1 1l2-2h0c1 1 2 1 4 1 1 2 1 3 0 5v1h0v3l1 1-1 1v1c-1 2-3 4-5 6l-1 1c-2-1-3 0-5-1-1-1-1 1-2 1-1-1-2-1-2-1-1 1-2 2-2 4 1 1 1 2 2 3h-1c-6-4-14-4-21-4v-1c1 0 3-1 4-1l-1-1c0-3 1-6 1-9l1-2 2-2h0c3-1 9-2 12-1z" class="U"></path><path d="M678 239c3-1 9-2 12-1-2 2-4 6-5 9 1 1 2 2 3 2l3-4c0 2-1 4-2 6-1 1-1 2-1 4 1 0 2-2 3-2l7-10c1-1 2-3 4-4 0 0 1 1 2 1l-1 1-1-1c-4 3-7 8-9 12-1 1-2 2-2 4 1 1 1 2 2 3h-1c-6-4-14-4-21-4v-1c1 0 3-1 4-1l-1-1c0-3 1-6 1-9l1-2 2-2h0z" class="c"></path><path d="M688 249c0 1-1 2-2 3 0 1-1 1-1 2h-2c0-3 1-5 2-7 1 1 2 2 3 2z" class="J"></path><path d="M682 242c1-1 1-2 2-2 1-1 2-1 3-1v1c-1 3-2 6-4 9-1-3-1-4-1-7z" class="e"></path><path d="M678 239h2v1h2 0v2c0 3 0 4 1 7l-1 1-1 3c-2 0-2 0-3-1 0-2 1-4 1-7 1-1 1-2 1-4h0c-2 3-2 10-5 12l-1-1c0-3 1-6 1-9l1-2 2-2z" class="b"></path><defs><linearGradient id="As" x1="130.563" y1="369.582" x2="199.06" y2="311.855" xlink:href="#B"><stop offset="0" stop-color="#121315"></stop><stop offset="1" stop-color="#2c2e32"></stop></linearGradient></defs><path fill="url(#As)" d="M176 222c4 3 7 6 10 10 2 1 2 1 3 3s3 3 5 4l2 2 3 2h1c1-1 1-2 1-3l-2-2h2l3 1v-1c3 0 4 1 6 3v1l1 1h-1l2 4c1 0 1 1 2 2l1 1-2 2h-1-1l-2 2 6 14c0 2 0 2 1 3l2 2c1-1 2-1 4-1 0 1-1 2-2 2v2l2 2c-2 2-7 3-10 4-2 0-5 3-7 4h-1l-2 2c-2-1-3 0-5 0h-1l1 3-5 5-1 1c-2 2-3 6-4 9-1 1-3 2-3 4h1c0 2 1 2 2 3v2h1l-2 1h2c-3 2-5 1-7 4h-1l-1-1h0v1h-4c-1 1-1 1-1 2-1 1-1 1-1 2h0c-1 4-1 6 0 9l3 7c1-2 3-4 4-6h2l2 1-2 2c1 0 2 1 3 2 2-1 4-2 5-2h4 3 1l4 2 2 2 4 3 3 4-2 2h-5l-2-2c-1 1-1 2-1 2l-1 1h0 2v1h0v2c-1 0-2 1-3 2s-1 2-2 3c0 0-1 2-1 3h2c-1 2-1 3-1 5 1 0 2 1 3 1l1 1 1-2c2 0 6 0 8-2l1 1c0 3-1 5-2 7h1v-1h1c-3 6-9 11-15 15l-1 1c0 2 1 1 0 3h0l-1-2c-2 0-8 3-11 4h-2c-2 1-3 1-4 1-11 1-21-1-30-7-4-2-7-5-10-8l6-2c7-3 11-8 14-16 2-3 2-7 2-10-1-9-4-15-10-22-2-3-5-5-6-8-3-8-1-22 2-30l2-4c0-1 0 0-1-1v-1l3-6v-1l-1 1-1-1 1-3c1 0 2-1 2-1 1-1 1-1 1-2l2-2 1-1 2-3h-1v-1c0-2 0-3-1-4l-3 3h-1v3c-2 0-8-3-11-3h-2l-3-2v2c-1 0-2-1-3-1-3-3-4-6-6-9h3v-3-3c-1-4-1-9 0-13 2-1 2-1 3-2h2v1l2 2-1 1v1l2 2c1-1 2-1 3-2l2-1h6c2 0 5 1 7 0v-2c2 1 3 1 5 1l2 2c1 0 1 1 2 1 0-2-3-3-3-5 3 2 5 4 7 7l1-1 2 3h0l-2-4h0l-1-4c0-2 1-3 3-4v1c1-1 3-2 5-3 1 0 3 0 4 1 1 0 1 1 1 1h1c0-2-5-5-6-6 1-1 1-2 1-3z"></path><path d="M174 384h1c1 0 2 1 2 1 2 0 4 0 5 1v1c-2 1-4 0-6 0-1-1-2-1-2-2-1-1-1 0 0-1z" class="Z"></path><path d="M204 371h1c1 0 2 0 3 1 1 2 0 2 0 4l-2 1h0c-1-2-2-4-2-6z" class="AE"></path><path d="M164 371h0c1 2 1 3 1 4-2 1-4 1-6 2l-1-1c1-2 3-3 4-4 0-1 1-1 2-1z" class="K"></path><path d="M167 361l4 10-2 2c1 3 2 5 4 7 2 0 3 1 5 1h1 0c1 0 2 1 3 1-3 1-6 1-9 0-2-1-3-2-4-4l-1-1c-2-3-2-7-1-11v-3-2z" class="H"></path><path d="M172 295v1h0-1c-1 3-3 5-4 8-1 0-3 2-3 3l1 1h1 1 0v2c-3 4-2 6-2 11v9 5c-1-2 0-7-1-9v-6c-1-2-1-4 0-6 0-2 0-3 2-5h-1c-1 0-1 0-2 1-2 4 0 8-1 12-1 2 0 3 0 5v5c0 1 1 2 1 2v5l1 1v4c-1 0-1 0-1 1-2-2-2-5-2-7h1c1-2-2-19-2-22 0-8 7-13 10-20h1l1-1z" class="Q"></path><defs><linearGradient id="At" x1="191.412" y1="366.586" x2="176.721" y2="382.82" xlink:href="#B"><stop offset="0" stop-color="#464648"></stop><stop offset="1" stop-color="#5a5d62"></stop></linearGradient></defs><path fill="url(#At)" d="M198 371h1v2l1 2v2c-2 2-5 1-7 2-3 1-7 3-11 3-1 0-2-1-3-1h0-1c-2 0-3-1-5-1-2-2-3-4-4-7l2-2 3 6c7 0 13-2 19-4l5-2z"></path><path d="M198 371h1v2h-3-3l5-2z" class="K"></path><defs><linearGradient id="Au" x1="173.624" y1="319.209" x2="156.581" y2="305.045" xlink:href="#B"><stop offset="0" stop-color="#3f444d"></stop><stop offset="1" stop-color="#67686b"></stop></linearGradient></defs><path fill="url(#Au)" d="M170 282h0c1-2 1-3 2-5 0 2 0 6-1 8h0c0 3-1 6-1 9l1 2h-1c-3 7-10 12-10 20 0 3 3 20 2 22h-1c-2-8-5-16-5-25 0-1-1-3-1-4 1 0 2-2 2-2 2-1 4-3 5-5 3-3 5-7 6-10 2-3 2-6 2-10z"></path><path d="M166 276h1v4c-1 2-1 3-3 3l-9 3c1 1 0 1 1 2 2-1 5 0 7 0v1c-2 4-4 8-6 13-1 1-2 4-3 6v-1c0-1 0-2 1-3l1-3 1-1-1-1 1-1h0 0c1-1 2-2 2-3 1-1 1-2 2-3h0c0-2 1-2 1-3-2-1-4-1-6 0v2h0 1c0 2-1 5-2 6v-1h-1c-1 3-2 6-1 9v1 1l-1-2c-1 0-1 0-1-1l-1-1v6 5-2c0-1 0-5-1-6l-1 1v3c1 2 1 4 1 6l1 5v2c-1-2-1-4-1-6-1-2 0-4-1-5v-3c-1-3-1-6-1-9 1-3 1-5 2-8 1-4 3-8 5-11l2-2c2 0 3-1 5-3h1 1l-1 1v1c2-1 3-1 4-2z" class="D"></path><path d="M155 286h0c0-1 1-1 1-2v-1c1-1 3-3 5-4h1c1-1 3-2 4-1l1 2c-1 2-1 3-3 3l-9 3z" class="K"></path><path d="M158 260c3-1 4-2 6-2h2c1 7 4 14 4 21h-1v-1c-1-1-1-2-1-3l-1 1h-1c-1 1-2 1-4 2v-1l1-1h-1-1c-2 2-3 3-5 3l-2 2c-1-1-1-2-2-2l-5 8c0-1 0 0-1-1v-1l3-6v-1l-1 1-1-1 1-3c1 0 2-1 2-1 1-1 1-1 1-2l2-2 1-1 2-3h-1v-1c0-2 0-3-1-4l4-2v1z" class="F"></path><path d="M160 273h0l-1 2v1h1 1 0c-2 2-3 3-5 3 1-2 2-4 4-6z" class="H"></path><path d="M160 273v-1c1-1 1-1 1-2 1-2 2-3 3-4h1c2 3 0 4 0 6 1 1 1 2 1 4-1 1-2 1-4 2v-1l1-1h-1-1 0-1-1v-1l1-2h0z" class="S"></path><path d="M154 269c1 0 2-1 3-1h1 3c-3 4-7 7-9 11l-5 8c0-1 0 0-1-1v-1l3-6v-1l-1 1-1-1 1-3c1 0 2-1 2-1 1-1 1-1 1-2l2-2 1-1z" class="q"></path><path d="M158 268h3c-3 4-7 7-9 11l-5 8c0-1 0 0-1-1v-1l3-6c2-1 3-1 3-3 1-2 2-3 4-4l-1-1c1-2 2-2 3-3z" class="AB"></path><path d="M158 260c3-1 4-2 6-2l2 5c-1 2-4 3-5 5h-3-1c-1 0-2 1-3 1l2-3h-1v-1c0-2 0-3-1-4l4-2v1z" class="AJ"></path><path d="M158 259v1c1 1 1 2 1 3l-3 3h-1v-1c0-2 0-3-1-4l4-2z" class="q"></path><path d="M160 234c3 2 5 4 7 7l1-1 2 3h0c2 3 3 6 4 8l-1 2c1 4 2 7 3 11l2 10c0 4-1 8 0 11 1 1 1 2 1 3h1c0 2 1 3 1 5h-1-5 0l-1-1v-1l-1-1-1 5-1 1-1-2c0-3 1-6 1-9h0c1-2 1-6 1-8-1 2-1 3-2 5h0v-3c0-7-3-14-4-21v-2c-1-3-2-6-4-9l-1-1c2-4-2-4-2-7-1-1-4-2-5-2v-2c2 1 3 1 5 1l2 2c1 0 1 1 2 1 0-2-3-3-3-5z" class="N"></path><path d="M167 241l1-1 2 3h0c2 3 3 6 4 8l-1 2-6-12z" class="X"></path><path d="M178 285c1 1 1 2 1 3h1c0 2 1 3 1 5h-1-5c1-2 2-3 3-4-1-2 0-2 0-3v-1z" class="M"></path><path d="M169 253c3 6 5 13 6 20 1 2 1 4 1 6h-1v3c-3-3-2-7-3-10 0-6-1-11-3-16v-3z" class="T"></path><path d="M168 251l1 2v3c2 5 3 10 3 16 1 3 0 7 3 10v2c0 1-1 2-1 4-1 0-1 1-1 2l-1 5-1 1-1-2c0-3 1-6 1-9h0c1-2 1-6 1-8-1-7-2-14-4-21v-5z" class="J"></path><path d="M170 294c1-2 1-4 2-6 1 0 1-1 1-2s1-1 2-2c0 1-1 2-1 4-1 0-1 1-1 2l-1 5-1 1-1-2z" class="V"></path><path d="M159 239c3 2 5 4 7 8h0l2 4v5c2 7 3 14 4 21-1 2-1 3-2 5h0v-3c0-7-3-14-4-21v-2c-1-3-2-6-4-9l-1-1c2-4-2-4-2-7z" class="L"></path><path d="M159 239c3 2 5 4 7 8h0v1c-1 1-1 1 0 1 0 2 1 5 1 7-1-2-1-4-2-6s-1-2-3-3l-1-1c2-4-2-4-2-7z" class="g"></path><path d="M128 235c2-1 2-1 3-2h2v1l2 2-1 1v1l2 2c1-1 2-1 3-2l2-1h6c2 0 5 1 7 0 1 0 4 1 5 2 0 3 4 3 2 7l1 1c2 3 3 6 4 9v2h-2c-2 0-3 1-6 2v-1l-4 2-3 3h-1v3c-2 0-8-3-11-3h-2l-3-2v2c-1 0-2-1-3-1-3-3-4-6-6-9h3v-3-3c-1-4-1-9 0-13z" class="k"></path><path d="M141 243c3-3 5-2 9-2l1 1h-2c-2 1-2 1-3 1l-4 4v5l-2 2v-1l-2-2v-1c0-3 1-5 3-7z" class="f"></path><path d="M141 237h6l-4 2c-2 1-3 3-5 4l1 1c0-1 1-1 2-1-2 2-3 4-3 7v1h-2c-1-2-2-3-2-5v-4c1-1 1-2 0-4l2 2c1-1 2-1 3-2l2-1z" class="K"></path><path d="M134 238l2 2-1 1v3c1 1 0 1 1 1v3 2h1v-2c1 1 1 1 1 2v1h-2c-1-2-2-3-2-5v-4c1-1 1-2 0-4z" class="R"></path><path d="M146 243v2c1 0 3-1 4-1l1 1c1 0 1 0 2 1 0 2 1 3 1 5-1 2-1 4-2 5h-1c-1 1-2 1-3 0h-5c-1-1-2-1-3-2l2-2v-5l4-4z" class="M"></path><path d="M152 256l-1-1h0c0-2 0-4 1-5l2 1c-1 2-1 4-2 5z" class="H"></path><path d="M146 243v2c-1 1-1 1-2 3h1 2v1l-1 1-1 1c2 2 4 3 6 5-1 1-2 1-3 0h-5c-1-1-2-1-3-2l2-2v-5l4-4z" class="R"></path><path d="M140 254l2-2v-5c0 3 0 4 2 6l4 2h0v1h-5c-1-1-2-1-3-2z" class="J"></path><path d="M153 246v-3l-1-1c3 0 4 0 6 2 1 0 2 1 3 2l1 1c2 3 3 6 4 9v2h-2c-2 0-3 1-6 2v-1l-4 2-3 3c-4-2-10-2-13-5v-2h-1c1-1 2-2 3-2 1 1 1 1 3 1h0 5c1 1 2 1 3 0h1c1-1 1-3 2-5 0-2-1-3-1-5z" class="W"></path><path d="M158 259c-1-1-2-1-3-2l-1-1c2-2 2-5 3-7l-3-3 1-2h1c2 1 4 3 5 5 0 0 1 1 1 2s1 2 1 2v1c0 1 1 1 2 2h1v2h-2c-2 0-3 1-6 2v-1z" class="m"></path><path d="M128 235c2-1 2-1 3-2h2v1l2 2-1 1v1c1 2 1 3 0 4v4c0 2 1 3 2 5h2l2 2v1c1 1 2 1 3 2h0c-2 0-2 0-3-1-1 0-2 1-3 2h1v2c3 3 9 3 13 5h-1v3c-2 0-8-3-11-3h-2l-3-2v2c-1 0-2-1-3-1-3-3-4-6-6-9h3v-3-3c-1-4-1-9 0-13z" class="E"></path><path d="M134 246c0 2 1 3 2 5h2l2 2-2 2-1-1-1-1c-1-3-2-4-2-7z" class="F"></path><path d="M128 235c2-1 2-1 3-2h2v1c-2 3-3 5-3 8v4c1 3 1 5 2 8l1 1 1 2c1 3 3 5 5 7h-2l-3-2v2c-1 0-2-1-3-1-3-3-4-6-6-9h3v-3-3c-1-4-1-9 0-13z" class="v"></path><path d="M128 248l1 3c1 4 3 8 5 11v2c-1 0-2-1-3-1-3-3-4-6-6-9h3v-3-3z" class="y"></path><path d="M185 288l1 2h0l1-2c1-1 3-3 5-3l-1 2c-3 1-5 4-7 7 3 0 3-2 6-3l1 2 5-5 1 3-5 5-1 1c-2 2-3 6-4 9-1 1-3 2-3 4h1c0 2 1 2 2 3v2h1l-2 1h2c-3 2-5 1-7 4h-1l-1-1h0v1h-4c-1 1-1 1-1 2-1 1-1 1-1 2h0c-1 4-1 6 0 9l3 7c1-2 3-4 4-6h2l2 1-2 2c1 0 2 1 3 2 2-1 4-2 5-2h4 3 1l4 2 2 2 4 3 3 4-2 2h-5l-2-2c-1 1-1 2-1 2l-1 1h0 2v1h0v2c-1 0-2 1-3 2s-1 2-2 3c0 0-1 2-1 3h2c-1 2-1 3-1 5 1 0 2 1 3 1l1 1c-1 2-1 4-1 6l-1-2v-2h-1l-5 2c-6 2-12 4-19 4l-3-6-4-10v-1c1-3-1-7-1-10-1-6 0-12 0-17 0-1 0-2-1-3v-9c0-5-1-7 2-11v-2h0-1-1l-1-1c0-1 2-3 3-3 1-3 3-5 4-8h1 0v-1l1-5 1 1v1l1 1h0 5l1 1c1-2 2-4 4-6z" class="E"></path><path d="M171 350c0-1-1-3 0-5h0l3 3-3 2z" class="AF"></path><path d="M179 359c1 2 3 3 4 4 0 1 0 2-1 3-2-1-3-2-4-4 0-1 1-2 1-3z" class="s"></path><path d="M196 362h2c-1 2-1 3-1 5 1 0 2 1 3 1l1 1c-1 2-1 4-1 6l-1-2v-2h-1v-1l-1-1c-1 1-3 2-4 1h1l-1-1s1 0 2-1c0 0 1-1 1-2s0-2-1-2l1-2z" class="R"></path><path d="M172 352l2-1 2 1 1 3h0l1-1 1 5c0 1-1 2-1 3-3-3-5-7-6-10z" class="r"></path><path d="M193 359h2 2s-1 2-1 3l-1 2-1 1c-4 2-7 3-11 1h-1c1-1 1-2 1-3h4c2 0 4-1 5-2l1-2z" class="AB"></path><path d="M193 359h2 2s-1 2-1 3l-1 2-1 1-1-1c0-1 0-2-1-3l1-2z" class="AJ"></path><path d="M182 334l2 1-2 2c1 0 2 1 3 2-3 3-6 6-7 12v3l-1 1h0l-1-3-2-1-2 1-1-2 3-2 3-6-1-2c1-2 3-4 4-6h2z" class="q"></path><path d="M182 334l2 1-2 2c-2 1-4 3-5 5l-1-2c1-2 3-4 4-6h2z" class="I"></path><path d="M196 288l1 3-5 5-1 1c-2 2-3 6-4 9-1 1-3 2-3 4h1c0 2 1 2 2 3v2h1l-2 1h2c-3 2-5 1-7 4h-1l-1-1h0v1h-4c-1 1-1 1-1 2-1 1-1 1-1 2h0 0c-1-8 2-17 7-24 1-2 2-4 4-6 3 0 3-2 6-3l1 2 5-5z" class="AF"></path><path d="M175 320v-1c1-1 2-1 4-1v1 1h-4zm9-26c3 0 3-2 6-3l1 2c-3 3-5 6-7 10h0v-2l1-2v-1l2-2v-1h0c-2 1-3 3-4 4-1 0-3 1-3 1 1-2 2-4 4-6z" class="AD"></path><defs><linearGradient id="Av" x1="188.478" y1="317.871" x2="188.022" y2="289.629" xlink:href="#B"><stop offset="0" stop-color="#0f0f12"></stop><stop offset="1" stop-color="#312f2e"></stop></linearGradient></defs><path fill="url(#Av)" d="M196 288l1 3-5 5-1 1c-2 2-3 6-4 9-1 1-3 2-3 4h1c0 2 1 2 2 3v2h1l-2 1h2c-3 2-5 1-7 4h-1l-1-1c1-6 3-11 5-16h0c2-4 4-7 7-10l5-5z"></path><path d="M185 339c2-1 4-2 5-2h4 3 1l4 2 2 2 4 3 3 4-2 2h-5l-2-2c-1 1-1 2-1 2l-1 1h0 2v1h0v2c-1 0-2 1-3 2s-1 2-2 3h-2-2l-1 2c-1 1-3 2-5 2h-4c-1-1-3-2-4-4l-1-5v-3c1-6 4-9 7-12z" class="Z"></path><path d="M193 351l2 3-1 2c-1-1-1-2-2-3l1-2z" class="E"></path><path d="M185 355h2l1 1c0 1 0 1-1 3h-2l-1-1c0-2 0-2 1-3z" class="Y"></path><path d="M193 351c-2-2-4-3-7-3-2 0-3 0-4-1 1-3 7-5 9-6 0 2-1 3 0 5s3 3 3 4c1 1 1 3 1 4l-2-3z" class="B"></path><path d="M191 341h0c0-2 0 0 1-2 1 0 1 1 2 1 1 2 1 3 2 4h1l1 2v-1h1l-1 2 1 2c0 1 0 1 1 2h0 0 2v1h0v2c-1 0-2 1-3 2s-1 2-2 3h-2-2l1-3 1-2c0-1 0-3-1-4 0-1-2-2-3-4s0-3 0-5z" class="O"></path><path d="M196 344h1l1 2v-1h1l-1 2 1 2c0 1 0 1 1 2 0 1-1 3-1 4h-1c-1-1-2-3-3-5 0-1 0-2-1-3 0-1-1-1-1-2 1-1 2-1 3-1z" class="k"></path><path d="M194 337h3 1l4 2 2 2 4 3 3 4-2 2h-5l-2-2c-1 1-1 2-1 2l-1 1h0c-1-1-1-1-1-2l-1-2 1-2h-1v1l-1-2h-1c-1-1-1-2-2-4h0c1-1 1-1 0-3z" class="G"></path><path d="M202 344h-1c1-1 2-2 3-2h0l1 1c2 1 3 2 3 3v1c-2 1-4-1-7 0h-1c1-1 2-1 3-2h3 0c-1-1-2-1-4-1z" class="k"></path><path d="M202 339l2 2v1c-1 0-2 1-3 2h1s-2 1-3 1h-1v1l-1-2c1-1 3-4 5-5z" class="O"></path><path d="M194 337h3 1l4 2c-2 1-4 4-5 5h-1c-1-1-1-2-2-4h0c1-1 1-1 0-3z" class="m"></path><path d="M176 222c4 3 7 6 10 10 2 1 2 1 3 3s3 3 5 4l2 2 3 2h1c1-1 1-2 1-3l-2-2h2l3 1v-1c3 0 4 1 6 3v1l1 1h-1l2 4c1 0 1 1 2 2l1 1-2 2h-1-1l-2 2 6 14c0 2 0 2 1 3l2 2c1-1 2-1 4-1 0 1-1 2-2 2v2l2 2c-2 2-7 3-10 4-2 0-5 3-7 4h-1l-2 2c-2-1-3 0-5 0h-1l-5 5-1-2c-3 1-3 3-6 3 2-3 4-6 7-7l1-2c-2 0-4 2-5 3l-1 2h0l-1-2c-2 2-3 4-4 6l-1-1h1c0-2-1-3-1-5h-1c0-1 0-2-1-3-1-3 0-7 0-11l-2-10c-1-4-2-7-3-11l1-2c-1-2-2-5-4-8l-2-4h0l-1-4c0-2 1-3 3-4v1c1-1 3-2 5-3 1 0 3 0 4 1 1 0 1 1 1 1h1c0-2-5-5-6-6 1-1 1-2 1-3z" class="F"></path><path d="M176 222c4 3 7 6 10 10 5 10 8 20 7 32v1h0l-1-9c0-1 0-3-1-4-1-6-2-11-6-16-1-2-2-4-4-5 0-2-5-5-6-6 1-1 1-2 1-3z" class="y"></path><path d="M185 236c4 5 5 10 6 16 1 1 1 3 1 4-1 3 0 5-1 8h-1c-1 2-1 4-1 6l-2 2 2-9c0-3 0-4-2-6l-2-10 2-1c1-3-2-7-2-10z" class="R"></path><path d="M199 238h2l3 1v-1c3 0 4 1 6 3v1l1 1h-1l2 4c1 0 1 1 2 2l1 1-2 2h-1-1l-2 2-1-2c-3-2-9-1-12 0l-1 1h-1c-1-2-1-3-1-4v-2-2c2 0 2 0 3 1 1 0 2 0 2-1-1-1-1-3-2-4l3 2h1c1-1 1-2 1-3l-2-2z" class="O"></path><path d="M199 246c2 1 5 0 7 1-2 2-5 2-8 2l-1-1 2-2z" class="b"></path><path d="M205 241c2 0 3 1 5 2l2 4c1 0 1 1 2 2-2 0-4-1-5-2h-3c-2-1-5 0-7-1l2-2c1-2 2-3 4-3z" class="e"></path><path d="M205 241c2 0 3 1 5 2l2 4c1 0 1 1 2 2-2 0-4-1-5-2l-1-2v-1h0l-2 1h-1v-3-1z" class="J"></path><path d="M199 238h2l3 1v-1c3 0 4 1 6 3v1l1 1h-1c-2-1-3-2-5-2s-3 1-4 3l-2 2-2 2c0 2-1 2-1 4l-1 1h-1c-1-2-1-3-1-4v-2-2c2 0 2 0 3 1 1 0 2 0 2-1-1-1-1-3-2-4l3 2h1c1-1 1-2 1-3l-2-2z" class="E"></path><path d="M199 238h2l3 1v-1c3 0 4 1 6 3v1c-2-1-3-2-5-2h-4l-2-2z" class="S"></path><path d="M187 257c2 2 2 3 2 6l-2 9 2-2c0-2 0-4 1-6h1c1-3 0-5 1-8l1 9h0v5l-1 1v1c1 0 1 1 2 1v4c0 1 2 5 1 6s-2 2-3 2c-2 0-4 2-5 3l-1 2h0l-1-2c-2 2-3 4-4 6l-1-1h1c0-2-1-3-1-5h-1c0-1 0-2-1-3-1-3 0-7 0-11l2 2c1 0 2 0 3-1l1-4c1-4 1-8 2-12l1-2h0z" class="F"></path><path d="M192 256l1 9h0v5l-1 1v1c0 2-1 4-2 6h-1 0l-1-1v1h-1c1-2 2-5 3-8 0-2 1-4 0-6h1c1-3 0-5 1-8z" class="C"></path><path d="M192 272c1 0 1 1 2 1v4c0 1 2 5 1 6s-2 2-3 2c-2 0-4 2-5 3l-1 2h0l-1-2 5-10c1-2 2-4 2-6z" class="m"></path><path d="M187 257c2 2 2 3 2 6l-2 9c-2 4-5 8-6 13 0 1-1 2-1 3h-1c0-1 0-2-1-3-1-3 0-7 0-11l2 2c1 0 2 0 3-1l1-4c1-4 1-8 2-12l1-2h0z" class="B"></path><path d="M170 232c1-1 3-2 5-3 1 0 3 0 4 1 1 0 1 1 1 1h1c2 1 3 3 4 5 0 3 3 7 2 10l-2 1 2 10h0l-1 2c-1 4-1 8-2 12l-1 4c-1 1-2 1-3 1l-2-2-2-10c-1-4-2-7-3-11l1-2c-1-2-2-5-4-8l-2-4h0l-1-4c0-2 1-3 3-4v1z" class="n"></path><path d="M180 244h2l1-1c1 1 1 2 2 4l2 10c-1 0-2-1-4-1l-1-3v-3l1-1c0-1 0-1-1-2h1c-1-2 0-1-1-2l-1 1c0-1 0-1-1-2z" class="d"></path><path d="M170 232c1-1 3-2 5-3 1 0 3 0 4 1 1 0 1 1 1 1h1c2 1 3 3 4 5 0 3 3 7 2 10l-2 1c-1-2-1-3-2-4l-1 1h-2v-2h1v-1c-1 0 0 0-1-1h1l1 1v1h1c-1-3-1-5-3-7v1 1l-2 1c-1 0-2-2-3-3s0-2-1-4h-1l-1 2h0l-2-1z" class="C"></path><path d="M168 239h0l-1-4c0-2 1-3 3-4v1l2 1h0 1l-1 1h-2c-1 2 0 5 1 7 0 1 0 2 1 2 2 2 3 5 5 8 1 1 1 2 1 3 1 1 1 2 2 3 1 5-3 11 4 14l-1 4c-1 1-2 1-3 1l-2-2-2-10c-1-4-2-7-3-11l1-2c-1-2-2-5-4-8l-2-4z" class="d"></path><path d="M174 251c2 5 4 8 2 13-1-4-2-7-3-11l1-2z" class="Y"></path><path d="M196 252c3-1 9-2 12 0l1 2 6 14c0 2 0 2 1 3l2 2c1-1 2-1 4-1 0 1-1 2-2 2v2l2 2c-2 2-7 3-10 4-2 0-5 3-7 4h-1l-2 2c-2-1-3 0-5 0h-1l-5 5-1-2c-3 1-3 3-6 3 2-3 4-6 7-7l1-2c1 0 2-1 3-2s-1-5-1-6v-4c-1 0-1-1-2-1v-1l1-1v-5-1h2v-6-1l1-2h3 1s-4 0-5-2l1-1z" class="G"></path><path d="M200 255c2 0 4 0 5 1l1 1h-1 0c-1-1-1-1-2-1v3l-1 1-2-2c-1-1-1-1-2 0l-3-1 1-2h3 1z" class="D"></path><path d="M195 257l3 1c3 2 5 5 6 9 0 4-2 11-5 14l-1 1v-1l1-3c2-4 3-8 1-13 0-2-1-3-2-5v-1l-3-1v-1z" class="T"></path><path d="M197 284l4-1c2 0 1 0 2-1h2 0c0 1-1 1-2 2-2 1-4 2-6 4h-1l-5 5-1-2c-3 1-3 3-6 3 2-3 4-6 7-7l6-3z" class="q"></path><path d="M195 258l3 1v1c1 2 2 3 2 5 2 5 1 9-1 13l-1 3v1c0 1-1 1-1 2l-6 3 1-2c1 0 2-1 3-2s-1-5-1-6v-4c-1 0-1-1-2-1v-1l1-1v-5-1h2v-6z" class="N"></path><path d="M193 264h2c1 4 0 5-1 9-1 0-1-1-2-1v-1l1-1v-5-1z" class="E"></path><path d="M196 252c3-1 9-2 12 0l1 2 6 14c0 2 0 2 1 3l2 2c1-1 2-1 4-1 0 1-1 2-2 2v2l-1 1-8 2h-1c-1 1-2 1-3 1l-2-2h0l2-2c3-3 3-6 2-10 0-3-1-7-3-9l-1-1c-1-1-3-1-5-1 0 0-4 0-5-2l1-1z" class="m"></path><path d="M375 234h2 4 2v1h-4v1l2 1s1 0 2-1c1 0 0 0 2 1h1 14l-1 1c2 3 3 7 6 9l1 1 1 1c0 2 2 3 3 4-6-1-13-1-20-1-2 0-5 0-8 1-1 2-1 5-1 7 1 6 1 12 3 18 1 1 1 2 1 3s1 4 2 4l45 108h0l9 21c1 1 3 7 4 8v1c3 14 10 27 16 40 1 2 2 5 3 6 2 2 6 4 8 5l5 2h-3-1-5l3 1v2l-10-3c-1 0-1 0-1-1-1-1-2-1-3-2l-2-1c0 2 1 3 2 4h-7v-2c1 1 1 0 2 0 0-4-5-11-7-14a30.44 30.44 0 0 0-8-8l-1 2-3-2c-4-3-9-5-14-7l-1 1c1 0 2 1 3 1h0c1 0 0 0 1 1-2 0-3-2-5-2l-1 1c-1-1-2-1-4-2l1 1v1c-3-2-4-1-6-1-1 2-2 4-2 6h-1v-6l-1 1h0l-1 2-5-15c-2-5-4-10-6-16-2-5-6-12-6-18l-1 1-1 1-1-2c-1-1-1-1-1-2l1-2h0c-1-3-1-4 0-7h-1c0 1 0 1-1 2l-1-2c0-2-1-4-2-5-1-4-3-10-5-14-1-2-1-3-1-5v-1l-1-1c-2-2-3-4-3-7-2-3-2-5-3-8 0-4-1-7-2-11v-1c-1-2-1-4-1-6v-8-1c0-2 1-5 1-7h1v-2c-3-2-5-6-9-7l-1 1v1h0c1 3 3 5 3 8v1l1 2h-1-3-2c0-1-1-2-1-2h-1l-1 1-2-1c-1 0-4 1-5 0 0 1 1 1 1 2s0 1-1 1l-2 2h0c2 2 2 3 2 5l-4-7v-1c-1 0-2-1-2-1-3 0-4 0-6-2-1-4 1-9 2-13 1-2 2-5 2-7-1-2-2-3-3-5l-1-2c1 0 2-1 3-1h1v-1c-1-2-1-2-3-2v-3l-1-1 2-1c0-1 1-1 2-2 0-3 4-8 5-11 0-1 0-2 1-3 7-6 12-8 21-8 4 1 7 1 11 2 0-1 0-1 1-2v-11l1-1c1-2 0-4 0-6l-1-1v-2l1 1 2-1z" class="n"></path><path d="M372 234l1 1 2-1c2 1 3 1 4 3h-6l-1-1v-2z" class="d"></path><path d="M445 423c3 14 10 27 16 40-1 0-2 0-3-1-2-5-5-11-7-17-2-4-5-7-5-11h0c2 6 5 12 8 17 0-3-2-7-3-9-2-3-4-6-4-9l-1-1-1-3v-1h0v-5z" class="L"></path><path d="M451 455c1 2 2 3 3 5h0v-1c1 3 2 6 4 8 1-1 0-3 0-4v-1c1 1 2 1 3 1 1 2 2 5 3 6 2 2 6 4 8 5l5 2h-3-1-5l3 1v2l-10-3c-1 0-1 0-1-1-1-1-2-1-3-2l1-1c-2-5-5-11-7-17z" class="P"></path><path d="M458 472l10 4 3 1v2l-10-3c-1 0-1 0-1-1-1-1-2-1-3-2l1-1z" class="N"></path><path d="M386 237h14l-1 1-1 1c0 2 2 6 2 8h-24c1-3 1-6 1-9 3 0 6-1 9-1z" class="m"></path><path d="M382 292c3 4 5 10 7 15l13 31c5 11 10 22 14 34h-1l-10-24h-1l-2-1-3 4h-1c-2-1-2 0-3-1l1-1c0-3 0-3-1-4 1-1 0-1 0-2s-1-2-2-3c0-1 1-2 2-3 0-2-1-5-2-7l2 1c0-2-1-4-2-5l-2-4-1-4-5-11c-1 0-1 0-2-1-1-2-3-3-4-5h0l-1 1-1-2c1-2 2-3 3-5l-1-1h1v-2h1 0 1z" class="B"></path><path d="M380 295l5 12c-1 0-1 0-2-1-1-2-3-3-4-5h0l-1 1-1-2c1-2 2-3 3-5z" class="P"></path><path d="M393 330l2 1 3 9c1 1 2 2 2 3 1 1 2 3 2 4l-3 4h-1c-2-1-2 0-3-1l1-1c0-3 0-3-1-4 1-1 0-1 0-2s-1-2-2-3c0-1 1-2 2-3 0-2-1-5-2-7z" class="L"></path><path d="M376 300h1l1 2 1-1h0c1 2 3 3 4 5 1 1 1 1 2 1l5 11 1 4 2 4c1 1 2 3 2 5l-2-1c1 2 2 5 2 7-1 1-2 2-2 3-1 1-2 1-2 3h-1-1c-1 0-1 0-2 1h-2c0 1 0 2 1 3 0 1 1 2 1 2v1h-2l-2 2v2c-1-2-2-3-2-4-1-4-4-7-5-11-1-2-2-5-3-8 0-3-1-6-2-9-1-2-1-7-3-9h0v-6c1-1 1-1 1-2l1 2h-1l1 1c3-2 3-6 6-8z" class="K"></path><path d="M376 300h1l1 2-3 5-1 1-1-2 3-6z" class="J"></path><path d="M375 329c1-1 1-1 2-1l1 1h2 0c1 2 1 2 1 4l1 2 1 2 1 1 3 6h-2c0 1 0 2 1 3 0 1 1 2 1 2v1h-2c-1-1-1-1-2-1h-1v-2l1 1h0c0-3-1-4-2-7l-6-12z" class="f"></path><path d="M379 328l1-1c2 0 5 1 8 0 2 1 3 2 5 3 1 2 2 5 2 7-1 1-2 2-2 3-1 1-2 1-2 3h-1-1c-1 0-1 0-2 1l-3-6-1-1-1-2-1-2c0-2 0-2-1-4h0l-1-1z" class="j"></path><path d="M380 329h2c2 2 3 4 3 7 0 1-1 1-1 2l-1-1-1-2-1-2c0-2 0-2-1-4z" class="L"></path><path d="M378 302l1-1h0c1 2 3 3 4 5 1 1 1 1 2 1l5 11 1 4 2 4c1 1 2 3 2 5l-2-1c-2-1-3-2-5-3-3 1-6 0-8 0l-1 1 1 1h-2l-1-1c-1 0-1 0-2 1-1-2-1-4-1-6-1-5-2-8-1-13l1-2 1-1 3-5z" class="l"></path><path d="M391 322l2 4c1 1 2 3 2 5l-2-1c-2-1-3-2-5-3-3 1-6 0-8 0l-1 1 1 1h-2l-1-1c-1 0-1 0-2 1-1-2-1-4-1-6h1c5 2 10 1 15 1l1-2z" class="T"></path><path d="M379 328l-3-3h3c2 0 3 0 5 1 2 0 3 1 4 0h1l1 1c2 0 2-1 3-1 1 1 2 3 2 5l-2-1c-2-1-3-2-5-3-3 1-6 0-8 0l-1 1z" class="P"></path><path d="M378 302l1-1h0c1 2 3 3 4 5 1 1 1 1 2 1l5 11c-1 0-2 1-3 0-3-2-4-1-6-2-2 0-5 0-6 1h2c0 3-4 3-2 6h-1c-1-5-2-8-1-13l1-2 1-1 3-5z" class="L"></path><path d="M383 312c1-1 1-1 2 0 0 1 0 2-1 3-1 0-1-1-2-2l1-1z" class="d"></path><path d="M375 307h3l-2 2v1h-3l1-2 1-1z" class="X"></path><path d="M378 307c1-1 1-2 2-2l1 1-2 2c-1 1 0 1-1 2l-2-1 2-2z" class="d"></path><path d="M373 310h3c-1 1-1 3-1 5h7v1h-1c-2 0-5 0-6 1h2c0 3-4 3-2 6h-1c-1-5-2-8-1-13z" class="P"></path><path d="M393 340c1 1 2 2 2 3s1 1 0 2c1 1 1 1 1 4l-1 1c1 1 1 0 3 1h1l3-4 2 1h1l10 24h1c3 5 5 12 7 18l-1 1c1 1 3 4 3 6 0 1-1 1-1 2l-6-1c-2 0-3 0-4-1 1-1 2-1 3-1l-1-1c-2 0-3-1-4-2-2-1-3-3-4-5 0 0-1-1-1-2l-1 3c1 1 1 2 1 3h0l-5-10-8-14v1c-1-2-1-2-2-3l-1 1c-1-2-1-3-3-4-1 0-1-1-2-2-1-2-3-4-3-7v-2l2-2h2v-1s-1-1-1-2c-1-1-1-2-1-3h2c1-1 1-1 2-1h1 1c0-2 1-2 2-3z" class="L"></path><path d="M402 347l2 1-1 3v1l-1 3v1l-1 6-2-6-1-1c0-1-1-2-2-3h0 0 1c1 0 1-1 2-1l3-4z" class="N"></path><path d="M402 355c-1 0-1 0-2-1l-1-1 2-2 2 1-1 3z" class="F"></path><path d="M412 393c-1-3-1-5 0-7 0-1 2-1 2-1l1-1h1 0c1 2 3 6 2 8v1c-1 1-2 1-2 2-2 0-3-1-4-2z" class="d"></path><path d="M414 375c2 5 4 9 7 13l1 1v2c1 1 3 4 3 6 0 1-1 1-1 2l-6-1c-2 0-3 0-4-1 1-1 2-1 3-1l-1-1c0-1 1-1 2-2v-1c1-2-1-6-2-8-2-3-2-5-2-9z" class="U"></path><path d="M404 348h1l10 24h1c3 5 5 12 7 18l-1 1v-2l-1-1c-3-4-5-8-7-13l-5-11-3-3c-1-2-1-4-4-5v-1l1-3v-1l1-3z" class="J"></path><path d="M403 351l2 2c1 4 3 7 4 11l-3-3c-1-2-1-4-4-5v-1l1-3v-1z" class="X"></path><path d="M396 352c1 1 2 2 2 3h-1c1 5 2 9 4 13s5 6 5 11c1 3 1 6 2 9 0 0-1-1-1-2l-1 3c1 1 1 2 1 3h0l-5-10-8-14v-3-9 3c0 1 0 1 1 2v-6l1-2v-1z" class="S"></path><path d="M407 386c-2-3-3-5-4-9 1 0 1-1 1-1v-1l1 1v3h1c1 3 1 6 2 9 0 0-1-1-1-2z" class="e"></path><path d="M394 365v-9 3c0 1 0 1 1 2 1 5 4 10 6 15 2 4 3 8 5 13 1 1 1 2 1 3h0l-5-10-8-14v-3z" class="d"></path><path d="M393 340c1 1 2 2 2 3s1 1 0 2c1 1 1 1 1 4l-1 1c1 1 1 0 3 1h1c-1 0-1 1-2 1h-1 0 0v1l-1 2v6c-1-1-1-1-1-2v-3 9 3 1c-1-2-1-2-2-3l-1 1c-1-2-1-3-3-4-1 0-1-1-2-2-1-2-3-4-3-7v-2l2-2h2v-1s-1-1-1-2c-1-1-1-2-1-3h2c1-1 1-1 2-1h1 1c0-2 1-2 2-3z" class="k"></path><path d="M391 343h1l1-1h1c0 1 0 1-1 2 1 1 1 1 1 2l-1 1c-2 3-3 6-3 9h-1c-1-2-1-5-1-7 1-3 3-4 5-5v-1h-3 1z" class="Y"></path><path d="M383 352c1 0 1 2 1 2 1 1 2 1 2 2 1 1 1 2 2 2 0 1 0 1 1 2h1v-1c2 3 2 4 2 7l-1 1c-1-2-1-3-3-4-1 0-1-1-2-2-1-2-3-4-3-7v-2z" class="O"></path><path d="M393 355c0-1 1-3 2-4l1 1h0v1l-1 2v6c-1-1-1-1-1-2v-3 9 3 1c-1-2-1-2-2-3 0-3 0-4-2-7h0c0-1-1-1-1-2h1l3-2z" class="L"></path><path d="M390 359h0c0-1-1-1-1-2h1l3-2c0 3-1 6 1 10v3 1c-1-2-1-2-2-3 0-3 0-4-2-7z" class="B"></path><defs><linearGradient id="Aw" x1="441.867" y1="433.888" x2="423.641" y2="437.85" xlink:href="#B"><stop offset="0" stop-color="#939496"></stop><stop offset="1" stop-color="#aeaeae"></stop></linearGradient></defs><path fill="url(#Aw)" d="M407 386c0 1 1 2 1 2 1 2 2 4 4 5 1 1 2 2 4 2l1 1c-1 0-2 0-3 1 1 1 2 1 4 1l6 1c0-1 1-1 1-2 0-2-2-5-3-6l1-1 12 28 10 23c2 5 3 10 6 14 2 6 5 12 7 17l-1 1-2-1c0 2 1 3 2 4h-7v-2c1 1 1 0 2 0 0-4-5-11-7-14a30.44 30.44 0 0 0-8-8l-1 2-3-2c-4-3-9-5-14-7h0 0l-1-1-4-3c-1 0-1-1-1-1-2-3-4-5-5-8l-1-6c1-2 1-3 2-4l1 1h1c1 0 2 1 3 1s2 0 3-1c0-3-1-9-3-12v1l-1-1h-1l-1-1c-1-1-1-2-2-3l-2-12s0-1-1-1l1-2h0c0-1 0-2-1-3l1-3z"></path><path d="M412 407c1-2 1-2 3-3 0 2 0 3 1 5l-1 1c2 2 2 1 4 1h1v3h1v-2h0c1-1 2-1 3-1 1 1 2 2 3 4 1 3 3 5 1 8v1c-1 1-2 3-3 3-1 1-1 0-2 1h-1v1 1c-1-1-1-2-1-3-1-3-2-6-1-9h0v-1h-3c1 2 1 4 1 6h-1c0-3-1-9-3-12l-2-4z" class="Y"></path><path d="M412 407c1-2 1-2 3-3 0 2 0 3 1 5l-1 1c2 2 2 1 4 1v5c-1 0-1 0-1-1v-3h-1v5c1 2 1 4 1 6h-1c0-3-1-9-3-12l-2-4z" class="G"></path><defs><linearGradient id="Ax" x1="409.76" y1="433.601" x2="418.582" y2="428.29" xlink:href="#B"><stop offset="0" stop-color="#89898b"></stop><stop offset="1" stop-color="#a6a6a8"></stop></linearGradient></defs><path fill="url(#Ax)" d="M417 417h3v1h0c-1 3 0 6 1 9 0 1 0 2 1 3h2v1h-1c1 1 0 1 1 1 1 1 1 3 2 4 1 4 3 8 5 11 2 2 4 3 6 5l-1 2-3-2c-4-3-9-5-14-7h0 0l-1-1-4-3c-1 0-1-1-1-1-2-3-4-5-5-8l-1-6c1-2 1-3 2-4l1 1h1c1 0 2 1 3 1s2 0 3-1h1c0-2 0-4-1-6z"></path><path d="M416 438l-1-1c0-1 1-2 1-3 1 1 2 3 4 3l2 4h-2c-1-2-3-2-4-3z" class="i"></path><path d="M416 438c1 1 3 1 4 3h2v4h-3l-1-1v-1c0-2-1-3-2-5z" class="g"></path><path d="M420 437v-1h1l1 5h1c0-1 0-2 1-3l3 9-5-2v-4l-2-4z" class="j"></path><path d="M417 417h3v1h0c-1 3 0 6 1 9 0 1 0 2 1 3h2v1h-1c1 1 0 1 1 1 1 1 1 3 2 4 1 4 3 8 5 11 2 2 4 3 6 5l-1 2-3-2c-4-3-9-5-14-7h0 0 3l5 2-3-9c-1-2-1-5-3-7v3h0c-2-4-3-7-4-11h1c0-2 0-4-1-6z" class="H"></path><path d="M407 386c0 1 1 2 1 2 1 2 2 4 4 5 1 1 2 2 4 2l1 1c-1 0-2 0-3 1 1 1 2 1 4 1l6 1c0-1 1-1 1-2 0-2-2-5-3-6l1-1 12 28 10 23c2 5 3 10 6 14 2 6 5 12 7 17l-1 1-2-1c0 2 1 3 2 4h-7v-2c1 1 1 0 2 0 0-4-5-11-7-14l1-1-1-2h1l1 2v-1c0-1-1-2-1-3s-1-2-1-3c-1-1-1-2-1-4 0-1-1-4-2-5l-7-17c-2-4-4-12-8-16-1-1-4-2-6-3v2h1v1l-1 1h-1-1c-2 0-2 1-4-1l1-1c-1-2-1-3-1-5-2 1-2 1-3 3l2 4v1l-1-1h-1l-1-1c-1-1-1-2-2-3l-2-12s0-1-1-1l1-2h0c0-1 0-2-1-3l1-3z" class="D"></path><path d="M444 448l11 24c0 2 1 3 2 4h-7v-2c1 1 1 0 2 0 0-4-5-11-7-14l1-1-1-2h1l1 2v-1c0-1-1-2-1-3s-1-2-1-3c-1-1-1-2-1-4z" class="f"></path><path d="M407 386c0 1 1 2 1 2 1 2 2 4 4 5 1 1 2 2 4 2l1 1c-1 0-2 0-3 1h-2c3 2 6 3 10 4 1 1 5 2 6 3 1 0 1 1 1 1l-1 1c-2 0-6-1-8 0h0l1 1v2h1v1l-1 1h-1-1c-2 0-2 1-4-1l1-1c-1-2-1-3-1-5-2 1-2 1-3 3l2 4v1l-1-1h-1l-1-1c-1-1-1-2-2-3l-2-12s0-1-1-1l1-2h0c0-1 0-2-1-3l1-3z" class="L"></path><path d="M407 386c0 1 1 2 1 2 1 2 2 4 4 5 1 1 2 2 4 2l1 1c-1 0-2 0-3 1h-2l-3-3h0v2h0l-1-1h-1s0-1-1-1l1-2h0c0-1 0-2-1-3l1-3z" class="J"></path><path d="M407 395h1l1 1h0 1c5 6 11 5 16 9-2 0-5-1-7-2-1 0-3 0-5-1v-1h-1v2h1c2 0 3 1 5 2v1c-1 0-2-1-3-2h-1c-2 1-2 1-3 3l2 4v1l-1-1h-1l-1-1c-1-1-1-2-2-3l-2-12z" class="D"></path><path d="M407 395h1l1 1c0 4 2 7 3 11l2 4v1l-1-1h-1l-1-1c-1-1-1-2-2-3l-2-12z" class="L"></path><path d="M360 255c4 1 7 1 11 2 0-1 0-1 1-2v6c0 2 1 5 1 8 1 5 3 10 5 14 1 3 3 6 4 9h-1 0-1v2h-1l1 1c-1 2-2 3-3 5h-1c-3 2-3 6-6 8l-1-1h1l-1-2c0 1 0 1-1 2v-1l-2-4-2 2-2-6-1 2 1 3c1 2 2 5 2 7l1 2h-2c-3-2-5-6-9-7l-1 1v1h0c1 3 3 5 3 8v1l1 2h-1-3-2c0-1-1-2-1-2h-1l-1 1-2-1c-1 0-4 1-5 0 0 1 1 1 1 2s0 1-1 1l-2 2h0c2 2 2 3 2 5l-4-7v-1c-1 0-2-1-2-1-3 0-4 0-6-2-1-4 1-9 2-13 1-2 2-5 2-7-1-2-2-3-3-5l-1-2c1 0 2-1 3-1h1v-1c-1-2-1-2-3-2v-3l-1-1 2-1c0-1 1-1 2-2 0-3 4-8 5-11 0-1 0-2 1-3 7-6 12-8 21-8z" class="M"></path><path d="M341 288l7 2h0-1c-2 0-3 1-5 1h-2v-1l1-2z" class="N"></path><path d="M342 286v1l-1 1h0l-1 2v1h2c-1 2-1 2-2 3 0 1 0 1-1 2v-1c0-1 0-2-1-4v-1c0-1 1-2 2-3l2-1z" class="B"></path><path d="M331 302l2-1c1-1 1-2 1-3h1c1-1 1-1 2-1v3c-3 3-5 8-5 12l1 1c0 1 0 2 1 3l1 1c-3 0-4 0-6-2-1-4 1-9 2-13z" class="F"></path><path d="M329 288c1 0 2-1 3-1h1c1 2 2 3 2 5l1-1v-2c1 1 1 0 1 1h1v1c1 2 1 3 1 4v1l-1 1 1 1 2-2h1c-1 2-3 3-5 4v-3c-1 0-1 0-2 1h-1c0 1 0 2-1 3l-2 1c1-2 2-5 2-7-1-2-2-3-3-5l-1-2z" class="N"></path><path d="M335 292l1-1v-2c1 1 1 0 1 1h1v1c1 2 1 3 1 4h-3 0c-1-1-1-2-1-3z" class="M"></path><path d="M336 295l2-4c1 2 1 3 1 4h-3z" class="O"></path><path d="M333 277l2-1c2 0 3 2 5 3v1c1 1 3 1 3 3-1 1-1 1-3 1v1h2v1l-2 1c-1 1-2 2-2 3h-1c0-1 0 0-1-1v2l-1 1c0-2-1-3-2-5v-1c-1-2-1-2-3-2v-3l-1-1 2-1c0-1 1-1 2-2z" class="L"></path><path d="M330 281c3 0 3 0 5 2v4l1 1c1 0 2-1 2-1h2c-1 1-2 2-2 3h-1c0-1 0 0-1-1v2l-1 1c0-2-1-3-2-5v-1c-1-2-1-2-3-2v-3z" class="T"></path><path d="M353 293c-1-2-3-5-4-8v-2h-1l1-1-1-2c1-1 1-1 3-2l1 1c2 2 4 4 4 7l3 6c0 3 1 6 2 8l1 3c1 2 2 5 2 7l1 2h-2c-3-2-5-6-9-7l-1 1v-2c1 1 2 1 3 0-1-1-1-1-1-2v-1-1c1-2 0-3-1-5v-2h-1z" class="P"></path><path d="M359 292c0 3 1 6 2 8l1 3h-1c-2-1-1-1-2-3v-1-2c0-1 0-2-1-2v-1l1-2z" class="p"></path><path d="M354 293h0c0-2 0-3-1-4 1 0 2 1 2 3l1 1v1c1 1 1 2 1 3 0 0 1 1 1 2v1h-1l1 1 1-1c1 2 0 2 2 3h1c1 2 2 5 2 7l1 2h-2c-3-2-5-6-9-7l-1 1v-2c1 1 2 1 3 0-1-1-1-1-1-2v-1-1c1-2 0-3-1-5v-2z" class="n"></path><path d="M355 301c3 1 5 3 6 6l2 2-1 1 1 1 1-1 1 2h-2c-3-2-5-6-9-7l-1 1v-2c1 1 2 1 3 0-1-1-1-1-1-2v-1z" class="G"></path><path d="M353 265l1 1 1-1 3 6 1 1 1 3c2 3 3 5 4 8-1 0-2 0-3 1h0l5 18-2 2-2-6c-1-2-2-5-3-7-1-4-2-8-3-11-1-2-2-3-4-3-1 0-4 1-5 2l-1 1c-1-1-1-3-1-5 0-1-1-1-1-2 3-4 5-5 8-8h1z" class="Y"></path><path d="M355 265l3 6 1 1 1 3c2 3 3 5 4 8-1 0-2 0-3 1h0c-2-4-4-7-6-11-1-3-2-4-1-7l1-1z" class="B"></path><path d="M359 272c-1 1-1 1-2 1-1-2-1-2-1-3 0 0 1 0 2 1l1 1z" class="V"></path><path d="M360 255c4 1 7 1 11 2 0-1 0-1 1-2v6c0 2 1 5 1 8-1 0-2-1-3-2-1-2-2-3-3-4v2h-8c-1-1-2-1-3-1l-1 1-1 1-1-1h-1-3l-1-1c-4 3-7 5-9 10l1 2c-1 0-1-1-2-1v1c1 1 1 1 2 3-2-1-3-3-5-3l-2 1c0-3 4-8 5-11 0-1 0-2 1-3 7-6 12-8 21-8z" class="E"></path><path d="M348 264c1-1 2-2 4-2 5-2 10-1 15 1v2h-8c-1-1-2-1-3-1l-1 1-1 1-1-1h-1-3l-1-1z" class="J"></path><path d="M360 255c4 1 7 1 11 2 0-1 0-1 1-2v6c-8-3-16-6-24-3-4 2-7 5-10 8 0-1 0-2 1-3 7-6 12-8 21-8z" class="X"></path><path d="M337 319c0-1 0-2-1-3v-1c0-4-1-10 3-14h0c1-1 1-1 3-2 1-1 3-4 5-5h4v-1l-1-1c-2 0-2 1-4 1v1-1l2-2h2s1 2 2 2h1 1v2c1 2 2 3 1 5v1 1c0 1 0 1 1 2-1 1-2 1-3 0v2 1h0c1 3 3 5 3 8v1l1 2h-1-3-2c0-1-1-2-1-2h-1l-1 1-2-1c-1 0-4 1-5 0 0 1 1 1 1 2s0 1-1 1l-2 2h0c2 2 2 3 2 5l-4-7z" class="L"></path><path d="M341 307c0-2 0-4 2-5h1l2 2c0 1 0 2-1 3s0 2 0 3 0 1-1 1c-1-1-2-2-3-4z" class="D"></path><path d="M340 309h0l-1-1 2-1c1 2 2 3 3 4l1 2-1 1h0v1h-4v1h0c-1-2 0-4 0-7z" class="j"></path><path d="M340 309l2 1-1 3c1 1 1 1 3 1v1h-4v1h0c-1-2 0-4 0-7z" class="Y"></path><path d="M345 313l2 1c3 1 6 1 9 2l1 2h-1-3-2c0-1-1-2-1-2h-1l-1 1-2-1c-1 0-4 1-5 0 0 1 1 1 1 2s0 1-1 1l-1-3v-1h4v-1h0l1-1z" class="d"></path><path d="M354 295c1 2 2 3 1 5v1h-5l-2 3-1-1c0-2-1-5 1-7h3c2 0 2 0 3-1z" class="j"></path><path d="M348 296h3l3 1v1l-1 1c-2 0-4 0-5-1v-2z" class="n"></path><path d="M348 304l2-3h5v1c0 1 0 1 1 2-1 1-2 1-3 0v2 1h0c1 3 3 5 3 8v1c-3-1-6-1-9-2l1-6v-4z" class="F"></path><path d="M348 304l2-3h5v1c0 1 0 1 1 2-1 1-2 1-3 0v2 1h0-2v2c1 1 2 1 1 2v1l-3-3-1-1v-4z" class="D"></path><path d="M367 263c1 1 2 2 3 4 1 1 2 2 3 2 1 5 3 10 5 14 1 3 3 6 4 9h-1 0-1v2h-1l1 1c-1 2-2 3-3 5h-1c-3 2-3 6-6 8l-1-1h1l-1-2c0 1 0 1-1 2v-1l-2-4-5-18h0c1-1 2-1 3-1-1-3-2-5-4-8l-1-3-1-1-3-6 1-1c1 0 2 0 3 1h8v-2z" class="X"></path><path d="M370 283h0l2-2c0 1-1 2 0 3v1c-2 3-2 7-4 11h2v1 1c-1 1-2 2-2 4v3 1l-2-4-5-18h0c1-1 2-1 3-1 1 5 2 9 3 13 1-2 2-7 2-9s0-2 1-4h0z" class="N"></path><path d="M370 267c1 1 2 2 3 2 1 5 3 10 5 14 1 3 3 6 4 9h-1 0-1v2h-1l1 1c-1 2-2 3-3 5h-1c-3 2-3 6-6 8l-1-1h1l-1-2c3-3 7-9 9-12v-3l-1-1-1 1-1-1c0-1 0-2-1-3v-1l-1-3-1-1h0l-2 2h0l2-8v-4l-1 1c0-2-1-4-1-5z" class="C"></path><path d="M372 281v-2c0-1 0-1 1-1l1 1c0 1 0 2-1 2v1l-1-1z" class="I"></path><path d="M379 294l1 1c-1 2-2 3-3 5h-1c-3 2-3 6-6 8l-1-1h1l3-6 3-3c0-2 2-3 3-4z" class="V"></path><path d="M367 263c1 1 2 2 3 4 0 1 1 3 1 5l1-1v4l-2 8h0c-1 2-1 2-1 4s-1 7-2 9c-1-4-2-8-3-13-1-3-2-5-4-8l-1-3-1-1-3-6 1-1c1 0 2 0 3 1h8v-2z" class="P"></path><path d="M359 265c2 2 3 3 3 6v1l-1-1h-1c-1-2-2-3-3-5v-1h2z" class="L"></path><path d="M363 272v-7c1 3 2 7 4 8 1 2 1 1 1 3h-1l1 2v2l-2-2c0-2-2-4-3-6z" class="Y"></path><path d="M367 263c1 1 2 2 3 4 0 1 1 3 1 5l1-1v4l-1-1c-1 2-2 5-2 7 0 1-1 1-1 2h0c-1-1-2-3-2-4v-1l2 2v-2l-1-2h1 1l1-2c-2-2-1-4-2-6-1-1-1-2-1-3v-2z" class="M"></path><path d="M360 275h1l1 1 1-1v-3c1 2 3 4 3 6v1c0 1 1 3 2 4h0c0-1 1-1 1-2 0-2 1-5 2-7l1 1-2 8h0c-1 2-1 2-1 4s-1 7-2 9c-1-4-2-8-3-13-1-3-2-5-4-8z" class="X"></path><path d="M362 298l2 6 2-2 2 4v1 6h0c2 2 2 7 3 9 1 3 2 6 2 9l3 8c1 4 4 7 5 11 0 1 1 2 2 4 0 3 2 5 3 7 1 1 1 2 2 2 2 1 2 2 3 4l1-1c1 1 1 1 2 3v-1l8 14 5 10-1 2c1 0 1 1 1 1l2 12c1 1 1 2 2 3l1 1h1l1 1v-1c2 3 3 9 3 12-1 1-2 1-3 1s-2-1-3-1h-1l-1-1c-1 1-1 2-2 4l1 6c1 3 3 5 5 8 0 0 0 1 1 1l4 3 1 1h0 0l-1 1c1 0 2 1 3 1h0c1 0 0 0 1 1-2 0-3-2-5-2l-1 1c-1-1-2-1-4-2l1 1v1c-3-2-4-1-6-1-1 2-2 4-2 6h-1v-6l-1 1h0l-1 2-5-15c-2-5-4-10-6-16-2-5-6-12-6-18l-1 1-1 1-1-2c-1-1-1-1-1-2l1-2h0c-1-3-1-4 0-7h-1c0 1 0 1-1 2l-1-2c0-2-1-4-2-5-1-4-3-10-5-14-1-2-1-3-1-5v-1l-1-1c-2-2-3-4-3-7-2-3-2-5-3-8 0-4-1-7-2-11v-1c-1-2-1-4-1-6v-8-1c0-2 1-5 1-7h1v-2h2l-1-2c0-2-1-5-2-7l-1-3 1-2z" class="n"></path><path d="M401 384l1-2 5 10-1 2-1-1v-3h0v3 1c-1-4-3-7-4-10z" class="N"></path><path d="M408 432c1 3 3 5 5 8 0 0 0 1 1 1l4 3 1 1h0c-6-2-10-6-13-11l2-2z" class="G"></path><path d="M382 358c2 1 3 3 4 3 1 1 1 2 2 2 2 1 2 2 3 4l1-1c1 1 1 1 2 3v-1l8 14-1 2c-4-5-8-11-12-15-3-3-6-5-9-7h0 2 2 1c-1-1-2-2-2-3l-1-1z" class="H"></path><path d="M400 428h0c1 2 1 6 2 8l2 2c2 2 4 4 7 6l1 1 1 1v1c-3-2-4-1-6-1-1 2-2 4-2 6h-1v-6l-1 1h0l-1 2-5-15c1-1 1-1 1-2l2-4z" class="S"></path><path d="M404 446l-1-8c1 2 4 5 4 6v2c-1 2-2 4-2 6h-1v-6z" class="d"></path><path d="M397 434c1-1 1-1 1-2l2-4c0 4 0 7 1 10 0 3 1 6 2 9l-1 2-5-15z" class="X"></path><path d="M381 373c2 0 5 0 7 1 5 4 13 15 14 22 1 9 0 21-1 31-1-2-1-10-1-12l-5-3c1-1 4 0 6 0v-9-1c1-7-2-10-6-15l-1-1c0-1-1-2-2-3-2-2-4-4-7-5-1 0-2 0-3 1h1c0 2-2 4-2 6-2-1-2-6-2-7 0-2 1-3 2-5z" class="K"></path><path d="M382 389c1 0 2-1 3-1l1-1c3-1 5-1 8-1l1 1c4 5 7 8 6 15v1c-1-1-1-1-3-2h-2-1c-2 0-3 0-4-1h-6 0l-1 1-1 1-1-2c-1-1-1-1-1-2l1-2h0c-1-3-1-4 0-7z" class="Y"></path><path d="M395 387c4 5 7 8 6 15v1c-1-1-1-1-3-2h-2-1c-2 0-3 0-4-1v-1c1 0 2 0 3 1h3l1-1c-1-2 0-5-1-7h-1c-1-1-2-1-3-1-2 1-3 1-4 2 1-2 2-3 3-4 0 0 1-1 2-1l1-1z" class="P"></path><path d="M382 389c1 0 2-1 3-1l1-1c3-1 5-1 8-1l1 1-1 1c-1 0-2 1-2 1-1 1-2 2-3 4l-3 5 5 1v1h-6 0l-1 1-1 1-1-2c-1-1-1-1-1-2l1-2h0c-1-3-1-4 0-7z" class="S"></path><path d="M387 389h2c-1 1-3 2-4 4l-1-2c0-1 2-1 3-2z" class="U"></path><path d="M382 400c0-2 0-2 2-3l1 1v2h0l-1 1-1 1-1-2z" class="c"></path><path d="M382 389c1 0 2-1 3-1l1-1c3-1 5-1 8-1l1 1-1 1c-1 0-2 1-2 1h-3-2c-1 1-3 1-3 2l1 2-3 3h0c-1-3-1-4 0-7z" class="T"></path><path d="M387 389l1-2c3 0 4 0 6 1-1 0-2 1-2 1h-3-2z" class="i"></path><path d="M405 394v-1-3h0v3l1 1c1 0 1 1 1 1l2 12c1 1 1 2 2 3l1 1h1l1 1v-1c2 3 3 9 3 12-1 1-2 1-3 1s-2-1-3-1h-1l-1-1c-1 1-1 2-2 4l1 6-2 2c-3-7-2-14-2-21 1-7 1-13 1-19z" class="C"></path><path d="M409 407c1 1 1 2 2 3l1 1h1l1 1v-1c2 3 3 9 3 12-1 1-2 1-3 1s-2-1-3-1h-1l-1-1c-1 1-1 2-2 4 0-4 0-7 1-10 0-2 1-5 1-8v-1z" class="X"></path><path d="M409 408c0 2 0 3 1 5h0 3v1l1 1 1 3c-2-1-3-1-3-2-2 1-1 2-2 3h-1v-3h-1c0-2 1-5 1-8z" class="B"></path><path d="M408 416h1v3h1c1-1 0-2 2-3 0 1 1 1 3 2-1 1-2 2-2 3h-2l-1-1c-1 1-1 1-1 2-1 1-1 2-2 4 0-4 0-7 1-10z" class="O"></path><path d="M367 356c2 2 4 4 6 5 2 2 11 7 12 10-1 1-1 1-2 1s-2 0-4 1h2c-1 2-2 3-2 5 0 1 0 6 2 7 0-2 2-4 2-6h-1c1-1 2-1 3-1 3 1 5 3 7 5 1 1 2 2 2 3-3 0-5 0-8 1l-1 1c-1 0-2 1-3 1h-1c0 1 0 1-1 2l-1-2c0-2-1-4-2-5-1-4-3-10-5-14-1-2-1-3-1-5v-1l-1-1c-2-2-3-4-3-7z" class="T"></path><path d="M372 364c2 0 2 2 3 2l1-1 4 4c-1 1-1 2-2 2-1 1-1 2-2 3v1h-1c0-2 0-4-1-5-1-2-2-3-2-6z" class="d"></path><path d="M381 389c1-1 1-2 1-3h0c2-1 3-2 5-3v-1l1 1 1-1c1 1 2 1 3 1h0c1 1 2 2 2 3-3 0-5 0-8 1l-1 1c-1 0-2 1-3 1h-1z" class="H"></path><path d="M385 400h6c1 1 2 1 4 1h1 2c2 1 2 1 3 2v9c-2 0-5-1-6 0l5 3c0 2 0 10 1 12l-1 1h0l-2 4c0 1 0 1-1 2-2-5-4-10-6-16-2-5-6-12-6-18h0z" class="Y"></path><path d="M362 298l2 6 2-2 2 4v1 6h0c2 2 2 7 3 9 1 3 2 6 2 9l3 8c1 4 4 7 5 11 0 1 1 2 2 4 0 3 2 5 3 7-1 0-2-2-4-3l1 1c0 1 1 2 2 3h-1-2-2 0c-4-2-8-5-10-9-5-9-6-20-6-29-1-3-1-7-1-10v-2h2l-1-2c0-2-1-5-2-7l-1-3 1-2z" class="L"></path><path d="M369 337h-1v-1-1c-1-2-1-6-1-8 0-1 0-1 1-2h1v1c1 1 1 3 2 6v3c1 1 2 2 2 3v2h-1l-3-3z" class="G"></path><path d="M369 339c2 2 3 3 5 4s3 3 4 5h0c-1-1-1-2-3-2 1 2 2 4 3 5h1l1 2h0-1c0 2 3 3 3 5l1 1c0 1 1 2 2 3h-1-2-2 0c-4-2-8-5-10-9v-2c0-1-1-3 0-4 2-3 0-2-1-5v-3z" class="k"></path><path d="M362 298l2 6 2-2 2 4v1 6h0l1 13v-1h-1c-1 1-1 1-1 2 0 2 0 6 1 8v1 1h1v2 3c1 3 3 2 1 5-1 1 0 3 0 4v2c-5-9-6-20-6-29-1-3-1-7-1-10v-2h2l-1-2c0-2-1-5-2-7l-1-3 1-2z" class="R"></path><path d="M362 298l2 6 2-2 2 4v1 6c-1 0-1 1-1 1v1c-1 1-2 1-3 2v7c-1-3-1-7-1-10v-2h2l-1-2c0-2-1-5-2-7l-1-3 1-2z" class="K"></path><path d="M366 302l2 4v1 6c-1 0-1 1-1 1-2-3-2-7-3-10l2-2z" class="P"></path><defs><linearGradient id="Ay" x1="407.775" y1="445.029" x2="385.15" y2="456.022" xlink:href="#B"><stop offset="0" stop-color="#bfbdbf"></stop><stop offset="1" stop-color="#d8d0c5"></stop></linearGradient></defs><path fill="url(#Ay)" d="M353 306l1-1c4 1 6 5 9 7v2h-1c0 2-1 5-1 7v1 8c0 2 0 4 1 6v1c1 4 2 7 2 11 1 3 1 5 3 8 0 3 1 5 3 7l1 1v1c0 2 0 3 1 5 2 4 4 10 5 14 1 1 2 3 2 5l1 2c1-1 1-1 1-2h1c-1 3-1 4 0 7h0l-1 2c0 1 0 1 1 2l1 2 1-1 1-1c0 6 4 13 6 18 2 6 4 11 6 16l5 15 1-2h0l1-1v6h1c0-2 1-4 2-6 2 0 3-1 6 1v-1l-1-1c2 1 3 1 4 2l1-1c2 0 3 2 5 2-1-1 0-1-1-1h0c-1 0-2-1-3-1l1-1c5 2 10 4 14 7l3 2 1-2a30.44 30.44 0 0 1 8 8c2 3 7 10 7 14-1 0-1 1-2 0v2h7c-1-1-2-2-2-4l2 1c1 1 2 1 3 2 0 1 0 1 1 1l2 4c2 2 4 3 3 6-1 2-1 3-2 4 1 1 1 1 1 2 0 2-1 2-2 4-1 0-1 1-2 2l7-2 2-2c0-1 0-1 1-2l-2 8c0 3 2 8 4 10h0c2 1 2 4 3 6 2 3 3 6 4 10l2 2v2c1 1 1 3 2 4l3 6c1 3 3 6 4 9-1 2 1 4 1 7 1 2 3 5 3 7-1 2 0 4 0 6 0 1 0 2 1 3h1v-3c1 4 4 9 5 13 0 2 0 3 1 5h0l1 2v2c0 1 1 4 2 5 0 2 0 3-1 5v1l-1-1h-1v5c0-1-1 0-1 0v10l1 1h2v1l-1 1c2 0 2 1 3 2-2 0-3 0-5-1l-3 4-2 2-1 1-3 3-1-1c-1-1-2-1-2-1l-7 1h-1l-2-2-1-1c-1-1-2-1-3-1l-2-1-2 2v1h-1c-1 0-1-1-2-2l-6-7-3-4v-1c-1-1-1-2-2-4l-2 1-11-25-3-5 3-1c-4-2-25-56-28-64l-76-189c0-2 0-3-2-5h0l2-2c1 0 1 0 1-1s-1-1-1-2c1 1 4 0 5 0l2 1 1-1h1s1 1 1 2h2 3 1l-1-2v-1c0-3-2-5-3-8h0v-1z"></path><path d="M445 579h1c1 1 1 2 1 3l-2 2v1l-3-5 3-1z" class="j"></path><path d="M346 316l2 1c1 1 2 1 2 2v1c0 5 4 10 6 14 2 5 3 9 5 14l-1 2-14-34z" class="c"></path><path d="M404 446v6h1c-1 3-1 5-1 8 1 2 2 4 3 7-1-1-2-2-2-3-2-1-3-2-5-3l-2-2-1-2c0-3 0-4 1-6v-4l3 8c1-2 1-4 1-6l1-2h0l1-1z" class="L"></path><path d="M404 446v6c0 2-1 4-1 6h0-1c0-2 0-2-1-3 1-2 1-4 1-6l1-2h0l1-1z" class="M"></path><path d="M360 339l4 9c1 3 1 5 3 8 0 3 1 5 3 7l1 1v1c0 2 0 3 1 5 2 4 4 10 5 14 1 1 2 3 2 5h-1c-1-2-2-5-3-8 0-2-1-3-1-5-1-1-1-4-2-5l-1 1 4 12c1 2 1 3 1 5v1l-16-40 1-2v1c1 2 2 3 3 5 0-2-1-5-2-7s-3-5-2-8z" class="S"></path><path d="M376 390v-1c0-2 0-3-1-5l-4-12 1-1c1 1 1 4 2 5 0 2 1 3 1 5l3 8h1l1 2c1-1 1-1 1-2h1c-1 3-1 4 0 7h0l-1 2c0 1 0 1 1 2l1 2 1-1 1-1c0 6 4 13 6 18 2 6 4 11 6 16l5 15c0 2 0 4-1 6l-3-8-22-57z" class="b"></path><path d="M353 306l1-1c4 1 6 5 9 7v2h-1c0 2-1 5-1 7v1 8c0 2 0 4 1 6v1c1 4 2 7 2 11l-4-9c-1 3 1 6 2 8s2 5 2 7c-1-2-2-3-3-5v-1c-2-5-3-9-5-14-2-4-6-9-6-14 0-1 0 1 0-1 0-1-1-1-2-2l1-1h1s1 1 1 2h2 3 1l-1-2v-1c0-3-2-5-3-8h0v-1z" class="T"></path><path d="M360 339h0c-1-5-4-8-5-12l-1-4h0c1 0 1 1 3 1h0c3 0 2 3 2 4l2 2c0 2 0 4 1 6v1c1 4 2 7 2 11l-4-9zm-7-33l1-1c4 1 6 5 9 7v2h-1c0 2-1 5-1 7v1h-1 0c-1 1-1 1-2 1-1-1-2-1-4-1l-2-2h0 4v1c1-1 1-1 2-1h1v-1-1c-1 1-1 1-2 1l-1-1h1l-1-2v-1c0-3-2-5-3-8h0v-1z" class="L"></path><path d="M416 447l1-1c2 0 3 2 5 2-1-1 0-1-1-1h0c-1 0-2-1-3-1l1-1c5 2 10 4 14 7l3 2 1-2a30.44 30.44 0 0 1 8 8c2 3 7 10 7 14-1 0-1 1-2 0v2h7c-1-1-2-2-2-4l2 1c1 1 2 1 3 2 0 1 0 1 1 1l2 4c2 2 4 3 3 6-1 2-1 3-2 4 0 1-2 3-3 3-1 1-2 2-4 3l-1-1h-1v2h-1l-3-3h-1l-2-2v1l2 2v1h-2 0c-1 1-2 2-2 3-1 6-2 24 0 29h-1c1 4 1 7 4 10-1 1-1 1-2 1l-1 1-2 9v6l-2-2-1-4-13-32c-4-8-7-17-11-25-3-7-6-14-8-21l-2-4c-1-3-2-5-3-7 0-3 0-5 1-8 0-2 1-4 2-6 2 0 3-1 6 1v-1l-1-1c2 1 3 1 4 2z" class="O"></path><path d="M428 514l-1-4c1-2 1-3 3-3v2 5h-2z" class="R"></path><path d="M426 502l1 2-1 1c-3-2-5-6-5-9-1 0 0-2 0-2l5 8z" class="N"></path><path d="M407 457h3l-1 1v1l-1 1s-1 0-2 1l2 2c1 2 1 4 1 7v1l-2-4c-1-3-2-5-3-7h2c1-1 1-1 1-3z" class="G"></path><path d="M410 467l1-1c5 3 11 5 17 7 1 0 4 1 4 2h0c-2 2-3 3-4 5h0l-1 1c-1 0-3 0-4-1h-1c-6-3-8-7-12-13z" class="L"></path><path d="M428 480c0-1-1-2-2-3l1-1c1-1 3-1 5-1-2 2-3 3-4 5h0z" class="P"></path><path d="M432 462h0c2 3 4 7 6 11 1 2 2 4 4 6 1 0 2-1 3-2v-3l1 1v-2l3-2c0 1 1 2 1 3v2c-3 3-4 4-5 8 0 3 0 4 2 6v1c1 0 3 2 4 3h-1l-2-2v1l2 2v1h-2 0c-1 1-2 2-2 3l-1-2h0l1-1v-1l-5-4c-1-2-1-3-2-5v-7l-5 4c-1 0-1 0-2-1 0-2 3-4 3-7 1-1 0-1 0-2l-3-2-1-3c0-1 1-1 2-2-1-2-2-2-1-4z" class="L"></path><path d="M439 486c2 1 2 0 3 2 0 0 0 1-1 1v2c-1-2-1-3-2-5z" class="P"></path><path d="M449 471c0 1 1 2 1 3-1 1-2 1-3 2-1-1-1 0-1-1v-2l3-2z" class="o"></path><path d="M431 468c0-1 1-1 2-2 1 2 2 5 2 7l-3-2-1-3z" class="p"></path><path d="M410 457h0c1-1 2-2 2-3 2-1 5-1 7-1h2l4 2v-2l2 2c2 2 4 4 5 7-1 2 0 2 1 4-1 1-2 1-2 2l1 3h-7l-9-3-1-1c-2-1-4-3-4-5-1 0-2-1-2-1v-2h0v-1l1-1z" class="Y"></path><path d="M425 453l2 2c2 2 4 4 5 7-1 2 0 2 1 4-1 1-2 1-2 2l1 3h-7l-9-3-1-1c-2-1-4-3-4-5 6 3 12 7 20 8-3-5-5-10-6-15v-2z" class="g"></path><path d="M431 468l-2-7v-1h0v-1c-2-1-2-2-2-4 2 2 4 4 5 7-1 2 0 2 1 4-1 1-2 1-2 2z" class="n"></path><path d="M410 467c4 6 6 10 12 13h1c1 1 3 1 4 1l1-1h0l2 4h6l1 1h1v2c-1 0-1 1-2 1 1 1 2 2 3 2l1 1-1 1h0c-3 0-6 1-8 4-2 2-3 4-3 7v-1h-2l-5-8s-3-7-3-8l-3-3c0-1 0-1-1-2 0-1 0-2-1-3 0-2-1-4-2-5l-1-1c-1-2 0-3 0-5z" class="L"></path><path d="M410 467c4 6 6 10 12 13h1c1 1 3 1 4 1l1-1h0l2 4h6c0 1-1 2-2 3-3 0-4-2-6-3-4-2-8-3-12-6l-3-5 5 13-3-3c0-1 0-1-1-2 0-1 0-2-1-3 0-2-1-4-2-5l-1-1c-1-2 0-3 0-5z" class="k"></path><path d="M455 472l2 1c1 1 2 1 3 2 0 1 0 1 1 1l2 4c2 2 4 3 3 6-1 2-1 3-2 4 0 1-2 3-3 3-1 1-2 2-4 3l-1-1h-1v2h-1l-3-3c-1-1-3-3-4-3v-1c-2-2-2-3-2-6 1-4 2-5 5-8h7c-1-1-2-2-2-4z" class="G"></path><path d="M453 477h5c2 1 4 3 5 5v1c-1 1 0 1-1 1-2-3-2-4-5-6h-3l-1-1z" class="g"></path><path d="M455 479h1c1 1 3 2 3 3 1 2 0 2 0 4l-4 1c-2 0-3-1-4-2s-1-2 0-3c1-2 2-2 4-3z" class="o"></path><path d="M447 491l1-2v-1h1c2 2 4 3 7 3 2-1 4-2 6-4v3l-3 2h1 0c1 0 1 0 1 1-1 1-2 2-4 3l-1-1h-1v2h-1l-3-3c-1-1-3-3-4-3z" class="I"></path><defs><linearGradient id="Az" x1="427.117" y1="479.607" x2="417.204" y2="443.684" xlink:href="#B"><stop offset="0" stop-color="#24262a"></stop><stop offset="1" stop-color="#3f3f40"></stop></linearGradient></defs><path fill="url(#Az)" d="M416 447l1-1c2 0 3 2 5 2-1-1 0-1-1-1h0c-1 0-2-1-3-1l1-1c5 2 10 4 14 7l3 2 1-2a30.44 30.44 0 0 1 8 8c2 3 7 10 7 14-1 0-1 1-2 0 0-1-1-2-1-3l-3 2v2l-1-1v3c-1 1-2 2-3 2-2-2-3-4-4-6-2-4-4-8-6-11h0c-1-3-3-5-5-7l-2-2v2l-4-2h-2c-2 0-5 0-7 1 0 1-1 2-2 3h0-3c0 2 0 2-1 3h-2c0-3 0-5 1-8 0-2 1-4 2-6 2 0 3-1 6 1v-1l-1-1c2 1 3 1 4 2z"></path><path d="M416 447l1-1c2 0 3 2 5 2-1-1 0-1-1-1h0c-1 0-2-1-3-1l1-1c5 2 10 4 14 7l-2 1c-5-3-10-4-15-6z" class="p"></path><path d="M408 451c1-1 2-1 4-1 4 0 9 2 13 3v2l-4-2h-2c-2 0-5 0-7 1 0 1-1 2-2 3h0-3v-2c0-1 0-2 1-4z" class="X"></path><path d="M408 451c1 1 1 1 1 2l1 1v1h-3c0-1 0-2 1-4z" class="Y"></path><path d="M437 452a30.44 30.44 0 0 1 8 8c2 3 7 10 7 14-1 0-1 1-2 0 0-1-1-2-1-3l-3 2v2l-1-1c-1-4-3-9-5-13-3-3-6-5-9-8l2-1 3 2 1-2z" class="n"></path><path d="M437 452a30.44 30.44 0 0 1 8 8c2 3 7 10 7 14-1 0-1 1-2 0 0-1-1-2-1-3-3-8-7-12-13-17l1-2z" class="J"></path><defs><linearGradient id="BA" x1="432.271" y1="513.474" x2="439.175" y2="514.114" xlink:href="#B"><stop offset="0" stop-color="#9c9b9c"></stop><stop offset="1" stop-color="#bbbabb"></stop></linearGradient></defs><path fill="url(#BA)" d="M439 492h0 1c2 1 3 3 5 4l1-1v1l-1 1h0l1 2c-1 6-2 24 0 29h-1c1 4 1 7 4 10-1 1-1 1-2 1l-1 1-2 9v6l-2-2-1-4-13-32v-3h2v-5-2-1-6c1-2 4-4 6-5s5 0 7 1h0l-2-2h-1c-1-1-1-1-1-2z"></path><path d="M442 531v-3c-1-2 0-8-1-11-1-2-1-4 1-7v1l1 3c1 2 1 4 1 5-1 2-1 3-1 4h0v-2c-1 2-1 5 0 7 0 3 1 3 1 6-1-1-1-2-2-3z" class="l"></path><path d="M433 521c0-2-1-5 0-6v-3l1 3c1 2 2 4 4 6 2 3 3 7 4 10 1 1 1 2 2 3h0l-1 1c-5 0-6-7-9-10v-1l-1-3z" class="P"></path><path d="M444 519l1 9c1 4 1 7 4 10-1 1-1 1-2 1l-1 1-2 9v6l-2-2-1-4c1-1 1-4 1-5s1-1 1-2v-7l1-1h0c0-3-1-3-1-6-1-2-1-5 0-7v2h0c0-1 0-2 1-4z" class="o"></path><path d="M444 534c1 2 1 5 0 7 0 3-2 6-1 8h1v6l-2-2-1-4c1-1 1-4 1-5s1-1 1-2v-7l1-1z" class="b"></path><path d="M439 492h0 1c2 1 3 3 5 4l1-1v1l-1 1h0l1 2c-1 6-2 24 0 29h-1l-1-9c0-1 0-3-1-5v-4l-2-2c-1 2-2 4-3 5-1-1-2-1-3-2-2-1-3-2-5-2v-2-1c1 0 2 0 3 1h1 5l1-1-1-1v-1c1-1 2-1 2-3h1l-1-2c1 1 2 1 2 2h1v-3l-1-2h0l-2-2h-1c-1-1-1-1-1-2z" class="f"></path><path d="M441 508c0-3 0-3 2-5h1l-1 7-2-2z" class="P"></path><path d="M430 506v-6c1-2 4-4 6-5s5 0 7 1l1 2v3h-1c0-1-1-1-2-2l1 2h-1c0 2-1 2-2 3v1l1 1-1 1h-5-1c-1-1-2-1-3-1z" class="Y"></path><path d="M434 505l-1-1c0-1 0-2 1-3 1-2 2-2 4-2 1 1 2 1 3 2 0 2-1 2-2 3v1c-2 1-3 1-5 0z" class="K"></path><path d="M434 505v-1c1 0 2-1 3-2s0-1 1-1l1 3v1c-2 1-3 1-5 0z" class="C"></path><path d="M430 514c1 2 2 5 3 7l1 3v1c3 3 4 10 9 10v7c0 1-1 1-1 2s0 4-1 5l-13-32v-3h2z" class="N"></path><path d="M434 524v1c3 3 4 10 9 10v7c0 1-1 1-1 2l-1-1-7-19z" class="i"></path><defs><linearGradient id="BB" x1="451.277" y1="566.564" x2="465.742" y2="562.132" xlink:href="#B"><stop offset="0" stop-color="#858688"></stop><stop offset="1" stop-color="#acacac"></stop></linearGradient></defs><path fill="url(#BB)" d="M461 493c1 0 3-2 3-3 1 1 1 1 1 2 0 2-1 2-2 4-1 0-1 1-2 2l7-2 2-2c0-1 0-1 1-2l-2 8c0 3 2 8 4 10h0c2 1 2 4 3 6 2 3 3 6 4 10l2 2v2c1 1 1 3 2 4l3 6c1 3 3 6 4 9-1 2 1 4 1 7 1 2 3 5 3 7-1 2 0 4 0 6 0 1 0 2 1 3h1v-3c1 4 4 9 5 13 0 2 0 3 1 5h0l1 2v2c0 1 1 4 2 5 0 2 0 3-1 5v1l-1-1h-1v5c0-1-1 0-1 0v10l1 1h2v1l-1 1c2 0 2 1 3 2-2 0-3 0-5-1l-3 4-2 2-1 1-3 3-1-1c-1-1-2-1-2-1l-7 1h-1l-2-2-1-1c-1-1-2-1-3-1l-2-1-2 2v1h-1c-1 0-1-1-2-2l-6-7-3-4v-1c-1-1-1-2-2-4l-2 1-11-25v-1l2-2 2 3h1c1 1 1 2 2 2l2-2-2-3h1l-11-29 2 2v-6l2-9 1-1c1 0 1 0 2-1-3-3-3-6-4-10h1c-2-5-1-23 0-29 0-1 1-2 2-3h0 2v-1l-2-2v-1l2 2h1l3 3h1v-2h1l1 1c2-1 3-2 4-3z"></path><path d="M468 565c2 2 4 5 4 8-1 0-1-1-2-1-2-2-2-5-2-7z" class="U"></path><path d="M448 542l4 6h-2v1c-1 0-2-1-3-3 0-1 0-2 1-4z" class="f"></path><path d="M444 549l2-9 1-1 1 3c-1 2-1 3-1 4-1 2-1 5-1 7s1 4 2 6l6 15c0 1 0 1-1 1l-9-20v-6z" class="E"></path><path d="M445 528h1c1 2 1 3 2 5h1c1 1 4 5 4 7 2 3 4 5 7 8v-1c2 1 4 4 6 4 2 2 3 4 6 4l3 1h0l-1 1v2c1 1 1 1 1 2s1 2 1 3l-3-4-2-2-1 2 1 3h0l-1 1c0 1 2 2 2 4l-1-1h0c-1-2-2-3-4-5 0 0-1 0-1-1-1 0-2-1-3-2a57.31 57.31 0 0 1-11-11l-4-6-1-3c1 0 1 0 2-1-3-3-3-6-4-10z" class="j"></path><path d="M468 559l1-1 1 2 1 3h-1c-1-1-1-2-2-4h0z" class="n"></path><path d="M468 559c-2 0-2 0-3-2 0-1-1-2-1-3l1 1c2 1 3 1 3 4h0z" class="d"></path><path d="M460 547c2 1 4 4 6 4 2 2 3 4 6 4l3 1h0l-1 1v2c1 1 1 1 1 2s1 2 1 3l-3-4-2-2c-3-4-8-7-11-10v-1z" class="N"></path><path d="M445 528h1c1 2 1 3 2 5h1c1 1 4 5 4 7h-1c0 2 1 4 0 5v1c-2-3-2-5-3-7v-1c-3-3-3-6-4-10z" class="d"></path><path d="M471 558l2 2 3 4c1 0 1 1 2 1v1l2 4v2c1 1 1 2 1 4-1 1-2 1-3 1v12c-1 1-1 1-2 3s-2 5-3 8l-1 2c-1 3-2 6-2 9h-1c0-2-1-3 0-5-1-3-1-4-3-5l-1-4-3-6-3-6h1c0-1 0-1 1-2l2-2h1l-1 2h3c-2-3 2-6 2-9l1-1 3 2v-1-1c0-3-2-6-4-8v-1h-1c0 2-1 2-2 3-1-1-1-1-1-2s2-2 3-3c2 2 3 3 4 5h0l1 1c0-2-2-3-2-4l1-1h0l-1-3 1-2z" class="k"></path><path d="M467 593h1c1-1 3-2 4-3l1 1h-1v2c-1 1-2 1-2 2-2 0-3 0-4-1h0l1-1z" class="X"></path><path d="M469 575h0c2 0 3 1 4 2-1 1-3 2-4 4-1 0-1 1-2 1 0-2 0-3 1-5l1-1v-1z" class="L"></path><path d="M462 591l2-1v1c1 0 1 0 1 1h-1c1 1 2 1 3 1l-1 1h0c1 1 2 1 4 1-1 1-1 2-3 2h-2l-3-6z" class="i"></path><path d="M477 576l1 1v12c-1 1-1 1-2 3s-2 5-3 8l-1 2c-1 3-2 6-2 9h-1c0-2-1-3 0-5v-2c0-2 1-3 1-5 5-6 6-10 6-17 1 1 1 3 1 5l-1 3 1-1v-1-12z" class="L"></path><path d="M460 585c3-1 5-1 8-2 2-1 3-1 6-1v3l-1 1-1-1-1 3c0 1 1 1 1 1-1 2-4 2-7 3 0-1 0-1-1-1v-1l-2 1-3-6h1 0z" class="P"></path><path d="M460 585l1 1c1 0 0 0 1 1v1c1 1 1 1 2 1 1-2 0-2 1-3 0-1 1-1 2-2h1v2c-1 1-1 2-1 3h1c1 0 2 0 3-1 0 1 1 1 1 1-1 2-4 2-7 3 0-1 0-1-1-1v-1l-2 1-3-6h1 0z" class="X"></path><path d="M471 558l2 2 3 4c1 0 1 1 2 1v1l2 4v2c1 1 1 2 1 4-1 1-2 1-3 1l-1-1v12 1l-1 1 1-3c0-2 0-4-1-5s-1-1-1-2c0-4-2-8-3-12 0-2-2-3-2-4l1-1h0l-1-3 1-2z" class="P"></path><path d="M473 560l3 4c1 0 1 1 2 1v1l2 4v2c1 1 1 2 1 4-1 1-2 1-3 1l-1-1v-4c-1-4-2-8-4-12z" class="G"></path><path d="M477 572h1l1 1 1 1v-2c1 1 1 2 1 4-1 1-2 1-3 1l-1-1v-4z" class="F"></path><path d="M442 553l2 2 9 20c1 0 1 0 1-1l1 1c1 0 2 0 2 2 2 2 1 4 4 6-1 1-1 1-1 2h-1l3 6 3 6 1 4c2 1 2 2 3 5-1 2 0 3 0 5h1l1 2v1c1 1 1 1 2 1l1 1-2 1 2 3c1-1 2-2 2-3 2 1 3 2 4 2l1-1 3 3v-1c1 1 2 1 3 1 0 1-1 2 0 3v1l1-1c1 1 1 2 2 3v1l-7 1h-1l-2-2-1-1c-1-1-2-1-3-1l-2-1-2 2v1h-1c-1 0-1-1-2-2l-6-7-3-4v-1c-1-1-1-2-2-4l-2 1-11-25v-1l2-2 2 3h1c1 1 1 2 2 2l2-2-2-3h1l-11-29z" class="p"></path><path d="M458 609l-2-5c0-2-1-3 0-5v-1c1 1 1 2 1 3 2 3 4 6 5 9s5 6 7 9c0 2 1 3 3 4 1 0 1 0 2 1h0 0l-2 2v1h-1c-1 0-1-1-2-2l-6-7-3-4v-1c-1-1-1-2-2-4z" class="n"></path><path d="M465 609l3 4 4 4 2 3c1-1 2-2 2-3 2 1 3 2 4 2l1-1 3 3v-1c1 1 2 1 3 1 0 1-1 2 0 3v1l1-1c1 1 1 2 2 3v1l-7 1h-1l-2-2-1-1c-1-1-2-1-3-1l-1-2v-1c-4-2-10-9-10-13z" class="L"></path><path d="M475 623c3 1 4 2 7 2l1-1h1c1 1 2 1 3 1h0l1-1c1 1 1 2 2 3v1l-7 1h-1l-2-2-1-1c-1-1-2-1-3-1l-1-2z" class="j"></path><path d="M476 617c2 1 3 2 4 2l1-1 3 3v-1c1 1 2 1 3 1 0 1-1 2 0 3v1h0c-1 0-2 0-3-1h-1c-3-1-6-2-9-4 1-1 2-2 2-3z" class="g"></path><path d="M480 619l1-1 3 3v-1c1 1 2 1 3 1 0 1-1 2 0 3v1h0c-1 0-2 0-3-1h1c-1-1-4-2-4-2v-2s0-1-1-1z" class="U"></path><path d="M442 553l2 2 9 20c1 0 1 0 1-1l1 1c1 0 2 0 2 2 2 2 1 4 4 6-1 1-1 1-1 2h-1l3 6 3 6 1 4c2 1 2 2 3 5-1 2 0 3 0 5h1l1 2v1c1 1 1 1 2 1l1 1-2 1-4-4-3-4c-1-3-3-5-4-8-2-6-5-13-8-19l-11-29z" class="M"></path><path d="M464 601h2c2 1 2 2 3 5-1 2 0 3 0 5h1l1 2v1c1 1 1 1 2 1l1 1-2 1-4-4c-1-4-2-8-4-12z" class="D"></path><path d="M454 574l1 1c1 0 2 0 2 2 2 2 1 4 4 6-1 1-1 1-1 2h-1l3 6 3 6 1 4h-2 0c-5-7-8-17-11-26 1 0 1 0 1-1z" class="G"></path><path d="M455 575c1 0 2 0 2 2 2 2 1 4 4 6-1 1-1 1-1 2h-1c-2-3-3-7-4-10z" class="T"></path><path d="M487 540c1 3 3 6 4 9-1 2 1 4 1 7 1 2 3 5 3 7-1 2 0 4 0 6 0 1 0 2 1 3h1v-3c1 4 4 9 5 13 0 2 0 3 1 5h0l1 2v2c0 1 1 4 2 5 0 2 0 3-1 5v1l-1-1h-1v5c0-1-1 0-1 0v10l1 1h2v1l-1 1c2 0 2 1 3 2-2 0-3 0-5-1l-3 4-2 2-1 1-3 3-1-1c-1-1-2-1-2-1v-1c-1-1-1-2-2-3l-1 1v-1c-1-1 0-2 0-3-1 0-2 0-3-1v1l-3-3-1 1c-1 0-2-1-4-2 0 1-1 2-2 3l-2-3 2-1-1-1c-1 0-1 0-2-1v-1l-1-2c0-3 1-6 2-9l1-2c1-3 2-6 3-8s1-2 2-3v-12c1 0 2 0 3-1 0-2 0-3-1-4v-2l-2-4c1-2 4-3 4-5l-2-1c3-2 4-3 6-6 0-1 1-3 1-4l-1-2c0-2 0-1-1-2-1 0-2-1-2-2h2 1 1v-2-2z" class="H"></path><path d="M492 585h0 3v1c-2 1-3 2-6 2l2-3h1z" class="Y"></path><path d="M485 581c0 2 0 3 1 4l1-1 1-1v1h1 2l1 1h-1l-2 3-3 1c-2 0-3 0-4-1l1-2c0-2 1-3 2-5z" class="L"></path><path d="M485 581c0 2 0 3 1 4l1-1 1-1v1h1 2l1 1h-1c-3 1-5 1-8 1 0-2 1-3 2-5z" class="B"></path><path d="M496 600v-1-1c-1 0-1-1-2-2 2 0 2-1 4 0 2 3 2 9 3 13v6s0 1-1 1c0 0-1-1-1-2v-3c0-1 1-2 1-3-2-2-2-3-2-6h0c-1-1-1-2-2-2z" class="i"></path><path d="M487 593s1 0 1 1c1 1 2 2 4 3l4 3c1 0 1 1 2 2h0c0 3 0 4 2 6 0 1-1 2-1 3v3l-10-16h0c-2-2-2-3-2-5z" class="L"></path><path d="M497 569c1 4 4 9 5 13 0 2 0 3 1 5h0l1 2v2c0 1 1 4 2 5 0 2 0 3-1 5v1l-1-1h-1v5c0-1-1 0-1 0-1-5-3-10-3-15v-8h-1l-1-1 1-1 1 2 1-1c-1-2-2-2-3-3 1-2 0-3-1-4v-1h-1l1-2h1v-3z" class="J"></path><path d="M499 583c1 1 1 3 2 5l2 10-1 1c0-2-1-3-1-5 0-1-1-2-2-3v-8z" class="U"></path><path d="M503 598l1-1c0-3-1-6-2-9l1-1 1 2v2c0 1 1 4 2 5 0 2 0 3-1 5v1l-1-1h-1v5c0-1-1 0-1 0-1-5-3-10-3-15 1 1 2 2 2 3 0 2 1 3 1 5l1-1z" class="M"></path><path d="M482 590h4c0 1 0 1-1 1l2 2c0 2 0 3 2 5h0l10 16c0 1 1 2 1 2l1 1 1-1 1 1h2v1l-1 1c2 0 2 1 3 2-2 0-3 0-5-1l-3 4-2 2-1 1-3 3-1-1c-1-1-2-1-2-1v-1c-1-1-1-2-2-3l-1 1v-1c-1-1 0-2 0-3v-4l-1-5c0-2 0-4-1-5h0v-1-1c1-1 0-3 0-5l-3-6v-4z" class="G"></path><path d="M502 616l1 1h2v1l-1 1c2 0 2 1 3 2-2 0-3 0-5-1l-3 4-2-2c1-1 2-2 4-3h0v-2l1-1z" class="V"></path><path d="M502 616l1 1h2v1l-1 1h-2 0-1v-2l1-1z" class="M"></path><path d="M494 609c2 3 5 6 6 9-2 2-4 5-7 5 0-2 0-2 1-4v-2l-1 1v-2h-1c-1-1-1 0-2-1l2-1c0 1 0 1 1 1v-2c0-1 0-2 1-4z" class="f"></path><path d="M485 606v-1c3 2 5 5 7 7l1 1v2c-1 0-1 0-1-1l-2 1c1 1 1 0 2 1h1v2 1l-3 8c-1-1-1-2-2-3l-1 1v-1c-1-1 0-2 0-3v-4l-1-5c0-2 0-4-1-5h0v-1z" class="O"></path><path d="M490 615c1 1 1 0 2 1h1v2 1l-3 8c-1-1-1-2-2-3 0-2 0-5 1-8 0-1 1-1 1-1z" class="l"></path><path d="M492 616h1v2 1h-2l-1-1 2-2z" class="j"></path><path d="M482 590h4c0 1 0 1-1 1l2 2c0 2 0 3 2 5h0c0 3 2 5 3 7 1 1 2 3 2 4-1 2-1 3-1 4l-1-1c-2-2-4-5-7-7 1-1 0-3 0-5l-3-6v-4z" class="T"></path><path d="M485 600c1 2 2 3 4 5v1c0 1 1 1 2 2v-1h1v-2c1 1 2 3 2 4-1 2-1 3-1 4l-1-1c-2-2-4-5-7-7 1-1 0-3 0-5z" class="U"></path><path d="M481 576h1l-1 3 2 1h1c0 1 0 1 1 1-1 2-2 3-2 5l-1 2h-1c1 0 1 1 1 2v4l3 6c0 2 1 4 0 5v1 1h0c1 1 1 3 1 5l1 5v4c-1 0-2 0-3-1v1l-3-3-1 1c-1 0-2-1-4-2 0 1-1 2-2 3l-2-3 2-1-1-1c-1 0-1 0-2-1v-1l-1-2c0-3 1-6 2-9l1-2 3-8c1-2 1-2 2-3v-12c1 0 2 0 3-1z" class="P"></path><path d="M475 605v1c1-2 0-3 2-4 0 1 1 3 1 5 0 1 0 2 1 2l-1 1h0v2h-1v-2h-1c0 1 0 3-1 4l-1-3c1-1 0-5 1-6zm5 1v-1c2 0 2 2 5 2h0c1 1 1 3 1 5h-1l-3-1c-1-1-3-3-3-4v-2l1 1z" class="U"></path><path d="M480 606v-1c2 0 2 2 5 2h0v2l-2 1h-1c0-2-1-3-2-3v-1z" class="Y"></path><path d="M476 592l-1 13c-1 1 0 5-1 6h0v-4l-2-1v-4l1-2 3-8z" class="S"></path><path d="M472 602v4l2 1v4h0l1 3c0 1 1 2 1 3s-1 2-2 3l-2-3 2-1-1-1c-1 0-1 0-2-1v-1l-1-2c0-3 1-6 2-9z" class="F"></path><path d="M471 613l1-4h1v6c-1 0-1 0-2-1v-1z" class="C"></path><path d="M475 614c1-1 1-3 1-4h1v2h1v-2h0l2 2h0c2 1 3 2 5 2l1-1-1-1h1l1 5v4c-1 0-2 0-3-1v1l-3-3-1 1c-1 0-2-1-4-2 0-1-1-2-1-3z" class="M"></path><path d="M485 612h1l1 5v4c-1 0-2 0-3-1l-3-3c-1-2-1-3-1-5h0c2 1 3 2 5 2l1-1-1-1z" class="T"></path><path d="M481 617h1c1 0 1 0 2 1h1s1-1 2-1v4c-1 0-2 0-3-1l-3-3z" class="X"></path><path d="M481 576h1l-1 3 2 1h1c0 1 0 1 1 1-1 2-2 3-2 5l-1 2h-1c1 0 1 1 1 2v4l3 6c0 2 1 4 0 5v1c-2-1-2-4-3-6-2-4-4-6-4-11v-12c1 0 2 0 3-1z" class="D"></path><path d="M481 588v-9l2 1h1c0 1 0 1 1 1-1 2-2 3-2 5l-1 2h-1z" class="f"></path><path d="M482 594l3 6c0 2 1 4 0 5v1c-2-1-2-4-3-6h0c0-3-1-4 0-6z" class="K"></path><path d="M487 540c1 3 3 6 4 9-1 2 1 4 1 7 1 2 3 5 3 7-1 2 0 4 0 6 0 1 0 2 1 3l-1 2h1v1c1 1 2 2 1 4l-1 5c-1 0-1 1-1 2v-1h-3 0l-1-1h-2-1v-1l-1 1-1 1c-1-1-1-2-1-4-1 0-1 0-1-1h-1l-2-1 1-3h-1c0-2 0-3-1-4v-2l-2-4c1-2 4-3 4-5l-2-1c3-2 4-3 6-6 0-1 1-3 1-4l-1-2c0-2 0-1-1-2-1 0-2-1-2-2h2 1 1v-2-2z" class="S"></path><path d="M487 540c1 3 3 6 4 9-1 2 1 4 1 7h-1c0-2-1-4-2-6h-1l1 5-3-1c0-1 1-3 1-4l-1-2c0-2 0-1-1-2-1 0-2-1-2-2h2 1 1v-2-2z" class="U"></path><path d="M485 544h1c1 2 2 3 1 6l-1-2c0-2 0-1-1-2-1 0-2-1-2-2h2z" class="K"></path><path d="M482 576c3-1 6-9 7-11l1-2 1-4h1c0 3 0 4-1 6v1l1-2h0c0 6-5 12-7 17-1 0-1 0-1-1h-1l-2-1 1-3z" class="L"></path><path d="M495 569c0 1 0 2 1 3l-1 2h1v1c1 1 2 2 1 4l-1 5c-1 0-1 1-1 2v-1h-3 0l-1-1h-2-1v-1l-1 1v-1-2h1c2-4 4-9 7-12z" class="f"></path><path d="M480 560c3-2 4-3 6-6l3 1c0 3 1 6-1 10-1 2-3 8-5 9h-1v-3h0c-1 0-1 1-2 1v-2l-2-4c1-2 4-3 4-5l-2-1z" class="T"></path><path d="M480 570v-3c1-1 1-2 2-3s1-2 2-4l-1-1h1 1v3 2c1 1 2 1 3 1-1 2-3 8-5 9h-1v-3h0c-1 0-1 1-2 1v-2z" class="L"></path><path d="M485 562v2h0v3c0 1 0 1-1 2 0-1-1-2-1-3 0-2 1-2 2-4z" class="f"></path><path d="M461 493c1 0 3-2 3-3 1 1 1 1 1 2 0 2-1 2-2 4-1 0-1 1-2 2l7-2 2-2c0-1 0-1 1-2l-2 8c0 3 2 8 4 10h0c2 1 2 4 3 6 2 3 3 6 4 10l2 2v2c1 1 1 3 2 4l3 6v2 2h-1-1-2c0 1 1 2 2 2 1 1 1 0 1 2l1 2c0 1-1 3-1 4-2 3-3 4-6 6l2 1c0 2-3 3-4 5v-1c-1 0-1-1-2-1 0-1-1-2-1-3s0-1-1-2v-2l1-1h0l-3-1c-3 0-4-2-6-4-2 0-4-3-6-4v1c-3-3-5-5-7-8 0-2-3-6-4-7h-1c-1-2-1-3-2-5-2-5-1-23 0-29 0-1 1-2 2-3h0 2v-1l-2-2v-1l2 2h1l3 3h1v-2h1l1 1c2-1 3-2 4-3z" class="P"></path><path d="M455 497l2 7c0 3 0 5 2 7l1 7v5h1c0-2 1-3 1-5v-3h1 2c0 4-3 8-5 11-3-1-4-23-6-27h0v-2h1z" class="O"></path><path d="M461 493c1 0 3-2 3-3 1 1 1 1 1 2 0 2-1 2-2 4-1 0-1 1-2 2l-1 1c0 2 0 3 1 5 1 0 1 1 2 1v5c0 1 0 1 2 2h1v1h-2c-1 0-1 0-1 2h-1v3c0 2-1 3-1 5h-1v-5l-1-7c-2-2-2-4-2-7l-2-7v-2h1l1 1c2-1 3-2 4-3z" class="X"></path><path d="M461 493c1 0 3-2 3-3 1 1 1 1 1 2 0 2-1 2-2 4-1 0-1 1-2 2l-1 1-3-1c0 1 0 1 2 2l-1 1s-1 1-1 2l1 1c1 2 1 5 1 7-2-2-2-4-2-7l-2-7v-2h1l1 1c2-1 3-2 4-3z" class="M"></path><path d="M466 541c-2-4-3-9-5-14 0-1 0 0 1-1l4-7 1 1 1 1h0v2s1 1 1 2l1 4c1 2 2 3 2 5 1 2 2 4 2 6l1 1 1 1v-1h1l-1 1v3h0 0l-2-1h0l-1 1 1 1h3c-2 1-5 1-7 1-2-1-3-2-4-4v-2z" class="O"></path><path d="M472 534c1 2 2 4 2 6l1 1 1 1v-1h1l-1 1v3h0 0l-2-1h0c-2-2-3-5-4-8l2-2z" class="d"></path><path d="M467 520l1 1h0v2s1 1 1 2l1 4c1 2 2 3 2 5l-2 2c-1-3-2-7-3-11l-1-1c1-1 1-3 1-4z" class="n"></path><path d="M466 541c-2-4-3-9-5-14 0-1 0 0 1-1l4-7 1 1c0 1 0 3-1 4l1 1c-1 2-2 4-2 6l-1-1h0 0c-1 1 0 3 0 5l2 6z" class="S"></path><path d="M459 533h1c0 3 0 4 3 5l1 2c-1 0-2-1-2-2 0 2 2 4 3 5h1c1 2 2 3 4 4 2 0 5 0 7-1h-3l-1-1 1-1h0l2 1h0 0c3-1 4-2 7-1 0 1 1 2 2 2 1 1 1 0 1 2l1 2c0 1-1 3-1 4-2 3-3 4-6 6l2 1c0 2-3 3-4 5v-1c-1 0-1-1-2-1 0-1-1-2-1-3s0-1-1-2v-2l1-1h0l-3-1c-3 0-4-2-6-4-2 0-4-3-6-4l-3-3 2-2 3 4h1c-1-1 0-1-1-2-1 0-1-1-1-1-2-3-2-6-2-9v-1z" class="L"></path><path d="M485 546c1 1 1 0 1 2l1 2c0 1-1 3-1 4-2 3-3 4-6 6l2 1c0 2-3 3-4 5v-1c-1 0-1-1-2-1 0-1-1-2-1-3s0-1-1-2v-2l1-1c2 0 5 0 7-1 2-3 2-6 3-9z" class="R"></path><path d="M480 560l2 1c0 2-3 3-4 5v-1c-1-2-2-4-1-5h3z" class="P"></path><path d="M459 533h1c0 3 0 4 3 5l1 2c-1 0-2-1-2-2 0 2 2 4 3 5v1c2 2 3 3 5 4 1 1 0 1 1 1 2 2 5 3 8 4 1-2 3-3 3-5l-1-2h1c1 0 1 0 2 2-1 1-1 4-3 5h0l-1 1c-1 0-3-1-4-1-1 1-2 0-3 0h-1-1s-1-1-2-1-2-1-3-1c-2 0-4-3-6-4l-3-3 2-2 3 4h1c-1-1 0-1-1-2-1 0-1-1-1-1-2-3-2-6-2-9v-1z" class="k"></path><path d="M450 496v-1l-2-2v-1l2 2h1l3 3v2 2c1 9 1 19 4 27 1 2 1 3 2 5h-1v1c0 3 0 6 2 9 0 0 0 1 1 1 1 1 0 1 1 2h-1l-3-4-2 2 3 3v1c-3-3-5-5-7-8 0-2-3-6-4-7h-1c-1-2-1-3-2-5-2-5-1-23 0-29 0-1 1-2 2-3h0 2z" class="D"></path><path d="M457 544h0c-1-1-2-2-2-3s0-2-1-3-1-1-1-2c-1-4-3-6-3-10h1c1 1 0 4 2 5h1l4-3h0c1 2 1 3 2 5h-1v1c0 3 0 6 2 9 0 0 0 1 1 1 1 1 0 1 1 2h-1l-3-4-2 2z" class="O"></path><path d="M458 528c1 2 1 3 2 5h-1c-2-2-2-2-5-2l4-3h0z" class="g"></path><path d="M446 499c0-1 1-2 2-3h0 2v3l-1 3c-1 11-2 20 0 31h-1c-1-2-1-3-2-5-2-5-1-23 0-29z" class="l"></path><path d="M450 496v-1l-2-2v-1l2 2h1l3 3v2 2c1 9 1 19 4 27h0-1c-2-1-2-1-4-1h-1c0-1-1-2-1-2v-8c1-4 1-9 1-13-1 0-2-1-3-2l1-3v-3z" class="k"></path><path d="M450 499v-2l1 1 1 6c-1 0-2-1-3-2l1-3z" class="B"></path><path d="M471 492l-2 8c0 3 2 8 4 10h0c2 1 2 4 3 6 2 3 3 6 4 10l2 2v2c1 1 1 3 2 4l3 6v2 2h-1-1-2c-3-1-4 0-7 1v-3l1-1h-1v1l-1-1-1-1c0-2-1-4-2-6 0-2-1-3-2-5l-1-4c0-1-1-2-1-2v-2h0v-1c-1-1-1-2-1-3l-1-4v-1h-1c-2-1-2-1-2-2v-5c-1 0-1-1-2-1-1-2-1-3-1-5l1-1 7-2 2-2c0-1 0-1 1-2z" class="L"></path><path d="M463 505v-1c1 1 2 2 2 3v4 1c-2-1-2-1-2-2v-5z" class="Y"></path><path d="M476 542c4-1 6-1 9 2h-2c-3-1-4 0-7 1v-3z" class="D"></path><path d="M468 520l1 1 2 6h1v-1c1 1 1 2 2 3v1l1 1v1c-1 1-1 2-1 4l1 1v1h0c1 1 0 1 1 2h-2c0-2-1-4-2-6 0-2-1-3-2-5l-1-4c0-1-1-2-1-2v-2h0v-1z" class="X"></path><path d="M469 500c0 3 2 8 4 10h0c2 1 2 4 3 6 2 3 3 6 4 10l2 2v2c1 1 1 3 2 4l3 6v2l-2-1h-2l-2-1v-3c0-1 0-2-1-3-3-6-5-12-7-17-2-4-4-8-5-12v-1c0-1-1-2-1-4h2z" class="e"></path><path d="M547 668l4-1v-1c0-1 1-2 2-3h0 2c1-2 2-2 3-4h0l1-1c0 1 1 1 2 2 1 0 3 1 5 1h1-2v1h-2l-1 1c2 0 4 0 6 1 4 2 7 5 9 10v1 8c0 1 1 2 2 4v3c0 1-1 3-1 4-1 1-1 1-2 1v1l1-1v2c-1 1 0 2 0 3v2h0c-3 7-5 13-9 20l3 2c-2 1-3 2-4 3-2-1-3-2-5-3v1c-1 1-1 2-2 3v3 1c4 0 8-1 12-2 2 0 3-1 4 0l-5 14c1-1 2-2 2-3 0 1 1 2 0 3v2c1-1 1-2 2-2v1l1 1-1 1c0 1 1 2 1 3 1 3 2 3 4 5-1 3-3 10-7 12h-1c-1 1-2 2-3 2l-4-2c0 5-1 10-2 14h-2v-2c-1 0-1 0-1 1-2 4-3 7-4 11l-1-2c-1-2-2-2-4-3l-3 9c0 3-2 5-3 7l-5 10c-1-1-1 0-1-1l-2-1c-1-2-1-3-1-5-1-1-2-2-3-4v1l-2-3-1-3c0 1-1 2-2 2v1c-1-2-1-3-2-4l-3 6h0c-2 2-2 4-3 7-1-3 0-6-1-8s-4-2-6-3h-1-1l1 2c-2 2-3 1-5 2v1c-1 0-1 1-1 0l-1 1h-1v-1l-1-5c-1-1-1-2-2-3h-1v2h-1c-1 2-1 5-2 8-2 3-4 5-7 7h0l-2-5c-1-6-5-12-9-17v-2l-1 1c-2-2-2-4-3-5 0-3-2-7-3-9l-3-6c-1-4-3-7-4-11-1-3-2-6-4-9l1-1-3-9c-1-5-4-8-5-13l1-1c-1 0-1-1-2-2v-1c2 1 3 3 5 4 0-1 0-1 1-1h1c2 1 3 2 5 2l-3-3c-2-2-3-4-4-7h-2c-1 0-1-1-1-2-1 2-2 3-2 4l-3-5v-1c-1-2-1-6-1-9l-2-8-1-1c0-3-1-7-1-10 1-1 1-2 2-3l2-1 2 2 1-4 2-7 3-5 1 2 2-2h1l1-1h1l1 2c1 0 2 1 4 2 1 1 5 2 7 3 0 0 1 0 2-1 5 10 8 21 11 30l8-3v-2c-2-6-4-12-7-18h7 0 4c8-2 14-5 21-9h0v2c0 1 1 1 2 2 3 3 6 6 8 10l1-1 1 1c1-2 1-2 1-4-1-1-1-1-1-2h1c1-2 2-3 4-4h0c1 0 2 0 3-1l2 2 3-6h1z" class="m"></path><path d="M503 759l2-1 1 1c0 1 0 2-1 3h-1c-1 0-2 0-3-1v-1h2v-1zm36-14h-1c-2 0-3-2-4-3 0-2 0-3 1-4s1 0 2 0v1 1 3l2 2z" class="E"></path><path d="M526 795l3-6c1 2 1 4 1 7 0 1-1 2-2 2v1c-1-2-1-3-2-4z" class="C"></path><path d="M562 687l3-1c1 0 2 0 3 1-2 1-4 3-7 3l-3 1h-1c2-1 3-2 5-4z" class="S"></path><path d="M556 688s2-1 3-1h3c-2 2-3 3-5 4h1c-1 1-2 2-4 2 0-2 1-4 2-5z" class="Q"></path><path d="M469 734h0c2 0 3 0 4 2 0 2 0 3-1 5l-3-2c0-2-1-3-1-5h1z" class="c"></path><path d="M469 739c0-2-1-3-1-5h1c1 0 2 1 2 2l-2 3z" class="B"></path><path d="M495 758c2-1 3 0 5 0 1 1 1 1 3 1v1h-2v1c1 1 2 1 3 1 0 1-1 1-2 1l-2-1h-1c1 0 1 0 1-1-1-1-5-1-6 0h-1v-1c1 0 2-1 3-2h-1z" class="h"></path><path d="M476 743l1 1v1 1 1c-3 2-5 5-6 7l-1 1c1-5 1-9 5-12h1z" class="b"></path><path d="M488 748l1 1c2 0 3-2 5-2 1 1 1 2 1 3l-1 1h1l4 4h0c0 1-1 1-2 0v-1c-2-1-3-2-5-2h-3c-1-1-2 0-3 0 1-1 1-2 2-4zm-17-16c1 0 2 0 3 1v3c2 2 2 3 4 4 0 1-1 2-2 3h-1l-3-2c1-2 1-3 1-5-1-2-2-2-4-2 2-1 2-1 2-2z" class="a"></path><path d="M548 795c0 3-2 5-3 7l-5 10c-1-1-1 0-1-1v-1h0c1-2 1-5 3-7l3-4v-1l3-3z" class="Z"></path><path d="M561 678h-1c-1 0-1 1-2 1l1-1v-2c1-1 1-1 1-2v-1l1-1v-2h0c-1 2-2 4-3 5h-1l3-5c0-1 1-2 1-3 1 0 2 0 3-1v1l1 1c-1 0-1 1-2 2l1 3c-1 2-2 4-3 5z" class="E"></path><path d="M514 772h1c1-2 1-4 1-6l1-1c1 0 2 1 3 1h1c-2 4-3 9-5 12l-2-6z" class="AI"></path><path d="M555 706h2v1l2 5h-3v1s1 1 2 1l1 1h-1c-2-1-5-1-8-1-1 0-2-1-3-2v-1c1-1 5-2 7-1h3c0-1 0-1-1-2v-1l-1-1z" class="a"></path><path d="M542 736h3c2 0 3-2 6 0v-1c2 0 2 0 3 1h7v4 1l-6-2-1 1 1 3-1 1c-2-4-5-5-8-7l-4-1z" class="B"></path><path d="M546 737c4 0 8-1 11 1l1 1h-3l-1 1 1 3-1 1c-2-4-5-5-8-7z" class="H"></path><path d="M564 673c2 3 3 7 4 10v4c-1-1-2-1-3-1l-3 1h-3c-1 0-3 1-3 1 0-1 1-3 2-4s0-1 1-2c0-1 1-3 2-4s2-3 3-5z" class="D"></path><path d="M559 687c2-1 4-2 6-4h0 3v4c-1-1-2-1-3-1l-3 1h-3z" class="C"></path><path d="M488 741c1-2 2-3 4-4l2-1c-1 1-1 1-1 2h0c2 0 3 1 3 2l1 1-1 1h-2-1c-1 0-2 1-3 2l-5 5c-1 0-2 2-3 1s-2-4-5-4v-1-1c2 1 2 1 4 1h1c2-2 3-4 6-4z" class="a"></path><path d="M482 745c2-2 3-4 6-4-1 1-2 3-4 3h-1l-1 1z" class="E"></path><path d="M541 746c2 0 3 1 4 2l1-1v1 1h1v1l-1 2-3 3h-1 0c-2 0-3 0-4-1-3 1-5 0-8 0l4-4h1c1 0 2-1 3-1s0 0 1-1c-1 0-2-1-3-1 1-2 2 0 3-1h2z" class="a"></path><path d="M538 754l-1-1h2v-1h-1v-1c2 0 2 0 4 1v3h0c-2 0-3 0-4-1z" class="G"></path><path d="M545 748l1-1v1 1h1v1l-1 2-3 3h-1v-3c1-1 2-3 3-4z" class="S"></path><path d="M477 746c3 0 4 3 5 4s2-1 3-1l5-5c1-1 2-2 3-2h1l-6 6c-1 2-1 3-2 4 1 0 2-1 3 0h3c2 0 3 1 5 2v1c0 1-1 1-1 1-3-1-7-2-10 0v1c-3 1-4 3-6 5l3-7c-2-3-2-6-5-8h-1v-1z" class="D"></path><path d="M481 729h1c1 0 1 0 1-1l1 1c-1 1-1 1-1 2 1 1 2 1 2 1 1 0 2-1 3-1 0 0 2 2 3 2l-6 5c-2 1-7 3-8 6l-1-1c1-1 2-2 2-3-2-1-2-2-4-4v-3l1 1h1l1-1c2-1 3-2 4-4z" class="F"></path><path d="M474 733l1 1 1 1c1 0 4-1 5 0 0 1 0 1-2 1-1 1-2 0-3 0h-2v-3z" class="N"></path><path d="M568 664c4 2 7 5 9 10v1 8c0 2 0 4-1 6v-4l-1-1-8-13c0-1-1-2-1-3 1-1 2-1 3-1-1-1-1-2-1-3z" class="C"></path><defs><linearGradient id="BC" x1="536.5" y1="739.866" x2="548.851" y2="746.991" xlink:href="#B"><stop offset="0" stop-color="#212328"></stop><stop offset="1" stop-color="#4a4d51"></stop></linearGradient></defs><path fill="url(#BC)" d="M537 738l14 6c1 1 2 2 4 1l-1-1 1-1 5 9c-2-2-5-5-9-5-2 1-4 3-5 5l1-2v-1h-1v-1-1l-1 1c-1-1-2-2-4-2l-2-1-2-2v-3-1-1z"></path><path d="M495 758h1c-1 1-2 2-3 2v1h1c1-1 5-1 6 0 0 1 0 1-1 1h1c-2 2-3 2-5 3 0 1 0 1-1 2h-2l-1 1h-5c-2 2-2 2-2 4l1 2-1 2c-1-2-2-5-3-7 0-1 0-3 1-4 2-4 9-6 13-7z" class="B"></path><path d="M495 765c1-1 1-2 2-2v-1-1c-1 0-2 0-3 1-2 0-4 2-5 2h-3v-1c2-1 4-2 7-3v1h1c1-1 5-1 6 0 0 1 0 1-1 1h1c-2 2-3 2-5 3z" class="c"></path><path d="M481 769c2-2 3-3 6-4 1 0 2 0 3-1v1l2 2-1 1h-5c-2 2-2 2-2 4l1 2-1 2c-1-2-2-5-3-7z" class="V"></path><path d="M453 722v-1c2 1 3 3 5 4 0-1 0-1 1-1h1c2 1 3 2 5 2l-3-3h1c1 0 2 1 3 1l1 1c1 0 1 0 1-1 1-1 3-2 4-2l1 1 2 1c1 1 2 1 3 1 1 1 2 1 3 2s0 1 0 2c-1 2-2 3-4 4l-1 1h-1l-1-1c-1-1-2-1-3-1l-8-3c-3-1-6-3-8-5-1 0-1-1-2-2z" class="k"></path><path d="M476 727c1 1 2 2 1 3v3l-1 1c0-2-1-3-3-4h0l1-1 2-2z" class="e"></path><path d="M478 725c1 1 2 1 3 2s0 1 0 2c-1 2-2 3-4 4v-3c1-1 0-2-1-3l2-2z" class="b"></path><path d="M453 722v-1c2 1 3 3 5 4 0-1 0-1 1-1h0c2 2 5 3 8 4l1 1h2l-1 1c-1 0-5-2-6-1-3-1-6-3-8-5-1 0-1-1-2-2z" class="AL"></path><path d="M467 725c1 0 1 0 1-1 1-1 3-2 4-2l1 1 2 1c1 1 2 1 3 1l-2 2-2 2-2-1c-2-1-3-2-5-3z" class="G"></path><path d="M473 723l2 1c0 2 0 2-1 3h-1v-4z" class="F"></path><path d="M475 724c1 1 2 1 3 1l-2 2-2 2-2-1 1-1h1c1-1 1-1 1-3z" class="C"></path><path d="M507 727l4 12c0 2 1 4 2 5-1 1-2 1-2 2-1 1 0 2 0 2l-1 1c0 1-1 1-1 2 1 1 1 1 0 3l-1 1-9-24c2 0 4-1 6-2l2-2z" class="AA"></path><path d="M533 786v-2l6-3 1 3h1c-1-1 0-1-1-2h0l5 2c1 0 2 0 2 1-3 2-4 6-6 10-2 3-4 7-5 10-1-1-2-2-3-4v1l-2-3-1-3c2-2 4-4 4-6-1-2-1-2-1-4h0z" class="J"></path><path d="M540 788l1 2c-1 3-2 5-4 7 0-3 1-7 3-9z" class="M"></path><path d="M540 782l5 2c-2 2-3 4-4 6l-1-2 1-2c0-1-1-2-1-2h1c-1-1 0-1-1-2h0z" class="i"></path><path d="M533 786v-2l6-3 1 3s1 1 1 2c-3 1-3 2-5 5h0c1-2 1-3 0-5h-2-1z" class="Q"></path><path d="M533 786h1 2c1 2 1 3 0 5l-3 10v1l-2-3-1-3c2-2 4-4 4-6-1-2-1-2-1-4h0z" class="Z"></path><path d="M577 683c0 1 1 2 2 4v3c0 1-1 3-1 4-1 1-1 1-2 1v1l-1 1h-1l-2 3c-3 0-7 2-11 3-1 1-2 1-3 2v1c1 1 1 1 0 2l2 4h-1l-2-5v-1h-2-8v-1h2c1-1 1-2 2-3 1-3 4-7 7-8l3-1c0-2 0-2 1-2h1v5 3c1 0 2 1 2 1 3-1 5-2 8-4 1-2 3-4 3-7 1-2 1-4 1-6z" class="R"></path><path d="M562 699h1c1 0 2 1 2 1-1 1-2 1-3 2h-1c0-1 0-2 1-3z" class="C"></path><path d="M577 683c0 1 1 2 2 4v3l-3 3-1 1c0 1-1 1-2 2h0c1-2 3-4 3-7 1-2 1-4 1-6zm-19 11l3-1c0-2 0-2 1-2h1v5 3h-1c-1 1-1 2-1 3-2 1-2 1-3 0h-1c0-3 0-5 1-8z" class="G"></path><path d="M558 702c1-2 2-5 4-6v3c-1 1-1 2-1 3-2 1-2 1-3 0z" class="E"></path><path d="M558 715h1l-1-1c-1 0-2-1-2-1v-1h3c0 1 1 4 0 5s-2 1-3 2c1 0 1 1 2 1l2 1c0 1 1 2 2 3v1c-1 1-1 2-2 3v3 1c0 1 0 2 1 3v1h-7c-1-1-1-1-3-1v1c-3-2-4 0-6 0h-3c-1 0-2-1-4-1v-1c4-2 8-1 12-1 1 0 2 1 3 1h3 1c1 0 1 0 2-1h0l-3-2c0 1-1 1-1 2h-1c-1-1-2-1-2-1-1-1-2-1-2-2-2-1-4-3-5-4v-1c1 0 1 0 2 1h1c1 0 1 1 3 2 1 1 2 2 4 2v-2-1l3-3h0c-1-2-2-1-3-2 1 0 2 0 3-1h-1c-1-1-3 0-4 0l-2 1c1 0 1 1 2 2 0 1 1 3 1 4h0l-1-1-1-2c-1-1-1-2-2-3-1 0-2 0-3 1h-1v-1l3-4c1-1 1-1 2-1h1l1-1h5v-1z" class="h"></path><path d="M558 720c-2 0-5 0-7 1h-3v-1c1 0 2-2 3-2 2 0 3 1 5 1 1 0 1 1 2 1z" class="G"></path><path d="M558 725c1 0 0 0 1 1s1 2 0 3c0 2 0 1-1 2-1 0-1 0-2-1 0-3 0-3 2-5z" class="f"></path><path d="M465 757c-1-3-2-6-4-9l1-1c4 8 7 17 11 25 1 5 4 10 5 14 3-1 5-3 7-5l-1-5 1-2-1-2c0-2 0-2 2-4h5l1-1h2c1-1 1-1 1-2 2-1 3-1 5-3l2 1c-3 3-3 8-4 11h0c0 3-1 7-4 9h-1-4c-1 0-2 0-4 1h-2c-1 1-2 1-3 2l-1 1-1 1c-2-2-2-4-3-5 0-3-2-7-3-9l-3-6c-1-4-3-7-4-11z" class="g"></path><path d="M484 772c2 1 3 2 5 3 0 2-1 3-3 5l-1 1-1-5 1-2-1-2z" class="T"></path><path d="M484 772c0-2 0-2 2-4h5l1 2c0 2 0 3-2 4l-1 1c-2-1-3-2-5-3z" class="o"></path><path d="M483 784c1-1 2-2 3-2 2-1 4-3 5-4v-1c2-2 5-3 7-3 0 3-1 7-4 9h-1-4c-1 0-2 0-4 1h-2z" class="H"></path><path d="M483 784h2c2-1 3-1 4-1h4l1 4h1c0 1 1 2 1 3h0c2 1 3 2 4 4h0v2h-1c-1 2-1 5-2 8-2 3-4 5-7 7h0l-2-5c-1-6-5-12-9-17v-2l1-1c1-1 2-1 3-2z" class="m"></path><path d="M483 784h2c2-1 3-1 4-1l-2 2c1 6 5 10 7 15-1 0-1-1-2-3-1 0 0 0-1-1v1c-1-3-6-7-6-10l1-1h-1-5c1-1 2-1 3-2z" class="Z"></path><path d="M489 783h4l1 4h1c0 1 1 2 1 3h0c2 1 3 2 4 4h0v2h-1c-1 2-1 5-2 8-1-1-1-1-2-3l-1-1c-2-5-6-9-7-15l2-2z" class="i"></path><path d="M494 787h1c0 1 1 2 1 3h0c2 1 3 2 4 4h0v2h-1c-1 2-1 5-2 8-1-1-1-1-2-3 1-4 0-9-1-14z" class="G"></path><path d="M519 736c0 1 0 2 1 3l1 3v2l1 1v-1c0-2 0-3 2-5h1c1 5-6 8 1 11v1l-5 15h-1c-1 0-2-1-3-1l-1 1c0 2 0 4-1 6h-1l-6-17 1-1c1-2 1-2 0-3 0-1 1-1 1-2l1-1s-1-1 0-2c0-1 1-1 2-2h0c0 2 0 2 1 3 3-2 4-8 5-11z" class="z"></path><path d="M576 696l1-1v2c-1 1 0 2 0 3v2h0c-3 7-5 13-9 20l3 2c-2 1-3 2-4 3-2-1-3-2-5-3-1-1-2-2-2-3l-2-1c-1 0-1-1-2-1 1-1 2-1 3-2s0-4 0-5h1l-2-4c1-1 1-1 0-2v-1c1-1 2-1 3-2 4-1 8-3 11-3l2-3h1l1-1z" class="W"></path><path d="M560 712l1 4c2-1 3-3 4-4 1-2 2-6 4-7 0 2-1 4-2 5-1 2-5 7-5 8 1 1 2 2 2 3 1 1 1 2 2 2s2-1 2-1l3 2c-2 1-3 2-4 3-2-1-3-2-5-3-1-1-2-2-2-3l-2-1c-1 0-1-1-2-1 1-1 2-1 3-2s0-4 0-5h1z" class="J"></path><path d="M560 721c2 0 4 4 5 3l1-1c1 0 2-1 2-1l3 2c-2 1-3 2-4 3-2-1-3-2-5-3-1-1-2-2-2-3z" class="i"></path><path d="M576 696l1-1v2c-1 1 0 2 0 3v2h0c-3 7-5 13-9 20 0 0-1 1-2 1s-1-1-2-2c0-1-1-2-2-3 3-2 8-9 8-13 0-1 0-1 1-2 0-1 1-2 1-3l2-3h1l1-1z" class="W"></path><path d="M572 700l2-3h1c-2 5-7 20-11 24 0-1-1-2-2-3 3-2 8-9 8-13 0-1 0-1 1-2 0-1 1-2 1-3z" class="k"></path><path d="M539 781l-1-1c-3-4-3-7-5-12 0-3-1-5-1-8 1-1 1-2 2-3 5 0 6 1 10 3 3 0 5 2 7 4s2 4 2 6c-1 3-1 5-3 8-1 2-2 3-2 6l-1 1c0-1-1-1-2-1l-5-2h0c1 1 0 1 1 2h-1l-1-3z" class="J"></path><path d="M541 766l3-1 2 1c0 2 1 4 1 6h-1c-1 0-1 0-2-1v-1h-1l-1 1c-1-2-1-3-1-5z" class="o"></path><path d="M540 776c-2-1-2-1-2-2-1-2-1-5-1-6 1-2 1-3 1-4 1-1 0-2 1-4 3 0 4 2 6 4h1v2l-2-1-3 1c0 2 0 3 1 5v3l-1 2h-1z" class="U"></path><path d="M542 774h-1v1c-1-1-1-2-2-3 0-3 0-4 2-6 0 2 0 3 1 5v3z" class="T"></path><path d="M544 760c3 0 5 2 7 4s2 4 2 6c-1 3-1 5-3 8-1 2-2 3-2 6l-1 1c0-1-1-1-2-1l-5-2c-1-2-1-4 0-6h1l2 1c2 0 4 0 6-1 1-3 1-6 1-8-1-1-1-2-2-3 0-2-3-4-4-5z" class="R"></path><path d="M540 776h1l2 1c1 2 2 2 5 2 1 0 2-1 2-1-1 2-2 3-2 6l-1 1c0-1-1-1-2-1l-5-2c-1-2-1-4 0-6z" class="C"></path><defs><linearGradient id="BD" x1="567.725" y1="765.16" x2="561.159" y2="761.551" xlink:href="#B"><stop offset="0" stop-color="#4d5054"></stop><stop offset="1" stop-color="#66666b"></stop></linearGradient></defs><path fill="url(#BD)" d="M571 744c1-1 2-2 2-3 0 1 1 2 0 3v2c1-1 1-2 2-2v1l1 1-1 1c0 1 1 2 1 3 1 3 2 3 4 5-1 3-3 10-7 12h-1c-1 1-2 2-3 2l-4-2c0 5-1 10-2 14h-2v-2c-1 0-1 0-1 1-2 4-3 7-4 11l-1-2c-1-2-2-2-4-3 6-7 9-18 13-26l7-16z"></path><path d="M573 746c1-1 1-2 2-2v1l1 1-1 1c0 1 1 2 1 3 1 3 2 3 4 5-1 3-3 10-7 12h-1c-1 1-2 2-3 2l-4-2v-2c0-1 0-2 1-3l7-16z" class="b"></path><path d="M573 746c1-1 1-2 2-2v1c-3 4-4 9-4 14l1 2h-3c-1-1-2 0-3 1l7-16z" class="D"></path><path d="M566 762c1-1 2-2 3-1h3c1 2 1 4 0 6-1 1-2 2-3 2l-4-2v-2c0-1 0-2 1-3z" class="W"></path><path d="M499 790h0 0c-1-2-1-4-1-5v1h2 1l3-1c1-1 1-1 2-1l-1-1h5c-1 1-1 1-1 2l1-1c1 0 3 0 4-1 1 0 9-1 9-1v1c0 2 1 4 2 5 0-2 1-2 2-4 1 0 2 0 3-1h0 1c0 2-1 4-2 5v1l-3 6-3 6h0c-2 2-2 4-3 7-1-3 0-6-1-8s-4-2-6-3h-1-1l1 2c-2 2-3 1-5 2v1c-1 0-1 1-1 0l-1 1h-1v-1l-1-5c-1-1-1-2-2-3 0-2-1-3-2-4z" class="a"></path><path d="M499 790c0-1-1-2 0-3 2-1 5-2 7-2v1c-2 4-2 7-3 11-1-1-1-2-2-3 0-2-1-3-2-4z" class="S"></path><path d="M503 797c1-4 1-7 3-11v5l1 1v-2c0 1 0 2 1 3l3 4 1 2c-2 2-3 1-5 2v1c-1 0-1 1-1 0l-1 1h-1v-1l-1-5z" class="R"></path><path d="M508 788c0-1 1-2 2-3v1c3 1 6-1 9-1v1h1c1 2 2 3 3 5 0 1 0 1 1 1 1-1 2-2 4-3 0-1 0-1 1-2v1 1l-3 6-3 6h0c-2 2-2 4-3 7-1-3 0-6-1-8s-4-2-6-3h-1-1l-3-4c-1-1-1-2-1-3l1-2z" class="N"></path><path d="M508 788c0-1 1-2 2-3v1c3 1 6-1 9-1v1h1c1 2 2 3 3 5 0 1 0 1 1 1l-1 1-3-3h-1 0v1h-1v-1c-1 0-2 0-3-1s-2-1-4-1c0 0-1 0-1-1l-2 1z" class="D"></path><path d="M528 789c0-1 0-1 1-2v1 1l-3 6-3 6h0c-2 2-2 4-3 7-1-3 0-6-1-8s-4-2-6-3h-1-1l-3-4c1-1 2-1 3 0 3 1 6 1 9 2 1 1 1 2 2 4h0 1c1-2 4-7 5-10z" class="M"></path><path d="M547 668l4-1v-1c0-1 1-2 2-3h0 2c1-2 2-2 3-4h0l1-1c0 1 1 1 2 2 1 0 3 1 5 1h1-2v1h-2l-1 1c-3 1-5 3-6 6-5 9-8 20-11 30l-19 52v-1c-7-3 0-6-1-11h-1c-2 2-2 3-2 5v1l-1-1v-2l-1-3c-1-1-1-2-1-3-1 3-2 9-5 11-1-1-1-1-1-3v-1c1-1 1-1 1-2 0-4 2-9 3-13v-1 1c-1 0-1 1-2 2 0 1 1 0 0 1v-5h0 0l-1 1h0l-2-7h1c3-3 4-8 5-12l2-3 3-8h0l1-3 2-2v-3l-1-1 1-1c0 1 1 1 2 1l2-1v-1c1 0 1-1 2-1l-1-1v-1l1-1 1 1c1-2 1-2 1-4-1-1-1-1-1-2h1c1-2 2-3 4-4h0c1 0 2 0 3-1l2 2 3-6h1z" class="AI"></path><path d="M542 696h0c1 2 2 2 1 4h0l-2-2 1-2z" class="y"></path><path d="M554 663l1 1v1c0 2 0 2-2 3v1 1l-2 1 1 1-1 1h-3l-1 1h-2c1-1 1-1 2-1v-1c1-2 2-2 3-2v-1-1l1 1h1l-1-1c1-1 2-1 2-1l1-1v-3z" class="u"></path><path d="M538 673c1 0 2 0 3-1l2 2-9 20c-5 11-9 21-13 33 0 2-2 6-2 9-1 3-2 9-5 11-1-1-1-1-1-3v-1c1-1 1-1 1-2 0-4 2-9 3-13v-1 1c-1 0-1 1-2 2 0 1 1 0 0 1v-5h0 0l-1 1h0l-2-7h1c3-3 4-8 5-12l2-3 3-8h0l1-3 2-2v-3l-1-1 1-1c0 1 1 1 2 1l2-1v-1c1 0 1-1 2-1l-1-1v-1l1-1 1 1c1-2 1-2 1-4-1-1-1-1-1-2h1c1-2 2-3 4-4h0z" class="I"></path><path d="M533 677h1c1-2 2-3 4-4h0c1 2 0 3 0 5 0 4-4 9-5 12-1 0-1 0-2-1-1 0-1-1-1-2v-1c1 0 1-1 2-1l-1-1v-1l1-1 1 1c1-2 1-2 1-4-1-1-1-1-1-2z" class="x"></path><path d="M527 693l1-2c2 0 2 0 3 1-2 6-5 13-8 19v-2l1-1v-1c1-1 0-2 0-3h0c-1 0-1 1-2 1v1h-1l-1-1 3-8h0l1-3 2-2 1 1z" class="AH"></path><path d="M526 692l1 1c-1 4-3 9-5 13h-1l-1-1 3-8h0l1-3 2-2z" class="h"></path><path d="M522 706v-1c1 0 1-1 2-1h0c0 1 1 2 0 3v1l-1 1v2l-4 11c0 2-1 4-2 6v-1 1c-1 0-1 1-2 2 0 1 1 0 0 1v-5h0 0l-1 1h0l-2-7h1c3-3 4-8 5-12l2-3 1 1h1z" class="t"></path><path d="M520 705l1 1h1c-2 4-4 9-6 14 0 2-1 4-1 6l-1 1h0l-2-7h1c3-3 4-8 5-12l2-3z" class="AG"></path><path d="M521 669h0v2c0 1 1 1 2 2 3 3 6 6 8 10v1l1 1c-1 0-1 1-2 1v1l-2 1c-1 0-2 0-2-1l-1 1 1 1v3l-2 2-1 3h0l-3 8-2 3c-1 4-2 9-5 12h-1l2 7h0l1-1h0 0v5c1-1 0 0 0-1 1-1 1-2 2-2v-1 1c-1 4-3 9-3 13 0 1 0 1-1 2v1h0c-1-1-2-3-2-5l-4-12-2 2c-2 1-4 2-6 2l-2-6-3-8-6-16 8-3v-2c-2-6-4-12-7-18h7 0 4c8-2 14-5 21-9z" class="j"></path><path d="M496 678h4l1 2h-3l-2-2z" class="n"></path><path d="M504 689c2 3 4 6 5 9h-1c-1 0-1 0-2 1 0-1-1-2-1-2 0-1-1-3-1-4s-1-1-1-1c1-1 1-1 1-3z" class="G"></path><path d="M514 709l1-1c1-1 0-1 0-2l1-1c0-1 1-1 1 0l1 1-2 2h1 0 1c-1 4-2 9-5 12h-1c-1-2-2-3-2-5 1 0 1-2 2-2l1 1h0c1-2 1-3 1-5z" class="w"></path><path d="M510 715c1 0 1-2 2-2l1 1h0v6h-1c-1-2-2-3-2-5z" class="k"></path><path d="M509 698c2 3 3 7 5 11 0 2 0 3-1 5h0l-1-1c-1 0-1 2-2 2v-3-1c-1-4-1-6-3-9l-1-3c1-1 1-1 2-1h1z" class="O"></path><path d="M506 699c1-1 1-1 2-1l1 2-2 2-1-3z" class="H"></path><path d="M508 687l1 1 2-3v2c-1 1-1 2-1 3h1c3-1 3-5 6-7v-1 2c-1 1-2 2-2 4l-3 6h3 0c1 1 2 1 2 2v1l1 1h0c-1 2-1 5-2 7l-9-17 1-1z" class="N"></path><path d="M515 694c1 1 2 1 2 2v1l-2 1c-1-2-1-2 0-4h0z" class="I"></path><path d="M518 681c1 0 2 1 3 1v1 1c1 1 2 1 3 2l-3 4h-3c-1 1-2 2-3 4h0-3l3-6c0-2 1-3 2-4v-2l1-1z" class="X"></path><path d="M521 684c1 1 2 1 3 2l-3 4h-3l3-6z" class="h"></path><path d="M524 686h3 0l-1 1-1 1 1 1v3l-2 2-1 3h0-1c-1 0-1 0-2 1v1l-2-1h0l-1-1v-1c0-1-1-1-2-2 1-2 2-3 3-4h3l3-4z" class="AE"></path><path d="M521 690c1 1 1 1 1 2v3c1 0 1 0 2-1h0l-1 3h0-1c-1 0-1 0-2 1v1l-2-1h0l3-8z" class="t"></path><path d="M518 690h3 0l-3 8-1-1v-1c0-1-1-1-2-2 1-2 2-3 3-4z" class="F"></path><path d="M521 669h0v2c0 1 1 1 2 2 3 3 6 6 8 10v1l1 1c-1 0-1 1-2 1v1l-2 1c-1 0-2 0-2-1l1-1h0-3c-1-1-2-1-3-2v-1-1c-1 0-2-1-3-1l-1 1v1c-3 2-3 6-6 7h-1c0-1 0-2 1-3v-2l-2 3-1-1-1 1c-1-3-3-6-6-8l-1-2c8-2 14-5 21-9z" class="C"></path><path d="M521 682l1-1h-2v-1l3-4h1l1 1-1 1c1 0 2 0 3 1s2 3 3 5h1 0l1 1c-1 0-1 1-2 1v1l-2 1c-1 0-2 0-2-1l1-1h0-3c-1-1-2-1-3-2v-1-1z" class="e"></path><path d="M521 683c2-1 3-3 5-4 0 1-1 1-2 2 0 1 1 2 1 2h1v1c2 1 3 2 4 2v1l-2 1c-1 0-2 0-2-1l1-1h0-3c-1-1-2-1-3-2v-1z" class="E"></path><path d="M508 687c0-1 0-1-1-2 0-1-1-1-1-2h1l3-3 1-2h1v2c1-1 1-2 2-2 1-2 2-3 3-5v1h1c0-1 0-1 1-2h1v3c-1 1-1 1-2 1h-1c-1 0-1 0-1 1h3l1-1v-1l1-1v2c-1 2-2 4-3 5l-1 1v1c-3 2-3 6-6 7h-1c0-1 0-2 1-3v-2l-2 3-1-1z" class="t"></path><path d="M496 696c-2-6-4-12-7-18h7 0l2 2 3 4 3 5c0 2 0 2-1 3 0 0 1 0 1 1s1 3 1 4c0 0 1 1 1 2l1 3c2 3 2 5 3 9v1 3c0 2 1 3 2 5l2 7h0l1-1h0 0v5c1-1 0 0 0-1 1-1 1-2 2-2v-1 1c-1 4-3 9-3 13 0 1 0 1-1 2v1h0c-1-1-2-3-2-5l-4-12-2 2c-2 1-4 2-6 2l-2-6-3-8-6-16 8-3v-2z" class="N"></path><path d="M501 684l3 5c0 2 0 2-1 3l-3 3-1 1v1c-1-1-1-2-1-3-1-2 0-4 1-5 2-2 2-2 2-5z" class="E"></path><path d="M507 715v-1s2-1 3-2v3c0 2 1 3 2 5l2 7h0l1-1h0 0v5c1-1 0 0 0-1 1-1 1-2 2-2v-1 1c-1 4-3 9-3 13-2-2-1-6-2-9-1-5-3-11-5-17z" class="d"></path><path d="M503 692s1 0 1 1 1 3 1 4c0 0 1 1 1 2l1 3c2 3 2 5 3 9v1c-1 1-3 2-3 2v1l-5-12c-1-3-1-5-3-7l1-1 3-3z" class="X"></path><path d="M503 692s1 0 1 1 1 3 1 4c0 0 1 1 1 2l1 3c2 3 2 5 3 9-2-1-3-3-4-5s-1-4-2-5c0-1-1-1-1-2v-2c-1-1-1-2-1-3l-2 1 3-3z" class="t"></path><path d="M496 696l10 27 1 4-2 2c-2 1-4 2-6 2l-2-6-3-8-6-16 8-3v-2z" class="U"></path><path d="M506 723l1 4-2 2c-1-2-1-3-1-6h2z" class="e"></path><path d="M488 701l8-3-1 1c-1 4-1 5 0 9 1 1 2 3 2 5v1c0 2 1 6 2 8h-1c-1-2-2-3-2-5h-1-1l-6-16z" class="w"></path><defs><linearGradient id="BE" x1="493.583" y1="709.787" x2="504.305" y2="711.136" xlink:href="#B"><stop offset="0" stop-color="#f1ead0"></stop><stop offset="1" stop-color="#e4e0e3"></stop></linearGradient></defs><path fill="url(#BE)" d="M496 696l10 27h-2-3c-2-2-3-7-4-10 0-2-1-4-2-5-1-4-1-5 0-9l1-1v-2z"></path><path d="M462 665h1l1 2c1 0 2 1 4 2 1 1 5 2 7 3 0 0 1 0 2-1 5 10 8 21 11 30l6 16 3 8v1 2l-1 1c0-1-1-1-1 0l-1 1-3 3c-1 0-3-2-3-2-1 0-2 1-3 1 0 0-1 0-2-1 0-1 0-1 1-2l-1-1c0 1 0 1-1 1h-1c0-1 1-1 0-2s-2-1-3-2c-1 0-2 0-3-1l-2-1-1-1c-1 0-3 1-4 2 0 1 0 1-1 1l-1-1c-1 0-2-1-3-1h-1c-2-2-3-4-4-7h-2c-1 0-1-1-1-2-1 2-2 3-2 4l-3-5v-1c-1-2-1-6-1-9l-2-8-1-1c0-3-1-7-1-10 1-1 1-2 2-3l2-1 2 2 1-4 2-7 3-5 1 2 2-2h1l1-1z" class="Z"></path><path d="M474 693c0 1 1 2 2 3h1v2h1c1 2 2 5 3 6s4 1 5 2v1c-2 0-4 0-6 1l-1-1c-1 0-2 0-3-1l-1-2v-1h-1-1l2-3c0-1 1-2 1-4l-1 1-1-1v-3z" class="I"></path><path d="M476 706l-1-2v-1h-1-1l2-3v2l2-1c0 1 1 3 2 5h7v1c-2 0-4 0-6 1l-1-1c-1 0-2 0-3-1z" class="h"></path><path d="M489 720c1 0 1 0 2 2v1h-2-2c-1-1-1-1-2-1h-2v1h-1l-2 1h0 1 4c-1 1 0 1-1 1h-3l1 3c1-1 0 0 2-1l1 1v2c1 1 1 1 2 1l1-1h6l-3 3c-1 0-3-2-3-2-1 0-2 1-3 1 0 0-1 0-2-1 0-1 0-1 1-2l-1-1c0 1 0 1-1 1h-1c0-1 1-1 0-2s-2-1-3-2c-1 0-2 0-3-1l-2-1-1-1 1-1c4 0 8 1 12 0 2 0 2 1 4-1z" class="I"></path><path d="M469 717h1 1 1 0 1c2 1 6 1 9 1 2 1 4 1 7 2-2 2-2 1-4 1-4 1-8 0-12 0l-1 1c-1 0-3 1-4 2 0 1 0 1-1 1l-1-1c1-1 0-3 0-5l3-2z" class="D"></path><path d="M469 717l1 1-1 2h0l1-2c2 1 3 0 5 0 0 1-1 2-2 3l-1 1c-1 0-3 1-4 2 0 1 0 1-1 1l-1-1c1-1 0-3 0-5l3-2z" class="O"></path><path d="M466 705v-1l1 1c1 1 2 1 3 2 2 1 2 1 3 1v-1-1h3c1 1 2 1 3 1l1 1h-2l-3 1-1 1h1 2v2l-1 1 6 5c-3 0-7 0-9-1h-1 0-1-1-1l-3 2c-1-2-3-3-4-7l1 1c0 1 1 1 2 2h2c2 0 4-3 6-5l-2-1c-2-1-3-2-5-4h0z" class="Q"></path><path d="M473 706h3c1 1 2 1 3 1l1 1h-2c-2 0-3 0-5-1v-1z" class="R"></path><path d="M469 688l1-1c2 1 3 4 4 6v3l1 1 1-1c0 2-1 3-1 4l-2 3h1 1v1l1 2h-3c0-1-1-2-1-3v-1l-1-1-1 1-3-3c0-2-1-4-1-6s1-4 3-5z" class="N"></path><path d="M469 688l1-1c2 1 3 4 4 6v3l1 1c-1 0 0 0-2 1 0-1 0-2-1-2-1-2-2-2-2-4l-1-4z" class="Q"></path><path d="M469 688l1 4c0 3 0 6 1 9l-1 1-3-3c0-2-1-4-1-6s1-4 3-5z" class="O"></path><path d="M452 693c0-1 0-1 1-2 0 1 0 2 1 2h1c0 2 1 3 1 5h2l2 2c2 2 4 4 6 5h0c2 2 3 3 5 4l2 1c-2 2-4 5-6 5h-2c-1-1-2-1-2-2l-1-1c1 4 3 5 4 7 0 2 1 4 0 5-1 0-2-1-3-1h-1c-2-2-3-4-4-7h-2c-1 0-1-1-1-2-1 2-2 3-2 4l-3-5v-1c-1-2-1-6-1-9l-2-8c1 0 1 0 2 2l2 1 1 1v-1-5z"></path><path d="M452 693c0-1 0-1 1-2 0 1 0 2 1 2h1c0 2 1 3 1 5l2 5-1 1c0-2-1-3-1-4l-1-1v-2l-1 1 1 2c0 1 0 2-1 4h0l-2-6v-5z" class="c"></path><path d="M456 709h2c0 1 0 2 1 3l1 1v-1c0-1 0-2-1-3v-1-1c2 2 3 4 4 6l-1-1c1 4 3 5 4 7 0 2 1 4 0 5-1 0-2-1-3-1l-1-2c-3-4-4-8-6-12z" class="W"></path><path d="M447 695c1 0 1 0 2 2l2 1 1 1v-1l2 6 2 5h0c2 4 3 8 6 12l1 2h-1c-2-2-3-4-4-7h-2c-1 0-1-1-1-2-1 2-2 3-2 4l-3-5v-1c-1-2-1-6-1-9l-2-8z" class="e"></path><path d="M449 703l6 11c-1 2-2 3-2 4l-3-5v-1c-1-2-1-6-1-9z" class="R"></path><path d="M462 665h1l1 2c-2 2-4 4-5 7 0 1-1 2-2 3h3c1 1 1 3 1 5l3 8c0 2 1 2 2 3 0 2 1 4 1 6l3 3 1-1 1 1v1c0 1 1 2 1 3v1 1c-1 0-1 0-3-1-1-1-2-1-3-2l-1-1v1c-2-1-4-3-6-5l-2-2h-2c0-2-1-3-1-5h-1c-1 0-1-1-1-2-1 1-1 1-1 2v5 1l-1-1-2-1c-1-2-1-2-2-2l-1-1c0-3-1-7-1-10 1-1 1-2 2-3l2-1 2 2 1-4 2-7 3-5 1 2 2-2h1l1-1z" class="F"></path><path d="M458 668l2-2h1c-1 2-1 3-3 4v-2z" class="I"></path><path d="M451 682l1-4c1 2 1 3 1 6s0 6 2 9h-1c-1 0-1-1-1-2-1 1-1 1-1 2-1-4-1-8-1-11z" class="B"></path><path d="M458 698l1-1h0c1 1 2 1 4 3 1 1 0 0 1 0l1 1c1 1 2 1 3 1 0-1 0-2-1-2v-1l3 3 1-1 1 1v1c0 1 1 2 1 3v1 1c-1 0-1 0-3-1-1-1-2-1-3-2l-1-1v1c-2-1-4-3-6-5l-2-2z" class="C"></path><path d="M449 680l2 2c0 3 0 7 1 11v5 1l-1-1-2-1c-1-2-1-2-2-2l-1-1c0-3-1-7-1-10 1-1 1-2 2-3l2-1z" class="u"></path><path d="M457 677h3c1 1 1 3 1 5l3 8c0 2 1 2 2 3 0 2 1 4 1 6v1c-1-1-3-1-4-2l-1-1c-2-1-5-2-6-5 0-2-1-5-2-7v-1c0-3 1-5 3-7z"></path><path d="M329 315c2 2 3 2 6 2 0 0 1 1 2 1v1l4 7 76 189c3 8 24 62 28 64l-3 1 3 5 11 25 2-1c1 2 1 3 2 4v1l3 4 6 7c1 1 1 2 2 2l2 4v1l4 12c-7 0-15 1-20 6-1 1-2 3-3 5-2 4-2 7-3 10 1 0 3-4 4-5 0 2-1 2-1 4l-1 1 1 2v1 3l-2 7-1 4-2-2-2 1c-1 1-1 2-2 3 0 3 1 7 1 10l1 1 2 8c0 3 0 7 1 9v1l-10-21v-3-1c-1-2-1-5-2-7s-2-4-3-5h0c-1-1-2-3-3-4-2-2-2-4-3-7l-5-13v-1c0-1-1-2-1-2l-1-1 1-1v-1c-1-1-1-3-2-4l-1-4v-1l-2-2-1-2-1-3-3-11-14-34c0-1-2-5-2-6-1-3-3-7-4-10-2-4-3-9-6-13-1-4-3-9-4-13-3-6-4-12-7-17h-1c-1-2-2-5-4-7l-4-11c-1-4-4-9-6-13-1-2-2-2-2-4-2-3-2-6-4-9l-1-1v-2l1-1c0-4-3-8-4-12-1-2-1-2 0-3l-3-9-3-4c0-1 0-2-1-3l-3-8h0l-1-4-2-4-2-5-1-3v-2-1c0-1 0-1 1-2l-1-1v-3h0c1-2 2-3 3-5l1-2c-4-3-8-5-12-7l-1 1-14-30 1-2 1 1c8 0 14 1 22 4 1 0 0 0 1 1 1 0 2-1 3-1v-1h0c1-1 1-4 1-6 0 2 1 3 1 4h0 1l1-9-1-3c-1-4-2-6-5-9h0c0-1 0-2-1-3l-1-1-1-1c-2-5-5-8-8-12-1-1-1-2 0-3v-3l-1 1-2-1h1c0-1 1-3 2-4h0c1-2 1-4 0-6v-1c1-1 1-1 2-1z" class="W"></path><path d="M361 465c-1 2-1 2-3 3h-1c0-2 1-3 3-4 0 1 0 1 1 1z" class="C"></path><path d="M406 516c0-2 1-3 3-5v1h1l-3 6-1-2z" class="E"></path><path d="M406 516l1 2-3 8v-7l2-3z" class="Q"></path><path d="M336 418v5c1 1 2 2 2 3 1 2 0 4 0 5l-2-5-1-3v-2-1c0-1 0-1 1-2h0z" class="E"></path><path d="M404 519v7c-1 1-1 2-1 3-1-1-2-3-2-4l2-2v-3l1-1z" class="D"></path><path d="M439 676l-1-2c-2-4-1-7 1-12v14z" class="H"></path><path d="M412 548c1-1 1-1 1-2h1c1 3 2 7 3 10l-1 1c-2-3-4-6-4-9z" class="a"></path><path d="M431 598c1 1 6 1 7 2 2 1 3 3 3 5-1 0 0 0-1-1-1 0-2-1-3-1s-2 0-2-1l-4-4z" class="I"></path><path d="M419 583h10c2 0 5-1 8 0v1c-3 1-6 0-9 0-2 0-5 1-7 1s-2-1-2-2z"></path><path d="M340 435c1 1 2 1 3 3v3h2l1 1c-1 1-1 3-1 5 0 1 1 0 1 1s0 2 1 3c0 1-1-1 0 1 1 1 1 2 1 2l-3-4c0-1 0-2-1-3l-3-8h0l-1-4z" class="E"></path><path d="M448 631l2 1 1 1-1 1h-1-1c-2 0-2 2-4 2-1 0-2 0-3-1v-2c2-1 4-2 7-2z" class="G"></path><path d="M329 315c2 2 3 2 6 2 0 0 1 1 2 1l-5 2c-2 0-3 1-5 3h0c1-2 1-4 0-6v-1c1-1 1-1 2-1z" class="X"></path><path d="M338 409l2 2v2 1l-4 4h0l-1-1v-3h0c1-2 2-3 3-5z" class="AC"></path><path d="M403 520v3l-2 2c-3-2-3-2-6-2-1 1-2 1-3 2v-1c-1-1-2-1-2-1l3-2c3 0 6-1 10-1z" class="B"></path><path d="M365 442l-1-3c1-2 4-3 5-5 1 2 0 4 0 6s1 4 1 6h-2c-2-1-2-4-2-5l-1 1z" class="a"></path><path d="M365 491c1-1 3-3 4-5l1-1c1-2 4-4 6-5l3 1h-1v1c-1 0-1 0-1 1h0l-8 4c1 1 2 1 3 1-1 2-3 3-5 5l-2-2z" class="G"></path><path d="M424 651c0-1-1-2-1-2l-1-1 1-1c1 2 2 3 2 5 2 5 4 10 6 14 2 3 3 7 4 10h0c-1-1-2-3-3-4-2-2-2-4-3-7l-5-13v-1z" class="V"></path><path d="M334 338l-1-1-3-8c-1-2 0-3 0-4h1c3 2 5 10 6 14-1 0-2 0-3-1z" class="c"></path><path d="M401 513c2-2 3-5 5-6 0 2-3 6-5 8-1 2-6 4-9 5-1 1-3 2-4 3-1-1-1-3-2-4v-1c2 0 4-1 6-1l1 1c1 0 2-1 4-1 2-1 3-3 4-4z" class="D"></path><path d="M369 416c2-1 2-1 3-1 1 1 1 3 1 4-2 2-4 3-7 4h-3c-1 1-1 1-2 1l-1-1 1-1 1-1h0c2-1 6-3 7-5z" class="a"></path><path d="M436 588c0-2 2-3 2-4 1-1 1-3 1-4l2 1c1 2 1 5 2 9-1 0-1 1-2 2h0c-2-1-3-2-3-3-1-1-1-1-2-1z" class="P"></path><path d="M410 515h0c2 1 2 4 3 5 1 3 2 7 3 10h-1c-2-2-5-4-6-7s0-6 1-8z" class="Z"></path><path d="M345 441c2-2 5-4 7-5 7-5 14-9 21-11h1l-1 1c-2 0-3 2-4 2-8 5-16 8-23 14l-1-1z" class="D"></path><path d="M377 483c4-1 8-2 12 0l2 1v1l1 1c-4-1-7-1-10-1s-6 1-8 2l-2 1c-1 0-2 0-3-1l8-4z" class="B"></path><path d="M339 407c3 3 7 6 8 10l3 6-1 2v-2l-1-1c0 1-1 1-2 2l-1-1v-3c-1-1-1-1-1-2s-1-2-1-3v-2c-1 0-2 0-3 1v-1-2l-2-2 1-2z" class="I"></path><path d="M340 374c2 1 4 3 5 4l3 3 3 5c1 3 3 7 3 10-3-5-6-11-10-15-3-3-6-5-8-7 1 0 0 0 1 1 1 0 2-1 3-1z" class="M"></path><path d="M360 477c2-1 3-2 4-2l1 1h4l1-1h1c-2 2-4 4-4 6h2l-3 3c0-1 1-1 1-2l-1-1v-1h0v-1l-3 3-2 2-1 2-2-5v-1l1-2 1-1z" class="I"></path><path d="M359 478l1-1 1 2-1 1-2 1v-1l1-2z" class="Z"></path><path d="M360 477c2-1 3-2 4-2l1 1h4c-2 1-3 2-4 3-2 0-3 0-4 1v-1l-1-2z" class="D"></path><path d="M365 442l1-1c0 1 0 4 2 5h2c1 4-1 7-3 11l-6 8c-1 0-1 0-1-1l5-10c3-5 1-7 0-12z" class="G"></path><path d="M334 338c1 1 2 1 3 1 3 6 6 10 8 16h0-1 0c0 2 0 3-1 4-1-4-2-6-5-9h0c0-1 0-2-1-3l-1-1-1-1c0-3-1-5-1-7z" class="H"></path><path d="M354 471c1 0 3-1 4-1 3 1 4 1 7 0h0l-4-3h1 1c3 3 5 3 9 3-2 1-4 1-5 2l-2 2-1 1c-1 0-2 1-4 2l-1 1-1 2-4-9z" class="I"></path><path d="M359 478c-1-2-1-3-1-4h-1l1-1c2 0 5-1 7 1l-1 1c-1 0-2 1-4 2l-1 1z" class="W"></path><path d="M423 646c1 0 2-1 3 0 2 1 4 2 5 3 2 0 3-1 4-2l2 1v3c0 3-1 3-2 4 0 1-1 2-1 2h0c-1-1-1-1-1-2h-1c-1 0-2-1-3-1-2 0-1 0-2-1l1-1c0-2 0-4-1-5h-1v2c0 1 0 2-1 3 0-2-1-3-2-5v-1z" class="C"></path><path d="M435 655c-1-2 0-3 0-5l1-1c0 2 0 2 1 2 0 3-1 3-2 4z" class="E"></path><path d="M425 602c0-1-1-3-2-4 1-1 2-1 3-1 3 1 6 8 8 11 3 3 6 5 9 8h0v1c-3-1-6-3-8-4-4-3-7-7-10-11z" class="Q"></path><path d="M436 588c1 0 1 0 2 1 0 1 1 2 3 3h0c1-1 1-2 2-2 2 3 7 13 6 16-1 0-2 1-3 2h-1v-1c0-4-2-8-5-12-2-2-4-3-5-5l1-2z" class="Y"></path><path d="M382 478l1-1h0l-2-1 1-1c1 0 1 0 2 1 2 1 4 2 6 4 2 1 4 2 6 4 1 1 2 2 3 4h0-1-1c-1 0-4-1-5-2h0l-1-1v-1l-2-1c-4-2-8-1-12 0h0c0-1 0-1 1-1v-1h1l-3-1c2-1 3-2 6-2z" class="a"></path><path d="M382 478c1 0 2 1 4 1v1c-2 1-6 1-8 2v-1h1l-3-1c2-1 3-2 6-2zm58 146c-2 0-4-1-6-1l-6-3-1-1c-1-1-2-1-2-2h-1c-2-2-3-4-3-6v-1l-1-1v-3c-1-1-1-1-1-2 0 0 0-1-1-1 0-1-1-2-1-3 0-2-1-3-3-4h-1l1-1s1 0 2 1c2 1 3 2 3 5l3 8c1 1 0 1 1 2 1 0 2 1 2 1 2 3 5 5 8 7 2 1 4 2 7 3 0 1 2 1 2 2h0c-1 1-1 1-2 0h0z" class="Q"></path><path d="M386 519c1 1 1 3 2 4 1-1 3-2 4-3l1 1-3 2s1 0 2 1v1c-2 2-4 5-6 6-1 1-1 1-2 1h0c-1 0-2 0-3-1 1-1 1-2 2-3h1c-2-1-3 0-5-1l-1-1-1-1 1-1v1c2-2 4-4 6-5l2-1z" class="a"></path><path d="M386 531v-1c2-1 2-2 3-3v-1h-1c-1 1-2 1-3 2v-1c1-2 3-3 5-4 0 0 1 0 2 1v1c-2 2-4 5-6 6z" class="Q"></path><path d="M386 519c1 1 1 3 2 4l-2 1c-3 2-4 2-7 2l-1-1c2-2 4-4 6-5l2-1z" class="D"></path><path d="M386 519c1 1 1 3 2 4l-2 1c-2-1-2-3-2-4l2-1z" class="O"></path><path d="M359 422c1 0 2 0 3-1l-1 1-1 1 1 1c1 0 1 0 2-1h3l-9 5c-4 2-8 4-12 7-1 0-1 0-1-1-1 0-2-1-3 0l-1-1c2-1 3-2 4-3l1-1c1 0 2-1 2-1l6-4h1c2-2 3-2 5-2z" class="C"></path><path d="M347 428l6-4h1c2-2 3-2 5-2-1 3-4 5-7 6l-3 2h-1l2-2h-3z" class="c"></path><path d="M351 386c0-2-1-13 1-14 1 2 0 6 1 8v6 3c0 2 1 4 2 6 0-1 0-1 1-2v-2-2h0l1-3v-4h1c1 0 1-1 1-1 2 9 0 19-1 28l-4-10v-3h0c0-3-2-7-3-10z" class="F"></path><path d="M356 389l1-3v-4h1c1 5 0 9-1 13h-1c0-1 1-3 1-4 0 0 0-2-1-2z" class="I"></path><defs><linearGradient id="BF" x1="409.565" y1="553.493" x2="425.435" y2="567.007" xlink:href="#B"><stop offset="0" stop-color="#10121a"></stop><stop offset="1" stop-color="#2f3131"></stop></linearGradient></defs><path fill="url(#BF)" d="M416 576v-1c3-8 2-16 2-25 0-3 0-5 1-8v-2 1 3c1 2 1 4 1 6l1 1c0 4 1 7 1 11 0 10-6 19-11 28 0-2 1-3 1-4l-1-1c1-3 2-6 4-9h1z"></path><path d="M415 576h1c-1 1-1 2-1 3 0 2-2 5-3 7l-1-1c1-3 2-6 4-9z" class="E"></path><path d="M345 355c2 3 3 6 3 10 1 4 2 12 0 15v1l-3-3c-1-1-3-3-5-4v-1h0c1-1 1-4 1-6 0 2 1 3 1 4h0 1l1-9-1-3c1-1 1-2 1-4h0 1 0z" class="F"></path><path d="M345 355c2 3 3 6 3 10h-1v-1-2c-1-1-1-3-2-4h0c0 4 3 11 0 15 0-4-1-7-1-11l-1-3c1-1 1-2 1-4h0 1 0z" class="K"></path><path d="M344 362c0 4 1 7 1 11v5c-1-1-3-3-5-4v-1h0c1-1 1-4 1-6 0 2 1 3 1 4h0 1l1-9z" class="E"></path><path d="M351 463l3 8 4 9v1l2 5 3 7c1 2 2 5 3 7l2 7 5 12h0l3 7h-1c-1-2-2-5-4-7l-4-11c-1-4-4-9-6-13-1-2-2-2-2-4-2-3-2-6-4-9l-1-1v-2l1-1c0-4-3-8-4-12-1-2-1-2 0-3z" class="d"></path><path d="M352 447c1-4 4-9 8-10 1 1 1 0 0 1-1 7 2 16-4 21-1 0-1 1-2 1-1-1-1-2-1-3-1-3 1-6 1-8l-1-1-2 5-1-1c0-2 0-3 2-5z" class="h"></path><path d="M352 447v1h1v-1c1-2 2-4 4-6-1 1-2 7-3 8l-1-1-2 5-1-1c0-2 0-3 2-5z" class="I"></path><defs><linearGradient id="BG" x1="453.676" y1="615.763" x2="440.516" y2="587.91" xlink:href="#B"><stop offset="0" stop-color="#0f0e0e"></stop><stop offset="1" stop-color="#2d2e30"></stop></linearGradient></defs><path fill="url(#BG)" d="M441 581l1-1 3 5 11 25 1 3c-2 2-4 3-5 6 0 0-1 1-1 2-3-5-3-10-2-15 1-3-4-13-6-16-1-4-1-7-2-9z"></path><path d="M413 619c0 1 1 1 1 2 0 2 2 3 3 4h1 3 3v1c1 0 1 1 2 2 0-1 1-1 1-1 1 0 2 0 2 1 2 1 4 0 6 0 1 0 3 0 4-1s1-1 2 0v1c0 1 0 1-1 2h-1c-1 1-1 1-1 2v1h0-1-2-2l-1 1h0c-2 1-3 1-4 2h-1-2c-2-1-3-1-5 0v1l-2-2-1-2-1-3-3-11z" class="Z"></path><path d="M413 619c0 1 1 1 1 2 0 2 2 3 3 4h1 3 3c0 1 0 2-1 3v2h-1c-2-1-1-3-3-4l-1 1c-1 1 0 3 0 4l-2-1-3-11z" class="E"></path><path d="M435 628c1 0 3 0 4-1s1-1 2 0v1c0 1 0 1-1 2h-1c-1 1-1 1-1 2v1h0-1-2-2c0-1 1-1 1-1v-1h-3c-1 0-2-1-3-1-1-1-1-1-1-2v-1c1 1 1 1 1 2h1 5l1-1z" class="I"></path><path d="M416 630l2 1c2 1 5 0 6 0l8 3c-2 1-3 1-4 2h-1-2c-2-1-3-1-5 0v1l-2-2-1-2-1-3z" class="Q"></path><path d="M432 634c2 1 3 2 5 4v1c1 1 1 2 2 3h-1v1c1 1 0 3 0 4l-1 1-2-1c-1 1-2 2-4 2-1-1-3-2-5-3-1-1-2 0-3 0-1-1-1-3-2-4l-1-4v-1-1c2-1 3-1 5 0h2 1c1-1 2-1 4-2h0z" class="C"></path><path d="M420 637v-1c2-1 3-1 5 0-1 0-1 0-3 1l1 1h2v1c-1 0-2 1-3 0l-2-1v-1zm1 5l2-1c1 1 1 1 1 2 2 0 3 1 4 1-1 0-1 0-2 2-1-1-2 0-3 0-1-1-1-3-2-4z" class="a"></path><path d="M426 646c1-2 1-2 2-2 3 0 5 1 8 1l1 1v2l-2-1c-1 1-2 2-4 2-1-1-3-2-5-3z" class="Z"></path><path d="M448 634h1 1c-1 5-4 10-4 14-2 4-3 8-3 12h0c1-1 1-1 1-2h1v2l1 1c-2 7-2 16-1 23 0 3 1 7 1 10v-1c-2-1-2-2-2-5-2-4-3-8-5-12v-14c1-8 4-20 9-28z" class="b"></path><path d="M445 660l1 1c-2 7-2 16-1 23 0 3 1 7 1 10v-1c-2-1-2-2-2-5h0v-3c-2-6-1-18 1-25z" class="C"></path><path d="M372 466l-5-2c5-1 11-2 17 1h0c6 3 11 7 14 13-2-1-3-2-4-2-7-3-13-4-19 0-2 1-5 3-6 5h-2c0-2 2-4 4-6h-1l-1 1h-4l-1-1 1-1 2-2c1-1 3-1 5-2l4-1-4-3z" class="R"></path><path d="M372 466c1-1 2-1 3-1s2 0 3 1c2 0 3 0 5 2h0c-2 0-3 0-5 1h-2l-4-3z" class="c"></path><path d="M383 468c1 1 2 1 3 2v1c-2 0-4 0-7 1s-6 1-8 3h-1l-1 1h-4l-1-1 1-1 2-2c1-1 3-1 5-2l4-1h2c2-1 3-1 5-1z" class="H"></path><path d="M383 468c1 1 2 1 3 2-3 0-5 0-8-1 2-1 3-1 5-1z" class="K"></path><path d="M451 642l1-1c1-1 1-1 1-2h1l-1 1 1 1h0c1 1 2 2 3 2v1c-1 2-4 3-5 5 2 2 2 3 2 6-2 4-2 7-3 10 1 0 3-4 4-5 0 2-1 2-1 4l-1 1 1 2v1 3l-2 7-1 4-2-2-2 1c-1 1-1 2-2 3-1-7-1-16 1-23l-1-1v-2h-1c0 1 0 1-1 2h0c0-4 1-8 3-12 2-1 1-1 2-3 0-1 2-1 3-2v-1z" class="y"></path><path d="M447 681v-1c0-2 0-3 2-5v5l-2 1z" class="AI"></path><path d="M446 648c2-1 1-1 2-3 0-1 2-1 3-2-2 6-4 12-5 18l-1-1v-2h-1c0 1 0 1-1 2h0c0-4 1-8 3-12z" class="H"></path><path d="M452 649c2 2 2 3 2 6-2 4-2 7-3 10v1l-3 6-1-1c0-3 0-5 1-8v-2-3c0-3 3-3 3-6 0-1 1-2 1-3z" class="z"></path><path d="M425 602c3 4 6 8 10 11 2 1 5 3 8 4v-1h0l1-1 1-8v1h1c1-1 2-2 3-2-1 5-1 10 2 15l3 8v2 2l-3 9v1c-1 1-3 1-3 2-1 2 0 2-2 3 0-4 3-9 4-14l1-1-1-1-2-1c-3 0-5 1-7 2 1-2 1-3 2-5l-1-2h0 0l-1-1c-2 1-7 1-9 0h-1l-6-3-5-5h0c1 0 2 1 3 2 3 1 5 3 9 5 2 1 6 0 8 0h0c1 1 1 1 2 0h0c0-1-2-1-2-2-3-1-5-2-7-3-3-2-6-4-8-7 0 0-1-1-2-1-1-1 0-1-1-2l1 1c1-1 2-6 2-8z" class="a"></path><path d="M443 626l1-2c1 1 2 2 2 3l2 4c-3 0-5 1-7 2 1-2 1-3 2-5h0v-2z" class="N"></path><path d="M443 626h2v2c0 1 0 1-1 1l-1-1v-2z" class="K"></path><defs><linearGradient id="BH" x1="444.647" y1="629.635" x2="453.353" y2="638.865" xlink:href="#B"><stop offset="0" stop-color="#353333"></stop><stop offset="1" stop-color="#464b4e"></stop></linearGradient></defs><path fill="url(#BH)" d="M445 608h1c1-1 2-2 3-2-1 5-1 10 2 15l3 8v2 2l-3 9v1c-1 1-3 1-3 2-1 2 0 2-2 3 0-4 3-9 4-14l1-1-1-1-2-1-2-4h1v-4h0c-1-1-1-3-1-5-1-1-1-2-1-3v-7z"></path><path d="M447 623c0 1 1 3 1 4s1 3 2 4v1l-2-1-2-4h1v-4z" class="C"></path><path d="M445 608h1c1-1 2-2 3-2-1 5-1 10 2 15l3 8v2 2l-5-8-3-7c-1-1-1-2-1-3v-7z" class="P"></path><path d="M445 615v-1c1-1 1-2 1-3h1v1c0 2 0 5 1 7s2 3 1 6l-3-7c-1-1-1-2-1-3z" class="U"></path><defs><linearGradient id="BI" x1="390.766" y1="542.072" x2="418.432" y2="586.812" xlink:href="#B"><stop offset="0" stop-color="#131518"></stop><stop offset="1" stop-color="#3c4147"></stop></linearGradient></defs><path fill="url(#BI)" d="M411 585c-1 3-3 6-3 8-1 2-1 2-2 3h0c-1-1-1-1-1-2s1-4 1-5c2-6 5-16 5-22-1-3-1-6-3-9h0c-2-4-5-7-9-9h0c-1-1-2-1-3-1-3-1-5 0-7 1l-1 1h-1l-2-4c3-3 7-5 11-7 3 2 6 3 8 5 1 2 2 3 3 4 1 0 1-1 2-1 0-1 1-1 1-1h1 1v2c0 3 2 6 4 9l1-1c0 5 1 10-1 15l-1 5c-2 3-3 6-4 9z"></path><defs><linearGradient id="BJ" x1="380.87" y1="514.501" x2="386.397" y2="486.568" xlink:href="#B"><stop offset="0" stop-color="#101116"></stop><stop offset="1" stop-color="#35373b"></stop></linearGradient></defs><path fill="url(#BJ)" d="M374 487c2-1 5-2 8-2h0c2 1 4 1 5 1 5 1 11 5 12 9 1 1 1 1 1 2v2c1 1 1 1 1 2v2c1 2 0 6-1 9l1 1c-1 1-2 3-4 4-2 0-3 1-4 1l-1-1c-2 0-4 1-6 1h0c1-1 1-2 2-2 3-2 5-4 6-7h0c2-3 2-6 2-9h0c-1-2-2-3-3-4-1-2-5-4-7-5-6 0-11 4-13 9-1 1-2 3-3 4h0c-1 1-1 2-2 3l-2-7c-1-2-2-5-3-7 1 0 1-1 2-2l2 2c2-2 4-3 5-5l2-1z"></path><path d="M374 487c-1 2-2 2-3 3-3 2-5 4-5 7v1 2h0c-1-2-2-5-3-7 1 0 1-1 2-2l2 2c2-2 4-3 5-5l2-1z" class="E"></path><path d="M456 610l2-1c1 2 1 3 2 4v1l3 4 6 7c1 1 1 2 2 2l2 4v1l4 12c-7 0-15 1-20 6-1 1-2 3-3 5 0-3 0-4-2-6 1-2 4-3 5-5v-1c-1 0-2-1-3-2h0l-1-1 1-1h-1c0 1 0 1-1 2l-1 1 3-9v-2-2l-3-8c0-1 1-2 1-2 1-3 3-4 5-6l-1-3z" class="AA"></path><path d="M454 629c2 2 2 3 5 3v1 1h-2 0c0 2 0 3-1 4s-2 2-2 3l-1-1 1-1h-1c0 1 0 1-1 2l-1 1 3-9v-2-2z" class="AE"></path><path d="M456 610l2-1c1 2 1 3 2 4v1l3 4c-1 1-1 2-2 4 3 6 6 11 7 17v1c-3 1-6 2-9 1-1 0-1-1-1-1 0-2 0-4 1-6v-1-1c-3 0-3-1-5-3l-3-8c0-1 1-2 1-2 1-3 3-4 5-6l-1-3z" class="m"></path><path d="M456 610l2-1c1 2 1 3 2 4v1l3 4c-1 1-1 2-2 4l-4-9-1-3z" class="w"></path><path d="M368 507c1-1 1-2 2-3h0c1-1 2-3 3-4 2-5 7-9 13-9 2 1 6 3 7 5 1 1 2 2 3 4h0c0 3 0 6-2 9h0c-1 3-3 5-6 7-1 0-1 1-2 2h0v1l-2 1c-2 1-4 3-6 5v-1h-1l-1-2h-1c0-1-1-2-2-3h0l-5-12z"></path><path d="M381 495h1c1 3 2 9 1 12v1c1 0 4-2 6-2 1 2 0 3 0 4-1 3-5 5-7 6l-4 3c-1 1-1 1-2 1-1-2-1-3-1-4 0-3 3-5 4-7 1-5-1-10 2-14z" class="G"></path><defs><linearGradient id="BK" x1="335.115" y1="407.726" x2="345.907" y2="396.094" xlink:href="#B"><stop offset="0" stop-color="#27282c"></stop><stop offset="1" stop-color="#4f5154"></stop></linearGradient></defs><path fill="url(#BK)" d="M313 369l1 1-1 1h1c1 2 2 4 2 6l2 1c5 0 12 1 16 5v1c2 1 4 2 6 4 5 4 10 10 13 16 3 5 4 10 6 15v1h1 0c3-2 6-3 9-4-1 2-5 4-7 5h0c-1 1-2 1-3 1-2 0-3 0-5 2h-1l-6 4s-1 1-2 1h0c0-1 0-1 1-2l4-2h-1l1-2-3-6c-1-4-5-7-8-10-4-3-8-5-12-7l-1 1-14-30 1-2z"></path><path d="M326 383h1l2 1c1 0 1 0 2 1 1 0 3 0 4 1l2 2-1 1c-1-1-2-2-3-2-3-1-5-2-7-4z" class="b"></path><path d="M331 395c2 0 3 1 5 2l1 1c2 1 3 3 4 4l1 1-1 1-4-2v-1l-1-1c-2-2-4-3-5-5z" class="R"></path><path d="M318 378c5 0 12 1 16 5v1h-1c1 1 2 1 2 2-1-1-3-1-4-1-1-1-1-1-2-1l-2-1h-1l-1 1c-2-1-3-1-4-2-1-2-3-2-3-4z" class="H"></path><path d="M327 400c-1-1-1-1-1-3 3 0 7 2 9 3l2 2 4 2 5 5c2 3 3 9 4 13v1l-3-6c-1-4-5-7-8-10-4-3-8-5-12-7z" class="O"></path><path d="M313 369l1 1-1 1h1c1 2 2 4 2 6l2 1c0 2 2 2 3 4v1c4 4 5 8 10 12 1 2 3 3 5 5l1 1v1l-2-2c-2-1-6-3-9-3 0 2 0 2 1 3l-1 1-14-30 1-2z" class="C"></path><path d="M261 329l7-3c2 0 5 0 7 1 0 0 2 1 2 2l22 24 2 2-2 2c1 2 3 3 4 5l1 2c1 1 3 3 4 5h1v1l2 1h1l14 30 1-1c4 2 8 4 12 7l-1 2c-1 2-2 3-3 5h0v3l1 1c-1 1-1 1-1 2v1 2l1 3 2 5 2 4 1 4h0l3 8c1 1 1 2 1 3l3 4 3 9c-1 1-1 1 0 3 1 4 4 8 4 12l-1 1v2l1 1c2 3 2 6 4 9 0 2 1 2 2 4 2 4 5 9 6 13l4 11c2 2 3 5 4 7h1c3 5 4 11 7 17 1 4 3 9 4 13 3 4 4 9 6 13 1 3 3 7 4 10 0 1 2 5 2 6l14 34 3 11 1 3 1 2 2 2v1l1 4c1 1 1 3 2 4v1l-1 1 1 1s1 1 1 2v1l5 13c1 3 1 5 3 7 1 1 2 3 3 4h0c1 1 2 3 3 5s1 5 2 7v1 3l10 21 3 5c0-1 1-2 2-4 0 1 0 2 1 2h2c1 3 2 5 4 7l3 3c-2 0-3-1-5-2h-1c-1 0-1 0-1 1-2-1-3-3-5-4v1c1 1 1 2 2 2l-1 1c1 5 4 8 5 13l3 9-1 1c2 3 3 6 4 9 1 4 3 7 4 11l3 6c1 2 3 6 3 9 1 1 1 3 3 5l1-1v2c4 5 8 11 9 17l2 5h0c3-2 5-4 7-7 1-3 1-6 2-8h1v-2h1c1 1 1 2 2 3l1 5v1h1l1-1c0 1 0 0 1 0v-1c2-1 3 0 5-2l-1-2h1 1c2 1 5 1 6 3s0 5 1 8c-1 3-1 6 0 10v3c1 3 0 7 1 9l-1 3-1 5-1 18v8l-1 8c1 2 1 3 1 5v1 4l1-1-2 8c0 1-1 2-1 2-1 0-2 0-3-1-1-2-1-2-2-3 1 3 2 4 4 6 1 0 2 2 2 3h0 2 0l-2 3h-3c-2-1-2-2-3-4 0-1-2-3-2-4l-7-13-1 2c0 1 0 3 1 5l-3-1 6 15c0 1 2 4 2 5v4 1l1 1c0 1 0 0-1 1-1-1-1-3-1-4l-2-1-187-469-3-9-6-14c-6-15-12-28-21-41-7-9-16-18-25-25l1-1h0c1-1 1 0 2-1h0l3 2c0-1 0-1-1-2l-10-8h1c-1-1-1-2-1-3l-2-1c1 0 2 1 3 1l1 1 1-1-1-1c-1 0-1 0-1-1-2 0-2 0-2-1 1-2 1-2 2-2 1 1 2 1 3 1h1l-1-1v-1z" class="w"></path><path d="M445 746c1 2 2 3 3 4 1 3 4 8 3 11h-1l-6-15h1z" class="AG"></path><path d="M356 527c2 1 2 2 3 4v1h1l2 1h0l1 1c0 2-1 4 0 6 1 3 5 9 4 13l-11-26z" class="B"></path><path d="M457 778v-3h1c2 4 3 9 5 13 0 2 0 2 1 4v2c1 1 1 2 2 3l-1 1-8-20z" class="AG"></path><path d="M428 707v-3c1 1 0 1 1 1v-3l3 6 2 4c2 4 2 7 3 12h1 1v-1-1-1h0l1 3 3 8c0 2 0 4 1 5l4 13c-1-1-2-2-3-4h-1l-16-39z" class="D"></path><path d="M437 724h1 1v-1-1-1h0l1 3 3 8c0 2 0 4 1 5l4 13c-1-1-2-2-3-4 0-1-1-3-2-4v-1c0-1-1-3-1-4v-4l-5-9z" class="H"></path><path d="M363 534l3 8c2 5 4 9 6 14l9 25c1 3 3 6 4 9l-1 2h0v2c1 3 3 6 3 9l-20-50c1-4-3-10-4-13-1-2 0-4 0-6z" class="G"></path><path d="M332 464c1 1 2 5 3 7 1 3 3 7 5 9h0c-2-8-6-15-8-23h0l11 27 13 33c2 5 5 11 6 16h0l-2-1h-1v-1c-1-2-1-3-3-4l-24-63z" class="N"></path><defs><linearGradient id="BL" x1="280.247" y1="373.317" x2="290.301" y2="368.945" xlink:href="#B"><stop offset="0" stop-color="#979797"></stop><stop offset="1" stop-color="#b5b4b6"></stop></linearGradient></defs><path fill="url(#BL)" d="M265 346l3 2a57.31 57.31 0 0 1 11 11c2 2 4 5 6 7h1c2 5 6 8 9 13s6 11 9 17l12 27c2 5 5 11 6 17-1-1-2-3-2-4s-1-1-1-2v-1c-1-2-1-4-2-5-1-2-2-4-2-6h0l-2-2v-2l-4-7-10-23-1-1c-1 3 6 16 7 18 2 4 13 29 12 31v1l-3-9-6-14c-6-15-12-28-21-41-7-9-16-18-25-25l1-1h0c1-1 1 0 2-1h0z"></path><defs><linearGradient id="BM" x1="314.127" y1="409.087" x2="307.815" y2="412.21" xlink:href="#B"><stop offset="0" stop-color="#323234"></stop><stop offset="1" stop-color="#585755"></stop></linearGradient></defs><path fill="url(#BM)" d="M285 366c0-4-5-8-8-11 3-1 5 6 8 6h0l10 12c1 2 2 3 3 4l1 1c-1 1 0 1 0 3l2-2c1 1 2 1 2 2l1 2c1 2 1 4 1 6l-1 1 3 5 20 48 1 3c2 3 3 7 4 11h0c2 8 6 15 8 23h0c-2-2-4-6-5-9-1-2-2-6-3-7-1 0-9-22-10-24-1-6-4-12-6-17l-12-27c-3-6-6-12-9-17s-7-8-9-13h-1z"></path><path d="M299 381l2-2c1 1 2 1 2 2l1 2c1 2 1 4 1 6l-1 1c-1-3-3-8-5-9z" class="P"></path><path d="M344 482l3-2c1-1 2-1 3-1h0c1 0 2 0 3 1 0 0 1 0 1 1l1 1c2 3 2 6 4 9 0 2 1 2 2 4 2 4 5 9 6 13l-1 1c-4 2-5 1-7 6v4l-2-2h-1l-13-33c2 0 1-1 1-2z" class="f"></path><path d="M344 482l3-2c1-1 2-1 3-1h0c1 0 2 0 3 1 0 0 1 0 1 1l1 1-1 1v1 1c-1 1-3 3-4 3h-2c-3-2-3-4-4-6z" class="Z"></path><path d="M350 479h1c1 1 1 2 1 3-1 2-1 2-2 3l-2-1c-1-1-1-1-1-2l3-3h0z" class="n"></path><path d="M359 515c2-5 3-4 7-6l1-1 4 11c2 2 3 5 4 7h1c3 5 4 11 7 17 1 4 3 9 4 13v1c-2-2-3-7-4-9-1 1-1 2-1 4 1 1 1 2 1 3 0 2-1 3-1 4-1 1-1 1-1 2s-1 2-2 3h0l-1 1c0 4 2 10 4 14l-1 2-9-25c-2-5-4-9-6-14l-3-8-1-1c-1-5-4-11-6-16h1l2 2v-4z" class="i"></path><path d="M379 540l-1-5h0l5 13c-1 1-1 2-1 4h-1c-1-2-2-6-2-9v-1c1-1 1-1 0-2z" class="H"></path><path d="M359 515c2-5 3-4 7-6l1-1 4 11h0l-3-3c-1-1-1-2-2-2h-4c-1 1-1 1-2 1h-1zm7 27l1-1v-3l-1-1-1-3c-1-2-1-3-1-5h-1c-1-2-2-5-3-7l3 3 1 1c1 1 0 0 0 2 1 1 2 2 2 4h0l1 4c1 1 1 2 2 4 0 1 1 2 2 3s2 3 3 4l-1-1v-1l1-1v1h2l1-2v-2h2v-1c1 1 1 1 0 2v1c0 3 1 7 2 9h1c1 1 1 2 1 3 0 2-1 3-1 4-1 1-1 1-1 2s-1 2-2 3h0l-1 1c0 4 2 10 4 14l-1 2-9-25c-2-5-4-9-6-14z" class="M"></path><path d="M379 540c1 1 1 1 0 2v1c0 3 1 7 2 9h1c1 1 1 2 1 3 0 2-1 3-1 4-1 1-1 1-1 2s-1 2-2 3h0l-1-2v-1c0-1 0-2 1-3-2-3 0-6 0-9v-4l-1-1c0 3 0 3-2 5h-1c-1 1-1 1-2 1-1-1-2-2-2-3h0c-1-1-1-2-1-3l1-1c1 1 2 3 3 4l-1-1v-1l1-1v1h2l1-2v-2h2v-1zm65 197h0c1 2 6 18 7 19l1-1v1c1 4 3 8 5 12 2 3 4 5 4 9 0 1 2 3 2 4h-1c0 2 1 3 1 4 1 1 1 1 1 2 1-1 1-2 1-3 0 2 1 4 2 7 0 2 1 3 2 5s1 3 3 5h1v-2c0-2 0-2 1-3h1 2c1 0 1 0 2-1s0-5 0-6c4 5 8 11 9 17l2 5c-1 1-1 3-2 4h-1c0 2 0 5 2 7 0 1 1 1 1 1 0 1 0 1 1 1l-1 1c3 2 2 6 4 8l-1 1c-2 1-2 1-2 3l-1 2c-1 3-2 7-2 10l1 2-1 1c0-1 0-2-1-2l-1 2-4-9c-1-4-3-8-4-11l-13-34 1-1c-1-1-1-2-2-3v-2c-1-2-1-2-1-4-2-4-3-9-5-13h-1v3l-7-17h1c1-3-2-8-3-11l-4-13z" class="b"></path><path d="M482 829c-1-2-1-4-2-6h0c3 0 7 1 10 2-2 0-3 1-4 2l-2-2-1 1 1 2v4c1 3 2 4 1 7-1-4-1-7-3-10z" class="E"></path><path d="M444 737h0c1 2 6 18 7 19l25 60h-1c-2-3-3-6-4-9l-8-19c-2-4-3-9-5-13h-1v3l-7-17h1c1-3-2-8-3-11l-4-13z" class="I"></path><path d="M479 789c4 5 8 11 9 17h-1c1 1 1 1 1 2-2-1-3-4-4-5v-1l-3 2c-2 1-4 1-6 1-1-1-2-2-2-4v-2c0-2 0-2 1-3h1 2c1 0 1 0 2-1s0-5 0-6z" class="B"></path><path d="M473 801v-2c0-2 0-2 1-3h1v3l1 1 1-1h1v1h1l1-1v1c1 1 1 3 1 4-2 1-4 1-6 1-1-1-2-2-2-4z" class="Z"></path><path d="M490 825h0c3 2 2 6 4 8l-1 1c-2 1-2 1-2 3l-1 2c-1 3-2 7-2 10l1 2-1 1c0-1 0-2-1-2v-2c-1-3-1-6-2-9 1-3 0-4-1-7v-4l-1-2 1-1 2 2c1-1 2-2 4-2z" class="I"></path><path d="M490 825h0c3 2 2 6 4 8l-1 1c-2 1-2 1-2 3l-1 2h-1c-1 0 0-1 0-1v-7c0-1-2-3-3-4 1-1 2-2 4-2z" class="W"></path><path d="M466 797c-1-1-1-2-2-3v-2c-1-2-1-2-1-4l8 19c1 3 2 6 4 9h1c2 4 3 8 5 12l1 3v-2c2 3 2 6 3 10 1 3 1 6 2 9v2l-1 2-4-9c-1-4-3-8-4-11l-13-34 1-1z" class="AC"></path><path d="M466 797c-1-1-1-2-2-3v-2c-1-2-1-2-1-4l8 19c1 3 2 6 4 9 1 2 2 4 2 6l2 4c-3-2-5-6-5-9-2-5-4-10-6-14l-2-6z" class="H"></path><path d="M476 816c2 4 3 8 5 12l1 3v-2c2 3 2 6 3 10 1 3 1 6 2 9v2l-1 2-4-9 2-1c-2-5-3-11-5-16l-2-4c0-2-1-4-2-6h1z" class="F"></path><defs><linearGradient id="BN" x1="381.556" y1="605.703" x2="404.331" y2="560.499" xlink:href="#B"><stop offset="0" stop-color="#4a4b4f"></stop><stop offset="1" stop-color="#737477"></stop></linearGradient></defs><path fill="url(#BN)" d="M383 548c1 2 2 7 4 9v-1c3 4 4 9 6 13 1 3 3 7 4 10 0 1 2 5 2 6l-1 2-1-3c-1 1 0 6-1 8 1 2 0 4 2 6 0 1 2 2 3 3s5 11 5 13h0c1 1 2 3 2 4l-1 1c-2 1-3 2-4 3l-2-1-1 1 1 3c0 2 1 5 2 7 1 1 3 6 3 7 0 0-1 1-1 2l-3-8-1-2-1 1c-1 0-1-1-2-1-1-1-2-5-3-6-3-7-6-14-8-22 0-3-2-6-3-9v-2h0l1-2c-1-3-3-6-4-9l1-2c-2-4-4-10-4-14l1-1h0c1-1 2-2 2-3s0-1 1-2c0-1 1-2 1-4 0-1 0-2-1-3 0-2 0-3 1-4z"></path><path d="M391 576l3-1c0 1 0 1 1 2 0 1 0 2 1 3l1-1c0 1 2 5 2 6l-1 2-1-3c-1 1 0 6-1 8 0-1-1-3-1-4v-1h-1c1-2 1-3 0-5l-1-1c1-1 0-2 0-3s-1-2-2-2z" class="V"></path><path d="M395 612c-1-3-3-6-3-8h1c3 4 4 11 9 13h1c1-2 2-2 3-3 1 1 2 3 2 4l-1 1c-2 1-3 2-4 3l-2-1-1 1c-3-3-3-7-5-10z" class="N"></path><path d="M402 617h1c1-2 2-2 3-3 1 1 2 3 2 4l-1 1c-2 0-4 0-5 1h-1c1-1 1-1 1-2v-1z" class="G"></path><path d="M387 578c0-1 1-2 2-3 0 0 1 0 2 1 1 0 2 1 2 2s1 2 0 3l1 1c1 2 1 3 0 5l-3-1h-4c-1-1-2-1-2-2-1-2 1-5 2-6z" class="C"></path><path d="M394 582c1 2 1 3 0 5l-3-1 3-4z" class="b"></path><path d="M387 578c0-1 1-2 2-3 0 0 1 0 2 1 1 0 2 1 2 2s1 2 0 3l-1-1c-1 2-1 2-2 3-2 0-2 0-3-1 0-2 0-2 1-4h-1z" class="S"></path><defs><linearGradient id="BO" x1="389.423" y1="612.311" x2="395.672" y2="610.344" xlink:href="#B"><stop offset="0" stop-color="#3c3838"></stop><stop offset="1" stop-color="#4f5051"></stop></linearGradient></defs><path fill="url(#BO)" d="M385 590c1 6 4 12 7 18 1 1 1 3 2 4h1c2 3 2 7 5 10l1 3c0 2 1 5 2 7 1 1 3 6 3 7 0 0-1 1-1 2l-3-8-1-2-1 1c-1 0-1-1-2-1-1-1-2-5-3-6-3-7-6-14-8-22 0-3-2-6-3-9v-2h0l1-2z"></path><path d="M440 721v-1l1-2h0c1-1 2-2 3-2 2 0 3 0 4 1v1h1l1-2 3 6c1 1 1 2 2 2l-1 1c1 5 4 8 5 13l3 9-1 1c2 3 3 6 4 9 1 4 3 7 4 11l3 6c1 2 3 6 3 9 1 1 1 3 3 5l1-1v2c0 1 1 5 0 6s-1 1-2 1h-2-1c-1 1-1 1-1 3v2h-1c-2-2-2-3-3-5s-2-3-2-5c-1-3-2-5-2-7 0 1 0 2-1 3 0-1 0-1-1-2 0-1-1-2-1-4h1c0-1-2-3-2-4 0-4-2-6-4-9-2-4-4-8-5-12v-1l-1 1c-1-1-6-17-7-19h0c-1-1-1-3-1-5l-3-8-1-3h1z" class="H"></path><path d="M463 759l1 2-1 1c-2 1-3 2-3 4v-2l-1 1v1c-1-1 0-2 0-3 1-2 2-3 4-4z" class="C"></path><path d="M460 766h3l2-2c1 1 1 2 2 3l-1 1c-1 1-3 1-5 1-2-1-2-2-2-3v-1l1-1v2z" class="W"></path><path d="M465 757c1 4 3 7 4 11l3 6c-3-2-2-5-5-7-1-1-1-2-2-3l-2 2h-3c0-2 1-3 3-4l1-1-1-2 2-2z" class="F"></path><defs><linearGradient id="BP" x1="467.833" y1="785.525" x2="477.052" y2="793.115" xlink:href="#B"><stop offset="0" stop-color="#28292d"></stop><stop offset="1" stop-color="#414245"></stop></linearGradient></defs><path fill="url(#BP)" d="M472 801c0-2 0-5-1-6v-1l-1-1v-1c-1-1-2-6-2-7 1-2 2-3 3-4l1 1c1 1 3 4 2 5v2l2 2 1 5h-2-1c-1 1-1 1-1 3v2h-1z"></path><defs><linearGradient id="BQ" x1="451.567" y1="727.184" x2="442.65" y2="748.254" xlink:href="#B"><stop offset="0" stop-color="#242427"></stop><stop offset="1" stop-color="#4c4e52"></stop></linearGradient></defs><path fill="url(#BQ)" d="M440 721v-1l1-2h0c1-1 2-2 3-2 2 0 3 0 4 1v1h1l1-2 3 6c1 1 1 2 2 2l-1 1c1 5 4 8 5 13h0v3c0 1 0 2 1 3 0 0 0 1 1 1v1 2l-1-2h0l-3 3c-1 1 1-1-1 1 0 1-1 1-1 2-2 1-3 0-3 3v1-1l-1 1c-1-1-6-17-7-19h0c-1-1-1-3-1-5l-3-8-1-3h1z"></path><path d="M440 721v-1l1-2h0c1-1 2-2 3-2 2 0 3 0 4 1v1h1l1-2 3 6c1 1 1 2 2 2l-1 1h-1v2h0c1 1 2 4 2 5h-1c-2 0-2-1-3-2h-1v1c-2-1-1-2-2-3l-2-2h0c-1-1-2-1-3-2-1 0-1-1-2-1l-1 1-1-3h1z" class="E"></path><path d="M440 721v-1l1-2h0c1-1 2-2 3-2 2 0 3 0 4 1v1c-2 1-2 2-3 4l-1 2c-1-1-3-2-4-3z" class="W"></path><path d="M261 329l7-3c2 0 5 0 7 1 0 0 2 1 2 2l22 24 2 2-2 2c1 2 3 3 4 5l1 2-1 1c2 1 3 2 3 4h0c-1 2-1 3-1 5-2 2-2 2-5 3h-1-1c-1-1-2-2-3-4l-10-12h0c-3 0-5-7-8-6 3 3 8 7 8 11-2-2-4-5-6-7a57.31 57.31 0 0 0-11-11c0-1 0-1-1-2l-10-8h1c-1-1-1-2-1-3l-2-1c1 0 2 1 3 1l1 1 1-1-1-1c-1 0-1 0-1-1-2 0-2 0-2-1 1-2 1-2 2-2 1 1 2 1 3 1h1l-1-1v-1z" class="d"></path><path d="M283 359c1-1 2-1 3-1 1 1 0 1 0 3h-1 0l-2-2z" class="L"></path><path d="M281 349h1l2 1v2c-1 1-1 1-3 1l-2-2 2-2z" class="o"></path><path d="M268 336h0c1-1 1-3 1-4h2l2 1v2c-1 0-1 0-2-1v2h-1c1 1 1 1 3 1l1-2h1v1c0 2-1 3-2 4-1 0-1 0-1-1-1 0-1 0-1 1l1 2h-1l-4-3 1-3z" class="E"></path><path d="M261 329l7-3c2 0 5 0 7 1l-2 2h-1l-1 1h-4l-1 1h2c2 0 0 0 2 1h1 0-2c0 1 0 3-1 4h0-4l-1 1-1-1-1-1v-1c1-1 1-1 1-2l-1-1h1l-1-1v-1z" class="L"></path><path d="M262 336l1-4c2 1 2 3 5 3v1h-4l-1 1-1-1z" class="o"></path><path d="M257 335c10 7 19 14 26 24l2 2c-3 0-5-7-8-6 3 3 8 7 8 11-2-2-4-5-6-7a57.31 57.31 0 0 0-11-11c0-1 0-1-1-2l-10-8h1c-1-1-1-2-1-3z" class="K"></path><path d="M295 373l1-1v-2l-1-2c0-1 0-1-1-2v-1l-3-3 1-1c4 3 4 0 7 0l1 2v-1c1 0 1 1 2 2 0-1 0-1 1-2l1 2-1 1c2 1 3 2 3 4h0c-1 2-1 3-1 5-2 2-2 2-5 3h-1-1c-1-1-2-2-3-4z" class="Y"></path><path d="M299 372c2 0 1-1 2 0l1 2-1 1h-1c-1-1-1-2-1-3z" class="R"></path><path d="M274 345l-1-1c1-1 1-2 1-3s1-1 1-2c1-1 1-4 1-5-1-1-3-2-4-3v-1c2 0 2 0 4 1h0l1-1h0c2 2 5 4 6 6l-1 1v2c2 1 2 2 3 4h0l1 2c-1 0-1-1-2-1 0 0-1 0-1 1h1 0c0 1-1 1-2 2h0l1 1v-1h3l1 1c-1 1 0 3-1 3l-1 2-1-1v-2l-2-1h-1-1c-1 0 0 0-1 1h0l-2-1c-1 0 0 0-1-1-2-1-2-1-2-3z" class="L"></path><path d="M279 345l1 1v3c-1 0 0 0-1 1h0l-2-1c0-2 0-3 2-4z" class="o"></path><path d="M283 345c-1 0-2 0-3-1 1-1 1-2 3-3l2 2h0l1 2c-1 0-1-1-2-1 0 0-1 0-1 1z" class="p"></path><path d="M278 336l1 1c0 2 0 3-1 5l-1 1c-1-1-1-1-1-3 1 0 1-1 1-2 1 0 0 0 1-1v-1z" class="j"></path><path d="M274 345c2-1 2-1 4-1l1 1c-2 1-2 2-2 4-1 0 0 0-1-1-2-1-2-1-2-3z" class="n"></path><defs><linearGradient id="BR" x1="513.133" y1="856.589" x2="497.947" y2="866.547" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2f333a"></stop></linearGradient></defs><path fill="url(#BR)" d="M494 833l1 1c1 2 2 3 4 5s4 3 6 5c6 5 7 12 7 19 0 3 0 5 2 7l1 7c1-1 1-4 2-5h0c1 2 1 3 1 5v1 4l1-1-2 8c0 1-1 2-1 2-1 0-2 0-3-1-1-2-1-2-2-3 1 3 2 4 4 6 1 0 2 2 2 3h0 2 0l-2 3h-3c-2-1-2-2-3-4 0-1-2-3-2-4l-7-13-1 2c0 1 0 3 1 5l-3-1-13-32 1-2c1 0 1 1 1 2l1-1-1-2c0-3 1-7 2-10l1-2c0-2 0-2 2-3l1-1z"></path><path d="M517 872h0c1 2 1 3 1 5v1 4l1-1-2 8c-2-2-2-6-3-8v-1c1 1 1 2 1 2 1-1 1-1 0-2l1-1c-1-1-1-1-1-2 1-1 1-4 2-5z" class="a"></path><path d="M492 839v3l1-1v2c-1 5 2 11 4 16l8 17c1 3 2 6 4 9 1 0 1 2 2 2 1 3 2 4 4 6 1 0 2 2 2 3h0 2 0l-2 3h-3c-2-1-2-2-3-4 0-1-2-3-2-4l-7-13-7-17c-1-3-3-6-3-10h-1c0-4 0-8 1-12z" class="W"></path><path d="M509 885c1 0 1 2 2 2 1 3 2 4 4 6 1 0 2 2 2 3h0-2c-3-2-5-8-6-11z" class="V"></path><path d="M490 839l1-2c0-2 0-2 2-3l-1 5c-1 4-1 8-1 12h1c0 4 2 7 3 10l7 17-1 2c0 1 0 3 1 5l-3-1-13-32 1-2c1 0 1 1 1 2l1-1-1-2c0-3 1-7 2-10z" class="R"></path><path d="M501 794c1 1 1 2 2 3l1 5v1h1l1-1c0 1 0 0 1 0v-1c2-1 3 0 5-2l-1-2h1 1c2 1 5 1 6 3s0 5 1 8c-1 3-1 6 0 10v3c1 3 0 7 1 9l-1 3-1 5-1 18v8l-1 8h0c-1 1-1 4-2 5l-1-7c-2-2-2-4-2-7 0-7-1-14-7-19-2-2-4-3-6-5s-3-3-4-5l-1-1c-2-2-1-6-4-8l1-1c-1 0-1 0-1-1 0 0-1 0-1-1-2-2-2-5-2-7h1c1-1 1-3 2-4h0c3-2 5-4 7-7 1-3 1-6 2-8h1v-2h1z" class="P"></path><path d="M502 821c2-1 2-5 3-6v4h1c2 4 1 6 1 10 0 2 1 4 1 6l2 3v4c-4-6-6-9-7-16v-1c-1-1-1-2-1-4z" class="p"></path><path d="M502 821c2-1 2-5 3-6v4l-2 7v-1c-1-1-1-2-1-4z" class="R"></path><path d="M504 803h1l1-1c0 1 0 0 1 0v-1c1 2 2 4 2 6l-3 12h-1v-4c-1 1-1 5-3 6 0-1 0-2-1-3l2-3c0-3 1-6 0-10 0-1 1-2 1-3v1z" class="F"></path><path d="M504 803h1l1-1c0 1 0 0 1 0v-1c1 2 2 4 2 6l-3 12h-1v-4c1-5 2-8-1-12z" class="G"></path><path d="M512 799v-1c3 3 1 32 1 37l-1-2c-2-4-2-13-1-18 0-1 1-3 1-5l-1-5c1-1 1-2 1-3l-1 1c-2 1-2 2-2 4 0-2-1-4-2-6 2-1 3 0 5-2z" class="L"></path><path d="M520 833l-1 5-1 18v8l-1 8h0l-1-2h0c0-1-1-2 0-3 1-8 0-18-2-26l-1-4 1-1 1 1c2-1 3-2 5-4z" class="O"></path><path d="M520 833l-1 5h-1v-1c-2 1-2 3-2 4h-1l-1-3v3l-1-4 1-1 1 1c2-1 3-2 5-4z" class="e"></path><path d="M511 797h1 1c2 1 5 1 6 3s0 5 1 8c-1 3-1 6 0 10v3c1 3 0 7 1 9l-1 3c-2 2-3 3-5 4l-1-1-1 1v-2c0-5 2-34-1-37v1l-1-2z" class="U"></path><path d="M501 794c1 1 1 2 2 3l1 5c0 1-1 2-1 3 1 4 0 7 0 10l-2 3c1 1 1 2 1 3 0 2 0 3 1 4v1c1 7 3 10 7 16 2 5 4 10 5 16h0c0 4 0 9-1 12-2-2-2-4-2-7 0-7-1-14-7-19-2-2-4-3-6-5s-3-3-4-5l-1-1c-2-2-1-6-4-8l1-1c-1 0-1 0-1-1 0 0-1 0-1-1-2-2-2-5-2-7h1c1-1 1-3 2-4h0c3-2 5-4 7-7 1-3 1-6 2-8h1v-2h1z" class="a"></path><path d="M500 812l3 3-2 3c-1 0-2 0-2-1 0-2 0-3 1-5z" class="T"></path><path d="M503 805c1 4 0 7 0 10l-3-3 3-7z" class="M"></path><path d="M497 804c1-3 1-6 2-8h1c0 2 1 5 1 7-1 6-5 11-7 17-1 1-1 2-2 1-3-2-3-3-4-6 1-1 1-3 2-4h0c3-2 5-4 7-7z" class="H"></path><path d="M304 364c1 1 3 3 4 5h1v1l2 1h1l14 30 1-1c4 2 8 4 12 7l-1 2c-1 2-2 3-3 5h0v3l1 1c-1 1-1 1-1 2v1 2l1 3 2 5 2 4 1 4h0l3 8c1 1 1 2 1 3l3 4 3 9c-1 1-1 1 0 3 1 4 4 8 4 12l-1 1v2c0-1-1-1-1-1-1-1-2-1-3-1h0c-1 0-2 0-3 1l-3 2c0 1 1 2-1 2l-11-27c-1-4-2-8-4-11l-1-3-20-48-3-5 1-1c0-2 0-4-1-6l-1-2c0-1-1-1-2-2l-2 2c0-2-1-2 0-3l-1-1h1 1c3-1 3-1 5-3 0-2 0-3 1-5h0c0-2-1-3-3-4l1-1z" class="L"></path><path d="M313 400h3v2 1c-2-1-2-1-3-3z" class="P"></path><path d="M327 443v-1c1-1 1-2 1-3l5 1v1s0 1-1 1c-1 1-2 1-3 3l-1 1-1-3z" class="l"></path><path d="M327 405c1 0 2 0 3 1s2 5 2 7l2 1h1 0v3l1 1c-1 1-1 1-1 2v1 2l-3-5-5-13z" class="C"></path><path d="M329 445c1-2 2-2 3-3-1 2-1 2-1 4 1 1 2 1 3 1h1v-1-2l2-1 2 1c0 1 0 2 1 3-1 2-2 3-4 3-2 1-3 1-4 0-2-2-2-3-3-5z" class="F"></path><path d="M327 400c4 2 8 4 12 7l-1 2c-1 2-2 3-3 5h-1l-2-1c0-2-1-6-2-7s-2-1-3-1l-1-4 1-1z" class="W"></path><path d="M332 418l3 5 1 3 2 5 2 4 1 4h0l3 8c1 1 1 2 1 3v1c0 2 3 6 1 8-1 0-1-1-1-2h-1l-1 1h0v3c0 1 0 2-1 3v-1h-1c1-1 1-2 1-3h0c1-3 0-7-1-10 0-1-1-2-1-3-1-1-1-2-1-3v-1c0-1-1-2-2-3h-1l-1-1c-1-1-2-1-4-1l-1-2c-1-1-1-2-2-3l-1-1-3-3h-1v-2l-1-1c1 0 1 0 1-1v-2c0-1 1-3 2-3 2-2 3-1 5-1 1 0 2 0 2-1z" class="X"></path><path d="M332 418l3 5 1 3c-1 0-1 0-1 1h0-1c0-1 0-2-1-3h-1l1-2c0-1-2-2-3-3 1 0 2 0 2-1z" class="T"></path><path d="M343 458v-3c1-1 1-1 0-2l-1-3c-1-3-2-8-1-11l3 8c1 1 1 2 1 3v1c0 2 3 6 1 8-1 0-1-1-1-2h-1l-1 1h0z" class="P"></path><path d="M323 425v-2c0-1 1-3 2-3 2-2 3-1 5-1 1 1 3 2 3 3l-1 2c-1 1-1 3-3 4-1 0-3 0-4-1-1 0-1-1-2-2z" class="C"></path><path d="M327 420h2c2 1 2 1 2 3l-1 1c-2 1-2 1-3 0-1-2-1-2 0-4z" class="j"></path><path d="M304 364c1 1 3 3 4 5h1v1c1 2 2 4 2 5 1 2 3 3 3 4 1 1 0 4 0 4l1 1c0 1 1 1 1 2 1 1 2 2 2 4 2 2 1 2 2 4l-1 1c0 3 0 3-2 5h-1-3l-2-2v-2h0l-1 1c-1 0-2-1-3-2l-3-5 1-1c0-2 0-4-1-6l-1-2c0-1-1-1-2-2l-2 2c0-2-1-2 0-3l-1-1h1 1c3-1 3-1 5-3 0-2 0-3 1-5h0c0-2-1-3-3-4l1-1z" class="Y"></path><path d="M311 393c0-1 0-1 1-2s2-1 3-1 2 1 3 2l1 3c0 3 0 3-2 5h-1-3l-2-2v-2h0c-1-1-1-2 0-3z" class="F"></path><path d="M311 393c1 0 2-1 4-1v4h0-1l-1-1-2 1h0c-1-1-1-2 0-3z" class="d"></path><path d="M304 364c1 1 3 3 4 5h1v1c1 2 2 4 2 5 1 2 3 3 3 4 1 1 0 4 0 4l-2-3-1 1c1 0 1 1 1 2 1 0 1 1 1 2h0-2c-1-1-2-1-3-3 1-1 1-1 1-2v-2l1-1h-1l-1 4h-2-3c0-1-1-1-2-2l-2 2c0-2-1-2 0-3l-1-1h1 1c3-1 3-1 5-3 0-2 0-3 1-5h0c0-2-1-3-3-4l1-1z" class="P"></path><path d="M306 369c2 2 2 3 2 6h-1s-1-1-2-1c0-2 0-3 1-5z" class="K"></path><path d="M308 375c1 2-1 4-2 6h-3c0-1-1-1-2-2h1c2 0 4-2 5-4h1z" class="i"></path><path d="M305 374c1 0 2 1 2 1-1 2-3 4-5 4h-1l-2 2c0-2-1-2 0-3l-1-1h1 1c3-1 3-1 5-3z" class="D"></path><path d="M328 446l1-1c1 2 1 3 3 5 1 1 2 1 4 0 2 0 3-1 4-3 0 1 1 2 1 3 1 3 2 7 1 10h0c0 1 0 2-1 3h1v1c1-1 1-2 1-3v-3h0l1-1h1c0 1 0 2 1 2 2-2-1-6-1-8v-1l3 4 3 9c-1 1-1 1 0 3 1 4 4 8 4 12l-1 1v2c0-1-1-1-1-1-1-1-2-1-3-1h0c-1 0-2 0-3 1l-3 2c0 1 1 2-1 2l-11-27c-1-4-2-8-4-11z" class="L"></path><path d="M396 592c1-2 0-7 1-8l1 3 1-2 14 34 3 11 1 3 1 2 2 2v1l1 4c1 1 1 3 2 4v1l-1 1 1 1s1 1 1 2v1l5 13c1 3 1 5 3 7 1 1 2 3 3 4h0c1 1 2 3 3 5s1 5 2 7v1 3l10 21 3 5c0-1 1-2 2-4 0 1 0 2 1 2h2c1 3 2 5 4 7l3 3c-2 0-3-1-5-2h-1c-1 0-1 0-1 1-2-1-3-3-5-4v1l-3-6-1 2h-1v-1c-1-1-2-1-4-1-1 0-2 1-3 2h0l-1 2v1h-1 0v1 1 1h-1-1c-1-5-1-8-3-12l-2-4-3-6v3c-1 0 0 0-1-1v3l-2-6-3-5-7-18c-1-3-2-7-4-10l-7-18-7-19c1 0 1 1 2 1l1-1 1 2 3 8c0-1 1-2 1-2 0-1-2-6-3-7-1-2-2-5-2-7l-1-3 1-1 2 1c1-1 2-2 4-3l1-1c0-1-1-3-2-4h0c0-2-4-12-5-13s-3-2-3-3c-2-2-1-4-2-6z" class="G"></path><path d="M417 633l1 2h0l1 4-1 1h0l-1 2v-2h-1c-1-1-2 0-3 0v-1c1-1 1-2 2-2 0-1 1-2 1-3v-1h1z" class="S"></path><path d="M407 619l1-1 1 4c-1 2-2 3-4 4 0 1-1 0-2 0l-2-1-1-3 1-1 2 1c1-1 2-2 4-3z" class="Z"></path><path d="M417 642l1-2h0l1-1-1-4h0l2 2v1l1 4c1 1 1 3 2 4v1l-1 1 1 1s1 1 1 2c-2-1-2 0-3 0-1-2 0-3-1-4s-1-1-1-2c0 0 0-1-1-2l-1-1z" class="H"></path><path d="M423 696c1-2 1-4 1-6h0 0c1 0 1 1 1 2 1 1 1 2 1 3 1 1 2 2 2 3 1 1 1 2 1 4v3c-1 0 0 0-1-1v3l-2-6-3-5z" class="F"></path><path d="M423 696c1-2 1-4 1-6h0 0c1 0 1 1 1 2l-1 1 1 2c1 0 1 1 1 1v1c0 1 1 2 1 2l-1 2-3-5z" class="AG"></path><path d="M416 678h2 0 1 0c2 3 4 7 5 11v1h0 0c0 2 0 4-1 6l-7-18z" class="AC"></path><path d="M416 665v-4c1-1 1-1 2-1 0-1 1-2 1-3l1-1c1-2 2-3 4-4l5 13-1 1c-1-2-1-3-2-5v2c-1-2-1-3-2-4-3 0-3 0-5 1-1 1-2 3-3 5z" class="D"></path><path d="M398 631c1 0 1 1 2 1l1-1 1 2 3 8h0l4 10 2 4v2l2 5h0c-1-1-1-1-1-2l-1-2h0c-1-3-3-9-5-10l-1 2-7-19z" class="N"></path><path d="M396 592c1-2 0-7 1-8l1 3 1-2 14 34 3 11 1 3h-1c0-1-1-3-2-4l-3-9-1-5-1-3c-1-1-2-2-2-3s0-2-1-3c-1 2 2 9 3 11 0 1 1 2 1 4l-1-1v2l-1-4c0-1-1-3-2-4h0c0-2-4-12-5-13s-3-2-3-3c-2-2-1-4-2-6z" class="J"></path><path d="M396 592c1-2 0-7 1-8l1 3h0l3 8c-1 1-2 1-3 3-2-2-1-4-2-6z" class="c"></path><path d="M416 665c1-2 2-4 3-5 2-1 2-1 5-1 1 1 1 2 2 4v-2c1 2 1 3 2 5l1-1c1 3 1 5 3 7 1 1 2 3 3 4h0c1 1 2 3 3 5s1 5 2 7v1 3l10 21 3 5c0-1 1-2 2-4 0 1 0 2 1 2h2c1 3 2 5 4 7l3 3c-2 0-3-1-5-2h-1c-1 0-1 0-1 1-2-1-3-3-5-4v1l-3-6-1 2h-1v-1c-1-1-2-1-4-1-1 0-2 1-3 2h0l-1 2v1h-1 0c-1-11-6-19-10-29-2-1-2-3-3-5-1-1-2-3-3-4 0-1-1-2-1-2-1-4-2-7-4-10 0-2-2-3-2-6z" class="I"></path><path d="M419 667v-2c1 0 0 0 1-1v-2h2l1 1v-1l2 2c-1 1-2 2-4 3h-2z" class="E"></path><path d="M441 697c-1 3 2 5 1 8-2 0-2 1-4 0-1-1-1-3 0-5v-5h0l1-1 1 2 1 1z" class="R"></path><path d="M426 663v-2c1 2 1 3 2 5l1-1c1 3 1 5 3 7 1 1 2 3 3 4h0c1 1 2 3 3 5s1 5 2 7v1 3l-1-2h-1c-4-6-4-13-9-19l-3-3c0 1-1 1-2 1-1-1-2-1-3-1s-1 0-2-1h0 2c2-1 3-2 4-3l1-1z" class="K"></path><path d="M421 668c2-1 3-1 4-1l1-1c0-1 0 0 1-1v1h0c0 2 1 4 2 5l-3-3c0 1-1 1-2 1-1-1-2-1-3-1z" class="R"></path><path d="M428 666l1-1c1 3 1 5 3 7 1 1 2 3 3 4h0c1 1 2 3 3 5s1 5 2 7v1 3l-1-2-2-6-1-3-4-7c-1-2-2-4-3-5l-1-3z" class="Q"></path><path d="M438 690h1l1 2 10 21 3 5c0-1 1-2 2-4 0 1 0 2 1 2h2c1 3 2 5 4 7l3 3c-2 0-3-1-5-2h-1c-1 0-1 0-1 1-2-1-3-3-5-4v1l-3-6-9-19-1-1-2-6z" class="H"></path><path d="M455 714c0 1 0 2 1 2 1 3 3 5 4 8-2-1-5-4-7-5v-1c0-1 1-2 2-4z" class="D"></path><path d="M418 671h9c1 2 2 4 2 7l1 5c0 1 0 2 1 2l-1 1c0 1 0 2-1 3v3c-2-1-2-3-3-5-1-1-2-3-3-4 0-1-1-2-1-2-1-4-2-7-4-10z" class="R"></path><defs><linearGradient id="BS" x1="306.604" y1="240.457" x2="259.076" y2="263.479" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#26272c"></stop></linearGradient></defs><path fill="url(#BS)" d="M284 212c4-1 9 4 12 7h1l13 12h0l1-1c2 1 2 4 4 4 2 1 3 3 3 4l2 2h0l-3-10 3 3 1 1v-1l1-1 2 2-1 1h-1c0 1 1 4 2 5h0 1c1 0 2 1 3 1h1c-1-1-1-2-1-4l3-1c1 1 1 1 1 2s-1 2-1 2c1 2 2 2 3 4 4 3 7 7 9 11v1h-2l-1-1c-1 1 0 1 0 2v1 1h-2c-1 1-2 1-2 2v3c1 0 2-1 3-1h0c-1 1-1 2-1 3-1 3-5 8-5 11-1 1-2 1-2 2l-2 1 1 1v3c2 0 2 0 3 2v1h-1c-1 0-2 1-3 1l1 2c1 2 2 3 3 5 0 2-1 5-2 7-1 4-3 9-2 13-1 0-1 0-2 1v1c1 2 1 4 0 6h0c-1 1-2 3-2 4h-1l2 1 1-1v3c-1 1-1 2 0 3 3 4 6 7 8 12l1 1 1 1c1 1 1 2 1 3h0c3 3 4 5 5 9l1 3-1 9h-1 0c0-1-1-2-1-4 0 2 0 5-1 6h0v1c-1 0-2 1-3 1-1-1 0-1-1-1-8-3-14-4-22-4l-1-1-1 2h-1l-2-1v-1h-1c-1-2-3-4-4-5l-1-2c-1-2-3-3-4-5l2-2-2-2-22-24c0-1-2-2-2-2-2-1-5-1-7-1l-7 3s0-1-1 0c-2 0-5 1-7 1v-1l2-1c-1 0-2 0-3-1l-1 1-1-2c-2 0-3 0-4-1l-2-2-1 1h-1c1-1 1-2 2-3 1 0 0 1 1 0v-1l-2 1v1c-3 2-4 1-7 1l1-1c1-1 3-1 4-1h1v-1h-2-3l-5 1c-1 1-3 0-4 0l-1 1-2-1h0c-3-2-8-1-11-2h-3-2l-1-1c-1 0-1 0-2-1-2 1-2 1-4 1-3 1-6 1-9 1-4 0-8 0-12 1 2-3 4-2 7-4h-2l2-1h-1v-2c-1-1-2-1-2-3h-1c0-2 2-3 3-4 1-3 2-7 4-9l1-1 5-5-1-3h1c2 0 3-1 5 0l2-2h1c2-1 5-4 7-4 3-1 8-2 10-4l-2-2v-2c1 0 2-1 2-2-2 0-3 0-4 1l-2-2c-1-1-1-1-1-3l-6-14 2-2h1 1l2-2-1-1c-1-1-1-2-2-2l-2-4h1l-1-1v-1c-2-2-3-3-6-3l-2-1c-2-2-3-4-5-5h0l-1-4c-1-1-1-2 0-3l2 1c2 0 4 0 5-1 4 0 9 0 12 1h0c2 0 3-1 4 0l2 1c1 0 2-1 2-1h1l1-1c1 0 2 0 3-1 2-1 5-2 7-3 2 0 4-1 7 0 4 0 7 2 11 2h0 2v-1-3l1 1c1 1 1 2 2 3l1 1h1l2 1h1l1-2 1 3c1 0 0 0 1-1h3c2-2 3-5 4-7 3-3 7-4 11-6z"></path><path d="M238 225c-1 1-2 2-3 2s-2 1-2 1c-2 1-4 2-5 3-1-1-1-2-2-2v-1c2 0 3-1 4-1h1l7-2z" class="Z"></path><path d="M240 222l1 1h2v1c-1 1-3 1-5 1h0l-7 2v-2h2c3-2 4-2 7-3z" class="D"></path><path d="M282 218l2-1c1 1 2 1 4 1h1v1c-1 1-2 4-3 5h0l-3 1c-1-2 2-4 1-7h-2z" class="E"></path><path d="M282 268c1 0 1-1 3-1 3 1 4 3 8 2h1c1 0 2-1 3-1h1c0 1-1 2-1 3h-1c-1-1-2-1-3 0l-2 1c-3-1-6-3-9-4z" class="I"></path><path d="M292 237l5 13c1 2 3 4 3 6l-2 1c0-2-1-4-2-5l-1-2c-1-3-2-5-3-7s0-4 0-6z" class="C"></path><path d="M282 218h2c1 3-2 5-1 7l3-1c-2 1-4 2-5 4l-1 1v1c-1 0-2 0-4-1l1-1c1-3 2-9 5-10z" class="F"></path><path d="M312 246c1 2 3 3 6 2-3 2-3 5-3 8v4c0 1 0 1 1 2 1 4-1 7 1 10v1l-3-4v-3l-1-8c0-4-1-8-2-11l1-1z" class="T"></path><path d="M314 269l3 4s2 2 2 3v2 4 4c-1 1-2 3-2 4-1 0-2-1-3-2 1-2 2-4 1-6v-2l-1-11z" class="j"></path><path d="M235 221c3 0 3 0 5 1-3 1-4 1-7 3h-2v2h-1c-1 0-2 1-4 1v1c1 0 1 1 2 2 0 1 1 2 2 3s3 2 4 3c2 2 4 4 7 4l2 1 2 1h-4c-2-1-5-3-7-4l-13-12c1 0 2-1 2-1h1l1-1c1 0 2 0 3-1 2-1 5-2 7-3z" class="G"></path><path d="M230 234l1-1c1-1 1-1 1-2 4-3 8-3 13-3v4h0-1c-4 1-6 1-8 4 1 2 2 2 3 2h0v1c2 1 3 2 4 3l-2-1c-3 0-5-2-7-4-1-1-3-2-4-3z" class="g"></path><path d="M230 234l1-1c1-1 1-1 1-2 4-3 8-3 13-3v4h0-1c-1-1-2-1-1-3h-3l-1 1h-1c-2 1-4 3-5 4v1l1 1h0v1c-1-1-3-2-4-3z" class="b"></path><path d="M299 235c1 1 2 2 2 3 6 10 6 17 4 28l-1 1h0c-3-6-2-14-3-20-1-4-3-9-2-12z" class="V"></path><path d="M286 224h0c-1 3-2 5-2 8h0c0 6 1 9 4 14-1 0-1 0-1 1h-2c0-1-1-2-2-2l-1-1c-1 0-3-2-4-2l-1-5h0c-2-2-1-5-1-8 2 1 3 1 4 1v-1l1-1c1-2 3-3 5-4z" class="i"></path><path d="M279 235h2c2 1 1 2 2 4-1 1-1 1-2 1-1-2-2-2-2-5z" class="T"></path><path d="M286 224h0c-1 3-2 5-2 8h0v-2c-2 0-2 1-3 1v1c-1 1-1 1-1 2l-1-1c-1 1 0 2 0 2 0 3 1 3 2 5 0 2 1 2 1 3-3-1-3-4-5-6h0 0c-2-2-1-5-1-8 2 1 3 1 4 1v-1l1-1c1-2 3-3 5-4z" class="B"></path><path d="M297 219l13 12h0c3 4 7 11 9 17h-1 0c-3 1-5 0-6-2l-1 1c-2-2-3-5-4-6l-5-10c-3-3-4-6-5-10l-1-1h1v-1z" class="I"></path><path d="M297 221c2 0 3 2 4 3 2 2 3 4 4 6h-3v1c-3-3-4-6-5-10z" class="F"></path><path d="M305 230c3 5 7 11 11 16h0c-3 0-5-3-6-5-1 0 0 0-1-1 1 2 1 4 3 6l-1 1c-2-2-3-5-4-6l-5-10v-1h3z" class="B"></path><defs><linearGradient id="BT" x1="268.329" y1="220.203" x2="253.344" y2="237.447" xlink:href="#B"><stop offset="0" stop-color="#19191b"></stop><stop offset="1" stop-color="#2f3239"></stop></linearGradient></defs><path fill="url(#BT)" d="M245 228h9c4 1 8 0 13 1h2 0c2-1 4-2 5-3l1-1 1 1v2h1l-1 1c0 3-1 6 1 8h0l1 5h0c-1-2-2-3-2-5v-1c-1-1-1-3-2-5v1c-3 0-7 1-10 1h0c-4 0-9 0-11 2v1c-2 1-5 1-7 3h-2v-1c-2 0-3 0-5 1v-1h0c-1 0-2 0-3-2 2-3 4-3 8-4h1 0v-4z"></path><path d="M244 232h1c2 1 5 1 8 1 1-1 5-1 7-1 1 1 0 1 1 0 1 0 2 1 3 1-4 0-9 0-11 2v1c-2 1-5 1-7 3h-2v-1c-2 0-3 0-5 1v-1h0c-1 0-2 0-3-2 2-3 4-3 8-4z" class="W"></path><path d="M253 233c1-1 5-1 7-1 1 1 0 1 1 0 1 0 2 1 3 1-4 0-9 0-11 2v1c-2 1-5 1-7 3h-2v-1c-2 0-3 0-5 1v-1l1-1h4 1l1-1c3 0 5-1 7-3z" class="I"></path><path d="M264 233h0c3 0 7-1 10-1v-1c1 2 1 4 2 5v1c0 2 1 3 2 5h0c1 0 3 2 4 2l1 1c1 0 2 1 2 2h-2c-1-1-1-1-2-1l-1 2-1-1h-6-2-1c-1 0-2 1-2 1h-3c-2 0-5 1-7 2-1 0-2 1-2 1l-1-1-1-1h-1l1-2c-3-1-6-2-9-4h0l-2-1c-1-1-2-2-4-3 2-1 3-1 5-1v1h2c2-2 5-2 7-3v-1c2-2 7-2 11-2z" class="S"></path><path d="M253 236c2 2 5 4 5 8h1l1-1c1 0 2 0 3 1h-2l-1 1c2 1 4 1 6 1-1 1-3 1-5 1l-6 3-1-1h-1l1-2c-3-1-6-2-9-4h0l-2-1c-1-1-2-2-4-3 2-1 3-1 5-1v1h2c2-2 5-2 7-3z" class="C"></path><path d="M253 236c2 2 5 4 5 8h0l-1-1c-1-2-3-3-4-3l-1 1-1-1-1 1-1-1c-1 1-2 1-3 2-1-1-2-1-2-3h2c2-2 5-2 7-3z" class="E"></path><path d="M319 286v-4-4-2c2 2 4 3 4 6h0l1 1 3-1v1l-2 3 2 1 2 1 1 2c1 2 2 3 3 5 0 2-1 5-2 7-1 4-3 9-2 13-1 0-1 0-2 1v1c1 2 1 4 0 6h0c-1 1-2 3-2 4v-1c0-2 0-3-1-5l-2 1-1 1c-4-7-9-14-15-20 1-3 1-5 1-8 1-1 1-2 2-3h0v1h1c3-3 3-7 5-11 1 2 0 4-1 6 1 1 2 2 3 2 0-1 1-3 2-4z" class="L"></path><path d="M325 286l2 1 2 1 1 2c-1 2 1 2 0 4 0 0-1 2-1 3-1 1-1 2-1 4-1 2-1 4-2 6s-2 9-3 11h-1c0-2-1-4-1-6 1-2 0-5 1-8 1-2 1-3 1-5h0l1-11 1-2z" class="b"></path><path d="M327 287l2 1 1 2c-1 2 1 2 0 4 0 0-1 2-1 3-1 1-1 2-1 4-1 2-1 4-2 6h-2 0v-2c1-3 1-6 2-9 1-2 1-6 1-9z" class="g"></path><path d="M315 282c1 2 0 4-1 6 1 1 2 2 3 2l-1 2v2l-2 8h1-1l4 6c1 1 2 3 3 4 0 2 1 4 1 6-2-1-4-3-5-4l-9-11 1-11h0v1h1c3-3 3-7 5-11z" class="I"></path><path d="M314 302l-3-2 3-12c1 1 2 2 3 2l-1 2v2l-2 8h1-1z" class="Y"></path><path d="M314 302l-1-1v-3c1-3 1-3 3-4l-2 8z" class="L"></path><path d="M319 286v-4-4-2c2 2 4 3 4 6h0l1 1 3-1v1l-2 3-1 2-1 11h0c0 2 0 3-1 5-1 3 0 6-1 8-1-1-2-3-3-4l-4-6h1-1l2-8v-2l1-2c0-1 1-3 2-4z" class="P"></path><path d="M327 282v1l-2 3-1 2v-5l3-1z" class="S"></path><path d="M316 292c2 4 2 8 2 12v2c-1-1-2-3-3-4h-1l2-8v-2z" class="k"></path><path d="M319 286v-4-4-2c2 2 4 3 4 6h0v1c-1 1-2 4-2 6 0 0 0 1 1 1-2 0-2 0-3-1v-3z" class="d"></path><path d="M318 304c1 0 1-2 2-3h0v3h1c0-1 0-3 1-5h1c0 2 0 3-1 5-1 3 0 6-1 8-1-1-2-3-3-4l-4-6h1c1 1 2 3 3 4v-2z" class="U"></path><path d="M270 267c3-1 9 0 12 1s6 3 9 4l3 3c3 3 7 6 10 9 1 1 2 2 3 4 1 1 1 2 2 4h0c-1 1-1 2-2 3 0 3 0 5-1 8-2-1-3-3-4-4l-2-1c-2-1-3-2-4-3h-1l-1-1c-1 0-1 0-2-1s-2-1-3-1c-1-1-2-1-3-1v-1h-2-1c-1-1-1-1-3-1-1-1-1-1-2-1l-2-1c-2-1-3-3-3-4-1-1-2-1-3-1h0c-1 0-1 1-1 2l-1-1c-1-2-1-3-1-5 1-1 1-1 1-3l1-8h1z" class="H"></path><path d="M270 267v4c-1 1-1 0 0 1v4l-1 1h0l-1-2 1-8h1z" class="B"></path><path d="M290 278c1 1 2 1 3 3h-1l-2 2s-1-1-2-1l2-4z" class="h"></path><path d="M270 267c3-1 9 0 12 1s6 3 9 4l3 3c-1 0-2-1-3-1l-1-1-1 1h-2c-1-1-1-1-2-1h-1c-2-1-7-1-8-2l-1-1c-1-1-4 0-5 1v-4z" class="J"></path><path d="M290 278l1-1c1 0 4 3 5 4 1 0 2 1 3 1s1 0 2 1 1 1 3 1c1 1 2 2 3 4 1 1 1 2 2 4h0c-1 1-1 2-2 3h0l-1-1-1-1c-4-4-10-5-15-9h1l-1-1 2-2h1c-1-2-2-2-3-3z" class="N"></path><path d="M307 288c1 1 1 2 2 4h0c-1 1-1 2-2 3h0l-1-1-1-1c1-2 1-4 2-5z" class="F"></path><path d="M270 282v-4c3-2 8 1 11 2l9 4c5 4 11 5 15 9l1 1 1 1h0c0 3 0 5-1 8-2-1-3-3-4-4l-2-1c-2-1-3-2-4-3h-1l-1-1c-1 0-1 0-2-1s-2-1-3-1c-1-1-2-1-3-1v-1h-2-1c-1-1-1-1-3-1-1-1-1-1-2-1l-2-1c-2-1-3-3-3-4-1-1-2-1-3-1h0z" class="E"></path><path d="M273 283c2 1 4 2 6 2 2 1 3 1 4 3h1v1c3 1 6 1 8 4-1-1-2-1-3-1-1-1-2-1-3-1v-1h-2-1c-1-1-1-1-3-1-1-1-1-1-2-1l-2-1c-2-1-3-3-3-4z" class="R"></path><path d="M284 232c1 4 1 6 4 9h1v-5c0-3 2-8 4-10l1-1c0 4-2 8-2 12h0c0 2-1 4 0 6s2 4 3 7l1 2c1 1 2 3 2 5h0v1 3c-1 0-2 1-3 1s-2 1-3 1c-2 1-3 1-4 2h-1-1l1 1h0l-2 1h1l1 1h1 1c1 0 3 0 4 1-4 1-5-1-8-2-2 0-2 1-3 1-3-1-9-2-12-1h-1c0-4 1-9 2-14-2 2-3 4-5 7l-2-1c-1 0-3 0-4 1-2 0-4-1-6-2l-9-1v-1c-1-1-2-1-3-1 1-1 3-1 5-2 1 0 3 0 5-1l4-1s1-1 2-1c2-1 5-2 7-2h3s1-1 2-1h1 2 6l1 1 1-2c1 0 1 0 2 1h2 2c0-1 0-1 1-1-3-5-4-8-4-14h0z" class="E"></path><path d="M285 247h2c0-1 0-1 1-1 2 2 5 5 6 7 1 1 1 2 2 2l1 2c1 1 0 2 0 4h-1l-1-1c1 0 1 0 1-1 0-2-2-4-3-5h-1l-1-1c-1 0-1 0-2-1s-2-2-4-3h-2c1-2 1-1 0-2h2z" class="F"></path><path d="M279 247l1 1h1c1 1 1 2 2 2 3 0 7 4 10 5-4 3-7 5-11 6-3 1-5 2-7 3h-1 0c3-3 4-5 6-9l-5-3c-1-1-2-2-3-2-1-1-2-1-3-2h9l1-1z" class="N"></path><path d="M256 251s1-1 2-1c2-1 5-2 7-2h3s1-1 2-1h1 2 6l-1 1h-9c1 1 2 1 3 2 1 0 2 1 3 2h-2c-2 0-3 1-5 2 0 1-1 1-2 1-2 0-4 1-6 2s-4 1-6 1l-9-1v-1c-1-1-2-1-3-1 1-1 3-1 5-2 1 0 3 0 5-1l4-1z" class="G"></path><path d="M245 256c4 0 9-1 13-2 2-1 4-2 5-2 3-1 7-2 10 0-2 0-3 1-5 2 0 1-1 1-2 1-2 0-4 1-6 2s-4 1-6 1l-9-1v-1z" class="N"></path><path d="M317 230l3 3 1 1v-1l1-1 2 2-1 1h-1c0 1 1 4 2 5h0 1c1 0 2 1 3 1h1c-1-1-1-2-1-4l3-1c1 1 1 1 1 2s-1 2-1 2c1 2 2 2 3 4 4 3 7 7 9 11v1h-2l-1-1c-1 1 0 1 0 2v1 1h-2c-1 1-2 1-2 2v3c1 0 2-1 3-1h0c-1 1-1 2-1 3-1 3-5 8-5 11-1 1-2 1-2 2l-2 1 1 1v3c2 0 2 0 3 2v1h-1c-1 0-2 1-3 1l-2-1-2-1 2-3v-1l-3 1-1-1h0c0-3-2-4-4-6 0-1-2-3-2-3v-1c-2-3 0-6-1-10-1-1-1-1-1-2v-4c0-3 0-6 3-8h0 1c-2-6-6-13-9-17l1-1c2 1 2 4 4 4 2 1 3 3 3 4l2 2h0l-3-10z" class="J"></path><path d="M323 282c1-2 2-2 3-3 1 0 1 2 1 3l-3 1-1-1z" class="K"></path><path d="M327 283c2 1 4 2 5 4-1 0-2 1-3 1l-2-1-2-1 2-3z" class="F"></path><path d="M315 260l1-1v-4 1l4 5h0c1 5 2 9 2 13l-1 1h0v1l-4-4c-2-3 0-6-1-10-1-1-1-1-1-2z" class="j"></path><path d="M336 264c1 0 2-1 3-1h0c-1 1-1 2-1 3-1 3-5 8-5 11-1 1-2 1-2 2l-2 1h-2c0-2 0-1 1-2v-3l-1-1h-1l1-1h2c2-1 2-2 3-4h0l-1-1c2 0 3-1 4-3l1-1z" class="i"></path><defs><linearGradient id="BU" x1="316.356" y1="236.455" x2="320.385" y2="250.18" xlink:href="#B"><stop offset="0" stop-color="#5a5c61"></stop><stop offset="1" stop-color="#727678"></stop></linearGradient></defs><path fill="url(#BU)" d="M317 230l3 3 1 1v-1l1-1 2 2-1 1h-1c0 1 1 4 2 5h0c1 1 1 1 1 2-1 0-2-1-3-2v1h0c1 2 1 4 1 6 0 3 2 12 1 14h-1 0l-1 2 1 2v3c0 1 0 2 1 3h0c-1-1-1 0-1-1v-2h0c-1-3-1-5-3-7h0l-4-5v-1 4l-1 1v-4c0-3 0-6 3-8h0 1c-2-6-6-13-9-17l1-1c2 1 2 4 4 4 2 1 3 3 3 4l2 2h0l-3-10z"></path><path d="M318 248h0c3 4 2 7 3 11v2h0l-1-1v1l-4-5v-1 4l-1 1v-4c0-3 0-6 3-8z" class="P"></path><path d="M317 230l3 3 1 1v-1l1-1 2 2-1 1h-1c0 1 1 4 2 5h0c1 1 1 1 1 2-1 0-2-1-3-2v1h0c1 2 1 4 1 6 0 3 2 12 1 14h-1c0-2 0-5-1-8v-8l-1-3c0-1 0-1-1-2l-3-10z" class="b"></path><path d="M328 237l3-1c1 1 1 1 1 2s-1 2-1 2c1 2 2 2 3 4 4 3 7 7 9 11v1h-2l-1-1c-1 1 0 1 0 2v1 1h-2c-1 1-2 1-2 2v3l-1 1c-1 2-2 3-4 3h-1v1c-1 0-1 0-1 2h-2l-1 1-1-1c0-2-1-4-1-6 0-1 0-2-1-3v-1h0 1c1-2-1-11-1-14 0-2 0-4-1-6h0v-1c1 1 2 2 3 2 0-1 0-1-1-2h1c1 0 2 1 3 1h1c-1-1-1-2-1-4z" class="X"></path><path d="M323 247v1c2 2 1 6 3 8 2-1 3-2 4-3 2 1 3 2 5 3v-1l-1-2h1l5 4v1 1h-2c-1 1-2 1-2 2v-1c-2-1-3-2-5-3-2 0-2 0-4 1-1 1-2 2-3 4 1 2 1 3 2 5 1 1 3 1 4 1v1c-1 0-1 0-1 2h-2l-1 1-1-1c0-2-1-4-1-6 0-1 0-2-1-3v-1h0 1c1-2-1-11-1-14z" class="i"></path><path d="M327 258c2-1 2-1 4-1 2 1 3 2 5 3v1 3l-1 1c-1 2-2 3-4 3h-1c-1 0-3 0-4-1-1-2-1-3-2-5 1-2 2-3 3-4z" class="C"></path><path d="M329 260h1c1 1 2 1 2 3v1h-2-1c-1-2-1-2 0-4z" class="Y"></path><path d="M327 258c2-1 2-1 4-1 2 1 3 2 5 3v1 3l-1 1-1-1v-1c0-2-2-3-4-4l-1-1h-2z" class="G"></path><path d="M196 225l2 1c2 0 4 0 5-1 4 0 9 0 12 1h0c2 0 3-1 4 0l2 1 13 12c2 1 5 3 7 4h4 0c3 2 6 3 9 4l-1 2h1l1 1 1 1-4 1c-2 1-4 1-5 1-2 1-4 1-5 2 1 0 2 0 3 1v1l9 1c2 1 4 2 6 2 1-1 3-1 4-1l2 1-5 6h0c-2 2-4 4-5 7-1 1-2 3-2 5l-1 1h0v1h-2l-2 1h-5l-3-1v-1l-2-1-1-1c-2-1-5-1-7-1-2 1-4 1-5 0-2-2-2-3-2-5-1 0-2 1-2 1-2 0-3 0-4 1l-2-2c-1-1-1-1-1-3l-6-14 2-2h1 1l2-2-1-1c-1-1-1-2-2-2l-2-4h1l-1-1v-1c-2-2-3-3-6-3l-2-1c-2-2-3-4-5-5h0l-1-4c-1-1-1-2 0-3z" class="Q"></path><path d="M215 226h0c2 0 3-1 4 0v1l2 2h0l-5-1-2-1h0c-1 1-1 2-1 3l2 2v1 1l-2-1v1c1 1 2 2 3 2 1 2 2 3 3 4-5-3-9-7-13-11h0c1-1 3-3 4-3h5z" class="B"></path><path d="M219 253l-1-1c0-1 1-1 1-2h0v-1c-1 1-1 1-2 1l-1-1c1 0 2-1 3-1v-1c1-2 2-4 3-5 2 2 4 4 5 6-1 1 0 1 0 2l-6 4-2-1z" class="J"></path><defs><linearGradient id="BV" x1="194.492" y1="226.956" x2="218.126" y2="243.318" xlink:href="#B"><stop offset="0" stop-color="#4c4f54"></stop><stop offset="1" stop-color="#6b6d70"></stop></linearGradient></defs><path fill="url(#BV)" d="M196 225l2 1 5 3c6 4 11 10 17 14l-3 4-1 1c-3-1-4-3-5-5l-1-1v-1c-2-2-3-3-6-3l-2-1c-2-2-3-4-5-5h0l-1-4c-1-1-1-2 0-3z"></path><path d="M210 241c3 0 4 3 6 4v1l1 1-1 1c-3-1-4-3-5-5l-1-1v-1zm9-15l2 1 13 12c2 1 5 3 7 4l-6-1 2 2v1h-1v-1h-1-6v1 1h-1c-1-1-4-4-6-5-1 0-2 0-3-1s-2-2-3-4c-1 0-2-1-3-2v-1l2 1v-1-1l-2-2c0-1 0-2 1-3h0l2 1 5 1h0l-2-2v-1z" class="H"></path><path d="M227 238h5c1 0 1 2 2 1 2 1 5 3 7 4l-6-1c-3-1-5-3-8-4z" class="g"></path><path d="M215 232l-2-2c0-1 0-2 1-3h0l2 1h-1c3 3 7 5 10 8 0 2 1 2 2 3v1l-2-1c-2 0-2 1-2 2h-2c-1-1-1-2-2-3-2-2-3-4-4-6z" class="b"></path><path d="M219 226l2 1 13 12c-1 1-1-1-2-1h-5l-2-2c-3-3-7-5-10-8h1l5 1h0l-2-2v-1z" class="M"></path><path d="M235 242c1 0 6 1 6 1h4 0c3 2 6 3 9 4l-1 2c-3 1-6 0-9 1l-4 1c-5 3-14 4-17 10l-1 1v2l1 1h4c-2 0-4 0-6 1s-3 1-4 2l-1 3c-1-1-1-1-1-3l-6-14 2-2h1 1l2-2 4 4v-1l2 1 6-4c0-1-1-1 0-2l1 1c1 0 2-1 3-1h0v-1l-1-1-1-1v-1h6 1v1h1v-1l-2-2z" class="V"></path><path d="M227 250l3-1h1c2 0 0 0 2-1h1l8-1v1c-1 1 0 1-1 1h-3l2 2c-5 3-14 4-17 10l-1 1v-2c0-1 2-3 3-3l5-3v-1c-2 0-4 0-6 1h-3l6-4z" class="N"></path><defs><linearGradient id="BW" x1="213.37" y1="266.425" x2="214.286" y2="252.72" xlink:href="#B"><stop offset="0" stop-color="#1d1c1c"></stop><stop offset="1" stop-color="#494c51"></stop></linearGradient></defs><path fill="url(#BW)" d="M215 250l4 4v-1l2 1h3c2-1 4-1 6-1v1l-5 3c-1 0-3 2-3 3v2 2l1 1h4c-2 0-4 0-6 1s-3 1-4 2l-1 3c-1-1-1-1-1-3l-6-14 2-2h1 1l2-2z"></path><path d="M215 250l4 4v1c-3-1-4-1-6-3l2-2z" class="b"></path><path d="M219 257c1 2 1 4 2 6v1h1l1 1h4c-2 0-4 0-6 1s-3 1-4 2l-1 3c-1-1-1-1-1-3l1-1c3-3 3-6 3-10z" class="Z"></path><path d="M224 254c2-1 4-1 6-1v1l-5 3c-1 0-3 2-3 3v2 2h-1v-1c-1-2-1-4-2-6v-1c2 1 1 3 2 5v-1h0c0-3 1-4 3-6h0z" class="R"></path><defs><linearGradient id="BX" x1="234.982" y1="251.58" x2="246.883" y2="268.231" xlink:href="#B"><stop offset="0" stop-color="#17181b"></stop><stop offset="1" stop-color="#313234"></stop></linearGradient></defs><path fill="url(#BX)" d="M253 249h1l1 1 1 1-4 1c-2 1-4 1-5 1-2 1-4 1-5 2 1 0 2 0 3 1v1l9 1c2 1 4 2 6 2 1-1 3-1 4-1l2 1-5 6h0v-1c-1 0-2 1-2 1h-11c-3-1-6 0-8-1-4-1-9-1-13 0h-4l-1-1v-2l1-1c3-6 12-7 17-10l4-1c3-1 6 0 9-1z"></path><path d="M264 259l2 1-5 6h0v-1l-2-2c-2 0-4-1-6-1 3 0 6 1 9 0 0-1 1-2 2-3z" class="I"></path><path d="M254 249l1 1 1 1-4 1c-2 1-4 1-5 1-2 1-4 1-5 2-3 0-5 2-8 2 1-1 3-2 4-2 4-2 8-3 12-4v-1c2 1 3 0 4-1z" class="c"></path><path d="M223 261h6c5-1 12-1 17-1 3 1 5 2 7 2s4 1 6 1l2 2c-1 0-2 1-2 1h-11c-3-1-6 0-8-1-4-1-9-1-13 0h-4l-1-1v-2l1-1z" class="m"></path><defs><linearGradient id="BY" x1="258.801" y1="270.017" x2="222.398" y2="273.137" xlink:href="#B"><stop offset="0" stop-color="#222225"></stop><stop offset="1" stop-color="#3b3e42"></stop></linearGradient></defs><path fill="url(#BY)" d="M261 265v1c-2 2-4 4-5 7-1 1-2 3-2 5l-1 1h0v1h-2l-2 1h-5l-3-1v-1l-2-1-1-1c-2-1-5-1-7-1-2 1-4 1-5 0-2-2-2-3-2-5-1 0-2 1-2 1-2 0-3 0-4 1l-2-2 1-3c1-1 2-1 4-2s4-1 6-1c4-1 9-1 13 0 2 1 5 0 8 1h11s1-1 2-1z"></path><path d="M216 271l1-3c1-1 2-1 4-2 1 0 2 1 3 1-1 1-1 0-2 2l1 2 1-1v1h0c-1 0-2 1-2 1-2 0-3 0-4 1l-2-2z" class="G"></path><path d="M241 279h0c2-1 4-1 6-2 1 0 2-1 4-2 1 1 2 3 2 4v1h-2l-2 1h-5l-3-1v-1z" class="W"></path><path d="M266 260c2-3 3-5 5-7-1 5-2 10-2 14l-1 8c0 2 0 2-1 3 0 2 0 3 1 5l1 1c0-1 0-2 1-2h0c0 1 0 2 1 2l1 3c1 1 1 3 1 4s0 1 1 2v6l-1 4c0 1 0 1-1 2s-1 1-1 2l-3 3h-1v-1c-1 0-2 1-3 1s-3-1-4-2c-2 0-3 1-5 1v-2h-1c-4 3-7 4-10 7l-3 3c1 1 1 1 2 1l-6 2-5 1c-1 1-3 0-4 0l-1 1-2-1h0c-3-2-8-1-11-2h-3-2l-1-1c-1 0-1 0-2-1-2 1-2 1-4 1-3 1-6 1-9 1-4 0-8 0-12 1 2-3 4-2 7-4h-2l2-1h-1v-2c-1-1-2-1-2-3h-1c0-2 2-3 3-4 1-3 2-7 4-9l1-1 5-5-1-3h1c2 0 3-1 5 0l2-2h1c2-1 5-4 7-4 3-1 8-2 10-4l-2-2v-2c1 0 2-1 2-2 0 0 1-1 2-1 0 2 0 3 2 5 1 1 3 1 5 0 2 0 5 0 7 1l1 1 2 1v1l3 1h5l2-1h2v-1h0l1-1c0-2 1-4 2-5 1-3 3-5 5-7h0l5-6z" class="E"></path><path d="M237 300h5 2c-3 1-3 2-5 4l-2-2v-2z" class="Q"></path><path d="M254 284v2c-1 2-1 4-1 7h0-1v-7h0c1-1 2-1 2-2z" class="B"></path><path d="M253 280l-1 6h0-1c-2 1-4 3-5 5s-2 3-3 4h-1c3-3 8-10 9-14v-1h2z" class="F"></path><path d="M252 293h1 1c1 1 0 1 1 2h1 1v1c0 1 0 2-1 3-1 0-2 0-3 1h-2c0-2 0-2 1-4v-3z" class="Q"></path><path d="M252 293h1 1c1 1 0 1 1 2h1c-1 1-3 1-4 1v-3zm-23-7l2-2c0-1 0-3 1-4 2-1 3-1 5 0v1c-1 2-3 4-5 5h-3 0z" class="G"></path><path d="M209 291l1 1c1 0 1 1 2 1v-1h1 1 1 1c-1 1-2 1-4 1l-1 2h3 3c0 1 1 1 1 1l-9 2c-1-1-2-1-3-2l1-1v-1h-1v-2h2l1-1z" class="c"></path><path d="M254 286h1c1-2 1-3 2-4h0c0 1 1 2 0 3 0 2-1 5 0 7h0c0 1 1 2 1 3 1 1 1 0 1 3-1 0-1 1-3 1 1-1 1-2 1-3v-1h-1-1c-1-1 0-1-1-2h-1 0c0-3 0-5 1-7z" class="E"></path><path d="M254 286h1c1-2 1-3 2-4h0c0 1 1 2 0 3-1 2 0 2-1 4 0 1-2 3-3 4 0-3 0-5 1-7z" class="Q"></path><path d="M222 278c2 3 5 5 7 8h0c-3 0-5 1-8 1l-3 3c-1 1-3 1-5 2h-1v1c-1 0-1-1-2-1l-1-1h-3-1c-1 0-2-1-2-1h-2l3-3v-1h1c2-1 5-4 7-4 3-1 8-2 10-4z" class="H"></path><path d="M212 292l1-2h0v-1c2-1 5-2 8-2l-3 3c-1 1-3 1-5 2h-1z" class="B"></path><path d="M225 296c2 0 3 1 4 1 1 1 3 1 3 2h1l4 1v2l2 2c2-2 2-3 5-4h2v2l1 1c2 1 4 3 7 4-4 3-7 4-10 7l-3 3c1 1 1 1 2 1l-6 2-5 1c-1 1-3 0-4 0l-1 1-2-1h0c-3-2-8-1-11-2h-3c1-1 2-2 4-3l-1-1 1-1v-1c2-1 2-1 3-3l1-1s1-1 1-2 2-3 4-4l-2-1c2-1 2-1 3-3 0-1 1-1 1-2l-1-1z" class="a"></path><path d="M224 317c2-2 5-4 7-6 0 2-1 4-3 5-1 1-3 1-4 1z" class="h"></path><path d="M237 302l2 2c-2 2-5 6-7 7 0-3 3-6 4-8l1-1z" class="R"></path><path d="M228 311h0c2-3 4-6 7-9l1 1c-1 2-4 5-4 8h-1c-2 2-5 4-7 6l-2 1h-1l7-7z" class="H"></path><path d="M218 310l4-1c0 1 0 2-1 3h1l1-1c2 0 3-1 5 0l-7 7h1c-1 1-3 0-4 0l-1-1-1 1s-1 0-2 1h-3c1-1 2-2 4-3l-1-1 1-1v-1c2-1 2-1 3-3z" class="D"></path><path d="M218 310l4-1c0 1 0 2-1 3h1c-2 3-3 4-6 4h-1l-1-1 1-1v-1c2-1 2-1 3-3z" class="B"></path><path d="M218 310l4-1c0 1 0 2-1 3l-7 3 1-1v-1c2-1 2-1 3-3z" class="J"></path><path d="M225 321c1-1 2-2 4-2s5-5 7-7c2-3 6-8 10-10l1 1c-3 3-7 6-8 10h-2c-1 1-1 2-2 3 1 1 1 1 2 1 2 0 2 0 4-2l3-1-3 3c1 1 1 1 2 1l-6 2-5 1c-1 1-3 0-4 0l-1 1-2-1h0z" class="V"></path><path d="M241 315l3-1-3 3c1 1 1 1 2 1l-6 2-5 1c1-2 3-2 5-4 2 0 2 0 4-2z" class="K"></path><path d="M247 303c2 1 4 3 7 4-4 3-7 4-10 7l-3 1c-2 2-2 2-4 2-1 0-1 0-2-1 1-1 1-2 2-3h2c1-4 5-7 8-10z" class="J"></path><defs><linearGradient id="BZ" x1="235.183" y1="314.071" x2="246.894" y2="309.109" xlink:href="#B"><stop offset="0" stop-color="#616266"></stop><stop offset="1" stop-color="#787a7d"></stop></linearGradient></defs><path fill="url(#BZ)" d="M239 313h1c2-2 4-5 7-7v1c0 3-4 6-6 8s-2 2-4 2c-1 0-1 0-2-1 1-1 1-2 2-3h2z"></path><path d="M225 296c2 0 3 1 4 1 1 1 3 1 3 2h1l4 1v2l-1 1-1-1c-3 3-5 6-7 9h0c-2-1-3 0-5 0l-1 1h-1c1-1 1-2 1-3l-4 1 1-1s1-1 1-2 2-3 4-4l-2-1c2-1 2-1 3-3 0-1 1-1 1-2l-1-1z" class="K"></path><path d="M232 299h1l1 2c-2 2-4 5-6 6 0-1 1-2 1-4 1-2 2-3 3-4z" class="H"></path><path d="M229 303c0 2-1 3-1 4l-5 4-1 1h-1c1-1 1-2 1-3v-1l2-1c2-1 4-3 5-4z" class="b"></path><path d="M225 296c2 0 3 1 4 1 1 1 3 1 3 2-1 1-2 2-3 4-1 1-3 3-5 4l-2 1v1l-4 1 1-1s1-1 1-2 2-3 4-4l-2-1c2-1 2-1 3-3 0-1 1-1 1-2l-1-1z" class="h"></path><path d="M225 296c2 0 3 1 4 1-2 2-4 4-5 6l-2-1c2-1 2-1 3-3 0-1 1-1 1-2l-1-1z" class="B"></path><defs><linearGradient id="Ba" x1="254.061" y1="306.679" x2="274.563" y2="277.874" xlink:href="#B"><stop offset="0" stop-color="#111113"></stop><stop offset="1" stop-color="#2a2c30"></stop></linearGradient></defs><path fill="url(#Ba)" d="M266 260c2-3 3-5 5-7-1 5-2 10-2 14l-1 8c0 2 0 2-1 3 0 2 0 3 1 5l1 1c0-1 0-2 1-2h0c0 1 0 2 1 2l1 3c1 1 1 3 1 4s0 1 1 2v6l-1 4c0 1 0 1-1 2s-1 1-1 2l-3 3h-1v-1c-1 0-2 1-3 1s-3-1-4-2c-2 0-3 1-5 1v-2l-1-1c-1-1-1-1-3-1h0c-1-2 0-2 1-4 1 0 1 0 1-1 1-1 2-1 3-1 2 0 2-1 3-1 0-3 0-2-1-3 0-1-1-2-1-3h0c-1-2 0-5 0-7 1-1 0-2 0-3h0c-1 1-1 2-2 4h-1v-2c0 1-1 1-2 2l1-6v-1h0l1-1c0-2 1-4 2-5 1-3 3-5 5-7h0l5-6z"></path><path d="M257 292h2c1 1 1 2 1 4h0l-1 2h0c0-3 0-2-1-3 0-1-1-2-1-3z" class="W"></path><path d="M259 280c1 2 1 4 2 6 1 0 1 1 2 2 0-1 1-1 1-1h0c-1 2-1 2-3 3h0c-1-1-1 0-2-1v1c-1-3 0-6 0-10z" class="a"></path><path d="M268 304l1 2h1v1l-3 2c-1 0-2 1-3 1s-3-1-4-2c2 0 2 0 4-2 1 0 1 0 2 1h0 1v-2l1-1z" class="Z"></path><path d="M264 306c0-3-1-4-1-6 1 0 1 2 2 2s4-3 5-4v3l-2 3-1 1v2h-1 0c-1-1-1-1-2-1z" class="B"></path><path d="M259 273l6-7c1 2 1 4 0 6h0v-2h-1c-2 1-2 3-4 5-1 1-1 3-1 5 0 4-1 7 0 10l-2 2c-1-2 0-5 0-7 1-1 0-2 0-3h0c-1 1-1 2-2 4h-1v-2c1-5 2-7 5-11z" class="D"></path><path d="M266 260c2-3 3-5 5-7-1 5-2 10-2 14l-1 8c0 2 0 2-1 3v-1c0-2-1-3-2-5 1-2 1-4 0-6l-6 7c-3 4-4 6-5 11 0 1-1 1-2 2l1-6v-1h0l1-1c0-2 1-4 2-5 1-3 3-5 5-7h0l5-6z" class="e"></path><path d="M266 263c0-2 0-2 2-3 1 1 0 3-1 4l-1-1z" class="O"></path><path d="M266 263l1 1v6 1 6c0-2-1-3-2-5 1-2 1-4 0-6 0 0 0-2 1-3z" class="B"></path><path d="M256 273h1l4-5h0v1 1l-2 2v1c-3 4-4 6-5 11 0 1-1 1-2 2l1-6v-1h0l1-1c0-2 1-4 2-5z" class="J"></path><path d="M204 286v1l-3 3h2s1 1 2 1h1 3l-1 1h-2v2h1v1l-1 1c1 1 2 1 3 2l9-2h7l1 1c0 1-1 1-1 2-1 2-1 2-3 3l2 1c-2 1-4 3-4 4s-1 2-1 2l-1 1c-1 2-1 2-3 3v1l-1 1 1 1c-2 1-3 2-4 3h-2l-1-1c-1 0-1 0-2-1-2 1-2 1-4 1-3 1-6 1-9 1-4 0-8 0-12 1 2-3 4-2 7-4h-2l2-1h-1v-2c-1-1-2-1-2-3h-1c0-2 2-3 3-4 1-3 2-7 4-9l1-1 5-5-1-3h1c2 0 3-1 5 0l2-2z" class="V"></path><path d="M218 296h7l1 1c0 1-1 1-1 2h-1c-3 0-6 1-8 1-2 1-3 1-4 1 0-1 3-2 4-3-2 0-5 1-6 1-1 1-2 1-3 1h-3c-1 1-3 1-4 2l-2-1 11-3 9-2z" class="J"></path><path d="M216 300v-1l2-1h1c2 0 4 0 5 1-3 0-6 1-8 1z" class="e"></path><path d="M224 299h1c-1 2-1 2-3 3h0l-3 1c-2 0-4 1-6 1-1 0 0 0-1 1l-7 1c-1 0-3 1-4 1-2 0-5 0-7-1 6-2 12-3 18-5 1 0 2 0 4-1 2 0 5-1 8-1z" class="h"></path><defs><linearGradient id="Bb" x1="199.239" y1="301.659" x2="194.489" y2="293.366" xlink:href="#B"><stop offset="0" stop-color="#2b2b2f"></stop><stop offset="1" stop-color="#444649"></stop></linearGradient></defs><path fill="url(#Bb)" d="M204 286v1l-3 3h2s1 1 2 1h1 3l-1 1h-2v2h1v1l-1 1c1 1 2 1 3 2l-11 3h-2c-3 1-6 3-9 5 1-3 2-7 4-9l1-1 5-5-1-3h1c2 0 3-1 5 0l2-2z"></path><path d="M197 288c2 0 3-1 5 0-1 1-2 1-4 2 0 0 0 1-1 1l-1-3h1z" class="F"></path><path d="M222 302l2 1c-2 1-4 3-4 4s-1 2-1 2l-1 1c-1 2-1 2-3 3v1l-1 1 1 1c-2 1-3 2-4 3h-2l-1-1c-1 0-1 0-2-1-2 1-2 1-4 1-3 1-6 1-9 1-4 0-8 0-12 1 2-3 4-2 7-4h-2l2-1h-1v-2c-1-1-2-1-2-3 2-2 7-3 9-4 2 1 5 1 7 1 1 0 3-1 4-1l7-1c1-1 0-1 1-1 2 0 4-1 6-1l3-1h0z" class="h"></path><path d="M222 302l2 1c-2 1-4 3-4 4s-1 2-1 2l-1 1c-1 2-1 2-3 3v1l-1 1 1 1c-2 1-3 2-4 3h-2l-1-1c-1 0-1 0-2-1l6-5-2-2c1-2 2-3 3-4h-2c1 0 1 0 1-1 1-1 0-1 1-1 2 0 4-1 6-1l3-1h0z" class="V"></path><path d="M212 305c1-1 0-1 1-1 2 0 4-1 6-1l3-1c-4 4-7 6-10 10l-2-2c1-2 2-3 3-4h-2c1 0 1 0 1-1z" class="N"></path><path d="M212 305c0 1 0 1-1 1v1c-3 1-4 2-6 4l-8 3c-2 2-5 1-7 1 0-1 0-1 1-2h-4c-1-1-2-1-2-3 2-2 7-3 9-4 2 1 5 1 7 1 1 0 3-1 4-1l7-1z" class="B"></path><path d="M195 309l3-1c1 0 3 0 4 1-2 1-5 1-7 1v-1z" class="O"></path><path d="M201 307c1 0 3-1 4-1v2c-1 0-2 1-3 1-1-1-3-1-4-1l3-1z" class="H"></path><path d="M185 310c2-2 7-3 9-4 2 1 5 1 7 1l-3 1-3 1c-3-1-5 0-8 1l1 1v1l-1 1c-1-1-2-1-2-3z" class="Q"></path><path d="M270 282c1 0 2 0 3 1 0 1 1 3 3 4l2 1c1 0 1 0 2 1 2 0 2 0 3 1h1 2v1c1 0 2 0 3 1 1 0 2 0 3 1s1 1 2 1l1 1h1c1 1 2 2 4 3l2 1c1 1 2 3 4 4 6 6 11 13 15 20l1-1 2-1c1 2 1 3 1 5v1h-1l2 1 1-1v3c-1 1-1 2 0 3 3 4 6 7 8 12l1 1 1 1c1 1 1 2 1 3h0c3 3 4 5 5 9l1 3-1 9h-1 0c0-1-1-2-1-4 0 2 0 5-1 6h0v1c-1 0-2 1-3 1-1-1 0-1-1-1-8-3-14-4-22-4l-1-1-1 2h-1l-2-1v-1h-1c-1-2-3-4-4-5l-1-2c-1-2-3-3-4-5l2-2-2-2-22-24c0-1-2-2-2-2-2-1-5-1-7-1l-7 3s0-1-1 0c-2 0-5 1-7 1v-1l2-1c-1 0-2 0-3-1l-1 1-1-2c-2 0-3 0-4-1l-2-2-1 1h-1c1-1 1-2 2-3 1 0 0 1 1 0v-1l-2 1v1c-3 2-4 1-7 1l1-1c1-1 3-1 4-1h1v-1h-2-3l6-2c-1 0-1 0-2-1l3-3c3-3 6-4 10-7h1v2c2 0 3-1 5-1 1 1 3 2 4 2s2-1 3-1v1h1l3-3c0-1 0-1 1-2s1-1 1-2l1-4v-6c-1-1-1-1-1-2s0-3-1-4l-1-3c-1 0-1-1-1-2z" class="E"></path><path d="M280 308v2c1 1 1 1 2 1-2 0-3 1-4 2l-1 1-1-1c0-2 2-4 4-5z" class="R"></path><path d="M324 357l-1 1h-3l-2 1c-1-1-2-1-3-2v-3c1-1 3-1 4-1v1h-2l1 1h4 2-1c0 1 0 1-1 1 1 1 1 1 2 1z" class="V"></path><path d="M324 352c6 2 10 5 14 11v1h-1v-1c-3-1-6-3-9-4-1 0-2-1-4-2-1 0-1 0-2-1 1 0 1 0 1-1h1-2-4l-1-1h2v-1c1-1 4-1 5-1z" class="S"></path><path d="M322 355c2-1 4-1 6 0 1 0 1 0 2 1v1h-3c-1 0-2-1-3-2h-2z" class="G"></path><path d="M280 310c7 0 13 3 18 6 1 1 1 1 2 1 1 1 2 2 2 4h0-1-2-3-1l1 1c-4 0-6 1-9 3-2 1-4 2-6 2v-1l-2-1h0l5-4-1-1h-4-3s-1 0-1-1c-2 0-3-1-4-1h-1c-1-1-1-2-2-3l1-1c2 1 4 3 6 4 0 0 1-1 2 0 3 0 4 0 6-2h1-2 0l-3-1-2-1 1-1c1-1 2-2 4-2-1 0-1 0-2-1z" class="c"></path><path d="M279 320c2-1 4-1 6-1h3l1 1c-1 1-2 1-4 2l-1-1-1-1h-4z" class="C"></path><path d="M280 310c7 0 13 3 18 6 1 1 1 1 2 1 1 1 2 2 2 4h0-1-2-3l1-1-1-1h-3v1l-1-1v-1h4v-1h-3c-1 0-1-1-3-1 0 1 0 1-1 2-2 0-3 0-5-1h0l3-1h1l-1-1-3 1h-2 0l-3-1-2-1 1-1c1-1 2-2 4-2-1 0-1 0-2-1z" class="I"></path><path d="M277 314l1-1c1-1 2-2 4-2 3 1 7 2 10 4h-1c-1 0-2 0-3-1-3 0-6 1-9 1l-2-1z" class="K"></path><path d="M252 319l8-4c1 1 2 2 4 2l8 6c1 1 0 2 2 2h2v1l2 2c0 1 0 0-1 1 0-1-2-2-2-2-2-1-5-1-7-1l-7 3s0-1-1 0c-2 0-5 1-7 1v-1l2-1c-1 0-2 0-3-1l-1 1-1-2c-2 0-3 0-4-1l-2-2c2-2 5-3 7-3l1-1z" class="f"></path><path d="M272 323c1 1 0 2 2 2h2v1l2 2c0 1 0 0-1 1 0-1-2-2-2-2-2-1-5-1-7-1l-7 3s0-1-1 0c-2 0-5 1-7 1v-1l2-1 17-5z" class="D"></path><path d="M252 319l8-4c1 1 2 2 4 2-2 1-3 1-5 3h1 1c-2 1-4 1-6 2v1l1 1c-1 1-3 1-4 2v1l-1 1-1-2c-2 0-3 0-4-1l-2-2c2-2 5-3 7-3l1-1z" class="F"></path><path d="M246 325c2-1 5-1 7-2 1-1 1-1 2-1v1l1 1c-1 1-3 1-4 2v1l-1 1-1-2c-2 0-3 0-4-1z" class="G"></path><path d="M282 306c1-1 1-1 0-2l-1-1h0l1-2h2v-1c-1-1-3-1-4-2h0c3 0 5 0 8 1 4 1 7 3 11 6s6 7 9 11l1 2c0 1 0 1 1 2 0 1 0 3-1 4v4h-1-2l-1 2 1 1-2 3-1-1c1-1 1-2 1-4l-2-2v-1c-1-2-2-2-4-3l-2-1-1-1h1 3 2 1 0c0-2-1-3-2-4-1 0-1 0-2-1-5-3-11-6-18-6v-2l2-1v-1z" class="N"></path><path d="M306 328h1v-8h1l1 4v4h-1-2z" class="H"></path><path d="M300 317c2 0 4 2 6 4l-1 5v3 1l1 1-2 3-1-1c1-1 1-2 1-4l-2-2v-1c-1-2-2-2-4-3l-2-1-1-1h1 3 2 1 0c0-2-1-3-2-4z" class="B"></path><path d="M270 282c1 0 2 0 3 1 0 1 1 3 3 4l2 1c1 0 1 0 2 1v4l2 1h0l1-1v2l1 2h0 3l2 1-1 1c-3-1-5-1-8-1h0c1 1 3 1 4 2v1h-2l-1 2h0l1 1c1 1 1 1 0 2h-4-1c-1 0-2 1-2 2-1 2-2 3-4 3-2 1-3 2-5 2h-3l-1 1-2 1-8 4-1 1c-2 0-5 1-7 3l-1 1h-1c1-1 1-2 2-3 1 0 0 1 1 0v-1l-2 1v1c-3 2-4 1-7 1l1-1c1-1 3-1 4-1h1v-1h-2-3l6-2c-1 0-1 0-2-1l3-3c3-3 6-4 10-7h1v2c2 0 3-1 5-1 1 1 3 2 4 2s2-1 3-1v1h1l3-3c0-1 0-1 1-2s1-1 1-2l1-4v-6c-1-1-1-1-1-2s0-3-1-4l-1-3c-1 0-1-1-1-2z" class="Q"></path><g class="i"><path d="M252 313c2-1 3-2 5-2v1l5 2-2 1-8 4c0-1 0-1 1-3 0-1 0-2-1-3z"></path><path d="M252 313h0c1 1 1 2 1 3-1 2-1 2-1 3l-1 1c-2 0-5 1-7 3l-1 1h-1c1-1 1-2 2-3 1 0 0 1 1 0v-1l-2 1v1c-3 2-4 1-7 1l1-1c1-1 3-1 4-1h1v-1h-2-3l6-2 9-5z"></path></g><path d="M252 313h0c1 1 1 2 1 3-1 2-1 2-1 3l-1 1c-1-1-1-2-2-2v-1c1-1 2-2 2-3l1-1z" class="f"></path><path d="M270 282c1 0 2 0 3 1 0 1 1 3 3 4l2 1c1 0 1 0 2 1v4l2 1h0l1-1v2l1 2h0 3l2 1-1 1c-3-1-5-1-8-1h0c1 1 3 1 4 2v1h-2l-1 2h0l1 1c1 1 1 1 0 2h-4-1c-1 0-2 1-2 2-1 2-2 3-4 3l1-2c2-3 2-5 3-8 0-2 1-6 0-8h0c0-1 0-2-1-3h0v-2c0-1-1-1-2-1l-1-3c-1 0-1-1-1-2z" class="h"></path><defs><linearGradient id="Bc" x1="309.245" y1="306.005" x2="298.665" y2="314.064" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#2a2c30"></stop></linearGradient></defs><path fill="url(#Bc)" d="M280 289c2 0 2 0 3 1h1 2v1c1 0 2 0 3 1 1 0 2 0 3 1s1 1 2 1l1 1h1c1 1 2 2 4 3l2 1c1 1 2 3 4 4 6 6 11 13 15 20l1-1 2-1c1 2 1 3 1 5v1h-1l2 1 1-1v3c-1 1-1 2 0 3-2 0-4 0-6 1s-3 2-4 2c0-2 1-3 2-3v-1l-2 1h-1c-2-3-1-8-2-11-1-1-1-1-1-3-2-1-2-1-4-1l-1-2c-3-4-5-8-9-11s-7-5-11-6l1-1-2-1h-3 0l-1-2v-2l-1 1h0l-2-1v-4z"></path><path d="M321 323l1-1 2-1c1 2 1 3 1 5v1h-1c0 1-1 2-2 2v-5l-1-1z" class="j"></path><path d="M283 295l2-1c1 0 2 1 3 2 3 0 5 1 8 3 1 1 3 1 4 2v1l-7-3-4-1-2-1h-3 0l-1-2z" class="F"></path><path d="M299 353c2 1 2 1 4 1h1l9-1v1h0v3c1 2 2 2 5 2h0c4 0 7 0 10 1v-1c3 1 6 3 9 4v1h1l2 2 1 1c0 2 0 5-1 6h0v1c-1 0-2 1-3 1-1-1 0-1-1-1-8-3-14-4-22-4l-1-1-1 2h-1l-2-1v-1h-1c-1-2-3-4-4-5l-1-2c-1-2-3-3-4-5l2-2-2-2z" class="R"></path><path d="M312 368v-1c1-1 4 0 5-1l1 2h-6z" class="X"></path><path d="M317 366c5 0 9 1 14 3-2 0-4-1-6-1h-7l-1-2z" class="P"></path><path d="M303 362c-1-2-3-3-4-5l2-2 10 16-2-1v-1h-1c-1-2-3-4-4-5l-1-2z" class="l"></path><path d="M328 359c3 1 6 3 9 4v1h1l2 2c-1 2-1 3-1 5h0c-2 0-3-2-4-3l-1-1c-3-1-5-3-7-4 2 0 5 1 7 0-1-1-4-2-6-3v-1z" class="N"></path><path d="M327 363c2 0 5 1 7 0 2 1 2 2 2 3l2 3h0l-3-1-1-1c-3-1-5-3-7-4z" class="S"></path><path d="M318 368h7c2 0 4 1 6 1 3 1 6 3 9 4v1c-1 0-2 1-3 1-1-1 0-1-1-1-8-3-14-4-22-4l-1-1-1-1h6z" class="T"></path><path d="M304 354l9-1v1h0v3c1 2 2 2 5 2h0c4 0 7 0 10 1 2 1 5 2 6 3-2 1-5 0-7 0-6-1-12 0-17-1-2-1-2-1-3-3 0-2-2-3-3-5z" class="O"></path><path d="M304 354l9-1c-1 2-2 2-2 4l1 1c-2 1-3 1-5 1 0-2-2-3-3-5z" class="B"></path><path d="M309 318c2 0 2 0 4 1 0 2 0 2 1 3 1 3 0 8 2 11h1l2-1v1c-1 0-2 1-2 3 1 0 2-1 4-2s4-1 6-1c3 4 6 7 8 12l1 1 1 1c1 1 1 2 1 3h0c3 3 4 5 5 9l1 3-1 9h-1 0c0-1-1-2-1-4l-1-1-2-2v-1c-4-6-8-9-14-11h-5v-1h-2l1 1h0-1c-1 0-1 0-1 1h-1c-1 0-1-1-1-1h-3c-3-2-5 0-7-1l-1-1h-3c-3-2-5-5-7-8-2-2-5-4-7-6-1-1-1-2-1-3v-1-1l7-5c2-1 3-1 5 0h1v-3c2 1 3 1 4 3v1l2 2c0 2 0 3-1 4l1 1 2-3-1-1 1-2h2 1v-4c1-1 1-3 1-4-1-1-1-1-1-2z" class="m"></path><path d="M298 323c2 1 3 1 4 3l-2 1-1 2c0 2-1 3-2 4-1 0-1 1-2 2l-1 1-1 1v-1l3-3-1-2v-1l2-1c0 1 0 0 1 1v-2l-1-2h1v-3z" class="C"></path><path d="M288 333c1 0 1-1 2-1h0l3-2c0-1 1-1 2 0v1l1 2-3 3v1h-2c-1 0-1-1-1-2-1 0-1-1-1-1l-1-1z" class="c"></path><path d="M302 327l2 2c0 2 0 3-1 4l1 1c-1 1-1 2-2 3h-1c-1 1-1 1-2 1h-1-2c3-3 5-7 6-11z" class="J"></path><path d="M285 333v-1-1l7-5c2-1 3-1 5 0l1 2v2c-1-1-1 0-1-1l-2 1c-1-1-2-1-2 0l-3 2h0c-1 0-1 1-2 1h-3z" class="K"></path><path d="M296 338h2 1c1 0 1 0 2-1h1 0l-3 3v1h2c1-1 1-1 2-1 2-1 4-1 6-1l1-1c2 0 5-1 6 1h1c1 0 2 1 3 1-2 0-2 0-4 1l-6 1h-1-1v1l10-1h0l-2 2c1 1 3-1 4 1h-1-1c-4 1-9 1-13 0-3-1-9-3-11-6l2-1z" class="M"></path><path d="M303 340c2-1 4-1 6-1-2 2-4 2-7 2h-1c1-1 1-1 2-1z" class="X"></path><path d="M308 343l10-1h0l-2 2c1 1 3-1 4 1h-1-1-6c-1-1-1-1-3-1h-2l1-1z" class="i"></path><defs><linearGradient id="Bd" x1="312.834" y1="327.409" x2="308.153" y2="339.423" xlink:href="#B"><stop offset="0" stop-color="#3c4045"></stop><stop offset="1" stop-color="#74757a"></stop></linearGradient></defs><path fill="url(#Bd)" d="M309 318c2 0 2 0 4 1 0 2 0 2 1 3 1 3 0 8 2 11h1l2-1v1c-1 0-2 1-2 3l-2-1v2c2 1 2 0 3 1 1 0 1 0 2 1h1 2l-1 1-6 1c2-1 2-1 4-1-1 0-2-1-3-1h-1c-1-2-4-1-6-1l-1 1c-2 0-4 0-6 1-1 0-1 0-2 1h-2v-1l3-3h0c1-1 1-2 2-3l2-3-1-1 1-2h2 1v-4c1-1 1-3 1-4-1-1-1-1-1-2z"></path><path d="M306 331c0 1 1 2 0 3v1l-2 3v1h0l-1 1c-1 0-1 0-2 1h-2v-1l3-3h0c1-1 1-2 2-3l2-3z" class="U"></path><path d="M317 336c1 0 2-1 4-2s4-1 6-1c3 4 6 7 8 12l1 1 1 1c1 1 1 2 1 3h0l-7-3c-1 0-2 0-3-1l-9-1h1c-1-2-3 0-4-1l2-2h0l-10 1v-1h1 1l6-1 6-1 1-1h-2-1c-1-1-1-1-2-1-1-1-1 0-3-1v-2l2 1z" class="V"></path><path d="M323 339c1-1 4-1 6 0l1 1h-8l1-1z" class="J"></path><path d="M323 343c2 0 4 0 6 1l3 1h-4c1 1 2 1 3 1v1c-1 0-2 0-3-1l-1-1-4-2z" class="e"></path><path d="M332 345c1 0 3 0 4 1l1 1c1 1 1 2 1 3h0l-7-3v-1c-1 0-2 0-3-1h4z" class="J"></path><path d="M318 342c2-1 4-1 6-1v1c-1 0-2 0-3 1h2l4 2 1 1-9-1h1c-1-2-3 0-4-1l2-2h0z" class="U"></path><path d="M699 318c2 1 4 1 5 3 1 1 1 2 1 3v4h0l5-3 1-2c1 2 2 3 3 4l3 4v1 1l1 3c-1 1-3 0-5 0-2 1-3 1-5 2-1 1-2 1-3 2l-1 1c1 1 0 1 1 1-2 1-3 1-5 2l-1-1-3 1-1 2-1 2c-1 2-1 3-1 4-4 6-4 10-4 17h1c-1 3-1 5-1 8 5-4 11-8 18-9l1-1h8c2 0 3 0 3 1l-1 1c1 0 2 1 2 1 0 1-2 3-2 5l-1 1c-1 1-2 0-3 0-1 2-1 3-3 4l-1 3c-1 0-2 2-2 3l1 1-1 3 1 1c-1 2-2 5-2 7h-1l-3 6-1 4h0l-3 8v1c1 1 0 2 0 3l1 1v3l2 3-3 3c1 1 1 1 2 1l1 1-2 2 1 2c0 1-1 1-2 2l3 1c1 1 1 2 1 3v1c1 3-2 7-2 9-2 6-5 12-7 18-2 4-4 9-6 14-2 2-2 3-3 6-1 7-6 14-8 21l-1 4s-1 2-2 2l-6 17-3 8-9 22-6 14-19 49v-1-2h0-1l1-3v-1l-1-1h-1v1h0c-2 0-3-1-4-1v1h-1v-2-1c-1 1-1 2-2 3h0c-2 5-1 8-4 13l-1-1v-2h0-1c0 1-1 2-2 2l3-6-2-1v-2h-1l-10 2v-1 1h-1c-2 1-2 1-4 1-1 0-2 1-3 1s-2 0-3-1c0-1-4-4-6-5-1 0-2 0-3-1 4-2 6 1 9-1l-3-1h-1c-2-1-2-2-3-3h-3l4-10c1 0 1-1 2-2l-2-1h-1c-1 0-2-1-2-2l-1 1-3-2-4-3 19-48c2-4 3-8 5-11l16-40v-2l6-12c0-3 2-6 3-9l6-13 1-1 20-53 6-14c0-1 1-2 1-3l4-9-1-3c1-1 1-2 1-3l1-1c0-2 1-4 2-6l1-2 1 3 1-3c2-1 2-3 4-4h6l1 1 5-12c2-3 3-7 4-10 3-7 6-15 8-22l2-1z" class="m"></path><path d="M656 446c0-2 0-3 1-4 1 1 1 2 3 3l-1 1s-1 1-1 2l-2-2z" class="h"></path><path d="M696 344l1-2c2-2 3-4 5-6 0 1-1 2-1 4h1c-1 1-1 1-2 3h-1l-3 1z" class="N"></path><path d="M660 445v-1c-1-2-3-5-2-8 1 2 2 5 4 6v1h0c-1 1-1 2-1 3h-2l1-1z" class="Z"></path><path d="M663 416v-1-3h0c3 2 5 5 8 7h-2c-2-1-2-3-6-3z" class="D"></path><path d="M657 523c1-3 2-7 4-10 1 1 1 2 1 4l-2 3c-1 2-1 2-3 3z" class="J"></path><path d="M665 472c1-1 3-2 5-2 2-1 3 0 4 1v2h-1-1l-2-1c-1-1-2 0-4 0v1l-1-1z" class="C"></path><path d="M656 446c-1-1-2-2-2-4-1-2-1-3-1-6h1l3 6c-1 1-1 2-1 4z" class="E"></path><path d="M633 516c-2 0-4-2-5-4v-1c-2-2-2-3-3-6h1c1 0 1 0 1 2 1 2 1 3 3 5s0 0 1 2l2 2z" class="I"></path><path d="M682 372c0-3 1-6 1-9 1 4 1 8 2 12v3l-1-1-2-5z" class="N"></path><path d="M639 514c3 2 6 5 10 7-2 0-2 0-3-1-2 1-2 1-3 2l-1-1c0-1-1-3-2-5-1-1-1-1-1-2z" class="O"></path><path d="M685 428l2 2-1 1v-1c-1 0-2 1-2 1 0 2 0 2-1 3v1c-1-1-2-2-3-2-2-2-4-3-5-4 2 0 4 2 6 3l2-2 2-2z" class="I"></path><path d="M643 522c1-1 1-1 3-2 1 1 1 1 3 1 2 1 3 1 4 3v1c-1 0-2-1-2-1l-1-1c-1 1-2 1-3 1-1-1-2-1-3-1l-1-1z" class="H"></path><path d="M684 418c1 1 2 2 2 4v1h1 0 4c-3 1-6 1-9 2l1 1c1 1 2 1 2 2l-2-1c-1-1-2-1-2-2l1-1h1v-2c0-2 1-3 1-4z" class="E"></path><path d="M635 518l1 1h1l-1-2 1-1h2 1c1 2 2 4 2 5-1 0-2 0-3-1l-1 1c-2 0-4-1-6-1h4l-1-2z" class="I"></path><path d="M666 472c2 0 3-1 4 0l2 1-1 1 1 1c0 1-1 1-2 2l-2 1c0-1-1-2-2-2-1-2 0-2-1-3v-1l1 1v-1z" class="E"></path><path d="M666 472c2 1 3 1 4 3v1c-2-1-3-1-4-3v-1z" class="W"></path><path d="M665 472l1 1c1 2 2 2 4 3v1l-2 1c0-1-1-2-2-2-1-2 0-2-1-3v-1z" class="R"></path><path d="M659 484c2 1 4 3 6 5v3h-1l-1-1c-1-1-1-1-2-1-1-2-2-2-4-3 0-1 1-2 2-3z" class="Q"></path><path d="M674 394c-1-7 0-11 3-16l1 11v1-1l-1-1-1-1c-1 1 0 2-1 4l-1-1v4z" class="N"></path><path d="M690 439c1 0 1 0 2-1 0 1 0 3-1 4 0 1-1 3-2 4l-2 5-2 2 5-14z" class="g"></path><path d="M663 416c4 0 4 2 6 3 1 1 2 2 3 2l1 1c0 2 2 3 4 4h-1c-1-1-2-1-4-2 0-1-1-1-2-1-1-1-3-2-4-3-1 1-1 1-1 2-2 0-3-1-5-2h1c1-1 2-1 4 0h1 0l-3-4z" class="I"></path><path d="M650 504l-1-8 1-1c2 2 2 4 3 6 0 3 0 5 1 8-1 0-3 0-4-1v-3-1z" class="D"></path><path d="M600 607l6-9c2-1 3-1 5-1h0c-4 4-4 9-4 14l-1-2c-1-2-2-2-3-3-2 0-2 1-3 2v-1z" class="R"></path><path d="M611 566c-1 0-1-2-1-2 0-2-1-4-1-5 1 0 2 0 2-1v-4c0-1 1-1 1-2 0 0 0-2 1-2l1-1h1l-1 7c-1 3 0 6-1 10v-2h-1c0 1 0 1-1 2z" class="a"></path><path d="M612 564c0-2-1-3 0-5h1v5h-1z" class="Q"></path><path d="M638 525c-1-1-1-1-2-1-3-1-5 1-7 2l-1 1h0l-2-9c2 2 3 2 6 2 2 0 4 1 6 1 0 0 1 1 2 1l-2 3z" class="B"></path><path d="M614 556h0l1-3v2l1-18v1l1 1c1 3 1 7 1 9v2h-1v-4h0c0 2-1 3-1 5v6c-1 5-1 11 0 16 0 2 1 4 1 6-3-3-3-10-4-13 1-4 0-7 1-10z" class="Z"></path><path d="M638 521l1-1c1 1 2 1 3 1l1 1 1 1c1 0 2 0 3 1 1 0 2 0 3-1l1 1s1 1 2 1v1c-1 1-1 1-2 1h-6l-1-1c-1 0-2 1-3 0h0l-1 1 1 1h0c-1 0-2-2-3-3h0l2-3c-1 0-2-1-2-1z" class="h"></path><path d="M640 522l1 1c0 1 0 1-1 2h-2 0l2-3z" class="c"></path><path d="M632 460c1-1 2-1 4-1h1 4l-5 12-1-1 1-4h-2 0c-2-2-2-3-3-5l1-1z" class="P"></path><path d="M665 422c0-1 0-1 1-2 1 1 3 2 4 3 1 0 2 0 2 1 2 1 3 1 4 2h1c-2-1-4-2-4-4l-1-1c2 1 2 2 4 2l2 2 1 1h1c0 1 1 1 2 1h1l2 1-2 2-2 2c-2-1-4-3-6-3-3-4-6-5-10-7z" class="Q"></path><path d="M698 417l1-1v1c1 1 0 2 0 3l1 1v3l-1-1c-1 2-2 5-3 7-1 3-2 5-3 9v2l-2 1c1-1 1-3 1-4-1 1-1 1-2 1l6-16 2-6z" class="M"></path><path d="M693 441v-2c1-4 2-6 3-9 1-2 2-5 3-7l1 1 2 3-3 3c1 1 1 1 2 1l1 1-2 2c0 1-1 2-3 2h0l-1 1-3 6v-2z" class="X"></path><path d="M699 430c1 1 1 1 2 1l1 1-2 2c0 1-1 2-3 2h0l-1 1c1-3 3-5 3-7z" class="L"></path><path d="M618 551c1-2 2-3 2-5 1-1 2-1 3-2l2 2c1 1 1 1 0 2v5 1c1-2 5-5 7-6l1-1 1-1c0-1 2-1 3-2h0 1l1-1h0c1 1 2 0 3 0-1 2-1 1-2 2-4 1-7 4-10 7l-6 5v-7c0-1-1-1-2-2l-4 3z" class="a"></path><path d="M613 564v2c1 3 1 10 4 13 1 0 1 1 1 1 2 5 7 9 7 14h1 0c0 2 0 3-1 4-2-2-3-5-5-8-1-4-3-8-5-11-1-5-2-9-4-13 1-1 1-1 1-2h1z" class="c"></path><path d="M662 443l4-10h1c1 2 0 5-1 7 0 2-3 7-2 9 0 3 3 6 4 9h-1c-3-3-6-6-9-10 0-1 1-2 1-2h2c0-1 0-2 1-3h0z" class="D"></path><path d="M687 373c1-1 2-2 2-4h1c-1 3-1 5-1 8-4 4-6 7-8 12l-1 1c0 1 0 2-1 3v1l-1-1v-3-1c1-1 1-4 2-6 1-4 0-8 2-11l2 5 1 1v-3l2-2z" class="I"></path><path d="M711 323c1 2 2 3 3 4l3 4v1 1l1 3c-1 1-3 0-5 0-2 1-3 1-5 2-1 1-2 1-3 2l-1 1c1 1 0 1 1 1-2 1-3 1-5 2l-1-1h1c1-2 1-2 2-3h-1c0-2 1-3 1-4l1-1c2-1 3 0 5 0v-1c1-2 1-2 1-3l-1-1h1c2 1 4 3 6 5v-2c-2-3-2-6-5-8l1-2z" class="B"></path><path d="M700 343l4-4c1 0 1 0 2-1h1c0-1 1-1 1-1 1-1 1-1 1-2 1 0 3 0 4 1-2 1-3 1-5 2-1 1-2 1-3 2l-1 1c1 1 0 1 1 1-2 1-3 1-5 2l-1-1h1z" class="S"></path><path d="M650 504v1 3c1 1 3 1 4 1 0 2 4 4 4 6l-2 1c-1 0-1 0-2-1-2 0-3 2-4 3l-8-5c-1-2-2-2-2-4 3-2 7 0 10-1v-4zm-9-34c3-3 7-6 11-7h1c2-1 8-1 10 0s3-1 5 0c-1 1-2 2-4 2l-7 5v-1c0 1 0 1-1 1-2 0-3 0-4-1l-5 1v-1c-2 0-4 1-6 1z" class="G"></path><path d="M651 466c-1 0-2 1-4 0h2c1-1 2-1 3-1 2-1 4-2 7-1l1 1-4 1h-1l1-1 1-1c-1 0-2 0-2 1-2 0-2 1-4 1z" class="a"></path><path d="M651 466c2 0 2-1 4-1 0-1 1-1 2-1l-1 1-1 1h1l4-1h3 1l-7 5v-1c0 1 0 1-1 1-2 0-3 0-4-1l-5 1v-1c0-1 0-1 1-1l3-1v-1z" class="K"></path><path d="M651 467h3l-2 2-5 1v-1c0-1 0-1 1-1l3-1z" class="a"></path><defs><linearGradient id="Be" x1="656.488" y1="481.335" x2="654.994" y2="469.016" xlink:href="#B"><stop offset="0" stop-color="#2b2d2e"></stop><stop offset="1" stop-color="#4c5055"></stop></linearGradient></defs><path fill="url(#Be)" d="M652 469c1 1 2 1 4 1 1 0 1 0 1-1v1l8 3c1 1 0 1 1 3 1 0 2 1 2 2l1 1-2 2-3-2-2-2c-4-3-9-5-14-4l-9 2v-1c0-2 1-2 2-4 2 0 4-1 6-1v1l5-1z"></path><path d="M647 469v1c-1 0-3 1-4 1-1 1-2 3-4 3 0-2 1-2 2-4 2 0 4-1 6-1z" class="E"></path><path d="M668 458h1 1v-6c0-4 0-12 2-16h2l1 2c2 1 2 2 4 3 0 1 0 1-1 2-1 3-2 7-3 10-1 2-2 4-2 6h-4l-2-1h1z" class="G"></path><path d="M675 438c2 1 2 2 4 3-2 1-1 2-2 3-1-2-2-4-2-6z" class="Z"></path><path d="M675 391c1-2 0-3 1-4l1 1 1 1v1 3l1 1v-1c1-1 1-2 1-3l1-1v1 1c0 2-1 4-2 7l-3 15c-1 2-1 4-1 6 0 1-1 1-1 1l2 2h0l5 3c0 1 1 1 2 2h-1c-1 0-2 0-2-1h-1l-1-1-2-2c-2 0-2-1-4-2-1 0-2-1-3-2h2 1c0-4 2-9 2-14v-11-4l1 1z" class="B"></path><path d="M675 391c1-2 0-3 1-4l1 1c-1 4-1 8-1 11-1-2-1-6-1-8z" class="c"></path><path d="M687 373c-1-5-3-11-1-15l13-33 1-4 1-1h1v1c-1 2-1 4-2 6l-3 12c-1 2-2 4-2 6v1l-1 2c-1 2-1 3-1 4-4 6-4 10-4 17 0 2-1 3-2 4z" class="Q"></path><path d="M600 608c1-1 1-2 3-2 1 1 2 1 3 3l1 2h0c0 1 0 1 1 2-1 1-2 1-3 2-3 2-6 5-9 7l-1-1-2-2-3-1h-1c-2-1-2-2-3-3l1-1c4-3 9-5 13-7v1z" class="E"></path><path d="M586 615l1-1 2 2h5c-2 1-3 0-4 2h-1c-2-1-2-2-3-3z" class="h"></path><path d="M600 608c1-1 1-2 3-2 1 1 2 1 3 3l1 2h0c0 1 0 1 1 2-1 1-2 1-3 2-3 2-6 5-9 7l-1-1-2-2-3-1c1-2 2-1 4-2l2 3c2-1 3-1 4-2 3-2 4-4 5-7-1-1-3-2-5-2z" class="c"></path><path d="M673 459v1c3-5 3-11 6-16l1-1v-1c1-1 2 0 3-1-2-2-7-5-9-6l-1-1c-1 0-2-1-3-2l-1-1-5-3h1c1 0 2 1 3 1l1 1h1c-2-2-4-2-5-3v-1h2c1 1 0 1 1 1 2 0 3 2 4 3 2 1 4 2 6 4 1 0 1 0 2 1l-1 1v-1h-2c2 1 4 3 6 4 1 0 1 1 2 2 0 2-1 4-2 5-2 3-4 6-5 10-1 3-2 5-4 7v3l-1 1h0c-2-1-3 0-4 0l-2 2-1-1 1-1c1-1 2-1 3-2 0-2-1-3 0-3l-1-1-13-3h-1c3-1 7 0 10 1 1 0 3 1 4 0h4z" class="Z"></path><path d="M637 491v-1h0-1l-5 5 4-9c0-2 3-4 4-5 3-1 3-2 6-2h0 1l1-1c2 1 4 0 6 1v2l2 1 4 2c-1 1-2 2-2 3 2 1 3 1 4 3 1 0 1 0 2 1l1 1h1v1c-2 0-2-1-3-2l1 4c1 3 0 7-1 10h-1 0v-1-1c1-1 1-3 1-5l-1-1c0-1 0-2 1-2 0-1-2-3-2-3h0c0 3-1 3-3 5-1-1 0-1 0-2l2-1v-4c-2-1-4-2-5-3-5-1-10 0-13 3l-4 1z" class="a"></path><path d="M653 479v2c-3 0-5 0-8 1h0c-1 0-1 1-1 1-1 1-2 1-3 2v1l-2 1h-1c0-2 1-3 2-4h1c4-4 6-3 12-4z" class="h"></path><path d="M653 481l2 1 4 2c-1 1-2 2-2 3-2-1-3-1-5-1-4-1-7-1-11 0v-1c1-1 2-1 3-2 0 0 0-1 1-1h0c3-1 5-1 8-1z" class="H"></path><path d="M655 482l4 2c-1 1-2 2-2 3-2-1-3-1-5-1l1-2 2-2z" class="B"></path><defs><linearGradient id="Bf" x1="615.271" y1="551.291" x2="631.179" y2="589.644" xlink:href="#B"><stop offset="0" stop-color="#282b2f"></stop><stop offset="1" stop-color="#47484b"></stop></linearGradient></defs><path fill="url(#Bf)" d="M618 551l4-3c1 1 2 1 2 2v7c-3 10-2 17 1 27 1 4 2 6 1 10h0-1c0-5-5-9-7-14 0 0 0-1-1-1 0-2-1-4-1-6-1-5-1-11 0-16h1 2 1v-1c-1 0-2-1-3-1l1-1v-3z"></path><defs><linearGradient id="Bg" x1="671.348" y1="361.263" x2="670.642" y2="384.051" xlink:href="#B"><stop offset="0" stop-color="#8b8b8d"></stop><stop offset="1" stop-color="#b9b9ba"></stop></linearGradient></defs><path fill="url(#Bg)" d="M669 366c2-1 2-3 4-4h6l1 1-8 20c-1 2-2 5-3 7 0-1-1-1 0-2 0-1 0-2 1-3v-1h-7l-4 9h-1c0-1 1-2 1-3l4-9-1-3c1-1 1-2 1-3l1-1c0-2 1-4 2-6l1-2 1 3 1-3z"></path><path d="M667 366l1 3-5 12-1-3c1-1 1-2 1-3l1-1c0-2 1-4 2-6l1-2z" class="G"></path><defs><linearGradient id="Bh" x1="629.27" y1="503.804" x2="659.477" y2="498.641" xlink:href="#B"><stop offset="0" stop-color="#222224"></stop><stop offset="1" stop-color="#404247"></stop></linearGradient></defs><path fill="url(#Bh)" d="M637 491l4-1c3-3 8-4 13-3 1 1 3 2 5 3v4l-1-3c-1 1-2 3-3 5 0 1 0 3 1 3l1 1h-1c-3-3-3-8-9-8-2 0-5 1-7 3-3 2-4 6-4 10 0 3 1 6 3 9h0c0 1 0 1 1 2h-1-2l-1 1 1 2h-1l-1-1-2-2-2-2c-1-2 1 0-1-2s-2-3-3-5c1 1 3 2 4 3v-9h0c1-4 3-7 6-10z"></path><path d="M674 473h1c1-1 1-1 1-3 2-2 4-6 4-9l1-1c0-2 1-2 2-3v1l-9 23c1 3 0 6-1 9-1 1 0 3 0 4l-5 14-1 1 1 4h-3l-1-1h-2l2-7v-6c1-2 2-3 3-5l3-9c-1-1-3-3-4-3 1 0 1 1 1 2h-1l-2-1-8-3c-1-1-2-2-4-2h-1-4-5l2-2h1l1-1h2l1-1h1 0 2 1l4 2h1l5 3h1l3 2 2-2-1-1 2-1c1-1 2-1 2-2l-1-1 1-1h1 1z" class="I"></path><path d="M667 481l2-2v1c0 2 2 3 3 5l-5-4z" class="F"></path><path d="M670 477c1-1 2-1 2-2l-1-1 1-1h1c1 2 1 2 1 4-1 1-1 2-2 3-1-1-1-2-2-3z" class="W"></path><path d="M650 477h-4v-1h2c2-1 2-1 3-1l6 1h1c-3 2-5 1-8 1z" class="c"></path><path d="M658 476l5 3-1 1c-2 0-3 0-5-1-3-2-4-2-7-2 3 0 5 1 8-1z" class="Q"></path><path d="M673 485c0-1 1-3 1-4 1 3 0 6-1 9-1 1 0 3 0 4l-5 14-1 1 1 4h-3l-1-1h-2l2-7 4-10c2-3 3-7 5-10z" class="g"></path><path d="M668 495c2-3 3-7 5-10 0 2-2 5-2 7-1 2-1 4-1 7-1 2-2 4-2 6l-1-1c-1-3 2-5 1-9z" class="M"></path><path d="M668 495c1 4-2 6-1 9l1 1-1 3v1l1 4h-3l-1-1h-2l2-7 4-10z" class="U"></path><path d="M611 597l10-1c-1 5-1 10-2 15h0c0 3 0 4-1 5v2 1c1-2 1-4 2-5l1 2-4 10-2-1v-2h-1l-10 2v-1 1h-1c-2 1-2 1-4 1-1 0-2 1-3 1s-2 0-3-1c0-1-4-4-6-5-1 0-2 0-3-1 4-2 6 1 9-1l2 2 1 1c3-2 6-5 9-7 1-1 2-1 3-2-1-1-1-1-1-2h0c0-5 0-10 4-14h0z" class="h"></path><path d="M614 623l3-1v-4c-1-1 1-6 2-7h0c0 3 0 4-1 5v2 1c1-2 1-4 2-5l1 2-4 10-2-1v-2h-1zm-3-26l1 1v10 1c-1-2-1-4-1-6l1-4h-2c-1 3-2 5-2 9v2h1 1v1l-1-1-1 1c1 1 1 1 1 2s1 1 1 3v1c-3 3-7 5-11 8l-3 1v-1l-3 1c0-1-4-4-6-5-1 0-2 0-3-1 4-2 6 1 9-1l2 2 1 1c3-2 6-5 9-7 1-1 2-1 3-2-1-1-1-1-1-2h0c0-5 0-10 4-14z" class="I"></path><path d="M593 619l2 2 1 1c-1 0-3 1-4 0-2 0-3-1-5-1-1 0-2 0-3-1 4-2 6 1 9-1z" class="h"></path><path d="M662 512h2l1 1h3c2 0 3 0 4 1l4 1s-1 2-2 2l-6 17-3 8-9 22c-1-2-2-4-2-7-1-3-1-5-2-8 0-1 1-2 1-3v-3h0c1-3 0-4-1-6l5-14c2-1 2-1 3-3l2-3c0-2 0-3-1-4l1-1z" class="T"></path><path d="M668 519c2 0 3-1 5-2-1 2-1 4-3 5-2 0-3 0-5-1l3-2z" class="F"></path><path d="M662 512h2l1 1h3c2 0 3 0 4 1l4 1s-1 2-2 2h-1c-2 1-3 2-5 2l-3 2-1-1v-1c-1 0-1 0-2 1h-2l2-3c0-2 0-3-1-4l1-1z" class="g"></path><path d="M664 519h0c-1-2 0-3 0-5h2v1 2c0 1 1 2 2 2l-3 2-1-1v-1z" class="D"></path><path d="M660 520h2c1-1 1-1 2-1v1c-2 1-4 1-5 3s-3 6-2 8c0 3 4 5 5 7h0c1 1 1 2 1 2v1l1 1v1l-1 1h0v-2c-1 1-1 0-1 1h-2c1 1 1 1 0 2 0 1 0 1-1 1h-2l-2-2 1-2c-1 0-2 1-3 1h0c1-3 0-4-1-6l5-14c2-1 2-1 3-3z" class="M"></path><path d="M662 538c1 1 2 2 3 4l-9 22c-1-2-2-4-2-7-1-3-1-5-2-8 0-1 1-2 1-3v-3c1 0 2-1 3-1l-1 2 2 2h2c1 0 1 0 1-1 1-1 1-1 0-2h2c0-1 0 0 1-1v2h0l1-1v-1l-1-1v-1s0-1-1-2h0z" class="U"></path><path d="M653 543c1 0 2-1 3-1l-1 2 2 2v1c-1 0-2 0-3 1h-1c0 2 0 4 1 6v3c-1-3-1-5-2-8 0-1 1-2 1-3v-3z" class="E"></path><path d="M657 546h2c1 0 1 0 1-1 1-1 1-1 0-2h2c0 3-1 3-2 6h-4c-1-1-1-1-2-1 1-1 2-1 3-1v-1z" class="C"></path><defs><linearGradient id="Bi" x1="657.091" y1="382.143" x2="645.839" y2="460.347" xlink:href="#B"><stop offset="0" stop-color="#cac9c9"></stop><stop offset="1" stop-color="#efefef"></stop></linearGradient></defs><path fill="url(#Bi)" d="M658 393h1l4-9h7v1c-1 1-1 2-1 3-1 1 0 1 0 2l-28 69h-4-1c-2 0-3 0-4 1l20-53 6-14z"></path><path d="M651 537h1c1 2 2 3 1 6h0v3c0 1-1 2-1 3l-1-2-1 1h-1c-2 3-3 9-6 12h0l-3 4-1 3c-2-2-3-5-5-7 0 0-1-1-1-2l-1 1c2 2 3 5 4 8 1 1 1 2 1 3v4c-2 5-4 11-6 15s-3 7-5 10l-1-1c1-1 1-2 1-4 1-4 0-6-1-10-3-10-4-17-1-27l6-5c3-3 6-6 10-7 1-1 1 0 2-2h0c1 0 2-1 4 0h1s1-1 2-1v-2c1-1 1-2 2-3z" class="W"></path><path d="M651 537h1c1 2 2 3 1 6h0v3c0 1-1 2-1 3l-1-2-1 1h-1c-2 3-3 9-6 12h0l3-7 5-16z" class="J"></path><path d="M640 562c-1-2-8-8-8-9l6-3 3 3h1 4l-3 7-3 4v-2z" class="AG"></path><path d="M646 553l-3 7-3 4v-2-1c1-1 1-3 1-4s1-2 1-4h4z" class="I"></path><path d="M696 437l1-1h0c2 0 3-1 3-2l1 2c0 1-1 1-2 2l3 1c1 1 1 2 1 3v1c1 3-2 7-2 9-2 6-5 12-7 18-2 4-4 9-6 14-2 2-2 3-3 6-1 7-6 14-8 21l-1 4-4-1c-1-1-2-1-4-1l-1-4 1-1 5-14c0-1-1-3 0-4 1-3 2-6 1-9l9-23v-1c1-1 1-2 2-4l2-2 2-5c1-1 2-3 2-4l2-1v2l3-6z" class="L"></path><path d="M685 479v-4-2l1 1c0 1 0 1 1 2v2 3l-2-2z" class="P"></path><path d="M685 453l2-2 1 2-3 5h-2v-1c1-1 1-2 2-4z" class="M"></path><path d="M693 441v2c-1 1-1 1-1 2v2 2c0 1 1 1 0 3-1-1-1-2-1-4-2 2-2 4-3 5l-1-2 2-5c1-1 2-3 2-4l2-1z" class="i"></path><path d="M679 480s1-1 2-1c2-1 3 0 4 0l2 2v3l-2 2-2-1c-1-2 1-2-1-4 0-1-1-1-1-1l-1 1-1-1z" class="C"></path><path d="M679 480l1 1 1-1s1 0 1 1c2 2 0 2 1 4l2 1c-1 1-1 2-2 2-1 1-2 1-3 0-1 0-3-2-3-4s1-3 2-4z" class="D"></path><path d="M703 443h0c1 3-2 7-2 9l-4 1h-1c-3-2-5 8-7 9h-1c-1-1-1 0-1-1v-3c1-1 1-2 2-4 0-1 0-2 1-3 0 0 1 1 2 1h0c1-2 0-2 0-3 1 1 2 2 3 2 2 1 3 1 4 0 2-2 3-4 3-7l1-1z" class="d"></path><path d="M683 458h2l-8 23c-1 2-2 3-1 5v1 3h0c-1 1-2 3-3 4 0-1-1-3 0-4 1-3 2-6 1-9l9-23z" class="i"></path><path d="M696 437l1-1h0c2 0 3-1 3-2l1 2c0 1-1 1-2 2l3 1c1 1 1 2 1 3v1h0l-1 1c0 3-1 5-3 7-1 1-2 1-4 0-1 0-2-1-3-2v-2-2c0-1 0-1 1-2l3-6z" class="Y"></path><path d="M702 441c-1 0-1-1-2-1-1-1-1 0-2-1l1-1h0l3 1c1 1 1 2 1 3v1h0-1v-2z" class="p"></path><path d="M702 441v2h1l-1 1h0-3l1 2v1c-1 1-1 1-3 2l-1-1v-3l1-2 1 1h1v-1h0c-1-1-1-1-2-1h-1l1-1c1 1 2 1 4 0h1z" class="n"></path><path d="M692 445l1-1c1-1 2-1 3-1v2 3l1 1c2-1 2-1 3-2v-1l-1-2h3 0c0 3-1 5-3 7-1 1-2 1-4 0-1 0-2-1-3-2v-2-2z" class="I"></path><path d="M676 486c1 1 2 3 3 3 1 1 1 0 3 1l-2 2h-1v-1l-1-1c0 1-1 1-1 2-1 1-1 1-1 2l-1 2c-2 3-3 8-5 11h0c2 1 3 2 5 3 1-1 1-2 2-3v-1c1-1 0-1 1 0v-2h-1l1-1c1-2 1-3 2-5v-1c0-1 1-2 2-3s1-2 2-3v-2l1 1c-1 7-6 14-8 21l-1 4-4-1c-1-1-2-1-4-1l-1-4 1-1 5-14c1-1 2-3 3-4h0v-3-1z" class="P"></path><path d="M667 509l1-1c2 2 3 3 7 4 0 0 1 0 2-1l-1 4-4-1c-1-1-2-1-4-1l-1-4z" class="T"></path><path d="M650 548l1-1 1 2c1 3 1 5 2 8 0 3 1 5 2 7l-6 14-19 49v-1-2h0-1l1-3v-1l-1-1h-1v1h0c-2 0-3-1-4-1v1h-1v-2-1c-1 1-1 2-2 3h0c-2 5-1 8-4 13l-1-1v-2h0-1c0 1-1 2-2 2l3-6 4-10-1-2c1-1 2-3 2-4l4-11c2-3 3-6 5-10s4-10 6-15v-4c0-1 0-2-1-3-1-3-2-6-4-8l1-1c0 1 1 2 1 2 2 2 3 5 5 7l1-3 3-4h0c3-3 4-9 6-12h1z" class="S"></path><path d="M641 569v3c1 0 1 1 2 1h-1l1 1v1c1 0 2 1 3 2v1 1 3l-3 5v1c-2 1-1 1-2 0h-5c0-3 0-7 1-9 2-4 3-7 4-10z" class="g"></path><path d="M643 587c-1-1-3-1-4-2-1-2-1-3-1-4l1-1c0 2 0 2 1 3 2 0 2 0 4-1v-3h0 2v3l-3 5z" class="F"></path><path d="M650 548l1-1 1 2c1 3 1 5 2 8 0 3 1 5 2 7l-6 14c-1 0-1-1-1-3-1 3-1 5-3 7v-3-1-1c-1-1-2-2-3-2v-1l-1-1h1c-1 0-1-1-2-1v-3c1-4 2-7 4-10 0-1 1-3 2-4 1-2 2-5 3-7z" class="T"></path><path d="M641 569c1-4 2-7 4-10v3c0 2-4 8-1 10h1c0 1-1 1-2 2l-1-1h1c-1 0-1-1-2-1v-3z" class="e"></path><path d="M645 572h4v1 2c-1 3-1 5-3 7v-3-1-1c-1-1-2-2-3-2v-1c1-1 2-1 2-2z" class="J"></path><defs><linearGradient id="Bj" x1="623.411" y1="616.375" x2="651.572" y2="584.118" xlink:href="#B"><stop offset="0" stop-color="#606166"></stop><stop offset="1" stop-color="#797c80"></stop></linearGradient></defs><path fill="url(#Bj)" d="M649 575c0 2 0 3 1 3l-19 49v-1-2h0-1l1-3v-1l-1-1h-1v1h0c-2 0-3-1-4-1v1h-1v-2-1c-1 1-1 2-2 3h0c-2 5-1 8-4 13l-1-1v-2h0-1c0 1-1 2-2 2l3-6 4-10v-1c2 0 4-7 5-10 2-6 5-12 9-18v11h1l1-1c-1-2-1-4-1-6v-3h5c1 1 0 1 2 0v-1l3-5c2-2 2-4 3-7z"></path><path d="M636 591c1 1 2 2 2 3s0 2-1 3c-1-2-1-4-1-6z" class="e"></path><path d="M621 616v-1c2 0 4-7 5-10l-1 5v2l1 1c1-1 1 0 1-1 2-2 3-4 5-5l1 1c-1 1-4 3-5 4 0 2 0 2-1 3l1 2c-1 0-1 0-1 1h-1c-1 0 0 0-1 1v1h-1v-2-1c-1 1-1 2-2 3h0c-2 5-1 8-4 13l-1-1v-2h0-1c0 1-1 2-2 2l3-6 4-10z" class="V"></path><path d="M631 461c1 2 1 3 3 5h0 2l-1 4 1 1-4 8-1 3-30 74-11 27c-2 6-5 11-6 17l-1 1-3-2-4-3 19-48c2-4 3-8 5-11l16-40v-2l6-12c0-3 2-6 3-9l6-13z" class="o"></path><path d="M631 461c1 2 1 3 3 5h0 2l-1 4 1 1-4 8-1 3c-1 0-2-1-3-1l-1 1v-2h0c-1 2-1 3-1 5l1-1v1l-1 1-1-1c0-1-1-4 0-5 0-1 0-1 1-2v-3c-1 3-2 7-4 8 0-3 2-6 3-9l6-13z" class="l"></path><path d="M632 479h-1c-1 0-2-1-4-1v-5c1-1 1-3 2-4l6 1 1 1-4 8z" class="f"></path><defs><linearGradient id="Bk" x1="694.409" y1="405.499" x2="695.459" y2="381.517" xlink:href="#B"><stop offset="0" stop-color="#464549"></stop><stop offset="1" stop-color="#6e7175"></stop></linearGradient></defs><path fill="url(#Bk)" d="M689 377c5-4 11-8 18-9l1-1h8c2 0 3 0 3 1l-1 1c1 0 2 1 2 1 0 1-2 3-2 5l-1 1c-1 1-2 0-3 0-1 2-1 3-3 4l-1 3c-1 0-2 2-2 3l1 1-1 3 1 1c-1 2-2 5-2 7h-1l-3 6-1 4h0l-3 8-1 1-2 6-1-1c-2 1-2 1-4 1h-4 0-1v-1c0-2-1-3-2-4 0 1-1 2-1 4v2h-1l-1 1-5-3h0l-2-2s1 0 1-1c0-2 0-4 1-6l3-15c1-3 2-5 2-7v-1-1c2-5 4-8 8-12z"></path><path d="M691 391c4-3 6-6 9-8l6-3c2 1 3 0 5 0l-1 3c-2-1-4-1-6-1l-4 4c-3 2-6 4-9 7l-1-1 1-1z" class="V"></path><path d="M708 386l1 1-1 3c-1 1-3 4-4 4-2 3-5 4-8 6-2 1-5 1-5 2h-1 0c2-4 6-8 9-10 1-1 1-1 2-1 1-2 2-2 3-2l4-3z" class="E"></path><path d="M708 386l1 1-1 3c-1 1-3 4-4 4s-3 1-4 1l1-1h-1c1-2 3-3 4-5h0l4-3z" class="W"></path><path d="M708 390l1 1c-1 2-2 5-2 7h-1l-3 6s-1-1-2-1c-1 1-3 2-4 2s-3 2-5 3l-1 1-1-2 2-2h-1l2-1-2-2c0-1 3-1 5-2 3-2 6-3 8-6 1 0 3-3 4-4z" class="B"></path><path d="M693 404c3-1 6-2 7-4 3-2 3-2 6-2l-3 6s-1-1-2-1c-1 1-3 2-4 2s-3 2-5 3l-1 1-1-2 2-2h-1l2-1z" class="D"></path><path d="M692 405h3l6-3v1c-1 1-3 2-4 2s-3 2-5 3l-1 1-1-2 2-2z" class="B"></path><path d="M691 402l2 2-2 1h1l-2 2 1 2c-2 2-4 4-6 7l-1 2c0 1-1 2-1 4v2h-1l-1 1-5-3h0c1-7 2-13 4-19h0c2 1 2 2 3 3v1h0 0c2-1 3-2 4-2l3-3h1z" class="Q"></path><path d="M676 422h1c1 0 2-1 2-2l3 4-1 1-5-3z" class="I"></path><path d="M691 402l2 2-2 1c-2 1-4 2-7 4h0 0l1-2 2-2 3-3h1z" class="O"></path><path d="M690 407l1 2c-2 2-4 4-6 7h-2c0-3 1-5 3-7 1 0 3-1 4-2z" class="H"></path><path d="M701 403c1 0 2 1 2 1l-1 4h0l-3 8-1 1-2 6-1-1c-2 1-2 1-4 1h-4 0-1v-1c0-2-1-3-2-4l1-2c2-3 4-5 6-7l1-1c2-1 4-3 5-3s3-1 4-2z" class="I"></path><path d="M685 416c1 0 2 1 3 2v2c1 1 1 1 3 1v1h-5c0-2-1-3-2-4l1-2z"></path><path d="M692 408c1 0 3-1 4 0v2 1-1l-1-1v2l-1 1v-1h-2c-2 2-3 4-4 7-1-1-2-2-3-2 2-3 4-5 6-7l1-1z" class="W"></path><path d="M701 403c1 0 2 1 2 1l-1 4h0l-3 8-1 1-2 6-1-1c0-1-2-2-1-3v-4-4 1l1-1v-2l1 1v1-1-2c-1-1-3 0-4 0 2-1 4-3 5-3s3-1 4-2z" class="C"></path><path d="M694 419c0-2 1-3 2-4 2 0 2 1 2 2l-2 6-1-1c0-1-2-2-1-3z" class="W"></path><path d="M701 403c1 0 2 1 2 1l-1 4h0c-1 1-2 1-2 2v2-3h0c-1 1-1 3-2 4-2 0-2 0-3-1h-1l1-1v-2l1 1v1-1-2c-1-1-3 0-4 0 2-1 4-3 5-3s3-1 4-2z" class="Z"></path><path d="M689 377c5-4 11-8 18-9l1-1h8c2 0 3 0 3 1l-1 1c1 0 2 1 2 1 0 1-2 3-2 5l-1 1c-1 1-2 0-3 0-1 2-1 3-3 4-2 0-3 1-5 0l-6 3c-3 2-5 5-9 8h-1l-2-1c-4 4-6 8-8 13-2 6-3 12-4 19l-2-2s1 0 1-1c0-2 0-4 1-6l3-15c1-3 2-5 2-7v-1-1c2-5 4-8 8-12z" class="W"></path><path d="M706 376h8c-1 2-1 3-3 4-2 0-3 1-5 0l-6 3c-3 2-5 5-9 8h-1l-2-1c5-6 11-10 18-14z" class="J"></path><path d="M706 376h8c-1 2-1 3-3 4-2 0-3 1-5 0l3-3v-1h-3z" class="B"></path><path d="M689 377c5-4 11-8 18-9l1-1h8c2 0 3 0 3 1l-1 1c-11-1-19 3-28 10-3 3-6 7-9 11v-1c2-5 4-8 8-12z" class="P"></path></svg>