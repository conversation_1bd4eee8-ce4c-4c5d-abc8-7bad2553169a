<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="345 96 404 776"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#e3e2e2}.C{fill:#ecebec}.D{fill:#f4f3f3}.E{fill:#dbd9da}.F{fill:#2a2929}.G{fill:#484848}.H{fill:#272627}.I{fill:#bbbaba}.J{fill:#111011}.K{fill:#1c1b1c}.L{fill:#3a3939}.M{fill:#505050}.N{fill:#686768}.O{fill:#8a8989}.P{fill:#7b7a7b}.Q{fill:#9a9898}.R{fill:#c1c0c0}.S{fill:#6b696a}.T{fill:#a8a6a6}</style><path d="M381 474h2l1 1-1 2h-1l-1-1v-2z" class="B"></path><path d="M511 884h2 0c0 2 0 2-1 3-1 0-1-1-2-1l1-2z" class="F"></path><path d="M580 346c1-1 1-1 2 0v1l-1 2-2-2 1-1zm-204-9h3v1 2h-2l-1-1v-2z" class="D"></path><path d="M388 312h2c0 2 0 2-1 3-1 0-1 0-2-1v-1l1-1z" class="B"></path><path d="M598 766h2l1 2-1 1h-1c-1 0-1 0-2-1l1-2z" class="D"></path><path d="M606 432h1l2 2-2 2c-1 0-1 0-2-1v-2l1-1z" class="B"></path><path d="M417 166h2l1 2-1 1-1 1-1-1-1-1 1-2z" class="Q"></path><path d="M561 728c1 0 1 0 2 1v2h-1c-1 0-2 0-2-1l-1-1 2-1zM383 290h1c1 0 1 0 1 2l-1 1h-1l-1-1v-1l1-1z" class="B"></path><path d="M599 786h0c1 1 2 1 2 3l-1 1c-2 0-2 0-3-1v-2l2-1z" class="D"></path><path d="M597 650h0c2 1 2 1 2 3l-1 1c-2 0-2 0-3-1v-2l2-1z" class="B"></path><path d="M367 682h3v2 1l-2 1c-1-1-1-1-1-2v-2z" class="D"></path><path d="M388 453h0c2 1 2 1 2 3l-1 1c-2 0-2 0-3-1v-1l2-2z" class="B"></path><path d="M427 820c2 0 2-1 3 0 1 2 0 2 0 3-2 0-2 0-3-1l-1-1 1-1z" class="H"></path><path d="M531 114h2l1 1c0 1 0 1-1 2 0 1-1 1-1 1l-2-1c0-1 0-1 1-3z" class="B"></path><path d="M423 354l5-2c-1 2-3 4-3 6v1c-1 1-1 2-1 3l-1-8z" class="R"></path><path d="M598 344c1 0 1 0 2 1v1l-1 2h-1l-1-1c0-2-1-2 1-3z" class="B"></path><path d="M598 299h1l1 4v2c-2 0-2 0-3-1 0-2 1-4 1-5z" class="C"></path><path d="M553 655c1 2 2 3 3 5-1 2-2 2-4 3v-1l1-1c1-2 0-4 0-6z" class="L"></path><path d="M579 435h1c1 0 1 0 2 1 0 1 0 1-1 3h-1c-2-1-2-1-2-3l1-1zm-1 252h2c1 1 1 2 1 3l-2 1c-1 0-1 0-2-1v-3h1zM391 533h1c1 2 1 1 1 3l-1 2h-1c-1 0-1-1-2-2 0-2 0-1 2-3z" class="B"></path><path d="M425 283l-1-1c-1-2 0-5 0-7l4 5v1c-1 1-2 1-3 2z" class="D"></path><path d="M445 200h0l-2 7h0-2v-2l-1-2 5-3z" class="Q"></path><path d="M547 162c1 0 1-1 2-1l1 2v2l-1 1h0c-2 0-2 0-3-1 0-2 0-2 1-3z" class="B"></path><path d="M383 574l1-1c1 0 2 1 3 2 0 1 0 1-1 3h-1c-1 0-2 0-3-1 0-2 0-2 1-3z" class="H"></path><path d="M535 727h2c1 1 1 1 1 3l-2 1-1 1c-1-1-2-1-2-3l2-2z" class="B"></path><path d="M475 381h1c1-1 1-1 2-1 2 1 2 1 2 3 1 1 0 2 0 3h-1c-1-2-3-3-4-5z" class="C"></path><path d="M516 215c1 1 1 3 1 5-1 1-2 3-4 4h0v-1-6s2 0 2-1l1-1z" class="K"></path><path d="M513 523c-3-1-6-1-8-1h-1l1-1h6 1 3 2c-1 1-3 1-4 1h4 1c-2 2-3 1-5 1h0z" class="J"></path><path d="M479 107h2c1 1 1 1 2 3-1 1-1 1-3 2h-1c-1-1-1-1-2-3l2-2z" class="C"></path><path d="M689 281l-6 2v-7h2c0 2 3 3 4 5z" class="E"></path><path d="M635 190h2c0 1 1 1 1 2 0 2 0 2-2 3h-1c-2-1-2-1-2-3 0-1 0-1 2-2z" class="O"></path><path d="M692 252h-5v1l-1-1c1-2 1-5 3-6 1 0 1 1 2 1 0 2 0 3 1 4v1z" class="B"></path><path d="M611 202c1 2 3 3 5 4h0c0 1 0 2-1 3 0 1-1 1-1 2l-1-1-2-1-1-1c0-1 1-2 1-2v-4z" class="F"></path><path d="M513 620h2l2-1c1-1 2-1 2-2l3 2-1 3c-2 1-4 0-7 0l1-1-2-1z" class="M"></path><path d="M509 332v-1-3c2-1 2 0 3 0 1 1 1 4 1 6v1l-2 1h-1c-1-1-1-3-1-4zm182-85c2 1 6 1 8 3l-1 2c-3 1-3 1-6 0v-1c-1-1-1-2-1-4z" class="D"></path><path d="M684 285h0c1 1 2 1 3 1 2 0 4 1 5 2h0l-8 3v-6z" class="B"></path><path d="M504 88h12l-4 3c-1 1-2 3-3 4-1-3-2-5-5-7h0z" class="N"></path><path d="M647 547c1 0 1 0 2 1s2 2 1 3c0 2-1 2-2 3l-2-1c-2-1-2-1-2-3l3-3z" class="M"></path><path d="M689 299c-1 0-2 1-3 1l-1-6c2-2 4-3 6-4 0 2-1 4-2 5s-1 1-1 2 1 1 1 2z" class="E"></path><path d="M509 209v-2c-1-1-2-1-2-3 0-1 1-1 1-2l-1-1v-1c0-2 0-5 1-7h1v-5 13c0 3 1 5 0 8z" class="K"></path><path d="M472 370l2-2 1 1c1 1 3 2 5 2v7h0-1l-7-8z" class="C"></path><path d="M503 694c4-1 14-2 17 0v2c-4 0-15 1-18-1l1-1z" class="D"></path><path d="M555 505h18c-3 3-9-2-11 4h0c0 1-1 1-2 1-2-1-4-3-5-5zm127-157h0c2 2 3 3 4 5-1 2-1 2-3 3h-2c-1-1-1-1-2-3 0-2 1-3 3-5z" class="B"></path><path d="M514 637c3 1 8 1 11 3v1h-1-2c-2 1-4 0-6 1h-3l1-5z" class="N"></path><path d="M539 214c0 2 0 5-1 7l-6 3c1-2 0-5 0-7h2c2 0 2-2 4-1l1-2z" class="L"></path><path d="M425 395c1 0 1 0 2 2 1 3 0 9 0 13v4c-2 0-1 1-2 0v-19z" class="I"></path><path d="M513 183c0 2 1 3 0 5h2v17h0 0c-1 0-1 0-2 1v-23zm-11 683c3-1 7 0 10 0h8v1h1c-2 1-4 1-6 1-1 0-1 1-2 1-2 0-3 1-4-1h-1c-1-1-5 0-7-1l1-1z" class="F"></path><path d="M484 630h1l-1 39c0 6 0 13-2 18v1c-1-2 0-4 0-6v-1h1v-18c0-2 1-4 1-6v-27z" class="J"></path><path d="M523 560c4 0 7-3 9-2-2 1-4 3-7 4l-1 1h-39c1-1 3-1 5-1h13 11c1 0 4 1 5 0 1 0 3-2 4-2z" class="I"></path><path d="M685 259l1-1c2 0 9 0 10 1v3h-1c-2 0-10 1-11 0l1-3zm-1 4h12c0 2 0 2-1 3l-11 1c-1-2 0-2 0-4z" class="D"></path><path d="M565 161c2 2 3 4 5 6v1c-3 1-4 2-4 5h-1c0-4-2-4-4-6 2-2 3-3 4-6h0z" class="B"></path><path d="M515 616h2l-1-1c3-2 4-4 7-6v1h0c-1 3-1 6-1 9l-3-2c0 1-1 1-2 2l-2 1h-2l-1-2c1-1 2-1 3-2z" class="L"></path><path d="M444 285l2 1c2 2 3 5 5 7 2 3 5 7 8 9-1 1-3 1-4 1-2-1-3-3-4-5 1 0 0 0 1-1l-4-4-2-4c-1-2-2-3-2-4z" class="B"></path><path d="M549 504c2 0 3 0 5 1h1c1 2 3 4 5 5l5 6h-1v1 1l-7-7-8-7z" class="L"></path><path d="M350 287c1 0 2 0 3 1s1 2 2 3c-1 2-2 3-3 4-2 0-3 0-4-2-1-1-1-2-1-3s1-2 3-3z" class="B"></path><path d="M686 253c2-1 4 0 7 0 1 0 3 1 4 1 1 1 0 2 0 3h-12c0-1 0-2 1-4z" class="D"></path><path d="M685 276c3-1 6-2 10-1 1 1 0 3 0 5l-6 1c-1-2-4-3-4-5z" class="C"></path><path d="M480 483h1v6l-1 1v4 4c0-2 0-4-1-6v-1c-6-2-12-1-17-1h-1-3v6c-1-3-1-5-1-8 2 0 3 1 5 0h12c2 0 4 1 6 0v-5z" class="J"></path><path d="M585 398h1c2 0 3 0 4 2 1 1 1 2 0 4 0 1-1 1-2 2-2 0-3 0-4-1s-1-2-2-4c1-2 2-2 3-3z" class="B"></path><path d="M509 672c1 0 3-1 4 0l1 1h0c0 1 0 3 1 4h0l1-2h0l2 1 1 1h2 1l1 2v2l-2-1h1v-1c-3-2-6-1-9-1-2 0-5 1-7-1 0-1 2-1 3-2v-3z" class="D"></path><path d="M553 524h1l4 4c2 2 5 3 7 5l2 3c-1 0-1 0-2-1-2-2 1 0-1-1l-1-1-1-1-1-1-3-2h0c0 1 0 1 1 1l7 7-1 1-4-4 1 4-3-2-1-1-1-1v-1-2l-3-3c0-2 0-3-1-4z" class="F"></path><path d="M557 531l4 3 1 4-3-2-1-1-1-1v-1-2z" class="B"></path><path d="M509 642c-4-1-6 1-9 3v-2c1-1 1-1 1-2h-3-1c3-3 9-5 12-5 1 2 1 3 0 4v2z" class="F"></path><path d="M461 658l-2 3c-3 4-8 4-13 5 5-4 10-9 16-10l-1 2z" class="I"></path><path d="M423 354l1-7h14v1 1h-5v1 1h-1l-4 1-5 2z" class="E"></path><path d="M684 285l-1-1c4-2 8-3 12-3 1 1 1 1 1 3v3c-1 1-2 1-4 1h0c-1-1-3-2-5-2-1 0-2 0-3-1h0z" class="C"></path><path d="M422 753c3-1 7 0 10 0 9 0 19-1 27 0v1c-6 1-14 1-20 1s-12 0-17-1v-1z" class="B"></path><path d="M500 346l1-1v3l2-1v1c0 5 1 10 0 15l-2 2-1-1v-5-13z" class="K"></path><path d="M483 530c3 3 7 1 10 2v1h-5 16c1 0 3 0 4 1h0 0l-1 1h-2c-5 1-11 0-16 0-2 0-5 1-6 0v-5z" class="N"></path><path d="M691 290l6-1 2 6-10 4c0-1-1-1-1-2s0-1 1-2 2-3 2-5z" class="C"></path><path d="M483 518c2 0 41 0 43 1-1 1 0 1-1 1h-6-35l-1-2z" class="B"></path><path d="M458 644l3-1h0c6 1 13 1 19 1 1 1 1 1 0 2-2 1-6 0-8 0-5 0-10 1-14 0-1-1 0-1 0-2z" class="D"></path><path d="M509 666v-2c0-1 0-1 1-1-1-1 0-1-1-2h0l1-1c2 0 2 0 4 1v1l-2 1c2 1 2 1 4 1l1 1-3 1v1c0 1 0 1-1 2l2 1c0-1 1-1 1-2h0c1 1 1 2 1 3h-2c-1 1-1 1-1 2h0l-1-1c-1-1-3 0-4 0 0-2 0-4 1-6h-1z" class="B"></path><path d="M686 268c2 0 8-1 9 1 0 1 1 3 0 4h-2l-10 1c0-2 0-4 1-5l2-1z" class="C"></path><path d="M483 551v-7h1c0-1 0-1 1-1h9 3c0 1 1 1 1 1v1h-4c-1 4-5 1-7 3h5l-1 1c-2 0-5 0-7 1l-1 1z" class="F"></path><path d="M574 740h1c2 0 3 1 4 2v4c0 1-1 2-3 2 0 1-1 1-2 1-1-1-2-1-3-2s-1-2-1-4c1-2 2-2 4-3z" class="B"></path><path d="M511 905l1-1c1 0 1 0 2-1 1 0 2-1 2-3h0c1 1 1 2 1 3-1 0-2 1-3 1l1 1h1l1-1h1c-1 2-3 6-5 7-1 1-2 1-2 1-2-2-3-2-4-4 0-1-1-2-2-2-2-3 1 1 0-1l-2-1v-2h1l1 1c0 1 1 1 1 2h5z" class="J"></path><path d="M506 905h5 3l1 1c-2 1-4 1-6 1-1 0-2-1-3-2z" class="H"></path><path d="M620 529l8 1c0 2 1 7-1 8l-4 1-1-2c-1-1-1-2-2-2l-1-1c0-2 0-2 1-3v-2z" class="E"></path><path d="M620 531c0 1 1 2 2 3 2 0 3 1 4 1 1 1 1 2 1 3l-4 1-1-2c-1-1-1-2-2-2l-1-1c0-2 0-2 1-3zM419 765c8-2 17-1 25-1 3 0 7 0 10 1-1 1 0 1-1 1-3 1-8 0-11 0h-24v-1h1 0z" class="B"></path><path d="M510 209h3v3c0-1 1-1 2-2 1 2 1 3 1 5l-1 1c0 1-2 1-2 1v6 1h-1c-1 1-1 1-2 0-1-5-1-10 0-15z" class="C"></path><path d="M513 212c0-1 1-1 2-2 1 2 1 3 1 5l-1 1c0 1-2 1-2 1v6c-1-3 0-5-1-7h0c1-1 1-2 1-3v-1z" class="F"></path><path d="M443 668c12-3 24-1 36-1h1v2c-6 1-14 0-20 0h-4c-3 0-10 1-13-1z" class="D"></path><path d="M492 548c1 1 1 1 2 3l-11 10c-1-2 0-7 0-10l1-1c2-1 5-1 7-1l1-1z" class="J"></path><path d="M623 539h-10c0-3-1-8 0-11l7 1v2c-1 1-1 1-1 3l1 1c1 0 1 1 2 2l1 2z" class="C"></path><path d="M515 496h0c-4 0-30 1-32 0l1-1c1-2 9-1 12-1l36 1c-1 1-3 1-5 1-4 0-8-1-12 0z" class="D"></path><path d="M435 280v-3l1-1 1 2v-1c0-1-1-2-2-3 1-1 1-1 2 0s2 1 2 3l2 2v2h1c1 1 1 2 2 3v1c0 1 1 2 2 4l2 4 4 4c-1 1 0 1-1 1l-16-18z" class="E"></path><path d="M701 303l-11 6-2-7c3-2 7-5 11-5l2 5v1z" class="D"></path><path d="M517 334c2 0 4 0 5 2h0v5c1 3 0 7 0 9 0 3 1 12-1 14h0c-1-1-2-2-2-3 1-2 0-4 0-7h0c0-2-1-2-2-3 1-2 1-2 3-3-1-3-1-6-1-8l-1 1-1-1h0c0-1 1-1 1-1v-3c0-1-1-1-1-2z" class="J"></path><path d="M439 692c10-1 20 0 29 0 3 0 7-1 10 1v1c-3 2-17 1-21 1-8-1-15 0-22-1l4-2z" class="C"></path><path d="M506 686c3 0 12-1 14 1v2l1 1-1 1h-4c-1 1-2 1-3 1l-1-1c-2 0-4 1-6 0h-2c-1 0-1 0-2-1v-1-2l4-1z" class="I"></path><path d="M449 679c3 0 28-1 30 1v1c-2 1-8 1-11 1h-22v-1h-8v-1c3-1 7-1 11-1z" class="D"></path><path d="M600 262l4-3c1-1 1-1 2-3h1c1 0 0 0 1 1h1v3c-4 5-7 10-6 17 1 3 2 6 4 9h-1c-3-5-4-13-8-18 1-2 2-5 2-6z" class="G"></path><path d="M483 530v-1c1-1 6-1 8-1 6 2 14 1 21 1 1 0 1-1 2 0-1 1-1 1-2 1h0c-3 1-5 1-8 1v2h-16 5v-1c-3-1-7 1-10-2zm-42 139l1-1h1c3 2 10 1 13 1h4c-3 2-7 4-10 6-2 1-7 4-10 3l1-1c1-1 0-2 0-2v-6z" class="F"></path><path d="M441 669l1-1h1c3 2 10 1 13 1-4 3-10 3-14 3l-1-1h2v-1l-2-1z" class="G"></path><path d="M441 346h-11c-2-1-5 0-6-1 0-1-1-2 0-3 7-1 15 0 22 0 1 0 2 0 3 1l2 3h-1-9z" class="E"></path><path d="M513 523c-4 2-9 1-13 1h-2c-3 1-8 0-11 0-1-1-3 0-4-1 0-1-1-3 0-5l1 2h35c-3 1-5 1-8 1h-6l-1 1h1c2 0 5 0 8 1z" class="F"></path><path d="M441 392h-18l1-6h12c1 0 3-1 4 0h1 1c-1 2-1 4-1 6z" class="B"></path><path d="M461 530h18v6h-18v-5h8c0-1-5 0-7-1h-1zm60 172h1v43 19 5h-1v-10h0l-1-3h1v-5-9h-7-10l-1-1c0-1 1-1 1-2 5-1 11 0 16 0l1 1v-7-31z" class="I"></path><path d="M561 534l4 4 21 19c5 4 11 9 15 14-4-2-7-4-10-6l-29-27-1-4z" class="E"></path><path d="M424 719l1-1c6-1 14-1 21-1 9 0 18 0 27 1v1 1c-12 1-24 0-36 0h-12l-1-1zm59-221h1 63l1 1h-1c-15 2-30 1-45 1-6 0-13 1-19 0v-2z" class="C"></path><path d="M513 355v5c0 2 1 3 0 5 0 0 1 1 1 2s-1 1 0 2c-1 3 0 2 0 4 0 1 0 2-1 3l1 1-1 2 1 1-1 1v1c1 0 1 1 1 2l-5-1v-1h0v-2c0-1 0-1-1-2v-1h1v-2l-1-1h1v-2-4c1-4 0-8 0-11l4-2zm-4 274h4l1 2h0c-1 2-1 4 0 6l-1 5v2 1l1 1v2c0 2-1 4 0 6v1c-1 0-2 0-3 1h2l1 2v1c-2 0-3 0-5-1v-1l1-1c0-1-1-1-1-2v-1c0-2 1-7 0-9v-2-2c1-1 1-2 0-4 1-2 1-5 0-7z" class="B"></path><path d="M469 313c0-1 0-1-1-2l1-1 3 3h1v-1c1 1 2 2 3 2l-1 1c5 5 9 8 10 16v8 15 4c-1-2-1-4-1-6v-13c-1-1 0-7-1-8h-2 0v-4s-11-12-12-14z" class="K"></path><path d="M549 682h2c2 0 4 1 5 3s1 3 1 5c-2 2-3 3-5 4-2 0-3 0-6-2h0s-1-3-2-3c0 0-6 0-7-1 2 0 5 0 8-1h0c0-2 2-4 4-5z" class="B"></path><path d="M480 669c0 1 0 3-1 4-3 2-18 1-23 1-2 0-4 1-6 1 3-2 7-4 10-6 6 0 14 1 20 0z" class="K"></path><path d="M623 540h5l1 7-11 2c-1 1-3 1-5 1v-9l9-1h1z" class="D"></path><path d="M424 301v-17c2 2 5 5 7 8 1 1 1 2 2 4s4 3 4 5h-7-6 0z" class="E"></path><path d="M631 554l1 1-1 1-15 4h-1v-6l-1-1c2-2 12-5 16-5 0 1 0 3 1 5v1z" class="C"></path><path d="M631 554v1h-5 0 0v-1c0-1 1-2 1-4h1l3 3v1z" class="D"></path><path d="M455 303c1 0 3 0 4-1l10 11c1 2 12 14 12 14v4l-1 1-25-29z" class="C"></path><path d="M546 504c1-1 2-1 3 0l8 7-3 1h0l-4 3-3 3-8-8h-1l8-6z" class="Q"></path><path d="M546 504c1-1 2-1 3 0l8 7-3 1h0c-2-1-4-3-5-4-3 4-7 1-10 2h-1l8-6z" class="E"></path><path d="M530 165v-4-1c0-2 1-3 1-4l1 14h0l1 13-1 34c0 2 1 5 0 7-2-3-1-32-2-38v-1-20z" class="B"></path><path d="M552 663v6c0 1 0 3-1 4-1-3 0-7-1-10-1 0-1-1-2-1-1-1-1-1-1-2 0-2 2-3 3-4v-1l1-68c2 2 0 58 1 67l1 1c0 2 1 4 0 6l-1 1v1z" class="G"></path><path d="M406 245l-2-4c2-1 4-3 6-4 3-2 6-6 9-7l1 1c1 2 1 4 1 5 0 2 0 2-1 3v1c-5 1-9 6-13 9 0-2 0-3-1-4z" class="C"></path><path d="M406 245l15-9c0 2 0 2-1 3v1c-5 1-9 6-13 9 0-2 0-3-1-4z" class="K"></path><defs><linearGradient id="A" x1="541.541" y1="207.453" x2="528.381" y2="198.663" xlink:href="#B"><stop offset="0" stop-color="#41403e"></stop><stop offset="1" stop-color="#6d6d6f"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M533 183c0 1 0 3 1 4 0-1 0-1 1-2l1-3h0v7c1 4 1 19 2 21l1 4-1 2c-2-1-2 1-4 1h-2l1-34z"></path><path d="M462 656h19l-1 7-21 1c1-2 2-4 2-6l1-2z" class="H"></path><defs><linearGradient id="C" x1="433.723" y1="306.446" x2="428.222" y2="311.315" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#C)" d="M424 301h0 6c1 2 1 3 3 4 0 2 0 3 1 5l1 1h0c1 2 2 2 3 3v4l-1-2c-1-1-2-1-4-1v1h-9v-8c-1-2 0-5 0-7z"></path><path d="M424 301h0l1 1h2c1 2 1 4 1 6l-1 1-1-1-1-1-1 1h0c-1-2 0-5 0-7z" class="N"></path><path d="M509 188c1-5-1-20 2-24 1 0 1 1 2 2v17 23 3h-3-1c1-3 0-5 0-8v-13zm159 100l1 1c-2 5-5 8-8 12-2 2-5 4-7 6-9 5-18 6-28 5-7 0-14-3-19-7-1-2-2-2-2-4-1-1 0-1 0-1v1c1 1 2 2 3 2 4 3 9 5 14 6 6 2 14 2 21 0 3 0 8-3 11-5 5-3 12-9 14-16z" class="B"></path><path d="M435 280c-3-4-7-9-11-12h0c0-2 0-3-1-4h1c3 2 5 4 7 7 1 0 1 0 2 1v-1c-3-4-6-7-9-9l-1-1c1-1 1-2 1-4h0v-1l2 2c1 3 5 5 6 8 0 1 1 3 2 3v-2l-2-3v-1h0l3-3c0 3 2 7 3 10 2 3 2 6 4 8 1 3 3 5 4 8l-2-1v-1c-1-1-1-2-2-3h-1v-2l-2-2c0-2-1-2-2-3s-1-1-2 0c1 1 2 2 2 3v1l-1-2-1 1v3z" class="C"></path><path d="M424 243l-3-10 10 13c1 1 1 4 2 6 0 2 1 5 2 8l-3 3h0v1c-1-3-5-7-7-9l-2-1c1-2 1-8 1-11z" class="B"></path><path d="M424 243c1 0 2 0 3 2l1 1-1 1h-2v1h0c1 1 1 2 2 3l1 1v1c-1 1-2 1-3 2l-2-1c1-2 1-8 1-11z" class="C"></path><path d="M498 439v-67-2c-1-13-1-27 1-40l1 1c1 4 2 9 1 13v1l-1 1v-6l-1 1v26 11c1 1 1 3 1 5v10 39 6l-2 1z" class="F"></path><path d="M532 495l87 1v1c-2 1-5 0-7 0h-12l-40 1-11-1-22-1c-4 0-8 1-12 0 4-1 8 0 12 0 2 0 4 0 5-1z" class="B"></path><path d="M509 629c1-1 1-4 0-6h-1v-1h2c-1-1-2-2-2-3l2-1h0l-1-2c0-2 0-2 1-3-1-2-1-2-1-4l1-1-1-1v-2c1-2 1-3 0-4v-2-3-1-1-3-2-2l2-1 2 2v5c0 2 1 3 0 5v1c1 2 0 2 0 3s0 0 1 1v1l-1 1c0 1 0 1 1 2l-1 2 1 1c-1 1-1 2-1 3l2 3c-1 1-2 1-3 2l1 2 2 1-1 1-1 1v1c1 1 0 2 0 3v2h-4z" class="D"></path><path d="M509 372l-2-2-1-1v-2-8c0-3 1-8 0-11v-2c1-2 1-6 0-8v-1c1-1 2 0 3 0v-5c0 1 0 3 1 4h1v1c2 0 2 2 4 2v1c-1 0 0 0-1 1v4h1v1h-3l1 1v7 1l-4 2c0 3 1 7 0 11v4z" class="L"></path><path d="M509 357v-8c1-1 2-2 4-2v7 1l-4 2z" class="D"></path><defs><linearGradient id="D" x1="432.363" y1="453.571" x2="433.973" y2="439.852" xlink:href="#B"><stop offset="0" stop-color="#2a2a2b"></stop><stop offset="1" stop-color="#4e4d4d"></stop></linearGradient></defs><path fill="url(#D)" d="M428 440h9c1 0 3-1 4 0v10c-2 0-4 1-5 2v7 2h-1c0-1 0-1 1-2h-1v-5l-1-3h0c-2 1-2 1-3 1-2 1-2 1-3 2v12c-1-1-1-3-1-4v-9c-1-4 0-9 0-13h1z"></path><path d="M602 524l-1 3 1 1c0 2 0 3-1 5 1-1 2-2 2-3l1-2c1-2 2-3 2-4s1-2 1-3c1-1 2-1 4-2-2 10-2 20-2 30h-2c-1-2 0-3 0-5l-1-9-2 10h-1v-1c1-1 1-3 1-4h-1l-2 2h0v-3l-2 3-1-1 1-3h0l-2 2v-1l1-2 3-6h-1l-1 1-1-1c0-2 2-5 4-7z" class="B"></path><path d="M507 437h1 2 0l-3 3-2 2h4c1 2 1 4 0 5s-2 0-4 1c0 1-1 1-1 1h-4c-1 0-2 0-3 1h-2c-3 3-4 17-6 22v1c0 1 0 2-1 2v5c-1 3-2 5-3 7l-1-2c1-4 3-9 4-13l3-13c1-4 1-8 2-11s3-7 5-9l2-1c1 0 2-1 3-1h1 3z" class="H"></path><path d="M425 710v-6l50 1 1 1-1 1h-32c-1 0-2 2-2 2-4 1-7 0-9 5h-6-1v-4z" class="B"></path><path d="M425 710c1 0 2-1 3-2h1c4-2 9-1 14-1-1 0-2 2-2 2-4 1-7 0-9 5h-6-1v-4z" class="S"></path><path d="M424 373h7 9 1 1 3c1 3 0 8 0 11h-3c0-1-1-1-1-2l-1 1h-5-11v-7-3z" class="I"></path><path d="M424 373h7 9 1 1 3c1 3 0 8 0 11h-3c0-1-1-1-1-2 0-3 0-4 2-6-6-1-13 0-19 0v-3z" class="H"></path><path d="M443 376l1 1c0 2 1 4 0 6l-2 1c0-1-1-1-1-2 0-3 0-4 2-6z" class="B"></path><path d="M538 807l1-1-1-1h1 0c0 1 1 1 1 2 2 1 2 2 4 3h1c0 1 1 1 1 3h0c0 1 1 2 1 2 0 1 1 3 1 4v2 1 2c1 1 0 3 1 5 1 1 0 6-1 8h0c0 2-2 5-2 7l-4 6-1-1c2-3 4-6 5-9l1-1c0-2 0-4 1-7 0-3-1-7-1-10s-1-6-3-8l1 6c0 9-4 17-10 23l-1 1c-1 0-1 1-2 1l-1 1c-1 0-2 1-3 2l-1 1h-1l-2 1 2-2c2-1 3-2 5-4 1 0 1-1 2-2l1-2 2-3c6-9 5-20 2-30z" class="F"></path><path d="M701 302c5 8 10 15 17 22 3 2 7 4 9 6-7-1-16-4-22-9-3-3-5-6-9-8l-1-1h-2l-1-1c2-3 7-5 9-8v-1z" class="C"></path><defs><linearGradient id="E" x1="444.97" y1="307.291" x2="437.474" y2="319.298" xlink:href="#B"><stop offset="0" stop-color="#4f4f4e"></stop><stop offset="1" stop-color="#6c6a6c"></stop></linearGradient></defs><path fill="url(#E)" d="M432 292c2 3 5 8 9 10v1 1c2 2 1 6 2 8 0 3 0 5 1 8v1 2h1c1 0 2 2 3 4 1 1 1 3 1 5h-1c0-1-1 0-1 0l-1 1c-1 0-1-1-2-1l-5-7h1l1-1h-1v-1c-1 0-2-1-1-2 0-1 0-2-1-3v-4c-1-1-2-1-3-3h0l-1-1c-1-2-1-3-1-5-2-1-2-2-3-4h7c0-2-3-3-4-5s-1-3-2-4h1z"></path><path d="M440 325c0 1 3 4 5 5h0l-1-1c0-2-1-2-2-3l1-1-1-2c0-1 1-1 2-2v2h1c1 0 2 2 3 4 1 1 1 3 1 5h-1c0-1-1 0-1 0l-1 1c-1 0-1-1-2-1l-5-7h1z" class="N"></path><path d="M430 301h7c0 2 0 2 1 3h0c0 2 0 2 1 3v1h1v1c0 1 0 2 1 3v2c0 1 0-1 0 1s0 7-1 8c-1 0-2-1-1-2 0-1 0-2-1-3v-4c-1-1-2-1-3-3h0l-1-1c-1-2-1-3-1-5-2-1-2-2-3-4z" class="G"></path><defs><linearGradient id="F" x1="471.03" y1="714.845" x2="434.946" y2="704.923" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#343233"></stop></linearGradient></defs><path fill="url(#F)" d="M443 707h32c-1 3-2 4-5 7l-21-1h-12l-5 1c2-5 5-4 9-5 0 0 1-2 2-2z"></path><defs><linearGradient id="G" x1="449.701" y1="726.275" x2="447.952" y2="717.034" xlink:href="#B"><stop offset="0" stop-color="#171716"></stop><stop offset="1" stop-color="#383538"></stop></linearGradient></defs><path fill="url(#G)" d="M432 725c-2 0-8 1-9 0 1-2 1-4 1-6l1 1h12c12 0 24 1 36 0-1 1-2 3-4 4h-2-1c-6 1-12 1-18 1h-16z"></path><path d="M432 725c-2 0-8 1-9 0 1-2 1-4 1-6l1 1v1l-1 2c2 1 5 0 7 0h4 0c-1 1-2 1-3 1-1 1-1 1 0 1z" class="F"></path><path d="M502 739v-37l1-1v1 40 21c0 2 1 5 0 8h0c-2 5-5 8-6 13h1 0 1l8-4c2 0 4 1 5 1 1-1 3 0 4 0-1 1-2 2-2 3v1c-2-1-3-1-5-1v1c0 1 1 1 1 2h-3c-1 0-2-1-3-2-2 1-2 1-3 3v1c-3 2-5 3-7 5h-4c-3 2-5 5-7 8-3 5-6 10-7 16-1-4 2-10 4-13s3-4 3-7h0c2-1 3-3 5-5s5-4 6-7c2-5 6-11 7-16h0c1-3 1-7 1-10v-21z" class="N"></path><path d="M507 780c2 0 4 1 5 1 1-1 3 0 4 0-1 1-2 2-2 3v1c-2-1-3-1-5-1v1c0 1 1 1 1 2h-3c-1 0-2-1-3-2 1 0 2 0 3-1l-1-1c-1 1-2 1-3 2l-1-1c2-2 5-1 7-3h0-1l-1-1z" class="O"></path><path d="M514 654c3 0 5 0 8 1 1 1 0 1 0 3v6l1 1 1-1v1l1 1c2-3 1-9 3-11 1 1 0 2 0 3l-3 25c0 4 0 8-1 12 0 1 0 2-1 3-1-1 0-3 0-4-1-2-1-1-1-3l1-1v-1-1c1-2 0-5 1-6 0-3 0-6 1-9v-6h0c-2-1-5-1-7-1l-1-1-1-1c-2 0-2 0-4-1l2-1v-1c-2-1-2-1-4-1l-1 1h0c1 1 0 1 1 2-1 0-1 0-1 1v2c-3 0-6 0-9 1h-1v-2c-1-2-1-2 0-4h0c1 2 1 4 2 5v-9c1 0 2-1 2-1l1-1c1 1 0 4 1 6h1v-5-1-1h3c0 1 1 1 1 2l-1 1v1c2 1 3 1 5 1v-1l-1-2h-2c1-1 2-1 3-1v-1z" class="N"></path><path d="M514 654c3 0 5 0 8 1 1 1 0 1 0 3h0v-2c-1 0-4 0-5 1v3h-1c0-2 0-3-1-4h-2-2c1-1 2-1 3-1v-1z" class="M"></path><path d="M427 453v9c0 1 0 3 1 4v24l-1 27c0 2 1 4 1 5v14l-1 85v55 15c0 3 1 6 0 9 2 0 2 0 4-1h1l3-1h1l1-1c1-1 1-1 1-2h-5c0-1 0-2 1-2 2-1 3-1 5-1l-4 2c7 1 14 0 22 1-2 0-9 0-10 1s-1 2-2 3c-6 1-13 1-19 3 1-6 1-13 1-19v-37-129-42c0-7-1-15 0-22z" class="H"></path><path d="M410 731h39c6 0 14-1 20 0v1c-4 2-11 1-15 1h-17c-5 1-11 0-15 3h0v1c-1 1-2 1-3 2 1 0 1 0 1 1 1 1 0 3 0 5-2 2-8 1-11 1l1-15z" class="D"></path><path d="M461 536h18c1 3 2 10 0 13l1 2c-2 1-4 0-6 0h-12l-1 1v-16z" class="H"></path><path d="M462 551c1-1 0-1 1-2h5 11l1 2c-2 1-4 0-6 0h-12zm67-410c3 5 7 9 9 15 2 7 1 17 1 25l-1 29c-1-2-1-17-2-21v-7h0l-1 3c-1 1-1 1-1 2-1-1-1-3-1-4l-1-13h0l-1-14c0-3-2-8-2-11-1-1 0-2 0-4z" class="I"></path><path d="M532 170l1-1c1-1 2-3 2-5 1 8 2 16 1 25v-7h0l-1 3c-1 1-1 1-1 2-1-1-1-3-1-4l-1-13z" class="P"></path><path d="M524 850l2-1h1l1-1c1-1 2-2 3-2l1-1c1 0 1-1 2-1v2c-2 3-5 4-6 7h-1c-1 0-1 0-2 1v-1c-1 0-2 1-3 1h-1v2 1c1 0 3-1 4-1h0c-1 2-2 2-4 2-1 1-2 1-4 1-1 0-1 0-2 1 1 0 2-1 3 0l-1 1 1 1c0 1 0 1-1 2v2l2-1h2 2 1c-2 1-3 1-4 1h-8c-3 0-7-1-10 0h-1 3v-1-4h-4v-1h2 4c-2 0-4-1-5-1l-2-2h0 1l1 1v-2c0-1 0-2-1-3-1 0-3 0-4-1h0l-3-3 3 1c0 1 1 1 2 1h2l2 1h1c3 1 7 1 11 1h0 4c0-1 1-1 2-1h1c1-1 2-1 3-2z" class="H"></path><path d="M689 246c1-6 7-15 12-19 1 0 2-1 3-1 2-1 4 0 6 0l16-1-6 3c-5 2-9 5-13 9l-5-4c1 3 4 3 4 5l-2 2c-1 0-3-2-5-3 0 1 4 4 4 5 0 0-2 4-2 5l-1 1c-2 0-3 0-5-1-2 0-4-1-6-1z" class="C"></path><path d="M695 247c1-1 2-1 2-3v-1l2 1c1 1 1 1 2 3l-1 1c-2 0-3 0-5-1z" class="B"></path><path d="M461 490h1c5 0 11-1 17 1v1c1 2 1 4 1 6v7 1c-1 1-1 1-2 0h-17v7c1 1 1 0 2 1h0-1l-2-1v-7c1-1 0-1 0-2 1-2 0-4 1-6l1-1c-1-2-1-5-1-7z" class="R"></path><path d="M479 492c1 2 1 4 1 6v7 1c-1 1-1 1-2 0h1v-6h-17l-1-2 1-1h5l12 1v-6h0z" class="K"></path><path d="M461 490h1c5 0 11-1 17 1v1h0v6l-12-1h-5c-1-2-1-5-1-7z" class="S"></path><path d="M630 557h3c4 9 10 17 17 24 3 2 7 5 11 7l-12-3c-6-3-12-6-17-10-2-3-5-7-8-10-2-2-4-3-7-3h0v-1c4-2 9-2 13-4z" class="B"></path><defs><linearGradient id="H" x1="469.276" y1="620.663" x2="484.723" y2="615.159" xlink:href="#B"><stop offset="0" stop-color="#515051"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#H)" d="M480 598c1 2 1 8 1 10 0 9 1 20-1 29-1 1-3 0-4 1l-1-1v-4 1l-1 1-1 2c-1 1-1 1-2 1l-1-2c-1-2 0-3 0-4-1-2-1-1-1-3l1-1c1-1 1-3 1-5 0-4 1-8 2-12v-2-1c1-3 1-5 1-8l5-1 1-1z"></path><path d="M475 630c0-3 1-6 2-9 0 2-1 6 0 8 0 1 1 1 1 2 0 2 0 4 1 6h1c-1 1-3 0-4 1l-1-1v-4h0c1-2 1-3 1-4l-1 1z" class="G"></path><path d="M470 628v3c2-2 2-6 3-8v-2h1c0 1 0 1-1 2v8h1l1-1 1-1c0 1 0 2-1 4h0v1l-1 1-1 2c-1 1-1 1-2 1l-1-2c-1-2 0-3 0-4-1-2-1-1-1-3l1-1z" class="L"></path><path d="M509 383l5 1-1 4c1 1 1 2 2 2h-1l-1 1 1 1c0 2 0 3-1 4l1 1h-1-1l2 2h-1c0 2 0 3 1 4l-1 1c0 1 0 1 1 2l-1 1v1c1 1 1 0 1 1v3h0-1c0 1 1 2 1 4 0 1-1 1-1 3l1 1c0 1-1 1-1 2s1 2 0 3c0 1 0 2 1 3-1 1-1 0-2 1l1 1c1 1 0 1 0 3h0v1c0 1 1 2 0 3 4 1 10 2 12 7h0c1 2 2 3 2 5-4-4-7-7-13-9l-1 7h-4c1-1 1-3 0-5h-4l2-2 3-3h0-2-1l2-2v-2l1-1h-1c0-2 0-2 1-3-1-1-1-2-1-3v-1c0-1 0-2-1-3l1-2v-1-3l-1-1 1-1-1-2h1v-1l-1-1 1-1-1-2c1-1 1-2 1-2v-3-2-1-2c0-3 0-3-2-5h2l-1-1 2-1c-1 0-1-1-2-2l1-2-1-2 1-1z" class="D"></path><path d="M418 766c-1 2-1 5-2 7l-12 1 5-27c6 1 12 0 17 0l24-1c4 0 9 0 13 1l-1 1c-10 1-22 0-32 0-3 0-7 0-10 1-1 1 0 3 0 5h0l-2 2v2c-1 2 0 1 0 2 1 1 0 3 0 4l1 1h0-1v1z" class="C"></path><path d="M438 347h2v26h-9-7v-11c0-1 0-2 1-3v-1c0-2 2-4 3-6l4-1h1v-1-1h5v-1-1z" class="Q"></path><path d="M428 352l4-1c1 1 1 3 0 4h0c2 2 3 3 2 6v5h-1l-1-1h-1l-4-4-2-2v-1c0-2 2-4 3-6z" class="T"></path><path d="M425 358h3c2 2 3 4 4 7h-1l-4-4-2-2v-1z" class="H"></path><path d="M428 352l4-1c1 1 1 3 0 4h0v5h-1c-1-1-1-2-2-2h-1-3c0-2 2-4 3-6z" class="O"></path><path d="M425 359l2 2 4 4s1 1 1 2c-1 2-1 3-1 5v1h0-7v-11c0-1 0-2 1-3z" class="I"></path><path d="M427 361l4 4s1 1 1 2c-1 2-1 3-1 5v1l-1-1v-4c-1-2-2-3-4-4 0-1 0-2 1-3z" class="B"></path><defs><linearGradient id="I" x1="588.712" y1="560.046" x2="594.273" y2="541.097" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#444343"></stop></linearGradient></defs><path fill="url(#I)" d="M574 531l3 4c0 1 0 1 1 2h4 0l26 24 1-1v-2-1-3l1 1v2c1 1 0 3 1 4 1 2 0 4 0 6h-2c-5-3-10-5-15-8-8-5-15-13-22-19 0-1 0-1 1-2h0v-5l1-2z"></path><path d="M582 537h0v2 1h0-2c-1-1-2-2-2-3h4z" class="G"></path><path d="M545 810l-1-2c-1-2-3-6-5-7v-1-1c1 0 0 0 1 1h1c7 7 13 15 20 23l6 6c1 1 2 1 2 2l-5 5h0l4-7c-4 2-8 5-12 7-3 2-7 8-10 8 0-2 2-5 2-7h0c1-2 2-7 1-8-1-2 0-4-1-5v-2-1-2c0-1-1-3-1-4 0 0-1-1-1-2h0c0-2-1-2-1-3z" class="H"></path><path d="M502 739c-1-3-1-7-1-10 0-10 1-20 0-30h0c-1-1-1-1-1-2v-1l1 1h1c1-1 6 0 8 0 1-1 1-1 2-1 2 1 6 1 8 1h0 1 1v1 1h-2c1 1 1 1 1 2l1 1h-1c-5-1-11-1-16-1v1c0 1 1 1 2 1h3v1h-1l-1 1c-1 0-3 0-4 1v2 1l1 1c1 1 2 1 3 1v1c-2 2-2 4-2 7h3v5 2 3 5l1 1h0c2 0 4 0 6-1 1-2 1-5 1-7 0-4 2-6 2-9 0-1 0-1 1-2v4c1 2 0 9 1 12v1 7l-1-1c-5 0-11-1-16 0 0 1-1 1-1 2v1-40-1l-1 1v37z" class="J"></path><g class="F"><path d="M506 719h3v5 2 3 5l1 1h-1c0-1-1-1-1-2-2-2-2-11-2-14z"></path><path d="M508 705l1-1h1v-1h-3c-1 0-2 0-2-1v-1c5 0 11 0 16 1v31-1c-1-3 0-10-1-12v-4c-1 1-1 1-1 2 0 3-2 5-2 9 0 2 0 5-1 7-2 1-4 1-6 1h0l-1-1v-5-3-2-5h-3c0-3 0-5 2-7v-1c-1 0-2 0-3-1l-1-1v-1-2c1-1 3-1 4-1z"></path></g><path d="M509 729c2 1 3 1 5 1v2l-4 3h0l-1-1v-5z" class="H"></path><path d="M511 718v-5h1v4h1v-4l1 1v3h1l1 1c0 2-2 2-1 5h0-1v2 3 2c-2 0-3 0-5-1v-3-2-5c0-1 1-1 1-1h1z" class="L"></path><path d="M511 718l2-1v1 1 2l-1 1-1-1v-1c0-1 0-1-1-2h1z" class="M"></path><path d="M508 705h0c2 1 3 1 4 1l1-1c1 0 2 0 3 1v1s-1 0-1 1c-1 0-1 0-2 1v2h0c-1 1-1 1-1 2h-1v5h-1s-1 0-1 1h-3c0-3 0-5 2-7v-1c-1 0-2 0-3-1l-1-1v-1-2c1-1 3-1 4-1z" class="H"></path><defs><linearGradient id="J" x1="455.141" y1="622.365" x2="477.583" y2="611.898" xlink:href="#B"><stop offset="0" stop-color="#1d1c1e"></stop><stop offset="1" stop-color="#696968"></stop></linearGradient></defs><path fill="url(#J)" d="M461 642h0c-1-4 0-9 0-12v-22c0-2-1-7 0-9l13 1c0 3 0 5-1 8v1 2c-1 4-2 8-2 12 0 2 0 4-1 5l-1 1c0 2 0 1 1 3 0 1-1 2 0 4l1 2c-2-1-4-1-5-1h-5v5z"></path><path d="M433 316v-1c2 0 3 0 4 1l1 2c1 1 1 2 1 3-1 1 0 2 1 2v1h1l-1 1h-1l5 7c1 0 1 1 2 1 1 2 4 4 4 6l-1-1c-1 1-1 1-1 2 0 0-1 0-2 1l-22-1v-6c3 0 6 1 10 0h0l1 3 2-1 1 1v-1-1l-3-2h-2c-3 0-8 0-10-1l1-4v-2-4h7 0v-1l-3-1h-4c0-2-1-2 0-3 3 0 5 0 8 1l1-2z" class="D"></path><path d="M433 316v-1c2 0 3 0 4 1l1 2c1 1 1 2 1 3-1 1 0 2 1 2v1h1l-1 1h-1c-3-2-4-6-7-7l1-2z" class="P"></path><path d="M428 320c2 0 4 0 5 1 2 2 2 4 3 6 3 3 9 7 10 11l-1-1h-1v1-1c-1-1-3-2-3-4-2 0-2 1-3 2l-3-2h-2c-3 0-8 0-10-1l1-4v-2-4h7 0v-1l-3-1z" class="H"></path><path d="M435 333c-1-2-2-3-2-4l1-2c1 1 2 1 2 2h0c1 1 2 2 2 3l2-1c0 1 0 1 1 2-2 0-2 1-3 2l-3-2z" class="B"></path><path d="M431 322c1 1 2 3 3 4h-10v-4h7 0zm-7 6c2 0 5 0 7 1l1-1c0 2 1 3 1 5-3 0-8 0-10-1l1-4z" class="D"></path><path d="M557 511l7 7 23 21 22 21-1 1-26-24h0-4c-1-1-1-1-1-2l-3-4-1 2v5h0c-1 1-1 1-1 2l-3-3-2-1-2-3c-2-2-5-3-7-5l-4-4h-1c-1-2-2-3-3-4l-3-2 3-3 4-3h0l3-1z" class="B"></path><path d="M573 529l9 8h-4c-1-1-1-1-1-2l-3-4-1-1v-1z" class="L"></path><path d="M558 521l1-2v-1h0c2 0 3 2 5 3l-1 1v2c-1 1-3 3-5 4l-4-4h1c0-1 0-1 1-2 1 0 1 0 2-1z" class="P"></path><path d="M563 524c1-1 1-1 2-1 1 1 3 1 4 3 0 1 0 1-1 3-1 0-1-1-2-1h-1c-1 1-1 2 0 4v1c-2-2-5-3-7-5 2-1 4-3 5-4z" class="N"></path><path d="M550 515l4-3h0l1 2-2 3h0c2 0 2-1 3-2h1l1 1-3 3v1c1 0 1 0 3 1-1 1-1 1-2 1-1 1-1 1-1 2h-1-1c-1-2-2-3-3-4l-3-2 3-3z" class="O"></path><path d="M550 515s1 1 1 2l-1 3-3-2 3-3z" class="I"></path><path d="M569 526c1 1 3 2 4 3v1l1 1-1 2v5h0c-1 1-1 1-1 2l-3-3-2-1-2-3v-1c-1-2-1-3 0-4h1c1 0 1 1 2 1 1-2 1-2 1-3z" class="M"></path><path d="M569 537v-1l-1-1c0-1 0-1 1-2s2 0 4 0v5h0c-1 1-1 1-1 2l-3-3z" class="G"></path><path d="M476 818v3c-1 4-1 9 0 13v1 2c1 2 2 5 3 7v1l-1-1h-1l-2-1c-7-5-12-11-18-17l26-28c0 3-1 4-3 7s-5 9-4 13z" class="K"></path><path d="M446 341c1-1 2-1 2-1 0-1 0-1 1-2l1 1h0l5 5c1 2 2 4 4 6 3 1 6 6 7 9h1l7 9-2 2 7 8s-1 1-1 2h0c-1 0-1 0-2 1h-1c1 2 3 3 4 5l-1 1h0l-13-15c-3-3-7-10-12-13h-1c-1-1-1-2-2-4l-1-6-1-1h-4c1-1 1 0 1-1-1 0-2-1-4-1h9 1l-2-3c-1-1-2-1-3-1h0v-1z" class="G"></path><path d="M450 339l5 5c1 2 2 4 4 6 3 1 6 6 7 9h1l7 9-2 2-2-3-18-21c-1-2-1-4-2-7z" class="B"></path><path d="M475 381c-3-1-6-6-8-9l-12-14c-1-1-4-3-4-5s0-3 1-5c2 2 3 5 5 7 3 4 6 8 10 11l3 1 2 3 7 8s-1 1-1 2h0c-1 0-1 0-2 1h-1z" class="E"></path><path d="M467 366l3 1 2 3 7 8s-1 1-1 2l-11-14z" class="L"></path><path d="M442 657h0c1-5 0-11 0-16l1-31v-93-21-20l33 1h4c0 3 1 4 1 6h-1v5c-2 1-4 0-6 0h-12c-2 1-3 0-5 0h-1l-2 1h0c0-1 0-2 1-3h1c0-2 0-3 1-4 0-1 0-3 1-4v1h1 2 3c-2-1-4 0-5-1h0c1-1 2-1 3-1-5-1-13 0-18 0v132 24 6s-1 0-1 1c0 2 0 9 1 11v11c-1 1-1 1-2 1-1-2-1-5 0-6z" class="K"></path><path d="M457 488l1-8h16 4l2 1v2 5c-2 1-4 0-6 0h-12c-2 1-3 0-5 0z" class="I"></path><path d="M474 480h4l2 1v2 5c-2 1-4 0-6 0h-12l-1-2 1-1h1v-1l1-1v1 2h5 0l-1-2h0c2 0 2 0 4 2h1v-2l2-2-1-2z" class="T"></path><path d="M474 480h4l2 1v2 5c-2-1-2-1-2-3h-1-1c0-1-1-2-1-3l-1-2z" class="R"></path><path d="M474 480h4v3l-2 2c0-1-1-2-1-3l-1-2z" class="B"></path><path d="M430 280c-2-1-6-5-7-7l1-3h0c4 3 7 7 10 10l12 16 15 17c6 7 11 14 17 20 2 1 3 2 2 5v2c-2-1-4-3-6-5-2-4-6-8-10-11-1-2-3-4-3-6 0-1-2-2-3-3l-8-8s0 1-1 2h-1l-7-7c-4-2-7-7-9-10s-5-6-7-9c1-1 2-1 3-2v-1h2z" class="E"></path><path d="M461 318l16 17c1-1 1-2 1-2 2 1 3 2 2 5v2c-2-1-4-3-6-5-2-4-6-8-10-11-1-2-3-4-3-6z" class="C"></path><path d="M430 280l20 27s0 1-1 2h-1l-7-7c-4-2-7-7-9-10s-5-6-7-9c1-1 2-1 3-2v-1h2z" class="B"></path><path d="M430 280l20 27s0 1-1 2c-9-8-13-19-21-28v-1h2z" class="L"></path><path d="M509 447h4c2 1 3 0 4 1l-2 2h-1c-1 1 0 5 0 7h1v1c-1 0 0 1-1 0l-1 1c0 1 0 1 1 2h0c-1 1-1 1-1 2v1 1 1c0 1 1 1 0 2 0 2 0 5 1 7v3h-4c-2-1-3-3-4-4 1-2 1-3 1-4v-1c-3-2-5-4-8-6l-1 2v-1l-10 16v-5c1 0 1-1 1-2v-1c2-5 3-19 6-22h2c1-1 2-1 3-1h4s1 0 1-1c2-1 3 0 4-1z"></path><path d="M498 464v-2h0c1-1 1-2 1-2 2-3 1-7 3-10h0c1 0 2 1 2 2 1 1 0 3 1 5 1 0 2 0 3 1h-2 0c1 1 2 1 3 1v1c-1 0-2 0-3 1h-3-1c-1 1-2 1-3 2l-1 2v-1z" class="F"></path><path d="M509 447h4c2 1 3 0 4 1l-2 2h-1c-1 1 0 5 0 7h1v1c-1 0 0 1-1 0l-1 1c0 1 0 1 1 2h0c-1 1-1 1-1 2v1 1 1c0 1 1 1 0 2 0 2 0 5 1 7v3h-4c-2-1-3-3-4-4 1-2 1-3 1-4v-1c-3-2-5-4-8-6 1-1 2-1 3-2h1 3c1-1 2-1 3-1v-1c-1 0-2 0-3-1h0 2 1v-1l1-1c-1-2 0-4-1-6l-4-2c2-1 3 0 4-1z" class="B"></path><path d="M509 476l2-2c1 0 2 1 3 1v3h-4l-1-2z" class="D"></path><path d="M507 469v-2c0-2 1-2 2-4v13l1 2c-2-1-3-3-4-4 1-2 1-3 1-4v-1z" class="G"></path><path d="M499 463c1-1 2-1 3-2h1 3 2c1 1 1 1 1 2-1 2-2 2-2 4v2c-3-2-5-4-8-6z" class="K"></path><path d="M628 486l4-3c1 1 2 3 3 3 1 3 4 5 5 8l-2 2c-4-1-6-3-9-5h0l7 8v1c-1 1-1 3-2 3-4-1-7-2-9-5l-1 1c3 3 6 5 9 7l-2 5c-4 0-8-2-11-4h-1c3 3 8 5 12 6l-2 6c-4 0-9-2-13-3 4 4 9 3 12 6 1 1 0 5 0 6l-10-1c-2 0-3 1-5 0 2-11 4-20 9-30 1-2 2-5 4-7 1-1 2-2 2-4zm-170 10v-6h3c0 2 0 5 1 7l-1 1c-1 2 0 4-1 6 0 1 1 1 0 2v7l2 1h1 0c5-1 11 0 16 0v6h-18v10h1c2 1 7 0 7 1h-8v5 16c0 7-1 44 1 46l-1 1c-1 2 0 7 0 9v22c0 3-1 8 0 12h0v1l-3 1v-17-33-98z" class="B"></path><path d="M483 508c-2-2 0-2-1-4l1-1h6c2 1 3 0 5 1h36 16l-8 6v2h-2 0l-1 1-5 4-1-3-46 1c-1-1 0-5 0-7z" class="J"></path><path d="M530 504c3 1 6 0 9 1l-1 1c-4 0-10-1-14-1h-15-25-1l1-1h10 36z" class="G"></path><path d="M483 508h14 25c3 0 11-1 14 1h-1c-7 2-15 1-22 1h-29l-1-2z" class="D"></path><path d="M483 508h0l1 2h29c7 0 15 1 22-1l2 1-2 2h1 0l-1 1-5 4-1-3-46 1c-1-1 0-5 0-7z" class="F"></path><defs><linearGradient id="K" x1="580.601" y1="559.985" x2="553.201" y2="547.577" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#4e4d4d"></stop></linearGradient></defs><path fill="url(#K)" d="M557 541v-1c2 0 3-1 4-2-1 0-1-1-2-1v-1l3 2 29 27h0l-2-1c-2-1-3-3-5-5-1 0-2 1-3 1-1 1 0 1-1 1h-1l-1 1c-1 1-2 1-2 1l-1 1c-4-1-7 2-10 1v-2c-3 0-6 1-9 1-2 0-5-1-8-1-3 1-7 0-10 0 1-1 2-1 3-1 1-1 2-2 4-2-1-1-2-1-2-1 1-1 3-2 4-3s1-2 2-3h-1v-2c3-2 6-4 8-5v-3-1-1h1z"></path><path d="M546 559l4-2 1 2 5-2v1c0 1 0 2-1 3h1 1c-1 1-2 1-4 1v-1c0-2-3-1-5-1-1 0-1-1-2-1z" class="M"></path><defs><linearGradient id="L" x1="558.906" y1="544.669" x2="549.027" y2="551.737" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#696969"></stop></linearGradient></defs><path fill="url(#L)" d="M556 546l3-1h2c-1 2-7 5-9 6l-3 2h-1v-2c3-2 6-4 8-5z"></path><path d="M545 560l1-1c1 0 1 1 2 1 2 0 5-1 5 1v1c-1 0-3 0-5 1-3 1-7 0-10 0 1-1 2-1 3-1 1-1 2-2 4-2z" class="G"></path><defs><linearGradient id="M" x1="556.743" y1="554.078" x2="546.243" y2="555.307" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#M)" d="M549 553l3-2 2 2 2-1h1v1c-2 1-4 3-7 4l-4 2-1 1c-1-1-2-1-2-1 1-1 3-2 4-3s1-2 2-3z"></path><path d="M549 553l3-2 2 2-3 2-2-1-2 2c1-1 1-2 2-3z" class="M"></path><path d="M640 225h0l3 1c-3 5-5 9-7 14l-2 5h0c-1-1-1-2-1-4-5-1-13 0-19 0-2 0-6-1-9 0l-1 9-4 12c0 1-1 4-2 6-1 3-1 5 0 8 1 5 3 9 4 14 1 3 2 5 2 7-1-2-2-5-2-7-1-3-3-6-4-9-1 10-5 21-12 28 3-7 6-13 9-21 1-3 1-6 2-8 0-3-1-5-1-7-7 15-19 30-34 38l-1-1c5-3 11-7 16-11 7-8 15-19 19-29 2-5 3-10 5-15 1-4 2-9 3-13 0-4 0-9 2-12 0-1 1-1 1-2 2-1 2-2 4-2l29-1z" class="D"></path><path d="M640 225h0l3 1c-3 5-5 9-7 14l-2 5h0c-1-1-1-2-1-4 2-5 4-10 7-14v-1-1z" class="F"></path><path d="M480 551c2 5 0 10 0 16h0v3c1 4 1 8 0 12h0v2l-1 1c-1 1-5 0-7 0h0c-3 0-5 1-8 0-1 0-2-1-2 0h0v5 7h2 6 4 1c1 0 2 0 4 1h1l-1 1-5 1-13-1 1-1c-2-2-1-39-1-46l1-1h12c2 0 4 1 6 0z" class="J"></path><path d="M479 598h1l-1 1-5 1-13-1 1-1h17zm1-14h-1c-3 0-14 1-16-1h0l17-1v2z" class="D"></path><defs><linearGradient id="N" x1="550.74" y1="542.811" x2="535.858" y2="543.746" xlink:href="#B"><stop offset="0" stop-color="#7e7e7e"></stop><stop offset="1" stop-color="#9d9c9b"></stop></linearGradient></defs><path fill="url(#N)" d="M554 532c1 1 2 1 3 1v1l1 1 1 1v1c1 0 1 1 2 1-1 1-2 2-4 2v1h-1v1 1 3c-2 1-5 3-8 5v2h1c-1 1-1 2-2 3s-3 2-4 3c0 0 1 0 2 1-2 0-3 1-4 2-1 0-2 0-3 1h-10-4l1-1c3-1 5-3 7-4-2-1-5 2-9 2-1 0-3 2-4 2-1 1-4 0-5 0h-11c1 0 1 0 2-1 6-2 11-7 16-8l2-2c1 0 9-5 10-6s1-2 2-3h0v-1c-2 1-6 4-9 4l9-6c2 0 3-2 5-2 1-1 2-1 3-2h0 1c3-1 7-3 10-3z"></path><path d="M558 535l1 1v1c1 0 1 1 2 1-1 1-2 2-4 2v1c-1-3-4 0-6-1l6-4 1 1v-1-1z" class="G"></path><path d="M554 532c1 1 2 1 3 1v1l-1 1v-1c-1 1-1 0-1 1-1 3-9 5-11 6 2-1 3-3 4-4h0c2-1 3-2 5-3h-1-2c-1 1-4 1-5 1h-1c3-1 7-3 10-3z" class="N"></path><path d="M544 535h1c1 0 4 0 5-1h2 1c-2 1-3 2-5 3h0c-5 2-9 6-15 8 1-1 1-2 2-3h0v-1c-2 1-6 4-9 4l9-6c2 0 3-2 5-2 1-1 2-1 3-2h0 1z" class="T"></path><defs><linearGradient id="O" x1="551.221" y1="536.982" x2="540.695" y2="553.65" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#8b8b8b"></stop></linearGradient></defs><path fill="url(#O)" d="M551 540c2 1 5-2 6 1h-1l-5 3c-3 2-7 6-10 8l-2-1-1 1c-1 0-2-1-3-1 5-4 11-8 16-11z"></path><path d="M521 553c2 0 2 0 3-1h2c1-1 1-1 2-1l4-2-14 11c3-1 5-1 7-3 1-1 3-2 5-2l-7 5c-1 0-3 2-4 2-1 1-4 0-5 0h-11c1 0 1 0 2-1 6-2 11-7 16-8z" class="R"></path><path d="M505 561c1 0 3 1 3 0 4-2 8-2 11-3h1l-6 4h-11c1 0 1 0 2-1z" class="E"></path><defs><linearGradient id="P" x1="555.031" y1="547.853" x2="545.025" y2="545.858" xlink:href="#B"><stop offset="0" stop-color="#4a4a4a"></stop><stop offset="1" stop-color="#6a6969"></stop></linearGradient></defs><path fill="url(#P)" d="M556 541v1 1 3c-2 1-5 3-8 5v2h1c-1 1-1 2-2 3s-3 2-4 3c0 0 1 0 2 1-2 0-3 1-4 2-1 0-2 0-3 1h-10-4l1-1c3-1 5-3 7-4-2-1-5 2-9 2l7-5 1-1c1-1 3-2 4-3 1 0 2 1 3 1l1-1 2 1c3-2 7-6 10-8l5-3z"></path><path d="M535 551c1 0 2 1 3 1l1-1 2 1c-1 1-2 1-3 2s-4 4-6 4c-2-1-5 2-9 2l7-5 1-1c1-1 3-2 4-3z" class="Q"></path><path d="M535 551c1 0 2 1 3 1l1-1 2 1c-1 1-2 1-3 2-1-1-2-1-4 0h-3c1-1 3-2 4-3z" class="O"></path><defs><linearGradient id="Q" x1="544.486" y1="549.184" x2="534.014" y2="562.816" xlink:href="#B"><stop offset="0" stop-color="#70706f"></stop><stop offset="1" stop-color="#a19fa1"></stop></linearGradient></defs><path fill="url(#Q)" d="M548 551v2h1c-1 1-1 2-2 3s-3 2-4 3c0 0 1 0 2 1-2 0-3 1-4 2-1 0-2 0-3 1h-10-4l1-1h2c1 0 3-2 4-2 4-2 7-5 11-7 2-1 4-2 6-2z"></path><path d="M548 553h1c-1 1-1 2-2 3s-3 2-4 3c-3 0-5 1-7 1l12-7z" class="N"></path><path d="M543 559s1 0 2 1c-2 0-3 1-4 2-1 0-2 0-3 1h-10l8-3c2 0 4-1 7-1z" class="O"></path><defs><linearGradient id="R" x1="649.462" y1="310.418" x2="657.954" y2="259.662" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#383838"></stop></linearGradient></defs><path fill="url(#R)" d="M654 257c2 2 4 5 5 8 0 1 1 2 1 3 0-1 0-2 1-3 0-1 0-1 1-2h0c0 1 1 2 1 3l1-1 2-1c2 5 3 11 4 16l-2 8c-2 7-9 13-14 16-3 2-8 5-11 5h-5l1-2v-2-1h0l-1-1c1-2 1-2 2-3h-1-1-1c-1 1-1 1-2 1-2 1-7 0-8 0h-3l-1-1h-2c-2-1-4-1-5-3l-1-1c-2 0-2 0-3-1l1-1 1 1c1 1 1 0 2 1h1c1 1 3 2 4 2h1c1 1 0 1 2 1 1 1 2 0 3 0 1 1 3 1 5 1v-1h3c3-1 6-2 9-4h1l1-1 2-2h1v-1l2-2v-1c1-1 1-1 1-2l1-3c2-5 2-9 1-13 1-2 0-4 0-6s0-3-1-5c0-1 0-1 1-2z"></path><path d="M643 226c5-1 12-1 17-1h37l-11 16h-8c-2-1-5-1-7-1h-10-8c-2 0-7 0-8 1-3 0-6-1-9-1 2-5 4-9 7-14z" class="D"></path><path d="M503 741l1 1h10 7v9 5h-1l1 3h0v10h1c1 7 5 12 6 18h0c-3-1-6-4-9-5-1 0-2 0-3-1-1 0-3-1-4 0-1 0-3-1-5-1l-8 4h-1 0-1c1-5 4-8 6-13h0c1-3 0-6 0-8v-21-1z"></path><path d="M504 742h10-1c-2 2-4 2-6 2-2-1-2-1-3-2z" class="J"></path><path d="M515 755l1-1h1v2c-2 2-1 4-2 6 0 1-1 1-2 1 1-2 0-5 1-6 0-1 1-2 1-2z" class="H"></path><path d="M509 768l-1-1c-1 0-1 0-1-1-1-1 0-6 0-7h2v-2c1 3 1 7 0 11z" class="J"></path><path d="M509 757v-2c1 0 2 0 2-1l2-1c1 0 1-1 2 0-1 1-1 1-2 1l-1 1h3s-1 1-1 2c-1 1 0 4-1 6 0 2 0 3 1 5h0c-1 1-1 3-1 4h-1l1 1v6l8 3 2 1s2 1 3 1h0c-2-5-4-9-5-13l-1-1 1-11h0v10h1c1 7 5 12 6 18h0c-3-1-6-4-9-5-1 0-2 0-3-1-1 0-3-1-4 0-1 0-3-1-5-1l-8 4h-1 0c3-3 8-4 11-5 0-2 0-5 1-7v-1l-1-1c0-1 1-1 1-2h-1c1-4 1-8 0-11z" class="B"></path><path d="M515 762h2c0-1 0-2 1-3s0-2 1-3h1l1 3-1 11 1 1c1 4 3 8 5 13h0c-1 0-3-1-3-1l-2-1c-3-1-5-2-8-3v-6l-1-1h1c0-1 0-3 1-4h0c-1-2-1-3-1-5 1 0 2 0 2-1z"></path><path d="M513 773c1 0 2 0 3 1h1c-1 0 0 1-1 1l-2 2c1 1 2 1 3 1l2-1c1 0 2 2 2 3v1h1c1 1 1 1 1 2l-2-1-8-3v-6z" class="J"></path><path d="M515 762h2c0-1 0-2 1-3s0-2 1-3h1l1 3-1 11v-1c-1-2 0-4 0-6-2 0-3 1-4 2s-1 1-1 2v1h0c1 2 2 3 2 5v1h-1c-1-1-2-1-3-1l-1-1h1c0-1 0-3 1-4h0c-1-2-1-3-1-5 1 0 2 0 2-1z" class="K"></path><path d="M413 784c-1 1-1 2-1 4h0c-3-1-7-2-11-2l11 5 1 1h0-1-3c-2 2-4 7-4 10 0 1 0 1-1 1l-9-2c4 2 7 5 10 8-2 0-4-2-6-2 1 1 1 2 1 3l-8 9c-1 1-2 3-3 4l-2 1c-1 1-1 1-1 2h0c-1 1-2 2-3 2h-1l-2 2h-1l1 1h0c-11 5-20 11-32 14 4-4 11-7 15-10 20-16 34-36 41-60 1-1 2 0 3 0l11 1c9 1 19-1 28 0-3 2-22 1-26 1l-2 5h0v-3h-1l-2 5h-2z" class="C"></path><path d="M413 784c0-2 1-5 2-7h3v2h-1l-2 5h-2z" class="F"></path><path d="M425 421c1 2 1 6 1 8v1l1-6c0-2 0-3 1-4h0 8c0 3 0 7 1 10h7 2 4 6 5l1 1v1c0 1 1 2 1 3v5l-1 1-1-1h-20c-1-1-3 0-4 0h-9-1c0 4-1 9 0 13-1 7 0 15 0 22v42 129 37c0 6 0 13-1 19l-1-1V421z" class="Q"></path><path d="M456 430h5l1 1v1c0 1 1 2 1 3v5l-1 1-1-1h-20c-1-1-3 0-4 0h-9-1v-9h1c2-1 4 0 6 0l12-1h4 6z" class="B"></path><path d="M427 440v-9h1l1 2h0v1c1 1 1 2 0 3 2 1 7 0 10 1-2 0-10 0-11 1v1h-1z" class="G"></path><path d="M460 437l1 3h-20c-1-1-3 0-4 0h-9v-1c1-1 9-1 11-1 6 0 13-1 19 0 0 0 1 0 1-1h1z" class="J"></path><defs><linearGradient id="S" x1="444.084" y1="435.414" x2="445.916" y2="427.586" xlink:href="#B"><stop offset="0" stop-color="#080608"></stop><stop offset="1" stop-color="#323130"></stop></linearGradient></defs><path fill="url(#S)" d="M456 430h5l1 1v1c0 1 1 2 1 3v5l-1 1-1-1-1-3h-1c0-1 0-3-1-4h-29 0l-1-2c2-1 4 0 6 0l12-1h4 6z"></path><path d="M462 432c0 1 1 2 1 3v5l-1 1-1-1-1-3v-2c0-2 0-2 2-3z" class="B"></path><defs><linearGradient id="T" x1="625.203" y1="260.104" x2="639.177" y2="231.181" xlink:href="#B"><stop offset="0" stop-color="#717072"></stop><stop offset="1" stop-color="#a4a3a2"></stop></linearGradient></defs><path fill="url(#T)" d="M633 241c0 2 0 3 1 4h0l2-5c3 0 6 1 9 1 1-1 6-1 8-1h8 10c0 2-1 2-1 3l-1 2v1c0 1 0 1-1 2-1 2-2 4-2 7-1 0-1 1-2 2v1l1 1 1 5-2 1-1 1c0-1-1-2-1-3h0c-1 1-1 1-1 2-1 1-1 2-1 3 0-1-1-2-1-3-1-3-3-6-5-8-1 1-1 1-1 2 1 2 1 3 1 5s1 4 0 6c-1-5-3-9-6-13-5-5-11-8-17-8-8 0-14 2-20 8-1 1-1 2-2 3v-3h-1c-1-1 0-1-1-1h-1c-1 2-1 2-2 3l-4 3 4-12 1-9c3-1 7 0 9 0 6 0 14-1 19 0z"></path><path d="M643 246v-1l1-1 2 1c1-1 1-1 3-1 1 0 1 1 1 2h-2c-2 1-2 1-3 0h-1v-1l-1 1z" class="O"></path><path d="M649 256c0-1 0-1 1-2h1l3 3h0c-1 1-1 1-1 2 1 2 1 3 1 5-2-3-3-6-5-8z" class="M"></path><path d="M643 246l1-1v1h1c1 1 1 1 3 0h2v2c1 0 1 0 2 1l1-1 1 1-1 1c-1 0-2-1-3 0-1 0-2 0-3-1l-1-1-1 1 1 1h0v4l-3-3c-1 0-2-1-3-1 1 0 2-1 2-1l1-3z" class="P"></path><defs><linearGradient id="U" x1="607.157" y1="254.189" x2="605.81" y2="249.186" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#U)" d="M604 250h0c1 0 2-1 3-1h1l1 6h1l2-1h0c0 1-1 2-1 3-1 1-1 2-2 3v-3h-1c-1-1 0-1-1-1h-1c-1 2-1 2-2 3l-4 3 4-12z"></path><defs><linearGradient id="V" x1="659.49" y1="258.956" x2="657.582" y2="248.441" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#767575"></stop></linearGradient></defs><path fill="url(#V)" d="M650 246c4 0 6 1 9 4h0c2 2 4 5 5 7v1h0l-2 2c0-1 0-1-1-2h0c-1-2-1-3-3-4-1 0-1 0-2-1h-1c-1-1-1-2-2-3l1-1-1-1-1 1c-1-1-1-1-2-1v-2z"></path><path d="M661 240h10c0 2-1 2-1 3l-1 2v1c0 1 0 1-1 2-1 2-2 4-2 7-1 0-1 1-2 2-1-2-3-5-5-7 1-1 1-4 1-6h0c0-1 0-2 1-4z" class="Q"></path><path d="M659 250c1-1 1-4 1-6l1 1v4l1 1c1 2 2 4 4 5-1 0-1 1-2 2-1-2-3-5-5-7z" class="O"></path><defs><linearGradient id="W" x1="658.746" y1="265.358" x2="656.338" y2="249.204" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#6e6d6c"></stop></linearGradient></defs><path fill="url(#W)" d="M646 250h0l-1-1 1-1 1 1c1 1 2 1 3 1 1-1 2 0 3 0 1 1 1 2 2 3h1c1 1 1 1 2 1 2 1 2 2 3 4h0c1 1 1 1 1 2l2-2h0l1 1 1 5-2 1-1 1c0-1-1-2-1-3h0c-1 1-1 1-1 2-1 1-1 2-1 3 0-1-1-2-1-3-1-3-3-6-5-8h0l-3-3h-1c-1 1-1 1-1 2l-3-2v-4z"></path><path d="M646 250h1l5 3-1 1h-1c-1 1-1 1-1 2l-3-2v-4z" class="S"></path><defs><linearGradient id="X" x1="659.521" y1="314.127" x2="686.869" y2="283.802" xlink:href="#B"><stop offset="0" stop-color="#0c0b0c"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#X)" d="M671 240c2 0 5 0 7 1h8c-3 7-6 14-7 21 0 8-1 16 0 23 1 10 4 18 6 27h0-1c-1-1-1-1-3-1v-1c-2 0-5 1-7 1-6 1-15 2-20 0l6-5c1-1-1 0 1-1l1-1c1-1 1-1 1-2l2-2c1-1 2-3 3-5v-1l1-1v-1l-2 3c-1 2-4 6-6 6 3-4 6-7 8-12l-1-1 2-8c-1-5-2-11-4-16l-1-5-1-1v-1c1-1 1-2 2-2 0-3 1-5 2-7 1-1 1-1 1-2v-1l1-2c0-1 1-1 1-3z"></path><path d="M668 263c2 2 3 3 4 6v1c-1 2-1 3-1 5 0 3 0 11-2 14l-1-1 2-8c2-4 0-12-2-17z" class="N"></path><path d="M665 259c2 1 2 1 3 3v1c2 5 4 13 2 17-1-5-2-11-4-16l-1-5z" class="G"></path><defs><linearGradient id="Y" x1="668.286" y1="284.994" x2="676.557" y2="272.738" xlink:href="#B"><stop offset="0" stop-color="#4e4d4d"></stop><stop offset="1" stop-color="#7a7979"></stop></linearGradient></defs><path fill="url(#Y)" d="M672 270c1 0 1 1 3 1v3 5c0 2-1 6-2 9h-1v-1c-1-1 0-4 0-6v-11z"></path><defs><linearGradient id="Z" x1="679.362" y1="270.128" x2="674.053" y2="240.542" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#Z)" d="M671 240c2 0 5 0 7 1h8c-3 7-6 14-7 21 0-1-1-1 0-2l1-7c-2 2-2 7-3 9 0 2-1 4-1 6 0 4 0 7-1 11v-5-3c-2 0-2-1-3-1v-1c-1-3-2-4-4-6v-1c-1-2-1-2-3-3l-1-1v-1c1-1 1-2 2-2 0-3 1-5 2-7 1-1 1-1 1-2v-1l1-2c0-1 1-1 1-3z"></path><path d="M666 255c0-3 1-5 2-7l-1 6c-1 1-1 2-1 3v1c1 0 2 1 2 2h2c2 2 2 6 2 8v1c-1-3-2-4-4-6v-1c-1-2-1-2-3-3l-1-1v-1c1-1 1-2 2-2z" class="P"></path><path d="M627 254c5 0 8 1 12 3 5 3 8 7 9 12l1 1h0c0 2 0 6-1 7v-2l-1 1v1c-3 6-8 10-13 13-6 1-11 1-16-3-4-2-7-7-7-11-1-5-1-10 3-14 3-5 8-7 13-8z" class="C"></path><path d="M638 263c2 0 3 0 4 1 2 4 0 6-1 9 0-4-1-7-3-10z" class="B"></path><path d="M618 270c1 0 2 0 3 1l2 1c2 1 5 2 7 2h1c-2 1-4 1-6 1-1 0-1-1-2-1h-1v-1h-3v-1c-1 1-1 1-1 2-1 0-1 1 0 2 1 3 3 7 7 8 2 1 5 0 7 0 2-1 4-2 5-3s1-1 2-1c-2 2-3 4-5 5-4 2-8 2-11 0a10.85 10.85 0 0 1-8-8s1 0 2-1v-5l1-1z" class="F"></path><path d="M631 264c2 0 5 1 6 2 0 1 1 3 2 4 1 2 1 6 1 9l-1 1c-1 0-1 0-2 1s-3 2-5 3c-2 0-5 1-7 0-4-1-6-5-7-8-1-1-1-2 0-2 0-1 0-1 1-2v1h3v1h1c1 0 1 1 2 1 2 0 4 0 6-1 1 0 1-1 2-1 1-2 2-3 2-5-1-2-2-2-4-3v-1z" class="M"></path><path d="M618 276h3c3 0 6 1 10 1h2c1 1 2 2 1 4v1l-2 2c-2 0-5 1-7 0-4-1-6-5-7-8z" class="B"></path><path d="M615 277c-2-4-2-8 1-12 1-3 5-6 9-6 5-1 9 1 13 4 2 3 3 6 3 10 0 2 0 4-1 6 0-3 0-7-1-9-1-1-2-3-2-4-1-1-4-2-6-2v1c2 1 3 1 4 3 0 2-1 3-2 5-1 0-1 1-2 1h-1c-2 0-5-1-7-2l-2-1c-1-1-2-1-3-1l-1 1v5c-1 1-2 1-2 1z"></path><path d="M626 264c1-1 3-1 5 0v1c-2 0-4 0-6 1v-1s0-1 1-1z" class="H"></path><path d="M618 270c0-1 1-2 2-3 1 0 2-1 3-1 1-1 1-2 3-2-1 0-1 1-1 1v1c-1 1-2 2-2 3v3l-2-1c-1-1-2-1-3-1z" class="K"></path><path d="M625 266c2-1 4-1 6-1 2 1 3 1 4 3 0 2-1 3-2 5-1 0-1 1-2 1h-1c-2 0-5-1-7-2v-3c0-1 1-2 2-3z"></path><defs><linearGradient id="a" x1="461.343" y1="324.209" x2="452.255" y2="330.637" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#a)" d="M441 302l7 7h1c1-1 1-2 1-2l8 8c1 1 3 2 3 3 0 2 2 4 3 6 4 3 8 7 10 11h0c2 2 3 4 5 6 1 0 1 0 2 1v1 2l-1 1v-1c-1 1 0 2 0 3h0c1 2 1 4 1 5v17l-4-2c-3-1-5-4-6-6-1-1-3-3-4-3h-1c-1-3-4-8-7-9-2-2-3-4-4-6l-5-5h0c0-2-3-4-4-6l1-1s1-1 1 0h1c0-2 0-4-1-5-1-2-2-4-3-4h-1v-2-1c-1-3-1-5-1-8-1-2 0-6-2-8v-1-1z"></path><path d="M454 333l2-1c1 1 1 3 2 4v1l-1 1c-1-1-2-4-3-5z" class="N"></path><path d="M450 326v-2h0c3 2 4 5 6 8l-2 1c-1-2-2-4-4-7z" class="S"></path><path d="M468 347c1 0 3 0 4 1 1 0 0 2 2 1l-1-1 1-1c1 1 1 2 2 4 1 1 1 0 1 1-1 0-2-1-2-2l-2 1c-2-1-3-2-5-2h0v-2h0z" class="M"></path><path d="M450 326c2 3 3 5 4 7 1 1 2 4 3 5v3c0 1 0 1-1 1v-1c-1-4-5-5-6-9v-2l-1-1c1-1 1-2 1-3z" class="G"></path><path d="M457 338l1-1c1 1 2 3 3 5s3 4 4 6c1 1 1 2 1 4l1 1-1 1 1 1v1l-1 1h0c1 1 1 1 0 2-1-3-4-8-7-9-2-2-3-4-4-6v-1l-2-3v-1l3 2v1c1 0 1 0 1-1v-3z" class="S"></path><path d="M455 343l-2-3v-1l3 2v1c1 0 1 0 1-1l1-1c1 1 1 1 1 3h0c-1 1-1 0-1 2h0v1h-1l-2-3z" class="P"></path><path d="M449 309c1-1 1-2 1-2l8 8c1 1 3 2 3 3 0 2 2 4 3 6 4 3 8 7 10 11h0c2 2 3 4 5 6 1 0 1 0 2 1v1 2l-1 1v-1l-12-14c-2-3-6-5-8-8-1-1-2-3-3-4l-1-1-1-1c-1-1-1-2-2-3l-5-5h1z" class="M"></path><defs><linearGradient id="b" x1="474.318" y1="350.146" x2="473.723" y2="365.78" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#b)" d="M465 348l2-5 1 1v3h0v2h0c2 0 3 1 5 2l2-1c0 1 1 2 2 2 1 2 2 1 4 1v17l-4-2c-3-1-5-4-6-6-1-1-3-3-4-3h-1c1-1 1-1 0-2h0l1-1v-1l-1-1 1-1-1-1c0-2 0-3-1-4z"></path><path d="M573 505h42c0 1-2 5-2 6-1 3-2 6-2 8-2 1-3 1-4 2 0 1-1 2-1 3s-1 2-2 4l-1 2c0 1-1 2-2 3 1-2 1-3 1-5l-1-1 1-3c-2 2-4 5-4 7l1 1 1-1h1l-3 6c-2 0-3 1-4 2h-1l1-4-2 1c-1 0-2-1-3-2h-3c0 1 1 1 0 2 1 1 1 1 1 3l-23-21v-1-1h1l-5-6c1 0 2 0 2-1h0c2-6 8-1 11-4z" class="C"></path><path d="M562 509c2 0 3 0 4 1-1 1-1 2-1 4 2 0 2-2 3-1v1c0 1 0 3 1 4l-4-2-5-6c1 0 2 0 2-1z" class="E"></path><path d="M588 532c1-1 3-2 4-3h1c-1 1-2 2-2 3 2 0 4-2 6-3 1-1 4-6 6-7h0l-1 2c-2 2-4 5-4 7l1 1 1-1h1l-3 6c-2 0-3 1-4 2h-1l1-4-2 1c-1 0-2-1-3-2 0 0-1-1-1-2z" class="I"></path><defs><linearGradient id="c" x1="584.578" y1="527.58" x2="568.427" y2="527.409" xlink:href="#B"><stop offset="0" stop-color="#161414"></stop><stop offset="1" stop-color="#2d302e"></stop></linearGradient></defs><path fill="url(#c)" d="M564 518v-1-1h1l4 2 11 9c2 2 5 5 7 6l1-1c0 1 1 2 1 2h-3c0 1 1 1 0 2 1 1 1 1 1 3l-23-21z"></path><path d="M538 510h1l8 8 3 2c1 1 2 2 3 4 1 1 1 2 1 4l3 3v2c-1 0-2 0-3-1-3 0-7 2-10 3h-1 0c-1 1-2 1-3 2-2 0-3 2-5 2l-9 6c3 0 7-3 9-4v1h0c-1 1-1 2-2 3s-9 6-10 6l-2 2c-5 1-10 6-16 8-1 1-1 1-2 1h-13c-2 0-4 0-5 1h-2v-2l11-10 30-28 6-6 5-4 1-1h0 2v-2z" class="C"></path><path d="M536 512c1 1 2 1 3 2-1 1-3 2-3 3l-2 2c-2 1-4 2-5 3l-1-2v1c-1 1-2 2-4 2l6-6 5-4 1-1z" class="B"></path><path d="M536 512c1 1 2 1 3 2-1 1-3 2-3 3h-2c0-2 1-1 1-2v-2l1-1z" class="C"></path><path d="M526 545c3 0 7-3 9-4v1h0c-1 1-1 2-2 3s-9 6-10 6l1-1 3-2-8 3c-6 2-12 7-19 8 4-2 7-5 11-6 2 0 4-1 5-2 3-2 7-5 10-6z" class="I"></path><path d="M538 510h1l8 8 3 2c1 1 2 2 3 4 1 1 1 2 1 4l3 3v2c-1 0-2 0-3-1l-1-1c0-1 0-1-1-2l-3 1 1-1v-1c0-1-1-1-1-2h0v-2l-2-1c-1 1-1 0-1 1-1 1-1 1-2 1h-1 0c1-1 1-2 2-2h1l-1-1c-2-3-3-2-6-2h0l2-1c-1-2-2-2-2-3l-2 1h-1c0-1 2-2 3-3-1-1-2-1-3-2h0 2v-2z" class="E"></path><path d="M538 510h1l8 8 3 2c1 1 2 2 3 4 1 1 1 2 1 4l-15-14c-1-1-2-1-3-2h0 2v-2z" class="L"></path><path d="M511 553c-6-2-9 2-14 4-1 1-2 1-4 1v-1c8-2 11-8 18-12 3-2 6-3 9-5l2-2 1 1c-1 1-3 2-5 3l-9 6-3 2h1c4 0 8-4 13-6l1 1c1-1 1-1 2-1 2-2 4-2 6-3 2 0 4-1 6-2l-9 6c-3 1-7 4-10 6-1 1-3 2-5 2z" class="E"></path><path d="M549 526h0c0 1 1 1 1 2v1l-1 1 3-1c1 1 1 1 1 2l1 1c-3 0-7 2-10 3h-1 0c-1 1-2 1-3 2-2 0-3 2-5 2-2 1-4 2-6 2-2 1-4 1-6 3-1 0-1 0-2 1l-1-1 20-14h1c3-2 5-3 8-4z" class="I"></path><path d="M540 530h1c2 0 3 0 4 1l-1 1-4 2-1-1 1-1v-2z" class="T"></path><path d="M549 526h0c0 1 1 1 1 2v1c-2 0-3 1-5 2-1-1-2-1-4-1 3-2 5-3 8-4z" class="O"></path><path d="M461 440l1 1 12 13c2 2 3 5 5 6h2l-1 10v7h-4l-33-1v20 21 93l-1 31c0 5 1 11 0 16h0l-1-156v-41-10-10h20z" class="I"></path><path d="M443 467c5-1 11-1 17 0h6l1 1c0 1-1 1-2 1-2 1-20 1-22 0v-2z" class="K"></path><path d="M443 454h9c1 0 3 0 3 1 1 0 3 2 4 3 4 3 8 8 12 10 1 0 2 1 2 2h1c1 1 3 2 4 3 1-1 1-1 1-2l1-1v7h-4c-1-2-3-4-4-5h-2v-2c-1-1-2-1-3-2l-1-1h-6c-6-1-12-1-17 0-1-4 0-9 0-13z" class="O"></path><defs><linearGradient id="d" x1="472.459" y1="448.763" x2="450.338" y2="461.491" xlink:href="#B"><stop offset="0" stop-color="#838382"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#d)" d="M461 440l1 1 12 13c2 2 3 5 5 6h2l-1 10-1 1c0 1 0 1-1 2-1-1-3-2-4-3h-1c0-1-1-2-2-2-4-2-8-7-12-10-1-1-3-3-4-3 0-1-2-1-3-1h-9l-1-1v7h-1v-10-10h20z"></path><path d="M473 467c2 0 4 1 6 3v1c0 1 0 1-1 2-1-1-3-2-4-3l-1-3z" class="P"></path><path d="M442 452l1-3c3 0 11-1 13 1l2 2h-16z" class="F"></path><path d="M442 452h16c3 6 10 10 15 15l1 3h-1c0-1-1-2-2-2-4-2-8-7-12-10-1-1-3-3-4-3 0-1-2-1-3-1h-9l-1-1v-1h0z" class="B"></path><path d="M407 249c4-3 8-8 13-9l-1 175h-9v-80-43-24c0-7-1-13-3-19zm90-111l14-18 18 21c0 2-1 3 0 4 0 3 2 8 2 11 0 1-1 2-1 4v1 4 20 1l-7-1c-1 0-2 0-4-1v-6c0-4 0-8-1-12 0-2-1-3-2-4-2-1-5-1-7 0-3 0-4 2-5 4 1 5 0 11 0 15l-1 3c-1 1-1 1-2 1h-3c-2 0-4 0-6 1v26c0 3 1 8 0 11h-1c-2-2-1-3-3-5 0 0-1 0-1 1-1 1 0 3-1 5-1-2-1-4-1-6v-14l-1-29c0-8 0-16 3-24 3-5 6-9 10-13z" class="C"></path><path d="M519 184h2v-4l-1-2c-1-5 0-10 0-15l-1-1c0-1 0-3 1-4l1 1v3 2h0c-1 3 0 6 1 8v1 6h1v-8c0 2-1 6 0 8v1c0 1 0 1 1 2v1 2h0-1c-1 0-2 0-4-1z" class="D"></path><path d="M498 168c2-5 1-9 3-14v1 4 6c2-4 3-8 4-13 0 3 0 8-1 11l-1 3h1c1 5 0 11 0 15l-1 3c-1 1-1 1-2 1h-3c0-3-1-6 0-9v-2-6z" class="B"></path><path d="M502 174v-3l1-1c1 4 0 8 1 11l-1 3c-1 1-1 1-2 1 1-2 1-9 1-11z" class="C"></path><path d="M498 168c1 0 1 0 2 1v3 1h1l1 1c0 2 0 9-1 11h-3c0-3-1-6 0-9v-2-6z" class="I"></path><path d="M529 145c0 3 2 8 2 11 0 1-1 2-1 4v1 4 20 1l-7-1h1 0v-2-1c-1-1-1-1-1-2v-1c-1-2 0-6 0-8-1-2-1-2 0-3 1 0 0 4 2 5l-1-2h1c0-2 1-2 0-3v-6h1v3c0-3-1-6 0-9v-10h1 0 0l1 6v-1c0-2 0-4 1-5v-1z" class="E"></path><path d="M530 185h-2l-1-1v-16c0-3-1-6 0-10v2c1 2 0 4 1 6v4h1c0-2 0-3 1-5v20z" class="I"></path><path d="M487 151c3-5 6-9 10-13 0 4 0 10-2 13v5c-1 1 0 4 0 6 0 1-1 2-1 3v12 7l-1 1h0v-6-12c-1 1-1 2-1 4h0c0 4 1 9-1 13h0v-5h-1v19 10c-1-3 0-7 0-11-1-10 0-20-1-30-1-1-1-4-1-5h1v-4c-2 3 0 8-1 11-1-1-1-2-1-4v-7c0-3 1-4 0-7z" class="E"></path><path d="M489 162h1v-5c0-4 1-6 2-9h0c0 5-1 9-1 14 0 7 1 15 0 22h0v-5h-1v19 10c-1-3 0-7 0-11-1-10 0-20-1-30-1-1-1-4-1-5h1z" class="I"></path><path d="M485 218v-14l-1-29c0-8 0-16 3-24 1 3 0 4 0 7v7c0 2 0 3 1 4 1-3-1-8 1-11v4h-1c0 1 0 4 1 5 1 10 0 20 1 30 0 4-1 8 0 11 0 4 1 10 0 13l1 2c-2-2-1-3-3-5 0 0-1 0-1 1-1 1 0 3-1 5-1-2-1-4-1-6z" class="R"></path><path d="M489 167c1 10 0 20 1 30 0 4-1 8 0 11 0 4 1 10 0 13l1 2c-2-2-1-3-3-5 0 0-1 0-1 1-1 1 0 3-1 5-1-2-1-4-1-6 1-1 1-5 1-7l3-44z" class="G"></path><path d="M486 211l1-3v4h2v-3c0 3 0 7 1 10v2h0l1 2c-2-2-1-3-3-5 0 0-1 0-1 1-1 1 0 3-1 5-1-2-1-4-1-6 1-1 1-5 1-7z" class="K"></path><path d="M521 457l1-4c1 1 1 1 1 2 0 6 4 12 6 18 1 1 2 4 3 6h2c1-2-1-5-1-6l-2-13c-1-2-2-5-2-7 2 5 3 11 4 16 1 3 3 7 3 10h125c-3 1-6 2-8 3-5 2-8 6-12 9-2-1-4-3-6-5-1 0-2-2-3-3l-4 3c-1 1-3 4-4 5h-3-9-35c-12 1-24 0-36 0-4 0-9 1-13 0h-32c-3 0-8 0-11 1 0 0-1 0-1 1-1-2 0-5 1-6 1-2 2-4 3-7l10-16v1l1-2c3 2 5 4 8 6v1c0 1 0 2-1 4 1 1 2 3 4 4h4v-3c-1-2-1-5-1-7 1-1 0-1 0-2v-1-1-1c0-1 0-1 1-2h0c-1-1-1-1-1-2l1-1c1 1 0 0 1 0v-1h6z" class="E"></path><path d="M544 489c1-2 5-6 7-8l1 1c-1 2-4 6-4 8 3 0 6-1 9 0h3c5 0 11 0 17 1-12 1-24 0-36 0-4 0-9 1-13 0h0c4-1 10-1 15-1l1-1z" class="I"></path><path d="M515 457h6c3 8 7 15 10 22h-17v-1-3c-1-2-1-5-1-7 1-1 0-1 0-2v-1-1-1c0-1 0-1 1-2h0c-1-1-1-1-1-2l1-1c1 1 0 0 1 0v-1z" class="J"></path><path d="M499 463c3 2 5 4 8 6v1c0 1 0 2-1 4 1 1 2 3 4 4-2 1-2 1-4 1l1 4c3-1 1-3 4-2l1 1c-1 2-5 3-4 6 1 0 1 0 3-1 1-2 2-5 5-7h0c1 2 0 2 1 4l3-3c1 1 0 2 1 4h-1v3c2 0 3-3 4-4s2-3 4-4c-1 3-4 6-3 8 1 0 2 0 3-2h1c0-1 2-3 3-4h4l-1 1 1 1 1-2h2c0 2-3 4-3 6l1 1c2-3 4-7 8-7 0 2-3 5-3 7h2l-1 1c-5 0-11 0-15 1h0-32c-3 0-8 0-11 1 0 0-1 0-1 1-1-2 0-5 1-6 1-2 2-4 3-7l10-16v1l1-2z" class="B"></path><path d="M499 463c3 2 5 4 8 6v1c0 1 0 2-1 4 1 1 2 3 4 4-2 1-2 1-4 1-5-1-12 0-16 1 3-5 5-10 8-15l1-2z" class="J"></path><path d="M410 417c4 0 25-1 27 1h-15v1h7l-1 1c-1 1-1 2-1 4l-1 6v-1c0-2 0-6-1-8l-1-1c-1 1-2 1-2 2-1 0-3 3-3 3-1 11 0 22 0 32v66 206h-9V417z" class="C"></path><path d="M591 266l-1 1c-10 21-28 38-49 45-19 7-40 6-57-3-23-11-37-30-45-54-2-4-2-9-4-14-1-4-3-8-4-11l1 3v11l-14-16-15 12c-5-8-11-11-19-14v-1l149 1c2 1 18 0 21 1h7c-1 2-2 2-2 4-2 2-6 8-9 9l-2 2h0c-14 2-29 1-44 1h-25-3c-2 0-4 1-6 0l-1 1 2 2-1 1h0c0-1-1-1-1-2v1 1c-1-1-2-5-4-7h-11c-1 3 5 14 7 17l4 7h0c-2-1-2-2-3-3 0-1-1-2-2-3 1 1 1 2 2 3v1l1 2c1 1 1 2 1 2-3-3-5-8-7-12s-4-13-7-15c0 0 0 1-1 1l-12 1c3 8 5 17 9 25 8 17 20 30 37 39s36 11 55 5c16-5 30-15 40-28 3-4 5-9 8-12 3-1 4-5 5-7l-1 2 1 1z" class="D"></path><path d="M450 239l-1-2v-2c0-2-2-5-1-7 2 2 2 3 2 5 1 3 1 5 3 8v-1l1-1v-3-2h0l1 2h0c0 2 0 3-1 4-1 3 5 14 7 17l4 7h0c-2-1-2-2-3-3 0-1-1-2-2-3 1 1 1 2 2 3v1l1 2c1 1 1 2 1 2-3-3-5-8-7-12s-4-13-7-15z" class="B"></path><defs><linearGradient id="e" x1="476.915" y1="251.726" x2="492.504" y2="232.492" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#e)" d="M465 240v-2h0c1 1 1 2 3 3h6c7-1 14-1 20-1h10v3h-25-3c-2 0-4 1-6 0l-1 1 2 2-1 1h0c0-1-1-1-1-2v1 1c-1-1-2-5-4-7z"></path><path d="M504 240h46l-2 2h0c-14 2-29 1-44 1v-3z" class="J"></path><path d="M440 347l4 1h4l1 1 1 6c1 2 1 3 2 4h1c5 3 9 10 12 13l13 15h0c1 1 3 2 3 4 0 0-1 1-1 2l1 67h-2c-2-1-3-4-5-6l-12-13 1-1v-5c0-1-1-2-1-3v-1l-1-1h-5-6-4-2-7c-1-3-1-7-1-10h-8 0l1-1h-7v-1h15c-2-2-23-1-27-1 2-1 6 0 8 0 5 0 34 0 36-1-1-2-2-2-3-2-1 1-5 1-6 1l-18-1v-4c0-4 1-10 0-13-1-2-1-2-2-2 2-1 3-1 5-1 5 0 10 1 15 0v-1l-1-1h-3c0-2 0-4 1-6h2c0 1 0 0 1 1v1-4c0-3 1-8 0-11h-3-1-1v-26z" class="T"></path><path d="M442 386h2v6h-3c0-2 0-4 1-6z" class="C"></path><path d="M440 347l4 1h4l-1 1h-4v1h3c0 1-1 2-2 3 1 2 2 3 3 4 1 0 1 1 1 2h-4s-1 0-1-1l-1 15h-1-1v-26z" class="E"></path><path d="M441 373v-15h1 1l-1 15h-1z" class="C"></path><path d="M451 414v-16-7h1l-1-1v2c-1 7-1 14-1 21h0v-15c1-2 0-3 0-5v-1c0-1 0-2 1-3v-3h0v-1-1l-2 8h0v-2-1-1c1-2 0-4 0-6v-4c1 1 0 1 1 1v1l1-1 2 2v-1c1 4 5 6 8 9 0 1 1 2 0 3 0 1 0 1-1 2v22c0 4-1 10 1 14h-5-6-4-2-7c-1-3-1-7-1-10h-8 0l1-1h-7v-1h15c-2-2-23-1-27-1 2-1 6 0 8 0 5 0 34 0 36-1-1-2-2-2-3-2z" class="I"></path><path d="M456 430v-37c-1-2 0-1 0-2-1-2-1-1-1-3 2 1 3 3 4 4l1 2v22c0 4-1 10 1 14h-5z" class="R"></path><path d="M437 418c2 0 8 0 10-1h4l1 1 1 1h0c-1 3-1 8-1 10h0l-1-2h0l-1 3h-4-2-7c-1-3-1-7-1-10h-8 0l1-1h-7v-1h15z" class="T"></path><path d="M452 418l1 1h0c-1 3-1 8-1 10h0l-1-2h0l-1 3h-4-2c0-3 1-8 0-10v-1c1-1 3 0 4-1h4z" class="R"></path><defs><linearGradient id="f" x1="462.576" y1="416.169" x2="480.448" y2="415.395" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#f)" d="M463 435v-3c2-7 1-15 2-22 0-9 0-17-1-25 0-2 0-5-1-6v-1c1-1 2-2 4-3v4c3 1 7 6 10 9l1-1h0c1 1 3 2 3 4 0 0-1 1-1 2l1 67h-2c-2-1-3-4-5-6l-12-13 1-1v-5z"></path><path d="M463 440l14 14 3 3v2l-1 1c-2-1-3-4-5-6l-12-13 1-1z" class="C"></path><path d="M514 785v-1c0-1 1-2 2-3 1 1 2 1 3 1 3 1 6 4 9 5h0l13 13h-1c-1-1 0-1-1-1v1 1c2 1 4 5 5 7l1 2h-1c-2-1-2-2-4-3 0-1-1-1-1-2h0-1l1 1-1 1c3 10 4 21-2 30l-2 3-1 2c-1 1-1 2-2 2-2 2-3 3-5 4l-2 2c-1 1-2 1-3 2h-1c-1 0-2 0-2 1h-4 0c-4 0-8 0-11-1h-1l-2-1h-2c-1 0-2 0-2-1l-3-1-2-2c-5-4-9-11-11-16-3-8 0-20 3-28v-1c2-3 4-6 7-8h4c2-2 4-3 7-5v-1c1-2 1-2 3-3 1 1 2 2 3 2h3c0-1-1-1-1-2v-1c2 0 3 0 5 1z"></path><path d="M501 789v-1c1-2 1-2 3-3 1 1 2 2 3 2-2 1-4 1-6 2z" class="P"></path><path d="M502 808h1c-1 7 0 14-1 20h-2v-2c0-5-1-14 2-18z" class="C"></path><path d="M490 836c-1-1-1 0-1-1 2-1 3-1 5 0h1 0 6l8 1v1l1 2-1 2c-1 0-2 0-3 1s-1 1-2 1h-1c-1 1-5 0-7 0l-1-1c-2-1-5-4-5-6z" class="L"></path><path d="M490 836c1 0 1 0 2 1 0 1 1 1 2 1h3c1 1 1 2 1 3l-2 2-1-1c-2-1-5-4-5-6z" class="H"></path><path d="M530 834c1 0 2 1 3 1 0 2 0 2-1 3l-1 2h0c-2 3-7 3-11 5-1 1-6 2-8 2l-1-1c-1 0-1-1-1-1 0-1 0-2-1-3v-1l1-2 4 1v-1c2-2 8 0 11-1 1 0 3-1 4-2v-1h0l1-1z" class="J"></path><path d="M510 839l4 1-1 2 1 4h-3c-1 0-1-1-1-1 0-1 0-2-1-3v-1l1-2z" class="B"></path><path d="M530 834c1 0 2 1 3 1 0 2 0 2-1 3l-1 2h0c-3 0-7 0-9 1-3 1-6 1-9 1l1-2v-1c2-2 8 0 11-1 1 0 3-1 4-2v-1h0l1-1z" class="G"></path><defs><linearGradient id="g" x1="540.142" y1="798.189" x2="518.906" y2="789.039" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#7c7b7c"></stop></linearGradient></defs><path fill="url(#g)" d="M514 785v-1c0-1 1-2 2-3 1 1 2 1 3 1 3 1 6 4 9 5h0l13 13h-1c-1-1 0-1-1-1v1 1c2 1 4 5 5 7l1 2h-1c-2-1-2-2-4-3 0-1-1-1-1-2h0-1l1 1-1 1c-1-4-3-7-6-10-4-5-11-9-18-10v-2z"></path><path d="M483 802c2-3 4-6 7-8h4c-8 8-11 16-11 28 0 9 4 19 11 26l4 3c-1 0-2 0-2-1l-3-1-2-2c-5-4-9-11-11-16-3-8 0-20 3-28v-1z" class="L"></path><path d="M510 787c0-1-1-1-1-2v-1c2 0 3 0 5 1v2 7c-1 2 0 4-1 6 2 2 4 2 6 3 6 3 11 9 11 16v9l-1 1-1 2h-6-2c-1 0-2 0-4-1l-1 2 1 2h3 11l-1 1h0v1c-1 1-3 2-4 2-3 1-9-1-11 1v1l-4-1-1-2v-1l-8-1h-6 0-1l11-1 2-1v-4c-2 0-3 0-5-1 1-6 0-13 1-20h-1c1-1 1-1 0-2h0c-6 4-7 9-8 16v7l-1-1c0-5 0-11 2-16 2-4 6-8 10-9 1-1 3-1 4-2s-1-6 0-8l1-1h-1c0-2 0-3 1-5z" class="D"></path><path d="M506 819c1-3 1-3 4-4 0 2-1 3-1 5 0 0 1 0 1 1v1l-2 1c-1 0-1 0-2-1v-1h2v-1h-1l-1-1z" class="S"></path><path d="M506 822c1 1 1 1 2 1l2-1v5l-1 1 2 1c0 1-1 1-1 1-1 1-1 2 0 3-2 1-3 1-5 1l2-1v-4c-1 0-1 0-2-1l1-1h0v-1c-1-2 0-3 0-4zm13 12h11l-1 1h0v1c-1 1-3 2-4 2-3 1-9-1-11 1v-3c2 0 9 0 11-1l-6-1z" class="M"></path><path d="M502 806c3-1 4-2 7-2v2l-1 2h2c0 1 0 1-1 2 1 1 1 2 1 3l-1 1 1 1c-3 1-3 1-4 4 0-4 1-8 0-11h-3-1c1-1 1-1 0-2z" class="G"></path><path d="M503 808h3c1 3 0 7 0 11l1 1h1v1h-2v1c0 1-1 2 0 4v1h0l-1 1c1 1 1 1 2 1-2 0-3 0-5-1 1-6 0-13 1-20z" class="F"></path><path d="M513 826c1-1 0-2 1-4 0-1 0 0-1-1 0-1 1-3 1-4 0-2 0-4-1-6l2-2-1-1c0-1 0-3 1-4 1 0 2 0 4 1 3 1 5 3 7 7-1-1-1-1-2-1-1-1-1-1-2-1l-1-1c-2 2-1 12-1 15h-1l-2-2h0l-2 1 1 2c-1 0-2 1-3 1z" class="G"></path><path d="M520 824c0-3-1-13 1-15l1 1c1 0 1 0 2 1 1 0 1 0 2 1h0c2 4 4 12 3 16v1l-1 2h-6-2c-1 0-2 0-4-1l-1 2 1 2h-2c-1-1 0-3-1-5h0v-3c1 0 2-1 3-1l-1-2 2-1h0l2 2h1z" class="K"></path><path d="M526 812c2 4 4 12 3 16h-1c-2-3-1-6-2-9v-7z" class="L"></path><path d="M520 824c0-3-1-13 1-15l1 1c1 5 1 12 0 18-1 0-1 0-2-1v-3z" class="C"></path><path d="M513 826c1 0 2-1 3-1l-1-2 2-1h0l2 2h1v3c1 1 1 1 2 1-1 1-1 1-3 0h0c-2 1-2 1-3 2l-1 2 1 2h-2c-1-1 0-3-1-5h0v-3z" class="H"></path><defs><linearGradient id="h" x1="517.212" y1="312.517" x2="517.503" y2="240.702" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#h)" d="M533 226h78c-2 0-2 1-4 2 0 1-1 1-1 2-7 8-8 20-12 29-1 2-2 5-3 7l-1-1 1-2c-1 2-2 6-5 7-3 3-5 8-8 12-10 13-24 23-40 28-19 6-38 4-55-5s-29-22-37-39c-4-8-6-17-9-25l12-1c1 0 1-1 1-1 3 2 5 11 7 15s4 9 7 12c0 0 0-1-1-2l-1-2v-1c-1-1-1-2-2-3 1 1 2 2 2 3 1 1 1 2 3 3h0c7 9 16 17 27 22 14 7 33 7 48 1 22-8 35-26 44-46h-17c0-2 1-5 1-6l-3 5c-4 1-8 1-12 1l5-10h1c0-2 1-2 2-4h-7c-3-1-19 0-21-1z"></path><path d="M464 266c1 2 3 4 4 6 6 6 13 12 21 16 16 8 33 8 50 3 17-6 32-19 40-35 4-8 7-16 11-24l-1 8c-1 9-5 19-10 26-1 2-3 4-5 6v1c-13 15-31 25-50 27-18 2-36-4-50-15-10-8-18-20-23-32-1-4-3-8-3-12l1-1c1 0 1-1 1-1 3 2 5 11 7 15s4 9 7 12z"></path><path d="M533 226h78c-2 0-2 1-4 2 0 1-1 1-1 2-7 8-8 20-12 29-1 2-2 5-3 7l-1-1 1-2c-1 2-2 6-5 7l4-9c0-2 1-4 1-7 0-1 0-3 1-4v-1c1-1 1-2 1-3h1c0-2 1-3 2-4h1c-2-1-3 0-4 0s-2-1-2-1l-2-1 1-8c-4 8-7 16-11 24-8 16-23 29-40 35-17 5-34 5-50-3-8-4-15-10-21-16-1-2-3-4-4-6 0 0 0-1-1-2l-1-2v-1c-1-1-1-2-2-3 1 1 2 2 2 3 1 1 1 2 3 3h0c7 9 16 17 27 22 14 7 33 7 48 1 22-8 35-26 44-46h-17c0-2 1-5 1-6l-3 5c-4 1-8 1-12 1l5-10h1c0-2 1-2 2-4h-7c-3-1-19 0-21-1z" class="C"></path><path d="M591 241l7-1c-2 7-4 15-8 21 0-2 1-4 1-7 0-1 0-3 1-4v-1c1-1 1-2 1-3h1c0-2 1-3 2-4h1c-2-1-3 0-4 0s-2-1-2-1z" class="G"></path><defs><linearGradient id="i" x1="517.904" y1="238.892" x2="519.622" y2="290.147" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#i)" d="M550 240c3-1 7-7 9-9h-1l-5 10c4 0 8 0 12-1l3-5c0 1-1 4-1 6h17c-9 20-22 38-44 46-15 6-34 6-48-1-11-5-20-13-27-22l-4-7c-2-3-8-14-7-17h11c2 2 3 6 4 7v-1-1c0 1 1 1 1 2h0l1-1-2-2 1-1c2 1 4 0 6 0h3 25c15 0 30 1 44-1h0l2-2z"></path><path d="M501 266h16 41l1 1v1c-3 2-9 1-13 1l5 1c1 0 2 0 3 1-3 0-3 2-6 2l-3 1v2 1h0c-2 1-5 2-8 2-12 3-28 3-40 0-3-1-5-4-8-5h0c-1 0-2 0-2-1l1-1-4-3c-2 0-3 0-4-1v-1c1-1 4 0 6 0 4 0 10 0 15-1z" class="O"></path><path d="M501 266h16 41l1 1v1c-3 2-9 1-13 1h-62c-2 0-3 0-4-1v-1c1-1 4 0 6 0 4 0 10 0 15-1z"></path><path d="M550 240c3-1 7-7 9-9h-1l-5 10c4 0 8 0 12-1l3-5c0 1-1 4-1 6 0 1 0 2-1 4v2l-3 6c0 1-1 3-1 4h-1 0l-1 2-2 1h2c0 2-1 4-1 5l1 1 1 1c0 1-1 1-2 1v-1l-1-1h-41-16c-5 1-11 1-15 1-2 0-5-1-6 0v1c-3-7-8-13-11-21v-1-1c0 1 1 1 1 2h0l1-1-2-2 1-1c2 1 4 0 6 0h3 25c15 0 30 1 44-1h0l2-2z" class="H"></path><path d="M553 249h1c2-1 4 0 6 0v3h0l-21 1c2-1 5 0 7-1h4l-2-1h2c2 0 2-1 3-2z" class="B"></path><path d="M553 243h9l1 1-1 3h-5c-3 1-6 1-10 2l6-6z" class="E"></path><defs><linearGradient id="j" x1="524.947" y1="227.807" x2="500.952" y2="259.999" xlink:href="#B"><stop offset="0" stop-color="#d7cfd0"></stop><stop offset="1" stop-color="#ebeff0"></stop></linearGradient></defs><path fill="url(#j)" d="M548 242l1 1c-5 5-14 4-21 4h-38c-2 0-7 1-8 0-1 0-1-1-2-1l-1-3h25c15 0 30 1 44-1z"></path><path d="M471 246l-2-2 1-1c2 1 4 0 6 0 1 3 2 5 4 6 5 2 12 1 17 1h41c5 0 10-1 15-1-1 1-1 2-3 2h-2l2 1h-4c-2 1-5 0-7 1h-5-12-47c-1-2-3-5-4-7z" class="E"></path><path d="M477 256c9-1 17 0 26 0h38 14c2 0 4 0 6 1l-1 2-2 1h-57-16c-2 0-4 0-6-1-1 0-2-2-2-3z" class="C"></path><path d="M469 247v-1-1c0 1 1 1 1 2h0l1-1c1 2 3 5 4 7 0 2 1 2 2 3 0 1 1 3 2 3 2 1 4 1 6 1h16 57 2c0 2-1 4-1 5l1 1 1 1c0 1-1 1-2 1v-1l-1-1h-41-16c-5 1-11 1-15 1-2 0-5-1-6 0v1c-3-7-8-13-11-21z" class="E"></path><path d="M469 247v-1-1c0 1 1 1 1 2h0l1-1c1 2 3 5 4 7 0 2 1 2 2 3 0 1 1 3 2 3 2 1 4 1 6 1h16 57 2c0 2-1 4-1 5l1 1 1 1c0 1-1 1-2 1v-1l-1-1 1-2c0-1-1-1-1-1h-4-13l-40-1c-7 0-13 0-20 1h-1c-1 1-1 1 0 2s4 1 5 1h16c-5 1-11 1-15 1-2 0-5-1-6 0v1c-3-7-8-13-11-21z" class="J"></path></svg>