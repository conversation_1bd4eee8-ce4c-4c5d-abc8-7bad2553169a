<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="74 48 536 604"><!--oldViewBox="0 0 660 752"--><style>.B{fill:#211c1c}.C{fill:#591919}.D{fill:#5c1b1b}.E{fill:#2f2728}.F{fill:#ab1b1d}.G{fill:#71181a}.H{fill:#23191a}.I{fill:#c91d21}.J{fill:#b61c1f}.K{fill:#331a1a}.L{fill:#961a1e}.M{fill:#bd1c20}.N{fill:#9d1a1e}.O{fill:#91181b}.P{fill:#2a2828}.Q{fill:#4c1719}.R{fill:#52191a}.S{fill:#3c1819}.T{fill:#ab1b1e}.U{fill:#f35450}.V{fill:#571a1b}.W{fill:#3d3a39}.X{fill:#cc1e22}.Y{fill:#615b5a}.Z{fill:#eee4e5}.a{fill:#f8f5f6}.b{fill:#771719}.c{fill:#911a1b}.d{fill:#3f1818}.e{fill:#87181a}.f{fill:#433f3f}.g{fill:#efe7e9}.h{fill:#d92326}.i{fill:#484444}.j{fill:#7e181a}.k{fill:#f85b58}.l{fill:#827c7c}.m{fill:#857f7e}.n{fill:#6f6a69}.o{fill:#f64740}.p{fill:#2b1718}.q{fill:#ee7f7e}.r{fill:#b9b1b1}.s{fill:#c4bfbf}.t{fill:#a7a0a0}.u{fill:#f64946}.v{fill:#9e9796}.w{fill:#d21f23}.x{fill:#f6ccca}.y{fill:#f7adab}.z{fill:#524d4d}.AA{fill:#f7908e}.AB{fill:#56514f}.AC{fill:#fe8781}.AD{fill:#fda5a3}.AE{fill:#f6dbda}.AF{fill:#ee605e}.AG{fill:#121212}</style><path d="M290 188l4 1-1 1h0c-1 0-2-1-3-1v-1z" class="r"></path><path d="M218 321h2 0c0 1-1 1-2 2h-2 0v-1l2-1z" class="B"></path><path d="M361 237l2-1 1 1-2 3-1-1v-2z" class="Y"></path><path d="M375 227h4v1h-1c-1 0-3 1-4 0l1-1z" class="E"></path><path d="M231 348l-1-1h-1v-1c1-1 1-1 2-1l1 2-1 1zm140-118l1 1c0 1 0 1-1 2h-1c-1-1-1-1-2-1l1-1h1l1-1z" class="B"></path><path d="M498 215l2 1-2 2c-1-1-2-1-3-2h1c1 0 2-1 2-1z" class="W"></path><path d="M377 230h2v1l-3 1h-2v-1-1h3z" class="E"></path><path d="M348 261c0-1 1-2 2-3h1v2 1h0l-1 1v-2c-1 0-2 0-2 1z" class="f"></path><path d="M318 254c0 2 1 3 1 5 0 0-1 0-1 1l-2-5c0 1 1 2 2 2-1-1 0-2 0-3z" class="l"></path><path d="M500 216l3 1-2 2-3-1 2-2z" class="i"></path><path d="M350 248h2l1 1-2 2-1 1c0-1 0-1-1-1h0c0-1 0-2 1-3z" class="B"></path><path d="M308 231l2 2v5c-1-2-2-4-2-6v-1z" class="P"></path><path d="M495 216l-4-3v-1c2 2 5 2 7 3 0 0-1 1-2 1h-1z" class="E"></path><path d="M294 284l1 10c-1-2-2-5-2-8 1 0 1-1 1-2z" class="P"></path><path d="M399 234c2 2 3 3 5 4l-1 3c0-1 0-2-1-3h-1c-1-2-1-2-2-3v-1z" class="H"></path><path d="M389 182v1 1 1c-1 1-1 1-1 2-1-1-1-1-2-1l-1-1c1-1 2-2 4-3z" class="r"></path><path d="M399 208c1 0 1 0 2 1 2 1 4 2 6 4-2 0-5-3-7-4-1 0-1 0-1-1z" class="H"></path><path d="M582 130l2 4c0 1 0 2-1 3l-2-3h1v-4z" class="AD"></path><path d="M405 187l2 2c0 1-1 1-1 1-2 1-5 1-7 1l2-1c1 0 1-1 3-1h1v-1-1z" class="B"></path><path d="M361 239l1 1-1 5c-1 0-1-1-2-1l2-5z" class="m"></path><path d="M597 166c1 4 2 9 1 13 0-2 0-4-1-7v-6z" class="X"></path><path d="M203 327v-1c0 1 0 1 1 2h0c0 1 0 1 1 1h0c-1 1-1 1-2 1h-2-1v-1c1-1 2-2 3-2z" class="B"></path><path d="M361 261h1l4 7h-1v1c-1-1-1-2-2-3s-2-2-2-3v-2z" class="H"></path><path d="M426 164v6c1 0 0 1 0 2s0 2-1 3h-1l2-11z" class="U"></path><path d="M459 299h1 1c0 2 1 5 1 7-1 0-1 0-2-1 0-2 0-4-1-6z" class="M"></path><path d="M365 223c0-3 1-6 2-9v8c-1 0-2 0-2 1z" class="P"></path><path d="M303 280c1-2 2-3 4-4l1 1-4 5-1-1v-1z" class="W"></path><path d="M407 189c2 3 2 5 3 7h-1l-2 1h-1c1-1 2-1 2-2s-2-3-2-4v-1s1 0 1-1z" class="E"></path><path d="M578 125l4 5v4h-1l-2-3 1-1v-2l-2-2v-1z" class="y"></path><path d="M319 241l-2-3c-1-1-2-1-2-3l-1-1h1c1 0 2 2 3 3s2 2 2 3l1 1h0c-1 0-2-2-3-3h0l1 3z" class="B"></path><path d="M298 284v1c0 1 0 4-1 5h-1v-5l1-1h-1l1-1v-1-2l1-1c0 2-1 3 0 5z" class="P"></path><path d="M294 189l6 1-2 1c0 1 0 2-1 2-1-1-2-2-4-3l1-1z" class="s"></path><path d="M175 215l6-3c-3 3-5 5-9 6l1-1 2-2zm119 25h2v1h3v1c-1 0-1 1-2 1l-2 2h0v-1c-1-2-1-2-1-4z" class="B"></path><path d="M255 175h2 1l2 2s1 1 1 2-1 1-1 1c0 1-1 2-1 3 0-2 0-3-1-4 0-1 0-2-1-2 0-1-1-1-2-2z" class="E"></path><path d="M365 223c0-1 1-1 2-1 0 2-1 4-1 6h0-1l-1 2 1-7z" class="B"></path><path d="M306 286h2c-1 3-2 7-3 10h-1 0c0-3 1-7 2-10z" class="H"></path><path d="M343 224c0-1 1-3 1-4 1-1 2-1 4-1-3 3-5 6-7 10l-1-1c0-1 1-2 2-3 0-1 0-1 1-1z" class="n"></path><path d="M396 218l1 1s0 1 1 1v1-1c1 1 2 3 2 4h-2l-3-3c1-1 1-2 1-3z" class="H"></path><path d="M503 217c1 0 2 0 2 1 1 1 2 1 2 1 1 1 1 2 1 2-3 0-5-1-7-2l2-2z" class="Y"></path><path d="M508 328l1 1 1 1v2h0 1l1 1c0 1 0 3-1 4h0c0-1 0-1-1-2-1-2-2-4-2-7z" class="P"></path><path d="M458 293v-3h1c1 3 2 6 2 9h-1-1c0-2-1-4-1-6z" class="N"></path><path d="M307 276c3-3 7-4 11-5-3 2-7 4-10 6l-1-1z" class="AB"></path><path d="M280 184h3c1-1 1-1 2-1l1 1v1h-1v1h-1v2c-2-2-1-2-3-3h-2l1-1z" class="s"></path><path d="M485 319h0l-1 1c-2 2-1 4-2 7h-1 0c-1-2 0-4 1-6 0-1 1-2 2-3h1v-1 2z" class="P"></path><path d="M372 241l2-2 2-1c1 1 1 3 1 4-1 0-1 1-2 1v1-1c-1-1-2-1-3-2z" class="B"></path><path d="M584 163l2 3 2 5-1 4c-1-4-3-8-3-12z" class="Q"></path><path d="M303 235h0c-1 2-1 3-2 4h-1l-6-3v-1l1 1h5c1 0 2-1 3-1zm-134 52h1v3h1c-1 2 1 7 2 9v2h0c-1 1-1 2-1 3-1-2 0-5-1-7l-2-6c1-1 0-3 0-4z" class="W"></path><path d="M357 189c3 2 5 0 8 1 1 1 2 0 3 0v1c-3 0-7 1-11 2v-1-3zm7 41l1-2h1c-1 3-1 6-2 9l-1-1-2 1 3-7z" class="z"></path><path d="M289 225c0 3-1 8 0 11l1-1v5 4c-1 0-1-1-1-2-1-5-1-12 0-17z" class="n"></path><path d="M283 222c0 1 1 1 1 2h0l-9 9c1-2 2-3 3-4l5-7z" class="l"></path><path d="M286 184c1 2 2 3 4 4h0v1c1 0 2 1 3 1-2 0-4 0-6 1h0l-1-1 1-2-1-1s0-1-1-1h0v-1h1v-1z" class="t"></path><path d="M232 347h1c0 2-2 3-3 3 0 1-1 1-1 1-2 0-3 3-4 3h-1c1-2 4-5 7-6l1-1z" class="B"></path><path d="M290 240v6c0 2 1 9-1 11v-15c0 1 0 2 1 2v-4z" class="Y"></path><path d="M207 330h1 2c-1 1-3 2-4 3h0-4c-1 1-1 1-2 0h0v-1l1-1h0c1 1 2 1 3 0h1c1 0 1-1 2-1h0z" class="E"></path><path d="M279 212c0-1-1-1-1-2l-2-1c-1 0-1 0-2-1v-1h1c1 1 2 1 2 2 1 0 3-1 4-1 1 1 1 1 2 1h1 0l-1 2h-1 0c-1 0-2 0-3 1z" class="B"></path><path d="M292 251c4-1 6-6 9-7-4 4-7 8-10 13 0-1 0-2 1-4v-2z" class="Y"></path><path d="M388 232h1c3 4 7 9 8 14h0l-2-3c-2-4-5-7-7-11z" class="E"></path><path d="M171 290l2 1c0 2 0 2 1 4v3 4 2c0-1-1-2-1-3h0v-2c-1-2-3-7-2-9z" class="B"></path><path d="M353 182h3l9 3h-3v1l1 1h0c-2 0-6-2-7-3s-2-2-3-2zm-85-2c1 1 2 1 3 2s0 1 1 2l1 2h-1l-2-2c-2 1-5 2-6 3h-1 0c0-1 4-3 5-3v-1c-1-1 0-2 0-3z" class="n"></path><path d="M479 287s0-1-1-2c-1-2-1-4-3-6-1-1-1-3-2-4 0-1-1-1 0-2v1c2 4 6 7 7 12l-1 1z" class="E"></path><path d="M340 246v4h-1v2c1-1 1-2 2-4v-1c1-3 4-7 6-10v2h0l-2 2v1c-1 1-2 2-2 4l-1 1-1 2v1s0 1-1 2c0 1 0 3-1 3h-1c0-3 0-7 2-9z" class="B"></path><path d="M588 171l1 4c0 2 1 5 1 8 0 2-1 3-1 5v4c0-6-1-11-2-17l1-4z" class="G"></path><path d="M412 180c0 5 2 10 3 15-1-2-2-3-2-5l-3-3h2c-1-2-2-4-2-6 0 0 1 0 2-1z" class="AB"></path><path d="M487 264l-16-1h0c6-1 12-1 17 0l-1 1z" class="l"></path><path d="M269 177c1 0 2 0 3 1v1h0 0 1c0 1 1 1 1 1v-3l-1-1v-1c1 2 3 3 4 5h2v1l-1-1c-1 0-2 0-3 1h0c-2 0-7-3-9-3l2-2 1 1z" class="P"></path><path d="M307 233c-2-5-2-11-2-16 2 4 1 9 3 14h0v1l-1 1z" class="W"></path><path d="M459 362l2 3c0 1 0 3 1 4h0v-1l1 1c0 2 0 3 1 4-1 1-1 1-2 1-1-3-2-6-4-9l1-1v-2z" class="n"></path><path d="M533 282c3 4 5 9 6 14-1 0-1 0-2-1s-1-3-2-5-2-3-2-5v-3z" class="B"></path><path d="M474 296l11 5 3 3h0c-3-1-5-2-7-3s-4-2-5-3-2-1-2-2z" class="i"></path><path d="M377 211l1 2c0 2 1 3 2 5v4s-1-1-1-2v-1l-2-2c0-1 0-1-1-1h-2c-1-1-1 0-2 0h0v-2c2 0 2 1 4 1v-2h1v-2z" class="P"></path><path d="M424 175h1c1-1 1-2 1-3v4h0 0c-1 2-2 4-2 6v1h-1c-1 1-1 3-2 4l2-10 1-2z" class="o"></path><path d="M424 175h1c1-1 1-2 1-3v4h0c-1 1-1 2-2 3 0-1-1-1-1-2l1-2z" class="u"></path><path d="M377 189c1-1 4-1 5-1 1-1 1-2 2-2l1-1 1 1s-1 1-1 2c1 0 1 0 1 1h-1c0 1 0 1-1 1h-5c0 1-1 2-1 3 0-2 0-2-1-3h1l-1-1z" class="Z"></path><path d="M262 175l1-1c1-1 1-1 2-3v-2-1h1c0 1 1 2 0 3h-1l1 1c1 0 1 1 1 1 0 1 0 1 1 2v1h0c1 0 1 0 1 1l-1-1-2 2-2-2-2-1z" class="B"></path><path d="M360 290h0l-2-8v-1h1s1 1 1 2h0l1 2c0 1 0 1 1 2v-1c0-1 0-1 1-2 1 2 1 3 1 4h0c-1 1-1 2-1 4h-1v-1s0-1-1-2c0 0 0 1-1 1z" class="H"></path><path d="M405 178l1 1c-1 1-5 2-5 3l-2 2v-1c-2 0-2 0-3 1-1-1-1-1-1-2 0 0 1-1 1-2h0 1c0 1 0 1 1 1 3-1 5-2 7-3z" class="a"></path><path d="M265 192s0 1 1 1c-1 1-2 2-2 3v3c1 2 4 2 4 4 1 1 2 1 2 2l1 1c0 1 0 1 1 2h-1l-1-1v-1c-2-2-5-5-8-6 0-1 0-4 1-6l2-2z" class="H"></path><path d="M406 179h1l-2 4c-1 0-1 0-2 1l1 2h0l1 1v1c0-1-1-1-2-2h-2c-1 0-1 1-2 2 0-1 1-2 1-3l-1-1 2-2c0-1 4-2 5-3z" class="Y"></path><path d="M559 140l5 2 9 5v1h-1c-4-3-9-4-14-6h1 2 0c1 0 2 1 3 1-1-1-2-1-3-1-1-1-2-1-2-2z" class="AE"></path><path d="M215 328c1-1 1-2 2-3s2-1 3-2c-1 2-3 4-3 6-1 0-1 0-1 1l-1 1-1 1v2c-1 1-2 2-2 3h0v2l-1 1v1 1l-1 1v2c-1 1 0 3 0 4v4l-1 2c-1-5 0-12 2-16 2-3 3-7 4-10v-1z" class="B"></path><path d="M291 231c-1-3 0-5 0-7 0-3 0-5 1-7v1c0 1 1 1 2 2h5c-1 1-2 1-3 1h-1l-1 1h0l1 1c-1 1-1 1-2 1v1c-1 2-2 2-2 4v2z" class="P"></path><path d="M292 221h1v2h-1v-2z" class="B"></path><path d="M257 172l1 1h1c1 1 1 2 2 2 1 1 1 1 2 1l1 1v4h-1 0c-1-1 0-1 1-2h-3c0-1-1-2-1-2l-2-2h-1-2c-1 1-2 2-3 1 1-1 4-3 5-4z" class="i"></path><path d="M380 218c0 1 1 1 1 1 2 3 2 7 2 10v7-4l-1-1v2 3-3c-1-4-1-7-2-11v-4z" class="l"></path><path d="M279 195h1l-1 2 1 1-1 1v1h0c0 1-1 2-2 2h-3c-4 0-6-1-10-4 3 1 5 2 7 3h5c1-2 2-3 2-5 0-1 0-1 1-1zm200 106c-3-1-7-2-10-4l1-1c0-1-1-1-1-1l-2-1h2c2 0 4 1 5 2 0 1 1 1 2 2s3 2 5 3h-2z" class="P"></path><path d="M261 179h3c-1 1-2 1-1 2h0c0 2-2 4-3 6 0 1 0 2-1 4h-1 0c0-3 1-6 1-8 0-1 1-2 1-3 0 0 1 0 1-1zm38 92c1-1 1-3 1-4 1-2 3-4 4-7h1c-2 5-4 10-5 15-1 3-1 6-2 9-1-2 0-3 0-5l1-8z" class="W"></path><path d="M383 229c1 5 0 12 0 18v3h0v8h-1v-2c1-2 0-5 0-8v-9-6 3-3-2l1 1v4-7z" class="Y"></path><path d="M468 269v-3h0l1 1c1 4 2 8 2 12 1 1 1 2 1 4h-1v-1c0-2-1-4-3-6v-7z" class="M"></path><path d="M373 265l-3-6c-2-5-6-9-7-14 2 2 4 6 7 8 0 4 2 8 3 12h0zm-81-14c1-1 1-3 3-4 1-2 4-4 5-6 0-1 0-1 1-1h0v3c2 0 3-2 4-2h1c-1 1-4 3-5 3-3 1-5 6-9 7z" class="W"></path><path d="M450 362c1 1 3 3 5 3 1 1 5 10 6 12v3c-2-4-4-11-8-13l-5-4 2-1z" class="B"></path><defs><linearGradient id="A" x1="322.933" y1="266.002" x2="317.002" y2="261.47" xlink:href="#B"><stop offset="0" stop-color="#696564"></stop><stop offset="1" stop-color="#817d7d"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M319 259l5 9v1l-1-1-1 1v1 1h0 1v1c-1 1 0 1 0 2l-5-14c0-1 1-1 1-1z"></path><path d="M290 246l1 7h0 1c-1 2-1 3-1 4 0 2 0 3 1 5 0 1 0 2-1 3v4h-1c-1-4-1-8-1-12 2-2 1-9 1-11z" class="f"></path><path d="M257 172h0c1-2 1-3 0-4-1-3-4-5-7-6h0v-1c3 1 7 2 9 6 0 1 0 3 1 5 0 1 1 2 2 3l2 1h-1c-1 0-1 0-2-1-1 0-1-1-2-2h-1l-1-1z" class="m"></path><path d="M392 174h0c1 1 0 2 0 3-1 1-3 3-4 5l-1-1-1-1v1c-1 1 0 0-1 0v1h-2l-1 1-1 1h-1c-1 0-2 1-3 1l-1 1h-2-5-1c4 0 6-1 10-2 1 0 2-1 3-1 4-2 7-5 11-9z" class="P"></path><path d="M297 207l2-2v3l-1 1c1 0 2 0 3 1v2c-1 0-1-1-1-1h-1c0 2 1 3 1 5h0c-1-1-1-1-1-2h-2c-2-1-2-1-3-1 0-1 1-2 1-3l2-3z" class="B"></path><path d="M295 210l2-3 1 2-1 1h-2z" class="P"></path><path d="M405 183l4-2h1c0 2 1 4 2 6h-2s-1 0-2-1h-1-1 0-1-1 0l-1-2c1-1 1-1 2-1z" class="B"></path><path d="M188 98h30c-4 1-9 2-13 2-5-1-11 0-16-1l-1-1z" class="u"></path><path d="M405 178h0l8-6c0-2 0-4 1-6 2-2 5-3 8-4-2 1-6 3-7 6-1 2-1 3 0 5h-2s-1 2-2 2c-1 2-3 3-4 4h-1l-1-1z" class="v"></path><path d="M591 148c2 3 3 7 4 11 1 2 2 4 2 7v6c-1-2-2-6-2-9h0v-2h-1v-2h0l-1-2c-1-3-2-6-2-9z" class="w"></path><path d="M290 216c0-1 1-2 1-3 0 0 1 0 1-1v1c0 1-1 2-1 4h1 0c-1 2-1 4-1 7 0 2-1 4 0 7l-1 4-1 1c-1-3 0-8 0-11v-4c1-2 1-3 1-5z" class="l"></path><path d="M456 99l30-2c-2 1-3 1-4 2-2 1-7 1-9 1l-17-1z" class="o"></path><path d="M489 246l5 3s1 1 2 1c1 1 4 2 5 4-4 0-8-3-12-5-2-1-3-1-5-2 1-1 3 0 4 0l1-1z" class="W"></path><path d="M318 180h1l3 1c0 1 0 2 1 2l3 5c-1 1-1 1-2 1h-1c-1 0-1 1-2 1v-1-1c1 0 1 0 1-1 0 0-1 0-1-1l-1-1c1-1 1-2 0-3h-1l-2 2h-1-1l3-3v-1z" class="E"></path><path d="M458 365c-1-4-2-7-4-11l-6-11c0-1-1-2-2-4 1 1 3 3 3 4 2 4 4 7 6 10l3 6 1 3v2l-1 1z" class="m"></path><path d="M464 379h1s0 1 1 2c0 2 0 4 1 7 0 5 0 15-4 20-1-1 0-3 1-4 2-7 2-17 0-24v-1z" class="r"></path><path d="M343 160v-2c2 4 3 8 5 12 2 2 4 5 6 7h-2v-1 2c-1 0-2-1-3-2l-1-2-2-3h1l-4-11z" class="s"></path><defs><linearGradient id="C" x1="436.959" y1="360.627" x2="443.775" y2="358.326" xlink:href="#B"><stop offset="0" stop-color="#2e2c2b"></stop><stop offset="1" stop-color="#494441"></stop></linearGradient></defs><path fill="url(#C)" d="M442 360c-3 0-6 1-9 2 2-2 6-4 9-4 4 0 9 4 12 7h1c-2 0-4-2-5-3l-2 1-6-3z"></path><path d="M442 360h3c1 0 3 1 5 2l-2 1-6-3z" class="P"></path><path d="M393 181h1 0c0 1 0 1 1 1 0 1 0 1 1 2-2 2-2 3-3 6h0l-1-1c1-1 1-2 0-3v1h0l-1 1-3-1h0c0-1 0-1 1-2v-1-1-1h1c1-1 2-1 3-1z" class="s"></path><path d="M389 182h1c1-1 2-1 3-1 0 1 0 3-1 4-1 0-2-1-3-2v-1z" class="B"></path><path d="M382 217l1-1h1c1 2 3 4 5 6l7 7c1 2 2 4 3 5v1l-3-3-8-8c-2-2-4-5-6-7z" class="v"></path><path d="M376 286l1 1-3 24h0c0-4-1-8-1-12v-2c1-1 1-2 2-3v-4h0v-1 1l1 1v-5z" class="B"></path><path d="M454 306v-3l1 2h1l-1 3-1 2h1c1-2 3-3 5-4v-1c1 1 1 1 2 1h0c2 2 3 1 5 1h-3c-4 0-5 1-8 3 0 0-1 1-2 1-1 1-2 4-3 6l-3 3 3-6 3-8z" class="G"></path><path d="M454 306v-3l1 2h1l-1 3h-1v1-3z" class="j"></path><path d="M276 183v-1h2l2 2-1 1h2v1l1 1c0 1 0 1-1 2 0 0-1 1-2 1 0 0-3-1-3-2-1-1 0-3-2-4l-1 2-1-2c1-1 2-1 3-2l1 1z" class="B"></path><path d="M276 183v-1h2l2 2-1 1v3h-1c-1-2-1-4-2-5z" class="a"></path><path d="M403 140h1l4 14c0 1 1 2 2 4v6c-1-1-1-2-1-3-1-2-2-5-3-7h0v3h0v-1-3h0c0-2-2-3-2-5v-2c-1 0-1-1-1-2v-4z" class="E"></path><path d="M264 176l2 2c2 0 7 3 9 3h0c1-1 2-1 3-1l1 1c-1 0-1 1-1 1h-2v1l-1-1c-1 1-2 1-3 2-1-1 0-1-1-2s-2-1-3-2c0 1-1 2 0 3-1-1-2-2-4-2v-4l-1-1h1z" class="Z"></path><path d="M264 177l4 3c0 1-1 2 0 3-1-1-2-2-4-2v-4z" class="Y"></path><path d="M292 262c0 2 1 4 1 6 1 5 0 11 1 16 0 1 0 2-1 2l-3-17h1v-4c1-1 1-2 1-3z" class="B"></path><path d="M329 225l1-2h1c1 2 2 5 3 7 1 4 1 9 1 13-1 0-1-1-1-1-1-6-3-11-5-17z" class="n"></path><path d="M307 233l1-1c0 2 1 4 2 6s2 5 3 7c1 3 4 7 5 9 0 1-1 2 0 3-1 0-2-1-2-2-4-7-7-14-9-22z" class="t"></path><path d="M418 91c6 2 12 4 18 5l12 3h-1-1 0 1 0c1 0 1 1 2 1h0 2-2-1c-3-1-6-1-9-1l-11-4-6-1c-1-1-3-2-5-2l1-1z" class="Z"></path><path d="M370 203h1l1 1 5 7v2h-1v2c-2 0-2-1-4-1v2h0v1h-1c0-2 1-6 1-7-1-2-2-3-2-4v-2-1z" class="H"></path><path d="M215 328v1c-1 3-2 7-4 10h-1 0v1c-2 2-2 6-2 8-1 2-1 4 0 6 0 1 0 3-1 4 0-2-1-5-2-7-2-4-5-6-9-9 1 0 2 0 3 1h1c0 1 1 1 2 2s3 4 4 6v1c1 1 1 1 1 2v-2c-1 0 0-1 0-1v-5-1h1v-2-1c0-1 1-2 1-3 1-2 1-3 1-4 1-1 1-1 1-2 1-1 1-1 1-2l3-3z" class="E"></path><path d="M457 297v-6h1v2c0 2 1 4 1 6 1 2 1 4 1 6v1c-2 1-4 2-5 4h-1l1-2 1-3 1-8z" class="B"></path><defs><linearGradient id="D" x1="357.549" y1="255.031" x2="353.87" y2="252.422" xlink:href="#B"><stop offset="0" stop-color="#817b7a"></stop><stop offset="1" stop-color="#979192"></stop></linearGradient></defs><path fill="url(#D)" d="M359 244c1 0 1 1 2 1-4 9-8 17-10 26h-1c0-2 1-3 2-5v-2h0l1-1h-1c0 1-1 4-2 5 1-6 4-11 5-16l4-8z"></path><path d="M481 251h4 0l1 1h1 1 0 1-1v-1c1 0 1 1 2 1h0c2 0 4 1 5 2h0c3 1 5 2 7 3l3 2c1 0 1 0 2 1h-2l-1-1h-1l-3-2-2-1c-2 0-4-1-6-2h0 0-2c-1-1-3-1-4-1h-6 0c-1 1-1 1-2 1h0 0s0-1 1-1l1-1h-3l-1 1h0-2c-1 0-1 0-2-1h1v1c1 0 1 0 2-1h1 1c1-1 3-1 4-1z" class="B"></path><path d="M334 353h0c1 2 1 4 1 6 1 8 1 15 1 23v-10c0-2 0-4 1-5 0-2-1-4 0-5v-1-4-2h1v-2l-2 36v1c0-1-1-1-1-1v-3-8l-1-25z" class="f"></path><path d="M508 279l2-2h2 0l1-1c-1 0-2-2-2-2-1-1-2-1-3-1 0 0-1 0-2-1h-3c-1-1-4-1-5-1h-1c-1 0-2-1-3 0h-2-2-2v-1h0c3 1 5 0 7 0 4 0 7 1 10 2 2 0 4 1 6 0l8 2h0l-1 1c0 1-1 2-2 2-2 0-2 0-4 2h0c-1-1-2-1-2 0h-1-1z" class="B"></path><path d="M488 263c7 0 14 1 20 3 4 1 8 2 11 5h-1c-5-2-10-4-15-5h-4c-4-1-8-2-12-2l1-1z" class="r"></path><path d="M207 374c1 1 1 2 2 3-2 8-3 15-2 23 1 3 2 5 2 8h0c-6-9-4-24-2-34z" class="t"></path><path d="M207 358c1-1 1-3 1-4-1-2-1-4 0-6 0-2 0-6 2-8v-1h0 1c-2 4-3 11-2 16 1 3 0 9 2 12 0 1 0 1-1 2v-1-1h0c-1 0-1 1-2 2v-5l-1-6z" class="n"></path><path d="M287 181c-2-3-5-5-7-8l1-1c5 6 10 8 17 10 0 1-1 2-1 3-3-1-8-2-10-4z" class="s"></path><defs><linearGradient id="E" x1="490.244" y1="242.601" x2="490.399" y2="248.696" xlink:href="#B"><stop offset="0" stop-color="#625b5e"></stop><stop offset="1" stop-color="#7d756e"></stop></linearGradient></defs><path fill="url(#E)" d="M489 246c-4-3-9-6-13-10 2 1 4 3 6 3s6 3 8 5c1 2 6 3 7 5l-1 1c-1 0-2-1-2-1l-5-3z"></path><path d="M207 374c0-8 0-17-4-25-2-3-5-6-9-7h0c-1 0-2 0-3 1h-1v-1h1c2-1 3 0 5 0 4 3 7 5 9 9 1 2 2 5 2 7l1 6c0 4 0 8 1 12v1c-1-1-1-2-2-3z" class="l"></path><path d="M501 254l1-1c1 0 4 1 4 2l1 1h1v-1c2 2 4 4 6 5h0l2 2 1 1 2 4h0s1 1 1 2c-1 0-1 0-1-1-2-1-3-3-5-4l-13-10z" class="v"></path><path d="M391 171l1 1v2c-4 4-7 7-11 9-1 0-2 1-3 1h-2-1v-1h-5c9-1 15-5 21-12z" class="a"></path><path d="M159 254c2-8 3-16-1-23-1-1-2-2-3-4l2 1h2v1c2 2 3 4 3 6l-1 1c0 2 1 5 1 7 0 3-2 8-1 10v3l-2 2v-4h0z" class="r"></path><path d="M157 228h2v1c2 2 3 4 3 6l-1 1c-1-3-2-5-4-8z" class="P"></path><path d="M341 249v2h2l-1-1h0l2 1h-1v1c2-1 3-2 4-3-1 0-1 0-2-1v-5h1c0 2-1 4 0 5h0l1-1h2l1 1h-1v1c-1 1-3 1-3 2l-3 3h0v1h-1v1s0 1-1 1v1h0c1-1 2-2 3-2h1c-2 1-4 3-5 4s-1 3-2 4c0-3-1-6 0-9h1c1 0 1-2 1-3 1-1 1-2 1-2v-1z" class="i"></path><path d="M202 310h2 0l-1 1c-2 0-4 1-5 2h-1l1 1c2 0 4 0 6-1 2 0 3-1 5-1h4 0 1c-1 1-1 1-2 1s-1 0-2 1h0c-1 1-2 1-2 2-1 0-1 0-2 1h-1l-1 1-1 1h-1 0l-3 3-3 2h0l1-2 2-1 3-3c1 0 2-1 3-2 0-1-1 1 0 0l1-1c-1 0-2 0-2 1h-1 0l2-2s-1-1-2 0h-6 0v-1c-1 0-1 0-2 1h-1v-1c1 0 1 0 2-1h0-1-1c-1 1-1 1-2 1v-1l4-1h1 3c1 0 1-1 2-1z" class="B"></path><path d="M214 295c1-1 1-2 0-3h1l3 11c0 3 1 7 2 10l3 6c-2-2-3-4-4-6-2-1-3-2-4-3h1l-2-2h1l-1-2h0c1-1 1-1 2-1h0l-2-10z" class="N"></path><path d="M216 305l3 8c-2-1-3-2-4-3h1l-2-2h1l-1-2h0c1-1 1-1 2-1h0z" class="K"></path><path d="M415 173c2 1 2 2 4 2 1 1 2 1 3 2v1h-1c-1-1-1-1-2-1-1-1-2-1-3-2v2h0c-1 0-2 1-2 2l-2 1c-1 1-2 1-2 1h-1l-4 2 2-4c1-1 3-2 4-4 1 0 2-2 2-2h2z" class="z"></path><path d="M414 179l-2-2c0-1 1-1 1-2 1 1 2 1 3 1-1-1-2-1-2-2 1 0 2 1 2 1v2h0c-1 0-2 1-2 2z" class="W"></path><path d="M345 207h1 1c1-1 3 0 4 0v1s-1 0-1 1 0 2-1 3-2 2-2 3v1h1c1 0 1 0 1 1 0 0-1 1-1 2h0c-2 0-3 0-4 1 0 1-1 3-1 4-1-2 0-4 0-5 1-2 2-3 3-5-1-1-1-1-1-2-1-1-1-1 0-2l-1-1 1-2z" class="P"></path><path d="M345 207h1 1c1-1 3 0 4 0v1s-1 0-1 1c-2 1-3 3-4 5-1-1-1-1-1-2-1-1-1-1 0-2l-1-1 1-2z" class="m"></path><path d="M340 141l3 17v2h0-1c0-1 0-1-1-1v2l-1-5-1 3-1-1h0l-1-4 2-5-1-1c1-2 2-5 2-7z" class="r"></path><path d="M339 149c0 2 0 4 1 7l-1 3-1-1h0l-1-4 2-5z" class="n"></path><path d="M455 281s1 0 1-1v1s0 1 1 2v2c1 1 1 3 1 5v1h-1v6l-1 8h-1l-1-2c1-3 2-7 2-11-1-1 0-3-1-5l-1 1v-1c0-1-1-2-2-2l1-2h-1c1-2 1-2 3-2z" class="c"></path><path d="M452 283c1-2 1-2 3-2 1 2 0 4 0 6h0l-1 1v-1c0-1-1-2-2-2l1-2h-1z" class="T"></path><path d="M456 292h0v-2s0-1 1-1v8l-1 8h-1l-1-2c1-3 2-7 2-11z" class="L"></path><path d="M505 218l5 1c-3-3-6-5-9-8 4 2 7 6 11 8s9 3 13 4c2 1 4 1 5 3h-1c-3-1-9-3-13-3-3 0-6-1-8-2 0 0 0-1-1-2 0 0-1 0-2-1z" class="m"></path><path d="M303 235v-1c0-1-1-2-2-3 0-1 0-1-1 0-1 0-2 0-3 1h-2v-1-1h1c2 0 3 0 5-1v-1c-1-3-1-5-1-8-1-1-1-2-1-3h1c1 1 0 3 1 5 1 3 0 6 2 9v1c0 1 1 2 2 3s1 3 2 5c-1 0-1 1-1 1h-1c-1 0-2 2-4 2v-3h0v-1h-1 1c1-1 1-2 2-4h0z" class="E"></path><path d="M303 235h0 1v3l-1 1h-2-1 1c1-1 1-2 2-4z" class="B"></path><path d="M461 341c0-1 0-1-1-2 0 0 1 0 0-1v-1h-1v-1-1-1c-1-1-3-6-3-7l-3-3c1 0 2 1 3 3 1 0 1 0 1 1 1 0 1 1 2 2l3 6c0 1 0 2 1 4v1c0 1 0 0 1 1v1h0v2c1 1 0 2 1 3v4s1-1 1-2h0c1-3 4-6 6-7h1 0c-1 1-3 3-5 4-2 3-3 7-4 11 0-6 0-12-2-18h0v4c-1-1-1-2-1-3z" class="P"></path><path d="M368 190c3 0 6-1 9-1l1 1h-1l-3 3v-1l-1-1-1 2c-1 0-2 0-3-1-4 2-9 2-13 4-1 0-2 1-2 0l-1-1c1-1 3-2 4-2 4-1 8-2 11-2v-1z" class="g"></path><path d="M368 190c3 0 6-1 9-1l1 1h-1l-3 3v-1l-1-1-1 2c-1 0-2 0-3-1h1c1 0 1-1 2-1h1c-2-1-3 0-5 0v-1z" class="a"></path><path d="M352 178v-2 1h2c5 4 9 5 16 6h5v1h1 2c-4 1-6 2-10 2-1-1-2-1-3-1l-9-3-4-4z" class="g"></path><path d="M503 266c5 1 10 3 15 5h1c0 1 1 1 1 2s0 1-1 1l-8-2-5-1-13-3c-1 0-3 0-4-1 4 0 9 2 14 1h1c0 1 1 1 1 1h2 1c1 1 3 1 4 1-1-1-2-1-3-1 0-1 0 0-1 0 0-1-1-1-2-1s-1-1-2-1l-1-1z" class="B"></path><path d="M506 271v-1c5 0 8 1 12 3l2-1v1c0 1 0 1-1 1l-8-2-5-1z" class="i"></path><defs><linearGradient id="F" x1="471.931" y1="298.492" x2="478.044" y2="306.762" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#3c3837"></stop></linearGradient></defs><path fill="url(#F)" d="M488 307c-1-1-2-2-4-2-3 0-7-1-10-2h-7v-2h1c2-1 4-1 5 0 2 0 4 1 5 1l1-1h2c2 1 4 2 7 3 1 1 2 2 4 3v1l-3-2-1 1z"></path><path d="M479 301h2c2 1 4 2 7 3 1 1 2 2 4 3v1l-3-2-6-3c-1 0-3 0-4-1h-1l1-1z" class="B"></path><path d="M249 182l1-1 1 1h1l1-1 2 5 5 18-1-1c-1-2-2-5-3-8l-1 1-4-10-1-3-1-1z" class="q"></path><path d="M251 182h1l1-1 2 5h-1-1c-1-1 0-1-1-3l-1-1z" class="U"></path><path d="M251 186h1c2 2 2 4 3 7 0 1 1 1 1 2l-1 1-4-10z" class="AC"></path><path d="M509 247c0 2 1 4 2 5-1-7-2-14 2-20h0c0 2-1 3-1 5-2 9 0 17 5 24l4 4-2 2-2-4-1-1-2-2c-2-2-3-5-4-8l-1-4h-1l1-1z" class="s"></path><path d="M519 267l2-2c9 11 20 22 19 37 0 2 0 4-1 6v3c0-5 1-9 0-15-1-5-3-10-6-14-3-5-9-10-14-15h0z" class="Z"></path><path d="M224 354h1l-3 4c2 0 3-1 5-1 0 1-1 1-1 2h3 1 3 0c1 1 2 0 3 1l-1 1h0c-2-1-5-1-6 0h-4c-1 1-2 2-3 2h-1l-1 1c-1 0-1 1-1 1-2 1-1 0-1 0-1 1-1 2-2 2h-1l1-2 1-1c1-4 5-6 7-10h0z" class="E"></path><path d="M255 196l1-1 3 8 1 1 7 20c-2-2-3-6-4-8v-1-1c-1-1 0-1-1-2h-1c0 1 1 2 1 4 0 0 1 0 1 1v2c1 0 1 1 1 1-1 2 1 4 1 7-2-4-3-8-4-13l-6-18z" class="x"></path><path d="M324 209c3 2 4 6 6 9 1 3 3 6 4 9l1 2-1 1c-1-2-2-5-3-7h-1l-1 2s-1 0-1-1c-1-2-2-3-3-5l-3-2v-1h1c2 0 1 0 2-1h1 1v-1l-2-2-1-3z" class="B"></path><path d="M325 219l-3-2v-1h1c2 0 1 0 2-1h1l-1 1 1 1h1l2 1v2l-1-1c-1-1-2 0-3 0z" class="P"></path><path d="M584 134l2 3 5 11c0 3 1 6 2 9h-2c-1-3-2-6-4-9 0-1-1-2-2-4l-1-2v-2l-1-3c1-1 1-2 1-3z" class="k"></path><path d="M584 134l2 3v1c-1 1 1 5 0 5s-1-2-2-3l-1-3c1-1 1-2 1-3z" class="AA"></path><path d="M304 282c-1 1-2 3-3 5-2 4-1 7-1 12l-1-1v-4c0-1-1-2-1-3 0-3 2-5 2-8 0-4 2-8 4-12l3-3h0v1h1l2-3v1c-1 4-4 7-6 10-1 0-1 1-1 1v1l-1 1h1v1l1 1z" class="E"></path><path d="M147 94c8-1 16 0 24 1 0 1 0 1-1 1-1 1-2 1-4 1h-10c-2 0-3-1-4 0-2 0-5-1-8-1h-6-3c4-1 8-2 12-2z" class="y"></path><path d="M135 96c4-1 8-2 12-2l7 1h0-3-3c2 1 2 0 4 2-2 0-5-1-8-1h-6-3z" class="x"></path><path d="M211 367c2-8 7-14 10-21 3-4 4-7 7-11 0 2-2 4-2 5l-10 18c-2 6-5 12-7 18-1-4-1-8-1-12v5c1-1 1-2 2-2h0v1 1c1-1 1-1 1-2z" class="m"></path><path d="M260 124c0-1 1 0 1 0 1 0 1 0 2 1l3 6 1 1c0 1 1 2 1 2v1c0 1 0 1 1 2v3h0c-1 1 0 3-1 4v2 3l-2 5c0 1-1 1-1 2s-1 2-2 3v2l-1-1c1-1 1-1 1-2v-2c-1 1-2 2-2 3l-2 4h-1c0-2 3-6 4-8 1-1 1-3 2-4 0-1 2-2 2-3l1-2v-1-1c1-1 0-1 1-2v-4c-1-1-1-1-1-2v-1l-4-7s0-1-1-1v-1c0-1-1-2-2-2z" class="B"></path><path d="M319 241l-1-3h0c1 1 2 3 3 3h0c1 1 2 3 3 4s4 5 4 6h0c-2-1-3-4-4-5h-1c1 2 3 5 4 7-1 0-1 0-2 1-1 0-1-1-2-2h-1c0 1 1 1 1 2l-2-2-2-2h1 0v-1c1 0 1 0 1-1l-1-1c-3-1-3-4-4-7l4 3h0c0-1-1-2-1-2z" class="P"></path><path d="M494 285v-1s-1-1-1-2l1-1c1 2 1 3 2 5v2l1 2v1 2h0c1 1 0 2 1 3v3c0 1 0 2 1 3 0 1-1 2 0 3 0 0 1 1 1 2h0v2c-1 0-1 0-1 1h-1c0-1 0-2-1-2-1-1-2-1-2-2 0 0 0-1-1-1l1-1c0-2-1-3-2-5 1 0 2 0 3-1v-2h0v-2c-1-1-1-1-1-2h0v-2-1l-1-1v-1-2z" class="f"></path><path d="M495 304h2c0 1 0 1 1 2l-1 1 2 1h0l1-1h0v2c-1 0-1 0-1 1h-1c0-1 0-2-1-2-1-1-2-1-2-2 0 0 0-1-1-1l1-1z" class="W"></path><path d="M449 343c1 1 2 1 2 2 1 1 1 1 1 2l1 1c0 1 1 2 2 3 0 1 1 2 1 2 1 2 1 3 2 5h0c1-1 1-1 0-2v-1c0-1 0-1-1-2v-1-4-1-1c-1-1-1-2-1-3s0-2-1-3v-1c0-3-1-6-1-9l3 4c0 1 1 2 1 3 0 3 1 6 1 9l1 10-1-2c-1 2 1 3-1 5l-3-6c-2-3-4-6-6-10z" class="B"></path><path d="M192 332c1-1 2-3 4-5 2-1 3-2 5-3 4-3 8-6 13-8v1c-9 5-19 9-23 19-2 4-3 8-5 12 0-2 1-4 1-6 2-5 2-8 0-13-1-5-5-7-9-9 1-1 1-1 2 0 5 2 7 5 9 10 1 1 1 3 1 4 1 0 1-1 2-2z" class="t"></path><path d="M282 211c1 1 1 2 2 2 1 1 0 2 1 3 0 1 1 1 1 2l2-2h-1v-1h0 2l9-12h1v1 1l-2 2-2 3c0 1-1 2-1 3l-2 4h-1c0-2 1-3 1-4v-1c0 1-1 1-1 1 0 1-1 2-1 3l-6 8c0-1-1-1-1-2 1-1 1-2 2-2v-1-2c-1-2-3-3-5-5-2 2-4 4-5 6h0-1c2-2 3-4 5-6 1-1 2-1 3-1h0z" class="v"></path><path d="M372 204v-3h0l1-1 2 4c1 0 2 0 4-1v2l1 3h2l1 1v2c-1 1-2 1-3 1 1 1 2 3 4 4h0-1l-1 1-2-4c0 2 1 4 1 6 0 0-1 0-1-1-1-2-2-3-2-5l-1-2-5-7z" class="t"></path><path d="M375 204c1 0 2 0 4-1v2l1 3h2l1 1v2c-1 1-2 1-3 1l-5-8z" class="B"></path><path d="M344 209l1 1c-1 1-1 1 0 2 0 1 0 1 1 2-1 2-2 3-3 5 0 1-1 3 0 5-1 0-1 0-1 1-1 1-2 2-2 3l-2 5h0c-1-1-2-2-2-3s0-1 1-2l1-5 3-8h0 0l3-6z" class="v"></path><path d="M337 228h0l6-9c0 1-1 3 0 5-1 0-1 0-1 1-1 1-2 2-2 3l-2 5h0c-1-1-2-2-2-3s0-1 1-2z" class="Y"></path><path d="M322 172l1 1c-1 2-2 4-3 5h0l2-1v4l-3-1h-1c-6 4-14 7-21 5h0c0-1 1-2 1-3 2 1 4 1 6 0 5 0 10-2 13-4 2-2 3-4 5-6z" class="Z"></path><path d="M485 317h4v-1c4-1 7 0 10 1l1 1c0 1-1 1-1 1-4 0-9 2-12 5v1l-1 1c0 1-1 2-1 4h-1v2c-1 2-1 5 0 7h0c0 1 0 2 1 3v1 3 1h0c-1-1-2-4-2-5l-3-6c0-1-1-2-1-3l1-1c1 1 1 3 2 3v-2c1-1 1-1 1-2l1-3c0-1 0-2 1-3h0c1-3 5-5 8-6 1-1 2-1 3-2-2 0-5 0-6 1h-2c-1 0-2 1-3 1v-2z" class="W"></path><path d="M366 237v-1h1 0c1 2 4 4 5 5s2 1 3 2v1c3 2 4 5 6 9 1-1 0-3 0-4l1-10v9c0 3 1 6 0 8l-2-2v1h0c-2-3-4-5-6-7-1-2-3-3-4-4-2-2-3-2-4-4 0-1-1-1-1-2l1-1z" class="f"></path><path d="M366 237l10 10c-3-2-7-5-10-7h0c0-1-1-1-1-2l1-1z" class="B"></path><defs><linearGradient id="G" x1="317.132" y1="298.511" x2="300.263" y2="303.676" xlink:href="#B"><stop offset="0" stop-color="#2d2225"></stop><stop offset="1" stop-color="#3e4340"></stop></linearGradient></defs><path fill="url(#G)" d="M313 281v1l-2 9-1 8c-2 9-4 17-3 27h-1c-1-2-1-5-1-7-1-3-1-6 0-8 0-5 2-9 3-14s2-12 5-16z"></path><path d="M172 218c-2 1-5 2-7 3-3 1-6 1-8 2h-5-1l-3 1s-1 0-1 1h-1c-1 0-3 1-4 1v-1c1 0 2-1 3-1l1-1h1l5-2c3 0 6-1 8-2 1-1 3-2 4-3 0-1 1-2 2-2l1 1-4 3 12-3-2 2-1 1z" class="i"></path><path d="M502 283h1v1l2-2 1-1c1-2 0-2 1-4h1v1h-1v1h1 1 1c0-1 1-1 2 0-1 0-1 1-2 1-1 1-2 2-2 3-1 1-2 2-2 3l-5 9c-1 3-1 9-1 12h0c0-1-1-2-1-2-1-1 0-2 0-3-1-1-1-2-1-3v-3-2h1l1-2c0-1 1-2 1-4v-3h3l-2-1v-1z" class="E"></path><path d="M504 285v-1l-3 4v-3h3z" class="f"></path><path d="M299 271l-2 1v1 2h-1v-2h0v-1l1-1v-2-2h1v-2-1l1-1c0-2 1-4 2-5l1-3 2-3s1-1 1-2l3-3v-1h1v1c-1 1-1 2-1 2 0 3-1 8-3 11h0-1c-1 3-3 5-4 7 0 1 0 3-1 4z" class="H"></path><path d="M458 337v1-1h1 0c0 1 0 1 1 2 0 0 1 1 1 2s0 2 1 3c1 4 1 9 1 13-1 4-2 7-1 11v1h0c-1-1-1-3-1-4l-2-3-1-3c2-2 0-3 1-5l1 2-1-10c0-3-1-6-1-9z" class="B"></path><path d="M458 359c2-2 0-3 1-5l1 2c1 2 1 6 1 9l-2-3-1-3zm21-26h0l-1-1-1-1c0-1-1-2-2-3l-2-1-1-1-2-1c-1-1-3-2-4-3-2-1-4-2-5-3v-1h2v1l4 2c1 0 1 0 1 1 2 1 3 1 4 2h1 0c-1-3-3-4-5-6-1-1-5-3-6-4h1c2 1 5 2 6 3s3 2 4 3c2 1 5 5 6 7v1c1 1 1 1 1 2s0 1 1 2h1 0v-1c1-1 1-2 2-3l-1 3c0 1 0 1-1 2v2c-1 0-1-2-2-3l-1 1z" class="E"></path><path d="M159 254h0v4c-3 5-8 10-12 14-2 2-4 4-5 6-2 3-4 5-6 8-3 7-5 15-3 23v1c0-1-1-1-1-2-1-3-1-7-1-11 1-8 6-16 12-23 3-3 7-7 10-11l6-9z" class="s"></path><path d="M391 171c6-8 8-16 8-26 1-4 1-9 0-13s-2-8-4-12h-1l1-1c3 3 5 11 6 15 0 5 0 10-1 14 0 10-1 18-8 26h0v-2l-1-1z" class="t"></path><path d="M328 251l3 3v1c0 1 0 1 1 2 1 0 1 0 2-1l-1-1v-1h0v1h-1-1 0v-3l-1-2c-1-1-1-2-1-3s1-1 1-2c1 1 1 2 2 3 1 2 2 3 2 6l1 13-1 1c0-1-1-1-1-1 0-1-1-2-1-2v-1-1l-2-2c0-1-2-3-3-4 0-1-1-2-1-3h1l1 1c0-1 0-1-1-2-1-2-3-5-4-7h1c1 1 2 4 4 5h0z" class="f"></path><path d="M337 222l1 1-1 5c-1 1-1 1-1 2s1 2 2 3h0c0 1 0 2-1 4 0 2 1 5 0 8-1 5 0 9-1 14v-2c0-3-2-5-2-8-1-2-3-5-4-7h0l1-1s1 1 1 2c1 0 1 0 1 1l1-1v-1s0 1 1 1c0-4 0-9-1-13l1-1-1-2h1v-2h0l1-2v2h1v-3z" class="i"></path><path d="M337 222l1 1-1 5c-1 1-1 1-1 2s1 2 2 3h0c0 1 0 2-1 4 0 2 1 5 0 8v-7c0-2-1-8-2-9l-1-2h1v-2h0l1-2v2h1v-3z" class="AB"></path><path d="M349 195c0-1 1-1 1-2v1c1 0 1 0 0 1h1 2 0l1 1c0 1 1 0 2 0v1l-1 1v1c0 1 0 1-1 2v1c1 1 0 1 0 2-1 0-1 1-1 1v2l1 1 3 6c-2-3-3-5-6-6v-1c-1 0-3-1-4 0h-1-1c-1-2 3-5 3-7l1-3v-2z" class="Z"></path><path d="M355 199c0 1 0 1-1 2v1c1 1 0 1 0 2-1 0-1 1-1 1v2l-4-1 1-1c2-2 3-4 5-6z" class="B"></path><path d="M349 195c0-1 1-1 1-2v1c1 0 1 0 0 1h1 2c0 2-1 3-1 5-2 3-4 5-6 7h-1c-1-2 3-5 3-7l1-3v-2z" class="Y"></path><path d="M349 195c0-1 1-1 1-2v1c1 0 1 0 0 1h1c0 1-1 2-2 2v-2z" class="z"></path><path d="M383 89h-5c14-5 27-2 40 2l-1 1-12-3h-5-2 0 0l-1 1c1 0 1 1 1 1 0 1-1 1-2 1h0c-4-1-8-2-13-3z" class="g"></path><path d="M383 89c4-1 11-1 15 0h0l-1 1c1 0 1 1 1 1 0 1-1 1-2 1h0c-4-1-8-2-13-3z" class="u"></path><defs><linearGradient id="H" x1="343.861" y1="263.476" x2="332.299" y2="235.164" xlink:href="#B"><stop offset="0" stop-color="#4f534d"></stop><stop offset="1" stop-color="#8c7c80"></stop></linearGradient></defs><path fill="url(#H)" d="M340 228l1 1c-1 5-2 10-2 15 1-2 3-5 5-6h0l-4 8c-2 2-2 6-2 9-1 3 0 6 0 9 1-1 1-3 2-4s3-3 5-4v-1l1 1c-1 2-4 3-5 5-2 3-3 4-3 6l-2 2v-10c1-5 0-9 1-14 1-3 0-6 0-8 1-2 1-3 1-4l2-5z"></path><path d="M178 320l-1-1h-2 0c-1 0-1 0-2-1v-1h2 1v-1h2 0 4c1 0 2 1 2 1h2c1 0 2 1 3 2h0v1c2 2 1 4 2 5v2 4-1l-1 1c1 0 1 1 2 1-1 1-1 2-2 2 0-1 0-3-1-4-2-5-4-8-9-10-1-1-1-1-2 0z" class="B"></path><path d="M494 285v-1c-1-1-2-2-2-3v-1l-1-1c-2-5-6-8-9-13 2 1 4 3 5 5 1 0 1 1 2 1l1 1h1v-1h0l3 2v1 1 2c1 1 0 1 0 2l1 1v2c1 1 1 1 1 2h0c0 1 0 1 1 2h0v1 2-2-1-1h0c0-2 0-3-1-5h0c1-1 1-1 2-1v1c1 2 1 3 2 5v1-4c1 0 1-1 2-1v1 1l2 1h-3v3c0 2-1 3-1 4l-1 2h-1v2c-1-1 0-2-1-3h0v-2-1l-1-2v-2c-1-2-1-3-2-5l-1 1c0 1 1 2 1 2v1z" class="W"></path><path d="M386 186c1 0 1 0 2 1h0l3 1 1-1h0v-1c1 1 1 2 0 3l1 1h0 0v4h0-1 0l-2-1c0 1 0 1-1 2 0 1 1 1 1 2 1 0 2-2 4-2v2c0 1 1 2 2 3l1 1h0c-1 1-1 0-2 1l-1 1h-1v-3c-1-1-1-2-2-3h-1c-1 0-1 0-1-1-1-1-1-1-2-1h0v1c-1 1-1 2-2 3-1 0-1 1-2 2h1l2-1v1 2h-1-1s-1 0-1-1h-1 0c0-2 1-3 1-4 1-1 1-2 2-3h2l-1-1c-1-1 0-3-1-5h1c0-1 0-1-1-1 0-1 1-2 1-2z" class="H"></path><path d="M386 186c1 0 1 0 2 1h0v3 3l-2 1c-1-1 0-3-1-5h1c0-1 0-1-1-1 0-1 1-2 1-2z" class="B"></path><path d="M360 290c1 0 1-1 1-1 1 1 1 2 1 2v1h1c0-2 0-3 1-4h0c0 3 0 7 1 10v1c0 2 1 4 1 6 3 9 3 21 1 31-1-2 0-5 0-7l-1-13c-1-6-3-12-4-18h0l-2-8z" class="W"></path><path d="M360 290c1 0 1-1 1-1 1 1 1 2 1 2v1h1c0-2 0-3 1-4h0c0 3 0 7 1 10v1l-1-1v-1c-1-2-1-4-1-6 1 0 1 0 0-1 0 1 1 2 0 3h-1c0 2 1 3 1 5h-1 0l-2-8z" class="E"></path><path d="M462 344v-4h0c2 6 2 12 2 18 1-4 2-8 4-11 2-1 4-3 5-4h0c3-1 5-2 8-2v1h0-1c-1-1-4 1-6 2-4 2-7 7-8 12-2 8-1 17 0 25-1-1-1-2-1-2h-1l-2-5c1 0 1 0 2-1-1-1-1-2-1-4l-1-1c-1-4 0-7 1-11 0-4 0-9-1-13z" class="m"></path><path d="M346 190c2-3 3-6 6-9l1 1c1 0 2 1 3 2l-1 1v1s-1 1-1 2c1 1 2 1 3 1v3 1c-1 0-3 1-4 2h0-2-1c1-1 1-1 0-1v-1c0 1-1 1-1 2-1 0-1 1-2 1h0v-3h1v-1c-1-1-1-1-1 0-1 0-1 0-2 1v1h-1 0l2-4z" class="E"></path><path d="M350 193c1 0 4-1 5-1h2v1c-1 0-3 1-4 2h0-2-1c1-1 1-1 0-1v-1z" class="i"></path><defs><linearGradient id="I" x1="388.861" y1="293.054" x2="369.57" y2="263.235" xlink:href="#B"><stop offset="0" stop-color="#1a1c1d"></stop><stop offset="1" stop-color="#554d4a"></stop></linearGradient></defs><path fill="url(#I)" d="M382 256v2h1v-8h0v-3 12 7c-1 2 0 4-1 6-1 4-1 9-2 13l-2 18h0c-1-1-1-3-1-5l1-6c0-2 0-6 1-9v-5c1-7 3-16 1-23v-1l2 2z"></path><path d="M287 181c-2-1-3-2-4-3-8-6-11-17-12-27 0-11-1-24 6-33 0 2-1 4-2 6s-2 5-2 8c-1 3-1 7-1 11 1 11 3 20 9 29l-1 1c2 3 5 5 7 8z" class="r"></path><path d="M273 90c5-1 9-1 14-1-4 1-8 1-12 3-9 3-15 10-20 17-4 6-6 12-8 19 0-1-1-2 0-4h0c0-1 1-2 1-3h0v-1l1-1v-1c1-1 1-2 1-3 1-1 2-3 3-5l3-4-3 3c-2 3-3 7-5 11-2 2-3 7-4 10-1 0-1 1-1 1l1-5c4-11 10-21 19-29 2-1 6-3 7-5h-1c1-1 3-2 4-2z" class="N"></path><path d="M320 185l1 1c0 1 1 1 1 1 0 1 0 1-1 1v1 1c1 0 1-1 2-1v4l2 2h0-2 0v1h1l2 2c0 1 1 2 2 3l-2 2h0v3c-1-2-3-3-4-5-1-1-1-2-2-3h0c0-2-2-4-3-5-4-1-7-2-11-2 1 0 2 0 3-1h1c1-1 1-1 2 0h1c2 1 3 0 4-1v-1c0-1 2-2 3-3z" class="W"></path><path d="M321 190c1 0 1-1 2-1v4h-1-1c-1-1 0-1 0-2h-1c-1 1-1 1-2 1h-1v-1c1-1 2-1 4-1z" class="B"></path><path d="M320 198c2 0 2 0 3 2l3 3h0v3c-1-2-3-3-4-5-1-1-1-2-2-3h0z" class="l"></path><path d="M504 244l-1-2h0c1 0 1 0 1 1l1-1c0 1 1 1 1 1v-1h0v-4-1-1h0v-2h-1v1c0 1 1 1 1 2-1 0-1-1-2-2l1-1h1v-1c0-1 1-2 0-3l-1 1-1-1h1 2v2 1c1-2 2-3 4-3 0-1 1-1 2 0 0 0 1-1 1-2 1 0 3-2 4-2h1l-1 1c-1 1-2 1-3 2 0 1 0 2-1 3h-1 0c-4 6-3 13-2 20-1-1-2-3-2-5l-1 1h1l1 4c-3-2-4-5-5-8h-1z" class="W"></path><path d="M505 244h0c1 0 1 0 1 1l1 1h0c0-1 1-2 0-3h0 1v1h0c1 1 1 1 1 2h0v1l-1 1h1l1 4c-3-2-4-5-5-8z" class="z"></path><path d="M490 244c1-1 1-1 2 0l2 1h0c1 0 2 0 2 1h1c1 0 1 1 1 1h1s0 1 1 1l1 1h0c0-1-1-2-1-2v-1h0 1 1c-1-1-2-2-2-3h1c1 0 1 1 2 1v1l1-1h1c1 3 2 6 5 8 1 3 2 6 4 8h0c-2-1-4-3-6-5v1h-1l-1-1c0-1-3-2-4-2l-1 1h0c-1-2-4-3-5-4l1-1c-1-2-6-3-7-5z" class="f"></path><path d="M496 250l1-1c2 1 5 3 7 3 1 1 1 1 2 1h2v2 1h-1l-1-1c0-1-3-2-4-2l-1 1h0c-1-2-4-3-5-4z" class="l"></path><path d="M504 244h1c1 3 2 6 5 8 1 3 2 6 4 8h0c-2-1-4-3-6-5v-2c-1-1-2-1-2-2-1-2-1-4-3-6l1-1z" class="B"></path><path d="M318 199l2 3c1-1 1-1 2-1 1 2 3 3 4 5 1 1 2 3 3 4 2 3 3 6 4 10l2 5v2h-1c-1-3-3-6-4-9-2-3-3-7-6-9-1-1-2-1-3-1-3 0-5 5-7 8 0-3 2-5 3-7v-1-5c1 1 1 2 1 3h1c0-1 0-3-1-4v-2-1z" class="t"></path><path d="M318 199l2 3c1-1 1-1 2-1 1 2 3 3 4 5 1 1 2 3 3 4v1l-4-4h-1-2c-1-1-2-1-3 0l-1 1h0-1v-5c1 1 1 2 1 3h1c0-1 0-3-1-4v-2-1z" class="B"></path><path d="M322 201c1 2 3 3 4 5 1 1 2 3 3 4v1l-4-4c-2-2-4-3-5-5 1-1 1-1 2-1z" class="Z"></path><path d="M212 291v-4c1 1 1 2 1 3v2l1 2v1l2 10h0c-1 0-1 0-2 1h0l1 2h-1l2 2h-1c-1-1-3-2-4-3h-2c-5 0-10 1-14 1-4 1-9 2-13 3l-6 3v-1c11-5 22-8 33-7h1c0-5 1-10 2-15z" class="N"></path><path d="M213 292l1 2v1l2 10h0c-1 0-1 0-2 1h0l1 2h-1c-1 0-1-1-2-1-1-2 1-13 1-15z" class="H"></path><path d="M326 188c2 3 3 5 4 7v2 1s1 1 1 2h-1c1 2 1 5 2 7 0 2 1 3 1 5 1 3 1 5 0 7v1c-1-4-2-7-4-10-1-1-2-3-3-4v-3h0l2-2c-1-1-2-2-2-3l-2-2h-1v-1h0 2 0l-2-2v-4h1c1 0 1 0 2-1z" class="n"></path><path d="M326 193c1 2 3 5 3 8v2l-1-2c-1-1-2-2-2-3v-5z" class="AB"></path><path d="M326 188c2 3 3 5 4 7v2 1s1 1 1 2h-1l-1-3c-1-4-2-6-6-8l3 4v5l-2-2h-1v-1h0 2 0l-2-2v-4h1c1 0 1 0 2-1z" class="i"></path><path d="M328 201l1 2 1 3c1 2 1 6 3 7v-1c1 3 1 5 0 7v1c-1-4-2-7-4-10-1-1-2-3-3-4v-3h0l2-2z" class="AB"></path><path d="M328 201l1 2 1 3h0c-2 0-3-2-4-3h0l2-2z" class="i"></path><path d="M388 209h1 3v1c1 0 2 0 3-1v3h2l-1 1v1c1 1 1 2 1 3l1 3v1-1c-1 0-1-1-1-1l-1-1c0 1 0 2-1 3 0-1-1-1-1-2-1-1-2-1-2-1-1-1-1 0-2 0 0-1 0-1-1-1h0l-1-1c0 1 0 1-1 1h0s0 1 1 1v1l1 1v2c-2-2-4-4-5-6h0c-2-1-3-3-4-4 1 0 2 0 3-1l3-2h2z" class="Y"></path><path d="M392 210c1 0 2 0 3-1v3h2l-1 1v1c1 1 1 2 1 3l-3-3c-1-1-3-2-3-4h1z" class="H"></path><path d="M392 217l1-1c-1-1-2-2-2-4h0c2 1 5 4 5 6 0 1 0 2-1 3 0-1-1-1-1-2v-2h-2z" class="P"></path><path d="M388 219l-3-3v-2c0-1 2-2 3-2v-1h0 1c-1 2-1 3-2 5h0c1 0 2-1 3-2 0 1 0 1 1 2v1h1 2v2c-1-1-2-1-2-1-1-1-1 0-2 0 0-1 0-1-1-1h0l-1-1c0 1 0 1-1 1h0s0 1 1 1v1z" class="B"></path><path d="M383 211l3-2c1 1 2 1 3 2h0-1 0v1c-1 0-3 1-3 2v2l3 3 1 1v2c-2-2-4-4-5-6h0c-2-1-3-3-4-4 1 0 2 0 3-1z" class="P"></path><path d="M372 193l1-2 1 1v1l-1 4h-1l1 3-1 1h0v3l-1-1h-1l-1-2c-1 0-1-1-1-1-2 0-3 0-4-1-1 1-2 1-3 0l-1-1c0 1-1 2-2 2h0v-1h-1c-1 2-3 6-3 9l-1-1v-2s0-1 1-1c0-1 1-1 0-2v-1c1-1 1-1 1-2v-1l1-1v-1c4-2 9-2 13-4 1 1 2 1 3 1z" class="r"></path><path d="M372 193l1-2 1 1v1l-1 4h-1l1 3-1 1h0v3l-1-1h-1l-1-2c-1 0-1-1-1-1-2 0-3 0-4-1h3l1 1h0v-2c0-2 1-3 3-5v1l1-1z" class="v"></path><path d="M369 201l2-1s1 0 1 1-1 2-1 2h-1l-1-2z" class="l"></path><path d="M340 156l1 5v-2c1 0 1 0 1 1h1 0l4 11h-1l2 3h-1l-1-1c-2 2-4 4-5 6-1 0-1-1-1-2h0l-1-1c0 1 0 2-1 2v-7-5l1-7 1-3z" class="m"></path><path d="M340 174v-2l2-2h0c0 1 1 1 1 1v1l-1 1h-1 0l1 1c-1 1-1 2-2 3h0l-1-1s1-1 1-2z" class="f"></path><path d="M338 166v6h1c0 1 0 2 1 2 0 1-1 2-1 2 0 1 0 2-1 2v-7-5z" class="l"></path><path d="M342 165c-1 2-1 5-2 7l-1-1c1-2 1-8 2-10 0 1 1 2 1 4z" class="z"></path><path d="M341 161v-2c1 0 1 0 1 1 1 2 1 4 2 6 0 1 1 2 1 4h-1l-1-3-1-1v-1c0-2-1-3-1-4h0z" class="a"></path><path d="M346 171l2 3h-1l-1-1c-2 2-4 4-5 6-1 0-1-1-1-2 1-1 1-2 2-3l-1-1h0 1v2h1c0-2 1-2 3-3v-1z" class="n"></path><path d="M334 254c1 1 0 3 1 4 0 2 0 4 1 6l-1 1v1l1 2v-1-1c0-2-1-5-1-6l1-1c0-1 0-2-1-3 0-1 0-2-1-4 0-1-1-2-1-2v-1c0-1-1-2-1-2-1-1-1-2-1-3h-1c0-1 0-1-1-2h0v-1c0-1-2-3-2-4l-1-1h1 0l2 1c0 1-1 0 0 1l1 1 1 1v1l-1 1h0c1 2 3 5 4 7 0 3 2 5 2 8v2 10c0 1 0 4 1 5v2c1 2 1 3 0 5v3 2c-1-2-1-3-1-5 0-1-1-2-1-4 0-1-1-1-1-2-1-1-1-2-1-3-1-5-4-9-7-12v-1c0-1-1-1-1-2-1 0-2-1-2-1 0-1 0-1-1-1v-1c-1-1-1-1-1-2l2 2c0-1-1-1-1-2h1c1 1 1 2 2 2 1-1 1-1 2-1 1 1 1 1 1 2l-1-1h-1c0 1 1 2 1 3 1 1 3 3 3 4l2 2v1 1s1 1 1 2c0 0 1 0 1 1l1-1c0-1-1-13-1-13z" class="B"></path><defs><linearGradient id="J" x1="326.791" y1="197.761" x2="334.451" y2="191.797" xlink:href="#B"><stop offset="0" stop-color="#5e5757"></stop><stop offset="1" stop-color="#726f6e"></stop></linearGradient></defs><path fill="url(#J)" d="M323 177h0c2 2 3 4 5 5h2c3 9 5 17 4 26h0l-2-2v1c-1-2-1-5-2-7h1c0-1-1-2-1-2v-1-2c-1-2-2-4-4-7l-3-5c-1 0-1-1-1-2v-4h1z"></path><path d="M331 200h0c0 1 1 2 2 3 0 1 0 2-1 3v1c-1-2-1-5-2-7h1z" class="Y"></path><path d="M323 177h0c0 1 1 2 1 3s2 2 2 3h-3c-1 0-1-1-1-2v-4h1z" class="a"></path><path d="M326 183c2 2 5 9 4 11v1c-1-2-2-4-4-7l-3-5h3z" class="s"></path><path d="M480 286c1 2 2 5 4 6l2 3c1 2 2 3 4 5 1 1 3 2 4 3h1-1 0c0-1 0-1-1-2h0v-1c-1-1-1-2-2-3v-1c-1-1-1-2-2-3h0v-1c-1-1 0-1-1-1v-1l-3-6v-1c0-1-1-1-1-2-1-2-2-3-3-5 0-1-1-2-1-3-2-3-4-5-6-8 2 1 2 2 4 3 2 3 5 7 6 10 1 2 2 4 3 5v1c1 1 2 2 2 3 1 1 1 2 1 3v1c1 1 1 2 2 4h0 0v1h0v-4h1c1 1 0 3 0 4v1 2c1 2 2 3 2 5l-1 1c-1-2-3-3-4-4s-3-2-4-3h0l-1-1-1 1c-1 0-1 0-2-1h0l-2-1-2-2c-1 0-1-1-2-2l-3-2h0 0 2c0 1 1 1 2 1s1 1 2 1h1 0l1 1s-1-1-1-2c1-1 0-3-1-4l1-1z" class="B"></path><path d="M266 93l3-1h1c-1 2-5 4-7 5-9 8-15 18-19 29v-1c0-3 1-6 2-9l4-9h-2c0-2 2-4 3-6l2-2 1-1c3-2 8-6 12-5z" class="p"></path><path d="M253 99l1-1c3-2 8-6 12-5-6 4-12 8-16 14h-2c0-2 2-4 3-6l2-2z" class="h"></path><path d="M219 98c5 0 11-1 16-2 19-5 38-14 58-7h-2-4c-5 0-9 0-14 1l-1-1c-1 0-4 1-5 1-4 0-9 0-14 2l-3 1-8 3-7 1-4 2c-5 1-10 2-14 2v-1h0l2-2zm325 41c5 0 10 0 15 1 0 1 1 1 2 2 1 0 2 0 3 1-1 0-2-1-3-1h0-2-1c-1 0-2 0-4-1h-6c-10 1-18 3-26 9-3 2-5 4-7 5l-9 9h0c0-2 1-3 2-4s3-2 3-3 1-2 1-3c1 0 2-2 3-2l11-9c1 0 2 0 3 1h0c5-2 10-4 15-5z" class="a"></path><path d="M544 139c5 0 10 0 15 1 0 1 1 1 2 2 1 0 2 0 3 1-1 0-2-1-3-1h0-2-1c-1 0-2 0-4-1h-6c2-1 4 0 6 0h-1c-3-1-5 0-7 0-1 0-2-1-3 0h-1-3c2-1 4 0 6-1 0 0-1 0-1-1z" class="Z"></path><path d="M526 143c1 0 2 0 3 1h0c-5 3-10 7-14 11h0l-9 9h0c0-2 1-3 2-4s3-2 3-3 1-2 1-3c1 0 2-2 3-2l11-9z" class="h"></path><path d="M386 201v-1l-2 1h-1c1-1 1-2 2-2 1-1 1-2 2-3v-1h0c1 0 1 0 2 1 0 1 0 1 1 1h1c1 1 1 2 2 3v3h1l1-1c1-1 1 0 2-1h1c2 0 4-1 6-3v1c-2 2-6 3-8 5h-2c0 1 2 2 2 3v1c1 2 1 1 1 3 1 0 1 0 1-1h1v1l-1 1h-1-2v-3c-1 1-2 1-3 1v-1h-3-1-2l-3 2v-2l-1-1h-2l-1-3h2 0c1 0 1-1 2-1h1v2l1-1c1 0 2 1 2 1h1v-1c-1 0-1-1-1-2 0 0 1 0 1-1l-1-1h-1z" class="P"></path><path d="M382 208c1-1 1-1 2-1h0 3l1 2h-2l-3 2v-2l-1-1z" class="H"></path><path d="M389 209c0-1 0-2 1-3 1 0 2-1 2-2l1 1h0c1 1 1 2 2 3h-2l-1 1h-3zm-2-8c1-1 1-1 2-1l1-1c1 0 1 1 2 2 0 1 0 1-1 1v2c-1 1-2 1-2 1h-1c-1 0-1-1-1-2 0 0 1 0 1-1l-1-1z" class="B"></path><path d="M171 95l17 3 1 1c5 1 11 0 16 1h-19-32c-2-1-5 0-7 0l-1-1c-2 0-3-1-4 0-1 0-2 1-2 1-1 0-2 0-2-1h3c2-1 4-1 6-2h1 8 10c2 0 3 0 4-1 1 0 1 0 1-1z" class="AF"></path><path d="M250 93l3-1c5-2 10-2 14-2 1 0 4-1 5-1l1 1c-1 0-3 1-4 2l-3 1c-4-1-9 3-12 5l-1 1-2 2s-1 0-1 1c-2 0-1 1-3 1v-1c-1 0-2 0-3 1 0-1-1-2-1-3v-1c0-1 0-3-1-3l8-3z" class="AD"></path><path d="M242 96l8-3c1 1 1 2 0 3v1l-1 3h1 1s1-1 2-1l-2 2s-1 0-1 1c-2 0-1 1-3 1v-1c-1 0-2 0-3 1 0-1-1-2-1-3v-1c0-1 0-3-1-3z" class="AC"></path><path d="M385 189c1 2 0 4 1 5l1 1h-2c-1 1-1 2-2 3 0 1-1 2-1 4h0 1c0 1 1 1 1 1h1 1v-2h1l1 1c0 1-1 1-1 1 0 1 0 2 1 2v1h-1s-1-1-2-1l-1 1v-2h-1c-1 0-1 1-2 1h0-2v-2c-2 1-3 1-4 1l-2-4-1-3h1l1-4 3-3c1 1 1 1 1 3 0-1 1-2 1-3h5c1 0 1 0 1-1z" class="B"></path><path d="M381 203v2h-2v-2h2z" class="W"></path><path d="M377 190c1 1 1 1 1 3 0 0 1 0 0 1h-1v2c-1 0-2 1-3 1h-1l1-4 3-3z" class="B"></path><path d="M385 189c1 2 0 4 1 5l1 1h-2c-1 1-1 2-2 3 0 1-1 2-1 4h0 1c0 1 1 1 1 1h1 1v-2h1l1 1c0 1-1 1-1 1 0 1 0 2 1 2v1h-1s-1-1-2-1l-1 1v-2h-1c-1 0-1 1-2 1h0v-2c0-1-1-1-1-2 0-3 1-3 3-5v-2l-1 1c0 1-1 1-2 2v2h-2v-1c1 0 1-1 1-1 1-2 2-3 4-4 0 0 1 0 1-1v-2c1 0 1 0 1-1z" class="f"></path><path d="M346 408h0l-1-1v-1c-1-2 2-7 3-10l7-18c1-4 3-7 5-11-1 2-1 4-1 5-1 2-1 4-2 5 1 1 2 1 2 2 0 2-1 3-2 5s-1 4-1 6c-1 2-2 5-3 7-2 4-4 8-7 11h0z" class="C"></path><path d="M357 377c1 1 2 1 2 2 0 2-1 3-2 5s-1 4-1 6c-1 2-2 5-3 7 0-1-1-1-1-2 0-2 4-14 5-18z" class="c"></path><path d="M344 256c1-1 3-4 4-4l1 1c0 1 1 1 0 2v1c0 3-1 3-2 5-1 0-1 1-2 1l1 1h0l2-2h0c0-1 1-1 2-1v2c-1 1-2 2-2 3l-2 2c0 1-4 7-5 8-1 2-2 4-3 7h0l-1-1c1-2 1-3 0-5v-2c-1-1-1-4-1-5l2-2c0-2 1-3 3-6 1-2 4-3 5-5l-1-1v1h-1z" class="f"></path><path d="M348 261c0-1 1-1 2-1v2c-1 1-2 2-2 3l-2 2c0 1-4 7-5 8-1 2-2 4-3 7h0l-1-1c1-2 1-3 0-5 1-1 2-3 2-4 1-4 3-8 6-10-1 2-3 4-3 7l-1 3 7-10v-1h0z" class="Y"></path><path d="M348 261c0-1 1-1 2-1v2c-1 1-2 2-2 3l-2 2c0 1-4 7-5 8 2-4 5-8 7-12v-1-1h0z" class="i"></path><path d="M341 179c1-2 3-4 5-6l1 1h1l1 2c1 1 2 2 3 2l4 4h-3l-1-1c-3 3-4 6-6 9l-1-1c-1 1-1 2-2 3s-2 2-2 4h-1v-3l-1 1v-1l-1 1v-2c1-3 1-5 2-8l-1-2c0-1 1-2 2-3z" class="v"></path><path d="M340 191h2v-1c1-2 4-5 5-8 0-1 1-2 2-3h1c-2 3-4 7-5 10-1 1-1 2-2 3s-2 2-2 4h-1v-3-2z" class="m"></path><path d="M352 178l4 4h-3l-1-1c-3 3-4 6-6 9l-1-1c1-3 3-7 5-10h1l1-1z" class="t"></path><path d="M341 179c1-2 3-4 5-6l1 1h1l1 2c-3 5-7 9-9 15v2l-1 1v-1l-1 1v-2c1-3 1-5 2-8l-1-2c0-1 1-2 2-3z" class="W"></path><path d="M341 179c1-2 3-4 5-6l1 1-7 10-1-2c0-1 1-2 2-3zm-213-40c1-1 2-1 4-1 1-1 3 0 5 0 2 1 4 2 6 2 3 1 5 3 7 5 3 1 5 3 7 5l-2 1h1v2l1 1v1c-5-5-11-9-17-12-12-4-26-4-37 1h0c1-1 3-2 5-3h2l5-2c4-1 9-1 13 0z" class="a"></path><path d="M128 139c1-1 2-1 4-1 1-1 3 0 5 0 2 1 4 2 6 2l1 3h-2c-4-2-9-3-14-4z" class="F"></path><path d="M143 140c3 1 5 3 7 5 3 1 5 3 7 5l-2 1h1v2c-2-1-3-3-5-4-3-3-6-4-9-6h2l-1-3z" class="M"></path><path d="M537 137c2-1 3-1 5-1 6-2 13 0 19 1l8 3 1 1v2l1 1h1l3 3h-1-1l-9-5-5-2c-5-1-10-1-15-1-5 1-10 3-15 5h0c-1-1-2-1-3-1 1-2 2-2 3-3 3-2 5-3 8-3z" class="c"></path><path d="M529 140c3-2 5-3 8-3v1h-2l-1 1c2 1 7-1 10-1 5 0 10 0 15 1 2 1 4 1 5 2v1l-5-2c-5-1-10-1-15-1-5 1-10 3-15 5h0c-1-1-2-1-3-1 1-2 2-2 3-3z" class="F"></path><path d="M329 279v-1-2h-1v-2h1v1l1-1-1-1v-1c-1 0-1 0-1-1h0l-1-1c0-1-1-1-1-2s0-1-1-1v-2h-1v-1c-1-1-1-1-1-2v-1c1 0 1 0 1 1v1h1c1 0 2 2 3 3h0c-1-1-1-2-2-3l-1-1s0-1-1-2h0c0-1 0 0-1-1 0-1-1-2-2-3h0l-1-1v-2h0c1 1 1 1 1 2 1 0 1 0 1 1 0 0 1 1 1 2h1 0l1-1c0 1 1 1 1 2v1c3 3 6 7 7 12 0 1 0 2 1 3 0 1 1 1 1 2 0 2 1 3 1 4 0 2 0 3 1 5v-2 17-2c-1 1-1 2-1 3 0-3-1-5-1-7s0-3-1-4c0-1-1-2-1-3v-1c0-2-1-2-1-3v-2l-1-1-1-2c0 1 1 3 1 4s0 2-1 3v-2c-1-2 0-3-1-5h0z" class="f"></path><path d="M332 279c-2-5-5-10-7-16 2 3 3 5 5 8 0 2 3 5 3 7l-1 1z" class="Y"></path><path d="M330 271c1-2-1-3-1-5-1-2-3-3-3-5v-1c3 3 6 7 7 12v5l1 1v5c-1-1-1-2-2-4l1-1c0-2-3-5-3-7z" class="AB"></path><path d="M472 237l1 1h0v2h0 0 1v2c-3 8-6 16-5 25l-1-1h0v3 7h-1l-8 10c0-2-1-4 0-6 2-2 3-6 3-8l4-16c1-2 2-4 2-7l4-12z" class="R"></path><path d="M473 240h0 1v2c-3 8-6 16-5 25l-1-1h0v3c-1-1 0-2-1-3 0-8 2-18 6-26z" class="G"></path><path d="M300 190c1 0 4 1 6 1 4 0 7 1 11 2 1 1 3 3 3 5h0c1 1 1 2 2 3-1 0-1 0-2 1l-2-3v1 2c1 1 1 3 1 4h-1c0-1 0-2-1-3h0c-1-1-1-2-3-4-1-1-2-1-3-1-2 0-2 1-3 2h0c-1 0-2 0-3-1-2 1-3 4-6 5v-1h-1c1-3 2-4 1-7 0-2 0-2-2-3 1 0 1-1 1-2l2-1z" class="v"></path><path d="M304 193l3 1c1 1 2 1 3 2 1 0 1 0 2 1h-1-1c-2 0-3 0-5-1v-1c0-1-1-1-1-2z" class="r"></path><path d="M307 194c2 0 5 0 7 1v1h1v1h-1-2c-1-1-1-1-2-1-1-1-2-1-3-2z" class="s"></path><path d="M317 193c1 1 3 3 3 5h0c1 1 1 2 2 3-1 0-1 0-2 1l-2-3-1-1c-2 0-2 0-3-1h1v-1h-1v-1l1 1c1-1 1-2 2-3z" class="Z"></path><path d="M317 198l1-1c1 1 1 1 2 1 1 1 1 2 2 3-1 0-1 0-2 1l-2-3-1-1z" class="g"></path><path d="M300 190c1 0 4 1 6 1 4 0 7 1 11 2-1 1-1 2-2 3l-1-1c-2-1-5-1-7-1l-3-1c-2 0-4-1-6-2l2-1z" class="a"></path><path d="M298 191c2 1 4 2 6 2 0 1 1 1 1 2v1l-1 2c0 1 0 1 1 1-2 1-3 4-6 5v-1h-1c1-3 2-4 1-7 0-2 0-2-2-3 1 0 1-1 1-2z" class="t"></path><path d="M299 203c1-2 3-4 4-6 0-1 0-2 1-3l1 1v1l-1 2c0 1 0 1 1 1-2 1-3 4-6 5v-1z" class="m"></path><path d="M322 172c1-2 3-4 4-7 2-6 3-13 5-20 1 2 1 3 1 5h0c2 6 0 12 2 18v1l-1-1h-1v2 1s0 1-1 1v1c1 1 1 1 0 2h-1l-1-1h0l-1-1h0c-1 2 0 3 0 4l1 3c0 1 0 1 1 2h-2c-2-1-3-3-5-5h0-1l-2 1h0c1-1 2-3 3-5l-1-1z" class="s"></path><path d="M330 165c0 3 1 5 1 7v1c1 1 1 1 0 2h-1l-1-1h0l-1-1c1 0 1-1 1-2h1c0-1-1-2-1-2h0c-1-2 0-3 1-4z" class="n"></path><path d="M329 174v-3h1c0 1 0 1 1 1v1c1 1 1 1 0 2h-1l-1-1h0z" class="z"></path><path d="M332 170v-2c-1-5 0-9 0-13 0-2-1-3 0-5 2 6 0 12 2 18v1l-1-1h-1v2z" class="n"></path><path d="M323 173c3-4 5-9 7-14v1 5c-1 1-2 2-1 4h0s1 1 1 2h-1c0 1 0 2-1 2h0c-1 2 0 3 0 4l1 3c0 1 0 1 1 2h-2c-2-1-3-3-5-5h0-1l-2 1h0c1-1 2-3 3-5z" class="m"></path><path d="M329 169s1 1 1 2h-1c0 1 0 2-1 2h0c-1 2 0 3 0 4l1 3c0 1 0 1 1 2h-2c-2-1-3-3-5-5h0l-1-1 3-2 2-1c0-1 1-1 2-2v-2z" class="l"></path><path d="M325 174c1 1 1 5 2 7 1 0 1 1 1 1-2-1-3-3-5-5h0l-1-1 3-2z" class="E"></path><path d="M345 189l1 1-2 4h0 1v-1c1-1 1-1 2-1 0-1 0-1 1 0v1h-1v3h0c1 0 1-1 2-1v2l-1 3c0 2-4 5-3 7l-1 2-3 6h0 0l-3 8-1-1v-3-9c0-6 0-12 1-18v2l1-1v1l1-1v3h1c0-2 1-3 2-4s1-2 2-3z" class="i"></path><path d="M339 199l2-1v-1h2s0 1-1 1v3c-1 2-1 8 0 9v1c-1 1-1 2-1 4h0 0-1l-2 3c2-6 1-13 1-19z" class="Y"></path><path d="M345 189l1 1-2 4h0 1c1 1 0 3-1 4 0 2-1 4-1 6s0 5-1 7v-1c-1-1-1-7 0-9v-3c1 0 1-1 1-1h-2v1l-2 1v-5l1-1v3h1c0-2 1-3 2-4s1-2 2-3z" class="n"></path><path d="M345 189l1 1-2 4c-1 0-1 0-1 1h0-1c0-1 1-2 1-3 1-1 1-2 2-3z" class="v"></path><path d="M345 194v-1c1-1 1-1 2-1 0-1 0-1 1 0v1h-1v3h0c1 0 1-1 2-1v2l-1 3c0 2-4 5-3 7l-1 2-3 6c0-2 0-3 1-4 1-2 1-5 1-7s1-4 1-6c1-1 2-3 1-4z" class="i"></path><path d="M345 194v-1c1-1 1-1 2-1 0-1 0-1 1 0v1h-1c0 1 0 1-1 1v2 1c-1 1-1 0 0 1 0 1-1 4-2 5l-1 1c0-2 1-4 1-6 1-1 2-3 1-4z" class="AB"></path><path d="M484 298l1-1 1 1h0c1 1 3 2 4 3s3 2 4 4c1 0 1 1 1 1 0 1 1 1 2 2 1 0 1 1 1 2h1l3 3c2 2 3 4 5 7l1 2v1h1v2c1 2 1 3 2 4v1 2h-1 0v-2l-1-1-1-1-4-8-2-1c-2-4-6-5-10-6l-4-2h0l-14-3c-2 0-4-1-7-1-2 0-3 1-5-1 4 0 8 0 12 1h3l3 1h1 2 0 1 1v1h3c1 0 1 1 2 1h1 1 0l-4-2h0-3-1l-1-1c2 0 4 1 5 0l1-1 3 2v-1c-2-1-3-2-4-3h0l-3-3h0v-1c0-1-1-1-1-2z" class="i"></path><path d="M492 307h2c1 1 2 2 2 4h0l-4-3v-1z" class="P"></path><path d="M504 320c1 1 1 2 2 3v1h2v1c0 1 1 1 1 2v2l-1-1-4-8z" class="E"></path><path d="M484 298l1-1 1 1h0c1 1 3 2 4 3 0 2 3 4 4 6h-2c-2-1-3-2-4-3h0l-3-3h0v-1c0-1-1-1-1-2z" class="W"></path><defs><linearGradient id="K" x1="122.268" y1="107.355" x2="119.579" y2="101.527" xlink:href="#B"><stop offset="0" stop-color="#f0d8d8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#K)" d="M135 96h3 6c3 0 6 1 8 1 1-1 2 0 4 0h-8-1c-2 1-4 1-6 2h-3c0 1 1 1 2 1 0 0 1-1 2-1 1-1 2 0 4 0l1 1c2 0 5-1 7 0-8 0-15 0-23 2-4 1-9 3-13 5l-4 1c-1 1-3 2-4 3v-1c-4 3-7 6-10 10l-2-2c3-3 5-7 9-9 8-7 18-11 28-13z"></path><path d="M110 110c2-1 3-2 5-2 2-2 5-3 8-4 8-4 16-6 25-7h-1c-2 1-4 1-6 2h-3c0 1 1 1 2 1 0 0 1-1 2-1 1-1 2 0 4 0l1 1c2 0 5-1 7 0-8 0-15 0-23 2-4 1-9 3-13 5l-4 1c-1 1-3 2-4 3v-1z" class="k"></path><path d="M462 250l3-7c1 1 1 1 1 2 1 0 2 1 2 2v1 1c0 3-1 5-2 7l-4 16c0 2-1 6-3 8-1 2 0 4 0 6v4h-1v3-2-1c0-2 0-4-1-5v-2c-1-1-1-2-1-2v-1l1-5 1-4h0c0-2-2-1-3-2l2-6 5-13z" class="J"></path><path d="M457 263h0c1 1 1 2 2 2h2 0l-1 1h0c0 2-1 3-1 5h-1 0 0c0-2-2-1-3-2l2-6z" class="F"></path><path d="M457 275l1-4h0v1c0 1 0 4-1 5 0 2-1 3 0 5h1v-2c1 0 1-1 1-1v-1c1 0 1-1 1-2v-1h1v-1c0-2 0-2 1-2 0 2-1 6-3 8-1 2 0 4 0 6v4h-1v3-2-1c0-2 0-4-1-5v-2c-1-1-1-2-1-2v-1l1-5z" class="O"></path><path d="M583 143c0 1 1 1 2 1 1 2 2 3 2 4 2 3 3 6 4 9 2 6 3 12 2 19v5c-1-3-2-5-2-8h-1v1h0c0-1 0-2-1-2v3h0l-1-4-2-5-2-3c-2-3-3-6-5-8l-3-3-4-4h1v-1h1 1 2v-1c3 3 6 7 8 11v1h0c0 1 1 1 1 2h0c1 0 0-1 0-1v-2 1h1v-2-1l-6-11 2-1z" class="b"></path><path d="M578 151l3 3c-1 1-2 1-2 1l-3-3 2-1z" class="U"></path><path d="M587 156l3 10h0l-1-3c-1 0-1 0-1 1-1-1-1-2-1-2 0-1-1-1-1-2 1 0 0-1 0-1v-2 1h1v-2z" class="L"></path><path d="M573 147h1l4 4-2 1-4-4h1v-1z" class="k"></path><path d="M587 165l3 8v1h0c0-1 0-2-1-2v3h0l-1-4-2-5h1v-1z" class="U"></path><path d="M583 143c0 1 1 1 2 1 1 2 2 3 2 4 0 2 1 4 0 5v1-1c0-1-1-3-1-4l-1-1-1-1h1v-1-1l-1 1h-1l1 1c1 2 3 6 3 8l-6-11 2-1z" class="Q"></path><path d="M581 154c3 3 4 7 6 11v1h-1l-2-3c-2-3-3-6-5-8 0 0 1 0 2-1z" class="u"></path><path d="M370 253c3 5 6 11 7 17 0 2 0 5 1 8v5h1c-1 3-1 7-1 9l-1-5-1-1v5l-1-1v-1 1h0v4c-1 1-1 2-2 3 0-3 0-6-1-9-2-5-6-10-11-13-1-1-3-2-5-2l-1-1v-1c2 0 3 0 4 1h0c2 1 3 2 5 3 1 1 2 2 3 2l-1-1c-3-4-6-6-9-9 4 1 8 3 10 7h2c1 4 3 8 4 11v1h0c1-1 0-3 0-4v-6c-1-3-1-7-2-10l-1-3-1-1v-2h0c1 1 2 3 2 4v1c1 1 1 2 2 4h1l-1-4h0c-1-4-3-8-3-12z" class="E"></path><path d="M366 276c2 1 3 2 3 4-1 0-2-2-2-3l-1-1z" class="B"></path><path d="M376 286v-4l1 1c0 1 0 1 1 2v-2h1c-1 3-1 7-1 9l-1-5-1-1z" class="H"></path><path d="M373 269h1 0l2 4c-1 2-1 3-1 5h-1v-4c-1-1-1-2-1-3v-2z" class="B"></path><defs><linearGradient id="L" x1="426.355" y1="125.129" x2="410.907" y2="131.104" xlink:href="#B"><stop offset="0" stop-color="#c51b20"></stop><stop offset="1" stop-color="#ff413b"></stop></linearGradient></defs><path fill="url(#L)" d="M398 91c2 0 3 1 4 2 4 2 7 4 10 7 4 4 8 10 11 16 1 1 2 3 2 4 2 3 3 6 3 8 1 2 1 4 1 5l1 23c1 0 1 1 1 0v2l-5 18v-4c0-1 1-2 0-2v-6-3l1-6c1-11 0-22-4-32-3-10-9-19-17-25-3-3-7-4-10-6h0c1 0 2 0 2-1z"></path><path d="M425 120c2 3 3 6 3 8 0 0-1 0-1-1h-1v-2-1h0c-1-2-1-2-1-4z" class="M"></path><defs><linearGradient id="M" x1="549.692" y1="113.347" x2="555.015" y2="104.115" xlink:href="#B"><stop offset="0" stop-color="#f0c3c1"></stop><stop offset="1" stop-color="#fdffff"></stop></linearGradient></defs><path fill="url(#M)" d="M506 95c4-1 7-1 11-1 17 0 32 4 46 15 4 2 7 6 10 9 2 2 4 5 5 7v1l2 2v2l-1 1-7-8c-8-9-18-17-29-22-3-1-7-2-10-2-5-1-10-2-15-2h-10l-3-1-1-1h2z"></path><path d="M504 95h2 0c3 0 7-1 9 0h3 2v2h-1-1-10l-3-1-1-1z" class="x"></path><defs><linearGradient id="N" x1="572.297" y1="134.983" x2="555.799" y2="149.766" xlink:href="#B"><stop offset="0" stop-color="#841519"></stop><stop offset="1" stop-color="#c32022"></stop></linearGradient></defs><path fill="url(#N)" d="M536 130c0-1 2 0 3-1v-1h2c5 0 9 1 14 2l1-1h2c1 1 2 1 3 2h0v-1c1 0 3 2 4 2v-1s-1 0-1-1h0l3 1h0l-1-1h1c3 2 5 3 7 5l2 3 1 1h1 1c1 1 3 2 4 4l-2 1 6 11v1 2h-1v-1 2s1 1 0 1h0c0-1-1-1-1-2h0v-1c-2-4-5-8-8-11v1h-2l-3-3h-1l-1-1v-2l-1-1-8-3h3c-1 0-2-1-3-2h-1c-3-2-8-3-12-3-2-1-4-1-5-1h-7 2v-1h-2z"></path><path d="M556 129h2c1 1 2 1 3 2h0v1l-6-2 1-1z" class="F"></path><path d="M579 139c1 1 3 2 4 4l-2 1-4-5h1 1z" class="C"></path><path d="M560 135c4 1 7 3 10 5h-1l-8-3h3c-1 0-2-1-3-2h-1z" class="S"></path><path d="M570 140c3 1 5 4 7 6v1h-2l-3-3h-1l-1-1v-2l-1-1h1z" class="V"></path><path d="M566 130h1c3 2 5 3 7 5l2 3c-1 0-2-1-3-2h-1c-3-1-3-2-6-3-1-1-3-1-5-2v-1c1 0 3 2 4 2v-1s-1 0-1-1h0l3 1h0l-1-1z" class="G"></path><defs><linearGradient id="O" x1="331.682" y1="201.893" x2="339.686" y2="200.099" xlink:href="#B"><stop offset="0" stop-color="#6b605f"></stop><stop offset="1" stop-color="#999796"></stop></linearGradient></defs><path fill="url(#O)" d="M334 168h0v2h1v-3l1 2s0-1 1-1v2l1 1v7c1 0 1-1 1-2l1 1h0c0 1 0 2 1 2-1 1-2 2-2 3l1 2c-1 3-1 5-2 8-1 6-1 12-1 18v9 3 3h-1v-2l-1 2h0l-2-5v-1c1-2 1-4 0-7 0-2-1-3-1-5v-1l2 2h0c1-9-1-17-4-26-1-1-1-1-1-2l-1-3c0-1-1-2 0-4h0l1 1h0l1 1h1c1-1 1-1 0-2v-1c1 0 1-1 1-1v-1-2h1l1 1v-1z"></path><path d="M329 180l4 7v1l1-1v-2h1l1 38-1 2h0l-2-5v-1c1-2 1-4 0-7 0-2-1-3-1-5v-1l2 2h0c1-9-1-17-4-26-1-1-1-1-1-2z" class="f"></path><path d="M334 168h0v2h1v-3l1 2s0-1 1-1v2l1 1v7c1 0 1-1 1-2l1 1h0c0 1 0 2 1 2-1 1-2 2-2 3v2c-1 2-1 5-2 6-1-1-1-2-1-3 1-1 1-1 0-2v-2-2h-1v3 1h-1v2l-1 1v-1l-4-7-1-3c0-1-1-2 0-4h0l1 1h0l1 1h1c1-1 1-1 0-2v-1c1 0 1-1 1-1v-1-2h1l1 1v-1z" class="m"></path><path d="M332 170v-2h1l1 1v2c1 2 0 3 0 5l-1-1v-2c0-1 0-1-1-2h0v-1z" class="Y"></path><path d="M332 171h0c1 1 1 1 1 2v2l1 1c1 1 1 2 0 3h0c-1-1-1-1-1-2s0-1-1-1-1 0-1 1c-1-1-1-2-2-3l1 1h1c1-1 1-1 0-2v-1c1 0 1-1 1-1z" class="l"></path><path d="M331 177c0-1 0-1 1-1s1 0 1 1 0 1 1 2c0 2 0 4 1 5v1h-1v2l-1 1v-1-1c0-2 0-3-1-5l-1-4z" class="z"></path><path d="M329 180l-1-3c0-1-1-2 0-4h0l1 1h0c1 1 1 2 2 3l1 4c1 2 1 3 1 5v1l-4-7z" class="Z"></path><path d="M336 169s0-1 1-1v2l1 1v7c1 0 1-1 1-2l1 1h0c0 1 0 2 1 2-1 1-2 2-2 3v2l-1 1h0c0-1-1-2-1-3-1-1-1-2-1-3v-10z" class="Y"></path><path d="M281 185c2 1 1 1 3 3v-2h1 0c1 0 1 1 1 1l1 1-1 2 1 1h0c2-1 4-1 6-1h0c2 1 3 2 4 3 2 1 2 1 2 3 1 3 0 4-1 7l-9 12h-2 0v1h1l-2 2c0-1-1-1-1-2-1-1 0-2-1-3-1 0-1-1-2-2h1l1-2h0v-2c0-1-1-1-1-2-1 0 0-1 0-1h-1-1v-1h1c1-1 1-3 2-4l-1-1c-1 0-3 1-4 2v-1l1-1-1-1 1-2h-1c-1-1-1-2-1-3 1 0 1 0 1-1 1 0 1 1 2 1s2 0 3 1c0 0 2 1 2 2v-1h0c0-1 0-1-1-2h0c1-1 1-2 1-3-1 0-2 1-3 1h0c0-1-1-1-2-1 1-1 1-1 1-2l-1-1v-1z" class="H"></path><path d="M284 209h2v3h-2l-1-1 1-2z" class="AG"></path><path d="M280 198c2-1 2-1 4-1v-2-1h0c2 1 2 2 3 5 1 0 2 1 3 2 1 0 3-1 3 0v1c0 1 0 1 1 2h-2c-1 1-1 3-3 3 0-1 0-2-1-2-1-1-2 0-2 0l-1-1v-1c-1 0-2 0-2 1h-1-1v-1h1c1-1 1-3 2-4l-1-1c-1 0-3 1-4 2v-1l1-1z" class="W"></path><path d="M292 204h-2c-1 0-1-1-2-1h-2l-2-1c1-2 0-3 1-4 1 0 1 0 2 1h0c1 0 2 1 3 2 1 0 3-1 3 0v1c0 1 0 1 1 2h-2z" class="P"></path><path d="M287 200l2 2h-1-1v-2z" class="B"></path><path d="M591 157h2l1 2h0v2h1v2h0c0 3 1 7 2 9 1 3 1 5 1 7 0 11-4 25-12 33-4 4-10 8-16 9 2-1 4-3 6-4 7-7 11-15 13-25v-4c0-2 1-3 1-5 0-3-1-6-1-8h0v-3c1 0 1 1 1 2h0v-1h1c0 3 1 5 2 8v-5c1-7 0-13-2-19z" class="h"></path><path d="M243 131s0-1 1-1c1-3 2-8 4-10 2-4 3-8 5-11l3-3-3 4c-1 2-2 4-3 5 0 1 0 2-1 3v1l-1 1v1h0c0 1-1 2-1 3h0c-1 2 0 3 0 4-2 10-3 20-1 30l2 8c0 1 1 3 1 3l4 12-1 1h-1l-1-1-1 1 1 1-1 1h0v-2h-1 0v1 2h-1 0v2h0v2c1 1 1 1 1 2v1h0l-8-22c0-2-1-3-1-5 0-1-1-2-1-3h0l1-1c0-3-1-7 1-9l-1-18c1 1 1 1 1 2h1v-2 1h0l2-4z" class="I"></path><path d="M239 134c1 1 1 1 1 2h1v-2 1 11 6h-1 0l-1-18z" class="G"></path><path d="M246 167v1c1 1 1 1 1 2v1c1 2 1 4 2 6v1c1-1 1-1 1-2v-2l-2-4 1-1 4 12-1 1h-1l-1-1-1 1c0-1-1-2-1-3 1-2-1-6-2-8v-4z" class="k"></path><path d="M241 153l1-2c1 0 1 1 1 2l3 14v4c1 2 3 6 2 8-4-8-5-17-7-26z" class="AA"></path><path d="M240 152h0v1h1c2 9 3 18 7 26 0 1 1 2 1 3l1 1-1 1h0v-2h-1 0v1 2h-1 0v2h0v2c1 1 1 1 1 2v1h0l-8-22c0-2-1-3-1-5 0-1-1-2-1-3h0l1-1c0-3-1-7 1-9z" class="X"></path><path d="M398 89h0 2 5l12 3c2 0 4 1 5 2l6 1 11 4h-1v1h-1s0 1-1 1v1c0 1 1 1 1 2s0 1-1 2c-2-1-3-2-5-2-1-1-3-1-4-2h-1l1 2v1l-1-1v1c-1-1-2-2-3-1v1l4 7c-1 0-2 1-2 2s1 3 2 4c1 3 2 5 2 8h0c1 1 0 2 0 3h0c1 1 1 2 1 3h0v1l1 1c0 1-1 2 0 2v3c-1 0 0-2 0-4-1 0-1-1-2-2 0-1 0-3-1-5 0-2-1-5-3-8 0-1-1-3-2-4-3-6-7-12-11-16-3-3-6-5-10-7-1-1-2-2-4-2 0 0 0-1-1-1l1-1h0z" class="d"></path><defs><linearGradient id="P" x1="417.333" y1="96.983" x2="406.098" y2="86.252" xlink:href="#B"><stop offset="0" stop-color="#ff908c"></stop><stop offset="1" stop-color="#ffb1ae"></stop></linearGradient></defs><path fill="url(#P)" d="M398 89h0 2 5l12 3c2 0 4 1 5 2-1 0-2 1-3 1v1l-1 1c-1 0-2 0-2 1-6-4-12-7-18-9h0z"></path><path d="M422 94l6 1 11 4h-1v1h-1s0 1-1 1v1c0 1 1 1 1 2s0 1-1 2c-2-1-3-2-5-2-1-1-3-1-4-2h-1l1 2v1l-1-1v1c-1-1-2-2-3-1v1l-5-6-2-1c0-1 1-1 2-1l1-1v-1c1 0 2-1 3-1z" class="U"></path><path d="M422 94l6 1-2 1-1 1h0c1 1 1 2 2 3l-2 2c-2 0-5-2-7-3l-2-1c0-1 1-1 2-1l1-1v-1c1 0 2-1 3-1z" class="AC"></path><path d="M314 574c1 0 1 0 1 1 1 3 2 7 4 10l1-1c0 1 1 1 1 1v2c1 1 1 2 1 2h1v1l1 1v1c1 1 1 3 2 4v1 1c1-2 0-3-1-5s-1-5-3-8h0 1s1 0 1 1c1 4 2 8 4 12 1 0 1 0 2 1h0l1 1c0 1 0 2 1 3 1 2 1 4 2 6 1 0 1-1 1-1v2h0 1l2-3v1h0l1 1 1-1c0-1 0-2 1-3 0 1-1 7-1 7v3h0 0 2v1c1-1 1-2 1-3s1-1 1-2 1-2 1-3h1c-3 11-8 20-10 31l-22-65z" class="T"></path><path d="M328 598c1 0 1 0 2 1h0l-1 1-1-2z" class="L"></path><path d="M330 599l1 1c0 1 0 2 1 3l-1 2c-1-2-2-3-2-5l1-1z" class="c"></path><path d="M332 617c0-2-1-4-1-7v1h1 0c0 1 0 1 1 2v4h-1z" class="L"></path><path d="M335 616v-1c1 0 1 1 1 1h1v3c0 1 0 2-1 3h-1v-2-4z" class="V"></path><path d="M332 609c1 2 1 4 2 5l1 2v4h0-1c-1-1-1-2-2-3h1v-4c-1-1-1-1-1-2v-2z" class="F"></path><path d="M332 603c1 2 1 4 2 6 1 0 1-1 1-1v2c0 2 0 3-1 4-1-1-1-3-2-5l-1-4 1-2z" class="G"></path><path d="M338 607v1c0 1 0 4-1 4v4h-1s0-1-1-1v1l-1-2c1-1 1-2 1-4h0 1l2-3z" class="C"></path><path d="M335 615v-1l2-2h0v4h-1s0-1-1-1z" class="R"></path><path d="M338 608l1 1 1-1c0-1 0-2 1-3 0 1-1 7-1 7-1 3-2 5-3 7v-3-4c1 0 1-3 1-4h0z" class="j"></path><defs><linearGradient id="Q" x1="167.147" y1="99.044" x2="167.507" y2="104.119" xlink:href="#B"><stop offset="0" stop-color="#f9dddf"></stop><stop offset="1" stop-color="#fdfeff"></stop></linearGradient></defs><path fill="url(#Q)" d="M218 98h1l-2 2h0v1c-4 0-8 1-12 1l-24 1h-2c-1 0-1 0-2 1-2 0-4 1-6 2v1c-1 1-3 2-3 3s0 2 1 2c-1 1-1 2-1 3h0c-1 1-1 1-1 2h-1c-1-1-2-1-3-1h-2c0-1 0-3 1-4-1 0-2 0-2-1l-2-1h0l-1 1v1h-2c0 1 0 2-1 2l-3-2v-1l-1-1h-2c-1 0-1-1-2-1h-2l-8-3h2 2 0v-1l-1-1v1c-1 1-3 1-4 0h3v-1c-1 0-2 0-3-1h-1-2v1c-1 1-4 1-6 2l-7 2c-1 0-1 0-1-1 4-2 9-4 13-5 8-2 15-2 23-2h32 19c4 0 9-1 13-2z"></path><path d="M145 103l15-1c2 0 5 0 7 1h-1-14-1-1l1 1h-7l-5-1h6 0z" class="k"></path><path d="M151 104l-1-1h1 1 14c0 2-3 4-5 5h0c-1 0-2 1-3 2h0l-1-1h0c-1-1-1-1-2-1h-2 0v-1c-1-1-1-2-1-3h-1z" class="q"></path><path d="M152 104h1v1c0 1 0 1 1 1s2 0 2 1h2 0c0 1 0 1-1 2h0c-1-1-1-1-2-1h-2 0v-1c-1-1-1-2-1-3z" class="AA"></path><path d="M152 103h14c0 2-3 4-5 5h0c-1-1-2-2-2-3h3v-1c-2 0-8 0-10-1z" class="u"></path><path d="M134 103c4 0 7-1 11 0h0-6l5 1h7 1c0 1 0 2 1 3v1h0 2c1 0 1 0 2 1h0l1 1-1 1v1h-2c0 1 0 2-1 2l-3-2v-1l-1-1h-2c-1 0-1-1-2-1h-2l-8-3h2 2 0v-1l-1-1v1c-1 1-3 1-4 0h3v-1c-1 0-2 0-3-1h-1z" class="AA"></path><path d="M151 111c1 0 2 1 3 1h1c0 1 0 2-1 2l-3-2v-1z" class="q"></path><path d="M155 108c1 0 1 0 2 1h0l1 1-1 1v1c-1-1-2-1-2-2h-1l1-1v-1z" class="k"></path><path d="M144 104h7 1c0 1 0 2 1 3v1h0 2v1l-1 1h1-3c-2-1-2-1-4-1 0-1-1-1-1-1-1-1 0-1 0-2l-2-2h-1z" class="AC"></path><path d="M145 104c2 0 3 0 4 2h1c0 1-1 1-1 2l-1 1c0-1-1-1-1-1-1-1 0-1 0-2l-2-2z" class="q"></path><path d="M167 103h14-2c-1 0-1 0-2 1-2 0-4 1-6 2v1c-1 1-3 2-3 3s0 2 1 2c-1 1-1 2-1 3h0c-1 1-1 1-1 2h-1c-1-1-2-1-3-1h-2c0-1 0-3 1-4-1 0-2 0-2-1l-2-1c1-1 2-2 3-2h0c2-1 5-3 5-5h1z" class="X"></path><path d="M168 110c0 1 0 2 1 2-1 1-1 2-1 3h0c-1 1-1 1-1 2h-1 0c0-3 1-4 2-7z" class="E"></path><path d="M162 112l1-2h1c-1 1-1 2-1 4v2h-2c0-1 0-3 1-4z" class="u"></path><path d="M167 103h14-2c-1 0-1 0-2 1-2 0-4 1-6 2v1c-1-1-2-1-3-1-2 0-3 2-5 3h1v1h0-1l-1 2c-1 0-2 0-2-1l-2-1c1-1 2-2 3-2h0c2-1 5-3 5-5h1z" class="J"></path><path d="M163 109h1v1h0-1l-1 2c-1 0-2 0-2-1l3-2zm84-7v1c2 0 1-1 3-1 0-1 1-1 1-1-1 2-3 4-3 6h2l-4 9c-1 3-2 6-2 9v1l-1 5-2 4h0v-1 2h-1c0-1 0-1-1-2l1 18c-2 2-1 6-1 9l-1 1-1-3v-1l-1 1c1 1 1 2 1 3h-1v-3-1-1c0-2-2-4-2-7-1-2-2-12-3-13v-2h1v-5c0-1 1-3 1-4l-1-1c-1-1 0-2 0-2v-4-1l1-4-4 4-1-1 2-2 2-1c2-1 2-1 3-3v-1c0-1-1 0-2-1l4-2 7-4c1-1 2-1 3-1z" class="h"></path><path d="M232 118c1 0 0 0 1 1h0c1-1 2-1 3-2h1l-2 5v1h-3v-4-1z" class="R"></path><path d="M232 123v-4 2c1 0 1 0 1-1l2 2v1h-3z" class="Q"></path><path d="M232 135v-5c0-1 1-3 1-4l-1-1c-1-1 0-2 0-2h3c-1 2-1 11-2 13l-1-1z" class="D"></path><path d="M235 111v1h0c2 0 4-1 5-2l-3 7h-1c-1 1-2 1-3 2h0c-1-1 0-1-1-1l1-4-4 4-1-1 2-2 2-1c2-1 2-1 3-3z" class="C"></path><path d="M247 102v1c2 0 1-1 3-1 0-1 1-1 1-1-1 2-3 4-3 6l-5 8c0-1 1-1 1-2h-1v-1-1h0v-2-2l4-5z" class="k"></path><path d="M244 103c1-1 2-1 3-1l-4 5-3 3c-1 1-3 2-5 2h0v-1-1c0-1-1 0-2-1l4-2 7-4z" class="p"></path><path d="M248 107h2l-4 9c-1 3-2 6-2 9v1l-1 5-2 4h0v-1 2h-1c0-1 0-1-1-2 0-7 2-13 4-19l5-8z" class="b"></path><path d="M332 132l4-111c0 1 1 46 1 52l1 47 2 21c0 2-1 5-2 7l1 1-2 5 1 4h0l1 1-1 7v5l-1-1v-2c-1 0-1 1-1 1l-1-2v3h-1v-2h0c-2-6 0-12-2-18h0c0-2 0-3-1-5l1-13z" class="a"></path><defs><linearGradient id="R" x1="331.944" y1="165.593" x2="336.703" y2="150.996" xlink:href="#B"><stop offset="0" stop-color="#aba1a1"></stop><stop offset="1" stop-color="#cec9c9"></stop></linearGradient></defs><path fill="url(#R)" d="M332 132c0 1 1 4 0 5v5 4h1 0v3-2c-1-1 0-2-1-3 0-2 0-2 1-4v-2-2c1 1 1 1 1 2 0 2 0 4 1 6l1 4v-9c0-1-1-3 0-3v-3-1c1 1 1 2 1 3s0 2 1 3c0 3-1 7 0 10h0l1 1-2 5 1 4h0l1 1-1 7v5l-1-1v-2c-1 0-1 1-1 1l-1-2v3h-1v-2h0c-2-6 0-12-2-18h0c0-2 0-3-1-5l1-13z"></path><defs><linearGradient id="S" x1="338.915" y1="163.39" x2="334.307" y2="160.837" xlink:href="#B"><stop offset="0" stop-color="#5e5a59"></stop><stop offset="1" stop-color="#767072"></stop></linearGradient></defs><path fill="url(#S)" d="M335 167c0-2 0-5 1-8 0-2 0-3 1-5l1 4h0l1 1-1 7v5l-1-1v-2c-1 0-1 1-1 1l-1-2z"></path><path d="M376 330l1 1c-1 2-2 4-2 6v-1s1 0 1-1l1 1v-1c0-1 0-1 1-1 0 3-3 5-2 8h1 1 0v1 3s0-1 1-1c0-1 1-1 1-2l-6 15-6 13c-1 3-3 6-3 9h0c0 5-4 9-5 14h-1c-3 3-4 6-6 9-1 2-2 3-3 4v1l-1 1c-1-1 0-2 1-3l-1-1-2 4-1-1c3-3 5-7 7-11 1-2 2-5 3-7 0-2 0-4 1-6s2-3 2-5c0-1-1-1-2-2 1-1 1-3 2-5 0-1 0-3 1-5 1-7 4-15 7-22 1-4 3-8 4-12h0c0 2-1 5-1 7l1-1v-1l1-1v2l-1 1v1 1c1-1 1-2 1-3 1-1 1-2 2-4l2-5z" class="h"></path><path d="M362 371c1 1 0 1 2 2l-2 4s0 1-1 1l-2 7c-1 1-1 2-2 2l-1 3c0-2 0-4 1-6s2-3 2-5c0-1-1-1-2-2 1-1 1-3 2-5v1h1l2-2z" class="O"></path><path d="M376 330l1 1c-1 2-2 4-2 6v-1s1 0 1-1l1 1v-1c0-1 0-1 1-1 0 3-3 5-2 8h1c0 1 0 2-1 2v2l-1 2-1 1v1c-1 1-1 2-2 4v2l-1 1-1 3-2 3v1l-1 2-3 7c-2-1-1-1-2-2l-2 2h-1v-1c0-1 0-3 1-5 1-7 4-15 7-22 1-4 3-8 4-12h0c0 2-1 5-1 7l1-1v-1l1-1v2l-1 1v1 1c1-1 1-2 1-3 1-1 1-2 2-4l2-5z" class="J"></path><path d="M377 336v-1c0-1 0-1 1-1 0 3-3 5-2 8h1c0 1 0 2-1 2v2l-1 2-1 1v1c0-1-1-3 0-4 0-1 0-2 1-3 0-1 0-2 1-4l1-3z" class="M"></path><path d="M366 356l1 1-1 1c-1 3-1 6-2 9-1 1-1 3-2 4l-2 2c0-3 1-5 2-8l4-9z" class="b"></path><path d="M366 356c0-1 1-4 2-5v-1l1-1c0-2 1-5 3-7h0v1c-1 1-1 3 0 3h0v-3h1c0 2-1 3 0 4v1c-1 0-1 0-1 1v2c-1 0-1 1-1 2 1 1 1 2 1 3l-1 1-1 3-2 3v1l-1 2-3 7c-2-1-1-1-2-2 1-1 1-3 2-4 1-3 1-6 2-9l1-1-1-1z" class="L"></path><path d="M421 187c1-1 1-3 2-4h1v-1c0-2 1-4 2-6v3l-1 4 1 1v2h0l-2 4v2c0 2 0 2 1 4-1 1-3 4-2 5v1 1l-3 9-4 11-2 1-5 14-14 38-12 35-5 14c-1 1-2 3-2 5l-2 5c-1 2-1 3-2 4 0 1 0 2-1 3v-1-1l1-1v-2l-1 1v1l-1 1c0-2 1-5 1-7h0l11-30 3-9 9-25 3-9 6-19 1-3 11-32c2-6 3-13 6-19z" class="a"></path><path d="M394 269l3-9v3-1h1v1l-4 11v-2-3zm-9 25l3-1-7 21c0-1-1-2 0-3 0-1 0-1 1-1 0-2 0-3 1-4l1-3v-1l-1 2h-1v-1l3-9z" class="U"></path><path d="M394 269v3 2l-6 19-3 1 9-25z" class="o"></path><path d="M382 303v1h1l1-2v1l-1 3c-1 1-1 2-1 4-1 0-1 0-1 1-1 1 0 2 0 3-1 3-2 5-3 7l-3 8c0 1 0 2-1 3v3c-1 2-1 3-2 4 0 1 0 2-1 3v-1-1l1-1v-2l-1 1v1l-1 1c0-2 1-5 1-7h0l11-30z" class="I"></path><path d="M415 206l1 1-1 1v2l-1 1v2c-1 1-1 2-2 4 0 1 0 2-1 4h1v-1h0v-2h1v-1-1c1-2 2-5 2-7l1-1v2l-18 53h0v-1h-1v1-3l6-19 1-3 11-32z" class="AF"></path><path d="M421 187c1-1 1-3 2-4h1v-1c0-2 1-4 2-6v3l-1 4 1 1v2h0l-2 4v2c0 2 0 2 1 4-1 1-3 4-2 5v1 1l-3 9-4 11-2 1 4-14v-1h0c0-1 0-2 1-3h0v-2-1c-1 2-2 5-2 7h-1v-2l-1 1c0 2-1 5-2 7v1 1h-1v2h0v1h-1c1-2 1-3 1-4 1-2 1-3 2-4v-2l1-1v-2l1-1-1-1c2-6 3-13 6-19z" class="k"></path><path fill="#eac4cb" d="M425 183l1 1v2h0l-2 4v-2h-1l2-5z"></path><path d="M423 188h1v2 2l-2 6c0-1 0-1-1-2v1h-1l3-9z" class="AE"></path><path d="M420 197h1v-1c1 1 1 1 1 2-1 2-3 11-4 12v-1h0c0-1 0-2 1-3h0v-2-1c-1 2-2 5-2 7h-1v-2l4-11z" class="g"></path><path d="M424 192c0 2 0 2 1 4-1 1-3 4-2 5v1 1l-3 9-4 11-2 1 4-14c1-1 3-10 4-12l2-6z" class="u"></path><defs><linearGradient id="T" x1="126.57" y1="113.692" x2="127.347" y2="125.794" xlink:href="#B"><stop offset="0" stop-color="#1a1919"></stop><stop offset="1" stop-color="#2f1d1d"></stop></linearGradient></defs><path fill="url(#T)" d="M119 108l7-2c2-1 5-1 6-2v-1h2 1c1 1 2 1 3 1v1h-3c1 1 3 1 4 0v-1l1 1v1h0-2-2l8 3h2c1 0 1 1 2 1h2l1 1v1l3 2c1 2 1 2 0 4v1c1 0 1 1 1 1v2h-1v2l2 4c0 3 1 4 1 7l-1-1c0-1-1-1-2-1h1v-1l-1-2c-1-2-1-4-2-6-2-2-4-2-7-3l-1-1h-2c-1-1-3-1-4-1h-6c-10-1-22 4-30 10-1 0-3 2-3 2l-3 2-1 1-2-2c-2 3-4 5-6 8l-2 2c-1-1 0-2 1-3l-1-1-2 3h-1c3-5 6-10 9-14 1-2 2-4 3-5l4-4 2 2c3-4 6-7 10-10v1c1-1 3-2 4-3l4-1c0 1 0 1 1 1z"></path><path d="M132 119l4-1c3-1 8 0 11 1s5 3 6 5h1l2 4c0 3 1 4 1 7l-1-1c0-1-1-1-2-1h1v-1l-1-2c-1-2-1-4-2-6-2-2-4-2-7-3l-1-1h-2c-1-1-3-1-4-1h-6z" class="K"></path><path d="M116 116c0-1-1-1-1-1 0-1 0-1 1-1 0-1 2-1 2-2 3-1 7-3 10-3h6v2s-1 1-2 1l-1 1h1l-1 1h-3l-1-1-1 2h0l-2-2v1 1l-1 1-2-2c-1 1 0 1-1 2 0 0-1 0-1-1h-1v1 1l-2-1z" class="a"></path><path d="M138 109h3 1l2 1 2-1c1 0 1 1 2 1h2l1 1v1l3 2c1 2 1 2 0 4v1c1 0 1 1 1 1v2h-1c-2-4-5-6-9-8-3-2-7-2-10-1-2 0-2 1-3 3h0c-1-1 0-1 0-3h-1l1-1c1 0 2-1 2-1v-2h4z" class="AE"></path><path d="M148 112h3l3 2c1 2 1 2 0 4v1-2c-1-1-4-4-6-5z" class="y"></path><path d="M138 109h3 1l2 1 2-1c1 0 1 1 2 1h2l1 1v1h-3c-2-2-7-3-10-3z" class="AD"></path><path d="M119 108l7-2c2-1 5-1 6-2v-1h2 1c1 1 2 1 3 1v1h-3c1 1 3 1 4 0v-1l1 1v1h0-2-2l8 3h2l-2 1-2-1h-1-3-4-6c-3 0-7 2-10 3 0 1-2 1-2 2-1 0-1 0-1 1 0 0 1 0 1 1v1c-1 1-1 1-2 1h-1-1v2c-2 0-3-2-4 1v1h-3l-1 1c-1 1 1 2-1 2l-1-1-1 1v3h0l1 1c-1 0-3 2-3 2l-3 2-1 1-2-2c-2 3-4 5-6 8l-2 2c-1-1 0-2 1-3l-1-1-2 3h-1c3-5 6-10 9-14 1-2 2-4 3-5l4-4 2 2c3-4 6-7 10-10v1c1-1 3-2 4-3l4-1c0 1 0 1 1 1z" class="x"></path><path d="M114 108l4-1c0 1 0 1 1 1-1 1-3 2-4 1l-1-1z" class="AE"></path><path d="M101 125v3s-1 0-1 1c-2 1-3 3-5 2 1-2 4-3 6-6z" class="R"></path><path d="M101 128h0l1 1c-1 0-3 2-3 2l-3 2-1 1-2-2h1 0l1-1c2 1 3-1 5-2 0-1 1-1 1-1z" class="Q"></path><path d="M94 132h0c1 0 2 0 2 1l-1 1-2-2h1z" class="d"></path><path d="M100 120c3-4 6-7 10-10v1c-5 4-10 10-14 15v-1l-3 3h-1l1-1 1-1-1-1-1 2h-1c1-2 2-4 3-5l4-4 2 2z" class="AD"></path><path d="M94 122l4-4 2 2c-1 0-2 1-2 2-1 0-2 1-3 0h-1z" class="y"></path><path d="M91 127h1l1-2 1 1-1 1-1 1h1l3-3v1c-1 1-2 3-2 5v1h-1c-2 3-4 5-6 8l-2 2c-1-1 0-2 1-3l-1-1-2 3h-1c3-5 6-10 9-14z" class="q"></path><path d="M99 131c0 2-1 3-3 4-1 2-2 3-3 4-2 2-4 4-5 6l-2 4h0c3-4 6-7 9-10h1l-1 1c1 0 2-1 3-1s2 0 3-1h3 2c1-1 2 0 2 0h2 4l1 1-5 2h-2c-2 1-4 2-5 3h0c0 1-2 2-2 2-5 3-8 6-11 10-8 8-10 20-9 31 1 12 6 22 14 29 2 2 5 3 7 5-4-1-8-3-11-5-9-6-16-18-18-29-3-12 0-24 5-36 1-4 2-7 4-10h1l2-3 1 1c-1 1-2 2-1 3l2-2c2-3 4-5 6-8l2 2 1-1 3-2z" class="w"></path><path d="M89 148c2 0 3-1 4-2s1-1 3-1c-5 4-9 7-12 12l-1-1c0-1 5-6 6-8z" class="F"></path><path d="M101 138h3 2c1-1 2 0 2 0h2 4l1 1-5 2h-2c-2 0-3 0-5 1h-1l-6 3c-2 0-2 0-3 1s-2 2-4 2l2-2 1-1v-1c1-2 2-3 3-4 1 0 2-1 3-1s2 0 3-1z" class="J"></path><path d="M110 138h4l1 1-5 2h-2c-2 0-3 0-5 1h-1c3-2 5-3 8-4z" class="I"></path><path d="M99 131c0 2-1 3-3 4-1 2-2 3-3 4-2 2-4 4-5 6l-2 4h0c3-4 6-7 9-10h1l-1 1c-1 1-2 2-3 4v1l-1 1-2 2c-1 2-6 7-6 8l1 1v1h-1 0l-1-1v-1-2-1c1-2 2-5 4-6l1-3h0l-1 1c0 1-1 1-2 2-1 0-3 6-4 7 0 1 0 2-1 3 0-3 2-5 2-7 1 0 1-1 1-1 0-1 0-1 1-2h0v-1c1 0 1-1 2-2l2-4c2-3 4-5 6-8l2 2 1-1 3-2z" class="X"></path><path d="M87 140c2-3 4-5 6-8l2 2-7 7-3 3 2-4z" class="C"></path><path d="M226 312v1l10 25c1 2 2 5 2 8l10 28 7 22 5 14 8 22 7 17 4 13 17 49 10 28c1 4 3 8 4 13l3 9c0 1 1 2 1 3l2 6c0 1 1 2 1 2 0 1 1 2 1 3v1l2 6c1 1 2 3 2 4l-1-1s-1 0-1-1l-1 1c-2-3-3-7-4-10 0-1 0-1-1-1-2-4-3-9-4-13l-14-41-35-100-21-59-6-19c-1-2-1-5-2-8s-2-5-3-8c-1-1-1-2-2-3-1-4-2-7-1-11z" class="R"></path><defs><linearGradient id="U" x1="373.381" y1="286.476" x2="303.796" y2="348.917" xlink:href="#B"><stop offset="0" stop-color="#514c4b"></stop><stop offset="1" stop-color="#767070"></stop></linearGradient></defs><path fill="url(#U)" d="M346 267h3s0-1 1-1c0-1 0-2 1-3v-1-1-1h1c0-1 1-2 0-3l1-1c0-1 1-2 1-2 0-1 1-2 1-2-1 5-4 10-5 16 1-1 2-4 2-5h1l-1 1h0v2c-1 2-2 3-2 5h1l-9 45-2 19s0 4-1 4l-1 14v2h-1v2 4 1c-1 1 0 3 0 5-1 1-1 3-1 5v10c0-8 0-15-1-23 0-2 0-4-1-6h0l-1-14-3-26c-1-5-2-11-2-16-1-8-3-15-5-23 0-1-1-1 0-2v-1h-1 0v-1-1l1-1 1 1v-1c1 1 1 2 2 3v1 1 1l1 1v1 1 1l1 1h1 0 0c1 2 0 3 1 5v2c1-1 1-2 1-3s-1-3-1-4l1 2 1 1v2c0 1 1 1 1 3v1c0 1 1 2 1 3 1 1 1 2 1 4s1 4 1 7c0-1 0-2 1-3v2-17-3l1 1h0c1-3 2-5 3-7 1-1 5-7 5-8z"></path><path d="M339 339h0c0-1 0-4 1-5 0-1-1-2 0-3v-4c0-1-1-3 0-4h1v-1c0-1 0-2 1-3 0-1-1-3 0-4v-2 3l-2 19s0 4-1 4zm-9-26v-3 2 1l1 1v2l1 1 1-1v-4-2c1 3 0 7 1 11 1 1 0 4 0 5h0c-1 2-2 6-1 8h0v2 3l-3-26z" class="l"></path><path d="M536 130h2v1h-2 7c1 0 3 0 5 1 4 0 9 1 12 3h1c1 1 2 2 3 2h-3c-6-1-13-3-19-1-2 0-3 0-5 1-3 0-5 1-8 3-1 1-2 1-3 3l-11 9c-1 0-2 2-3 2 0 1-1 2-1 3s-2 2-3 3-2 2-2 4h0c0 3-3 7-5 10l-5 12-3 6c0 1-1 2-1 3l-18 47v-2h-1 0 0v-2h0l-1-1 6-18c1-4 2-8 4-12 2-2 6-15 6-17h-1c-1 1-1 2-2 3v-3h0v-2c2-3 3-6 4-9l6-12 6-9c2-2 3-4 5-6h0v-1s0-1 1-1v-1c0 1-1 1-2 2h-1v-1l1-3c-1 0-1 0-2-1l1-1h0c0-1 0-1-1-2v-1c1-1 4-3 5-4l1-1c2-1 4-2 5-4h0c1 0 1 0 2 1h1l1 1h1l2-3v3c1 0 2-1 3-2 1 0 3-1 4-1l1-1c1 0 2-1 3-1h4z" class="V"></path><path d="M521 143c3-1 5-3 8-3-1 1-2 1-3 3l-11 9c-1 0-2 2-3 2-3 1-4 2-6 4-2 1-3 2-4 4l-7 11h0 0v-1c1-1 1-1 1-2 0 0 0-1 1-1 0-1 1-2 1-2 1-1 1-2 2-2v-1l1-2 2-3c1-1 1-1 1-2l2-2c1-1 2-1 2-1h2c0-1 1 0 1-1 2 0 3-2 4-3 0 0 1-1 1-2h1v-2l3-3h0 1zm-34 47c0-1 0-2 1-3v-1l1 1c1-1 1-1 1-2l1-1h1v-1h0c1 3-2 6-2 9-1 0-1 0-1 1v-2h0c1-2 1-4 2-6h0c-1 1-2 4-2 6-1 2-1 3-2 5l-4 10c-2 4-3 8-4 13l1-1v-1c0 1 0 1 1 2h-1v3h0v-2c1 0 1 0 1-1v-1l1-1c0-1 0-2 1-3l1-1v-1l1-3c1-1 0-1 1-1v-2l1-1v-1c1-1 1-2 1-3l1-1h0v-1h0l1-1v-1-1l1-1h1l-18 47v-2h-1 0 0v-2h0l-1-1 6-18c1-4 2-8 4-12 2-2 6-15 6-17h-1z" class="D"></path><path d="M478 219h1l-1 6c0 2-1 4-2 5-1 3-2 7-3 10v-2h0l-1-1 6-18z" class="C"></path><path d="M536 130h2v1h-2 7c1 0 3 0 5 1 4 0 9 1 12 3h1c1 1 2 2 3 2h-3c-6-1-13-3-19-1-2 0-3 0-5 1-3 0-5 1-8 3-3 0-5 2-8 3l1-1h0c1 0 2-1 3-2h1l1-1h1 1c2 0 3-1 4-2h1c1-1 2-1 2-1h3c0-1 1-1 2-1h1 1v-1h-2v-1c-2 0-6 1-8 2-3 1-7 3-10 5-2 2-5 3-7 4l-3 3-1-1c-1 1-5 6-6 6v-1s0-1 1-1v-1c0 1-1 1-2 2h-1v-1l1-3c-1 0-1 0-2-1l1-1h0c0-1 0-1-1-2v-1c1-1 4-3 5-4l1-1c2-1 4-2 5-4h0c1 0 1 0 2 1h1l1 1h1l2-3v3c1 0 2-1 3-2 1 0 3-1 4-1l1-1c1 0 2-1 3-1h4z" class="p"></path><path d="M517 141c2-2 5-3 8-4h1c-5 2-11 6-14 9-1 1-5 6-6 6v-1s0-1 1-1v-1c0 1-1 1-2 2h-1v-1c4-3 8-6 13-9z" class="Z"></path><path d="M536 130h2v1h-2 7c1 0 3 0 5 1-6 0-11 1-17 3-1 0-3 1-4 2h-1-1c-3 1-6 2-8 4h-1-2c0 1 0 1-1 0 2-1 4-2 6-4l2-2c1 0 2-1 3-2 1 0 3-1 4-1l1-1c1 0 2-1 3-1h4z" class="T"></path><path d="M529 131l1 1-4 2c-1 0-1 0-2-1 1 0 3-1 4-1l1-1z" class="U"></path><path d="M536 130h2v1h-2c-2 0-4 1-6 1l-1-1c1 0 2-1 3-1h4z" class="I"></path><path d="M521 135c1 0 2-1 3-2 1 1 1 1 2 1l-10 7h-2c0 1 0 1-1 0 2-1 4-2 6-4l2-2z" class="u"></path><path d="M517 134l1 1h1l2-3v3l-2 2c-2 2-4 3-6 4 1 1 1 1 1 0h2 1c-5 3-9 6-13 9l1-3c-1 0-1 0-2-1l1-1h0c0-1 0-1-1-2v-1c1-1 4-3 5-4l1-1c2-1 4-2 5-4h0c1 0 1 0 2 1h1z" class="X"></path><path d="M517 134l1 1h1l2-3v3l-2 2c-2 0-4 1-5 2h-1l-1-1 4-4h1z" class="V"></path><path d="M504 145h1c1-3 5-5 7-7l1 1h1c-1 2-2 3-4 4s-4 3-5 4c-1 0-1 0-2-1l1-1z" class="L"></path><path d="M514 139c1-1 3-2 5-2-2 2-4 3-6 4 1 1 1 1 1 0h2 1c-5 3-9 6-13 9l1-3c1-1 3-3 5-4s3-2 4-4z" class="U"></path><path d="M513 120c1 1 1 2 2 3v1c-1 2 0 4 0 5v3h-1v-1c0 1 0 1-1 1-1 2-3 3-4 4v1l-1 1c-1 1-4 3-5 4v1c1 1 1 1 1 2h0l-1 1c1 1 1 1 2 1l-1 3v1h1c1-1 2-1 2-2v1c-1 0-1 1-1 1v1h0c-2 2-3 4-5 6l-6 9-6 12c-1 3-2 6-4 9v2h-1 0c-1-1 0-2 0-3 0-2 0-3-1-5 1-2 0-5-1-8-2-2-4-5-4-8-1-4-1-7-1-11l1-3s0-1 1-2 2-3 3-4l1 1c2-4 6-8 9-12h-2v-2h0v-1c-1-1-2-1-3-2l1-1h3c1-1 3-2 4-4l-1-1h0 1 2l1-1 1 1 2-3c2 0 3 0 4-1v1 1h1c1-1 2-1 3-1 2-1 3 0 4-1z" class="X"></path><path d="M492 155s1 0 1 1l-6 6-2 2h-1 0c1-2 4-6 5-7 1 0 2-1 3-2z" class="h"></path><path d="M491 141c1-1 2-2 3-2s4 0 4 1l2 1v1c0 1 3 2 4 3h0l-1 1c-2-2-5-3-7-4-2 1-3 1-4 2h-1l1-1v-1l-1-1z" class="G"></path><path d="M491 141l1 1v1l-1 1h1c-4 5-6 10-9 15 0-2 1-4 1-6 1-1 2-3 2-4l-2 2c1-4 4-7 7-10z" class="b"></path><path d="M479 150c1-1 2-3 3-4l1 1-1 2c0 2 0 3 1 4-1 3-1 5-1 7l-1 1v4l1 2c-1 0-1-1-2-1h0c0 1 1 2 1 3l-3-3c-1-4-1-7-1-11l1-3s0-1 1-2z" class="o"></path><path d="M481 161c-1-4 0-8 1-12 0 2 0 3 1 4-1 3-1 5-1 7l-1 1z" class="E"></path><path d="M478 152c1 2 0 4 0 6 1 2 2 5 3 7l1 2c-1 0-1-1-2-1h0c0 1 1 2 1 3l-3-3c-1-4-1-7-1-11l1-3z" class="g"></path><path d="M497 132c1 1 2 1 3 2h0l1 1h0 1l-1 1h0c0 2-2 3-3 4 0-1-3-1-4-1s-2 1-3 2c-3 3-6 6-7 10l-1 2c-1-1-1-2-1-4l1-2c2-4 6-8 9-12 2-1 3-2 5-3z" class="H"></path><path d="M494 139l7-3h0c0 2-2 3-3 4 0-1-3-1-4-1z" class="C"></path><path d="M504 151h1c1-1 2-1 2-2v1c-1 0-1 1-1 1v1h0c-2 2-3 4-5 6l-6 9-6 12c-1 3-2 6-4 9v2h-1 0c-1-1 0-2 0-3 0-2 0-3-1-5 1-2 0-5-1-8-2-2-4-5-4-8l3 3c0-1-1-2-1-3h0c1 0 1 1 2 1l2 2 4 5c4-7 8-14 13-20l3-3z" class="a"></path><path d="M478 166l3 3v1c1 2 3 4 4 7 1 2 0 4 0 6s-1 4 0 5v2h-1 0c-1-1 0-2 0-3 0-2 0-3-1-5 1-2 0-5-1-8-2-2-4-5-4-8z" class="F"></path><path d="M513 120c1 1 1 2 2 3v1c-1 2 0 4 0 5v3h-1v-1c0 1 0 1-1 1-1 2-3 3-4 4v1l-1 1c-1 1-4 3-5 4v1c1 1 1 1 1 2-1-1-4-2-4-3v-1l-2-1c1-1 3-2 3-4h0l1-1h-1 0l-1-1h0c-1-1-2-1-3-2-2 1-3 2-5 3h-2v-2h0v-1c-1-1-2-1-3-2l1-1h3c1-1 3-2 4-4l-1-1h0 1 2l1-1 1 1 2-3c2 0 3 0 4-1v1 1h1c1-1 2-1 3-1 2-1 3 0 4-1z" class="B"></path><path d="M513 120c1 1 1 2 2 3v1c-1 0-2-2-3-2-2 0-6 2-8 3v1h-1v-2s-1 1-2 1v-1c0-1 1-1 1-1l1-1h2 1c1-1 2-1 3-1 2-1 3 0 4-1z" class="I"></path><path d="M503 135l1-3c0-1 1-2 2-3 2-1 4-2 6-1 1 1 1 2 2 3 0 1 0 1-1 1-1 2-3 3-4 4v1l-1 1c-1 1-4 3-5 4v1c1 1 1 1 1 2-1-1-4-2-4-3v-1l-2-1c1-1 3-2 3-4h0l1-1h1z" class="F"></path><path d="M502 135h1l-3 6-2-1c1-1 3-2 3-4h0l1-1z" class="R"></path><path d="M505 120v1 1h-2l-1 1s-1 0-1 1v1c1 0 2-1 2-1v2c-1 2-2 5-3 8h0c-1-1-2-1-3-2-2 1-3 2-5 3h-2v-2h0v-1c-1-1-2-1-3-2l1-1h3c1-1 3-2 4-4l-1-1h0 1 2l1-1 1 1 2-3c2 0 3 0 4-1z" class="h"></path><path d="M492 130c1 1 2 2 3 2s1 0 2-1v1h0c-2 1-3 2-5 3h-2v-2h0v-1c1 0 2-1 2-2z" class="u"></path><path d="M495 124h2l1-1 1 1c-2 2-4 4-7 6 0 1-1 2-2 2-1-1-2-1-3-2l1-1h3c1-1 3-2 4-4l-1-1h0 1z" class="D"></path><path d="M159 228h6c1 1 1 2 1 3h1v-2c-3-2-5-3-7-5 3 0 6 1 9 1 3-1 7-2 10-3 0 1 1 2 1 3 1 1 1 1 1 2v1c1 1 4 4 4 5l-2 3c0 1 0 0 1 1 1 0 2 0 3 1v1l1 1c2-1 5-3 7-4l1 1c2 6 3 13 5 19 0 2 1 5 1 6v1c-4 1-9 1-13 1h-6c-2-1-4-1-6-1-3 1-6 1-9 2-3 0-5 1-7 1-3 1-8 3-9 5v1l-1 1c-3 1-6 4-9 5 1-2 3-4 5-6 4-4 9-9 12-14l2-2v-3c-1-2 1-7 1-10 0-2-1-5-1-7l1-1c0-2-1-4-3-6v-1z" class="E"></path><path d="M166 226h1c2 1 3 1 4 2 2 1 6 1 7 4h-1c-2 0-3-2-4-2-3-1-5-2-7-4zm18 11c1 0 2 0 3 1v1l1 1-5 2v-1s1-1 1-2c-1-1-2 0-2-1l2-1z" class="B"></path><path d="M182 244h0c-1 2-4 3-5 5-1 0-1 1-1 2l-3 2h-1c-1 0-2-1-4-1l14-8z" class="m"></path><path d="M164 235l1 1c1-1 0-3 2-4l1 1-1 2c0 1 0 2-1 3-2 2-1 4-2 6 1 0 1-1 2-2 0 0 0-1 1-2 1-3 3-4 5-6-1 3-3 5-4 8l-1 1h-1l-3 5c0-2 1-5 1-7 0-1 0-2-1-3 0-1 0-2 1-3z" class="f"></path><path d="M168 252c2 0 3 1 4 1-4 3-8 6-11 9-3 2-5 4-8 6 2-3 5-5 7-8 3-3 5-6 8-8z" class="v"></path><defs><linearGradient id="V" x1="158.88" y1="235.134" x2="165.517" y2="246.774" xlink:href="#B"><stop offset="0" stop-color="#484444"></stop><stop offset="1" stop-color="#635e5f"></stop></linearGradient></defs><path fill="url(#V)" d="M159 229c1 0 1 0 2 1s2 3 3 5c-1 1-1 2-1 3 1 1 1 2 1 3 0 2-1 5-1 7l3-5h1c-1 3-3 6-4 9 2-3 5-7 8-9-1 2-2 3-3 5-2 3-4 5-7 8v-3c-1-2 1-7 1-10 0-2-1-5-1-7l1-1c0-2-1-4-3-6z"></path><defs><linearGradient id="W" x1="200.632" y1="248.021" x2="165.989" y2="267.273" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#302e2e"></stop></linearGradient></defs><path fill="url(#W)" d="M188 240c2-1 5-3 7-4l1 1c2 6 3 13 5 19 0 2 1 5 1 6v1c-4 1-9 1-13 1h-6c-2-1-4-1-6-1-3 1-6 1-9 2-3 0-5 1-7 1l3-3 1-1c1 0 1 0 2-1h0l1 1c2-2 4-3 6-4 1-1 3-2 3-2v-1l1-1c-1 0 0 0-1-1 2 0 4-2 6-3 3-1 8-3 9-5l-9 4c-1 0-2 1-2 1-2 0-3 1-4 2-1 0-2 1-3 1h-1l3-2c0-1 0-2 1-2 1-2 4-3 5-5h0v-1l1-1 5-2z"></path><path d="M187 263h4s-1 0-2 1h-6c-2-1-4-1-6-1 3-1 7 0 10 0z" class="t"></path><path d="M187 263c1-1 3-1 5-1h10v1c-4 1-9 1-13 1 1-1 2-1 2-1h-4z" class="Y"></path><path d="M178 254l4-2c3-1 8-3 11-3-3 2-6 3-10 4-1 1-2 1-3 2h-1l-2 1v-1l1-1z" class="B"></path><defs><linearGradient id="X" x1="191.874" y1="238.01" x2="177.752" y2="248.203" xlink:href="#B"><stop offset="0" stop-color="#433f3f"></stop><stop offset="1" stop-color="#686362"></stop></linearGradient></defs><path fill="url(#X)" d="M188 240c2-1 5-3 7-4l1 1-20 14c0-1 0-2 1-2 1-2 4-3 5-5h0v-1l1-1 5-2z"></path><defs><linearGradient id="Y" x1="172.941" y1="104.128" x2="230.846" y2="110.422" xlink:href="#B"><stop offset="0" stop-color="#a50d14"></stop><stop offset="1" stop-color="#e82d2f"></stop></linearGradient></defs><path fill="url(#Y)" d="M235 97l7-1c1 0 1 2 1 3v1c0 1 1 2 1 3l-7 4-4 2-3 1v1c-3 3-7 5-11 8-2 2-3 5-5 7-1 1-3 2-4 2-5 3-8 6-10 11-1-1-2-1-3-2h-1c0-2 0 0-2-1h1l-1-1-2 2h-2l-1-1-2-2c-1-1-2-1-3 0l-1-2h-1c-1 0-2-1-3-2l1-2c0 1 1 1 2 1 2 0 5-2 7-3 0-1 1-2 2-2h0l3-3c-1-1-1-1-3-1-2 1-5-1-8-1h-3-3c-1-1-1-1-2-1l-4 1c-1 0-1 0-2-1s-1-2-1-3h0c0-1 0-2 1-3-1 0-1-1-1-2s2-2 3-3v-1c2-1 4-2 6-2 1-1 1-1 2-1h2l24-1c4 0 8-1 12-1s9-1 14-2l4-2z"></path><path d="M183 107l3 3 1-1h2 1l1-1h1v1c-1 0-1 0-2 1h-2 0c0 1-1 1-2 1h0c-2-1-3-1-4-3l1-1z" class="T"></path><path d="M219 108c1 0 3 0 5-1 2 0 6-3 8-2l-4 2c-2 1-4 3-6 4-1-1-2-1-2-1 0-1-1-1-1-2z" class="K"></path><path d="M232 105h1c1 0 3 1 4 2l-4 2-3 1h0v-1c-1 0-1-1-2-2l4-2z" class="o"></path><path d="M181 113h0c-3 1-6 1-9 1h0c2 1 3 0 4 1h2l1 1h3l-5 2-3-1-1-1v-1c-1 0-2 0-2-1v-1c1 0 2 1 3 0h1 4 2z" class="O"></path><path d="M171 113h4-1c-1 1-2 0-3 0v1c0 1 1 1 2 1v1l1 1 3 1h-2l-4 1c-1 0-1 0-2-1s-1-2-1-3h0c1-1 2-2 3-2z" class="F"></path><path d="M205 112c4-2 9-3 14-4 0 1 1 1 1 2 0 0 1 0 2 1h-1l-2-1c-1 1-4 2-5 2h-1-4c-1 0-3 1-4 0z" class="Q"></path><path d="M186 116l19-4c1 1 3 0 4 0l-4 1c-5 2-8 2-13 3-1 0-2 1-3 1l-1-1h-2z" class="S"></path><path d="M205 113l2 1v1l-2 1h-2-1l-2 1-2 1v1h-1-1-3c-1 1-1 1-2 1-2 1-5-1-8-1h-3-3c-1-1-1-1-2-1h2l5-2c1 1 3 0 4 0h2l1 1c1 0 2-1 3-1 5-1 8-1 13-3z" class="O"></path><path d="M182 116c1 1 3 0 4 0h2l1 1-9 2h-3c-1-1-1-1-2-1h2l5-2z" class="d"></path><path d="M235 97l7-1c1 0 1 2 1 3v1c0 1 1 2 1 3l-7 4c-1-1-3-2-4-2l2-2c-1 0-2 1-3 1v-1-2l-1-2 4-2z" class="U"></path><path d="M231 99l4-2v1c2 1 3 1 5 2-1 1-3 3-5 3h0c-1 0-2 1-3 1v-1-2l-1-2z" class="k"></path><path d="M213 112h1c1 0 4-1 5-2l2 1c-2 1-5 4-6 4l-7 5c-3 0-5 0-8 1l1-1c-2 0-4 1-6 1h-1c-1-1-1-1-3-1 1 0 1 0 2-1h3 1 1v-1l2-1 2-1h1 2l2-1v-1l-2-1 4-1h4z" class="c"></path><path d="M213 115h2l-7 5c-3 0-5 0-8 1l1-1c1-1 3-1 5-2 3-1 5-2 7-3z" class="S"></path><path d="M213 112h1c1 0 4-1 5-2l2 1c-2 1-5 4-6 4h-2v-1l-2 1c-4 2-9 3-13 4v-1l2-1 2-1h1 2l2-1v-1l-2-1 4-1h4z" class="C"></path><path d="M209 112h4v1c-1 0-2 0-2 1h-1l-3 1v-1l-2-1 4-1z" class="D"></path><path d="M177 104h6l4 5-1 1-3-3-1 1c1 2 2 2 4 3v1h-2-3v1h-2-4-4c-1 0-2 1-3 2 0-1 0-2 1-3-1 0-1-1-1-2s2-2 3-3v-1c2-1 4-2 6-2z" class="c"></path><path d="M172 112c2-1 5 0 7-1v2h-4-4v-1h1z" class="j"></path><path d="M171 113v-1h1 4c0 1 0 0-1 1h-4z" class="b"></path><path d="M177 104h6l4 5-1 1-3-3c-2-1-4-1-7-1-2 0-4 2-6 4l-1 2c-1 0-1-1-1-2s2-2 3-3v-1c2-1 4-2 6-2z" class="p"></path><path d="M222 111c2-1 4-3 6-4 1 1 1 2 2 2v1h0v1c-3 3-7 5-11 8-2 2-3 5-5 7-1 1-3 2-4 2-5 3-8 6-10 11-1-1-2-1-3-2h-1c0-2 0 0-2-1h1l-1-1-2 2h-2l-1-1-2-2c-1-1-2-1-3 0l-1-2h-1c-1 0-2-1-3-2l1-2c0 1 1 1 2 1 2 0 5-2 7-3 0-1 1-2 2-2h0l3-3h1c2 0 4-1 6-1l-1 1c3-1 5-1 8-1l7-5c1 0 4-3 6-4h1z" class="M"></path><path d="M195 129c1-1 1-1 2-1 0 2-1 3-1 4-1-1-1-2-1-3z" class="C"></path><path d="M194 121h1c2 0 4-1 6-1l-1 1c3-1 5-1 8-1-2 2-4 3-6 4-2 2-3 3-5 3v1c-1 0-1 0-2 1 0 1 0 2 1 3-1 1-2 2-2 3l-2 2h-2l-1-1-2-2c-1-1-2-1-3 0l-1-2h-1c-1 0-2-1-3-2l1-2c0 1 1 1 2 1 2 0 5-2 7-3 0-1 1-2 2-2h0l3-3z" class="j"></path><path d="M195 123c0 1 1 1 1 1-2 2-4 2-6 4h-1v-1c2-2 3-3 6-4z" class="N"></path><path d="M190 134c1-1 1-3 3-4h1c0-1 1-1 1-1 0 1 0 2 1 3-1 1-2 2-2 3l-2 2h-2l-1-1 1-2z" class="D"></path><path d="M190 134h1c1 1 1 1 1 3h-2l-1-1 1-2z" class="K"></path><path d="M194 121h1c2 0 4-1 6-1l-1 1-4 2h-1c-3 1-4 2-6 4l-3 2c-1 1-1 2-2 2h-1s-1 0-1 1c-1 0-2-1-3-2l1-2c0 1 1 1 2 1 2 0 5-2 7-3 0-1 1-2 2-2h0l3-3z" class="R"></path><defs><linearGradient id="Z" x1="207.022" y1="273.935" x2="158.965" y2="300.777" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#3e3b3b"></stop></linearGradient></defs><path fill="url(#Z)" d="M202 263s0 1 1 2v-2c1 1 1 2 1 3 0 3-1 6-1 9 3 2 6 6 8 9 1 2 1 3 1 5v2c-1 5-2 10-2 15h-1c-11-1-22 2-33 7v1h-1c-1 1-1 1-2 1h0c-1 2-3 3-4 4-1 2-2 4-4 5-1 1-2 1-2 2v2l-1 3c-1 1-1 2-1 2v2 1h-1 0v-5s0-1 1-1v-2l1-2c0-1 0-1 1-2l1-4c1 0 1-1 1-1l1-1c0-1 1-1 1-2h0c1-1 2-1 2-2 1-1 2-2 2-3 1-1 2-1 2-2l-1-1v-4c0-1 0-2 1-3 0 1 1 2 1 3v-2-4-3c-1-2-1-2-1-4l-2-1h-1v-3h-1c0 1 1 3 0 4-1-2-3-4-3-7-2-2-5-4-7-6-1-1-1 0-1-1 2-3 5-2 7-4-4 0-9 2-12 3 1 0 1-1 2-1v-1h-2 0-1l1-2h0-1v-1c1-2 6-4 9-5 2 0 4-1 7-1 3-1 6-1 9-2 2 0 4 0 6 1h6c4 0 9 0 13-1z"></path><path d="M179 268l3-1 1 1h-4 0z" class="B"></path><path d="M169 287v-1h0c1 0 2 1 3 2 0 1 0 1-1 2h-1v-3h-1z" class="z"></path><path d="M179 268h0l-12 2-4 1h0v-1c5-3 11-1 16-2h0z" class="P"></path><path d="M159 278v-1c4 0 4 3 7 4 0 1 1 2 1 2l-1 1c-2-2-5-4-7-6z" class="W"></path><path d="M172 288v-1c0-2 2-5 2-7h-1l-1-1v-1h2v2c2-2 2-7 5-8v1c0 1-1 2-1 2-1 2-2 3-2 5s-1 3-2 5c0 2 0 2-1 4v2h0l-2-1c1-1 1-1 1-2z" class="B"></path><path d="M203 265v-2c1 1 1 2 1 3 0 3-1 6-1 9s-2 6-3 9c0-7 2-13 3-19z" class="M"></path><path d="M187 301h1c0 1-1 2-1 2v1c2-1 4-3 7-3h0c-1 2-3 2-4 3h-1l-1 1h-1c-1 1-1 1-2 1l1 1h2c-1 0-2 1-3 1-2 0-3 2-5 1l-1-1c1-2 3-3 5-4l2-2 1-1z" class="E"></path><path d="M161 266c2 0 4-1 7-1 3-1 6-1 9-2 2 0 4 0 6 1-8 1-16 2-23 4-2 1-5 2-7 4h-1v-1c1-2 6-4 9-5z" class="r"></path><path d="M178 301h0c2-2 2-5 3-8 1-2 2-3 2-5l1-1c1 1 0 2 0 3s0 1-1 1c0 1-2 6-2 8v1h2l2-2c1 0 1-1 2-1l2-2 6-3 1-1h1c-2 1-3 3-5 3l-2 2-1 1-4 3-1 1c-2 1-2 3-4 3l-3 3c-2 2-4 4-6 7 0 1-1 1-2 2 0 1-1 1-2 2h-1c0-1 1-1 1-2h0c1-1 2-1 2-2 1-1 2-2 2-3 1-1 2-1 2-2l-1-1v-4c0-1 0-2 1-3 0 1 1 2 1 3v-2c1 1 1 1 1 2 1 0 1 0 1 1 1-1 1-2 2-3v-1z" class="f"></path><path d="M174 298c0-1 0-1 1-2 0-1-1-3 0-4v-3s1 0 1-1v-1-1l1-3c1-1 1-3 2-4 0-1 0-2 1-2 0-1 0-1 1-2 0-1 0-2 1-3h1v1h0v1c-1 0-1 1-1 2-1 0-1 1-1 2v1c1-1 1-2 1-3l1-1v-1s1-2 2-2h1l-1 1c-1 1-1 3-2 4v1c-1 1-1 2-1 4-1 2-3 5-3 8v3c0 2 0 5-1 8h0v1c-1 1-1 2-2 3 0-1 0-1-1-1 0-1 0-1-1-2v-4z" class="B"></path><path d="M248 192h0v-1c0-1 0-1-1-2v-2h0v-2h0 1v-2-1h0 1v2h0l1-1 1 3 4 10 6 18c1 5 2 9 4 13 0-3-2-5-1-7 0 0 0-1-1-1v-2c0-1-1-1-1-1 0-2-1-3-1-4h1c1 1 0 1 1 2v1 1c1 2 2 6 4 8l21 59 14 39 16 44 12 34c2 6 6 13 7 19l-1 1-1-1-1 2v1h-1s0-1-1-1l-2-2c-4-6-6-11-8-17l-29-75-21-57v-2l-2-6-7-23 1-1-3-9s-1-3-1-4l-6-16-4-11c-1-2-2-4-2-6z" class="a"></path><path d="M294 325l3 7v-3c-1-1-1-2 0-3s1 0 2 0c0 1 1 1 2 2l32 87 2 4-1 2v1h-1s0-1-1-1l-2-2c-4-6-6-11-8-17l-29-75 1-2z" class="X"></path><path d="M331 419c0-1 1-2 0-2v-1h0 1l1-1 2 4-1 2v1h-1s0-1-1-1h1l-2-2z" class="J"></path><path d="M294 325l3 7 12 32 13 34c2 7 5 15 9 21l2 2h-1l-2-2c-4-6-6-11-8-17l-29-75 1-2z" class="g"></path><path d="M248 192h0v-1c0-1 0-1-1-2v-2h0v-2h0 1v-2-1h0 1v2h0l1-1 1 3 4 10 6 18c1 5 2 9 4 13l12 33 7 21 5 13 8 22c1 4 3 8 4 12-1-1-2-1-2-2-1 0-1-1-2 0s-1 2 0 3v3l-3-7-1 2-21-57v-2l-2-6-7-23 1-1-3-9s-1-3-1-4l-6-16-4-11c-1-2-2-4-2-6z" class="k"></path><path d="M261 229c2 1 2 2 2 4 2 4 3 9 4 13 1 1 1 0 1 1v1h1 0c1 3 3 4 3 7h-1c0 2 1 5 2 7v1 1c1 0 1 1 1 1 2 5 4 10 5 14 0 2 1 3 1 4 1 1 1 2 1 3l-17-48-3-9z" class="U"></path><path d="M264 238l17 48 13 39-1 2-21-57v-2l-2-6-7-23 1-1z" class="Z"></path><path d="M158 110h0l2 1c0 1 1 1 2 1-1 1-1 3-1 4h2c1 0 2 0 3 1h1c0-1 0-1 1-2 0 1 0 2 1 3s1 1 2 1l4-1c1 0 1 0 2 1h3 3c3 0 6 2 8 1 2 0 2 0 3 1l-3 3h0c-1 0-2 1-2 2-2 1-5 3-7 3-1 0-2 0-2-1l-1 2c1 1 2 2 3 2h1l1 2 3 2c-1 0-2 1-2 1 3 4 6 7 8 11 0 1 1 3 1 4v1c-1 2-1 5-1 7l-1 1c0 2-1 3-2 5 1 1 1 1 1 2l-3 3-1 2v3c0 3 1 8 2 11 0 0 0 1-1 2l-3-7v3l1 3c2 4 3 9 5 13l1 4c-1 0-2 0-2 1-1-1-2-3-4-4s-3-5-4-7-2-4-4-5c-5-9-9-19-15-27-2-3-4-5-6-8h0v-1l-1-1v-2h-1l2-1c-2-2-4-4-7-5-2-2-4-4-7-5-2 0-4-1-6-2-2 0-4-1-5 0-2 0-3 0-4 1-4-1-9-1-13 0l-1-1h-4-2s-1-1-2 0h-2-3c-1 1-2 1-3 1s-2 1-3 1l1-1h-1c-3 3-6 6-9 10h0l2-4c1-2 3-4 5-6 1-1 2-2 3-4 2-1 3-2 3-4 0 0 2-2 3-2 8-6 20-11 30-10h6c1 0 3 0 4 1h2l1 1c3 1 5 1 7 3 1 2 1 4 2 6l1 2v1h-1c1 0 2 0 2 1l1 1c0-3-1-4-1-7l-2-4v-2h1v-2s0-1-1-1v-1c1-2 1-2 0-4 1 0 1-1 1-2h2v-1l1-1z" class="H"></path><path d="M173 161l7 11-1 3-5-9c0-2-1-3-1-5z" class="L"></path><path d="M180 172c2 3 4 7 5 10v3l1 3v1h-1l-6-14 1-3zm-79-34c11-5 23-5 35 0h1c-2 0-4-1-5 0-2 0-3 0-4 1-4-1-9-1-13 0l-1-1h-4-2s-1-1-2 0h-2-3z" class="N"></path><path d="M132 130h1c4 0 8 1 12 3l2 1 1 1h1c1 1 2 1 2 1v1c2 2 9 5 9 8-1 0-1-1-2 0-1 0-2-2-3-2-2-2-4-3-6-4-5-3-11-5-18-6 0-1-1-1-2-2-1 0-1 0-1-1h0 4z" class="M"></path><path d="M132 130h1c4 0 8 1 12 3l2 1c-2 1-2 1-4 0-4-2-9-3-14-3-1 0-1 0-1-1h0 4z" class="X"></path><path d="M151 136c2 1 4 3 6 4 3 2 6 4 8 7 3 4 6 7 9 11 3 5 6 11 10 16v-1l6-7c1 1 1 1 1 2l-3 3-1 2v3c0 3 1 8 2 11 0 0 0 1-1 2l-3-7c-1-3-3-7-5-10l-7-11c-4-6-9-12-15-16 1-1 1 0 2 0 0-3-7-6-9-8v-1z" class="Z"></path><path d="M157 150c2 2 5 3 7 6 2 2 4 5 5 7 4 7 8 13 12 20 1 1 4 10 5 10l-1-4h1v-1c2 4 3 9 5 13l1 4c-1 0-2 0-2 1-1-1-2-3-4-4s-3-5-4-7-2-4-4-5c-5-9-9-19-15-27-2-3-4-5-6-8h0v-1l-1-1v-2h-1l2-1z" class="G"></path><path d="M186 202c0-1 0-2 1-3h0c1 1 1 2 2 3l1-1h1l1 4c-1 0-2 0-2 1-1-1-2-3-4-4z" class="O"></path><path d="M102 129c8-6 20-11 30-10h6c1 0 3 0 4 1h2l1 1c3 1 5 1 7 3 1 2 1 4 2 6l1 2v1h-1c1 0 2 0 2 1l1 1h0c0 1 0 1-1 1 1 2 1 3 1 4-2-1-4-3-6-4 0 0-1 0-2-1h-1l-1-1-2-1c-4-2-8-3-12-3h-1-4 0c0 1 0 1 1 1 1 1 2 1 2 2-9-2-18-2-27 2l-6 4c-1 0-2 1-3 1l1-1h-1c-3 3-6 6-9 10h0l2-4c1-2 3-4 5-6 1-1 2-2 3-4 2-1 3-2 3-4 0 0 2-2 3-2z" class="M"></path><path d="M132 128c5 1 9 1 13 2 1 0 3 0 3 1h-1c0 1 1 1 1 1 1 0 1 0 1 1-2-1-2-1-4 0-4-2-8-3-12-3l-1-1h0v-1z" class="Q"></path><path d="M149 133c0-1 0-1-1-1 0 0-1 0-1-1h1l6 2c1 0 2 0 2 1l1 1h0c0 1 0 1-1 1 1 2 1 3 1 4-2-1-4-3-6-4 0 0-1 0-2-1h-1l-1-1-2-1c2-1 2-1 4 0z" class="F"></path><path d="M149 133c0-1 0-1-1-1 0 0-1 0-1-1h1l6 2c1 0 2 0 2 1l1 1h0c0 1 0 1-1 1l-7-3z" class="H"></path><path d="M104 135c0-1 1-1 2-2v-1c1 0 3-1 4-2 2 0 4 0 6-1 4 0 7-1 11-1h5v1h0l1 1h-1-4 0c0 1 0 1 1 1 1 1 2 1 2 2-9-2-18-2-27 2z" class="N"></path><path d="M127 128h5v1h0l1 1h-1c-2-1-5-1-8-1 1 0 1-1 1-1h2z" class="G"></path><path d="M116 126c5-3 14-3 20-4h3c2 0 4 0 6 1 1 0 3 1 4 1h3c1 2 1 4 2 6h-2c-2 0-2-1-4-2s-3-1-5-1h-1c-4 0-8-1-12-1-5 0-9 1-14 0z" class="L"></path><path d="M142 124v-1h3 0c1 0 3 1 4 1h3c1 2 1 4 2 6h-2c-2 0-2-1-4-2-2-2-5-2-7-3v-1h1z" class="N"></path><path d="M142 124c3 1 7 2 10 5v1c-2 0-2-1-4-2-2-2-5-2-7-3v-1h1zm-40 5c8-6 20-11 30-10h6c1 0 3 0 4 1h2l1 1c3 1 5 1 7 3h-3c-1 0-3-1-4-1-2-1-4-1-6-1h-3c-6 1-15 1-20 4-5 1-13 4-16 8-2 2-4 3-5 5-3 3-6 6-9 10h0l2-4c1-2 3-4 5-6 1-1 2-2 3-4 2-1 3-2 3-4 0 0 2-2 3-2z" class="G"></path><defs><linearGradient id="a" x1="182.614" y1="135.918" x2="178.009" y2="148.911" xlink:href="#B"><stop offset="0" stop-color="#231716"></stop><stop offset="1" stop-color="#451b1c"></stop></linearGradient></defs><path fill="url(#a)" d="M158 110h0l2 1c0 1 1 1 2 1-1 1-1 3-1 4h2c1 0 2 0 3 1h1c0-1 0-1 1-2 0 1 0 2 1 3s1 1 2 1l4-1c1 0 1 0 2 1h3 3c3 0 6 2 8 1 2 0 2 0 3 1l-3 3h0c-1 0-2 1-2 2-2 1-5 3-7 3-1 0-2 0-2-1l-1 2c1 1 2 2 3 2h1l1 2 3 2c-1 0-2 1-2 1 3 4 6 7 8 11 0 1 1 3 1 4v1c-1 2-1 5-1 7l-1 1c0 2-1 3-2 5l-6 7v1c-4-5-7-11-10-16-3-4-6-7-9-11-2-3-5-5-8-7 0-1 0-2-1-4 1 0 1 0 1-1h0c0-3-1-4-1-7l-2-4v-2h1v-2s0-1-1-1v-1c1-2 1-2 0-4 1 0 1-1 1-2h2v-1l1-1z"></path><path d="M168 133v-1h1l2 1h0l-1 1 1 1h1 0-2l-1-1s-1 0-1-1z" class="H"></path><path d="M170 139v-3h3c1 1 2 2 2 4v1s-1 1-2 1c0 1-2 1-2 2-1-1-1-2-1-3l-1-1 1-1z" class="G"></path><path d="M172 138c1 0 2 1 3 2v1s-1 1-2 1c0-1-1-3-1-4z" class="C"></path><path d="M170 139v-3h3c1 1 2 2 2 4-1-1-2-2-3-2s-1 0-2 1z" class="R"></path><defs><linearGradient id="b" x1="186.716" y1="135.002" x2="183.108" y2="148.193" xlink:href="#B"><stop offset="0" stop-color="#c52424"></stop><stop offset="1" stop-color="#f73b3b"></stop></linearGradient></defs><path fill="url(#b)" d="M177 133h0c-1-2-3-2-4-4l-1-2v-1c2 2 3 4 5 5v-3l2 2c1 1 2 2 3 2h1l1 2 3 2c-1 0-2 1-2 1 3 4 6 7 8 11 0 1 1 3 1 4v1c-1 2-1 5-1 7-1-2-1-5-2-7-2-7-6-12-11-17-1-1-3-2-3-3z"></path><path d="M177 128l2 2c1 1 2 2 3 2h1c-2 0-2 1-3 1l-3-2v-3z" class="q"></path><path d="M183 132l1 2 3 2c-1 0-2 1-2 1-2 0-3-2-5-4 1 0 1-1 3-1z" class="AD"></path><path d="M157 127l1 4v2c1 0 1-2 2-2 2 0 4-1 6-1h1v2 1h1c0 1 1 1 1 1l1 1c1 1 2 1 3 1h-3v3l-1 1 1 1c0 1 0 2 1 3-2 1-3 1-4 1l-1 1-1 1c-2-3-5-5-8-7 0-1 0-2-1-4 1 0 1 0 1-1h0c0-3-1-4-1-7l1-1z" class="I"></path><path d="M159 135c2 1 2 1 3 3h0-2l-1 1v-1c-1-1 0-1 0-3z" class="L"></path><path d="M167 133h1c0 1 1 1 1 1l1 1c1 1 2 1 3 1h-3v3l-1 1c-1-2-1-5-2-7z" class="D"></path><path d="M157 135h2c0 2-1 2 0 3v1l1-1h2 0l2 1 3 3c1 0 2 0 3-1 0 1 0 2 1 3-2 1-3 1-4 1l-1 1-1 1c-2-3-5-5-8-7 0-1 0-2-1-4 1 0 1 0 1-1z" class="Q"></path><path d="M162 138l2 1v2h1c0 1 1 1 1 2l-1 1c-2-2-3-4-6-5l1-1h2 0z" class="N"></path><path d="M164 139l3 3c1 0 2 0 3-1 0 1 0 2 1 3-2 1-3 1-4 1l-2-1 1-1c0-1-1-1-1-2h-1v-2z" class="L"></path><path d="M160 121v-1c3-1 7 4 10 3 1 1 1 2 2 3v1l1 2c1 2 3 2 4 4h0c-1 0-2-1-3-1-1 1-2 1-3 1h0l-2-1h-1v1h-1v-1-2h-1c-2 0-4 1-6 1-1 0-1 2-2 2v-2l-1-4-1 1-2-4v-2h1v-2l1-1 1 1v1 1h1c1-1 1-1 2-1z" class="B"></path><path d="M157 123c0 1 0 2 1 3h3v1h-1c-1 1-1 4-2 4l-1-4v-4z" class="p"></path><path d="M155 120l1-1 1 1v1 1h0v1 4l-1 1-2-4v-2h1v-2z" class="x"></path><path d="M155 120l1-1 1 1v1 1h0l-1 1-1-1v-2z" class="g"></path><path d="M161 126h5c2 1 1 3 3 4l1-1 1 4-2-1h-1v1h-1v-1-3c0-1-1-1-1-1-2-1-4-2-5-1v-1z" class="E"></path><path d="M161 127c1-1 3 0 5 1 0 0 1 0 1 1v3-2h-1c-2 0-4 1-6 1-1 0-1 2-2 2v-2c1 0 1-3 2-4h1z" class="F"></path><path d="M160 121v-1c3-1 7 4 10 3 1 1 1 2 2 3v1l1 2c1 2 3 2 4 4h0c-1 0-2-1-3-1-1 1-2 1-3 1h0l-1-4v-2c-1-3-7-5-10-6z" class="I"></path><path d="M158 110h0l2 1c0 1 1 1 2 1-1 1-1 3-1 4h2c1 0 2 0 3 1h1c0-1 0-1 1-2 0 1 0 2 1 3s1 1 2 1l4-1c1 0 1 0 2 1h3 3c3 0 6 2 8 1 2 0 2 0 3 1l-3 3h0c-1 0-2 1-2 2-2 1-5 3-7 3-1 0-2 0-2-1l-1 2-2-2v3c-2-1-3-3-5-5-1-1-1-2-2-3-3 1-7-4-10-3v1c-1 0-1 0-2 1h-1v-1-1l-1-1-1 1s0-1-1-1v-1c1-2 1-2 0-4 1 0 1-1 1-2h2v-1l1-1z" class="D"></path><path d="M161 116h2c1 0 2 0 3 1h1l-1 1v-1c-3 0-5 1-7 2l-2 2v-1l-1-1h0c1-1 3-2 4-3h1z" class="Z"></path><path d="M170 123h-1c-2-1-4-3-6-3v-1c2 0 4 1 6 2h0c2 3 6 4 8 7v3c-2-1-3-3-5-5-1-1-1-2-2-3zm-12-13h0l2 1c0 1 1 1 2 1-1 1-1 3-1 4h-1c-1 1-3 2-4 3h0l-1 1s0-1-1-1v-1c1-2 1-2 0-4 1 0 1-1 1-2h2v-1l1-1z" class="U"></path><path d="M158 110h0l2 1c0 1 1 1 2 1-1 1-1 3-1 4h-1-1c0-1 0-2 1-2 0-1 0-1-1-2h-2v-1l1-1z" class="o"></path><path d="M156 119v-2-1c0-1 0-2 1-3h1l2 1c-1 0-1 1-1 2h1c-1 1-3 2-4 3z" class="AF"></path><path d="M180 128c-1 0-2-1-2-1-2-2-3-2-4-3l10-1h4l1 1 1-1h0c1 1 0 1 1 1-1 0-2 1-2 2-2 1-5 3-7 3-1 0-2 0-2-1z" class="I"></path><path d="M184 123h4l1 1 1-1h0c1 1 0 1 1 1-1 0-2 1-2 2l-2-1c-1-1-2-1-3-2z" class="w"></path><path d="M171 119l4-1c1 0 1 0 2 1h3 3c3 0 6 2 8 1 2 0 2 0 3 1l-3 3h0c-1 0 0 0-1-1h0l-1 1-1-1h0c-3-1-7-1-10-1-2 0-4 1-6 1-1-1-1-2-2-3h0v-1h1z" class="I"></path><path d="M171 119l4-1c1 0 1 0 2 1-2 1-4 1-7 1h0v-1h1z" class="K"></path><path d="M174 143l5-2c4 3 6 6 9 10l3 5v-1-2c1 2 1 5 2 7l-1 1c0 2-1 3-2 5l-6 7v1c-4-5-7-11-10-16-3-4-6-7-9-11l1-1c3 1 6-2 8-3z" class="I"></path><path d="M187 155v-2c0-1 0-1 1-2l3 5v-1-2c1 2 1 5 2 7l-1 1h-1c-2-2-3-4-4-6z" class="c"></path><path d="M174 143l5-2c4 3 6 6 9 10-1 1-1 1-1 2v2c-2-4-5-7-9-10-1-2-1-2-4-2z" class="e"></path><defs><linearGradient id="c" x1="508.353" y1="95.543" x2="510.13" y2="130.277" xlink:href="#B"><stop offset="0" stop-color="#ffebed"></stop><stop offset="1" stop-color="#f6f9fc"></stop></linearGradient></defs><path fill="url(#c)" d="M502 95h2l1 1 3 1h10c5 0 10 1 15 2 3 0 7 1 10 2 11 5 21 13 29 22l7 8 2 3 2 3 1 3v2l1 2c-1 0-2 0-2-1-1-2-3-3-4-4h-1-1l-1-1-2-3c-2-2-4-3-7-5h-1l1 1h0l-3-1h0c0 1 1 1 1 1v1c-1 0-3-2-4-2v1h0c-1-1-2-1-3-2h-2l-1 1c-5-1-9-2-14-2h-2v1c-1 1-3 0-3 1h-4c-1 0-2 1-3 1l-1 1c-1 0-3 1-4 1-1 1-2 2-3 2v-3l-2 3h-1l-1-1h-1c-1-1-1-1-2-1h0c-1 2-3 3-5 4v-1c1-1 3-2 4-4 1 0 1 0 1-1v1h1v-3c0-1-1-3 0-5v-1c-1-1-1-2-2-3-1 1-2 0-4 1-1 0-2 0-3 1h-1v-1-1c-1 1-2 1-4 1l-2 3-1-1-1 1h-2-1 0l1 1c-1 2-3 3-4 4h-3l-6-3c-1-1-2-2-3-2-3-2-7-4-11-5l-2-1-4-2v1h-1l-1 1c0-1 0-1-1 0l-3-3c-1-1-3-2-4-3l-10-6-2 2h-1v1h-1l-2-2-5-3c2 0 3 1 5 2 1-1 1-1 1-2s-1-1-1-2v-1c1 0 1-1 1-1h1v-1h1c3 0 6 0 9 1h1 2-2 0c-1 0-1-1-2-1h0-1 0 1 1 8l17 1c2 0 7 0 9-1 1-1 2-1 4-2l16-2z"></path><path d="M520 114c2 0 3-1 5-2l-1 1c-1 1-3 2-4 3l1 1c0-1 1-2 2-2h0c1-1 1-1 2-1h1c-2 1-4 2-5 3-2 2-3 4-4 6v-4c1-2 2-3 3-5z" class="Z"></path><path d="M539 105c2 0 4 1 5 2 2 0 4 1 5 2h2v1l-1-1h-2c-2 0-4-2-6-1-2 0-3 1-4 1-1 1-2 1-4 1-1-1-2 0-3 0 1-1 2-1 3-1 1-1 3-2 5-3h0v-1z" class="x"></path><path d="M535 104l4 1v1h0c-2 1-4 2-5 3-1 0-2 0-3 1l-6 2-1-1h-1 0v-1c1 0 1 0 2-1h0c1 0 2-1 2-1h5l1-1h1 1 1v-1h2 0c-1-1-2-1-3-2h0z" class="AD"></path><path d="M572 123l7 8 2 3 2 3 1 3v2l-5-8-1-1h0c-2-4-4-7-6-10h0z" class="U"></path><path d="M575 131l2 2 2 1 5 8 1 2c-1 0-2 0-2-1-1-2-3-3-4-4h-1-1l-1-1-2-3h1c0-1 0-2-1-3l1-1z" class="D"></path><path d="M575 131l2 2v2c-1 0-1 0-1 1 1 0 2 1 3 1v2h-1-1l-1-1-2-3h1c0-1 0-2-1-3l1-1z" class="S"></path><path d="M529 103c1 0 5 0 6 1h0c1 1 2 1 3 2h0-2v1h-1-1-1l-1 1h-5s-1 1-2 1c0-1 1-2 1-3 1 0 2 0 3-1v-2z" class="y"></path><path d="M526 114c3-1 6-2 10-1h0v1 2c-1 1-2 0-3 1h-1v-1c-2 0-3 1-4 1-3 2-5 2-6 5h0l-1 1h-1v-3c1 0 1-1 1-2v-1c1-1 3-2 5-3z" class="B"></path><path d="M533 99c3 0 7 1 10 2 11 5 21 13 29 22h0c-6-7-15-14-23-17-7-3-13-4-20-5l1-1c1 0 2 0 3-1z" class="AC"></path><path d="M508 97h10c5 0 10 1 15 2-1 1-2 1-3 1l-1 1-25-1v-1h-2 0 2v-2h4z" class="U"></path><defs><linearGradient id="d" x1="492.781" y1="100.954" x2="494.071" y2="95.023" xlink:href="#B"><stop offset="0" stop-color="#ff5451"></stop><stop offset="1" stop-color="#e27979"></stop></linearGradient></defs><path fill="url(#d)" d="M502 95h2l1 1 3 1h-4v2h-2 0 2v1h-31c2 0 7 0 9-1 1-1 2-1 4-2l16-2z"></path><path d="M502 95h2l1 1 3 1h-4-3v-1l1-1z" class="q"></path><path d="M503 103c6-1 11-1 17-1 2 1 4 0 6 1h3v2c-1 1-2 1-3 1 0 1-1 2-1 3h0c-1 1-1 1-2 1v1h0 1l1 1h0c-2 1-3 2-5 2-1 0-2 1-3 2h0l-1-1-1-2c-2-3-4-5-6-7-2-1-3-1-5-2s-3-1-5-1h4z" class="AF"></path><path d="M507 103h8 0v2h-2c-2 0-4-1-6-1h0 0v-1h0z" class="U"></path><path d="M515 103h1 0v2s1 1 1 2h2l-1-1 1-1c0 2-1 1 1 2v1c-2 0-2 0-3 1l-1-1h-2c-1 0-2 0-2-1s3 0 4-1c-1-1-2-1-3-1h2v-2h0z" class="AA"></path><path d="M503 103c6-1 11-1 17-1 2 1 4 0 6 1 1 0 1 1 2 1h0-2-5v1h0 1l-1 1h-1v1c-2-1-1 0-1-2l-1 1 1 1h-2c0-1-1-2-1-2v-2h0-1-8-4z" class="AC"></path><path d="M526 103h3v2c-1 1-2 1-3 1 0 1-1 2-1 3h0c-1 1-1 1-2 1v1h0 1l1 1h0c-2 1-3 2-5 2-1 0-2 1-3 2h0l-1-1-1-2v-4h2c1-1 1-1 3-1v-1-1h1l1-1h-1 0v-1h5 2 0c-1 0-1-1-2-1z" class="q"></path><path d="M516 115c1-2 3-4 5-5v-1h-1l1-1c1 0 1 0 2 1h2 0c-1 1-1 1-2 1v1h0 1l1 1h0c-2 1-3 2-5 2-1 0-2 1-3 2h0l-1-1z" class="y"></path><path d="M491 103h8c2 0 3 0 5 1s3 1 5 2c2 2 4 4 6 7l1 2 1 1h0c1-1 2-2 3-2-1 2-2 3-3 5h-2l-2 1h0c-1 1-2 0-4 1-1 0-2 0-3 1h-1v-1-1l1-1c0-2 0-3-1-5h0 0l-1 1c0-1-1-2-2-3h0c-1-1-2-3-3-4 1 0 1-1 2-1-4-3-6-4-10-4h0z" class="J"></path><path d="M504 104c2 1 3 1 5 2l-1 1c-2-1-3-1-5 0-1-1-2-1-3-2l1-1h3z" class="G"></path><path d="M501 107c2 2 4 4 4 7h0 0l-1 1c0-1-1-2-2-3h0c-1-1-2-3-3-4 1 0 1-1 2-1z" class="K"></path><path d="M503 107c2-1 3-1 5 0l1-1c2 2 4 4 6 7l1 2 1 1h0c1-1 2-2 3-2-1 2-2 3-3 5h-2l-2-1h-2c-1 0-2-1-2-2-1-3-4-7-6-9z" class="N"></path><path d="M536 113c1 0 1 0 2 2 0 0 1 1 1 2h1c0-1 0-3 1-4 2 0 2 1 4 0v1l1 1 1-1c1 0 1 2 2 2l1-2h1l-1 1s0 1 1 2h0c1-1 2-1 3-1 0 1 1 1 1 2h1l1-1 1 1v1 1l1-1h1v1l1 1 1-1v1c0 1 1 1 1 1 1 0 1 0 1-1l1 1c0 1 0 1 1 2l1-1h1c0 1 1 4 2 4v-1h1v3c2 0 1-1 3 0l1 2-1 1c1 1 1 2 1 3h-1c-2-2-4-3-7-5h-1c-6-5-13-8-21-9-2-1-3-1-5-1-2-1-3-1-4-2h0c0-1-1-1-1 0h-2c-2 1-3 0-5-1 1 0 2-1 4-1v1h1c1-1 2 0 3-1v-2-1h0z" class="E"></path><path d="M567 130h1 0c2 1 3 2 4 2h1 0 1c1 1 1 2 1 3h-1c-2-2-4-3-7-5z" class="B"></path><path d="M528 117c2 1 3 2 5 1h2c0-1 1-1 1 0h0c1 1 2 1 4 2 2 0 3 0 5 1 8 1 15 4 21 9l1 1h0l-3-1h0c0 1 1 1 1 1v1c-1 0-3-2-4-2v1h0c-1-1-2-1-3-2h-2l-1 1c-5-1-9-2-14-2h-2v1c-1 1-3 0-3 1h-4c-1 0-2 1-3 1l-1 1c-1 0-3 1-4 1-1 1-2 2-3 2v-3l-2 3h-1l-1-1h-1c-1-1-1-1-2-1h0c-1 2-3 3-5 4v-1c1-1 3-2 4-4 1 0 1 0 1-1v1h1v-3c0-1-1-3 0-5v-1c-1-1-1-2-2-3h0l2-1h2v4c1-2 2-4 4-6v1c0 1 0 2-1 2v3h1l1-1h0c1-3 3-3 6-5z" class="b"></path><path d="M529 124l5-2 2 1c-1 1-1 1-2 1l-5 1 1-1h-1 0z" class="O"></path><path d="M521 130h0v-1c1-2 6-4 8-5h0 1l-1 1c-2 1-4 2-5 3s-2 2-3 2z" class="M"></path><path d="M534 124l-1 2h0c-4 1-8 2-11 5l-1 1-2 3h-1l-1-1c1-2 3-3 4-4 1 0 2-1 3-2s3-2 5-3l5-1z" class="D"></path><path d="M517 123c1-2 2-4 4-6v1c0 1 0 2-1 2v3h1c-1 1-2 2-2 3 0 2 0 4-2 6v-1l-1-1c1-2 2-5 1-7z" class="E"></path><path d="M528 117c2 1 3 2 5 1h2c0-1 1-1 1 0h0c1 1 2 1 4 2-2 0-3-1-4 0-1-1-1-2-2-2s-2 1-2 1h-1c-1 0-2 1-3 1-1 1-3 1-5 2h-1 0c1-3 3-3 6-5z" class="R"></path><path d="M515 119h2v4c1 2 0 5-1 7 0 1 0 1-1 1v-2c0-1-1-3 0-5v-1c-1-1-1-2-2-3h0l2-1z" class="M"></path><path d="M522 131c3-3 7-4 11-5h1c0 1 0 1-1 1l1 1h-1l-1 2c-1 0-2 1-3 1l-1 1c-1 0-3 1-4 1-1 1-2 2-3 2v-3l1-1z" class="J"></path><path d="M522 131l1 1c1-1 3-2 5-1v1c-1 0-3 1-4 1-1 1-2 2-3 2v-3l1-1zm12-9c4 0 8 0 12 2h2c2 0 4 1 7 2 2 1 2 2 3 3h-2l-1 1c-5-1-9-2-14-2h-2v1c-1 1-3 0-3 1h-4l1-2h1l-1-1c1 0 1 0 1-1h-1 0l1-2c1 0 1 0 2-1l-2-1z" class="N"></path><path d="M544 127h4c1-1 0-2 2-1 1 1 0 1 1 2-3-1-5-1-7-1zm-11-1c3-1 7-2 9-1-1 1-2 1-4 1l1 1h1 0c-3 0-4 0-6 1h0l-1-1c1 0 1 0 1-1h-1 0z" class="T"></path><path d="M534 128h0c2-1 3-1 6-1h1v1h-2v1c-1 1-3 0-3 1h-4l1-2h1z" class="M"></path><path d="M540 127h4c2 0 4 0 7 1 1 0 3 1 5 1l-1 1c-5-1-9-2-14-2v-1h-1 0zm-109-23c2 0 3 1 5 2 1-1 1-1 1-2s-1-1-1-2v-1c1 0 1-1 1-1h1v-1h1c3 0 6 0 9 1h1l42 3h0c4 0 6 1 10 4-1 0-1 1-2 1 1 1 2 3 3 4h0c1 1 2 2 2 3l1-1h0 0c1 2 1 3 1 5l-1 1c-1 1-2 1-4 1l-2 3-1-1-1 1h-2-1 0l1 1c-1 2-3 3-4 4h-3l-6-3c-1-1-2-2-3-2-3-2-7-4-11-5l-2-1-4-2v1h-1l-1 1c0-1 0-1-1 0l-3-3c-1-1-3-2-4-3l-10-6-2 2h-1v1h-1l-2-2-5-3z" class="I"></path><path d="M494 110h4v1c-1 1-2 1-4 1h-3v-1c1 0 2 0 3-1z" class="R"></path><path d="M431 104c2 0 3 1 5 2 1-1 1-1 1-2s-1-1-1-2v-1c1 0 1-1 1-1h1v-1h1c3 0 6 0 9 1-1 1-2 1-3 1-2-1-4-1-6-1 0 1-1 1-1 3h0 1 0c1 1 2 1 3 1-2 0-4 0-6-1 2 1 4 3 5 3h1l-2 2h-1v1h-1l-2-2-5-3z" class="o"></path><path d="M491 103c4 0 6 1 10 4-1 0-1 1-2 1-2-1-6-3-9-2h0c-2 1-3 2-4 3-1 0-1 1-1 1-2 0-4 0-5-1-1 0-2 0-3-1l8 1v-1c0-1 2-4 3-4l3-1z" class="E"></path><path d="M465 112l23 5h0-4 1v1h-1c-1 0-3 0-5 1v1h0c-1-1-4-1-5-2l1-1c-1-1-5-2-6-2l-6-1-2-1h5s-1 0-1-1z" class="S"></path><path d="M475 117l9 1c-1 0-3 0-5 1v1h0c-1-1-4-1-5-2l1-1z" class="T"></path><defs><linearGradient id="e" x1="496.464" y1="110.957" x2="492.9" y2="120.647" xlink:href="#B"><stop offset="0" stop-color="#4f1717"></stop><stop offset="1" stop-color="#352020"></stop></linearGradient></defs><path fill="url(#e)" d="M502 112c1 1 2 2 2 3l1-1h0 0c1 2 1 3 1 5l-1 1c-1 1-2 1-4 1l-2 3-1-1c0-1 1-1 1-2v-1c-4-1-9-1-14-2h0v-1h-1 4 0 4c3 0 7 1 10 2v-7z"></path><path d="M484 118h1 0c5 1 10 1 14 2v1c0 1-1 1-1 2l-1 1h-2v-1l-16-3v-1c2-1 4-1 5-1z" class="F"></path><path d="M441 106l1-1c2 1 4 2 6 2 2 1 16 5 17 5 0 1 1 1 1 1h-5l2 1 6 1-3 3-4-2v1h-1l-1 1c0-1 0-1-1 0l-3-3c-1-1-3-2-4-3l-10-6h-1z" class="K"></path><path d="M452 112c3-1 6 1 9 1l2 1 6 1-3 3-4-2v1h-1l-1 1c0-1 0-1-1 0l-3-3c-1-1-3-2-4-3z" class="Q"></path><path d="M462 116l-2-1 1-1h2l6 1-3 3-4-2z" class="j"></path><path d="M469 115c1 0 5 1 6 2l-1 1c1 1 4 1 5 2h0l16 3v1h-1 0l1 1c-1 2-3 3-4 4h-3l-6-3c-1-1-2-2-3-2-3-2-7-4-11-5l-2-1 3-3z" class="w"></path><path d="M469 115c1 0 5 1 6 2l-1 1c1 1 4 1 5 2h0l16 3v1h-1 0c-1 0-4-1-5-1-3-1-6-1-9-2-3 0-6-2-9-3h-3v1l-2-1 3-3z" class="c"></path><defs><linearGradient id="f" x1="389.546" y1="490.336" x2="371.422" y2="485.291" xlink:href="#B"><stop offset="0" stop-color="#631817"></stop><stop offset="1" stop-color="#a0181c"></stop></linearGradient></defs><path fill="url(#f)" d="M450 282h1v2 1l2-2-1 2c1 0 2 1 2 2v1l1-1c0 3-1 4-2 6s-1 5-2 7c-1 5-4 10-4 15-1 9-6 18-8 26l-2 7-12 33-15 42-5 15-13 38-4 11c-1 1-1 4-2 6l-40 115h0-1c0 1-1 2-1 3s-1 1-1 2 0 2-1 3v-1h-2 0 0v-3s1-6 1-7c2-3 2-7 3-9 0-2 1-4 1-5v-3l1-3-1-1c0-1 0-3 1-4v-4l3-9h0v-6c1-1 2-3 2-4l4-13-2-2 2-4 2-5c1-1 1-2 1-3h0c1-1 1-1 1-2s1-3 1-4h0c0-1 0-1 1-2v-1s0-1 1-1v-1-1l1-1v-1-1c0-1 1-2 1-3 0 0 0-1 1-1h0v-2c1-1 1-2 1-2v-1l1-2h2l5-14h-1 0v-1c0-1 0-2 1-2 0-2 1-3 1-4l1-4c1-2 2-3 2-4l1-4c1-1 1-1 1-2 0 0 1-1 1-2l1-3v-1l1-3c1-1 1-2 1-3h0c0-1 1-1 1-2l1-5c2-3 2-5 2-8l1-1c3-3 3-8 4-11l2-4h0l9-26c3-8 6-16 6-25 1-1 1-3 1-4s0-1 1-2v1c1 2 1 6 2 7h1v-4c1-1 1 0 2 0h1l1 1v-1c0-3 1-7 2-8v-1l2-3-1-4c0-1 1-2 1-4l1 1c1-1 2-4 3-5h0v-1c2-3 3-7 3-11l3-6c0-2 1-4 1-6l7-18c1-3 1-6 3-8 3-5 4-11 6-17z"></path><path d="M355 538h1l1-1-2 7-2-2 2-4z" class="B"></path><path d="M406 421s0 2 1 2l-5 13h0v-2c0-1 0-2 1-3 0-1 0-2 1-3v-2c1 0 1 0 1-1s0-1 1-2v-2z" class="G"></path><path d="M366 506l1-2h2l-4 11c-1-1-1-1-2 0 0-1 1-2 1-3 0 0 0-1 1-1h0v-2c1-1 1-2 1-2v-1z" class="B"></path><path d="M363 515c1-1 1-1 2 0l-8 22-1 1h-1l2-5c1-1 1-2 1-3h0c1-1 1-1 1-2s1-3 1-4h0c0-1 0-1 1-2v-1s0-1 1-1v-1-1l1-1v-1-1z" class="AG"></path><path d="M350 569l2-3c-1 4-2 9-4 13-1 2-1 4-2 6l-1-1c0-1 0-3 1-4v-4l3-9h0c0 2-1 4-1 6h0c0-2 0-2 2-4z" class="j"></path><path d="M346 576l3-9h0c0 2-1 4-1 6h0c0-2 0-2 2-4-1 2-1 5-2 7-1 1-1 1-1 2s-1 2-1 3v1-2h0v-4z" class="e"></path><path d="M433 331l1-2h1l-12 38-1-1c1-1 1-2 1-3v-2c-1 1-1 3-2 5v-1-1l2-3-1-4c0-1 1-2 1-4l1 1c1-1 2-4 3-5h0v-1c2-3 3-7 3-11l3-6z" class="c"></path><path d="M423 353l1 1c1-1 2-4 3-5h0c0 3-1 5-3 8 0 1 0 3-1 4l-1-4c0-1 1-2 1-4z" class="C"></path><path d="M426 361c0 1-1 3-1 4-1 1-1 1-1 2v4c0 1 0 2-1 3 0 2-1 4-1 6v-1l1-2h0v-1l1-1h0v-2h0l1-3c1 0 1-1 1-1 0-1 1-2 1-3v-1h1l-21 58c-1 0-1-2-1-2 1-1 1-2 1-3l2-6 1-1c0-1 0-1 1-2 0-2 1-3 1-4 1-2 1-3 2-5 0-1 1-3 2-4 2-7 5-14 6-21 0-1 1-2 1-3 1-2 0-4 1-5 0-2 1-4 2-6z" class="j"></path><path d="M393 430c1 1 1 1 1 2-1 3-2 6-3 10 0 1 0 4-1 5h-1c-1 3-2 8-4 11l-7 20-4 12h-1 0v-1c0-1 0-2 1-2 0-2 1-3 1-4l1-4c1-2 2-3 2-4l1-4c1-1 1-1 1-2 0 0 1-1 1-2l1-3v-1l1-3c1-1 1-2 1-3h0c0-1 1-1 1-2l1-5c2-3 2-5 2-8l1-1c3-3 3-8 4-11z" class="AG"></path><path d="M412 369v1c1 2 1 6 2 7h1v-4c1-1 1 0 2 0h1l1 1-1 3c-1 3-2 5-4 7h0c-1 1-2 1-3 1l-17 47c0-1 0-1-1-2l2-4h0l9-26c3-8 6-16 6-25 1-1 1-3 1-4s0-1 1-2z" class="B"></path><path d="M411 385c1-1 1-3 2-4 1-2 2-3 5-4-1 3-2 5-4 7h0c-1 1-2 1-3 1z" class="S"></path><path d="M450 282h1v2 1l2-2-1 2c1 0 2 1 2 2v1l1-1c0 3-1 4-2 6s-1 5-2 7c-1 5-4 10-4 15-1 0-2 1-3 2l-3 9-5 16-8 23h-1v1c0 1-1 2-1 3 0 0 0 1-1 1l-1 3h0v2h0l-1 1v1h0l-1 2v1c0-2 1-4 1-6 1-1 1-2 1-3v-4c0-1 0-1 1-2 0-1 1-3 1-4 1-3 2-5 3-7l4-16 8-21 5-16c0-2 1-4 2-5v-2h0c-1 1-2 3-2 4-4 11-6 21-11 31h-1l-1 2c0-2 1-4 1-6l7-18c1-3 1-6 3-8 3-5 4-11 6-17z" class="O"></path><path d="M444 317v-1c0-2 4-17 5-18l2 2c-1 5-4 10-4 15-1 0-2 1-3 2z" class="G"></path><path d="M444 317c1-1 2-2 3-2-1 9-6 18-8 26l-2 7-12 33-15 42-5 15-13 38-4 11c-1 1-1 4-2 6-1-1-1-2 0-2v-1c0-1 0-1 1-1v-2h0v-1c0-1 1-1 1-1v-2c0-1 0 0 1-1v-1h0v-1c-1 1-1 3-2 4v2c-1 4-3 7-4 11-1 1-1 3-2 4-1 2-1 4-2 6-1 1-1 0-2 1-1 2-1 6-2 7l27-79 5-13 21-58 8-23 5-16 3-9z" class="Q"></path><defs><linearGradient id="g" x1="208.692" y1="180.375" x2="250.058" y2="188.598" xlink:href="#B"><stop offset="0" stop-color="#c70e18"></stop><stop offset="1" stop-color="#ed3031"></stop></linearGradient></defs><path fill="url(#g)" d="M233 109c1 1 2 0 2 1v1c-1 2-1 2-3 3l-2 1-2 2 1 1 4-4-1 4v1 4s-1 1 0 2l1 1c0 1-1 3-1 4v5h-1v2c1 1 2 11 3 13 0 3 2 5 2 7v1 1 3h1c0-1 0-2-1-3l1-1v1l1 3h0c0 1 1 2 1 3 0 2 1 3 1 5l8 22c0 2 1 4 2 6l4 11 6 16c0 1 1 4 1 4l3 9-1 1 7 23 2 6v2c0 1 0 2 1 4v1l-1 1c2 3 4 8 5 12-1 0-1 0-1-1h-2v-2c-2-2-2-5-3-7-2-6-4-12-7-18l-1-4c-1 0-1 1-2 1l-2-5c-2-3-3-5-4-8l-4-8v2l1 2h0c-1 0 0 0-1 1-4-7-7-14-10-20-1-3-2-6-4-7h0 0c-1 0-1 1-1 1l-3 1-1 1c-2-1-3-2-4-4l-2-2h0c-1 0-1-1-1-1l-15-14c-2-3-6-5-7-8 1 0 2-1 3-2-2-1-4-2-5-3-4-2-8-4-11-8v-2l-3 3v-3l1-2 3-3c0-1 0-1-1-2 1-2 2-3 2-5l1-1c0-2 0-5 1-7v-1c0-1-1-3-1-4-2-4-5-7-8-11 0 0 1-1 2-1l-3-2c1-1 2-1 3 0l2 2 1 1h2l2-2 1 1h-1c2 1 2-1 2 1h1c1 1 2 1 3 2 2-5 5-8 10-11 1 0 3-1 4-2 2-2 3-5 5-7 4-3 8-5 11-8v-1l3-1z"></path><path d="M235 200l3 4-2 1-2-3v-1l1-1z" class="C"></path><path d="M212 164c5 1 9 2 13 3h-7-2-1-1l-2-3z" class="F"></path><path d="M220 171c3 0 5-1 7-2-1 1-4 4-4 5 0 2 1 4 1 6l-3-3c0-1 1-1 1-2s0-2-1-3c-1 0-1 0-1-1h0z" class="T"></path><path d="M240 198c-2-5-4-10-4-16l3-1h0v1c-1 1-1 4-1 6l3 9h0l1 1h0v1c0 1 1 2 1 2l-1 1-2-4z" class="o"></path><path d="M214 167h1l2 2c1 0 3 1 3 2h0c0 1 0 1 1 1 1 1 1 2 1 3s-1 1-1 2l3 3 11 20-1 1v1l-21-34c0-1 1-1 1-1z" class="Q"></path><path d="M217 169c1 0 3 1 3 2h0c0 1 0 1 1 1 1 1 1 2 1 3s-1 1-1 2c-2-3-3-5-4-8z" class="J"></path><path d="M231 137c1 1 2 11 3 13 0 3 2 5 2 7v1 1 3h1c0-1 0-2-1-3l1-1v1l1 3h0c0 1 1 2 1 3 0 2 1 3 1 5l8 22c0 2 1 4 2 6l4 11 6 16c0 1 1 4 1 4l3 9-1 1c-1-4-3-9-5-13-2-8-5-15-8-23l-7-20-5-14c-1-3-2-5-3-7l-2-8v-3l-2-14z" class="g"></path><path d="M231 154h2l2 8-2-2h0 0-1-1c0 1 0 1 1 1h-2v1h1 0c-1 1-8 0-10 0h-8-2 0c-1 0-1-1-2-2l-3-5h1l2 1h4v1h5 8 1 0c-1-1-1-1-1-2l1-1s1 1 2 1h1l1-1z" class="h"></path><path d="M206 155h1l2 1h4v1h-4 0c0 1 0 1 1 2h3l-2 1c1 0 1 1 2 1v1h-2 0c-1 0-1-1-2-2l-3-5z" class="J"></path><path d="M213 159c1 1 3 1 4 1 4 1 9 2 13 2h1 0c-1 1-8 0-10 0h-8v-1c-1 0-1-1-2-1l2-1z" class="O"></path><path d="M213 159c1 1 3 1 4 1v1h-4c-1 0-1-1-2-1l2-1z" class="F"></path><path d="M205 152l-2-4c1 0 2 0 4 1l2-1h2c1 1 4 0 5 0 3 1 5 2 9 2l-1-1 1-1 1 1c1 1 1 1 2 1l1 1 1 1h0c-1 0-1 1-1 1l2 1-1 1h-1c-1 0-2-1-2-1l-1 1c0 1 0 1 1 2h0-1-8-5v-1h-4l-2-1h-1s-1-2-1-3z" class="X"></path><path d="M215 155c3-1 5 0 7 1l-4 1h-5v-1h2v-1z" class="F"></path><path d="M218 150c3 1 5 1 7 2h1l-1 1c-3 0-5 0-7-2v-1z" class="I"></path><path d="M210 154l5 1v1h-2-4 1v-1h1l-1-1z" class="e"></path><path d="M205 152l5 2 1 1h-1v1h-1l-2-1h-1s-1-2-1-3z" class="G"></path><path d="M225 150l-1-1 1-1 1 1c1 1 1 1 2 1l1 1 1 1h0c-1 0-1 1-1 1-1 1-3 0-4 0l1-1h-1l2-1c-1-1-1-1-2-1z" class="h"></path><path d="M209 148h2c1 1 4 0 5 0 3 1 5 2 9 2 1 0 1 0 2 1l-2 1c-2-1-4-1-7-2h0c-3 0-6 0-9-1h-2l2-1z" class="J"></path><defs><linearGradient id="h" x1="217.816" y1="189.616" x2="223.775" y2="185.991" xlink:href="#B"><stop offset="0" stop-color="#e8d6d8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#h)" d="M184 134c1-1 2-1 3 0l2 2 1 1h2l2-2 1 1h-1c2 1 2-1 2 1h1c1 1 2 1 3 2-1 3-1 5 0 8h0v-1h0l1 2c0 1 1 1 2 1-1 0-1 0-1-1h0v-1l1-1c2 1 4 2 6 2l-2 1c-2-1-3-1-4-1l2 4c0 1 1 3 1 3l3 5c1 1 1 2 2 2l1 2 2 3s-1 0-1 1l21 34 2 3 2-1c1 2 2 5 4 8 0 1 0 2-1 3l9 18 1 3v2l1 2h0c-1 0 0 0-1 1-4-7-7-14-10-20-1-3-2-6-4-7-1-4-4-8-6-12-5-10-11-19-18-28l-7-10-13-16c-2-4-5-7-8-11 0 0 1-1 2-1l-3-2z"></path><path d="M238 204c1 2 2 5 4 8 0 1 0 2-1 3l-5-10 2-1z" class="V"></path><path d="M200 147v-1h0l1 2c0 1 1 1 2 1-1 0-1 0-1-1h0v-1l1-1c2 1 4 2 6 2l-2 1c-2-1-3-1-4-1l2 4c0 1 1 3 1 3l3 5c1 1 1 2 2 2l1 2 2 3s-1 0-1 1l-13-18v-3z" class="K"></path><path d="M184 134c1-1 2-1 3 0l2 2 1 1h2l2-2 1 1h-1c2 1 2-1 2 1h1c1 1 2 1 3 2-1 3-1 5 0 8h0v3c-2-2-3-5-6-8-2-2-5-4-7-6l-3-2z" class="w"></path><path d="M246 217v-1l-1-2-1-1v-1c-1-4-3-7-5-10h0v-1-1-1c0-2 0 1 0-1h1l2 4 1-1 1 3c1-2-1-3-1-4 1 0 3-1 4 0h2v2h0c1 1 1 2 1 2 0 1 1 2 1 3 0 0 0 1 1 1 0 2 0 3 1 4v1l1 2c1 1 1 3 1 4l1 3v1c1 0 1 1 1 1v1h0l1 2v-1c2 4 4 9 5 13l7 23 2 6v2c0 1 0 2 1 4v1l-1 1c2 3 4 8 5 12-1 0-1 0-1-1h-2v-2c-2-2-2-5-3-7-2-6-4-12-7-18l-1-4c-1 0-1 1-2 1l-2-5c-2-3-3-5-4-8l-4-8-1-3-9-18c1-1 1-2 1-3 0 1 1 2 2 3l1 2s0 1 1 1v-1z" class="u"></path><path d="M246 217v-1l-1-2-1-1v-1c-1-4-3-7-5-10h0v-1-1-1c0-2 0 1 0-1h1l2 4c2 4 3 8 5 11h1s1 0 1 1 0 1 1 2c0 1 1 2 1 3 1 1 1 2 1 2 1 3 3 6 4 9l3 9c1 2 2 4 2 6v1c2 4 4 8 5 12 0 1 0 1 1 2v1c1 2 2 3 2 5 1 3 3 6 4 9l-1 1-15-37c-2-2-3-5-4-8l-7-14z" class="k"></path><path d="M246 217v-1l-1-2-1-1v-1c-1-4-3-7-5-10h0v-1-1-1c0-2 0 1 0-1h1l2 4c2 4 3 8 5 11l8 19c0 2 2 5 2 7-2-2-3-5-4-8l-7-14z" class="I"></path><path d="M242 212c0 1 1 2 2 3l1 2s0 1 1 1v-1l7 14c1 3 2 6 4 8l15 37c2 3 4 8 5 12-1 0-1 0-1-1h-2v-2c-2-2-2-5-3-7-2-6-4-12-7-18l-1-4c-1 0-1 1-2 1l-2-5c-2-3-3-5-4-8l-4-8-1-3-9-18c1-1 1-2 1-3z" class="g"></path><path d="M255 244v-2l1 1v-1c3 4 5 10 7 14-1 0-1 1-2 1l-2-5c-2-3-3-5-4-8z" class="k"></path><path d="M242 212c0 1 1 2 2 3l1 2s0 1 1 1l5 12c1 2 2 5 3 7s2 4 2 5v1l-1-1v2l-4-8-1-3-9-18c1-1 1-2 1-3z" class="I"></path><path d="M251 230c1 2 2 5 3 7s2 4 2 5v1l-1-1v2l-4-8-1-3h0c1 1 2 3 3 5v-1l-1-1c0-1-1-2-1-3v-1-2z" class="U"></path><path d="M233 109c1 1 2 0 2 1v1c-1 2-1 2-3 3l-2 1-2 2 1 1 4-4-1 4v1 4s-1 1 0 2l1 1c0 1-1 3-1 4v5h-1v2l2 14v3h-2l-2-1s0-1 1-1h0l-1-1-1-1c-1 0-1 0-2-1l-1-1-1 1 1 1c-4 0-6-1-9-2-1 0-4 1-5 0h-2c-2 0-4-1-6-2l-1 1v1h0c0 1 0 1 1 1-1 0-2 0-2-1l-1-2h0v1h0c-1-3-1-5 0-8 2-5 5-8 10-11 1 0 3-1 4-2 2-2 3-5 5-7 4-3 8-5 11-8v-1l3-1z" class="Q"></path><path d="M229 114l2-2 1 2-2 1c0-1-1-1-1-1z" class="G"></path><path d="M231 112h0c1 0 2-1 3-2l1 1c-1 2-1 2-3 3l-1-2z" class="D"></path><path d="M208 136l1-1c1 0 2 1 3 1l1 2c1 0 1 0 2 1h0 1c-1 1-1 2-1 2l-7-5z" class="F"></path><path d="M229 114s1 0 1 1l-2 2-6 4-1-1c2-2 5-4 8-6z" class="b"></path><path d="M221 120l1 1 6-4 1 1c-3 2-6 4-8 7l-2 1v-2-1c0-1 1-2 2-3z" class="D"></path><path d="M219 126l2-1c-1 2-2 5-2 7l-3 7h-1 0c-1-1-1-1-2-1l-1-2c-1 0-2-1-3-1l-1 1h0c0-2 6-5 7-6l4-4z" class="O"></path><path d="M219 132l-3 7h-1 0c-1-1-1-1-2-1v-1c1-1 1-2 1-2 2-2 3-2 5-3z" class="T"></path><path d="M203 146c0-4 1-6 3-9v-1l1 1c4 4 12 8 17 11h1l-1 1 1 1c-4 0-6-1-9-2-1 0-4 1-5 0h-2c-2 0-4-1-6-2z" class="X"></path><path d="M229 118l4-4-1 4v1 4s-1 1 0 2l1 1c0 1-1 3-1 4v5h-1v2l2 14v3h-2l-2-1s0-1 1-1h0l-1-1-1-1c-1 0-1 0-2-1 0-1-1-1-2-2l-9-6s0-1 1-2l3-7c0-2 1-5 2-7 2-3 5-5 8-7z" class="w"></path><path d="M233 151v3h-2l-2-1s0-1 1-1 1 1 2 0l1-1z" class="I"></path><path d="M228 130v5h-1-1v-3c1-1 0-3 1-5 0 2-1 2 0 3h1z" class="F"></path><path d="M227 127v-4l1-1h1c1 2-1 6-1 8h-1c-1-1 0-1 0-3z" class="J"></path><path d="M223 130h1 1l-1 1c0 1-1 3 0 5v2h-1-2v-1c0-2 0-5 2-7z" class="F"></path><path d="M193 148l13 16 7 10c7 9 13 18 18 28 2 4 5 8 6 12h0 0c-1 0-1 1-1 1l-3 1-1 1c-2-1-3-2-4-4l-2-2h0c-1 0-1-1-1-1l-15-14c-2-3-6-5-7-8 1 0 2-1 3-2-2-1-4-2-5-3-4-2-8-4-11-8v-2l-3 3v-3l1-2 3-3c0-1 0-1-1-2 1-2 2-3 2-5l1-1c0-2 0-5 1-7v-1c0-1-1-3-1-4z" class="h"></path><path d="M193 160c0-2 0-5 1-7l1 1h0c2 2 3 6 4 8l7 13c1 2 3 5 3 7s0 2-1 3l-2 1c-2-1-4-2-5-3-4-2-8-4-11-8v-2l-3 3v-3l1-2 3-3c0-1 0-1-1-2 1-2 2-3 2-5l1-1z" class="B"></path><path d="M202 181h3v3h0c-2 0-2-1-3-2v-1z" class="F"></path><path d="M196 168v-1h1l3 3c2 4 4 8 5 11h-3c-1-2-2-5-3-7 0-1-1-3-1-4-1-1-1-1-1-2h-1z" class="O"></path><path d="M193 160c0-2 0-5 1-7l1 1c1 7-1 13-5 19h0l-3 3v-3l1-2 3-3c0-1 0-1-1-2 1-2 2-3 2-5l1-1z" class="x"></path><path d="M196 168h1c0 1 0 1 1 2 0 1 1 3 1 4 1 2 2 5 3 7v1c-3-1-5-3-8-5l2-9z" class="I"></path><defs><linearGradient id="i" x1="236.962" y1="299.016" x2="267.459" y2="280.259" xlink:href="#B"><stop offset="0" stop-color="#371516"></stop><stop offset="1" stop-color="#1c1817"></stop></linearGradient></defs><path fill="url(#i)" d="M190 173v2c3 4 7 6 11 8 1 1 3 2 5 3-1 1-2 2-3 2 1 3 5 5 7 8l15 14s0 1 1 1h0l2 2c1 2 2 3 4 4l1-1 3-1s0-1 1-1h0 0c2 1 3 4 4 7 3 6 6 13 10 20 1-1 0-1 1-1h0l-1-2v-2l4 8c1 3 2 5 4 8l2 5c1 0 1-1 2-1l1 4c3 6 5 12 7 18 1 2 1 5 3 7v2h2c0 1 0 1 1 1-1-4-3-9-5-12l1-1v-1c-1-2-1-3-1-4l21 57 29 75h-1c-1-2-2-5-3-8-1 3 1 4 1 6 0-1-1-2-1-3l-1-1c0-1-1-3-2-4 0-1 0-1-1-2h0 0l7 18c0 1 0 2-1 3v2c-3-4-4-6-9-7-1 0-4-1-6 0-1 1-3 2-4 2-3 4-5 8-7 12h-1v-2c0-1 0-1-1-2 1-2 2-4 3-5h-1l3-5h0-2c-1 3-2 5-4 7l-3 3c-4-8-7-17-10-25-1-4-3-7-5-10-1-2-1-5-3-7 0-1 1-1 2-3-2-5 0-10 2-15-1-1-1-1-1-2h0v-1l-2 1c0 2-1 7-3 8v-1l-1 2v-1l-1-2-1 1c2-6 3-13 4-19 1-2 1-4 1-7v-4-11-6-1l-2-4-1-3c-1-3-2-6-4-8l1-2-2-4c-1-2-2-2-4-4h-1l-4-2h-1l1 2c-1-1-2-1-3-2-2-1-4-3-6-5-1-2-3-2-4-3s-2-1-2-2h1l-1-1h0c0-1-1-1-1-2h0c-2-1-4-2-6-4-1 1-2 1-3 1v-1l1-1h0c-1 0-3 0-4 1 3-2 5-3 8-5 0-1 1-1 1-2h0c1-1 1-2 1-4l1-5v-3l-1-1v5l-1 2v1 1c-1 1-1 2-1 3l-1 1c-1 1-1 2-2 3-1 0-3 1-4 1s-3 1-4 0l-2-2-1 1c0 1 1 2 1 3h0c0 3 2 6 2 7h-1c0-1 0-1-1-1h-1s0-3-1-4c0 0-1 0-1-1-2 0-2 0-3 1-1 2 0 3 0 5l1 1 1 4 1 2v1h1v1c-1 1 0 2-1 4v3l-1-2-1 1v2h-1c1 1 1 2 0 3v-1l-1-2v-2c0-1 0-2-1-3v4-2c0-2 0-3-1-5-2-3-5-7-8-9 0-3 1-6 1-9 0-1 0-2-1-3v2c-1-1-1-2-1-2v-1c0-1-1-4-1-6-2-6-3-13-5-19l-1-1c0-3-2-6-3-9l-6-17-8-20c2 1 3 3 4 5s2 6 4 7 3 3 4 4c0-1 1-1 2-1l-1-4c-2-4-3-9-5-13l-1-3v-3l3 7c1-1 1-2 1-2-1-3-2-8-2-11l3-3z"></path><path d="M273 350s1 0 1 1-1 2-1 3h0v-1l-2 1 2-4z" class="j"></path><path d="M274 345l2 2-2 4c0-1-1-1-1-1 0-2 1-3 1-5z" class="b"></path><path d="M302 396c1-2 2-4 2-6h0c1 2 1 4 1 5v3c-1 0-1-1-2-1v-1h-1z" class="c"></path><path d="M281 336v-1h1v-1c2 2 2 3 3 5-1 0-2 1-2 1l-1-4h-1z" class="K"></path><path d="M228 237c0-1 0-1 1-3l3 9c-1 0-2-1-2-1l-2-5z" class="C"></path><path d="M274 345c1-3 2-8 4-11h0c1 1 1 2 1 3s0 2-1 3-2 5-2 7l-2-2z" class="Q"></path><path d="M197 199h1l3 6 3 6c0 1 0 1-1 2h0c0-1-1-1-1-1l-1-1-4-12z" class="F"></path><path d="M219 222c1 0 2 1 3 1 1 1 1 3 2 4 2 2 3 5 5 7-1 2-1 2-1 3l-2-3c-2-4-5-7-6-11l-1-1z" class="R"></path><path d="M197 199h0c-2-4-4-10-4-14h1c1 1 2 2 3 4l-3-3c1 3 3 6 4 9s2 6 4 9l-1 1-3-6h-1z" class="j"></path><path d="M302 396h1v1c1 0 1 1 2 1l-3 8-1 1v-1l-1 1v-2h0c1-1 2-3 2-4v-1l-2 3c-1 2-3 5-5 8h-1l3-5c2-3 4-6 5-10z" class="N"></path><path d="M234 248h0l1-16c0 2 1 4 1 6l1 8c0 2 0 5-1 7v-3l-1-1v5l-1 2v-1-7h0z" class="G"></path><path d="M245 255l3 1h0-2c0 1 1 2 2 3 1 2 1 4 2 6 2 3 4 6 5 9 0 1 1 1 1 2h0c2 0 3 1 5 1 0 2 1 4 2 6 1 3 2 5 3 8l-3-6h-2v-1c0-1-1-1-2-2v-2c0-1 0-1 1-2l-1-1h-3l-2-1-2-1h1 1c0-2-1-3-2-4 0-1-1-3-2-4l-4-10s-1-1-1-2h0z" class="S"></path><path d="M295 411l5-8 2-3v1c0 1-1 3-2 4h0v2l1-1v1l1-1-1 2c-3 4-5 8-7 12h-1v-2c0-1 0-1-1-2 1-2 2-4 3-5z" class="O"></path><path d="M190 175c3 4 7 6 11 8 1 1 3 2 5 3-1 1-2 2-3 2 1 3 5 5 7 8h-1l-18-17c-1-2-1-2-1-4z" class="I"></path><path d="M292 357l3 6c2 3 2 6 3 9 2 3 4 8 4 12v2 1h0l-1-1v2c-2-9-5-18-9-25 1-3 1-3 0-6z" class="C"></path><path d="M242 268c1-1 1-3 1-5 1-2 2-2 2-4 0-1 0-1 1-2l4 10c1 1 2 3 2 4 1 1 2 2 2 4h-1c-1-1-3-1-4-1-3-2-4-4-7-6z" class="M"></path><path d="M249 274v-3h-1l1-1 2 2c0-1 0-1 1-1 1 1 2 2 2 4h-1c-1-1-3-1-4-1z" class="I"></path><path d="M210 196l15 14s0 1 1 1l10 14c3 4 18 27 17 31h0l-16-26c-8-12-18-23-28-34h1z" class="g"></path><path d="M306 375l1 1 3 6c1 3 2 5 4 8l7 18c0 1 0 2-1 3v2c-3-4-4-6-9-7 1-1 1-2 0-3s-1-6-1-7c0-2 0-5-1-7 0-2 0-5-1-7h0l-2-7z" class="C"></path><path d="M262 289c0-1 0-2-1-4h0v-1 1h2l3 6 3 6c1 2 3 4 4 6l3 9c1 2 2 5 2 8 1 2 0 6-1 7h0c0-1 1-3 0-4s-1-2-2-3c0-2-1-4-1-6-1-3-3-7-5-10v2c1 2 1 4 2 7l-1 1v-1l-2-4-1-3c-1-3-2-6-4-8l1-2-2-4h1v-1l-1-2z" class="d"></path><path d="M263 291l2 2c0 2 2 4 3 6 0 1 1 4 1 5v2c-1-4-3-7-5-10l-2-4h1v-1z" class="S"></path><path d="M263 298l1-2c2 3 4 6 5 10 1 2 1 4 2 7l-1 1v-1l-2-4-1-3c-1-3-2-6-4-8z" class="B"></path><path d="M186 188l-1-3v-3l3 7c1-1 1-2 1-2 2 1 1 4 3 6 3 3 5 9 7 13 0 2 1 3 2 5v2h0l-1 1v2 5c-1-1-2-3-2-5h-1v2l-1-2c-1-1-1-3-2-4l-2-7-1-4c-2-4-3-9-5-13z" class="N"></path><path d="M198 216c0-1 1-2 2-3v-1h0l1 1-1 1v2 5c-1-1-2-3-2-5z" class="M"></path><path d="M186 188l-1-3v-3l3 7c1 1 1 1 1 2 2 3 2 8 3 11l5 14v2l-1-2c-1-1-1-3-2-4l-2-7-1-4c-2-4-3-9-5-13z" class="S"></path><path d="M236 238c1 1 1 2 2 3s2 2 2 4c2 3 3 7 5 10h0c0 1 1 2 1 2-1 1-1 1-1 2 0 2-1 2-2 4 0 2 0 4-1 5-1 0-1-1-1-1h-4v-1-2-1l-2 1-1 1v-1-2h0c1-1 1-2 1-4l1-5c1-2 1-5 1-7l-1-8z" class="d"></path><path d="M241 267v-1c1-2 0-2 0-3s1-1 2-2c0-1 0-2 1-3h0c0-1 0-2 1-3h0c0 1 1 2 1 2-1 1-1 1-1 2 0 2-1 2-2 4 0 2 0 4-1 5-1 0-1-1-1-1z" class="R"></path><path d="M237 246c0 1 0 1 1 2v2h1c1 1 1 2 1 3v1c0 1-1 4-1 5s-1 3-2 5h0v-1l-2 1-1 1v-1-2h0c1-1 1-2 1-4l1-5c1-2 1-5 1-7z" class="C"></path><path d="M237 258c0-1 1-3 2-5l1 1c0 1-1 4-1 5-1 0-1-1-2-1z" class="b"></path><path d="M237 258c1 0 1 1 2 1 0 1-1 3-2 5h0v-1l-2 1c1-2 2-4 2-6z" class="e"></path><path d="M269 304c2 3 4 7 5 10 0 2 1 4 1 6 1 1 1 2 2 3s0 3 0 4l-1 7-8 27-1 2v-1l-1-2-1 1c2-6 3-13 4-19 1-2 1-4 1-7v-4-11-6-1 1l1-1c-1-3-1-5-2-7v-2z" class="c"></path><path d="M269 304c2 3 4 7 5 10l-1 4v-2l-1-1h1v-1l-1-1v-2 3 3h-1v-4c-1-3-1-5-2-7v-2z" class="b"></path><path d="M273 319c2 2 2 5 2 8v6s-1 0-1 1v1h0v-1h-1v1c1-6 0-11 0-16z" class="j"></path><path d="M274 334v-9h1v2 6s-1 0-1 1z" class="L"></path><path d="M270 335h1c0 2 0 5-1 7 0 3-1 6-2 9l-1 11-1-2-1 1c2-6 3-13 4-19 1-2 1-4 1-7z" class="K"></path><path d="M271 313v4c1 5 0 10 0 14v4h-1v-4-11-6-1 1l1-1z" class="C"></path><path d="M274 314c0 2 1 4 1 6 1 1 1 2 2 3s0 3 0 4l-1 7-1-1v-6c0-3 0-6-2-8v-1l1-4z" class="G"></path><path d="M234 262v2 1l1-1 2-1v1 2 1h4s0 1 1 1c3 2 4 4 7 6 1 0 3 0 4 1h-1l2 1 2 1h3l1 1c-1 1-1 1-1 2v2c1 1 2 1 2 2v1h0c1 2 1 3 1 4l1 2v1h-1c-1-2-2-2-4-4h-1l-4-2h-1l1 2c-1-1-2-1-3-2-2-1-4-3-6-5-1-2-3-2-4-3s-2-1-2-2h1l-1-1h0c0-1-1-1-1-2h0c-2-1-4-2-6-4-1 1-2 1-3 1v-1l1-1h0c-1 0-3 0-4 1l8-5c0-1 1-1 1-2z" class="L"></path><path d="M247 278c1 0 2-1 3-1 1 1 2 1 3 2-2 0-4-1-6-1z" class="J"></path><path d="M256 277h3v1c0 1-1 2-1 2h-1c-1-1-1-2-1-3z" class="D"></path><path d="M256 285c-1-2-3-3-4-5h5 1v1c0 1 2 2 1 3h0c0-1-1-1-1-1h-2 0c0 1 0 1 1 2h-1z" class="C"></path><path d="M259 277l1 1c-1 1-1 1-1 2v2c1 1 2 1 2 2v1h0c1 2 1 3 1 4l1 2v1h-1c-1-2-2-2-4-4h-1 1c0-1-1-2-2-3h1c-1-1-1-1-1-2h0 2s1 0 1 1h0c1-1-1-2-1-3v-1s1-1 1-2v-1z" class="V"></path><path d="M257 285l5 4 1 2v1h-1c-1-2-2-2-4-4h-1 1c0-1-1-2-2-3h1z" class="D"></path><path d="M244 281c-1-2-3-2-4-3s-2-1-2-2h1l-1-1h0c1 0 3 1 4 1l2 2c3 2 5 5 8 7l1 1h-1l1 2c-1-1-2-1-3-2-2-1-4-3-6-5z" class="K"></path><path d="M241 267s0 1 1 1c3 2 4 4 7 6 1 0 3 0 4 1h-1l2 1v1s-1 1-2 0h0-2c-1 0-2 1-3 1v-1l-3 1-2-2v-1h-1v-1l-2-1 1-1c0-1 0-1-1-2l1-1-3-2h4z" class="T"></path><path d="M241 274h1v1c1 0 2 0 3 1l2 1-3 1-2-2v-1h-1v-1z" class="M"></path><path d="M240 269l1 1v2h1v1c1 0 2 1 3 1l1 1h1v1c-1 0-2-1-3-1l-2-1h-1l-2-1 1-1c0-1 0-1-1-2l1-1z" class="F"></path><path d="M241 267s0 1 1 1c3 2 4 4 7 6 1 0 3 0 4 1h-1c-4-1-8-3-11-5l-1-1-3-2h4z" class="K"></path><path d="M234 262v2 1l1-1 2-1v1 2 1l3 2-1 1c1 1 1 1 1 2l-1 1 2 1v1h1v1c-1 0-3-1-4-1 0-1-1-1-1-2h0c-2-1-4-2-6-4-1 1-2 1-3 1v-1l1-1h0c-1 0-3 0-4 1l8-5c0-1 1-1 1-2z" class="N"></path><path d="M237 273c-1 0 0 0-1-1h0c1 0 2 0 3 1l2 1v1h1v1c-1 0-3-1-4-1 0-1-1-1-1-2h0z" class="T"></path><path d="M233 267c1 0 1 0 2-1 1 1 1 2 2 3s1 1 0 2c-1 0-3-2-4-2v-2z" class="V"></path><path d="M234 262v2 1l1-1 2-1v1 2 1l3 2-1 1s-1-1-2-1c-1-1-1-2-2-3-1 1-1 1-2 1v-1c-1 1-2 1-3 2h-1 0c-1 0-3 0-4 1l8-5c0-1 1-1 1-2z" class="L"></path><path d="M202 204c-2-3-3-6-4-9s-3-6-4-9l3 3 22 33 1 1h-1v6l-2-1c-1 1-1 1 0 3l1 1c0 1-1 3-1 4h-1c0-2-1-6-2-7-1-2-2-3-4-4-1-1-3-2-4-1 0 1 0 1-1 2-2-3-3-5-4-9l-1-1v-2l1-1h0v-2h0l1 1s1 0 1 1h0c1-1 1-1 1-2l-3-6 1-1z" class="N"></path><path d="M211 219l1-1 2 2c0 1 2 1 2 3l-2-1c-1 0-2-2-3-3z" class="T"></path><path d="M202 204l3 6c1 1 1 2 1 3l-1 1v-1l-1-2-3-6 1-1z" class="O"></path><path d="M204 217h0c1-1 1-1 2-1h1 1 0l-1 1h0l-1 2 2 1c1 0 2-1 3-1 1 1 2 3 3 3l-1 1c-3-1-6-1-8-4v-1l-1-1z" class="R"></path><path d="M201 211h0l1 1 1 2h2c1 1 2 1 3 2h-1-1c-1 0-1 0-2 1h0c0 1-1 2-1 3l2 3 1 1c0 1 0 1-1 2-2-3-3-5-4-9l-1-1v-2l1-1h0v-2z" class="p"></path><path d="M201 213h0c1 2 1 2 0 4l-1-1v-2l1-1z" class="X"></path><path d="M204 217l1 1v1c2 3 5 3 8 4 1 2 3 3 4 5-1 1-1 1 0 3l1 1c0 1-1 3-1 4h-1c0-2-1-6-2-7-1-2-2-3-4-4-1-1-3-2-4-1l-1-1-2-3c0-1 1-2 1-3z" class="I"></path><path d="M217 228l2 1v-6h1c1 4 4 7 6 11l2 3 2 5s1 1 2 1l2 5h0v7 1 1 1c-1 1-1 2-1 3l-1 1c-1 1-1 2-2 3-1 0-3 1-4 1s-3 1-4 0l-2-2v-1c-1-2-2-3-2-4-1 0-1-1-1-1v-1c0-2-1-4-1-6s0-3 1-4v1-9h0 0l-1-3h1c0-1 1-3 1-4l-1-1c-1-2-1-2 0-3z" class="X"></path><path d="M218 233c0-1 0-2 1-3 3 1 4 6 5 9l1 2c0 1 0 1-1 1l-3-7c0 3 0 6-1 9h-1v-1c0-2 0-5 1-7 1-1 1-2 1-3h-3 0z" class="D"></path><path d="M225 241c0 1 1 1 1 2l4 9c1 3 1 6 0 9l1 1c1 0 1-1 2-1l-1 1c-1 1-1 2-2 3-1 0-3 1-4 1l1-1c1 0 1-1 2-2v-8c-1-5-3-8-5-13h0c1 0 1 0 1-1z" class="L"></path><path d="M217 239l1-6h0 3c0 1 0 2-1 3-1 2-1 5-1 7v1h1v5c-2 2-2 3-2 5l-1-5v-1-9z" class="C"></path><path d="M217 249c0-1 1-1 1-2 0 0 1-1 1-2v-2 1h1v5c-2 2-2 3-2 5l-1-5z" class="V"></path><path d="M216 251c0-2 0-3 1-4v1 1l1 5c0-2 0-3 2-5 1 5 2 9 4 13l3 3-1 1c-1 0-3 1-4 0l-2-2v-1c-1-2-2-3-2-4-1 0-1-1-1-1v-1c0-2-1-4-1-6z" class="K"></path><path d="M216 251c0-2 0-3 1-4v1 1l1 5c1 4 2 8 6 10v1h0c-1 0-3-1-4-2h0c-1-2-2-3-2-4-1 0-1-1-1-1v-1c0-2-1-4-1-6z" class="F"></path><path d="M217 228l2 1v-6h1c1 4 4 7 6 11l2 3 2 5s1 1 2 1l2 5h0v7 1 1 1c-1 1-1 2-1 3-1 0-1 1-2 1l-1-1c1-3 1-6 0-9l-4-9c0-1-1-1-1-2l-1-2c-1-3-2-8-5-9-1 1-1 2-1 3l-1 6h0 0l-1-3h1c0-1 1-3 1-4l-1-1c-1-2-1-2 0-3z" class="M"></path><path d="M230 242s1 1 2 1l2 5h0c-3-1-4-2-5-4 0-1 1-2 1-2z" class="N"></path><path d="M217 228l2 1v-6h1c1 4 4 7 6 11l-1-1c-1-1-2-2-3-4v2c1 2 3 5 2 8-1-3-2-8-5-9-1 1-1 2-1 3l-1 6h0 0l-1-3h1c0-1 1-3 1-4l-1-1c-1-2-1-2 0-3z" class="J"></path><defs><linearGradient id="j" x1="241.637" y1="253.958" x2="259.949" y2="248.829" xlink:href="#B"><stop offset="0" stop-color="#e2353b"></stop><stop offset="1" stop-color="#ff7570"></stop></linearGradient></defs><path fill="url(#j)" d="M226 211h0l2 2c1 2 2 3 4 4l1-1 3-1s0-1 1-1h0 0c2 1 3 4 4 7 3 6 6 13 10 20 1-1 0-1 1-1h0l-1-2v-2l4 8c1 3 2 5 4 8l2 5c1 0 1-1 2-1l1 4c3 6 5 12 7 18 1 2 1 5 3 7h-2c1 2 1 3 1 5v2 3l-3-6-7-14-10-19h0c1-4-14-27-17-31l-10-14z"></path><path d="M251 236l4 8c1 3 2 5 4 8-1 0-1 0-2 1 0-1-1-2-1-2l-5-10c1-1 0-1 1-1h0l-1-2v-2z" class="g"></path><path d="M263 275h1c1 1 1 1 1 2l1 1h0c1 2 1 4 3 6 0 2 1 3 2 4h0v-2c-1-1-1-1-1-2l-1-2-1-2h0l-1-1c0-1 0-1-1-2v-1c-1-1-1-3-2-4s-1-2-1-3l-1-1 1-1c3 8 7 15 10 23v2 3l-3-6-7-14z" class="U"></path><path d="M257 253c1-1 1-1 2-1l2 5c1 0 1-1 2-1l1 4c3 6 5 12 7 18 1 2 1 5 3 7h-2c1 2 1 3 1 5-3-8-7-15-10-23l-6-14z" class="Z"></path><path d="M263 256l1 4c0 1 0 1-1 1l-2-4c1 0 1-1 2-1z" class="o"></path><path d="M264 260c3 6 5 12 7 18 1 2 1 5 3 7h-2c-2-8-7-15-9-24 1 0 1 0 1-1z" class="k"></path><path d="M272 270l21 57 29 75h-1c-1-2-2-5-3-8-1 3 1 4 1 6 0-1-1-2-1-3l-1-1c0-1-1-3-2-4 0-1 0-1-1-2h0 0c-2-3-3-5-4-8l-3-6-1-1-11-25-13-33-9-22v-3-2c0-2 0-3-1-5h2v2h2c0 1 0 1 1 1-1-4-3-9-5-12l1-1v-1c-1-2-1-3-1-4z" class="a"></path><path d="M295 350h2 1l9 26-1-1-11-25z" class="AA"></path><path d="M284 314l14 36h-1-2l-13-33 2-3z" class="y"></path><path d="M274 285v2l6 17c2 3 3 7 4 10l-2 3-9-22v-3-2c0-2 0-3-1-5h2z" class="AC"></path><path d="M272 270l21 57 29 75h-1c-1-2-2-5-3-8 0-1-1-2-2-3l-3-9-3-8-17-42-16-44c-1-4-3-9-5-12l1-1v-1c-1-2-1-3-1-4z" class="o"></path><path d="M178 190c2 1 3 3 4 5s2 6 4 7 3 3 4 4c0-1 1-1 2-1l2 7c1 1 1 3 2 4l1 2v-2h1c0 2 1 4 2 5v-5l1 1c1 4 2 6 4 9 1-1 1-1 1-2 1-1 3 0 4 1 2 1 3 2 4 4 1 1 2 5 2 7l1 3h0 0v9-1c-1 1-1 2-1 4s1 4 1 6v1s0 1 1 1c0 1 1 2 2 4v1l-1 1c0 1 1 2 1 3h0c0 3 2 6 2 7h-1c0-1 0-1-1-1h-1s0-3-1-4c0 0-1 0-1-1-2 0-2 0-3 1-1 2 0 3 0 5l1 1 1 4 1 2v1h1v1c-1 1 0 2-1 4v3l-1-2-1 1v2h-1c1 1 1 2 0 3v-1l-1-2v-2c0-1 0-2-1-3v4-2c0-2 0-3-1-5-2-3-5-7-8-9 0-3 1-6 1-9 0-1 0-2-1-3v2c-1-1-1-2-1-2v-1c0-1-1-4-1-6-2-6-3-13-5-19l-1-1c0-3-2-6-3-9l-6-17-8-20z" class="w"></path><path d="M211 253c1 0 1-1 1-1l1 2 1 2h-1l-1 1-1-4z" class="S"></path><path d="M197 220l3 6v2l4 9v1c0-1-1-2-1-3l-2-5h-1l-1-3v-1l-1 1c-1-1-2-4-2-5s0-1 1-2z" class="I"></path><path d="M209 242l2 4 1 6s0 1-1 1h0l-4-9 2-2zm-12-26h1c0 2 1 4 2 5l2 6h-1v-1 2h-1v-2l-3-6-1-4 1 2v-2z" class="V"></path><path d="M202 227c3 4 5 10 7 15l-2 2c-1-2-2-5-3-7l-4-9h1v-2 1h1z" class="D"></path><path d="M212 257l1-1h1c1 3 3 6 5 9h0c0 1 1 2 1 3h0c0 3 2 6 2 7h-1c0-1 0-1-1-1h-1s0-3-1-4c0 0-1 0-1-1h0c-2-3-3-8-5-12z" class="C"></path><path d="M214 246v-1h0c1 0 1 1 1 1 1 1 0 2 1 3v2c0 2 1 4 1 6v1s0 1 1 1c0 1 1 2 2 4v1l-1 1h0c-2-3-4-6-5-9l-1-2-1-2-1-6c0 1 1 2 2 2 1-1 1-1 1-2h0z" class="L"></path><path d="M211 246c0 1 1 2 2 2 1-1 1-1 1-2h0c0 2 0 4 1 6v3l-2-1-1-2-1-6z" class="J"></path><path d="M200 216l1 1c1 4 2 6 4 9l5 12 4 8c0 1 0 1-1 2-1 0-2-1-2-2l-2-4c-2-5-4-11-7-15l-2-6v-5z" class="U"></path><path d="M206 224c1-1 3 0 4 1 2 1 3 2 4 4 1 1 2 5 2 7l1 3h0 0v9-1c-1 1-1 2-1 4v-2c-1-1 0-2-1-3 0 0 0-1-1-1h0v1h0l-4-8-5-12c1-1 1-1 1-2z" class="J"></path><path d="M206 224c1-1 3 0 4 1 2 1 3 2 4 4-1 0-1-1-2-1-1-1-2-2-2-3 0 4 0 8 1 12v1h-1l-5-12c1-1 1-1 1-2z" class="B"></path><path d="M212 228c1 0 1 1 2 1 1 1 2 5 2 7l1 3h0 0v9-1c-1 1-1 2-1 4v-2c-1-1 0-2-1-3 0 0 0-1-1-1h0v1h0l-4-8h1v-1h0l4 7v-8c0-1 0-3-1-4s-1-3-2-4z" class="N"></path><path d="M178 190c2 1 3 3 4 5s2 6 4 7 3 3 4 4c0-1 1-1 2-1l2 7c0-1-1-1-1-2-1 1-1 2-1 4 3 12 8 22 12 34l4 15c0 2 1 6 2 8s2 3 3 5h0l1-1 1 1 1 4 1 2v1h1v1c-1 1 0 2-1 4v3l-1-2-1 1v2h-1c1 1 1 2 0 3v-1l-1-2v-2c0-1 0-2-1-3v4-2c0-2 0-3-1-5-2-3-5-7-8-9 0-3 1-6 1-9 0-1 0-2-1-3v2c-1-1-1-2-1-2v-1c0-1-1-4-1-6-2-6-3-13-5-19l-1-1c0-3-2-6-3-9l-6-17-8-20z" class="C"></path><path d="M190 206c0-1 1-1 2-1l2 7c0-1-1-1-1-2-1 1-1 2-1 4l-2-8z" class="T"></path><defs><linearGradient id="k" x1="203.92" y1="270.325" x2="210.371" y2="270.542" xlink:href="#B"><stop offset="0" stop-color="#2d1716"></stop><stop offset="1" stop-color="#4a191b"></stop></linearGradient></defs><path fill="url(#k)" d="M203 263l1-1c0-4-3-8-3-13v1c1 2 2 4 3 7 1 6 2 13 6 19l2 4h-1l-2-1c1 2 2 3 2 5h0 0c-2-3-5-7-8-9 0-3 1-6 1-9 0-1 0-2-1-3z"></path><path d="M204 266v4c1 3 3 6 5 9 1 2 2 3 2 5h0 0c-2-3-5-7-8-9 0-3 1-6 1-9z" class="O"></path><path d="M210 271c1 2 2 3 3 5h0l1-1 1 1 1 4 1 2v1h1v1c-1 1 0 2-1 4v3l-1-2-1 1v2h-1c1 1 1 2 0 3v-1l-1-2v-2c0-1 0-2-1-3v4-2c0-2 0-3-1-5h0 0c0-2-1-3-2-5l2 1h1l-2-4v-5z" class="F"></path><path d="M216 280l1 2v1h1v1c-1 1 0 2-1 4v3l-1-2-1 1v2h-1c1 1 1 2 0 3v-1c0-2-1-5 0-7l1 1h1c-1-3 0-6 0-8z" class="G"></path><path d="M209 279l2 1h1c1 2 2 5 2 7-1 2 0 5 0 7l-1-2v-2c0-1 0-2-1-3v4-2c0-2 0-3-1-5h0 0c0-2-1-3-2-5z" class="C"></path><path d="M281 336h1l1 4s1-1 2-1l1 4 2 5 4 9c1 3 1 3 0 6 4 7 7 16 9 25 0 3 0 5-1 7-1 4-3 7-5 11-1 3-2 5-4 7l-3 3c-4-8-7-17-10-25-1-4-3-7-5-10-1-2-1-5-3-7 0-1 1-1 2-3-2-5 0-10 2-15l4-10 3-10z" class="O"></path><path d="M281 336h1l1 4s1-1 2-1l1 4 2 5 4 9c1 3 1 3 0 6 4 7 7 16 9 25 0 3 0 5-1 7-1 1-1 1-1 2-1 1-1 2-1 3 0-1-1-4-2-4 0-1 0 0-1-1v-1c0-1 0-1-1-2l-1-4s0-1-1-1v-2c-1-3-3-6-4-8 0-1 0-2-1-3v-1c-1-1-1-2-2-4v-1c-1-1-1-2-1-2l-3-5-1-3c0-1 0-1-1-1h0v1 1-1c-1-1-1-2-1-3s-1-2-1-4c0-1 0-1 1-1h0c0 1 0 2 1 3v2-1-4l1-1-1-1c0-1 0 0 1-1v1h0l1-1h0v-1s1-1 1-2l1 1h1l-1-3-1-1c0-1 0-1-1-2h0c-1 2-2 5-3 7h0 0l3-10z" class="L"></path><path d="M283 340s1-1 2-1l1 4c-1 0-1 0-2 1h0l-1-4z" class="D"></path><path d="M284 344h0c1-1 1-1 2-1l2 5h-2l-2-4z" class="R"></path><path d="M286 348h2l4 9c1 3 1 3 0 6l-6-15z" class="Q"></path><path d="M234 256l1-2v-5l1 1v3l-1 5c0 2 0 3-1 4h0c0 1-1 1-1 2l-8 5c1-1 3-1 4-1h0l-1 1v1c1 0 2 0 3-1 2 2 4 3 6 4h0c0 1 1 1 1 2h0l1 1h-1c0 1 1 1 2 2s3 1 4 3c2 2 4 4 6 5 1 1 2 1 3 2l-1-2h1l4 2h1c2 2 3 2 4 4l2 4-1 2c2 2 3 5 4 8l1 3 2 4v1 6 11 4c0 3 0 5-1 7-1 6-2 13-4 19l1-1 1 2v1l1-2v1c2-1 3-6 3-8l2-1v1h0c0 1 0 1 1 2-2 5-4 10-2 15-1 2-2 2-2 3 2 2 2 5 3 7 2 3 4 6 5 10 3 8 6 17 10 25l3-3c2-2 3-4 4-7h2 0l-3 5h1c-1 1-2 3-3 5 1 1 1 1 1 2v2h1c2-4 4-8 7-12 1 0 3-1 4-2 2-1 5 0 6 0 5 1 6 3 9 7v-2c1-1 1-2 1-3l4 16 1 3v3c0 4 1 9 0 13 0 5-1 10-3 14 0 1 0 2 1 2l-3 6-2 2-1-1-3 4-9 9 1 1-3 3c-1 1-2 2-4 2v1 2h0l1 3 1 1v2h0l1 1c0 1 0 1 1 2l2 7c0 1 1 2 1 3 1 1 1 1 1 2h0v3l-1-2-1-1v-1h-1v1l14 41v3h0c0 2 1 3 1 4 1 5 3 10 4 15 2 4 4 7 4 12v1l1 2c0 1 0 2 1 4 0 0-1 1-1 2l2 7-1-1h0c-1-1-1-1-2-1-2-4-3-8-4-12 0-1-1-1-1-1h-1 0c2 3 2 6 3 8s2 3 1 5v-1-1c-1-1-1-3-2-4v-1l-1-1v-1h-1s0-1-1-2v-2l1 1c0-1-1-3-2-4l-2-6v-1c0-1-1-2-1-3 0 0-1-1-1-2l-2-6c0-1-1-2-1-3l-3-9c-1-5-3-9-4-13l-10-28-17-49-4-13-7-17-8-22-5-14-7-22-10-28c0-3-1-6-2-8l-10-25v-1l-5-13c-1-2-2-5-4-8v-3c1-2 0-3 1-4v-1h-1v-1l-1-2-1-4-1-1c0-2-1-3 0-5 1-1 1-1 3-1 0 1 1 1 1 1 1 1 1 4 1 4h1c1 0 1 0 1 1h1c0-1-2-4-2-7h0c0-1-1-2-1-3l1-1 2 2c1 1 3 0 4 0s3-1 4-1c1-1 1-2 2-3l1-1c0-1 0-2 1-3v-1-1z" class="O"></path><path d="M326 581c1 1 2 2 2 3v1h0l1 2c-2-2-3-3-3-6z" class="b"></path><path d="M324 572c2 4 4 7 4 12v1h0v-1c0-1-1-2-2-3l-2-9z" class="G"></path><g class="B"><path d="M296 485c1-1 1-1 2-1h0c0 1 0 2 1 3h0c0 1 0 1 1 2v1l1 1 1 1v2h0l1 1c0 1 0 1 1 2l2 7c0 1 1 2 1 3 1 1 1 1 1 2h0v3l-1-2-1-1v-1h-1v1c-2-3-3-7-4-10-1-4-3-8-4-12l-1-2z"></path><path d="M296 485c1-1 1-1 2-1h0v3h-1l-1-2z"></path></g><path d="M255 382c2 2 2 2 4 2l3 6c1 2 2 5 3 8l12 31c1 4 3 8 4 11-1 2-1 3-1 4h0 1c3 6 6 13 7 19-1-1-1-2-2-4 0-1 0-1-1-2v-1-1c-1-1 0-1-1-1v-1c-1-1-1-3-2-4 0-1-1-2-1-4-1 1-2 1-3 2h0v-1c0-1 0-2 1-2v-1-1c1-2 1-2 0-4h0c0-1 0-2-1-3v-1c-1-1-1-2-2-3 0-2-1-4-1-6-1-1-1-3-2-4 0-1-1-2-1-3l-2-5-1-4-2-3v-1l-2-4c-1-2-2-5-2-7-1-1-1-3-2-4 0-1 0-1-1-2-1 0-4-2-4-3-1 0 0-1-1-1 1 1 1 2 0 3h0v-1c0-1-1-2 0-4z" class="b"></path><path d="M255 387l1 2v1h0c0 1 0 1 1 2l1 5s1 1 1 2l1 3c0 1 1 1 1 2h0l1 1v3l4 9v1c1 2 1 4 2 5 0 2 1 3 2 5v1h0v2c1 1 1 0 1 1s1 2 1 3l1 2v1l1 1v1 1h1c0 1 0 1-1 2-1-1-1-2-1-3-1-1-1-1-1-2l-1-3-1-1v-1l-1-1v-2l-1-1v-1c0-1 0-1-1-2v-2c-1-1-1-1-1-2-1-1-1-2-1-3-1-2-2-4-2-6 0 0 0-1-1-1l-1-3c0-1 0-2-1-3 0-2-1-4-2-6l-1-4c-1-2-2-4-2-6l-4-12c0-3-1-5-1-8h0v3l1 1c0 1 0 1 1 2v1h1 0v-2h-1c0-1 1-4 0-5l-1-1c0-1 1-1 0-2v-1l1-1c0 2 1 4 1 6v4c0 1 1 3 2 4v3c-1 2 0 3 0 4v1z" class="G"></path><path d="M246 348h0l1-1 6 16c1 3 1 6 2 9v1h1c-1-2 1-8 2-10 0-2 1-3 2-5h1c-1 2-2 3-1 5v-1c1 1 0 4 1 6v1h0 1c-1 2-1 4 0 7h0c1 2 2 5 2 7l6 15-1 1 1 4 1 1v1l2 4h0c0 2 1 3 1 4s0 0 1 1v2s1 1 1 2v1 1c1 1 2 2 2 4 0 1 1 3 2 4v2c1 1 1 2 1 3 1 1 1 2 2 4v1 3 2l1 1v1l1 1v1c0 1 1 1 1 2l1 3v1l1 2c0 1 0 2 1 3 0 1 0 2 1 3v1h0c1 1 1 2 1 3l1 1v1 1c1 0 1 2 1 3h-1l-1-1-3-7c-1-6-4-13-7-19h-1 0c0-1 0-2 1-4-1-3-3-7-4-11l-12-31c-1-3-2-6-3-8l-3-6c-2 0-2 0-4-2v-3c-1-1-2-3-2-4v-4c0-2-1-4-1-6s-2-6-3-8l-3-9z" class="B"></path><path d="M255 379c2 1 3 3 4 5-2 0-2 0-4-2v-3z" class="D"></path><path d="M260 358h1c-1 2-2 3-1 5v-1c1 1 0 4 1 6v1c-1 2-2 6-3 8h-1v-1-2c0-1-1-1-1-1-1-2 1-8 2-10 0-2 1-3 2-5z" class="J"></path><path d="M262 376c1 2 2 5 2 7l6 15-1 1 1 4 1 1v1l2 4h0c0 2 1 3 1 4s0 0 1 1v2s1 1 1 2v1 1c1 1 2 2 2 4 0 1 1 3 2 4v2c1 1 1 2 1 3 1 1 1 2 2 4v1 3c-1-1-1-2-2-4v-2c0-1 0-1-1-2v-2c-1-2-2-3-2-5l-1-2c0-1-1-2-2-4 0-2-1-4-2-6v-1-1l-1-1c-1-2-1-4-2-6v-1c-1-1-1-2-2-3v-2l-1-1v-1c0-1-1-1-1-2l-1-2v-2l-2-3c-1-4-2-9-1-12z" class="K"></path><path d="M234 256l1-2v-5l1 1v3l-1 5c0 2 0 3-1 4h0c0 1-1 1-1 2l-8 5c1-1 3-1 4-1h0l-1 1v1c1 0 2 0 3-1 2 2 4 3 6 4h0l-2 2h1l1 1c-1 1-1 1-1 2-3 2-6 6-6 10h0c0 1 0 1 1 2h1l1 5h0l-1 1v1l1 1v2c0 2 1 3 2 5-1 0-1 1-1 2 1 0 1 1 1 1l-1 1c2 1 2 3 3 5l1 3 1 3s1 2 1 3 0 2 1 2v3c2 4 2 8 4 12 0 3 1 5 2 7l-1 1h0l3 9v2c-1-1-1-1-1-2h-1v-2l-1-1-1-4-1-2c0-1-1-2-1-3s-1-1-1-2h-1c-1-1-1-2-1-3-1-2-2-6-3-8v-1c-1-4-3-8-5-11h0 0c-1 0-1-1-1-1-1-1-1-1-1-2-1 2 0 3 1 5 0 1 1 3 1 4 1 3 5 8 4 12l-10-25v-1l-5-13c-1-2-2-5-4-8v-3c1-2 0-3 1-4v-1h-1v-1l-1-2-1-4-1-1c0-2-1-3 0-5 1-1 1-1 3-1 0 1 1 1 1 1 1 1 1 4 1 4h1c1 0 1 0 1 1h1c0-1-2-4-2-7h0c0-1-1-2-1-3l1-1 2 2c1 1 3 0 4 0s3-1 4-1c1-1 1-2 2-3l1-1c0-1 0-2 1-3v-1-1z" class="R"></path><path d="M230 317c-1-1-1-3-1-4h0 1 0l2 7h0 0c-1 0-1-1-1-1-1-1-1-1-1-2z" class="J"></path><path d="M226 297l3 1 1 2h0c0 3 0 6 2 8h0l-1 1c-2-3-2-6-4-9 0-1 0-2-1-3z" class="c"></path><path d="M224 279l1 2c0 1 1 1 2 1v-1c1 2 2 5 2 8 1 0 1 1 1 2h-1c0-2-1-3-2-4h-1c-1-2-2-5-2-8z" class="T"></path><path d="M229 298h2 0c0 1 1 2 2 2 0 2 1 3 2 5-1 0-1 1-1 2 1 0 1 1 1 1l-1 1c2 1 2 3 3 5l-2 2-3-8h0c-2-2-2-5-2-8h0l-1-2z" class="B"></path><path d="M229 298h2 0c0 1 1 2 2 2 0 2 1 3 2 5-1 0-1 1-1 2 1 0 1 1 1 1l-1 1c-1-3-2-6-4-9h0l-1-2z" class="N"></path><path d="M226 287h1c1 1 2 2 2 4h1c0-1 0-2-1-2l1-1c0 1 0 1 1 2h1l1 5h0l-1 1v1l1 1v2c-1 0-2-1-2-2h0-2l-3-1h0c-1-1-1-3 0-5 0-1-1-3 0-4h0v-1z" class="F"></path><path d="M226 292c0-1-1-3 0-4h0c1 2 2 6 4 7v-1c0-1 0-2 1-3v4c0 1-2 1-3 2-1-2-2-3-2-5z" class="p"></path><path d="M234 256l1-2v-5l1 1v3l-1 5c0 2 0 3-1 4h0c0 1-1 1-1 2l-8 5h-1-1c0 1 1 3 1 4l3 8v1c-1 0-2 0-2-1l-1-2c-1 0-1-2-1-3l-3-8c0-1-1-2-1-3l1-1 2 2c1 1 3 0 4 0s3-1 4-1c1-1 1-2 2-3l1-1c0-1 0-2 1-3v-1-1z" class="X"></path><path d="M214 275c0-2-1-3 0-5 1-1 1-1 3-1 0 1 1 1 1 1 1 1 1 4 1 4l4 10c0 2 1 3 1 5h-1l1 2c-1 2-2 1-3 2l-1-1c0-1-1-2-1-3-1-1-1-3-1-5v-1h-1v-1l-1-2-1-4-1-1z" class="B"></path><path d="M215 276c0-1 1-2 1-2 1 0 2 0 2 1 1 0 1 1 1 1v1 1 1c0 2 0 3-1 4h-1v-1l-1-2-1-4z" class="H"></path><path d="M217 282c0-1-1-3 0-4h1l1 1c0 2 0 3-1 4h-1v-1z" class="B"></path><path d="M219 278c1 4 3 7 4 11l1 2c-1 2-2 1-3 2l-1-1c0-1-1-2-1-3-1-1-1-3-1-5v-1c1-1 1-2 1-4v-1z" class="F"></path><path d="M218 284c0 2 0 4 1 5 0 1 1 2 1 3l1 1c1-1 2 0 3-2 1 4 1 9 3 13 1 3 3 5 3 9h0-1 0c0 1 0 3 1 4-1 2 0 3 1 5 0 1 1 3 1 4 1 3 5 8 4 12l-10-25v-1l-5-13c-1-2-2-5-4-8v-3c1-2 0-3 1-4z" class="L"></path><path d="M221 299c1 1 2 1 3 2 1 2 1 3 1 5 0 1 1 2 1 4v1 2-1l-5-13z" class="e"></path><path d="M225 269c1-1 3-1 4-1h0l-1 1v1c1 0 2 0 3-1 2 2 4 3 6 4h0l-2 2h1l1 1c-1 1-1 1-1 2-3 2-6 6-6 10h0l-1 1c0-3-1-6-2-8l-3-8c0-1-1-3-1-4h1 1z" class="H"></path><path d="M232 273c1 0 2 1 3 2-1 0-3 1-4 1v-1c1-1 1-1 1-2z" class="j"></path><path d="M229 271h1l2 2c0 1 0 1-1 2l-2-4z" class="L"></path><path d="M225 269c1-1 3-1 4-1h0l-1 1v1c1 0 2 0 3-1 2 2 4 3 6 4h0l-2 2h0c-1-1-2-2-3-2l-2-2h-1-1v-1h-2c-1 0-1-1-2-1h1z" class="D"></path><path d="M232 308l3 8 2-2 1 3 1 3s1 2 1 3 0 2 1 2v3c2 4 2 8 4 12 0 3 1 5 2 7l-1 1h0l3 9v2c-1-1-1-1-1-2h-1v-2l-1-1-1-4-1-2c0-1-1-2-1-3s-1-1-1-2l-5-14c-1-5-4-10-5-15-1-1-2-3-1-5l1-1z" class="F"></path><path d="M237 314l1 3c-1 1-1 2-2 2l-1-3 2-2z" class="E"></path><path d="M238 317l1 3s1 2 1 3 0 2 1 2v3c-1-1-1-2-2-2-1-1-2-5-3-7 1 0 1-1 2-2z" class="C"></path><path d="M239 326c1 0 1 1 2 2 2 4 2 8 4 12 0 3 1 5 2 7l-1 1h0c-1-1-1-4-2-5-2-6-4-11-5-17z" class="K"></path><path d="M271 354l2-1v1h0c0 1 0 1 1 2-2 5-4 10-2 15-1 2-2 2-2 3 2 2 2 5 3 7 2 3 4 6 5 10 3 8 6 17 10 25l3-3c2-2 3-4 4-7h2 0l-3 5h1c-1 1-2 3-3 5 1 1 1 1 1 2v2h1v1l-3 3c-3 4-5 9-5 14 1 5 4 8 5 13 5 10 9 20 12 31h0l3-3 1 1-3 3c-1 1-2 2-4 2v1 2h0l1 3-1-1v-1c-1-1-1-1-1-2h0c-1-1-1-2-1-3h0c-1 0-1 0-2 1 0-1-1-4-2-6l-3-9 1 1h1c0-1 0-3-1-3v-1-1l-1-1c0-1 0-2-1-3h0v-1c-1-1-1-2-1-3-1-1-1-2-1-3l-1-2v-1l-1-3c0-1-1-1-1-2v-1l-1-1v-1l-1-1v-2-3-1c-1-2-1-3-2-4 0-1 0-2-1-3v-2c-1-1-2-3-2-4 0-2-1-3-2-4v-1-1c0-1-1-2-1-2v-2c-1-1-1 0-1-1s-1-2-1-4h0l-2-4v-1l-1-1-1-4 1-1-6-15c0-2-1-5-2-7h0c-1-3-1-5 0-7h-1l4-8 1-1 1 2v1l1-2v1c2-1 3-6 3-8z" class="L"></path><path d="M292 416c1 1 1 1 1 2v2h1v1l-3 3c-3 4-5 9-5 14-1-2-1-4 0-7v-2l2-3v-1c1-2 3-5 3-7 1-1 1-1 1-2z" class="D"></path><path d="M283 441v-3-1c-1-2-1-3-2-4 0-1 0-2-1-3v-2c-1-1-2-3-2-4 0-2-1-3-2-4v-1-1c0-1-1-2-1-2v-2c-1-1-1 0-1-1s-1-2-1-4h0l-2-4v-1l-1-1-1-4 1-1 22 61 6 17c1 2 1 4 2 7 1 0 1 1 0 2v1 2h0l1 3-1-1v-1c-1-1-1-1-1-2h0c-1-1-1-2-1-3h0c-1 0-1 0-2 1 0-1-1-4-2-6l-3-9 1 1h1c0-1 0-3-1-3v-1-1l-1-1c0-1 0-2-1-3h0v-1c-1-1-1-2-1-3-1-1-1-2-1-3l-1-2v-1l-1-3c0-1-1-1-1-2v-1l-1-1v-1l-1-1v-2z" class="H"></path><path d="M294 479l1-1h1l1 3v1 1c1 0 1 0 1 1-1 0-1 0-2 1 0-1-1-4-2-6z" class="B"></path><defs><linearGradient id="l" x1="269.627" y1="396.224" x2="281.999" y2="390.362" xlink:href="#B"><stop offset="0" stop-color="#101314"></stop><stop offset="1" stop-color="#2a1716"></stop></linearGradient></defs><path fill="url(#l)" d="M271 354l2-1v1h0c0 1 0 1 1 2-2 5-4 10-2 15-1 2-2 2-2 3 2 2 2 5 3 7 2 3 4 6 5 10 3 8 6 17 10 25l3-3c2-2 3-4 4-7h2 0l-3 5c-5 5-7 13-10 19-1-2-1-4-1-5-2-5-4-9-5-14l-12-31-2-8c-1-1-1-2-2-3h-1l4-8 1-1 1 2v1l1-2v1c2-1 3-6 3-8z"></path><path d="M271 354l2-1v1l-3 13c-1-2-2-3-3-4l1-2v1c2-1 3-6 3-8z" class="e"></path><path d="M270 374c2 2 2 5 3 7 2 3 4 6 5 10 3 8 6 17 10 25l3-3c-1 2-3 3-3 5 0 1 0 1-1 1h0c-1-4-4-9-6-13l-5-14c-2-5-4-9-6-13v-5z" class="Q"></path><path d="M301 408c1 0 3-1 4-2 2-1 5 0 6 0 5 1 6 3 9 7v-2c1-1 1-2 1-3l4 16 1 3v3c0 4 1 9 0 13 0 5-1 10-3 14 0 1 0 2 1 2l-3 6-2 2-1-1-3 4-9 9-3 3h0c-3-11-7-21-12-31-1-5-4-8-5-13 0-5 2-10 5-14l3-3v-1c2-4 4-8 7-12z" class="B"></path><path d="M323 457c0 1 0 2 1 2l-3 6-2 2-1-1 5-9z" class="AE"></path><path d="M321 408l4 16 1 3v3c-3-5-4-12-6-17v-2c1-1 1-2 1-3z" class="L"></path><path d="M311 469l-4 6v-1c-4-8-7-17-10-26l-2-8c-1-1-1-2-1-2v-1h1l1-1 5 11c0 3 1 6 3 9 0 2 1 3 0 6v3c0 1 1 2 1 2 1 2 2 5 2 7h1v-1c0-1 1-1 1-2l1-1 1-1z" class="S"></path><path d="M299 432l3-2c-1-2-1-4 0-6 2-4 7-5 10-6 6 6 10 20 9 28-1 6-3 11-5 16-1 2-3 5-5 7l-1 1-1 1c0 1-1 1-1 2v1h-1c0-2-1-5-2-7 0 0-1-1-1-2v-3c1-3 0-4 0-6-2-3-3-6-3-9l-5-11c1-2 2-3 3-4z" class="G"></path><path d="M304 443l1 4v1 1 1-1c-1-2-2-4-2-6h1z" class="D"></path><path d="M303 443l-2-7h0l1 1v-2c0 3 1 5 2 8h-1z" class="C"></path><path d="M296 436c1-2 2-3 3-4v1c0 1-1 2-2 3 0 1 1 5 2 6l1 1v-1-1h1 0l1 4c1 1 2 5 2 7v-1-1l-1 2h0v1c1 1 1 2 1 3-2-3-3-6-3-9l-5-11z" class="D"></path><path d="M301 441l1 4h-1 0c0-2-1-2 0-4z" class="C"></path><path d="M237 273c0 1 1 1 1 2h0l1 1h-1c0 1 1 1 2 2s3 1 4 3c2 2 4 4 6 5 1 1 2 1 3 2l-1-2h1l4 2h1c2 2 3 2 4 4l2 4-1 2c2 2 3 5 4 8l1 3 2 4v1 6 11 4c0 3 0 5-1 7-1 6-2 13-4 19l-4 8h0v-1c-1-2 0-5-1-6v1c-1-2 0-3 1-5h-1c-1 2-2 3-2 5-1 2-3 8-2 10h-1v-1c-1-3-1-6-2-9l-6-16c-1-2-2-4-2-7-2-4-2-8-4-12v-3c-1 0-1-1-1-2s-1-3-1-3l-1-3-1-3c-1-2-1-4-3-5l1-1s0-1-1-1c0-1 0-2 1-2-1-2-2-3-2-5v-2l-1-1v-1l1-1h0l-1-5h-1c-1-1-1-1-1-2h0c0-4 3-8 6-10 0-1 0-1 1-2l-1-1h-1l2-2z" class="I"></path><path d="M270 314v6h-1l-1-6h0 1 1z" class="D"></path><path d="M237 273c0 1 1 1 1 2h0l1 1h-1c0 1 1 1 2 2s3 1 4 3c-1 0-3 0-4 1 0 1 0 1 1 2h0c0 2 1 5 2 7l-4-5v-2c0-2 0-3-2-4 0-1-1-2-1-2 0-1 0-1 1-2l-1-1h-1l2-2z" class="d"></path><path d="M255 294c3 1 5 3 7 6 1 1 1 2 2 4 1 1 2 1 3 2l1 3 2 4v1h-1-1 0l-1-1c-4-7-7-13-12-19z" class="D"></path><path d="M267 313h1c-1-2-1-2-1-4h1l2 4v1h-1-1 0l-1-1z" class="b"></path><path d="M269 320h1v11 4c0 3 0 5-1 7v-3h-1 0c-1 1-1 2-2 3v2 1c-1 2-2 4-2 5h-1v-2h1c-1-1-1-1 0-2v-1h0v-1-1-2c1 0 1-1 1-2h0v-2c0-1 0-1 1-1v-2c1-1 0-1 1-2l-1-2c1-1 1-2 1-3l1-6c1 0 1 0 1-1h0z" class="M"></path><path d="M268 339c0-1 0-3 1-4 0-1 1-2 1-4v4c0 3 0 5-1 7v-3h-1 0z" class="T"></path><path d="M268 339h0 1v3c-1 6-2 13-4 19l-4 8h0v-1c-1-2 0-5-1-6v1c-1-2 0-3 1-5h-1v-1h1c1-1 1-2 2-3v-1-1l1-1v-1c0-1 1-3 2-5v-1-2c1-1 1-2 2-3z" class="O"></path><path d="M241 284c-1-1-1-1-1-2 1-1 3-1 4-1 2 2 4 4 6 5 1 1 2 1 3 2l-1-2h1l4 2h1c2 2 3 2 4 4l2 4-1 2c2 2 3 5 4 8-1-1-2-1-3-2-1-2-1-3-2-4-2-3-4-5-7-6-3-2-7-4-9-6l-5-5v1z" class="C"></path><path d="M253 286l4 2h1c2 2 3 2 4 4l2 4-1 2c2 2 3 5 4 8-1-1-2-1-3-2-1-2-1-3-2-4-1-3-3-5-5-7h-2c0-1 1-1 2-1-1-1-1-2-1-3h0c-1 0-2-1-3-1l-1-2h1z" class="V"></path><path d="M253 286l4 2h1c2 2 3 2 4 4l2 4-1 2c-2-4-4-6-7-9h0c-1 0-2-1-3-1l-1-2h1z" class="E"></path><path d="M236 278s1 1 1 2c2 1 2 2 2 4v2h-1c-1 5 1 12 3 15 2 4 4 6 6 9-1-1-3-1-3-2h0c0-1 0-1-1-1h-1l-1-1h-2 0 0v-1l-2 1v2h-1l-1-3c-1-2-2-3-2-5v-2l-1-1v-1l1-1h0l-1-5h-1c-1-1-1-1-1-2h0c0-4 3-8 6-10z" class="S"></path><path d="M237 280c2 1 2 2 2 4v2h-1l-1-1c-1 1-1 4-2 6l-1-1c0-2 0-4 1-6l-1-1h0c1 0 1-1 2-2v1c1-1 1-1 1-2z" class="D"></path><path d="M237 280c2 1 2 2 2 4h-2l-1-1v-1c1-1 1-1 1-2z" class="F"></path><path d="M236 278s1 1 1 2 0 1-1 2v-1c-1 1-1 2-2 2h0c-1 3-1 5-2 7h-1c-1-1-1-1-1-2h0c0-4 3-8 6-10z" class="J"></path><path d="M233 300v-2l-1-1v-1l1-1c0 2 1 3 2 4s5 4 4 6l-2 1v2h-1l-1-3c-1-2-2-3-2-5z" class="T"></path><path d="M234 309l1-1s0-1-1-1c0-1 0-2 1-2l1 3h1v-2l2-1v1h0 0 2l1 1h1c1 0 1 0 1 1h0c-2-1-3-1-5-1v1c1 3 3 6 4 8 3 3 5 9 7 13l6 15c1 2 2 4 3 7 0 1 1 2 1 3 1-1 1-3 2-5 1 3-2 5-2 8v1c-1 2-2 3-2 5-1 2-3 8-2 10h-1v-1c-1-3-1-6-2-9l-6-16c-1-2-2-4-2-7-2-4-2-8-4-12v-3c-1 0-1-1-1-2s-1-3-1-3l-1-3-1-3c-1-2-1-4-3-5z" class="AG"></path><path d="M247 339c0-1 0-1 1-2l2 6c-1 1-1 1-2 1l-1-5z" class="J"></path><path d="M254 343h0c1 0 1 1 1 1h1c1 2 2 4 3 7-2-3-4-5-5-8z" class="C"></path><path d="M250 343l3 7c-1 0-1 1-2 1l-3-7c1 0 1 0 2-1z" class="T"></path><path d="M234 309l1-1s0-1-1-1c0-1 0-2 1-2l1 3h1v-2c0 3 2 7 3 11-1 1-1 1-1 3l-1-3-1-3c-1-2-1-4-3-5z" class="L"></path><path d="M240 317c3 6 6 13 8 20-1 1-1 1-1 2l-3-6h0c0-2-1-3-1-4l-3-6c0-1-1-3-1-3 0-2 0-2 1-3z" class="T"></path><path d="M243 316c3 3 5 9 7 13l6 15h-1s0-1-1-1h0v-1l-7-16c-2-3-3-6-4-10z" class="D"></path><path d="M240 323l3 6c0 1 1 2 1 4h0l3 6 1 5 3 7c1 0 1-1 2-1 2 4 3 9 4 13h1c-1 2-3 8-2 10h-1v-1c-1-3-1-6-2-9l-6-16c-1-2-2-4-2-7-2-4-2-8-4-12v-3c-1 0-1-1-1-2z" class="Q"></path><path d="M253 350c2 4 3 9 4 13 0 1 0 1-1 1-2-4-3-9-5-13 1 0 1-1 2-1z" class="F"></path><defs><linearGradient id="m" x1="307.258" y1="554.105" x2="345.962" y2="463.181" xlink:href="#B"><stop offset="0" stop-color="#0b1212"></stop><stop offset="1" stop-color="#2e1717"></stop></linearGradient></defs><path fill="url(#m)" d="M314 390h0 0c1 1 1 1 1 2 1 1 2 3 2 4l1 1c0 1 1 2 1 3 0-2-2-3-1-6l3 8h1c2 6 4 11 8 17l2 2c1 0 1 1 1 1h1v-1l1-2 1 1 1-1h0c2-1 5-4 6-6l3-4v-1h0l1 1 2-4 1 1c-1 1-2 2-1 3l1-1v-1c1-1 2-2 3-4 2-3 3-6 6-9h1l-2 11h3l3-1h4 0c5 5 7 10 9 15 2 1 2 0 3 2v1h0c1 2 2 4 3 5 3 7 2 12 0 19 0-1 1-1 1-2v1 2c-1 3-2 8-3 11v2l2-2c-1-1 1-3 1-5v-1l1-1v-2l1 1-1 5c0 1-1 1-1 2h0c0 1 0 2-1 3l-1 3v1l-1 3c0 1-1 2-1 2 0 1 0 1-1 2l-1 4c0 1-1 2-2 4l-1 4c0 1-1 2-1 4-1 0-1 1-1 2v1h0 1l-5 14h-2l-1 2v1s0 1-1 2v2h0c-1 0-1 1-1 1 0 1-1 2-1 3v1 1l-1 1v1 1c-1 0-1 1-1 1v1c-1 1-1 1-1 2h0c0 1-1 3-1 4s0 1-1 2h0c0 1 0 2-1 3l-2 5-2 4 2 2-4 13c0 1-1 3-2 4v6h0l-3 9v4c-1 1-1 3-1 4l1 1-1 3v3c0 1-1 3-1 5-1 2-1 6-3 9-1 1-1 2-1 3l-1 1-1-1h0v-1l-2 3h-1 0v-2s0 1-1 1c-1-2-1-4-2-6-1-1-1-2-1-3l-2-7c0-1 1-2 1-2-1-2-1-3-1-4l-1-2v-1c0-5-2-8-4-12-1-5-3-10-4-15 0-1-1-2-1-4h0v-3l-14-41v-1h1v1l1 1 1 2v-3h0c0-1 0-1-1-2 0-1-1-2-1-3l-2-7c-1-1-1-1-1-2l-1-1h0v-2l-1-1-1-3h0v-2-1c2 0 3-1 4-2l3-3-1-1 9-9 3-4 1 1 2-2 3-6c-1 0-1-1-1-2 2-4 3-9 3-14 1-4 0-9 0-13v-3l-1-3-4-16-7-18z"></path><path d="M318 466l1 1 2-2v1l-5 5-1-1 3-4z" class="AA"></path><path d="M306 479l9-9 1 1-9 9-1-1z" class="q"></path><path d="M357 514c1 1 1 2 2 3h1c-1 3-2 7-4 10l-1-2h0l-1-2 3-8v-1z" class="O"></path><path d="M357 515c1 1 1 2 2 3 0 1-1 1-1 2-1 0-2 1-2 2s-1 2-1 2v1h0l-1-2 3-8z" class="F"></path><path d="M373 489v1h0 1l-5 14h-2l-1 2v-2-1l1-1v-1-1l1-1v-1c1 0 1-1 1-2h0 1v-1-1-1-1h1v-2h1 0l1-1z" class="H"></path><path d="M326 521l1 3 1-1 2 7c0 1 0 2 1 4h0c1 0 1 0 1 1h0l-2-1c0 1 0 1 1 2 0 1 0 1-1 2v-1c-1 0-1-1-1-2-1-3-3-6-4-9h1c0-2 0-3-1-5h1z" class="B"></path><path d="M326 521l1 3v4c1 2 3 5 2 7-1-3-3-6-4-9h1c0-2 0-3-1-5h1z" class="R"></path><path d="M359 468l4 5c1 0 2 1 2 2l1 1-1 2c1 1 3 3 4 5l1 1h-1c-5-4-9-9-13-14l1-1h1 0 0l1-1z" class="M"></path><path d="M359 468l4 5c1 0 2 1 2 2l1 1-1 2-7-9h0 0l1-1z" class="p"></path><path d="M312 488c1 0 1 0 2-1 1 1 1 4 2 4l1 1h0c0 1 0 1 1 2v2c1 1 1 2 1 3-1 0-1 0-1 1 1 1 1 2 1 3l2 10c1 0 1 3 1 3v1l-3-9c-3-4-4-9-6-14 0-2-1-4-1-6z" class="D"></path><path d="M312 488c1 3 3 4 3 6v1l-2-1c0-2-1-4-1-6z" class="C"></path><path d="M313 494l2 1c2 4 3 9 4 13-3-4-4-9-6-14z" class="S"></path><path d="M357 514l9-24c0-2 1-4 2-5 1 0 1 1 2 2 0 2-1 4-2 6l-2 7-6 17h0-1c-1-1-1-2-2-3z" class="F"></path><path d="M341 520h0l1 2h0 1l-1 5c1-1 0 0 1 0v2h0l1 1v2c-1 2-1 4-2 5 0 2-1 3-2 4 0 2-1 4-2 5h0c0-2-1-3-2-4l-1 1v-1-5c1-1 1-3 1-4v-5l-1-1 1-1c1 1 1 1 1 2l1-2h0v2h1v-1c1-2 1-5 2-7z" class="K"></path><path d="M338 526h0v2h1v3h-1l-1-1v-2l1-2z" class="V"></path><path d="M342 527c1-1 0 0 1 0v2c0 1 0 2-1 3h0l-1-2 1-3z" class="B"></path><path d="M337 535v-5l1 1h1l-1 7h-1v-2-1z" class="D"></path><path d="M335 527l1-1c1 1 1 1 1 2v2 5-2h-1v-5l-1-1z" class="H"></path><path d="M336 533h1v2 1c-1 2-1 5-1 6l-1 1v-1-5c1-1 1-3 1-4z" class="B"></path><path d="M340 530l1 1c0 3 1 7-1 10-1-3-1-8 0-11z" class="d"></path><path d="M341 520h0l1 2h0 1l-1 5-1 3v1l-1-1c0-3 1-6 1-10z" class="D"></path><path d="M337 566l17-43 1 2h0l1 2-15 42h0l-1-2v1 2c-1 0-1 0-1 1h0c0 1-1 1-1 2l-1 1h0c0 1 0 1-1 2v1h0l-1-1c1-1 1-2 1-3s1-5 1-6l1-1h-1z" class="T"></path><path d="M304 483c0 1-1 1 0 2h0c1 2 1 4 2 6 2 5 4 11 6 17l9 23 8 20c3 6 5 11 6 18h1l1-3h1l-1 1c0 1-1 5-1 6s0 2-1 3l1 1h0v-1c1-1 1-1 1-2h0l1-1c0-1 1-1 1-2h0c0-1 0-1 1-1v-2-1l1 2c-2 3-3 6-4 10 2-3 4-7 5-10l11-28c0-1 0-1 1-2l2-7 2-2c0 1 0 2-1 3l-2 5-2 4-8 21v1l-6 13c0 1 0 2-1 3 0 1-1 2-2 3-1 0-1 0-1-1l-1-1c1-1 0-1 1-1 0 1 0 1 1 1v-1c-1-1-1-3-1-4l-2-5-19-50-1 1-2-2h0l-1-1v-1-1h-1c0-1 0-2-1-2 0-1 1-1 0-1v-2h0v-3h0c0-1 0-1-1-2 0-1-1-2-1-3l-2-7c-1-1-1-1-1-2l-1-1h0v-2l-1-1-1-3h0v-2-1c2 0 3-1 4-2z" class="M"></path><path d="M304 483c0 1-1 1 0 2h0c-1 1-2 2-2 3s1 3 1 4l5 14 6 15-1 1-2-2h0l-1-1v-1-1h-1c0-1 0-2-1-2 0-1 1-1 0-1v-2h0v-3h0c0-1 0-1-1-2 0-1-1-2-1-3l-2-7c-1-1-1-1-1-2l-1-1h0v-2l-1-1-1-3h0v-2-1c2 0 3-1 4-2z" class="B"></path><path d="M346 484l3-3h2l2 2s1 0 1 1c1 0 2 1 2 2 1 0 2 1 3 1v1s0 1 1 1h0c0 3-2 6-3 9l-2 5-3 8c-2 6-4 12-5 18l-1 4h-1 0l-1-1h0 0v-2l-1-1h0v-2c-1 0 0-1-1 0l1-5v-2-1l-1-2 1-5c0-1 0-2-1-3h0c1-3 0-7 1-10 0-3 1-6 1-9 0-1 0-2 1-3h1 0v-3z" class="b"></path><path d="M343 520l3-11c1 6-3 13-3 20h0v-2c-1 0 0-1-1 0l1-5v-2z" class="H"></path><path d="M342 509h0c1-3 0-7 1-10 0-3 1-6 1-9 0-1 0-2 1-3h1c-2 5-2 9-2 14 0 4-1 7-1 11 0-1 0-2-1-3z" class="D"></path><path d="M351 481l2 2s1 0 1 1c1 0 2 1 2 2 1 0 2 1 3 1v1s0 1 1 1h0c0 3-2 6-3 9 0-1-1-2-1-2l1-4c1-1 1-3 1-4l-2-1h0-1c0-2-1-2-2-3v1l-1 4c-1 1-1 2-1 3-1 3-2 6-2 9-1 1-1 2-1 3-1-2 1-6 1-8l2-10s1-2 1-3c0 0-1-1-1-2z" class="d"></path><path d="M355 487h1 0l2 1c0 1 0 3-1 4l-1 4s1 1 1 2l-2 5-3 8c-2 6-4 12-5 18l-1 4h-1 0l-1-1h0 0v-2l4-18v-1c0-2 1-4 1-6l1-1c0-1 1-1 1-1v-2c1-2 2-5 3-7 0-1 0-2 1-3 0-2-1-3 0-4z" class="C"></path><path d="M356 496s1 1 1 2l-2 5c-1-2 0-5 1-7z" class="R"></path><path d="M356 487l2 1c0 1 0 3-1 4l-1 2h0c-1-1-1-2 0-3v-4h0z" class="G"></path><path d="M348 512h1c0 4-1 8-2 12 0 2-1 4 0 5l-1 4h-1 0l-1-1h0 0v-2l4-18z" class="B"></path><defs><linearGradient id="n" x1="334.45" y1="537.401" x2="325.696" y2="607.597" xlink:href="#B"><stop offset="0" stop-color="#1b1717"></stop><stop offset="1" stop-color="#5b1718"></stop></linearGradient></defs><path fill="url(#n)" d="M305 509v-1h1v1l1 1 1 2h0v2c1 0 0 0 0 1 1 0 1 1 1 2h1v1 1l1 1h0l2 2 1-1 19 50 2 5c0 1 0 3 1 4v1c-1 0-1 0-1-1-1 0 0 0-1 1l1 1c0 1 0 1 1 1 1-1 2-2 2-3 1-1 1-2 1-3l6-13v-1l8-21 2 2-4 13c0 1-1 3-2 4v6h0l-3 9v4c-1 1-1 3-1 4l1 1-1 3v3c0 1-1 3-1 5-1 2-1 6-3 9-1 1-1 2-1 3l-1 1-1-1h0v-1l-2 3h-1 0v-2s0 1-1 1c-1-2-1-4-2-6-1-1-1-2-1-3l-2-7c0-1 1-2 1-2-1-2-1-3-1-4l-1-2v-1c0-5-2-8-4-12-1-5-3-10-4-15 0-1-1-2-1-4h0v-3l-14-41z"></path><path d="M337 604v-7c0-1 2 0 2-2 1-3 1-6 3-9v2c-1 3 0 6-2 8h-1c0 2 0 3-1 5 0 1-1 2-1 3z" class="C"></path><path d="M330 591c0 1 0 1 1 2v1l4 14s0 1-1 1c-1-2-1-4-2-6-1-1-1-2-1-3l-2-7c0-1 1-2 1-2z" class="j"></path><path d="M337 604c0-1 1-2 1-3 1-2 1-3 1-5h1c2-2 1-5 2-8v6l-4 14h0v-1c-1-1-1-2-1-3z" class="D"></path><path d="M342 586l4-10v4c-1 1-1 3-1 4l1 1-1 3c-1 1 0 2-1 2-1 2-2 3-2 4v-6-2z" class="G"></path><path d="M345 588v3c0 1-1 3-1 5-1 2-1 6-3 9-1 1-1 2-1 3l-1 1-1-1 4-14c0-1 1-2 2-4 1 0 0-1 1-2z" class="e"></path><path d="M319 550c3 4 4 9 5 14 1 2 3 5 3 7 1 4-1 7 2 11v2 1 1c0 1 1 1 1 2h1 0v2c1 2 1 3 0 4v-1c-1-1-1-1-1-2-1-2-1-3-1-4l-1-2v-1c0-5-2-8-4-12-1-5-3-10-4-15 0-1-1-2-1-4h0v-3z" class="C"></path><path d="M311 520h0l2 2 1-1 19 50 2 5c0 1 0 3 1 4v1c-1 0-1 0-1-1s-1-2-1-3c0 0-1-1-1-2h0c0-1-1-1-1-2 0 0 0-1-1-2v-1c-1-1-1-2-1-3-1-3-3-5-3-8-1-2-2-5-3-7h0l-1-2h0c0-1-1-2-1-3l-1-1c-1-1-1-2-2-3h0v-1-1l-1-2-1-1c0-1 0-2-1-2 0-1 0-2-1-3l-1-3s0-1-1-2v-1l-1-1-1-3v-3z" class="H"></path><path d="M316 491l-1-4c2-1 5-3 7-3h0c1-2 4-4 5-5 3 2 6 3 8 5l2 2c1 1 3 1 4 2 2-1 4-3 5-4v3h0-1c-1 1-1 2-1 3 0 3-1 6-1 9-1 3 0 7-1 10h0c1 1 1 2 1 3l-1 5 1 2v1 2h-1 0l-1-2h0c-1 2-1 5-2 7v1h-1v-2h0l-1 2c0-1 0-1-1-2l-1 1 1 1v5c0 1 0 3-1 4v5 1c0 1 1 3 0 4-1-1-1-4-1-6l-1-4v-1l-1-1h0c0-1 0-1-1-1h0c-1-2-1-3-1-4l-2-7-1 1-1-3h-1c1 2 1 3 1 5h-1c-1-2-3-6-3-9v-1s0-3-1-3l-2-10c0-1 0-2-1-3 0-1 0-1 1-1 0-1 0-2-1-3v-2c-1-1-1-1-1-2h0l-1-1z" class="b"></path><path d="M335 501v-2h1v1 5c-2 3-1 10-1 14-1-2 0-5 0-7v-11z" class="D"></path><path d="M329 510v-4c1 0 1 1 1 1v3h1c0 2 0 4 1 5-1 1-1 1-2 1l-1-6z" class="e"></path><path d="M326 521c-1-4-1-8 0-12 0 2 0 4 1 6 0 2 1 5 1 8l-1 1-1-3z" class="H"></path><path d="M335 484l2 2h-1v1c1 1 0 3 0 4 0 2 1 4 0 6v3-1h-1v2-10-7z" class="R"></path><path d="M331 510v-1c1 0 1 2 1 3l2 10v5l-1-1v1h0c-1-1-1-2-1-2l-1-4c0-2-1-3-1-5 1 0 1 0 2-1-1-1-1-3-1-5z" class="K"></path><path d="M332 515c0 4 1 7 1 11v1h0c-1-1-1-2-1-2l-1-4c0-2-1-3-1-5 1 0 1 0 2-1z" class="b"></path><path d="M328 511c0-1 0-1 1-1l1 6c0 2 1 3 1 5h-1v9h0l-2-7c0-3-1-6-1-8v-2h0v1h1v-3z" class="e"></path><path d="M328 511c0-1 0-1 1-1l1 6c0 2 1 3 1 5h-1c-2-2-1-7-2-10z" class="S"></path><path d="M322 517v-1s0-3-1-3l-2-10c0-1 0-2-1-3 0-1 0-1 1-1l6 22c1 2 1 3 1 5h-1c-1-2-3-6-3-9z" class="K"></path><path d="M330 521h1l1 4s0 1 1 2h0v-1l1 1v-5s1 0 1-1v6l1 1v5c0 1 0 3-1 4v5 1c0 1 1 3 0 4-1-1-1-4-1-6l-1-4v-1l-1-1h0c0-1 0-1-1-1h0c-1-2-1-3-1-4h0v-9z" class="S"></path><path d="M334 535c1 2 1 4 0 6h0 0l-1-4h1v-2z" class="Q"></path><path d="M335 521v6l1 1v4c-2-2-2-4-2-5v-5s1 0 1-1z" class="D"></path><path d="M330 521h1l1 4s0 1 1 2h0c1 3 1 6 1 8v2h-1v-1l-1-1h0c0-1 0-1-1-1h0c-1-2-1-3-1-4h0v-9z" class="K"></path><path d="M332 525s0 1 1 2h0c1 3 1 6 1 8v2h-1v-1-4l-1-1v-6zm4-28l2 1c1 3 0 7 1 10 0 2 0 4 1 6h1v1c1-1 1-4 1-6 1 1 1 2 1 3l-1 5 1 2v1 2h-1 0l-1-2h0c-1 2-1 5-2 7v1h-1v-2h0l-1 2c0-1 0-1-1-2l-1 1v-6-2c0-4-1-11 1-14v-5-3z" class="V"></path><path d="M338 524h1v3 1h-1v-2-2z" class="C"></path><path d="M342 517h0l1 2v1 2h-1 0l-1-2 1-3z" class="G"></path><path d="M336 516c0 1 1 2 1 3v3h0l1 1c0-1 0-2 1-3 0 2-1 3-1 4v2h0l-1-1h-1v-4-2-3z" class="Q"></path><path d="M339 508c0 2 0 4 1 6h1v1c1-1 1-4 1-6 1 1 1 2 1 3l-1 5h0v-1h-1c0 1 0 2-1 3v-1c-2-1-1-8-1-10z" class="C"></path><path d="M335 519c0-4-1-11 1-14v8 3 3 2 4h1l1 1-1 2c0-1 0-1-1-2l-1 1v-6-2z" class="K"></path><path d="M337 486c1 1 3 1 4 2 2-1 4-3 5-4v3h0-1c-1 1-1 2-1 3 0 3-1 6-1 9-1 3 0 7-1 10h0c0 2 0 5-1 6v-1h-1c-1-2-1-4-1-6-1-3 0-7-1-10l-2-1c1-2 0-4 0-6 0-1 1-3 0-4v-1h1z" class="G"></path><path d="M314 390h0 0c1 1 1 1 1 2 1 1 2 3 2 4l1 1c0 1 1 2 1 3 0-2-2-3-1-6l3 8h1c2 6 4 11 8 17l2 2c1 0 1 1 1 1h1v-1l1-2 1 1 1-1h0c2-1 5-4 6-6l3-4v-1h0l1 1 2-4 1 1c-1 1-2 2-1 3l1-1v-1c1-1 2-2 3-4 2-3 3-6 6-9h1l-2 11h3l3-1h4 0c5 5 7 10 9 15 2 1 2 0 3 2v1h0c1 2 2 4 3 5 3 7 2 12 0 19 0-1 1-1 1-2v1 2c-1 3-2 8-3 11l-8 24c0 1-1 2-2 4h0l-2-2h1l-1-1c-1-2-3-4-4-5l1-2-1-1c0-1-1-2-2-2l-4-5-1 1h0 0-1l-1 1-7-6c-3-2-7 7-10 7h0c-2 1-4 0-5-1l-10-6-3 2v-1l3-6c-1 0-1-1-1-2 2-4 3-9 3-14 1-4 0-9 0-13v-3l-1-3-4-16-7-18z" class="b"></path><path d="M372 431l-1 1v2c0 1 0 1-1 1 0 1-1 1-1 1v1h-1c0-1 0-2 1-3v-4c1-1 1-1 1-2 1 1 1 2 2 3z" class="D"></path><path d="M358 446c1 1 2 3 4 3v1c1 0 1 0 2 1 1 0 0 0 1 1-3 1-3 7-4 9h-1l2-9c-1-2-2-4-5-4v-2h1z" class="E"></path><path d="M353 451c1-3 2-4 4-5v2c-1 1-2 2-2 4-1 5 0 12 4 16h0l-1 1h0c-4-6-5-12-5-18z" class="B"></path><path d="M375 434v1c0 2-1 4 0 6s-5 16-6 19h-1c-1 1-1 1-1 2v-2l-1 1v-1l5-12c1-2 1-4 2-6v-2-3l1 2h0v-1-2c1 0 1-1 1-2z" class="V"></path><path d="M375 435h1 2c1 1 0 3 0 5-1 5-4 12-6 17v-1c-2 2-2 5-3 6-1 0 0-1 0-2 1-3 7-17 6-19s0-4 0-6z" class="Q"></path><path d="M369 460c0 1-1 2 0 2 1-1 1-4 3-6v1l-4 13c-1 2-1 4-2 6l-1-1c0-1-1-2-2-2 1-1 1-2 1-3 0-3 1-7 2-10v1l1-1v2c0-1 0-1 1-2h1z" class="R"></path><path d="M364 470h1 1c0 2 0 3-1 4v1c0-1-1-2-2-2 1-1 1-2 1-3z" class="S"></path><path d="M361 405l3-1c1 0 2 0 3 1h-1c-1 0-4 1-5 2s-1 2-2 4v1h0c0 2-1 3-1 5 0 1-1 2-1 3v2 4c0-1 0-1 1-1v-2l1-1c-1 2-1 4-2 6s-1 4-1 6c-1 2 0 4 1 6s2 4 4 6c1-4 2-7 4-10h0l-3 9v2h-1c-2-3-5-6-6-10h-1c-1-5 0-13 1-18l3-14h3z" class="E"></path><path d="M361 405l3-1c1 0 2 0 3 1h-1c-1 0-4 1-5 2s-1 2-2 4v1h0l-1 2c0 1-1 3-1 4-1 2-1 3-1 5l-1-1v-3l3-14h3z" class="B"></path><path d="M349 409l1-1v-1c1-1 2-2 3-4 2-3 3-6 6-9h1l-2 11-3 14c-1 5-2 13-1 18h1c1 4 4 7 6 10 1 1 2 2 3 2v1 1c-1-1-1-1-2-1v-1c-2 0-3-2-4-3 0-1-2-2-2-2-1-2-2-3-2-4-1-2-1-3-2-5v-3l-1-11h0c0-1 0-4-2-5-1 0-3 1-4 2 0-2 0-4 1-5l1-1c0-1 1-2 2-3z" class="N"></path><path d="M364 404h4 0c5 5 7 10 9 15 2 1 2 0 3 2v1h0c1 2 2 4 3 5 3 7 2 12 0 19 0-1 1-1 1-2v1 2c-1 3-2 8-3 11l-8 24c0 1-1 2-2 4h0l-2-2h1l-1-1c-1-2-3-4-4-5l1-2c1-2 1-4 2-6l4-13c2-5 5-12 6-17 0-2 1-4 0-5h-2-1v-1l-3-3c-1-1-1-2-2-3 1-1 2-3 2-4 0-3-3-5-5-7-1 0-1 1-2 1 0-1 0-1-1-2h-2c-2 2-2 4-3 6l-1 1v2c-1 0-1 0-1 1v-4-2c0-1 1-2 1-3 0-2 1-3 1-5h0v-1c1-2 1-3 2-4s4-2 5-2h1c-1-1-2-1-3-1z" class="H"></path><path d="M377 419c2 1 2 0 3 2v1l-1 1h-1l-1-4z" class="G"></path><path d="M383 446c0-1 1-1 1-2v1 2c-1 3-2 8-3 11l-8 24c0 1-1 2-2 4h0l-2-2h1l-1-1c0-1 1-2 1-3s1-3 1-4c2-5 4-10 6-16 2-4 3-10 6-14z" class="c"></path><path d="M370 480c1 1 1 3 1 4v1 1l-2-2h1l-1-1c0-1 1-2 1-3z" class="J"></path><path d="M314 390h0 0c1 1 1 1 1 2 1 1 2 3 2 4l1 1c0 1 1 2 1 3 0-2-2-3-1-6l3 8h1c2 6 4 11 8 17l2 2c1 0 1 1 1 1h1v-1l1-2 1 1 1-1h0c2-1 5-4 6-6l3-4v-1h0l1 1 2-4 1 1c-1 1-2 2-1 3-1 1-2 2-2 3l-1 1c-1 1-1 3-1 5 1-1 3-2 4-2 2 1 2 4 2 5h0l1 11v3c1 2 1 3 2 5 0 1 1 2 2 4 0 0 2 1 2 2h-1c-2 1-3 2-4 5 0 6 1 12 5 18h0-1l-1 1-7-6c-3-2-7 7-10 7h0c-2 1-4 0-5-1l-10-6-3 2v-1l3-6c-1 0-1-1-1-2 2-4 3-9 3-14 1-4 0-9 0-13v-3l-1-3-4-16-7-18z" class="N"></path><path d="M326 443l2-1h0c0 6-1 12-4 17-1 0-1-1-1-2 2-4 3-9 3-14z" class="g"></path><path d="M326 427l1 1h0c2 3 1 7 1 10v4h0 0l-2 1c1-4 0-9 0-13v-3z" class="x"></path><path d="M322 402c2 6 4 11 8 17h-1l-2-4c-1 0-1 0-1-1v-1l-1 1c1 1 1 3 1 5 1 1 1 2 1 4 1 1 1 2 0 3v2h0l-1-1-1-3h1v-3c-1-6-3-13-5-19h1z" class="I"></path><path d="M326 421l1 5v2h0l-1-1-1-3h1v-3z" class="g"></path><path d="M354 440c0 1 1 2 2 4 0 0 2 1 2 2h-1c-2 1-3 2-4 5v-3-1h0c-1-1-1-2-2-2h-2 0c-1 2-2 6-4 7 2-4 4-7 4-11l2-1h0 3z" class="e"></path><path d="M314 390h0 0c1 1 1 1 1 2 1 1 2 3 2 4l1 1c0 1 1 2 1 3 0-2-2-3-1-6l3 8c2 6 4 13 5 19v3h-1l-4-16-7-18z" class="Z"></path><path d="M350 430v-3-1c0-2 0-3 1-5l1 11v3c1 2 1 3 2 5h-3 0l-2 1c0 4-2 7-4 11l-4 5v-1c3-4 5-9 7-13v-7c0-1 0-3 1-3 0-1 0-1 1-2v-1z" class="p"></path><path d="M352 432v3c1 2 1 3 2 5h-3 0l-2 1c1-2 1-4 2-5v-2c1 0 1-2 1-2z" class="G"></path><defs><linearGradient id="o" x1="331.308" y1="433.505" x2="346.6" y2="439.53" xlink:href="#B"><stop offset="0" stop-color="#1b1616"></stop><stop offset="1" stop-color="#371616"></stop></linearGradient></defs><path fill="url(#o)" d="M346 408l1 1 2-4 1 1c-1 1-2 2-1 3-1 1-2 2-2 3l-1 1c-1 1-1 3-1 5 1-1 3-2 4-2 2 1 2 4 2 5h0c-1 2-1 3-1 5v1 3 1c-1 1-1 1-1 2-1 0-1 2-1 3v7c-2 4-4 9-7 13v1h0c-1 2-2 3-3 4-1 0-2 0-3-1-7-5-1-18-1-26 0-2 0-5-1-8v2h0-1v-2l-1-2h0v-2h-1c0-1 0-2-1-3h1l2 2c1 0 1 1 1 1h1v-1l1-2 1 1 1-1h0c2-1 5-4 6-6l3-4v-1h0z"></path><path d="M347 429h0c1-1 1-2 2-2h0l1 3v1c-1 1-1 1-1 2-1 0-1 2-1 3 0-3 0-5-1-7z" class="d"></path><path d="M347 429c1 2 1 4 1 7v7c-2 4-4 9-7 13h-1v-4l3-8c1-5 2-10 4-15z" class="G"></path><path d="M346 408l1 1 2-4 1 1c-1 1-2 2-1 3-1 1-2 2-2 3l-1 1c-1 1-1 3-1 5-5 3-7 6-9 10-1-1-2-3-3-4h-1l1 2v2h0-1v-2l-1-2h0v-2h-1c0-1 0-2-1-3h1l2 2c1 0 1 1 1 1h1v-1l1-2 1 1 1-1h0c2-1 5-4 6-6l3-4v-1h0z" class="M"></path><path d="M427 104l-1-2h1c1 1 3 1 4 2l5 3 2 2h1v-1h1l2-2 10 6c1 1 3 2 4 3l3 3c1-1 1-1 1 0l1-1h1v-1l4 2 2 1c4 1 8 3 11 5 1 0 2 1 3 2l6 3-1 1c1 1 2 1 3 2v1h0v2h2c-3 4-7 8-9 12l-1-1c-1 1-2 3-3 4s-1 2-1 2l-1 3c0 4 0 7 1 11 0 3 2 6 4 8 1 3 2 6 1 8 1 2 1 3 1 5 0 1-1 2 0 3h0 1 0v3c1-1 1-2 2-3h1c0 2-4 15-6 17-2 4-3 8-4 12l-6 18-4 12v-1-1c0-1-1-2-2-2 0-1 0-1-1-2l-3 7-5 13-2 6c1 1 3 0 3 2h0l-1 4-1 5c0 1-1 1-1 1-2 0-2 0-3 2h1l-2 2v-1-2h-1c-2 6-3 12-6 17-2 2-2 5-3 8l-7 18c0 2-1 4-1 6l-3 6c0 4-1 8-3 11v1h0c-1 1-2 4-3 5l-1-1c0 2-1 3-1 4l1 4-2 3v1c-1 1-2 5-2 8v1l-1-1h-1c-1 0-1-1-2 0v4h-1c-1-1-1-5-2-7v-1c-1 1-1 1-1 2s0 3-1 4c0 9-3 17-6 25l-9 26h0l-2 4c-1 3-1 8-4 11l-1 1c0 3 0 5-2 8l-1-1v2l-1 1v1c0 2-2 4-1 5l-2 2v-2c1-3 2-8 3-11v-2-1c0 1-1 1-1 2 2-7 3-12 0-19-1-1-2-3-3-5h0v-1c-1-2-1-1-3-2-2-5-4-10-9-15h0-4l-3 1h-3l2-11c1-5 5-9 5-14h0c0-3 2-6 3-9l6-13 6-15c0 1-1 1-1 2-1 0-1 1-1 1v-3-1h0-1-1c-1-3 2-5 2-8-1 0-1 0-1 1v1l-1-1c0 1-1 1-1 1v1c0-2 1-4 2-6l-1-1c0-2 1-4 2-5l5-14 12-35 14-38 5-14 2-1 4-11 3-9v-1-1c-1-1 1-4 2-5-1-2-1-2-1-4v-2l2-4h0v-2l-1-1 1-4v-3h0 0l5-18v-2c0 1 0 0-1 0l-1-23c1 1 1 2 2 2 0 2-1 4 0 4v-3c-1 0 0-1 0-2l-1-1v-1h0c0-1 0-2-1-3h0c0-1 1-2 0-3h0c0-3-1-5-2-8-1-1-2-3-2-4s1-2 2-2l-4-7v-1c1-1 2 0 3 1v-1l1 1v-1z" class="h"></path><path d="M426 194l1-2c0 1-1 2 0 3 0 1-3 6-4 8v-1-1c-1-1 1-4 2-5 0-1 0-2 1-2z" class="o"></path><path d="M454 171s0-1-1-1c0 0-1 0-1-1h-3v-1h8l-2 4-1-1zm-11-6c3-1 6 0 9 0h6l-1 1h0 0-2c-4 1-8 0-12-1zm4-5h1c2-1 4-1 6-1s4-1 6-1c0 0 1 0 1-1h1 1l-1 1s-1 0-2 1h-1v1l1-1h0v1h0c-1 0-1 0-2 1h1v1c-1-1-1-1-2-1h-4c-1 0-4 1-5 1h1c1 0 1-1 2-1h0c1 0 1 0 1-1h1 0c-1-1-5 0-7 0 0 1-1 0-1 0h2z" class="I"></path><path d="M447 172c-1 0-2-2-2-3h1c0 1 1 1 2 1 0 0 1 0 2 1h4l1 1c-1 2-3 5-5 6v-1c0-2-2-3-3-5z" class="T"></path><path d="M447 172c-1 0-2-2-2-3h1c0 1 1 1 2 1 0 0 1 0 2 1h-1l2 1v1l-1 1c-1-1-2-2-3-2z" class="M"></path><g class="I"><path d="M416 236c1-2 1-3 3-5 0 2-1 5-2 7-1 1-1 3-1 5s-2 5-3 7l-3 6v1 1h0c0 1-1 2-1 2h-1 0v-1h0l1-1v-1s1-2 1-3 1-2 2-4l1-3c1-4 2-7 3-11z"></path><path d="M416 252h0v-6c1-2 1-3 1-4 0-3 1-5 2-7l3-8v1c0 1-2 5-2 6h1v-1l1-3 1-1v-2c1 0 1-1 1-1h0l-1 4c0 1 1 2 1 3l-4 11c-1 3-2 5-4 8z"></path></g><path d="M420 244c-1-1-1-3-1-4 0-3 3-7 4-10 0 1 1 2 1 3l-4 11z" class="J"></path><path d="M490 133h0v2h2c-3 4-7 8-9 12l-1-1c-1 1-2 3-3 4h-1l-1-1c-11 14-21 29-31 44-4 8-9 16-13 24-6 11-11 23-15 35l-6 14c0 1-1 3-2 5h1 0l2-2c0-1 0-2 1-2v-1l1 1-2 4c-2 3-3 6-4 9-2 3-2 6-4 8l-2 1-5 13-22 55c-1 0-2 4-2 4-2 3-3 6-4 10l-5 9h0c0-3 2-6 3-9l6-13 6-15 7-16h0-1v1c0 1-1 1-1 2v-4l1-2v-1-1c0-1 1-1 1-2s1-3 1-4l1-3c1 0 1 0 2-1 0-1 1-3 1-4 1-1 1-1 2 0h1l21-56c2-3 3-5 4-8l4-11c0-1-1-2-1-3l1-4c2 0 3-8 5-10 1 0 1 0 1-1h0c1-2 2-3 3-4v-1-1-1h1c0-1 0-2 1-3h0v-1l2-2 1-1 6-14c2-3 3-6 6-9 2-1 4-4 5-6l2-4h1c2-4 5-7 6-11l3-5c0-1 1-2 1-3v-1h-5c1-1 4-1 5-2l1-1v2 3l1-1v-1l1-2c0-1 0-1 1-2v-1 4 2c1-2 3-4 5-5 1-1 2-3 3-4 2-1 5-3 6-5 1 0 2-1 3-2h1z" class="a"></path><path d="M410 271h1 0l2-2c0-1 0-2 1-2v-1l1 1-2 4c-2 3-3 6-4 9-2 3-2 6-4 8l-2 1 7-18z" class="U"></path><path d="M490 135h2c-3 4-7 8-9 12l-1-1c-1 1-2 3-3 4h-1l-1-1c4-6 8-10 13-14z" class="I"></path><path d="M469 145v2 3l1-1v-1l1-2c0-1 0-1 1-2v-1 4 2l-14 20v-1c2-4 5-7 6-11l3-5c0-1 1-2 1-3v-1h-5c1-1 4-1 5-2l1-1z" class="C"></path><path d="M389 313c1 0 1 0 2-1 0-1 1-3 1-4 1-1 1-1 2 0h1c-2 7-5 13-8 19h0-1v1c0 1-1 1-1 2v-4l1-2v-1-1c0-1 1-1 1-2s1-3 1-4l1-3z" class="I"></path><path d="M424 226c2 0 3-8 5-10 1 0 1 0 1-1h0c1-2 2-3 3-4v-1-1-1h1c0-1 0-2 1-3h0v-1l2-2 1-1v3l-14 29c0-1-1-2-1-3l1-4z" class="F"></path><path d="M457 168h1v1l-20 35v-3l6-14c2-3 3-6 6-9 2-1 4-4 5-6l2-4z" class="V"></path><path d="M420 212c1 1 1 2 1 3-1 1-1 3-1 5-1 0 0 0-1 1v1l-1 3h0c0 2-1 2-1 3v2c-2 1-1 4-1 6-1 4-2 7-3 11l-1 3c-1 2-2 3-2 4s-1 3-1 3v1l-1 1h0v1h0 1v2c-1 1-2 3-2 4-1 1-1 1-1 2h0c0 1 0 1-1 2h0v2c0 1-1 1-1 2l-1 1v1c-1 1 0 2-1 3 0 1 0 1-1 2v1l-1 1v1s0 1-1 1c0 1 1 1 0 1 0 1 0 1-1 2v1s0 1-1 1v1 1l-1 1v1l-1 1h0c0 1 0 1-1 2l1 1h-1v1h-1v1l1 1h-1 0v1l-1 2v2l-1 3c-1 1-1 2-1 3l-1 1-1 3c0 1-1 3-1 4s-1 1-1 2v1 1l-1 2v4c0-1 1-1 1-2v-1h1 0l-7 16c0 1-1 1-1 2-1 0-1 1-1 1v-3-1h0-1-1c-1-3 2-5 2-8-1 0-1 0-1 1v1l-1-1c0 1-1 1-1 1v1c0-2 1-4 2-6l-1-1c0-2 1-4 2-5l5-14 12-35 14-38 5-14 2-1 4-11z" class="w"></path><path d="M414 224l2-1-4 12-3 3 5-14z" class="o"></path><path d="M378 334v-2h1v-1 1 2 1-1c0-1 0 0 1-1h0v-1c0-1 1-1 1-1v-1-1l1-1h0 0v-2c1 0 1-1 1-2l1-1v-1-1l1-1v1 1c-1 1-1 1-1 2v2l2-3v1l-1 2v4c0-1 1-1 1-2v-1h1 0l-7 16c0 1-1 1-1 2-1 0-1 1-1 1v-3-1h0-1-1c-1-3 2-5 2-8z" class="I"></path><path d="M386 323v1l-1 2v4c0-1 1-1 1-2v-1h1 0l-7 16c0 1-1 1-1 2-1 0-1 1-1 1v-3-1h0c1-1 1-1 1-2 1-3 4-7 5-11 0-1 0-2 1-3h-1l2-3z" class="J"></path><path d="M477 149l1 1h1c-1 1-1 2-1 2l-1 3c0 4 0 7 1 11 0 3 2 6 4 8-2 0-2 0-3-1v-2h0c-1-1-2-2-2-3 0 3 1 5 1 7 0 1 1 1 2 2 0 1 0 2-1 2-1 1-5 6-6 6h-1c-9 7-19 17-26 26l-3 3-2 4-3 3-3 4c-3 4-5 8-7 13-3 3-4 6-6 10-1 2-2 3-2 5 0 1 0 2-1 3l-5 10v1c-1 0-1 1-1 2l-2 2h0-1c1-2 2-4 2-5l6-14c4-12 9-24 15-35 4-8 9-16 13-24 10-15 20-30 31-44z" class="h"></path><path d="M470 183c-2 1-3 3-6 3 0-1-1-1-1-1-2-7 6-16 9-22 2-3 3-6 4-9l1 1c0 4 0 7 1 11 0 3 2 6 4 8-2 0-2 0-3-1v-2h0c-1-1-2-2-2-3 0 3 1 5 1 7 0 1 1 1 2 2-2 1-3 2-4 3h0-1c-1 0-4 3-5 3z" class="B"></path><path d="M470 183h-2l-1-1c0-4 4-7 6-10 1-2 1-4 2-6h0l1 3c1 3 2 7 0 10l-1 1c-1 0-4 3-5 3z" class="F"></path><path d="M476 179c-2-2 0-2-1-4 0-1-1-3-1-4l1-2h1c1 3 2 7 0 10z" class="I"></path><defs><linearGradient id="p" x1="437.391" y1="146.523" x2="419.587" y2="145.188" xlink:href="#B"><stop offset="0" stop-color="#d7181a"></stop><stop offset="1" stop-color="#ff5a55"></stop></linearGradient></defs><path fill="url(#p)" d="M427 104l-1-2h1c1 1 3 1 4 2l5 3 2 2h1v-1h1l2-2 10 6c1 1 3 2 4 3l3 3c1-1 1-1 1 0l1-1h1v-1l4 2 2 1c4 1 8 3 11 5 1 0 2 1 3 2l6 3-1 1c1 1 2 1 3 2v1h-1c-1 1-2 2-3 2-1 2-4 4-6 5-1 1-2 3-3 4-2 1-4 3-5 5v-2-4 1c-1 1-1 1-1 2l-1 2v1l-1 1v-3-2l-1 1c-1 1-4 1-5 2h5v1c0 1-1 2-1 3l-3 5c-1-1-1-1-2-1-3 0-5 1-7 1s-5 0-7 1h0l-1 2h-2c-3 1-6 1-8 2l-6 21c-1 4-2 9-4 12-1-1 0-2 0-3l-1 2c-1 0-1 1-1 2-1-2-1-2-1-4v-2l2-4h0v-2l-1-1 1-4v-3h0 0l5-18v-2c0 1 0 0-1 0l-1-23c1 1 1 2 2 2 0 2-1 4 0 4v-3c-1 0 0-1 0-2l-1-1v-1h0c0-1 0-2-1-3h0c0-1 1-2 0-3h0c0-3-1-5-2-8-1-1-2-3-2-4s1-2 2-2l-4-7v-1c1-1 2 0 3 1v-1l1 1v-1z"></path><path d="M431 110l1 1c0 1 1 2 0 3 0 1 1 4 0 5v-2c-1-1 0-3-1-4v-2-1h0z" class="U"></path><path d="M426 179l1 1v-1 1l-1 6v-2l-1-1 1-4z" class="x"></path><path d="M424 190l2-4c1 3-1 5 0 8-1 0-1 1-1 2-1-2-1-2-1-4v-2z" class="k"></path><path fill="#eac4cb" d="M431 158c2 3-1 10-1 13-1 3-1 5-2 8l-1 1v-1 1l-1-1v-3h0 0l5-18z"></path><path d="M431 122c1 4 1 8 1 11l-1 23c0 1 0 0-1 0l-1-23c1 1 1 2 2 2 0 2-1 4 0 4v2-19z" class="N"></path><path d="M427 112c2 3 3 6 4 10v19-2-3c-1 0 0-1 0-2l-1-1v-1h0c0-1 0-2-1-3h0c0-1 1-2 0-3h0c0-3-1-5-2-8-1-1-2-3-2-4s1-2 2-2z" class="G"></path><path d="M439 141v-9c2 1 2 1 2 2 0 5-1 11-2 15v4 2l-1 3-1 2c2 0 9-2 11-2l-1 2h-2c-3 1-6 1-8 2l-6 21c-1 4-2 9-4 12-1-1 0-2 0-3 0 0 1-4 1-5 2-4 3-9 4-13 3-11 7-22 7-33z" class="q"></path><path d="M427 104l-1-2h1c1 1 3 1 4 2l5 3 2 2 8 6-1 1c2 1 3 2 4 3h0-1c-1 1-1 1-1 2l-5-5h-1l-1 2h0l-1-1c-1 1 0 2 0 4v8 3h-1c0-3 0-7-1-10s-3-8-5-11l-1-1c-1-2-2-4-4-6z" class="S"></path><path d="M436 107l2 2 8 6-1 1c2 1 3 2 4 3h0-1c-1 1-1 1-1 2l-5-5h-1l-1 2h0l-1-1c-1 1 0 2 0 4-1-2-1-4-1-5-1-2-2-4-3-5l1-1c0-1-1-1-1-2l1-1z" class="C"></path><path d="M436 107l2 2 8 6-1 1-9-6c0-1-1-1-1-2l1-1z" class="E"></path><path d="M439 121c0-2-1-3 0-4l1 1h0l1-2h1l5 5 3 3h0v3c0 1 1 3 1 4 1 2 3 6 4 7s1 1 2 1v1l-7 5c-3 2-6 4-9 5l-2-1c1-4 2-10 2-15 0-1 0-1-2-2v9c-1-2-1-7-1-9h1v-3-8z" class="X"></path><path d="M439 121c0-2-1-3 0-4l1 1h0l1-2h1l-1 1c0 3 0 5 1 8 0 2 1 4 1 6 1 6-1 13-2 19l-2-1c1-4 2-10 2-15 0-1 0-1-2-2v9c-1-2-1-7-1-9h1v-3-8z" class="N"></path><path d="M457 140s2-1 3-2c0 0 1-1 2-1v1c1-1 1-1 2-1 2 1 3 2 4 5l1 3-1 1c-1 1-4 1-5 2h5v1c0 1-1 2-1 3l-3 5c-1-1-1-1-2-1-3 0-5 1-7 1s-5 0-7 1h0c-2 0-9 2-11 2l1-2 1-3v-2-4l2 1c3-1 6-3 9-5l7-5z" class="X"></path><path d="M444 152l-1-1 3-1c2 0 2 0 3 1l-5 1z" class="I"></path><path d="M464 137c2 1 3 2 4 5-1-1-1-1-2-1-2 0-2-2-3-3h-1 0c1-1 1-1 2-1z" class="O"></path><path d="M457 140s2-1 3-2c0 0 1-1 2-1v1h0c-5 4-9 6-14 9-2 2-6 3-7 5-1 0-2 1-2 1v-4l2 1c3-1 6-3 9-5l7-5z" class="L"></path><path d="M463 148h5v1c0 1-1 2-1 3l-3 5c-1-1-1-1-2-1-3 0-5 1-7 1s-5 0-7 1h0c-2 0-9 2-11 2l1-2 1-3 2-2c1 0 1 0 2-1h1l5-1 5-1h1 2 1c0-1 2-1 3-1s1 0 2-1z" class="w"></path><path d="M438 158c4-2 7-2 11-3 0 0 1-1 2-1l11-1c2 0 3-1 5-1l-3 5c-1-1-1-1-2-1-3 0-5 1-7 1s-5 0-7 1h0c-2 0-9 2-11 2l1-2z" class="O"></path><path d="M442 106l10 6c1 1 3 2 4 3l3 3c1-1 1-1 1 0l1-1h1v-1l4 2 2 1c4 1 8 3 11 5 1 0 2 1 3 2l6 3-1 1c1 1 2 1 3 2v1h-1c-1 1-2 2-3 2-1 2-4 4-6 5-1 1-2 3-3 4-2 1-4 3-5 5v-2-4 1c-1 1-1 1-1 2l-1 2v1l-1 1v-3-2l-1-3c-1-3-2-4-4-5-1 0-1 0-2 1v-1c-1 0-2 1-2 1-1 1-3 2-3 2v-1c-1 0-1 0-2-1s-3-5-4-7c0-1-1-3-1-4v-3h0l-3-3c0-1 0-1 1-2h1 0c-1-1-2-2-4-3l1-1-8-6h1v-1h1l2-2z" class="X"></path><path d="M471 139h1v-1c-1-1-1-2-1-3h0v-3c-1-2-3-4-4-5v-1h1c1 1 2 2 3 2v-1h0 0c1 0 1 0 2 1 0 0 0 1 1 1v1l2 4c1 1 1 2 2 3v1c-1-1-3-3-3-4-1-1-1-1-2-1l-1 1 1 1c-1 2 0 3 0 5 0 1-1 2-1 3h0l-1-4z" class="h"></path><path d="M459 118c1-1 1-1 1 0 3 1 6 3 8 4 3 2 9 3 11 6 0 1 1 3 1 4l-1 1h0c-1 0-2 0-3 1l-2-4v-1c1 0 1 1 1 1h2v-1l-2-2c-1-1-2-1-3-1-2-2-5-3-7-4s-4-3-6-4z" class="D"></path><path d="M480 132l2 1h1c1 1 1 2 2 2h1c-1 2-4 4-6 5-1 1-2 3-3 4-2 1-4 3-5 5v-2-4h0 0c0-1 1-2 1-3 0-2-1-3 0-5l-1-1 1-1c1 0 1 0 2 1 0 1 2 3 3 4v-1c-1-1-1-2-2-3 1-1 2-1 3-1h0l1-1z" class="U"></path><path d="M473 135l-1-1 1-1c1 0 1 0 2 1 0 1 2 3 3 4 0 0 1 0 1 1s-1 1-1 1c-2-1-4-3-5-5z" class="X"></path><path d="M480 132l2 1h1c1 1 1 2 2 2-2 1-3 2-5 3l-2-1c-1-1-1-2-2-3 1-1 2-1 3-1h0l1-1z" class="R"></path><path d="M462 116l4 2 2 1c4 1 8 3 11 5 1 0 2 1 3 2l6 3-1 1c1 1 2 1 3 2v1h-1c-1 1-2 2-3 2h-1c-1 0-1-1-2-2h-1l-2-1c0-1-1-3-1-4-2-3-8-4-11-6-2-1-5-3-8-4l1-1h1v-1z" class="L"></path><path d="M482 133v-2c1 0 0 0 0-1v-1l1-1c1 1 2 2 4 2 1 1 2 1 3 2v1h-1c-1 1-2 2-3 2h-1c-1 0-1-1-2-2h-1z" class="c"></path><path d="M483 133c1-1 2-1 4-1 0 0 1 0 2 1-1 1-2 2-3 2h-1c-1 0-1-1-2-2z" class="G"></path><path d="M462 116l4 2 2 1c4 1 8 3 11 5 1 0 2 1 3 2l6 3-1 1c-2 0-3-1-4-2h0c-3-1-5-3-7-4-5-2-9-4-14-7v-1z" class="R"></path><path d="M446 115c4 3 9 7 11 12 4 2 8 4 11 8 1 1 2 3 3 4l1 4h0v1c-1 1-1 1-1 2l-1 2v1l-1 1v-3-2l-1-3c-1-3-2-4-4-5-1 0-1 0-2 1v-1c-1 0-2 1-2 1-1 1-3 2-3 2v-1c-1 0-1 0-2-1s-3-5-4-7c0-1-1-3-1-4v-3h0l-3-3c0-1 0-1 1-2h1 0c-1-1-2-2-4-3l1-1z" class="d"></path><path d="M447 121c0-1 0-1 1-2h1 0c1 1 2 2 2 3v2h-1l-3-3z" class="Q"></path><path d="M450 124h1c2 3 3 6 6 8 2 2 5 3 7 5-1 0-1 0-2 1v-1c-1 0-2 1-2 1-1 1-3 2-3 2v-1c-1 0-1 0-2-1s-3-5-4-7c0-1-1-3-1-4v-3h0z" class="N"></path><defs><linearGradient id="q" x1="437.897" y1="217.861" x2="455.588" y2="231.679" xlink:href="#B"><stop offset="0" stop-color="#0a1313"></stop><stop offset="1" stop-color="#441a1a"></stop></linearGradient></defs><path fill="url(#q)" d="M480 177c-1-1-2-1-2-2 0-2-1-4-1-7 0 1 1 2 2 3h0v2c1 1 1 1 3 1 1 3 2 6 1 8 1 2 1 3 1 5 0 1-1 2 0 3h0 1 0v3c1-1 1-2 2-3h1c0 2-4 15-6 17-2 4-3 8-4 12l-6 18-4 12v-1-1c0-1-1-2-2-2 0-1 0-1-1-2l-3 7v-1h0-1c0-1 1-2 1-3l-2 2h-1c1-1 1-3 1-4l-2 4v-2c-1 0-1 0-1-1v-1h-1v1h-1c-1-5 0-9-1-14 0-1 0-1 1-2l-1-1c-4 6-8 13-11 20v1l-1-1c-1 1-1 3-2 4v4-4c-1-1-1-2-2-3l-1-1 2 12-1 1c1 2 2 4 4 5l-2 1-2-2-3-1v2h0l1 1-1 1c-1 1-1 1-1 2-1 0-1 0-1 1h2v1c-3 3-5 4-8 6l-3 2h-1l1-1c-2 0-4 3-6 2 0-1 1-1 1-2l-3-2c0 1-1 1-1 1 0 1 0 1-1 1l-1 1c-1 0-1 0-1-1l-2 3-2 3-3 6h0v-3c2-2 2-5 4-8 1-3 2-6 4-9l2-4-1-1 5-10c1-1 1-2 1-3 0-2 1-3 2-5 2-4 3-7 6-10 2-5 4-9 7-13l3-4 3-3 2-4 3-3c7-9 17-19 26-26h1c1 0 5-5 6-6 1 0 1-1 1-2z"></path><path d="M417 270c1-2 2-3 3-5l-2 6-1 1h-1l1-2z" class="C"></path><path d="M443 214l3-3 1 1-2 4-2-2z" class="g"></path><path d="M439 245c0-1 1-2 1-3h0v-1c0-1 1-2 2-3 0-1 1-1 1-1-1 3-2 6-4 8z" class="d"></path><path d="M443 214l2 2-3 3-1-1 2-4z" class="x"></path><path d="M438 221l3-3 1 1-2 4-2-2z" class="y"></path><path d="M426 259c0-1 0-1 1-2 2 2-2 4 3 5l2 3v1l-3-3v1 1h-1c-1-2-1-4-2-6h0z" class="C"></path><path d="M435 225l3-4 2 2-4 5c0-1 0-2-1-3z" class="AA"></path><path d="M428 265h1v-1-1l3 3v2c-1 0-1 1-2 1l-2 2v-6z" class="D"></path><path d="M433 257c0 2 0 4 1 5v2h1v2l-3 3h0v-1-2-1-3l-1-1 1-1h1v-3z" class="E"></path><path d="M428 238c2-5 4-9 7-13 1 1 1 2 1 3l-9 16v-1c0-2 0-3 1-5z" class="q"></path><path d="M413 271v1c0 2-2 3-2 5h1 1l2-2h0v-2-1c1-1 0-1 1-2h1l-1 2h1l1-1c1 1 1 1 1 0 0 2-1 3-2 5-1 0-2 1-2 2h0c0 1 0 1-1 1l-1 1c-1 0-1 0-1-1l-2 3-2 3-3 6h0v-3c2-2 2-5 4-8 1-3 2-6 4-9z" class="p"></path><path d="M416 272h1l1-1c1 1 1 1 1 0 0 2-1 3-2 5-1 0-2 1-2 2h0c0 1 0 1-1 1l-1 1c-1 0-1 0-1-1 0 0 1-1 1-2h1c2-1 1-4 2-5z" class="R"></path><path d="M414 266l5-10c1-1 1-2 1-3 0-2 1-3 2-5 2-4 3-7 6-10-1 2-1 3-1 5v1l-12 23-1-1z" class="AF"></path><path d="M446 211c7-9 17-19 26-26h1l-26 27-1-1z" class="a"></path><path d="M433 257v-1c-1-2-1-4-1-6 0-3 0-7 2-10 0-2 1-3 2-4 1-2 1-4 3-6 0 2-1 3-1 5 0 1 0 3-1 4h0v-1h0l-1 1c0 1-1 2-1 3 0 4 1 7-1 10v2c-1 2 0 3 0 5v3c-1-1-1-3-1-5z" class="D"></path><path d="M426 259h0c1 2 1 4 2 6v6l-5 2c-1 1-4 3-6 3 1-2 2-3 2-5l7-12z" class="M"></path><path d="M419 271l2 1c1-1 1-1 1-2l1 1v2c-1 1-4 3-6 3 1-2 2-3 2-5z" class="I"></path><path d="M434 262v-3c0-2-1-3 0-5v-2c2-3 1-6 1-10 0-1 1-2 1-3l1-1h0v1 3 6l2 12-1 1c1 2 2 4 4 5l-2 1-2-2-3-1h-1v-2z" class="Q"></path><path d="M436 254c1 3 1 5 2 7h0c1 2 2 4 4 5l-2 1-2-2-1-1c-1-3-2-7-1-10z" class="O"></path><path d="M436 254c-1-3 0-8 0-11l1-1v6l2 12-1 1h0c-1-2-1-4-2-7z" class="h"></path><path d="M435 266l1 1-1 1c-1 1-1 1-1 2-1 0-1 0-1 1h2v1c-3 3-5 4-8 6l-3 2h-1l1-1c-2 0-4 3-6 2 0-1 1-1 1-2l-3-2c0 1-1 1-1 1h0c0-1 1-2 2-2 2 0 5-2 6-3l5-2 2-2c1 0 1-1 2-1v1h0l3-3h0z" class="F"></path><path d="M418 277h1 1 1v2c1 0 2-1 3-1h3l-3 2h-1l1-1c-2 0-4 3-6 2 0-1 1-1 1-2l-3-2h2z" class="L"></path><path d="M432 268v1h0c-3 4-9 6-14 8h-2c0 1-1 1-1 1h0c0-1 1-2 2-2 2 0 5-2 6-3l5-2 2-2c1 0 1-1 2-1z" class="V"></path><g class="N"><path d="M453 222c0 1 0 1 1 1h0c1 1 1 1 1 2s0 1-1 2v1c-4 6-8 13-11 20v1l-1-1c-1 1-1 3-2 4v4-4c-1-1-1-2-2-3l1-4c2-2 3-5 4-8l10-15z"></path><path d="M483 182c1 2 1 3 1 5 0 1-1 2 0 3h0l-3 6c-2 6-3 13-6 19v-3h0c0-1 0-2 1-2v-1c-1 1-2 3-2 4-1 1-2 3-4 5 0 1-1 3-2 4s-2 1-3 1h-1c-2 0-5 1-6 2s-2 3-3 4l-1-1v-1c1-1 1-1 1-2s0-1-1-2h0c-1 0-1 0-1-1 0 0 1-1 1-2 4-7 9-14 13-21 2-3 4-6 6-8 2-3 4-6 7-9-1 3-1 5-2 7-1 4-3 7-4 10-1 2-2 3-2 5 1-2 3-5 4-7 3-5 5-10 7-15z"></path></g><path d="M454 220h1l1-2 1 1 4-7c1-1 1-1 1-2l2-2v-1h1 1c-1 1-1 1-1 2-1 0-1 0-1 1s-1 1-1 2l-1 1c-1 2-1 3-2 4 0 1-1 2-1 2-1 2-1 3-3 4h1c0 1 1 1 2 1v-1h1 1c1-1 1-1 2-1l1 1c-2 0-5 1-6 2s-2 3-3 4l-1-1v-1c1-1 1-1 1-2s0-1-1-2h0c-1 0-1 0-1-1 0 0 1-1 1-2z" class="L"></path><path d="M461 221c0-2 3-5 5-6 1-1 1-1 2 0 2 1 2 1 2 3 0 1-1 3-2 4s-2 1-3 1h-1l-1-1c-1 0-1 0-2 1l-1-1c0-1 1-1 1-1z" class="p"></path><path d="M461 221l3-3c0 1 1 2 1 3h0l-2 1c-1 0-1 0-2 1l-1-1c0-1 1-1 1-1z" class="J"></path><path d="M464 218c1-1 2-1 3-2h1c0 1 0 1-1 2 0 1-1 3-2 3h0c0-1-1-2-1-3z" class="T"></path><path d="M487 190h1c0 2-4 15-6 17-2 4-3 8-4 12l-6 18-4 12v-1-1c0-1-1-2-2-2 0-1 0-1-1-2l-3 7v-1h0-1c0-1 1-2 1-3l-2 2h-1c1-1 1-3 1-4l-2 4v-2c-1 0-1 0-1-1v-1h-1v1h-1c-1-5 0-9-1-14 0-1 0-1 1-2s2-3 3-4 4-2 6-2h1c1 0 2 0 3-1s2-3 2-4c2-2 3-4 4-5 0-1 1-3 2-4v1c-1 0-1 1-1 2h0v3c3-6 4-13 6-19l3-6h1 0v3c1-1 1-2 2-3z" class="w"></path><path d="M474 216c1 0 1 1 2 1l-1 3-2-2 1-2z" class="S"></path><path d="M487 190h1c0 2-4 15-6 17h-1v-1s1 0 1-1v-1c1 0 1-1 1-1l1-2h0v-2 1l-1 1v1c-1 2-2 4-3 5l-1 2c1-6 4-11 6-16 1-1 1-2 2-3z" class="e"></path><path d="M484 190h1 0v3c-2 5-5 10-6 16-1 2-2 5-3 8-1 0-1-1-2-1l1-1c3-6 4-13 6-19l3-6z" class="K"></path><path d="M470 218c2-2 3-4 4-5 0-1 1-3 2-4v1c-1 0-1 1-1 2h0v3l-1 1-1 2 2 2-10 23-3 7v-1h0-1c0-1 1-2 1-3l-2 2h-1c1-1 1-3 1-4l7-19v-1l-2-1c1 0 2 0 3-1s2-3 2-4z" class="h"></path><path d="M462 246l11-28 2 2-10 23-3 7v-1h0-1c0-1 1-2 1-3z" class="Q"></path><path d="M464 223h1l2 1v1l-7 19-2 4v-2c-1 0-1 0-1-1v-1h-1v1h-1c-1-5 0-9-1-14 0-1 0-1 1-2s2-3 3-4 4-2 6-2z" class="E"></path><path d="M459 232c0-2 1-3 2-4h1c0 2 0 5-1 7v-3h-2z" class="J"></path><path d="M459 232h2v3c0 3-1 5-2 7 0 2-1 3-1 4-1 0-1 0-1-1v-1l2-12zm5-9h1l2 1c-3 0-5 1-7 2-3 3-2 6-3 9 0 3-1 6-1 9v1h-1c-1-5 0-9-1-14 0-1 0-1 1-2s2-3 3-4 4-2 6-2z" class="I"></path><path d="M454 228l1 1c-1 1-1 1-1 2 1 5 0 9 1 14h1v-1h1v1c0 1 0 1 1 1v2l2-4c0 1 0 3-1 4h1l2-2c0 1-1 2-1 3h1 0v1l-5 13-2 6c1 1 3 0 3 2h0l-1 4-1 5c0 1-1 1-1 1-2 0-2 0-3 2h1l-2 2v-1-2h-1c-2 6-3 12-6 17-2 2-2 5-3 8l-7 18c0 2-1 4-1 6l-3 6c0 4-1 8-3 11v1h0c-1 1-2 4-3 5l-1-1c0 2-1 3-1 4l1 4-2 3v1c-1 1-2 5-2 8v1l-1-1h-1c-1 0-1-1-2 0v4h-1c-1-1-1-5-2-7v-1l-2-2-3-8-3-10c-1-3-2-7-2-11-1-1-1-2-2-3l1-1h1c1-2 1-3 1-5-1-1 0-5 0-7v-4l-1 1v-2l-1 2v-2l1-3c1-1 5-17 7-21h1c1-2 2-3 3-4l2-1h-1c0-1 1-2 1-3h0c0-2 1-3 3-4 2 1 4-2 6-2l-1 1h1l3-2c3-2 5-3 8-6v-1h-2c0-1 0-1 1-1 0-1 0-1 1-2l1-1-1-1h0v-2l3 1 2 2 2-1c-2-1-3-3-4-5l1-1-2-12 1 1c1 1 1 2 2 3v4-4c1-1 1-3 2-4l1 1v-1c3-7 7-14 11-20z" class="M"></path><path d="M423 329h2l-1 3-3 1 2-4z" class="C"></path><path d="M421 333l3-1-3 6c0 1-1 2-1 3h-1 0c-1 1-1 2-2 2l4-10z" class="V"></path><path d="M417 343c1 0 1-1 2-2h0 1l-3 8c0 1 0 2-1 3-2 0-2 0-3 1 1-3 3-7 4-10z" class="d"></path><path d="M432 309l1-1-1-1c-2 0-4 2-6 2 2-2 4-4 6-7v2l-1 1 1 1h3v1c0 2-1 4-2 6l-1-1v1-4z" class="H"></path><path d="M432 309v4l-7 16h-2l6-15c1-1 2-3 3-5z" class="D"></path><path d="M432 313v-1l1 1-11 32c-3 6-6 13-7 19h0l-2-3-2-7c-1-3-3-6-3-9 1 3 3 5 3 7 1 1 1 3 1 3h1v-2c1-1 1-1 3-1 1-1 1-2 1-3l3-8c0-1 1-2 1-3l3-6 1-3 7-16z" class="B"></path><path d="M403 320l1 3h0l2 2v2h0v5c1 1 1 2 1 3v1l1 1c-1 0-1 2-1 3l1 1v2l1 1-1 1c0 3 2 6 3 9l2 7 2 3 1 4v1c0 1 1 1 1 2v2c-1 0-1-1-2 0v4h-1c-1-1-1-5-2-7v-1l-2-2-3-8-3-10c-1-3-2-7-2-11-1-1-1-2-2-3l1-1h1c1-2 1-3 1-5-1-1 0-5 0-7v-2z" class="F"></path><path d="M415 373h-1c0-2-1-4 0-6h0l1 1s0 1 1 1c0 1 1 1 1 2v2c-1 0-1-1-2 0z" class="L"></path><path d="M403 329c1 5 1 10 3 15v1l-1-1v1c-1-1-1-2-1-3s-1-2-1-3v3l1 7c-1-3-2-7-2-11-1-1-1-2-2-3l1-1h1c1-2 1-3 1-5z" class="j"></path><path d="M403 320l1 3h0l2 2v2h0v5c1 1 1 2 1 3v1l1 1c-1 0-1 2-1 3l1 1v2l1 1-1 1c0 3 2 6 3 9l2 7h0c-1 0-1 0-1-1-1 0-1-1-1-2s-1-3-2-4c0-1 0-2-1-3v-2h0v-1h-1v-1h0v-1h0c-1-1-1-1-1-2-2-5-2-10-3-15-1-1 0-5 0-7v-2z" class="J"></path><path d="M438 297l2-5c1 1 1 1 2 1v1l-1 1 1 1h2 0c-1 2-1 4-2 5-2 3-4 5-5 7-1 3-1 6-2 9l-15 41v1l2-2 1 4-2 3v1c-1 1-2 5-2 8v1l-1-1h-1v-2c0-1-1-1-1-2v-1l-1-4h0c1-6 4-13 7-19l11-32c1-2 2-4 2-6v-1h-3l-1-1 1-1c2-2 5-4 6-7z" class="c"></path><path d="M420 358v1l2-2 1 4-2 3v1c-1 1-2 5-2 8v1l-1-1h-1v-2c0-1-1-1-1-2v-1-2h1l3-8z" class="d"></path><path d="M417 366h0c2 0 1-3 3-4h0c0 3-1 7-3 9 0-1-1-1-1-2v-1-2h1z" class="R"></path><path d="M420 358v1l2-2 1 4-2 3c0-1 1-2 0-3-1 0-1 1-1 1h0c-2 1-1 4-3 4h0l3-8z" class="S"></path><path d="M435 264l3 1 2 2 2-1 2 1c1 1 2 1 4 2h1v2l-2 3c-1 4-1 7-2 11 0 1-1 5 0 6v1c0 1-1 1-2 2h-1v-1c-1 0-1 0-2-1l-2 5c-1 3-4 5-6 7v-2l3-12v-3s-1 0-1-1l1-2c-1 0-1 0-2-1 1-1 2-3 2-4l1-2c-1 0-1 0-1-1 1-1 1-1 2-1-1 0-1 0-1-1 0 0 0-1-1-2h0v-1h-2c0-1 0-1 1-1 0-1 0-1 1-2l1-1-1-1h0v-2z" class="p"></path><path d="M441 285l-3-6c2 1 3 2 5 3v2c-1 1-1 1-2 1z" class="J"></path><path d="M436 277c1 3 2 5 3 7l1 1h-1-1c-1 0-3 0-4 1l1-2c-1 0-1 0-2-1 1-1 2-3 2-4l1-2z" class="N"></path><path d="M443 282v-1c1-2 1-3 1-5 0-1 1-1 1-2h0c1-2 2-2 4-3l-2 3c-1 4-1 7-2 11h-2v-1-2z" class="K"></path><path d="M443 284v1h2c0 1-1 5 0 6v1c0 1-1 1-2 2h-1v-1c-1 0-1 0-2-1 1-2 1-5 1-7 1 0 1 0 2-1z" class="F"></path><path d="M445 285c0 1-1 5 0 6v1c0 1-1 1-2 2h-1v-1c1-2 1-5 1-8h2z" class="V"></path><path d="M440 285v4l-3 7 1 1c-1 3-4 5-6 7v-2l3-12v-3s-1 0-1-1c1-1 3-1 4-1h1 1z" class="K"></path><path d="M440 285v4l-3 7-1 1c0-2 0-2 1-4 0-2 2-5 1-8h1 1zm-5-21l3 1 2 2 2-1 2 1c1 1 2 1 4 2h-2c0 1-1 1-2 2s-3 4-4 4h-3c-1 0-1 0-1-1 0 0 0-1-1-2h0v-1h-2c0-1 0-1 1-1 0-1 0-1 1-2l1-1-1-1h0v-2z" class="C"></path><path d="M440 267l2-1 2 1-1 1v1h-1-1l-1-1h0v-1z" class="D"></path><path d="M435 268v2h2 0l3-2 1 1h1c-2 2-4 3-6 2l-1 1h0v-1h-2c0-1 0-1 1-1 0-1 0-1 1-2z" class="O"></path><path d="M435 264l3 1 2 2v1h0l-3 2h0-2v-2l1-1-1-1h0v-2z" class="V"></path><path d="M435 266h1c1 1 1 2 1 3v1h0-2v-2l1-1-1-1z" class="Q"></path><path d="M435 272c1 1 1 2 1 2 0 1 0 1 1 1-1 0-1 0-2 1 0 1 0 1 1 1l-1 2c0 1-1 3-2 4-1 3-2 5-4 8l2-6h0c-2 1-3 2-5 3-4 3-8 5-11 10l-6 9c-2 4-4 8-6 13v2-4l-1 1v-2l-1 2v-2l1-3c1-1 5-17 7-21h1c1-2 2-3 3-4l2-1h-1c0-1 1-2 1-3h0c0-2 1-3 3-4 2 1 4-2 6-2l-1 1h1l3-2c3-2 5-3 8-6h0z" class="K"></path><path d="M418 281c2 1 4-2 6-2l-1 1h1c-3 4-5 6-9 8h-1c0-1 1-2 1-3h0c0-2 1-3 3-4z" class="O"></path><path d="M413 289v1c-3 2-4 6-6 9 0 1-1 4-1 5v-1h1v-1-1-1c1-1 2-4 3-5v-1l1-1 1-1h0l2-1h0 0c-2 2-3 3-3 5-3 3-4 6-5 10s-3 8-3 12l-1 1v-2l-1 2v-2l1-3c1-1 5-17 7-21h1c1-2 2-3 3-4z" class="H"></path><path d="M411 296c3-4 8-7 12-11l2-2c3 0 6-2 8-4 0 1 0 2-1 3v2 1h-1 0c-2 1-3 2-5 3-4 3-8 5-11 10l-6 9c-2 4-4 8-6 13v2-4c0-4 2-8 3-12s2-7 5-10z" class="D"></path><path d="M409 307h0c0-1 0-1 1-2h-1v-1c2-2 3-5 5-7v1-1h1v1l-6 9z" class="C"></path><path d="M454 228l1 1c-1 1-1 1-1 2 1 5 0 9 1 14h1v-1h1v1c0 1 0 1 1 1v2l2-4c0 1 0 3-1 4h1l2-2c0 1-1 2-1 3h1 0v1l-5 13-2 6c1 1 3 0 3 2h0l-1 4-1 5c0 1-1 1-1 1-2 0-2 0-3 2h1l-2 2v-1-2h-1c-2 6-3 12-6 17-2 2-2 5-3 8l-7 18c0 2-1 4-1 6l-3 6c0 4-1 8-3 11v1h0c-1 1-2 4-3 5l-1-1c0 2-1 3-1 4l-2 2v-1c5-13 10-27 15-41 1-3 1-6 2-9 1-2 3-4 5-7 1-1 1-3 2-5h0-2l-1-1 1-1h1c1-1 2-1 2-2v-1c-1-1 0-5 0-6 1-4 1-7 2-11l2-3v-2h-1c-2-1-3-1-4-2l-2-1c-2-1-3-3-4-5l1-1-2-12 1 1c1 1 1 2 2 3v4-4c1-1 1-3 2-4l1 1v-1c3-7 7-14 11-20z" class="H"></path><path d="M426 346c0 1 0 2 1 2h0v1h0c-1 1-2 4-3 5l-1-1 3-7z" class="D"></path><path d="M455 261h0l1 1c-1 3-3 5-4 7 0-1-1-2-1-2h-1l-1 1v-1c3-1 4-3 6-6z" class="F"></path><path d="M430 337c0 4-1 8-3 11h0c-1 0-1-1-1-2 1-3 3-6 4-9z" class="G"></path><path d="M449 268l1-1h1s1 1 1 2l-3 7h-1l-1-2 2-3v-2-1z" class="X"></path><path d="M450 282c1-3 3-8 4-10h1l1 1-2 4c-1 1-2 3-2 5v1h1l-2 2v-1-2h-1z" class="e"></path><path d="M456 273c0 1-1 1 0 2h1l-1 5c0 1-1 1-1 1-2 0-2 0-3 2v-1c0-2 1-4 2-5l2-4z" class="K"></path><path d="M447 274l1 2h1c-1 5-2 11-4 16v-1c-1-1 0-5 0-6 1-4 1-7 2-11z" class="T"></path><path d="M458 246v2l2-4c0 1 0 3-1 4h1l2-2c0 1-1 2-1 3v1l-2 4-3 8-1-1h0l-2-1c-1 2-3 5-5 5h0l-2 1h0v-1c2-4 4-7 5-11h0c1 1 1 1 1 2h1c0-1 0-2 1-2h1c1-1 1-3 1-4 0-2 0-3 1-5 0 1 0 1 1 1z" class="M"></path><path d="M457 253h0c1-1 1-2 2-3h2l-2 4h-1l-1-1h0z" class="F"></path><path d="M457 253h0l1 1h1l-3 8-1-1h0l-2-1c2-2 3-4 4-7z" class="O"></path><path d="M454 254h1c-2 4-4 7-7 11l-2 1h0v-1c2-4 4-7 5-11h0c1 1 1 1 1 2h1c0-1 0-2 1-2z" class="E"></path><path d="M437 248l1 1c1 1 1 2 2 3v4-4c1-1 1-3 2-4l1 1c-1 2-1 4-1 6l1 1c1 1 0 5 1 7l-1 2h1 2v1h0l2-1h0c2 0 4-3 5-5l2 1c-2 3-3 5-6 6v1 1h-1c-2-1-3-1-4-2l-2-1c-2-1-3-3-4-5l1-1-2-12z" class="G"></path><path d="M438 261l1-1c1 2 1 3 2 4l1 1c0 1 2 1 3 2h4v1 1h-1c-2-1-3-1-4-2l-2-1c-2-1-3-3-4-5zm2-5v-4c1-1 1-3 2-4l1 1c-1 2-1 4-1 6l1 1c1 1 0 5 1 7l-1 2c-2-2-3-7-3-9z" class="I"></path><path d="M454 228l1 1c-1 1-1 1-1 2 1 5 0 9 1 14h1v-1h1v1c-1 2-1 3-1 5 0 1 0 3-1 4h-1c-1 0-1 1-1 2h-1c0-1 0-1-1-2h0c-1 4-3 7-5 11h-2-1l1-2c-1-2 0-6-1-7l-1-1c0-2 0-4 1-6v-1c3-7 7-14 11-20z" class="X"></path><path d="M454 228l1 1c-1 1-1 1-1 2 1 5 0 9 1 14h1v-1h1v1c-1 2-1 3-1 5 0 1 0 3-1 4h-1c-1 0-1 1-1 2h-1c0-1 0-1-1-2h0c1-6-1-11-1-17-4 6-7 12-7 19l-1-1c0-2 0-4 1-6v-1c3-7 7-14 11-20z" class="S"></path><path d="M452 240c-1-2-1-5 0-6v-1c1 1 1 6 1 7h-1 0z" class="c"></path><path d="M452 240h0 1l1 9c1 2 1 3 0 5-1 0-1 1-1 2-1-2 0-4 0-6 0-4-1-7-1-10z" class="F"></path><path d="M416 277l3 2c0 1-1 1-1 2-2 1-3 2-3 4h0c0 1-1 2-1 3h1l-2 1c-1 1-2 2-3 4h-1c-2 4-6 20-7 21l-1 3v2l1-2v2l1-1v4c0 2-1 6 0 7 0 2 0 3-1 5h-1l-1 1c1 1 1 2 2 3 0 4 1 8 2 11l3 10 3 8 2 2c-1 1-1 1-1 2s0 3-1 4c0 9-3 17-6 25l-9 26h0l-2 4c-1 3-1 8-4 11l-1 1c0 3 0 5-2 8l-1-1v2l-1 1v1c0 2-2 4-1 5l-2 2v-2c1-3 2-8 3-11v-2-1c0 1-1 1-1 2 2-7 3-12 0-19-1-1-2-3-3-5h0v-1c-1-2-1-1-3-2-2-5-4-10-9-15h0-4l-3 1h-3l2-11c1-5 5-9 5-14l5-9c1-4 2-7 4-10 0 0 1-4 2-4l22-55 5-13 2-1v3h0l3-6 2-3 2-3c0 1 0 1 1 1l1-1c1 0 1 0 1-1 0 0 1 0 1-1z" class="e"></path><path d="M379 413h1v1c0 1 0 1 1 1v1 1c1 1 1 1 1 2l-1-1h0v1c-1-1-3-4-2-6z" class="j"></path><path d="M407 378l1 1-3 9h0v-2c0-1 0-2 1-2v-1c-1 1-1 2-2 3h0c0-3 2-5 3-8z" class="O"></path><defs><linearGradient id="r" x1="384.935" y1="444.645" x2="387.105" y2="444.684" xlink:href="#B"><stop offset="0" stop-color="#181716"></stop><stop offset="1" stop-color="#25191c"></stop></linearGradient></defs><path fill="url(#r)" d="M384 447c1-4 3-8 3-11 0-1 0-2 1-2h0c-1 2 0 4 0 6-1 1 0 1 0 2 0 3 0 5-2 8l-1-1v2l-1 1v1c0 2-2 4-1 5l-2 2v-2c1-3 2-8 3-11z"></path><path d="M369 383c0-1 1-2 1-2v-1l1 1c0 1-1 2-1 3v8l-1-2v1-1 5c0-1-1-1-1-2h0v4c-1 1-1 1-1 3 0 1 0 1 1 2v2h0l-3-5v-7l2-5 1-2 1-2z" class="D"></path><path d="M368 385v7c-2 1-3 5-3 7v-7l2-5 1-2z" class="C"></path><path d="M381 384c0 3-2 6-3 9-1 2-1 3-2 5l-1 2c0 1 1 2 1 3 3 4 6 8 8 13 0 1 1 2 1 3l-1 1v-1c-1-4-4-8-7-12-2-5-6-10-7-15v-8h1v2h0v3c0 3 1 8 3 10 1-1 6-14 7-15z" class="E"></path><path d="M408 379c0-1 1-3 2-4 0 9-3 17-6 25l-9 26h0-1c-1 1-1 2-1 3s-1 2-1 3c-1 1-1 2-2 3h0v-2c0-1 0-1 1-2v-1c1-3 2-6 3-8 0-1 0-2 1-3v-1l1-1v-1l6-18c1-4 3-7 3-10l3-9z" class="V"></path><defs><linearGradient id="s" x1="407.766" y1="399.78" x2="385.189" y2="383.064" xlink:href="#B"><stop offset="0" stop-color="#121514"></stop><stop offset="1" stop-color="#201516"></stop></linearGradient></defs><path fill="url(#s)" d="M405 362c1-1 0-4 0-5h0l2 2 3 8 2 2c-1 1-1 1-1 2s0 3-1 4-2 3-2 4l-1-1c-1 3-3 5-3 8h0c-1 2-2 4-2 6l-4 10-6 16c-1 4-3 8-4 11v2 1c-1-4-2-8-4-12l1-1c0-1-1-2-1-3h1v-1c2-11 8-21 12-31 1-2 2-8 4-9 0 0 0-1 1-1v-1l-1-1-1-1v-1l2-1c1-1 1-1 1-2v-2c0-1 1-1 1-2l1-1z"></path><path d="M410 367l2 2c-1 1-1 1-1 2s0 3-1 4-2 3-2 4l-1-1c0-1 0-2 1-3 1-3 2-5 2-8z" class="e"></path><path d="M402 373v2c-2 9-6 16-9 24-2 6-4 13-7 19l-1 1c0-1-1-2-1-3h1v-1c2-11 8-21 12-31 1-2 2-8 4-9 0 0 0-1 1-1v-1z" class="C"></path><path d="M394 345c0 1 0 3 1 3 1 2 1 5 2 7 2 4 4 10 3 15v1l1 1 1 1v1c-1 0-1 1-1 1-2 1-3 7-4 9-4 10-10 20-12 31v1h-1c-2-5-5-9-8-13 0-1-1-2-1-3l1-2c1-2 1-3 2-5 1-3 3-6 3-9l1-2c0-1 0-2 1-2v-2-1c1-1 1-2 2-3 0-1 1-3 1-5h1v-2l2-4 1-2s0-1 1-1v-2c0-1 1-2 1-3l1-2v-1c1-2 1-4 1-7z" class="e"></path><defs><linearGradient id="t" x1="377.511" y1="336.642" x2="399.118" y2="342.927" xlink:href="#B"><stop offset="0" stop-color="#131414"></stop><stop offset="1" stop-color="#561819"></stop></linearGradient></defs><path fill="url(#t)" d="M416 277l3 2c0 1-1 1-1 2-2 1-3 2-3 4h0c0 1-1 2-1 3h1l-2 1c-1 1-2 2-3 4h-1c-2 4-6 20-7 21l-1 3v2l1-2v2l1-1v4c0 2-1 6 0 7 0 2 0 3-1 5h-1l-1 1c1 1 1 2 2 3 0 4 1 8 2 11l3 10-2-2h0c0 1 1 4 0 5l-1 1c0 1-1 1-1 2v2c0 1 0 1-1 2l-2 1c1-5-1-11-3-15-1-2-1-5-2-7-1 0-1-2-1-3 0 3 0 5-1 7v1l-1 2c0 1-1 2-1 3v2c-1 0-1 1-1 1l-1 2-2 4v2h-1c0 2-1 4-1 5-1 1-1 2-2 3v1 2c-1 0-1 1-1 2l-1 2c-1 1-6 14-7 15-2-2-3-7-3-10v-3h0v-2h-1c0-1 1-2 1-3l-1-1v1s-1 1-1 2l-1 2-1 2-2 5v7l3 5h-4l-3 1h-3l2-11c1-5 5-9 5-14l5-9c1-4 2-7 4-10 0 0 1-4 2-4l22-55 5-13 2-1v3h0l3-6 2-3 2-3c0 1 0 1 1 1l1-1c1 0 1 0 1-1 0 0 1 0 1-1z"></path><path d="M390 338h1s1 2 1 3h0c-1 1-1 1-1 2v-1l-1-3v-1z" class="J"></path><path d="M372 373c0 1 1 2 1 2l-2 6-1-1v1s-1 1-1 2l3-10z" class="Q"></path><path d="M395 348v-2c0-2-1-4-2-5l1-1 1 3v-2-1c0-1 0 0-1-1v-2-1-1-2 2c1 1 1 2 1 3v1h0c0 1 0 2 1 3h0v2 1 1c1 0 0 1 0 2h-1z" class="d"></path><path d="M394 333v-6c1-1 0-2 1-3v-4c0 2 0 3 1 4v3c0 2 0 4 1 6h0v4h-1v2h0-1 0v-1c0-1 0-2-1-3v-2z" class="D"></path><path d="M396 339v-3-1h0c-1-3-1-5 0-8 0 2 0 4 1 6h0v4h-1v2z" class="S"></path><path d="M383 347l1 1c0 2-1 3-2 5l-5 11c-1 4-2 8-4 11 0 0-1-1-1-2s1-2 1-3c3-8 7-16 10-23z" class="V"></path><path d="M383 347c1-2 3-4 4-7 1-2 2-5 2-7l2-2c0 1-1 1 0 1h0 0c0 2-1 4-2 6h1v1c-3 5-5 11-7 16-3 8-7 15-10 23 0 3-1 6-2 8h0v-2h-1c0-1 1-2 1-3l2-6c2-3 3-7 4-11l5-11c1-2 2-3 2-5l-1-1z" class="d"></path><path d="M395 320l3-6c1 0 2 0 2-1 1-1 1-2 2-3 1-4 2-8 3-11 0-2 1-4 2-5 0-1 1-2 1-3l2-2h1c1-1 2-1 2-2l2-2h0c0 1-1 2-1 3h1l-2 1c-1 1-2 2-3 4h-1c-2 4-6 20-7 21 0-1 0-1-1-1-1 1-1 3-1 5-1 3-1 8-1 12l-1 1h0l-1 1h0v1h0c-1-2-1-4-1-6v-3c-1-1-1-2-1-4z" class="C"></path><path d="M365 380l5-9c1-4 2-7 4-10 0 3-2 6-3 9 0 2-1 4-2 5-1 2-1 4-2 5 0 1 0 1-1 2l-3 6c-1 2-2 5-2 7l-1 4v3-1l1-3c0-1 0-1 1-2v-1c1-1 1-2 2-3v-1l1-1v-2l2-1-2 5v7l3 5h-4l-3 1h-3l2-11c1-5 5-9 5-14z" class="H"></path><path d="M361 405l-1-1c1-4 3-7 5-12v7l3 5h-4l-3 1z" class="d"></path><path d="M397 333v-1h0l1-1h0l1-1c0-4 0-9 1-12 0-2 0-4 1-5 1 0 1 0 1 1l-1 3v2l1-2v2l1-1v4c0 2-1 6 0 7 0 2 0 3-1 5h-1l-1 1c1 1 1 2 2 3 0 4 1 8 2 11l3 10-2-2h0c0 1 1 4 0 5l-1 1c0 1-1 1-1 2v2c0 1 0 1-1 2l-2 1c1-5-1-11-3-15-1-2-1-5-2-7h0 1c0-1 1-2 0-2v-1-1-2h0c-1-1-1-2-1-3h1 0v-2h1v-4z" class="e"></path><path d="M401 317v2l1-2v2l1-1v4c0 2-1 6 0 7 0 2 0 3-1 5h-1c-1-6-1-11 0-17zm-1 18c1 1 1 2 2 3 0 4 1 8 2 11l3 10-2-2h0c0 1 1 4 0 5 0-4-1-6-2-10-1-5-3-11-3-17z" class="K"></path><path d="M396 339v-2h1c1 4 5 24 7 26 0 1-1 1-1 2v2c0 1 0 1-1 2l-2 1c1-5-1-11-3-15-1-2-1-5-2-7h0 1c0-1 1-2 0-2v-1-1-2h0c-1-1-1-2-1-3h1 0z" class="Q"></path><path d="M397 355l1-1c1 1 2 5 3 8v2c1 0 1 1 1 1 0 1 0 1 1 2 0 1 0 1-1 2l-2 1c1-5-1-11-3-15z" class="S"></path><path d="M371 386c1-2 2-5 2-8 3-8 7-15 10-23 2-5 4-11 7-16l1 3v1c0-1 0-1 1-2l2 4c0 3 0 5-1 7v1l-1 2c0 1-1 2-1 3v2c-1 0-1 1-1 1l-1 2-2 4v2h-1c0 2-1 4-1 5-1 1-1 2-2 3v1 2c-1 0-1 1-1 2l-1 2c-1 1-6 14-7 15-2-2-3-7-3-10v-3z" class="O"></path><path d="M391 342v1l1 4h-1-1v-4l1-1z" class="G"></path><path d="M391 343c0-1 0-1 1-2l2 4c0 3 0 5-1 7v1l-1 1v-7l-1-4z" class="M"></path></svg>