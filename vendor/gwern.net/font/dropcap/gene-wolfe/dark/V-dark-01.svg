<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="60 36 580 628"><!--oldViewBox="0 0 688 752"--><style>.B{fill:#414141}.C{fill:#6a696a}.D{fill:#4b494a}.E{fill:#878687}.F{fill:#292929}.G{fill:#4c4a4b}.H{fill:#cbcacb}.I{fill:#898888}.J{fill:#949494}.K{fill:#aba9aa}.L{fill:#d8d7d8}.M{fill:#2f2f2f}.N{fill:#c2c1c2}.O{fill:#5b5b5b}.P{fill:#797878}.Q{fill:#efefef}.R{fill:#bab9b9}.S{fill:#d1d1d1}.T{fill:#e8e8e8}.U{fill:#1d1c1d}.V{fill:#eae9ea}.W{fill:#b4b2b2}.X{fill:#242323}.Y{fill:#f3f2f2}.Z{fill:#727172}</style><path d="M251 351c2-1 4-2 5-2l-3 3-2-1z" class="M"></path><path d="M251 367c1-1 2-3 3-3l1 1c-1 1-1 3-1 4-1-1-2-2-3-2z" class="O"></path><path d="M494 276c1 0 2 1 3 2h-1 0c-1 1-2 1-3 1 0-1 0-2 1-3h0z" class="F"></path><path d="M169 357l3 2c-1 0-2 0-3 2l-2-2 2-2z" class="J"></path><path d="M431 371h5l1 1-1 1h-2c-1 0-2-1-3-2z" class="D"></path><path d="M414 465l3-1c0 1 0 2 1 3v1 1h-2l1-3-1-1h-2z" class="B"></path><path d="M401 489c-1 1-2 2-4 2 0 0-1 0-1-1s1-3 2-4v2l-1 1 2 1v-1h2z" class="U"></path><path d="M172 359l5 1c-1 0-5 0-6 1v1l-2-1c1-2 2-2 3-2z" class="E"></path><path d="M422 417c0 1-2 3-3 3 0-2 0-4 1-6l1 1c1 1 0 1 1 2z" class="X"></path><path d="M246 429l3 1-1 1v2l-4-3 2-1z" class="D"></path><path d="M240 426l6 3-2 1c-2-1-3-2-4-4z" class="B"></path><path d="M391 515h-1c0 1-3 3-4 3 1-1 3-2 3-4h-1c1-2 0-4 1-6 0 2 0 4 1 6l1 1z" class="X"></path><path d="M405 199l1 1c0 1-1 1 0 2l2 2v1h-4v-1l1-5z" class="E"></path><path d="M243 327h3v3l-2 2h0v-3c-1-1-1-1-1-2z" class="F"></path><path d="M422 412l2 2v1l-2 2c-1-1 0-1-1-2l-1-1 2-2z" class="M"></path><path d="M251 351l2 1c-1 1-2 2-3 2h-3l4-3zm-70-120c0-1 0-1 1 0 2 1 2 3 4 5h0-3c0-2-1-4-2-5h0z" class="D"></path><path d="M163 354l6 3-2 2-5-4 1-1z" class="E"></path><path d="M429 405v3h1l-3 3v-1s-1-1-1-2l3-3z" class="O"></path><path d="M449 326c0 1 1 2 0 3v3c-2-1-3-2-4-3v-1c1 0 2-1 3 0h0c1-1 1-1 1-2h0zM194 202c0-1 0-1 1-2l1-1c0 1 1 1 1 2l-1 9v-4l-1-3-1-1z" class="F"></path><path d="M440 353c-1-1-2-2-3-2s-1 0-1-1v-1c1 1 2 1 2 1 3 0 4 1 6 3-2 0-3-1-4 0z" class="M"></path><path d="M281 443c2 1 4 0 5 1s1 1 1 2c-1 1-2 1-2 3l-2-1c1-2 1-3 0-4l-2-1z" class="F"></path><path d="M155 351h-1c-2-1-3-3-4-4 0-1 0-1 1-2v-1h1c0 1-1 1 0 2 0 1 1 1 1 1 1 1 1 1 1 2s1 1 1 2z" class="X"></path><path d="M535 349c2 0 4-2 7-2l-6 5v-1h-1v-1-1z" class="D"></path><path d="M417 464l7-5c-2 3-6 5-6 9v-1c-1-1-1-2-1-3z" class="M"></path><path d="M411 452c-1 2-3 3-5 5 0 1 0 1-1 1v-1h0l1-1c1 0 1 0 1-2l-1-1v-1l1-1 2 1h2 0zM238 311h1l3 3v1h-2c-2-1-2-2-2-4z" class="X"></path><path d="M535 349c1-2 3-2 4-3s1-2 2-3c1 1 1 2 2 3h0v1h-1c-3 0-5 2-7 2zm-35 27c1 0 2 0 3-1l1 1c1 1 0 2 2 3h0-2c-2-1-4-1-6-1 1 0 1 0 2-2z" class="C"></path><path d="M402 488c0-1 0-1 1-1 1-1 2-1 4-1l-8 8h0v-1c0-1 2-3 4-4l-1-1z" class="U"></path><path d="M236 301c1 1 4 2 4 3v1c1 1 1 1 1 2v1l-4-3h0 0c0-1 0-3-1-4zm259-48c0-1 0-3 1-3l2-2c0-2-1-1-2-3h1v1c1 1 2 1 2 3v2h0l-4 2z" class="F"></path><path d="M400 238c-3-3-6-4-11-5-1-1-3-1-4-1h2c1 0 3 0 5 1 3 0 5 2 8 2l1 1v1l-1 1z" class="J"></path><path d="M426 408c0 1 1 2 1 2v1l-3 4v-1l-2-2 4-4z" class="D"></path><path d="M184 249c1 0 7 2 9 3-1 1-1 1-2 1l-9-4h2z" class="O"></path><path d="M272 414c2 3 3 5 4 8-2-1-5-3-6-5 1-1 1-1 1-2 0 0 0-1 1-1z" class="F"></path><path d="M259 429c2 1 3 2 3 4 0 1-1 2-3 2l-2-1 1-2-1-1c1 0 2 1 3 1l-1-3z" class="I"></path><path d="M251 367c1 0 2 1 3 2-1 0-1 1-1 2 0 0 0 1-1 2l1 1c-1 1-1 2 0 3v1h-1l-1-2v-9z" class="G"></path><path d="M413 214c1 5 0 11 0 17h0c-1-2-1-6-1-8l-1-3-1-2c1 0 2 2 2 3h0c1 1 0 3 1 5v-5-7h0z" class="X"></path><path d="M188 353v1c0 1-1 2-2 3-3 1-5 1-7 0h-2l1-1h1c4 0 6 0 9-3z" class="J"></path><path d="M453 323v2l1 1-4 4v1l1 1h-2v-3c1-1 0-2 0-3 1-1 3-2 4-3zm74 33l8-5h1v1c-1 1-2 3-4 4h-1-4z" class="G"></path><path d="M409 450c-1 0-2-1-2-2 1-1 3-2 3-5h0 2-1v1h2v1l-2 2c0 1-1 2-2 3z" class="B"></path><path d="M478 245l3-3c0 1 0 2-1 3v2h0l-5 5h0-1c1-1 1-1 1-2 1-2 2-3 3-5z" class="M"></path><path d="M286 486c2 1 4 1 6 1l2 1-2 1s-1 1 0 2c0 1 2 2 2 2l2 2s1 1 1 2l-1 1c0-1-1-2-1-2-1-2-2-3-3-4l-6-6z" class="X"></path><path d="M282 452l1-1v1h2l1 1h2 0l1 6c-3-2-6-4-7-7z" class="F"></path><path d="M314 85c2 0 4-1 7-1-3 3-7 4-10 6h-1 0-3c1-1 3-2 5-3 1 0 2 0 3-1l-1-1z" class="J"></path><path d="M478 238c0-3 2-4 3-6 1-1 1-2 2-3v3l-1 1h1c-1 2-3 5-3 7v1h-1l1-2-2-1z" class="X"></path><path d="M441 375c-1 1-1 2-1 2-2 2-3 3-6 4v-1c2 0 4-2 5-3 1-2 1-5 0-8 0-1-1-2 0-4v-1c1 1 2 2 2 4 1 2 0 5 0 7z" class="D"></path><path d="M152 344l1 1 2 2h2l1 1c-1 1-1 1-1 2l2 1-1 3-3-3c0-1-1-1-1-2s0-1-1-2c0 0-1 0-1-1-1-1 0-1 0-2z" class="G"></path><path d="M162 169l1 1v1c2 6 2 15 1 21l-1 1v-7c0-5 0-11-1-17z" class="R"></path><path d="M275 469c-2-1-1-2-2-5l-1-1c-1-2-3-3-4-4 1 0 2 0 2 1 3 1 5 3 7 4-1 2-1 4-2 5z" class="M"></path><path d="M142 246v7c0 6 2 10 5 14h0c-5-6-7-11-7-19v1h1l1-3z" class="N"></path><path d="M305 194c-1-1-1-3-1-4s1-2 2-3c1 2 2 2 4 3-1 1-3 3-4 5l-1-1h0z" class="E"></path><path d="M261 450v3c-1 0-1 1-1 1s-1 0-2 1c0 0-4 1-5 2-3 1-6 4-8 7 1-4 5-8 8-9 2-1 3-1 5-2l3-3z" class="J"></path><path d="M254 369c0-1 0-3 1-4v3c2 0 2-1 4-1h0l-1 1-2 2c0 1 0 1 1 2h0c1 1 1 0 2 0v1c0 1-1 1-2 1s0 0-1-1l-1-1c-1 0-1 1-2 2l-1-1c1-1 1-2 1-2 0-1 0-2 1-2z" class="F"></path><path d="M434 452h0c2 1 3 2 5 3 4 1 7 5 9 8-2-1-3-2-5-4-3-3-8-5-13-5 1 0 1-1 2-1 0 0 1 0 2-1z" class="I"></path><path d="M429 455v-1h0v-3c0-1 0-2 1-4 0-1 1-1 2-2h0c0 4 0 4 2 7-1 1-2 1-2 1-1 0-1 1-2 1l-1 1z" class="P"></path><path d="M230 296c2 1 4 3 6 5 1 1 1 3 1 4h0l-6-3 1-1-2-5z" class="M"></path><path d="M545 198l1 1-3 6c-2 3-3 5-5 7l-2-1h0 0c1-1 2-2 2-3h-1c4-3 6-6 8-10z" class="Q"></path><path d="M234 312l1-1c2 3 3 7 5 10v2h-1l-1-1h0c-2-1-2-2-3-4-1-1-1-4-1-6zm7 32c1-1 1-2 1-3 1 0 3-1 4-1l-1 2 1 1 1-2h1c0 1 0 1-1 2l1 1 2 1-1 1h-1c-2 0-4 0-5-1l-2-1z" class="D"></path><path d="M243 345c1-1 2-2 3-1h0l2 2h1-1c-2 0-4 0-5-1z" class="G"></path><path d="M396 226l-3-2c-3-1-6-1-8 0s-4 2-5 4-1 3 0 5h-1l-1-1c0-2 1-4 2-5 1-3 4-4 6-5h1c3 0 5 0 8 1 0 1 1 1 1 2v1z" class="E"></path><path d="M474 265c0-2 0-3-1-4 4-5 7-10 11-14-2 6-7 10-9 16 0 1 0 1-1 2z" class="G"></path><path d="M530 169v24h-1c-2-7-1-18 1-24zm21 79l1 2c1-1 0-1 1-1 0 7-3 14-8 20-1 0-2 1-3 2 0-1 1-1 1-1 4-4 6-9 7-15 1-2 1-5 1-7z" class="E"></path><path d="M477 298v-3c-1-1-2 0-2-1s1-1 1-2c1-1-1-1 1-3v1c2 2 3 2 6 3h1c-2 1-3 2-5 3l1 1c-1 0-2 0-3 1z" class="R"></path><path d="M471 282c2-2 4-3 6-4-1 2-1 2-2 3v1h4l2-1v1l-4 4s-1 0-1-1c-1 0-1-2-3-3 0 1 0 2-1 2-1 1-1 2-1 4-1-2-1-3 0-4v-2z" class="U"></path><path d="M87 149h0c3 1 7 0 10-1 2-1 3-3 5-4h0c0 2-1 3-2 4-2 1-4 3-7 4-1-1-3-1-4-1h-1c-1 0-2-1-3-1v-1h2z" class="Y"></path><path d="M312 210c-1 1-1 2-2 2l-1-1c-1-1-1-1-1-2l-1-1-1-5c0-2 1-5 2-6 0 6 0 9 4 13z" class="F"></path><path d="M404 197h2l1 1c0 1 0 1 1 1 2 2 3 5 4 7l1 1c-1 1-1 2-1 3-1-2-2-4-4-5v-1l-2-2c-1-1 0-1 0-2l-1-1c1 0 1 0 1-1l-2-1z" class="C"></path><path d="M279 139l1-3h1v10l1 7c-1 1-1 2-1 3l-1-3c0-1 0-3-1-4v-10z" class="L"></path><path d="M280 153c0-2 0-5 1-7l1 7c-1 1-1 2-1 3l-1-3z" class="T"></path><defs><linearGradient id="A" x1="197.204" y1="206.754" x2="192.627" y2="212.896" xlink:href="#B"><stop offset="0" stop-color="#414042"></stop><stop offset="1" stop-color="#5b5c5a"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M194 202l1 1 1 3v4l2 10c-1 0-1-1-2-1-1-2-1-5-2-7 0-2 0-5-1-6 0-1 1-2 1-4z"></path><path d="M424 403c0-3-1-7 0-10 0 0 0-1 1-2s1-2 2-3v-2c1 0 1-1 2-2h4c0 1-1 0-2 1 0 0 0 1-1 1-2 1-2 3-3 5 0 1-1 2-2 4 0 2 0 5-1 8z" class="F"></path><path d="M483 377h1c1 1 2 1 3 1h0c2 0 3 0 5 1l-5 1c-1 1-6 3-8 3l3-1c0-1 1-1 1-1v-1h-4 0-1-2l1-1 5-1 1-1z" class="B"></path><path d="M483 377h1c1 1 2 1 3 1h0c2 0 3 0 5 1l-5 1c-1 0-2-1-4-1-1 0-1-1-1-1l1-1zm21-1l1-2c1 0 2-1 3-1h1l4 1-2 2c0 1 0 2-1 3-1-1-3 0-4 0h0c-2-1-1-2-2-3z" class="O"></path><path d="M509 373l4 1-2 2h-1c0-1 0-2-1-3zm-5 3l1-2c1 0 2-1 3-1 0 2 0 4-1 5l-1 1c-2-1-1-2-2-3z" class="E"></path><path d="M545 198l-3-1v-1c3-1 5-3 6-6 1-6 0-14-2-20 0 0-1-2-1-3 4 7 6 18 3 26l-2 6-1-1z" class="V"></path><defs><linearGradient id="C" x1="526.517" y1="395.363" x2="531.343" y2="403.317" xlink:href="#B"><stop offset="0" stop-color="#595959"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#C)" d="M526 396l2-2c2 1 3 3 3 5h0c1 1 1 3 1 4l-2 2v1c0 1 0 5-1 5 0-6 0-10-3-15z"></path><path d="M268 438c1-1 3-2 5-2l4 5v3h-2c-3-1-5-4-7-6zm169-18l-1-1 1-1h0v-1h0c1-1 1-2 2-2 1-1 1-1 1-2l3-3h-1l-1 2-1-1c2-1 3-3 5-4s3-3 4-4c2-1 4-2 6-2v-1h1v1l-3 2c-2 2-4 3-5 4l-3 3c-3 2-7 6-8 10z" class="F"></path><path d="M399 493v1l-5 8h0c-2 1-3 6-4 8l-1 1 1 1h1c0 1 0 2 1 2l-1 1-1-1c-1-2-1-4-1-6v-2c1-2 3-5 4-7l6-6z" class="M"></path><path d="M236 409c-5-3-12-4-17-8 4 1 7 3 11 4 3 0 5-1 7-2v-1c1 2 0 2 0 4 0 0-1 1-1 2v1z" class="P"></path><path d="M182 249c-4 0-8-1-12 0h-1c-2 1-3 1-5 3-2 1-3 4-4 7h0c0 2 0 4 1 6v2c-1-1-1-2-1-3h0c-1-2-1-5 0-7 3-8 9-9 16-10h4c0-1 0-2 1-3 0 1 2 4 3 5h-2z" class="Q"></path><path d="M491 275c1 0 2 1 3 1-1 1-1 2-1 3-1 1-3 4-4 4h-1c-1-2-2-3-4-3h-1l2-2c2-1 2-1 4-1h0c2 0 2-1 2-2z" class="X"></path><path d="M315 204h0c3 2 6 0 9 0s5 1 7 2c1 1 2 2 2 3 1 2 1 3 0 4l-1 1h-1v-1c1-1 2-2 1-3 0-2-2-3-3-3-3-2-7-2-10-1l-4 2c0-1-1-2-1-4h1z" class="P"></path><defs><linearGradient id="D" x1="215.835" y1="246.373" x2="211.886" y2="247.2" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#403d3f"></stop></linearGradient></defs><path fill="url(#D)" d="M212 242c0 1 1 2 2 2s1 1 2 1c0 1 0 1 1 2h0v1c1 1 1 1 1 2l1 1v1l1 2h0-1v1c-3-4-7-8-10-11 1 0 2-1 3-2z"></path><path d="M349 353c2 2 3 8 4 11h0v-1c-1-1-1-3-1-4l6 18v1h-1c-1-3-1-6-4-9l-4-16z" class="Q"></path><path d="M509 249l3-5h0l1 3h5c7 1 13 4 15 11 1 3 0 6-1 8v-1c1-4 1-7-1-10s-5-5-9-6-9 0-12 0h-1z" class="Y"></path><path d="M390 510c1-2 2-7 4-8-1 2-1 3-1 5 1 2 5 3 7 5h-4 0-1c-1 1-2 1-3 2-1 0-1-1-1-2h-1l-1-1 1-1z" class="C"></path><path d="M390 510l1 1h0c1 0 3-1 4-1v1c0 1 1 1 1 1h0-1c-1 1-2 1-3 2-1 0-1-1-1-2h-1l-1-1 1-1z" class="G"></path><path d="M424 403c1-3 1-6 1-8 1-2 2-3 2-4 1-2 1-4 3-5 1 0 1-1 1-1 1-1 2 0 2-1-1 2-2 3-3 4-3 4-5 10-4 15l1 1c-1 1-4 5-5 5v-1c1-1 1-3 2-5z" class="B"></path><defs><linearGradient id="E" x1="482.142" y1="240.813" x2="488.907" y2="240.13" xlink:href="#B"><stop offset="0" stop-color="#434141"></stop><stop offset="1" stop-color="#5a5b5b"></stop></linearGradient></defs><path fill="url(#E)" d="M481 242c2-1 3-3 5-5 1-1 3-2 4-4v3l1 1-11 10h0v-2c1-1 1-2 1-3z"></path><path d="M442 343h3c1-1 2-1 2-2 2-5-2-5-4-7h4v1h1l5 4c-2 2-3 3-5 4l-2 2v1c-1-1-3-2-4-3z" class="B"></path><path d="M397 190c1 1 2 2 4 3 0 1 1 1 2 1l1 1h0c1 1 1 1 2 1v1h-2l-1-1h-4 0c-2 1-5 2-7 4l2-3 1-1c1-1 0-2 0-3v-1c-1 0-1-1-2-1l1-1c1 0 1 1 2 1l1-1z" class="I"></path><path d="M397 190c1 1 2 2 4 3v1h-2s0-1-1-1-1 0-2-1h-1c-1 0-1-1-2-1l1-1c1 0 1 1 2 1l1-1z" class="P"></path><path d="M299 501c2 1 3 2 4 4v1l1 1c0 1 0 1 1 2h0c0 1 1 2 1 3v1h1c-1 2-2 2-3 3-3-2-5-4-10-4 2 0 5-1 6-1h2c1 0 1 0 2-1-1-3-3-6-5-9z" class="F"></path><path d="M456 402c2 2 3 2 5 3 5 0 9-3 13-4-5 4-11 5-16 8-1 0-1-1-2-1v-1c-1-2-1-3 0-5z" class="E"></path><path d="M297 497l2 4c2 3 4 6 5 9-1 1-1 1-2 1h-2c-1 0-4 1-6 1h-1c0-1 6-4 6-6 1-2-2-7-3-8l1-1z" class="C"></path><path d="M150 400c2-1 3-1 5-1h0c2 1 3 2 4 1v5c0 4 1 7 1 10h-1c-1-3-2-7-4-11-1-2-2-3-5-4z" class="J"></path><defs><linearGradient id="F" x1="534.539" y1="412.079" x2="535.945" y2="401.139" xlink:href="#B"><stop offset="0" stop-color="#888789"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#F)" d="M533 398l3 4 2-2h0 1c1-1 1-1 2-1-5 5-5 11-8 17-1-1 0-6 1-8l-1-10z"></path><path d="M209 377c2 0 4 1 6 2h-2 0-1l1 1 1 1 2 2c-7-2-13-4-20-5 2 0 3-1 5-1s4 1 6 0h2zm31-56l2 2v1h-1c0 1 1 2 2 3 0 1 0 1 1 2v3c-3 0-4-1-7-3l-1-1c1-1 1-1 1-2l1-1v-3l1 1h1v-2z" class="B"></path><path d="M610 151c1-1 2-1 4-2 5-2 8-6 11-10 1 1 1 1 1 2v1c-3 6-10 10-16 11h-1v-1l1-1z" class="R"></path><path d="M414 443h1c1-3 4-6 6-8 2 0 3 1 5 3-3 2-6 6-10 6-1 0-2 0-2-1z" class="F"></path><path d="M277 444l4-1 2 1c1 1 1 2 0 4l2 1c-1 1-1 2-2 2l-1 1-7-6c1 0 2-1 3-2h-3 2z" class="C"></path><path d="M277 444l4-1 2 1c1 1 1 2 0 4 0-1-1-2-2-2h-1v-1l1-1h-1-2-3 2z" class="D"></path><path d="M530 379l3 1c2 0 7-1 9 0v1c-2 0-6 0-8 1 0 1 0 1-1 2 0 1 1 2 1 3-2 1-2 1-4 0v1l-1-1-1-2c2-1 0-2 1-4 0-1 1-1 1-2z" class="S"></path><path d="M530 387l3-3c0 1 1 2 1 3-2 1-2 1-4 0z" class="K"></path><path d="M530 379l3 1c-1 1-1 2-2 3 0 1-1 2-1 3l-1 1-1-2c2-1 0-2 1-4 0-1 1-1 1-2z" class="Q"></path><path d="M281 483c-1 0-2-1-4-1-6 0-13 2-16 8-1 1-1 4-2 5v-3c1-4 3-6 6-8 8-5 15-3 24-1-2 1-6 0-8 0z" class="C"></path><defs><linearGradient id="G" x1="497.973" y1="204.133" x2="499.203" y2="211.96" xlink:href="#B"><stop offset="0" stop-color="#222223"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#G)" d="M495 219c1-3 2-5 2-8s0-6 1-10c0 2 1 5 3 6 1 1 2 1 4 1l-3 3c-2 2-4 5-5 7l-2 1z"></path><path d="M193 373l5 1h2l6 2 3 1h-2c-2 1-4 0-6 0s-3 1-5 1h-5 0c1-1 1-1 1-2h0c0-1 0-2 1-3z" class="C"></path><path d="M193 373l5 1h2c-1 0-2 0-3 1s-3 1-5 1c0-1 0-2 1-3z" class="I"></path><path d="M191 378h3c1 0 1-1 1-1h2c1-1 3-1 4-1v1c2 0 3 0 4-1h1 0l3 1h-2c-2 1-4 0-6 0s-3 1-5 1h-5 0zm45 31v-1c0-1 1-2 1-2v2c1 1 3 1 4 2 2 1 4 3 6 4 1 0 2 3 3 4l4 4h0l-1 1-17-14z" class="O"></path><path d="M262 433h1l5 5c2 2 4 5 7 6h3c-1 1-2 2-3 2l-15-10-1-1c2 0 3-1 3-2z" class="P"></path><defs><linearGradient id="H" x1="432.694" y1="410.325" x2="440.25" y2="407.53" xlink:href="#B"><stop offset="0" stop-color="#535152"></stop><stop offset="1" stop-color="#6e6f70"></stop></linearGradient></defs><path fill="url(#H)" d="M433 410l12-11 1 1c-4 4-8 8-11 13l-1 1-2 3c-1-1-1-3-1-4l2-3z"></path><path d="M431 413l2-3v3l1 1-2 3c-1-1-1-3-1-4z" class="D"></path><path d="M226 272c3 4 3 10 6 13l1 1 1 2c1 2 2 2 2 4h-1c-2-1-8-4-9-6h0 1 2l1-1c-1 0-2-1-2-2s-1-2-1-3v-4c0-1-1-2-1-4z" class="F"></path><path d="M442 364c0-1-1-3-1-4-1-1-1-2-1-3 1 0 2 1 3 2h0c0 1 1 1 1 2 2 5 1 8 0 12l-1 3c-1 0-2 0-2-1 0-2 1-5 0-7h1v-4z" class="B"></path><path d="M442 364c1 2 1 4 1 6v3h1l-1 3c-1 0-2 0-2-1 0-2 1-5 0-7h1v-4z" class="X"></path><path d="M198 241h0c0-1-1-2-1-2l-2-1-1-1c-1 0-1-1-2-1h-1v-1c2 0 3 2 5 2 1 1 2 2 3 2 0 1 1 3 2 3 1 1 1 1 2 1 0 0 0 1 1 1 0-1-1-2-2-2v-1l9 9c1 1 3 4 5 6 1 1 4 4 4 5h0-2c-2-1-4-4-4-6-1 0-1-2-2-2 0-1-1-1-2-2s-2-3-3-4c-1 0-2-1-3-1h0l-2-1-4-4z" class="B"></path><path d="M415 208l1 5c0 5 0 13-2 17 0 1 0 1-1 1h0c0-6 1-12 0-17 0-1-1-3-1-4s0-2 1-3c0 1 0 1 1 1h1z" class="E"></path><path d="M259 410l3 3c1 1 1 2 2 3h1c0 1 0 1 1 2 1 2 4 4 5 7 1 0 2 1 2 1v3 2c-3-2-4-5-6-8l-4-6-2-2-2-2v-1h-1c1-1 1-1 1-2z" class="F"></path><path d="M414 465h2l1 1-1 3c-4 2-8 4-11 7 0-1-1-2-2-3 3-3 7-6 11-8z" class="D"></path><path d="M197 277h1c2-1 3-2 5-2 1 0 2 0 3-1 0 1 0 2 1 3s3 2 3 3h-2c-2 0-3 1-5 3h0c0-1-1-2-1-2-1-1-4-2-6-2v-1l1-1z" class="B"></path><path d="M459 308c-1 2-3 4-4 7-1 1-1 3-3 4l-1-1c0-2 2-3 3-4-1-2-1-3 0-4 0-1 0-3 1-4h0 1c-1 0-1-1-1-1l1-1c2-3 5-5 7-7v2c-1 0-1 1-2 1-1 2-3 4-3 7l1 1z" class="F"></path><path d="M154 210c-3-3-5-6-6-9-5-8-6-17-4-26 1-2 2-4 3-7h0 0c0 1 0 1-1 2-1 6-2 11-2 17 0 2 1 5 2 6 1 2 3 2 4 3 0 1-1 1-2 2 2 5 4 8 9 11-1 1-1 1-2 1h-1z" class="Y"></path><path d="M175 222l4 1c-1 1-1 1-2 1h0c0 1 0 1 1 1h0c3 2 6 3 8 5h0c0 1 0 1 1 2v1h1v1h1 1 1 0c-1 1-1 1-2 1h-1c0-1 0-1-1-1 0-1 0 0-1-1h-1c0 1 2 2 2 3h2c1 1 3 1 4 2h0 0 1c1 0 1 1 1 1 2 1 2 1 3 2-4-2-7-4-12-5h0c-2-2-2-4-4-5-1-1-1-1-1 0-1-1-2-2-4-2v-1h1c1 0 1 1 2 1 0-1-1-1-2-1 0 0-1 0-1-1-1 0-2-2-2-3l-1-1 1-1z" class="B"></path><path d="M485 270c2-2 4-4 6-5v1l1-1v1l-1 2 2 2c0 1-1 1-1 2s0 1 1 2c0 1 1 1 1 2h0c-1 0-2-1-3-1-3-1-5-1-9 0 1-2 2-3 3-5z" class="D"></path><path d="M464 317l1 2c-1 2-1 4-2 6-2 4-3 8-3 13-1 2-3 3-4 5-1 1-3 3-5 4-2 0-4 1-6 0s-4-3-5-5h1l1 1c1 1 3 2 4 3 1 0 2 0 3-1 5-1 8-7 10-11l2-7c1-3 2-7 3-10z" class="S"></path><path d="M440 353c1-1 2 0 4 0 1 1 2 2 4 3v1l1-1v1c1 1 3 0 3 2 1 0 1 1 1 1 1-1 0-5 1-5 0 3 0 6-1 9-1 2-1 5-2 7v-1c-1-2-1-3-1-5s-1-6-3-8c-1 0-1 0-2-1h-1v1l-4-4z" class="D"></path><defs><linearGradient id="I" x1="438.903" y1="423.86" x2="454.471" y2="431.762" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#d4d4d6"></stop></linearGradient></defs><path fill="url(#I)" d="M434 432c5-2 10-5 15-7l9-3 1 3-7 1c-5 2-10 5-15 7l-4 1v-1l1-1z"></path><defs><linearGradient id="J" x1="249.862" y1="357.44" x2="249.02" y2="371.946" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#4c4c4d"></stop></linearGradient></defs><path fill="url(#J)" d="M246 363c2-1 3-4 5-5 1-1 3-1 5-2-4 5-7 9-6 15v3l-1-1h-1c-1-1-2-3-2-5s-1-3 0-5z"></path><path d="M460 311v1l-2 9h0l-2 2v1h1 1c-1 3-3 6-6 7l-1 1-1-1v-1l4-4-1-1v-2c3-3 3-6 4-9l3-3z" class="B"></path><path d="M342 327l8 22c0 3 2 6 2 10 0 1 0 3 1 4v1h0c-1-3-2-9-4-11-2-3-3-6-4-10s-3-9-4-14v-1-1h1 0z" class="T"></path><path d="M468 305v1c-1 2-3 5-4 6-1 3-3 7-4 11h-1v1h-1-1-1v-1l2-2h0l2-9v-1h0c1-1 1-2 3-3 1 0 2-1 3-1l2-2z" class="G"></path><path d="M460 311h0c1-1 1-2 3-3 1 0 2-1 3-1-2 2-3 4-4 6-2 4-2 8-5 11h-1v-1l2-2h0l2-9v-1z" class="X"></path><path d="M435 413h0c-2 4-5 7-7 10-1 2-2 3-3 4 0 0 0 1-1 1-2 1-3 4-4 6 0-3 2-5 2-7 1-5 3-9 7-12l2-2c0 1 0 3 1 4l2-3 1-1z" class="F"></path><path d="M429 415l2-2c0 1 0 3 1 4-1 0-6 7-6 8l-1-1c1-3 3-6 4-9z" class="B"></path><path d="M328 177h1c4 1 9-3 12-5 5-4 9-10 10-18 1-3 0-6-1-9v-1c3 5 3 11 1 16-2 8-12 16-19 20h-2c-1 0-2-1-2-2v-1z" class="H"></path><path d="M530 388v-1c2 1 2 1 4 0 1 2 1 5 3 7 1 2 3 3 5 4 0 0 1 1 2 1h-3c-1 0-1 0-2 1h-1 0l-2 2-3-4v-1-1-1c-1-3-2-5-3-7z" class="R"></path><path d="M509 215l1 1c-2 3-5 5-7 7l-13 10c-1 2-3 3-4 4-2 2-3 4-5 5l-3 3v-1-1c0-2 1-2 0-5l2 1-1 2h1v-1c3-2 6-5 8-7 1-2 2-3 3-4h0l1-1 1-3v-1-1l2-1-1 3-2 6c2-1 4-3 5-4 4-4 8-7 12-12z" class="U"></path><path d="M234 328l3 2c1 1 2 2 3 2 1 1 3 1 4 2 0 1-1 2-1 2v1c1-1 2-1 3-1 1-1 3-2 5-2-2 2-4 3-5 5v1c-1 0-3 1-4 1 0 1 0 2-1 3l-1-1h1c0-1 1-1 1-2l-2-2c-2-2-3-4-4-6s-1-3-2-5z" class="B"></path><path d="M217 287v-3c1 0 2 0 3 1 1 0 2 1 3 2s1 1 1 2c2 3 4 5 6 7l2 5-1 1c-3-2-7-2-10-3h3 0c1-1 0-1 0-2 1-1 1-2 2-2l-2-2v-2c0-1-1-2-2-2-1-1-2-1-3-1h-1v-1c-1 1-1 2-2 3v-1l1-2z" class="R"></path><path d="M236 354c1 2 1 3 1 4 1 1 1 1 1 2v2 2l1 1v2h0l1 2c0 1 0 1 1 2v1c1 1 0 1 1 1v1 1h1c-1-1-1-2-1-3s-1-2-1-3v-1l-1-2v-1-1s0-1-1-1v-1-1h0c-1-2-1-5 0-6l1-1 1 1c2 1 3 0 5 0h0 0l-2 3c-2 4-2 7-2 10l2 8h-1v1h0l-4-8c-2-5-2-10-3-15z" class="F"></path><path d="M241 359h1l1-3c1 0 2-1 3-1l-2 3c-1 1-2 1-3 1h0z" class="M"></path><path d="M242 368c-2-3-4-8-3-11h1l1 2h0c1 0 2 0 3-1-2 4-2 7-2 10z" class="B"></path><path d="M458 422c4 0 7-1 11 0h1c5 1 11 5 14 9 2 5 2 9 2 14h0c0-2 0-5-1-7v-1c-1-4-5-8-9-10-5-2-11-3-17-2l-1-3z" class="L"></path><defs><linearGradient id="K" x1="163.198" y1="392.739" x2="165.785" y2="404.216" xlink:href="#B"><stop offset="0" stop-color="#484747"></stop><stop offset="1" stop-color="#6f6f70"></stop></linearGradient></defs><path fill="url(#K)" d="M162 390v1c0 1 0 2-1 3 1 0 1 0 1-1h4c0-1 1-1 1-2l1 1c-3 5-4 9-5 15l-1-1-1 1h0-2v-2-5c1-4 1-6 3-10z"></path><defs><linearGradient id="L" x1="157.85" y1="398.168" x2="162.25" y2="403.034" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#535254"></stop></linearGradient></defs><path fill="url(#L)" d="M162 390v1c0 1 0 2-1 3 1 0 1 0 1-1h4c-2 2-3 2-5 5v9h-2v-2-5c1-4 1-6 3-10z"></path><defs><linearGradient id="M" x1="205.997" y1="222.926" x2="201.111" y2="225.455" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#656566"></stop></linearGradient></defs><path fill="url(#M)" d="M200 223c0-4-1-8-1-12l1-3c1 3 1 7 2 10s2 8 4 11l5 10c-3-2-7-6-9-10-1-2-1-4-2-6z"></path><path d="M361 201v-1c0-1 1-2 2-3 2-1 4-3 6-4 4-2 8-3 12-4l9-3 1 1h1 0c1 1 2 2 3 2l1-1 1 2-1 1c-1 0-1-1-2-1l-1 1c-1 0-3-1-4-1-7-1-15 2-20 5-2 1-6 4-7 5 0 1 0 1-1 1z" class="G"></path><defs><linearGradient id="N" x1="214.912" y1="266.678" x2="209.209" y2="268.746" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#N)" d="M209 255l1-1c1 2 2 4 2 6l4 8c0 1 0 3 2 3 0 1-1 4-1 6l-6-9c-2-3-4-5-7-8l2 1h3c0-1 1-1 1-2 1-1 0-3-1-4z"></path><defs><linearGradient id="O" x1="162.929" y1="386.422" x2="172.331" y2="388.719" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#4c4c4c"></stop></linearGradient></defs><path fill="url(#O)" d="M172 379l1 1c0 2-1 2-2 4h4c-1 2-3 3-4 5l-3 3-1-1c0 1-1 1-1 2h-4c0 1 0 1-1 1 1-1 1-2 1-3v-1c1 0 2-2 2-2 2-4 5-6 8-9z"></path><defs><linearGradient id="P" x1="522.743" y1="382.053" x2="510.49" y2="378.654" xlink:href="#B"><stop offset="0" stop-color="#4e4e4e"></stop><stop offset="1" stop-color="#7a7979"></stop></linearGradient></defs><path fill="url(#P)" d="M513 374l2 1c4 2 7 5 10 8-1 0-1 0-2 1h-1s0 1-1 1-1 1-1 1c-3-2-6-4-10-6h0v-1c1-1 1-2 1-3l2-2z"></path><path d="M513 374l2 1c-1 1-1 1-1 2l-1 1h-2l-1 2h0v-1c1-1 1-2 1-3l2-2z" class="P"></path><defs><linearGradient id="Q" x1="301.253" y1="191.217" x2="300.987" y2="182.838" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#Q)" d="M295 181h1c0 1 1 1 1 2h4 3v-1l-1-1h-1-1c1-1 1-1 2-1 1 1 1 2 2 3l2 2h1l-2 2c-1 1-2 2-2 3s0 3 1 4l-2-1c-1-1-1-1-1-2h-1c0 1-1 1-2 1 0 0-1 1-2 0 0 0-1-1-1-2 2-4-1-6-1-9z"></path><path d="M505 208h0c5-4 5-8 7-14 1-5 2-9 5-13 1-1 2-2 4-3-1 2-3 3-4 5-5 8-4 18-6 27h0c0-1 0-1-1-2-1 0-2 0-3 1h0c0 1-1 1-1 1h0l-1-1-1 2-4 5c0 1-2 1-2 2-1 1-1 1-1 2-1 1-1 2-2 3l-1 1v1l1-3v-3l2-1c1-2 3-5 5-7l3-3z" class="O"></path><path d="M204 246c1 0 2 1 3 1 1 1 2 3 3 4s2 1 2 2c1 0 1 2 2 2 0 2 2 5 4 6h2c-1 4-2 7-2 10-2 0-2-2-2-3l-4-8c0-2-1-4-2-6l-1 1-3-6h0c-1 0-2-2-2-3z" class="G"></path><defs><linearGradient id="R" x1="529.365" y1="405.819" x2="532.121" y2="412.176" xlink:href="#B"><stop offset="0" stop-color="#919291"></stop><stop offset="1" stop-color="#aeacae"></stop></linearGradient></defs><path fill="url(#R)" d="M533 397v1l1 10c-1 2-2 7-1 8-1 2-1 4-3 6h-1c-3 5-6 8-11 11 7-6 11-13 11-22 1 0 1-4 1-5v-1l2-2c0-1 0-3-1-4l1 2c1-1 1-3 1-4z"></path><path d="M533 397v1l1 10c-1 2-2 7-1 8-1 2-1 4-3 6h-1c2-6 4-12 3-19 0-1 0-3-1-4l1 2c1-1 1-3 1-4z" class="O"></path><path d="M258 384c1 0 3 0 5 1h0c0 1 1 2 1 2 3 3 5 10 5 14v3c-1 0-1 0-2 1 0 1 1 1 1 2v1l-5-5h0l1-1c1 0 1-1 1-1 0-2-1-4-3-5 0-2 1-5 0-7s-3-3-4-4v-1z" class="D"></path><path d="M515 223h1v1c-1 3-6 3-8 5-1 2-1 5-2 6 0 1-1 1-1 2h-1c-3 1-6 2-9 4-1 1-3 2-4 3v1l-1-1h-1c-1 1-2 1-4 2 1-2 3-3 4-4l2-2 1-1c3-1 7-3 10-5l6-6h0c1-1 2-1 3-1l1-1s1 0 2-1h0 0l1-1h-1l-1 1h-1-1l-1 1h-2c2-1 4-2 7-3z" class="F"></path><path d="M520 386s0-1 1-1 1-1 1-1h1c1-1 1-1 2-1l3 2 1 2 1 1c1 2 2 4 3 7v1 1c0 1 0 3-1 4l-1-2h0c0-2-1-4-3-5l-2 2v-1l-1-2-5-7z" class="B"></path><path d="M525 393c1-1 2-2 4-1 1 0 2 1 3 2l1 1v1h0l-1-1v1l-2-2c-1-1-2-1-2 0-1 0-2 0-2 1l-1-2z" class="D"></path><path d="M526 395c0-1 1-1 2-1 0-1 1-1 2 0l2 2v-1l1 1h0v1c0 1 0 3-1 4l-1-2h0c0-2-1-4-3-5l-2 2v-1z" class="G"></path><path d="M220 266l1-2h0l2 2c1 1 3 5 3 6 0 2 1 3 1 4v4c0 1 1 2 1 3s1 2 2 2l-1 1h-2-1v-2h0 1l-3-3c-1-1-2-1-2-2v-1c-1-1-1-1-2-3l-1 1v-3l1-7z" class="B"></path><path d="M220 266h2 0c0 2 1 4 0 7h-1-2l1-7z" class="G"></path><path d="M223 266c1 1 3 5 3 6 0 2 1 3 1 4v4c-1-2-2-4-2-6-1-2-1-4-1-5-1-1-1-2-1-3z" class="M"></path><path d="M150 400l-1-1 5-3c4-2 5-7 6-11 0-1 0-1-1-2-2-2-5-2-8-2v-1c3 0 7 1 10 0h1c1 2 1 3 0 6l2 2s-1 2-2 2c-2 4-2 6-3 10-1 1-2 0-4-1h0c-2 0-3 0-5 1z" class="H"></path><defs><linearGradient id="S" x1="485.76" y1="222.129" x2="491.315" y2="224.69" xlink:href="#B"><stop offset="0" stop-color="#2e2d2e"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#S)" d="M483 233c4-6 7-13 8-21 1-1 1-5 2-6h0c2 7-1 16-2 23-1 1-2 2-3 4-2 2-5 5-8 7 0-2 2-5 3-7z"></path><path d="M495 357c2 0 4-3 6-4v-1c1 0 1 0 2 1v-1h0v-2h0c1 1 1 1 1 2 1 1 1 2 2 3h1l1 1h1 1c1 1 5 0 6 1-1 0-1 0-2 1v1h3c-3 1-7 2-10 0l-3-3-5 1c-2 0-6 5-7 7v2l1 1h-1c-2 1-5 2-7 1l10-11z" class="F"></path><path d="M216 290c1-1 1-2 2-3v1h1c1 0 2 0 3 1 1 0 2 1 2 2v2l2 2c-1 0-1 1-2 2 0 1 1 1 0 2h0-3c-1 0-2 0-3-1 1-2 3-2 5-3h0l-1-1c-3 1-5 2-7 5 0-1 0-2-1-2h-3c1-1 2-1 3-1-1-1-3-2-4-3 2 0 4-1 6-3h0z" class="S"></path><path d="M216 290c1-1 1-2 2-3v1h1c1 0 2 0 3 1 1 0 2 1 2 2v2c-1-1-1-2-3-3h-1c-1 0-1 1-1 2-3 0-4 1-5 3v1h0c-1-1-3-2-4-3 2 0 4-1 6-3h0z" class="N"></path><defs><linearGradient id="T" x1="158.786" y1="356.805" x2="167.895" y2="356.276" xlink:href="#B"><stop offset="0" stop-color="#40413f"></stop><stop offset="1" stop-color="#5a585b"></stop></linearGradient></defs><path fill="url(#T)" d="M159 351c1 1 2 3 4 3l-1 1c1 1 5 4 5 4l2 2 2 1c1 1 3 2 5 3 3 2 7 3 11 5l1 1v1h-1l-1-1c-2 0-4 1-6-1l-2-1c-2-2-3 0-5 0-1-1-1 0-2 0 0-2-1-3-1-4h0l-3-3c-3-2-6-5-9-8l1-3z"></path><path d="M170 365v-2c3 0 13 7 16 8-2 0-4 1-6-1l-2-1c-2-2-3 0-5 0-1-1-1 0-2 0 0-2-1-3-1-4z" class="L"></path><defs><linearGradient id="U" x1="411.133" y1="440.016" x2="431.743" y2="441.569" xlink:href="#B"><stop offset="0" stop-color="#656362"></stop><stop offset="1" stop-color="#79797b"></stop></linearGradient></defs><path fill="url(#U)" d="M433 429v2l1 1-1 1v1l4-1-3 2h0l-4 3-19 14h0l-2-2c1-1 2-2 2-3l2-2v-1h-2v-1h1 2c0 1 1 1 2 1 4 0 7-4 10-6l7-9z"></path><path d="M430 438h0c0-2 1-4 2-5l2 2h0l-4 3z" class="C"></path><defs><linearGradient id="V" x1="459.463" y1="337.182" x2="446.655" y2="339.427" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#V)" d="M459 324v-1h1v4l-2 5h1v2c-2 4-5 10-10 11-1 1-2 1-3 1v-1l2-2c2-1 3-2 5-4l-5-4h-1v-1l1-1h2c3 0 5-2 7-4 1-1 2-3 2-5z"></path><path d="M459 324v-1h1v4l-2 5c-1 2-2 4-4 6-2-1-4-2-5-4 0 0 1 0 1-1 3 0 5-2 7-4 1-1 2-3 2-5z" class="D"></path><path d="M159 405v2h2 0l1-1 1 1c0 7 1 13 5 19h0c2 2 3 4 5 6 2 1 3 2 5 3-2 0-6-2-7-3-6-4-11-9-12-17h1c0-3-1-6-1-10z" class="C"></path><path d="M161 407l1-1 1 1c0 7 1 13 5 19h-1 0c-3-2-5-6-5-9l-1-3v-7z" class="I"></path><path d="M308 197l1-1 3-3c1-1 2-1 3-1 3-1 6-1 9-1l-7 4c-2 2-3 4-3 8l1 1h-1c0 2 1 3 1 4l-1 1-2 1c-4-4-4-7-4-13z" class="B"></path><path d="M314 209c-1-1-2-1-3-2 0-2-1-7-1-9h1v-1c2 0 4-2 5-3l1 1c-2 2-3 4-3 8l1 1h-1c0 2 1 3 1 4l-1 1z" class="O"></path><path d="M451 375v3c1 1 1 1 2 1-6 11-14 21-23 29h-1v-3c9-9 16-19 22-30z" class="P"></path><path d="M489 202l1-1h0 1 0l-19 44h-1v-1 1c-1-1-1-2-1-3l19-40z" class="L"></path><defs><linearGradient id="W" x1="451.034" y1="357.789" x2="459.201" y2="361.214" xlink:href="#B"><stop offset="0" stop-color="#6b6b6b"></stop><stop offset="1" stop-color="#a3a1a2"></stop></linearGradient></defs><path fill="url(#W)" d="M456 343c1-2 3-3 4-5 0 9 0 18-2 27-1 4-2 7-3 10l-2 4c-1 0-1 0-2-1v-3c3-7 6-15 6-22 0-3-1-6-1-10z"></path><defs><linearGradient id="X" x1="304.618" y1="102.595" x2="290.41" y2="98.969" xlink:href="#B"><stop offset="0" stop-color="#9d9ba2"></stop><stop offset="1" stop-color="#c8c7c5"></stop></linearGradient></defs><path fill="url(#X)" d="M307 90h3 0 1c-14 9-26 21-29 39-1 1-1 6-1 7h-1l-1 3v-6-1c1-1 1-1 1-2 0-2 0-3 1-5l1-3c0-2 1-5 2-7h1c1-3 3-5 4-8 1-1 1-1 1-2l7-7 8-7 2-1z"></path><path d="M499 251c3 0 8-2 10-2h1l-7 3c-1 2-3 3-5 5h0c-3 3-4 6-6 9v-1l-1 1v-1c-2 1-4 3-6 5 0-1 1-2 1-2v-1c1-1 2-2 3-4 0-1 0-1 1-2h0 1c1-1 1-2 2-2-2 1-3 2-5 3h0l1-2h-3-2v-1c0-1 0-1 1-2l1 2c3-1 6-4 9-6l4-2z" class="C"></path><path d="M489 260c1-1 3-2 5-3 3-2 6-4 9-5-1 2-3 3-5 5h0c-2 0-2 0-3 1-2 1-4 4-6 5 0-1 0-1 1-2h0 1c1-1 1-2 2-2-2 1-3 2-5 3h0l1-2z" class="F"></path><path d="M489 263c2-1 4-4 6-5 1-1 1-1 3-1-3 3-4 6-6 9v-1l-1 1v-1c-2 1-4 3-6 5 0-1 1-2 1-2v-1c1-1 2-2 3-4z" class="B"></path><path d="M403 473c1 1 2 2 2 3-1 2-3 4-3 6l1 1c2 0 4-1 6-1 1 0 2-1 3-1h3c1-1 4-1 5 0h1 2v1h2c1 1 1 1 2 1v1c2 1 4 2 5 4h0c0 1 1 1 1 2h0v2h0c-1 0-1-1-1-1 0-2-2-5-4-5v-1h-1c-1-1-1-1-2-1s-1-1-2-1h0-2 0c-2-1-4-1-6 0-3 0-5 1-7 2l-1 1c-2 0-3 0-4 1-1 0-1 0-1 1l-1 1h-2v1l-2-1 1-1v-2-1c1-2 1-3 2-5s2-4 3-7z" class="M"></path><path d="M488 262h0c2-1 3-2 5-3-1 0-1 1-2 2h-1 0c-1 1-1 1-1 2-1 2-2 3-3 4v1s-1 1-1 2c-1 2-2 3-3 5-1 1-2 1-2 2l-1-1c-1 0-3 1-4 1h0l1-1c0-1-1-1 0-1v-3l2-1c0-3 2-6 3-8 0-1 1-3 1-4h1l1 1h2 3l-1 2z" class="U"></path><path d="M478 271h0 1 1c-1 2-3 4-4 5 0-1-1-1 0-1v-3l2-1z" class="M"></path><path d="M489 263c-1 2-2 3-3 4v1s-1 1-1 2c-1 2-2 3-3 5-1 1-2 1-2 2l-1-1 2-2c1-3 3-7 6-9l2-2z" class="G"></path><path d="M478 271c0-3 2-6 3-8 0-1 1-3 1-4h1l1 1h2 3l-1 2c-4 1-6 6-8 9h-1-1 0z" class="F"></path><path d="M489 260l-1 2c-4 1-6 6-8 9h-1c0-2 1-3 2-4 1-2 1-4 3-6v1h1 1v-1-1h3zm-4-14c2-1 3-1 4-2h1l1 1c-3 3-5 6-6 11v1c-1 1-1 1-1 2v1l-1-1h-1c0 1-1 3-1 4-1 2-3 5-3 8l-2 1c0-2-1-5-2-7 1-1 1-1 1-2 2-6 7-10 9-16l1-1z" class="B"></path><path d="M315 179l2-1c4-2 2 0 5 1 2-1 3-1 5-2l1-1v1 1c0 1 1 2 0 3-6 2-12 5-18 9-2-1-3-1-4-3l2-2 2-2 5-4z" class="R"></path><path d="M493 367l-1-1v-2c1-2 5-7 7-7l5-1 3 3c3 2 7 1 10 0 4 0 7-2 10-3h4 1c-1 1-6 6-7 6v-1l-4 2v-1l3-3c-3 2-5 4-8 2-1 0-1 0-1-1l-8 1c-2-1-3-1-5-1-1 0-2 1-3 1l-1 1c-1 0-1 1-2 2h0c2 0 2 0 3 1-1 1-4 2-5 2h-1z" class="J"></path><path d="M471 265l1-2c0 1 0 2 1 2 0 3 1 5 1 7v3c1 0 1 0 1 1-1 1-3 2-3 3l-3 2h1l5-4h0 0c1 0 3-1 4-1l1 1c-1 0-2 1-3 1-2 1-4 2-6 4s-5 6-7 6l-1-1h0 1c1-2 3-3 4-5-3 1-5 4-8 5l7-7v-2c-1-1-1-1-1-2 0-3 3-8 5-11z" class="F"></path><path d="M471 265l1-2c0 1 0 2 1 2 0 3 1 5 1 7v3h0c-1 2-3 3-4 5l-1-1c0-1 0-1 1-2h0v-1h0-2c0-1 1-3 1-4 1-2 1-5 2-7z" class="B"></path><path d="M474 272l-3 2h-1l-1 1s0-1 1-1c2-3 1-6 3-9 0 3 1 5 1 7z" class="D"></path><defs><linearGradient id="Y" x1="298.514" y1="183.034" x2="301.178" y2="165.618" xlink:href="#B"><stop offset="0" stop-color="#504f4f"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#Y)" d="M295 181v-4l1-14c2 3 4 3 7 4-1 0-2 0-2 1-1 2 0 5 0 7 1 0 3 1 3 1 1 1 2 3 3 4 0 0 1 1 1 2 0 0 2 0 2 1l-2 2h-1l-2-2c-1-1-1-2-2-3-1 0-1 0-2 1h1 1l1 1v1h-3-4c0-1-1-1-1-2h-1z"></path><defs><linearGradient id="Z" x1="501.963" y1="215.346" x2="504.94" y2="218.88" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#908f8f"></stop></linearGradient></defs><path fill="url(#Z)" d="M498 218c0-1 2-1 2-2l4-5 1-2 1 1h0s1 0 1-1h0c1-1 2-1 3-1 1 1 1 1 1 2l-2 5c-4 5-8 8-12 12-1 1-3 3-5 4l2-6v-1l1-1c1-1 1-2 2-3 0-1 0-1 1-2z"></path><path d="M498 218h0c1 0 2-1 2-1 1 0 1 0 1-1 1-1 0 0 1 0l1-1h0c-1 3-7 7-7 12h1c-1 1-3 3-5 4l2-6v-1l1-1c1-1 1-2 2-3 0-1 0-1 1-2z" class="C"></path><defs><linearGradient id="a" x1="306.426" y1="177.595" x2="311.505" y2="166.417" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#b9b9b9"></stop></linearGradient></defs><path fill="url(#a)" d="M303 167l1-1c3-2 4-4 6-7-2 6-2 10 1 15 1 2 2 3 4 5h0l-5 4c0-1-2-1-2-1 0-1-1-2-1-2-1-1-2-3-3-4 0 0-2-1-3-1 0-2-1-5 0-7 0-1 1-1 2-1z"></path><path d="M410 242l1 1c-1 3-1 5-1 8-1 4-2 7-3 10h-1 0c-2-3-5-8-8-11s-9-4-13-4c-3 0-6 1-8 4-1 1-2 3-2 5 0 1 1 2 2 2 1 1 3 2 5 2h0c1-1 2-1 3-2h0v1c-1 1-2 2-4 2s-4-1-6-3c-1-1-1-2-1-3 0-3 1-5 3-6 2-2 5-3 8-3s6 0 9 1 6 4 9 4c1 0 2 0 3-1s0-2 0-3h0l1-1h1c1-1 2-2 2-3z" class="E"></path><path d="M237 402l1-1c0 2 1 2 2 3h2c0 1 1 1 1 1l2 2h1l1 1 1 1c1 1 2 1 3 3 0 0 1 0 1 1 1 0 2 1 3 3h0l1 1-1 1 1 1c0 1 1 1 1 3h0l1 2c0 1 2 2 2 3v1l1 1 1 1c1 1 1 2 1 3h-1c0-2-1-3-3-4-1-2-4-4-6-6l1-1h0l-4-4c-1-1-2-4-3-4-2-1-4-3-6-4-1-1-3-1-4-2v-2c0-2 1-2 0-4z" class="B"></path><defs><linearGradient id="b" x1="284.32" y1="485.759" x2="287.319" y2="466.978" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#b)" d="M275 469c1-1 1-3 2-5l2 1h1l3 2c2 1 2 1 3 2l1 1 1-2v2h1c1 3 4 5 5 8h-1-1 0l3 7v4c0-1-1-1-1-1l-2-1c-2 0-4 0-6-1l-5-3c2 0 6 1 8 0h1c1-1 1-1 1-2-2-4-4-6-7-8s-5-3-9-4z"></path><path d="M275 469c1-1 1-3 2-5l2 1-2 1v1c3 2 6 3 7 5v1c-3-2-5-3-9-4z" class="B"></path><path d="M283 467c2 1 2 1 3 2l1 1 1-2v2h1c1 3 4 5 5 8h-1-1 0c-1 0-4-6-5-7s-2-3-4-4z" class="F"></path><path d="M215 300c1 0 2 1 3 1 2 2 4 3 6 6 4 5 6 13 8 20l2 1c1 2 1 3 2 5s2 4 4 6l2 2c0 1-1 1-1 2h-1l1 1 2 1c1 1 3 1 5 1h1l1-1c0-1 1-1 2-2v1c-1 0-1 1-1 2-2 1-3 2-5 2-4 0-7-4-9-6 0-1-3-4-4-5h0c0-4-2-8-3-12-1-1-2-3-2-5 0 3 1 4 0 7l-4-14 1-1c-1-4-3-6-6-9-1-1-2-2-4-3h0z" class="R"></path><path d="M225 312c2 4 4 8 5 13-1-1-2-3-2-5 0 3 1 4 0 7l-4-14 1-1z" class="U"></path><defs><linearGradient id="c" x1="234.758" y1="337.405" x2="238.91" y2="332.338" xlink:href="#B"><stop offset="0" stop-color="#0f1012"></stop><stop offset="1" stop-color="#2d2c2b"></stop></linearGradient></defs><path fill="url(#c)" d="M232 327l2 1c1 2 1 3 2 5s2 4 4 6l2 2c0 1-1 1-1 2h-1c-5-4-6-10-8-16z"></path><path d="M225 372c1 0 2-1 3 0-2 2-2 2-3 5h0l1 2v-1l7 6c1 1 2 2 4 2h1 0c0 1 0 1 1 2l-1 1h0l2 2h-1l20 19c0 1 0 1-1 2-2-4-5-6-8-9-10-9-19-17-32-23h1c1-1 2-1 2 0h1 1v-2c0-1-1-3-3-3l-1 1v-4c1 1 1 0 2 0 1 1 1 1 2 1h0c1 0 1 1 1 1 0 1 0 3 1 4v-2-2s0-1 1-1h0c0-1-1-1-2-1h-1-2 4z" class="Y"></path><path d="M226 379v-1l7 6c1 1 2 2 4 2h1 0c0 1 0 1 1 2l-1 1h0l2 2h-1c-2-1-4-3-6-5-2-1-5-3-6-5-1 0-1-1-1-2z" class="U"></path><path d="M468 297c1 0 1-1 2-1 2 2 3 3 3 5h1c3-1 7 0 10 0h-3v1c-1 1-5 4-7 5-3 2-3 4-5 7v-1h0l-1 2v-1c-1 0-1 1-2 3h0l-1 2-1-2c-1 3-2 7-3 10l-2 7v-2h-1l2-5v-4c1-4 3-8 4-11 1-1 3-4 4-6v-1-1h1l-1-1h0 2c1-1 1-1 1-2s0-2-1-3c-1 0-1 0-2-1z" class="X"></path><path d="M464 317s0-1 1-2l1-1v-2l1-1 1-2v-1c0-1 1-2 2-2v-1c1-2 1-2 3-3 1 0 0 1 1 2v3c-3 2-3 4-5 7v-1h0l-1 2v-1c-1 0-1 1-2 3h0l-1 2-1-2z" class="B"></path><defs><linearGradient id="d" x1="234.338" y1="433.716" x2="233.72" y2="421.926" xlink:href="#B"><stop offset="0" stop-color="#9d9c9d"></stop><stop offset="1" stop-color="#deddde"></stop></linearGradient></defs><path fill="url(#d)" d="M240 426c-8-2-16-3-23 2-5 2-7 5-9 10-1 5-1 10 2 15l1 1h-1c-3-3-4-7-4-11 0-6 2-11 6-15 3-3 7-5 11-6h1 5c11 0 19 5 28 9l1 1-1 2-8-4-3-1-6-3z"></path><path d="M515 367c2 1 5-1 7 1v2c2 4 4 7 8 9 0 1-1 1-1 2-1 2 1 3-1 4l-3-2c-3-3-6-6-10-8l-2-1-4-1c-1 0-2 0-2-1l1-1 2-1c1-1 3-2 5-3z" class="Y"></path><path d="M515 367c2 1 5-1 7 1v2c-1-1-2-2-3-2-4 0-5 3-9 2h0c1-1 3-2 5-3z" class="Q"></path><path d="M177 360c3 0 5 0 7-1 2 0 5-3 6-5 1 0 1 0 1-1 2 0 5 4 6 5 2 2 4 5 5 7l1 1c-1 1-4 0-6 0v1h0c-1 0-3 0-4-1h-3l-2-1v-1h-1 0l-3-3h-5-1c-1 0-2 1-4 1v1h0l2 2c-2-1-4-2-5-3v-1c1-1 5-1 6-1h0z" class="P"></path><path d="M177 360c3 1 5 0 7 0s9-1 10 0c2 2 5 3 8 5l1 1c-1 1-4 0-6 0 1-1 2 0 3-1l-1-1c-1 0-1 0-2-1 0 0-2-1-3-1v-1h-3-3-4-5-1c-1 0-2 1-4 1v1h0l2 2c-2-1-4-2-5-3v-1c1-1 5-1 6-1h0z" class="R"></path><path d="M184 361h4 3 3v1c1 0 3 1 3 1 1 1 1 1 2 1l1 1c-1 1-2 0-3 1v1h0c-1 0-3 0-4-1h-3l-2-1v-1h-1 0l-3-3z" class="H"></path><path d="M194 251c0-3 1-5 3-7 2 1 3 1 5 1h0l2 1h0c0 1 1 3 2 3h0l3 6c1 1 2 3 1 4 0 1-1 1-1 2h-3l-2-1-5-3v-1l-5-4 1-1h-1z" class="M"></path><path d="M206 249h0l3 6c1 1 2 3 1 4 0 1-1 1-1 2h-3c1-1 1-1 1-2 1 0 1-1 1-1 1-2-1-7-2-9z" class="E"></path><defs><linearGradient id="e" x1="200.451" y1="259.014" x2="200.817" y2="252.911" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#848384"></stop></linearGradient></defs><path fill="url(#e)" d="M195 251c4 2 8 6 12 8h0c0 1 0 1-1 2l-2-1-5-3v-1l-5-4 1-1z"></path><path d="M182 208c0 3 1 7 3 10 2 2 4 3 6 5l11 9c-2-4-4-8-4-12 0 1 1 1 1 2v1h1v1c0 1 0 2 1 3 0-1 0-1-1-2v-1-1c1 2 1 4 2 6 2 4 6 8 9 10l-5-10c1 1 2 1 2 2s1 3 2 5l1 1v2c0 1 2 3 3 3s1 1 2 1l1 1v-1l1 1-1 1c-1-1-2-1-3-2v1c-1 0-2-1-2-2-1 1-2 2-3 2l-1-1-2-3-13-9 1-1c0-2-3-3-4-5v-2c-5-4-8-7-10-13h1l1-2z" class="F"></path><path d="M209 239l3 3c-1 1-2 2-3 2l-1-1c1-1 1-2 1-4z" class="G"></path><path d="M206 237l3 2c0 2 0 3-1 4l-2-3v-3z" class="O"></path><defs><linearGradient id="f" x1="206.048" y1="235.998" x2="193.999" y2="232.738" xlink:href="#B"><stop offset="0" stop-color="#6f6e70"></stop><stop offset="1" stop-color="#878786"></stop></linearGradient></defs><path fill="url(#f)" d="M190 223l16 14v3l-13-9 1-1c0-2-3-3-4-5v-2z"></path><path d="M521 148c2-1 3-2 4-4l12-12c1-1 3-3 5-4h0c-5 6-10 11-15 16-8 10-15 21-21 31l-10 17-3 7c-1 0-1 1-2 2h0-1 0l-1 1c3-9 8-16 12-24 7-10 13-20 20-30z" class="H"></path><defs><linearGradient id="g" x1="283.152" y1="471.425" x2="273.347" y2="449.846" xlink:href="#B"><stop offset="0" stop-color="#131314"></stop><stop offset="1" stop-color="#484747"></stop></linearGradient></defs><path fill="url(#g)" d="M272 447c6 4 12 9 15 16v1c1 1 1 0 2 1l-1 2s0 1 1 1l-1 2v-2l-1 2-1-1c-1-1-1-1-3-2l-3-2h0c0-1 0-2-1-3-2 0-3 0-4-2-3-2-6-4-9-5 2 0 3-2 5-3h0c1 0 2-1 3-2v-1s-1 0-1-1l-1-1z"></path><defs><linearGradient id="h" x1="404.92" y1="217.626" x2="388.325" y2="217.504" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#a5a5a5"></stop></linearGradient></defs><path fill="url(#h)" d="M395 223c1 0 3-1 4-1 1-1 2-1 2-3 1-1 0-4-1-6-2-4-8-7-12-8-2-1-4-1-5-1 2-1 3-1 5-1 6 1 13 4 17 8 0 1 1 2 1 3 0 2-1 3-1 4l-1 9c0 3 0 7-1 9-1 1-1 1-2 1v-1l-1-1h2v-1c-1-3-4-6-6-8v-1c0-1-1-1-1-2z"></path><path d="M218 301c1 0 2 0 2 1l1-1h2l1 1c1 0 2 0 4 1s6 5 7 7v1l-1 1c0 2 0 5 1 6 1 2 1 3 3 4h0v3l-1 1c0 1 0 1-1 2l1 1v1l-3-2-2-1c-2-7-4-15-8-20-2-3-4-4-6-6z" class="U"></path><path d="M223 301l1 1c2 4 5 7 7 10l3 9h1v-3c1 2 1 3 3 4h0v3l-1 1c0 1 0 1-1 2-3-4-4-10-6-15-1-2-2-3-3-5s-4-4-5-6l1-1z" class="O"></path><path d="M235 318c1 2 1 3 3 4h0v3l-1 1-3-5h1v-3z" class="M"></path><path d="M224 302c1 0 2 0 4 1s6 5 7 7v1l-1 1c0 2 0 5 1 6v3h-1l-3-9c-2-3-5-6-7-10z" class="U"></path><path d="M228 303c2 1 6 5 7 7v1l-1 1c-1-3-2-5-5-7-1 0-1 0-2-1l1-1z" class="X"></path><path d="M471 288c0-2 0-3 1-4 1 0 1-1 1-2 2 1 2 3 3 3 0 1 1 1 1 1v1 2h0c-2 2 0 2-1 3 0 1-1 1-1 2s1 0 2 1v3c-1 0-3 1-4 2 1 0 1 1 1 1h-1c0-2-1-3-3-5-1 0-1 1-2 1 1 1 1 1 2 1 1 1 1 2 1 3s0 1-1 2h-2 0l-3 1-2 1c-2 0-3 1-4 3l-1-1c0-3 2-5 3-7 1 0 1-1 2-1v-2c3-2 6-6 8-9z" class="D"></path><path d="M465 304h-1-1l-1-1c1-1 2-2 3-2v-2c0-1 1-2 2-2h1c1 1 1 1 2 1 1 1 1 2 1 3s0 1-1 2h-2 0l-3 1z" class="P"></path><path d="M470 298c1 1 1 2 1 3s0 1-1 2h-2s-1 0-2-1c0-1 1-1 1-2l1-1c1 0 1 0 2-1zm-3-1c0-2 1-3 2-4 1-2 3-3 4-4v-1c1-1 2-2 3-2l1 1v2h0c-2 2 0 2-1 3 0 1-1 1-1 2s1 0 2 1v3c-1 0-3 1-4 2 1 0 1 1 1 1h-1c0-2-1-3-3-5-1 0-1 1-2 1h-1z" class="E"></path><defs><linearGradient id="i" x1="444.133" y1="408.993" x2="448.713" y2="414.959" xlink:href="#B"><stop offset="0" stop-color="#323131"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#i)" d="M456 401v1c-1 2-1 3 0 5v1c1 0 1 1 2 1l-16 12c-3 2-8 6-9 10v-2l1-3s0-1-1-1h0v-1l1 1h0l1-1s1 0 1-1l1-1 1-1v-1l1-1h-1l-1 1c1-4 5-8 8-10l3-3c1-1 3-2 5-4l3-2z"></path><path d="M456 408c1 0 1 1 2 1l-16 12v-1c2-2 3-3 4-5 2-1 3-3 5-4h0c1-1 1-1 1-2 2 0 2-1 3-1l1 1v-1z" class="G"></path><path d="M173 204c0-1 0-4-1-5 0-3-2-5-4-6 2 0 4 3 5 4 1 3 1 6 3 8l4 5c2 6 5 9 10 13v2c1 2 4 3 4 5l-1 1-8-5c-1-1-2-1-4-2h1v-1c-1-1-2-3-3-4-3-4-5-8-10-10h1v-1c1-1 2-2 3-4z" class="J"></path><path d="M173 204v-3h1v5c3 7 10 12 12 19l-1 1c-1-1-2-1-4-2h1v-1c-1-1-2-3-3-4-3-4-5-8-10-10h1v-1c1-1 2-2 3-4z" class="K"></path><defs><linearGradient id="j" x1="269.283" y1="393.472" x2="238.463" y2="393.77" xlink:href="#B"><stop offset="0" stop-color="#646265"></stop><stop offset="1" stop-color="#9e9f9e"></stop></linearGradient></defs><path fill="url(#j)" d="M239 369l4 8h0c3 6 6 11 10 16 6 7 12 14 19 21-1 0-1 1-1 1 0 1 0 1-1 2l-14-15c-7-7-12-15-16-23 0-1-2-5-3-6l1-1c1-1 1-2 1-3z"></path><path d="M176 365l-2-2h0v-1c2 0 3-1 4-1h1 5l3 3h0 1v1l2 1h3c1 1 1 1 2 1 1 1 1 1 2 1v5 1h1l-5-1c-1 1-1 2-1 3h0c0 1 0 1-1 2h0c-1 0-3 1-4 1l-3 1c-1-1-1-1-1-2v-3-1l2-1h1 1v-1h1v-1l-1-1c-4-2-8-3-11-5z" class="L"></path><path d="M185 373c1 0 3 0 4 1l-1 1h1c1 0 1-1 1-1l1-1c0 2 0 3 1 3 0 1 0 1-1 2h0c-1 0-3 1-4 1l-3 1c-1-1-1-1-1-2v-3-1l2-1z" class="C"></path><path d="M183 375c1 0 2 1 3 3l1 1-3 1c-1-1-1-1-1-2v-3z" class="F"></path><path d="M188 365l2 1h3c1 1 1 1 2 1 1 1 1 1 2 1v5 1h1l-5-1c-1 1-1 2-1 3h0c-1 0-1-1-1-3v-1c-1-2-2-3-3-5l-1-1c0-1 1-1 1-1z" class="Q"></path><path d="M190 366h3c1 1 1 1 2 1 1 1 1 1 2 1v5 1h1l-5-1h2 0c1-1 1-2 1-4-1 0-1-1-2-1-1 1-1 1-2 1l-2-3z" class="V"></path><path d="M480 297c1 0 4-1 5 0 1 0 2 1 3 1 3 2 5 3 8 6 7 5 10 11 11 20 1 10-4 20-9 29-1 1-2 2-3 4l-10 11-1 1-1-1c0-1 2-2 3-4 4-3 9-9 11-13l3-5c2-4 4-9 5-14 2-6 1-13-2-18-4-7-12-11-19-13-3 0-7-1-10 0 0 0 0-1-1-1 1-1 3-2 4-2 1-1 2-1 3-1z" class="T"></path><path d="M142 246v-2c2-8 6-14 13-19 6-4 13-5 20-3l-1 1 1 1c0 1 1 3 2 3 0 1 1 1 1 1 1 0 2 0 2 1-1 0-1-1-2-1h-1v1s-1 1-1 0h-1c-3-2-7-2-10-2-7 1-12 5-16 10-3 4-5 8-7 13v3-7z" class="G"></path><path d="M249 430l8 4 2 1 1 1c1 2 2 2 3 3 3 3 6 5 9 8l1 1c0 1 1 1 1 1v1c-1 1-2 2-3 2h0c-2 1-3 3-5 3-3-1-5-1-8 0 1-1 2-1 2-1s0-1 1-1v-3h0v-4c-1-5-9-10-13-13v-2l1-1z" class="C"></path><defs><linearGradient id="k" x1="458.198" y1="341.954" x2="472.204" y2="346.214" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#30302f"></stop></linearGradient></defs><path fill="url(#k)" d="M465 319l1-2h0c1-2 1-3 2-3v1c-2 5-3 9-3 14-1 6-1 11 1 16 2 3 3 6 6 7l-1 2h0-1-1c-2-1-3-3-3-5h-1c-1 0-2 0-2 1-1 2-1 6-1 8l-3 9c-1 4-2 7-4 10v-2c1-3 2-6 3-10 2-9 2-18 2-27 0-5 1-9 3-13 1-2 1-4 2-6z"></path><path d="M460 269l10-27c0 1 0 2 1 3v-1 1h1l-13 32-13 33-5 16c-1 2-1 5-2 7h-1 0 0l-1-1c0 2-1 3-2 4 1-4 3-9 5-13l20-54z" class="S"></path><defs><linearGradient id="l" x1="491.054" y1="232.217" x2="498.622" y2="232.963" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#919191"></stop></linearGradient></defs><path fill="url(#l)" d="M510 216l1-2c1-2 2-3 3-5s2-3 3-5l3-8v3h0c-1 4 0 6 1 9h1l-3 3c-1 2-3 4-4 5v7c-3 1-5 2-7 3l-6 3-11 8-1-1v-3l13-10c2-2 5-4 7-7z"></path><defs><linearGradient id="m" x1="503.46" y1="224.622" x2="511.995" y2="225.181" xlink:href="#B"><stop offset="0" stop-color="#c1bebf"></stop><stop offset="1" stop-color="#e3e3e5"></stop></linearGradient></defs><path fill="url(#m)" d="M515 216v7c-3 1-5 2-7 3l-6 3h-1v-1l5-5c2-1 3-3 5-4l2-1c0-1 1-1 2-2z"></path><path d="M405 211l4 4h0c0 1 1 2 1 3l1 2 1 3c0 2 0 6 1 8 0 1-1 3-1 5l-1 3-1 3c0 1-1 2-2 3h-1l-1 1h0 0c-1-3-3-5-6-8l1-1c1 0 1 0 2-1 1-2 1-6 1-9l1-9c0-1 1-2 1-4 0-1-1-2-1-3z" class="P"></path><path d="M409 215c0 1 1 2 1 3l1 2 1 3c0 2 0 6 1 8 0 1-1 3-1 5l-1 3-1 3c0 1-1 2-2 3h-1v-2c0-2 1-5 1-7l1-21z" class="G"></path><path d="M411 220l1 3c0 2 0 6 1 8 0 1-1 3-1 5l-1 3c-1-2-1-5 0-7 1-4 0-8 0-12z" class="M"></path><path d="M412 223c0 2 0 6 1 8 0 1-1 3-1 5-2-2-1-4 0-6v-7z" class="F"></path><path d="M389 480c2-1 2-6 4-8v-1h1 0l-31 111h-1 0v-2c0 1 0 3-1 4 0-2 1-4 1-6 1-7 3-13 5-19 0-2 1-4 1-6 1-2 2-6 2-9l4-14c2-7 5-14 7-21 0-2 0-3 1-5 2-4 3-9 4-14l3-10z" class="L"></path><path d="M171 369c1 0 1-1 2 0 2 0 3-2 5 0l2 1c2 2 4 1 6 1l1 1v1h-1-1l-2 1v1 3c0 1 0 1 1 2-3 1-6 2-9 4h-4c1-2 2-2 2-4l-1-1c-3 3-6 5-8 9l-2-2c1-3 1-4 0-6 5-3 7-5 9-11z" class="V"></path><defs><linearGradient id="n" x1="172.229" y1="380.514" x2="181.003" y2="379.366" xlink:href="#B"><stop offset="0" stop-color="#5f5d5e"></stop><stop offset="1" stop-color="#7a7a7a"></stop></linearGradient></defs><path fill="url(#n)" d="M178 375c2 0 3-1 5-1v1 3c0 1 0 1 1 2-3 1-6 2-9 4h-4c1-2 2-2 2-4l-1-1c1-1 5-3 6-4z"></path><path d="M178 375c2 0 3-1 5-1v1 3 1h-3c-1-1-1-2-2-4z" class="J"></path><path d="M466 371h3c1-1 1-4 1-5 1-2 3-5 5-6h1v1 7l1-1h1v1c0 1-1 2-2 3l2 1v2h1c1 1 1 1 1 2 1 0 2 0 3 1l-1 1-5 1-1 1c-5 1-9 4-13 7-6 4-12 8-17 13l-1-1 11-11c1 0 3-1 4-2 2-2 7-4 8-6v-4c0-1 0-2-1-3l-1-1v-1z" class="Y"></path><path d="M478 374h1c1 1 1 1 1 2-1 0-2 1-3 1 0-1 0-2 1-3z" class="H"></path><path d="M477 377c1 0 2-1 3-1s2 0 3 1l-1 1-5 1v-2z" class="T"></path><path d="M466 371h3c1-1 1-4 1-5 1-2 3-5 5-6h1v1h-1c0 1-1 2-2 4 0 0 0 1-1 2 1 2 1 2 1 3-1 0-1 1-2 1s-2 1-2 1l-1 1v3c0-1 0-2-1-3l-1-1v-1z" class="Q"></path><path d="M476 368l1-1h1v1c0 1-1 2-2 3-1 2-1 4-1 7h0c0 1-1 1-2 2h0-1-2v-6c2-1 2-2 4-3h1c1-1 1-2 1-3z" class="N"></path><defs><linearGradient id="o" x1="429.176" y1="437.227" x2="436.886" y2="444.419" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#696969"></stop></linearGradient></defs><path fill="url(#o)" d="M437 433c5-2 10-5 15-7v1h1 0 1c-1 1-1 0-2 1l-1 1c-1 1-3 2-4 3-6 4-12 8-15 13h0c-1 1-2 1-2 2-1 2-1 3-1 4v3h0v1h0c-2 0-3 0-5 1l-2 1h-1c-1 0-1 0-2-1h0c-1-1-3-1-4-1v1c-1 0-2 0-3-1 0 1 0 2-1 2 0 1 1 1 1 1v1h-1v-1c0-4 18-17 22-20 1-1 1-2 1-3h0l3-2z"></path><defs><linearGradient id="p" x1="517.688" y1="238.715" x2="545.474" y2="229.978" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#p)" d="M516 223c4-3 10-2 15 0 6 1 13 7 16 13 2 3 4 8 4 12 0 2 0 5-1 7 0-5 0-9-3-13-3-7-9-12-16-14h-1c-3-1-7-1-10-1-1 1-2 2-3 2-1-1-1 0-1-1h0-1c0 1-2 2-3 3-2 1-2 2-2 5h0l-5 1c0-1 1-1 1-2 1-1 1-4 2-6 2-2 7-2 8-5v-1z"></path><defs><linearGradient id="q" x1="194.588" y1="209.152" x2="186.465" y2="217.888" xlink:href="#B"><stop offset="0" stop-color="#585858"></stop><stop offset="1" stop-color="#818081"></stop></linearGradient></defs><path fill="url(#q)" d="M182 208c-1-3-1-6-1-10-1-1-1-3-1-5-2-7-4-10-9-15h1c6 4 8 11 10 18 0 3 1 6 3 9 1 2 3 3 4 3 2 0 3-1 4-2 1 1 1 4 1 6 1 2 1 5 2 7 1 0 1 1 2 1 0 4 2 8 4 12l-11-9c-2-2-4-3-6-5-2-3-3-7-3-10z"></path><path d="M247 354h3c-2 3-3 6-4 9-1 2 0 3 0 5s1 4 2 5h1l1 1c0 2 1 4 2 5l2 3c1 1 2 2 4 2v1c1 1 3 2 4 4s0 5 0 7c2 1 3 3 3 5 0 0 0 1-1 1l-1 1c-3-4-7-8-9-12-4-4-8-10-10-15l-2-8c0-3 0-6 2-10l2-3h0l1-1z" class="C"></path><path d="M246 368c0 2 1 4 2 5 0 2 1 2 1 3h-1l-1 1h0c0-3-1-6-1-9z" class="O"></path><path d="M248 373h1l1 1c0 2 1 4 2 5l2 3h-2c-2-1-4-3-5-5l1-1h1c0-1-1-1-1-3z" class="D"></path><defs><linearGradient id="r" x1="266.384" y1="398.799" x2="250.663" y2="387.174" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#r)" d="M254 391h-1v-3c-1-2 0-3 0-4h0c2 1 4 3 5 4 1 2 2 2 2 4v1c0 2 1 2 2 3 2 1 3 3 3 5 0 0 0 1-1 1l-1 1c-3-4-7-8-9-12z"></path><path d="M194 251h1l-1 1 5 4v1h0l9 9c1 2 2 3 3 5s2 3 3 5c1 0 2 1 3 1h1l1-1 1-1c1 2 1 2 2 3v1c0 1 1 1 2 2l3 3h-1 0-2v1 4c0-1 0-1-1-2s-2-2-3-2c-1-1-2-1-3-1v3l-1 2-1-2c0-1 0-2-1-3s-2-3-4-4c0-1-2-2-3-3s-1-2-1-3c-1 1-2 1-3 1-2 0-3 1-5 2h-1c1-2 3-2 5-3v-4c0-1-1-2-1-2 0-2 0-2 1-2v-1h-1c-3-5-6-9-10-12 1 0 1 0 2-1 0 0 0-1 1-1z" class="G"></path><path d="M202 270c1 0 2-1 3-1l1 1c1 1 3 2 4 3h-6l-2 1v-4z" class="D"></path><path d="M213 278c1 0 4 2 6 3s3 3 5 4v4c0-1 0-1-1-2s-2-2-3-2c-1-1-2-1-3-1v3c0-4-2-6-4-9z" class="P"></path><path d="M206 274c2 0 4 1 5 2v1c1 0 1 1 2 1 2 3 4 5 4 9l-1 2-1-2c0-1 0-2-1-3s-2-3-4-4c0-1-2-2-3-3s-1-2-1-3z" class="M"></path><path d="M472 355c2 1 4 0 6 0 4 0 9 0 13-2 2 0 4-1 6-2h0c-2 4-7 10-11 13-1 2-3 3-3 4l1 1 1-1c2 1 5 0 7-1v1c-1 0-2 1-3 1v1 1c-1 1-1 2-2 4h3l-6 2h-1c-1-1-2-1-3-1 0-1 0-1-1-2h-1v-2l-2-1c1-1 2-2 2-3v-1h-1l-1 1v-7-1h-1c-2 1-4 4-5 6 0 1 0 4-1 5h-3c1-1 1-2 1-3v-1c1-1 1-2 1-3 3-5 5-6 10-5h3c1-1 1 0 1-1h1 0-2 0l-1-1h-2-1s-1 0-2-1h-1 0-2v-1z" class="H"></path><path d="M478 372c3 0 5 0 7-1-1 1-2 3-3 5l4-2c1 0 1-1 2-1 0-1 0-2 1-3v1c-1 1-1 2-2 4h3l-6 2h-1c-1-1-2-1-3-1 0-1 0-1-1-2h-1v-2z" class="V"></path><path d="M478 367c0-1-1-1 0-2v-1c0-1 0-2-1-3h1 1c1 1 5-1 6-1s3-1 4-1c-3 4-6 7-9 10l-2-1v-1z" class="T"></path><path d="M444 357v-1h1c1 1 1 1 2 1 2 2 3 6 3 8s0 3 1 5v1c-4 10-10 19-17 26-1 2-3 4-5 5l-2 2-1-1c-1-5 1-11 4-15 1-1 2-2 3-4h2 2c4-2 6-6 8-10 0 0-1 0-1-1 1-4 2-7 0-12 0-1-1-1-1-2h0 1v-1-1h0z" class="C"></path><path d="M444 357c2 2 2 6 2 9s0 6-1 8c0 0-1 0-1-1 1-4 2-7 0-12 0-1-1-1-1-2h0 1v-1-1z" class="U"></path><path d="M435 384c0 1 0 2-1 2-1 1-1 1-2 1l-1 1h0c-1 1-1 2-2 3h1l-1 3h0c-1 1-1 3-1 5h0c-1 1-1 3-1 3v1c1 0 2-1 2-1l-2 2-1-1c-1-5 1-11 4-15 1-1 2-2 3-4h2z" class="C"></path><path d="M435 336c1-1 2-2 2-4l1 1h0 0 1l-17 50-28 88h0 0-1v1c-2 2-2 7-4 8l7-22h0l9-26c1-6 3-11 5-17l11-35 14-44z" class="S"></path><path d="M435 336c1-1 2-2 2-4l1 1h0 0 1l-17 50v-1-2h0c1-1 1-1 1-2v-1h0c1-1 1-2 1-3 0 0 0-1 1-1v-2-1c1 0 1-1 1-1v-1h1c-1-1 0-1 0-2v-1h0l1-1v-1c0-1 1-2 1-4h0v-1c1 0 1 0 1-1h0v-1c0-1 1-2 1-3-2 1-4 11-5 14-2 5-3 10-5 14v-1l14-44z" class="V"></path><defs><linearGradient id="s" x1="460.997" y1="378.938" x2="462.644" y2="371.24" xlink:href="#B"><stop offset="0" stop-color="#676667"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#s)" d="M455 377c2-3 3-6 4-10l3-9c0-2 0-6 1-8 0-1 1-1 2-1h1c0 2 1 4 3 5h1s1 1 2 1h0v1h2 0 1c1 1 2 1 2 1h1 2l1 1h0 2 0-1c0 1 0 0-1 1h-3c-5-1-7 0-10 5 0 1 0 2-1 3v1c0 1 0 2-1 3v1l1 1c1 1 1 2 1 3v4c-1 2-6 4-8 6-1 1-3 2-4 2l3-3h-1v1l-1-1c1 0 0 0 1-1-1 0-1-1-2-1s-1-1-2-1c0 1 0 1-1 1h0c0-2 1-4 2-6z"></path><path d="M455 377c2-3 3-6 4-10l3-9c0-2 0-6 1-8 0-1 1-1 2-1h1c0 2 1 4 3 5h1s1 1 2 1h0v1c-3 0-4 1-6 3-2 4-4 7-6 11-1 3-4 6-3 10 0 1 0 1 1 2h1c3-1 5-3 8-4 1 0 1 1 1 2-1 2-6 4-8 6-1 1-3 2-4 2l3-3h-1v1l-1-1c1 0 0 0 1-1-1 0-1-1-2-1s-1-1-2-1c0 1 0 1-1 1h0c0-2 1-4 2-6z" class="B"></path><path d="M282 153c2 13 7 25 12 37l15 40 12 34 7 21 14 42h0-1v1 1l-49-136c0-1 0-2-1-3l-2-7-6-16-2-11c0-1 0-2 1-3z" class="Y"></path><path d="M493 367h1c1 0 4-1 5-2-1-1-1-1-3-1h0c1-1 1-2 2-2l1-1c1 0 2-1 3-1 2 0 3 0 5 1l8-1c0 1 0 1 1 1 3 2 5 0 8-2l-3 3v1l4-2v1c-2 2-3 3-3 5v1c-2-2-5 0-7-1-2 1-4 2-5 3l-2 1-1 1c0 1 1 1 2 1h-1c-1 0-2 1-3 1l-1 2-1-1c-1 1-2 1-3 1-1 2-1 2-2 2-2 0-4 1-6 1-2-1-3-1-5-1h0c-1 0-2 0-3-1l6-2h-3c1-2 1-3 2-4v-1-1c1 0 2-1 3-1v-1h1z" class="T"></path><path d="M507 372h-1c-1 0-1-1-1-2l1-1 1 1 1-1v1h0v1l-1 1z" class="N"></path><path d="M506 369l1-1c1-1 2 0 3 0 4-2 7-3 11-6v1c-2 2-4 3-6 4s-4 2-5 3l-2 1v-1h0v-1l-1 1-1-1z" class="R"></path><path d="M521 363l4-2v1c-2 2-3 3-3 5v1c-2-2-5 0-7-1 2-1 4-2 6-4z" class="L"></path><defs><linearGradient id="t" x1="493.295" y1="378.246" x2="494.23" y2="372.946" xlink:href="#B"><stop offset="0" stop-color="#797678"></stop><stop offset="1" stop-color="#8b8c8b"></stop></linearGradient></defs><path fill="url(#t)" d="M490 375c4-1 7-2 11-2 1-1 2-3 3-5h1c-1 2-3 4-3 5s1 1 1 2c-1 1-2 1-3 1-1 2-1 2-2 2-2 0-4 1-6 1-2-1-3-1-5-1h0c-1 0-2 0-3-1l6-2z"></path><path d="M487 378l13-2c-1 2-1 2-2 2-2 0-4 1-6 1-2-1-3-1-5-1z" class="Z"></path><path d="M198 357l-3-4c-7-9-11-22-9-33 2-8 7-15 14-19 2-2 5-2 7-4 1-1 2-1 4 0h3c1 0 1 1 1 2v1h0c-7 0-15 3-21 8-5 4-7 11-7 17 0 4 0 7 1 11 0 1 1 2 1 3 1 1 1 1 2 1 0 1-1 1-1 2s2 3 2 4l2 3c4 8 11 13 16 20 1 1 3 2 4 2l1 1h1s1 0 1-1c3 0 5 0 8 1h-4 2 1c1 0 2 0 2 1h0c-1 0-1 1-1 1v2 2c-1-1-1-3-1-4 0 0 0-1-1-1h0c-1 0-1 0-2-1-1 0-1 1-2 0v4l1-1c2 0 3 2 3 3v2h-1-1c0-1-1-1-2 0h-1l-3-1c-2-1-4-2-6-2l-3-1-6-2h-2-1v-1-5c-1 0-1 0-2-1-1 0-1 0-2-1 1 1 3 1 4 1h0v-1c2 0 5 1 6 0l-1-1c-1-2-3-5-5-7l1-1z" class="T"></path><path d="M198 357c2 3 4 6 6 8s4 3 6 5l-13-3v-1c2 0 5 1 6 0l-1-1c-1-2-3-5-5-7l1-1z" class="F"></path><path d="M522 208c2 2 3 3 6 4 4-1 6-2 9-4h1c0 1-1 2-2 3h0 0l2 1c0 2 0 4 2 6 1 2 4 4 6 4s5-1 6-3l2-2 1 1c-2 4-11 5-8 10 1 2 3 5 4 6 2 1 4 1 5 2h0c-1 1-2 1-3 2-1 2 0 6 1 8v1l-1 1v1c-1 0 0 0-1 1l-1-2c0-4-2-9-4-12-3-6-10-12-16-13-5-2-11-3-15 0h-1v-7c1-1 3-3 4-5l3-3zm-365 1c3 1 6 3 9 2 2 0 2-1 3-2 5 2 7 6 10 10 1 1 2 3 3 4v1h-1l-2-1-4-1c-7-2-14-1-20 3-7 5-11 11-13 19v2l-1 3h-1v-1h-1l-1-1v-1l1-1c1-2 1-5 1-6-1-2-2-2-3-3h-1c5-2 7-3 9-8 3-5-5-6-7-10h0c2 1 3 2 5 3 1 1 3 1 5 1 2-1 5-3 6-5 1-3 0-5 0-7h1c1 0 1 0 2-1zm255-53l1 1c0 1-1 3-1 4s1 3 2 4c0 1 1 2 1 4-3 3-7 7-11 10s-9 5-14 7l-9 3c-4 1-8 2-12 4-2 1-4 3-6 4-1 1-2 2-2 3v1c1 1 1 2 0 3h0l-1-2h0c-2 3-5 6-6 10-4 8-5 16-4 25v4h1v2c0-1-1-1-1-2v-1c-3-9-1-22 3-31 2-3 4-7 6-10 0-1-1-2-1-2 1 0 1 0 2 1l4-4c1-1 1-2 1-3v-1h2c3-1 6-3 9-5 2-1 5-2 7-4v-1s1 1 1 0c2 0 2-1 3-2h0 2c1-1 3-1 4-2l1-1v-1c1 0 1 1 2 1h0c2 0 3-2 5-3 5-4 10-9 11-16z" class="V"></path><path d="M412 156l1 1c0 1-1 3-1 4s1 3 2 4c0 1 1 2 1 4-3 3-7 7-11 10v-2-1-3c1-1 0-1 0-1l1-1c0-1 1-1 1-2 1 0 1-1 2-2h-1v1c-2 2-4 4-6 4h0c5-4 10-9 11-16z" class="N"></path><defs><linearGradient id="u" x1="348.052" y1="607.284" x2="356.408" y2="609.828" xlink:href="#B"><stop offset="0" stop-color="#aaa9a9"></stop><stop offset="1" stop-color="#d9d8d9"></stop></linearGradient></defs><path fill="url(#u)" d="M355 585c2-2 3-5 4-8l6-19 6-22 5-16c3-10 6-20 10-30-1 5-2 10-4 14-1 2-1 3-1 5-2 7-5 14-7 21l-4 14c0 3-1 7-2 9 0 2-1 4-1 6-2 6-4 12-5 19 0 2-1 4-1 6 1-1 1-3 1-4v2h0 1l-15 60v-1c-1-2-1-4-1-6l1-10v-20 2h0c2-1 2-5 3-7 1-4 2-9 4-13v-2z"></path><path d="M405 421c2-2 3-4 4-6h1c-2 6-4 11-5 17l-9 26h0l-7 22-3 10c-4 10-7 20-10 30l-5 16-6 22-6 19c-1 3-2 6-4 8v2c-2-3 0-9 1-12l-2 1c-1-1 4-18 4-21v-1l21-63 5-14h0c1-1 1-2 1-2 2-5 3-10 5-14 0-2 1-5 2-6 1 0 1-1 1-1 1-2 2-6 3-7l7-21c1-1 1-3 2-5z" class="I"></path><path d="M355 585c1-4 2-7 3-11l4-11 16-54c1-3 3-6 4-9 1-5 2-11 4-16l7-17c0-3 1-5 2-7 0-1 0-1 1-2l-7 22-3 10c-4 10-7 20-10 30l-5 16-6 22-6 19c-1 3-2 6-4 8z" class="J"></path><path d="M384 477h0c1-1 1-2 1-2 2-5 3-10 5-14 0-2 1-5 2-6 1 0 1-1 1-1 1-2 2-6 3-7 0 2-1 5-2 7l-3 9-7 23-3 9-2 8-5 14-12 39-6 19h0l-2 1c-1-1 4-18 4-21v-1l21-63 5-14z" class="C"></path><defs><linearGradient id="v" x1="231.777" y1="320.78" x2="226.819" y2="355.587" xlink:href="#B"><stop offset="0" stop-color="#030303"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#v)" d="M228 327c1-3 0-4 0-7 0 2 1 4 2 5 1 4 3 8 3 12h0c1 1 4 4 4 5-1 4-1 8-1 12 1 5 1 10 3 15 0 1 0 2-1 3l-1 1c1 1 3 5 3 6-2 2 1 5-2 7h-1c-2 0-3-1-4-2l-7-6v1l-1-2h0c1-3 1-3 3-5-1-1-2 0-3 0-3-1-5-1-8-1 0 1-1 1-1 1h-1l-1-1c-1 0-3-1-4-2-5-7-12-12-16-20l-2-3c2 2 4 1 7 2h1 0c5 3 12 6 19 5 2 0 4-2 6-5 2-2 2-5 3-8 1-4 1-9 0-13z"></path><path d="M232 348v-10h0 1v-1h0 0l1 9v3l-1 5c-1-2-1-4-1-6z" class="U"></path><path d="M232 348c0 2 0 4 1 6l1-5 1 17h0l-1 1c-2-6-2-13-2-19z" class="X"></path><path d="M228 340c1 2 0 4-1 6 0 1-1 2-1 2 1 0 1-1 2-2 0-1 1-1 1-2l1-1h0c-2 5-4 10-10 12-3 1-7 0-10-1h-1c-3-1-6-3-9-4-1 0-3-2-4-2s-2 1-2 1l-2-3c2 2 4 1 7 2h1 0c5 3 12 6 19 5 2 0 4-2 6-5 2-2 2-5 3-8z" class="U"></path><defs><linearGradient id="w" x1="241.342" y1="352.207" x2="230.444" y2="358.089" xlink:href="#B"><stop offset="0" stop-color="#7b7878"></stop><stop offset="1" stop-color="#bdbebf"></stop></linearGradient></defs><path fill="url(#w)" d="M233 337c1 1 4 4 4 5-1 4-1 8-1 12 1 5 1 10 3 15 0 1 0 2-1 3l-1 1-2-7-1-17v-3l-1-9z"></path><path d="M205 361l-7-7c-1-1-1-2-2-3 5 2 11 6 16 6h1c2 1 4 0 6 0 3-1 6-3 8 0 1 1 1 3 2 4v1c0 2-1 4 0 6-1 0-2-2-3-3l-1-1c-1 0-1-1-1-1-1 0-3-2-3-2-1 0-4 1-5 1h-6c-1 1-2 1-2 2l-3-3z" class="I"></path><path d="M205 361c2-2 7-1 10-1 2 0 4-1 7-1 1 2 3 4 4 6l-1-1c-1 0-1-1-1-1-1 0-3-2-3-2-1 0-4 1-5 1h-6c-1 1-2 1-2 2l-3-3z" class="J"></path><defs><linearGradient id="x" x1="234.53" y1="378.04" x2="227.252" y2="377.917" xlink:href="#B"><stop offset="0" stop-color="#6c6b6c"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#x)" d="M216 362c1 0 4-1 5-1 0 0 2 2 3 2 0 0 0 1 1 1l1 1c1 1 2 3 3 3-1-2 0-4 0-6 1 1 1 3 2 5 2 2 5 5 5 7 0 1 0 5-1 6 0 1-1 3-2 4l-7-6v1l-1-2h0c1-3 1-3 3-5-1-1-2 0-3 0-3-1-5-1-8-1 0 1-1 1-1 1h-1l1-1-1-1c-3-2-5-4-7-6 0-1 1-1 2-2h6z"></path><path d="M225 368l1-1c1 0 3 3 4 4v2 1c-1 1-2 1-3 3h-2 0c1-3 1-3 3-5h0l1-1c-2-1-3-2-4-3z" class="J"></path><path d="M208 364c0-1 1-1 2-2h6s1 1 1 2c1 0 2-1 3-1 1 1 3 2 4 4l1 1c1 1 2 2 4 3l-1 1h0c-1-1-2 0-3 0-3-1-5-1-8-1 0 1-1 1-1 1h-1l1-1-1-1c-3-2-5-4-7-6z" class="R"></path><path d="M215 370c-1-1-1-2-1-4 0 0 1-1 2-1s3 0 4 1c2 2 3 4 5 5h4l-1 1h0c-1-1-2 0-3 0-3-1-5-1-8-1 0 1-1 1-1 1h-1l1-1-1-1z" class="S"></path><path d="M421 136c3 12 4 25 4 37 0 23-1 46-5 68l-2 13c-2 12-5 24-8 35-1-1-1-2-1-3-1-4 0-7-1-11 0-3-1-5-1-8l-1-6h0 1c1-3 2-6 3-10 0-3 0-5 1-8l-1-1 1-3 1-3c0-2 1-4 1-5h0 0c1 0 1 0 1-1 2-4 2-12 2-17l-1-5h-1c-1 0-1 0-1-1l-1-1c-1-2-2-5-4-7-1 0-1 0-1-1l-1-1v-1c-1 0-1 0-2-1h0l-1-1c-1 0-2 0-2-1-2-1-3-2-4-3l-1-2-1 1c-1 0-2-1-3-2h0-1l-1-1c5-2 10-4 14-7s8-7 11-10c0-2-1-3-1-4-1-1-2-3-2-4s1-3 1-4l-1-1h0v-2-1h1c2-4 3-7 4-10h0v1l1-2h0v4h0c0 1 0 2 1 3v1c0 1 1 0 1 2v6h-1v2h1c1 0 1 1 2 2v-4l1-1v1c0 1 1 4 0 5 1 0 1 1 1 2 0-3 0-6-1-8v-1-3-2c-1-1 0-2-1-3v-2-1-1l-1-3h0v-2c-1-1-1-2 0-3z" class="M"></path><path d="M413 204l2 4h-1c-1 0-1 0-1-1l-1-1c1-1 1-1 1-2z" class="I"></path><path d="M409 188c2-1 4-3 6-3l1 1c-3 2-5 3-7 5-1-1-1 0-1-1h0v-2h1z" class="C"></path><path d="M418 190l-1-2h0c-2 1-3 3-5 4 1 0 1 1 2 1v2l-1 1-4-1h0c-1-1-1-1-2 0v-1c1-1 3-2 4-4l6-4s0 1 1 1v3h0z" class="D"></path><path d="M396 188v-1c3 2 6 4 9 7s6 6 8 10c0 1 0 1-1 2-1-2-2-5-4-7-1 0-1 0-1-1l-1-1v-1c-1 0-1 0-2-1h0l-1-1c-1 0-2 0-2-1-2-1-3-2-4-3l-1-2z" class="K"></path><path d="M416 213c1 5 1 14 0 19 0 3-1 5-1 7-1 3-1 5-2 7v1c0 2 0 4-1 6 0 4-2 9-4 13 0 1 0 1-1 1l-1-6h0 1c1-3 2-6 3-10 0-3 0-5 1-8l-1-1 1-3 1-3c0-2 1-4 1-5h0 0c1 0 1 0 1-1 2-4 2-12 2-17z" class="C"></path><defs><linearGradient id="y" x1="403.337" y1="169.096" x2="408.993" y2="186.892" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#4e4d4e"></stop></linearGradient></defs><path fill="url(#y)" d="M415 169v-1c1-2 3-4 4-6l-1 22h0v-1-3h-1v5-1 1c-1 0-1 0-1 1l-1-1c-2 0-4 2-6 3h-1v2h0c0 1 0 0 1 1l-2 1c-1 0-2 1-2 2-3-3-6-5-9-7v1l-1 1c-1 0-2-1-3-2h0-1l-1-1c5-2 10-4 14-7s8-7 11-10z"></path><defs><linearGradient id="z" x1="396.819" y1="186.231" x2="409.678" y2="187.645" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#z)" d="M392 187h1 0l1 1h0l2-1v-1h0 0c1 0 2 0 2-1 2-1 3-1 5-1l2-1 1 1h3 1 0c0 1 0 1-1 2l-1 1 1 1h-1v2h0c0 1 0 0 1 1l-2 1c-1 0-2 1-2 2-3-3-6-5-9-7v1l-1 1c-1 0-2-1-3-2z"></path><defs><linearGradient id="AA" x1="423.493" y1="175.96" x2="415.922" y2="176.962" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#646364"></stop></linearGradient></defs><path fill="url(#AA)" d="M412 156h0v-2-1h1c2-4 3-7 4-10h0v1l1-2h0v4h0c0 1 0 2 1 3v1c0 1 1 0 1 2v6h-1v2h1c1 0 1 1 2 2v-4l1-1v1c0 1 1 4 0 5-1 11 0 23-1 35 0 3 0 5-1 8 1 4-1 8-2 11v1c-2-3-2-24-1-28h0v-3c-1 0-1-1-1-1 1-1 1-2 1-2h0l1-22c-1 2-3 4-4 6v1c0-2-1-3-1-4-1-1-2-3-2-4s1-3 1-4l-1-1z"></path><path d="M418 190h0c0 5-1 11 0 16 1-3 0-7 0-11 1 2 0 9 1 10l1-12h0v2c0 1 0 2 1 2 0 1-1 3 0 3v3 3c1 4-1 8-2 11v1c-2-3-2-24-1-28z" class="O"></path><path fill="#121212" d="M192 346c0-1-2-3-2-4s1-1 1-2c-1 0-1 0-2-1 0-1-1-2-1-3-1-4-1-7-1-11 0-6 2-13 7-17 6-5 14-8 21-8 2 1 3 2 4 3 3 3 5 5 6 9l-1 1 4 14c1 4 1 9 0 13-1 3-1 6-3 8-2 3-4 5-6 5-7 1-14-2-19-5h0-1c-3-1-5 0-7-2z"></path><path d="M219 303c3 3 5 5 6 9l-1 1h0-1 0c-2-4-4-6-7-9h0 2l1-1z" class="M"></path><path d="M200 348c-2-2-3-4-5-7l6 5c4 4 12 6 17 5 3 0 4-2 6-4l1 1c-2 3-4 5-6 5-7 1-14-2-19-5z" class="R"></path><defs><linearGradient id="AB" x1="228.828" y1="325.552" x2="223.842" y2="328.042" xlink:href="#B"><stop offset="0" stop-color="#6a6b69"></stop><stop offset="1" stop-color="#89888b"></stop></linearGradient></defs><path fill="url(#AB)" d="M223 313h1 0l4 14c1 4 1 9 0 13-1 3-1 6-3 8l-1-1h0c4-9 3-18 1-27-1-2-1-5-2-7z"></path><path fill="#121212" d="M484 301c7 2 15 6 19 13 3 5 4 12 2 18-1 5-3 10-5 14l-3 5h0c-2 1-4 2-6 2-4 2-9 2-13 2-2 0-4 1-6 0h0c-1 0-2-1-2-1h1 0l1-2c-3-1-4-4-6-7-2-5-2-10-1-16 0-5 1-9 3-14l1-2h0v1c2-3 2-5 5-7 2-1 6-4 7-5v-1h3z"></path><path d="M468 315l1-2h0v1l-3 15h-1c0-5 1-9 3-14z" class="I"></path><path d="M465 329h1c0 5 0 10 2 16h-2c-2-5-2-10-1-16z" class="J"></path><path d="M473 310h1l3 3c3 2 6 5 7 8 0 2 0 4 1 6h-1c-1-3-2-6-3-8-2-3-8-7-8-9z" class="F"></path><path d="M468 345c1 2 2 4 4 6 7 1 14-1 20-5 3-2 5-5 6-9v-2c1 1 0 3 0 5 0 3-4 7-7 8l-4 3c-5 1-10 4-15 2v-1c-3-1-4-4-6-7h2z" class="I"></path><path d="M487 351h1c3 0 7-2 9-4 1 0 2-1 3-1l-3 5h0c-2 1-4 2-6 2-4 2-9 2-13 2-2 0-4 1-6 0h0c-1 0-2-1-2-1h1 0l1-2v1c5 2 10-1 15-2z" class="G"></path><path d="M86 153h0c-6 0-13-3-17-7-5-6-8-13-8-21 0-13 8-24 18-33 2-3 6-6 9-8 1-1 2-2 3-2h20 41 122 32 10c2 0 4 0 6 1v1h-1c-3 0-5 1-7 1l1 1c-1 1-2 1-3 1-2 1-4 2-5 3l-2 1-8 7-7 7c0 1 0 1-1 2-1 3-3 5-4 8h-1c-1 2-2 5-2 7l-1 3c-1 2-1 3-1 5 0 1 0 1-1 2v1 6 10c1 1 1 3 1 4l1 3 2 11 6 16 2 7c1 1 1 2 1 3l49 136c1 5 3 10 4 14s2 7 4 10l4 16c3 3 3 6 4 9h1v-1l8 26c2 4 4 8 4 12l40-126c3-11 6-23 8-35l2-13c4-22 5-45 5-68 0-12-1-25-4-37 0-2-1-4-2-6-3-13-11-23-21-33 0 0-1 0-1-1-2-1-4-2-5-4-5-3-10-5-15-8l-2-1h1c2-1 4-1 6-1h13 44 107 55c1 0 2 0 2 1 8 3 15 11 20 18 6 8 10 18 9 28-1 5-3 9-6 13v-1c0-1 0-1-1-2-3 4-6 8-11 10-2 1-3 1-4 2l-1 1v1c-4 1-7 0-11-1-4-2-6-5-8-9 2 2 5 5 8 6s6 1 9-1c5-2 8-6 9-11 2-7 1-13-3-19-2-2-3-4-5-5-6-4-13-6-20-6-14-1-27 6-38 15l-5 4c-1 1-2 2-3 2h0c-2 1-4 3-5 4l-12 12c-1 2-2 3-4 4-7 10-13 20-20 30-4 8-9 15-12 24l-19 40-10 27-20 54c-2 4-4 9-5 13l-14 44-11 35h-1c-1 2-2 4-4 6-1 2-1 4-2 5l-7 21c-1 1-2 5-3 7 0 0 0 1-1 1-1 1-2 4-2 6-2 4-3 9-5 14 0 0 0 1-1 2h0l-5 14-21 63v1c0 3-5 20-4 21l2-1c-1 3-3 9-1 12-2 4-3 9-4 13-1 2-1 6-3 7h0v-2 20l-1 10c0 2 0 4 1 6v1c-1 2-1 3-2 4-2 0-3-7-3-9l-14-60-39-131-14-44-11-34-37-102-20-51-16-33c-13-23-29-44-50-61-6-4-12-8-18-11-13-3-25-6-37 1-5 3-10 8-11 14-2 6-1 11 1 16 2 4 6 7 10 8h-2v1c1 0 2 1 3 1h1c1 0 3 0 4 1-2 1-4 1-7 1z" class="Y"></path><path d="M284 381l1 5h0v-1 2 1s-1-4-2-5v-1l1 1v-2z" class="Q"></path><path d="M441 276h-1c0-1 2-5 2-6v-1c0 1 0 1 1 2h0l-2 5z" class="T"></path><path d="M352 447h5 0c-2 1-4 1-7 1h0v-1l1 1 1-1z" class="R"></path><path d="M327 369l2-4v4c0 1-1 2-2 3l1-2-1-1z" class="G"></path><path d="M341 496c1 0 1-1 2-1h0l1-1-1 8c0-2-1-4-2-6z" class="E"></path><path d="M466 180v-3l4 2s2 2 2 3h0c-2-2-4-2-6-2z" class="H"></path><path d="M467 211h1c-1 3-3 5-4 7l-1-1v-1l1-1 3-4z" class="L"></path><path d="M395 398h1c1 1 1 1 1 2s-1 2-2 2h-1v-1c0-1 0-2 1-3z" class="B"></path><path d="M309 345l1 2c0 1-1 4-1 5-1 0-2 1-2 1h0l2-8z" class="G"></path><path d="M309 434l7 1c-2 1-7 1-9 2v-2h0l2-1zm151-246h1 0v2h2c-1 1-1 1-1 2v1h-1l-1-1v-1c0-2-3-1-5-1 1 0 3 0 4-1 1 0 1-1 1-1z" class="L"></path><path d="M268 287l2 11h0l-1-1v2 1-1l-1-5v-2-3h0v-2zm161-14l1 1c0-1 1-1 2-2l-2 5c-1 1-1 2-2 3l-1-1c1-2 1-4 2-6z" class="J"></path><path d="M441 128c-1-2-1-5 0-7 1 1 2 2 2 4v1h-1v2h-1z" class="C"></path><path d="M337 482c0-2 2-4 3-5v2 1c0 1-1 1-1 2h-1v1h0 1c1 1 1 1 1 2-2 0-2 0-4-2l1-1z" class="T"></path><path d="M322 327l3 8h-3l-1-1c2-2 0-5 1-7z" class="D"></path><path d="M463 176s1 1 2 1c0 0 1-1 1 0 1 0 3 1 4 2l-4-2v3l-6 3c1-2 2-3 4-4 0-1-1-2-1-3z" class="N"></path><path d="M289 384c1 2 2 2 4 3h2l-2 2h-3 0v-1c-1-1-1-2-1-4z" class="T"></path><path d="M479 182l1 2c-2 1-2 3-2 5-2-2-2-2-3-4h1 1v-2-1h2z" class="R"></path><path d="M294 421l-1-3h5l1 3c-2 1-3 0-5 0z" class="J"></path><path d="M435 97c1-1 2-1 3-1 2 1 5 3 5 5h0v1-1c-3-2-5-3-8-4z" class="E"></path><path d="M450 242h1v1h1 1 0 1 1l1 1h-1 0c-2 0-4 2-6 3-1-1-1-1-2-1v-1c1 0 3-1 4-1-1-1-1-1-1-2z" class="Q"></path><path d="M452 105c1 1 1 2 2 3v2h0c-2 1-4 1-6 1 2-2 3-4 4-6z" class="B"></path><path d="M359 454h1s0 1-1 1v6c1 2 2 3 3 4h0c-1 0-2-1-3-2-2-2-2-4-2-6s1-2 2-3z" class="K"></path><path d="M210 106h1c2-2 5-3 7-3v2h0c-2 1-4 2-7 2l-1-1z" class="G"></path><path d="M345 475c1 0 1 1 2 1 1 1 1 3 2 4l2 2-2 1c-1 0-1-1-2-1-1-1-1-6-2-7z" class="K"></path><path d="M445 115h3s0-1 1-2h4c1 0 2 0 3-1-1 2-3 3-5 4h0c-2-1-4-1-6-1z" class="N"></path><path d="M310 448c1 4 3 8 3 13-2-3-4-7-4-10l1-3z" class="L"></path><path d="M455 190c0-1 0-1-1-1 0-1 0-1 1-2 0-1 1-1 2-1s2 1 3 2c0 0 0 1-1 1-1 1-3 1-4 1z" class="Z"></path><path d="M441 128h1v-2h1c0 2 1 4 1 7h1v4c-2-3-3-6-4-9z" class="I"></path><path d="M285 311l2-6c2 2 4 3 6 5h-2-3l-2 2-1-1z" class="D"></path><path d="M347 416c-1-1 0-4 0-6 0-4-1-8-1-12 1 2 2 5 2 8v12h0v-1l-1-1z" class="R"></path><path d="M260 262c1-1 1-3 1-4 1-2 2-6 4-7-2 5-3 11-3 16 0-2-1-3-2-4v-1z" class="W"></path><path d="M445 100c2 1 1 1 2 2s3 1 4 1c-1 2-3 3-4 5-1-1-2-3-2-5v-3z" class="E"></path><path d="M598 152c2-1 3-1 5-1s4 0 6-1l1 1-1 1v1c-4 1-7 0-11-1z" class="W"></path><path d="M454 110v-1c2-1 2-4 4-5l1 1h3c1 0 1 1 1 1l-9 4h0z" class="K"></path><path d="M195 117s-1 1-1 2l-10 4c1-1 2-4 4-5l1-1c1 1 1 1 3 1 1 0 2-1 3-1z" class="H"></path><path d="M327 403l1 1 1 1c-2 2-4 4-4 6v1h0l-1 1c-1-2-2-3-2-4s4-5 5-6z" class="D"></path><path d="M494 94l14-1v1h-1c0 1-2 2-3 2h-2c-3-1-6-2-8-2z" class="B"></path><path d="M417 316l1 1 1-1v1c0 1 0 2-1 3-1 2-2 7-2 9l-2 5-1-2 4-16z" class="J"></path><path d="M293 286h0c-2 2-3 2-5 2-4 0-9-4-12-7h0c2 1 4 3 6 4 3 1 8 1 10 1l1-1v1z" class="W"></path><path d="M445 95v-2c4-1 10 0 14 0l-2 2h-12z" class="D"></path><path d="M318 295c1 2 3 6 2 9-1 1-2 2-4 3v-1l2-2c1-1 1-2 1-3h-1c-1 1-2 2-3 2h-1c2-1 2-3 3-5 0-1 0-2 1-3z" class="K"></path><path d="M344 487v7l-1 1h0c-1 0-1 1-2 1l-2-2h-1 0l1-1c1-1 3-2 3-4l2-2z" class="P"></path><path d="M342 489c0 1 1 3 1 4s0 1-1 1h0-3-1 0l1-1c1-1 3-2 3-4z" class="C"></path><path d="M296 424h3c1 0 3 0 4 1h0 0l-3 5h0 3l-1 1h-2c-1 0-1 1-2 0h0c1-1 1-1 1-2h0l-3-5z" class="H"></path><path d="M243 254h0c4-2 6-7 10-9h0c-2 3-7 7-7 11l1 1 1 1-1 2h-1c-1-2-1-4-3-6z" class="W"></path><path d="M321 456h1c1 2-4 8-6 10 0 2-1 3-1 5h-1 0c0-6 4-10 7-15z" class="N"></path><path d="M443 139c2 3 4 7 7 9-1 1-1 1-1 2s0 1 1 2h-3v-1c-1-3-1-5-3-7v-1l-2-3 1-1z" class="C"></path><path d="M527 98c-3-2-5-4-9-5h15v2h-1-7l2 2v1z" class="B"></path><path d="M206 105c0 2 0 2 2 2-3 2-5 3-7 5 0 0-1 0-1-1v-1h0c-1 1-1 1-1 2 0 0 0 1-1 1v1c0-2 0-3 1-4l1-1h0 1c0-1 1-1 1-2h0 0c2 0 3-1 4-2z" class="T"></path><path d="M262 214c4-1 11-3 15-1l2 2c-1 0-2 0-3-1h-6c-1 0-2 1-3 1-2 0-3 0-5-1z" class="G"></path><path d="M265 277l1 1v-1 1-1l1-1c-1 3 1 8 1 11v2h0v3 2c-2-5-3-11-3-17z" class="I"></path><path d="M350 449c1 1 1 2 3 2h0c0 1-1 2-1 2 0 1 0 2 1 3v2h1l2 6c-2-1-3-3-4-4-1-2-1-4-2-7h0v-4z" class="C"></path><path d="M304 400c1-1 1-1 2 0h0c0 2-1 3 0 5v1l-1-1h0c-1 0-1 0-1-1l-1-1v1c-1 1-1 1 0 2h0l3 5h-1 0l-1-1c0-1-1-2-2-3v-1l-1-1c0-1 1 0 0-1v2 1h0l-1-1h0v-2c0-1 2-3 2-3l2-1z" class="H"></path><path d="M248 258h0c2-2 4-4 5-7-2 5-2 10-4 15l-3-6h1l1-2z" class="I"></path><path d="M353 451c1 0 3 0 4-1 2 0 4-2 6-1h0c-4 3-8 3-9 9h-1v-2c-1-1-1-2-1-3 0 0 1-1 1-2h0z" class="C"></path><path d="M439 166c1 0 1 1 2 1 2 2 5 3 8 4h3v2l-3 3h-1v-2c-1-2-5-3-7-4v-1c-1 0-1-1-2-1v-2z" class="I"></path><path d="M199 110c-1 1-1 2-1 4h-1c0 1-1 2-2 3-1 0-2 1-3 1-2 0-2 0-3-1l2-1c2-2 5-4 8-6z" class="K"></path><path d="M191 116h1c2 0 3-1 4-2h0 1c0 1-1 2-2 3-1 0-2 1-3 1-2 0-2 0-3-1l2-1z" class="I"></path><path d="M269 300v-1-2l1 1h0c2 5 4 10 7 15v1c-1 0-1 0-2-1-3-2-6-10-6-13h0z" class="E"></path><path d="M464 142h1 0c-1 1-1 2-1 4-1 2 0 4 2 5 2 3 6 3 10 4h0-5c-2 0-5-1-7-3s-3-5-3-8c1-1 1-1 3-2z" class="C"></path><path d="M309 451l-4-16c0-1-1-1 0-2s3-1 4 0h-3c1 1 2 1 3 1l-2 1h0v2c1 3 1 7 3 11l-1 3z" class="H"></path><path d="M296 259v-9c0-1 1-2 1-3l2-5c0 6 0 12-1 18-1 0-1-1-2-1z" class="O"></path><path d="M455 144c1 0 2 1 2 2v-2c2-3 4-3 6-4s3-1 5-1v1 1h-4c-2 1-4 1-4 2-1 2-1 4 0 5-1-1-2-1-3-1-3-2-4-4-6-6l2-1 1 2c0 1 1 2 1 2z" class="H"></path><path d="M450 152c-1-1-1-1-1-2s0-1 1-2c4 3 7 5 11 7h0v1h0l-1 1-4-2-6-3z" class="D"></path><path d="M447 246c-1 0-3 0-4-1l-3-3c0-2 1-2 2-4 0 2 1 3 2 4h1 0c2 0 3 1 5 0 0 1 0 1 1 2-1 0-3 1-4 1v1z" class="W"></path><path d="M221 110c0 4 0 7-1 11-2 4-5 8-9 10 0-1 1-2 2-3 2-3 5-6 6-9s2-6 2-9z" class="S"></path><path d="M460 93h6 11-1c1 1 1 1 2 1-3 1-7 1-11 1l-2 1c-1-1-2-1-4-1h-1l-1-1 1-1z" class="D"></path><path d="M504 107c-1-1 0-2 0-3s0-1 1-1l1 1c1 0 1 0 1 1s0 1 1 2h0c1 0 1 1 2 1 1 2 0 2 0 4v-1l-1 1v-1 1c0 1 1 1 1 2l-1 1c0-1 0-1-1-2 0-1-1-1-1-2v-1c0-1-2-2-3-3z" class="Q"></path><path d="M437 237c1 0 0 0 1 1-3 2-1 6-2 9 0 2-2 4-3 6v1h-1c0-1 1-4 1-5 1-2 1-5 1-7 1-2 1-3 3-5z" class="H"></path><path d="M381 439c0 3 0 5-1 7v-1c-1 1-2 3-3 4-1 4-3 7-4 10l-1-1c1-4 3-8 5-11 1-3 2-5 4-8z" class="S"></path><path d="M283 228c2-1 3-1 6-1 2 2 4 3 6 5l-1 1c0-1 0 0-1-1-2-2-4-2-7-2-1 1-2 1-3 1l-2 2v-1h0v-1l2-2v-1z" class="B"></path><path d="M283 228c2-1 3-1 6-1-2 1-3 2-4 2v1h0 1c-1 1-2 1-3 1l-2 2v-1h0v-1l2-2v-1z" class="D"></path><path d="M210 106l1 1c3 0 5-1 7-2l-1 2c-1 0-2 1-4 1s-4 1-6 2c-3 1-4 5-7 5l1-3c2-2 4-3 7-5l2-1z" class="B"></path><defs><linearGradient id="AC" x1="433.415" y1="263.925" x2="432.531" y2="269.581" xlink:href="#B"><stop offset="0" stop-color="#6a6868"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#AC)" d="M436 261l2 1c-2 1-3 3-3 5v1c-1 2-1 4 0 6v1l-1-1c0-1-1-2-1-2l-1-1v1c-1 1-2 1-2 2l-1-1c1-1 1-2 2-3s1 0 1-1v-2-1c1-2 2-3 4-5z"></path><path d="M316 306v1c-7 4-13 6-21 5l-4-1v-1h2 2c7 1 15-1 21-4z" class="P"></path><path d="M301 323c2-3 5-5 8-8 3-2 6-4 10-6 1-1 2-3 3-2l1 2-1 1h-1c-2 0-9 4-11 5l-8 8h-1 0z" class="B"></path><path d="M243 254v-1c1-2 2-4 4-6 3-3 8-5 12-7l-6 5c-4 2-6 7-10 9h0z" class="I"></path><path d="M342 521l1 15h1v1c1 3 1 9 1 12l-2-10c0-1 0-2-1-3s-1-5-2-7c1-2 1-3 1-5v-2 1c0-1 0-1 1-2zm-56-146h0c3 4 5 9 10 10 1 0 2-1 4 0-1 1-3 1-5 2h-2c-2-1-3-1-4-3-2-3-3-6-3-9zm153-207c1 0 1 1 2 1v1h-1v9 16l1 1h-1-1c-1-2 0-6 0-9v-19z" class="R"></path><path d="M165 115h0 3c2 1 2 1 2 3 1 0 1 0 1 1s0 1 1 2h0c0 1 1 2 1 3h-2 0-2 0-2l-1-1 2-1c0-2 0-2-1-4 0-1-1-2-2-3z" class="H"></path><path d="M477 119h0 1c0-1 1-2 1-4h1v1c-1 4 0 9 0 14 1 6-2 11-7 15l-3 2 3-3c2-2 4-4 5-7 3-5 1-10-1-16v-2z" class="E"></path><path d="M330 420c1 4 0 7-1 11l1 1c1 1 2 2 2 4l-4-4h-1l-2-1c0 1-1 0-2 1h-1-1c1-2 2-2 4-3h0 2c2-3 2-5 3-9z" class="H"></path><path d="M406 361l7-29 1 2-1 6c-1 4-2 7-3 10l-3 11v1l-1-1z" class="K"></path><path d="M338 381c1-6 2-11 2-17-1-3-1-6-2-8 4 5 4 13 3 19 0 1 0 2-1 2v1 1l-1 2h-1 0z" class="D"></path><path d="M271 242c3-6 6-10 12-14v1l-2 2v1h0v1l-2 2c-2 2-3 4-5 6l-3 3v-2z" class="O"></path><path d="M313 336l1-1h1c0 3 0 5 1 6 0 1-1 2-1 3l-2 2v-1-1l-1-1c0 1-1 3-2 4l-1-2 4-9z" class="C"></path><path d="M439 106l1-1c0-1 0-1-1-2-2-1-5-2-7-3 4 1 7 1 10 4l3 6c-1 1-2 3-3 5-1-2 0-4 0-5 0 1-1 1-1 2 0-1 0-2-1-3l-2-1 2-1-1-1z" class="G"></path><path d="M440 107l2-1v1c1 1 0 2 0 3s-1 1-1 2c0-1 0-2-1-3l-2-1 2-1z" class="W"></path><path d="M460 224c0-1 0-3-1-5 0 0-1-2-2-2l1-1c3 1 3 3 4 5 1 4 0 6-1 8-1 1-1 3-2 4s-5 3-6 3h-1c2-1 4-3 6-5 1-3 3-5 3-9l-1-1v3z" class="H"></path><path d="M406 361l1 1c-1 3-2 6-3 10l-3 13v1c0 1-1 2-1 4h-1-1c0-2 1-3 1-5l3-10 4-14z" class="I"></path><path d="M450 194h5c2 0 6 3 8 5v2c-1 1-2 3-3 3s-1 1-1 0c0 0 1 0 2-1s1-2 1-3-1-2-2-2c-1-1-3-2-5-2-5-1-10 1-13 4v1l-1-1s0-1 1-1c1-3 6-4 8-5z" class="D"></path><path d="M441 255h3v2c1 0 3 0 4 1v1 1h-3c-2-1-4-1-6 0l-1 2-2-1h0c-1-1-1 0-1-1l2-2c2-1 3-2 4-3z" class="O"></path><path d="M441 255h3v2c-4 0-6 1-8 4-1-1-1 0-1-1l2-2c2-1 3-2 4-3z" class="H"></path><path d="M293 285l7-12c2-3 4-7 5-11 0-3-1-6-1-9l1 1c1 3 1 6 1 8 0 6-5 11-8 16-1 2-2 4-4 6 0 1 0 1-1 2v-1z" class="L"></path><path d="M289 217h1c1 2 2 6 3 8h0v1h0 0c-1-1-2-1-3-1l-1-1-6 1h0c2-3 4-5 6-8z" class="H"></path><path d="M286 230c3 0 5 0 7 2 1 1 1 0 1 1l1-1c1 1 2 5 2 6 1 3 0 6-2 8 0 1 0 1-1 1v-1c0-1 1-3 2-4 0-3-1-5-2-6-3-4-7-4-11-5 1 0 2 0 3-1z" class="K"></path><path d="M176 98c3-1 6-2 9-2h4c2 0 3 1 5 0l1 1-1 1c2 2 3 3 3 5v1c-1-2-3-4-5-5h-3c-6-1-10-1-15 1h-1l3-2z" class="X"></path><path d="M298 418c3 1 7 1 9 2-1 2-2 4-4 5h0 0c-1-1-3-1-4-1h-3l-2-3c2 0 3 1 5 0l-1-3z" class="K"></path><path d="M507 105l3 1h1l1-1 3 3h0c0 1 2 2 2 3l-1 2h3c1 1 1 1 2 1h-1v1l1 1c-2-1-3-1-4-1l-2-1c0-1-1-1-2-2h0-2-1c0-2 1-2 0-4-1 0-1-1-2-1h0c-1-1-1-1-1-2z" class="V"></path><path d="M433 123h0c0-2 0-4-1-6 0-2-1-3 0-5v-1-2c1-2 5-2 7-3l1 1-2 1c-3 2-4 4-4 7 0 2 0 4 1 6v3l-2-1z" class="K"></path><path d="M299 432h0l1 1c1 1 1 3 1 4l5 15c1 5 3 9 4 13h0 0 0 0v2c-1-2-2-5-3-7-3-7-5-15-7-22 0-2-1-4-1-6z" class="Q"></path><path d="M226 212c4 3 7 4 11 5h-6c2 4 3 10 7 14-2-1-2-1-3-1-2-2-3-6-4-8-2-3-4-6-5-10z" class="S"></path><path d="M166 142c2 0 3 1 4 2l1 1 2 2c4 3 7 8 10 12h0l-1 1c1 1 1 1 1 2s1 1 1 2h0l-2-2s0-1-1-1c0-1 0-1-1-2l-2-2c0-1 1 0 0-1 0 0-1 0-1-1-1-1-1-2-2-3h-1c0-1 0-1-1-2s1 0 0-1c-3-2-5-5-7-7z" class="L"></path><path d="M498 106c2 0 3 0 5 1h1c1 1 3 2 3 3v1c-1-1-2-1-3-2-6-2-14-2-20 1l-2-1 6-3h10z" class="U"></path><path d="M285 374c1-2 1-3 1-4h0l1-1c0 1 1 2 1 3 1 4 2 8 7 10 1 1 4 1 5 1 1-1 2-1 3-1l1 1-4 2c-2-1-3 0-4 0-5-1-7-6-10-10h0l-1-1z" class="O"></path><path d="M477 93c8 0 15 1 22 4 1 0 3 1 4 2v1c-2 1-3 0-5-1-6-3-12-4-20-5-1 0-1 0-2-1h1z" class="M"></path><path d="M317 298v-2c0-2-1-7-3-8l-1 1h-3c-1 1-9 10-11 11h0v-1c1-2 3-2 4-4s3-3 4-4l2-2c1-2 3-4 4-5 1 1 2 2 3 4s2 5 2 7c-1 1-1 2-1 3z" class="S"></path><path d="M454 137c1-1 2-2 2-4v-1h2c1-1 1 0 1 0l4 2c1 0 2 0 3 1h1 1l-3-3v-1h1c2 0 3 1 4 1h-2-1-1c1 2 3 3 4 5h-1 0c-1-1-2-1-2-1h-1-2 0-6c-1 0-1 0-1 1s0 1-1 2h0v3h-2v-5zm-95 317c1-1 1-1 2-1h1 1c1 1 2 0 4 1-2 3-6 2-5 6 0 1 2 3 3 4h3l1 1c-2 1-4 1-6 0h-1 0c-1-1-2-2-3-4v-6c1 0 1-1 1-1h-1z" class="L"></path><defs><linearGradient id="AD" x1="528.929" y1="95.356" x2="528.377" y2="101.248" xlink:href="#B"><stop offset="0" stop-color="#9f9e9e"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#AD)" d="M533 93c9-1 17 0 26 0 3 0 8-1 11 0-1 1-1 1-2 1h0c-1-1-28-1-30 0h0-4c-1 0-1 0-1 1s0 1 1 1l-2 2c0 1-1 2-1 2 0 2-1 3-1 4-1-1-1-3-2-4l-1-1v-1-1l-2-2h7 1v-2z"></path><path d="M447 132c1 0 1 0 1 1v1c1 2 2 5 3 7 2 2 3 4 6 6 1 0 2 0 3 1 0 1 1 2 0 3-4-1-8-4-10-7v-2h0c-1-4-3-6-3-10z" class="K"></path><path d="M283 231c4 1 8 1 11 5 1 1 2 3 2 6-1 1-2 3-2 4-2 0-3-1-4-2l3-3v-3c0-2-1-3-3-4-3-2-8 0-11 1l2-2 2-2z" class="R"></path><path d="M480 184c1-1 1-2 1-3l-1-2h0 1 0v-1l1 1 1 1c-1 1-1 1-1 2 1 0 1 0 2 1h0c1 1 1 1 1 3-1 1-1 1-2 1l-1-1c-1 2 1 6-1 7v1c0 1 0 2-1 2v1h-1c-1 3-4 6-7 8v1h-1c-1 1-1 2-2 2h-3c6-2 11-8 14-13 2-4 1-8 0-11z" class="T"></path><path d="M456 112c2-1 3-1 5-2 3-1 5-3 8-4l1 1h2 1l-1 1-1-1c-2 0-6 4-8 5h0c-2 1-2 1-3 2h-1l-2 1h0c-1 0-3 2-4 3v1h-1 0-1 1v-1c0-1 0-1-1-2 2-1 4-2 5-4z" class="B"></path><path d="M460 157l1-1h0v-1c2 1 4 2 7 3 6 1 13 0 18-2l1 1-1 1h-1c-2 0-2 0-3 1-1 0-2 1-2 1-4 1-7 1-11 0-2-1-6-2-9-3z" class="M"></path><defs><linearGradient id="AE" x1="437.027" y1="131.428" x2="440.372" y2="116.978" xlink:href="#B"><stop offset="0" stop-color="#767676"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#AE)" d="M441 112c0-1 1-1 1-2 0 1-1 3 0 5l-1 1c-2 4-3 9-2 14 1 3 2 6 4 9l-1 1-2-3v-2c-1-1-1-1-1-2-1-2-2-4-2-5-1-4 1-11 2-14l2-2z"></path><path d="M198 97c-1 0-1 0-1-1s1-2 1-3h11 10c1 1 3 1 4 2l-2 2-3-1c-2-1-5-1-7-1h-11c0 1-1 1-1 2h-1zm65-4h13c2 0 5 0 7 1v1h0c-1 2-3 3-4 5h0l-1-1-1 1-1-2-1-1-1-2h-13l2-2z" class="G"></path><path d="M274 95c3 0 6 1 9 0h0c-1 2-3 3-4 5h0l-1-1-1 1-1-2-1-1-1-2z" class="E"></path><path d="M427 279l1 1c1-1 1-2 2-3l-7 31h-1-1l6-29z" class="K"></path><path d="M225 210c3 1 7 5 10 4h0c3 2 9 2 12 1 2-1 3-2 4-3 1 1 3 2 4 3l-4 1c-3 2-10 1-14 1-4-1-7-2-11-5 0 0 0-1-1-2z" class="C"></path><path d="M334 494l1-1 1 1h1v1l1-1h0v1-1h0c1 4 3 9 4 13 1 10 1 20 2 29h-1l-1-15c0-4-1-10-2-14-1-3-2-6-4-9 0-2-1-3-2-4z" class="O"></path><path d="M373 459c1-3 3-6 4-10 1-1 2-3 3-4v1c-1 4-2 11-6 13v1c1 1 2 1 2 2 2 0 3 0 4 1v1h-1v1h-2c-1 1-4 1-5 2h-2v-1h0 2s1 0 1-1v-1h1c-1 0-1-1-1-1 0-1-1-1-1-2 1 0 1 0 2-1l-1-1z" class="T"></path><path d="M373 465h1v-3h0c1 0 1 0 2 1s1 0 2 0c0 1 1 1 1 1v1h-2c-1 1-4 1-5 2h-2v-1h0 2s1 0 1-1z" class="L"></path><path d="M502 138c2-7 0-12-2-18 4 5 7 11 6 18-1 2-2 5-4 6v2h0l-2 1-1-2 2-4 1-3z" class="E"></path><path d="M502 138c2 2 1 4 0 6v2-4l-1-1 1-3z" class="Z"></path><path d="M501 141l1 1v4h0l-2 1-1-2 2-4zm-41 83v-3l1 1c0 4-2 6-3 9-2 2-4 4-6 5-3 2-6 3-7 6h0-1c-1-1-2-2-2-4 2-2 5-3 9-4 4-2 7-5 9-10z" class="C"></path><path d="M323 468v-1c-1-3-1-6 1-8 1-4 5-8 9-10 3 0 6-1 8-1-2 2-5 2-7 3-1 1-2 2-4 3-1 1-2 3-3 4v1l-2 4c0 2-1 4-1 6h-1v-1z" class="Z"></path><path d="M478 94c8 1 14 2 20 5v1h-1-2v-1c-2 0-4-1-6-1-7-1-14-2-22-1h0l-1-1 1-1c4 0 8 0 11-1z" class="Q"></path><path d="M381 439l3-8c1-3 1-6 3-8h1l1 1c-1 1-2 2-2 3-1 3-2 5-2 8l-1 3v-1c-1 0-1 1-1 2-2 5-2 10-3 14-1 3-3 6-4 9 0-1-1-1-2-2v-1c4-2 5-9 6-13 1-2 1-4 1-7z" class="N"></path><defs><linearGradient id="AF" x1="328.276" y1="353.345" x2="333.868" y2="352.689" xlink:href="#B"><stop offset="0" stop-color="#403c3d"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#AF)" d="M331 335c2 5 3 11 3 16 0 6-2 12-5 18v-4c3-8 3-15 2-24l-1-3v-1h1v-2z"></path><path d="M432 272v-1l1 1s1 1 1 2h-1c-1 0-1 1-1 1l-1 4h0c-2 7-4 14-5 21 0 2 0 7-1 9h0l-1 1c-1-1-2-1-3-1v-1h1 1l7-31 2-5z" class="L"></path><path d="M319 467l1-1c1 1 2 1 3 2v1h1c2-1 4-1 6-1 1 1 0 1 1 2l-3 1c-1 1-1 1-3 1h0 0-3c-3 1-3 4-3 6l-1 3h0c0-1-1-1-1-2v-2c0-3 0-8 2-10z" class="C"></path><path d="M319 467l1-1c1 1 2 1 3 2v1h3l1 1c-1 0-1 1-2 1-1 1-3 0-4-1-1 0-1-1-1-1-1-1-1-1-1-2z" class="D"></path><path d="M461 188c0-2-1-2 0-4h3 0c1-1 1-1 2-1v-1c2 0 2 0 4 1h0l1 1 1 1c1 0 1 1 2 2 1 2 1 3 1 5l-1 1v-2l-3-3c-1-1-1-1-2-1s-2 0-3 1l-3 2h-2v-2h0z" class="S"></path><path d="M316 341c2-2 3-5 5-7l1 1h3c2 3 3 7 3 10 1 2 0 4 1 6h-1c0-4-1-7-2-10v-1l-3-3-6 8c-1 1-3 2-4 4h0v-3l2-2c0-1 1-2 1-3z" class="B"></path><path d="M316 341c2-2 3-5 5-7l1 1c1 0 2 0 2 1v1c-1 0-2-1-2 0-1 0-1 1-1 2-1 0-3 2-3 3h-1c-1 0-1 2-2 2 0-1 1-2 1-3z" class="M"></path><path d="M296 259c1 0 1 1 2 1-2 6-5 11-11 13-5 3-13 5-19 3h0-2c-1-1 0-1 0-3h1c1 0 1 1 2 1 6 1 14 0 19-3s7-7 8-12z" class="G"></path><defs><linearGradient id="AG" x1="440.196" y1="121.366" x2="434.306" y2="113.267" xlink:href="#B"><stop offset="0" stop-color="#aba6ab"></stop><stop offset="1" stop-color="#d4d3d2"></stop></linearGradient></defs><path fill="url(#AG)" d="M438 108l2 1c1 1 1 2 1 3l-2 2c-1 3-3 10-2 14 0 1 1 3 2 5h-2c-1-3-2-6-2-9v-3c-1-2-1-4-1-6 0-3 1-5 4-7z"></path><path d="M438 108l2 1c1 1 1 2 1 3l-2 2c0-1 1-3 1-4l-1-1c-1 1-1 2-2 3l-2 9c-1-2-1-4-1-6 0-3 1-5 4-7z" class="R"></path><path d="M459 93h1l-1 1c-1 1-1 2-2 3l-1 2-1-1c-2 1-3 3-4 5-1 0-3 0-4-1s0-1-2-2h0v-2-3h12l2-2z" class="H"></path><defs><linearGradient id="AH" x1="331.646" y1="391.925" x2="335.134" y2="393.176" xlink:href="#B"><stop offset="0" stop-color="#414042"></stop><stop offset="1" stop-color="#646464"></stop></linearGradient></defs><path fill="url(#AH)" d="M338 381h0 1l1-2v-1-1c1 0 1-1 1-2 0 3 0 6-1 9l-1 4c-3 6-6 13-10 17l-1-1-1-1c5-7 9-14 11-22z"></path><path d="M329 405c4-4 7-11 10-17-2 6-4 10-7 15-2 3-5 6-6 10 2 2 3 5 4 7-1 4-1 6-3 9h-2 0c1-2 2-3 3-5 1-4-2-8-4-11l1-1h0v-1c0-2 2-4 4-6z" class="R"></path><defs><linearGradient id="AI" x1="327.814" y1="376.842" x2="308.062" y2="379.988" xlink:href="#B"><stop offset="0" stop-color="#323031"></stop><stop offset="1" stop-color="#4f4d4d"></stop></linearGradient></defs><path fill="url(#AI)" d="M327 369l1 1-1 2c-4 7-11 11-17 16l4 8-1 1c0-1-1-2-1-2-1-2-4-9-4-10 2-1 3-2 5-3 6-3 10-7 14-13z"></path><path d="M531 100c0 4 1 8 0 12h0c-1 2-2 2-3 3v1h-2v-1c-1 0-1 0-2 1h1 3c1 0 1 0 1-1 1 0 3 1 4 0 2 0 3 0 4 1l-6 5h-1v-1c1 0 2-1 2-2 1-1 0-1 0-1-2-1-4 0-6 0h-4c0-1-1-1-1-1l-1-1v-1h1c3 0 4 0 7-2h-1l1-2c1-1 1-2 2-3v-3c0-1 1-2 1-4z" class="N"></path><path d="M528 110c1-1 1-2 2-3 0 2 0 3-1 5v1l-1-1h-1l1-2z" class="S"></path><path d="M528 112l1 1c-2 1-4 2-6 2l-2 1-1-1v-1h1c3 0 4 0 7-2z" class="T"></path><path d="M314 303h1c1 0 2-1 3-2h1c0 1 0 2-1 3l-2 2c-6 3-14 5-21 4h2l1-1c0 1 1 1 2 1-1 0-1-1-2-1h-2v-1h1c0-1-1-1-1-1-1-1 0-3 0-4 0 1 1 2 1 3s1 1 1 1c4 1 11 0 15-3l1-1h0z" class="V"></path><path d="M443 271h0l1-1 3-8c1 1 1 2 0 3v1h0l-1 2-1 3v1l-1 2h0l-4 9c-1 2-2 4-3 7v1l-7 21-1 1c-1-4 2-8 3-12 3-8 5-15 8-23h0c1-1 1-1 1-2l2-5z" class="Q"></path><defs><linearGradient id="AJ" x1="430.455" y1="91.651" x2="431.248" y2="96.277" xlink:href="#B"><stop offset="0" stop-color="#777776"></stop><stop offset="1" stop-color="#8e8a8d"></stop></linearGradient></defs><path fill="url(#AJ)" d="M435 97c-3-2-6-1-9-2h-10-1l-2-2c3-1 7 0 10 0h14 7v4h-1v-1h0-5c-1 0-2 0-3 1z"></path><path d="M376 462c1-3 3-6 4-9 1-4 1-9 3-14 0-1 0-2 1-2v1l1-3v5h1c0 2-1 5-2 7-1 5-4 11-4 16-1-1-2-1-4-1z" class="L"></path><path d="M451 119h1 0 1v-1c1-1 3-3 4-3h0l2-1h1c1-1 1-1 3-2l-4 4h0c-1 1-1 1-2 1s-3 1-4 1v2c-2 1-5 0-7 0-1 1-2 3-2 5 0 3 2 6 1 8h-1c0-3-1-5-1-7v-1c0-2-1-3-2-4 1-2 2-5 4-6h0c2 0 4 0 6 1h0c1 1 1 1 1 2v1h-1z" class="G"></path><path d="M445 115c2 0 4 0 6 1h0c1 1 1 1 1 2v1h-1c-1 0-3-1-4-1-1-1-2-1-2-3h0z" class="F"></path><path d="M141 94c-2 1-4 1-6 1-4 0-8 0-12-2h31l-6 1c0 2-1 6-4 7h0c-2 0-3 0-4-1 0 0-1 0-1-1l1-1h0l1-3v-1z" class="O"></path><path d="M141 94c1 0 2 0 4 1 0 0 1 0 1 1 0 2-1 3-2 4s-3 0-4 0c0 0-1 0-1-1l1-1h0l1-3v-1z" class="T"></path><path d="M141 95c2 0 2 0 3 1 0 1 0 1-1 3h-2l-1-1 1-3z" class="G"></path><path d="M511 112h2 0c1 1 2 1 2 2l2 1c1 0 2 0 4 1 0 0 1 0 1 1h4c2 0 4-1 6 0 0 0 1 0 0 1 0 1-1 2-2 2v1c-1 0-3 1-4 0-3-1-6-3-8-4 0-1-1-1-2-1 0-1-1-1-1-1v1c1 1 2 2 2 4 0 1-1 1 0 2v4l-1 1v-6l-1-1v-2c-1-1-1-2-2-3h0c-1-1 0-1-1-1 0-1-1-1-1-2h0z" class="K"></path><path d="M296 286l12-20 2 7v3h-2c-1 0-2 1-3 2-1 2-4 3-6 5-1 1-2 2-2 3h-1z" class="I"></path><path d="M531 100s1-1 1-2l2-2c-1 0-1 0-1-1s0-1 1-1h4 0s1 1 1 2c0 2-2 3-4 4 0 1-1 1-2 2h0c1 1 1 1 3 1v1c-1 1-2 2-2 4 1 1 2 1 3 1 1-1 2-1 2-3l-1-1v-1h2 1 1l-1-1 3-1c1-1 2-1 2 0 0 0-1 0-1 1-1 0-2 1-3 1 0 1 0 3 1 4h-1 0v-1c0-1-1-1-1-2h0l-1 1v1 1c-1-1 0-1-1-1 0 1 0 2-1 2h0l-1 1c-2 0-2 0-3-1v-3-2h-1-1 0v1h0c1 1 1 4 0 5 0 1 0 2-1 3h0c0 1-1 2-3 2 1-1 2-1 3-3h0c1-4 0-8 0-12z" class="L"></path><path d="M538 94c2-1 29-1 30 0h0c-3 1-7 1-10 3-2-1-4-1-6-1-3-1-7-1-10-1-1 1-1 1-1 2s-1 2-2 3h0c-2 1-4 2-6 2h0c1-1 2-1 2-2 2-1 4-2 4-4 0-1-1-2-1-2z" class="D"></path><path d="M271 242v2l3-3c-1 2-1 3-2 5-2 5-3 10-4 16 0 2-1 7 0 9v1l-1-1-1 1s0 1 1 1h-1c0 2-1 2 0 3h2 0-1l-1 1v1-1 1l-1-1v-7c0-9 3-19 6-28z" class="P"></path><path d="M443 205h-1c-1 2-1 4-2 6l-1 15v6l2 2s1 0 2-1 1-1 1-3h1v3l-6 3h-1c-1-1-1-2-2-3h0v-1c0-3 1-7 1-10l1-13c1-2 1-4 2-6 1 1 2 1 3 2z" class="R"></path><path d="M285 241h1 0v1c-1 0-4 2-5 3v1h2l1-1c2 0 4 1 6 2h0l1 1c1 0 2 2 2 2l1 1h0c0 1 0 1-1 2 0-1-1-2-1-2-1-1-3-2-4-3h-5 0c-1 0-1 0-2 1h0c-1 1-2 2-2 3h0v1c-1 1 0 4 0 5 1 1 2 3 2 4v1l-3-4c-1-2-3-4-2-8 1-5 5-8 9-10z" class="N"></path><path d="M279 235c3-1 8-3 11-1 2 1 3 2 3 4v3l-3 3c-1-1-2-2-4-2v-1h0-1v-1c1-1 3-2 4-3v-1c-1-1-2-1-3-1-4 0-9 4-11 8h0c-1 1-1 1-1 2l-2 1c1-2 1-3 2-5 2-2 3-4 5-6z" class="L"></path><path d="M321 432h1 1c1-1 2 0 2-1l2 1h1l4 4c2 2 4 5 4 8h0c-1-3-2-5-4-7h-1c1 1 1 3 1 4 1 1 1 3 0 4l-1-4c0-2-2-3-4-4v-1c-3-1-8 0-11-1l-7-1c-1 0-2 0-3-1h3c4 0 8 0 12-1z" class="I"></path><path d="M330 468c5-3 9-6 12-11-1 3-1 6-2 10-1 1-2 3-2 4-1 2-1 3-2 4-1-1-2-1-3-1l-2 1-1-1v-2h-2v-1l3-1c-1-1 0-1-1-2z" class="E"></path><path d="M331 470c1 0 3-1 4-2 1 1-1 2 0 3s2 1 3 0c-1 2-1 3-2 4-1-1-2-1-3-1l-2 1-1-1v-2h-2v-1l3-1z" class="K"></path><path d="M458 104l1-2c5-4 14-3 20-3h11c-3 1-6 0-9 1-1 1-3 1-4 1-3 1-5 2-8 3-1 0-5 2-6 2 0 0 0-1-1-1h-3l-1-1z" class="S"></path><path d="M445 133c1-2-1-5-1-8 0-2 1-4 2-5 2 0 5 1 7 0v-2c1 0 3-1 4-1s1 0 2-1h0 3c-1 1-1 2-2 3-1 0-1 1-1 1 0 1-1 1-1 1h-1v-2c-1 0-1 0-2 1h0l-1 1v1c-1 1-5 0-6 0v1 1h0c-1 1-2 1-2 2v2h0c0 1 0 2 1 4 0 4 2 6 3 10h0v2c-2-2-4-5-5-7v-4z" class="E"></path><path d="M307 420l12-9c2 2 3 5 4 8l-4-4c-6 4-11 10-16 15h0-3 0l3-5c2-1 3-3 4-5z" class="G"></path><path d="M198 97h1c0-1 1-1 1-2h11c2 0 5 0 7 1l3 1c-3 3-6 3-10 5-2 1-4 2-5 3s-2 2-4 2h0c2-4 5-4 8-6 2-1 4-2 6-4-4 0-10 0-14 2h-2c-1 0-1 0-2-1v-1z" class="I"></path><path d="M390 411v-2l1-1c0-1 1-2 2-2h0c1 0 3 0 4 1h0 0v5l-1 1-1 3h0v1 1 1 1c-1 1-1 2-2 2-1 1-1 0-2 0s-1 0-2-2c1 0 1-1 2-2h-1c0-1-1-1-1-2s-1-2 0-4l1-1z" class="L"></path><path d="M392 418c0 1 1 2 2 2l-2 2-2-2 2-2zm-2-7c0-1 1-3 2-4h3c1 1 1 1 1 2-1 0-3 0-4 1s-2 3-2 4v2h0c-1-1-1-2-1-4l1-1z" class="B"></path><path d="M390 416h0v-2c0-1 1-3 2-4s3-1 4-1v2c-2 1-2 2-3 4v2c1-1 1-1 2-1-1 1-1 3-1 4-1 0-2-1-2-2v-1h-1l-1-1z" class="H"></path><defs><linearGradient id="AK" x1="313.074" y1="354.838" x2="301.867" y2="369.256" xlink:href="#B"><stop offset="0" stop-color="#33352f"></stop><stop offset="1" stop-color="#555258"></stop></linearGradient></defs><path fill="url(#AK)" d="M310 347c1-1 2-3 2-4l1 1v1 1 3h0l-3 6-3 13v7c1 3 1 4 1 7h-1 0c-3-10-2-20 0-29h0s1-1 2-1c0-1 1-4 1-5z"></path><path d="M310 355c0-3 1-7 3-10v1 3h0l-3 6z" class="O"></path><path d="M313 349c1-2 3-3 4-4l6-8 3 3c-1 0-3 0-4 1 0 0-1 1-1 2-1 1-3 2-3 4-2 2-2 5-3 8-2 5-5 10-6 14h-1l-1-1 3-13 3-6z" class="C"></path><defs><linearGradient id="AL" x1="309.396" y1="354.145" x2="311.786" y2="361.136" xlink:href="#B"><stop offset="0" stop-color="#9d9d9e"></stop><stop offset="1" stop-color="#bcbabb"></stop></linearGradient></defs><path fill="url(#AL)" d="M313 349l1 1c0 3-2 5-3 8s-2 7-3 11l-1-1 3-13 3-6z"></path><defs><linearGradient id="AM" x1="189.415" y1="98.167" x2="180.78" y2="92.927" xlink:href="#B"><stop offset="0" stop-color="#b5bab6"></stop><stop offset="1" stop-color="#cdc8ce"></stop></linearGradient></defs><path fill="url(#AM)" d="M163 93h7 18c2 0 4 2 6 3-2 1-3 0-5 0h-4c-3 0-6 1-9 2h0l1-1h1c-1-1-2-1-4-1h0-2c-2-1-4 0-5 0h0l-3 1v1l1 1v1c1 1 1 2 2 4 0 0 1 1 1 2l-1-1c-1-1-3-3-3-5l-1-3 1-2h0v-1l1-1h-2z"></path><path d="M163 93h7 18v1c-4 1-8 1-13 1-3 0-8-1-11 0v-1l1-1h-2z" class="G"></path><path d="M533 102c2 0 4-1 6-2h0c1-1 2-2 2-3s0-1 1-2c3 0 7 0 10 1 2 0 4 0 6 1-2 1-6 3-8 2 0 0 1 0 1-1-1 0-1 0-1 1-1 0-1-1-2 0h-1l-1 1h-2l-2 2-1 1 1 1h-1-1-2v1l1 1c0 2-1 2-2 3-1 0-2 0-3-1 0-2 1-3 2-4v-1c-2 0-2 0-3-1z" class="N"></path><path d="M334 480c1-1 2-1 2-2v1c0 1 0 1-1 1l1 1 1 1-1 1c2 2 2 2 4 2 1 1 3 0 4 2l-2 2c0 2-2 3-3 4l-1 1v1-1h0l-1 1v-1h-1l-1-1-1-1c-1-2-3-3-3-6v-1l-2-3c1 0 1-1 2-1s2-1 3-1z" class="O"></path><path d="M337 489c0-1 1-1 1-2l1 1 1 1c0 1 0 2-1 4l-1 1v1-1h0l-1 1v-1c0-2 0-3-1-5h1z" class="D"></path><path d="M337 489c0-1 1-1 1-2l1 1v2h-1l-1-1zm-8-7c1 0 1-1 2-1 0 0 1 0 1 1h3l1 1h-1v2c-1 1-1 1-2 1l-2-1-2-3z" class="B"></path><path d="M331 485l2 1c1 1 2 1 2 2l1 1c1 2 1 3 1 5h-1l-1-1-1-1c-1-2-3-3-3-6v-1z" class="L"></path><path d="M335 488l1 1c1 2 1 3 1 5h-1l-1-1-1-1v-4h1z" class="V"></path><path d="M346 439c0 2-1 6 0 8 0 5 1 9 0 13-1 6 1 12 3 18v2c-1-1-1-3-2-4-1 0-1-1-2-1v-10l-1-1-4 13c-1 1-3 3-3 5l-1-1-1-1c1 0 1 0 1-1v-1h1c3-7 4-13 6-21 1-3 2-6 2-10 1-2 0-5 1-8z" class="P"></path><path d="M344 464v-1c0-3 0-9 2-12v1 8c-1 6 1 12 3 18v2c-1-1-1-3-2-4-1 0-1-1-2-1v-10l-1-1z" class="E"></path><path d="M239 100l-11-7h6 12v2c2 1 2 1 3 2-1 2-2 3-3 4h-4 5l-1 1c-2 1-5-1-7-2z" class="D"></path><path d="M242 101c-4-1-8-4-11-6h0 3c3 0 6 0 9 1 1 1 1 1 2 1s1-1 1-2c2 1 2 1 3 2-1 2-2 3-3 4h-4z" class="N"></path><path d="M450 160c1-1 1-2 2-2h1c2 1 4 0 5 1l-1 2c-1 1-1 3-1 5 3 2 6 2 10 3l6 3-1 1v1h0c-2-1-4-2-6-2-4-1-8-1-11-3h-1-1l-1-3c-1-1-1-5-1-6z" class="Y"></path><path d="M453 158c2 1 4 0 5 1l-1 2c-1 1-1 3-1 5 3 2 6 2 10 3l6 3-1 1c-5-4-12-3-16-7-2-2-2-5-2-8z" class="C"></path><path d="M280 290h0c1 0 1 0 2-1h2c2 1 4 1 7 1l-6 18c-1 2-2 5-3 8h-1c1-3 2-6 2-9 1-4 0-10-2-14l-6-4 5 1z" class="N"></path><path d="M280 290h0c1 0 1 0 2-1h2-2v1h0l2 2c0 2 1 2 1 4l-1 1c-1-1-1-3-2-4 0-1-1-2-2-3z" class="L"></path><path d="M329 351c0 7-4 16-9 22l-2 2c-3 2-6 5-10 7h0c0-3 0-4-1-7v-7l1 1h1v3l6 1c5-3 9-7 11-12 1-3 1-6 2-10h0 1z" class="H"></path><path d="M308 382c0-1 0-1 1-2v-4c-1 0 0-1 0-2h5c2 0 3 1 4 1h0c-3 2-6 5-10 7z" class="S"></path><defs><linearGradient id="AN" x1="482.741" y1="99.158" x2="477.752" y2="112.03" xlink:href="#B"><stop offset="0" stop-color="#1d1b1c"></stop><stop offset="1" stop-color="#3c3c3c"></stop></linearGradient></defs><path fill="url(#AN)" d="M469 106c9-4 19-4 29-4 1 0 2 0 3 1l1 1-1 1h-3v1h-10-1v-1h0c-3 0-7 1-9 3-3 0-5 1-7 3l-1 1v-2l2-2 1-1h-1-2l-1-1z"></path><path d="M498 102c1 0 2 0 3 1v1h-5l-3-1h0c1 0 3 0 4-1h1z" class="U"></path><path d="M501 103l1 1-1 1h-3v1h-10-1v-1h0c3-1 6-1 9-1h5v-1z" class="J"></path><defs><linearGradient id="AO" x1="473.723" y1="185.601" x2="475.491" y2="203.269" xlink:href="#B"><stop offset="0" stop-color="#302d2f"></stop><stop offset="1" stop-color="#565655"></stop></linearGradient></defs><path fill="url(#AO)" d="M478 189c0-2 0-4 2-5h0c1 3 2 7 0 11-3 5-8 11-14 13-2 0-4 1-6 1-7 1-12-1-17-4-1-1-2-1-3-2h1c2 1 5 3 8 4 5 2 12 1 17-2 7-3 10-9 12-16z"></path><path d="M433 123l2 1c0 3 1 6 2 9h2c0 1 0 1 1 2v2h-1c0 3 1 6 0 9 0 5 0 11 1 16h0c0 2 1 3 2 4 3 1 5 2 8 2 0-3-1-5-1-8h1c0 1 0 5 1 6l1 3h1v1c-2 1-3 1-4 1-3-1-6-2-8-4-1 0-1-1-2-1h0v-4l-6-39z" class="I"></path><path d="M439 162c1 2 2 4 3 5 3 1 6 2 8 2 1-1 1-2 1-3l1 3h1v1c-2 1-3 1-4 1-3-1-6-2-8-4-1 0-1-1-2-1h0v-4z" class="Q"></path><path d="M247 234h0l1-1c0-2 2-3 2-4 3-5 4-8 10-9-5 4-7 9-10 14 1 0 3 0 3 1-1 0-3 0-4 1-1 0-2 1-3 2l-5 6-5-12-1-2c1 0 1 0 3 1s4 0 6 0l-2 2v1h5z" class="H"></path><path d="M235 230c1 0 1 0 3 1s4 0 6 0l-2 2v1h5c-1 0-1 1-1 1-2 0-5 0-8-2 0 0-1 0-2-1h0l-1-2z" class="E"></path><defs><linearGradient id="AP" x1="617.569" y1="131.349" x2="627.514" y2="131.13" xlink:href="#B"><stop offset="0" stop-color="#86858a"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#AP)" d="M622 114h1c2 0 4 4 5 5 1 5 1 10 0 14v1c-1 2-1 4-3 5-3 4-6 8-11 10-2 1-3 1-4 2l-1-1c6-2 11-6 13-11 4-8 3-17 0-25z"></path><defs><linearGradient id="AQ" x1="435.739" y1="251.024" x2="437.587" y2="259.045" xlink:href="#B"><stop offset="0" stop-color="#817e80"></stop><stop offset="1" stop-color="#9c9c9b"></stop></linearGradient></defs><path fill="url(#AQ)" d="M449 247c2-1 4-3 6-3h0c-1 2-2 2-4 4l-3 3c-1 1-2 3-4 4h-3c-1 1-2 2-4 3l-2 2c0 1 0 0 1 1h0c-2 2-3 3-4 5v1 2c0 1 0 0-1 1 0-2 0-4 1-6h0c1-6 6-11 6-17 0-1 0 0 1-1v1l5 1c2 0 3 0 5-1z"></path><path d="M437 258l1-3c1-1 2-4 4-4h0v1 1l-1 2c-1 1-2 2-4 3z" class="K"></path><path d="M439 247l5 1h2v1l-1 1c-1 0-2 0-3-1h0-2c-1 1-1 1-1 0v-2h0z" class="O"></path><path d="M442 253c1-1 3-3 5-3l4-2-3 3c-1 1-2 3-4 4h-3l1-2z" class="W"></path><path d="M316 332c0 2 1 2 2 3l1-3h0c1-1 2-2 2-3v-4l1-1 1 2-1 1c-1 2 1 5-1 7s-3 5-5 7c-1-1-1-3-1-6h-1l-1 1v-1h-1c-2 1-3 2-4 4 0 0 0 1-1 1-1 2-2 5-2 8l-1-1h-1c0-2 1-3 1-5-1 0-2 2-2 2h-2c-1 0-1 0-1-1h0l1-1c3-7 7-8 14-10l1 1 1-1z" class="T"></path><path d="M299 343h1l1 1c0-1 1-2 2-3 1-2 4-5 7-6 1-1 1-1 2 0-2 1-3 2-4 4 0 0 0 1-1 1-1 2-2 5-2 8l-1-1h-1c0-2 1-3 1-5-1 0-2 2-2 2h-2c-1 0-1 0-1-1h0z" class="N"></path><path d="M253 220c3-3 6-5 9-6 2 1 3 1 5 1 1 0 2-1 3-1h6c1 1 2 1 3 1 1 1 0 3 0 4-1 0-1-1-1-1-5-2-11-1-15 0-1 1-2 1-3 2-6 1-7 4-10 9 0 1-2 2-2 4l-1 1h0-5v-1l2-2c5-2 5-8 9-11z" class="I"></path><path d="M253 220c3-3 6-5 9-6 2 1 3 1 5 1-2 0-4 1-5 1-3 1-6 4-9 4z" class="B"></path><path d="M459 94l1 1h1c2 0 3 0 4 1l2-1-1 1 1 1h0c8-1 15 0 22 1 2 0 4 1 6 1v1l-5-1h-11c-6 0-15-1-20 3l-1 2c-2 1-2 4-4 5v1-2c-1-1-1-2-2-3 1-2 2-4 4-6l1-2c1-1 1-2 2-3z" class="C"></path><path d="M456 99l1-2v2 1c-1 1-3 4-3 5v1 2c-1-1-1-2-2-3 1-2 2-4 4-6z" class="D"></path><path d="M459 94l1 1h1c2 0 3 0 4 1l2-1-1 1 1 1h0c-2 1-5 1-7 3 0 0-1 0-2 1v-1h-1v-1-2c1-1 1-2 2-3z" class="H"></path><path d="M461 95c2 0 3 0 4 1-2 0-3 1-5 2 0-1 0-2 1-3z" class="C"></path><path d="M459 94l1 1h1c-1 1-1 2-1 3-1 1-2 1-2 2h-1v-1-2c1-1 1-2 2-3z" class="O"></path><path d="M623 101c6 8 10 18 9 28-1 5-3 9-6 13v-1c0-1 0-1-1-2 2-1 2-3 3-5v-1c1-4 1-9 0-14-1-1-3-5-5-5h-1c-2-4-4-8-7-11v-1l1 2h1 0c0 1 1 1 1 2h1s0 1 1 1v-1h0v-1h-1v-1c-1 0-1-1-1-1h1v1h1 0l1 1c1 1 2 1 2 2s1 1 1 2l1-1c1 1 1 2 2 3 0 1 1 1 1 2s0 1 1 2v1c1 1 0 2 0 3 1 0 1 1 1 3v2h1c0 2 0 4-1 5v2l-1 1c1 0 1 0 1 1l-1 1v2l-1 1c-1 1 0 1 0 2 0-2 1-3 1-4 1-1 1-2 1-3l1-1v-1-1c0-2 1-7 0-8 0-1 0-2-1-2v-1-1h0c0-1 0-2-1-2v-1h0v-1c-1-1-1-1-1-2v-1l-1-1-1-2h0l-1-1c0-1 0-1-1-1v-1h0c-1-1-1-2-1-3h0z" class="N"></path><path d="M169 107c2 0 3 0 5-1h1 3l4 2c-1 0-3-1-5-1l-1 2c-1 2-1 3-2 4v3s0 1 1 2v3c1 1 1 1 1 2v1c1 1 2 2 3 4 2 3 5 6 9 6 1 1 1 1 2 1 1-1 0-2 1-1v2c-2 1-4 1-6 0-5-1-9-7-11-11-1-1-1 0-1-1s-1-2-1-3h0c-1-1-1-1-1-2s0-1-1-1c0-2 0-2-2-3l-1-1 1-1c1-3 3-3 3-5-1 0-1-1-2-1z" class="K"></path><path d="M169 107c2 0 3 0 5-1h1c-1 1-1 2-2 2 0 0-1 1-1 2-1 1-1 2-1 3h0c-1 2-1 4 0 5v1c0-1 0-1-1-1 0-2 0-2-2-3l-1-1 1-1c1-3 3-3 3-5-1 0-1-1-2-1z" class="R"></path><path d="M317 314c2-1 3-1 5-2 0 2 1 4 1 5 0 2-1 3-1 4 1 3 3 6 4 9l4 8 1 3v2 1-2c-1-1-1-3-2-4 0-1 0-2-1-3v-1c-1-1-2-3-2-4l-1-1h0v-1l-1-1s0-1-1-1v-1h0v1l-1-2-1 1v4c0 1-1 2-2 3h0l-1 3c-1-1-2-1-2-3l-1 1-1-1c-7 2-11 3-14 10l-1 1h0c-1-1-1-1-1-2 2-2 4-5 7-7 1-1 3-2 4-4 2-1 4-4 6-6h0c1 0 1 0 1-1 1-1 1 0 2 0 1-1 1-1 2-1h0v-2c-1-2-1-4-1-6h-2z" class="L"></path><path d="M314 332v-1-1h0c1-2 3-6 5-7 1 0 1 1 1 1-1 3-3 5-4 8l-1 1-1-1z" class="H"></path><path d="M320 324h1v-1c-1-1-1-3-1-5 0-1-1-2 0-4l1 1c1 2 1 4 1 6h0c1 3 3 6 4 9l4 8 1 3v2 1-2c-1-1-1-3-2-4 0-1 0-2-1-3v-1c-1-1-2-3-2-4l-1-1h0v-1l-1-1s0-1-1-1v-1h0v1l-1-2-1 1v4c0 1-1 2-2 3h0l-1 3c-1-1-2-1-2-3 1-3 3-5 4-8z" class="Q"></path><path d="M314 85l1 1c-1 1-2 1-3 1-2 1-4 2-5 3l-2 1c-2 1-4 1-6 2-4 0-8 5-11 7l-3 3h0l-1 1v1c-1 0-1 1-2 2 0 0-1 0-1 1h-1 0 0c-1 2-2 4-3 5l-4 8c1-8 6-16 11-22 4-6 11-8 18-10l12-4z" class="B"></path><path d="M284 99c4-6 11-8 18-10h0 0v1c-1 0-1 0-2 1s-2 1-4 2-4 2-6 4c-1 0-1 0-1-1l2-1c-2 0-3 1-4 2l-3 2h0z" class="M"></path><path d="M262 267c-1 1-1 2-2 2s-3 0-5-1c-1-1-2-3-2-4-1-6 2-12 5-16l-2 5c-2 4-2 9 0 14l4 1-1-3c-1-2-1-4-1-6 0-9 6-18 12-24 0-1 4-5 5-5 0 1-1 2-2 3s-2 3-2 4l-5 10c0 1-1 3-1 4-2 1-3 5-4 7 0 1 0 3-1 4v1c1 1 2 2 2 4z" class="E"></path><path d="M260 262c-1-2-1-4 0-6 0-7 5-15 11-19l-5 10c0 1-1 3-1 4-2 1-3 5-4 7 0 1 0 3-1 4z" class="S"></path><path d="M456 155l4 2 9 3c4 1 7 1 11 0 0 0 1-1 2-1 1-1 1-1 3-1 2 1 8 1 9 3s0 3 0 4l-1 1-3 5c0-1 1-3 1-4h0c-1-1-2-3-3-3h-4c-5 0-10 1-15 0h0c-3-1-6-3-9-4h1l1-1c-2 0-5-1-6-2v-2z" class="P"></path><path d="M482 162c-1 0-5 1-6 1-2 0-6 0-8-2h0c-1-1-1-1-2-1h0 3 0c4 1 7 1 11 0h1l4 1h0l-3 1z" class="I"></path><path d="M480 160s1-1 2-1c1-1 1-1 3-1 2 1 8 1 9 3s0 3 0 4l-1 1-3 5c0-1 1-3 1-4h0c0-1 1-3 0-3 0-1-2-2-3-2l-2 1c-1 0-3-1-4-1l3-1h0l-4-1h-1z" class="B"></path><path d="M481 160c3 0 7 0 10 2 1 0 1 1 2 2l1 1-1 1-3 5c0-1 1-3 1-4h0c0-1 1-3 0-3 0-1-2-2-3-2l-2 1c-1 0-3-1-4-1l3-1h0l-4-1z" class="C"></path><path d="M329 485c1 0 1 0 2 1 0 3 2 4 3 6l1 1-1 1c1 1 2 2 2 4 2 3 3 6 4 9 1 4 2 10 2 14-1 1-1 1-1 2v-1 2c0 2 0 3-1 5l-4-19c-1-1-2-1-2-1-1-3-1-6-1-9 0-1-1-2-2-4v-1-1c0-1-1-2-2-3s0-5 0-6h0z" class="E"></path><path d="M329 485c1 0 1 0 2 1 0 3 2 4 3 6l1 1-1 1c0-1-1-2-1-2-2-1-2-2-3-3v-1c-1-1-1-2-1-3z" class="P"></path><path d="M333 500c2 3 2 6 3 10-1-1-2-1-2-1-1-3-1-6-1-9z" class="T"></path><path d="M339 510c-1-4-3-8-4-12h1c2 3 3 6 4 9l-1 3z" class="J"></path><path d="M339 510l1-3c1 4 2 10 2 14-1 1-1 1-1 2v-1 2c-1-2-2-5-2-8 0-2 1-3 0-6z" class="K"></path><path d="M453 169h1c3 2 7 2 11 3 2 0 4 1 6 2h0v-1l1-1c3 3 5 6 7 10h-2v1 2h-1-1l-3-3c0-1-2-3-2-3-1-1-3-2-4-2 0-1-1 0-1 0-1 0-2-1-2-1l-2-1c-2 1-3 2-4 4-2 1-5 2-7 3l5-5c-1 0-3-4-3-4v-2h-3c1 0 2 0 4-1v-1z" class="W"></path><path d="M457 179c0-1-1-1-1-2l1-1c1-1 3-1 4-1-2 1-3 2-4 4z" class="N"></path><path d="M453 169h1c3 2 7 2 11 3 2 0 4 1 6 2 3 3 4 5 6 9l-6-6c-4-4-9-3-15-3-2-1-3-2-3-4v-1z" class="B"></path><path d="M200 115c3 0 4-4 7-5 2-1 4-2 6-2-1 1-3 2-5 3-3 1-5 4-6 6-2 4-4 8-4 12-1 1-1 2-1 3v4h0 1c-1 0-1 0-1 1h3-1l-2 2v3c0 4 0 7 1 11 0 1 0 1-1 2-1 0-1-1-2-2-2-3-3-7-6-9h-3l-1-1c1-2 3-1 5-2l3-2c1-1 1-3 1-3 0-3 0-6 1-9 1-4 2-9 5-12z" class="I"></path><path d="M195 148c1-1 1-2 1-3v-6-1l3-1-2 2v3c0 4 0 7 1 11 0 1 0 1-1 2-1 0-1-1-2-2-2-3-3-7-6-9 1-1 1-1 2-1 0-2 0-2 1-2h1c2 2 2 5 2 7z" class="K"></path><path d="M191 143c0-2 0-2 1-2h1c2 2 2 5 2 7 1 1 1 3 0 3-1-1-1-2-2-3-1-2-1-3-2-5h0z" class="N"></path><path d="M310 273l2 6h0l-2-2h-1c0 2 1 2 1 3-1 0-1-1-3-1-4 6-8 12-10 19h-1v5c0 1-1 3 0 4 0 0 1 0 1 1h-1c-1 0-2-1-3-1s-1-1-2-1c-1-1-2-2-2-4 0-3 3-9 5-12 0-1 1-3 2-4h1c0-1 1-2 2-3 2-2 5-3 6-5 1-1 2-2 3-2h2v-3z" class="R"></path><path d="M299 286h1 0l-1 1c0 2-1 3-2 4-2 3-3 6-3 8h-1l-1-1h0c1-2 2-5 3-7h0v-2h0l-1 1h0c0-1 1-3 2-4h1 2z" class="E"></path><path d="M299 286h1 0l-1 1v1c-1 0-1 1-2 1h-1l1-2s1 0 2-1z" class="C"></path><path d="M294 290h0l1-1h0v2h0c-1 2-2 5-3 7h0l1 1h1v1c0 2 0 5-1 7-1 0-1-1-2-1-1-1-2-2-2-4 0-3 3-9 5-12z" class="J"></path><path d="M292 298l1 1h1v1c-1 1-1 2-1 2h-1v-4z" class="I"></path><path d="M421 309c1 0 2 0 3 1 2 2 3 3 2 5 0 2 0 3-1 4v3c0 1-1 1-1 2v1h0l-2 5c-1 2-1 4-1 5-2 5-2 9-4 13 1-5 1-11 1-17v-5h0l-1 4-1-1c0-2 1-7 2-9 1-1 1-2 1-3v-1l-1 1-1-1c1-2 2-3 2-5l2-2z" class="L"></path><path d="M419 311l1 1h3c-2 1-3 2-4 4l-1 1-1-1c1-2 2-3 2-5z" class="I"></path><path d="M425 312c1 2 1 3 1 4s-1 1-1 2c-2-1-3 0-4-1-1 0 0-1 1-2v-1c1-1 2-1 3-2z" class="T"></path><path d="M416 329c0-2 1-7 2-9 1-1 1-2 1-3 0 5 0 10-1 14v-5h0l-1 4-1-1zm5-20c1 0 2 0 3 1 2 2 3 3 2 5 0 2 0 3-1 4v1c-1 1-2 1-3 1s-1 0-2-1c0-1 0-1 1-2h3 1c0-1 1-1 1-2s0-2-1-4h-2-3l-1-1 2-2z" class="E"></path><path d="M323 309c1 6 3 13 5 19 1 2 3 5 3 7v2h-1v1l-4-8c-1-3-3-6-4-9 0-1 1-2 1-4 0-1-1-3-1-5-2 1-3 1-5 2l-3 2c-3 1-6 3-9 6-5 4-8 8-11 14-1 2-1 5-2 7-1 1-1 4-1 5 0 2 0 5-1 7v1 1c-1-3-1-6-1-9 1-9 6-20 12-25h0 1l8-8c2-1 9-5 11-5h1l1-1z" class="C"></path><path d="M323 317c1 4 4 9 3 13-1-3-3-6-4-9 0-1 1-2 1-4z" class="K"></path><path d="M348 406c2 6 1 11 2 17l1 1v1c1 1 0 3 1 5 0 1-1 3 0 4h0v1h0c1-2 0-4 1-6v-3h0v21h6 1c-1 0-2 1-3 0h0-5l-1 1-1-1v1l-1 1c0-2-1-3-1-5h-1l-1 3c-1-2 0-6 0-8l1-18c-1-1 0-3 0-5l1 1v1h0v-12z" class="H"></path><path d="M347 416l1 1v1c0 3-1 7 0 10 0 2 1 4 2 6 0 3-1 10 1 12l1 1-1 1-1-1v1l-1 1c0-2-1-3-1-5h-1l-1 3c-1-2 0-6 0-8l1-18c-1-1 0-3 0-5z" class="K"></path><path d="M347 421c1 3 0 14 0 18 0 1 1 3 1 5h-1l-1 3c-1-2 0-6 0-8l1-18z" class="M"></path><path d="M325 472h0c2 0 2 0 3-1v1h2v2l1 1 2-1c1 0 2 0 3 1 0 1-1 1-1 2 1 0 1 0 1 1h1-1c0 1-1 1-2 2-1 0-2 1-3 1s-1 1-2 1l2 3v1c-1-1-1-1-2-1h0v-1h-1c-2-1-4-3-6-2-1 2-1 5-1 8l1 1v7c1 2 1 4 2 5 0 1-1 1-1 2l-1-1c-1-2-1-4-1-6l-4-16v-3c0 1 1 1 1 2h0l1-3c0-2 0-5 3-6h3 0z" class="N"></path><path d="M325 472h0c2 0 2 0 3-1v1h2v2l1 1c-1 0-1 0-1 1-1-1-2-3-3-4-1 1-1 3-1 4s1 2 1 3l-1 1-2-1c-1 0-2 1-2 2h-1c0-3 4-6 4-8v-1z" class="E"></path><path d="M327 479c0-1-1-2-1-3s0-3 1-4c1 1 2 3 3 4 0-1 0-1 1-1l2-1c1 0 2 0 3 1 0 1-1 1-1 2 1 0 1 0 1 1h1-1c0 1-1 1-2 2-1 0-2 1-3 1s-1 1-2 1h0l-1 1c0-1 0-3-1-3v-1z" class="Y"></path><path d="M333 474c1 0 2 0 3 1 0 1-1 1-1 2 1 0 1 0 1 1h1-1c0 1-1 1-2 2v-2c-1-1-1-1-2-1h0c-1 0-2-1-2-1 0-1 0-1 1-1l2-1z" class="T"></path><path d="M331 475l2-1 1 1c0 1 0 1-1 2h-1c-1 0-2-1-2-1 0-1 0-1 1-1z" class="I"></path><defs><linearGradient id="AR" x1="468.246" y1="123.68" x2="467.384" y2="141.059" xlink:href="#B"><stop offset="0" stop-color="#acabab"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#AR)" d="M477 121c2 6 4 11 1 16-1 3-3 5-5 7v-2c0-2 1-3 1-5-1-2-2-3-4-5-1 0-2-1-4-1h-1v1l3 3h-1-1c-1-1-2-1-3-1l-4-2s0-1-1 0h-2v1c0 2-1 3-2 4v-1c1-1 0-3 1-5 1 0 1-1 3-1 0 1 0 1 1 1 0-1-1-2-2-3h4v-1c2-1 3-1 5-1h0c1 0 1 0 2-1l1-1c0 1 0 1 1 2 1 0 2-1 3 0h2c0-1 1-1 2-2h0v-2-1z"></path><path d="M246 93h2 15l-2 2h13l1 2c-3 0-6 1-9 2-1 1-2 2-4 3h0c-1 1-2 2-4 3v-1h-1-3l-1-1c-1 0-1 0-2-1-1 1-2 0-3 0h-2l1-1h-5 4c1-1 2-2 3-4-1-1-1-1-3-2v-2z" class="K"></path><path d="M261 95h13l1 2c-3 0-6 1-9 2-1 1-2 2-4 3h0v-2c1-1 1-1 2-1l1-1c1 0 1 0 1-1-2-1-6 0-9-1h0l1-1h3z" class="S"></path><path d="M246 93h2 15l-2 2h-3l-7 1 7 3c1 0 2 0 3 1v1c-3 1-6 0-9 0l-4 1h-2l1-1h-5 4c1-1 2-2 3-4-1-1-1-1-3-2v-2z" class="U"></path><path d="M246 93h2v1h1c1 1 2 2 4 3v1c0 1-2 2-3 3h-3-5 4c1-1 2-2 3-4-1-1-1-1-3-2v-2z" class="W"></path><path d="M327 507l6 3c2 4 3 9 4 13h0c2 6 3 13 4 19 1 4 2 8 2 12h0l-15-40h0c0-2-1-3-1-4-1 0 0 0 0 0s1 0 1 1l1 1 1-1c-1-1-3-2-4-3l1-1z" class="H"></path><path d="M327 507l6 3c2 4 3 9 4 13v1c0-1 0-1-1-1-1-5-3-8-6-12-1-1-3-2-4-3l1-1z" class="E"></path><path d="M74 107c5-7 13-11 22-13 13-3 26 2 37 7 3 2 6 3 9 5-2 0-5-1-7-2-3-2-6-3-10-4-6-2-14-3-21-3-3 0-5 1-7 2-3 0-6 0-8 1-4 1-9 3-12 6-1 0-2 2-3 1z" class="U"></path><path d="M285 311l1 1 2-2h3v1h-1c-2 3-3 6-3 9-3 11-5 22-3 33 1 6 3 12 4 19 0-1-1-2-1-3l-1 1h0c0 1 0 2-1 4v-1c-1-2-1-4-2-6-4-16-3-33 0-48 1-2 1-5 2-8z" class="J"></path><path d="M74 107c1 1 2-1 3-1 3-3 8-5 12-6 2-1 5-1 8-1 2-1 4-2 7-2 7 0 15 1 21 3 4 1 7 2 10 4 2 1 5 2 7 2 4 2 8 5 12 8h-1l-8-4c-11-6-23-10-35-12-4 0-9 0-13 1-8 2-17 6-22 13l-1 1c-2 3-4 8-5 11 0 2-1 5-1 7l-1-4c0-7 2-15 7-20z" class="B"></path><path d="M290 398c1-2 1-2 1-3 2-5 10-7 15-9 1 1 1 2 1 3 1 2 1 5 3 7h-1v-1h0c-1-1-1-1-1-2v1h-1l-1 1h0c-3-1-4 0-6 2-2 1-2 3-2 6h0c0 2-1 4 0 5v1c0 1 0 1 1 2v2c-1 0-4 0-5-1-2-1-3-5-4-7v-7z" class="W"></path><defs><linearGradient id="AS" x1="304.44" y1="386.12" x2="290.27" y2="398.113" xlink:href="#B"><stop offset="0" stop-color="#353434"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#AS)" d="M290 398c1-2 1-2 1-3 2-5 10-7 15-9 1 1 1 2 1 3v1h-1c-4 0-9 3-11 6s-3 6-4 9l-1-7z"></path><g class="C"><path d="M97 99c4-1 9-1 13-1 12 2 24 6 35 12l8 4h1c3 2 6 5 8 7s3 3 5 4l3 3c-4 1-14-8-18-11l-9-5c-10-6-21-10-33-11-5-1-10-1-15 0h0c-8 3-14 7-21 12l1-1c5-7 14-11 22-13z"></path><path d="M140 109c4 1 8 4 11 6s5 3 7 5h1c1 1 2 1 3 2v-1c2 2 3 3 5 4l-1 1c-2 0-4-2-5-3l-12-8-6-4h-1l-2-2z"></path></g><path d="M97 99c4-1 9-1 13-1 12 2 24 6 35 12l8 4h1c3 2 6 5 8 7v1c-1-1-2-1-3-2h-1c-2-2-4-3-7-5s-7-5-11-6c-14-7-27-11-43-10z" class="O"></path><path d="M322 498v-7l-1-1c0-3 0-6 1-8 2-1 4 1 6 2h1v1c0 1-1 5 0 6s2 2 2 3v1 1c1 2 2 3 2 4 0 3 0 6 1 9 0 0 0 1-1 1l-6-3-1 1c1 1 3 2 4 3l-1 1-1-1c0-1-1-1-1-1s-1 0 0 0c0 1 1 2 1 4h0l-5-9c0-1 1-1 1-2-1-1-1-3-2-5z" class="S"></path><path d="M322 498v-4c0-2 0-6 2-7l2 1v6 1c0 3 1 6-1 8h-1c-1-1-1-3-2-5z" class="T"></path><path d="M327 497l1-7c0 2 2 5 3 6 1 2 2 3 2 4 0 3 0 6 1 9 0 0 0 1-1 1l-6-3-1 1c1 1 3 2 4 3l-1 1-1-1c0-1-1-1-1-1s-1 0 0 0c0 1 1 2 1 4h0l-5-9c0-1 1-1 1-2h1c2-2 1-5 1-8l1-1v3z" class="Q"></path><path d="M326 495l1-1v3c1 1 1 4 1 5s-3 3-2 4c0 1 0 1 1 1l-1 1c1 1 3 2 4 3l-1 1-1-1c0-1-1-1-1-1s-1 0 0 0c0 1 1 2 1 4h0l-5-9c0-1 1-1 1-2h1c2-2 1-5 1-8z" class="W"></path><path d="M292 343c1-2 1-5 2-7 3-6 6-10 11-14 3-3 6-5 9-6h0c-1 1-1 1-2 1l-2 2-2 1v1h0c-2 1-5 4-5 5-1 1-2 2-2 3-1 0-1 1-1 1l-1 1c0 1-1 1-1 2-1 0-1 0-2 1v2l-1 3c-1 0-1 1-1 1v3l-1 2c0 1 0 6 1 8 0 1-1 4 0 5v3s0 1 1 1v2 1c1 1 1 1 1 2s1 1 1 1c1 2 2 4 4 5h0c-1-1-1-1-1-2-1-1 0-1 0-2 0-3-1-5 0-7 0-3 0-6 1-9 0-3-1-6 1-8h0 0c-1 0-1 0-2-1h2s1-2 2-2c0 2-1 3-1 5h1l1 1c-2 8-2 16-2 25v4c-5-2-8-6-10-10-2-3-4-7-3-10v-1-1c1-2 1-5 1-7 0-1 0-4 1-5z" class="Q"></path><path d="M290 357v-1-1c1-2 1-5 1-7 0-1 0-4 1-5v1 2h0c0 4-1 9 0 13v2l1 1 1 4 1 2c0 1 1 2 2 3l5 4c0-4-1-8 0-12 0-6 0-11 1-16h1l1 1c-2 8-2 16-2 25v4c-5-2-8-6-10-10-2-3-4-7-3-10z" class="K"></path><path d="M449 160c0 3 1 5 1 8-3 0-5-1-8-2-1-1-2-2-2-4h0c-1-5-1-11-1-16 1-3 0-6 0-9h1l2 3 2 3v1c2 2 2 4 3 7v1h3l6 3v2c1 1 4 2 6 2l-1 1h-1l-2-1c-1-1-3 0-5-1h-1c-1 0-1 1-2 2h-1z" class="T"></path><path d="M448 157c-1 0-2-1-3-2v-2h-1-1v-3c-1-2 0-5-1-7v-1l1 1h1v1c2 2 2 4 3 7v1h3l6 3v2c1 1 4 2 6 2l-1 1h-1l-2-1c-1-1-3 0-5-1h-1c-1 0-1 1-2 2h-1v-1c-1-1-1-1-1-2z" class="Y"></path><path d="M448 157s1-1 2-1c2-1 4 0 6 1 1 1 4 2 6 2l-1 1h-1l-2-1c-1-1-3 0-5-1h-1c-1 0-1 1-2 2h-1v-1c-1-1-1-1-1-2z" class="D"></path><path d="M291 311l4 1h-5c-1 2-2 4-2 6-1 2-1 5-1 6-1 4-1 9-1 13 0 2 1 4 1 7 0 4 0 9 1 13 0 3 1 6 3 9 1 4 4 8 8 10l1 1h1l1 1h1s1 0 1 1h0l1 1v2l-1 1-1-1c-1 0-2 0-3 1-1 0-4 0-5-1-5-2-6-6-7-10-1-7-3-13-4-19-2-11 0-22 3-33 0-3 1-6 3-9h1z" class="S"></path><defs><linearGradient id="AT" x1="345.016" y1="588.288" x2="355.982" y2="598.263" xlink:href="#B"><stop offset="0" stop-color="#747273"></stop><stop offset="1" stop-color="#a6a6a5"></stop></linearGradient></defs><path fill="url(#AT)" d="M360 538h1c0 2-1 4-1 5-1 3-3 8-3 11h1v1c0 3-5 20-4 21l2-1c-1 3-3 9-1 12-2 4-3 9-4 13-1 2-1 6-3 7h0v-2c-1-2-1-5-1-7v-14c1-6 3-12 5-18l8-28z"></path><path d="M377 84c1 0 5 0 6 1 12 3 25 7 34 16h0c2 2 4 5 5 7 1 1 1 2 2 4l-1-1c0 1-1 1-2 1 0-1 0-1-1-1h0c-4-2-18-14-22-14 0 0-1 0-1-1-2-1-4-2-5-4-5-3-10-5-15-8z" class="K"></path><path d="M383 85c12 3 25 7 34 16h-1l-17-8c-2-1-4-2-6-2l-6-3c-1-1-3-2-4-3h0z" class="Z"></path><path d="M511 105c-1-1-2-1-2-2l-2-1-2-1v-1l1-1c1-2 3-3 5-4h0 1c2 0 4-1 6 0h0c1 0 1 0 2 1h0c1 0 1 0 2 1h0c1 1 2 1 3 2h0c2 1 2 3 3 4v2 4 1l-1 2h1c-3 2-4 2-7 2-1 0-1 0-2-1h-3l1-2c0-1-2-2-2-3h0l-3-3h-1z" class="W"></path><path d="M516 108c0-2 0-2 1-3v-1c0-1 1-1 2-2h1c0 1 0 1 1 2h0c-1 1-1 2-1 4 0 1 1 2 2 2h2 0l-4 1c0-1-1-1-1-2s0-1-1-2l-2 1z" class="S"></path><path d="M516 108l2-1c1 1 1 1 1 2s1 1 1 2l4-1v1 1c1 1 2 0 3 0h0 1c-3 2-4 2-7 2-1 0-1 0-2-1h-3l1-2 1 1h1v-1h0c-2 0-2-2-3-3z" class="H"></path><path d="M522 103v-1h4l1 1h0 1v2 4 1l-1 2h0c-1 0-2 1-3 0v-1-1h0-2c-1 0-2-1-2-2 0-2 0-3 1-4l1-1z" class="V"></path><path d="M522 103v-1h4l1 1h0 1v2h-1 0-3l-1-2h-1z" class="H"></path><path d="M511 105v-1c-1-1-3-2-4-4h0c1-1 2-2 4-2l1-1h1 1c1 0 3-1 4 0h1l1 1h2l-2 2c-1 0-4 2-4 3v1 1c-1 1-1 2-1 3h0l-3-3h-1z" class="Q"></path><path d="M154 93h6 2 1 2l-1 1v1h0l-1 2 1 3c0 2 2 4 3 5l1 1 1 1c1 0 1 1 2 1 0 2-2 2-3 5l-1 1 1 1h-3 0-1v1c-1 1-2 1-3 1l-2-1v-2s0-1 1-1l2 2-1-1-1-1c-1-2-4-4-6-6-3-2-2-3-4-5v-1c0-1 0-2-1-2v1l-1 1c-1 1-2 0-4 0h0 0c3-1 4-5 4-7l6-1z" class="V"></path><path d="M160 104h1c0 2 1 4 2 5l4 5 1 1h-3 0-1v1c-1 1-2 1-3 1l-2-1v-2h1c1 1 1 1 2 1 1-1 1 0 1-2 0-1-2-2-2-4h0l1 1v-1h-1c0-1 0-1-1-2v-1-2z" class="H"></path><path d="M163 109l4 5 1 1h-3 0-1 0c1-2 0-4-1-6z" class="N"></path><path d="M154 93h6 0c-3 2-8 1-11 1l2 2c0 2 0 4 1 6 1 1 2 3 2 5-3-2-2-3-4-5v-1c0-1 0-2-1-2v1l-1 1c-1 1-2 0-4 0h0 0c3-1 4-5 4-7l6-1z" class="S"></path><path d="M162 93h1 2l-1 1v1h0l-1 2 1 3c0 2 2 4 3 5l1 1 1 1c1 0 1 1 2 1 0 2-2 2-3 5l-1 1-4-5c-1-1-2-3-2-5h-1v-1c0-2-1-6 1-9l1-1z" class="D"></path><path d="M162 93h1 2l-1 1v1h0-3v-1l1-1z" class="B"></path><path d="M161 104c0 1 0 1 1 1h0c2 3 4 4 7 6 0-2-1-3-2-4-1 0-2-1-3-2s-1-6-1-8h0l1 3c0 2 2 4 3 5l1 1 1 1c1 0 1 1 2 1 0 2-2 2-3 5l-1 1-4-5c-1-1-2-3-2-5z" class="J"></path><path d="M254 277l-1-3h-1c-2 0-5-5-6-7 2 0 4 2 6 3h0c3 1 9 1 10 4 1 1 0 4 1 5l2 12 4 17c1 4 1 8 2 12l3 11c1 5 3 10 3 15l-18-55h0l-5-14z" class="H"></path><path d="M262 274v1c-3-1-6-1-9-2-1-1-1-1-1-2v-1c3 1 9 1 10 4z" class="D"></path><path d="M254 277l1-2h2c1 1 3 2 3 3 1 1 6 23 6 24h-1l-4-10c-1 0-1 0-2-1h0l-5-14z" class="Q"></path><path d="M254 277l1-2h2l-2 1c0 1 1 2 2 3 1 3 2 7 3 10 0 0 1 2 1 3-1 0-1 0-2-1h0l-5-14z" class="S"></path><path d="M299 93c2-1 4-1 6-2l-8 7c-4 2-7 5-9 8-6 8-10 15-12 24-4 13-2 27 2 40 1 2 1 5 2 7 0 1 0 2 1 3v2 1 1c-1-2-2-6-3-8-3-10-6-20-7-30 0-6-1-12 0-18 0-2 1-5 2-7l4-8c1-1 2-3 3-5h0 0 1c0-1 1-1 1-1 1-1 1-2 2-2v-1l1-1h0l3-3c3-2 7-7 11-7z" class="Z"></path><path d="M271 128c0 2 0 3 1 5l2-10c1-5 7-15 12-18-6 8-12 17-13 27-1 4 0 9-1 14h-1c0-6-1-12 0-18z" class="G"></path><path d="M273 121l4-8c1-1 2-3 3-5h0 0 1c0-1 1-1 1-1 1-1 1-2 2-2v-1l1-1h0l3-3c3-2 7-7 11-7-5 4-9 7-13 12-5 3-11 13-12 18l-2 10c-1-2-1-3-1-5s1-5 2-7z" class="D"></path><path d="M74 113c7-5 13-9 21-12h0c5-1 10-1 15 0 12 1 23 5 33 11l9 5c4 3 14 12 18 11l3 5 6 6-1 1v-1c-3-1-4-3-6-5l-11-8c-7-5-13-10-21-14-5-3-12-5-17-6-11-4-24-3-34 1-7 3-14 9-17 16-1 3-2 7-2 11 0 2 1 3 2 5 0 1 1 2 1 2 0 1-1 1-1 1-2-4-4-7-4-11 0-2 1-5 1-7 1-3 3-8 5-11z" class="I"></path><path d="M74 113c7-5 13-9 21-12h0c5-1 10-1 15 0 12 1 23 5 33 11l9 5c4 3 14 12 18 11l3 5h0-1c-2-1-4-3-6-4l-14-11c-10-7-20-12-32-15-9-2-17-3-26 0-8 3-17 7-21 14-2 5-5 12-3 17 0 2 1 3 2 5 0 1 1 2 1 2 0 1-1 1-1 1-2-4-4-7-4-11 0-2 1-5 1-7 1-3 3-8 5-11z" class="P"></path><path d="M309 369c1-4 4-9 6-14 1-3 1-6 3-8 0-2 2-3 3-4 0-1 1-2 1-2 1-1 3-1 4-1v1c1 3 2 6 2 10h0c-1 4-1 7-2 10-2 5-6 9-11 12l-6-1v-3z" class="V"></path><defs><linearGradient id="AU" x1="406.428" y1="360.105" x2="411.967" y2="361.131" xlink:href="#B"><stop offset="0" stop-color="#c4c3c3"></stop><stop offset="1" stop-color="#f5f4f5"></stop></linearGradient></defs><path fill="url(#AU)" d="M417 330l1-4h0v5c0 6 0 12-1 17-2 7-3 13-5 19-1 2-1 4-2 6v1c0 1 0 1-1 2h0l-2 5c0 1 0 2-1 3l-1 2v2h0v2l-1 1v2c-1 2-2 3-3 5h-1v-2l1-1h0v-4s-1 0-2-1h1c0-2 1-3 1-4v-1l3-13c1-4 2-7 3-10v-1l3-11c1-3 2-6 3-10l1-6 2-5 1 1z"></path><path d="M417 330l1-4h0v5c0 6 0 12-1 17-2 7-3 13-5 19-1 2-1 4-2 6 1-8 4-16 5-24 1-6 1-13 2-19h0z" class="J"></path><path d="M413 340c0 9-3 19-5 27-1 3-1 5-2 7v3c0 1-2 3-2 5-1 1-1 2-2 4 0 1 0 2-1 2v-3l3-13c1-4 2-7 3-10v-1l3-11c1-3 2-6 3-10z" class="N"></path><path d="M218 105h0v1c2 5-1 12-3 16-1 2-2 4-3 5v1h1c-1 1-2 2-2 3-4 3-8 4-13 5h-1 0v-4c0-1 0-2 1-3 0-4 2-8 4-12 1-2 3-5 6-6 2-1 4-2 5-3 2 0 3-1 4-1l1-2z" class="Q"></path><path d="M218 105h0v1 4h0c-3 1-3 2-5 3h0l-1-2s-2 0-3 1c-3 3-6 5-8 8v1c-2 3-3 7-3 10-1 1 0 1-1 1 0-1 0-2 1-3 0-4 2-8 4-12 1-2 3-5 6-6 2-1 4-2 5-3 2 0 3-1 4-1l1-2z" class="H"></path><defs><linearGradient id="AV" x1="191.139" y1="150.754" x2="184.843" y2="158.002" xlink:href="#B"><stop offset="0" stop-color="#8d8b8d"></stop><stop offset="1" stop-color="#b9b8b8"></stop></linearGradient></defs><path fill="url(#AV)" d="M123 106c5 1 12 3 17 6 8 4 14 9 21 14l11 8c2 2 3 4 6 5v1l1-1c3 3 5 7 8 11 10 14 19 29 26 44v1l-1-1-2-4v-1l-1-1h0c0-1-1-1-1-2h0v-1h-1v-1c0-1-1-2-1-3h0c-1-1-2-3-3-4l-1-3-1-2c-1-1-1-1-2-1-1-1-1-2-2-2l-3-4-1 1h1v1 1l1 1h0v1l1 1h0v1-1h-1 0l-1-1-1-1-2-2 1-1c0-1-3-4-4-6h-1c-1-2-2-3-3-5h0l-1-1c-2-4-5-7-8-10-1-2-3-5-5-6s-3-3-4-4c-7-6-13-12-21-16-2-2-5-3-8-4l-8-4c-1 0-3-1-4-2h-1c0-1-1-1-1-2z"></path><path d="M314 316l3-2h2c0 2 0 4 1 6v2h0c-1 0-1 0-2 1-1 0-1-1-2 0 0 1 0 1-1 1h0c-2 2-4 5-6 6-1 2-3 3-4 4-3 2-5 5-7 7 0 1 0 1 1 2 0 1 0 1 1 1 1 1 1 1 2 1h0 0c-2 2-1 5-1 8-1 3-1 6-1 9-1 2 0 4 0 7 0 1-1 1 0 2 0 1 0 1 1 2h0c-2-1-3-3-4-5 0 0-1 0-1-1s0-1-1-2v-1-2c-1 0-1-1-1-1v-3c-1-1 0-4 0-5-1-2-1-7-1-8l1-2v-3s0-1 1-1l1-3v-2c1-1 1-1 2-1 0-1 1-1 1-2l1-1s0-1 1-1c0-1 1-2 2-3 0-1 3-4 5-5h0v-1l2-1 2-2c1 0 1 0 2-1h0z" class="V"></path><path d="M280 177c-1-2-1-5-2-7-4-13-6-27-2-40 2-9 6-16 12-24 2-3 5-6 9-8l-7 7c-4 5-7 11-10 17-1 2-2 5-3 7-2 10-2 21 0 31 1 6 3 12 5 18l8 25 13 37 13 36c0 1 1 1 1 2l2 4v2c1 1 1 1 1 2l1 3 2 7v1 1l3 7c0 1 1 2 1 3v2h1v1 1 1l1 1h0v2c1 1 1 0 1 1v2c1 1 1 1 1 2h1v2l2 5v2h0c1 1 1 1 1 2l1 5 1 1v1 1c1 0 1 1 1 2v1h0c1 2 1 3 2 5v1c0 1 0 1 1 1 0 1 0 2 1 4 1 3 2 5 2 9 1 2 2 3 1 5l-50-147-14-37v-1-1-2c-1-1-1-2-1-3z" class="I"></path><path d="M280 177c1 2 2 5 3 7l3 10 7 20c1 2 1 3 2 5v2l-14-37v-1-1-2c-1-1-1-2-1-3z" class="P"></path><path d="M220 121c1 1 2 3 1 4v2h-1c-3 4-5 7-7 11l-1-1c-1 0-3 1-4 2-3 4-3 10-4 14 0 4 0 7 1 10l1 1v-3c1 3 1 7 3 10l1-6c0 1 0 2 1 3-2 7-1 15 3 21l1 3v1c-3-4-4-9-6-13-1-1-3-5-4-7 0 0 0-1-1-1 0-1-1-3-1-3l-10-15c-2-3-6-6-7-10h3c3 2 4 6 6 9 1 1 1 2 2 2 1-1 1-1 1-2-1-4-1-7-1-11v-3l2-2h1-3c0-1 0-1 1-1 5-1 9-2 13-5 4-2 7-6 9-10z" class="V"></path><path d="M202 160l1-1v-1-7c0-2-2-7-1-8v-1-1h0c1-1 1-1 1-2l1 14c0 4 0 7 1 10v1h0c-1-1-2-3-3-4z" class="S"></path><path d="M209 180c-1-8-5-13-8-20h0 1c1 1 2 3 3 4h0v-1l1 1v-3c1 3 1 7 3 10l1-6c0 1 0 2 1 3-2 7-1 15 3 21l1 3v1c-3-4-4-9-6-13z" class="E"></path><defs><linearGradient id="AW" x1="213.73" y1="136.039" x2="205.226" y2="133.957" xlink:href="#B"><stop offset="0" stop-color="#9b9b9a"></stop><stop offset="1" stop-color="#bbb9bb"></stop></linearGradient></defs><path fill="url(#AW)" d="M220 121c1 1 2 3 1 4v2h-1c-3 4-5 7-7 11l-1-1c-1 0-3 1-4 2-3 4-3 10-4 14l-1-14h0l1-1 1-1c-2 0-3 1-5 1 0 1-1 1-1 3h0c0 1 0 3-1 4v8c-1-4-1-7-1-11v-3l2-2h1-3c0-1 0-1 1-1 5-1 9-2 13-5 4-2 7-6 9-10z"></path><path d="M220 121c1 1 2 3 1 4v2h-1l-2 2c-1 1-2 2-4 3-4 2-9 4-14 5h-3c0-1 0-1 1-1 5-1 9-2 13-5 4-2 7-6 9-10z" class="B"></path><path d="M516 127l1-1v-4c-1-1 0-1 0-2 0-2-1-3-2-4v-1s1 0 1 1c1 0 2 0 2 1 2 1 5 3 8 4 1 1 3 0 4 0h1c-4 4-8 9-11 13-2 2-4 5-6 7-4 7-9 13-13 20-2 3-4 7-6 11 0-2 0-2 1-4v-1l2-3s1-1 1-2c-1 1-2 0-3 0-1 1-2 3-2 4h-1l1-1c0-1 1-2 0-4s-7-2-9-3h1l1-1-1-1c6-3 9-6 13-11l1 2 2-1h0l6-4c1-2 2-4 4-6 0-1 1-1 2-3h0v-2h1v-1c1-1 0-2 1-3z" class="V"></path><path d="M508 142l-3 3c-3 4-6 7-10 9-1 1-3 2-5 3 2-3 5-4 7-6 1-1 2-3 3-4l2-1h0l6-4z" class="P"></path><path d="M486 156c6-3 9-6 13-11l1 2c-1 1-2 3-3 4-2 2-5 3-7 6-1 0-3 1-4 1l1-1-1-1z" class="G"></path><path d="M279 100h0c1-2 3-3 4-5 0 2 0 2-1 3-1 0 0-1-1 0l-1 2h0l-1 1v2h-1v1c-1 0-1 1-2 2 0 0-1 1-1 2s-1 1-1 1l-3 7s0 1-1 2v2h0v1 2c0 1 0 1-1 2v1 2c0 1 0 2-1 3v3 3 13 2h0v3h1c0 2 0 3-1 5h1v-3-1h0c0-1-1-2 0-3h0 0l1-1v-1c1 2 0 4 1 5v1 2l1 1v2c0 1 0 1 1 1 0 1-1 3 0 4v1c0 1 0 2 1 2v2 2c1 1 1 2 1 2 0 1 1 1 1 2l1 2h0s0 1 1 1v3c0 1 0 1 1 1v2h0c0 1 0 1 1 1 0 2 1 3 1 4v1c1 1 1 2 2 3h0v2h0l1 1v2 1 1l1 1v1 2c1 0 1 1 1 1v2c0 1 1 2 0 3-1 0-1-1-2-3 0-1-2-2-3-3h0 1s0 1 1 1v-1h0l1 2v-1h0l1 1v-1l-3-12c-2-5-4-10-5-15h-1v1h0l-1-1-1 1c-2 5-3 8-7 11h0l-1-1v-2h0l-2 2c0 1-2 1-3 1h-1l4-4c1-1 2-3 2-5v-2-1-9c0-4-2-9-5-12-1-1-2-1-3-2-3-2-6-2-10-1l-1-1c1-1 3-2 4-2 2-1 4-1 6 0h1c2 1 4 3 6 4v-1l2 2c1-4 0-8 0-12v-14h-1c-1 0-1 0-2 1h0l-1-1c2-1 4-3 5-5h1c1-3 1-5 1-8l4-10c1-4 4-7 6-10z" class="V"></path><path d="M267 128h1c0 4-1 8-1 11 0 7 0 14 1 21 0 1 0 2-1 3l-3-5v-1l2 2c1-4 0-8 0-12v-14h-1c-1 0-1 0-2 1h0l-1-1c2-1 4-3 5-5z" class="R"></path><path d="M248 157l-1-1c1-1 3-2 4-2 2-1 4-1 6 0h1c2 1 4 3 6 4l3 5c2 2 2 7 1 10v2h0c0 1 0 2-1 2 0 2 1 3 0 4h0v-2c1-2 1-4 0-6l-1-1c0-4-2-9-5-12-1-1-2-1-3-2-3-2-6-2-10-1z" class="Y"></path><path d="M269 171v-5l1-11c1 4 1 8 2 12s3 9 2 13h0v2c-2 5-3 8-7 11h0l-1-1v-2h0l-2 2c0 1-2 1-3 1h-1l4-4c1-1 2-3 2-5v-2-1l1 2h0c1-2 1-3 1-5 1-2 1-4 1-7z" class="I"></path><defs><linearGradient id="AX" x1="268.399" y1="176.178" x2="274.218" y2="180.361" xlink:href="#B"><stop offset="0" stop-color="#a7a1a0"></stop><stop offset="1" stop-color="#c1c1c3"></stop></linearGradient></defs><path fill="url(#AX)" d="M272 167c1 4 3 9 2 13h0v-1c-1 0-1 1-1 1 0 3-1 7-4 9v-1c0-2 1-3 2-5v-12c1-1 1-3 1-4z"></path><path d="M269 171c1 6 1 14-3 19h0l-2 2c0 1-2 1-3 1h-1l4-4c1-1 2-3 2-5v-2-1l1 2h0c1-2 1-3 1-5 1-2 1-4 1-7z" class="L"></path><path d="M221 105c0-1 1-2 1-3 1-2 2-3 4-4s3 0 4 1c2 0 2 0 3 1 2 1 3 1 5 1l1-1c2 1 5 3 7 2h2c1 0 2 1 3 0 1 1 1 1 2 1l1 1h3 1v1c-4 3-9 4-14 7-7 3-13 7-19 11l-4 4v-2c1-1 0-3-1-4 1-4 1-7 1-11v-5z" class="N"></path><path d="M236 104h3 0 1c1 1 1 1 2 1v1h0c-3 0-5 0-7-2h1z" class="R"></path><path d="M232 100l4 4h-1c-2 0-6-1-8-1-1 1-2 2-4 3h0l1-4c1 0 3-1 4-1s2 0 3-1h1z" class="J"></path><path d="M221 105c0-1 1-2 1-3 1-2 2-3 4-4s3 0 4 1l2 1h-1c-1 1-2 1-3 1s-3 1-4 1l-1 4c0 1 0 1-1 2h0l-1-3z" class="F"></path><path d="M223 106h0c0 4 0 10 1 13 0 1 0 3 1 4l-4 4v-2c1-1 0-3-1-4 1-4 1-7 1-11v-5l1 3h0c1-1 1-1 1-2z" class="E"></path><path d="M228 106h6c1 1 2 1 3 1h1c1 0 0 0 1 1 0 1 0 1-1 2l-2 3-3 3c-1 1-5 3-7 3 0-1-1-2-1-3-1-1-1-3-1-4l1-3c1-2 2-2 3-3z" class="V"></path><defs><linearGradient id="AY" x1="469.201" y1="110.561" x2="475.117" y2="125.637" xlink:href="#B"><stop offset="0" stop-color="#747474"></stop><stop offset="1" stop-color="#b8b7b7"></stop></linearGradient></defs><path fill="url(#AY)" d="M478 108c2-2 6-3 9-3h0v1h1l-6 3 2 1c-2 2-3 3-4 5h-1c0 2-1 3-1 4h-1 0v2 1 2h0c-1 1-2 1-2 2h-2c-1-1-2 0-3 0-1-1-1-1-1-2l-1 1c-1 1-1 1-2 1h0c-2 0-3 0-5 1v1h-4c1 1 2 2 2 3-1 0-1 0-1-1-2 0-2 1-3 1-1 2 0 4-1 5v1 5h2l-1 1h1l-1 1s-1-1-1-2l-1-2-2 1c-1-2-2-5-3-7v-1c0-1 0-1-1-1-1-2-1-3-1-4h0v-2c0-1 1-1 2-2h0v-1-1c1 0 5 1 6 0v-1l1-1h0c1-1 1-1 2-1v2h1s1 0 1-1c0 0 0-1 1-1 1-1 1-2 2-3h-3l4-4h0c2-1 6-5 8-5l1 1-2 2v2l1-1c2-2 4-3 7-3z"></path><path d="M470 112l1-1c2-2 4-3 7-3-1 1-3 2-4 4 0 0-1 1-1 2h0v-2-1c-1 0-1 0-1 1l-1 1h-2 1v-1z" class="C"></path><path d="M477 119h0c0-2-1-3 0-5s3-4 5-5l2 1c-2 2-3 3-4 5h-1c0 2-1 3-1 4h-1 0z" class="G"></path><defs><linearGradient id="AZ" x1="469.797" y1="106.92" x2="463.419" y2="115.176" xlink:href="#B"><stop offset="0" stop-color="#525152"></stop><stop offset="1" stop-color="#727271"></stop></linearGradient></defs><path fill="url(#AZ)" d="M463 112h0c2-1 6-5 8-5l1 1-2 2v2 1h-1 0c-1 0-2 0-2 1v1h-1c-1 1-1 1-1 2l-1 1h-1v-2c1-1 2-2 1-4v1l-2 3h-3l4-4z"></path><defs><linearGradient id="Aa" x1="451.853" y1="127.438" x2="455.76" y2="140.785" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#Aa)" d="M453 140l-2-4c-1-2-2-4-2-7-1-1-1-2 0-3h3c1 0 2 0 3 1 0 0 1 1 2 1h0c1 1 2 2 2 3-1 0-1 0-1-1-2 0-2 1-3 1-1 2 0 4-1 5v1 5h2l-1 1h1l-1 1s-1-1-1-2l-1-2z"></path><path d="M346 447l1-3h1c0 2 1 3 1 5h1v4h0c1 3 1 5 2 7 1 1 2 3 4 4 3 2 7 5 11 4h3v2l1 1s0 1-1 1v1c0 1 0 1-1 1v1l-1-1v-1c1 0 1-1 1-1v-1-2h-1v1l-2 6-2 5-1 4c-1 1-2 4-3 5l-2 1h0-2c-1 0-3-2-4-3-1 0-1-1-1-2-1 0-2-1-2-2v-1l2-1-2-2v-2c-2-6-4-12-3-18 1-4 0-8 0-13z" class="F"></path><path d="M349 478c1 0 3 2 3 3l1 1v1c-1 0-1 1-1 2l-1-3-2-2v-2z" class="B"></path><path d="M351 482l1 3h1 0 1l-1 1c0 2 1 3 3 3l1 1c1 0 1 0 1 1h-2c-1 0-3-2-4-3-1 0-1-1-1-2-1 0-2-1-2-2v-1l2-1z" class="J"></path><path d="M357 490c0-3-1-2-2-4 1 0 1-1 1-2s-2-3-2-4c-1-2-4-6-4-9 1-2 0-5-1-7h0c0-2 0-2 1-3 0 2 0 3 1 5v1l1 2v2 1c0 1 1 3 2 4 0 1 1 2 1 2l2 2c2 0 3 1 4 1 1 1 2 0 3 0l-1 4c-1 1-2 4-3 5l-2 1h0c0-1 0-1-1-1z" class="B"></path><path d="M357 480c2 0 3 1 4 1 1 1 2 0 3 0l-1 4c-1 1-2 4-3 5l-1-1c0-1 0-1-1-2 0 0-1-1-1-2h0 1 1l-1-1-1-4z" class="L"></path><path d="M361 481c1 1 2 0 3 0l-1 4c-1-1-2-1-3-1 0-1 1-2 1-3z" class="T"></path><path d="M350 453c1 3 1 5 2 7 1 1 2 3 4 4 3 2 7 5 11 4h3v2l1 1s0 1-1 1v1c0 1 0 1-1 1v1l-1-1v-1c1 0 1-1 1-1v-1-2h-1v1l-2 6-2 5c-1 0-2 1-3 0-1 0-2-1-4-1l-2-2s-1-1-1-2c-1-1-2-3-2-4v-1-2l-1-2v-1c-1-2-1-3-1-5v-1c-1-1 0-4-1-5 0-1 0-1 1-2z" class="D"></path><path d="M353 465h1c1 1 3 2 4 3 2 1 3 2 5 2-1 1-1 1-2 1-1 1-2 2-4 3v1c-1-2-2-3-3-5l-1-5z" class="J"></path><path d="M354 470c2-1 5 0 7 1h0c-1 1-2 2-4 3v1c-1-2-2-3-3-5z" class="K"></path><defs><linearGradient id="Ab" x1="347.903" y1="464.423" x2="356.985" y2="467.499" xlink:href="#B"><stop offset="0" stop-color="#151716"></stop><stop offset="1" stop-color="#403e3e"></stop></linearGradient></defs><path fill="url(#Ab)" d="M350 453c1 3 1 5 2 7 0 1 0 4 1 5l1 5c1 2 2 3 3 5v1 1h-1c-2-2-3-3-4-5v-1-2l-1-2v-1c-1-2-1-3-1-5v-1c-1-1 0-4-1-5 0-1 0-1 1-2z"></path><path d="M366 470l1-1 1 1-2 6-2 5c-1 0-2 1-3 0-1 0-2-1-4-1l-2-2s-1-1-1-2c-1-1-2-3-2-4 1 2 2 3 4 5h1v-1-1-1c2-1 3-2 4-3 1 0 1 0 2-1h3z" class="Q"></path><path d="M363 470h3c-3 3-5 6-9 9 0-1-1-1-2-1 0 0-1-1-1-2-1-1-2-3-2-4 1 2 2 3 4 5h1v-1-1-1c2-1 3-2 4-3 1 0 1 0 2-1z" class="P"></path><path d="M349 484c0 1 1 2 2 2 0 1 0 2 1 2 1 1 3 3 4 3h0c1 1 3 0 4 0l1-2v1l2 2-1 1h0v1c-1 0-1 1-1 2l-3 8-4 19c-1 2-1 4-1 5v1l-1 4c-1 3-1 5-2 8 0 4-1 9-1 13v2c-1 2-1 3-2 4v-33c-1-4-1-8-1-13 0-3-1-6 0-9v-18c0-2 1-2 3-3z" class="U"></path><path d="M346 505v5c1-3 1-6 2-9h0c0 6 0 11-1 17v9c-1-4-1-8-1-13 0-3-1-6 0-9z" class="J"></path><path d="M349 484c0 1 1 2 2 2 0 1 0 2 1 2-2 1-2 6-3 8 0 0 0 4-1 5h0c-1 3-1 6-2 9v-5-18c0-2 1-2 3-3z" class="C"></path><path d="M347 487c1 0 1-1 3 0v1 3c-1 1-2 3-2 5l-1 1h0c0-3-1-8 0-10z" class="B"></path><path d="M352 533c-1-1-1-2-1-3l-1-1v-8c0-7 0-16 3-23h1c0-2 1-3 2-4 2-1 2-1 3-2l1-1h0l2 2h-1c-1 1-1 3-2 4-2 4-3 9-5 15 0 3-1 6-1 10l1 1c-1 2-1 4-1 5v1l-1 4z" class="M"></path><path d="M70 134c0-4 1-8 2-11 3-7 10-13 17-16 10-4 23-5 34-1 0 1 1 1 1 2h1c1 1 3 2 4 2l8 4c3 1 6 2 8 4 8 4 14 10 21 16 1 1 2 3 4 4s4 4 5 6c3 3 6 6 8 10l1 1h0c1 2 2 3 3 5h1c1 2 4 5 4 6l-1 1c-1-1-1-1-1-2h-1c0-1-1-1-1-2h-1 0l-1-1h-1v-1h-1s-1-1-1-2h0c-3-4-6-9-10-12l-2-2-1-1c-1-1-2-2-4-2a53.56 53.56 0 0 0-15-15c-2-2-5-4-7-6h-1-1c-6-4-12-8-18-11-13-3-25-6-37 1-5 3-10 8-11 14-2 6-1 11 1 16 2 4 6 7 10 8h-2v1c1 0 2 1 3 1h1c1 0 3 0 4 1-2 1-4 1-7 1h2v-1h-3l-1-1c-5-1-9-4-12-9 0 0 1 0 1-1 0 0-1-1-1-2-1-2-2-3-2-5z" class="N"></path><path d="M124 110h3c12 6 23 13 33 22l6 5c2 2 3 3 5 4v2l17 20h0-1 0l-1-1h-1v-1h-1s-1-1-1-2h0c-3-4-6-9-10-12l-2-2-1-1c-1-1-2-2-4-2a53.56 53.56 0 0 0-15-15c-2-2-5-4-7-6h-1-1c-6-4-12-8-18-11z" class="H"></path><path d="M262 102h0c2-1 3-2 4-3 3-1 6-2 9-2l1 1 1 2 1-1 1 1c-2 3-5 6-6 10h-1v-1h-1c-4 2-8 4-12 5-1 1-3 2-4 2h-1c-2 0-2 0-4-1-9 4-19 8-27 16-3 2-5 5-6 8l-2 2 1 1c1 0 3 0 4 1-2 1-4 2-5 4-1 4-3 10-2 14 0 0 0 1 1 1h0v1 1c-1 1-2 2-3 4-1-1-1-2-1-3l-1 6c-2-3-2-7-3-10v3l-1-1c-1-3-1-6-1-10 1-4 1-10 4-14 1-1 3-2 4-2l1 1c2-4 4-7 7-11h1l4-4c6-4 12-8 19-11 5-3 10-4 14-7 2-1 3-2 4-3z" class="Q"></path><path d="M215 141h0l1 1c1 0 3 0 4 1-2 1-4 2-5 4-1 4-3 10-2 14 0 0 0 1 1 1h0v1 1c-1 1-2 2-3 4-1-1-1-2-1-3 0-9 1-16 5-24z" class="O"></path><defs><linearGradient id="Ac" x1="203.283" y1="150.59" x2="212.616" y2="148.913" xlink:href="#B"><stop offset="0" stop-color="#626262"></stop><stop offset="1" stop-color="#8b8a88"></stop></linearGradient></defs><path fill="url(#Ac)" d="M204 153c1-4 1-10 4-14 1-1 3-2 4-2l1 1c-3 4-5 9-6 14 0 3 0 6-1 9v3l-1-1c-1-3-1-6-1-10z"></path><path d="M276 98l1 2 1-1 1 1c-2 3-5 6-6 10h-1v-1h-1c-4 2-8 4-12 5-1 1-3 2-4 2h-1c-2 0-2 0-4-1-9 4-19 8-27 16-3 2-5 5-6 8l-2 2h0c1-5 4-8 8-12 2-2 4-4 6-5 5-4 11-7 16-9 3-1 6-3 9-4 5-2 10-3 14-6 4-1 6-4 8-7z" class="N"></path><path d="M277 100l1-1 1 1c-2 3-5 6-6 10h-1v-1h-1c-4 2-8 4-12 5-1 1-3 2-4 2h-1c-2 0-2 0-4-1l6-2c4-2 9-3 12-5 4-2 8-5 9-8z" class="U"></path><path d="M256 113l-2 1h5c-1 1-3 2-4 2h-1c-2 0-2 0-4-1l6-2zm281 3c3-3 6-5 9-7 11-7 22-13 34-15 10-2 20 0 28 5 2 1 3 3 5 4h-1c-4-2-7-3-11-4-10-3-21-2-30 1-5 2-11 4-15 6-8 5-14 10-20 16-6 5-13 11-16 17-4 7-10 13-14 20-2 3-4 6-5 9l-9 13c-2 4-4 9-6 13l-14 28-5 13-8 19-4 9-4 10c-2 6-5 11-7 16l-2 2v1 1l-1-1 5-12 21-51c4-11 9-21 14-31 2-4 3-8 5-11l6-10s2-5 3-5c2-4 4-8 6-11 4-7 9-13 13-20 2-2 4-5 6-7 3-4 7-9 11-13l6-5z" class="F"></path><path d="M492 177v1 1c0 1-2 4-2 5s-1 2-2 3h0l-1-1v1h-1l6-10z" class="U"></path><path d="M467 229v3c-1 3-2 5-3 7 0 2-1 3-2 5 0 2-1 4-2 5l-4 9-3 6-2 5c-1 3-2 5-3 7v1l-1 1-1 2v1-1l21-51z" class="X"></path><path d="M537 116c3-3 6-5 9-7 11-7 22-13 34-15 10-2 20 0 28 5h-1-1c-9-4-20-4-29-3-2 1-5 1-7 2-11 4-20 9-29 17-3 2-5 5-8 7l-3 3c-2 2-5 4-7 7l-3 3v-1c3-4 7-9 11-13l6-5z" class="U"></path><path d="M389 424c1 2 0 5 0 7l-3 16-6 25-1 1v1 1l-1 1c0 1 0 1-1 2v1 1c-1 1-1 2-1 3h-1 0v3c0 1 0 2-1 3v2c0 1-1 2-1 3v2c-1 1 0 3-1 4 0 1-1 1-1 2-1 1 0 2-1 2v1l-1 1c-1 2 0 0-1 1 0 1-1 1-1 2h0c-1 1-1 3-1 4l-1 1h0v1l-1 1h0v2c-1 0-1 1-1 2s0 1-1 1v2c-1 1-1 1-1 2l-1 1v2l-1 1v1h0c0 1 0 1-1 2v1 1h0l-1 1v1 1c-1 0-1 1-1 1v2 1l-1 1v2l-1 1v1c0 1 0 1-1 2 0 0 1 1 0 1 0 1 0 2-1 3v2h0l-1 1v1 3h-1c0 1 1 1 0 2v2c0 1 0 2-1 2v3 1c0 1 0 2-1 3v1 1l-1 1c0-1-1-1-1-2 0-4 0-9 1-13 1-1 1-2 2-4v-2c0-4 1-9 1-13 1-3 1-5 2-8l1-4v-1c0-1 0-3 1-5l4-19 3-8c0-1 0-2 1-2v-1h0l1-1-2-2v-1l-1 2c-1 0-3 1-4 0h0 2 0l2-1c1-1 2-4 3-5l1-4 2-5 2-6v-1h1v2 1s0 1-1 1v1l1 1v-1c1 0 1 0 1-1v-1c1 0 1-1 1-1l-1-1v-2-1h2c1-1 4-1 5-2h2v-1h1v-1c0-5 3-11 4-16 1-2 2-5 2-7h-1v-5c0-3 1-5 2-8 0-1 1-2 2-3z" class="H"></path><path d="M387 427c2 2 0 10-1 13h-1v-5c0-3 1-5 2-8z" class="Q"></path><path d="M347 560c1-1 1-2 2-4 0 2-1 3 0 4h0l-2 13h-1c0-4 0-9 1-13z" class="G"></path><path d="M379 465h1c0 1 0 3-1 5l-4 13-1-2c0-2 1-5 0-7-1-1-1-5-1-7h-1 0c1-1 4-1 5-2h2z" class="I"></path><path d="M379 465h1c0 1 0 3-1 5v-1l-3 7h-1v-1c-1-3 0-6 2-8v-1-1h2z" class="K"></path><defs><linearGradient id="Ad" x1="353.995" y1="528.842" x2="361.003" y2="529.84" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#5e5f5e"></stop></linearGradient></defs><path fill="url(#Ad)" d="M368 503h1v1c-1 2-3 4-4 7-6 16-12 33-16 49h0c-1-1 0-2 0-4v-2c0-4 1-9 1-13 1-3 1-5 2-8l1-4v-1c0-1 0-3 1-5l4-19h2v4c-1 1 0 2 0 3 1-2 2-3 4-5h1v-1c1 0 2-1 3-2z"></path><path d="M365 506h1c-1 2-3 4-5 5-1 2-3 5-3 7h-1 0c1-3 2-5 3-7s2-3 4-5h1z" class="U"></path><path d="M358 504h2v4c-1 1 0 2 0 3-1 2-2 4-3 7-1 2-1 4-2 6v2 2c-1 0-1 1-1 1v1 1l-1 2v2 3c0 1 0 1-1 1v3c0 1-1 2 0 3h-1v3c0 1 0 1-1 2v3 2l-1 1v-2c0-4 1-9 1-13 1-3 1-5 2-8l1-4v-1c0-1 0-3 1-5l4-19z" class="C"></path><path d="M372 467h1c0 2 0 6 1 7 1 2 0 5 0 7l1 2c-1 2-2 5-2 8l-3 10v1l-1 1h-1c-1 1-2 2-3 2v1h-1c-2 2-3 3-4 5 0-1-1-2 0-3v-4h-2l3-8c0-1 0-2 1-2v-1h0l1-1-2-2v-1l-1 2c-1 0-3 1-4 0h0 2 0l2-1c1-1 2-4 3-5l1-4 2-5 2-6v-1h1v2 1s0 1-1 1v1l1 1v-1c1 0 1 0 1-1v-1c1 0 1-1 1-1l-1-1v-2-1h2 0z" class="P"></path><path d="M364 491v-3c1-1 3-3 3-4 0 3 1 8 0 11-1 1 0 3 0 5l1 1 1-1 1 2-1 1h-1c-1 0-1 0-1-1-2-2-2-5-2-7v-3c0-1 0-1-1-1zm8-24h0c1 2 0 5 1 7v9c-1-1-2-5-4-6h1c1-1 1-1 0-2-1 0-1 0-1 1l-2 1-1-1 2-6v-1h1v2 1s0 1-1 1v1l1 1v-1c1 0 1 0 1-1v-1c1 0 1-1 1-1l-1-1v-2-1h2z" class="G"></path><path d="M369 485h0 1c1 1 1 3 1 4l2 2-3 10v1l-1-2-1 1-1-1 1-1v-4-2-6c1 0 1-1 1-2z" class="J"></path><g class="E"><path d="M370 501v-5-6l1-1 2 2-3 10z"></path><path d="M369 485c0 2 0 3 1 4v5c-2 2-1 4-2 5v-4-2-6c1 0 1-1 1-2z"></path></g><path d="M364 481l2-5 1 1c1 1 4 3 3 4 0 1-2 2-3 3 0 1-2 3-3 4v3c-1 2-2 3-3 5 0-1 0-2 1-2v-1h0l1-1-2-2v-1l-1 2c-1 0-3 1-4 0h0 2 0l2-1c1-1 2-4 3-5l1-4z" class="M"></path><path d="M364 491c1 0 1 0 1 1v3c0 2 0 5 2 7 0 1 0 1 1 1-1 1-2 2-3 2v1h-1c-2 2-3 3-4 5 0-1-1-2 0-3v-4h-2l3-8c1-2 2-3 3-5z" class="C"></path><path d="M364 506h-1l1-2c-1-1-1-5-1-6 0-2 1-3 2-3 0 2 0 5 2 7 0 1 0 1 1 1-1 1-2 2-3 2v1h-1z" class="W"></path><path d="M218 162c3 0 4 0 7 2 1 1 3 2 4 4l1 2 2 3c1 2 1 5 2 6 4 7 8 10 14 14h1c4 0 7 1 11 0h1c1 0 3 0 3-1l2-2h0v2l1 1h0c4-3 5-6 7-11l1-1 1 1h0v-1h1c1 5 3 10 5 15l3 12v1l-1-1h0v1l-1-2h0v1c-1 0-1-1-1-1h-1 0c-3 0-5 0-8 1-4 1-8 3-13 4h-2-3l-1-1c-1 0-2-1-3-2s-2-2-3-2v1c0 1 1 2 1 3-1 0-1 1-2 1s-2 1-2 1h-1c-1 0-3 1-4 0h-3 0c-2 0-2 0-3-1h0 0 0l-1-1c-1 0-1 0-2-1 0 0-1 0-2-1v1h0c-1 0-1 0-2-1h-1 0c0 1 1 1 2 1 0 1 1 1 2 2 0 0 1 0 1 1h1c1 0 2 0 3 1h0c-3 1-7-3-10-4h0c-1-1-2-2-2-4-1-2-2-5-3-6-2-2-3-2-3-5l-2-2v-1l-1-3c-4-6-5-14-3-21 1-2 2-3 3-4l4-2z" class="T"></path><path d="M223 190v-1c-1-2-3-9-2-11h0c1-1 1-2 2-2 1-1 1-2 2-1 1 0 1 1 1 1h1v-1c0 1 0 3-1 4s-1 1-2 1l-1-1c0-1 1-1 1-2-1 0-1 0-2 1v4 3c1 2 1 3 1 5z" class="S"></path><path d="M217 195l7 8c2-1 3-1 5-2l2-1c-1 1-2 2-3 4v1c-1 1-1 1-1 2l2 2h-1c-1 0-2-1-3-2-1 0-2 0-2-1-1-2-2-5-3-6-2-2-3-2-3-5z" class="L"></path><path d="M215 192c2 2 5 6 8 6 4 1 9 0 12-1h1-1c-1 1-3 1-4 3l-2 1c-2 1-3 1-5 2l-7-8-2-2v-1z" class="C"></path><path d="M230 184h2l1 1h0l2 3c2 3 3 4 6 6-2 1-3 0-5 1l-1-1c-1-1-3-1-4-3 0-2-1-4-1-7z" class="L"></path><path d="M230 184h2l1 1h0l-1 1h1v1 1h-1v2l-1 1c0-2-1-4-1-7z" class="E"></path><path d="M235 187c4 3 7 6 12 8 0-1 1-1 1-2h1c4 0 7 1 11 0h1c1 0 3 0 3-1l2-2h0v2l1 1c-5 3-11 5-17 4l-9-3c-3-2-4-3-6-6v-1z" class="G"></path><path d="M260 193h1c1 0 3 0 3-1l2-2h0v2c-6 3-12 5-18 3h-1c0-1 1-1 1-2h1c4 0 7 1 11 0z" class="S"></path><path d="M228 170h0l1 5v1l-1-2h0l-1 1v1h-1s0-1-1-1c-1-1-1 0-2 1-1 0-1 1-2 2h0c-1 2 1 9 2 11v1l3 5c3 1 5 0 7 1-2 1-4 1-7 1l-1-1h1c-2-3-4-5-6-7-1-3-2-8-1-11v-1c1-3 2-4 5-6 1 0 3 0 4-1h0z" class="N"></path><path d="M229 168l1 2 2 3c1 2 1 5 2 6 4 7 8 10 14 14 0 1-1 1-1 2-5-2-8-5-12-8v1l-2-3h0l-1-1h-2c0-2-1-6-1-8v-1l-1-5 1-2z" class="Y"></path><path d="M229 176v-1c3 2 2 6 4 8 0 1 2 3 2 4v1l-2-3h0l-1-1h-2c0-2-1-6-1-8z" class="I"></path><path d="M276 182v-1h1c1 5 3 10 5 15l3 12v1l-1-1h0v1l-1-2h0v1c-1 0-1-1-1-1h-1 0c-3 0-5 0-8 1-4 1-8 3-13 4h-2-3l-1-1c-1 0-2-1-3-2h2c1 1-1 1 1 1h0 1c2 1 7 0 9 0l1-1c4-1 9-3 12-7 2-5 0-15-1-20z" class="S"></path><path d="M218 162c3 0 4 0 7 2 1 1 3 2 4 4l-1 2h0 0c-1 1-3 1-4 1-3 2-4 3-5 6v1c-1 3 0 8 1 11 2 2 4 4 6 7h-1l1 1 2 1h0-1-4c-3 0-6-4-8-6l-1-3c-4-6-5-14-3-21 1-2 2-3 3-4l4-2z" class="P"></path><path d="M216 169c1-1 2-3 4-3s4 0 6 1c0 1 0 1-1 1-3 0-5 1-7 3 0 1-1 2-2 3h0-1c0-1 0-1 1-2v-3h0z" class="F"></path><path d="M216 169h0v3c-1 1-1 1-1 2h1 0c0 3 0 7 2 11l1 2-1 1c1 2 1 3 1 4-1 0-1-1-2-2-4-5-4-12-3-19l1-1 1-1z" class="E"></path><path d="M216 169h0v3c-1 1-1 1-1 2h1 0c0 3 0 7 2 11l1 2-1 1c-3-5-3-11-3-18l1-1z" class="D"></path><path d="M218 162c3 0 4 0 7 2 1 1 3 2 4 4l-1 2h0c0-2-1-2-2-3-2-1-4-1-6-1s-3 2-4 3l-1 1-1 1c-1 7-1 14 3 19 1 1 1 2 2 2 2 2 4 3 6 4l1 1 2 1h0-1-4c-3 0-6-4-8-6l-1-3c-4-6-5-14-3-21 1-2 2-3 3-4l4-2z" class="Q"></path><path d="M225 164c1 1 3 2 4 4l-1 2h0c0-2-1-2-2-3-2-1-4-1-6-1s-3 2-4 3l-1 1-1 1v-1c0-1 1-2 1-3 1-1 2-2 4-2 2-1 4 0 6-1z" class="V"></path><path d="M290 105c0 1 0 1-1 2-1 3-3 5-4 8h-1c-1 2-2 5-2 7l-1 3c-1 2-1 3-1 5 0 1 0 1-1 2v1 6 10c1 1 1 3 1 4l1 3 2 11 6 16 2 7c1 1 1 2 1 3l49 136c1 5 3 10 4 14s2 7 4 10l4 16c3 3 3 6 4 9h1v-1l8 26c2 4 4 8 4 12v11c0 9 1 19 0 28-2-4-4-8-5-13-3-10-5-20-8-30 0-3-1-6-1-9l-11-34c1-2 0-3-1-5 0-4-1-6-2-9-1-2-1-3-1-4-1 0-1 0-1-1v-1c-1-2-1-3-2-5h0v-1c0-1 0-2-1-2v-1-1l-1-1-1-5c0-1 0-1-1-2h0v-2l-2-5v-2h-1c0-1 0-1-1-2v-2c0-1 0 0-1-1v-2h0l-1-1v-1-1-1h-1v-2c0-1-1-2-1-3l-3-7v-1-1l-2-7-1-3c0-1 0-1-1-2v-2l-2-4c0-1-1-1-1-2l-13-36-13-37-8-25c-2-6-4-12-5-18-2-10-2-21 0-31 1-2 2-5 3-7 3-6 6-12 10-17z" class="K"></path><path d="M283 167l6 16 2 7c1 1 1 2 1 3s0 2 1 3v1h0v3l1 1v1 1l1 1h0v1c0 1 0 1 1 2h0c0 2 1 3 1 4l2 5s0 1 1 1v2l1 1v2c1 0 1 0 1 1v1h0c0 1 1 1 1 2v2l3 7c0 1 0 1 1 2v2l1 1 1 2v1c0 1 0 1 1 2v1h0v2l1 1 3 8v2c1 1 1 2 1 3 1 1 1 2 2 3v1 1l5 13 1 4v1l2 5 3 8c0 2 1 3 1 5 1 1 1 1 1 2 1 1 0-1 1 1v2h1l2 6v3l1 2 1 2s0 1 1 2v2c0 1 1 3 1 4v1l-2-4-4-13c0-1 0-2-1-3v-1l-2-4-12-36-4-11-9-26c-1-1-1-2-1-3-1 0 0 0-1-1l-8-23-3-9-4-13c-1-1-1-2-1-3l-3-9v-4z" class="W"></path><path d="M345 368c1-2 0-3-1-5 0-4-1-6-2-9-1-2-1-3-1-4-1 0-1 0-1-1v-1c-1-2-1-3-2-5h0v-1c0-1 0-2-1-2v-1-1l-1-1-1-5c0-1 0-1-1-2h0v-2l-2-5v-2h-1c0-1 0-1-1-2v-2c0-1 0 0-1-1v-2h0l-1-1v-1-1-1h-1v-2c0-1-1-2-1-3l-3-7v-1-1l-2-7-1-3c0-1 0-1-1-2v-2l-2-4c0-1-1-1-1-2l-13-36-13-37-8-25c-2-6-4-12-5-18-2-10-2-21 0-31 1-2 2-5 3-7l-2 9v1c-2 9-2 20 1 30 1 6 3 12 5 19l13 36 3 8 12 36 8 20 3 11 9 25 10 31 10 35 4 14c0 1 1 4 1 5h0c-1-1-1-1-1-2v-1c-1 1-1 1 0 2v1l-11-34z" class="J"></path><path d="M292 193l49 136c1 5 3 10 4 14s2 7 4 10l4 16c3 3 3 6 4 9h1v-1l8 26c2 4 4 8 4 12v11c0 9 1 19 0 28-2-4-4-8-5-13h1v2h1c0-7-1-13-2-21-1 0-1-1-1-2v-2c-1-1-1-2-1-3 0-2-1-5-1-6l-3-9-21-70h0v-1c0-1-1-3-1-4v-2c-1-1-1-2-1-2l-1-2-1-2v-3l-2-6h-1v-2c-1-2 0 0-1-1 0-1 0-1-1-2 0-2-1-3-1-5l-3-8-2-5v-1l-1-4-5-13v-1-1c-1-1-1-2-2-3 0-1 0-2-1-3v-2l-3-8-1-1v-2h0v-1c-1-1-1-1-1-2v-1l-1-2-1-1v-2c-1-1-1-1-1-2l-3-7v-2c0-1-1-1-1-2h0v-1c0-1 0-1-1-1v-2l-1-1v-2c-1 0-1-1-1-1l-2-5c0-1-1-2-1-4h0c-1-1-1-1-1-2v-1h0l-1-1v-1-1l-1-1v-3h0v-1c-1-1-1-2-1-3z" class="R"></path><defs><linearGradient id="Ae" x1="351.449" y1="422.225" x2="375.467" y2="407.619" xlink:href="#B"><stop offset="0" stop-color="#b2b0b1"></stop><stop offset="1" stop-color="#d9d9d9"></stop></linearGradient></defs><path fill="url(#Ae)" d="M353 369c3 3 3 6 4 9h1v-1l8 26c2 4 4 8 4 12v11c0 9 1 19 0 28-2-4-4-8-5-13h1v2h1c0-7-1-13-2-21 1-3-1-7-2-11l-1-10-4-16c-1-5-3-10-5-16z"></path><path d="M271 109h1v1h1l-4 10c0 3 0 5-1 8h-1c-1 2-3 4-5 5l1 1h0c1-1 1-1 2-1h1v14c0 4 1 8 0 12l-2-2v1c-2-1-4-3-6-4h-1c-2-1-4-1-6 0-1 0-3 1-4 2l1 1c4-1 7-1 10 1 1 1 2 1 3 2 3 3 5 8 5 12v9 1 2c0 2-1 4-2 5l-4 4c-4 1-7 0-11 0h-1c-6-4-10-7-14-14-1-1-1-4-2-6l-2-3-1-2c-1-2-3-3-4-4-3-2-4-2-7-2l-4 2v-1-1h0c-1 0-1-1-1-1-1-4 1-10 2-14 1-2 3-3 5-4-1-1-3-1-4-1l-1-1 2-2c1-3 3-6 6-8 8-8 18-12 27-16 2 1 2 1 4 1h1c1 0 3-1 4-2 4-1 8-3 12-5z" class="W"></path><path d="M227 134h2v1c0 1-1 1-2 1h-1v-1c0-1 1-1 1-1z" class="N"></path><path d="M265 125l1 1c-2 2-4 5-7 5-1 0-2 0-2-1v-1h2c3-1 4-2 6-4z" class="P"></path><path d="M250 131c2 0 5 1 8 1 4 0 6-2 9-5v1c-1 2-3 4-5 5h0c-1 0-2 0-3 1-2 1-5 0-7-1h-6c1-1 3-1 4-2z" class="M"></path><path d="M244 120h3c1 2 1 4 3 5v-1l1 2c2 2 4 2 7 3h1-2v1c0 1 1 1 2 1-3 0-6 0-8-1s-4-6-5-8v-1c-2 1-4 1-7 1h0l5-2z" class="J"></path><path d="M217 139c1-3 3-6 6-8 8-8 18-12 27-16 2 1 2 1 4 1h1l-1 1h0c-2 2-4 3-4 5v2 1c-2-1-2-3-3-5h-3l-5 2-6 2v1c1 1 2 1 3 3l-2 2c-1 0-2 0-3-1-3 0-5 3-7 5s-3 4-5 6l-2-1z" class="M"></path><path d="M244 120c1-1 2-1 3-1 2-1 4-2 7-2-2 2-4 3-4 5v2 1c-2-1-2-3-3-5h-3z" class="O"></path><path d="M271 109h1v1h1l-4 10-3 6-1-1c-2 2-3 3-6 4h-1c-3-1-5-1-7-3l-1-2v-2c0-2 2-3 4-5h0l1-1c1 0 3-1 4-2 4-1 8-3 12-5z" class="Q"></path><path d="M256 117h1c0 1 1 1 2 1l1 1c1 1 1 1 3 1v1c0 1 0 2-1 3s-2 2-3 2c-2 0-3 0-4-1s-2-2-2-3c0-2 2-3 3-5z" class="U"></path><path d="M271 109h1v1h1l-4 10-3 6-1-1c1-1 1-3 2-4l-1-1c-1-2-4-3-6-3l3 3h0c-2 0-2 0-3-1l-1-1c-1 0-2 0-2-1h-1-2l1-1c1 0 3-1 4-2 4-1 8-3 12-5z" class="E"></path><path d="M271 109h1v1h1l-4 10-3 6-1-1c1-1 1-3 2-4 2-3 2-8 4-11v-1z" class="I"></path><path d="M221 141c0-1 2-2 2-3h3c2 0 3 0 5-1s4-3 5-6c0-1 0-2 1-3 0-2 4-4 6-4s2 1 3 3l3 3h-1l2 1c-1 1-3 1-4 2-2 2-3 3-4 6l-4 9c-1 0-2 1-3 1v2c-1-2-1-5-2-7 0 0-1-1-2-1v-1h1l1-2c-3 2-6 4-10 4-1 0-2-1-3-1-1-1-3-1-4-1l-1-1 2-2 2 1 2 1z" class="N"></path><path d="M238 132c1-1 2-4 3-4l3 2c-2 1-2 1-3 3h-1 0v-2l-2 1z" class="L"></path><path d="M221 141c3 1 7 1 10-1 1-1 2-2 3-2l-1 2c-3 2-6 4-10 4-1 0-2-1-3-1-1-1-3-1-4-1l-1-1 2-2 2 1 2 1z" class="U"></path><path d="M244 130h4l2 1c-1 1-3 1-4 2-2 2-3 3-4 6l-4 9c-1 0-2 1-3 1v2c-1-2-1-5-2-7 0 0-1-1-2-1v-1h1l1-2 1-2h1l3-6 2-1v2h0 1c1-2 1-2 3-3z" class="J"></path><path d="M236 148c2-2 3-5 4-8 0 0 1-1 1-2l1 1-4 9c-1 0-2 1-3 1l1-1z" class="C"></path><path d="M244 130h4l2 1c-1 1-3 1-4 2-2 2-3 3-4 6l-1-1c1-1 2-3 2-5h-2c1-2 1-2 3-3z" class="F"></path><path d="M238 132l2-1v2h0l-4 15-1 1v2c-1-2-1-5-2-7 0 0-1-1-2-1v-1h1l1-2 1-2h1l3-6z" class="Y"></path><path d="M220 143c1 0 2 1 3 1 4 0 7-2 10-4l-1 2h-1v1c1 0 2 1 2 1 1 2 1 5 2 7-1 2-2 4-2 6-1 5-1 10-1 14v2l-2-3-1-2c-1-2-3-3-4-4-3-2-4-2-7-2l-4 2v-1-1h0c-1 0-1-1-1-1-1-4 1-10 2-14 1-2 3-3 5-4z" class="Q"></path><path d="M218 162v-1c-1 0-2 0-2-1-1-1-1-3-1-4 1-4 1-8 4-11 2 0 4 1 6 1s3-1 5 0h1-6c-2 1-5 0-6 2-2 2-2 8-2 10 0 1 0 2 1 2s3 0 4 1c3 0 7 4 8 7v2l-1-2c-1-2-3-3-4-4-3-2-4-2-7-2z" class="R"></path><path d="M220 143c1 0 2 1 3 1 4 0 7-2 10-4l-1 2h-1v1c1 0 2 1 2 1 1 2 1 5 2 7-1 2-2 4-2 6h0c-2-2 0-5-1-8 0-1-1-2-1-3h-1c-2-1-3 0-5 0s-4-1-6-1c-3 3-3 7-4 11 0 1 0 3 1 4 0 1 1 1 2 1v1l-4 2v-1-1h0c-1 0-1-1-1-1-1-4 1-10 2-14 1-2 3-3 5-4z" class="K"></path><path d="M263 134h0c1-1 1-1 2-1h1v14c0 4 1 8 0 12l-2-2v1c-2-1-4-3-6-4h-1c-2-1-4-1-6 0-1 0-3 1-4 2l1 1c-2 1-5 3-6 6h0c0 1-1 2-1 3l-1 1c-1-1 0-2-1-3-2 3-3 6-3 10 0 2 1 5 2 7 2 5 6 10 11 12h-1c-6-4-10-7-14-14-1-1-1-4-2-6v-2c0-4 0-9 1-14 0-2 1-4 2-6v-2c1 0 2-1 3-1l4-9c1-3 2-4 4-6h6c2 1 5 2 7 1 1-1 2-1 3-1h0l1 1z" class="L"></path><path d="M241 166h-1v-1h0c0-1 1-2 1-3l-1-1c1-3 7-6 9-8 3-1 6-1 9 0h0 1 1l1 1 1 1c1 0 1 1 2 2v1c-2-1-4-3-6-4h-1c-2-1-4-1-6 0-1 0-3 1-4 2l1 1c-2 1-5 3-6 6h0c0 1-1 2-1 3z" class="H"></path><path d="M262 133l1 1h-1c-2 2-5 2-7 2-3-1-6-1-9-1-2 3-3 6-4 9s-3 6-4 10c-1 2-1 4-2 7-1 2-2 5-2 8 0 2 0 5 1 7s1 4 3 5c2 5 6 10 11 12h-1c-6-4-10-7-14-14-1-1-1-4-2-6v-2c0-4 0-9 1-14 0-2 1-4 2-6v-2c1 0 2-1 3-1l4-9c1-3 2-4 4-6h6c2 1 5 2 7 1 1-1 2-1 3-1h0z" class="W"></path><path d="M232 171l1 1h0c0-1 0-1 1-1v3c0 1 1 4 0 5-1-1-1-4-2-6v-2z" class="K"></path><defs><linearGradient id="Af" x1="230.515" y1="151.736" x2="238.703" y2="166.022" xlink:href="#B"><stop offset="0" stop-color="#6d6a6b"></stop><stop offset="1" stop-color="#a3a4a3"></stop></linearGradient></defs><path fill="url(#Af)" d="M235 149c1 0 2-1 3-1-2 8-5 15-4 23-1 0-1 0-1 1h0l-1-1c0-4 0-9 1-14 0-2 1-4 2-6v-2z"></path><path d="M248 157c4-1 7-1 10 1 1 1 2 1 3 2 3 3 5 8 5 12v9 1 2c0 2-1 4-2 5l-4 4c-4 1-7 0-11 0-5-2-9-7-11-12-1-2-2-5-2-7 0-4 1-7 3-10 1 1 0 2 1 3l1-1c0-1 1-2 1-3h0c1-3 4-5 6-6z" class="T"></path><path d="M248 157c4-1 7-1 10 1 1 1 2 1 3 2l-1 1h-2c-3-2-8-2-12-2-1 1-3 2-4 4h0c1-3 4-5 6-6z" class="D"></path><path d="M261 160c3 3 5 8 5 12v9 1-1c0-1-1-3-1-5-1-6-3-11-7-15h2l1-1z" class="P"></path><path d="M444 289c2-5 5-10 7-16l4-10 4-9 8-19 5-13 14-28c2-4 4-9 6-13l9-13c1-3 3-6 5-9 4-7 10-13 14-20 3-6 10-12 16-17 6-6 12-11 20-16 4-2 10-4 15-6 9-3 20-4 30-1 4 1 7 2 11 4h1c1 1 1 2 2 3s2 3 3 4c2 2 4 7 4 10v1c1 2 1 4 1 6 0 1-1 3-1 3v-2l-1 2c0-2-1-4-2-6-1-1-4-5-6-6-2-2-3-4-5-5-6-4-13-6-20-6-14-1-27 6-38 15l-5 4c-1 1-2 2-3 2h0c-2 1-4 3-5 4l-12 12c-1 2-2 3-4 4-7 10-13 20-20 30-4 8-9 15-12 24l-19 40-10 27-20 54c-2 4-4 9-5 13l-14 44-11 35h-1c-1 2-2 4-4 6-1 2-1 4-2 5l-7 21c-1 1-2 5-3 7 0 0 0 1-1 1-1 1-2 4-2 6-2 4-3 9-5 14 0 0 0 1-1 2h0l-5 14-21 63h-1c0-3 2-8 3-11 0-1 1-3 1-5h-1c1-4 3-8 4-12l15-42 15-48c1-5 3-11 5-16l42-128 1 1v-1-1l2-2z" class="B"></path><path d="M441 292l1 1v-1-1l2-2-3 9-5 13-5 16c-1 3-3 6-4 9l-3 12c-2 4-3 9-5 13-2 8-4 16-7 24-1 2-2 6-3 8v2c-1 1-1 2-1 3l-4 12c-1 2-2 7-4 10h0-1 0l42-128z" class="M"></path><path d="M384 477v-2c1-4 3-8 4-12l11-34c0-2 1-5 2-7l9-28 19-57 5-14 13-36 8-19 9-22 7-17 12-27c2-3 3-6 4-9l9-15c3-4 5-10 8-13 2-3 3-6 5-8l10-15c3-4 6-7 9-10 12-13 27-26 45-31 9-3 18-3 27-1 5 1 10 4 15 6h0c1 1 2 3 3 4h0c-11-7-23-12-37-10-13 2-26 10-37 19-3 2-5 5-8 8-6 5-11 10-15 16l-1 2-3 3c-4 6-8 11-12 17-1 1-1 3-2 5l-18 32c-10 22-19 45-28 68l-8 18c-2 4-3 9-4 13l-5 10-7 22-9 26-6 21-13 39c-1 2-2 5-2 7l-7 21c-1 1-2 5-3 7 0 0 0 1-1 1-1 1-2 4-2 6-2 4-3 9-5 14 0 0 0 1-1 2h0z" class="O"></path><path d="M403 426c0-2 1-5 2-7l13-39 6-21 9-26 7-22 5-10c1-4 2-9 4-13l8-18c9-23 18-46 28-68l18-32c1-2 1-4 2-5 4-6 8-11 12-17l3-3 1-2c4-6 9-11 15-16 3-3 5-6 8-8 11-9 24-17 37-19 14-2 26 3 37 10h0c2 2 4 7 4 10v1c1 2 1 4 1 6 0 1-1 3-1 3v-2l-1 2c0-2-1-4-2-6-1-1-4-5-6-6-2-2-3-4-5-5-6-4-13-6-20-6-14-1-27 6-38 15l-5 4c-1 1-2 2-3 2h0c-2 1-4 3-5 4l-12 12c-1 2-2 3-4 4-7 10-13 20-20 30-4 8-9 15-12 24l-19 40-10 27-20 54c-2 4-4 9-5 13l-14 44-11 35h-1c-1 2-2 4-4 6-1 2-1 4-2 5z" class="P"></path><path d="M405 421l2-5 1-2 2-3 1-4v-1l2-6 1-4 1-5h0 0c1-1 1-1 1-2l3-10c0-2 1-3 1-4l1-4 1-1v-1c1-2 2-5 3-7s1-3 2-4v-2c1-1 2-3 2-4h0v-2c1 0 1 0 1-1v-1c0-1 1-1 1-3l1-1v-2h0v-1c0-1 1-2 1-3s1-3 2-4h0v-2l1-1 1-3v-1-1l1-1v-2l2-5c0-1 0-1 1-2v-1h0v-2l2-3v-1-1l1-3c3-7 12-33 16-36l-20 54c-2 4-4 9-5 13l-14 44-11 35h-1c-1 2-2 4-4 6z" class="E"></path><path d="M521 148s1-1 1-2c15-20 36-38 61-42 10-2 21 0 29 6h0c3 2 5 4 7 6 1 2 1 3 2 5l1-1v1c1 2 1 4 1 6 0 1-1 3-1 3v-2l-1 2c0-2-1-4-2-6-1-1-4-5-6-6-2-2-3-4-5-5-6-4-13-6-20-6-14-1-27 6-38 15l-5 4c-1 1-2 2-3 2h0c-2 1-4 3-5 4l-12 12c-1 2-2 3-4 4z" class="N"></path></svg>