<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="114 94 804 812"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#bfbebd}.C{fill:#f8f7f7}.D{fill:#dcdbdb}.E{fill:#a6a5a5}.F{fill:#9f9d9d}.G{fill:#424141}.H{fill:#1c1c1c}.I{fill:#797979}.J{fill:#8a8989}.K{fill:#101010}</style><path d="M756 241h1l1 1c0 1 1 1 0 3-2 0-2 0-3-1v-2l1-1z" class="C"></path><path d="M265 240c1 0 1 0 2 1 0 2 0 2-1 3h-2l-1-1v-2l2-1z" class="D"></path><path d="M723 408l2-1 1 1v4h-2c-2-1-2-1-2-3l1-1zm-426-1c2 0 2 1 3 2 0 1 0 1-1 3h-2l-2-2c1-2 1-2 2-3zm94 192h0c2 0 2 0 3 1 0 2 0 2-2 3h-1c-1 0-1-1-2-2 1-1 0-1 2-2zM153 388h2l1 2c0 1 0 1-1 3h-1c-1-1-2-1-2-2 0-2 0-2 1-3z" class="C"></path><path d="M629 599h1c1 0 2 0 2 1 0 2 0 2-1 3l-1 1c-2-1-2-1-2-2 0-2 0-2 1-3z" class="B"></path><path d="M867 388h1c1 1 2 1 2 3 0 1-1 1-2 2-2 0-2 0-3-2v-1l2-2z" class="C"></path><path d="M349 514h0c2 0 2 1 3 2-1 1-1 2-2 3h-2c-1-1-1-1-2-3 1-1 1-1 3-2zm322 0h2c1 0 1 0 2 2 0 1 0 1-1 3h-3c-1-2-1-2-1-3l1-2z" class="D"></path><path d="M725 525h1c1 1 2 2 2 3 0 2-1 2-2 3h-1c-2-1-2-2-3-3 1-2 1-2 3-3zm-429 0h1c1 1 1 1 2 3 0 2 0 1-2 3h-2c-1-2-1-2-2-3 1-2 1-2 3-3zm-7-203c1 0 2 0 3 1s1 1 1 3l-2 2h-2c-1-1-1-1-2-3 1-1 1-2 2-3z" class="C"></path><path d="M781 366h1c2 1 2 1 3 2 0 3 0 2-2 4h-2c-1-1-1-1-2-3 0-1 1-2 2-3z" class="D"></path><path d="M238 366h3c1 1 1 1 1 3 0 1 0 1-1 3h-1c-2 0-2-1-3-2-1-2-1-2 1-4zm492-44h2c2 2 2 2 2 4l-2 2h-2c-1-1-2-1-2-2 0-2 1-3 2-4zm-473-41h2c1 1 1 1 2 3 0 2 0 2-2 3h-2c-2-1-2-1-2-3s0-2 2-3zm507 0h1c1 0 2 1 3 2v2c0 1-1 1-2 2h-2c-2 0-2 0-2-2s0-2 2-4zm4-15h2c2 0 1 0 2 2 0 2 1 2-1 4h-3c-1-1-1-1-2-3 1-1 1-2 2-3zm-44 348s1 0 2 1 1 1 1 3c0 1 0 2-2 3-1 0-1 0-2-1-2-1-2-1-2-3 1-2 1-2 3-3zm-427 0c1 0 1 0 3 1 1 1 1 1 1 3-1 2-1 2-2 3l-3-1c-1-1-1-1-2-3 1-2 2-2 3-3z" class="C"></path><path d="M892 265c1 0 2 0 3 1 1 0 1 1 1 2-1 2-1 2-2 3h-3c-1-1-1-1-2-3 1-2 1-2 3-3z" class="B"></path><path d="M128 265h2c1 1 1 1 2 3-1 2-1 2-2 3-2 1-2 0-3 0l-2-4c1-1 1-1 3-2zm64-78c0-1 1-2 2-3a30.44 30.44 0 0 1 8-8c9-6 21-10 32-8l12 3h-1c-3 1-5-3-8 1v2l-1 1c3 0 5 1 7 2v1c-8-3-14-4-22-4-9 1-18 4-24 12-4 4-5 9-4 15 0 2 1 3 2 5-1 2-3 4-4 6-3-8-2-17 1-25z" class="D"></path><path d="M234 168l12 3h-1c-3 1-5-3-8 1v2l-1 1c3 0 5 1 7 2v1c-8-3-14-4-22-4 3-1 5 0 7 0l1-1c1 1 2 1 4 1h1l2-4v-1h-3l1-1z" class="B"></path><path d="M313 378c-4-5-9-11-15-14-3-2-6-3-9-5-10-7-19-15-30-20-4-1-9-2-13-3 4 0 8 0 11-1 2 0 6-3 7-3 4 0 10 4 13 6 5 4 8 9 12 13 5 5 10 8 14 12 9 7 17 16 23 25 6 12 10 25 12 39 2 13 2 26 7 39 3 9 8 17 13 24 3 6 7 11 11 16 6 10 10 23 11 34 0 17-6 33-17 45-10 10-23 20-38 20-8 0-17-3-24-8 11 2 20 3 30 0 3-1 5-3 8-4h0-2c-4-2-8-3-12-4-5-3-11-7-13-13l-1-4c9 7 21 11 34 10 1 0 2-1 3-1h1c9-3 18-9 23-18 4-8 5-19 4-28v-1c0 9-1 19-8 26-3 3-7 5-12 4-1 0-3 0-4-1h2c4 0 8-1 11-4s5-7 6-11c3-13 0-29-7-41l-14-20c-7-12-13-25-14-39-2 2-5 5-6 8-4 6-5 17-13 20l-1-12-5 3c-1 1-2 1-4 2-4 0-8 7-11 10l1-9c-5 5-10 7-18 7-8-1-14-6-19-12l-4-5c8 5 14 7 23 5 11-2 22-11 29-20 9-14 13-31 10-47-1-7-4-13-7-18-2-3-4-4-5-7 2 1 4 4 5 6l2-1z" class="C"></path><defs><linearGradient id="A" x1="318.324" y1="381.773" x2="319.169" y2="394.125" xlink:href="#B"><stop offset="0" stop-color="#2c2a2a"></stop><stop offset="1" stop-color="#5c5b5b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M313 378c4 5 8 10 11 16-1 0-2 0-3 1v1 1c-2-7-6-12-10-18l2-1z"></path><defs><linearGradient id="C" x1="328.649" y1="419.214" x2="323.902" y2="433.632" xlink:href="#B"><stop offset="0" stop-color="#a5a5a4"></stop><stop offset="1" stop-color="#c7c5c5"></stop></linearGradient></defs><path fill="url(#C)" d="M319 442h1c1-2 1-4 2-6 1-1 1-2 1-2v-2c1-2 1-4 1-5 1-3 1-6 2-8h3c1 2-1 7 0 10v1c0 5-2 9-3 14l-1 1-3 6h-1c0-2 0-2 1-3v-2s1 0 1-1v-1l1-1v-1h0c0-2 0-3 1-4v-1h0c0-1 0-1 1-2v-1h-2c-2 3-3 9-5 13l-1 2c0 1-2 1-2 2l-1-1 1-1c0-1 1-1 1-2s1-2 1-3h1v-2z"></path><defs><linearGradient id="D" x1="322.52" y1="393.808" x2="327.157" y2="418.501" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#D)" d="M324 394c5 11 8 24 5 36v-1c-1-3 1-8 0-10h-3c-1 2-1 5-2 8 0 1 0 3-1 5v2s0 1-1 2c-1 2-1 4-2 6h-1l2-7c3-9 4-19 3-27-1-4-2-7-3-11v-1-1c1-1 2-1 3-1z"></path><path d="M717 371c0 1-1 2-1 3-9 11-15 26-13 41v1c2 11 7 23 13 32 7 8 17 16 28 17 9 2 14 0 21-6-2 4-4 7-6 9-6 6-12 9-20 9-7 0-10-3-15-8l2 10-8-9c-1-1-2-2-3-2-3 0-6-2-8-4l-1 1c-1 3-1 8-2 12-4-4-7-8-9-13s-3-8-6-11l-3-4c-1 1-1 6-2 8-1 5-2 9-4 13-6 13-14 24-22 36-7 11-11 27-8 40 1 4 3 9 7 13s9 4 14 4c-2 1-4 1-6 1-4 1-8-1-11-4-7-6-8-16-9-25v1c-1 3 0 6 0 8 0 9 3 18 9 25s15 13 25 13c11 1 24-4 32-11-1 3-2 6-4 8-5 9-15 12-25 14v1h3l1 1c10 6 23 5 34 2-7 5-15 8-24 8-15 0-28-9-37-19-11-12-18-29-17-45 0-12 5-25 11-34 7-12 15-22 21-35s7-27 9-42c3-18 8-35 19-49 4-6 9-11 14-16s11-8 16-13c5-4 8-10 13-14 2-2 9-6 13-5 2 0 4 2 6 2 3 1 7 1 11 1-4 1-8 2-12 4-7 2-13 6-18 10-5 3-9 6-13 9-3 2-6 3-9 5-2 1-4 3-6 5 1 1 1 1 0 2z" class="C"></path><defs><linearGradient id="E" x1="698.375" y1="404.684" x2="695.516" y2="410.518" xlink:href="#B"><stop offset="0" stop-color="#7b7b7a"></stop><stop offset="1" stop-color="#919092"></stop></linearGradient></defs><path fill="url(#E)" d="M696 414c0-3 0-6 1-9l3-3c-2 7-2 13-1 20l-2-1v-6h-1v-1z"></path><path d="M696 415h1v6l2 1v2c-1 2-1 2-1 4l1 2v3l1 1v1 5l-3-8-2-2v-1c0-5 1-9 1-14z" class="B"></path><path d="M700 440v-5-1l-1-1v-3l-1-2c0-2 0-2 1-4 2 12 5 22 12 32h-1c-2-3-4-7-6-8-2-2-3-5-4-8z" class="E"></path><defs><linearGradient id="F" x1="694.342" y1="396.468" x2="699.446" y2="410.931" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#858583"></stop></linearGradient></defs><path fill="url(#F)" d="M698 395h4l-2 7-3 3c-1 3-1 6-1 9-2-1-2-1-4 0 1-7 3-13 6-19z"></path><defs><linearGradient id="G" x1="702.881" y1="381.794" x2="702.147" y2="394.707" xlink:href="#B"><stop offset="0" stop-color="#353435"></stop><stop offset="1" stop-color="#626362"></stop></linearGradient></defs><path fill="url(#G)" d="M717 369c1 1 1 1 0 2-6 6-13 15-15 24h-4c4-10 12-19 19-26z"></path><defs><linearGradient id="H" x1="687.467" y1="419.197" x2="702.754" y2="434.047" xlink:href="#B"><stop offset="0" stop-color="#848483"></stop><stop offset="1" stop-color="#c3c2c1"></stop></linearGradient></defs><path fill="url(#H)" d="M692 414c2-1 2-1 4 0v1c0 5-1 9-1 14v1l3 15 1 1c0 1 1 3 1 4v1c-2-2-2-4-3-6-4-11-6-20-5-31z"></path><path d="M265 191c7 0 15 6 19 10 6 6 11 12 14 19 3 6 4 12 6 18l19 55 57 168 96 281 39 114 3 11-4 13-14-43-38-107-86-253-57-167-20-58-11-28c-6-13-13-23-23-33zm180 35c12-7 25-15 29-29 1-5-1-12-4-17-1-1-2-2-4-3l-1-1c2-1 5-2 5-4 0-3 0-4-3-6-4-4-12-4-17-3l-6 1c0-2 1-2 2-2 5-4 14-5 20-3 7 2 13 6 16 12 5 7 5 17 3 25-4 12-13 21-23 28-4 3-9 5-12 9-5 5-9 11-12 17 7-6 14-11 23-15 7-3 16-5 24-9 15-7 26-21 26-38 1-10 0-22-6-30-2-2-6-5-9-5-4 0-6 1-9 3l-1-1c2-3 5-4 9-4 5-1 11 2 15 5 5 4 7 9 9 15 3-8 6-14 13-18 4-2 10-3 15-2 2 1 4 3 5 5h0c-4-2-8-4-12-2-4 1-7 5-9 9-5 10-4 27 0 38 5 12 15 21 27 26 4 2 18 7 20 10h3l5 3c1 1 3 1 4 1l1 1c1 0 2 1 4 0l1-1c-3-3-4-6-7-8l-11-8c-10-7-19-15-23-27-3-9-3-19 2-27 3-6 9-10 15-12 9-3 17 0 25 4-5 0-9-1-14 0-5 0-10 1-13 5-1 1-1 3-1 5l3 3h1l-4 4c-3 4-6 12-4 18 3 14 19 23 30 30l-2-11c-1-7 0-17 3-24 1-2 2-3 4-4l1 1c-1 1-1 2 0 4v1h1c1-1 2-2 3-2 4-5 6-10 7-16 3-1 8-2 12 0 9-3 24-3 33 3h0l-5 1c-3 1-7 2-10 3h1c1 1 2 0 3 0s3 2 3 3h0c-1 0-1 0-2 1l-4 3c7 2 13 6 17 13 2 4 2 10 0 14-1 4-5 8-9 10-5 2-10 2-15 1l4-2a10.85 10.85 0 0 0 8-8c1-2 0-5-1-6-1-3-4-4-7-5h0-3c-2 0-3 1-5 3h-1c-5 6-6 15-5 23 2 16 11 33 18 48 7 14 15 27 13 43 0 6-3 13-5 19 0-7 0-14-1-20-3-18-14-36-27-49-12-12-28-21-43-28-7-3-15-5-22-7l-15-3c-7-2-16-3-20-10l-1-1c-2 0-5 0-7-1v1l-6 6c-4 2-10 4-15 5-11 4-22 6-33 10-10 4-19 9-28 15-18 14-29 32-32 55l-7-21c1-2 2-5 3-7 1-4 3-7 5-10 6-13 12-24 16-37 1-4 3-8 3-12v-1c1-7 0-14-3-21h0c1-1 2 0 2 0 7 1 6 11 9 15 1-1 1-2 1-4-2-4-2-7-5-10l-1-1c-4-2-7-3-11-1-2 0-4 2-5 3s-2 2-2 3c-1 2-1 5 0 8 2 4 6 5 10 7h0c-3 1-5 2-8 2-6-1-10-3-14-7-3-3-4-8-4-13 1-7 5-13 11-17l-5-4c0-1-1-2-2-2l-2-1c1-1 1-1 1-2l1-1c1 0 1 1 2 1 2 1 3 1 4 1h1c-6-3-11-4-17-5 4-2 8-3 13-5 5 0 12-1 17 1 3-1 9 1 12 2v2c2 5 6 11 10 15 1-1 2-3 3-4l-1-1v-1c1 1 2 1 3 3 5 7 5 20 3 28l-2 5 2 1z" class="C"></path><path d="M437 196s0 1 1 1c1 5 1 9 0 14-4-5-1-10-1-15z" class="H"></path><path d="M515 190l1-16 4 11v1c-1 1-1 2-3 2l-1-1c0 1 0 2-1 3z" class="D"></path><path d="M601 195h1c1 3 2 7 3 11l-4 5c-1-6 0-11 0-16z" class="G"></path><path d="M611 204h3l-7 20-1-1 5-19z" class="H"></path><path d="M568 165h0v1c-6 7-8 21-7 30v1h0c-1-3-3-6-3-9 0-10 3-16 10-23z" class="G"></path><path d="M471 165c3 3 6 7 8 11 3 7 2 14-1 21-1 1 0 1-1 1 2-10 0-24-6-33z" class="I"></path><defs><linearGradient id="I" x1="600" y1="242.438" x2="602.899" y2="256.084" xlink:href="#B"><stop offset="0" stop-color="#39383a"></stop><stop offset="1" stop-color="#636362"></stop></linearGradient></defs><path fill="url(#I)" d="M578 237h3l5 3c1 1 3 1 4 1l1 1c1 0 2 1 4 0l1-1 11 11c3 3 7 6 9 9l-26-16c-4-3-9-5-12-8z"></path><path d="M521 212c2 1 6 1 8 3 1 1 1 2 1 4-1 3-5 6-6 8l-1-1c-2 0-5 0-7-1 3-3 2-8 5-13z" class="G"></path><path d="M409 210c0-1 0-2 2-3-2 0-1 0-2 1-3 0-3 0-5-2-2 0-2 0-3 1v-2c1-3 4-4 7-5 4-2 7-2 10 0 4 2 6 4 7 8-4-2-7-3-11-1-2 0-4 2-5 3z" class="D"></path><path d="M632 211c-1 0-1-1-1 0-1 0-3 0-3 1-2 1-4 7-5 10v-6-1l3-6c2-5 6-8 12-10 2-1 4-1 6 0 0 2-1 5-2 6 0 1-1 2-2 3h-3c-2 0-3 1-5 3z" class="E"></path><defs><linearGradient id="J" x1="440.819" y1="243.188" x2="423.181" y2="240.812" xlink:href="#B"><stop offset="0" stop-color="#525352"></stop><stop offset="1" stop-color="#7c7a7a"></stop></linearGradient></defs><path fill="url(#J)" d="M443 225l2 1-1 3c-3 4-7 8-10 12s-5 8-8 12l-10 15-1-1c6-10 12-21 16-32 6-2 9-6 12-10z"></path><defs><linearGradient id="K" x1="509.618" y1="220.412" x2="521.943" y2="188.642" xlink:href="#B"><stop offset="0" stop-color="#a7a6a5"></stop><stop offset="1" stop-color="#d9d8d7"></stop></linearGradient></defs><path fill="url(#K)" d="M520 186c0 4 1 9 0 13 0 4-2 9-2 12 1 1 2 1 3 1-3 5-2 10-5 13v1c-2-4-6-7-9-10 5-8 8-16 8-26 1-1 1-2 1-3l1 1c2 0 2-1 3-2z"></path><defs><linearGradient id="L" x1="609.792" y1="246.423" x2="620.953" y2="241.668" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#939293"></stop></linearGradient></defs><path fill="url(#L)" d="M607 224l1 2c2 0 4-3 6-5 0 6 0 12 1 18 2 6 6 13 9 19l9 15-1 1c-4-7-10-13-15-20-3-5-4-10-7-14-2-2-5-4-7-6l1-2c2-3 2-5 2-9l1 1z"></path><path d="M398 175c5 0 12-1 17 1 3 2 7 5 9 8s1 8 2 11l5 21h0l-3-9c-4-11-11-16-21-20-2-1-4-2-5-2-6-3-11-4-17-5 4-2 8-3 13-5zm226 2c9-3 24-3 33 3h0l-5 1c-3 1-7 2-10 3-11 3-21 9-27 19l-1 1h-3l3-14c1-7 4-9 10-13z" class="K"></path><path d="M611 171l-10-22h138 42c16 0 33-1 48 3 17 4 30 11 42 23 6 6 10 13 14 20l6 15c2 7 5 16 11 21 1 1 2 2 3 2 3 0 5-1 7-3 0-2-1-3-1-5 2 1 2 1 3 2 1 3 1 5-1 7-1 3-4 4-7 4-5 1-10 0-14-4-4-3-8-8-11-13 4 23 0 47-7 70-5 15-12 30-21 43-9 12-26 25-24 42 1 4 2 8 5 10 2 1 4 2 5 2 1 1 5 0 6 1-5 1-10 2-15 2v-1c-3 0-5-2-7-4-1-2-2-5-3-7 1 8 2 16 9 22 6 5 14 6 22 6 4 1 8 1 12 1l8-1c-3 3-8 7-13 7-2 0-4-1-6-2h-1c0 2 0 3 2 4 1 2 4 3 5 4 3 2 5 6 7 8-8-2-16-4-24-9l-1 1c-5-1-15-16-17-20-5-8-7-19-5-29 5-18 19-32 31-46 18-23 29-52 30-81 0-5 1-19-2-23 0 23-1 47-14 67-2 9-25 34-33 38v1c-4 3-9 5-13 7-10 4-24 6-35 2-4-2-8-5-12-7s-9-4-14-5l4-4-10-8 9-1c-2-2-5-4-8-6-3-3-6-6-10-9 1-1 1-3 1-4 1-6-3-13-7-18 13 3 16 22 25 30 11 11 26 15 41 15 20-1 38-11 51-24-7-1-12 0-19-4 1-1 3-1 4-1 3-1 5-2 8-4 11-7 17-18 20-30l-8 9c-9 9-21 16-35 15-10 0-19-4-26-12-2-2-4-5-5-7-2-4-2-9-5-12-4-4-10-3-14-7h1 0c6 1 12 0 16 1 6 2 8 8 10 13 1 2 1 4 3 5 1 1 3 1 5 1 1-2 1-3 1-5v-1 1h1c1 1 2 1 3 1 2 1 5 0 7-2h1c1 2 3 4 5 5 4 2 8 2 12 1 11-3 18-14 24-23 1-2 2-4 2-7 1-4 2-9 2-13 1-7 0-15-2-21-3-11-10-22-20-28v1c-4-2-7-4-10-5-2-1-3-2-4-2h-4-1-1c-2 1-3 0-4 0-2 1-4 0-5 1-1 0-2 1-2 1 13 4 25 14 32 27 6 11 10 24 6 36-2 7-7 15-14 18-5 3-11 4-17 2-3-1-6-3-9-5 6 2 12 3 17 0 2-1 5-4 5-7 1 0 0 0 0-1-2 0-5 2-7 3 2-3 5-8 5-11-1-1-2-1-3-1 2-2 4-4 5-6 2-4 4-8 4-12 1-8-1-17-6-23-1-3-4-5-6-6-2-2-4-4-6-5-8-5-18-7-28-6-17 1-34 11-46 24-6 7-11 15-15 24-5 12-10 25-15 38l-39 114-70 210-20 59-4 12c0 2 0 4-1 6l-5-14-1-3-6-18c-1-2-2-4-2-5 0-3 17-54 20-61l65-198 7-22 7-23c8-27 18-54 20-82 1-15 0-27-10-38l-6-5c-3-3-4-3-8-3l-2-2-1-1-1-1h0c-9-6-24-6-33-3-4-2-9-1-12 0l-1-6z" class="C"></path><path d="M789 157h12l-4 1 3 1h-4-12c2-1 3-2 5-2z" class="J"></path><path d="M766 159c8-1 15-2 23-2-2 0-3 1-5 2l-12 1-6-1z" class="F"></path><path d="M801 157l17 3h-1c-3 2-14 0-17-1l-3-1 4-1z" class="I"></path><path d="M615 158c10 0 19 0 28 1 3 0 6 0 9 1h-7l-1 1h-2c-7-1-15-1-23-2-1-1-2 0-4-1z" class="E"></path><path d="M863 232l1 1c3-2 4-3 6-6h0c0 5 0 11-3 14-2 1-3 2-5 2l-1-1v-3c1-2 2-4 2-7z" class="B"></path><defs><linearGradient id="M" x1="789.521" y1="161.661" x2="768.132" y2="160.349" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#M)" d="M784 159h12-1c-1 1-3 1-5 1v2c1 0 3 1 4 1-9 0-18 0-27 1l-17 2 1-1h1l1-1c1 0 2 1 3 0h2 1c1 0 2-1 3-1h1 2 3l1-1-1-1 4-1 12-1z"></path><path d="M814 261c2 1 4 5 5 7 4 1 8 1 12 0l-2 6-1 1c-2 1-5 1-7 0-3-2-6-5-7-8-1-1-1-4 0-6z" class="B"></path><defs><linearGradient id="N" x1="884.681" y1="211.161" x2="873.527" y2="188.735" xlink:href="#B"><stop offset="0" stop-color="#757473"></stop><stop offset="1" stop-color="#989797"></stop></linearGradient></defs><path fill="url(#N)" d="M866 181h0c4 1 7 5 9 7 5 5 11 17 10 24l-1 1c-5-1-6-13-8-18s-5-10-10-14z"></path><path d="M822 323h1c3 0 5 0 8 1v1c-1 1-1 1-1 2-4 3-9 5-13 7 1-1 1-2 2-2h1c1-1 0-1 2-1 0-1 1-1 2-2h-1c-2 0-3 1-5 1l-12 3h-10c5-1 13-1 17-4 1-1 2-1 3-2-1 0-2 1-3 0h-1l10-4z" class="B"></path><path d="M648 165c-10-2-21-1-32-2 0-1-1-3-1-4v-1c2 1 3 0 4 1 8 1 16 1 23 2l4 1 1-1c2 1 3 1 4 3-1 0-2 0-3 1z" class="J"></path><path d="M812 327c-3 2-8 2-12 2-8 0-17 0-24-4a30.44 30.44 0 0 1-8-8h0c5 4 11 6 18 8 12 2 24 0 36-4v2l-10 4z" class="F"></path><defs><linearGradient id="O" x1="806.024" y1="166.321" x2="792.071" y2="156.545" xlink:href="#B"><stop offset="0" stop-color="#424241"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#O)" d="M796 159h4c3 1 14 3 17 1h1c8 2 18 5 25 10v1c-4-2-7-4-10-5-2-1-3-2-4-2h-4-1-1c-2 1-3 0-4 0-2 1-4 0-5 1-1 0-2 1-2 1-6-2-12-2-18-3-1 0-3-1-4-1v-2c2 0 4 0 5-1h1z"></path><defs><linearGradient id="P" x1="846.703" y1="289.938" x2="835.935" y2="325.604" xlink:href="#B"><stop offset="0" stop-color="#7d7b7b"></stop><stop offset="1" stop-color="#bebebe"></stop></linearGradient></defs><path fill="url(#P)" d="M822 321l12-6c7-5 12-10 18-16l11-11c-2 9-25 34-33 38v1c0-1 0-1 1-2v-1c-3-1-5-1-8-1h-1v-2z"></path><defs><linearGradient id="Q" x1="706.966" y1="158.785" x2="707.179" y2="169.576" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#Q)" d="M652 160c1 0 4 1 6 1l11 2c11 1 22 3 33 4 12 0 23 0 35-2l29-6 6 1-4 1 1 1-1 1h-3-2-1c-1 0-2 1-3 1h-1-2c-1 1-2 0-3 0l-1 1h-1l-1 1h-2l-8 2c-20 3-41 3-61 1l-31-4c1-1 2-1 3-1-1-2-2-2-4-3l-1 1-4-1h2l1-1h7z"></path><defs><linearGradient id="R" x1="682.679" y1="176.243" x2="631.417" y2="206.379" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#b1b0af"></stop></linearGradient></defs><path fill="url(#R)" d="M611 171c5-2 11-2 16-2 17 0 35 2 50 10 9 5 19 14 21 24 1 3 2 9 0 12l-2 3c-2-9-4-18-11-23-3-1-8-3-10-3l-6-5c-3-3-4-3-8-3l-2-2-1-1-1-1h0c-9-6-24-6-33-3-4-2-9-1-12 0l-1-6z"></path><path d="M574 639h0c1-2 1-5 2-7 1-4 2-7 3-10l17-53 67-202 34-102v1c0 1-1 4-2 6l-6 18-15 48-70 216-18 54c-3 11-6 22-11 32v2l-1-3z" class="B"></path><path d="M580 656c1-2 1-4 1-6l4-12 20-59 70-210 39-114 15-38c4-9 9-17 15-24 12-13 29-23 46-24 10-1 20 1 28 6 2 1 4 3 6 5 2 1 5 3 6 6 5 6 7 15 6 23 0 4-2 8-4 12l-1-1 1-1h0c0-1 1-2 1-2 1-1 1-2 1-3h1v-2-1c0-1 0-1 1-2 0-2 0-6-1-7v-4l-1-1v-2l-1-1v3c1 3 1 8 1 11l-1 1v1 2l-1 1c-1 0-2 0-2-1-1-1-1-1-1-2l-1-1v-1c-1-1-1-1-2-3l1-1v-1l1-1v-1-8l-1-1v-2l-1-1-1-1v-1c-2-2-4-4-6-5l-1-1-4-2c0-1-2-1-2-1l-2-1h-1c-1-1-1-1-2-1h-1-1c-1-1-2-1-2-1h-2c-1 0-9 0-10 1h-3c-1 0-2 0-3 1-1 0-2 0-4 1h-1c-3 1-6 3-9 4-2 1-4 2-5 4-1 0-2 1-2 1-4 2-6 5-9 8-2 2-4 3-5 5 0 1-1 2-2 3l-1 1-3 4v1l-3 4-1 2c-1 2-5 8-5 11v1h-1c0 1-1 2-1 2v1c0 1-1 1-1 2s1 0 0 1l-1 1v2h0l-1 1h0v1l-1 1v2c-1 1-1 0-1 1l-1 2v1l-1 1v1l-1 2v1h0c-1 1-1 1-1 2l-1 2v1 1l-1 1h0v1c-1 1-1 2-1 2 0 1-1 1-1 3v1h0l-1 2v1h-1v1 1c-1 0-1 0-1 1v1 1c-1 1-1 1-1 2-1 1-1 1-1 2 0 2 1-2 0 1l-1 1c0 1 0 2-1 3v1h0v1h-1v2 1h-1v1 1h0v1c-1 1-1 1-1 2v1h-1v1h0c-1 1-1 2-1 3v1c-1 1-1 1-1 2v1c-1 1-1 1-1 2v1l-1 2c-1 1-1 1-1 2v2l-1 1v2l-1 1v2l-1 1v2l-1 1v2l-1 1v2l-1 1v2c0 1-1 0-1 1v1 1h-1v2 1l-1 1v1h0c0 1-1 1-1 2v1 1h-1v1h0c-1 1-1 2-1 3v1c-1 0-1 1-1 2v1c-1 0-1 1-1 2v1l-1 1-1 4c0 1 0 2-1 3s-1 3-1 4c-1 1-1 1-1 2v1c-1 0-1 1-1 2v1 1c-1 1-1 1-1 2s-1 1-1 2v1h0v1c-1 1-1 1-1 2v1h-1v1 1 1h-1v1h0c-1 1-1 2-1 3v1c-1 1-1 1-1 2v1c-1 1-1 1-1 2v1c-1 0-1 1-1 2v1c-1 1-1 1-1 2v1c-1 1-1 1-1 2v1c-1 1-1 1-1 2s-1 2-1 3v1l-1 1v1h0c0 2-1 5-2 6h0v1 1h-1v1h0c-1 1-1 1-1 2s-1 2-1 4v1c-1 0-1 1-1 2v1l-1 1-1 4-1 3h0v1l-1 3c-1 1-1 1-1 2s-1 2-1 3v1c-1 0-1 1-1 2v1c-1 1-1 1-1 2v1l-3 6v2 1c-1 0-1 1-1 2v1c-1 0-1 1-1 2v1l-1 1v1c0 1 0 2-1 3v1c-1 1 0 0-1 2v1c-1 1 0 0-1 2 0 1-1 2-1 3v1c-1 3-3 7-4 10v1c0 1-1 2-1 3v1l-1 1v1h0c-1 1-1 1-1 2h0v2l-1 1v1h0v1l-1 1v1h0v1l-1 1v1h0v1l-1 1v1h0v1l-1 1v1h0v1l-1 1v1h0v1c-1 1-1 1-1 2s0 1-1 2v1 1c-1 0-1 1-1 1v2c-1 1-2 3-2 4s0 2-1 3v1h0v1l-1 1v1c-1 1-1 1-1 2v2h-1v2c-1 1-1 2-1 3v1c-1 1-1 0-1 1-1 1-1 4-2 5v2 1h-1v2 1c-1 0-1 1-1 1v2c-1 0-1 1-1 2h0v1c-1 1-1 2-1 3-1 1-1 3-2 5h0c-1 1-1 1-1 2h0c-1 1-1 2-1 3v1l-1 1v1 1 1h0l-1 1v1h0v1l-1 1v1h0v1l-1 1v1 1h0l-1 1v1l-1 1v1 2l-1 1v1h0l-1 1v1 2l-1 1v2l-1 1v2l-1 1v1 1c-1 1-1 0-1 1v2c-1 0-1 1-1 1v2c0 1 0 0-1 2h0c0 1 0 1-1 2v2c-1 1-1 1-1 2v1c-1 1-1 2-1 2v1c-1 0-1 1-1 2s0-1 0 1l-1 1v2 1c-1 1-1 1-1 2v1h-1v1 1 1h-1v1 1 1h-1v1 1 1h-1v1 1 1h-1v1 2c-1 0-1 1-1 1v2l-1 1v1c-1 1 0 1-1 1v1c-1 1-1 1-1 2s1 1 1 2v1l3 5h0l2 3v1l1 1v1h-1c-8-14-13-29-18-44l-16-49-26-75c0-1 0-1 1-2l-1-5c-1-1-2-3-2-4l-3-7-5-12c1-4 5-8 7-12v-1l-1 2-1 1c-1 1-1 2-1 2l-2 2s-1 1-1 2l-1 1v1l-2 2c-1-2-1-4-2-5s0-1-1-2c0-2-1-3-1-4l-1-1v1l-6-12-11-23c-16-30-37-63-33-99 1-14 8-29 20-39 10-8 23-13 37-11 0-2 0-4 1-5 0-1 2-2 2-3l-1-1c-11-8-28-11-41-10h-1c-5 1-10 2-15 4-16 7-27 19-34 35-14 36-3 75 12 109l23 51c13 29 25 61 29 92 2 10 2 19 2 29v13h0c-2-3-4-9-5-13l-13-38-61-177-14-45-6-19-10-25-14-40c-4-12-8-24-8-37 1-11 4-19 12-27 6-1 15 3 20 6l5 4c-6 4-10 10-11 17 0 5 1 10 4 13 4 4 8 6 14 7 3 0 5-1 8-2h0c-4-2-8-3-10-7-1-3-1-6 0-8 0-1 1-2 2-3s3-3 5-3c4-2 7-1 11 1l1 1c3 3 3 6 5 10 0 2 0 3-1 4-3-4-2-14-9-15 0 0-1-1-2 0h0c3 7 4 14 3 21v1c0 4-2 8-3 12-4 13-10 24-16 37-2 3-4 6-5 10-1 2-2 5-3 7l7 21c3-23 14-41 32-55 9-6 18-11 28-15 11-4 22-6 33-10 5-1 11-3 15-5l6-6v-1c2 1 5 1 7 1l1 1c4 7 13 8 20 10l15 3c7 2 15 4 22 7 15 7 31 16 43 28 13 13 24 31 27 49 1 6 1 13 1 20 2-6 5-13 5-19 2-16-6-29-13-43-7-15-16-32-18-48-1-8 0-17 5-23h1c2-2 3-3 5-3h3 0c3 1 6 2 7 5 1 1 2 4 1 6a10.85 10.85 0 0 1-8 8l-4 2c5 1 10 1 15-1 4-2 8-6 9-10 2-4 2-10 0-14-4-7-10-11-17-13l4-3c1-1 1-1 2-1h0c0-1-2-3-3-3s-2 1-3 0h-1c3-1 7-2 10-3l5-1 1 1 1 1 2 2c4 0 5 0 8 3l6 5c10 11 11 23 10 38-2 28-12 55-20 82l-7 23-7 22-65 198c-3 7-20 58-20 61 0 1 1 3 2 5l6 18 1 3 5 14z" class="K"></path><path d="M394 257h2c1 0 1 0 2 2 0 1 0 2-1 3h-2c-1 0-1-1-2-2 0-1 0-2 1-3z" class="C"></path><path d="M652 181l5-1 1 1 1 1 2 2-7 2v-1c0-2-1-3-2-4z" class="B"></path><path d="M642 184c3-1 7-2 10-3 1 1 2 2 2 4v1l-7 2c1-1 1-1 2-1h0c0-1-2-3-3-3s-2 1-3 0h-1z" class="D"></path><path d="M393 267v1c-1 6-3 10 1 16 1 1 2 2 3 4 0 1 0 1-1 2h-2-1c-1 0-3-3-3-5-3-6 1-12 3-18zm267 11c4 5 1 14 2 19 0 3 2 6 2 9-1 2-2 3-3 4h-1c-5-7-1-16 0-23 1-3 0-6 0-9z" class="C"></path><defs><linearGradient id="S" x1="648.446" y1="395.54" x2="729.04" y2="432.334" xlink:href="#B"><stop offset="0" stop-color="#b2b1b0"></stop><stop offset="1" stop-color="#e1e0e0"></stop></linearGradient></defs><path fill="url(#S)" d="M580 656c1-2 1-4 1-6l4-12 20-59 70-210 39-114 15-38c4-9 9-17 15-24 12-13 29-23 46-24 10-1 20 1 28 6 2 1 4 3 6 5 2 1 5 3 6 6 3 8 4 18 2 27l-5-8h0c2-4 2-10 1-14-3-7-10-12-16-14-12-5-26-3-37 2-10 5-19 12-26 21-10 13-16 28-22 42l-18 49-125 375-4-10z"></path><path d="M461 273l-1 1c-1 0-2 1-3 1-10 4-15 14-23 20 14-24 37-44 64-52 7-2 13-3 20-4 18-1 38 4 54 13 22 14 40 34 47 60 11 42-8 85-29 121l-58 84h0l-2-4-1-5c-1-1-2-3-2-4l-3-7-5-12c1-4 5-8 7-12l35-48c6-10 11-19 15-30 10-25 16-51 9-77-5-21-18-40-36-51-17-11-39-17-60-12-10 3-23 9-28 18z" class="C"></path><path d="M557 265c-8-4-16-8-25-11-3-1-7-2-10-2l-8-2h0 0c14 1 32 5 43 12v1h-1v1c1 0 1 0 1 1z" class="E"></path><path d="M571 266c-17-12-36-18-57-19 4-1 9-1 13-1 13 1 26 5 37 11 3 2 7 4 9 7h0l3 3v1l-1-1c-1-1-2-2-3-2l-1 1z" class="B"></path><defs><linearGradient id="T" x1="524.663" y1="501.385" x2="550.718" y2="468.129" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#a2a2a2"></stop></linearGradient></defs><path fill="url(#T)" d="M546 466c2 1 3 0 4-1-4 8-10 14-15 22-3 7-5 14-6 21-1-1-2-3-2-4l-3-7c1-1 1-1 1-2s0-2 1-3l1-1c2-4 6-7 9-11 3-5 6-9 10-14z"></path><defs><linearGradient id="U" x1="596.83" y1="311.819" x2="577.74" y2="318.648" xlink:href="#B"><stop offset="0" stop-color="#8c8c8c"></stop><stop offset="1" stop-color="#b3b2b1"></stop></linearGradient></defs><path fill="url(#U)" d="M557 262c20 11 35 29 41 51 7 23 1 48-8 70-1-1 0-3 0-5 2-5 3-10 4-15 2-12 4-25 3-37-3-22-15-41-32-55l-8-6c0-1 0-1-1-1v-1h1v-1z"></path><defs><linearGradient id="V" x1="597.809" y1="381.132" x2="540.094" y2="357.531" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#a1a09f"></stop></linearGradient></defs><path fill="url(#V)" d="M573 264c17 10 29 30 34 50 3 11 3 23 1 34-4 29-18 56-34 81l-24 36c-1 1-2 2-4 1 23-34 48-69 57-110 3-11 4-23 2-34-3-23-16-42-34-56l1-1c1 0 2 1 3 2l1 1v-1l-3-3h0z"></path><defs><linearGradient id="W" x1="515.403" y1="252.712" x2="537.684" y2="481.99" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#353635"></stop></linearGradient></defs><path fill="url(#W)" d="M461 273c5-9 18-15 28-18 21-5 43 1 60 12 18 11 31 30 36 51 7 26 1 52-9 77-4 11-9 20-15 30l-35 48v-1l-1 2-1 1c-1 1-1 2-1 2l-2 2s-1 1-1 2l-1 1v1l-2 2c-1-2-1-4-2-5s0-1-1-2c0-2-1-3-1-4l-1-1v1l-6-12-11-23c-16-30-37-63-33-99 1-14 8-29 20-39 10-8 23-13 37-11 0-2 0-4 1-5 0-1 2-2 2-3l-1-1c-11-8-28-11-41-10h-1c-5 1-10 2-15 4l-3-2z"></path><path d="M548 363h2c1 1 2 1 2 2 0 2 0 2-2 4h-1c-2-1-2-1-3-2 0-2 1-3 2-4zm-64-5c1-1 2-1 3 0s2 1 2 3c0 1-1 2-2 3h-3c-1-1-2-2-2-3 0-2 1-2 2-3z" class="D"></path><path d="M523 276c8 0 15 0 23 1s15 4 20 10c5 8 5 16 3 24-2 10-8 21-17 26-6 3-11 4-17 5-3 1-8 1-11 3-2 2-2 4-2 6-1 4-1 8-1 11s0 6 1 9 3 6 5 8c-2-1-3-1-5-2-4-4-6-10-6-15-1-5-1-10-3-14s-7-5-11-7c-3 4-7 6-11 9 0-5 0-10-1-15 0-3-2-6-3-8-3-6-5-13-4-19 1-3 0-4 2-6 5-3 10-6 15-7 1-1 3-2 5-2-1 2-1 3-2 5-1 3-1 7 1 10 2 2 5 3 8 4 4 0 9-1 12-3 4-3 7-7 7-12 1-8-3-15-8-21z" class="C"></path><defs><linearGradient id="X" x1="543.474" y1="290.09" x2="554.09" y2="318.967" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#cececd"></stop></linearGradient></defs><path fill="url(#X)" d="M541 290h2c5 1 8 5 10 9 3 5 4 13 3 19-1 1-1 1-1 2s0 2-1 4h-1v-1l-1 1-1 1h0c0-1 1-2 2-4 1-4 0-10-2-14s-6-7-11-8h-1c1-3 1-6 2-9z"></path><defs><linearGradient id="Y" x1="502.163" y1="330.281" x2="521.24" y2="311.798" xlink:href="#B"><stop offset="0" stop-color="#bdbcbb"></stop><stop offset="1" stop-color="#eaeae9"></stop></linearGradient></defs><path fill="url(#Y)" d="M491 309h1c2 1 6 7 8 9 1 1 2 3 4 4 3 2 6 3 9 4h1c3 1 7 1 10 1 3-1 9-3 12-2v2c0 3-2 5-5 7s-5 2-8 3l2 1h-1c-4 2-8 1-12-1-8-2-15-10-19-17-1-3-3-7-2-11z"></path><path d="M162 293c0-1-3-5-4-6-2-4-4-8-6-13-7-18-7-37-7-56h-1c-1 6-1 12-1 18 0 30 9 60 27 85 12 15 25 28 32 46 4 9 4 18 0 27-3 7-7 13-12 19-2 3-4 6-8 7l-1-1c-8 4-16 7-24 9 1-3 3-5 5-7 3-2 5-3 7-5 2-1 1-2 1-4h0c-2 1-4 2-6 2-5 0-10-4-14-7l11 1c3 1 6 0 9 0 9-1 17-2 24-8 7-7 8-13 8-22l-1 3c-1 3-2 5-4 7-3 3-7 3-11 3-2 0-6-1-7-2 3 0 6 0 9-2s5-6 5-9c2-18-13-30-23-43s-17-30-22-46c-7-23-10-44-7-68-5 6-10 14-18 16-3 2-8 2-12 0-2-1-4-3-4-5s0-4 1-5c0-1 2-2 3-2-1 1-1 2-1 3s0 2 1 3 2 2 4 2c10 0 15-19 17-26 2-4 3-8 5-12 4-7 9-14 14-20 11-10 23-19 38-22 17-4 35-4 52-4h43 154l-10 23c-1 2-1 5-1 8v-2c-3-1-9-3-12-2-5-2-12-1-17-1-5 2-9 3-13 5 6 1 11 2 17 5h-1c-1 0-2 0-4-1-1 0-1-1-2-1l-1 1c0 1 0 1-1 2l2 1c1 0 2 1 2 2-5-3-14-7-20-6-8 8-11 16-12 27 0 13 4 25 8 37l14 40 10 25 6 19 14 45 61 177 13 38c1 4 3 10 5 13h0v-13c0-10 0-19-2-29-4-31-16-63-29-92l-23-51c-15-34-26-73-12-109 7-16 18-28 34-35 5-2 10-3 15-4h1c13-1 30 2 41 10l1 1c0 1-2 2-2 3-1 1-1 3-1 5-14-2-27 3-37 11-12 10-19 25-20 39-4 36 17 69 33 99l11 23 6 12v-1l1 1c0 1 1 2 1 4 1 1 0 1 1 2s1 3 2 5l2-2v-1l1-1c0-1 1-2 1-2l2-2s0-1 1-2l1-1 1-2v1c-2 4-6 8-7 12l5 12 3 7c0 1 1 3 2 4l1 5c-1 1-1 1-1 2l26 75 16 49c5 15 10 30 18 44 7 14 20 29 35 34 5 2 9 3 14 4l-6 3c-6 3-12 7-16 12-3 5-5 10-4 15 3 11 11 20 20 25 4 3 8 4 13 6h1c8 2 19 1 27-4 5-2 8-7 10-12v-1c2-4 2-10-1-14-1-2-3-3-6-5-3 0-6 0-9 3-2 2-2 5-2 8v1c-2 0-5-1-7-3s-3-6-2-8c0-5 3-8 6-10 4-3 9-4 14-3 5 0 10 4 13 8 5 8 5 18 3 26l-2 4h0c-3 9-10 16-18 20-13 7-29 7-42 2-5-1-9-3-14-6h0c-6-4-13-9-18-15-13-13-23-27-31-44l-3-7c-3 20-11 39-17 58l-29 87-3-11-39-114-96-281-57-168-19-55c-2-6-3-12-6-18-3-7-8-13-14-19-4-4-12-10-19-10-7-6-14-9-22-13v-1c-2-1-4-2-7-2l1-1v-2c3-4 5 0 8-1h1l-12-3c-11-2-23 2-32 8a30.44 30.44 0 0 0-8 8c-1 1-2 2-2 3-5 6-7 15-6 23 1 4 2 9 5 12l3 3 1 1v1h-2l-1 1c1 4 3 7 6 11-3-1-6-2-8-3h0c1 4 3 6 6 8 5 3 11 2 16 0-3 2-5 3-9 5h-2 0v1h-1c0 1 0 2 2 3 0 0 1 1 1 2l-1 1c2 0 3 1 4 2 1 0 2 1 4 1 1-1 3-2 5-3 0 3-1 4 1 6 2 0 3 0 4-2 5-6 5-16 14-17 6-1 10 0 16-2-1 2-2 2-4 3-4 1-9 2-12 7-1 2-1 6-2 9-4 9-14 16-23 19-11 3-22 1-32-5-8-4-15-11-21-18 4 12 10 24 22 30l12 5c-7 3-13 3-20 4 13 14 30 22 49 23 16 2 31-3 43-14 8-7 10-19 18-26 2-2 4-3 7-4-4 6-8 12-7 19l1 3c-3 3-8 8-12 10-2 2-5 3-7 5l10 1-11 8c2 1 3 2 4 3-1 2-3 2-5 3-7 2-13 7-20 10-3 1-7 2-10 2-6 0-14 0-20-2s-13-5-18-9c-9-5-17-16-23-24-3-3-5-7-7-10z" class="C"></path><path d="M527 504c0 1 1 3 2 4l1 5c-1 1-1 1-1 2l-3-7c1-1 1-3 1-4z" class="G"></path><path d="M205 160c4-2 10-2 15-3l-3 1h1c1 0 2 1 3 1h1l-7 1h-2c-3 0-5 1-8 0z" class="I"></path><path d="M201 274c-2 2-5 2-7 2v-1-3-1-1h7l-1 4h1z" class="D"></path><path d="M201 270c2-2 4-4 5-6l2-2h1c0 2 0 4-1 6-2 3-5 5-7 6h-1l1-4z" class="B"></path><path d="M196 320l15 5c1 0 2 1 3 1 1 1 2 1 4 2h0-1v1c-3 0-6-1-9-2-1 0-2-1-3-1h0l-3-3c-2 0-4-1-6-2v-1z" class="E"></path><path d="M665 757c-1 0-2-2-2-2-1-2-2-5-1-7 1-3 3-5 6-6 2-1 2 1 4 2l2 2h2c-3 0-6 0-9 3-2 2-2 5-2 8z" class="J"></path><path d="M196 162l9-2c3 1 5 0 8 0h2l5 1c1 0 2 0 3 1v1h-1c-1 0-1 0-2 1h-2l-10 3c1-1 2-2 3-2 0-1 1-1 2-1l-1-1h-1c-1 0-2 1-3 0h-6c-2 0-4 0-6-1z" class="G"></path><path d="M202 163c5-1 10-2 15-1 1 1 1 1 1 2l-10 3c1-1 2-2 3-2 0-1 1-1 2-1l-1-1h-1c-1 0-2 1-3 0h-6z" class="H"></path><path d="M161 238c-1 1-1 2-1 3 1 1 0 1 0 2-2 0-3 0-4-1-4-3-4-12-5-17 3 3 6 6 7 10h2c0 1 1 2 1 3z" class="B"></path><path d="M512 474v-1l1 1c0 1 1 2 1 4 1 1 0 1 1 2s1 3 2 5l2-2v-1l1-1c0-1 1-2 1-2l2-2s0-1 1-2l1-1 1-2v1c-2 4-6 8-7 12l5 12 3 7c0 1 0 3-1 4l-14-34z" class="H"></path><path d="M503 597v1c1 2 1 4 1 6 2 1 2 1 4 1v4l1-4v17c0 6 1 11 1 16h-1v-2l1-1c-1-2-1-4-1-5v-3-2-1h-2c0 1-1 2 0 3v1h0c1 1 0 2 1 3v3 4c-3-14-4-27-5-41z" class="B"></path><path d="M211 325c14 2 30 1 42-7l4-4c-1 4-10 10-14 12-8 3-17 4-26 3v-1h1 0c-2-1-3-1-4-2-1 0-2-1-3-1z" class="F"></path><defs><linearGradient id="Z" x1="426.447" y1="171.855" x2="404.512" y2="173.649" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#b6b6b6"></stop></linearGradient></defs><path fill="url(#Z)" d="M397 171l31 1c-1 2-1 5-1 8v-2c-3-1-9-3-12-2-5-2-12-1-17-1v-1h3 3l1-2c-3 0-5-1-7 0h-1v-1z"></path><defs><linearGradient id="a" x1="133.904" y1="204.691" x2="153.32" y2="192.67" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#a0a1a0"></stop></linearGradient></defs><path fill="url(#a)" d="M157 180h0c-5 4-9 9-11 15-1 5-2 9-4 13-1 2-2 4-4 5l-1-1v-5c2-8 7-17 13-23 2-1 4-3 7-4z"></path><path d="M192 327c0-2-1-2-2-3l-1-1c1-1 2-1 3-2l1-1c1 0 2 1 3 1 2 1 4 2 6 2l3 3h0c1 0 2 1 3 1h-3-1 1c0 1 0 1 1 1 1 3 8 3 11 4 2 1 3 1 4 1-4 0-9-1-13-2h-3 0c-1 0-1 0-2-1h-1-1c-1 0 0 0-1 1h1c1 0 1 0 2 1v-1l6 3h0l1 2c-6-2-13-5-18-9z" class="B"></path><path d="M434 377h1c14 20 28 40 38 62h-1c-4-5-7-11-10-17-8-11-15-22-22-33l-6-12z" class="F"></path><defs><linearGradient id="b" x1="523.233" y1="765.817" x2="545.267" y2="760.183" xlink:href="#B"><stop offset="0" stop-color="#a3a2a0"></stop><stop offset="1" stop-color="#d0cfd0"></stop></linearGradient></defs><path fill="url(#b)" d="M526 779l19-48c1 1 1 1 1 2l-19 55c-2-2 0-6 0-9h-1z"></path><defs><linearGradient id="c" x1="164.934" y1="297.095" x2="190.851" y2="321.735" xlink:href="#B"><stop offset="0" stop-color="#7a7978"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#c)" d="M162 293c7 4 13 12 19 18 3 2 6 3 8 5 2 1 6 2 7 4v1c-1 0-2-1-3-1l-1 1c-1 1-2 1-3 2l1 1c1 1 2 1 2 3-9-5-17-16-23-24-3-3-5-7-7-10z"></path><path d="M616 736c-6-1-12 0-17 0 3-5 6-9 12-10 3-1 5-1 8 0 3 0 5 0 8-1 2-1 3-1 5-1-6 3-12 7-16 12z" class="B"></path><path d="M526 779h1c0 3-2 7 0 9-2 6-2 13-7 18h-3c-5-3-8-15-9-20h0l1 2 2-2-1-1v-1l3 3c1 1 2 1 4 0 4 0 7-5 9-8z" class="I"></path><defs><linearGradient id="d" x1="452.034" y1="321.166" x2="467.806" y2="327.472" xlink:href="#B"><stop offset="0" stop-color="#acacab"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#d)" d="M487 288c1 0 1-1 2 0-3 2-7 3-10 5-8 5-15 12-19 20-10 19-8 42-1 62h-1l-1-1c-2-6-5-13-6-20-2-14-2-30 5-42 4-9 13-17 22-21 3-2 6-2 9-3z"></path><path d="M373 183c-2 1-5 2-7 3-4 2-6 5-10 6l-1-1c1-2 2-4 4-6 9-10 25-13 38-14v1h1c2-1 4 0 7 0l-1 2h-3-3v1c-5 2-9 3-13 5 6 1 11 2 17 5h-1c-1 0-2 0-4-1-1 0-1-1-2-1l-1 1c0 1 0 1-1 2l2 1c1 0 2 1 2 2-5-3-14-7-20-6-2 0-2-1-4 0z" class="B"></path><defs><linearGradient id="e" x1="636.694" y1="798.186" x2="650.796" y2="772.558" xlink:href="#B"><stop offset="0" stop-color="#888"></stop><stop offset="1" stop-color="#cac8c8"></stop></linearGradient></defs><path fill="url(#e)" d="M613 775c9 7 17 12 28 14 10 2 22 1 31-6h1l1-1c2-2 7-5 7-8v-1h1v-1h1c0 1 0 2-1 4h-1c0 1-2 3-3 4l-2 2c-1 0 0 0-1 1l-2 1v1l2 2v2h1l1-1h1c3-3 7-7 9-11l1-1 1-2c0-1 0-1 1-2-3 9-10 16-18 20-13 7-29 7-42 2-5-1-9-3-14-6h0v-1c0-1-1-1-2-1h0c0-3 0-5 1-8-1-1-1-2-2-3z"></path><defs><linearGradient id="f" x1="607.868" y1="760.986" x2="588.697" y2="768.685" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#c7c6c5"></stop></linearGradient></defs><path fill="url(#f)" d="M571 722c1 1 2 2 2 4l14 20c1 1 2 3 4 4h0c3 3 5 6 7 8 3 5 6 9 10 13 1 0 5 4 5 4 1 1 1 2 2 3-1 3-1 5-1 8h0c1 0 2 0 2 1v1c-6-4-13-9-18-15-13-13-23-27-31-44 3 2 2 3 4 5v1s0 1 1 2l1 1 2 4c0 1 1 2 1 3 1 1 2 3 3 4s3 3 3 5h1v-1l-1-4c0-1-1-2-1-3v-1c-1 0-1-1-2-2v-1l-1-1v1l-1-1-1-2c-1-1-1-1-1-2v-1l-1-2-1-1v-2-1l-1-1c-1-2-1-5-1-7z"></path><path d="M430 559v-1c1 1 1 3 2 4v-1-1l-1-1c-1-2 1 1 0-2-1-1-1 0-1-1v-1c-1-1-1-2-1-3-1-1-1-2-1-4h-1 1l61 175 16 46 4 11c0 1 1 3 1 4v1l1 1-2 2-1-2h0c-4-8-6-18-9-27l-22-62-33-99c-5-13-10-26-14-39z" class="F"></path><path d="M430 559c-1-1-1-4-2-5l-4-12-21-62-54-158-29-90c0-1-1-3-1-5l109 321h-1 1c0 2 0 3 1 4 0 1 0 2 1 3v1c0 1 0 0 1 1 1 3-1 0 0 2l1 1v1 1c-1-1-1-3-2-4v1z" class="E"></path><path d="M220 157h10l196 2c0 1-1 2-1 3-3 2-10 1-13 1h-31-92-23c-3 0-7 1-9 0l-37 1c1-1 1-1 2-1h1v-1c-1-1-2-1-3-1l-5-1 7-1h-1c-1 0-2-1-3-1h-1l3-1z" class="F"></path><path d="M220 157h10c3 2 6 1 9 1 1 0 2 1 3 1 2 0 3-1 5 0h-8c-1 1-4 0-6 0h-11-1c-1 0-2-1-3-1h-1l3-1z" class="E"></path><defs><linearGradient id="g" x1="226.456" y1="163.658" x2="229.731" y2="158.806" xlink:href="#B"><stop offset="0" stop-color="#5c5c5b"></stop><stop offset="1" stop-color="#737171"></stop></linearGradient></defs><path fill="url(#g)" d="M222 159h11c2 0 5 1 6 0h13v1c-1 1-6 1-8 0h-11-1l2 2h10c1 0 2 0 3 1 0 0 2-1 3-1 2 0 5 0 7 1l-37 1c1-1 1-1 2-1h1v-1c-1-1-2-1-3-1l-5-1 7-1z"></path><defs><linearGradient id="h" x1="493.36" y1="515.171" x2="473.781" y2="522.424" xlink:href="#B"><stop offset="0" stop-color="#828282"></stop><stop offset="1" stop-color="#c8c7c6"></stop></linearGradient></defs><path fill="url(#h)" d="M503 597c-1-12-1-24-3-36-5-35-19-69-34-100l-14-32c-1-3-3-5-4-8h0c17 28 32 56 44 87 6 15 11 32 14 48 2 17 2 33 3 49l-1 4v-4c-2 0-2 0-4-1 0-2 0-4-1-6v-1z"></path><defs><linearGradient id="i" x1="499.279" y1="516.603" x2="548.234" y2="499.588" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#i)" d="M571 722l-6-18-11-42c-7-26-13-52-22-77l-27-73c-7-21-13-43-21-63-13-30-31-57-39-88-5-16-5-32 0-47 4-11 13-22 24-27 5-2 10-3 15-4-3 1-5 2-8 3-14 7-24 16-29 32-4 15-2 31 2 46 5 18 14 34 22 50s15 32 21 48l26 80c8 21 18 42 25 64 13 44 19 92 41 132l7 12h0c-2-1-3-3-4-4l-14-20c0-2-1-3-2-4z"></path><defs><linearGradient id="j" x1="569.259" y1="697.647" x2="466.336" y2="741.741" xlink:href="#B"><stop offset="0" stop-color="#9f9f9e"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#j)" d="M518 843h1l7-24 14-42 11-33c2-6 6-14 6-21s-3-16-5-23l-29-102-8-26c-1-2-3-6-2-8l21 54 30 104c-3 20-11 39-17 58l-29 87-3-11 1-1c1-3 3-9 2-12z"></path><path d="M196 162c2 1 4 1 6 1h6c1 1 2 0 3 0h1l1 1c-1 0-2 0-2 1-1 0-2 1-3 2-16 6-27 18-34 34-4 10-5 21-1 32 3 6 8 12 14 15 5 2 9 2 14 1v1h-1c0 1 0 2 2 3 0 0 1 1 1 2l-1 1c-1 2-3 4-6 6-4 2-10 1-14-1-9-4-17-15-21-23 0-1-1-2-1-3-4-16-4-34 4-48 7-13 19-21 32-25z" class="K"></path><path d="M373 183c2-1 2 0 4 0-8 8-11 16-12 27 0 13 4 25 8 37l14 40 10 25 6 19 14 45 61 177 13 38c1 4 3 10 5 13h0c1 10 4 20 8 29 1 3 2 5 2 8 1 1 1 2 1 3v2c1 1 1 1 1 2v2c1 1 1 1 1 2v2l1 1v1 1s1 1 1 2c0 2 1 4 2 6v2c1 1 1 1 1 2v1s1 1 1 2v2c1 5 3 9 4 14l11 37 1 4v1l-84-234-41-120-30-88-11-32c-3-8-6-16-7-24-3-10-3-21 0-31 2-5 6-12 12-15h1c1-1 2-1 2-3z" class="D"></path><path d="M243 178v-1c-2-1-4-2-7-2l1-1v-2c3-4 5 0 8-1h1c20 6 43 16 54 35 3 5 5 11 7 16l7 22 16 47 50 147 85 248 28 83 25 74c1 3-1 9-2 12l-1 1-39-114-96-281-57-168-19-55c-2-6-3-12-6-18-3-7-8-13-14-19-4-4-12-10-19-10-7-6-14-9-22-13z" class="J"></path></svg>