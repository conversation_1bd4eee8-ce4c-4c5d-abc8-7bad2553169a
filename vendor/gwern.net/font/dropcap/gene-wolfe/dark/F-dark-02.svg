<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="60 45 496 628"><!--oldViewBox="0 0 620 752"--><style>.B{fill:#373633}.C{fill:#2f2d2a}.D{fill:#444443}.E{fill:#423e38}.F{fill:#32312f}.G{fill:#e7c688}.H{fill:#ecd49b}.I{fill:#cea864}.J{fill:#262525}.K{fill:#faebbe}.L{fill:#212121}.M{fill:#292927}.N{fill:#4e4e4d}.O{fill:#f3dca6}.P{fill:#d0a862}.Q{fill:#595855}.R{fill:#957c52}.S{fill:#1d1d1d}.T{fill:#d7b677}.U{fill:#e4c078}.V{fill:#c09a58}.W{fill:#4a4949}.X{fill:#3a3a39}.Y{fill:#181817}.Z{fill:#d6af68}.a{fill:#e8c785}.b{fill:#f1dda9}.c{fill:#d0ab68}.d{fill:#a4834c}.e{fill:#b18c4f}.f{fill:#e8c57e}.g{fill:#f9e3af}.h{fill:#f9edc5}.i{fill:#eccf93}.j{fill:#eed49b}.k{fill:#bd9a5e}.l{fill:#2e2b24}.m{fill:#555553}.n{fill:#f8eec6}.o{fill:#f0cf8d}.p{fill:#fbe7b4}.q{fill:#816d4c}.r{fill:#fbf1ce}.s{fill:#c5a15e}.t{fill:#735c3a}.u{fill:#4d3d26}.v{fill:#fbf5d8}.w{fill:#646362}.x{fill:#856a3d}.y{fill:#624d2e}.z{fill:#5a492f}.AA{fill:#aa8c59}.AB{fill:#faf3d3}.AC{fill:#f4dba3}.AD{fill:#eed190}.AE{fill:#b08c4f}.AF{fill:#5d5d5c}.AG{fill:#695434}.AH{fill:#bd9551}.AI{fill:#776343}.AJ{fill:#917344}.AK{fill:#6b6b69}.AL{fill:#443522}.AM{fill:#4e422f}.AN{fill:#947747}.AO{fill:#7f6033}.AP{fill:#7a6136}.AQ{fill:#fdfadf}.AR{fill:#121212}.AS{fill:#828179}.AT{fill:#71706d}.AU{fill:#988c70}.AV{fill:#76756f}</style><path d="M467 427h2v2h-1l-1-2h0z" class="S"></path><path d="M408 572c-1 0-1 0-1-1 1 0 3 1 4 1l-1 1c-1 0-2 0-2-1z" class="F"></path><path d="M363 291v-2c0-1 1-1 2-2 0 2 0 2-2 4z" class="S"></path><path d="M517 561c1 0 1 1 1 2h-2l-1-1v-1h2z" class="F"></path><path d="M513 455c-1-1-1-1-2-1v-1l1-1h2-1v3z" class="z"></path><path d="M133 551c1-1 1-1 2 0v1l-2 1h-1v-1c0-1 1-1 1-1z" class="S"></path><path d="M445 515h2l1 1h0c-1 1-2 2-3 2v-3z" class="l"></path><path d="M516 557c0 2 1 3 1 4h-2-1c1-2 1-3 2-4z" class="D"></path><path d="M536 531c2 0 3 1 4 1v1h-1s-1 1-2 1l-1-3z" class="W"></path><path d="M445 515l3-2h0 1v1c-1 1-1 2-1 2l-1-1h-2z" class="F"></path><path d="M444 562c1-1 1-1 2-1s2 0 3 1l-1 1-4-1z" class="i"></path><path d="M475 474h1 0c0-1 0 0 1-1v1c0 2-1 2-1 4h0v-2h-1-1 0l1-2zM372 224h0c2 1 5 1 7 1l-5 1-2-2z" class="L"></path><path d="M175 286c1 0 1-1 2-2 1 0 1 1 2 1-1 1-2 2-3 2l-1-1z" class="AS"></path><path d="M104 350c1 1 1 2 2 3h-1l-1 1v3 1-1c-1-1-1-6 0-7z" class="D"></path><path d="M332 224c0 2 0 3 1 4v3l-2-1v-2c1-1 1-3 1-4z" class="C"></path><path d="M377 196c2 0 2 0 3 1l-1 1c0-1-1-1-2-1 0 1 1 1 1 2-1-1-1-1-2-1h-3l2-1c1 0 2-1 2-1z" class="S"></path><path d="M181 342c-1-1-3-1-4-2h0c1-1 3-1 4-1v3z" class="AO"></path><path d="M173 575v2l1 1-1 1s-1 1-2 0h0c0-2 1-3 2-4zm263-99h1c0 1 0 1-1 2h-1s-1 0-1-1c-1 0-1 0-2 1v-1h0c1 0 1 0 2-1h0 2z" class="E"></path><path d="M380 542l-1-1c1-1 3 0 4 0v3h0s0-1-1-1c-1-1-1-1-2-1z" class="C"></path><path d="M420 495l2 1c0 1 0 2-1 3-1 0-2-1-3-1 1-1 2-1 2-3z" class="F"></path><path d="M143 407v-1l3-1 1 1c1 0 2 0 3 1h-7z" class="W"></path><path d="M167 317c2 1 3 1 4 2v1c-1 1-3 0-4 0v-3z" class="AP"></path><path d="M89 535c0-1-1-1-1-1v-1c1-1 3-2 4-2-1 1-1 2-2 3v1h-1z" class="m"></path><path d="M182 285l4-1-1 2c0 1 0 2-1 2 0-1-1-1-2-2v-1z" class="N"></path><path d="M415 260v-1-1-2-2h1l2 1c-1 1-1 1-2 1 0 1 1 3 0 4h-1z" class="M"></path><path d="M419 590l-1-1v-2c-1 0-3-1-3-1v-1c2 1 3 1 5 1h1c-1 0-2 1-2 1h-1l1 1 1 1h-1v1z" class="J"></path><path d="M466 373v-3c1-1 1-2 0-3h0l1-1c1 1 1 2 2 3-1 0-1 0-1 1s-1 1-1 1l-1 2z" class="AO"></path><path d="M453 500c1 1 2 2 1 3v1 2h-1l-1-2v-2c1-1 1-1 1-2z" class="J"></path><path d="M458 576l2 2c-1 1-1 1-2 1s-2 0-3-1h0c2 0 2-1 3-2zM145 412l-1-1c1-2 3-2 4-3l-1 3h0c-1 1-1 1-2 1z" class="B"></path><path d="M510 602l7 3-4 1c-1-1-2-2-3-4z" class="M"></path><path d="M171 338c2 0 2 0 4 1-1 1-2 1-4 2l-1-1 1-2h0z" class="t"></path><path d="M470 318h1l2 2v2l-1-1-2-1c-1-1-1-1-2 0l-1-1c1 0 2 0 3-1z" class="z"></path><path d="M380 542c1 0 1 0 2 1 1 0 1 1 1 1v3l-1 1-2-6z" class="B"></path><path d="M520 374c2-1 3-1 4 0l-4 2c-1 1-1 1-2 0l2-2z" class="m"></path><path d="M293 562c0-1 0-2 1-2h1v-1c-1 3-4 7-6 9 0-1 0-2 1-3 0 0 1 0 1-1h0l2-2z" class="AP"></path><path d="M192 281l1-1c1 0 2 1 4 1l1-2-2 4c-1 0-2-1-4-2z" class="C"></path><path d="M357 588c1 2 2 3 3 5h0c0 1 0 1-1 2h0c-2-2-2-4-2-7z" class="S"></path><path d="M337 293h1v2 1c0 1 0 3-1 4v-1c0-2-1-4 0-6z" class="z"></path><path d="M127 356v-2c2 0 4 1 6 2l-1 1h0c-1-1-1-1-3-1 0 0-1 0-1-1l-1 1z" class="W"></path><path d="M535 531h1l1 3v1h-1l-3-1 1-1c0-1 0-1 1-2z" class="AF"></path><path d="M514 452c2 0 2 0 3 1 0 1 0 1-1 1-1 1-1 1-2 1h-1v-3h1z" class="y"></path><path d="M442 560h6c1 0 1 0 2 1h-4c-1 0-1 0-2 1l-5-1h0l3-1z" class="H"></path><path d="M454 573l4 3c-1 1-1 2-3 2h0v-2l-1-1v-2z" class="m"></path><path d="M248 617h0l1 1c0 1 1 2 0 3h0-2-1l-1 1v-1l3-4z" class="S"></path><path d="M143 407c-1 0-2 0-3-1l2-2h5v1h-1l-3 1v1z" class="X"></path><path d="M182 361v-1c1 0 1-1 2-2l2 3c-1 1-1 1-3 2-1-1-1-1-1-2z" class="F"></path><path d="M513 461h3c1 1 1 1 1 2h-1-4l-1-1 2-1z" class="AO"></path><path d="M329 220c1 1 2 3 3 4 0 1 0 3-1 4h-1c0-3 0-5-2-7 0 0 0-1 1-1z" class="y"></path><path d="M504 466h1c0 2-1 3-1 5h-1-1l-1-1c1-1 2-3 3-4z" class="P"></path><path d="M346 313h1l1 1c0 2 0 4-1 6-1 0 0 0-1-1 0-1-1-4 0-6z" class="u"></path><path d="M346 266c2-2 3-3 5-4 0 1-1 2-1 4-1 0-2 1-3 1 0 0 0-1-1-1z" class="i"></path><path d="M497 397h5l1 1h0l1 1h-2-1-6l2-2z" class="B"></path><path d="M374 312h1c0-1 1-2 2-3v12c0-1 0-4-1-5l-1 1v-2c1 0 1-1 1-2-1 0-1 0-1-1h-1z" class="AG"></path><path d="M464 483c1 1 1 5 1 6v-1l-1 1h0c-1 0-2 1-3 1v-1h0c1-1 2-1 3-1v-1l-1-1c0-1 1-2 1-3z" class="M"></path><path d="M399 216h5c1 1 0 1 0 2h-1c-1 0-3 0-4-1v-1z" class="d"></path><path d="M162 545c1 1 2 1 3 2l1 1h-3c-2 0-3 0-4-1v-1h0 2l1-1z" class="B"></path><path d="M328 654c1 0 3 1 4 2v2c0 1-1 1-1 2l-1-1v-1c0-1-1-1-1-2-1 0-1-1-2-1l1-1z" class="AE"></path><path d="M336 260l1-1h1c0 3 1 8 0 10h-1v-6c-1-1-1-2-1-3z" class="u"></path><path d="M101 411h0c2 0 3 1 4 2h-1-2-1l-2 2h0v-2l2-2z" class="R"></path><path d="M466 568l3 4c-1 0-5 0-5-1-1 0-1-1-2-1h3c0-1 1-2 1-2z" class="F"></path><path d="M395 547l1-1h1c0 1 1 2 2 2h0c0 1 0 2-1 2l-2 2h0c0-2 0-3-1-4v-1z" class="X"></path><path d="M397 284c1 2 1 3 1 4v1c-1 2-2 3-3 4l-1-1c2-3 3-5 3-8z" class="u"></path><path d="M499 414l3 3v1h0v1l1 1c0 1 0 2-1 2h0v-2l-1-1-1-1c-1-1-2-3-1-4z" class="L"></path><path d="M489 320h-1c-2 1-3 2-4 4l-1-1c0-1 1-2 1-2 1-2 2-3 3-3v1h1 1v1z" class="d"></path><path d="M509 430v-1c1 0 1 0 2-1 1 1 4 3 5 4-1 0-1 1-2 1-1-1-3-2-5-3h0z" class="M"></path><path d="M495 454h-2c-1 0-1-1-1-1 0-1 0-1 1-1 1-1 3 0 4 0v3h-1 0l-1-1z" class="q"></path><path d="M177 350c-2 1-3 2-5 3 0-2 1-4 2-6 2 1 2 1 3 3z" class="S"></path><path d="M430 576h3v1s0 1 1 1c-1 1-1 1-2 1h-1-2l-1-1-1-1h1s1 1 2 0v-1z" class="C"></path><path d="M458 550l6-3h1 1 1 2c-1 1-2 2-4 1-2 0-5 2-6 3l-1-1z" class="J"></path><path d="M493 562c0-1 1-1 1-2 1 1 2 2 2 3 1 1 0 2 0 3h0c-1 0-2 0-2-1h0c0-1 0-2-1-3z" class="B"></path><path d="M287 575h0v1 1 1c0-1 0-2 1-2h0l-3 8v2l-1 1c-1-3 2-9 3-12zm212-6l3 6h-1v3 2h-1v-2c1-2 0-3-1-6l-1-2 1-1zm55 6h1v1c0 2-1 4-3 6h-1v-1c0-2 2-5 3-6z" class="S"></path><path d="M391 496h1 0 0 0c-1 1-1 1-1 2h0c-2 1-3 2-5 3h-1c1-2 4-4 6-5z" class="W"></path><path d="M358 542c0 1 1 2 0 3v1 1h0 0c-1 1-3 3-4 5v-3c0-1 1-2 2-2 0-1 0-1 1-1 1-1 1-3 1-4z" class="M"></path><path d="M452 504l-2 2h0v-1h-1l-1-1 1-1c0 1 0 1 1 1v-2-1c1-1 1-1 2-1 0-1 0-1 1-1v1c0 1 0 1-1 2v2z" class="S"></path><path d="M121 411h2c1 0 2 1 3 2h-1l-2 1h-1c0 1-1 1-2 1v-1c0-1 1-2 1-3z" class="F"></path><path d="M300 620c1 1 2 4 4 4-1 1-1 1-1 2l-1 1c-1-2-3-4-4-6h1c0-1 0-1 1-1z" class="AL"></path><path d="M167 320c-2 0-3 1-5 3l1-5c2-1 2-1 4-1v3z" class="x"></path><path d="M537 535c2 1 5 2 7 3 0 1-1 1-1 2l-7-4v-1h1z" class="AS"></path><path d="M355 261h3-1c-1 1-3 1-3 2l1 1-5 2c0-2 1-3 1-4l4-1z" class="K"></path><path d="M519 563c1 1 1 2 2 2h2c1 1 2 3 3 4l1 1c-2 0-4-1-5-2s-2-1-3-2v-3z" class="L"></path><path d="M469 369c0 2 0 3-1 5s-4 4-6 5c1-2 3-4 4-6l1-2s1 0 1-1 0-1 1-1z" class="x"></path><path d="M307 652h0c2 1 2 0 4 0l-1 1c-2 0-3 1-4 2-1 0-2 0-3-1l-1-1c0-1 1-1 1-1h4z" class="d"></path><path d="M418 498c1 0 2 1 3 1-2 0-3 2-5 3-1 0-1 0-2 1l-3-1h2l3-3 2-1z" class="L"></path><path d="M140 360c3 1 6 2 8 5h-2c-1 0-2 0-3-1l-2-2-1-2zm412 187c2 4 3 8 3 12v4 1c-1-1-1-3-1-5s-1-5-2-7-1-3 0-5z" class="W"></path><path d="M442 568c2 0 4 0 5 1-1 1-2 1-4 1-1 1-2 1-3 1h-1c0-1 0-1 1-2 1 0 2-1 2-1z" class="N"></path><path d="M186 428l2-1 2 1h0c-1 1-2 1-2 3h0c-3 0-5-1-7-2l5-1h0z" class="X"></path><path d="M294 612l6 8c-1 0-1 0-1 1h-1 0c-3-3-4-5-4-9z" class="l"></path><path d="M365 287h1c0 1 2 3 2 4-1 1-1 2-2 2s-1 1-1 0c-1-1-1-2-2-2 2-2 2-2 2-4z" class="L"></path><path d="M487 218l1-3v1c1 2 4 6 4 8v3c1 0 1 2 1 3l-6-12z" class="G"></path><path d="M181 339l3 1c2 0 5 3 6 5l-9-3v-3z" class="AP"></path><path d="M487 318c2-1 4 0 6 0 1 1 3 3 3 5v1c-1-2-2-3-4-4h0-3v-1h-1-1v-1z" class="AG"></path><path d="M485 355c2-1 3-1 5 0h0l-3 1c-1 0-2 1-3 2s-1 3-2 3c0-1 0-3 1-5 0-1 1-1 2-1z" class="R"></path><path d="M347 621c1 0 1-1 1-1 2-3 3-4 6-4 2-1 3 0 4 0-1 1-3 1-5 2h0c-1 1-2 2-4 2l-2 1z" class="AL"></path><path d="M341 623c3-3 5-6 7-9 0 2 0 2-1 4s-3 4-5 6c0 0 0-1-1-1z" class="l"></path><path d="M421 586l1-1 5 4c-1 1-6-1-8-1l-1-1h1s1-1 2-1z" class="M"></path><path d="M413 269h2c1 0 2 1 3 2s1 1 1 3h0l-3-2c0-1-2-1-3-1 0 0-1 1-2 1h0c0-1 1-2 2-3z" class="AH"></path><path d="M281 627c-1-2-1-3-2-5 1-1 2-3 3-4 2-2 3-2 5-1h0c-1 0-3 0-4 1l-1 1h2v1l-3 3v4z" class="y"></path><path d="M154 423l9 2-1 1-6-1h-6 4v-2z" class="j"></path><path d="M447 569c3 1 5 2 7 4v2c-3-3-6-5-11-5 2 0 3 0 4-1z" class="Q"></path><path d="M360 626v2h0c0-1 1-1 1-1l-1 3 1 1 1 1v1l-4-2c-1 0-2 0-3-1 2-1 4-2 5-4z" class="I"></path><path d="M330 258h1v1c1 2 0 5 0 7l1 2c-1 0-1 0-1 1l-1-1v-2c0-2-1-6 0-8z" class="L"></path><path d="M314 661h-1c-1 0-1-1-2-2s-1-2 0-3c0-1 1-1 2-1s2 0 3 1l-3-1-1 1 3 3c-1 1-1 1-1 2h0z" class="AJ"></path><path d="M346 630h1c1 1 1 1 3 2h3c0 1-1 2-2 2h-3c-1-1-3-2-3-3h1v-1z" class="AG"></path><path d="M495 454l1 1c-1 2-1 4 0 6v3l-1 1c0-1 0-1-1-2-1-2-2-5-2-8 0 1 1 1 1 2l2 4v-7z" class="L"></path><path d="M138 563c-2 0-3 1-5 2l-1-1v-1c1-3 3-4 5-6v1c0 1-1 2-2 4h1l2 1z" class="F"></path><path d="M430 568c-2 0-6-1-7 0h-2l-2-1v-1h1c2 1 7-1 10 1v1h4c-2 0-3 1-4 0z" class="L"></path><path d="M475 542v-1c2-2 6-2 9-4h2l-1 2-8 2c-1 1-1 1-2 1z" class="S"></path><path d="M379 288h1v3c-1 2-3 4-5 6v-3l1-1 2-4h0 0 1v-1z" class="x"></path><path d="M528 531l2-1h4l1 1c-1 1-1 1-1 2l-1 1-3-1 1-1v-1h-3z" class="N"></path><path d="M534 530l1 1c-1 1-1 1-1 2h-1v-1c-1 0 0 0-1-1 1 0 2 0 2-1h0z" class="E"></path><path d="M337 293l-1-6c0-1 0-1 1-2v-3c1 1 1 3 1 4 0 3 1 6 0 9v-2h-1z" class="u"></path><path d="M450 561c1 0 3 1 5 2h0l-1 1h-1l1 1-1 1-5-3 1-1c-1-1-2-1-3-1h4z" class="G"></path><path d="M191 569h0v-1c2-1 4 0 5-1 2-1 5 0 7 0l-1 1c-1-1-2-1-3-1l-2 2c-2 0-3 1-5 1l-1-1z" class="J"></path><path d="M393 270c-2 0-3-2-4-2s-2 0-3-1c0 0-1 0-1-1 0-3 0-4 2-5v3c0 1 2 2 3 3 0 1 1 2 3 3h0z" class="L"></path><path d="M292 582c1-4 2-8 5-11v1c0 2-1 4-2 6s-1 4-2 5c-1 0-1 0-1-1z" class="E"></path><path d="M411 572c2 1 6 1 8 3-1 1-1 1-2 1-2-1-7-3-9-2v-2c0 1 1 1 2 1l1-1zm116-230v-3c2 1 2 6 4 6v3c-1 0-1-1-1-1h-2l-1 1c0-2 1-4 0-6z" class="C"></path><path d="M549 579v-2c2-4 4-9 6-13 0 5-2 11-5 14-1 0-1 0-1 1z" class="D"></path><path d="M192 570c2 0 3-1 5-1l2-2c1 0 2 0 3 1l-3 3-1-1c-2 0-4 2-7 1l1-1z" class="B"></path><path d="M358 616c1 0 2 1 3 1-2 1-4-1-6 2-1 0-2 1-3 2h0c0 1-1 2-1 2h-1v-1-1l-1-1c2 0 3-1 4-2h0c2-1 4-1 5-2z" class="AG"></path><path d="M187 284v-1c-2-1-4-3-5-5h0c-1-1-1-1-1-2h0 2l4 6h2l1 1c-1 1-2 1-3 1z" class="J"></path><path d="M346 328h1 0 0c2 0 2 0 3 1h2c1 1 1 1 2 1 1 1 3 0 4 1h-13v-1h-1l2-1h-1c1 0 1-1 1-1z" class="E"></path><path d="M446 555l6-3c2 0 4-2 6-3v1l1 1c-3 1-9 5-12 4h-1z" class="S"></path><path d="M112 335c0 2-1 5-2 6l-2 6-1-2-1-1 6-9z" class="Q"></path><path d="M400 548c0-1 0-1 1-2 0 1 1 4 1 4 0 2 1 4 1 6l-4-4c0-1 0-1-1-2 1 0 1-1 1-2h1z" class="F"></path><path d="M400 548v1c0 1-1 2-1 3 0-1 0-1-1-2 1 0 1-1 1-2h1z" class="B"></path><path d="M492 224c2 2 3 8 4 11l1 1c0 2-1 3 0 5l-4-11c0-1 0-3-1-3v-3z" class="H"></path><path d="M380 517l5-2-11 11v-3l6-6z" class="D"></path><path d="M472 475c0-1-1-2-1-3v-1h2 1v1c0 1 1 1 1 2l-1 2h0 1 1v2h0 0-2c0-1-1-1-1-2v-1h-1z" class="l"></path><path d="M421 284c-1-1-1-3-2-4s-2-2-2-4h3 0c0 1 1 2 1 3l1 1-1 4z" class="F"></path><path d="M376 198c1 0 1 0 2 1 0 0-1 1-1 2l1 1c0 1-1 2-2 3-1 0-3 1-4 1 1-1 2-1 2-2v-2-1l2-2h0v-1z" class="J"></path><path d="M347 621l2-1 1 1v1 1h1l-1 1-1-1-5 5-2 1c1-3 4-6 5-8z" class="u"></path><path d="M525 348v-1c0-1 0-5 1-6h0v5h0v-2c0-1 0-1 1-2v1h0v-1c1 2 0 4 0 6l-1 6h-1v-2-1-3z" class="Y"></path><path d="M142 317h3c2 1 3 1 4 2v1h-1c-2-1-2-1-5 0l-3 3h0v-4l2-2z" class="AN"></path><path d="M344 628l5-5 1 1-3 3 1 1-1 1v1h-1c-1 0-1 0-1 1-1 1-3 2-4 4v-1c1-3 2-3 3-6z" class="t"></path><path d="M394 281c1 1 2 1 3 3 0 3-1 5-3 8 0-2 1-4 0-5 0-2-1-4-1-5l1-1z" class="P"></path><path d="M359 217c1 3 3 7 2 10 0 3 0 6-1 8 0-5 0-10-1-15v-3z" class="AP"></path><path d="M327 243l1-1c-1 3-1 10 0 11 1 0 2 0 3 1v1 1h-3c-1 1 0 2 0 3 0 3 0 8-1 10v-26z" class="l"></path><path d="M170 337h1v1h0-1c-2 1-5 5-7 5-1-1-1-1-2-1v-1c3-2 6-3 9-4z" class="AM"></path><path d="M457 513l1 1-7 7-2 2h-1s-1-1-2-1c2-1 3-2 5-3l6-6z" class="x"></path><path d="M174 347c2-1 5-3 8-3h2l-7 6c-1-2-1-2-3-3z" class="J"></path><path d="M420 516s0-1 1-1c-1-2-3-3-3-5v-1h1c1 2 2 2 3 3 0 1 0 1 1 2 0 1 1 1 2 1h0-1-2l-1 1c1 1 2 2 3 4h-2l-2-4z" class="L"></path><path d="M420 522c-2-2-4-4-5-6v-1c2 0 3 0 5 1l2 4v1l-2 1z" class="B"></path><path d="M294 557c1-1 2-3 3-4l2 2-4 4v1h-1c-1 0-1 1-1 2-2 1-3 2-4 3h0l5-8z" class="t"></path><path d="M440 526c2-1 4-3 6-4 1 0 2 1 2 1-2 2-5 4-7 5h-1v-1h-1l1-1z" class="AG"></path><path d="M330 228h1v2l2 1c0 2-1 4-2 6 0-1-1-1-1-1h-1c-1-1 0-3 0-4 0-2 1-3 1-4z" class="F"></path><path d="M330 228h1v2 1s0 1-1 1v4h-1c-1-1 0-3 0-4 0-2 1-3 1-4z" class="t"></path><path d="M500 555l1-4h0 1c0 2-1 2 0 3v2 1h1c1-1 1-1 2-1s1-1 2-1h1l-3 3h-2-1c0 1 0 1 1 1h-1v1l-1-1-1 1v-5z" class="S"></path><path d="M500 555h1v2h1 1v1h-1c0 1 0 1 1 1h-1v1l-1-1-1 1v-5z" class="J"></path><path d="M488 548h2c3 0 7 2 9 5v1c-3-1-8-3-11-5v-1z" class="Y"></path><path d="M472 447c2-1 3 2 4 2h1v1c-1 0-3-2-4-3v1c0 2 3 3 4 4 1 0 1 1 2 1s1 0 2 1h0c-1 1-1 1-2 0h-2c-1-1-2-1-3-2h0l-1 1c1 1 3 2 4 3 2 0 4 1 5 2h-1-1-1c-2-1-4-3-6-5v-2l3 1c-1-1-2-2-3-2-1-1-1-2-1-3z" class="S"></path><path d="M442 560c5-2 11 0 17 2-1 2-2 2-3 3l-2-1 1-1h0c-2-1-4-2-5-2-1-1-1-1-2-1h-6z" class="k"></path><path d="M303 560v-2s-1 1-2 1v-1c1-2 3-2 4-4s4-4 5-5c-2 4-4 8-7 11z" class="J"></path><path d="M501 507h0c1-1 1-1 1-2 1 0 2-1 2-2h2l1 1c-1 2-3 4-3 6h-3-1l-1-1 1-1 1 1c-1 0-1 0 0 1h2c0-1 0-1 1-1v-1l-1-1v1c-1 0 0 0-1-1v1h-1-1-1v1l-1 1-1-2h3l1-1z" class="M"></path><path d="M496 508h1l1 2h-1c1 1 1 2 2 2 0 1 0 1-1 2-1 0-1 1-2 1l-1-1h0l-2-2 2-3 1-1z" class="AM"></path><path d="M496 513c1 0 1 0 2 1-1 0-1 1-2 1l-1-1h0c0-1 1-1 1-1z" class="Q"></path><path d="M495 509c0 1 2 2 2 3h-1l-1 1h1s-1 0-1 1l-2-2 2-3z" class="m"></path><path d="M429 483h0l2 1s0 1 1 1 1 0 3 2v1c2 0 3 0 5-1h1s1 0 1 1h0l-1 1v-2c-1 0-1 0-1 1h-6c-3 0-5-1-8-3l1-1h0c1 1 2 1 3 1v-1l-1-1z" class="J"></path><path d="M375 317l1-1c1 1 1 4 1 5v8c-1 1-2 1-3 1l1-1c0-1 0-3-1-5h0l1-1v-2h-1l1-2-1-1h0l1-1z" class="AA"></path><path d="M350 305l-11-2c-2 0-4-1-5-1 2-1 14 1 18 1v2h-2z" class="e"></path><path d="M516 432v1c1 1 1 1 3 1h0v1c0 1 1 2 2 3h-2v2c-2-1-3-1-5-2h0c1 0 2-1 2-2s0-1-1-1l-1-2c1 0 1-1 2-1z" class="l"></path><path d="M516 355l3-3 1-1c1 0 0-1 0-2 2 0 2 1 3 2h1c-2 2-2 4-4 6-1-1 0-2-1-3l-1 1s0 1-1 2v-2h-1z" class="B"></path><path d="M96 552c-1 1-2 3-3 4v-4-5s1 0 2-1c0 0 0-1 1-1 1-1 2-1 3-2h0c0 1-1 1-1 2h-1c1 1 1 2 0 3s-1 1-2 1h-1c0 1 0 1 1 1 0 1 0 1 1 2h0z" class="M"></path><path d="M97 545c1 1 1 2 0 3s-1 1-2 1-1 0-1-1h0l3-3z" class="D"></path><path d="M468 475h0c0-1-1-2-1-3h-1l1-1h1c1 1 1 2 1 2l3 3v-1h1v1c0 1 1 1 1 2l-1 1h-1l-1 1v1h-1c0-2-1-4-2-6z" class="S"></path><path d="M468 475l1-1 3 3v2l-1 1v1h-1c0-2-1-4-2-6z" class="AL"></path><path d="M408 529h0v4 1l-1-1v1c0 1-1 3-1 4s1 4 2 5l3-3c-1 2-2 3-3 4h-1l-2-7c0-1 0-1-1-2v-3-1-1c1 1 1 1 2 1 0 1 1 1 2 1v-3z" class="J"></path><path d="M535 545c1 0 2 0 3 1l1 2c-1 2 1 6 0 8h1c-1 1-1 1-2 1-1-1-1-3-1-4 0-3 0-5-2-7v-1z" class="AI"></path><path d="M478 449l3 3h1v-1c2 1 2 2 3 4s3 3 4 5 1 5 3 7h-1v-1l-1-1h-1v-1c-1-1-3-2-4-4h0 1c0 1 2 2 3 3v1-1c-1-1-1-3-2-5h-1c-1-2-2-3-4-4h0v-1c-1 0-1 0-2-1 0-1-1-1-1-1-1-1-1-1-1-2z" class="S"></path><path d="M154 252c2 0 5-1 7 1h2v2h-3l-10-2c1-1 2-1 4-1z" class="D"></path><path d="M342 198h-1c-2-2-4-3-6-4v-1c1 0 2 1 3 1 3 2 7 4 9 6v2 3l-3-3v-2l-2-2z" class="V"></path><path d="M429 513l1-1c1-2 0-2-1-4h0c1 0 1 0 2 1l1 2c2 1 2 3 3 5v2c-1 0-1 1-1 2h0v-2l-1-1c0-2-1-2-1-3-1-1-1-1-2-1 0 1-1 1-1 1-1 0-1 0-2 1h-2 0c0-1-1-2-1-3h0c1 1 1 1 1 2h1 2l1-1h0z" class="J"></path><path d="M461 509l2 1-2 2c0 2-1 3-2 4h0l-5 5h-3l7-7c1-2 2-3 3-5z" class="B"></path><path d="M362 547c0 2-1 4-2 6h-1v-2s-1 0-1-1c0 0 1-1 1-2v-1c0-1 1-2 0-3h0 0l1-1 1-1v-1-1-2h1v-2c1-1 1-3 1-4 0 0 1 0 1-1s1-1 1-1c0-1 0-2 1-2l-3 9c-2 3-3 7-3 11h0l2-1z" class="C"></path><path d="M508 555c0-1 2-2 3-2 2 0 4 1 6 1l-2 2h-1c-1 1-3 1-4 2l-2-1c-1 0-2 1-3 1l3-3z" class="L"></path><path d="M157 229c0 2 0 3 2 5h0c1 1 2 1 3 2 0 1 0 1 1 1h0v6c-1-1-2-3-2-3-4-4-4-6-4-11z" class="B"></path><path d="M421 572l9-2c2 0 3-1 4 0l-1 1c-2 0-5 1-7 2-1 0-2 0-3 1 1 0 2 1 3 1 0 1 0 2 1 2l1 1c-1 1-1 1-1 2 0-1-1-2-2-3s-2-2-3-2-1 0-2-1h2 1 0c-1-1 0-1 0-2h1 3l-1-1-1 1h-4 0z" class="Y"></path><path d="M402 550c1 1 2 2 2 3 1 2 3 3 4 4 1 0 1 0 2-1 2 0 2-1 4 0-1 0-1 1-2 1l-3 3c-2-1-3-1-4-3l-2-1c0-2-1-4-1-6z" class="E"></path><path d="M334 215c-2-3 1-4 0-6h0c2 0 3 1 5 3l-1 1c1 1 1 1 0 2v1c-1-1-1-1-1-2-1 1-1 2-1 2l-2-1z" class="u"></path><path d="M334 215c1-1 1-2 1-4h0 2v3c-1 1-1 2-1 2l-2-1z" class="C"></path><path d="M505 558c1 0 2-1 3-1l2 1c1-1 3-1 4-2h1c-2 2-3 3-4 5v2h-1-2c0-2 1-2 2-3v-1c-3 1-5 0-7 0-1 0-1 0-1-1h1 2z" class="X"></path><path d="M326 657v-1h1c1 1 2 1 3 2v1l1 1c-1 1-2 1-3 1h-1c-2 0-2 0-3 2l-1-1h-1l-1 1h0l1-2c1-2 3-2 4-4z" class="V"></path><path d="M330 658v1l1 1c-1 1-2 1-3 1h-1v-2h2l1-1z" class="AH"></path><path d="M94 530h4c1 1 1 2 1 3l-4 1-5 1v-1c1-1 1-2 2-3l2-1z" class="AK"></path><path d="M94 530h4c1 1 1 2 1 3l-4 1v-1h0c1-1 2-2 3-2l-1-1h-3z" class="AF"></path><path d="M419 588c2 0 7 2 8 1l1 1 1 1c-1 1-2 1-3 1-1 1-3 1-4 0-2 0-3-1-3-2v-1h1l-1-1z" class="M"></path><path d="M419 588c2 0 7 2 8 1l1 1h-2c-2 0-4 1-6-1l-1-1z" class="Y"></path><path d="M383 544l1 2c1-1 1-3 2-4h1c1 1 1 2 1 4h-1c-1 2 0 3-2 5h0-1l-2-1 1-1-1-1 1-1v-3h0z" class="J"></path><path d="M324 647l1 1c0 1-1 2-1 2v1 1h0l2-1c1 1 0 2 1 2v1h1l-1 1c1 0 1 1 2 1 0 1 1 1 1 2v1-1c-1-1-2-1-3-2h-1v1h-1-1v-2-1c-1-1-1 1-3 0 1-1 1-2 1-4 1-1 2-2 2-3z" class="R"></path><path d="M164 566l1 1 1 1c2 0 3-1 4-2l1 1-2 2h0c0 1-3 3-3 3h-2s-1 1-2 0h0c0-2 1-4 2-6z" class="AL"></path><path d="M449 250c-1-1-3-1-4-3h1 2v-1l1-1h0 6v4c-2-1-3 0-5 0l-1 1h0z" class="L"></path><path d="M364 300c1-1 4-2 5-4l3-8c1 0 0 3 1 4h1c0 1 0 1-1 2v1c-2 2-4 4-7 5h-2z" class="P"></path><path d="M334 267v-9h0c1 1 0 1 1 2h1c0 1 0 2 1 3v6h1v1h1l1 1h0c-2 0-4 1-5-1l1-1v-1h0-2v-1z" class="l"></path><path d="M334 267l1-2 1 1v1 1h-2v-1z" class="E"></path><path d="M335 632l1-1c-1 3-2 4-4 6 3-1 4-4 7-5l-9 9h-1-1s0-1 1-1v-1-1-1l4-4h0l2-1z" class="l"></path><path d="M344 628c-1 3-2 3-3 6-1 1-2 1-2 1-2 1-3 3-5 4l-4 2 9-9 3-3 2-1z" class="y"></path><path d="M424 476h1v1 2c-4 0-7 2-11 3-2 0-3 0-4 1 0-1 1-2 2-3h0c3-1 5-2 8-3 1 0 2-1 3-1 1 1 0 1 1 1v-1z" class="L"></path><path d="M517 554c2-1 4-1 7 0 1 0 2 0 3 1v1 1c-1-1-2-2-4-3h0c-1 0-3 0-4 1s-2 2-3 2c-1 1-1 2-2 4h-1l-1-1v1c0 1-1 1-1 2s0 1-1 2v-1-1h1v-2c1-2 2-3 4-5l2-2z" class="AF"></path><path d="M434 568h8s-1 1-2 1c-2-1-4 0-6 1-1-1-2 0-4 0l-9 2h-5v-1h3c4 0 7-1 11-3 1 1 2 0 4 0z" class="B"></path><path d="M344 646c2 1 3 1 4 3v3c0 1-2 2-3 3-1 0-2-1-3-1v-2c1 0 1 0 2 1h1l1-2h0-3v-3h0c0-1 0-1 1-2z" class="Z"></path><path d="M343 648c2 1 3 1 4 2v1h-1 0-3v-3z" class="f"></path><path d="M374 195l3 1s-1 1-2 1h-1 0c-1-1-1-1-2-1-1 1-2 1-3 1h0c-2 0-2 1-3 1-1 1-1 1-2 1h0l-1 1-2 1s0 1-1 1h0c-1 1-2 1-2 2h-1 0c-1 0-1 0-1-1v-1c0-1 0-2-1-3v-1-2l1-1v1 1c-1 2 0 3 1 5h1c1-1 1-2 2-2 2 0 3-1 5-2 1 0 2-2 3-2s1 1 2 1c0-1 1-1 1-1 1-1 2-1 3-1z" class="B"></path><path d="M368 274h0l1-1c1-1 3 0 5 0 2 1 3 3 3 5 0 1 0 1-1 1s-1-1-2-1h0l-6-4z" class="U"></path><path d="M365 221l2 1c0 1 0 1-1 2h2 1v-1c1 0 1-1 1 0 1 1 1 1 2 1l2 2-8 3c0-3-1-5-1-8z" class="C"></path><defs><linearGradient id="A" x1="185.724" y1="573.154" x2="177.071" y2="569.946" xlink:href="#B"><stop offset="0" stop-color="#3e3b3d"></stop><stop offset="1" stop-color="#4d4f4c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M173 575l5-3c3-3 7-4 11-3l-1 1h0-1c-1 1-2 1-4 1-1 0-2 1-3 2-2 1-4 2-7 4v-2z"></path><path d="M355 619c2-3 4-1 6-2 2 2 2 4 2 7v1s-1 2-2 2c0 0-1 0-1 1h0v-2-1c1-2 0-2-1-4v-1h0c-1-1-2-1-4-1z" class="AH"></path><path d="M359 620c1 1 3 2 3 3l1 2s-1 2-2 2c0 0-1 0-1 1h0v-2-1c1-2 0-2-1-4v-1z" class="s"></path><path d="M385 306v3c0 1 1 2 1 3l1 1v1 1 1c1 1 1 2 1 4 0 1 0 1-1 1h0s-1 0-1-1c-1-1-1-3-1-4 0-2-2-5-2-6s1-3 2-4z" class="M"></path><path d="M502 560v-1h1c2 0 4 1 7 0v1c-1 1-2 1-2 3h-1c0 1-1 1-1 2h-3l-1-1v-4z" class="Y"></path><path d="M502 564c2 0 4-1 5-1 0 1-1 1-1 2h-3l-1-1z" class="J"></path><path d="M82 560h0c0 1 0 2 1 3s3-1 3 1v-1h1c1 0 1 0 1 1 1 1 2 2 2 3v1c-1 0 0 1-1 2l-1 1c0-1 0-1-1-2 0-1-1-1-2-2s-2-2-3-4v-3z" class="B"></path><path d="M87 569l1-1-2-2c0-1 1 0 1-1 1 0 1-1 1-1 1 1 2 2 2 3v1c-1 0 0 1-1 2l-1 1c0-1 0-1-1-2z" class="D"></path><path d="M482 487v-5s-1-1-1-2-1-1-1-2v-2c-1-1-1-1-1-2s0 0-1-1v-1l-1-1c0-1 0-1-1-2h0v-1h2c2 3 2 5 4 8 1 2 1 4 2 6-1 1-1 0-1 1-1 1 0 2 0 3l-1 1z" class="l"></path><path d="M179 558c5 0 11 2 16 3l-8 1s1 0 2-1h-1c-1 0-2-1-2-1-2-1-5 0-7 0l-3 1-3 1 1-3c1 0 4 0 5-1zm266 16l2 1 3 3c0 1 1 2 2 2h1c0 1 1 2 1 3s0 1-1 2v1c-2-1-4-4-6-6v-1-3c-1-1-2-1-2-2z" class="Q"></path><path d="M509 430c2 1 4 2 5 3l1 2c1 0 1 0 1 1s-1 2-2 2h0c-1 0-2-1-3-2l-2-1c0-1 0-2 1-3l-2-1c0-1 1-1 1-1z" class="AG"></path><path d="M509 430c2 1 4 2 5 3l1 2v1c-2-1-4-3-5-4l-2-1c0-1 1-1 1-1z" class="a"></path><path d="M317 655v-1c1 0 1 0 1-1v-1l1 2c0 1 1 2 1 3 0-1 1-2 1-3 2 1 2-1 3 0v1 2h1 1c-1 2-3 2-4 4-2-2-2-3-3-4l-2-2z" class="y"></path><path d="M321 654c2 1 2-1 3 0v1 2h1c-1 1-1 1-2 1l-1 1h-1l-1-2c0-1 1-2 1-3z" class="AJ"></path><path d="M531 345c1 1 1 2 1 3s0 1 1 1c1 3 2 8 1 10v1c-1 0-1 0-2 1h-2-1l1-2c1-1 1-2 2-2v-1c0-2 0-2-1-4h0v-2-2-3z" class="F"></path><path d="M179 560c2 0 5-1 7 0 0 0 1 1 2 1h1c-1 1-2 1-2 1-3 0-5 1-8 2 0 0-1-1-2-1l-2 1v-1l1-2 3-1z" class="G"></path><path d="M179 560c1 0 3 0 4 1-1 1-4 1-6 2l-2 1v-1l1-2 3-1z" class="v"></path><path d="M415 260h1c1 0 2 0 2 1 2 1 2 3 3 5h-1c0 2 1 2 1 4-2-2-4-5-6-6h0c-2-1-2-2-2-3 1-1 1-1 2-1z" class="z"></path><path d="M339 276c1-1 2-1 3-1l1 1h1c1 0 2 1 4 1 2 1 5 1 7 0 2 2 4 3 5 5 0 0-1 0-1-1-1 0-2-1-3-1-7 1-11-3-16-4h-1z" class="P"></path><path d="M425 477v1l2 1c0 1 1 2 2 3v1h0c-1 0-1-1-2-2-3 3-9 4-13 5 0-1 0-2 1-2 1-1 2-1 3-2s2-1 3-1h1 1l2-2h0v-2z" class="C"></path><path d="M368 556c-1-1-1-1-1-3h2 0c0-1 0-1 1-1 0-2 4-3 6-4 1 1 2 1 3 1 0-1 1 0 1 0-1 0-1 1-2 2-4 1-7 3-10 5z" class="M"></path><path d="M262 595s1 0 1-1c1-2 3-3 4-4h1l-1 1 3-3s1 0 2-1c-3 4-7 7-11 10-6 5-12 10-18 13 1-1 2-2 3-2l1-2c2-1 3-2 5-3 0 0 0-1 1-1h1l4-4c2-1 2-2 4-3z" class="Q"></path><path d="M519 434l4 3h0v1c1 2 3 3 4 5h0v3l-8-6v-2h2c-1-1-2-2-2-3v-1z" class="u"></path><path d="M445 377h0l2-2v-2h1v1c1 0 1 0 2-1-1 2-1 4-3 5-1 1-3 1-4 1-1 5 0 10 0 15v23c0 4 0 9-1 13v-17-33c1-1 1-2 1-2v-1h2z" class="d"></path><path d="M106 344l1 1 1 2 1 4h0c0 2 2 4 2 6h-1l-2-2c-1 0-2-1-2-2-1-1-1-2-2-3l1-2c1-1 1-1 0-2 0-1 1-1 1-2z" class="w"></path><path d="M384 551h1c2 1 4 1 6 2h3c1 1 3 3 3 4h1-1v1c-2-1-6-2-7-3-2-1-3-1-5-2h0c-1-1-2-1-4-1 1-1 2-1 3-1z" class="B"></path><path d="M335 321c1 1 1 2 2 3 0 2-1 3 0 4h1 8s0 1-1 1h1l-2 1h1v1h-11v-1c1 0 1 0 2-1-1-2-1-6-1-8z" class="L"></path><path d="M338 328h8s0 1-1 1h1l-2 1h1-1c-2-1-4-1-7-2h1z" class="C"></path><path d="M429 513c-1-1 0-3-1-4s-2-1-2-2h0l2 1h0v-1l-1-1-1-1h0v-2c1-1 0-1-1-2-1 0-1-1-2-2l1-1 3 2h0c3 2 4 5 6 7v1h-1c-1 0-1-1-1-1v-1c-1-1-2-2-4-3v1c0 1 0 1 1 2v1-1l1-1v1c1 1 1 2 2 2v1c-1-1-1-1-2-1h0c1 2 2 2 1 4l-1 1z" class="M"></path><path d="M464 483l1-1c0-1 1-4 0-6v-2l-1-1v-1c2 2 3 4 4 6l-2 1h1c0 1-1 2 0 3v11h0c-1 1-1 1-2 1v-5c0-1 0-5-1-6zm14-15l2 2h0v1c1 1 1 2 1 3h1c0 1 0 1 1 2v-1h2v-4 2 1 2l1 1h0c0 2 2 5 1 6-1 0-1 0-1 1h0s0 1-1 1c-1-1-1-1-1-3-1-2-1-4-2-6-2-3-2-5-4-8z" class="Y"></path><path d="M425 515h2v1c1 0 1 2 1 2v2c-1 1-2 2-2 4h0v1l-1-1c-1 0-1-1-1-2h1c-1-1-1-2-1-2-1-2-2-3-3-4l1-1h2 1z" class="l"></path><path d="M425 522l1 2v1l-1-1c-1 0-1-1-1-2h1z" class="C"></path><path d="M364 218c2 0 3 1 5 0h0 1-1v1c1 0 2 0 2 1h2c2 0 3 0 5 1l-8 2c0-1 0 0-1 0v1h-1-2c1-1 1-1 1-2l-2-1-1-1v-2z" class="B"></path><path d="M83 483c1 1 1 2 2 3h0v3c1 1 1 2 1 2l2 6c1 2 3 4 3 6 0 1 0 1 1 2v2 1c-5-8-8-16-9-25z" class="z"></path><path d="M292 582c0 1 0 1 1 1 0 1 0 2-1 3v3c-1 2-1 3-1 4-1 2 0 6 0 8s1 3 1 4 1 1 0 1v1c-1-1-1-3-2-4-1-5-1-12 0-17l1-1v-3h1z" class="l"></path><path d="M292 582c0 1 0 1 1 1 0 1 0 2-1 3 0 0-1 0-1-1v-2-1h1z" class="C"></path><path d="M290 603v-2-1h0l1-1v2c0 2 1 3 1 4s1 1 0 1v1c-1-1-1-3-2-4z" class="F"></path><path d="M493 512l2 2h0l1 1v3c-1 1-3 2-4 4l-2 1-1-2c-1 0-1 0-2 1 1-2 2-3 3-5s3-3 3-5h0z" class="B"></path><path d="M489 521v-1h1c1 0 1 1 2 2l-2 1-1-2z" class="E"></path><path d="M493 512l2 2h0l-1 1c0 1 0 1-1 2l-1 1c-1 0-1 0-2-1 1-2 3-3 3-5h0z" class="X"></path><path d="M163 425c1 0 3 1 4 1 0 1-1 1-1 1h-1l-1 1c-1 1-1 1-2 1h-1c-1 1-2 0-4 1-1 0-1 0-1-1v-2h-3v-1h0l3-1 6 1 1-1z" class="AO"></path><path d="M382 291c1 0 2 1 3 1v1c-2 4-4 7-7 9h-3v-1c1-1 1-2 2-3 1-2 3-2 3-3l1-1v-1s1-1 1-2zm103 180h-1c-1 0-1 1-3 0-1-2-2-3-2-4v-1c2 2 1 3 4 3h1 0c0-1 1-1 1-1l1 1h0v-2h0c1 1 1 1 1 2v1c0 1 1 2 3 2h1l1 1v-1l-1-2h-1c0-2-2-5-3-6h0 1l2 4 1 1v1c1 0 1 1 1 2 1 0 1 1 1 2v1h0v1h0c-1 0-1 0-1-1l-1-1h-3c-1 0-1 0-1-1h-1-1v-2z" class="L"></path><path d="M384 268c2 4 3 9 2 13 0 1-1 2-1 3-1 2-1 2-3 3 0 2 0 3-2 4v-3c1-1 1-2 2-4v1l1-1-1-1c1-1 1-2 1-4 0-1 1-1 1-2v-1c1-3 1-5 0-8z" class="z"></path><path d="M347 328v-1c0-1-1-2-1-3v-1-2h4 5l1 1 3-2h0l-6 4c-2 2-4 3-6 4z" class="S"></path><path d="M500 560l1-1 1 1v4l1 1h3l-1 1c-1 0-2-1-3-1 0 1 0 1 1 2-1 0-1-1-2-1h0l1 1c0 2 1 4 1 7 1 1 1 2 1 3s1 1 1 2l-3-4-3-6c0-3 0-6 1-9z" class="X"></path><path d="M364 316c2-1 3-1 4-2v1l-2 2-6 3h-1 0l-1-1c-1 0-1 0-2-1h-1v1c1 1 1 1 2 1l-1 1v-1h-2c1-2 0-3 1-5 2 1 4 1 6 1 1-1 2-1 3 0z" class="C"></path><path d="M364 316c2-1 3-1 4-2v1l-2 2-6 3h0c0-1 0-1-1-1v-1h2c1-1 2-1 3-2h0z" class="B"></path><path d="M337 310l1-1v19h-1c-1-1 0-2 0-4-1-1-1-2-2-3 0-2 1-6 0-7v-1c0-1 0-2 1-3h1z" class="u"></path><path d="M335 321c0-2 1-6 0-7v-1c0-1 0-2 1-3h1 0v2c-1 2 0 3 0 5v7c-1-1-1-2-2-3z" class="l"></path><path d="M491 535h4l-1 2 3-1 1 1h6c-4 1-9 1-13 2h-2c-1 0-3 0-5 1-1 0-5 2-6 2l-1-1 8-2 1-2c2-1 3-1 5-2z" class="j"></path><path d="M491 535h4l-1 2c-3 1-6 1-9 2l1-2c2-1 3-1 5-2z" class="AK"></path><path d="M315 534c1-2 2-5 4-7h2l-1 1c-2 3-3 6-5 9-2 2-4 3-6 5-3 3-6 6-8 10l-2 3-2-2 4-5h1c4-3 8-8 11-12l2-2z" class="B"></path><path d="M174 578c1 0 2-1 3-1v5h0 1c1 1 0 1 0 3h-1v-1h0c-1 1-2 1-3 1-1 1-2 2-3 2v-3c0-2 1-3 2-4v-1l1-1z" class="C"></path><path d="M360 259c2-1 3-2 5-2l1-1c0-1-1-1-1-2h1c1 0 2 0 3 1v1c3 2 5 1 7 2l2-1v1 3c-1-1-2-2-4-2h-4c-3-1-7 0-10 0zM97 543h0l-2 2c-1 0-2 2-2 2l-1 1v1c-1 1 0 4 0 6-1 0-1 0-2 1h0c0 1-1 1-2 2v-1l-2-2v-1l-1-1 3-3 7-6 2-1z" class="J"></path><path d="M430 224l5-8 4 8c1 1 2 2 3 2 1 2 2 3 3 4-3-1-5-4-7-5s-4 0-6-1h0-2z" class="j"></path><path d="M544 584h1c1 0 2-1 3-2h0c0 2 0 3-2 5l-4 2c-3 1-6 2-9 2 0-1-1-1-1-1h-3c6-1 10-3 15-6z" class="C"></path><path d="M462 337c1 1 2 1 4 1v1s0 1 1 1v1c-1 0-3 1-4 0h0c-1-1-2-1-3-1l-3 1h-3c0 1-1 1-2 1s-1 0-1 1h0c-1 1-1 1-1 2v-7l1 1v1h1l1-1c3-1 5-2 9-2z" class="Y"></path><path d="M438 506c1-2-3-4-3-6v-3l1-1h0v1l5 5s0 1 1 1c0 1 0 1 1 2v3 1 2h0v1c0 1 0 3-1 5l1 1h0c-1 1-1 1-1 2v1l-1-1 1-1c0-1-1-2 0-3 0-2 1-6-1-8 0-1-1-2-1-2 0-1-1-2-2-2h0c0 1 1 1 1 2h-1z" class="J"></path><path d="M173 562l3-1-1 2v1l2-1c1 0 2 1 2 1-3 1-7 3-10 5h0l2-2-1-1c-1 1-2 2-4 2l-1-1c0-1 1-2 2-3h0c2-1 4-1 6-2z" class="Z"></path><path d="M173 562l3-1-1 2v1l-5 2h-2v-1l-1-1c2-1 4-1 6-2z" class="h"></path><path d="M385 186c-5 0-10 0-15 2-2 1-3 2-5 3l-1-1s0-1-1-2h0v-1c5-2 9-3 15-3l3 1h0l-2 1c1 0 4-1 6 0z" class="R"></path><path d="M368 314h2c2-1 3-1 4-2h1c0 1 0 1 1 1 0 1 0 2-1 2v2l-1 1h0l1 1-1 2h1v2l-1 1h0c1 2 1 4 1 5l-1 1h0c-1-1-1-2-1-4v-4c0-2 0-3 1-5l-5-1-1-1v-1z" class="E"></path><path d="M497 452c2 3 2 6 3 9v4c0 3-1 5-1 7l-1 1c-1-3-1-6-2-9 0-3 1-6 0-9h1v-3z" class="D"></path><path d="M459 562c0 1 1 1 2 2h1 1c1 2 2 3 3 4 0 0-1 1-1 2h-3c-1-1-1-2-3-2l-1-1h-1c0 1 1 1 2 2h-1l-5-3 1-1-1-1h1l2 1c1-1 2-1 3-3z" class="t"></path><path d="M459 562c0 1 1 1 2 2h1l-1 1v2h0-1l-2-2h0l-1 1-1-1c1-1 2-1 3-3z" class="I"></path><path d="M502 404c1 1 2 1 2 2 4 1 6 4 8 7l-15-2h2v-1h2 4v-1l-2-1h0l-3-1h-1l1-1h-1c2-1 2-1 3-2z" class="C"></path><path d="M502 404c1 1 2 1 2 2v1l-3-1c1 1 0 1 1 1l1 1-3-1h-1l1-1h-1c2-1 2-1 3-2z" class="X"></path><path d="M381 247h1 0c-1 1-3 4-4 4-1 1-3 1-5 1h-3v1h-1-1l-1-1c0-2 3-3 4-4 4 0 7-1 10-1z" class="L"></path><path d="M442 226c5 3 9 5 13 8 2 1 3 2 4 4h-2-6c0-1-1-2-1-3-1-1-1-2-2-3l-3-2c-1-1-2-2-3-4z" class="T"></path><path d="M448 232c2 1 5 3 7 4l2 2h-6c0-1-1-2-1-3-1-1-1-2-2-3z" class="b"></path><path d="M465 332c3 0 6 1 9 3v2 1c2 3 2 4 3 8-1-2-3-4-5-5v-1h-1l-1-1s0-1-1-2h-1c0-1 0-1 1-2h-2c-1-1-2-1-2-2v-1z" class="k"></path><path d="M469 335c0 1 0 1 1 1 0 1 0 1 1 2l1 1v1h-1l-1-1s0-1-1-2h-1c0-1 0-1 1-2z" class="e"></path><path d="M443 570c5 0 8 2 11 5l1 1v2c-1-1-2-2-4-2 0 1 1 3 2 4h-1c-1 0-2-1-2-2l-3-3-2-1c-1-1-3-2-5-3 1 0 2 0 3-1z" class="AK"></path><path d="M445 574v-1c2 0 3 0 4 1v1h-2l-2-1z" class="AT"></path><path d="M106 353c0 1 1 2 2 2l2 2h1c1 1 2 3 3 4l2 4c-2 0-9-5-11-6h0l-1-1v-1-3l1-1h1z" class="N"></path><path d="M106 353c0 1 1 2 2 2h-1c-1 0-2-1-3-1v1c1 1 1 2 1 4l-1-1v-1-3l1-1h1z" class="W"></path><path d="M420 261c-1-4 1-9-1-12l-1-4h0 1c1 1 2 1 2 1 1 1 1 1 2 1h1 0c0 3 0 6-1 8v2l-1 1c0 1-1 2-1 3l-1 1v-1z" class="X"></path><path d="M419 245c1 1 2 1 2 1 1 1 1 1 2 1 0 1-1 2-2 2-1-1-1-2-2-4z" class="J"></path><path d="M111 561c0-1 1-2 1-3h1v-1c-1-1-2-2-3-2-2-1-3-1-5 0-1 0-3 2-3 3h0-1c1 0 1-1 1-2 1-2 3-3 5-3 4 1 6 2 9 4 0 2 0 3 1 4h0v5c-2-2-2 0-4-1v-1l-1-1c0-1 0-2 1-2h-2z" class="W"></path><path d="M113 561h1c1 0 1 1 2 2-1 1-2 1-3 1l-1-1c0-1 0-2 1-2z" class="AF"></path><path d="M467 427h-3-3c0-1-2-1-2-1-1 0-1 1-1 1h-4v-1h1 2s1 0 2-1h0l-3-2v-3h0 3l1 1-3-3s-1 0-1-1h-1l1-1v1l2 1c2 1 2 2 3 3 1 0 2 1 3 2s2 3 3 4h0z" class="L"></path><path d="M451 339c2-1 4-2 5-2s1 0 2-1h0-2v-1h2c1 0 2 0 2-1 1 0 1-1 1-1 1-1 3-1 4-1v1c0 1 1 1 2 2h2c-1 1-1 1-1 2h1c1 1 1 2 1 2l-4-1c-2 0-3 0-4-1-4 0-6 1-9 2l-1 1h-1v-1z" class="F"></path><path d="M465 333c0 1 1 1 2 2h2c-1 1-1 1-1 2h1c1 1 1 2 1 2l-4-1c-2 0-3 0-4-1h-1v-1c1 0 5 1 6 1-2-1-4-1-5-2v-1h2l1-1z" class="AA"></path><path d="M350 191c0-2 1-5 2-7h1v1 2s1-3 2-3c1 2 1 4 1 6h1 1c0 1 0 1 1 2-1 0-2 1-2 2l-1 1-1 1c0 1 0 1-1 2v-1c1-1 0-2 1-4v-1-3c1-1 0-2 0-3h0l-1-1v2 1c-1 0-1 1-1 1v2c0 1 0 2-1 3v2c1 0 0 1 0 2 1 1 1 2 1 3 1 2 3 4 3 6 0 0-1 0-1-1 0 0 0-1-1-1v-1l-2-2v-1-1c-1-1-1-2-1-4h0v-2-1c1 0 0-2 1-2v-1h0v-1c0 1 0 1-1 1v2h0v-2h-1v1z" class="M"></path><path d="M464 489h0l1-1v1 5c1 0 1 0 2-1l-1 4v2c-1 4-4 7-6 10 0 1-1 2-1 2v-4c0-1 1-2 2-4v-2c1 0 1-1 1-2 1-1 1-2 1-4h0c1-2 1-4 1-6z" class="C"></path><path d="M465 494c1 0 1 0 2-1l-1 4h-1v-3z" class="M"></path><path d="M380 549l2 1 2 1c-1 0-2 0-3 1 2 0 3 0 4 1h0v1h-3l-7 1-9 3h-1l3-2c3-2 6-4 10-5 1-1 1-2 2-2z" class="N"></path><path d="M380 549l2 1 2 1c-1 0-2 0-3 1 2 0 3 0 4 1h-2-6 0c0-1 1-1 1-1v-1c1-1 1-2 2-2z" class="D"></path><path d="M341 270c1 0 3-1 4-1 0 1 0 1-1 2h1l3-1v2c-2 1-2 2-4 2v1 1h-1l-1-1c-1 0-2 0-3 1-1 0-4-1-5-1-2 0-5 1-7 1 2-3 6-2 9-4h0c1 1 2 1 4 1l2-2v-1l-2 1-1-1h2z" class="u"></path><path d="M424 520s0 1 1 2h-1c0 1 0 2 1 2l1 1-1 1c0 1 0 2-1 2h-1c-1 0-1 0-2 1-2 0-3 0-4 1v2h-1 0v-1c0-1 0-1 1-2h-1c0 2 0 3-1 4v1 1c0 1 0 1-1 2l1-3-1-1h0 0c0-1 0-3 1-4v-2h2v-1c2 0 3-1 4-2l-1-1h0l1-1h-1l2-1v-1h2z" class="X"></path><path d="M424 520s0 1 1 2h-1-2c0 1-1 1-2 1l1-1h-1l2-1v-1h2z" class="J"></path><path d="M425 524l1 1-1 1c0 1 0 2-1 2h-1c-1 0-1 0-2 1-2 0-3 0-4 1v2h-1 0v-1c0-1 0-1 1-2h-1c0 2 0 3-1 4v1 1c0 1 0 1-1 2l1-3c0-2 0-4 1-6v1c1-1 1-1 2-1s1 0 1-1c2 0 4-1 6-3z" class="S"></path><path d="M144 270c1 1 2 1 2 2l1-1h0c1 2 1 2 2 3h0l1 1 1 1c-2 1-3 2-4 4v-1c-1-1-1-1-2 0 0 0-1 0-1 1h-1c-1-1-1-2-2-4 0-2 0-2 1-3 0-1 1-2 2-3z" class="B"></path><path d="M142 273l1 1v3c-1 0-1 0-2-1 0-2 0-2 1-3z" class="C"></path><path d="M144 270c1 1 2 1 2 2-1 1-2 2-2 3h-1c0-1 1-2 1-2h-2c0-1 1-2 2-3z" class="AF"></path><path d="M149 274h0l1 1 1 1c-2 1-3 2-4 4v-1c-1-1-1-1-2 0 0 0-1 0-1 1h-1c-1-1-1-2-2-4 1 1 1 1 2 1h0 3c1 0 1 0 2-1v-1l1-1z" class="L"></path><path d="M450 274h5v8c0 2-4 4-5 6v-6-2-6z" class="W"></path><path d="M499 572c1 3 2 4 1 6v2h-1c-1 1 0 5 1 6h0l3 8c1 1 1 3 2 4h-2c-1-2-2-3-3-5l-2-3v-3c-1-1-1-2-1-3l-1-1c0-2 0-3 1-4s1 0 2 0c0-1 0-1 1-2v-1c0-1-1-2-1-4z" class="M"></path><path d="M152 247h0v-1h-1c-1 0-3-1-4-2 1 0 1 0 2-1 2-1 9-3 11-2l2 4c-1 0-1 1-2 1h-2v1h-6z" class="D"></path><path d="M155 243c1-1 2-1 3-1 1 1 2 2 2 3v1h-2 0-4l1-1h1v-1l-1-1z" class="N"></path><path d="M155 243h3v1h-2l-1-1z" class="Q"></path><path d="M352 585l1 2v-1c1 1 0 3 2 4-1 1-1 2-2 4 0 7-1 14-5 20-2 3-4 6-7 9v-1l1-1c3-5 7-11 9-16v-1c1-3 1-7 1-10 0-2 0-3-1-4v-1-1h1v-3z" class="t"></path><path d="M353 587v-1c1 1 0 3 2 4-1 1-1 2-2 4v-7z" class="J"></path><path d="M450 262l4 1c1 1 1 2 1 3v2 9 5-8h-5v-1c0-2 0-4-1-5v-1c1 0 1-1 1-2-1-1 0-1 0-3z" class="W"></path><path d="M450 273h1c1 0 3-1 4-1v-3h-3-1l4-1v9 5-8h-5v-1zm-293-44c0-3 1-6 2-8l1 2c1 3 2 4 4 5v1c-1 2-1 5-1 8-1 0-1 0-1-1-1-1-2-1-3-2h0c-2-2-2-3-2-5z" class="E"></path><path d="M506 471h1c1 1 1 1 1 2 0 5-2 11-6 15l-1 1h0c0-4 1-8 3-12 0-2 1-4 2-6z" class="B"></path><path d="M402 500c2 0 3 1 4 1 1 1 2 1 3 2-2 0-3 1-5 1-2 1-5 0-6 0-3 1-6 2-9 2h-1v-1c1-1 3-1 4-2 1 0 2-1 2-2 1 0 3 0 3-1h5z" class="AK"></path><path d="M162 543v-1c-1-1-3-3-4-5 1 1 3 3 4 3l3 3c1 1 3 2 5 3 0 0 1 1 2 1h1c1 0 2 1 2 2h1v1h1c0 1 1 1 1 1l3 3h1 0c1 1 1 1 2 1 1 1 1 1 2 1h0c-1 1-1 1-2 0h0c-2-1-3-1-4-2s-2-1-3-2c-1 0-2-1-3-1-1-1-2 0-2-1-2-1-4-1-6-2l-1-1h1c-1-1-3-2-4-3v-1z" class="L"></path><path d="M408 574c2-1 7 1 9 2 1 0 1 0 2-1l1-1c1 1 1 1 2 1s2 1 3 2 2 2 2 3h-5c-2 0-3 0-5-1-2 0-3-1-5-1-1-1-3-2-4-4z" class="E"></path><path d="M420 574c1 1 1 1 2 1s2 1 3 2c-2 1-6 0-8-1 1 0 1 0 2-1l1-1z" class="B"></path><path d="M287 617c1 0 2-1 3 0 2 1 5 4 6 5 3 3 4 5 6 8l5 5c-1-1-1-1-2-1h0c-1-1-1-1-1-2-1 1-1 1-1 2h-2l-2-2-2-2h2c-1-1-1-2-1-2l-1-1-1-1c-2-1-3-2-4-4h0v-1h-1v-2c-1 0-2 0-3-1 0-1 0-1-1-1h0z" class="u"></path><path d="M414 542c2 2 5 3 8 5v1h-3c-1 2-2 5-3 7v-1l-2 2c0-1-1-1-1-1v-1h-1 0l2-2-1-1 2-2h-2-1c-1-1 0-1 0-2v-2-1c1 0 2-1 2-2h0z" class="J"></path><path d="M415 549v-1l1-1c-1-1-2-1-2-3h1c2 0 3 2 4 3-1 1-3 1-3 3-1 0-1 1-2 2l-1-1 2-2z" class="F"></path><path d="M474 478h2c1 1 1 2 1 2l-1 1h1c1 1 1 2 1 4h0v1h-1v2l-3 2h-2c-1-1 0-2 0-3-1-2-1-4-1-6v-1l1-1h1l1-1z" class="z"></path><path d="M472 487l1-1 1 1c-1 1-1 1 0 2v1h-2c-1-1 0-2 0-3z" class="t"></path><path d="M473 481v2h1v-1c1 1 2 2 2 3s-1 1-1 2h0c0 1 0 1-1 1v-2c0-1-1-1-1-1v-1l-1-1 1-2z" class="F"></path><path d="M474 478h2c1 1 1 2 1 2l-1 1h1c1 1 1 2 1 4h0v1h-1v-4h-3v1h-1v-2l1-1-1-1 1-1z" class="J"></path><path d="M474 478h2v3h-1l-1-1-1-1 1-1z" class="E"></path><path d="M442 248c1 1 1 2 1 3v9c0 7-1 64 0 66h0v3c1 0 1 1 1 1h2v-1h0c1 0 0 0 1-1l1-1c1-2 0-4 0-6 1-2 1-5 1-8v-16h0v35c-1 0-3-1-4-1s-2-1-3-1v-20c1-4 0-9 0-14v-32-7-3-6z" class="B"></path><path d="M321 663l1-1h1l1 1v2h1l2-1h1v1c0 1 0 2-1 3h-5 0l1 1c0 1-1 1-1 2-1 0-2 0-3-1s0-1 0-2c-1 0-1 0-2 1l-1-1c-1 0-1-1-2-2 1-3 3-1 4-2l1-2v2h1s0-1 1-1h0z" class="I"></path><path d="M321 663l1-1h1l1 1v2h0c-1 0-2 0-3 1h-1c0-1 1-2 1-3z" class="G"></path><path d="M388 506h1 1c0 1 1 1 2 1 2 0 4 1 7 3-5 1-10 2-14 5l-5 2c1-1 3-2 4-3l-1-1h0c2-2 5-3 7-4-3 0-5 1-8 1 2-2 4-3 6-4z" class="w"></path><path d="M360 212c1 0 2-1 4-1 0 1 0 1 1 2 3 2 7 0 10 2h0 3c-1 1-1 1-2 1l-1 1s-1 1-2 1h-1-2l1 1h0c1 0 2 0 2 1h-2c0-1-1-1-2-1v-1h1-1 0c-2 1-3 0-5 0v2c-1-3-2-5-4-8z" class="C"></path><path d="M364 218l1-2v-1h1v2h2c1-1 3-1 4-2h1 0 1s-1 1-2 1c0 1-2 1-2 2h-1 0c-2 1-3 0-5 0z" class="D"></path><path d="M447 355l1 1v-1c0-1 0-1 1-2v-1l1 2v3s-1 0-1 1c0 0 1 3 1 4v1 6 4c-1 1-1 1-2 1v-1h-1v2l-2 2h0c0-2 1-3 1-4h-1v-1c0-1 1-1 2-1l1-1v-2h-2-1c0 1-1 0-1 0h-1l2-1c1 0 1 0 2-1-2-2 0-6-1-7v-1l1-1v-2z" class="t"></path><path d="M494 588l1-1c0 2 2 5 3 5 0 1 1 1 2 1 1 2 2 3 3 5h2c2 2 3 3 5 4 1 2 2 3 3 4l-4 1c1-1 1-1 1-2l-2-1c-3-1-8-3-10-6v-2c-2-2-3-6-4-8z" class="N"></path><path d="M498 596c1 1 2 3 3 4 3 2 7 4 10 4-1 0-2-1-3-2-2-1-4-3-5-4h2c2 2 3 3 5 4 1 2 2 3 3 4l-4 1c1-1 1-1 1-2l-2-1c-3-1-8-3-10-6v-2z" class="B"></path><path d="M485 473h1v2c0 1 1 1 1 1v3 1c1 1 0 2 1 3v2l1-1v-1h0c1 1 0 2 1 2v3l1 1c0-1 0-2-1-3v-4l1 1v2 2 2 1c-2-2-2-4-3-6l-2 18-2 2-1-1c1-1 1-1 0-2v-1l1-1c1-2 0-5 2-6v-1-1h-2 0c0-1 1-3 0-4h-1v1-2c0-1-1-2 0-3 0-1 0 0 1-1 0 2 0 2 1 3 1 0 1-1 1-1h0c0-1 0-1 1-1 1-1-1-4-1-6h0l-1-1v-2-1z" class="J"></path><path d="M483 486c0-1-1-2 0-3 0-1 0 0 1-1 0 2 0 2 1 3 1 0 1-1 1-1h1v1 2h0-2c0 2 0 3 1 4h-2 0c0-1 1-3 0-4h-1v1-2z" class="AL"></path><path d="M347 200c6 4 10 11 12 17v3l-3-5h0c0 3 2 5 0 8l-2-7c-1-4-4-7-6-11h-1v-3-2z" class="Z"></path><path d="M188 570v1c-1 1-2 2-4 3l2 1-1 1-1 1-4 5c0 1-1 2-2 3 0-2 1-2 0-3h-1 0v-5c-1 0-2 1-3 1l-1-1c3-2 5-3 7-4 1-1 2-2 3-2 2 0 3 0 4-1h1z" class="AF"></path><path d="M179 577v-1c2-1 3-1 5-2l2 1-1 1-1 1-4 5v-1l-2-1h1c0-1 0-1-1-2l1-1z" class="w"></path><path d="M179 577v-1c2-1 3-1 5-2l2 1-1 1-1 1c-1 0-1-1-1-1h-1c-1 0-2 1-3 1h0z" class="N"></path><path d="M340 227c0-1 0-1 1-2h1v2h1v1h1 0c-1 2-1 2-1 4h1 0c2 2 5 3 6 5l-1 1h-1c0-1-1-2-2-2-3 0-8 1-10 3h0-1l2-2v-3c0-1 1-1 1-2s0-3 1-4v-1h1z" class="y"></path><path d="M339 227h1 0c0 1 0 1 1 2h0c-1 2 0 2 0 3s-1 1-1 2c-1-2-1-5-1-6v-1z" class="l"></path><path d="M337 237v-3c0-1 1-1 1-2s0-3 1-4c0 1 0 4 1 6h2c-1 1-1 1-2 1-1 1-2 2-3 2z" class="M"></path><path d="M379 276v1l1-1 1 3c0 1 0 1-1 2h1v1h-1 0c0 2 0 4-1 6v1h-1 0 0l-2 4c-1-1-1-1-2-1 0-1 1-3 1-4 0-2-1-4-2-6 0-1-1-2-1-2 0-1 1-2 2-2h0c1 0 1 1 2 1s1 0 1-1c1 0 1-1 2-2z" class="V"></path><path d="M380 276l1 3c0 1 0 1-1 2h1v1h-1c0-1-1-1-2-2h0c0-1 1-2 1-3l1-1z" class="b"></path><path d="M378 289v-5l-1-1 2-1h1c0 2 0 4-1 6v1h-1 0 0z" class="AO"></path><path d="M271 599h1c0 3-1 5-2 8 2-2 3-4 4-6 1-1 1-1 1-2 1 1 2 1 2 2 0 2-2 5-3 7-1 1-4 6-5 6s-1-1-2-2h0c-1-1-1-2-1-3 0-3 1-6 4-8 1-1 1-1 1-2h0z" class="J"></path><defs><linearGradient id="C" x1="454.913" y1="249.703" x2="449.485" y2="261.55" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#414140"></stop></linearGradient></defs><path fill="url(#C)" d="M449 250l1-1c2 0 3-1 5 0v10c0 2 1 5 0 7 0-1 0-2-1-3l-4-1c0-1 0-3-1-5 0-2 1-5 0-7z"></path><path d="M284 620v1c-1 1-1 2-2 3 1 3 2 4 5 6 2 0 4 1 6 0 1-1 2-2 2-3s0-1-1-2c-1 1-1 1-1 2v-1c0-2-1-2-2-3 0 0 1 0 1-1 1 2 2 3 4 4l1 1 1 1s0 1 1 2h-2l2 2c-2 0-3 1-5 1 0 1-1 1-2 1 0 0-1 0-1-1-2 0-3-1-5-1-2-1-3-1-4 0h-1c0-1 1-2 1-2l1-1c-1-1-1-2-2-2v-4l3-3z" class="AO"></path><path d="M292 622c1 2 2 3 4 4l1 1 1 1s0 1 1 2h-2c-1 2-3 2-5 2 1-1 3-2 4-3v-1l-1-1c0-1 0-1-1-2-1 1-1 1-1 2v-1c0-2-1-2-2-3 0 0 1 0 1-1z" class="C"></path><path d="M93 589c-3-1-5-2-7-4-7-4-12-12-13-19-2-6 0-11 1-16h1v2c-2 6-3 11 0 17l2 1c2 6 6 12 13 15 0 1 1 1 2 1h1v3z" class="AK"></path><path d="M374 292c1 0 1 0 2 1l-1 1v3c-5 5-12 8-20 8h-5 2v-2l8-1 4-2h2c3-1 5-3 7-5v-1c1-1 1-1 1-2z" class="AE"></path><path d="M374 292c1 0 1 0 2 1l-1 1c-1 2-3 4-4 5-1 0-2 1-3 1-3 1-4 2-7 2h-1l4-2h2c3-1 5-3 7-5v-1c1-1 1-1 1-2z" class="T"></path><path d="M325 285c1 1 1 1 1 2h1v1 4 13 25c-2-2 0-6-2-9v-8-6-14-5-3h0z" class="G"></path><path d="M325 293h0c0 1 1 2 1 3v3 3 8c-1 1-1 2-1 3h0v-6-14z" class="g"></path><path d="M189 569h2l1 1-1 1c3 1 5-1 7-1l1 1-2 2c-2 2-4 4-7 6l-1 1-1-1h-2c0-1-1-1-1-3l1-1-2-1c2-1 3-2 4-3v-1h0l1-1z" class="J"></path><path d="M189 569h2l1 1-1 1c-1 1-4 4-5 4l-2-1c2-1 3-2 4-3v-1h0l1-1z" class="D"></path><path d="M190 579l-2-1h0c-1-1-1-1 0-2 0-1 1-1 2-1 3-1 4-3 7-2-2 2-4 4-7 6z" class="X"></path><path d="M179 285l1 1c1 0 1 0 2-1v1 1c2 2 3 3 4 6h-1-1v2h-1c-1 0-1 0-2-1h-9c0-2 1-3 2-4v-1c0-1 0-2 1-3l1 1c1 0 2-1 3-2z" class="W"></path><path d="M356 195l1-1c0-1 1-2 2-2l3 2c1 0 2-1 3-1 2-1 6 1 9 2-1 0-2 0-3 1 0 0-1 0-1 1-1 0-1-1-2-1s-2 2-3 2c-2 1-3 2-5 2-1 0-1 1-2 2h-1c-1-2-2-3-1-5v-1-1z" class="D"></path><defs><linearGradient id="D" x1="528.105" y1="347.223" x2="526.507" y2="361.571" xlink:href="#B"><stop offset="0" stop-color="#41403f"></stop><stop offset="1" stop-color="#626261"></stop></linearGradient></defs><path fill="url(#D)" d="M527 348l1-1h2s0 1 1 1v2 2h0c1 2 1 2 1 4v1c-1 0-1 1-2 2-2 3-4 5-7 7v-1c0-1 0-3 1-4 1-2 1-5 2-7l1-6z"></path><path d="M531 350v2h0c1 2 1 2 1 4v1l-1-1-3 4-1 1h-2c2-1 3-3 4-5 0-1 0-3 1-4 1 0 1-1 1-2z" class="X"></path><path d="M525 361h2l1-1 3-4 1 1c-1 0-1 1-2 2-2 3-4 5-7 7v-1c0-1 0-3 1-4v1l1-1h0z" class="m"></path><path d="M496 455h0c1 3 0 6 0 9 1 3 1 6 2 9l1-1c1 5 0 9 0 13 1 4-1 8-2 12v-1c0-2 0-1-1-1v-1c0-1 0-1-1-2 2-1 1-4 1-5 1-7 0-15-1-22l1-1v-3c-1-2-1-4 0-6z" class="C"></path><path d="M365 585v1c-1 1-1 0-1 1 1 1 1 2 3 2l-1 1 1 1c0 1 1 3 0 4h-1c0 2 2 4 3 5l3 2h-2 0 0c0 1 0 2 1 3h0c-3 0-5-3-7-5h-1c2 2 2 5 3 7-1 0-2-1-3-1-2-1-1-2-2-3h0-1c1-1 1-1 1-2l-1-1c0-2-1-3-1-5l1 1c1 2 2 3 3 4v-1h0c-1-1-1-3-1-4h1c1-1 3-2 3-4v-1l-1 1-1-1c-1-1-2-1-2-3 0 1 0 1 1 1v-1h0c1-1 1-2 2-2z" class="J"></path><path d="M161 264c1 0 1 0 2-1h1l1 1c1-1 0-1 0-2 1 0 2-1 2-2 2 1 3 4 5 5 2 2 2 5 3 7v1h0c0 1-1 1-1 2h0c-1 2-1 3-1 5h-1s0-1-1-1c-1 1 0 3-1 4v-6c0-1-1-2-1-4-1-1 0-3-1-4v-4c-1 0-1 0-1-1v-1h-1v1c0 1-1 1-1 1l-1-1h-3z" class="C"></path><path d="M423 257c0 1 0 1 1 1 2-1 2-2 2-4l-1 10v5l-1 5-2 6-1-1c0-1-1-2-1-3h0v-1c2-1 1-3 1-5s-1-2-1-4h1c-1-2-1-4-3-5h2v1l1-1c0-1 1-2 1-3l1-1z" class="M"></path><path d="M422 258c1 1 1 1 1 2v1h-2c0-1 1-2 1-3z" class="Y"></path><path d="M424 274v-2c0-1 0-1-1-2 0-1 0-1-1-2h0c1-1 3-2 3-4h0v5l-1 5z" class="E"></path><path d="M491 487c0-1 1-3 1-4-1-1-1 0-1-1 2 2 1 6 1 9h0c0 1 0 2 1 3 1 0 1-1 2 0 0 3-1 4-2 6 0 1-1 2-1 2-1 0-1 0-2-1h-1v-4c-1 1-1 4-1 5v1h-1c-1 0-1 0-1-1l2-18c1 2 1 4 3 6v-1-2z" class="D"></path><path d="M339 212c1 1 3 2 4 4 1 0 1 1 2 1v1 1l-1 1c0 1 1 1 1 2v1c1 0 1 0 1-1 1 1 1 1 1 2-2 0-2 1-4 3h0-1v-2h-1c-1 1-1 1-1 2h-1l-1-4c-1-1-2-3-3-5h1 1 1l-2-2s0-1 1-2c0 1 0 1 1 2v-1c1-1 1-1 0-2l1-1z" class="AL"></path><path d="M338 223l1-1c1 0 2 1 2 1v1h0v1h1v-2c-1 0-1-1-1-1l1-1v-2-1h0 1c0 1 0 1 1 2h0c0 1 1 1 1 2v1c1 0 1 0 1-1 1 1 1 1 1 2-2 0-2 1-4 3h0-1v-2h-1c-1 1-1 1-1 2h-1l-1-4z" class="u"></path><path d="M510 563v1 1c1-1 1-1 1-2s1-1 1-2v-1l1 1h1 1v1l1 1h-2-1l-1 1h1c-1 1-1 2-1 2h0c-1 0 0 1 0 2s0 2 1 3 3 2 3 3v1c-1 0-1 0-2-1s-3-1-4-1-1 0-2-1c2 0 3 1 5 1 0-1-2-2-3-3h0c-1 1-3 1-4 1l-1 1v2h-2c0-3-1-5-1-7l-1-1h0c1 0 1 1 2 1-1-1-1-1-1-2 1 0 2 1 3 1l1-1c0-1 1-1 1-2h1 2z" class="E"></path><path d="M502 567c1 1 1 1 2 1 2 1 4 0 6 2h0c-1 1-3 1-4 1l-1 1v2h-2c0-3-1-5-1-7z" class="Q"></path><path d="M345 192v-3h-1c0 1-1 2-1 2h-1v-1l-2-2-1 1-1-1h0c5-2 4-4 7-7h0s0 1 1 1c0 1 1 1 2 1h0c1 0 2 0 3-1h3l-1 3v-1h-1c-1 2-2 5-2 7v2 1 2c1 1 1 2 1 3v1l-1-1c0-1-1-2-1-4v-2-2h0l-1-1c0 1-1 2-2 3v1-1c-1 0-1-1-1-1z" class="L"></path><path d="M345 192c0-2 1-3 1-4 1 0 1-1 0-2 1-1 2-2 3-2h1l-1 1-1 5c0 1-1 2-2 3v1-1c-1 0-1-1-1-1z" class="M"></path><path d="M492 491h2 1v1c1 1 1 1 1 2v1c1 0 1-1 1 1v1 1l-1 1c-1 2-3 3-4 5 0 2 0 3-1 4l-1-1-1 1c-1 1-2 1-3 2h-2 0-3l1-2c1 0 2-3 2-4l-1-1 1 1 2-2c0 1 0 1 1 1h1v-1c0-1 0-4 1-5v4h1c1 1 1 1 2 1 0 0 1-1 1-2 1-2 2-3 2-6-1-1-1 0-2 0-1-1-1-2-1-3h0z" class="X"></path><path d="M484 504h1 1c1 1 1 0 2 1 1 0 2 0 3 1v1h-1 0l-1 1c-1 1-2 1-3 2h-2 0-3l1-2c1 0 2-3 2-4z" class="AF"></path><path d="M486 508l-1-1v-2h1c1 1 1 1 1 2l-1 1z" class="AK"></path><path d="M487 507h2 1 0 0l-1 1c-1 1-2 1-3 2h-2 0c1-1 1-2 2-2l1-1z" class="N"></path><defs><linearGradient id="E" x1="545.535" y1="561.524" x2="539.087" y2="563.198" xlink:href="#B"><stop offset="0" stop-color="#1d1d1f"></stop><stop offset="1" stop-color="#3c3c3b"></stop></linearGradient></defs><path fill="url(#E)" d="M540 556h-1c1-2-1-6 0-8 2 2 4 5 5 7 0 1 1 2 2 3 0 1-1 4-1 5s-1 1-1 2-1 1-1 2l-7 6v-1h-2c1 0 2-1 3-1l-1-1h0-1v1h-1l2-1-1-1c4-3 5-6 6-10 1-2 0-3 0-5-1 0-1 1-1 1v1z"></path><path d="M541 554c-1-1-1-2-1-3 2 1 3 3 3 4 1 2 1 4 0 6v-3-2l-2 3c1-2 0-3 0-5z" class="Q"></path><path d="M541 559l2-3v2 3c0 3-2 7-5 9-1 0-1 1-1 1l-1-1h0-1v1h-1l2-1-1-1c4-3 5-6 6-10zm-368-15h0c2 1 3 3 5 4s5 2 8 3l3 1 3 2c4 2 8 3 13 5l3 2-1 1c-1-1-2 0-3-1-2 0-3 0-5-1-8-3-15-6-22-11-1-2-4-3-6-4l2-1z" class="w"></path><path d="M138 540c3 0 5 0 8 1 1 0 4 1 5 1h1c3 0 6 2 10 3l-1 1h-2 0v1c-6-2-13-3-20-3-4 0-8 1-12 2 3-2 6-4 10-4h0c-1-1-2-1-4-1l3-1h2 0z" class="m"></path><path d="M386 502c0 1 0 1 1 1h1c2-2 4-2 6-2 0 1-1 2-2 2-1 1-3 1-4 2v1c-2 1-4 2-6 4l-3 3c-3 4-7 7-9 11l-1-1c5-8 9-15 16-22h1v1z" class="AF"></path><path d="M388 505v1c-2 1-4 2-6 4l-3 3-1-1c1-3 7-5 10-7z" class="AT"></path><path d="M425 479h0l-2 2h-1-1c-1 0-2 0-3 1s-2 1-3 2c-1 0-1 1-1 2-7 1-15 5-21 10v1l-2 1h0c0-1 0-1 1-2h0 0 0-1c1-2 2-3 3-4 5-3 10-6 16-9 1-1 2-1 4-1 4-1 7-3 11-3z" class="X"></path><path d="M449 250c1 2 0 5 0 7 1 2 1 4 1 5 0 2-1 2 0 3 0 1 0 2-1 2v1c1 1 1 3 1 5v1 6 2c-1 2-1 3-1 5v10h0l-1-39h-1c0 1 1 3 0 4v18 2c1 1 0 1 0 2v6 1-1h-1l1-37c0-1 0-1 1-1v1l1 1v-4h0z" class="C"></path><path d="M372 534c3-3 7-5 11-6 6-1 10-1 15 2v2c1 1 3 3 2 4h-1c-1-1-2 0-3 0l-2-1c-2-2-4-3-6-3-1-1-2-1-4-1-4 0-7 1-11 3h-1z" class="AD"></path><path d="M389 528c2 1 6 2 7 3h0v1h-2c-1-1-2 0-3 0 0-1 0-1-1-1s-1-1-2-1h0c0-1 1-1 1-2z" class="g"></path><path d="M384 531v-1c0-1 0-1 1-1 1-1 3-1 4-1 0 1-1 1-1 2h0c1 0 1 1 2 1s1 0 1 1v-1c-1 0-2 0-3 1-1-1-2-1-4-1z" class="G"></path><path d="M378 184c3 0 7 0 10 1 12 2 22 8 31 17 4 3 12 14 12 20v2c-6-15-15-27-30-33-5-3-10-4-16-5-2-1-5 0-6 0l2-1h0l-3-1z" class="k"></path><path d="M437 517l-4-14c2 1 3 3 4 3h1 1c0-1-1-1-1-2h0c1 0 2 1 2 2 0 0 1 1 1 2 2 2 1 6 1 8-1 1 0 2 0 3l-1 1 1 1c-1 1-2 3-2 5l-1 1h1v1h-1l-2 2-1-1c1 0 1 0 1-1 2-3 1-8 0-11zm-1-63l1 1c0-1 0-1 1-1v1l3 3 1 1v3l1 8v1h1c1-3 0-6 1-9v9c-1 0-1 1-2 0h-1v2h1c0 1-1 1 0 1v1c-3 1-5 0-7 1h-2c-1-1-2 0-3 0v-1h7v-9l-1-5c0-2-1-5-1-7z" class="B"></path><path d="M172 317v-1c2 0 2 0 3 1 1 0 1 0 1 1h-1c1 1 1 1 2 1l3 3h1v1l2 1c2 1 4 3 5 5h1l1-1v5h0l1 6c-2-2-3-3-4-5l-7-7-5-4c0-1-2-1-2-2-1-1-1-3-1-4z" class="F"></path><path d="M287 617c1 0 1 0 1 1 1 1 2 1 3 1v2h1v1h0c0 1-1 1-1 1 1 1 2 1 2 3v1c0-1 0-1 1-2 1 1 1 1 1 2s-1 2-2 3c-2 1-4 0-6 0-3-2-4-3-5-6 1-1 1-2 2-3v-1-1h-2l1-1c1-1 3-1 4-1z" class="AR"></path><path d="M291 623c1 1 2 1 2 3v1h-1-1 0c-1-1 0-3 0-4z" class="x"></path><path d="M287 617c1 0 1 0 1 1 1 1 2 1 3 1v2h1v1c-2 0-3 0-4 1-1 0-2-1-2-2h0l1-1c-1 0-2 0-3 1v-1-1h-2l1-1c1-1 3-1 4-1z" class="AG"></path><path d="M489 395h0c2-1 3-2 4-2 1 1 6 0 8 0 3 0 6 1 10 3l4 4c-1 0-2 1-4 0h0l-2 2-2-2h-3l-3-1h1 2l-1-1h0l-1-1h-5-2c-1-2-5-1-8-1l2-1z" class="D"></path><path d="M489 395h0c1 0 2-1 2-1h1 8 0c-1 1-4 1-5 2 2 0 5-1 7 0l-1 1h1 0-5-2c-1-2-5-1-8-1l2-1z" class="m"></path><path d="M489 395h0c2-1 3-2 4-2 1 1 6 0 8 0 3 0 6 1 10 3l4 4c-1 0-2 1-4 0h0l-2 2-2-2h-3l-3-1h1 2l-1-1h0c3 0 5 1 7 2 1 0 2-1 2-1l-1-2c-3-2-7-3-11-3h-8-1s-1 1-2 1h0z" class="F"></path><path d="M161 342l-1 1v1h-1c1-2 0-4 1-6s4-5 6-5c3-1 10-1 13 1l1 1c1 0 1 0 2 1 0 1 2 1 3 3h0c-3-1-5-2-8-2-1 0-1 1-2 2-2-1-2-1-4-1v-1h-1c-3 1-6 2-9 4v1z" class="O"></path><path d="M171 337h6c-1 0-1 1-2 2-2-1-2-1-4-1v-1h-1 1z" class="AG"></path><path d="M161 341s0-1 1-1v-2h0c2-1 3-3 5-4h1 7l1 1c-1 1-3 1-5 2h-1c-3 1-6 2-9 4z" class="v"></path><path d="M453 545h2c0 1 0 1 1 2l-9 5h1c0 1-1 1-2 2h-1c-1 1-2 1-4 2v1c-3 1-4 2-7 1l-16 5h-1c0-1 1-1 1-1l6-3 7-2c1-1 2-1 3-2 0 0 1-1 2-1 3 0 8-4 11-6h2c1-1 3-2 4-3z" class="AK"></path><path d="M447 552h1c0 1-1 1-2 2h-1c-1 1-2 1-4 2v1c-3 1-4 2-7 1 2-1 4-2 7-3l6-3z" class="B"></path><path d="M490 427v-1c1-1 3 0 5 0 5 0 9 2 14 4h0s-1 0-1 1l2 1c-1 1-1 2-1 3l2 1-3 2-1-1c-1 0-1-1-2-2-2-1-5-3-7-3l-3-2h-2l-3-3h0z" class="M"></path><path d="M500 429l4 1c0 1 0 1 1 2-2 0-3-1-4-1-1-1-1-2-1-2z" class="B"></path><path d="M504 430c1 0 4 1 4 1l2 1c-1 1-1 2-1 3l2 1-3 2-1-1c0-1 1-1 1-2l-3-3c-1-1-1-1-1-2z" class="u"></path><path d="M490 427v-1c1-1 3 0 5 0 5 0 9 2 14 4h0s-1 0-1 1c0 0-3-1-4-1l-4-1s-2-1-3-1c-2-1-5-1-7-1z" class="R"></path><path d="M543 567h1c1 0 1 0 2-1h0v1l-1 2c-2 3-5 5-8 6-2 1-3 1-5 1l-1 1c-3 0-6-1-9-1v-1c0-1 1-1 2-2h1v-1l-3-1-1-3h1c1 1 3 2 5 2 2 1 4 1 5 1l3-2 1 1-2 1h1v-1h1 0l1 1c-1 0-2 1-3 1h2v1l7-6z" class="w"></path><path d="M532 571l3-2 1 1-2 1h1v-1h1 0l1 1c-1 0-2 1-3 1h-2-4v-1h4 0z" class="AS"></path><path d="M352 621h0c1-1 2-2 3-2 2 0 3 0 4 1h0v1c1 2 2 2 1 4v1c-1 2-3 3-5 4 1 1 2 1 3 1-1 1-3 1-5 1h-3c-2-1-2-1-3-2v-1l1-1-1-1 3-3 1-1s1-1 1-2z" class="AR"></path><path d="M351 624c1 0 1 0 1 1s0 2-1 3v-1c-1 0-1-1-1-2l1-1z" class="AN"></path><path d="M347 630v-1l1-1c1 1 2 2 4 2v1c-1 0-1 0-2 1-2-1-2-1-3-2z" class="AH"></path><path d="M352 631c1-1 2-1 3-1 1 1 2 1 3 1-1 1-3 1-5 1h-3c1-1 1-1 2-1z" class="V"></path><path d="M352 621h0c1-1 2-2 3-2 2 0 3 0 4 1h0v1h-1c-2-1-4 0-6 0z" class="I"></path><path d="M489 539h2 0c-1 1-1 1-2 1l-1 1v1h1 5c2 0 4 0 5 1s1 2 0 3-2 0-3 0l-9-2h-6c-3 0-4 1-6 2s-4 1-6 1h-2-1-1-1c1-1 2-1 3-2l8-3c1 0 1 0 2-1l1 1c1 0 5-2 6-2 2-1 4-1 5-1z" class="X"></path><path d="M477 541l1 1c1 0 5-2 6-2 2-1 4-1 5-1-1 1-1 2-3 2-1 1-3 1-4 2h-4c-3-1-9 4-11 2l8-3c1 0 1 0 2-1z" class="AK"></path><path d="M363 203v-1c1-1 2-1 3-2 0 0 1 0 1-1h1 1c0-1 1-1 1-1h1 0 0 2 3v1h0l-2 2v1 2c0 1-1 1-2 2-2 1-5 2-7 4v1h-1c-2 0-3 1-4 1l-1-1v-3c1-2 2-3 4-5z" class="D"></path><path d="M363 203c2-1 4-3 7-4h2c0 1-1 2-2 3-1 0-2 1-3 2-2 1-3 3-4 4h-2l-1 1-1-1c1-2 2-3 4-5z" class="Q"></path><path d="M162 256c1 1 2 1 3 0l1 1c-4 3-8 5-12 8h0c-1 1-1 2-2 2l-1 1-1 1 1 1c1 1 1 1 1 2l-2 2h-1 0c-1-1-1-1-2-3h0l-1 1c0-1-1-1-2-2l1-1v-2-1h0v-1c1-1 2-2 4-3 4-3 8-4 13-6z" class="N"></path><path d="M145 266h3l-3 3v-2-1h0z" class="S"></path><path d="M112 335c0-1 1-2 2-4 0 2-1 4-1 6s0 3-1 5h1c0 1 0 0 1 1h0c-1 1-1 1-1 2h0c0 3 0 7 1 10 2 9 9 17 17 22h-1 0c-2-1-5-2-7-4l-4-4h0c-1-1-2-4-3-4l-2-4c-1-1-2-3-3-4 0-2-2-4-2-6h0l-1-4 2-6c1-1 2-4 2-6z" class="F"></path><path d="M113 345v3h-1v-6h1c0 1 0 0 1 1h0c-1 1-1 1-1 2h0z" class="S"></path><path d="M108 347l2-6 1 1v1c1 1 0 5 1 7 0 4 1 7 2 11-1-1-2-3-3-4 0-2-2-4-2-6h0l-1-4z" class="W"></path><path d="M443 378s0 1-1 2v33 17c0 3 1 13 0 15h-2v-3c-1-1 0-10 1-12l-1-1c1-3 1-8 1-12l-1-26c1 0 0-3 0-4 0-2 0-6 1-8v-1h2z" class="j"></path><path d="M441 430c0-2 0-3 1-5v14 6h-2v-3c-1-1 0-10 1-12zm2-52s0 1-1 2c-1 2 0 8 0 11v15h-1v-7-20-1h2z" class="b"></path><path d="M91 503l3 4c1 2 3 3 4 4s2 3 3 4c0 1 2 3 3 4l1-1c6 4 12 9 18 12h0c0 1 0 1-1 1h1v1c-2 0-4-2-5-2s-1 0-2-1h-1c-1-1-2-1-3 0l-2-2c-4-3-8-7-11-10s-6-6-7-9v-1-2c-1-1-1-1-1-2z" class="AM"></path><path d="M398 530c1 1 3 3 3 4 3 4 4 18 10 18 1 0 2-1 2-1l1 1-2 2h0 1v1s1 0 1 1h0c-2-1-2 0-4 0-1 1-1 1-2 1-1-1-3-2-4-4 0-1-1-2-2-3 0 0-1-3-1-4-1-2-1-4-2-6s-2-3-3-4c1 0 2-1 3 0h1c1-1-1-3-2-4v-2z" class="AJ"></path><path d="M406 553c-2-3-4-9-4-12 2 3 3 7 5 10h0c-1 1-1 1-1 2z" class="F"></path><path d="M406 553c0-1 0-1 1-2 1 2 2 3 4 3h1 0 1v1s1 0 1 1h0c-2-1-2 0-4 0s-3-1-4-3z" class="M"></path><defs><linearGradient id="F" x1="164.711" y1="565.992" x2="144.564" y2="549.131" xlink:href="#B"><stop offset="0" stop-color="#1c1c1b"></stop><stop offset="1" stop-color="#434342"></stop></linearGradient></defs><path fill="url(#F)" d="M137 557c2-1 3-3 5-3 2-1 5-2 7-2 8-1 17-2 25 1h1l1 1s1 0 1 1h1v1h-1-1-1c-2-1-3-1-5-1-8-1-17-1-25 1-2 1-5 2-6 5l-1 1c1 0 1 0 1 1h1-2l-2-1h-1c1-2 2-3 2-4v-1z"></path><path d="M293 562l-2 2h0c0 1-1 1-1 1-1 1-1 2-1 3-5 7-11 13-17 19-1 1-2 1-2 1l-3 3 1-1h-1c-1 1-3 2-4 4 0 1-1 1-1 1h-1 0l-15 12h-1c3-3 7-5 10-8 1-1 2-2 2-3 8-6 14-13 21-19 1-1 3-4 3-4l8-8h0c1-1 2-2 4-3z" class="AI"></path><path d="M427 515c1-1 1-1 2-1 0 0 1 0 1-1 1 0 1 0 2 1 0 1 1 1 1 3l1 1v2 6 3c-1 1-2 1-2 2h-1 0l2 1c-1 1-1 2-2 2 0 1-1 1-1 1v1l-1-3c-1 0-2-1-2-2-1 0-1-4-2-5l1-1v-1h0c0-2 1-3 2-4v-2s0-2-1-2v-1z" class="C"></path><path d="M427 515c1-1 1-1 2-1 0 0 1 0 1-1 1 0 1 0 2 1h-1c0 1 0 1 1 2 0 2 0 3-2 4 0 1-1 2-1 2h-1v-2-2s0-2-1-2v-1z" class="F"></path><path d="M426 524h0c0-2 1-3 2-4v2 3l1 1v-1h2c1 0 1 0 2 1h1v3c-1 1-2 1-2 2h-1 0l2 1c-1 1-1 2-2 2 0 1-1 1-1 1v1l-1-3c-1 0-2-1-2-2-1 0-1-4-2-5l1-1v-1z" class="E"></path><path d="M429 533c-1-1-2-5-1-6h1v1c0 2 0 3 1 4v3 1l-1-3z" class="Q"></path><path d="M429 525h2c1 0 1 0 2 1h1v3h-3c0-1 1-1 2-1l-1-1h0l-1 1h-1c0-1 0-2-1-3z" class="AO"></path><path d="M429 528l2 1h3c-1 1-2 1-2 2h-1 0l2 1c-1 1-1 2-2 2 0 1-1 1-1 1v-3c-1-1-1-2-1-4z" class="M"></path><path d="M510 570c1 1 3 2 3 3-2 0-3-1-5-1l-3 1c1 3 3 5 5 7l3 3c3 2 6 2 9 3 3-2 3-5 8-4 1 1 2 1 3 2h0l2 1h3 0c-2 3-6 2-10 3 0-1 0-1-1-1s-2 1-3 0c-1 0-3 1-4 1v1c-1 0-4-1-5-2-4-1-8-5-10-8 0-1-1-1-1-2s0-2-1-3h2v-2l1-1c1 0 3 0 4-1h0z" class="W"></path><path d="M515 587l1-1 1 1h4 1 0 5c-1 0-2 1-3 0-1 0-3 1-4 1v1c-1 0-4-1-5-2zM412 476c-1 2-4 2-5 5-7 3-15 9-21 15l-6 6c0-3 2-5 3-7v-1c0-1 1-2 1-2 1-2 2-3 3-5l4-5c-1 1-1 2-1 3h1c1-1 2-2 4-2 1 0 3-2 3-2h1l1 1 12-6z" class="N"></path><path d="M391 482c-1 1-1 2-1 3h1c1-1 2-2 4-2 1 0 3-2 3-2h1l1 1c-2 1-3 2-4 3h-1c-3 1-5 4-7 6-1 0-2 1-2 1h-1-1c1-2 2-3 3-5l4-5z" class="X"></path><path d="M387 261c1-1 2-2 4-2l1 1c1 0 1 0 1-1l3-4h0c1 4 2 7 4 10 1 1 2 2 2 3h-1c0 1-1 3-1 4-1 0-1 1-3 1-1-1-1-2-3-2h-1v-1h0c-2-1-3-2-3-3-1-1-3-2-3-3v-3z" class="M"></path><path d="M434 570c2-1 4-2 6-1-1 1-1 1-1 2h1c2 1 4 2 5 3 0 1 1 1 2 2v3 1c-1 0-1 0-2-1 0 1 0 1-1 2-3-1-7-3-11-5h-3v1c-1 1-2 0-2 0h-1c-1 0-1-1-1-2-1 0-2-1-3-1 1-1 2-1 3-1 2-1 5-2 7-2l1-1z" class="E"></path><path d="M434 572h4v1h-1c-1 0-2 0-3-1z" class="D"></path><path d="M426 575l4 1v1c-1 1-2 0-2 0h-1c-1 0-1-1-1-2z" class="J"></path><path d="M434 570c2-1 4-2 6-1-1 1-1 1-1 2h-1c-1 0-1 0-1-1-1 0-2 0-3 1h-1l1-1z" class="W"></path><path d="M523 366c3-2 5-4 7-7l-1 2h1 2c1-1 1-1 2-1v1l-2 7c-1 2-2 3-4 4-1 1-2 1-2 1l-2 1c-1-1-2-1-4 0l-2 2-5 3c1-1 3-3 3-5l4-6c1-1 2-2 3-2z" class="B"></path><path d="M520 374l1-1c2-2 5-4 7-4h0l-3 3 1 1h0l-2 1c-1-1-2-1-4 0z" class="W"></path><path d="M532 361c1-1 1-1 2-1v1c-1 0-1 0-1 1h0c-4 4-9 7-14 9l1-1c3-3 7-7 12-9z" class="D"></path><path d="M454 521l5-5v1 1h2l-2 2-3 4c-1 2-4 4-6 4-1 1-3 2-5 2l2 1h0 0-3c-1 1-3 2-4 2-3 0-6-1-8-2 0-1 1-1 2-2v-3-6l1 1v2 1 3 1h0v1h1s0-1 1-1c0-1-1 0 0-1v-4-6c1 3 2 8 0 11 0 1 0 1-1 1l1 1 2-2h1 1c2-1 5-3 7-5h1l2-2h3z" class="J"></path><path d="M440 530c3-1 6-3 8-4v1c-1 0-1 1-1 2-2 1-3 1-4 2h-3v-1h0z" class="u"></path><path d="M449 523h1v1l-2 2c-2 1-5 3-8 4 0-1 0-1-1-2h1 1c2-1 5-3 7-5h1z" class="M"></path><path d="M448 527l2-1 3-3c1 0 3-2 4-3h2l-3 4c-3 2-6 4-9 5h0c0-1 0-2 1-2z" class="B"></path><path d="M454 521l5-5v1 1h2l-2 2h-2c-1 1-3 3-4 3l-3 3-2 1v-1l2-2v-1h-1l2-2h3z" class="E"></path><path d="M449 523l2-2h3c-1 1-2 2-4 3v-1h-1z" class="F"></path><path d="M442 488h0c1-1 1-2 2-2h3l-1 1c0 1 1 1 1 2v1l-1-1v2l1-1h0v-1-6h0c-2 1-3 2-4 3h-2 0v-1c0-1 2-2 3-3l1-1h0c1 0 1 0 1-1h-1v1h0-1v-1l1-1c1 0 2 1 3-1 1-1 1-5 1-7-1-1 0-2 0-3v-49-1-1h0v-7c-1-1 0-1 0-2v-7-4c-1-2-1-6-1-9h-1 0v1 3 5c-1 2 0 5 0 6-1 1-1 2-1 3l-1-1 1-1c0-2-1-4 0-5v-8-3l1-1h0l1-1h1v93c-1 0-2 1-3 2l1 1s1 0 1 1c1 2 0 5 0 7l-5 6c-1-2-1-4-2-5v-2h0l1-1z" class="L"></path><path d="M444 486c1 0 1 0 2 1 0 1 0 1-1 2v2h0c-2-1-2-1-2-2 0-2 1-2 1-3z" class="Y"></path><path d="M356 223c2-3 0-5 0-8h0l3 5c1 5 1 10 1 15-2 7-5 14-12 18-2 1-5 1-8 0-3 0-4-1-6-3 0-2-1-4 0-5 0-2 2-3 3-4 1 0 3 1 3 2h0-4c-1 1-1 2-1 3-1 1-1 2 0 3s3 3 5 3c4 0 6-1 9-3 1-1 1-1 1-2h1c3-3 5-9 6-14v-8l-1-2z" class="j"></path><path d="M303 626c1 0 2 1 3 1h0l-1 1 3 3h0c1 1 1 0 1 1v1h1l3 3 3 3v3l3 3h0 2c0 1 0 1 1 3-1 0-1 1-2 1l1 1h1c0 2 0 3-1 4 0 1-1 2-1 3 0-1-1-2-1-3l-1-2v1c0 1 0 1-1 1v1l-1-2h1v-2h1v-1c-2-3-4-6-7-9l-2-2c-1-1-3-2-4-3l-1 1h-1 0l-2-3h2c0-1 0-1 1-2 0 1 0 1 1 2h0c1 0 1 0 2 1l-5-5 3 2v-1c-1-1-2-3-3-4l1-1z" class="t"></path><path d="M321 650h1c0 2 0 3-1 4 0 1-1 2-1 3 0-1-1-2-1-3h1l1-1-1-2h1v-1z" class="AG"></path><path d="M319 645h2c0 1 0 1 1 3-1 0-1 1-2 1l-1-1c0-1-1-2-2-2 1 0 2-1 2-1h0z" class="B"></path><path d="M319 645h0c1 1 1 1 1 2l-1 1c0-1-1-2-2-2 1 0 2-1 2-1z" class="C"></path><path d="M314 640l2 2 3 3s-1 1-2 1c-2-2-2-3-4-5l1-1z" class="F"></path><path d="M303 626c1 0 2 1 3 1h0l-1 1 3 3h0c1 1 1 0 1 1v1h1l3 3 3 3v3l-2-2-1 1-6-6-5-5 3 2v-1c-1-1-2-3-3-4l1-1z" class="L"></path><path d="M309 633h1l3 3 3 3v3l-2-2-5-5v-2z" class="E"></path><path d="M303 626c1 0 2 1 3 1h0l-1 1 3 3h0c1 1 1 0 1 1v1 2l-4-4c-1-1-2-3-3-4l1-1z" class="C"></path><path d="M313 636c1-1 1-2 3-2h7c2 0 3 0 5 1 1 0 1 1 1 2h0v1 1 1c-1 0-1 1-1 1h1l-1 2-2 2c0 1-1 2-2 2 0 1-1 2-2 3h-1l-1-1c1 0 1-1 2-1-1-2-1-2-1-3h-2 0l-3-3v-3l-3-3z" class="T"></path><path d="M323 639l2-1h1v2l-1 2-2-1-2-1h1v-1h1z" class="U"></path><path d="M322 639h1 1c0 1 0 1-1 2l-2-1h1v-1z" class="i"></path><path d="M316 639c0-1 0-2 1-2h1l1 1h2l1 1v1h-1v1 2h-1c1 1 1 1 2 1l-1 1h-2 0l-3-3v-3z" class="a"></path><path d="M321 638l1 1v1h-1v1c-1 0-1-1-2-1v-1l2-1z" class="H"></path><path d="M316 639l4 4c1 1 1 1 2 1l-1 1h-2 0l-3-3v-3z" class="Q"></path><path d="M329 637h0v1 1 1c-1 0-1 1-1 1h1l-1 2-2 2c0 1-1 2-2 2 0 1-1 2-2 3h-1l-1-1c1 0 1-1 2-1-1-2-1-2-1-3l1-1c-1 0-1 0-2-1h1v-2-1l2 1 2 1 1-2 3-3z" class="C"></path><path d="M321 640l2 1 2 1c-1 1-2 1-3 2-1 0-1 0-2-1h1v-2-1z" class="T"></path><path d="M322 648l1-2c1-1 2-1 3-1 0 1-1 2-2 2 0 1-1 2-2 3h-1l-1-1c1 0 1-1 2-1z" class="AM"></path><path d="M423 528h1c1 0 1-1 1-2 1 1 1 5 2 5 0 1 1 2 2 2l1 3h-1-3c-1 0-2 1-3 2h-1v3c-1 1 0 2 0 3h0l-1 1h1v2c-3-2-6-3-8-5h-1v-1c1 0 1 0 2-1 0-3 1-5 1-8h1v-2c1-1 2-1 4-1 1-1 1-1 2-1z" class="F"></path><path d="M421 529c1-1 1-1 2-1 1 1 1 2 2 3h-3l-1-1v-1z" class="B"></path><defs><linearGradient id="G" x1="481.79" y1="420.45" x2="482.967" y2="411.054" xlink:href="#B"><stop offset="0" stop-color="#2f2e2e"></stop><stop offset="1" stop-color="#4f504e"></stop></linearGradient></defs><path fill="url(#G)" d="M472 410c2-1 5 0 7 0 0 1 1 1 1 1 1 2 2 2 4 3h1l5 5c1 1 2 2 2 3h0s-1 0-1 1h-2 0c1 0 2 1 3 1 0 1 2 2 3 2-2 0-4-1-5 0v1h0l3 3c-1 0-3-1-4-1h0c-1 0-1 1-2 1-2 0-4 0-7-1l1-1h2 3 0c1-1 1-1 2-1v-1l-1 1c0-2-2-3-3-4l-2-2 1-1-2-1c-2-3-7-5-10-7-1 0-1-1-2-1h0c1-1 2-1 3 0l1-1h-1z"></path><path d="M476 413c1 0 2 0 3 1 2 1 7 3 8 6v2l-6-5c-2-1-3-3-5-4z" class="F"></path><defs><linearGradient id="H" x1="489.998" y1="422.209" x2="481.228" y2="420.996" xlink:href="#B"><stop offset="0" stop-color="#2c302d"></stop><stop offset="1" stop-color="#433e3e"></stop></linearGradient></defs><path fill="url(#H)" d="M471 412l1-1c1 0 1 0 2 1h0l2 1c2 1 3 3 5 4l6 5c1 0 1 1 2 1s2 1 3 1c0 1 2 2 3 2-2 0-4-1-5 0v1h0l3 3c-1 0-3-1-4-1h0c-1 0-1 1-2 1-2 0-4 0-7-1l1-1h2 3 0c1-1 1-1 2-1v-1l-1 1c0-2-2-3-3-4l-2-2 1-1-2-1c-2-3-7-5-10-7z"></path><path d="M483 420c2 2 4 4 5 6l-1 1c0-2-2-3-3-4l-2-2 1-1z" class="L"></path><defs><linearGradient id="I" x1="99.887" y1="547.925" x2="126.014" y2="534.842" xlink:href="#B"><stop offset="0" stop-color="#3d3e3d"></stop><stop offset="1" stop-color="#565554"></stop></linearGradient></defs><path fill="url(#I)" d="M128 536l3 1c1 0 1 0 2 1h-1l6 2h0-2l-3 1h0c-5 0-10-1-15 0l-1 1c-5 1-10 3-14 5-3 2-5 4-7 5h0l3-3h1l1-1h0c-1 0-1 0-2-1 0-1 1-1 1-2l1-1 1-1s1-1 1-2h-2c-1 0 0 1-1 1s-1 0-1 1h-2 0c5-5 16-5 23-5h5c1-1 2-1 3-1v-1z"></path><path d="M128 536l3 1c1 0 1 0 2 1h-1-7c1-1 2-1 3-1v-1z" class="K"></path><path d="M382 510c3 0 5-1 8-1-2 1-5 2-7 4h0l1 1c-1 1-3 2-4 3l-6 6v3l-3 3c-2 3-4 6-5 9s-3 6-4 9l-2 1h0c0-4 1-8 3-11l3-9h0l3-5 1 1c2-4 6-7 9-11l3-3z" class="D"></path><path d="M369 523l1 1c-1 2-2 3-3 5-1 3-2 6-4 8l3-9h0l3-5z" class="N"></path><path d="M371 527v2c-2 3-4 6-5 9h-1c1-4 2-8 4-10h0l2-1z" class="AF"></path><path d="M383 513l1 1c-1 1-3 2-4 3l-6 6v3l-3 3v-2l-2 1h0c1-3 3-7 6-9l8-6z" class="AK"></path><path d="M374 523v3l-3 3v-2l3-4z" class="m"></path><path d="M408 529l-1-5c0-1-1-2-1-2l1-1c1-1 1-1 1-2v-1c2-2 6 4 8 6h3c0-1 1-1 1-1l1 1c-1 1-2 2-4 2v1h-2v2c-1 1-1 3-1 4h0 0l1 1-1 3c-1 1-2 3-3 3l-3 3c-1-1-2-4-2-5s1-3 1-4v-1l1 1v-1-4h0z" class="F"></path><path d="M419 524c0-1 1-1 1-1l1 1c-1 1-2 2-4 2v1h-2 0-1l-1 1c0-1 0-1 1-2h0c1 0 1-1 1-2h1 1 2zm-9 0h1c2 4 1 8 1 12h0v-2l-1 1h0c0-1 0-2-1-2v-1c0-3-1-5 0-8z" class="D"></path><path d="M464 409l2-1-2 2v1c0 2-3 3-5 4h1c1 0 1 1 2 1l6 3c1 1 2 2 3 2l1-1h1l4 2 3 2h1 3v-1c1 1 3 2 3 4l1-1v1c-1 0-1 0-2 1h0-3-2l-1 1v-1c-4-1-7-1-11-1h-2c-1-1-2-3-3-4s-2-2-3-2c-1-1-1-2-3-3l-2-1v-1c2 0 4-2 5-3h0c0-1-1-1-1-2l4-2z" class="C"></path><path d="M461 413h0c0-1-1-1-1-2l4-2-1 2c0 1-1 2-2 2z" class="L"></path><path d="M337 628h1l-1 3h-1l-1 1-2 1h0l-4 4h0c0-1 0-2-1-2-2-1-3-1-5-1h-7c-2 0-2 1-3 2l-3-3h-1v-1c0-1 0 0-1-1h0 3v-1c2-2 5-1 6-2h17 3z" class="Z"></path><path d="M337 628h1l-1 3h-1l-1 1h-3v-1c2-1 3-1 5-2v-1z" class="y"></path><path d="M325 631h1c1 1 5 0 6 0v1h3l-2 1v-1h-8v-1z" class="t"></path><path d="M320 631h1v-2c1 1 0 2 2 2h2v1h8v1h0c-1-1-3 0-4 0h-11l2-1v-1z" class="AJ"></path><path d="M311 631h4l1-1s0 1 1 1h3v1l-2 1c-2 0-7-1-8 0h-1v-1c0-1 0 0-1-1h0 3z" class="AP"></path><path d="M310 633c1-1 6 0 8 0h11c1 0 3-1 4 0l-4 4h0c0-1 0-2-1-2-2-1-3-1-5-1h-7c-2 0-2 1-3 2l-3-3z" class="P"></path><path d="M347 569h0l3 3c0-1 1-1 1-1h0c0-1 1-1 1-2l2 2c1 0 2 2 3 3l1 1h1c1 2 2 3 3 5 1 1 2 3 3 5-1 0-1 1-2 2h0v1c-1 0-1 0-1-1 0 2 1 2 2 3l1 1c-1 1-2 2-4 2h-1 0c-1-2-2-3-3-5s-1-4-3-6c1-1 0-2 0-2-1-2-2-3-4-4v1c-2-3-2-5-3-8z" class="X"></path><path d="M361 589c-1-2-2-3-2-5h1l1 3h2 0 0v1c-1 0-1 0-1-1h-1v2z" class="F"></path><path d="M361 589v-2h1c0 2 1 2 2 3l1 1c-1 1-2 2-4 2 0-2 1-2 0-4z" class="S"></path><path d="M354 571c1 0 2 2 3 3l1 1h1c1 2 2 3 3 5 0 0-1 1-2 0-1 0-2-2-2-3l-3-3c0-1-1-2-1-3z" class="Q"></path><path d="M347 569h0l3 3c0-1 1-1 1-1h0c0-1 1-1 1-2l2 2c0 1 1 2 1 3 0 2 1 3 1 5 1 3 1 5 2 8 1 2 2 4 2 6-1-2-2-3-3-5s-1-4-3-6c1-1 0-2 0-2-1-2-2-3-4-4v1c-2-3-2-5-3-8z" class="B"></path><path d="M350 572c0-1 1-1 1-1l3 6c-1 0-2-2-3-3l-1-2z" class="m"></path><path d="M347 569h0l3 3 1 2c1 1 2 3 3 3 0 1 1 2 0 3-1-2-2-3-4-4v1c-2-3-2-5-3-8z" class="E"></path><path d="M467 407c1 0 3-1 4-2 1 1 2 1 4 1v1c-2 0-4 0-5 1 0 1 1 2 2 2h1l-1 1c-1-1-2-1-3 0h0c1 0 1 1 2 1 3 2 8 4 10 7l2 1-1 1 2 2v1h-3-1l-3-2-4-2h-1l-1 1c-1 0-2-1-3-2l-6-3c-1 0-1-1-2-1h-1c2-1 5-2 5-4v-1l2-2 1-1z" class="X"></path><defs><linearGradient id="J" x1="481.995" y1="424.343" x2="468.906" y2="409.799" xlink:href="#B"><stop offset="0" stop-color="#30302f"></stop><stop offset="1" stop-color="#484947"></stop></linearGradient></defs><path fill="url(#J)" d="M475 417l-2-1c-2-2-4-2-6-4l1-1h-1l-1-1h1c1 0 1-1 2 0v-1l1-1c0 1 1 2 2 2h1l-1 1c-1-1-2-1-3 0h0c1 0 1 1 2 1 3 2 8 4 10 7l2 1-1 1 2 2v1h-3-1l-3-2 1-1-3-2v-2z"></path><path d="M475 417h0l1-1h-1c1 0 1 1 2 1l1 1v1l-3-2z" class="D"></path><path d="M482 421c-1 0-1-1-2-2h1l2 1-1 1z" class="J"></path><defs><linearGradient id="K" x1="377.38" y1="516.007" x2="367.738" y2="507.662" xlink:href="#B"><stop offset="0" stop-color="#3d3e3b"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#K)" d="M385 487h2c-1 2-2 3-3 5 0 0-1 1-1 2v1c-1 2-3 4-3 7-5 5-9 12-13 18-1 2-1 5-2 7-2 3-4 6-4 9 0 1 0 1-1 2v2c-1 0-1 1-1 1h0v-1l-1 1h0c-1 0-1 0-1-1 0-2 1-4 1-5v-1l1-4 1-5v1c0 1-1 3 0 4v-1c0-1 0-1 1-1v-2 1c1 0 2-3 2-4 2-4 3-8 5-12 4-3 5-6 7-9l6-8c1-2 3-5 4-7z"></path><path d="M360 525v1c0 1-1 3 0 4v-1c0-1 0-1 1-1v-2 1l-3 14c-1 0-1 0-1-1 0-2 1-4 1-5v-1l1-4 1-5z" class="S"></path><path d="M393 497c8-3 16-4 25-2h2c0 2-1 2-2 3l-2 1-3 3h-2-1l-1 1c-1-1-2-1-3-2-1 0-2-1-4-1h-5c0 1-2 1-3 1-2 0-4 0-6 2h-1c-1 0-1 0-1-1v-1c2-1 3-2 5-3l2-1z" class="AK"></path><path d="M386 502c2-1 3-2 5-2 1-1 2 0 3 0 2 0 3-1 5-1 2 1 2 1 3 1h-5c0 1-2 1-3 1-2 0-4 0-6 2h-1c-1 0-1 0-1-1z" class="Q"></path><path d="M418 495h2c0 2-1 2-2 3l-2 1-3 3h-2-1l-1 1c-1-1-2-1-3-2-1 0-2-1-4-1-1 0-1 0-3-1h2c2 0 4 0 6-1 2 0 4-1 6 0h0c2-1 3-2 5-3z" class="N"></path><path d="M399 499h2c4 0 6-1 9 0-1 1-1 2-3 2h0-1c-1 0-2-1-4-1-1 0-1 0-3-1z" class="D"></path><path d="M410 499h3 1 2l-3 3h-2-1l-1 1c-1-1-2-1-3-2h1 0c2 0 2-1 3-2z" class="B"></path><path d="M182 286c1 1 2 1 2 2l3 5h0l2 2c0 1 1 2 1 3s1 1 1 1c1 1 1 1 0 2 0 1-1 2-1 2-1 1-2 1-2 1v-1c-1 0-1 0-2 1l-1 1v1c-2 1-4 2-5 3h-2c-2 1-4 2-6 4v-1-1l-2 1c-1-1 0-1 0-2v-1c0-1 1-1 1-2h2c1-1 1-1 1-2h-3v-1c2-3 7-3 11-5 1 0 2-1 3-2 0-1-1-1-2-2h1v-2h1 1c-1-3-2-4-4-6v-1z" class="C"></path><path d="M172 312c1-1 1-1 2-1h-2v-1-2h0 1v2h1c1-1 2-1 3-2h0l1 1c-2 1-4 2-6 4v-1z" class="L"></path><path d="M185 305v1c-2 1-4 2-5 3h-2l-1-1c1 0 1-1 2-1l1-1c2 0 3 0 5-1z" class="Y"></path><path d="M185 297c2 0 2 2 3 3l-2 2c-2 1-3 2-5 2s-6 0-7 1h-3v-1c2-3 7-3 11-5 1 0 2-1 3-2z" class="X"></path><path d="M435 244c1 0 2 1 3 1h3l1 1v2 6 3-1c-2 0-4-1-6-1h-3c-1 0-1 0-2 1h0-1 0c0 3-2 5-3 7 0 2-1 4-2 6v-5l1-10v-6c1 0 1 0 2-1 2 0 2 0 3-1v-1c1-1 3-1 4-1z" class="s"></path><path d="M435 244c1 0 2 1 3 1h3l1 1v2 6-3l-2-2s0-1-1-1c0-2-2-2-4-3-1 0-2 0-4 1h0v-1c1-1 3-1 4-1z" class="e"></path><path d="M432 251c1-1 1-1 2-1l2 2c0 1 0 1-1 1l-1 1h-1l1 1v-1h1l1 1h-3c-1 0-1 0-2 1h0l-2-3h-1l2-2v1l2-1z" class="c"></path><path d="M432 251c1 0 2 0 3 1l-1 1-1 1c-1-1-2-1-2-2h-1l2-1z" class="G"></path><path d="M431 246h0c2-1 3-1 4-1 1 1 1 1 1 2l1 1h-1c-1 0-2 1-4 1v1c-1 0-2 1-2 1l-2 2h1l2 3h-1 0c0 3-2 5-3 7 0 2-1 4-2 6v-5l1-10v-6c1 0 1 0 2-1 2 0 2 0 3-1z" class="T"></path><path d="M431 246h0c2-1 3-1 4-1 1 1 1 1 1 2h-1c-1 1-3 1-4 0v-1z" class="b"></path><path d="M429 253l2 3h-1 0c0 3-2 5-3 7 0-2 0-6 1-8 0 0 0-1 1-1v-1z" class="O"></path><path d="M442 330c1 0 2 1 3 1s3 1 4 1c0 1 1 2 1 3v3 7 1 6 2l-1-2v1c-1 1-1 1-1 2v1l-1-1v2l-1 1v1c1 1-1 5 1 7-1 1-1 1-2 1 0-1 1-1 0-2v-4-3l1-1v-1-1c-4 0-7-1-10 0v-1h0v-1h-1-2-1v-1c2 0 5 0 8-1 1 0 3-1 4-1l-2-2c2 0 3-1 3-2 0-2 0-4-1-7v-1h-1v-1c1 0 2 0 2-1h0l-3-1h-2-1v-1-1l2 1c1 0 1 0 2-1-1-1-1-2-1-3h0z" class="q"></path><path d="M450 346v6 2l-1-2v1c-1 1-1 1-1 2v1l-1-1c-1 0-3-1-4-2v-1c2 0 4-1 6-1l1-5z" class="Y"></path><path d="M442 330c1 0 2 1 3 1s3 1 4 1c0 1 1 2 1 3l-1 1c-1-2-1-2-3-2v1l2 2-1 1c-1 2 1 4 1 6l-1 3s-1 0-1 1-1 2-2 2h0l-2-2c2 0 3-1 3-2 0-2 0-4-1-7v-1h-1v-1c1 0 2 0 2-1h0l-3-1h-2-1v-1-1l2 1c1 0 1 0 2-1-1-1-1-2-1-3h0z" class="Z"></path><path d="M442 330c1 0 2 1 3 1 0 1-1 2-1 3h-2v1h-2-1v-1-1l2 1c1 0 1 0 2-1-1-1-1-2-1-3h0z" class="U"></path><defs><linearGradient id="L" x1="477.238" y1="530.752" x2="451.047" y2="553.064" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#L)" d="M489 521l1 2-3 3-6 7c-1 1-2 3-3 3h-1l-1 1-1 1c-2 1-3 2-4 2-2 2-4 3-6 4-3 2-4 4-7 5-2 1-4 3-6 3l-6 3c-2 1-3 2-5 2v-1c2-1 3-1 4-2h1c1-1 2-1 2-2h-1l9-5c1-1 2-2 3-2l6-6c5-4 10-7 14-13l1 1c-1 1-4 4-4 5h1l4-4v1c2-1 4-3 5-6l1-1c1-1 1-1 2-1z"></path><path d="M489 521l1 2-3 3-6 7c-3 1-5 3-7 4v1-1s1 0 1-1l6-7c2-1 4-3 5-6l1-1c1-1 1-1 2-1z" class="C"></path><path d="M384 531c2 0 3 0 4 1 2 0 4 1 6 3l2 1c1 1 2 2 3 4s1 4 2 6c-1 1-1 1-1 2h-1 0c-1 0-2-1-2-2h-1l-1 1c0-1-1-2-3-4-1-1-3-4-5-5-1-1-3-1-4-1h-11-1l-3 1h0c1-2 2-3 4-4h1c4-2 7-3 11-3z" class="B"></path><path d="M392 539s-1 0-1-1v-1h-1v-3c1 0 2 1 2 1h1v4 1h-1v-1z" class="W"></path><path d="M392 535h1v4 1h-1v-1-4z" class="m"></path><path d="M394 535l2 1c1 1 2 2 3 4s1 4 2 6c-1 1-1 1-1 2h-1 0c-1 0-2-1-2-2h-1l-1 1c0-1-1-2-3-4 1 0 1-1 1-2 1-1 1-2 1-4h-1v2-4h1z" class="D"></path><path d="M504 537c7 0 13 0 19 2l9 4c1 0 2 2 3 2v1h-1 0v1c1 2 0 4 0 6-2-2-5-3-8-5-4-3-8-5-13-7-7-2-16 0-23 0l-1 1h-1v-1l1-1c1 0 1 0 2-1h0c4-1 9-1 13-2z" class="B"></path><path d="M497 498h1c0-1 1-2 2-3l1 1c1 0 2 0 3-1h1 1l-6 7s-1 1-1 2v1 1l2 1-1 1h-3-1l-1 1-2 3h0c0 2-2 3-3 5s-2 3-3 5l-1 1c-1 3-3 5-5 6v-1l-4 4h-1c0-1 3-4 4-5l-1-1 6-9h0v-1l1-1c0-1 0-2 1-3l-2-1 1-1c1-1 2-1 3-2l1-1 1 1c1-1 1-2 1-4 1-2 3-3 4-5l1-1z" class="F"></path><path d="M496 508s0-1 1-1c1-1 1-1 2-1l2 1-1 1h-3-1z" class="e"></path><path d="M485 517h1c0 1-1 2-2 3h0c0 2-2 5-3 6l-1 1-1-1 6-9h0z" class="M"></path><path d="M490 507l1 1v1c-1 2-3 6-5 8h-1v-1l1-1c0-1 0-2 1-3l-2-1 1-1c1-1 2-1 3-2l1-1z" class="S"></path><path d="M489 508c-1 2-1 3-2 4l-2-1 1-1c1-1 2-1 3-2z" class="B"></path><path d="M481 528h0c0-2 3-4 4-5 1-2 3-4 4-6 1-1 2-4 4-5 0 2-2 3-3 5s-2 3-3 5l-1 1c-1 3-3 5-5 6v-1z" class="L"></path><path d="M491 508c1-1 1-2 1-4 1-2 3-3 4-5 0 2-1 3-1 5 1 0 1-1 2 0v1c-1 0-1 0-2 1s-1 2-2 3v1c-1 0-1 0-2-1h0v-1z" class="D"></path><path d="M341 634v1l-1 2h0c-1 2-1 4 1 6 0 2 1 3 3 3-1 1-1 1-1 2h0v3h3-1c-3 1-3 0-5-2-1 0-2 0-3 1l-1 2c1 1 1 1 1 3-2 0-4-2-5-2-2-1-3-1-5 0-1 0 0-1-1-2l-2 1h0v-1-1s1-1 1-2l-1-1c1 0 2-1 2-2l2-2 1-2h1l4-2c2-1 3-3 5-4 0 0 1 0 2-1z" class="AH"></path><path d="M333 647c1 0 1 0 2 1 1 0 1 0 2 1v1l-1 2v1c-1 0-1-1-2-1 0-1-1-1-2-1h-1-1c0-2 2-3 3-4z" class="G"></path><path d="M333 647l5-5c1 2 2 4 4 5 0 1 0 1 1 1h0 0v3h3-1c-3 1-3 0-5-2-1 0-2 0-3 1v-1c-1-1-1-1-2-1-1-1-1-1-2-1z" class="c"></path><path d="M341 634v1l-1 2h0-1 0c-1 2-3 3-5 4-1 1-2 3-4 4 0 1-2 2-2 3 0 0 0 1-1 1 0 1-2 1-3 1 0 0 1-1 1-2l-1-1c1 0 2-1 2-2l2-2 1-2h1l4-2c2-1 3-3 5-4 0 0 1 0 2-1z" class="AO"></path><path d="M330 641l4-2c0 1-1 2-1 2 0 1-1 2-2 2 0 0-1 1-2 1 0 0 0-1-1-1l1-2h1z" class="AG"></path><path d="M552 552c1 2 2 5 2 7s0 4 1 5h0c-2 4-4 9-6 13v2h0c0 1 0 2-1 3h0c-1 1-2 2-3 2h-1c-5 3-9 5-15 6-3 0-6 0-9-1v-1c1 0 3-1 4-1 1 1 2 0 3 0s1 0 1 1c4-1 8 0 10-3h0-3l-2-1-1-1-2-3 9-2c2-3 6-4 9-6 1 0 2 0 3-1 2-7 2-12 0-19h1z" class="Q"></path><path d="M544 584l5-5c0 1 0 2-1 3h0c-1 1-2 2-3 2h-1z" class="S"></path><path d="M543 580h2c-2 2-4 4-7 5h0c0-1 1-2 1-3v-1c2 0 3 0 4-1z" class="Y"></path><path d="M548 572c1 0 2 0 3-1l-5 8-1 1h-2l2-1c-1-1-5 0-6-1h0c2-3 6-4 9-6z" class="B"></path><path d="M539 578h0c1 1 5 0 6 1l-2 1c-1 1-2 1-4 1v1c0 1-1 2-1 3h-3l-2-1-1-1-2-3 9-2z" class="D"></path><path d="M532 583c2-2 4-2 6-2l1 1c0 1-1 2-1 3h-3l-2-1-1-1z" class="N"></path><path d="M489 599c-2-2-2-3-2-5-1-3-1-4-1-6 1-1 1-1 2-1 0 0 0 1 1 1 1 3 4 6 7 7 1 0 2 2 2 3 2 3 7 5 10 6l2 1c0 1 0 1-1 2-2 1-7 2-9 1l-1-1c-1-1-1 0-3 1-1-1-1-2-2-2h0c-1 0-1 0-2 1l-2-2c0-2-1-4-1-6z" class="D"></path><path d="M492 597c1 0 3 1 3 2l2 3v1h-1c-1-1-1-1-1-2l-3-3v-1z" class="m"></path><path d="M489 599c1 1 5 6 5 7h0c-1 0-1 0-2 1l-2-2c0-2-1-4-1-6zm19 5l2 1c0 1 0 1-1 2-2 1-7 2-9 1 1-1 3-2 5-3h3v-1z" class="E"></path><path d="M166 228v-1c0 1 0 2 1 4l1-1-1 6v3c1 2 1 4 1 6 1 0 0 1 0 2 0 0-1 1-1 2 1 0 2 4 2 5 0 0-1 1-1 2s1 2 1 3v-1s-1 0-1-1c-1 0-2 1-3 2s-3 1-5 2c0 0 0 1-1 1-1 1-3 3-5 3h0 0c4-3 8-5 12-8l-1-1c-1 1-2 1-3 0l-2-1h3v-2h-2c-2-2-5-1-7-1 1-1 3-1 5-1-2-1-4-1-6-1-1 0-1-1-2-1s-2 1-3 0h1l3-2h6v-1h2c1 0 1-1 2-1l-2-4 1-1s1 2 2 3v-6h0c0-3 0-6 1-8v3l1-1h-1v-1c1 0 1-1 1-1l1-1z" class="J"></path><path d="M162 245c0 1 0 2 1 3-1 0-2-1-3 0l-2-1v-1h2c1 0 1-1 2-1z" class="F"></path><path d="M160 248c1-1 2 0 3 0v1s-1 1-2 1l-2-1c-1 1-2 0-4 0 1 0 3-1 4-1l1 1v-1z" class="X"></path><path d="M163 253c1 2 2 2 4 3h0v1h-1l-1-1c-1 1-2 1-3 0l-2-1h3v-2z" class="B"></path><path d="M165 245c1 1 1 1 1 2s1 1 1 2c1 0 2 4 2 5 0 0-1 1-1 2 0-2-2-4-2-6-1-1-1-3-1-5zm-13 2h6l2 1v1l-1-1c-1 0-3 1-4 1h-3-1-2l3-2z" class="W"></path><path d="M167 239c1 2 1 4 1 6 1 0 0 1 0 2 0 0-1 1-1 2 0-1-1-1-1-2s0-1-1-2c0-2-1-3-1-5l1 3h2v-1-3z" class="m"></path><path d="M164 240c0-2 0-5 1-7 0 2 0 3 1 4 1 0 1 0 1-1v3 3 1h-2l-1-3z" class="w"></path><path d="M166 228v-1c0 1 0 2 1 4l1-1-1 6c0 1 0 1-1 1-1-1-1-2-1-4 0-1 0-3 1-5z" class="AV"></path><path d="M475 407c2 0 4-1 6 0h4c2 0 3 1 5 2 1 1 3 1 4 2h0-1l6 3c-1 1 0 3 1 4l1 1 1 1v2c-3 1-6 0-9 1h-1-1c0-1 1-1 1-1h0c0-1-1-2-2-3l-5-5h-1c-2-1-3-1-4-3 0 0-1 0-1-1-2 0-5-1-7 0-1 0-2-1-2-2 1-1 3-1 5-1z" class="C"></path><path d="M488 409c2 1 5 2 6 4 0 1 0 1 1 1l-1 1c-1 0-5-3-6-2-1 0-2 0-2-1h0 1 0v-1c1 1 1 1 2 1-1-1-1-2-1-3z" class="E"></path><path d="M475 407c2 0 4-1 6 0h4l2 2h1c0 1 0 2 1 3-1 0-1 0-2-1v1h0-1 0c0 1 1 1 2 1l2 2h0c-2 0-3-1-4-1h-1-1c-2-1-3-1-4-3 0 0-1 0-1-1-2 0-5-1-7 0-1 0-2-1-2-2 1-1 3-1 5-1z" class="Q"></path><path d="M480 411l6 3h-1-1c-2-1-3-1-4-3zm7-2h1c0 1 0 2 1 3-1 0-1 0-2-1v1h0-1 0l-7-3h4c2 0 3 1 4 1v-1z" class="D"></path><path d="M82 544h1v3 1l-1 2c-1 3-1 4-1 7h1v3 3 4h0v3h-2c0 1 1 1 2 2s1 2 2 2c0 1 0 2-1 2s0 0-1 1c1 1 2 3 3 4h1 1c0 2 2 3 3 4h0c-7-3-11-9-13-15l-2-1c-3-6-2-11 0-17v-2c1-2 3-4 5-5l2-1z" class="M"></path><path d="M75 550c1-2 3-4 5-5l-3 6-2 1v-2z" class="Q"></path><path d="M81 557h1v3 3 4h0v3h-2v-1c-1-1 0-10 1-12z" class="AF"></path><path d="M75 552l2-1c-2 5-3 9-2 14 1 2 1 4 2 5l-2-1c-3-6-2-11 0-17z" class="W"></path><path d="M360 264h7l1 1c2 1 5 2 6 4h-1c0 1 1 1 1 2v1 1c-2 0-4-1-5 0l-1 1h0l-2-1h-5-5-4c-2 0-2-1-3-1h-1v-2l-3 1h-1c1-1 1-1 1-2-1 0-3 1-4 1 2-1 3-3 5-4 1 0 1 1 1 1 1 0 2-1 3-1l5-2h5z" class="z"></path><path d="M362 265c1-1 1-1 2-1 1 1 1 1 3 1h1c2 1 5 2 6 4h-1l-6-3h-2c-2-1-2-1-3-1zm3 5c1-1 2-1 3-1h0c2 0 4 2 6 3v1c-2 0-4-1-5 0l-1 1h0l-2-1s0-1 1-1h0v-1c-1 0-1 0-2-1h0z" class="AO"></path><path d="M356 268c1 0 3-1 4 0h1s1 0 1 1c1 0 2 1 3 1h0c1 1 1 1 2 1v1h0c-1 0-1 1-1 1h-5l2-1h0c-1-1-2-2-4-1h-1v-1-1l-2-1z" class="AL"></path><path d="M355 268h1l2 1v1 1h1c2-1 3 0 4 1h0l-2 1h-5-4c-2 0-2-1-3-1h-1v-2l5-1 1-1h1z" class="y"></path><path d="M353 269l1-1c1 1 2 1 2 1 0 1-2 1-2 1l-1-1z" class="AL"></path><path d="M348 270l5-1 1 1c-1 1-2 1-3 2l1 1c-2 0-2-1-3-1h-1v-2z" class="C"></path><path d="M360 264h7l1 1h-1c-2 0-2 0-3-1-1 0-1 0-2 1h-6 0c-1 1-1 1-2 1v1l1 1h-1l-1 1-5 1-3 1h-1c1-1 1-1 1-2-1 0-3 1-4 1 2-1 3-3 5-4 1 0 1 1 1 1 1 0 2-1 3-1l5-2h5z" class="V"></path><path d="M391 322c-4-6-5-14-4-21v-7c1-3 1-6 3-10l1-2c1 0 1 0 2-1h1l-1 1c0 1 1 3 1 5 1 1 0 3 0 5l1 1c-1 5-2 10-2 15 0 4 1 8 1 12-1 1-2 2-3 2z" class="D"></path><defs><linearGradient id="M" x1="449.029" y1="250.705" x2="421.204" y2="228.082" xlink:href="#B"><stop offset="0" stop-color="#70664e"></stop><stop offset="1" stop-color="#907960"></stop></linearGradient></defs><path fill="url(#M)" d="M459 238c0 1-1 3-1 4h-8-8c0 1 1 2 0 4l-1-1h-3c-1 0-2-1-3-1s-3 0-4 1v1c-1 1-1 1-3 1-1 1-1 1-2 1 0-2 0-3 1-5v-1h-9c-1 0-3 0-5-1-1 0-1-1-1-3h0c2 1 8 0 10 0h29 6 2z"></path><path d="M418 242c5-1 10 0 14 0l1 1-4 1c0-1-1-1-2-1h0v-1h-9z" class="M"></path><path d="M427 243h0c1 0 2 0 2 1l4-1c1 0 2 0 2 1-1 0-3 0-4 1v1c-1 1-1 1-3 1-1 1-1 1-2 1 0-2 0-3 1-5z" class="AM"></path><path d="M432 242h12 6-8c0 1 1 2 0 4l-1-1h-3c-1 0-2-1-3-1 0-1-1-1-2-1l-1-1z" class="F"></path><path d="M368 315l1 1 5 1c-1 2-1 3-1 5v4c0 2 0 3 1 4h-1l-3 1c-1 1-3 1-4 1v-1c-1-1-2 0-3 0h-5c-1-1-3 0-4-1-1 0-1 0-2-1h-2c-1-1-1-1-3-1h0 0c2-1 4-2 6-4l6-4h1l6-3 2-2z" class="u"></path><path d="M365 322c0 1 1 2 1 3s-1 1-1 2h-3c-1-1-1-2-1-3s1-1 2-2h2z" class="S"></path><path d="M360 320l6-3v2c0 1-2 2-3 3h0c-1 1-2 1-2 2-1 0-2-1-2 0-2 1-3 2-4 3 0-1-1-2-2-3l6-4h1z" class="y"></path><path d="M368 315l1 1 5 1c-1 2-1 3-1 5v4c0 2 0 3 1 4h-1l-3 1h-2c-1 0-1 0-2-1v-1l1-1v-1c-1-1-1-2-1-2 0-1-1-2-1-3h-2 0c1-1 3-2 3-3v-2l2-2z" class="AR"></path><path d="M367 327h0c1-1 1-2 1-3v7c-1 0-1 0-2-1v-1l1-1v-1z" class="AP"></path><path d="M365 322c0-1 1-1 1-1h1 1v3c0 1 0 2-1 3h0c-1-1-1-2-1-2 0-1-1-2-1-3z" class="x"></path><path d="M368 315l1 1c-1 2-1 3-1 5h-1-1s-1 0-1 1h-2 0c1-1 3-2 3-3v-2l2-2z" class="AJ"></path><path d="M433 532l1 1 1 1c1 1 1 2 1 3 0 3-1 6-2 8h-1v1c-1 2-1 3-2 5l-1-2c-1 2-3 5-6 6h-1 0l-3 3c-1 0-2 0-2 1h-1c-1-1-1-1-1-2v-2c1-2 2-5 3-7h3v-1-2h-1l1-1h0c0-1-1-2 0-3v-3h1c1-1 2-2 3-2h3 1v-1s1 0 1-1c1 0 1-1 2-2z" class="M"></path><path d="M430 549l3-4h0v1c-1 2-1 3-2 5l-1-2z" class="Y"></path><path d="M425 545l-2-2v-3l1 1c0 1 1 1 2 3h0l-1 1z" class="B"></path><path d="M433 532l1 1c0 1-2 4-3 5h0c-1-1-1-2-1-3 0 0 1 0 1-1 1 0 1-1 2-2z" class="S"></path><path d="M422 547v-2h-1l1-1c1 1 1 2 1 5-2 1-5 4-5 6v1l-2 1v-2c1-2 2-5 3-7h3v-1z" class="B"></path><path d="M426 544c0-1 0-4 1-5 0 0 1 0 1-1 1-1 1-1 2 0 0 1-2 7-2 9-1 2-2 4-4 5h0c-2 1-3 2-4 3 1-2 2-4 3-5 1-2 1-3 2-5l1-1z" class="D"></path><path d="M111 561h2c-1 0-1 1-1 2l1 1v1c2 1 2-1 4 1-1 3-2 5-4 7-3 2-5 3-8 4h-2c-2 0-5 1-7 0s-4-1-7-2v1c-1-1-3-2-4-3l-1 1c-1 0-1-1-2-2s-2-1-2-2h2v-3h0v-4c1 2 2 3 3 4s2 1 2 2c1 1 1 1 1 2l1-1c1-1 0-2 1-2v-1l2 2h0c3 2 5 2 8 2 4-1 6-2 8-5l3-5z" class="AK"></path><path d="M92 569c3 2 5 2 8 2 1 0 2 0 3 1h1 0 1c-1 1 0 1-1 1l1 1h-2c-1 1-2 1-4 0l-2-1c-1 0-2-1-3-1-1-1-2-1-2-2v-1z" class="AS"></path><path d="M111 561h2c-1 0-1 1-1 2l1 1v1c2 1 2-1 4 1-1 3-2 5-4 7h0v-1h0c0-2 1-3 2-5l-1-1h-1v2 1c-1 0-2 1-2 1v-2c-1 0-1 1-2 1l-1-1c1-1 2 0 3-1s1-2 1-3h-1c-1 1-1 1-3 2h0l3-5z" class="Q"></path><path d="M82 563c1 2 2 3 3 4s2 1 2 2c1 1 1 1 1 2l1-1c1-1 0-2 1-2v-1l2 2h0v1c0 1 1 1 2 2-2 0-3 0-4-1h-2v1h1c2 1 4 3 6 4l1 1c-2-1-4-1-7-2v1c-1-1-3-2-4-3l-1 1c-1 0-1-1-2-2s-2-1-2-2h2v-3h0v-4z" class="w"></path><path d="M82 563c1 2 2 3 3 4s2 1 2 2c1 1 1 1 1 2h0l-1 1h-1v-2c-1-2-2-2-4-3h0v-4z" class="m"></path><path d="M80 570h2c3 1 5 3 7 5v1c-1-1-3-2-4-3l-1 1c-1 0-1-1-2-2s-2-1-2-2z" class="AR"></path><path d="M360 259c3 0 7-1 10 0h4c2 0 3 1 4 2 2 3 4 5 6 7 1 3 1 5 0 8v1c0 1-1 1-1 2 0 2 0 3-1 4l1 1-1 1v-1c-1 2-1 3-2 4h-1c1-2 1-4 1-6h0 1v-1h-1c1-1 1-1 1-2l-1-3-1 1v-1c-1 1-1 2-2 2 0-2-1-4-3-5v-1-1c0-1-1-1-1-2h1c-1-2-4-3-6-4l-1-1h-7-5l-1-1c0-1 2-1 3-2h1-3l5-2z" class="d"></path><path d="M358 261c3-1 6-1 9-1 2 0 4 1 6 2v1h-1c0 1 1 1 1 2-2 0-4-2-6-1h-7-5l-1-1c0-1 2-1 3-2h1 0z" class="r"></path><path d="M358 261h0c3 0 5 0 8 1h1-2l-1 1h0-7v-1h0v-1h1z" class="v"></path><path d="M367 260c2 0 4 1 6 2v1h-1c0 1 1 1 1 2-2 0-4-2-6-1h-7c2-1 5 0 7-1h2v-1h0c-1-1-2-1-2-2z" class="K"></path><path d="M373 262c3 2 7 6 8 9v1c1 1 1 2 1 4 0 1-1 1-1 2v1l-1-3-1 1v-1c-1 1-1 2-2 2 0-2-1-4-3-5v-1-1c0-1-1-1-1-2h1c-1-2-4-3-6-4l-1-1c2-1 4 1 6 1 0-1-1-1-1-2h1v-1z" class="i"></path><path d="M374 269c3 2 4 4 5 7-1 1-1 2-2 2 0-2-1-4-3-5v-1-1c0-1-1-1-1-2h1z" class="AG"></path><path d="M380 276c-1-4-3-7-5-10h0 1c2 2 3 4 4 6h1c1 1 1 2 1 4 0 1-1 1-1 2v1l-1-3z" class="k"></path><defs><linearGradient id="N" x1="495.422" y1="398.338" x2="481.542" y2="409.964" xlink:href="#B"><stop offset="0" stop-color="#4b4b4a"></stop><stop offset="1" stop-color="#6f6e6d"></stop></linearGradient></defs><path fill="url(#N)" d="M482 399l5-3c3 0 7-1 8 1h2l-2 2h-1 0c2 2 5 4 8 5-1 1-1 1-3 2h1l-1 1h1l3 1h0l2 1v1h-4-2v1h-2-3 0c-1-1-3-1-4-2-2-1-3-2-5-2h-4c-2-1-4 0-6 0v-1c-2 0-3 0-4-1 2-1 4-3 7-4l4-2z"></path><path d="M482 399l5-3c3 0 7-1 8 1-3 0-5 0-8 1-1 1-3 1-4 1h-1z" class="AF"></path><path d="M471 405c2-1 4-3 7-4v1l1-1c3-1 7 0 10 0v1h-4c-2 0-5 0-7 1h0c-1 0-2 1-2 1 0 1 0 1 1 1l-2 1c-2 0-3 0-4-1z" class="N"></path><path d="M477 405c3-1 8-1 11 0s5 3 9 3h2l1-1 3 1h0l2 1v1h-4-2v1h-2-3 0c-1-1-3-1-4-2-2-1-3-2-5-2h-4c-2-1-4 0-6 0v-1l2-1z" class="W"></path><path d="M503 408l2 1v1h-4-2v1h-2-3 0c-1-1-3-1-4-2 1 0 3 1 5 1 2-1 3 0 4-1h4v-1z" class="B"></path><path d="M303 637h1l1-1c1 1 3 2 4 3l2 2c3 3 5 6 7 9v1h-1v2h-1l1 2 2 2c1 1 1 2 3 4l-1 2c-1 0-1 1-1 1h-1v-2h-1c-1-1-3-1-4-1h0c0-1 0-1 1-2l-3-3 1-1 3 1c0 1 0 1 1 2v-1-1c-2-2-4-3-6-4-2 0-2 1-4 0h0-4s-1 0-1 1h-1l-1 1h-1c-1 0-3 0-4-1 0-1-1-2-1-3 1-3 6-4 7-6h1 0c1-3 1-5 1-7z" class="c"></path><path d="M302 644s1 0 1-1c1 0 1-1 1-1 1-1 3 0 4 0l1 1c-1 1-1 0-1 2 0 1 0 1 1 1 0 1 0 1 1 1 0 1 1 1 2 2 1 0 1 1 2 1v1c-1 0-2 0-3 1h-4 0-4s-1 0-1 1h-1l-1 1h-1c-1 0-3 0-4-1 0-1-1-2-1-3 1-3 6-4 7-6h1z" class="T"></path><path d="M303 649c-1 0-1-1-2-1 0-1 0-2 1-3h0c1 0 1-1 2-1s1 0 2 1h-2v3c1 0 2 0 3 1h-3-1z" class="G"></path><path d="M307 649c-1-1-2-1-3-1v-3h2c1 1 3 2 3 5l-1 1h0l-1-2z" class="a"></path><path d="M303 649h1c1 1 2 1 3 3h-4s-1 0-1 1h-1l-1 1v-2c0-1 2-2 3-3z" class="l"></path><path d="M503 532c3 0 5-3 8-3 1 0 2 0 3-1 2 0 3 0 5 1l1 2c0-1 0-1 1-1 1-1 1-1 2-1s2 0 3 1v1h2 3v1l-1 1 3 1 3 1v1c-3-1-6-1-9-1h0 0v1l4 1-1 1c-1-1-1-1-3-1 1 1 2 1 2 1h1 1l1 1h0 1c2 1 3 3 4 4l-1 1s0-1-1-1c0 0-1-1-1 0h-2l-9-4c-6-2-12-2-19-2h-6l-1-1-3 1 1-2c2 0 4-2 6-2l2-1z" class="AV"></path><path d="M528 531h3v1l-1 1-6-1h3l1-1z" class="E"></path><path d="M503 532c3 0 5-3 8-3 1 0 2 0 3-1 2 0 3 0 5 1l1 2c0-1 0-1 1-1 1-1 1-1 2-1s2 0 3 1v1h2l-1 1h-3c-5 0-10-1-14 0-3 0-6 1-9 1l2-1z" class="X"></path><path d="M497 536c8-2 16-2 24-1l2 1h4l4 1-1 1c-1-1-1-1-3-1 1 1 2 1 2 1h1 1l1 1h0 1c2 1 3 3 4 4l-1 1s0-1-1-1c0 0-1-1-1 0h-2l-9-4c-6-2-12-2-19-2h-6l-1-1z" class="G"></path><path d="M523 536h4l4 1-1 1c-1-1-1-1-3-1 1 1 2 1 2 1h1 1l1 1h0 1c2 1 3 3 4 4l-1 1s0-1-1-1c0 0-1-1-1 0h-2l-9-4h4c1 1 2 1 3 1s1 0 2 1h1 1c-1-1-2-1-4-2-1 0-4-1-5-2 0-1-1-1-2-1z" class="i"></path><path d="M425 454v-2c1-1 1-2 1-2 1 0 1 0 2 1s1 1 2 1c1 1 1 1 2 1 1 1 2 1 4 1 0 2 1 5 1 7l1 5v9h-7v1c-1 0-2 1-2 1-1 0-3 0-4 1v-1-1h-1c0-1 1-2 0-3v-2l-1-1 1-1h0l1-1v-3 1l-1 1v-3h1v-4l1-1c0-1 0-1-1-2v-2h0v-1z" class="Q"></path><path d="M427 462h2l1 1h-2-1v-1z" class="w"></path><path d="M425 454v-2c1-1 1-2 1-2 1 0 1 0 2 1s1 1 2 1c1 1 1 1 2 1l-1 1v7l-1-1v-1-2c-1-1 0-3-1-4h-1c-1 1-1 2 0 3v2h1c-1 1-1 1-2 1 0 0 0 1-1 1 0 1 1 1 1 2v1c-1 2-1 3-1 5-1 2 0 3 0 5 1 1 1 2 1 3 1 0 3 0 4-1v1c-1 0-2 1-2 1-1 0-3 0-4 1v-1-1h-1c0-1 1-2 0-3v-2l-1-1 1-1h0l1-1v-3 1l-1 1v-3h1v-4l1-1c0-1 0-1-1-2v-2h0v-1z" class="W"></path><path d="M424 469l1 1v6h-1c0-1 1-2 0-3v-2l-1-1 1-1z" class="J"></path><path d="M431 461v-7l1-1c1 1 2 1 4 1 0 2 1 5 1 7h-1v3h-1c-1 0-1 0-1 1l1 1s1 0 1 1v2l-2 2v2h-3v-1-3-8z" class="w"></path><path d="M432 453c1 1 2 1 4 1 0 2 1 5 1 7h-1-2c-1-2 0-1 0-3-1-1-2-3-2-5z" class="W"></path><path d="M161 264h3l1 1s1 0 1-1v-1h1v1c0 1 0 1 1 1v4c1 1 0 3 1 4 0 2 1 3 1 4v6c0 2-2 3-3 4h-1-3c-1 0-1-1-1-2-1 1-2 3-3 4l2-11c0-3 1-5 1-8-1 1-2 1-3 2-2 1-3 4-4 6s-2 3-3 4h-1v-3c-1 0-4 3-5 4h-1c1-2 1-2 2-3 1-2 2-3 4-4l-1-1-1-1h1l2-2c0-1 0-1-1-2l-1-1 1-1v1c1 1 1 1 2 1 1-1 2-2 2-3l1-1c0 1 1 1 1 1h0v1h0c0-1 1-1 1-1 0-1 1-1 1-2 1-1 1 0 2-1z" class="B"></path><path d="M167 279l1 1v4h-1v-5 1-1z" class="E"></path><path d="M162 285c0-1 1-2 2-3v1l-1 4c1 0 2-1 3 0h-3c-1 0-1-1-1-2z" class="Y"></path><path d="M149 274h1l2-2c0-1 0-1-1-2l-1-1 1-1v1c1 1 1 1 2 1s2 0 3-1v1c0 1-1 2-2 3-1 0 0-1-2 0l-1 2h1l1-1 1 1c0 2-2 4-2 6h0c1-2 2-2 3-3-1 2-2 3-3 4h-1v-3c-1 0-4 3-5 4h-1c1-2 1-2 2-3 1-2 2-3 4-4l-1-1-1-1z" class="C"></path><path d="M162 270l3-3h0v8 6l-1 2v-1c-1 1-2 2-2 3-1 1-2 3-3 4l2-11c0-3 1-5 1-8z" class="M"></path><defs><linearGradient id="O" x1="193.657" y1="274.122" x2="165.581" y2="251.355" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#585756"></stop></linearGradient></defs><path fill="url(#O)" d="M167 249c0-1 1-2 1-2h1c0 1 0 2 1 3h1l1-1h1l1 1 4 5 6 3c0 1 0 2 1 3h2v1l1 1v2h-1c2 1 5 3 7 3l2 1c-1 1-1 2-1 3 0 2 0 2-1 3-5 0-9-3-13-5-5-3-9-7-12-11 0-1-1-2-1-3s1-2 1-2c0-1-1-5-2-5z"></path><path d="M172 253c2 1 5 5 7 7h-1c-2 0-2-1-3-2-2-1-2-3-3-5z" class="R"></path><path d="M172 249h1l1 1 4 5 6 3c0 1 0 2 1 3h2v1l1 1v2h-1c-1 0-2-1-3-2-2 0-4-2-5-3-2-2-5-6-7-7-1-1-1-2-1-3l1-1z" class="T"></path><path d="M178 255l6 3c0 1 0 2 1 3h2v1l1 1v2h-1c-1 0-2-1-3-2 0-1-1-2-2-3h0c-2-2-3-3-4-5z" class="AV"></path><path d="M182 260c2 1 4 2 5 2l1 1v2h-1c-1 0-2-1-3-2 0-1-1-2-2-3z" class="AU"></path><path d="M435 534c1 0 2 1 2 1 1 0 0 2 2 1h4 5 5-2c-1 1-1 1-1 2 3 0 5 2 6 4 0 1-1 2-3 3-1 1-3 2-4 3h-2c-3 2-8 6-11 6-1 0-2 1-2 1-1 1-2 1-3 2l-7 2s1-2 2-2h1c0-1 0-1 1-2s2-3 3-4c1-2 1-3 2-5v-1h1c1-2 2-5 2-8 0-1 0-2-1-3z" class="Y"></path><path d="M433 546l1 1v1s-1 1-1 2 0 1 1 1c-2 2-3 3-5 4h-1c1-1 2-3 3-4 1-2 1-3 2-5z" class="F"></path><path d="M439 543c1-2 2-3 3-3 0 4-3 8-6 10-1 1-1 1-2 1s-1 0-1-1 1-2 1-2v-1l2-2c0-1 1-2 2-3 1 0 0 1 1 1z" class="X"></path><path d="M436 545l1 1-3 3v-1-1l2-2z" class="D"></path><path d="M436 545c0-1 1-2 2-3 1 0 0 1 1 1l-2 3-1-1z" class="N"></path><defs><linearGradient id="P" x1="432.319" y1="543.266" x2="442.673" y2="537.328" xlink:href="#B"><stop offset="0" stop-color="#323231"></stop><stop offset="1" stop-color="#4e4d4e"></stop></linearGradient></defs><path fill="url(#P)" d="M435 534c1 0 2 1 2 1 1 0 0 2 2 1h4v1 1l-1 2c-1 0-2 1-3 3-1 0 0-1-1-1-1 1-2 2-2 3l-2 2-1-1v-1h1c1-2 2-5 2-8 0-1 0-2-1-3z"></path><path d="M438 542l2-3c0-1 2-2 3-2v1l-1 2c-1 0-2 1-3 3-1 0 0-1-1-1z" class="m"></path><path d="M448 536h5-2c-1 1-1 1-1 2 3 0 5 2 6 4 0 1-1 2-3 3-1 1-3 2-4 3h-2c-3 2-8 6-11 6 0 0 1-1 1-2 1-1 1-2 2-3s2-3 3-4l2-5v-2h-1v-1-1h5z" class="D"></path><path d="M450 538h0c-2 0-3 0-4-1v-1h5c-1 1-1 1-1 2z" class="J"></path><path d="M444 540l-1 5c1 0 1 1 3 0h0c1-1 1 0 2-2h2l1 1-1 1h-1l-3 1c-2 0-3 0-4-1l2-5z" class="X"></path><path d="M446 546l3-1h1l-3 3c-3 2-8 6-11 6 0 0 1-1 1-2 1-1 1-2 2-3s2-3 3-4c1 1 2 1 4 1z" class="J"></path><path d="M439 549c1-1 2-3 3-4 1 1 2 1 4 1-2 2-5 4-7 4v-1z" class="F"></path><path d="M385 553c2 1 3 1 5 2 1 1 5 2 7 3 3 3 7 7 8 11v1c1 4 2 8 1 12h0c0 3-1 5-2 7v-1h0v-1c-2 4-6 8-10 9h-2c4-3 7-6 9-11v-1c1-4 1-8-1-12l-1-1c-1-3-3-5-5-7-1-1-2-2-3-2-1-2-4-2-5-4-3-1-5-1-8-1l-9 3c-1 0-2 1-3 2-1 0-1 1-2 1-1 1-2 1-3 1h-1c1-1 2-1 2-2 1-2 2-2 3-4h1l9-3 7-1h3v-1z" class="E"></path><path d="M375 555l7-1 1 1c-2 1-6 1-8 1v-1z" class="B"></path><path d="M404 581h1c1 0 1 1 1 1 0 3-1 5-2 7v-1h0v-1c-1 0 0-5 0-6z" class="Q"></path><path d="M404 581c0-2 1-4 1-6 0-1-1-3-1-4s1-1 1-1c1 4 2 8 1 12h0s0-1-1-1h-1z" class="N"></path><path d="M365 558h1l9-3v1c-1 1-2 2-4 2-1 0-2 1-3 1l-6 3c1-2 2-2 3-4zm13-1c5-1 10 0 14 3 2 1 6 4 6 7 1 2 2 3 2 5l-1-1c-1-3-3-5-5-7-1-1-2-2-3-2-1-2-4-2-5-4-3-1-5-1-8-1zm-33-340c1 1 2 3 2 5l1 1c0 1 0 2 1 3h1c-1 0-1-1-1-1 0-1-1-3-2-4v-1h0v-2c-1 0-1-1-1-1 0-1-1-2-1-3-1-1-1-4-1-5 1-1 2 0 2-3h-1c-1 0-1 0-2 1h-2l-2-2-2-2c-1 0 0-1-1-1l1-1c2 0 2 2 3 3l1-1-1-1h1 1 1c-1-1-4-3-5-3l-1-1v-1h1l1 1h1c1 1 1 0 2 0l2 2v2l3 3h1c2 4 5 7 6 11l2 7 1 2v8c-1 5-3 11-6 14h-1 0l1-1v-1c1-1 1-1 1-2h1c0-1 0-1 1-2v-2-1c1 0 1-1 1-2s0-1 1-2h0c-1-2-2-1-3-1l-3-3v-1-1-1h-1v1c0 1 0 1 1 2 0 1 1 1 1 2h0-1c-1-1-1-1-2-1h-1 0c0 1 0 1 1 1s1 0 2 1l-1 1-1-1h-1c2 2 3 2 3 4h1v-1c0-1 0-1 1-1 1 1 1 2 2 3h0-1c0-1-1-1-2 0h-2-1 1l1-1c-1-2-4-3-6-5h0-1c0-2 0-2 1-4h0-1v-1h0c2-2 2-3 4-3 0-1 0-1-1-2 0 1 0 1-1 1v-1c0-1-1-1-1-2l1-1v-1-1z" class="M"></path><path d="M347 215v1h1c1-1 1-1 2-1h0l-1 1c0 2 0 3 1 5v4h-1c0-4-2-7-2-10z" class="B"></path><path d="M345 212v-1c0-1 1-1 1-2 2 0 3 2 4 3 0 1 1 2 1 2 0 1 0 1-1 1s-1 0-2 1h-1v-1-1l-2-2z" class="AJ"></path><path d="M345 212l1-1h2v3h-1l-2-2z" class="R"></path><path d="M343 227h0c2-2 2-3 4-3v1c0 1 0 1 1 2l1 3h-1c-1 1-2 1-4 2h0-1c0-2 0-2 1-4h0-1v-1z" class="s"></path><path d="M357 225v8c-1-1-1-1-2-1h-2c0-1-1-1-2-2v-3l2-1h1c0 1 0 1 1 1 0 0 1 0 1-1l1-1z" class="E"></path><path d="M351 214l2 2h1l2 7 1 2-1 1c0 1-1 1-1 1-1 0-1 0-1-1h-1c0-2-1-3-2-5h0v-1l-1 1c-1-2-1-3-1-5l1-1h0c1 0 1 0 1-1z" class="l"></path><path d="M351 214l2 2v1l1 2h-1-1v-1-1l-2-2h0c1 0 1 0 1-1z" class="z"></path><path d="M352 217v1 1h1v1l1 1c1 1 1 2 2 4-2-2-3-3-5-4h0v-1c0-1 1-2 1-3z" class="E"></path><path d="M350 215l2 2c0 1-1 2-1 3l-1 1c-1-2-1-3-1-5l1-1z" class="J"></path><defs><linearGradient id="Q" x1="481.464" y1="531.56" x2="457.862" y2="528.084" xlink:href="#B"><stop offset="0" stop-color="#3c3b3b"></stop><stop offset="1" stop-color="#595957"></stop></linearGradient></defs><path fill="url(#Q)" d="M477 514c1 0 1 0 1-1l1-1s1-1 1-2 0-1 1-2h0 1 0l-1 2h3 0 2l-1 1 2 1c-1 1-1 2-1 3l-1 1v1h0l-6 9c-4 6-9 9-14 13l-6 6c-1 0-2 1-3 2-1-1-1-1-1-2h-2c2-1 3-2 3-3-1-2-3-4-6-4 0-1 0-1 1-2h2c3 0 8-2 11-3 1 0 2-1 3-2 2-4 6-6 7-10v-1l3-6z"></path><path d="M485 517h-2l2-2v1 1h0z" class="D"></path><path d="M470 530h1 1c-1 1-4 5-6 5h0-1l3-3 2-2z" class="m"></path><path d="M479 516v-1c0-2 1-4 2-5h3 0 2l-1 1 2 1c-1 1-1 2-1 3-1 0-1 0-2-1-1 1-2 2-3 2h-2z" class="D"></path><path d="M485 511l2 1c-1 1-1 2-1 3-1 0-1 0-2-1-1 1-2 2-3 2v-3c2 0 2-1 3-1 0-1 1-1 1-1z" class="F"></path><path d="M467 531l1 1-3 3-9 7c-1-2-3-4-6-4 0-1 0-1 1-2h2c3 0 8-2 11-3 1 0 2-1 3-2z" class="AR"></path><path d="M477 514c1 0 1 0 1-1l1-1s1-1 1-2 0-1 1-2h0 1 0l-1 2c-1 1-2 3-2 5v1c0 1 1 2 0 4l1 1c-1 2-2 4-4 5h-1c-1 1-2 2-3 4h-1-1l-2 2-1-1c2-4 6-6 7-10v-1l3-6z" class="F"></path><path d="M470 530l7-10h1v2c-1 1-2 2-2 4h-1c-1 1-2 2-3 4h-1-1z" class="N"></path><defs><linearGradient id="R" x1="551.621" y1="552.283" x2="530.391" y2="553.523" xlink:href="#B"><stop offset="0" stop-color="#242323"></stop><stop offset="1" stop-color="#545555"></stop></linearGradient></defs><path fill="url(#R)" d="M527 536v-1h0 0c3 0 6 0 9 1l7 4c0-1 1-1 1-2 3 2 5 4 6 7l2 2c-1 2-1 3 0 5h-1c2 7 2 12 0 19-1 1-2 1-3 1 1 0 1-1 1-1-1-1-3 0-4-2l1-2v-1h0c-1 1-1 1-2 1h-1c0-1 1-1 1-2s1-1 1-2 1-4 1-5c-1-1-2-2-2-3-1-2-3-5-5-7l-1-2c-1-1-2-1-3-1s-2-2-3-2h2c0-1 1 0 1 0 1 0 1 1 1 1l1-1c-1-1-2-3-4-4h-1 0l-1-1h-1-1s-1 0-2-1c2 0 2 0 3 1l1-1-4-1z"></path><path d="M550 545l2 2c-1 2-1 3 0 5h-1l-2-5 1-2z" class="w"></path><defs><linearGradient id="S" x1="545.675" y1="545.061" x2="546.758" y2="539.244" xlink:href="#B"><stop offset="0" stop-color="#6d6d69"></stop><stop offset="1" stop-color="#818176"></stop></linearGradient></defs><path fill="url(#S)" d="M543 540c0-1 1-1 1-2 3 2 5 4 6 7l-1 2c-2-3-4-5-6-7z"></path><path d="M546 554c2 3 2 9 1 12l-1 1v-1h0c-1 1-1 1-2 1h-1c0-1 1-1 1-2s1-1 1-2 1-4 1-5c-1-1-2-2-2-3h2v-1z" class="N"></path><path d="M542 544c2 2 4 4 3 7l1 3v1h-2c-1-2-3-5-5-7l-1-2 1-1c1 1 2 2 2 3 1 0 1 1 2 2v-1l-2-4 1-1z" class="e"></path><path d="M532 543h2c0-1 1 0 1 0 1 0 1 1 1 1l1-1c-1-1-2-3-4-4h-1 0l-1-1h-1-1s-1 0-2-1c2 0 2 0 3 1l1-1c5 2 8 4 11 7l-1 1 2 4v1c-1-1-1-2-2-2 0-1-1-2-2-3l-1 1c-1-1-2-1-3-1s-2-2-3-2z" class="f"></path><path d="M430 224h2 0c2 1 4 0 6 1s4 4 7 5l3 2c1 1 1 2 2 3 0 1 1 2 1 3h-29c-2 0-8 1-10 0 5-6 13-9 18-14z" class="AB"></path><path d="M516 563h2 1v3c1 1 2 1 3 2h-1l1 3 3 1v1h-1c-1 1-2 1-2 2v1c3 0 6 1 9 1l1-1c2 0 3 0 5-1 3-1 6-3 8-6 1 2 3 1 4 2 0 0 0 1-1 1-3 2-7 3-9 6l-9 2 2 3 1 1h0c-1-1-2-1-3-2-5-1-5 2-8 4-3-1-6-1-9-3l-3-3c-2-2-4-4-5-7l3-1c1 1 1 1 2 1s3 0 4 1 1 1 2 1v-1c0-1-2-2-3-3s-1-2-1-3-1-2 0-2h0s0-1 1-2h-1l1-1h1 2z" class="W"></path><path d="M516 581c0-1-1-1-1-2 1 0 1-1 1-1l1 1h1l-2 2z" class="D"></path><path d="M508 572c1 1 1 1 2 1s3 0 4 1 1 1 2 1v-1c0-1-2-2-3-3s-1-2-1-3-1-2 0-2h0c2 4 4 7 8 9h2v1c3 0 6 1 9 1h-3-4c1 0 2 0 3 1h0 1v1h-1c-1 0-2-1-3-1-2 0-4-3-6-3-1 0-2 1-3 1-1 1-4 0-6 0l-1-1v1c0 1 1 2 2 3v1c-2-2-4-4-5-7l3-1z" class="J"></path><path d="M516 563h2 1v3c1 1 2 1 3 2h-1l1 3 3 1v1h-1c-1 1-2 1-2 2v1-1h-2c-4-2-6-5-8-9 0 0 0-1 1-2h-1l1-1h1 2z" class="Q"></path><path d="M516 563c1 0 1 1 1 2h0c-2-1-2-1-3-2h2z" class="m"></path><path d="M516 563h2 1v3-2c-1 1-1 1-2 1h0 0c0-1 0-2-1-2z" class="B"></path><path d="M524 573h-1c-1 0-1-1-2-1s-1-1-2-1l1-1 2 1 3 1v1h-1zm-11-9c0 1 1 1 1 2l1 1c2 0 1-1 2-1h1v3 3c1 1 2 2 3 2h0l1 1h-2c-4-2-6-5-8-9 0 0 0-1 1-2z" class="N"></path><path d="M545 569c1 2 3 1 4 2 0 0 0 1-1 1-3 2-7 3-9 6l-9 2 2 3 1 1h0c-1-1-2-1-3-2-5-1-5 2-8 4-3-1-6-1-9-3l-3-3v-1c1 1 3 3 5 3l1-1 2-2 1-1h1v1c-1 1-1 2 0 4 0 1 1 1 2 2h1c1-2 3-4 4-6h1v-1h-1 0c-1-1-2-1-3-1h4 3l1-1c2 0 3 0 5-1 3-1 6-3 8-6z" class="Y"></path><path d="M338 557h2c0 1 1 2 1 2 2 1 3 2 5 3v-1l4 3c0 2 1 3 2 5h0c0 1-1 1-1 2h0s-1 0-1 1l-3-3h0c1 3 1 5 3 8v-1c2 1 3 2 4 4 0 0 1 1 0 2l1 5c1 1 0 2 0 2v1c-2-1-1-3-2-4v1l-1-2v3h-1v1h-2-2v1h0c-1-1-2-1-3-1l-1-1h0s0-1-1-1h-2c1 0 1-1 2-1v-3l-2-2-3-2-1-2c-1 1-2 2-2 3v1l-1-1c0-1 1-2 1-3 1-1 1-3 2-4 0-1 0-2 1-3 0 0 1 1 2 1 0-2-2-4-3-5v-2h2 0 1 0l1-2h0l1-1-3-4z" class="B"></path><path d="M344 577h1c0 1 2 2 3 3l1 1c-1 0-2 0-4-1 0-1 0-1-1-2v-1z" class="l"></path><path d="M340 581l2-1h0c1 1 2 1 3 1h1v1c-1 0-1 1-1 2-1 0-1 0-1 1-1-2-1-2-2-3v1l-2-2z" class="u"></path><path d="M344 571l2 2h1l-1 1c-1 1-2 2-4 3 0-1 0 0-1-1l3-5z" class="J"></path><path d="M346 581l1 1h3v3c0 1-1 1-1 1h-4-1v-1c0-1 0-1 1-1 0-1 0-2 1-2v-1z" class="F"></path><path d="M346 581l1 1h-1c1 1 1 1 0 3h1c1 0 1 0 2 1h-4-1v-1c0-1 0-1 1-1 0-1 0-2 1-2v-1z" class="C"></path><path d="M347 569h0c1 3 1 5 3 8v1l1 4 1 3h0v3h-1l-1-3v-3l-3-9h-1v-2h1v-2z" class="AP"></path><path d="M342 583v-1c1 1 1 1 2 3v1h1 4s1 0 1-1l1 3v1h-2-2v1h0c-1-1-2-1-3-1l-1-1h0s0-1-1-1h-2c1 0 1-1 2-1v-3z" class="B"></path><path d="M349 586s1 0 1-1l1 3v1h-2-2l1-1h1l-1-1c-1 0-2 1-3 0v-1h4z" class="l"></path><path d="M350 577v-1c2 1 3 2 4 4 0 0 1 1 0 2l1 5c1 1 0 2 0 2v1c-2-1-1-3-2-4v1l-1-2h0l-1-3-1-4v-1z" class="C"></path><path d="M350 578l3 3-2 1-1-4z" class="B"></path><path d="M352 585v-2c1 1 2 4 3 4h0c1 1 0 2 0 2v1c-2-1-1-3-2-4v1l-1-2h0z" class="l"></path><path d="M336 573c0-1 0-2 1-3 0 0 1 1 2 1h3 0 2l-3 5v1c-2 0-2 0-4-1v-3h-1z" class="AJ"></path><path d="M336 573c0-1 0-2 1-3 0 0 1 1 2 1h3c-1 1-1 2-3 3v1h-1 0c0-1-1-1-1-2h-1z" class="C"></path><path d="M338 557h2c0 1 1 2 1 2 2 1 3 2 5 3v-1l4 3c0 2 1 3 2 5h0c0 1-1 1-1 2h0s-1 0-1 1l-3-3h0 0v2h-1v2l-2-2h-2 0-3c0-2-2-4-3-5v-2h2 0 1 0l1-2h0l1-1-3-4z" class="R"></path><path d="M341 564c2 0 2 0 3 1v1s0 1-1 1c0-1-1-2-2-3z" class="d"></path><path d="M340 562c0 1 1 2 1 2 1 1 2 2 2 3 1 1 2 3 3 4v2l-2-2h-2v-2h-1v-2c-1-1-1-3-1-5z" class="B"></path><path d="M340 562h0 0c0 2 0 4 1 5v2h1v2h0-3c0-2-2-4-3-5v-2h2 0 1 0l1-2z" class="M"></path><path d="M341 559c2 1 3 2 5 3v-1l4 3c0 2 1 3 2 5h0c0 1-1 1-1 2h0s-1 0-1 1l-3-3h0 0c-1-1-2-2-2-3s-1-2-2-4c0-1-1-2-2-3z" class="Q"></path><path d="M346 561l4 3c0 2 1 3 2 5h0c0 1-1 1-1 2l-5-9v-1z" class="D"></path><path d="M527 446v-3h0c-1-2-3-3-4-5v-1c12 9 17 21 19 35 2 13-1 28-9 39l-12 12c-2 1-4 2-5 4-2 0-3 1-5 2-3 0-5 3-8 3l-2 1c-2 0-4 2-6 2h-4l10-5c3-1 6-3 8-4 8-5 15-10 21-17 6-8 10-18 10-28s-2-20-7-28c-2-3-4-5-6-7z" class="AG"></path><defs><linearGradient id="T" x1="499.257" y1="533.933" x2="495.9" y2="532.012" xlink:href="#B"><stop offset="0" stop-color="#3e3b3f"></stop><stop offset="1" stop-color="#474a46"></stop></linearGradient></defs><path fill="url(#T)" d="M501 530c1 0 1 1 2 0l6-3c-1 2-5 3-6 5l-2 1c-2 0-4 2-6 2h-4l10-5z"></path><path d="M431 256c1-1 1-1 2-1h3c2 0 4 1 6 1v1 7 32c0 5 1 10 0 14v20h0c0 1 0 2 1 3-1 1-1 1-2 1l-2-1v-49-5-3h-1c-1-1-1-2-1-3h-1v-1h-1c-2-1-3-3-4-5 1 0 1-1 1-1h0-1v-1c1-2-1-6 1-9h-1z" class="O"></path><path d="M441 279v-4c0 1-1 3 0 4v4h0c1 0 1-1 1-2-1-2 0-4 0-6 0-1-1-3 0-4v7 31 1 20h0c0 1 0 2 1 3-1 1-1 1-2 1v-55z" class="n"></path><g class="i"><path d="M440 279h1v55l-2-1v-49c0-1 1 0 1 0v-5z"></path><path d="M431 256c1-1 1-1 2-1h3c2 0 4 1 6 1v1 7h0v-4c-1-1 0-3-1-4h-1-1l-1 1h1v8-1-1h1c0 2-1 3-1 5 0 1 0 3 1 4v7 5s-1-1-1 0v-5-3h-1c-1-1-1-2-1-3h-1v-1h-1c-2-1-3-3-4-5 1 0 1-1 1-1h0-1v-1c1-2-1-6 1-9h-1z"></path></g><path d="M438 267v-1h1c0 2 1 3-1 5h0l-1 2h-1v-1c1-2 2-3 2-5z" class="U"></path><path d="M440 272v7 5s-1-1-1 0v-5c0-2 0-5 1-7z" class="G"></path><path d="M432 266h1c1 0 0 0 1 1h1v-1c1 0 2 0 3 1 0 2-1 3-2 5h-1c-2-1-3-3-4-5 1 0 1-1 1-1z" class="v"></path><path d="M432 266c0-3 0-8 1-10 2 0 3 1 5 2v2c0 1 1 3 0 5-2-1-2-5-3-6l-1 1c1 2 0 5 1 6v1h-1c-1-1 0-1-1-1h-1 0z" class="r"></path><path d="M192 281c2 1 3 2 4 2-1 4-2 7-3 11l-3 19c0 2 0 5-1 8l1 7-1 1h-1c-1-2-3-4-5-5l-2-1v-1h-1l-3-3c-1 0-1 0-2-1h1c0-1 0-1-1-1-1-1-1-1-3-1v1-3c-1-1 0-1 0-1 2-2 4-3 6-4h2c1-1 3-2 5-3v-1l1-1c1-1 1-1 2-1v1s1 0 2-1c0 0 1-1 1-2 1-1 1-1 0-2 0 0-1 0-1-1s-1-2-1-3l-2-2h0l-3-5c1 0 1-1 1-2l1-2 1 1 1-1h-1c1 0 2 0 3-1l-1-1h3v-1z" class="F"></path><path d="M185 305l1-1c1-1 1-1 2-1v1l-1 1c1 1 2 2 2 3v1-1s0-1-1-1v1c-1-1-1-2-2-2h-1v-1z" class="L"></path><path d="M189 282h3l-1 2c1 1 1 3 1 4-1 1-1 2-1 2h-1v-2l-1-1c0 1 0 1-1 2h0c-1 2 1 3-1 4h0l-3-5c1 0 1-1 1-2l1-2 1 1 1-1h-1c1 0 2 0 3-1l-1-1z" class="E"></path><path d="M189 282h3l-1 2-1 2c-1 0-2-1-3-1l1-1h-1c1 0 2 0 3-1l-1-1z" class="Y"></path><path d="M185 286h0l2 2v5l-3-5c1 0 1-1 1-2z" class="D"></path><path d="M172 314c2 2 6 3 9 3v-1l1-1 1 1v-2h1l1 1v-1l1-4c1 2 2 5 3 8l-1 1 1 2 1 7-1 1h-1c-1-2-3-4-5-5l-2-1v-1h-1l-3-3c-1 0-1 0-2-1h1c0-1 0-1-1-1-1-1-1-1-3-1v1-3z" class="S"></path><defs><linearGradient id="U" x1="394.709" y1="578.121" x2="376.877" y2="604.81" xlink:href="#B"><stop offset="0" stop-color="#181819"></stop><stop offset="1" stop-color="#40403f"></stop></linearGradient></defs><path fill="url(#U)" d="M369 582c1 0 1 0 2 1s1 4 1 5 1 1 2 2v1c1 2 2 3 4 4h1 0c2 2 5 2 7 3l3-1 3-1h2c4-1 8-5 10-9v1h0v1c1-2 2-4 2-7h0v1c0 1 1 1 1 2v1h0c0 4-3 10-6 13-3 4-9 7-15 7-5 1-10-1-14-4l-3-2c-1-1-3-3-3-5h1c1-1 0-3 0-4l-1-1 1-1h0c0-2 0-3-1-5 0 0 1 0 1-1 0 0 1 0 2-1z"></path><path d="M386 598l3-1 2 2h-7v-1h2z" class="AS"></path><path d="M379 595c2 2 5 2 7 3h-2v1c-2 0-4 0-5-1v-1c-1-1 0-1 0-2z" class="AF"></path><path d="M404 587v1h0v1c-2 4-5 8-10 9l-3 1-2-2 3-1h2c4-1 8-5 10-9z" class="AU"></path><path d="M369 582c1 0 1 0 2 1s1 4 1 5 1 1 2 2v1c1 2 2 3 4 4h1 0c0 1-1 1 0 2v1h-1c-2 0-5-3-6-4-1-2-2-3-4-4 0-1-1-2 0-3v-1c-1-1-1-2-1-3 0 0 1 0 2-1z" class="D"></path><path d="M175 210c1-4 13-13 16-15 9-7 21-12 33-9h0l5 1h1v2 2c-2 0-4-1-6-1-9-2-20 0-27 5-7 4-12 9-16 16l-3 6v1c-2 4-2 8-2 13l-1-1-1 2v2-2h-1v6-1l-2 1h-1v2h-1l3 9-1 1h-1c-1-1-1-2-1-3h-1c0-1 1-2 0-2 0-2 0-4-1-6v-3l1-6-1 1c-1-2-1-3-1-4 1-1 2-4 2-6 2-4 5-8 7-11z" class="R"></path><path d="M169 234h0l1-1c1 2 1 3 1 5h-1v2h-1c0-2-1-4 0-6z" class="q"></path><path d="M224 190v-1c1 0 1 0 1-1h3c1 0 1 0 1 1h1v2c-2 0-4-1-6-1z" class="y"></path><path d="M174 225h1v5l-1 2v2-2h-1v6-1c0-4 0-8 1-12z" class="o"></path><path d="M174 225v-1c1-2 1-4 3-6h1c-2 4-2 8-2 13l-1-1v-5h-1z" class="G"></path><path d="M171 222c0 2-1 4-2 6v6c-1 2 0 4 0 6l3 9-1 1h-1c-1-1-1-2-1-3h-1c0-1 1-2 0-2 0-2 0-4-1-6v-3l1-6 3-8z" class="F"></path><path d="M175 210h1c-1 4-5 8-5 12-1 2-2 5-3 8l-1 1c-1-2-1-3-1-4 1-1 2-4 2-6 2-4 5-8 7-11z" class="H"></path><path d="M142 385h0l8 3c1 1 1 2 2 2 4 2 8 3 11 5l-1 1 13 9h-1l-4-3c-4 0-7-2-11-4-2-1-4-1-6-1h-2 0-5l-12 1h-6l-1-1h1v-1c-1 1-3 1-4 1-4 0-9-3-11-6l-3-2c3 0 5 1 7 0s4-2 6-2 3-1 4-1v1h1 0c0-1 1-1 1-1h1c1-1 1-1 3-1s7 1 9 1v-1z" class="X"></path><path d="M131 392c1 0 2-1 3-1 3-1 4-1 7-1h0l8 1v1h-3 2c-1 1-5 1-6 1-1-1-7-1-9-1-1 0-3 1-4 1l2-1z" class="F"></path><path d="M133 392c4-1 10 0 13 0h2c-1 1-5 1-6 1-1-1-7-1-9-1z" class="W"></path><path d="M128 396c2-1 4-2 6-2h1c2 0 4-1 7-1h2l-1 1h0c2 1 7 0 10 1h4c3 1 5 2 7 4l6 3c-4 0-7-2-11-4-2-1-4-1-6-1h-2 0-5l-12 1h-6l-1-1h1v-1z" class="D"></path><path d="M123 387c2 0 3-1 4-1v1h1 0c6-1 13 0 18 3h-5c-3 0-4 0-7 1-1 0-2 1-3 1l-2 1c1 0 3-1 4-1 2 0 8 0 9 1h0c-3 0-5 1-7 1h-1c-2 0-4 1-6 2-1 1-3 1-4 1-4 0-9-3-11-6l-3-2c3 0 5 1 7 0s4-2 6-2z" class="m"></path><path d="M113 391c1 0 2 0 3 1h1c2 1 5 0 7 0s5-1 7 0l-2 1c1 0 3-1 4-1 2 0 8 0 9 1h0c-3 0-5 1-7 1h-1c-2 0-4 1-6 2-1 1-3 1-4 1-4 0-9-3-11-6z" class="N"></path><path d="M124 392c2 0 5-1 7 0l-2 1c-3-1-6 2-9 1h0l4-2z" class="Q"></path><path d="M110 527l2 2c1-1 2-1 3 0h1c1 1 1 1 2 1s3 2 5 2c1 1 2 1 2 2l6 1 6 2h-6l-3-1v1c-1 0-2 0-3 1h-5c-7 0-18 0-23 5l-2 1-7 6-3 3c-2 1-3 2-3 4h-1c0-3 0-4 1-7l1-2v-1-3h-1l-2 1c-2 1-4 3-5 5h-1c2-4 5-9 9-12l6-3h1l5-1 4-1c0-1 0-2-1-3h11l1-2v-1z" class="AS"></path><path d="M82 544c1-2 2-3 4-4h4c-1 1-3 2-4 4-1 1-2 2-3 4v-1-3h-1z" class="D"></path><path d="M110 528l1 1v2h0l1-1c1 1 1 1 1 2h2c-5 0-10 0-16 1 0-1 0-2-1-3h11l1-2z" class="E"></path><path d="M110 527l2 2c1-1 2-1 3 0h1c1 1 1 1 2 1s3 2 5 2c1 1 2 1 2 2-2 0-3 0-6-1l-4-1h-2c0-1 0-1-1-2l-1 1h0v-2l-1-1v-1z" class="z"></path><path d="M110 527l2 2c2 1 5 2 7 3h1c0 1 0 1-1 1l-4-1h-2c0-1 0-1-1-2l-1 1h0v-2l-1-1v-1z" class="S"></path><defs><linearGradient id="V" x1="112.379" y1="539.156" x2="111.707" y2="534.61" xlink:href="#B"><stop offset="0" stop-color="#fee9ba"></stop><stop offset="1" stop-color="#ffffe3"></stop></linearGradient></defs><path fill="url(#V)" d="M97 537c10-3 22-3 31-1v1c-1 0-2 0-3 1h-5c-2-1-3-1-5-1-1-1-4 0-6 0-2 1-5 0-7 2h-1-2v1c-1 0-2 0-3-1v-1l1-1z"></path><path d="M102 539c2-2 5-1 7-2 2 0 5-1 6 0 2 0 3 0 5 1-7 0-18 0-23 5l-2 1-7 6-3 3c-2 1-3 2-3 4h-1c0-3 0-4 1-7l1-2c1-2 2-3 3-4 1-2 3-3 4-4 1 0 2-1 3-2 1 0 3-1 4-1l-1 1v1c1 1 2 1 3 1v-1h2 1z" class="AC"></path><path d="M97 537l-1 1v1c1 1 2 1 3 1v-1h2 1s-1 0-1 1c-1 0-1-1-2 0h-2v1c-2 0-3 2-5 2 0-1 1-3 1-5 1 0 3-1 4-1z" class="K"></path><path d="M86 544h1c2-1 2-2 4-2 0 0-1 1-2 1 0 0 0 1-1 1l1 1 1-1h0v2c1 0 2-1 3-2h2l-7 6-3 3c-2 1-3 2-3 4h-1c0-3 0-4 1-7l1-2c1-2 2-3 3-4z" class="k"></path><path d="M86 544h1c2-1 2-2 4-2 0 0-1 1-2 1 0 0 0 1-1 1l1 1-3 3c-1 1-2 3-3 4-1-1-1-1-1-2l1-2c1-2 2-3 3-4z" class="j"></path><path d="M360 564h1c1 0 2 0 3-1 1 0 1-1 2-1 1-1 2-2 3-2v1-1h4c4 0 8-1 12 0 1 0 2 1 2 1h0l2 2h-3 1l-1 1c1 1 2 2 2 3h-1c-3 0-8 2-10 4l-1 1c-1 1-2 3-3 5l-1 1v3l-1 2c-1-1-1-1-2-1-1 1-2 1-2 1 0 1-1 1-1 1 1 2 1 3 1 5h0c-2 0-2-1-3-2 0-1 0 0 1-1v-1c-1-2-2-4-3-5-1-2-2-3-3-5h-1l-1-1c-1-1-2-3-3-3l-2-2h0c-1-2-2-3-2-5l-4-3v-1c2 1 4 2 5 2s2 0 4 1h0 0 2 0 2v2l1-1z" class="AK"></path><path d="M366 563c2-1 3-2 6-2l-2 2h-2-2z" class="w"></path><path d="M360 564h1c1 0 2 0 3-1 1 0 1-1 2-1 1-1 2-2 3-2v1-1h4l-1 1c-3 0-4 1-6 2l-1 1h0v2l-1 1h2l-1 1h0c-1 0-1 0-2 1l-2-2v3c-1-1-1-3-3-3h0-1l-2-2h1l-1-2h0 2 0 2v2l1-1z" class="L"></path><path d="M356 565l-1-2h0 2 0c1 0 1 0 1 1h0v2c-1 0-1-1-2-1z" class="E"></path><path d="M361 567v-1c1 0 2-1 4-2h0v2l-1 1h2l-1 1h0c-1 0-1 0-2 1l-2-2z" class="D"></path><path d="M366 567v1c1 1 0 2 2 2h2 1c0 1 0 1 1 2-2 1-4 0-5 2l-1 1v4h0c-2-1-3-4-4-6l-1-3v-3l2 2c1-1 1-1 2-1h0l1-1z" class="C"></path><path d="M366 567v1c1 1 0 2 2 2h2 1c0 1 0 1 1 2-2 1-4 0-5 2l-1 1c0-1-1-2-2-3h2 0 0l-3-3c1-1 1-1 2-1h0l1-1z" class="Q"></path><path d="M358 567h0c2 0 2 2 3 3l1 3c1 2 2 5 4 6h0 0l2 1h0l-1 1h0l2 1c-1 1-2 1-2 1 0 1-1 1-1 1 1 2 1 3 1 5h0c-2 0-2-1-3-2 0-1 0 0 1-1v-1c-1-2-2-4-3-5-1-2-2-3-3-5h2l-3-8z" class="Y"></path><path d="M366 579l2 1h0l-1 1h0l2 1c-1 1-2 1-2 1 0 1-1 1-1 1h0c0-1 0-1-1-2h0v-2l1-1z" class="C"></path><path d="M371 570l2-1 1 1h1l1 1v1c-1 1-2 3-3 5l-1 1v3l-1 2c-1-1-1-1-2-1l-2-1h0l1-1h0l-2-1h0v-4l1-1c1-2 3-1 5-2-1-1-1-1-1-2z" class="w"></path><path d="M371 570l2-1 1 1h1l1 1v1c-1 1-2 3-3 5-1-1-2-1-2-2 1-1 2-2 2-3h-1c-1-1-1-1-1-2z" class="Q"></path><path d="M371 570l2-1 1 1c0 1 0 1-2 2-1-1-1-1-1-2z" class="W"></path><path d="M366 575l4 2c1 0 1 0 2 1v3l-1 2c-1-1-1-1-2-1l-2-1h0l1-1h0l-2-1h0v-4z" class="N"></path><path d="M367 581h2 1 2l-1 2c-1-1-1-1-2-1l-2-1z" class="Q"></path><path d="M346 561v-1c2 1 4 2 5 2s2 0 4 1h0 0 0l1 2h-1l2 2h1l3 8h-2-1l-1-1c-1-1-2-3-3-3l-2-2h0c-1-2-2-3-2-5l-4-3z" class="AT"></path><path d="M351 562c1 0 2 0 4 1h0 0 0l1 2h-1l-4-3z" class="B"></path><path d="M350 564v1c4 2 6 6 9 9-1-2-1-4-2-6v-1h1c1 3 2 5 3 8h-2-1l-1-1c-1-1-2-3-3-3l-2-2h0c-1-2-2-3-2-5z" class="N"></path><path d="M370 570s-1 0-1-1v-1c5-3 11-4 17-4 1 1 2 2 2 3h-1c-3 0-8 2-10 4l-1 1v-1l-1-1h-1l-1-1-2 1h-1z" class="AS"></path><defs><linearGradient id="W" x1="380.172" y1="569.581" x2="376.015" y2="566.497" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#474748"></stop></linearGradient></defs><path fill="url(#W)" d="M373 569c5-2 9-3 14-2-3 0-8 2-10 4l-1 1v-1l-1-1h-1l-1-1z"></path><path d="M138 562l1-1c1-3 4-4 6-5 8-2 17-2 25-1h1c1 2 2 2 4 2 0 1-1 1-1 1l-6 2c-4 1-8 4-12 6-3 2-7 3-9 5-3 2-5 5-7 7-3 1-5 3-8 4-1 0-2 2-4 1 1 0 1-1 1-1h-2c-1 0-1 0-2-1 2-1 3-3 5-4h-2 0v-1c0-2 0-4 2-5 1-1 3-3 5-4 3-1 7-1 10-2-1-1-3-2-5-2h-1c0-1 0-1-1-1z" class="W"></path><path d="M151 564h1c0 2-3 3-4 3h0l-1-1 4-2zm-13-2h1c1 0 2 0 3-1 1 0 3 1 4 2h1l1 1c-1 0-2 1-2 1h-1c-1-1-3-2-5-2h-1c0-1 0-1-1-1z" class="E"></path><path d="M130 577h-2 0v-1c0-2 0-4 2-5h0c1 1 1 1 2 1 1 1 1 2 2 2h4c-1 1-3 2-5 2-1 0-1 0-2 1h-1z" class="X"></path><path d="M461 564c2-1 3-1 5 0 3 0 5 2 8 3 1 1 2 2 4 2 1 0 3 2 4 3 5 5 8 10 12 16 1 2 2 6 4 8v2c0-1-1-3-2-3-3-1-6-4-7-7-1 0-1-1-1-1-1 0-1 0-2 1 0 2 0 3 1 6 0 2 0 3 2 5 0 2 1 4 1 6-1-1-3-2-4-4-3-3-4-11-4-15 0-2 0-4-1-6-1 0-1 0-2 1v1c-1 5 0 12 2 17 0 1 1 2 1 3-3-4-9-7-11-12-1-1-1-2-1-3s-1-1-1-2c-1-1 0-3-1-5l1-1v-2c0-1 1-1 1-1v-2h0c0-1-1-2-1-3 0-2-1-3-3-5-1-1-2-2-3-2h-1-1z" class="C"></path><path d="M169 372v-4c1 0 1-1 2-2h0c1-1 1-2 3-2 1 1 2 1 2 2h-1c0 1-1 1-2 2v4c2 5 7 6 11 9h1v1c0 1 1 1 2 2v1-1h1 0c0 2 0 2 1 3s1 3 2 4c0 2 1 4 1 6 1 2 1 4 2 6s2 3 2 5l-1 2c-2-3-4-4-6-6-1-1-2-2-4-3-1 0-2-1-3-2h0l-2-3-1 1-6-5h-1c-2-1-3-2-4-4 0-1-2-3-2-3 1-1 1-1 2-1-1-3-2-5-3-7l1-1h0 1v-2l1 1h1 1c-1-1-1-2-1-2v-1z" class="Q"></path><path d="M169 372l3 3h0v-1l-1-1v-1c-1-1-1-2 0-2v1c1 2 2 3 3 4 0 1 1 2 2 3 1 0 2 1 2 1v2 6 1c-1-2-1-6-2-8-1-1-3-1-4-2s-3-2-4-3h1 1c-1-1-1-2-1-2v-1z" class="Y"></path><path d="M178 389s1 0 1-1v-1-7h0c0 1 1 1 1 1h1c0 1 0 2 1 2l-1 1-1-1h0c1 1 1 1 2 1h0l-1-2 1-1c0 1 2 2 2 3v1h2v4h0l-1-1v1l-1-1v5 3l-1 1c0 1 0 1-1 2h0l-2-3c0-1-1-1-1-2 0-2-1-3-1-5z" class="w"></path><path d="M185 389v-1l1 1c1 2 2 4 2 6 1 1 2 2 2 3 1 1 2 2 2 3s1 2 2 2c1 2 2 3 2 5l-1 2c-2-3-4-4-6-6-1-1-2-2-4-3-1 0-2-1-3-2 1-1 1-1 1-2l1-1v-3-5l1 1z" class="Q"></path><path d="M184 396v-3l4 9c-2-2-4-3-4-6zm1-7v-1l1 1c1 2 2 4 2 6 1 2 1 3 1 5h-1c-1-3-2-7-3-11z" class="AK"></path><path d="M188 395c1 1 2 2 2 3 1 1 2 2 2 3s1 2 2 2c1 2 2 3 2 5h-1-1l-1-1c0-1-1-2-2-3s-2-2-3-4h1c0-2 0-3-1-5z" class="w"></path><defs><linearGradient id="X" x1="175.18" y1="386.385" x2="170.231" y2="388.386" xlink:href="#B"><stop offset="0" stop-color="#575758"></stop><stop offset="1" stop-color="#787773"></stop></linearGradient></defs><path fill="url(#X)" d="M166 376h1v-2l1 1c1 1 3 2 4 3s3 1 4 2c1 2 1 6 2 8h0v1c0 2 1 3 1 5 0 1 1 1 1 2l-1 1-6-5h-1c-2-1-3-2-4-4 0-1-2-3-2-3 1-1 1-1 2-1-1-3-2-5-3-7l1-1h0z"></path><path d="M176 387c1 1 1 1 2 1v1c0 2 1 3 1 5l-4-6 1-1z" class="AT"></path><defs><linearGradient id="Y" x1="171.354" y1="387.875" x2="166.111" y2="386.456" xlink:href="#B"><stop offset="0" stop-color="#525551"></stop><stop offset="1" stop-color="#696667"></stop></linearGradient></defs><path fill="url(#Y)" d="M166 385c1-1 1-1 2-1 1 3 3 5 5 8h-1c-2-1-3-2-4-4 0-1-2-3-2-3z"></path><path d="M166 376h1v-2l1 1c1 1 3 2 4 3s3 1 4 2c1 2 1 6 2 8h0c-1 0-1 0-2-1l-1 1c-1-3-3-5-5-8v2h-1l-3-6z" class="w"></path><path d="M170 380c-1-1-2-2-2-3 1 0 2 0 2 1 2 2 5 7 6 9l-1 1c-1-3-3-5-5-8z" class="AV"></path><path d="M529 377l7-5-1 6v3c-1 3-2 5-4 6h-1 1c0 1 0 1 1 1-1 2-3 3-4 4l-2 2c-2 1-4 2-5 3h-1c-2 2-3 2-5 3l-4-4c-4-2-7-3-10-3-2 0-7 1-8 0l2-1c2-1 3-2 5-3 1-1 3-2 4-3l3-2c2-1 4-3 6-5l5-3c1 1 1 1 2 0 2 1 3 2 6 2 0 0 1-1 2-1h1z" class="N"></path><path d="M523 393c-1 0-3 0-4-1h3 6l-2 2c-1-1-2-1-3-1z" class="B"></path><path d="M523 393c1 0 2 0 3 1-2 1-4 2-5 3h-1 0v-1l-2-1h0c2 0 3 0 4-1 1 0 1 0 1-1z" class="E"></path><path d="M511 396l4 1s1-1 2-1c1 1 2 1 3 1h0c-2 2-3 2-5 3l-4-4z" class="X"></path><path d="M515 397s1-1 2-1c1 1 2 1 3 1h0-2v1c-1 0-2 0-3-1z" class="D"></path><path d="M526 385h-2l1-1h1 1l3-1c1-1 2-1 3-2h2c-1 3-2 5-4 6h-1l-1 1h1l-1 1c-2 0-7 0-9 1-1 1-3 1-5 1v-1c2 0 3-1 5-2 2 0 4-1 6-1 1-1 2-1 4-2h-1-3z" class="AF"></path><path d="M529 377l7-5-1 6v3h-2c-1 1-2 1-3 2l-3 1h-1-1l-1 1h2l-8 3c-2 0-3-1-5-1s-5 1-7 1c0 0-1 0-1-1h-1v2c-1 1-2 1-3 1s-2 1-4 2h-2c2 0 4 0 6 1-2 0-7 1-8 0l2-1c2-1 3-2 5-3 1-1 3-2 4-3l3-2c2-1 4-3 6-5l5-3c1 1 1 1 2 0 2 1 3 2 6 2 0 0 1-1 2-1h1z" class="Q"></path><path d="M518 376c1 1 1 1 2 0 2 1 3 2 6 2 0 0 1-1 2-1h1c-1 1-2 2-4 2-1 0-2-1-3 0h-2c-2 0-4 1-6 4 1-1 3-1 5-2 1 0 5-2 6-2v1l2-1h0l-2 1c-6 3-13 5-20 7h-1v2c-1 1-2 1-3 1s-2 1-4 2h-2c2 0 4 0 6 1-2 0-7 1-8 0l2-1c2-1 3-2 5-3 1-1 3-2 4-3l3-2c2-1 4-3 6-5l5-3z" class="D"></path><path d="M83 483c-1-9 2-19 6-27 3-7 8-14 14-19 2-1 5-3 7-4 3-3 5-6 8-7s5-2 7-2c3-1 6-1 9-2h5 4c2-1 6-1 8 0l3 1v2h-4-2l-8 1v1h2l-1 1h0v1h-4c0 1-1 1-2 1l-1-1h-2c-2 0-5 2-6 3l-2 1c-2 1-3 2-5 4v1c-1 0-1 0-2-1h-1l-3 1h-2s0-1-1-1h0v1c-1 1-2 1-2 2l-5 3c-2 2-4 3-5 5-14 15-17 37-6 54 4 6 8 11 13 16l-1 1c-1-1-3-3-3-4-1-1-2-3-3-4s-3-2-4-4l-3-4c0-2-2-4-3-6l-2-6s0-1-1-2v-3h0c-1-1-1-2-2-3z" class="t"></path><path d="M113 438v-1-2c-1 0-1 0-2 1v-1-1h1 0 1 4l-1 3-3 1z" class="AI"></path><path d="M140 423c3-1 8-1 11-1l3 1v2h-4-2l-8 1v1h2l-1 1h0v1h-4c0 1-1 1-2 1l-1-1h-2c-2 0-5 2-6 3l-2 1c-2 1-3 2-5 4v1c-1 0-1 0-2-1h-1l1-3h-4-1 0c6-7 13-9 21-10 2-1 5-1 7-1z" class="AQ"></path><path d="M139 426h1v1h2l-1 1h0v1h-4c0 1-1 1-2 1l-1-1v-1c-2 0-3 0-4-1h2l7-1z" class="AE"></path><path d="M130 427h2 3 0c1 1 1 1 1 2h1c0 1-1 1-2 1l-1-1v-1c-2 0-3 0-4-1z" class="AN"></path><path d="M140 423c3-1 8-1 11-1l3 1v2h-4-2l-8 1h-1v-1h-2c-1 0-2 1-3 0h1c1-1 2-1 4-1h0-6c2-1 5-1 7-1z" class="v"></path><path d="M140 423c3-1 8-1 11-1l3 1v2h-4-2l2-1c-1 0-1-1-2-1-1-1-6 0-8 0z" class="O"></path><path d="M113 434c6-3 11-5 17-7 1 1 2 1 4 1v1h-2c-2 0-5 2-6 3l-2 1c-2 1-3 2-5 4v1c-1 0-1 0-2-1h-1l1-3h-4z" class="x"></path><path d="M117 434c1-1 1-1 2-1 1 2 0 2 0 4v1c-1 0-1 0-2-1h-1l1-3z" class="t"></path><defs><linearGradient id="Z" x1="454.411" y1="394.604" x2="481.359" y2="387.854" xlink:href="#B"><stop offset="0" stop-color="#1b1b1a"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#Z)" d="M477 381c2-4 9-7 13-10 0 3-1 5 0 7-1 1-1 1-2 1l-8 10c0 1-3 4-3 6l3-3c1-2 2-3 3-4v1l2-2h0v1l-2 2-1 1v1h-1 1c1 0 1 1 2 1h0 1v-1l1 1c-6 3-12 6-17 10v1c0 1-2 2-2 2-1 1-3 1-3 2-1 0-1 0-1 1-1 0-2 0-2 1h-1c0-1 3-3 4-5-1-1-1-1-2-1s-1 1-2 2h-3 0-1c2-2 3-4 3-6 1-2 2-3 2-5v-1c1-1 1-1 1-2 1-1 0-4 1-6h-1c1-1 1-1 1-2h-1-1l6-3c2-2 5-4 8-4v1c1 1 1 2 2 3z"></path><path d="M475 378c1 1 1 2 2 3l-1 1v2h-1v1 1h0c0 1-1 2-1 3v6c1-1 2-2 3-4h0c1-1 2-2 2-3l1 1c0 1-3 4-3 6h0c-1 1-3 2-4 2-2 0-3 2-5 2-1 1-1 1-2 1v1-1l-1-1c1-3 1-5 2-7 1-7 5-9 8-14h0z" class="D"></path><path d="M477 381c2-4 9-7 13-10 0 3-1 5 0 7-1 1-1 1-2 1l-8 10-1-1c0 1-1 2-2 3h0c-1 2-2 3-3 4v-6c0-1 1-2 1-3h0v-1-1h1v-2l1-1z" class="N"></path><path d="M482 379h0c-1 2-2 4-4 6h-1c0-1 1-2 1-3 1-1 2-2 4-3z" class="AF"></path><path d="M477 391s0-1-1-1c1-4 6-7 8-11 1-1 2-3 4-5v1c0 5-6 9-9 13 0 1-1 2-2 3z" class="w"></path><defs><linearGradient id="a" x1="114.745" y1="627.478" x2="117.475" y2="596.413" xlink:href="#B"><stop offset="0" stop-color="#efd6a1"></stop><stop offset="1" stop-color="#fffcc9"></stop></linearGradient></defs><path fill="url(#a)" d="M157 602h0c2-1 4-1 5-2 0 1 0 1-1 1l-1 1c-10 6-22 9-33 11-5 1-9 1-13 2-1 0-3 0-3 1-1 0-1 1-2 1s0 0-1 1h0-4l-1 1h-1l1 1v1h1v1c1 1 2 1 3 1h1s1 0 2 1h1 0 2l6 1c1 1 1 0 1 1 2 0 4 1 6 1h0v1l-2-1-1 1c0 1 2 1 2 2-12-1-23-5-35-8l-7-3c-2-1-4-2-6-1l-9-5h15 17c8-1 16-3 23-3l21-6h4 4c2-1 3-2 5-2z"></path><path d="M102 619h-3v-1c1 0 1-1 2-1 1 1 2 1 3 1l-1 1h-1z" class="g"></path><path d="M101 617h0-1l11-1c-1 0-1 1-2 1s0 0-1 1h0-4c-1 0-2 0-3-1z" class="AD"></path><path d="M144 604h4 4 0c-1 0-2 0-3 1-1 0-1 0-3 1-1 0-2 0-3 1h-2c-2 0-4 1-6 1s-3 0-4 1c-2 1-6 1-8 1l21-6z" class="b"></path><defs><linearGradient id="b" x1="122.948" y1="370.985" x2="117.826" y2="382.466" xlink:href="#B"><stop offset="0" stop-color="#363635"></stop><stop offset="1" stop-color="#666665"></stop></linearGradient></defs><path fill="url(#b)" d="M105 359c2 1 9 6 11 6 1 0 2 3 3 4h0l4 4c2 2 5 3 7 4h0 1c1 0 4 2 5 3h1c2 1 5 2 6 4h0l1 1h-2 0v1c-2 0-7-1-9-1s-2 0-3 1h-1s-1 0-1 1h0-1v-1c-1 0-2 1-4 1s-4 1-6 2-4 0-7 0c-1-1-4-2-4-3v-2l-1-1-1-1c-2-2-2-4-3-7v-8c3 4 5 6 10 7h0c2 1 3 1 5 1 1-1 2-1 2-2-3-2-7-2-10-4-2-1-3-4-3-6v-2c-1-1 0-1 0-2z"></path><path d="M111 374c2 1 3 1 5 1l1 1v1c-1 0-2 0-3-1h0c-1 0-2 0-3-2z" class="N"></path><path d="M136 380h1c2 1 5 2 6 4h0l1 1h-2 0c-1-1-3-1-4-2-1 0-2-1-2-2h1c0-1-1-1-1-1z" class="J"></path><path d="M105 359c2 1 9 6 11 6 1 0 2 3 3 4h-1c-1-1-1-1-2-1h0-1c-1 0-1 0-2-1h-1c-1 0-3 0-4-1-1 0-2-2-3-3v-2c-1-1 0-1 0-2z" class="D"></path><path d="M105 359c2 1 9 6 11 6 1 0 2 3 3 4h-1c-1-1-1-1-2-1-3-3-7-6-10-8l-1 1c-1-1 0-1 0-2z" class="B"></path><path d="M101 375c1 0 1 1 2 1h1c0 1 0 1 1 1s1 1 2 1h1v1h1l4 1h0 2c1 1 2 1 3 2h1l3 2c-2 1-3 0-5 0s-5 0-7-1c-2 0-4-1-6-1-2-2-2-4-3-7z" class="w"></path><path d="M104 382c2 0 4 1 6 1 2 1 5 1 7 1s3 1 5 0c4-1 7 1 11 1-2 0-2 0-3 1h-1s-1 0-1 1h0-1v-1c-1 0-2 1-4 1s-4 1-6 2-4 0-7 0c-1-1-4-2-4-3v-2l-1-1-1-1z" class="Q"></path><path d="M109 384c5 1 9 2 14 2v1c-2 0-4 1-6 2h-7c2-1 5-1 6-3h-1c-2 0-4 0-6-1l1-1h-1z" class="w"></path><path d="M104 382c2 0 4 1 6 1 2 1 5 1 7 1s3 1 5 0c4-1 7 1 11 1-2 0-2 0-3 1h-1s-1 0-1 1h0-1v-1c-1 0-2 1-4 1v-1c-5 0-9-1-14-2-1-1-3-1-4-1l-1-1z" class="W"></path><path d="M123 386c1-1 3-1 5-1v1h-1c-1 0-2 1-4 1v-1z" class="Q"></path><path d="M478 569c-5-4-10-6-15-8-2-1-5-2-7-2l-4-1c7-2 14-5 21-4 5 0 12 1 16 4 2 1 3 3 4 4s1 2 1 3h0c-4-1-7-1-11-1 2 1 5 1 7 2l6 3 2 1 1 2c0 2 1 3 1 4v1c-1 1-1 1-1 2-1 0-1-1-2 0s-1 2-1 4l1 1c0 1 0 2 1 3v3l2 3c-1 0-2 0-2-1-1 0-3-3-3-5l-1 1c-4-6-7-11-12-16-1-1-3-3-4-3z" class="D"></path><path d="M492 572c2 0 3 0 4 1v1l-1 1c-1-1-2-2-3-2v-1z" class="X"></path><path d="M496 569l2 1 1 2c0 2 1 3 1 4v1c-1 1-1 1-1 2-1 0-1-1-2 0s-1 2-1 4l1 1c0 1 0 2 1 3v3c0-1-2-2-2-3s1-2 0-3v-1c-1-1 0-2-1-3v-1c1-2 1-1 3-1-1-1-1-1-1-2l1 1v-1h0v-1l-2-2c0-1-1-1-1-3h1v-1z" class="F"></path><path d="M170 555c2 0 3 0 5 1h1 1c0 1 1 1 0 2h2 0c-1 1-4 1-5 1l-1 3c-2 1-4 1-6 2h0c-1 1-2 2-2 3l-1-1h-1c-1 1-3 2-4 3-1 0-2 1-3 2-1 0-1 1-2 2 0 1-2 3-3 4l-1 1h-1v1h-1c-1 1-1 2-1 4h-1s0 1-1 1l1 1c0 1-1 3-1 5s0 5-1 7c-1 1-3 2-4 3-1-1-1-3-1-4h0v-1 3c-1 2-1 3-3 5-1 1-3 1-5 0h-1c-1 0-1 0-2 1-5 0-10-1-15-2 1-1 3-2 4-2 4-3 6-8 8-12 1-1 2-3 3-5 2 1 3-1 4-1 3-1 5-3 8-4 2-2 4-5 7-7 2-2 6-3 9-5 4-2 8-5 12-6l6-2s1 0 1-1c-2 0-3 0-4-2h-1z" class="J"></path><path d="M170 555c2 0 3 0 5 1h1 1c0 1 1 1 0 2h2 0c-1 1-4 1-5 1l-1 3c-2 1-4 1-6 2h0-4c-3 1-5 3-8 4l-1 1h-1c-1 2-3 3-5 4-1 2-3 2-4 4h-1l-7 9c0-1 2-4 3-6l1-1v-1c2-2 4-5 7-7 2-2 6-3 9-5 4-2 8-5 12-6l6-2s1 0 1-1c-2 0-3 0-4-2h-1z" class="F"></path><path d="M155 568l1-1c3-2 7-4 11-6 2 0 5-1 7-2l-1 3c-2 1-4 1-6 2h0-4c-3 1-5 3-8 4z" class="m"></path><path d="M128 583c2 1 3-1 4-1 3-1 5-3 8-4v1l-11 14c-2 3-6 6-10 7h0c-1 1-1 1-2 1v-1c4-3 6-8 8-12 1-1 2-3 3-5z" class="B"></path><defs><linearGradient id="c" x1="117.302" y1="549.021" x2="108.217" y2="591.075" xlink:href="#B"><stop offset="0" stop-color="#3c3c3b"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#c)" d="M121 555c1-1 4-2 5-4h0c1-1 1-2 1-2v-1h1c1 1 1 2 1 3l1 7c1 2 1 6 1 8-1 4-4 8-6 11s-4 6-7 8-8 4-13 4c-4 1-8 0-12 0v-3h-1c-1 0-2 0-2-1h0c-1-1-3-2-3-4h-1-1c-1-1-2-3-3-4 1-1 0-1 1-1s1-1 1-2l1-1c1 1 3 2 4 3v-1c3 1 5 1 7 2s5 0 7 0h2c3-1 5-2 8-4 2-2 3-4 4-7v-5h0c-1-1-1-2-1-4 1 0 2-1 2-1l1-1h2z"></path><path d="M123 557h0l1-1c1 0 2-1 3-2h0 0v3h-4z" class="L"></path><path d="M118 556h1v2 1h-1-1v1 1h0c-1-1-1-2-1-4 1 0 2-1 2-1z" class="E"></path><path d="M128 565c1 2 0 2 0 4l-1 1c-1 0-1 0-2 1v-1h0 0 1l1-1c-2 0-3 0-5 1h-1c2-1 4-1 5-2 1 0 2-2 2-3z" class="m"></path><path d="M121 561c2-1 5-2 6-2l1 4c-2 0-1 0-2-1-2 0-3 1-5 0v-1z" class="Y"></path><path d="M121 555c1-1 4-2 5-4h0c1-1 1-2 1-2v-1h1c1 1 1 2 1 3-1 0-1 1-2 2v1h0 0c-1 1-2 2-3 2l-1 1h0c-1 1-2 1-4 2v-1-2h-1l1-1h2z" class="M"></path><path d="M121 555s1 0 2 1c-1 1-1 1-2 1l-2-2h2zm-4 5l1 1h3v1c2 1 3 0 5 0 1 1 0 1 2 1v2c0 1-1 3-2 3-1 1-3 1-5 2-1 0-2 1-3 2 1 0 1 1 2 1 2-1 3-1 5-1h0l-3 4-4 5c-1 1-3 2-4 3-1-1-2-1-3 0h-2c0-2 0-4-1-6h-2s1 0 1-1h1-3c3-1 5-2 8-4 2-2 3-4 4-7v-5-1z" class="B"></path><path d="M120 573c2-1 3-1 5-1h0l-3 4h0c0-1 0-1-1-2h-2s0 1-1 1-1 1-2 0l-1-1c1 0 1 0 2-1h3z" class="M"></path><path d="M117 560l1 1c1 2 1 5 1 7-2 4-5 6-7 8-1 1-3 1-3 2h-1-2s1 0 1-1h1-3c3-1 5-2 8-4 2-2 3-4 4-7v-5-1z" class="Y"></path><path d="M112 576h2 4c0 1 1 1 2 1h0c0 1-2 2-3 3l1 1c-1 1-3 2-4 3-1-1-2-1-3 0h-2c0-2 0-4-1-6h1c0-1 2-1 3-2z" class="W"></path><path d="M108 578h1c1 1 2 2 4 2h4l1 1c-1 1-3 2-4 3-1-1-2-1-3 0h-2c0-2 0-4-1-6z" class="S"></path><path d="M84 574l1-1c1 1 3 2 4 3v-1c3 1 5 1 7 2s5 0 7 0h2 3-1c0 1-1 1-1 1h2c1 2 1 4 1 6h2c1-1 2-1 3 0-4 3-8 3-13 3-2 0-5-1-8-1h-1c-1 0-2 0-2-1h0c-1-1-3-2-3-4h-1-1c-1-1-2-3-3-4 1-1 0-1 1-1s1-1 1-2z" class="D"></path><path d="M86 581c2-1 3-2 4-3h0c0 1 0 2-1 3 0 1 0 1-1 1l-1-1h-1z" class="Y"></path><path d="M89 575c3 1 5 1 7 2s5 0 7 0h2 3-1c0 1-1 1-1 1-2 1-3 1-4 2-2-1-3-1-5-1l-8-3v-1z" class="S"></path><path d="M92 586c1-1 6-5 7-6 1 1 1 1 1 2 2 1 4 3 5 4 2 0 3 0 4-1v-1h2c1-1 2-1 3 0-4 3-8 3-13 3-2 0-5-1-8-1h-1z" class="B"></path><defs><linearGradient id="d" x1="157.308" y1="419.34" x2="162.791" y2="405.71" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#595857"></stop></linearGradient></defs><path fill="url(#d)" d="M174 405h1l-13-9 1-1c8 5 16 10 23 16v1s-1 0-1-1l-1 1 7 5-2 1 1 1h-5c3 1 6 4 9 5h-1l-4-1c-3-1-8-1-11 0l-2 2v-1h-2-1l-1-1c-1 1-2 1-3 1-1-1-3-1-4-2-2-1 0-1-1-2h-3v1c-3 0-9-3-12-4-2-1-4-3-4-5 1 0 1 0 2-1h0l1-3c1 0 2-1 3-1l-1-1v1c-1-1-2-1-3-1l-1-1h1v-1h-5l1-1c2-2 5-5 8-6h0 2c2 0 4 0 6 1 4 2 7 4 11 4l4 3z"></path><path d="M165 417l2-1-1 1h0v1h1l-3 2h-3 0c1-1 2-1 2-2h-1v-1h3 0z" class="B"></path><path d="M167 408h3c1-1 3-1 4-1l3 2h0l6 5h-6l-3 1c-1 1-2 1-3 1l-1-1c-1 0-2 1-3 1l-2 1v-2h0-3v-1c4-2 7-1 11-2h3v-1c-1-1-3-1-4 0h-1c-3-1-4 0-6 0-2 1-5 1-7 1l4-2c2-1 4-1 5-2z" class="Q"></path><path d="M165 415h2c1 0 5-1 5-1l2 1c-1 1-2 1-3 1l-1-1c-1 0-2 1-3 1l-2 1v-2z" class="W"></path><path d="M174 405h1l-13-9 1-1c8 5 16 10 23 16v1s-1 0-1-1l-1 1c-2-1-4-2-5-3h-2l-3-2c-1 0-3 0-4 1h-3c-1 1-3 1-5 2h-3 0-4c-3 1-5 1-8 1h0l1-3c1 0 2-1 3-1l-1-1c1 0 4 0 5-1 1 0 1 1 2 1h1c1 0 1 0 2 1 1-1 2-1 3-1 4-1 6-2 9-1 1 1 2 1 2 1v-1z" class="D"></path><path d="M150 406c1 0 4 0 5-1 1 0 1 1 2 1h1c1 0 1 0 2 1l-2 1h0c3 0 5-1 7-1 1-1 2-1 3-1l1 1h-2v1c-1 1-3 1-5 2h-3 0-4c-3 1-5 1-8 1h0l1-3c1 0 2-1 3-1l-1-1z" class="W"></path><path d="M151 397h0 2c2 0 4 0 6 1 4 2 7 4 11 4l4 3v1s-1 0-2-1c-3-1-5 0-9 1-1 0-2 0-3 1-1-1-1-1-2-1h-1c-1 0-1-1-2-1-1 1-4 1-5 1v1c-1-1-2-1-3-1l-1-1h1v-1h-5l1-1c2-2 5-5 8-6z" class="AF"></path><path d="M147 403h4c1 1 1 1 2 1l-6 1v-1h-5l1-1 1 1 3-1z" class="E"></path><path d="M151 403c5-1 7-1 12 0v1c-2 1-4 0-6 0-1-1-3 0-4 0s-1 0-2-1z" class="X"></path><path d="M151 397h0 2c2 0 4 0 6 1-1 1-2 1-4 1s-3 0-4 1c-1 0-3 1-3 2l-1 1-3 1-1-1c2-2 5-5 8-6z" class="N"></path><path d="M177 409h2c1 1 3 2 5 3l7 5-2 1 1 1h-5c3 1 6 4 9 5h-1l-4-1c-3-1-8-1-11 0l-2 2v-1h-2-1l-1-1c-1 1-2 1-3 1-1-1-3-1-4-2-2-1 0-1-1-2l3-2h-1v-1h0l1-1c1 0 2-1 3-1l1 1c1 0 2 0 3-1l3-1h6l-6-5h0z" class="B"></path><path d="M179 420h5c-2 1-5 2-7 2-2 1-3 1-5 1v-1c2 0 4-1 7-2z" class="E"></path><path d="M177 414v1l-4 1c-2 1-4 2-6 4h0 2v1h-1v1h1v2c-1-1-3-1-4-2-2-1 0-1-1-2l3-2h-1v-1h0l1-1c1 0 2-1 3-1l1 1c1 0 2 0 3-1l3-1z" class="J"></path><path d="M167 416c1 0 2-1 3-1l1 1-4 2h-1v-1h0l1-1z" class="X"></path><path d="M177 409h2c1 1 3 2 5 3l7 5-2 1 1 1h-5-8 0l-2 1h-2c0 1-1 1-2 1h0l1-1c1-1 2-2 3-2l1-1h0c-1 0-2 0-2-1h-1l4-1v-1h6l-6-5h0z" class="N"></path><path d="M177 419v-1c-1 1-2 1-3 1h0 1l2-1c1-1 1-1 2-3h0c1 0 3 1 4 1h3c1 0 2 1 3 2l1 1h-5-8z" class="Q"></path><path d="M378 557c3 0 5 0 8 1 1 2 4 2 5 4 1 0 2 1 3 2 2 2 4 4 5 7l1 1c2 4 2 8 1 12v1c-2 5-5 8-9 11l-3 1-3 1c-2-1-5-1-7-3h0-1c-2-1-3-2-4-4v-1c-1-1-2-1-2-2s0-4-1-5l1-2v-3l1-1c1-2 2-4 3-5l1-1c2-2 7-4 10-4h1c0-1-1-2-2-3l1-1h-1 3l-2-2h0s-1-1-2-1c-4-1-8 0-12 0h-4v1-1l9-3z" class="Y"></path><path d="M387 561c3 1 6 4 8 6h-2c-1 0-2-1-4-1l1 1v1c-1 0-1-1-2-1 0-1-1-2-2-3l1-1h-1 3l-2-2h0z" class="w"></path><path d="M387 563c1 0 2 0 3 1v1h-2l1 1 1 1v1c-1 0-1-1-2-1 0-1-1-2-2-3l1-1z" class="AT"></path><path d="M398 570c1 1 1 2 1 3 1 1 1 1 1 2l-2 2h-1l1 2v1c-2 1-1-1-2-1-1-1-1-1-2-1v-3h0 0 1l1-1c1-1 1-1 1-2 0 0 1-1 1-2z" class="B"></path><path d="M399 573c1 1 1 1 1 2l-2 2h-2v-1c1-1 1-1 2-1 1-1 1-2 1-2z" class="N"></path><path d="M394 578c1 0 1 0 2 1 1 0 0 2 2 1v-1c1 1 0 3-1 5 0 1-2 3-3 4h-2c-1-1 1-2 1-3 1-2 1-4 1-7z" class="C"></path><path d="M389 566c2 0 3 1 4 1h2c2 1 2 2 3 3 0 1-1 2-1 2 0 1 0 1-1 2l-1 1h-1l1-2-3-3-2-2v-1l-1-1z" class="D"></path><path d="M395 567c2 1 2 2 3 3 0 1-1 2-1 2h-1c-1-1-2-2-2-4 0 0-1 0-1-1h2z" class="m"></path><path d="M374 590h0c2-1 3-2 5-3l1 1c0 1 0 2 2 3 1 0 4 0 5-1h2 1l-3 2c-2 1-5 1-8 1-1-1-2-2-3-2h-2v-1z" class="B"></path><path d="M392 570l3 3-1 2h0 0v3c0 3 0 5-1 7 0 1-2 2-1 3h2c0 1-1 1-2 2h-2-1-2 0 1l-1-2-1-1c1-2 2-2 3-4v1c1 0 2-1 3-1h0 0l1-1v-6-1c0-1-1-3-2-4l1-1z" class="L"></path><path d="M393 582c0 2-1 3-2 5h-1l-2-1c0 1-1 1-1 2h0l-1-1c1-2 2-2 3-4v1c1 0 2-1 3-1h0 0l1-1z" class="M"></path><path d="M387 567h1c1 0 1 1 2 1l2 2-1 1c1 1 2 3 2 4v1 6l-1 1h0 0c-1 0-2 1-3 1v-1c-1 2-2 2-3 4l1 1 1 2h-1 0c-1 1-4 1-5 1-2-1-2-2-2-3l-1-1c-2 1-3 2-5 3h0c-1-1-2-1-2-2s0-4-1-5l1-2v-3l1-1c1-2 2-4 3-5l1-1c2-2 7-4 10-4z" class="AR"></path><path d="M374 584v-1h3l1 1c-2 1-2 1-3 0h-1z" class="B"></path><path d="M374 584c-1-1-1-2-1-3v-4h1c0-1 1-2 2-3h0l1 1c-1 1-2 1-2 2-1 1-1 2-1 3v2h0l2-1 1 1v1h-3v1z" class="S"></path><path d="M377 582l-1-1-2 1h0v-2c0-1 0-2 1-3 0-1 1-1 2-2l1 1c0 2 0 2-1 3h-1v1c1 1 1 1 1 2zm13-9c-1 1-2 2-4 3h-2c0-1-1-3-1-4-1 0 0-1 0-1 1-1 2-2 3-2s3 0 4 1c-1 1-1 1 0 3z" class="C"></path><path d="M390 570l1 1c1 1 2 3 2 4v1 6l-1 1h0 0c-1 0-2 1-3 1v-1c1 0 1-1 2-2h-1c-1 1-2 1-3 0v-1l1-1c1-2 2-4 2-6-1-2-1-2 0-3z" class="X"></path><path d="M525 348v3 1 2h1c-1 2-1 5-2 7-1 1-1 3-1 4v1c-1 0-2 1-3 2l-4 6c0 2-2 4-3 5-2 2-4 4-6 5l-3 2c-1 1-3 2-4 3-2 1-3 2-5 3l-2 1c-1 0-2 1-4 2h0l-2 1-5 3-4 2c-3 1-5 3-7 4-1 1-3 2-4 2v-1s2-1 2-2v-1c5-4 11-7 17-10l-1-1v1h-1 0c-1 0-1-1-2-1h-1 1v-1l1-1 2-2v-1h0l-2 2v-1c-1 1-2 2-3 4l-3 3c0-2 3-5 3-6l8-10c1 0 1 0 2-1-1-2 0-4 0-7h0c1-1 3-2 4-2 2-2 3-4 5-4 1-1 1-1 2 0h1c0 2-1 5 0 7v-1l2-1c1-1 1-3 1-5 1-1 3-4 5-5l1 1h0c1-2 2-4 4-6l-1 4h1l1-2v-2h1v2c1-1 1-2 1-2l1-1c1 1 0 2 1 3 2-2 2-4 4-6l1-3z" class="w"></path><path d="M494 388c1-1 2-1 3-2s2-2 3-2c1-1 2-1 2-2v-1s-1 1-2 1h0c0-1 1-1 2-2 1 0 3-2 4-3v1c-1 1-3 5-4 5-3 2-5 4-8 6v-1z" class="Q"></path><path d="M516 374c0 2-2 4-3 5-2 2-4 4-6 5l1-2h0l8-8z" class="F"></path><path d="M520 368l3-8c1-3 1-6 2-8v2h1c-1 2-1 5-2 7-1 1-1 3-1 4v1c-1 0-2 1-3 2z" class="D"></path><path d="M498 388h0l10-6h0l-1 2-3 2c-1 1-3 2-4 3-2 1-3 2-5 3l1-2c1 0 2-1 3-2h-1z" class="J"></path><path d="M492 388c3-2 5-4 7-6h0c-2 2-3 4-6 6h1v1c-3 0-6 2-8 4l-1-1v1h-1 0c-1 0-1-1-2-1h-1 1v-1l1-1v1c2-1 3-2 5-3 1-1 1-1 3-1h0v1c0 1 0 0 1 0zm24-33h1v2c1-1 1-2 1-2l1-1c1 1 0 2 1 3 0 1 0 2-1 3-1 2-1 4-2 6-2 5-5 9-9 13h-1c1-1 4-4 5-6 3-3 5-8 5-13 0-1 0-2-1-3h0v-2z" class="N"></path><path d="M469 404c9-6 19-12 29-16h1c-1 1-2 2-3 2l-1 2-2 1c-1 0-2 1-4 2h0l-2 1-5 3-4 2c-3 1-5 3-7 4-1 1-3 2-4 2v-1s2-1 2-2z" class="S"></path><path d="M490 371c1-1 3-2 4-2 2-2 3-4 5-4 1-1 1-1 2 0h1c0 2-1 5 0 7-1 1-1 2-2 3v2c-1-2 0-4 1-6v-2h0v-1-1c-2 3-2 7-4 10-1 1-1 2-1 3l-4 8h0c-1 0-1 1-1 0v-1h0c-2 0-2 0-3 1-2 1-3 2-5 3v-1l2-2v-1h0l-2 2v-1c-1 1-2 2-3 4l-3 3c0-2 3-5 3-6l8-10c1 0 1 0 2-1-1-2 0-4 0-7h0z" class="AF"></path><path d="M485 388c2-3 4-5 5-8v1h1c0 1 0 1-1 2-1 2-1 3-2 5-2 1-3 2-5 3v-1l2-2z" class="AK"></path><path d="M490 371c1-1 3-2 4-2 2-2 3-4 5-4 1-1 1-1 2 0-2 0-4 2-6 4l-3 9h0c-1-1 0-4 0-5v-2h-2z" class="m"></path><path d="M467 482c-1-1 0-2 0-3h-1l2-1c0 1 1 2 1 3v1c1 1 1 1 1 2v2c0 1 1 3 0 4v3l1-1h0l-1-1c0-1 1 0 1-2 0-1 0-2-1-3 0-1 1-3 0-4v-1h0 1c0 2 0 4 1 6 0 1-1 2 0 3h2l3-2v-2h1v1c0 1 0 1-1 2 1 0 1 1 0 1v-1c-2 0-2 2-4 2h0c1 0 1 0 1 1v2s-1 1-1 2v-3-1h0-1c0 2-1 2 0 4h0c1 2 2 3 2 4h1v-1c0-1 0-3-1-4l1-1 1-1v-1h1v1h-1c0 1 0 2-1 2 1 1 1 1 1 3v1c0 1 0 0-1 1 1 0 1 1 1 1h0c0-1 0-2 1-3h2 0c1-1 1-2 2-3 0-1 0-2 1-3v-1-3-1l1-1v2-1h1c1 1 0 3 0 4h0 2v1 1c-2 1-1 4-2 6l-1 1v1c1 1 1 1 0 2l1 1c0 1-1 4-2 4h0-1 0c-1 1-1 1-1 2s-1 2-1 2l-1 1c0 1 0 1-1 1l-3 6v1c-1 4-5 6-7 10-1 1-2 2-3 2-3 1-8 3-11 3h-5-5-4c-2 1-1-1-2-1 0 0-1-1-2-1l-1-1-1-1-2-1h0 1c2 1 5 2 8 2 1 0 3-1 4-2h3 0 0l-2-1c2 0 4-1 5-2 2 0 5-2 6-4l3-4 2-2h-2v-1-1h0c1-1 2-2 2-4l2-2-2-1c-1 2-2 3-3 5l-1-1 2-2s1-1 1-2c2-3 5-6 6-10v-2l1-4h0v-11z" class="M"></path><path d="M478 506c0-1 0-1-1-2 0-1 0-1 1-1h1v2l-1 1z" class="E"></path><path d="M463 510l1-2v1h0l1 2-1 3c-1-1-1-1-3-2l2-2z" class="y"></path><path d="M466 505h2c0 2 0 4-2 5l-1 1-1-2h0c2-1 2-1 3-2 0 0-1 0-2-1l1-1z" class="AP"></path><path d="M465 503l1 1v1l-1 1c1 1 2 1 2 1-1 1-1 1-3 2v-1l-1 2-2-1c1 0 1 0 1-1 2-1 2-3 3-5z" class="F"></path><path d="M461 512c2 1 2 1 3 2l-2 3-1 1h-2v-1-1h0c1-1 2-2 2-4z" class="AM"></path><path d="M459 517l1-1 2 1-1 1h-2v-1z" class="u"></path><path d="M479 505h1c-1 3-2 6-4 8-2 3-4 5-6 7v-2c2-1 4-4 5-6h0c-1 0-2 1-3 1v-1c0-1 0-1 1-2h3c1 0 2-3 2-4l1-1z" class="m"></path><path d="M483 488v-1h1c1 1 0 3 0 4h0l-1 5v1c-1 1-1 1-1 2l-1 1c0 1 0 1-1 2 0-1-1-1-1-2s0-1 1-2c1 0 1-1 1-2 1-3 1-5 2-8z" class="z"></path><path d="M484 491h2v1 1c-2 1-1 4-2 6l-1 1v1s-1 0-2 1c0 1 0 2-1 3h-1v-2l1-1c1-1 1-1 1-2l1-1c0-1 0-1 1-2v-1l1-5z" class="AM"></path><path d="M467 482h1c1 1 1 2 1 3 1 2 1 5 0 7 0 1 0 3-1 4-1-1-1-1-1-3v-11z" class="q"></path><path d="M469 492l1 1h1 0l-1 3c2 1 3 2 4 4l-2 2h-2l-1-1c-1 0-1-1-2-1l1-2v-2c1-1 1-3 1-4z" class="B"></path><path d="M468 498h0c1 0 1 0 2-1 0 1 1 1 1 2v2s0 1-1 1l-1-1c-1 0-1-1-2-1l1-2z" class="AM"></path><path d="M450 532l2-2c7-2 13-7 18-12v2c-1 1-2 1-3 3h0c-2 2-4 3-6 5 0 0-2 0-2 1-3 2-5 3-9 3z" class="N"></path><path d="M467 493c0 2 0 2 1 3v2l-1 2c1 0 1 1 2 1l-1 1v3h-2v-1l-1-1c-1 2-1 4-3 5 0 1 0 1-1 1-1 2-2 3-3 5l-1-1 2-2s1-1 1-2c2-3 5-6 6-10v-2l1-4h0z" class="AO"></path><path d="M465 503c1-1 1-2 2-3 1 0 1 1 2 1l-1 1v3h-2v-1l-1-1z" class="E"></path><path d="M483 501c1 1 1 1 0 2l1 1c0 1-1 4-2 4h0-1 0c-1 1-1 1-1 2s-1 2-1 2l-1 1c0 1 0 1-1 1l-3 6-1 2c-1-1-2-1-2-1v-1l-4 3h0c1-2 2-2 3-3 2-2 4-4 6-7 2-2 3-5 4-8 1-1 1-2 1-3 1-1 2-1 2-1z" class="B"></path><path d="M471 520c2-2 4-5 6-6h0l-3 6-1 2c-1-1-2-1-2-1v-1z" class="x"></path><path d="M473 522l1-2v1c-1 4-5 6-7 10-1 1-2 2-3 2-3 1-8 3-11 3h-5-5-4c-2 1-1-1-2-1 0 0-1-1-2-1l-1-1-1-1-2-1h0 1c2 1 5 2 8 2h5l5-1c4 0 6-1 9-3 0-1 2-1 2-1 2-2 4-3 6-5l4-3v1s1 0 2 1z" class="N"></path><path d="M473 522l1-2v1c-1 4-5 6-7 10-1 1-2 2-3 2l-1-1c2-2 4-2 5-4 1-1 4-3 4-4h-1l2-2z" class="D"></path><path d="M467 523l4-3v1s1 0 2 1l-2 2c-2 2-4 3-6 4l-3 2c-2 1-3 1-4 1l1-2h0c0-1 2-1 2-1 2-2 4-3 6-5z" class="q"></path><path d="M461 528h0c1 0 2-1 3-1l1 1-3 2c-2 1-3 1-4 1l1-2h0c0-1 2-1 2-1z" class="m"></path><path d="M459 529h0l-1 2c1 0 2 0 4-1-2 1-4 3-6 4h-3c-2 0-4 1-5 2h-5-4c-2 1-1-1-2-1 0 0-1-1-2-1l-1-1-1-1-2-1h0 1c2 1 5 2 8 2h5l5-1c4 0 6-1 9-3z" class="AF"></path><path d="M459 529h0l-1 2c-2 1-5 3-7 3-1-1-1 0-2 0h-2l-2-1 5-1c4 0 6-1 9-3z" class="AT"></path><path d="M114 343c1 2 4 5 5 7v1h1 1l3 3c0 2 1 3 0 5 1 2 1 4 2 6h1 1v-5c-1-2-1-3-1-4l1-1c0 1 1 1 1 1 2 0 2 0 3 1h0l1-1c2 1 5 2 7 4l1 2 2 2c1 1 2 1 3 1h2l5 4v1h1l1-1c1 1 2 2 3 4 1-1 1-1 2-1 0 1 1 1 1 2l1 1v-1-1c1 0 3 1 4 2v1l-1 1c1 2 2 4 3 7-1 0-1 0-2 1 0 0 2 2 2 3l-1 1 1 2h0-1 0-1c-1-1-2-1-4-2-1 0-2 0-3-1l-1-1v1c-6-2-12-6-18-8h0c1 0 1 0 2 2-2 0-3-2-5-2h-1c-1-1-4-3-5-3-8-5-15-13-17-22-1-3-1-7-1-10h0c0-1 0-1 1-2z" class="AF"></path><path d="M157 375c1 1 2 3 3 6h-1c-1-2-2-4-2-6z" class="AT"></path><path d="M150 369h3v1h-1c-1 2 1 3 0 6-1-3-2-4-2-7z" class="Q"></path><path d="M162 375s-1 0 0 0c0 3 0 5 2 7v1l-1 1c0-1-1-1-1-2-1-1-2-7-2-8h1l1 1z" class="AK"></path><path d="M117 352c0-1 0-1 1-2h1v1h1 1l3 3c0 2 1 3 0 5v-3h0c-1 1-1 2-2 3 0-1 1-1 1-2v-2h0c0-1-1-2-2-3v4l-1 1c0-2 0-4-1-5h-2z" class="E"></path><path d="M117 352h2c1 1 1 3 1 5 1 0 1 1 2 2v2c0-1-1-1-1-1 0-1-1-1-2-2 1 2 2 4 3 5v1 1l-2-2c-1-2-2-2-2-4 1-1 0-2 0-3-1-1-1-3-1-4z" class="N"></path><path d="M162 389h0c-2-1-3-2-4-4h-2l-1-1h2c1 0 2 1 2 2 1 1 2 1 4 2h0c1 1 1 1 2 1h0 1c0-1-1-2-1-3h-1v-3l2 2s2 2 2 3l-1 1 1 2h0-1 0-1c-1-1-2-1-4-2zm-35-33l1-1c0 1 1 1 1 1 2 0 2 0 3 1h0c2 2 3 7 4 9h0c-1 0-1-2-2-3l-3-6v6c0 2 1 4 2 5-1 0-2-3-3-4v-2c-1-2-1-3-1-4v-1l-1-1v4c-1-2-1-3-1-4z" class="m"></path><path d="M114 343c1 2 4 5 5 7h-1c-1 1-1 1-1 2s0 3 1 4c0 1 1 2 0 3-1-1-1-3-2-5 0-1-1-2-1-2h-1v3c-1-3-1-7-1-10h0c0-1 0-1 1-2z" class="F"></path><path d="M113 345h1c2 3 2 6 2 9 0-1-1-2-1-2h-1v3c-1-3-1-7-1-10h0z" class="E"></path><path d="M147 374v1c-1-1-1-2-2-3l-3-8c0 2 1 5 1 6 1 2 1 2 1 4 0 1 0 1 1 2h0l-1 1c-1-2-1-5-2-7-2-2-3-6-4-9h1l2 1 2 2c2 3 4 7 4 10z" class="AT"></path><path d="M143 364c1 1 2 1 3 1h2l5 4h-3c0 3 1 4 2 7h1c0 1 1 2 2 3h0-1 0v1 1c-1-2-2-3-4-4h0c0 1 1 2 1 3h0-1c-2-2-1-4-2-5l-1-1c0-3-2-7-4-10z" class="AK"></path><path d="M143 364c1 1 2 1 3 1h2l5 4h-3l-4-4c1 4 2 8 4 12 0 1 1 2 1 3h0-1c-2-2-1-4-2-5l-1-1c0-3-2-7-4-10z" class="N"></path><path d="M257 596c0 1-1 2-2 3-3 3-7 5-10 8h1l15-12h0 1c-2 1-2 2-4 3l-4 4h-1c-1 0-1 1-1 1-2 1-3 2-5 3l-1 2c-1 0-2 1-3 2l-9 6c-36 19-79 24-119 16-7-2-14-4-21-7-6-2-12-4-17-7 2-1 4 0 6 1l7 3c12 3 23 7 35 8 0-1-2-1-2-2l1-1 2 1v-1h0 1 0 3c3 1 6 1 9 1h23 7c2-1 4-1 6-1 1 0 2 0 3-1h3c3-1 7-1 10-2 2-1 4-1 6-2l19-7v1c2 0 4-1 6-2l6-2c1 1 4-1 5-1 3-2 5-3 7-4 6-3 12-8 17-11z" class="R"></path><path d="M228 612c1 1 4-1 5-1-1 1-3 2-5 3l-8 4c-29 12-62 16-93 12h-2c0-1-2-1-2-2l1-1 2 1v-1h0 1 0 3c3 1 6 1 9 1h23 7c2-1 4-1 6-1 1 0 2 0 3-1h3c3-1 7-1 10-2 2-1 4-1 6-2l19-7v1c2 0 4-1 6-2l6-2z" class="O"></path><path d="M327 131v-2h5 9c22 0 42-1 63 5 5 1 9 3 14 5l5 2c1 0 2 1 3 1 10 5 19 11 27 18 0 1 1 2 1 3 2 2 6 4 7 7h-1c0 2 1 4 2 5-1 1-1 1-1 2l-10-9c-4-4-9-7-13-10-8-5-16-10-24-13-27-10-58-10-87-9-1-2 0-3 0-5zm-169-2c16-2 32-1 46 6v1h1c1 2 5 3 7 6 0 0 0 2 1 2l3 6c1 2 2 6 3 8a30.44 30.44 0 0 0-8-8c-8-8-19-11-31-13-13-2-27 1-40 6-8 2-15 6-21 10-13 10-22 22-31 36l-6 12c0-3 0-5 1-7h0 0v1c2-3 3-6 4-9 10-20 24-37 44-48l15-6 12-3z" class="q"></path><path d="M397 464h1c2-1 4-3 6-5 4-3 9-6 13-9 2-2 6-3 7-5 1 3 4 5 7 6s6 0 9-1h-1l-2 2h0-5v1c2 0 5 1 6 0l4-1c0 3 1 6 0 10v-3l-1-1-3-3v-1c-1 0-1 0-1 1l-1-1c-2 0-3 0-4-1-1 0-1 0-2-1-1 0-1 0-2-1s-1-1-2-1c0 0 0 1-1 2v2 1h0v2c1 1 1 1 1 2l-1 1v4h-1v3l1-1v-1 3l-1 1h0l-1 1 1 1v2c-2 1-4 2-7 3s-6 4-10 5c1-3 4-3 5-5l-12 6-1-1h-1s-2 2-3 2c-2 0-3 1-4 2h-1c0-1 0-2 1-3l-4 5h-2c-1 2-3 5-4 7l-6 8c-2 3-3 6-7 9-2 4-3 8-5 12 0 1-1 4-2 4v-1 2c-1 0-1 0-1 1v1c-1-1 0-3 0-4v-1c1-4 3-7 4-11 1-2 2-4 3-7 4-9 10-17 16-25 4-6 9-12 14-18z" class="L"></path><path d="M402 467c1-1 2-3 3-4 3-2 5-3 8-4 1 1 2 1 2 2v1h1c0 1-2 2-3 3v1l-1 1c0-1 0-1 1-2-1 0-2-1-3-1h0c-1 2-2 4-4 5-1 0-1 0-1-1-1 0-1-1-2-1h-1z" class="x"></path><path d="M410 464c1-1 2-3 4-4l1 1v1h1c0 1-2 2-3 3v1l-1 1c0-1 0-1 1-2-1 0-2-1-3-1h0z" class="l"></path><path d="M425 454v1h0v2c1 1 1 1 1 2l-1 1v4h-1c-1 0-4 2-5 3-2 1-4 1-6 1l-1-1h0l1-1v-1c1-1 3-2 3-3h-1v-1c0-1-1-1-2-2 3-2 5-3 7-3l2-1h3v-1z" class="AR"></path><path d="M425 454v1h0v2c1 1 1 1 1 2l-1 1h-2-2c-1 0-2 1-3 1l1-1c0-1 1-2 2-3l-1-1 2-1h3v-1z" class="B"></path><path d="M423 460h-2v-2c1 0 2-1 3-1 0 1 0 2-1 3z" class="t"></path><path d="M425 454v1h0v2c1 1 1 1 1 2l-1 1h-2c1-1 1-2 1-3h0c0-1 0-1-1-2h-1 3v-1z" class="l"></path><path d="M413 459c3-2 5-3 7-3l1 1c-1 1-2 2-2 3l-1 1c-1 2-3 4-5 5v-1c1-1 3-2 3-3h-1v-1c0-1-1-1-2-2z" class="q"></path><path d="M368 511c1-3 3-6 5-8l11-18h0c-2 3-3 5-4 7 2-2 4-4 5-7l3-3 1-1c1-1 3-3 3-5 3-3 6-7 10-9h1c1 0 1 1 2 1 0 1 0 1 1 1l-1 1c-1 1-2 2-3 2l-2 2h2 0l1 2h0c-1 2-3 3-5 5 0 0-2 2-3 2-2 0-3 1-4 2h-1c0-1 0-2 1-3l-4 5h-2c-1 2-3 5-4 7l-6 8c-2 3-3 6-7 9z" class="D"></path><path d="M403 467c1 0 1 1 2 1 0 1 0 1 1 1l-1 1-1-1-1 1h-1l-1-1 2-2z" class="B"></path><path d="M385 487c3-5 8-10 12-13 1-1 2-2 4-2h1l-2 2h2v1l-1 1c-1 0-1 0-1 1h-1l-2 1v-1-1l-1 1-1 1-4 4-4 5h-2z" class="w"></path><path d="M402 474h0l1 2h0c-1 2-3 3-5 5 0 0-2 2-3 2-2 0-3 1-4 2h-1c0-1 0-2 1-3l4-4 1-1 1-1v1 1l2-1h1c0-1 0-1 1-1l1-1v-1z" class="L"></path><path d="M412 467l1 1c2 0 4 0 6-1 1-1 4-3 5-3v3l1-1v-1 3l-1 1h0l-1 1 1 1v2c-2 1-4 2-7 3s-6 4-10 5c1-3 4-3 5-5l-12 6-1-1h-1c2-2 4-3 5-5h0l-1-2h0-2l2-2c1 0 2-1 3-2l1-1c2-1 3-3 4-5h0c1 0 2 1 3 1-1 1-1 1-1 2h0z" class="N"></path><path d="M411 470v-1h2c1 1 1 1 0 2-1 2-3 3-5 3l3-3v-1z" class="Q"></path><path d="M411 470l1-1 1 1v1h-2 0v-1z" class="w"></path><path d="M410 464h0c1 0 2 1 3 1-1 1-1 1-1 2h0-1c0 1 0 1-1 1 0 1-2 2-3 3l-4 5h0l-1-2h0-2l2-2c1 0 2-1 3-2l1-1c2-1 3-3 4-5z" class="S"></path><path d="M399 481c4-2 8-4 12-5l12-6 1 1v2c-2 1-4 2-7 3s-6 4-10 5c1-3 4-3 5-5l-12 6-1-1z" class="F"></path><path d="M412 476h0c1-1 3-1 4-2 2 0 3-1 5-2l-4 4c-3 1-6 4-10 5 1-3 4-3 5-5z" class="E"></path><path d="M426 142c2-1 1 0 3 0h0 1 0c8 3 15 7 21 12l6 6c1 0 2 2 3 2h1c6 5 12 12 17 19h0c2 3 4 6 5 8 7 11 13 24 17 36 1 3 2 5 3 8 2 2 4 4 6 5l1 1v4 33c-2-2-2-5-3-8-1-1-2-3-2-5-1-2-1-4-3-6-1-1-2-5-2-8l-3-8c-1-2 0-3 0-5l-1-1c-1-3-2-9-4-11 0-2-3-6-4-8v-1l-1 3-7-15-6-9-13-17c0-1 0-1 1-2-1-1-2-3-2-5h1c-1-3-5-5-7-7 0-1-1-2-1-3-8-7-17-13-27-18z" class="p"></path><path d="M500 225l3 8h-1v1c-2-3-3-5-3-8l1-1z" class="H"></path><path d="M497 236c1 1 1 2 1 3 1 1 2 5 1 6v1c1 1 1 2 1 3l-3-8c-1-2 0-3 0-5z" class="O"></path><path d="M502 234v-1h1c2 2 4 4 6 5l1 1h-1 0l-1 1h-2c-2-2-3-3-4-6z" class="o"></path><path d="M499 226c-4-10-8-20-13-30l-7-11c0-1-1-2-2-3l1-1c2 3 4 6 5 8 7 11 13 24 17 36l-1 1z" class="G"></path><path d="M470 178c3 4 7 9 10 14 0 3 0 5 1 7l-1 2v2l-6-9c0-4-2-7-3-10 0-2-1-4-1-6z" class="AA"></path><path d="M453 160c6 5 11 11 17 18 0 2 1 4 1 6 1 3 3 6 3 10l-13-17c0-1 0-1 1-2-1-1-2-3-2-5h1c-1-3-5-5-7-7 0-1-1-2-1-3z" class="AU"></path><defs><linearGradient id="e" x1="479.354" y1="212.69" x2="497.449" y2="214.349" xlink:href="#B"><stop offset="0" stop-color="#baad81"></stop><stop offset="1" stop-color="#efcd9b"></stop></linearGradient></defs><path fill="url(#e)" d="M480 192c4 7 7 14 10 22 3 6 5 12 7 18 1 2 1 5 1 7 0-1 0-2-1-3l-1-1c-1-3-2-9-4-11 0-2-3-6-4-8v-1l-1 3-7-15v-2l1-2c-1-2-1-4-1-7z"></path><defs><linearGradient id="f" x1="193.244" y1="400.369" x2="167.741" y2="418.512" xlink:href="#B"><stop offset="0" stop-color="#4f4f54"></stop><stop offset="1" stop-color="#838275"></stop></linearGradient></defs><path fill="url(#f)" d="M137 380c2 0 3 2 5 2-1-2-1-2-2-2h0c6 2 12 6 18 8v-1l1 1c1 1 2 1 3 1 2 1 3 1 4 2h1 0 1 0l-1-2 1-1c1 2 2 3 4 4h1l6 5 1-1 2 3h0c1 1 2 2 3 2 2 1 3 2 4 3 2 2 4 3 6 6 0 1 2 2 3 3h2v1c1 0 1 0 1-1 0-2-3-3-4-5h1 1c2 3 6 6 7 10v1l16 18v1h-1 0c2 2 4 5 6 8 1 1 2 3 4 4v4 15h-1v-2c-1-1-2-3-4-4 0-1-1-2-1-2-1-2-3-3-4-5v-2-1c-2-2-4-3-5-4-7-5-15-8-22-12l-6-4 5-2c0-1-1-1-1-1-2 0-3 1-4 1 0-2 1-2 2-3h0l-2-1-2 1h-2v-1h1v-1h-5c-1-1-3-1-4-1l2-2c3-1 8-1 11 0l4 1h1c-3-1-6-4-9-5h5l-1-1 2-1-7-5 1-1c0 1 1 1 1 1v-1c-7-6-15-11-23-16-3-2-7-3-11-5-1 0-1-1-2-2-3-1-5-2-8-3h2l-1-1h0c-1-2-4-3-6-4z"></path><path d="M199 416l1-1c1 1 1 1 3 1l3 3 16 18v1h-1c-2-3-5-6-7-9-5-4-10-9-15-13z" class="J"></path><path d="M158 388v-1l1 1c1 1 2 1 3 1 2 1 3 1 4 2h1 0 1 0l-1-2 1-1c1 2 2 3 4 4h1l6 5 1-1 2 3h0c1 1 2 2 3 2 2 1 3 2 4 3 2 2 4 3 6 6 0 1 2 2 3 3h2v1c1 0 1 0 1-1 0-2-3-3-4-5h1 1c2 3 6 6 7 10v1l-3-3c-2 0-2 0-3-1l-1 1c-1 0-5-4-6-5-5-4-10-9-16-12-6-4-13-8-19-11z" class="E"></path><path d="M180 396l2 3h-2l-1-2 1-1z" class="N"></path><path d="M143 384c11 4 21 10 31 16 9 6 17 12 25 20 7 5 13 12 19 18 1 0 1 0 2 1l1-1c2 2 4 5 6 8 1 1 2 3 4 4v4h-1v2c-1 1-1 1-2 1-3-3-4-7-6-10-3-4-7-9-11-12-2-2-3-4-5-6l-11-10c-2 0-3-1-4-2l-7-5 1-1c0 1 1 1 1 1v-1c-7-6-15-11-23-16-3-2-7-3-11-5-1 0-1-1-2-2-3-1-5-2-8-3h2l-1-1h0z" class="L"></path><path d="M186 411c3 2 8 5 9 8-2 0-3-1-4-2l-7-5 1-1c0 1 1 1 1 1v-1z" class="B"></path><path d="M218 438c1 0 1 0 2 1l1-1c2 2 4 5 6 8 1 1 2 3 4 4v4h-1v-1c-4-6-8-10-12-15z" class="N"></path><defs><linearGradient id="g" x1="233.733" y1="446.789" x2="183.809" y2="432.572" xlink:href="#B"><stop offset="0" stop-color="#393938"></stop><stop offset="1" stop-color="#51514f"></stop></linearGradient></defs><path fill="url(#g)" d="M191 417c1 1 2 2 4 2l11 10c2 2 3 4 5 6 4 3 8 8 11 12 2 3 3 7 6 10 1 0 1 0 2-1v-2h1v15h-1v-2c-1-1-2-3-4-4 0-1-1-2-1-2-1-2-3-3-4-5v-2-1c-2-2-4-3-5-4-7-5-15-8-22-12l-6-4 5-2c0-1-1-1-1-1-2 0-3 1-4 1 0-2 1-2 2-3h0l-2-1-2 1h-2v-1h1v-1h-5c-1-1-3-1-4-1l2-2c3-1 8-1 11 0l4 1h1c-3-1-6-4-9-5h5l-1-1 2-1z"></path><path d="M192 430h3 2l-1 1h-1-2c0-1-1-1-1-1z" class="L"></path><path d="M206 429c2 2 3 4 5 6l-1-1h0-1c-2-1-3-2-3-5zm-16-1l3 1c1 1 1 1 2 1h-3c-2 0-3 1-4 1 0-2 1-2 2-3z" class="D"></path><path d="M196 434c5 2 10 5 15 8v1h0c-3-2-6-4-9-5-2-1-4-1-6-3h1-1v-1z" class="X"></path><path d="M197 430c3 1 6 2 8 4 3 1 5 3 7 4h-1v1c-4-3-10-7-15-8l1-1z" class="E"></path><path d="M195 431h1c5 1 11 5 15 8l5 5h-1v1c-1-1-3-2-4-3-5-3-10-6-15-8h0c-1 0-2 0-2-1l1-2z" class="N"></path><path d="M178 423c3-1 8-1 11 0l4 1h1c1 0 2 1 3 2 2 1 3 1 5 3h-1-1c-2-2-3-2-6-2 0-1 0 0-1-1h-3v2l-2-1-2 1h-2v-1h1v-1h-5c-1-1-3-1-4-1l2-2z" class="D"></path><path d="M188 427v-1h2v2l-2-1z" class="W"></path><path d="M178 423c3-1 8-1 11 0l4 1c0 1 0 1-1 1h-6c0-1-1-1-2-1l-1-1h-3c-1 1-1 1-2 0z" class="N"></path><path d="M193 352l1 2c2 3 2 5 3 8s3 5 3 7l2 2c-1 0-1 0-2-1v1c1 0 1 1 1 1 0 1 0 1 1 2 0 1 1 1 1 2 1 1 1 2 2 2 1 1 0 1 1 1 0 1 1 2 2 3 2 3 6 5 8 8 4 4 8 9 11 14 1 1 3 2 3 4l1 21-1 10h0c0 1-1 2-2 3h0v2c0 1 0 1-1 2-2-3-4-6-6-8h0 1v-1l-16-18v-1c-1-4-5-7-7-10h-1-1c1 2 4 3 4 5 0 1 0 1-1 1v-1h-2c-1-1-3-2-3-3l1-2c0-2-1-3-2-5s-1-4-2-6c0-2-1-4-1-6-1-1-1-3-2-4s-1-1-1-3h0-1v1-1c-1-1-2-1-2-2v-1h-1 2v1h1c0-1-1-1-1-2-1-1-3-3-3-4-2-1-2-3-3-5l-1-2c-1-3 0-4 1-6l2-2c0 1 0 1 1 2 2-1 2-1 3-2h1v-2l1-1h1v-1l-1-1 1-1c0 1 1 1 2 2l1-1 1-4z" class="L"></path><path d="M194 354c2 3 2 5 3 8s3 5 3 7c-4-3-6-9-7-14l1-1z" class="E"></path><path d="M199 377l1-1v1c1 1 2 1 3 3 1 1 2 1 2 1 2 2 6 4 6 6h-1s-1 0-1-1c-1 0-2 0-2-1 0 3 2 7 2 9l-2-1v-3c-1 1-1 2-1 2h0v-1h-1v2h-1c-1-2-1-3-2-4 0-2-1-3-1-4-1-3-2-6-2-8z" class="M"></path><path d="M207 390c0-2-2-6-3-8l3 3c0 3 2 7 2 9l-2-1v-3z" class="Y"></path><path d="M192 364l-2-6v-1c1 1 2 2 2 3v1c2 5 7 10 7 16 0 2 1 5 2 8 0 1 1 2 1 4 1 1 1 2 2 4h1v-2h1v1h0s0-1 1-2v3h-1v4c1 0 1 1 1 2 2 2 1 5 1 9-1-1-1-4-2-5s-1-3-1-4c-1-1-2-2-2-4h0-1s0 1-1 1c-1-1-2-2-2-3l-1-2-1 2h0v-1c-1 0-1-1-1-1-1-2-1-3-2-5h0l1 1 1-1 1 1h0c1 1 1 1 2 1h1v-1l-1-1c0-1-2-1-3-1s-1 0-1-1l-3-6v-5-3c-1-2-1-3-1-5l1-1z" class="C"></path><path d="M192 364c0 1 1 2 1 4s0 4 1 6v2c0 3 3 7 2 9-1 0-1 0-1-1l-3-6v-5-3c-1-2-1-3-1-5l1-1z" class="M"></path><path d="M187 359c1 0 1 0 1 1 1 2 3 3 3 5s0 3 1 5v3 5l3 6c0 1 0 1 1 1s3 0 3 1l1 1v1h-1c-1 0-1 0-2-1h0l-1-1-1 1-1-1h0l-2-1c-1 0-2 0-3-1s-1 0-1-1l-3-2h-1 2v1h1c0-1-1-1-1-2-1-1-3-3-3-4-2-1-2-3-3-5l-1-2c-1-3 0-4 1-6l2-2c0 1 0 1 1 2 2-1 2-1 3-2h1v-2z" class="X"></path><path d="M182 366h1c1 1 1 2 1 3v1l-1-1c0-1-1-2-1-3z" class="W"></path><path d="M191 371v3l-1-1c0-1-1-3-1-5l1-1c1 1 1 2 1 4z" class="D"></path><path d="M187 359c1 0 1 0 1 1 1 2 3 3 3 5s0 3 1 5v3l-1-2c0-2 0-3-1-4s-1-2-2-2v1l-1-1c0-1 0-2-1-3v-1h1v-2z" class="l"></path><path d="M186 362h0v2c0 1 1 3 2 4 0 2 0 4 1 6h0v1l-1-1-2-2c-2-2-1-3-1-6 0 0-1-1-1-2 1-1 1-1 1-2h1z" class="D"></path><path d="M180 363c1 0 1 1 2 1v2c0 1 1 2 1 3l1 1 2 4h0v1h-1c-1 0-3-3-5-3v-1l-1-2c-1-3 0-4 1-6z" class="N"></path><path d="M180 363c1 0 1 1 2 1-1 1-2 3-2 5 1 1 2 2 2 3 1 1 2 2 3 2h1 0v1h-1c-1 0-3-3-5-3v-1l-1-2c-1-3 0-4 1-6z" class="W"></path><path d="M185 381l3 2c0 1 0 0 1 1s2 1 3 1l2 1c1 2 1 3 2 5 0 0 0 1 1 1v1h0l1-2 1 2c0 1 1 2 2 3 1 0 1-1 1-1h1 0c0 2 1 3 2 4 0 1 0 3 1 4s1 4 2 5h0 1c1 2-1 6 0 8 1 0 0 0 1 1h-1c-1-1-1-3-3-4h0c0 2 0 3 1 4v1h-1c-1-4-5-7-7-10h-1-1c1 2 4 3 4 5 0 1 0 1-1 1v-1h-2c-1-1-3-2-3-3l1-2c0-2-1-3-2-5s-1-4-2-6c0-2-1-4-1-6-1-1-1-3-2-4s-1-1-1-3h0-1v1-1c-1-1-2-1-2-2v-1z" class="Q"></path><path d="M199 393c0 1 1 2 2 3 1 0 1-1 1-1h1 0c0 2 1 3 2 4 0 1 0 3 1 4s1 4 2 5h0v3c-1 1-1 1-2 1-1-2-2-5-3-7 0-2 0-4-1-5 0-1-1-1-1-2h0c0-1-1-2-3-3l-1 1v-1h0l1-1c0-1 0-1 1-1h0z" class="W"></path><path d="M185 381l3 2c0 1 0 0 1 1s2 1 3 1l2 1c1 2 1 3 2 5 0 0 0 1 1 1v1c-1 1-1 1 0 3v1h0c-1-1-1-2-2-3s-2-3-2-5l-1-1-1 1c0 1 1 4 2 6v2c1 4 4 7 6 11h-1-1c1 2 4 3 4 5 0 1 0 1-1 1v-1h-2c-1-1-3-2-3-3l1-2c0-2-1-3-2-5s-1-4-2-6c0-2-1-4-1-6-1-1-1-3-2-4s-1-1-1-3h0-1v1-1c-1-1-2-1-2-2v-1z" class="N"></path><path d="M217 401c0-2 0-4-1-6 0-1-1-3 0-5 4 4 8 9 11 14 1 1 3 2 3 4l1 21-1 10h0c0 1-1 2-2 3h0v2c0 1 0 1-1 2-2-3-4-6-6-8h0 1v-1l-16-18v-1h1v-1c-1-1-1-2-1-4h0c2 1 2 3 3 4h1c-1-1 0-1-1-1-1-2 1-6 0-8h-1 0c0-4 1-7-1-9 0-1 0-2-1-2v-4h1l2 1c0 1 2 3 2 3 2 1 3 2 4 3 1 0 1 1 2 1z" class="X"></path><path d="M215 400c1 0 1 1 2 1s2 1 2 2 0 3 1 4v1h0v-2-2h1c1 1 1 3 2 4 0 3 2 6 3 9-1-1-2-3-3-5h0v2c-1-2 0-5-2-7-1 2 0 3-1 4-1-1-1-2-1-3v-4h-1c0-2 0-2-1-2h-1l-1-2z" class="M"></path><path d="M217 401c0-2 0-4-1-6 0-1-1-3 0-5 4 4 8 9 11 14 1 1 3 2 3 4l1 21h-2c-2-3-1-8-3-12-1-3-3-6-3-9-1-1-1-3-2-4h-1v2 2h0v-1c-1-1-1-3-1-4s-1-2-2-2z" class="AR"></path><defs><linearGradient id="h" x1="235.2" y1="428.403" x2="198.03" y2="409.738" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#535351"></stop></linearGradient></defs><path fill="url(#h)" d="M208 408c0-4 1-7-1-9 0-1 0-2-1-2v-4h1l2 1c0 1 2 3 2 3l1 5h1c0-1 1-1 1-2l1 1c0 2 0 4 1 6 0 1 0 2 1 3 0 1-1 2 0 4l3 9v1l1-1h0v1s0 1 1 1c1 2 1 4 3 5v1c0 1 1 1 0 2l5 6c0 1-1 2-2 3h0v2c0 1 0 1-1 2-2-3-4-6-6-8h0 1v-1l-16-18v-1h1v-1c-1-1-1-2-1-4h0c2 1 2 3 3 4h1c-1-1 0-1-1-1-1-2 1-6 0-8h-1 0z"></path><path d="M228 442c0-3-3-7-3-9l5 6c0 1-1 2-2 3zm-50-224v-1l3-6c4-7 9-12 16-16 7-5 18-7 27-5 2 0 4 1 6 1v12l1 19c-1 2-2 3-4 4h-1c-1 0-2 0-2 1-3 1-6 5-6 8h0l-3 6h-1v4 1h0c-2 1-2 2-3 3h-2 0l-2 6c-2 1-2 2-3 4-1 0-1 1-2 1v1c0 3-1 6-2 9h0l-1-1c-1 1-2 1-3 0l-2-1c-2 0-5-2-7-3h1v-2l-1-1v-1h-2c-1-1-1-2-1-3l-6-3-4-5-1-1h-1l-3-9h1v-2h1l2-1v1-6h1v2-2l1-2 1 1c0-5 0-9 2-13z" class="AR"></path><path d="M216 210h3c1 0 2 0 3 1l-1 1c-1-1-1-1-2-1h-1c-1 1-2 2-3 2h0c0-2 0-2 1-3z" class="e"></path><path d="M198 225h1c1 0 1 1 2 2v1h-2-3v-1c0-1 1-1 2-2zm-2-9h5v2h-1c-1 1-2 1-4 1v-3z" class="AI"></path><path d="M214 241v4 1h0c-2 1-2 2-3 3h-2l5-8z" class="w"></path><path d="M187 249c1 0 2 0 3 1v1h-2 0l2 1h0c-2 2-3 2-5 2 0-2 1-3 1-4l1-1z" class="AG"></path><path d="M202 260l1-3c1-1 4-8 5-8h1l-2 6c-2 1-2 2-3 4-1 0-1 1-2 1z" class="m"></path><path d="M175 230l1 1c1 4 3 6 7 8 2 1 4 1 6 1h1c0 1-2 2-2 3h-1l-2 1c-3-1-4-2-6-3-1-1-2-2-3-2v-1l-1-1c0-1-1-2-1-3v-2l1-2z" class="f"></path><path d="M179 241c2 0 4 0 6 2h2l-2 1c-3-1-4-2-6-3z" class="T"></path><path d="M173 238v-6h1v2c0 1 1 2 1 3l1 1v1c1 0 2 1 3 2 2 1 3 2 6 3-1 1-2 1-2 2v1l4 2-1 1h-1 0c-3 0-5 0-7-2s-4-6-5-10z" class="K"></path><path d="M176 239c1 0 2 1 3 2 2 1 3 2 6 3-1 1-2 1-2 2v1c-3-2-5-5-7-8z" class="F"></path><path d="M173 237v1c1 4 3 8 5 10s4 2 7 2h0 1c0 1-1 2-1 4h-1c-2-1-4-1-6-3l-3-3-1 2-1-1h-1l-3-9h1v-2h1l2-1z" class="x"></path><path d="M185 250h1c0 1-1 2-1 4h-1c-2-1-4-1-6-3h3c1 1 1 1 2 1 1-1 2-1 2-2z" class="AP"></path><path d="M170 238l1 1h0c0 2 2 3 2 5v1l1 1 1 1v1l-1 2-1-1h-1l-3-9h1v-2z" class="AI"></path><path d="M184 258c4 2 8 4 11 5 2 0 3 0 4 1h1 1c0-1 1-2 1-3 0 3-1 6-2 9h0l-1-1c-1 1-2 1-3 0l-2-1c-2 0-5-2-7-3h1v-2l-1-1v-1h-2c-1-1-1-2-1-3z" class="X"></path><path d="M195 263c2 0 3 0 4 1h1c0 1 0 1-1 2h-2v1l-3-1c1 0 1-1 2-1h1l-2-2z" class="N"></path><path d="M188 263c2 0 4 2 6 3l3 1h1v1h-4c-2 0-5-2-7-3h1v-2z" class="AS"></path><path d="M184 258c4 2 8 4 11 5l2 2h-1c-1 0-1 1-2 1-2-1-4-3-6-3l-1-1v-1h-2c-1-1-1-2-1-3z" class="AK"></path><defs><linearGradient id="i" x1="255.677" y1="348.722" x2="178.968" y2="313.157" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#60605f"></stop></linearGradient></defs><path fill="url(#i)" d="M214 245h1v5l-1 1c0 2 0 2-2 4h0v-1h-1v-1c-1 1-1 3-2 4h1c1 3 0 5 0 8l1-2h1 1c1 2 0 6 0 9v19 11c0 2 1 4 1 6 0 3 1 6 1 9 1 1 1 2 1 3s1 2 1 3v1c1 0 1 1 1 1v2l1 3c-1 3 3 8 4 11 1 2 1 3 1 5 1 4 3 7 5 11h1v3 5 8 8c0 3 1 6 0 9v4 14c0-2-2-3-3-4-3-5-7-10-11-14-2-3-6-5-8-8-1-1-2-2-2-3-1 0 0 0-1-1-1 0-1-1-2-2 0-1-1-1-1-2-1-1-1-1-1-2 0 0 0-1-1-1v-1c1 1 1 1 2 1l-2-2c0-2-2-4-3-7s-1-5-3-8l-1-2c-1-1-2-2-2-3v-1-2h1 1c-2-2-3-4-5-5-1-1-2-1-3-2h0 0c-1-2-3-2-3-3-1-1-1-1-2-1l-1-1c3 0 5 1 7 3 2 1 3 3 4 4h1v-2l-1-6h0v-5l-1-7c1-3 1-6 1-8l3-19c1-4 2-7 3-11l2-4c0-3 1-6 2-9h0c1-3 2-6 2-9v-1c1 0 1-1 2-1 1-2 1-3 3-4l2-6h0 2c1-1 1-2 3-3h0v-1z"></path><path d="M197 297v2-2l1 1-2 9v11 4h-1c1-2 0-4 1-5 0-2 0-7-1-9v2 1c0-4 1-10 2-14z" class="N"></path><path d="M190 333l1-1 1 1h-1 0v4 2l1-1v-2c-1 0 0-1 0-2h0 1l1 1v5h-1-1 0l-1-1v2 1l-1-1h1v-2l-1-6z" class="Q"></path><path d="M202 364c2 1 2 2 3 3 3 5 5 11 10 15l-1 1-2-2c-1-2-3-3-4-5s-1-3-2-4c-1-3-2-5-4-8z" class="B"></path><path d="M198 335h0c0-1 0-2-1-3v-2-1c0-1-1-2 0-2v-5h0c1-3 0-6 0-8 1-2 1-4 2-6v-1-2l2-3c-1 2-1 4-1 6 0 1-1 2-1 3 0 3 0 6-1 9-1 1-1 5-1 7 0 3 1 5 1 8z" class="W"></path><path d="M201 301s0-1 1-1l1 1v3c0 1-1 2-1 3s1 2 1 3l1 2c-1 1-2 3-3 3h-1v1h0v1 5c-1 1-2 2-2 3l-1 2c0-2 0-6 1-7 1-3 1-6 1-9 0-1 1-2 1-3 0-2 0-4 1-6v-1z" class="E"></path><path d="M203 310l1 2c-1 1-2 3-3 3h-1v-2l3-3z" class="T"></path><path d="M214 245h1v5l-1 1c0 2 0 2-2 4h0v-1h-1v-1c-1 1-1 3-2 4v2l-3 9h-1 0v-3-1l1-1c0-1 0-1-1-2 0-1 2-5 2-6l2-6h0 2c1-1 1-2 3-3h0v-1z" class="AV"></path><path d="M214 245h1v5l-1 1c0 2 0 2-2 4h0v-1c1-3 2-5 2-8v-1z" class="AA"></path><path d="M201 301c0-1 0-2 1-3h0v-1h0c0-1 0-2 1-2v-2-1c0-1 1-1 1-1v-1c0-1 1-3 0-4 0-1 0-2-1-3h0 1 0v-1c1-2 1-5 1-8v12 5l1 1c0 2-1 5 0 7s0 3 0 4c0 2 1 5 1 7 1-1 0-2 0-3h1v2 2 4 1l-2-1c0 1 0 1-1 1v-2l-1-2-1-2c0-1-1-2-1-3s1-2 1-3v-3l-1-1c-1 0-1 1-1 1z" class="D"></path><path d="M203 301c0-1 0-2 1-3h0l1 3-1 1s0 1-1 2v-3z" class="i"></path><path d="M206 310l2 5v1l-2-1c0 1 0 1-1 1v-2h1v-4h0z" class="Z"></path><path d="M203 304c1-1 1-2 1-2l1-1 1 9h0v4h-1l-1-2-1-2c0-1-1-2-1-3s1-2 1-3z" class="O"></path><defs><linearGradient id="j" x1="211.038" y1="369.787" x2="188.916" y2="352.124" xlink:href="#B"><stop offset="0" stop-color="#4a4948"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#j)" d="M192 340v1c1 1 2 1 3 3h0v-2-5c-1 0 0-1-1-2v-3 1c1 2 1 3 1 5 1 3 1 6 1 9 1 5 3 9 5 14 0 1 1 3 1 3 2 3 3 5 4 8 1 1 1 2 2 4s3 3 4 5v1l-2-3-1 1c-1-3-3-4-4-6l-3-3-2-2c0-2-2-4-3-7s-1-5-3-8l-1-2c-1-1-2-2-2-3v-1-2h1 1c-2-2-3-4-5-5-1-1-2-1-3-2h0 0c-1-2-3-2-3-3-1-1-1-1-2-1l-1-1c3 0 5 1 7 3 2 1 3 3 4 4l1 1v-1-2l1 1z"></path><path d="M200 317v-1h0l8 9c0 1 2 2 1 3 0 0-1-1-2-1s-2 1-3 2l-1 3v1 2c0 1 1 2 1 3 0 5-1 10 0 14 1 2 1 3 1 4l2 2h-2l-1-2-1 1c0 1 1 2 2 3v1c1 2 4 5 5 8h-1 0c-1-2-2-4-4-6v1c0-3-2-4-3-7 0-2-1-4-1-6-2-4-2-10-2-15l-1-1c0-3-1-5-1-8l1-2c0-1 1-2 2-3v-5z" class="M"></path><path d="M197 327l1-2c0-1 1-2 2-3-1 3 0 6-1 8v6l-1-1c0-3-1-5-1-8zm5 8v1 7h-1v-6c-1 0-1-1-1-1v-3-1l1-1c1 1 1 2 1 4z" class="B"></path><path d="M200 317v-1h0l8 9c0 1 2 2 1 3 0 0-1-1-2-1s-2 1-3 2l-1 3v1 2h-1c0-2 0-3-1-4 0-2-1-4-1-6v-8z" class="Y"></path><path d="M205 360c-1-1-2-2-2-3l1-1 1 2h2l3 3c1 0 1 0 1 1 1 0 2 1 3 1l2 2h0v1c1 0 1 1 2 2h0c1 0 2 1 3 1s1 0 2 1 2 3 3 5 2 4 4 5v1h0c0 3 1 6 0 9v4 1h-1c-2-1-4-4-4-5-3-3-6-6-8-10-3-3-5-7-7-11-1-3-4-6-5-8v-1z" class="AR"></path><path d="M205 360c-1-1-2-2-2-3l1-1 1 2h2l3 3c1 0 1 0 1 1 1 0 2 1 3 1l2 2h0v1c1 0 1 1 2 2h0c1 0 2 1 3 1s1 0 2 1 2 3 3 5 2 4 4 5v1h0c0 3 1 6 0 9-2-3-3-6-5-9-6-8-12-15-20-21z" class="E"></path><path d="M205 358h2l3 3c1 0 1 0 1 1 1 0 2 1 3 1l2 2h0-2l-3-3c-1 0-1 0-2-1 0 0-1-1-2-1l-2-2z" class="F"></path><path d="M221 369c1 0 1 0 2 1s2 3 3 5 2 4 4 5v1 4h-1c-1-1-1-4-2-6l-4-7c0-1-2-2-2-3z" class="J"></path><defs><linearGradient id="k" x1="219.179" y1="333.255" x2="213.158" y2="376.681" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#k)" d="M203 332l1-3c1-1 2-2 3-2s2 1 2 1l1 1c1 1 1 2 2 3l1 1h0v1 1l3 5c0 3 2 7 4 9l1 2c1 1 1 2 2 3 1 2 1 3 2 4 1 3 3 5 4 7v1h1v-1 8 8h0v-1c-2-1-3-3-4-5s-2-4-3-5-1-1-2-1-2-1-3-1h0c-1-1-1-2-2-2v-1h0l-2-2c-1 0-2-1-3-1 0-1 0-1-1-1l-3-3-2-2c0-1 0-2-1-4-1-4 0-9 0-14 0-1-1-2-1-3v-2-1z"></path><path d="M203 332h1l2-1c0 2 0 2-1 3-1 0-2 0-2-1v-1z" class="L"></path><path d="M208 346c-1-1-1-1 0-2h0c-1-1-1-2-2-2 0-1 2-2 3-3h0l1 1c-1 1-2 2-2 3s1 2 1 2h-1v1z" class="J"></path><path d="M203 332l1-3c1-1 2-2 3-2s2 1 2 1l1 1c1 1 1 2 2 3l1 1h0v1 1l-3-3c-1-1-3-1-4-1l-2 1h-1z" class="R"></path><path d="M214 363c-1-1-2-3-3-3-1-1-2-4-2-5v-3c2 1 4 1 5 3 1 1 1 1 1 2 0 3 2 6 1 8l-2-2z" class="AR"></path><path d="M210 340c3 4 7 8 8 13 0 1 0 2 1 3 1 2 1 5 2 8h0c-1-2-2-3-2-4l-1-3c0-1-1-1-1-2s-3-4-3-5l-2-1h0c-2-1-3-1-4-3v-1h1s-1-1-1-2 1-2 2-3zm-1-83h1c1 3 0 5 0 8l1-2h1 1c1 2 0 6 0 9v19 11c0 2 1 4 1 6 0 3 1 6 1 9 1 1 1 2 1 3s1 2 1 3v1c1 0 1 1 1 1v2l1 3c-1 3 3 8 4 11 1 2 1 3 1 5 1 4 3 7 5 11h1v3 5 1h-1v-1c-1-2-3-4-4-7-1-1-1-2-2-4-1-1-1-2-2-3l-1-2c-2-2-4-6-4-9l-3-5v-1-1h0l-1-1c-1-1-1-2-2-3l-1-1c1-1-1-2-1-3l-8-9v-1h1c1 0 2-2 3-3l1 2v2c1 0 1 0 1-1l2 1v-1-4-2-2h-1c0 1 1 2 0 3 0-2-1-5-1-7 0-1 1-2 0-4s0-5 0-7l-1-1v-5-12l1-6 3-9v-2z" class="F"></path><path d="M208 311c1 2 1 3 1 5 1 2 1 3 1 5h-1l-1-5v-1-4z" class="E"></path><path d="M208 302v-12l2 8h0c-1 4 6 20 4 23h0l-6-19zm5-39c1 2 0 6 0 9v19h-1v-5-2-9-4c0-1 1-4 0-5v3c-1 7-1 14-1 20 0 0 0-1-1-2 0-8 1-16 2-24h1z" class="J"></path><defs><linearGradient id="l" x1="205.78" y1="273.799" x2="214.313" y2="268.667" xlink:href="#B"><stop offset="0" stop-color="#5a585a"></stop><stop offset="1" stop-color="#6f726b"></stop></linearGradient></defs><path fill="url(#l)" d="M211 263h1c-1 8-2 16-2 24l1 8h-1v-15l-1-1c-1 1-1 2-1 3 1 1 0 2 0 3v1-5-5c0-4 1-7 2-11l1-2z"></path><path d="M230 366c0-2-2-3-2-5-1-1 0-1-1-1-1-2-2-4-3-5 0-2-1-4-2-5-1-2-2-4-2-5h0v-1c1 2 2 3 2 5 0 1 2 3 3 5s2 3 3 5c-1-6-6-12-8-17 0-1-1-2-1-4h0v-1h0c1 1 1 1 1 2 1 2 3 5 4 7 1 4 3 7 5 11h1v3 5 1z" class="L"></path><path d="M200 315h1c1 0 2-2 3-3l1 2v2c1 0 1 0 1-1l2 1 1 5h1l2 6c1 3 2 7 3 10 1 1 1 2 1 3h0l-3-5v-1-1h0l-1-1c-1-1-1-2-2-3l-1-1c1-1-1-2-1-3l-8-9v-1z" class="q"></path><path d="M205 316c1 0 1 0 1-1l2 1 1 5h1l2 6h-1v-1c-1-1-2-1-3-3 0-1-2-2-2-3v-2l-1-2z" class="d"></path><path d="M205 316c1 0 1 0 1-1l2 1 1 5h0l-3-3-1-2z" class="T"></path><defs><linearGradient id="m" x1="204.323" y1="285.479" x2="211.1" y2="284.775" xlink:href="#B"><stop offset="0" stop-color="#353534"></stop><stop offset="1" stop-color="#525250"></stop></linearGradient></defs><path fill="url(#m)" d="M209 257h1c1 3 0 5 0 8-1 4-2 7-2 11v5 5l1 2v1 4c1 1 1 2 1 3v2l-2-8v12c1 3 1 5 0 7v-2h-1c0 1 1 2 0 3 0-2-1-5-1-7 0-1 1-2 0-4s0-5 0-7l-1-1v-5-12l1-6 3-9v-2z"></path><path d="M209 257h1c0 2-1 4-1 6v-2-2-2z" class="m"></path><path d="M208 281l-1-2c0-1-1-3-1-5 0-1 0-2 1-4v3c1 1 1 2 1 3v5z" class="W"></path><path d="M83 194c2-12 6-23 11-34 8-15 17-29 28-42 5-6 11-11 17-16 5-4 9-8 13-12 2-1 8 0 10 0h216 84 34 17v97 29c0 5 1 9 0 14v45c0 3 0 7-1 10l-1-1c-4-8-7-18-9-27 2 2 2 4 3 6 0 2 1 4 2 5 1 3 1 6 3 8v-33-4l-1-1v-22c1-4 1-9 1-13h0c0 2 0 4 1 6v-9-35l-1-38v-21-11h-1-34-54-165-72-18-10c-1 0-2 2-3 2l-3 3-11 9-9 10c-2 1-3 3-4 5-2 1-3 3-5 5v1c-4 5-10 13-12 19l-1 1c-1 1-2 3-3 5 5-4 9-8 14-12l9-6c3 0 5-1 7-2 3-2 7-4 11-4v1l-15 6c-20 11-34 28-44 48-1 3-2 6-4 9v-1h0z" class="p"></path><path d="M511 200v16l1 24c0 2-1 4-1 5 1 2 0 3 0 4v14 5-1c-1 2-1 5-1 6v1c1 1 1 1 1 2h1v-1h0c0-2-1-7 0-8v-1-1-4h0v-1l1-24v-6 45c0 3 0 7-1 10l-1-1c-4-8-7-18-9-27 2 2 2 4 3 6 0 2 1 4 2 5 1 3 1 6 3 8v-33-4l-1-1v-22c1-4 1-9 1-13h0c0 2 0 4 1 6v-9z" class="H"></path><path d="M509 216c1-4 1-9 1-13h0c0 2 0 4 1 6l-1 38h0v-4-4l-1-1v-22zM153 97h0v-1c2-2 4-2 6-2 3 1 6 0 9 0h14 52 198 48 29l1 1h-1-34-54-165-72-18-10c-1 0-2 2-3 2z" class="Z"></path><path d="M321 534c0-1 1-2 2-3 0 1 1 1 1 2h-1v1s0 1-1 1l1 1c1 1 2 3 2 4 3 3 6 8 8 11 1 1 2 2 2 4 1 1 2 1 3 2l3 4-1 1h0l-1 2h0-1 0-2v2c1 1 3 3 3 5-1 0-2-1-2-1-1 1-1 2-1 3-1 1-1 3-2 4 0 1-1 2-1 3l1 1v-1c0-1 1-2 2-3l1 2 3 2 2 2v3c-1 0-1 1-2 1h2c1 0 1 1 1 1h0l1 1c1 0 2 0 3 1h0v-1h2 2v1c1 1 1 2 1 4 0 3 0 7-1 10v1c-2 5-6 11-9 16l-1 1v1c1 0 1 1 1 1-1 2-3 4-5 6v1l1-3h-1-3-17c-1 1-4 0-6 2v1h-3l-3-3 1-1h0c-1 0-2-1-3-1 0-1 0-1 1-2-2 0-3-3-4-4l-6-8-2-5v-1c1 0 0 0 0-1s-1-2-1-4-1-6 0-8c0-1 0-2 1-4v-3c1-1 1-2 1-3 1-1 1-3 2-5s2-4 2-6v-1l6-11c3-3 5-7 7-11 3-3 5-6 7-9 1-2 3-4 4-6z" class="Y"></path><path d="M312 620h0l1 1s0 1-1 1h-2s-1 0-1-1h2l1-1z" class="B"></path><path d="M340 621h2l-1 1-2 1c-1 0-1 0-2 1h-1l-1-1v-1h1c0 1 0 1 1 1v-1h-1l1-1h0 3z" class="C"></path><path d="M318 621c1 0 1 0 1-1h2v-3c0 2 1 5 0 7-1 0-2 0-2-1-1 0-1-1-1-2z" class="AN"></path><path d="M351 590c1 1 1 2 1 4-1 0-2 1-2 2-1 0-1 1-1 1 0-2-1-4-3-5 2 0 3-1 4 0h1 0v-2z" class="AL"></path><path d="M300 572l1-2c1 0 2 0 2 1h1l1 1c0 1-1 3-2 3-1-1-1-2-2-3h0-1z" class="F"></path><path d="M312 622l1 1s1 1 1 2l1-1h0v-1c0-2-2-5-3-6l1-1c0 1 0 1 1 2h0c1 2 2 6 2 7h0-4c-1-1-1-2-2-3h2z" class="l"></path><path d="M344 604h5c-1 1-3 2-5 3h-1 0c-1-1-1 0-3 0v-1c2-1 3-1 4-2z" class="y"></path><path d="M301 606c2 1 3 2 4 4-3-2-8-3-11-5 1-1 2-1 3 0 2 0 3 1 4 1h0z" class="AP"></path><path d="M314 617c1 0 2 1 4 0v1 1 2c0 1 0 2 1 2 0 1 1 1 2 1l-1 1c-1 0-2 0-3-1 0-2-1-5-3-7z" class="AJ"></path><path d="M297 614s0-1 1-1c2 0 2 2 3 2 2 1 1-1 2 1h0v2h-3l-3-4z" class="J"></path><path d="M337 624c1-1 1-1 2-1l2-1v1c1 0 1 1 1 1-1 2-3 4-5 6v1l1-3h-1-3c1-1 2-1 3-1s1 0 2-1l-2-2z" class="E"></path><path d="M306 627c2 1 5 1 7 1h4c-1 1-4 0-6 2v1h-3l-3-3 1-1z" class="V"></path><path d="M334 612l1 1h2c0 2-2 4-3 6v2l-1 1h0c-1-1-1-1-1-3s0-3 2-4h0v-1-2z" class="l"></path><path d="M308 614v-1h2c0 1 0 1 1 2 1 2 1 3 1 5l-1 1c-1-1-1 0-2-1-1-2-1-4-1-6z" class="E"></path><path d="M308 614v-1h2c0 1 0 1 1 2v3l-1-1v-1l-2-2h0z" class="AL"></path><path d="M344 596v-2c0-1-1-1 0-2s1-1 2 0c2 1 3 3 3 5v1h0c-1 0-1 1-2 0-2 0-2-1-3-2z" class="E"></path><path d="M344 596c1 0 1 0 2-1v-1h0l2 2v2h1 0c-1 0-1 1-2 0-2 0-2-1-3-2z" class="B"></path><path d="M341 589c1 0 1 0 1 1 1 2 0 4 0 6 0 1 0 4-1 5s-2 3-3 3l-1 1-2-1v-1h3v-1c0-1 1-1 2-1v-3c0-1 0-1 1-2-1 0-1 0-1-1h2l-1-1c-2-3-1-2 0-4v-1z" class="d"></path><path d="M311 557c1-2 2-3 2-4 1-1 1-1 1-2 1-1 1-3 2-4 1 0 2-1 2-2l3-3c1 0 1 0 1 1 2 0 2 1 3 2v1h0c-1 0-1-1-2-1-1-1-1-1-2-1-1 1-2 2-2 3 0 2-3 7-4 7l-1 1c-1 0-2 1-3 2z" class="M"></path><path d="M292 607v-1c2 3 3 6 5 8l3 4h3l2 1h0v3 1h1l-2 1c-2 0-3-3-4-4l-6-8-2-5z" class="AG"></path><path d="M300 618h3l2 1h0v3 1s-1-1-2-1c-1-1-2-3-3-4z" class="C"></path><path d="M321 534c0-1 1-2 2-3 0 1 1 1 1 2h-1v1s0 1-1 1l1 1c1 1 2 3 2 4v2l-2-2-2-3-24 35v-1l6-11c3-3 5-7 7-11 3-3 5-6 7-9 1-2 3-4 4-6z" class="y"></path><path d="M298 591c0-1 1-1 2-1v3c0 2 0 3 1 5v1c0 2 2 4 3 5 1 0 1 1 2 1l-2 1h0c-1-1-1-1-2-1h0-1v1h0c-1 0-2-1-4-1 0-1 1-2 1-2l1-1 1-1c-1-1-1-1-1-3h0v-1l-1 1c0 1-1 1-2 2-1 0-1 0-2-1-1 0-2-2-2-3v-3c2-1 2-1 3 0l3-1v-1z" class="E"></path><path d="M298 598c0 1-1 1-2 2-1 0-1 0-2-1-1 0-2-2-2-3v-3c2-1 2-1 3 0v5h1v-4h1 1c0 1 0 2-1 3v1h1zm2-26h1 0c1 1 1 2 2 3v1h0 1c0 1 1 1 1 1h1 1v1h-1c-2 1-3 3-4 4 0 1-1 2-1 4h0l1 2-1 1h-1v1c-1 0-2 0-2 1 0-1-1-2-1-3h-4v-1c2-2 3-3 4-5 0-2 0-2 1-4l1-1c1-1 0-1 0-2l1-1v-2z" class="C"></path><path d="M300 581l2 1c0 1-1 2-1 4h0l1 2-1 1h-1v1c-1 0-2 0-2 1 0-1-1-2-1-3l1-1h-1 0c2-1 2-4 3-6z" class="u"></path><path d="M298 587c1 0 1 0 2 1v1 1c-1 0-2 0-2 1 0-1-1-2-1-3l1-1z" class="F"></path><path d="M300 572h1 0c1 1 1 2 2 3v1h0 1c0 1 1 1 1 1h1 1v1h-1c-2 1-3 3-4 4l-2-1c1-1 0-4 1-5h1c-1-1-1-1-1-2h-1v-2z" class="AL"></path><path d="M332 612h2v2 1h0c-2 1-2 2-2 4s0 2 1 3h0l1 1c-1 1-2 1-3 2h1 0c-2 0-4 1-6 0v-1h-1c-1 1-2 2-3 1h0c-1-2 0-6 0-8v-1 1 2h1 2l2-1 1-2c1-2 2-3 4-4z" class="M"></path><path d="M322 616v1 2h1 2l2-1c0 2-1 4-2 6-1 0-2 1-3 1-1-2 0-6 0-8v-1z" class="q"></path><path d="M334 614v1h0c-2 1-2 2-2 4s0 2 1 3h0l1 1c-1 1-2 1-3 2h-3c-1 0-1 0-2-1 1-2 2-3 3-5s1-3 3-4l2-1z" class="E"></path><path d="M332 619c0 2 0 2 1 3h0l1 1c-1 1-2 1-3 2h-3c0-2 1-3 2-4 1 0 1-1 2-2z" class="l"></path><path d="M332 619c0 2 0 2 1 3h0v1h0c-1-1-2-1-3-2h0c1 0 1-1 2-2z" class="C"></path><path d="M332 590c2-1 6-1 8-1h1v1c-1 2-2 1 0 4l1 1h-2c0 1 0 1 1 1-1 1-1 1-1 2v3c-1 0-2 0-2 1v1h-3l-4-3c-2-1-3-2-4-3l-1-1v-1h3 0 2 0l-1-2c2-1 2-1 2-3z" class="G"></path><path d="M326 595h3 0c1 1 2 1 2 1 2 0 3 2 4 3h2l1 1c0 1-1 1-1 2v1c-2-1-3-2-4-3s-1-1-1-2h-1v2c-2-1-3-2-4-3l-1-1v-1z" class="AD"></path><path d="M332 590c2-1 6-1 8-1h1v1c-1 2-2 1 0 4l1 1h-2c0 1 0 1 1 1-1 1-1 1-1 2v3l-1-1v-1l-3-3c-1 0-1 0-2-1-1 0-1-1-1-2 0 0 0-1 1-1h0v-2h-2z" class="c"></path><path d="M318 554h2 0c-2 3-4 6-6 8-2 4-4 7-6 10v1c0 1 0 2 1 4v1h-1-1v-1h-1-1s-1 0-1-1h-1 0v-1c1 0 2-2 2-3l1-1c1-1-2-3-2-5h1l1-1v-1c0-2 2-5 3-6l2-1c1-1 2-2 3-2l-2 3c2-1 4-3 6-4h0z" class="E"></path><path d="M312 558c2-1 4-3 6-4-1 1-2 4-3 5h-2c0 1-1 2-2 2l1-3z" class="B"></path><path d="M311 557c1-1 2-2 3-2l-2 3-1 3c-1 2-3 3-3 5s1 3 1 4h-1c-2-1-1-4-4-4h1l1-1v-1c0-2 2-5 3-6l2-1z" class="l"></path><path d="M334 581v-1c0-1 1-2 2-3l1 2 3 2 2 2v3c-1 0-1 1-2 1h-3c1 1 2 1 3 1v1c-2 0-6 0-8 1 0 2 0 2-2 3l1 2h0-2 0-3v1h-1 0c0-3 0-3-2-5l1-1h1v-1h0v-3l1-1 2-2h0 0l2-2s1 0 1-1c1 0 1 1 2 0l1 1z" class="P"></path><path d="M331 585h2 1l-2 2-4 1s0-1 1-1l2-2z" class="j"></path><path d="M328 583h0l2-2c0 1 0 2 1 2h0c-1 1-2 2-3 2v1c0 1-1 1-1 2l-2 1h0v-3l1-1 2-2h0z" class="G"></path><path d="M328 583h0c-1 1-2 3-2 5h1l-2 1h0v-3l1-1 2-2z" class="f"></path><path d="M325 590h0c1 1 1 1 2 1 2-2 7-3 10-4 1 1 2 1 3 1v1c-2 0-6 0-8 1 0 2 0 2-2 3l1 2h0-2 0-3v1h-1 0c0-3 0-3-2-5l1-1h1z" class="u"></path><path d="M328 592c1-1 3-2 4-2 0 2 0 2-2 3h-1c0-1 0-1-1-1z" class="V"></path><path d="M328 592c1 0 1 0 1 1h1l1 2h0-2 0-3l2-1-1-2h1z" class="Z"></path><path d="M334 581v-1c0-1 1-2 2-3l1 2v1l2 2h1v1h-1l-2 2c-1 1-3 1-4 2h-1l2-2h-1-2 0v-2h0c-1 0-1-1-1-2 0 0 1 0 1-1 1 0 1 1 2 0l1 1z" class="a"></path><path d="M339 582c-1 1-1 1-2 1-1-1-1-1-1-2l1-1 2 2z" class="o"></path><path d="M331 580c1 0 1 1 2 0l1 1c-1 1-2 3-3 4v-2h0c-1 0-1-1-1-2 0 0 1 0 1-1z" class="Z"></path><path d="M320 554c1 2 0 3 0 5v11c-1 3-2 5-2 8v2h0v1l-1-1v-3h-1v2 1c-1 0-1 1-2 1-1-1-1-2-1-3v-1 1c0 1-1 2-1 2h0-1c-1 0-1-1-2-2v-1c-1-2-1-3-1-4v-1c2-3 4-6 6-10 2-2 4-5 6-8z" class="a"></path><path d="M309 577l1-1c1 1 1 2 1 3s0 0 1 1h0-1c-1 0-1-1-2-2v-1zm7 3v-3-1-1c1 0 1-1 1-2h0 0l1 1c0-2 1-3 1-4l-1-1v-1c-1 1-1 2-1 3 0 2-1 3-2 5v-1l-1-1c3-2 2-5 4-7 0 1 0 2 2 3-1 3-2 5-2 8v2h0v1l-1-1v-3h-1v2 1z" class="f"></path><path d="M327 597c1 1 2 2 4 3l4 3v1c0 1 1 1 1 2l-1 2-1 1s0 1-1 1c0 1-1 2-1 2-2 1-3 2-4 4l-1 2-2 1h-2-1v-2-1 1h-1v-4-5c1-2 0-4 0-6h2c2 0 2-1 3-2v-1l1-2z" class="AI"></path><path d="M326 599c2 0 4 1 6 3l1 2h-2-1-1-1c-2 0-3 1-5 0l1-1 1-1v1l1 1v-2l1-1c0 1 0 2 1 2h1v-1c0-1-2-2-3-2v-1z" class="e"></path><path d="M327 597c1 1 2 2 4 3l4 3v1c0 1 1 1 1 2l-1 2-1 1s0 1-1 1v-2-4l-1-2c-2-2-4-3-6-3l1-2z" class="x"></path><path d="M322 608v-5h1v1c2 1 3 0 5 0h1 1 1 2v4 1h-2c-1 1-2 0-3 1 0 1 0 1-1 2l-1-1-2-2h0v-1h-1 0-1z" class="V"></path><path d="M329 604h1c0 1 1 2 2 3v-2c1 1 1 2 0 3h-3c-1-1-1-1-1-2-1 0-1 0-1-1l2-1z" class="P"></path><path d="M322 608h1 0 1v1h0l2 2 1 1c1-1 1-1 1-2 1-1 2 0 3-1h2v-1 2c0 1-1 2-1 2-2 1-3 2-4 4l-1 2-2 1h-2-1v-2-1-4-4z" class="AE"></path><path d="M322 612c0 1 0 2 1 3l3-2h0v3h1 1l-1 2-2 1h-2-1v-2-1-4z" class="AN"></path><path d="M323 574c1 0 1 1 1 2l1 2c0 1 0 2-1 3l1 1v1l1 2-1 1v3h0v1h-1l-1 1c-1 0-1 0-1 1h0c-1-1-1-1-2-1h-1l-1-1c-2 0-3 0-4-1l-3-1c-1 0-2 0-3-1-3 0-5-2-7-1h0c0-2 1-3 1-4 1-1 2-3 4-4h1 1 1c1 1 1 2 2 2h1 0s1-1 1-2v-1 1c0 1 0 2 1 3 1 0 1-1 2-1v-1-2h1v3l1 1v-1 2c1-1 1-1 2 0v-1c1 1 1 2 2 3l1-10z" class="G"></path><path d="M315 581l1 1v2h-1v-2-1z" class="Z"></path><path d="M324 585v-1l1-1 1 2-1 1-1-1z" class="P"></path><path d="M313 578c0 1 0 2 1 3v1 1h-1c-1-1-1-1-1-3h0s1-1 1-2z" class="o"></path><path d="M324 585l1 1v3h0v1h-1-1v-1c0-2 0-3 1-4z" class="I"></path><path d="M318 582c1-1 1-1 2 0v7l-1-1c0-1 0-2-1-3h0c1-1 1-2 0-3z" class="V"></path><path d="M320 581c1 1 1 2 2 3v6h-2v-1-7-1z" class="U"></path><path d="M309 581c1 1 1 2 2 3 1 2 3 3 4 5h-1l-3-1 1-1c0-1-3-2-3-3v-1-2z" class="a"></path><path d="M307 578h1l1 3h0v2 1c0 1 3 2 3 3l-1 1c-1 0-2 0-3-1-3 0-5-2-7-1h0c0-2 1-3 1-4 1-1 2-3 4-4h1z" class="o"></path><path d="M307 578h1l1 3h-1v1c0 1 0 1-1 2l-1-1c0-1 0-2 1-2v-1c0-1 0-1-1-2h1z" class="f"></path><path d="M325 540c3 3 6 8 8 11 1 1 2 2 2 4 1 1 2 1 3 2l3 4-1 1h0l-1 2h0-1 0c-1-1-3-2-4-2h-1c-1-1-1-1 0-2l-2-1c-1 0-1 0-2 1-1 0-1 0-2 1-1-2-2-4-3-5l-1-2c0-1 0-1-1-1s-2 0-2 1h-2 0c-2 1-4 3-6 4l2-3 1-1c1 0 4-5 4-7 0-1 1-2 2-3 1 0 1 0 2 1 1 0 1 1 2 1h0v-1h1s-1-2-2-3h0c0-1-1-1-1-2l2 2v-2z" class="C"></path><path d="M325 546l1 1h-3c-1 0-1-1-2-1v-1h2c1 0 1 1 2 1z" class="F"></path><path d="M322 550v-1h1 1c0 1 1 1 2 1 2 3 3 6 5 8v1c-1 0-1 0-2 1-1 0-1 0-2 1-1-2-2-4-3-5l-1-2c0-1 0-1-1-1s-2 0-2 1h-2l1-3v-1h0c1-1 2-1 3 0z" class="AM"></path><path d="M319 551v-1h0c1-1 2-1 3 0v1h-3z" class="J"></path><path d="M324 556h1l1 1h0 2v2l1 1c-1 0-1 0-2 1-1-2-2-4-3-5z" class="E"></path><path d="M325 540c3 3 6 8 8 11 1 1 2 2 2 4 1 1 2 1 3 2l3 4-1 1h0l-1 2h0-1c0-1-1-1-1-2s-2-4-4-5c-2-3-5-6-7-10 0 0 0-1-1-1v-1h1s-1-2-2-3h0c0-1-1-1-1-2l2 2v-2z" class="L"></path><path d="M335 555c1 1 2 1 3 2l3 4-1 1h0l-5-6v-1z" class="AJ"></path><path d="M325 540c3 3 6 8 8 11 1 1 2 2 2 4v1l-10-14v-2z" class="AO"></path><path d="M320 554c0-1 1-1 2-1s1 0 1 1l1 2c1 1 2 3 3 5 1-1 1-1 2-1 1-1 1-1 2-1l2 1c-1 1-1 1 0 2h1c1 0 3 1 4 2h-2v2c1 1 3 3 3 5-1 0-2-1-2-1-1 1-1 2-1 3-1 1-1 3-2 4 0 1-1 2-1 3-1 1-1 0-2 0 0 1-1 1-1 1l-2 2h0 0l-2 2-1-2v-1l-1-1c1-1 1-2 1-3l-1-2c0-1 0-2-1-2l-1 10c-1-1-1-2-2-3v1c-1-1-1-1-2 0v-2h0v-2c0-3 1-5 2-8v-11c0-2 1-3 0-5h0z" class="a"></path><path d="M325 561c1 0 1 1 2 1v1 2 1h-2v-1-4z" class="i"></path><path d="M326 574v-1c1 0 1 0 2-1v-3-2c1 1 1 1 1 2v7 2c-1-1-1-2-1-3l-1-1h-1z" class="T"></path><path d="M323 574c0-3 0-6 1-9v1c1 0 1 1 0 1 0 3 2 4 0 7 1 1 1 1 0 2 0-1 0-2-1-2z" class="f"></path><path d="M332 568l1 2 1 1v6c0 1-1 2-1 3-1 1-1 0-2 0v-1c0-1 1-2 1-3 1-2 1-3 0-5v-3h0z" class="G"></path><path d="M334 567c1-1 1-1 2-1 1 1 3 3 3 5-1 0-2-1-2-1-1 1-1 2-1 3-1 1-1 3-2 4v-6l-1-1c0-1 1-1 1-1v-2z" class="AG"></path><path d="M326 574h1l1 1c0 1 0 2 1 3l-1 2v3l-2 2-1-2v-1l-1-1c1-1 1-2 1-3v-2s0-1 1-2z" class="I"></path><path d="M325 576l1 2c0 1 0 2-1 4l-1-1c1-1 1-2 1-3v-2z" class="c"></path><path d="M326 574h1l1 1c0 1 0 2 1 3l-1 2c-1-1-1-2-1-3v-1c-1 0-1 1-1 2l-1-2s0-1 1-2z" class="G"></path><path d="M320 554c0-1 1-1 2-1s1 0 1 1h-1c-1 1-1 3-1 4v10c0 3-1 7-1 10v3 1c-1-1-1-1-2 0v-2h0v-2c0-3 1-5 2-8v-11c0-2 1-3 0-5h0z" class="c"></path><path d="M318 580c1-1 1-2 1-3s0-1 1-1v2 3 1c-1-1-1-1-2 0v-2h0z" class="P"></path><path d="M329 560c1-1 1-1 2-1l2 1c-1 1-1 1 0 2h1c1 0 3 1 4 2h-2v2c-1 0-1 0-2 1v2s-1 0-1 1l-1-2c-2-3-3-5-5-7 1-1 1-1 2-1z" class="z"></path><path d="M334 567h-1v-1-1c1 0 2 0 3-1v2c-1 0-1 0-2 1z" class="C"></path><path d="M301 586c2-1 4 1 7 1 1 1 2 1 3 1l3 1c1 1 2 1 4 1l1 1h1c1 0 1 0 2 1h0c0-1 0-1 1-1 2 2 2 2 2 5h0 1l1 1-1 2v1c-1 1-1 2-3 2h-2c0 2 1 4 0 6v5 4h0v3h-2c0 1 0 1-1 1v-2-1-1c-2 1-3 0-4 0-1-3-3-5-5-7h-1c0-1 0-2 1-2v-1c-1-1-2-2-3-2s-1-1-2-1c-1-1-3-3-3-5v-1c-1-2-1-3-1-5v-3-1h1l1-1-1-2z" class="V"></path><path d="M308 594h1l2 2c-1 0-3 0-5 1-1-2-2 0-4 0l-1-2 1-1v1h2c1 0 3 0 4-1z" class="c"></path><path d="M325 596h1l1 1-1 2v1c-1 1-1 2-3 2h-2l-2-1-1-1c-2-1-2 0-3 0l-6 5h0c0-1 1-2 2-3h0c1-1 1-2 2-2h1l1-1 3-1h0l1 1 1-1c1 0 3-2 4-2h1z" class="C"></path><path d="M325 596h1l1 1-1 2v1l-2-1h-1-4l1-1c1 0 3-2 4-2h1z" class="V"></path><path d="M325 596h1l1 1-1 2v1l-2-1v-3h1z" class="B"></path><path d="M315 600c1 0 1-1 3 0l1 1 2 1c0 2 1 4 0 6l-2-1v2s0 1-1 1c0 0-1-1-1-2s1-1 0-2h-2-1v-1c0-2 0-2 1-4v-1z" class="Z"></path><path d="M315 600c1 0 1-1 3 0l1 1h0l-1 1-1 1h-1c0-1 0-1-1-2h0v-1z" class="I"></path><path d="M319 601l2 1c0 2 1 4 0 6l-2-1h0c0-1-1-2 0-3v-3h0z" class="k"></path><path d="M315 595h-1l1-1h1c1 2 0 2-1 4v1l-1 1h-1-3l-1 2c-2 0-3-1-4-2l-2 1c-1-1-1-1-1-2 1-1 3-2 4-2 2-1 4-1 5-1l4-1z" class="h"></path><path d="M315 595h-1l1-1h1c1 2 0 2-1 4v1l-1 1h-1-3l-1 2c-2 0-3-1-4-2l6-1c1-1 4-1 4-3v-1z" class="s"></path><path d="M318 590l1 1h1c1 0 1 0 2 1h0c0-1 0-1 1-1 2 2 2 2 2 5h0-1c-1 0-3 2-4 2l-1 1-1-1h0l-3 1v-1c1-2 2-2 1-4 0-1-1-1-2-2 0-1 1-1 1-1l3-1z" class="T"></path><path d="M320 595c0-1 1-1 2-2l3 3h0-1c-1 0-3 2-4 2-1-1 0-2 0-3z" class="z"></path><path d="M318 590l1 1c-1 1-1 2-1 3v2c1 0 1 0 2-1 0 1-1 2 0 3l-1 1-1-1h0l-3 1v-1c1-2 2-2 1-4 0-1-1-1-2-2 0-1 1-1 1-1l3-1z" class="x"></path><path d="M319 607l2 1v5 4h0v3h-2c0 1 0 1-1 1v-2-1-1c-2 1-3 0-4 0-1-3-3-5-5-7 2 1 3 1 5 3v1h2v-3-2h0l1-1c0 1 1 2 1 2 1 0 1-1 1-1v-2z" class="d"></path><path d="M319 607l2 1v5l-1 1c-1 0-2 1-3 2v1l-1-1-1-1-1-1h2v-3-2h0l1-1c0 1 1 2 1 2 1 0 1-1 1-1v-2z" class="e"></path><path d="M319 609h0 2v3h-1c-1 0-1 0-2-1v-1c1 0 1-1 1-1z" class="V"></path><path d="M301 586c2-1 4 1 7 1 1 1 2 1 3 1l3 1c1 1 2 1 4 1l-3 1s-1 0-1 1c1 1 2 1 2 2h-1l-1 1h1l-4 1-2-2h-1c-1 1-3 1-4 1h-2v-1s1-1 2-1v-2c-1-1-2-1-3-1h0v-1h1-1l1-1-1-2z" class="f"></path><path d="M314 592h0c1 1 2 1 2 2h-1l-1 1h1l-4 1-2-2h-1 3 0c1-1 1-2 3-2z" class="I"></path><path d="M302 589c3 0 5 1 7 1 1 0 2 1 3 1-1 0-2 0-2 1h0c-2 0-2 0-3-1-1 0-1 1-2 0l-1-1v1c-1-1-2-1-3-1h0v-1h1z" class="G"></path><path d="M301 586c2-1 4 1 7 1 1 1 2 1 3 1l3 1c1 1 2 1 4 1l-3 1s-1 0-1 1h0l-2-1c-1 0-2-1-3-1-2 0-4-1-7-1h-1l1-1-1-2z" class="R"></path><path d="M431 256h0 1c-2 3 0 7-1 9v1h1 0s0 1-1 1c1 2 2 4 4 5h1v1h1c0 1 0 2 1 3h1v3 5 49 1 1h1 2l3 1h0c0 1-1 1-2 1v1h1v1c1 3 1 5 1 7 0 1-1 2-3 2l2 2c-1 0-3 1-4 1-3 1-6 1-8 1v1h1 2 1v1h0v1h-5 0c-2 1-4 1-6 1-3 0-7 2-9 1l-5 1-7 1c-2 0-9 1-10 1-2-1-6-1-8-2-1-1-2-1-2-1-1-1-1-1-1-2v-1l2 1 1-1h-2c-1 0-1 0-2-1h-1s-1 0-1 1h-2l-20-1h3-2-5v1l-1-1v1l-2-2h-11c-9 0-18 2-26 0-1 0-1 0-1-1h0-1v-1h0 2l-1-1s0-1 1-1v-1l-2-1v-1-2l1-1c1 0 2 1 3 0v-3c0-4 0-23 1-25h2s1 0 1-1h2c1-1 0-2 1-3v-1c-1-1 0-3 0-3 1-1 0-3 0-4l1-8h0c0-2 0-5 1-6v5 14 6 8c2 3 0 7 2 9v4c1 1 4 1 6 1l20-1 13-2c1 0 3 0 4-1l3-1h1 0c1 0 2 0 3-1s3-1 5-2c3-1 6-3 9-5 1 0 2-1 3-2 4-2 7-6 10-9s12-14 11-16v-1h0 1c2-2 4-7 5-10l1-4 2-6 1-5c1-2 2-4 2-6 1-2 3-4 3-7h0 1z" class="AQ"></path><path d="M435 322v-2h0c0 2-1 7 0 9s1 3 2 4h2v1c-2 0-5 0-7 1v-1l1-1c2-3 2-8 2-11z" class="n"></path><path d="M434 320c0 1 0 1 1 2 0 3 0 8-2 11l-1 1h-1v-1c0-1 1-1 1-2s-1-1-1-2c1-1 3-3 2-5 0-1 1-2 1-4z" class="AD"></path><path d="M434 288c1 2 1 3 1 5v7 20 2c-1-1-1-1-1-2h-1l1-1-1-7c0-2 1-6 0-8 0-1 0-3 1-4v-3h0c0-2 1-5 0-7v-2z" class="H"></path><path d="M324 294h0c0-2 0-5 1-6v5 14 6 8l-1 18h5l-1 1h-5l1-23c0-2 0-5-1-7v-1c-1-1 0-3 0-3 1-1 0-3 0-4l1-8z" class="I"></path><path d="M422 307l12-24v5 2l-7 14c-1 3-4 7-6 10-1 1-1 2-2 3s-3 1-4 2l8-11c0-1-1-1-1-1z" class="Z"></path><path d="M427 304c-1 3-4 7-6 10-1 1-1 2-2 3s-3 1-4 2l8-11h0c0 1-2 3-3 4l1 1v-1c1-1 2-2 2-3l1-1c1-1 1-1 1-2 0 0 1-2 2-2z" class="a"></path><path d="M422 307s1 0 1 1l-8 11c1-1 3-1 4-2l-2 2c-1 2-3 5-5 6-5 3-10 9-17 10l-2 1 1-1h0l1-1c2-1 4-2 6-4h1c1-1 1-2 2-3v-1c0-1 8-7 9-8 4-4 6-8 9-11z" class="P"></path><path d="M415 319c1-1 3-1 4-2l-2 2c-1 2-3 5-5 6-5 3-10 9-17 10 2-1 4-2 6-4 1 0 2-1 3-2 1 0 2-1 3-1l6-6 2-3z" class="j"></path><path d="M415 319c1-1 3-1 4-2l-2 2c-1 1-3 2-4 3l2-3z" class="i"></path><path d="M360 338c7-1 13-3 19-5 3-1 7-3 10-5 12-6 22-15 29-26 3-6 6-13 9-19l3-9c0-2 0-5 1-6v-1c1 2 2 4 4 5h1v1h1c0 1 0 2 1 3h0c-2 0-2 0-4-1-1-1-1-2-2-4-1 4-2 7-3 11-2 5-5 12-8 17-8 14-21 26-36 33-12 5-25 7-38 8h-19l1-1h10 9c4-1 8 0 12-1z" class="a"></path><path d="M325 321c2 3 0 7 2 9v4c1 1 4 1 6 1l20-1 13-2c1 0 3 0 4-1l3-1c-1 3-5 3-7 4-1 1-4 1-5 2h2v1h-2-1v1c-4 1-8 0-12 1h-9-10-5l1-18z" class="g"></path><path d="M312 345h27c13 0 26-1 38-5 10-3 19-8 27-14v1c-1 1-1 2-2 3h-1c-2 2-4 3-6 4l-1 1h0l-1 1c-1 1-2 1-3 2-2 0-3 1-5 2l-3 2c2 1 4 1 7 1h-4l-1 1h-5-2l-5 1c-3 0-7 1-10 2h-3l-37 1c-2 0-6 1-8-1l-2-1v-1z" class="k"></path><path d="M372 345c4-2 6-3 10-3 2 1 4 1 7 1h-4l-1 1h-5-2l-5 1z" class="G"></path><path d="M431 256h0 1c-2 3 0 7-1 9v1h1 0s0 1-1 1v1c-1 1-1 4-1 6l-3 9c-3 6-6 13-9 19-7 11-17 20-29 26-3 2-7 4-10 5-6 2-12 4-19 5v-1h1 2v-1h-2c1-1 4-1 5-2 2-1 6-1 7-4h1 0c1 0 2 0 3-1s3-1 5-2c3-1 6-3 9-5 1 0 2-1 3-2 4-2 7-6 10-9s12-14 11-16v-1h0 1c2-2 4-7 5-10l1-4 2-6 1-5c1-2 2-4 2-6 1-2 3-4 3-7h0 1z" class="p"></path><defs><linearGradient id="n" x1="360.069" y1="346.435" x2="360.151" y2="348.854" xlink:href="#B"><stop offset="0" stop-color="#f5e2af"></stop><stop offset="1" stop-color="#faf6dc"></stop></linearGradient></defs><path fill="url(#n)" d="M405 343h0 0c0 1-1 1-1 1l1 1h-3c2 1 2 1 3 0 0 1-1 1-1 2h-1-2s1 0 2 1c-1 1-3 2-5 3h-3 0c-1 1-2 1-4 1s-7 0-9 1h-1s-1 0-1 1h-2l-20-1h3-2-5v1l-1-1v1l-2-2h-11c-9 0-18 2-26 0-1 0-1 0-1-1h0-1v-1h0 2l-1-1s0-1 1-1v-1c2 2 6 1 8 1l37-1h3c3-1 7-2 10-2l5-1h2 5 1l6 1h1l8-1c2 0 3-1 5-1z"></path><path d="M378 345h3v1h1-3-1v-1z" class="AC"></path><path d="M379 344h5 1l6 1-9 1h-1v-1h-3-1v-1h2z" class="j"></path><path d="M377 344h2 3l-1 1h-3-1v-1z" class="b"></path><path d="M372 345l5-1v1h1v1h1c-7 1-13 1-20 1h3c3-1 7-2 10-2z" class="O"></path><path d="M314 350c2 1 7 1 9 1l31-1h0c-2 1-5 1-7 1h-4-5c1 0 7 1 8 0h1c2 1 4 0 6 0 0 0 0 1 1 1h-3-11c-9 0-18 2-26 0-1 0-1 0-1-1h0-1v-1h0 2z" class="Z"></path><path d="M401 347s1 0 2 1c-1 1-3 2-5 3h-3 0c-1 1-2 1-4 1s-7 0-9 1h-1s-1 0-1 1h-2l-20-1h3-2-5v1l-1-1v1l-2-2h3c-1 0-1-1-1-1-2 0-4 1-6 0h-1c-1 1-7 0-8 0h5 4c2 0 5 0 7-1h0l47-3z" class="k"></path><path d="M354 352c4 1 9 0 13 0l28-1c-1 1-2 1-4 1s-7 0-9 1h-1s-1 0-1 1h-2l-20-1h3-2-5v1l-1-1v1l-2-2h3z" class="j"></path><defs><linearGradient id="o" x1="405.614" y1="313.396" x2="416.473" y2="327.818" xlink:href="#B"><stop offset="0" stop-color="#f6e8b8"></stop><stop offset="1" stop-color="#fffde5"></stop></linearGradient></defs><path fill="url(#o)" d="M434 290c1 2 0 5 0 7h0v3c-1 1-1 3-1 4 1 2 0 6 0 8l1 7-1 1h1c0 2-1 3-1 4 1 2-1 4-2 5-3 3-6 5-9 7s-7 4-11 5c-2 1-4 1-6 2h0c-2 0-3 1-5 1l-8 1h-1l-6-1h-1l1-1h4c-3 0-5 0-7-1l3-2c2-1 3-2 5-2 1-1 2-1 3-2l2-1c7-1 12-7 17-10 2-1 4-4 5-6l2-2c1-1 1-2 2-3 2-3 5-7 6-10l7-14z"></path><path d="M412 325c-1 5-7 9-11 11-3 1-7 3-11 3l-1 1h-1-1-2c2-1 3-2 5-2 1-1 2-1 3-2l2-1c7-1 12-7 17-10z" class="p"></path><path d="M434 297h0v3c-1 1-1 3-1 4 1 2 0 6 0 8l1 7-2-2c-1-1-2-1-3-1s-2 0-3 2v1h0c-1 3-2 4-4 6l-5 6c-1 1-4 2-5 4h-1l-1-1c1-1 3-2 4-3 1-2 2-5 4-7 5-8 12-17 16-27z" class="c"></path><path d="M433 304c1 2 0 6 0 8l-1-1c-1 1-2 1-3 1 2-2 3-5 4-8z" class="K"></path><path d="M429 312c1 0 2 0 3-1l1 1 1 7-2-2c-1-1-2-1-3-1s-2 0-3 2v1l-1-1c1-2 3-4 4-6z" class="n"></path><path d="M425 318l1 1h0c-1 3-2 4-4 6l-5 6-2-1c0-1 2-2 3-4l7-8z" class="AB"></path><path d="M426 319v-1c1-2 2-2 3-2s2 0 3 1l2 2-1 1h1c0 2-1 3-1 4 1 2-1 4-2 5-3 3-6 5-9 7s-7 4-11 5c-2 1-4 1-6 2h0c-2 0-3 1-5 1l-8 1h-1l-6-1h-1l1-1h4l6-2c1 0 5-1 6-2 3-1 6-3 9-5l1 1h1c1-2 4-3 5-4l5-6c2-2 3-3 4-6h0z" class="e"></path><path d="M389 343l6-2 1 1h-1c-1 0-2 1-3 1h0c-2 1-5 1-7 1h-1l1-1h4z" class="R"></path><path d="M405 343c-1-1-1-1-2-1 1-2 6-4 7-4 4-1 6-5 9-6 0 1 0 2 1 2 0 1 0 1-1 1l-4 2-1 1c2 1 6-2 8-3v1c-3 2-7 4-11 5-2 1-4 1-6 2h0z" class="s"></path><path d="M426 319v-1c1-2 2-2 3-2s2 0 3 1l2 2-1 1h1c0 2-1 3-1 4 1 2-1 4-2 5-3 3-6 5-9 7v-1c-2 1-6 4-8 3l1-1 4-2c1 0 1 0 1-1-1 0-1-1-1-2l3-2 1-2c0-1 0-1-1-3 2-2 3-3 4-6h0z" class="U"></path><path d="M423 328l1-1c0 1 0 2 1 3-1 1-2 2-3 2h-1l1-2 1-2z" class="AD"></path><path d="M428 321v1c0 1-1 1 0 2s1 0 2 0h1l-1 2c-1 1-2 2-3 2 0 1-1 2-2 2-1-1-1-2-1-3 1-2 3-3 4-6z" class="O"></path><path d="M426 319v-1c1-2 2-2 3-2s2 0 3 1l-1 1c-1-1-1-1-2-1-1 1-1 2-2 3l1 1c-1 3-3 4-4 6l-1 1c0-1 0-1-1-3 2-2 3-3 4-6h0zm7 1h1c0 2-1 3-1 4 1 2-1 4-2 5-3 3-6 5-9 7v-1c2-1 4-3 6-5l1-1c1-1 1-2 1-3l1-2c1-1 2-3 2-4z" class="I"></path><path d="M432 317l2 2-1 1c0 1-1 3-2 4h-1c-1 0-1 1-2 0s0-1 0-2v-1l-1-1c1-1 1-2 2-3 1 0 1 0 2 1l1-1z" class="r"></path><path d="M432 317l2 2-1 1c0 1-1 3-2 4h-1c0-2 1-3 2-5l-1-1 1-1z" class="K"></path><path d="M431 329c0 1 1 1 1 2s-1 1-1 2v1h1v1c2-1 5-1 7-1v1h1 2l3 1h0c0 1-1 1-2 1v1h1v1c1 3 1 5 1 7 0 1-1 2-3 2l2 2c-1 0-3 1-4 1-3 1-6 1-8 1v1h1 2 1v1h0v1h-5 0c-2 1-4 1-6 1-3 0-7 2-9 1l-5 1-7 1c-2 0-9 1-10 1-2-1-6-1-8-2-1-1-2-1-2-1-1-1-1-1-1-2v-1l2 1 1-1h-2c-1 0-1 0-2-1 2-1 7-1 9-1s3 0 4-1h0 3c2-1 4-2 5-3-1-1-2-1-2-1h2 1c0-1 1-1 1-2-1 1-1 1-3 0h3l-1-1s1 0 1-1h0c2-1 4-1 6-2 4-1 8-3 11-5s6-4 9-7z" class="v"></path><path d="M428 344l7-2 2 4c0 1-1 1-2 2 0 0-1 0-2 1h-2c-1 1-2 1-3 1l3-4v-1h0c-1-1-2-1-3-1z" class="K"></path><path d="M428 344c1 0 2 0 3 1h0v1l-3 4h-3c-1 1-2 1-4 1 2-1 3-2 5-3l-5 1-1-1h1c-1-1-1-1-2-1h-1v-1h1c3 0 6-1 9-2z" class="H"></path><path d="M442 348h0l2 2c-1 0-3 1-4 1-3 1-6 1-8 1l-8 1h-2-2v-1l1-1c2 0 3 0 4-1h3c1 0 2 0 3-1h2c1 0 1 1 2 1l7-2z" class="T"></path><path d="M421 351c2 0 3 0 4-1l1 1h2l-4 2h-2-2v-1l1-1z" class="O"></path><path d="M428 350c1 0 2 0 3-1h2c1 0 1 1 2 1l-7 1h-2l-1-1h3z" class="r"></path><path d="M432 335c2-1 5-1 7-1v1h1 2l3 1h0c0 1-1 1-2 1v1h1v1c1 3 1 5 1 7 0 1-1 2-3 2h0c0-2-1-3 0-5v-4-1h0c-2 0-4 0-6 1-6 2-11 4-17 7h-1v1h1c1 0 1 0 2 1h-1l1 1c-3 1-8 3-11 2v-1c-1 0-1 0-2 1h-3 0c0-1 1-1 2-1v-1h0 0c2 0 3-1 5-1l16-8v-1c1 0 2-1 3-2v-1h2 1-2v-1z" class="j"></path><path d="M412 348h1c-2 3-5 2-8 3h0c0-1 1-1 2-1v-1h0 0c2 0 3-1 5-1z" class="a"></path><path d="M432 335c2-1 5-1 7-1v1h1 0 2v1l-14 4v-1c1 0 2-1 3-2v-1h2 1-2v-1z" class="v"></path><path d="M432 335c2-1 5-1 7-1v1h1 0c-2 0-4 0-6 1h-2v-1z" class="AD"></path><path d="M431 329c0 1 1 1 1 2s-1 1-1 2v1h1v1 1h2-1-2v1c-1 1-2 2-3 2v1l-16 8c-2 0-3 1-5 1h0-4v-1c-1-1-2-1-2-1h2 1c0-1 1-1 1-2-1 1-1 1-3 0h3l-1-1s1 0 1-1h0c2-1 4-1 6-2 4-1 8-3 11-5s6-4 9-7z" class="K"></path><path d="M431 329c0 1 1 1 1 2s-1 1-1 2v1h1v1 1l-1-1c-1-1-1-2 0-3v-1l-2 2c-1 2-3 2-4 3s-1 2-3 3l-2 2h-1c-2 0-4 2-6 3s-4 1-6 1c-1 1-2 2-4 2h1c0-1 1-1 1-2-1 1-1 1-3 0h3l-1-1s1 0 1-1h0c2-1 4-1 6-2 4-1 8-3 11-5s6-4 9-7z" class="G"></path><path d="M403 348v1h4 0v1c-1 0-2 0-2 1h0 3c1-1 1-1 2-1v1c3 1 8-1 11-2l5-1c-2 1-3 2-5 3l-1 1v1h2 2l8-1v1h1 2 1v1h0v1h-5 0c-2 1-4 1-6 1-3 0-7 2-9 1l-5 1-7 1c-2 0-9 1-10 1-2-1-6-1-8-2-1-1-2-1-2-1-1-1-1-1-1-2v-1l2 1 1-1h-2c-1 0-1 0-2-1 2-1 7-1 9-1s3 0 4-1h0 3c2-1 4-2 5-3z" class="j"></path><path d="M392 356h5c1 1 1 1 2 1l-4 1h-2c-1-1-1-1-1-2z" class="AA"></path><path d="M405 356h3 0v1h-4-5c-1 0-1 0-2-1h8z" class="R"></path><path d="M395 353c3 0 6-1 9-1h3c1 1 2 1 3 2-2 0-14 1-16 0l1-1z" class="f"></path><path d="M421 349l5-1c-2 1-3 2-5 3l-1 1c-4 1-7 2-10 2-1-1-2-1-3-2 1 0 2 0 3-1 3 1 8-1 11-2z" class="T"></path><path d="M394 360c-2-1-6-1-8-2-1-1-2-1-2-1-1-1-1-1-1-2v-1l2 1c1 1 3 1 5 1 0-1 0-1 1-1l1 1c0 1 0 1 1 2h2l4-1h5c1 0 3 0 4 1h3l-7 1c-2 0-9 1-10 1z" class="f"></path><path d="M404 357c1 0 3 0 4 1h3l-7 1c-1 0-3-1-4-1h-5l4-1h5z" class="c"></path><path d="M403 348v1h4 0v1c-1 0-2 0-2 1h0 3c1-1 1-1 2-1v1c-1 1-2 1-3 1h-3c-3 0-6 1-9 1h-4l-5 1h-2c-1 0-1 0-2-1 2-1 7-1 9-1s3 0 4-1h0 3c2-1 4-2 5-3z" class="K"></path><path d="M403 348v1h4 0v1c-1 0-2 0-2 1h-10 3c2-1 4-2 5-3z" class="f"></path><defs><linearGradient id="p" x1="422.82" y1="357.936" x2="418.529" y2="350.736" xlink:href="#B"><stop offset="0" stop-color="#8d7350"></stop><stop offset="1" stop-color="#a58b5a"></stop></linearGradient></defs><path fill="url(#p)" d="M424 353l8-1v1h1 2 1v1h0v1h-5 0c-2 1-4 1-6 1-3 0-7 2-9 1l-5 1h-3c-1-1-3-1-4-1h4v-1h0-3 0c6-1 12-1 17-3h2z"></path><path d="M408 357c1 0 3-1 4 0s3 0 4 0l-5 1h-3c-1-1-3-1-4-1h4z" class="e"></path><path d="M148 425h2 6l-3 1h0v1h3v2c0 1 0 1 1 1 2-1 3 0 4-1h1c1 0 1 0 2-1l1-1h1s1 0 1-1c2 0 4 1 5 1l10 4c2 0 3 1 4 2l8 4h0c7 4 15 7 22 12 1 1 3 2 5 4v1 2c1 2 3 3 4 5 0 0 1 1 1 2 2 1 3 3 4 4v2h1v4c0 5 0 11-1 17v6c0 5 0 10-1 14l-1 3c0 2-1 6-2 9v-2c-2-1-3-1-5-1-2 1-3 2-4 4l-1-1c-1-1-1-2-2-2-2 3-3 6-6 7l-1 1c-1-1-2-2-4-2h-2v1c-1-1 0-1-1-2l-2 2v2l-1 1h-1 0v1 1h1 0 0c-2 2-3 2-5 3-1 0-1 1-2 2h0l-1-1c-1 0-2 1-2 1l-1 1c-1 1-1 1 0 2v2c0 1-1 2 0 3 0 1 2 3 3 3 1 1 3 3 3 5v1l-3-2-3-1c-3-1-6-2-8-3s-3-3-5-4h0l-2 1c2 1 5 2 6 4h0-2c0-1-1-2-2-2h-1c-1 0-2-1-2-1-2-1-4-2-5-3l-3-3c-1 0-3-2-4-3 1 2 3 4 4 5v1 1c1 1 3 2 4 3h-1c-1-1-2-1-3-2-4-1-7-3-10-3h-1c-1 0-4-1-5-1-3-1-5-1-8-1l-6-2h1c-1-1-1-1-2-1h6l-6-2-6-1c0-1-1-1-2-2v-1h-1c1 0 1 0 1-1h0c-6-3-12-8-18-12-5-5-9-10-13-16-11-17-8-39 6-54 1-2 3-3 5-5l5-3c0-1 1-1 2-2v-1h0c1 0 1 1 1 1h2l3-1h1c1 1 1 1 2 1v-1c2-2 3-3 5-4l2-1c1-1 4-3 6-3h2l1 1c1 0 2 0 2-1h4v-1h0l1-1h-2v-1l8-1z" class="AR"></path><path d="M149 483h-2v-1c1 0 2-1 3-1-1 0-1 1-1 2h0z" class="L"></path><path d="M150 481l1-1v3h0-2 0c0-1 0-2 1-2z" class="l"></path><path d="M115 451h1c1 1 1 1 1 2l-1 1h-1v-3z" class="q"></path><path d="M153 467h3c1 0 1 1 2 2l-1 1c-1 0-1-1-2-2h-2v-1z" class="L"></path><path d="M115 451v3h1-1c-1 0-3 0-4-1 0-1 0-1 1-1s2 0 2-1h1z" class="z"></path><path d="M130 481c0 2 1 4 1 6v1 1c1-1 0-2 1-3v-1 8l-1 1h0l-1-1h1v-2c-1-2-1-4-1-6-1-1 0-2 0-4zm-2-26v-2c0-1 1-4 2-5h1c2 1 1 2 2 3-2 0-2 0-3 2l-2 2z" class="J"></path><path d="M171 463c1-1 1-2 2-2h2c0-2 1-2 3-3 1 0 1-1 2 0 1 0 1 0 1-1 1 0 2-1 3-1l-1 1-2 1c-2 2-5 3-8 4h0l-2 1z" class="L"></path><path d="M126 432v1l2 2s1 0 1 1h1s0 1 1 1c0 1 0 1 1 1 0 1 1 2 1 3-3-1-6-5-9-8l2-1z" class="C"></path><path d="M132 510c1 1 1 2 2 3l1 1-1 1h-2c-2-1-3-3-4-4h4v-1z" class="Q"></path><path d="M132 510c1 1 1 2 2 3l1 1-1 1c-1 0-2-2-2-3v-1-1z" class="AI"></path><path d="M133 451c1 1 2 1 2 2s0 1-1 1-2 0-3 1v1l-1 1h-2v-2l2-2c1-2 1-2 3-2z" class="U"></path><path d="M163 471h1v1s0 1 1 1h-1v1 1h-1l-1 1h0c1 1 1 1 1 2h0-1-2c-1 0-1 0-1 1s0 2-1 3v1-4c0-1 1-2 1-3v-1h0c0-1 1-2 1-3h0c1 0 2-1 3-1z" class="l"></path><path d="M127 502l-3-4s1 0 1-1-1-1-2-2c-1 0-1 0-1-1 1 0 2 1 3 1h2c1 1 1 1 2 3l-1 1c1 1 2 1 2 3l-1 1c-1-1-1-1-2-1h0z" class="C"></path><path d="M123 530l15 6h-2s-1-1-2-1h-3l-6-1c0-1-1-1-2-2v-1h-1c1 0 1 0 1-1h0z" class="D"></path><path d="M128 511c-1-1-2-1-3-2-1 0-1-1-1-1v-2c1-1 2-1 3-1s2 1 3 2h0c-1 1-1 0 0 1h0c1 1 2 1 2 2v1h-4z" class="t"></path><path d="M130 457l1-1c1 1 1 2 1 4 1 1 0 3 0 4l-3 10c0-3 1-7 0-9v-6c1 0 1-1 1-2z" class="F"></path><path d="M122 484h0 1c0 1 1 1 2 2v2c1 1 1 2 1 3l-1-1c0 1 1 2 1 3v1c-2-2-3-4-6-5-1 1-2 2-3 2h-1v-1-1c1-1 1-2 3-3 1 0 2 1 3 1 0 1 1 1 1 1 0-1-1-3-1-4z" class="m"></path><path d="M183 446h0c2 0 3 0 4-1-1 1-1 2-1 3v1c-4 1-6 2-9 5 1 0 2 0 3 1-3 1-5 1-8 2 2-3 5-6 7-8 1-1 3-2 4-3h0z" class="w"></path><path d="M148 425h2 6l-3 1h0v1h3v2c-5-2-11-1-15 0v-1h0l1-1h-2v-1l8-1z" class="k"></path><path d="M148 425h2 6l-3 1h0-3c-2 0-6 1-8 1h-2v-1l8-1z" class="AJ"></path><path d="M155 439c2-2 3-3 5-4h1c0 1 1 1 1 1 0 2-1 2-2 3-1 3-3 6-6 8 0 1-1 2-2 2 0 1-1 1-2 0 2-3 5-5 7-8 0-1 1-2 1-3h-1s-1 0-2 1z" class="L"></path><path d="M119 472c0 1 0 2 1 3h1c0 1 1 1 1 2h1v1h1c1 0 1 1 1 2h0v8-2c-1-1-2-1-2-2h-1 0c-2-3-4-5-4-8 1-2 0-3 1-4z" class="X"></path><path d="M119 472c0 1 0 2 1 3h1c0 1 1 1 1 2h-1l1 1h2 0c0 1-1 1-1 2 1 1 1 2 2 3h-1c-2-1-2-3-3-4 0-1-2-2-3-3 1-2 0-3 1-4z" class="N"></path><path d="M131 535h3c1 0 2 1 2 1h2c2 0 3 1 5 1v1c1 1 1 1 2 1l1 1 6 2h-1c-1 0-4-1-5-1-3-1-5-1-8-1l-6-2h1c-1-1-1-1-2-1h6l-6-2z" class="j"></path><path d="M131 535h3c1 0 2 1 2 1h2c2 0 3 1 5 1v1c1 1 1 1 2 1l1 1-9-3-6-2z" class="W"></path><path d="M140 474c0-1 1-2 1-3s1-2 1-3c3-5 5-11 8-15h1c0 1 1 1 0 2-1 2-2 4-3 5-3 5-6 10-7 15 0 2-1 3 0 4h0c-1 1-1 2-1 4l-1-5c0-1 1-2 1-4z" class="L"></path><path d="M121 467c1-1 1-1 1-2l1 1c1 1 1 3 1 4h0c1 1 1 1 1 2v4 4h0c0-1 0-2-1-2h-1v-1h-1c0-1-1-1-1-2h-1c-1-1-1-2-1-3v-1l1-1c1 0 1-1 1-1v-2z" class="J"></path><path d="M121 467c1-1 1-1 1-2l1 1c1 1 1 3 1 4v2h0c-2-1-3-3-3-5z" class="V"></path><path d="M120 470l3 3c0 1 1 2 1 3s-1 1-1 1h-1c0-1-1-1-1-2h-1c-1-1-1-2-1-3v-1l1-1z" class="w"></path><path d="M120 470l3 3c-1 0-2 1-3 1 0-1-1-2-1-3l1-1z" class="AK"></path><path d="M123 466h1l1 1h1c2-4 0-6 1-9l1-1h2c0 1 0 2-1 2v6c1 2 0 6 0 9 0 2 0 5 1 7 0 2-1 3 0 4 0 2 0 4 1 6v2h-1l-1-7c0-2-2-17-4-18h0l-1 2h0c0-1 0-3-1-4z" class="B"></path><path d="M146 528l7 8c2 2 4 2 5 5l-8-3c-3-1-5-3-7-5-1-1-3-2-4-3 1-3 4-1 6-1 0 0 0-1 1-1z" class="C"></path><path d="M143 533h2l5 5h0c-3-1-5-3-7-5z" class="L"></path><path d="M167 426c2 0 4 1 5 1l10 4c2 0 3 1 4 2l8 4 3 3v1c-2 0-2-1-3 0h0c-3-1-5-3-7-5-6-3-12-5-18-8-1 0-2 0-3-1 0 0 1 0 1-1z" class="F"></path><path d="M124 470l1-2h0c2 1 4 16 4 18l1 7 1 1h0c0 2 1 4 1 5v1l1 2h-2c0-2-2-3-2-4-1-2-1-2-2-3h-2l1-1v-1c0-1-1-2-1-3l1 1c0-1 0-2-1-3v-8-4-4c0-1 0-1-1-2z" class="S"></path><defs><linearGradient id="q" x1="194.001" y1="447.529" x2="180.018" y2="456.281" xlink:href="#B"><stop offset="0" stop-color="#434443"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#q)" d="M187 445h5 3-3v2c1 1 1 2 2 2s3 1 4 2v1h1-2c-1 0-4 0-4 1h-2l-1 1c-2-1-7 0-9 0l-1 1c-1-1-2-1-3-1 3-3 5-4 9-5v-1c0-1 0-2 1-3z"></path><path d="M187 445h5s0 1-1 2c0 1-2 2-2 2h-3v-1c0-1 0-2 1-3z" class="Q"></path><path d="M181 454h-1c1-1 3-2 4-2 3-1 6-2 8-1l-1 2-1 1c-2-1-7 0-9 0z" class="w"></path><defs><linearGradient id="r" x1="147.558" y1="495.477" x2="155.953" y2="502.979" xlink:href="#B"><stop offset="0" stop-color="#29231a"></stop><stop offset="1" stop-color="#463824"></stop></linearGradient></defs><path fill="url(#r)" d="M151 483l1 1h0v5h0v-1c0-1 0-1 1-1v-3h1v3l1 4h-1l1 4-1 1v3 1l2 4-1 2-1 1c-1-1-1-2-2-3h0c-1-1-1 0-1-1h-2v-2c0-1-1-2-1-4v-7c-1-2-1-3 0-4 2 1 1 3 1 5v1c-1 0 0-4-1-5v2 4h1 0l1 1v-2c1-3 1-6 1-9z"></path><path d="M152 504l1-2h0c1 1 1 3 2 4l-1 1c-1-1-1-2-2-3z" class="u"></path><path d="M152 495c-1-1-1-2-1-3 0-2 1-3 2-4 0 2 0 5-1 7z" class="E"></path><path d="M153 488s0-1 1-1l1 4h-1 0c-1 1-1 1-1 2v1h0v3l-1-2c1-2 1-5 1-7z" class="AL"></path><path d="M153 497v-3h0v-1c0-1 0-1 1-2h0l1 4-1 1v3l-1-2z" class="u"></path><path d="M194 437c7 4 15 7 22 12 1 1 3 2 5 4v1c-5-4-11-7-17-9-2 1-3 1-4 1l-5-1h-3-5c-1 1-2 1-4 1h0l2-2c1-1 4-1 5-1h1c1-1 2 0 3-2h0c1-1 1 0 3 0v-1l-3-3h0z" class="J"></path><path d="M191 443c4 0 8 1 13 2-2 1-3 1-4 1l-5-1h-3-5c-1 1-2 1-4 1h0l2-2c1-1 4-1 5-1h1z" class="a"></path><path d="M146 440l2-1v-1c2-1 5-2 7-2v1s0 1-1 2c0 0 0 1-1 1l2-1h0c1-1 2-1 2-1h1c0 1-1 2-1 3-2 3-5 5-7 8-3 5-6 9-8 14v-1-2-1c1-1 0-1 0-1 0-1 2-4 2-5l-1-1c1-2 3-5 4-8-2 2-3 4-5 7 0 1-1 1-1 2l-1 1c1-3 1-5 2-8v-1c1-2 3-3 4-5h0z" class="X"></path><path d="M144 453c1-2 3-6 5-7h0l-7 14v-1c1-1 0-1 0-1 0-1 2-4 2-5z" class="Y"></path><path d="M139 518v-1l1-1 1 3c5 5 10 11 15 16v-2l-1-2c1 1 1 2 2 2h1l4 4c4 2 6 6 9 8 2 1 5 2 6 4h0-2c0-1-1-2-2-2h-1c-1 0-2-1-2-1-2-1-4-2-5-3l-3-3c-1 0-3-2-4-3 1 2 3 4 4 5v1c-2-1-3-2-4-2-1-3-3-3-5-5l-7-8c-1-1-3-2-4-3-2-1-3-3-5-4s-3-2-4-4c1-1 1 0 3 0 1 0 2 0 3 1z" class="X"></path><path d="M136 517c1 0 2 0 3 1h0v1h-2c-1-1-1-1-1-2z" class="W"></path><path d="M139 518v-1l1-1 1 3v1l-2-2h0z" class="M"></path><path d="M155 531c1 1 1 2 2 2h1l4 4 1 2c-3-1-5-4-7-6l-1-2z" class="Y"></path><path d="M162 537c4 2 6 6 9 8 2 1 5 2 6 4h0-2c0-1-1-2-2-2l-7-5-1-1c-1 0-1-1-2-2l-1-2zm-21-18c5 5 10 11 15 16 1 0 2 1 2 2-2 0-3-2-4-2-2-2-4-4-5-6-3-3-6-6-8-9v-1z" class="S"></path><path d="M204 445c6 2 12 5 17 9v2c1 2 3 3 4 5 0 0 1 1 1 2s1 2 2 3c-1 0-2-2-3-2h0l-2-1c0-1-1-1-2-1h0c-1-1-2-1-2-1l-1-1c-1-1-2-2-4-3s-4-1-6-2c-1-1-2-1-2-1-3-1-5-1-7-2h-1v-1c-1-1-3-2-4-2s-1-1-2-2v-2h3l5 1c1 0 2 0 4-1z" class="L"></path><path d="M204 450c1 1 3 1 4 3h1l-1-1v-1c1-1 1-1 2-1l1 1c-1 1-1 1-1 3-2 0-2-1-4-2h-3l1-2z" class="J"></path><path d="M204 445c6 2 12 5 17 9v2c-6-5-12-8-20-10h-1c1 0 2 0 4-1z" class="G"></path><path d="M195 445l5 1h1c-1 1-1 1-1 2 1 0 2 1 4 2l-1 2h3c2 1 2 2 4 2s5 1 7 2h1c2 1 3 3 4 5l3 3h0l-2-1c0-1-1-1-2-1h0c-1-1-2-1-2-1l-1-1c-1-1-2-2-4-3s-4-1-6-2c-1-1-2-1-2-1-3-1-5-1-7-2h-1v-1c-1-1-3-2-4-2s-1-1-2-2v-2h3z" class="B"></path><defs><linearGradient id="s" x1="147.083" y1="477.722" x2="136.931" y2="501.14" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#4a4a49"></stop></linearGradient></defs><path fill="url(#s)" d="M145 474c1-1 2-1 3-1h0v1 1c0 1-1 1-1 2v2h0c-1 3-1 6-3 9h0c1 1 1 1 0 2h0v2c0 3 0 5 1 8h0l-1 1v2l-1 2h0l-2 2-2 1s-1 0-1-1h0c0-1 0-2-1-3-1 0-1-1-1-2v-1c0-1-1-3-1-5l1-2h1v-5c0-3 1-7 2-11h0l1 5c0-2 0-3 1-4 1-2 2-3 4-5z"></path><path d="M140 505v-2h1c0 1 1 2 1 2h1l-2 2-1-2z" class="N"></path><path d="M145 474c1-1 2-1 3-1h0v1 1h-1l-1 1c-1 0-2 1-2 2h-1c1-1 1-2 2-3h0v-1zm-3 17v-2c1-1 1-3 1-5 1-1 1-3 2-4 0-1 0-1 2-1-1 3-1 6-3 9h0c1 1 1 1 0 2h0l-2 1z" class="J"></path><path d="M142 491l2-1v2c0 3 0 5 1 8h0l-1 1v2l-1 2h0-1v-14z" class="S"></path><path d="M144 501v-1-1c-1-2-1-4-1-7h1c0 3 0 5 1 8h0l-1 1z" class="M"></path><defs><linearGradient id="t" x1="144.665" y1="489.561" x2="132.399" y2="497.861" xlink:href="#B"><stop offset="0" stop-color="#1d1b1e"></stop><stop offset="1" stop-color="#3f413d"></stop></linearGradient></defs><path fill="url(#t)" d="M139 478h0l1 5c-2 5-2 13 0 18v4l1 2-2 1s-1 0-1-1h0c0-1 0-2-1-3-1 0-1-1-1-2v-1c0-1-1-3-1-5l1-2h1v-5c0-3 1-7 2-11z"></path><g class="D"><path d="M136 501c0-1-1-3-1-5l1-2h1s1 1 1 2c-1 1-1 3 0 5l-1 1-1-1z"></path><path d="M136 488c1 0 1 1 1 1v5h-1l-1 2c0 2 1 4 1 5v1c0 1 0 2 1 2 1 1 1 2 1 3h0c0 1 1 1 1 1l2-1 2-2h0l1-2v-2l1-1 1 2 3-1v2l-2 1v3 1h-1l-1 2h-1l-1 1h0c0 1 0 2-1 3v2l5 6c3 4 5 7 8 9l1 2v2c-5-5-10-11-15-16l-1-3-1 1v1c-1-1-2-1-3-1-2 0-2-1-3 0-1-1-1-1-1-2h2l1-1-1-1c-1-1-1-2-2-3 0-1-1-1-2-2h0c-1-1-1 0 0-1h0c-1-1-2-2-3-2 1 0 1 0 1-1l-1-2h0c1 0 1 0 2 1l1-1c0-2-1-2-2-3l1-1c0 1 2 2 2 4h2l-1-2v-2l1-1c0-2 0-4-1-6h1 1l2 1v-2-2z"></path></g><path d="M134 491l2 1c-1 2-1 4-1 5-1-2-1-4-1-6z" class="l"></path><path d="M142 516c0-1-1-2-2-3l3-2h0c0 1 0 2-1 3v2z" class="J"></path><path d="M132 500v-2l1-1c0 2 1 4 1 6l-1-1-1-2z" class="F"></path><path d="M136 488c1 0 1 1 1 1v5h-1l-1 2c0 2 1 4 1 5v1c-1-2-1-3-1-5 0-1 0-3 1-5v-2-2z" class="X"></path><path d="M133 502l1 1 3 7c-3-2-4-5-6-8h2z" class="L"></path><path d="M143 505h1c0 1 0 1-1 2h0c-1 1-1 2-1 3v1h-1 0l-2-2v-1h0l2-1 2-2h0z" class="Q"></path><path d="M134 513h1c1 0 2 3 4 3l-3-3 1-1c2 0 2 3 3 4l-1 1v1c-1-1-2-1-3-1-2 0-2-1-3 0-1-1-1-1-1-2h2l1-1-1-1z" class="B"></path><path d="M144 501l1-1 1 2 3-1v2l-2 1v3 1h-1l-1 2h-1l-1-3c1-1 1-1 1-2h-1l1-2v-2z" class="D"></path><path d="M149 501v2l-2 1c-1-1-1-1-1-2l3-1z" class="B"></path><path d="M144 503c1 1 1 1 1 2s-1 2 0 3h1l-1 2h-1l-1-3c1-1 1-1 1-2h-1l1-2z" class="N"></path><path d="M132 485v-2c0-1 1-2 0-3 0-1 1-3 0-5-1 0-1-2 0-3 0-6 3-12 5-18l3-9c1-2 3-4 5-6 2-1 6-4 9-4-5 2-9 4-12 8v1c-3 3-4 8-5 13 1-1 1-2 2-4h0 1v-1c0-2 0-4 1-5h0v-1l1-1v-1c0-1 3-4 4-4h0c-1 2-3 3-4 5v1c-1 3-1 5-2 8l1-1c0-1 1-1 1-2 2-3 3-5 5-7-1 3-3 6-4 8l1 1c0 1-2 4-2 5 0 0 1 0 0 1v1 2 1l-2 8v3c0 2-1 3-1 4h0c-1 4-2 8-2 11 0 0 0-1-1-1v2 2l-2-1h-1-1c1 2 1 4 1 6l-1 1v2-1c0-1-1-3-1-5l1-1v-8z" class="C"></path><path d="M140 471v3c0 2-1 3-1 4h0l1-7z" class="Y"></path><path d="M136 490h-1v-4c-1 1-1 2-1 3h-1v-2c1-1 1-1 1-2v-12 4h1v-4c1 2 1 5 1 7v8 2z" class="J"></path><g class="S"><path d="M134 473c1-5 1-11 4-16 0-1 1-2 1-3-1 7-3 13-4 19v4h-1v-4z"></path><path d="M142 451c2-3 3-5 5-7-1 3-3 6-4 8l1 1c0 1-2 4-2 5 0 0 1 0 0 1v1 2c-3 3-3 6-4 9 0 1 0 3-1 4 0-2 0-5 1-7 0-2 1-4 1-6s0-3 1-5l2-6z"></path></g><path d="M149 503h2c0 1 0 0 1 1h0c1 1 1 2 2 3l2 5c2 3 4 6 6 8 1 1 2 2 2 3h-2c2 1 2 1 3 3l2 2c1 0 3 1 4 1l-2 1c1 0 2 1 2 1h0c1 1 2 2 3 2v1l-2-1 2 2h1c4 1 7 1 11 1l1 1-1 1c-1 1-1 1 0 2v2c0 1-1 2 0 3 0 1 2 3 3 3 1 1 3 3 3 5v1l-3-2-3-1c-3-1-6-2-8-3s-3-3-5-4h0l-2 1c-3-2-5-6-9-8l-4-4h-1c-1 0-1-1-2-2-3-2-5-5-8-9l-5-6v-2c1-1 1-2 1-3h0l1-1h1l1-2h1v-1-3l2-1z" class="Q"></path><path d="M153 524c2 1 2 2 3 3 0 1 1 2 2 3h0c-2-1-4-3-6-5l1-1z" class="X"></path><path d="M151 515h1v-2h0l1 1s1 2 1 3v1c1 2 3 5 5 7l3 3-1 1-2-2c-2-2-3-4-4-6-2-2-3-4-4-6z" class="D"></path><path d="M147 507c1 1 2 3 3 5h0c-1 0-1 0-1 1s1 0 0 1v1c1 3 2 6 4 9l-1 1-1-3c-1-1-1-1-3-2 0 0-2-3-3-3v-1h0c0-1-1-1-1-2h1 1 0c0-1-1-2-1-2v-2l1-2h1v-1z" class="F"></path><path d="M144 510h1v2s1 1 1 2h0-1-1c0 1 1 1 1 2h0v1c1 0 3 3 3 3 3 5 7 9 10 13h-1c-1 0-1-1-2-2-3-2-5-5-8-9l-5-6v-2c1-1 1-2 1-3h0l1-1z" class="S"></path><path d="M159 525c2 1 3 1 4 2 1 0 1 1 2 1h2c1 0 3 1 4 1l-2 1c1 0 2 1 2 1h0c1 1 2 2 3 2v1l-2-1 2 2h1c-3 0-7-1-9 0h-1c-2-1-6-5-7-8h1l2 2 1-1-3-3z" class="W"></path><path d="M159 525c2 1 3 1 4 2 1 0 1 1 2 1h2c1 0 3 1 4 1l-2 1c1 0 2 1 2 1h0c1 1 2 2 3 2v1l-2-1c-2 0-4-1-6-2-1-1-2-2-4-3l-3-3z" class="AK"></path><path d="M149 503h2c0 1 0 0 1 1h0c1 1 1 2 2 3l2 5c2 3 4 6 6 8 1 1 2 2 2 3h-2c2 1 2 1 3 3l2 2h-2c-1 0-1-1-2-1-1-1-2-1-4-2-2-2-4-5-5-7v-1c0-1-1-3-1-3l-1-1h0v2h-1v-1l-1-2h0c-1-2-2-4-3-5v-3l2-1z" class="M"></path><path d="M156 516c2 2 4 4 6 4 1 1 2 2 2 3h-2c0-1-2-2-3-2-1-2-3-3-3-5z" class="B"></path><path d="M155 513c0 1 1 2 1 2v1c0 2 2 3 3 5v1l-5-5c0-1-1-3-1-3 1-1 1 0 2 0v-1z" class="F"></path><path d="M151 506l1 1c0 3 1 4 3 6v1c-1 0-1-1-2 0l-1-1h0v2h-1v-1h0 1c-1-3-2-6-1-8z" class="B"></path><path d="M149 503h2c0 1 0 0 1 1l-1 2c-1 2 0 5 1 8h-1 0l-1-2h0c-1-2-2-4-3-5v-3l2-1z" class="Y"></path><path d="M154 517l5 5v-1c1 0 3 1 3 2 2 1 2 1 3 3l2 2h-2c-1 0-1-1-2-1-1-1-2-1-4-2-2-2-4-5-5-7v-1z" class="AV"></path><path d="M159 522v-1c1 0 3 1 3 2 2 1 2 1 3 3-2-1-4-3-6-4z" class="M"></path><path d="M166 535c2-1 6 0 9 0 4 1 7 1 11 1l1 1-1 1c-1 1-1 1 0 2v2c0 1-1 2 0 3 0 1 2 3 3 3 1 1 3 3 3 5v1l-3-2-3-1c-3-1-6-2-8-3s-3-3-5-4h0c-1 0-1-1-1-1l1-1h0l-7-7z" class="D"></path><path d="M189 552l-4-4h1 2 1c1 1 3 3 3 5v1l-3-2z" class="C"></path><path d="M175 540v1c1 0 1 0 1 1 0 2 2 3 3 4 2 1 3 0 4 2 1 0 2 1 2 2h0 0l1 1-8-3c-2-1-3-3-5-4h0c-1 0-1-1-1-1l1-1h0l2-2z" class="N"></path><path d="M166 535c2-1 6 0 9 0 4 1 7 1 11 1l1 1-1 1h-6-2c-1 0-2 2-3 2l-2 2-7-7z" class="Y"></path><path d="M159 479v3h1c2 1 2 1 3 2v1 2c1 3 2 7 3 10v-1c1 2 1 4 2 6 1 0 1 1 1 2 1 1 1 2 2 2l1-1-1-1h1 1l1 3c1 1 4 4 4 6l3 3h3c1 2 3 2 3 4 1 1 3 3 5 4 1 1 3 2 4 3l-1 1 1 1h0v1h0v1 1h1 0 0c-2 2-3 2-5 3-1 0-1 1-2 2h0l-1-1c-1 0-2 1-2 1l-1-1c-4 0-7 0-11-1h-1l-2-2 2 1v-1c-1 0-2-1-3-2h0s-1-1-2-1l2-1c-1 0-3-1-4-1l-2-2c-1-2-1-2-3-3h2c0-1-1-2-2-3-2-2-4-5-6-8l-2-5 1-1 1-2-2-4v-1-3l1-1-1-4h1c0 1 0 2 1 3 0 1 0 2 1 3v1h0v-1-1h1-1l1-2v-1l-1-2 2 2c0 2 0 4-1 6-1 0-1 1-1 1h1c1 0 2 0 3 1h1l-1-1h0v-1h1v1c1-1 1-2 2-3h0v-1c1 0 1-1 0-2h0v-1h-2 0v-1l1 1v-2h-4c0-1-1-1-1-2-1 0-1-2-1-3v-1h1 0l-1-1c0-1 0-1 1-1v-1c1-1 1-2 1-3z" class="B"></path><path d="M173 513c0 1 0 1-1 1l-4-1v-1c2-1 2-1 4-1h0l1 2z" class="M"></path><path d="M155 495l2 6c-2-1-2-1-3-1v-1-3l1-1z" class="z"></path><path d="M166 496c1 2 1 4 2 6 1 0 1 1 1 2l-1-1s0-1-1-1c-1-1-2-1-2-1l-1-1h0c0-2 1-2 2-3v-1z" class="q"></path><path d="M158 485l2-2c1 1 2 2 3 2v2h-1c-1 1-1 2-1 3h-2l-1-1v-4z" class="t"></path><path d="M158 485v1c1 0 1 1 2 1 0 2 0 2-1 3l-1-1v-4zm27 38l5 4c2 1 4 1 6 2v1h0c-2 0-4-1-6-1-1 0-1-1-1-1-2 0-4-1-6-3 1 0 1-1 2-2z" class="z"></path><path d="M174 517h1c1 1 1 1 1 2 1 0 2 1 3 2l2-1h0c1 1 2 1 3 2l1 1c-1 1-1 2-2 2l-1-1h-1c-1 0-2 0-3-1s-2-2-2-4l-2-2z" class="AM"></path><path d="M184 522l1 1c-1 1-1 2-2 2l-1-1 2-2z" class="u"></path><path d="M178 523l1-2h1c1 1 1 2 1 3h0c-1 0-2 0-3-1z" class="F"></path><path d="M181 520v-1h1c4 3 9 6 13 9l1 1h0c-2-1-4-1-6-2l-5-4-1-1c-1-1-2-1-3-2z" class="M"></path><path d="M185 531c3 2 7 2 11 1h1 0 0c-2 2-3 2-5 3-1 0-1 1-2 2h0l-1-1h0c-1 0-1-1-2-1h-2v-1h0c0-1-1-1-1-1 0-1 0-1 1-2z" class="w"></path><path d="M173 513c1 0 2 1 2 1l1-1 1 1c2 2 3 4 5 5h-1v1h0l-2 1c-1-1-2-2-3-2 0-1 0-1-1-2h-1c-1-1-1-2-1-4z" class="AO"></path><path d="M175 514l1-1 1 1c2 2 3 4 5 5h-1v1h0c-3-2-4-4-6-6z" class="E"></path><path d="M154 500c1 0 1 0 3 1 1 3 1 6 3 9l4 5-1 2c0-1-1-1-1-2-1-1-2-3-3-5l-1 1c-1 0-1 0-2 1l-2-5 1-1 1-2-2-4z" class="y"></path><path d="M156 504c0 1 1 2 1 3l2 3-1 1c-1 0-1 0-2 1l-2-5 1-1 1-2z" class="AM"></path><path d="M177 514v-1h1l3 3h3c1 2 3 2 3 4 1 1 3 3 5 4 1 1 3 2 4 3l-1 1c-4-3-9-6-13-9-2-1-3-3-5-5z" class="AP"></path><path d="M181 516h3c1 2 3 2 3 4-2-1-4-2-6-4z" class="E"></path><path d="M164 515c4 6 11 12 17 15 1 0 4 1 4 1-1 1-1 1-1 2-3-1-4-1-7-1-2-1-3-1-5-2l1-1c1 0 2-1 2 0h1 0c0-1 0-1-1-2l-3-2c-3-2-7-5-9-8l1-2z" class="Q"></path><path d="M168 503l1 1c1 1 1 2 2 2l1-1-1-1h1 1l1 3c1 1 4 4 4 6h-1v1l-1-1-1 1s-1-1-2-1h0l-1-2h0c-1 0-1-2-2-2-1-1-2-1-2-1-1-1-1-2-1-2 0-1 1-2 1-3z" class="AP"></path><path d="M168 503l1 1c1 1 1 2 2 2v1h0l-1-1h-3c0-1 1-2 1-3z" class="R"></path><path d="M172 504h1l1 3-1 1h1v2l-3-3v-1l1-1-1-1h1z" class="y"></path><path d="M174 507c1 1 4 4 4 6h-1v1l-1-1-1 1s-1-1-2-1h0l-1-2h2c0 1 1 1 1 2h1c-1-1-1-2-2-3h0v-2h-1l1-1z" class="z"></path><path d="M171 531s-1-1-2-1l2-1v1h1c2 1 3 1 5 2 3 0 4 0 7 1 0 0 1 0 1 1h0v1h2c1 0 1 1 2 1h0c-1 0-2 1-2 1l-1-1c-4 0-7 0-11-1h-1l-2-2 2 1v-1c-1 0-2-1-3-2h0z" class="AF"></path><path d="M171 531s-1-1-2-1l2-1v1h1c2 1 3 1 5 2 3 0 4 0 7 1 0 0 1 0 1 1h0c-5-1-9-1-14-3z" class="AS"></path><path d="M159 510c1 2 2 4 3 5 0 1 1 1 1 2 2 3 6 6 9 8l3 2c1 1 1 1 1 2h0-1c0-1-1 0-2 0l-1 1h-1v-1c-1 0-3-1-4-1l-2-2c-1-2-1-2-3-3h2c0-1-1-2-2-3-2-2-4-5-6-8 1-1 1-1 2-1l1-1z" class="E"></path><path d="M165 523h1c1 1 4 3 6 4 1 0 1 0 2 1h1v-1c1 1 1 1 1 2h0-1c0-1-1 0-2 0l-1 1h-1v-1c-1 0-3-1-4-1l-2-2c-1-2-1-2-3-3h2 1z" class="z"></path><path d="M164 523h1c0 1 1 1 2 2l3 3 1 1v1-1c-1 0-3-1-4-1l-2-2c-1-2-1-2-3-3h2z" class="l"></path><defs><linearGradient id="u" x1="200.778" y1="470.622" x2="178.616" y2="518.276" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#2d2c2a"></stop></linearGradient></defs><path fill="url(#u)" d="M193 453c0-1 3-1 4-1h2c2 1 4 1 7 2 0 0 1 0 2 1 2 1 4 1 6 2s3 2 4 3l1 1s1 0 2 1h0c1 0 2 0 2 1l2 1h0c1 0 2 2 3 2-1-1-2-2-2-3 2 1 3 3 4 4v2h1v4c0 5 0 11-1 17v6c0 5 0 10-1 14l-1 3c0 2-1 6-2 9v-2c-2-1-3-1-5-1-2 1-3 2-4 4l-1-1c-1-1-1-2-2-2-2 3-3 6-6 7l-1 1c-1-1-2-2-4-2h-2v1c-1-1 0-1-1-2l-2 2v2l-1 1h-1v-1h0l-1-1 1-1c-1-1-3-2-4-3-2-1-4-3-5-4 0-2-2-2-3-4h-3l-3-3c0-2-3-5-4-6l-1-3h-1-1l1 1-1 1c-1 0-1-1-2-2 0-1 0-2-1-2-1-2-1-4-2-6v1c-1-3-2-7-3-10v-2-1c-1-1-1-1-3-2h-1v-3c0-1 0-1 1-1h2 1 0c0-1 0-1-1-2h0l1-1h1v-1-1h1c-1 0-1-1-1-1v-1h-1c-1 0-2-1-3-1 0 0 0-1-1-1h0 2l-1-1c0-1 0-1 1-3l2 1h2l6-3 2-1h0c3-1 6-2 8-4l2-1 1-1c1 0 2-1 4-1l2-1 1-1h2z"></path><path d="M196 527c0 1 0 1 1 1l1-1v2l-1 1h-1v-1h0l-1-1 1-1z" class="F"></path><path d="M191 506c0 1 1 2 1 3h-1c-1-1-1-1-1-2v-1h1z" class="B"></path><path d="M167 482c0-3 1-5 2-7v6 4h0c0-2 0-2-1-3h-1z" class="y"></path><path d="M201 513c0 1-1 2-1 3s0 2-1 4v1l-1 1h0-1c1-2 1-4 2-5 0-1 1-3 2-4z" class="F"></path><path d="M178 489c1 0 2 0 3 1h3c0 1 0 1 1 2h-3c-1-1-2 0-3-1l-1-2z" class="C"></path><path d="M195 492v2c1 1 1 2 2 3h-1l-1 1h0l-1 1h-1v-1-1c0-2 1-4 2-5z" class="S"></path><path d="M196 497c1 1 1 1 1 2l1 1v2c-2 3-1 8-4 11v1c0-1 0-1 1-2 0-1 0-3 1-4v-1c1-1 0-2 0-3 1-1 1-1 1-2h0c-1 1-1 3-2 4 0-3 1-5 1-8h-1 0l1-1z" class="B"></path><path d="M163 466h2c0 1 0 1 1 1s1 0 2 1h1v1h-1-1c-2 0-4 0-6-1h0c1-1 1-2 2-2z" class="E"></path><path d="M200 516l1-1v2 2h0c0 1-1 1-1 1l1 1v1l-2 2h0c0 1-1 1-2 2v-4h1 0l1-1v-1c1-2 1-3 1-4z" class="AL"></path><path d="M193 497c0-1 0-1-1-1v-4c1-3 2-4 5-5l-2 5c-1 1-2 3-2 5z" class="s"></path><path d="M182 492h3l1 2h0v3c1 3 4 6 5 9h-1l-1-1v-1c-1-2-2-4-4-6-1-1-1-2-2-3 0-1 0-1-1-2v-1z" class="l"></path><path d="M169 481h1v2 1h1c3 1 4 3 7 4v-2 3h0l1 2-2-1v1c-1 0-1 0-1-1l-2-1h0c-1-1-2-1-3-2v-1l-1-1-1 1v-1-4z" class="C"></path><path d="M177 490l-1-1h1 1 0l1 2-2-1z" class="AL"></path><path d="M182 513c0-1-1-2-1-3h1c2 2 4 5 6 7s3 4 4 6v1c-2-1-4-3-5-4 0-2-2-2-3-4l-1-1-1-2z" class="Y"></path><path d="M181 501h0c1-1 1-1 1-3l1-1c1 3 3 5 3 8h0c-1-1-2-3-3-5l-1 2h1c2 3 4 7 6 10 0 1 1 2 1 3-2-1-2-5-4-6-1 0-1-1-2-1l-1 1h0l-1-1h0c-1-1-1-1-2-3l1-1h-1s-1 0-1-1h0l2-2z" class="B"></path><path d="M181 504c1 0 1-1 1-1 2 2 3 4 4 6-1 0-1-1-2-1l-1 1h0l-1-1h0c-1-1-1-1-2-3l1-1z" class="S"></path><path d="M176 490c0 1 0 1 1 1v-1l2 1c1 1 2 0 3 1v1c1 1 1 1 1 2v2l-1 1c0 2 0 2-1 3h0-3v-1s-1-1-1-2h0c-1-1-1-2-1-3l-1-1h0v-2c0-1 0-1 1-2z" class="u"></path><path d="M176 495l2 2-1 1h0c-1-1-1-2-1-3z" class="AL"></path><path d="M176 490c0 1 0 1 1 1v-1l2 1c1 1 2 0 3 1v1h0-2c0 1 1 2 2 3v1c-1-1-2-1-2-1-1-2-2-2-4-2h-1 0v-2c0-1 0-1 1-2z" class="z"></path><path d="M177 490l2 1c1 1 2 0 3 1v1h0c-2 0-3 0-5-1v-1-1z" class="y"></path><path d="M216 498c2-1 4-1 6-1l1 1-1 1h2l-2 1v2l2-1v1h0c0 1-1 1-1 2h1l1 1h-1-2l-2-1h-1s0-1-1-1h0-1l-1 1h0c0-1-1-2-1-2-1-1-2 0-2 0h-1c0-1 1-1 1-2l-1-1c1-1 3-1 4-1z" class="AL"></path><path d="M216 498c2-1 4-1 6-1l1 1-1 1h2l-2 1v2 1c-2-1-3-2-4-2h-1v-1h2v-1h-1c-1 0-1 1-2 1s-1 1-1 1l-1-1h0c1-1 1-1 2-1v-1z" class="M"></path><path d="M175 503l2-2c0 1 1 1 1 2h1 0c0 1 1 1 1 1h1l-1 1c1 2 1 2 2 3h0l1 1c0 1 0 1-1 1h-1c0 1 1 2 1 3l1 2 1 1h-3l-3-3c0-2-3-5-4-6l-1-3h1c1 0 1 1 2 1l-1-1v-1z" class="u"></path><path d="M182 513l-1-2c-1 0-1 0 0-1h0c-1-2-2-3-4-5h1 2c1 2 1 2 2 3h0l1 1c0 1 0 1-1 1h-1c0 1 1 2 1 3zm-9-9h1c1 0 1 1 2 1 0 3 3 7 5 9 1 0 1 1 2 1l1 1h-3l-3-3c0-2-3-5-4-6l-1-3z" class="l"></path><path d="M191 506c-1-3-4-6-5-9v-3c2 1 6 2 7 4v1h1l1-1h1c0 3-1 5-1 8-1 2-1 3-2 4h-1 0v-1c0-1-1-2-1-3z" class="E"></path><path d="M224 499c2 0 3-1 5-1h0c1 0 1-1 1-2 0 5 0 10-1 14-1 1-2 1-3 1h-3c0-1 0-2-1-3 1-1 0-2 0-3h2 1l-1-1h-1c0-1 1-1 1-2h0v-1l-2 1v-2l2-1z" class="C"></path><path d="M169 485h0v1l1-1 1 1v1c1 1 2 1 3 2h0l2 1c-1 1-1 1-1 2v2h0l1 1c0 1 0 2 1 3h0c0 1 1 2 1 2v1h3l-2 2h-1c0-1-1-1-1-2l-2 2v1l1 1c-1 0-1-1-2-1h-1-1l-1-5c-1-1-1-2-2-4v-2h-2v-3h1v-3c1 0 1-2 1-2z" class="B"></path><path d="M167 490h1v-3l1 6h-2v-3z" class="R"></path><path d="M171 487c1 1 2 1 3 2h0l2 1c-1 1-1 1-1 2v2h-1-2v1h1v2 1h-1l-1-1v-2c-1-2-1-6 0-7v-1z" class="y"></path><path d="M171 487c1 1 2 1 3 2l-2 2h-1v-3-1z" class="AO"></path><path d="M174 489l2 1c-1 1-1 1-1 2v2h-1l-1-1c0-2 1-3 1-4z" class="t"></path><path d="M174 494h1 0l1 1c0 1 0 2 1 3h0c0 1 1 2 1 2v1h3l-2 2h-1c0-1-1-1-1-2l-2 2v1l1 1c-1 0-1-1-2-1h-1-1l-1-5v-2h0l1 1h1v-1-2h-1v-1h2z" class="AM"></path><path d="M173 502l1-1h1v2 1l-2-2z" class="y"></path><path d="M173 495h1c1 1 1 2 1 3l1 1v1l-3-3v-2z" class="C"></path><path d="M171 499v-2h0l1 1c0 1 0 3 1 4l2 2 1 1c-1 0-1-1-2-1h-1-1l-1-5z" class="F"></path><path d="M181 458c1 1 1 1 1 2v2 2c-2 0-4 1-6 2h1l4-1h3 0l-1 1c-1 1-1 1 0 2s2 0 3 0v1c-2 0-3 0-4 1-2 0-6 1-8 0-2 0-3-1-5-1h-2 1 1v-1h-1c-1-1-1-1-2-1s-1 0-1-1l6-3 2-1h0c3-1 6-2 8-4z" class="W"></path><g class="Q"><path d="M177 466l4-1v2c-1 0-2-1-3 0-1 0-1 1-1 2-1 0-2-1-3-1h0c1-1 2-1 3-2z"></path><path d="M181 458c1 1 1 1 1 2v2 2c-2 0-4 1-6 2h0-2 0c-1 0-2 0-2 1h-4 0c1-1 1-1 2-1h1l2-4h0c3-1 6-2 8-4z"></path></g><path d="M182 462h-1c-1 0-1 1-1 1h-4l1-1h1l4-2v2z" class="w"></path><path d="M165 473h0c1 0 1 0 2-1 0 2-1 5-1 7v3h0 1 1c1 1 1 1 1 3 0 0 0 2-1 2v3h-1v3h2v2c1 2 1 3 2 4l1 5h-1l1 1-1 1c-1 0-1-1-2-2 0-1 0-2-1-2-1-2-1-4-2-6v1c-1-3-2-7-3-10v-2-1c-1-1-1-1-3-2h-1v-3c0-1 0-1 1-1h2 1 0c0-1 0-1-1-2h0l1-1h1v-1-1h1z" class="u"></path><path d="M169 493v2 3h-1l-1-5h2z" class="x"></path><path d="M164 479l1-1c1 1 0 2 0 4h0l-2 2c-1-1-1-1-3-2h1c2-1 2-1 3-3z" class="AM"></path><path d="M169 495c1 2 1 3 2 4l1 5h-1l-3-6h1v-3z" class="AP"></path><path d="M167 482h1c1 1 1 1 1 3 0 0 0 2-1 2v3h-1c-1-2 0-6 0-8z" class="AN"></path><path d="M159 479c0-1 0-1 1-1h2 1l1 1c-1 2-1 2-3 3h-1-1v-3z" class="F"></path><path d="M162 478h1l1 1c-1 2-1 2-3 3 0-2 0-2 1-4z" class="J"></path><path d="M163 484l2-2c1 5 0 9 1 14v1c-1-3-2-7-3-10v-2-1z" class="x"></path><path d="M212 499l1 1c0 1-1 1-1 2h1s1-1 2 0c0 0 1 1 1 2h0 0c0 1 0 2-1 3l-1 1h1l-1 1-1-1c0 1 0 2 1 3l-2 2h-1l-1-1-1 1c-1 0-2 0-3-1v2c-1 1-2 1-2 2 0 2 1 2 1 3h-2v1l1 1h-1-1l-1 1v-1l-1-1s1 0 1-1h0v-2-2l-1 1c0-1 1-2 1-3s2-5 3-6a30.44 30.44 0 0 1 8-8z" class="E"></path><path d="M212 499l1 1c0 1-1 1-1 2h1s1-1 2 0c0 0 1 1 1 2h0 0c0 1 0 2-1 3l-1 1h1l-1 1-1-1c0 1 0 2 1 3l-2 2h-1l-1-1v-1-1l-1-1h0l2-2 2-2-1-1-2 2h-1v-1-1h0l-1 1c-1 1-2 2-4 2a30.44 30.44 0 0 1 8-8z" class="u"></path><path d="M211 513v-4c1 0 1 0 2-1 0 1 0 2 1 3l-2 2h-1z" class="M"></path><path d="M218 503h0c1 0 1 1 1 1h1l2 1c0 1 1 2 0 3 1 1 1 2 1 3h3c1 0 2 0 3-1l-1 3c0 2-1 6-2 9v-2c-2-1-3-1-5-1h0v-1c-1-1-2-1-3-1v-2c0-1-1-1-2-1h-3c-1 1-2 1-3 2 0 0-1-1-2-1l1-2 1-1 1 1h1l2-2c-1-1-1-2-1-3l1 1 1-1h-1l1-1c1-1 1-2 1-3h0l1-1h1z" class="m"></path><path d="M210 512l1 1h1 4v1h-3c-1 1-2 1-3 2 0 0-1-1-2-1l1-2 1-1z" class="Q"></path><path d="M221 519c1 0 3 0 3-1v-1h-1v-3l-1-1 1-1 5 1c0 2-1 6-2 9v-2c-2-1-3-1-5-1h0z" class="D"></path><path d="M218 503h0c1 0 1 1 1 1h1l2 1c0 1 1 2 0 3 1 1 1 2 1 3h3c-3 1-8 1-11 1v-1h-1c-1-1-1-2-1-3l1 1 1-1h-1l1-1c1-1 1-2 1-3h0l1-1h1z" class="AM"></path><path d="M218 503h0c1 0 1 1 1 1 0 1 0 2-1 2 0 1 0 1 1 2-1 1-1 1-1 2l-1-1h-1c-1-1-1-1-1-2 1-1 1-2 1-3h0l1-1h1z" class="C"></path><path d="M218 503h0c1 0 1 1 1 1 0 1 0 2-1 2h-1c1-1 1-2 1-3z" class="E"></path><path d="M217 509v-3h1c0 1 0 1 1 2-1 1-1 1-1 2l-1-1z" class="u"></path><path d="M219 504h1l2 1c0 1 1 2 0 3 1 1 1 2 1 3h-1c-1-1-1-2-1-3h0l-2 1v-1c-1-1-1-1-1-2 1 0 1-1 1-2z" class="B"></path><path d="M220 504l2 1c0 1 1 2 0 3-1-1-2-2-2-4z" class="M"></path><path d="M206 514v-2c1 1 2 1 3 1l-1 2c1 0 2 1 2 1 1-1 2-1 3-2h3c1 0 2 0 2 1v2c1 0 2 0 3 1v1h0c-2 1-3 2-4 4l-1-1c-1-1-1-2-2-2-2 3-3 6-6 7l-1 1c-1-1-2-2-4-2h-2v1c-1-1 0-1-1-2l-2 2-1-1c1-1 2-1 2-2h0l2-2 1-1h1 1l-1-1v-1h2c0-1-1-1-1-3 0-1 1-1 2-2z" class="Q"></path><path d="M206 520l1 1v2l-1 1-1-1c0-1 0-2 1-3zm-7 4c1 0 2-1 2 0 1 0 2 1 2 2h-2v1c-1-1 0-1-1-2l-2 2-1-1c1-1 2-1 2-2z" class="F"></path><path d="M216 514c1 0 2 0 2 1v2c-1 0-1 1-3 1l-1-2c-1 0-1-1-1-2h3z" class="C"></path><path d="M207 521c2-1 3-2 6-2l-4 4c-1 1-2 1-3 1h0l1-1v-2z" class="D"></path><path d="M206 514v-2c1 1 2 1 3 1l-1 2c1 0 2 1 2 1l-2 2h-1-1v-2h1c0-1-1-2-1-2z" class="N"></path><path d="M210 516c1-1 2-1 3-2 0 1 0 2 1 2-1 1 0 2-1 3-3 0-4 1-6 2l-1-1c1 0 1-1 2-2l2-2z" class="X"></path><defs><linearGradient id="v" x1="209.896" y1="488.93" x2="200.347" y2="458.233" xlink:href="#B"><stop offset="0" stop-color="#222221"></stop><stop offset="1" stop-color="#404040"></stop></linearGradient></defs><path fill="url(#v)" d="M190 462l14 2 13 5 8 4c1 1 3 2 5 3v-3h1c0 5 0 11-1 17v-6c-2-1-4-1-7-2l-12-6h-2l-10-3c-3-1-7-2-10-3-1 0-2 0-3-1v-1c-1 0-2 1-3 0s-1-1 0-2l1-1h0-3l-4 1h-1c2-1 4-2 6-2h2c2-1 4 0 6-2z"></path><path d="M189 470c2-1 3 0 6-1h3v1l-1 1h0 1v1l1 1c-3-1-7-2-10-3z" class="F"></path><path d="M184 465h1c1 1 2 1 3 0l1 1c-1 1-1 2-2 2h-1c-1 0-2 1-3 0s-1-1 0-2l1-1z" class="AA"></path><defs><linearGradient id="w" x1="208.948" y1="463.656" x2="204.752" y2="469.367" xlink:href="#B"><stop offset="0" stop-color="#474946"></stop><stop offset="1" stop-color="#615d5b"></stop></linearGradient></defs><path fill="url(#w)" d="M190 462l14 2 13 5 8 4c1 1 3 2 5 3v-3h1c0 5 0 11-1 17v-6-6c-1-2-3-2-4-3-2-1-4-2-5-3-3-1-5-2-8-3-7-2-14-5-21-5h-8c2-1 4 0 6-2z"></path><defs><linearGradient id="x" x1="229.82" y1="467.18" x2="189.96" y2="465.007" xlink:href="#B"><stop offset="0" stop-color="#181717"></stop><stop offset="1" stop-color="#4a4b4a"></stop></linearGradient></defs><path fill="url(#x)" d="M193 453c0-1 3-1 4-1h2c2 1 4 1 7 2 0 0 1 0 2 1 2 1 4 1 6 2s3 2 4 3l1 1s1 0 2 1h0c1 0 2 0 2 1l2 1h0c1 0 2 2 3 2-1-1-2-2-2-3 2 1 3 3 4 4v2h1v4h-1v3c-2-1-4-2-5-3l-8-4-13-5-14-2c-2 2-4 1-6 2h-2v-2-2c0-1 0-1-1-2l2-1 1-1c1 0 2-1 4-1l2-1 1-1h2z"></path><path d="M196 457h2l1 1c0 1 0 2-1 2h-2v-2-1z" class="m"></path><path d="M204 464v-1c5 0 7 1 11 3 1 0 2 1 3 2l-1 1-13-5z" class="L"></path><path d="M200 456l9 3c2 2 6 3 9 5h-1c-1 0-2-1-3-1h0v1c-5-3-10-5-16-7h-2l-1-1h5z" class="S"></path><path d="M193 453c0-1 3-1 4-1h2c2 1 4 1 7 2 1 2 3 3 4 4l-1 1-9-3h-5l-1 1c-1 0-2 0-2-1h-1c-1 0-2 0-3-1l2-1 1-1h2z" class="o"></path><path d="M193 453c0-1 3-1 4-1-1 2-1 2-1 3 1 1 3 0 4 1h-5l-1 1c-1 0-2 0-2-1l2-1v-2h-1z" class="m"></path><path d="M193 453h1v2l-2 1h-1c-1 0-2 0-3-1l2-1 1-1h2z" class="AU"></path><defs><linearGradient id="y" x1="194.325" y1="460.912" x2="185.362" y2="455.384" xlink:href="#B"><stop offset="0" stop-color="#686665"></stop><stop offset="1" stop-color="#818077"></stop></linearGradient></defs><path fill="url(#y)" d="M184 456c1 0 2-1 4-1 1 1 2 1 3 1h1c0 1 1 1 2 1l1-1 1 1v1 2h-5v1l-1-1v-1l-1 1c0 1-1 1-2 2h3c-2 2-4 1-6 2h-2v-2-2c0-1 0-1-1-2l2-1 1-1z"></path><path d="M183 457l1 3c1 0 1-1 2-1s2 1 3 1c0 1-1 1-2 2h3c-2 2-4 1-6 2h-2v-2-2c0-1 0-1-1-2l2-1z" class="m"></path><path d="M182 462h0c2 0 4-1 5 0h3c-2 2-4 1-6 2h-2v-2z" class="AU"></path><path d="M206 454s1 0 2 1c2 1 4 1 6 2s3 2 4 3l1 1s1 0 2 1h0c1 0 2 0 2 1l2 1h0c1 0 2 2 3 2-1-1-2-2-2-3 2 1 3 3 4 4v2h1v4h-1c-2-2-5-4-7-6l-5-3c-3-2-7-3-9-5l1-1c-1-1-3-2-4-4z" class="N"></path><path d="M226 463c2 1 3 3 4 4v2 1l-1-1c-1-1-3-3-4-5h0c1 0 2 2 3 2-1-1-2-2-2-3z" class="M"></path><path d="M210 458c1 0 1 0 1 1h1l1 1h2c3 0 4 4 8 6v1l-5-3c-3-2-7-3-9-5l1-1z" class="Q"></path><path d="M312 351h1 0c0 1 0 1 1 1 8 2 17 0 26 0h11l2 2v-1l1 1v-1h5 2-3l20 1h2c0-1 1-1 1-1h1c1 1 1 1 2 1h2l-1 1-2-1v1c0 1 0 1 1 2 0 0 1 0 2 1 2 1 6 1 8 2 1 0 8-1 10-1l7-1 5-1c2 1 6-1 9-1 2 0 4 0 6-1h0 5c3-1 6 0 10 0v1 1l-1 1v3 4c1 1 0 1 0 2l-2 1h1s1 1 1 0h1 2v2l-1 1c-1 0-2 0-2 1v1h1c0 1-1 2-1 4h-2v1h-2v1c-1 2-1 6-1 8 0 1 1 4 0 4l1 26c0 4 0 9-1 12l1 1c-1 2-2 11-1 12v3h2v1c0 1 0 2-2 3v1c-3 1-6 2-9 1s-6-3-7-6c-1 2-5 3-7 5-4 3-9 6-13 9-2 2-4 4-6 5h-1c-5 6-10 12-14 18-6 8-12 16-16 25-1 3-2 5-3 7-1 4-3 7-4 11l-1 5-1 4v1c0 1-1 3-1 5 0 1 0 1 1 1h0v1h0c0 1 0 3-1 4-1 0-1 0-1 1-1 0-2 1-2 2v3l-2 2h0-1v1l-1 1c2 0 4 0 5 1h0l-1 1h-7c2 1 6 4 9 4l1 1h-2 0 0c-2-1-3-1-4-1s-3-1-5-2v1 1c-2-1-3-2-5-3 0 0-1-1-1-2h-2c-1-1-2-1-3-2 0-2-1-3-2-4-2-3-5-8-8-11 0-1-1-3-2-4l-1-1c1 0 1-1 1-1v-1h1c0-1-1-1-1-2-1 1-2 2-2 3h-1v-1c0-1 0-2 1-2v-1c0-1 0-1-1-2l1-1h-2c-2 2-3 5-4 7l-2 2c-3 4-7 9-11 12h-1l-4 5c-1 1-2 3-3 4l-5 8-8 8s-2 3-3 4c-7 6-13 13-21 19-5 3-11 8-17 11-2 1-4 2-7 4-1 0-4 2-5 1l-6 2c-2 1-4 2-6 2v-1l-19 7c-2 1-4 1-6 2-3 1-7 1-10 2h-3c-1 1-2 1-3 1-2 0-4 0-6 1h-7-23c-3 0-6 0-9-1h-3 0-1c-2 0-4-1-6-1 0-1 0 0-1-1l-6-1h-2 0-1c-1-1-2-1-2-1h-1c-1 0-2 0-3-1v-1h-1v-1l-1-1h1l1-1h4c1 1 3 1 5 2h1 4 4 6c6-1 12-2 18-2-1-1-3-1-3-2h2v-1c2-1 4-1 6-2v-1l7-2c4-2 8-3 12-5 1-1 3-1 4-2 7-4 14-9 21-13h1 0 1 1v1 1c0 1 1 1 1 2v1h1c0 1-1 1-1 2l1 1c0-2 1-2 3-4h1c1 0 1-1 1-1h3l2-1h2v-1c-1-2-2-2-3-3l1-1h0l-1-1h0c0-1 1-2 1-3 0 0-1 0-2-1 2-2 6-5 8-8 1-2 3-3 4-5l1-2 2-1 1 1c1-2 2-3 3-5 2-2 4-4 5-7l7-9c0-1 1-4 3-4 1-3 2-5 3-8 2-2 3-7 4-10 0-1 0-1 1-2v-2c-2-1-3-2-3-3v-1c-1 1-1 1-2 0 0 0-1-1-1-2h-1c2-1 2-2 4-2l-2-2h3v-1l-1-2h1c-1-2-3 0-4-2h-1l-1-1v-3c0-4 0-8 1-11v-21-18l-1-68c1 2 1 3 1 4l1 1c0-2 1-4 2-5 2-1 3-2 5-1 2 0 4 2 5 4 2 2 2 5 3 8 1 1 1 3 1 4 0 2 0 3 1 5h-1v1l1-1c4-1 7-1 11-1 1 0 3 0 4-1 2 0 5 0 6 1h1 1v-2l-1-2v-1h1v-1-1-1c0-1 0-3-1-4 2 0 2-1 3-1 0-1 1-2 1-3 1 0 1-1 2-1 1-1 3-1 4-1h0 4l1-1h2v-1c-3-1-4-1-6-1v-2l1-1 1 2v-3l1-1h0c0-2-1-3-1-5 0-3 1-9 2-12 1-2 2-3 3-5h0c4 0 8-1 11-3z" class="AB"></path><path d="M317 479h2l-2 7v-1l-1-1 1-5z" class="b"></path><path d="M317 479h0l1-9v6h1c0-1-1-4 0-5v-1h0v9h-2z" class="O"></path><path d="M325 368h10 2 0c-2 1-4 0-6 1h-3-3l1-1h-1z" class="I"></path><path d="M245 588c3-2 6-3 9-5-1 1-2 2-2 3-2 2-3 4-6 5v-1h-1c1-1 1-1 1-2h-1z" class="O"></path><path d="M439 428h0c1 2 0 3 0 5h1v-4l1 1c-1 2-2 11-1 12-1 2-2 3-3 3h-3l1-1c1 0 1 0 2-1 3-2 2-11 2-15z" class="T"></path><path d="M431 418v-2 6c0 6 1 10 3 16l1 1h-1c-2-4-5-11-5-16h1v2c0-1 0-2 1-3v-4z" class="g"></path><path d="M104 618h4c1 1 3 1 5 2h1 4 4 0l7 1h-7c-2-1-4 0-6 0h0 1 0l-14-2 1-1z" class="AQ"></path><path d="M234 598c-2 2-4 1-6 3-3 1-6 4-10 5-1 1-2 1-3 1h1v-2l3-1c4-3 10-4 15-6z" class="g"></path><path d="M219 604l-1 2c-1 0-2 0-2 1v-2l3-1z" class="G"></path><path d="M335 368c6-1 14 0 20 1l-3 1c-2 1-4 0-6 0l-15-1c2-1 4 0 6-1h0-2z" class="a"></path><path d="M426 422c2 6 3 11 5 17 1 2 1 4 3 5h1l-1 1h0c-3-1-3-4-4-6s-2-4-2-6c-1-4-2-7-3-10l1-1z" class="o"></path><path d="M255 579c1-1 2-1 3-1 0-1 1-1 1 0h0l-5 5c-3 2-6 3-9 5h-1l-1-1c5-3 9-5 12-8z" class="e"></path><path d="M103 619l14 2 2 1-1 1h-5v1h-2 0-1c-1-1-2-1-2-1h-1c-1 0-2 0-3-1v-1h-1v-1l-1-1h1z" class="b"></path><path d="M431 422l1-1v-1-1h1l1 3v4c1 2 1 5 1 7 1 2 1 3 0 4l-1 1c-2-6-3-10-3-16z" class="v"></path><path d="M425 400l5 8c1 3 1 6 1 8v2 4c-1 1-1 2-1 3v-2h-1c0-3-1-6-2-9l1-1c-1-1-1-3-1-4-1-3-2-5-3-7 0-1 1-1 1-2z" class="U"></path><path d="M428 413v1c1 0 1 1 1 2 1-1 0-2 0-4h0l1-2v6h1v2 4c-1 1-1 2-1 3v-2h-1c0-3-1-6-2-9l1-1zm-24-24c6 6 14 13 18 20 2 4 3 9 4 13l-1 1c0-3-2-6-3-8-2-5-5-11-9-15-3-4-7-7-11-10l2-1z" class="a"></path><path d="M390 376l3 2 6 3c8 5 14 11 20 18 2 3 3 5 5 7 1 1 1 3 3 4v-1c0 1 0 3 1 4l-1 1c-8-16-22-30-38-37l1-1z" class="f"></path><path d="M355 369c15 4 30 8 43 17l6 3-2 1c-4-3-9-5-13-8s-11-4-15-6c-4-1-7-3-11-4h-5l-6-2 3-1z" class="G"></path><path d="M234 603v2c-6 2-11 5-17 7l-9 4c-2 1-4 1-6 2-1 0-2 0-3 1-1 0-3 0-4 1-2 1-4 1-6 1s-4 1-6 1l-2-1c18-3 37-9 53-18z" class="AC"></path><path d="M440 375c1 1 1 2 1 3v1c-1 2-1 6-1 8 0 1 1 4 0 4l1 26c0 4 0 9-1 12v4h-1c0-2 1-3 0-5h0v-5-9c0-5 0-37 1-38v-1z" class="H"></path><path d="M234 605c1-1 3-2 4-3l1 1c-4 4-9 7-15 9-3 1-5 2-8 3l-19 7h0c0-1 4-3 5-4 2-1 4-1 6-2l9-4c6-2 11-5 17-7z" class="r"></path><path d="M331 369l15 1c2 0 4 1 6 0l6 2h5c4 1 7 3 11 4-1 0-1 1-1 1-3 0-6-1-8-2l-10-2c-8-1-17-2-25-2l-3-1 1-1h3zm-7-5c8 0 16 0 24 1l8 1c2 0 3 1 4 1l20 4 10 5-1 1c-3-2-6-3-9-4-5-2-10-3-16-4s-12-3-18-3h-8c-3 1-6 1-9 0h-2-1c-1 0-2-1-3-1h-3 0l4-1z" class="O"></path><path d="M309 365l1 1c1-1 2-2 3-2h11l-4 1h0l-1 1c0 1 1 1 1 2v3c0 7 1 13 0 19 0-1-1-3 0-4v-2-4-8c-1 1-1 3-1 5v10 2-20l-1-1h-1v10 21h-1v-22-6h-1s0 1-1 2h-1c-1-1-3-2-3-4l-1-1v-1-2z" class="K"></path><path d="M309 368c2-2 3-2 5-2s2 1 3 2c0 1-1 1-2 3 0 0 0 1-1 2h-1c-1-1-3-2-3-4l-1-1z" class="P"></path><path d="M418 386c6 5 10 11 15 16l1 1v3l1 4c1 2 1 4 1 6l-1 1c0-1 1-1 1-1v-3c-2 1-2 1-3 0 0-1 0-2-1-3 0-1 0-3-1-5h0v1 4c0 1 1 2 1 3s-1 2-1 3v2-2c0-2 0-5-1-8l-5-8-1-1c-1-2-3-4-4-5h-2l-5-5c1 0 2 1 2 1 1 1 1 1 3 1 0-1-1-2-1-3v-1h1v-1z" class="b"></path><path d="M435 410l-1 1c-1-2-2-4-2-6l-3-6c2 2 3 4 5 7l1 4z" class="f"></path><path d="M417 388c3 1 5 4 7 7h-1c-1-1-2-2-3-2v1h-2l-5-5c1 0 2 1 2 1 1 1 1 1 3 1 0-1-1-2-1-3z" class="Z"></path><path d="M420 394v-1c1 0 2 1 3 2h1c1 0 2 2 2 3 1 1 2 2 2 3 1 1 2 4 2 5v2l-5-8-1-1c-1-2-3-4-4-5z" class="j"></path><path d="M395 375l7 2c6 3 11 7 15 10v1c0 1 1 2 1 3-2 0-2 0-3-1 0 0-1-1-2-1l5 5h2c1 1 3 3 4 5l1 1c0 1-1 1-1 2 1 2 2 4 3 7v1c-2-1-2-3-3-4-2-2-3-4-5-7-6-7-12-13-20-18l-6-3h2 1 0c-1-1-1-1-1-2l-1-1h1z" class="K"></path><path d="M422 399h2l1 1c0 1-1 1-1 2l-2-3z" class="I"></path><path d="M420 394c1 1 3 3 4 5h-2l-4-5h2z" class="c"></path><path d="M395 375l7 2c6 3 11 7 15 10v1c0 1 1 2 1 3-2 0-2 0-3-1 0 0-1-1-2-1-6-5-12-9-18-13l-1-1h1z" class="I"></path><path d="M316 484l1 1v1l-2 10c-1 7-3 13-6 19-1 4-2 7-4 10-1 3-2 4-3 6-1 3-2 5-4 7l-6 9-5 9c-3 4-6 8-9 11-2 3-5 5-7 8-2 2-4 3-5 4-5 5-10 10-15 14-4 3-9 6-13 9-1 1-3 2-4 3v-2l4-2c4-3 9-6 13-10 1 0 1-1 2-2l8-7c6-6 12-11 17-17s10-12 14-19l6-9c1-3 2-6 4-9 1-3 3-6 4-9 1-2 2-5 3-8 3-9 5-17 7-27z" class="O"></path><path d="M335 359l7 2c2 0 5 0 7 1h2c2-1 4 0 5 0l1-1 2 1h-1l3 3h4c2 1 4 1 6 1l1 1c1 0 1 1 2 1h1c1 1 3 1 4 2h1v1l-20-4c-1 0-2-1-4-1l-8-1c-8-1-16-1-24-1h-11c-1 0-2 1-3 2l-1-1 2-2s0-1 1-1h0c3-2 8-1 11-1h0 6c1-1 6-1 7-1-1 0-1 0-1-1z" class="v"></path><path d="M357 361l2 1h-1l3 3-3-1c-2-1-5-1-7-2 2-1 4 0 5 0l1-1z" class="U"></path><path d="M358 364l3 1h4c2 1 4 1 6 1l1 1c1 0 1 1 2 1h1c1 1 3 1 4 2h1v1l-20-4c1 0 3-1 4 0h2c1 0 2 1 3 1h0 3c1 1 1 1 2 1-1-1-1-1-2-1s-2-1-3-1-2 0-3-1h-3c-1 0-3-1-5-2h0z" class="r"></path><path d="M335 359l7 2c2 0 5 0 7 1-3 0-5 1-8 0-7-1-14 0-21 0-3 0-6 0-9 1 0 0 0-1 1-1h0c3-2 8-1 11-1h0 6c1-1 6-1 7-1-1 0-1 0-1-1z" class="K"></path><path d="M183 622c2 0 4-1 6-1s4 0 6-1c1-1 3-1 4-1 1-1 2-1 3-1-1 1-5 3-5 4h0c-2 1-4 1-6 2-3 1-7 1-10 2h-3c-1 1-2 1-3 1-2 0-4 0-6 1h-7-23c-3 0-6 0-9-1h-3 0-1c-2 0-4-1-6-1 0-1 0 0-1-1l-6-1v-1h5l1-1c21 3 42 3 62-1l2 1z" class="h"></path><path d="M119 622c21 3 42 3 62-1l2 1c-9 3-18 3-27 3h-17-10c-1 0-2-1-4-1s-3 0-6-1h-1l1-1z" class="O"></path><path d="M315 371h1v6 22h1v39c0 11 0 21-2 32 1 3 0 8 0 11-1 1-1 2-1 3 0 0 0 1-1 1v3c-1 0 0 1-1 2v1c-1 1-1 1-1 2v2c0 1 0 1-1 2h0c-1 1-1 3-1 4 0-11 4-22 3-34v-1-3c0-1 1-3 1-4 0-5 1-9 1-13 1-14 0-29 0-44v-9c0-3 2-18 0-20 1-1 1-2 1-2z" class="n"></path><path d="M315 371h1v6 22 21c-1-1 0-22 0-26 0-1-1-2-1-3v-5-1-1-4l-1 16c0 2 1 4 0 6h0v-9c0-3 2-18 0-20 1-1 1-2 1-2z" class="h"></path><path d="M316 399h1v39c0 11 0 21-2 32h0v4l-1 1v-4c1-3 1-6 1-9 1-14 1-28 1-42v-21z" class="O"></path><path d="M361 360v1c1 0 1-1 2-1 2-1 7 0 10 0 1 0 3 0 5 1h1 1c1 1 1 0 1 1h2 0 2v1h0 5c3 0 7-1 10 1h-3c2 0 4 0 6 1h2s1 1 2 1h-1-1-1l-1-1c-1 0-2 0-3 1 4 0 8 1 12 2h0c-1 1-1 1-2 0l-8-1s1 1 2 1c0 1 1 1 1 2 1 0 2 0 2 1 1 1 1 1 1 2l-1 1v-1h-2c0-1 0-1-1-1l-2 1v1l1 1v1h0l-1 1-7-2h-1l1 1c0 1 0 1 1 2h0-1-2l-3-2-10-5v-1h-1c-1-1-3-1-4-2h-1c-1 0-1-1-2-1l-1-1c-2 0-4 0-6-1h-4l-3-3h1l-2-1c1 0 2 0 4-1z" class="G"></path><path d="M397 369h5 2v2h-1-2-1-1v-1l-2-1z" class="b"></path><path d="M386 369c1-1 3-1 5-1 1 0 1 1 2 1h4l2 1v1l-6-1-2 1v1l-2 1c0-1-1-1-2-2 0 0-1 0-1-1v-1z" class="v"></path><path d="M386 369c2 0 5 1 7 1l-2 1v1l-2 1c0-1-1-1-2-2 0 0-1 0-1-1v-1z" class="U"></path><path d="M393 370l6 1h1 1 2 1v1l-2 1v1l1 1v1h0l-1 1-7-2-1-1v-1h0l-3-1v-1l2-1z" class="Z"></path><path d="M394 373c3 1 6 1 9 3h0l-1 1-7-2-1-1v-1z" class="p"></path><path d="M371 366c1 1 3 1 4 1 1 1 2 1 4 1h1l3 3h4c1 1 2 1 2 2l2-1 3 1h0v1l1 1h-1l1 1c0 1 0 1 1 2h0-1-2l-3-2-10-5v-1h-1c-1-1-3-1-4-2h-1c-1 0-1-1-2-1l-1-1z" class="h"></path><path d="M391 372l3 1h0v1l1 1h-1c-1 0-3-2-5-2l2-1z" class="AC"></path><path d="M361 360v1c1 0 1-1 2-1 2-1 7 0 10 0 1 0 3 0 5 1h1 1c1 1 1 0 1 1h2 0 2v1h0 5c3 0 7-1 10 1h-3c2 0 4 0 6 1h2s1 1 2 1h-1-1-1l-1-1c-1 0-2 0-3 1-1 0-3-1-5-1h-10c-5 1-10 1-14 0h-1-5-4l-3-3h1l-2-1c1 0 2 0 4-1z" class="s"></path><path d="M359 362l6 1h9 8 0c-2 1-8 1-10 1-2-1-4 0-5 0 1 0 2-1 3 0h1c1 1 3 0 4 0s1 1 2 1c3 0 5-1 8 0-5 1-10 1-14 0h-1-5-4l-3-3h1z" class="c"></path><path d="M359 362l6 1h-2c1 1 2 1 4 1h1c1 1 2 1 3 1h0-1-5-4l-3-3h1z" class="Z"></path><path d="M361 360v1c1 0 1-1 2-1 2-1 7 0 10 0 1 0 3 0 5 1h1 1c1 1 1 0 1 1h2 0 2v1h0-3-8-9l-6-1-2-1c1 0 2 0 4-1z" class="p"></path><path d="M383 362h2v1h0-3-8-2v-1h11z" class="v"></path><path d="M340 352h11l2 2v-1l1 1v-1h5 2-3l20 1h2c0-1 1-1 1-1h1c1 1 1 1 2 1h2l-1 1-2-1v1c0 1 0 1 1 2 0 0 1 0 2 1 2 1 6 1 8 2h-1l-1 1h0 0-1v1h1 0l-2 1h-5 0v-1h-2 0-2c0-1 0 0-1-1h-1-1c-2-1-4-1-5-1-3 0-8-1-10 0-1 0-1 1-2 1v-1c-2 1-3 1-4 1l-1 1c-1 0-3-1-5 0h-2c-2-1-5-1-7-1l-7-2c-1 0-3-1-5-1-1-1-2-1-2-3h0 1 4v-1h1l1-1c1 0 3 0 4-1h1z" class="AB"></path><path d="M340 352h11l2 2v-1l1 1h0l2 2h0l-5 1c-6-1-12-1-18-2v-1h1l1-1c1 0 3 0 4-1h1z" class="U"></path><path d="M340 352h11l2 2c-2 1-16 0-19 0l1-1c1 0 3 0 4-1h1z" class="v"></path><path d="M328 355c1 1 1 1 2 1 2 1 5 1 8 1 4 1 9 2 13 2s7 0 10 1c-2 1-3 1-4 1l-1 1c-1 0-3-1-5 0h-2c-2-1-5-1-7-1l-7-2c-1 0-3-1-5-1-1-1-2-1-2-3h0z" class="T"></path><path d="M354 354v-1h5 2-3l20 1h2c0-1 1-1 1-1h1c1 1 1 1 2 1h2l-1 1-2-1v1c0 1 0 1 1 2 0 0 1 0 2 1 2 1 6 1 8 2h-1l-1 1h0 0-1v1h1 0l-2 1h-5 0v-1h-2 0-2c0-1 0 0-1-1 2-1 3 0 5-1 1 0 3 0 4-1-2-1-6 0-8 0h-13c-2 0-5-1-8-1-2-1-4-1-6-1h-3l5-1h0l-2-2h0z" class="g"></path><path d="M392 361h0-1v1h1 0l-2 1h-5 0v-1h-2 0c3 0 6 0 9-1z" class="n"></path><path d="M237 589c1-1 3-2 4-4l1 1-2 3h0 1l1-2h0 1 0l1 1h1 1c0 1 0 1-1 2h1v1l-12 7h0c-5 2-11 3-15 6l-3 1v2h-1c-1 1-3 2-4 3h-3v-1h-4l-1-1c-1-1-1-2-2-4h0-1v-1h0c-2-2-2-3-2-5l1-1 1 1c0-2 1-2 3-4h1c1 0 1-1 1-1h3l2 2h1 2l1-2 1 1c4-1 7-3 11-5 0 1 0 1 1 1l1 1-3 1 1 1c0-1 1-1 2-1l3-1 3-3c1 0 2 0 2 1l1-1v1z" class="j"></path><path d="M242 589l1 2c-1 0-2 1-3 0 0-1 1-1 2-2z" class="AB"></path><path d="M243 587h0l1 1h1 1c0 1 0 1-1 2l-2 1-1-2h0v-2h0 1z" class="n"></path><path d="M234 588c1 0 2 0 2 1l1-1v1c-2 2-5 5-8 6v-1-1c1 0 1-1 2-2l3-3z" class="V"></path><path d="M226 589c0 1 0 1 1 1l1 1-3 1 1 1c0-1 1-1 2-1l3-1c-1 1-1 2-2 2v1 1c-4 2-7 4-12 5-1 0-1 0-2 1v1l-1 2-1 1c-1-1-1-2-2-2l-1-1c0-1 1-3 2-4h0c0-1-1-2-1-3h2l1-2 1 1c4-1 7-3 11-5z" class="d"></path><path d="M213 600l1 4-1 1c-1-1-1-2-2-2l2-3z" class="R"></path><path d="M214 598c1-1 2-1 3-2v3c-1 0-2 0-3-1h-1 1z" class="AH"></path><path d="M212 598h0 1 1c-1 1-1 2-1 2l-2 3-1-1c0-1 1-3 2-4z" class="s"></path><path d="M231 591c-1 1-1 2-2 2v1c-4 2-8 3-12 5v-3c4-1 7-2 11-4l3-1z" class="P"></path><path d="M226 589c0 1 0 1 1 1l1 1-3 1 1 1c0-1 1-1 2-1-4 2-7 3-11 4-1 1-2 1-3 2h-1-1 0 0c0-1-1-2-1-3h2l1-2 1 1c4-1 7-3 11-5z" class="T"></path><path d="M214 598l-1-1c0-1 1-1 1-2 4 0 7-2 11-3l1 1c0-1 1-1 2-1-4 2-7 3-11 4-1 1-2 1-3 2z" class="p"></path><path d="M200 598c0-2 1-2 3-4h1c1 0 1-1 1-1h3l2 2h1c0 1 1 2 1 3h0c-1 1-2 3-2 4l1 1c1 0 1 1 2 2l1-1 1-2 1 1v2 2h-1c-1 1-3 2-4 3h-3v-1h-4l-1-1c-1-1-1-2-2-4h0-1v-1h0c-2-2-2-3-2-5l1-1 1 1z" class="o"></path><path d="M204 599l2-1c1 0 1 0 1-1h0 1v1c-1 1 0 4-1 4 0 1-1 1-1 1v1c-1 0-1 0-2-1l-1-2h2v-1l-1-1z" class="a"></path><path d="M200 598c0-2 1-2 3-4h1c1 0 1-1 1-1h3l2 2h1l-2 1-1-2-1 1c-1 0-2 0-4 1 1 1 1 0 1 1v2l1 1v1h-2l1 2h-1 0c-1-1-2-1-3-2s-1-1 0-3z" class="V"></path><path d="M203 601l-2-2c1-1 1-2 1-3h1c1 1 1 0 1 1v2l1 1v1h-2z" class="AD"></path><path d="M199 597l1 1c-1 2-1 2 0 3s2 1 3 2h0 1c1 1 1 1 2 1h1c2 0 2-1 3-2l1 1c1 0 1 1 2 2l1-1 1-2 1 1v2 2h-1c-1 1-3 2-4 3h-3v-1h-4l-1-1c-1-1-1-2-2-4h0-1v-1h0c-2-2-2-3-2-5l1-1z" class="I"></path><path d="M201 604c1 1 2 2 4 3h3 2l-2 2h-4l-1-1c-1-1-1-2-2-4z" class="AH"></path><path d="M200 601c1 1 2 1 3 2h0 1c1 1 1 1 2 1h1c2 0 2-1 3-2l1 1c1 0 1 1 2 2-1 0-3 1-3 1h-1c-1-1-1 0-2 0h-1c-1-1-2-1-3-2-1 0-2-1-3-3z" class="AE"></path><path d="M213 606h0l3-3v2 2h-1c-1 1-3 2-4 3h-3v-1l2-2 3-1z" class="e"></path><path d="M213 606h1l-2 2c-1 0-1 1-1 2h-3v-1l2-2 3-1z" class="d"></path><path d="M389 385c0-1 0-2-1-2h0l1-1c4 3 9 5 13 8s8 6 11 10c4 4 7 10 9 15 1 2 3 5 3 8 1 3 2 6 3 10 0 2 1 4 2 6s1 5 4 6h0 3c1 0 2-1 3-3v3h2v1c0 1 0 2-2 3v1c-3 1-6 2-9 1s-6-3-7-6l-1-3v-1l-1-4-2-10-1-3c-1-1-1-3-2-4l-4-8v-2c-2-2-3-4-5-6-3-3-4-5-4-9-5-4-10-7-15-10z" class="g"></path><path d="M420 419c2 6 2 12 4 17 0 3 1 5 1 7l-2-2-1-4-2-10c0-2-1-3-1-5l1-3z" class="G"></path><path d="M404 395l6 6 6 9c2 3 3 6 4 9l-1 3c0 2 1 3 1 5l-1-3c-1-1-1-3-2-4l-4-8v-2c-2-2-3-4-5-6-3-3-4-5-4-9z" class="AU"></path><path d="M416 410c2 3 3 6 4 9l-1 3c0 2 1 3 1 5l-1-3c-1-1-1-3-2-4 1-4-1-5-2-8 0 0 1-1 1-2z" class="T"></path><path d="M424 436l3 6 1-1v1l1 1c1 1 2 2 3 2v-1c-1 0-1-1-2-2v-2c-1-1-1-1 0-1 1 2 1 5 4 6h0 3c1 0 2-1 3-3v3h2v1c0 1 0 2-2 3v1c-3 1-6 2-9 1s-6-3-7-6l-1-3v-1l2 2c0-2-1-4-1-7z" class="R"></path><path d="M428 443l3 3c0 1 1 2 2 3-2 0-3-1-4-2l-1-4z" class="i"></path><path d="M424 436l3 6 1 1 1 4-4-4c0-2-1-4-1-7z" class="AD"></path><path d="M440 445h2v1c-2 2-5 4-8 3h-1c-1-1-2-2-2-3 1 0 2 1 4 1s4 0 5-2z" class="AC"></path><path d="M427 442l1-1v1l1 1c1 1 2 2 3 2v-1c-1 0-1-1-2-2v-2c-1-1-1-1 0-1 1 2 1 5 4 6h0 3c1 0 2-1 3-3v3c-1 2-3 2-5 2s-3-1-4-1l-3-3-1-1z" class="p"></path><path d="M404 372c1 0 1 0 1 1h2v1l1-1c0-1 0-1-1-2 0-1-1-1-2-1 0-1-1-1-1-2-1 0-2-1-2-1l8 1c1 1 1 1 2 0h0l6 2h0l12 3h5 4 1-1c-1 0-4 0-5 1v29l-1-1c-5-5-9-11-15-16v1h-1c-4-3-9-7-15-10l1-1h0v-1l-1-1v-1l2-1z" class="g"></path><path d="M425 389h2c1 0 2 0 3 1l1 2-2 1-4-4z" class="n"></path><path d="M402 373c1 1 2 1 3 1h3c2 1 4 2 5 4v2l-10-5-1-1v-1z" class="p"></path><path d="M418 370l12 3h5 4 1-1c-1 0-4 0-5 1v29l-1-1c0-2 0-2-1-3 0 0 0-1 1-1v-14c0-3 1-6 0-9-2 0-4 0-5-1-2 0-3-1-4-1s-1-1-2-1c0 0-1-1-2-1s-1 0-2-1z" class="H"></path><path d="M403 375l10 5 6 3c2 2 4 5 6 6l4 4 2-1v3c-1 0-1 1-1 1 0 1 1 2 2 3s1 1 1 3c-5-5-9-11-15-16v1h-1c-4-3-9-7-15-10l1-1h0v-1z" class="U"></path><path d="M403 376c3 1 6 2 8 4 3 1 5 4 7 6v1h-1c-4-3-9-7-15-10l1-1z" class="H"></path><path d="M436 355c3-1 6 0 10 0v1 1l-1 1v3 4c1 1 0 1 0 2l-2 1h1s1 1 1 0h1 2v2l-1 1c-1 0-2 0-2 1v1h1c0 1-1 2-1 4h-2v1h-2c0-1 0-2-1-3v-2h-1-4-5l-12-3h0l-6-2c-4-1-8-2-12-2 1-1 2-1 3-1l1 1h1 1 1c-1 0-2-1-2-1h-2c-2-1-4-1-6-1h3c-3-2-7-1-10-1l2-1h0-1v-1h1 0 0l1-1h1c1 0 8-1 10-1l7-1 5-1c2 1 6-1 9-1 2 0 4 0 6-1h0 5z" class="p"></path><path d="M392 362h1 5c3-1 5-1 7-1v1h2c2 1 5 1 7 2h-3 0l1 1h-1c-4 0-7-1-11-1-3-2-7-1-10-1l2-1z" class="G"></path><path d="M405 362h2c-1 0-1 0-2 1h0-2c1-1 1-1 2-1z" class="O"></path><path d="M407 362c2 1 5 1 7 2h-3 0l1 1h-1c-1 0-2-1-3-1h0c-1 0-2 0-3-1h0c1-1 1-1 2-1z" class="g"></path><path d="M430 359c3 0 4 1 6 2l1 1h0c-2 1-3 1-4 1h-4-7c-3 0-5 0-7 1h-1c-2-1-5-1-7-2h-2v-1h12l13-2z" class="f"></path><path d="M430 359c3 0 4 1 6 2-2 1-4 1-6 1h-6-5-1l-1-1 13-2z" class="G"></path><path d="M436 355c3-1 6 0 10 0v1 1l-1 1v3 4c1 1 0 1 0 2l-2 1h-1c0-1 0-1-1-2 0-1 1-1 0-2h-1-3 0-4c-2 0-3 1-4 1-5 0-10 0-14-1 2-1 4-1 7-1h7 4c1 0 2 0 4-1h0l-1-1c-2-1-3-2-6-2h0c-1 0-3 0-4-1 0-1 1-1 2-1 1-1 2-1 3-1v-1h0 5z" class="i"></path><path d="M431 355h0l11 1h0c-1 2-8 3-10 3h-2c-1 0-3 0-4-1 0-1 1-1 2-1 1-1 2-1 3-1v-1z" class="v"></path><path d="M414 364h1c4 1 9 1 14 1 1 0 2-1 4-1h4 0 3 1c1 1 0 1 0 2 1 1 1 1 1 2h1 1s1 1 1 0h1 2v2l-1 1c-1 0-2 0-2 1v1h1c0 1-1 2-1 4h-2v1h-2c0-1 0-2-1-3v-2h-1-4-5l-12-3h0l-6-2c-4-1-8-2-12-2 1-1 2-1 3-1l1 1h1 1 1c-1 0-2-1-2-1h-2c-2-1-4-1-6-1h3c4 0 7 1 11 1h1l-1-1h0 3z" class="n"></path><path d="M429 365c1 0 2-1 4-1h4 0 3 1c1 1 0 1 0 2 1 1 1 1 1 2l-1 1h3c0 1-1 1-1 2-1 0 0 0-1 1h-2c1-1 1-1 1-2h-1l-1-1v-1-1c-3-3-7-2-10-2z" class="O"></path><path d="M400 364c4 0 7 1 11 1h1l19 5c3 1 6 2 9 2h2c1-1 0-1 1-1 0-1 1-1 1-2h-3l1-1h1 1s1 1 1 0h1 2v2l-1 1c-1 0-2 0-2 1v1h1c0 1-1 2-1 4h-2v1h-2c0-1 0-2-1-3v-2h-1-4-5l-12-3h0l-6-2c-4-1-8-2-12-2 1-1 2-1 3-1l1 1h1 1 1c-1 0-2-1-2-1h-2c-2-1-4-1-6-1h3z" class="AA"></path><path d="M445 373h1c0 1-1 2-1 4h-2v1h-2c0-1 0-2-1-3v-2h5z" class="n"></path><path d="M315 496h1c0 2-2 6-1 7 0-1 0-2 1-3v-2c0-1 1-1 1-2v-1h0v1c-2 8-4 16-7 24l-6 15v1c-2 5-5 9-7 13-1 1-2 2-2 3-1 1-3 4-4 6h0c1 0 2-1 3-1l-5 8-8 8s-2 3-3 4c-7 6-13 13-21 19-5 3-11 8-17 11-2 1-4 2-7 4-1 0-4 2-5 1l-6 2c-2 1-4 2-6 2v-1c3-1 5-2 8-3 6-2 11-5 15-9l-1-1c4-3 9-6 13-9 5-4 10-9 15-14 1-1 3-2 5-4 2-3 5-5 7-8 3-3 6-7 9-11l5-9 6-9c2-2 3-4 4-7 1-2 2-3 3-6 2-3 3-6 4-10 3-6 5-12 6-19z" class="AQ"></path><path d="M239 603c12-6 23-16 33-25 6-6 12-13 17-20 6-7 10-15 15-23v1c-2 5-5 9-7 13-1 1-2 2-2 3-1 1-3 4-4 6h0c1 0 2-1 3-1l-5 8-8 8s-2 3-3 4c-7 6-13 13-21 19-5 3-11 8-17 11-2 1-4 2-7 4-1 0-4 2-5 1l-6 2c-2 1-4 2-6 2v-1c3-1 5-2 8-3 6-2 11-5 15-9z" class="Z"></path><path d="M276 575l2-1c0-1 1-2 2-3l1 2s-2 3-3 4l-1-1-1-1z" class="a"></path><path d="M239 605v1l-3 2c1 0 2-1 3-1 0 0 1 0 1-1v1c-2 1-4 2-7 4-1 0-4 2-5 1l11-7z" class="p"></path><path d="M276 575l1 1 1 1c-7 6-13 13-21 19-5 3-11 8-17 11v-1c0 1-1 1-1 1-1 0-2 1-3 1l3-2v-1c1-1 3-1 5-3 1 0 2-1 3-2s2-1 3-2 2-2 3-2l18-15c2-2 3-4 5-5v-1z" class="AC"></path><path d="M317 496c1-4 2-7 3-11 2-17 3-33 3-50l1-32v-26c0-3-1-7 0-9h1 1l-1 1h3l-1 1c-1 2 0 5 0 7h0v60 12c-1 24-5 48-13 70l-4 12 2-2h1c-1 3-2 5-2 7l1 1c0-1 1-1 1-1-3 4-7 9-11 12h-1l-4 5c-1 1-2 3-3 4-1 0-2 1-3 1h0c1-2 3-5 4-6 0-1 1-2 2-3 2-4 5-8 7-13v-1l6-15c3-8 5-16 7-24z" class="T"></path><path d="M325 414v3c1 2 1 4 1 6v8 7l1-1v12c-1 2-2 5-2 7v7c0-1-1-1-1-2v2-4l1-45z" class="j"></path><path d="M325 369h3l-1 1c-1 2 0 5 0 7h0v60l-1 1v-7-8c0-2 0-4-1-6v-3-30c0-3-1-13 0-15z" class="H"></path><path d="M325 369h3l-1 1c-1 2 0 5 0 7h0c-1 3 0 7 0 10-1-1-1-1-1-2h0 0c-1-1 0-3 0-5h-1v7 3-6c0-3-1-13 0-15z" class="AC"></path><path d="M324 459v4-2c0 1 1 1 1 2v-7c0-2 1-5 2-7-1 24-5 48-13 70l-4 12 2-2h1c-1 3-2 5-2 7l1 1c0-1 1-1 1-1-3 4-7 9-11 12h-1l-4 5c-1 1-2 3-3 4-1 0-2 1-3 1h0c1-2 3-5 4-6 0-1 1-2 2-3 2-4 5-8 7-13 3-3 4-9 6-13l4-9h0l2-9h0c1-3 2-5 2-8l2-7c0-1 0-2 1-3 2-9 2-19 3-28z" class="G"></path><path d="M297 549s1-1 2-1c0 1 0 2-1 3-1 0-1 1-1 1l-1 1h-1v-1c0-1 1-2 2-3z" class="H"></path><path d="M310 531l2-2h1c-1 3-2 5-2 7l1 1c0-1 1-1 1-1-3 4-7 9-11 12h-1c3-6 6-11 9-17z" class="Y"></path><path d="M257 554c0 3-1 4 1 6v1h0c1 0 2 0 3-1v2c0-1 0-2 1-3 1 5 0 11-3 15-1 2-2 3-4 5-3 3-7 5-12 8h0-1 0l-1 2h-1 0l2-3-1-1c-1 2-3 3-4 4v-1l-1 1c0-1-1-1-2-1l-3 3-3 1c-1 0-2 0-2 1l-1-1 3-1-1-1c-1 0-1 0-1-1-4 2-7 4-11 5l-1-1 3-2c1-1 2-2 4-3 2-3 6-5 8-8l3-3 3-3 1-3c0-1 2-3 3-4 1-2 3-5 4-7 3 0 4-4 6-6v1h2 1 0 1 4v-1z" class="f"></path><path d="M251 565c0-1 0-2 1-2v3c-1 3-2 5-4 7l-5 6-2-1c3-3 6-6 8-10 0 0 1-2 2-3z" class="n"></path><path d="M228 591c2-2 4-4 7-6 1 0 2-1 3-2 1-2 2-3 3-5l2 1c-1 2-3 4-4 5s-4 3-5 4l-3 3-3 1c-1 0-2 0-2 1l-1-1 3-1z" class="h"></path><path d="M243 579l5-6v1c0 1 0 1-1 2l-1 2c2-1 3-3 5-4l-10 11c-1 2-3 3-4 4v-1l-1 1c0-1-1-1-2-1 1-1 4-3 5-4s3-3 4-5z" class="P"></path><path d="M257 554c0 3-1 4 1 6h-2v3l-1 2v3l-3 4-1 2c-2 1-3 3-5 4l1-2c1-1 1-1 1-2v-1c2-2 3-4 4-7 1-1 1-2 0-4v-4-1l1-1h2l2-1v-1z" class="I"></path><path d="M254 557c1 1 2 3 2 4v1l-1 1v-1h-1 0v-2c-1-1-1-2 0-3z" class="e"></path><path d="M257 554c0 3-1 4 1 6h-2v3-1-1c0-1-1-3-2-4h-1v1h-1v-1l1-1h2l2-1v-1z" class="f"></path><path d="M261 562c0-1 0-2 1-3 1 5 0 11-3 15-1 2-2 3-4 5-3 3-7 5-12 8h0-1 0l-1 2h-1 0l2-3-1-1 10-11 1-2 3-4v-3l1-2v-3h2v1h0c1 0 2 0 3-1v2z" class="AH"></path><path d="M255 565h2l-1 4-1-1v-3z" class="g"></path><path d="M255 568l1 1c0 1-1 3-2 4v-2s-1 1-2 1l3-4z" class="K"></path><path d="M256 563v-3h2v1l-1 4h-2l1-2z" class="G"></path><path d="M252 572c1 0 2-1 2-1v2c-2 5-8 9-12 13l-1-1 10-11 1-2z" class="n"></path><path d="M261 562c0-1 0-2 1-3 1 5 0 11-3 15-1 2-2 3-4 5-3 3-7 5-12 8h0l8-7c6-4 9-11 10-18z" class="g"></path><path d="M249 554v1h2 1 0 1 4l-2 1h-2l-1 1v1 4c1 2 1 3 0 4v-3c-1 0-1 1-1 2-1 1-2 3-2 3-2 1-5 4-6 6-2 3-4 5-7 7l-7 6c-1 1-2 2-3 2h0c-4 2-7 4-11 5l-1-1 3-2c1-1 2-2 4-3 2-3 6-5 8-8l3-3 3-3 1-3c0-1 2-3 3-4 1-2 3-5 4-7 3 0 4-4 6-6z" class="j"></path><path d="M221 588h0 2c2-1 3-3 5-4 1 0 1-1 1-1 1-1 2-3 3-4h2l5-8 1 1c-2 3-4 7-7 10-2 1-4 3-6 4-1 0-1 0-1 1-1 1-1 1 0 2h0c-4 2-7 4-11 5l-1-1 3-2c1-1 2-2 4-3z" class="g"></path><path d="M249 554v1h2 1 0 1 4l-2 1h-2l-1 1v1 4c1 2 1 3 0 4v-3c-1 0-1 1-1 2-1-1-1-2 0-4h0v-3-1c0 1-1 2-1 2h0v-2l1-1-2-1c-1 2-2 3-3 5s-2 5-4 6c-1 1-1 1-1 2-1 2-5 5-6 6l1-3c0-1 2-3 3-4 1-2 3-5 4-7 3 0 4-4 6-6z" class="o"></path><path d="M170 605c1-1 3-1 4-2 7-4 14-9 21-13h1 0 1 1v1 1c0 1 1 1 1 2v1h1c0 1-1 1-1 2l-1 1c0 2 0 3 2 5h0v1h1 0c1 2 1 3 2 4l1 1h4v1l-1 1h-3c-1 0-2 1-3 1-3 1-7 3-10 4s-5 1-8 2c-2 0-4 1-6 1h-9c-2 0-4 1-6 1-2 1-4 0-5 0-5 0-11 1-16 0-4 1-9 1-13 0 6-1 12-2 18-2-1-1-3-1-3-2h2v-1c2-1 4-1 6-2v-1l7-2c4-2 8-3 12-5z" class="p"></path><path d="M128 620c6-1 12-2 18-2 5 1 11 0 16 0l-5 1c-3 1-5 0-8 0-1 0-2 1-3 1-2-1-3 0-5 0-4 1-9 1-13 0z" class="j"></path><path d="M196 603l1-1c1 0 1 2 3 2h1 0c1 2 1 3 2 4l1 1h4v1l-1 1h-4c-2-1-3-2-6-1l-8 1-17 5c0-1 1-1 1-2v-1h0 2l5-2c6-1 11-4 16-7v-1z" class="k"></path><path d="M197 606l1 2c-1 0-3 1-4 1 1 1 2 1 3 1l-8 1c2-3 3-2 6-4l2-1z" class="s"></path><path d="M196 603l1-1c1 0 1 2 3 2h1 0c1 2 1 3 2 4l1 1c-2-1-3-1-5-1-1-1-1-1-1 0l-1-2h0v-1c1 1 2 1 2 1l1-1c-2-1-2-1-4-1v-1z" class="V"></path><path d="M198 608c0-1 0-1 1 0 2 0 3 0 5 1h4v1l-1 1h-4c-2-1-3-2-6-1-1 0-2 0-3-1 1 0 3-1 4-1z" class="R"></path><path d="M157 613h3 0 4c-1 1-1 1-2 1h2 0c1 0 2 1 3 0 2-1 4-1 6-1h0v1c0 1-1 1-1 2h-2l-5 2h-3c-5 0-11 1-16 0-1-1-3-1-3-2h2v-1c2-1 4-1 6-2h6z" class="H"></path><path d="M173 613v1c0 1-1 1-1 2h-2-1c0-1 0-1 1-2l3-1z" class="e"></path><path d="M157 613h3 0 4c-1 1-1 1-2 1h2c-3 1-5 0-7 1-1 1-3 1-4 1-3-1-6-1-8 0h0v-1c2-1 4-1 6-2h6z" class="n"></path><path d="M145 616h7c6 1 12 0 18-2-1 1-1 1-1 2h1l-5 2h-3c-5 0-11 1-16 0-1-1-3-1-3-2h2 0z" class="I"></path><path d="M196 599h0v-1h2c0 2 0 3 2 5h0v1c-2 0-2-2-3-2l-1 1v1c-5 3-10 6-16 7l-5 2h-2c-2 0-4 0-6 1-1 1-2 0-3 0h0-2c1 0 1 0 2-1h-4 0-3-6v-1l7-2c0 1 0 1 1 1 2 0 3-1 5-1 4-1 7-2 10-4 1 0 2-1 3-1 0 1 1 1 2 1h0 1 3c3-2 7-3 10-6l3-1z" class="c"></path><path d="M151 612l7-2c0 1 0 1 1 1 2 0 3-1 5-1-1 0-2 1-3 1v1c-1 1-3 0-4 1h-6v-1z" class="I"></path><path d="M196 599h0v-1h2c0 2 0 3 2 5h0v1c-2 0-2-2-3-2l-1 1c-2 1-5 2-8 4l-1-1 2-2c1 0 1 0 2-1 1 0 2 0 3-1s2-1 2-3z" class="AH"></path><path d="M196 603v1c-5 3-10 6-16 7-1 0-1-1-2-1 3-1 7-2 10-3 3-2 6-3 8-4z" class="n"></path><path d="M173 611c1 0 3-1 5-1 1 0 1 1 2 1l-5 2h-2c-2 0-4 0-6 1-1 1-2 0-3 0h0-2c1 0 1 0 2-1h-4 0l13-2z" class="p"></path><path d="M173 611c1 0 3-1 5-1 1 0 1 1 2 1l-5 2h-2c0-1 1-1 1-1l-1-1z" class="v"></path><defs><linearGradient id="z" x1="178.294" y1="607.216" x2="175.86" y2="592.644" xlink:href="#B"><stop offset="0" stop-color="#a68657"></stop><stop offset="1" stop-color="#d1ac5f"></stop></linearGradient></defs><path fill="url(#z)" d="M170 605c1-1 3-1 4-2 7-4 14-9 21-13h1 0 1 1v1 1c0 1 1 1 1 2v1h1c0 1-1 1-1 2l-1 1h-2v1h0l-3 1c-3 3-7 4-10 6h-3-1 0c-1 0-2 0-2-1-1 0-2 1-3 1-3 2-6 3-10 4-2 0-3 1-5 1-1 0-1 0-1-1 4-2 8-3 12-5z"></path><path d="M177 605c6-4 12-9 19-12 1-1 1-2 2-3v1 1c0 1 1 1 1 2v1h1c0 1-1 1-1 2l-1 1h-2v1h0l-3 1c-3 3-7 4-10 6h-3-1 0c-1 0-2 0-2-1z" class="AB"></path><path d="M199 595h1c0 1-1 1-1 2l-1 1h-2v1h0l-3 1c-3 3-7 4-10 6h-3-1 0c2-1 4-3 7-4l10-6c1-1 2-1 3-1z" class="e"></path><path d="M199 595h1c0 1-1 1-1 2l-1 1h-2v1h0l-3 1h0l1-1c1-1 2-2 2-3 1-1 2-1 3-1z" class="d"></path><path d="M311 470c0-1 1-2 1-3 1 12-3 23-3 34-1 1-1 2-2 4v5c-1 1-1 2-1 3-1 2-2 5-3 7l-1 3c-1 1-1 3-2 4h0c-2 2-3 5-4 7s-3 4-4 6-1 4-3 5c-2 5-5 8-8 12-3 3-5 7-8 10-1 2-3 4-5 5-2 2-5 5-8 6h-1 0c0-1-1-1-1 0-1 0-2 0-3 1 2-2 3-3 4-5 3-4 4-10 3-15-1 1-1 2-1 3v-2c-1 1-2 1-3 1h0v-1c-2-2-1-3-1-6l3-1c7-2 13-5 19-9 4-4 6-8 8-13-1-1-1-1-2-1v-5c1-4 0-7 0-10l1-2v-1c1-1 1-2 1-3v-4-1c0-2 0-4-1-6v-1l1-7v-4-5-1l1-1c1 1 1 1 1 2h0 1 1v2c0-1 1-2 2-3h2c3-2 7-2 10-4s4-3 6-6z" class="g"></path><path d="M282 548c-1 2-1 3-3 5h-2-1l1-1h2c0-2 1-3 3-4zm12-24l1-1 1 1-1 5-1 1v-1c-1-2 0-4 0-5z" class="G"></path><path d="M291 528c0-1 1-1 2-2 0-1 0-1 1-2 0 1-1 3 0 5-1 1-2 2-2 4v-1c-1-1-1-3-1-4z" class="i"></path><path d="M295 510h0c0 1 0 2-1 4l-1 1v1h-1l-1 5h1c0 2-1 2-2 3h0v-1-7c0-1 1-2 1-2l4-4z" class="a"></path><path d="M294 529v1c-2 6-5 9-9 14v-1-1h-1v-1c1-2 2-3 3-4 0 1-1 2 0 3 2-2 3-4 5-7 0-2 1-3 2-4z" class="P"></path><path d="M294 506c0-1 1-3 2-4v2h1c1 3 1 5 1 7-1 1-1 1-1 2h-2c0 1-1 2-1 3s0 2-1 3v-1-1-1-1l1-1c1-2 1-3 1-4h0c0-2 0-3-1-4z" class="o"></path><path d="M294 506c0-1 1-3 2-4v2c0 2 0 4-1 6h0c0-2 0-3-1-4z" class="T"></path><path d="M290 524c1 1 1 3 0 4l1 1h0v-1c0 1 0 3 1 4v1c-2 3-3 5-5 7-1-1 0-2 0-3s1-1 1-2c0-2 0-3 1-5 0-2 1-4 1-6h0z" class="b"></path><path d="M285 543v1c0 2-1 2-3 4h0c-2 1-3 2-3 4h-2l-1 1c-1 1-3 2-3 3-1 1-2 2-3 2 0-1 1-1 1-2h1c0-1 1-1 1-1-2 0-4 2-5 3 0 1 0 2 1 3h-1v-1h-2v1c-1 0-1 1-1 1 0 1 1 1 0 1v3h0v1l-1 1v-10c1-2 4-2 6-3 1-1 2-2 3-2 1-1 4-2 5-3 1-2 4-4 6-6l1-1z" class="H"></path><path d="M284 541v1h1v1l-1 1c-2 2-5 4-6 6-1 1-4 2-5 3-1 0-2 1-3 2-2 1-5 1-6 3v10c-1 4-2 6-5 10 0-1-1-1-1 0-1 0-2 0-3 1 2-2 3-3 4-5 3-4 4-10 3-15-1 1-1 2-1 3v-2c-1 1-2 1-3 1h0v-1c-2-2-1-3-1-6l3-1c7-2 13-5 19-9h1l4-3z" class="AE"></path><path d="M284 541v1h1v1l-1 1-1-1-3 3c-2 1-4 2-5 3-3 2-7 4-10 5-2 1-3 1-4 2 0 1 1 2 1 2v1c-1 1-1 2-1 3v-2c-1 1-2 1-3 1h0v-1c-2-2-1-3-1-6l3-1c7-2 13-5 19-9h1l4-3z" class="c"></path><path d="M257 554l3-1c0 1 0 2-1 2 1 2 2 3 2 5-1 1-2 1-3 1h0v-1c-2-2-1-3-1-6z" class="T"></path><path d="M259 555c1 2 2 3 2 5-1 1-2 1-3 1 0-2 0-4 1-6z" class="AE"></path><path d="M287 481v-1l1-1c1 1 1 1 1 2h0 1 1v2l-1 24v6h1v1s-1 1-1 2v7 1c0 2-1 4-1 6-1 2-1 3-1 5 0 1-1 1-1 2-1 1-2 2-3 4l-4 3h-1c4-4 6-8 8-13-1-1-1-1-2-1v-5c1-4 0-7 0-10l1-2v-1c1-1 1-2 1-3v-4-1c0-2 0-4-1-6v-1l1-7v-4-5z" class="AN"></path><path d="M289 519v-4l1-1h0v2h0v7h0-1v-4z" class="AJ"></path><path d="M287 481v-1l1-1c1 1 1 1 1 2l-1 20c0 3 0 5-1 8v-4-1c0-2 0-4-1-6v-1l1-7v-4-5z" class="b"></path><path d="M287 509c1-3 1-5 1-8v7l1 11v4h1 0v1c0 2-1 4-1 6-1 2-1 3-1 5 0 1-1 1-1 2-1 1-2 2-3 4l-4 3h-1c4-4 6-8 8-13-1-1-1-1-2-1v-5c1-4 0-7 0-10l1-2v-1c1-1 1-2 1-3z" class="i"></path><path d="M287 531c0-3 1-6 1-8v-15l1 11v4h1 0v1c0 2-1 4-1 6-1 2-1 3-1 5 0 1-1 1-1 2-1 1-2 2-3 4l-4 3h-1c4-4 6-8 8-13z" class="e"></path><defs><linearGradient id="AA" x1="292.304" y1="484.121" x2="312.514" y2="491.457" xlink:href="#B"><stop offset="0" stop-color="#ebca8f"></stop><stop offset="1" stop-color="#ffedb5"></stop></linearGradient></defs><path fill="url(#AA)" d="M311 470c0-1 1-2 1-3 1 12-3 23-3 34-1 1-1 2-2 4v5c-1 1-1 2-1 3-1 2-2 5-3 7l-1 3c-1 1-1 3-2 4h0 0v-2h0l3-7c0-1 0-1 1-1v-1l1-2v-2c0 1 0 1-1 1v-1h0c0-1 0-1 1-1h0l-1-1v1h-1c1-1 1-2 1-4l-2 2h-1l-2-3h0c-1 0-1-1-2-2h0-1v-2c-1 1-2 3-2 4 1 1 1 2 1 4l-4 4v-1h-1v-6l1-24c0-1 1-2 2-3h2c3-2 7-2 10-4s4-3 6-6z"></path><path d="M297 496c0-1 0-1 1-2 0 0 0-1-1-1v-1c0-2 1-2 2-3h1l1 2-1 1h2c-1 1-2 1-3 1l-2 3z" class="g"></path><path d="M295 480c3-2 7-2 10-4-1 2-2 3-3 4h-7z" class="a"></path><path d="M297 496l2-3 1 2h0c-1 1-2 1-2 3v4c-1 0-1 1-1 2h0-1v-2-1c-1-2 0-3 1-5z" class="K"></path><path d="M291 483c0-1 1-2 2-3 1 3 1 4 0 6-1 4 0 8-1 11v8 1h0c0 1-1 3 0 5l2-5c1 1 1 2 1 4l-4 4v-1h-1v-6l1-24z" class="i"></path><path d="M294 506c1 1 1 2 1 4l-4 4v-1h-1v-6l1 5 1-1 2-5z" class="Z"></path><path d="M301 491c2 1 3 3 4 5v2h-1-1v1c1 0 2 1 2 2 0 2 0 4-1 6l-2 2h-1l-2-3h0c-1 0-1-1-2-2 0-1 0-2 1-2v-4c0-2 1-2 2-3h0l-1-2c1 0 2 0 3-1h-2l1-1z" class="T"></path><path d="M302 492c0 2 1 2 1 4h-1c-1 0-1-1-2-1h0l-1-2c1 0 2 0 3-1z" class="v"></path><path d="M298 502l2-2v-2h1v2c0 1 1 1 1 2s0 3-1 4h-2 0 0c-1 0-1-1-2-2 0-1 0-2 1-2z" class="a"></path><path d="M303 499c1 0 2 1 2 2 0 2 0 4-1 6l-2 2h-1l-2-3h0 2c1-1 1-3 1-4 1-2 1-2 0-3h1z" class="s"></path><path d="M312 351h1 0c0 1 0 1 1 1 8 2 17 0 26 0h-1c-1 1-3 1-4 1l-1 1h-1v1h-4-1 0c0 2 1 2 2 3 2 0 4 1 5 1 0 1 0 1 1 1-1 0-6 0-7 1h-6 0c-3 0-8-1-11 1h0c-1 0-1 1-1 1l-2 2v2 1l1 1c0 2 2 3 3 4h1c2 2 0 17 0 20v9c0 15 1 30 0 44 0 4-1 8-1 13 0 1-1 3-1 4-1-7-3-14-8-20l-1-1 1-2h-3v-1l-1 1v1c-1-1-1-3-1-5l-1-2s0-1-1-2h0 0l-2 1v-1c-1 0-1-1-1-1 0-2-1-3-2-5 0-1 0-2-1-3h-2l-1-1c0-1 1-4 0-5v-1l1-3h1v-3-1-5-4h-1-1v-6l-1-2v-1-5c1 0 1-1 2-1 1-1 3-1 4-1h0 4l1-1h2v-1c-3-1-4-1-6-1v-2l1-1 1 2v-3l1-1h0c0-2-1-3-1-5 0-3 1-9 2-12 1-2 2-3 3-5h0c4 0 8-1 11-3z" class="AC"></path><path d="M297 376v1s-1 1-1 2l1-1 1 1-1 1-1 1v-1-3l1-1h0z" class="AD"></path><path d="M304 394v1c1 1 1 1 2 1h-1l-1 3h-1v-1h-1v-1h0c1-1 1-2 2-3z" class="AN"></path><path d="M294 379l1-1 1 2v1l1-1 1-1 2 2v1c-3-1-4-1-6-1v-2z" class="H"></path><path d="M299 394l1 1 1 1c0 1 0 1 1 1v1h1v1 1c-2-1-4-3-5-4v-1h0l1-1z" class="AA"></path><path d="M304 386c0-2 0-2-1-2v-1c2 1 4 2 5 4v2c-1-1-2-2-4-3h0z" class="U"></path><path d="M304 386c2 1 3 2 4 3 1 2 2 5 1 7 0 1 0 1-1 1s-1 0-3-1h1c-1 0-1 0-2-1v-1h0l1-1h2 0c0-1-1-2-2-2v-1h1c1-1-2-2-2-4z" class="s"></path><path d="M304 394l1-1h2l1 1c-1 1-1 1-2 1v1c-1 0-1 0-2-1v-1h0z" class="q"></path><path d="M301 425c1 2 1 4 1 6 1 3 1 7 2 9h-3v-1l-1 1v1c-1-1-1-3-1-5l1-1c1-3 1-6 0-9 0-1 1-1 1-1z" class="f"></path><path d="M301 384l3 2h0c0 2 3 3 2 4h-1v1c1 0 2 1 2 2h0-2l-1 1h0c-1 1-1 2-2 3h0c-1 0-1 0-1-1l-1-1-1-1h0c0-1 0-2 1-4l-3-3h0l-1-1h2s1 0 1-1c1-1 1-1 2-1z" class="AH"></path><path d="M305 391c1 0 2 1 2 2h0-2l-1 1v-1-2h1z" class="AE"></path><path d="M302 391h1c0 2-1 3-2 4v1l-1-1 2-4z" class="T"></path><path d="M300 390l1-1 1 1v1h0l-2 4-1-1h0c0-1 0-2 1-4z" class="c"></path><path d="M301 384l3 2h0c0 2 3 3 2 4h-1c-1 0-1 0-1-1-2-2-4-2-6-3 0 0 1 0 1-1 1-1 1-1 2-1z" class="P"></path><path d="M297 387l3 3c-1 2-1 3-1 4h0l-1 1h0v1c-2 0-2 0-4 1v2l-1 1-1 2c0 1-1 1-1 2h-1v-4h-1-1v-6h1 0 2c0-1 0-2 1-2 0 0 1-1 1 0 1 0 2 1 3 1l1-1v-1c0-2 0-1-1-2l1-2z" class="R"></path><path d="M289 394h0v6h-1v-6h1z" class="t"></path><path d="M290 400c0-1-1-3 0-4h2c2-1 4-1 6-1v1c-2 0-2 0-4 1v2l-1 1-1 2c0 1-1 1-1 2h-1v-4z" class="H"></path><path d="M297 384h4c-1 0-1 0-2 1 0 1-1 1-1 1h-2l1 1h0l-1 2c1 1 1 0 1 2v1l-1 1c-1 0-2-1-3-1 0-1-1 0-1 0-1 0-1 1-1 2h-2 0-1l-1-2v-1-5c1 0 1-1 2-1 1-1 3-1 4-1h0 4z" class="AN"></path><path d="M291 387h2 0l1 2c-1 0-3 1-5 1 1-1 2-2 2-3z" class="y"></path><path d="M290 387h1c0 1-1 2-2 3v4h-1l-1-2 1-2c0-2 1-2 2-3z" class="AM"></path><path d="M295 385l1 1 1 1h0l-1 2c1 1 1 0 1 2h-2l-1-2-1-2h0-2-1c0-1 0-1 1-1h1c1 0 2 0 3-1z" class="AE"></path><path d="M293 387c2 0 2 1 3 2s1 0 1 2h-2l-1-2-1-2z" class="I"></path><path d="M297 384h4c-1 0-1 0-2 1 0 1-1 1-1 1h-2l-1-1c-1 1-2 1-3 1h-1c-1 0-1 0-1 1-1 1-2 1-2 3l-1 2v-1-5c1 0 1-1 2-1 1-1 3-1 4-1h0 4z" class="P"></path><path d="M297 384h4c-1 0-1 0-2 1 0 1-1 1-1 1h-2l-1-1h-2-4c1-1 3-1 4-1h0 4z" class="Z"></path><path d="M290 404h1c4 5 6 9 8 15 1 2 2 3 2 6 0 0-1 0-1 1 1 3 1 6 0 9l-1 1-1-2s0-1-1-2h0 0l-2 1v-1c-1 0-1-1-1-1 0-2-1-3-2-5 0-1 0-2-1-3h-2l-1-1c0-1 1-4 0-5v-1l1-3h1v-3-1-5z" class="U"></path><path d="M292 409c-1-1-1-1-1-2h1c1 2 3 4 4 7l-2-2h0v6c0-2 0-5-1-6 0-1-1-2-1-3z" class="H"></path><path d="M290 410c1 3 0 7 0 10 0 1 1 2 1 3h-2l-1-1c0-1 1-4 0-5v-1l1-3h1v-3z" class="x"></path><path d="M299 419c1 2 2 3 2 6 0 0-1 0-1 1 1 3 1 6 0 9l-1 1-1-2h1c0-5-1-10 0-15z" class="o"></path><path d="M294 418v-6h0l2 2c1 3 2 8 2 12v8s0-1-1-2h0 0l-2 1v-1h1c0-5-1-9-2-14z" class="j"></path><path d="M292 409c0 1 1 2 1 3 1 1 1 4 1 6 1 5 2 9 2 14h-1c-1 0-1-1-1-1 0-2-1-3-2-5 0-1 0-2-1-3 0-1-1-2-1-3 1-1 1-2 2-2 0-1-1-1-1-2s0-1 1-2v-5z" class="f"></path><defs><linearGradient id="AB" x1="231.527" y1="532.33" x2="249.05" y2="549.435" xlink:href="#B"><stop offset="0" stop-color="#f3e2bf"></stop><stop offset="1" stop-color="#ffffdf"></stop></linearGradient></defs><path fill="url(#AB)" d="M273 499c1-1 3-3 5-4v-1c1 0 2 0 2-1 2 0 3-1 5-1h1v-1l1-1-1 7v1c1 2 1 4 1 6v1 4c0 1 0 2-1 3v1l-1 2c0 3 1 6 0 10v5c1 0 1 0 2 1-2 5-4 9-8 13-6 4-12 7-19 9l-3 1v1h-4-1 0-1-2v-1c-2 2-3 6-6 6-1 2-3 5-4 7-1 1-3 3-3 4l-1 3-3 3-3 3c-2 3-6 5-8 8-2 1-3 2-4 3l-3 2-1 2h-2-1l-2-2 2-1h2v-1c-1-2-2-2-3-3l1-1h0l-1-1h0c0-1 1-2 1-3 0 0-1 0-2-1 2-2 6-5 8-8 1-2 3-3 4-5l1-2 2-1 1 1c1-2 2-3 3-5 2-2 4-4 5-7l7-9c0-1 1-4 3-4 1-3 2-5 3-8 2-2 3-7 4-10 0-1 0-1 1-2v-2c-2-1-3-2-3-3v-1c-1 1-1 1-2 0 0 0-1-1-1-2h-1c2-1 2-2 4-2l-2-2h3v-1l-1-2h1c-1-2-3 0-4-2h0l1-1h1l1-1s-1-1-1-2c-1 0-1-1-1-1 0-2 0-3 1-4h2c1 0 2-1 4 0h1 3l1-1h2c0-1 1-1 2-1 1-1 2-2 4-3l1 2c1-1 2-1 4-2h0 1c-1 2-1 2 0 4l-2 7v1c2-2 3-4 4-5v1z"></path><path d="M262 500c1-2 2-2 4-3l-3 6h-1v-2-1z" class="c"></path><path d="M246 547c2-4 3-7 6-10v1c-1 1-1 1-1 2v1-1h1l-1 1c0 1 0 1-1 2-1 2-2 3-4 4z" class="G"></path><path d="M233 566l10-14v3c-1 1-2 3-2 4h-1c-3 2-4 5-6 7h-1z" class="T"></path><path d="M270 491h1c-1 2-1 2 0 4l-2 7c-1 1-3 7-3 8l-2 7c-1-1 0-1-1-1v-2c0-2 0-3 1-5s1-4 2-6l3-6v-4l1-2h0z" class="f"></path><path d="M262 501v2h1l-7 18h-1v-2-3c0-1 1-2 1-3h0c0-1 0-2-1-2h-1v-1h1 0c-1-1-1-2-1-3h2c1-1 0-2 0-3v-1c1 0 2 0 3-1v1h1c1 0 1-1 2-2z" class="I"></path><path d="M256 503c1 0 2 0 3-1v1h0c0 2 0 2-1 4h-1-1c1-1 0-2 0-3v-1z" class="k"></path><path d="M263 514v2c1 0 0 0 1 1l-4 8c1 2 1 3 1 5l-6 9c-1 1-2 2-3 2h-1l1-1h-1v1-1c0-1 0-1 1-2v-1l11-23z" class="T"></path><path d="M260 525c1 2 1 3 1 5l-6 9c-1 1-2 2-3 2h-1l1-1s1-2 2-3l6-12z" class="R"></path><path d="M240 551c-2 3-4 5-6 8-4 7-10 13-16 19-3 3-5 6-9 8h0c0-1 1-2 1-3 0 0-1 0-2-1 2-2 6-5 8-8 1 0 2-1 2-1l1 1v1c3-2 5-4 7-7 1-1 2-2 3-4l4-5c1-1 3-5 3-5l2-3v1c1 0 1-1 2-1z" class="o"></path><path d="M216 574c1 0 2-1 2-1l1 1v1c-3 3-5 6-9 8h0s-1 0-2-1c2-2 6-5 8-8z" class="h"></path><path d="M255 519v2h1c-3 8-7 14-11 21-1 3-3 6-5 9-1 0-1 1-2 1v-1c2-2 9-13 9-15h-1c-1 2-2 4-4 6h0c1-3 2-5 3-8 2-2 3-7 4-10 0-1 0-1 1-2v1h1c0-1 0-1 1-1h1c0-1 1-2 1-3h1z" class="i"></path><path d="M250 522v1h1c0-1 0-1 1-1h1l-7 14c-1 2-2 4-4 6h0l3-8c2-2 3-7 4-10 0-1 0-1 1-2z" class="c"></path><path d="M246 536h1c0 2-7 13-9 15l-2 3s-2 4-3 5l-4 5c-1 2-2 3-3 4-2 3-4 5-7 7v-1l-1-1s-1 1-2 1c1-2 3-3 4-5l1-2 2-1 1 1c1-2 2-3 3-5 2-2 4-4 5-7l7-9c0-1 1-4 3-4h0c2-2 3-4 4-6z" class="AC"></path><path d="M226 568c0-3 2-4 4-7v-1c1-1 2-1 3-1l-4 5c-1 2-2 3-3 4z" class="g"></path><path d="M221 567l2-1 1 1c-2 2-3 5-5 7l-1-1s-1 1-2 1c1-2 3-3 4-5l1-2z" class="K"></path><path d="M248 507h2s1 0 1-1c1 0 1 1 2 1h1 0c0 1 0 2 1 3h0-1v1h1c1 0 1 1 1 2h0c0 1-1 2-1 3v3h-1c0 1-1 2-1 3h-1c-1 0-1 0-1 1h-1v-1-2c-2-1-3-2-3-3v-1c-1 1-1 1-2 0 0 0-1-1-1-2h-1c2-1 2-2 4-2l-2-2h3v-1l-1-2h1z" class="P"></path><path d="M253 514l2-1h1c0 1-1 2-1 3v3h-1-1v-2c1-1 0-2 0-3z" class="d"></path><path d="M254 511h1c1 0 1 1 1 2h0-1l-2 1-2 1-1-1c1-1 1-1 1-2 2-1 2 0 3-1z" class="c"></path><path d="M243 514c2-1 2-2 4-2 1 0 2 1 3 2l1 1v1c0 1 1 3 0 4-1 0-1 0-1-1v-2l-3-1h0 0c-1 1-1 1-2 0 0 0-1-1-1-2h-1z" class="AA"></path><path d="M247 516l1-1h2v1 1l-3-1z" class="AN"></path><path d="M248 507h2s1 0 1-1c1 0 1 1 2 1h1 0c0 1 0 2 1 3h0-1v1c-1 1-1 0-3 1 0 1 0 1-1 2-1-1-2-2-3-2l-2-2h3v-1l-1-2h1z" class="H"></path><path d="M248 510c2 0 2 1 3 2 0 1 0 1-1 2-1-1-2-2-3-2l-2-2h3z" class="h"></path><path d="M266 511l3-1c-1 1-1 2-1 3 1-1 1-1 1-2 1 1 1 1 1 2 0-1 1-2 1-3v6c-2 3-2 5-3 9-1 1-2 3-3 4l-2 4-2 2c-2 1-3 3-4 4l-1 1-1-1 6-9c0-2 0-3-1-5l4-8 2-7v1z" class="P"></path><path d="M261 535c1-4 2-7 5-10-1 2-1 3-1 4l-2 4-2 2z" class="T"></path><path d="M270 513c0-1 1-2 1-3v6c-2 3-2 5-3 9-1 1-2 3-3 4 0-1 0-2 1-4 1-1 1-4 2-5 0-3 1-5 2-7z" class="U"></path><path d="M266 511l3-1c-1 1-1 2-1 3h-1v3c-1 1-1 2-1 3v1l-2 3c0 1-1 2-1 3l-2 4c0-2 0-3-1-5l4-8 2-7v1z" class="AE"></path><path d="M265 517s-1 1-1 2c1 2 0 3 0 4s-1 2-1 3l-1-1c0-1 1-2 1-3 0-2 1-4 2-5z" class="k"></path><path d="M266 511l3-1c-1 1-1 2-1 3h-1v3c-1 1-1 2-1 3v1l-2 3c0-1 1-2 0-4 0-1 1-2 1-2 0-2 1-4 1-6z" class="V"></path><path d="M261 494c1-1 2-2 4-3l1 2c1-1 2-1 4-2l-1 2-1 1h0c-1 1-2 2-2 3-2 1-3 1-4 3v1c-1 1-1 2-2 2h-1v-1c-1 1-2 1-3 1v1c0 1 1 2 0 3h-2 0-1c-1 0-1-1-2-1 0 1-1 1-1 1h-2c-1-2-3 0-4-2h0l1-1h1l1-1s-1-1-1-2c-1 0-1-1-1-1 0-2 0-3 1-4h2c1 0 2-1 4 0h1 3l1-1h2c0-1 1-1 2-1z" class="s"></path><path d="M261 494c1-1 2-2 4-3l1 2c-2 1-3 2-5 2v-1z" class="V"></path><path d="M253 496l3 2h5 0c2-1 4-4 7-4-1 1-2 2-2 3-2 1-3 1-4 3 0 0-1 1-2 1s-3-2-4-2h-2l-1-1c0-1-1-1-2-1v-1h0 1 1z" class="x"></path><path d="M248 496c1 0 2-1 4 0h-1 0v1c1 0 2 0 2 1l1 1h2c1 0 3 2 4 2s2-1 2-1v1c-1 1-1 2-2 2h-1v-1c-1 1-2 1-3 1v1c-1-1-2-1-3-2v-2c0-1-1-1-1-1-2-1-3-1-4-2h-1l1-1z" class="d"></path><path d="M253 500c2 1 2 1 3 3v1c-1-1-2-1-3-2v-2z" class="I"></path><path d="M246 501c-1 0-1-1-1-1 0-2 0-3 1-4h2l-1 1h1c1 1 2 1 4 2 0 0 1 0 1 1v2s-1 0-2-1v1h-2c-1 1-1 1-1 2v1h-1v-2s-1-1-1-2z" class="P"></path><path d="M248 501h3v1h-2c-1 1-1 1-1 2v1h-1v-2s-1-1-1-2h2z" class="i"></path><path d="M246 501c-1 0-1-1-1-1 0-2 0-3 1-4h2l-1 1h1v4h-2z" class="s"></path><path d="M251 501c1 1 2 1 2 1 1 1 2 1 3 2 0 1 1 2 0 3h-2 0-1c-1 0-1-1-2-1 0 1-1 1-1 1h-2c-1-2-3 0-4-2h0l1-1h1l1-1v2h1v-1c0-1 0-1 1-2h2v-1z" class="T"></path><path d="M249 502c2 0 3 0 4 2v1l-1 1-1-2c-1 0-1 1-2 1l-1-1c0-1 0-1 1-2z" class="e"></path><path d="M251 501c1 1 2 1 2 1 1 1 2 1 3 2 0 1 1 2 0 3h-2 0-1v-1-2c-1-2-2-2-4-2h2v-1z" class="U"></path><path d="M233 566h1c2-2 3-5 6-7h1c-1 1-1 1-1 2h0l-1 1v1l-1 1c-1 1-1 2-1 3-1 1-2 1-2 3h0l-1 2-3 4 1 1-3 3c-2 3-6 5-8 8-2 1-3 2-4 3l-3 2-1 2h-2-1l-2-2 2-1h2v-1c-1-2-2-2-3-3l1-1 1 1c3 0 6-5 8-7 5-5 9-10 14-15z" class="f"></path><path d="M235 570h0l-1 2-3 4c-1 1-1 0-2 0l6-6z" class="d"></path><path d="M229 576c1 0 1 1 2 0l1 1-3 3c-2 3-6 5-8 8-2 1-3 2-4 3l-3 2-1 2h-2-1l-2-2 2-1h2v-1c-1-2-2-2-3-3l1-1 1 1h0c1 1 2 1 2 2v1c1-1 1-1 1-2 2-1 4-3 5-4 4-1 6-6 9-8l1-1z" class="e"></path><path d="M273 499c1-1 3-3 5-4v-1c1 0 2 0 2-1 2 0 3-1 5-1h1v-1l1-1-1 7v1c1 2 1 4 1 6v1 4c0 1 0 2-1 3v1l-1 2c0 1 1 4 0 5-1 0-1 0-2-1v-1c-1 1-2 1-3 2 0 0 0 1-1 1-2 1-4 2-5 4h0c-2 0-2-1-3-1-3 3-5 7-8 9h0l2-4c1-1 2-3 3-4 1-4 1-6 3-9v-6c0 1-1 2-1 3 0-1 0-1-1-2 0 1 0 1-1 2 0-1 0-2 1-3l-3 1v-1c0-1 2-7 3-8v1c2-2 3-4 4-5v1z" class="j"></path><path d="M285 509h0l1 4-1 2c0 1 1 4 0 5-1 0-1 0-2-1v-1l1-5h0c1-1 1-2 0-2h-1v-1l2-1z" class="AH"></path><path d="M278 519c2-1 4-4 6-6l-1 5c-1 1-2 1-3 2 0 0 0 1-1 1s-1 0-2-1l1-1z" class="n"></path><path d="M271 516c3-2 9-6 12-5h1v1c-2 0-2 1-3 2-1 2-3 3-4 4l1 1-1 1c1 1 1 1 2 1-2 1-4 2-5 4h0c-2 0-2-1-3-1-3 3-5 7-8 9h0l2-4c1-1 2-3 3-4 1-4 1-6 3-9z" class="K"></path><path d="M273 499c1-1 3-3 5-4v-1c1 0 2 0 2-1 2 0 3-1 5-1h1v-1l1-1-1 7v1c1 2 1 4 1 6v1 4c0 1 0 2-1 3v1l-1-4h0c-1-3 1-9-1-12-6 0-8 6-12 11l-1 2c0 1-1 2-1 3 0-1 0-1-1-2 0 1 0 1-1 2 0-1 0-2 1-3l-3 1v-1c0-1 2-7 3-8v1c2-2 3-4 4-5v1z" class="G"></path><path d="M286 498c1 2 1 4 1 6v1 4c0 1 0 2-1 3v-14z" class="g"></path><path d="M273 499c1-1 3-3 5-4v-1c1 0 2 0 2-1 2 0 3-1 5-1h1v-1l1-1-1 7v1h-1c0-2 0-4-1-5v1c-4 1-7 2-9 5-1 1-2 3-3 4h0c0-1 0-2 1-4z" class="P"></path><path d="M269 502v1c2-2 3-4 4-5v1c-1 2-1 3-1 4h0v5l-1 2c0 1-1 2-1 3 0-1 0-1-1-2 0 1 0 1-1 2 0-1 0-2 1-3l-3 1v-1c0-1 2-7 3-8z" class="I"></path><path d="M272 503h0v5l-1 2c0 1-1 2-1 3 0-1 0-1-1-2 0 1 0 1-1 2 0-1 0-2 1-3 1-3 2-5 3-7z" class="c"></path><path d="M285 520c1-1 0-4 0-5 0 3 1 6 0 10v5c1 0 1 0 2 1-2 5-4 9-8 13-6 4-12 7-19 9l-3 1v1h-4-1 0-1-2v-1c-2 2-3 6-6 6-1 2-3 5-4 7-1 1-3 3-3 4l-1 3-3 3-1-1 3-4 1-2h0c0-2 1-2 2-3 0-1 0-2 1-3l1-1v-1l1-1h0c0-1 0-1 1-2 0-1 1-3 2-4v-3l3-5c2-1 3-2 4-4 1-1 1-1 1-2h1c1 0 2-1 3-2l1 1 1-1c1-1 2-3 4-4l2-2h0c3-2 5-6 8-9 1 0 1 1 3 1h0c1-2 3-3 5-4 1 0 1-1 1-1 1-1 2-1 3-2v1c1 1 1 1 2 1z" class="Z"></path><path d="M275 533c1-1 1-1 2-1l1-1 2 1c-1 2-2 3-3 4h-1c0-1 0-2 1-2l-1-1h-1z" class="o"></path><path d="M246 547c2-1 3-2 4-4 0 1-1 2-1 3-1 1-2 3-3 4-1 2-2 3-3 5v-3l3-5z" class="P"></path><path d="M281 534c0 2-1 3-1 4-3 3-7 5-11 6h-1c-3 1-6 0-9 0h-3c1-1 10-1 12-2 4 0 7-2 10-4h0c1-1 2-2 3-4z" class="a"></path><path d="M237 567c5-6 8-14 13-19l1 1c-3 3-5 7-8 11-1 2-3 5-4 7-1 1-3 3-3 4l-1 3-3 3-1-1 3-4 1-2h0c0-2 1-2 2-3z" class="AA"></path><path d="M285 520c1-1 0-4 0-5 0 3 1 6 0 10v5c-1 3-3 6-4 8h-1c0-1 1-2 1-4-1 2-2 3-3 4h0c-1 0-1 0-1-1 0 0 1-1 2-1 1-2 2-3 3-6-1-3 1-8 1-11 1 1 1 1 2 1z" class="P"></path><path d="M283 519c1 1 1 1 2 1-1 3-1 5-2 8 0 2-1 4-2 6s-2 3-3 4h0c-1 0-1 0-1-1 0 0 1-1 2-1 1-2 2-3 3-6-1-3 1-8 1-11z" class="k"></path><path d="M275 533h1l1 1c-1 0-1 1-1 2h1c-2 2-8 4-11 4h-2c-1 0-1 1-1 1-3 1-7 0-10 1l-1-1c1 0 2-1 3-2l1 1v1h0c2 0 4-1 6-1l3-3 1-1v1c2-1 3-2 4-3 2 0 3 0 5-1h0z" class="a"></path><path d="M275 533h1c-3 3-6 3-8 5-1 0-2 1-3 1h-1l-2 1 3-3 1-1v1c2-1 3-2 4-3 2 0 3 0 5-1h0z" class="h"></path><path d="M279 521c1 0 1-1 1-1 1-1 2-1 3-2v1c0 3-2 8-1 11h-1l-1 2-2-1-1 1c-1 0-1 0-2 1h0c-2 1-3 1-5 1 2-3 4-5 5-8 0-1 0-1-1-1 1-2 3-3 5-4z" class="a"></path><path d="M277 532c1-2 1-3 1-4 0-2 0-2 1-4v-1h1v2c0 2-2 4-2 6l-1 1z" class="U"></path><path d="M280 525l1-1h0 0v2c0 2-1 3 0 4l-1 2-2-1c0-2 2-4 2-6zm-5 1l1-1h0c1 1 1 1 1 2s-1 2-2 3-1 2-2 2c0 1-1 1-1 1h3c-2 1-3 1-5 1 2-3 4-5 5-8z" class="i"></path><path d="M263 533h0c3-2 5-6 8-9 1 0 1 1 3 1h0c1 0 1 0 1 1-1 3-3 5-5 8-1 1-2 2-4 3v-1l-1 1-3 3c-2 0-4 1-6 1h0v-1l1-1c1-1 2-3 4-4l2-2z" class="AB"></path><path d="M274 525c1 0 1 0 1 1-1 3-3 5-5 8-1 1-2 2-4 3v-1c2-3 5-6 7-10l1-1h0z" class="U"></path><path d="M263 533h0c3-2 5-6 8-9 1 0 1 1 3 1l-1 1c-1 0-2 0-3 1s-2 2-2 3c-3 4-6 8-11 9 1-1 2-3 4-4l2-2z" class="H"></path><defs><linearGradient id="AC" x1="265.315" y1="538.358" x2="261.426" y2="553.367" xlink:href="#B"><stop offset="0" stop-color="#b28e4d"></stop><stop offset="1" stop-color="#d4af74"></stop></linearGradient></defs><path fill="url(#AC)" d="M281 538c1-2 3-5 4-8v-5 5c1 0 1 0 2 1-2 5-4 9-8 13-6 4-12 7-19 9l-3 1v1h-4-1 0-1-2v-1c-2 2-3 6-6 6 3-4 5-8 8-11h0c1-1 1-2 2-2v-1h-1v-1h1c1-1 5-1 6-1 3 0 6 1 9 0h1c4-1 8-3 11-6h1z"></path><path d="M259 544c3 0 6 1 9 0v1c-5 2-10 0-15 0 1-1 5-1 6-1z" class="R"></path><path d="M249 554c1-1 2-2 4-3 3-2 6-3 10-2-3 1-6 2-8 4h-1c-1 0-1 1-2 2h0-1-2v-1z" class="a"></path><defs><linearGradient id="AD" x1="270.856" y1="528.472" x2="268.263" y2="555.489" xlink:href="#B"><stop offset="0" stop-color="#eed596"></stop><stop offset="1" stop-color="#fffee7"></stop></linearGradient></defs><path fill="url(#AD)" d="M281 538c1-2 3-5 4-8v-5 5c1 0 1 0 2 1-2 5-4 9-8 13-6 4-12 7-19 9l-3 1v1h-4-1c1-1 1-2 2-2h1c2-2 5-3 8-4 8-2 13-5 18-11z"></path><path d="M246 383c2-1 3-2 5-1 2 0 4 2 5 4 2 2 2 5 3 8 1 1 1 3 1 4 0 2 0 3 1 5h-1v1l1-1c4-1 7-1 11-1 1 0 3 0 4-1 2 0 5 0 6 1h1 1v-2l-1-2v-1h1v-1-1-1c0-1 0-3-1-4 2 0 2-1 3-1 0-1 1-2 1-3v5 1l1 2v6h1 1v4 5 1 3h-1l-1 3v1c1 1 0 4 0 5l1 1h2c1 1 1 2 1 3 1 2 2 3 2 5 0 0 0 1 1 1v1l2-1h0 0c1 1 1 2 1 2l1 2c0 2 0 4 1 5v-1l1-1v1h3l-1 2 1 1c5 6 7 13 8 20v3 1c0 1-1 2-1 3-2 3-3 4-6 6s-7 2-10 4h-2c-1 1-2 2-2 3v-2h-1-1 0c0-1 0-1-1-2l-1 1v1 5 4l-1 1v1h-1c-2 0-3 1-5 1 0 1-1 1-2 1v1c-2 1-4 3-5 4v-1c-1 1-2 3-4 5v-1l2-7c-1-2-1-2 0-4h-1 0c-2 1-3 1-4 2l-1-2c-2 1-3 2-4 3-1 0-2 0-2 1h-2l-1 1h-3-1c-2-1-3 0-4 0h-2c-1 1-1 2-1 4 0 0 0 1 1 1 0 1 1 2 1 2l-1 1h-1l-1 1h0-1l-1-1v-3c0-4 0-8 1-11v-21-18l-1-68c1 2 1 3 1 4l1 1c0-2 1-4 2-5z" class="AC"></path><path d="M246 469c-1-1-1-2-1-2l1-1 1 1 1 2h-2z" class="T"></path><path d="M250 485c1-1 1-2 2-2s2 0 3 1l-2 1h-3z" class="Z"></path><path d="M247 467h4v2h-2-1l-1-2z" class="I"></path><path d="M255 484v1l1-1c1 0 1 0 2 1-1 1-2 1-3 3h-2v-1l-1-1 1-1 2-1z" class="i"></path><path d="M246 469h2 1 3v2c-1 0-1 0-1 1l-1-1h-1l-1 1h0l-1-1-1-2z" class="j"></path><path d="M261 411h1 0 1 0v2c1 1 0 3 1 4 0 1 1 3 0 5h0-1v-6c-1-2-1-3-2-5z" class="H"></path><path d="M259 403h1v1 1 1c-2 0-4 1-5 3v1s-1 0-2 1v-1l1-1v-2c1-1 4-3 5-4z" class="i"></path><path d="M254 463c1 0 1 0 2 1l-1 2v1l-1 1-2 1h-3 2v-2c1-1 2-1 3-2v-2z" class="U"></path><path d="M254 463c1 0 1 0 2 1l-1 2c-1 0-1-1-1-1v-2z" class="I"></path><path d="M249 485h1 3l-1 1 1 1v1c0 1-1 1-1 2h-2l-1-1c0-1 1-2 1-3l-1-1z" class="U"></path><path d="M252 455c1 0 2 1 3 1s2-1 3-1v1l-1 1h0c-1-1-2-1-3-1l-1 1c-1 0-1 0-1 1h0v1c-2 0-2-1-4-1v-1-1c2 0 3-1 4-1z" class="G"></path><path d="M262 428c2 0 4 2 5 4l1 1 1-1s0-1 1-1v5l-1 1h-1c-2-4-4-6-7-9h1z" class="U"></path><path d="M250 453v-2c2-2 4-6 7-8h0c1-1 1-2 2-3h1c-1 1-1 2-2 3v1h-1v1l-2 2 1 1-3 3-3 2z" class="O"></path><path d="M255 410v-1c1-2 3-3 5-3h2 4l-1 1c1 1 1 1 2 1l-1 1h-1v-1c-3-1-7 1-10 2z" class="q"></path><path d="M262 406h4l-1 1c1 1 1 1 2 1l-1 1h-1v-1h0c-1-1-2-1-3-1v-1z" class="x"></path><path d="M263 422v1c-1-1-1-1-1-2s-1-2-1-3v-1c-1-1-1-1-1-2s-1-1-1-1v-3h0c0-1 0-1 1-1l1 1c1 2 1 3 2 5v6z" class="g"></path><path d="M258 485c1 1 1 1 1 2s1 1 1 1c0 2-2 2-3 3 1 0 1 0 1 1h-2l-1-1c-1-1-2-1-3-1 0-1 1-1 1-2h2c1-2 2-2 3-3z" class="p"></path><path d="M255 488v2 1c-1-1-2-1-3-1 0-1 1-1 1-2h2z" class="b"></path><path d="M244 388c0-2 1-4 2-5h2c0 1-1 3-1 3l-1 1 1 1h1c0 2-1 4-2 5h-1c-1-2-1-3-1-5z" class="I"></path><path d="M266 410c2 4 2 9 2 14l2 7c-1 0-1 1-1 1-1 0-1-1-1-1v-2c0-3-1-6-2-10l-1-9h1z" class="T"></path><path d="M249 485l1 1c0 1-1 2-1 3l1 1c1 2 3 4 5 4 1 1 3 1 4 1h-2l-1 1h-3-1c-2-1-3 0-4 0h-2l2-2h2 0c-1-1-1-2-1-3l-1-2c0-1 1-3 1-4z" class="I"></path><path d="M256 386c2 2 2 5 3 8 1 1 1 3 1 4 0 2 0 3 1 5h-1-1l-2-10c-1-2-1-4-2-5l1-2z" class="o"></path><path d="M246 427c1 0 2 0 3-1h2c2-1 3-1 4-1 2 0 5 1 7 3h-1l-2-1c-2-1-5-1-8 0-3 2-5 6-6 9v-7-1l1-1z" class="k"></path><path d="M246 383c2-1 3-2 5-1 2 0 4 2 5 4l-1 2c-1-1-2-2-3-2l-4 2h-1l-1-1 1-1s1-2 1-3h-2z" class="e"></path><path d="M247 386c1-1 1-2 3-3 1 1 2 1 3 2h0l-1 1-4 2h-1l-1-1 1-1z" class="AJ"></path><path d="M245 428v-1l1-1c-1 0 0 0-1-1 2-1 3-2 5-3l1-1c1 0 2 0 4-1h5l1 2v2c-4-1-8-2-11 0-2 1-3 1-4 3l-1 1z" class="h"></path><path d="M243 451v10 6c0 2 1 3 0 4v7c0 5 0 9 1 14h0 0c-1 4 0 9-1 12 0 0 1 0 1 1h0-1l-1-1v-3c0-4 0-8 1-11v-21-18z" class="j"></path><path d="M258 492c3-1 4-3 7-4l1 1-1 2c-2 1-3 2-4 3-1 0-2 0-2 1-1 0-3 0-4-1-2 0-4-2-5-4h2c1 0 2 0 3 1l1 1h2z" class="r"></path><path d="M270 436c1 4 1 9 1 13 0-1 0-2-1-3l-1 1h-3v-2h-2v-1l-3 1-1-1h1l2-2h1c1-1 1-1 3-1 0-1 1 0 2-1l-1-3h1l1-1z" class="f"></path><path d="M266 445c1 0 2 0 3-1v1c1 0 1 1 1 1l-1 1h-3v-2z" class="b"></path><path d="M267 441s1 0 2 1v1h0c-2 0-4 1-5 1l-3 1-1-1h1l2-2h1c1-1 1-1 3-1z" class="p"></path><path d="M246 427c1-2 2-2 4-3 3-2 7-1 11 0 1 1 2 1 4 2 1 1 1 3 3 3v2s0 1 1 1l-1 1-1-1c-1-2-3-4-5-4-2-2-5-3-7-3-1 0-2 0-4 1h-2c-1 1-2 1-3 1z" class="a"></path><path d="M255 425h-1c1-1 2-1 3-1 2 1 3 2 5 2 2 1 4 4 6 5 0 0 0 1 1 1l-1 1-1-1c-1-2-3-4-5-4-2-2-5-3-7-3z" class="AD"></path><path d="M264 444v1h2v2h-3v1c-2 0-5 1-7 2v1h1 0l-1 1v-1c2 0 2 1 3 1l-2 1c-1 0-1 0-1 1h-4-1l1 1c-1 0-2 1-4 1 1-1 2-2 2-3l3-2 3-3 5-3 3-1z" class="Z"></path><path d="M264 445h2v2h-3-3v-1c2 0 3-1 4-1z" class="O"></path><path d="M250 453l3-2 1 1h0v2h-2-1l1 1c-1 0-2 1-4 1 1-1 2-2 2-3z" class="c"></path><path d="M254 468l1 1h2c1 1 1 1 1 2 1 0 2 1 2 1v1 2l1 1h1 1l1 1c-1 0-2-1-3-1-1 1-1 1-1 2h-1v1c0-1-1-2-2-2-1-1-4-2-5-2-1-1-1-1-1-2-1-1-2-1-3-1l1-1h1l1 1c0-1 0-1 1-1v-2l2-1z" class="I"></path><path d="M254 468l1 1h2c1 1 1 1 1 2h-6v-2l2-1z" class="O"></path><path d="M248 472l1-1h1l1 1h6l1 1v2c-2 0-4-1-6-1l-1-1c-1-1-2-1-3-1z" class="b"></path><path d="M259 479v-1h1c0-1 0-1 1-2 1 0 2 1 3 1l3 2c0 1 0 1-1 2h1c1-1 1-1 3-2v1 2c-1 0-1 0-1 1v1c-1 1-1 1-2 1v1h-1l-1 1 1 1h-3c-1 0-2-2-3-3-1 0-1-3-1-4v-1-1z" class="Z"></path><path d="M260 485v-1h1c1 1 2 1 3 2h2l-1 1 1 1h-3c-1 0-2-2-3-3z" class="k"></path><path d="M270 446c1 1 1 2 1 3v4c0 3-1 5 0 7v1l-1 1-1-2c0-1 0-1-1-2 1 0 1 0 1-1l-2-2c-1 0-3-1-5-1 1-1 1-1 1-2h-4c-1 0-1-1-3-1v1l1-1h0-1v-1c2-1 5-2 7-2v-1h3 3l1-1z" class="U"></path><path d="M269 454c1 1 1 1 1 2l-1 1h0l-2-2h0c1-1 1-1 2-1z" class="O"></path><path d="M266 447h3 1l-1 1h-5-1v-1h3z" class="P"></path><path d="M263 452l3 1s2 0 3 1c-1 0-1 0-2 1h0c-1 0-3-1-5-1 1-1 1-1 1-2z" class="i"></path><path d="M263 448h1c2 0 5 0 6 1 0 1 0 1-1 1v2l-3 1-3-1h-4c-1 0-1-1-3-1v1l1-1h0-1v-1c2-1 5-2 7-2z" class="P"></path><path d="M263 448h1c2 0 5 0 6 1 0 1 0 1-1 1h-9-3v1h-1v-1c2-1 5-2 7-2z" class="AD"></path><path d="M254 456c1 0 2 0 3 1h0c3 2 7 5 8 8 1 2 1 5 0 8h0 1v1h0l1 1c0 1-1 1-1 1-1 0-1 0-2-1h-4v-2-1s-1-1-2-1c0-1 0-1-1-2h-2l-1-1 1-1v-1l1-2c-1-1-1-1-2-1v-4c0-1-1-2-1-2l1-1z" class="AC"></path><path d="M260 473c2 0 3 1 4 2h-4v-2z" class="h"></path><path d="M255 467l2-1 1 1-1 2h-2l-1-1 1-1z" class="b"></path><path d="M254 456c0 1 2 2 2 3v5c-1-1-1-1-2-1v-4c0-1-1-2-1-2l1-1z" class="U"></path><path d="M259 452h4c0 1 0 1-1 2 2 0 4 1 5 1l2 2c0 1 0 1-1 1 1 1 1 1 1 2l1 2 1-1v-1c0 1 0 3 1 4h0l1-1v2h0l1 4-1 3h-1c-1-1-1-1-1-2h0l-2 2c0 2-1 3-1 4 0-1 0-1-1-1l-1-1h0v-1h-1 0c1-3 1-6 0-8-1-3-5-6-8-8l1-1v-1c-1 0-2 1-3 1s-2-1-3-1l-1-1h1 4c0-1 0-1 1-1l2-1z" class="H"></path><path d="M263 456c2 0 3 1 5 2 1 1 1 1 1 2s1 3 0 4h0c-2-2-5-5-6-8z" class="j"></path><path d="M257 457l1-1c5 3 8 6 9 11v5l-1 1h-1 0c1-3 1-6 0-8-1-3-5-6-8-8z" class="U"></path><path d="M259 452h4c0 1 0 1-1 2 2 0 4 1 5 1l2 2c0 1 0 1-1 1-2-1-3-2-5-2l-5-1c-1 0-2 1-3 1s-2-1-3-1l-1-1h1 4c0-1 0-1 1-1l2-1z" class="s"></path><path d="M259 452h4c0 1 0 1-1 2h-6c0-1 0-1 1-1l2-1z" class="U"></path><path d="M271 460c0 1 0 3 1 4h0l1-1v2h0l1 4-1 3h-1c-1-1-1-1-1-2h0l-2 2c-1-1 0-3 0-5v-3h0c1-1 0-3 0-4l1 2 1-1v-1z" class="o"></path><path d="M272 464l1-1v2 6h-1v-7z" class="Z"></path><path d="M267 408h0l3 1v1h2l2 2 1 1c-1 1-1 2-1 3 1 3 2 8 2 10h-1l-1 1 2 12c0 2 0 13 1 14-1 1-1 1-1 2v1c1 3 1 6 1 10 0 1 0 3-1 4l-2-1-1-4h0v-2l-1 1h0c-1-1-1-3-1-4-1-2 0-4 0-7v-4c0-4 0-9-1-13v-5l-2-7c0-5 0-10-2-14h-1v-1h1l1-1z" class="O"></path><path d="M272 410l2 2 1 1c-1 1-1 2-1 3 1 3 2 8 2 10h-1l-1 1h0l-2-17z" class="G"></path><path d="M272 455c0-1 0-2 1-2h0l1 2-1 10h0v-2l-1 1h0c-1-1-1-3-1-4-1-2 0-4 0-7v1s0 1 1 1h0z" class="I"></path><path d="M271 453v1s0 1 1 1v9c-1-1-1-3-1-4-1-2 0-4 0-7z" class="H"></path><path d="M272 428c1 3 1 6 1 10l1 17-1-2h0c-1 0-1 1-1 2l-1-25h1v-2z" class="f"></path><path d="M268 424l1-2h0 0c1 1 1 0 1 1h0l1 7 1 25h0c-1 0-1-1-1-1v-1-4c0-4 0-9-1-13v-5l-2-7z" class="p"></path><path d="M267 408h0l3 1v1h0l2 18v2h-1l-1-7h0c0-1 0 0-1-1h0 0l-1 2c0-5 0-10-2-14h-1v-1h1l1-1z" class="U"></path><path d="M266 410l1-1c1 2 1 4 2 6l1 8h0c0-1 0 0-1-1h0 0l-1 2c0-5 0-10-2-14z" class="b"></path><path d="M294 474c1 0 4-1 5-2h0c1-1 2-1 3-2h1 2c1-1 2-1 3-2s1-3 2-4v2 3l1 1c-2 3-3 4-6 6s-7 2-10 4h-2c-1 1-2 2-2 3v-2h-1-1 0c0-1 0-1-1-2l-1 1v1 5 4l-1 1v1h-1c-2 0-3 1-5 1 0 1-1 1-2 1v1c-2 1-4 3-5 4v-1c-1 1-2 3-4 5v-1l2-7c-1-2-1-2 0-4h-1 0c-2 1-3 1-4 2l-1-2 1-2h1l-1-1-1-1 1-1h1v-1c1 0 1 0 2-1v-1c0-1 0-1 1-1v-2-1c-2 1-2 1-3 2h-1c1-1 1-1 1-2l-3-2-1-1h-1-1l-1-1h4c1 1 1 1 2 1 0 0 1 0 1-1 1 0 1 0 1 1 0-1 1-2 1-4l2-2h0c0 1 0 1 1 2h1l1-3 2 1 2 1c2 1 3 1 5 2h1v-1l1-1h0v3h1 1c1 1 1 1 2 1l1-1h4z" class="e"></path><path d="M289 481v-3h3l1-1h2v1c-1 1-3 1-4 3h-1-1z" class="q"></path><path d="M276 482h1 3 0c1 0 1 0 1-1h1l1 1-2 1c-1 0-2 2-3 3l-1-1h-1l-2-2h0l1-1h1z" class="s"></path><path d="M277 475h8c1 1 2 1 4 1h2c-1 1-1 1-2 1s-1 0-2 1h-3l-7 1c-1 0-2 0-3 1h-4v-1h0 1l1-1h1c1 0 2 0 4-1h2c-1-1-2 0-2-2z" class="P"></path><path d="M277 475h8c1 1 2 1 4 1l-10 1c-1-1-2 0-2-2z" class="H"></path><path d="M260 475h4c1 1 1 1 2 1 0 0 1 0 1-1 1 0 1 0 1 1h4c2-1 3-1 5-1 0 2 1 1 2 2h-2c-2 1-3 1-4 1h-1l-1 1h-1 0c-2 1-2 1-3 2h-1c1-1 1-1 1-2l-3-2-1-1h-1-1l-1-1z" class="b"></path><path d="M271 479c-1-1-2 0-3-1l1-1h2c1 0 1 1 1 1l-1 1zm1-3c2-1 3-1 5-1 0 2 1 1 2 2h-2c-1 0-2 0-3 1l-2-2z" class="p"></path><path d="M294 474c1 0 4-1 5-2h0c1-1 2-1 3-2h1 2c1-1 2-1 3-2s1-3 2-4v2 3c-2 3-5 5-8 6h-3c-2 0-4 0-6 1h-2-2c-2 0-3 0-4-1v-1h1 1c1 1 1 1 2 1l1-1h4z" class="h"></path><path d="M286 474h1c1 1 1 1 2 1l1-1h4c-1 0-2 0-3 1h0l2 1h-2-2c-2 0-3 0-4-1v-1h1z" class="K"></path><path d="M274 469l2 1 2 1c2 1 3 1 5 2h1v-1l1-1h0v3 1h-8c-2 0-3 0-5 1h-4c0-1 1-2 1-4l2-2h0c0 1 0 1 1 2h1l1-3z" class="h"></path><path d="M270 480h4c1-1 2-1 3-1 1 1 0 2 0 3h0-1-1l-1 1h0l2 2h1l-1 1c0 1 0 1 1 1 0 1 0 2 1 2-2 1-3 1-4 1h-1l-1 4-1 1c-1-2-1-2 0-4h-1 0c-2 1-3 1-4 2l-1-2 1-2h1l-1-1-1-1 1-1h1v-1c1 0 1 0 2-1v-1c0-1 0-1 1-1v-2z" class="V"></path><path d="M275 486v1s0 1 1 2h-1c-1 0-2-1-3-1v-1l3-1z" class="R"></path><path d="M268 486c1 0 2-1 2-1 1-1 2-1 2-1l1-1 2 3-3 1v1l-1 1v-1c-1-1-2-1-3-2z" class="e"></path><path d="M268 486c1 1 2 1 3 2v1s-1 1-1 2h0c-2 1-3 1-4 2l-1-2 1-2h1 0c0-1 0-2 1-3z" class="d"></path><path d="M268 486c1 1 2 1 3 2-1 1-2 2-3 2h-1v-1c0-1 0-2 1-3z" class="AI"></path><path d="M283 482h0c1-1 2-2 3-2l1 1v5 4l-1 1v1h-1c-2 0-3 1-5 1 0 1-1 1-2 1v1c-2 1-4 3-5 4v-1c-1 1-2 3-4 5v-1l2-7 1-1 1-4h1c1 0 2 0 4-1-1 0-1-1-1-2-1 0-1 0-1-1l1-1 1 1c1-1 2-3 3-3l2-1z" class="R"></path><path d="M283 482h0c1-1 2-2 3-2l1 1c-1 0-1 1-2 1-2 0-3 3-4 4-1-1 0-2 0-3l2-1z" class="AI"></path><path d="M273 498c1-1 1-2 2-3 2-1 6-3 7-4 1 0 4-2 4-3s0-1 1-2v4l-1 1v1h-1c-2 0-3 1-5 1 0 1-1 1-2 1v1c-2 1-4 3-5 4v-1z" class="V"></path><path d="M278 489c3-1 5-3 7-5v1 1s-1 2-2 3c-1 0-1 1-2 1l-1 1c-2 1-5 3-8 3h0l1-4h1c1 0 2 0 4-1z" class="x"></path><path d="M278 489c3-1 5-3 7-5v1 1c-1 0-1 1-2 2-2 2-6 3-8 3h-2 0c0-1 0-1 1-1s2 0 4-1z" class="t"></path><path d="M287 386v5 1l1 2v6h1 1v4 5 1 3h-1l-1 3v1c1 1 0 4 0 5l1 1h2c1 1 1 2 1 3 1 2 2 3 2 5 0 0 0 1 1 1v1l2-1h0 0c1 1 1 2 1 2l1 2c0 2 0 4 1 5v-1l1-1v1h3l-1 2 1 1c5 6 7 13 8 20v3 1c0 1-1 2-1 3l-1-1v-3-2c-1 1-1 3-2 4s-2 1-3 2h-2-1c-1 1-2 1-3 2h0c-1 1-4 2-5 2h-4l-1 1c-1 0-1 0-2-1h-1-1v-3h0l-1 1v1h-1c-2-1-3-1-5-2l-2-1c1-1 1-3 1-4 0-4 0-7-1-10v-1c0-1 0-1 1-2-1-1-1-12-1-14l-2-12 1-1h1c0-2-1-7-2-10 0-1 0-2 1-3l-1-1-2-2h-2v-1l-3-1h0c-1 0-1 0-2-1l1-1h-4-2v-1-1l1-1c4-1 7-1 11-1 1 0 3 0 4-1 2 0 5 0 6 1h1 1v-2l-1-2v-1h1v-1-1-1c0-1 0-3-1-4 2 0 2-1 3-1 0-1 1-2 1-3z" class="a"></path><path d="M289 437h1v1 10h-1v3-14z" class="s"></path><path d="M289 451v-3h1v4 1 8 13l-1 1c-1-2 0-6 0-9v-15z" class="AH"></path><path d="M287 386v5 1l1 2v6h1 1v4 5 1 3h-1l-1 3v7c0-1 0-3-1-4h0c1-1 0-3 0-4 1-1 1-1 0-2v-5 1h-1v-1h0 1v-2c-1 0-1 1-1 1h-1c1 3 0 6 1 8v8l-1-1c0-1-1-1-1-2-1-1-1-1-1-3l-3-3c-1 0-1 0-1-1h0 1v-2l2 1c0-1 1-1 1-2l2 1c0-3-1-6-1-9v-2l-1-2v-1h1v-1-1-1c0-1 0-3-1-4 2 0 2-1 3-1 0-1 1-2 1-3z" class="T"></path><path d="M285 411c1 4 0 7 0 11 0-1-1-1-1-2-1-1-1-1-1-3l1 2c1-1 0-3 1-4v-4z" class="V"></path><path d="M287 386v5 1l1 2v6 7-3c-1-1-1-1-1-2s1-2 0-3c-1-2 0-3-1-5l-1-1c1-1 1-3 1-4s1-2 1-3z" class="Z"></path><path d="M288 400h1 1v4 5 1 3h-1c-1-2-1-4-1-6v-7z" class="AI"></path><path d="M289 413v-4h1v1 3h-1z" class="AO"></path><path d="M283 410l2 1h0v4c-1 1 0 3-1 4l-1-2-3-3c-1 0-1 0-1-1h0 1v-2l2 1c0-1 1-1 1-2z" class="c"></path><path d="M283 410l2 1h0v4l-3-3c0-1 1-1 1-2z" class="x"></path><path d="M283 390c2 0 2-1 3-1 0 1 0 3-1 4l1 1v5c0 1 1 4 1 5l-1 1c0-1 0-1-1-1v-3l-1-1-1-2v-1h1v-1-1-1c0-1 0-3-1-4z" class="f"></path><path d="M276 401c2 0 5 0 6 1h1 1c0 3 1 6 1 9l-2-1c0 1-1 1-1 2l-2-1v2h-1c0-1-1-2-3-2v1c1 1 1 1 0 3l-1-2-1-1-2-2h-2v-1l-3-1h0c-1 0-1 0-2-1l1-1h-4-2v-1-1l1-1c4-1 7-1 11-1 1 0 3 0 4-1z" class="R"></path><path d="M281 408l2 2c0 1-1 1-1 2l-2-1v-1l1-2z" class="AJ"></path><path d="M266 406s1 0 2 1h2l1 1-1 1-3-1h0c-1 0-1 0-2-1l1-1z" class="t"></path><path d="M268 407h2l1 1-1 1-3-1h2-1v-1z" class="AI"></path><path d="M271 407c2-1 4 0 5 0l3 4c-1-1-3-1-5-2-1-1-2-1-3-2z" class="I"></path><path d="M271 407c1 1 2 1 3 2v3h0l-2-2h-2v-1l1-1-1-1h1z" class="q"></path><path d="M276 401c2 0 5 0 6 1h1 1c0 3 1 6 1 9l-2-1-2-2c-1-1-4-3-6-3-1-1-2-1-2-1l-6-1c-2 1-4 1-7 2v-1l1-1c4-1 7-1 11-1 1 0 3 0 4-1z" class="AC"></path><path d="M260 404l1-1c4-1 7-1 11-1h4v1c-3 1-6 0-9 0-2 1-4 1-7 2v-1z" class="H"></path><path d="M283 402h1c0 3 1 6 1 9l-2-1-2-2c-1-1-4-3-6-3-1-1-2-1-2-1h3c2 0 3 1 5 1 0 0 1 0 1 1 1-1 0-2 0-3v-1h1z" class="AE"></path><path d="M288 416v1c1 1 0 4 0 5l1 1h2c1 1 1 2 1 3 1 2 2 3 2 5 0 0 0 1 1 1v1l2-1h0 0c1 1 1 2 1 2l1 2c0 2 0 4 1 5v7c1 1 1 1 2 1l-1 1c0 1-1 2-2 3h0-1v-2c-1 0-1 0-2 1 0-1-1-2-1-2-2 0-3 2-5 3v-1-4-10-1h-1l-1-14v-7z" class="P"></path><path d="M290 448h1 0c0 1 0 2 1 2l-2 2v-4z" class="Z"></path><path d="M290 438h1v2c0 2 1 3 1 5 0 1 0 2-1 3h-1v-10z" class="G"></path><path d="M293 433c1 2 1 4 1 6v2h0c0 2 1 5 1 8h-1c0-1 0-2-1-2v-1h0c1-3 0-5-1-7v-1c1-1 1-3 1-5z" class="U"></path><path d="M295 433l2-1h0 0c1 1 1 2 1 2l1 2c0 2 0 4 1 5v7c-2-1-2-5-3-6s-2-1-3-1h0v-2c1-1 0-3 0-4l1-1v-1z" class="r"></path><path d="M288 416v1c1 1 0 4 0 5l1 1h2c1 1 1 2 1 3 1 2 2 3 2 5 0 0 0 1 1 1v1 1l-1 1c0 1 1 3 0 4 0-2 0-4-1-6 0 2 0 4-1 5l-2-2v1h-1l-1-14v-7z" class="AH"></path><path d="M291 429c0-2-1-3-1-4l1-1 1 2c1 2 2 3 2 5 0 0 0 1 1 1v1 1l-1 1c0 1 1 3 0 4 0-2 0-4-1-6 0-1-1-3-2-4z" class="b"></path><path d="M291 429c1 1 2 3 2 4 0 2 0 4-1 5l-2-2v-6l1-1z" class="f"></path><path d="M300 441v-1l1-1v1h3l-1 2 1 1c5 6 7 13 8 20v3 1c0 1-1 2-1 3l-1-1v-3-2c-1 1-1 3-2 4s-2 1-3 2h-2-1c-1 1-2 1-3 2h0c-1 1-4 2-5 2h-4v-13-8c2-1 3-3 5-3 0 0 1 1 1 2 1-1 1-1 2-1v2h1 0c1-1 2-2 2-3l1-1c-1 0-1 0-2-1v-7z" class="AC"></path><path d="M300 441v-1l1-1v1h3l-1 2 1 1c5 6 7 13 8 20v3 1c0 1-1 2-1 3l-1-1v-3l1-1c-1-3-2-7-3-9-2-2-2-4-3-5v-1-1l-1-1c0-1 0-1-1-2v2c1 1 1 1 1 3h-1v-2h0c-1 1-1 1-1 2v2l-1 1h-1c-1 0-2 0-2 1h0-1c-1-1-2-1-2-2s-1-1-1-1l-1-1c0 1-2 2-2 3-1 1-1 5-1 7v-8c2-1 3-3 5-3 0 0 1 1 1 2 1-1 1-1 2-1v2h1 0c1-1 2-2 2-3l1-1c-1 0-1 0-2-1v-7z" class="o"></path><path d="M299 453c1-1 2-2 2-3 0 1 1 2 0 3h-2z" class="a"></path><path d="M311 465v-2h1v3 1c0 1-1 2-1 3l-1-1v-3l1-1z" class="f"></path><path d="M300 441v-1l1-1v1h3l-1 2 1 1v1h-1c-1 1-1 4-1 5-1 0-1 0-2-1v-7z" class="s"></path><path d="M301 440h3l-1 2v1h-1c-1-1-1-2-1-3z" class="k"></path><path d="M276 415c1-2 1-2 0-3v-1c2 0 3 1 3 2h0c0 1 0 1 1 1l3 3c0 2 0 2 1 3 0 1 1 1 1 2l1 1v9l1 42h0-1-1v-3h0l-1 1v1h-1c-2-1-3-1-5-2l-2-1c1-1 1-3 1-4 0-4 0-7-1-10v-1c0-1 0-1 1-2-1-1-1-12-1-14l-2-12 1-1h1c0-2-1-7-2-10 0-1 0-2 1-3l1 2z" class="i"></path><path d="M283 420h1c0 1 1 1 1 2l1 1v9l-1-2h0c-1-2 0-6-1-8 0 0-1-1-1-2z" class="e"></path><path d="M274 427l1-1h1l2 14v2c-1-1-2-2-2-3l-2-12z" class="P"></path><path d="M276 415c1-2 1-2 0-3v-1c2 0 3 1 3 2h0c0 1 0 1 1 1l3 3c0 2 0 2 1 3h-1l-7-5z" class="AE"></path><path d="M285 430h0l1 2 1 42h0-1l-1-44z" class="d"></path><path d="M276 439c0 1 1 2 2 3h1l1-1h0c1 1 0 4 0 5h1c0 3 0 5-1 8s0 6 0 9c-1 3-2 5-2 8h0l-2-1c1-1 1-3 1-4 0-4 0-7-1-10v-1c0-1 0-1 1-2-1-1-1-12-1-14z" class="G"></path><path d="M279 442l1-1h0c1 1 0 4 0 5l-1 7v-11z" class="O"></path><path d="M276 456l1-1c1 0 1 0 2-1h0c0 1 0 2-1 2v1l1 1c0 1-1 6-1 7 0 0 0 1-1 1h0c0-4 0-7-1-10z" class="V"></path><path d="M327 370l3 1c8 0 17 1 25 2l10 2c2 1 5 2 8 2 0 0 0-1 1-1 4 2 11 3 15 6l-1 1h0c1 0 1 1 1 2 5 3 10 6 15 10 0 4 1 6 4 9 2 2 3 4 5 6v2l4 8c1 1 1 3 2 4l1 3 2 10 1 4v1l1 3c-1 2-5 3-7 5-4 3-9 6-13 9-2 2-4 4-6 5h-1c-5 6-10 12-14 18-6 8-12 16-16 25-1 3-2 5-3 7-1 4-3 7-4 11l-1 5-1 4v1c0 1-1 3-1 5 0 1 0 1 1 1h0v1h0c0 1 0 3-1 4-1 0-1 0-1 1-1 0-2 1-2 2v3l-2 2h0-1v1l-1 1c2 0 4 0 5 1h0l-1 1h-7c2 1 6 4 9 4l1 1h-2 0 0c-2-1-3-1-4-1s-3-1-5-2v1 1c-2-1-3-2-5-3 0 0-1-1-1-2h-2c-1-1-2-1-3-2 0-2-1-3-2-4-2-3-5-8-8-11 0-1-1-3-2-4l-1-1c1 0 1-1 1-1v-1h1c0-1-1-1-1-2-1 1-2 2-2 3h-1v-1c0-1 0-2 1-2v-1c0-1 0-1-1-2l1-1h-2c-2 2-3 5-4 7l-2 2s-1 0-1 1l-1-1c0-2 1-4 2-7h-1l-2 2 4-12c8-22 12-46 13-70v-12-60h0c0-2-1-5 0-7z" class="Y"></path><path d="M364 416c0-2 0-2 1-4h0l2 1-1 2h-1 0l-1 1z" class="B"></path><path d="M392 410c0-1 1-2 2-3v1h1c0 1-1 2-1 4 0-1-1-1-1-2h-1z" class="C"></path><path d="M356 385l1-1c-1-1-1-2-2-2h-1v-1h1c1 1 2 1 3 2 0 1 0 1-1 2l-1 1v-1zm-9 4l1-1c-1-1-1-1-1-2s1-1 1-1h0v3c1 1 1 1 2 1l-1 2-1 1v-1-2h-1 0z" class="L"></path><path d="M325 483h1 2 1 0v8 3-9c-1 0-3 0-4-1v-1z" class="l"></path><path d="M329 515l2-2c1 0 1 0 2 1v1c-1 0-1 1-2 2-1-1-2-1-2-2z" class="AM"></path><path d="M353 387c-2-1-2-2-2-4-1-2-1-2-3-3h1s1 0 2 1 2 2 1 3c0 1 1 2 2 3h-1z" class="L"></path><path d="M329 508h1l1 1c-1 1-2 3-3 5 0 2 0 2-1 3l-1-1c0-1 0-2 1-3h0l2-5z" class="AL"></path><path d="M334 501v1c-1 1-4 0-5 2h0-3c-1-1-1-1-1-2 3 0 6-1 9-1z" class="l"></path><path d="M335 398h0c0-3-1-6 0-10l6 1h-3c-2 2-1 3-3 6v3z" class="L"></path><path d="M334 450c0-1-1-1-1-2-1 0-1 0-2-1h0c1 0 2-1 4-1 0 2 2 2 1 4v5c0-1-1-2-1-2v-1-1s-1 0-1-1z" class="F"></path><path d="M364 416l1-1h0 1c1 1 1 2 2 2l-1 1-1 1-2 2-2 2c0-2 0-3 1-4 0 0 1 0 1-1 1 0 1-1 1-2h-1z" class="B"></path><path d="M364 416l1-1h0 1c1 1 1 2 2 2l-1 1-1 1c0-1 0-1-1-1v-2h-1z" class="E"></path><path d="M360 389l10 1c2 0 4 1 5 0h1v1h-1c-3 1-7 0-11 0h-4v-2z" class="L"></path><path d="M375 412l2-1 1 1c0 1 0 2-1 2 0 1 1 2 0 2-1 2-2 3-4 4 0-2 1-3 2-4v-1-1-1-1z" class="B"></path><path d="M338 435c2-1 4-1 5-1 0 1-1 2-1 3l1 1 1 1h-7c1-1 1-2 2-3l-1-1h0z" class="t"></path><path d="M330 506h1c0 1 1 2 1 2h1v1h0-1-1l-1-1h-1l-2 5h0l-1-2c0-1 0-3 1-5h3z" class="C"></path><path d="M330 506h1c0 1 1 2 1 2h1v1h0-1-1l-1-1h-1-2v-1l3-1z" class="E"></path><path d="M331 509h1l2 2c0 1 0 1 1 2v1h-2c-1-1-1-1-2-1l-2 2-1-1c1-2 2-4 3-5z" class="S"></path><path d="M367 430c1-3 2-7 4-9l1 1c-1 2-2 3-2 5-1 2-1 5-1 7l-2-2v-2z" class="Q"></path><path d="M322 524h-1l1-1c1 0 1 0 2 1s1 1 1 2c1 1 1 2 2 3 1 0 1 0 2 1l-1 1-1-1h0c0 1-1 1 0 1 0 1 1 2 1 3 1 1 1 2 2 3h-2-1c0-2-1-3-2-4l-1-1 1-2c0-1 0-2-1-4l-2-2z" class="E"></path><path d="M331 529c-1-1-2-2-3-2l-1-1c1-1 2-3 1-5 0-2-2-2-4-3h1 4c2 1 3 1 4 1l1 1-1 1-3-1c0 1 0 3-1 4v2l2 1h0v2z" class="u"></path><path d="M338 435h-8 0l2-1c1-2 0-1 0-3 1-1 1-1 1-2l1-1v-1h-1c0-1 2-1 2-1 1-1 0-4 1-6v6h0c0 1-1 3 0 4 0 1-1 3-1 5h3z" class="F"></path><path d="M310 531l4-12c1 3 0 6-1 8v3c1 1 1 2 1 3l1 1-2 2s-1 0-1 1l-1-1c0-2 1-4 2-7h-1l-2 2z" class="C"></path><path d="M322 524l2 2c1 2 1 3 1 4l-1 2 1 1-1 1v-1c0-1-1-1-1-2-1 1-2 2-2 3h-1v-1c0-1 0-2 1-2v-1c0-1 0-1-1-2l1-1c0-1 0-1 1-1v-2z" class="S"></path><path d="M336 426h1c0-2-1-4 0-6v-1h1v9 6 1h0 0-3c0-2 1-4 1-5-1-1 0-3 0-4z" class="AI"></path><path d="M334 450c0 1 1 1 1 1v1 1s1 1 1 2c0 3 1 6 0 9v4c0 4 0 7-1 11 0 1 0 2-1 3h0v-1-3l1-1h-1c1-5 1-10 1-15 0-1-1-2-1-3v-4-1c0-1-1-1-1-2h1v-2z" class="C"></path><path d="M327 370l3 1c1 0 3 2 5 2 2 1 2 2 3 3h-1-10v1h0c0-2-1-5 0-7z" class="z"></path><path d="M327 370l3 1c1 0 3 2 5 2 2 1 2 2 3 3h-1c-1-1 0-1-1-2-3-1-6-2-9-1v3 1h0c0-2-1-5 0-7z" class="AI"></path><path d="M405 412l2 1c-1 1-3 2-4 3-1 0-2 1-4 1-4 5-9 9-13 15v-1-1l3-3h0c0-1 0-2 1-3l5-7c0 2-4 7-6 9 2-1 4-3 5-4v-1s0-1 1-1c0-1 0-1 1-2h0v1c1 0 3-3 4-4h1l3-3h1z" class="J"></path><path d="M405 412l2 1c-1 1-3 2-4 3-1 0-2 1-4 1l5-5h1z" class="B"></path><path d="M347 389h0 1v2h0l-1-1v1c-1 1-2 1-3 1l-1 1h-3-2c-1 1-1 2-1 3s-1 1-1 2c-1 2 0 6 0 9 1 2 0 4 0 5 0 3 1 6 0 8v-8-5c-1-3 0-6-1-9v-3c2-3 1-4 3-6h3 6z" class="F"></path><path d="M347 389h1v2h0l-1-1v1h-3c1-1 2-1 3-2z" class="AL"></path><path d="M344 391h3c-1 1-2 1-3 1l-1 1h-3-2c0-1 0-1 1-1h1c1-1 2-1 4-1z" class="u"></path><path d="M349 437v1c0 2 1 4 1 6 0 1 0 2-1 3h0-1c-1 0-1 1-1 1h0l-1 1c-2 1-5 1-7 1l-1 1v-1c0-1 1-2 0-3v-2h0l1 1c1-1 1-1 1-2h1l2-2v-1h0 1c1 0 1 1 1 2l-2 2v-2l-3 3h0 1l1 1h2l1-1-1-1v-1h1c1 0 1 0 1-1v-2c1-1 1-2 1-2 1 0 1 0 1-1l1-1z" class="J"></path><path d="M347 444s1 0 2 1l1-1c0 1 0 2-1 3h0-1c-1 0-1 1-1 1h0c-1 0-1-1-1-1v-1c1-1 1-2 1-2z" class="R"></path><path d="M349 437v1c0 2 1 4 1 6l-1 1c-1-1-2-1-2-1l-1-3c1-1 1-2 1-2 1 0 1 0 1-1l1-1z" class="d"></path><path d="M331 529v-2h0l-2-1v-2h4 0l1 1 1 11v2 2c-1-1-2-1-3-2-1 0-1 0-2-1s-1-2-2-3c0-1-1-2-1-3-1 0 0 0 0-1h0l1 1 1-1h2v-1z" class="s"></path><path d="M332 536c0-1 1-1 1-1l2 1v2 2c-1-1-2-1-3-2v-2z" class="I"></path><path d="M331 529v-2h0l-2-1v-2h4 0c0 3 0 4-1 6l-1 1h-3l1-1h2v-1z" class="J"></path><path d="M328 534c0-1-1-2-1-3-1 0 0 0 0-1h0l1 1h3v3 1l1 1v2c-1 0-1 0-2-1s-1-2-2-3z" class="AA"></path><path d="M328 534l1-1 1 1h0c0 1 1 1 1 1l1 1v2c-1 0-1 0-2-1s-1-2-2-3z" class="e"></path><path d="M367 413l1 1h0c2-3 0-9 2-12h1c0-1 1-3 2-3 0 0 0 1 1 1 0-1 1-1 2-1h0c0 2-1 4-1 6s0 4-1 6c-1 1 0 1 0 1h1v1l-7 4c-1 0-1-1-2-2l1-2z" class="M"></path><path d="M356 385v1 2c1 0 2 0 3 1v11 1c-1-1-2-3-3-3h-2c-1 0-2-1-2-1l-1 2-1-1c-1 0-1 1-2 1l-1-2c0-2 0-3 1-4v-2h0v1l1-1 1-2c1-1 2-1 3-2h1c1-1 1-2 2-2z" class="E"></path><path d="M351 397c1-2 2-2 4-2l-1 1v2c-1 0-2-1-2-1h-1z" class="AP"></path><path d="M348 391v1l1-1h2v2c-1 0-2 1-3 1 0 1 0 1 1 2h0c1 0 1 0 2 1h1l-1 2-1-1c-1 0-1 1-2 1l-1-2c0-2 0-3 1-4v-2h0z" class="AM"></path><path d="M354 388l2 2v1l-1 2h-1c-1 0-2 0-3-1h0c0-2 2-3 3-4z" class="Y"></path><path d="M354 388v-1c1 0 1 0 2 1h0c1 0 2 0 3 1v11 1c-1-1-2-3-3-3h-2v-2l1-1h1c0-1 2-1 2-2v-1l-1 1h-1v-2-1l-2-2z" class="AL"></path><path d="M355 395h1l1 1h1c1 1 1 3 1 4v1c-1-1-2-3-3-3h-2v-2l1-1z" class="AO"></path><path d="M347 391v-1l1 1v2c-1 1-1 2-1 4v1h-1-1c-1 1-2 1-3 2-2 1-3 2-4 4v7 8h-1v1c-1 2 0 4 0 6h-1 0v-6c1-2 0-5 0-8 0-1 1-3 0-5 0-3-1-7 0-9 0-1 1-1 1-2s0-2 1-3h2 3l1-1c1 0 2 0 3-1z" class="z"></path><path d="M340 397h-1c0-1-1-1-1-2l1-1h3l1 1v1 1h-3z" class="L"></path><path d="M336 412c1-1 1-3 1-5v-2l1 1c-1 2 0 3 0 5v8h-1v1c-1 2 0 4 0 6h-1 0v-6c1-2 0-5 0-8z" class="AP"></path><path d="M340 397h3c-1 1-1 1-1 3h0c-2 1-3 2-4 4v-2c0-1-1-1-1-2l3-3z" class="AG"></path><path d="M342 394h0v-1c1 1 2 1 3 1 0-1 1-1 1-1 0-1 0-1 1-1l1 1c-1 1-1 2-1 4v1h-1-1c-1 1-2 1-3 2h0c0-2 0-2 1-3v-1-1l-1-1z" class="q"></path><path d="M343 395l1 1h0c0 1 1 2 1 2-1 1-2 1-3 2h0c0-2 0-2 1-3v-1-1z" class="y"></path><defs><linearGradient id="AE" x1="407.688" y1="427.942" x2="411.095" y2="434.257" xlink:href="#B"><stop offset="0" stop-color="#2f2f2e"></stop><stop offset="1" stop-color="#4c4c4b"></stop></linearGradient></defs><path fill="url(#AE)" d="M411 423v-2 1h1v-1h1v3l-1 3h0c1-1 1-2 2-2 0-1 1-1 0-2 1 0 2 0 2 1 1 0 1 0 1 1h0v3l1 1 1 1c0 1 0 1-1 2v-1l-1-1h-1-1c-1 2-3 4-4 4h0l-1 1c-1 0-1 1-1 1-1 1-1 0-2 0h0c-2 1-4 4-5 4h-1c0-1 1-2 2-3l2-3c-1-1-1-2-1-3s0-1-1-2h0 0c1-3 3-4 4-5h1c0 1 0 1 1 2 1-1 1-2 2-3z"></path><path d="M411 423h1c0 2 0 4-1 5l-4 4c-1 0-1 0-1 1h-1v1c-1-1-1-2-1-3s0-1-1-2h0 0c1-3 3-4 4-5h1c0 1 0 1 1 2 1-1 1-2 2-3z" class="E"></path><path d="M407 424c1 1 1 2 0 3v2c0 2-2 3-2 4v1c-1-1-1-2-1-3s0-1-1-2h0 0c1-3 3-4 4-5z" class="W"></path><path d="M338 447c1 1 0 2 0 3 0 2-1 4 0 6v18 28h0l1 1v2l-1 1h-1c0-1 0-1-1-1l-2 1v-1c-2-1-3-1-5-1h0c1-2 4-1 5-2v-1-19c1-1 1-2 1-3 1-4 1-7 1-11v-4c1-3 0-6 0-9v-5c1-2-1-2-1-4l3 1z" class="u"></path><path d="M336 474c1 1 1 3 1 5 1-1 1-4 1-5v28l-1-1v-21c0-2-1-4-1-6z" class="AO"></path><path d="M338 447c1 1 0 2 0 3 0 2-1 4 0 6v18c0 1 0 4-1 5 0-2 0-4-1-5 1-2 1-5 1-6 0-6 0-13-1-18 1-2-1-2-1-4l3 1z" class="q"></path><path d="M357 444c0-1 1-1 1-1l1-1h0l1 1h0c0 1 0 2 1 3l5 8h-1-6v2h-2v-1c-1 0-1 1-2 1h-2l-3 1v-1h-2c0-1 1-2 0-3h-1c0 1-1 2-2 2l1 1h-7-1c-1-2 0-4 0-6v1l1-1c2 0 5 0 7-1l1-1h0s0-1 1-1h1 0 1v-1h2 1 0c1 0 2-1 3 0l1-2z" class="AN"></path><path d="M347 448s0-1 1-1h1v4l-1 1-1-1v-3z" class="e"></path><path d="M350 447v-1h2 1 0c1 0 2-1 3 0l-1 1-1 1v1l1 1c-1 0-1-1-2 0h0c-1 0-2 0-2-1l-1 1h-1l1-3z" class="t"></path><path d="M351 449s0-1 1-2c1 0 1 0 2 1v1l1 1c-1 0-1-1-2 0h0c-1 0-2 0-2-1z" class="e"></path><path d="M338 450v1l1-1c2 0 5 0 7-1 0 1-1 1-1 1l-1 1-1 1-1 1v2h-3v1h-1c-1-2 0-4 0-6z" class="AG"></path><path d="M355 447l1 1 1-1c1 0 1 0 2 1v6 2h-2v-1c-1 0-1 1-2 1h-2 0-2v-1c0-1 0-1 1-2 0-1 0-1 1-1s1-1 2-1h0 1v-1h-1l-1-1v-1l1-1z" class="R"></path><path d="M355 447l1 1c1 1 1 2 1 3v1-1l-1-1h-1l-1-1v-1l1-1z" class="k"></path><path d="M353 456h0-2v-1c0-1 0-1 1-2 0-1 0-1 1-1v2c1 0 1 0 2-1l2 1v1c-1 0-1 1-2 1h-2z" class="I"></path><path d="M357 444c0-1 1-1 1-1l1-1h0l1 1h0c0 1 0 2 1 3l5 8h-1-6v-6c-1-1-1-1-2-1l-1 1-1-1 1-1 1-2z" class="M"></path><path d="M357 444c0-1 1-1 1-1l1-1h0v6c-1-1-1-1-2-1l-1 1-1-1 1-1 1-2z" class="d"></path><path d="M375 413v1 1 1c-1 1-2 2-2 4l-1 2-1-1c-2 2-3 6-4 9 0 1-1 1-1 2 1 1 1 1 1 2h-1 0c1 2 1 4 1 6-1 1 0 3-2 4h-1-1 0c-1 0-2-1-3-1h0l-1-1h0v-13-1h0v-2h0 1l1-2 1-1 2-2 2-2 1-1 1-1 7-4z" class="D"></path><path d="M359 428l1-1h1c0 1 0 2 1 2v1 3l1 1s1 0 1 1c1 2 1 3 1 6 0-1 0-2-1-3l-1-2c-1-1-1-2-2-3 0-1 0-2-1-3 0-1 0 0-1-1v-1h0z" class="B"></path><path d="M375 413v1c-1 1-2 2-2 3l-4 4c-1 0-2 0-3-1 1-1 1-1 1-2l1-1 7-4z" class="G"></path><path d="M366 419l1-1c0 1 0 1-1 2 1 1 2 1 3 1-1 2-3 3-3 5 0 1 0 3-1 5h0c0-1 0-2-1-3 0-1-1-1-1-1h-1l-1-3 1-1 2-2 2-2z" class="AF"></path><path d="M366 419l1-1c0 1 0 1-1 2 1 1 2 1 3 1-1 2-3 3-3 5h-1c0-1-1-2-1-3v-2l2-2z" class="P"></path><path d="M359 429c1 1 1 0 1 1 1 1 1 2 1 3 1 1 1 2 2 3l1 2c1 1 1 2 1 3 1 1 1 2 0 3h-1-1 0c-1 0-2-1-3-1h0l-1-1h0v-13z" class="S"></path><path d="M361 433c1 1 1 2 2 3l1 2c1 1 1 2 1 3 1 1 1 2 0 3h-1-1 0s1 0 1-1c-1-1-1-3-1-5 0-1-1-1-1-2-1-1-1-2-1-3z" class="M"></path><defs><linearGradient id="AF" x1="399.252" y1="433.722" x2="392.146" y2="429.097" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#474846"></stop></linearGradient></defs><path fill="url(#AF)" d="M405 412l2-2 1 1c1 0 2 1 2 2l2 2c-1 3-11 10-13 13-4 4-8 8-11 13h0v1l-3 6-3 6-1-1-3-2h0c0-1 0-1 1-2-1 0-1 0-1-1 0-2 2-4 2-6l1-1 5-9c4-6 9-10 13-15 2 0 3-1 4-1 1-1 3-2 4-3l-2-1z"></path><path d="M405 417c0 1 0 2-1 3 0 2-1 3-2 4l-1-1 2-2c-1 0-1-1-1-1l3-3z" class="X"></path><path d="M388 441v1l-3 6-3 6-1-1-3-2h0c0-1 0-1 1-2h0l1 2h0l8-10z" class="C"></path><path d="M405 412l2-2 1 1h0c0 1 0 2-1 3h1v1h-1c-1 1-2 1-2 2l-3 3c-3 1-4 4-6 6s-10 14-12 15c5-9 12-18 19-25 1-1 3-2 4-3l-2-1z" class="J"></path><defs><linearGradient id="AG" x1="391.184" y1="433.416" x2="387.328" y2="430.922" xlink:href="#B"><stop offset="0" stop-color="#3f403e"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#AG)" d="M399 417c2 0 3-1 4-1-7 7-14 16-19 25-1 1-4 6-5 8h0c-1 0-1 0-1-1 0-2 2-4 2-6l1-1 5-9c4-6 9-10 13-15z"></path><defs><linearGradient id="AH" x1="363.444" y1="392.728" x2="374.865" y2="375.642" xlink:href="#B"><stop offset="0" stop-color="#74664d"></stop><stop offset="1" stop-color="#8d7a59"></stop></linearGradient></defs><path fill="url(#AH)" d="M330 371c8 0 17 1 25 2l10 2c2 1 5 2 8 2 0 0 0-1 1-1 4 2 11 3 15 6l-1 1h0c1 0 1 1 1 2 5 3 10 6 15 10 0 4 1 6 4 9 2 2 3 4 5 6v2c-8-11-18-19-30-25l-18-6-15-4-12-1c-1-1-1-2-3-3-2 0-4-2-5-2z"></path><path d="M374 376c4 2 11 3 15 6l-1 1h0c1 0 1 1 1 2-5-3-11-6-16-8 0 0 0-1 1-1z" class="n"></path><path d="M350 377h6c4 1 9 2 13 4 3 1 7 1 10 3 2 1 3 2 4 3l-18-6-15-4z" class="q"></path><path d="M339 456h7l1 1v2 15 2 4 3 5 3c1 2 0 5 0 7v2h1 2l2 2 1 2v2h1l-1 2-1 1h0c-1 0-2 2-4 2-1 0-3-1-4-2l-1-2-1-1-2 1h-1v-2-2l-1-1h0v-28-18h1z" class="Y"></path><path d="M347 476v4 3h-4v-4h4v-3z" class="D"></path><path d="M347 491c1 2 0 5 0 7v2h1c0 1 0 2 1 3h0c-2 1-2 1-4 3v1h0v1l-1 1-1-2-1-1h0c1-2 2-4 4-5l1-1v-9z" class="AI"></path><path d="M342 506h1c0-1 1-1 1-2 1 1 1 1 1 2v1h0v1l-1 1-1-2-1-1h0z" class="t"></path><path d="M348 500h2l2 2 1 2v2h1l-1 2-1 1h0c-1 0-2 2-4 2-1 0-3-1-4-2l1-1v-1h0v-1c2-2 2-2 4-3h0c-1-1-1-2-1-3z" class="AG"></path><path d="M348 500h2l2 2 1 2v1c-1-1-1-1-2-1h0c0 1 0 1-1 1l-1-2h0c-1-1-1-2-1-3z" class="AJ"></path><path d="M345 506c2-2 2-2 4-3l1 2c0 2 0 3-2 4-1 0-1-1-2-1l-1-1v-1z" class="M"></path><path d="M369 438v5 1c3-10 8-18 12-26 1-3 3-8 4-10l2-2c0 1 0 1 1 1v1h0c0 2 0 3-1 5h1 0c0-1 0-1 1-1h0l-1 2 1 1 3-5h1c0 1 1 1 1 2l-9 15v1c-1 4-4 8-5 13h0v-1h0l2-3h0c0-2 1-2 2-3v-1h1l1-2v1l-5 9-1 1c0 2-2 4-2 6 0 1 0 1 1 1-1 1-1 1-1 2h0l3 2 1 1c-2 5-4 9-6 13l-9 19h-1c-1 2-1 4-2 5 0 2-1 3-1 5v-5l1-1v-2h-1l1-2c0-1 0-2 1-2v-2-1c0-2 1-3 1-5v-2c1-4 3-9 2-13 0-1 0-3-1-5h-1v-1h-1v-1h1l-5-8c-1-1-1-2-1-3 1 0 2 1 3 1h0 1 1c2-1 1-3 2-4 0-2 0-4-1-6h0 1c0-1 0-1-1-2 0-1 1-1 1-2v2l2 2v4z" class="D"></path><path d="M384 422c1-2 3-6 3-8v-5c1 0 0 0 0-1h1 0c0 2 0 3-1 5h1 0c0-1 0-1 1-1h0l-1 2 1 1c-1 2-3 5-4 7h-1z" class="S"></path><path d="M376 442c0 1 0 2-1 2h-1l10-22h0 1c-1 3-3 6-4 9-1 2-2 5-3 8-1 1-1 2-2 3z" class="X"></path><path d="M366 454c3 2 4 5 4 8 0 4-1 9-3 13-1 2-1 5-2 7v-1c0-2 1-3 1-5v-2c1-4 3-9 2-13 0-1 0-3-1-5h-1v-1h-1v-1h1z" class="B"></path><path d="M365 444c2-1 1-3 2-4 0-2 0-4-1-6h0 1c0-1 0-1-1-2 0-1 1-1 1-2v2l2 2v4l-1 1c0 1-1 2-1 2 0 1 1 2 1 4 1 1 2 1 3 2v1h-3c0 1 1 1 0 2h-1c-1-1-2-1-3-2 0-1 0 0-1-1h0c-1-1-1-1-2-1-1-1-1-2-1-3 1 0 2 1 3 1h0 1 1z" class="N"></path><path d="M363 444h1s1 0 2 1v1 1l-1-1h-2l-2-2h2z" class="AF"></path><path d="M367 432l2 2v4l-1 1v1l-1-1v-7z" class="m"></path><path d="M392 410h1c0 1 1 1 1 2l-9 15c-3 6-6 13-9 20v-5c1-1 1-2 2-3 1-3 2-6 3-8 1-3 3-6 4-9 1-2 3-5 4-7l3-5z" class="W"></path><path d="M385 427v1c-1 4-4 8-5 13h0v-1h0l2-3h0c0-2 1-2 2-3v-1h1l1-2v1l-5 9-1 1c0 2-2 4-2 6 0 1 0 1 1 1-1 1-1 1-1 2h0l3 2 1 1c-2 5-4 9-6 13l-9 19h-1c2-6 6-11 7-18 1-2 1-5 1-7 1-2 1-7-1-8 0-1 0-2-1-2 1-2 1-3 2-4h0v2h1c0-1 1-1 1-2h0c3-7 6-14 9-20z" class="C"></path><path d="M376 451v-1c0-2 3-7 5-9l-1 1c0 2-2 4-2 6 0 1 0 1 1 1-1 1-1 1-1 2h-2z" class="W"></path><path d="M378 451h0l3 2 1 1c-2 5-4 9-6 13-1-2 1-5 1-7 1-1 1-5 0-7 0 0 0-1-1-2h0 2z" class="L"></path><path d="M378 451l3 2c-1 1-1 2-3 2v-3-1z" class="Y"></path><path d="M346 456l-1-1c1 0 2-1 2-2h1c1 1 0 2 0 3h2v1l3-1h2c1 0 1-1 2-1v1h2v-2h6v1h1v1h1c1 2 1 4 1 5 1 4-1 9-2 13v2c0 2-1 3-1 5v1 2c-1 0-1 1-1 2l-1 2h1v2l-1 1v5c-1 1-1 3-2 4h0c-1 0-1 1-2 1l-1 1v-3h0c-1 1-1 2-2 2 0 1-1 1-2 1h-1-1l-2-2h-2-1v-2c0-2 1-5 0-7v-3-5-3-4-2-15-2l-1-1z" class="AN"></path><path d="M347 467h1l1 1c0 1 0 2-1 4h-1v-5z" class="R"></path><path d="M347 480h2c-1 1-1 2-1 3l1 4-2 1v-5-3z" class="q"></path><path d="M347 474h0l1 1 2-1h-1v3c1 0 1 0 1 1s-1 1-1 2h-2v-4-2z" class="R"></path><path d="M355 468s-1-1-2-1-1-1-2-1c0-1 0-1 1-2h1 3 1v2 2 1c-1 0-1 0-2-1z" class="V"></path><path d="M346 456l-1-1c1 0 2-1 2-2h1c1 1 0 2 0 3h2v1l1 2v1h-1 0v2h-1c0 1-1 1-2 2 0-2 1-4 0-5v-2l-1-1z" class="AJ"></path><path d="M357 468c0 1 1 1 1 2l1 1c-1 1-1 2-2 3v1 2l-3-2c-1 0-1 0-2 1h-1v-1c0-1 1-2 1-2h1 0v-2c1-1 2-2 2-3 1 1 1 1 2 1v-1z" class="AA"></path><path d="M357 468c0 1 1 1 1 2l1 1c-1 1-1 2-2 3-1-1 0-2-1-4l-1 1v1l-2 1v-2c1-1 2-2 2-3 1 1 1 1 2 1v-1z" class="k"></path><path d="M355 456c1 0 1-1 2-1v1h2v1 3 11h0l-1-1c0-1-1-1-1-2v-2-2h-1-3 2l1-1c-1 0-1 0-2-1v-1h0l-1-1h0v1h-2v-1-1l-1-2 3-1h2z" class="q"></path><path d="M351 459l2-1h2v1c-1 0-1 0-2 1h0v1h-2v-1-1z" class="d"></path><path d="M356 463v-3c1 0 1 0 2 1 0 2-1 3-1 5h0v-2h-1-3 2l1-1z" class="e"></path><path d="M355 456c1 0 1-1 2-1v1h2v1 3c-2 0-2-3-4-4z" class="d"></path><path d="M354 481l2 1h1c0 1 0 1-1 2l1 1v5c0 2 0 3-2 3v1c-1 0-2 1-2 2l-1 1v1h0v1c-1 0-2 1-3 1 0-1 1-3 1-4l-1-2c0-1 1-2 1-2 0-2-1-3 0-4 0-1 1-3 0-4v-1h1v-2h2 1z" class="E"></path><path d="M351 493l2-1h2v1c-1 0-1 1-2 1h-2v-1z" class="d"></path><path d="M351 493v-1h0v-3l2 2h1 2v1h-1-2l-2 1z" class="AJ"></path><path d="M355 489l2 1c0 2 0 3-2 3v1-1-1h1v-1h-2-1l2-2z" class="B"></path><path d="M354 481l2 1c-1 1-2 2-3 2h-2v-1-2h2 1z" class="AE"></path><path d="M356 484l1 1v5l-2-1h-1c-1-1-2-1-3-2l2-2c1 1 1 1 2 1l1-2z" class="I"></path><path d="M359 471v3c0 3 0 6 1 8v-1-1c1 0 1-1 1-2 0 3-1 6-1 9l1 2 3-3-1 2h1v2l-1 1v5c-1 1-1 3-2 4h0c-1 0-1 1-2 1l-1 1v-3h0c-1 1-1 2-2 2 0 1-1 1-2 1h-1l1-1v-2c0-1 1-1 1-2-1-1-2-1-2-1 0-1 1-2 2-2v-1c2 0 2-1 2-3v-5l-1-1c1-1 1-1 1-2h-1l-2-1c1-1 1-2 2-2l1-2v-2-1c1-1 1-2 2-3h0z" class="F"></path><path d="M359 501c0-2 0-3 1-4 0 1 1 2 1 3h0c-1 0-1 1-2 1z" class="E"></path><path d="M359 471v3 3c-1 0-1-1-2-2v-1c1-1 1-2 2-3h0z" class="AJ"></path><path d="M364 486l-1 2h1v2l-1 1v5c-1 1-1 3-2 4 0-1-1-2-1-3 0-3 1-5 1-8l3-3z" class="N"></path><path d="M357 475c1 1 1 2 2 2v2 7c-1-1-1-1-2-1l-1-1c1-1 1-1 1-2h-1l-2-1c1-1 1-2 2-2l1-2v-2z" class="q"></path><path d="M356 479h1c0 2 0 2-1 3l-2-1c1-1 1-2 2-2z" class="e"></path><path d="M357 475c1 1 1 2 2 2v2h-1-1-1l1-2v-2z" class="d"></path><path d="M357 485c1 0 1 0 2 1 0 4 0 7-1 11v2c-1 1-1 2-2 2 0 1-1 1-2 1h-1l1-1v-2c0-1 1-1 1-2-1-1-2-1-2-1 0-1 1-2 2-2v-1c2 0 2-1 2-3v-5z" class="AG"></path><path d="M356 501h0c0-2 0-2 1-4h1v2c-1 1-1 2-2 2z" class="AI"></path><path d="M359 454h6v1h1v1h1c1 2 1 4 1 5 1 4-1 9-2 13v2c0 2-1 3-1 5v1 2c-1 0-1 1-1 2l-3 3-1-2c0-3 1-6 1-9 0 1 0 2-1 2v1 1c-1-2-1-5-1-8v-3-11-3-1-2z" class="W"></path><path d="M365 481v1 2c-1 0-1 1-1 2l-3 3-1-2c1-1 2-1 2-2 1 0 2-3 3-4z" class="E"></path><path d="M359 454h6v1h1v1h1l-1 1v4c-1 2-5 11-5 12l1 1h0c0 1-1 3-1 4s0 2-1 2v1 1c-1-2-1-5-1-8v-3-11-3-1-2z" class="Y"></path><path d="M359 454h6v1h1v1h1l-1 1c-1-1-5 0-7 0h0v-1-2z" class="E"></path><path d="M359 457l1-1c1 0 2 0 3-1 1 0 2 1 3 1h1l-1 1c-1-1-5 0-7 0z" class="z"></path><path d="M347 397l1 2c1 0 1-1 2-1l1 1 1-2s1 1 2 1h2c1 0 2 2 3 3v18 7h0v2h0v1 13l-1 1s-1 0-1 1l-1 2c-1-1-2 0-3 0h0-1-2v1h-1c1-1 1-2 1-3 0-2-1-4-1-6v-1l-1 1c0 1 0 1-1 1h-1-2l-1-1-1-1c0-1 1-2 1-3-1 0-3 0-5 1v-1-6-9-8-7c1-2 2-3 4-4 1-1 2-1 3-2h1 1v-1z" class="Y"></path><path d="M347 424h1l1 2c0 1 0 3-1 4h-1v-6z" class="R"></path><path d="M347 397l1 2 1 3s-1 0 0 0v3c-1-1-1-2-1-2v-1c0 2 0 4-1 6l-1-10h1v-1z" class="AI"></path><path d="M347 408c1-2 1-4 1-6v1s0 1 1 2v2 4l-2 2h0v-5z" class="q"></path><path d="M348 399c1 0 1-1 2-1l1 1-1 1c0 1 0 1 1 2 0 0 0 1 1 1l-1 1h0v2l-2 1v-2-3c-1 0 0 0 0 0l-1-3z" class="t"></path><path d="M349 411h2l1 1h-1v2c1 1 1 1 2 1h0-2-2v1c0 2 0 3-1 4 0 0-1 1-1 2v1c-1-3 0-7 0-10h0l2-2z" class="AI"></path><path d="M347 422c0-2 0-7 1-8l1 1v1c0 2 0 3-1 4 0 0-1 1-1 2z" class="AJ"></path><path d="M349 416c1 1 1 2 1 3h0v2 2l1 1v-2s0 1 1 2h-1v4h1 2 0l-1 2h0c-1 0-2 0-2 1h-1v-2c-1-1-1-2-1-3l-1-2h-1v-1-1c0-1 1-2 1-2 1-1 1-2 1-4z" class="AN"></path><path d="M343 429h1v-2c1 1 0 1 0 2 1 1 1 1 1 2h0l1 1h-1l-1 2h-1c-1 0-3 0-5 1v-1-6h1l4 1z" class="l"></path><path d="M338 434c1 0 1-1 1-1 2-1 5-1 6-1l-1 2h-1c-1 0-3 0-5 1v-1z" class="AO"></path><path d="M339 428l4 1c1 0 1 1 1 1v1h-3-1-1c-1-1-1-1 0-3z" class="AN"></path><path d="M353 415l1-1 3 2c-1 1-1 2-1 3s-1 1-2 1c-1 1-2 0-3 2v2l-1-1v-2-2h0c0-1 0-2-1-3v-1h2 2 0z" class="x"></path><path d="M353 415l1 1c-1 1-2 2-3 2h0v-3h2z" class="F"></path><path d="M353 415l1-1 3 2c-1 1-1 2-1 3s-1 1-2 1h-3 0c0-1 3-2 4-3v-1h-1l-1-1h0z" class="z"></path><path d="M349 426c0 1 0 2 1 3v2h1c0-1 1-1 2-1h0l1-2h0l1-1c1 1 1 1 2 1h2v1 13l-1 1s-1 0-1 1l-1 2c-1-1-2 0-3 0h0-1-2v1h-1c1-1 1-2 1-3 0-2-1-4-1-6v-1l-1 1c0 1 0 1-1 1h-1-2l-1-1-1-1c0-1 1-2 1-3h1l1-2h1c0-1 0-1 1-2h1c1-1 1-3 1-4z" class="e"></path><path d="M356 434c1 0 1 1 1 2s-2 3-3 3h-1v-1l-2 1 1-1c0-1 1-1 2-1v-1h0l1-1c1 0 1-1 1-1z" class="R"></path><path d="M354 428l1 1h0c1 0 1 1 1 1v1h0c-1 0 0 0-1 1h1-1c-1 0-2 0-3 1h0l-1-1c0-1 0-1 2-2h0l1-2z" class="k"></path><path d="M349 438h1c0-1 1-1 2-1v1l-1 1 2-1v1c0 1 0 1 1 2h0c1 0 2 1 3 2v1l-1 2c-1-1-2 0-3 0h0-1-2v1h-1c1-1 1-2 1-3 0-2-1-4-1-6z" class="q"></path><path d="M349 426c0 1 0 2 1 3v2h1c0-1 1-1 2-1-2 1-2 1-2 2l1 1h0c1-1 2-1 3-1l1 2s0 1-1 1l-1 1h0v1c-1 0-2 0-2 1v-1c-1 0-2 0-2 1h-1v-1l-1 1c0 1 0 1-1 1h-1-2l-1-1-1-1c0-1 1-2 1-3h1l1-2h1c0-1 0-1 1-2h1c1-1 1-3 1-4z" class="AJ"></path><path d="M345 436h3l1 1-1 1c0 1 0 1-1 1h-1c0-1 0-1-1-2h0v-1z" class="AA"></path><path d="M344 434c1 0 2 0 3-1h1 1 0v1h-1c-1 1-1 1-2 1v1h-1v1h0c1 1 1 1 1 2h-2l-1-1-1-1c0-1 1-2 1-3h1z" class="R"></path><path d="M352 397s1 1 2 1h2c1 0 2 2 3 3v18 7h0v2h0-2c-1 0-1 0-2-1l-1 1h-2-1v-4h1c-1-1-1-2-1-2 1-2 2-1 3-2 1 0 2 0 2-1s0-2 1-3l-3-2-1 1c-1 0-1 0-2-1v-2h1l-1-1h-2v-4l2-1v-2h0l1-1c-1 0-1-1-1-1-1-1-1-1-1-2l1-1 1-2z" class="e"></path><path d="M356 422l1 1 1 1 1 2v2h0-2c-1-1-1-1-1-3h0l-1-1h1v-2z" class="V"></path><path d="M353 406h2c1 1 2 2 2 3v1l-2 2-1-1c0-1 0-1 1-1v-1c-1-1-2-2-2-3z" class="Q"></path><path d="M351 412c2 1 4-1 5 1 1 1 1 1 1 2v1l-3-2-1 1c-1 0-1 0-2-1v-2z" class="V"></path><path d="M354 414c1-1 1-1 2-1 1 1 1 1 1 2v1l-3-2z" class="P"></path><path d="M356 402c1 1 1 2 1 3h0l-1-1h0l-1 1v1h-2-1 0c-1-1-1-1-1-2l1-1h2c1 0 2-1 2-1z" class="d"></path><path d="M356 398c1 0 2 2 3 3v18c-1-2 0-5 0-8 0-2 0-4-1-7l-1 1c0-1 0-2-1-3h0c1-2 0-3 0-4z" class="q"></path><path d="M356 419l1-1h1v1 1c-1 0-1 1-2 2v2h-1-1-2c-1-1-1-2-1-2 1-2 2-1 3-2 1 0 2 0 2-1z" class="P"></path><path d="M354 424h1l1 1h0c0 2 0 2 1 3-1 0-1 0-2-1l-1 1h-2-1v-4h1 2z" class="z"></path><path d="M352 424h2v1c-1 1-1 1-2 1-1-1-1-1-1-2h1z" class="C"></path><path d="M349 407l2-1v-2h0c0 1 0 1 1 2h0 1c0 1 1 2 2 3v1c-1 0-1 0-1 1l-2 1-1-1h-2v-4z" class="y"></path><path d="M349 407l2-1v-2h0c0 1 0 1 1 2h0c-1 0-1 1-2 2 0 1 1 2 1 3h-2v-4z" class="x"></path><path d="M352 397s1 1 2 1h2c0 1 1 2 0 4h0s-1 1-2 1h-2c-1 0-1-1-1-1-1-1-1-1-1-2l1-1 1-2z" class="C"></path><path d="M350 400h1l1-1 1 1 2-1v1s0 1-1 2v1h-2c-1 0-1-1-1-1-1-1-1-1-1-2z" class="AM"></path><path d="M366 486h1c-2 7-4 12-6 18l-2 6v2l1 2c-1 2-2 4-1 6l1 1h-1v1 2c-1 1-1 3-1 4l1 2-1 4v1c0 1-1 3-1 5 0 1 0 1 1 1h0v1h0c0 1 0 3-1 4-1 0-1 0-1 1-1 0-2 1-2 2v3l-2 2h0-1v1l-1 1c2 0 4 0 5 1h0l-1 1h-7c2 1 6 4 9 4l1 1h-2 0 0c-2-1-3-1-4-1s-3-1-5-2v1 1c-2-1-3-2-5-3 0 0-1-1-1-2h-2c-1-1-2-1-3-2 0-2-1-3-2-4-2-3-5-8-8-11 0-1-1-3-2-4l-1-1c1 0 1-1 1-1v-1h1v1l1-1c1 1 2 2 2 4h1 2c1 1 1 1 2 1 1 1 2 1 3 2v-2-2l-1-11-1-1h0-4c1-1 1-3 1-4l3 1 1-1-1-1h1v-2l1-1-2-1v-1h2v-1c-1-1-1-1-1-2l-2-2h1 0v-1c0-1 0-1 1-2l2-1c1 0 1 0 1 1h1l1-1v2h1l2-1 1 1 1 2c1 1 3 2 4 2 2 0 3-2 4-2h0l1-1 1-2h-1v-2l-1-2h1 1c1 0 2 0 2-1 1 0 1-1 2-2h0v3l1-1c1 0 1-1 2-1h0c1-1 1-3 2-4 0-2 1-3 1-5 1-1 1-3 2-5z" class="D"></path><path d="M340 522l1-1 1 1v3l-1-1c0-1-1-1-1-2z" class="E"></path><path d="M341 510v3c-1 0-1-1-1-1-1-2 0-2 0-3h1v1z" class="W"></path><path d="M351 552c1-2 1-2 3-3v3l-2 2c0-1 0-2-1-2z" class="C"></path><path d="M335 538l1 2h0c1 0 2 0 2-1l1 1 1-1c0 2 1 3 0 4v1c-1-1-3-3-4-3h-1 0v-1-2z" class="L"></path><path d="M340 539c0-1-1-3 0-4h0c2 4 5 6 6 11l-1 1c-1 0-2-1-3-1l-2-2v-1c1-1 0-2 0-4z" class="Y"></path><path d="M334 509l1-1 1-1v1c0 3 2 4 2 7 0 0 0 1 1 1h0v4h0l-2-1v1h0c0 1 1 1 1 2 0 3 1 5 1 8-1 0-1-1-2-1-1-3-1-6-4-8l1-1-1-1h1v-2l1-1-2-1v-1h2v-1c-1-1-1-1-1-2l-2-2h1 1z" class="B"></path><path d="M334 509h1v4h0c-1-1-1-1-1-2l-2-2h1 1z" class="F"></path><path d="M335 513c2 1 2 2 2 4 0 0 1 1 1 2-1 0-1-1-1-1-1-1-2-1-3-1l1-1-2-1v-1h2v-1h0z" class="C"></path><path d="M329 524c1-1 1-3 1-4l3 1c3 2 3 5 4 8 1 0 1 1 2 1 0 2 1 3 1 5-1 1 0 3 0 4l-1 1-1-1c0 1-1 1-2 1h0l-1-2v-2l-1-11-1-1h0-4z" class="S"></path><path d="M337 529c1 0 1 1 2 1 0 2 1 3 1 5-1 1 0 3 0 4l-1 1-1-1c0-4-1-7-1-10z" class="C"></path><path d="M325 533c1 1 2 2 2 4h1c3 3 7 7 11 9l4 2h-1-1c0 1 1 1 1 1 0 1 0 1 1 2 2 0 5 0 6 2v1h2v-1-1c1 0 1 1 1 2h0-1v1l-1 1c2 0 4 0 5 1h0l-1 1h-7c2 1 6 4 9 4l1 1h-2 0 0c-2-1-3-1-4-1s-3-1-5-2v1 1c-2-1-3-2-5-3 0 0-1-1-1-2h-2c-1-1-2-1-3-2 0-2-1-3-2-4-2-3-5-8-8-11 0-1-1-3-2-4l-1-1c1 0 1-1 1-1v-1h1v1l1-1z" class="M"></path><path d="M338 549c-2-1-4-4-6-6h0c1 0 2 2 3 2 2 1 3 2 5 3l1 1c-1 0-1 1-2 2 0-1-1-1-1-2h0z" class="D"></path><path d="M340 548l1 1c-1 0-1 1-2 2 0-1-1-1-1-2h0s1 0 2-1z" class="N"></path><path d="M334 550c2 2 4 5 6 7h-2c-1-1-2-1-3-2 0-2-1-3-2-4l1-1z" class="q"></path><path d="M340 553l1-1 2 2h0c2 0 4 1 6 1 0 0 1 0 1 1 2 0 4 0 5 1h0l-1 1c-1-1-2-1-3-1-2 1-4 0-5 0l-1-1c-2 0-2 0-3-1l-2-2z" class="E"></path><path d="M325 540c0-1-1-3-2-4l-1-1c1 0 1-1 1-1v-1h1v1c3 5 7 11 10 16l-1 1c-2-3-5-8-8-11z" class="R"></path><path d="M341 549h1c0 1 0 1 1 2 2 0 5 0 6 2v1h2v-1-1c1 0 1 1 1 2h0-1v1l-1 1c0-1-1-1-1-1-2 0-4-1-6-1h0l-2-2-1 1c-1-1-2-1-2-4 0 1 1 1 1 2 1-1 1-2 2-2z" class="X"></path><path d="M341 549h1c0 1 0 1 1 2 2 0 5 0 6 2v1c-4 0-6-2-10-3 1-1 1-2 2-2z" class="Q"></path><path d="M366 486h1c-2 7-4 12-6 18l-2 6v2l1 2c-1 2-2 4-1 6l1 1h-1v1 2c-1 1-1 3-1 4l1 2-1 4v1c0 1-1 3-1 5 0 1 0 1 1 1h0v1h-1c-3 2-6 4-7 6h0 3 0c-1 1-1 2-3 3h-2c-1-1-1-1-1-2v-1h0v-1c0-1-1-2 0-2 1-3 0-7 0-10v-1c-1-1-1-2-1-2v-5c-1-2 0-5 0-7s-1-2-1-3l-3-3 1-2c0-1-1-1-2-2h0v-1-1l-1-1 2-1 1 1 1 2c1 1 3 2 4 2 2 0 3-2 4-2h0l1-1 1-2h-1v-2l-1-2h1 1c1 0 2 0 2-1 1 0 1-1 2-2h0v3l1-1c1 0 1-1 2-1h0c1-1 1-3 2-4 0-2 1-3 1-5 1-1 1-3 2-5z" class="l"></path><path d="M350 529h0c0 1 0 2-1 3h0c-1-1-1-2 0-3h1z" class="M"></path><path d="M342 506l1 1-1 1c1 2 1 2 3 2h0c-2 1-2 1-4 0h0v-1-1l-1-1 2-1zm3 7h1l1-1h0c1 0 1 1 1 1v1l-1 4-1 1c1-2 0-4-1-6z" class="F"></path><path d="M345 510v3c1 2 2 4 1 6v1c0-2-1-2-1-3l-3-3 1-2c0-1-1-1-2-2 2 1 2 1 4 0z" class="E"></path><path d="M354 528l1-4h1c-1 4-1 7-1 11v5c0 1 2 2 2 2-3 2-6 4-7 6h0 3 0c-1 1-1 2-3 3h-2c-1-1-1-1-1-2v-1c2-2 4-3 6-5 1-1 1-3 1-4-1-3 0-7 0-11z" class="S"></path><defs><linearGradient id="AI" x1="351.711" y1="534.303" x2="362.023" y2="527.89" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#484947"></stop></linearGradient></defs><path fill="url(#AI)" d="M359 512l1 2c-1 2-2 4-1 6l1 1h-1v1 2c-1 1-1 3-1 4l1 2-1 4v1c0 1-1 3-1 5 0 1 0 1 1 1h0v1h-1s-2-1-2-2v-5c0-4 0-7 1-11h0c1-1 1-2 1-3l2-9z"></path><path d="M358 528l1 2-1 4c-1-1-1-2-1-4l1-2zm8-42h1c-2 7-4 12-6 18l-2 6v2l-2 9c0 1 0 2-1 3h0-1l-1 4c0 1 0 1-1 2-1-1-1-1-1-2l-1-6v-2c1-1 2-3 3-4h-2c0-1-1-2-1-3 1-1 2-1 3-2h0l-1-1c1 0 1 0 2-1l-1-1-1 1h-1l1-1 1-2h-1v-2l-1-2h1 1c1 0 2 0 2-1 1 0 1-1 2-2h0v3l1-1c1 0 1-1 2-1h0c1-1 1-3 2-4 0-2 1-3 1-5 1-1 1-3 2-5z" class="X"></path><path d="M359 501c1 0 1-1 2-1 0 1-1 3-2 4 0 0 0-1-1-1v-1l1-1z" class="m"></path><path d="M355 509h0c1-1 2-2 2-3h1c0 2-1 4-1 6-1 0-2-1-3-1h0l-1-1c1 0 1 0 2-1z" class="L"></path><path d="M358 499h0v3 1c-1 0-1 1-1 2v1h-1l-2 2-1 1h-1l1-1 1-2h-1v-2l-1-2h1 1c1 0 2 0 2-1 1 0 1-1 2-2z" class="J"></path><path d="M354 511c1 0 2 1 3 1h1l-2 5-2-1h-2c0-1-1-2-1-3 1-1 2-1 3-2z" class="AK"></path><path d="M419 424l1 3 2 10 1 4v1l1 3c-1 2-5 3-7 5-4 3-9 6-13 9-2 2-4 4-6 5h-1c-5 6-10 12-14 18-6 8-12 16-16 25-1 3-2 5-3 7-1 4-3 7-4 11l-1 5-1-2c0-1 0-3 1-4v-2-1h1l-1-1c-1-2 0-4 1-6l-1-2v-2l2-6c2-6 4-11 6-18l9-19c2-4 4-8 6-13l3-6 3-6 1 1v2l1 1c1-3 3-5 4-8 2-3 3-5 6-8h1 0c0 1 1 1 1 1v1l1-3c1 1 1 1 1 2s0 2 1 3l-2 3c-1 1-2 2-2 3h1c1 0 3-3 5-4h0c1 0 1 1 2 0 0 0 0-1 1-1l1-1h0c1 0 3-2 4-4h1 1l1 1v1c1-1 1-1 1-2v-1-1l-1-3 1-1z" class="N"></path><path d="M400 430h1 0c0 1 1 1 1 1v1 3l-1-1v-2-1l-1-1z" class="D"></path><path d="M403 429c1 1 1 1 1 2s0 2 1 3l-2 3c-1-1-1-1-1-2v-3l1-3z" class="Q"></path><path d="M394 438l1-1c1 1 0 2 0 2v2c-1 2-3 4-4 7h0c0 1-1 1-1 2l-1 1h-1v-1l2-4c1-3 3-5 4-8z" class="m"></path><path d="M366 495c2-3 3-6 4-9 2-4 4-7 7-11 1-2 2-5 3-7h1c-2 5-5 10-8 15l-2 5c-1 2-2 7-4 8l-1-1z" class="D"></path><defs><linearGradient id="AJ" x1="378.731" y1="482.833" x2="368.141" y2="479.877" xlink:href="#B"><stop offset="0" stop-color="#474746"></stop><stop offset="1" stop-color="#5e5e5d"></stop></linearGradient></defs><path fill="url(#AJ)" d="M388 442l1 1v2l1 1-2 4v1h1l1-1c0 1-1 1-2 2h0c-1 1-2 1-2 2s-2 4-1 4v1l-2 5c-1 1-1 3-2 4h-1c-1 2-2 5-3 7-3 4-5 7-7 11-1 3-2 6-4 9v2c-2 3-2 5-3 8l-3 9-1-2v-2l2-6c2-6 4-11 6-18l9-19c2-4 4-8 6-13l3-6 3-6z"></path><path d="M388 442l1 1v2c0 1-1 3-2 5-1 0-1-1-2-2l3-6z" class="Q"></path><path d="M419 424l1 3 2 10-1 1c-5 3-9 7-14 10-7 7-13 14-20 21-2 4-4 7-7 11-4 5-7 10-10 15v-2c3-6 7-13 11-18 1-2 2-4 4-6 5-8 12-16 19-24 4-4 9-9 14-13 1-1 1-1 1-2v-1-1l-1-3 1-1z" class="L"></path><path d="M407 448c1-1 1-2 2-2l2-2h-1-1c4-2 6-5 10-7 1 0 1 1 2 1-5 3-9 7-14 10z" class="B"></path><defs><linearGradient id="AK" x1="392.044" y1="485.476" x2="381.726" y2="477.486" xlink:href="#B"><stop offset="0" stop-color="#464645"></stop><stop offset="1" stop-color="#6b6b6a"></stop></linearGradient></defs><path fill="url(#AK)" d="M422 437l1 4v1l1 3c-1 2-5 3-7 5-4 3-9 6-13 9-2 2-4 4-6 5h-1c-5 6-10 12-14 18-6 8-12 16-16 25-1 3-2 5-3 7-1 4-3 7-4 11l-1 5-1-2c0-1 0-3 1-4v-2-1h1l-1-1c2-4 3-8 4-11l4-8 3-6c3-5 6-10 10-15 3-4 5-7 7-11 7-7 13-14 20-21 5-3 9-7 14-10l1-1z"></path><path d="M397 464c3-6 10-12 16-16l5-3c1-1 2-1 3-2 1 0 1-1 2-1l1 3c-1 2-5 3-7 5-4 3-9 6-13 9-2 2-4 4-6 5h-1z" class="W"></path><path d="M153 97c1 0 2-2 3-2h10 18 72 165 54 34 1v11 21l1 38v35 9c-1-2-1-4-1-6h0c0 4 0 9-1 13v22c-2-1-4-3-6-5-1-3-2-5-3-8-4-12-10-25-17-36-1-2-3-5-5-8h0c-5-7-11-14-17-19h-1c-1 0-2-2-3-2l-6-6c-6-5-13-9-21-12h0-1 0c-2 0-1-1-3 0-1 0-2-1-3-1l-5-2c-5-2-9-4-14-5-21-6-41-5-63-5h-9-5v2c0 2-1 3 0 5v39 10 4c1 3 0 6 0 9v15 5c0 1 1 1 1 1l1 1c-1 0-1 1-1 1 2 2 2 4 2 7 0 1-1 2-1 4 0 1-1 3 0 4h1s1 0 1 1c-1 2-1 4-3 5l-1 1v26 1 5 12h-1c0-1 0-1-1-2h0v3c-1 1-1 4-1 6h0l-1 8c0 1 1 3 0 4 0 0-1 2 0 3v1c-1 1 0 2-1 3h-2c0 1-1 1-1 1h-2c-1 2-1 21-1 25v3c-1 1-2 0-3 0l-1 1v2 1l2 1v1c-1 0-1 1-1 1l1 1h-2 0v1c-3 2-7 3-11 3h0c-1 2-2 3-3 5-1 3-2 9-2 12 0 2 1 3 1 5h0l-1 1v3l-1-2-1 1v2c2 0 3 0 6 1v1h-2l-1 1h-4 0c-1 0-3 0-4 1-1 0-1 1-2 1 0 1-1 2-1 3-1 0-1 1-3 1 1 1 1 3 1 4v1 1 1h-1v1l1 2v2h-1-1c-1-1-4-1-6-1-1 1-3 1-4 1-4 0-7 0-11 1l-1 1v-1h1c-1-2-1-3-1-5 0-1 0-3-1-4-1-3-1-6-3-8-1-2-3-4-5-4-2-1-3 0-5 1-1 1-2 3-2 5l-1-1c0-1 0-2-1-4l1 68v18 21c-1 3-1 7-1 11v3l1 1h1c1 2 3 0 4 2h-1l1 2v1h-3l2 2c-2 0-2 1-4 2h1c0 1 1 2 1 2 1 1 1 1 2 0v1c0 1 1 2 3 3v2c-1 1-1 1-1 2-1 3-2 8-4 10-1 3-2 5-3 8-2 0-3 3-3 4l-7 9c-1 3-3 5-5 7-1 2-2 3-3 5l-1-1-2 1-1 2c-1 2-3 3-4 5-2 3-6 6-8 8 1 1 2 1 2 1 0 1-1 2-1 3h0l1 1h0l-1 1c1 1 2 1 3 3v1h-2l-2 1h-3s0 1-1 1h-1c-2 2-3 2-3 4l-1-1c0-1 1-1 1-2h-1v-1c0-1-1-1-1-2v-1-1h-1-1 0-1c-7 4-14 9-21 13-1 1-3 1-4 2-4 2-8 3-12 5l-7 2v1c-2 1-4 1-6 2v1h-2c0 1 2 1 3 2-6 0-12 1-18 2h-6-4-4-1c-2-1-4-1-5-2h0c1-1 0-1 1-1s1-1 2-1c0-1 2-1 3-1 4-1 8-1 13-2 11-2 23-5 33-11l1-1c1 0 1 0 1-1-1 1-3 1-5 2h0c-2 0-3 1-5 2h-4-4l20-8h0c9-4 18-9 25-16l1-1c3-2 5-4 7-6l2-2 3-3 1-1c1-2 3-4 4-5l1-1-3-2c-5-2-9-3-13-5v-1c0-2-2-4-3-5-1 0-3-2-3-3-1-1 0-2 0-3v-2c-1-1-1-1 0-2l1-1s1-1 2-1l1 1h0c1-1 1-2 2-2 2-1 3-1 5-3h0 0-1v-1-1h0 1l1-1v-2l2-2c1 1 0 1 1 2v-1h2c2 0 3 1 4 2l1-1c3-1 4-4 6-7 1 0 1 1 2 2l1 1c1-2 2-3 4-4 2 0 3 0 5 1v2c1-3 2-7 2-9l1-3c1-4 1-9 1-14v-6c1-6 1-12 1-17v-4-15-4c-2-1-3-3-4-4 1-1 1-1 1-2v-2h0c1-1 2-2 2-3h0l1-10-1-21v-14-4c1-3 0-6 0-9v-8-8-5-3h-1c-2-4-4-7-5-11 0-2 0-3-1-5-1-3-5-8-4-11l-1-3v-2s0-1-1-1v-1c0-1-1-2-1-3s0-2-1-3c0-3-1-6-1-9 0-2-1-4-1-6v-11-19c0-3 1-7 0-9h-1-1l-1 2c0-3 1-5 0-8h-1c1-1 1-3 2-4v1h1v1h0c2-2 2-2 2-4l1-1v-5h-1v-4h1l3-6h0c0-3 3-7 6-8 0-1 1-1 2-1h1c2-1 3-2 4-4l-1-19v-12-2-2l-1-2v-2l-1-6-2-6-4-8-3-5c-1-2-2-6-3-8l-3-6c-1 0-1-2-1-2-2-3-6-4-7-6h-1v-1c-14-7-30-8-46-6l-12 3v-1c-4 0-8 2-11 4-2 1-4 2-7 2l-9 6c-5 4-9 8-14 12 1-2 2-4 3-5l1-1c2-6 8-14 12-19v-1c2-2 3-4 5-5 1-2 2-4 4-5l9-10 11-9 3-3z" class="AQ"></path><path d="M316 167c0-1 0-1 1-1 0 3 0 6-1 9-1-2 0-6 0-8zm0-37c1-1 0-2 1-4v10l-1 1v-7z" class="H"></path><path d="M317 296l1-1h1c0 1 0 2 1 3h-5l2-2z" class="c"></path><path d="M313 300c0-1 1-2 1-2h1 5l-1 2h-1c0-1 0-1-1-1l-1 1h-3z" class="j"></path><path d="M504 176h1l-1 12v-5c-1 0-1-1-1-1 1-2 1-5 1-6z" class="O"></path><path d="M306 300c1 0 2-1 3-1 3-2 5-3 8-3l-2 2h-1s-1 1-1 2c-1-1-2 0-2 0h-5 0z" class="o"></path><path d="M497 100c2 0 4 0 6 1v1 3h-1 0c0-1-1-1-1-2-2-2-2-2-4-2v-1z" class="c"></path><path d="M486 183c1 2 2 3 4 4h0l-1 1 3 4-1 2c-2-4-4-7-6-10l1-1z" class="P"></path><path d="M316 137l1-1v30c-1 0-1 0-1 1v-30z" class="AC"></path><path d="M323 127l2 1v9l-1 9v-2c-1 2 0 7-1 10v-17-10z" class="G"></path><path d="M233 532c1 1 1 2 1 3l-6 15h-2l7-18z" class="P"></path><path d="M130 125h0c2-1 4-3 5-3l2-1c-1 2-2 3-3 5l-2 1-9 6c2-3 4-6 6-8h1z" class="Z"></path><path d="M314 195h3c0-1 1-2 2-2l1 1c0 1-1 2-1 2h0v-1l-1-1h0c0 1-1 2 0 3v1l-1 1h0v1h0l1 1c0 1 0 2 1 3h0v2c1 1 1 1 1 2l1 1c0 1 0 4 1 5v2l-3-8c0-1-1-3-1-4s-1-2-1-3c-2-2-3-3-5-4v-1l1-1h1z" class="r"></path><path d="M319 208l3 8v7c-1 0-2-1-2-1 0-1 0-1-1-1 0-1 0-2-1-3h0c0 1-1 1 0 2 0 1-1 1-1 2h-1v-4l1-2v-1l1-2c1-2 1-3 1-5z" class="O"></path><path d="M324 204v9 3l1-1v-14 28 11 18h-1v-54z" class="I"></path><path d="M503 102l1 6c1 4 1 9 1 14v21c-1-1 0-3-1-5v-8-3c0 3 0 8-1 11h0v-18-6h-1v-3c1-2 1-4 1-6v-3z" class="p"></path><path d="M492 192l6 14 2 3 1 4 3 9c0 1 0 2 1 3v1h-1c-3-5-4-11-6-16-2-6-5-11-7-16l1-2z" class="T"></path><path d="M490 187h1c1 0 3 1 4 1l-1 1s0 1 1 1h-2c2 2 3 3 4 6 1 2 2 3 2 6 0 1-1 2-1 3v1l-6-14-3-4 1-1h0z" class="n"></path><path d="M490 187h1c1 0 3 1 4 1l-1 1s0 1 1 1h-2l-3-3h0z" class="e"></path><path d="M503 138h0c1-3 1-8 1-11v3 8c1 2 0 4 1 5v33h-1c0-5 1-11 0-15h-1v-1h0s1-1 0-1v-7-1h-1v-3-2-2l1-1v-2-3z" class="b"></path><path d="M502 144l1-1v-2c0 2 1 5 0 7 0 1 1 2 1 3l-1 1v-1h-1v-3-2-2z" class="K"></path><path d="M325 258v10c1 2 1 2 1 4h0v3h1v12h-1c0-1 0-1-1-2h0v3c-1 1-1 4-1 6h0c-1-3 0-8 0-11v-25h1z" class="P"></path><path d="M325 268c1 2 1 2 1 4h0c0 1 0 1-1 1v3 9h0v-6-11z" class="o"></path><path d="M325 285v-9-3c1 0 1 0 1-1v3h1v12h-1c0-1 0-1-1-2z" class="O"></path><path d="M325 240l1-1v1c1-1 0-5 1-6h0c0 2-1 7 0 9v26 1 5h-1v-3h0c0-2 0-2-1-4v-10-18z" class="f"></path><path d="M323 154c1-3 0-8 1-10v2l1 27v8 3 17 14l-1 1v-3-9l-1-50z" class="Z"></path><path d="M323 127v-1h1c2-1 6-1 8-1h30c5 0 10 0 14 1h-2l1 1h-4-19-14c-4 0-9 1-13 0v1l-2-1z" class="T"></path><path d="M362 125c5 0 10 0 14 1h-2l1 1h-4-19c4-1 8 0 12-1h6c-2 0-5 0-7-1h-1z" class="Z"></path><path d="M325 184c1 3-1 8 0 11v-3c1 0 1-1 1-1h-1v-4c1-1 0-3 1-4l1-3c0-1-1-3 0-5v10 4 24s0 1-1 2v6c0 3 0 6-1 8v-28-17z" class="G"></path><path d="M391 127c7 1 15 3 21 6 10 3 19 7 28 12l9 6c4 4 8 7 12 11h-1c-1 0-2-2-3-2l-6-6c-6-5-13-9-21-12-3-2-6-4-9-5-5-2-10-4-15-5-1 0-1-1-2-1-2-1-5 0-8-1-2 0-4-1-6-1-1 0-3-1-4-1 2 0 3 0 5-1z" class="H"></path><path d="M502 114h1v6 18 3 2l-1 1-1-2v-1l-1-3h-2-1v-3l1-1c0-1 1-2 0-4 1 1 0 1 1 1h0c0-2 1-3 3-4v-2c-1-1-1-2-1-4h0v-1c0-1-1-2-1-4v-1-1c1 1 1 1 1 2v1l1-3z" class="v"></path><path d="M500 114c1 1 1 1 1 2v1 4h0v-1c0-1-1-2-1-4v-1-1z" class="g"></path><path d="M502 127c-1 2 0 8-1 9-1 0-1 1-2 2h1-2-1v-3l1-1c0-1 1-2 0-4 1 1 0 1 1 1h0c0-2 1-3 3-4z" class="K"></path><path d="M210 129h1c4 3 9 6 12 9 6 6 9 13 12 20l3 9c2 5 3 10 4 15 0 2-1 5 0 8 0 0 0 1 1 1v1l-1 1c-2-1-1-2-3-3l1-1c-1-8-2-16-4-24-4-15-12-28-26-36z" class="I"></path><path d="M325 137h0 0l1-5 1-1c0 2-1 3 0 5v39c-1 2 0 4 0 5l-1 3c-1 1 0 3-1 4v4h1s0 1-1 1v3c-1-3 1-8 0-11v-3-8l-1-27 1-9z" class="g"></path><path d="M327 189c1 3 0 6 0 9v15 5c0 1 1 1 1 1l1 1c-1 0-1 1-1 1 2 2 2 4 2 7 0 1-1 2-1 4 0 1-1 3 0 4h1s1 0 1 1c-1 2-1 4-3 5l-1 1c-1-2 0-7 0-9h0c-1 1 0 5-1 6v-1l-1 1v-11c1-2 1-5 1-8v-6c1-1 1-2 1-2v-24z" class="B"></path><path d="M329 232c-2-2-1-5-1-7s-1-3 0-4c2 2 2 4 2 7 0 1-1 2-1 4z" class="Y"></path><path d="M325 229c1-2 1-5 1-8v-6c1-1 1-2 1-2v21h0c-1 1 0 5-1 6v-1l-1 1v-11z" class="j"></path><path d="M169 599c4-2 9-4 13-6 5-3 10-7 15-12 3-2 6-4 9-7 10-12 17-27 23-42v4c0 2-1 4-2 5-2 6-4 12-7 17-2 2-2 4-4 7v-2h0s0-1 1-1h0v-1c0-1 1-2 1-2-1 0-1 1-1 2l-2 3c-1 1-3 3-3 4-1 2-3 3-3 4l-1 1c-1 1-2 1-2 2l-3 3-4 3-7 6c-4 3-8 5-11 7-1 1-1 1-2 1l-1 1c-1 0-2 1-3 1h0c-2 1-3 1-4 2h0l-2 1v-1z" class="K"></path><path d="M167 117c1 0 3 1 4 1h1 1c1 1 1 1 2 0 1 1 2 1 4 2l10 1c2 0 4 1 6 1s4 1 5 2l8 3c1 0 2 1 3 2h-1c-1 0-3-1-4-2-3-1-5-2-7-2-8-3-16-4-25-5-8 0-18 2-26 4-1 1-3 1-5 1-3 1-6 4-10 4l1-1c3-2 8-3 12-4l1-1h0 0v-1c1-1 2-1 3-1 1-1 3-1 5-2h0c4-1 8-1 12-2z" class="O"></path><path d="M167 117c1 0 3 1 4 1h1c-1 2-13 2-16 3-3 0-6 1-9 2h0 0v-1c1-1 2-1 3-1 1-1 3-1 5-2h0c4-1 8-1 12-2z" class="AQ"></path><path d="M109 149l5-3c12-11 30-19 46-21 7-1 14-1 22 0l-1 1h-3c-7 0-13 0-21 1l1 2-12 3v-1c-4 0-8 2-11 4-2 1-4 2-7 2l-9 6c-5 4-9 8-14 12 1-2 2-4 3-5l1-1z" class="Z"></path><path d="M128 137c2-1 5-2 7-3 7-3 15-5 22-7l1 2-12 3v-1c-4 0-8 2-11 4-2 1-4 2-7 2z" class="H"></path><path d="M509 95h1v11 21l1 38v35 9c-1-2-1-4-1-6h0c0 4 0 9-1 13v-12-24-85zm-339 17l2 1-1 2c-1 0-2 1-3 1l-1 1c-4 1-8 1-12 2h0c-2 1-4 1-5 2-1 0-2 0-3 1v1h0 0l-1 1v-1h-1c-1 1-3 1-4 1 1 0 0 0 0-1l2-1h-1-1c-2 2-4 3-6 3l-1 1c1-2 2-3 3-5s2-3 4-4h1c8-6 9-3 17-3h1c3 0 5 0 8-1l2-1z" class="G"></path><path d="M170 112l2 1-1 2c-1 0-2 1-3 1v-1-2l2-1z" class="c"></path><path d="M137 121c1-2 2-3 4-4h1c-1 1-3 2-3 4 0 1 1 1 2 1-2 2-4 3-6 3l-1 1c1-2 2-3 3-5z" class="K"></path><path d="M142 122c0-1 1-1 2-2 2-2 5-3 7-3 1-1 3 0 3 0 1 0 0 1 1 2h0c-2 1-4 1-5 2-1 0-2 0-3 1v1h0 0l-1 1v-1h-1c-1 1-3 1-4 1 1 0 0 0 0-1l2-1h-1z" class="r"></path><path d="M261 384c-1-1-1-6-2-8l-6-24c-1-3-2-6-3-10l1-1 6 14c1 3 2 5 3 7v1 1 1c1 1 1 2 1 3v1 2c1 0 1 0 1 1v2c1 1 1 3 1 4l1 1v3c1 0 1 0 1 1h0v1h0v1c0 1 0 1 1 1 0 1 0 6 1 6 0 1 0 2 1 3v1 1 1c1 0 1 1 1 2h1v-1c-1-1-1-1-1-2v-1h0v-1l-1-1v-2c-1-3-1-6-2-9 0-1 0-3-1-4v-1-1l-1-1c0-1 0-4-1-5v-1c0-1 0-1-1-1v-2-1c-1 0-1-1-1-1v-1c-1-1-1-1-1-2h0v-1-4l4 13c3 10 4 20 7 30-1 1-3 1-4 1v-1c0-1 0-1-1-2v-2l-1-4c-1-1 0-3-1-5 0-1 0-1-1-2h-1v-1h-1z" class="O"></path><path d="M246 323c1 0 1 1 2 1v-1c1 2 2 3 3 5h1 1c0 1 1 2 1 4h1v2 1c5 14 10 28 13 42l3 18c1 2 1 5 2 6h3c-1 1-3 1-4 1-4 0-7 0-11 1l-1 1v-1h1c2-1 4-2 6-2 1 0 3 0 4-1-3-10-4-20-7-30l-4-13c-4-12-9-23-14-34z" class="I"></path><path d="M246 323c1 0 1 1 2 1v-1c1 2 2 3 3 5h1c0 4 2 7 3 10l4 11c1 5 2 9 4 13 1 3 1 5 1 7v1h0l-4-13c-4-12-9-23-14-34z" class="AD"></path><path d="M501 180l1 1 1 1s0 1 1 1v5 13l1 14c0 3 1 7 0 10-1-1-1-2-1-3l-3-9-1-4-2-3v-1c0-1 1-2 1-3 0-3-1-4-2-6-1-3-2-4-4-6h2c-1 0-1-1-1-1l1-1c2 0 3-1 5-3 1-1 1-2 1-3v-2z" class="g"></path><path d="M501 180l1 1c1 3 1 5 0 7h-1 0c0-1 0-2-1-3 1-1 1-2 1-3v-2z" class="P"></path><path d="M497 196c1-1 1-1 2-1 0-1 0-3 1-4h0c0 2-1 4 0 7h0v4h-1c0-3-1-4-2-6z" class="U"></path><path d="M495 188c2 0 3-1 5-3 1 1 1 2 1 3h0c0 2 0 2-1 3h0c-1 1-1 3-1 4-1 0-1 0-2 1-1-3-2-4-4-6h2c-1 0-1-1-1-1l1-1z" class="G"></path><path d="M495 188c2 0 3-1 5-3 1 1 1 2 1 3-2 2-2 2-4 2h-2c-1 0-1-1-1-1l1-1z" class="AA"></path><path d="M500 198v-1c1-3 1-8 3-10 1 5 0 11 0 17 0 5 1 10 1 15v3l-3-9-1-4-2-3v-1c0-1 1-2 1-3h1v-4h0z" class="AQ"></path><path d="M501 204v3c1 2 1 4 0 6l-1-4h0c1-2 1-4 1-5z" class="v"></path><path d="M500 198c1 2 0 4 1 6 0 1 0 3-1 5h0l-2-3v-1c0-1 1-2 1-3h1v-4z" class="K"></path><path d="M343 120c18-1 37-1 55 3 6 1 13 3 19 5 1-1 3-1 5-1h1 2c1 0 2-1 3-1l1 1h1c1-1 2 0 3 1-1 0-2 0-3 1v2l-1-1v3c1 0 2 1 3 1l6 3c3 2 7 4 10 6l11 9a30.44 30.44 0 0 1 8 8c2 2 4 4 5 6l8 9c1 2 3 4 5 7 0 0 0 1 1 1l-1 1c-3-3-5-7-7-10-3-3-5-6-8-9-5-6-11-12-17-17-3-2-6-4-10-6-9-6-20-11-31-15-5-1-11-2-17-3-11-2-22-3-33-3h-38v-1h0 19z" class="o"></path><path d="M428 126l1 1h1c1-1 2 0 3 1-1 0-2 0-3 1v2l-1-1v3c-4-2-8-4-12-5 1-1 3-1 5-1h1 2c1 0 2-1 3-1z" class="r"></path><path d="M428 126l1 1h1c1-1 2 0 3 1-1 0-2 0-3 1v2l-1-1c-2 0-4-1-6-3h2c1 0 2-1 3-1z" class="U"></path><path d="M226 550h2c0 2-1 3-2 5-2 4-3 8-6 12v2c-1 2-3 3-4 5-2 3-6 6-8 8 1 1 2 1 2 1 0 1-1 2-1 3h0l1 1h0l-1 1c1 1 2 1 3 3v1h-2l-2 1h-3s0 1-1 1h-1c-2 2-3 2-3 4l-1-1c0-1 1-1 1-2h-1v-1c0-1-1-1-1-2v-1-1h-1-1 0-1c1-2 3-3 4-5 8-6 14-14 20-22 3-4 5-9 7-13z" class="c"></path><path d="M199 585l2 1c0 1-3 2-2 3-1 1-1 2-1 2v-1h-1-1 0-1c1-2 3-3 4-5z" class="V"></path><path d="M205 584l1-1 3-3 8-10 3-3v2c-1 2-3 3-4 5-2 3-6 6-8 8-1 1-1 2-3 2z" class="I"></path><path d="M205 584c2 0 2-1 3-2 1 1 2 1 2 1 0 1-1 2-1 3h0l1 1h0l-1 1c1 1 2 1 3 3v1h-2l-2 1h-3s0 1-1 1h-1c-2 2-3 2-3 4l-1-1c0-1 1-1 1-2h-1v-1c0-1-1-1-1-2v-1s0-1 1-2 3-2 5-3h0l1-2z" class="G"></path><path d="M205 584c2 0 2-1 3-2 1 1 2 1 2 1 0 1-1 2-1 3h0l1 1h0l-1 1c-2-1-2-1-5-1-1 1-3 2-4 3 0 1 1 2 0 3h-1l-1-1v-1s0-1 1-2 3-2 5-3h0l1-2z" class="d"></path><path d="M205 584c2 0 2-1 3-2 1 1 2 1 2 1 0 1-1 2-1 3h0l1 1-3-1c-1-1-2 0-3 0h0l1-2z" class="r"></path><path d="M376 126c5 0 10 0 15 1-2 1-3 1-5 1 1 0 3 1 4 1 2 0 4 1 6 1 3 1 6 0 8 1 1 0 1 1 2 1 5 1 10 3 15 5 3 1 6 3 9 5h0-1 0c-2 0-1-1-3 0-1 0-2-1-3-1l-5-2c-5-2-9-4-14-5-21-6-41-5-63-5h-9-5v2l-1 1-1 5h0 0v-9-1c4 1 9 0 13 0h14 19 4l-1-1h2z" class="K"></path><path d="M376 126c5 0 10 0 15 1-2 1-3 1-5 1h-5l-10-1h4l-1-1h2z" class="T"></path><path d="M208 115l1 1c1 1 0 1 1 2l2 2h1l2 2v-1c0 2 1 3 2 3h1c2 2 4 4 4 6l1 1c3 5 8 10 10 15h-1l4 11v1h-1 0c-3-7-6-14-12-20-3-3-8-6-12-9-1-1-2-2-3-2-2-1-5-2-8-3l1-1h-1 0c-1-1-2-1-2-2l3-3h1c1 0 1 0 1-1h3 1c0-1-1-1-1-1l2-1z" class="k"></path><path d="M208 115l1 1c1 1 0 1 1 2l2 2h1l2 2v-1c0 2 1 3 2 3h1c2 2 4 4 4 6-1-1-3-2-4-3-3-2-6-3-8-4s-3-1-4-1h-1v-1c1 0 3 0 4-1h-1-1-2-2-1 0v-2c1 0 1 0 1-1h3 1c0-1-1-1-1-1l2-1z" class="b"></path><path d="M204 119h2l1 1h-2-2l1-1z" class="n"></path><path d="M206 117h1v1c-1 1-2 0-3 1l-1 1h-1 0v-2c1 0 1 0 1-1h3z" class="AC"></path><path d="M198 121l3-3h1v2h0 1 2c-1 1-1 2-2 3 2 2 8 3 11 5 7 4 14 11 18 18l4 11v1h-1 0c-3-7-6-14-12-20-3-3-8-6-12-9-1-1-2-2-3-2-2-1-5-2-8-3l1-1h-1 0c-1-1-2-1-2-2z" class="h"></path><path d="M177 109h17c2 1 5 1 7 2 1 0 2 1 4 1h0 1 0l2 3-2 1s1 0 1 1h-1-3c0 1 0 1-1 1h-1l-3 3c0 1 1 1 2 2h0 1l-1 1c-1-1-3-2-5-2s-4-1-6-1l-10-1c-2-1-3-1-4-2-1 1-1 1-2 0h-1-1c-1 0-3-1-4-1l1-1c1 0 2-1 3-1l1-2-2-1c2 0 2-1 3-1l4-2z" class="AC"></path><path d="M173 111l4-2c1 1 1 1 2 1-3 2-5 1-7 3l-2-1c2 0 2-1 3-1z" class="H"></path><path d="M171 115h1c1-1 2-2 3-2h1l-1 1c0 1-1 1-1 2v1h-1v1h-1-1c-1 0-3-1-4-1l1-1c1 0 2-1 3-1zm16-3c2 0 8-1 10 0h1l1 1h2c1 0 2 1 3 2 1-1 1-1 1-2h0l1-1 2 3-2 1s1 0 1 1h-1-3c0 1 0 1-1 1h-1l-3 3c-1-1-1-1-1-2l1-2h0l1-1-2-1c-2-1-5 0-7-1h0v-1h-3v-1z" class="AD"></path><path d="M196 113c3 0 5 1 9 3-2 1-4 0-6 0l-2-1h1c0-1-2-2-2-2z" class="s"></path><path d="M187 112l9 1s2 1 2 2h-1c-2-1-5 0-7-1h0v-1h-3v-1z" class="d"></path><path d="M205 116c1 0 1 0 1 1h-3c0 1 0 1-1 1h-1l-3 3c-1-1-1-1-1-2l1-2h0l1-1c2 0 4 1 6 0z" class="f"></path><path d="M174 116c4-2 8-3 13-3h3v1h0c2 1 5 0 7 1l2 1-1 1h0l-1 2c0 1 0 1 1 2 0 1 1 1 2 2h0 1l-1 1c-1-1-3-2-5-2s-4-1-6-1l-10-1c-2-1-3-1-4-2-1 1-1 1-2 0v-1h1v-1z" class="v"></path><path d="M174 116c4-2 8-3 13-3h3v1h0c2 1 5 0 7 1l2 1-1 1c-3 0-6-1-9-1-4 0-10 0-14 2-1 1-1 1-2 0v-1h1v-1z" class="U"></path><path d="M174 116c4-2 8-3 13-3h3v1h0c-5 0-8 1-13 2-1 1-1 1-3 1v-1z" class="R"></path><path d="M170 601h1c0-1 1-1 1-1h1c1 0 1-1 2-1s1-1 2-1c0 1-1 1-2 2-1 0-2 1-4 1v1c2 0 3-1 5-2 1-1 4-2 5-3s1-1 2-1l1-1h1l-2 2h-1 0c-1 1-3 2-4 3h-1c-1 1-3 2-4 2l-1 1-1 1c-1 0-1 0-2 1h1c-4 2-8 3-12 5l-7 2v1c-2 1-4 1-6 2v1h-2c0 1 2 1 3 2-6 0-12 1-18 2h-6-4-4-1c-2-1-4-1-5-2h0c1-1 0-1 1-1s1-1 2-1c0-1 2-1 3-1 4-1 8-1 13-2 11-2 23-5 33-11 3 0 5-2 8-3h0c0 1 0 1-1 1s-1 0-2 1h0c-1 0-1 0-1 1h-1 0c-2 1-4 1-5 2h3 1c1-1 1-1 2-1 0-1 0-1 1-1h0c1 0 1-1 1-1h1 3z" class="AB"></path><path d="M111 616c0-1 2-1 3-1 1 1 2 1 3 1-3 0-6 1-9 2 1-1 0-1 1-1s1-1 2-1z" class="H"></path><path d="M118 620v-1h3l7-1 23-6v1c-2 1-4 1-6 2v1h-2c0 1 2 1 3 2-6 0-12 1-18 2h-6-4z" class="c"></path><path d="M168 599h0c0 1 0 1-1 1s-1 0-2 1h0c-1 0-1 0-1 1h-1 0c-2 1-4 1-5 2h3 1c1-1 1-1 2-1 0-1 0-1 1-1h0c1 0 1-1 1-1h1 3c-4 2-10 4-15 6-9 2-17 5-26 7-4 1-8 1-12 2-1 0-2 0-3-1 4-1 8-1 13-2 11-2 23-5 33-11 3 0 5-2 8-3z" class="o"></path><path d="M501 156c1 2 1 3 2 4v1h1c1 4 0 10 0 15 0 1 0 4-1 6l-1-1-1-1v2c0 1 0 2-1 3-2 2-3 3-5 3-1 0-3-1-4-1 0-2 0-2-1-3v-2h-1v1h-1c-1-1-2-3-2-4 0-3-1-5-2-8v-3l-1-2-1-2 1-1h0c1 0 1 1 2 1v-1c1 0 2 1 2 1l1-1c4 2 6 2 10 1 1-1 2-1 2-2 1-1 1-1 1-2 0-2 0-2-1-3l1-1z" class="K"></path><path d="M494 182h-1c0-1-1-1-1-2 0-2 0-1 1-2h1v2c0 1 1 1 0 2z" class="G"></path><path d="M493 178c2-1 2-1 3 0 1 0 2 1 2 2v1c-1 1-2 1-3 2l-1-1h0c1-1 0-1 0-2v-2h-1z" class="U"></path><path d="M500 168v-1l2-2v3h0c-2 1-2 2-3 3h-4c-2 0-3-1-4-1 3-1 5 0 8-1l1-1z" class="g"></path><path d="M485 164v-1c1 0 2 1 2 1 2 2 4 4 7 4v1c2 0 4 0 6-1l-1 1c-3 1-5 0-8 1l-2-2c-1 0-2-1-3-2h0c0 2 1 4 0 7 0-3-1-6-1-8v-1z" class="G"></path><path d="M483 163h0c1 0 1 1 2 1v1c0 2 1 5 1 8 1 3 1 6 2 10h1-1c-1-1-2-3-2-4 0-3-1-5-2-8v-3l-1-2-1-2 1-1z" class="T"></path><path d="M483 163h0c1 0 1 1 2 1v1c-1 1-1 2-1 3l-1-2-1-2 1-1z" class="a"></path><path d="M503 161h1c1 4 0 10 0 15 0 1 0 4-1 6l-1-1-1-1v-1-1s-1 0-1-1h0l1 1c0-1 1-2 1-3v-7-3l1-4z" class="v"></path><path d="M495 183h3c2-2 0-3 1-5 1 1 1 2 2 4 0 1 0 2-1 3-2 2-3 3-5 3-1 0-3-1-4-1 0-2 0-2-1-3v-2h4l1 1z" class="AQ"></path><path d="M501 156c1 2 1 3 2 4v1l-1 4-2 2v1c-2 1-4 1-6 1v-1c-3 0-5-2-7-4l1-1c4 2 6 2 10 1 1-1 2-1 2-2 1-1 1-1 1-2 0-2 0-2-1-3l1-1z" class="AH"></path><path d="M493 136c2 0 3 2 5 3v-1h2l1 3v1l1 2v2 2 3h1v1 7c1 0 0 1 0 1h0c-1-1-1-2-2-4l-1 1c1 1 1 1 1 3 0 1 0 1-1 2 0 1-1 1-2 2-4 1-6 1-10-1l-1 1s-1-1-2-1v1c-1 0-1-1-2-1h1v-2-2-2-1-1-3l1-3c0-2 1-4 2-6l2-2c1-1 2-4 4-5z" class="r"></path><path d="M498 138h2l1 3v1h-1v2l-1-2c0-2-1-2-1-3v-1z" class="H"></path><path d="M492 144c2 0 4 0 7 1-1 1-1 1-3 1h-2l-2-2z" class="P"></path><path d="M487 150v-1c1-3 2-4 5-5l2 2c-1 0-2 0-3 1h0c1 1 2 0 4 0l-3 1c-1 0-3 0-4 1l-1 1h0z" class="V"></path><path d="M500 144v-2h1l1 2v2l-2 2c-2 0-4 0-5-1-2 0-3 1-4 0h0c1-1 2-1 3-1h2c2 0 2 0 3-1l1-1z" class="k"></path><path d="M484 152l1-3v7h0l1-1c0-1-1-2-1-2 0-1 1-2 1-2 0 5 1 7 4 10v1-1c2 1 4 3 5 3h3c-4 1-6 1-10-1l-1 1s-1-1-2-1v1c-1 0-1-1-2-1h1v-2-2-2-1-1-3z" class="K"></path><path d="M484 157v-1-1-3c0 2 1 4 2 6l-2 1v-2z" class="o"></path><path d="M486 158v3l2 2-1 1s-1-1-2-1v1c-1 0-1-1-2-1h1v-2-2l2-1z" class="H"></path><path d="M502 146v2 3c0 1 0 1-1 1h-3c-2 1-4 1-6 1 0 0-1 0-1 1v1l-1 6h0v1-1c-3-3-4-5-4-10l1-1h0l1-1c1-1 3-1 4-1l3-1c1 1 3 1 5 1l2-2z" class="p"></path><path d="M487 150h0l1-1c1-1 3-1 4-1l-3 3c-1 2-1 5 0 7 0 1 1 2 1 3h0v1-1c-3-3-4-5-4-10l1-1z" class="P"></path><path d="M502 151h1v1 7c1 0 0 1 0 1h0c-1-1-1-2-2-4l-1 1c1 1 1 1 1 3 0 1 0 1-1 2 0 1-1 1-2 2h-3c-1 0-3-2-5-3h0l1-6v-1c0-1 1-1 1-1 2 0 4 0 6-1h3c1 0 1 0 1-1z" class="v"></path><path d="M500 162c-2 0-2 0-4-1l-1-1c0-1 0-2 1-3l1-1h4l-1 1c1 1 1 1 1 3 0 1 0 1-1 2z" class="V"></path><path d="M501 160l-2-1c-1 1-1 1-2 1s-1-1-1-1c0-1 0-2 1-2 1-1 2-1 3 0s1 1 1 3z" class="AQ"></path><path d="M233 532l4-14c1-6 2-12 2-18 1-8 1-16 1-25v-36-121l2 5v60l1 68v18c-2 7 0 15-1 22-1 2 0 4-1 7 0 1 0 3-1 5v3l-1 12c0 2-1 3-1 4-1 3-1 5-2 8-1 1-1 3-2 5 0-1 0-2-1-3z" class="T"></path><path d="M243 469v21c-1 3-1 7-1 11v3l1 1h1c1 2 3 0 4 2h-1l1 2v1h-3l2 2c-2 0-2 1-4 2h1c0 1 1 2 1 2 1 1 1 1 2 0v1c0 1 1 2 3 3v2c-1 1-1 1-1 2-1 3-2 8-4 10-1 3-2 5-3 8-2 0-3 3-3 4l-7 9c-1 3-3 5-5 7-1 2-2 3-3 5l-1-1-2 1-1 2v-2c3-4 4-8 6-12 1-2 2-3 2-5l6-15c1-2 1-4 2-5 1-3 1-5 2-8 0-1 1-2 1-4l1-12v-3c1-2 1-4 1-5 1-3 0-5 1-7 1-7-1-15 1-22z" class="g"></path><path d="M247 517c0 1 1 2 3 3v2c-1 1-1 1-1 2v-2c0-1-1-1-2-2-1 0-1 0-2 1h0v-2c1-1 1-2 2-2z" class="i"></path><path d="M224 563c3-3 4-6 6-9 1-2 3-3 4-5 1-1 1-2 2-3 0-1 0-1 1-1h0v-1l1-1c0-1 0-2 1-3h0v1h1v1s-1 1-1 2c2-2 3-5 4-6l2-4h0 0c-1 3-2 5-3 8-2 0-3 3-3 4l-7 9c-1 3-3 5-5 7-1 2-2 3-3 5l-1-1-2 1 3-4z" class="p"></path><path d="M243 469v21c-1 3-1 7-1 11v3l1 1h1c1 2 3 0 4 2h-1l1 2v1h-3l2 2c-2 0-2 1-4 2h1c0 1 1 2 1 2 1 1 1 1 2 0v1c-1 0-1 1-2 2v2h0c-1 1-1 1-2 1s-1-1-2-2c-1-2-1-2-1-4 0 1 0 2-1 3v3c0 1 0 2-1 4-1 3-1 5-2 8s-2 7-3 10l-4 9c-2 3-4 7-5 10l-3 4-1 2v-2c3-4 4-8 6-12 1-2 2-3 2-5l6-15c1-2 1-4 2-5 1-3 1-5 2-8 0-1 1-2 1-4l1-12v-3c1-2 1-4 1-5 1-3 0-5 1-7 1-7-1-15 1-22z" class="a"></path><path d="M241 520c0-3-1-4 0-6v-1c0-2 1-3 1-4s0-1 1-2h3 1 0l1 2v1h-3l2 2c-2 0-2 1-4 2h1c0 1 1 2 1 2 1 1 1 1 2 0v1c-1 0-1 1-2 2v2h0c-1 1-1 1-2 1s-1-1-2-2z" class="G"></path><path d="M247 507h0l1 2v1h-3c-1 1-2 1-2 2v-1c0-1 1-2 1-2h2c0-1 0-1 1-2z" class="Z"></path><path d="M243 512c0-1 1-1 2-2l2 2c-2 0-2 1-4 2h1c0 1 1 2 1 2 1 1 1 1 2 0v1c-1 0-1 1-2 2v2l-2-1-1-1v-1c-1-3 0-4 1-6h0z" class="b"></path><path d="M243 520c0-2-1-4 0-6h1c0 1 1 2 1 2 1 1 1 1 2 0v1c-1 0-1 1-2 2v2l-2-1z" class="c"></path><path d="M130 125c6-8 13-14 21-20 1-2 3-4 6-5h0c3-1 6-1 9-1h14 13-6v1l2 3 1 2 4 2h2l6 2 3 2v-1c0 1 0 1 1 2h-1 0c-2 0-3-1-4-1-2-1-5-1-7-2h-17l-4 2c-1 0-1 1-3 1l-2 1c-3 1-5 1-8 1h-1c-8 0-9-3-17 3h-1c-2 1-3 2-4 4l-2 1c-1 0-3 2-5 3h0z" class="n"></path><path d="M157 106c0-1 0-1 1-1 0-1 2-1 3-1v3h-2-2v-1z" class="AE"></path><path d="M160 114c1 0 2 0 3-1h1 1 1l1-1c1 0 1 0 2-1h0c1-1 3-1 4 0-1 0-1 1-3 1l-2 1c-3 1-5 1-8 1zm25-10h1c1 0 2 0 3 1h1l4 2h-1c-2 1-6-1-8-2v-1z" class="K"></path><path d="M183 102c1 0 1 0 1-1 1-1 1-1 3-1l2 3 1 2h-1c-1-1-2-1-3-1h-1c-1 0-1 0-2-2z" class="j"></path><path d="M161 104h3s1 1 2 1 2 1 4 2h-2c-1 1-6 1-7 0h-1-1 2v-3z" class="V"></path><path d="M161 104h3v1c0 1 0 1-1 2h-2v-3z" class="AH"></path><path d="M166 102c2 0 3-1 5-1h1 3c2 0 4 0 6 1h1l1 2h0l1-2h-1c1 2 1 2 2 2v1c-4-1-7-1-11-1-3-1-5-1-8-2z" class="i"></path><path d="M130 125c6-8 13-14 21-20 1-2 3-4 6-5h0c3-1 6-1 9-1h14 13-6v1c-2 0-2 0-3 1 0 1 0 1-1 1h1l-1 2h0l-1-2h-1c-2-1-4-1-6-1h-3-1c-2 0-3 1-5 1-4 0-7-1-10 2v2h1v1h2 1 1c1 1 6 1 7 0h2 1v1h-3c-1 1-1 2-3 1-3 0-7-1-11-2v-2c-2 1-4 4-6 6l-7 6c-2 1-3 2-4 4l-2 1c-1 0-3 2-5 3h0z" class="c"></path><defs><linearGradient id="AL" x1="257.595" y1="326.553" x2="251.385" y2="400.56" xlink:href="#B"><stop offset="0" stop-color="#f5d89e"></stop><stop offset="1" stop-color="#fff9d0"></stop></linearGradient></defs><path fill="url(#AL)" d="M242 323c4 6 7 12 9 18l-1 1c1 4 2 7 3 10l6 24c1 2 1 7 2 8h1v1h1c1 1 1 1 1 2 1 2 0 4 1 5l1 4v2c1 1 1 1 1 2v1c-2 0-4 1-6 2-1-2-1-3-1-5 0-1 0-3-1-4-1-3-1-6-3-8-1-2-3-4-5-4-2-1-3 0-5 1-1 1-2 3-2 5l-1-1c0-1 0-2-1-4v-60z"></path><path d="M248 366h1v1c0 1 1 2 1 2l-1 1c-1-1-1-2-1-4z" class="p"></path><path d="M245 365c0-1 1-1 1-2h7 0c1 0 1 1 1 1h0-1v1 1l-3 1h0v3 1c1 0 1 0 1 1v1c-1-1-1-1-1-2v-1-1s-1-1-1-2v-1h-1l-3 1-1-1c0-1 0-1 1-1z" class="K"></path><path d="M245 365c3-1 4-1 6 0h1v1h-3-1l-3 1-1-1c0-1 0-1 1-1z" class="G"></path><path d="M261 384h1v1h1c1 1 1 1 1 2 1 2 0 4 1 5l1 4v2c1 1 1 1 1 2v1c-2 0-4 1-6 2-1-2-1-3-1-5 0-1 0-3-1-4 1 0 2 1 2 2v3 2 1c1-1 1-1 2-1v-1c1 0 2 1 2 0h1 0v-1h-1-3c0-2 0-2-1-3v-1h0v-1-1c1-1 0-3 0-4h0v-5z" class="b"></path><path d="M261 384h1v1l1 5-1 1c0-1 0-2-1-2v-5z" class="a"></path><path d="M262 391l1-1c0 2 0 4 1 6 0 1 1 1 1 2l-1 1-1-1v-1c-1-2-1-3-1-6z" class="o"></path><path d="M316 212s0 1 1 1v2 1l-1 2v4h1v37 25c0 3 1 6 0 9-1-4 0-8-1-12v1h-1v-3c-1-3-6-1-8-2l-1-1h0c-3-2-5-5-8-6h0 0v-1l-5-3-2-1h0c8-4 14-10 18-18l2-4c1-1 2-3 4-3v-1h-2c-1 1-1 1-2 1l-1-2v-2h-2l-1-2h0c-2-1-2-2-4-2l-1 1-2-1-1 1c-1-1-1-1-1-2v-1c1-1 2-2 2-3l1 1h1l1-3c0-1 0-2-1-3 2-1 4-1 6-1 1 0 3-1 3-2h0 0l2-2 1-1v-1h0l2-3z" class="b"></path><path d="M308 233l1-1h2c2-2 1-4 5-5v3 9h-1-2c-1 1-1 1-2 1l-1-2v-2l-2-3z" class="K"></path><path d="M316 212s0 1 1 1v2 1l-1 2v4h-1s0 2-1 2l-2 2c-1 1-3 4-5 5 0 0-1-1-1 1 1 0 1 1 2 1l2 3h-2l-1-2h0c-2-1-2-2-4-2l-1 1-2-1-1 1c-1-1-1-1-1-2v-1c1-1 2-2 2-3l1 1h1l1-3c0-1 0-2-1-3 2-1 4-1 6-1 1 0 3-1 3-2h0 0l2-2 1-1v-1h0l2-3z" class="t"></path><path d="M300 227l1 1-1 1v3l-1 1c-1-1-1-1-1-2v-1c1-1 2-2 2-3z" class="AN"></path><path d="M314 216h1 0c0 1 0 1 1 1-3 2-5 4-8 4 1 0 3-1 3-2h0 0l2-2 1-1z" class="y"></path><path d="M316 212s0 1 1 1v2 1l-1 1c-1 0-1 0-1-1h0-1v-1h0l2-3z" class="AG"></path><path d="M316 212s0 1 1 1c-1 1-1 2-3 2h0l2-3z" class="t"></path><path d="M303 232l2-3c1-2 4-4 5-5s2-1 3-2 2-3 3-4v4h-1s0 2-1 2l-2 2c-1 1-3 4-5 5 0 0-1-1-1 1 1 0 1 1 2 1l2 3h-2l-1-2h0c-2-1-2-2-4-2z" class="U"></path><path d="M315 240h1c0 3 0 6-1 9v4c0 2-1 3-2 5h0v1 5 5c1 2 1 5 1 8-1 0-2 0-3-1h0c-1 0-2-1-2-2h-1l1 2h0c-4-2-7-6-11-7l-5-3-2-1h0c8-4 14-10 18-18l2-4c1-1 2-3 4-3h0z" class="j"></path><path d="M312 249c1-1 2-1 2-2v2c0 1-1 1-1 2h0c-1 1-1 2-1 3s-1 2-1 2c0 1 1 1 1 1v1 1l-2-2c1 0 0 0 0-1v-3c1-1 2-3 2-4z" class="AD"></path><path d="M315 240h0c0 2 0 4-1 7 0 1-1 1-2 2 0 1-1 3-2 4v3c0 1 1 1 0 1h-1-4c-2 3-6 8-10 8-1 0-1 1-2 1l-2-1h0c8-4 14-10 18-18l2-4c1-1 2-3 4-3z" class="I"></path><path d="M305 257l3-3c1 1 1 2 2 2 0 1 1 1 0 1h-1-4z" class="G"></path><path d="M309 250l1 1c0-1 1-1 2-2 0 1-1 3-2 4h-2c0-1 0-2 1-3z" class="R"></path><path d="M315 240h0c0 2 0 4-1 7 0 1-1 1-2 2s-2 1-2 2l-1-1c1-2 2-4 2-7 1-1 2-3 4-3z" class="q"></path><path d="M207 109h1v-2c1 0 1-1 1-1h2 1v1s1 0 1 1l1 2v-1l1 1 1 1 1-1c1 0 2-1 3-1-1 1-1 3-1 4s-1 3 0 4l3 5-1 1c0 1 1 2 2 2l1 1c1 1 2 3 3 4 0 1 0 2 2 3h1c0 2 1 3 2 4l1-3 1 1c1 1 1 1 1 2v3h-1c1 3 2 5 3 7l3 8c1-1 1-2 2-4 0 3 1 6 3 8l1 4 2 3c0 1 1 2 2 4s5 7 8 8h0c1 1 2 1 3 2l2-1h0c1 0 2 0 3-1h1c1 1 1 1 1 2v1h1c0 1 0 1 1 1 0 0 1 0 1 1h0c-1 2-4 3-6 4-1 1-2 2-3 4-1 0-1 1-2 2l-1-1c1-1 3-3 3-5l-1 1v-1l-1-1c-1 0-2 1-3 1-1 2-2 3-4 4h-2l-4 4c-1 0-2 0-3-1h-1c0-1-1-1-1-1h0l1-1v-1c-1 0-1-1-1-1-1-3 0-6 0-8-1-5-2-10-4-15l-3-9h0 1v-1l-4-11h1c-2-5-7-10-10-15l-1-1c0-2-2-4-4-6h-1c-1 0-2-1-2-3v1l-2-2h-1l-2-2c-1-1 0-1-1-2h0c0-1 0-2-1-3h0c0-2-1-3-1-4z" class="P"></path><path d="M256 183c1 1 3 1 4 2h3 1c0-1 1-1 2-1l-1 1h-2c0 1-1 1-2 2l-1-1c-1 0-2 1-3 1h-2v-1c0-1 0-2 1-3h0z" class="O"></path><path d="M236 157l10 16h-2c0-1-3-5-4-6v1l-2-1-3-9h0 1v-1zm14 13c1 2 5 7 8 8h0c1 1 2 1 3 2l2-1h0c1 0 2 0 3-1h1c1 1 1 1 1 2v1h1c0 1 0 1 1 1 0 0 1 0 1 1h0-4c-8-1-14-4-20-10 3 0 5 4 8 5l-3-2-3-3h1l-1-2 1-1z" class="n"></path><path d="M266 178h1c1 1 1 1 1 2h-5v-1h0c1 0 2 0 3-1z" class="r"></path><path d="M250 170c1 2 5 7 8 8h0c1 1 2 1 3 2l2-1v1 1c-2-1-3-1-4-2-1 0-2 0-3-1-2-2-4-3-6-5h0l-1-2 1-1z" class="T"></path><path d="M240 168v-1c1 1 4 5 4 6h2c1 1 2 3 2 4 1 2 4 3 5 5 1 0 2 1 3 1h0c-1 1-1 2-1 3v1h2c-1 2-2 3-4 4h-2l-4 4c-1 0-2 0-3-1h-1c0-1-1-1-1-1h0l1-1v-1c-1 0-1-1-1-1-1-3 0-6 0-8-1-5-2-10-4-15l2 1z" class="p"></path><path d="M240 168v-1c1 1 4 5 4 6 1 2 2 3 2 5h0-2c-1-1-2-3-2-5-1-1-1-3-2-5z" class="f"></path><path d="M244 178h2c1 2 1 2 3 3h1l2 2c1 0 1 1 1 1h1v-1 1c1 0 1 0 2-1-1 1-1 2-1 3v1c-1-1-3-2-4-2-1-1-1-2-2-2-3-1-4-3-5-5z" class="i"></path><path d="M246 173c1 1 2 3 2 4 1 2 4 3 5 5 1 0 2 1 3 1h0c-1 1-1 1-2 1v-1 1h-1s0-1-1-1l-2-2h-1c-2-1-2-1-3-3h0c0-2-1-3-2-5h2z" class="H"></path><path d="M207 109h1v-2c1 0 1-1 1-1h2 1v1s1 0 1 1l1 2v-1l1 1 1 1 1-1c1 0 2-1 3-1-1 1-1 3-1 4s-1 3 0 4l3 5-1 1c0 1 1 2 2 2l1 1c1 1 2 3 3 4 0 1 0 2 2 3h1c0 2 1 3 2 4l1-3 1 1c1 1 1 1 1 2v3h-1c1 3 2 5 3 7l3 8c1-1 1-2 2-4 0 3 1 6 3 8l1 4 2 3c0 1 1 2 2 4l-1 1 1 2h-1l3 3 3 2c-3-1-5-5-8-5-5-5-8-14-11-20-1-2-1-5-3-7-2-5-7-10-10-15l-1-1c0-2-2-4-4-6h-1c-1 0-2-1-2-3v1l-2-2h-1l-2-2c-1-1 0-1-1-2h0c0-1 0-2-1-3h0c0-2-1-3-1-4z" class="v"></path><path d="M248 166c0 1 1 2 2 4l-1 1c-1-1-2-2-3-4h0c1 0 1 0 2-1z" class="U"></path><path d="M233 134l1 1c1 1 1 1 1 2v3h-1c0-1-1-2-2-3l1-3z" class="Z"></path><path d="M214 110v-1l1 1 1 1h0c-2 2-2 4-1 6 0 2 1 3 1 4l2 3h-1c-1 0-2-1-2-3 0-1-2-2-2-4-1-2 0-5 1-7z" class="U"></path><path d="M220 109c-1 1-1 3-1 4s-1 3 0 4l3 5-1 1c-2-3-5-6-5-11h0v-1h0l1-1c1 0 2-1 3-1z" class="f"></path><path d="M240 155c1-1 1-2 2-4 0 3 1 6 3 8l1 4 2 3c-1 1-1 1-2 1h0s-1-1-1-2c-2-3-3-7-5-10zm-33-46h1v-2c1 0 1-1 1-1h2 1v1s1 0 1 1l1 2c-1 2-2 5-1 7 0 2 2 3 2 4v1l-2-2h-1l-2-2c-1-1 0-1-1-2h0c0-1 0-2-1-3h0c0-2-1-3-1-4z" class="G"></path><path d="M212 107s1 0 1 1v1h-2c0-1 0-2 1-2z" class="h"></path><path d="M213 108l1 2c-1 2-2 5-1 7 0 2 2 3 2 4v1l-2-2c-1-1-2-3-2-4-1-3-1-4 0-7h2v-1z" class="K"></path><path d="M291 265l2 1 5 3v1h0 0c3 1 5 4 8 6h0l1 1c2 1 7-1 8 2v3h1v-1c1 4 0 8 1 12l-1 1h0c-4 1-8 4-12 5h-1c-2 1-4 2-7 2l-2 1h-4-3l-4-2-1-1-2 1-1-1c-1-1-2-3-3-4l-1-2-1 1-1-2-2 1h0c-1-3-1-6-1-9 1-3 1-7 3-9 1-2 2-3 3-4l-1-1c3-2 6-4 9-5 2 0 5 1 7 0z" class="AE"></path><path d="M279 288l-1-1v-1l1-1c0-2 1-3 2-4v2c1 1 1 1 2 1v2l-2 2c0-1 1-2 1-3-1 1-1 0-1 1l-2 2z" class="AD"></path><path d="M290 285h1 0c0 1-1 1-1 1s0 1-1 1c0 0-1 0-1 1-1 0-2 1-2 1 0 1 0 1-1 2l-1-1c0 1 0 1-1 1v-1-2c1 0 1 0 2-1 0 0 1-1 2-1 1-1 2-1 3-1z" class="t"></path><path d="M285 294v-2h1c0-1 1-1 1-1l1-2h1l1 1c0 1 1 2 2 3l-1 1v1c-1 0-1 0-2-1h-2-1-1z" class="U"></path><path d="M289 294h1l-2-3v-1h2c0 1 1 2 2 3l-1 1v1c-1 0-1 0-2-1z" class="j"></path><path d="M286 285c2-1 5-2 6-1h2v1h1 1l1 1v1h-2v3h-1c-1-1-2-1-3-2l1-1h0l1-1c-1-1-1-1-2-1h-1-4z" class="P"></path><path d="M291 280c3 1 5 3 6 5v1h0l-1-1h-1-1v-1h-2c-1-1-4 0-6 1-1 0-2 1-3 1v-2c1 0 2-1 3-1s2-1 3-1h2v-2z" class="H"></path><path d="M281 281c1 0 2-1 3-1 1-1 5-1 7 0v2h-2c-1 0-2 1-3 1s-2 1-3 1-1 0-2-1v-2z" class="AB"></path><path d="M279 293c-1 0-1-1-1-1-1-2-1-2 0-4v3l2 2c1 0 1-1 1-1 2 0 3 1 4 2h0 1 1 2c1 1 1 1 2 1h1l-2 1v1c2 1 4 1 6 1l-1 1c0 1 0 1-1 1 1 0 1 0 2 1h0l-2 1h-4-3l-4-2-1-1-2 1-1-1c-1-1-2-3-3-4 1 0 1-1 2-1l1-1z" class="k"></path><path d="M291 300c-1 0-1 0-2-1h1 5c0 1 0 1-1 1h-3z" class="d"></path><path d="M286 294h1 2c1 1 1 1 2 1h1l-2 1v1c-2 0-3-2-4-3z" class="H"></path><path d="M294 300c1 0 1 0 2 1h0l-2 1h-3c-1-1 0-1 0-2h3z" class="V"></path><path d="M276 295c1 0 1-1 2-1l1-1 2 2h2c1 1 2 3 3 4 2 1 3 3 5 3h3-4-3l-4-2-1-1-2 1-1-1c-1-1-2-3-3-4z" class="a"></path><path d="M279 299l1-5c2 2 3 5 5 6 1 0 2 1 2 2l-4-2-1-1-2 1-1-1z" class="j"></path><path d="M306 276l1 1c2 1 7-1 8 2v3h1v-1c1 4 0 8 1 12l-1 1h0c-4 1-8 4-12 5h-1c-2 1-4 2-7 2h0c-1-1-1-1-2-1 1 0 1 0 1-1l1-1c-2 0-4 0-6-1v-1l2-1h-1v-1l1-1c2 1 4 1 5 0s3-3 3-4h1 0l1-2h0l1-2h1 1v3c1-2 1-3 0-5h1s1 0 1-1l1-1v-2l-2-3z" class="AC"></path><path d="M306 276l1 1c2 1 7-1 8 2v3h0c-2 0-4 0-6-1h0c0 3 0 5-1 8v-4-4-2l-2-3z" class="e"></path><path d="M307 282l1-1v4 4c0 2-1 6-3 8h-1l-1 2c-2 1-4 2-7 2h0c-1-1-1-1-2-1 1 0 1 0 1-1l1-1c3-1 5-3 7-5h0c1-2 2-3 2-5 1-2 1-3 0-5h1s1 0 1-1z" class="AA"></path><path d="M307 282l1-1v4c0 1-1 1-1 2v-1-1c0-1-1-1-1-2 0 0 1 0 1-1z" class="d"></path><path d="M296 298c3-1 5-3 7-5v1c-1 2-2 2-3 4 1 0 2 0 3-1h1l-1 2c-2 1-4 2-7 2h0c-1-1-1-1-2-1 1 0 1 0 1-1l1-1z" class="AE"></path><path d="M304 285h1v3c0 2-1 3-2 5h0c-2 2-4 4-7 5-2 0-4 0-6-1v-1l2-1h-1v-1l1-1c2 1 4 1 5 0s3-3 3-4h1 0l1-2h0l1-2h1z" class="r"></path><path d="M303 285h1c0 1 0 2-1 4l-1-1v-1l1-2z" class="f"></path><path d="M302 287v1l1 1c-2 3-4 5-8 6-1 1-2 1-3 0h-1v-1l1-1c2 1 4 1 5 0s3-3 3-4h1 0l1-2h0z" class="a"></path><path d="M291 265l2 1 5 3v1h0 0c3 1 5 4 8 6h0l2 3v2l-1 1c0 1-1 1-1 1h-1c1 2 1 3 0 5v-3h-1-1l-1 2h0l-1 2h0-1-2l-1-2v-1h0v-1c-1-2-3-4-6-5-2-1-6-1-7 0-1 0-2 1-3 1-1 1-2 2-2 4l-1 1v1l1 1h-1c-1 2-1 2 0 4 0 0 0 1 1 1l-1 1c-1 0-1 1-2 1l-1-2-1 1-1-2-2 1h0c-1-3-1-6-1-9 1-3 1-7 3-9 1-2 2-3 3-4l-1-1c3-2 6-4 9-5 2 0 5 1 7 0z" class="i"></path><path d="M282 276h3 4c-3 0-5 1-8 3l1 1c-3 2-4 4-6 6v-1-1c1-2 1-3 1-4 2-2 3-3 5-4z" class="a"></path><path d="M282 276h1c2-1 7-2 9-1 1 1 3 1 4 1l4 1h0l3 3s1 1 2 1v2c1 2 1 3 0 5v-3h-1-1c-1-3-1-4-4-6-2-1-5-3-8-3h0-2-4-3z" class="U"></path><path d="M282 280c1-1 2-2 4-2h0c2-1 4 0 6 1h0c1 0 2-1 3 0l2 3c2 1 3 5 5 5l-1 2h0-1-2l-1-2v-1h0v-1c-1-2-3-4-6-5-2-1-6-1-7 0-1 0-2 1-3 1-1 1-2 2-2 4l-1 1v1l1 1h-1c-1 2-1 2 0 4 0 0 0 1 1 1l-1 1c-1 0-1 1-2 1l-1-2h0v-4c1-1 1-2 1-3 2-2 3-4 6-6z" class="T"></path><path d="M297 282c2 1 3 5 5 5l-1 2h0-1c0-3-3-4-3-7z" class="AD"></path><path d="M280 275h4c2-1 8-1 10 0 0 0 1 0 2 1-1 0-3 0-4-1-2-1-7 0-9 1h-1c-2 1-3 2-5 4 0 1 0 2-1 4v1 1c0 1 0 2-1 3v4h0l-1 1-1-2-2 1h0c-1-3-1-6-1-9l1 1h0c1-1 3-3 3-4 1-2 2-3 4-5l2-1z" class="c"></path><path d="M273 292c-1-2-1-4-1-7h2v-2c1-2 2-3 3-3 0 1 0 2-1 4v1 1c0 1 0 2-1 3v4h0l-1 1-1-2z" class="U"></path><path d="M291 265l2 1 5 3v1h0 0c3 1 5 4 8 6h0l2 3v2l-1 1c0 1-1 1-1 1h-1v-2c-1 0-2-1-2-1l-3-3h0l-4-1c-1-1-2-1-2-1-2-1-8-1-10 0h-4l-2 1c-2 2-3 3-4 5 0 1-2 3-3 4h0l-1-1c1-3 1-7 3-9 1-2 2-3 3-4l-1-1c3-2 6-4 9-5 2 0 5 1 7 0z" class="AA"></path><path d="M305 281c-1-2-3-4-3-5h1 3 0l2 3v2l-1 1c0 1-1 1-1 1h-1v-2z" class="R"></path><path d="M305 281c-1-2-3-4-3-5h1c2 1 2 3 3 4s1 1 1 2-1 1-1 1h-1v-2z" class="q"></path><path d="M286 269c3 0 5 0 8 2 2 1 6 2 7 4v1c-3-1-4-2-6-2-5-1-10-2-15 1l-2 1v-1l6-4-3-1c2 0 3-1 5-1z" class="AI"></path><path d="M286 269c3 0 5 0 8 2h-10l-3-1c2 0 3-1 5-1z" class="R"></path><path d="M291 265l2 1 5 3v1h0 0c-2 1-3-1-5-1-1 0-1 0-2-1-2 0-4 0-5 1-2 0-3 1-5 1l3 1-6 4v1c-2 2-3 3-4 5 0 1-2 3-3 4h0l-1-1c1-3 1-7 3-9 1-2 2-3 3-4l-1-1c3-2 6-4 9-5 2 0 5 1 7 0z" class="I"></path><path d="M274 281h-1v-3h0c2-4 5-6 8-8l3 1-6 4v1c-2 2-3 3-4 5z" class="x"></path><path d="M291 265l2 1 5 3v1h0c-2-1-5-2-7-2-6-1-11 0-15 3l-1-1c3-2 6-4 9-5 2 0 5 1 7 0z" class="v"></path><path d="M426 120c3 0 5-1 8 0 4 0 8 0 12 1 1 0 2 0 3 1l8 1c1 1 2 1 3 2l7 10v-1c7 9 12 18 16 29l-1 1 1 2 1 2v3c1 3 2 5 2 8 0 1 1 3 2 4h1v-1h1v2c1 1 1 1 1 3h-1c-2-1-3-2-4-4-1 0-1-1-1-1-2-3-4-5-5-7l-8-9c-1-2-3-4-5-6a30.44 30.44 0 0 0-8-8l-11-9c-3-2-7-4-10-6l-6-3c-1 0-2-1-3-1v-3l1 1v-2c1-1 2-1 3-1-1-1-2-2-3-1h-1l-1-1c-1 0-2 1-3 1h-2-1l-3-3v-1-2c2-1 5-1 7-1z" class="g"></path><path d="M466 149c-2 0-3-1-4-2v-1h0c1 0 4 0 6 1v1c-1 0-1 1-2 1z" class="AE"></path><path d="M468 148c2 0 5 5 7 6l1 2-1 1v-1c-2-3-5-6-9-7 1 0 1-1 2-1z" class="e"></path><path d="M480 175v-1h0c0 1 1 1 1 2l3 4c0-2-1-4-2-6v-1l3 6h1c0 1 1 3 2 4h1v-1h1v2c1 1 1 1 1 3h-1c-2-1-3-2-4-4-1 0-1-1-1-1-2-3-4-5-5-7z" class="AB"></path><path d="M476 156c3 4 4 7 5 12l2-2 1 2v3c1 3 2 5 2 8h-1l-3-6c0-1-1-4-2-5-1-4-4-8-5-11l1-1z" class="b"></path><path d="M483 166l1 2v3h0c-2 0-2-2-3-3l2-2z" class="H"></path><path d="M433 128c4-1 8-1 12-1 2 1 3 2 6 1v1 1h-1c-2 0-3 1-5 1-1 1-3 0-4 0h-8-3v-2c1-1 2-1 3-1z" class="AJ"></path><path d="M464 142c-1-1-2-1-3-2-3 0-6-2-9-3l-8-2c3-2 6-3 10-2 2 0 3 1 5 2l6 6-1 1z" class="R"></path><path d="M456 127h0 1l12 15c0 1 1 2 2 3l4 8v1c-2-1-5-6-7-6v-1-1l-2-2c-1-1-2-1-2-1v-1l1-1-6-6v-1h0c-1-2-4-3-5-4h0 1 0l2 2h1 1 0c-1-1-2-2-4-3l1-1v-1z" class="b"></path><path d="M454 130h0 1 0l2 2h1 1 0c-1-1-2-2-4-3l1-1c5 4 8 9 11 14 1 2 3 4 4 5v1h-1 0c-1-3-3-5-5-7l-6-6v-1h0c-1-2-4-3-5-4z" class="AQ"></path><path d="M460 125l7 10v-1c7 9 12 18 16 29l-1 1 1 2-2 2c-1-5-2-8-5-12l-1-2v-1l-4-8c-1-1-2-2-2-3l-12-15s0-1-1-1l4-1z" class="r"></path><path d="M467 135v-1c7 9 12 18 16 29l-1 1c-2-3-3-7-4-10-3-7-7-13-11-19z" class="f"></path><path d="M426 120c3 0 5-1 8 0 4 0 8 0 12 1 1 0 2 0 3 1l8 1c1 1 2 1 3 2l-4 1c1 0 1 1 1 1h-1 0v1l-1 1c2 1 3 2 4 3h0-1-1l-2-2h0-1 0c-1-1-2-1-3-1v-1c-3 1-4 0-6-1-4 0-8 0-12 1-1-1-2-2-3-1h-1l-1-1c-1 0-2 1-3 1h-2-1l-3-3v-1-2c2-1 5-1 7-1z" class="AB"></path><path d="M447 126h4v3-1l-4-2z" class="n"></path><path d="M449 122l8 1c1 1 2 1 3 2l-4 1c1 0 1 1 1 1h-1 0c-2 0-6-4-7-5z" class="O"></path><path d="M433 123c5 0 10 1 14 3l4 2c-3 1-4 0-6-1-4 0-8 0-12 1-1-1-2-2-3-1h-1l-1-1c2-1 4-1 5-2v-1z" class="o"></path><path d="M426 120c3 0 5-1 8 0-2 0-7 0-9 1v1l3 1h2 3v1c-1 1-3 1-5 2-1 0-2 1-3 1h-2-1l-3-3v-1-2c2-1 5-1 7-1z" class="r"></path><path d="M419 123c2 0 3 1 5 1l1 3h-2-1l-3-3v-1z" class="I"></path><path d="M430 123h3v1c-1 1-3 1-5 2-1 0-2 1-3 1l-1-3c2-1 4-1 6-1z" class="P"></path><path d="M257 187c1 0 2-1 3-1l1 1v1l1-1c0 2-2 4-3 5l1 1c-1 2-3 3-3 6h1v2c0 2 0 4 1 6v2h-1l-1 1c-1 3-2 6-4 9l-2 3c1 1 1 1 1 2 0 0 0 1-1 1h0l2 2c-2 3-3 5-4 7l1 1c1-2 2-5 4-6l1 1 1 1 1 2c-1 1-2 1-3 3l-1 2h0v3h-1c-1 2-1 2-1 4v2c1 2 2 4 2 6 1 2 1 4 1 6h1l-1 5 1 2-1 2-1-1v1c-1 0-2-1-3-1l-1-1-2 1c-1 0-1 1-2 2v1c0-1 0-2-1-3h0v-1-1c-2 1-2 4-2 6 0 3 1 6 1 9v17c0 4-1 8 0 11h1c1 1 2 4 3 5 0 0 3 1 4 1 1 1 1 1 2 1h0l-1 2c0 1 1 1 1 2h0 1c-1 1-2 1-3 2h-1 0l-2 2h0v1c-1 0-1-1-2-1 0-3-1-5-2-7s-3-4-3-6v-1c-1-4-1-9-1-14v-29-67l-1-2h0c0-2-1-3-1-5 0-1 1-1 1-2 2 1 1 2 3 3h0s1 0 1 1h1c1 1 2 1 3 1l4-4h2c2-1 3-2 4-4z" class="Z"></path><path d="M243 308h1c1 1 2 4 3 5 0 0 3 1 4 1 1 1 1 1 2 1h0l-1 2c0 1 1 1 1 2h0 1c-1 1-2 1-3 2h-1 0l-2 2c-2-5-4-10-5-15z" class="p"></path><path d="M250 321v-1l2-3c0 1 1 1 1 2h0 1c-1 1-2 1-3 2h-1 0z" class="T"></path><path d="M242 208c1 2 1 3 1 5l1 1h0c0 2 0 3 1 5h-1c0-1 0-2-1-3v-1 4 1 2c1 1 0 1 0 2v1h1v4 11 4c1 1 1 1 0 2v1 1 4c0 3-1 7 0 10v3c-2 1-2 4-2 6 0 3 1 6 1 9-1-3-1-7-1-11v-23-8-18-6-6z" class="j"></path><path d="M257 187c1 0 2-1 3-1l1 1v1l1-1c0 2-2 4-3 5l1 1c-1 2-3 3-3 6h0c-1 0-2 1-3 2s-2 2-4 2c-2 1-5 1-7 0-1 0-1-2-2-2 0-1-1-2-1-2l-1-2h0c0-2-1-3-1-5 0-1 1-1 1-2 2 1 1 2 3 3h0s1 0 1 1h1c1 1 2 1 3 1l4-4h2c2-1 3-2 4-4z" class="i"></path><path d="M240 196v-1h1c2 2 4 3 7 3 1 0 3 0 5-1v1 1h0c-2 1-4 1-6 1-4 0-5-1-7-4z" class="K"></path><path d="M257 187c1 0 2-1 3-1l1 1v1h-1l-2 2c-2 2-5 5-7 6l-1 1c-1 0-5 0-6-1-1 0-2-1-3-2h0l1-1s1 0 1 1h1c1 1 2 1 3 1l4-4h2c2-1 3-2 4-4z" class="r"></path><path d="M259 192l1 1c-1 2-3 3-3 6h0c-1 0-2 1-3 2s-2 2-4 2c-2 1-5 1-7 0-1 0-1-2-2-2 0-1-1-2-1-2l-1-2 1-1c2 3 3 4 7 4 2 0 4 0 6-1h0v-1c3-1 5-3 6-6z" class="d"></path><path d="M257 199h1v2c0 2 0 4 1 6v2h-1l-1 1c-1 3-2 6-4 9l-1-1c-2 2-4 2-6 1h-1c-1-2-1-3-1-5h0l-1-1c0-2 0-3-1-5v-4c1 0 2 0 2 1 3 0 6-1 9-3h1l3-3h0z" class="g"></path><path d="M247 217h1 1l2-1c1 0 3-1 4-3h-1c-1 1-2 1-3 1h0c2-1 4-2 5-4h0 1c-1 3-2 6-4 9l-1-1v-1h-4-1z" class="c"></path><path d="M244 214c0 1 1 2 2 2 0 0 1 0 1 1h1 4v1c-2 2-4 2-6 1h-1c-1-2-1-3-1-5z" class="I"></path><path d="M244 262c-1-3 0-7 0-10v-4-1-1c1-1 1-1 0-2v-4-11-4h-1v-1c0-1 1-1 0-2v-2-1-4 1c1 1 1 2 1 3h1 1c2 1 4 1 6-1l1 1-2 3c1 1 1 1 1 2 0 0 0 1-1 1h0l2 2c-2 3-3 5-4 7l1 1c1-2 2-5 4-6l1 1 1 1 1 2c-1 1-2 1-3 3l-1 2h0v3h-1c-1 2-1 2-1 4v2c1 2 2 4 2 6 1 2 1 4 1 6h1l-1 5 1 2-1 2-1-1v1c-1 0-2-1-3-1l-1-1-2 1c-1 0-1 1-2 2v1c0-1 0-2-1-3h0v-1-1-3z" class="O"></path><path d="M247 257h0c-1-1-1-2 0-3 0 0 0-2 1-2v3 1l-1 1z" class="g"></path><path d="M247 240c0 1 1 0 2 1l-1 3v2l-1 1v-1c1-2 0-3-2-5h1 1v-1z" class="G"></path><path d="M247 247l1-1 1 6 1 2-1-1h0l-1 2v-3c0-2-1-4-1-5z" class="H"></path><path d="M248 232l-1-1 1-1v-1-2c1 0 1-1 1-1l-1-1v-1c1 0 2 1 3 0v1h0c-1 3-2 4-3 7z" class="h"></path><path d="M251 225l2 2c-2 3-3 5-4 7l-1 1h0v-3h0c1-3 2-4 3-7z" class="U"></path><path d="M250 235c1-2 2-5 4-6l1 1c-2 2-3 4-5 6-1 1-1 3-1 5-1-1-2 0-2-1v-1-1l3-3z" class="H"></path><path d="M255 230l1 1 1 2c-1 1-2 1-3 3-1 0-1 0-2 1l-2-1c2-2 3-4 5-6z" class="AB"></path><path d="M252 237c1-1 1-1 2-1l-1 2h0v3h-1c-1 2-1 2-1 4v2-1h-1c0 2 0 4-1 6l-1-6v-2l1-3c0-2 0-4 1-5l2 1z" class="K"></path><path d="M252 237c1-1 1-1 2-1l-1 2h0v3h-1v-2h-1l1-2z" class="h"></path><path d="M250 236l2 1-1 2c-1 1-1 3-2 4l-1 1 1-3c0-2 0-4 1-5z" class="v"></path><path d="M249 252c1-2 1-4 1-6h1v1c1 2 2 4 2 6 1 2 1 4 1 6h1l-1 5 1 2-1 2-1-1v1c-1 0-2-1-3-1l-1-1-2 1c-1 0-1 1-2 2v1c0-1 0-2-1-3h0v-1-1-3c1-1 2-1 3-1h0l-1-1v-3h1l1-1v-1l1-2h0l1 1-1-2z" class="O"></path><path d="M251 257c1 0 1 0 2-1v3 1c-2-2-3-2-5-2v-1h3z" class="H"></path><path d="M248 255l1-2h0l1 1c0 1 1 2 1 3h-3v1c2 0 3 0 5 2v1l-1 2h-1 0l-3-1-1-1h0l-1-1v-3h1l1-1v-1z" class="f"></path><path d="M248 258c2 0 3 0 5 2v1l-1 2h-1l-2-2c-1 0-1-1-2-3h1z" class="v"></path><path d="M253 261c0 1 1 2 1 3l1 2-1 2-1-1v1c-1 0-2-1-3-1l-1-1-2 1c-1 0-1 1-2 2v1c0-1 0-2-1-3h0v-1-1-3c1-1 2-1 3-1l1 1 3 1h0 1l1-2z" class="O"></path><path d="M244 262c1-1 2-1 3-1l1 1h-2v1c-1 1-1 2-2 3v-1-3z" class="K"></path><path d="M253 261c0 1 1 2 1 3l1 2-1 2-1-1c0-1 0-1-1-2-2 0-3-2-6-2v-1h2l3 1h0 1l1-2z" class="h"></path><defs><linearGradient id="AM" x1="242.18" y1="288.881" x2="303.647" y2="290.173" xlink:href="#B"><stop offset="0" stop-color="#f8dea4"></stop><stop offset="1" stop-color="#faf3da"></stop></linearGradient></defs><path fill="url(#AM)" d="M243 280c0-3-1-6-1-9 0-2 0-5 2-6v1 1h0c1 1 1 2 1 3v-1c1-1 1-2 2-2l2-1 1 1c1 0 2 1 3 1v-1l1 1 1-2c0 1 0 3 1 4l1 4 8 15 1-1 1 1c0 1 0 2 1 2l1 1h0l2 4 1-1-1-2 2-1 1 2 1-1 1 2c1 1 2 3 3 4l1 1 2-1 1 1 4 2h3 4l2-1c3 0 5-1 7-2h1l1 1h1 0 5s1-1 2 0h3l1-1c1 0 1 0 1 1h1l2 3v2c-1 2-2 4-3 5h-1l-1 1c-3 1-6 0-8-1h-1c0 1 0 1 1 1l1 1v4c-1 2 0 4 0 6 0 1-2 2-2 3-1-1-1-1-2-1h-1-1l-1-1h0v-1-3c-2-1-3-3-5-4h-1l-4 1c-2 1-4 3-5 5v1c-1-1-1-1-3-1h0-1c0 1-1 2-2 3h-3c-1 0-2-1-3-2v-1h-2c-1 0-1-1-2 0s-1 1-3 1l-1 1c-1-2-2-4-4-6h0 0l-2-1-1-1c-1 0-2-1-3-1s-2 1-3 2l-1-1h0c-1 0-1 0-2-1-1 0-4-1-4-1-1-1-2-4-3-5h-1c-1-3 0-7 0-11v-17z"></path><path d="M256 303c1 2 2 3 3 3l1 1c-1 0-2 1-3 0-1-2-1-2-1-4z" class="g"></path><path d="M245 305c1-1 1-2 3-3h0c1 2 1 3 0 4 1 2 2 2 3 3l-1 1c-2-1-3-3-5-5z" class="a"></path><path d="M256 303c0-1-1-3-1-4 1-1 2-1 2-2h1c1 3-2 6 2 9h-1c-1 0-2-1-3-3z" class="G"></path><path d="M245 307v-2c2 2 3 4 5 5h0c1 0 2 0 3 1h0c-1 1-3 1-4 1-2-1-4-3-4-5z" class="h"></path><path d="M244 308l1-1c0 2 2 4 4 5 1 0 3 0 4-1h2c1-1 3-1 4-1v1h0c1 0 2 1 2 1 1 1 1 1 3 1l-3 3-1-1c-1 0-2-1-3-1s-2 1-3 2l-1-1h0c-1 0-1 0-2-1-1 0-4-1-4-1-1-1-2-4-3-5z" class="a"></path><path d="M261 312c1 1 1 1 3 1l-3 3-1-1c-1 0-2-1-3-1l-1-1 1-1h4z" class="b"></path><path d="M300 306h2c1 2 4 3 6 4h0-1c0 1 0 1 1 1l1 1v4c-1 2 0 4 0 6 0 1-2 2-2 3-1-1-1-1-2-1h-1v-3l-1-2c0-1 0-1-1-2s-1-2-3-3c-1-1-1-2-2-3 3 1 5 5 8 5v-1c1-1 1-1 2 0v-1c0-1 0-2-1-3-3-2-5-4-9-5h3z" class="T"></path><path d="M306 322c-1-1-1-2-1-3h1c1 1 1 2 1 3v1l-1-1z" class="h"></path><path d="M303 319h1c1-1 1-1 2 0h-1c0 1 0 2 1 3l-2 2v-3l-1-2z" class="O"></path><path d="M273 292l1 2 1-1 1 2c1 1 2 3 3 4l1 1 2-1 1 1c-1 0-1 1-1 2l-1 1 2 1s1 1 2 1c-3 0-6 1-8 0-4 0-8 1-12 1-1 1-3 1-5 1l-1-1h1c1 0 3-1 4-1v-1-1l1 1 2-2h1c-1-1-1-1-1-2 0 1 1 2 3 2l1-1c0-1 1-1 1-2l-1-3 1-1-1-2 2-1z" class="U"></path><path d="M267 300c0 1 1 2 3 2l1-1c1 1 2 1 2 2v1c-3 1-6 1-9 1v-1-1l1 1 2-2h1c-1-1-1-1-1-2z" class="H"></path><path d="M272 295c2 4 5 7 9 8l2 1h-8l-3-5-1-3 1-1z" class="h"></path><path d="M273 292l1 2 1-1 1 2c1 1 2 3 3 4l1 1 2-1 1 1c-1 0-1 1-1 2l-1 1c-4-1-7-4-9-8l-1-2 2-1z" class="T"></path><path d="M274 294l1-1 1 2c1 1 2 3 3 4l1 1 2-1 1 1c-1 0-1 1-1 2h0l-3-1c-2-1-4-5-5-7z" class="O"></path><path d="M303 299h1l1 1h1 0 5s1-1 2 0h3l1-1c1 0 1 0 1 1h1l2 3v2c-1 2-2 4-3 5h-1l-1 1c-3 1-6 0-8-1h0c-2-1-5-2-6-4h-2-3-3l-9-1c-1 0-2-1-2-1l-2-1 1-1c0-1 0-2 1-2l4 2h3 4l2-1c3 0 5-1 7-2z" class="s"></path><path d="M303 299h1l1 1h1 0l-11 3h0-6l1-1h4l2-1c3 0 5-1 7-2z" class="K"></path><path d="M298 304h2c1-1 2-1 3-1 2 0 4-1 6-1h3c1-1 2-1 4 0h0v1c-1 1-2 1-4 1-1 1-2 1-2 0l-5 2h0-2-1-2c-1-1-2-1-3-1v-1h1z" class="AA"></path><path d="M305 306h-2 0v-2c2 0 4 0 6-1l1 1-5 2z" class="d"></path><path d="M309 303c2-1 4-1 7-1h0v1c-1 1-2 1-4 1-1 1-2 1-2 0l-1-1z" class="R"></path><path d="M283 300l4 2h3l-1 1h6 0c2 0 2 0 3 1h-1v1c1 0 2 0 3 1h-3-3l-9-1c-1 0-2-1-2-1l-2-1 1-1c0-1 0-2 1-2z" class="G"></path><path d="M283 300l4 2h3l-1 1c-2 0-4 0-7-1 0-1 0-2 1-2z" class="g"></path><path d="M295 303c2 0 2 0 3 1h-1v1c1 0 2 0 3 1h-3-3l-2-1c-1-1-1-1-1-2h4 0z" class="V"></path><path d="M318 300h1l2 3v2c-1 2-2 4-3 5h-1l-1 1c-3 1-6 0-8-1h0c-2-1-5-2-6-4h1 2 0l5-2c0 1 1 1 2 0 2 0 3 0 4-1v-1h1v-2h0 1z" class="r"></path><path d="M318 300h1l2 3v2c-1-1-1-3-2-4 0 1 0 1-1 1-2 3-4 4-7 4-3 1-6 1-8 0h2 0l5-2c0 1 1 1 2 0 2 0 3 0 4-1v-1h1v-2h0 1z" class="AH"></path><path d="M255 266c0 1 0 3 1 4l1 4 8 15 1-1 1 1c0 1 0 2 1 2l1 1h0l2 4 1 3c0 1-1 1-1 2l-1 1c-2 0-3-1-3-2-3-4-7-7-9-11-3-1-7-12-8-15-1-1-1-2-2-3-1 0-2 0-2-1h-1v-1c1-1 1-2 2-2l2-1 1 1c1 0 2 1 3 1v-1l1 1 1-2z" class="i"></path><path d="M255 266c0 1 0 3 1 4l1 4h0 0c-1-1-1-2-1-3-1 0-1-1-2-1 0 1 2 3 2 5l-3-7v-1l1 1 1-2z" class="K"></path><path d="M247 267l2-1 1 1c1 0 2 1 3 1l3 7c0 2 1 4 2 6 1 1 2 2 1 4 0-1-1-2-1-2h0c-1-3-3-8-5-9-1-1-1-1-2-1v1h-1c-1-1-1-2-2-3-1 0-2 0-2-1h-1v-1c1-1 1-2 2-2z" class="Z"></path><path d="M247 267h3l2 3h-5s0-1-1-1l-1 1v-1c1-1 1-2 2-2z" class="y"></path><path d="M250 274h1v-1c1 0 1 0 2 1 2 1 4 6 5 9h0l-1 1h0l1 1c0 2 2 3 3 5-1 0-2 0-3-1h0 0c-3-1-7-12-8-15z" class="H"></path><path d="M266 288l1 1c0 1 0 2 1 2l1 1h0l2 4 1 3c0 1-1 1-1 2l-1 1c-2 0-3-1-3-2-3-4-7-7-9-11h0 0c1 1 2 1 3 1 1 2 3 3 4 4 2 1 3 2 4 3-1-3-2-5-4-8h0l1-1z" class="I"></path><path d="M266 309c5-1 9-1 13-1h7c4 0 8 2 11 3 1 1 1 2 2 3 2 1 2 2 3 3s1 1 1 2l1 2v3h-1l-1-1h0v-1-3c-2-1-3-3-5-4h-1l-4 1c-2 1-4 3-5 5v1c-1-1-1-1-3-1h0-1c0 1-1 2-2 3h-3c-1 0-2-1-3-2v-1h-2c-1 0-1-1-2 0s-1 1-3 1l-1 1c-1-2-2-4-4-6h0 0l-2-1 3-3c-2 0-2 0-3-1 0 0-1-1-2-1h0v-1l7-1z" class="R"></path><path d="M276 311c0-1 0-1 1-2l2 1c2-1 3-1 5-1v2l1 1c-2-1-3 0-5-1h-1l-2 1-1-1z" class="x"></path><path d="M284 309l1 1c2 0 4 0 7 1 1 0 3 1 4 2 1 0 2 0 3 1 2 1 2 2 3 3s1 1 1 2l1 2v3h-1l-1-1h0v-1-3c-2-1-3-3-5-4-1 0-1-1-2-1-2-2-7-2-10-2l-1-1v-2z" class="t"></path><path d="M275 313v-2h1 0l1 1 2-1h1c2 1 3 0 5 1 3 0 8 0 10 2 1 0 1 1 2 1h-1l-4 1c-1 0-1-1-2-1-2 0-5-1-7-1-1-1-3-1-4-1-1 1-1 2-1 4 0 0-1 0-2-1 0-1 0-3-1-4v1z" class="P"></path><path d="M278 317c0-2 0-3 1-4 1 0 3 0 4 1 2 0 5 1 7 1 1 0 1 1 2 1-2 1-4 3-5 5v1c-1-1-1-1-3-1h0-1c0 1-1 2-2 3h-3c-1 0-2-1-3-2v-1h-2c-1 0-1-1-2 0s-1 1-3 1c1 0 2-1 2-2 1 0 1-2 1-3 2 1 1 3 3 4 1-2 2-3 2-5 1 1 2 1 2 1z" class="j"></path><path d="M279 321l1-2h1 0c0 1 1 1 1 2h1c-1 1-3 1-4 1v-1z" class="O"></path><path d="M283 315h1c1 1 2 2 4 3-1 1-2 2-3 2-2 0-3-1-5-3 1 0 2-1 3-2z" class="p"></path><path d="M276 316c1 1 2 1 2 1h0c0 1-1 3-2 4h1l1-1h1v1 1c1 0 3 0 4-1h0c0 1-1 2-2 3h-3c-1 0-2-1-3-2v-1h-2c-1 0-1-1-2 0s-1 1-3 1c1 0 2-1 2-2 1 0 1-2 1-3 2 1 1 3 3 4 1-2 2-3 2-5z" class="f"></path><path d="M266 309c5-1 9-1 13-1h7 0-7-4c0 1 0 2 1 3h-1v2-1c1 1 1 3 1 4 0 2-1 3-2 5-2-1-1-3-3-4 0 1 0 3-1 3 0 1-1 2-2 2l-1 1c-1-2-2-4-4-6h0 0l-2-1 3-3c-2 0-2 0-3-1 0 0-1-1-2-1h0v-1l7-1z" class="V"></path><path d="M268 310h5 1v2h0c-2-1-4 0-6 0v-2z" class="R"></path><path d="M275 313v-1c1 1 1 3 1 4 0 2-1 3-2 5-2-1-1-3-3-4h0l1-1c-1-1-2-1-3-2h2c1 1 2 1 3 2v1c1-2 1-3 1-4z" class="b"></path><path d="M259 310l7-1v2s1-1 2-1v2h-1v2c-1-1-2-1-3-1-2 0-2 0-3-1 0 0-1-1-2-1h0v-1z" class="s"></path><path d="M266 311s1-1 2-1v2h-1-4v-1h2 1z" class="AA"></path><path d="M264 313c1 0 2 0 3 1h2c1 1 2 1 3 2l-1 1h0c0 1 0 3-1 3 0 1-1 2-2 2l-1 1c-1-2-2-4-4-6h0 0l-2-1 3-3z" class="AB"></path><path d="M264 313c1 0 2 0 3 1h2c1 1 2 1 3 2l-1 1v-1h-2c-1-1-1-1-2-1v2c-1-1-1-1-1-2l-2 1s-1 0-1 1l-2-1 3-3z" class="n"></path><path d="M264 316l2-1c0 1 0 1 1 2v1c0 1 0 2 1 3 1 0 1 0 2-1 0 1-1 2-2 2l-1 1c-1-2-2-4-4-6h0 0c0-1 1-1 1-1z" class="h"></path><path d="M264 316l2-1c0 1 0 1 1 2v1l-1 1c-1-1-1-2-2-3z" class="H"></path><path d="M313 175l4 2 1 1c3 5 4 9 3 15 0 0 0 1-1 1l-1-1c-1 0-2 1-2 2h-3-1l-1 1v1c2 1 3 2 5 4 0 1 1 2 1 3s1 3 1 4c0 2 0 3-1 5l-1 2v-2c-1 0-1-1-1-1l-2 3h0v1l-1 1-2 2h0 0c0 1-2 2-3 2-2 0-4 0-6 1 1 1 1 2 1 3l-1 3h-1l-1-1c0 1-1 2-2 3v1h0c-1 0-1 1-1 2v1 1c-1 1-1 2-1 3-1-1-1-2-1-2-1-2-2-3-3-4-1 0-2-1-3-1h0c-2-1-4-2-5-3h1l1-1v-1c-1-1-2 0-3-2-1 0-1-1-1-2h-1l-1-1-2 1h0-4l-2-1-2-1 1-1h1 1c-1-1-2-1-2-2h-1c-2-1-4-4-6-5l-2-3v1c-1 0-1-1-2-2l-1-1c-1-2-1-4-1-6v-2h-1c0-3 2-4 3-6 1-1 1-2 2-2 1-2 2-3 3-4 2-1 5-2 6-4h0 2c1 0 2 0 3-1s3 0 5-1h2l3-2 1 2h0c1 0 2 0 3 1 1 0 2 0 2 1 1-1 1-1 2-1l1 1h0c1 2 2 4 4 5l1-1h1 4v-1c1 1 3 1 4 0v-1-1c2-1 2-2 3-3v-1l-1-2h0 2v-2h0v-1z" class="AB"></path><path d="M287 181c1 0 2 0 3 1-1 2 0 4 1 7 1 1 2 2 3 2l-1 1c-2-1-4-3-5-4s-1-1-1-2c-1-1 0-3 0-4v-1z" class="I"></path><path d="M286 194c-2-1-4-3-4-5-1-1-1-3-1-3h1c1 4 5 8 9 9 3 2 7 2 10 4h-3s-1 0-1-1h-2c-3 0-8-2-9-4z" class="e"></path><path d="M294 191c3 2 7 3 11 3h0c1 1 2 2 3 2 1 1 3 1 4 2v1h-1-1c1 2 2 4 2 6v1c0-1 0-1-1-2 0 0-1-1-1-2-1-2-2-3-3-5-1 0-1 0-2-1-2-2-8-2-11-3l-1-1 1-1z" class="U"></path><path d="M283 181l3-2 1 2h0v1c-2 0-3 0-5 2v2h-1s0 2 1 3c0 2 2 4 4 5-3-1-5-3-6-5 0-1-2-2-2-3h-2-1c-1 0-1 0-2-1h0c1 0 5-2 5-1h2l3-3z" class="V"></path><path d="M280 189c1 2 3 4 6 5 1 2 6 4 9 4h2c0 1 1 1 1 1h3c2 0 3 1 4 2v2h-1l-2-1c-1 0-1 0-2-1-2 0-4 0-6-1-2 0-3-1-5-2h0c-2 0-2-1-3-2l-2-2-5-4 1-1z" class="z"></path><path d="M301 199c2 0 3 1 4 2v2h-1l-2-1c-1 0-1 0-2-1h1v-1l-1-1h-2 0 3z" class="x"></path><path d="M305 201h1 0l3 3 1-1v-1c0 1 1 2 1 2 0 2 0 3-1 4h0l-1 2h0c-1 1-1 1-2 1l-2 2-1-1c-1 0-1-1-1-1 0-2 0-2 1-3v-1-1h2v-1l-1-1s-1 0-1-1h1v-2z" class="R"></path><path d="M304 207l1-1 1 1c1 1 0 2 0 4h1l-2 2-1-1c-1 0-1-1-1-1 0-2 0-2 1-3v-1z" class="x"></path><path d="M304 212v-3h1c1 1 1 1 1 2h1l-2 2-1-1z" class="AI"></path><path d="M306 201h0l3 3 1-1v-1c0 1 1 2 1 2 0 2 0 3-1 4h0l-1 2c-3-1-1-3-2-5 0-2-1-3-1-4z" class="b"></path><path d="M312 197c2 1 3 2 5 4 0 1 1 2 1 3s1 3 1 4c0 2 0 3-1 5l-1 2v-2c-1 0-1-1-1-1l-2 3-2-1-2 2h-1v-2-1c-2 0-3 1-4 0l2-2c1 0 1 0 2-1h0l1-2h0c1-1 1-2 1-4 1 1 1 1 1 2v-1c0-2-1-4-2-6h1 1v-1-1z" class="V"></path><path d="M317 201c0 1 1 2 1 3l-2 2v-1c-1-2 0-2 1-4z" class="k"></path><path d="M312 205c0-2-1-4-2-6h1 1c2 2 3 4 3 7v2c-1-1-1-3-2-4l-1 1z" class="i"></path><path d="M316 206l2-2c0 1 1 3 1 4 0 2 0 3-1 5l-1 2v-2c-1 0-1-1-1-1l1-4c0-1-1-1-1-2z" class="AI"></path><path d="M316 206l2-2c0 1 1 3 1 4 0 2 0 3-1 5v-2c0-2 0-2-1-3 0-1-1-1-1-2z" class="R"></path><path d="M312 205l1-1c1 1 1 3 2 4-1 3-2 4-3 6l-2 2h-1v-2-1c-2 0-3 1-4 0l2-2c1 0 1 0 2-1h0l1-2h0c1-1 1-2 1-4 1 1 1 1 1 2v-1z" class="O"></path><path d="M311 204c1 1 1 1 1 2v5c-1-1-1-2-2-3h0c1-1 1-2 1-4z" class="k"></path><path d="M310 208c1 1 1 2 2 3-1 1-2 2-3 2-2 0-3 1-4 0l2-2c1 0 1 0 2-1h0l1-2z" class="t"></path><path d="M292 183c1-1 1-1 2-1l1 1h0c1 2 2 4 4 5l1-1h1 4v-1c1 1 3 1 4 0 2-1 3-1 5-2-1 1-1 3-2 4l2 1-3 2c-1 1-3 2-6 3-4 0-8-1-11-3-1 0-2-1-3-2-1-3-2-5-1-7 1 0 2 0 2 1z" class="AQ"></path><path d="M292 183c1-1 1-1 2-1l1 1h0c1 2 2 4 4 5l2 1v1h-1c-3-1-6-5-8-7z" class="T"></path><path d="M314 184c-1 1-1 3-2 4-4 1-7 2-11 2h0v-1l-2-1 1-1h1 4v-1c1 1 3 1 4 0 2-1 3-1 5-2z" class="G"></path><path d="M300 187h1 4l1 1c-2 1-3 1-5 1l-2-1 1-1z" class="O"></path><path d="M313 175l4 2 1 1c3 5 4 9 3 15 0 0 0 1-1 1l-1-1c-1 0-2 1-2 2h-3-1l-1 1v1 1c-1-1-3-1-4-2-1 0-2-1-3-2h0c3-1 5-2 6-3l3-2-2-1c1-1 1-3 2-4-2 1-3 1-5 2v-1-1c2-1 2-2 3-3v-1l-1-2h0 2v-2h0v-1z" class="AA"></path><path d="M318 178c3 5 4 9 3 15 0 0 0 1-1 1l-1-1c-1 0-2 1-2 2h-3c0-1 1-1 2-1 1-2 2-3 3-5v-7-1h0-1c1-1 0-2 0-3z" class="H"></path><path d="M311 191h1s1 1 2 0c1 0 1-1 2-2 0-1 1-2 1-3h1v1 2c0 1-2 2-2 2v1l-3 3-1 1v1 1c-1-1-3-1-4-2-1 0-2-1-3-2h0c3-1 5-2 6-3z" class="d"></path><path d="M313 176c1 1 3 3 3 4 1 3-1 6-2 9l-2-1c1-1 1-3 2-4-2 1-3 1-5 2v-1-1c2-1 2-2 3-3v-1l-1-2h0 2v-2z" class="n"></path><path d="M311 178c1 0 2 0 2 1 1 2 1 3 1 5h0c-2 1-3 1-5 2v-1-1c2-1 2-2 3-3v-1l-1-2z" class="Z"></path><path d="M289 198h0c2 1 3 2 5 2 2 1 4 1 6 1 1 1 1 1 2 1l2 1c0 1 1 1 1 1l1 1v1h-2v1 1c-1 1-1 1-1 3 0 0 0 1 1 1l1 1c1 1 2 0 4 0v1 2h1l2-2 2 1h0v1l-1 1-2 2h0 0c0 1-2 2-3 2-2 0-4 0-6 1-1-1-1-2-3-3-1 0-2-1-3-1l-3-1c-1-1-2-1-3-2l-3-3c0-1-1-3-1-4 0-2 1-5 2-7l1-2v-1z" class="V"></path><path d="M294 215c1 0 2 1 3 1 1-1 1-1 1-2h1l2 2c-1 0-1 1-1 1h-2c0-1-1-1-2 0v1l-3-1c1-1 1-1 1-2z" class="AE"></path><path d="M287 212c1-1 2-1 3-1h1l1 2c0 1 0 2 1 2h1c0 1 0 1-1 2-1-1-2-1-3-2l-3-3z" class="e"></path><path d="M309 216c-2 0-3 0-5-1-1 0-4-2-5-4 0 0-1-1 0-2s3-1 4-2l1 1c-1 1-1 1-1 3 0 0 0 1 1 1l1 1c1 1 2 0 4 0v1 2z" class="h"></path><path d="M312 214l2 1h0v1l-1 1-2 2h0 0c0 1-2 2-3 2-2 0-4 0-6 1-1-1-1-2-3-3-1 0-2-1-3-1v-1c1-1 2-1 2 0h2s0-1 1-1l3 1h3c1-1 1-1 2-1h1l2-2z" class="d"></path><path d="M303 219h1 1c1 0 2 0 3-1h1c1-1 2-1 4-1l-2 2h0c-3 1-5 1-8 0z" class="R"></path><path d="M299 219l-1-1h1c1 0 2 1 4 1 3 1 5 1 8 0h0c0 1-2 2-3 2-2 0-4 0-6 1-1-1-1-2-3-3z" class="u"></path><path d="M302 202l2 1c0 1 1 1 1 1v1h-1c-2 0-5 2-6 3s-1 1-1 2-1 2 0 4h-1s-1 0-2-1h-2l-1-2h-1c1-3 5-6 7-7l1-1h2c1 0 2-1 2-1z" class="g"></path><path d="M290 211c1-3 5-6 7-7l1 1c0 1 0 1-1 2l-1 1v1 2s-1 1-2 1-2-1-3-1h-1z" class="AB"></path><path d="M289 198h0c2 1 3 2 5 2 2 1 4 1 6 1 1 1 1 1 2 1 0 0-1 1-2 1h-2l-1 1c-2 1-6 4-7 7-1 0-2 0-3 1 0-1-1-3-1-4 0-2 1-5 2-7l1-2v-1z" class="e"></path><path d="M291 207c0-3 0-4 2-5h4c-1 2-4 3-6 5h0z" class="H"></path><path d="M288 201c1 1 1 1 2 0v1c0 1-1 2-2 4 0 1-1 2-1 3l1 1c1-1 1-3 3-3h0c2-2 5-3 6-5l1 1-1 1c-2 1-6 4-7 7-1 0-2 0-3 1 0-1-1-3-1-4 0-2 1-5 2-7z" class="I"></path><path d="M286 196c1 1 1 2 3 2v1l-1 2c-1 2-2 5-2 7 0 1 1 3 1 4l3 3c1 1 2 1 3 2l3 1c1 0 2 1 3 1 2 1 2 2 3 3s1 2 1 3l-1 3h-1l-1-1c0 1-1 2-2 3v1h0c-1 0-1 1-1 2v1 1c-1 1-1 2-1 3-1-1-1-2-1-2-1-2-2-3-3-4-1 0-2-1-3-1h0c-2-1-4-2-5-3h1l1-1v-1c-1-1-2 0-3-2-1 0-1-1-1-2h-1l-1-1c-1-1-1-2-1-3-1-1-1-2-1-3v-3h0v-1h0l1-3c0-2 0-2 1-4 2-3 3-6 6-8z" class="k"></path><path d="M292 223h3l3 3c0 1 1 2-1 3l-1 1-1-1 1-1 1-1h0-2-1v-1h1v-1c-1-1-2-1-3-2z" class="P"></path><path d="M285 224h1l1-1 1 1h2 0 1v3c1 1 0 1 1 2 1-1 1-1 2-1h0v2c-1 1-1 2-2 2s-2-1-3-1h0c-2-1-4-2-5-3h1l1-1v-1c-1-1-2 0-3-2h2z" class="x"></path><path d="M285 224h1l1-1 1 1h2 0 1v3h-4c-1 1-1 1-2 1l1-1v-1c-1-1-2 0-3-2h2z" class="d"></path><path d="M299 219c2 1 2 2 3 3s1 2 1 3l-1 3h-1l-1-1c0 1-1 2-2 3v1h0c-1 0-1 1-1 2v1 1c-1 1-1 2-1 3-1-1-1-2-1-2-1-2-2-3-3-4 1 0 1-1 2-2v-2h2l-1 1 1 1 1-1c2-1 1-2 1-3 1-1 1-1 1-2 0 0 0-2 1-2l1-1-2-2z" class="e"></path><path d="M297 233c-1 0-1-1-2-1v-1c1 0 2-1 3 0-1 0-1 1-1 2z" class="V"></path><path d="M300 227h0c0-1 0-2 1-3h1s0 1 1 1l-1 3h-1l-1-1z" class="R"></path><path d="M290 215c1 1 2 1 3 2l3 1c1 0 2 1 3 1l2 2-1 1c-1 0-1 2-1 2 0 1 0 1-1 2l-3-3h-3-1c1-2 2-2 3-3-1 0-2 0-3-1h0c-1-1-1-1-2-1 0-1 1-1 1-1v-1-1z" class="o"></path><path d="M296 219c1 1 1 1 1 2s0 1-1 2h0-1v-1-2c0-1 1-1 1-1z" class="n"></path><path d="M290 216c2 2 5 1 6 3 0 0-1 0-1 1h-1c-1 0-2 0-3-1h0c-1-1-1-1-2-1 0-1 1-1 1-1v-1z" class="r"></path><path d="M278 211h0l1-3c0-2 0-2 1-4v5 1l4 4-2 1c1 2 2 4 3 4 1 1 1 1 2 1l1 2h0v2l-1-1-1 1h-1-2c-1 0-1-1-1-2h-1l-1-1c-1-1-1-2-1-3-1-1-1-2-1-3v-3h0v-1z" class="o"></path><path d="M278 212l1 1c0 1 0 2 1 3l3 3c1 1 3 2 4 4l1-1h0v2l-1-1-1 1h-1-2c-1 0-1-1-1-2h-1l-1-1c-1-1-1-2-1-3-1-1-1-2-1-3v-3z" class="e"></path><path d="M282 222l1-1c1 1 1 2 2 3h-2c-1 0-1-1-1-2z" class="q"></path><path d="M279 218v-1c1 1 3 2 4 4l-1 1h-1l-1-1c-1-1-1-2-1-3z" class="AN"></path><path d="M286 196c1 1 1 2 3 2v1l-1 2c-1 2-2 5-2 7 0 1 1 3 1 4l3 3v1 1s-1 0-1 1c-1-1-1-1-2-1-2 0-2-1-3-3l-4-4v-1-5c2-3 3-6 6-8z" class="n"></path><path d="M271 183h2c1 0 2 0 3-1s3 0 5-1h2l-3 3h-2c0-1-4 1-5 1h0c1 1 1 1 2 1h1 2c0 1 2 2 2 3l-1 1 5 4 2 2c-3 2-4 5-6 8-1 2-1 2-1 4l-1 3h0v1h0v3c0 1 0 2 1 3 0 1 0 2 1 3l-2 1h0-4l-2-1-2-1 1-1h1 1c-1-1-2-1-2-2h-1c-2-1-4-4-6-5l-2-3v1c-1 0-1-1-2-2l-1-1c-1-2-1-4-1-6v-2h-1c0-3 2-4 3-6 1-1 1-2 2-2 1-2 2-3 3-4 2-1 5-2 6-4h0z" class="AC"></path><path d="M270 211h0 1c0 1 1 3 2 4h2 1s0-1-1-2h0c0-1 0-2-1-2v-1c1 1 2 2 2 4 1 1 1 2 0 3l-1-1c-1 1-1 1-2 3-1-1-2-1-2-2h-1v-2l1-1c-1-1-1-2-1-3z" class="H"></path><path d="M271 217l2-2c1 0 1 1 2 1-1 1-1 1-2 3-1-1-2-1-2-2z" class="O"></path><path d="M278 215c0 1 0 2 1 3 0 1 0 2 1 3l-2 1h0-4l-2-1-2-1 1-1h1 1c1-2 1-2 2-3l1 1h1l1-2z" class="G"></path><path d="M272 219c2 1 3 0 4 0s2 1 3 1c0 1 0 1-1 2h0-4l-2-1-2-1 1-1h1z" class="h"></path><path d="M284 194l2 2c-3 2-4 5-6 8-1 2-1 2-1 4l-1 3h0l-1-1c1-3 2-6 2-9 1-1 1-2 1-3 1-1 2-3 4-4z" class="e"></path><path d="M266 206c0-3 1-6 1-9l1 1 2-1-1 3v1 2 2c0 2 0 4 1 5v1c0 1 0 2 1 3h-1c-3-3-6-6-7-11h2c0 1 1 2 1 3z" class="P"></path><path d="M266 206c0-3 1-6 1-9l1 1 2-1-1 3c-1 3-1 6-1 8h-1s0-1-1-2z" class="y"></path><defs><linearGradient id="AN" x1="272.94" y1="186.629" x2="273.234" y2="197.041" xlink:href="#B"><stop offset="0" stop-color="#332c1d"></stop><stop offset="1" stop-color="#51442a"></stop></linearGradient></defs><path fill="url(#AN)" d="M276 186h2c0 1 2 2 2 3l-1 1c-2 0-4 1-6 2-1 1-2 4-3 5l-2 1-1-1 1-1c1-4 4-8 8-10z"></path><path d="M271 183h2c1 0 2 0 3-1s3 0 5-1h2l-3 3h-2c0-1-4 1-5 1h0c1 1 1 1 2 1h1c-4 2-7 6-8 10l-1 1c0 3-1 6-1 9 0-1-1-2-1-3h-2c1 5 4 8 7 11h1l-1 1v2c-2-1-4-4-6-5l-2-3v1c-1 0-1-1-2-2l-1-1c-1-2-1-4-1-6v-2h-1c0-3 2-4 3-6 1-1 1-2 2-2 1-2 2-3 3-4 2-1 5-2 6-4h0z" class="s"></path><path d="M263 203c0-1 1-3 1-4 1 1 1 2 1 4h-2z" class="I"></path><path d="M265 189v2h2c1-1 1-2 3-3h0c-2 3-4 5-5 8h-1c-1 0-2-1-2-2s1-2 2-3v-1h0l1-1z" class="K"></path><path d="M271 183h2c1 0 2 0 3-1s3 0 5-1h2l-3 3h-2c0-1-4 1-5 1l-3 3h0c-2 1-2 2-3 3h-2v-2l-1 1h0c-1 1-1 1-2 1 1-2 2-3 3-4 2-1 5-2 6-4h0z" class="v"></path><path d="M265 187c1 1 2 0 2 1s0 2-1 2l-1-1-1 1h0c-1 1-1 1-2 1 1-2 2-3 3-4z" class="AB"></path><path d="M264 190v1c-1 1-2 2-2 3s1 2 2 2h1c0 1-1 2-1 3s-1 3-1 4c1 5 4 8 7 11h1l-1 1v2c-2-1-4-4-6-5l-2-3v1c-1 0-1-1-2-2l-1-1c-1-2-1-4-1-6v-2h-1c0-3 2-4 3-6 1-1 1-2 2-2s1 0 2-1z" class="U"></path><path d="M260 208v-2h0l1-2c0 1 1 5 1 5v1c-1 0-1-1-2-2z" class="AE"></path><path d="M258 201c0-2 1-4 2-5 0 1 1 3 0 5h0c0 1 0 2 1 3l-1 2h0v2l-1-1c-1-2-1-4-1-6z" class="AH"></path><path d="M264 212c0-3-1-5-2-8v-4c-1-2-1-3 0-6 0 1 1 2 2 2h1c0 1-1 2-1 3s-1 3-1 4c1 5 4 8 7 11h1l-1 1v2c-2-1-4-4-6-5z" class="O"></path><path d="M259 207l1 1c1 1 1 2 2 2v-1l2 3c2 1 4 4 6 5h1c0 1 1 1 2 2h-1-1l-1 1 2 1 2 1h4 0l2-1 1 1h1c0 1 0 2 1 2 1 2 2 1 3 2v1l-1 1h-1c1 1 3 2 5 3h0c1 0 2 1 3 1 1 1 2 2 3 4 0 0 0 1 1 2 0-1 0-2 1-3v-1-1c0-1 0-2 1-2h0c0 1 0 1 1 2l1-1 2 1 1-1c2 0 2 1 4 2h0l1 2h2v2l1 2c1 0 1 0 2-1h2v1c-2 0-3 2-4 3l-2 4c-4 8-10 14-18 18h0c-2 1-5 0-7 0-3 1-6 3-9 5l1 1c-1 1-2 2-3 4-2 2-2 6-3 9 0 3 0 6 1 9h0l1 2-1 1-2-4h0l-1-1c-1 0-1-1-1-2l-1-1-1 1-8-15-1-4c-1-1-1-3-1-4l-1-2 1-5h-1c0-2 0-4-1-6 0-2-1-4-2-6v-2c0-2 0-2 1-4h1v-3h0l1-2c1-2 2-2 3-3l-1-2-1-1-1-1c-2 1-3 4-4 6l-1-1c1-2 2-4 4-7l-2-2h0c1 0 1-1 1-1 0-1 0-1-1-2l2-3c2-3 3-6 4-9l1-1h1v-2z" class="AC"></path><path d="M265 265c1 0 1-1 1-1 0 2 0 3-1 5-1 1-1 0-2 0l-2-2c0-1 0-2 1-3h0 3v1z" class="s"></path><path d="M262 264h3v1 1c-2 1-2 0-3 0v-2z" class="t"></path><path d="M283 264l1 1c2-1 4-1 6-2 0 1-1 1-1 2h-5c-3 1-6 3-9 5-4 3-5 7-6 11-1 0-1 0-1-1v-3c1-4 5-7 9-9 2-1 4-3 6-4z" class="G"></path><path d="M275 270l1 1c-1 1-2 2-3 4-2 2-2 6-3 9 0 3 0 6 1 9h0l1 2-1 1-2-4h0l-1-1c-1 0-1-1-1-2h0v-1c1-3 0-5 1-8 0 1 0 1 1 1 1-4 2-8 6-11z" class="p"></path><path d="M268 280c0 1 0 1 1 1v11h0l-1-1c-1 0-1-1-1-2h0v-1c1-3 0-5 1-8z" class="f"></path><path d="M277 268l-1-1 5-2c-2 0-4 1-5 0h1c1 0 1-1 2-1l1-1c1-1 1-1 2-1 0-1 1-1 1-1 1-1 2-1 3-2h1v-1c1-1 2-4 3-5 0-1 1-1 1-2h0 1l-1 3c1 0 1-1 2-1h2v2l-2 1 2 1 1 1c-1 0-2 1-3 2l2 1c-2 1-3 2-5 2h0c-2 1-4 1-6 2l-1-1c-2 1-4 3-6 4z" class="O"></path><path d="M291 256v2c0-1 0-1 2-1-1 1-2 1-3 2 0 1 0 1-1 2l-1-1 3-4z" class="AE"></path><path d="M291 254c1 0 1-1 2-1h2v2l-2 1v1c-2 0-2 0-2 1v-2-2z" class="AA"></path><path d="M293 257v-1l2 1 1 1c-1 0-2 1-3 2l-4 1h0c1-1 1-1 1-2 1-1 2-1 3-2z" class="H"></path><path d="M289 261h0l4-1 2 1c-2 1-3 2-5 2h0c-2 1-4 1-6 2l-1-1c2-1 3-2 5-4l1 1zm-29-9v1h1 0l1-1c2-1 3-1 6 0l1 2c1 1 1 1 1 2l-3-2h-1l1 2h0c0 1 0 1-1 1l-2 2h-1l-2 3c-2 1-2 3-3 5h0c0 1 0 2 1 3 1 4 3 8 4 12l3 6-1 1-8-15-1-4c-1-1-1-3-1-4l-1-2 1-5c0-1 1-3 1-4 1-1 2-2 4-3z" class="k"></path><path d="M261 258h1 0c1-1 2-1 4-1l-2 2h-1c-2 0-2 1-4 2l2-3z" class="AA"></path><path d="M260 257v1c1-1 1-1 2-1l-1 1h0l-2 3c-1 1-2 3-2 4h-1c0-1 0-3 1-5 0-1 1-2 3-3z" class="x"></path><path d="M260 252v1h1 0l1-1c2-1 3-1 6 0l1 2c1 1 1 1 1 2l-3-2h-1l1 2h0c0 1 0 1-1 1-2 0-3 0-4 1h0-1 0l1-1c-1 0-1 0-2 1v-1c-2 1-3 2-3 3-1 2-1 4-1 5v5c-1-1-1-3-1-4l-1-2 1-5c0-1 1-3 1-4 1-1 2-2 4-3z" class="s"></path><path d="M260 257l-1-1c1-1 2-2 4-3 2 0 3 0 4 1h-1l1 2h0c0 1 0 1-1 1-2 0-3 0-4 1h0-1 0l1-1c-1 0-1 0-2 1v-1h0z" class="i"></path><path d="M260 257c2-1 4-2 7-1h0c0 1 0 1-1 1-2 0-3 0-4 1h0-1 0l1-1c-1 0-1 0-2 1v-1h0z" class="AI"></path><path d="M283 228h1c1 1 3 2 5 3h0c1 0 2 1 3 1 1 1 2 2 3 4 0 0 0 1 1 2 0 1 1 2 1 4v1 1c-2 0-2 1-2 2s-1 3-1 3c0 1-1 2-1 4-1 0-1 1-2 1l1-3c0-1 1-3 1-4v-1h-1c-1 2-2 4-3 5 0-2 0-4 2-6l1-1v-2l-1 1c-1 0-2-1-2-2l-3-3 1-1h-2-2v1h-2l-1 1-1-2h0c0-1-1-1-1-2h1l1-2-1-1 2-1h0c1-1 1-1 2-3z" class="s"></path><path d="M295 236s0 1 1 2c0 1 1 2 1 4v1 1c-3-2-2-4-4-6v-1h2v-1z" class="c"></path><path d="M284 235c2 0 4 0 6 2v1h-1l-2-1h-2-2v1h-2l-1 1-1-2 2-1c1-1 2-1 3-1z" class="n"></path><path d="M279 237l2-1 1 1h0 3-2v1h-2l-1 1-1-2z" class="j"></path><path d="M289 231c1 0 2 1 3 1 1 1 2 2 3 4v1h-2-1c0-2-2-2-4-2v-1l-1-1h3l-1-2z" class="n"></path><path d="M290 237l2 2h0c1 2 1 3 1 4v1c1 1 0 2 0 3h0v-1h-1c-1 2-2 4-3 5 0-2 0-4 2-6l1-1v-2l-1 1c-1 0-2-1-2-2l-3-3 1-1 2 1h1v-1z" class="T"></path><path d="M287 237l2 1c1 1 2 2 2 3l1 1-1 1c-1 0-2-1-2-2l-3-3 1-1z" class="t"></path><path d="M283 228h1c1 1 3 2 5 3h0l1 2h-3-3v2c-1 0-2 0-3 1l-2 1h0c0-1-1-1-1-2h1l1-2-1-1 2-1h0c1-1 1-1 2-3z" class="AB"></path><path d="M283 228h1c1 1 3 2 5 3h-8 0c1-1 1-1 2-3z" class="O"></path><path d="M279 235c1-1 3-2 5-2v2c-1 0-2 0-3 1l-2 1h0c0-1-1-1-1-2h1z" class="V"></path><path d="M289 241c0 1 1 2 2 2l1-1v2l-1 1c-2 2-2 4-2 6s-1 4-2 6h-3l-1 1c-1 1-3 0-4 0h-3c-2 1-4 2-4 4-1 1-3 2-3 2-1 0-2-1-3 0 0 0 0 1-1 1v-1h-3v-1l-1-1 2-3h1l2-2c1 0 1 0 1-1h0l-1-2h1l3 2c0-1 0-1-1-2l-1-2c1 1 3 2 4 3h4c1 0 1 0 1-1v-1h4c2-2 4-3 6-6h1c1 0 0-3 0-4s0-2 1-2z" class="P"></path><path d="M264 259l2 1c-1 2-1 2-2 3h-2l-1-1 2-3h1z" class="r"></path><path d="M287 247h1c-1 2-1 3-2 4 0 1-1 1-1 2s-1 2-2 2c-1 1-2 1-3 1h0c-2 1-6 0-8-1h4c1 0 1 0 1-1v-1h4c2-2 4-3 6-6z" class="AM"></path><path d="M268 252c1 1 3 2 4 3 2 1 6 2 8 1h0c-2 2-5 0-7 2h-1v1l-1 1c-1 1-3 1-4 1 0 0 0-1-1-1h0l-2-1 2-2c1 0 1 0 1-1h0l-1-2h1l3 2c0-1 0-1-1-2l-1-2z" class="k"></path><path d="M266 260c1-1 2-1 3-1 1-1 1 0 2 0v1c-1 1-3 1-4 1 0 0 0-1-1-1z" class="AA"></path><path d="M267 254l3 2c1 1 1 1 1 2-2 0-2-1-4-2h0l-1-2h1z" class="O"></path><path d="M278 235c0 1 1 1 1 2h0l1 2 1-1h2v-1h2 2l-1 1 3 3c-1 0-1 1-1 2s1 4 0 4h-1c-2 3-4 4-6 6h-4c-2 0-3 0-5-1l-1-1c0-1-1-1 0-2v-2h0c0-2-1-3-2-3l1-2 1-2 1-2c1 0 1 1 2 1v-1l4-3z" class="p"></path><path d="M282 245c1-1 1-2 1-3v-1c1 1 1 2 1 3 0 2-2 3-3 4h-1 0-1c-2 0-3-1-5-2l1-2c1 0 1 0 1 1v-1c1 1 2 2 4 3 1-1 1-1 2-1v-1z" class="AN"></path><path d="M285 237h2l-1 1 3 3c-1 0-1 1-1 2s1 4 0 4h-1c-1-1-1-1-1-2s1-2 0-3c0-2-1-3-3-4v-1h2z" class="AA"></path><path d="M278 235c0 1 1 1 1 2h0l1 2 1-1c2 1 2 1 3 2h0c-1 1-1 1-2 1l-1 1c0 1 1 2 1 3v1c-1 0-1 0-2 1-2-1-3-2-4-3v1c0-1 0-1-1-1l-1 2v-1c-1-2-1-4 0-6v-1l4-3z" class="AB"></path><path d="M279 237h0l1 2c-1 1-1 3-2 5l2 2h2c-1 0-1 0-2 1-2-1-3-2-4-3h0c0-4 1-4 3-7z" class="O"></path><path d="M278 235c0 1 1 1 1 2-2 3-3 3-3 7h0v1c0-1 0-1-1-1l-1 2v-1c-1-2-1-4 0-6v-1l4-3z" class="AA"></path><path d="M275 244v-1l-1-1h0v-1c1 0 1-1 2-1v3 1h0v1c0-1 0-1-1-1z" class="d"></path><path d="M272 238c1 0 1 1 2 1-1 2-1 4 0 6v1c2 1 3 2 5 2h1l1 1h0 1 0l4-4c0 1 0 1 1 2-2 3-4 4-6 6h-4c-2 0-3 0-5-1l-1-1c0-1-1-1 0-2v-2h0c0-2-1-3-2-3l1-2 1-2 1-2z" class="AE"></path><path d="M274 251l1 1h3c1 0 2 0 3 1h-4c-2 0-3 0-5-1 1 0 1-1 2-1z" class="d"></path><path d="M271 247c2 2 4 3 7 3l3-1h1c-2 2-4 2-6 3h-1l-1-1c-1 0-1 1-2 1l-1-1c0-1-1-1 0-2v-2z" class="I"></path><path d="M271 249c1 0 2 2 3 2-1 0-1 1-2 1l-1-1c0-1-1-1 0-2z" class="AE"></path><path d="M272 238c1 0 1 1 2 1-1 2-1 4 0 6v1c2 1 3 2 5 2h1l1 1h0l-3 1c-3 0-5-1-7-3h0c0-2-1-3-2-3l1-2 1-2 1-2z" class="K"></path><path d="M271 240l1 1v2h-1s0-1-1-1l1-2z" class="g"></path><path d="M298 231h0c0 1 0 1 1 2l1-1 2 1 1-1c2 0 2 1 4 2h0l1 2h2v2l1 2c1 0 1 0 2-1h2v1c-2 0-3 2-4 3l-2 4c-4 8-10 14-18 18h0c-2 1-5 0-7 0h5c0-1 1-1 1-2h0c2 0 3-1 5-2l-2-1c1-1 2-2 3-2l-1-1-2-1 2-1v-2h-2c0-2 1-3 1-4 0 0 1-2 1-3s0-2 2-2v-1-1c0-2-1-3-1-4s0-2 1-3v-1-1c0-1 0-2 1-2z" class="AC"></path><path d="M310 236v2c0 2-1 3-2 5 0-2 1-5 0-7h0 2z" class="G"></path><path d="M296 258l8-6c-3 3-6 7-9 9l-2-1c1-1 2-2 3-2z" class="s"></path><path d="M298 231h0c0 1 0 1 1 2l1-1 2 1 1-1c2 0 2 1 4 2h0l-2 1h0l-2-1v2 3c-1 1 0 3 0 4 1 2 0 5-2 7l-2 3c-1 2-2 3-4 4l-2-1 2-1v-2h-2c0-2 1-3 1-4 0 0 1-2 1-3s0-2 2-2v-1-1c0-2-1-3-1-4s0-2 1-3v-1-1c0-1 0-2 1-2z" class="i"></path><path d="M298 231h0c0 1 0 1 1 2l1-1 2 1h-2c0 2 1 4 2 5 0 2 0 3-1 4h0c0-2-1-3-1-4-1-2-1-2-2-3 0-1 0-1-1-1v-1c0-1 0-2 1-2z" class="AE"></path><path d="M297 234c1 0 1 0 1 1 1 1 1 1 2 3 0 1 1 2 1 4v1h-1c-1-1-1-1-1-3-1 1-1 2-2 3v-1c0-2-1-3-1-4s0-2 1-3v-1z" class="x"></path><path d="M297 235c0 2 1 4 2 5-1 1-1 2-2 3v-1c0-2-1-3-1-4s0-2 1-3z" class="d"></path><defs><linearGradient id="AO" x1="295.408" y1="243.541" x2="299.264" y2="249.591" xlink:href="#B"><stop offset="0" stop-color="#98773d"></stop><stop offset="1" stop-color="#b28f59"></stop></linearGradient></defs><path fill="url(#AO)" d="M299 240c0 2 0 2 1 3h1v-1h0c0 3-1 5-2 8h0v3c-1 2-2 3-4 4l-2-1 2-1v-2h-2c0-2 1-3 1-4 0 0 1-2 1-3s0-2 2-2v-1c1-1 1-2 2-3z"></path><path d="M295 255c2-2 3-3 4-5v3c-1 2-2 3-4 4l-2-1 2-1z" class="o"></path><path d="M294 249h1v-2l2-1h1c0 1 0 2-1 3 0 1-2 2-2 4h-2c0-2 1-3 1-4z" class="R"></path><path d="M267 229l2-2v1l3 3v2h0c0 1-1 2-1 4h1v1l-1 2-1 2-1 2c1 0 2 1 2 3h0v2c-1 1 0 1 0 2l1 1c2 1 3 1 5 1v1c0 1 0 1-1 1h-4c-1-1-3-2-4-3-3-1-4-1-6 0l-1 1h0-1v-1c-2 1-3 2-4 3 0 1-1 3-1 4h-1c0-2 0-4-1-6 0-2-1-4-2-6v-2c0-2 0-2 1-4h1v-3h0l1-2c1-2 2-2 3-3l2-1 6-2h0l2-1z" class="b"></path><path d="M255 238h0 1 2c-1 1-3 5-5 5v-5h0 2z" class="V"></path><path d="M267 229l2-2v1c1 3 2 6 0 8 0 1-2 1-2 2h-2v-2h-1c1-1 3-1 4-2h1v-3-1h-1c-1 1-2 0-3 0h0l2-1z" class="c"></path><path d="M253 241c0 2 0 3 1 4h1 0c1 0 1-1 2-1 1-2 2-3 4-4h1v-1h3c-1 1-3 2-5 3-1 1-2 2-3 4-1 1-2 1-2 2-1-1-1-2-3-4l-1 1c0-2 0-2 1-4h1z" class="AB"></path><path d="M257 233l2-1v2 1c3 0 6-1 9-1-1 1-3 1-4 2-2 1-4 1-6 2h-2-1 0-2l1-2c1-2 2-2 3-3z" class="k"></path><path d="M257 233l2-1v2c-1 0-2 1-3 2l-1 2h-2l1-2c1-2 2-2 3-3z" class="Z"></path><path d="M268 230h1v1 3h-1c-3 0-6 1-9 1v-1-2l6-2c1 0 2 1 3 0z" class="d"></path><path d="M265 230c1 0 2 1 3 0-2 2-4 1-5 3h0c-2 0-3 0-4 1v-2l6-2z" class="V"></path><path d="M271 237h1v1l-1 2-1 2-1 2c1 0 2 1 2 3h0v2c-1 1 0 1 0 2l1 1c2 1 3 1 5 1v1c0 1 0 1-1 1h-4c-1-1-3-2-4-3-3-1-4-1-6 0l-1 1h0-1v-1c-2 1-3 2-4 3 0 1-1 3-1 4h-1c0-2 0-4-1-6 0-2-1-4-2-6v-2l1-1c2 2 2 3 3 4 0 1-1 2-1 3l1 1c1-1 1-2 1-3 1-2 2-3 3-4 4-3 8-5 12-8z" class="AQ"></path><path d="M269 244c1 0 2 1 2 3h0v2c-1 1 0 1 0 2l1 1c2 1 3 1 5 1v1c0 1 0 1-1 1h-4c-1-1-3-2-4-3-3-1-4-1-6 0l-1 1h0-1v-1c-2 1-3 2-4 3v-1c1-1 2-2 2-3 4-3 8-4 11-7z" class="f"></path><path d="M263 250c1 0 1 0 2-1h1c1 0 1 0 2-1 1 0 0 0 0-1v-1c1 0 2 0 3 1h0v2c-1 1 0 1 0 2-3-1-5-1-8-1z" class="P"></path><path d="M263 250c3 0 5 0 8 1l1 1c2 1 3 1 5 1v1c0 1 0 1-1 1h-4c-1-1-3-2-4-3-3-1-4-1-6 0l-1 1h0-1v-1c1 0 2-1 3-2z" class="u"></path><path d="M259 207l1 1c1 1 1 2 2 2v-1l2 3c2 1 4 4 6 5h1c0 1 1 1 2 2h-1-1l-1 1 2 1 2 1h4 0l2-1 1 1h1c0 1 0 2 1 2 1 2 2 1 3 2v1l-1 1h-1-1c-1 2-1 2-2 3h0l-2 1 1 1-1 2h-1l-4 3v1c-1 0-1-1-2-1v-1h-1c0-2 1-3 1-4h0v-2l-3-3v-1l-2 2-2 1h0l-6 2-2 1-1-2-1-1-1-1c-2 1-3 4-4 6l-1-1c1-2 2-4 4-7l-2-2h0c1 0 1-1 1-1 0-1 0-1-1-2l2-3c2-3 3-6 4-9l1-1h1v-2z" class="n"></path><path d="M257 227v-2l1-1h0c0 1 1 1 1 2h1s0 1 1 2l-1 1c-1-1-1-2-3-2z" class="b"></path><path d="M257 227c2 0 2 1 3 2v2h-4l-1-1-1-1c1 0 2-1 3-2z" class="V"></path><path d="M259 213v1c0 2-1 3-2 5v4c-1 2-3 3-4 4l-2-2h0c1 0 1-1 1-1 2-2 3-4 4-5 1-2 1-4 3-6z" class="a"></path><path d="M259 207l1 1c1 1 1 2 2 2 0 1 1 2 1 3v1l-1 1v1l1 2-1 1c0 1 0 1-1 1v1h-2l1-5c0-2 1-3 0-4l-1-3v-2z" class="V"></path><path d="M259 221l1-5c0 1 0 2 2 3 0-1 0-1-1-2l1-1 1 2-1 1c0 1 0 1-1 1v1h-2z" class="I"></path><path d="M257 210l1-1h1l1 3-1 1c-2 2-2 4-3 6-1 1-2 3-4 5 0-1 0-1-1-2l2-3c2-3 3-6 4-9z" class="h"></path><path d="M260 226c-1-2-1-3-1-5h2c0 2 1 2 1 4 1 1 1 2 2 2h0v3h1 0l-6 2-2 1-1-2h4v-2l1-1c-1-1-1-2-1-2z" class="n"></path><path d="M260 226c-1-2-1-3-1-5h2c0 2 1 2 1 4l-1 1c1 2 2 2 2 4h0c-1 0-1-1-2-2h0c-1-1-1-2-1-2z" class="a"></path><path d="M262 219l1-1 1 2c2 2 4 3 6 4l2 2h0c-2 0-2-1-4-1h0c0 1 0 1 1 2h0l-2 2-2 1h-1v-3h0c-1 0-1-1-2-2 0-2-1-2-1-4v-1c1 0 1 0 1-1z" class="h"></path><path d="M262 219c1 1 1 2 1 3 0 2 1 3 1 5h0c-1 0-1-1-2-2 0-2-1-2-1-4v-1c1 0 1 0 1-1z" class="f"></path><path d="M264 220c2 2 4 3 6 4l2 2h0c-2 0-2-1-4-1h0c0 1 0 1 1 2h0l-2 2-3-9z" class="AC"></path><path d="M262 210v-1l2 3c2 1 4 4 6 5h1c0 1 1 1 2 2h-1-1l-1 1 2 1 2 1h2c0 1 0 2-1 2h-1-4c-2-1-4-2-6-4l-1-2-1-2v-1l1-1v-1c0-1-1-2-1-3z" class="d"></path><path d="M263 214v-1c3 3 5 5 8 6l-1 1 2 1c-4-1-7-3-9-7z" class="O"></path><path d="M262 210v-1l2 3c2 1 4 4 6 5h1c0 1 1 1 2 2h-1-1c-3-1-5-3-8-6 0-1-1-2-1-3z" class="d"></path><path d="M280 221l1 1h1c0 1 0 2 1 2 1 2 2 1 3 2v1l-1 1h-1-1c-1 2-1 2-2 3h0l-2 1 1 1-1 2h-1l-4 3v1c-1 0-1-1-2-1v-1h-1c0-2 1-3 1-4h0v-2l-3-3v-1h0c-1-1-1-1-1-2h0c2 0 2 1 4 1h0l-2-2h4 1c1 0 1-1 1-2h-2 4 0l2-1z" class="p"></path><path d="M269 227h2c1 1 1 2 1 4l-3-3v-1h0z" class="g"></path><path d="M272 233c1-1 2-2 4-3h1v1c-1 1-3 2-4 3l-1-1h0z" class="K"></path><path d="M272 233l1 1v2h0v2h1v1c-1 0-1-1-2-1v-1h-1c0-2 1-3 1-4z" class="h"></path><path d="M279 232l1 1-1 2h-1l-4 3h-1v-2c2-2 4-3 6-4z" class="v"></path><path d="M280 221l1 1h1c0 1 0 2 1 2 1 2 2 1 3 2v1l-1 1h-1-1-2 0c-1 0-3 0-4-1l-5-1-2-2h4 1c1 0 1-1 1-2h-2 4 0l2-1z" class="Z"></path><path d="M274 222h4 1c-1 1-1 2-2 3h-3v-1h1c1 0 1-1 1-2h-2z" class="V"></path><path d="M281 222h1c0 1 0 2 1 2 1 2 2 1 3 2v1l-1 1h-1-1-2v-2h1v-1c-1-1-1-2-1-3h0z" class="AP"></path><path d="M321 303l2 3s-1 2 0 3v1c-1 1 0 2-1 3h-2c0 1-1 1-1 1h-2c-1 2-1 21-1 25v3c-1 1-2 0-3 0l-1 1v2 1l2 1v1c-1 0-1 1-1 1l1 1h-2 0v1c-3 2-7 3-11 3h0c-1 2-2 3-3 5-1 3-2 9-2 12 0 2 1 3 1 5h0l-1 1v3l-1-2-1 1v2c2 0 3 0 6 1v1h-2l-1 1h-4 0c-1 0-3 0-4 1-1 0-1 1-2 1 0 1-1 2-1 3-1 0-1 1-3 1 1 1 1 3 1 4v1 1 1h-1v1l1 2v2h-1-1c-1-1-4-1-6-1h-3c-1-1-1-4-2-6l-3-18c-3-14-8-28-13-42v-1-2h-1c0-2-1-3-1-4h-1-1c-1-2-2-3-3-5h0l2-2h0 1c1-1 2-1 3-2h-1 0c0-1-1-1-1-2l1-2 1 1c1-1 2-2 3-2s2 1 3 1l1 1 2 1h0 0c2 2 3 4 4 6l1-1c2 0 2 0 3-1s1 0 2 0h2v1c1 1 2 2 3 2h3c1-1 2-2 2-3h1 0c2 0 2 0 3 1v-1c1-2 3-4 5-5l4-1h1c2 1 3 3 5 4v3 1h0l1 1h1 1c1 0 1 0 2 1 0-1 2-2 2-3 0-2-1-4 0-6v-4l-1-1c-1 0-1 0-1-1h1c2 1 5 2 8 1l1-1h1c1-1 2-3 3-5v-2z" class="O"></path><path d="M272 355c0-1 0-1 1-1v-1h0 1 0-1c0 1 0 2 1 3-1 1-1 2-2 3v-4z" class="c"></path><path d="M282 398h1l1 2v2h-1c0-1 0-1-1-1v-3z" class="V"></path><path d="M272 355c-1-3-1-4-1-6h1c0 1 0 1 1 2h1v2h-1 0v1c-1 0-1 0-1 1z" class="k"></path><path d="M278 370c0 2 1 3 1 5v2c0-2-2-3-3-5l1-2h1z" class="i"></path><path d="M279 375c1 0 1 1 1 2 1 0 1 1 1 2 0 2 0 3-1 5l-1-7v-2zm11-33l1-1c3 2 4 4 5 7h0 0c-3-1-4-4-6-6z" class="a"></path><path d="M274 356c0 2 1 4 1 6s0 4 1 5 1 2 1 3l-1 2c-1-5-3-9-4-13 1-1 1-2 2-3z" class="I"></path><path d="M274 356c-1-1-1-2-1-3h1c0 1 0 2 1 4l1 2 2 11h-1c0-1 0-2-1-3s-1-3-1-5-1-4-1-6z" class="H"></path><path d="M321 303l2 3s-1 2 0 3v1c-2 1-4 3-6 3h-1c1 0 1-1 2-1v-1l-1-1h1c1-1 2-3 3-5v-2z" class="R"></path><path d="M271 327l1-1v1 1h1 0c1-1 2-1 2-2h1c2 1 3 1 5 1h1 1l-1 1h-8c-1 2-1 4-3 4l-1-1h0-1v-1h1c1-1 1-2 1-3z" class="I"></path><path d="M265 327l2 2c1-1 2-2 4-2 0 1 0 2-1 3h-1v1c-1 1-2 1-2 1-1-1-1-1 0-1l-1-1-2 2c0 1-2 1-2 2-1 0-4 1-5 0h0v-1c3-1 6-4 8-6z" class="e"></path><path d="M281 379v-2h1l1 7v6c1 1 1 3 1 4v1 1 1h-1v1h-1c-1-1-1-3-1-4l-1-10c1-2 1-3 1-5h0z" class="Z"></path><path d="M281 379h0c0 3 1 6 1 9v2c0 1-1 1-1 1v3l-1-10c1-2 1-3 1-5z" class="T"></path><path d="M312 350v1c-3 2-7 3-11 3-1-1-7 0-9 0h-4c1 0 2 0 2-1v-1h-2 1 1 2c1-1 2-1 4-1h3c4 1 9 0 13-1z" class="d"></path><path d="M286 344h0c0-1 1-2 2-3 1 0 1 0 2 1 2 2 3 5 6 6h0l1 1c1 1 4 1 6 2h-4-3l-5-2c-2-1-3-1-5-2h2 1 1c-2-1-2-2-4-3z" class="K"></path><path d="M305 329l1 1h0c-1 1-1 2-2 3l4-1v2c-1 0-3 1-4 2l-3 1c-4 0-8-2-11-4h4c2 0 3 1 4 1l-1-1h4c0-1 1-1 1-2 1 0 2-1 3-2z" class="q"></path><path d="M305 329l1 1h0c-1 1-1 2-2 3-2 1-3 1-6 1l-1-1h4c0-1 1-1 1-2 1 0 2-1 3-2z" class="s"></path><path d="M284 321c2 0 2 0 3 1l1 1 1-1c1 0 1 1 1 2s1 2 2 3h0v1c-1 0 0 2-1 2 2 2 3 2 6 3l1 1c-1 0-2-1-4-1h-4c-1 0-2-3-3-4-2 0-3-1-5-1l1-1c1 0 1 0 2-1 0-2-1-3-1-5h0z" class="a"></path><path d="M287 329c-1-1-1-2-1-2v-3c1 0 1 0 2 1v1c1 2 2 3 3 4 2 2 3 2 6 3l1 1c-1 0-2-1-4-1h-4c-1 0-2-3-3-4z" class="I"></path><path d="M283 321h1 0 0c0 2 1 3 1 5-1 1-1 1-2 1h-1-1c-2 0-3 0-5-1h-1c0 1-1 1-2 2h0-1v-1-1l-1 1c-2 0-3 1-4 2l-2-2 2-1v-1c1-1 3-2 5-2h1 0c1 0 1-1 1-1h1c1 1 2 2 3 2h3c1-1 2-2 2-3z" class="R"></path><path d="M273 323h0l1 2-1 1c-1 0-1 0-1-1v-1l1-1z" class="AN"></path><path d="M267 325c1-1 3-2 5-2h1l-1 1v1h-1s-1 1-2 1h-2v-1z" class="AA"></path><path d="M276 354c2 1 2 1 3 3h0v2c0 1 1 2 2 2h1v-1 7 10h-1v2h0c0-1 0-2-1-2 0-1 0-2-1-2 0-2-1-3-1-5l-2-11h0v-1-4z" class="U"></path><path d="M278 363c-1-1-1-4-1-5h1l1 1c0 1 1 2 2 2l-1 1h-2v1z" class="a"></path><path d="M278 363v-1h2v9l-1-1c0-2-1-5-1-7z" class="i"></path><path d="M280 350h4c2 1 4 1 5 2h-1 2v1c0 1-1 1-2 1h-6l-1 1c0 1 0 3 1 5v1h-1c-1 0-2-1-2-2v-2h0c-1-2-1-2-3-3v4 1h0l-1-2c-1-2-1-3-1-4h0v-2h0l1-1h5z" class="Z"></path><path d="M274 351h0 3c0 1-1 1-1 2v1 4 1h0l-1-2c-1-2-1-3-1-4h0v-2z" class="V"></path><path d="M281 355h-1 0v-2c-1-1-1-1-1-2h0l1 1h4 4 2v1c0 1-1 1-2 1h-6l-1 1z" class="q"></path><path d="M281 355h-1 0v-2c-1-1-1-1-1-2h0l1 1c1 0 3 0 4 1v1h-2l-1 1z" class="y"></path><path d="M280 350l-4-1h0c-1 0-2 1-3 0h0l1-2c0-2 2-4 4-4 1 0 3-2 4-1 1 0 1 1 1 1 1 1 2 1 3 1 2 1 2 2 4 3h-1-1-2c2 1 3 1 5 2l5 2c-2 0-3 0-4 1h-2-1c-1-1-3-1-5-2h-4z" class="v"></path><path d="M284 350h2 0c-2-2-5-2-8-4h1c2 0 4 1 7 1 2 1 3 1 5 2l5 2c-2 0-3 0-4 1h-2-1c-1-1-3-1-5-2z" class="T"></path><path d="M291 349l5 2c-2 0-3 0-4 1h-2l-2-2c1 0 2-1 3-1z" class="V"></path><path d="M309 312l-1-1c-1 0-1 0-1-1h1c2 1 5 2 8 1l1-1 1 1v1c-1 0-1 1-2 1h1c-1 1-2 1-2 1 0 2 0 3-1 5 0 6-1 10-6 15h0v-2l-4 1c1-1 1-2 2-3h0l-1-1c0-1 1-2 1-3s0-1 1-1c0-1 2-2 2-3 0-2-1-4 0-6v-4z" class="d"></path><path d="M306 330v1h2v1l-4 1c1-1 1-2 2-3z" class="k"></path><path d="M310 318c1-1 2-1 3 0 0 1 0 2-1 3-1 0-1 0-1-1l-1-2z" class="AG"></path><path d="M311 320c0 1 0 1 1 1v3c0 2-2 4-3 5h0v-1c1-2 1-5 2-7v-1z" class="q"></path><path d="M311 320c0 1 0 1 1 1v3h-1v-3-1z" class="t"></path><path d="M309 312l-1-1c-1 0-1 0-1-1h1c2 1 5 2 8 1l1-1 1 1v1c-1 0-1 1-2 1s-3 0-4 1h0c1 1 1 2 1 4-1-1-2-1-3 0 0-3 0-3-1-6z" class="q"></path><path d="M309 312c1 3 1 3 1 6l1 2v1c-1 2-1 5-2 7-1 1-2 1-3 2l-1-1c0-1 1-2 1-3s0-1 1-1c0-1 2-2 2-3 0-2-1-4 0-6v-4z" class="AH"></path><path d="M296 315h1c2 1 3 3 5 4v3 1h0l1 1h1 1c1 0 1 0 2 1-1 0-1 0-1 1s-1 2-1 3c-1 1-2 2-3 2 0 1-1 1-1 2h-4c-3-1-4-1-6-3 1 0 0-2 1-2v-1h0c-1-1-2-2-2-3s0-2-1-2l-1 1-1-1v-1c1-2 3-4 5-5l4-1z" class="c"></path><path d="M292 324l-1-1v-2l2 2h0c1 1 1 1 2 1l2-1 2 1c-2 1-2 2-4 2-1-1-2-2-3-2z" class="V"></path><path d="M300 322v1h1v-2h1v1 1h0l-2 2v1h-5c2 0 2-1 4-2 0-1 1-2 1-2z" class="k"></path><path d="M292 324c1 0 2 1 3 2h5v2h-3 0c-3-1-4-2-5-4z" class="y"></path><path d="M296 315v1c-1 1-2 1-3 1-2 1-3 3-4 5l-1 1-1-1v-1c1-2 3-4 5-5l4-1z" class="n"></path><path d="M295 324l-2-3c1-1 1-2 2-2s2 1 3 1c0 2 0 2-1 3l-2 1z" class="AB"></path><path d="M297 315c2 1 3 3 5 4v3-1h-1v2h-1v-1c-1-2-2-3-3-5v-1-1z" class="AA"></path><path d="M291 330c1 0 0-2 1-2 0 1 1 1 2 2 1 0 2 0 3 1h1l2-1c1 1 1 1 2 1 0 1-1 1-1 2h-4c-3-1-4-1-6-3z" class="U"></path><path d="M302 323l1 1h1 1c1 0 1 0 2 1-1 0-1 0-1 1s-1 2-1 3c-1 1-2 2-3 2s-1 0-2-1h0c-2 0-2-1-3-2h3v-2-1l2-2z" class="P"></path><path d="M300 325c1 1 1 1 2 1-1 1-1 2-2 2v-2-1z" class="z"></path><path d="M302 323l1 1c0 1 0 2-1 2s-1 0-2-1l2-2z" class="Q"></path><path d="M305 324c1 0 1 0 2 1-1 0-1 0-1 1-1 1-2 1-3 2h0l2-4z" class="k"></path><path d="M303 328c1-1 2-1 3-2 0 1-1 2-1 3-1 1-2 2-3 2s-1 0-2-1h0c1 0 2-1 3-2z" class="I"></path><path d="M257 314c1 0 2 1 3 1l1 1 2 1h0 0c2 2 3 4 4 6l1-1c2 0 2 0 3-1s1 0 2 0h2v1h-1s0 1-1 1h0-1c-2 0-4 1-5 2v1l-2 1c-2 2-5 5-8 6v1h-1l-1 1v-1-2h-1c0-2-1-3-1-4h-1-1c-1-2-2-3-3-5h0l2-2h0 1c1-1 2-1 3-2h-1 0c0-1-1-1-1-2l1-2 1 1c1-1 2-2 3-2z" class="AD"></path><path d="M263 317c2 2 3 4 4 6l1-1c2 0 2 0 3-1s1 0 2 0h2v1h-1s0 1-1 1h0-1c-2 0-4 1-5 2l-1-1-1-1c-1 0-1 1-2 1-1 1-1 2-1 2-1 3-1 3-4 4l-1-1c1-1 1-2 2-3s2-2 4-3v-1h2c-1-1-1-1-1-2l-1-1v-2z" class="Z"></path><path d="M254 325v-1c1-1 1-2 2-2 1-2 2-2 4-1h3v1 1c-2 1-3 2-4 3s-1 2-2 3l-1 2h0c-1-1-1-2-1-3v-1-2l-1 1v-1z" class="j"></path><path d="M260 321h3v1 1c-2 1-3 2-4 3h-3 0c0-2 3-3 4-5h0z" class="g"></path><path d="M257 314c1 0 2 1 3 1l1 1 2 1h0 0v2l1 1c0 1 0 1 1 2h-2v-1h-3c-2-1-3-1-4 1-1 0-1 1-2 2v1c-1 1-1 2-1 3h-1-1c-1-2-2-3-3-5h0l2-2h0 1c1-1 2-1 3-2h-1 0c0-1-1-1-1-2l1-2 1 1c1-1 2-2 3-2z" class="P"></path><path d="M254 319h1v1c0 1-2 3-2 4v-1c-1-1-2-1-2-1l-1-1h1c1-1 2-1 3-2z" class="AC"></path><path d="M263 321c-1-1-2-1-4-2h0-1c1-1 1-1 1-2s0-1 1-1c0 1 1 1 1 2l2-1h0v2l1 1c0 1 0 1 1 2h-2v-1z" class="AE"></path><path d="M257 314c1 0 2 1 3 1-1 0-1 1-2 1h0c-2 0-4 2-5 3h0 0c0-1-1-1-1-2l1-2 1 1c1-1 2-2 3-2z" class="c"></path><path d="M250 321h0l1 1s1 0 2 1v1l-1 1c-1 1-1 2-1 3-1-2-2-3-3-5h0l2-2z" class="b"></path><path d="M251 322s1 0 2 1v1l-1 1c-1 0-1-1-2-2 0 0 0-1 1-1z" class="K"></path><path d="M292 354c2 0 8-1 9 0h0c-1 2-2 3-3 5-1 3-2 9-2 12 0 2 1 3 1 5h0l-1 1v3l-1-2-1 1v2c2 0 3 0 6 1v1h-2l-1 1h-4 0c-1 0-3 0-4 1-1 0-1 1-2 1 0 1-1 2-1 3-1 0-1 1-3 1v-6l-1-7v-10-7c-1-2-1-4-1-5l1-1h6 4z" class="AC"></path><path d="M295 364c0 4-2 9 0 13v1l-1 1v2h-2c-1 0-2 0-3-1 1-1 1-2 1-4 2-4 2-8 5-12z" class="AD"></path><path d="M292 380c1-1 1-1 2-1v2h-2v-1z" class="o"></path><path d="M290 376c0 2 1 3 2 4v1c-1 0-2 0-3-1 1-1 1-2 1-4z" class="H"></path><path d="M292 354c2 0 8-1 9 0h0c-1 2-2 3-3 5-1 3-2 9-2 12 0 2 1 3 1 5h0l-1 1v3l-1-2v-1c-2-4 0-9 0-13l6-10h-9z" class="f"></path><path d="M289 380c1 1 2 1 3 1h2c2 0 3 0 6 1v1h-2l-1 1h-4 0c-1 0-3 0-4 1-1 0-1 1-2 1 0 1-1 2-1 3-1 0-1 1-3 1v-6 1h1l2-1c-1 0-1-1-1-1v-1c1-1 2-1 4-2z" class="v"></path><path d="M286 384h3 4 0c-1 0-3 0-4 1-1 0-1 1-2 1 0 1-1 2-1 3-1 0-1 1-3 1v-6 1h1l2-1z" class="AD"></path><path d="M182 125l5 1c2 0 4 0 7 1 10 3 19 9 26 16 2 2 3 3 4 5 6 8 9 18 11 29v2 5c2 11 1 23 1 33v82 60 33 60 38c0 12-1 24-5 35h0l-2 7c-6 15-13 30-23 42-3 3-6 5-9 7-5 5-10 9-15 12-4 2-9 4-13 6h0-1 0c-3 1-5 3-8 3l1-1c1 0 1 0 1-1-1 1-3 1-5 2h0c-2 0-3 1-5 2h-4-4l20-8h0c9-4 18-9 25-16l1-1c3-2 5-4 7-6l2-2 3-3 1-1c1-2 3-4 4-5l1-1-3-2c-5-2-9-3-13-5v-1c0-2-2-4-3-5-1 0-3-2-3-3-1-1 0-2 0-3v-2c-1-1-1-1 0-2l1-1s1-1 2-1l1 1h0c1-1 1-2 2-2 2-1 3-1 5-3h0 0-1v-1-1h0 1l1-1v-2l2-2c1 1 0 1 1 2v-1h2c2 0 3 1 4 2l1-1c3-1 4-4 6-7 1 0 1 1 2 2l1 1c1-2 2-3 4-4 2 0 3 0 5 1v2c1-3 2-7 2-9l1-3c1-4 1-9 1-14v-6c1-6 1-12 1-17v-4-15-4c-2-1-3-3-4-4 1-1 1-1 1-2v-2h0c1-1 2-2 2-3h0l1-10-1-21v-14-4c1-3 0-6 0-9v-8-8-5-3h-1c-2-4-4-7-5-11 0-2 0-3-1-5-1-3-5-8-4-11l-1-3v-2s0-1-1-1v-1c0-1-1-2-1-3s0-2-1-3c0-3-1-6-1-9 0-2-1-4-1-6v-11-19c0-3 1-7 0-9h-1-1l-1 2c0-3 1-5 0-8h-1c1-1 1-3 2-4v1h1v1h0c2-2 2-2 2-4l1-1v-5h-1v-4h1l3-6h0c0-3 3-7 6-8 0-1 1-1 2-1h1c2-1 3-2 4-4l-1-19v-12-2-2l-1-2v-2l-1-6-2-6-4-8-3-5c-1-2-2-6-3-8l-3-6c-1 0-1-2-1-2-2-3-6-4-7-6h-1v-1c-14-7-30-8-46-6l-1-2c8-1 14-1 21-1h3l1-1z" class="g"></path><path d="M233 178l1-1h1v2h-1v3c0-1 0-3-1-4z" class="H"></path><path d="M182 125l5 1-2 1-7-1h3l1-1z" class="T"></path><path d="M234 182v-3h1v5 3h0v-3h0l-1 4v-6z" class="O"></path><path d="M230 309h0l1 13h-2-2c0-1-1-2-1-4 1 1 3 2 4 2v-5-3-3z" class="d"></path><path d="M221 525v-1h4l-2 6v-1c0-1-1-1-1-2 0 0-1-1-1-2z" class="B"></path><path d="M227 293v-1c0-1 1-2 2-3l2-2-1 22h0v-16-1l-3 1z" class="C"></path><path d="M230 182c1 3 2 9 1 12-1 0 0 1 0 2 0 3 0 5-1 7v-12-2-2l-1-2 1-1h0v-2z" class="H"></path><path d="M224 317s3 0 3-1c1 0 1-1 1-1h2v5c-1 0-3-1-4-2-1 0-2 0-4 1 1-1 1-2 2-2z" class="S"></path><path d="M230 439h0l1 11c-2-1-3-3-4-4 1-1 1-1 1-2v-2h0c1-1 2-2 2-3z" class="C"></path><path d="M228 169c2 4 2 9 2 13v2h0l-1 1v-2l-1-6v-4-4z" class="G"></path><path d="M187 126c2 0 4 0 7 1 10 3 19 9 26 16 2 2 3 3 4 5h-2l-1-2c-1-1-2-2-3-4-7-6-14-11-23-13-1-1-2-1-4-1s-4-1-6-1l2-1z" class="H"></path><path d="M224 148c6 8 9 18 11 29h-1l-1 1h0v-2-1l-1-2c0-1 0-2-1-3v-1-1c-1-1-1-3-1-4s0-2-1-3v-1c-1-1-1-2-1-3h-1c-1-3-3-6-5-9h2zm-55 448l5-2 1 1-7 4c-3 1-5 3-8 3l1-1c1 0 1 0 1-1-1 1-3 1-5 2h0c-2 0-3 1-5 2h-4-4l20-8c-1 2-4 3-6 4-1 0-1 0-2 1 1 0 2-1 3-1h1l1-1h1c1-1 1-1 2-1s1 0 2-1c2-1 0 0 1 0l1-1h0 1 0 0z" class="G"></path><path d="M169 596l5-2 1 1-7 4c-3 1-5 3-8 3l1-1c1 0 1 0 1-1-1 1-3 1-5 2h0c4-2 8-3 12-6z" class="r"></path><path d="M221 152c2 2 3 5 4 7l3 10v4 4l-2-6-4-8v-3-2c0-1-1-5-1-6h0z" class="AU"></path><path d="M225 159l3 10v4 4l-2-6v-1c0-4-1-7-1-11z" class="T"></path><path d="M204 136c7 3 14 9 17 16h0c0 1 1 5 1 6v2 3l-3-5c-1-2-2-6-3-8l-3-6c-1 0-1-2-1-2-2-3-6-4-7-6h-1z" class="R"></path><path d="M221 519c2 0 3 0 5 1v2l-1 2h-4v1c0 1 1 2 1 2 0 1 1 1 1 2v1l-3 8c-1 0-2-1-4-2-1-4-2-5-1-9 0-2 1-3 2-4 1-2 2-3 4-4z" class="Y"></path><path d="M221 525c0 1 1 2 1 2 0 1 1 1 1 2v1l-3 8c-1 0-2-1-4-2-1-4-2-5-1-9 0 1 0 4 1 5v-1h0l1-1v-1c1-1 2-1 3-1 0 0 1 0 1-1v-2z" class="C"></path><path d="M216 531l1-1h1c1 1 2 1 3 1-2 2-2 3-3 5h0v-2c0-1-2-2-2-3h0z" class="D"></path><path d="M175 595c1 0 3-2 4-2 4-3 8-5 13-9 1-1 3-2 5-4 7-6 13-12 18-20 3-6 6-13 9-20l4-8c0-2 1-3 1-5 1-1 1-1 1-2l1-1v1l-2 7c-6 15-13 30-23 42-3 3-6 5-9 7-5 5-10 9-15 12-4 2-9 4-13 6h0-1 0l7-4z" class="H"></path><path d="M207 528l1-1c3-1 4-4 6-7 1 0 1 1 2 2l1 1c-1 1-2 2-2 4-1 4 0 5 1 9 2 1 3 2 4 2l-2 6-3 6-1 1-3 6-1 1-2 3-3-2c-5-2-9-3-13-5v-1c0-2-2-4-3-5-1 0-3-2-3-3-1-1 0-2 0-3v-2c-1-1-1-1 0-2l1-1s1-1 2-1l1 1h0c1-1 1-2 2-2 2-1 3-1 5-3h0 0-1v-1-1h0 1l1-1v-2l2-2c1 1 0 1 1 2v-1h2c2 0 3 1 4 2z" class="E"></path><path d="M212 537h0c0-2 0-3-1-4h0c1 1 2 2 2 3v1h0v1l-1-1h0z" class="W"></path><path d="M210 544v2 1l-1 2c-1-1-1-2-2-2h-1l4-3z" class="Y"></path><path d="M211 536l1 1h0c-1 2-3 3-5 4h-3 0c2-3 4-4 7-5z" class="S"></path><path d="M203 526c2 0 3 1 4 2s2 1 2 2l-1 1c-1-1-2-1-3-1h-2v-1c-1-1-1-1-2-1v-1-1h2z" class="C"></path><path d="M200 535c0 1 1 2 2 2s3-1 4-1 3-1 5 0c-3 1-5 2-7 5h0c0 1-1 1-2 1v1h-2v-3h-1v-1c0-1 1-3 1-4z" class="X"></path><path d="M210 544l4-4h1c0 2 1 2 2 3v1h1l-3 6-1 1c-2 0-3-1-5-2l1-2v-1-2z" class="L"></path><path d="M217 544h1l-3 6v-1-1-1-2h1l1-1z" class="C"></path><path d="M212 545l1-1c1 1 1 1 1 2v2l1 1v1l-1 1c-2 0-3-1-5-2l1-2v-1l1-1h1z" class="S"></path><path d="M210 546l1-1h1c0 1 0 1-1 2h2v1l-1 1-2-2v-1z" class="C"></path><path d="M198 527l2-2c1 1 0 1 1 2v1c1 0 1 0 2 1v1 2h3c1 0 1 0 2 1h1l1 1-1 1c-1 0-2 0-3 1-1 0-3 1-4 1s-2-1-2-2c-1-1-1-1-1-2-1 0-1 1-1 1-1 1-1 1-2 1 0 0 1 0 1-1h0v-2h0 0-1v-1-1h0 1l1-1v-2z" class="W"></path><path d="M198 527l2-2c1 1 0 1 1 2v1c1 0 1 0 2 1h-1c-1 0-1 1-1 2 0 0 0 1 1 1v1h-1c-1-1-2-1-2-2-1 0-1-1-2-1l1-1v-2z" class="E"></path><path d="M198 527l2-2c1 1 0 1 1 2v1c0 1-1 1-2 2-1 0-1-1-1-1v-2z" class="x"></path><path d="M190 537c1-1 1-2 2-2 2-1 3-1 5-3v2h0c0 1-1 1-1 1 1 0 1 0 2-1 0 0 0-1 1-1 0 1 0 1 1 2 0 1-1 3-1 4v1h1v3h2l-1 2s1 1 1 2c1 0 1 0 2 1 1 0 1-1 2-1h1c1 0 1 1 2 2 2 1 3 2 5 2l-3 6-1 1-2 3-3-2c-5-2-9-3-13-5v-1c0-2-2-4-3-5-1 0-3-2-3-3-1-1 0-2 0-3v-2c-1-1-1-1 0-2l1-1s1-1 2-1l1 1h0z" class="M"></path><path d="M203 550h0l-2-4-1-1v-2h1v2s1 1 1 2c1 1 2 2 2 3h-1 0z" class="L"></path><path d="M203 550h0 1l3 3h1 0l3 4-1 1c-3-1-5-5-7-8z" class="Y"></path><path d="M202 547c1 0 1 0 2 1 1 0 1-1 2-1h1c1 0 1 1 2 2 2 1 3 2 5 2l-3 6-3-4h0-1l-3-3c0-1-1-2-2-3z" class="E"></path><path d="M202 547c1 0 1 0 2 1 1 0 1-1 2-1h1c0 1-1 2-1 2 1 1 2 3 2 4h0-1l-3-3c0-1-1-2-2-3z" class="C"></path><path d="M186 542l2 2 5 6c0 1 1 2 2 2 1 1 2 2 3 2h1 0c2 2 4 3 7 4l-1 1c-5-2-9-3-13-5v-1c0-2-2-4-3-5-1 0-3-2-3-3-1-1 0-2 0-3z" class="S"></path><path d="M190 537c1-1 1-2 2-2 2-1 3-1 5-3v2h0c0 1-1 1-1 1 0 3 2 4 3 7v1h-2-1v2 1 2l1 1v1l1 2h0l-2-1-1 1c-1 0-2-1-2-2l-5-6-2-2v-2c-1-1-1-1 0-2l1-1s1-1 2-1l1 1h0z" class="D"></path><path d="M190 537c1-1 1-2 2-2 2-1 3-1 5-3v2h0c0 1-1 1-1 1-1 1-3 1-4 2v2h-1v-2h-1z" class="m"></path><path d="M191 543l1-1c0 1 1 1 1 2 0 2 2 4 4 6l1 2h0l-2-1-1 1c-1 0-2-1-2-2h0c1-2-1-5-2-7z" class="F"></path><path d="M191 543h0c1 2 3 5 2 7h0l-5-6c1 0 2-1 3-1z" class="N"></path><path d="M189 536l1 1h0c0 2 1 4 1 6-1 0-2 1-3 1l-2-2v-2c-1-1-1-1 0-2l1-1s1-1 2-1z" class="AT"></path><path d="M190 537h0c0 2 1 4 1 6-1 0-2 1-3 1l-2-2v-2c1 0 1 1 2 2h0 1c1-2 1-3 1-5z" class="Q"></path><path d="M231 222l-1 4v6 22l1 19-1 4 1 2v8l-2 2c-1 1-2 2-2 3v1l3-1v1 16 3 3h-2s0 1-1 1c0 1-3 1-3 1-1 0-1 1-2 2 2-1 3-1 4-1 0 2 1 3 1 4h2 2c0 2 0 6-1 8v1 1c1 2 0 4 0 5v23-3h-1c-2-4-4-7-5-11 0-2 0-3-1-5-1-3-5-8-4-11l-1-3v-2s0-1-1-1v-1c0-1-1-2-1-3s0-2-1-3c0-3-1-6-1-9 0-2-1-4-1-6v-11-19c0-3 1-7 0-9h-1-1l-1 2c0-3 1-5 0-8h-1c1-1 1-3 2-4v1h1v1h0c2-2 2-2 2-4l1-1v-5h-1v-4h1l3-6h0c0-3 3-7 6-8 0-1 1-1 2-1h1c2-1 3-2 4-4z" class="Y"></path><path d="M221 287h2v1c0 1 0 1-1 2l-1 1-1-1c0-1 1-2 1-3z" class="C"></path><path d="M224 317c1-2 4-4 6-5v3h-2s0 1-1 1c0 1-3 1-3 1z" class="B"></path><path d="M230 274v3l1 2h-1c-1-1-2-1-2-1-1 0-1-1-2-1 0-1-1-1-2-2h1c1 0 2 1 2 2l1-1 2-2z" class="C"></path><path d="M227 322h2 2c0 2 0 6-1 8v-2l-5-3 1-1c2 1 3 3 4 4l-1-4c-1-1-2-1-3-1h0l1-1z" class="e"></path><path d="M231 279v8l-2 2c-1 1-2 2-2 3v1h-1c0-1 1-2 0-3v-2c1-1 2-2 4-3h0v-6h1z" class="J"></path><path d="M222 319c2-1 3-1 4-1 0 2 1 3 1 4l-1 1h0-1v1c-1 1-2 1-4 1-1-1-1-1-1-2 0-2 1-3 2-4z" class="AH"></path><path d="M225 323h-2v1l-1-1c0-1 1-2 2-3h0 2v1 2h0-1z" class="Z"></path><path d="M214 308c0 1 1 2 1 3l1-1c1 6 2 10 4 15 0 3 1 5 2 7h-1c-1 0-1-1-2-2l-1-3v-2s0-1-1-1v-1c0-1-1-2-1-3s0-2-1-3c0-3-1-6-1-9z" class="D"></path><path d="M224 275c-1 1-2 1-3 1-1 1-2 2-3 4h0-1c1-3 3-7 5-9 1-1 2-2 3-1 3 0 4 1 6 3l-1 4v-3l-2 2-1 1c0-1-1-2-2-2h-1z" class="I"></path><path d="M225 275l-2-2c0-1 2-2 3-3 2 1 3 2 4 4l-2 2-1 1c0-1-1-2-2-2z" class="l"></path><path d="M219 330c1 1 1 2 2 2h1c1 1 5 16 6 18 1 1 1 1 2 1v-4-14h-1v-1l1-1h0v1c1 2 0 4 0 5v23-3h-1c-2-4-4-7-5-11 0-2 0-3-1-5-1-3-5-8-4-11z" class="X"></path><path d="M231 222l-1 4v6 22h0v-11l-11 24c-1 4-2 8-3 11-1 11-2 22 0 32l-1 1c0-1-1-2-1-3 0-2-1-4-1-6v-11-19c0-3 1-7 0-9h-1-1l-1 2c0-3 1-5 0-8h-1c1-1 1-3 2-4v1h1v1h0c2-2 2-2 2-4l1-1v-5h-1v-4h1l3-6h0c0-3 3-7 6-8 0-1 1-1 2-1h1c2-1 3-2 4-4z" class="m"></path><path d="M222 240h1v1h1v1c-1 1-1 2-2 3h-1c-1 0-1 0-2-1 1-2 2-3 3-4z" class="G"></path><path d="M215 277c0-3 1-7 2-10 0 0 0 1 1 1 0 0-1 1 0 2-1-1-1-1 0-1 0-1 0-2 1-3v1c-1 4-2 8-3 11l-1-1z" class="AF"></path><path d="M215 241v1c1 1 2 1 3 1-1 2-1 4-2 6v2 1l-1-2v-5h-1v-4h1z" class="AS"></path><path d="M218 235h1l1 4-2 4c-1 0-2 0-3-1v-1l3-6z" class="n"></path><path d="M230 226h0v6 6l-6 3c1-2 3-4 4-6 0-1 0-3-1-4 1-1 2-1 3-2h0v-3z" class="F"></path><path d="M231 222l-1 4h0v3h0c-1 1-2 1-3 2l-6 8h-1c3-2 4-5 6-8l-1-1h-1c0-1 2-2 2-4h1c2-1 3-2 4-4z" class="S"></path><path d="M231 222l-1 4h0l-1-1c-1 2-2 5-3 6l-1-1h-1c0-1 2-2 2-4h1c2-1 3-2 4-4z" class="N"></path><path d="M224 227c0-1 1-1 2-1 0 2-2 3-2 4h1l1 1c-2 3-3 6-6 8h0l-1-4h-1 0c0-3 3-7 6-8z" class="w"></path><path d="M224 227c0-1 1-1 2-1 0 2-2 3-2 4-1 2-2 3-3 4v-1-1c1-2 1-3 2-4 1 0 1-1 1-1z" class="D"></path><path d="M216 251c1-1 1-1 1-2l2-2h1v-1l1 1v1l-1 1c0 3-1 6-3 9v1 1c-1 1-1 1-1 2h1v-2c1-1 2-3 2-4l1-1v-1l1-1c0-1 1-3 1-3v2l-4 7v2c-3 5-3 12-4 18 1 0 1-1 1-2l1 1c-1 11-2 22 0 32l-1 1c0-1-1-2-1-3 0-2-1-4-1-6v-11-19c0-3 1-7 0-9h-1-1l-1 2c0-3 1-5 0-8h-1c1-1 1-3 2-4v1h1v1h0c2-2 2-2 2-4l1-1 1 2v-1z" class="N"></path><path d="M216 251c1-1 1-1 1-2l2-2h1v-1l1 1v1l-1 1c-3 5-5 11-6 17 0 2 0 4-1 6 0-3 1-7 0-9h-1-1l-1 2c0-3 1-5 0-8h-1c1-1 1-3 2-4v1h1v1h0c2-2 2-2 2-4l1-1 1 2v-1z" class="C"></path><path d="M212 254v1h0c2-2 2-2 2-4l1-1 1 2-3 8-1-2c0-2-1-2-1-4h1z" class="AU"></path><path d="M209 257c1-1 1-3 2-4v1c0 2 1 2 1 4l1 2v3h-1-1l-1 2c0-3 1-5 0-8h-1z" class="w"></path><path d="M212 258l1 2v3h-1-1l1-5z" class="AV"></path><path d="M193 99h9c3 0 5 0 8-1l2-1 4 2 281 1v1c2 0 2 0 4 2 0 1 1 1 1 2h0 1c0 2 0 4-1 6v3l-1 3v-1c0-1 0-1-1-2v1 1c0 2 1 3 1 4v1h0c0 2 0 3 1 4v2c-2 1-3 2-3 4h0c-1 0 0 0-1-1 1 2 0 3 0 4l-1 1v3h1v1c-2-1-3-3-5-3-2 1-3 4-4 5l-2 2c-1 2-2 4-2 6l-1 3v3 1 1 2 2 2h-1 0c-4-11-9-20-16-29v1l-7-10c-1-1-2-1-3-2l-8-1c-1-1-2-1-3-1-4-1-8-1-12-1-3-1-5 0-8 0-2 0-5 0-7 1v2 1l3 3c-2 0-4 0-5 1-6-2-13-4-19-5-18-4-37-4-55-3h-19 0v1h-7v5c-1 2 0 3-1 4v7 30c0 2-1 6 0 8l1 2-4-2v1h0v2h-2 0l1 2v1c-1 1-1 2-3 3v1 1c-1 1-3 1-4 0v1h-4-1l-1 1c-2-1-3-3-4-5h0l-1-1c-1 0-1 0-2 1 0-1-1-1-2-1-1-1-2-1-3-1h0l-1-2-3 2h-2c-2 1-4 0-5 1s-2 1-3 1h-2c0-1-1-1-1-1-1 0-1 0-1-1h-1v-1c0-1 0-1-1-2h-1c-1 1-2 1-3 1h0l-2 1c-1-1-2-1-3-2h0c-3-1-7-6-8-8s-2-3-2-4l-2-3-1-4c-2-2-3-5-3-8-1 2-1 3-2 4-1-2-2-5-3-8-1-2-2-4-3-7h1v-3c0-1 0-1-1-2l-1-1-1 3c-1-1-2-2-2-4h-1c-2-1-2-2-2-3-1-1-2-3-3-4l-1-1c-1 0-2-1-2-2l1-1-3-5c-1-1 0-3 0-4s0-3 1-4c-1 0-2 1-3 1l-1 1-1-1-1-1v1l-1-2c0-1-1-1-1-1v-1h-1-2s0 1-1 1v2h-1c0 1 1 2 1 4h0c1 1 1 2 1 3h0l-1-1-2-3h0c-1-1-1-1-1-2v1l-3-2-6-2h-2l-4-2-1-2-2-3v-1h6z" class="k"></path><path d="M461 101v-1h6v1h-2-2-2z" class="I"></path><path d="M483 102l1-1h5 2s0 1-1 1c-1 1-5 0-7 0z" class="c"></path><path d="M212 107c3 0 4-3 6-5 2-1 6-1 8 0-3 0-5 0-7 1h-1c1 1 2 1 3 1-1 0-2 0-3 1l-2 1c-1 1-1 2-2 3v1l-1-2c0-1-1-1-1-1z" class="b"></path><path d="M218 105l1 1h0c1 1 2 1 3 1-1 1-2 1-2 2-1 0-2 1-3 1l-1 1-1-1-1-1c1-1 1-2 2-3l2-1z" class="s"></path><path d="M218 105l1 1h0c0 1-1 2-2 3l-1-3 2-1z" class="k"></path><path d="M214 109c1-1 1-2 2-3l1 3v1l-1 1-1-1-1-1z" class="c"></path><path d="M261 102h9 8c3 1 7 0 10 1h-5c-1 1-1 1-2 1h-5c-4-1-7-1-10 0h0v-1h0l-5-1z" class="G"></path><path d="M491 101h6c2 0 2 0 4 2h-2-2 0l1 1-1 1c0-1 0-1-1-1v-1h-14c-3 0-7 0-10-1h11c2 0 6 1 7 0 1 0 1-1 1-1z" class="Z"></path><path d="M288 103h6c2 0 5-1 8 0l-2 2h0c-1-1-3 0-4 0l-10-1h-2-3c1 0 1 0 2-1h5z" class="i"></path><path d="M288 103h6-5c-2 1-3 1-5 1h-3c1 0 1 0 2-1h5z" class="o"></path><path d="M302 103c1-1 1-1 3-2v1l-1 1c-1 1-2 2-2 3v3h1l-1 1c-1-1-1-2-1-3h-1c-1 0-1 1-2 2h0c-4-2-8-4-12-5l10 1c1 0 3-1 4 0h0l2-2z" class="g"></path><path d="M206 103c1-2 3-4 5-5h1c2 1 2 1 3 2 0 2 0 2-1 3l-1 1h0 2v1c-1 0-3 0-4-1l-1 1 2 1h-1-2s0 1-1 1v2h-1v-4-2h-1z" class="H"></path><path d="M210 105l-1-2c0-1 0-1 1-2h1v1c0 1 1 1 2 2h0 2v1c-1 0-3 0-4-1l-1 1z" class="R"></path><path d="M471 102h1c3 1 7 1 10 1h14v1 1h-8-3c-2 1-4 1-5 1-1-1-1-1-2-1h-2-1c-1-1-3-1-4-3z" class="j"></path><path d="M226 102h4 0v1c0 1 1 2 2 2 1 2 1 3 1 4l-1 2h0v-1l-2-1c0 1-1 2-2 2h0-1-2v-1l-2-2s0-1-1-1-2 0-3-1h0l-1-1c1-1 2-1 3-1-1 0-2 0-3-1h1c2-1 4-1 7-1h0z" class="r"></path><path d="M226 102h4 0v1c0 1 1 2 2 2 1 2 1 3 1 4l-1 2h0v-1l-2-1c0-1 1-2 1-3-1-2-3-3-5-4z" class="P"></path><path d="M221 104c2 0 3 0 5 2 1 1 2 2 2 4 0 0-1 0-1 1h-2v-1l-2-2s0-1-1-1-2 0-3-1h0l-1-1c1-1 2-1 3-1z" class="I"></path><path d="M276 104h5 3 2c4 1 8 3 12 5h0l5 4c1 1 1 2 1 3l1 1c0 1 1 2 2 3 0 0-1 0-1-1h0c-1-1-2-2-3-2-4-4-7-7-11-9h-1-3v1c-3-1-6-2-9-2l-1-1 1-1h-1c-1 0-2-1-2-1z" class="h"></path><path d="M279 107l1-2c4 0 8 1 12 3h-1-3v1c-3-1-6-2-9-2z" class="n"></path><path d="M189 103h1l-1-1v-1l1 1c5 0 11-1 15 1h1 0 1v2 4c0 1 1 2 1 4h0c1 1 1 2 1 3h0l-1-1-2-3h0c-1-1-1-1-1-2v1l-3-2-6-2h-2l-4-2-1-2z" class="AC"></path><path d="M195 105l2-1s1 0 1 1l1 2h2v-2h1c1 1 1 1 1 2h-1v1h0v1l-6-2h1c0-1-1-1-2-2z" class="AD"></path><path d="M189 103h1l-1-1v-1l1 1c2 1 4 1 5 3 1 1 2 1 2 2h-1-2l-4-2-1-2z" class="I"></path><path d="M205 103h1 0 1v2 4c0 1 1 2 1 4h0c1 1 1 2 1 3h0l-1-1-2-3h0c-1-1-1-1-1-2v1l-3-2v-1l2-2c0-1 0-2 1-3z" class="r"></path><path d="M205 103h1c-1 2-1 4-1 7v1l-3-2v-1l2-2c0-1 0-2 1-3z" class="o"></path><path d="M496 104c1 0 1 0 1 1l1-1-1-1h0 2 2c0 1 1 1 1 2h0 1c0 2 0 4-1 6v3l-1 3v-1c0-1 0-1-1-2v1 1c-1-1-2-1-3-2l-1-2h-2c-2-1-3-1-5-1h0c-2 0-2 1-3 2-1-1-2-1-3-2 0-1 1 0 0-1-1-2-2-3-3-4 1 0 3 0 5-1h3 8v-1z" class="g"></path><path d="M500 106v1c-1 1-1 2-2 2h-1c-1-1-5 0-6 0h-1c-1 0-2 0-2 1h-1v-1c-1-1-2 0-3 0v-1c3-1 4-2 7-2h4 5z" class="v"></path><path d="M496 104c1 0 1 0 1 1l1-1-1-1h0 2 2c0 1 1 1 1 2h0 1c0 2 0 4-1 6v3l-1 3v-1c0-1 0-1-1-2v1 1c-1-1-2-1-3-2l-1-2h-2c-2-1-3-1-5-1l1-1h3 0c1 0 1-1 2-1v1c1 0 2 0 3-1 1 0 1-1 2-2v-1c-1-1-3-1-4-1v-1z" class="I"></path><path d="M502 111v3l-1 3v-1c0-1 0-1-1-2h-1l3-3z" class="O"></path><path d="M496 112c1 0 1 0 2 1l1 1h1v1 1c-1-1-2-1-3-2l-1-2z" class="K"></path><path d="M306 103c1-1 2-1 3-1h6 12 17 9 4c2-1 3-1 4-1s3 1 4 1h1c1 0 1 0 2-1h8 6 2 3 0 2 10 3c2 1 2 0 3 0 0 0 1 0 1 1 1 0 4-1 5 0h1c1-1 3 0 4 0h1c1 0 2-1 3 0h4v1l1-1-1-1h0 2l2 2h-2v1l1 2h0l-8-3h-4-3s0 1-1 1h-22l-81-1c1 1 2 3 3 4-1 0-1 1-1 1-1 1-2 2-3 2-2 0-3-1-4-3l-1 2v-3c0-1 1-2 2-3v1c1 0 2-1 2-1z" class="s"></path><path d="M426 101l2 2h-2v1l1 2h0l-8-3h5c1 1 1 0 1 0l1-2z" class="b"></path><path d="M359 103h0 5l1-1c1 0 1 1 2 0 1 0 1 0 2 1v-1h4 2 2c1-1 7 0 9 0 2-1 4 0 6 0 2-1 5 0 7 0h12c2 0 3 0 4 1h-3-2-9-42z" class="c"></path><path d="M306 103h6 8 39 42 9 2s0 1-1 1h-22l-81-1c1 1 2 3 3 4-1 0-1 1-1 1-1 1-2 2-3 2-2 0-3-1-4-3l-1 2v-3c0-1 1-2 2-3v1c1 0 2-1 2-1z" class="o"></path><path d="M303 107c0-1 0-1 1-2 2 1 2 1 3 1 1 1 1 1 2 1s1 1 1 1c-1 1-2 2-3 2-2 0-3-1-4-3z" class="AB"></path><path d="M428 103h1 9 14c2 0 3-1 5-1l-2 2c1 0 1 0 1 1-1 2-2 2-2 5l1-1c0-1 1-1 2-2l2 2c-1 0-1 1-2 2v2h0c0 2 0 4 1 6 1 1 1 2 1 3s2 2 3 3l5 9h0v1l-7-10c-1-1-2-1-3-2l-8-1c-1-1-2-1-3-1-4-1-8-1-12-1-3-1-5 0-8 0 1-1 1-1 2-1h0v-1h0 1c1-1 2-1 3-1 0-1 0-1-1-2h-2c-1 0-1 0-1-1h0c-1-1-2-1-3-1-4-1-10 1-13-1h4c2-1 6 0 7-1v-1c-2-1-5-2-6-3l-1-1c-1 0-2-1-2-1h-1v-1l-1 1h0c-1 0-1 0-2 1-2 1-3 1-5 1h-1v-1l-1 1c-1 0-1 0-2-1 2 0 3-1 5-1v-1c-1 0-2 1-4 0h-4c-1 0-1 1-2 1s-3 0-4-1c0 0-1 1-1 0h-2 22c1 0 1-1 1-1h3 4c3 1 5 2 8 3h0l-1-2v-1h2 0z" class="h"></path><path d="M449 113v-2-3c1-1 1-1 2-1h1l-1 2h-1v2c0 1 0 1-1 1v2h0l-1-1h1z" class="K"></path><path d="M404 106h0c2-1 4-2 6 0-2 1-3 1-5 1h-1v-1z" class="o"></path><path d="M427 106h0l-1-2v-1h2 0l2 1v1h2l1 2 10 4 1 1c-6 0-12-3-17-6z" class="g"></path><path d="M433 107c-2 0-4-1-5-2h2 2l1 2z" class="K"></path><path d="M428 103h1 9 14c2 0 3-1 5-1l-2 2c1 0 1 0 1 1-1 2-2 2-2 5 0 2 0 4 1 6h-2v1c0-1-1-1-2-2-1-2 0-4 0-6l1-2h-1c-1 0-1 0-2 1v3 2h-1-1c-2-2-4-4-7-6-1-1-2-1-3-2-2 0-4-1-5 0h-2v-1l-2-1z" class="i"></path><path d="M455 104c1 0 1 0 1 1-1 2-2 2-2 5 0 2 0 4 1 6h-2c0-1-1-2-1-3 0-4 1-6 3-9z" class="r"></path><path d="M449 113l-1-1c-2-2-5-5-8-7h6c1 0 2 1 3 1s2-1 4-1h0l-1 2h-1c-1 0-1 0-2 1v3 2z" class="p"></path><path d="M455 109c0-1 1-1 2-2l2 2c-1 0-1 1-2 2v2h0c0 2 0 4 1 6 1 1 1 2 1 3s2 2 3 3l5 9h0v1l-7-10c-1-1-2-1-3-2l-8-1c-1-1-2-1-3-1-4-1-8-1-12-1-3-1-5 0-8 0 1-1 1-1 2-1h0v-1h0 1c1-1 2-1 3-1 0-1 0-1-1-2h-2c-1 0-1 0-1-1h0c-1-1-2-1-3-1-4-1-10 1-13-1h4 11c9 1 18 4 27 7 0-1 0-1-1-2v-1h2c-1-2-1-4-1-6l1-1z" class="o"></path><path d="M455 116l1 2 1 2h0-3v-1c0-1 0-1-1-2v-1h2z" class="K"></path><path d="M455 109c0-1 1-1 2-2l2 2c-1 0-1 1-2 2v2h0c0 1 0 4-1 5l-1-2c-1-2-1-4-1-6l1-1z" class="P"></path><path d="M455 109c0-1 1-1 2-2l2 2c-1 0-1 1-2 2h-2v-2z" class="d"></path><path d="M432 117c3 1 5 1 8 1 2 1 5 1 7 1 3 1 5 2 9 2 1 0 1 1 2 2h-1l-8-1c-1-1-2-1-3-1-4-1-8-1-12-1-3-1-5 0-8 0 1-1 1-1 2-1h0v-1h0 1c1-1 2-1 3-1z" class="I"></path><path d="M258 102h3l5 1h0v1h0c-4 1-8 1-11 3v1l-3 2v1c-1 1-2 2-3 2l-3 3v1c-1 1-1 2-2 3h0c-3 5-6 9-8 15v1l-1 1c0-1 0-1-1-2l-1-1-1 3c-1-1-2-2-2-4h-1c-2-1-2-2-2-3-1-1-2-3-3-4l-1-1c-1 0-2-1-2-2l1-1-3-5c-1-1 0-3 0-4s0-3 1-4c0-1 1-1 2-2 1 0 1 1 1 1l2 2v1h2 1 0c1 0 2-1 2-2l2 1v1h0l1-2c0-1 0-2-1-4-1 0-2-1-2-2v-1h28z" class="K"></path><path d="M236 107h1l2 2c0 1 0 2-1 3h-1l-1-1 1-1v-1h-1l-1 1h0c0-1 0-1 1-3z" class="i"></path><path d="M229 121h0c0-2 0-3-1-4v-1h1s1 1 1 2c1 0 1 1 2 1 0 1-1 2-2 2h-1 0z" class="n"></path><path d="M222 107c1 0 1 1 1 1l2 2v1h2 1 0c1 0 2-1 2-2l2 1v1h0l1-2c0 2 0 3-1 4v2 1c-1 0-2-1-3-1-1 1-1 0-2 0h-2v1h2c-1 1-2 1-2 2l4 3h0l-1 2c-1 2-1 3-1 5v2c-1-1-2-3-3-4l-1-1c-1 0-2-1-2-2l1-1-3-5c-1-1 0-3 0-4s0-3 1-4c0-1 1-1 2-2z" class="a"></path><path d="M225 125v-2-3c1 1 2 1 2 2 0 2 0 3-1 4h-1v-1z" class="K"></path><path d="M227 122l1 1c-1 2-1 3-1 5v2c-1-1-2-3-3-4l-1-2c1 0 1 1 2 1v1h1c1-1 1-2 1-4z" class="H"></path><path d="M225 110v1h2 1c-1 1-2 2-2 3h0c2-1 2 0 3 0h-7l-1-1c1 0 2-1 2-1h0l2-2z" class="c"></path><path d="M230 109l2 1v1c-1 1-1 1-1 2v1l-1 1-1-1c-1 0-1-1-3 0h0c0-1 1-2 2-3h0c1 0 2-1 2-2z" class="s"></path><path d="M230 109l2 1v1c-1 1-1 1-1 2h-1l-2-2c1 0 2-1 2-2z" class="c"></path><path d="M220 116l1 1c1 0 2-1 2 0h0c-1 2 0 5 0 7l1 2-1-1c-1 0-2-1-2-2l1-1-3-5 1-1z" class="n"></path><path d="M222 107c1 0 1 1 1 1l2 2-2 2h0c-1 0-2 0-3 1v3l-1 1c-1-1 0-3 0-4s0-3 1-4c0-1 1-1 2-2z" class="H"></path><path d="M223 108l2 2-2 2h0c-1 0-2 0-3 1v-1c2-1 3-2 3-4z" class="O"></path><path d="M258 102h3l5 1h0v1h0c-4 1-8 1-11 3v1l-3 2v1c-1 1-2 2-3 2l-3 3v1c-1 1-1 2-2 3h0c-3 5-6 9-8 15v1l-1 1c0-1 0-1-1-2l-1-1-1 3c-1-1-2-2-2-4h0v-2-1-1l2-4c0-1 0-2 1-3v-1c1 0 1-1 1-2h1c0-1 1-2 2-3h1v-1h1s2-1 2-2c1-3 4-5 6-6s4-2 5-3h1-1c-1-1-5-1-6 0h-1c-1 0-1 0-1 1h0-4c-2-1-3-1-5 0h-3c-1 0-2-1-2-2v-1h28z" class="b"></path><path d="M258 102h3l5 1h0v1h0c-4 1-8 1-11 3-5 2-8 4-12 7v-1c0-2 3-3 4-4 2-1 4-1 6-2 1-1 3-2 4-2l1-1h0v-2z" class="AD"></path><path d="M255 107v1l-3 2v1c-1 1-2 2-3 2l-3 3-3 2h-2l3-3-1-1h0c4-3 7-5 12-7z" class="v"></path><path d="M244 115l8-5v1c-1 1-2 2-3 2l-3 3-3 2h-2l3-3z" class="I"></path><path d="M243 114l1 1-3 3-3 3c-2 3-3 7-4 10l-1 3-1 3c-1-1-2-2-2-4h0l1-1v-2c1-1 2-4 2-5l1-2c0-1 1-1 1-2h0c1-1 1-1 2-1v-1c1-2 4-4 6-5z" class="h"></path><path d="M243 118l3-2v1c-1 1-1 2-2 3h0c-3 5-6 9-8 15v1l-1 1c0-1 0-1-1-2l-1-1 1-3c1-3 2-7 4-10l3-3h2z" class="c"></path><path d="M241 118h2l-3 3h0-1 0-1l3-3z" class="V"></path><path d="M233 134l1-3h2c0 1 0 2-1 2l1 2v1l-1 1c0-1 0-1-1-2l-1-1z" class="T"></path><path d="M311 107c-1-1-2-3-3-4l81 1h2c0 1 1 0 1 0 1 1 3 1 4 1s1-1 2-1h4c2 1 3 0 4 0v1c-2 0-3 1-5 1 1 1 1 1 2 1l1-1v1h1c2 0 3 0 5-1 1-1 1-1 2-1h0l1-1v1h1s1 1 2 1l1 1c1 1 4 2 6 3v1c-1 1-5 0-7 1h-4-7c-2 1-5 2-8 2-6 1-13-1-19 1h0 0l-3 1h-55-13v-1-1h3v-1h-1-1c-2-1-4-2-5-4h-1l1-2c1 2 2 3 4 3 1 0 2-1 3-2 0 0 0-1 1-1z" class="K"></path><path d="M400 108h2 1l2 1v1h-2c-2 0-4-1-6-1h-14c3-1 6-1 10-1 2 0 5 1 7 0z" class="o"></path><path d="M370 107h23c2 0 5 0 7 1-2 1-5 0-7 0-4 0-7 0-10 1l-17-1h1 1c3-1 5 0 7-1-1 0-3 1-5 0h0z" class="P"></path><path d="M410 106c1-1 1-1 2-1h0l1-1v1h1s1 1 2 1l1 1c1 1 4 2 6 3v1c-1 1-5 0-7 1h-4-7c-2 1-5 2-8 2-1-1-2 0-3-1 2 0 4 1 5 0h2c0-1 0-1 1-1h0 1 1c1-1 3 0 4 0 4-1 8 0 12-1h2c-1-1-2-1-3-1-3 0-6-1-9 0-2 0-4 0-6 1h0-3-1c1 0 2-1 3-1h0 2v-1l-2-1 1-1h1c2 0 3 0 5-1z" class="AB"></path><path d="M404 107h1v1h7c1-1 4 0 5 1h-5c-2 0-5 0-7 1h-2 0 2v-1l-2-1 1-1z" class="O"></path><path d="M311 107h59 0c2 1 4 0 5 0-2 1-4 0-7 1h-1-1-53c-1 2-3 3-5 5-2-1-4-2-5-4h-1l1-2c1 2 2 3 4 3 1 0 2-1 3-2 0 0 0-1 1-1z" class="I"></path><path d="M311 107c-1-1-2-3-3-4l81 1h2c0 1 1 0 1 0 1 1 3 1 4 1s1-1 2-1h4c2 1 3 0 4 0v1c-2 0-3 1-5 1 1 1 1 1 2 1l1-1v1l-1 1h-1-2c-2-1-5-1-7-1h-23-59z" class="r"></path><path d="M393 107h6c1-1 0-1 2-1h0c1 1 1 1 2 1l1-1v1l-1 1h-1-2c-2-1-5-1-7-1z" class="G"></path><path d="M298 109c1-1 1-2 2-2h1c0 1 0 2 1 3l1-1c1 2 3 3 5 4h1 1v1h-3v1 1h13 55l3-1h0 0c6-2 13 0 19-1 3 0 6-1 8-2h7c3 2 9 0 13 1 1 0 2 0 3 1h0c0 1 0 1 1 1h2c1 1 1 1 1 2-1 0-2 0-3 1h-1 0v1h0c-1 0-1 0-2 1-2 0-5 0-7 1v2 1l3 3c-2 0-4 0-5 1-6-2-13-4-19-5-18-4-37-4-55-3h-19 0v1h-7v5c-1 2 0 3-1 4v7 30c0 2-1 6 0 8l1 2-4-2v1h0v2h-2 0l1 2v1c-1 1-1 2-3 3v1 1c-1 1-3 1-4 0v1h-4-1l-1 1c-2-1-3-3-4-5h0l-1-1c0-1 1-2 2-3l1-1 2 1 3-3c3-3 5-6 7-9 2-2 2-6 3-9 0-2 1-3-1-5l1-1v-5c1-1 1-2 1-3-1 0-1 0-1-1 1-1 0-3 0-4 1-1 1-2 1-3h0c-2-7-5-13-10-19 1 0 2 1 3 2h0c0 1 1 1 1 1-1-1-2-2-2-3l-1-1c0-1 0-2-1-3l-5-4z" class="h"></path><path d="M314 120v3l-1 1-3-3h1 0c1 0 2 0 3-1h0z" class="K"></path><path d="M412 119h6c1 1 1 2 1 2v2 1c-1-1 0-2-1-3h-10c1 0 3-1 4-2z" class="H"></path><path d="M394 119c2-1 9-1 10 0h1 2 5c-1 1-3 2-4 2s-2 0-3-1c-4-1-7-1-11-1z" class="o"></path><path d="M316 130c-1-2-1-5-1-7v-3h28-19 0v1h-7v5c-1 2 0 3-1 4z" class="AD"></path><path d="M313 136c1 5 2 11 1 16l-1 1c0 2 0 4-1 5 0-2 1-3-1-5l1-1v-5c1-1 1-2 1-3-1 0-1 0-1-1 1-1 0-3 0-4 1-1 1-2 1-3z" class="T"></path><path d="M312 147l1-1c1 2 0 4 1 6l-1 1c0 2 0 4-1 5 0-2 1-3-1-5l1-1v-5z" class="s"></path><path d="M309 172c2-2 2-3 4-6 0 2-1 5-1 7-1 0-2 0-3 1 1 1 3 0 4 1v1h-4-1-1c-1 1-2 1-3 3v-1h0c1-2 1-3 2-4s2-1 3-2z" class="b"></path><path d="M312 158c1-1 1-3 1-5 1 3 0 5 0 8v5c-2 3-2 4-4 6v-5c2-2 2-6 3-9z" class="j"></path><path d="M298 109c1-1 1-2 2-2h1c0 1 0 2 1 3l1-1c1 2 3 3 5 4h1 1v1h-3v1 1h0c1 0 1 0 2 1h2c0 1 1 1 2 1l1 2h0c-1 1-2 1-3 1h0-1l-1-1-1 1c1 2 3 4 4 6 0 1 0 3 1 5v4c-2-7-5-13-10-19 1 0 2 1 3 2h0c0 1 1 1 1 1-1-1-2-2-2-3l-1-1c0-1 0-2-1-3l-5-4z" class="H"></path><path d="M305 116c1-1 1 0 2 0h0c1 0 1 0 2 1h2c0 1 1 1 2 1l1 2h0-1c-3 0-6-2-8-4z" class="AH"></path><path d="M298 109c1-1 1-2 2-2h1c0 1 0 2 1 3l1-1c1 2 3 3 5 4h1 1v1h-3v1 1c-1 0-1-1-2 0v-1-1h1v-1h-1-2l-5-4z" class="o"></path><path d="M375 116h12l-1 2c-3-1-7-1-10-1h-43-15c-1 0-4 0-5 1-1 0-2 0-2-1h-2c-1-1-1-1-2-1h0 13 55z" class="I"></path><path d="M307 116h13 0-5v1h12c2 0 4-1 6 0h-15c-1 0-4 0-5 1-1 0-2 0-2-1h-2c-1-1-1-1-2-1h0z" class="Z"></path><path d="M309 167v5c-1 1-2 1-3 2s-1 2-2 4h0v1c1-2 2-2 3-3h1 1 4 0v2h-2 0l1 2v1c-1 1-1 2-3 3v1 1c-1 1-3 1-4 0v1h-4-1l-1 1c-2-1-3-3-4-5h0l-1-1c0-1 1-2 2-3l1-1 2 1 3-3c3-3 5-6 7-9z" class="U"></path><path d="M295 183l1-1h0l4 5-1 1c-2-1-3-3-4-5z" class="H"></path><path d="M301 187c0-2-3-3-3-6h1c1 0 1 2 2 3s2 1 3 2h0 1v1h-4z" class="b"></path><path d="M304 178v1c0 1 1 2 2 3 0 1 1 1 2 1h1v-2h2s0-1 1-1v1c-1 1-1 2-3 3s-4 1-6 0c-1-1-1-2-2-3l3-3z" class="K"></path><path d="M304 179c1-2 2-2 3-3h1 1 4 0v2h-2 0l1 2c-1 0-1 1-1 1h-2v2h-1c-1 0-2 0-2-1-1-1-2-2-2-3z" class="P"></path><path d="M308 176h1 4 0v2h-2 0l1 2c-1 0-1 1-1 1h-2c-1-2-1-3-1-5z" class="AB"></path><path d="M397 114c3 0 6-1 8-2h7c3 2 9 0 13 1 1 0 2 0 3 1h0c0 1 0 1 1 1h2c1 1 1 1 1 2-1 0-2 0-3 1h-1 0v1h0c-1 0-1 0-2 1-2 0-5 0-7 1 0 0 0-1-1-2h-6-5-2-1c-1-1-8-1-10 0-2 0-6 0-8-1l1-2h-12l3-1h0 0c6-2 13 0 19-1z" class="c"></path><path d="M428 114c0 1 0 1 1 1h2c1 1 1 1 1 2-1 0-2 0-3 1h-1-2-2c1-1 2-1 4-1v-1c-1 0-2 0-4-1h0l4-1z" class="a"></path><path d="M424 118h2 2 0v1h0c-1 0-1 0-2 1-2 0-5 0-7 1 0 0 0-1-1-2h-6-5c2 0 4 0 5-1h2 3 7z" class="V"></path><path d="M424 118h2 2 0v1h0c-1 0-1 0-2 1-2 0-5 0-7 1 0 0 0-1-1-2h6v-1z" class="c"></path><path d="M387 116h6c2 0 5 0 7 1 4 0 8 1 12 1-1 1-3 1-5 1h-2-1c-1-1-8-1-10 0-2 0-6 0-8-1l1-2z" class="k"></path><path d="M397 114c3 0 6-1 8-2h7c3 2 9 0 13 1 1 0 2 0 3 1h0l-4 1-31 1h-6-12l3-1h0 0c6-2 13 0 19-1z" class="O"></path><path d="M461 101h2l1 1c1-1 2-1 3 0h1 1 1 1c1 2 3 2 4 3h1 2c1 0 1 0 2 1s2 2 3 4c1 1 0 0 0 1 1 1 2 1 3 2 1-1 1-2 3-2h0c2 0 3 0 5 1h2l1 2c1 1 2 1 3 2 0 2 1 3 1 4v1h0c0 2 0 3 1 4v2c-2 1-3 2-3 4h0c-1 0 0 0-1-1 1 2 0 3 0 4l-1 1v3h1v1c-2-1-3-3-5-3-2 1-3 4-4 5l-2 2c-1 2-2 4-2 6l-1 3v3 1 1 2 2 2h-1 0c-4-11-9-20-16-29h0l-5-9c-1-1-3-2-3-3s0-2-1-3c-1-2-1-4-1-6h0v-2c1-1 1-2 2-2l-2-2c-1 1-2 1-2 2l-1 1c0-3 1-3 2-5 0-1 0-1-1-1l2-2 2-1h2z" class="p"></path><path d="M468 114l1 1h1c1-1 2-2 4-2v2h-1-2-2c0 1-1 0-1-1z" class="G"></path><path d="M472 125c2 0 3 0 5 1 1 1 1 1 0 2s-3 1-4 0c-1 0-2-1-3-1l1-2h1z" class="P"></path><path d="M471 125h1c2 0 2 0 3 1v1h-2v-1c-1 0-1 0-2-1z" class="k"></path><path d="M470 124c1 0 3-1 4 0l1-1-1-2h2c2 0 4 3 5 4l-1 1h0c-1-1-2-1-2-1l-1 1c-2-1-3-1-5-1h-1l-1 2c-1 0-1-1-2-1 0-1 1-2 2-2z" class="Z"></path><path d="M473 107l3 1h-2c0 1 1 2 2 2h0v1l-2 2c-2 0-3 1-4 2h-1l-1-1c-2 0-2 0-3-1h3c1 0 3-2 4-3 0-1 0-2 1-3z" class="i"></path><path d="M481 125c1 2 1 3 2 5h0l1-1c0 1 0 1 1 2 0 1 0 3 1 4v1h0c1 0 1 0 1 1v1c1 1 1 1 1 2l1 1-2 2c-1 2-2 4-2 6l-1 3v3 1 1c-1-1-1-3-1-5-1-6 0-14-1-21-1-1-2-3-2-4v-1l1-1z" class="b"></path><path d="M485 143l1-1 1 1c-1 2-2 4-2 6l-1 3v3 1 1c-1-1-1-3-1-5 0-3 1-6 2-9z" class="AD"></path><path d="M484 129c0 1 0 1 1 2 0 1 0 3 1 4v1h0c1 0 1 0 1 1v1c1 1 1 1 1 2l1 1-2 2-1-1-1 1h-1c0-2 1-4 0-6s-1-5-1-7h0l1-1z" class="K"></path><path d="M486 136h0c1 0 1 0 1 1v1c1 1 1 1 1 2l1 1-2 2-1-1v-6z" class="U"></path><path d="M461 101h2l1 1c1-1 2-1 3 0h1 1 1 1c1 2 3 2 4 3v1h-1v1h-1c-1 1-1 2-1 3-1 1-3 3-4 3h-3s-1-1-2-1c0-1-1-1-2-2v1h-2l-2 2v-2c1-1 1-2 2-2l-2-2c-1 1-2 1-2 2l-1 1c0-3 1-3 2-5 0-1 0-1-1-1l2-2 2-1h2z" class="R"></path><path d="M464 105v-1h0 3l1 1c1 1 1 2 1 3h0c-1 0-2-1-4-1h0l-1-2z" class="H"></path><path d="M465 107c2 0 3 1 4 1-1 1-1 2-2 3s-3 0-4 1c0-1-1-1-2-2v1h-2l-2 2v-2c1-1 1-2 2-2 1-1 2-1 3-1h3v-1z" class="AD"></path><path d="M464 102c1-1 2-1 3 0h1 1 1 1c1 2 3 2 4 3v1h-1v1h-1c-1 1-1 2-1 3-1-1-1-2-1-2-1-2-1-3-3-3l-1-1-3-2z" class="c"></path><path d="M464 102c1-1 2-1 3 0h1 1 1c-1 0-1 1-1 1l2 2 1 1s-1 1-1 2c-1-2-1-3-3-3l-1-1-3-2z" class="P"></path><path d="M461 101h2l1 1 3 2h-3 0v1l1 2h0v1h-3c-1 0-2 0-3 1l-2-2c-1 1-2 1-2 2l-1 1c0-3 1-3 2-5 0-1 0-1-1-1l2-2 2-1h2z" class="f"></path><path d="M457 107h0c3-2 5-1 8 0h0v1h-3c-1 0-2 0-3 1l-2-2z" class="q"></path><path d="M461 101h2l1 1 3 2h-3 0v1c-1 0-4-1-5-1s-2 1-3 1c0-1 0-1-1-1l2-2 2-1h2z" class="b"></path><path d="M457 102l2-1 2 1c-1 0-2 1-2 2-1 0-2 1-3 1 0-1 0-1-1-1l2-2zm19 3h2c1 0 1 0 2 1s2 2 3 4c1 1 0 0 0 1 1 1 2 1 3 2l-1 3-1 2c1 3 1 5 2 8l1 5c0 1 0 3-1 4-1-1-1-3-1-4-1-1-1-1-1-2l-1 1h0c-1-2-1-3-2-5-1-1-3-4-5-4h-2l1 2-1 1c-1-1-3 0-4 0l-1-1c1-1 1-1 2-1l1-1h-4v-1l1-1 1 1v-1h0c1-1 3-2 4-2 1-1 1-1 1-2l1-1v-3-1h0c-1 0-2-1-2-2h2l-3-1h1v-1h1v-1h1z" class="h"></path><path d="M470 120v-1h0c1-1 3-2 4-2 1-1 1-1 1-2l1-1v1h1v2c1 0 2 1 4 2l2 2v2l-1-1c-1-1-2-2-3-2l-1-1c-1 0-2-1-3-1-1 1-3 2-5 2h0z" class="b"></path><path d="M477 115h1 0c1 1 2 2 3 2l2 2h0v-3h2l-1 2c1 3 1 5 2 8l1 5c0 1 0 3-1 4-1-1-1-3-1-4-1-1-1-1-1-2-1-2-2-4-2-7l1 1v-2l-2-2c-2-1-3-2-4-2v-2z" class="p"></path><path d="M483 121v2c1 0 1 1 1 1 0 1 1 2 1 3l1-1 1 5c0 1 0 3-1 4-1-1-1-3-1-4-1-1-1-1-1-2-1-2-2-4-2-7l1 1v-2z" class="g"></path><path d="M476 105h2c1 0 1 0 2 1s2 2 3 4c1 1 0 0 0 1 1 1 2 1 3 2l-1 3h-2v3h0l-2-2c-1 0-2-1-3-2h0-1-1v-1-3-1h0c-1 0-2-1-2-2h2l-3-1h1v-1h1v-1h1z" class="f"></path><path d="M476 108c1 0 2 1 3 2s2 4 3 6l-1 1c0-1-1-2-1-2 0-2-2-4-4-5h0c-1 0-2-1-2-2h2z" class="H"></path><path d="M476 105h2c1 0 1 0 2 1s2 2 3 4c1 1 0 0 0 1 1 1 2 1 3 2l-1 3h-2c-1-5-4-8-7-11z" class="r"></path><path d="M489 111h0c2 0 3 0 5 1h2l1 2c1 1 2 1 3 2 0 2 1 3 1 4v1h0c0 2 0 3 1 4v2c-2 1-3 2-3 4h0c-1 0 0 0-1-1 1 2 0 3 0 4l-1 1v3h1v1c-2-1-3-3-5-3-2 1-3 4-4 5l-1-1c0-1 0-1-1-2v-1c0-1 0-1-1-1h0v-1c1-1 1-3 1-4l-1-5c-1-3-1-5-2-8l1-2 1-3c1-1 1-2 3-2z" class="p"></path><path d="M497 114c1 1 2 1 3 2 0 2 1 3 1 4 0 0-1 0-1 1-1-3-2-5-4-7h1z" class="T"></path><path d="M500 121c0-1 1-1 1-1v1c0 2 0 3-1 5h-1s0-1-1-1c-2-1-5-2-7-2v-1c0-1 0-1 1-1 2-1 5 1 7 3 1-1 1-2 1-3z" class="f"></path><path d="M489 111h0c2 0 3 0 5 1h2l1 2h-1c-4-1-6-1-9 0l-1 3-2 1 1-2 1-3c1-1 1-2 3-2z" class="Z"></path><path d="M486 113c1-1 1-2 3-2h-1c0 2-1 2-1 3l-1 3-2 1 1-2 1-3z" class="P"></path><path d="M501 121h0c0 2 0 3 1 4v2c-2 1-3 2-3 4h0c-1 0 0 0-1-1l-1-2c-1-1-3-2-4-3 0-1 0-1 1-1-2 0-2 0-3-1 2 0 5 1 7 2 1 0 1 1 1 1h1c1-2 1-3 1-5z" class="G"></path><path d="M484 118l2-1v4c2 1 2 1 3 3v3h2v2l-3 2h-1l-1-5c-1-3-1-5-2-8z" class="H"></path><path d="M484 118l2-1v4c1 3 1 5 2 8v2h-1l-1-5c-1-3-1-5-2-8z" class="U"></path><path d="M491 129h0c1 1 3 1 4 1 0-1 1-1 2-1v-1l1 2c1 2 0 3 0 4l-1 1v3h1v1c-2-1-3-3-5-3-2 1-3 4-4 5l-1-1c0-1 0-1-1-2v-1c0-1 0-1-1-1h0v-1c1-1 1-3 1-4h1l3-2z" class="G"></path><path d="M491 129h0c1 1 3 1 4 1 0-1 1-1 2-1v-1l1 2c1 2 0 3 0 4l-1-2c-2-2-7 0-9 0v-1l3-2z" class="h"></path><path d="M497 132l1 2-1 1v3h1v1c-2-1-3-3-5-3-2 1-3 4-4 5l-1-1c0-1 0-1-1-2l2-1c0-1 1-3 1-3-1 0-1 0-2-1h1 0 7l1-1z" class="AC"></path><path d="M493 136h1v-2h1c0 1 1 1 2 1h0v3h1v1c-2-1-3-3-5-3z" class="b"></path><path d="M266 104c3-1 6-1 10 0 0 0 1 1 2 1h1l-1 1 1 1c3 0 6 1 9 2v-1h3 1c4 2 7 5 11 9 5 6 8 12 10 19h0c0 1 0 2-1 3 0 1 1 3 0 4 0 1 0 1 1 1 0 1 0 2-1 3v5l-1 1c2 2 1 3 1 5-1 3-1 7-3 9-2 3-4 6-7 9l-3 3-2-1-1 1c-1 1-2 2-2 3-1 0-1 0-2 1 0-1-1-1-2-1-1-1-2-1-3-1h0l-1-2-3 2h-2c-2 1-4 0-5 1s-2 1-3 1h-2c0-1-1-1-1-1-1 0-1 0-1-1h-1v-1c0-1 0-1-1-2h-1c-1 1-2 1-3 1h0l-2 1c-1-1-2-1-3-2h0c-3-1-7-6-8-8s-2-3-2-4l-2-3-1-4c-2-2-3-5-3-8-1 2-1 3-2 4-1-2-2-5-3-8-1-2-2-4-3-7h1v-3l1-1v-1c2-6 5-10 8-15h0c1-1 1-2 2-3v-1l3-3c1 0 2-1 3-2v-1l3-2v-1c3-2 7-2 11-3z" class="K"></path><path d="M239 139h0v3h0v-2h1v1c0 1-1 3-2 4-1-2 0-4 1-6z" class="r"></path><path d="M284 173v-2h-1c0-1 0-1-1-2 0-1 0-1 1-1h0l2 2h2v1h0c1 0 1 0 2 1-2 0-2-1-3 0l-1 1c0 1 0 1-1 1v-1z" class="O"></path><path d="M240 141c0-2 1-3 1-4l1 1h0c-1 3-1 6-2 9l-1 1h0l-1-3c1-1 2-3 2-4z" class="g"></path><path d="M276 166v1c3 2 5 4 8 6v1c0 1 0 1-1 2h0c-1 0-1-1-2-2l-1-1c-1-3-2-4-5-6l1-1z" class="T"></path><path d="M298 141c1 4 1 13-1 17l-1 3c-1-1-1-1-1-2 1-2 3-5 2-8v-1-1h0v-6l-1-1h1l1-1z" class="H"></path><path d="M240 133h0c1-2 2-4 4-5 3-5 8-9 13-12h1v1c-1 1-3 2-4 2-5 4-12 11-14 16 0 1 0 1-1 1v3c-1 2-2 4-1 6l1 3-2-1c-1-2-2-4-3-7h1v-3l1-1 2-3h1 0 1z" class="AD"></path><path d="M236 136l2-3h1 0 1l-2 5c-1 2-1 3-2 4 0-1 0-1-1-2v-3l1-1z" class="r"></path><path d="M289 113l1 1c1 1 4 3 5 4l3 4-2 3 2 1v1c0 1 0 2 1 3h1v2c0 2 1 4 2 6 0 1 1 3 1 4v3 1 1h-1 0c-1-5-1-9-3-14-3-8-9-13-17-16v-1l2 1c0-1 1-1 1-2h0 3c0-1 0-1 1-2z" class="i"></path><path d="M284 117c0-1 1-1 1-2h0 3v2h1l1 1c1 0 2 1 4 1v2h-2c-1 0-2-2-3-2h-2c-1-1-2-2-3-2z" class="H"></path><path d="M289 113l1 1c1 1 4 3 5 4l3 4-2 3-2-4v-2c-2 0-3-1-4-1l-1-1h-1v-2c0-1 0-1 1-2z" class="h"></path><path d="M289 115h2c0 2 0 2-1 3l-1-1v-2z" class="r"></path><path d="M289 113l1 1-1 1v2h-1v-2c0-1 0-1 1-2z" class="g"></path><path d="M278 151l1 1h0c1 0 1 0 2 1h1 1c1 1 1 1 2 1h1c2 1 3 2 5 2 0 1 0 1 1 2h0v1c-1 0-1-1-2-1 0 0-1 0-2-1h-1c-1 0-2-2-4-2-1 0-3-3-5-2v3h0c2 1 4 3 5 5h0l1 1c1 1 1 1 2 1 0 1-1 0 0 1l2 1v1h-1l-2-2-6-5-1 1h1v1c0 1 1 2 2 3-1 0-1 1-2 1 0-1-1-1-2-2 0-1 0-2-1-2v4c1 0 1 1 0 2v-1l-1 1c-2 1-2 1-3 2h0-1v-1-1h-1v-1h0c-1-1 0-3 0-4 0-2 1-3 2-5l1 2c1-1 1-2 2-2v2c1 0 1 1 2 1h1c-1-1-1-2-2-2v-1h1l-2-1 1-1c1-1 2-3 2-4z" class="p"></path><path d="M270 162c0-2 1-3 2-5l1 2-1 2h2 1v2c1 1 1 2 1 3l-1 1c-2 1-2 1-3 2h0-1v-1-1h-1v-1h0c-1-1 0-3 0-4z" class="I"></path><path d="M272 161h2c0 1-1 2-1 3l-2-1c0-1 1-1 1-2z" class="O"></path><path d="M271 163l2 1-2 4v-1h-1v-1c0-1 1-2 1-3z" class="h"></path><path d="M270 162c0-2 1-3 2-5l1 2-1 2c0 1-1 1-1 2s-1 2-1 3h0c-1-1 0-3 0-4z" class="H"></path><path d="M270 110h8c1 0 2 0 4 1h1c2 0 4 1 6 2-1 1-1 1-1 2h-3 0c0 1-1 1-1 2l-2-1v1c-2 0-4-1-6-1-2-1-3 0-5 0-3 0-6 1-9 0l2-1h-2v-3l1-1h0c2 0 3 0 4-1h3z" class="b"></path><path d="M263 111h0c2 0 5 0 6 1 0 1 0 2-1 3h-4-2v-3l1-1zm7-1h8v3h-1c-2 2-4 2-6 2-1-2-1-3-1-5z" class="r"></path><path d="M282 116s-1 0-1-1c0 0 1 0 1-1h0-2v1c-1 0-2 0-3-1 1-1 1-1 2-1s1-1 1-1v-1h3c2 0 4 1 6 2-1 1-1 1-1 2h-3 0c0 1-1 1-1 2l-2-1z" class="K"></path><path d="M272 169h0c1-1 1-1 3-2 3 2 4 3 5 6l1 1c1 1 1 2 2 2h0c1-1 1-1 1-2 1 0 1 0 1-1 1 1 1 1 2 1l1 1h0c1 0 1 0 1 1-2 0-3 1-5 1v1h3l1 1h-2l-3 2h-2c-2 1-4 0-5 1s-2 1-3 1h-2c0-1-1-1-1-1-1 0-1 0-1-1h-1v-1c0-1 0-1-1-2 2 0 3-1 4-1v-1c-1 0-1-1-1-2l1-2h-2l2-3h1z" class="AQ"></path><path d="M285 173c1 1 1 1 2 1l1 1h0c-1 0-2 0-2 1h-3c1-1 1-1 1-2 1 0 1 0 1-1z" class="g"></path><path d="M272 177s0-1 1-1v-1-3c0 2 1 3 2 4h0v2c1 0 2-1 2-1 1 0 1 1 2 1h-1c-1 1-2 1-3 1l-2 2h0-4-1v-1c0-1 0-1-1-2 2 0 3-1 4-1h1z" class="h"></path><path d="M272 177s0-1 1-1v-1-3c0 2 1 3 2 4h0v2c1 0 2-1 2-1 1 0 1 1 2 1h-1-3c-1 0-2 0-3-1z" class="G"></path><path d="M273 172l1-1c2 0 2 0 3 1h1c1 1 1 1 2 1l1 1c0 1-1 1-1 3l-1 1c-1 0-1-1-2-1 0 0-1 1-2 1v-2h0c-1-1-2-2-2-4z" class="R"></path><path d="M278 172c1 1 1 1 2 1l1 1c0 1-1 1-1 3l-1 1c-1 0-1-1-2-1 0 0-1 1-2 1v-2h2c1-2 1-2 1-4z" class="U"></path><path d="M272 169h0c1-1 1-1 3-2 3 2 4 3 5 6-1 0-1 0-2-1h-1c-1-1-1-1-3-1l-1 1v3 1c-1 0-1 1-1 1h-1v-1c-1 0-1-1-1-2l1-2h-2l2-3h1z" class="K"></path><path d="M271 169h1c0 1 0 2-1 3h-2l2-3z" class="c"></path><defs><linearGradient id="AP" x1="282.065" y1="106.939" x2="281.453" y2="118.01" xlink:href="#B"><stop offset="0" stop-color="#ccaa6d"></stop><stop offset="1" stop-color="#eed598"></stop></linearGradient></defs><path fill="url(#AP)" d="M266 104c3-1 6-1 10 0 0 0 1 1 2 1h1l-1 1 1 1c3 0 6 1 9 2 4 2 9 6 11 9h0v1c-1 0-1-1-1 0l-3-1c-1-1-4-3-5-4l-1-1c-2-1-4-2-6-2h-1c-2-1-3-1-4-1h-8-3c-1 1-2 1-4 1h0c-5 0-13 4-17 8l-2 1h0c1-1 1-2 2-3v-1l3-3c1 0 2-1 3-2v-1l3-2v-1c3-2 7-2 11-3z"></path><path d="M267 109c3 0 5-1 7-1s3 1 5 1h1v1h-11c1 0 2 0 2-1h-4z" class="V"></path><path d="M266 104c3-1 6-1 10 0 0 0 1 1 2 1h1l-1 1c-6 0-11 0-16 1h0c-2 0-4 0-7 1v-1c3-2 7-2 11-3z" class="AB"></path><path d="M262 107h0 2c1 0 1 1 1 1l1 1h1 4c0 1-1 1-2 1h-2c-1 1-2 1-4 1h0c-5 0-13 4-17 8l-2 1h0c1-1 1-2 2-3v-1l3-3c1 0 2-1 3-2v-1l3-2c3-1 5-1 7-1z" class="j"></path><path d="M262 107h0 2c1 0 1 1 1 1-4 0-9 1-13 3v-1l3-2c3-1 5-1 7-1z" class="P"></path><path d="M266 109h1 4c0 1-1 1-2 1h-2c-1 1-2 1-4 1h0c-5 0-13 4-17 8l-2 1h0c1-1 1-2 2-3 5-4 13-7 20-8z" class="k"></path><path d="M302 147h0 1v5c0 1 1 1 1 2h1c1 1 1 1 0 2 1 0 1 0 1 1 0-1 1-2 1-2v-1c0 3 0 5-1 7h0v1-1c1 0 1 0 1-1v1l1-2 1 1 2-7c2 2 1 3 1 5-1 3-1 7-3 9-2 3-4 6-7 9l-3 3-2-1-1 1c-1 1-2 2-2 3-1 0-1 0-2 1 0-1-1-1-2-1-1-1-2-1-3-1h0l-1-2h2l-1-1h-3v-1c2 0 3-1 5-1 0-1 0-1-1-1h0l-1-1c1 0 2-1 3-2 5-4 10-11 11-18l1-7z" class="b"></path><path d="M300 162l4-4c-1 2-1 4-2 6l-1 1-4 5c-1 1-2 3-4 3v1c-1 1-3 1-4 2 0-1 0-1-1-1h0c5-2 9-8 12-13z" class="AB"></path><path d="M302 147h0 1v5c0 1 1 1 1 2h1c1 1 1 1 0 2l-1 2-1 1v-1-1l-1-1c-1 2-2 4-2 6-3 5-7 11-12 13l-1-1c1 0 2-1 3-2 5-4 10-11 11-18l1-7z" class="G"></path><path d="M302 156v-2h1c1 1 1 2 1 3h-1l-1-1z" class="n"></path><path d="M311 153c2 2 1 3 1 5-1 3-1 7-3 9-2 3-4 6-7 9l-3 3-2-1-1 1c-1 1-2 2-2 3-1 0-1 0-2 1 0-1-1-1-2-1-1-1-2-1-3-1h0l-1-2h2c2-1 3-2 5-3s3-2 5-3c1 0 1-1 2-2 0-1 5-9 6-10h0v1-1c1 0 1 0 1-1v1l1-2 1 1 2-7z" class="d"></path><path d="M290 181h1c2-1 3-1 5-2-1 1-2 2-2 3-1 0-1 0-2 1 0-1-1-1-2-1v-1z" class="I"></path><path d="M288 179c2-1 3-2 5-3s3-2 5-3c-2 3-5 5-8 7h0v1 1c-1-1-2-1-3-1h0l-1-2h2z" class="k"></path><path d="M306 161h0v1-1c1 0 1 0 1-1v1l-1 2 2 1-1 2c-1 2-3 3-4 5s-3 4-5 5l3-5h0-1c0-1 5-9 6-10z" class="c"></path><path d="M306 163l2 1-1 2c-1 2-3 3-4 5 0-1 3-6 3-8z" class="i"></path><path d="M311 153c2 2 1 3 1 5-1 3-1 7-3 9-2 3-4 6-7 9l-3 3-2-1c2-1 3-3 5-4 2-2 3-4 4-5s1-1 1-2v-1l1-2-2-1 1-2 1-2 1 1 2-7z" class="e"></path><path d="M307 161l1-2 1 1c0 1 0 3-1 4l-2-1 1-2z" class="j"></path><path d="M288 109v-1h3 1c4 2 7 5 11 9 5 6 8 12 10 19h0c0 1 0 2-1 3 0 1 1 3 0 4 0 1 0 1 1 1 0 1 0 2-1 3v5l-1 1-2 7-1-1-1 2v-1c0 1 0 1-1 1v1-1h0c1-2 1-4 1-7v1s-1 1-1 2c0-1 0-1-1-1 1-1 1-1 0-2h-1c0-1-1-1-1-2v-5-1-1-3c0-1-1-3-1-4-1-2-2-4-2-6v-2h-1c-1-1-1-2-1-3v-1l-2-1 2-3-3-4 3 1c0-1 0 0 1 0v-1h0c-2-3-7-7-11-9z" class="O"></path><path d="M309 138c-1-2-1-4-1-7-1 0-1-1-1-1h1c1 2 1 3 2 5 0 1 0 2 1 4-1-1-1-1-2-1z" class="a"></path><path d="M309 138c1 0 1 0 2 1v12l-1-1v-6h0c-1 1-1 2-1 3v1h0v-3-7z" class="G"></path><path d="M303 146h1c0 1 0 1 1 1 1 1 1 0 1 1s0 2-1 2h-1v1h2l1-1s0-1 1-1v-1 2 4h-1v1s-1 1-1 2c0-1 0-1-1-1 1-1 1-1 0-2h-1c0-1-1-1-1-2v-5-1z" class="p"></path><path d="M309 148h0v-1c0-1 0-2 1-3h0v6l1 1 1 1-1 1-2 7-1-1-1 2v-1c0 1 0 1-1 1v1-1h0c1-2 1-4 1-7h1v-4l1-2z" class="I"></path><path d="M310 150l1 1 1 1-1 1-2 7-1-1 2-9z" class="b"></path><path d="M288 109v-1h3 1c4 2 7 5 11 9 5 6 8 12 10 19h0c0 1 0 2-1 3 0-2-1-4-2-6-1-3-2-6-3-8v2c0 1 1 1 1 2h-1-1c0 2 1 3 1 5h0c0 1 1 1 1 2-2-2-3-7-3-9 0-1 0-2-1-2 0-1-1-2-2-2 0-2-2-3-3-5-2-3-7-7-11-9z" class="K"></path><path d="M292 108c4 2 7 5 11 9 5 6 8 12 10 19h0c0 1 0 2-1 3 0-2-1-4-2-6-1-3-2-6-3-8h0c-3-5-6-9-10-13-2-1-4-2-6-4h1z" class="G"></path><path d="M299 118c1 2 3 3 3 5 2 4 3 8 5 13v4h-1c-1 1-2 1-2 3h0l2 1v1s0 1-1 1h-1-1v-1-3c0-1-1-3-1-4-1-2-2-4-2-6v-2h-1c-1-1-1-2-1-3v-1l-2-1 2-3-3-4 3 1c0-1 0 0 1 0v-1h0z" class="o"></path><path d="M295 118l3 1c1 1 2 2 2 4h-1l-1-1-3-4z" class="j"></path><path d="M298 122l1 1h1l-1 2h2c0 1 1 2 1 3l1 1-1 2-2-1h-1c-1-1-1-2-1-3v-1l-2-1 2-3z" class="g"></path><path d="M302 128l1 1-1 2-2-1h-1c1-1 2-2 3-2z" class="h"></path><path d="M302 131h0l1-1h0c1 1 1 1 1 2 0 2 1 3 2 4 0 1-1 1-1 2h-1c-1 0-1 0-1 1 2 0 2-1 3 1-1 1-2 1-2 3h0l2 1v1s0 1-1 1h-1-1v-1-3c0-1-1-3-1-4-1-2-2-4-2-6v-2l2 1z" class="K"></path><path d="M239 148l1 2h0c1-3 1-6 2-9 0-3 3-7 5-10 4-7 11-11 20-13 7-1 16 3 22 7 4 3 7 10 8 14 0 1 0 2 1 2h0l-1 1h-1l1 1v6h0v1l-18-1s0 1-1 2c0 1-1 3-2 4l-1 1 2 1h-1v1c1 0 1 1 2 2h-1c-1 0-1-1-2-1v-2c-1 0-1 1-2 2l-1-2c-1 2-2 3-2 5 0 1-1 3 0 4h0v1h1v1 1l-2 3h2l-1 2c0 1 0 2 1 2v1c-1 0-2 1-4 1h-1c-1 1-2 1-3 1h0l-2 1c-1-1-2-1-3-2h0c-3-1-7-6-8-8s-2-3-2-4l-2-3-1-4c-2-2-3-5-3-8-1 2-1 3-2 4-1-2-2-5-3-8l2 1h0z" class="a"></path><path d="M243 149h-1c0-2 0-5 1-7h1l1 1-2 3v2 1z" class="h"></path><path d="M258 144v-2h-4c-3 0-5 0-7-1l-4-1c0-2 2-5 3-7 2 1 4 1 6 3 2 1 5 4 8 4v-1c-1-1-2-1-3-2l-10-5 7-7 6 7c0 1 2 2 2 4l-1 3-1 1v1 2l-1-2h0c0 1 0 2-1 2v2-1z" class="K"></path><path d="M276 136l9-12 4 3 2 2 1 1c-1 1-2 2-3 2-2 2-9 5-10 8h0c5-3 10-5 14-9 1 2 3 7 3 9h0c0 1 1 1 1 2h-1l1 1v6h0l-18-2c1-4 0-6-3-9l1-1-1-1z" class="h"></path><path d="M284 142h3v1h1-2-1l-1-1z" class="G"></path><path d="M287 142l2-1v1h0l2 1h-3-1v-1z" class="U"></path><path d="M289 127l2 2-3 1h-1l2-3z" class="O"></path><path d="M286 143c-2 1-6 1-7 1 2-1 3-2 5-2l1 1h1z" class="a"></path><path d="M289 141l7-1c0 1 1 1 1 2h-1l-5 1-2-1h0v-1z" class="T"></path><path d="M263 135c-3-3-6-7-8-11 2-1 5-2 8-3l2 8c1 1 1 3 2 5h0l1-1c-2-4-2-8-4-13 7-1 14 0 20 4-2 3-4 7-7 9l-1 2v1l1 1-1 1c3 3 4 5 3 9l18 2v1l-18-1s0 1-1 2c0 1-1 3-2 4l-1 1 2 1h-1v1c1 0 1 1 2 2h-1c-1 0-1-1-2-1v-2c-1 0-1 1-2 2l-1-2c-1 2-2 3-2 5-1-1-1-4-1-6l-1 3v-6l1-6v-2l1-2v-1-2h0l-1-2h-1c-1-1-1-1-1-2l-3-1v1l-1-1z" class="K"></path><path d="M273 133c1 1 2 0 4 0h0l-1 2v1l1 1-1 1-3-3v-1-1z" class="r"></path><path d="M271 145l2-2c0 3-1 6-3 8 0-2 0-4 1-6z" class="AC"></path><path d="M264 135c2 0 4 0 5 1l2 2-1 2h0 0l-1-2h-1c-1-1-1-1-1-2l-3-1z" class="U"></path><path d="M270 140h0l1-2c2 2 2 3 2 5l-2 2h-2l1-2v-1-2z" class="I"></path><path d="M273 134h-1v-1c0-2 1-11 3-13v1c1 2-1 10-2 12v1zm-4 13v-2h2c-1 2-1 4-1 6 0 1-1 4-1 5l-1 3v-6l1-6z" class="U"></path><path d="M273 135l3 3c3 3 4 5 3 9l18 2v1l-18-1s0 1-1 2c0 1-1 3-2 4l-1 1 2 1h-1v1c1 0 1 1 2 2h-1c-1 0-1-1-2-1v-2c-1 0-1 1-2 2l-1-2c3-3 6-7 6-12 0-4-2-7-5-10h0z" class="f"></path><path d="M263 135l1 1v-1l3 1c0 1 0 1 1 2h1l1 2h0v2 1l-1 2v2l-1 6v6l1-3c0 2 0 5 1 6 0 1-1 3 0 4h0v1h1v1 1l-2 3h2l-1 2c0 1 0 2 1 2v1c-1 0-2 1-4 1h-1c-1 1-2 1-3 1h0l-2 1c-1-1-2-1-3-2h0c-3-1-7-6-8-8s-2-3-2-4l-2-3-1-4c-2-2-3-5-3-8v-1h4c2 0 10-1 11-2-3 1-6 0-9 0-2 1-4 1-5 1v-1-2l2-3-1-1c4 1 7 2 11 2h3v1-2c1 0 1-1 1-2h0l1 2v-2-1l1-1h0c1-1 2-2 2-4z" class="p"></path><path d="M248 148h1c1-1 1 0 3 0h5 0c-3 1-6 0-9 0z" class="K"></path><path d="M245 143l1 1v1c0 1 0 1-1 2h-1c-1 0-1-1-1-1l2-3z" class="H"></path><path d="M267 136c0 1 0 1 1 2l-2 2h-3l1-2c1-1 1-2 3-2z" class="AQ"></path><path d="M263 161c0-1 0-4-1-5v-2c-1-3-1-7 0-10v1c1 5 2 11 2 16l-1 1v-1z" class="AB"></path><path d="M268 138h1l1 2h0v2 1c-1 0-2 1-3 1s-3 0-3-1c-1-1-1-1-1-3h0 3l2-2z" class="s"></path><path d="M268 138h1l1 2-1 1c-1 0-2 0-3-1l2-2z" class="AH"></path><path d="M245 159h0c1 0 1 0 2 1 2-2 5-5 8-5 1 0 3 1 3 1v1c-1 0-1 1-2 1h-1s-1-1-2 0c-1 0-4 2-5 3s-1 1-1 2h-1l-1-4z" class="n"></path><path d="M262 145l1-1 1 2 1 11v5h-1l-1 1-1 1h-2c-2-1-4-2-5-4v-2h1c1 0 2 0 3 1 0 1 1 1 1 2 1 1 1 0 3 0v1l1-1c0-5-1-11-2-16z" class="a"></path><path d="M264 146v-1h0 3c1 0 1 1 2 1v1l-1 6v6c1 2 0 4 0 6v3l-1 1c-1 1 0 2-1 2h-1-1l-1-1h1 1v-2l-1 1h-1l1-1-1-1c1-1 1-2 1-3h-2l1-1 1-1h1v-5l-1-11z" class="r"></path><path d="M265 157c1 3 0 7 0 10l-1 1-1-1c1-1 1-2 1-3h-2l1-1 1-1h1v-5z" class="p"></path><path d="M268 153v6c1 2 0 4 0 6v3l-1 1c-1 1 0 2-1 2h-1c1-2 2-5 2-7 0-4 0-7 1-11z" class="a"></path><path d="M268 159l1-3c0 2 0 5 1 6 0 1-1 3 0 4h0v1h1v1 1l-2 3h2l-1 2c0 1 0 2 1 2v1c-1 0-2 1-4 1h-1c-1 1-2 1-3 1h0l-2 1c-1-1-2-1-3-2h0c1-1 1-1 2-1v-2-2c1-1 2-2 4-2h1 1c1 0 0-1 1-2l1-1v-3c0-2 1-4 0-6z" class="b"></path><path d="M269 172h2l-1 2c0 1 0 2 1 2v1c-1 0-2 1-4 1h-1c-1 1-2 1-3 1h0l-2 1c-1-1-2-1-3-2h0c1-1 1-1 2-1s1 0 2-1l1-1h1c2 0 4-2 5-3z" class="H"></path><path d="M269 172h2l-1 2c-2 1-3 2-5 2l-1-1c2 0 4-2 5-3z" class="I"></path><path d="M260 177c1 0 1 0 2-1l1 2h3c-1 1-2 1-3 1h0l-2 1c-1-1-2-1-3-2h0c1-1 1-1 2-1z" class="n"></path><path d="M246 163h1c0-1 0-1 1-2s4-3 5-3c1-1 2 0 2 0v2c1 2 3 3 5 4h2 2c0 1 0 2-1 3l1 1-1 1h1l1-1v2h-1-1l1 1c-2 0-3 1-4 2v2 2c-1 0-1 0-2 1-3-1-7-6-8-8s-2-3-2-4l-2-3z" class="K"></path></svg>