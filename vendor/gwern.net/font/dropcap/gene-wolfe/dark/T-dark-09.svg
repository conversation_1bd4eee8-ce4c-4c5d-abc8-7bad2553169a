<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="157 88 729 836"><!--oldViewBox="0 0 1024 994"--><style>.B{fill:#c9c8c8}.C{fill:#1c1c1c}.D{fill:#939292}.E{fill:#d4d3d3}.F{fill:#717071}.G{fill:#4c4b4c}.H{fill:#a9a7a7}.I{fill:#333233}.J{fill:#f5f4f3}.K{fill:#616060}.L{fill:#ebeaea}</style><path d="M358 248l5 3-5 1v-1-3z" class="C"></path><path d="M468 225l2 2h3c0 1-1 2-2 3h0c-1-1-2-1-3-1s-1 0-2-1l2-3z" class="E"></path><path d="M460 240l6 2c-1 2-2 3-2 5-2 0-4-1-6-2l2-5z" class="F"></path><path d="M547 214l5 7c0 2 0 4-2 5l-5-4c0-1-1-1-1-2 1 0 1 0 2 1l1 1c1 0 2-1 3-2l-3-4v-2z" class="B"></path><path d="M474 233c1 1 3 2 4 3 0 1 0 1 1 2l-2 4-5-4 2-5z" class="D"></path><path d="M561 237l3 7c-1 1-3 1-4 0h-1l-2-5c1-1 2-2 3-2s0 1 1 0z" class="F"></path><path d="M469 245c0-2 1-4 3-7l5 4-2 4h-5l-1-1z" class="G"></path><path d="M759 257l2 6c-3 0-6 0-9-1-1-2-2-3-3-4h1c3 0 6 1 9-1zm-485 0l-2 4-10 1c0-2 1-4 2-5 2 1 7 1 10 0zm107-12l2 1h0c1 2 1 3 3 4h1c-6 5-13 4-20 5 4-2 8-2 12-4 2-1 3-3 3-4l-1-2zm256 5c2 0 6 2 8 1h0c3 2 7 4 12 6h-1-1c-6 1-13-3-18-7z" class="C"></path><path d="M458 245c2 1 4 2 6 2l-2 6c-1 0-2 1-3 0h-1c-2 0-2 0-3-1l3-7z" class="I"></path><defs><linearGradient id="A" x1="318.203" y1="235.627" x2="317.804" y2="229.979" xlink:href="#B"><stop offset="0" stop-color="#7d7b7b"></stop><stop offset="1" stop-color="#939292"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M315 226c1 1 0 3 1 4h5v-5-1c1 5 1 11-1 17v-5h-4c-1-1-1-3-1-4v-6z"></path><path d="M583 252c1 3 2 6 2 9-2 0-4 1-6-1-1-1-2-3-3-5h2v-1l1-1 2 1 2-2z" class="C"></path><path d="M564 244c3 3 4 8 6 12h0s-1-1-1-2c-1-1-1-1-1-2l-1-1h-4v1h-1l1 2v1c-2-2-2-5-3-8-1-1-1-2-1-3h1c1 1 3 1 4 0z" class="I"></path><path d="M415 255c-2 2-5 3-7 4s-7 2-10 1h0l6-7c2 1 3 2 5 2v1c2 0 4 0 6-1zm301-5c3 2 5 3 8 4l2-1h0c2 3 3 6 5 9-1-1-2-1-3-1-2-1-3-1-5-2-3-1-6-5-7-9z" class="C"></path><defs><linearGradient id="C" x1="424.575" y1="258.361" x2="431.858" y2="249.805" xlink:href="#B"><stop offset="0" stop-color="#0d0b0d"></stop><stop offset="1" stop-color="#323131"></stop></linearGradient></defs><path fill="url(#C)" d="M427 248c3-1 5-1 7-2-1 4-3 12-7 15h-1c-1-1 0-4 0-6 1-2 1-5 1-7z"></path><path d="M462 253c0 1-3 6-4 7-1 2-6 1-8 1 1-3 3-6 5-9 1 1 1 1 3 1h1c1 1 2 0 3 0z" class="C"></path><path d="M477 213c0 3-2 5-4 7l4 2 1-1c1 0 1 0 2-1v1h5v-1l1-3 2-2-3 7c-1 1-2 0-4 1h0l-1-1h-1c-2 2-4 3-6 5h-3l-2-2 9-12z" class="B"></path><path d="M619 254l7 8c-8-1-14-2-20-8l4 1c1-1 2-1 3-1l1 1c2 0 3 0 5-1z" class="C"></path><defs><linearGradient id="D" x1="634.563" y1="236.959" x2="620.558" y2="228.345" xlink:href="#B"><stop offset="0" stop-color="#6a6969"></stop><stop offset="1" stop-color="#9a9899"></stop></linearGradient></defs><path fill="url(#D)" d="M625 236c-2-3-4-6-4-10l1-1h3c1 2 1 3 2 5 1 6 5 8 10 10h-1-2c-1 0-2 0-4-1h0c-1 0-2 0-2-1l-3-2z"></path><defs><linearGradient id="E" x1="462.788" y1="239.916" x2="467.19" y2="231.573" xlink:href="#B"><stop offset="0" stop-color="#747272"></stop><stop offset="1" stop-color="#9f9d9d"></stop></linearGradient></defs><path fill="url(#E)" d="M466 228c1 1 1 1 2 1s2 0 3 1h0v1l-5 11-6-2 5-10 1-2z"></path><path d="M466 228c1 1 1 1 2 1s2 0 3 1h0v1c-3 0-4 0-6-1l1-2z" class="H"></path><defs><linearGradient id="F" x1="593.032" y1="260.514" x2="592.972" y2="247.489" xlink:href="#B"><stop offset="0" stop-color="#0a080a"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#F)" d="M587 245h1l2 2 1-1c1 0 3 0 4 1h1c-1 5 1 9 1 14l-1 1c0-1-1-1-1-1-1-1-2-2-2-3-2-4-4-9-6-13z"></path><path d="M391 238c3-2 5-4 6-8v-5h5c0 5-2 10-5 14-2 0-4 0-6-1z" class="D"></path><defs><linearGradient id="G" x1="477.268" y1="235.305" x2="480.557" y2="222.981" xlink:href="#B"><stop offset="0" stop-color="#939292"></stop><stop offset="1" stop-color="#c3c1c1"></stop></linearGradient></defs><path fill="url(#G)" d="M479 222h1l1 1h0c2-1 3 0 4-1l-6 16c-1-1-1-1-1-2-1-1-3-2-4-3 2-3 4-7 5-11z"></path><defs><linearGradient id="H" x1="558.185" y1="237.327" x2="553.867" y2="226.553" xlink:href="#B"><stop offset="0" stop-color="#7b7a7b"></stop><stop offset="1" stop-color="#aaa8a9"></stop></linearGradient></defs><path fill="url(#H)" d="M552 221c4 5 8 10 9 16-1 1 0 0-1 0s-2 1-3 2c-2-4-4-9-7-13 2-1 2-3 2-5z"></path><defs><linearGradient id="I" x1="315.692" y1="245.485" x2="317.482" y2="235.546" xlink:href="#B"><stop offset="0" stop-color="#4e4d4e"></stop><stop offset="1" stop-color="#7d7b7c"></stop></linearGradient></defs><path fill="url(#I)" d="M315 232c0 1 0 3 1 4h4v5l-2 7h-2-2-1l-1-2c2-5 2-9 3-14z"></path><defs><linearGradient id="J" x1="300.001" y1="259.677" x2="298.944" y2="248.236" xlink:href="#B"><stop offset="0" stop-color="#0c0a0b"></stop><stop offset="1" stop-color="#3e3e3e"></stop></linearGradient></defs><path fill="url(#J)" d="M299 252c1-1 2-2 2-4 2 0 3 0 4 1v1h1l2-2c-2 4-5 11-10 13l-2 1h-1-2c0-2 5-8 6-10z"></path><defs><linearGradient id="K" x1="699.163" y1="244.592" x2="693.63" y2="236.09" xlink:href="#B"><stop offset="0" stop-color="#424141"></stop><stop offset="1" stop-color="#696869"></stop></linearGradient></defs><path fill="url(#K)" d="M698 234c3 7 7 15 4 23-1 1-1 2-2 4h-2v-2h2c0-1 1-2 1-4v-1c1-2 1-4 1-6h-1v-1c0-1 0-2-1-3l-3 3h-1v2c1 1 1 2 0 3h0c0-5-2-9-4-14 2-2 4-3 6-4z"></path><defs><linearGradient id="L" x1="308.823" y1="261.404" x2="309.799" y2="247.347" xlink:href="#B"><stop offset="0" stop-color="#090809"></stop><stop offset="1" stop-color="#3a3939"></stop></linearGradient></defs><path fill="url(#L)" d="M318 248c-2 3-3 6-6 9-3 4-5 5-10 5l10-16 1 2h1 2 2z"></path><defs><linearGradient id="M" x1="270.118" y1="253.125" x2="271.461" y2="246.486" xlink:href="#B"><stop offset="0" stop-color="#3e3d3d"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#M)" d="M269 245l1 1 2-1h1 0c1 0 1 0 2 1v3h-1c0 2 0 2 1 3l-1 2v3c-3 1-8 1-10 0l1-3 4-9z"></path><path d="M265 254c1 0 2 1 4 0v1c2 0 3 0 5-1v3c-3 1-8 1-10 0l1-3z" class="I"></path><defs><linearGradient id="N" x1="705.467" y1="240.971" x2="704.771" y2="229.858" xlink:href="#B"><stop offset="0" stop-color="#757374"></stop><stop offset="1" stop-color="#9f9d9d"></stop></linearGradient></defs><path fill="url(#N)" d="M702 226l1 4h3v-1l2-2v-1c0 6 0 11 2 17-1 1-3 2-4 3l-1 1c-3-7-4-13-3-21z"></path><defs><linearGradient id="O" x1="544.307" y1="230.614" x2="540.334" y2="219.654" xlink:href="#B"><stop offset="0" stop-color="#a8a6a6"></stop><stop offset="1" stop-color="#d2d0d0"></stop></linearGradient></defs><path fill="url(#O)" d="M533 211h1l3 6 1 2c0 1 0 0 1 1h3l1-1s1 0 1 1 1 1 1 2c1 4 3 8 4 11l-1 1h-1-2c-1 0-2 0-3-1l-9-22z"></path><defs><linearGradient id="P" x1="703.574" y1="229.75" x2="706.955" y2="215.222" xlink:href="#B"><stop offset="0" stop-color="#a4a2a2"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#P)" d="M710 206v1c0 2-3 5-4 8 1 0 2 1 4 1 0-1 1-3 2-4v-1c1-2 2-2 4-3-5 6-6 11-8 18v1l-2 2v1h-3l-1-4c1-8 3-14 8-20z"></path><defs><linearGradient id="Q" x1="466.237" y1="261.169" x2="469.053" y2="249.646" xlink:href="#B"><stop offset="0" stop-color="#070606"></stop><stop offset="1" stop-color="#313030"></stop></linearGradient></defs><path fill="url(#Q)" d="M475 246l-6 15c-1 1-2 1-4 1-1 0-1 0-2-1 0-1 1-3 1-5 2-4 3-7 5-11l1 1h5z"></path><defs><linearGradient id="R" x1="714.12" y1="261.293" x2="713.332" y2="246.819" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#R)" d="M710 243l1 3c4 6 7 12 11 17-1 0-5-1-7-1-1-2-4-5-5-7l-4-6-1-2 1-1c1-1 3-2 4-3z"></path><path d="M710 243l1 3c-1 2-2 2-4 3h-1l-1-2 1-1c1-1 3-2 4-3z" class="G"></path><defs><linearGradient id="S" x1="375.191" y1="238.109" x2="381.832" y2="226.475" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#979695"></stop></linearGradient></defs><path fill="url(#S)" d="M383 223c-1 5-1 10-1 15h-1-2-1c-1 0-1 0-2 1h-3v-1h-1c1-1 1-2 2-4s1-6 0-8l9-3z"></path><defs><linearGradient id="T" x1="456.713" y1="233.032" x2="458.214" y2="222.957" xlink:href="#B"><stop offset="0" stop-color="#a09e9e"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#T)" d="M451 232c2-6 8-14 13-19v1l-6 8v1l6 1 1-1 4-6c1-1 2-2 3-2h0c-2 3-5 7-7 10s-4 6-6 10l-2-1h0c-1-1-2-1-3-2-1 0-1 0-2 1h-1v-1z"></path><defs><linearGradient id="U" x1="275.028" y1="246.943" x2="276.082" y2="238.267" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#8d8b8c"></stop></linearGradient></defs><path fill="url(#U)" d="M272 234l1 2s1-1 2-1c0-1 1-1 2-1h1 0c1-1 2-1 3 0 1 0 1-1 2-1l-8 19c-1-1-1-1-1-3h1v-3c-1-1-1-1-2-1h0-1l-2 1-1-1 3-8h0v-3z"></path><path d="M272 237h0c2 0 4-1 5-1s1-1 3-1v1 1 1h-1-1c-1 0-2 0-3 1h-2-1 0v-2h0z" class="D"></path><path d="M385 240l1 1c2-1 3-2 4-3h1c2 1 4 1 6 1-2 3-4 6-7 9l-3 2h-1c-2-1-2-2-3-4h0l-2-1-2-1-6 3-1-1v-1c2-2 9-3 13-4v-1z" class="G"></path><path d="M379 244h5 1c1 2 3 3 5 4l-3 2h-1c-2-1-2-2-3-4h0l-2-1-2-1z" class="I"></path><defs><linearGradient id="V" x1="214.806" y1="367.673" x2="213.273" y2="379.235" xlink:href="#B"><stop offset="0" stop-color="#666"></stop><stop offset="1" stop-color="#949494"></stop></linearGradient></defs><path fill="url(#V)" d="M211 364v3c2 0 3 1 4 1h1l3 1h1v-1h1l-3 13c-2-2-4-1-6-2h-4l3-15z"></path><defs><linearGradient id="W" x1="303.832" y1="247.971" x2="304.714" y2="242.864" xlink:href="#B"><stop offset="0" stop-color="#3f3e3e"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#W)" d="M304 229c0 1 1 2 1 3 3 0 5 0 7 1v2c-1 5-2 9-4 13l-2 2h-1v-1c-1-1-2-1-4-1 0 2-1 3-2 4 4-8 5-14 5-23z"></path><path d="M305 232c3 0 5 0 7 1v2c-1 0-1 1-1 1-2-1-3-1-5-1h-1v-3z" class="D"></path><defs><linearGradient id="X" x1="744.466" y1="237.167" x2="742.349" y2="224.686" xlink:href="#B"><stop offset="0" stop-color="#a6a4a4"></stop><stop offset="1" stop-color="#d5d3d3"></stop></linearGradient></defs><path fill="url(#X)" d="M733 220c1 1 3 3 4 5v1l2-1 7-1v-2-1-3h-1 1l4 14c0 1-1 2-1 3v1h0c-3 0-5 1-7 3-3-7-5-13-10-19h1z"></path><defs><linearGradient id="Y" x1="362.015" y1="225.934" x2="379.794" y2="215.071" xlink:href="#B"><stop offset="0" stop-color="#b1b0b1"></stop><stop offset="1" stop-color="#dad8d8"></stop></linearGradient></defs><path fill="url(#Y)" d="M364 220l-1 1c2 1 4 2 7 2 2-1 3-2 4-4 3 0 6 0 9-1h1l-1 5-9 3-9 4c-1-2-2-4-4-6v-1-1l-1-1h-2-2 1l2-2c2 0 3 0 5 1z"></path><defs><linearGradient id="Z" x1="691.211" y1="258.877" x2="698.102" y2="242.692" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#Z)" d="M696 252h0c1-1 1-2 0-3v-2h1l3-3c1 1 1 2 1 3v1h1c0 2 0 4-1 6v1c0 2-1 3-1 4h-2v2h-5c-3-1-5-2-6-5-1-2-2-4-1-6 1-1 1-1 2-1 2 2 2 3 3 5 1 1 1 1 2 1 2-1 2-1 3-3z"></path><defs><linearGradient id="a" x1="668.953" y1="245.22" x2="669.992" y2="235.779" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#a)" d="M663 231c3 0 6 1 9 2l4 11c-1 1-1 2-3 2-1 0-5-2-7-1v1c1 0 1 1 1 2-3-5-4-11-4-17z"></path><path d="M349 240c1-2 1-3 2-4 3 0 6 0 9 1l-1 5-3 8h-1l1-1c0-1-1-2-1-4h-2c-1 0-2 0-2 1-1 1-1 1-2 1-1 1-2 2-3 2h-1c0 1 0 2 1 2v1 1l-6-12 6 3 1-1 1 2 1-5z" class="I"></path><path d="M349 240c1-2 1-3 2-4 3 0 6 0 9 1l-1 5h0c-2-1-2-1-3-2l-1-1h-2-1v1h-1-2z" class="G"></path><defs><linearGradient id="b" x1="607.879" y1="367.745" x2="609.004" y2="380.443" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#b)" d="M602 366h9c3 5 4 13 5 19l-2-2h0v-2h-7c1 1 1 3 1 5-2-4-3-8-4-12-1-3-2-5-2-8z"></path><defs><linearGradient id="c" x1="714.562" y1="233.803" x2="716.359" y2="218.601" xlink:href="#B"><stop offset="0" stop-color="#aaa9a9"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#c)" d="M711 234c0-4 0-8 1-12 0-2 1-5 3-7 2-4 4-6 8-7h0c-1 1-2 1-3 2-2 1-4 4-5 6l-1 2h1c2 1 3 1 5 2 2-1 4-4 6-4h2 1l4 4h-1c-1-1-3-3-5-3s-3 1-4 2c-2 2-3 6-4 10-1 1-1 2-2 4-2 1-4 1-6 1z"></path><defs><linearGradient id="d" x1="279.472" y1="234.563" x2="280.58" y2="221.833" xlink:href="#B"><stop offset="0" stop-color="#a7a5a5"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#d)" d="M278 212c1 3-1 7-1 10h2 4 5l1-2 1 1c-3 4-5 7-7 12-1 0-1 1-2 1-1-1-2-1-3 0h0-1c-1 0-2 0-2 1-1 0-2 1-2 1l-1-2 6-22z"></path><path d="M517 797h0c0 2 1 5 0 8-2 2-1 11-1 14l-1 30c-1-3 0-9 0-13v1c0 4 0 9-1 14s1 10 0 15c-1-14-1-28 0-42 0-6 0-12-1-18-1-4-4-6-7-9 3 0 8 3 10 3l1-3z" class="C"></path><defs><linearGradient id="e" x1="715.731" y1="239.125" x2="714.503" y2="233.457" xlink:href="#B"><stop offset="0" stop-color="#8c8b8a"></stop><stop offset="1" stop-color="#a7a5a5"></stop></linearGradient></defs><path fill="url(#e)" d="M711 234c2 0 4 0 6-1 1-2 1-3 2-4 0 10 2 16 7 24l-2 1c-3-1-5-2-8-4-3-5-4-10-5-16z"></path><defs><linearGradient id="f" x1="552.498" y1="251.406" x2="550.08" y2="239.477" xlink:href="#B"><stop offset="0" stop-color="#2c2b2b"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#f)" d="M549 233l12 26h-1 0l-1-2v-1l-1-3-1-1-1-1-2 1h-3l1 1v1c1 1 1 4 3 6l-1 1c-2-3-3-7-4-10-3-6-6-12-8-18 1 1 2 1 3 1h2 1l1-1z"></path><defs><linearGradient id="g" x1="213.297" y1="379.04" x2="210.433" y2="392.755" xlink:href="#B"><stop offset="0" stop-color="#969595"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#g)" d="M208 379h4c2 1 4 0 6 2l-3 26c-1-2-1-5-1-6 1-1 1-3 1-4 1-1 0-2 0-3-1 0-3 0-4-1s-2-1-4-1c-1 3-1 8-2 11v1c0-8 2-17 3-25z"></path><defs><linearGradient id="h" x1="673.148" y1="258.591" x2="672.172" y2="244.085" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#3d3c3c"></stop></linearGradient></defs><path fill="url(#h)" d="M676 244h0c1-3 2-6 2-9 0 1 0 3 1 4 2 1 3 1 4 1h1c-4 7-7 13-9 21h0c-3-4-6-7-8-11v-2c0-1 0-2-1-2v-1c2-1 6 1 7 1 2 0 2-1 3-2z"></path><defs><linearGradient id="i" x1="306.8" y1="230.9" x2="304.977" y2="216.315" xlink:href="#B"><stop offset="0" stop-color="#a9a7a7"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#i)" d="M292 209c4-1 7-2 10 0 5 2 7 6 8 11 2 4 2 8 2 13-2-1-4-1-7-1 0-1-1-2-1-3-1-4-1-7-4-10 0-1-2-2-3-2-3 0-5 2-7 4l-1-1c2-2 4-3 6-4h1 3v1h1 0c3 0 5 0 8-1l-1-1c0-1-1-2-1-2l-1-1c0-1-1-2-2-2l-1-1c-2-1-3-1-6 0h-4z"></path><defs><linearGradient id="j" x1="364.918" y1="249.707" x2="368.582" y2="236.293" xlink:href="#B"><stop offset="0" stop-color="#313031"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#j)" d="M382 238l3 2v1c-4 1-11 2-13 4v1l1 1-10 4-5-3 2-5c3-2 7-3 11-4l1-1h1v1h3c1-1 1-1 2-1h1 2 1z"></path><path d="M373 239h3 1c-2 1-3 1-5 0h1z" class="K"></path><defs><linearGradient id="k" x1="612.798" y1="380.238" x2="613.736" y2="401.012" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#k)" d="M608 386c0-2 0-4-1-5h7v2h0l2 2 5 14-2 2-6 1-5-16z"></path><defs><linearGradient id="l" x1="355.671" y1="230.622" x2="354.737" y2="222.793" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#a2a1a1"></stop></linearGradient></defs><path fill="url(#l)" d="M351 218v2c1 0 1 0 1-1 2-1 5 0 7 0l-2 2h-1 2 2l1 1v1 1c-1 2-1 5-1 7v6c-3-1-6-1-9-1v-4l-2-7c0-3 0-5 2-7z"></path><path d="M360 221l1 1v1h0-2c-1 0-3 0-4-1h4l1-1z" class="H"></path><path d="M351 232l1 2h1c2-1 4-2 7-3v6c-3-1-6-1-9-1v-4z" class="F"></path><defs><linearGradient id="m" x1="574.144" y1="248.985" x2="574.365" y2="236.015" xlink:href="#B"><stop offset="0" stop-color="#403f40"></stop><stop offset="1" stop-color="#777676"></stop></linearGradient></defs><path fill="url(#m)" d="M574 234l9 18-2 2-2-1-1 1v1h-2l-9-16 1-1c1-2 2-2 4-3 1 0 1-1 2-1z"></path><defs><linearGradient id="n" x1="388.82" y1="259.983" x2="388.591" y2="248.629" xlink:href="#B"><stop offset="0" stop-color="#090809"></stop><stop offset="1" stop-color="#363535"></stop></linearGradient></defs><path fill="url(#n)" d="M407 247c-6 7-12 13-21 14-3 1-5 0-8 0 8-4 14-10 20-16 1 2 4 1 6 2h3z"></path><defs><linearGradient id="o" x1="315.088" y1="230.152" x2="312.942" y2="203.226" xlink:href="#B"><stop offset="0" stop-color="#969494"></stop><stop offset="1" stop-color="#f7f6f6"></stop></linearGradient></defs><path fill="url(#o)" d="M304 205c2 0 2 1 3 2l2-2h0l1-1c1 1 2 1 3 1 5 6 7 12 8 19v1 5h-5c-1-1 0-3-1-4-2-9-5-16-11-21z"></path><defs><linearGradient id="p" x1="423.877" y1="235.041" x2="442.045" y2="207.576" xlink:href="#B"><stop offset="0" stop-color="#b1afb0"></stop><stop offset="1" stop-color="#f4f3f2"></stop></linearGradient></defs><path fill="url(#p)" d="M411 231c3-3 6-4 9-6 6-4 12-7 18-9 4-2 8-2 13-3 3-1 6-2 9-2-6 3-13 5-20 8-6 3-12 8-18 13l-1-1c-2 0-4-1-6-1-1 0-3 0-4 1z"></path><defs><linearGradient id="q" x1="410.792" y1="239.477" x2="412.9" y2="229.075" xlink:href="#B"><stop offset="0" stop-color="#8c8a8b"></stop><stop offset="1" stop-color="#b5b3b3"></stop></linearGradient></defs><path fill="url(#q)" d="M411 231c1-1 3-1 4-1 2 0 4 1 6 1l1 1c-5 5-10 9-15 15h-3c-2-1-5 0-6-2 3-5 7-11 13-14z"></path><defs><linearGradient id="r" x1="562.506" y1="229.732" x2="560.43" y2="216.356" xlink:href="#B"><stop offset="0" stop-color="#afaeae"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#r)" d="M555 208c7 7 12 16 17 24l2 2c-1 0-1 1-2 1-2 1-3 1-4 3l-1 1-4-6c-4-8-11-15-15-24 1 1 2 3 3 5l3 4 6-2s0-1-1-1l-2-4c-1-1-2-1-2-3h0z"></path><path d="M572 232l2 2c-1 0-1 1-2 1-2 1-3 1-4 3l-1 1-4-6c3 0 7 0 9-1z" class="D"></path><defs><linearGradient id="s" x1="747.726" y1="241.793" x2="746.18" y2="236.377" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#a6a4a4"></stop></linearGradient></defs><path fill="url(#s)" d="M742 239c2-2 4-3 7-3h0v-1c0-1 1-2 1-3l8 22 1 3c-3 2-6 1-9 1h-1c0-2-1-3-1-5l-3-8h-1c-1-2-1-4-2-6z"></path><path d="M744 245h1c1 0 2 0 3-1l1 1c-2 0-3 1-4 0h-1z" class="F"></path><path d="M748 253c2 1 8 3 9 2l1-1 1 3c-3 2-6 1-9 1h-1c0-2-1-3-1-5z" class="I"></path><defs><linearGradient id="t" x1="588.62" y1="238.078" x2="585.946" y2="221.332" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#bcbbbb"></stop></linearGradient></defs><path fill="url(#t)" d="M585 239c-4-8-8-14-14-19 7 3 14 5 21 9 4 3 8 7 12 10-2 1-4 0-6 0-4 0-9 1-13 0z"></path><defs><linearGradient id="u" x1="594.562" y1="234.99" x2="590.965" y2="210.692" xlink:href="#B"><stop offset="0" stop-color="#aaa8a8"></stop><stop offset="1" stop-color="#dddbdc"></stop></linearGradient></defs><path fill="url(#u)" d="M603 233c-7-6-15-12-23-15-5-3-11-4-16-7 6 1 14 2 20 4 3 1 6 3 9 4 6 3 12 7 18 11 1 1 2 2 3 2h-2l-2-1v1c-1 1-2 1-4 1h-3z"></path><defs><linearGradient id="v" x1="329.424" y1="259.884" x2="327.129" y2="241.546" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#3b3a3a"></stop></linearGradient></defs><path fill="url(#v)" d="M324 237h6v1 1 1c-2 4-3 7-2 11 0 1 1 3 2 4 3-1 4-3 6-5v-1h1c0 2 0 3-1 4 0 4-3 6-6 8-1 1-5 0-7 0-1-1-2-2-2-3-1-5-2-13 1-17l2-4z"></path><path d="M324 237h6v1 1 1l-2 1v1h-6v-1l2-4z" class="G"></path><defs><linearGradient id="w" x1="451.215" y1="240.064" x2="451.304" y2="233.511" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#979695"></stop></linearGradient></defs><path fill="url(#w)" d="M451 232v1h1c1-1 1-1 2-1 1 1 2 1 3 2h0l2 1-9 15c-2 2-3 4-4 6h-2c-1-1-1-1-2-1-2 0-2 0-4-1 0-4 10-18 13-22z"></path><defs><linearGradient id="x" x1="531.167" y1="888.223" x2="508.915" y2="873.155" xlink:href="#B"><stop offset="0" stop-color="#3e3f3f"></stop><stop offset="1" stop-color="#6f6d6d"></stop></linearGradient></defs><path fill="url(#x)" d="M518 865c1-4 0-7 0-11 0 2 0 4 1 5 1 0 2-1 3-2v-1c0-2 0-3 1-4l-1 12c0 3 1 7 2 10h1c-1 7-2 14-2 20v8c-1 2-3 4-4 6l-1-43z"></path><defs><linearGradient id="y" x1="417.585" y1="251.85" x2="417.891" y2="245.649" xlink:href="#B"><stop offset="0" stop-color="#383737"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#y)" d="M415 243l1 1s1 0 2-1c2-1 4-1 7-2h2 1c3-1 6-1 9-1l-3 6c-2 1-4 1-7 2-5 2-8 4-12 7-2 1-4 1-6 1v-1c-2 0-3-1-5-2l11-10z"></path><defs><linearGradient id="z" x1="434.996" y1="236.593" x2="434.507" y2="223.465" xlink:href="#B"><stop offset="0" stop-color="#8e8d8d"></stop><stop offset="1" stop-color="#c0bfbe"></stop></linearGradient></defs><path fill="url(#z)" d="M415 243c6-6 13-12 20-16 6-3 12-5 18-7-1 2-4 4-6 5-3 4-6 8-8 12h0l-1 1-1 2c-3 0-6 0-9 1h-1-2c-3 1-5 1-7 2-1 1-2 1-2 1l-1-1z"></path><path d="M438 238h-3v1c-3-2-10-1-13-1 3-1 7-2 11-1h6l-1 1z" class="D"></path><defs><linearGradient id="AA" x1="607.856" y1="249.727" x2="608.146" y2="243.725" xlink:href="#B"><stop offset="0" stop-color="#3d3c3d"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#AA)" d="M585 239c4 1 9 0 13 0 2 0 4 1 6 0l3 3 12 12c-2 1-3 1-5 1l-1-1c-1 0-2 0-3 1l-4-1c-3-3-6-5-10-7h-1c-1-1-3-1-4-1l-1 1-2-2h-1v-1l-1-3-1-2z"></path><path d="M587 244h0c5 1 10 0 15 1v1h-3c-3-1-8-1-11-1h-1v-1z" class="G"></path><path d="M585 239c4 1 9 0 13 0 2 0 4 1 6 0l3 3h-1-1v1h0-3-1l2-1c-6 0-12 1-17-1l-1-2z" class="K"></path><defs><linearGradient id="AB" x1="614.128" y1="238.578" x2="613.873" y2="232.443" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#a1a09f"></stop></linearGradient></defs><path fill="url(#AB)" d="M611 230c11 6 15 17 24 24-1 1-1 1-2 0-1 0-2 1-3 0-2 0-2 0-3-1l-1 1-1-1v2c-3-1-6-5-8-7-5-5-9-10-14-15h3c2 0 3 0 4-1v-1l2 1h2c-1 0-2-1-3-2z"></path><defs><linearGradient id="AC" x1="796.151" y1="336.632" x2="807.717" y2="360.132" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#AC)" d="M796 345l6-14c1 2 1 4 2 7 2 7 6 16 7 23v1c-1 1-1 1 0 2l-1 1h0c-1 0-2 0-3 1l-4 1h-1 0l-1-1-3-15c-1-2 0-5-2-6z"></path><path d="M801 366l1-3 6-3c1 0 2 1 3 1v1c-1 1-1 1 0 2l-1 1h0c-1 0-2 0-3 1l-4 1h-1 0l-1-1z" class="K"></path><defs><linearGradient id="AD" x1="689.004" y1="237.635" x2="684.493" y2="225.23" xlink:href="#B"><stop offset="0" stop-color="#6a6969"></stop><stop offset="1" stop-color="#989697"></stop></linearGradient></defs><path fill="url(#AD)" d="M681 220c1 0 1-1 2-2 0-1 0-2-1-3s-1-2-1-3l1-1 3 6c1 1 2 1 3 2v1h1c0-1-1-3-1-4 2 1 3 3 4 5 3 4 5 4 5 9l1 4c-2 1-4 2-6 4l-2-6c-3 3-4 5-6 8h-1c-1 0-2 0-4-1-1-1-1-3-1-4 0-3-1-5 0-7h1c1 1 1 1 3 2 1-1 2-1 3-2l-4-8z"></path><path d="M688 216c2 1 3 3 4 5 3 4 5 4 5 9-5-2-9-8-12-13 1 1 2 1 3 2v1h1c0-1-1-3-1-4z" class="B"></path><defs><linearGradient id="AE" x1="506.806" y1="839.929" x2="530.836" y2="824.797" xlink:href="#B"><stop offset="0" stop-color="#6a696c"></stop><stop offset="1" stop-color="#a6a4a3"></stop></linearGradient></defs><path fill="url(#AE)" d="M518 820v-4c1-2 0-8 1-10 1 2 1 5 1 8 1 0 1 1 1 1 0 2 1 3 2 5h1l1-4c0 3 0 18 1 19-2 6-2 11-3 17-1 1-1 2-1 4v1c-1 1-2 2-3 2-1-1-1-3-1-5 0 4 1 7 0 11-1-5 0-11 0-16v-29z"></path><defs><linearGradient id="AF" x1="809.609" y1="376.982" x2="809.859" y2="392.085" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#cfcecd"></stop></linearGradient></defs><path fill="url(#AF)" d="M811 362c1 3 2 8 2 11l4 29h-1c1-3-1-6-1-9h1-9v2 1 3 1s1 1 1 2l-1 1-3-22c-1-4-1-9-2-14h0 1l4-1c1-1 2-1 3-1h0l1-1c-1-1-1-1 0-2z"></path><defs><linearGradient id="AG" x1="805.897" y1="365.659" x2="808.876" y2="377.418" xlink:href="#B"><stop offset="0" stop-color="#686768"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#AG)" d="M811 362c1 3 2 8 2 11v4c-2 0-4 0-6 1h-2s-1 1-1 2 1 0 0 1c-1-4-1-9-2-14h0 1l4-1c1-1 2-1 3-1h0l1-1c-1-1-1-1 0-2z"></path><defs><linearGradient id="AH" x1="226.814" y1="331.938" x2="214.749" y2="361.905" xlink:href="#B"><stop offset="0" stop-color="#0e0d0e"></stop><stop offset="1" stop-color="#4d4c4d"></stop></linearGradient></defs><path fill="url(#AH)" d="M223 328l4 10c0 1 1 3 1 3 0 1-1 1-1 2-2 2-5 20-6 24v1h-1v1h-1l-3-1h-1c-1 0-2-1-4-1v-3-1l7-24c1-4 2-8 5-11z"></path><path d="M211 363l2-1c2 0 7 0 8 2v3 1h-1v1h-1l-3-1h-1c-1 0-2-1-4-1v-3-1z" class="K"></path><defs><linearGradient id="AI" x1="648.599" y1="252.444" x2="646.557" y2="235.738" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#727171"></stop></linearGradient></defs><path fill="url(#AI)" d="M641 229c2-1 3-1 5-2h1l1-1c0 2 1 4 1 6 1 3 2 6 5 7 2 1 5 1 8 2 0 0 2 6 3 7 0 1 1 3 1 4-4-1-8-2-12-4s-9-4-14-5v1c0 3 2 5 5 7-2 1-6-1-8-1-5-4-9-9-12-14l3 2c0 1 1 1 2 1h0c2 1 3 1 4 1h2 1l3-1c1-2 1-2 2-5l-1-5z"></path><path d="M641 229c2-1 3-1 5-2h1l1-1c0 2 1 4 1 6v3l-4 2h-3v-3l-1-5z" class="F"></path><defs><linearGradient id="AJ" x1="335.003" y1="238.017" x2="334.978" y2="224.497" xlink:href="#B"><stop offset="0" stop-color="#504f4f"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#AJ)" d="M360 203c-1 5-2 10-6 13l-3 2c-2 2-2 4-2 7l2 7v4c-1 1-1 2-2 4l-1 5-1-2-1 1-6-3-2-2-5-7-3 7v-1-1h-6c2-3 1-6 3-9 1-4 4-7 6-10s4-6 6-10v1c0 2-1 3-2 5h1l1-1c0-1 1-1 2-2 3-3 5-5 8-7h0v1l-6 6v1c-1 2-1 3-2 4 0 1-1 1-1 2l2 1c1-2-1 0 1-2 0-1 1-1 1-2l2-1c1-2 3-4 5-6 3-2 6-3 9-5z"></path><path d="M338 239l1-1h0l5 1c1 0 1 0 2-1l1 5-1 1-6-3-2-2z" class="G"></path><path d="M338 227l-1-1h0c0-5 3-11 6-15v1c-1 2-1 3-2 4 0 1-1 1-1 2l2 1c1-2-1 0 1-2 0-1 1-1 1-2l2-1-8 13z" class="B"></path><defs><linearGradient id="AK" x1="333.1" y1="224.113" x2="333.436" y2="214.889" xlink:href="#B"><stop offset="0" stop-color="#b4b0b1"></stop><stop offset="1" stop-color="#d7d6d4"></stop></linearGradient></defs><path fill="url(#AK)" d="M327 228c1-4 4-7 6-10s4-6 6-10v1c0 2-1 3-2 5h1l1-1c0-1 1-1 2-2l-6 11c-1 1-3 3-4 5-1 0-2 1-3 1h-1z"></path><path d="M360 203c-1 5-2 10-6 13l-3 2c-2 2-2 4-2 7l2 7v4c-1 1-1 2-2 4l-1 5-1-2-1-5c-1-2-1-8-2-9-2 0-4 0-6-1v-1l8-13c1-2 3-4 5-6 3-2 6-3 9-5z"></path><defs><linearGradient id="AL" x1="668.195" y1="231.519" x2="649.783" y2="203.586" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#eae8e8"></stop></linearGradient></defs><path fill="url(#AL)" d="M651 202c1 1 2 2 2 4 1 2 2 4 1 6v1c3 0 5-1 8-3-2-3-3-4-4-7-1-2 0-4-1-6h0l4 4h1c4 3 8 4 12 9h1v1l6 9 4 8c-1 1-2 1-3 2-2-1-2-1-3-2h-1c-1 2 0 4 0 7s-1 6-2 9h0l-4-11c-3-1-6-2-9-2v-8l-1 2c-2 2-3 3-4 5-4-1-5-5-10-5 2-2 3-1 5-1 1 0 3-1 5-1h-2l3-3c-1-2-6-3-7-5 0 0 1-5 1-6 0-2-1-5-2-7z"></path><path d="M661 201h1c4 3 8 4 12 9h1v1l6 9 4 8c-1 1-2 1-3 2-2-1-2-1-3-2h-1c-1 2 0 4 0 7s-1 6-2 9h0l-4-11c1-4 3-7 2-10 0-5-6-7-8-11-3-3-4-7-5-11z"></path><path d="M533 211l-19-58c8 11 18 24 33 27 4 0 8 0 12-3 2-2 4-4 4-7 1-9-4-18-9-24-8-10-20-17-29-25 41 15 83 28 126 40 14 4 30 8 45 11 3 1 7 0 11 1 6 2 12 5 19 6 3 1 6 1 9 1h4c1 0 1 1 2 2 2 4 2 10 2 15l3 21h-1 1v3c0 1 0-1 0 1v2l-7 1-2 1v-1c-1-2-3-4-4-5l-4-4h-1-2c-2 0-4 3-6 4-2-1-3-1-5-2h-1l1-2c1-2 3-5 5-6 1-1 2-1 3-2h0 1c5-1 10 2 14 4l-4-24c-9-1-19-4-28-6-6-1-13-1-18-3l-31-8c-23-6-46-12-69-20-12-4-23-9-35-14 9 7 18 17 19 30 1 6 0 11-4 15-3 4-7 5-12 6-10 1-18-4-26-8 2 8 6 14 9 21l6 9c0 1 2 3 2 4v2l3 4c-1 1-2 2-3 2l-1-1c-1-1-1-1-2-1 0-1-1-1-1-1l-1 1h-3c-1-1-1 0-1-1l-1-2-3-6h-1zm-255 1l5-32c7 0 13 0 19-2 4-1 8-3 12-4 1-1 3-2 5-1 4 1 21-4 26-6l102-29 40-13c6-2 11-4 16-6-12 9-26 19-36 31-3 5-6 10-7 16-1 3-1 6 1 9s5 5 8 5c8 2 18-4 24-8 5-4 9-9 13-13-4 19-11 38-18 56l-2 2-1 3v1h-5v-1c-1 1-1 1-2 1l-1 1-4-2c2-2 4-4 4-7 6-10 12-20 17-31-8 4-16 8-25 8-6-1-11-3-15-7-3-5-5-11-5-16 1-15 12-23 23-31l-38 15c-21 7-43 13-65 18l-33 10-16 3-31 6-3 24 6-3h4c3-1 4-1 6 0l1 1c1 0 2 1 2 2l1 1s1 1 1 2l1 1c-3 1-5 1-8 1h0-1v-1h-3-1c-2 1-4 2-6 4l-1 2h-5-4-2c0-3 2-7 1-10zm26-7h-1c-5-1-10 0-14 2l4-16c5-1 10-2 15-4l27-5c5-1 11-1 16-2 4-1 9-4 13-6l25-6 25-8 25-8 18-6c-7 7-10 14-10 24 0 6 3 13 8 17s11 6 17 5c6 0 11-2 16-4l-10 17c-2 4-4 7-6 10h0c-1 0-2 1-3 2l-4 6-1 1-6-1v-1l6-8v-1c1-2 3-4 4-5 3-3 5-6 7-9-1-1-3 0-4 0-6 2-11 6-18 7-3 1-6 0-10 0-1 0-2 1-3 0-1 0-4-2-5-3l-6-3c-2-2-3-4-5-5-3-2-7-4-10-4v2 3c1 1 10 11 11 11h2c-1 4-4 8-6 12-4-1-9 0-13 0-4 1-9 2-12 1-4-1-9-3-12-5-1-1-3-4-3-6s1-3 1-4l2-9c0 4 1 10 3 13 3 3 10 7 14 6 1 0 2 0 2-1s1-2 0-3c0-2-2-3-4-4l-3-3h0c3-1 6-1 9 0h1l1-1c-1-2-1-3-3-4-5-4-11-6-17-7-2 0-5-1-7 0-2 0-5 7-7 9l-2 5c-1 2-1 5-1 8 0 0 1 1 0 2s-5 3-6 4c-2-1-3-1-5-1s-5-1-7 0c0 1 0 1-1 1v-2l3-2c4-3 5-8 6-13-3 2-6 3-9 5-2 2-4 4-5 6l-2 1c0 1-1 1-1 2-2 2 0 0-1 2l-2-1c0-1 1-1 1-2 1-1 1-2 2-4v-1l6-6v-1h0c-3 2-5 4-8 7-1 1-2 1-2 2l-1 1h-1c1-2 2-3 2-5v-1c0-3 5-7 6-11 1-1 3-2 4-2 3-1 6-2 8-5v-1h2c3 0 15-9 17-11v-1c-5 2-11 3-16 5-2 1-4 3-7 4-2 1-5 1-8 2-5 1-9 3-13 6 1 3 2 5 1 8 0 2 0 5-1 6-1 2-4 4-6 5 0-3 1-6 0-9-1-2-3-4-5-5-6-4-15-5-22-3-1 1-3 1-3 3h2 2c5 0 9 2 12 6h1c-1 0-2 0-3-1l-1 1h0l-2 2c-1-1-1-2-3-2z" class="J"></path><path d="M333 187h2c-3 2-8 3-10 5h0-6l-2-1c4-2 11-3 16-4zm80 16h1c2 3 5 5 5 9l-2 2h-6l2-11z"></path><path d="M360 203c2-3 6-5 10-7h0c-2 1-5 3-6 5l-1 1c-3 0-3 7-4 9s-2 3-3 4v1c2 0 7-1 9 1h1 1v-1h2v-8h0l2-4h0c0-1 1-2 2-2v-1l-2 5c-1 2-1 5-1 8 0 0 1 1 0 2s-5 3-6 4c-2-1-3-1-5-1s-5-1-7 0c0 1 0 1-1 1v-2l3-2c4-3 5-8 6-13z" class="B"></path><path d="M441 157h1c-1 7-2 15 0 22 3 9 10 14 17 18-7 1-16 1-22-3-4-3-8-6-12-8-2-2-4-3-6-3-6-1-9 1-13 4-5-2-10-4-16-5-3-1-7-1-10-4v-1c4-2 8-3 12-3 7-1 10 0 15 3l3-6h3c5 0 11-1 15 3s4 7 5 12c1-1 2-1 3-2s2-3 2-4c0-6-7-11-11-15 5-4 8-6 14-8z"></path><path d="M548 209h0l-13-22c8 3 16 6 24 4 6-1 11-5 13-10 4-6 4-12 2-18-2-7-5-13-9-18l17 7 36 12 35 9c6 2 13 5 19 7 7 2 14 2 20 3 13 2 26 4 39 8l4 16-5-2c-4-1-8-1-12 2l-1 1h-1c-2 1-3 1-4 3v1c-1 1-2 3-2 4-2 0-3-1-4-1 1-3 4-6 4-8v-1h0c4-4 8-7 13-7h6c-6-4-11-5-18-3-5 1-10 3-12 7-3 4-1 8-1 12-1-1-6-4-7-6s0-5-1-7c0-3 1-6 0-8-6-6-12-7-20-8l-2 1h0v-2c-6-4-15-6-22-7 6 5 14 10 21 13l10 5s2 1 3 1c0 1 1 3 1 5l5 10c1 1 2 2 2 4 0 1 1 3 1 4h-1v-1c-1-1-2-1-3-2l-3-6-1 1c0 1 0 2 1 3s1 2 1 3c-1 1-1 2-2 2l-6-9v-1h-1c-4-5-8-6-12-9h-1l-4-4h0c1 2 0 4 1 6 1 3 2 4 4 7-3 2-5 3-8 3v-1c1-2 0-4-1-6 0-2-1-3-2-4 1 2 2 5 2 7 0 1-1 6-1 6 1 2 6 3 7 5l-3 3h2c-2 0-4 1-5 1-2 0-3-1-5 1v1l-1 1h-1c-2 1-3 1-5 2l-2-13h-1c-3 1-6 3-10 4-4 0-24 0-26-2 0-3-3-5-5-8-1-1-1-2-1-3h2c3-1 5-4 7-7l4-5c1-2 1-2 0-4-3-1-6 1-8 2-3 2-5 4-7 6-2 3-9 6-12 8-2 0-4 0-6-1-4 0-7 1-11-1-6-2-11-7-18-8 3 3 6 7 8 11h0c0 2 1 2 2 3l2 4c1 0 1 1 1 1l-6 2-3-4c-1-2-2-4-3-5z" class="J"></path><path d="M688 188h2c4 0 13 1 16 3l-1 1c-5 1-13-1-17-4z"></path><path d="M675 211h1c1 1 1 2 2 3h1v-2c0-1 0-1-1-1v-2c-1-3-4-5-5-7 4 3 6 5 9 9l-1 1c0 1 0 2 1 3s1 2 1 3c-1 1-1 2-2 2l-6-9z" class="B"></path><path d="M605 200c-1 3-3 6-6 8l-2 1 5 6c2 0 3 1 4 0-2-2-3 0-3-3 0-2 0-2 1-3v-1s1-1 2-1l1-3h1l2-1v2l1 1c1 1 1 1 1 2v1c1 2 1 3 2 5l-1 1c1 1 1 2 2 2 2 2 3 1 5 1 0 0 1 1 1 2h4c2-1 3-1 4-2h2c1-1 1-1 3-1h0l1-1h1l1-1s1-1 3-1v2h-1-1c-3 1-6 3-10 4-4 0-24 0-26-2 0-3-3-5-5-8-1-1-1-2-1-3h2c3-1 5-4 7-7z" class="E"></path><path d="M608 204l2-1v2l3 9h-7c-2-1-2-1-2-2 1-3 3-6 4-8zm32 10c0-2 2-5 2-6s0-3-1-5c0-2-1-4-1-7-2 3-1 8-3 11s-8 7-12 8c-2 0-3 0-5-1v-2c-1-6 4-6 8-10-3 1-9 3-11 2l-1-1c1-2 2-3 3-4 6-5 18-7 25-7 3 2 5 7 7 10 1 2 2 5 2 7 0 1-1 6-1 6 1 2 6 3 7 5l-3 3h2c-2 0-4 1-5 1-2 0-3-1-5 1v1l-1 1h-1c-2 1-3 1-5 2l-2-13h1v-2z"></path><path d="M639 216h1c0 2 0 2 1 3 2 0 6-1 8 0s3 3 6 3v1h1 2c-2 0-4 1-5 1-2 0-3-1-5 1v1l-1 1h-1c-2 1-3 1-5 2l-2-13z" class="B"></path><path d="M579 157c7 2 12 4 18 8-4 2-8 6-11 10-1 2-2 4-1 7 1 2 3 3 5 4 1-1 0-5 1-6 0-3 1-6 4-7 4-2 12-3 16-2 3 1 4 4 5 6l5-3c6-3 17 2 24 4h-2c-1 1-2 1-3 2l-5 1c-4 1-8 2-12 4-1 0-5 3-6 2-3-1-5-4-8-4-5-1-12 1-16 5-3 2-5 5-8 7-7 5-16 3-23 2 5-3 11-6 15-11 6-9 4-18 2-29z"></path><defs><linearGradient id="AM" x1="411.014" y1="579.947" x2="326.868" y2="440.94" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#e0dfdf"></stop></linearGradient></defs><path fill="url(#AM)" d="M408 367l11-1c1 4-1 8-2 11l-8 25-8 28h0c-1 6-3 11-4 16l-6 32c-3 20-4 40-4 60v23c1 11 3 22 4 34 3 17 7 35 14 51 7 18 19 33 36 41l-43-3c-2 8-7 16-12 22-12 20-31 36-54 41-14 2-29 1-41-7-6-5-11-11-13-19-1-8 1-14 5-20v10c1 5 4 10 8 14 5 4 13 4 20 3s13-4 17-10 6-14 4-22c-2-11-7-22-14-30-6-7-13-12-20-18l-25-21c-16-15-30-32-41-51-24-44-31-94-29-143l1-20c0-3 0-6 1-9v-1c1-3 1-8 2-11 2 0 3 0 4 1s3 1 4 1c0 1 1 2 0 3 0 1 0 3-1 4 0 1 0 4 1 6 0 7 0 14 6 20 7 6 16 11 23 17l7 7c1 1 1 2 3 3h0c1 3 5 7 8 10l2 2c1-7 7-11 12-16l3-3c2-4 1-9 2-13 0-8 1-17 2-25 1-3 6-6 8-9 1-1 2-3 2-5v-1c3-1 5-2 7-3h2c1-2 8-4 9-5l19-8 1 1c-2 1-2 2-2 4l-2 9v3c0 1 0 1 1 0 3 0 7 0 10-1 0 2 0 3 2 5h0l-1-3c1-2 4-5 6-7s3-4 5-7l1-1c2-3 7-9 10-10h4c2 0 2-1 3-2 2-1 6-1 8-1l12-1h7c4-1 9 0 13 0z"></path><path d="M306 505c2-2 2-3 5-3v1c-1 2-3 5-3 7-1 2-1 4-2 7l-4-4 4-8zm21 55h1c1 0 3 1 5 0 6-1 6-7 9-11h1c0 5 1 12-3 16-1 2-3 2-5 3-2-1-5-2-6-4l-2-4zm54-120c1 1 0 4 0 6-1 8-7 13-11 19-6 10-10 20-13 32-4 14-6 27-4 41 1 2 1 4 0 5h-2c-3-3-3-13-2-17 1-15 4-30 8-44 4-16 13-30 24-42z"></path><defs><linearGradient id="AN" x1="399.499" y1="378.141" x2="389.941" y2="404.773" xlink:href="#B"><stop offset="0" stop-color="#4c4b4c"></stop><stop offset="1" stop-color="#a09f9f"></stop></linearGradient></defs><path fill="url(#AN)" d="M392 380c1 0 2 0 3-1h1c1 0 3-1 5 0v2c-2 7-1 14-2 21 0 3-1 6-1 9v2c0 1 0 1-1 2v1 1c0 2-1 3-2 5 0-1 0-2-1-3l1-1v-1-1c1-1 1-2 1-3h0v-1l1-1c0-1-1-1 0-2 0-1 0-2 1-3v-2-4c-1 0-2 0-3 1 0 3-1 6-1 10l-1 2c0 4-2 8-4 11l-1 1-2 2-1 1c-2 2-3 4-6 5 0-12 2-23 7-34-1-2 5-16 6-19z"></path><path d="M390 404c2-3 3-8 4-11 0 11-1 22-6 32l-2 2c-1-1-1-2-1-4 1-6 3-13 5-19z"></path><defs><linearGradient id="AO" x1="383.493" y1="403.446" x2="386.483" y2="430.335" xlink:href="#B"><stop offset="0" stop-color="#9c9b9b"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#AO)" d="M386 399v1c0 1 0 2-1 3 2 1 3 0 5 0h0v1c-2 6-4 13-5 19 0 2 0 3 1 4l-1 1c-2 2-3 4-6 5 0-12 2-23 7-34z"></path><path d="M408 367l11-1c1 4-1 8-2 11l-8 25h-1-1-2c-2 0-2 0-4-1l-3 10c0-3 1-6 1-9 1-7 0-14 2-21v-2c-2-1-4 0-5 0h-1c-1 1-2 1-3 1 1-3 4-10 3-13 4-1 9 0 13 0z"></path><defs><linearGradient id="AP" x1="412.522" y1="366.886" x2="413.335" y2="377.712" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#AP)" d="M408 367l11-1c1 4-1 8-2 11-4 1-7 1-10 2 1-4 3-8 3-12h-2 0z"></path><defs><linearGradient id="AQ" x1="398.051" y1="367.167" x2="400.405" y2="378.128" xlink:href="#B"><stop offset="0" stop-color="#242223"></stop><stop offset="1" stop-color="#484747"></stop></linearGradient></defs><path fill="url(#AQ)" d="M395 367c4-1 9 0 13 0h0-2l-5 14v-2c-2-1-4 0-5 0h-1c-1 1-2 1-3 1 1-3 4-10 3-13z"></path><defs><linearGradient id="AR" x1="408.618" y1="377.438" x2="408.382" y2="401.557" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#a2a2a2"></stop></linearGradient></defs><path fill="url(#AR)" d="M407 379c3-1 6-1 10-2l-8 25h-1-1-2c-2 0-2 0-4-1l6-22z"></path><path d="M295 512c-1-8-2-16-1-25 5-2 9-2 15-1l14 2c2 1 3 4 4 5 3 7 6 15 3 22-1 3-4 7-6 8-3 1-6 1-8 0s-4-2-4-3c-1-2 0-5 0-7l-1-2 1-1-3-1-1 2v-1c0-2 2-5 3-7v-1c-3 0-3 1-5 3-1-1-3-1-4-2-3 3-4 6-6 9h-1z"></path><path d="M308 510c0-2 2-5 3-7 2 1 2 1 4 1h1c-2 3-3 6-4 9l-1-2 1-1-3-1-1 2v-1z" class="L"></path><path d="M302 503c3-4 7-8 8-13 2 1 4 3 6 5 4 3 8 5 9 11v2l-1 1c-2-1-6-5-8-5h-1c-2 0-2 0-4-1v-1c-3 0-3 1-5 3-1-1-3-1-4-2z" class="J"></path><path d="M388 367h7c1 3-2 10-3 13s-7 17-6 19c-5 11-7 22-7 34l-1 2 1 1-2 2c1 1 1 1 3 2v-1l1 1c-11 12-20 26-24 42-1 0-2 1-3 0h-1l-1 1h-1l15-62c1-1 1-3 1-4l3-8 1-6 4-13c0-2 2-5 2-7v-1c-3 2-3 5-6 7h-1c2-3 4-7 5-10 2-4 2-7 1-11l12-1z"></path><defs><linearGradient id="AS" x1="379.656" y1="378.011" x2="376.906" y2="402.433" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#a1a0a0"></stop></linearGradient></defs><path fill="url(#AS)" d="M376 368l12-1-1 5c-2 11-6 21-8 32h0c-1-1-2-1-3-2-2 0-3 0-5 1l4-13c0-2 2-5 2-7v-1c-3 2-3 5-6 7h-1c2-3 4-7 5-10 2-4 2-7 1-11z"></path><defs><linearGradient id="AT" x1="381.291" y1="367.967" x2="381.22" y2="378.033" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#444"></stop></linearGradient></defs><path fill="url(#AT)" d="M376 368l12-1-1 5-2 2c0 1 1 1 0 3h0c0 1 0 1-1 2h0c-2-1-5-1-7-1 0 0-1 0-2 1 2-4 2-7 1-11z"></path><defs><linearGradient id="AU" x1="348.387" y1="431.166" x2="377.845" y2="458.171" xlink:href="#B"><stop offset="0" stop-color="#bebdbe"></stop><stop offset="1" stop-color="#f8f7f7"></stop></linearGradient></defs><path fill="url(#AU)" d="M371 403c2-1 3-1 5-1 1 1 2 1 3 2h0c-1 3-2 7-3 11l-11 38c4-6 8-13 13-18l1 1-2 2c1 1 1 1 3 2v-1l1 1c-11 12-20 26-24 42-1 0-2 1-3 0h-1l-1 1h-1l15-62c1-1 1-3 1-4l3-8 1-6z"></path><path d="M371 403c2-1 3-1 5-1 1 1 2 1 3 2h0c-1 3-2 7-3 11v-4h0v-3c-1 0-3-1-5-1 0 1 0 1-1 2h0l1-6z" class="H"></path><path d="M368 369c2-1 6-1 8-1 1 4 1 7-1 11-1 3-3 7-5 10h1c3-2 3-5 6-7v1c0 2-2 5-2 7l-4 13-1 6-3 8c0 1 0 3-1 4l-15 62-7 35c-3-10-4-20-2-30 1-4 2-8 4-11-6 4-9 7-10 14v2c-1-2-2-4-4-6l-1 1c0-1 0-1-1-2v1l-4 3c1 2 1 2 1 3-1-1-2-4-4-5l-14-2c-6-1-10-1-15 1-1 9 0 17 1 25h1l-1 1c0 5 1 10 2 14 0 1-1 2-1 2h0l-6 3v1c0 2 0 4 1 6h-1c-1-1-1-4-1-5l-2-22c-4-29 0-60 6-88 1-7 3-15 5-21 0-1 1-2 1-3l3-9c1-2 8-4 9-5l19-8 1 1c-2 1-2 2-2 4l-2 9v3c0 1 0 1 1 0 3 0 7 0 10-1 0 2 0 3 2 5h0l-1-3c1-2 4-5 6-7s3-4 5-7l1-1c2-3 7-9 10-10h4c2 0 2-1 3-2z" class="L"></path><path d="M295 512h1l-1 1c0 5 1 10 2 14 0 1-1 2-1 2h0l-6 3v1c0 2 0 4 1 6h-1c-1-1-1-4-1-5l-2-22c1 2 1 3 1 5 0 1 0 1 1 2v4 6c2-1 5-2 7-2v-1c-1-1-1-1 0-2-1-1-1-1-1-2v-2c0-1-1-2-1-3 1-1 1 0 0-1v-1c0-1 0-1 1-3zm7-121c1-2 8-4 9-5l-7 22v-5c1-1 1-1 1-2v-1l1-1h0v-1-1h-2c-1 0-1-1-3-1l-2 7h-1c0-1 1-2 1-3l3-9z" class="B"></path><path d="M353 436c3-1 7-4 8-7 2-3 3-6 5-8l-15 62-7 35c-3-10-4-20-2-30 1-4 2-8 4-11-6 4-9 7-10 14v2c-1-2-2-4-4-6l-2-4c4-7 12-11 16-18 4-4 6-10 7-15 1-2 2-5 1-7v1s-1 2-1 3c-1 2-2 4-3 7l-8-10c1 3 3 6 4 9 0 1 1 3 1 4s-1 2-2 2c-4 4-9 7-12 11-3 3-4 5-6 8v1l-6-7c-2-3-4-5-6-8-1-3-2-6-2-9 3 3 7 6 11 9 3-4 5-8 9-12 2 3 4 6 8 8v-1c0-2-2-4-4-6-1-2-1-4-1-6 2-8 11-7 17-11h0zm-42-50l19-8 1 1c-2 1-2 2-2 4l-2 9v3c-1 5-2 10-4 14-1 1-3 5-2 6-1 4-1 8-2 11 0 5-2 9-2 14 0 3 3 7 2 10h-4c-2-1-4-3-5-5v-7c0-9 4-16 7-24v-1-2l1-1v-1c1-1 1 0 1-1 1-1 1-2 1-3l-1-1c-1 0-3 1-4 0h-1c0 2-1 7-3 8l-1 1h0c-4 7-7 14-8 23v1 1c-1 5 0 10 1 14v5c0 1 1 2 2 2l16 24-19-4c2-2 5-4 6-7 0-1-1-1-1-2-5 2-9 6-13 8 0-7 1-15 2-22 2-16 4-33 8-48l7-22z"></path><path d="M310 413c1-3 3-5 3-8l3-15c0 2 0 4 1 6 0 2 4 7 4 9-1 3-2 6-4 8v1-1-2l1-1v-1c1-1 1 0 1-1 1-1 1-2 1-3l-1-1c-1 0-3 1-4 0h-1c0 2-1 7-3 8l-1 1z" class="B"></path><path d="M368 369c2-1 6-1 8-1 1 4 1 7-1 11-1 3-3 7-5 10h1c3-2 3-5 6-7v1c0 2-2 5-2 7l-4 13-1 6-3 8c0 1 0 3-1 4-2 2-3 5-5 8-1 3-5 6-8 7l1-2c1 0 2-1 3-2 1 0 2-1 3-2-1-1-1-1-2-1l-2 2c-1 2-1 2-4 2h-1c-4 1-8 1-12 0h-1c-1 2-2 4-3 5-2 5-4 9-8 13v-10c-1-4-1-9-1-13 0-5 1-9 2-13h0v-2c0-1 0-1 1-2v-2h1v-1-1h0-5c-1 3-2 6-4 8h0c-1-1 1-5 2-6 2-4 3-9 4-14 0 1 0 1 1 0 3 0 7 0 10-1 0 2 0 3 2 5h0l-1-3c1-2 4-5 6-7s3-4 5-7l1-1c2-3 7-9 10-10h4c2 0 2-1 3-2z"></path><defs><linearGradient id="AV" x1="349.256" y1="387.352" x2="353.997" y2="397.391" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#AV)" d="M350 397h0c-1 0-2-1-3-2 0-4 1-6 3-10 4 3 7 8 11 10l1 1 1 1c-2 0-2 0-3-1h-4l-1 1h1c-1 1-2 1-3 2l-3-2z"></path><path d="M350 397c1 0 1-1 1-1h5l-1 1h1c-1 1-2 1-3 2l-3-2z" class="D"></path><defs><linearGradient id="AW" x1="340.033" y1="394.926" x2="333.721" y2="425.081" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#AW)" d="M327 395c0 1 0 1 1 0 3 0 7 0 10-1 0 2 0 3 2 5h0c3 3 4 10 5 14 2 3 3 5 4 7-1 2-4 7-7 8h-2l-2-2h-1 0c-1 1 0 1-1 1l-1-1c-2-1-4-1-5-3-1-1 0-1 0-3h0c4-2 6-5 8-8 1-5-1-9-3-12h-1c-1 3-3 7-4 11 0 0-1 4-2 4h0v-2c0-1 0-1 1-2v-2h1v-1-1h0-5c-1 3-2 6-4 8h0c-1-1 1-5 2-6 2-4 3-9 4-14z"></path><defs><linearGradient id="AX" x1="361.927" y1="395.479" x2="355.294" y2="428.25" xlink:href="#B"><stop offset="0" stop-color="#9f9f9e"></stop><stop offset="1" stop-color="#c6c4c4"></stop></linearGradient></defs><path fill="url(#AX)" d="M370 389h1c3-2 3-5 6-7v1c0 2-2 5-2 7l-4 13-1 6-3 8c0 1 0 3-1 4-2 2-3 5-5 8-1 3-5 6-8 7l1-2c1 0 2-1 3-2 1 0 2-1 3-2-1-1-1-1-2-1l-2 2c-1 2-1 2-4 2l4-3c3-3 4-12 4-16 0-6-3-11-7-15 1-1 2-1 3-2h-1l1-1h4c1 1 1 1 3 1l-1-1c4-1 6-4 8-7z"></path><path d="M369 401c1-4 4-7 6-11l-4 13-1 6-3 8c0-5 1-11 2-16z"></path><defs><linearGradient id="AY" x1="372.573" y1="382.547" x2="368.42" y2="397.478" xlink:href="#B"><stop offset="0" stop-color="#646263"></stop><stop offset="1" stop-color="#939393"></stop></linearGradient></defs><path fill="url(#AY)" d="M370 389h1c3-2 3-5 6-7v1c0 2-2 5-2 7-2 4-5 7-6 11 0-2 0-2-1-4-2 0-3-1-5 0l-1-1c4-1 6-4 8-7z"></path><path d="M295 513c4 4 8 9 11 13 2 5 2 11 6 14-2 2-4 3-6 6 0 4 0 9 1 14l1 1c1 4 2 10 5 13 1 3 4 6 5 9 2 4 3 8 5 11 0 2 2 3 3 5 1 1 2 3 3 5 1 0 4 0 5 1 0 0 1 2 2 3 2 2 9 13 12 13 0-4-1-7-3-11-1-5-3-10-6-14l-7-11c-1-1-1-4-2-5l-5-5c-4-7-8-16-10-24l-1 10c-2-7-3-12 1-19 2-3 5-4 8-6-3-1-7-2-8-5v-2h0c6 0 11 0 16 3 1-5 4-11 5-16l5 25c1 3 2 6 2 8h-1c-3 4-3 10-9 11-2 1-4 0-5 0h-1c-1-1-1-2-1-3 0-3-1-5-1-7-1-3-4-7-6-8v-1c-2 2-2 4-1 6 0 7 6 18 12 23 2 2 6 1 8 1l3-1h-1l2-1h1 1v-1c2 0 2 0 4-1v-2-1c1-1 0-1 0-2v-1c2-2 1-3 1-5v-2l1-1c0-1 0-1 1-3l1 1c2 1 8 5 8 7 1 7-7 13-10 18-2 3-3 6-4 9-1 0-2 2-2 3s1 4 1 6c2 4 4 8 5 12 1 2 2 4 4 5 0-1 1-2 1-3 0-2 0-4-1-6-1-8-2-16 1-23l6 14c0-3 2-7 1-9v-1c0-2-2-4-4-6 0-4 6-8 7-12 1-3 0-6-1-9h0c3-2 6-4 9-7 2-3 1-7 1-10-1-3-2-6-4-7h-1c-2 2-3 8-4 11 0 1-1 3-2 5-2-2-3-3-3-5-2-5-1-12 1-16 1-2 2-3 4-4 2 1 4 3 5 5 9 13 9 28 12 43l5 31c4 21 10 42 19 62-2-3-3-5-5-8 0 1 0 1-1 2-1-1-1-1-1-2l-2-2c-1 0-2 0-2-1l-1-2-3-3h0c-1 2-1 2 0 4l1 1c0 1 0 1 1 1 1 1 1 2 1 3v1c6 9 17 15 26 21-6-1-12-4-17-8-11-7-20-16-28-27l-5 4v1c1 1 3 3 4 5 9 12 21 22 35 27h1c-5 0-13-3-17-5l-9 18c-5 11-12 20-21 28-10 8-21 15-34 17h-13c5-2 10-3 15-6 11-6 17-18 21-31 2-5 3-11 3-16 0-11-3-22-7-33-2-6-4-12-8-18h0c-2-5-5-9-7-13h0v2c0 1-2 3-3 4l-3-6-1 1h0l-17-25c-3-5-7-9-10-14-1-3-2-8-2-12-3-11-6-22-7-33h1c-1-2-1-4-1-6v-1l6-3h0s1-1 1-2c-1-4-2-9-2-14z"></path><path d="M373 624l3 1-2 2c0 3 2 6 1 9l-1-1c-1-2-1-9-1-11z" class="C"></path><path d="M377 627l1-1c2 0 3 1 4 2l-4 8c-1-3 0-6-1-9h0z" class="E"></path><path d="M359 668c2 1 5 1 7 1v1c-4 3-5 6-7 10l-1 1 1-13z" class="B"></path><path d="M338 623c-2-3-3-6-4-10l8 8c0 2 0 2 1 4v1c-2 0-3-2-5-3zm23 20l6-5s1 1 2 1 2-1 3-2c1 2 2 4 2 6l-1 2-1-1-4-1c-1 0-1-1 0-2v-2l-1 1-2 2c-1 1-2 1-4 1z" class="D"></path><path d="M394 658c-3-4-8-11-10-16 1-1 1-1 3-2 1 1 2 3 4 5l6 9c-1 0-2 0-2-1l-1-2-3-3h0c-1 2-1 2 0 4l1 1c0 1 0 1 1 1 1 1 1 2 1 3v1z" class="B"></path><defs><linearGradient id="AZ" x1="342.966" y1="576.754" x2="347.187" y2="567.586" xlink:href="#B"><stop offset="0" stop-color="#6a696a"></stop><stop offset="1" stop-color="#8b8989"></stop></linearGradient></defs><path fill="url(#AZ)" d="M341 570h-1l2-1h1 1v-1c2 0 2 0 4-1v-2-1c1-1 0-1 0-2v-1c2-2 1-3 1-5v-2l1-1c0 7-1 21-6 26-1 1-3 1-4 2h-1c-2 1-2 1-4 1 4-2 7-3 8-8l2-4c-2 0-3 1-4 0z"></path><path d="M361 612c0 1 0 3-1 4-3 3-7 12-12 12-2 0-4-1-5-2v-1c-1-2-1-2-1-4 2 2 3 3 5 3 4-1 5-4 8-6 2-3 4-4 6-6zm0 31c2 0 3 0 4-1l2-2 1-1v2c-1 1-1 2 0 2l4 1 1 1c-4 3-12 4-17 5-1-1-1-2-1-3 0-2 1-5 2-6h0l1 3h1l2-1z" class="B"></path><path d="M341 541c1 3 2 6 2 8h-1c-3 4-3 10-9 11-2 1-4 0-5 0h-1c-1-1-1-2-1-3 3-1 5-1 7-2 4-3 6-9 8-14z" class="I"></path><path d="M371 623c0-3-1-7 0-9 2 1 3 3 6 3h0c3 0 3 0 4-2 2 5 2 8 1 13-1-1-2-2-4-2l-1 1-1-2-3-1-2-1z" class="B"></path><path d="M377 617c1-2 0-7-1-9-1-4-3-6-6-8h-1c1-2 2-5 4-7h0c3 2 6 8 7 11 0 1 1 5 1 6h-1l-1 1c1 1 1 3 2 4-1 2-1 2-4 2z" class="H"></path><defs><linearGradient id="Aa" x1="380.977" y1="628.4" x2="404.344" y2="645.839" xlink:href="#B"><stop offset="0" stop-color="#aeabac"></stop><stop offset="1" stop-color="#d9dada"></stop></linearGradient></defs><path fill="url(#Aa)" d="M386 615c2 6 3 11 5 16 3 9 7 17 10 25 0 1 0 1-1 2-1-1-1-1-1-2-2-4-5-8-7-12s-4-9-5-13c-1-5-1-10-2-15l1-1z"></path><path d="M371 573l4-6c-1 3-2 6-2 9 4 2 8 4 9 9 1 3 2 7 2 10l2 20-1 1c-1-2-1-5-1-7-1-7 0-15-5-21-2-2-4-3-6-4-1 0-2 1-2 1h-2c-1-3 3-6 2-9l-1 1c0 1 0 2-1 3-1-3 1-3 2-5v-2z" class="B"></path><defs><linearGradient id="Ab" x1="358.939" y1="610.354" x2="377.236" y2="585.408" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#c0bfbe"></stop></linearGradient></defs><path fill="url(#Ab)" d="M371 573v2c-1 2-3 2-2 5 1-1 1-2 1-3l1-1c1 3-3 6-2 9h2c-3 5-3 9-4 14 0 3-1 5 0 8 1 2 2 5 4 7-1 2 0 6 0 9-4-3-7-12-8-18s-2-14 1-20c2-4 5-8 7-12z"></path><defs><linearGradient id="Ac" x1="341.473" y1="629.087" x2="361.318" y2="629.993" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#979695"></stop></linearGradient></defs><path fill="url(#Ac)" d="M361 612v-1c0 6 1 11 2 16h0l1 2h2c0 1 1 1 0 1-1 5-4 9-8 11h-1 0c-1 1-2 4-2 6 0 1 0 2 1 3l-1 1-5 2-12-30c2 1 3 3 5 3 1 1 3 2 5 2 5 0 9-9 12-12 1-1 1-3 1-4z"></path><path d="M355 651l-2-1c0-1-1-2 0-4v-1c1-2 1-3 2-4l1-1h1c1-2 3-4 5-5 0 0 1-1 1-2-1-2-2-3-1-5l1-1 1 2h2c0 1 1 1 0 1-1 5-4 9-8 11h-1 0c-1 1-2 4-2 6 0 1 0 2 1 3l-1 1z" class="H"></path><defs><linearGradient id="Ad" x1="360.546" y1="702.749" x2="374.326" y2="660.181" xlink:href="#B"><stop offset="0" stop-color="#b3b1b1"></stop><stop offset="1" stop-color="#f7f6f6"></stop></linearGradient></defs><path fill="url(#Ad)" d="M359 668v-3c2-1 4-2 5-2l-1-9 6 6c5 4 15 7 14 15 0 3-1 6-2 8-5 14-15 29-26 38h-1c9-9 17-20 17-33 0-3-2-6-2-10 0 0 0-1 1-1 2-1 3-1 5-2v-1c0-2-2-4-4-4-1-1-3-1-5-1s-5 0-7-1z"></path><path d="M377 679h0c0 2-1 2-1 3v1c-1 1-2 2-2 3l-1 1v2h-1-1v-2c1-1 1-2 1-4h0l-1-2v-1c1 0 3 1 4 1l1-1 1-1z" class="E"></path><defs><linearGradient id="Ae" x1="300.152" y1="583.945" x2="317.304" y2="576.204" xlink:href="#B"><stop offset="0" stop-color="#8f8d8e"></stop><stop offset="1" stop-color="#b8b8b7"></stop></linearGradient></defs><path fill="url(#Ae)" d="M297 527l6 28c1 7 1 14 4 21 2 6 5 12 8 18 6 9 13 18 18 28h0v2c0 1-2 3-3 4l-3-6-1 1h0l-17-25c-3-5-7-9-10-14-1-3-2-8-2-12-3-11-6-22-7-33h1c-1-2-1-4-1-6v-1l6-3h0s1-1 1-2z"></path><path d="M302 391l-3 9c0 1-1 2-1 3-2 6-4 14-5 21-6 28-10 59-6 88l2 22c0 1 0 4 1 5 1 11 4 22 7 33 0 4 1 9 2 12 3 5 7 9 10 14l17 25h0l1-1 3 6c1-1 3-3 3-4v-2h0c2 4 5 8 7 13h0c4 6 6 12 8 18 4 11 7 22 7 33 0 5-1 11-3 16-4 13-10 25-21 31-5 3-10 4-15 6h13c13-2 24-9 34-17 9-8 16-17 21-28l9-18c4 2 12 5 17 5h-1c-14-5-26-15-35-27-1-2-3-4-4-5v-1l5-4c8 11 17 20 28 27 5 4 11 7 17 8-9-6-20-12-26-21v-1c0-1 0-2-1-3-1 0-1 0-1-1l-1-1c-1-2-1-2 0-4h0l3 3 1 2c0 1 1 1 2 1l2 2c0 1 0 1 1 2 1-1 1-1 1-2 2 3 3 5 5 8-9-20-15-41-19-62l-5-31c-3-15-3-30-12-43v-3c-2-1-3-2-5-3h-1 0v-4c3 0 5-1 7 1 1 1 1 2 3 2l1 1h0v-1c-5-5-9-4-15-5 0-6 2-12 3-18 3-9 6-18 11-27 3-5 6-11 9-16 1-4 2-7 3-11 0-3 0-8 1-10 1-3 5-8 7-10h0v-1c0-1 0-1 1-1 1-2 2-3 2-5v-1-1c1-1 1-1 1-2v-2l3-10c2 1 2 1 4 1h2 1 1l-8 28h0c-1 6-3 11-4 16l-6 32c-3 20-4 40-4 60v23c1 11 3 22 4 34 3 17 7 35 14 51 7 18 19 33 36 41l-43-3c-2 8-7 16-12 22-12 20-31 36-54 41-14 2-29 1-41-7-6-5-11-11-13-19-1-8 1-14 5-20v10c1 5 4 10 8 14 5 4 13 4 20 3s13-4 17-10 6-14 4-22c-2-11-7-22-14-30-6-7-13-12-20-18l-25-21c-16-15-30-32-41-51-24-44-31-94-29-143l1-20c0-3 0-6 1-9v-1c1-3 1-8 2-11 2 0 3 0 4 1s3 1 4 1c0 1 1 2 0 3 0 1 0 3-1 4 0 1 0 4 1 6 0 7 0 14 6 20 7 6 16 11 23 17l7 7c1 1 1 2 3 3h0c1 3 5 7 8 10l2 2c1-7 7-11 12-16l3-3c2-4 1-9 2-13 0-8 1-17 2-25 1-3 6-6 8-9 1-1 2-3 2-5v-1c3-1 5-2 7-3h2z" class="J"></path><path d="M333 622c2 4 5 8 7 13l-1-1s-1-1-1-2l-1-2c-1-1-1-2-2-2-1 1-2 2-3 4 1 2 3 5 3 8-1-2-2-5-3-7l-6-10 1-1 3 6c1-1 3-3 3-4v-2h0z" class="B"></path><path d="M251 497h0v6c2 0 4 1 6 0h0 1 0c1-1 1-2 3-3h0c1 0 3 0 4 1 2 0 3 2 6 2 0 1 0 2 1 2 2 1 3 2 4 3v2h-1 0c0-2-1-3-3-4-3-2-6-5-11-5-1 1-2 3-3 4h-2-1v1c-2 1-2 1-4 1-1-2-1-8 0-10z" class="E"></path><path d="M255 470h0c1 5 0 12 0 17 1 2 0 5 1 6l6 1h-6 0c-2 1-1 5-2 8v-1h-1v-6c-1-6-2-12 1-16 1-2 1-7 1-9z" class="B"></path><defs><linearGradient id="Af" x1="402.609" y1="401.488" x2="402.891" y2="415.512" xlink:href="#B"><stop offset="0" stop-color="#a4a2a2"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#Af)" d="M401 401c2 1 2 1 4 1h2 1 1l-8 28h0-1c0-1 1-3 1-4v-2h1c0-2 0-3 1-4v-2-1l1-1h-6l-3 8c-1 3-1 8-3 10l2-10h0v-1c0-1 0-1 1-1 1-2 2-3 2-5v-1-1c1-1 1-1 1-2v-2l3-10z"></path><path d="M255 470v-2c8 9 15 19 17 31-3-2-6-5-10-5l-6-1c-1-1 0-4-1-6 0-5 1-12 0-17h0zm0 35h1 2c1-1 2-3 3-4 5 0 8 3 11 5 2 1 3 2 3 4h0c1 3 1 4 0 7-1 1-1 2-1 3v2c-1-1-6-5-7-5 0 0-1 1-2 1-2 1-4 3-6 3l-1-1c-1-2-1-5-2-7 1-2 0-5-1-8z"></path><path d="M258 520c1-2 3-3 4-5l6-6 7 8c-1 1-1 2-1 3v2c-1-1-6-5-7-5 0 0-1 1-2 1-2 1-4 3-6 3l-1-1z" class="B"></path><path d="M375 521c-5-5-9-4-15-5 0-6 2-12 3-18 3-9 6-18 11-27 3-5 6-11 9-16 1-4 2-7 3-11 0-3 0-8 1-10 1-3 5-8 7-10l-2 10c-2 5-3 10-4 15-6 28-9 57-8 85l-2-7c0-1-1-1-1-2l-2-3h0v-1z"></path><path d="M375 521c-4-7-4-14-5-22v-11l1-1v-1l4-10 3 51c0-1-1-1-1-2l-2-3h0v-1z" class="L"></path><path d="M229 509c-1-4-2-8-3-13 0-5 1-12 4-16 2-2 3-2 5-2 2-1 3 0 4 2h1v-2c-1-6-4-8-9-12-1 4-2 7-4 11l-4 7c-1 0-2 2-2 2 0 1 0 5 1 6-2-3-3-7-4-10-2-7-1-15 2-21 1-2 3-6 6-7 1-1 4 1 5 0 1 0 1-1 1-1-2-6-7-9-12-12 0 2-1 4-2 5-1 0-2-1-3-1 0 1 0 3-1 5 0 1-1 1-2 2h-1c-1-8-1-17 0-24 5 6 11 9 18 14s14 11 19 19h0c0 4-1 9-2 14-1 3-2 6-3 10h0v1c0 2 0 2-1 4l-1 2c1 1 2 2 3 2 1-1 1-2 1-2 0-2 1-2 1-4 1 0 1 0 1-1 1 2 1 6 1 8h0 0c0-4 0-6 2-9v1l1 10c-1 2-1 8 0 10 2 0 2 0 4-1v-1c1 3 2 6 1 8 1 2 1 5 2 7l1 1c2 0 4-2 6-3 1 0 2-1 2-1 1 0 6 4 7 5 0 3-3 8-5 11-2 2-3 4-6 4h-1c-4 0-9-1-13-3-5-2-11-7-14-11-2-4-4-9-6-14z"></path><path d="M266 523h2v1c1 1 0 3-1 4-1 2-4 2-6 3 3-3 4-4 5-8zm-27-17h0c-1 1-1 2-3 3l-1-1c-1-3-1-5 0-8 3 2 2 3 4 6z" class="F"></path><defs><linearGradient id="Ag" x1="261.993" y1="521.669" x2="251.121" y2="529.377" xlink:href="#B"><stop offset="0" stop-color="#767475"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#Ag)" d="M252 517h1l3 6 2 1h1c2 0 5 0 7-1-1 4-2 5-5 8h-3c-5 0-7-1-11-4 3 0 5 0 7-1 0-3-1-6-2-9z"></path><path d="M235 500c3-5 6-9 8-15v1c0 2 0 2-1 4l-1 2c1 1 2 2 3 2 1-1 1-2 1-2 0-2 1-2 1-4 1 0 1 0 1-1 1 2 1 6 1 8h0 0c0-4 0-6 2-9v1l1 10c-1 2-1 8 0 10 2 0 2 0 4-1v-1c1 3 2 6 1 8 1 2 1 5 2 7l1 1-1 1 1 2h-1l-2-1-3-6h-1c1 3 2 6 2 9-2 1-4 1-7 1-2-2-4-5-6-7s-5-2-7-4c2-2 8-2 11-2 2-7 2-16 2-24-2 6-5 11-8 16-2-3-1-4-4-6z" class="B"></path><path d="M252 517v-4-1c1 2 1 3 2 4 1 2 1 3 3 4v-1l-1-1 1-1c-1-2-1-3-1-4 1 2 1 5 2 7l1 1-1 1 1 2h-1l-2-1-3-6h-1z" class="H"></path><path d="M302 391l-3 9c0 1-1 2-1 3-2 6-4 14-5 21-6 28-10 59-6 88l2 22c0 1 0 4 1 5 1 11 4 22 7 33 0 4 1 9 2 12 3 5 7 9 10 14l17 25h0l6 10c1 2 2 5 3 7h0l5 12c4 8 6 18 8 27 3 10 0 27-5 36-2 4-4 7-6 9 0-2 1-5 2-8v-13c0-2-1-4-1-6-2-14-10-28-20-38-5-4-11-8-16-12-8-7-16-14-23-21-17-17-32-36-42-58-15-29-23-63-26-96 0-4-1-12 0-16 3 0 6-1 8-3-2 3-3 6-4 10-2 9 0 19 2 28 1 7 2 14 5 20h1 0c2 4 3 7 5 10 2 2 2 4 4 5 1 0 2-1 3-2-1-1-2-2-2-3h0c-1-2-1-3-2-4v-1c-1-1-1-4-2-5v-2c2 5 4 10 6 14 3 4 9 9 14 11 4 2 9 3 13 3h1c3 0 4-2 6-4 2-3 5-8 5-11v-2c0-1 0-2 1-3 1-3 1-4 0-7h1c2-1 3-2 5-4 0-2 0-3-1-4v-2l-1-3-1-1c0-1 1 0 0-1-1-2-1-3-1-6-1-2-2-3-2-5-1-2-1-3-3-4-2-3-5-8-6-12-1 0-1-1-2-2 1-7 7-11 12-16l3-3c2-4 1-9 2-13 0-8 1-17 2-25 1-3 6-6 8-9 1-1 2-3 2-5v-1c3-1 5-2 7-3h2z"></path><path d="M239 559l-4-11h0c2 3 3 6 6 8h1v3h-2-1z" class="G"></path><path d="M239 559h1 2c2 1 5 4 6 6h-1-1l-1-1v1c-1 0-1 0-2-1-1 1-1 1-2 1-1-2-2-4-2-6z" class="K"></path><defs><linearGradient id="Ah" x1="247.888" y1="553.799" x2="251.366" y2="563.469" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#Ah)" d="M242 556l3 2c3-1 4-4 6-6 1 0 2-1 3 0 1 0 1 0 1 1 1 3 0 6 0 9 1 1 3 1 5 1-3 0-7-1-10 2v3c-1-1-1-2-2-3-1-2-4-5-6-6v-3z"></path><path d="M278 551c2 0 3 0 5 1 3 2 4 7 7 10l1-2 1 1c1 3-2 9-3 13-3-3-5-9-7-12-1-2-1-3-2-4-2-2-3-4-4-6l2-1z" class="B"></path><path d="M276 552l2-1c2 2 4 5 5 8v1l-3-2c-2-2-3-4-4-6z" class="E"></path><defs><linearGradient id="Ai" x1="302.11" y1="638.077" x2="292.889" y2="621.922" xlink:href="#B"><stop offset="0" stop-color="#aaa9a9"></stop><stop offset="1" stop-color="#d6d4d5"></stop></linearGradient></defs><path fill="url(#Ai)" d="M287 616c7 7 14 12 21 18-1 2-3 2-5 3-1 0-2 0-3 1-6-6-16-12-21-19l1-1 7 8c2-1 3-2 5-4l-3-3-1-1c-1-1-1-1-1-2z"></path><defs><linearGradient id="Aj" x1="244.675" y1="536.597" x2="246.779" y2="527.112" xlink:href="#B"><stop offset="0" stop-color="#abaaaa"></stop><stop offset="1" stop-color="#cbc9ca"></stop></linearGradient></defs><path fill="url(#Aj)" d="M222 511h1 0c2 4 3 7 5 10 2 2 2 4 4 5 1 0 2-1 3-2-1-1-2-2-2-3h0c-1-2-1-3-2-4v-1c-1-1-1-4-2-5v-2c2 5 4 10 6 14 3 4 9 9 14 11 4 2 9 3 13 3 1 1 1 1 0 1l-1 1h-2c-6 1-13 0-18-3-7-5-13-12-16-19l-3-6z"></path><defs><linearGradient id="Ak" x1="277.308" y1="534.723" x2="235.525" y2="514.885" xlink:href="#B"><stop offset="0" stop-color="#a6a5a5"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#Ak)" d="M277 489c5 11 9 27 5 39-3 7-8 11-12 17-12 3-22 3-34 1-2-1-5-1-8-3-1 0-3-5-3-6v-1c0-1 1-2 2-1 1 0 4 2 5 2 9 4 21 7 29 2l1-1c1 0 1 0 0-1h1c3 0 4-2 6-4 2-3 5-8 5-11v-2c0-1 0-2 1-3 1-3 1-4 0-7h1c2-1 3-2 5-4 0-2 0-3-1-4v-2l-1-3-1-1c0-1 1 0 0-1-1-2-1-3-1-6z"></path><path d="M241 565c1 0 1 0 2-1 1 1 1 1 2 1v-1l1 1h1 1c1 1 1 2 2 3 2 2 5 7 8 8l1 2 6 4 2 2c2 1 3 2 5 4h0c1 2 2 5 3 8l6 9 6 11c0 1 0 1 1 2l1 1 3 3c-2 2-3 3-5 4l-7-8-1 1c-2-2-4-6-6-9-4-6-8-13-12-18-2-3-4-5-7-8-3-2-9-5-10-8-2-3-2-7-3-10v-1z" class="L"></path><path d="M256 577l3 1 6 4-2 1-1 1 2 2v1l-7-8-1-2z" class="B"></path><path d="M241 565c1 0 1 0 2-1 1 1 1 1 2 1v-1l1 1h1 1c1 1 1 2 2 3 2 2 5 7 8 8l1 2-3-1 1 2c-4 0-8-3-12-5 0-2 0-2-1-3h-1c1 2 1 3 1 5-2-3-2-7-3-10v-1z" class="E"></path><path d="M241 565c1 0 1 0 2-1 1 1 1 1 2 1v-1l1 1h1 1c1 1 1 2 2 3 2 2 5 7 8 8l1 2-3-1c-3-2-11-11-15-11v-1z" class="F"></path><defs><linearGradient id="Al" x1="298.379" y1="437.531" x2="265.569" y2="437.442" xlink:href="#B"><stop offset="0" stop-color="#d6d5d5"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Al)" d="M293 395v-1c3-1 5-2 7-3-1 3-2 10-4 13-1 0-2 1-3 1-1 1-3 2-5 3-2 6-1 13-2 19-1 19-1 41-10 58-1 0-1-1-1-1-1-2-1-3-3-4-2-3-5-8-6-12-1 0-1-1-2-2 1-7 7-11 12-16l3-3c2-4 1-9 2-13 0-8 1-17 2-25 1-3 6-6 8-9 1-1 2-3 2-5z"></path><path d="M266 468c4-5 8-10 14-14-1 4-1 8-3 12-1 3-5 7-5 10v4c-2-3-5-8-6-12z"></path><path d="M261 562c1 0 4 0 5-1 2-2 2-8 3-10l5 2 2-1c1 2 2 4 4 6 1 1 1 2 2 4 2 3 4 9 7 12 3 9 9 17 14 25l8 11c1 1 2 3 3 4 5 6 8 12 11 19 0 1 0 1-1 1-1-1 0-1-1-1v-1c-1-1 0-1-1-1h-4v2l1 1h-1l-9-12c-2-3-5-7-9-8-2-1-6-1-9-1-1-2-1-2-1-3s0-2 1-3c0-1 1-1 2-1 1-1 1-1 2-1-3-8-5-17-9-24-2-3-4-6-6-8-1-3-2-5-3-7-2-1-2-1-4-1-2 1-3 2-4 4-1 0-2-1-2 0l-2 1-2 1-1-1 1-2c-1-1-3-2-4-3h0c1-1 1-2 2-3z" class="J"></path><defs><linearGradient id="Am" x1="311.595" y1="626.292" x2="310.905" y2="614.708" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#dad8d8"></stop></linearGradient></defs><path fill="url(#Am)" d="M290 610h4l3 1c1 1 3 1 4 2s2 1 3 1c2-1 3-1 5-1 3 1 3 3 6 5 1 0 1 1 1 2 1 0 0 0 1 1 0-2-1-3-2-4s-1-2-1-2v-1c5 6 8 12 11 19 0 1 0 1-1 1-1-1 0-1-1-1v-1c-1-1 0-1-1-1h-4v2l1 1h-1l-9-12c-2-3-5-7-9-8-2-1-6-1-9-1-1-2-1-2-1-3z"></path><defs><linearGradient id="An" x1="342.212" y1="669.437" x2="319.282" y2="678.487" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#a8a7a7"></stop></linearGradient></defs><path fill="url(#An)" d="M318 634h1l-1-1v-2h4c1 0 0 0 1 1v1c1 0 0 0 1 1 1 0 1 0 1-1 5 9 10 18 14 27 3 6 5 14 9 19 3 10 0 27-5 36-2 4-4 7-6 9 0-2 1-5 2-8v-13c0-2-1-4-1-6l1-3c0-4-2-9-3-12-3-8-7-16-13-23-5-6-11-11-16-16l-7-5c1-1 2-1 3-1 2-1 4-1 5-3 2 1 4 2 5 4 7 4 13 11 18 18h0 1c-5-7-10-14-14-22z"></path><path d="M308 634c2 1 4 2 5 4h-1c-1-1-1-1-3-2-1 1-2 1-3 1-1 1-2 1-2 2 1 1 2 2 3 4l-7-5c1-1 2-1 3-1 2-1 4-1 5-3z" class="H"></path><path d="M260 563l1-1c-1 1-1 2-2 3h0c1 1 3 2 4 3l-1 2 1 1 2-1 2-1c0-1 1 0 2 0 1-2 2-3 4-4 2 0 2 0 4 1 1 2 2 4 3 7 2 2 4 5 6 8 4 7 6 16 9 24-1 0-1 0-2 1-1 0-2 0-2 1-1 1-1 2-1 3s0 1 1 3c3 0 7 0 9 1 4 1 7 5 9 8l9 12c4 8 9 15 14 22h-1 0c-5-7-11-14-18-18-1-2-3-3-5-4-7-6-14-11-21-18l-6-11-6-9c-1-3-2-6-3-8h0c-2-2-3-3-5-4l-2-2-6-4-1-2c-3-1-6-6-8-8v-3c3-3 7-2 10-2z"></path><path d="M278 575l1 3c0 3 2 5 2 8h-3c-2 1-4 1-6 2h0c-2-2-3-3-5-4l4-2 4-3c1-2 0-2 0-3h2l1-1z" class="D"></path><path d="M267 584l4-2c1 1 2 1 3 2l4 1h2c-1 1-1 1-2 1-2 1-4 1-6 2h0c-2-2-3-3-5-4z" class="B"></path><path d="M260 563l1-1c-1 1-1 2-2 3h0c1 1 3 2 4 3l-1 2 1 1 2-1 2-1c0-1 1 0 2 0 3 1 7 2 8 5 1 0 1 0 1 1l-1 1h-2c0 1 1 1 0 3l-4 3-4 2-2-2-6-4-1-2c-3-1-6-6-8-8v-3c3-3 7-2 10-2z"></path><path d="M267 573c-2 1-7 2-10 3 1-1 1-2 2-2 1-1 2-2 3-4h0l1 1 2-1c0 1 1 2 2 3z" class="D"></path><path d="M265 570l2-1c0-1 1 0 2 0 3 1 7 2 8 5 1 0 1 0 1 1l-1 1h-2c-2-2-5-3-6-4v-1h-1l-1 2c-1-1-2-2-2-3z" class="H"></path><path d="M260 563l1-1c-1 1-1 2-2 3h0c-2 0-2 0-4 1 0 3 1 5 2 8l1 2c-3-1-6-6-8-8v-3c3-3 7-2 10-2z" class="D"></path><path d="M223 328c-11-24-24-51-50-61-9-4-20-5-30-5 17-5 32-6 48-3 8 2 16 4 24 5 6 1 12 1 18 2h345 144l78-1c5 0 9 0 13-1 13-2 25-6 39-7 7 0 15 1 22 3 2 0 4 0 6 1-6 1-12 1-17 3-17 3-30 13-40 26-9 12-15 28-21 41l-6 14c2 1 1 4 2 6l3 15 1 1c1 5 1 10 2 14l3 22 1-1c0-1-1-2-1-2v-1-3-1-2h9-1c0 3 2 6 1 9h1c2 21 3 41 2 62-1 13-2 27-4 40-6 36-20 71-43 99-10 13-22 24-34 34l-16 13c-6 4-12 9-17 14-5 6-10 14-13 22-2 6-4 13-3 20s5 13 10 17c6 5 13 6 21 6 6-1 11-4 15-9 5-5 4-11 4-17l-1-2c4 6 7 11 6 18-1 8-6 15-12 20-12 8-28 10-42 8-22-4-41-19-53-37-6-8-10-16-14-25-14 2-27 4-40 4 18-9 28-24 35-43l9-32c4-19 7-39 8-58 2-26 1-52-3-78-3-18-8-36-12-53l-7-23 6-1 2-2-5-14c-1-6-2-14-5-19h-9-55v311 92 28c0 6 0 13 1 19 1 14 5 27 15 37 17 17 39 20 62 22l29 2-115 16h0-1c-3 0-7 2-9 3-1 1-2 1-3 2l-3 4v-8c0-6 1-13 2-20h-1c-1-3-2-7-2-10l1-12c1-6 1-11 3-17-1-1-1-16-1-19l-1 4h-1c-1-2-2-3-2-5 0 0 0-1-1-1 0-3 0-6-1-8-1 2 0 8-1 10v4l-1-15c1-3 0-6 0-8h0l-1 3c-2 0-7-3-10-3 3 3 6 5 7 9 1 6 1 12 1 18-1 14-1 28 0 42l-1 48-15-20-15-2-116-15c6-1 13-1 19-1 23-1 53-3 70-20 11-11 16-26 18-41 1-6 1-12 1-18v-31-87-312l-56-1-11 1c-4 0-9-1-13 0h-7l-12 1c-2 0-6 0-8 1-1 1-1 2-3 2h-4c-3 1-8 7-10 10l-1 1c-2 3-3 5-5 7s-5 5-6 7l1 3h0c-2-2-2-3-2-5-3 1-7 1-10 1-1 1-1 1-1 0v-3l2-9c0-2 0-3 2-4l-1-1-19 8c-1 1-8 3-9 5h-2c-2 1-4 2-7 3v1c0 2-1 4-2 5-2 3-7 6-8 9-1 8-2 17-2 25-1 4 0 9-2 13l-3 3c-5 5-11 9-12 16l-2-2c-3-3-7-7-8-10h0c-2-1-2-2-3-3l-7-7c-7-6-16-11-23-17-6-6-6-13-6-20l3-26 3-13v-1c1-4 4-22 6-24 0-1 1-1 1-2 0 0-1-2-1-3l-4-10z" class="J"></path><path d="M330 378l16-5h1c0 1-1 2-1 2-2 2-4 4-5 6l-1 1v-4-2c-3 0-6 2-9 3l-1-1z" class="C"></path><path d="M515 711c-1 2-4 5-5 7 0-6 1-13 2-19 1 3 0 8 1 10 0 1 2 2 2 2z" class="E"></path><path d="M711 503c1-1 1-1 2 0 3 0 4 4 6 7l1 2c0 1-1 2-1 4l-2 1h-1c-1-5-2-9-5-14z"></path><path d="M331 379c3-1 6-3 9-3v2 4 1h-2c-2-1-5 0-7 0h-2c0-2 0-3 2-4z" class="G"></path><path d="M711 385c1 1 8 3 9 5 1 4 3 7 3 11-1-2-1-3-2-4l-1-2v-1c-1 1-2 1-2 3 0 1-1 1-1 2v2l1 1v2 3l-7-22z" class="B"></path><path d="M518 556c1-9 0-19 2-28 1-5 0-12 0-18 0-16-1-33 0-49 1 16 0 32 1 47 0 7 1 14 2 21 0 7 0 14-1 21 0 4-1 8-1 12v-6c1-1 1-4 1-6v-1-16-6c-1-2 0-3-1-4v-2h0c0 3 0 8-1 12-1 1 0 3-1 5 0 7 0 14-1 20v1-3z" class="L"></path><path d="M742 455c3 2 7 4 9 7 2 2 3 4 5 6h1l-4 7-3 4v-3c0-3-3-9-5-11-1-4-2-7-3-10z"></path><defs><linearGradient id="Ao" x1="618.237" y1="400.727" x2="619.157" y2="413.318" xlink:href="#B"><stop offset="0" stop-color="#a8a7a7"></stop><stop offset="1" stop-color="#d0cfcf"></stop></linearGradient></defs><path fill="url(#Ao)" d="M621 399l4 16 3 9c-1-1-2-1-2-2v-3h-1l-1-4v-1h-6 0v2l1 1c0 1 0 2 1 4h0v1 3l-7-23 6-1 2-2z"></path><defs><linearGradient id="Ap" x1="350.996" y1="370.892" x2="351.568" y2="381.84" xlink:href="#B"><stop offset="0" stop-color="#3b3a3b"></stop><stop offset="1" stop-color="#545453"></stop></linearGradient></defs><path fill="url(#Ap)" d="M346 373c1-1 3-1 5-2 5-1 11-2 17-2-1 1-1 2-3 2h-4c-3 1-8 7-10 10l-1 1h-5l-4-1c1-2 3-4 5-6 0 0 1-1 1-2h-1z"></path><defs><linearGradient id="Aq" x1="645.197" y1="405.383" x2="653.44" y2="418.537" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#Aq)" d="M651 402c2 5 3 11 4 16 1 3 2 6 2 9h0-1v-1-2-1h-1c0-1 1 0 0-1v-1c0-1 0-1-1-1v-1h0c-1 1-2 1-3 1h0l-1 1c0 1-1 2 0 3 0 1 0 0 1 2v1c0 1 0 1 1 2h0v1 3l-7-25c1-1 1-2 2-3s3-2 4-3z"></path><defs><linearGradient id="Ar" x1="669.462" y1="371.231" x2="667.463" y2="379.961" xlink:href="#B"><stop offset="0" stop-color="#2d2b2c"></stop><stop offset="1" stop-color="#4b4b4b"></stop></linearGradient></defs><path fill="url(#Ar)" d="M658 369c3 0 19 2 20 4l4 1c0 3 1 4 2 6h-1 0c-3-1-5-1-8 0h-2v1l-1 1c-2-3-5-5-8-8-2-2-4-3-6-5z"></path><path d="M678 373l4 1c0 3 1 4 2 6h-1 0l-5-6v-1z" class="C"></path><path d="M526 835c2-3 5-6 7-9-2 5-4 11-5 16s-1 9-1 14c0 6-1 12-2 18h-1c-1-3-2-7-2-10l1-12c1-6 1-11 3-17z" class="I"></path><defs><linearGradient id="As" x1="687.36" y1="375.549" x2="686.298" y2="382.049" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#As)" d="M682 374c3 0 7 1 10 3v2c1 2 2 4 2 6-3 0-5 1-8 1-2 0-5-1-8-1h-2l-2-1 1 2-3-4 1-1v-1h2c3-1 5-1 8 0h0 1c-1-2-2-3-2-6z"></path><path d="M493 834c6 7 14 15 17 24 1 4 1 9 2 13-5-7-10-13-13-20-2-5-4-11-6-17zm33 64c6-13 11-24 22-32l-1 2c-4 6-6 12-7 19-1 2-1 4-1 5v1h0-1c-3 0-7 2-9 3-1 1-2 1-3 2z" class="I"></path><path d="M512 542l3 42c-1 2-3 3-4 5h-1-1c-1-16-1-32 3-47z" class="E"></path><path d="M483 892l-3-24 18 26-15-2z" class="C"></path><defs><linearGradient id="At" x1="515.236" y1="641.471" x2="513.194" y2="590.052" xlink:href="#B"><stop offset="0" stop-color="#a3a1a2"></stop><stop offset="1" stop-color="#dcdbda"></stop></linearGradient></defs><path fill="url(#At)" d="M515 588c1 4 1 9 1 13v37c0 3 0 3-2 4v16h0c0-2 0-5-1-7v-8-20c0-8-1-16-1-24 0-1-1-5-1-7l4-4z"></path><path d="M675 386l-1-2 2 1h2c3 0 6 1 8 1 3 0 5-1 8-1l1 6 1 6h-2c-3 1-7 1-10-1h0l-1 1c-1-2-2-3-3-5-2-1-4-4-5-6z" class="F"></path><path d="M680 392c3-2 11-1 15-1l1 6h-2c-3 1-7 1-10-1h0l-1 1c-1-2-2-3-3-5z" class="H"></path><defs><linearGradient id="Au" x1="344.483" y1="381.442" x2="341.809" y2="393.512" xlink:href="#B"><stop offset="0" stop-color="#545353"></stop><stop offset="1" stop-color="#8e8d8e"></stop></linearGradient></defs><path fill="url(#Au)" d="M341 381l4 1h5c-2 3-3 5-5 7s-5 5-6 7l1 3h0c-2-2-2-3-2-5-3 1-7 1-10 1-1 1-1 1-1 0v-3l2-9h2c2 0 5-1 7 0h2v-1l1-1z"></path><path d="M327 392c2-1 3-3 5-3 1 0 2 1 3 2h4v2 1h-1c-3 1-7 1-10 1-1 1-1 1-1 0v-3z" class="D"></path><defs><linearGradient id="Av" x1="527.451" y1="679.818" x2="502.457" y2="669.219" xlink:href="#B"><stop offset="0" stop-color="#5a5656"></stop><stop offset="1" stop-color="#a5a5a6"></stop></linearGradient></defs><path fill="url(#Av)" d="M513 651c1 2 1 5 1 7h0v-16c2-1 2-1 2-4l1 40v24c0 2 0 6-1 7 0 1 0 1-1 2h0s-2-1-2-2c-1-2 0-7-1-10 2-16 0-32 1-48z"></path><path d="M766 468l1 7c0 5 0 16-3 19-2 1-6 0-8 0l-1 1c-1 1-2 2-3 4h-2v-1c0-3 1-6 2-8 4-8 8-15 14-22z"></path><defs><linearGradient id="Aw" x1="497.652" y1="779.375" x2="534.093" y2="732.594" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#Aw)" d="M517 715c1 3 0 8 0 11v25c0 6 2 40 0 43-1 0-2 0-4-1v-7-13c0-16-1-32 1-48 0-3 1-6 3-10z"></path><defs><linearGradient id="Ax" x1="511.257" y1="801.626" x2="531.393" y2="788.632" xlink:href="#B"><stop offset="0" stop-color="#9b999a"></stop><stop offset="1" stop-color="#bfbebd"></stop></linearGradient></defs><path fill="url(#Ax)" d="M517 751c1 2 1 3 1 5v13 2h1c1 1 1 2 1 4 2 1 2 1 3 2l3 2v-2c0-2 0-3 1-4v4l-2 22c-1 6-1 12 0 17l-1 4h-1c-1-2-2-3-2-5 0 0 0-1-1-1 0-3 0-6-1-8-1 2 0 8-1 10v4l-1-15c1-3 0-6 0-8h0-1l1-2v-1c2-3 0-37 0-43z"></path><path d="M671 522c-1-18-9-39-18-55-2-5-7-9-9-15-2-4-2-9-3-14 3 3 5 6 7 8 3 5 6 8 8 13 6 10 10 21 13 32l4 27c1 7 2 14 1 21-1 1-1 3-2 4 0 1-1 1-2 1-1-3 0-9 1-12v-10z"></path><path d="M697 611c1-3 4-7 6-10 3-4 5-9 7-14 5-10 7-21 10-32l4-18 4-27c1 1 0 2 1 3v1c-1 0 0 2 0 3l-1 1c0 1 1 2 0 3 0 1 0 2-1 3v1 2c0 2 0 2 1 3l1-1h0v4l-1 1v3c0 1 0 1-1 3v2c0 1 0 1-1 3v1c0 1 0 1-1 2v2 3h1v-4h0c0-2 0-1 1-2v-2-1-1c1-1 1-1 1-2v-1h0c0-1 0-2 1-2v-2l1-1v3c0 1 2 1 2 1 1-2 0-3 0-5h1v-5c1-1 0-2 1-4v-2-1-1c1-1 1-2 1-3s-1-2-1-2l2-2c-1 7-6 45-5 47l-1 2c-3 7-5 17-10 23h0c0-2 1-2 1-3l1-2 1-1v-2c1-2 1-3 2-4v-1l1-1v-2h-1c-1 0-1 0-1 1-1 1-1 1-1 2-1 0-1 0-1 1s0 0-1 2h0c0 1-1 3-2 3h0c0 1 0 2-1 3v2l-1 1c0 1-1 2-1 3l-2 2c0 1 0 1-1 2v-2l-1 1h-2c-1 1-1 2-2 3 0 0 0 1-1 2v1l-2 4-4 5c-1 1 0 1-1 1 0 1 0 1-1 2l-1 2h-1z" class="B"></path><path d="M726 555c0 7-1 12-4 17h-1c0-5 3-12 5-17zm-6 31c5-6 7-16 10-23l2 8c0 2 1 4 0 6-1 4-4 7-5 10-3 3-5 7-7 11l-13 17c-2 3-4 6-5 10-3 6-5 12-8 18-3 5-7 10-9 15-4 6-5 12-7 19-1 7 1 14 0 21l-2-6c-2-6-1-12 0-18 5-34 28-60 44-88z"></path><path d="M702 625h0c0 3-1 5-2 7l3 3c1-2 2-4 4-5-2 4-4 9-7 14-2 3-5 7-7 10-5 7-8 14-10 22 2-4 4-9 6-13 4-7 9-15 15-21s14-11 20-16v1l-6 6 2 1c1 1 2 1 4 1l1-1c1-1 2-1 3-2l2-2h1c1-1 2-3 3-4h2 0c-6 6-13 11-20 17-5 4-9 9-14 14-8 10-18 22-17 36v2c0 2-1 4-1 6v15l3 9c-5-6-7-13-8-20-1-2-2-5-2-7h1c1-7-1-14 0-21 2-7 3-13 7-19 2-5 6-10 9-15 3-6 5-12 8-18z" class="B"></path><path d="M623 367h10l4 13 7 24 1 4 7 25 7 21c-3-6-8-13-13-18l-1 1-2-3v-1c-1-3 0-7-1-9l-3 1c0 1 0 1-1 3v-1c-6-7-8-14-9-23v-2c-2 0-3 0-4 1l-1-1v1h0c-1-5-1-11-1-16v-4c0-1 0-1-1-2 0-1 2-2 3-3s2-1 3-1c-1-3-1-6-2-9-1 0-2-1-3-1h0z"></path><defs><linearGradient id="Ay" x1="635.834" y1="404.655" x2="640.31" y2="423.928" xlink:href="#B"><stop offset="0" stop-color="#979697"></stop><stop offset="1" stop-color="#cbcacb"></stop></linearGradient></defs><path fill="url(#Ay)" d="M629 402l-1-10 2 7 3 3c0 1 0 2 1 3h1 3l1-1c2 6 3 12 4 17v9c1 1 0 3 1 3 0 1 2 2 2 3l-1 1-2-3v-1c-1-3 0-7-1-9l-3 1c0 1 0 1-1 3v-1c-6-7-8-14-9-23v-2z"></path><path d="M629 402l-1-10 2 7c3 9 8 18 8 28-6-7-8-14-9-23v-2z"></path><defs><linearGradient id="Az" x1="625.682" y1="376.662" x2="631.375" y2="405.421" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#a2a1a1"></stop></linearGradient></defs><path fill="url(#Az)" d="M623 387v-4c0-1 0-1-1-2 0-1 2-2 3-3s2-1 3-1l10 24 1 3-1 1h-3-1c-1-1-1-2-1-3l-3-3-2-7 1 10c-2 0-3 0-4 1l-1-1v1h0c-1-5-1-11-1-16z"></path><path d="M638 401l1 3-1 1h-3c2-1 2-1 3-4z" class="D"></path><path d="M616 366c2 0 5 0 7 1h0c1 0 2 1 3 1 1 3 1 6 2 9-1 0-2 0-3 1s-3 2-3 3c1 1 1 1 1 2v4c0 5 0 11 1 16h0v-1l1 1c1-1 2-1 4-1v2c1 9 3 16 9 23v1l-2-2c-1-1-1-2-2-3-1 1-1 2-2 2v1h0c1 0 1 1 1 2l2 1v1l1 1c1 0 1 0 1 1v1c1 1 1 1 1 2v1c0 1 0 3-1 5-1 9 5 18 9 25 3 4 5 9 7 14 4 8 6 16 8 24 1 4 3 9 3 12h-1c-6 0-10 1-14 5l-1 1 2-4c2-6 2-13 2-19 0-3 1-8 0-11-1-4-3-8-5-12l-2 35c-1 8 0 15-2 23-1-4 0-8 0-11v-21c-1-9-2-19-4-28-2-16-6-33-11-49v-1l-3-9-4-16-5-14c-1-6-2-14-5-19h5z"></path><defs><linearGradient id="BA" x1="619.417" y1="367.389" x2="625.539" y2="377.948" xlink:href="#B"><stop offset="0" stop-color="#212020"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#BA)" d="M616 366c2 0 5 0 7 1h0c1 0 2 1 3 1 1 3 1 6 2 9-1 0-2 0-3 1s-3 2-3 3c1 1 1 1 1 2v4l-4-13c-1-3-3-5-3-8z"></path><defs><linearGradient id="BB" x1="623.127" y1="404.442" x2="635.093" y2="421.878" xlink:href="#B"><stop offset="0" stop-color="#9b9b9a"></stop><stop offset="1" stop-color="#cdcbcd"></stop></linearGradient></defs><path fill="url(#BB)" d="M624 403v-1l1 1c1-1 2-1 4-1v2c1 9 3 16 9 23v1l-2-2c-1-1-1-2-2-3-1 1-1 2-2 2v1h0c1 0 1 1 1 2l2 1v1l1 1c1 0 1 0 1 1v1c1 1 1 1 1 2v1c0 1 0 3-1 5 0-2 0-7-1-8-1-2-3-4-4-7-5-6-7-15-8-23z"></path><path d="M625 403c1-1 2-1 4-1v2l-1-1h-3z" class="D"></path><path d="M692 377c2 1 19 8 19 8l7 22c4 14 6 28 8 42 2 9 3 20 2 29-2-1-4-2-6-4-1-1-3-3-5-3 0 0-1 0-1 1-1 1-1 1-1 3l5 5-19 3 14-20c2-2 4-4 5-7 2-5 2-15 1-21s-3-12-6-18c-2-3-4-7-5-11-2-4-2-9-3-13-2 4-3 8-5 13 4 11 10 20 11 32 0 2 1 5 0 7-1 3-6 6-8 7-2-1-2-1-3-2l3-6c1-6-1-13-2-19 0-3 0-7-1-10s-2-6-3-8c-2-3-2-7-3-10l-1-6-1-6c0-2-1-4-2-6v-2zm-42 22l-3-6c2 3 5 6 6 9 1 2 1 4 1 7 2 7 5 21 12 25l3 2c6 4 12 2 17 10 0 1 2 4 1 4 0 1-1 2-1 3-2 2-4 4-5 7h1c3-1 5-3 7-6h0c4 1 8 8 10 11l13-10c-4 9-10 15-16 23-2-3-4-6-6-8-5-4-11-8-15-13 1-5 4-9 5-13-3 2-6 7-8 10-1-2-3-7-3-10l1-1h-1l-1 1c1 7 3 16 8 21 2 3 6 6 8 9 4 3 6 6 9 10l-6 9v-5c-1-5-6-8-9-10l1 3c2 4 2 8 2 13 1 8 1 16-2 23-4-9-3-19-5-29-2-7-4-13-6-19l-4-20c-1-3-3-7-4-10l-3-12c0-3-1-6-2-9-1-5-2-11-4-16l-1-3zm128 82l-4-21c10-13 25-21 37-32 1 7 1 16 0 23l-1 1c-3-2-3-3-4-6-1-2-3-3-4-5-4 3-8 6-11 9v4h0c2 0 4 0 5 1 6 5 8 12 9 19 0 5-1 9-2 14-3-4-5-7-7-11-1-3-3-8-5-10-4 1-6 5-7 7s-2 3-1 5c1 1 2-1 4-1 2-1 4 1 5 2 3 4 5 11 4 16 0 3 0 5-1 8l-1 2-4 10c-3-1-8-1-11-1h-1c-1-2-2-5-3-7-1-6 0-13-2-20v-1h1l3 5c1 0 2 0 3-1v-2h0l-1-1v-1c-1-1-1-1-1-2h0v-4z"></path><defs><linearGradient id="BC" x1="785.087" y1="508.113" x2="780.374" y2="490.898" xlink:href="#B"><stop offset="0" stop-color="#999798"></stop><stop offset="1" stop-color="#cbc9c9"></stop></linearGradient></defs><path fill="url(#BC)" d="M778 481c2 6 4 10 7 16 1 1 4 5 4 6 0 2-2 6-3 7l-12-21-1-1v-1h1l3 5c1 0 2 0 3-1v-2h0l-1-1v-1c-1-1-1-1-1-2h0v-4z"></path><defs><linearGradient id="BD" x1="581.749" y1="742.879" x2="462.529" y2="557.227" xlink:href="#B"><stop offset="0" stop-color="#c7c6c0"></stop><stop offset="1" stop-color="#f7f6fb"></stop></linearGradient></defs><path fill="url(#BD)" d="M518 556v3-1c1-6 1-13 1-20 1-2 0-4 1-5 1-4 1-9 1-12h0v2c1 1 0 2 1 4v6 16 1c0 2 0 5-1 6v6l2 20c0 4 1 8 1 12 1 9-3 18 0 27l2 4c1 5-1 13-1 19-1 9-2 18-2 26 0 9 3 17 3 26 0 17-3 33-2 50 0 4 0 10 2 14 0 2 2 3 2 4v4c0 3 0 6-1 9v-4c-1 1-1 2-1 4v2l-3-2c-1-1-1-1-3-2 0-2 0-3-1-4h-1v-2-13c0-2 0-3-1-5v-25c0-3 1-8 0-11l-1-4h-1c1-1 1-1 1-2 1-1 1-5 1-7v-24l-1-40v-37c0-4 0-9-1-13 2-3 2-7 2-10v-10c0-4 0-8 1-12z"></path><path d="M433 309l42-1v32h-36-20 0c-1 0-3 0-4-1 0-3 1-6 2-9l6-18v-1c1-2 2-2 4-3l6 1z"></path><defs><linearGradient id="BE" x1="418.392" y1="319.707" x2="432.091" y2="327.234" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#BE)" d="M427 308l6 1 3 1c0 5-2 11-4 16-2 4-3 8-5 12-1 1-1 1-2 1h0l14 1h-20 0c-1 0-3 0-4-1 0-3 1-6 2-9l6-18v-1c1-2 2-2 4-3z"></path><defs><linearGradient id="BF" x1="322.839" y1="315.819" x2="331.901" y2="356.516" xlink:href="#B"><stop offset="0" stop-color="#060506"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#BF)" d="M352 310h8c0 8-7 17-10 25-2 4-3 9-4 14 0 2-1 4-1 6-3 3-7 6-11 9l-17 9-11 6-3 3c0-5 3-11 4-16l4-12c3-5 4-12 5-17 3-7 6-15 8-22l7-2 13-2 8-1z"></path><path d="M313 359h1l-1 1h5c1-1 2-1 3 0l-1 1-8 1 1-3z" class="F"></path><path d="M331 313l13-2c0 1 0 3-1 4 0-1-1-1-2-2h-5c-1 0-3 2-4 3s-1 3-3 4l3-6-1-1z" class="C"></path><path d="M344 311l8-1c0 3-3 6-4 9l-9 21c-3 1-7 2-10 5-1 1-2 4-4 4 5-11 11-24 18-34 1-1 1-3 1-4z"></path><path d="M325 349c2 0 3-3 4-4 3-3 7-4 10-5 0 0 0 3-1 3-2 7-4 14-4 21l-17 9c0-2 1-5 2-7l1-5 1-1h0c1-4 2-8 4-11z"></path><path d="M325 349c2 0 3-3 4-4 3-3 7-4 10-5 0 0 0 3-1 3-3 1-6 4-7 6l-6 9c-1 3-3 6-6 8l1-5 1-1h0c1-4 2-8 4-11z" class="C"></path><path d="M324 315l7-2 1 1-3 6c-5 8-8 18-12 27-1 4-3 8-4 12l-1 3 8-1-1 5c-1 2-2 5-2 7l-11 6-3 3c0-5 3-11 4-16l4-12c3-5 4-12 5-17 3-7 6-15 8-22z"></path><defs><linearGradient id="BG" x1="313.358" y1="361.099" x2="313.643" y2="375.911" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#BG)" d="M312 362l8-1-1 5c-1 2-2 5-2 7l-11 6 6-17z"></path><defs><linearGradient id="BH" x1="355.096" y1="309.045" x2="337.447" y2="362.473" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#3c3b3c"></stop></linearGradient></defs><path fill="url(#BH)" d="M352 310h8c0 8-7 17-10 25-2 4-3 9-4 14 0 2-1 4-1 6-3 3-7 6-11 9 0-7 2-14 4-21 1 0 1-3 1-3l9-21c1-3 4-6 4-9z"></path><path d="M360 310c9-1 19-1 28-1l39-1c-2 1-3 1-4 3v1l-6 18c-1 3-2 6-2 9 1 1 3 1 4 1h0c-4 1-10 0-15 0h-19c-2 0-4 0-6-1-6-2-11-7-14-12h-1c-3 11-10 20-19 28 0-2 1-4 1-6 1-5 2-10 4-14 3-8 10-17 10-25z"></path><path d="M364 327v-2-1c-1-2 2-8 4-10 1-2 2-2 3-2l1 1c-1 3-3 8-5 11l-2 2v1h-1zm15 12v-1-11c0-2-1-4 0-5 2 0 4 1 6 2l10-3c3 6 5 10 4 17h-4c-2-3 2-8-3-8-2 0-4 0-5 1s-2 7-2 8h0v1c-2 0-4 0-6-1zm25 1v-11c-1-8-6-12-12-17 2 0 6 0 8 1 3 2 5 6 6 9v1c1 3 1 6 1 9l6-15c1-1 2-5 3-6 0 0 1 0 2-1h0 0c1 0 3-1 4 0l1 2-6 18c-1 3-2 6-2 9 1 1 3 1 4 1h0c-4 1-10 0-15 0z" class="C"></path><path d="M734 560c2-2 4-7 7-8 1 0 2-1 3 0 1 0 2 1 3 1 2 1 4-1 5-2 1 3 3 7 4 10 3 1 7 1 9 3l1 1c-4 2-5-1-7 4 0 2 2 3 4 4h-13c-1 1-2 1-2 3s0 4 1 5l1 1 5 1c-2 1-6 3-7 5 1 1 1 2 0 4-1 4-3 7-5 11s-5 8-7 12c-4 4-8 8-12 11-6 5-14 10-20 16s-11 14-15 21c-2 4-4 9-6 13 2-8 5-15 10-22 2-3 5-7 7-10 3-5 5-10 7-14-2 1-3 3-4 5l-3-3c1-2 2-4 2-7h0c1-4 3-7 5-10l13-17c2-4 4-8 7-11 1-3 4-6 5-10 1-2 0-4 0-6l-2-8 1-2h0c1 0 2-1 3-1z"></path><path d="M729 606c1 0 1 0 2 1s1 3 1 5c0 0 0 1-1 1-3 1-6 1-10 1 2-2 4-2 6-4 2-1 2-2 2-4zm13-22c0-2 1-4 2-5 1-3 0-5 2-7 2-1 3-1 5-1l-1 1v1c-1 1-2 1-2 3s0 4 1 5l1 1 5 1c-2 1-6 3-7 5l-6-2v-2z" class="E"></path><path d="M750 582l5 1c-2 1-6 3-7 5l-6-2v-2c3-1 5 0 8-2z" class="B"></path><path d="M734 560c2-2 4-7 7-8 1 0 2-1 3 0 1 0 2 1 3 1 2 1 4-1 5-2 1 3 3 7 4 10 3 1 7 1 9 3l1 1c-4 2-5-1-7 4 0 2 2 3 4 4h-13v-1l1-1h0c1-1 1-2 1-3-1-2-2-2-4-3-1 0-1 0-2 1-2 2-3 5-5 7-2 4-5 8-7 12-3 6-5 13-7 19 0 1 1 1 2 2 0 2 0 3-2 4-2 2-4 2-6 4l-2 2c-5 3-9 9-12 14-2 1-3 3-4 5l-3-3c1-2 2-4 2-7h0c1-4 3-7 5-10l13-17c2-4 4-8 7-11 1-3 4-6 5-10 1-2 0-4 0-6l-2-8 1-2h0c1 0 2-1 3-1z" class="J"></path><path d="M734 560c1 3 1 8-1 10l-1 1-2-8 1-2h0c1 0 2-1 3-1z" class="E"></path><path d="M314 318l10-3c-2 7-5 15-8 22-1 5-2 12-5 17l-4 12c-1 5-4 11-4 16-2 2-5 4-7 5h-1c-1 1-2 2-3 2-2-7-8-16-13-21l-1-1c-4-2-7-1-10-1h-1c-1 1-2 1-3 2l-1 2-2 2c-1 1-1 2-2 3l-1-1c2-6 5-12 9-18 2-4 4-8 7-12l5-5c5-6 14-12 21-15v-1c4-2 10-4 14-5z"></path><defs><linearGradient id="BI" x1="279.047" y1="366.874" x2="294.223" y2="383.42" xlink:href="#B"><stop offset="0" stop-color="#636363"></stop><stop offset="1" stop-color="#a1a0a2"></stop></linearGradient></defs><path fill="url(#BI)" d="M282 364l1 2c5 5 10 14 11 21h1c-1 1-2 2-3 2-2-7-8-16-13-21l-1-1v-1c1-1 2-2 4-2z"></path><path d="M282 364l1 2c-1 0-2 0-3 1-1 0-1 0-1 1l-1-1v-1c1-1 2-2 4-2z" class="K"></path><defs><linearGradient id="BJ" x1="264" y1="361.132" x2="275.526" y2="369.888" xlink:href="#B"><stop offset="0" stop-color="#272627"></stop><stop offset="1" stop-color="#585759"></stop></linearGradient></defs><path fill="url(#BJ)" d="M258 374c2-6 5-12 9-18l2 1c5 0 9 3 13 7-2 0-3 1-4 2v1c-4-2-7-1-10-1h-1c-1 1-2 1-3 2l-1 2-2 2c-1 1-1 2-2 3l-1-1z"></path><defs><linearGradient id="BK" x1="301.176" y1="364.632" x2="297.047" y2="385.21" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#bab8b8"></stop></linearGradient></defs><path fill="url(#BK)" d="M295 369c1-1 2-2 2-3h1 3 3 3c-1 5-4 11-4 16-2 2-5 4-7 5-1-6-2-13-1-18z"></path><path d="M300 324v-1l2 1c2 7 3 14 3 20h-1c-4-1-8-2-11 0-5 3-6 11-10 13-1 0-2 0-3-1l3-9c-3-2-6-2-9-3l5-5c5-6 14-12 21-15z" class="C"></path><path d="M279 339c5-6 14-12 21-15-1 3-4 5-6 7-1 3-3 7-5 8s-6 0-8 0h-2z"></path><defs><linearGradient id="BL" x1="312.191" y1="316.104" x2="310.329" y2="359.918" xlink:href="#B"><stop offset="0" stop-color="#060506"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#BL)" d="M314 318l10-3c-2 7-5 15-8 22-1 5-2 12-5 17l-4 12h-3-3-3-1c0 1-1 2-2 3 1-4 4-8 6-12l4-12 7-21c1-1 2-4 2-5v-1z"></path><defs><linearGradient id="BM" x1="302.65" y1="359.19" x2="302.878" y2="365.611" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#BM)" d="M301 357v1 2c2 0 5 0 7-1 1-1 1-2 2-3l1-2h0l-4 12h-3-3-3-1c0 1-1 2-2 3 1-4 4-8 6-12z"></path><path d="M221 367c1-4 4-22 6-24 0-1 1-1 1-2 10 25 17 49 20 76l2-12v-5c2 4 3 7 5 10 1 3 1 5 1 7l-1 1c0 5-3 9-6 13-1 1-3 3-3 5-1 2 0 8 1 10 1 3 5 5 7 8-2-1-2-2-3-3l-7-7c-7-6-16-11-23-17-6-6-6-13-6-20l3-26 3-13v-1zm412 0h11l14 2c2 2 4 3 6 5 3 3 6 5 8 8l3 4c1 2 3 5 5 6 1 2 2 3 3 5l1-1h0c3 2 7 2 10 1h2c1 3 1 7 3 10 1 2 2 5 3 8s1 7 1 10c0 0-1-1-1-2v-4-2c-1-1-1-1-1-2v-1l-1-2-3-6c-2 1-3 2-4 3v2l1 1v1 1c1 2 0 1 1 2 0 0 1 1 1 2v1c1 1 1 2 1 2v2h0v2c1 1 0 2 0 3l-1 14c0 2 0 5-1 7-1 0-1 0-2-1-5-2-3-12-10-14-4-1-8 0-12-1h-3-1c0-1 0-1-1-1v1c0 1 1 1 2 1l1 2-3-2c-7-4-10-18-12-25 0-3 0-5-1-7-1-3-4-6-6-9l3 6 1 3c-1 1-3 2-4 3s-1 2-2 3l-1-4-7-24-4-13z"></path><defs><linearGradient id="BN" x1="669.332" y1="388.297" x2="668.653" y2="395.531" xlink:href="#B"><stop offset="0" stop-color="#7b797a"></stop><stop offset="1" stop-color="#999899"></stop></linearGradient></defs><path fill="url(#BN)" d="M661 396l1-1 9-10c2 3 4 6 5 10-1 1-2 1-4 1-2-1-8 0-11 0z"></path><defs><linearGradient id="BO" x1="639.783" y1="367.537" x2="639.225" y2="379.297" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#BO)" d="M633 367h11c0 4 1 8 2 12 1 3 3 6 4 9l-2-2c-1-1-2-2-2-3v-1c-1-1-1-2-3-2-2-1-4-1-6 0l-4-13z"></path><defs><linearGradient id="BP" x1="663.711" y1="394.152" x2="656.951" y2="428.2" xlink:href="#B"><stop offset="0" stop-color="#999898"></stop><stop offset="1" stop-color="#cfcdce"></stop></linearGradient></defs><path fill="url(#BP)" d="M658 396h3c3 0 9-1 11 0-4 2-8 8-9 12-3 6-2 14 1 20 1 2 4 4 7 5h-3-1c0-1 0-1-1-1v1c0 1 1 1 2 1l1 2-3-2c-7-4-10-18-12-25 1-1 1-2 1-3h0c1-3 1-4 2-6 0-1 1-2 1-3h0v-1z"></path><defs><linearGradient id="BQ" x1="646.521" y1="378.647" x2="648.884" y2="403.633" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient></defs><path fill="url(#BQ)" d="M637 380c2-1 4-1 6 0 2 0 2 1 3 2v1c0 1 1 2 2 3l2 2c2 3 4 5 7 7l1 1v1h0c0 1-1 2-1 3-1 2-1 3-2 6h0c0 1 0 2-1 3 0-3 0-5-1-7-1-3-4-6-6-9l3 6 1 3c-1 1-3 2-4 3s-1 2-2 3l-1-4-7-24z"></path><path d="M650 399l1 3c-1 1-3 2-4 3s-1 2-2 3l-1-4h1l5-5z" class="D"></path><defs><linearGradient id="BR" x1="681.864" y1="396.907" x2="689.335" y2="426.919" xlink:href="#B"><stop offset="0" stop-color="#b2b0b1"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#BR)" d="M683 397l1-1h0c3 2 7 2 10 1h2c1 3 1 7 3 10 1 2 2 5 3 8s1 7 1 10c0 0-1-1-1-2v-4-2c-1-1-1-1-1-2v-1l-1-2-3-6c-2 1-3 2-4 3v2l1 1v1 1c1 2 0 1 1 2 0 0 1 1 1 2v1c1 1 1 2 1 2v2h0v2c1 1 0 2 0 3-2-9-5-20-10-28-2 4-3 8-2 11 1 5 6 8 9 10-2 2-5 4-6 7v1h-1c-3-2-6-3-9-4l-1-2c0-1-2-2-4-3 1-1 2-3 3-4 2-3 2-6 3-9l4-10z"></path><path d="M261 372l2-2 1-2c1-1 2-1 3-2h1c3 0 6-1 10 1l1 1c5 5 11 14 13 21-1 2-2 4-2 7h0l3-1c0 2-1 4-2 5-2 3-7 6-8 9-1 8-2 17-2 25-1 4 0 9-2 13l-3 3c-5 5-11 9-12 16l-2-2c-3-3-7-7-8-10h0c-2-3-6-5-7-8-1-2-2-8-1-10 0-2 2-4 3-5 3-4 6-8 6-13l1-1c0-2 0-4-1-7-2-3-3-6-5-10l1-2c1-9 4-16 7-24l1 1c1-1 1-2 2-3z"></path><path d="M272 377c0-1-1-4 0-5 3 0 4 3 6 5h-6z" class="F"></path><defs><linearGradient id="BS" x1="270.53" y1="379.854" x2="274.206" y2="395.126" xlink:href="#B"><stop offset="0" stop-color="#939292"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#BS)" d="M272 377h6s1 1 1 2-1 3-1 4c-2 3-4 6-3 10 0 3 1 6 2 8h-1c-2-2-1-6-2-7l-5 3v3l-1 3h-1v-1c2-3 0-7-1-10 0-3-1-8 1-11h1 1c2-1 2-2 3-4z"></path><path d="M259 375c1-1 1-2 2-3 2 4 5 6 7 9h-1c-2 3-1 8-1 11 1 3 3 7 1 10v1c-2 2-4 5-6 7l-2-3-1 1c0 1 0 2-1 3v-1h-2c-2-3-3-6-5-10l1-2c1-9 4-16 7-24l1 1z"></path><path d="M251 398c3 2 6 6 8 9l-1 1c0 1 0 2-1 3v-1h-2c-2-3-3-6-5-10l1-2z" class="E"></path><defs><linearGradient id="BT" x1="258.126" y1="378.414" x2="266.498" y2="381.842" xlink:href="#B"><stop offset="0" stop-color="#656464"></stop><stop offset="1" stop-color="#7e7d7d"></stop></linearGradient></defs><path fill="url(#BT)" d="M259 375c1-1 1-2 2-3 2 4 5 6 7 9h-1c-2 3-1 8-1 11l-3-5c-3-1-4-3-6-5 0-3 1-5 2-7z"></path><defs><linearGradient id="BU" x1="271.009" y1="393.296" x2="254.358" y2="452.242" xlink:href="#B"><stop offset="0" stop-color="#d2d1d1"></stop><stop offset="1" stop-color="#f8f6f7"></stop></linearGradient></defs><path fill="url(#BU)" d="M267 403h1l1-3v-3l5-3c1 1 0 5 2 7h1l2 5c-2 2-4 3-7 5 3 2 5 3 5 7-1 6-3 14-8 19-5 4-12 8-14 15 0 1-1 1-1 2h0c-2-3-6-5-7-8-1-2-2-8-1-10 0-2 2-4 3-5 3-4 6-8 6-13l1-1c0-2 0-4-1-7h2v1c1-1 1-2 1-3l1-1 2 3c2-2 4-5 6-7z"></path><path d="M272 412c1 0 1 0 2 1s1 2 1 4c-2 11-8 17-17 23-1 1-1 1-2 1v-1c0-6 5-17 9-21 1-1 2-2 3-4 1-1 2-2 4-3zm394-102c14 1 28 2 42 5h1l15 5 4 2c8 5 16 9 22 16l4 5 6 10c-3 0-4 2-7 3s-6 2-9 4c-1 1-3 3-4 5-4 5-8 10-10 16-1 2-1 5-3 5-2-1-5-2-7-4l-4-3c-3-2-8-4-11-7-6-3-12-6-18-11 1-3 0-5-1-7 0-4-1-7-2-11-2-7-6-14-8-21-1-2-1-3-2-5 0-1-1-2-2-2 0-1-1-2-1-2-1-1 0-1 0-2-2-1-3 0-5-1h0z"></path><path d="M685 340c1 0 2 0 3 1 4 2 6 4 8 8 2 6 6 12 6 18-2-1-3-5-4-7-1-3-3-7-5-10s-6-4-7-7c-1-1-1-1-1-3z" class="C"></path><defs><linearGradient id="BV" x1="706.88" y1="363.253" x2="710.807" y2="376.918" xlink:href="#B"><stop offset="0" stop-color="#807f80"></stop><stop offset="1" stop-color="#abaaa9"></stop></linearGradient></defs><path fill="url(#BV)" d="M703 364c1-2 2-3 4-4l2-1 7 20c-3-2-8-4-11-7l-2-8z"></path><defs><linearGradient id="BW" x1="707.65" y1="313.665" x2="712.761" y2="360.517" xlink:href="#B"><stop offset="0" stop-color="#040304"></stop><stop offset="1" stop-color="#565656"></stop></linearGradient></defs><path fill="url(#BW)" d="M713 360l-14-42v-1-1c3-4 5 0 9-1h0 1l-1 2 15 43h-5-5z"></path><defs><linearGradient id="BX" x1="692.494" y1="315.764" x2="698.048" y2="362.587" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#BX)" d="M703 364c-7-15-13-31-22-45l1-1h1c2-1 3-2 6-2h2c0-2-1-2 0-3 2 4 4 9 6 14 4 10 9 21 12 32l-2 1c-2 1-3 2-4 4z"></path><path d="M709 315l15 5 4 2c8 5 16 9 22 16l4 5 6 10c-3 0-4 2-7 3s-6 2-9 4c-1 1-3 3-4 5-4 5-8 10-10 16-1 2-1 5-3 5-2-1-5-2-7-4 0-2-1-4-1-5l-4-13-2-4h5 5l-15-43 1-2z"></path><defs><linearGradient id="BY" x1="720.361" y1="363.473" x2="724.853" y2="384.742" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#b2b1b1"></stop></linearGradient></defs><path fill="url(#BY)" d="M713 360h5 5l1 2c1 2 3 5 4 7 1 4 0 13-1 17-2-1-5-2-7-4 0-2-1-4-1-5l-4-13-2-4z"></path><path d="M713 360h5 5l1 2v2h-4-5 0l-2-4z" class="F"></path><path d="M724 320l4 2c8 5 16 9 22 16l4 5c-5 1-10 1-15 2l5 10-5 3c-3-6-6-11-11-15l-9 2c-1-7 0-13 1-19 1-3 2-4 4-6z" class="C"></path><path d="M728 322c8 5 16 9 22 16l-1 1c-5 0-9 0-13 1-3-4-9-8-9-13 0-2 1-3 1-5z"></path><defs><linearGradient id="BZ" x1="643.253" y1="309.499" x2="643.247" y2="358.995" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2929"></stop></linearGradient></defs><path fill="url(#BZ)" d="M549 340h-1l-1-1v-31l64 1 55 1h0c2 1 3 0 5 1 0 1-1 1 0 2 0 0 1 1 1 2 1 0 2 1 2 2 1 2 1 3 2 5 2 7 6 14 8 21 1 4 2 7 2 11 1 2 2 4 1 7-13-9-23-21-29-36-2 6-7 11-13 14h0c-4 1-8 1-12 1h-15-69z"></path><g class="C"><path d="M618 340l-1-1c0-1 0-1 1-2v-3c0-7-1-13 3-18 2-3 3-4 6-4 1 1 0 2-1 2-3 3-4 6-5 9s-1 6-1 9c-1 2 0 4-1 6 0 1 0 1-1 2z"></path><path d="M622 339v-1-1c1-2 0-5 1-7v-1c1-3 2-5 4-7 2 0 4 1 7 1 0 1 3 2 3 2 2-1 6-4 7-5l-1 13c0 1 0 3-1 4l1 2 1-1 1 1c-4 1-8 1-12 1h-15 0c1-1 1-1 1-2 1-2 0-4 1-6 0-3 0-6 1-9 0 2-1 6-1 7v8 1h2 0z"></path></g><path d="M621 323c0 2-1 6-1 7v8 1h2 0c2 0 4 0 5-1 0-3 0-6 1-8 1-1 2-1 3-1 4 2 6 7 8 10l-21 1c1-1 1-1 1-2 1-2 0-4 1-6 0-3 0-6 1-9zm-72 17h-1l-1-1v-31l64 1-11 1 1 3v3c0 1 1 1 1 2v1c2 6 4 14 7 20l-1 1h-4c0-1 0-2-1-3l-4-13c0-1 0-1-1-2 0 2 1 5 2 7l2 5v1c0 1 1 2 1 3v1h0-6-1v-1c-1-1-1-1-1-2l-2-5-2-7-1-1c0 2 1 5 2 7l3 10h-29c-6 0-11-1-17 0zm247 5c2 1 1 4 2 6l3 15 1 1c1 5 1 10 2 14l3 22c0 6 0 13-3 18-2 5-6 8-11 11l-15 12c-7 6-13 14-20 22h0c1-7-8-12-12-16-1 0-2-2-3-2-1-2-1-7-1-9l-2-23c-1-2 0-6-1-8-1-1-2-2-3-2-2-2-4-4-5-6l-2-5 5 3-4-8c-1-1-2-2-3-4 2 0 2-3 3-5 2-6 6-11 10-16 1-2 3-4 4-5 3-2 6-3 9-4s4-3 7-3c4 6 7 12 9 19 3 11 4 23 5 35 0 3 0 8 1 11 1-10 4-21 7-31 4-15 9-29 14-42z"></path><path d="M744 377c1-2 4-4 7-6-1 2-1 4-1 6h-6zm14-1c3 2 5 4 7 6l-6 6-3-9 2-3z" class="F"></path><defs><linearGradient id="Ba" x1="738.265" y1="367.402" x2="730.303" y2="386.542" xlink:href="#B"><stop offset="0" stop-color="#7b7a7a"></stop><stop offset="1" stop-color="#afaeae"></stop></linearGradient></defs><path fill="url(#Ba)" d="M740 365c2 1 2 1 3 3-6 5-11 15-13 22-1-1-2-2-3-4 2 0 2-3 3-5 2-6 6-11 10-16z"></path><defs><linearGradient id="Bb" x1="751.91" y1="377.933" x2="747.177" y2="395.228" xlink:href="#B"><stop offset="0" stop-color="gray"></stop><stop offset="1" stop-color="#c3c1c1"></stop></linearGradient></defs><path fill="url(#Bb)" d="M744 377h6c1 2 2 2 3 4 1 0 2-1 3-2l3 9c-2 4-4 7-4 11 0 1 0 2 1 3h-1c-1-1-1-1-1-2s0-2-1-3c-1 0-2 0-3-1h-2v2h-1c0-3 1-7 0-11-1-2-3-4-3-6-1-1-1-2 0-4z"></path><defs><linearGradient id="Bc" x1="762.719" y1="356.892" x2="750.526" y2="371.195" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#Bc)" d="M760 353c4 6 7 12 9 19-1 0-1-1-1-2l-1-1c-1 0 0 1 0 2-1 4-1 7-2 11-2-2-4-4-7-6l1-1c2-3 2-5 2-8l-1-1c-4-1-13 0-17 2-1-2-1-2-3-3 1-2 3-4 4-5 3-2 6-3 9-4s4-3 7-3z"></path><defs><linearGradient id="Bd" x1="745.162" y1="398.514" x2="781.551" y2="438.813" xlink:href="#B"><stop offset="0" stop-color="#d1d0d0"></stop><stop offset="1" stop-color="#fffefe"></stop></linearGradient></defs><path fill="url(#Bd)" d="M747 398h1v-2h2c1 1 2 1 3 1 1 1 1 2 1 3s0 1 1 2h1l6 9c3-5 6-9 9-14h1c1 5-2 8-4 12-1 3-2 5-2 8 2 6 5 11 9 16 1 0 1 1 2 1l-1 2v8c-2 4-6 7-9 10-1-9-11-13-16-19-4-4-5-9-6-15v-5c0-1 1-1 2-2 0 4 1 8 2 12 3 8 11 13 18 16-1-5-3-10-6-15-1-2-3-4-4-6-2-3-4-5-6-8-1 1-2 1-3 1 1-2 1-2 0-3-1-2-3-2-5-3h0c1-3 3-5 4-9z"></path><path d="M720 390c1 1 3 1 3 3 1 4 1 9 4 12 2 1 6 3 7 4 0 1 0 2 1 3 0 6 1 12 1 18 1 12 1 23 4 34 1 7 3 14 6 20 1 1 0 1 0 2l1 2-1 1v1c0 1 0 0-1 1v2c-1 0-1 1-1 2h0l-1 1v1 2h-1v2c0 1 0 1-1 1 0 1 0 2-1 3v3c1 1 1 0 2 1s1 2 3 2c0-1 0-2 1-3h1c1-1 2-2 3-2l3-2 1-1s0-1 2-1h0c1-1 1-1 2-1h1c0-1 1 0 1 0h1l1-1c1 1 2 1 3 2 0 0 1 1 2 1 1 1 5 0 7 0v-2-4h-1v-2c1-2 0-3 0-5l-1-1 1-2v1c2 7 1 14 2 20 1 2 2 5 3 7h1c3 0 8 0 11 1l4-10v3 1l-1 1v2l-1 1h0v1c-1 1-1 2-2 3v1h1 0 2 1c1-1 2-1 2-2v-1l2-4 1-2c1-2 0-1 1-2v-1h1c2-6 3-12 5-18 1-8 3-16 0-25l-3-10 7 2c1 43-10 89-32 126-9 15-19 29-31 41-9 9-18 17-27 24l-15 12c-10 9-16 23-20 36v-2c-1-14 9-26 17-36 5-5 9-10 14-14 7-6 14-11 20-17h0-2c-1 1-2 3-3 4h-1l-2 2c-1 1-2 1-3 2l-1 1c-2 0-3 0-4-1l-2-1 6-6v-1c4-3 8-7 12-11 2-4 5-8 7-12s4-7 5-11c1-2 1-3 0-4 1-2 5-4 7-5l-5-1-1-1c-1-1-1-3-1-5s1-2 2-3h13c-2-1-4-2-4-4 2-5 3-2 7-4l-1-1c-2-2-6-2-9-3-1-3-3-7-4-10-1 1-3 3-5 2-1 0-2-1-3-1-1-1-2 0-3 0-3 1-5 6-7 8-1 0-2 1-3 1h0c-1-2 4-40 5-47 1-11 1-24 1-36 0-9-2-18-3-28-2-13-4-26-7-39-1-3-2-7-4-10 0-4-2-7-3-11z"></path><path d="M783 555c1-1 4-8 5-8-2 7-5 13-7 20l-4-1 1-1 1-1c0-1 1-2 2-3h0c2-2 2-4 2-6z" class="K"></path><defs><linearGradient id="Be" x1="778.215" y1="539.324" x2="780.404" y2="517.488" xlink:href="#B"><stop offset="0" stop-color="#9c9a9b"></stop><stop offset="1" stop-color="#cecccd"></stop></linearGradient></defs><path fill="url(#Be)" d="M794 506v3 1l-1 1v2l-1 1h0v1c-1 1-1 2-2 3v1h1 0 2 1c1-1 2-1 2-2v-1l2-4 1-2c1-2 0-1 1-2v-1h1c-3 11-11 24-21 29-5 3-11 4-16 3l4-3 4-1c7-3 15-11 18-18v-1l4-10z"></path><defs><linearGradient id="Bf" x1="782.166" y1="563.648" x2="765.234" y2="564.041" xlink:href="#B"><stop offset="0" stop-color="#555"></stop><stop offset="1" stop-color="#6e6d6d"></stop></linearGradient></defs><path fill="url(#Bf)" d="M760 580c2-3 5-6 6-10v-5c1-5 1-9 1-13h1c3 0 6 4 9 5 2 1 3 0 5-2h1c0 2 0 4-2 6h0c-1 1-2 2-2 3l-1 1-1 1-2 3c-4 4-7 8-12 11h-3z"></path><path d="M777 566l4 1-1 1c0 1 0 7-1 8-1 2-4 3-6 5-4 2-7 5-10 9-9 11-14 26-27 36h-2c-1 1-2 3-3 4h-1l-2 2c-1 1-2 1-3 2l-1 1c-2 0-3 0-4-1l-2-1 6-6v-1c4-3 8-7 12-11 2-4 5-8 7-12s4-7 5-11c1-2 1-3 0-4 1-2 5-4 7-5s4-1 5-3h3c5-3 8-7 12-11l2-3z" class="L"></path><path d="M775 569l1 1c-2 2-4 4-5 6-2 1-3 2-5 4l-5 5h-1c0-1 1-1 2-2l1-2v-1c5-3 8-7 12-11z" class="J"></path><defs><linearGradient id="Bg" x1="754.575" y1="545.219" x2="770.018" y2="518.938" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#d4d3d3"></stop></linearGradient></defs><path fill="url(#Bg)" d="M758 538c1 0 1 0 2 1 8 5 19 3 27-1 2-1 6-4 9-4 1-1 1 0 2 0 0 3-2 6-3 9-13 5-30 6-43 2-4-5-10-10-12-17-6-13 1-29 6-42l1 2-1 1v1c0 1 0 0-1 1v2c-1 0-1 1-1 2h0l-1 1v1 2h-1v2c0 1 0 1-1 1 0 1 0 2-1 3v3c1 1 1 0 2 1s1 2 3 2c0-1 0-2 1-3h1c1-1 2-2 3-2l3-2 1-1s0-1 2-1h0c1-1 1-1 2-1h1c0-1 1 0 1 0h1l1-1c1 1 2 1 3 2 0 0 1 1 2 1 1 1 5 0 7 0v-2-4h-1v-2c1-2 0-3 0-5l-1-1 1-2v1c2 7 1 14 2 20 1 2 2 5 3 7h1c3 0 8 0 11 1v1c-3 7-11 15-18 18l-4 1-4 3-6-1z"></path><path d="M760 501l2 1h0c0 2 0 3 1 5h1v-4c1 0 1 0 2 1 2 4-1 10-1 14l-1 1v3h-1c-2 0-5-3-6-4l-2-1c-2 0-4-1-5-2l-3 3c-1-2-1-6 0-9 0-1 2-1 3-2 3-2 6-4 10-6z"></path><path d="M751 514c1-2 3-4 4-5l9 10v3h-1c-2 0-5-3-6-4l-2-1c-2 0-4-1-5-2l1-1z" class="B"></path><path d="M751 514c2 0 4 0 6 1 0 1 1 2 0 3l-2-1c-2 0-4-1-5-2l1-1z" class="E"></path><path d="M761 501l1-1c1 1 2 1 3 2 0 0 1 1 2 1 1 1 5 0 7 0v-2-4h-1v-2c1-2 0-3 0-5l-1-1 1-2v1c2 7 1 14 2 20 1 2 2 5 3 7h1c3 0 8 0 11 1v1c-3 7-11 15-18 18l-4 1-4 3-6-1v-1c-6-2-9-10-10-15l7-5 2 1c1 1 4 4 6 4h1v-3l1-1c0-4 3-10 1-14-1-1-1-1-2-1v4h-1c-1-2-1-3-1-5h0l-2-1h1z"></path><path d="M758 538v-2h10l-4 3-6-1z" class="D"></path><defs><linearGradient id="Bh" x1="755.931" y1="529.659" x2="768.26" y2="524.075" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#8c8a8a"></stop></linearGradient></defs><path fill="url(#Bh)" d="M767 523h2c1 2 0 5 1 7-4 1-12 1-15-2-1-1-1-1-1-3 1-1 1-1 3-1h5 1l1 1c1-1 2-1 3-2z"></path><path d="M757 524h5l-1 1v1h-3c-1-1-1-1-1-2z" class="F"></path><defs><linearGradient id="Bi" x1="769.441" y1="527.013" x2="777.69" y2="505.616" xlink:href="#B"><stop offset="0" stop-color="#b2b0b0"></stop><stop offset="1" stop-color="#d3d1d1"></stop></linearGradient></defs><path fill="url(#Bi)" d="M761 501l1-1c1 1 2 1 3 2 0 0 1 1 2 1 1 1 5 0 7 0v-2-4h-1v-2c1-2 0-3 0-5l-1-1 1-2v1c2 7 1 14 2 20 1 2 2 5 3 7h1c3 0 8 0 11 1v1c-2-1-3-1-5 0-5 3-7 10-12 12l-1 1h-2c-1-2 0-5-1-7h-2c-1 1-2 1-3 2l-1-1 1-2h0v-3l1-1c0-4 3-10 1-14-1-1-1-1-2-1v4h-1c-1-2-1-3-1-5h0l-2-1h1z"></path><path d="M765 518v1c4-2 6-5 7-9h1v9c0 2-2 8-1 9 0 0 0 1 1 1l-1 1h-2c-1-2 0-5-1-7h-2c-1 1-2 1-3 2l-1-1 1-2h0v-3l1-1z" class="H"></path><path d="M767 523c1-2 1-2 2-3h1c1 1 1 3 1 5 0 1-1 2 0 4l1 1h-2c-1-2 0-5-1-7h-2z" class="D"></path><path d="M711 540c3-3 3-10 6-14 1-2 2-3 4-5l6-9c-6-6-11-14-14-22-4 3-8 5-12 9-2 2-3 4-3 7 0 1 1 2 2 2l6-4c1 2 2 4 3 5 1 3 3 10 1 12l-3 3c-3 0-6 1-8-1-4-2-7-8-8-12-1-6 4-16 7-21 0-1 1-2 2-2 1-1 3-1 4-1l11-1c3 0 10-1 12 1 3 3 2 18 1 23l-4 27-4 18c-3 11-5 22-10 32-2 5-4 10-7 14-2 3-5 7-6 10-12 16-21 33-26 52-3 11-5 22-3 33 2 14 7 27 19 35 6 5 13 6 21 7-16 2-31-2-44-11-13-10-22-24-28-38-2-3-4-9-6-11-5 1-12 4-18 4 17-7 33-17 41-35l-5-3c-3 5-7 10-11 14-6 7-14 12-22 17-3 2-6 4-10 5h0v-1c13-7 22-16 30-28 2-4 4-7 5-10v-1h-1l-2-1c-2 1-3 6-5 8l-6 9 3-8h0v-2l3-7c-1-1 0-1-1 0-1 0-1 0-1 1l-3 7h0c-2 4-3 9-6 13-1 2-3 5-5 7 1-4 4-8 5-12 6-17 10-34 14-51l6-35c1-7 2-15 4-22s3-16 10-22c1-1 1-1 3-1 1 0 2 1 3 2 3 4 3 8 3 13 0 3-1 6-3 8-4-2-4-9-6-12 0-2-1-2-2-3-1 1-2 2-3 4-1 3-2 8-1 11 2 3 5 6 8 7l1 1 1-1 1 2h-1v2 1c-1 0 0 2-1 3h1s1 0 2-1h0v-1-1c0-1 0-2 1-3h1l1-2c1 0 2-1 2-1 1-1 2-2 3-2l2-2v-2c-1-1 0-1 0-2l-2-1c-1 0-1-1-1-2h0v-1l1-1v-3-3c1-1 1-2 1-3v-2-2-1c0-1 0-2 1-4v10c-1 3-2 9-1 12 1 0 2 0 2-1 1-1 1-3 2-4 1-7 0-14-1-21l2 1v1c-1 1-1 0 0 2v2 1 3c0 1 0 2 1 3 0 1 0 2-1 2h1c1-1 4 0 6 0h0 0c0-1 1-2 1-2v-2s0-1 1-2l-1-1h1v-3c1-1 0-2 1-3v-3c1-1 0-2 0-2 1-2 1-4 1-6 1-1 0-2 0-3l1-1h0c1 0 0 0 1 1l1 1c0 1 0 2-1 3 0 2-1 7 1 9v1 2l1 2 1 2c0 1 1 2 1 3l1 1c2-1 2-1 5-1h0c3 0 8-1 10 0 0 2-1 3-2 5v1c-1 1-3 1-4 2 0 1 1 1 2 1l2 2c1 1 2 1 3 3h0 1v-1c-1-1 0-1 0-2h0 1z"></path><path d="M649 644l-1-1c2-4 1-7 2-11 1 2 1 3 2 4v1c0 2 1 3-1 5l-1 1-1 1z" class="F"></path><path d="M681 620l10-9-5 10-4 2 2-4-3 1z" class="B"></path><path d="M654 700c1 3 2 7 4 10s6 7 7 10c-5-6-10-11-14-18l3-2z" class="D"></path><path d="M650 577l-1-11c2 7 7 12 9 18l-2 1c-2-2-2-4-3-6s-2-2-3-2zm12 38c1 1 2 2 2 3-1 2-1 4-1 6 0 3-2 8-4 10l-3-3c4-5 5-11 6-16z" class="B"></path><path d="M680 550l2-7c1 3 2 6 4 9 1 1 2 2 3 2l1 1c2 1 4 1 7 1h0c-1 1-1 2-1 3h-1c-2 1-5 1-7 1-4-3-5-6-8-10z" class="G"></path><path d="M636 617h2l-3 17c-1 5-3 9-6 14h0 0v-2l3-7c-1-1 0-1-1 0-1 0-1 0-1 1l-3 7h0c0-1 1-3 1-4l8-26z" class="B"></path><path d="M662 615l-1-4c2 0 2 2 3 3 3 2 8 8 11 8 1 1 3 0 4-1l2-1 3-1-2 4v1c-3 1-5 2-8 2-4-1-8-5-10-8 0-1-1-2-2-3zm-12-38c1 0 2 0 3 2s1 4 3 6l2-1 1 2-1 1c-2 0-4-1-6-1l-1-1c-3-1-4-1-6 1-4 3-5 8-5 13-1 2 0 5-1 7 0 4-1 7-1 11h-2l1-10c1-9 2-21 8-28 1-2 3-2 5-2z" class="L"></path><defs><linearGradient id="Bj" x1="658.627" y1="606.194" x2="670.791" y2="564.346" xlink:href="#B"><stop offset="0" stop-color="#aaa9a9"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#Bj)" d="M671 549l1 1-7 5c-1 1-2 1-2 2-1 0-1 1-1 2-1 5 7 11 10 15 2 3 5 7 5 10 1 2 1 4 0 6 0 7-4 14-6 21-1 0-2-1-2-2-1-7 1-14 1-21 0-1 0-4-1-5 0-2-2-4-3-5-2-3-3-5-5-8-1-2-3-3-4-5-1-4 2-7 1-10l1-1 1 2h-1v2 1c-1 0 0 2-1 3h1s1 0 2-1h0v-1-1c0-1 0-2 1-3h1l1-2c1 0 2-1 2-1 1-1 2-2 3-2l2-2z"></path><defs><linearGradient id="Bk" x1="645.021" y1="698.646" x2="656.808" y2="657.445" xlink:href="#B"><stop offset="0" stop-color="#a3a1a3"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Bk)" d="M651 702c-5-9-10-17-11-27v-2c1-4 14-13 18-17v-1l1 8 5 2 1 18c-1-4-4-10-7-12-2-2-5-2-7-2-2 1-3 2-4 3 1 4 6 4 8 8 0 3-1 5-2 7s0 10 1 13l-3 2z"></path><defs><linearGradient id="Bl" x1="680.829" y1="621.843" x2="655.178" y2="647.65" xlink:href="#B"><stop offset="0" stop-color="#8f8e8d"></stop><stop offset="1" stop-color="#d0cfcf"></stop></linearGradient></defs><path fill="url(#Bl)" d="M659 634c2-2 4-7 4-10 0-2 0-4 1-6 2 3 6 7 10 8 3 0 5-1 8-2v-1l4-2c-5 10-8 22-14 32-5-2-9-4-14-6l-9-3 1-1 1-1c2-2 1-3 1-5l13 7v-1-1c0-4-4-5-6-8z"></path><path d="M659 586c2-2 3-6 5-8h1c0 2 1 5 0 7s-2 3-4 3h-1v2c2 8-1 19-5 26-1 1-3 4-3 6-1 1 0 2 0 2-2-1-2-1-4 0-2 0-3 3-4 5s-1 4-2 6l-2-7c-1-5 1-11 2-16 1-1 0-1 0-2-1-3 3-10 5-12 1-2 2-3 4-4 1 1 3 2 4 3-1-4-1-7-3-11 2 0 4 1 6 1l1-1z" class="E"></path><path d="M654 598h1c0 3 1 7 0 10-2 4-5 8-9 9h-2v-2c1-7 5-14 10-17z"></path><defs><linearGradient id="Bm" x1="671.773" y1="555.597" x2="705.158" y2="546.994" xlink:href="#B"><stop offset="0" stop-color="#b9b8b8"></stop><stop offset="1" stop-color="#f1efef"></stop></linearGradient></defs><path fill="url(#Bm)" d="M673 518l2 1v1c-1 1-1 0 0 2v2 1 3c0 1 0 2 1 3 0 1 0 2-1 2h1c1-1 4 0 6 0h0 0c0-1 1-2 1-2v-2s0-1 1-2l-1-1h1v-3c1-1 0-2 1-3v-3c1-1 0-2 0-2 1-2 1-4 1-6 1-1 0-2 0-3l1-1h0c1 0 0 0 1 1l1 1c0 1 0 2-1 3 0 2-1 7 1 9v1 2l1 2 1 2c0 1 1 2 1 3l1 1c2-1 2-1 5-1-2 1-4 1-5 2-2-1-2-2-3-4-1-4-3-7-3-12-1 3-2 7-2 10-1 6-3 12-3 18l-2 7c3 4 4 7 8 10 2 0 5 0 7-1h1c0-1 0-2 1-3 1-2 1-3 2-5 0-2 0-4 2-6 1-2 1-3 4-4v1c-1 11-5 19-11 27h0c-2 0-4 1-6 2 0 0-1 1-2 1-2 0-5-2-7-1v5c2 3 5 4 7 5h0-1c-12-4-11-15-12-25 0-2 0-4-1-5v-1l-1-1v-2c-1-1 0-1 0-2l-2-1c-1 0-1-1-1-2h0v-1l1-1v-3-3c1-1 1-2 1-3v-2-2-1c0-1 0-2 1-4v10c-1 3-2 9-1 12 1 0 2 0 2-1 1-1 1-3 2-4 1-7 0-14-1-21z"></path><path d="M680 550c3 4 4 7 8 10 2 0 5 0 7-1h1c-1 3-3 6-5 8-1 1-2 1-4 1s-4-1-5-3c-3-4-2-11-2-15z"></path><path d="M706 534v1c-1 1-3 1-4 2 0 1 1 1 2 1l2 2c1 1 2 1 3 3h0 1v-1c-1-1 0-1 0-2h0 1v1c1 1 3 2 4 3 1 3 1 7 1 10-1 7-4 16-8 23-1 2-3 4-4 7-1 2-2 4-2 6-2 3-5 9-8 11h-2c-1 3-4 4-6 7-4 5-6 9-11 12 1-5 3-9 5-14 0-3 1-5 2-7 2-5 5-9 7-13 1-1 3-3 3-4 0-2-1-4 0-5l2 1c3-1 6-8 7-11l7-13 1 5h0c1-4 0-9 0-13-2-4-6-7-9-9l6-3z" class="B"></path></svg>