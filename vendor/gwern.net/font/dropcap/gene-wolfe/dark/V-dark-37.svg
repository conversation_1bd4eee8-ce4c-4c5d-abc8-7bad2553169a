<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="202 186 629 756"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#959494}.C{fill:#bab9b9}.D{fill:#272626}.E{fill:#929191}.F{fill:#696868}.G{fill:#4b4a4b}.H{fill:#6b6a6a}.I{fill:#232222}.J{fill:#474647}.K{fill:#c0bebf}.L{fill:#c8c7c8}.M{fill:#545353}.N{fill:#2a292a}.O{fill:#787777}.P{fill:#161516}.Q{fill:#9c9a9b}.R{fill:#383737}.S{fill:#807f7f}.T{fill:#424141}.U{fill:#e3e3e3}.V{fill:#0e0e0e}</style><path d="M387 545c2 1 2 3 3 5 1 0 2 1 2 2l1 1v1h-4c-1-3-2-6-2-9z" class="I"></path><path d="M362 588h1 0l1 2h1 3c1 4 2 8 4 11h-1c-4-2-7-9-9-13zm397-11l1-1c3-3 6-3 10-2-1 0-3 0-4 2 0 0-1 1-2 1-1 2 0 2-2 3-1 0-1-1-2-2h0l-1 1h1c1 2 1 2 1 3s1 1 1 1c1 1 1 2 2 2v2c-2 0-3 0-4-1h2l-2-2h0v-1c-1-1-1-1-1-2h0l-1-1c1-1 1-2 1-2v-1z" class="D"></path><path d="M727 390c3 0 5 2 7 2 5 4 9 9 12 14l-15-12v-1c-1-1-2-2-4-3z" class="K"></path><path d="M293 367c3 0 6-1 9-1l3 1c-7 1-13 3-19 6-4 2-7 4-10 5h0c5-4 12-8 17-11z" class="E"></path><path d="M271 379c1-1 1-2 2-3 3-2 7-4 10-6 1-1 2-2 3-2 1-1 2-1 3-2h1 1 3l-1 1c-5 3-12 7-17 11h0l-6 6v-1c-1-1 0-2 1-4z" class="J"></path><path d="M761 245c4-1 10 0 14-1h33-5l-1 1h-4l-9 2c-4 0-9 1-12-1v-1c-2 1-5 1-6 0h-10z" class="C"></path><path d="M764 587l-1 1c0 1-1 1-1 1-1 0-1 0-2 1-1 0-1 0-2-1l-3-3v-1l-1-1c0-2 0-5 1-7v-1c1-2 2-3 4-4 0 1-1 4 0 5v1s0 1-1 2l1 1h0c0 1 0 1 1 2v1h0l2 2h-2c1 1 2 1 4 1z" class="I"></path><path d="M347 506c10 2 19 5 27 8 3 2 6 4 10 4 0 1 1 1 2 2-2 0-4 1-6 1 0-1-1-1-2-2-3-2-5-2-9-3-2-1-4-3-6-3 1 0 1 0 2-1h-1c-4-2-7-3-11-4-2 0-4 0-6-1v-1z" class="L"></path><path d="M649 309l3-3c0 3-1 5-2 7-3 1-4 2-5 4 0 1 0 1 1 1l2 2-1 1-3-3h-1c0 1 0 2 1 3 1 3 1 8 0 10v3l-1 6c0-1-1-1 0-2 0-1 0-2 1-3-1-1-1-1-1-2h0 0c0 1-1 2-1 3 0 3-1 6-2 9h-1c0-1 1-2 1-3 1-3 1-5 1-8 1-3 1-7 1-10-1-2-2-4-2-5s2-5 3-6c0 1 0 2-1 3h0 1c1-2 4-4 6-7z" class="T"></path><path d="M777 584v4 1c0 2-1 5-2 6-1 2-1 3-1 4-6 6-14 10-21 12 1-1 1-2 3-2 3-3 13-9 14-13l2-3c2-3 4-5 5-9z" class="C"></path><path d="M645 291c2 0 2 0 3 1 1 0 2 0 2 1v2c0 1 0 1 1 2 0 3 0 4-1 7v2c0 1-1 2-1 3-2 3-5 5-6 7h-1 0c1-1 1-2 1-3 2-8 3-14 2-22z" class="M"></path><defs><linearGradient id="A" x1="254.356" y1="399.677" x2="272.169" y2="391.311" xlink:href="#B"><stop offset="0" stop-color="#30302f"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M267 383h0c1-2 2-3 4-4-1 2-2 3-1 4v1c-6 7-11 14-10 24 0 6 3 11 5 17-1-2-2-3-4-5v-2h0l-1-1v-1c0-2-1-5-2-7h0v-2c-1-2-1-6-1-9h1c1-6 5-11 9-15z"></path><path d="M283 562c0 1 1 1 1 2s0 1 1 1c0 3-1 4-2 7-1 2-4 5-7 5-4 1-10 1-14-1-1-1-2-3-3-4l-2-1 1-1h2c2 0 4 2 6 3 2 0 5 1 6 0 2 0 4 0 5-1 3-2 4-4 5-6l1-4z" class="P"></path><path d="M283 562c0 1 1 1 1 2s0 1 1 1c0 3-1 4-2 7-1 0-2-1-3 0-3 2-7 4-10 3l-1-1c-4-1-7-1-10-2l-2-1 1-1h2c2 0 4 2 6 3 2 0 5 1 6 0 2 0 4 0 5-1 3-2 4-4 5-6l1-4z" class="D"></path><defs><linearGradient id="C" x1="716.424" y1="441.201" x2="702.13" y2="435.824" xlink:href="#B"><stop offset="0" stop-color="#111010"></stop><stop offset="1" stop-color="#313132"></stop></linearGradient></defs><path fill="url(#C)" d="M703 432l12 6c2 2 4 3 6 4-6 1-11-1-16-3-4-1-7-1-11-2-2 0-8 0-9-1h4c4-4 8-3 14-4z"></path><path d="M265 369h1c1 2 2 5 3 6-1 2-1 5-2 7v1c-4 4-8 9-9 15h-1c0 3 0 7 1 9v2h0c1 2 2 5 2 7v1l1 1h0v2c-4-5-7-11-7-18 0-11 7-19 13-26v-2c-1-2-1-3-2-5h0z" class="N"></path><defs><linearGradient id="D" x1="681.781" y1="591.279" x2="688.881" y2="580.987" xlink:href="#B"><stop offset="0" stop-color="#838283"></stop><stop offset="1" stop-color="#9d9c9c"></stop></linearGradient></defs><path fill="url(#D)" d="M671 560c6 3 13 10 16 18 1 5 1 11-1 16l24 5c13 2 26 5 39 3h0-2l-1 1h-13c-3 0-6 0-9-1h-2c-2-1-3-1-4-1-2-1-5-1-7-1l-12-2-6-1-1-1h-2c-1 0-2 0-3-1h-1l-2-1v-4c0-3 0-7-1-11 1 0 1 0 1-1 0-5-6-9-10-13-1-2-3-3-3-5z"></path><defs><linearGradient id="E" x1="277.05" y1="384.899" x2="299.193" y2="354.68" xlink:href="#B"><stop offset="0" stop-color="#353437"></stop><stop offset="1" stop-color="#6d6d6c"></stop></linearGradient></defs><path fill="url(#E)" d="M274 371c3-1 5-3 7-5 5-3 13-6 19-6 2 1 7 2 10 0h2v2h3 2c-4 0-9 0-13 1-3 1-7 1-10 3h-3-1-1c-1 1-2 1-3 2-1 0-2 1-3 2-3 2-7 4-10 6-1 1-1 2-2 3-2 1-3 2-4 4h0v-1c1-2 1-5 2-7l5-4z"></path><defs><linearGradient id="F" x1="780.716" y1="582.106" x2="762.149" y2="580.431" xlink:href="#B"><stop offset="0" stop-color="#a09f9f"></stop><stop offset="1" stop-color="#c6c4c5"></stop></linearGradient></defs><path fill="url(#F)" d="M759 572c4-2 7-3 12-2 4 1 6 3 8 6 3 4 3 9 2 13-1 5-4 7-7 10 0-1 0-2 1-4 1-1 2-4 2-6v-1-4c0-1 0-3-1-4 0-3-3-5-6-6-4-1-7-1-10 2l-1 1c-1-1 0-4 0-5z"></path><path d="M693 402h1l-7 20c17 2 30 11 40 24l-6-4c-2-1-4-2-6-4v-1s-1-1-2-1l-6-5c-9-5-17-7-27-7h-7l-2 1-2 1h-2c-1 1-2 1-3 1-2 0-3 1-4 2s-2 2-4 2h0l1-3c2 0 4-2 6-3 1-1 3-1 4-2 4-1 7-1 11-2l7 1c2-3 3-5 3-8l5-12z" class="K"></path><path d="M377 399l9 9c4 6 9 12 13 18 2 2 4 5 5 7h0v4l1 1c1 3 2 7 4 9l-1 1-1-1c-1-3-4-5-5-7l-2-2c-1 0-1 0-1-1l-5-5-14-16c0-5-4-6-6-9l-1-1h1l2 2c1 1 2 3 4 4 1 0 2 0 3 1 0 1 0 2 1 3 2 1 5 3 6 5 0 1 1 1 1 2 1 2 4 4 6 6v1c1 0 1 0 2 1v-1h1c0-1-1-3-2-4l-1-1c0-1-1-1-1-2l-1-1-1-1c-1-2 1 1-1-1v-1c-1-1-2-2-2-3-1-1 0 1-1-1l-3-3-1-1c-1-1 0 0-1-2-1 0-1-1-2-1 0-1 0-2-1-2l-1-1c-1-2-3-3-4-5v-1z" class="P"></path><path d="M685 400l8-1c1 1 0 1 0 3l-5 12c0 3-1 5-3 8l-7-1c-4 1-7 1-11 2v-1c2-1 3-2 5-4 1 0 3-1 5-2 1-2 2-5 3-7 0-1 1-4 2-5-1-1-1-2-1-3l4-1z" class="O"></path><path d="M688 405h2c0 3-2 5-2 8-1 2-1 4-2 5-1 0-2-1-3-2l3-4c0-2 0-4 1-6l1-1z" class="B"></path><path d="M685 400l8-1c1 1 0 1 0 3l-5 12v-1c0-3 2-5 2-8h-2-3l-2 4v-1-2h0l-1-2c-1-1-1-2-1-3l4-1z" class="F"></path><path d="M685 400l1 1c0 1-1 3-1 4l-2 4v-1-2h0l-1-2c-1-1-1-2-1-3l4-1z" class="P"></path><path d="M682 404l1 2h0v2 1l-3 12h-2 0c-4 1-7 1-11 2v-1c2-1 3-2 5-4 1 0 3-1 5-2 1-2 2-5 3-7 0-1 1-4 2-5z" class="N"></path><path d="M682 404l1 2h0v2 1l-3 12h-2-3l2-1v-2c1-1 2 0 3-2l-1-2c1-1 1-3 1-5 0-1 1-4 2-5z" class="D"></path><path d="M640 554c4 0 8-1 12 0 7 0 13 3 19 6h0c0 2 2 3 3 5 4 4 10 8 10 13 0 1 0 1-1 1-1-1-1-3-3-5 0-1-1-2-2-2-5-7-14-11-23-11-5-1-11-1-17 0h-2-2 0c-3 0-5 1-7 2-4 1-8 3-11 6 1-2 3-4 5-5 0 0 1 0 2-1l4-4 1-1 1-1c2 0 3 0 4-2h0l4-1h1 2z" class="K"></path><path d="M652 554c7 0 13 3 19 6h0c0 2 2 3 3 5-3-2-6-3-9-4-3-2-5-4-9-5h-1 2 3c-3-1-5-1-8-2z" class="C"></path><path d="M382 542c2-1 2-1 3-1 3 1 5 3 8 4 6 3 12 7 18 11v2h-1-1c-2-1-6 0-8 0-1 0-3 0-4 1-5 1-10 2-14 4l-3 2c-4 2-7 5-9 9-1 2-2 3-3 5-1 3-1 7 0 10v1h-3-1l-1-2h0-1v-1c-1-3-1-5-1-7 0-7 3-11 8-16 6-5 16-9 24-9 5-1 10 0 14 1-6-7-17-10-25-14z" class="N"></path><path d="M368 589c-1 0-2-1-3-1-2-2-2-4-2-6s1-4 2-5 2-1 2-2c1-3 4-6 7-7l1-1h1v-1c1 0 1-1 2-1 1-1 1-1 2-1 1-1 2-1 3-1l-3 2c-4 2-7 5-9 9-1 2-2 3-3 5-1 3-1 7 0 10z" class="R"></path><path d="M656 431h0c2 0 3-1 4-2s2-2 4-2c1 0 2 0 3-1h2l2-1 2-1h7c10 0 18 2 27 7l6 5c1 0 2 1 2 1v1l-12-6c-6 1-10 0-14 4h-4-3l-1 1h-1v-1h-5l-6 1c-3 0-9 0-12 2-3 0-5 1-7 2s-3 1-4 3l-2 2-2 1v-2c0-1 0-1 1-2s5-5 6-5v1h0l2-1c-1-1 3-3 4-4v-2l1-1z" class="U"></path><path d="M651 438c2-2 5-4 8-5s7 0 10 0c5 0 9 0 13-1 4 0 7-2 10-3 4 1 7 1 11 3-6 1-10 0-14 4h-4-3l-1 1h-1v-1h-5l-6 1c-3 0-9 0-12 2-3 0-5 1-7 2s-3 1-4 3l-2 2-2 1v-2c0-1 0-1 1-2s5-5 6-5v1h0l2-1z" class="C"></path><defs><linearGradient id="G" x1="674.578" y1="577.845" x2="650.288" y2="562.022" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#4f4e4e"></stop></linearGradient></defs><path fill="url(#G)" d="M638 561c6-1 12-1 17 0 9 0 18 4 23 11 1 0 2 1 2 2-2 0-4-2-5-3l-1-1v1c0 1 0 0-1 1 0 3 1 6 1 8l1 1v2l-2-1c-3-3-8-5-12-7-2-1-3-1-5-2-8-2-14-2-21-2 1 0 2-1 3-1-3-3-5-2-8-2-1 0-2 0-3 1-3 1-4 1-7 1 3-2 7-5 9-6s5-1 6-2l-1-1h0 2 2z"></path><defs><linearGradient id="H" x1="650.633" y1="564.866" x2="640.8" y2="562.661" xlink:href="#B"><stop offset="0" stop-color="#525151"></stop><stop offset="1" stop-color="#727171"></stop></linearGradient></defs><path fill="url(#H)" d="M638 561c6-1 12-1 17 0l-1 1h0c-3 0-4 0-6 2v2l1 1 1 1v1l2 2h1c1 0 2 1 3 2-8-2-14-2-21-2 1 0 2-1 3-1-3-3-5-2-8-2-1 0-2 0-3 1-3 1-4 1-7 1 3-2 7-5 9-6s5-1 6-2l-1-1h0 2 2z"></path><path d="M636 561h2 1s1 1 2 1c0 1-1 2-1 3h1c1 1 1 2 2 3l2 2h-3c-1-2-3-4-4-5h-2l-1-1-1 1v-1h1c0-1 1-2 1-3z" class="S"></path><path d="M634 561h0 2c0 1-1 2-1 3h-1v1l1-1 1 1h2c1 1 3 3 4 5h-4c-3-3-5-2-8-2-1 0-2 0-3 1-3 1-4 1-7 1 3-2 7-5 9-6s5-1 6-2l-1-1z" class="B"></path><defs><linearGradient id="I" x1="286.616" y1="348.484" x2="277.604" y2="371.577" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242323"></stop></linearGradient></defs><path fill="url(#I)" d="M274 348h5l1-1h2 0c2 1 4 1 5 1h2c2 1 3 2 5 3 1 2 3 4 5 6h1v1c1 0 1 0 2 1-1 0 0 0-1 1h-1c-6 0-14 3-19 6-2 2-4 4-7 5l-5 4c-1-1-2-4-3-6h-1c-1-1-1-1 0-3h0v-5-2c0-1 0-2 1-2v-1-1l1-1c1-2 3-4 5-5h1l1-1z"></path><path d="M266 369v-3l1 1h0l3 3c1 0 3 0 4 1l-5 4c-1-1-2-4-3-6z"></path><defs><linearGradient id="J" x1="278.545" y1="354.736" x2="236.221" y2="344.586" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#J)" d="M235 324c2 2 5 4 6 6 1 1 1 3 3 4h1l3 2v1c5 3 13 4 20 5h0c4 2 11 0 14 3h1 0 0c-2 0-2 1-3 1h-3l-1 1h-1l-1 1-1 1h-1c-2 1-4 3-5 5l-1 1v1 1c-1 0-1 1-1 2v2 5h0c-2-1-4-2-6-4-13-9-20-23-24-38z"></path><defs><linearGradient id="K" x1="736.578" y1="617.509" x2="741.714" y2="607.88" xlink:href="#B"><stop offset="0" stop-color="#a7a8a8"></stop><stop offset="1" stop-color="#c9c6c5"></stop></linearGradient></defs><path fill="url(#K)" d="M770 596c-1 4-11 10-14 13-2 0-2 1-3 2-9 4-17 5-25 7h-7c-11 1-22-1-33-2h-16c1 0 4 0 5-1 1-2 5-2 7-3l10-2c3-1 6-1 9-3 13 2 27 4 41 2 3 0 7-1 10-3 2-1 2-1 3-3 5-2 9-4 13-7z"></path><path d="M688 616h1c1 0 2-1 2-1 7-5 17-5 25-3 1 0 3 1 4 2 0 1 0 1 2 2h2c1 0 2 1 4 2h-7c-11 1-22-1-33-2z" class="J"></path><defs><linearGradient id="L" x1="408.407" y1="579.618" x2="370.338" y2="576.445" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#727171"></stop></linearGradient></defs><path fill="url(#L)" d="M383 563c4-2 9-3 14-4 1-1 3-1 4-1l1 1c1 0 3 0 5 1h0-1c-2 0-2 0-3 1v3h0c-1 1-1 0-1 1v1l1 2h1c3 1 3 2 5 4l1 2c-2-1-4 0-5-1-2 0-5 0-7 1-8 2-17 5-22 13-3 3-3 7-2 11s3 7 5 9c-2-2-5-3-7-6s-3-7-4-11v-1c-1-3-1-7 0-10 1-2 2-3 3-5 2-4 5-7 9-9l3-2z"></path><path d="M678 604c4-4 5-9 6-14v4l2 1h1c1 1 2 1 3 1h2l1 1 6 1 12 2c2 0 5 0 7 1 1 0 2 0 4 1h2c3 1 6 1 9 1h13l1-1h2 0c4-2 8-2 12-3 4-2 7-5 11-6l-2 3c-4 3-8 5-13 7-1 2-1 2-3 3-3 2-7 3-10 3-14 2-28 0-41-2h-13c-1 0-3 1-5 1s-3 1-4 0v-1h4l1-1h-4l-1-1s-1 0-2 1c0-1-1-1-1-1v-1z" class="B"></path><path d="M678 604c4-4 5-9 6-14v4l2 1h1c1 1 2 1 3 1h2l1 1 6 1 12 2c2 0 5 0 7 1l-1 1h-2c-4-1-9 0-13-1h-5c-1-1-3 0-4-1h-9l1 1h3l1 1c-1 1-3 0-4 0v1c6 0 13-1 19 0 1 0 3 0 4 1 2 0 4-1 6 0v1h0-1c-10 0-22-2-32 0 0 0-1 0-2 1 0-1-1-1-1-1v-1z" class="O"></path><path d="M757 603c-1 2-1 2-3 3-3 2-7 3-10 3-14 2-28 0-41-2h-13c-1 0-3 1-5 1s-3 1-4 0v-1h4l1-1h-4l-1-1c10-2 22 0 32 0 2 1 5 1 7 1 13 1 24 1 37-3z" class="U"></path><path d="M347 419s0-1-1-2c0-1-1-3-1-5-1-1-1-1-1-2l1-1c1 1 1 3 1 5l1 3c1 1 1 0 1 1 0 2 1 7 3 8l33 92c-4 0-7-2-10-4 1-1-1-3-1-4-3-5-5-11-7-16l-14-42-5-14c0-2-2-4-2-6 1 0 1 1 1 2l1 1 1-1 1-1c0-1 1-1 1-2s0-2-1-2v-2c1-2-1-5-2-8z" class="J"></path><defs><linearGradient id="M" x1="608.648" y1="395.572" x2="632.034" y2="408.025" xlink:href="#B"><stop offset="0" stop-color="#5b5a59"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#M)" d="M639 345h1c1-3 2-6 2-9 0-1 1-2 1-3h0 0c0 1 0 1 1 2-1 1-1 2-1 3-1 1 0 1 0 2l1-6c1 2 1 3 1 5 0 1 0 0-1 1v1l-1 4c0 3-2 6-2 9h1c-1 1-1 2-1 3h0 0l-38 105h-1v1c-1 1-1 2-2 3v1c0 2-1 3-2 5v-2h-2c-1-2-1-1 0-2v-4c0-2 2-5 3-7l8-22 32-90z"></path><path d="M647 425c4-9 10-18 17-25 1 1 1 1 3 1 0-1 1-1 2-1l-2 2 2 2h0c1 0 2-1 3-1v1h1c1-1 2-2 4-2 1 0 2-1 4-1h0c0 1 0 2 1 3-1 1-2 4-2 5-1 2-2 5-3 7-2 1-4 2-5 2-2 2-3 3-5 4v1c-1 1-3 1-4 2-2 1-4 3-6 3l-1 3-1 1v2c-1 1-5 3-4 4l-2 1h0v-1c-1 0-5 4-6 5s-1 1-1 2v2c-3 3-5 5-7 8s-3 6-5 9h-1c1-4 6-9 6-13-1-3 3-9 5-12l-1-1 4-7c1-2 2-4 4-6z" class="S"></path><path d="M651 422l1-1 11-10c0 1 0 2 1 3-7 5-12 9-16 16v1c-2 1-3 1-4 3l-2 2c-1 1-2 2-2 3l-1-1 4-7c1-2 2-4 4-6h0c2-1 3-2 4-3h0z" class="Q"></path><path d="M666 408c2-2 6-3 9-3 0 1 0 1 1 2h2l-1 1c-1 1-1 1-1 2-1 4-5 7-8 9-4 2-7 4-10 6 0-1 0-2 1-3h1c2-2 5-3 8-5 2-2 3-4 4-8-2 1-3 1-4 3-2 0-3 0-4 2-1-1-1-2-1-3s2-2 3-3z" class="H"></path><path d="M666 408c1 0 2 1 3 0 2 0 3-1 5-1v1l-2 2v-1c-2 1-3 1-4 3-2 0-3 0-4 2-1-1-1-2-1-3s2-2 3-3z" class="B"></path><path d="M678 407l1-1 1 1c-1 1-2 1-2 2v1c-1 0 0 0-1 1 0 3-3 5-5 7s-3 3-5 4v1c-1 1-3 1-4 2-2 1-4 3-6 3l-1 1c-2 1-3 2-5 3s-3 3-5 4h-1c1-2 1-2 2-3l1-2v-1c2 0 3-1 4-2l6-3c3-2 6-4 10-6 3-2 7-5 8-9 0-1 0-1 1-2l1-1z" class="G"></path><path d="M648 431l-1 2c-1 1-1 1-2 3h1c2-1 3-3 5-4s3-2 5-3l1-1-1 3-1 1v2c-1 1-5 3-4 4l-2 1h0v-1c-1 0-5 4-6 5s-1 1-1 2v2c-3 3-5 5-7 8s-3 6-5 9h-1c1-4 6-9 6-13-1-3 3-9 5-12 0-1 1-2 2-3l2-2c1-2 2-2 4-3z" class="M"></path><path d="M635 451c5-7 12-16 20-19v2c-1 1-5 3-4 4l-2 1h0v-1c-1 0-5 4-6 5s-1 1-1 2v2c-3 3-5 5-7 8s-3 6-5 9h-1c1-4 6-9 6-13z" class="K"></path><path d="M647 425c4-9 10-18 17-25 1 1 1 1 3 1 0-1 1-1 2-1l-2 2 2 2h0c1 0 2-1 3-1v1h1c1-1 2-2 4-2 1 0 2-1 4-1h0c0 1 0 2 1 3-1 1-2 4-2 5-1 2-2 5-3 7-2 1-4 2-5 2 2-2 5-4 5-7 1-1 0-1 1-1v-1c0-1 1-1 2-2l-1-1-1 1h-2c-1-1-1-1-1-2-3 0-7 1-9 3-1 1-3 2-3 3l-11 10-1 1h0c-1 1-2 2-4 3h0z" class="J"></path><path d="M647 425c4-9 10-18 17-25 1 1 1 1 3 1 0-1 1-1 2-1l-2 2-4 3c-1 2-3 4-4 6l3-2h0c-3 4-8 8-11 13h0c-1 1-2 2-4 3h0z" class="K"></path><defs><linearGradient id="N" x1="376.015" y1="443.619" x2="401.076" y2="418.934" xlink:href="#B"><stop offset="0" stop-color="#afafaf"></stop><stop offset="1" stop-color="#f2f0f1"></stop></linearGradient></defs><path fill="url(#N)" d="M317 362l1 1-1 1c4 0 6 1 10 2h-1c4 2 7 2 11 3s8 2 12 4c1 1 1 1 2 1 3 2 6 5 9 7 2 1 5 2 7 4 2 1 4 3 6 4 2 2 5 5 6 7 2 2 4 4 5 6 3 3 5 7 9 9 5 5 9 11 12 17l31 72c0 2 0 3-1 5l6 15h-1l-1-1-2-5c0-1 0-1-1-1h0c-1 0-1-1-2-2 0-1 0-2-1-2 0-1 0-1-1-2 0-1-1-2-1-3l-2-3s0-1-1-1v-2-1l1 1c1-4-3-9-4-12-4-10-7-20-13-30-1-3-1-6-3-9-2-2-3-6-4-9l-1-1v-4h0c-1-2-3-5-5-7-4-6-9-12-13-18l-9-9c-3-4-9-9-14-13-11-8-26-16-39-19-7-1-13-1-19 0l-3-1c-3 0-6 1-9 1l1-1c3-2 7-2 10-3 4-1 9-1 13-1z"></path><path d="M317 362l1 1-1 1c4 0 6 1 10 2h-1c-8-1-16-1-24 0-3 0-6 1-9 1l1-1c3-2 7-2 10-3 4-1 9-1 13-1z" class="F"></path><path d="M317 362l1 1-1 1c-3 0-10 1-13-1 4-1 9-1 13-1z" class="B"></path><path d="M404 433c4 7 8 15 11 23l20 49 6 15h-1l-1-1-2-5c0-1 0-1-1-1h0c-1 0-1-1-2-2 0-1 0-2-1-2 0-1 0-1-1-2 0-1-1-2-1-3l-2-3s0-1-1-1v-2-1l1 1c1-4-3-9-4-12-4-10-7-20-13-30-1-3-1-6-3-9-2-2-3-6-4-9l-1-1v-4z" class="G"></path><path d="M681 379c1 1 2 1 2 4l1-1c2-1 2-1 4-1 1 0 1 0 2-1v1c1 0 2-1 3-2 0 1 0 1 1 1l1 1v1h4c2-1 5-1 7-1 11 0 21 3 28 11h0c-2 0-4-2-7-2 2 1 3 2 4 3v1c-2 0-3-1-4-2l-7-2c-3 0-7 0-9 3-2 1-2 3-2 5-3 0-13 0-14 1s-1 2-1 3h-1c0-2 1-2 0-3l-8 1-4 1h0c-2 0-3 1-4 1-2 0-3 1-4 2h-1v-1c-1 0-2 1-3 1h0l-2-2 2-2c-1 0-2 0-2 1-2 0-2 0-3-1l4-4v-2-1l-1-1-1-1 8-9c0-1 2-2 3-3l2 1c0-1 1-1 2-1z" class="C"></path><path d="M706 381c11 0 21 3 28 11h0c-2 0-4-2-7-2-1-2-5-4-7-5-7-3-18-2-26 0h-3c-1 1-1 1-2 1l-4 1h-4l2-2c2 0 5-1 8-2 1 0 3-1 4-1h4c2-1 5-1 7-1z" class="L"></path><path d="M681 379c1 1 2 1 2 4l1-1c2-1 2-1 4-1 1 0 1 0 2-1v1c1 0 2-1 3-2 0 1 0 1 1 1l1 1v1c-1 0-3 1-4 1-3 1-6 2-8 2l-2 2c-5 3-9 6-13 9v-2-1l-1-1-1-1 8-9c0-1 2-2 3-3l2 1c0-1 1-1 2-1z" class="T"></path><path d="M681 379c1 1 2 1 2 4l1-1c2-1 2-1 4-1 1 0 1 0 2-1v1c1 0 2-1 3-2 0 1 0 1 1 1l1 1v1c-1 0-3 1-4 1-3 1-6 2-8 2h-1l-1-1c-1 0-1 0-2 1h-3 0-1v-1c1-2 2-3 4-3v-1c0-1 1-1 2-1z" class="R"></path><path d="M681 379c1 1 2 1 2 4l-1 1c-2-1-2-2-3-3v-1c0-1 1-1 2-1z" class="L"></path><path d="M670 398h0c1-2 2-3 4-4 2-2 5-3 8-5 2 0 3-2 5-2h1c8-1 15-1 23-2l1 1h2 2c2 1 4 1 6 2l5 3v1l-7-2c-3-1-5-1-7-2-1 0-6-1-8 0l-3 3c-3 2-5 4-9 3s-7-2-10-1c-2 1-5 1-7 2-1 1-1 1-2 1s-2 1-4 2z" class="U"></path><path d="M670 398c2-1 3-2 4-2s1 0 2-1c2-1 5-1 7-2 3-1 6 0 10 1s6-1 9-3l3-3c2-1 7 0 8 0 2 1 4 1 7 2-3 0-7 0-9 3-2 1-2 3-2 5-3 0-13 0-14 1s-1 2-1 3h-1c0-2 1-2 0-3l-8 1-4 1h0c-2 0-3 1-4 1-2 0-3 1-4 2h-1v-1c-1 0-2 1-3 1h0l-2-2 2-2 1-1v-1z" class="L"></path><defs><linearGradient id="O" x1="674.991" y1="402.143" x2="680.509" y2="397.357" xlink:href="#B"><stop offset="0" stop-color="#504f50"></stop><stop offset="1" stop-color="#686766"></stop></linearGradient></defs><path fill="url(#O)" d="M670 399h1c2 0 3-2 5-2 2-1 9-2 11-2 0 0 2 1 2 2h1c-3 2-6 2-9 3v1c-2 0-3 1-4 1-2 0-3 1-4 2h-1v-1c-1 0-2 1-3 1h0l-2-2 2-2 1-1z"></path><defs><linearGradient id="P" x1="640.766" y1="493.245" x2="675.772" y2="493.779" xlink:href="#B"><stop offset="0" stop-color="#a09b9a"></stop><stop offset="1" stop-color="#bec0c1"></stop></linearGradient></defs><path fill="url(#P)" d="M675 436h5v1h1l-29 82-8 22c-1 4-2 8-4 12v-4l-2-1-1-1 1-1c2-4 3-8 4-12l3-6 4-15-1-1v-3l5-15h0v-1-1l-3 3c1-1 1-4 1-4 1-1 2 0 2-2v-1l-2 1v-1c0-3 0-3-1-5v-1l2 1v1h1l1-2c0-4 3-8 4-12 2-3 3-5 3-8l1-1-1-1v-1c1-1 1-2 2-3 0-1 0-1 1-1 3-5 4-13 5-18l6-1z"></path><path d="M638 546c2-4 3-8 4-12 0 4-1 6-2 10v1l2-2c0 1-1 4-2 6l-2-1-1-1 1-1z" class="E"></path><path d="M675 436h5v1l-2 5h-1l-1-1c-2 1-3 6-4 8l-3 8c-1 2-2 3-3 5v3l-1 1h0c0 4-2 7-3 10l-8 23c-1 4-2 10-5 14l-1-1v-3l5-15h0v-1-1l-3 3c1-1 1-4 1-4 1-1 2 0 2-2v-1l-2 1v-1c0-3 0-3-1-5v-1l2 1v1h1l1-2c0-4 3-8 4-12 2-3 3-5 3-8l1-1-1-1v-1c1-1 1-2 2-3 0-1 0-1 1-1 3-5 4-13 5-18l6-1z" class="Q"></path><defs><linearGradient id="Q" x1="663.387" y1="437.351" x2="662.597" y2="485.624" xlink:href="#B"><stop offset="0" stop-color="#0d0d0c"></stop><stop offset="1" stop-color="#333"></stop></linearGradient></defs><path fill="url(#Q)" d="M669 437l6-1c-6 8-8 18-11 26l-7 21c-1 3-2 8-4 11h0v-1-1l-3 3c1-1 1-4 1-4 1-1 2 0 2-2v-1l-2 1v-1c0-3 0-3-1-5v-1l2 1v1h1l1-2c0-4 3-8 4-12 2-3 3-5 3-8l1-1-1-1v-1c1-1 1-2 2-3 0-1 0-1 1-1 3-5 4-13 5-18z"></path><path d="M808 244c6-1 13-2 20-1-6 2-11 3-17 5-16 5-32 12-46 22-4 3-8 7-13 10h0-1c-1-1-2 0-4 0h-9v-1c-5 0-9 1-14 2 1-1 2-1 3-1 1-1 1-1 2-1h0c3-2 6-5 7-8l3-3c1-2 4-2 5-3l1-1 3-3h0l17-7c1-1 9-3 10-3v1h1c-1 3-3 5-5 7l6-3c3-4 7-7 12-9l9-2h4l1-1h5z" class="U"></path><path d="M775 251v1h1c-1 3-3 5-5 7-1 2-3 3-5 5l-3 3h0l2-1h1l2-2c0-1 0 0 1-1l2-1h0c-3 3-7 5-10 8l-6 6c-1 1-3 3-3 4h-1c-1-1-2 0-4 0h-9v-1h5v-2c2 0 4-2 5-3 1-2 3-3 5-5s4-5 7-7c2-1 6-4 7-6l-2-2c1-1 9-3 10-3z" class="K"></path><path d="M765 254l2 2c-1 2-5 5-7 6-3 2-5 5-7 7s-4 3-5 5c-1 1-3 3-5 3v2h-5c-5 0-9 1-14 2 1-1 2-1 3-1 1-1 1-1 2-1h0c3-2 6-5 7-8l3-3c1-2 4-2 5-3l1-1 3-3h0l17-7z" class="R"></path><defs><linearGradient id="R" x1="638.281" y1="255.219" x2="624.117" y2="273.973" xlink:href="#B"><stop offset="0" stop-color="#0d0c0c"></stop><stop offset="1" stop-color="#373737"></stop></linearGradient></defs><path fill="url(#R)" d="M593 247c18 1 37 6 53 14 3 2 8 4 11 7h0c0 1 0 2 1 3 2 1 4 2 5 4v4h0l-1 2v1c0 1 0 1-1 2v1c-2 2-1 4-4 6-1-1-1-1-2-3-1 1-1 0-1 1 0-1-1-2-1-2 0 1 0 4 1 5 0 1 1 0 1 1-1 1-1 1 0 2v3l-1 1v1c0 1-1 1-1 1v4c0 1 0 1-1 1l-3 3c0-1 1-2 1-3v-2c1-3 1-4 1-7-1-1-1-1-1-2v-2c0-1-1-1-2-1-1-1-1-1-3-1l-1-2c-5-17-22-27-36-35l-12-6c-1 0-2-1-3-1z"></path><defs><linearGradient id="S" x1="486.85" y1="597.499" x2="506.68" y2="555.31" xlink:href="#B"><stop offset="0" stop-color="#838184"></stop><stop offset="1" stop-color="#b9b9b7"></stop></linearGradient></defs><path fill="url(#S)" d="M477 525c4 3 7 22 10 24l2-2c1 1 2 2 2 3 0-2-1-4-1-6l-1-1v-1c0-1-1-1-1-2l-1-2c0-1-1-2 0-4h0c4 8 6 17 9 25l24 69c1 1 1 3 2 4l4 12 2 5v1c-1 4 0 8-1 12-1 2 1 3 0 5-1-2-2-4-2-6l-4-10-3-10v-1l-2-5v6c1 2 1 5 1 7-3-6-4-13-6-19s-3-13-6-18c0 0-1-1-1-2-2-6-3-13-6-19 0-1-1-3-2-4l-5-16-9-25v-4c-1-1-1-1 0-3l-5-13z"></path><path d="M508 602h0l-3-7-1-1c-1-2-1-5 0-7 1 1 1 1 1 2 1 1 1 2 2 3l3 9-1 1v1c1 0 1 1 1 2l-2-4v1h0z" class="F"></path><path d="M482 545v-4c-1-1-1-1 0-3l23 68 7 19c1 3 3 7 4 10v6c1 2 1 5 1 7-3-6-4-13-6-19s-3-13-6-18c0 0-1-1-1-2-2-6-3-13-6-19 0-1-1-3-2-4l-5-16-9-25z" class="I"></path><defs><linearGradient id="T" x1="526.719" y1="632.665" x2="508.644" y2="628.42" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#5e5c5d"></stop></linearGradient></defs><path fill="url(#T)" d="M508 602h0v-1l2 4c0-1 0-2-1-2v-1l1-1 2 8 5 13c1 2 2 5 3 6h0c1 1 1 3 2 4l4 12 2 5v1c-1 4 0 8-1 12-1 2 1 3 0 5-1-2-2-4-2-6l-4-10-3-10v-1c1-3-3-11-4-14h0l-1-2-5-22z"></path><path d="M525 661v-4l-3-7c3-2 0-6 2-8l1 3 1-1 2 5v1c-1 4 0 8-1 12-1 2 1 3 0 5-1-2-2-4-2-6z" class="D"></path><defs><linearGradient id="U" x1="720.066" y1="353.691" x2="704.676" y2="342.484" xlink:href="#B"><stop offset="0" stop-color="#969696"></stop><stop offset="1" stop-color="#cac8c8"></stop></linearGradient></defs><path fill="url(#U)" d="M702 309l13-6c7-2 13-3 20-4 2 0 4 0 6 1h0l-6 12c-1 4-2 7-2 11h-1c-6 2-10 8-13 14l-16 43 3 1c-2 0-5 0-7 1h-4v-1l-1-1c-1 0-1 0-1-1-1 1-2 2-3 2v-1c2-2 4-3 4-6 0-2 0-4 1-5h1v-1c1-2 1-3 1-5 1-1 3-2 4-4 0-1 0-1-1-2 1-2 2-3 2-5 3-10 8-21 13-31l1-1c1 0 2-2 4-3 2-3 4-5 6-8 0-1 0-1-1-2-6-3-11 0-17 2-2 2-5 4-7 5-4 3-7 7-12 7 4-5 9-8 13-12z"></path><path d="M727 317h1c2 1 4 3 5 4-2 1-5 1-7 3l-4 3-3 3-1-1c3-4 8-7 9-12z" class="Q"></path><path d="M726 316c1-2 2-3 3-5 1-1 2-4 4-5 1-2 3-4 6-4l-7 15c-2-1-3-1-6-1z" class="M"></path><path d="M702 309l13-6c7-2 13-3 20-4 2 0 4 0 6 1l-2 2c-3 0-5 2-6 4-2 1-3 4-4 5-1 2-2 3-3 5-10 5-15 20-19 31l-6 15c-2 6-5 12-6 19l-1-1c-1 0-1 0-1-1-1 1-2 2-3 2v-1c2-2 4-3 4-6 0-2 0-4 1-5h1v-1c1-2 1-3 1-5 1-1 3-2 4-4 0-1 0-1-1-2 1-2 2-3 2-5 3-10 8-21 13-31l1-1c1 0 2-2 4-3 2-3 4-5 6-8 0-1 0-1-1-2-6-3-11 0-17 2-2 2-5 4-7 5-4 3-7 7-12 7 4-5 9-8 13-12z" class="N"></path><path d="M708 309c2-1 2-1 3-2h1c1-1 2-1 3-1h1c1-1 6-1 7-2 2 0 3 0 5 1l1 1-1 1-2 2c0-1 0-1-1-2-6-3-11 0-17 2z" class="D"></path><defs><linearGradient id="V" x1="674.456" y1="376.057" x2="663.357" y2="368.332" xlink:href="#B"><stop offset="0" stop-color="#747374"></stop><stop offset="1" stop-color="#a2a1a1"></stop></linearGradient></defs><path fill="url(#V)" d="M689 362c1-1 2-2 3-2l1-1c1 0 1-1 2-2h0c2-2 1-3 2-5 1-1 0-2 1-3 1-3 2-5 4-7 1-1 2-2 2-3 1-2 3-3 4-5s1-4 2-6l2-2v-2c1-2 2-2 3-3-5 10-10 21-13 31 0 2-1 3-2 5 1 1 1 1 1 2-1 2-3 3-4 4 0 2 0 3-1 5v1h-1c-1 1-1 3-1 5 0 3-2 4-4 6-1 1-1 1-2 1-2 0-2 0-4 1l-1 1c0-3-1-3-2-4-1 0-2 0-2 1l-2-1c-1 1-3 2-3 3l-8 9 1 1 1 1v1 2l-4 4c-7 7-13 16-17 25-2 2-3 4-4 6l-4 7-5 10c0-3 1-5 3-8v-1-2-2c4-7 6-13 9-20h0c0-3 3-6 4-8 0-2 1-3 2-5 0-2 1-3 1-4 1-2 2-5 3-7-1-1-1 0-1-1s1-1 1-2h0c1-2 1-3 1-5h0l1-1c1-1 1-3 0-5 0-2 2-4 3-6h0c1-1 1-1 2-1l1-1c2 0 4-2 6-3l2-1c2 1 3 0 4-1 2-1 3-1 5-1l1 1h1 1c2 0 3-1 5-2z"></path><path d="M655 403c0 1 0 1-1 2v-1-1l1-1-1-1c1-1 1-2 1-3l5-8c0-1 0-2-1-2v-1l2-1c1-1 2-1 2-2 1-1 0-2 1-3s4-3 5-5c1-1 2-1 3-2 1-2 4-3 5-5l1-1c1-1 2-1 4-1-1 1-3 2-3 3-1 2-4 4-6 5l-4 4-1 1-3 3c-1 2-2 3-3 4v2h-1v1c0 2-1 2-1 3-1 1-1 2-2 3l-1 2v2l-1 1s-1 1-1 2z" class="F"></path><path d="M637 439l4-8c0-1 0-1 1-3 3-3 4-7 6-11 1-3 3-7 5-10 1-2 4-3 5-6 3-3 5-7 8-10l1 1 1 1v1 2l-4 4c-7 7-13 16-17 25-2 2-3 4-4 6l-4 7-5 10c0-3 1-5 3-8v-1z" class="J"></path><path d="M689 362c1-1 2-2 3-2l1-1c1 0 1-1 2-2h0c2-2 1-3 2-5 1-1 0-2 1-3 1-3 2-5 4-7 1-1 2-2 2-3 1-2 3-3 4-5s1-4 2-6l2-2v-2c1-2 2-2 3-3-5 10-10 21-13 31 0 2-1 3-2 5 1 1 1 1 1 2-1 2-3 3-4 4 0 2 0 3-1 5v1h-1c-1 1-1 3-1 5 0 3-2 4-4 6-1 1-1 1-2 1-2 0-2 0-4 1l-1 1c0-3-1-3-2-4-1 0-2 0-2 1l-2-1c-1 1-3 2-3 3h-1c-2 2-4 5-6 7-1 2-3 3-4 4 0 1-1 2-2 3 0 1-1 2-2 3 0 1-1 1-2 2 0 1-1 1-2 2 0-1 1-2 1-2l1-1v-2l1-2c1-1 1-2 2-3 0-1 1-1 1-3v-1h1v-2c1-1 2-2 3-4l3-3 1-1 4-4c2-1 5-3 6-5 0-1 2-2 3-3h0c4-1 5-3 7-5z" class="H"></path><path d="M673 382c8-10 16-17 27-24l1 1c-1 2-3 3-4 4 0 2 0 3-1 5v1h-1c-1 1-1 3-1 5 0 3-2 4-4 6-1 1-1 1-2 1-2 0-2 0-4 1l-1 1c0-3-1-3-2-4-1 0-2 0-2 1l-2-1c-1 1-3 2-3 3h-1z" class="C"></path><path d="M677 379l9-7c0 1 0 2 2 2-2 3-4 3-7 5h0c-1 0-2 0-2 1l-2-1z" class="G"></path><path d="M697 363c0 2 0 3-1 5v1h-1l-1-1c-2 1-3 2-4 4h0l-2 2c-2 0-2-1-2-2l11-9z" class="T"></path><path d="M690 372h0c1-2 2-3 4-4l1 1c-1 1-1 3-1 5 0 3-2 4-4 6-1 1-1 1-2 1-2 0-2 0-4 1l-1 1c0-3-1-3-2-4h0c3-2 5-2 7-5l2-2z" class="G"></path><path d="M690 372c0 2-2 3-2 5-1 2-3 3-4 5l-1 1c0-3-1-3-2-4h0c3-2 5-2 7-5l2-2z" class="F"></path><path d="M235 324l-2-5c5 5 9 11 15 14 7 4 16 6 24 7l20 3c7 1 14 3 22 5 0 0 2 1 3 1 3 1 5 2 8 3 9 4 17 9 25 13l1-4v-1c-1-1-3-2-4-3-5-4-14-11-20-12v-1c9 2 18 10 25 15 1 1 4 2 4 4 1 1 1 2 1 3 1 1 1 1 1 2 4 4 6 11 11 14v-1-1c1 0 1 1 2 1h0v-1l1-2c2 0 2 0 3 1 1 2-1 4-1 6l-1 1v1c5 4 8 9 12 13 3 4 6 7 8 11-4-2-6-6-9-9-1-2-3-4-5-6-1-2-4-5-6-7-2-1-4-3-6-4-2-2-5-3-7-4-3-2-6-5-9-7-1 0-1 0-2-1-4-2-8-3-12-4s-7-1-11-3h1c-4-1-6-2-10-2l1-1-1-1h-2-3v-2h-2c-3 2-8 1-10 0h1c1-1 0-1 1-1-1-1-1-1-2-1v-1h-1c-2-2-4-4-5-6-2-1-3-2-5-3h-2c-1 0-3 0-5-1h0-2l-1 1h-5l1-1h1l1-1h3c1 0 1-1 3-1h0 0-1c-3-3-10-1-14-3h0c-7-1-15-2-20-5v-1l-3-2h-1c-2-1-2-3-3-4-1-2-4-4-6-6z" class="U"></path><path d="M358 368c0 1 1 3 0 4l-7-6h2c0-1 1-1 2-2v-1c1 1 2 2 2 3 1 1 1 1 1 2z" class="I"></path><path d="M241 330c9 6 18 8 29 10v1l-2 1c-7-1-15-2-20-5v-1l-3-2h-1c-2-1-2-3-3-4z" class="G"></path><path d="M332 361c5 2 12 5 17 8l-1 1-8-3v1h1c1 0 1 0 2 1 2 1 5 2 6 3v1c-4-2-8-3-12-4s-7-1-11-3h1c-4-1-6-2-10-2l1-1c3 0 7 1 10 0v-2h0 4z" class="Q"></path><path d="M270 340l13 2c2 0 3 0 5 1h1c3 0 5 1 8 2 2 0 4 1 6 1-3 0-7-2-9 0h-1-3-1 0c-2 0-2 0-3-1h-1c1 1 2 1 3 1l1 1h2 0c3 1 4 3 6 4l3 3h0c-1 0 0 1-1 0l-5-3c-2-1-3-2-5-3h-2c-1 0-3 0-5-1h0-2l-1 1h-5l1-1h1l1-1h3c1 0 1-1 3-1h0 0-1c-3-3-10-1-14-3h0l2-1v-1z" class="R"></path><path d="M294 346c2-2 6 0 9 0 5 2 11 2 16 5v2c2 3 7 4 9 7-4-1-10-3-14-5-1-1-2-1-4-2-2 0-3-2-5-2-3-1-4-2-6-3s-4-2-5-2z" class="Q"></path><path d="M294 351l5 3c1 1 0 0 1 0h0l-3-3c-2-1-3-3-6-4h0-2l-1-1c-1 0-2 0-3-1h1c1 1 1 1 3 1h0 1 3 1c1 0 3 1 5 2s3 2 6 3c2 0 3 2 5 2 2 1 3 1 4 2 4 2 10 4 14 5l4 1h-4 0v2c-3 1-7 0-10 0l-1-1h-2-3v-2h-2c-3 2-8 1-10 0h1c1-1 0-1 1-1-1-1-1-1-2-1v-1h-1c-2-2-4-4-5-6z" class="P"></path><path d="M301 360c8-1 19-2 27 1h0v2c-3 1-7 0-10 0l-1-1h-2-3v-2h-2c-3 2-8 1-10 0h1z" class="O"></path><defs><linearGradient id="W" x1="341.307" y1="392.789" x2="357.281" y2="434.744" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#3a3939"></stop></linearGradient></defs><path fill="url(#W)" d="M333 424c-1 3-2 5-1 8v1h0l-3-2h0l1 2v1h1 0l-4-2-2-2h-1c-2-1-4-5-4-7l-1-1c-1-3-1-7 0-10 1-6 5-11 10-15h1-1l-5 3c-7 0-15-5-21-9h0 1c1 0 1 1 2 1h1c3 2 7 4 10 6h1c1 0 2 0 3 1h1c1 0 2 0 4-1 0-1 1-1 3-2l-2-1c-1 0-3 1-3 0-2 0-2-1-4-1h-1-1c-1 0-2-1-2-1v-1l1 1h1 3 2c0 1 1 1 2 1h1c1 0 2 0 3 1v1l1-3c1 1 1 2 1 3l2-1h0c2 0 6-1 9-2s6-1 9 1c2 0 2-1 4 0l5 3c1 0 3 2 5 3l3 2c1 1 2 1 3 2l2 2 1 1c2 3 6 4 6 9l14 16c-5-1-8-7-12-9l-1-1c-3-2-6-3-10-4h-1-3-1c-2 0-4 1-6 2l-12-2c0-1 0 0-1-1l-1-3c0-2 0-4-1-5l-1 1c0 1 0 1 1 2 0 2 1 4 1 5 1 1 1 2 1 2h-2c-1 0-4 1-5 1-3 1-5 2-7 4z"></path><path d="M345 419c0-1 0-2-1-3s-1-3-1-5l1-1-1-2v-2h0v-1c1 2 1 3 2 4l-1 1c0 1 0 1 1 2 0 2 1 4 1 5 1 1 1 2 1 2h-2z" class="D"></path><path d="M333 424c0-1-1-1-1-2-3-2-6-5-6-9 2 1 4 3 7 4 2 1 4 1 7 3-3 1-5 2-7 4z" class="E"></path><path d="M351 394c2 0 2-1 4 0l5 3c1 0 3 2 5 3l3 2c1 1 2 1 3 2l2 2 1 1c2 3 6 4 6 9-6-6-12-12-18-16-4-3-7-4-11-6z" class="V"></path><path d="M312 505c12-1 23-1 35 1h0v1c2 1 4 1 6 1 4 1 7 2 11 4-2 0-6-1-8-2-3 0-6-1-8-1-3 0-7-1-9 0s-3 2-5 2v1h1v1l-11 2v1h0l-15 3c-15 5-34 16-41 31-2 4-4 8-3 11h0 2v2l2-2v2c0 2 2 3 4 5 0 1 1 1 2 2 2 0 3-1 5-2v-1c0-2 0-3-1-5h0c-1-2-2-3-4-4 1 0 2-1 3 0 2 1 4 3 5 4l-1 4c-1 2-2 4-5 6-1 1-3 1-5 1-1 1-4 0-6 0-2-1-4-3-6-3h-2l-1 1c-1-4-2-7-2-11-1-12 4-23 12-32 9-9 23-17 35-20 3-1 7-2 10-3z" class="E"></path><path d="M275 558c1 0 2-1 3 0 2 1 4 3 5 4l-1 4c-1 2-2 4-5 6-1 1-3 1-5 1h0c-2 0-4-1-6-2h0c-2-2-3-3-3-7 1 1 1 2 2 3v-2l1-1c1 2 2 3 2 4 2 2 4 4 7 3 2 0 3-1 4-2l1-1v-1c0-2 0-3-1-5h0c-1-2-2-3-4-4z" class="C"></path><path d="M323 506c5-1 10 0 15 0l-33 10c-2 1-5 3-7 4-3 1-5 2-8 4-11 7-19 16-24 27-2 4-4 10-3 13 0 4 1 5 3 7h0l-3-1c-1 0-2-1-2-3-2-6 0-12 2-17 0-1 1-2 1-3s1-1 1-1l-1-1 2-2c1-1 1-2 2-3 0-1 1-2 2-3h0c0-1 1-2 2-3v-1c5-7 13-10 20-14 7-3 13-7 21-9h1 3c1-1 1-1 3-1 1-1 5 0 7 0h1c0-1 0-1 1-1 1-1 0-1 1-1h1l-8-1z" class="G"></path><path d="M257 571c-1-4-2-7-2-11-1-12 4-23 12-32 9-9 23-17 35-20 3-1 7-2 10-3 2 1 2 1 4 1h7l8 1h-1c-1 0 0 0-1 1-1 0-1 0-1 1h-1c-2 0-6-1-7 0-2 0-2 0-3 1h-3-1c-8 2-14 6-21 9-7 4-15 7-20 14v1c-1 1-2 2-2 3h0c-1 1-2 2-2 3-1 1-1 2-2 3l-2 2 1 1s-1 0-1 1-1 2-1 3c-2 5-4 11-2 17 0 2 1 3 2 3l3 1c2 1 4 2 6 2h0c-1 1-4 0-6 0-2-1-4-3-6-3h-2l-1 1z" class="R"></path><path d="M414 316h0c1 2 2 4 4 5-1 2-3 4-3 6-2 5-1 11 0 15l72 192h0c-1 2 0 3 0 4l1 2c0 1 1 1 1 2v1l1 1c0 2 1 4 1 6 0-1-1-2-2-3l-2 2c-3-2-6-21-10-24-1-1-2-7-3-9l-12-32c-2-6-4-12-7-18l-3-3c-1-1-7-17-8-20l2 2 1-1-31-80c-4-13-11-27-9-40h0c1-2 1-3 2-4s0-1 1-1h1 2 1c0-1 0-2-1-3h1z" class="L"></path><path d="M444 443l2 2 1-1c3 7 6 15 8 22l-3-3c-1-1-7-17-8-20z" class="I"></path><defs><linearGradient id="X" x1="382.304" y1="414.253" x2="396.862" y2="499.719" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#343435"></stop></linearGradient></defs><path fill="url(#X)" d="M360 420c2-1 4-2 6-2h1 3 1c4 1 7 2 10 4l1 1c4 2 7 8 12 9l5 5c0 1 0 1 1 1l2 2c1 2 4 4 5 7l1 1 1-1c2 3 2 6 3 9 6 10 9 20 13 30 1 3 5 8 4 12l-1-1c0-1 0-2-1-2l-7-10-1-2c-1-2-4-4-6-7-6-7-13-13-20-20-2-1-4-3-6-4l-1-1h-2c-1 0-1-1-2-1-1-1-2-1-2-1l-1-1-2-1-4-2-5-3-5-2c-3-1-5-2-6-5 0-1 0-2-1-2 0-1-1-1-1-2v-1l-1-1v-3l-3-6h0-2l2 5v1h0c-2-1-3-6-3-8l12 2z"></path><path d="M360 420c2-1 4-2 6-2h1 3 1c4 1 7 2 10 4l1 1c4 2 7 8 12 9l5 5c0 1 0 1 1 1l2 2c1 2 4 4 5 7l1 1 1 2v1l-1-2c-4-5-9-10-15-13-10-7-21-13-33-16z" class="G"></path><defs><linearGradient id="Y" x1="728.724" y1="323.593" x2="735.751" y2="289.607" xlink:href="#B"><stop offset="0" stop-color="#8e8d8e"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#Y)" d="M724 281c5-1 9-2 14-2v1h9c2 0 3-1 4 0h1 0 1c3 2 8 3 12 4 15 5 33 16 39 32 1 3 1 8 0 11h0c-2-2-3-6-4-9-1-2-3-5-4-7l-6-6-16 2c-1 0-6-2-8-2-5-2-21-7-25-5h0c-2-1-4-1-6-1-7 1-13 2-20 4l-13 6c-4 4-9 7-13 12l-2 3c-1-1-1-1-1-2l-1 1c1-2 2-3 3-5l-1-1 2-2 6-7h-3c1-3 5-8 8-9 5-3 8-8 12-11l4-2c-2 1-4 2-6 2h-1l15-7z"></path><path d="M693 312v1c-1 1-2 3-2 4 2-1 4-4 6-5 1 0 0 0 1-1s2-2 3-2h1c-4 4-9 7-13 12l-2 3c-1-1-1-1-1-2l-1 1c1-2 2-3 3-5 2-2 3-4 5-6z" class="Q"></path><path d="M695 308c5-8 15-17 23-20-2 2-4 3-5 5l1 1c-3 2-6 4-8 6-4 2-6 5-9 8-1 1-3 2-4 4-2 2-3 4-5 6l-1-1 2-2 6-7z" class="L"></path><path d="M718 288c16-7 34-5 50 1 9 4 22 9 26 18 1 1 2 2 2 4l-6-6-3-3c-4-3-9-5-14-8-3 1-6 0-9 0-2 0-4 1-6 0-3 0-6 1-8 0h-5c-2-1-4-1-6-2v1l-1-1c-6-2-13-2-19-1l-5 3-1-1c1-2 3-3 5-5z" class="U"></path><path d="M719 291c9-6 24-2 34-1l14 3 6 1c-3 1-6 0-9 0-2 0-4 1-6 0-3 0-6 1-8 0h-5c-2-1-4-1-6-2v1l-1-1c-6-2-13-2-19-1zm5-10c5-1 9-2 14-2v1h9c2 0 3-1 4 0h1 0 1c3 2 8 3 12 4 15 5 33 16 39 32 1 3 1 8 0 11h0c-2-2-3-6-4-9-1-2-3-5-4-7 0-2-1-3-2-4-4-9-17-14-26-18-16-6-34-8-50-1-8 3-18 12-23 20h-3c1-3 5-8 8-9 5-3 8-8 12-11l4-2c-2 1-4 2-6 2h-1l15-7z" class="C"></path><path d="M632 435v1c3-3 4-6 6-10 1-1 2-2 2-4 1-2 2-4 3-5v-1c1-3 1-5 2-8 0-1 1-3 2-4l7-16h1c0-2 1-3 2-5 0 2 0 3-1 5h0c0 1-1 1-1 2s0 0 1 1c-1 2-2 5-3 7 0 1-1 2-1 4-1 2-2 3-2 5-1 2-4 5-4 8h0c-3 7-5 13-9 20v2 2 1c-2 3-3 5-3 8l5-10 1 1c-2 3-6 9-5 12 0 4-5 9-6 13h1c2-3 3-6 5-9s4-5 7-8l2-1 2-2c1-2 2-2 4-3s4-2 7-2c3-2 9-2 12-2-1 5-2 13-5 18-1 0-1 0-1 1-1 1-1 2-2 3v1l1 1-1 1-21 14c-6 6-13 13-18 20-3 3-6 8-8 11-1 2-2 3-3 5-6 8-8 17-12 25v-1h-1c0-2 1-4 1-5l10-25 16-50c2-7 4-14 7-21z" class="M"></path><path d="M657 439c3-2 9-2 12-2-1 5-2 13-5 18v-3c1-2 0-4 1-6v-1c-1-1-1-1-2-1l-1-1h-1-5l-2-1 1-1c1 0 1-1 2-2z" class="R"></path><path d="M650 441c2-1 4-2 7-2-1 1-1 2-2 2-2 1-4 1-5 3h0c3 1 8 1 11 4h0c-1 2-1 3 0 4 0 2-2 4-3 5-2 3-7 5-10 7-2 2-4 4-6 5-5 4-11 8-15 14-2 2-4 3-6 5-2 3-3 6-5 8 0-2 2-5 2-7l1-2c3-2 5-5 6-8 2-1 4-2 5-3 2-1 3-3 5-4s3-2 5-3c2-2 4-3 6-5l-1-2c2 0 3-1 4-2 2-2 5-2 6-4 0-2 0-4-1-5l-1-1v-1h0c-1-2-3-2-5-2h-2v-3c1-2 2-2 4-3z" class="H"></path><path d="M646 444c1-2 2-2 4-3l-1 1-3 4c4 0 6 0 10 2 1 1 3 3 2 6 0 2-3 4-5 5-3 2-5 4-7 5l-1-2c2 0 3-1 4-2 2-2 5-2 6-4 0-2 0-4-1-5l-1-1v-1h0c-1-2-3-2-5-2h-2v-3z" class="O"></path><path d="M646 444v3h2c2 0 4 0 5 2h0v1l1 1c1 1 1 3 1 5-1 2-4 2-6 4-1 1-2 2-4 2l1 2c-2 2-4 3-6 5-2 1-3 2-5 3s-3 3-5 4c-1 1-3 2-5 3-1 3-3 6-6 8 2-5 5-12 8-17l3-6c2-3 3-6 5-9s4-5 7-8l2-1 2-2z" class="E"></path><path d="M646 444v3h2c2 0 4 0 5 2h0v1l1 1c1 1 1 3 1 5-1 2-4 2-6 4-1 1-2 2-4 2l1 2c-2 2-4 3-6 5-2 1-3 2-5 3s-3 3-5 4c-1 1-3 2-5 3-1 3-3 6-6 8 2-5 5-12 8-17 1 0 2 0 2-1l3-3h1c1 2-1 4-1 6h1l-1-1 1-1 1-1c1-1 0-1 1-2s3-1 4-2h0c1 0 1-1 1-2l1 2c0-1 1-1 1-1 0-1-1-2-1-3 1 0 2-1 2-1 2 0 2 0 4-1 1-1 1-1 2-1 2 0 2-1 3-2l1 1c0-1 1-1 1-1l-1-1h0v-3l-1-1v-2c-1 0-4-1-5-1-2 0-1 1-3 1v-2-1l2-2z" class="S"></path><path d="M625 479c1-2 2-3 2-5l1-1c0-1 1-1 2-2l2 2c1 0 2-1 3-2 1-2 3-3 5-5 2-1 3-2 6-2-2 2-4 3-6 5-2 1-3 2-5 3s-3 3-5 4c-1 1-3 2-5 3z" class="O"></path><path d="M632 435v1c3-3 4-6 6-10 1-1 2-2 2-4 1-2 2-4 3-5v-1c1-3 1-5 2-8 0-1 1-3 2-4l7-16h1c0-2 1-3 2-5 0 2 0 3-1 5h0c0 1-1 1-1 2s0 0 1 1c-1 2-2 5-3 7 0 1-1 2-1 4-1 2-2 3-2 5-1 2-4 5-4 8h0c-3 7-5 13-9 20v2 2 1c-2 3-3 5-3 8l5-10 1 1c-2 3-6 9-5 12 0 4-5 9-6 13h1l-3 6c-3 5-6 12-8 17l-1 2c0 2-2 5-2 7l-7 17c10-14 19-29 32-39 6-5 14-10 20-14l1 1-1 1-21 14c-6 6-13 13-18 20-3 3-6 8-8 11-1 2-2 3-3 5-6 8-8 17-12 25v-1h-1c0-2 1-4 1-5l10-25 16-50c2-7 4-14 7-21z" class="B"></path><path d="M619 479v-2c1-2 2-5 3-8 2-5 5-19 8-22 0 3-2 7-1 9l1 1c-1 2-3 3-4 5-1 1-1 3-2 5l-5 12z" class="S"></path><path d="M630 447c1-1 3-3 3-5 1-2 2-3 3-4 0-2 1-2 1-3v2 2 1c-2 3-3 5-3 8-1 3-3 6-4 9l-1-1c-1-2 1-6 1-9z" class="O"></path><path d="M639 438l1 1c-2 3-6 9-5 12 0 4-5 9-6 13h1l-3 6c-3 5-6 12-8 17l-1 2v-3c0-1 1-2 1-3v-1-3l5-12c1-2 1-4 2-5 1-2 3-3 4-5 1-3 3-6 4-9l5-10z" class="C"></path><path d="M624 467l2-2v1c-1 1-1 2-1 4 0 1-1 2-2 3l-1 4h0c2-1 3-5 4-7 0-2 1-4 2-6h1 1l-3 6c-3 5-6 12-8 17l-1 2v-3c0-1 1-2 1-3v-1-3l5-12z" class="L"></path><defs><linearGradient id="Z" x1="695.556" y1="386.824" x2="633.949" y2="385.002" xlink:href="#B"><stop offset="0" stop-color="#838283"></stop><stop offset="1" stop-color="#c5c4c4"></stop></linearGradient></defs><path fill="url(#Z)" d="M708 309c6-2 11-5 17-2 1 1 1 1 1 2-2 3-4 5-6 8-2 1-3 3-4 3l-1 1c-1 1-2 1-3 3v2l-2 2c-1 2-1 4-2 6s-3 3-4 5c0 1-1 2-2 3-2 2-3 4-4 7-1 1 0 2-1 3-1 2 0 3-2 5h0c-1 1-1 2-2 2l-1 1c-1 0-2 1-3 2-2 1-3 2-5 2h-1-1l-1-1c-2 0-3 0-5 1-1 1-2 2-4 1l-2 1c-2 1-4 3-6 3l-1 1c-1 0-1 0-2 1h0c-1 2-3 4-3 6 1 2 1 4 0 5l-1 1h0c-1 2-2 3-2 5h-1l-7 16c-1 1-2 3-2 4-1 3-1 5-2 8v1c-1 1-2 3-3 5 0 2-1 3-2 4-2 4-3 7-6 10v-1l26-71 1-1h2c0-1 2-3 2-4 2-3 4-6 6-8 0-1 1-2 1-3h-1c5-9 11-17 18-24l2-3c5 0 8-4 12-7 2-1 5-3 7-5z"></path><path d="M682 333c1 0 2 0 3-1h1 3c0-1 0-1 1-2h1l1 2h0v-1h1c-1 1-1 1-1 2s1 2 1 3h2s0 1 1 1c2 0 3 1 4 2l-2 1h-3-1-1v-1h2 2v-1h-3c-1 0-1 0-1-1l-1 1h-2l-1-1-1 1-2-1c-2 0-3 2-5 2h-1c-1 0-3 1-4 2h0c-1 2-2 3-4 4 2-4 6-8 10-12z" class="B"></path><path d="M708 309c6-2 11-5 17-2 1 1 1 1 1 2-2 3-4 5-6 8-2 1-3 3-4 3l-1 1c-1 1-2 1-3 3v2l-2 2c-1 2-1 4-2 6s-3 3-4 5c0 1-1 2-2 3-2 2-3 4-4 7-1 1 0 2-1 3-1 2 0 3-2 5h0c-1 1-1 2-2 2l-1 1c-1 0-2 1-3 2-2 1-3 2-5 2h-1-1l-1-1h1c3-1 5-3 7-5v-1-2c2-2-1-1-1-3v-2h0c1-2 2-3 2-5h0l2 2 2-2v-3l-1-1 1-1h1 3l2-1c-1-1-2-2-4-2-1 0-1-1-1-1h-2c0-1-1-2-1-3s0-1 1-2h-1v1h0l-1-2h-1c-1 1-1 1-1 2h-3-1c-1 1-2 1-3 1-4 4-8 8-10 12l-2 3h-1c5-9 11-17 18-24l2-3c5 0 8-4 12-7 2-1 5-3 7-5z" class="F"></path><path d="M684 330c2 0 4-1 5-2 1 0 1-1 2-1 2 0 3-2 4-3s2-2 4-2l2-2c1 0 1-1 2-1l2-1h0l4-4h1c1 0 1-1 2-1s1 0 2-1h2c1-1 1-1 2-1l1 1h1l-1 2-3 3-2-2c-1 0-2 1-3 2l-1 1c-2 1-3 2-5 3h0c-1 1 0 1-1 1s-1 0-2 1-3 1-5 2c0 1-1 2-1 2l-1 1-1-1c-1 2-2 1-3 3h-1c-1 1-1 1-1 2h-3-1c-1 1-2 1-3 1l2-3zm16 9c2-2 2-4 4-5 0-1 1-1 1-2l3-3c0 2-1 3-3 5 0 1-1 2-1 2-1 1 0 1-1 2-3 3-4 6-6 9l-1-1-1 1v3 1c-1 1-1 1-1 2-1 4-7 9-10 11h-1-1l-1-1h1c3-1 5-3 7-5v-1-2c2-2-1-1-1-3v-2h0c1-2 2-3 2-5h0l2 2 2-2v-3l-1-1 1-1h1 3l2-1z" class="S"></path><path d="M708 309c6-2 11-5 17-2 1 1 1 1 1 2-2 3-4 5-6 8-2 1-3 3-4 3l2-3c1-2 2-3 3-4 0-2 0-2 1-3-2-1-5 0-7 0-1 0-2 0-2 1h-1c-4 2-7 4-10 6-2 2-4 3-5 4-1 0-2 1-3 1-3 1-8 5-10 8l-2 3c-4 4-8 8-10 12l-2 3h-1c5-9 11-17 18-24l2-3c5 0 8-4 12-7 2-1 5-3 7-5z" class="G"></path><path d="M691 330c1-2 2-1 3-3l1 1 1-1s1-1 1-2c2-1 4-1 5-2s1-1 2-1 0 0 1-1h0c2-1 3-2 5-3l1-1c1-1 2-2 3-2l2 2c-1 1-2 1-3 2-1 2-2 2-2 4-1 1 0 2-1 3l-1 1c-1 1 0 1-1 2l-3 3c0 1-1 1-1 2-2 1-2 3-4 5-1-1-2-2-4-2-1 0-1-1-1-1h-2c0-1-1-2-1-3s0-1 1-2h-1v1h0l-1-2z" class="E"></path><path d="M596 464v4c-1 1-1 0 0 2h2v2c1-2 2-3 2-5v-1c1-1 1-2 2-3v-1h1l-1 3v1 1 3c0 1-1 2-1 3h1v-1c0 1-1 3-1 3l-30 85-3 8-7 21-6 24-9 27-2 7v2l-1 3v2c-1 1-1 3-1 5v1 1l-3 8-1 3v2c-1 1-1 3-1 4-1 1-1 2-1 3-1 3 0 8 0 11-1 2-2 5-2 8-2 3-3 6-4 10l1 1-6 15-2 5c-1 2-1 3-2 4v2h-1v-1l-7-22-2-5h1 0c1-2 1-4 1-5 1-6 0-11 0-17-1-1-3-2-4-2l-1-1-1-1v1h0c-1-1-1-1-1-2v-1l-1-4-1-2c-1-2-1-3-2-5h1c0-1 0-2-1-3s-1-2-2-4l-3-11-4-10c-1-2-2-3-2-5h1c0 1 1 3 1 4l1-1v-2l1 2v-1s0-1-1-2l2-1 1 1 1-1h0c0-1 1-2 1-2-1-1-1-1-2-1 0-1 1-2 1-3l-1-1h1l1 1h0c1 1 1 2 2 2v2l2 2v1 2c1 1 1 2 1 3 2 1 3 3 2 5v1l2 2v-2-2l1 1c1 1 1 2 3 3v3l1 1v2l1 2 1 1c-1-3-2-5-2-8 0-1 1 0 0-1 0-3 0-7-1-10-1-4-3-7-3-12l2 1c2 6 3 13 6 19 0-2 0-5-1-7v-6l2 5v1l3 10 4 10c0 2 1 4 2 6 1-2-1-3 0-5 1-4 0-8 1-12v-1c0 3 0 7 1 9 2-11 6-21 10-32l15-43 25-71c5-16 11-32 17-48z" class="V"></path><path d="M522 719c-1 3-2 10 0 12v-1l2-4c0-2 0-1-1-3 1-5 3-11 5-16 1-9 2-19 5-28v-1c1-1 1-2 1-3 1-1 1-2 1-3h0v-2l9-27c1-5 3-10 4-15l5-15 4-13c1-4 2-8 4-11l-6 24-9 27-2 7v2l-1 3v2c-1 1-1 3-1 5v1 1l-3 8-1 3v2c-1 1-1 3-1 4-1 1-1 2-1 3-1 3 0 8 0 11-1 2-2 5-2 8-2 3-3 6-4 10l1 1-6 15-2 5c-1 2-1 3-2 4v2h-1v-1c0-3 0-5-1-7l-2-6c1-3 1-7 1-10 1-11 1-21 0-31 0-3 0-5-1-8 0-2 1-5 0-7-1-3-2-6-2-8-1-3-2-5-2-8 0-1 1 0 0-1 0-3 0-7-1-10-1-4-3-7-3-12l2 1c2 6 3 13 6 19 0-2 0-5-1-7v-6l2 5v1l3 10c-1 1 0 4 1 5 1 4 1 8 1 11 1 13 0 25-1 38 0 4 1 10 0 14z" class="D"></path><path d="M518 641l3 10c-1 1 0 4 1 5 1 4 1 8 1 11 1 13 0 25-1 38 0 4 1 10 0 14v-19c0-11 1-23 1-34-1-9-5-17-5-25z" class="P"></path><path d="M525 726v-2l1-4c1-2 1-2 1-4h0v-1c0-4 2-9 3-13l3-19c1-4 3-10 5-14h1l-1 3v2c-1 1-1 3-1 4-1 1-1 2-1 3-1 3 0 8 0 11-1 2-2 5-2 8-2 3-3 6-4 10l1 1-6 15z" class="I"></path><path d="M515 659c-1-3-2-5-2-8 0-1 1 0 0-1 0-3 0-7-1-10-1-4-3-7-3-12l2 1c2 6 3 13 6 19 1 4 2 8 2 12l1 3v9c0 5 1 10 1 16 0 13-2 25-1 38 0 1 0 2-1 3l-2-6c1-3 1-7 1-10 1-11 1-21 0-31 0-3 0-5-1-8 0-2 1-5 0-7-1-3-2-6-2-8z" class="T"></path><path d="M498 636c0-1 1-2 1-2-1-1-1-1-2-1 0-1 1-2 1-3l-1-1h1l1 1h0c1 1 1 2 2 2v2l2 2v1 2c1 1 1 2 1 3 2 1 3 3 2 5v1l2 2v-2-2l1 1c1 1 1 2 3 3v3l1 1v2l1 2 1 1c0 2 1 5 2 8 1 2 0 5 0 7 1 3 1 5 1 8 1 10 1 20 0 31 0 3 0 7-1 10l2 6c1 2 1 4 1 7l-7-22-2-5h1 0c1-2 1-4 1-5 1-6 0-11 0-17-1-1-3-2-4-2l-1-1-1-1v1h0c-1-1-1-1-1-2v-1l-1-4-1-2c-1-2-1-3-2-5h1c0-1 0-2-1-3s-1-2-2-4l-3-11-4-10c-1-2-2-3-2-5h1c0 1 1 3 1 4l1-1v-2l1 2v-1s0-1-1-2l2-1 1 1 1-1h0z" class="S"></path><path d="M508 650v-2-2l1 1c1 1 1 2 3 3l-2 1c0 1 0 1 1 2v1c0 2 2 3 2 4 1 2 1 4 1 6 2 4 2 10 2 14 0 2 0 4 1 6v4l-1-3v-5c-1-4-2-9-3-13h0v-1c-1-2-1-1-1-2v-2h-1c0-3-1-4-2-5l-1-1h1c0-1-1-2-1-2v-2l-1-1 1-1z" class="H"></path><path d="M512 650v3l1 1v2l1 2 1 1c0 2 1 5 2 8 1 2 0 5 0 7 1 3 1 5 1 8 1 10 1 20 0 31 0 3 0 7-1 10-1-2-1-5-2-7 3-3 1-8 2-12v-3-13-4c-1-2-1-4-1-6 0-4 0-10-2-14 0-2 0-4-1-6 0-1-2-2-2-4v-1c-1-1-1-1-1-2l2-1z" class="J"></path><path d="M513 687c0-4-1-8-2-12 0-1 0-2-1-3 0-2-1-3-1-5-1-2-1-3-2-4v-2c-1-2-3-5-3-7l1-1h0c1 1 1 1 2 1 0 2 0 3 1 4v3l1 2c1 1 1 2 1 3 1 2 2 4 2 6 1 2 1 3 1 5 1 2 2 5 2 8v1c0 7 1 13 0 21 0 1 0 2-1 3v2l-1 2-2-5h1 0c1-2 1-4 1-5 1-6 0-11 0-17z" class="E"></path><defs><linearGradient id="a" x1="507.012" y1="659.246" x2="497.47" y2="664.221" xlink:href="#B"><stop offset="0" stop-color="#9d9b9c"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#a)" d="M498 636l1 1-1 3 1 2c2 1 3 4 4 6v2h1l1 3-1 1c0 2 2 5 3 7v2c1 1 1 2 2 4 0 2 1 3 1 5 1 1 1 2 1 3 1 4 2 8 2 12-1-1-3-2-4-2l-1-1-1-1v1h0c-1-1-1-1-1-2v-1l-1-4-1-2c-1-2-1-3-2-5h1c0-1 0-2-1-3s-1-2-2-4l-3-11-4-10c-1-2-2-3-2-5h1c0 1 1 3 1 4l1-1v-2l1 2v-1s0-1-1-2l2-1 1 1 1-1z"></path><path d="M499 642c2 1 3 4 4 6v2c-2-1-2-2-4-2l-1-1 1-1-1-1v-1-1l1-1z" class="Q"></path><defs><linearGradient id="b" x1="596.964" y1="627.657" x2="529.011" y2="506.516" xlink:href="#B"><stop offset="0" stop-color="#242c2f"></stop><stop offset="1" stop-color="#776e6b"></stop></linearGradient></defs><path fill="url(#b)" d="M596 464v4c-1 1-1 0 0 2h2v2c1-2 2-3 2-5v-1c1-1 1-2 2-3v-1h1l-1 3-73 204v-8-3c2-11 6-21 10-32l15-43 25-71c5-16 11-32 17-48z"></path><path d="M678 605s1 0 1 1c1-1 2-1 2-1l1 1h4l-1 1h-4v1c1 1 2 0 4 0s4-1 5-1h13c-3 2-6 2-9 3l-10 2c-2 1-6 1-7 3-1 1-4 1-5 1-8 1-15 3-22 5-12 5-23 10-34 16-1 1-3 2-4 2 0 1-1 2-1 3-3 6-5 12-7 18l-11 32-35 105-21 68c-1-1 0-3 0-4 1-3 2-6 2-9v-1l10-34c1-2 3-7 2-10 0-1 1-4 2-6l-1-1c-1 2-2 3-2 5h0l-2 2 2-7c-2 0-2 1-3 3l2-7c0-1 1-3 1-4 3-6 5-13 7-20 1-2 1-5 2-8l2-5c1-2 2-6 2-7l-1-1v-1h2l3-3c-1-2-1-3-2-4-1-2-2-3-4-3-2-1-2-1-3 0v2l-1-2c1-1 1-1 1-2h-3l-3 3c0 2 0 3-1 4l-1 1c0-1 0-1-1-2v-1l7-9v-1c-3 3-5 6-8 8-1 2-3 4-4 5l-3 3c-2 1-2 2-4 4l-1 1h0c0-1 0-2 1-2v-1l-2-2 4-7v-1l1-1c-1-1 0-1-1-2 3-3 4-9 6-12 1-3 2-6 4-9l2-5 11-19v-2c0-1 1-2 2-3v-1-2c1-2 2-4 4-6 1-1 1-2 1-4l2-2 6-8c11-13 22-24 35-33 4-3 9-6 13-9 1 0 2-1 3-1l10-5c9-4 18-8 28-10 2 0 8-2 10-1l-2 1v1l-5 5c3-1 5-3 7-5l2-3z" class="U"></path><path d="M599 650h-1c-1-1-1-1-2-1 1-1 3-2 4-3h0 3 0l-4 4h0z" class="C"></path><path d="M603 646l6-4-3 10c-1-1-1-2-2-2h-1-4l4-4z" class="R"></path><path d="M574 735l1-1h3c-2 5-3 12-6 16-1-1-2-2-2-4 1-2 1-4 2-6s2-3 2-5z" class="S"></path><path d="M579 722c2-3 3-7 6-9l-7 21h-3l-1 1v-2-1l-1-1c0-1 0-2 1-3 2-1 5-3 5-6z" class="F"></path><defs><linearGradient id="c" x1="577.812" y1="672.657" x2="573.685" y2="680.841" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#737372"></stop></linearGradient></defs><path fill="url(#c)" d="M580 669c2 1 2 3 3 5v1c1 0 2 1 3 1h-2 0c-1-1-3-1-4-1l1 2c-2 0-4 0-5 2-1 1-1 2 0 3-1 1-1 2-2 3l-1-1c0-1 0-2-1-3v1h0l-1-2c2-2 4-5 6-8l3-3z"></path><path d="M580 669c2 1 2 3 3 5h-2c-1 0-3-1-4-2l3-3z" class="M"></path><path d="M678 605s1 0 1 1c1-1 2-1 2-1l1 1h4l-1 1h-4v1c1 1 2 0 4 0s4-1 5-1h13c-3 2-6 2-9 3l-10 2c-2 1-6 1-7 3-1 1-4 1-5 1-8 1-15 3-22 5l3-1 1-1h2c1-1 1 0 2-1h-1-4c3-1 6-1 9-3h1c2-1 3-3 4-4s2-2 4-3h1c1-1 2-1 2-1v1l-5 5c3-1 5-3 7-5l2-3z" class="K"></path><path d="M599 650h0 4c-2 3-4 5-5 8-1 2-1 4-2 6-2 4-3 9-5 13-1-1-2-1-4-2l-1 1c-1 0-2-1-3-1v-1c-1-2-1-4-3-5v-1l4-4c5-3 10-10 15-14z" class="I"></path><path d="M580 668l4-4c2 1 4 3 5 6 1 1 0 3 0 4-1 0-1 1-2 1l-1 1c-1 0-2-1-3-1v-1c-1-2-1-4-3-5v-1z" class="K"></path><path d="M580 668h5c1 1 0 3-1 4 0 1 0 2-1 3v-1c-1-2-1-4-3-5v-1z" class="C"></path><defs><linearGradient id="d" x1="556.691" y1="777.527" x2="563.325" y2="780.18" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#828282"></stop></linearGradient></defs><path fill="url(#d)" d="M568 747l2-1c0 2 1 3 2 4v1l-9 27-12 38-8 25c-1 4-2 8-4 11v-1l10-34c1-2 3-7 2-10 0-1 1-4 2-6l-1-1c-1 2-2 3-2 5h0l-2 2 2-7c6-13 8-28 14-41 1-4 3-8 4-12z"></path><path d="M568 747l2-1c0 2 1 3 2 4v1c-1 1-2 1-2 3-1-1-1-1-1-2v1l-1 1h0v-1c-1 0-1 0-1 1-1 1-1 2-1 3-1 1-1 1-1 2h-1c1-4 3-8 4-12z" class="F"></path><path d="M603 650h1c1 0 1 1 2 2l-21 61c-3 2-4 6-6 9h0c-2-3 2-6 0-10 1-4 3-9 4-12 2-6 4-14 7-20l1-3c2-4 3-9 5-13 1-2 1-4 2-6 1-3 3-5 5-8z" class="M"></path><path d="M596 664v6c-2 3-1 9-5 11l-1-1 1-3c2-4 3-9 5-13z" class="F"></path><path d="M586 676l1-1c2 1 3 1 4 2l-1 3c-3 6-5 14-7 20-1 3-3 8-4 12 2 4-2 7 0 10h0c0 3-3 5-5 6-1 1-1 2-1 3l1 1v1 2c0 2-1 3-2 5s-1 4-2 6l-2 1c-1 4-3 8-4 12-6 13-8 28-14 41-2 0-2 1-3 3l2-7c0-1 1-3 1-4 3-6 5-13 7-20 1-2 1-5 2-8l2-5c1-2 2-6 2-7l-1-1v-1h2l3-3c-1-2-1-3-2-4-1-2-2-3-4-3-2-1-2-1-3 0v2l-1-2c1-1 1-1 1-2h-3l-3 3c0 2 0 3-1 4l-1 1c0-1 0-1-1-2v-1l7-9v-1l5-6c2-2 4-4 6-7l8-7 1 1v-1-1h-1 0c0-1 1-2 1-3 2-1 2-2 3-4 4-8 6-18 8-27-1-1-2-1-3-2h2z" class="P"></path><path d="M573 731l1 1v1 2c0 2-1 3-2 5s-1 4-2 6l-2 1 5-16zm6-19c2 4-2 7 0 10h0c0 3-3 5-5 6l5-16z" class="H"></path><path d="M567 720l8-7 1 1-1 1c0 1-1 2-1 4l-3 6c-1 3-2 7-3 10l-2 6v1l-1 1h0c-1-2-2-3-4-3-2-1-2-1-3 0v2l-1-2c1-1 1-1 1-2h-3l-3 3c0 2 0 3-1 4l-1 1c0-1 0-1-1-2v-1l7-9v-1l5-6c2-2 4-4 6-7z" class="M"></path><path d="M566 737v1l-2 2v-1c0-1 0-3 1-4l1 1v1z" class="J"></path><path d="M566 736v-2c0-1 0-2 1-2 1-1 1-2 2-4 0-1 1-2 2-3-1 3-2 7-3 10l-2 6c0-1 1-2 0-4v-1z" class="T"></path><path d="M572 717v3l-3 6c-1 2-2 4-3 5h-1c-1-1-1-1-1-2l-2 1h0l-1-1c1-2 4-5 6-7l1-1c2-1 3-3 4-4z" class="F"></path><path d="M567 720l8-7 1 1-1 1-3 2c-1 1-2 3-4 4l-1 1c-2 2-5 5-6 7l1 1h0c-2 3-5 5-7 8l-3 3c0 2 0 3-1 4l-1 1c0-1 0-1-1-2v-1l7-9v-1l5-6c2-2 4-4 6-7z" class="E"></path><defs><linearGradient id="e" x1="573.891" y1="716.371" x2="541.729" y2="712.611" xlink:href="#B"><stop offset="0" stop-color="#878687"></stop><stop offset="1" stop-color="#bbbab9"></stop></linearGradient></defs><path fill="url(#e)" d="M581 677l-1-2c1 0 3 0 4 1h0c1 1 2 1 3 2-2 9-4 19-8 27-1 2-1 3-3 4 0 1-1 2-1 3h0 1v1 1l-1-1-8 7c-2 3-4 5-6 7l-5 6c-3 3-5 6-8 8-1 2-3 4-4 5l-3 3c-2 1-2 2-4 4l-1 1h0c0-1 0-2 1-2v-1l-2-2 4-7v-1l1-1c-1-1 0-1-1-2 3-3 4-9 6-12 1-3 2-6 4-9l2-5 11-19 9-13 1 2h0v-1c1 1 1 2 1 3l1 1c1-1 1-2 2-3-1-1-1-2 0-3 1-2 3-2 5-2z"></path><path d="M551 712c1 2 1 3 0 5l-1 1-1-1 2-5z" class="B"></path><path d="M539 742c0 1-1 2-1 4l1-1c0-1 1-2 2-3v1h-1c-1 1-1 3-1 5h1 0c1-1 2-1 4-2l-3 3c-2 1-2 2-4 4l-1 1h0c0-1 0-2 1-2v-1l-2-2 4-7z" class="Q"></path><path d="M581 677l-1-2c1 0 3 0 4 1h0c1 1 2 1 3 2-2 9-4 19-8 27-1 2-1 3-3 4 0 1-1 2-1 3h0 1v1 1l-1-1-8 7c-2 3-4 5-6 7l-5 6c-3 3-5 6-8 8 1-2 3-4 4-5l8-10c1 0 2-2 2-2v-2s2-1 2-2c2-1 3-3 4-5 2-3 0-4 1-7 1-1 2-3 3-5h-1v-1l2-4c0-1 2-5 1-7h-1v-1h2l1-1v-1l1-1 1-1v-2-1c-1-1-1-1-2-1-1-1-1-2 0-3 1-2 3-2 5-2z" class="O"></path><path d="M581 677l-1-2c1 0 3 0 4 1h0c1 1 2 1 3 2-2 9-4 19-8 27-1 2-1 3-3 4 0 1-1 2-1 3h0 1v1 1l-1-1-8 7c0-1 2-2 3-3 2-2 3-4 4-7 0-2 1-4 2-6v-2l1-1c0-1 0-1-1-2h0l1-1v-2c1-2 0-4 1-5s1-2 1-3v-2l1-1c0-1 0-2 1-3h0l-1-2c1-1 1-2 1-3z" class="F"></path><defs><linearGradient id="f" x1="371.519" y1="490.868" x2="407.249" y2="473.803" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#333"></stop></linearGradient></defs><path fill="url(#f)" d="M351 426h0v-1l-2-5h2 0l3 6v3l1 1v1c0 1 1 1 1 2 1 0 1 1 1 2 1 3 3 4 6 5l5 2 5 3 4 2 2 1 1 1s1 0 2 1c1 0 1 1 2 1h2l1 1c2 1 4 3 6 4 7 7 14 13 20 20 2 3 5 5 6 7l1 2 7 10c1 0 1 1 1 2v1 2c1 0 1 1 1 1l2 3c0 1 1 2 1 3 1 1 1 1 1 2 1 0 1 1 1 2 1 1 1 2 2 2h0c1 0 1 0 1 1l2 5 1 1h1l21 56-3-2-1-3-3-3c-2-2-3-4-5-6-3-2-5-6-7-9-2-2-2-5-2-7h-1c-1 0-3 1-4 1h0l-2 2c-1-2-4-3-6-5-1-3-2-6-5-8-1 2-2 3-3 4l-2-1h0c-10-6-21-15-32-19-1-1-2-1-2-2l-33-92z"></path><path d="M380 460c1 0 1 0 2 2-1 2-1 2-3 4h-3c-1-1-2-1-3-2l1-2c2 0 2 0 3 1 1 0 1-1 2-2l1-1z" class="V"></path><path d="M439 539l2 2v2c1 3 3 5 4 8 2 3 4 7 5 11-3-2-5-6-7-9-1-2-1-4-2-6 0-1 0-2-1-3v-1c0-1-1-3-1-4z" class="I"></path><path d="M385 468l-1 1c-2-1-2-1-3-2 1-1 1-2 1-3l1-1c0-2 1-3 2-3h1l-1 1c-1 1-1 1-1 3 1-1 2-2 2-3h1c1-1 1-1 3-1v1c1-1 1-1 1-2-1-1-3-2-4-3-3-2-5-4-8-5l-1-1-3-3c-3-3-9-5-12-7h0l5 2 5 3 4 2 2 1 1 1s1 0 2 1c1 0 1 1 2 1 0 3 2 4 4 6 2 1 4 1 4 3l-1 1c-3 2-5 4-6 7z" class="P"></path><path d="M391 461c2 0 3 0 4 1 1 2 1 3 1 5 0 3 2 3 0 5-2 0-2 1-3 0v-1c-1 0-2 0-3-1l-2 2c-2 0-2-1-3-2v-2c1-3 3-5 6-7zm29 73c0-1-2-3-3-5-6-7-14-13-21-19-5-5-11-9-15-14v-1l29 24c4 4 7 8 11 12h1l1 2c-1 1-2 1-3 1z" class="S"></path><path d="M422 531c2 1 2 2 4 4 1 0 1 1 2 1 0 1 1 1 2 2l1 2c0 1 0 1 1 2 1-1 1-2 2-1h2 2l1 1v-1l-2-2c-3 0-3-1-5-3-1-1-1 0-2 0l-3-3v-1c4 2 8 5 12 7 0 1 1 3 1 4v1c1 1 1 2 1 3 1 2 1 4 2 6-2-2-2-5-2-7h-1c-1 0-3 1-4 1h0l-2 2c-1-2-4-3-6-5-1-3-2-6-5-8-1 2-2 3-3 4l-2-1-2-2c2-2 2-2 4-3 1 0 2 0 3-1l-1-2z" class="P"></path><path d="M420 534c1 0 2 0 3-1l9 12v-1c1-1 2-2 4-2h2v1l-2 4-2 2c-1-2-4-3-6-5-1-3-2-6-5-8-1 2-2 3-3 4l-2-1-2-2c2-2 2-2 4-3z" class="C"></path><path d="M373 464v1c-1-1-2-2-3-2h0c-1-1-1-1-2-1v1-1c-2-4-3-8-4-11l-2-4c-1-2-1-3-1-4 3 2 5 3 8 4 1 1 2 1 2 2 2 1 4 0 6 1 0 1 1 1 1 2h1v1l4 3v3c-1 0-2 0-2-1h-1v2l-1 1c-1 1-1 2-2 2-1-1-1-1-3-1l-1 2z" class="I"></path><path d="M379 461c-1 0-1 0-1-1-1-1-2-1-3-1 0-1-1-2-1-2l1-1h0l1-1h1 0v-1c-2 0-3 0-4-1-1 0-1 0-1-1l1-1h4c0 1 1 1 2 2h0l4 3v3c-1 0-2 0-2-1h-1v2l-1 1z" class="N"></path><path d="M387 452c2 1 4 3 6 4 7 7 14 13 20 20 2 3 5 5 6 7l1 2 7 10c1 0 1 1 1 2v1 2c1 0 1 1 1 1l2 3c0 1 1 2 1 3 1 1 1 1 1 2 1 0 1 1 1 2 1 1 1 2 2 2h0c1 0 1 0 1 1l2 5 1 1h1l21 56-3-2-1-3-3-3c-2-5-3-9-5-13-3-4-5-8-7-13 0-1-1-3-1-4l-1-1c-1 1-1 0-2 1l-2-2c-1-3-3-4-5-6l-2-2-2-2-5-5c-3-3-6-5-8-8l-8-9-3-3c-1-2-2-5-3-7 0-2 1-5 2-6l-1-2c-1-3 0-7 2-11 1-1 1-2 1-5h0l1 1v-2l-4-4c-2-1-2-1-3-2l-3-3c-2-1-3-3-5-4l-2-2c-1 0-2-1-2-2z" class="T"></path><path d="M436 513h0c1 0 1 0 1 1l2 5 1 1h1l21 56-3-2-1-3-5-12-3-6-3-6c0-2-1-3-1-5 0-1 0 0-1-1l-1-4c-1-3-1-5-2-7 0-1 0-2-1-3 0-1-1-2-1-3s0-1-1-2v-1l-1-1c0-2-1-3-1-4-1-1-1-1-1-3z" class="M"></path><path d="M593 247l-8-2c5-1 10 0 15 0h27 134 10c1 1 4 1 6 0v1c3 2 8 1 12 1-5 2-9 5-12 9l-6 3c2-2 4-4 5-7h-1v-1c-1 0-9 2-10 3l-17 7h0l-3 3-1 1c-1 1-4 1-5 3l-3 3c-1 3-4 6-7 8h0c-1 0-1 0-2 1-1 0-2 0-3 1l-15 7c-2 1-4 3-6 5-1 2-2 4-4 4-3 1-7 7-9 9-2 3-5 6-6 9 0 1-1 1-1 1-1 1-2 2-2 3-1 1-2 1-2 3-1 1-3 2-3 4 1-5 4-9 4-14 2-4 5-8 6-12l-1-1c-2-4-4-7-6-10-6-8-14-16-22-21h0c-3-3-8-5-11-7-16-8-35-13-53-14z" class="U"></path><path d="M775 251c0-1 1-1 2-1v-1c-4 0-9 1-12 1v-1h2 3l1-1h3l1-1 2-1c3 2 8 1 12 1-5 2-9 5-12 9l-6 3c2-2 4-4 5-7h-1v-1z" class="L"></path><path d="M686 300c6-3 12-7 17-11 3-2 6-5 9-6s5-2 7-3c3-2 7-3 9-5 1-2 3-3 4-4 2-1 4-2 5-4s9-5 11-6l-3 3-1 1c-1 1-4 1-5 3l-3 3c-1 3-4 6-7 8h0c-1 0-1 0-2 1-1 0-2 0-3 1l-15 7c-2 1-4 3-6 5-1 2-2 4-4 4-3 1-7 7-9 9-2 3-5 6-6 9 0 1-1 1-1 1-1 1-2 2-2 3-1 1-2 1-2 3-1 1-3 2-3 4 1-5 4-9 4-14 2-4 5-8 6-12z" class="M"></path><path d="M661 462c0 3-1 5-3 8-1 4-4 8-4 12l-1 2h-1v-1l-2-1v1c1 2 1 2 1 5v1l2-1v1c0 2-1 1-2 2 0 0 0 3-1 4l3-3v1 1h0l-5 15v3l1 1-4 15-3 6c-1 4-2 8-4 12l-1 1 1 1 2 1v4 1h-2-1l-4 1h0c-1 2-2 2-4 2l-1 1-1 1-4 4c-1 1-2 1-2 1-2 1-4 3-5 5 3-3 7-5 11-6 2-1 4-2 7-2l1 1c-1 1-4 1-6 2s-6 4-9 6c3 0 4 0 7-1 1-1 2-1 3-1 3 0 5-1 8 2-1 0-2 1-3 1h0c-2 0-5 0-6 1l-3 1c-1 3-3 2-4 5 0 2-2 3-1 6 0 3-2 6-4 8l-1 1h-2 0-1l1-1v-1-1h-2l3-3h0l-14 12h-3c-1 2-3 3-5 4l-1-1c0 1-1 1-1 1-6 9-13 18-17 28l-5 9-2 2-1 1v1h-1v-2c0-2 1-3 1-5l9-20v-1-2c1-1 1-1 1-2 1-3 2-6 2-10 1-1 1-3 1-4l5-13 3-8 4-10c0-2 0-3 1-4 1-2 1-4 2-6l-1-1c1-3 1-5 2-7l3-6v-2-1h0l1-3c4-8 6-17 12-25 1-2 2-3 3-5 2-3 5-8 8-11 5-7 12-14 18-20l21-14z" class="I"></path><path d="M634 551l4-15c1 3 1 4 0 7v3l-1 1 1 1 2 1v4 1h-2-1c0-1-1-1-2-1 0-1-1-1-1-2z" class="H"></path><path d="M638 554v-1c-1-1-1-2-1-3v-3l1 1 2 1v4 1h-2z" class="O"></path><path d="M578 621v-3l1-1c1-2 2-5 3-7s3-4 3-6c1-4 2-6 4-9v-2l2-3c2-3 3-5 6-8-1 3-1 5-3 8 0 0-1 1-1 2l-3 6c-2 2-3 4-3 6-3 6-6 12-9 17z" class="D"></path><path d="M638 536l10-27v3l1 1-4 15-3 6c-1 4-2 8-4 12v-3c1-3 1-4 0-7z" class="O"></path><path d="M615 545h2l1 1h0c1 0 1 0 2 1h-1 0c1 0 2 0 3 1l3 3v1l-3 1c-5 2-8 6-13 9-1 1-3 2-5 3v1l-2 1h-1l-2 1h-1c-1 2-2 2-3 3 2-5 5-11 9-16 1-1 3-2 4-3 1-2 1-2 2-3s4-3 5-4z" class="D"></path><path d="M615 545l1 1c-1 5-3 10-7 12-2 1-3 2-5 2 0-1 1-2 1-3 2-3 4-5 5-8 1-1 4-3 5-4zm-4-33c0 2-1 4-2 6l-3 8h1v1 1 2c1 1 1 1 1 2l1 1h1 3v1l-4 4c0 2 0 2 1 3h1s1-1 2 0c-4 4-7 9-9 14-4 5-7 11-9 16-5 9-10 19-13 28-2 5-3 9-5 13-1 1 0 1-1 2 0 1 0 2-1 3h0v-1-2c1-1 1-1 1-2 1-3 2-6 2-10 1-1 1-3 1-4l5-13 3-8 4-10c0-2 0-3 1-4 1-2 1-4 2-6l-1-1c1-3 1-5 2-7l3-6v-2-1h0l1-3c4-8 6-17 12-25z" class="J"></path><path d="M611 512c0 2-1 4-2 6l-3 8h1v1 1 2c1 1 1 1 1 2l1 1h1 3v1l-4 4c-2 1-2 2-3 4l-2 6h-1c0 1 0 1-1 2v1c-1 0-2 1-2 2-3 4-6 10-9 14 0-2 0-3 1-4 1-2 1-4 2-6l-1-1c1-3 1-5 2-7l3-6v-2-1h0l1-3c4-8 6-17 12-25z" class="E"></path><path d="M602 532l1 2v3c0 2-1 3-2 5 0 2-3 6-4 9v1h0c-2 2-2 3-3 5h0l-1-1c1-3 1-5 2-7l3-6v-2-1l4-8z" class="B"></path><path d="M611 512c0 2-1 4-2 6l-3 8h1v1 1 2c1 1 1 1 1 2l1 1h1 3v1l-4 4c-2 1-2 2-3 4l-2 6h-1c0 1 0 1-1 2v1c-1 0-2 1-2 2v-1c1-2 1-6 4-7h0v-2-3h0c1-2 1-4 1-5v-2l1-1c0-1-1-4-1-5l-3 5-4 8h0l1-3c4-8 6-17 12-25z" class="H"></path><path d="M645 507h0c-1 8-5 17-8 24-2 4-3 7-3 11-1 1-1 0-1 1l-1 1v3c-1 1-3 1-5 1-2-2-3-2-4-4l-1-1h-1l-2-1-1 1h-3c-1-1-2-1-2-2-1-1-2 0-2 0h-1c-1-1-1-1-1-3l4-4v-1h-3c1 0 1-1 2-1h1c1 0 0 0 1-1h0l1-1c1-1 2-1 3-1l1 1 1-1s1 0 2-1v-1c2-2 3-3 4-5l8-7 1-1c1-1 2-1 2-2 2-1 3-2 4-2l1-1c1-1 2-1 3-2z" class="G"></path><path d="M645 507h0c-1 8-5 17-8 24-2 4-3 7-3 11-1 1-1 0-1 1l-1 1v3c-1 1-3 1-5 1-2-2-3-2-4-4l-1-1h-1l-2-1-1 1-1-1-2-1 2-2h-1l-1-1c2-2 2-1 3-2 1 0 2-1 3-1s2 1 3 0h-3l1-1h2c1-1 2-1 3-1l1 1h1c1-1 6-4 6-5l1-1 1-1v-1c1-1 1-1 2-3h0l1-2v-1s1-1 1-2v-2l1-1 1-1v-3h1l-1-1h0l-1-1c1-1 2-1 3-2z" class="T"></path><path d="M618 536c3 0 6 0 8 1 1 1 1 1 1 2-2 0-3 0-4 1v4l-1-1h-1l-2-1-1 1-1-1-2-1 2-2h-1l-1-1c2-2 2-1 3-2z" class="N"></path><path d="M597 582c1-1 2-3 3-4s1-1 1-2 0 0 1-1c0-1 1-2 1-3h0l6-6c2-2 4-5 7-6 2-2 7-4 10-5 3-2 6-1 8-4 0 1 1 1 1 2 1 0 2 0 2 1l-4 1h0c-1 2-2 2-4 2l-1 1-1 1-4 4c-1 1-2 1-2 1-2 1-4 3-5 5 3-3 7-5 11-6 2-1 4-2 7-2l1 1c-1 1-4 1-6 2s-6 4-9 6c3 0 4 0 7-1 1-1 2-1 3-1 3 0 5-1 8 2-1 0-2 1-3 1h0c-2 0-5 0-6 1l-3 1c-1 3-3 2-4 5 0 2-2 3-1 6 0 3-2 6-4 8l-1 1h-2 0-1l1-1v-1-1h-2l3-3h0l-14 12h-3c-1 2-3 3-5 4l-1-1c0 1-1 1-1 1-6 9-13 18-17 28l-5 9-2 2 11-21c3-5 6-11 9-17 0-2 1-4 3-6l3-6c0-1 1-2 1-2 2-3 2-5 3-8z" class="R"></path><path d="M600 585c0-5 3-4 5-7 0-1 0-1 1-2 3-3 5-7 9-10 1-2 4-4 7-6 1 0 2-1 3-2s3-2 4-2c2 0 3-1 4-1h0c-1 2-2 2-4 2l-1 1-1 1-4 4c-1 1-2 1-2 1-2 1-4 3-5 5 3-3 7-5 11-6 2-1 4-2 7-2l1 1c-1 1-4 1-6 2s-6 4-9 6c-1 0-2 1-4 2-5 1-12 9-16 13z" class="S"></path><path d="M620 570c3 0 4 0 7-1 1-1 2-1 3-1 3 0 5-1 8 2-1 0-2 1-3 1h0c-2 0-5 0-6 1l-3 1-5 2c-3 0-5 1-8 2-2 1-3 3-5 4-9 6-13 13-17 22-6 9-13 18-17 28l-5 9-2 2 11-21c3-5 6-11 9-17 5-6 8-13 13-19 4-4 11-12 16-13 2-1 3-2 4-2z" class="K"></path><defs><linearGradient id="g" x1="622.872" y1="581.222" x2="599.322" y2="588.877" xlink:href="#B"><stop offset="0" stop-color="#1a1a1b"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#g)" d="M626 573c-1 3-3 2-4 5 0 2-2 3-1 6 0 3-2 6-4 8l-1 1h-2 0-1l1-1v-1-1h-2l3-3h0l-14 12h-3c-1 2-3 3-5 4l-1-1c0 1-1 1-1 1 4-9 8-16 17-22 2-1 3-3 5-4 3-1 5-2 8-2l5-2z"></path><g class="M"><path d="M603 593l11-8 1-1c1-1 3-1 4-2l1 1-2 4h-1c0 2-2 2-3 3h-2l3-3h0l-14 12h-3c1-1 3-2 3-3 1-1 1-2 2-3z"></path><path d="M591 603c4-9 8-16 17-22 2-1 3-3 5-4 3-1 5-2 8-2-2 2-3 3-6 4h-1c-1 0-1 1-2 2 0 1 0 1-1 2-3 2-6 6-9 8h0c1-2 3-4 5-6-3 1-7 5-9 7v3l2-2h3c-1 1-1 2-2 3 0 1-2 2-3 3-1 2-3 3-5 4l-1-1c0 1-1 1-1 1z"></path></g><path d="M598 595l2-2h3c-1 1-1 2-2 3 0 1-2 2-3 3-1 2-3 3-5 4l-1-1c1-1 1-2 2-2 1-1 2-2 2-3l2-2z" class="G"></path><path d="M661 462c0 3-1 5-3 8-1 4-4 8-4 12l-1 2h-1v-1l-2-1v1c1 2 1 2 1 5v1l2-1v1c0 2-1 1-2 2 0 0 0 3-1 4l-3 6c-1 1-2 2-1 3l1 1-2 2h0c-1 1-2 1-3 2l-1 1c-1 0-2 1-4 2 0 1-1 1-2 2l-1 1-8 7c-1 2-2 3-4 5v1c-1 1-2 1-2 1l-1 1-1-1c-1 0-2 0-3 1l-1 1h0c-1 1 0 1-1 1h-1c-1 0-1 1-2 1h-1l-1-1c0-1 0-1-1-2v-2-1-1h-1l3-8c1-2 2-4 2-6 1-2 2-3 3-5 2-3 5-8 8-11 5-7 12-14 18-20l21-14z" class="O"></path><path d="M640 476c0 2 1 3 0 4v1 1l-1 1 1 1c0 3 1 4 2 5 2 2 3 1 5 1h0c0-2-1-2-1-3v-3l2-1h2c1 2 1 2 1 5l-1 1v2c-1 1-4 5-6 6v-1c-1-1 0-1-1-2 0-1 0 0-1-1l-2 1h-1v-4c-1-1-2-2-2-3v-2c1-2 1-3 1-4-3 0-10 9-12 11-1 1-2 4-4 4 5-7 12-14 18-20z" class="F"></path><path d="M661 462c0 3-1 5-3 8-1 4-4 8-4 12l-1 2h-1v-1l-2-1v1h-2l-2 1v3c0 1 1 1 1 3h0c-2 0-3 1-5-1-1-1-2-2-2-5l-1-1 1-1v-1-1c1-1 0-2 0-4l21-14z" class="J"></path><path d="M652 483v-1c-1-2-1-2-3-2-1-1-1-2-1-3 2-2 4-5 6-7 0-1 1-2 1-2 1-1 3-2 4-2 0 2-1 3-1 4-1 4-4 8-4 12l-1 2h-1v-1z" class="H"></path><defs><linearGradient id="h" x1="628.687" y1="522.561" x2="622.763" y2="513.865" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#h)" d="M651 488v1l2-1v1c0 2-1 1-2 2 0 0 0 3-1 4l-3 6c-1 1-2 2-1 3l1 1-2 2h0c-1 1-2 1-3 2l-1 1c-1 0-2 1-4 2 0 1-1 1-2 2l-1 1-8 7c-1 2-2 3-4 5v1c-1 1-2 1-2 1l-1 1-1-1c-1 0-2 0-3 1l-1 1h0c-1 1 0 1-1 1h-1c-1 0-1 1-2 1h-1l-1-1c0-1 0-1-1-2v-2-1-1c0-1 1-2 2-2v-1c1-1 1-2 2-3s1-2 2-2l1 1h1c1 0 1-1 2-1s1 0 2-1h1c0-1 1-1 2-2 2-2 4-5 7-6 1-1 3-1 4-1l1-1h1c1 0 2-1 3-1v-1l-2 1h-1l4-3v-2-1c1 1 0 1 1 1 0-1 0-1 1-2h-1l1-1h1c0-1 0-1 1-2l-1-2h-2l2-1c1 1 1 0 1 1 1 1 0 1 1 2v1c2-1 5-5 6-6v-2l1-1z"></path><path d="M613 521c2-1 4 0 6 0v2l-2 1-1 1h-1l1-1h-2c-2 1-3 2-4 2h0v-1c1-2 1-3 3-4z" class="M"></path><path d="M651 488v1l2-1v1c0 2-1 1-2 2 0 0 0 3-1 4l-3 6c-1 1-2 2-1 3l1 1-2 2h0-1c-1 0-1 0-2 1l-1-1h1l1-1h0l-1-2h-1c1-1 1-3 2-4l1-2v-1c2-1 5-5 6-6v-2l1-1z" class="G"></path><defs><linearGradient id="i" x1="230.222" y1="338.496" x2="378.56" y2="308.954" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#i)" d="M211 246c22 3 45 7 67 14 20 7 38 16 57 26l12 6c2 2 4 2 5 4 4 6 7 13 10 19l23 49 6 15c1 2 2 5 3 7l6 16c-1 0-3 1-4 2l1 3h-2c2 4 5 8 7 12v1c1 2 3 5 4 8h-1c-3-6-7-12-12-17-2-4-5-7-8-11-4-4-7-9-12-13v-1l1-1c0-2 2-4 1-6-1-1-1-1-3-1l-1 2v1h0c-1 0-1-1-2-1v1 1c-5-3-7-10-11-14 0-1 0-1-1-2 0-1 0-2-1-3 0-2-3-3-4-4-7-5-16-13-25-15v1c6 1 15 8 20 12 1 1 3 2 4 3v1l-1 4c-8-4-16-9-25-13-3-1-5-2-8-3-1 0-3-1-3-1v-1l-5-11c-2-6-5-12-11-15l-3-2c-1-2-1-3 0-5-4-13-11-23-21-33-3-2-6-5-9-7-8-7-18-12-28-17-5-2-11-4-17-6-3-1-7-2-9-4v-1z"></path><path d="M274 281c2 0 5 3 6 5s4 4 5 7c0 0 1 1 1 2 1 0 1 1 2 2 0 1 2 5 3 6 2 0 2 1 3 2 1 2 0 4 0 6 0 1 1 1 1 2 0-1 0-3 1-4v-1l-1-3v-1l-1-1c-1-2-1-2 0-3l1-1v-1l1 3c1 2 3 7 2 9-1 1-2 3-2 4h-1c-4-13-11-23-21-33z" class="G"></path><defs><linearGradient id="j" x1="391.962" y1="392.728" x2="396.08" y2="402.466" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#6a6969"></stop></linearGradient></defs><path fill="url(#j)" d="M394 386l6 16c-1 0-3 1-4 2l1 3h-2c-3-3-6-8-7-12-1-1-2-3-2-4h0c2 1 2 2 3 3 0 1 0 0 1 1v-1c1-1 2-1 3-2h2v-2h0c-1-2-1-2-1-4z"></path><defs><linearGradient id="k" x1="323.351" y1="334.173" x2="292.548" y2="328.25" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#5d5c5d"></stop></linearGradient></defs><path fill="url(#k)" d="M296 314c2 0 4-1 6 0h1c2 1 6 3 7 6h0c2 4 5 7 6 11l1 2v1h0c1 2 1 3 2 5l1 1v1 1c1 1 1 2 1 3 0 0 1 1 1 2s1 3 3 4v-1l2 1v1c1 1 2 1 3 2 2 0 4 1 6 3h2c1 1 3 2 4 3h1c2 2 4 3 6 3v-3l-3-3c-1 0 0 0-1-1-2-1-4-2-6-4-4-2-8-5-12-6l-1-1h1c6 1 15 8 20 12 1 1 3 2 4 3v1l-1 4c-8-4-16-9-25-13-3-1-5-2-8-3-1 0-3-1-3-1v-1l-5-11c-2-6-5-12-11-15l-3-2c-1-2-1-3 0-5h1z"></path><path d="M675 583v-2l-1-1c0-2-1-5-1-8 1-1 1 0 1-1v-1l1 1c1 1 3 3 5 3 2 2 2 4 3 5 1 4 1 8 1 11-1 5-2 10-6 14v1l-2 3c-2 2-4 4-7 5l5-5v-1l2-1c-2-1-8 1-10 1-10 2-19 6-28 10l-10 5c-1 0-2 1-3 1-4 3-9 6-13 9-13 9-24 20-35 33l-6 8-2 2c0 2 0 3-1 4-2 2-3 4-4 6v2 1c-1 1-2 2-2 3v2l-11 19-2 5c-2 3-3 6-4 9-2 3-3 9-6 12 1 1 0 1 1 2l-1 1v1l-4 7-2 3v-1s0-1-1-2v-1h-1-1c0-2 0-3-1-5l-1 1h-1c-1 1-1 0-3 1 0 0-1 1-2 1 0 1 0 1-2 2v1l-3-12c-1-4-2-8-5-11v-2c-1-1-1-2-2-3v-1-1c-1-1-1-3-1-4l-3-8-1-3v-2s-1-1-1-2 0-2-1-2v-2l-1-2v-1l-1-3v-1l-1-4-1-3v-1-1s-1-1-1-2l-2-7-4-10c0-2-1-5-3-7l-9-31h2l14 39c2 5 3 10 6 15 0-1 0-2-1-4v-1h0c1-2 0-3 0-5l1 1v2h0c1 2 1 3 2 5l1 2 1 4v1c0 1 0 1 1 2h0v-1l1 1 1 1c1 0 3 1 4 2 0 6 1 11 0 17 0 1 0 3-1 5h0-1l2 5 7 22v1h1v-2c1-1 1-2 2-4l2-5 6-15c7-20 16-39 25-59 1-1 2-4 3-5 0-1 0-1 1-1l-3 8 9-17c0 2-1 3-1 5v2h1v-1l1-1 2-2 5-9c4-10 11-19 17-28 0 0 1 0 1-1l1 1c2-1 4-2 5-4h3l14-12h0l-3 3h2v1 1l-1 1h1 0 2l1-1c2-2 4-5 4-8-1-3 1-4 1-6 1-3 3-2 4-5l3-1c1-1 4-1 6-1h0c7 0 13 0 21 2 2 1 3 1 5 2 4 2 9 4 12 7l2 1z" class="V"></path><path d="M624 619l3-2c6-4 12-6 19-8l6-2c2-1 5 0 8-1 2 0 4-1 7-1 3-1 8-1 11-1v1l-2 3c-2 2-4 4-7 5l5-5v-1l2-1c-2-1-8 1-10 1-10 2-19 6-28 10l-10 5c-1 0-2 1-3 1h0c1-1 2-2 4-3h1c-2-3-4 0-6-1z" class="D"></path><path d="M534 727c2-6 3-14 7-19l3-6 1-1 2-4 1-2c1-1 1-2 2-3l2-3c0-2 1-2 2-3 1-4 4-8 7-10 1-2 3-4 4-5l1-1c0-1 1-1 1-2s3-4 5-5h0c3-2 5-5 7-8 6-6 13-12 18-19 2-2 5-4 7-6 2-1 4-3 5-4 3-2 5-4 8-5 1 0 2-1 3-1 0-1 1-1 2-1h2c2 1 4-2 6 1h-1c-2 1-3 2-4 3h0c-4 3-9 6-13 9-13 9-24 20-35 33-3 1-5 3-8 5-4 1-6 6-8 8-2 4-6 8-8 13l-12 23c-2 4-4 10-7 13z" class="N"></path><defs><linearGradient id="l" x1="556.192" y1="709.772" x2="544.731" y2="701.169" xlink:href="#B"><stop offset="0" stop-color="#252525"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#l)" d="M534 727c3-3 5-9 7-13l12-23c2-5 6-9 8-13 2-2 4-7 8-8 3-2 5-4 8-5l-6 8-2 2c0 2 0 3-1 4-2 2-3 4-4 6v2 1c-1 1-2 2-2 3v2l-11 19-2 5c-2 3-3 6-4 9-2 3-3 9-6 12 1 1 0 1 1 2l-1 1v1l-4 7-2 3v-1s0-1-1-2v-1h-1-1c0-2 0-3-1-5l5-16z"></path><defs><linearGradient id="m" x1="530.425" y1="740.239" x2="545.83" y2="733.144" xlink:href="#B"><stop offset="0" stop-color="#aeacac"></stop><stop offset="1" stop-color="#cdcdcf"></stop></linearGradient></defs><path fill="url(#m)" d="M543 722c0 1 1 2 2 4-2 3-3 9-6 12 1 1 0 1 1 2l-1 1v1l-4 7-2 3v-1s0-1-1-2v-1c1-4 4-9 5-13l6-13z"></path><path d="M543 722c5-13 11-25 19-37 2-3 5-6 7-10 0 2 0 3-1 4-2 2-3 4-4 6v2 1c-1 1-2 2-2 3v2l-11 19-2 5c-2 3-3 6-4 9-1-2-2-3-2-4z" class="L"></path><defs><linearGradient id="n" x1="659.8" y1="574.907" x2="647.462" y2="601.426" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#n)" d="M635 571c7 0 13 0 21 2 2 1 3 1 5 2 4 2 9 4 12 7l2 1c1 1 1 2 2 3 1 2 2 3 3 6l1 1v1h0-1c-4-2-10 1-15 0-3 1-7 1-10 1l-3 1h-4l-3 1h-1l-2 1-4 1c-1 0-2 1-2 1h-1-1c-1 1-3 1-4 2 0 0-1 0-2 1h-1v-3l-6 3c-2-1-3-1-4-2h-3l-1-1c2-1 2-1 4-1 0-1 0-1 1-2h0l-1-1c-1-1 0-3 0-4 2-2 4-5 4-8-1-3 1-4 1-6 1-3 3-2 4-5l3-1c1-1 4-1 6-1h0z"></path><path d="M659 588c0-1 0-1-2-2h-1l-1-1h-3c-1 0-1 0-2-1h-3c1-1 1-1 2-1 7 0 11 3 15 8 1 0 2 1 2 1 1 1 6 0 7 0 2 0 5 1 7 0l1 1v1h0-1c-4-2-10 1-15 0-1-1-2-1-4-1h-1c-1-1 0-3-1-5z" class="P"></path><path d="M648 596l-1-1c-1 0-1 0-1-1h0l2-1c1-1 1-1 3-1 1-1 3 0 5 0l-1-1h-6l-1-1h5l1-1h1c-1 0-1-1-2-2h-3v-1c2 0 3 0 4 1 2 0 4 0 5 1 1 2 0 4 1 5h1c2 0 3 0 4 1-3 1-7 1-10 1l-3 1h-4z" class="N"></path><path d="M629 572c1-1 4-1 6-1l-4 14c-1 3-1 5-3 8h0c-1 0-1 0-2 1h1v1c0 1 1 2 2 3l-2 2-6 3c-2-1-3-1-4-2h-3l-1-1c2-1 2-1 4-1 0-1 0-1 1-2h0l-1-1c-1-1 0-3 0-4 2-2 4-5 4-8-1-3 1-4 1-6 1-3 3-2 4-5l3-1z" class="D"></path><path d="M627 594v1c0 1 1 2 2 3l-2 2-6 3c-2-1-3-1-4-2l1-2h2l1-2c1 0 4 0 4-1 1 0 1-1 2-2z" class="P"></path><path d="M626 573l3-1v2c0 1-2 3-3 5-2 4-3 10-6 14l-1 2s-1 1-1 2l-1-1c-1-1 0-3 0-4 2-2 4-5 4-8-1-3 1-4 1-6 1-3 3-2 4-5z" class="V"></path><defs><linearGradient id="o" x1="578.586" y1="704.689" x2="513.817" y2="613.934" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#o)" d="M598 599h3l14-12h0l-3 3h2v1 1l-1 1h1 0 2l1-1c0 1-1 3 0 4l1 1h0c-1 1-1 1-1 2-2 0-2 0-4 1l1 1h3c1 1 2 1 4 2l6-3v3c-1 1-1 1-2 1l-1 1-3 1c-1 0-1 0-1 1-2 2-4 4-6 5-2 3-6 3-8 7l1-1h2l2-1 2-1c3-1 6-3 10-4l3-1 6-3h2l1-1 1 1h0c-1 1-3 1-4 2l-9 4-6 2c-1 1-2 1-3 2h-2c-4 2-7 4-11 7l-3 2-1 1-2 1-3 3c-1 1-2 2-4 3-1 1-2 3-4 3l-17 19-1 1c0 1-1 1-2 2s-1 2-2 3l-2 2-1 2c-1 1-1 2-2 2l-1 2c0 1-1 2-2 3l-3 5-1 3h0l-3 3c-1 2-4 7-4 9l-3 6-1 1c0 3-2 6-4 8-1 2-2 6-3 9l-8 22c-1 1-1 2-2 3h0v3c0 1 0 1-2 2v1l-3-12c-1-4-2-8-5-11v-2c-1-1-1-2-2-3v-1-1c-1-1-1-3-1-4l-3-8-1-3v-2s-1-1-1-2 0-2-1-2v-2l-1-2v-1l-1-3v-1l-1-4-1-3v-1-1s-1-1-1-2l-2-7-4-10c0-2-1-5-3-7l-9-31h2l14 39c2 5 3 10 6 15 0-1 0-2-1-4v-1h0c1-2 0-3 0-5l1 1v2h0c1 2 1 3 2 5l1 2 1 4v1c0 1 0 1 1 2h0v-1l1 1 1 1c1 0 3 1 4 2 0 6 1 11 0 17 0 1 0 3-1 5h0-1l2 5 7 22v1h1v-2c1-1 1-2 2-4l2-5 6-15c7-20 16-39 25-59 1-1 2-4 3-5 0-1 0-1 1-1l-3 8 9-17c0 2-1 3-1 5v2h1v-1l1-1 2-2 5-9c4-10 11-19 17-28 0 0 1 0 1-1l1 1c2-1 4-2 5-4z"></path><path d="M574 631h1c2-1 2-2 3-4 1-1 1-1 3-1l-2 3c-2 4-6 5-7 10l-3 1 5-9z" class="R"></path><defs><linearGradient id="p" x1="516.971" y1="690.326" x2="496.029" y2="685.174" xlink:href="#B"><stop offset="0" stop-color="#b4abb0"></stop><stop offset="1" stop-color="#cbd1cd"></stop></linearGradient></defs><path fill="url(#p)" d="M502 677c0-1 0-2-1-4v-1h0c1-2 0-3 0-5l1 1v2h0c1 2 1 3 2 5l1 2 1 4v1c0 1 0 1 1 2h0v-1l1 1 1 1c1 0 3 1 4 2 0 6 1 11 0 17 0 1 0 3-1 5h0-1l-9-32z"></path><path d="M598 599h3l14-12h0l-3 3h2v1 1l-1 1h1 0 2l1-1c0 1-1 3 0 4l1 1h0c-1 1-1 1-1 2-2 0-2 0-4 1l1 1h3c1 1 2 1 4 2l6-3v3c-1 1-1 1-2 1l-1 1-3 1c-1 0-1 0-1 1-2 2-4 4-6 5-3 1-6 3-10 5l-2 2h-3l-3 3c-1 1-2 1-4 2h0c-1 1-2 1-3 1-2 1-3 3-5 4s-3 3-4 5l-3 3c-1 2-2 3-3 4h-1c0-1 0-1 1-2l6-9-1-1 2-3c-2 0-2 0-3 1-1 2-1 3-3 4h-1c4-10 11-19 17-28 0 0 1 0 1-1l1 1c2-1 4-2 5-4z" class="J"></path><path d="M586 619v2c-1 1-2 2-2 4s-2 4-4 5l-1-1 2-3c2-1 4-5 5-7z" class="N"></path><path d="M600 609h0c0 2-1 2-1 3v1l3-1-6 6-1-1c-2 0-2 1-4 2l1-1 1-1c2-3 4-6 7-8z" class="D"></path><path d="M617 592c0 1-1 3 0 4l1 1h0c-1 1-1 1-1 2-2 0-2 0-4 1l1 1h3c1 1 2 1 4 2l-7 2v-1c-1 0-2 1-3 1-2 0-4 2-6 3-1 1-2 2-3 4h0l-3 1v-1c0-1 1-1 1-3h0a30.44 30.44 0 0 1 8-8c2-2 6-5 8-8l1-1z" class="I"></path><defs><linearGradient id="q" x1="578.871" y1="628.714" x2="592.633" y2="601.824" xlink:href="#B"><stop offset="0" stop-color="#3a393b"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#q)" d="M598 599h3l14-12h0l-3 3c-4 5-9 8-13 13-1 1-1 2-2 2-4 4-9 9-11 14h0c-1 2-3 6-5 7-2 0-2 0-3 1-1 2-1 3-3 4h-1c4-10 11-19 17-28 0 0 1 0 1-1l1 1c2-1 4-2 5-4z"></path><defs><linearGradient id="r" x1="586.875" y1="466.128" x2="619.784" y2="478.151" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#r)" d="M657 268c8 5 16 13 22 21 2 3 4 6 6 10l1 1c-1 4-4 8-6 12 0 5-3 9-4 14 0-2 2-3 3-4 0-2 1-2 2-3 0-1 1-2 2-3 0 0 1 0 1-1 1-3 4-6 6-9 2-2 6-8 9-9 2 0 3-2 4-4 2-2 4-4 6-5h1c2 0 4-1 6-2l-4 2c-4 3-7 8-12 11-3 1-7 6-8 9h3l-6 7-2 2 1 1c-1 2-2 3-3 5l1-1c0 1 0 1 1 2-7 7-13 15-18 24h1c0 1-1 2-1 3-2 2-4 5-6 8 0 1-2 3-2 4h-2l-1 1-26 71c-3 7-5 14-7 21l-16 50-10 25c0 1-1 3-1 5h1v1l-1 3h0v1 2l-3 6c-1 2-1 4-2 7l1 1c-1 2-1 4-2 6-1 1-1 2-1 4l-4 10-3 8-5 13c0 1 0 3-1 4 0 4-1 7-2 10 0 1 0 1-1 2v2 1l-9 20-9 17 3-8c-1 0-1 0-1 1-1 1-2 4-3 5-9 20-18 39-25 59l-1-1c1-4 2-7 4-10 0-3 1-6 2-8 0-3-1-8 0-11 0-1 0-2 1-3 0-1 0-3 1-4v-2l1-3 3-8v-1-1c0-2 0-4 1-5v-2l1-3v-2l2-7 9-27 6-24 7-21c1-2 2-5 3-8l30-85s1-2 1-3v1h-1c0-1 1-2 1-3v-3-1-1l1-3 38-105h0 0c0-1 0-2 1-3h-1c0-3 2-6 2-9l1-4v-1c1-1 1 0 1-1 0-2 0-3-1-5v-3c1-2 1-7 0-10-1-1-1-2-1-3h1l3 3 1-1-2-2c-1 0-1 0-1-1 1-2 2-3 5-4 1-2 2-4 2-7 1 0 1 0 1-1v-4s1 0 1-1v-1l1-1v-3c-1-1-1-1 0-2 0-1-1 0-1-1-1-1-1-4-1-5 0 0 1 1 1 2 0-1 0 0 1-1 1 2 1 2 2 3 3-2 2-4 4-6v-1c1-1 1-1 1-2v-1l1-2h0v-4c-1-2-3-3-5-4-1-1-1-2-1-3z"></path><path d="M685 299l1 1c-1 4-4 8-6 12s-3 9-5 14c-2 2-3 5-5 7l-1 2v-1c2-3 2-7 4-10 3-7 8-14 10-21h1c0-2 1-3 1-4z" class="H"></path><path d="M613 445l-1-1c1-1 1-1 1-2l2-4v-1c0-1 0-1 1-2 0-1 1-3 1-4v-1l1-1v-1c1-2 0 1 1-1v-1c0-1 1-3 1-4 1-1 1-1 1-2v-1c1-1 1-3 2-4l2-5v-1-1c1-1 1-2 2-3h0v-1l1 2v2 1c-1 1-1 2-1 3l-1 2h0l-1 2-6 15-6 14z" class="R"></path><path d="M628 409c1 2-5 16-6 20l-1 3-1 2c-1 4-3 6-5 9-1 3-2 7-3 10-1 1-1 3-2 5l-3 6h0v-1c0-1 0-1 1-2v-2c1-1 1-2 1-2 0-2 1-3 1-4 2-1 2-5 3-8h0l6-14 6-15 1-2h0l1-2c0-1 0-2 1-3z" class="J"></path><path d="M644 334v-3c1-2 1-7 0-10-1-1-1-2-1-3h1l3 3c1 10 0 19-3 29-1 2-1 5-3 7h0 0 0c0-1 0-2 1-3h-1c0-3 2-6 2-9l1-4v-1c1-1 1 0 1-1 0-2 0-3-1-5z" class="M"></path><path d="M598 536h1v1l-1 3h0v1 2l-3 6c-1 2-1 4-2 7l1 1c-1 2-1 4-2 6-1 1-1 2-1 4l-4 10-3 8-5 13c0 1 0 3-1 4 0 4-1 7-2 10 0 1 0 1-1 2v2 1l-9 20-9 17 3-8c1-1 5-14 6-17s2-7 3-10l5-15 7-22 17-46z" class="L"></path><path d="M584 585v-4c0-1 1-1 1-2 0-3 1-5 2-7 0-4 2-7 3-11l5-14c1-3 1-5 3-7h0v1 2l-3 6c-1 2-1 4-2 7l1 1c-1 2-1 4-2 6-1 1-1 2-1 4l-4 10-3 8z" class="Q"></path><path d="M566 629h1v-1c2-3 3-5 4-8 2-6 4-12 7-18 0 4-1 7-2 10 0 1 0 1-1 2v2 1l-9 20-9 17 3-8c1-1 5-14 6-17z" class="E"></path><path d="M561 589l7-21c1-2 2-5 3-8 0 4-3 7-2 10h0l-4 16v2 1c-1 2 0-1-1 1 0 2-1 5-2 7-2 9-3 19-8 26-1 3-1 6-3 9-1 1-1 2-1 3 0-1 1-1 1-2h1l-5 15c0 1-1 3-1 5l-1 1v1c1 1 0 3 0 4-1 2-1 4-2 5l-1 1v3 1c1-1 1-3 1-4h1s0-1 1-2c0-1 0-1 1-2h0c0 2 0 4-1 6-2 5-4 11-6 16l-3 9c0-3-1-8 0-11 0-1 0-2 1-3 0-1 0-3 1-4v-2l1-3 3-8v-1-1c0-2 0-4 1-5v-2l1-3v-2l2-7 9-27 6-24z" class="N"></path><path d="M709 288h1c2 0 4-1 6-2l-4 2c-4 3-7 8-12 11-3 1-7 6-8 9h3l-6 7-2 2 1 1c-1 2-2 3-3 5l1-1c0 1 0 1 1 2-7 7-13 15-18 24h1c0 1-1 2-1 3-2 2-4 5-6 8 0 1-2 3-2 4h-2l-1 1c0-2 1-3 2-4 0-1-1-2 0-3 0-1 1-1 1-2v-1c1-1 1-2 1-3-3 2-4 7-6 11l-7 16c-5 7-8 16-11 24-2 4-3 8-4 12s-2 7-4 10h-1l3-9 1-1h0v-2c1-1 1-1 1-2v-1l1-2h0v-1l1-1v-2l1-1h0c0-1 0-2 1-2v-2l2-2v-1c0-1 0-2 1-2v-2l1-1c1-2 1-4 2-6l1-2c1-1 1-2 2-3 0-1 1-3 1-4l12-24c1-1 1-3 2-4 2-4 3-9 7-13v1l1-2c2-2 3-5 5-7 2-5 3-10 5-14 0 5-3 9-4 14 0-2 2-3 3-4 0-2 1-2 2-3 0-1 1-2 2-3 0 0 1 0 1-1 1-3 4-6 6-9 2-2 6-8 9-9 2 0 3-2 4-4 2-2 4-4 6-5z" class="F"></path><path d="M692 308h3l-6 7-2 2 1 1c-1 2-2 3-3 5l-3 2h0c1-2 1-3 1-5v-1c1 0 2-2 3-3 1-3 3-5 6-8z" class="K"></path><path d="M674 333c3-5 6-10 9-14v1c0 2 0 3-1 5h0l3-2 1-1c0 1 0 1 1 2-7 7-13 15-18 24h1c0 1-1 2-1 3-2 2-4 5-6 8 0 1-2 3-2 4h-2c1-2 2-5 3-7 2-6 5-11 8-16 1-3 2-5 4-7z" class="E"></path><path d="M674 333c3-5 6-10 9-14v1c0 2 0 3-1 5h0l3-2 1-1c0 1 0 1 1 2-7 7-13 15-18 24 0 0-1 1-1 2-1-2 6-13 7-15 2-2 4-5 4-7-1 1-3 4-5 5z" class="C"></path><defs><linearGradient id="s" x1="401.185" y1="595.424" x2="325.419" y2="221.136" xlink:href="#B"><stop offset="0" stop-color="#b4b3b3"></stop><stop offset="1" stop-color="#dedddd"></stop></linearGradient></defs><path fill="url(#s)" d="M211 246l-12-2v-1h6l11 1 24 1h35 137 31 22c-8 2-15 4-22 8-13 6-26 14-30 28-4 12-2 23 1 35h-1c1 1 1 2 1 3h-1-2-1c-1 0 0 0-1 1s-1 2-2 4h0c-2 13 5 27 9 40l31 80-1 1-2-2c1 3 7 19 8 20l3 3c3 6 5 12 7 18l12 32c1 2 2 8 3 9l5 13c-1 2-1 2 0 3v4l9 25 5 16c1 1 2 3 2 4 3 6 4 13 6 19 0 1 1 2 1 2 3 5 4 12 6 18l-2-1c0 5 2 8 3 12 1 3 1 7 1 10 1 1 0 0 0 1 0 3 1 5 2 8l-1-1-1-2v-2l-1-1v-3c-2-1-2-2-3-3l-1-1v2 2l-2-2v-1c1-2 0-4-2-5 0-1 0-2-1-3v-2-1l-2-2v-2c-1 0-1-1-2-2h0l-1-1h-1l1 1c0 1-1 2-1 3 1 0 1 0 2 1 0 0-1 1-1 2h0l-1 1-1-1-2 1c1 1 1 2 1 2v1l-1-2v2l-1 1c0-1-1-3-1-4h-1c0 2 1 3 2 5l4 10 3 11c1 2 1 3 2 4s1 2 1 3h-1 0v-2l-1-1c0 2 1 3 0 5h0v1c1 2 1 3 1 4-3-5-4-10-6-15l-14-39-14-38-2 1c-1-3-3-7-4-10l-21-56-6-15c1-2 1-3 1-5l-31-72h1c-1-3-3-6-4-8v-1c-2-4-5-8-7-12h2l-1-3c1-1 3-2 4-2l-6-16c-1-2-2-5-3-7l-6-15-23-49c-3-6-6-13-10-19-1-2-3-2-5-4l-12-6c-19-10-37-19-57-26-22-7-45-11-67-14z"></path><path d="M432 428c0 1 1 1 1 2s0 1 1 2v1c0 1 1 3 2 4v2c1 1 1 2 1 2v1c1 1 1 2 1 3 1 0 1 1 0 2v2l-1-1v1h-1v-4-1c-1-1-1-2-2-3 0 0 0-1-1-1l1-1c0-2 0-3-1-5s-1-3-1-5v-1z" class="B"></path><path d="M448 524c1-1 2-1 3-1l1 1h2v15l-1-1c0-1 0-2-1-2v-1-1-1l-3-6c0-1-1-2-1-3z" class="L"></path><path d="M444 482l-1 1v-1h0v-2-2c0-1-1-2-1-2 0-2-1-3-2-5l1-1h2v1c-1-2 0-4-1-6s-1-4-1-5c-1-1-1-1-1-2l-1-1v1c-1-1-1-3-1-4h1c1 1 1 2 2 2v1c0 3 1 6 2 8 0 1 0 4 1 4h1c0 1 1 1 0 2 0 0-1 1 0 2 0 0 0 1 1 1-1 1-2 4-2 4v2c0 1 0 1 1 1l-1 1z" class="B"></path><defs><linearGradient id="t" x1="394.153" y1="410.744" x2="409.39" y2="414.629" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#t)" d="M395 407h2l-1-3c1-1 3-2 4-2l10 27c-3-3-4-7-8-10-2-4-5-8-7-12z"></path><path d="M417 271c2-2 3-4 5-6 3-4 9-8 13-10 2-1 5-3 7-4l1 2c-13 6-26 14-30 28-4 12-2 23 1 35h-1c0-1 0-2-1-3 0-2-1-4-1-6h0l-1 1c-1 0-1 0-2 1 1 1 1 2 2 3l1 1h-1-1c-1-2-2-3-3-5 0-3-3-1-5-2h-1c0 1 0 1 1 2v2 4c0 1 1 3 1 5-2-3-2-7-4-9 0-1-1-1-1-2v-1h-2 0l-1-1h-1l-6-6c-5-5-10-9-15-13-1-3-2-5-2-8s1-3 3-5l2-2 3-2c-1 4-1 7-1 12 1 2 2 3 4 4 0 1 0 2 1 2 2 1 4 4 6 5 3 2 7 4 10 7 1-2 3-4 4-6l1-1-1-1 1-1c0-1 1-2 1-2 1-3 2-6 4-8 3-3 5-8 9-10z" class="Q"></path><path d="M375 272c-1 3-1 8 1 11 0 2 1 3 2 3l5 5c3 3 7 6 9 7 2 2 4 3 6 5h1v-1h1l1 1h1c-1-2 0-2 0-4 1-1 2-1 4-1 0 1 0 1 1 2v1c0 1-1 2-2 3h-1 0-1 0c-1 0 0 0-1 1l-2-2v1c-1 0-1 0-1 1v2c-2-2-2-3-5-4h0s-1-2-2-2c-2-1-3-3-5-4-2-2-3-4-6-5 0-1-1-1-1-2-2-1-6-5-7-7v-2c-1-2 0-5 0-7l2-2z" class="E"></path><path d="M375 272l3-2c-1 4-1 7-1 12 1 2 2 3 4 4 0 1 0 2 1 2 2 1 4 4 6 5 3 2 7 4 10 7 1-2 3-4 4-6l1-1-1-1 1-1c0-1 1-2 1-2 1-3 2-6 4-8 3-3 5-8 9-10l-4 6h0c-2 3-4 6-5 9-1 2-2 3-2 5l1 2c-1 1-1 1-2 1 1 1 2 2 1 4h0c-2 0-3 0-4 1 0 2-1 2 0 4h-1l-1-1h-1v1h-1c-2-2-4-3-6-5-2-1-6-4-9-7l-5-5c-1 0-2-1-2-3-2-3-2-8-1-11z" class="B"></path><path d="M451 500l1-1 1 1-1 2c2 1 3 0 3 2v1c1 1 1 1 1 2v1c1 2 2 3 3 5 1 3 2 5 4 8 1 2 2 4 1 6v1 3h0c1 1 1 1 1 2h0c1 2 2 3 2 4s1 2 1 3c0 2 0 1 1 3v1c0 1 1 1 1 2-1 0-1 1-1 2l1 1c0 1 1 2 1 3 1 2 1 4 1 5s1 2 1 3v1c1 0 2 1 2 2h0v5l2 1c-1 3 1 5 2 8v1 1c1 1 1 1 1 3v1h0c1 0 2 0 3 1-1 1-2 1-2 2l1 1 1-1c1 0 1 1 2 1l-1 3 2 2c0 3-2 2 1 6 0 1 0 2 1 3 0 1 0 1 1 2h0v3c1 1 1 2 1 3s1 2 1 3h0c1 1 2 2 1 3v5l2 2c1-1 1-1 2 0v3h0c2 1 2 2 2 4h-1l1 1c0 1-1 2-1 3 1 0 1 0 2 1 0 0-1 1-1 2h0l-1 1-1-1-2 1c1 1 1 2 1 2v1l-1-2v2l-1 1c0-1-1-3-1-4h-1l-7-20v-3c-1-2-1-2-1-3v-2l-1-1v-4l-1-1v-2l-1-1v-1-1l-1-1c0-1 0-3-1-4v-3c-1-1-1-1-1-2h0c-1-2-1-2-1-3-1-1-1-2-1-3v-1l-1-1h0l2 1v-3l-2-2v-4h1c0-3-2-5-3-7h0v-1 4c0-1 0-2-1-3s0-2-1-4c1 0 1 0 2-1-1-1-1-2-2-3v-2l-1 1v-1c-1-1-2-3-2-5h1v-1h-1v-1c0-1-1-2-1-3s0-2 1-3c-1-1-1-2-1-2-2 0-1 0-2 2l-1-2 2-2v-2c-1-1-1 0-1-1l-1-1v1c-1 1-1 1-1 2v1l-1-4-1-1v-2-1-1h0 1v-1-3-1c-1-1-1-2-2-3s-2-3-2-5c-1-2-2-3-2-4v-3h-2l-1-4h-1 0c-1-1-1-1 0-2h0v-2h1l-1-1z" class="Q"></path><path d="M482 587l1-1c1 0 1 1 2 1l-1 3 2 2c0 3-2 2 1 6 0 1 0 2 1 3 0 1 0 1 1 2h0v3c1 1 1 2 1 3s1 2 1 3h0c1 1 2 2 1 3v5l2 2c1-1 1-1 2 0v3h0c2 1 2 2 2 4h-1l1 1c0 1-1 2-1 3 1 0 1 0 2 1 0 0-1 1-1 2h0l-1 1-1-1-2 1v-1l1-2-1-1c-1-1-1-2-2-3l1-1-1-1h-1l-1-2c0-1-1-2-1-4 0-1 0-1-1-2v-4c-1-3-2-6-2-9-1-1-1-2-1-3-1-3 0-5-1-7-1-3-2-6-1-8l-1-2z" class="B"></path><path d="M488 616c1 1 2 2 1 3v1l2 2h1l-1 2 1 3 1 2 1-1 1 1-2 2 2 2h1c1 1 1 2 2 3h0l-1 1-1-1-2 1v-1l1-2-1-1c-1-1-1-2-2-3l1-1-1-1h-1l-1-2c0-1-1-2-1-4 0-1 0-1-1-2v-4z" class="E"></path><path d="M402 419c4 3 5 7 8 10l11 28 7 18c0 2 1 6 3 8 2 1 3 7 5 10l4 12v1l1 2c1 3 3 7 4 10 0 1 0 2 1 2l2 6 1 4c0 1 0 2 1 3 0 1 0 1 1 2v2c1 2 1 3 2 4 0 1 0 2 1 3 0 3 2 6 3 9 0 1 1 2 1 4l1 1v1l1 2c1 2 1 4 2 6s2 5 3 7v2h0c1 2 2 3 2 5l1 1c0-1 0-2 1-3l1 1c-1-2 0-1-1-2s-1-2-1-3v-1-1c-1-2-2-3-2-4-1-2-1-2-1-3-2-2-2-5-3-8 0-1-1-2-1-3l-1-1 1-1c1 1 1 3 2 5l3 6v1c2 3 3 8 4 11l3 4h1l1 1v1c0 1 0 2 1 3 0 1 0 1 1 3h0c0 1 0 1 1 2v3c1 1 1 3 1 4l1 1v1 1l1 1v2l1 1v4l1 1v2c0 1 0 1 1 3v3l7 20c0 2 1 3 2 5l4 10 3 11c1 2 1 3 2 4s1 2 1 3h-1 0v-2l-1-1c0 2 1 3 0 5h0v1c1 2 1 3 1 4-3-5-4-10-6-15l-14-39-14-38-2 1c-1-3-3-7-4-10l-21-56-6-15c1-2 1-3 1-5l-31-72h1c-1-3-3-6-4-8v-1z" class="K"></path><path d="M470 576l3 4h1l1 1v1c0 1 0 2 1 3 0 1 0 1 1 3h0c0 1 0 1 1 2v3c1 1 1 3 1 4l1 1v1 1l1 1v2l1 1v4l1 1v2c0 1 0 1 1 3v3c-1-1-1-2-2-3v-1-1c-1-1-1-1-1-2-2-4-4-9-5-13-1-2-1-5-2-6-1-3-3-4-3-8h1l-2-7z" class="L"></path><path d="M402 419c4 3 5 7 8 10l11 28 7 18c0 2 1 6 3 8l21 59 5 15c1 1 2 3 2 4 2 4 3 9 5 13v1l4 10-2 1c-1-3-3-7-4-10l-21-56-6-15c1-2 1-3 1-5l-31-72h1c-1-3-3-6-4-8v-1z" class="B"></path><path d="M436 500l2 6c0 1 1 2 1 3 1 1 1 3 2 4v1l1 4c1 2 1 3 2 4 0 3 2 7 3 10 1 1 1 3 2 5l2 6 7 17c0 1 0 1 1 1h0c2 4 3 9 5 13v1l4 10-2 1c-1-3-3-7-4-10l-21-56-6-15c1-2 1-3 1-5z" class="C"></path><path d="M392 262c19-11 41-14 63-16-4 2-9 4-13 5-2 1-5 3-7 4-4 2-10 6-13 10-2 2-3 4-5 6-4 2-6 7-9 10-2 2-3 5-4 8 0 0-1 1-1 2l-1 1 1 1-1 1c-1 2-3 4-4 6-3-3-7-5-10-7-2-1-4-4-6-5-1 0-1-1-1-2-2-1-3-2-4-4 0-5 0-8 1-12l3-2 7-4 4-2z" class="T"></path><path d="M382 282h1c1 0 2 0 2 1 2 2 4 6 6 7h2 1v-1h1 1c2 2 2 3 2 5h1v-1l1-1c-1-1-1 0-1-1 1-1 1-2 2-3 1-5 3-7 5-11 2-3 5-5 7-8 3-5 8-8 12-12l2-2 3-1 1-1 3-1c1 0 2-1 3-1l-1 1c-1 1-3 2-5 3l-11 8c-2 2-4 4-5 7-5 5-11 11-13 18-1 1-1 2-1 4v1h-1c0 1-1 2-2 2-2-2-4-2-7-4l-1-1c-1 0-2 0-3-2h0l-1-1c-1-1-1-1-1-3l-3-2z" class="J"></path><path d="M392 262c19-11 41-14 63-16-4 2-9 4-13 5-2 1-5 3-7 4-4 2-10 6-13 10-2 2-3 4-5 6-4 2-6 7-9 10-2 2-3 5-4 8 0 0-1 1-1 2l-1 1 1 1-1 1c-1 2-3 4-4 6-3-3-7-5-10-7-2-1-4-4-6-5-1 0-1-1-1-2-2-1-3-2-4-4 0-5 0-8 1-12l3-2 7-4c-1 2-2 3-4 5l-2 1c0 2 0 3 1 4l-3 5 1 1h0v1l1 1 3 2c0 2 0 2 1 3l1 1h0c1 2 2 2 3 2l1 1c3 2 5 2 7 4 1 0 2-1 2-2h1v-1c0-2 0-3 1-4 2-7 8-13 13-18 1-3 3-5 5-7 4-3 7-5 11-8 2-1 4-2 5-3l1-1c1 0 1-1 2-1-2-1-3 1-5 2h-6l-14 3c-7 2-14 6-22 7z" class="H"></path><path d="M381 268l7-4c-1 2-2 3-4 5l-2 1c0 2 0 3 1 4l-3 5 1 1h0v1l1 1 3 2c0 2 0 2 1 3l1 1h0c1 2 2 2 3 2l1 1c3 2 5 2 7 4 1 0 2-1 2-2v3c-1 0-1 1-2 1-1-1-2-1-3-2-1 0-1-1-2-2h0c-1 0-2 0-2-1-2 0-3-1-4-2s-2-3-4-3h-1l-1-2c0-1-1-1-2-2v-1c-1-3 0-3 0-5h0c1-2 1-3 1-5 0-1 1-3 1-4z" class="F"></path><defs><linearGradient id="u" x1="496.758" y1="579.16" x2="480.435" y2="581.964" xlink:href="#B"><stop offset="0" stop-color="#525152"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#u)" d="M465 533c2 1 2 1 3 2v1h1l3-1v3h0l2 2c1 0 0 0 1-1h0c-1 0-1 0-2-1v-1-2c1 0 3 0 4 2v1c0 2 2 4 3 5l1-1h0l1 3 9 25 5 16c1 1 2 3 2 4 3 6 4 13 6 19 0 1 1 2 1 2 3 5 4 12 6 18l-2-1c0 5 2 8 3 12 1 3 1 7 1 10 1 1 0 0 0 1 0 3 1 5 2 8l-1-1-1-2v-2l-1-1v-3c-2-1-2-2-3-3l-1-1v2 2l-2-2v-1c1-2 0-4-2-5 0-1 0-2-1-3v-2-1l-2-2v-2c-1 0-1-1-2-2h0l-1-1c0-2 0-3-2-4h0v-3c-1-1-1-1-2 0l-2-2v-5c1-1 0-2-1-3h0c0-1-1-2-1-3s0-2-1-3v-3h0c-1-1-1-1-1-2-1-1-1-2-1-3-3-4-1-3-1-6l-2-2 1-3c-1 0-1-1-2-1l-1 1-1-1c0-1 1-1 2-2-1-1-2-1-3-1h0v-1c0-2 0-2-1-3v-1-1c-1-3-3-5-2-8l-2-1v-5h0c0-1-1-2-2-2v-1c0-1-1-2-1-3s0-3-1-5c0-1-1-2-1-3l-1-1c0-1 0-2 1-2 0-1-1-1-1-2v-1c-1-2-1-1-1-3 0-1-1-2-1-3s-1-2-2-4z"></path><path d="M476 561c0-1 1-1 1-2 0-3-1-4-1-7v1c1 1 1 2 2 3l2 1h0c-1 1-2 3-3 3l-1 1z" class="B"></path><path d="M489 590l1-3v1l1 1h1l1 2-3 4-1-2v-3zm7 32v-1-1-2-1c-1-1-1-2-1-3l1-1v-2h0c0 1 0 2 1 3s1 3 2 4 2 2 1 4v1h0v3 1c1 1 2 1 3 3h-1l1 1v2c0 1 0 1 1 2l-1 1-2-2v-2c-1 0-1-1-2-2h0l-1-1c0-2 0-3-2-4h0v-3z" class="H"></path><path d="M485 587h1c-1-1-2-1-2-3 1 0 2-1 2-2v1c0 1 0 0 1 1s1 1 2 1v1 4 3l1 2c1 1 1 2 2 3v2h0v1c1 1 2 2 2 3v1c0 1 0 1-1 2h0v3 1l-2 1h0c0-1-1-2-1-3s0-2-1-3v-3h0c-1-1-1-1-1-2-1-1-1-2-1-3-3-4-1-3-1-6l-2-2 1-3z" class="O"></path><path d="M465 533c2 1 2 1 3 2v1h1l3-1v3h0l2 2v5s1 1 1 2-1 2 1 2v1 2c0 3 1 4 1 7 0 1-1 1-1 2l2-1 1 1v1c-1 0 0 0-1 1 1 0 1 1 3 1v1l-1 1 1 1-1 2h1c0 1 1 2 1 3v3l-2-1-1 1 1 1v1h-1c-1-3-3-5-2-8l-2-1v-5h0c0-1-1-2-2-2v-1c0-1-1-2-1-3s0-3-1-5c0-1-1-2-1-3l-1-1c0-1 0-2 1-2 0-1-1-1-1-2v-1c-1-2-1-1-1-3 0-1-1-2-1-3s-1-2-2-4z" class="E"></path><defs><linearGradient id="v" x1="509.839" y1="632.014" x2="503.892" y2="634.279" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#v)" d="M498 590c3 6 4 13 6 19 0 1 1 2 1 2 3 5 4 12 6 18l-2-1c0 5 2 8 3 12 1 3 1 7 1 10 1 1 0 0 0 1 0 3 1 5 2 8l-1-1-1-2v-2l-1-1v-3c-2-1-2-2-3-3l-1-1v2 2l-2-2v-1c1-2 0-4-2-5 0-1 0-2-1-3v-2-1l1-1c-1-1-1-1-1-2v-2l-1-1h1c-1-2-2-2-3-3h3c0-2 0-3 1-5 0-2 1-5 0-8-1-1-2-3-3-4v-4c-1-1-1 0-1-2v-2c-1-3-1-5-2-8v-4z"></path><defs><linearGradient id="w" x1="461.566" y1="472.649" x2="443.551" y2="476.7" xlink:href="#B"><stop offset="0" stop-color="#767475"></stop><stop offset="1" stop-color="#a09f9f"></stop></linearGradient></defs><path fill="url(#w)" d="M432 428l-1-1c0-2-1-3-2-5v-2h1l1 4c1 2 1 3 2 5v-1-5c-1-1 0-2 0-3-1-2-1-3-2-5v-3l-2-2v-2-1l-2-3c0-1-1-3-2-5 0-2-1-3-2-5l-1-4v-1c0-1 0-1-1-2l-1-3s0-1-1-2h0c-1-3 0 0-1-2v-2c-1-1-1-2-2-3 0-1-1-2-1-3v-1c-1-2-1-2-1-4-1-1-2-2-2-4l-2-5c-1-2-2-5-2-8-1-1-1-1-1-2-1-1-1-2-1-3l-1-1c0-4-2-4-3-6v-3c-1-1 0-4 0-5 1-2 0-2 1-3h1l1-3c0-1 0-2 1-3-1-1-1-1-1-3v-2-1c1-1 1-2 1-3 1 1 2 6 3 8-1 1-1 2-2 4h0c-2 13 5 27 9 40l31 80-1 1-2-2c1 3 7 19 8 20l3 3c3 6 5 12 7 18l12 32c1 2 2 8 3 9l5 13c-1 2-1 2 0 3v4l-1-3h0l-1 1c-1-1-3-3-3-5v-1c-1-2-3-2-4-2v2 1c1 1 1 1 2 1h0c-1 1 0 1-1 1l-2-2h0v-3l-3 1h-1v-1c-1-1-1-1-3-2h0c0-1 0-1-1-2h0v-3-1c1-2 0-4-1-6-2-3-3-5-4-8-1-2-2-3-3-5v-1c0-1 0-1-1-2v-1c0-2-1-1-3-2l1-2-1-1-1 1h-2c0 2 0 3 1 5 1 1 0 2 0 4 1 0 1 0 2 1h0c0 1 1 2 1 3v2-1c-1-1-1 0-1-1-1-1-1-1-1-2s0-1-1-1l-1-1c0-2 0-4-1-7h0v-2l1-1v-3c0-2-1-6-2-8h-1c0-3-1-4-2-6l1-1c-1 0-1 0-1-1v-2s1-3 2-4c-1 0-1-1-1-1-1-1 0-2 0-2 1-1 0-1 0-2h-1c-1 0-1-3-1-4-1-2-2-5-2-8v-2c-2-2-2-3-3-6v-2c1-1 1-2 0-2 0-1 0-2-1-3v-1s0-1-1-2v-2c-1-1-2-3-2-4v-1c-1-1-1-1-1-2s-1-1-1-2z"></path><path d="M469 528c2 0 3 1 4 3v4 2 1c1 1 1 1 2 1h0c-1 1 0 1-1 1l-2-2h0v-3l-3 1c0-1 2-2 3-3-1-1-1-2-2-3-1 0-1-1-2-1l1-1z" class="S"></path><path d="M407 324c-2 13 5 27 9 40h-1s-1-1-1-2h0v-1l-1-1c0 2 2 4 1 6-3-8-7-19-9-28 0-5-1-10 2-14z" class="N"></path><defs><linearGradient id="x" x1="431.607" y1="401.225" x2="426.893" y2="402.775" xlink:href="#B"><stop offset="0" stop-color="#252321"></stop><stop offset="1" stop-color="#3a3b3d"></stop></linearGradient></defs><path fill="url(#x)" d="M414 366c1-2-1-4-1-6l1 1v1h0c0 1 1 2 1 2h1l31 80-1 1-2-2-30-77z"></path><path d="M452 463l3 3c3 6 5 12 7 18l12 32c1 2 2 8 3 9l5 13c-1 2-1 2 0 3v4l-1-3h0l-1 1c-1-1-3-3-3-5v-1c-1-2-3-2-4-2v-4c-1-2-2-3-4-3 0-1 0-2 1-2h0c1 1 1 1 2 1 2-1 2-2 2-4l-22-60z" class="N"></path><path d="M474 523l7 19h0l-1 1c-1-1-3-3-3-5v-1c-1-2-3-2-4-2v-4c-1-2-2-3-4-3 0-1 0-2 1-2h0c1 1 1 1 2 1 2-1 2-2 2-4z" class="F"></path><path d="M335 513v-1h-1v-1c2 0 3-1 5-2s6 0 9 0c2 0 5 1 8 1 2 1 6 2 8 2h1c-1 1-1 1-2 1 2 0 4 2 6 3 4 1 6 1 9 3 1 1 2 1 2 2 2 0 4-1 6-1 11 4 22 13 32 19h0l2 1c1-1 2-2 3-4 3 2 4 5 5 8 2 2 5 3 6 5l2-2h0c1 0 3-1 4-1h1c0 2 0 5 2 7 2 3 4 7 7 9 2 2 3 4 5 6l3 3 1 3 3 2c1 3 3 7 4 10l2-1 14 38h-2l9 31c2 2 3 5 3 7l4 10 2 7c0 1 1 2 1 2v1 1l1 3 1 4v1l1 3v1l1 2v2c1 0 1 1 1 2s1 2 1 2v2l1 3 3 8c0 1 0 3 1 4v1 1c1 1 1 2 2 3v2c3 3 4 7 5 11l3 12v-1c2-1 2-1 2-2 1 0 2-1 2-1 2-1 2 0 3-1h1l1-1c1 2 1 3 1 5h1 1v1c1 1 1 2 1 2v1l2-3 2 2v1c-1 0-1 1-1 2h0l1-1c2-2 2-3 4-4l3-3c1-1 3-3 4-5 3-2 5-5 8-8v1l-7 9v1c1 1 1 1 1 2l1-1c1-1 1-2 1-4l3-3h3c0 1 0 1-1 2l1 2v-2c1-1 1-1 3 0 2 0 3 1 4 3 1 1 1 2 2 4l-3 3h-2v1l1 1c0 1-1 5-2 7l-2 5c-1 3-1 6-2 8-2 7-4 14-7 20 0 1-1 3-1 4l-2 7c1-2 1-3 3-3l-2 7 2-2h0c0-2 1-3 2-5l1 1c-1 2-2 5-2 6 1 3-1 8-2 10l-10 34v1c0 3-1 6-2 9 0 1-1 3 0 4l-19 59-1-3v-1c-2-5-4-9-6-13l-33-99-31-87v-3l-4-9-2-8-6-16c0-1-1-2-1-3l-3-10c-1-1-2-3-2-4l-3-9-3-8-4-10-4-12-6-18c-1-2-1-4-2-5l-3-9-7-22h7l6 15 4 10v-1-1h0l-1-1v-1h0v-1c-1-1-1-2-2-3 0-2-1-3-1-4-1-2-1-3-2-4 0-2-1-5-2-6 0-1-1-2-1-3v-1c-2 1-5 1-6 0h-1c2-1 5-1 7-1 1 1 3 0 5 1l-1-2c-2-2-2-3-5-4h-1l-1-2v-1c0-1 0 0 1-1h0v-3c1-1 1-1 3-1h1 0c-2-1-4-1-5-1l-1-1c2 0 6-1 8 0h1 1v-2c-6-4-12-8-18-11-3-1-5-3-8-4-1 0-1 0-3 1-4-2-8-4-12-5-16-6-36-8-53-3l-8 2h-1c-7 3-13 7-19 12l-4 4c-1 2-1 3-2 4 2 3 2 5 2 8v1c-1 0-1 0-1-1s-1-1-1-2c-1-1-3-3-5-4-1-1-2 0-3 0 2 1 3 2 4 4h0c1 2 1 3 1 5v1c-2 1-3 2-5 2-1-1-2-1-2-2-2-2-4-3-4-5v-2l-2 2v-2h-2 0c-1-3 1-7 3-11 7-15 26-26 41-31l15-3h0v-1l11-2z" class="P"></path><path d="M524 764l-2-1v-1c0-1 1-2 2-2l1 1-1 3zM269 563c1 1 2 1 4 0h0 3v1l-3 4c-2-2-4-3-4-5z" class="V"></path><path d="M456 592c4 4 7 11 9 16 3 5 6 10 8 15l-1 1c-3-5-5-11-8-16-3-4-6-9-9-12h0c2 1 3 2 5 3-2-2-4-4-5-7h0 1z" class="I"></path><path d="M275 558l-1 1-1-1v-1c-1-1 0-1-1-1 1-1 2-2 4-2v-1c2 0 3 0 4 1l3 2c2 3 2 5 2 8v1c-1 0-1 0-1-1s-1-1-1-2c-1-1-3-3-5-4-1-1-2 0-3 0z" class="N"></path><path d="M466 600c5 8 8 17 11 25 2 3 4 7 5 11v3c1 1 1 2 1 3 1 1 0 1 1 2l-1 1-16-40h0c-1-2-1-3-1-5z" class="T"></path><path d="M502 750c2 0 3 3 5 4 1 1 4 4 5 6l1 1v1c1 2 3 3 4 5 1 1 2 2 4 2h0c2-2 2-3 3-5l1-3h0l3 3h1l-6 10h-1-1c-2-2-5-7-7-9l-12-15z" class="H"></path><path d="M519 863v7c1-1 1 0 1-1h0c1 2 1 4 2 6h1c0 5-2 11-3 16s-1 10-1 16c-1 1-1 2-1 3s0 2-1 3c0-3 1-7 1-10 0-13-1-27 1-40zm12-115h1v1c1 1 1 2 1 2v1l2-3 2 2v1c-1 0-1 1-1 2h0l1-1c2-2 2-3 4-4l-12 15h-1l-3-3 1-1c1-4 4-8 5-12z" class="B"></path><path d="M536 754l1-1c2-2 2-3 4-4l-12 15h-1l-3-3 1-1c0 1 0 1 1 1 2-1 3-3 5-5 1-1 1-1 2-1-1 1-2 2-3 4 2 0 4-4 5-5z" class="F"></path><path d="M285 552c-1-1-2-1-2-3v-1c3-5 9-9 14-11 3-1 8-2 11-1-7 3-13 7-19 12l-4 4z" class="H"></path><path d="M483 645l1-1c-1-1 0-1-1-2 0-1 0-2-1-3v-3l4 11c1 3 2 5 3 7 2 2 3 5 3 7l4 10 2 7c0 1 1 2 1 2v1 1 2 1 2c1 1 1 2 1 3 1 1 1 2 1 3-1-1-1-1-1-2l-2-5c0-1 0-2-1-3l-1 1c-1-2-1-3-2-4 0-1 0-2-1-3 0-1 0-1-1-2l-3-5v-1l-3-8c-3-6-7-12-11-17l1-1c0 1 1 1 1 2l5 8c2 2 7 12 9 13 0-4-4-6-5-10-1-1-1-3-1-4-1-2-2-4-2-7z" class="D"></path><path d="M519 863l1-29c0-7-1-15 0-22l1 4 1 1 1-1v1c1-2 0-2 1-3l1 1v1 2 3c-2 3-3 6-4 10l3-6c3 3 3 5 3 9l-1 1v8h0c0 2-1 3-1 5 0 0-1 1-1 2-1 4 0 12-3 15-1 1-1 2-1 4 0 1 0 0-1 1v-7z" class="K"></path><path d="M502 750l-52-57c6 4 11 10 17 15l25 26c6 7 12 12 17 19 3 3 4 6 7 9 1 2 3 4 5 7h0c-2 0-3-1-4-2-1-2-3-3-4-5v-1l-1-1c-1-2-4-5-5-6-2-1-3-4-5-4z" class="G"></path><path d="M335 513v-1h-1v-1c2 0 3-1 5-2s6 0 9 0c2 0 5 1 8 1 2 1 6 2 8 2h1c-1 1-1 1-2 1 2 0 4 2 6 3 4 1 6 1 9 3 1 1 2 1 2 2h1c1 1 3 2 5 3v1l-4-1h-1l1 1h-1v1c-19-8-37-12-57-10v-1l11-2z" class="U"></path><path d="M335 513v-1h-1v-1c2 0 3-1 5-2s6 0 9 0c2 0 5 1 8 1 2 1 6 2 8 2h1c-1 1-1 1-2 1 2 0 4 2 6 3h-2l-1-1c-2 1-3 0-5 0-4-1-10-3-14-2-3 1-6 1-9 1-1 0-2 0-3-1z" class="K"></path><path d="M335 513v-1h-1v-1c2 0 3-1 5-2s6 0 9 0c-3 0-4 0-6 1h-2c2 0 3 0 4 1h1l-3 1h0l-4 2c-1 0-2 0-3-1z" class="L"></path><path d="M496 684l1-1c1 1 1 2 1 3l2 5c0 1 0 1 1 2 0-1 0-2-1-3 0-1 0-2-1-3v-2-1-2l1 3 1 4v1l1 3v1l1 2v2c1 0 1 1 1 2s1 2 1 2v2l1 3 3 8c0 1 0 3 1 4v1 1c1 1 1 2 2 3v2c3 3 4 7 5 11l3 12v1l-1 1-2-2h0c0 2 1 4 2 6 0 2 1 3 1 5h0c-1-1-2-2-2-3h0l-1-1c-1 0-1-1-2-2-4-8-5-18-8-27-1-3-2-7-4-10l-1-4v-1c-2-4-4-10-7-14l-2-3 1-1c1 1 2 3 3 4 1 3 4 13 6 14v-4c-1-1-2-4-2-5-1-5-2-10-4-15 0-2-1-3-1-4z" class="D"></path><path d="M496 684l1-1c1 1 1 2 1 3l2 5c0 1 0 1 1 2 0-1 0-2-1-3 0-1 0-2-1-3v-2-1-2l1 3 1 4v1l1 3v1l1 2v2c1 0 1 1 1 2s1 2 1 2v2l1 3 3 8c0 1 0 3 1 4v1 1c1 1 1 2 2 3v2 1c1 1 1 1 1 2v1 2c1 1 1 1 1 2v2l1 1c0 1 0 1-1 2v-1l-1-1v-2c-1-1 0-1-1-2v-2-1h-1v-2l-2-6-2-6v-1c-1-1-1-2-1-2-1-2-2-4-3-5s-2-4-2-5c-1-5-2-10-4-15 0-2-1-3-1-4z" class="J"></path><defs><linearGradient id="y" x1="417.399" y1="538.739" x2="412.017" y2="547.862" xlink:href="#B"><stop offset="0" stop-color="#d1d1d1"></stop><stop offset="1" stop-color="#f9f8f8"></stop></linearGradient></defs><path fill="url(#y)" d="M386 520c11 4 22 13 32 19h0l2 1c1-1 2-2 3-4 3 2 4 5 5 8 2 2 5 3 6 5l5 5 2 2c1 1 0 0 0 1l3 3h-2v1c1 4 5 4 6 8-3-1-6-5-8-8h-1c-1-1-1-1-3-2h0l-20-14c-11-8-23-14-35-19v-1h1l-1-1h1l4 1v-1c-2-1-4-2-5-3h-1c2 0 4-1 6-1z"></path><path d="M418 539l2 1c1-1 2-2 3-4 3 2 4 5 5 8 2 2 5 3 6 5l5 5 2 2c1 1 0 0 0 1l3 3h-2c-7-9-16-14-24-21h0z" class="I"></path><path d="M411 556c11 7 22 14 32 23 4 3 10 8 13 13h-1 0c1 3 3 5 5 7-2-1-3-2-5-3h0l-3-3c-2-2-4-4-7-6l-9-6c-1 0-1 0-2-1-1 0-2-1-3-1s-2-1-3-1l-7-2c-4-1-7-2-11-2l-1-2c-2-2-2-3-5-4h-1l-1-2v-1c0-1 0 0 1-1h0v-3c1-1 1-1 3-1h1 0c-2-1-4-1-5-1l-1-1c2 0 6-1 8 0h1 1v-2z" class="D"></path><path d="M411 556c11 7 22 14 32 23 4 3 10 8 13 13h-1c-7-10-17-18-27-24-4 1-11-4-14-6-3-1-5-1-7-2h0c-2-1-4-1-5-1l-1-1c2 0 6-1 8 0h1 1v-2z" class="G"></path><path d="M436 547h0c1 0 3-1 4-1h1c0 2 0 5 2 7 2 3 4 7 7 9 2 2 3 4 5 6l3 3 1 3 3 2c1 3 3 7 4 10l2-1 14 38h-2l9 31c-1-2-2-4-3-7l-4-11c-1-4-3-8-5-11-3-8-6-17-11-25-1-3-4-7-6-11-7-11-15-20-24-30h0c2 1 2 1 3 2h1c2 3 5 7 8 8-1-4-5-4-6-8v-1h2l-3-3c0-1 1 0 0-1l-2-2-5-5 2-2z" class="C"></path><path d="M459 574l3 2c1 3 3 7 4 10l1 3c0 1 1 2 0 3l-10-16c1 0 2-1 2-2z" class="J"></path><path d="M466 586l2-1 14 38h-2l-13-31c1-1 0-2 0-3l-1-3z" class="G"></path><defs><linearGradient id="z" x1="436.613" y1="561.765" x2="456.414" y2="560.038" xlink:href="#B"><stop offset="0" stop-color="#030607"></stop><stop offset="1" stop-color="#312f2f"></stop></linearGradient></defs><path fill="url(#z)" d="M436 547h0c1 0 3-1 4-1h1c0 2 0 5 2 7 2 3 4 7 7 9 2 2 3 4 5 6l3 3 1 3c0 1-1 2-2 2-2-3-4-6-7-9-2-2-5-5-6-7l-3-3c0-1 1 0 0-1l-2-2-5-5 2-2z"></path><defs><linearGradient id="AA" x1="538.183" y1="783.047" x2="521.528" y2="779.071" xlink:href="#B"><stop offset="0" stop-color="#acabab"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#AA)" d="M556 733v1l-7 9v1c-1 2-3 4-4 6-1 3-4 3-3 6v5c-1 2-2 4-2 6v3c0 1 1 1-1 3-1 1-1 2-2 3v2c0 2 0 2-1 3 1 1 1 1 1 3 1 2 1 4 0 5l-1 1c-1 1-1 2-1 3l1 1c1 0 0 0 1 1-1 0-2 0-2 1-1 3-3 5-4 7l-3 6v3c0 1-1 2-1 2-1 2-1 3-2 4v-2-1l-1-1c-1 1 0 1-1 3v-1l-1 1-1-1-1-4 1-24c0-5 1-10 0-14h1 1l6-10 12-15 3-3c1-1 3-3 4-5 3-2 5-5 8-8z"></path><path d="M555 738h3c0 1 0 1-1 2l1 2v-2c1-1 1-1 3 0 2 0 3 1 4 3 1 1 1 2 2 4l-3 3h-2v1l1 1c0 1-1 5-2 7l-2 5c-1 3-1 6-2 8-2 7-4 14-7 20 0 1-1 3-1 4l-2 7c-3 5-5 11-6 16v1l-1-1c0 2 0 3-1 4s-2 1-2 1c-1 0-2-1-3-2v-4h1c-1-1 0-1-1-1s-2-1-3-2v1l-3 3h0l-1 1c-1 1-2 3-3 5l-3 6c1-4 2-7 4-10v-3c1-1 1-2 2-4 0 0 1-1 1-2v-3l3-6c1-2 3-4 4-7 0-1 1-1 2-1-1-1 0-1-1-1l-1-1c0-1 0-2 1-3l1-1c1-1 1-3 0-5 0-2 0-2-1-3 1-1 1-1 1-3v-2c1-1 1-2 2-3 2-2 1-2 1-3v-3c0-2 1-4 2-6v-5c-1-3 2-3 3-6 1-2 3-4 4-6 1 1 1 1 1 2l1-1c1-1 1-2 1-4l3-3z" class="M"></path><path d="M547 795l1 1c-1 1-3 2-4 4-2 3-3 7-6 10-1 0-2 1-3 1h-1l-2 2h-1l3-4c3-5 9-9 13-14z" class="H"></path><path d="M546 779s1-1 1-2c1-4 5-7 4-12 0-1 0-1 1-2h2c0 2 0 2 1 3-3 4-3 7-4 11-1 3-2 6-3 8-1 7-6 10-9 15s-6 9-9 13l-5 8v-3c1-1 1-2 2-4 0 0 1-1 1-2v-3l3-6c1-2 3-4 4-7 0-1 1-1 2-1l6-5c1-2 1-4 1-6v-3c0-3-1-4 0-7h0l1 4 1 1z" class="O"></path><path d="M537 795l6-5c1-2 1-4 1-6v-3c0-3-1-4 0-7h0l1 4 1 1 1 1v4 2c-3 3-5 7-8 10-3 4-6 8-8 12v2c-1 1-1 2-1 3l-5 8v-3c1-1 1-2 2-4 0 0 1-1 1-2v-3l3-6c1-2 3-4 4-7 0-1 1-1 2-1z" class="E"></path><defs><linearGradient id="AB" x1="552.985" y1="773.09" x2="537.287" y2="771.998" xlink:href="#B"><stop offset="0" stop-color="#7a797a"></stop><stop offset="1" stop-color="#acabaa"></stop></linearGradient></defs><path fill="url(#AB)" d="M555 738h3c0 1 0 1-1 2l1 2v-2c1-1 1-1 3 0 2 0 3 1 4 3 1 1 1 2 2 4l-3 3h-2-5v2c-1 1-1 1-2 1v1h1v1c0 4 0 8-1 11-1-1-1-1-1-3h-2c-1 1-1 1-1 2 1 5-3 8-4 12 0 1-1 2-1 2l-1-1-1-4h0c-1 3 0 4 0 7v3c0 2 0 4-1 6l-6 5c-1-1 0-1-1-1l-1-1c0-1 0-2 1-3l1-1c1-1 1-3 0-5 0-2 0-2-1-3 1-1 1-1 1-3v-2c1-1 1-2 2-3 2-2 1-2 1-3v-3c0-2 1-4 2-6v-5c-1-3 2-3 3-6 1-2 3-4 4-6 1 1 1 1 1 2l1-1c1-1 1-2 1-4l3-3z"></path><path d="M550 746l1-1c1-1 1-2 1-4l1 4h-1c0 1-1 1-2 2v2l2 2c0 2-1 3-1 3 0 2 1 3 0 4l-1 1-1-2 1-3v-1-3c-1-1-1-2 0-4z" class="B"></path><path d="M545 757l1-1c1 0 1 0 2-1l1 2 1 2h-1v2c0 1-2 2-2 4v2c-2 1-3 2-5 3 1-1 1-2 1-4 0-1 0-2 1-4 0-1 0-1 2-1 0-1-1-2-1-4z" class="Q"></path><path d="M555 738h3c0 1 0 1-1 2l1 2 1 2v3l-2 1c-2 0-3-1-4-3l-1-4 3-3z" class="P"></path><path d="M542 756h3v1c0 2 1 3 1 4-2 0-2 0-2 1-1 2-1 3-1 4 0 2 0 3-1 4l-1 1v3c-1 0 0 0-1 1 0 1-1 2-2 3v1l-1-3c1-1 1-2 2-3 2-2 1-2 1-3v-3c0-2 1-4 2-6v-5z" class="B"></path><defs><linearGradient id="AC" x1="548.792" y1="751.576" x2="541.612" y2="752.905" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#AC)" d="M542 756c-1-3 2-3 3-6 1-2 3-4 4-6 1 1 1 1 1 2-1 2-1 3 0 4v3 1l-1 3-1-2c-1 1-1 1-2 1l-1 1v-1h-3z"></path><path d="M558 742v-2c1-1 1-1 3 0 2 0 3 1 4 3 1 1 1 2 2 4l-3 3h-2-5v2c-1 1-1 1-2 1v1h1v1c0 1 0 1-1 2-1-1-2-3-2-3v-2c0-1-1-2-1-3l2-1c1 1 1 1 3 1v-1l2-1v-3l-1-2z" class="O"></path><path d="M547 803c1-2 1-3 3-3l-2 7 2-2h0c0-2 1-3 2-5l1 1c-1 2-2 5-2 6 1 3-1 8-2 10l-10 34v1c0 3-1 6-2 9 0 1-1 3 0 4l-19 59-1-3v-8c1-1 1-2 1-3s0-2 1-3c0-6 0-11 1-16s3-11 3-16h-1c-1-2-1-4-2-6h0c0-2 0-3 1-4 3-3 2-11 3-15 0-1 1-2 1-2 0-2 1-3 1-5h0v-8l1-1c0-4 0-6-3-9 1-2 2-4 3-5l1-1h0l3-3v-1c1 1 2 2 3 2s0 0 1 1h-1v4c1 1 2 2 3 2 0 0 1 0 2-1s1-2 1-4l1 1v-1c1-5 3-11 6-16z" class="C"></path><path d="M522 871c1 0 2 0 3-1h1c1 2 2 3 4 4l-1 1-2 1-1 4-1-1c0-1 0-1-1-2l-1-2h0c0-1-1-2-1-2-1-1 0-2 0-2z"></path><path d="M539 838s1 1 2 0l-3 8c-2 7-3 16-7 21l-1-1 9-28z" class="E"></path><path d="M523 875h0l1 2c1 1 1 1 1 2l1 1-8 30c0-1 0-2 1-3 0-6 0-11 1-16s3-11 3-16z" class="I"></path><defs><linearGradient id="AD" x1="550.858" y1="806.056" x2="538.446" y2="836.364" xlink:href="#B"><stop offset="0" stop-color="#676766"></stop><stop offset="1" stop-color="#918f90"></stop></linearGradient></defs><path fill="url(#AD)" d="M548 807l2-2h0c0-2 1-3 2-5l1 1c-1 2-2 5-2 6l-9 28c0 1-1 3-1 3-1 1-2 0-2 0l5-18c1-4 2-9 4-13z"></path><path d="M547 803c1-2 1-3 3-3l-2 7c-2 4-3 9-4 13l-5 18-9 28c-1-1-2-1-3-1 2-3 2-7 3-10 1-2 2-4 2-6 1-3 8-22 7-24h0-1-2c-3 0-5-2-7-4h0l-1-2 3-3v-1c1 1 2 2 3 2s0 0 1 1h-1v4c1 1 2 2 3 2 0 0 1 0 2-1s1-2 1-4l1 1v-1c1-5 3-11 6-16z" class="V"></path><path d="M524 825c1-2 2-4 3-5l1-1h0l1 2h0c2 2 4 4 7 4h2 1 0c1 2-6 21-7 24 0 2-1 4-2 6-1 3-1 7-3 10 1 0 2 0 3 1l1 1-1 1c-1 0-2 0-4 2h0-1c-1 1-2 1-3 1 0 0-1 1 0 2 0 0 1 1 1 2h-1c-1-2-1-4-2-6h0c0-2 0-3 1-4 3-3 2-11 3-15 0-1 1-2 1-2 0-2 1-3 1-5h0v-8l1-1c0-4 0-6-3-9z" class="H"></path><path d="M521 868c1-1 3-2 3-3s1-1 1-2c1-1 2-3 3-5 1-1 2-7 4-9 0 2-1 4-2 6-1 3-1 7-3 10 1 0 2 0 3 1l1 1-1 1c-1 0-2 0-4 2h0-1c-1 1-2 1-3 1 0 0-1 1 0 2 0 0 1 1 1 2h-1c-1-2-1-4-2-6l1-1z" class="G"></path><path d="M527 865c1 0 2 0 3 1l1 1-1 1c-1 0-2 0-4 2h0-1c-1 1-2 1-3 1 2-2 3-4 5-6z" class="D"></path><path d="M524 825c1-2 2-4 3-5l1-1h0l1 2h0c0 2 0 3 1 5h0l2 2c0 1 0 1-1 2l1 1v1c-2 3-1 6-2 9-2 3-2 7-2 10-1 4-3 8-4 12 0 2-2 3-3 5l-1 1h0c0-2 0-3 1-4 3-3 2-11 3-15 0-1 1-2 1-2 0-2 1-3 1-5h0v-8l1-1c0-4 0-6-3-9z" class="C"></path></svg>