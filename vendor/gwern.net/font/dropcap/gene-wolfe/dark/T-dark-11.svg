<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="114 -6 708 980"><!--oldViewBox="0 0 984 1024"--><style>.B{fill:#b3b1b1}.C{fill:#c1bfbe}.D{fill:#f6f5f4}.E{fill:#dcdada}.F{fill:#eeeded}.G{fill:#cfcdcc}.H{fill:#c8c6c6}.I{fill:#d6d3d3}.J{fill:#b7b5b4}.K{fill:#898786}.L{fill:#121211}.M{fill:#e7e5e5}.N{fill:#a1a09f}.O{fill:#a8a6a5}.P{fill:#989695}.Q{fill:#e2e0e0}.R{fill:#646262}.S{fill:#929090}.T{fill:#252424}.U{fill:#7a7978}.V{fill:#464444}.W{fill:#727070}.X{fill:#807f7e}.Y{fill:#575655}.Z{fill:#444343}</style><path d="M615 104v1c1 1 1 3 1 4l1 1c0 1-1 2-1 2h-2c1-3 1-5 1-8z" class="I"></path><path d="M341 856c4-1 9-1 13 1v1c-4 1-11 0-14-1h0 0c-1-1-2-1-3-1l-1 1v-1h5z" class="B"></path><path d="M353 130l1-1 1 1-1 2c1 1 2 2 3 2l2 2v1c-4-1-9-1-12-5h2 3l1-2z" class="S"></path><path d="M575 141l6-1v3c-1 0-3 1-4 1v1c1 0 3 0 3 1l1 1c-2 1-7 0-10 0l1-1c0-1 1-1 2-1l1-1v-3z" class="N"></path><path d="M571 147c-4-1-9-3-14-4 6-1 12 0 18-2v3l-1 1c-1 0-2 0-2 1l-1 1z" class="R"></path><path d="M580 92c0-1 1-3 2-4 2-3 6-8 10-9l1 1v2c-1 1-6 7-7 7s-1 1-2 1h-1c-1 0-2 1-2 2h-1 0z" class="I"></path><path d="M612 91c2 2 3 3 4 5 2 4 4 8 2 13 0 2-1 4-2 6v1l2 1c-1 1-3 2-4 3-4 2-9 0-13-1 6-1 9-2 13-7h2s1-1 1-2c2-8-2-12-5-18v-1z" class="C"></path><path d="M341 889v-3c-1-3-1-6-1-9 0-1 1-3 1-4 0 0-2-1-2-2-2-2-4-3-6-4-1-1-4-2-4-3h1c4 0 10 6 13 9h0 2 0c-2 2-1 4-1 7v2l1 2c1 1 0 1 1 3v3l-1-1h-1c0 1 2 3 3 5h-1c-1-1-2-2-3-4h-1l-1-1z" class="W"></path><path d="M556 864c2 1 5 2 7 4 3 2 5 5 7 8 0 6-2 12-4 18l-1-1 1-2c1-2 1-2 1-3l-3-2v-4l3-5c-1-1-1-1-1-2-1 0-1 0-1-1s0-3-1-4c-2-2-5-4-8-4h-1-1c0-1 1-1 2-2z" class="O"></path><defs><linearGradient id="A" x1="346.274" y1="118.73" x2="342.634" y2="111.076" xlink:href="#B"><stop offset="0" stop-color="#807e7e"></stop><stop offset="1" stop-color="#9f9c9a"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M344 110h1c0-1 1-2 2-2h1c1 0 3 0 4 1-2 3-3 6-5 10-2 1-3 1-4 3-2 2-4 5-7 6v1c-5 3-10 5-15 6v-1l5-1c4-2 13-6 15-11 1-4 1-8 3-12z"></path><path d="M337 142l16 1c-8 4-16 4-26 3-2 0-4 0-6 1l-14-4h0 5c4 1 9 1 13 0h12v-1z" class="Z"></path><path d="M599 851c-13-1-24 0-37 3 3-2 6-4 9-5 6-3 15-3 22-2 6 1 12 4 18 6-3 1-4 0-7 0-1 0-2 0-2-1h0l-1-1h-2z" class="J"></path><path d="M318 79l1-1c4 0 8 4 11 7 5 6 6 13 5 20v3h-1v2l-2-1c1-2 1-3 1-4l-1-1v-1c0-2 0-3-1-4 0-1 0-3-1-4h0c0-1 0-2-1-2h0l-2-2-1-2-2-2-1-1-1-1c-1-2-3-3-4-6z" class="D"></path><path d="M599 851h2l1 1h0-6l-1 1h0-3s-1 1-1 2h1 6v1c-13 0-26 2-38 4-4 0-7 1-10 1 11-5 21-7 32-8 4-1 8-1 11-1 2 0 4 0 5-1h1z" class="B"></path><defs><linearGradient id="C" x1="326.578" y1="845.758" x2="322.98" y2="856.214" xlink:href="#B"><stop offset="0" stop-color="#a09e9e"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#C)" d="M302 852c3-1 7-1 10-2 11-1 20-2 31-2-4 1-8 2-11 6 1 1 6 1 9 2h-5v1l1-1c1 0 2 0 3 1h0c-13 0-25-2-38 0l-1-1h2c2-1 4-1 6-1 1 1 0 0 1 0 1-1 3 0 4 0h4l-2-2c-1 0-2-1-2-1l-1-1c-1 1-3 1-4 1-1 1-3 0-4 0v1c-2 0-2 0-3-1z"></path><defs><linearGradient id="D" x1="318.448" y1="127.737" x2="314.752" y2="113.467" xlink:href="#B"><stop offset="0" stop-color="#abaaa9"></stop><stop offset="1" stop-color="#d3d0d0"></stop></linearGradient></defs><path fill="url(#D)" d="M300 126l1 1c3 1 6 1 9 0h0c5-2 10-6 13-10h0l3-6h0c1-3 0-6 0-8l1-1h1c0 2 0 7-1 8v1c0 1 0 2 1 3 0-1 1-2 2-4h0v-3c1-2 0-5 1-7v-1c1 1 1 2 1 4v1l1 1c0 1 0 2-1 4l2 1v-2h1c-1 6-4 12-9 16-6 6-14 7-22 6-4 0-7-1-11-3l7-1z"></path><defs><linearGradient id="E" x1="614.916" y1="122.706" x2="591.102" y2="91.851" xlink:href="#B"><stop offset="0" stop-color="#dcd9da"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#E)" d="M616 127h-4c-2 1-5 1-7 0-6-1-13-6-17-12-2-5-3-10-2-15 2-4 5-9 9-11v-1l3-1 2-1c2 0 6 2 8 3 1 0 1 1 2 1h1l1 1v1c3 6 7 10 5 18l-1-1c0-1 0-3-1-4v-1c-1-5-4-9-8-11-3-2-5-2-8-1s-6 4-8 7c-2 4-2 9 0 13 2 6 7 10 13 12 5 2 10 2 15 1-1 1-2 2-3 2z"></path><defs><linearGradient id="F" x1="591.794" y1="127.935" x2="597.173" y2="91.52" xlink:href="#B"><stop offset="0" stop-color="#a3a1a1"></stop><stop offset="1" stop-color="#cbc9c8"></stop></linearGradient></defs><path fill="url(#F)" d="M586 89c-1 2-2 4-3 7 4-3 8-7 13-9h1c3-2 6-1 9 0 2 1 4 2 5 3h-1c-1 0-1-1-2-1-2-1-6-3-8-3l-2 1-3 1v1c-4 2-7 7-9 11-1 5 0 10 2 15 4 6 11 11 17 12 2 1 5 1 7 0h4l-6 3c-6 1-13 1-19-3-7-4-10-10-12-18-2-5-2-12 1-17h0 1c0-1 1-2 2-2h1c1 0 1-1 2-1z"></path><path d="M593 82c5-3 10-4 15-2 6 1 12 6 15 11 3 6 4 13 3 19 0 1-1 1-1 2h-1l-1-2-5 7-2-1v-1c1-2 2-4 2-6 2-5 0-9-2-13-1-2-2-3-4-5l-1-1c-1-1-3-2-5-3-3-1-6-2-9 0h-1c-5 2-9 6-13 9 1-3 2-5 3-7 1 0 6-6 7-7z" class="D"></path><defs><linearGradient id="G" x1="308.052" y1="126.342" x2="304.469" y2="79.925" xlink:href="#B"><stop offset="0" stop-color="#d9d8d7"></stop><stop offset="1" stop-color="#fefefd"></stop></linearGradient></defs><path fill="url(#G)" d="M304 79h8c2 0 5 1 6 0 1 3 3 4 4 6l1 1 1 1 2 2 1 2 2 2h0c1 0 1 1 1 2h0c1 1 1 3 1 4v1c-1 2 0 5-1 7v3h0c-1 2-2 3-2 4-1-1-1-2-1-3v-1c1-1 1-6 1-8h-1l-1 1c0 2 1 5 0 8h0l-3 6h0c-3 4-8 8-13 10h0c-3 1-6 1-9 0l-1-1c5-1 11-2 15-5 4-4 7-8 7-13 1-4 0-9-2-12-3-3-7-5-11-6-4 0-8 2-11 4-3 3-5 7-5 12 0 3 1 6 3 9 3 2 7 3 10 3h1 0c-2 1-3 2-4 2-4 1-8 1-10-1-4-3-10-7-10-11-2-7 0-13 4-18s10-10 17-11z"></path><path d="M304 79h8c2 0 5 1 6 0 1 3 3 4 4 6l-1-1c-1 0-1 0-2-1-3 0-9-1-11-3h0l-4-1z" class="E"></path><path d="M588 869l2-2c6-3 15-5 22-3l5 1c-4 1-9 1-13 3-5 2-8 6-12 10-3 6-4 11-3 18 1 4 4 7 8 9 3 2 6 2 9 1 4-1 6-4 8-7 1-3 1-7 0-10-1-2-3-5-5-5-5-2-9 0-13 2 4-5 8-8 15-8 3 0 7 1 10 4 4 4 5 8 5 13 0 6-3 11-8 16-5 4-11 6-19 6-3 0-5-1-8-1l-1 3c-2 0-3-1-5-2-5-6-9-14-9-21 0-3 0-7 1-10h0c1-6 6-14 11-17z" class="F"></path><path d="M588 869l2-2c6-3 15-5 22-3l5 1c-4 1-9 1-13 3-5 2-8 6-12 10 0-2 1-3 2-4l2-2c-3 1-5 4-7 5-1 1-1 3-2 4s-1 3-2 4v1c0 1 0 2-1 3v7l1 1v1 2h1c0 1 1 3 1 4 2 2 4 5 6 6l1 1 2 1c1 0 2 0 2 1h-2c-2-1-4-1-5-3h-1c-2-1-3-3-4-4h0c-1-2-2-3-2-4v-1c-1-1-1-2-1-3-1-2 1-9-1-11l1-1v-2c0-1 0-1 1-1v-1c-1 2-2 3-3 5-1 1-1 3-1 5l-1 1h0v-7-1c0-1 0 0 1-1v-2h1v-1c-2 1-2 5-4 5h0c1-6 6-14 11-17z" class="B"></path><path d="M588 869l2-2c6-3 15-5 22-3-4 0-7 1-11 2-2 1-6 3-9 2-1 0-2 1-4 1z" class="P"></path><path d="M304 868l-9-3c7-2 17-3 24 1 5 3 10 7 13 13 3 7 5 14 3 22h0c-1 7-5 13-11 16-1 1-2 2-3 2 0-2 0-3 1-5-8 3-15 4-23 0-6-3-11-9-13-15-1-4-1-10 1-14h1l1 1 2-2c2-3 5-6 9-7 3-1 8 1 11 3 2 1 3 2 4 4v1l-1-1c-4-1-8-2-12 0-3 1-5 4-6 6-1 3 0 8 1 11 2 3 5 5 9 6 3 1 6-1 9-3 4-2 5-6 6-11 0-3 0-6-1-10-4-7-8-12-16-15z" class="F"></path><path d="M319 866c5 3 10 7 13 13h-1c-1-1-2-3-3-4-1-2-2-3-4-4v1 1c2 2 6 6 6 10v1l-1-1-3-6c-2-3-8-8-7-11z" class="G"></path><path d="M304 868l-9-3c7-2 17-3 24 1-1 3 5 8 7 11l3 6 1 1c0 2 1 4 1 6h0c0-3-3-7-4-10s-4-6-7-8c-4-5-8-6-14-6-1 0-1 1-2 2z" class="P"></path><path d="M304 868c1-1 1-2 2-2 6 0 10 1 14 6 3 2 6 5 7 8h-1c-1-2-2-3-4-5-1-2-3-3-5-5l4 7h-1c-1 0-1 1-1 2l2 4h-1c-4-7-8-12-16-15z" class="H"></path><path d="M566 894c-4 10-7 15-16 21 1-2 2-5 1-6-2 0-4 2-6 3s-4 2-7 2c-5 2-12 1-17-2h0c1-1 2-1 3-2-1-2-6-2-8-3-6-3-8-8-10-14 4 3 8 5 13 6s13 2 18-1v-1c-2 0-4-1-5-2v-1c4-4 13-1 17-6 1-1 1-2 1-4-2-2-5-1-8-3-4-2-8-6-11-8l9-1h0c-1-2-3-4-5-5 6-3 14-5 21-3-1 1-2 1-2 2h1 1c3 0 6 2 8 4 1 1 1 3 1 4s0 1 1 1c0 1 0 1 1 2l-3 5v4l3 2c0 1 0 1-1 3l-1 2 1 1z" class="D"></path><path d="M555 866h1c3 0 6 2 8 4 1 1 1 3 1 4s0 1 1 1c0 1 0 1 1 2l-3 5v4l3 2c0 1 0 1-1 3l-1 2-2 2c-1 2-2 3-3 4h-1c-1 0-2-1-2-1 0-1 1-1 0-3l-1 1v-3h0 0l-6 6h-1c2-2 6-6 6-8l-1-1 3-8v-2-3c-1-1 0-2 0-3v-1c-1 1-2 2-3 2-1 1-2 1-3 1l1-1c1-1 3-1 5-2-3-2-4-3-6-5v-1c2 1 3 1 4 3h0l-1-2h1v-2z" class="S"></path><path d="M556 866c3 0 6 2 8 4 1 1 1 3 1 4s0 1 1 1c0 1 0 1 1 2l-3 5c-2 3-5 6-7 8 1-2 2-3 3-6 0-3 4-7 3-9l-1-1c0 3-1 4-2 6l-1-1 1-3c0-1 0-2-1-3v-2c1 0 1 1 2 1v-2h1l1 1h0v-1c-1 0-2-1-2-1l-2 1c0-2-2-2-3-4z"></path><path d="M347 103c4-7 10-14 17-18l-1 9c3-3 6-6 10-8 6-3 13-3 20-3l-6 6c4 0 7 0 11 1v1 1c-1 0 0 0-1 1 1 1 3 2 5 3 1 2 3 5 3 8v2h-1-1c-6-4-16-6-23-5-1 0-2 1-4 1 0 2 0 2 1 3s2 2 3 2c-5 1-17 1-20 6-1 2-1 2 0 4 1 1 2 1 4 1s5 0 7 1c3 1 6 4 8 6l-12 3 10 4c-6 3-11 5-18 5v-1l-2-2c-1 0-2-1-3-2l1-2-1-1-1 1-1 2h-3-2c-2-3-3-7-4-10 1-2 2-2 4-3 2-4 3-7 5-10-1-1-3-1-4-1h-1c-1 0-2 1-2 2h-1c0-3 2-5 3-7z" class="D"></path><path d="M347 103l1 1c2-1 3-2 5-4 1 2-1 4 1 6v1l-2 2c-1-1-3-1-4-1h-1c-1 0-2 1-2 2h-1c0-3 2-5 3-7z" class="B"></path><path d="M354 106c1-1 1-2 3-3v2l7-6h1l-4 4v1c-1 2-1 3-2 4-1 2-1 4-2 6v3 1c0 2 2 3 1 6h0c-2 2-2 3-2 5 1 2 3 4 4 5l-5-4-1-1-1 1-1 2h-3-2c-2-3-3-7-4-10 1-2 2-2 4-3 2-4 3-7 5-10l2-2v-1z" class="K"></path><path d="M349 119c0 2 1 4 1 5 0 2 1 3 2 5h-2v-2l-1 1-1-1c0-1-1-2 0-4v-1l1-3z" class="V"></path><path d="M354 107c0 1 0 2-1 3-1 3-3 6-4 9l-1 3v1c-1 2 0 3 0 4l1 1v2h-1c-1-1-2-2-2-3-1-2 1-6 1-8 2-4 3-7 5-10l2-2z" class="Z"></path><path d="M343 122c1-2 2-2 4-3 0 2-2 6-1 8 0 1 1 2 2 3h1v-2l1-1v2h2l1 1-1 2h-3-2c-2-3-3-7-4-10z" class="R"></path><path d="M358 124c-1 0-2-1-3-2-2-1-3-2-3-4 0-5 6-11 9-15v1c-1 2-1 3-2 4-1 2-1 4-2 6v3 1c0 2 2 3 1 6h0z" class="F"></path><path d="M561 136c-4 1-9 2-13 1s-8-3-12-5c3-1 6-2 8-4h0c-2-2-5-3-8-3 2-2 4-4 7-5s7-1 10-2c1 0 2-1 3-3 0-1 0-2-2-3-5-5-14-4-20-5h-1c1-1 3-2 3-4-1-1-1-1-3-1-9-2-19 2-27 7l2-6c2-4 5-5 9-7l-5-3c4-2 8-2 13-2-2-1-3-2-4-4 1-1 3-2 5-2 11-2 18 3 27 9 0-1-3-8-4-10 6 3 13 7 17 13l2 4c4 7 6 17 5 25l-1 1-2 5c-2 2-5 4-8 4h-1z" class="D"></path><defs><linearGradient id="H" x1="562.372" y1="118.402" x2="573.067" y2="112.888" xlink:href="#B"><stop offset="0" stop-color="#8b8989"></stop><stop offset="1" stop-color="#a9a8a6"></stop></linearGradient></defs><path fill="url(#H)" d="M561 109l-8-8v-1c2 2 4 3 6 5 1 1 3 2 4 2v-3c0-2 0-3 1-3l2 1c1 0 1 0 2-1 4 7 6 17 5 25l-1 1-2 5c-2 2-5 4-8 4h-1c-1-1-4 0-6-1l3-3-1-1 1-1-1-2-3-2 1-1h1c1-1 6-5 6-7 1-1 0-3 0-4 0-2-1-3-1-5h0z"></path><path d="M561 131l1 1c1 0 1-1 2-1v-1h0c1-1 2-2 2-3s-1-1-1-1l1-1 2 2h0 4l-2 5c-2 2-5 4-8 4h-1c-1-1-4 0-6-1l3-3c1 0 2 0 3-1z" class="X"></path><path d="M568 127h4l-2 5v-2c-1-1-1-2-1-2l-1-1z" class="K"></path><path d="M561 109c2 2 3 4 4 7l1 4v2c-1 1-2 1-3 1l-1 1h2v5l-3 2c-1 1-2 1-3 1l-1-1 1-1-1-2-3-2 1-1h1c1-1 6-5 6-7 1-1 0-3 0-4 0-2-1-3-1-5h0z" class="B"></path><path d="M555 125l2 1c2-2 3-3 4-5h1v1l-1 1c-1 2-2 4-2 6l-1 1-1-2-3-2 1-1z" class="M"></path><path d="M353 865c9-3 15-4 23 0l-7 5c2 1 4 2 7 2 1 0 4 0 5 1-5 5-10 7-17 9-1 0-3 1-4 1-1 1-1 2-1 3 0 2 1 3 3 4 5 4 14 3 20 4-1 1-7 5-7 6 9 3 19 0 28-5 1-1 3-2 4-3 1 3 0 5-2 8-4 7-8 9-15 11l4 4c-9 4-17 5-26 1l-9-4 2 7c-4-1-12-6-14-11h0c-2-2-2-5-3-8l-3-11 1 1h1c1 2 2 3 3 4h1c-1-2-3-4-3-5h1l1 1v-3c-1-2 0-2-1-3l-1-2v-2c0-3-1-5 1-7h0v-1c1-3 6-7 8-7z" class="D"></path><path d="M357 867c1 2 0 3 1 5 0 2-1 3-2 5l-4 7c-1 2-1 7 0 9 1 3 3 6 5 9h-1l-2-3c0 2-1 3-2 5-1 0-1 0-2-1l-2-1h-1l-1 2v1c1 1 1 2 1 3-2-2-2-5-3-8l-3-11 1 1h1c1 2 2 3 3 4h1c-1-2-3-4-3-5h1l1 1c1 1 2 3 3 4h0c0-2-1-3-1-4v-1c1-2 1-2 1-4 1-1 1-1 1-2l1-3c2-1 4-6 5-8l-1-2c1-1 2-1 2-3z" class="B"></path><path d="M344 900l2-2c1-1 4 1 6 2l-1-4c-1-1-1-2-1-3h0l1 1c1 2 2 3 3 5 0 2-1 3-2 5-1 0-1 0-2-1l-2-1h-1l-1 2v1c1 1 1 2 1 3-2-2-2-5-3-8z" class="F"></path><path d="M345 873v-1c1-3 6-7 8-7h1c1 1 1 1 2 1l1 1c0 2-1 2-2 3l1 2c-1 2-3 7-5 8l-1 3c0 1 0 1-1 2 0 2 0 2-1 4v1c0 1 1 2 1 4h0c-1-1-2-3-3-4v-3c-1-2 0-2-1-3l-1-2v-2c0-3-1-5 1-7h0z" class="W"></path><path d="M345 873c1 0 0 0 1 1v1h1c1-3 2-4 3-5-1 2-2 4-3 7s0 9 1 12v1c0 1 1 2 1 4h0c-1-1-2-3-3-4v-3c-1-2 0-2-1-3l-1-2v-2c0-3-1-5 1-7z"></path><path d="M345 873v-1c1-3 6-7 8-7h1c1 1 1 1 2 1l1 1c0 2-1 2-2 3l1 2c-1 2-3 7-5 8h-2 0l-1-1 1-1h1c2-1 2-4 3-5s1-1 1-2l-1-1h0v1c-1 1-1 2-2 4l-1-1h0v-1c1-2 0-3 2-5 1-1 0 0 0-1h0c-1 1-2 1-2 3-1 1-2 2-3 5h-1v-1c-1-1 0-1-1-1h0z" class="K"></path><path d="M321 147c6 2 15 6 22 5 6 0 12-3 17-6 30-13 55-30 75-57 5-8 10-16 14-25l3-8h8c10 36 39 62 72 80l29 14c3 2 7 4 11 4s8-1 12-2c11-4 23-9 32-14l-35 9-1-1c0-1-2-1-3-1v-1c1 0 3-1 4-1v-3c5-1 9-1 14-1l16-3c14-4 27-14 36-25 4-6 7-12 6-19-2-13-10-19-20-27h165l1 112h-1c-5 1-9 4-13 6-13 9-23 21-30 34-12 23-16 47-17 73-1 34 4 67 8 101 5 35 9 70 9 106 1 26 0 52-2 78l-7 60c-4 38-10 80 2 117 5 16 13 31 26 43 6 5 12 9 19 13 1 0 5 2 5 3 1 1 0 4 0 6v19 92c-3 1-10 0-14 0h-34-76-41c0-2 1-5 2-7 3-4 7-6 10-9 10-8 9-20 3-31-5-7-13-15-21-19-6-2-12-3-18-5-3 0-7-1-11-1v-1h-6-1c0-1 1-2 1-2h3 0l1-1h6c0 1 1 1 2 1 3 0 4 1 7 0l11 4h1c-14-7-30-12-45-15-4 0-7 1-11 3-11 4-22 9-32 15-6 3-13 6-19 10-16 11-31 27-42 44-5 8-8 17-12 26l-11 2c-1-10-6-19-11-27-9-15-22-29-36-39-12-9-25-16-38-23-9-4-20-10-30-12-3-1-6 0-9 1l-15 5c-4 2-9 3-12 5h2c1 1 1 1 3 1v-1c1 0 3 1 4 0 1 0 3 0 4-1l1 1s1 1 2 1l2 2h-4c-1 0-3-1-4 0-1 0 0 1-1 0-2 0-4 0-6 1h-2l1 1h-2c-5 2-10 3-14 5-10 5-19 14-24 24-6 10-3 20 5 27 3 2 7 4 9 7 3 2 4 5 4 8H116l1-120 1-1c20-9 35-28 43-48 10-28 10-58 8-88l-9-89a521.96 521.96 0 0 1-3-79c1-37 5-74 9-110 4-34 9-68 8-101-1-23-4-48-14-70-7-15-17-29-31-39-4-2-8-5-12-6h0c-1-3-1-7-1-10v-22-80l161 1c-8 8-17 13-19 26 0 8 2 13 6 18 10 12 23 23 38 26 6 2 13 2 19 3l16 3v1h-12c-4 1-9 1-13 0h-5 0l14 4z" class="D"></path><path d="M742 419c0 2 0 5-1 6v1l-1-5 2-2z" class="Q"></path><path d="M784 76h3l1 2c-1 1-2 1-3 2 0-2-1-3-1-4zm-658 43c1 0 1 0 2 1s0 1 0 3h-1-1v-4zm659 759h2l1 1v2h-2-1v-3zm-1 36h3v1 2c-1 0-1 0-2-1l-1-2zm1-18h2c1 0 1 1 1 1l-1 2h-2v-3z"></path><path d="M786 869h2v2l-1 1c-1 1-1 0-2 0 0-1 0-2 1-3zm-1 36h1c1 1 1 1 2 3l-1 1h-2v-4zm0-822l2 1c1 1 1 1 0 3l-2 1v-5zM381 805h0c1-1 2-1 3 0v2h-2-2l1-2zM126 100c1 0 1 0 2 1v3h-2v-4zm513 688c1 0 1-1 2 0 0 2-1 2-2 3-1 0-1 0-2-1 0-1 1-1 2-2zm146-676v-3l2-1c1 1 1 1 1 3-1 1-2 1-3 1z"></path><path d="M786 851h2v3h-2c-1-1 0-2 0-3z" class="L"></path><path d="M127 900h1c1 2 1 3 0 4h-1c-1-1-1-3 0-4zm659-774c1 1 2 1 2 3s-1 2-2 3c-1-2 0-4 0-6zm-1-26h2c1 1 1 1 1 3h-1-2v-3zM126 79c1 0 1 0 2 1v3l-1 1-1-1c-1-1 0-2 0-4z"></path><path d="M741 413l1 6-2 2-1-6h1v1l1-2v-1z" class="G"></path><path d="M126 110h2c1 2 0 2 0 4h-1-1v-4zm132 120h3v1l-2 2c-1 0-1 0-2-1v-1l1-1zM126 89h1l1 1c1 2 0 2 0 4h-2v-5zm402 735c1 1 3 2 4 4-2 0-3 1-4 1v-5z"></path><path d="M348 197c2 0 1 0 2 1 0 2 0 2-1 3l-1 1-1-1c0-2 0-3 1-4z" class="L"></path><path d="M252 236h2v1c0 1 0 2-1 3-2 0-2 0-3-1 1-2 1-2 2-3zm210 654h0c1-1 2 0 3 0 0 2 0 2-1 3h-1l-1-1c-1-1-1-1 0-2zm-8-793h2c1 1 1 1 1 2l-1 1c-1 1-2 0-3 0v-1l1-2z"></path><path d="M309 238c0-2 0-4 2-5 1 1 1 1 1 3 0 1-1 2-2 3h-1v-1z" class="L"></path><path d="M463 824h1c2 1 2 1 2 3h-1c-1 0-2 1-3 0v-2l1-1z" class="T"></path><path d="M246 244h1c1 0 1 0 2 1l-3 3h-1l-1-1c1-2 1-2 2-3zm201 645c2-1 2-1 4 0 0 1 0 1-1 3 0 0-1 0-1 1l-2-1v-3z"></path><path d="M256 771h2c1 1 1 1 1 2l-1 2h-1c-1-1-1-1-2-3l1-1z" class="L"></path><path d="M429 128h1c1 0 2 0 2 1l-2 2c-2 0-2 0-4-1 1-1 2-1 3-2zM249 763c2 0 2 0 3 2 0 1 0 1-1 3h-1l-1-2c-1-1-1-1 0-3zm-15-502h2l1 1c-1 1-1 2-3 3-1 0-1 0-2-1 0-1 1-2 2-3z"></path><path d="M240 252h2l1 1c-1 2-2 2-3 3h-2c0-2 1-3 2-4z" class="L"></path><path d="M565 199h3c1 0 1 0 1 1l-2 2c0 1-1 1-1 1-1-1-2-1-2-2l1-2zM243 756h1l2 2-2 2h0c-2-1-2-1-3-3l2-1z"></path><path d="M671 86l2 7c-1 2-1 2-3 3 0-3 0-6 1-10z" class="E"></path><path d="M449 823h2c1 1 1 1 1 3l-1 1h-1c-1 0-2 0-3-1 1-2 1-2 2-3z" class="L"></path><path d="M228 269c1 0 1 0 3 1v1c-1 1-1 2-3 2l-2 1v-1c0-2 1-3 2-4z"></path><path d="M236 748h2c1 1 2 2 2 4l-1 1h-2c0-1-1-1-1-2-1-2 0-2 0-3z" class="T"></path><path d="M674 753l-2-1 1-2c1-1 1-2 2-2s2 1 2 2c-1 1-1 2-2 3h-1z" class="L"></path><path d="M230 661c2 2 3 4 4 6l-1 1-2-1v-1c-1-2-2-3-1-5z" class="V"></path><path d="M270 785c1 0 2 0 3 1 1 0 2 1 2 2l-1 1h-2c-1-1-2-1-2-3v-1z" class="T"></path><path d="M218 358h1c1 0 2 1 3 2l-3 2c-1 1-1 1-2 1v-1c0-2-1-2 1-4z"></path><path d="M264 778s1 0 2 1l2 2-2 2h-1c-1-1-2-2-2-4l1-1z" class="T"></path><path d="M224 277s1 0 1 1l1 1c-1 1-2 2-4 3h-2s0-1 1-2c0-1 1-2 3-3z"></path><path d="M386 801c2 0 7 1 9 2v1c-2-1-6 1-8 0l-1-3z" class="L"></path><path d="M230 651c3 2 4 3 5 6 0 1 0 2-1 3-2-3-3-5-4-9zm510-62c0 1 1 2 1 2 1 3-1 9-1 12h-1v-2-2h0c1-1 1-1 1-2-1 1-1 2-1 3l-1-1 2-10z" class="M"></path><path d="M218 722h2c1 2 1 2 1 4l-2 2h-1c0-1-1-1-1-2-1-2 0-3 1-4z" class="L"></path><path d="M493 138c1 0 1 1 2 1 1 1 2 2 2 4l-1 1c-1 0-2-1-3-2s-2-1-2-3l2-1zM212 714l1-1c1 1 2 2 2 3 0 2-1 2-2 4v-1c-2 0-2-2-3-4l2-1z"></path><path d="M229 740h3c1 1 1 1 2 3l-2 2s-1 0-1 1c-1-2-3-3-3-5l1-1z" class="V"></path><path d="M224 731h1c1 2 2 3 2 5l-1 1h-2c-1-1-1-2-2-3 1-2 1-2 2-3z" class="L"></path><path d="M217 286h2l1 1c-1 2-2 4-4 5h0c-1 0-2 0-2-1 0-2 1-3 3-5z"></path><path d="M581 140c5-1 9-1 14-1-2 0-3 0-4 1-3 1-7 2-10 3v-3z" class="B"></path><path d="M229 668c-1-2-4-4-4-6l1-1c2-1 2-1 4 0-1 2 0 3 1 5v2h-2z" class="T"></path><path d="M674 245v1l3 3c-1 1-1 1-1 2l-1 1c0 1 0 2-1 2h0 0c-1-2-3-3-4-5 1 0 1-1 2-1h0c1-1 2-2 2-3z" class="E"></path><path d="M203 571l2-1c1 1 2 1 3 2v4h-1c-2 0-5 0-6-2 1-1 1-2 2-3z"></path><path d="M248 294c0 2 1 3 2 5l2 1-2 1-1-1c1 1 1 1 1 3l-3 3v-5c-2-1-1 1-3 1 0-1-1-1-2-2l-2-2h0 2c1 1 2 1 4 1 2-1 2-2 2-5z" class="W"></path><path d="M618 764c1-1 1-1 3-2h0c1 1 1 1 2 0l1 1h2 1l-1 2c-1 1-3 3-4 5v-3l-1-1c-2 1-2 1-3 0v-2z" class="I"></path><path d="M621 766c0-1 1-1 2-2 1 0 2 0 3 1-1 1-3 3-4 5v-3l-1-1z"></path><path d="M521 812h0l1 1c2 2 4 2 7 2h2l-3 2-1 1c-1-1-1 0-2 0-2-1-4 0-6-1 0-1 1-1 1-2s0-2 1-3z" class="M"></path><path d="M711 542c0 1 1 2 1 3s0 1 1 2h1v-1l3 2-1 10c0-1-1-2-1-3-1-3-2-5-3-7l-1-3c-1-1-1-2-1-3h1z" class="B"></path><path d="M212 721c1 1 3 1 3 3v1c0 1 1 4 2 5l1 1h-1c1 1 1 1 1 2l3 4h0v1c1 0 1 1 1 2-4-5-8-11-11-16 1-1 1-2 1-3z" class="J"></path><path d="M131 130v15h-4v-1-1l-1-1c0-2 0-3 1-5h1c1 0 2-1 2-1 1-1 1-4 1-6z" class="Q"></path><path d="M451 900l11-1-4 6c0 1-1 3-2 4v-2h0c0-3-3-5-5-7zm20-781c4 3 6 7 10 9 2 2 5 3 6 5v2h-2c-5-3-10-11-14-16z"></path><path d="M226 679l5 6c1 2 1 4 1 6 1 1 1 2 0 3-2-2-4-4-5-6v-3c-1-2-1-4-1-6z" class="C"></path><path d="M208 554h1c0 1 1 1 1 2s0 2 1 3v1c0 1 1 3-1 4h0-2c-1 0-1-1-2-1-1-1 0-3 0-3v-2c1-2 1-3 2-4z" class="M"></path><path d="M692 644c-1 2-3 3-4 5-3 3-6 6-10 9l2-5c1-1 2-2 3-4 2-2 5-4 9-5z" class="G"></path><path d="M227 314v-1c0-2 2-4 3-6 2-3 4-9 7-11l3-3v-1l1 1c-1 1-1 2-2 3l-1 1-11 17z" class="C"></path><path d="M204 486c2 0 3 0 4 2s2 6 2 8v9h0c-1-4-2-12-5-14l-2-2v-2h-1v1-2h2z" class="L"></path><path d="M126 171c3 0 3-2 5-3 0 1 0 2 1 2 0 1 2 2 3 3 3 2 6 5 9 8-1-1-3-1-4-2h0 0c1 1 1 2 2 3-5-4-10-9-15-10h-1v-1z" class="J"></path><path d="M682 738c1 0 2-1 3-2 1 0 1 0 2 1l-2 3c-1-1-1-1-1-2h-1v1c1 1 1 1 1 2s-1 2-2 3h-1-1-2c0 1-1 2 0 3l-1 1h0c1 1 0 2 0 3s-1 2-1 3c-1-1-2-1-2-1h1c1-1 1-2 2-3 0-1-1-2-2-2l2-1c0-1-1-2 0-3l1-2h0l2-4h2z" class="C"></path><path d="M680 738h2c1 1 1 2 1 3-1 1-1 2-2 2-2 0-2 0-3-1l2-4z"></path><path d="M186 534l1-1c1 4 1 9 4 12l1 6v1 1l-2-4-1-2v-1h0 0l-1-2c0 2 1 3 1 5v1c0 2 1 9 0 11l-3-27z" class="I"></path><path d="M681 661l15-11c0 3-1 6-3 10l-2-1c-3 1-7 2-10 2z"></path><path d="M204 624h1v-4c1 1 2 1 3 2-2 8-3 15-3 23l-3-15 2-2v-4z" class="L"></path><path d="M781 835l1-5c1 0 1-1 1-1 0-3-1-6 1-8l3 1c-1 1-1 1-1 3l2 1v2c-1 0-2 1-3 1-1 1-1 2-1 4 0 1 0 0-1 2v3h0l-1-1c-1 2 0 6-1 9v-11z" class="I"></path><path d="M285 754h1l2 2 1 1h3c1-1 1-1 3 0h0c0 2 0 2-1 3l-1-1c-1 0-2 0-2 1l-2 1h0v1c-2 0-3 0-4-1l-1-1c-1-1-1-2-2-3 1-2 1-2 3-3z" class="F"></path><path d="M285 754c1 1 2 1 2 3 0 1-1 2-3 3h0c-1-1-1-2-2-3 1-2 1-2 3-3zm-57-49l9 9 14 11c2 1 4 3 5 5l-1-1c-6-4-12-7-17-12-4-3-7-8-10-12z"></path><path d="M516 830c2-2 4-5 7-7 0 1 1 1 1 1h1l1-1s1 0 2 1h0v5c-1 1-2 2-3 2-2 0-3-1-5-1-1-1-3 0-4 0z" class="I"></path><path d="M173 401l2-3v1l-2 19c-1 5-1 10-3 15 0-1 0-2 1-3h0c-1-2-1-2-1-4v3c-1-3 0-6 0-8l3-20z" class="C"></path><path d="M464 821c0-2 0-2 1-4 2-1 3 0 5 0h1c3 3 5 6 9 7 2 0 4 0 6 1-4 0-8 0-12-1-3-1-6-2-10-3z"></path><path d="M285 742c6 3 12 8 17 12l2 2 2 4h0c-3-1-6-4-9-5-3-3-9-5-12-8h0 1l1 1h1l2 2h2c0 1 1 0 2 1v1l1-1v-1c-3-1-5-4-8-5-1-1-1-2-2-3z" class="C"></path><path d="M681 670l2 1h1c0 1 1 1 2 2l1-1h0c-2 6-3 13-8 18 1-3 2-6 2-9 0-2 0-4-1-7 1-1 1-2 1-4z" class="N"></path><path d="M683 671h1c0 1 1 1 2 2v1c-1 1-1 2-2 3-1-2-1-4-1-6z" class="I"></path><path d="M424 818v2h0c1 2 1 2 2 3 1 0 2 0 2 1v2l1 1-28-7c3-1 5 0 8 1l10 2h0v-3h0c2 0 3 0 5-2z" class="J"></path><path d="M668 886c2 2 6 4 6 8 0 5-3 12-7 15l-4 5c-2 1-4 2-5 3h-1c1-1 3-3 5-4 3-5 7-8 9-13v-1-2c1-2 0-5-1-7-1-1-1-2-2-3v-1z" class="B"></path><path d="M212 491c1-3 1-7 1-10 2 5 2 10 2 16s0 14-1 20l-1-8c1-1 0-12 0-14v1 1l-1 1-1-1h0v1 4 2c0 1 1 1 1 2v1 1h-1-1v1l-1-1 1-3h0v-9c1-1 1-3 2-5z" class="J"></path><path d="M536 175c5 2 14 17 20 14 1 0 1-1 2-2h0c2 1 3 2 4 4-1 3-4 3-6 5-3-3-6-7-9-10-4-4-8-7-11-11zm-89-69c2 0 3 0 4 1-1 3-3 5-4 7l-11 11-1 1-1-1v-1c1-3 5-6 8-9 2-3 3-6 5-9z"></path><path d="M241 891c1 3 1 6 2 9 3 8 10 12 16 17l-2-1h-1c-2-1-4-1-7-1-5 0-10 1-15 0 6-1 12 0 18 0-4-4-10-9-12-14-1-3-1-7 1-10z" class="B"></path><path d="M618 762c3-2 5-3 8-3h3 1l-1 1h-1-1-3-1c-2 1-3 2-5 3v1 2c1 1 1 1 3 0l1 1v3l-2 1h0l-7 5-3 1-2-1v-1c0-1 0-2 1-3 1 0 2-1 3-2l2-1c0-1 1-1 1-2 1-1 2-2 2-3s0-1 1-1v-1z" class="M"></path><path d="M613 776c-1-2-1-3 0-4l2-1c2-1 3-1 5 0l-7 5z" class="E"></path><path d="M637 732v1c2-1 4-4 6-5h1l1 1h1c1-1 3-1 4-1-9 5-15 14-24 17 1-4 5-6 7-10h0l2-2 2-1z" class="K"></path><path d="M226 804c3-1 7 1 10 2 5 2 10 7 14 11 2 3 4 6 5 9 1 1 2 3 2 4-2-3-4-6-7-9-4-5-11-9-17-13-2-1-4-3-7-4zm-42-283v-3s0 3 1 3l1 13 3 27-1 1h-1c-2-4-2-10-2-15-1-9-1-17-1-26z"></path><path d="M670 249h0c-1 0-1-1-2-2l-5-6c-1-2-2-4-3-5v-1l-3-3 1-1v-2c2 1 2 1 4 3l-1 1h0c0 2 1 4 2 5 1-1 1-1 2-1v-1h1l4 5 2 2c1 1 2 1 2 2s-1 2-2 3h0c-1 0-1 1-2 1z" class="I"></path><path d="M730 354l1-1v-1 1c0 2 0 4 1 6h0c1-1 0-3 0-4v1l-1 1v-2h1v-5h0l6 45c-1-2-1-5-2-7h-1c-1-3-1-7-2-10l-3-24z" class="B"></path><path d="M730 227v1c0 2-3 6-2 8h1v-1-1c1 0 1-1 2-2 1 0 1 1 2 1v1c-1 1-1 2-2 3h-2l-1 1c1 1 2 1 3 2l1 1v2h0c-1 1-1 1-1 2h-1c0 1-1 2-2 2-1 2-1 3-1 4 1 1 1 0 2 0l1 2-1 1c-1 1-2 1-3 1h0c-1-1-1-3-1-5 0-1-1-2 0-3v-4c0-5 2-11 5-16z" class="H"></path><path d="M321 139l16 3v1h-12c-4 1-9 1-13 0h-5 0-2-1c-2-1-2-1-3-2h0 1 0 2 1l1-1h1 1c4 1 10 1 13 0h1 1c0-1-1-1-2-1z" class="M"></path><path d="M590 765c4-2 7-5 11-6l23-12c0 1 0 1-1 2l-2 1-5 2-5 3-1 1h-2l-1 1-5 3c-1 0-2 1-3 2 0 1-1 1-1 2 2 0 2-2 4-2l2-1c1-1 4-2 5-2v-1h1c1 0 1-1 2-1h1c1-1 1-2 2-2h1l1-1h1l1-1s1 0 2-1h0 1c1-1 0 0 1 0 1-1 2-1 3-1l-26 13-2 1h2l-2 1v1c2 1 2 2 4 3 0-1-1-2-1-2 1-1 0-1 1 0l1 2-2 2v-1l-1-1c-1-2-2-2-4-3s-4-1-6-1v-1z" class="I"></path><path d="M369 173c2 0 2 0 3 1v2c-3 4-8 7-12 11 0 2-2 4-4 6-1 0-5-5-6-6l2-3 10-7-6 9 2-1c4-2 11-6 13-10-1-1-2-1-3-2h1z"></path><path d="M127 145h4v16c0 2 1 5 0 7-2 1-2 3-5 3 1-2 1-4 1-6 0-6-1-15 0-20z" class="G"></path><path d="M673 761c-2 1-3 2-4 3h-1 0c0-1-1-1-1-2h-1v3h-1 0v1c-1 0-1 1-2 2h-3-1 0l-1 1c-1 0-2 0-2 1l2 2v2c-1 0-2 1-3 1v1c-1 0-2 0-3 1-1-1-1-1-1-2 1-2 3-4 4-6 1-1 1-1 2-1 1-1 1-1 2-1 0-2 0-3 1-4l1-1h1 2 1 0l-1-1c0-1 0-1 1-2 0-1 1-2 1-4 1-1 2-1 3-1 1 1 1 1 2 3 0-1 0-2 1-2l2-1c1 1 2 1 3 1-2 0-3 1-5 0v1 1c1 1 1 2 1 4z" class="Q"></path><path d="M668 755h1l1 2c0 2-1 2-2 2h-2c0-2 1-3 2-4z" class="L"></path><path d="M662 763h1l1 1c0 2-1 2-2 3h-1-1c0-2 1-3 2-4z"></path><path d="M277 243c0 2-1 3-2 5-1 0-2 1-2 2l-1 1-8 10c-1 1 0 1-1 2-1 0-1 1-2 2 0 1 0 0-1 1s-1 2-1 3v1c3-1 8-7 10-9v-1c-1 0-1 0-2 1s1 0-1 1l-1 1h-1c1-2 1-2 3-3l4-4c1-1 2-1 2-1 2-1 3-2 4-3 4-3 7-6 11-10v1c1 0 1 1 1 2l-14 12c-5 4-8 8-13 12-1 1-4 2-5 3s-1 1-1 2c-1-1-1-2-1-3l1-1h-1c3-3 6-6 8-9l14-18z" class="C"></path><defs><linearGradient id="I" x1="659.262" y1="723.786" x2="661.8" y2="706.798" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#343132"></stop></linearGradient></defs><path fill="url(#I)" d="M664 709l3-1h0v1h1c-4 7-10 13-16 18h-1l-1 1c-1 0-3 0-4 1 0-1-1-1-1-2s1-1 2-2c0-2 4-5 5-6l5-4 2-2c2-1 3-2 5-4z"></path><path d="M657 715l-1 2c-1 2-3 3-4 4l-1 1h1 1 0v1c-1 1-1 2-1 4h-1l-1 1c-1 0-3 0-4 1 0-1-1-1-1-2s1-1 2-2c0-2 4-5 5-6l5-4z"></path><path d="M647 725c1 1 2 2 4 2l-1 1c-1 0-3 0-4 1 0-1-1-1-1-2s1-1 2-2z" class="G"></path><path d="M561 785c3-2 6-6 10-8h3v1h0 1 1l-1 1s-1 1-1 2h-1c-1 0-2 1-3 2l-3 2-6 6h0c-3 1-5 3-6 5l-3 3c-4 4-8 8-12 13v-3c0-2 2-2 3-3 1 0 2-2 3-3l10-10 2-2c-5 0-8 7-12 9l-2 2-1 1c-2 1 0-1-1 1-1 1-2 1-3 1l16-15 6-5z" class="I"></path><path d="M561 785c3-2 6-6 10-8h3v1l-6 3c-5 2-6 9-13 9l6-5z" class="G"></path><path d="M202 630c0-1 0-1-1-1-2-2-2-6-3-9-1-4-3-8-5-12-2-10-3-21-4-31h0l2 4c0 3 2 6 2 10l4 19c2 6 4 13 5 20z" class="B"></path><path d="M752 793c2 1 4 4 6 5h1c-1-1-3-4-5-5 0-1 0-2-1-3-1 0-1-1-1-2 3 4 6 7 9 10s7 6 10 9 5 5 8 7 8 2 10 4c-1 1-1 2-1 3l-1 1-3-1c-2 2-1 5-1 8 0 0 0 1-1 1l-1 5v-17h-1v3c-1 0-1-3-1-4-2-4-9-8-13-10-1-1-3-2-4-3-1-2-3-3-4-5l-6-6zM184 521c-1-4 0-9-1-13v-5c-1-1-1-2 0-3 1 1 2 7 2 9v1c1 1 1 1 2 1 1 1 2 0 3-1 0 2 0 3 1 4l-2 1c1 2 0 3 1 4v2c-1 2 0 3-1 5v4l1-1c0 6 1 11 1 16-3-3-3-8-4-12l-1 1-1-13c-1 0-1-3-1-3v3z" class="C"></path><path d="M185 521h3l-1 12-1 1-1-13z" class="E"></path><path d="M210 509v-1h1c-1 6-2 11-3 16s-3 10-3 15c-2 7-1 14-3 20-1 3-3 6-5 7-1 1-1 1-2 1-4-4-2-9-3-14v-1c2 2 3 3 6 4 1 0 1-1 1-2 3-6 4-13 5-19l6-24v-2zm38-215c0-1 0-1-1-2-2-1-3-1-4-1h-1v-1c2-2 5-4 7-4s2 0 3 2 1 4 1 7c0 1-1 1 0 2 2 1 7 0 8 2-1 4-6 9-9 13l-1-2 3-3c-2-2-2-2-3-4l1-1-2-1 2-1-2-1c-1-2-2-3-2-5z"></path><path d="M252 300h1c2 0 4-1 5 0 0 2-3 5-4 7-2-2-2-2-3-4l1-1-2-1 2-1z" class="N"></path><path d="M530 164l-23-12c-3-2-6-3-8-6v-2h1l21 12c9 5 20 10 28 16h-1-2 0-1l-7-4-8-4z"></path><path d="M227 326h1c1-1 2-1 3-2s1-1 2-1h1l-2 2 1 1c-1 1-4 2-4 3-3 3-5 6-7 10-1 2-2 5-3 6 0 1-1 4 0 5h3-5c-1 1-3 2-3 3l-1-1c0-4 1-12 4-16 2-5 5-8 10-10z" class="B"></path><path d="M219 345h-1c-1 1-1 2-1 3h-1 0c0-3 2-7 3-10l3 1c-1 2-2 5-3 6z" class="K"></path><path d="M227 326h1c1-1 2-1 3-2s1-1 2-1h1l-2 2 1 1c-1 1-4 2-4 3-3 3-5 6-7 10l-3-1c2-5 5-8 10-11-6 2-8 4-11 9h-1c2-5 5-8 10-10z" class="W"></path><path d="M782 76h2c0 1 1 2 1 4l-1-1c-1 2-1 3 0 4h1v5c-1 1-1 1-1 3 1 1 1 0 2 1h1l1 1c-1 1 0 1-1 2h-1c-1 1-1 1-2 1 0 1-1 2-1 3l1 1h1v3h2-1c0 1-1 1-2 1 0 1-1 1-1 3l1 1h3l-2 1v3l-1 1c0 1 0 3 1 4h1l2 1c0 2 0 3-1 4l-3 1c-1-1 0-1-1-1v-1c-2-4-1-10-1-14l-1-1 1-30z" class="E"></path><path d="M787 92l1 1c-1 1 0 1-1 2h-2c-1-1-1-1 0-3h2zm-1 25l2 1c0 2 0 3-1 4-1 0-1-1-2-1 0-2 0-2 1-4z"></path><defs><linearGradient id="J" x1="682.801" y1="683.256" x2="660.509" y2="692.411" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2b2b"></stop></linearGradient></defs><path fill="url(#J)" d="M674 669l1-1c1 0 2 0 3 1 1 2 2 4 2 5 1 3 1 5 1 7 0 3-1 6-2 9-3 6-7 14-11 19h-1v-1h0l-3 1 3-4c4-6 7-12 10-19 0-2 1-4 1-6h-2s-1-1-2-1c-1-3 0-7 0-10z"></path><path d="M255 270c-2 2-4 5-7 8h0c8-14 19-27 29-39 6-7 12-14 19-20-3 4-5 7-7 11l-12 13-14 18c-2 3-5 6-8 9z" class="L"></path><path d="M726 698l2-14h0 0v-2l2 1-2 5 2 2v3c-1 0-2 1-2 1l1 5h0c1 1 1 2 1 3-1 1-1 1 0 3v1 2 4c-1 0-1 0-2 1l1 1 1 1v1 2h0c1 1 1 3 1 4s0 1-1 2c0 1 0 2 1 3v1h0c1 2 2 3 1 5-1 0-1 1-2 1-3-3-2-6-3-10-2-6-2-13-2-19l1-7z" class="I"></path><path d="M732 733l-1-1c-1-1-1-1-1-2l1-2c1 2 2 3 1 5zm-1-11h-2l-1-1c1-2 1-2 2-3 1 1 1 3 1 4z"></path><path d="M730 702c-1 1-1 1 0 3v1 2h-3c0-2 0-4 1-5 0 0 1 0 2-1h0z" class="F"></path><path d="M250 817h2l1 1 4 3c4 5 7 10 8 17 0 2 1 4 1 7h-1c-1 1-1 3-1 4 0 2 0 3 1 5v7l-2 1v-6c0-5-1-11-4-16-1-3-3-6-5-9s-4-6-7-9h0v-1c2 1 3 2 4 4 3 3 5 9 9 12l-3-6v-1c0-1-1-3-2-4-1-3-3-6-5-9z" class="S"></path><path d="M253 818l4 3c4 5 7 10 8 17 0 2 1 4 1 7h-1c0-9-7-19-12-27z"></path><path d="M691 659l2 1-6 12h0l-1 1c-1-1-2-1-2-2h-1l-2-1c0 2 0 3-1 4 0-1-1-3-2-5-1-1-2-1-3-1l-1 1-1-1-1-1-7 1 1-1 6-6 3-1c0 1-1 2-1 3h1 0 3l2-2h1c3 0 7-1 10-2z" class="G"></path><path d="M672 667c1 0 2 0 3-1 2-1 3 0 5 0 1 1 2 2 3 1l1 1c-1 1-1 1 0 3h-1l-2-1c0 2 0 3-1 4 0-1-1-3-2-5-1-1-2-1-3-1l-1 1-1-1-1-1z" class="B"></path><path d="M680 666c1 1 2 2 3 1l1 1c-1 1-1 1 0 3h-1l-2-1c0-1-1-2-1-4z" class="H"></path><path d="M691 659l2 1-6 12h0l-1 1c-1-1-2-1-2-2-1-2-1-2 0-3l-1-1-2-2c2-1 5-1 7-2h1c1 0 1-1 2-2s1-1 0-2z" class="V"></path><path d="M684 668l3 3v1l-1 1c-1-1-2-1-2-2-1-2-1-2 0-3z" class="C"></path><path d="M530 164l8 4 7 4c0 1 2 2 2 2l-1 1c2 2 3 3 5 4 1 1 2 1 2 2h1l1 1 2 2h0-1l-1 1h1l2 2c-1 1-1 2-2 2-6 3-15-12-20-14l-1-2-1-2c-1-3-3-4-4-7z" class="B"></path><path d="M530 164l8 4c-1 1-3 1-3 2l3 2-1 1-1-1-1 1-1-2c-1-3-3-4-4-7z" class="H"></path><defs><linearGradient id="K" x1="467.669" y1="817.864" x2="441.183" y2="808.557" xlink:href="#B"><stop offset="0" stop-color="#9b9898"></stop><stop offset="1" stop-color="#cac8c7"></stop></linearGradient></defs><path fill="url(#K)" d="M437 809c2 1 4 1 7 1l19 2 3 1h1c1 1 2 1 3 1v3c-2 0-3-1-5 0-1 2-1 2-1 4-1 0-1-1-2-1l-1 1h-1v-1l-4 1h-1c-2 0-4-1-6-3 0 1 0 1-1 2-1-1-1-2-2-3-2-3-6-5-9-8z"></path><path d="M449 818c-1-1-1-1-1-2 3-1 8-1 11 2h0c0 1-2 2-3 3h-1c-2 0-4-1-6-3zM216 609v2l-2 12 1 1v1c0 1 0 2 1 4 2 1 2 2 4 2 2-1 3-2 4-4 1 2 1 4 1 6-2 2-4 4-7 5h0c-2 0-2 0-4-1-1 2 0 4 0 6v3c1 4 2 7 4 10 1 0 1-1 2-1 0 2 0 7-1 9l-2-2c-10-17-5-36-1-53z"></path><path d="M214 623l1 1v1c0 1 0 2 1 4h-3c0-2 0-4 1-6z" class="I"></path><path d="M224 627c1 2 1 4 1 6-2 2-4 4-7 5-2-1-3-2-5-3-1-2 0-4 0-6h3c2 1 2 2 4 2 2-1 3-2 4-4z" class="F"></path><path d="M681 249c5 5 9 11 13 17-1 2-3 2-4 5 0 1 1 4 2 5s1 2 2 3v1h0c1 1 1 2 1 2l3 3v1c1 2 1 3 2 5 1 1 2 3 2 5v2c-5-5-7-11-10-16l-10-16-7-10v-1l2 2h1c1-2 1-2 1-3l-1-1 1-1c1 0 2 1 3 1 1 2 2 3 2 5 0 1-1 2-2 2v1h-1l2 3v-1l2-1v-1h0 2c0 1 0 0 1 1s2 2 2 4c-1 1-1 1-2 1l-1 1c0 1 0 1 1 2v-1l1 1 1-1c0-1 1-2 2-2h0c0-2 0-2-1-3 0-1-1-2-1-2-1-1-2-3-3-4s0 0-1-2h0c-2-2 0 1-1-1l-1-2-1-1c-1-1-1-2-2-3z" class="H"></path><path d="M680 254h1c1 1 2 2 2 4l-1 1h-1c-1 0-1-1-2-2 0-1 1-2 1-3zm5 9h2c1 1 2 1 2 2l-2 1-1 1c-1-1-1-2-2-3l1-1zM421 135c1-1 1-1 2 0s1 1 1 3c-2 2-6 4-9 6-6 4-13 9-20 12l-29 14c-9 5-18 11-27 16l-2 1h0c17-13 37-23 55-34 10-5 20-11 29-18z"></path><path d="M224 627c-1-1-1-1 0-2v-1l7 23v1c-1 0-1 1-3 1v1c-2 0-5 0-7 1-1 1-1 2-1 4-1 0-1 1-2 1-2-3-3-6-4-10v-3c0-2-1-4 0-6 2 1 2 1 4 1h0c3-1 5-3 7-5 0-2 0-4-1-6z" class="O"></path><path d="M216 644c1-1 4-2 6-2s3 0 4 2c-3 3-8 1-9 6h0l-1-6z" class="V"></path><path d="M218 656v-2-2c0-1 1-3 3-4 3-2 7-2 10-1v1c-1 0-1 1-3 1v1c-2 0-5 0-7 1-1 1-1 2-1 4-1 0-1 1-2 1z" class="L"></path><path d="M218 638c3-1 5-3 7-5 0 3 0 4 1 6s1 3 1 5h-1c-1-2-2-2-4-2s-5 1-6 2l-2 2v-3c0-2-1-4 0-6 2 1 2 1 4 1h0z" class="I"></path><path d="M673 668l1 1c0 3-1 7 0 10 1 0 2 1 2 1h2c0 2-1 4-1 6-3 7-6 13-10 19-2 0-3 1-5 1 2-5 2-8 0-12h1v-1h-1c1-6 5-11 7-17 1-3 3-6 4-8z" class="M"></path><path d="M738 599l1 1c0-1 0-2 1-3 0 1 0 1-1 2h0v2 2l-8 64c-1 4-2 10-1 14v2l-2-1v2h0 0l-2 14c-1-3-1-7-1-10h-1v-2c1-2 1-5 0-8l3-15 6-36c2-9 3-19 5-28z" class="C"></path><path d="M626 751l23-15-2 3c-2 3-4 5-7 8-2 3-4 5-7 8-1 1-2 3-3 4h-1-3c-3 0-5 1-8 3v1c-1 0-1 0-1 1s-1 2-2 3c0 1-1 1-1 2l-2 1c-1 1-2 2-3 2l2-5c2-1 3-3 5-5-2 1-4 3-6 3-3 1-6 2-8 3-1-1 0-1-1 0 0 0 1 1 1 2-2-1-2-2-4-3v-1l2-1h-2l2-1 26-13z"></path><path d="M624 755c1 0 5-2 5-2l11-6c-2 3-4 5-7 8-1 1-2 3-3 4h-1-3c-3 0-5 1-8 3v-1c1-1 3-2 5-3 0 0 1-1 2-1l-1-2z" class="O"></path><path d="M600 764h0c3 0 6-1 9-3l15-6 1 2c-1 0-2 1-2 1-2 1-4 2-5 3v1 1c-1 0-1 0-1 1s-1 2-2 3c0 1-1 1-1 2l-2 1c-1 1-2 2-3 2l2-5c2-1 3-3 5-5-2 1-4 3-6 3l-8 3c-1-1 0-1-1 0 0 0 1 1 1 2-2-1-2-2-4-3v-1l2-1h-2l2-1z" class="C"></path><path d="M521 801h0l28-13c3-1 6-3 9-4 1 0 2 0 3 1l-6 5-16 15s-7 4-9 4c-4 1-4-2-6-5l-3-3h0z"></path><path d="M546 793h1l1 1-4 3c-3 4-9 9-14 9-1-1-1-3-1-5 1-1 3-2 4-3 5-2 9-4 13-5z" class="J"></path><path d="M326 201c2-3 8-6 11-7-1 3-3 5-6 7-1 1-2 3-2 4v1c-3 2-6 5-9 7-1 1-3 2-3 3-1 1-2 2-2 3v3l3 2 4 4 1 1c-1 1-2 1-2 2l1 3-1 2-1-1v-1c-1-4-3-7-7-8-1-1-4-2-6-1-1 0-2 2-3 3s-1 2-2 2l-2-1c-1-1-1-3 0-4 1-7 20-20 26-24z"></path><path d="M326 201c2-3 8-6 11-7-1 3-3 5-6 7-1 1-2 3-2 4v1c-3 2-6 5-9 7-1 1-3 2-3 3-1 1-2 2-2 3v3c-2-1-4-1-7-1 7-5 14-11 18-19v-1z" class="N"></path><path d="M289 245c3-3 5-6 8-8 3 1 5 2 8 3 2 0 2-1 4-2v1c-4 3-8 5-12 8-2 2-5 4-7 7l-15 11c-2 2-5 4-6 6-3 2-5 3-7 5l-2 1c-3-1-3-1-4-3 0-1 0-1 1-2s4-2 5-3c5-4 8-8 13-12l14-12z"></path><path d="M726 515h0l1-1c0 3 0 4-1 6v7 2c-1 1 0 3 0 4 0 2-1 4-1 5s0 2-1 3v4l-3 16v1l-1-2h1v-1c0-1 0-2 1-3h-1v-2-2-1h0v1c0 1 0 1-1 2v3 1h0c0 1-1 2-1 3v1l-1 2-1 1-1-6v-1l1-10-3-2v1h-1c-1-1-1-1-1-2s-1-2-1-3h-1c0-1-1-1-1-1v-8c2-3 5-3 9-4l2-2c0-1 0-2 1-3v-3-1c1-2 3-3 5-5z" class="G"></path><path d="M721 520c1-2 3-3 5-5l-1 1c0 2 0 3-1 4h-3z" class="M"></path><path d="M720 537l1 1c0 4 0 8-1 12-1 5-2 9-2 14l-1 1-1-6v-1l1-10-3-2v-2l1-1v-2-1c2 0 4-2 5-3z"></path><path d="M720 537l1 1c-1 2-1 3-2 4l-4 1v-2-1c2 0 4-2 5-3z" class="H"></path><path d="M718 529c1 0 2 1 2 2h0-2l-1 1c2 0 2 0 3 1v4c-1 1-3 3-5 3v1 2l-1 1v2 1h-1c-1-1-1-1-1-2s-1-2-1-3h-1c0-1-1-1-1-1v-8c2-3 5-3 9-4z"></path><path d="M711 542l-1-2c1-1 1-2 2-3h3l1 1-1 2v1 2l-1 1v2 1h-1c-1-1-1-1-1-2s-1-2-1-3z" class="E"></path><path d="M725 243v4c-1 1 0 2 0 3 0 2 0 4 1 5l-1 5h1 2c1 1 1 2 1 3-1 1-3 1-3 2 2 2 2 3 2 5-1 16 0 31 1 47l3 33h0v5h-1v2l1-1v-1c0 1 1 3 0 4h0c-1-2-1-4-1-6v-1 1l-1 1-4-32c-2-20-5-39-4-59 0-7 1-13 3-20z" class="C"></path><path d="M726 265c2 2 2 3 2 5-1 0-1 0-2-1v-4z" class="M"></path><path d="M227 314l11-17 2 1h0l2 2c1 1 2 1 2 2 2 0 1-2 3-1v5l3-3c0-2 0-2-1-3l1 1 2 1-1 1c1 2 1 2 3 4l-3 3-12 10h0c-1 1-2 2-4 3 0 1-1 2-2 3l-1-1 2-2h-1c-1 0-1 0-2 1s-2 1-3 2h-1l-1-1-5 2 1-2c1-4 3-7 5-11z"></path><path d="M227 322l-1 1h-1l6-11v4l1 1-5 5z" class="E"></path><path d="M251 303c1 2 1 2 3 4l-3 3-12 10h0c-1 1-2 2-4 3 0 1-1 2-2 3l-1-1 2-2h-1c-1 0-1 0-2 1s-2 1-3 2h-1l-1-1c3-2 7-3 10-5 7-3 12-10 15-17z" class="X"></path><path d="M231 312l8-14h1l2 2h0v3c-2 5-6 10-10 14l-1-1v-4z" class="D"></path><path d="M242 300c1 1 2 1 2 2 2 0 1-2 3-1v5c-5 8-11 13-20 16l5-5c4-4 8-9 10-14v-3h0z" class="K"></path><path d="M724 726v-1l1 1c0-7-1-14 0-21 0 6 0 13 2 19 1 4 0 7 3 10l2 5h2c1 0 2 1 1 2v1 2c0 1-1 1-1 1 1 2 1 3 3 4v1l1 1s0 1-1 2h-1c0 1 1 2 1 3h1c2 1 2 2 3 4h0c1 1 1 2 1 4v2l1 1c0 1 1 2 1 3s-1 1 0 2v1c2 2 2 4 3 6 2 3 4 6 5 9 0 1 0 2 1 2 1 1 1 2 1 3 2 1 4 4 5 5h-1c-2-1-4-4-6-5h0c-6-7-11-14-15-22-8-14-11-30-13-45z" class="H"></path><path d="M738 756c2 1 2 2 3 4h0-2c-1-1-1-2-2-3l1-1zm-118 15h0c-4 4-8 9-12 12-5 4-9 7-14 10l-17 12-16 9c-7 4-15 9-22 10-2 0-2 0-4-1 0-1-1-2 0-4 0-2 3-5 5-7 4-5 8-9 12-13h0c-3 5-7 8-10 13-1 1-2 2-2 4 2 1 5 0 7-1l7-3c4-1 8-4 11-6l16-9c5-2 10-6 14-9l15-11 3-1 7-5z"></path><path d="M570 783c1-1 2-2 3-2 0 2-2 5-1 7 3 3 6 4 9 4-1 2-2 2-4 3 1 2 2 2 4 2l-16 9c-3 2-7 5-11 6l-7 3c-2 1-5 2-7 1 0-2 1-3 2-4 3-5 7-8 10-13h0l3-3c1-2 3-4 6-5h0l6-6 3-2z" class="B"></path><path d="M556 801c1-2 3-4 4-6l1 1-1 2h1 0c1 2 2 4 1 6-1 1-1 2-2 2l-1-7c-1 2-2 4-2 6-1 2-1 4-3 6 0-4 1-7 2-10zm14-18c1-1 2-2 3-2 0 2-2 5-1 7 3 3 6 4 9 4-1 2-2 2-4 3h-1l-9 4c0 1-1 1-2 2h-1c-1-2 0-5 0-7 1-2 3-5 5-6l1 1-4 5h0c2-1 4-3 4-4 1-2 0-4 1-5v-1l-1-1z"></path><path d="M567 799c1-1 1-4 3-5 2 0 5 1 6 1l-9 4z" class="V"></path><path d="M552 799l3-3c1-2 3-4 6-5l-3 3c-1 1 0 0 0 2l-3 3-3 4h-1c0 1-1 2-1 3l-2 3v2c-1 1-1 2-2 3h1c2-1 2-3 3-5 0-1 0-2 1-2v-1c1-2 2-3 3-4l1-1h1c-1 3-2 6-2 10v1l-7 3c-2 1-5 2-7 1 0-2 1-3 2-4 3-5 7-8 10-13h0z" class="C"></path><path d="M400 819c-3-1-10-2-11-4 0-2 0-4 1-5 3-3 12-2 16-2 10 0 27 1 33 8 1 1 2 2 2 4 0 1-1 3-3 4-1 1-4 2-6 3h-3l-1-1v-2c0-1-1-1-2-1-1-1-1-1-2-3h0v-2c-2 2-3 2-5 2h0v3h0l-10-2c-3-1-5-2-8-1l-1-1z"></path><path d="M397 817l-1-1c0-1-1-1 0-2 0 0 1-1 2-1 2 0 4 2 6 3h-6l-1 1z" class="H"></path><path d="M404 816c1 1 2 2 3 2s2 1 3 2c0 1 0 0-1 1-3-1-5-2-8-1l-1-1-3-2 1-1h6z" class="M"></path><defs><linearGradient id="L" x1="433.617" y1="819.816" x2="426.68" y2="822.058" xlink:href="#B"><stop offset="0" stop-color="#807e7d"></stop><stop offset="1" stop-color="#9a9897"></stop></linearGradient></defs><path fill="url(#L)" d="M426 818l1 2h2c1-2 1-2 1-4 1 0 2 1 3 1s1 0 2 1c1 0 2 1 2 3h0l1 1v2h0c-1 1-4 2-6 3h-3l-1-1v-2c0-1-1-1-2-1-1-1-1-1-2-3l2-2z"></path><path d="M428 824h4v3h-3l-1-1v-2z" class="N"></path><path d="M407 818v-1l-2-1c0-1 2-3 4-3 4-3 10-1 14 0 1 1 2 1 3 2v3l-2 2h0v-2c-2 2-3 2-5 2h0v3h0l-10-2c1-1 1 0 1-1-1-1-2-2-3-2z" class="H"></path><path d="M419 820c-1 0-1-1-2-1-1-1-3-2-4-3l1-1c1-1 2-1 4-1 3 1 4 2 6 4-2 2-3 2-5 2h0zM191 581c3 0 7 0 10-2 3 1 5 2 7 4 2 3 2 6 2 9 1 7 2 13 1 20l-3 10c-1-1-2-1-3-2v4h-1v4l-2 2c-1-7-3-14-5-20l-4-19c0-4-2-7-2-10z"></path><path d="M199 603l-5-17h2c2 0 3 1 4 4h0c-1 3 0 5 0 8l1 2c0 1 0 3-1 4v1h0c-1-1-1-1-1-2z" class="M"></path><path d="M206 592c2 3 3 6 3 9 0 7-2 13-4 19v4h-1v-3-6l1-2v-1c1-6 1-14 1-20z" class="H"></path><path d="M200 590c2 0 4 0 6 2 0 6 0 14-1 20v1l-1 2v6l-5-18c0 1 0 1 1 2h0v-1c1-1 1-3 1-4l-1-2c0-3-1-5 0-8h0z" class="D"></path><path d="M199 603c0 1 0 1 1 2h0v-1c1-1 1-3 1-4l-1-2c0-3-1-5 0-8l2 9c0 4 0 13 2 16v6l-5-18z" class="I"></path><defs><linearGradient id="M" x1="776.957" y1="190.175" x2="749.053" y2="173.805" xlink:href="#B"><stop offset="0" stop-color="#a8a6a5"></stop><stop offset="1" stop-color="#d4d3d2"></stop></linearGradient></defs><path fill="url(#M)" d="M781 106l1 1c0 4-1 10 1 14v1c1 0 0 0 1 1 0 1 0 2 2 3 0 2-1 4 0 6v1c0 1 0 1 1 2v1l-1 2c1 0 1 0 1 1s0 3 1 5l-1 1h-1c0 1-1 2 0 4l1 1s0 1 1 1c-1 1-1 1-1 2l-1-1c-1 2 1 4 1 6 0 3 0 7 1 10 1 1 1 3 2 4h-1c-2 0-3 0-5 1-4 1-7 3-10 5-6 5-12 10-17 16-5 5-8 10-12 16-2 3-4 6-5 10l-2 3h-1l-2 2c0 1 1 2 1 2 0 1-1 2-1 2-1 2-2 3-2 4-1 0-1-1-2-1-1 1-1 2-2 2v1 1h-1c-1-2 2-6 2-8v-1c9-23 30-44 51-56v-65z"></path><path d="M731 232c0-2 0-3 1-4l1-1 1-1 1 3c-1 2-2 3-2 4-1 0-1-1-2-1z" class="E"></path><path d="M688 621c2-1 4-3 6-4v1c1 1 2 3 3 4l1 2c1-1 1-3 1-4l1 1v8 2c-1 4-4 10-8 13h0c-4 1-7 3-9 5-1 2-2 3-3 4l-2 5c-1 1-2 2-3 2l-3 1-6 6c-1-3 2-6 3-9 1-7 4-13 5-19h1c1-1 1-3 1-3 1-4 2-7 4-10l2-2c1-2 3-3 5-4l1 1z"></path><path d="M687 620l1 1-6 9v1h0c-1 1-3 5-5 6l-1-1c1-4 2-7 4-10l2-2c1-2 3-3 5-4z" class="D"></path><path d="M686 627c1-2 2-3 4-4 2 1 5 1 6 4 2 2 1 5 1 7-1 2-2 4-4 6v-5-4c0-2 0-4-1-5-1 0-3 0-3 1h-1-2z" class="I"></path><path d="M686 627h2 1c0-1 2-1 3-1 1 1 1 3 1 5v4 5c-2 1-3 2-5 3l-3-1c0-1-1-1-1-2-1-2 0-4 1-6v-1h-1v-1l3-3h-1l-1-1v2c-1 1-1 1-2 1h-1 0v-1c2-1 3-2 4-3z" class="M"></path><path d="M687 635h2l1 1c0 2-1 3-2 4h-2-1v-1c0-2 1-3 2-4z" class="L"></path><path d="M682 631h1c1 0 1 0 2-1v-2l1 1h1l-3 3v1h1v1c-1 2-2 4-1 6 0 1 1 1 1 2l3 1c-3 2-4 3-5 6h0c-1 2-2 3-3 4l-2 5c-1 1-2 2-3 2l-3 1-6 6c-1-3 2-6 3-9 1-7 4-13 5-19h1c1-1 1-3 1-3l1 1c2-1 4-5 5-6z"></path><path d="M685 642l3 1c-3 2-4 3-5 6h0c-1 2-2 3-3 4l-2 5c-1 1-2 2-3 2l-3 1c2-3 5-6 7-10 2-2 3-5 5-7 1-1 1-1 1-2zm-3-11h1c1 0 1 0 2-1v-2l1 1h1l-3 3v1h1v1h-1c-1 0-1 1-2 1-2 1-3 3-3 4-2 5-4 11-7 14l3-14c1-1 1-3 1-3l1 1c2-1 4-5 5-6z" class="H"></path><path d="M444 810v-1c14 1 29 0 43-1l10-2c5-1 17-5 20-4-3 1-5 3-7 5h0c1 3 2 5 1 8s-8 6-11 7l-4 1c-2 2-7 2-10 2h0c-2-1-4-1-6-1-4-1-6-4-9-7h-1v-3c-1 0-2 0-3-1h-1l-3-1-19-2z"></path><path d="M496 808c3 0 4-1 7 1v1h-2-3l-3 1h-1l2-3z" class="H"></path><path d="M463 812h13l-4 4-1 1h-1v-3c-1 0-2 0-3-1h-1l-3-1z" class="X"></path><path d="M503 809c1 1 3 2 3 3 0 2-2 6-3 7-2 2-4 2-6 3l-1-1v-2c4 0 5-1 7-3 0-1 1-2 1-2 0-2-1-3-3-4h2v-1z" class="J"></path><path d="M476 812c6-2 14-3 20-4l-2 3-1-1c-1 0-2 0-3 1h-2 0c-2 1-3 1-5 1l-1 1c-2 0-6 2-8 3h-2l4-4z" class="C"></path><path d="M498 810h3c2 1 3 2 3 4 0 0-1 1-1 2-2 2-3 3-7 3v2c-2-1-4-1-6-3-1-1-1-1-1-2 0-2 2-4 4-6l1 1h1l3-1z"></path><path d="M494 811h1c-1 1-2 2-2 4 0 1 1 2 2 3l1 1v2c-2-1-4-1-6-3-1-1-1-1-1-2 0-2 2-4 4-6l1 1z" class="E"></path><path d="M498 810h3c2 1 3 2 3 4 0 0-1 1-1 2l-1-1-1 1c-1 0-2 1-3 1v-1c-2 0-2 0-3-1 1-2 1-2 2-3l1 1c0 1 0 1 1 2h1v-1-3-1h-2z" class="L"></path><defs><linearGradient id="N" x1="472.37" y1="818.603" x2="495.027" y2="821.267" xlink:href="#B"><stop offset="0" stop-color="#838181"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient></defs><path fill="url(#N)" d="M488 811h0 2c1-1 2-1 3-1-2 2-4 4-4 6 0 1 0 1 1 2 2 2 4 2 6 3l1 1-1 1c-2 2-7 2-10 2h0c-2-1-4-1-6-1-4-1-6-4-9-7l1-1h2c2-1 6-3 8-3l1-1c2 0 3 0 5-1z"></path><path d="M488 811h0 2c1-1 2-1 3-1-2 2-4 4-4 6 0 1 0 1 1 2l-1 2h0l-2-2h-2l-1-1 2-2h0c-2 0-4 1-5 0h0l4-2c1 0 1-1 2-1l1-1z" class="O"></path><path d="M545 172h1 0 2 1c19 9 36 23 52 36 11 10 22 19 32 30l21 26 8 11c1 1 2 3 3 3 1 4 4 8 6 11-4-4-7-9-10-13l-15-20c-6-7-11-14-18-20l-1 1c1 2 2 4 4 5l-1 1c-5-6-10-13-16-17-1-1-3-1-4 0-4-4-9-7-13-10l-8-8c-2-1-4-3-6-5-1-3-5-6-8-8-6-3-10-7-16-9l-1 1h0l-2-2h-1l1-1h1 0l-2-2-1-1h-1c0-1-1-1-2-2-2-1-3-2-5-4l1-1s-2-1-2-2z" class="L"></path><path d="M558 187l-2-2h-1l1-1h1 0l-2-2-1-1h-1c0-1-1-1-2-2-2-1-3-2-5-4l1-1c4 3 7 5 11 8 5 2 11 5 16 7l7 5-1 2c-2 0-3 0-4-1h-1c-6-3-10-7-16-9l-1 1h0z" class="G"></path><path d="M581 194c8 6 14 12 21 18l19 17 7 7-1 1c1 2 2 4 4 5l-1 1c-5-6-10-13-16-17-1-1-3-1-4 0-4-4-9-7-13-10l-8-8c-2-1-4-3-6-5-1-3-5-6-8-8h1c1 1 2 1 4 1l1-2z" class="N"></path><path d="M602 768c2-1 5-2 8-3 2 0 4-2 6-3-2 2-3 4-5 5l-2 5c-1 1-1 2-1 3v1l2 1-15 11c-4 3-9 7-14 9-2 0-3 0-4-2 2-1 3-1 4-3-3 0-6-1-9-4-1-2 1-5 1-7h1c0-1 1-2 1-2l1-1h-1-1 0v-1h-3c1-1 2-3 3-4l12-10 1 1v1h0l1 1v1l2-1c2 0 4 0 6 1s3 1 4 3l1 1v1l2-2-1-2z"></path><path d="M590 771h0c1-1 1-1 3-2v1c2 1 2 2 4 2h2v1c-2 1-2 2-5 2-2-1-3-2-4-4z" class="E"></path><path d="M586 763l1 1v1h0l1 1v1l2-1c2 0 4 0 6 1l-3 2c-2 1-2 1-3 2h0c-4 2-7 4-10 8h0c-1 2 0 4-3 5-1-1-2-1-3-2v-1c0-1 1-2 1-2l1-1h-1-1 0v-1h-3c1-1 2-3 3-4l12-10z" class="D"></path><defs><linearGradient id="O" x1="585.492" y1="765.492" x2="582.004" y2="769.004" xlink:href="#B"><stop offset="0" stop-color="#757473"></stop><stop offset="1" stop-color="#8a8987"></stop></linearGradient></defs><path fill="url(#O)" d="M586 763l1 1v1h0l1 1v1c-4 3-8 7-12 9-1 0-2 1-2 1h-3c1-1 2-3 3-4l12-10z"></path><path d="M608 776l2 1-15 11c-4 3-9 7-14 9-2 0-3 0-4-2 2-1 3-1 4-3-3 0-6-1-9-4-1-2 1-5 1-7h1v1c1 1 2 1 3 2 3-1 2-3 3-5 2 1 4 3 6 2s6-3 8-2v2h-1v1c-1 0-1 1-1 2 5-2 13-4 16-8z" class="E"></path><path d="M580 779c2 1 4 3 6 2s6-3 8-2v2h-1v1c-1 0-1 1-1 2l-2 1c-1 0-2 1-2 2-1 1-1 0-1 1h-1-2c0-2-1-2-3-4-1 1-1 0-2 2v2h1l1 1c1 1 0 1 1 1 0 1 0 1-1 2-3 0-6-1-9-4-1-2 1-5 1-7h1v1c1 1 2 1 3 2 3-1 2-3 3-5z" class="M"></path><path d="M285 742c-1-1-3-2-4-3l-15-9c-8-5-15-11-22-17-11-9-22-21-29-34-4-8-5-16-7-25 5 12 10 24 19 34 1 2 3 4 5 6l8 7 7 7c1-1 1-2 1-2h1l1-2 2 2h1v-1c-1-2-1-4-2-6l2 1 9 14c6 6 13 15 20 19 1 3 3 3 3 5l18 16h-1c-5-4-11-9-17-12z"></path><defs><linearGradient id="P" x1="275.831" y1="732.086" x2="260.906" y2="716.016" xlink:href="#B"><stop offset="0" stop-color="#828080"></stop><stop offset="1" stop-color="#9a9898"></stop></linearGradient></defs><path fill="url(#P)" d="M262 714c6 6 13 15 20 19 1 3 3 3 3 5-8-5-16-11-23-17v-2c-1-1-1-2-1-2 0-1 1-2 1-3z"></path><defs><linearGradient id="Q" x1="260.422" y1="716.206" x2="251.258" y2="705.557" xlink:href="#B"><stop offset="0" stop-color="#aaa8a7"></stop><stop offset="1" stop-color="#cccbcb"></stop></linearGradient></defs><path fill="url(#Q)" d="M251 699l2 1 9 14c0 1-1 2-1 3 0 0 0 1 1 2v2l-15-13c1-1 1-2 1-2h1l1-2 2 2h1v-1c-1-2-1-4-2-6z"></path><path d="M610 226c1-1 3-1 4 0 6 4 11 11 16 17l1-1c-2-1-3-3-4-5l1-1c7 6 12 13 18 20l15 20c3 4 6 9 10 13 0 2 1 3 2 4-1 1-2 2-2 3l-1 4h-1c-1 0-1 1-2 1v-2l-1-1c0-2-1-2-2-3h-5c-1 1-1 2-2 4l-1 1c-1 0-2 0-3-1l-1-6 6-4h0c-1-1-3 0-4 0h-1c1-1 1-1 1-2 1-1 2-1 3-2 2-1 3-3 3-4 0-6-10-13-14-16-2-1-3-3-5-4-2-2-4-3-7-3h-1c-1-1-1-2-2-3s-1-2-1-3c-1 0-2 1-4 2h0 0c-2 1-5 1-7 0l1-1-1-1-4-3 4-6s1-1 1-2c0-4-7-12-10-15z" class="D"></path><path d="M621 249l1-2c2 1 2 2 2 3 0 2 0 2-1 3-1 0 0 0-1-1h-1c-1-1 0-2 0-3z"></path><path d="M630 252c3-1 6 0 8 2h2c-3 2-6 2-9 1-1-1-1-2-1-3z" class="P"></path><path d="M640 254c1 2 3 3 3 5 0 1-1 1-2 2-2-2-4-3-7-3h-1c-1-1-1-2-2-3 3 1 6 1 9-1z" class="B"></path><path d="M619 243c0 2 0 3 1 5l1 1c0 1-1 2 0 3h1c1 1 0 1 1 1v1h3 0c-2 1-5 1-7 0l1-1-1-1-4-3 4-6z" class="I"></path><path d="M199 417c0-1 1-1 2-1 1-1 1-2 3-3v1c3 0 6 0 8 2 2 1 2 2 3 3v1h1v-1c1 1 1 2 1 3l1 1-3 17c-3 17-5 34-3 51-1 2-1 4-2 5 0-2-1-6-2-8s-2-2-4-2v-1c-1-4-1-26 0-30 1-5 0-10 0-15-1-3 0-6 1-8-3 0-5 1-7 0-1-1-2-2-2-4v-1c0-4 1-7 3-10z" class="F"></path><path d="M208 421c1 0 1 0 2 1 0 1 1 1 1 2l-2 1c-1 0-2 1-3 0l-1-1c1-2 1-2 3-3z" class="T"></path><path d="M205 450v-3c0-4 0-8 1-11l1-4h1l2 1c0 3-1 5-1 8v-3l-3 3v9h-1z" class="J"></path><path d="M196 428c3-1 11 0 14 1 1 1 0 3 0 4l-2-1h-1l-1 4c-1 3-1 7-1 11v3 5h-1c1-5 0-10 0-15-1-3 0-6 1-8-3 0-5 1-7 0-1-1-2-2-2-4z"></path><path d="M142 182c-1-1-1-2-2-3h0 0c1 1 3 1 4 2 17 15 30 35 37 57 4 13 6 27 6 40 1 13 0 26-1 39l-6 51c-2 10-2 21-5 31v-1l-2 3 10-106c1-34-7-69-27-97-4-6-9-11-14-16z" class="B"></path><path d="M241 723c5 5 10 10 14 15l12 15c5 7 12 14 19 20 15 14 32 27 50 37 19 12 40 21 59 33 1 0 3 2 3 3 1 0 0 0 0 1l4 1c5 1 7 5 11 7l13 10c3 2 5 4 8 7 3 2 8 5 10 9l-1 1c-3-2-5-4-8-6-1-2-3-3-5-5-3-3-7-5-11-8-7-5-14-11-21-15-6-3-11-6-17-8l-34-19c-21-12-41-26-60-42-12-11-23-22-32-35-5-6-10-13-14-21z"></path><path d="M261 293h3l1-1-1-1h1l1 1v2c-1 8-11 21-14 28-1 2-3 4-3 6-2 3-3 5-4 8-3 6-6 13-8 19h-4 0c-3 2-3 8-3 12 0-3-1-6-2-8-1-3-3-7-6-8v-1h-3c-1-1 0-4 0-5 1-1 2-4 3-6 2-4 4-7 7-10 0-1 3-2 4-3s2-2 2-3c2-1 3-2 4-3h0l12-10 1 2c3-4 8-9 9-13-1-2-6-1-8-2-1-1 0-1 0-2v1c4-1 5 0 8 0 0 1 1 0 2-1v-1l-2-1z" class="F"></path><path d="M238 331h2v1c-1 1-2 1-3 2l-3-1h0c1-2 2-2 4-2z" class="T"></path><path d="M229 338h2l2-1c2 0 4 1 5 1l1 1h0c-1 0 0 1-1 0-1 0-1-1-2-1l-1 1v2c-3-1-4-2-6-3z" class="M"></path><path d="M233 326c1-1 2-2 2-3 2-1 3-2 4-3 0 1-1 2-1 3s1 1 2 1h0c-4 3-8 6-11 10h-1c-1 0-1 1-2 3h-1c1-1 1-2 1-2 1-2 2-3 3-5v-1c0-1 3-2 4-3z" class="L"></path><path d="M251 310l1 2c-4 4-7 9-12 12h0c-1 0-2 0-2-1s1-2 1-3h0l12-10z" class="T"></path><path d="M229 329v1c-1 2-2 3-3 5 0 0 0 1-1 2h1c1-2 1-3 2-3h1l-1 3 1 1c2 1 3 2 6 3h1l-1 1c-1 1-1 2-2 3 0-1-1-1-1-2-2 0-3-1-5-1-2 1-4 3-4 5-1 1-1 1-1 2l2 2c3-1 6-2 8-4h1l-1 2v1c-3 0-4 0-5 2 0 2 1 2 2 4 2-1 4-3 5-4l1-2 1 1c0 1-1 2-2 3l-1 1h0c-3 2-3 8-3 12 0-3-1-6-2-8-1-3-3-7-6-8v-1h-3c-1-1 0-4 0-5 1-1 2-4 3-6 2-4 4-7 7-10zm-33 98l-3-3c0-7 3-15 5-22l4-21c2-7 2-14 5-21 0 3 1 5 3 6 2 2 4 2 7 2 2 0 5-2 6-4 1-1 0-1 1-1 1 1 1 2 1 3 2 9-3 20-6 29-2 6-3 12-7 17v1h0l1 1c1 2 2 3 3 5v1h-1v-1c-1-1-1-2-3-3-2-2-5-2-8-2v-1c-2 1-2 2-3 3-1 0-2 0-2 1-2 3-3 6-3 10z"></path><path d="M201 403c2 1 3 1 4 1-1 2-3 3-4 5l-2 4 2-10z" class="E"></path><path d="M223 370h0v2c0 1 0 4-1 5 0-1 0-2-1-2l-1 4-1 1-1-1v-5c1-2 3-3 5-4z" class="P"></path><path d="M214 379c1-1 1-2 2-2 1 1 1 4 1 5 1 4-1 8-2 11h0c-2-3-2-11-1-14z" class="E"></path><path d="M210 396c0-2 1-3 1-5 1-2 1-3 1-5s0-5 1-7h1c-1 3-1 11 1 14h0c-1 6-3 12-7 17l-4 4v-1c-2 1-2 2-3 3-1 0-2 0-2 1 2-7 11-13 11-21z" class="J"></path><path d="M201 403l4-21c1-3 0-9 2-11h4c1 0 2 1 3 2 1 2 0 4-1 6s-1 5-1 7 0 3-1 5c0 2-1 3-1 5-1 3-2 6-5 8-1 0-2 0-4-1z" class="D"></path><path d="M571 216l-1-2c-1-2-2-4-1-6 1-3 3-5 6-6s5 0 8 1c2 2 4 4 6 5l8 8c4 3 9 6 13 10 3 3 10 11 10 15 0 1-1 2-1 2l-4 6 4 3-1 1c-1-1-3-1-4-2v-3c0-1-1-1-1-1l-1-1c-2 0-3-1-4-3-1-1-3-1-4-2-1 2-2 4-1 5l2 1h-1v1h-1c-10-6-21-14-29-22-1-2-3-4-5-6 1 0 2 1 3 1v1c2 1 2 3 4 4h1v-1h-1l-1-2c-1-1 1 1-1-1l-1-1h0c-1-2-2-2-2-4v-1z"></path><path d="M583 221l-2-2c0-2 0-2 1-3h1l2 1v1l-2 2v1zm17 10l3-1c0 2-2 4-4 5h-1 0v-3l2-1z" class="P"></path><path d="M608 243h0c0-6-2-14-3-20h0c3 4 5 11 6 15 0 3 0 6 2 9l-1-1c-2 0-3-1-4-3z" class="R"></path><path d="M588 231l-2-1c-3-3-11-9-12-14 0-2 1-4 3-5 1-1 2-2 4-2 4 0 8 3 11 6-1 2-3 3-4 5l-1-1 1-1c0-1 0-1-1-2-1-2-3-2-5-3-2 1-2 1-3 2s-1 3 0 4 2 3 4 3v-1-1c1 1 1 2 1 3v1h1c1 1 2 2 2 3 0 2 0 3 1 4z" class="N"></path><path d="M571 216c3 4 6 8 9 11 4 5 10 8 15 11 3 1 6 3 9 3-1 2-2 4-1 5l2 1h-1v1h-1c-10-6-21-14-29-22-1-2-3-4-5-6 1 0 2 1 3 1v1c2 1 2 3 4 4h1v-1h-1l-1-2c-1-1 1 1-1-1l-1-1h0c-1-2-2-2-2-4v-1z" class="B"></path><defs><linearGradient id="R" x1="603.215" y1="226.296" x2="588.111" y2="220.57" xlink:href="#B"><stop offset="0" stop-color="#a9a7a6"></stop><stop offset="1" stop-color="#cdcbca"></stop></linearGradient></defs><path fill="url(#R)" d="M585 224c3-3 6-6 9-8 4 2 7 4 9 8 0 2 1 4 0 6l-3 1-2 1v3h0-1c-3 1-6-2-9-4-1-1-1-2-1-4 0-1-1-2-2-3z"></path><path d="M595 230c-1-1-2-2-2-4s1-2 2-3c2 0 3 0 4 2 1 1 1 2 1 3-2 1-3 2-5 2z"></path><path d="M587 227l4 1c1 1 2 1 3 3 1 0 1 0 1-1 2 0 3-1 5-2-1 2-1 2-2 3h1 1l-2 1v3h0-1c-3 1-6-2-9-4-1-1-1-2-1-4z" class="O"></path><path d="M218 591c0 1 1 2 1 2 1 1 1 4 1 6 1 6 2 12 4 18l4 13c0 3 1 8 3 10h0 1v-1l3 5 4 12v1l-1-1c0 2 1 2 1 4h-1l9 18c3 6 5 12 8 18 2 4 5 9 8 13 3 5 7 10 10 14 3 3 7 7 9 10-7-4-14-13-20-19l-9-14-2-1c1 2 1 4 2 6v1h-1l-2-2-1 2h-1s0 1-1 2l-7-7-8-7c1-1 1-2 0-3 0-2 0-4-1-6l-5-6c-2-2-3-5-5-8l8-3h2v-2 1l2 1 1-1c1 1 1 2 1 3h1c0-2 1-5-1-7v-1l-1-2c1-1 1-2 1-3-1-3-2-4-5-6l-2-1v-1c2 0 2-1 3-1v-1l-7-23v1c-1 1-1 1 0 2-1 2-2 3-4 4-2 0-2-1-4-2-1-2-1-3-1-4v-1l-1-1 2-12v-2-1l1-2v-1c0-1 0-2 1-3v-11z"></path><path d="M246 691h2 1c2 1 3 4 4 7-3-1-5-2-7-4v-1-2z" class="E"></path><path d="M239 684l-2 1c0-1 0-1-1-1-1-2-1-6-1-8l1-1c3 1 5 1 8 4h-4v-2c-1 1-1 1-2 1l1 2c0 1 0 1-1 2 0 1 0 1 1 2z" class="G"></path><path d="M238 682c-1-1-1-2-1-3l1-1 1 2c0 1 0 1-1 2z" class="C"></path><path d="M231 640h0 1v-1l3 5 4 12v1l-1-1c0 2 1 2 1 4h-1c-1-2-1-4-2-6-2-5-4-9-5-14z" class="U"></path><path d="M238 678c1 0 1 0 2-1v2h4c2 4 3 7 2 11v1l-3-1c-1-1-1-1-2-1 1-1 1-1 2-3l-1-1c-1-1-1-1-3-1-1-1-1-1-1-2 1-1 1-1 1-2l-1-2z" class="Q"></path><path d="M231 685l1 1c3 2 4 4 6 6 1 0 1-1 1-2 0 4-1 6 2 9 0 1 0 2-1 2l-8-7c1-1 1-2 0-3 0-2 0-4-1-6z" class="O"></path><path d="M235 657l5 16c-1 1-2-1-3 0-2 0-2 1-4 1-1-2 0-3 0-4v-1c-1 1-1 2-2 3-1-1 0-2 0-4v-2 1l2 1 1-1c1 1 1 2 1 3h1c0-2 1-5-1-7v-1l-1-2c1-1 1-2 1-3z" class="I"></path><path d="M241 689c1 0 1 0 2 1l3 1v2 1c-1 2-2 2-1 4 1 1 4 1 6 1 1 2 1 4 2 6v1h-1l-2-2-1 2h-1s0 1-1 2l-7-7c1 0 1-1 1-2-3-3-2-5-2-9l2-1z" class="F"></path><path d="M241 699h1c1 2 3 3 5 4l1 3s0 1-1 2l-7-7c1 0 1-1 1-2z" class="B"></path><path d="M216 609v-1l1-2v-1c0-1 0-2 1-3 1 1 1 3 2 5 1 5 3 11 3 16v1c-1 2 0 4-2 5l-1 1c-2-1-3-2-4-4v-1h-1v-1l-1-1 2-12v-2z" class="I"></path><path d="M218 620c1 1 2 1 3 2 0 2-1 2-1 3v1h-1v-1l-2-1-1-1c1-1 1-2 2-3z" class="M"></path><path d="M214 623l2-12c1 2 1 5 2 7v2c-1 1-1 2-2 3l1 1-1 1h-1v-1l-1-1z" class="H"></path><path d="M187 449l1-5h0c4 12 13 25 10 37l1 1v1h2-1v1c0 2 0 7-1 9 0 2-4 6-4 7l-1 3c2 0 2 0 4 1h3c1-1 1-1 3-2l2 2c0 2 1 3 2 4h1l1 1v2l-6 24c-1 6-2 13-5 19 0 1 0 2-1 2-3-1-4-2-6-4v-1l-1-6c0-5-1-10-1-16l-1 1v-4c1-2 0-3 1-5v-2c-1-1 0-2-1-4l2-1c-1-1-1-2-1-4v-1-1l2-1c1-1-1-7 0-9 1-5 2-11 4-16l2-6c-3 3-4 6-7 10-1 2-2 3-5 3h-2c-1-10 1-20 2-30h0c0-2-1-6 0-7s0-2 1-3z"></path><path d="M192 551c1-2 1-2 1-3 2 1 0 3 3 4l2-1c0 2 0 2 1 3 0 1 0 2-1 2-3-1-4-2-6-4v-1z" class="H"></path><path d="M197 530c1 3-1 7-1 9h1l1-1v1c0 2-1 5-3 6-2-4 1-10 2-15z" class="K"></path><path d="M190 510v-1-1l2-1c-1 2-1 3 0 4 1 2 5 3 8 4-2 1-3 2-4 2h-2-1c-2-1-2-2-2-3-1-1-1-2-1-4z" class="B"></path><path d="M190 529l-1 1v-4c1-2 0-3 1-5v-2c-1-1 0-2-1-4l2-1c0 1 0 2 2 3h1 2c-1 1-4 3-4 3-1 2-1 7-2 9z" class="E"></path><path d="M200 516l2-1c0 3 1 6-1 9-2 4-3 10-3 14l-1 1h-1c0-2 2-6 1-9 1-4 4-8 4-12 0-1 0-1-1-2z" class="Y"></path><path d="M190 477h0c4-5 3-10 3-16l3 8c0 2-1 5-2 7-2 2-4 3-6 4h0c0-1-1-1-1-2l2-1h1z" class="M"></path><path d="M186 459h0c0-2-1-6 0-7s0-2 1-3c0 6-1 23 1 28h2-1l-2 1c0 1 1 1 1 2l-2 1v-22h0z" class="I"></path><path d="M200 516c0-3-3-4-5-6l4 2c2 1 3 1 5 1h1c1 2 0 5-1 7-1 6-4 12-6 19v-1c0-4 1-10 3-14 2-3 1-6 1-9l-2 1z" class="O"></path><path d="M208 510l2 1-6 24c-1 6-2 13-5 19-1-1-1-1-1-3l10-41z" class="I"></path><path d="M208 510c0-1 1 0 0-1l-1 1c-1 1-3 2-5 2s-5-2-7-3v-1c-1-2-2-4-2-6l2-2-1 3c2 0 2 0 4 1h3c1-1 1-1 3-2l2 2c0 2 1 3 2 4h1l1 1v2l-2-1z" class="Q"></path><path d="M201 504c1-1 1-1 3-2l2 2-1 2c-1 1-1 2-2 2l-2-2v-2z" class="L"></path><path d="M719 461c0 2 1 3 0 4 0 1-1 1-1 1 2 0 2-3 3-2l1 1c-1 3-1 6-3 8h-1l1 1-1 2c0 1 2 3 3 5 2 4 4 8 5 12s2 9 2 14c-1 3-1 6 0 9h-1v-1-3h-2l1 1v-1c1 1 1 2 1 2l-1 1h0c-2 2-4 3-5 5v1 3c-1 1-1 2-1 3l-2 2c-4 1-7 1-9 4v8c-1-2-1-2-1-4h-2c0 1 0 1 1 2v3l1 3 1 1v1 2c-2-4-3-8-4-12-2-5-4-11-5-16l-1-1c0-5-1-10 0-14 0-5 1-11 1-16v-5l2-2 2 2c-1 2-1 4-1 5 3-4 6-9 7-13 0-1 0-2 1-3h0l1-2c1-1 1-2 2-3l3-3 2-5z" class="F"></path><path d="M704 485c-1 2-1 4-1 5 3-4 6-9 7-13 0-1 0-2 1-3h0v1c0 5-3 9-4 14l1 1-1 1c-2 1-3 3-4 5v2c2-2 2-5 6-6 2 1 2 1 4 3l-1 1c-2 0-3 1-5 2 1 1 1 2 3 3-2 1-3 2-5 3-1 4-2 9 0 12l1 3c-1 1-1 1-1 2 0 2 1 3 3 4 0 1 0 1-1 2-1-1-2-1-2-3l-2-3c0-1-1-1-2-2l-1-3v-3c-1-2 0-4 0-5v9 4l-1-1c0-5-1-10 0-14 0-5 1-11 1-16v-5l2-2 2 2z" class="H"></path><path d="M707 498c1 1 1 2 3 3-2 1-3 2-5 3h-1-1v-3c1-1 3-3 4-3z" class="I"></path><path d="M719 461c0 2 1 3 0 4 0 1-1 1-1 1 2 0 2-3 3-2l1 1c-1 3-1 6-3 8h-1l-1 1c-3 1-2-1-4 1l-1 1c5 5 9 9 9 17v4c-1 1-1 1-1 2h0c-3-1-5-3-7-4h0c-2-2-2-2-4-3-4 1-4 4-6 6v-2c1-2 2-4 4-5l1-1-1-1c1-5 4-9 4-14v-1l1-2c1-1 1-2 2-3l3-3 2-5z"></path><path d="M713 495h0c2 1 4 3 7 4h0c0 1 0 3-1 5 1 4 2 12-1 15-1 2-4 3-6 3-2 1-4-1-5-2l-1-1-1-3c-2-3-1-8 0-12 2-1 3-2 5-3-2-1-2-2-3-3 2-1 3-2 5-2l1-1z"></path><path d="M713 495h0c2 1 4 3 7 4h0c0 1 0 3-1 5l-1-1c-3-3-5-3-8-2-2-1-2-2-3-3 2-1 3-2 5-2l1-1z" class="Q"></path><path d="M672 667l1 1c-1 2-3 5-4 8-2 6-6 11-7 17h1v1h-1c2 4 2 7 0 12 2 0 3-1 5-1l-3 4c-2 2-3 3-5 4l-2 2-5 4c-1 1-5 4-5 6-1 1-2 1-2 2s1 1 1 2h-1l-1-1h-1c-2 1-4 4-6 5v-1l-2 1-2 2h0c-2 4-6 6-7 10l-2 2-23 12c-4 1-7 4-11 6v1l-2 1v-1l-1-1h0v-1l-1-1 10-9 2 1 10-9c6-5 12-11 17-18 13-15 25-32 35-49 2-4 4-7 5-11l7-1z"></path><path d="M596 754l2 1-8 9v1 1l-2 1v-1l-1-1h0v-1l-1-1 10-9z" class="R"></path><path d="M634 731c1-3 2-5 3-7h2v1c-1 3-2 5-4 8l-2 2h0c-5 5-10 11-17 15-4 3-9 5-14 7l31-31h0v4l1 1z" class="N"></path><path d="M634 731c1-3 2-5 3-7h2v1c-1 3-2 5-4 8l-2 2h0c-5 5-10 11-17 15v-2l3-2c1-1 1-3 3-5 3-4 7-6 10-10h2 0z" class="J"></path><defs><linearGradient id="S" x1="650.803" y1="697.308" x2="654.648" y2="699.53" xlink:href="#B"><stop offset="0" stop-color="#a3a09f"></stop><stop offset="1" stop-color="#cfcccc"></stop></linearGradient></defs><path fill="url(#S)" d="M664 678l1-1c1-2 1-3 3-3v1l1 1c-2 6-6 11-7 17h1v1h-1-1c0 1-1 4-2 5-2 5-6 10-10 13l1 1h1l1 1 1-1v1c-3 0-6 1-9 2-3 2-6 4-8 6-1 1-2 3-3 4h0l31-48z"></path><path d="M664 678l1-1c1-2 1-3 3-3v1l1 1c-2 6-6 11-7 17h1v1h-1-1c0 1-1 4-2 5-2 5-6 10-10 13l1 1h1l1 1 1-1v1c-3 0-6 1-9 2 2-3 5-6 7-9 4-6 9-13 11-19l2-5h0 0c0-1 0-2 1-2v-1l-1-2z" class="W"></path><path d="M664 678l1-1c1-2 1-3 3-3v1c-1 4-3 9-6 13l2-5h0 0c0-1 0-2 1-2v-1l-1-2z" class="O"></path><path d="M644 716c3-1 6-2 9-2v-1l-1 1-1-1h-1l-1-1c4-3 8-8 10-13 1-1 2-4 2-5h1c2 4 2 7 0 12 2 0 3-1 5-1l-3 4c-2 2-3 3-5 4l-2 2-5 4c-1 1-5 4-5 6-1 1-2 1-2 2s1 1 1 2h-1l-1-1h-1c-2 1-4 4-6 5v-1l-2 1c2-3 3-5 4-8v-1h-2c-1 2-2 4-3 7l-1-1v-4c1-1 2-3 3-4 2-2 5-4 8-6z"></path><path d="M651 717l1 2c-1 1-5 4-5 6-1 1-2 1-2 2s1 1 1 2h-1l-1-1h-1c-2 1-4 4-6 5v-1l14-15z" class="B"></path><path d="M662 706c2 0 3-1 5-1l-3 4c-2 2-3 3-5 4l-2 2-5 4-1-2a57.31 57.31 0 0 0 11-11z" class="G"></path><path d="M290 752c-1 0-3-1-5-1-3-1-6-1-10 1v1l-6-5-1 1c-2-1-5-4-6-6 1 0 2 1 3 1l1-2v-2c-1-1-2-1-4-2v1 1l1 1h-1c-2-1-4-4-5-6 0-1 1-2 2-3 3 2 7 4 11 7l15 8c3 3 9 5 12 8 3 1 6 4 9 5h0l-2-4 4 2h1l-1-1 1-1c1 1 2 2 4 2l7 6v3c1 0 3 3 4 3l5 7-1 1h-2l16 17c1 1 3 4 4 5 2 4 5 8 7 13 0 1 1 1 0 2-1-1-3-1-5-2-3-2-7-4-10-7l-17-11c-6-4-32-22-33-28 2-2 4-3 6-4l-1-2h-4 0l2-1c0-1 1-1 2-1l1 1c1-1 1-1 1-3 1 2 0 2 0 4h0 1s1 1 2 0h1c0 1 0 1 1 1h1c0 1 0 0 1 1h0c1 0 2 1 3 1v1h3 0l-7-6c-4-2-7-4-11-6v-1z"></path><path d="M329 791c1 1 2 1 3 2h2l1 2c-1 1-1 1-3 1-1 0-2 0-4-1 1-1 1-3 1-4z" class="E"></path><path d="M346 806c2 2 5 4 6 7h1c0 1 1 1 0 2-1-1-3-1-5-2-1-2-4-4-5-5 0-1 1-1 3-2z" class="N"></path><path d="M343 808l-15-10c6 3 12 5 18 8-2 1-3 1-3 2z" class="K"></path><path d="M319 789l2-1c0-2 0-1-1-2v-2l1-1c1 0 1 1 2 2v1 1c0 1 0 2 2 2 0 1 0 0 1 0l1 1c1 1 1 1 2 1 0 1 0 3-1 4l-9-6z" class="G"></path><path d="M304 756l4 2h1l-1-1 1-1c1 1 2 2 4 2l7 6v3c1 0 3 3 4 3l5 7-1 1h-2c-2-1-5-5-7-7-4-4-9-7-13-11h0l-2-4z" class="K"></path><path d="M308 758h1l-1-1 1-1c1 1 2 2 4 2l7 6v3l-2-2h-2c-2 0-6-5-8-7z" class="W"></path><path d="M319 789l-10-7c-3-3-7-7-10-8-2-1-4-3-6-5 2 0 6-1 8 0 3 1 5 4 6 7 1 1 3 4 5 4h0l1-3v1h1l2-1 5 5v1l-1 1v2c1 1 1 0 1 2l-2 1z" class="B"></path><path d="M298 770h1 1c2 1 3 2 5 3l-1 1-1 1-2-2c-1 0-2-1-4-2l1-1z" class="T"></path><path d="M290 752c12 4 22 16 31 25 5 5 9 10 13 16h-2c-1-1-2-1-3-2-1 0-1 0-2-1l-1-1c-1 0-1 1-1 0-2 0-2-1-2-2v-1-1c-1-1-1-2-2-2v-1l-5-5c-4-4-7-8-11-11s-7-4-11-3l-1-2h-4 0l2-1c0-1 1-1 2-1l1 1c1-1 1-1 1-3 1 2 0 2 0 4h0 1s1 1 2 0h1c0 1 0 1 1 1h1c0 1 0 0 1 1h0c1 0 2 1 3 1v1h3 0l-7-6c-4-2-7-4-11-6v-1z" class="J"></path><path d="M682 624l-1-1c-1-2 2-7 3-9 3-11 5-22 7-34l1-13 4-24 2-23h1l1 1c1 5 3 11 5 16 1 4 2 8 4 12 1 4 3 8 4 12 3 14 0 29-4 43-1 6-3 12-5 18-2 3-2 6-4 9v-2-8l-1-1c0 1 0 3-1 4l-1-2c-1-1-2-3-3-4v-1c-2 1-4 3-6 4l-1-1c-2 1-4 2-5 4z"></path><path d="M698 541c1 2 1 3 1 5l1-1 1 1v3h-1c-1 1-2 3-3 5 2 0 5-1 7 0v1h-7l1-14z" class="C"></path><path d="M700 545l3-3 4 12-1 1c-1-3-3-4-6-6h1v-3l-1-1z" class="O"></path><path d="M698 541l1-12 4 13-3 3-1 1c0-2 0-3-1-5z" class="K"></path><path d="M704 579c1 0 1-1 3-1 0 2-1 5-1 7l-1 1v-1c-3-1-6-1-8 0l3-7h1c0 1 0 1 1 2 0 0 1-1 2-1z" class="F"></path><path d="M701 594v1c0 1 0 1 1 2v3h-1-1c-3 9-10 13-16 18l3-6c1-2 3-3 5-4 5-4 7-7 9-14z" class="B"></path><defs><linearGradient id="T" x1="700.015" y1="616.711" x2="696.402" y2="608.603" xlink:href="#B"><stop offset="0" stop-color="#929090"></stop><stop offset="1" stop-color="#aeaba8"></stop></linearGradient></defs><path fill="url(#T)" d="M702 600c1 1 1 1 2 0 0 3-1 4-2 7v1c0 3-1 6-2 8l-2 1c0 1 0 2-1 3h0c1 1 1 1 0 2-1-1-2-3-3-4v-1c-2 1-4 3-6 4l-1-1c0-1 6-3 7-5 4-3 7-10 7-15h1z"></path><path d="M705 585v1l1-1-2 15c-1 1-1 1-2 0v-3c-1-1-1-1-1-2v-1l-1-1c-2-2-3-2-5-2v1 1c0 5-1 8-4 12 0-4 2-8 2-11 1-3 1-6 2-8 1-3 7 0 9-1h1z" class="E"></path><defs><linearGradient id="U" x1="698.713" y1="588.617" x2="709.962" y2="590.567" xlink:href="#B"><stop offset="0" stop-color="#908e8e"></stop><stop offset="1" stop-color="#b9b7b6"></stop></linearGradient></defs><path fill="url(#U)" d="M707 554c3 13 3 23 3 36 0 4 0 8-1 12v2c-1 6-3 12-5 18-2 3-2 6-4 9v-2-8l-1-1c0 1 0 3-1 4l-1-2c1-1 1-1 0-2h0c1-1 1-2 1-3l2-1c1-2 2-5 2-8v-1c1-3 2-4 2-7l2-15c0-2 1-5 1-7-2 0-2 1-3 1s-2 1-2 1c-1-1-1-1-1-2h-1l1-1v-1c-1 0-2 0-3-1-1 0-2-2-2-3-1-2 0-5 0-7 1-3 3-4 5-5h6c0-2-1-4-1-5l1-1z"></path><path d="M696 572v-1c1 1 2 1 3 1h1v1l1-1c-1-1-1-1-1-2h0c-1-1-1-1-1-2l3-3 1 1c1 0 1 1 1 2l-1 1c0 1 1 1 1 2l-3 4h1c1 0 1-2 2-1v4 1c-1 0-2 1-2 1-1-1-1-1-1-2h-1l1-1v-1c-1 0-2 0-3-1-1 0-2-2-2-3z" class="B"></path><path d="M696 572c-1-2 0-5 0-7 1-3 3-4 5-5l3 1h0c1 2 1 3 3 3v2l-1 1c-1-1-1-1-3-1l-1-1-3 3c0 1 0 1 1 2h0c0 1 0 1 1 2l-1 1v-1h-1c-1 0-2 0-3-1v1z" class="O"></path><defs><linearGradient id="V" x1="696.494" y1="616.476" x2="705.541" y2="609.743" xlink:href="#B"><stop offset="0" stop-color="#797b77"></stop><stop offset="1" stop-color="#989394"></stop></linearGradient></defs><path fill="url(#V)" d="M702 608l2-4 1 1s0-1 1-1v-2c0 2-1 4-1 5-1 4-5 12-1 15-2 3-2 6-4 9v-2-8l-1-1c0 1 0 3-1 4l-1-2c1-1 1-1 0-2h0c1-1 1-2 1-3l2-1c1-2 2-5 2-8z"></path><path d="M516 830c-1 1-5 4-7 5l-15 12c-4 3-8 6-11 9-10 9-18 18-26 28-7-8-13-15-20-22-10-9-23-17-34-24-9-6-18-11-28-15 7 4 13 8 18 13-8-3-16-9-23-15-3-1-6-4-8-7v-2c5-1 13 4 17 6 3 2 6 3 9 4l21 6c6 2 11 4 16 4 8 2 16 2 24 2 3 0 6 0 9 1 2 0 4 2 6 3h1c0-1 1-3 2-3 0-1 3-1 4-1l16-3c13-2 29-5 40-13-1 2-3 4-4 5-3 2-5 5-7 7z"></path><path d="M441 847l-5 3-1 2c-2-1-5-3-8-4l6-1c3-1 6-1 9 0h-1z" class="Q"></path><path d="M465 854l-5-11c5 3 9 7 14 10v1l-1-1c-2 0-4 0-5-1h-1-1v-1c-1 1-1 1-1 3z" class="I"></path><path d="M474 845h6c2 0 3 0 4 1-2 1-5 4-7 5 0 0-1-1-2-1l-3-3v-1l3 1-1-1c-1 0-4 0-5-1h5zm18-6h1 1c-2 1-5 3-6 4h-5c-4-1-8-1-12-1h-1-2c8-2 17-2 24-3zm-46 11c2-4 4-7 9-8v1l-3 5c-1 1-1 3-1 4v1l2-2v1c0 1-1 2-1 4-4-2-4-3-6-6z" class="B"></path><path d="M456 854c0-4 1-7 2-11 0 2 0 4 1 6 1 1 1 3 2 5s2 3 2 5h0c0 2-1 2-2 3l-1 1h-1l-3-3v-6z" class="C"></path><path d="M456 854c2 0 3 0 4 2 0 2 1 5 0 7h-1l-3-3v-6zm-14-7v-2c-4 0-8 1-12 0-6 0-13-4-18-7h0c12 4 22 5 34 6h0c-4 3-6 7-9 10h-1v-1c1-3 3-4 5-6h1z" class="F"></path><path d="M446 850c2 3 2 4 6 6l4 4 3 3h1l1-1c1-1 2-1 2-3 1-2 2-3 2-5s0-2 1-3v1h1 1c1 1 3 1 5 1l1 1c-3 3-6 6-9 8l-8 8-17-13c2-2 4-4 5-6l1-1z" class="D"></path><path d="M365 190c15-14 31-23 48-34 16-12 30-24 43-39 10 11 20 22 32 31l31 22c18 13 35 30 49 48l-1 1-12-14c-3 0-3-2-6-4h-1c-2-1-2-2-2-3h-2l-1-1v-1c-1-2-4-4-6-6 0-1 0-2 1-3l-8-5c-20-12-45-14-67-16-4 0-9-1-13-1-17 1-34 5-50 9-8 2-17 6-25 10-2 2-5 3-8 5l-2 1z"></path><path d="M425 154c2-1 3-2 4-2l1 1c2 2 5 3 7 5-2 2-6-3-9-3h0l-3-1z" class="B"></path><path d="M547 195l8 10c-3 0-3-2-6-4h-1c-2-1-2-2-2-3h0l1-3z" class="C"></path><path d="M476 154l5-4c3 1 6 4 8 6-1 0-3 0-4-1-3-1-7-1-9-1z" class="J"></path><path d="M538 187l9 8-1 3h0-2l-1-1v-1c-1-2-4-4-6-6 0-1 0-2 1-3z" class="H"></path><path d="M450 142c2-1 5-1 8 0h0 0c1 3 1 7 0 10-1 0-1 1-2 1-2-1-2-6-5-7h0c2-1 2-1 3-3-1-1-2-1-4-1z" class="G"></path><path d="M456 143h0l1 1v3c-1 1-1 0-2 0-1-2 0-2 1-4z" class="D"></path><path d="M442 156c-4-2-7-4-10-7 2-1 3-2 5-3 1 1 1 3 2 3 3 1 4 2 6 4h0c1 2 3 2 3 5l-6-2z" class="J"></path><path d="M442 156l1-1c1 0 2 1 3 1-3-2-5-4-8-5v-1l1-1c3 1 4 2 6 4h0c1 2 3 2 3 5l-6-2z" class="N"></path><path d="M443 143c1-2 3-3 5-5 3-2 6-5 9-6 0 1 1 4 0 6h-1c-1-1-2 0-3 0s-2 1-3 2v1c-1 0-2 0-3 1h3 0c2 0 3 0 4 1-1 2-1 2-3 3-2 0-4-1-6-2-1-1-1-1-2-1z" class="Q"></path><path d="M501 166c-3 0-5-1-8-2-10-2-20-3-30-3l3-2c4-1 7-1 11-1 7 1 14 2 21 6 1 1 2 1 3 2z" class="I"></path><path d="M476 154c2 0 6 0 9 1 1 1 3 1 4 1l20 13-8-3c-1-1-2-1-3-2v-1c-6-6-19-6-27-6 1-1 3-2 5-3z" class="G"></path><path d="M443 143c1 0 1 0 2 1 2 1 4 2 6 2h0-2v1l1 2v1c1 4 3 7 6 10-3-1-6-1-8-2 0-3-2-3-3-5h0c-2-2-3-3-6-4-1 0-1-2-2-3l3-2 2 2c0-2 0-2 1-3z" class="K"></path><path d="M450 149l-1 1c-2-1-4-1-5-2s-1-2-1-3v-1l2 1h0c1 1 2 1 4 2l1 2z" class="I"></path><path d="M437 146l3-2 2 2c0 1 1 2 3 3l2 1s1 1 1 2l1 1-1 1-3-1h0c-2-2-3-3-6-4-1 0-1-2-2-3z" class="G"></path><path d="M457 132l8 7 5 4h-1c1 1 1 1 1 2-1 1-2 1-4 1-1 0-2 1-3 2-1-2-2-5-5-6h0 0c-3-1-6-1-8 0h0-3c1-1 2-1 3-1v-1c1-1 2-2 3-2s2-1 3 0h1c1-2 0-5 0-6z" class="O"></path><path d="M465 139l5 4h-1-1l-1 1h-2v-3l-1-1 1-1z" class="C"></path><path d="M457 132l8 7-1 1c-3-1-5-1-8 0h-6c1-1 2-2 3-2s2-1 3 0h1c1-2 0-5 0-6z" class="E"></path><path d="M470 143c2 0 4 1 6 3v1c-2 5-9 8-13 10l-7 3c3-4 5-8 7-12 1-1 2-2 3-2 2 0 3 0 4-1 0-1 0-1-1-2h1z" class="J"></path><path d="M470 143c2 0 4 1 6 3l-1 1c-3 1-5 0-8 0h1l-2-1c2 0 3 0 4-1 0-1 0-1-1-2h1z" class="G"></path><path d="M425 154l3 1h0c3 0 7 5 9 3l8 2-34 7-8 2 15-10 7-5z" class="U"></path><path d="M425 154l3 1 6 4c-3 0-7-1-10-2l-1 1 1 1h-6l7-5z" class="C"></path><path d="M411 167l-8 2 15-10h6c2 1 4 1 6 2v1h-3c-2-1-12 1-14 2v1l-2 2z" class="I"></path><path d="M676 286c-14-22-29-44-47-63-14-14-29-26-45-38-17-13-35-24-53-34-10-6-21-12-31-19-19-14-34-32-44-52-11 18-23 35-39 48-12 11-27 18-41 26-21 11-41 23-60 37-51 40-91 96-111 158-7 20-11 40-15 60-7 31-10 61-11 92v38c3 31 9 60 16 90 4 16 8 32 14 47 9 24 21 46 36 67 9 11 17 21 27 31 21 21 46 38 72 53l33 19c17 9 35 18 49 31 13 12 22 27 31 43 6-12 13-25 22-35 21-23 49-35 76-50 13-7 27-16 40-25 31-22 59-49 80-80l18-29c-1 5-3 9-5 13-4 6-8 13-12 19-15 22-33 43-53 60-19 16-39 30-61 42l-42 23c-23 13-43 33-56 56-3 5-5 11-7 17-11-26-28-49-51-65-20-13-42-23-62-35-11-7-21-13-31-20-13-10-25-20-37-32-24-22-46-49-61-79-8-17-13-35-19-53-11-36-17-73-19-111-2-68 4-140 30-204 25-61 64-115 118-153 15-10 30-19 46-28 11-6 22-11 32-18 23-16 42-40 53-66 1 4 3 9 5 13 13 25 34 44 58 58l41 24c23 15 44 32 64 50 26 24 48 52 64 84 18 34 29 71 36 109 5 26 9 53 9 81v39c-1 11-1 23-3 34-4 37-15 74-29 108-5 12-11 23-17 33-12 22-27 44-45 62-24 27-55 49-87 66l-25 12c-21 10-41 24-56 42-1 1-1 0-2 0 0-2 1-3 2-4 5-6 11-13 17-18 13-10 28-18 42-25l21-9c8-4 17-9 25-15 21-13 42-29 58-48 8-8 14-18 22-27 11-16 23-32 31-50 9-18 15-37 21-57 6-17 11-35 15-53 2-11 4-21 5-32v-30-7c0-5-1-10-2-14s-3-8-5-12c-1-2-3-4-3-5l1-2c1 0 2 0 2-1 2-2 2-4 2-7 1-7 0-15-1-23v-5c0-24-6-48-12-71-3-12-7-26-12-38-6-15-14-29-22-43z"></path><path d="M727 492c0 1 1 2 2 3 0 2 1 7 0 9l-1 1c0 2 1 6 0 9v-7c0-5-1-10-2-14l1-1z" class="R"></path><path d="M727 492v-32c2 12 2 23 2 35-1-1-2-2-2-3z" class="K"></path><path d="M671 86c-2-4-5-6-7-10h118l-1 30v65c-21 12-42 33-51 56-3 5-5 11-5 16-2 7-3 13-3 20-1 20 2 39 4 59l4 32 3 24c1 3 1 7 2 10h1c1 2 1 5 2 7 2 5 3 13 3 18v1l-1 2v-1h-1l1 6 1 5c3 26 5 51 5 77 1 29-2 58-6 86l-2 10c-2 9-3 19-5 28l-6 36-3 15c1 3 1 6 0 8v2h1c0 3 0 7 1 10l-1 7c-1 7 0 14 0 21l-1-1v1c2 15 5 31 13 45 4 8 9 15 15 22h0l6 6c1 2 3 3 4 5 1 1 3 2 4 3 4 2 11 6 13 10 0 1 0 4 1 4v-3h1v17 11l-2 70-116-2 4-5c4-3 7-10 7-15 0-4-4-6-6-8-3-3-6-6-10-9-13-12-26-22-43-29-5-2-11-4-16-6-5-1-11-2-15-5 1-2 17-10 19-11a250.74 250.74 0 0 0 44-35v-1c1-2 4-4 5-6 5-6 10-11 15-15l1-1c1-2 2-3 3-4s1-1 1-2c1 0 1 0 1-1 0-2 0-3-1-4v-1-1c2 1 3 0 5 0s2-1 3-2l2-3 2-2v-1l1-1v-1c1 0 1-1 2-1 0-1 2-3 2-5l-2-2c-1-1-1-1-2-1-1 1-2 2-3 2h-2c1-2 3-4 4-5l3-5v-1c1-2 1-3 2-3l2-3v-2c0-1 1-2 2-3 1-2 2-4 3-7l33-89c8-27 11-56 13-84 3-65-3-129-22-191-4-15-9-31-16-44-1-1-2-2-2-3v-2c0-2-1-4-2-5-1-2-1-3-2-5v-1l-3-3s0-1-1-2h0v-1c-1-1-1-2-2-3s-2-4-2-5c1-3 3-3 4-5-4-6-8-12-13-17-3-5-7-10-11-14-8-9-16-18-25-27-11-11-23-20-35-29l-18-12c-3-2-7-4-9-6 23-11 46-20 65-37 5-6 11-12 17-18 2-3 5-6 5-10 2-1 2-1 3-3l-2-7z"></path><path d="M699 254l4 1v1c0 1-1 1-1 2-1 1-2 1-2 3l-1-7z" class="E"></path><path d="M636 159h1v7h-1v1h-1c0-1-1-1-2-2l3-6z" class="H"></path><path d="M617 826c1 0 2-2 3-1 2 0 4 2 5 3l-1 1c-3 0-5-1-7-3z" class="M"></path><path d="M712 680c1-3 2-5 3-7v8c-1 2 0 2-1 4-1-2-1-3-2-4v-1z" class="U"></path><path d="M731 862c2-1 4-1 5 0l-3 6c-1-1-1-2-1-3v-2h-2l1-1z" class="E"></path><path d="M617 826c2 2 4 3 7 3l-1 1h-4l-2 1c-1-1-3-1-4-1l4-4z" class="C"></path><path d="M707 697l1-1c2-1 4-1 5-1-1 4-2 8-4 11 0-3-1-6-2-9z" class="N"></path><path d="M632 815l2-1c3 1 7 8 8 11l-3-2-7-8z" class="B"></path><path d="M625 828c4 3 7 6 8 11-1 0-2-1-3-2-2-2-4-4-6-5v-1c-2-1-4-1-5-1h4l1-1 1-1z" class="H"></path><path d="M643 818h1c1-2 2-2 4-3l1 1-1 1s-1 1-1 2 1 2 2 3l2 1-3 4-5-9z" class="B"></path><path d="M771 896c1 2 5 5 5 7 1 1 1 4 1 5l-1 1c0 1-1 1-2 1l-3-14z" class="K"></path><path d="M769 876l7-7v16c0-1-1-1-1-2-2-1-2-2-3-4l1-3h0c1-1 0-2 0-3l-3 3h-1z" class="B"></path><path d="M656 806c0 2 1 7-1 9-1 2-1 3-2 5h0c-1-2-1-3-2-3l-2-1-1-1c0-1 1-1 2-2 0 1 0 1 1 2h0c0-2 0-4 1-6h0c1 0 2 1 3 1l1-4z" class="J"></path><path d="M716 307c3 5 5 11 6 17-3-4-7-9-8-14v-1c0-1 1-1 1-2h1z" class="P"></path><path d="M743 828c2 3 5 6 7 8 0 2 0 4-1 5-3 0-7-4-8-6 1 0 2 1 2 2h1c2 1 1 0 1 1 1 1 2 2 3 2l1-1c-1-1-4-3-5-4v-1l-3-3c0-1 1-2 2-3zm-8-440h1c1 2 1 5 2 7 2 5 3 13 3 18v1l-1 2v-1h-1c0-1-1-5-1-6l-3-21z" class="H"></path><defs><linearGradient id="W" x1="625.415" y1="840.706" x2="610.642" y2="842.718" xlink:href="#B"><stop offset="0" stop-color="#9b9b9c"></stop><stop offset="1" stop-color="#b7b6b1"></stop></linearGradient></defs><path fill="url(#W)" d="M629 849c-9-5-16-9-26-12l3-3-1 3 1-1c4 1 9 2 13 4 1 1 3 2 5 2 1 1 3 2 4 3l1 1c-1 1-1 1-2 1h0l2 2z"></path><path d="M737 905h1 0c-4 4-6 8-12 7-2 0-3-1-5-1-4 0-8 1-13 2l-1-1c10-3 21-3 30-7z" class="R"></path><path d="M677 755c2 0 2-1 3-2l1 1c-8 10-16 18-25 27-3 3-6 7-9 9 1-2 4-4 5-6 5-6 10-11 15-15l1-1c1-2 2-3 3-4s1-1 1-2c1 0 1 0 1-1 0-2 0-3-1-4v-1-1c2 1 3 0 5 0zm-44-590c1 1 2 1 2 2h1v-1h1c1 5 2 9 4 14-3 0-3-2-5-3h-2c0 2 0 2-1 2s-1-1-1-1v-3-1l-1-1-1-1 3-7z" class="I"></path><path d="M767 94l9-9v38l-4-4c1 0 2 0 3 1v-1l-2-1v-13c0-2-1-8 0-9 0-1 1-1 1-2 1-1 0-4 1-5v-1c-2 1-5 5-7 6h-1z" class="G"></path><path d="M712 680v1c1 1 1 2 2 4 1-2 0-2 1-4 0 5 0 9-1 12l-1 2c-1 0-3 0-5 1l-1 1c0 1 0 1-1 2 0-2 0-2-1-4 0-1 1-3 2-4l5-11z" class="N"></path><path d="M708 691h2 0c0-1 0-2 1-3h2c0 2 0 3-2 4 0 0-1 0-1 1l-2-2z"></path><path d="M707 691h1l2 2h3 1l-1 2c-1 0-3 0-5 1l-1 1c0 1 0 1-1 2 0-2 0-2-1-4 0-1 1-3 2-4zm17 35h0c-3-15-2-33 0-48 1 3 1 6 0 8v2h1c0 3 0 7 1 10l-1 7c-1 7 0 14 0 21l-1-1v1z" class="J"></path><path d="M756 889l4-3 11 10 3 14h-2c-2-4-4-9-6-13-2-3-6-7-10-8z" class="O"></path><path d="M756 889c4 1 8 5 10 8 2 4 4 9 6 13h-7 3c0-3-2-4-3-6h-1l-1-3c0-1-1-1-1-2-2-1 0 1-1-1-3-3-4-5-9-5l4-4zm-74-663c1 0 2 1 3 1h2v1c1 0 1 0 2 1s3 1 4 1c2-1 2-1 3-1 0 3-3 3-3 6l-1-1v-1c-1 0-1 1-1 2-2 2-2 4-3 6-1 1-1 3-1 4-2-2-3-4-4-7 1-2 3-4 4-7-3-4-7 1-11-1v-2c0-1 1-1 1-1 1 0 2 1 4 1l1-2zm87 650h1l3-3c0 1 1 2 0 3h0l-1 3c1 2 1 3 3 4 0 1 1 1 1 2h0c-1 4-1 6-1 10l-13-12 7-7z" class="H"></path><path d="M772 879c1 2 1 3 3 4-1 1-2 2-3 2l-3-3c1-2 1-2 3-3z"></path><path d="M692 845c2-1 4-1 6-1 2 1 4 1 6 3l-7 8c-1 0-2 1-4 1s-4-1-5-3-1-4-1-6c2-1 3-2 5-2z" class="B"></path><path d="M646 837c2 1 2 1 3 2v9c0 7 3 14 6 20-1-1-10-8-10-8v-1c-1-4 0-9-1-13-1-1-1-2-1-3h0l1-2c0-1 0-3 1-4h1z" class="P"></path><path d="M630 172l1 1 1 1v1 3s0 1 1 1 1 0 1-2h2c2 1 2 3 5 3 3 7 7 12 13 19-3-2-10-4-12-7 0 0 0-2-1-2 0-3-1-6-3-8-2 0-4 3-5 4-3-2-4-5-6-8l3-6z" class="F"></path><path d="M630 837c1 1 2 2 3 2l5 11c1 2 2 3 4 5v-11c1 0 1 0 1-1 0 1 0 2 1 3 1 4 0 9 1 13v1l-16-11-2-2h0c1 0 1 0 2-1l-1-1v-3h0l-1-1v-1l2-1 1-1-1-1h1z" class="B"></path><defs><linearGradient id="X" x1="658.079" y1="815.604" x2="661.77" y2="818.459" xlink:href="#B"><stop offset="0" stop-color="#64615f"></stop><stop offset="1" stop-color="#8b8a89"></stop></linearGradient></defs><path fill="url(#X)" d="M644 829l2 1c4-1 7-9 10-12 7-9 17-18 28-21-7 4-12 10-18 15-4 4-8 7-11 11s-5 9-6 14v2c-1-1-1-1-3-2 1-2-1-5-2-7v-1z"></path><path d="M624 170l-3 5c-7-3-14-8-19-13 0-1 2-1 3-2h8c2 0 3 1 4 2 2 1 4 3 6 4 1 1 1 2 1 4z" class="D"></path><path d="M693 825v1c0 1 0 3 1 5 2 3 8 2 10 7 1 1 2 2 1 3h-2c-3-2-7-2-11-1-3 0-5 2-7 5l-1 1v4l-1-1c-1-2-1-6-1-8 1-2 2-4 4-5l1 1c2 0 4 0 7-1h-2c-2 0-4-1-5-2 1-3 3-7 6-9z" class="G"></path><path d="M613 830c1 0 3 0 4 1l2-1c1 0 3 0 5 1v1c2 1 4 3 6 5h-1l1 1-1 1-2 1v1l1 1h0v3c-1-1-3-2-4-3-2 0-4-1-5-2-4-2-9-3-13-4l-1 1 1-3c1-1 5-3 7-4z" class="H"></path><path d="M619 830c1 0 3 0 5 1v1h-1-5l-1-1 2-1z" class="E"></path><path d="M624 832c2 1 4 3 6 5h-1l1 1-1 1-2 1v1h0s-1 0-2-1c-2-1-4-3-6-4l4-4h1z"></path><path d="M625 840c1 0 1-1 1-1l-1-1v-1-1l-1-1h0c2 0 4 1 5 2l1 1-1 1-2 1v1h0s-1 0-2-1z" class="J"></path><path d="M643 818c-1-3-3-5-5-7 5-5 8-9 12-15h1l1 2c2 3 3 5 4 8l-1 4c-1 0-2-1-3-1h0c-1 2-1 4-1 6h0c-1-1-1-1-1-2-1 1-2 1-2 2-2 1-3 1-4 3h-1z" class="F"></path><path d="M652 798c2 3 3 5 4 8l-1 4c-1 0-2-1-3-1 1-3 0-8 0-11z" class="E"></path><path d="M632 815l7 8 3 2 2 4v1c1 2 3 5 2 7h-1c-1 1-1 3-1 4l-1 2h0c0 1 0 1-1 1l-2-6c-4-8-8-13-14-18l6-5z" class="F"></path><path d="M639 823l3 2 2 4v1c1 2 3 5 2 7h-1c-1 1-1 3-1 4l-1 2h0c0 1 0 1-1 1l-2-6 1-1c1-1 1-3 1-5v-1c0-3-1-5-3-8z" class="J"></path><path d="M644 830c1 2 3 5 2 7h-1c-1 1-1 3-1 4l-1 2c0-5 0-9 1-13z" class="O"></path><path d="M767 94h1c2-1 5-5 7-6v1c-1 1 0 4-1 5 0 1-1 1-1 2-1 1 0 7 0 9v13l2 1v1c-1-1-2-1-3-1-5-4-10-10-14-15 0-1 1-2 2-3l7-7z" class="E"></path><path d="M768 103c1-1 2-1 3 0v1c1 1 1 3 1 4l-2 1c-2-1-2-1-3-3 0-2 0-2 1-3z"></path><defs><linearGradient id="Y" x1="680.719" y1="893.561" x2="716.756" y2="903.035" xlink:href="#B"><stop offset="0" stop-color="#9a9898"></stop><stop offset="1" stop-color="#c9c8c6"></stop></linearGradient></defs><path fill="url(#Y)" d="M682 908c-2 0-6 0-7-1 2-2 5-8 6-12v-1c10 0 19 0 28-3 2 1 3 1 4 2 3 0 5-1 6 2v1h0c-6 3-15 4-21 5-2 0-4 0-5-1h-5l-1-1h-3l-1 1c0 2-1 3-3 4v1l-2-1c0 1-1 2-1 3h0c2 0 3 0 4 1h1z"></path><path d="M694 266c9 13 16 27 22 41h-1l-2 1c-1-1-2-2-3-4-1-1-2-4-3-6h-1l-2 3c-1-1-2-2-2-3v-2c0-2-1-4-2-5-1-2-1-3-2-5v-1l-3-3s0-1-1-2h0v-1c-1-1-1-2-2-3s-2-4-2-5c1-3 3-3 4-5z" class="J"></path><path d="M690 271l3 1c1 1 1 1 1 2l-2 2c-1-1-2-4-2-5zm10 20l2-2c1 1 2 1 2 3 1 1 1 1 0 2l-1 2h-1c0-2-1-4-2-5z"></path><path d="M752 893c5 0 6 2 9 5 1 2-1 0 1 1 0 1 1 1 1 2l1 3h1c1 2 3 3 3 6h-3l-27 1 6-9c3-3 5-6 8-9z" class="F"></path><path d="M751 900l1-1c1 0 3 0 3 1 2 1 2 2 2 4-1 1-1 1-3 2-1 0-3 1-4 0s-1-2-1-3c1-2 1-2 2-3z"></path><path d="M693 900c-3 1-6 1-9 1v1h2 1 1c4 1 9 1 13 0 10-1 19-3 29-5 11-3 25-16 31-26 0-1 1-2 3-2v-1c-8 17-26 31-44 37-12 3-26 4-38 3h-1c-1-1-2-1-4-1h0c0-1 1-2 1-3l2 1v-1c2-1 3-2 3-4l1-1h3l1 1h5z" class="B"></path><defs><linearGradient id="Z" x1="702.618" y1="721.166" x2="688.13" y2="716.684" xlink:href="#B"><stop offset="0" stop-color="#b3b2b1"></stop><stop offset="1" stop-color="#d6d4d3"></stop></linearGradient></defs><path fill="url(#Z)" d="M705 695c1 2 1 2 1 4 1-1 1-1 1-2 1 3 2 6 2 9-7 17-17 34-28 48l-1-1 2-3 2-2v-1l1-1v-1c1 0 1-1 2-1 0-1 2-3 2-5l-2-2c-1-1-1-1-2-1-1 1-2 2-3 2h-2c1-2 3-4 4-5l3-5v-1c1-2 1-3 2-3l2-3v-2c0-1 1-2 2-3 1-2 2-4 3-7 3-1 3-5 5-7l3 2c1-1 1-2 1-3s-1-2-2-3l2-3z"></path><path d="M691 721h1l1 1c0 2 0 2-1 3-1 0-2 0-3-1l2-3zm-4 7l2 3c0 1 0 1-1 3-2 0-2 0-4-1l3-5zm6-12c1-2 2-4 3-7l1 3 2-1h0c0 2-1 4-2 5-2 1-2 0-4 0z"></path><defs><linearGradient id="a" x1="700.416" y1="834.333" x2="720.894" y2="803.173" xlink:href="#B"><stop offset="0" stop-color="#cbc8c7"></stop><stop offset="1" stop-color="#f8f8f7"></stop></linearGradient></defs><path fill="url(#a)" d="M672 812c10-9 21-13 33-12 16 0 29 10 40 20 3 2 5 4 8 7v1c0 1-1 2-1 4-3-1-4-4-7-5 1 3 5 5 5 7-2-1-4-4-6-6l-2-2c0-2-3-5-5-6l-3-3-2-1-3-2-2-2c-3-1-5-2-7-3l-1-1-5-1-3-2c-1 0-1 0-2 1h0-2c-3-1-6 0-9 0h-1-3l-1 1-4 1c-1 0-1 0-2 1l-5 2-2 1h0c-2 1-3 1-4 2h0c-2 0-3 1-5 1l1-3z"></path><path d="M708 81h68l-10 8-11 11c-8-6-16-12-26-15-7-2-14-3-21-4z" class="B"></path><path d="M752 88h3c1 1 1 1 1 2l-1 2h-2l-2-2 1-2z"></path><defs><linearGradient id="b" x1="677.194" y1="79.549" x2="709.049" y2="99.796" xlink:href="#B"><stop offset="0" stop-color="#aeacac"></stop><stop offset="1" stop-color="#d1cfcf"></stop></linearGradient></defs><path fill="url(#b)" d="M698 100h-7c-4 0-8 1-12 1l-5-19c16-2 32-1 47 5-3 1-4 3-5 4-2 1-6 0-8 0 3 1 5 2 7 3l2 1h0c-2 1-5-1-7-1-4 0-7 2-10 3h-5c-2 0-2 0-4-1h0 0-5c-2 1-3 1-4 1 0 1 1 2 2 3h2c1-1 3-1 5-1h1 6v1z"></path><path d="M685 86h3l1 2c-2 1-2 1-4 1-1 0-2 0-3-1 0-1 2-1 3-2zm-2 7c4 0 8-1 12 0-1 1-3 1-4 2l-9 1c-1-1-1-2 0-3h1z"></path><path d="M712 851c0-3 2-7 1-11s-6-6-9-9h0l3 1c5 3 9 7 11 13 1 6 0 12-3 18-3 5-8 8-14 10-7 1-14 0-20-4-10-6-13-15-16-26l-1-1-1-1 1-2 1-1 2-3 4-1c1 2 1 7 2 9 0 3 1 5 1 7 2 4 3 7 5 9 1 1 3 2 3 3 2 2 4 3 7 4h0c2 1 7 1 9 1h1c2-1 5-1 7-3 4-3 5-7 6-12v-1z" class="C"></path><path d="M671 834c1 2 1 7 2 9 0 3 1 5 1 7 2 4 3 7 5 9-5-3-7-13-9-19-2 0-3-1-5-2l2-3 4-1z"></path><path d="M680 812h0l2-1 5-2c1-1 1-1 2-1l4-1 1-1h3 1c3 0 6-1 9 0h2 0c1-1 1-1 2-1l3 2 5 1 1 1c2 1 4 2 7 3l2 2 3 2 2 1 3 3c2 1 5 4 5 6l1 2c-1 1-2 2-2 3l3 3v1c1 1 4 3 5 4l-1 1c-1 0-2-1-3-2 0-1 1 0-1-1h-1c0-1-1-2-2-2-10-11-18-22-33-23-9 0-17 3-23 8-5 4-8 10-8 16-1 3-1 6 0 9h0c1 5 5 12 9 15 3 2 7 3 11 2 3-1 7-3 8-6 2-2 2-4 2-7 1 1 2 3 4 3l1-1v1c-1 5-2 9-6 12-2 2-5 2-7 3h-1c-2 0-7 0-9-1h0c-3-1-5-2-7-4 0-1-2-2-3-3-2-2-3-5-5-9 0-2-1-4-1-7-1-2-1-7-2-9-2-1-3-3-4-5 0-1 1-3 2-4 1-2 2-3 2-5h0c1-2 3-4 6-5 0-1 1-1 2-2l1-1z" class="F"></path><defs><linearGradient id="c" x1="657.003" y1="835.271" x2="725.384" y2="866.031" xlink:href="#B"><stop offset="0" stop-color="#a19f9e"></stop><stop offset="1" stop-color="#cbc9c9"></stop></linearGradient></defs><path fill="url(#c)" d="M676 814h0c1-1 2-1 4-2l-1 1c-1 1-2 1-2 2-3 1-5 3-6 5h0c0 2-1 3-2 5-1 1-2 3-2 4 1 2 2 4 4 5l-4 1-2 3-1 1-1 2 1 1 1 1h0c-2 2-1 8-1 10 1 7 4 14 10 19 7 5 16 7 25 6 7-1 15-6 19-12 5-7 8-18 7-27-1-5-4-10-8-12-5-4-11-5-17-4h-1 0c2-1 4-2 6-2 5-2 13 0 17 3 8 5 11 12 15 19 1 2 3 5 4 7-1 4-3 8-5 12-1-1-3-1-5 0l-1 1h2v2c0 1 0 2 1 3-4 6-9 10-14 13-1 0-3 1-4 2-9 5-23 8-34 4-10-3-19-13-24-23-4-8-4-20-1-29s9-17 16-23l-1 3c2 0 3-1 5-1z"></path><path d="M664 839c0-2-1-5 0-6 1 1 2 1 3 2l-2 3-1 1z" class="B"></path><path d="M676 814h0c1-1 2-1 4-2l-1 1c-1 1-2 1-2 2-3 1-5 3-6 5h0c0 2-1 3-2 5-1 1-2 3-2 4 1 2 2 4 4 5l-4 1c-1-1-2-1-3-2l1-2v-1c0-1 1-1 1-2l1-1v-2c-2 2-3 4-4 7l-1-1c1-2 2-3 3-5v-1h1c1-2 3-3 4-5v-1c1-1 1-2 2-2s1-1 2-1c0-1 1-1 2-2h0z" class="J"></path><defs><linearGradient id="d" x1="659.398" y1="823.573" x2="668.986" y2="825.504" xlink:href="#B"><stop offset="0" stop-color="#989693"></stop><stop offset="1" stop-color="#bcb7b8"></stop></linearGradient></defs><path fill="url(#d)" d="M672 812l-1 3c2 0 3-1 5-1h0c-1 1-2 1-2 2-1 0-1 1-2 1s-1 1-2 2v1c-1 2-3 3-4 5h-1c-2 2-3 3-4 6 0 3-1 4-2 6s-1 4-2 5c0-1 0-5-1-7 3-9 9-17 16-23z"></path><path d="M727 861v-1c1 0 2-1 2-2l1-2h0c0 2-1 4-1 6 0 1 0 1 1 1h2v2c0 1 0 2 1 3-4 6-9 10-14 13l-1-1c1 0 2-1 2-2-1-1-2-1-3-2-1 1-1 1-2 1 1-2 3-3 5-5 3-2 4-5 6-9 0 0 1-1 1-2z" class="D"></path><path d="M722 824c8 5 11 12 15 19 1 2 3 5 4 7-1 4-3 8-5 12-1-1-3-1-5 0l-1 1c-1 0-1 0-1-1 0-2 1-4 1-6h0l-1 2c0 1-1 2-2 2v1c0-2 1-7 2-8 1-2 1-2 1-3 1-1 1-8 0-9v-1-2l-1-2-1-4-1-1c0-1-2-2-2-3h0 1c-2-1-3-2-4-4z" class="J"></path><path d="M728 832c1 1 2 1 2 3l1 4c1 1 1 2 1 3s1 4 1 5c1 1 0 1 1 1s1 0 2 1c0 1 0 3-1 4-1 3-1 4-3 7l-1 2-1 1c-1 0-1 0-1-1 0-2 1-4 1-6h0l-1 2c0 1-1 2-2 2v1c0-2 1-7 2-8 1-2 1-2 1-3 1-1 1-8 0-9v-1-2l-1-2-1-4z" class="N"></path><path d="M673 93c0 2 1 4 0 7h0c-1 4-5 8-7 11-7 8-14 16-17 26 0-1 0-2-1-2-1 1-2 2-2 4l-1 1v2 1c-1 2-1 10 0 12v1c0 1 1 2 1 3v3l1 4 1 1 2 5h0c1 3 3 5 5 7l4 5c1 2 7 6 7 8 1 2 1 3 3 3h2c1 0 3 1 4 0l1 1c1 0 1 0 2 1l4 1c3 1 7 3 10 4h2c1 1 3 1 4 2h2l1 1h3c2 0 5 1 8 0h2l2-1c6-2 13-6 18-10 2-1 3-2 4-3 2-1 3-3 5-4v1l-9 9c-2 2-4 4-5 6-5 4-9 8-15 11-1 1-3 3-5 3l-4 4c-1 4-6 7-8 10-1 2-2 3-4 4 0-3 3-3 3-6-1 0-1 0-3 1-1 0-3 0-4-1s-1-1-2-1v-1h-2c-1 0-2-1-3-1l-1 2c-2 0-3-1-4-1 0 0-1 0-1 1v2c-2-1-4-3-6-5-6-5-12-12-17-18 9 3 19 7 30 8 9 1 23 1 30-6l-1 1c-5 1-10 2-15 1-6-1-12-2-18-4-15-6-27-16-34-31-3-8-5-18-4-27 0-3 0-6 1-9h0c1-3 5-8 5-10l-6 9c-2 3-5 5-8 7v1c1 1 2 1 3 1s3-3 4-3l-1 6c-1 1-1 3-1 4l-2 4h0c-2 1-3 2-5 4l-7 7c0-2 0-3-1-4-2-1-4-3-6-4-1-1-2-2-4-2h-8c10-5 21-9 29-17 5-4 8-8 11-13 1-2 3-4 3-6 5-6 11-12 17-18 2-3 5-6 5-10 2-1 2-1 3-3z" class="C"></path><path d="M629 155v-1l3-3c1 1 2 1 3 1 1-1 2-1 4-1-1 1-1 3-1 4-2-1-3-1-5-1-2 1-3 1-4 1z" class="J"></path><path d="M617 162v-1c1 0 2 1 4 1v-1c1-2 1-2 3-2v1c1 1 2 1 2 3l-3 3c-2-1-4-3-6-4z" class="B"></path><defs><linearGradient id="e" x1="631.987" y1="154.361" x2="628.568" y2="167.901" xlink:href="#B"><stop offset="0" stop-color="#bab5b4"></stop><stop offset="1" stop-color="#d5d5d5"></stop></linearGradient></defs><path fill="url(#e)" d="M633 154c2 0 3 0 5 1l-2 4h0c-2 1-3 2-5 4l-7 7c0-2 0-3-1-4l3-3c0-2-1-2-2-3v-1h1l1-1h1v-1c0-1 1-1 2-2 1 0 2 0 4-1z"></path><path d="M629 155c1 0 2 0 4-1-2 3-3 4-5 6h-1l-2-1 1-1h1v-1c0-1 1-1 2-2z" class="L"></path><path d="M666 192c1 2 1 3 3 3h2c1 0 3 1 4 0l1 1c1 0 1 0 2 1l4 1c3 1 7 3 10 4h2l1 2v2l1 1h0c-7 1-12-1-18-4h0c-2 0-7-3-8-4s-2-2-2-3l-2-1v-3z" class="G"></path><path d="M678 203c-2-2-5-3-7-5 8 4 16 6 24 8l1 1h0c-7 1-12-1-18-4zm0 20c-4-1-8-4-12-6 6 2 13 3 20 2 4 0 7-1 11-1 3 0 7 1 10 0 1-1 1-1 2-1l-4 4c-1 4-6 7-8 10-1 2-2 3-4 4 0-3 3-3 3-6-1 0-1 0-3 1-1 0-3 0-4-1s-1-1-2-1v-1h-2c-1 0-2-1-3-1-1-1-1 0-2-1l-2-2z"></path><path d="M678 223l13 3c1 0 3 1 4 0 4-1 7-4 10-5-1 4-6 7-8 10-1 2-2 3-4 4 0-3 3-3 3-6-1 0-1 0-3 1-1 0-3 0-4-1s-1-1-2-1v-1h-2c-1 0-2-1-3-1-1-1-1 0-2-1l-2-2z" class="J"></path><path d="M721 87c13 6 26 14 36 25 7 9 13 20 17 30l5 14c1 3 1 6 1 9l-3 3-8 3c-13 8-23 18-33 30-8 11-14 21-18 34-3 12-5 25-5 38 0 4 2 10 1 14l-14-26c0-2 1-2 2-3 0-1 1-1 1-2v-1l-4-1c1-9 7-15 13-21 1-4 2-7 2-11h0c-7 2-11 9-14 15s-4 12-5 19l-8-11c0-1 0-3 1-4 1-2 1-4 3-6 0-1 0-2 1-2v1l1 1c2-1 3-2 4-4 2-3 7-6 8-10l4-4c2 0 4-2 5-3 6-3 10-7 15-11 1-2 3-4 5-6l9-9v-1c-2 1-3 3-5 4-1 1-2 2-4 3-5 4-12 8-18 10l-2 1h-2c-3 1-6 0-8 0h-3l-1-1h-2c-1-1-3-1-4-2h-2c-3-1-7-3-10-4l-4-1c-1-1-1-1-2-1l-1-1c-1 1-3 0-4 0h-2c-2 0-2-1-3-3 0-2-6-6-7-8l-4-5c-2-2-4-4-5-7h0l-2-5-1-1-1-4v-3c0-1-1-2-1-3v-1c-1-2-1-10 0-12v-1-2l1-1c0-2 1-3 2-4 1 0 1 1 1 2l-1 9c-1 11 4 26 11 35 5 5 12 6 19 9 3 2 6 4 10 6a34.53 34.53 0 0 0 18 5c15 1 24-8 33-19l11-12c4-4 7-9 12-13-5-11-10-23-17-32-12-15-27-23-47-25v-1h-6-1c-2 0-4 0-5 1h-2c-1-1-2-2-2-3 1 0 2 0 4-1h5 0 0c2 1 2 1 4 1h5c3-1 6-3 10-3 2 0 5 2 7 1h0l-2-1c-2-1-4-2-7-3 2 0 6 1 8 0 1-1 2-3 5-4z" class="D"></path><path d="M769 171c1-2 1-3 3-5l4-4c0 2 0 4 1 6l-8 3z" class="H"></path><path d="M776 160l1-1c-1-2-2-4-3-5l-2-3v-1c2 2 4 5 6 6h1c1 3 1 6 1 9l-3 3c-1-2-1-4-1-6v-2z" class="J"></path><path d="M709 245v4h-2l-1 1c1 1 1 1 2 1h1c1 3 0 9 0 11v1l-3-3c0-1 0-2-1-3 0-1 0-1-1-2v-1l-1-1c-1-2 0-3 0-5h3c1-1 2-1 3-3zm67-85c-1-1-2-1-4 0h0-1c0-1 1-1 1-2h1 0c0-1-1-2-2-2-1-1-2-3-3-4-3-5-4-9-6-15-1-2-2-5-2-7 1 3 3 6 5 9 2 4 5 7 7 11v1l2 3c1 1 2 3 3 5l-1 1z" class="G"></path><path d="M699 254c1-9 7-15 13-21 0 1 0 2-1 4h-1l-1 8c-1 2-2 2-3 3h-3c0 2-1 3 0 5l1 1v1c1 1 1 1 1 2 1 1 1 2 1 3-1-1-2-3-3-4v-1l-4-1z" class="C"></path><path d="M710 237l-1 8c-1 2-2 2-3 3h-3l3-3c0-1 1-2 1-2v-1c1-1 1-1 1-2s2-2 2-3z" class="H"></path><path d="M702 754c-2-3-3-6-3-9 1-5 4-9 6-13l8-17c0 27 10 53 30 73 5 6 12 12 19 16 1 1 3 2 4 3 4 2 11 6 13 10 0 1 0 4 1 4v8c-1 2-2 5-3 7-3 11-7 22-13 32v1c-2 0-3 1-3 2-6 10-20 23-31 26-10 2-19 4-29 5-4 1-9 1-13 0h-1-1-2v-1c3 0 6 0 9-1 1 1 3 1 5 1 6-1 15-2 21-5h0v-1c-1-3-3-2-6-2-1-1-2-1-4-2l8-3c12-6 21-15 28-27 3-7 6-15 9-22l9-18c-4-4-8-7-12-10-10-7-21-14-32-18-18-7-35-4-52 3-1 1-3 1-4 2-3-2-5-6-6-9 2-3 8-9 12-11 4-1 17-1 21 1h1 0c0-2 0-3-1-4-3-2-6-4-8-6-1 0-1-1-2-2 2-3 5-7 7-9 2-4 4-7 6-10l3 7c3 8 8 14 14 20 3 3 5 6 9 7 2 0 3 1 4 0h1l-1-5h0c-2-3-4-6-6-8-3-2-5-4-8-6s-5-6-7-9z" class="F"></path><path d="M703 774c1 2 3 3 5 4h-2-4l1-4z" class="L"></path><path d="M709 891l8-3-2 2h0c1 0 2 0 3 1 3-1 7-2 10-2-2 3-6 6-9 7h0v-1c-1-3-3-2-6-2-1-1-2-1-4-2zm65-60h1c1 1 1 1 0 2l-4 8c0 1-1 2-2 2 0 2-1 3-2 4 0 1-1 2-2 3l-2 5h-1c0-2 1-3 1-5-1-1 0-1-1-1h0l-1-1c3-2 4-5 6-7 1-2 1-3 2-5h0c1-1 1-2 2-2 1-1 2-1 2-2l1-1z" class="H"></path><path d="M702 774h1l-1 4h4 2l1 1c0 2-2 2-1 4h0c2 1 3 2 5 2l1 1h-1-1l-6-3c-4-1-10-1-14-1h6c-3-1-5-1-7-1l-1-1h2 4c0-2-1-4-1-5l1-1 1 1 5-1z" class="C"></path><path d="M696 780c0-2-1-4-1-5l1-1 1 1s0 2 1 3h4 4-6c-2 0-3 1-4 2z" class="P"></path><path d="M702 774h1l-1 4h-4c-1-1-1-3-1-3l5-1z"></path><path d="M706 751c1 3 1 5 3 7 0 1 0 2 1 3 1 2 3 3 5 4h0c1 0 1-1 1-1-1-2-1-3-1-5v-1l4 7c2 6 6 11 10 16l-1 1c-1-2-3-3-4-5h-1 0c-2-3-4-6-6-8-3-2-5-4-8-6s-5-6-7-9c1 1 2 1 2 2l1 1v-1-1c0-1 0-2 1-3v-1z" class="H"></path><path d="M706 748c0-1 0-2 2-3h1v-1c1 2 2 3 2 5l3 7 1 2v1c0 2 0 3 1 5 0 0 0 1-1 1h0c-2-1-4-2-5-4-1-1-1-2-1-3-2-2-2-4-3-7v-3z" class="E"></path><path d="M706 748c0 1 1 1 1 1 0 2 0 3 1 4v2l2 2c2 3 3 6 5 8h0c-2-1-4-2-5-4-1-1-1-2-1-3-2-2-2-4-3-7v-3z" class="K"></path><defs><linearGradient id="f" x1="678.258" y1="773.91" x2="667.44" y2="795.692" xlink:href="#B"><stop offset="0" stop-color="#a9a6a6"></stop><stop offset="1" stop-color="#c8c7c6"></stop></linearGradient></defs><path fill="url(#f)" d="M667 796c-1 1-3 1-4 2-3-2-5-6-6-9 2-3 8-9 12-11 4-1 17-1 21 1v1l1 1c2 0 4 0 7 1h-6l-17 3c-3 0-6 2-8 4h-1c-1 0-2 0-2 1-1 0-1 0-1 1v1c-1 0-1 0-1 1h0-2l1 1 2 3 2-1h1 1z"></path><defs><linearGradient id="g" x1="682.259" y1="767.488" x2="692.326" y2="762.76" xlink:href="#B"><stop offset="0" stop-color="#b2b0af"></stop><stop offset="1" stop-color="#d2d0d0"></stop></linearGradient></defs><path fill="url(#g)" d="M682 769c-1 0-1-1-2-2 2-3 5-7 7-9 2-4 4-7 6-10l3 7h-1l-2-5-3 5 1 2v1l2 2 2 2h-1v1c0 2 1 3 3 4 1 1 1 1 1 3 2 1 3 2 4 4l-5 1-1-1-1 1c0 1 1 3 1 5h-4-2v-1h1 0c0-2 0-3-1-4-3-2-6-4-8-6z"></path><path d="M682 769c1 0 2 1 3 1 2 0 3 0 4 1 2 0 3 0 5 1 1 0 2 0 3 1h-3c-1 0-2 1-3 1l-1 1c-3-2-6-4-8-6z" class="O"></path><path d="M694 768v-1h0l4 3c2 1 3 2 4 4l-5 1-1-1-1 1c0 1 1 3 1 5h-4-2v-1h1 0c0-2 0-3-1-4l1-1c1 0 2-1 3-1h3c-1-1-2-1-3-1v-4z" class="S"></path><path d="M694 768c0 1 1 2 2 2h0c1 0 2 1 3 1v1c-1 0-1 1-2 1-1-1-2-1-3-1v-4z" class="C"></path><defs><linearGradient id="h" x1="674.438" y1="112.673" x2="721.896" y2="176.246" xlink:href="#B"><stop offset="0" stop-color="#bab8b8"></stop><stop offset="1" stop-color="#f8f7f7"></stop></linearGradient></defs><path fill="url(#h)" d="M694 189c-13-3-24-9-32-21-6-8-8-17-7-27 2-12 11-23 20-30 10-6 22-7 33-5 14 3 25 11 33 23 6 7 9 15 12 24-3 1-5 3-7 6-9 12-16 29-33 32-6 1-13 1-19-2z"></path><path d="M668 160l2-2 1 7c-2-2-2-2-3-5z" class="T"></path><path d="M665 161c1-1 1-2 2-2 2-5 3-9 4-14h0c0 1 0 2-1 3-1 3 0 7 0 10l-2 2h-1c-1 1-1 1-2 1z" class="V"></path><path d="M665 161c1 0 1 0 2-1h1c1 3 1 3 3 5 0 1 1 2 1 3s1 3 0 4l-1-2c-2-3-6-5-8-8l1-1 1 1v-1zm76-32c6 7 9 15 12 24-3 1-5 3-7 6l-1-1h-4v1c-2 0-3-1-4-2-1-3 0-6 0-9 1-3 0-7 0-10h0c-1-1-1-3-1-5v-3h1c3 1 3 3 5 5 1 1 2 2 2 4 1 1 2 3 3 4 0 0 1 0 1 2 1 0 1 1 1 1 0-2-2-5-3-7s-3-5-4-7c-1-1-1-2-1-3z" class="H"></path><path d="M743 149c1 0 2 0 2-1 1 1 2 1 2 3l-2 1c-1 1-1 1-2 1l-1-1c0-2 0-2 1-3z"></path><path d="M736 130h1c0 2 0 2 1 3 0 1 1 2 1 3h1c1 3 1 8 1 10-1-2-2-5-4-8h0c-1-1-1-3-1-5v-3z" class="Q"></path><path d="M706 117c0-1 1-1 2-2 5 0 9 0 14 2l1 1c4 2 8 5 11 9l1 1v1l1 1v3c0 2 0 4 1 5h0c0 3 1 7 0 10 0 3-1 6 0 9 1 1 2 2 4 2v-1h4l1 1c-9 12-16 29-33 32-6 1-13 1-19-2h1l2-1c2-1 4 0 7 0 1-1 2-1 3-1h3c0-1 1-1 2-1h-1v-2l-1 1c-2 0-5 0-8-1l4-1c8-2 15-6 19-13 5-8 7-18 5-27h0l-2-5c-4-10-12-17-22-21z" class="D"></path><path d="M725 156v-1c1-7-1-14-5-20s-10-11-18-13c-8-1-17 0-24 5-9 6-12 15-14 25-2-5-2-8-1-13 2-8 8-16 15-20 9-5 18-4 28-2 10 4 18 11 22 21l2 5h0c2 9 0 19-5 27-4 7-11 11-19 13l-4 1h-5c-6 0-11-1-16-6-6-7-8-15-8-24 3 2 5 5 7 8l1-1-1-2c-3-5-4-12-3-17 2-4 4-7 8-9 5-3 12-4 17-2 4 1 6 3 8 7 0 3 0 4-1 7h1l2-1c3 4 3 9 5 13l1 2c0 1 0 3 1 4 1-3 2-6 4-9 0 2 0 1 2 2z"></path><path d="M688 158c3 0 4 1 7 2l4 4c-5-1-8-2-11-6z" class="G"></path><path d="M717 157l1 2c0 1 0 3 1 4v3c-2 3-4 5-7 7 1-3 3-5 4-7l1-1c1-3 0-5 0-8z" class="B"></path><path d="M719 163c1-3 2-6 4-9 0 2 0 1 2 2-1 4-3 7-6 10v-3z" class="N"></path><path d="M697 158c1 0 3 0 4-1s2-1 3-1c0 2-1 7-3 8h-2l-4-4 1-1 1-1z" class="F"></path><path d="M697 168c5 0 8 0 11-4 2-1 3-3 5-5-2 4-3 9-8 11-3 2-7 2-11 1h1c-1-1-1-1-1-2 1-1 2 0 3-1z" class="P"></path><defs><linearGradient id="i" x1="688.478" y1="164.378" x2="687.013" y2="175.189" xlink:href="#B"><stop offset="0" stop-color="#8e8c8c"></stop><stop offset="1" stop-color="#b0afae"></stop></linearGradient></defs><path fill="url(#i)" d="M681 161c2 2 3 4 5 5 3 2 8 2 11 2-1 1-2 0-3 1 0 1 0 1 1 2h-1c0 3 1 5 1 7l-6-3c-4-3-7-9-9-13l1-1z"></path><path d="M710 138c0 3 0 4-1 7h1c-2 4-4 7-6 11-1 0-2 0-3 1s-3 1-4 1h0c0-1 2-2 3-2-1-1-1-2-2-2s-1 0-2-1h-1c-1 0 0 0-1-1h-1c3 0 6-2 8-3 3-3 6-7 9-11z" class="B"></path><path d="M701 149c1 0 3 0 4-1 1 0 1-1 3-1-1 2-2 3-4 4s-3 3-4 5c-1-1-1-2-2-2s-1 0-2-1h-1c-1 0 0 0-1-1h-1c3 0 6-2 8-3z" class="C"></path><defs><linearGradient id="j" x1="703.275" y1="137.687" x2="687.185" y2="155.834" xlink:href="#B"><stop offset="0" stop-color="#c1bfbf"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#j)" d="M688 158c-1-1-1-2-2-4s-1-7 0-9c2-4 9-8 14-9 1 0 5 0 6 1-1 1-4 2-6 2-2 2-6 3-8 5-1 2-1 3-1 5s1 2 2 3h0 1c1 1 0 1 1 1h1c1 1 1 1 2 1s1 1 2 2c-1 0-3 1-3 2h0l-1 1-1 1c-3-1-4-2-7-2z"></path><path d="M131 130c1-5 0-11 0-16V76l117 1c-7 6-8 12-10 21 2 4 8 8 11 11l9 10c1 1 3 3 3 4 7 6 13 11 20 16 15 10 32 15 48 22-33 21-62 47-86 77-7 9-13 19-19 28l-11 17c-1 2-3 5-4 8h0c0 1 0 2-1 3h0c0 1-1 1-1 2l-2 4-1 2v1l1-1c1 0 2-1 2-1h0l1-2v-2c1-1 1-2 2-2h1l3 1c0 3-3 7-4 9l-9 19c-6 13-11 27-15 40-11 41-15 83-16 125-2 31-1 62 3 93 2 19 7 38 12 57 5 22 11 44 22 63l2 3h0c1 2 2 3 2 5l-2 2v1c1 1 1 1 3 1l-2 1c1 2 1 4 3 4v1l-1 1c0 1 0 2-1 3 3 5 7 11 11 16a229.82 229.82 0 0 0 35 41c13 13 26 25 41 35l18 12 7 4c1 0 2 1 3 2-6 4-15 7-23 10-15 6-29 14-42 25l2-7 2-1v-7c-1-2-1-3-1-5 0-1 0-3 1-4h1c0-3-1-5-1-7-1-7-4-12-8-17l-4-3-1-1h-2c-4-4-9-9-14-11-3-1-7-3-10-2 3 1 5 3 7 4-1 0-2 0-3-1h0-1 0c-1-1-1 0-1-1-1-1-1 0-2-1h0-2l-1-1h-4v1h1c1 0 1 0 2 1h1l4 3v1c2 2 4 4 7 6 2 0 4 2 5 3l1 1 2 2 2 2c1 1 0 0 1 2 1 1 3 2 3 4 1 1 2 1 3 3l1 2h0c1 1 2 3 2 5 4 10 8 20 4 31-2 3-4 6-7 9l-10 11c-2 3-2 7-1 10 2 5 8 10 12 14-6 0-12-1-18 0H131v-22c-1-8 0-17 0-25v-6c-1-2-1-5-1-7 1-1 0-2 0-3v1s0 1-1 2v1h-1v1h1v3c0-1 0-2-1-3l-1 1h-1c1-2 0-3 0-4s1-1 1-2h2 0l-2-1v-1c-1-2 0-5 0-7v-24c-1-1-2-3-3-4v-1h3 1c2 0 4-2 6-3 12-10 23-24 31-38l1-1 1-1c0-2 0-3 2-4v-1-1l-1 1v-3l3-9h0l1-3c0-4 2-9 3-13 2-4 2-9 3-14 0-4 1-8 1-12v-23-3l-12-106h0c-1-4-1-9-1-13 0-2-1-3 0-4h0c0-3 0-5 1-7l3-123c2-5 2-10 3-15l2-19c3-10 3-21 5-31l6-51c1-13 2-26 1-39 0-13-2-27-6-40-7-22-20-42-37-57-3-3-6-6-9-8-1-1-3-2-3-3-1 0-1-1-1-2 1-2 0-5 0-7v-16-15z"></path><path d="M223 844c2 4 5 7 4 11h0c-2-1-3-3-4-4 1-2 0-5 0-7z" class="G"></path><path d="M199 309l2 2c2 0 2 0 4 1 0 0-1 0-1 1-1 0-2-1-3 1 0 1 0 1-1 2-1-1-2-1-3-2l2-5z" class="J"></path><path d="M275 829c1 0 2 0 4-1 1-1 2-1 4-1-4 4-7 9-10 14l2-12z" class="E"></path><path d="M275 810c1 2 2 4 3 5s2 1 3 2c-1 0-2 1-3 1 0-1-1-1-1-1-1 1-1 2-1 3 1 2 3 2 2 5 0 1-1 1-1 2h-2c0-2 1-4 0-7v-9-1z" class="G"></path><path d="M218 771c2-1 3-2 5-3h1v-1c1-1 1-1 3-1l2 2c-2 2-4 3-6 4 4 0 8 0 11 1l-5 1h-2c-1 0-2 0-3-1-3 0-4-2-6-2z" class="H"></path><path d="M212 800c8-1 18 0 25 4h-3c-1-1-1-1-2-1-2-1-4 0-6 0l-1-1-2 1c-2 0-4-1-6-1s-3 0-5-2z" class="K"></path><path d="M299 827l12 7c-2 1-4 2-6 2-1-1-4-3-6-5v-4z" class="X"></path><path d="M205 733v5c1-1 2-1 3 0 2 1 3 4 4 5-1 2-1 3-2 4 0-1 0-3-1-4l-2 2-1 1c-1 0-2 0-3-1l1-3c0-2 0-6 1-9z" class="B"></path><path d="M204 742l3 2v1l-1 1c-1 0-2 0-3-1l1-3z"></path><path d="M136 882v-20l6 9c-3 3-5 7-6 11z" class="N"></path><path d="M200 316c-2 2-2 5-3 8l-3 10c-1 2-1 4-3 6 2-9 3-17 6-26 1 1 2 1 3 2z" class="O"></path><path d="M294 822l5 5v4c2 2 5 4 6 5l-5 2c-1-1-2-2-3-4v-2-1c-2-3-3-5-3-9z" class="P"></path><path d="M270 145l2-1c0 5 1 7 3 10l4 11-1 1c-1-1-1-3-2-4l1 5-1-1h-1v7c0-9-2-19-5-28z" class="K"></path><path d="M224 758c3 2 6 5 7 7l-2 3-2-2c-2 0-2 0-3 1v1h-1c-2 1-3 2-5 3-1 0-1 0-2-1 1-1 2-2 3-2s2-1 3-1c0-1 1-2 0-2 0-4 1-4 2-7z" class="J"></path><path d="M196 696c0-1-1-3-1-3-3-7-5-14-6-22 4 6 5 15 10 20 1 1 1 0 1 1l-3 3h0l-1 1z" class="O"></path><path d="M171 902l15 9h-22c1-1 4-1 5-1l2-8z" class="J"></path><path d="M199 309c3-6 6-12 10-18 0 1 0 2-1 3h0c0 1-1 1-1 2l-2 4-1 2v1l1-1c1 0 2-1 2-1h0l1-2v-2c1-1 1-2 2-2h1c-1 2-2 3-2 4l2 2c-1 2-2 3-3 4s-2 1-3 1c-1 1-2 1-2 3l3 1v1l-1 1c-2-1-2-1-4-1l-2-2z" class="C"></path><path d="M281 817c2 0 4 0 5 2 1 0 0 1 0 2 0 2-2 4-3 6-2 0-3 0-4 1-2 1-3 1-4 1v-2h2c0-1 1-1 1-2 1-3-1-3-2-5 0-1 0-2 1-3 0 0 1 0 1 1 1 0 2-1 3-1z" class="F"></path><path d="M215 836c-3 1-5 2-7 3l-1-1c1-4 6-5 8-8 1-1 2-3 1-5 0-2-1-4-2-5l-3-2c3 1 5 2 7 4v1c1 1 1 1 2 1 0 1 0 1 1 2l1-1v1l-3 5s0 1-1 2l2 2v1h-5z" class="C"></path><defs><linearGradient id="k" x1="237.798" y1="802.108" x2="251.447" y2="818.69" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#a5a4a1"></stop></linearGradient></defs><path fill="url(#k)" d="M223 803l2-1 1 1c2 0 4-1 6 0 1 0 1 0 2 1h3c6 2 12 7 17 12 1 1 3 3 3 4v1l-4-3-1-1h-2c-4-4-9-9-14-11-3-1-7-3-10-2l-3-1z"></path><path d="M167 81c7-1 15-1 22 0-10 2-19 6-26 13l-6 6c0-1 0-3 1-4 1-2 3-3 4-4 3-4 7-7 10-10-1-1-3-1-5-1z" class="J"></path><path d="M142 871l4 4c-2 0-4-1-5 0s-1 2-1 3c-2 3-2 4-2 7 0 1 1 2 1 3 1 1 1 2 1 3l-1 1h0l3 3c-1 1-2 3-2 6h0l-4 5v-24c1-4 3-8 6-11z" class="C"></path><path d="M165 828c1-1 2-2 4-2 0 1 1 1 2 2-1 3-2 4-4 7l-1 1v1h0 2v1c0 1 1 1 1 2h0l-3 7c-1-2-2-4-2-5-2-4-5-10-5-14 0 0 2-1 2-2 2 1 3 2 4 2z" class="P"></path><path d="M165 828c1-1 2-2 4-2 0 1 1 1 2 2-1 3-2 4-4 7-1-2-2-3-3-4 0 1 0 1-1 2h0v1l-1-2c0-2 2-3 3-4z" class="K"></path><path d="M218 822l1 1c2-1 2-1 4-1 1 1 1 2 1 4s-1 5-2 6c1 1 1 1 2 1 3 0 8 2 11 4h0c-1 1 1 5 0 6l-2 1c-1 0-3-3-4-4l-1 1c-5-4-7-5-13-5h5v-1l-2-2c1-1 1-2 1-2l3-5v-1l-1 1c-1-1-1-1-1-2-1 0-1 0-2-1v-1z" class="O"></path><path d="M235 843c-1-1-2-2-2-4-1-3-10-2-13-5 1-2 0-1 2-2 1 1 1 1 2 1 3 0 8 2 11 4h0c-1 1 1 5 0 6z" class="K"></path><path d="M275 173v-7h1l1 1c0 8 1 14-2 21 0 1 0 2 1 3h0c-4 4-9 7-14 9 7-8 10-17 13-27z" class="Q"></path><path d="M210 845c2-2 5-4 8-4 2 1 4 2 5 3 0 2 1 5 0 7-1-1-2-1-3-1-2 4-1 5-1 9 0 0-1 0-2-1-3-1-5-7-6-10 0-1 0-2-1-3z" class="M"></path><path d="M197 695l2 2c2 0 2 0 2-2 1 1 2 2 2 3 0 2-3 2-1 5l1 1c2 0 3-1 4-2l2 3h0c1 2 2 3 2 5l-2 2v1c1 1 1 1 3 1l-2 1c1 2 1 4 3 4v1l-1 1c0 1 0 2-1 3l-15-28 1-1z" class="H"></path><path d="M209 712h-1c-1-1-1-2-2-3 1-2 2-3 3-4 1 2 2 3 2 5l-2 2z"></path><path d="M277 167l-1-5c1 1 1 3 2 4l1-1c2 6 6 11 8 17-3 4-7 7-11 9h0c-1-1-1-2-1-3 3-7 2-13 2-21z" class="D"></path><defs><linearGradient id="l" x1="280.849" y1="173.259" x2="275.151" y2="178.241" xlink:href="#B"><stop offset="0" stop-color="#1f1d1b"></stop><stop offset="1" stop-color="#323338"></stop></linearGradient></defs><path fill="url(#l)" d="M277 167l-1-5c1 1 1 3 2 4 2 4 3 10 2 14-1 3-3 5-5 8 3-7 2-13 2-21z"></path><defs><linearGradient id="m" x1="169.982" y1="845.579" x2="184.258" y2="859.664" xlink:href="#B"><stop offset="0" stop-color="#9e9c9b"></stop><stop offset="1" stop-color="#d4d3d1"></stop></linearGradient></defs><path fill="url(#m)" d="M178 868c-3-4-7-13-7-19-1-6 4-9 8-13l-4 10 1 1h1c1 4 2 10 5 14h0l1 1c-2 2-1 2-2 3l-3 3z"></path><path d="M140 878c0-1 0-2 1-3s3 0 5 0c2 3 5 6 7 9-3 4-6 8-11 11l-3-3h0l1-1c0-1 0-2-1-3 0-1-1-2-1-3 0-3 0-4 2-7z" class="F"></path><path d="M140 878c1-1 2-1 3-1 2 0 3 1 4 2l-1 1h-6v-2z" class="D"></path><path d="M142 882h0c1 0 3 0 4 1s0 2 1 3c-1 1-1 1-3 2h-1c-2 0-3-2-3-3s0-2 2-3z"></path><path d="M260 812c3-5 5-10 5-16 4 4 8 9 10 14v1c-1 5-2 10-4 14h-1c-2-2-2-4-4-6v-1c2 0 3 0 3-1 0-2-1-2-3-2s-2 0-4 1h-1l-1-4z" class="F"></path><path d="M207 745l2-2c1 1 1 3 1 4 1-1 1-2 2-4 1 4 1 6 0 9-4 8-12 15-18 21h-1c-1 0 0 0-1-1h0c2-2 3-5 4-7 0 0 1-1 1-2l1-1v-1c1-1 1-2 2-3v-1c1-3 1-9 3-12 1 1 2 1 3 1l1-1z" class="I"></path><path d="M207 745l2-2c1 1 1 3 1 4 1 1 1 1 0 2v1c-2 3-6 5-8 8 0 1-1 2-2 2 0 1-1 2-1 2l-2 2-1 1s1-1 1-2l1-1v-1c1-1 1-2 2-3v-1c1-3 1-9 3-12 1 1 2 1 3 1l1-1z" class="G"></path><path d="M282 157l5 3c2 2 4 4 6 7l1-1v-2h0l7-4 2-1 8 5c-2 1-5 3-6 4l-11 8c-5-6-10-12-14-18l2-1z" class="D"></path><path d="M260 812l1 4h1c2-1 2-1 4-1s3 0 3 2c0 1-1 1-3 1v1h-1c2 7 4 13 5 20 0 3-1 7-1 10h-1v1c0 1 0 1-1 2v-1c-1 0-1-1-2-1h0v1c0 1 0 3 1 4v3h1l-2 3v-7c-1-2-1-3-1-5 0-1 0-3 1-4h1c0-3-1-5-1-7-1-7-4-12-8-17v-1c0-1-2-3-3-4l1-1 2-2c1 0 2 0 3-1h0z" class="N"></path><path d="M260 812l1 4h1c2-1 2-1 4-1h0c-2 2-3 3-4 6h-1l-2-4h-3l-1-2 2-2c1 0 2 0 3-1h0z" class="G"></path><path d="M254 816l1-1 1 2c2 2 4 3 5 5 2 2 3 5 4 7 1 3 1 4 1 7l-1 2c-1-7-4-12-8-17v-1c0-1-2-3-3-4z" class="X"></path><defs><linearGradient id="n" x1="277.477" y1="155.169" x2="296.704" y2="148.131" xlink:href="#B"><stop offset="0" stop-color="#c0bdbc"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#n)" d="M274 142c4 4 10 6 15 9l14 8-2 1-7 4h0v2l-1 1c-2-3-4-5-6-7l-5-3-2 1-4-4h-1c-2-3-3-5-3-10l1-1 1-1z"></path><path d="M272 144l1-1 3 11h-1c-2-3-3-5-3-10z" class="R"></path><path d="M282 157l-4-3c0-1 0-2 1-3 1 0 3 1 5 2h1l-2 2c0 2 4 4 4 5l-5-3z"></path><path d="M285 153c2 0 3 1 5 2 3 2 7 4 11 5l-7 4h0v2l-1 1c-2-3-4-5-6-7 0-1-4-3-4-5l2-2z" class="B"></path><defs><linearGradient id="o" x1="269.301" y1="169.73" x2="261.365" y2="169.306" xlink:href="#B"><stop offset="0" stop-color="#5f5e5d"></stop><stop offset="1" stop-color="#7c7a79"></stop></linearGradient></defs><path fill="url(#o)" d="M268 148v-1-3h1 0c0 2 1 6 2 9v1c1 14-4 27-12 38-3 4-7 7-11 10v-1l1-1 1-1c1-1 1-2 2-3l4-4h-3v-1l2-3c3-5 5-9 6-14 4-8 6-17 7-26z"></path><path d="M260 183c0 4-1 6-3 8l-1 1h-3v-1l2-3 2-1c1-1 2-2 3-4z" class="Z"></path><path d="M268 148c1 3 1 6 0 9 0 3-1 6-2 10-1 3-3 7-4 11 0 2-1 4-2 5-1 2-2 3-3 4l-2 1c3-5 5-9 6-14 4-8 6-17 7-26z" class="T"></path><path d="M203 241c5 8 10 16 8 27-1 5-5 11-8 16-1-4 0-9 0-12v-4c-2-3-2-8-2-11l-3-7h1l1-1 2-2c-1-3 0-4 1-6z" class="C"></path><path d="M200 249l1 1c0 2 1 5 0 7l-3-7h1l1-1z" class="G"></path><defs><linearGradient id="p" x1="183.399" y1="831.833" x2="198.478" y2="845.676" xlink:href="#B"><stop offset="0" stop-color="#a19f9e"></stop><stop offset="1" stop-color="#d8d7d6"></stop></linearGradient></defs><path fill="url(#p)" d="M182 861c-1-4-2-9-2-14-1-5 1-11 4-16 2-5 6-8 12-9l1-1c3-1 6 0 10 0-6 2-11 4-15 10-4 7-5 15-3 22v1 2l-2 2c-1-1-1-1-1-2v4l-1 1-2-1v1 1l-1-1z"></path><path d="M157 100h-1c-4-1-8-6-11-10l-9-9c2-1 6 0 8 0h23c2 0 4 0 5 1-3 3-7 6-10 10-1 1-3 2-4 4-1 1-1 3-1 4z" class="F"></path><path d="M157 86h1c1 0 2 0 3 1-1 2-1 2-2 3l-2 1-2-2c0-2 0-2 2-3z"></path><defs><linearGradient id="q" x1="145.081" y1="98.23" x2="126.919" y2="118.27" xlink:href="#B"><stop offset="0" stop-color="#d8d6d6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#q)" d="M135 87l17 19-10 14-7 10v1-44z"></path><path d="M138 102v-1c0-2 1-3 1-5h0c1 0 2 2 3 2l2 2v1 1c0 1 0 1-1 1l-3 2c-1-2-1-2-2-3z" class="E"></path><path d="M138 102h1c0-1 1-1 1-2 2 0 3 0 4 1v1c0 1 0 1-1 1l-3 2c-1-2-1-2-2-3z" class="Q"></path><path d="M143 103c1 1 2 1 3 3 0 1 0 2-1 3s-1 1-3 1c-1 0-2-1-3-1 0-1-1-1-1-2 1-1 1-2 2-2l3-2z"></path><path d="M189 854c4 10 10 17 19 22l-1 1c-2 1-4 0-6 0l-1-1c-2 0-5-1-7-2 4 5 7 7 13 10h1-2-2v1c2 1 3 1 4 2h0 0c-3 0-6-1-8-2-9-4-16-9-21-17l3-3c1-1 0-1 2-3v-1-1l2 1 1-1v-4c0 1 0 1 1 2l2-2v-2z" class="D"></path><path d="M140 901h1l15-12c5 4 10 10 15 13l-2 8c-1 0-4 0-5 1l-15-1c1-6 3-10 7-14-7 3-13 9-19 13h-1v-3l4-5z" class="F"></path><path d="M159 900h1c1-1 3 0 4 1s1 2 1 3c-1 2-2 3-3 4l-2 1c-1-1-3-1-4-3-1-1-1-2 0-3 0-2 2-3 3-3z"></path><path d="M291 823l2-2 1 1c0 4 1 6 3 9v1 2c1 2 2 3 3 4l-6 2c-5 3-10 5-15 8-4 3-8 7-12 10h-1v-3c-1-1-1-3-1-4v-1h0c1 0 1 1 2 1v1c1-1 1-1 1-2v-1h1c1-1 2-1 3-2 2-7 7-10 11-15 3-3 5-6 8-9z" class="J"></path><path d="M297 834c1 2 2 3 3 4l-6 2s1-1 1-2c1-1 2-2 2-4z" class="B"></path><path d="M290 834c2-1 3-1 4-1l1 1v1c-2 2-5 3-8 4-1 0-1 0-1-1-1 1 0 1-1 2-1 2-9 7-11 7 3-4 9-11 14-13 0 1 0 1 1 1l1-1z"></path><path d="M272 847c2-7 7-10 11-15 3-3 5-6 8-9l1 1c0 3-1 6-2 10l-1 1c-1 0-1 0-1-1-5 2-11 9-14 13h-1-1z" class="G"></path><path d="M191 215c5-2 8 1 12 2l2 1c0 1 0 2 1 3l8 9c1-1 2-2 3-1 1 0 2 0 2 1h1c3 1 5 3 8 3h1c1 1 2 2 3 4 0 2-2 3-3 5l-5 7-3 6-2 3c-1-2-2-7-3-9-4-10-10-22-18-29-2-2-4-3-6-4l-1-1z" class="D"></path><path d="M214 230c1-1 2-2 3-1 1 0 2 0 2 1h1c3 1 5 3 8 3h1c1 1 2 2 3 4 0 2-2 3-3 5l-5 7-3 6c-1-4 0-8-1-12-1 0-1-1-1-2s0-1 1-2v2c0 1 1 1 1 2l1-1c1-1 1-2 1-3-2-4-6-7-9-9z" class="O"></path><path d="M224 249l1-3v-3c1-1 1-2 1-3 0-4-4-6-6-8l-1-1 1-1c3 1 5 3 8 3h1c1 1 2 2 3 4 0 2-2 3-3 5l-5 7z" class="P"></path><path d="M136 816l1 1c4 1 9-3 12-4 0-1 2-1 2-1-4 3-8 5-12 8v1c1 11 3 22 6 32 1 3 2 7 3 10 2 4 4 8 4 12v-1c-3-3-5-7-7-11-3-6-7-13-9-20l-4-15-1 40c0 8 1 17 0 25-1-8 0-17 0-25v-6c-1-2-1-5-1-7 1-1 0-2 0-3v1s0 1-1 2v1h-1v1h1v3c0-1 0-2-1-3l-1 1h-1c1-2 0-3 0-4s1-1 1-2h2 0l-2-1v-1c-1-2 0-5 0-7v-24c-1-1-2-3-3-4 2 0 4 0 6 1l2 4 4-4z" class="C"></path><defs><linearGradient id="r" x1="236.208" y1="901.811" x2="182.757" y2="897.679" xlink:href="#B"><stop offset="0" stop-color="#959493"></stop><stop offset="1" stop-color="#cdcaca"></stop></linearGradient></defs><path fill="url(#r)" d="M201 891c12 4 23 3 35 2 0 4 0 8 2 12-4 1-9 2-13 3-14 2-31 1-44-6-1-1-3-2-4-3 3 0 5 1 8 2 8 2 16 3 23 1-1-1-6-3-8-3-2-2-5-3-8-5h2c2 1 6 2 9 0h0 1 1v-1c-2 0-2 0-3-1l-1-1z"></path><path d="M227 896h6c1 1 1 2 1 4-2 2-12 3-16 3-1 0-1 0-1-1v-1c1 0 3 1 4 0h5v-1c-1-1-2-2-4-2l-1-1c1-1 4-1 6-1z"></path><defs><linearGradient id="s" x1="215.925" y1="891.3" x2="239.804" y2="840.14" xlink:href="#B"><stop offset="0" stop-color="#b3b1b2"></stop><stop offset="1" stop-color="#e5e3e2"></stop></linearGradient></defs><path fill="url(#s)" d="M255 844c2 10 2 19-4 27-5 9-12 14-22 16-6 2-16 3-22 0h0c-1-1-2-1-4-2v-1h2 2-1c-6-3-9-5-13-10 2 1 5 2 7 2l1 1c2 0 4 1 6 0l1-1 7 2c8 2 17 0 24-4 7-5 10-11 11-18 1-1 1-3 1-5 0 0 1-2 2-2 1-2 2-3 2-5z"></path><path d="M195 842c2-3 3-6 5-8-1 7 0 13 5 19l5-8c1 1 1 2 1 3h-1c-1 1-3 6-3 7-1 1 0 2 1 3 2 3 6 4 9 5 4 0 9-1 12-4 2-1 4-4 4-6 1-5-2-9-5-12l1-1c1 1 3 4 4 4l2-1c1-1-1-5 0-6 4 3 9 9 10 14 0 4-1 8-4 12-4 5-10 8-16 9-4 0-9 0-13-1-6-2-11-7-14-12-4-6-4-11-3-17z" class="C"></path><path d="M212 871c-6-2-11-7-14-12-4-6-4-11-3-17 1 3 1 6 1 9v1c1-2 1-5 1-7 0 4 1 8 3 11l4 4c-1 0-1 0-2-1v1l3 3h-1c0 1 0 1 1 2h0c2 2 4 4 6 4l1 2z" class="O"></path><defs><linearGradient id="t" x1="226.333" y1="773.245" x2="225.26" y2="801.265" xlink:href="#B"><stop offset="0" stop-color="#9e9c9b"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#t)" d="M234 773c4 1 8 5 11 8a57.31 57.31 0 0 1 11 11c2 4 3 9 3 13-16-13-33-17-53-14l-13 2 2-1h0l-2-4h-2-1-3v-1c2-2 5-3 8-4 5-3 11-6 17-7 5-1 9-1 14-1 1-1 2-1 3-1h0 0l5-1z"></path><path d="M190 788h-3v-1c2-2 5-3 8-4 5-3 11-6 17-7 5-1 9-1 14-1 5 1 9 1 13 5v1c-3 2-7 1-10 1h-14c-5 0-11 2-17 3-3 1-6 1-8 3z"></path><defs><linearGradient id="u" x1="258.698" y1="164.382" x2="226.694" y2="74.219" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#b8b6b5"></stop></linearGradient></defs><path fill="url(#u)" d="M206 80c9 0 16 0 24 2 2 1 5 1 7 3 0 4-1 10 0 14h1v-1c2 4 8 8 11 11l9 10c1 1 3 3 3 4 0 3 10 16 13 19l-1 1-1 1-2 1-1-1h0-1v3 1c-1 9-3 18-7 26 0-2 0-3 1-5 0-2 1-3 0-6h-1v1l-1-1v-15c1 5 1 10 1 15l2-6c3-9 0-19-4-27-6-13-17-24-31-29-6-1-12-2-18-2v-1h3c0-1-1-2-1-4-1 0-2 0-3 1h-1c-1 1-4-1-6-1v-3-1h-1c1-1 3-2 4-3h1c-1 0-5 0-6 1h-6c-2 1-8 3-9 3l14-6 7-2 2-1v-1l-2-1z"></path><path d="M225 94l-1-1c1-1 1-1 2-1 1-1 2 0 3 1v1l-1 1-3-1z"></path><path d="M206 83l8-1h3l1 1c0 1-1 1-1 1 2 0 4 0 7 1h2 4c0 1 0 1 2 2l1 1h1l-1 1h0-3v-1h-1-2-2v-1h-1c-2 0-4 0-6-1h-2c-6 0-12-1-17 1-2 0-4 0-5 1-2 1-8 3-9 3l14-6 7-2z" class="S"></path><path d="M194 88c1-1 3-1 5-1 5-2 11-1 17-1v1c1 1 1 3 1 4-2 0-2 1-3 2l2 1c1-1 1-2 2-2l1 1c2 1 3 1 5 1h1l3 1h-1l1 1h0v4 1c-6-1-12-2-18-2v-1h3c0-1-1-2-1-4-1 0-2 0-3 1h-1c-1 1-4-1-6-1v-3-1h-1c1-1 3-2 4-3h1c-1 0-5 0-6 1h-6z" class="C"></path><path d="M248 195c1 1 1 1 2 1l2-1v1c-1 1-1 2-2 3l-1 1-1 1v1c-3 4-9 7-14 8l-17 4h-10c1 1 4 1 6 1 7 1 14 2 21 0l27-8c-3 5-10 16-16 18-3 1-6 1-9 1-4 0-7 1-11 2l4 5h-1c-3 0-5-2-8-3h-1c0-1-1-1-2-1-1-1-2 0-3 1l-8-9c-1-1-1-2-1-3l-2-1c-4-1-7-4-12-2l-1-1 1-1h2v-1c-2-2-4-5-7-6l-2-2-1-1-1-2-3-5c2 1 3 3 6 5s12 7 17 7c3 1 8 1 11 1 7 0 14-1 20-6l5-5v1c-1 2-5 5-6 6l-1 1c2 0 6-3 7-3 4-3 7-5 10-8z" class="B"></path><path d="M251 212c1 0 1-1 2 0l-3 3h-1c-1 0-1 0-2-1 1-1 2-2 4-2z" class="L"></path><path d="M248 195c1 1 1 1 2 1l2-1v1c-1 1-1 2-2 3l-1 1-1 1v1c-3 4-9 7-14 8l-1-1 3-2h2c3-2 9-6 10-10-3 2-6 5-9 7l-1-1c4-3 7-5 10-8z" class="K"></path><path d="M203 217c2-1 4 0 6 0l10 1c5 1 9 0 14 0 1 1 1 1 2 1v1c-3 2-7 2-10 2-3 1-5 2-8 3-4-2-8-4-12-7l-2-1z"></path><path d="M175 815c2-2 5-4 8-5 10-5 19-8 29-10 2 2 3 2 5 2s4 1 6 1l3 1c3 1 5 3 7 4-1 0-2 0-3-1h0-1 0c-1-1-1 0-1-1-1-1-1 0-2-1h0-2l-1-1h-4v1h1c1 0 1 0 2 1h1l4 3v1c2 2 4 4 7 6 2 0 4 2 5 3l1 1 2 2 2 2c1 1 0 0 1 2 1 1 3 2 3 4 1 1 2 1 3 3l1 2h0c1 1 2 3 2 5s0 3 1 4c0 2-1 3-2 5-1 0-2 2-2 2 0 2 0 4-1 5v-4c0-4-1-7-2-10-2-3-5-6-7-9-4-4-6-9-10-12-8-6-17-9-26-8-14 1-23 10-31 20l-5 7h0c0-1-1-1-1-2v-1h-2 0v-1l1-1c2-3 3-4 4-7-1-1-2-1-2-2-2 0-3 1-4 2-1 0-2-1-4-2 5-3 9-8 14-11z" class="Q"></path><path d="M171 828v3c-1 2-2 3-3 4h0l1 1h2v-1c1 0 1-1 2-2h0 1l-5 7h0c0-1-1-1-1-2v-1h-2 0v-1l1-1c2-3 3-4 4-7z" class="C"></path><defs><linearGradient id="v" x1="209.808" y1="803.726" x2="224.527" y2="812.192" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#282828"></stop></linearGradient></defs><path fill="url(#v)" d="M199 809c1-2 3-2 4-2 7-2 14-3 20 0l4 3c2 2 4 4 7 6-12-5-21-12-35-7z"></path><path d="M175 815c2-2 5-4 8-5 10-5 19-8 29-10 2 2 3 2 5 2s4 1 6 1l3 1c3 1 5 3 7 4-1 0-2 0-3-1h0-1 0c-1-1-1 0-1-1-1-1-1 0-2-1h0-2l-1-1h-4v1h1c1 0 1 0 2 1h1l4 3v1l-4-3c-6-3-13-2-20 0-1 0-3 0-4 2-2 1-4 2-7 3l-13 10h-1l1-1c1-1 1-1 1-2 1-1 1-1 2-1v-1c0-2-1-3-2-4-2 1-3 2-5 2z" class="J"></path><path d="M167 556l1 15 8 49 6 44c3 24 8 49 5 73-2 14-7 28-15 40-7 9-15 17-23 24-6 5-12 10-18 14l-4-1h1c2 0 4-2 6-3 12-10 23-24 31-38l1-1 1-1c0-2 0-3 2-4v-1-1l-1 1v-3l3-9h0l1-3c0-4 2-9 3-13 2-4 2-9 3-14 0-4 1-8 1-12v-23-3l-12-106h0c-1-4-1-9-1-13 0-2-1-3 0-4h0c0-3 0-5 1-7z" class="B"></path><path d="M178 738h2v1 2h-2c-1 0 0 0-1-1l1-2zm3-9h2c0 2 0 2-1 3h-1c-1-1 0-2 0-3z"></path><path d="M167 556l1 15v4c-1-1 0-3-1-4v-4c-1 2 0 6 0 8 1 2 0 3 1 5h-1 0c-1-4-1-9-1-13 0-2-1-3 0-4h0c0-3 0-5 1-7z" class="Q"></path><path d="M175 738h2 1l-1 2h0v4c0 2-1 2-2 4 0 1 0 1-1 2s-1 2-3 4l1-3c0-4 2-9 3-13z" class="G"></path><path d="M178 724c0-4 1-8 1-12v-23l1 7 1 1-1 2c1 1 2 1 4 1v4l-1 1-1-1-1 1c0 1 0 1 1 2v1h2c1 1 1 3 1 5-2 1-2 0-3 2 0 0 0 1 1 2h2v4c-2 1-3 0-5 1l2 2h0-2l-1 2h0l-1-2z" class="H"></path><path d="M146 820h0c3 0 5-1 7-2 0 2-1 4 0 6 0 3 2 6 2 9 8 23 16 45 40 56l6 2 1 1c1 1 1 1 3 1v1h-1-1 0c-3 2-7 1-9 0h-2c3 2 6 3 8 5 2 0 7 2 8 3-7 2-15 1-23-1-3-1-5-2-8-2-9-4-19-16-25-24 0-4-2-8-4-12-1-3-2-7-3-10-3-10-5-21-6-32h4c1-1 2-1 3-1z" class="D"></path><path d="M146 820h0c3 0 5-1 7-2 0 2-1 4 0 6 0 3 2 6 2 9-2-3-6-11-9-13z" class="U"></path><defs><linearGradient id="w" x1="248.96" y1="121.719" x2="206.035" y2="179.887" xlink:href="#B"><stop offset="0" stop-color="#afaead"></stop><stop offset="1" stop-color="#efedee"></stop></linearGradient></defs><path fill="url(#w)" d="M210 105c12-1 24 3 33 11s16 20 17 32h0v15l1 1v-1h1c1 3 0 4 0 6-1 2-1 3-1 5-1 5-3 9-6 14l-2 3v1h3l-4 4v-1l-2 1c-1 0-1 0-2-1-3 3-6 5-10 8-1 0-5 3-7 3l1-1c1-1 5-4 6-6v-1l-5 5c-6 5-13 6-20 6-3 0-8 0-11-1l2-1c1 1 2 1 3 1h1 3v-1c2 0 3 0 4-1 3-1 6-1 8-3l1-1c3-1 4-4 7-6h0l3-4c3-4 6-8 7-13h0l-1 1c-1-5-1-10-2-14h0c-1-1 0-1-1-1 0-3-2-5-4-7 1-2-1-3-2-5-1-1 0-5 0-7 1 1 2 1 3 3l1 2v-3h1l1 1 1-1c2 3 3 6 5 10l2-2 1 1 1 2 1 1h0c2-3 2-6 3-10 0-11-4-19-11-27l-5-3c-8-5-18-6-28-3l1-1 1-1c0-1-1-2-1-3 0-2 3-4 2-7z"></path><path d="M255 153c0 1 0 1 1 2 0 1-1 7-1 8v2c-1 4-2 9-4 14 0 1-1 2-1 3s-1 1-1 2c-1 2-2 3-3 5s-3 3-5 5c0 1-1 2-2 3 1-3 3-5 5-8 0-2 0-2 1-4 3-5 3-10 5-16 1-1 2-3 3-5 1-3 1-7 2-11z" class="P"></path><path d="M260 163l1 1v-1h1c1 3 0 4 0 6-1 2-1 3-1 5-1 5-3 9-6 14l-2 3v1h3l-4 4v-1l-2 1c-1 0-1 0-2-1 2-6 8-12 10-19 0-3 0-6 1-8 0-2 0-3 1-5z" class="J"></path><path d="M252 195l-2-1 1-1 2-1h3l-4 4v-1z" class="L"></path><path d="M240 123c1-1 1-1 3-1 7 7 11 17 11 27 0 0 0 4 1 4-1 4-1 8-2 11-1 2-2 4-3 5-2 6-2 11-5 16-1 2-1 2-1 4-2 3-4 5-5 8h-1v1l-5 5c-6 5-13 6-20 6-3 0-8 0-11-1l2-1c1 1 2 1 3 1h1 3v-1c2 0 3 0 4-1 3-1 6-1 8-3l1-1c3-1 4-4 7-6h0l3-4c3-4 6-8 7-13h0l-1 1c-1-5-1-10-2-14h0c-1-1 0-1-1-1 0-3-2-5-4-7 1-2-1-3-2-5-1-1 0-5 0-7 1 1 2 1 3 3l1 2v-3h1l1 1 1-1c2 3 3 6 5 10l2-2 1 1 1 2 1 1h0c2-3 2-6 3-10 0-11-4-19-11-27z" class="F"></path><path d="M237 149l1-1c2 3 3 6 5 10 3 10 3 16-2 25-2 4-6 10-10 13l3-4c3-4 6-8 7-13l1-1c3-10-1-21-5-29z" class="L"></path><path d="M231 146c1 1 2 1 3 3l1 2v-3h1l1 1c4 8 8 19 5 29l-1 1h0l-1 1c-1-5-1-10-2-14h0c-1-1 0-1-1-1 0-3-2-5-4-7 1-2-1-3-2-5-1-1 0-5 0-7z" class="Q"></path><path d="M231 146c1 1 2 1 3 3l1 2c0 3 1 5 1 8 1 3 2 4 2 7h0c-1-1 0-1-1-1 0-3-2-5-4-7 1-2-1-3-2-5-1-1 0-5 0-7z" class="J"></path><path d="M136 816l35-31c4-4 8-8 11-13 6-9 9-19 11-30 3-10 5-19 4-29 1 1 2 3 3 4 2 5 4 10 5 16-1 3-1 7-1 9l-1 3c-2 3-2 9-3 12v1c-1 1-1 2-2 3v1l-1 1c0 1-1 2-1 2-1 2-2 5-4 7h0c1 1 0 1 1 1h1c-1 2-2 4-2 6h0c1 0 3 0 4-1 12-6 19-16 24-28 1 3 3 5 4 8-1 3-2 3-2 7 1 0 0 1 0 2-1 0-2 1-3 1s-2 1-3 2c1 1 1 1 2 1 2 0 3 2 6 2 1 1 2 1 3 1h2 0 0c-1 0-2 0-3 1-5 0-9 0-14 1-6 1-12 4-17 7-3 1-6 2-8 4v1h3 1 2l2 4h0l-2 1c-10 4-18 9-26 16-5 3-9 6-13 9h-1c-2 1-4 2-7 2h0c-1 0-2 0-3 1h-4v-1c4-3 8-5 12-8 0 0-2 0-2 1-3 1-8 5-12 4l-1-1z" class="F"></path><path d="M199 82c2-1 5-1 7-2l2 1v1l-2 1-7 2-14 6c1 0 7-2 9-3h6c1-1 5-1 6-1h-1c-1 1-3 2-4 3h1v1 3c2 0 5 2 6 1h1c1-1 2-1 3-1 0 2 1 3 1 4h-3v1c-16 2-27 8-38 21-4 5-8 12-11 18-4 6-8 12-11 18 6 5 12 8 16 14 11 12 20 37 40 35 9-1 15-6 20-12s8-13 9-21l-1-5c0-4-3-6-5-8l-1-1 3-3 2 3c2 2 4 4 4 7 1 0 0 0 1 1h0c1 4 1 9 2 14l1-1h0c-1 5-4 9-7 13l-3 4h0c-3 2-4 5-7 6l-1 1c-2 2-5 2-8 3-1 1-2 1-4 1v1h-3-1c-1 0-2 0-3-1l-2 1c-5 0-14-5-17-7s-4-4-6-5l3 5 1 2 1 1 2 2c3 1 5 4 7 6v1h-2l-1 1-1-1 3 5 7 15 4 8c-1 2-2 3-1 6l-2 2-1 1h-1l3 7c0 3 0 8 2 11v4c0 3-1 8 0 12l-11 21c3-24 2-47-3-71-2-6-3-13-6-19-9-18-26-34-43-45l-4-2s-2-1-3-2c0-16 4-30 13-44l10-14c11-13 26-25 43-26z" class="D"></path><path d="M199 82c2-1 5-1 7-2l2 1v1l-2 1-7 2 2-2h3l-1-1h-4 0z" class="C"></path><path d="M139 162c2 1 5 4 6 5h-1c-1 0-3 2-4 3l-4-2c1 0 2-1 2-2 1 0 1-3 1-4z" class="E"></path><path d="M189 213c-3-1-5-6-6-8l-11-16v-1l7 8 3 5 1 2 1 1 2 2c3 1 5 4 7 6v1h-2l-1 1-1-1z" class="P"></path><path d="M235 172l-1-5c0-4-3-6-5-8l-1-1 3-3 2 3c2 2 4 4 4 7 1 0 0 0 1 1h0c1 4 1 9 2 14l1-1h0c-1 5-4 9-7 13 0-2 0-4 1-6 2-3 4-7 3-11v-6c0-1-1-1-1-2v-2-1h-1c0-2-1-4-2-4h-1v2c1 1 2 2 2 3v1c1 1 1 1 1 2l-1 1v3z" class="G"></path><path d="M192 218l7 15 4 8c-1 2-2 3-1 6l-2 2-1 1h-1v-1l-3-15c-1-5-3-10-3-16z" class="S"></path><path d="M199 233l4 8c-1 2-2 3-1 6l-2 2-1 1h-1v-1c1-1 2-1 2-2-1-1-1-2-1-3l-1-1v-7c0-1 0-2 1-3z" class="P"></path><path d="M146 122c1 1 1 2 1 3 0 0-1 2-1 3-2 6-4 13-4 19 2-3 5-8 8-10h0c-4 5-12 18-11 24v1c0 1 0 4-1 4 0 1-1 2-2 2 0 0-2-1-3-2 0-16 4-30 13-44z" class="C"></path><path d="M210 99c6 0 12 1 18 2 14 5 25 16 31 29 4 8 7 18 4 27l-2 6c0-5 0-10-1-15h0c-1-12-8-24-17-32s-21-12-33-11c1 3-2 5-2 7 0 1 1 2 1 3l-1 1-1 1c10-3 20-2 28 3l5 3c7 8 11 16 11 27-1 4-1 7-3 10h0l-1-1-1-2-1-1-2 2c-2-4-3-7-5-10l-1 1-1-1h-1v3l-1-2c-1-2-2-2-3-3 0 2-1 6 0 7 1 2 3 3 2 5l-2-3-3 3 1 1c2 2 5 4 5 8l1 5c-1 8-4 15-9 21s-11 11-20 12c-20 2-29-23-40-35-4-6-10-9-16-14 3-6 7-12 11-18 3-6 7-13 11-18 11-13 22-19 38-21z"></path><path d="M205 149c3 0 3 0 5-2l2 2h0l-1 2c-1 0-1 0-2 1-1-1-3-2-4-3z" class="O"></path><path d="M209 152c1-1 1-1 2-1 1 1 1 2 2 3 0 1 1 2 1 4h8c-1 1-3 3-5 4-1 0-3 0-4-1s-2-2-2-4c0-3 0-3-2-5z" class="H"></path><path d="M212 149c2 1 2 1 4 1 1-1 2-1 3-2-1-3-2-5-4-7-3-2-6-4-8-7 5 1 15 4 18 9 1 2 1 6 0 9l-3 6h-8c0-2-1-3-1-4-1-1-1-2-2-3l1-2z" class="F"></path><path d="M219 167c2-1 4-2 5-4v-1h1v2c3 4 2 9 1 14a19.81 19.81 0 0 1-11 11c-7 2-15 2-22-1 0 0-1 0-2-1 2 0 3 1 5 1h0l1 1 1-1 1-1h0v-1c2 1 4 2 7 3 2 0 5-1 7-3 1-1 3-3 3-4 1-1 0-4 0-6v-1c0-1-1-2-2-3 1-2 3-1 5-1l-1-1-3-3h4z" class="N"></path><path d="M219 167c2-1 4-2 5-4v-1h1v2h0c0 3 0 6-1 9h0l-2-4v-1c-1-1-1-1-3-1z" class="Y"></path><path d="M219 167c2 0 2 0 3 1v1l2 4h0c0 2 0 4-2 5-2 4-4 7-9 8 1-1 3-3 3-4 1-1 0-4 0-6v-1c0-1-1-2-2-3 1-2 3-1 5-1l-1-1-3-3h4z" class="L"></path><path d="M222 169l2 4h0c0 2 0 4-2 5l-1-7h1v-2z" class="R"></path><path d="M219 167c2 0 2 0 3 1v1 2h-1-1v2l-1-2-1-1-3-3h4z" class="S"></path><path d="M214 172c1-2 3-1 5-1l1 2c0 2-1 4-3 6 0 0 0-2-1-3v-1c0-1-1-2-2-3z" class="G"></path><defs><linearGradient id="x" x1="208.131" y1="145.768" x2="174.659" y2="144.175" xlink:href="#B"><stop offset="0" stop-color="#cfcecf"></stop><stop offset="1" stop-color="#fffefd"></stop></linearGradient></defs><path fill="url(#x)" d="M182 118l1-1c7-8 17-11 27-12 1 3-2 5-2 7 0 1 1 2 1 3l-1 1-1 1c-10 4-18 11-22 21a41.54 41.54 0 0 0 0 31c3 7 8 13 14 17v1h0l-1 1-1 1-1-1h0c-2 0-3-1-5-1-9-5-14-14-20-22-3-4-7-9-8-14 1-4 2-7 4-11 4-8 8-16 15-22z"></path><path d="M183 123c5-4 8-7 14-9l3-1h1l2 1c-4 1-7 1-11 3h-1c-6 3-10 11-13 17h0c-1 1-1 1-2 3 0 1-1 1-1 2-1 0-1 1-2 2 0 1-1 2-1 3l-1-1c0-1 0-1 1-2l1-3 1-1c-1-3 7-11 9-14z" class="G"></path><path d="M172 144c0-1 1-2 1-3 1-1 1-2 2-2 0-1 1-1 1-2 1-2 1-2 2-3v7c-1 1 0 3-1 4s-2 1-2 2v1s-1 0-1 1v1l-2 1-2-1 1-4c0-1 1-2 1-2z" class="I"></path><defs><linearGradient id="y" x1="176.888" y1="183.613" x2="189.969" y2="129.787" xlink:href="#B"><stop offset="0" stop-color="#acaaaa"></stop><stop offset="1" stop-color="#e2e1e0"></stop></linearGradient></defs><path fill="url(#y)" d="M191 187c-9-5-14-14-20-22-3-4-7-9-8-14 1-4 2-7 4-11 4-8 8-16 15-22h0c0 3-5 6-6 9h4v-1l3-3c-2 3-10 11-9 14l-1 1-1 3c-1 1-1 1-1 2l1 1s-1 1-1 2l-1 4 2 1 2-1v-1c0-1 1-1 1-1v-1c0-1 1-1 2-2v13h0c1 2 1 4 2 6 3 7 9 20 16 22 2 1 3 1 4 1h0l-1 1-1 1-1-1h0c-2 0-3-1-5-1z"></path><path d="M171 156c-2-1-3-3-4-5l4-5-1 4 2 1 2-1-3 6z" class="L"></path><path d="M174 150v-1c0-1 1-1 1-1v-1c0-1 1-1 2-2v13l-1-1c0-1 0-1-1-2-1 1-1 2-2 2l-2-1 3-6z" class="C"></path><path d="M207 117c10-3 20-2 28 3l5 3c7 8 11 16 11 27-1 4-1 7-3 10h0l-1-1-1-2-1-1-2 2c-2-4-3-7-5-10l-1 1-1-1h-1v3l-1-2c-1-2-2-2-3-3 0-2 0-4 1-6v-3c-2-3-5-5-9-6-6-1-12-1-17 3-1 1-2 3-2 5 1 2 3 4 4 5l2 3c-2 2-2 2-5 2l-3-2c0 6 0 12 4 16 3 3 6 4 9 4l3 3 1 1c-2 0-4-1-5 1 1 1 2 2 2 3v1c0 2 1 5 0 6 0 1-2 3-3 4-2 2-5 3-7 3-3-1-5-2-7-3-6-4-11-10-14-17a41.54 41.54 0 0 1 0-31c4-10 12-17 22-21z"></path><path d="M194 152c0 5 0 9 1 15a24.56 24.56 0 0 1-5-15c1 1 1 2 1 3l2 6c0-3-1-6 1-9h0z" class="U"></path><defs><linearGradient id="z" x1="195.427" y1="150.083" x2="218.256" y2="164.695" xlink:href="#B"><stop offset="0" stop-color="#8e8e8d"></stop><stop offset="1" stop-color="#adaaab"></stop></linearGradient></defs><path fill="url(#z)" d="M199 140v-1c2-3 4-4 7-5-1 1-2 3-2 5 1 2 3 4 4 5l2 3c-2 2-2 2-5 2l-3-2c0 6 0 12 4 16 3 3 6 4 9 4l3 3 1 1c-2 0-4-1-5 1-4-1-8-2-10-4v-1c2 2 4 2 6 3-3-3-8-4-10-7-4-5-3-14-2-20 1-1 1-2 1-3z"></path><path d="M199 140v-1c2-3 4-4 7-5-1 1-2 3-2 5 1 2 3 4 4 5l-1 1 1 1v2h-1c-3-3-5-3-9-5 1-1 1-2 1-3z" class="K"></path><defs><linearGradient id="AA" x1="194.176" y1="135.814" x2="242.489" y2="153.655" xlink:href="#B"><stop offset="0" stop-color="#979594"></stop><stop offset="1" stop-color="#cdcccb"></stop></linearGradient></defs><path fill="url(#AA)" d="M190 152c0-8 2-16 9-23 5-5 12-7 20-7s16 3 21 9c6 5 8 16 8 24 0 1-1 2-1 4l-1-2-1-1-2 2c-2-4-3-7-5-10l-1 1-1-1h-1v3l-1-2c-1-2-2-2-3-3 0-2 0-4 1-6v-3c-2-3-5-5-9-6-6-1-12-1-17 3-3 1-5 2-7 5v1c-2 4-4 7-5 12h0c-2 3-1 6-1 9l-2-6c0-1 0-2-1-3z"></path><path d="M232 137c2-1 5-3 6-3v-1h1l1 2-3 3c-1 0-2 0-3-1l-1 1c0 1 0 1-1 2v-3z" class="I"></path><path d="M238 147c-2-1-3-2-3-4v-2c2-1 4-1 5 0 3 1 5 5 6 8 0 2 1 6 0 8l-1-1-2 2c-2-4-3-7-5-10v-1z"></path><path d="M238 148v-1c3 2 6 5 7 9l-2 2c-2-4-3-7-5-10z" class="I"></path><path d="M367 189c3-2 6-3 8-5 8-4 17-8 25-10 16-4 33-8 50-9 4 0 9 1 13 1 22 2 47 4 67 16l8 5c-1 1-1 2-1 3 2 2 5 4 6 6v1l1 1h2c0 1 0 2 2 3h1c3 2 3 4 6 4l12 14 1-1c0 1 1 1 1 2 2 2 4 4 5 6 8 8 19 16 29 22h1v-1h1l-2-1c-1-1 0-3 1-5 1 1 3 1 4 2 1 2 2 3 4 3l1 1s1 0 1 1v3c1 1 3 1 4 2l1-1 1 1-1 1c2 1 5 1 7 0h0 0c2-1 3-2 4-2 0 1 0 2 1 3s1 2 2 3h1c3 0 5 1 7 3 2 1 3 3 5 4 4 3 14 10 14 16 0 1-1 3-3 4-1 1-2 1-3 2 0 1 0 1-1 2h1c1 0 3-1 4 0h0l-6 4 1 6c1 1 2 1 3 1l1-1c1-2 1-3 2-4h5c1 1 2 1 2 3l1 1v2c1 0 1-1 2-1h1l1-4c0-1 1-2 2-3-1-1-2-2-2-4-2-3-5-7-6-11v1c3 2 6 7 9 8l2-1c8 14 16 28 22 43 5 12 9 26 12 38 6 23 12 47 12 71v5c1 8 2 16 1 23 0 3 0 5-2 7 0 1-1 1-2 1l-1-1h1c2-2 2-5 3-8l-1-1c-1-1-1 2-3 2 0 0 1 0 1-1 1-1 0-2 0-4l-2 5-3 3c-1 1-1 2-2 3l-1 2h0c-1 1-1 2-1 3-1 4-4 9-7 13 0-1 0-3 1-5l-2-2-2 2v5c0 5-1 11-1 16-1 4 0 9 0 14h-1l-2 23-4 24-1 13c-2 12-4 23-7 34-1 2-4 7-3 9l1 1-2 2c-2 3-3 6-4 10 0 0 0 2-1 3h-1c-1 6-4 12-5 19-1 3-4 6-3 9l-1 1c-1 4-3 7-5 11-10 17-22 34-35 49-5 7-11 13-17 18l-10 9-2-1-10 9-12 10c-1 1-2 3-3 4-4 2-7 6-10 8-1-1-2-1-3-1-3 1-6 3-9 4l-28 13h0c-1 0-3 1-4 1-3-1-15 3-20 4l-10 2c-14 1-29 2-43 1v1c-3 0-5 0-7-1l-16-2-26-3v-1c-2-1-7-2-9-2l-3-1c-2-1-5-1-8 1-1 0-2 2-4 3l2 2h-1c-8 1-14-3-21-7h0c-3 0-4-4-7-4v1h0c1 1 1 2 1 2l1 1v1c-1-1-3-4-4-5l-16-17h2l1-1-5-7c-1 0-3-3-4-3v-3l-7-6c-2 0-3-1-4-2l-1 1 1 1h-1l-4-2-2-2h1l-18-16c0-2-2-2-3-5-2-3-6-7-9-10-3-4-7-9-10-14-3-4-6-9-8-13-3-6-5-12-8-18l-9-18h1c0-2-1-2-1-4l1 1v-1l-4-12-3-5v1h-1 0c-2-2-3-7-3-10l-4-13c-2-6-3-12-4-18 0-2 0-5-1-6 0 0-1-1-1-2v-3l-1-9c-1-5-3-10-3-15-1-6 0-12 0-17v-30c1-6 1-14 1-20s0-11-2-16c0 3 0 7-1 10-2-17 0-34 3-51l3-17c2-5 3-12 5-18 4-16 8-33 14-50 2-6 5-13 8-19 1-3 2-5 4-8 0-2 2-4 3-6 3-7 13-20 14-28v-2l-1-1h-1l1 1-1 1h-3c-1 0-1 0-2-1v-3c0-2 0-3 1-4l3-4c0-1 1-1 1-2-1-1-1-1-1-2h0l-1-1c2-2 4-3 7-5h0c1 1 3 1 4 1 2 3 3 5 4 7v-7a57.31 57.31 0 0 1 11-11c2-2 5-3 7-4l9-4c2-1 3-1 4-2 2-1 3-3 5-4l6-5c0-1 1-3 1-4v-3l1 1 1-2-1-3c0-1 1-1 2-2l-1-1-4-4-3-2v-3c0-1 1-2 2-3 0-1 2-2 3-3 3-2 6-5 9-7 5-3 11-6 17-4h0c1 1 1 0 0 1-1 3 0 8-2 10-1 3-5 7-7 10l-7 10c-1 1-4 3-4 5 7-6 10-12 16-19 0-1 13-16 16-19v-2c2-2 5-7 7-8l2-1z"></path><path d="M630 562h0l-1-1c-1-4-3-7-4-10 2 2 4 4 6 7 0 1 0 3-1 4z" class="B"></path><path d="M678 353c1-2 1-2 1-4v1c1 3 2 4 4 6-1 1-1 1-3 2-1-2-2-4-2-5z" class="S"></path><path d="M611 582l1 1c-1 4-3 6-5 9l-1-1c1-4 2-6 5-9z" class="K"></path><path d="M277 630c0-1 1-3 3-4v1h1 1 2c-1 2-1 4-3 5-2-2-2-2-4-2z" class="E"></path><path d="M249 509v16h0l-2-2v-2c-1-1-1-1-1-3 0-1 1-1 1-2v-1l1-1 1-5z" class="B"></path><path d="M377 263h0c6 0 8-2 12-6-1 3-3 7-5 9h-7c1-1 3-1 4-1-1-1-3-1-4-2z" class="O"></path><path d="M540 209h1c1 0 2 0 4-1 1 1 2 1 3 1l3 3v1l-17-4h6z" class="U"></path><path d="M327 732c1 1 4 4 4 5l-8 1c-1-1-1-2-2-2 2-2 4-3 6-4zm271-147h4l3 2h0v1l-3 8c-1-4-1-7-4-11h0zm17 69c4 0 8 1 11 4 1 0 2 1 3 2-1 2-1 4 0 5h-1v-1c-3-5-9-7-13-10z" class="J"></path><path d="M333 218v4l1-1c1-2 2-2 4-3 0 1-2 2-3 4s-3 5-4 7l-1 1-1-1-3 3h0l1-2 5-7c1-1 1-4 1-5z" class="K"></path><path d="M610 741v1l-2 3h0v1l-10 9-2-1 14-13z" class="Y"></path><path d="M695 403l1 1 2 2c0 2 1 5 2 6 1 2 1 4 3 5l-2 1h-2c-2-4-3-10-4-15z" class="J"></path><path d="M497 259l1 2c1 1 3-1 4-1 1 2-1 2 2 4v1l-10 1c2-2 3-4 3-6v-1z" class="I"></path><path d="M677 342l1 1c1 1 3 2 5 3 2 2 5 3 8 4-4 0-7 0-11-1-1-1-2-1-3-2v-5z" class="H"></path><path d="M657 299l2 3c0 2 0 3 1 4s1 1 1 2l-1 1-2-3h-1l2 3-1 1-5-11c1 1 2 1 3 1l1-1zM293 682c1-1 3-2 4-4v1c0 2 0 4 2 5 0 1 2 2 3 2h1l1 1c-2 1-3 1-4 1-2 0-4-1-5-3-1-1-2-2-2-3z" class="I"></path><path d="M377 266l-12 1c2-2 4-4 7-5 2 0 3 1 5 1 1 1 3 1 4 2-1 0-3 0-4 1z" class="Q"></path><path d="M332 572c2 1 4 4 4 6l-1-1c-3-2-7-2-10-2-2 1-4 1-6 3v-2c1-4 10-2 13-4z" class="H"></path><path d="M278 551v4h1c1-1 2-3 4-3h0c-2 5-3 9-4 14v7c-1-5-1-10-1-15 0-3-1-5 0-7zm342-66c2 1 3 2 4 3h0v2c1 1 4 2 3 4-5-2-9-3-14-3 2-3 5-3 9-3v-1c-1 0-2-1-3-1l1-1z" class="I"></path><path d="M320 726l7 6c-2 1-4 2-6 4l-5-6v-2l3 2v-1-1l1-2z" class="E"></path><path d="M327 674l3-3c0 1 1 1 0 2 0 4-3 9-6 12 0-1-1-1-2-1l1-1-1-1h0c1-2 3-7 5-8z" class="M"></path><path d="M433 181l1 1c-2 4-14 7-18 9-1-1-2-1-4-2 1 0 0-1 0-1 6 0 10-2 15-4 2-1 4-1 6-3h0z" class="S"></path><path d="M619 627l3 6c1 1 2 0 3 0l1-3 1 3h0l2-1c-2 2-4 4-6 4-2 1-4 0-6-1h0c1-1 1-1 1-2-1-1-1-2-1-3s1-2 2-3z" class="I"></path><path d="M607 682c0 2-1 2-2 3 1 0 1 0 2-1h1c1 0 0 0 1-1s3-1 4-1c0 2-1 3-2 4-3 3-5 3-8 2-2-1-3-1-4-3h0 1c2 1 3 2 6 2v-1h-3c-1-1-2-1-2-3 3 1 4 0 6-1z" class="G"></path><path d="M299 258h1 0c-3 3-7 9-12 9l-2-2c2-4 10-6 13-7z" class="E"></path><path d="M518 262c2 0 6-1 8 0 3 0 6 3 9 4-3 1-13 1-15 0v-1c-1-2-2-2-3-3h1z" class="C"></path><path d="M631 558v-1c2-1 2-4 3-6 0 13-5 29-14 38l-3 2c6-9 15-16 13-29 1-1 1-3 1-4z" class="O"></path><path d="M501 255l-1-8c0-1 0-1 1 0 3 1 5 5 7 8 2 2 6 6 10 7h-1c-1 0-1 0-3 1h0c-4-3-7-6-11-10l-1 1-1 1z" class="B"></path><path d="M568 555l16-15c0 3 0 6-1 8-4 3-8 6-13 8l-2-1z" class="F"></path><defs><linearGradient id="AB" x1="544.715" y1="207.317" x2="541.345" y2="196.394" xlink:href="#B"><stop offset="0" stop-color="#ada9a8"></stop><stop offset="1" stop-color="#cfcecc"></stop></linearGradient></defs><path fill="url(#AB)" d="M538 193c2 1 3 2 5 4h0l1 1c1 1 1 2 2 4 1 0 3 2 3 3l1 1c0 1-1 2-2 3-1 0-2 0-3-1-2 1-3 1-4 1h-1 1v-1c1-2 2-3 2-5 0-3-3-5-5-7l1-1-1-2z"></path><path d="M545 208l2-2v-2l2 1 1 1c0 1-1 2-2 3-1 0-2 0-3-1z"></path><path d="M415 180c3-2 9-3 13-4h7c1 0 1 1 2 2l-5 1c0 1 1 1 1 2h0c-2 2-4 2-6 3l-1-1v-2c-1 0-1-1-2-1-2-1-6 0-9 1v-1z" class="K"></path><path d="M426 181c1-1 3-1 4-1l3 1c-2 2-4 2-6 3l-1-1v-2z"></path><path d="M439 253c-1-3-2-6-5-8l-1-1h-1c4 0 6 2 8 5 4 5 3 11 2 16-1 1-3 1-4 1-2-2-1-5-1-7l1 2c1-1 1-2 1-3s0-2 1-2v-1c0-1 0-1-1-2z" class="N"></path><path d="M476 259s0 7 1 7c-3-3-15-13-15-16 1-1 2-1 3-2 1 1 4 2 4 4 1 1 1 2 2 3s2 4 4 4h1z" class="C"></path><path d="M652 293c-2-3-2-7-5-10-1-3-4-5-6-7 1-2 1-5 3-7l2 2c1 2 2 4 2 6h-3-1c3 2 5 7 9 9l1 1c0 1 0 1-1 2h1c1 0 3-1 4 0h0l-6 4z" class="E"></path><path d="M545 253c2-1 3-1 5 0h1l2-2c1 0 2 1 3 1l3 3c2 1 3 2 4 3h-1 0-2v1h1c1 0 1 1 2 2 0 2 6 2 7 3l-1 1c-3 1-20-10-24-12z" class="G"></path><path d="M556 252l3 3c0 1-1 2-2 2l-1-1c-1 0-2-1-3-1l3-3z" class="M"></path><path d="M583 575h0l-1 1c-2 2-2 3-3 5 0 1-1 5-1 6 1 0 1 1 1 1 2 6 4 9 8 14-5-3-10-6-12-11-1-5 0-11 3-16 2 1 3 0 5 0z" class="N"></path><path d="M641 590h2c2 1 3 2 4 4 4 8 2 16-1 23v2c-1-2-1-4-1-5 0-7 2-16-3-21-1 0-2-1-2-2l1-1z" class="D"></path><path d="M659 309c4 7 7 14 12 20 1 1 2 2 2 4l-1 1c1 1 2 1 4 1l-1 1h3 1l2-1 1 1h-1c-2 2-3 3-4 6v5c1 1 2 1 3 2h-1c0 2 0 2-1 4-3-7-4-15-8-22s-9-13-12-21l1-1z" class="O"></path><path d="M319 578c-1 2-2 6-4 8-1 2-3 3-5 4h0c2-5 3-10 5-14 2-3 4-5 7-6s7 0 10 2c-3 2-12 0-13 4v2z" class="Q"></path><path d="M460 244c2-1 5 0 7 0-4 1-7 2-10 4-6 4-5 8-6 15v1h1c0-3 1-7 2-9s3-5 6-5l-2 2c-3 3-5 7-4 12l1 1h0c-1 1-3 1-4 1h-1-1c0-5-1-14 1-18 3-3 6-4 10-4z" class="I"></path><path d="M415 181c3-1 7-2 9-1 1 0 1 1 2 1v2l1 1c-5 2-9 4-15 4 0 0 1 1 0 1-2-1-3-2-5-3v-1c2-2 5-3 8-4z" class="E"></path><path d="M426 183l1 1c-5 2-9 4-15 4l1-1c2-6 9-2 13-4z" class="L"></path><defs><linearGradient id="AC" x1="438.34" y1="256.256" x2="423.82" y2="257.588" xlink:href="#B"><stop offset="0" stop-color="#a5a3a3"></stop><stop offset="1" stop-color="#e1dfdf"></stop></linearGradient></defs><path fill="url(#AC)" d="M437 259v-1c-1-2-1-3-3-5l-1 1c-2 2-3 5-4 8-1 2-2 4-5 4h-1c0-2 3-6 3-8 1-2 1-4 1-6 1-2 2-2 4-3 4-1 5 2 8 4h0c1 1 1 1 1 2v1c-1 0-1 1-1 2s0 2-1 3l-1-2z"></path><defs><linearGradient id="AD" x1="450.801" y1="188.985" x2="466.128" y2="197.767" xlink:href="#B"><stop offset="0" stop-color="#898686"></stop><stop offset="1" stop-color="#b2b2b1"></stop></linearGradient></defs><path fill="url(#AD)" d="M463 190h2c1 1 1 0 1 1l1 1 1-1c1 0 2 1 3 1l3 2c2 1 3 2 4 4l-6-2-27-2c5-3 12-3 18-4z"></path><path d="M471 192l3 2c2 1 3 2 4 4l-6-2c-2-1-4-2-5-4h2c1 1 1 0 2 0z" class="G"></path><path d="M654 625c1-1 2-3 2-5 1-2 1-5 3-7 0 5-2 10-4 15l-6 20c-2 3-2 7-4 10-1 2-2 4-3 7-1-2-1-3-1-5l2-7 4-8c0-1 1-3 1-4 1-1 1-3 2-4 1-4 2-8 4-12z" class="U"></path><path d="M647 645c0 1-1 3-1 4v4c-1 2-2 3-1 5-1 2-2 4-3 7-1-2-1-3-1-5l2-7 4-8z" class="K"></path><path d="M626 630c-1-3-3-6-3-9-1-3 0-6 2-8 1-2 4-4 7-5 2 0 3 2 4 3s2 2 2 4l-1 1h-1c-1-2-1-2-3-3-1 0-3 0-4 1-2 2-2 4-2 6 1 5 3 8 2 12l-2 1h0l-1-3zm-9 5c-4-3-5-9-9-11-2-1-4-2-6-1-2 0-3 1-4 3 0 1 0 0-1 1v-2c0-2 0-3 2-4 3-3 7-3 10-2 5 1 7 4 10 8-1 1-2 2-2 3s0 2 1 3c0 1 0 1-1 2zm-337-9v-1c1-3 2-6 0-10 0-1-2-3-3-4-3-1-6 0-9 1 0-2 1-3 1-4 1-1 3-2 5-2 2-1 4 0 6 1 3 3 5 7 5 11 1 3 0 6-1 9h-2-1-1v-1z" class="D"></path><path d="M456 187c0 1 1 1 1 2l-17 5c-7 2-16 3-23 2h0c1-2 1-2 3-2v1c2 0 2-1 4-2l6-3c5 2 10 0 15-1l11-2z" class="J"></path><path d="M488 247c3 0 6 1 8 3l5 6v-1l1-1 1-1c4 4 7 7 11 10h0l3 2v1c-4 0-7-3-10-3 0 1-1 1-1 2h-2v-1c-3-2-1-2-2-4-1 0-3 2-4 1l-1-2c-1-3-1-6-4-9-1-2-3-2-5-2v-1z" class="E"></path><path d="M269 271h0c0 2 1 2 2 3l2 3c0 1 1 2 1 3-1 3-6 7-9 8h-1v-1l-2 2h-3c0-2 0-3 1-4l3-4c0-1 1-1 1-2-1-1-1-1-1-2h0l-1-1c2-2 4-3 7-5z" class="Q"></path><path d="M271 274l2 3h-3v1h-1c-1 1 0 4 0 6-1 1-2 1-3 2l-1-1 2-1v-1-1-3h-1c-1-1 0-2-1-3l1-1h1l1 1c2-1 2-1 3-2z" class="I"></path><path d="M646 344c4 6 6 13 8 19 6 15 10 30 12 47 0 1 1 1 0 2-1-1-1-2-2-2l-1-1 1-1 1-3h-1-2c-1-1-1-2-1-4v-1h2c0-2-1-3-1-4-1-3-1-6-2-9-3-13-7-25-14-35h-1v-1h1c1-2 0-5 0-7z" class="K"></path><path d="M661 401h2c1 1 1 3 1 4h-2c-1-1-1-2-1-4z"></path><path d="M428 176l5-1c2-1 4 0 7 0 0 3-1 6 2 8 4 2 9 0 13 0 2 1 3 1 4 2 2 1 2 1 3 3l-5 1c0-1-1-1-1-2l-11 2c-5 1-10 3-15 1l10-5v-2c-1-2-2-4-3-5s-1-2-2-2h-7z" class="P"></path><path d="M459 185c2 1 2 1 3 3l-5 1c0-1-1-1-1-2h0l3-1v-1z" class="N"></path><path d="M410 250l-1 8 12 1c-2 1-4 4-7 5-2 1-6 1-9 2h-4-1v-4l-4 4c4-7 8-11 14-16z" class="D"></path><defs><linearGradient id="AE" x1="638.984" y1="534.067" x2="628.577" y2="526.17" xlink:href="#B"><stop offset="0" stop-color="#93928f"></stop><stop offset="1" stop-color="#b2b0af"></stop></linearGradient></defs><path fill="url(#AE)" d="M633 514c1 5 1 8 0 13 0 1-1 2-1 3s4 5 4 6c3 5 4 11 4 16l-1-5c0-3-2-7-5-9-3-3-8-3-12-4 1 0 1-1 1-2-1-2-1-1-3-2 1-1 2-1 4-1 0 0 1-1 2-1h0c1-1 1-2 1-2l1-1c-1-1-1-1-1-2l-1-1h1c1 1 1 1 2 1 3-3 3-5 4-9z"></path><path d="M314 666l1-1c0 1 1 2 1 4 2 4 2 8 1 13-1 1-1 2-1 4 3 0 4-2 6-4l1 1-1 1c1 0 2 0 2 1 3 0 5 1 7 2h0l-1 1c-3 2-7 3-9 4-3 1-9 1-11 0-1-1-1 0-1-1 0 0 1 0 2-1 0 0 2-1 2-2l-2 1h-1c1-2 3-4 4-5 1-2 1-4 1-5 1-2 1-6 1-8-1-2-1-4-2-5z" class="B"></path><path d="M322 684c1 0 2 0 2 1 3 0 5 1 7 2h0l-1 1c-6 0-12 1-17 2 2-2 6-3 8-5l1-1z" class="D"></path><path d="M544 198h2c0 1 0 2 2 3h1c3 2 3 4 6 4l12 14 1-1c0 1 1 1 1 2 2 2 4 4 5 6-3-1-5-3-7-5-1-1-3-2-5-3-1 0-3-1-4-2-2-1-5-2-7-3v-1l-3-3c1-1 2-2 2-3l-1-1c0-1-2-3-3-3-1-2-1-3-2-4z" class="O"></path><path d="M550 206c3 3 6 7 9 8 2 1 3 3 3 4-1 0-3-1-4-2-2-1-5-2-7-3v-1l-3-3c1-1 2-2 2-3z" class="Z"></path><path d="M613 491c-5 2-7 4-10 8v-1c-1 0-1 0-1-1 0-3 1-6 3-8l1-1c-1-1-2-1-4-1-1-1-2-1-3-3 0-1 0 0 1-1 6-4 12 1 19 3h0c1 0 2 1 3 1v1c-4 0-7 0-9 3z" class="D"></path><path d="M465 248l14-6-2 4c3 1 7 1 11 1v1c-2 1-4 2-5 3v2c-2 1-6 0-7 2v1 3h-1c-2 0-3-3-4-4s-1-2-2-3c0-2-3-3-4-4zM257 595l2 17c3-8-2-21 9-26h1v1c-2 2-4 6-5 9 2-1 4-2 6-2-5 6-9 11-9 19-1 9 4 20 7 28l-1 1-1-2-2-6c-1-1-1-3-1-4l-2-6c-1-2-2-4-2-6v-2c-1 0-1-1-1-2l-1-5v-2c-1-2-1-4-2-7v-2c0-1 0-3-1-4l1-1 2 14v-1-1-2-1-1-1-1c-1-1 0-2 0-4z" class="F"></path><path d="M682 364h1c1 0 2 1 4 2 1 0 1 0 2-1 1 13 4 26 8 38l1 3-2-2-1-1h0c-4-10-8-19-10-30l-3-9z" class="X"></path><defs><linearGradient id="AF" x1="604.26" y1="255.462" x2="631.373" y2="253.026" xlink:href="#B"><stop offset="0" stop-color="#b0b0af"></stop><stop offset="1" stop-color="#d1cece"></stop></linearGradient></defs><path fill="url(#AF)" d="M604 241c1 1 3 1 4 2 1 2 2 3 4 3l1 1s1 0 1 1v3c1 1 3 1 4 2l1-1 1 1-1 1c2 1 5 1 7 0h0 0c2-1 3-2 4-2 0 1 0 2 1 3s1 2 2 3c0 3 1 5 1 8-2-2-3-4-3-6l-1-2c-1 0-6 1-8 0-7 0-13-6-19-10h1v-1h1l-2-1c-1-1 0-3 1-5z"></path><path d="M604 241c1 1 3 1 4 2 1 2 2 3 4 3-1 1-2 0-3 1l1 1-1 1c-1 0-2-1-3-1l-1-1-2-1c-1-1 0-3 1-5z" class="C"></path><path d="M660 472c0 2 1 8 0 10-2 2-5 5-8 5s-6-1-8-3l-4-4h3l1 1c1 0 3 0 5-1h1v-1-3-1s0-1-1-1v-1-1h2c2 1 4 1 6 1 1 0 2-1 3-1z" class="M"></path><path d="M254 474c1-5 3-10 4-15l6 8c1 1 1 1 2 0 3-1 4-5 5-8l3 13-1 1h-1v-1l-1-1c-1 0-1 1-2 2-2 1-10 4-13 3-1 0-1-1-2-2z" class="D"></path><path d="M254 474c1 1 1 2 2 2 3 1 11-2 13-3 1-1 1-2 2-2l1 1v1h1l1-1c1 2 0 4-1 6-1 3-5 6-8 9-1 0-3 1-4 1s-4-1-5-3c-2-1-2-3-2-5v-6z" class="H"></path><path d="M260 482l3-4 2 2v1c-1 2-1 3-3 3-1 0-1-1-2-2z"></path><path d="M266 482c1-1 1-2 2-3 1-2 2-4 4-4l1 3c-1 3-5 6-8 9v-2h1l-1-1c1-1 1-1 1-2z" class="P"></path><path d="M254 480l1-1s1-1 1-2h1v4c0 1 1 2 2 3h1v-2c1 1 1 2 2 2 2 0 2-1 3-3l1 1c0 1 0 1-1 2l1 1h-1v2c-1 0-3 1-4 1s-4-1-5-3c-2-1-2-3-2-5z" class="N"></path><path d="M285 689c5 7 9 15 15 22 3 3 6 7 10 11 1 1 5 4 6 6v2l5 6c1 0 1 1 2 2l-3-1c-3-2-6-5-8-7-8-8-14-15-20-24-3-4-7-9-9-14h1c1 1 1 2 2 3l1-1-1-1c0-1-1-2-1-4h0z" class="B"></path><path d="M578 575c2-3 5-4 9-5 5 0 9 2 12 5 4 3 5 7 6 12l-3-2h-4l-3-3c-4-2-6-3-11-4-2 2-4 4-4 7l-1 3s0-1-1-1c0-1 1-5 1-6 1-2 1-3 3-5l1-1h0c-2 0-3 1-5 0z" class="F"></path><path d="M598 582l4 3h-4l-3-3h3z" class="C"></path><path d="M591 577c3 1 5 3 7 5h-3c-4-2-6-3-11-4l7-1z" class="G"></path><path d="M583 575c1-2 2-1 3-2l1 1h0c-1 0-2 1-3 2 2 1 5 0 7 1l-7 1c-2 2-4 4-4 7l-1 3s0-1-1-1c0-1 1-5 1-6 1-2 1-3 3-5l1-1h0z" class="I"></path><path d="M398 198h-1c-1-1-1-2-1-3 0-2 2-3 3-4 2-2 5-2 7-1 3 0 5 2 7 3h0c1-1 3-1 4-1l-2 1c-3 1-4 4-7 6-4 2-10 2-14 4-5 1-9 4-13 6-1 0-1-1-2-2 0-2-1-3-2-5 1 0 2 0 3 1l1 1c3-1 5-4 8-5h1c1 0 1 1 2 1 1 1 2 1 3 0 1 0 3 0 4-1l-1-1z" class="B"></path><path d="M377 202c1 0 2 0 3 1l1 1c3-1 5-4 8-5h1c-1 2-2 3-4 4l-1 1h0l-1 1c0 1-1 2-1 2-2 1-2 1-4 0 0-2-1-3-2-5z" class="J"></path><path d="M403 192c1 0 2 0 4 1 1 1 1 1 1 3-1 1-2 2-3 2-1 1-1 1-2 0-1 0-3-1-4-2v-1c1-2 2-2 4-3z"></path><defs><linearGradient id="AG" x1="298.621" y1="636.306" x2="300.494" y2="616.78" xlink:href="#B"><stop offset="0" stop-color="#d2d0d0"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AG)" d="M281 632h0c1 1 1 1 3 1 5 0 8-8 12-12 3-3 7-4 12-4 3 0 6 2 8 4s2 3 2 6c-1 1-1 2-2 2l-1-1v-3c-1-2-3-3-4-3-10-2-12 9-19 12-2 2-6 3-8 2-4-1-6-3-7-6 2 0 2 0 4 2z"></path><defs><linearGradient id="AH" x1="637.151" y1="685.043" x2="628.349" y2="676.457" xlink:href="#B"><stop offset="0" stop-color="#777675"></stop><stop offset="1" stop-color="#939090"></stop></linearGradient></defs><path fill="url(#AH)" d="M643 653h0l-2 7c0 2 0 3 1 5-7 17-15 34-27 48-2 2-4 6-7 7v-1c0-2 0-2 1-4 10-10 18-22 24-35v-1l1-3h-2l3-7c1-2 3-5 3-7h0v-5c1-2 3-3 5-4z"></path><path d="M635 669l2 1v1l-4 9v-1l1-3h-2l3-7z" class="G"></path><path d="M643 653h0l-2 7c0 2-3 11-4 11v-1l-2-1c1-2 3-5 3-7h0v-5c1-2 3-3 5-4z" class="H"></path><defs><linearGradient id="AI" x1="357.563" y1="268.425" x2="397.391" y2="231.027" xlink:href="#B"><stop offset="0" stop-color="#a19e9e"></stop><stop offset="1" stop-color="#cccac9"></stop></linearGradient></defs><path fill="url(#AI)" d="M401 234c1 1 5 2 6 3-13 4-25 9-36 16-9 5-17 11-26 14l15-12c9-6 17-10 26-14l10-4c1-1 4-1 5-2v-1z"></path><path d="M282 518c1 1 2 1 4 2h4l4 3 2-1h0c-1 1-3 2-3 4h1v1 1c1 0 1 0 1 2v1h-2v3h-9c-3 1-7 4-8 6-4 5-4 11-3 17v2c-2-7-2-15 2-21 1-2 3-5 4-7 1-5 1-9 3-13z" class="B"></path><path d="M290 520l4 3-3 2c0-2-1-3-2-4l1-1z" class="G"></path><path d="M286 520h4l-1 1-3 3c0 1 1 1 1 1 1 1 2 1 2 2-1 0-1 0-2-1h-1-1c0-3 0-4 1-6z" class="C"></path><defs><linearGradient id="AJ" x1="292.368" y1="578.101" x2="320.363" y2="547.136" xlink:href="#B"><stop offset="0" stop-color="#979594"></stop><stop offset="1" stop-color="#b2b0af"></stop></linearGradient></defs><path fill="url(#AJ)" d="M305 547c2 0 6-2 8-2 1 0 3-1 6-1-1 1-1 2-1 2l-3 2c-7 4-13 9-15 16l-1 3v1c-2 5-1 10 2 15 0 1 1 1 2 2 0 0 0-1 1-1l1 1c-2 1-2 2-4 2-2-1-3-2-4-3-6-9-4-20 1-28 1-2 4-5 5-7h0l-1-2 1-1 2 1z"></path><path d="M303 549l-1-2 1-1 2 1v2l-1 1h-1v-1z" class="B"></path><path d="M632 676h2l-1 3v1c-6 13-14 25-24 35-1 2-1 2-1 4v1l-22 21-9-2c2-1 4 0 7-1l2-1c1-1 3-2 4-3h1l1-1 3-3h0c1-2 3-3 5-5 1-2 2-3 4-5-1-1-1-1-2-1v-2l3-3h1l1-1c3-2 6-6 8-10 6-8 12-18 17-27z" class="E"></path><path d="M355 785c20 10 42 16 65 19v1c2 0 3 1 4 1-1 0-2 0-3 1l-26-3v-1c-2-1-7-2-9-2l-3-1c-2-1-5-1-8 1-1 0-2 2-4 3v-1c-1-1-1-1-1-2l1-2c1 0 2 1 4 1v-1l-7-4c-2 0-4-1-6-2v-1c-1-2-4-1-5-4-1-1-1-2-2-3h0z" class="V"></path><path d="M368 795h4l7 2c-1 1-2 1-4 2l-7-4z" class="R"></path><path d="M640 480c-1-1-2-3-4-4l6-18c2 1 3 5 6 7h3c3-3 4-6 5-10 1 6 2 11 4 17-1 0-2 1-3 1-2 0-4 0-6-1h-2v1 1c1 0 1 1 1 1v1 3 1h-1c-2 1-4 1-5 1l-1-1h-3z" class="D"></path><defs><linearGradient id="AK" x1="279.974" y1="491.04" x2="285.109" y2="515.014" xlink:href="#B"><stop offset="0" stop-color="#c4c2c1"></stop><stop offset="1" stop-color="#f9f9f9"></stop></linearGradient></defs><path fill="url(#AK)" d="M281 492c1-1 1-2 2-2l2-1c3-2 6-2 10-2 1 0 5 1 6 2 1 0 1 1 2 2-8-1-15 0-21 5-7 6-12 14-14 23h-2l-1 5c0-1-1-2-1-3l-1-1h1v-2 1l-2 1v-4-1h0c0-2 1-4 1-5 3-7 6-13 12-18 1 1 1 1 3 2 1-1 2-2 3-2h0z"></path><path d="M281 492h1c-3 3-7 6-9 9l-1 1c-4 5-6 11-6 17l-1 5c0-1-1-2-1-3l-1-1h1v-2 1l-2 1v-4-1h0c0-2 1-4 1-5 3-7 6-13 12-18 1 1 1 1 3 2 1-1 2-2 3-2h0z" class="J"></path><path d="M305 655l2 1c1-2 0-5 0-7 6 4 10 9 15 14h0c1-3 2-5 3-8 3 6 4 10 3 16l-1 3c-2 1-4 6-5 8h0c-2 2-3 4-6 4 0-2 0-3 1-4 1-5 1-9-1-13 0-2-1-3-1-4-1-3-7-7-10-8v-2z" class="D"></path><path d="M317 216c2-1 5-2 8-3 3-2 8-6 12-4 1 0 2 0 2 2s0 2-1 4c-2 1-5-1-8-2l3 5c0 1 0 4-1 5l-5 7-1 2h0l3-3 1 1c-2 4-7 10-11 12 0-1 1-3 1-4v-3l1 1 1-2-1-3c0-1 1-1 2-2l-1-1-4-4-3-2v-3c0-1 1-2 2-3z" class="B"></path><path d="M321 231h3l1 1-3 6c-1-1 0-3 0-4l-1-3z" class="C"></path><path d="M318 222l-1-1v-1c2-1 4-2 7-1 1 0 2 1 2 3 1 3 0 5-2 8h0l-1-1h0l-1-1-4-4v-2z"></path><path d="M318 222l1 1c2 1 3 1 5 1 0-1 1-2 1-2l1 1c-1 2-2 3-3 6h0l-1-1-4-4v-2z" class="N"></path><path d="M276 662c9 20 21 39 36 55l8 9-1 2v1 1l-3-2c-1-2-5-5-6-6-4-4-7-8-10-11-6-7-10-15-15-22-2-4-5-8-6-12l-2-2v-2c-1-1-1-1-1-2h0c-1-2-1-2-1-3 1 0 1-1 1-1v-1c-1-1 0-3 0-4z" class="Q"></path><path d="M279 677c3 3 5 7 8 11l7 11 16 20 2-2 8 9-1 2v1 1l-3-2c-1-2-5-5-6-6-4-4-7-8-10-11-6-7-10-15-15-22-2-4-5-8-6-12z"></path><path d="M312 717l8 9-1 2c-4-2-6-6-9-9l2-2z" class="I"></path><path d="M249 525l4 45 2 17c1 3 2 6 2 8s-1 3 0 4v1c0 1 0-1 0 1v1 1 2 1 1l-2-14-1 1c1 1 1 3 1 4v2c1 3 1 5 2 7v2l1 5c0 1 0 2 1 2v2c0 2 1 4 2 6l2 6c0 1 0 3 1 4l2 6 1 2c0 2 0 2-1 3h-1l-2-6-1-4-1-4-1-2v-2l-1-1v-3c-1-1-1 0-1-1l-1-7-1-4-1-2c-1-2-1-5-1-7l-1-4v-3c-1-1-1-1-1-2l-1-7v-2c-1-4-1-9-2-13-1-1 0-3 0-5-1-1-1-3-1-5s-1-4 1-6h0c-1-1-1-2-2-4l2-2h-1c-1-1-1-1-1-2s0-2 1-3c0-2-1-3-2-4l1-1c0-2 1-2 1-3 0-2-1 0-2-1v-3l1-2h1v-1h1v-4h0z" class="C"></path><path d="M249 555c1 2 1 3 1 5-1 1-1 1-2 1 0-2-1-4 1-6z"></path><path d="M254 594v-1-2c0-1-1-2-1-3-1-3-1-8-1-11h0c-1-2-1-5-1-7 1-1 1-1 1-2 0 1 0 1 1 2l2 17c1 3 2 6 2 8s-1 3 0 4v1c0 1 0-1 0 1v1 1 2 1 1l-2-14-1 1z" class="Q"></path><path d="M282 518l8-15h1c1 1 1 3 1 4v3h0c5-2 9-4 14-7l-6 14 9 1c-1 2-3 5-5 6-2 2-4 3-6 4h1c1 1 3 2 5 3v1c-1 1-2 2-4 2-2 1-4 0-7 0v-3h2v-1c0-2 0-2-1-2v-1-1h-1c0-2 2-3 3-4h0l-2 1-4-3h-4c-2-1-3-1-4-2z" class="D"></path><path d="M420 804c20 3 41 4 62 2 8-1 15-3 23-5 24-5 48-15 69-28-1 1-2 3-3 4-4 2-7 6-10 8-1-1-2-1-3-1-3 1-6 3-9 4l-28 13h0c-1 0-3 1-4 1-3-1-15 3-20 4l-10 2c-14 1-29 2-43 1v1c-3 0-5 0-7-1l-16-2c1-1 2-1 3-1-1 0-2-1-4-1v-1zm256-177c0 1 1 1 0 2 0 1 0 1 1 2 1-2 1-3 3-5-2 3-3 6-4 10 0 0 0 2-1 3h-1c-1 6-4 12-5 19-1 3-4 6-3 9l-1 1c-1 4-3 7-5 11-10 17-22 34-35 49-5 7-11 13-17 18v-1h0l2-3v-1c0-1 2-2 3-3l9-10 9-11c12-15 22-32 30-49l7-16 8-25z" class="Z"></path><path d="M661 668l7-16c0 3-1 6-2 9s-2 5-5 7z" class="V"></path><defs><linearGradient id="AL" x1="358.908" y1="221.555" x2="354.651" y2="198.564" xlink:href="#B"><stop offset="0" stop-color="#919191"></stop><stop offset="1" stop-color="#aea9a7"></stop></linearGradient></defs><path fill="url(#AL)" d="M367 196l3-2c1 2 1 3 2 4l1 2c-2 0-3 1-4 2l3 6c2 1 3 2 3 4-2 1-7-2-9-1-1 1-2 2-2 3-2 1-4 2-5 3l-4 2c-1 0-1 0-2-1 0-2 0-3 1-4h0c-3 3-5 7-8 10-2 2-6 4-9 6 2-2 4-4 5-7-1-1-1-2 0-4 0-1 13-16 16-19l4-4h5z"></path><defs><linearGradient id="AM" x1="355.466" y1="213.98" x2="349.57" y2="203.712" xlink:href="#B"><stop offset="0" stop-color="#050b09"></stop><stop offset="1" stop-color="#3e393a"></stop></linearGradient></defs><path fill="url(#AM)" d="M358 200l4-4h5c-2 2-5 4-7 6-7 6-12 14-18 21-1-1-1-2 0-4 0-1 13-16 16-19z"></path><path d="M362 206l1-1c1 0 2-1 3-1s2 1 2 2l1 2h3c2 1 3 2 3 4-2 1-7-2-9-1-1 1-2 2-2 3-2 1-4 2-5 3h-2 0c0-2-1-3 0-4v-1c1-3 3-5 5-6z" class="C"></path><path d="M357 217h0c0-2-1-3 0-4v-1c1-3 3-5 5-6 1 1 3 2 3 3-1 2-2 3-3 3s-1 0-1-1h-1c-1 1-1 0 0 1v1c0 1-1 3-3 4z" class="L"></path><defs><linearGradient id="AN" x1="268.273" y1="571.411" x2="301.991" y2="561.558" xlink:href="#B"><stop offset="0" stop-color="#adabaa"></stop><stop offset="1" stop-color="#e9e7e9"></stop></linearGradient></defs><path fill="url(#AN)" d="M313 612c-7-1-13-3-19-7-21-14-32-42-33-66-1-4-1-8 0-12v-6l-5-21c3 4 4 10 4 15 1 1 1 2 1 3l1-3v1 4l2-1v-1 2h-1l1 1c0 1 1 2 1 3l1 1c0 1-1 3-1 4 0 2 0 2-1 4v7c0 9 2 17 6 26 3 8 7 15 12 22 3 3 6 7 10 10h1c1 1 3 2 3 3 4 5 14 7 19 7h5c-1 1-2 2-4 2l-1 2h-1-1z"></path><defs><linearGradient id="AO" x1="658.189" y1="363.016" x2="640.93" y2="374.406" xlink:href="#B"><stop offset="0" stop-color="#918e90"></stop><stop offset="1" stop-color="#b9b8b6"></stop></linearGradient></defs><path fill="url(#AO)" d="M662 433c-1-5-1-10-2-14-2-10-4-20-7-30l-8-24c-1-4-4-9-5-13 0-2 1-4 1-6l1-11 2 4c1 2 2 3 2 5s1 5 0 7h-1v1h1c7 10 11 22 14 35 1 3 1 6 2 9 0 1 1 2 1 4h-2v1c0 2 0 3 1 4h2 1l-1 3-1 1c-1 2-1 3-1 6v-1-5h0l1-1h0v-2h-2c0-1-1-2-1-2 1-2 0-3 0-4v-1h1l1-1c-2 0-2 1-3 2-1-1-1-1-1-2 0 0-1 0-1 1 1 3 2 7 2 11 1 4 2 8 2 12l1 6c1 2 1 3 0 5z"></path><path d="M645 351v-5l-1-3c0-2 0-2-1-4h0 1c1 2 2 3 2 5s1 5 0 7h-1z" class="S"></path><path d="M645 352h1c7 10 11 22 14 35 1 3 1 6 2 9-1 1-2 1-3 1l-7-22c-3-7-7-16-7-23z"></path><path d="M622 534c-6 0-10-2-15-4l5-3c-5-5-7-9-9-15 2 0 5 1 7 1 3 1 6 1 9 1l-1-12c3 2 4 5 7 8 1 1 1-1 3-1 2 1 3 3 5 5-1 4-1 6-4 9-1 0-1 0-2-1h-1l1 1c0 1 0 1 1 2l-1 1s0 1-1 2h0c-1 0-2 1-2 1-2 0-3 0-4 1 2 1 2 0 3 2 0 1 0 2-1 2z" class="D"></path><path d="M624 529l-1-2c0-1-1-1-1-2l2-1c0-1 1-1 2-2l1 1c0 1 0 1 1 2l-1 1s0 1-1 2h0c-1 0-2 1-2 1z" class="M"></path><path d="M235 644h0v-2-1c11 31 26 62 47 87 7 8 14 15 21 22l10 8c-2 0-3-1-4-2l-1 1 1 1h-1l-4-2-2-2h1l-18-16c0-2-2-2-3-5-2-3-6-7-9-10-3-4-7-9-10-14-3-4-6-9-8-13-3-6-5-12-8-18l-9-18h1c0-2-1-2-1-4l1 1v-1l-4-12z" class="R"></path><path d="M281 666c0-1-2-3-3-4l2-2c4-4 10-6 15-7 3 0 8 0 10 2v2c3 1 9 5 10 8l-1 1c1 1 1 3 2 5 0 2 0 6-1 8l-1-3c0-1-3-3-4-3-3-1-6-1-8 0-4 2-2 6-3 9-2-1-1-2-2-3v-1c-1 2-3 3-4 4 0-2-1-5 0-6 1-4 5-6 6-10-1-1-2-1-3-2-2-1-7-1-9 1-2 1-3 2-4 3l-2-2z" class="D"></path><path d="M281 666c0-1-2-3-3-4l2-2c4-4 10-6 15-7 3 0 8 0 10 2v2c3 1 9 5 10 8l-1 1c-4-5-8-8-14-9-6 0-13 2-17 6-1 1-2 2-2 3z" class="C"></path><defs><linearGradient id="AP" x1="232.904" y1="574.012" x2="214.834" y2="574.557" xlink:href="#B"><stop offset="0" stop-color="#5d5c5c"></stop><stop offset="1" stop-color="#81807f"></stop></linearGradient></defs><path fill="url(#AP)" d="M215 497v6h1v-4c1-3 0-6 1-9v-1l1 50c1 21 3 42 7 62l7 27c1 4 3 9 3 13v1 2h0l-3-5v1h-1 0c-2-2-3-7-3-10l-4-13c-2-6-3-12-4-18 0-2 0-5-1-6 0 0-1-1-1-2v-3l-1-9c-1-5-3-10-3-15-1-6 0-12 0-17v-30c1-6 1-14 1-20z"></path><path d="M320 764c11 8 22 15 35 21h0c1 1 1 2 2 3 1 3 4 2 5 4v1c2 1 4 2 6 2l7 4v1c-2 0-3-1-4-1l-1 2c0 1 0 1 1 2v1l2 2h-1c-8 1-14-3-21-7h0c-3 0-4-4-7-4v1h0c1 1 1 2 1 2l1 1v1c-1-1-3-4-4-5l-16-17h2l1-1-5-7c-1 0-3-3-4-3v-3z"></path><path d="M350 792c-4-2-9-7-12-11 2 2 4 3 7 5 1 1 4 1 5 3v3z" class="W"></path><path d="M350 789c2 0 3 1 4 1h1c-1 3-1 4 1 6h0l-1 2c-2-2-4-3-5-6v-3z" class="K"></path><path d="M356 796l2 2v-1c1-1 2-1 2-3 2 0 3 1 4 1 1 1 2 1 2 2s0 1-1 3h2v1l2 2c-2 0-5-1-7-2-2 0-5-2-7-3l1-2z" class="U"></path><defs><linearGradient id="AQ" x1="326.51" y1="781.232" x2="343.99" y2="790.768" xlink:href="#B"><stop offset="0" stop-color="#9c9b98"></stop><stop offset="1" stop-color="#d3d0d2"></stop></linearGradient></defs><path fill="url(#AQ)" d="M326 778h2l1-1 22 22h0c-3 0-4-4-7-4v1h0c1 1 1 2 1 2l1 1v1c-1-1-3-4-4-5l-16-17z"></path><path d="M320 764c11 8 22 15 35 21h0c1 1 1 2 2 3 1 3 4 2 5 4v1l-7-3h-1c-1-3-14-10-17-11-5-2-9-6-13-9-1 0-3-3-4-3v-3z" class="R"></path><path d="M607 682c2-2 3-3 3-5 0-1-1-3-2-3-2-2-6-2-9-2-2 1-4 2-5 4-2 3-1 5 0 8h-1v-2c-2-5 0-10 2-14-2 1-3 5-4 7-1 5 0 11 3 15h0c-4-1-7-4-10-6-3-1-6-2-7-4h0c1-2 3-3 5-3h1c-1-9 1-15 6-22l3 10h0l12-17v6c3-1 7-3 9-2-3 2-6 3-9 6h0c3-2 7-3 11-4 4 3 10 5 13 10v1h1l3-7c0 1 0 2-1 3v2c-1 2-1 2-2 3-1 2-1 2-1 4-1 3-4 8-6 11h0c2-4 4-8 5-12-2-4-5-7-10-7-1-1-4-1-6 0-2 6 4 13 3 19l-1 1c-1 0-3 0-4 1s0 1-1 1h-1c-1 1-1 1-2 1 1-1 2-1 2-3zM319 544h2c1-1-1-2-1-4h1c9 6 21 7 27 18 5 8 7 17 5 26-1 9-7 18-15 23-7 5-16 6-25 5h1 1l1-2c2 0 3-1 4-2 5-1 9-2 14-4 7-4 12-12 14-19 1-7 1-15-3-21s-10-10-17-12c-6-1-14 1-19 5-4 3-7 8-7 13-1 4-1 10 2 14-1 0-1 1-1 1-1-1-2-1-2-2-3-5-4-10-2-15v-1l1-3c2-7 8-12 15-16l3-2s0-1 1-2z" class="D"></path><path d="M319 544h2c1-1-1-2-1-4h1c9 6 21 7 27 18-2-1-4-5-7-5l1 1c1 0 2 1 3 2h0-1c-5-4-11-8-17-8-3-1-8 0-12 0l3-2s0-1 1-2z" class="C"></path><path d="M266 519h2c-4 21 1 42 12 60 7 11 18 21 32 24 5 2 11 1 16-2 6-3 9-9 11-15 1-5-1-12-4-17-2-4-7-7-11-7-5-1-10-1-14 2-3 2-5 6-6 10v5 3c-2-2-2-9-1-12 0-4 3-8 6-10 5-4 12-5 18-4 5 1 11 4 14 8 4 6 5 14 4 21-2 7-6 14-13 18-7 5-16 5-24 3-5-1-8-3-12-5 0-1-2-2-3-3h-1c-4-3-7-7-10-10-5-7-9-14-12-22-4-9-6-17-6-26v-7c1-2 1-2 1-4 0-1 1-3 1-4l-1-1 1-5z" class="F"></path><path d="M645 544c2-5 1-11 1-16 1 0 2 1 3 2v2h-1v5 2c1 2 0 9 0 12-3 11-7 21-13 31-2 3-5 7-8 10h-1c-2 1-3 3-5 5l-3 2h-1c-9 6-19 8-30 8-3-1-7-2-10-4-7-4-11-13-13-21 1 0 1 0 0-1v-11c1-7 6-12 12-16 2-1 5-3 7-4l1-1h0c7-3 15-4 23-4l4-1c0 1-1 3-1 4-3 4-10 5-14 6 2 2 5 3 7 5-8-1-16-2-24 2-4 3-9 8-10 13-1 7 1 14 5 19 3 5 8 8 14 9l1 1h1c11 2 22-4 30-10 16-12 22-31 25-49z" class="D"></path><path d="M584 549c7-3 15-4 23-4-5 3-14 4-20 5l-3-1z" class="O"></path><path d="M606 456v-1c-1-3-2-6-4-8l1-1c1 0 3 0 4 1 2 2 3 5 5 8h0c1-3 8-11 8-12-2-4-3-10 0-14 1-3 4-7 7-8h1c1 2 0 6 0 8 7-2 11-4 16-8 0 6-1 11-1 18h1c3 0 6 0 8 2 0 0 1 1 1 3-2 2-7 3-7 7-3 2-6 3-10 2-2 0-3-1-4-3-3-3-6-3-9 0-4 4-6 9-6 14-1 7 3 14 8 18 7 7 15 12 19 21 0 1-1 1-2 1l-1-1c-5-7-10-11-17-15h0c-1-1-2-2-4-3h0c-8-6-12-20-14-29z" class="D"></path><path d="M606 456c1 1 2 2 2 3 3 5 4 10 6 14 2 3 5 7 6 10v2c-8-6-12-20-14-29z" class="J"></path><defs><linearGradient id="AR" x1="314.92" y1="361.853" x2="178.673" y2="426.842" xlink:href="#B"><stop offset="0" stop-color="#6d6c68"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#AR)" d="M249 328v1l11-19c4-7 8-15 14-22 1-1 3-3 5-4l1 1c-1 4-4 7-6 10l-9 15c-3 4-5 8-7 12-6 12-11 25-16 37-16 42-24 85-25 130v1c-1 3 0 6-1 9v4h-1v-6c0-6 0-11-2-16 0 3 0 7-1 10-2-17 0-34 3-51l3-17c2-5 3-12 5-18 4-16 8-33 14-50 2-6 5-13 8-19 1-3 2-5 4-8z"></path><path d="M627 494c1-2-2-3-3-4v-2c7 4 12 8 17 15l1 1c1 0 2 0 2-1 2 4 3 8 5 11 2-2 2-6 2-8l1-6 1 6h0 1c1-2 3-4 5-6-3 5-6 10-7 15-1 4 0 8 0 12 1 4 1 9 0 13-1 21-8 40-23 56-5 6-12 11-19 14-10 4-22 4-32 0-7-3-13-10-16-17-4-9-3-20 0-28 1-3 4-8 6-10l2 1c5-2 9-5 13-8l1 1-1 1c-2 1-5 3-7 4-6 4-11 9-12 16v11c1 1 1 1 0 1 2 8 6 17 13 21 3 2 7 3 10 4 11 0 21-2 30-8h1l3-2c2-2 3-4 5-5h1c3-3 6-7 8-10 6-10 10-20 13-31 0-3 1-10 0-12v-2-5h1v-2c-1-1-2-2-3-2 0 5 1 11-1 16 0-6 1-12 0-18-2-14-7-24-18-32z" class="J"></path><path d="M564 582c2 8 6 17 13 21 3 2 7 3 10 4 1 0 2 1 3 1-2 1-4 0-6 0-6-1-12-5-15-10-3-4-5-11-5-16z"></path><path d="M501 201c3 2 6 2 9 3 5 0 9 1 13 3 2 0 4 1 5 2l9 3c6 2 11 4 17 7 22 10 45 26 62 44 1 1 2 2 2 3-2 0-3-1-5-2-5-3-9-7-13-11-8-7-18-14-27-20-9-5-18-9-27-13-13-7-27-12-42-15-16-3-31-4-47-4s-34 1-50 5c-30 7-58 22-84 41-5 3-9 7-13 11-3 3-6 6-9 8-1 0-2 1-3 1-2 0-2-1-3-2 3-3 7-5 10-8 4-3 7-6 10-9 6-4 12-8 18-11l10-7c5-4 27-17 32-16h1c4-3 9-5 14-6 20-8 42-11 63-10l27 1 1-1 14 2c2 0 6 0 6 1z" class="Z"></path><path d="M275 492c6-5 12-10 16-17 4-5 5-12 4-18-1-4-4-9-7-10-1-1-3-1-4-1s-2 2-2 2c-2 3-4 5-6 6s-4 1-6 0l-1-3c-3-1-7-1-10-1l12-13-2-14 2 1c4 3 9 4 13 6l-2-10h2l2 2c4 2 8 6 9 10v1c0 3 0 6-1 9v1c1 4 4 8 7 12 4-3 8-4 12-6-1 3-3 6-5 9l-8 14c-2 3-4 6-5 8-1 1 0 2 0 3 4 0 7-4 11-4 3-1 6 0 9 2v1c-2 2-4 3-7 4l1 1h1c3 3 3 9 3 13-3-5-6-7-10-9-1-1-1-2-2-2-1-1-5-2-6-2-4 0-7 0-10 2l-2 1c-1 0-1 1-2 2h0c-1 0-2 1-3 2-2-1-2-1-3-2z" class="D"></path><defs><linearGradient id="AS" x1="697.726" y1="460.035" x2="644.664" y2="461.34" xlink:href="#B"><stop offset="0" stop-color="#212121"></stop><stop offset="1" stop-color="#434141"></stop></linearGradient></defs><path fill="url(#AS)" d="M676 627c3-14 8-27 10-41 5-26 7-53 8-79 1-36-1-71-8-106-4-17-10-34-16-51-5-13-11-26-18-38-5-8-12-16-14-25 4 4 8 9 11 14 2 3 4 6 6 10 4 6 7 12 10 19 5 9 8 19 12 28 2 4 4 8 5 12 3 8 4 16 6 25 3 10 5 21 6 32l4 44 1 10c0 2-1 5 0 7h0c0 1 0 1 1 1v1c0 5-1 11-1 16-1 4 0 9 0 14h-1l-2 23-4 24-1 13c-2 12-4 23-7 34-1 2-4 7-3 9l1 1-2 2c-2 2-2 3-3 5-1-1-1-1-1-2 1-1 0-1 0-2z"></path><path d="M401 234c-4 0-8 2-12 3-12 5-25 11-36 19l-12 8c-1 1-2 2-3 2-3 1-6 1-9 1 15-12 32-23 50-30 32-14 68-18 102-13 39 5 75 20 107 43l-13-1s0-1-1-2c-2-2-7-4-10-5-1 0 0 1-1 2-1-1-1-2-2-2h-1v-1h2 0 1c-1-1-2-2-4-3l-3-3c-1 0-2-1-3-1l-2 2h-1c-2-1-3-1-5 0-8-4-16-8-24-11-36-13-77-14-114-5-1-1-5-2-6-3zM283 692c-3-4-5-8-7-13-14-26-22-56-28-86-3-16-5-33-6-50-5-63-2-128 22-188l8-18c1-3 2-6 4-8 0 3 0 7-1 10l-5 13c-4 12-8 23-10 34-3 12-5 24-6 35-1 5-1 9-2 14v5h0l-1 7-2 29v12 21l-1 5-1 1v1c0 1-1 1-1 2 0 2 0 2 1 3v2l2 2v4h-1v1h-1l-1 2v3c1 1 2-1 2 1 0 1-1 1-1 3l-1 1c1 1 2 2 2 4-1 1-1 2-1 3s0 1 1 2h1l-2 2c1 2 1 3 2 4h0c-2 2-1 4-1 6s0 4 1 5c0 2-1 4 0 5 1 4 1 9 2 13v2l1 7c0 1 0 1 1 2v3l1 4c0 2 0 5 1 7l1 2 1 4 1 7c0 1 0 0 1 1v3l1 1v2l1 2 1 4 1 4 2 6h1c1-1 1-1 1-3l1-1 5 15c1 2 3 4 3 6 0 1-1 3 0 4v1s0 1-1 1c0 1 0 1 1 3h0c0 1 0 1 1 2v2l2 2c1 4 4 8 6 12h0c0 2 1 3 1 4l1 1-1 1c-1-1-1-2-2-3h-1z" class="D"></path><path d="M250 419h0c0 1 1 2 1 3l1 2-3 1 1-6z" class="M"></path><path d="M268 648h0l1 2c1 1 0 2 0 3-2-1-2-2-2-3l1-2zm4 11l1-1v1c1 2 1 2 0 4-1 0-1 0-2-1 0-2 0-2 1-3z"></path><path d="M248 530l-2-2c0-1-1-2 0-3 0-1 0-1 1-2l2 2v4h-1v1z" class="H"></path><path d="M248 436l1 1 2-2h1v5h0l-1-1c-2 2 0 2-1 4 0 0-1 1-2 1s0 0-1-1c0-1 1-2 1-3l2-2-1-1v1l-1-1v-1z" class="P"></path><path d="M246 485l1 1c0 2-1 5 0 7 0 3 0 5-1 7l-1-2c-1-3 0-11 1-13z"></path><path d="M247 443c1 1 0 1 1 1s2-1 2-1c1-2-1-2 1-4l1 1-1 7v-2h-1v1c0 2 0 3-1 4s-2 1-2 1v-8z" class="N"></path><path d="M252 424c0-1 1-2 2-3-1 5-1 9-2 14h-1l-2 2-1-1 1-11 3-1z" class="K"></path><path d="M247 486c0-1 1-2 1-2h0c0 2 0 2 1 4v21l-1 5-1 1c-1 0-1 0-2-1v-3c1 0 1-1 2-1-1-1-1-1-1-2l-1-1v-5l1-1 1-1h-1c1-2 1-4 1-7-1-2 0-5 0-7z" class="C"></path><path d="M245 502h2v4c-1 0-1 1-2 1v-5z"></path><path d="M247 451s1 0 2-1 1-2 1-4v-1h1v2l-2 29v12c-1-2-1-2-1-4h0s-1 1-1 2l-1-1v-1s0-1-1-1c0-2 0-4 1-6v-2l-1-2c0-1 0-3 1-4h1c0-2 0-2-1-2v-2l1-1h1v-1c-1 0-2 0-2-1 0-2 0-2 1-4l1 1v-2c-1-1-1-1-2-1 0-2 0-4 1-5z" class="J"></path><path d="M246 477c1 1 2 4 1 6 0 1 0 1-1 1 0 0 0-1-1-1 0-2 0-4 1-6z"></path><path d="M250 419c1-2 2-4 3-7h-1c-1-1-1-2-1-2 1-2 2-3 2-4 3-15 6-29 11-43 2-3 3-8 6-11-4 12-8 23-10 34-3 12-5 24-6 35-1 1-2 2-2 3l-1-2c0-1-1-2-1-3h0z" class="P"></path><path d="M662 433c1-2 1-3 0-5l-1-6c0-4-1-8-2-12 0-4-1-8-2-11 0-1 1-1 1-1 0 1 0 1 1 2 1-1 1-2 3-2l-1 1h-1v1c0 1 1 2 0 4 0 0 1 1 1 2h2v2h0l-1 1h0v5 1c0-3 0-4 1-6l1 1c1 0 1 1 2 2 1-1 0-1 0-2 2 7 2 13 3 21 1 16 3 32 3 48l-1 54-3 35c-2 15-5 30-9 45h0c-2 2-2 5-3 7 0 2-1 4-2 5-2 4-3 8-4 12-1 1-1 3-2 4 0 1-1 3-1 4l-4 8h0c-2 1-4 2-5 4v5h0c0 2-2 5-3 7l-3 7c-5 9-11 19-17 27-2 4-5 8-8 10l-1 1h-1l-3 3v2c1 0 1 0 2 1-2 2-3 3-4 5-2 2-4 3-5 5h0l-3 3-1 1h-1c-1 1-3 2-4 3l-2 1c-3 1-5 0-7 1-1 0-1 1-2 0 2-3 6-5 9-8 5-4 10-9 15-15 9-10 14-23 22-34l1-1h0c2-3 5-8 6-11 0-2 0-2 1-4 1-1 1-1 2-3v-2c1-1 1-2 1-3l10-23 6-18c1-6 2-12 4-18 6-29 10-59 12-88 0-12 1-23 1-34 0-15-2-30-3-44z" class="M"></path><path d="M644 643c0-1 0-1-1-2 1-1 1-2 2-3 0-1 1-1 2-1v3c-1 1-1 2-3 3zm3-9c0-2 0-5 1-7 1-1 1 0 2-1 0 3 0 5-2 7l-1 1z"></path><path d="M647 640l1 1c0 1-1 3-1 4l-4 8h0l-1-1h-1c0-1-1-1-1-2l1-2 1-1h1l2-3-1-1c2-1 2-2 3-3z" class="I"></path><path d="M651 622c1 0 2 0 3-1v2 2c-2 4-3 8-4 12-1 1-1 3-2 4l-1-1v-3h0c1-1 1-2 1-3h-1l1-1c2-2 2-4 2-7h1c1-1 1-3 0-4z" class="O"></path><path d="M638 662c0 2-2 5-3 7l-3 7c-5 9-11 19-17 27-2 4-5 8-8 10 2-7 9-13 13-20 6-9 10-19 15-30 1-1 2-1 3-1z"></path><path d="M666 410c2 7 2 13 3 21 1 16 3 32 3 48l-1 54h-4l-1 1 1-8v-6c0-1 0-2 1-2l1-1-2-2c0-3 0-5 1-8v-1-1-8-13c0-1 0-3 1-5 0-1 1-1 0-3l-1-1v-5h2v-1c-1-1-2-2-2-3 0-2-1-5 1-7h0v-1c-2-2-1-5-1-8-1-1 0-2-1-3s-1-1-1-3c1-1 1-2 1-3h0c0-1 0-2-1-3 0-1 0-2-1-4 2-1 2-1 2-3v-1l-1 1-1-1c-1-2-1-3 1-5v-1c0-1 0-2-1-3s-1-1-1-3c1-1 1-1 1-2-1-2-1 0-2 0l-1-1c0-3 0-4 1-6l1 1c1 0 1 1 2 2 1-1 0-1 0-2z" class="U"></path><path d="M668 450h1c0 3 1 5 0 8-2-2-1-5-1-8zm-1 76v-6c0-1 0-2 1-2 2 1 2 3 1 6v1l-2 1z"></path><path d="M668 506l2 1c0 2 0 6-1 8h0-1v-8-1zm0-1v-8-13c0-1 0-3 1-5 0-1 1-1 0-3l-1-1v-5h2v-1c-1-1-2-2-2-3 0-2-1-5 1-7 2 3 1 7 2 10v2c-1 2 0 5 0 7v20h0-2 0c1 1 2 2 2 3s0 3-1 4h-2z"></path><defs><linearGradient id="AT" x1="671.48" y1="570.989" x2="654.625" y2="572.104" xlink:href="#B"><stop offset="0" stop-color="#706e6e"></stop><stop offset="1" stop-color="#858583"></stop></linearGradient></defs><path fill="url(#AT)" d="M666 534l1-1h4l-3 35c-2 15-5 30-9 45h0c-2 2-2 5-3 7 0 2-1 4-2 5v-2-2c-1 1-2 1-3 1v-1c0-2 0-2 1-3s1-1 2-1v-3c0-2 1-4 1-7l4-18 7-43c-1-4 0-8 0-12z"></path><path d="M656 607c2 3 1 4 1 6-1 4-1 7-3 10v-2c-1 1-2 1-3 1v-1c0-2 0-2 1-3s1-1 2-1v-3h0c1-1 2-5 2-7z" class="P"></path><path d="M666 546c0 1 1 2 0 4 0 2-1 5 0 7l-10 50c0 2-1 6-2 7h0c0-2 1-4 1-7l4-18 7-43z"></path><path d="M367 189c3-2 6-3 8-5 8-4 17-8 25-10 16-4 33-8 50-9 4 0 9 1 13 1 22 2 47 4 67 16l8 5c-1 1-1 2-1 3 2 2 5 4 6 6v1h0c-2-2-3-3-5-4l1 2-1 1h-3c-2 2-1 4-2 7 0 2-2 4-3 6-3-4-8-8-14-8h-15c0-1-4-1-6-1l-14-2h-3c-1-2-2-3-4-4l-3-2c-1 0-2-1-3-1l-1 1-1-1c0-1 0 0-1-1h-2l-1-2c-1-2-1-2-3-3-1-1-2-1-4-2-4 0-9 2-13 0-3-2-2-5-2-8-3 0-5-1-7 0l-5 1c-4 1-10 2-13 4-3 1-10 2-11 4v1h0l-1-1c-2 0-6 1-8 3-1 1-1 2-2 4v6c2 1 3 1 5 1l1 1c-1 1-3 1-4 1-1 1-2 1-3 0-1 0-1-1-2-1h-1c-3 1-5 4-8 5l-1-1c-1-1-2-1-3-1h-1c1-2 5-2 7-3s4-2 5-4l-1-1-6 3-8 3-1-2c-1-1-1-2-2-4l-3 2h-5l-4 4v-2c2-2 5-7 7-8l2-1z" class="D"></path><path d="M386 188h2c0 2 0 2-1 3h-1c-1 0-1-1-2-1 1-2 1-2 2-2zm75-13c1 0 3 0 4 1 0 1-1 2-2 3h-1c-1 1-2 0-2 0-1-2-1-2 0-3l1-1z"></path><path d="M455 183c4 0 6 1 8 3l5 5-1 1-1-1c0-1 0 0-1-1h-2l-1-2c-1-2-1-2-3-3-1-1-2-1-4-2z" class="O"></path><path d="M445 175h2c2 0 2 1 2 2 0 2 0 2-1 3h-3c-1-2-1-2-1-4l1-1z"></path><path d="M482 175h4l7-1c3 0 6 2 8 3l-5 1-9-2-5-1z" class="M"></path><path d="M370 194c1 0 4-2 5-2s1 1 2 1v1l-2 2c0 1 0 1 1 1h5l-8 3-1-2c-1-1-1-2-2-4z" class="C"></path><path d="M473 178v1 1c0 1 0 1 1 0l1 1c-1 1-1 2 0 4 1 1 2 3 4 4l-3 2-1-1c-2-2-5-5-5-8 0-2 2-3 3-4z"></path><path d="M387 194h2c0 1-1 2-1 3l-1 1h1 2c2 0 3 2 5 2 0-1 0-1 2-1l-3-1c-1 0-1 0-2-1v-1-3-2-1l1 1v6c2 1 3 1 5 1l1 1c-1 1-3 1-4 1-1 1-2 1-3 0-1 0-1-1-2-1h-1c-3 1-5 4-8 5l-1-1c-1-1-2-1-3-1h-1c1-2 5-2 7-3s4-2 5-4l-1-1z" class="I"></path><path d="M367 189l1 2h3c2 0 4-1 5 0s1 1 1 2c-1 0-1-1-2-1s-4 2-5 2l-3 2h-5l-4 4v-2c2-2 5-7 7-8l2-1z" class="E"></path><path d="M371 191c2 0 4-1 5 0s1 1 1 2c-1 0-1-1-2-1s-4 2-5 2l-3 2h-5c3-2 7-3 9-5z" class="T"></path><path d="M476 176c2-1 4-1 6-1l5 1 9 2c2 1 6 2 8 4-3-1-7-2-10-3h-10c-4 0-6-1-9 2l-1-1c-1 1-1 1-1 0v-1-1c1-1 2-2 3-2z" class="V"></path><path d="M476 176c2-1 4-1 6-1l5 1h-3c-1 1-1 1-2 1-3 0-5 1-7 2 0-2 1-2 1-3z" class="R"></path><path d="M496 178l5-1c2 1 6 2 7 3 7 2 13 4 20 4l2-2 8 5c-1 1-1 2-1 3 2 2 5 4 6 6v1h0c-2-2-3-3-5-4-4 0-8-2-11-3-2-1-4-1-5-2h-1l-1 1c-3-2-5-4-8-5h-1-1c-1 0-2-1-3-1s-2 0-3-1c-2-2-6-3-8-4z" class="J"></path><path d="M496 178l5-1c2 1 6 2 7 3h0c3 2 7 3 10 5 2 0 4 0 6 2h0 1v1c1 0 1 1 2 2-2-1-4-1-5-2h-1l-1 1c-3-2-5-4-8-5h-1-1c-1 0-2-1-3-1s-2 0-3-1c-2-2-6-3-8-4z" class="H"></path><path d="M475 181c3-3 5-2 9-2h10c3 1 7 2 10 3 1 1 2 1 3 1s2 1 3 1v2h-1c-1-1-2 0-3 0l-4 3c-1 1-2 1-2 2-1 1-2 2-3 2-3 0-6 0-9-1 1 1 1 2 2 3-4-2-8-3-11-6-2-1-3-3-4-4-1-2-1-3 0-4z" class="I"></path><path d="M493 185c2 0 4-2 6-1l1 1 1 1c-1 1-2 2-3 2h-1c-1-1-2-2-4-3z" class="C"></path><path d="M497 188c-1 1-2 2-4 3-1 0-3-2-4-3v-1c2-1 3-1 4-2 2 1 3 2 4 3z"></path><path d="M475 181c3-3 5-2 9-2h10c-3 1-7 0-10 1l-1 1 6 3c-2 1-4 3-6 4l-1 1c1 2 4 2 6 3 1 1 1 2 2 3-4-2-8-3-11-6-2-1-3-3-4-4-1-2-1-3 0-4z" class="B"></path><path d="M483 188h-2c-2-1-3-2-4-4 2-2 3-2 6-3l6 3c-2 1-4 3-6 4z"></path><path d="M510 184h1 1c3 1 5 3 8 5l1-1h1c1 1 3 1 5 2 3 1 7 3 11 3l1 2-1 1h-3c-2 2-1 4-2 7 0 2-2 4-3 6-3-4-8-8-14-8h-15c0-1-4-1-6-1l-14-2h-3c-1-2-2-3-4-4l2-1 1-1-1-1 3-2c3 3 7 4 11 6-1-1-1-2-2-3 3 1 6 1 9 1 1 0 2-1 3-2 0-1 1-1 2-2l4-3c1 0 2-1 3 0h1v-2z" class="F"></path><path d="M527 194c1 0 1 0 2 1 0 2 0 2-1 3-1 0-2 0-3-1v-1l2-2z"></path><path d="M479 189c3 3 7 4 11 6h3v1c-6 0-11-2-16-4l-1-1 3-2z" class="L"></path><path d="M510 184h1 1c3 1 5 3 8 5 1 2 1 2 0 4s-3 4-5 4c-6 2-16 1-22-1v-1h-3c-1-1-1-2-2-3 3 1 6 1 9 1 1 0 2-1 3-2 0-1 1-1 2-2l4-3c1 0 2-1 3 0h1v-2z"></path><path d="M510 184h1 1c1 2 2 3 3 5 0 2-1 2-2 3-3 1-7 2-9 2l-1-1h-2l1 1c-2 2-4 1-7 1h-2-3c-1-1-1-2-2-3 3 1 6 1 9 1 1 0 2-1 3-2 0-1 1-1 2-2l4-3c1 0 2-1 3 0h1v-2z" class="C"></path><path d="M506 186l3 3c-1 2-2 2-3 3l-4-3 4-3z"></path><defs><linearGradient id="AU" x1="680.128" y1="386.589" x2="717.97" y2="384.212" xlink:href="#B"><stop offset="0" stop-color="#bfbcbc"></stop><stop offset="1" stop-color="#e0dede"></stop></linearGradient></defs><path fill="url(#AU)" d="M665 278v1c3 2 6 7 9 8l2-1c8 14 16 28 22 43 5 12 9 26 12 38 6 23 12 47 12 71v5c1 8 2 16 1 23 0 3 0 5-2 7 0 1-1 1-2 1l-1-1h1c2-2 2-5 3-8l-1-1c-1-1-1 2-3 2 0 0 1 0 1-1 1-1 0-2 0-4l-2 5-3 3c-1 1-1 2-2 3l-1 2h0c-1 1-1 2-1 3-1 4-4 9-7 13 0-1 0-3 1-5l-2-2-2 2v-34c-1-2-1-5-1-6 0-6-2-15-1-21 1-3 4-4 7-5l-4-1 2-1c-2-1-2-3-3-5-1-1-2-4-2-6l-1-3c-4-12-7-25-8-38-1 1-1 1-2 1-2-1-3-2-4-2h-1c0-3 4-3 5-5l-2-2h0c1 0 2-1 3-1 2-1 3-3 4-5 1-3 0-8-2-11-1-1-2-2-4-2-4 1-5 2-8 5l-1-1c1-3 2-4 4-6h1l-1-1-2 1h-1-3l1-1c-2 0-3 0-4-1l1-1c0-2-1-3-2-4-5-6-8-13-12-20l-2-3h1l2 3 1-1c0-1 0-1-1-2s-1-2-1-4l-2-3c1-2 1-3 2-4h5c1 1 2 1 2 3l1 1v2c1 0 1-1 2-1h1l1-4c0-1 1-2 2-3-1-1-2-2-2-4-2-3-5-7-6-11z"></path><path d="M719 447c1-4 0-7 1-11 0 1 0 2 1 3l1-1v5 6l-1 1h0c0-2-1-2-2-3z" class="W"></path><path d="M708 380v-2l1 1c4 8 5 16 7 24 0 4 1 8 1 12-2-7-4-15-6-23-1-3-1-9-3-12zm-35-87c5 7 10 14 14 22 1 2 2 5 3 7-3-3-4-7-6-10-1-2-4-6-5-7-2-1-5 0-7 0h0c-1-2-1-6-1-9 0-1 1-2 2-3z"></path><path d="M718 452c2 3 0 8-2 11l-2 6c-1 1-1 2-2 3l-1 2h0c-1 1-1 2-1 3-1 4-4 9-7 13 0-1 0-3 1-5 4-11 10-21 14-33z" class="L"></path><path d="M722 443c1 8 2 16 1 23 0 3 0 5-2 7 0 1-1 1-2 1l-1-1h1c2-2 2-5 3-8l-1-1c-1-1-1 2-3 2 0 0 1 0 1-1 1-1 0-2 0-4l-2 5-3 3 2-6c2-3 4-8 2-11 0-1 1-4 1-5 1 1 2 1 2 3h0l1-1v-6z" class="R"></path><path d="M719 461c1-1 1-3 1-5 1 1 2 1 2 2v7l-1-1c-1-1-1 2-3 2 0 0 1 0 1-1 1-1 0-2 0-4z" class="Y"></path><path d="M672 305c2 0 5-1 7 0 1 1 4 5 5 7 2 3 3 7 6 10 0 1 0 1 1 2 4 5 6 9 8 15 0 1-1 2-1 3-6-11-14-20-22-28-1-1-1-2-2-4l-3-3h1v-2z" class="F"></path><path d="M672 305c2 0 5-1 7 0 1 1 4 5 5 7h0v2l-2-1v2c-1-1-1-2-2-3h-2v-3h-2c-1-1-2-2-4-2v-2z" class="Q"></path><path d="M657 299c1-2 1-3 2-4h5c1 1 2 1 2 3l1 1v2c1 0 1-1 2-1h1l1-4c0 3 0 7 1 9h0v2h-1l3 3c1 2 1 3 2 4 8 8 16 17 22 28l6 18c1 3 2 5 2 8-2-3-4-6-6-10-1-3-2-8-3-11-1-5-3-10-7-13-4-6-10-10-15-15-3-3-6-6-8-9-3-3-5-6-8-8l-2-3z"></path><path d="M671 296c0 3 0 7 1 9h0v2h-1l3 3c1 2 1 3 2 4-4-3-7-8-9-13 1 0 1-1 2-1h1l1-4z" class="G"></path><defs><linearGradient id="AV" x1="674.091" y1="362.097" x2="704.124" y2="355.642" xlink:href="#B"><stop offset="0" stop-color="#c6c4c4"></stop><stop offset="1" stop-color="#f2f1f1"></stop></linearGradient></defs><path fill="url(#AV)" d="M659 302c3 2 5 5 8 8 2 3 5 6 8 9 5 5 11 9 15 15 4 3 6 8 7 13 1 3 2 8 3 11 2 4 4 7 6 10 1 3 2 6 4 9l-1 2-1-1v2 1 2h0v2h0c1 1 0 1 1 2v1c1 1 1 2 1 3v1c0 2 1 3 1 5 0 0 1 0 0 2 1 0 1 0 1 1s1 5 2 7c0 2 1 4 1 5l-3-8c-1-3-2-7-2-10l-3-9v-1l-1-1h0c-2-4-3-6-6-9l-1-2c0-1 0-3-1-4 0-2-2-3-3-4-2 0-4 0-6 1-1 1-1 1-2 1-2-1-3-2-4-2h-1c0-3 4-3 5-5l-2-2h0c1 0 2-1 3-1 2-1 3-3 4-5 1-3 0-8-2-11-1-1-2-2-4-2-4 1-5 2-8 5l-1-1c1-3 2-4 4-6h1l-1-1-2 1h-1-3l1-1c-2 0-3 0-4-1l1-1c0-2-1-3-2-4-5-6-8-13-12-20l-2-3h1l2 3 1-1c0-1 0-1-1-2s-1-2-1-4z"></path><path d="M659 309l-2-3h1l2 3c4 5 9 10 14 14 4 3 8 6 11 9l-9 3c-2 0-3 0-4-1l1-1c0-2-1-3-2-4-5-6-8-13-12-20zm30 56c2-1 4-1 6-1 1 1 3 2 3 4 1 1 1 3 1 4l1 2c3 3 4 5 6 9l5 21c1 4 4 8 5 13 1 4 1 8 1 12 0 10-1 19-5 29-2 8-7 15-10 23v2l-2 2v-34c-1-2-1-5-1-6 0-6-2-15-1-21 1-3 4-4 7-5l-4-1 2-1c-2-1-2-3-3-5-1-1-2-4-2-6l-1-3c-4-12-7-25-8-38z"></path><path d="M697 403l5 5-1 1c-1 1-1 2-1 3-1-1-2-4-2-6l-1-3z" class="R"></path><path d="M700 374c-2-1-3-1-5-1-1 0-2 1-3 1l-1-1v-4c1 0 6 2 7 3h1l1 2z" class="N"></path><path d="M708 407c3 3 4 6 5 10-1-1-2-1-3-2-1 0-2-1-3-1 1-1 1-1 1-2l-4-3c2-1 3-1 4-2z" class="F"></path><path d="M703 417l4-3c1 0 2 1 3 1 1 1 2 1 3 2 3 4 3 8 2 13h0l-2-2c-2-1-4-1-7-2 0-1 1-3 2-4h1c1-1 1-1 1-2l-1-1h-4l-4-1 2-1z" class="D"></path><path d="M704 409c-1-1-1-1-1-2-1-10-10-21-8-30v-2h0l1-1c1 0 1 1 2 2s1 5 2 7c2 7 3 17 8 23v1c-1 1-2 1-4 2z" class="E"></path><path d="M705 419h4l1 1c0 1 0 1-1 2h-1c-1 1-2 3-2 4 3 1 5 1 7 2l-1 1h1c1 3 0 8-1 10 0 6-2 13-4 19-3 8-8 14-6 23v2l-2 2v-34c-1-2-1-5-1-6 0-6-2-15-1-21 1-3 4-4 7-5z" class="B"></path><path d="M701 451l1-1c2 0 3 1 5 1v1c-1 2-2 3-3 4 0 0-1 0-1-1-2-1-2-2-2-4z"></path><path d="M705 426h1c3 1 5 1 7 2l-1 1h1c1 3 0 8-1 10l-1-1v-6c0-2-1-2-2-3-1 0-2 0-3-1h0c-1-1-1-1-1-2z" class="C"></path><defs><linearGradient id="AW" x1="698.04" y1="447.503" x2="708.457" y2="421.807" xlink:href="#B"><stop offset="0" stop-color="#bdbbbb"></stop><stop offset="1" stop-color="#e9e7e5"></stop></linearGradient></defs><path fill="url(#AW)" d="M705 419h4l1 1c0 1 0 1-1 2h-1c-1 1-2 3-2 4h-1c0 1 0 1 1 2 0 3-1 3-2 5 1 1 1 1 2 3 0 1-1 2-2 3l2 2h-1c2 2 2 3 2 5l-1 1c1 2 1 3 1 4-2 0-3-1-5-1l-1 1h-1c-1-2-1-5-1-6 0-6-2-15-1-21 1-3 4-4 7-5z"></path><path d="M705 441c2 2 2 3 2 5l-1 1h-2c-2-2-2-2-2-4 1-1 2-1 3-2z"></path><path d="M701 430v-1c1-1 2-3 3-3h1c0 1 0 1 1 2 0 3-1 3-2 5l-3-3z" class="H"></path><path d="M701 430l3 3c1 1 1 1 2 3 0 1-1 2-2 3l-1-1c-2-2-2-4-2-8z"></path><path d="M294 284h335l-1 121h-14l-2-19c-3-20-8-40-20-56l-95-1v77 166 86c0 17 0 34 1 51 0 8 2 15 5 23 5 11 14 18 26 22 10 3 21 3 31 3v12H351v-13c12 0 24 1 35-3 11-3 20-12 25-22 4-8 5-17 6-25v-42-59l1-170v-77-21c0-2 0-5-1-8h-65-15c-1 0-4 0-6 1-6 10-12 20-17 30-7 15-12 30-17 45h-24c11-30 18-61 20-94l1-27z" class="D"></path><path d="M442 562c2 2 1 3 2 6v1h-2v2-9z" class="Y"></path><path d="M482 735h2l1 1c0 1 0 1-1 3h-2c-1-1-1-1-1-2l1-2zm0-421c1 0 2 0 2 1v1l-1 1c-2 1-2 1-3 0 1-2 1-2 2-3zm-1 160h2l1 1c0 2 0 3-1 4-1 0-1 0-2-1 0-1-1-2 0-4zm1 239c1-1 1-1 2 0s0 2 0 4h-2c-1-2-1-3 0-4z"></path><path d="M458 563l1 1c0 1 0 1 1 2 1 0 2 1 3 2h-5-1 0v-1-1c-1-1 0-1 0-3h1z" class="H"></path><path d="M395 757l6-1c2 1 3 1 4 2h1c-3 0-9 1-11-1z" class="C"></path><path d="M481 500h2 0c1 2 1 3 0 5-1 0-1 0-2-1v-4zm1 200h2v1c1 2 1 3 0 5-1 0-1 0-2-1v-5zm-1-374c1 0 2-1 3 0 0 2 0 3-1 4h-1c-1-1-2-1-2-3l1-1zm2 361h0c1 0 1 1 1 2s0 2-1 4c-1 0-1-1-2-1v-2c0-1 1-2 2-3zm-1-27h2v2c0 2 0 3-1 4-1 0-1-1-2-2 0-2 0-2 1-4z"></path><path d="M482 636h2v5h-1c-1 0-1 0-1-1-1 0-1-2-1-3l1-1zm1-216l1 1c0 2 0 3-1 5h-1l-1-1v-4l2-1zm-2-68h2 1c0 2 0 4-1 5h-2v-5zm1 186h1l1 1c0 1 0 3-1 5l-2-1h0c0-2 0-4 1-5zm0 85h1 1c0 2 0 4-1 6-1-1-1 0-2-2 0-2 0-3 1-4z"></path><path d="M483 581h0c1 2 1 4 1 6l-1 1-2-1v-2c0-2 1-3 2-4zm-1 144h2l1 1c0 2 0 2-1 3-2 0-2 1-3 0 0-2 0-3 1-4zm0-116h1l1 1c0 1 0 4-1 6l-1-1h-1c0-3 0-4 1-6zm0-230h1c0 1 1 1 1 2 0 2 0 3-1 4h-1c-1-1-1 0-1-1-1-2 0-4 1-5zm-1 108h2s1 0 1 1 0 4-1 5c-1 0-1 0-2-1 0-2-1-3 0-5z"></path><path d="M482 433h1l1 1c0 2 0 4-1 6-1 0-1 0-2-1 0-2 0-4 1-6zm-1 79c1-1 1-1 2-1l1 1c0 2 0 4-1 6-1 0-1 0-2-1v-5zm1 136h2v5l-1 1-2-1c-1-1 0-3 0-4l1-1z"></path><path d="M481 460h2c1 2 1 4 1 6l-1 1c-1 0-2-1-2-1v-6zm1 212h1c1 2 1 5 1 7h-1-2v-3c0-2 0-2 1-4zm0-308h1c1 2 1 4 1 7l-1 1-2-1h0c0-3 0-5 1-7z"></path><path d="M482 551h1c1 2 1 3 1 5 0 1 0 1-1 2-1 0-1-1-2-1v-2c0-2 0-3 1-4zm0-27h1c1 2 1 5 1 7h-1-2v-3c0-2 0-2 1-4zm0 71h1l1 1c0 2 0 4-1 6-1 0-1 0-2-1 0-2 0-4 1-6zm0-149h1c0 1 0 1 1 2 0 2 0 4-1 6-1-1-1-1-2-1v-1c-1-1 0-4 1-6zm0-54h1c1 2 1 4 1 5s0 1-1 2h-2v-2c0-2 0-4 1-5zm0-55s1 0 1 1c1 2 1 4 1 6h-2-1c0-3 0-5 1-7z"></path><path d="M482 406h1c1 2 1 4 1 6l-1 1c-1 0-1 0-2-1 0-2 0-4 1-6z"></path><path d="M463 672c2 3 2 5 1 8v4c0-1 0-2-1-4-2-1-1-4-2-6h0 1l1-2z" class="H"></path><path d="M483 565h0c1 3 1 6 1 8l-2 1-1-1v-2c0-2 0-5 2-6z"></path><path d="M444 746v2c1 1 1 1 1 2-2 1-6 2-9 3l-2-1 7-3s1 0 2-1l1-2z" class="T"></path><path d="M464 698h2c-2 2-4 5-5 8 0 1 1 1 2 2l-2 2-1-1h-2v-2c2-3 4-5 6-9z" class="M"></path><path d="M423 741l2 2-1 2c-2 2-3 4-6 5v1h-2v-1c1 0 1-1 2-2h0c0-1 1-2 2-2 2-2 2-3 3-5z" class="J"></path><path d="M454 660l-1-1v-1l1-1h1c2 1 4 1 6 0 1 1 2 2 3 5 0 1 0 2-1 3 0-1-1-3-2-4l-2-1c-2-1-3 0-5 0z" class="M"></path><path d="M471 753c7-1 16-2 24-1-2 0-3 0-4 1h-1-1c-3 1-6 0-8 1-3 0-8 0-10-1z" class="E"></path><path d="M455 591l3 4h0l2 4c1 2 4 8 3 10-2-6-5-11-9-16 1-1 1-1 1-2z" class="W"></path><path d="M446 660l4-4c-1 1-2 3-3 4h1c-1 2-1 2-1 4 1 2 2 4 3 5-1 0-1 0-2 1-2-2-3-3-4-5 0-2 0-3 2-5z" class="E"></path><path d="M444 531h0l1 1h5 1l-6 8-1-1v-8z" class="X"></path><path d="M611 350c1-1 1-2 2-2l2 9h0l1-1h0l1 12c-2-3-2-7-3-10s-2-5-3-8z" class="B"></path><path d="M424 745l3 3v3h-12 1 2v-1c3-1 4-3 6-5z" class="P"></path><path d="M452 651l1 3-3 2-4 4-2-4-1-2h5l2-2 2-1z" class="X"></path><path d="M443 654h5v1c-1 1-2 1-4 1l-1-2z" class="U"></path><path d="M454 660c2 0 3-1 5 0 0 1 0 2-2 3 0 1-1 2-1 3h-2c-1 0-1-1-2-1 0-3 0-3 2-5z" class="L"></path><path d="M459 660l2 1c1 1 2 3 2 4v1l-1 1c-2 0-7 0-8-1h2c0-1 1-2 1-3 2-1 2-2 2-3z" class="Y"></path><path d="M463 666c-1 0-2 1-3 0 0-2 1-4 1-5 1 1 2 3 2 4v1z" class="L"></path><path d="M443 680c1-4 3-6 7-8h-1c0 3-1 4-2 6l-1 1c1 2 1 3 2 4 0 2 2 3 2 4-2-1-4-2-5-4l-2-3z" class="G"></path><path d="M458 612c3 2 6 8 7 11l-1 1v-1c0-1-1-2-1-2v-1c-1 1-1 1-1 2 1 4 1 10 0 14 0-6-2-12-3-18v-2l-1-4z" class="O"></path><path d="M441 374c1-5 4-10 6-14 0 2-1 3 0 5 0 3-2 9-3 12v-5h1l-1-1v1c-1 0-1 1-1 1v1 1l-2 4v1-2c-1 1-1 2-1 3h0c0-3 0-5 1-7z" class="I"></path><path d="M451 584h1c1 1 1 1 3 2 0 0 1 1 1 2 2 1 2 0 3 2h0c1 1 1 2 1 3h0c-1-1-1-1-3-2 0 1 0 1 1 1v2 1h0l-3-4c0 1 0 1-1 2l-3-4v-4h0v-1z" class="F"></path><path d="M451 585l4 6c0 1 0 1-1 2l-3-4v-4zm2 91l1-1c2 0 3 1 4 3 0 0 1 1 1 2l-3 3c-1 0-2 0-3-1-2 0-2-2-2-3 0-2 0-2 2-3z" class="R"></path><path d="M442 571v-2h2v1c0 6 2 10 7 15h0v4c-3-3-7-7-8-11-1-2-1-5-1-7z" class="V"></path><path d="M432 305c1 4 1 10 0 14l-2 2c0 1-1 2-2 3h-1c1-3 2-3 3-5v-1c-2-1-3-2-4-3 1-2 3-4 4-5l1-1h1v-4z" class="P"></path><path d="M426 315h1l4-4h1v3l-1 1c-1 1-1 1-1 3h0c-2-1-3-2-4-3z" class="H"></path><path d="M444 570c1 2 2 3 2 5l1 1h2s1 2 2 2c1 2 2 1 4 2h2c1 0 1-1 3 0l-1 2-3 1-4 1h-1v1c-5-5-7-9-7-15z" class="S"></path><path d="M451 584l-1-2h1c2 0 4 0 5 1l-4 1h-1z" class="U"></path><defs><linearGradient id="AX" x1="443.714" y1="561.699" x2="446.699" y2="543.451" xlink:href="#B"><stop offset="0" stop-color="#6a6968"></stop><stop offset="1" stop-color="#8b8a89"></stop></linearGradient></defs><path fill="url(#AX)" d="M448 541c0 1 0 2 1 3v-1l1 1c-2 4-4 8-5 13v1l-1 10c-1-3 0-4-2-6-1-2 0-7 1-10h0c1-4 3-8 5-11z"></path><path d="M472 609c1-4 1-9 1-13v1l1 26h0v-16c0-4 0-7 1-10l-1 65c-1-2 0-6 0-9v-18c-1-8-2-17-2-26h0z"></path><path d="M444 344c3-5 7-10 11-15-1 1-1 2-1 4v1c-1 1-2 3-3 4-2 2-4 5-5 8-1 1-3 3-4 5s-1 6-1 9v1c1-1 1-2 1-3 1 0 1-1 1-2l1-1h0 0c1-1 1-2 1-3l1-1h0c1-2 2-2 3-3-4 6-8 13-9 21h-1c0-4 0-7 1-10 0-5 2-10 4-15zm15 238h0c1 3 4 4 6 7h0v-4-3c-1-1 0-2-1-4h0l1-1c1 2 1 3 2 5 0 2 0 6-1 7 0 1-1 2-2 2h-2v2c1 1 1 3 2 4l-1 1c-1-2-2-3-3-5 0-1 0-2-1-3h0c-1-2-1-1-3-2 0-1-1-2-1-2-2-1-2-1-3-2l4-1 3-1z" class="Q"></path><defs><linearGradient id="AY" x1="418.518" y1="750.385" x2="415.437" y2="760.257" xlink:href="#B"><stop offset="0" stop-color="#8b8c8c"></stop><stop offset="1" stop-color="#beb8b8"></stop></linearGradient></defs><path fill="url(#AY)" d="M434 752l2 1c-9 3-21 6-30 5h-1c-1-1-2-1-4-2 11-2 22-1 33-4z"></path><path d="M473 446c1-1 1-2 1-3v58-5l-1 4v-54z" class="V"></path><path d="M437 557c1-4 2-7 4-9-1 3-2 7-2 11v12 8c-1 2-1 8 0 11h-2 0c-1-10-1-20 0-30v-3z" class="K"></path><path d="M427 324h1c1-1 2-2 2-3l2-2c1 4 1 8 1 12-1 5 0 9-1 14-1 4 0 8-1 12l-1-3h-1l-1-1v-1l2-1v-5h-1-1v-2c1 0 1-1 2-1l-1-5c0-1 1-2 1-4l-2-1c0-2 0-3 2-4v-3l1-1-1-1c-1 0-1 1-2 1l-1-1z" class="N"></path><path d="M437 749l1-1 1 1h1 1l-7 3c-11 3-22 2-33 4l-6 1c-2 1-3 1-5 1l25-7h12c2 0 9-1 10-2z"></path><path d="M431 681c-1-1-2-1-2-3s-1-3-1-5v-2c-1-2 1-3 1-5l-1-1v-1c1-2 2-3 2-5v-1c-1 1-1 1-2 1l-1-1c1-2 1-2 3-3v-3c-1 0-1 0-2-1v-3c1-1 1-1 2-1 0-1 0-2-1-2-1-2-1-3-1-5 1-2 1-2 3-2 1 2 0 5 0 7v2 4 4 3c-1 3 0 5 0 7v5c1 4 1 7 0 10v1z" class="B"></path><path d="M464 624l1-1c2 6 4 11 5 16 1 6 1 13 1 19-1-2-3-3-5-5l-2-2c-1-3 2-8 2-11 1-5 0-11-2-16z"></path><path d="M448 312l1 1c2 0 6 1 8 0 2 0 5-2 7-3v4c-1 1-1 3-1 4-1 2-1 4-1 6-1 2-1 3-2 4v-2c0-1 0-2-1-3v-1c1-3 0-4-2-6l-1 1c-2 0-2 0-3 1v4h-1c0 1 0 2-1 2v-4c-1-1-3-1-4-1l-1-1c0-1 0-2 1-3v-2l1-1z" class="G"></path><path d="M447 315h0 5 1v1 2 4h-1c0 1 0 2-1 2v-4c-1-1-3-1-4-1l-1-1c0-1 0-2 1-3z" class="E"></path><path d="M447 315h0c2 1 3 2 4 3v2c-1-1-3-1-4-1l-1-1c0-1 0-2 1-3z" class="R"></path><path d="M433 331v20 31l-1 2-1 1v-3h0l-3 2h0c0-2 1-5 3-7v-1c-1-1-1-1-2-1-2-4 2-3 2-6-1-1-2-1-2-2v-1l1-1h1v-5c-1 1-1 2-2 2h-1c0-2 0-2 1-3h1l1-2c1-4 0-8 1-12 1-5 0-9 1-14z" class="K"></path><path d="M456 317l1-1c2 2 3 3 2 6v1l-4 6c-4 5-8 10-11 15-1-1 0-4 0-5-1-1-1-1-1-2h1v-1c0-1 1-2 2-3l2-2c2-2 3-3 2-6l1-1c1 0 1-1 1-2h1v-4c1-1 1-1 3-1z" class="U"></path><path d="M456 317c2 2 2 2 2 5 0 2-3 5-4 6-2 2-4 3-6 3 2-2 3-3 2-6l1-1c1 0 1-1 1-2h1v-4c1-1 1-1 3-1z" class="F"></path><path d="M471 753h-5v-1c6-3 12-5 18-8 4 2 10 5 13 8h-2c-8-1-17 0-24 1z"></path><path d="M447 558l-1 2h1 2c2-1 3-3 4-4 0 1 0 2 1 4l1-1 1 3h1v1c0 2-1 2 0 3-3 2-4 2-7 3h-2l-1-1-1-1v3l1 1c1 2 1 2 2 3v2h-2l-1-1c0-2-1-3-2-5v-1-1l1-10v-1h2v1z" class="G"></path><path d="M447 568v-7h1c1 2 2 3 2 5 0 1-1 2-2 3l-1-1z" class="D"></path><path d="M444 568l1-10c1 3 1 6 1 9v3l1 1c1 2 1 2 2 3v2h-2l-1-1c0-2-1-3-2-5v-1-1z" class="N"></path><path d="M454 560l1-1 1 3h1v1c0 2-1 2 0 3-3 2-4 2-7 3h-2c1-1 2-2 2-3h1 1c-1-1-1-2-1-3 1 0 2 0 3-1v-2z" class="Q"></path><path d="M449 348l12-17c-3 11-9 20-14 29-2 4-5 9-6 14-1-2-1-4-1-5 1-8 5-15 9-21z"></path><path d="M473 356v1c0 3 0 5 1 7v13c0 1-1 2 0 4v-7-4c1-1 0-1 0-2s0-2 1-3c0-3-1-5 0-8l-1 86c0 1 0 2-1 3v-12c1-7 0-14 0-21v-57z" class="T"></path><path d="M457 566v1 1h0l3 2c1 1 2 3 3 4-1 2-2 5-3 6-2-1-2 0-3 0h-2c-2-1-3 0-4-2-1 0-2-2-2-2v-2c-1-1-1-1-2-3l-1-1v-3l1 1 1 1h2c3-1 4-1 7-3z" class="O"></path><path d="M447 571l2-1h1c0 2 0 1 2 2l-3 2c-1-1-1-1-2-3z" class="S"></path><path d="M459 572l2 2c0 2 0 3-2 4 0 0-1 0-2 1v-1c1-1 1-2 2-2v-4z" class="J"></path><path d="M452 572l1-1c2 0 4 0 6 1v4c-1 0-1 1-2 2v1l-2 1c-2-1-3 0-4-2-1 0-2-2-2-2v-2l3-2z" class="F"></path><path d="M451 578h6v1l-2 1c-2-1-3 0-4-2z" class="M"></path><path d="M427 698h3l-1 1c0 1 0 1 1 2 1 2 2 2 2 4l-1 2h1 2 0c1 2 1 3 1 5v3c0 1 1 2 1 3l-2 2v1c-1 1-1 3-1 4h-3-1v1 2c-1 0 0 0-1-1l-2-1 1-28z" class="J"></path><path d="M434 707c1 2 1 3 1 5v3c0 1 1 2 1 3l-2 2c-1 0-2 1-3 0v-3c0-1 0 0-1-1 0-1 0-1-1-2 0-3 0-4 2-6h1 2v-1z" class="B"></path><path d="M435 712v3c0 1 1 2 1 3l-2 2c-1 0-2 1-3 0v-3l1-1c1-1 1-1 2-1v-2h1v-1z" class="R"></path><path d="M431 504h0v4c1 0 1 1 1 2l1 1v46-1c-1-2-1-4-1-7h-1v-3h-1v2l1 2c0 1 1 3 0 4v-3-1c-1-1-2-1-2-2l-1-2v-4-3-1l1 2h1c0-1 0-2-1-3v-1h1v-2c-2-3 0-6-2-9l-1-1 2-2c1-1 1-3 1-4h0l-1 1-1-1c0-2 1-3 2-4v-3c0-1 0-1-1 0-1 0 0 0-1-1 0-1 1-2 2-3s1-2 1-3z" class="S"></path><path d="M428 542l3 2v1l-3 1v-4z" class="N"></path><defs><linearGradient id="AZ" x1="451.679" y1="631.756" x2="465.211" y2="638.442" xlink:href="#B"><stop offset="0" stop-color="#565352"></stop><stop offset="1" stop-color="#838384"></stop></linearGradient></defs><path fill="url(#AZ)" d="M440 606c4 7 8 12 16 14h1 1l1-2c1 6 3 12 3 18l-1 15c-2 1-3 1-5 1l-3 2-1-3 1-1 1-2 2-3c4-5 2-10 2-16 0-1 0-3-1-4-1-3-3-4-6-6h-1c-1-1-3-2-4-3-3-3-5-6-6-9v-1z"></path><path d="M452 651l1-1c1 1 2 1 3 2l-3 2-1-3z" class="R"></path><path d="M607 315l5 25c1 2 2 6 1 8-1 0-1 1-2 2-2-3-2-7-4-10-1-1-1-2-1-3l-1-1h0c0-2-1-6-2-7s-2-2-2-4l2-2h1c0-2 2-3 2-4s0-2 1-3v-1z" class="N"></path><path d="M603 329l1-1h0c0-1 0-2 1-2v-3l1-1v1l3 12-1 1c-1 0-1 0-2 1l-1-1h0c0-2-1-6-2-7z" class="B"></path><path d="M470 379c0-8 1-16 2-23h1v57c0 7 1 14 0 21v-9-13l-4-2c0-3-1-6-1-9v-1c2-4 3-8 3-13 0-2-1-5-1-8z" class="K"></path><path d="M431 550v1 3c1-1 0-3 0-4l-1-2v-2h1v3h1c0 3 0 5 1 7v1 33l-2 3h-2c-1-1-1-2-1-3v-1-2c0-1 1-1 2-1-2-4 0-7-2-10v-1l1-1c1-1 1-2 0-4-1-3-1-5 0-8v-5l-1-4h1v-5c0 1 1 1 2 2z" class="P"></path><path d="M429 548c0 1 1 1 2 2l-1 5c-1 0-1-1-2-2h1v-5z" class="G"></path><defs><linearGradient id="Aa" x1="436.078" y1="479.47" x2="426.578" y2="475.957" xlink:href="#B"><stop offset="0" stop-color="#8c8c85"></stop><stop offset="1" stop-color="#a5a2a5"></stop></linearGradient></defs><path fill="url(#Aa)" d="M428 443l1 1s1-1 2 0h0v5c1 2 1 2 1 4-1 1-1 7-1 9 1 2 2 4 2 7v4 22c-1 2-1 3-2 4 0-1 0-4-1-5-1 1-1 1-2 1-1-2 2-3 2-5v-4l-1-1-1-1 1-2c1-2 0-5-1-7l2-2c0-2-1-3-2-5v-2-1-2-6-1-3c0-3-1-6 0-10z"></path><path d="M428 457l2 3c0 1-1 2-2 3v-6z" class="F"></path><path d="M428 443l1 1s1-1 2 0h0v5c-1 0-1 1-2 0-1 1-1 1-1 2l3 3c-1 1-2 1-3 2v-3c0-3-1-6 0-10z" class="E"></path><path d="M458 700c0 1-1 2-1 4-1 1-1 1-1 2 1 0 1 0 2-1l4-6 4-6v-1c0-1 1-1 1-2s1-2 1-4h1c0-2 0-3 1-5v1c1 0 1 0 1 1l-1 1c0 2 0 3-1 5 1 3 1 5-1 7 0 1-1 1-2 2h-2c-2 4-4 6-6 9-3 3-5 6-6 10-1 2-2 3-2 5 0 1 0 2-1 3 0 3-1 7-2 11-1 2 0 5 0 8 1 1 1 2 1 2l-4 2v-2c-1-7 0-12 2-19 0-2 1-4 2-5 0-2 0-3 1-4 1-2 2-5 3-7 1-4 3-8 6-11z" class="B"></path><path d="M469 689c1 3 1 5-1 7 0 1-1 1-2 2h-2l1-1c2-3 3-6 4-8z" class="I"></path><path d="M473 500l1-4v5l1 96c-1 3-1 6-1 10v16h0l-1-26v-1c0 4 0 9-1 13-1-5 0-12 0-18l1-59v-6-10-16z" class="L"></path><path d="M317 301c3-2 31-1 37-1h82c1 0 2 1 4 1h20c0 3-4 7-6 9 0 0-2 1-4 1h-1-3c-1 1 0 1-1 1l-1-2c-2 0-2 0-3 1-2 0-2 0-3-1l-1-7c0-2 1-1 0-2H317z" class="B"></path><path d="M457 354h3l-4 8s-1 1-1 2c1 4 0 7 0 11v2h1l1 4-3 1-6 3c0 2 1 3 2 5h2 1v1l-2 1c-2-2-3-4-4-6-1-1-1-1-3-1-1-2-1-4-1-7v-3-1-1s0-1 1-1v-1l1 1h-1v5 1l1 1v-1-1c0-1 0-2 1-3l1-2c2-7 6-13 10-18z" class="U"></path><path d="M443 375v-1-1s0-1 1-1v-1l1 1h-1v5 1l1 1v-1-1c0-1 0-2 1-3l1-2v6 8c-1-1-1-1-3-1-1-2-1-4-1-7v-3z" class="H"></path><path d="M452 382c-1 0-1 0-2-1 0-3 0-8 1-11 2-2 2-4 4-6 1 4 0 7 0 11v2h1l1 4-3 1h-2z" class="P"></path><path d="M452 382c0-1 1-3 2-4 0-1 0-1 1-1h1l1 4-3 1h-2z" class="W"></path><path d="M433 382v87c0-3-1-5-2-7 0-2 0-8 1-9 0-2 0-2-1-4v-5h0c-1-1-2 0-2 0l-1-1 1-1 1-1c1 0 1-4 1-5h0l-1-1v1l-1 1-1-1c0-2 1-2 2-3 1-3 2-9 1-11-1-1-2 0-2-2 0-1 0-1 1-1h0c1-2 1-3 1-4-1 0-1 1-2 2l-1-2c1-1 1-1 3-1v-1c0-2 0-3-1-4s-1-1-1-3h1 0c-1-2-1-3-2-5v-1c-1-2-1-4 0-6v-1-1c1 0 1-1 1-2 0 0 1 0 1-1 1-1 0-3 1-4l1-1 1-2z" class="X"></path><path d="M428 392c1 1 1 2 1 3h2c-1 2-2 3-3 5h0c-1-2-1-4 0-6v-1-1z" class="M"></path><path d="M469 410l4 2v13 9 12 54 16 10c-1-7-1-14-1-21v-1l-1 2c0-1 0-2-1-3v-20-5l-1-14c0-2 1-5 1-7-1-5 0-11 0-16 0-10 0-21-1-31z" class="P"></path><path d="M434 720l2-2c-1 3-1 8 0 10v1 2c1 3 1 5 1 7l-1 1v1c1 2-1 7 1 9-1 1-8 2-10 2v-3l-3-3 1-2-2-2 1-2 1-3v-3l1-2v-5l2 1c1 1 0 1 1 1v-2-1h1 3c0-1 0-3 1-4v-1z" class="K"></path><path d="M429 725h1 3l-1 2 1-1h1 0l1 1c0 1 0 2-1 3 1 1 1 2 2 3h-1c0 1-1 1-1 1l-2 1h-1l-1-1-5-1 1-2v-5l2 1c1 1 0 1 1 1v-2-1z" class="N"></path><path d="M429 725h1 3l-1 2 1-1h1 0c0 1-1 2-2 3-1 0-1 0-2-1l-1-3z" class="V"></path><path d="M426 731c1 0 2 1 4 0l1-1h1c0 2 1 3 0 5h-1l-1-1-5-1 1-2z" class="B"></path><path d="M425 733l5 1 1 1h1l2-1 1 1-1 3 1 1-1 1v2l-1 1h-4c-2 2-2 2-2 5l-3-3 1-2-2-2 1-2 1-3v-3z" class="O"></path><path d="M424 739c1 1 2 1 3 1 0 1 0 2-1 2l-1 1-2-2 1-2z" class="K"></path><path d="M425 733l5 1-1 1-1 1v1c-1-1-1-1-1-2l-2 1h0v-3z" class="N"></path><path d="M434 734l1 1-1 3h-3c-1-2 0-2 0-3h1l2-1zm29-62h1c2 3 2 5 2 8 0 2 0 3-1 5h0c-1 5-5 10-7 15-3 3-5 7-6 11-1 2-2 5-3 7-1 1-1 2-1 4-1 1-2 3-2 5-2 7-3 12-2 19l-1 2c-1 1-2 1-2 1h-1-1l-1-1-1 1c-2-2 0-7-1-9v-1l1-1c0-2 0-4-1-7l1-1h1c1 2 1 5 2 7v-4c1-1 1-4 1-5-1-1-1-2-1-3h2c0-2-1-2-1-4l1-1-1-1 2-2h0c1 0 1 0 2-1l-2-2c2 0 2 1 4 1 0-1 1-1 2-2h0c2-2 2-4 3-6l-1-1 1-1v-2l-2-1c2-2 2-2 2-4 0-1 0-2 1-2 1-2 2-4 3-5v3h1c0 1 0 1 1 2l6-12v-4c1-3 1-5-1-8z" class="L"></path><path d="M442 720l1 1h0c-1 2-1 4-1 6h1l-2 15-1-5v-4c1-1 1-4 1-5-1-1-1-2-1-3h2c0-2-1-2-1-4l1-1z" class="K"></path><path d="M443 714c2 0 2 1 4 1 0-1 1-1 2-2h0c-2 4-4 9-6 14h-1c0-2 0-4 1-6h0l-1-1-1-1 2-2h0c1 0 1 0 2-1l-2-2z" class="C"></path><path d="M452 698c0-1 0-2 1-2 1-2 2-4 3-5v3h1c0 1 0 1 1 2l-6 11-1-1 1-1v-2l-2-1c2-2 2-2 2-4z" class="M"></path><path d="M472 705c1-7 1-15 2-23 0-4-1-8 0-12h0c1 8 1 15 1 24-1 15-2 32-13 44-4 5-10 9-17 12 0-1 0-1-1-2l4-2 1-1c3-1 5-3 7-5l9-12c1-1 2-3 3-5v-3c-1-1-2-2-3-2-4 0-6 3-9 4v-1c-1-1-1-2 0-3s2-3 3-4l1-1 1 1c1-1 2-2 2-3h-2v-1l2-2 2-3v-1c1-1 2-1 3-2l2 3h1 1z"></path><path d="M463 708l2-3v-1c1-1 2-1 3-2l2 3h1 1c-2 7-11 12-16 16-1-1-1-2 0-3s2-3 3-4l1-1 1 1c1-1 2-2 2-3h-2v-1l2-2z" class="Q"></path><path d="M439 579c4 12 13 22 19 33l1 4v2l-1 2h-1-1c-8-2-12-7-16-14 0-2-1-3-1-5v-4c-1-2-1-1-1-2s0-1 1-3v-2c-1-3-1-9 0-11z"></path><path d="M440 606c0-2-1-3-1-5v-4c-1-2-1-1-1-2s0-1 1-3c4 10 9 18 17 25 1-1 1-1 3-1v2l-1 2h-1-1c-8-2-12-7-16-14z" class="D"></path><path d="M459 616v2l-1 2h-1c0-1-1-2-1-3 1-1 1-1 3-1z" class="V"></path><path d="M458 707v2h2l1 1v1h2c0 1-1 2-2 3l-1-1-1 1c-1 1-2 3-3 4s-1 2 0 3v1c3-1 5-4 9-4 1 0 2 1 3 2v3c-1 2-2 4-3 5l-9 12c-2 2-4 4-7 5l-1 1s0-1-1-2c0-3-1-6 0-8 1-4 2-8 2-11 1-1 1-2 1-3 0-2 1-3 2-5 1-4 3-7 6-10z" class="F"></path><path d="M458 709h2l1 1v1c-2 1-2 1-3 1l-1-1 1-2z" class="D"></path><path d="M463 724v-1h5c-1 2-2 4-3 5l-1-3-1-1z" class="C"></path><path d="M451 740v-2c0-3 2-7 4-10 1 0 2-1 3-1-1 2-1 3-2 5-2 2-2 6-5 8z" class="T"></path><path d="M458 707v2l-1 2c-1 2-1 2-2 3-2 1-2 4-3 6 0 1 1 2 1 3v1h0l-2 2v-2l-2 1c1-1 1-2 1-3 0-2 1-3 2-5 1-4 3-7 6-10z" class="E"></path><path d="M451 740c3-2 3-6 5-8 1-2 1-3 2-5 0 2 0 4 1 5l-3 6v2c-2 2-4 4-7 5 1-2 2-3 2-5z" class="I"></path><path d="M463 724l1 1 1 3-9 12v-2l3-6 4-8z" class="B"></path><defs><linearGradient id="Ab" x1="436.705" y1="333.6" x2="444.504" y2="333.908" xlink:href="#B"><stop offset="0" stop-color="#6b6a69"></stop><stop offset="1" stop-color="#868584"></stop></linearGradient></defs><path fill="url(#Ab)" d="M437 303l1 7c1 1 1 1 3 1 1-1 1-1 3-1l1 2c1 0 0 0 1-1h3 1l-2 1-1 1v2c-1 1-1 2-1 3l1 1c1 0 3 0 4 1v4l-1 1c1 3 0 4-2 6l-2 2c-1 1-2 2-2 3v1h-1c0 1 0 1 1 2 0 1-1 4 0 5-2 5-4 10-4 15-2 5-2 8-2 13h-1c-1-9 0-18 0-28v-31-10z"></path><path d="M447 319c1 0 3 0 4 1v4l-1 1c1 3 0 4-2 6l-2 2v-5c0-1 1-1 2-2l-1-1h-1c-1-1-1-2-1-3 0-2 0-2 2-3z" class="D"></path><path d="M433 590v67l1 32c0 4 0 8 1 12l-1 1s-1 0-1-1c-1 1-1 2-1 3h1l1-1h0c0 1-1 3 0 4h-2-1l1-2c0-2-1-2-2-4-1-1-1-1-1-2l1-1h-3c0-2 1-4 1-6 0-3-1-5 0-7 1-1 2-3 3-4v-1c1-3 1-6 0-10v-5c0-2-1-4 0-7v-3-4-4-2c0-2 1-5 0-7-1-1 0-3-1-4l-1 1-2-2c1-1 1-1 1-2 1 0 2-1 3-2v-1c-1 0-2-1-2-2h-1c-1-3 0-5 0-8 0-1-1-2 0-3v-3c-1-2 0-5 0-7-1-1-1 0 0-2 0-2-1-4 0-7l3-3 2-3z" class="X"></path><path d="M431 681v-1c1 3 3 4 1 7h0-1c-1 1-2 1-3 2v-4c1-1 2-3 3-4z" class="K"></path><path d="M428 685v4c1-1 2-1 3-2h1c-1 2-2 3-3 4v1h1 1c1 1 1 3 1 5-1 0-1 1-2 1h-3c0-2 1-4 1-6 0-3-1-5 0-7z" class="C"></path><path d="M428 615l3-1c0 1 0 3-1 4h-1l-1 1 2 2c2 1 1 4 1 7h0c-1 0-2-1-2-2h-1c-1-3 0-5 0-8 0-1-1-2 0-3z" class="E"></path><path d="M428 612c-1-2 0-5 0-7-1-1-1 0 0-2 0-2-1-4 0-7l1 1c0 1 0 1 1 2 1 3 1 9 1 12h-1l-2 1z" class="F"></path><path d="M503 305h64c0 2-1 1 1 3 2 0 7 0 9-1h1l1 1c1 1 4 1 6 1l2-1h0c2 0 4-1 5-2 3-2 10-1 13-1l2 10v1c-1 1-1 2-1 3s-2 2-2 4h-1-1l-1-1v-1h-1l-2-2c-1-1-2-2-2-4h0v-1c0-2-1-1-2-1l-11-1h-45-26c-4 0-28 1-30 0h-1v-3l1-1-1-1h-3l1-1c3-1 6-1 9-1h15z" class="B"></path><path d="M508 306c1 0 2 0 3 1h0l-2 1c-2 0-3 0-4-1 1-1 2-1 3-1zm96 8c-1 3-2 5-4 7l-2-2 3-3 3-2z"></path><path d="M598 314l3 2-3 3c-1-1-2-2-2-4l2-1z" class="E"></path><path d="M598 314c1-2 2-2 5-2h1c0 1 1 1 0 2l-3 2-3-2z" class="H"></path><path d="M479 306c3-1 6-1 9-1h15-4v2c-3 3-10 0-15 1v1l-3-3h-2z" class="O"></path><path d="M463 404c1 0 1-1 1-1h2l2-2c0 3 1 6 1 9 1 10 1 21 1 31 0 5-1 11 0 16 0 2-1 5-1 7l1 14v5 1c-1 2-1 5 0 7h-2l-1 1v2c-1-3-3-4-5-6l-2-2c-1-2-2-3-1-6h-5l-3-3c-1-2-1-4-1-6l-1-2 3-3v-1c-1-2-1-4-1-5h1v-2l-1-3c1-2 0-4 0-6l1-1 1 1c0-2 1-4 3-6h0c1-1 2-1 2-2s0-2 1-2l1 1c3-5 4-9 4-14l1-1c1-7 0-14-2-21z" class="F"></path><path d="M459 457c1-3 2-6 4-8 1 0 3-1 4 0v2h-1c-1-1-2-1-3 0s-2 2-1 4l2-1 2 2c-1 2-1 2-2 3-2 0-3 0-4-1l-1-1z" class="T"></path><path d="M461 441v1c1 1 0 1 0 2v1c0 1-1 2-2 3s-1 2-2 3h1c1 0 1 0 1-1s1-1 1-2c1 0 2-1 2-2l2-3c0-1 1-2 1-3l1-1v3 1h-1c0 1-1 2-1 3v1l3 1v1c-1-1-3 0-4 0-2 2-3 5-4 8v-2l-1 1c0 1-1 1-1 2h-2c0-5 3-13 6-17z" class="I"></path><path d="M465 425c0 6-2 11-4 16-3 4-6 12-6 17v2c-1 1-1 1-2 1s0 0-1-1v-2l-1-3c1-2 0-4 0-6l1-1 1 1c0-2 1-4 3-6h0c1-1 2-1 2-2s0-2 1-2l1 1c3-5 4-9 4-14l1-1z" class="S"></path><path d="M459 439l1 1c-3 5-6 12-8 18l-1-3c1-2 0-4 0-6l1-1 1 1c0-2 1-4 3-6h0c1-1 2-1 2-2s0-2 1-2z" class="G"></path><path d="M452 465c1-1 2-1 4-2h0c1-1 2-1 3-2 1 1 5 4 7 5 0 1 0 1 1 1h0c0-1 1-2 0-3v-2l1 1 1 1 1 14v5 1c-1 2-1 5 0 7h-2l-1 1v2c-1-3-3-4-5-6l-2-2c-1-2-2-3-1-6h-5l-3-3c-1-2-1-4-1-6l-1-2 3-3v-1z" class="E"></path><path d="M467 467h0c0-1 1-2 0-3v-2l1 1 1 1 1 14v5 1c-1 2-1 5 0 7h-2l-1 1c0-6-1-13 0-19h1l-1-1h0c-1-2-1-3 0-5zm-15-1c3 0 7-1 10 0 2 1 4 3 4 5v1 1 2c0 1-1 1-1 1-2 1-3 3-3 4l-1 1-2-1h-5l-3-3c-1-2-1-4-1-6l-1-2 3-3z" class="D"></path><path d="M457 472h1c1 1 2 1 2 3 0 1-1 2-2 3h-1c-2-1-2-2-2-3 0-2 1-2 2-3z" class="Z"></path><path d="M452 466c3 0 7-1 10 0 2 1 4 3 4 5v1c-1-1-2-3-4-4-1-1-5-1-7-1-2 1-3 2-5 4l-1-2 3-3z" class="K"></path><path d="M314 335l-16 32h0l19-66c0 2 1 3 2 5 1 1 2 1 3 0h2c1-1 3-1 5-1h0l1 2h0l2-1h0v-1h32c2 0 5-1 7 0h1l-2 1 1 1c3 0 5-1 7-2h6-3v1c2 1 4 0 6 0 2-1 5-1 8-1l1 1c3 0 6-1 10-1 4 1 9 0 13 0s7 1 11 0h2v4h-1l-1 1c-2-1-3 0-5 0-6 1-13 0-19 1h-27l-31 1c-2 1-4 1-6 1s-3-1-4-1c-3-1-8-1-11 0-1 3-2 5-4 7l-1 1c-1 0-1 1-1 2h-1l-1 4c-1 1-1 3-3 4 0 2-1 3-2 5z" class="B"></path><path d="M317 325l2 1c-1 1-1 3-3 4h0c-1 0-1-1-2-1l3-4z" class="H"></path><path d="M314 335l-1-1c0-1 0-2 1-3 0-1 1-1 2-1h0c0 2-1 3-2 5zm8-19c-1 0-2 0-3-1v-1c1-2 2-2 4-2 0 1 0 3-1 4zm-4 5c1 0 2 0 2 1l-1 4-2-1c-1-1-1-1 0-3l1-1z"></path><path d="M323 312h4c-1 3-2 5-4 7l-1 1c-1 0-1 1-1 2h-1c0-1-1-1-2-1 1-1 1 0 2 0 1-1 1-2 1-2v-2h-1c1 0 1 0 1-1h1c1-1 1-3 1-4z" class="H"></path><path d="M467 492l1-1h2c-1-2-1-5 0-7v-1 20c1 1 1 2 1 3l1-2v1c0 7 0 14 1 21v6l-1 59c-1-5-1-12-3-17l-6-6c-1-1-2-2-3-2-1-1-1-1-1-2l-1-1h-1v-1h-1l-1-3-1 1c-1-2-1-3-1-4-1 1-2 3-4 4h-2-1l1-2v-1h-2c1-5 3-9 5-13 6-12 13-25 16-38 1-4 2-8 1-12v-2z" class="C"></path><path d="M469 553l1-2 1 1c0 2 0 2-1 4-1-2-1-2-1-3z" class="H"></path><path d="M466 548c1-2 1-4 3-6 0 2 1 7 0 8l-3-1v-1z" class="G"></path><path d="M466 535l2 1 1 1v2c-1 1-3 1-5 1h0c0-2 1-3 2-5z" class="O"></path><path d="M458 563c1-1 1-3 2-4 3-1 4 0 7 0 1 1 2 1 2 2 1 1 1 6 0 7-1 0-1 0-1-1-3-1-7-3-9-3l-1-1z" class="D"></path><path d="M461 546l1-2h1c1 1 2 2 3 4v1l3 1v3c0 1 0 1 1 3-1 0-1 1-2 1l-1 1h-1l1 1h0c-3 0-4-1-7 0-1 1-1 3-2 4h-1v-1h-1l-1-3 1-1h0c1-3 2-6 4-9 0-1 1-2 1-3z" class="J"></path><path d="M456 558h0c1-3 2-6 4-9v3l1 1c0 1 0 2-1 3l-1 1h0c-1 1-2 2-2 3l-1 1v-3z" class="N"></path><path d="M461 546l1-2h1c1 1 2 2 3 4v1l3 1v3c0 1 0 1 1 3-1 0-1 1-2 1h-3v-1c0-1 0-3 1-4l1-1c-1-1-2-1-3-1-1-2-2-3-3-4z" class="O"></path><path d="M471 506l1-2v1c0 7 0 14 1 21v6c-2-1-2-1-3-2l1-1c0-2 0-2-1-2h-2v1c0 1 1 1 1 2l-1 1h0v1h2l1 1h-1-1v3h-1l-2-1c-1 2-2 3-2 5v1l2 2c1-1 1-2 1-3h1l1 2c-2 2-2 4-3 6-1-2-2-3-3-4h-1l-1 2c0 1-1 2-1 3-2 3-3 6-4 9h0l-1 1-1 1c-1-2-1-3-1-4 2-6 5-12 7-18 2-4 4-11 7-15l3-2 1-15z" class="F"></path><path d="M466 535v-1l3-1v3h-1l-2-1z" class="E"></path><defs><linearGradient id="Ac" x1="449.868" y1="522.218" x2="466.669" y2="523.998" xlink:href="#B"><stop offset="0" stop-color="#d5d4d3"></stop><stop offset="1" stop-color="#faf9f8"></stop></linearGradient></defs><path fill="url(#Ac)" d="M467 492l1-1h2c-1-2-1-5 0-7v-1 20c1 1 1 2 1 3l-1 15-3 2c-3 4-5 11-7 15-2 6-5 12-7 18-1 1-2 3-4 4h-2-1l1-2v-1h-2c1-5 3-9 5-13 6-12 13-25 16-38 1-4 2-8 1-12v-2z"></path><path d="M470 503c1 1 1 2 1 3l-1 15-3 2h-2c2-6 4-12 5-20z" class="V"></path><path d="M453 544l12-21h2c-3 4-5 11-7 15-2 6-5 12-7 18-1 1-2 3-4 4h-2-1l1-2v-1l3-8 3-5z" class="P"></path><path d="M453 544c1 1 2 3 1 4l-2 4c0-1-1-2-2-3l3-5z" class="K"></path><path d="M450 549c1 1 2 2 2 3-1 3-2 4-4 6v1h-1v-1-1l3-8z" class="U"></path><defs><linearGradient id="Ad" x1="426.417" y1="710.324" x2="451.747" y2="609.97" xlink:href="#B"><stop offset="0" stop-color="#5f5d5f"></stop><stop offset="1" stop-color="#8d8c89"></stop></linearGradient></defs><path fill="url(#Ad)" d="M437 590h2v2c-1 2-1 2-1 3s0 0 1 2v4c0 2 1 3 1 5v1c1 3 3 6 6 9 1 1 3 2 4 3h1c3 2 5 3 6 6 1 1 1 3 1 4 0 6 2 11-2 16l-2 3-1 2-1 1-2 1-2 2h-5l1 2 2 4c-2 2-2 3-2 5 1 2 2 3 4 5 1-1 1-1 2-1l3 1c-1 1-2 1-3 2-4 2-6 4-7 8l2 3c1 2 3 3 5 4l1 1c2 1 3 1 5 1h0v2c-1 1-2 3-3 5-1 0-1 1-1 2 0 2 0 2-2 4l2 1v2l-1 1 1 1c-1 2-1 4-3 6h0c-1 1-2 1-2 2-2 0-2-1-4-1l2 2c-1 1-1 1-2 1h0-1-1c-1-1-1-2-1-3-1-1-2-3-3-4 0-7 1-15 0-22v-98z"></path><path d="M446 616c1 1 3 2 4 3h1c-1 1-1 2-2 2-2 0-3 1-3 2l-1-1c0-1 1-1 1-2-1 0-1-1-1-2s0-1 1-2z" class="P"></path><path d="M442 652l1 2 1 2 2 4c-2 2-2 3-2 5 0-2-1-3-2-5 1-1 0-6 0-8z" class="S"></path><path d="M442 638c2 0 1-1 3-2h3c-1 2-2 2-1 4l2 2-1 2-1-1c-1 0-2 0-3-1s-2-3-2-4z" class="G"></path><path d="M442 688h-1c0-1 0-2-1-3v3l-1-15h1v1l1 1c-1-2-1-6 0-8v7h1c0 2 0 4 1 5v1c-1 2-1 5-1 7v1h0z" class="Y"></path><path d="M443 706s-1 0-2 1l-1-1c1-4 2-9 2-13v-1h1v1c1 1 2 1 3 2l-1 1c0 1-1 2-1 4l1 1c-1 1-2 2-2 3v1 1z" class="X"></path><path d="M443 680h0l2 3-1 2c1 1 1 2 2 3 0 1 0 1 1 2v4l-1 1c-1-1-2-1-3-2 0-2 0-3-1-5h0v-1c0-2 0-5 1-7z" class="V"></path><path d="M447 694l1-3h1v3s-1 1-1 2h1c1 1 2 1 3 2 0 2 0 2-2 4l-1 1-1-1h-2v1l1 1-4 2v-1-1c0-1 1-2 2-3l-1-1c0-2 1-3 1-4l1-1 1-1z" class="I"></path><path d="M445 696c1 1 2 1 2 3-1 1-1 0-3 1 0-2 1-3 1-4z" class="E"></path><path d="M445 683c1 2 3 3 5 4l1 1c2 1 3 1 5 1h0v2c-1 1-2 3-3 5-1 0-1 1-1 2-1-1-2-1-3-2h-1c0-1 1-2 1-2v-3h-1l-1 3v-4c-1-1-1-1-1-2-1-1-1-2-2-3l1-2z" class="Y"></path><path d="M450 702l2 1v2l-1 1 1 1c-1 2-1 4-3 6h0c-1 1-2 1-2 2-2 0-2-1-4-1l-1-1h0c2-1 2-1 3-1 0-2-2-2-2-3v-2h1c2-1 2-1 3-2v-1l-1-1v-1h2l1 1 1-1z" class="F"></path><path d="M451 706l1 1c-1 2-1 4-3 6h0c-1 1-2 1-2 2-2 0-2-1-4-1l-1-1h0c2-1 2-1 3-1 0-2-2-2-2-3v-2c1 2 1 2 2 2s2 0 3 1h2c0-2 0-2 1-4z" class="G"></path><path d="M442 660c1 2 2 3 2 5 1 2 2 3 4 5 1-1 1-1 2-1l3 1c-1 1-2 1-3 2-4 2-6 4-7 8h0v-1c-1-1-1-3-1-5 0-4-1-9 0-14z" class="U"></path><path d="M443 679v-2-1c1-2 2-4 1-6 0-1 0 0 1-1l2 1h1c1-1 1-1 2-1l3 1c-1 1-2 1-3 2-4 2-6 4-7 8h0v-1z" class="X"></path><path d="M447 643l1 1c3 1 5 1 8 1l-2 3-1 2-1 1-2 1-2 2h-5l-1-2 1-4c0-2 2-3 4-5z" class="D"></path><path d="M450 647h4v1l-1 2-1 1-2 1 1-2c0-1 0-2-1-3z" class="M"></path><path d="M447 643l1 1c3 1 5 1 8 1l-2 3v-1h-4c-2 0-4 1-6 1h-1c0-2 2-3 4-5z" class="N"></path><path d="M457 632l1-3c0 6 2 11-2 16-3 0-5 0-8-1l1-2-2-2c-1-2 0-2 1-4 3-1 3-1 6 1l1-1v-2l2-2z" class="C"></path><path d="M448 636c3-1 3-1 6 1v2c0 1 0 2-2 3h-3l-2-2c-1-2 0-2 1-4z"></path><path d="M451 619c3 2 5 3 6 6 1 1 1 3 1 4l-1 3-2 2v2l-1 1c-3-2-3-2-6-1h-3c-2 1-1 2-3 2v-3c0-2 1-5 2-7l1-1c1-2 2-4 4-6 1 0 1-1 2-2z" class="Q"></path><path d="M453 631c-1 0-2 0-3-1-2-1-2-1-2-2s1-1 1-1c2-2 3-2 5-1s2 1 2 3c0 1-2 2-3 2z"></path><path d="M453 631c1 0 3-1 3-2l1 3-2 2v2l-1 1c-3-2-3-2-6-1h-3c-2 1-1 2-3 2v-3c1 0 2-1 3-1 2-1 6 1 8-1l-1-1h-3l4-1z" class="F"></path><path d="M468 334c5-11 6-22 9-33h133l6 55-1 1h0l-2-9c1-2 0-6-1-8l-5-25-2-10c-3 0-10-1-13 1-1 1-3 2-5 2h0l-2 1c-2 0-5 0-6-1l-1-1h-1c-2 1-7 1-9 1-2-2-1-1-1-3h-64-15c-3 0-6 0-9 1l-1 1c-2 11-2 21-3 32 0 4 1 8 0 11v7c-1 3 0 5 0 8-1 1-1 2-1 3s1 1 0 2v4 7c-1-2 0-3 0-4v-13c-1-2-1-4-1-7v-1h-1c-1 7-2 15-2 23l-1 1h-1l-1-1c0-1 0-1-1-2h-2 0l-1-1c-1-1-2-1-3-2 0 0-2-1-3-1l-2 2c0-4 1-7 0-11 0-1 1-2 1-2l4-8h-3l6-9 5-11z"></path><path d="M473 344v9l2-28c1 5 0 10 0 15v1 10c0 2-1 4-1 6h1v-7 7c-1 3 0 5 0 8-1 1-1 2-1 3s1 1 0 2v4 7c-1-2 0-3 0-4v-13c-1-2-1-4-1-7v-1h-1l1-12z" class="L"></path><path d="M567 305h15 8l-3 3-2 1c-2 0-5 0-6-1l-1-1h-1c-2 1-7 1-9 1-2-2-1-1-1-3z" class="N"></path><path d="M471 335v-1l2-8c1 5 0 10 0 15l-1 3c-1 2-3 7-4 8-2 3-7 6-7 10-2 2-4 5-5 8h1v3l-2 2c0-4 1-7 0-11 0-1 1-2 1-2l4-8h-3l6-9 5-11 1 1 2-5v2 2l-1 1h1z" class="M"></path><path d="M463 345h3l-6 9h-3l6-9z" class="Z"></path><path d="M468 334l1 1 2-5v2 2l-1 1h1c-1 4-3 7-5 10h-3l5-11z" class="L"></path><defs><linearGradient id="Ae" x1="475.328" y1="357.872" x2="460.835" y2="373.491" xlink:href="#B"><stop offset="0" stop-color="#cecdce"></stop><stop offset="1" stop-color="#f3f1f0"></stop></linearGradient></defs><path fill="url(#Ae)" d="M461 362c0-4 5-7 7-10 1-1 3-6 4-8l1-3v3l-1 12c-1 7-2 15-2 23l-1 1h-1l-1-1c0-1 0-1-1-2h-2 0l-1-1c-1-1-2-1-3-2 0 0-2-1-3-1v-3h-1c1-3 3-6 5-8z"></path><path d="M464 360c2-1 2-2 3-3 1 3 2 11 0 14l-3 3h-4s-2-1-3-1v-3h-1c1-3 3-6 5-8 1-1 1-2 2-2h1z" class="B"></path><path d="M463 366l3-2c0 2 0 1-1 2s-2 2-2 4h-2 0l-1-2v-1c1-1 2-1 3-1z" class="F"></path><path d="M461 362c1-1 1-2 2-2h1l-1 4c-1 0-1 1-1 2h1c-1 0-2 0-3 1v1l1 2h0c2 1 2 2 4 1l1-2 1 2-3 3h-4s-2-1-3-1v-3h-1c1-3 3-6 5-8z" class="S"></path><path d="M461 362c1-1 1-2 2-2h1l-1 4c-1 0-1 1-1 2h1c-1 0-2 0-3 1v1l1 2c-1 0-2 1-2 1h-1l1-2v-1h-1l3-6z" class="M"></path><defs><linearGradient id="Af" x1="437.966" y1="514.183" x2="447.5" y2="514.497" xlink:href="#B"><stop offset="0" stop-color="#6d6b6b"></stop><stop offset="1" stop-color="#8e8c8b"></stop></linearGradient></defs><path fill="url(#Af)" d="M446 442v3h1l-1 2 1 2 1-1c0 5 2 7 3 12 0 1 0 3 1 5v1l-3 3 1 2c0 2 0 4 1 6l3 3h5c-1 3 0 4 1 6l2 2c2 2 4 3 5 6 1 4 0 8-1 12-3 13-10 26-16 38l-1-1v1c-1-1-1-2-1-3 1-3 3-5 5-7l2-5h-1 0l-3 3h-1-5l-1-1h0v-1h-2c-1 1 0 1-1 1l-1-4c0-2 0-5-1-7 0-2-1-4-1-7 0-1 1-4 0-5l-1-20v-18c0-2 1-5 3-7v-4l1-1c0-5 2-12 5-16z"></path><path d="M447 523c-1-1-1 0-3 0h-1c1-1 1-1 1-2v-4c1-1 2-1 4-1 0 0 0 1 1 1v1l2-1h0l1 2c0 1-2 2-2 3-1-1-2-1-2-2h-1c0 1 1 2 0 3z" class="M"></path><path d="M458 508c1 0 1 1 2 1-1 2-2 4-1 7l-1 1c-2 6-5 10-8 15h-5l-1-1h0v-1c0-3-1-4 1-6h2v-1c1-1 0-2 0-3h1c0 1 1 1 2 2 0-1 2-2 2-3l-1-2h0v-1l1 1 3-3 3-3h0v-3z" class="I"></path><path d="M458 508c1 0 1 1 2 1-1 2-2 4-1 7-3 0-5 1-8 1h0v-1l1 1 3-3 3-3h0v-3z" class="G"></path><path d="M444 531c0-2 0-2 1-4h2 2c1-1 2-3 3-5s3-4 5-5h1c-2 6-5 10-8 15h-5l-1-1z" class="F"></path><path d="M437 470c0-2 1-5 3-7l-1 5c0 6 0 9 4 13l3 3 3 2-1 2c-1 1-1 2-1 3l2 2-1 2h0 0c1 2 2 3 4 4l3 5c2 0 4 0 5 1l2-1v1c0 1 0 1-1 2-1-1-2-1-4-1 0 1 1 1 1 2v3h0l-3 3c-1 0-1 0-2-1h-1c-6-5-11-10-13-18v-2c-1-2-1-3-1-5h-1v-18z" class="Z"></path><path d="M448 495c1 2 2 3 4 4l3 5h-3c-2-2-3-5-5-8l1-1h0z" class="G"></path><path d="M452 504h3c2 0 4 0 5 1l2-1v1c0 1 0 1-1 2-1-1-2-1-4-1 0 1 1 1 1 2v3h0l-2-2c-1-2-2-4-4-5z" class="B"></path><path d="M451 505c1 2 2 3 4 4h1l2 2-3 3c-1 0-1 0-2-1-1-2-3-5-4-7l2-1z"></path><path d="M440 487c2 1 2 1 2 2 1 2 1 5 3 6v1c0 2 0 3 2 5h0c1 1 2 1 2 2h1c1 1 1 1 1 2l-2 1c-5-6-8-11-9-19z" class="L"></path><path d="M437 470c0-2 1-5 3-7l-1 5c0 6 0 9 4 13l3 3 3 2-1 2c-1 1-1 2-1 3l2 2-1 2h0l-1 1h-1c-1-2 0-4 0-6-1-2-2-4-4-5 0 0-1 0-2-1v3c1 8 4 13 9 19 1 2 3 5 4 7h-1c-6-5-11-10-13-18v-2c-1-2-1-3-1-5h-1v-18z" class="C"></path><path d="M437 470c0-2 1-5 3-7l-1 5c-1 2-1 8-1 10v10h-1v-18z" class="K"></path><path d="M446 442v3h1l-1 2 1 2 1-1c0 5 2 7 3 12 0 1 0 3 1 5v1l-3 3 1 2c0 2 0 4 1 6l3 3c-1 1-2-1-4-2l2 4 1 1 1 2-2 2v1l-2 2-2-2 1-2-3-2-3-3c-4-4-4-7-4-13l1-5v-4l1-1c0-5 2-12 5-16z" class="T"></path><path d="M446 442v3h1l-1 2c-2 6-2 12-3 18-1-2 0-4-1-6h0c1-1 0-4 0-5 0 1 0 3-1 4 0-5 2-12 5-16z" class="H"></path><path d="M446 453c0 6 0 11 3 17v-1l1 2c0 2 0 4 1 6l3 3c-1 1-2-1-4-2-1-1-3-2-3-3-3-5-2-17-1-22z" class="P"></path><path d="M448 448c0 5 2 7 3 12 0 1 0 3 1 5v1l-3 3v1c-3-6-3-11-3-17l1-4 1-1z" class="C"></path><path d="M441 458c1-1 1-3 1-4 0 1 1 4 0 5h0c1 2 0 4 1 6-1 4-1 10 3 13 1 2 4 3 6 4l1 1 1 2-2 2v1l-2 2-2-2 1-2-3-2-3-3c-4-4-4-7-4-13l1-5v-4l1-1z" class="E"></path><path d="M449 486c-1-1-1-2-1-2 1-1 1-1 2-1s1 0 2 2v2 1l-2 2-2-2 1-2z" class="D"></path><path d="M450 478c2 1 3 3 4 2h5c-1 3 0 4 1 6l2 2c2 2 4 3 5 6 1 4 0 8-1 12-3 13-10 26-16 38l-1-1v1c-1-1-1-2-1-3 1-3 3-5 5-7l2-5h-1 0l-3 3h-1c3-5 6-9 8-15l1-1c-1-3 0-5 1-7-1 0-1-1-2-1 0-1-1-1-1-2 2 0 3 0 4 1 1-1 1-1 1-2v-1l-2 1c-1-1-3-1-5-1l-3-5c-2-1-3-2-4-4h0 0l1-2-2-2c0-1 0-2 1-3l2 2 2-2v-1l2-2-1-2-1-1-2-4z" class="K"></path><path d="M462 504l1-1c1 0 1 1 2 1l-1 5c-1-1-2-2-3-2 1-1 1-1 1-2v-1z" class="G"></path><path d="M453 534c0 3-2 6-4 9v1c-1-1-1-2-1-3 1-3 3-5 5-7z" class="S"></path><path d="M454 485c1 1 2 2 3 2v1h-1c-2 1-5 3-7 5l-2-2c0-1 0-2 1-3l2 2 2-2v-1l2-2z" class="M"></path><path d="M450 478c2 1 3 3 4 2h5c-1 3 0 4 1 6-3-1-4-3-7-3l-1-1-2-4z" class="H"></path><path d="M458 508c0-1-1-1-1-2 2 0 3 0 4 1 1 0 2 1 3 2-2 6-4 13-7 18l-1-1s0-1 1-2c0 0 1-1 1-2 1-1 1-3 1-5 1-3 3-6 2-9l-1 1c-1 0-1-1-2-1z" class="M"></path><path d="M460 509l1-1c1 3-1 6-2 9 0 2 0 4-1 5 0 1-1 2-1 2-1 1-1 2-1 2l1 1-1 2h-1-1 0l-3 3h-1c3-5 6-9 8-15l1-1c-1-3 0-5 1-7z" class="B"></path><path d="M448 495c3-3 6-5 10-5 3 0 5 1 7 3 0 1 1 2 1 3 1 2 0 6-1 8-1 0-1-1-2-1l-1 1-2 1c-1-1-3-1-5-1l-3-5c-2-1-3-2-4-4z" class="F"></path><path d="M448 495c3-3 6-5 10-5 1 1 3 3 4 5l-2 2-1-1h-4c0 1-1 1-1 2v2l-2-1c-2-1-3-2-4-4z" class="D"></path><path d="M438 372c0-5 0-8 2-13-1 3-1 6-1 10h1c0 1 0 3 1 5-1 2-1 4-1 7h0c0-1 0-2 1-3v2-1l2-4v3c0 3 0 5 1 7 2 0 2 0 3 1 1 2 2 4 4 6l2-1v-1h-1-2c-1-2-2-3-2-5l6-3 3-1-1-4h-1v-2l2-2c1 0 3 1 3 1 1 1 2 1 3 2l1 1h0 2c1 1 1 1 1 2l1 1h1l1-1c0 3 1 6 1 8 0 5-1 9-3 13v1l-2 2h-2s0 1-1 1c2 7 3 14 2 21l-1 1c0 5-1 9-4 14l-1-1c-1 0-1 1-1 2s-1 1-2 2h0c-2 2-3 4-3 6l-1-1-1 1c0 2 1 4 0 6l1 3v2h-1c-1-5-3-7-3-12l-1 1-1-2 1-2h-1v-3c-3 4-5 11-5 16l-1 1v4c-2 2-3 5-3 7v-10-12-13-63h1z" class="R"></path><path d="M440 395v1c0 2 1 4 1 6s-1 3 0 4c0 1 2 4 3 4l1 1 2 2c-1 0-2 1-3 1-1 1-1 2-2 3v1c0-2-1-3-2-5v-18z" class="U"></path><path d="M438 372c0-5 0-8 2-13-1 3-1 6-1 10h1c0 1 0 3 1 5-1 2-1 4-1 7s1 8 0 11c0-1 0-2-1-4 0-2-1-4-1-5-1-4 0-7 0-11z" class="J"></path><path d="M440 413c1 2 2 3 2 5 1 1 1 3 3 4h1c1 1 2 1 3 3 1 1 1 3 1 4-1 1 0 1-1 2v1l-2 1h1 2l-4 8v1c-3 4-5 11-5 16l-1 1v4c-2 2-3 5-3 7v-10c1-3 1-7 2-10v-1l1-1v-2c0-3 1-6 1-9s-1-7 0-11c0-4-1-8-1-13z" class="W"></path><path d="M444 432l3 1h1 2l-4 8-1-1c-1-1-1-2-1-3v-5z" class="R"></path><path d="M444 432l3 1h1c0 2-1 2-1 3l-3 1v-5z" class="Y"></path><path d="M439 450c1 1 1 1 1 2 0 2-2 5-1 7h1v4c-2 2-3 5-3 7v-10c1-3 1-7 2-10z" class="X"></path><path d="M446 422c1 1 2 1 3 3 1 1 1 3 1 4-1 1 0 1-1 2v1l-2 1-3-1v-1c0-1-1-1-1-2 0-3 1-5 3-7z" class="D"></path><path d="M443 375v3c0 3 0 5 1 7 2 0 2 0 3 1 1 2 2 4 4 6h3l-1 2c0 1 1 1 3 2v1c-1 3 0 2 1 4 0 1 0 2 1 4v2 1 2h-1 0-1c-1 2-1 3-2 5h0-1-1c-2 1-2 0-4-1l-1-1-2-2-1-1c-1 0-3-3-3-4-1-1 0-2 0-4s-1-4-1-6v-1-2-1c1-3 0-8 0-11h0c0-1 0-2 1-3v2-1l2-4z" class="H"></path><path d="M445 411h6l-3 3-1-1-2-2z" class="S"></path><path d="M446 405l1-1c1 0 1 0 2 1s1 2 0 3l-1 1h-2v-4z" class="U"></path><path d="M451 411c1-1 1-2 1-3h0l1 2c0 2 1 3 0 5h-1c-2 1-2 0-4-1l3-3z" class="K"></path><path d="M453 410c0-1-1-3 0-4h1c1 1 2 3 3 4h-1c-1 2-1 3-2 5h0-1c1-2 0-3 0-5z" class="E"></path><path d="M443 375v3c0 3 0 5 1 7 1 5 3 8 6 11 0 2 1 3 0 5-7-6-7-13-9-22l2-4z" class="V"></path><path d="M441 392h1c3 4 3 9 2 13h2v4h2c-1 1-2 1-3 2l-1-1c-1 0-3-3-3-4-1-1 0-2 0-4s-1-4-1-6v-1-2l1-1z" class="K"></path><path d="M443 405l1 1v-1h2v4c-1-1-2-1-2-2-1 0-1-1-1-2z" class="W"></path><path d="M441 392h1c3 4 3 9 2 13v1l-1-1c0-4-1-8-2-13z" class="R"></path><path d="M444 385c2 0 2 0 3 1 1 2 2 4 4 6h3l-1 2c0 1 1 1 3 2v1c-1 3 0 2 1 4 0 1 0 2 1 4v2 1 2h-1 0c-1-1-2-3-3-4-1-2-2-3-4-5 1-2 0-3 0-5-3-3-5-6-6-11z" class="E"></path><path d="M450 396c4 4 6 7 8 11v1 2h-1 0c-1-1-2-3-3-4-1-2-2-3-4-5 1-2 0-3 0-5z" class="Y"></path><path d="M458 408l1 2c0 3 1 6 1 9-1 2-2 5-1 7v1c1 2 0 4 0 7h0l-3 9c-2 2-3 4-3 6l-1-1-1 1c0 2 1 4 0 6l1 3v2h-1c-1-5-3-7-3-12l-1 1-1-2 1-2h-1v-3-1l4-8h-2-1l2-1v-1c1-1 0-1 1-2 0-1 0-3-1-4-1-2-2-2-3-3h-1c-2-1-2-3-3-4v-1c1-1 1-2 2-3 1 0 2-1 3-1l1 1c2 1 2 2 4 1h1 1 0c1-2 1-3 2-5h1 0 1v-2z" class="D"></path><path d="M448 448c0-1 1-3 2-4 0 1 1 2 0 3h-1c0 3 0 5 1 7l1 1 1 3v2h-1c-1-5-3-7-3-12z" class="E"></path><path d="M453 433l2 3c-2 2-4 5-5 8-1 1-2 3-2 4l-1 1-1-2 1-2 6-12z" class="S"></path><path d="M452 428l1 1 3-1-3 5-6 12h-1v-3-1l4-8 2-5z" class="Q"></path><path d="M459 410c0 3 1 6 1 9-1 2-2 5-1 7v1l-3 8h-1v1l-2-3 3-5 1-4 1-4c1-2 1-3 0-5-1-1 0-4 1-5z" class="N"></path><path d="M458 408l1 2c-1 1-2 4-1 5 1 2 1 3 0 5l-1 4-1 4-3 1-1-1-2 5h-2-1l2-1v-1c1-1 0-1 1-2 0-1 0-3-1-4-1-2-2-2-3-3h-1c-2-1-2-3-3-4v-1c1-1 1-2 2-3 1 0 2-1 3-1l1 1c2 1 2 2 4 1h1 1 0c1-2 1-3 2-5h1 0 1v-2z" class="K"></path><path d="M454 415h0c1-2 1-3 2-5 1 3 1 5-1 7 0 1-1 1-1 2v-4z" class="I"></path><path d="M458 415c1 2 1 3 0 5l-1 4h-1l-1-1-1-1h-2v1l-2-1c1 0 2 0 3-1 2-1 4-3 5-6z" class="Q"></path><path d="M450 422l2 1v-1h2l1 1 1 1h1l-1 4-3 1-1-1 1-2h-1l-1 1c0-2-1-3-1-5zm-3-9l1 1c2 1 2 2 4 1h1 1v4l-1 1h-2c-2-1-6-3-7-6 1 0 2-1 3-1zm10-40c1 0 3 1 3 1 1 1 2 1 3 2l1 1h0 2c1 1 1 1 1 2l1 1h1l1-1c0 3 1 6 1 8 0 5-1 9-3 13v1l-2 2h-2s0 1-1 1c2 7 3 14 2 21l-1 1c0 5-1 9-4 14l-1-1c-1 0-1 1-1 2s-1 1-2 2h0l3-9h0c0-3 1-5 0-7v-1c-1-2 0-5 1-7 0-3-1-6-1-9l-1-2v-1-2c-1-2-1-3-1-4-1-2-2-1-1-4v-1c-2-1-3-1-3-2l1-2h-3l2-1v-1h-1-2c-1-2-2-3-2-5l6-3 3-1-1-4h-1v-2l2-2z" class="D"></path><path d="M460 419c1 3 1 5 0 8 2 3 0 9-1 12-1 0-1 1-1 2s-1 1-2 2h0l3-9h0c0-3 1-5 0-7v-1c-1-2 0-5 1-7z" class="H"></path><path d="M462 380h0c4 4 4 5 4 10 0 3 0 6 1 8l1 2v1l-2 2h-2s0 1-1 1c2 7 3 14 2 21l-1 1c0-5 1-10 0-15h0c-1-2-1-4-2-6 0-1-2-6-3-7-1 0-3-1-3-1v-1c2 0 3 0 4-2 3-2 4-5 4-8s-1-4-3-5l1-1z" class="N"></path><path d="M463 404l-2-5c2-2 4-5 5-8v-1c0 3 0 6 1 8l1 2v1l-2 2h-2s0 1-1 1z" class="E"></path><path d="M457 373c1 0 3 1 3 1 1 1 2 1 3 2l1 1h0 2c1 1 1 1 1 2l1 1h1l1-1c0 3 1 6 1 8 0 5-1 9-3 13l-1-2c-1-2-1-5-1-8 0-5 0-6-4-10h0l-1 1h-4l-1-4h-1v-2l2-2z" class="F"></path><path d="M457 373c1 0 3 1 3 1 1 1 2 1 3 2l1 1h0c1 2 4 4 4 5l-1-1-1 1v-1c0-1-1-1-2-2-1 0-1 1-2 1h0l-1 1h-4l-1-4h-1v-2l2-2z" class="D"></path><path d="M456 377c3 1 4 1 6 3l-1 1h-4l-1-4z" class="X"></path><path d="M457 381h4c2 1 3 2 3 5s-1 6-4 8c-1 2-2 2-4 2-2-1-3-1-3-2l1-2h-3l2-1v-1h-1-2c-1-2-2-3-2-5l6-3 3-1z" class="F"></path><path d="M452 390c1-2 1-3 2-4 2 0 2-1 4 0l1 1c-1 2-2 3-3 4s-1 1-2 1h-3l2-1v-1h-1z" class="B"></path><path d="M317 301h120c1 1 0 0 0 2v10 31c0 10-1 19 0 28v63 13 12 10 18l1 20c1 1 0 4 0 5 0 3 1 5 1 7 1 2 1 5 1 7l1 4c1 0 0 0 1-1h2v1 8l1 1-1 1-3 7c-2 2-3 5-4 9v3c-1 10-1 20 0 30h0v98c1 7 0 15 0 22 1 1 2 3 3 4 0 1 0 2 1 3h1 1l-2 2 1 1-1 1c0 2 1 2 1 4h-2c0 1 0 2 1 3 0 1 0 4-1 5v4c-1-2-1-5-2-7h-1l-1 1v-2-1c-1-2-1-7 0-10 0-1-1-2-1-3v-3c0-2 0-3-1-5h0c-1-1 0-3 0-4h0l-1 1h-1c0-1 0-2 1-3 0 1 1 1 1 1l1-1c-1-4-1-8-1-12l-1-32v-67-33-46l-1-1c0-1 0-2-1-2v-4h0v-2c-1 0-2 1-3 1 0-2 1-3 3-4 1-1 1-2 2-4v-22-4-87-31-20c0-4 0-8-1-12 1-4 1-10 0-14h-2c-4 1-7 0-11 0s-9 1-13 0c-4 0-7 1-10 1l-1-1c-3 0-6 0-8 1-2 0-4 1-6 0v-1h3-6c-2 1-4 2-7 2l-1-1 2-1h-1c-2-1-5 0-7 0h-32v1h0l-2 1h0l-1-2h0c-2 0-4 0-5 1h-2c-1 1-2 1-3 0-1-2-2-3-2-5z"></path><path d="M433 473c1 8 0 17 0 25 0 4 1 9 0 13l-1-1c0-1 0-2-1-2v-4h0v-2c-1 0-2 1-3 1 0-2 1-3 3-4 1-1 1-2 2-4v-22z" class="W"></path><path d="M431 499c1-1 1-2 2-4 0 2 0 4-1 7v1l-1 1h0v-2c-1 0-2 1-3 1 0-2 1-3 3-4z" class="U"></path><path d="M437 688c1 7 0 15 0 22 1 1 2 3 3 4 0 1 0 2 1 3h1 1l-2 2 1 1-1 1c0 2 1 2 1 4h-2c0 1 0 2 1 3 0 1 0 4-1 5v4c-1-2-1-5-2-7h-1l-1 1v-2-1c-1-2-1-7 0-10 0-1-1-2-1-3v-3c0-2 0-3-1-5h0c-1-1 0-3 0-4h0l-1 1h-1c0-1 0-2 1-3 0 1 1 1 1 1l1-1h0c0 2 0 4 1 5 1 3 0 7 0 10 1-1 0-8 1-10 1-5 0-12 0-18z" class="T"></path><path d="M437 710c1 1 2 3 3 4 0 1 0 2 1 3h1 1l-2 2 1 1-1 1c0 2 1 2 1 4h-2c0 1 0 2 1 3 0 1 0 4-1 5-1-2-1-4-1-6-1-6-2-11-2-17z" class="Y"></path><path d="M438 508c1 1 0 4 0 5 0 3 1 5 1 7 1 2 1 5 1 7l1 4c1 0 0 0 1-1h2v1 8l1 1-1 1-3 7c-2 2-3 5-4 9v3l1-52z" class="R"></path><path d="M440 527l1 4c1 0 0 0 1-1h2v1 8l1 1-1 1-3 7c-2 2-3 5-4 9v-6l3-9v-15z" class="W"></path><path d="M444 541c-1 0-2-1-2-1v-1c-1-2-1-6 0-7 1 1 1 4 1 6l1 1 1 1-1 1z" class="R"></path></svg>