<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="216 121 599 748"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#202020}.C{fill:#bebdbe}.D{fill:#8d8c8c}.E{fill:#494848}.F{fill:#737272}.G{fill:#2e2d2d}.H{fill:#626161}.I{fill:#3c3b3c}.J{fill:#6b696a}.K{fill:#929191}.L{fill:#111110}.M{fill:#4c4b4b}.N{fill:#d7d5d6}.O{fill:#807e7f}.P{fill:#e4e3e4}.Q{fill:#787777}.R{fill:#e2e1e2}.S{fill:#a7a6a6}.T{fill:#5a5959}</style><path d="M502 605l4 13v5l-3-11v-1 1 1h1l-1-2c0-2-1-3-1-6z" class="I"></path><path d="M399 284h1c1-1 1-2 1-3 1-3 2-6 5-8-4 7-7 14-7 22l-1 1c0-2-1-3-1-4l2-8z" class="F"></path><path d="M779 246c2 3 6 8 9 9 2 0 4-1 5-1 0 1 0 1-1 2s-3 1-5 1c-3-1-5-3-7-6-1-2-1-2-1-5z" class="I"></path><path d="M506 618c2 4 4 8 5 12 1 3 1 7 2 9v2l-1 1-6-19v-5z" class="G"></path><defs><linearGradient id="A" x1="236.726" y1="249.491" x2="237.986" y2="254.923" xlink:href="#B"><stop offset="0" stop-color="#3a3838"></stop><stop offset="1" stop-color="#515051"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M229 254h2c5 1 8-5 11-8 1-1 2-1 3-2-1 4-4 9-8 12-1 1-3 2-5 1-1 0-2-1-3-1v-2z"></path><path d="M782 208l1-1c0-1 1-1 2-2-2 6-4 12-5 18-1 3-1 8-3 11v-6c0-7 2-14 5-20z" class="H"></path><path d="M315 332c5 1 9 3 13 6 1 3 2 4 4 6 0 1 1 3 1 4l-3-4c-7-7-13-10-23-11l2-1h6z" class="E"></path><path d="M328 338c8 8 12 18 13 29h-1s0-1-1-1v4c0-8-2-14-6-22 0-1-1-3-1-4-2-2-3-3-4-6z" class="J"></path><path d="M493 577l9 28c0 3 1 4 1 6l1 2h-1v-1-1 1c-2-3-3-6-4-10l-7-22h1v-3z" class="H"></path><path d="M793 175c1-1 1 0 1-1 1-1 1-2 2-3-2 12-6 23-11 34-1 1-2 1-2 2l-1 1 11-33z" class="F"></path><path d="M298 396c2 0 8-1 9 0v1l10 2c4 1 8 3 12 5-1 1-1 2-2 2-5-2-9-4-15-5-9-3-16-3-25 2l1-1c2-3 6-5 10-6z" class="M"></path><path d="M478 531l15 46v3h-1l-11-34c-2-3-3-7-4-11 1-1 1-2 1-4z" class="K"></path><path d="M439 646c1 1 1 0 2 1 0 2 1 4 2 5h1c1 2 2 5 3 8 3 4 3 9 5 14l7 21h0l-1-1s0-1-1-1v-2c-1-1-1-1-1-2v-1h0l-1-1v-1c0-2 0 0-1-2v-2c-1 2 2 9 3 12l1 1v2l-19-51z" class="S"></path><path d="M770 331c6 1 11 1 16-2 3-2 7-7 7-11 1-2 0-4 1-6h1v2c1 5-2 12-5 16l-1 1c-3 1-5 3-8 5-12 5-29 5-42 1h0 0v-1c1 0 1 1 2 1h1s1 0 2 1l1-1-5-1c-1 0-1 0-1-1 8 2 16 4 25 3l14-3-8-1c1-1 3 0 5 0 1-1 2-1 4 0h0l-1 1h3c0-1 0-1 1-1h1l1-1h0l1-1 1-1c1 0 2-2 2-3l-2 2h-1c-3 2-8 3-12 2h-4l1-1z" class="R"></path><defs><linearGradient id="C" x1="419.78" y1="318.131" x2="398.72" y2="339.869" xlink:href="#B"><stop offset="0" stop-color="#757b79"></stop><stop offset="1" stop-color="#aaa4a6"></stop></linearGradient></defs><path fill="url(#C)" d="M397 292c0 1 1 2 1 4l1-1c2 2 3 7 3 10l7 21 21 62-1-1c0-1 0-1-1-2v-1l-3-8c0-1 0-2-1-3v-1l-4-11-1-2h-1c1 4 3 7 4 11l7 23h0l-28-83c-1-3-3-7-4-11-1-2 0-5 0-7z"></path><path d="M219 141c2 3 3 6 3 10 2 2 3 3 3 5 2 4 6 8 5 13v1c0 2 1 5 2 7 2 7 4 13 6 19h0c-1-1-1 0-2 0 2 6 10 24 9 30l-1-1v1c-2-4-3-9-4-13l-11-27-6-22c-2-8-3-15-4-23z" class="M"></path><path d="M222 151c2 2 3 3 3 5 2 4 6 8 5 13-1 2-1 3-2 5l-6-23z" class="N"></path><path d="M230 169v1c0 2 1 5 2 7 2 7 4 13 6 19h0c-1-1-1 0-2 0l-8-22c1-2 1-3 2-5z" class="R"></path><defs><linearGradient id="D" x1="692.332" y1="398.89" x2="718.616" y2="380.621" xlink:href="#B"><stop offset="0" stop-color="#cacacb"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#D)" d="M683 371l1-1c1 6 3 10 9 13 5 4 12 4 19 4 2 0 5 0 8 2 1 1 2 2 2 4 6 2 11 4 14 9 1 2 1 3 1 4l-3-5c-2-2-5-3-8-4-13-5-28-4-41-5v-2-1h0v-1h0 0v-2c-1-1 0-3 0-4-1-1-1-3-1-4v-1-2l-1-1v-3z"></path><path d="M448 237l1 1c3 0 7 0 10 1-18 2-35 10-47 25l-6 9h0c-3 2-4 5-5 8 0 1 0 2-1 3h-1c4-15 11-26 23-35-6-1-9 4-13 6 0-1-1-2-1-2l-1-1 1-1h1l1-1 5-3 2-2c1 0 3 0 4-1 4 0 9-3 13-5l3-1 11-1z" class="M"></path><defs><linearGradient id="E" x1="408.357" y1="250.163" x2="441.222" y2="241.128" xlink:href="#B"><stop offset="0" stop-color="#cbc9c9"></stop><stop offset="1" stop-color="#f4f3f6"></stop></linearGradient></defs><path fill="url(#E)" d="M448 237l1 1c-6 0-16 5-22 8-1 0-5 3-5 3-6-1-9 4-13 6 0-1-1-2-1-2l-1-1 1-1h1l1-1 5-3 2-2c1 0 3 0 4-1 4 0 9-3 13-5l3-1 11-1z"></path><defs><linearGradient id="F" x1="270.75" y1="343.222" x2="271.036" y2="330.681" xlink:href="#B"><stop offset="0" stop-color="#383737"></stop><stop offset="1" stop-color="#515051"></stop></linearGradient></defs><path fill="url(#F)" d="M229 326c5 4 7 7 13 10l2-1c7 2 13 3 21 3 1 0 2 0 3-1s3-2 5-2c3-1 5-2 8-3-3 2-5 3-7 5 5 0 10-2 14-3h2v1c2 0 4-1 6-2 6-2 12-3 19-1h-6l-2 1h0c-3 1-6 1-9 3-7 2-14 6-21 7-12 2-28 2-38-5-5-3-9-7-10-12z"></path><path d="M273 335l8-3c-3 2-5 3-7 5 5 0 10-2 14-3h2v1c-15 6-33 6-48 1l2-1c7 2 13 3 21 3 1 0 2 0 3-1s3-2 5-2z" class="P"></path><path d="M273 335l8-3c-3 2-5 3-7 5-3 1-6 1-9 1 1 0 2 0 3-1s3-2 5-2z" class="G"></path><path d="M429 393h0l-7-23c-1-4-3-7-4-11h1l1 2 4 11v1c1 1 1 2 1 3l3 8v1c1 1 1 1 1 2l1 1 48 143c0 2 0 3-1 4l-16-47 1-1h-1v-2l-1-1v-1c-1-2-1-1-1-2v-1c-1-2-1-1-1-2v-1c-1-1-1-1-1-2v-1c-2-4-2-9-4-12-1-1-1-2-1-2-1-2 0-1-1-2h0v-1l-1-1v-2l-1-1v-1l-1-3v-1c0-2-2-5-2-7-1-3-2-8-4-11s-3-7-4-10l-9-27z" class="C"></path><path d="M332 382h2v1c-1 0 0 2-1 3l-2 2c-1 1-2 3-4 5 4 0 8-1 10-2v4c1 0 1-1 2-2l1 1v3h-1v2h1c1 0 1 1 2 2h-1c-2 0-4-1-6-1-1-1-3-1-5 0l-1 1-1 1c-6-4-14-5-21-6-1-1-7 0-9 0-4 1-8 3-10 6l-1 1-3 4 1-2c3-9 9-10 16-13l-1-1c2-5 18-4 23-5 4-1 7-2 9-4z" class="R"></path><defs><linearGradient id="G" x1="319.91" y1="392.116" x2="319.461" y2="399.513" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#G)" d="M327 393c4 0 8-1 10-2v4c1 0 1-1 2-2l1 1v3h-1v2h1c1 0 1 1 2 2h-1c-2 0-4-1-6-1-1-1-3-1-5 0l-1 1-1 1c-6-4-14-5-21-6-1-1-7 0-9 0 10-3 19-3 29-3z"></path><path d="M339 399c-2 0-4-1-6-1-2-1-5 0-7-1h4 0c-2 0-4 0-6-1h12c0 1 1 1 2 1h1v2z" class="I"></path><defs><linearGradient id="H" x1="456.682" y1="735.193" x2="489.525" y2="733.171" xlink:href="#B"><stop offset="0" stop-color="#b1b0af"></stop><stop offset="1" stop-color="#e1e0e2"></stop></linearGradient></defs><path fill="url(#H)" d="M458 697v-2l-1-1c-1-3-4-10-3-12v2c1 2 1 0 1 2v1l1 1h0v1c0 1 0 1 1 2v2c1 0 1 1 1 1l1 1h0l7 14h1l27 73c-1 1-1 1-1 2-1 1-2 2-2 3 0 3 3 8 3 11 1 2 2 3 2 5-2-4-4-9-6-14l-10-30-22-62z"></path><defs><linearGradient id="I" x1="303.041" y1="297.337" x2="233.161" y2="330.048" xlink:href="#B"><stop offset="0" stop-color="#bcbbbc"></stop><stop offset="1" stop-color="#eeecee"></stop></linearGradient></defs><path fill="url(#I)" d="M297 277c0 2 0 4 1 6 1 6-1 10-1 15 2-2 1-5 2-6v3c-1 1-1 2-1 3v2l-1 1v1h1c-3 11-11 19-21 25-8 4-17 6-26 7-2 1-5 0-7 1l-2 1c-6-3-8-6-13-10-1-3-2-6-2-9 0-1-1-4 1-5 1 0 0 0 0 2 1 4 2 8 5 12 2 3 7 5 10 6 12 0 25-5 34-13 11-9 18-21 19-35 0-3 0-5 1-7z"></path><defs><linearGradient id="J" x1="282.986" y1="331.798" x2="266.867" y2="270.139" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#403f3f"></stop></linearGradient></defs><path fill="url(#J)" d="M297 265c0-2 1-2 0-4 2 0 3 3 4 4v4 1c0 4 1 8 2 13 0 5 0 9-1 14 0 4-1 7-3 10 0 2-2 4-2 6l-1 4c-1 1-1 1-1 2l-1 1v2h1 1c1 0 3 0 5-1 1 1 3 1 4 2h1c-5 1-10 2-15 4-3 2-7 4-10 5s-5 2-8 3c-2 0-4 1-5 2s-2 1-3 1c-8 0-14-1-21-3 2-1 5 0 7-1 9-1 18-3 26-7 10-6 18-14 21-25 4-12 2-26-1-37z"></path><defs><linearGradient id="K" x1="291.035" y1="332.076" x2="290.156" y2="318.72" xlink:href="#B"><stop offset="0" stop-color="#232223"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#K)" d="M297 313l-1 4c-1 1-1 1-1 2l-1 1v2h1 1c1 0 3 0 5-1 1 1 3 1 4 2h1c-5 1-10 2-15 4-3 2-7 4-10 5s-5 2-8 3c2-2 5-4 8-5 1-2 2-3 4-5 5-4 8-7 12-12z"></path><defs><linearGradient id="L" x1="750.746" y1="315.377" x2="723.042" y2="365.004" xlink:href="#B"><stop offset="0" stop-color="#393737"></stop><stop offset="1" stop-color="#5a595a"></stop></linearGradient></defs><path fill="url(#L)" d="M685 345a34.47 34.47 0 0 1 12-12c8-5 17-6 26-3l12 3c1 1 3 1 4 2 0 1 0 1 1 1l5 1-1 1c-1-1-2-1-2-1h-1c-1 0-1-1-2-1v1h0 0c13 4 30 4 42-1 3-2 5-4 8-5l1-1c-3 6-9 9-14 11-12 5-28 4-40-1-9-3-18-9-29-6v1c-8 2-15 10-19 16-2 4-3 8-4 12v7l-1 1h-1v-9c1-5 3-10 5-15h0c-2 1-2 2-2 4-1 1-1 2-2 4v-4c0-1 1-2 1-4 0-1 1-2 1-2z"></path><path d="M685 345a34.47 34.47 0 0 1 12-12c8-5 17-6 26-3l12 3c1 1 3 1 4 2 0 1 0 1 1 1l5 1-1 1c-1-1-2-1-2-1h-1c-1 0-1-1-2-1v1h0c-14-4-25-10-39-2-5 2-9 7-13 12h0c-2 1-2 2-2 4-1 1-1 2-2 4v-4c0-1 1-2 1-4 0-1 1-2 1-2zM238 196c5 8 8 18 10 27l3 3v1c1 3 1 7 3 8 1 1 2 1 3 2h1c4 0 8 0 11 1l3 1h0c7 3 11 6 17 11 3 5 5 10 8 15 3 11 5 25 1 37h-1v-1l1-1v-2c0-1 0-2 1-3v-3c-1 1 0 4-2 6 0-5 2-9 1-15-1-2-1-4-1-6-1 2-1 4-1 7-2-10-5-18-11-26-2-3-5-5-7-8-4-3-9-6-14-7-2-1-5-2-8-2-4 0-7 1-11 3-1 1-2 1-3 2-3 3-6 9-11 8h-2l-1-1 1-1h3c3 0 5-2 7-5 6-6 5-14 5-21v-1l1 1c1-6-7-24-9-30 1 0 1-1 2 0h0z" class="N"></path><path d="M248 223l3 3v1c1 3 1 7 3 8 1 1 2 1 3 2h1l-8 1-2-15z" class="B"></path><path d="M256 241c2 0 5-1 7-1 5 1 12 4 15 7h1c-1 2-1 2-1 3-4-3-9-6-14-7-2-1-5-2-8-2z" class="E"></path><defs><linearGradient id="M" x1="274.31" y1="252.777" x2="297.845" y2="268.491" xlink:href="#B"><stop offset="0" stop-color="#3d3e3d"></stop><stop offset="1" stop-color="#585657"></stop></linearGradient></defs><path fill="url(#M)" d="M279 247c10 8 15 18 18 30-1 2-1 4-1 7-2-10-5-18-11-26-2-3-5-5-7-8 0-1 0-1 1-3z"></path><defs><linearGradient id="N" x1="569.225" y1="750.181" x2="517.994" y2="747.275" xlink:href="#B"><stop offset="0" stop-color="#9fa5a8"></stop><stop offset="1" stop-color="#f6eeec"></stop></linearGradient></defs><path fill="url(#N)" d="M574 663c3-1 6-15 8-18h1l-66 186-1-1-1 3c-1-2-1-3-1-4v-2-3c1-5 4-9 4-14 0-1 13-38 15-42l17-47c3-9 5-19 10-28h0 1v2 1c-1 1-1 1-1 3 2-7 5-12 7-19 2-5 4-12 7-17z"></path><path d="M314 323c3 0 6-1 8-2l1 1v1h2c2 1 5 3 7 4l3 1c1 0 1 0 1-1h1l1-1c0 2 1 4 2 6 0 2 1 3 2 5l-1 1c1 1 1 2 1 4-1 0-1 0-2 1 0 1 1 2 1 3 1 3 3 6 4 10 1 9 1 20-1 30l-1 2c0 1-1 2-2 3 0 1 0 2-1 3h0l-1-1c-1 1-1 2-2 2v-4c-2 1-6 2-10 2 2-2 3-4 4-5l2-2c1-1 0-3 1-3v-1h-2c4-4 5-7 7-12v-4c1 0 1 1 1 1h1c-1-11-5-21-13-29-4-3-8-5-13-6-7-2-13-1-19 1-2 1-4 2-6 2v-1h-2c-4 1-9 3-14 3 2-2 4-3 7-5 3-1 7-3 10-5 5-2 10-3 15-4l6-1h2 0v1z" class="N"></path><path d="M339 370v-4c1 0 1 1 1 1h1v4c-1 2 0 4-1 7l-1 4v2c0 1-1 2-1 3v3l-1 1c-2 1-6 2-10 2 2-2 3-4 4-5l2-2c1-1 0-3 1-3v-1h-2c4-4 5-7 7-12z" class="C"></path><defs><linearGradient id="O" x1="348.645" y1="370.847" x2="332.049" y2="364.901" xlink:href="#B"><stop offset="0" stop-color="#211f22"></stop><stop offset="1" stop-color="#474846"></stop></linearGradient></defs><path fill="url(#O)" d="M338 341c1 2 2 4 3 5 1 3 3 6 4 10 1 9 1 20-1 30l-1 2c0 1-1 2-2 3 0 1 0 2-1 3h0l-1-1 3-9c3-16 2-29-7-42h1c1 1 2 2 2 3h1 0l-1-3h-1l1-1z"></path><defs><linearGradient id="P" x1="311.982" y1="323.552" x2="315.906" y2="348.547" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#P)" d="M314 323c3 0 6-1 8-2l1 1v1h2c2 1 5 3 7 4l3 1c1 0 1 0 1-1h1l1-1c0 2 1 4 2 6 0 2 1 3 2 5l-1 1c1 1 1 2 1 4-1 0-1 0-2 1 0 1 1 2 1 3-1-1-2-3-3-5l-1 1h1l1 3h0-1c0-1-1-2-2-3h-1c-3-4-7-7-11-10-4-2-8-3-12-4-8-1-16 3-24 6h0c-4 1-9 3-14 3 2-2 4-3 7-5 3-1 7-3 10-5 5-2 10-3 15-4l6-1h2 0v1z"></path><defs><linearGradient id="Q" x1="278.509" y1="337.464" x2="295.435" y2="324.463" xlink:href="#B"><stop offset="0" stop-color="#413f40"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#Q)" d="M312 322h2 0v1c-8 2-19 4-26 10v1h0c-4 1-9 3-14 3 2-2 4-3 7-5 3-1 7-3 10-5 5-2 10-3 15-4l6-1z"></path><path d="M323 327c-1 0-2 0-3-1v-1h3-1l1-1h0v-1h2c2 1 5 3 7 4l3 1c1 0 1 0 1-1h1l1-1c0 2 1 4 2 6 0 2 1 3 2 5l-1 1c1 1 1 2 1 4-1 0-1 0-2 1 0 1 1 2 1 3-1-1-2-3-3-5l-4-4c-2-2-2-4-4-6s-5-3-7-4z" class="M"></path><path d="M336 327h1l1-1c0 2 1 4 2 6 0 2 1 3 2 5l-1 1c-1-1-1-2-2-3-1-2-3-3-5-5-1-1-2-2-2-3l3 1c1 0 1 0 1-1z" class="E"></path><path d="M336 327h1l1-1c0 2 1 4 2 6h-1l-1-1-3-3c1 0 1 0 1-1z" class="H"></path><path d="M323 327c2 0 4 1 6 2 4 3 8 7 10 12l1 2c0 1 1 2 1 3-1-1-2-3-3-5l-4-4c-2-2-2-4-4-6s-5-3-7-4z" class="F"></path><path d="M219 141l-1-16c3 8 4 17 8 25 11 28 33 51 62 63 17 7 34 9 53 5 4-1 8-3 12-4h1c4 1 12-6 15-8 2-1 4-3 6-6h0c-2 4-6 10-9 12h-1c-1 1-2 1-3 2-1 0-1 0-2 1h0l-1 1c-5 4-12 6-18 7l-12 2c-8 1-17 2-25 1l-16-3c-1-1-3-2-5-2-14-6-27-18-37-30-4-3-8-7-10-11-2-3-3-7-6-10v-1c1-5-3-9-5-13 0-2-1-3-3-5 0-4-1-7-3-10z" class="P"></path><defs><linearGradient id="R" x1="268.479" y1="211.872" x2="276.101" y2="203.178" xlink:href="#B"><stop offset="0" stop-color="#bbbaba"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#R)" d="M246 191c1 0 2 0 3 1 4 2 7 5 10 7 5 4 11 8 17 11 2 2 4 4 7 5 3 2 8 4 12 5 2 1 4 1 5 2h1 0 2l1 1h0c2-1 2 0 4 0h1l10 1h-5c-2-1-6-1-9-1h-1c-1 0-1 0-2 1l-1 1h3v1l-16-3c-1-1-3-2-5-2-14-6-27-18-37-30z"></path><path d="M283 221l1-1c3 0 8 1 9 2-1 1-2 0-3 0l-2 1c-1-1-3-2-5-2z" class="C"></path><path d="M353 214c9-4 21-10 24-20 2-4 1-8 1-12h0c3 5 3 10 2 15-1 3-2 7-2 10 3 1 7 1 9 2-6 2-12 4-15 9-1 3-2 6-1 10l4 4c6 2 15 0 20 0 23-1 72-6 86 17-3-1-5-3-7-5-1 0-2-1-3-1-4-2-8-3-12-4-3-1-7-1-10-1l-1-1-11 1h-3l-13 1c-1 0-4-1-5 0l-24 1c-7 0-14 2-21 0-3-3-5-6-6-9v-1c0-3 0-7 1-10 1-2 3-5 2-7-4 6-16 9-23 11-2 0-3 0-4-1 6-1 13-3 18-7l1-1h0c1-1 1-1 2-1 1-1 2-1 3-2h1c3-2 7-8 9-12h0c-2 3-4 5-6 6-3 2-11 9-15 8h-1z" class="N"></path><path d="M368 231c3 0 4 2 6 3s5 1 7 3h-4c-5-1-7-2-9-6z" class="C"></path><path d="M455 236c7 1 14 3 19 8-1 0-2-1-3-1-4-2-8-3-12-4-3-1-7-1-10-1l-1-1c3-1 5 0 7-1z" class="T"></path><path d="M359 216c7-2 12-8 17-14-2 4-3 7-5 11h-1s-1 0-1-1l-1 1c-4 6-16 9-23 11-2 0-3 0-4-1 6-1 13-3 18-7z" class="B"></path><path d="M368 213l1-1c0 1 1 1 1 1-2 6-5 11-3 17 1 1 1 0 1 1 2 4 4 5 9 6 1 0 2 1 4 1 2 2 10 2 13 2h-2 0c-7 0-14 2-21 0-3-3-5-6-6-9v-1c0-3 0-7 1-10 1-2 3-5 2-7z" class="I"></path><defs><linearGradient id="S" x1="421.443" y1="222.907" x2="408.557" y2="250.593" xlink:href="#B"><stop offset="0" stop-color="#322b2e"></stop><stop offset="1" stop-color="#545855"></stop></linearGradient></defs><path fill="url(#S)" d="M381 237c7 0 14 0 20-1 18-1 36-3 54 0-2 1-4 0-7 1l-11 1h-3l-13 1c-1 0-4-1-5 0l-24 1h0 2c-3 0-11 0-13-2-2 0-3-1-4-1h4z"></path><defs><linearGradient id="T" x1="355.325" y1="501.747" x2="396.76" y2="492.117" xlink:href="#B"><stop offset="0" stop-color="#545453"></stop><stop offset="1" stop-color="#838283"></stop></linearGradient></defs><path fill="url(#T)" d="M307 396c7 1 15 2 21 6h2l9 6 4 4 8 7c3 4 6 7 8 10l7 12c7 11 12 24 18 36l31 89h-1l6 20h-1v-1 4l1 1-1 1c-1-1-1-3-1-4l-5-13-24-65c-6-17-13-34-21-52-4-9-8-18-14-26-7-10-17-18-27-25 1 0 1-1 2-2-4-2-8-4-12-5l-10-2v-1z"></path><path d="M307 396c7 1 15 2 21 6h2l9 6 4 4 8 7c3 4 6 7 8 10-1 2-1 2 0 4-2-2-4-5-6-7-7-9-15-16-24-22-4-2-8-4-12-5l-10-2v-1z" class="N"></path><defs><linearGradient id="U" x1="393.17" y1="497.988" x2="383.47" y2="500.969" xlink:href="#B"><stop offset="0" stop-color="#c5c4c5"></stop><stop offset="1" stop-color="#e8e7e8"></stop></linearGradient></defs><path fill="url(#U)" d="M359 429l7 12c7 11 12 24 18 36l31 89h-1l-12-31-16-45c-4-13-10-25-16-36-3-8-7-15-11-21-1-2-1-2 0-4z"></path><defs><linearGradient id="V" x1="330.312" y1="413.561" x2="370.52" y2="414.187" xlink:href="#B"><stop offset="0" stop-color="#3f3d3e"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#V)" d="M353 386h1l4-2v1h1c2 0 4 0 7 1 1 1 1 2 2 4 0 1 0 1 1 2h0c1 1 1 4 1 5 1 8 2 16 1 23l-3 12c0 2-1 5 0 7h1l1 2 1 3c1 2 3 3 5 5v1c2 2 2 4 3 7l3 9 4 10c-1 1-1 1-2 0v1c-6-12-11-25-18-36l-7-12c-2-3-5-6-8-10l-8-7-4-4-9-6h-2l1-1 1-1c2-1 4-1 5 0 2 0 4 1 6 1h1c-1-1-1-2-2-2h-1v-2h1l1-1c2 0 3-2 4-4s6-5 8-6z"></path><path d="M340 399c2 0 3 0 4 1v2c0 1 1 1 1 2h-1c-1-1-2-1-3-1l-7-2-1-1c-1 1-2 1-3 2h-2l1-1 1-1c2-1 4-1 5 0 2 0 4 1 6 1h1c-1-1-1-2-2-2zm3 13l1-1 1 1c1-1 1 0 1-1l1-1c2 2 4 5 6 7h0c0 2 0 2 1 3v1l-2-2h-1l-8-7z" class="I"></path><path d="M354 392l2-3h0c0 1-1 1-1 2 1 2 3 2 4 3 1-1 2-1 4-1-1 1 0 1-1 2h-3c0 1 0 1 1 1 3 1 5-1 7-2 1 0 1 0 1-1v2c-2 2-4 3-6 4-2 0-4 0-5-1-2-2-3-4-3-6z" class="B"></path><defs><linearGradient id="W" x1="355.028" y1="427.085" x2="361.523" y2="426.41" xlink:href="#B"><stop offset="0" stop-color="#464748"></stop><stop offset="1" stop-color="#625f60"></stop></linearGradient></defs><path fill="url(#W)" d="M353 417c4 4 7 10 11 14 0 1 0 2-1 3l1 1s0 1 1 2v1l2 2-1 1-7-12c-2-3-5-6-8-10h1l2 2v-1c-1-1-1-1-1-3h0z"></path><path d="M353 386h1l4-2v1h1c2 0 4 0 7 1 1 1 1 2 2 4 0 1 0 1 1 2l-1 1c0 1 0 1-1 1-2 1-4 3-7 2-1 0-1 0-1-1h3c1-1 0-1 1-2-2 0-3 0-4 1-1-1-3-1-4-3 0-1 1-1 1-2h0l-2 3-2-2c0-2 1-2 1-4z"></path><path d="M353 386h1l4-2v1h1c2 0 4 0 7 1 1 1 1 2 2 4-3-3-5-4-8-4-1 1-2 1-3 2l1 2h0l1 2c1 0 1-1 2-2l2 2v1c-2 0-3 0-4 1-1-1-3-1-4-3 0-1 1-1 1-2h0l-2 3-2-2c0-2 1-2 1-4z" class="J"></path><path d="M367 440v-6h-1c-2-5-3-10-6-14v-1-1c3 4 5 10 8 13v1c0 2-1 5 0 7h1l1 2 1 3c1 2 3 3 5 5v1c2 2 2 4 3 7l3 9 4 10c-1 1-1 1-2 0v1c-6-12-11-25-18-36l1-1z" class="M"></path><defs><linearGradient id="X" x1="273.909" y1="167.635" x2="287.766" y2="236.229" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2e2c2d"></stop></linearGradient></defs><path fill="url(#X)" d="M230 170c3 3 4 7 6 10 2 4 6 8 10 11 10 12 23 24 37 30 2 0 4 1 5 2 6 1 11 2 16 3 8 1 17 0 25-1 0 1 1 2 1 3l-1 1c-6 1-12 1-19 2-3 0-7 0-9 1v1l-3 2-3 1-1 1c-3 0-8 1-10-1-2 0-3 0-4 1-3 1-6 0-8 0v2l-3-1c-3-1-7-1-11-1h-1c-1-1-2-1-3-2-2-1-2-5-3-8v-1l-3-3c-2-9-5-19-10-27-2-6-4-12-6-19-1-2-2-5-2-7z"></path><path d="M243 199l-3-8 6 6c1 2 3 4 5 6-3-1-5-3-8-4z" class="B"></path><path d="M301 232v1l-3 2-3 1-1 1c-3 0-8 1-10-1h3l14-4z" class="C"></path><path d="M270 220c-1-1-2-1-2-2h1c8 6 17 8 27 11 3 0 6 0 9 1-2 1-6 2-8 2l-9 2c-4 1-8-1-11-4s-3-8-7-10z" class="Q"></path><path d="M296 229c3 0 6 0 9 1-2 1-6 2-8 2-5 0-11 1-15-1h2 0l-1-1h0c4-1 8 0 12 0l1-1z" class="O"></path><defs><linearGradient id="Y" x1="252.674" y1="203.531" x2="248.6" y2="212.963" xlink:href="#B"><stop offset="0" stop-color="#2c2b2b"></stop><stop offset="1" stop-color="#4c4a4a"></stop></linearGradient></defs><path fill="url(#Y)" d="M243 199c3 1 5 3 8 4l7 7c1 1 3 2 5 3 2 2 4 3 6 5h0-1c0 1 1 1 2 2v4l-4-3v-1h-2-4c-2 0-2 1-4 2-1 0-2 1-2 2-2 0-2 1-3 3h0v-1l-3-14-5-13z"></path><path d="M248 212c1 1 2 2 3 2 1 2 1 3 2 5h3v1 2c-1 0-2 1-2 2-2 0-2 1-3 3h0v-1l-3-14z" class="M"></path><defs><linearGradient id="Z" x1="254.639" y1="235.318" x2="272.293" y2="225.049" xlink:href="#B"><stop offset="0" stop-color="#515050"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#Z)" d="M264 220h2v1l4 3v-4c4 2 4 7 7 10l-1 1 6 3c0 1 1 1 2 1 1 1 1 1 3 1h-3c-2 0-3 0-4 1-3 1-6 0-8 0v2l-3-1c-3-1-7-1-11-1h-1c-1-1-2-1-3-2-2-1-2-5-3-8h0c1-2 1-3 3-3 0-1 1-2 2-2 2-1 2-2 4-2h4z"></path><path d="M264 220h2v1l4 3v-4c4 2 4 7 7 10l-1 1-3-5v1c-1 2-3 4-6 5-1 1-2 2-4 2s-4-1-5-2c-2-2-2-4-2-6 0-3 2-4 4-6h4z" class="I"></path><path d="M260 220h4c0 2-1 2-2 3h-1l-1-1h-1c-1 2-1 4-1 6s1 3 3 4h4 2c-1 1-2 2-4 2s-4-1-5-2c-2-2-2-4-2-6 0-3 2-4 4-6zm6 1l4 3h0v2l-3 3c-2 1-4 1-6 0-1-1-1-1-1-2 2-2 2-2 5-2v1l1-1h1c-1-1-1-1-1-2s0-1-1-2h1z" class="B"></path><path d="M790 175l1 1c1-1 1-4 1-5l1 2c0-1 0 0 1-1l-1 3-11 33c-3 6-5 13-5 20v6c2 6 4 13 9 17 2 2 5 2 7 1v2c-1 0-3 1-5 1-3-1-7-6-9-9l-1-1c-4-3-10-4-14-4-13 3-24 10-30 22-3 4-6 9-7 14v10c1 13 9 25 19 33 7 6 16 10 24 11h0l-1 1h4c4 1 9 0 12-2h1l2-2c0 1-1 3-2 3l-1 1-1 1h0l-1 1h-1c-1 0-1 0-1 1h-3l1-1h0c-2-1-3-1-4 0-2 0-4-1-5 0l8 1-14 3-2-2h-3c-3-1-7-1-10-2h-1c-1-1-2-1-3-2-1 0-1 0-2-1h0 0 1 3l-1-1c-7-2-13-7-18-14-6-8-9-19-8-28 0-5 1-9 1-13 1-2 2-5 2-7 0-1 0-3 1-4 0-1 0-2 1-3l1-2c1-2 3-6 5-6h0l7-8h0c1-1 0-1 1-1 2-1 3-2 4-4h-2v-1h1l1-1h3l1-1v1h5c5-1 10-1 16-1 1 0 3 1 5 1v-8l2-11 11-32 4-12z" class="N"></path><path d="M742 239l1-1h3l1-1v1h5l-14 7h0c1-1 0-1 1-1 2-1 3-2 4-4h-2v-1h1z" class="K"></path><path d="M727 287c0-1 0-2-1-3v-2c-2-5 1-15 4-19 5-9 15-18 25-21l3-2c2 0 4 0 6 1-13 3-24 10-30 22-3 4-6 9-7 14v10z" class="J"></path><path d="M726 259c1-2 3-6 5-6-8 11-10 30-8 43 2 12 9 21 19 28 6 5 14 8 22 9 2 0 4 1 6 1l8 1-14 3-2-2h-3c-3-1-7-1-10-2h-1c-1-1-2-1-3-2-1 0-1 0-2-1h0 0 1 3l-1-1c-7-2-13-7-18-14-6-8-9-19-8-28 0-5 1-9 1-13 1-2 2-5 2-7 0-1 0-3 1-4 0-1 0-2 1-3l1-2z" class="B"></path><defs><linearGradient id="a" x1="378.454" y1="227.73" x2="345.637" y2="264.124" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#a)" d="M368 213c1 2-1 5-2 7-1 3-1 7-1 10v1c1 3 3 6 6 9 7 2 14 0 21 0l24-1c1-1 4 0 5 0l13-1h3l-3 1c-4 2-9 5-13 5-1 1-3 1-4 1l-2 2-5 3-1 1h-1c0-1 1-2 3-3l1-1 2-2h0-1v-1h-1-2c-1 0-2 0-3 1h-1c-1 1-1 2-2 3-4 2-6 5-9 7 0 2 0 2-1 4-1 0-1 0-1 1-2 5-4 11-9 14-6 3-13 3-19 3-4-1-7-3-10-6l-2-3v-1c-1-2-1-4-1-7v1h-1c0-1 0 0-1-1v-4-4h-1c-2 4-3 8-2 13s3 10 7 13l-3 1c0-1 0-2-1-2-1-2-4-5-5-7-2-4-2-8-3-12l-1-1-2-3h0l-8-26c-1 1-1 1-2 1l1-1c0-1-1-2-1-3l12-2c1 1 2 1 4 1 7-2 19-5 23-11z"></path><path d="M329 225l12-2c1 1 2 1 4 1l-9 3c-2 0-4 0-5 1s-1 1-2 1l1-1c0-1-1-2-1-3z" class="I"></path><path d="M341 257h1v-6l1-1v-1-1c1-1 1-2 1-2l2-4 2-2v1c0 1 0 1-1 2l-1 2c-1 1-1 2-2 3v1c-1 1-2 5-1 7v1c1 1 0 3 1 4 0 2 1 3 1 4h2c1 5 3 10 7 13l-3 1c0-1 0-2-1-2-1-2-4-5-5-7-2-4-2-8-3-12l-1-1z" class="N"></path><defs><linearGradient id="b" x1="371.349" y1="267.542" x2="367.801" y2="250.189" xlink:href="#B"><stop offset="0" stop-color="#393837"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#b)" d="M383 261c5-3 10-8 15-13 3-2 5-5 8-6 5-2 10 1 15 2-1 1-3 1-4 1-1-1-3-1-5-2-3 0-4 0-6 1-6 4-12 9-16 14-7 6-13 12-23 10-4-1-7-4-9-7-2-4-3-8-2-12 1-2 2-4 3-5h2c0 1-1 1-1 2-2 2-2 4-2 7 2 5 5 10 10 12 3 2 7 0 11-1 1-1 3-2 4-3z"></path><path d="M434 238h3l-3 1c-4 2-9 5-13 5-5-1-10-4-15-2-3 1-5 4-8 6-5 5-10 10-15 13h-1l1-1v-1l-3 1c0-1-1-1-1-1h-1c-1 0-1 0-1-1l3-2h-4c-1-1-3-2-4-4 0-2 0-4 1-7h-1l2-2h1l1-1h1-1c-1 0-2 0-3-1l-3-1h1c7 2 14 0 21 0l24-1c1-1 4 0 5 0l13-1z" class="P"></path><path d="M416 239c1-1 4 0 5 0l13-1c-6 2-11 4-17 2l-1-1z" class="C"></path><path d="M373 245c1-1 3-2 4-2 2 0 4 0 6 1 1 1 2 3 2 5v2c-1 3-3 4-5 5h0-4c-1-1-3-2-4-4 0-2 0-4 1-7h0z" class="G"></path><path d="M373 245c1-1 3-2 4-2 2 0 4 0 6 1 1 1 2 3 2 5v2h-1-3v-1l1-1-1-1c-1 0-1 1-1 2v1h-3c-1-1-1-2-2-3v-1c-1 0-1-1-2-2z" class="B"></path><path d="M651 404c0-4 1-13 3-17 1-1 2-2 4-2h2c1-1 3 1 5 0h0l2 1h1 2l11 7v1h2 1l1-2c13 1 28 0 41 5 3 1 6 2 8 4-3 0-5-1-8-2h-10l-10 2-1 1c-5 1-10 4-15 7-9 7-17 15-24 25-8 12-13 25-19 38l-17 45-22 59-8 22c-1 4-3 9-5 13-1 2-11 33-12 34h-1c-2 3-5 17-8 18-3 5-5 12-7 17-2 7-5 12-7 19 0-2 0-2 1-3v-1-2h-1 0l32-89c-1-2-1-4 0-7l1-4c3-7 4-15 8-22l17-48 6-16 13-36h1c2-2 2-4 5-5 1-2 1-4 2-5 1-4 2-7 4-10l2-2 5-10-2-1c0-1 0-2 1-3 0-5-2-8-3-13-1-6-1-12-1-18z" class="C"></path><defs><linearGradient id="c" x1="602.22" y1="591.607" x2="591.78" y2="578.893" xlink:href="#B"><stop offset="0" stop-color="#252a2d"></stop><stop offset="1" stop-color="#494342"></stop></linearGradient></defs><path fill="url(#c)" d="M601 571l1-1s0-1 1-1c0 1 0 2 1 3l-12 32c-1-2-1-4 0-7l1-4c3-7 4-15 8-22z"></path><path d="M667 419c2-1 5-6 8-6l1-1c1 0 1-1 3-1-9 9-17 18-23 28l-2-1c0-1 0-2 1-3 2-1 3-3 4-5s2-3 3-4c1-3 3-5 5-7z" class="E"></path><path d="M704 399l3-1c3 0 6 0 9 1l-10 2-1 1c-5 1-10 4-15 7-1 0-2 1-3 1l-1 2-1-1v-1l-5 5c-1 1 0 0-2 1l-2 2h-1 0c2-2 5-4 7-6 6-6 14-11 22-13z" class="F"></path><path d="M593 610h1s1-1 1-2v2 1c-1 2-11 33-12 34h-1c-2 3-5 17-8 18l10-27c2-6 4-13 6-19 1-2 2-5 3-7z" class="D"></path><defs><linearGradient id="d" x1="603.918" y1="538.257" x2="617.582" y2="554.743" xlink:href="#B"><stop offset="0" stop-color="#1f1c1e"></stop><stop offset="1" stop-color="#363936"></stop></linearGradient></defs><path fill="url(#d)" d="M618 523c1 2 0 2 0 4l1-1h0v-1c1-1 0-1 1-1h0l-16 48c-1-1-1-2-1-3-1 0-1 1-1 1l-1 1 17-48z"></path><path d="M638 471c2-2 2-4 5-5l-23 58h0c-1 0 0 0-1 1v1h0l-1 1c0-2 1-2 0-4l6-16 13-36h1z" class="G"></path><defs><linearGradient id="e" x1="689.814" y1="392.241" x2="692.795" y2="409.194" xlink:href="#B"><stop offset="0" stop-color="#1d1b1c"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#e)" d="M685 392c13 1 28 0 41 5 3 1 6 2 8 4-3 0-5-1-8-2h-10c-3-1-6-1-9-1l-3 1v-1c-10 3-17 7-25 13-2 0-2 1-3 1l-1 1c-3 0-6 5-8 6-2 2-4 4-5 7-1 1-2 2-3 4 0-3 2-6 3-8 2-3 6-7 6-10l1-1c-1-1-1 0-2-1 0-1 1-2 1-3h1c1 0 2-1 3-1l2 1 4-4v-1c0 1 1 0 1 0h0l-3-3 1-1c1 1 2 1 3 1s1 0 2-1c0-2 0-2-1-4h2 1l1-2z"></path><path d="M679 402l4-2h2c2 0 5-2 7-3h3 0c0 1-2 1-3 2-4 2-8 5-12 6h0v-1c-2 1-4 2-6 4v-1l4-4v-1c0 1 1 0 1 0h0z" class="H"></path><path d="M704 398c3-1 6-1 8-2h8c2 0 3 1 6 1 3 1 6 2 8 4-3 0-5-1-8-2h-10c-3-1-6-1-9-1l-3 1v-1z" class="Q"></path><path d="M674 407v1c2-2 4-3 6-4v1c-2 1-12 11-13 14h0c-2 2-4 4-5 7-1 1-2 2-3 4 0-3 2-6 3-8 2-3 6-7 6-10l1-1c-1-1-1 0-2-1 0-1 1-2 1-3h1c1 0 2-1 3-1l2 1z" class="J"></path><path d="M675 418h1l2-2c2-1 1 0 2-1l5-5v1l1 1 1-2c1 0 2-1 3-1-9 7-17 15-24 25-8 12-13 25-19 38l-17 45-22 59-8 22c-1 4-3 9-5 13v-1-2c0 1-1 2-1 2h-1l10-28 6-20 12-32 17-44c3-8 6-17 10-25l6-13c6-11 12-22 21-30z" class="Q"></path><defs><linearGradient id="f" x1="674.179" y1="388.859" x2="651.817" y2="413.103" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#7b797a"></stop></linearGradient></defs><path fill="url(#f)" d="M651 404c0-4 1-13 3-17 1-1 2-2 4-2h2c1-1 3 1 5 0h0l2 1h1 2l11 7v1c1 2 1 2 1 4-1 1-1 1-2 1s-2 0-3-1l-1 1 3 3h0s-1 1-1 0v1l-4 4-2-1c-1 0-2 1-3 1h-1c0 1-1 2-1 3 1 1 1 0 2 1l-1 1c0 3-4 7-6 10-1 2-3 5-3 8-1 2-2 4-4 5 0-5-2-8-3-13-1-6-1-12-1-18z"></path><path d="M658 386c2 0 3 0 4 1 2 1 3 1 4 3l-1 1c-1 2-2 2-3 2l-2 1c-1 0-2-1-3-1h-1c1 1 2 2 3 2 3 1 3 1 5-1 2-1 2-2 3-3v-2h1v3c0 2-1 3-3 4-2 2-5 2-8 2-2-1-3-1-4-3 0-2 0-4 1-5 1-2 2-3 4-4z"></path><path d="M651 404v1c1 1 1 1 1 2s1 2 1 2c1 3 2 6 2 9 1 2 0 6 2 8 1-1 1-1 1-3 2-2 2-5 5-6 1-1 2-1 2-2l1-4c1 0 1 0 2 1 0 3-4 7-6 10-1 2-3 5-3 8-1 2-2 4-4 5 0-5-2-8-3-13-1-6-1-12-1-18z" class="D"></path><defs><linearGradient id="g" x1="288.797" y1="318.798" x2="336.583" y2="233.697" xlink:href="#B"><stop offset="0" stop-color="#504e4f"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#g)" d="M329 229c1 0 1 0 2-1l8 26h0l2 3 1 1c1 4 1 8 3 12 1 2 4 5 5 7h0c-3-1-8-3-11-1-2 2-3 5-6 6-1-1-2-1-3-2l-2 3h-6l1 2 1-1c5 1 10 5 13 8 1 3 2 6 2 9-2 8-9 17-16 21l-1-1c-2 1-5 2-8 2v-1h0-2l-6 1h-1c-1-1-3-1-4-2-2 1-4 1-5 1h-1-1v-2l1-1c0-1 0-1 1-2l1-4c0-2 2-4 2-6 2-3 3-6 3-10 1-5 1-9 1-14-1-5-2-9-2-13v-1-4c-1-1-2-4-4-4 1 2 0 2 0 4-3-5-5-10-8-15-6-5-10-8-17-11h0v-2c2 0 5 1 8 0 1-1 2-1 4-1 2 2 7 1 10 1l1-1 3-1 3-2v-1c2-1 6-1 9-1 7-1 13-1 19-2z"></path><path d="M301 321c3 0 8-1 11 0v1l-6 1h-1c-1-1-3-1-4-2z" class="E"></path><path d="M307 311l-1-1c1-1 1-1 2-1s2 1 2 1l1 1c1 0 1 0 1 1v1l-1 1-1 1-1-1-1-1c0-1-1-1-1-2z" class="H"></path><path d="M315 254v-3h0c1-3 1-5 4-8 2 2 2 3 2 6 0 1 0 2-1 3v3h-1c0-1-1-1-1-2h-1l-2 1z" class="D"></path><path d="M295 236l3-1h4c2 1 4 1 6 2v1c0 1 0 1 1 2 1 2 0 5-1 7v1c0 1-1 2-3 3h-1c1-1 1-2 1-3 1-1 2-3 2-4 0-2-1-4-2-5-3-3-6-3-10-3z" class="O"></path><path d="M329 279c-3-2-5 0-8 0l-1-1c-1-1-2 0-4-1 1-1 2-1 4-1 0 0 1 0 1-1h1c2-1 3-1 4-3 0-1 1-2 2-3 0 1 0 3-1 4 0 1-1 1-1 2 2 1 4 0 6 0l2-1v1h1c1-1 2-1 4-1-1 1 0 1-1 1-2 1-3 3-5 4-1 1-2 1-3 0h-1z" class="F"></path><path d="M317 303c3 0 4 0 6 2 1 1 2 2 2 4v3c-3 2-5 4-8 3-2 0-4-2-5-3 0-1 0-1 1-2l1 1c1 0 2 0 3-1h-2l-1-1h4v-1l1-2c-2 0-2-1-3-1-2 1-1 1-3 1l2-2 2-1h0z" class="B"></path><path d="M315 304l2-1c3 1 4 2 6 4v3l-1 1h-1v-1l-2-4c-2 0-2-1-3-1-2 1-1 1-3 1l2-2z" class="L"></path><path d="M328 269l1-1c0-2 1-5 2-7v-1l-1-1 1-1v-1c1-2 5-2 7-2l1-1h0l2 3 1 1c1 4 1 8 3 12 1 2 4 5 5 7h0c-3-1-8-3-11-1-2 2-3 5-6 6-1-1-2-1-3-2l-2 3h-6c-3 0-5 1-7 3h-1c1-2 3-3 5-4v-1c2 0 3-1 4-1h1c2-1 3-1 5-1h1c1 1 2 1 3 0 2-1 3-3 5-4 1 0 0 0 1-1-2 0-3 0-4 1h-1v-1l-2 1c-2 0-4 1-6 0 0-1 1-1 1-2 1-1 1-3 1-4z" class="D"></path><path d="M334 274l3-1c3-1 5-1 7 0h0l-3-6c-1-3-3-7-3-11 1 1 1 2 2 3 1 3 1 7 3 10v-1-3c-1-2-1-3-2-5 0-2-2-4-2-6l2 3 1 1c1 4 1 8 3 12 1 2 4 5 5 7h0c-3-1-8-3-11-1-2 2-3 5-6 6-1-1-2-1-3-2l-2 3h-6c-3 0-5 1-7 3h-1c1-2 3-3 5-4v-1c2 0 3-1 4-1h1c2-1 3-1 5-1h1c1 1 2 1 3 0 2-1 3-3 5-4 1 0 0 0 1-1-2 0-3 0-4 1h-1v-1z" class="I"></path><defs><linearGradient id="h" x1="326.577" y1="283.507" x2="322.423" y2="279.493" xlink:href="#B"><stop offset="0" stop-color="#939094"></stop><stop offset="1" stop-color="#a5a6a3"></stop></linearGradient></defs><path fill="url(#h)" d="M319 282l3-1c2-1 5-2 8-1l-2 3h-6c-3 0-5 1-7 3h-1c1-2 3-3 5-4z"></path><path d="M314 286h1c2-2 4-3 7-3l1 2 1-1c5 1 10 5 13 8 1 3 2 6 2 9-2 8-9 17-16 21l-1-1c2-1 4-2 6-4-1-1-1-1-1-2-1-1-1-1-1-2l-1-1v-3c0-2-1-3-2-4-2-2-3-2-6-2h0l-2 1v-1l-2 1c-1-2-1-7-1-10 0-1-1-1-1-2 0-2 1-3 2-5h0l1-1z" class="J"></path><path d="M325 309l1 2h1 0c1 2 1 3 2 4l-1 2c-1-1-1-1-1-2-1-1-1-1-1-2l-1-1v-3z" class="H"></path><path d="M321 299c1-2 6-1 8-2l1 8c0 1 0 1-1 1s-4-5-5-6c0 0-1-1-2-1h-1z" class="D"></path><path d="M324 284c5 1 10 5 13 8 1 3 2 6 2 9-2 8-9 17-16 21l-1-1c2-1 4-2 6-4l1-2c4-6 6-10 7-17-1-3-2-5-4-8l-1 1c-1 3 1 7 1 11-1 1-1 2-2 3l-1-8c0-2 0-4 1-6l1-1-1-2c-1-1-2-2-3-2 0 2 0 2-1 3s-3 2-4 2c-1 1-2 0-3-1 0-1-1-1 0-3 1-1 3-2 4-2l1-1z" class="B"></path><defs><linearGradient id="i" x1="324.064" y1="296.901" x2="322.033" y2="285.295" xlink:href="#B"><stop offset="0" stop-color="#6c6a6b"></stop><stop offset="1" stop-color="#959393"></stop></linearGradient></defs><path fill="url(#i)" d="M314 286h1c2-2 4-3 7-3l1 2c-1 0-3 1-4 2-1 2 0 2 0 3 1 1 2 2 3 1 1 0 3-1 4-2s1-1 1-3c1 0 2 1 3 2l1 2-1 1c-1 2-1 4-1 6-2 1-7 0-8 2h1l-1 3c-1 0-3-2-4 1h0l-2 1v-1l-2 1c-1-2-1-7-1-10 0-1-1-1-1-2 0-2 1-3 2-5h0l1-1z"></path><path d="M315 304v-1l-2 1c-1-2-1-7-1-10 0-1-1-1-1-2 0-2 1-3 2-5 0 3 0 6 1 9l2 1 1-1h-1c-1-2-2-3-2-5v-1c2 1 3 3 3 5v1c0 1 1 1 2 1 0 1 1 1 2 2h0 1l-1 3c-1 0-3-2-4 1h0l-2 1z" class="T"></path><path d="M295 236c4 0 7 0 10 3 1 1 2 3 2 5 0 1-1 3-2 4 0 1 0 2-1 3l-2 1h0c2 1 2 1 4 1 0 0 1 1 1 2v5c1 0 1-1 2-2 0-1 0-1 2-2 0 0 1 0 1-1l3-1 2-1h1c0 1 1 1 1 2-2 1-2 1-4 1v1h3c1 0 1 0 1 2h1v-1l1-1c1 1 3 3 3 5v2l-1 1c0 1-1 1-1 2h-1l-1 2h-2 0-4l-1-1c-2 0-3-2-4-3-1 0-1 1-2-1l-1 1v1l-5 3v-4c-1-1-2-4-4-4 1 2 0 2 0 4-3-5-5-10-8-15-6-5-10-8-17-11h0v-2c2 0 5 1 8 0 1-1 2-1 4-1 2 2 7 1 10 1l1-1z" class="F"></path><path d="M298 239l3 3-3 3h-2c-1-2 0-2 0-4 1 0 1 0 2-2z" class="L"></path><path d="M297 249h0v1 1h0c-1 0-2-1-3-1-2-2-3-3-3-5 0-3 1-4 2-5 2-1 3-1 5-1-1 2-1 2-2 2l-3 3v1c2 2 2 2 4 3h0v1z" class="B"></path><path d="M307 260v1 1c-3-2-6-7-8-9v-1h1c1 1 0 1 1 1l1-1c2 1 2 1 4 1 0 0 1 1 1 2v5z" class="H"></path><path d="M292 250c1 0 3 2 3 3 2 2 2 5 4 7s2 3 2 5c-1-1-2-4-4-4 1 2 0 2 0 4-3-5-5-10-8-15l2 2c1-1 1-1 1-2z" class="E"></path><path d="M295 236c4 0 7 0 10 3 1 1 2 3 2 5 0 1-1 3-2 4 0 1 0 2-1 3l-2 1-2-2c-1 0-2 0-3-1v-1l2-1h2c1-1 1-3 2-4-1-2-2-4-4-5-3 0-5 0-8 1l-2 1c0 1 0 1-1 1v-2h1c2-1 3-1 5-2l1-1z" class="G"></path><path d="M272 239v-2c2 0 5 1 8 0 1-1 2-1 4-1 2 2 7 1 10 1-2 1-3 1-5 2h-1v2c1 0 1 0 1-1l2-1c-1 2-2 3-2 5s0 3 2 4l1 2c0 1 0 1-1 2l-2-2c-6-5-10-8-17-11h0z" class="T"></path><path d="M272 239v-2c2 0 5 1 8 0 1-1 2-1 4-1 2 2 7 1 10 1-2 1-3 1-5 2h-8c-2-1-3 0-5 0h-3-1 0z" class="K"></path><defs><linearGradient id="j" x1="705.79" y1="266.45" x2="697.709" y2="229.365" xlink:href="#B"><stop offset="0" stop-color="#8e8d8e"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#j)" d="M683 253l8-24 1 1h15c5 0 10 0 14 2 2 0 3 1 5 2 1 0 5 1 6 2 3 0 6 1 9 2l1 1h-1v1h2c-1 2-2 3-4 4-1 0 0 0-1 1h0l-7 8h0c-2 0-4 4-5 6l-1 2c-1 1-1 2-1 3-1 1-1 3-1 4 0 2-1 5-2 7 0 4-1 8-1 13-1 9 2 20 8 28 5 7 11 12 18 14l1 1h-3-1 0 0c1 1 1 1 2 1 1 1 2 1 3 2h1c3 1 7 1 10 2h3l2 2c-9 1-17-1-25-3-1-1-3-1-4-2l-12-3v-2h0c-2-1-3-2-5-2l-5-1-6-2c-3-1-6-1-9-3h0c-2-1-4-3-5-5-1-1-2-2-2-3 0-2-1-4-1-7h1l3-6c-1-2-1-4-1-6v-6l2-1-1-1 1-1h2c3 1 4 1 6 3v-1c0-2-1-2-2-3l1-2c-2 0-3-1-5-1h2c-2-1-6-1-8-1 0-1-1-2-2-2l-1-2-5-3h-6l-2 1c2-3 3-6 4-10l1-7h1 0 1l1-3z"></path><path d="M683 253c1 2 0 8-1 10-1 4-2 7-5 9l-2 1c2-3 3-6 4-10l1-7h1 0 1l1-3z" class="E"></path><path d="M721 232c2 0 3 1 5 2 1 0 5 1 6 2 3 0 6 1 9 2l1 1h-1c-2 1-5 0-7 0-1 0-4 0-4 1h0c-3-1-5-2-7-3h-4 0v-1h-1c-1-1-1-1-2-1l1-1c1 1 3 0 4 0h4-1c-2-1-2-1-3-1v-1z" class="C"></path><path d="M718 236c3 0 9-1 12 0l-1 2h0l5 1c-1 0-4 0-4 1h0c-3-1-5-2-7-3h-4 0v-1h-1z" class="S"></path><path d="M719 237h4c2 1 4 2 7 3h0c0-1 3-1 4-1 2 0 5 1 7 0v1h2c-1 2-2 3-4 4-1 0 0 0-1 1h0l-7 8h0c-2 0-4 4-5 6l-1 2-1 1h-1c0 2-1 3-1 5h-5c-3-1-7-8-8-10-2 0-6-1-7 1-1 0-1 1-2 1v1c1 0 2-1 2-1 2-1 3-1 4 0l3 3c0 1 0 2-1 3l-2 3c-2 0-3 0-5-1l-1-1h0l-1-1c0-1-1-2-1-4 1-2 2-3 3-5 2-1 4-2 6-1 3 0 5 3 6 6 1-2 5-7 5-8s-1-3-2-4c-1-2-1-4-1-6s1-3 2-4l2-2h0z" class="E"></path><path d="M724 242h1 1c0 2 0 2-1 3l-2 1-1-1h-1v-1c1-1 2-2 3-2z" class="B"></path><path d="M716 262h6c0 1 0 2-1 3s-2 0-3 0c-2-1-2-1-2-3z" class="D"></path><path d="M723 237c2 1 4 2 7 3 1 1 2 2 2 4 0 1-1 2-2 3-1 0-2 0-3 1h0-1l2-4c0-2 0-2-2-3 0-2-2-2-3-4z" class="B"></path><path d="M718 248v-2-1c-1-2-1-3 1-5 1-1 2-1 4-1v1h-2c0 3-2 3-2 5 3 3 3 2 7 3h1 0c1-1 2-1 3-1l-1 2c-1 1-3 2-5 2-3 0-5-1-6-3z" class="G"></path><path d="M718 248c1 2 3 3 6 3 2 0 4-1 5-2l1 1-1 2c-1 1-1 2-2 3 0 1 0 0-1 1 0 1-1 1-1 2l1 1-1 2-1 1h-2-6v-1c0-1 1-3 2-5l1 1v1h0l2-2h0v-1c-1-1-1-2-2-3l-1-1c-1-1 0-1 0-3z" class="K"></path><path d="M734 239c2 0 5 1 7 0v1h2c-1 2-2 3-4 4-1 0 0 0-1 1h0l-7 8h0c-2 0-4 4-5 6l-1-1c0-1 1-1 1-2 1-1 1 0 1-1 1-1 1-2 2-3l1-2-1-1 1-2c1-1 2-2 2-3 0-2-1-3-2-4h0c0-1 3-1 4-1z" class="D"></path><path d="M700 266l1 1c2 1 3 1 5 1l2-3c1-1 1-2 1-3l-3-3c-1-1-2-1-4 0 0 0-1 1-2 1v-1c1 0 1-1 2-1 1-2 5-1 7-1 1 2 5 9 8 10h5c0-2 1-3 1-5h1l1-1c-1 1-1 2-1 3-1 1-1 3-1 4 0 2-1 5-2 7l-1-1 1-1v-2h1c-2-2-5 0-7-1v-1-1-2l-1-1c-1 2-1 3 0 5 0 1 1 2 2 2 0 1 0 1 1 2-2 2-5 3-8 3l-1 1h-2v3 1c1 1 2 1 3 2v1l1 1c1 2 1 5 1 7l-1 1c0 1 1 3 0 4v1l-1 3c-2 1-2-1-4 1l-2-1c-2 2-5 3-5 6-1 1-1 2 0 3l-1 1c-2 0-4-1-5-2 0-2-1-3-1-5l3-6c-1-2-1-4-1-6v-6l2-1-1-1 1-1h2c3 1 4 1 6 3v-1c0-2-1-2-2-3l1-2c-2 0-3-1-5-1h2c-2-1-6-1-8-1 0-1-1-2-2-2l-1-2-5-3h1c2 0 3-1 5-1 2-2 3-2 5-3 0 0 0-1-1-2-1-2-2-5-3-8 0-1 0 0 1-1 1 1 1 2 2 3l2 6c0 1 0 1 1 2 0-2 0-2-1-4h1 2c1 1 1 2 2 2z" class="Q"></path><path d="M694 268c1 2 2 3 1 4-1 0-4-1-6-1 2-2 3-2 5-3z" class="D"></path><path d="M688 275l1-1h4c1 0 1 0 3 1l2 1-1 1h-5c-1-1-2 0-3 0l-1-2z" class="K"></path><path d="M699 298l2 2c1 0 2-1 3-1l2 3v-1c1-1 2-1 4-2l-1 3c-2 1-2-1-4 1l-2-1h-2c-2 0-2 0-3-1 0-1 1-2 1-3z" class="J"></path><path d="M702 281c3 1 5 3 7 5v7l-1-1c-3 0-5 1-7 1l1-1c1-2 1-2 1-5v-1c0-2-1-2-2-3l1-2z" class="K"></path><path d="M694 285l1-1h2c3 1 4 1 6 3 0 3 0 3-1 5l-1 1-2 2c0 1 1 3 0 3 0 1-1 2-1 3 1 1 1 1 3 1h2c-2 2-5 3-5 6-1 1-1 2 0 3l-1 1c-2 0-4-1-5-2 0-2-1-3-1-5l3-6c-1-2-1-4-1-6v-6l2-1-1-1z" class="O"></path><path d="M697 284c3 1 4 1 6 3 0 3 0 3-1 5-1-1-2-2-3-2l-2-2c-1-2 0-3 0-4z" class="L"></path><defs><linearGradient id="k" x1="741.147" y1="303.468" x2="702.616" y2="288.491" xlink:href="#B"><stop offset="0" stop-color="#403e3f"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#k)" d="M710 299v-1c1-1 0-3 0-4l1-1c0-2 0-5-1-7l-1-1v-1c-1-1-2-1-3-2v-1-3h2l1-1c3 0 6-1 8-3-1-1-1-1-1-2-1 0-2-1-2-2-1-2-1-3 0-5l1 1v2 1 1c2 1 5-1 7 1h-1v2l-1 1 1 1c0 4-1 8-1 13-1 9 2 20 8 28 5 7 11 12 18 14l1 1h-3-1 0 0c1 1 1 1 2 1 1 1 2 1 3 2h1c3 1 7 1 10 2h3l2 2c-9 1-17-1-25-3-1-1-3-1-4-2l-12-3v-2h0c-2-1-3-2-5-2l-5-1-6-2c-3-1-6-1-9-3h0c-2-1-4-3-5-5-1-1-2-2-2-3 0-2-1-4-1-7h1c0 2 1 3 1 5 1 1 3 2 5 2l1-1c-1-1-1-2 0-3 0-3 3-4 5-6l2 1c2-2 2 0 4-1l1-3z"></path><path d="M707 322c5-1 7 0 12 1l3 2c-1 1-3 0-4 1l-5-1-6-2v-1z" class="F"></path><path d="M703 302l2 1 3 2c2 2 2 2 2 5-1 3-2 4-4 5h-5c-2-1-3-2-3-4-1-1-1-2 0-3 0-3 3-4 5-6z" class="G"></path><path d="M705 307c1 1 2 2 3 2 0 1 0 1-1 2-2 1-3 1-5 1l-1-1c1 0 0 0 1-1h-1l1-1 2 1c1-1 1-1 1-3z" class="L"></path><path d="M703 302l2 1 3 2c-1 0-2 1-3 2 0 2 0 2-1 3l-2-1-1 1h1c-1 1 0 1-1 1l-1 1 2 2-1 1c-2-1-3-2-3-4-1-1-1-2 0-3 0-3 3-4 5-6z" class="B"></path><path d="M690 305h1c0 2 1 3 1 5 1 1 3 2 5 2l1-1c0 2 1 3 3 4h5v1h2c1-1 0-1 1-1s2 0 3-1h1v1c-2 1-3 2-5 2v2c0 1 1 1 3 2h1c-2 0-3-1-5 1h0v1c-3-1-6-1-9-3h0c-2-1-4-3-5-5-1-1-2-2-2-3 0-2-1-4-1-7z" class="H"></path><path d="M690 305h1c0 2 1 3 1 5v1l4 4h0-3c-1-1-2-2-2-3 0-2-1-4-1-7z" class="E"></path><defs><linearGradient id="l" x1="760.973" y1="328.843" x2="732.395" y2="332.675" xlink:href="#B"><stop offset="0" stop-color="#2a2b27"></stop><stop offset="1" stop-color="#464447"></stop></linearGradient></defs><path fill="url(#l)" d="M722 325c4 1 8 2 11 4-1 0-1-1-1-1v-1-1-1c3 3 6 5 11 6h0c1 1 1 1 2 1 1 1 2 1 3 2h1c3 1 7 1 10 2h3l2 2c-9 1-17-1-25-3-1-1-3-1-4-2l-12-3v-2h0c-2-1-3-2-5-2 1-1 3 0 4-1z"></path><path d="M722 325c4 1 8 2 11 4l4 4c-1 0-5-2-7-2l-7-3h0c-2-1-3-2-5-2 1-1 3 0 4-1z" class="H"></path><path d="M552 241c-3 2-7 5-10 8 4-7 11-11 18-13 8-3 17-5 25-5 11 0 56 4 62 0 2-1 4-3 5-5 0-3 0-5-2-8s-6-6-10-7c-1-1-1-1-2-1 1-1 5-2 6-2v-1c-1-8-5-17 0-25 1 2 0 6 0 8s1 4 2 5c4 10 16 17 26 20 23 8 46 6 68-5 27-13 47-35 57-64 3-7 4-14 6-21 1 4 0 9 0 13-1 11-4 22-7 33-1 1-1 2-2 3 0 1 0 0-1 1l1-3c-1 1-1 0-1 1l-1-2c0 1 0 4-1 5l-1-1-4 12-11 32-2 11v8c-2 0-4-1-5-1-6 0-11 0-16 1h-5v-1l-1 1h-3l-1 1-1-1c-3-1-6-2-9-2-1-1-5-2-6-2-2-1-3-2-5-2-4-2-9-2-14-2-1-1-1-1-3-1l-1-1h-4l-2-1h-2c-1 0-2-1-2-1h-1v-1h-4c-9-2-17-6-26-9l-3 1-4-3v1c0 1 0 1 1 2 2 5 1 11-1 16-1 3-4 6-7 8h-1c-3 1-6 0-9 0l-17-1-42-3c-1 0-4 0-5 1h0 0c-3 1-5 0-8 1-5 0-9 2-14 2z" class="R"></path><path d="M651 213l-3-8c-1-1-1-2-1-3 4 6 8 10 15 14l-3 1-4-3h0c-2-1-2-1-4-1z" class="B"></path><path d="M552 241c6-4 14-5 20-6l-2 1v1h9c-1 0-4 0-5 1h0 0c-3 1-5 0-8 1-5 0-9 2-14 2z" class="J"></path><path d="M762 201c0 2-1 4-3 5-3 3-6 3-7 7-3 2-7 5-10 6l3-3 3-1c1-1 1-1 1-2-2 1-4 3-6 4l3-4c3-1 4-4 7-5 3-2 6-5 9-7z" class="C"></path><path d="M780 186c6-3 8-12 12-18l-2 7-4 12c-1 1-3 1-4 2v-1l-2-1v-1z" class="B"></path><path d="M794 172l8-31 1-3c-1 11-4 22-7 33-1 1-1 2-2 3 0 1 0 0-1 1l1-3z" class="K"></path><path d="M651 213c2 0 2 0 4 1h0v1c0 1 0 1 1 2 2 5 1 11-1 16-1 3-4 6-7 8h-1s0-2-1-2c-2-2-7 0-9-1 3-1 7 0 10-2 4-2 7-6 8-10 2-5-1-9-4-13z" class="T"></path><path d="M762 201l11-9c2-2 4-5 7-6v1c-9 10-17 19-28 26 1-4 4-4 7-7 2-1 3-3 3-5z" class="S"></path><path d="M572 235l65 3c2 1 7-1 9 1 1 0 1 2 1 2-3 1-6 0-9 0l-17-1-42-3h-9v-1l2-1z" class="I"></path><defs><linearGradient id="m" x1="735.133" y1="191.654" x2="738.303" y2="226.659" xlink:href="#B"><stop offset="0" stop-color="#060505"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#m)" d="M780 187l2 1v1c1-1 3-1 4-2l-11 32h-1c0-2-2-3-3-5-3 0-6 0-9 1-3 0-6 1-8 2l-2 2c-2 1-3 2-5 3-4 3-11 5-17 6-1 0 0 0-1 1-3 1-7 1-10 1l7 2v2c-2-1-3-2-5-2-4-2-9-2-14-2-1-1-1-1-3-1l-1-1h-4l-2-1h-2c-1 0-2-1-2-1h-1v-1c16 2 35 3 50-6 3-1 7-4 10-6 11-7 19-16 28-26z"></path><defs><linearGradient id="n" x1="776.318" y1="187.282" x2="772.4" y2="213.613" xlink:href="#B"><stop offset="0" stop-color="#302f2e"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#n)" d="M782 189c1-1 3-1 4-2l-11 32h-1c0-2-2-3-3-5-3 0-6 0-9 1-3 0-6 1-8 2 3-3 7-6 10-9 7-5 12-12 18-19z"></path><path d="M754 217c2-1 5-2 8-2 3-1 6-1 9-1 1 2 3 3 3 5h1l-2 11v8c-2 0-4-1-5-1-6 0-11 0-16 1h-5v-1l-1 1h-3l-1 1-1-1c-3-1-6-2-9-2-1-1-5-2-6-2v-2l-7-2c3 0 7 0 10-1 1-1 0-1 1-1 6-1 13-3 17-6 2-1 3-2 5-3l2-2z" class="G"></path><path d="M754 217c2-1 5-2 8-2 3-1 6-1 9-1 1 2 3 3 3 5h0c-1 0-1 0-2-1-3-2-8-1-11 0h-1c-4 1-8 3-11 7-1 2-4 6-7 7-5 1-9 2-14 1l-2-1-7-2c3 0 7 0 10-1 1-1 0-1 1-1 6-1 13-3 17-6 2-1 3-2 5-3l2-2z" class="D"></path><path d="M728 233c1-1 1-2 3-2s7-1 9-1c1 1 2 1 2 2-5 1-9 2-14 1z" class="S"></path><defs><linearGradient id="o" x1="764.543" y1="238.016" x2="748.454" y2="223.423" xlink:href="#B"><stop offset="0" stop-color="#6c6b6c"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient></defs><path fill="url(#o)" d="M732 236l4-1c5-1 11-4 14-9h0v1c0 1 0 2 1 3s3 2 4 3c2 0 5 0 6-1h-1c2-1 3-3 4-4 1-2 0-3 0-4-2-2-3-3-5-3h-2c1-1 3-2 5-2 0 0 1 1 2 1 2-1 5-1 7-1l1 6h0c0-2 0-4 1-5v5h-1v4l1 1v8c-2 0-4-1-5-1-6 0-11 0-16 1h-5v-1l-1 1h-3l-1 1-1-1c-3-1-6-2-9-2z"></path><path d="M772 229l1 1v8c-2 0-4-1-5-1-2-1-3-1-4-1h-1c3-1 5-2 7-2h1c1-2 1-3 1-5z" class="B"></path><path d="M655 214l4 3 3-1c9 3 17 7 26 9h4v1h1s1 1 2 1h2l2 1h4l1 1c2 0 2 0 3 1h-15l-1-1-8 24-1 3h-1 0-1l-1 7h-2c-1-1-1-2-1-3-1-4-2-6-4-9 1 6 2 18-3 23 0 1-1 2-2 3h-1-3 0c-2 1-3 2-4 2h0c-2 0-4 0-6 1-4-1-8 0-11-1-2 1-7 2-8 4v1c0-1-1-2-1-3 0 1 0 2 1 3l-1 1v2h0-1v1h0v1 3c0 1 0 1 1 2v1 2c0 2 0 4 1 5v1c-1 1-1 0 0 2v2h-1v3 1c0 1 0 2-1 3v2l-1 1 1-5c-1-1-1-2-1-3-1-2-1-5-1-7-1-5-1-11-3-16-1-1-1-1-1-2-1-1-1-2-1-3v-1c-1-1-1-1-1-2v-1h-1v-2h0v-1l-1-1v-1l-1-1v-2h-1v-1h0c0 1 1 2 1 3v1 2h1c0 1 0 2 1 3h0c1 1 0 2 1 3v1 1c1 0 1 1 1 2h-1c1 3 1 6 2 9-1 2-1 3-1 5 0-10-5-18-9-26l-2-4c-11-16-28-25-48-29 3-1 5 0 8-1h0 0c1-1 4-1 5-1l42 3 17 1c3 0 6 1 9 0h1c3-2 6-5 7-8 2-5 3-11 1-16-1-1-1-1-1-2v-1z" class="R"></path><path d="M602 244c-1-1-3-1-4-2h-1 0l-1-1h1c3 0 10-1 14 1-2 1-4 1-6 2h-3zm20 22l1 1c4 5 8 15 8 22v1l-9-24z" class="P"></path><path d="M680 256v-1c-1-2-1-4-2-6-2-4-5-7-7-10-2-4-3-7-6-10l-1-2c8 5 14 18 17 27v2h-1z" class="F"></path><path d="M627 286c0-2-1-5-2-7l-5-13 1-1 1 1 9 24c1 7 2 14 1 22-1-1-1-2-1-3-1-2-1-5-1-7-1-5-1-11-3-16z" class="D"></path><path d="M610 256c8 7 11 18 14 28 1 3 1 6 2 9-1 2-1 3-1 5 0-10-5-18-9-26v-3c-1-1-2-2-2-3l-3-3 1-1c-1-1-1-1-2-3l3 2c-1-1 0-1-1-1-1-1-2-2-2-4z" class="H"></path><path d="M662 216c9 3 17 7 26 9h4v1h1s1 1 2 1h2l2 1h4l1 1c2 0 2 0 3 1h-15l-1-1 1-1c-3-2-9-2-12-3-7-2-15-4-21-8l3-1z" class="G"></path><defs><linearGradient id="p" x1="644.945" y1="278.892" x2="646.398" y2="275.478" xlink:href="#B"><stop offset="0" stop-color="#312f30"></stop><stop offset="1" stop-color="#545251"></stop></linearGradient></defs><path fill="url(#p)" d="M642 279c-2-1-3-1-5-2-4-3-6-7-8-12 0-2-2-6-1-8 1 2 2 3 2 5 1 4 3 7 5 10 4 3 8 5 13 5 6 0 14-1 18-5l2-2 1 1c-2 1-3 2-3 5l1 1h-1-3 0c-2 1-3 2-4 2h0c-2 0-4 0-6 1-4-1-8 0-11-1z"></path><path d="M643 243c2 0 3 0 4 1 2 1 2 3 3 5 0 2 0 3-2 5-1 2-3 2-5 2h-2c-2-1-3-3-4-4-1-2-1-4 0-6 2-2 4-3 6-3z"></path><defs><linearGradient id="q" x1="594.467" y1="244.369" x2="591.409" y2="254.843" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#q)" d="M574 238c13 2 26 9 35 18h1c0 2 1 3 2 4 1 0 0 0 1 1l-3-2c1 2 1 2 2 3l-1 1 3 3c0 1 1 2 2 3v3l-2-4c-11-16-28-25-48-29 3-1 5 0 8-1h0z"></path><path d="M611 242c2 0 4 0 7 1 2 2 5 4 7 6 6 6 11 11 18 15 3 1 6 2 9 1 4-1 7-3 10-7 1-2 2-5 3-8l-3-6h0c1 1 3 2 4 3 1 7 0 12-5 17-2 3-4 4-8 4-10 1-15-5-22-11l-10-9c-2-1-4-4-7-5-3 0-6 2-8 3l7 7h-1c-3-3-6-6-10-9h3c2-1 4-1 6-2z" class="E"></path><defs><linearGradient id="r" x1="488.767" y1="696.629" x2="512.074" y2="248.76" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#eaeaea"></stop></linearGradient></defs><path fill="url(#r)" d="M394 259c1-2 1-2 1-4 3-2 5-5 9-7 1-1 1-2 2-3h1c1-1 2-1 3-1h2 1v1h1 0l-2 2-1 1c-2 1-3 2-3 3l-1 1 1 1s1 1 1 2c4-2 7-7 13-6-12 9-19 20-23 35l-2 8c0 2-1 5 0 7 1 4 3 8 4 11l28 83 9 27c1 3 2 7 4 10s3 8 4 11c0 2 2 5 2 7v1l1 3v1l1 1v2l1 1v1h0c1 1 0 0 1 2 0 0 0 1 1 2 2 3 2 8 4 12v1c0 1 0 1 1 2v1c0 1 0 0 1 2v1c0 1 0 0 1 2v1l1 1v2h1l-1 1 16 47c1 4 2 8 4 11l11 34 7 22c1 4 2 7 4 10l3 11 6 19 1-1v-2l90-273c1-7 4-15 7-22l15-46c0-2 0-3 1-5-1-3-1-6-2-9h1c0-1 0-2-1-2v-1-1c-1-1 0-2-1-3h0c-1-1-1-2-1-3h-1v-2-1c0-1-1-2-1-3h0v1h1v2l1 1v1l1 1v1h0v2h1v1c0 1 0 1 1 2v1c0 1 0 2 1 3 0 1 0 1 1 2 2 5 2 11 3 16 0 2 0 5 1 7 0 1 0 2 1 3l-1 5 1-1v-2c1-1 1-2 1-3v-1-3h1v-2c-1-2-1-1 0-2v-1c-1-1-1-3-1-5v-2-1c-1-1-1-1-1-2v-3-1h0v-1h1 0v-2l1-1c-1-1-1-2-1-3 0 1 1 2 1 3v-1c1-2 6-3 8-4 3 1 7 0 11 1 2-1 4-1 6-1h0c1 0 2-1 4-2h0 3 1c1-1 2-2 2-3 5-5 4-17 3-23 2 3 3 5 4 9 0 1 0 2 1 3h2c-1 4-2 7-4 10-1 1-3 2-4 4v1c-3 8-7 15-10 23l-26 72-17 49c-2 5-3 11-6 16l-21 63c0 2 0 3-1 5v1c-1 0-1 0-1 1s-1 2-1 3l-2 6-1 3v1c-1 1-1 1-1 2s-1 3-1 4l-1 1c0 1 0 2-1 3l-1 4-2 5v2l-1 1v1l-2 6v1c0 2 0 0-1 2 0 1 0 2-1 3v1c-1 2-1 3-2 4l3-12h0l-1 2v-2c-1 1-1 1-1 2l-1 4s-1 1-1 2l-2 7c-1 1 0 1-1 2v2l-1 1-2 7-27 83-1 1c-1 1-1 1-1 2s0 1-1 2v1c0 1-1 3-1 4l-1 2c0 1-1 1-1 2 0 2-1 4-2 6v2c0 1-1 2-1 3-1 1-1 3-2 4v1c0 2-1 4-2 6v1l-1 3c-1-1-2-1-2-1h-2v-1l-1 1c-3 0-11 1-13-1h-5s-1-1-1-2v-1c-1 0 0-1-1-1 0-1-1-3-1-4v-1c-1 0-1-1-1-2v-1c-1-2-2-5-3-7 0-2 0 1 0-1l-1-2c0-1 0-3-1-3v-2l-1-1v-2l-1-1c0-1 0-2-1-3h-1l-19-60-1 1-56-161c-2-5-3-10-5-14l-10-31-25-69c-6-17-12-34-20-50l3-1c-4-3-6-8-7-13s0-9 2-13h1v4 4c1 1 1 0 1 1h1v-1c0 3 0 5 1 7v1l2 3c3 3 6 5 10 6 6 0 13 0 19-3 5-3 7-9 9-14 0-1 0-1 1-1z"></path><path d="M642 279c3 1 7 0 11 1h-1c-3 0-6-1-8 0-3 0-7 3-9 5l-1-1v-1c1-2 6-3 8-4z" class="E"></path><path d="M630 302c0 2 0 5 1 7 0 1 0 2 1 3l-1 5c-1 2-1 6-2 8l1-23z" class="K"></path><path d="M393 323c-1-4-2-11-2-15-2-12 5-37 13-46h0c-2 5-5 10-6 15-4 11-5 23-5 35v11z" class="J"></path><path d="M626 293c1 3 1 6 1 9-2 7-5 14-8 21l-10 31-1-1h1v-2c1-1 1-2 1-4l1-1v-2l1-1v-1l2-4v-2c1-2 1-4 2-6l1-1 2-7c0-1 1-2 1-3l1-2-1 1-1 1c0 1 0 1-1 2v1c0 3-2 6-3 9v1c-1 3-2 5-2 7-1 1-1 1-1 2-1 1-1 2-1 3v1h-1v1 1h0c-1 1-1 2-1 3l-1 2v1c-1 1-1 2-1 3l-1 2v1c-1 1-1 2-1 3v1h-1c0 1-1 2-1 3 1-7 4-15 7-22l15-46c0-2 0-3 1-5z" class="K"></path><defs><linearGradient id="s" x1="377.694" y1="268.128" x2="374.339" y2="280.748" xlink:href="#B"><stop offset="0" stop-color="#494747"></stop><stop offset="1" stop-color="#605e5f"></stop></linearGradient></defs><path fill="url(#s)" d="M353 268l2 3c3 3 6 5 10 6 6 0 13 0 19-3 5-3 7-9 9-14 0-1 0-1 1-1-1 6-2 13-8 17-1 1-3 2-5 2v1h0c2 0 4 1 5 3 1 1 1 2 1 3h-1c-1-2-5-5-7-5h-5-8-2c-3-1-4-3-6-4v-1l-1 1c-2-2-3-5-4-8z"></path><path d="M591 501c-1 2-2 3-2 5l-2 4-3 9h0c-1-1 0-3 1-4l4-15 6-16c0-3 2-5 2-7 1-1 1-2 1-3l2-6 5-13v-3c1-3 0 2 1-2 0 0 0-1 1-2v-1l4-12h1c-1 1-1 2-1 2l-1 2v3c-1 1-2 3-2 4s-1 3-1 4v1c-1 1-1 2-1 4-1 1-2 2-2 4v2l-1 1 1 1 1-3v-2c1-2 1-3 1-4l1-1 4-13c0-1 1-1 1-2h0l-21 63z" class="P"></path><path d="M667 277c1-1 2-2 2-3 5-5 4-17 3-23 2 3 3 5 4 9 0 1 0 2 1 3h2c-1 4-2 7-4 10-1 1-3 2-4 4v1l-2-1c-2 1-5 1-8 3-3 1-6 5-8 8l-1 1c-2 4-3 7-4 11-2 8-2 15-2 23-3-10-4-21 0-31 1-1 2-3 3-4v-1c-4 4-7 8-8 13-1-3 3-8 5-11 3-4 8-8 13-10h0c1 0 2-1 4-2h0 3 1z" class="H"></path><path d="M676 260c0 1 0 2 1 3h2c-1 4-2 7-4 10-1 1-3 2-4 4v1l-2-1c5-5 7-11 7-17z" class="N"></path><path d="M347 265c-1-5 0-9 2-13h1v4 4c1 1 1 0 1 1h1v-1c0 3 0 5 1 7v1c1 3 2 6 4 8l1-1v1c2 1 3 3 6 4 7 3 12 8 15 15 2 6 0 20-3 27 1-13 0-22-6-33-4-7-8-9-16-11-4-3-6-8-7-13z" class="M"></path><path d="M350 256v4c1 1 1 0 1 1h1v-1c0 3 0 5 1 7v1c1 3 2 6 4 8v2c-2-1-3-2-4-3-4-4-4-14-3-19z" class="N"></path><defs><linearGradient id="t" x1="394.608" y1="277.561" x2="414.824" y2="291.058" xlink:href="#B"><stop offset="0" stop-color="#bebcbd"></stop><stop offset="1" stop-color="#e3e2e3"></stop></linearGradient></defs><path fill="url(#t)" d="M409 255c4-2 7-7 13-6-12 9-19 20-23 35l-2 8c0 2-1 5 0 7 1 4 3 8 4 11h-1c-1-1-1-2-1-3h-1v-2h0v3c0 1-1 3 0 5 0 1 0 3-1 4v1 1l-1 1s-1 1-1 2v1h-1-1v-11c0-12 1-24 5-35 1-5 4-10 6-15h0c0-1 0-2 1-2 1-2 2-3 4-5z"></path><path d="M411 443l1-1v1l1 2v1h1c0-2-2-4-1-7 2 6 4 12 5 18 1 2 2 4 3 7 0 1 1 2 1 4l2 3v2c1 1 1 1 1 2l2 6 5 14 1 2 8 26 27 80-1 1-56-161z" class="P"></path><defs><linearGradient id="u" x1="512.801" y1="266.738" x2="486.033" y2="785.185" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#b9b8b8"></stop></linearGradient></defs><path fill="url(#u)" d="M677 272h6l5 3 1 2c1 0 2 1 2 2 2 0 6 0 8 1h-2c2 0 3 1 5 1l-1 2c1 1 2 1 2 3v1c-2-2-3-2-6-3h-2l-1 1 1 1-2 1v6c0 2 0 4 1 6l-3 6h-1c0 3 1 5 1 7 0 1 1 2 2 3 1 2 3 4 5 5h0c3 2 6 2 9 3l6 2 5 1c2 0 3 1 5 2h0v2c-9-3-18-2-26 3a34.47 34.47 0 0 0-12 12s-1 1-1 2c0 2-1 3-1 4v4c1-2 1-3 2-4 0-2 0-3 2-4h0c-2 5-4 10-5 15v9h1v3l1 1v2 1c0 1 0 3 1 4 0 1-1 3 0 4v2h0 0v1h0v1 2l-1 2h-1-2v-1l-11-7h-2-1l-2-1h0c-2 1-4-1-5 0h-2c-2 0-3 1-4 2-2 4-3 13-3 17 0 6 0 12 1 18 1 5 3 8 3 13-1 1-1 2-1 3l2 1-5 10-2 2c-2 3-3 6-4 10-1 1-1 3-2 5-3 1-3 3-5 5h-1l-13 36-6 16-17 48c-4 7-5 15-8 22l-1 4c-1 3-1 5 0 7l-32 89c-5 9-7 19-10 28l-17 47c-2 4-15 41-15 42 0 5-3 9-4 14v3 2c0 1 0 2 1 4l1-3 1 1c0 1-1 3-2 4l-4 11c-2-3-3-8-4-12l-7-19c-2-4-3-8-4-12 0-2-1-3-2-5 0-3-3-8-3-11 0-1 1-2 2-3 0-1 0-1 1-2l-27-73h-1l-7-14-7-21c-2-5-2-10-5-14-1-3-2-6-3-8h-1c-1-1-2-3-2-5-1-1-1 0-2-1s-6-17-7-19l-13-36 1-1-1-1v-4 1h1l-6-20h1l-31-89v-1c1 1 1 1 2 0l-4-10-3-9c-1-3-1-5-3-7v-1c-2-2-4-3-5-5l-1-3-1-2h-1c-1-2 0-5 0-7l3-12c1-7 0-15-1-23 0-1 0-4-1-5h0c-1-1-1-1-1-2-1-2-1-3-2-4-3-1-5-1-7-1h-1v-1l-4 2h-1c-2 1-7 4-8 6s-2 4-4 4l-1 1v-3h0c1-1 1-2 1-3 1-1 2-2 2-3l1-2c2-10 2-21 1-30-1-4-3-7-4-10 0-1-1-2-1-3 1-1 1-1 2-1 0-2 0-3-1-4l1-1c-1-2-2-3-2-5-1-2-2-4-2-6l-1 1h-1c0 1 0 1-1 1l-3-1c-2-1-5-3-7-4h-2v-1c7-4 14-13 16-21 0-3-1-6-2-9-3-3-8-7-13-8l-1 1-1-2h6l2-3c1 1 2 1 3 2 3-1 4-4 6-6 3-2 8 0 11 1h0c1 0 1 1 1 2 8 16 14 33 20 50l25 69 10 31c2 4 3 9 5 14l56 161 1-1 19 60h1c1 1 1 2 1 3l1 1v2l1 1v2c1 0 1 2 1 3l1 2c0 2 0-1 0 1 1 2 2 5 3 7v1c0 1 0 2 1 2v1c0 1 1 3 1 4 1 0 0 1 1 1v1c0 1 1 2 1 2h5c2 2 10 1 13 1l1-1v1h2s1 0 2 1l1-3v-1c1-2 2-4 2-6v-1c1-1 1-3 2-4 0-1 1-2 1-3v-2c1-2 2-4 2-6 0-1 1-1 1-2l1-2c0-1 1-3 1-4v-1c1-1 1-1 1-2s0-1 1-2l1-1 27-83 2-7 1-1v-2c1-1 0-1 1-2l2-7c0-1 1-2 1-2l1-4c0-1 0-1 1-2v2l1-2h0l-3 12c1-1 1-2 2-4v-1c1-1 1-2 1-3 1-2 1 0 1-2v-1l2-6v-1l1-1v-2l2-5 1-4c1-1 1-2 1-3l1-1c0-1 1-3 1-4s0-1 1-2v-1l1-3 2-6c0-1 1-2 1-3s0-1 1-1v-1c1-2 1-3 1-5l21-63c3-5 4-11 6-16l17-49 26-72c3-8 7-15 10-23v-1c1-2 3-3 4-4l2-1z"></path><path d="M330 280c1 1 2 1 3 2v1h-5l2-3z" class="D"></path><path d="M539 700c1 0 2 2 3 2h0 0l1-1c1 1 1 1 1 2h0v2 1h-1l-3-2c0-2-1-2-1-4z" class="C"></path><path d="M633 421h3l-1 2h-6c-2 0-4 0-6-1l1-1h9z" class="B"></path><path d="M484 698h0c1 0 1 0 2 1-1 3-3 4-5 6l-1-1v-2s0-1-1-1c2-1 3-2 5-3z" class="C"></path><path d="M585 600l-3-1c1-1 2 0 3 0s1-1 1-1c2-4 1-6 2-10 0-2 0-2 1-3 1 1 1 2 1 4 0 3-1 8-4 10v1h-1 0z" class="B"></path><path d="M469 656v2c1 0 2 0 3-1 0 2-1 4-2 5-1 2-3 3-5 3v-1c0-3 3-4 4-8z" class="I"></path><path d="M386 421h0c5 0 11-1 16 0-5 2-11 3-16 3l-1-2 1-1zm205 137c1-1 2 0 3 0 1 1 1 1 1 2-3 0-8 3-11 4-1 1-2 1-3 1 3-3 6-5 10-7zm-162 0l6 2c3 1 6 3 7 6-3-2-11-5-14-5-1 1 0 1-1 0 0-1 1-2 2-3z" class="B"></path><path d="M636 421h2l1 1 2 3-1 1h-3l-5 2 2-3v-1l-5-1h6l1-2z" class="D"></path><path d="M638 421l1 1 2 3-1 1h-3c0-1 0-1-1-2 1-1 2 0 3-1l-1-2z" class="O"></path><path d="M434 584l1 1c2 3 0 7 0 10-1 1 0 2 1 3s2 1 3 1v1c-1 0-3 0-4-1s-2-2-2-4c-1-3 0-8 1-11z" class="G"></path><path d="M433 552h-2l-1-1c2 0 4 0 5 1 2 0 5 3 6 5 0 1 1 2 2 3v2 4c-1 0-1 1-2 0h1c-1-3-4-5-7-6l3-3 5 6c-2-5-5-9-10-11z" class="S"></path><path d="M459 656c1-2 3-4 5-4 2-1 4 0 5 1s2 2 2 3l1 1c-1 1-2 1-3 1v-2h-4-1 0c-2 1-3 1-5 0z" class="L"></path><path d="M590 589c1 1 2 2 3 4h0l-1 4c-2 4-3 8-5 11v-1-2c1-1 1 0 1-1v-1-3h-3 0 1v-1c3-2 4-7 4-10z" class="K"></path><path d="M664 376l6 10h-2l-1-2h0v-1c-3-1-5-1-8-1l-1-3c-1 0-1 0-1-1l1-1h4 2v-1z" class="J"></path><path d="M658 377h4c1 2 1 2 1 3-1 1-4 0-5-1-1 0-1 0-1-1l1-1z" class="M"></path><path d="M607 526h0v2c-1 1-2 2-4 3h0c-1 0-4 1-5 0-1 0-2-2-3-2l-1 1-2-4c2-1 2-1 3-2s1 0 2-1l1-1v-1l1-1c1-2 2-2 4-2-3 2-5 4-5 7-1 1 0 3 1 4s1 1 3 1 3-2 4-3l1-1z" class="C"></path><path d="M659 382c-1 0-5 1-6 0-1 0-1-1-2-3h0v-3c-1-1-1-1-1-2v-3h1c1 0 2 2 3 4v1c1 0 1 1 2 1l1-1h1 1l-1 1-1 1c0 1 0 1 1 1l1 3z" class="F"></path><path d="M446 582h3c2 1 2 1 2 2-1-1-2 0-3 0-2 1-4 2-5 4 3 2 8 4 11 7h-1l-2-1c-3-1-7-3-9-3h0-1c-1 1-1 2-1 3v1h0l-2-1v-2c1-4 5-8 8-10zm-13-30c5 2 8 6 10 11l-5-6-3 3-6-2v-2c-1 0-1-1-1-1v-1c2-2 3-2 5-2z" class="B"></path><path d="M429 556c1-1 1-1 3-1 2-1 4 1 6 2l-3 3-6-2v-2z" class="D"></path><path d="M677 272h6l5 3 1 2c1 0 2 1 2 2 2 0 6 0 8 1h-2c-1 0-7 1-8 1-1-1-3-3-3-4-1 0-2-2-3-2-3-2-8 3-12 2 1-2 3-3 4-4l2-1z" class="I"></path><path d="M464 656h0 1 4c-1 4-4 5-4 8v1h-1c-2 0-3 0-5-1v-1c-1-2 0-5 0-7 2 1 3 1 5 0z" class="E"></path><path d="M464 656h0 1 1c0 1 0 1 1 2l-2 2h-1c-1-1-1-2-1-3l1-1z" class="B"></path><path d="M459 656c2 1 3 1 5 0l-1 1c-1 1-2 1-2 3h0c0 2-1 3-2 4v-1c-1-2 0-5 0-7z" class="I"></path><path d="M423 532v1c-2 1-4 2-5 3-2-1-5-2-6-3-2-2-2-5-2-8 0-2 2-4 4-6l1 1c-1 1-2 2-2 4s2 6 4 7c1 1 3 2 5 1h1z" class="E"></path><path d="M384 408v1c0 2-1 10 1 11l1 1-1 1-1-1v1 2l1 1h-2-3l2-1-1-1h-1c-2 0-4 1-6 0 3-5 7-9 10-15z" class="Q"></path><path d="M424 520h0l1 1c0 1 1 2 3 3h1c1-1 1 0 2 1s1 1 0 2l-2 2-1 1h-1l1-1-1-1c-1 1-4 3-4 4h-1c-2 1-4 0-5-1-2-1-4-5-4-7s1-3 2-4l1 1h0c-1 2 0 4 1 5 1 2 2 3 4 3s2 0 3-1 1-2 1-3c0-2-1-4-1-5z" class="C"></path><path d="M414 519c3-1 5-1 7 0 1 0 2 1 3 1 0 1 1 3 1 5 0 1 0 2-1 3s-1 1-3 1-3-1-4-3c-1-1-2-3-1-5h0l-1-1-1-1z" class="B"></path><path d="M634 457h2 4c0 2 0 4-1 6 0 2-2 5-2 8l-13 36v-4l1-1c0-2 1-4 1-6 1-2 0-4 1-5 1-2 2-4 2-5 1-1 1-2 2-3l1-1v-1c0-1 1-1 2-2 0-1 0-3 1-3v-1-1-1-1c0-1 0-2 1-2 0-2 0-3 1-5s1-4 2-6h0l-1-1c-1 0-2 0-3-1h-1z" class="D"></path><path d="M640 426h0v1 2l-1 1c-3-1-4 0-6 1-13 6-12 20-9 31 1 3 3 9 2 11-2-3-4-10-5-13-1-5-1-12 0-17 2-6 5-11 11-15l5-2h3z" class="M"></path><path d="M603 518h0c3 1 6 1 8 4 1 2 1 5 1 7-1 2-2 4-4 5s-4 1-6 1v-1h-1 2c2 0 3-1 5-3 1-1 3-4 2-6 0-2 0-2-1-3s-4-2-5-1c-1 0-3 1-3 2-1 1-1 2-1 3h1c3-1 2-4 5-3 1 1 1 1 1 3l-1 1c-1 1-2 3-4 3s-2 0-3-1-2-3-1-4c0-3 2-5 5-7zm-37 97h2c3 1 5 4 6 6 0 2 0 4-1 5-1 2-2 2-3 2h-2c-2 0-5-2-6-4-1-1-1-3-1-5 1-2 3-3 5-4z" class="L"></path><path d="M566 619c2-1 4 1 5 2l1 2c0 2-1 2-3 4-2 0-2 0-4-1l-1-1v-1h3c1-1 2-1 3-2v-1c-1-1-2-1-3 0-1 0-2 0-3-1 1-1 1-1 2-1z" class="C"></path><path d="M453 628h-1c-1 0-2-1-3-2s-1-3 0-5 4-6 6-6c1 0 3 0 4 1s3 2 3 4 0 3-1 4c-2 3-3 4-7 4-1 1 0 1-1 0z" class="L"></path><path d="M453 628l-3-2c0-2 0-4 1-6 2-2 2-2 5-2 1 1 2 1 2 3-1-1-2 0-4 0v-2l-1 1v2c1 2 1 2 3 2 1-1 3-1 5-2-1 1-4 5-5 5s-1 0-2 1h0c-1 1 0 1-1 0z" class="K"></path><path d="M556 652h0c2-1 4 0 6 1 2 3 2 7 2 10-1 1-2 2-4 2h-2c-3 0-5-2-6-4-1-1-1-3 0-5 0-2 2-3 4-4z" class="L"></path><path d="M640 412c2 4 6 7 7 12 2 2 2 5 4 8v1h-1c0 2 0 3-1 4l-1-2c-2-2-3-3-5-4l-1 1-1-1-2-1 1-1v-2-1h0l1-1-2-3-1-1h-2-3c2 0 3 0 4-2 1-1 1-1 3-2 1 1 2 1 3 1l-1-1c-1-2-2-2-2-5h0z" class="F"></path><path d="M640 426h2c2-2-1-4-1-6l1-1 2 1c0 1 1 2 3 3v1c2 2 2 5 4 8v1h-1 0c0-2-2-4-3-6h-7v-1z" class="H"></path><path d="M640 427h7c1 2 3 4 3 6h0c0 2 0 3-1 4l-1-2c-2-2-3-3-5-4l-1 1-1-1-2-1 1-1v-2z" class="O"></path><path d="M643 431c-1 0-2 0-2-1 1-1 4 0 6 1 1 0 2 1 3 2h0c0 2 0 3-1 4l-1-2c-2-2-3-3-5-4z" class="C"></path><path d="M668 328l1-1c0 1 1 2 1 3l1 1h1c1 0 3 0 4 1h3v1c-2 1-4 1-6 0-1 0-2 0-2 1l-1 1 1 1-1 1v1h-1c-1 0-1 0-2 1v1l-1-1h-4c-2 1-4 3-5 3l-1 1c0-1-1-1-1-2 0-2 1-4 2-5 1 0 1 0 3-1h3c0-2-2-2-2-4v-1l-1 1v-1c0-1 0-1 1-2 2 0 4-1 6-1 0 1 1 1 1 1z" class="F"></path><path d="M670 335c-1 1-1 1-2 1l-1-2c0-1 0-2 1-3 2 1 2 1 3 3l-1 1z" class="O"></path><path d="M657 342v-3c2-2 3-1 4-2 2 0 2 0 3 1l3 1v1l-1-1h-4c-2 1-4 3-5 3z" class="D"></path><path d="M615 491h-1-1c-1-1-1-2-2-3 0-3 0-5 1-7 2-2 4-3 6-3s4 1 5 2c2 2 3 5 3 8s-1 6-3 8c-4 4-9 4-13 4v-1c4 0 9-2 12-4 2-2 3-5 3-7 0-3-1-5-3-7v2c1 1 1 2 1 4-1 1-3 4-5 4-1 1-2 0-3 0z" class="L"></path><path d="M616 487h-2l-2-2c0-2 1-2 2-3s3-1 4-1l1 1h-2c0 2-1 3-1 5z" class="C"></path><path d="M616 487c0-2 1-3 1-5h2l2 2 1-1c1 1 1 2 1 4-1 1-3 4-5 4-1 1-2 0-3 0h0c1-1 2-1 3-1l3-3h0l-1-1c-1 1-2 1-3 1h-1z"></path><path d="M665 344c2 2 3 3 4 6l1 2c-1 2-2 3-5 4v2c0 1 1 2 2 2-2 2-2 4-3 6v-1c-1 0-1-1-1-2l-3-2c-3-2-5-5-5-9 0-2 1-3 2-5 2-2 4-3 8-3z" class="B"></path><path d="M659 350h3 1 1l1 1h1c0 2 0 2-1 4h-4c-2 0-2-1-3-2 0-2 0-2 1-3z"></path><path d="M402 478h2c2 0 3 0 5 2 1 1 3 4 2 6 0 2 0 2-1 4h3c-1 1-3 1-5 2-3 0-5 0-7-2s-2-3-3-5v-1h0c-1 1-1 1-1 2-1 2 0 5 2 7 4 4 8 5 13 6l1 1c-5 0-10-1-14-4-3-2-3-5-4-9 2-4 3-7 7-9z" class="B"></path><path d="M369 439c0-4 0-7 2-11h0c1-2 2-4 3-5 2 1 4 0 6 0h1l1 1-2 1h3v1h1c3 0 5 1 7 3 3 1 5 3 6 6 3 3 4 6 5 11 1 3 0 7 0 10 0 2 0 3-1 4 0 3-1 6-2 8-1 3-2 5-4 7v-1c0-2 1-4 2-5l1-3v-1l1-1 1-7c0-2 0-4 1-6 0-2 0-4-1-6v-1c0-4-2-8-6-11s-7-4-12-4c-4 1-5 2-8 4-1 1-1 2-2 3l-2 5-1-2z" class="J"></path><path d="M380 425h3v1h1c3 0 5 1 7 3l-3-1h-1c-2 0-7-1-9 0h0-1c-1 0-2 1-3 1v-1c1-1 2-2 4-3h2z" class="O"></path><path d="M627 452c-1-3-2-6-1-9 1-2 3-4 5-6 2-1 5-1 7 0l1 2 2 2v2h0l1 1c0 3-1 4-3 6h0 1c0 1 0 2-1 3l2 1c-1 1-3 2-5 3h-2c-3-1-5-3-7-5z" class="F"></path><path d="M628 450c2 2 4 3 6 3s2 0 4-1c0-1-1-2 1-2h0 1c0 1 0 2-1 3l2 1c-1 1-3 2-5 3h-2c-3-1-5-3-7-5l1-2z" class="E"></path><path d="M639 439l2 2v2h0c0 2-1 4-3 5s-4 1-6 0c-2 0-3-2-4-4h0l1-1h0c3 1 5 1 8 0 1 0 0-1 1-1 0-2 1-2 1-3z" class="L"></path><path d="M627 452c-1-3-2-6-1-9 1-2 3-4 5-6 2-1 5-1 7 0l1 2c0 1-1 1-1 3-1 0 0 1-1 1-3 1-5 1-8 0h0l-1 1-1 1c0 2 1 3 1 5l-1 2z" class="G"></path><path d="M629 443c2-2 2-2 4-3 3 0 3 0 5 2-1 0 0 1-1 1-3 1-5 1-8 0z" class="C"></path><path d="M640 412c-3-8-5-13-3-21 0 2 0 5 1 8 1 8 5 15 9 22 2 3 2 6 4 8 0-3-2-5-2-7-2-6-2-13-1-18 1-6 3-16 7-20 3-2 8-1 12 0l1 2h-1l-2-1h0c-2 1-4-1-5 0h-2c-2 0-3 1-4 2-2 4-3 13-3 17 0 6 0 12 1 18 1 5 3 8 3 13-1 1-1 2-1 3l2 1-5 10-2 2h-1v-3c2-4 1-7 1-11 1-1 1-2 1-4h1v-1c-2-3-2-6-4-8-1-5-5-8-7-12z" class="G"></path><g class="D"><path d="M650 433h1c2 5 0 10 0 16l-2 2h-1v-3c2-4 1-7 1-11 1-1 1-2 1-4z"></path><path d="M641 431l1 1 1-1c2 1 3 2 5 4l1 2c0 4 1 7-1 11v3h1c-2 3-3 6-4 10-1 1-1 3-2 5-3 1-3 3-5 5h-1c0-3 2-6 2-8 1-2 1-4 1-6h-4c2-1 4-2 5-3l-2-1c1-1 1-2 1-3h-1 0c2-2 3-3 3-6l-1-1h0v-2l-2-2-1-2v-1h0c-2-1-4-1-5-2h-1l1-1 2-1h2c2 0 2-1 4-1z"></path></g><path d="M635 432c3 1 4 1 6 3 1 1 0 1 1 2 0 1 2 1 2 2v2c-1-1-2-3-4-3 1 1 2 2 2 4l-1-1-2-2-1-2v-1h0c-2-1-4-1-5-2h-1l1-1 2-1z" class="F"></path><path d="M641 441l1 1c0-2-1-3-2-4 2 0 3 2 4 3 0 2 1 3 1 5-1 3-2 6-4 8l-2-1c1-1 1-2 1-3h-1 0c2-2 3-3 3-6l-1-1h0v-2z" class="B"></path><path d="M643 431c2 1 3 2 5 4l1 2c0 4 1 7-1 11v3h1c-2 3-3 6-4 10-1 1-1 3-2 5-3 1-3 3-5 5h-1c0-3 2-6 2-8 1-2 1-4 1-6h-4c2-1 4-2 5-3 2-2 3-5 4-8 0 1 0 3 1 4h0l1-2c2-3 2-6 1-10-2-3-3-5-6-6l1-1z" class="E"></path><path d="M640 457l1-1c1 0 1 0 2 1l-5 14h-1c0-3 2-6 2-8 1-2 1-4 1-6z" class="Q"></path><path d="M641 454c2-2 3-5 4-8 0 1 0 3 1 4-1 3-2 5-3 7-1-1-1-1-2-1l-1 1h-4c2-1 4-2 5-3z" class="J"></path><path d="M384 477v-1c1 1 1 1 2 0l7 20 26 71c1 3 6 16 6 18l-1 2-9-21-31-89z" class="E"></path><path d="M592 597c-1 3-1 5 0 7l-32 89c-5 9-7 19-10 28l-17 47c-2 4-15 41-15 42-2 5-4 12-6 17h0 0l24-70c1-2 2-6 3-9l4-12 44-128c2-3 3-7 5-11z" class="I"></path><path d="M510 824l25-67h1l-24 70h0 0c2-5 4-12 6-17 0 5-3 9-4 14v3 2c0 1 0 2 1 4l1-3 1 1c0 1-1 3-2 4l-4 11c-2-3-3-8-4-12l-7-19c-2-4-3-8-4-12 0-2-1-3-2-5 0-3-3-8-3-11 0-1 1-2 2-3 0-1 0-1 1-2l6 18c1 3 2 8 4 10l2 5 1 5 3 6v1c1-1 0-1 0-2v-1z" class="R"></path><path d="M376 433c2-1 5-2 9-2h0c3 1 4 2 6 3 3 3 4 5 5 8 0 3 1 5-1 7 0 2-1 3-3 4v1c-2 2-3 3-6 4-2 0-2 0-3 2-1 1-1 4-1 6l-3-9c-1-3-1-5-3-7v-1c-2-2-4-3-5-5l-1-3 2-5c1-1 1-2 2-3h2z" class="B"></path><path d="M383 444h-1v-1c2-2 3-3 6-2 2 0 5 1 6 3v4c-2 1-2 2-4 2l-1-1 3-2c1-1 1-2 1-3-3-1-7 0-10 0z" class="D"></path><path d="M383 444c3 0 7-1 10 0 0 1 0 2-1 3l-3 2h-1-3c-2 0-3-1-4-2 0-2 1-2 2-3z"></path><path d="M371 444c2 0 4 1 6 0h0c1 3 1 9 4 11h1l5-2h0-1c-2 0-3 0-4-1-1-2-2-3-2-5h0 1c1 1 2 2 4 2h3 1l1 1c2 0 2-1 4-2h0c-1 2-1 3-2 5h0v1c-2 2-3 3-6 4-2 0-2 0-3 2-1 1-1 4-1 6l-3-9c-1-3-1-5-3-7v-1c-2-2-4-3-5-5z" class="H"></path><path d="M376 433c2-1 5-2 9-2h0c3 1 4 2 6 3 3 3 4 5 5 8-3-3-6-5-10-5-2 0-5 1-6 3-1 1-2 3-3 4h0c-2 1-4 0-6 0l-1-3 2-5c1-1 1-2 2-3h2z" class="O"></path><path d="M376 433c2-1 5-2 9-2h0c3 1 4 2 6 3v1l-2-1c-4-1-10-2-14 1h0-1l2-2z" class="J"></path><defs><linearGradient id="v" x1="497.813" y1="815.421" x2="511.163" y2="775.536" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#818080"></stop></linearGradient></defs><path fill="url(#v)" d="M510 824c-4-17-11-34-16-50l-4-11c0-1-1-3-1-4 1 0 1 0 1 1v1l1 1h0v1 1l1 1h0v1 1c1 1 0 1 1 3l1 1v2c1 0 0 0 1 1 0 1 0 2 1 3h1 1 1v1h1s1 1 2 1h2c0 1 0 1 1 1l3 1c1 1 1 1 3 1-1-3-1-6 0-8v2h1l-1-2 1-1 1 1c0 1 0 4-1 5v1c-1 2 0 5 0 7 0-1 0-2 1-2h0l1-1h1l1-1 1-1h0 1l1-1h0 1 1 1l1-1h0c1 0 1 0 2-1l1-2c0-1 1-1 1-2v-1l1-1c0-2 1-3 1-4 1-2 1-4 2-6l1-2 2-6 1-3 1-3c0-1 1-3 1-4s1-1 1-2h0c1-2 1-2 2-3h1v-1c1-2 1-2 2-3l-4 12c-1 3-2 7-3 9h-1l-25 67z"></path><defs><linearGradient id="w" x1="337.174" y1="333.926" x2="356.425" y2="314.256" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#w)" d="M324 284h3l1 1v-1l1 1h0c2 0 2 1 3 2 1 0 1 0 1 1 1 0 1 1 2 1 2 1 4 4 4 6 1 1 0 0 1 2v1c-1 2 0 3 1 5h1 1 1l1 2 1-1 2 3h1c1 0 1 0 1 1v1c1 1 0-1 1 1 0 1 1 1 1 1v1c0 1 1 1 1 2v1c2 2 1 2 2 4h0v1c0 1 0 0 1 1s1 3 1 4c1 2 3 3 5 3h1c1 0 1 0 3-1 1 0 1 0 2 1s0 2 1 4c-1 1-1 1-1 3h1l-1 1c-2-1-2 0-4 1 1 1 2 1 3 3 0 1 0 1-1 2l-2-1h-1 0-1-1c-1 0-2-1-3-1h-2l-5 3v-1s0-1 1-2h0v-1-4l-1 1-2 2h-5c-1 0-1 0-2-1-1-2-2-3-2-5-1-2-2-4-2-6l-1 1h-1c0 1 0 1-1 1l-3-1c-2-1-5-3-7-4h-2v-1c7-4 14-13 16-21 0-3-1-6-2-9-3-3-8-7-13-8z"></path><path d="M345 309h1c1 2 1 3 1 4l-1 1h-2c0-2 0-3 1-5z" class="T"></path><path d="M336 327v-2c0-2 1-4 3-5h0c1 1 1 2 1 3v1l-2 2-1 1h-1z" class="Q"></path><path d="M340 323c2-2 3-4 6-4 2 0 4 1 5 2 2 2 3 5 3 8l-4 4c0 1 0 1-1 1 0 1 1 1 2 2l-2 2h-5c-1 0-1 0-2-1-1-2-2-3-2-5-1-2-2-4-2-6l2-2v-1z" class="G"></path><path d="M343 329c0-1 0-2-1-3 0-1 1-2 2-3h3c0 1 1 2 1 3h2v1c0 1 0 1-1 2s-3 1-5 0h-1z"></path><path d="M350 326c-1-1-2-2-1-3l1-1c1 1 2 2 2 4s-1 4-3 5l-1 1h0 1s1 0 1 1 0 1-1 1c0 1 1 1 2 2l-2 2h-5c-1 0-1 0-2-1-1-2-2-3-2-5-1-2-2-4-2-6l2-2c0 2 0 3 1 4l2 1h1c2 1 4 1 5 0s1-1 1-2v-1z" class="M"></path><path d="M671 338l1-2c2 0 4 1 6 3l2 1c-1 3-1 6-1 10 1-3 1-5 2-7h2c-1 3-2 6-2 9l3-5c0 2-1 3-1 4v4c1-2 1-3 2-4 0-2 0-3 2-4h0c-2 5-4 10-5 15v9h1v3l1 1v2 1c0 1 0 3 1 4 0 1-1 3 0 4v2h0 0v1h0v1 2l-1 2h-1-2v-1l-11-7-6-10c-1-5-2-8-1-13 0 1 0 2 1 2v1c1-2 1-4 3-6-1 0-2-1-2-2v-2c3-1 4-2 5-4l-1-2c-1-3-2-4-4-6h-1-1l-1-1c-2 0-2 0-3-1-1 0-2 1-3 1l1-1c1 0 3-2 5-3h4l1 1v-1c1-1 1-1 2-1h1 1z" class="F"></path><path d="M671 338c2 0 2 0 4 1l-1 2h-3l-2-1v-2h1 1z" class="O"></path><path d="M667 339c1-1 1-1 2-1v2 1l3 3v6c0 3-2 6-3 8l-2 2c-1 0-2-1-2-2v-2c3-1 4-2 5-4l-1-2c-1-3-2-4-4-6h-1-1l-1-1c-2 0-2 0-3-1-1 0-2 1-3 1l1-1c1 0 3-2 5-3h4l1 1v-1z" class="L"></path><path d="M659 342c1-1 2-1 3-2h1 0 1c2 0 4 2 5 3 1 2 2 4 2 5 0 2-1 3-1 4l-1-2c-1-3-2-4-4-6h-1-1l-1-1c-2 0-2 0-3-1z" class="S"></path><path d="M670 362l1-4 1 1v-2c1 3-1 8-1 11 0 2 0 4 1 7l2 7 2-2c0-1 0-1 1-2 0 4 0 7 1 10l2 2 1 3-11-7-6-10c-1-5-2-8-1-13 0 1 0 2 1 2v1c1-2 1-4 3-6l2-2v4h1z" class="J"></path><path d="M670 362l1-4 1 1v-2c1 3-1 8-1 11 0 1-1 3 0 4 0 2 0 2-1 4-1-2-2-3-1-5l1-9z" class="H"></path><path d="M663 363c0 1 0 2 1 2v1c1 7 5 18 12 22h1 1l2 2 1 3-11-7-6-10c-1-5-2-8-1-13z" class="G"></path><path d="M679 350c1-3 1-5 2-7h2c-1 3-2 6-2 9l3-5c0 2-1 3-1 4v4c1-2 1-3 2-4 0-2 0-3 2-4h0c-2 5-4 10-5 15v9h1v3l1 1v2 1c0 1 0 3 1 4 0 1-1 3 0 4v2h0 0v1h0v1 2l-1 2h-1-2v-1l-1-3-2-2c-1-3-1-6-1-10-1-10 0-19 2-28z" class="P"></path><defs><linearGradient id="x" x1="670.626" y1="378.331" x2="689.874" y2="363.169" xlink:href="#B"><stop offset="0" stop-color="#1d1c1c"></stop><stop offset="1" stop-color="#353634"></stop></linearGradient></defs><path fill="url(#x)" d="M679 350c1-3 1-5 2-7h2c-1 3-2 6-2 9-3 14-2 28 2 42h-2v-1l-1-3-2-2c-1-3-1-6-1-10-1-10 0-19 2-28z"></path><defs><linearGradient id="y" x1="442.412" y1="662.158" x2="459.572" y2="658.64" xlink:href="#B"><stop offset="0" stop-color="#36363b"></stop><stop offset="1" stop-color="#6e6c68"></stop></linearGradient></defs><path fill="url(#y)" d="M415 566l9 21 1-2c1 3 3 6 4 9l76 216h-1c-2-2-3-7-4-10l-6-18-27-73h-1l-7-14-7-21c-2-5-2-10-5-14-1-3-2-6-3-8h-1c-1-1-2-3-2-5-1-1-1 0-2-1s-6-17-7-19l-13-36 1-1-1-1v-4 1h1l-6-20h1z"></path><path d="M448 655l19 54h-1l-7-14-7-21c-2-5-2-10-5-14 0-2 0-3 1-5z" class="P"></path><path d="M415 566l9 21 24 68c-1 2-1 3-1 5l-3-8h-1c-1-1-2-3-2-5-1-1-1 0-2-1s-6-17-7-19l-13-36 1-1-1-1v-4 1h1l-6-20h1z" class="C"></path><path d="M434 626l10 26h-1c-1-1-2-3-2-5-1-1-1 0-2-1s-6-17-7-19c1 0 1 0 2-1z" class="K"></path><path d="M419 591l1-1-1-1v-4 1h1l14 40c-1 1-1 1-2 1l-13-36z" class="D"></path><path d="M537 659l27-83 2-7 1-1v-2c1-1 0-1 1-2l2-7c0-1 1-2 1-2l1-4c0-1 0-1 1-2v2l1-2h0l-3 12-16 45-36 112-5 13c-1 3-1 9-3 11l-29-90-10-32v-1l-5-15 1-1 19 60h1c1 1 1 2 1 3l1 1v2l1 1v2c1 0 1 2 1 3l1 2c0 2 0-1 0 1 1 2 2 5 3 7v1c0 1 0 2 1 2v1c0 1 1 3 1 4 1 0 0 1 1 1v1c0 1 1 2 1 2h5c2 2 10 1 13 1l1-1v1h2s1 0 2 1l1-3v-1c1-2 2-4 2-6v-1c1-1 1-3 2-4 0-1 1-2 1-3v-2c1-2 2-4 2-6 0-1 1-1 1-2l1-2c0-1 1-3 1-4v-1c1-1 1-1 1-2s0-1 1-2l1-1z" class="R"></path><defs><linearGradient id="z" x1="511.984" y1="730.97" x2="510.968" y2="697.17" xlink:href="#B"><stop offset="0" stop-color="#727071"></stop><stop offset="1" stop-color="#9a9998"></stop></linearGradient></defs><path fill="url(#z)" d="M487 663h1c1 1 1 2 1 3l1 1v2l1 1v2c1 0 1 2 1 3l1 2c0 2 0-1 0 1 1 2 2 5 3 7v1c0 1 0 2 1 2v1c0 1 1 3 1 4 1 0 0 1 1 1v1c0 1 1 2 1 2h5c2 2 10 1 13 1l1-1v1h2s1 0 2 1l1-3v-1c1-2 2-4 2-6v-1c1-1 1-3 2-4 0-1 1-2 1-3v-2c1-2 2-4 2-6 0-1 1-1 1-2l1-2c0-1 1-3 1-4v-1c1-1 1-1 1-2s0-1 1-2l1-1-6 16-14 43-4 11c0 1-1 4-2 5l-24-71z"></path><defs><linearGradient id="AA" x1="694.311" y1="327.728" x2="676.218" y2="309.784" xlink:href="#B"><stop offset="0" stop-color="#656464"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#AA)" d="M694 285l1 1-2 1v6c0 2 0 4 1 6l-3 6h-1c0 3 1 5 1 7 0 1 1 2 2 3 1 2 3 4 5 5h0c3 2 6 2 9 3l6 2 5 1c2 0 3 1 5 2h0v2c-9-3-18-2-26 3a34.47 34.47 0 0 0-12 12s-1 1-1 2l-3 5c0-3 1-6 2-9h-2c-1 2-1 4-2 7 0-4 0-7 1-10l-2-1c-2-2-4-3-6-3l-1 2h-1v-1l1-1-1-1 1-1c0-1 1-1 2-1 2 1 4 1 6 0v-1h-3c-1-1-3-1-4-1h-1l-1-1c0-1-1-2-1-3l-1 1v-2c0-1 1-1 1-2l1-1h0c1-2 3-4 5-4h0v-3l-1-1c0-2 0-4 1-6h1 1 1 1l1-1h0l1-1h0c0-1 1-2 2-2h1v-1-1l-1-1c-1-1 0-2 0-3l-1-1 1-2v-2l1-1c1-2 3-4 5-5l2-2h1 1l1-1z"></path><path d="M694 285l1 1-2 1v6c0 2 0 4 1 6l-3 6h-1c0 3 1 5 1 7 0 1 1 2 2 3 1 2 3 4 5 5h0c3 2 6 2 9 3l6 2c-2 1-7 1-9 1h-1 1c1-1 2-1 4-1v-1h-5l-6-3c-6-4-13-15-13-22-1-4 1-7 5-10l3-3h1l1-1z" class="B"></path><path d="M693 287v6c0 2 0 4 1 6l-3 6h-1c0 3 1 5 1 7-2-3-3-5-4-8s-1-7 1-10c1-2 4-4 5-7h0z" class="F"></path><path d="M690 305c1-3 0-7 1-9 1-1 1-1 1-2v-2l1 1c0 2 0 4 1 6l-3 6h-1zm-19 18c1-2 3-3 5-3 1-1 3 0 5 1l2 2c1 2 2 3 2 5 1 2-1 2 1 4h-1c0 1 0 2-1 3-1 2-2 3-2 6h-1v-1h-1l-2-1c-2-2-4-3-6-3l-1 2h-1v-1l1-1-1-1 1-1c0-1 1-1 2-1 2 1 4 1 6 0v-1h-3c-1-1-3-1-4-1h-1l-1-1c0-1-1-2-1-3 1-2 1-3 2-4z" class="B"></path><path d="M682 325h1c1 1 0 2 0 3 0 2-1 4-3 4l-1 1v-1h-3c-1-1-3-1-4-1h-1l-1-1h1 1c4 0 6 0 9-2 1 0 1-1 2-2l-1-1z" class="J"></path><path d="M671 323c1-2 3-3 5-3 1-1 3 0 5 1l2 2h-1l-2-2h-1v1l3 3 1 1c-1 1-1 2-2 2-2-1-3-1-5-1-1-1-1-1-2-1l1-3-1-1c-1 1-2 1-3 1z" class="L"></path><path d="M675 323l3 1c1 0 2 1 2 2h0l-4 1c-1-1-1-1-2-1l1-3z"></path><path d="M682 325l-3-3v-1h1l2 2h1c1 2 2 3 2 5 1 2-1 2 1 4h-1c0 1 0 2-1 3-1 2-2 3-2 6h-1v-1h-1l-2-1c-2-2-4-3-6-3l-1 2h-1v-1l1-1-1-1 1-1c0-1 1-1 2-1 2 1 4 1 6 0l1-1c2 0 3-2 3-4 0-1 1-2 0-3h-1z" class="G"></path><path d="M685 328c1 2-1 2 1 4h-1c0 1 0 2-1 3-1 2-2 3-2 6h-1v-1c0-1-1-2-1-3-1-1-1-1-1-2h1c3-2 4-4 5-7z" class="F"></path><path d="M703 324h5v1c-2 0-3 0-4 1h-1 1c2 0 7 0 9-1l5 1c2 0 3 1 5 2h0v2c-9-3-18-2-26 3a34.47 34.47 0 0 0-12 12s-1 1-1 2l-3 5c0-3 1-6 2-9h-2c-1 2-1 4-2 7 0-4 0-7 1-10h1v1h1c0-3 1-4 2-6 4-4 6-7 11-9l1-1h2l1-1h4z" class="E"></path><defs><linearGradient id="AB" x1="693.902" y1="327.544" x2="685.877" y2="344.617" xlink:href="#B"><stop offset="0" stop-color="#5d5c5b"></stop><stop offset="1" stop-color="#7a787a"></stop></linearGradient></defs><path fill="url(#AB)" d="M683 343v-1c1-2 1-7 4-8h1c4-1 6-5 10-6v2c-3 4-8 6-11 10v1c-1 1-2 2-2 4 0 0-1 1-1 2l-3 5c0-3 1-6 2-9z"></path><path d="M351 336l1-1v4 1h0c-1 1-1 2-1 2v1l5-3h2c1 0 2 1 3 1h1 1 0 1v1h1c1 2 2 4 3 5s1 2 1 4v1c0 1 1 1 0 2v1h-1v3l-1 1v1 2 1h0v2 1l1 1v3l1 1h0l1 2 3-3 1 1c0 1 0 2-1 4v2 1c0 1 0 1-1 2 0 2 0 2-1 3-3 0-2-1-4-1l-1-1h-1v1c1 1 3 2 4 3v1c1 0 1 1 1 2h1l1 2h0c0 2 0 3 1 4v1 2c1 0 1 1 1 2v1 1 3h1v4c-1 1 0 6 0 7h1c1-1 1-2 2-3 1-2 2-4 3-5 0-2 0-3 1-4h0c0-1 0-2 1-3l1-2c-1-2-1-1 0-2v-3-2h1v1c2 6 1 11-1 16-3 6-7 10-10 15-1 1-2 3-3 5h0c-2 4-2 7-2 11h-1c-1-2 0-5 0-7l3-12c1-7 0-15-1-23 0-1 0-4-1-5h0c-1-1-1-1-1-2-1-2-1-3-2-4-3-1-5-1-7-1h-1v-1l-4 2h-1c-2 1-7 4-8 6s-2 4-4 4l-1 1v-3h0c1-1 1-2 1-3 1-1 2-2 2-3l1-2c2-10 2-21 1-30-1-4-3-7-4-10 0-1-1-2-1-3 1-1 1-1 2-1 0-2 0-3-1-4l1-1c1 1 1 1 2 1h5l2-2z" class="Q"></path><path d="M372 372h0l1 1c0 2-1 7-3 9-1-1-1-1-1-2 0-4 0-5 3-8z" class="B"></path><path d="M356 379h0c4 0 7-2 10-2 0 1-1 2-2 3h-1c-2 0-6 1-8 3h0c-1 1-2 1-3 1h0l1-2 3-3z" class="M"></path><path d="M357 363l1-2c4 0 5-1 7-4-1 5-4 10-6 15 0-3 1-5-1-7 0 0-1-1-1-2z" class="T"></path><path d="M358 365c2 2 1 4 1 7-1 2-2 5-3 7l-3 3-1 2h0c1 0 2 0 3-1 4-1 8-1 11 1h0v2c-3-1-5-1-7-1h-1v-1l-4 2h-1c-2 1-7 4-8 6s-2 4-4 4l-1 1v-3h0c1-1 1-2 1-3 1-1 2-2 2-3l1-2c1 0 0 0 1 1l6-5c5-4 7-11 7-17z" class="I"></path><path d="M366 384h1c5 6 7 16 7 24 0 5-1 11-2 16 5-8 9-15 12-23v-1c1-3 1-6 1-8h0c2 6 1 11-1 16-3 6-7 10-10 15-1 1-2 3-3 5h0c-2 4-2 7-2 11h-1c-1-2 0-5 0-7l3-12c1-7 0-15-1-23 0-1 0-4-1-5h0c-1-1-1-1-1-2-1-2-1-3-2-4v-2z" class="G"></path><path d="M351 343l5-3h2c1 0 2 1 3 1h1 1 0 1v1h1l-1 2c1 1 2 4 3 5h0c1 3 1 5-2 8-2 3-3 4-7 4l-1 2-1-1c-2-2-3-5-5-7v1c-1-1-1-2-1-3-1-4-1-7 1-10z" class="B"></path><path d="M366 351c-1 1-2 3-3 4-2 2-3 1-5 1-1 0-2-1-2-1-1-1-2-2-1-3 0-1 1-2 2-2v-1l1-1c0 1 1 2 1 2h2c1 1 3 0 4 0l1 1z" class="L"></path><path d="M351 343l5-3h2c1 0 2 1 3 1h1 1 0 1v1h1l-1 2c1 1 2 4 3 5l-1 2-1-1c0-2-1-3-3-4-1-1-4-2-5-1-3 1-5 4-6 6v4h0v1c-1-1-1-2-1-3-1-4-1-7 1-10z" class="K"></path><path d="M351 336l1-1v4 1h0c-1 1-1 2-1 2v1c-2 3-2 6-1 10 0 1 0 2 1 3v-1c2 2 3 5 5 7l1 1c0 1 1 2 1 2 0 6-2 13-7 17l-6 5c-1-1 0-1-1-1 2-10 2-21 1-30-1-4-3-7-4-10 0-1-1-2-1-3 1-1 1-1 2-1 0-2 0-3-1-4l1-1c1 1 1 1 2 1h5l2-2z" class="H"></path></svg>