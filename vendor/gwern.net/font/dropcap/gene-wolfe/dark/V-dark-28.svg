<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="126 96 772 872"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#2b2929}.C{fill:#d7d6d6}.D{fill:#c5c3c4}.E{fill:#191818}.F{fill:#111}.G{fill:#2f2f2f}.H{fill:#b2b1b1}.I{fill:#252424}.J{fill:#616060}.K{fill:#555454}.L{fill:#bfbebe}.M{fill:#343334}.N{fill:#cccacb}.O{fill:#4e4d4d}.P{fill:#3e3e3e}.Q{fill:#d1cfd0}.R{fill:#8e8c8c}.S{fill:#999898}.T{fill:#3a3939}.U{fill:#deddde}.V{fill:#b9b8b8}.W{fill:#484848}.X{fill:#1d1d1d}.Y{fill:#6f6e6e}.Z{fill:#757474}.a{fill:#e6e6e6}.b{fill:#7a7979}.c{fill:#6a6969}.d{fill:#a09f9e}.e{fill:#5b5a5a}.f{fill:#888687}.g{fill:#817f7f}.h{fill:#a8a7a7}.i{fill:#aeadad}.j{fill:#202020}.k{fill:#434242}.l{fill:#939292}.m{fill:#010101}.n{fill:#ebeaea}.o{fill:#a5a4a4}.p{fill:#0b0b0a}.q{fill:#efeeee}.r{fill:#060606}</style><path d="M549 422l1-4c1 2 0 5 0 7h0c0-1-1-2-1-2v-1zM391 800c0 3 1 8-1 10-1-2 0-3 0-5v-1l1-4z" class="F"></path><path d="M504 336h2l3 3c-1 0-3 0-4-1l-1-2z" class="K"></path><path d="M607 437v2l1 1c0 1 0 2-1 4v4h-1c0-4 0-8 1-11z" class="F"></path><path d="M575 398v-1c1 1 1 1 2 1h0c1 1 1 2 2 3 0 1 0 2-1 3l-3-6z" class="W"></path><path d="M649 743c0-3 0-3 1-5h1c1 1 1 2 2 3h-3l-1 2z" class="H"></path><path d="M520 370v-3l-1-1c1-2 1-6 1-8 1 2 1 5 1 8-1 1 0 2-1 4z" class="F"></path><path d="M428 384l2-5h0c1 0 1 0 1 2-1 2-1 3-1 6v1c-1 0-1 0-1-1v-1l-1-2z" class="E"></path><path d="M431 391l1-1 1 1c0 1-2 3-2 5h1v2h-1l-1-1h-1l2-6z" class="I"></path><path d="M575 639l2-1 1 1-3 6h0c-1-2-1-3-1-5l1-1z" class="P"></path><path d="M543 441l1-10v-4l1 3v4 2c0 2 0 3-1 5h-1z" class="F"></path><path d="M526 367c0-1-1-1-1-2s0-4 1-6c1 2 1 4 2 6l-2 2z" class="B"></path><path d="M456 432c1-1 1-3 1-5l1 1-1 9c-1-1-1-1-2-1 0-2 0-2 1-4z" class="M"></path><path d="M514 458v-1-4l1 1h0v2c1 1 1 3 1 5l-1-1c0 2 0 4-1 5v-7z" class="F"></path><path d="M431 391c0-2 1-4 1-5l1-1c1 2 1 2 1 4h0l3-3h0c-1 2-2 4-4 5l-1-1-1 1z" class="X"></path><path d="M599 476c1-2 2-3 3-5l-2 8h0s-1 0-1 1l-1 1-1-2 2-3z" class="P"></path><path d="M599 476l-2 3-3 5c-1-1-1-1 0-2l-1-1 3-3 3-2zM375 790l1 3c1 2 1 5 1 7-1 0 0 1-2 2v-12z" class="E"></path><path d="M526 367l2-2 2 4c-1 1-2 2-1 4h-1v-1l-2-4v-1z" class="P"></path><path d="M356 800h1c0 3 1 4 3 6h-1l-1 1c1 1 2 2 2 3h0c-1-1-2-2-3-4h0c-1-2-1-4-1-6z" class="F"></path><path d="M549 383c1 3 1 4 0 7v2c-1 2-1 4-1 6 0-4-1-8-2-11l2-1h1v-3z" class="E"></path><path d="M631 436h0c0-2-1-2-2-3h0c3 1 4 2 7 2h3l-6 3c0-1-1-1-2-2z" class="Q"></path><path d="M406 435v-1-2l-1-2v-1c-1-1 0-2 0-3l-1-1c0-2-2-6-3-9 2 1 3 3 4 5v2 1 2c1 1 0-1 1 1 0 1-1 3 0 4v4z" class="F"></path><path d="M575 394c0-1-1-3-1-5l8 16v-1h-1-1v2l-2-2c1-1 1-2 1-3-1-1-1-2-2-3 0-1-1-2-2-4z" class="K"></path><path d="M537 349h1s1 1 1 2 0 3 1 4v1c0 1 0 3 1 4h-1l-1-3h-1 0l-1-8z" class="j"></path><path d="M548 648c2 2 2 5 2 8 0 2-1 3-2 5v2-15z" class="T"></path><path d="M611 703c1 0 2 0 2 1 1 3 1 6 1 9h0c-1-1-2-3-2-5-1-2-2-3-1-5z" class="B"></path><path d="M402 798h0 0c2 3 2 8 1 11h0c-2-4-2-7-1-11z" class="i"></path><path d="M547 380c0-1 1-2 2-3 0 1 0 1 1 2 0-1 0-1 1-1 0 1-1 3-2 5v3h-1l-1-4v-2z" class="f"></path><path d="M605 747c1 1 1 1 3 1v-1l1 1c0 1 1 2 0 4-1 0-1 1-3 0-1 0-2-2-3-3 1-1 1 0 1-1 1 0 1-1 1-1z" class="G"></path><path d="M420 433l1 1c0-2 1-3 2-4 0 2-1 5 0 6-1 2-2 4-4 6l1-6c0-1-1-2 0-3z" class="e"></path><path d="M477 381h0 3l4-4h1c-3 4-5 7-7 11 0-2-1-4-1-7z" class="l"></path><path d="M649 743l1-2h3c0 3 0 8-2 10-1-2-1-6-2-8z" class="N"></path><path d="M550 466l1 2h0c1 3 3 4 2 7-2-2-4-5-6-8 1 0 2 0 3-1z" class="Z"></path><path d="M629 436h2c1 1 2 1 2 2l-6 2-3-1-1-3h3 3z" class="U"></path><path d="M565 606h1c0 3 1 5 1 8-1 2-1 3-1 5v3c-1-5-3-10-2-15l1-1z" class="K"></path><path d="M491 493c1 3 0 6 1 8 0 2-1 3-2 5l-1-11h1l1 1v-3z" class="k"></path><path d="M424 224l-3 1c-3 0-6-2-9-3v-1h3c1 1 4 1 6 2 1 0 2 0 3 1h0z" class="H"></path><path d="M593 393c0-1 0-1-1-2 0-2 0-4 1-6h2c0 1 0 1 1 1h1l2-1v2l1 4h-2v-1c-1-1-3-2-4-3h0l-1 6z" class="B"></path><path d="M438 175c-4-2-6-6-8-9l9 6h-1c1 1 3 2 4 3h-4z" class="H"></path><path d="M425 471c-2-2-3-6-4-8l-2-8c-1-1-1-2-1-4 1 3 3 5 4 8-1 2 1 5 2 8 0 1 1 2 1 4z" class="F"></path><path d="M575 398h-1l-1-2c-2-3-3-6-4-9 3 1 4 6 6 7 1 2 2 3 2 4h0c-1 0-1 0-2-1v1z" class="M"></path><path d="M419 394c1-3 1-7 3-10l-1 3c0 1 0-1 0 1-1 1-1 2-1 3v3 1h0 1 0 1c0 2 0 3-1 5-1 1-1 4-1 6h-1c-1-4 1-8 0-12z" class="G"></path><path d="M438 168c1 0 2 1 3 1l2 2v1c1 2 2 2 4 2v1l-3 1-2-1c-1-1-3-2-4-3h1 2l-3-3v-1z" class="L"></path><path d="M494 404v2c-3 3-6 4-9 5-1-1-2-1-2-2 1 0 2-1 3-1 1-1 3-2 4-2h1c1-1 2-1 3-2z" class="D"></path><path d="M446 464l2-2s0 1 1 1c0 1 0 1 1 1s2 0 3 1c-1 2-1 4-1 7l-6-8z" class="h"></path><path d="M466 418l-1-8-1-11v1c1 3 2 5 2 7 1 5 1 8 2 12h0l-1 2-1-3z" class="E"></path><path d="M624 427c2 2 4 4 5 6h0 0c1 1 2 1 2 3h0-2-1c-2-1-3-2-4-4v-2-1-2z" class="L"></path><path d="M503 451v-4c1-3-1-8 0-10 2 0 2 1 3 2v1c0 3 1 5 1 8v-1c-2-1-2-4-3-6v7l-1 3z" class="F"></path><path d="M524 309c-1-1-1-1 0-2h2v1 2l4 3c-3 0-5 0-8-1h0c-1-1-1-1 0-3h2z" class="B"></path><path d="M522 309h2l1 2h-1-2v1h0c-1-1-1-1 0-3z" class="E"></path><path d="M507 941v1c2-2 3-5 5-7h1c-1 4-3 8-4 10-1 1-2 1-3 1l-1-1h2c-1-2-1-2-1-4h1z" class="d"></path><path d="M576 427c1 4 1 8 1 12 0 3 0 6-1 8v-1l-2-6-1-10c0 1 1 3 1 4h1c-1 1-1 1 0 2v1 1l1-1v-10z" class="M"></path><path d="M613 471v-4-8l4 8-1 2v1c-1 0-1 0-1 1v2h-1c0-1-1-1-1-2zm11-11c3 6 5 11 7 17-1-1-3-6-5-7-2-3-3-6-2-10z" class="G"></path><path d="M413 360l1 1v-2l1-1c1 3 1 8 0 11 0 2 0 3-1 4h0c-2-3-1-10-1-13z" class="I"></path><path d="M572 457c1-2 1-6 2-8h1l1 1v1c-1 4-2 10-5 13l1-7z" class="R"></path><path d="M501 382c0-1 0-3 1-4l1-3h0v4 1 3 1l1-1v-2c1-1 0-2 1-4 0 1 1 2 1 3l-1 1h1 0 1v6c-1-1-1-2-2-3h0c-2 1-2 1-3 0v-1l-1-1z" class="G"></path><path d="M495 489h1v3l-4 9c-1-2 0-5-1-8 0-1 1-3 1-5 1 2 1 3 2 4v-1c0-1 0-1 1-2h0z" class="K"></path><path d="M610 241c3 0 7-1 10-2l-10 9h0-1l1-1h1v-1l-1-1 1-1c-1-2 0-1-2-2l1-1z" class="Q"></path><path d="M614 271c0-2-1-3-2-4l1-1 1 1c3 2 5 4 7 6v1 2h-2 0c-1 0-2-2-3-3l-2-2z" class="V"></path><path d="M501 382c0 1 0 3-1 4 0-5 1-11 2-15 0-2 0-3 1-5 0-1 1-2 1-3v10c0 1 0 1 1 2h-1c0 1 1 2 0 3v2h-1v-1-4h0l-1 3c-1 1-1 3-1 4z" class="j"></path><path d="M434 394l1-1c0-1 0-1 1-1 0-1 1-1 2-2l1 2s-1 0-1 1c0 3 0 4-1 6h-1l-1 4-1-1c-1-2 1-6 0-8z" class="X"></path><path d="M469 419l-1-2c0-4 1-8 0-12-1-3-4-10-3-13v-1c1 0 1 0 1-1v2h0c4 8 2 17 4 25h0l-1 2z" class="F"></path><path d="M536 373c2 2 5 6 8 7h1l2 2 1 4-2 1c-2-5-5-7-9-9 0-1-2-3-3-3 1 0 2 0 3 1v1c2 0 2 1 3 1-2-2-3-2-4-5z" class="j"></path><path d="M614 725l2 1c1 4 2 9 2 14v3c-2 0-3 0-3-1l1-1v-8c-1 0-1-3-1-3l-1-5z" class="J"></path><path d="M393 784c-1-4-2-9-2-13 1-1 1-2 4-2-1 1-1 2-1 3v6h0c0 1 1 5 1 6h-2z" class="E"></path><path d="M650 722l1-2 2 6v3h1v-1h0v-2c1 4 1 7 0 11l-3-6v-1l-1-8z" class="C"></path><path d="M544 441v8c0 3 0 6 1 8h1c0 3 1 6 1 9-2-3-3-5-4-9v-3-13h1z" class="I"></path><path d="M528 636h1c1 4-1 9-2 14h0c-1-1-1-3-1-4-1-4-1-7 2-10z" class="D"></path><path d="M537 568c1 2 1 3 1 5-1 2-2 6-3 7h-1c0 1-1 2-2 4-1 1-1 2-3 3h0c3-6 6-12 8-19z" class="J"></path><path d="M614 746h0c1 0 1 1 2 1 1 4 0 10-1 13l-1 1c-1-3-1-11 0-15z" class="h"></path><path d="M529 413c1 0 2-1 2-2v-1h2c-1 4-2 9-5 13l-1-2c0-1 1-2 1-3-1 1-2 1-2 1h-1c0-1 1-4 1-5s0-2 1-2v1c0 2 0 3 1 4h0c1-1 1-2 1-3v-1zm-34 5l3-3 1 2c0 1 1 2 2 4v2 1h0c0 1 1 2 0 3-3-2-5-5-6-9z" class="H"></path><path d="M501 424c-2-1-3-2-3-3 0-2 0-3 1-4 0 1 1 2 2 4v2 1z" class="N"></path><path d="M384 806h1 1c2 4 0 10-1 14h0-1c0-4-2-11 0-14z" class="Z"></path><path d="M421 400v1l1-5c1 0 0 0 1-1v-2h0l-1 1c-1-2-1-5 0-7l1-2 1 1v-1l1-1h0v1c-1 2 0 3-1 5h0v3c-1 2-1 6-1 7-1 3-1 4-2 6h-1c0-2 0-5 1-6z" class="E"></path><path d="M366 802h1c2 5 1 11 0 16h-1c-1-5-2-12 0-16z" class="Y"></path><path d="M532 365l2-1c1 1 1 2 1 3l2 2 1 1c0 2 1 3 2 5h1c1 2 3 3 3 5-3-1-6-5-8-7-2-3-3-5-4-8z" class="O"></path><path d="M571 447c1 2 1 4 1 6l-1 1v2c-3 5-9 9-12 13h0c-1-3 0-4 2-6l1-1 2-1c2-1 2-2 4-3 1-1 2-3 2-4 1-2 1-3 1-4v-3z" class="l"></path><path d="M634 747h0c3 6 2 10 0 15l-1 1h0c-1-5-2-12 1-16z" class="L"></path><defs><linearGradient id="A" x1="545.364" y1="453.721" x2="550.119" y2="464.507" xlink:href="#B"><stop offset="0" stop-color="#4b4b4a"></stop><stop offset="1" stop-color="#6f6e6e"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M545 453l2-2h1v6c0 1 1 2 1 3 1 1 1 3 1 4v2c-1 1-2 1-3 1v-1c0-3-1-6-1-9l-1-4z"></path><path d="M452 497l-1-5c-1-2-1-5-2-7-2-4-2-9-5-13 0-1-1-3-2-4h0v-1c5 7 8 14 10 22l1 6-1 2z" class="F"></path><path d="M399 764c2 8 6 17 7 26 0 1 0 3-1 5-2-2-2-9-3-12l1-1v-3c-1-2-2-4-2-6s-1-3-1-5v-1l-2-1c1 0 1-1 1-2z" class="j"></path><path d="M433 349c2 2 1 5 2 7l1 1 1 1 2 11h-1c-2-5-5-11-8-15 2-2 3-3 3-5z" class="i"></path><path d="M548 663l-1 3h-1c-1-3-2-9-1-13 0-2 1-4 3-5v15z" class="U"></path><defs><linearGradient id="C" x1="533.292" y1="350.726" x2="531.684" y2="363.46" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#3d3d3d"></stop></linearGradient></defs><path fill="url(#C)" d="M532 365c-2-6-2-12 0-17v2c1 5 2 9 3 13 1 2 2 4 2 6l-2-2c0-1 0-2-1-3l-2 1z"></path><path d="M605 734c0-3 1-6 0-8v-1-1l-1-1c1-2 1-5 3-6h0v3l1 1v2l1-1c0-2 1-5 1-7h1 0l-1 2v4c-2 2-2 8-2 10h0c-1-1-1-2-1-4v1l-1 1c0 2 0 3-1 5zm-16-268h1c-2 6-5 11-8 16-2 3-5 6-7 10 0 1-1 2-2 3h-1c3-7 7-11 11-17l6-12z" class="E"></path><path d="M495 282c2 0 2 1 4 1 0 1 0 1 1 2h-2l-1 2c1 1 1 2 2 3h-1c0 2 1 3 1 4 1 1 2 1 3 1h1c1 1 2 1 2 3l-1 1c0 1 1 1 2 2h1c-2 0-3 0-4-1 1 0 0 0 1-1-2-1-4-2-6-4-1-2-1-4-2-6s-1-5-1-7z" class="X"></path><path d="M442 460c1-2 1-3 1-5l1-1c0 3 1 5 3 7 0 0 0-1 1-2h0v1h1v-4h1v4h1v-1l1-1v1c1 1 1 2 2 3l-1 3c-1-1-2-1-3-1s-1 0-1-1c-1 0-1-1-1-1l-2 2-4-4z" class="l"></path><path d="M571 441l2-2h0l1 1 2 6v1c0 1 1 2 0 3l-1-1h-1c-1 2-1 6-2 8l-1-1v-2l1-1c0-2 0-4-1-6v-6z" class="Z"></path><path d="M537 378c4 2 7 4 9 9 1 3 2 7 2 11-1 3-2 6-4 8-1-2-1-2 1-4h0c1-3 1-5 1-7 0-3-1-6-2-8-1-3-6-6-7-9z" class="S"></path><path d="M630 675l1-1c0-1 0-2 1-4 1 3 1 7 2 9 2 5 5 9 3 15 0-1 0-2-1-2 0-2-1-3-1-4s0 0-1-2h0l-2-4-2-7z" class="G"></path><defs><linearGradient id="D" x1="528.972" y1="402.498" x2="527.997" y2="413.5" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#D)" d="M528 402l1-3h1c0 2 0 3 1 4v-1c2 1 1 2 2 4v4h-2v1c0 1-1 2-2 2v1c0 1 0 2-1 3h0c-1-1-1-2-1-4 1-2 1-5 1-7v-2h0v-2z"></path><path d="M531 402c2 1 1 2 2 4v4h-2v1c0 1-1 2-2 2l2-10v-1z" class="S"></path><path d="M561 603c2 1 3 2 4 3l-1 1v-1l-1 1c-2 3-2 5-2 8 0 1-1 3-2 5l-1 11c-1-2 0-2-2-5 1-4 0-8 3-12 0-1 0 0 1-2v-3l1-6z" class="P"></path><path d="M618 461c-2-3-4-5-4-8 0-4 1-8 3-10 1-1 2-1 4-1 0 0-1 1-1 2h0l-3 6c0 2 0 5 1 7v4z" class="E"></path><path d="M414 247c-2-1-3-3-4-4 5 0 9 3 15 1h0c1 1 1 1 1 3h-1v1h-3l-6 1-2-2z" class="C"></path><path d="M613 742l-1-1v-4c0-4 0-9 1-12h1l1 5s0 3 1 3v8l-1 1h-2z" class="Y"></path><path d="M616 733l-1 5h0l-2-1c1-2 1-5 1-7h1s0 3 1 3z" class="R"></path><path d="M568 89l8-6c0 3 0 7 1 10 2 5 4 9 6 14l-1 1-2-4-5-11-1-4-2 2h0l1-1v-1h0v-1l2-1c1-1 0-1 0-2-2 1-4 4-7 4z" class="a"></path><path d="M503 937c1 1 2 2 2 4h1c0 2 0 2 1 4h-2l1 1h-6l-3-5c2-3 3-3 6-4z" class="M"></path><path d="M556 629v-3c2 3 1 3 2 5-1 6 0 14-2 20v1c0 1-1 3-2 4l-2-7c1-1 1 0 1-1v-1h1v1h1c0-5 0-11 1-16h1c0-1-1-2-1-3z" class="O"></path><path d="M495 404h0c1-1 2-2 2-3l1 1v2 4 2l1 1v-1l2-3c0 3-1 3-2 5h-1-1l1 3-3 3c-1-4-1-7-1-10v-2-2h1z" class="b"></path><path d="M494 408h1v6h1v-3h1v1l1 3-3 3c-1-4-1-7-1-10z" class="d"></path><path d="M582 590l2-1c0 3 0 5 2 7l1 3h0c1 1 0 3-1 5l-1 3-1 3c0 2 1 4 0 6-1-1-1-2-1-4h0c1-1 0-9 0-10 0-4 0-8-1-12z" class="B"></path><path d="M585 607c0-3 0-7 1-11l1 3h0c1 1 0 3-1 5l-1 3z" class="X"></path><defs><linearGradient id="E" x1="561.766" y1="406.006" x2="572.756" y2="406.474" xlink:href="#B"><stop offset="0" stop-color="#111212"></stop><stop offset="1" stop-color="#343233"></stop></linearGradient></defs><path fill="url(#E)" d="M573 415c-1-1-3-4-4-6l-1-1c-2-3-4-5-6-7h-1c1-2 1-3 1-4h0c3 3 6 5 8 9l1 1c1 2 2 3 3 5h0c1 2 2 3 2 5h0 0c-1 0-1-1-1-2h-2z"></path><path d="M346 693c0 2 1 4 1 5l1 1c0 2 0 5 1 8 0 1 0 3 1 5s0 7 0 10h-2 0c-2-3 0-14-1-18-1-3-1-7-1-11z" class="E"></path><path d="M350 712c1 2 0 7 0 10h-2l2-10z" class="M"></path><path d="M329 617c1 1 1 2 1 3v1c1 1 1 0 1 2h0s1 1 1 2c0 2 1 4 2 6 1 0 1 2 1 3h0v1c1 2 2 4 4 6v1l-1 1-3-6h-1 0c-1-1-2-2-2-3l-2-7c0-2-1-3-2-4 0-2-1-4 0-6h1z" class="E"></path><path d="M329 617c0 3 2 6 1 10 0-2-1-3-2-4 0-2-1-4 0-6h1z" class="I"></path><path d="M552 639c1-1 3-3 3-4 1-1 0-4 1-6h0c0 1 1 2 1 3h-1c-1 5-1 11-1 16h-1v-1h-1v1c0 1 0 0-1 1 0-1-2-3-3-4h-1c-1 0-1 0-2-1v-1c1 1 1 0 2 1v-4h1v3h1v-5h1v6h1v-5z" class="Z"></path><path d="M490 355h1c1-1 1-3 2-5v-1l1-1h0v4c0 3-1 6-1 9 1-1 2-3 3-5 1 2 1 2 1 4 0 1 0 3-1 4-1-1-1-1-3-1-1 2-2 3-4 5 1-2 3-5 2-7v1c-1 0 0 0-1-1l-1 4c-1-2 0-4 0-5 0-2 0-3 1-5z" class="I"></path><defs><linearGradient id="F" x1="520.964" y1="381.023" x2="525.289" y2="393.72" xlink:href="#B"><stop offset="0" stop-color="#2e2e2d"></stop><stop offset="1" stop-color="#535252"></stop></linearGradient></defs><path fill="url(#F)" d="M522 366c1 2 1 4 1 7v1 2c0 1 1 2 0 3v1h1v-3c0-1 1-5 0-6 0-1 0-1-1-2 0-1 0-2 1-2l1 4c0 2-1 5 0 7l-1 5c0 2 0 5 1 7l-1 7v5-7l-1 1v4l-1-1c1-1 1-3 0-3v-18-6-6z"></path><path d="M473 368h3l1 1c1 3 1 7 2 10h1v-1c1-1 2-1 3-2l4-2 1 1c-1 0-2 2-3 2h-1l-4 4h-3 0c-2-4-3-8-4-13z" class="f"></path><defs><linearGradient id="G" x1="465.848" y1="452.265" x2="459.287" y2="463.012" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#858583"></stop></linearGradient></defs><path fill="url(#G)" d="M459 449l1-1h1l2 2h-1c1 4 2 7 3 11 0 2 1 6 2 9h0c-6-6-7-13-8-21z"></path><path d="M565 341l1-1 1 2h1v-1c1 1 2 3 2 4 1 2 2 5 2 7 1 2 0 5 1 7v2h0l-5-12h0c-2-1-3-2-3-3 0-2 0-3-1-5h1 0z" class="c"></path><path d="M565 346c0-2 0-3-1-5h1 0l3 8h0c-2-1-3-2-3-3z" class="F"></path><path d="M372 717h0c0 1 1 2 1 3s0 0 1 2v1c1-2 0-2 0-4 0 0 0-2 1-2v-1h-1v-2c-1-2-1-5 0-7l1-1-1-1h1l1 1v7h0v-10c0-2 0-4 1-5l-1-1h-1 0v-4l-1-2v-1-3c1 2 2 5 2 7v2h1v1h0c-1 7-1 15 1 22v4c-1-1-2-3-3-4v5h-1c-1-1-2-3-2-5v-2z" class="E"></path><path d="M610 288c2-1 3-2 5-2 1 1 2 0 3 0l5 2h1c1 1 1 2 1 4v1h0l2 1 1 1c1 1 4 3 5 4h1c1 2 1 4 2 5-7-9-14-13-26-16z" class="N"></path><path d="M529 449c3 4 3 11 4 16 0 3 0 7 1 10v5 3h0c-1 1-1 2-1 4l-1-1v-4-4h0v-1-2-2h-1v-3c-1-1 0-5 0-6l-2-15z" class="B"></path><path d="M493 471v2h1c-1 5-2 10-2 15 0 2-1 4-1 5v3l-1-1h-1c0-7-1-12 1-18l3-6z" class="e"></path><path d="M493 473h1c-1 5-2 10-2 15 0 2-1 4-1 5v3l-1-1c0-4 0-8 1-12l2-10z" class="T"></path><path d="M633 396c4 1 8 1 13-1 1-1 3-2 5-2l-5 7-4 4v-1c-1 0-1 0-2-1v-1-1h-4l-2-1h0 5v-1h-2c-2 0-3-1-4-2z" class="D"></path><path d="M640 400h6l-4 4v-1c-1 0-1 0-2-1v-1-1z" class="i"></path><path d="M436 472v-1c0-3-4-6-5-8h1c3 3 6 6 8 10 2 3 6 8 5 11l1 1v1h0c0 1 1 2 0 2h0s-2-5-2-6c0 3 1 5 1 7h0c-2-6-5-12-8-17h-1z" class="F"></path><path d="M581 489v1c-1 2-3 3-3 5-1 1-2 3-2 5l-1 1c-1 2-3 4-4 6-2 2-4 3-6 5h-3 0l2-1c2-1 3-2 4-4v-1c0-1 2-4 2-5 2-4 7-8 11-12z" class="T"></path><path d="M434 394c1 2-1 6 0 8l1 1c-1 2-2 6-2 9l-2 4-2-2c0-1 0-2-1-3l3-6c-1-2 0-5 1-7v-2l2-2z" class="M"></path><path d="M431 405h0c0 2 0 4 1 6v1h1l-2 4-2-2c0-1 0-2-1-3l3-6z" class="k"></path><defs><linearGradient id="H" x1="622.202" y1="470.652" x2="615.929" y2="484.142" xlink:href="#B"><stop offset="0" stop-color="#262727"></stop><stop offset="1" stop-color="#626260"></stop></linearGradient></defs><path fill="url(#H)" d="M617 467c3 6 4 12 6 18v1c-1 0-1 1-2 2l-1 6c-1-4 1-9-1-13h1c-1-2-2-4-2-6-1 1-1 2-2 3v-2l-1-1v-2-2c0-1 0-1 1-1v-1l1-2z"></path><path d="M615 471h1c0 1 2 4 2 4-1 1-1 2-2 3v-2l-1-1v-2-2z" class="W"></path><defs><linearGradient id="I" x1="423.075" y1="416.165" x2="428.506" y2="428.519" xlink:href="#B"><stop offset="0" stop-color="#504e4e"></stop><stop offset="1" stop-color="#707071"></stop></linearGradient></defs><path fill="url(#I)" d="M428 411c1 1 1 2 1 3l2 2-8 20c-1-1 0-4 0-6-1 1-2 2-2 4l-1-1 1-3c1-1 1-1 1-2l1-1v-2c2-2 2-3 3-6l2-8z"></path><path d="M393 758c3 1 5 3 6 6 0 1 0 2-1 2h-1c-1 1-2 0-3 0v1h-2c0 1-1 1-1 2-1-2-1-4-2-5l-1-4c1 0 2-1 3 0h0l1-2h1 0z" class="G"></path><path d="M392 758h1c0 2 1 3 1 4l-1 2h-2v-3c-1 1-1 1-1 2l-1 1-1-4c1 0 2-1 3 0h0l1-2zm224-408c-3-6-1-16-1-23h1c1 2 0 7 0 10 1 8 3 16 5 23-1-1-2-2-3-4v-1-1c-1-1-1-2-1-3l-1-1z" class="T"></path><defs><linearGradient id="J" x1="478.698" y1="389.235" x2="489.824" y2="404.479" xlink:href="#B"><stop offset="0" stop-color="#959596"></stop><stop offset="1" stop-color="#c6c3c3"></stop></linearGradient></defs><path fill="url(#J)" d="M484 384l2 2c-2 5-4 12-2 17v1c0 1 1 3 2 4-1 0-2 1-3 1l-1-1c0-1-1-3-1-4-2-8-1-14 3-20z"></path><path d="M521 366h1v6 6c-1 2-1 4-1 6l-1 1c0-1-1-4-1-5v-1l-1 1v2l-1-1v-2l-1-1c0 2-1 5-1 7h0v-7-1c1 0 1-1 1-2v-1-3c0 2 0 2 1 3l1-6v1c1 0 1 0 2 1 1-2 0-3 1-4z" class="j"></path><path d="M518 380c0-1 0-3 1-5 0 1 0 2 1 3h0c1-2 0-4 2-6v6c-1 2-1 4-1 6l-1 1c0-1-1-4-1-5v-1l-1 1z" class="M"></path><path d="M611 537h1v1c-2 6-5 12-7 18-2 2-3 5-5 7h0c0-1 0-3 1-5 2-6 6-15 10-21z" class="P"></path><path d="M483 340s0 2 1 2c1-2 1-2 1-4 2-1 0 0 2 0h0c1 1 0 2 0 4s1 7 0 8v3h0v2c-1 1-1 2-1 4l-1-1c-1 1-1 3-2 4v1c-1-2 0-6 0-8l1-5-1-1v-1l-1 1c0-2 1-3 2-5h-1v-4z" class="F"></path><path d="M484 344l1-1c0 3-1 5-1 7l-1-1v-1l-1 1c0-2 1-3 2-5zm-1 11c2-2 2-6 3-8 1 1 0 4 0 5l1 1h0v2c-1 1-1 2-1 4l-1-1c-1 1-1 3-2 4v1c-1-2 0-6 0-8z" class="I"></path><defs><linearGradient id="K" x1="549.966" y1="367.83" x2="552.486" y2="375.522" xlink:href="#B"><stop offset="0" stop-color="#615f5f"></stop><stop offset="1" stop-color="#767777"></stop></linearGradient></defs><path fill="url(#K)" d="M550 362h1c0 2 0 3 1 4v-1l2 2v-1c1-1 1-1 2-1-1 5-3 9-5 13-1 0-1 0-1 1-1-1-1-1-1-2-1 1-2 2-2 3v-7l1-2c2-2 2-6 2-9z"></path><path d="M550 362h1c0 2 0 3 1 4v3c-1 1-2 3-3 5v1l-2-2 1-2c2-2 2-6 2-9z" class="K"></path><path d="M589 448h2c-1 2-1 5-1 7-3 6-6 12-10 16v-8l2-1c1-2 3-4 4-6 1-1 1-1 1-2v-3l1-1v2h1v-4z" class="V"></path><path d="M537 452l1-1c-1-5-4-10-3-14v2s1 1 1 2v1h1v-2c1 3 1 6 2 9 0-3-1-10 0-12 1 4 2 8 2 11v5c1 1 1 1 2 1v3 2 6l-6-13z" class="E"></path><defs><linearGradient id="L" x1="491.577" y1="342.436" x2="486.259" y2="361.657" xlink:href="#B"><stop offset="0" stop-color="#0f0f0e"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#L)" d="M487 353c0-2 0-3 1-4 1-3 2-7 2-10 1 1 0 1 1 2 1 4 0 10-1 14-1 2-1 3-1 5 0 1-1 3 0 5-1 1-1 1-1 2h-1l1-2c-1-1-1-2-2-2v-1h0-3c1-1 1-3 2-4l1 1c0-2 0-3 1-4v-2z"></path><path d="M478 338c0-5 1-10 3-14 0 3-1 9 1 12 1-1 1-2 2-3 0 2-1 4-1 6v1 4h1c-1 2-2 3-2 5l-1-1v-2l-1-1v1 4h-1c0-4-1-8 0-11 0-1 0-1-1-2v1z" class="F"></path><path d="M480 346h-1v-1c0-2 1-6 2-7l2 1v1 4h1c-1 2-2 3-2 5l-1-1v-2l-1-1v1z" class="X"></path><defs><linearGradient id="M" x1="463.225" y1="353.42" x2="471.193" y2="373.113" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#M)" d="M463 357c1 0 1-4 1-4v1c0-1 0-1 1-2l1-1v-1l1 1c-1 4-1 8 1 12 0 3 2 5 3 8-1 2-1 4-1 6-3-6-6-13-7-20z"></path><path d="M438 357v-1h0 0l2 1 1-1v2 12l1 1c0 1 0 1-1 2v3h1c-1 2-2 2-3 3l-2 2h-1c2-5 3-7 2-12h1l-2-11 1-1z" class="S"></path><path d="M438 357v-1h0 0l2 1c0 1 0 2-1 3 0 3 1 6 0 9h0l-2-11 1-1z" class="g"></path><path d="M469 533c0-6 1-12 0-18-1-8-4-15-4-22h1l6 36c0 3 0 5 1 7v5c-1-2-2-10-4-11v3zm-5-228c1-2 3-4 6-5 0 0 1-1 1-2h2c1 1 1 1 2 1h1 2 0c-1 1-2 2-4 3-1 0-2 1-3 2h0c-3 2-5 5-7 7-1 0-1 1-2 1-2 1-4 3-5 4v-2l1-2v-1l1-1h3v1c1-1 3-2 3-4l-1-1v-1z" class="F"></path><path d="M329 587h1c2-4 1-8 4-12 1 4-4 10-2 14h0v1 3h0v1c1 4 3 7 3 11 0-1-1-2-1-3s0 0-1-1v-2l-1-5-1-1v-2h-1v7 1c0 1 0 5 1 6 0 1 0 1 1 2h0v5h0c0 1 0 1 1 2v3h1v1 1h0c1 3 3 7 4 10h0c1 1 1 1 1 2h0c-1-1-2-3-3-5-2-3-4-6-5-9l-1 1v-2h0v-1-2c1 1 0 1 1 1v1l1-1c-1-2-1-3-1-5-1-8-2-14-2-22z" class="E"></path><path d="M539 606l4-4 2 1c1 2 1 4 0 5 0 1 0 2-1 3h0v3h1v2c-1 2-2 6-2 9h-1l-1 1v-5c1-5 1-9 1-13l-3 4c-1 1-1 1-2 3l1-4 1-4v-1z" class="G"></path><path d="M539 606l4-4 2 1c1 2 1 4 0 5 0 1 0 2-1 3h0v-2h-1c0-2 0-2-1-3-1 1-1 1-3 1v-1z" class="M"></path><defs><linearGradient id="N" x1="521.494" y1="918.091" x2="507.542" y2="931.398" xlink:href="#B"><stop offset="0" stop-color="#393938"></stop><stop offset="1" stop-color="#6b696a"></stop></linearGradient></defs><path fill="url(#N)" d="M518 915l3 2-8 18h-1c-2 2-3 5-5 7v-1c1-2 2-6 4-8l7-18z"></path><path d="M573 330c1 2 3 5 3 9v1c-1 2 2 6 0 7v1c-2 2-2 3-2 5v1h0c-1 1-1 3-1 5-1-2 0-5-1-7 0-2-1-5-2-7 1-1 1-1 1-2s0-1 1-1v-3-2l1-1v-6z" class="W"></path><path d="M574 354c-1-5-1-9-1-14 1 2 3 5 3 7v1c-2 2-2 3-2 5v1z" class="g"></path><defs><linearGradient id="O" x1="452.734" y1="495.466" x2="457.404" y2="527.942" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#373737"></stop></linearGradient></defs><path fill="url(#O)" d="M453 495c0 1 1 5 2 5 1 2 1 6 1 7l2 12v13c-1-1-1-2-1-3l-1-2v-1c-1-2-1-4-1-6l-3-23 1-2z"></path><path d="M521 125c2 1 4 3 6 4 0-1 1-1 1-2l2 2c2 0 4 0 6-1 1 0 1 0 2 1v1l-3 3-1 1-2 2h-1v-1l1-1c-2 0-2 1-3 2 0-1 0-2-1-3h-1l-1 1c0-1-1-2-2-2v-1h0c-1 0-2-1-3-1h-1v-4l1-1z" class="a"></path><defs><linearGradient id="P" x1="612.062" y1="478.126" x2="614.97" y2="488.22" xlink:href="#B"><stop offset="0" stop-color="#525151"></stop><stop offset="1" stop-color="#797878"></stop></linearGradient></defs><path fill="url(#P)" d="M613 471c0 1 1 1 1 2h1v2l1 1v2l-1 6c0 2 0 4 1 5l-1 5-2-3c-1-1-3-1-4-1v-1l1-3 2-9 1-6z"></path><path d="M613 471c0 1 1 1 1 2v3c1 0 0 0 1 1l-1 1h0c-1-1-1-1-2-1l1-6z" class="O"></path><path d="M590 210c2 0 2 0 4 1 1 1 1 2 3 1h1 1c3 2 5 5 8 7l3 2 2 1h0c-3 1-5 2-7 2-2-1-2-2-3-3-4-4-10-7-12-11z" class="D"></path><path d="M602 221c0-1-1-2-1-3l1-1 5 3s1 0 1 1h1 1l2 1h0c-3 1-5 2-7 2-2-1-2-2-3-3z" class="H"></path><path d="M636 707c2 0 3 0 4 1s2 1 3 1l1 11 1 10c1 1 1 3 1 4l3 3c-1 0-1 1-1 2l-1-1-2-5-1 10c-1-5-1-12-1-18-1-4-2-9-3-13-1-1-3-3-4-5z" class="M"></path><defs><linearGradient id="Q" x1="523.807" y1="532.236" x2="512.308" y2="534.085" xlink:href="#B"><stop offset="0" stop-color="#5c5c5c"></stop><stop offset="1" stop-color="#757374"></stop></linearGradient></defs><path fill="url(#Q)" d="M519 524c0-1 0-3 2-4h2 1c-5 11-8 21-9 32l-2-9 1-1v-1c1-1 1-2 0-3v-2l5-12z"></path><path d="M433 210c2 0 4-1 6-2l1 1-1 1v1c-4 2-7 5-10 7s-5 4-8 5c-2-1-5-1-6-2 7-2 11-6 17-11h1z" class="Q"></path><defs><linearGradient id="R" x1="556.26" y1="354.343" x2="563.372" y2="368.847" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#666"></stop></linearGradient></defs><path fill="url(#R)" d="M557 360h0c0-2-1-3-1-4 1-2 3-3 5-3 0 1 1 2 2 3v1c-1 6-1 12-4 18v-1c0-4-1-9-2-14z"></path><path d="M406 427h0c1 1 2 2 2 3 1-1 1-1 1-2l-1-1v-1-1-2h-1v-1c0-3-2-6-2-8h1v-1 1l4 6c0 1 0 2 1 3v2l1 1v4l1-1c0 1 1 2 0 3h-1v-2l-1 1c0 1 0 2 1 3v2c-3 1-5 1-8 1l2-1v-1-4c-1-1 0-3 0-4z" class="X"></path><path d="M586 554l-1 21c0 8 1 16 2 24h0l-1-3c-2-2-2-4-2-7v-5c-1-3 0-5-1-7s0-10 0-12c0-3 1-5 1-7l1-1c0-1 0-2 1-3z" class="k"></path><path d="M583 565l1-1c1 4 0 9 0 14v6c-1-3 0-5-1-7s0-10 0-12z" class="T"></path><path d="M462 513h0l1-8h1c0 2 1 5 2 7-1-1-2-1-2-2v-2c-1 3-1 8-1 12l-1 20c0 4 0 8 2 11v1 3c0 1 0 3 1 4v3c1 2 1 22 0 26v-11-3-6l-1-2v-5l-1-2v1-1c-1-2-1-3-1-5-1-1 0-4 0-5-1-2-1-4-1-6l1-16c1-3 0-11 0-14z" class="X"></path><path d="M603 226c-1-1-1-1-1-2 1 0 1 0 2 1l3 2c4 2 8 3 13 3-2 2-5 4-8 5-2-1-2-1-4-1-1-1-3-1-4-2-2-1-4-2-5-3v-1c1 0 2 0 4 1h1v-1-2h-1z" class="D"></path><path d="M603 226c-1-1-1-1-1-2 1 0 1 0 2 1l3 2c0 1 1 2 2 2l2 1v1c-3 1-5-1-7-1h-1l1 2c-2-1-4-2-5-3v-1c1 0 2 0 4 1h1v-1-2h-1z" class="L"></path><path d="M471 425h1v-7c0-4-2-9-1-13l7 26-1 1 2 10c0 1 0 1-1 2v-1c-1-3-2-8-3-11h-1c0 2 2 5 0 8 0-4-1-7-2-11-1-1-1-2-1-4z" class="E"></path><path d="M530 369c1 2 2 4 4 6 1 0 3 2 3 3 1 3 6 6 7 9 1 2 2 5 2 8 0 2 0 4-1 7h0c0-5-1-9-2-13v-1c0-2-2-4-3-5l-3-1 1 7v3c1 0 1 1 1 1l-1 1c0-2-1-3-1-4 0-4 0-5-2-8h-1c0-1-1-1-1-2-1-1-2-2-2-4-1-1-2-2-2-3-1-2 0-3 1-4z" class="O"></path><path d="M605 734c1-2 1-3 1-5l1-1v-1c0 2 0 3 1 4h0v3 4 4h0c0 2 1 3 1 5v1l-1-1v1c-2 0-2 0-3-1 0 0 0 1-1 1 0 1 0 0-1 1v-1h0c0-1-1-2-1-3-1-5-1-14 2-18v1 6c-1 2-1 5-1 8l2-8z" class="X"></path><path d="M606 740v-1c1-2 1-3 2-5v4 4h0c0 2 1 3 1 5v1l-1-1v1c-2 0-2 0-3-1l1-7z" class="B"></path><path d="M606 740c1 3 2 5 2 7v1c-2 0-2 0-3-1l1-7z" class="T"></path><path d="M410 370c1-6 2-12 2-18h0l1 8c0 3-1 10 1 13h0l-2 4c-1 2-2 3-5 4-1 1-5 1-6 0v-1c2-2 4-4 6-7h0l1-1v1c-1 2-3 4-5 6l-1 1h1 1c1 0 3-2 4-3 1-2 2-5 2-7z" class="M"></path><path d="M559 609h1v3c-1 2-1 1-1 2-3 4-2 8-3 12v3h0c-1 2 0 5-1 6 0 1-2 3-3 4 1-3 1-8 2-10v-1c0-2 1-7 0-9l-1 1-1-1c-2 1-3 1-5 1 1-1 2-2 2-3l3-1 1-2h-1-3-2c0-1 1-1 1-2 1-1 1 0 2 1h3c1 0 1 0 2 1v-2h1c0 1 0 1 1 1l1-1h1v-3z" class="f"></path><path d="M442 795c4 9 10 19 13 30-1-1-1-2-2-3h0l-1-3c-1-1-2-2-2-3s0-1-1-3h0c-1-1-1-3-2-4h-1c-1-1-1-1-2-1-1 1-2 2-3 2h-1c1 1 0 1 1 2l2 5 1 1h-1l-7-16s1 1 1 2c1 0 1 0 1 1l1-1v-1l1-1c0-1-1 0 0-1s1-1 2-1l1-1-2-4h1z" class="G"></path><path d="M567 614l1-2c1 1 1 3 1 5 1 4 0 8-1 13l1 1c0 1-1 1-1 2v3h1c-1 1-1 1-2 1h-1c-1 2-2 3-2 5v1c-2-5 1-15 2-21v-3c0-2 0-3 1-5z" class="J"></path><path d="M568 633l-1 3h-1c0-6 2-13 3-19 1 4 0 8-1 13l1 1c0 1-1 1-1 2z" class="i"></path><path d="M470 331h1c0 1-1 3-1 4l1 1c0 1-1 3-1 3 0 3-1 6-1 9 0 2-1 3 0 5 0 2 0 8-1 10-2-4-2-8-1-12l-1-1v1l-1 1c-1 1-1 1-1 2v-1s0 4-1 4h0c0-6 1-11 3-16 1-4 3-7 4-10z" class="P"></path><defs><linearGradient id="S" x1="464.757" y1="350.285" x2="470.243" y2="358.715" xlink:href="#B"><stop offset="0" stop-color="#2f2d2c"></stop><stop offset="1" stop-color="#474648"></stop></linearGradient></defs><path fill="url(#S)" d="M470 335l1 1c0 1-1 3-1 3 0 3-1 6-1 9 0 2-1 3 0 5 0 2 0 8-1 10-2-4-2-8-1-12 0-2 0-4 1-6 0-3 1-7 2-10z"></path><path d="M524 397l2-2h0c0 1 0 3 1 3 1 1 1 3 1 4v2h0v2c0 2 0 5-1 7v-1c-1 0-1 1-1 2s-1 4-1 5l-2 2c-2-1-1-4-1-6h-1c0-2 0-4 1-6l1 3h0v-2c1-2 1-2 0-4v-6-4l1-1v7-5z" class="c"></path><path d="M524 411v-2-1-3c1-2 1-3 1-5 2 1 2 2 3 4v2l-2 2-1-1c0 2-1 3-1 4z" class="b"></path><path d="M524 411c0-1 1-2 1-4l1 1 2-2c0 2 0 5-1 7v-1c-1 0-1 1-1 2s-1 4-1 5l-2 2c-2-1-1-4-1-6h-1c0-2 0-4 1-6l1 3h0 0s0-1 1-1z" class="R"></path><path d="M521 415c0-2 0-4 1-6l1 3c0 2-1 3 0 4h1v1c1-1 1-2 1-2l1-1c0 1-1 4-1 5l-2 2c-2-1-1-4-1-6h-1z" class="d"></path><defs><linearGradient id="T" x1="455.111" y1="93.824" x2="444.282" y2="94.074" xlink:href="#B"><stop offset="0" stop-color="#bdbcbc"></stop><stop offset="1" stop-color="#e5e3e5"></stop></linearGradient></defs><path fill="url(#T)" d="M441 109h0c4-9 7-16 9-26l10 9s-1 1 0 1c0 2 6 6 5 7-3-3-6-7-10-8h-1l-2-2c-3 7-6 14-10 21l-1-2z"></path><defs><linearGradient id="U" x1="578.515" y1="446.986" x2="583.083" y2="460.812" xlink:href="#B"><stop offset="0" stop-color="#888889"></stop><stop offset="1" stop-color="#b1afaf"></stop></linearGradient></defs><path fill="url(#U)" d="M577 439v-2h1c0 1-1 4 0 5l1 1c3 1 0 8 2 10v-1c0-2 1-6 0-8l1-2v-1c1 4 1 10 1 15l1 1s1 0 1-1h1c-1 2-3 4-4 6l-2 1-3-11-1-1v-1c1-1 0-2 0-3 1-2 1-5 1-8z"></path><path d="M577 439v-2h1c0 1-1 4 0 5l1 1v7 2l-2-2v2l-1-1v-1c1-1 0-2 0-3 1-2 1-5 1-8z" class="b"></path><path d="M552 601c1 1 2 1 2 2h0l3 3c1 1 1 2 1 3h1v3h-1l-1 1c-1 0-1 0-1-1h-1v2c-1-1-1-1-2-1h-3c-1-1-1-2-2-1 0 1-1 1-1 2l-1-1v-1s-1 0-2-1c1-1 1-2 1-3l2-2c1 0 2-1 2-2l3-3z" class="L"></path><path d="M552 605v-1c1-1 1-1 2-1l3 3c1 1 1 2 1 3l-1 2h-1-1-4-3v-1c1-1 2-3 4-4v-1z" class="U"></path><path d="M552 605h1c2 1 1 1 1 3h0c-2 0-1-1-2-2v-1z" class="n"></path><path d="M529 449c-1-1-1-8-2-10 0-3 0-6 1-9v-1l1-4 1 10c1 4 3 8 4 12 0 1-1 2-1 3l-1-2c1 6 2 10 2 16l-1 1c-1-5-1-12-4-16z" class="F"></path><path d="M532 448c-2-4-3-8-3-12l1-1c1 4 3 8 4 12 0 1-1 2-1 3l-1-2z" class="G"></path><path d="M580 607c1 1 1 2 1 3v2c1 2 0 5 0 7 0 7-1 13-3 20l-1-1-2 1-1 1v-1c1-2 1-5 1-7 0-3 0-5-1-8l1-1c0 1 1 2 1 3 2-1 1-2 1-4 1-1 1-1 1-2s0-2 1-4c1-3 1-6 1-9z" class="O"></path><path d="M577 622c1-1 1-1 1-2s0-2 1-4c0 8 0 17-4 23l-1 1v-1c1-2 1-5 1-7 0-3 0-5-1-8l1-1c0 1 1 2 1 3 2-1 1-2 1-4z" class="b"></path><path d="M610 721c1-1 1-2 3-3h0c1 1 1 2 2 3l1 5-2-1h-1c-1 3-1 8-1 12v4l1 1-3 1c0 1 0 2-1 4 0-2-1-3-1-5h0v-4-4-3c0-2 0-8 2-10z" class="K"></path><path d="M608 742c1-1 1-6 1-7l1 8h0c0 1 0 2-1 4 0-2-1-3-1-5z" class="P"></path><path d="M610 721c1-1 1-2 3-3h0c1 1 1 2 2 3l-3 2c-2 3-2 6-2 10 0 0 0 2-1 2 0 1 0 6-1 7h0v-4-4-3c0-2 0-8 2-10z" class="M"></path><path d="M595 434c2 1 2 7 2 9 0 4-1 7-2 10h-1c-1 0-2 1-2 1l-2 1c0-2 0-5 1-7h-2v-3l-1-6 2-1v-3h1l1 5h1l1 1v-1l1-1 1-1c-1-2-1-3-1-4z" class="i"></path><path d="M591 435l1 5v6c0 3-1 6 0 8l-2 1c0-2 0-5 1-7h-2v-3l-1-6 2-1v-3h1z" class="c"></path><path d="M588 439l2-1c1 3 1 7 1 10h-2v-3l-1-6z" class="l"></path><defs><linearGradient id="V" x1="502.29" y1="493.366" x2="495.821" y2="493.652" xlink:href="#B"><stop offset="0" stop-color="#39393b"></stop><stop offset="1" stop-color="#5f5e5c"></stop></linearGradient></defs><path fill="url(#V)" d="M500 479c1-1 1-2 2-2 0 1 0 1-1 2v11 2c0 3-1 6-1 9v7h-1c-2 3 0 7 0 11-3-8-4-13-2-22l3-18z"></path><path d="M605 254c2-1 3-1 5-1 0-2 1-3 1-4 2 3 4 5 5 9 1 2 2 4 4 6 2 1 4 2 5 2l1 1c-1 1-5 0-6 0-2 0-3-1-4-1-3-1-4-4-5-6-2-3-4-4-6-6z" class="P"></path><path d="M522 321c-1-1-1-3-1-4 1-1 1-1 2-1 2 1 1 3 3 4 1-1 1-2 2-4 1-1 2-1 3-1 1 1 0 3 1 4v1c0 1 0 2-1 3v2h0 0c-2 2-4 4-6 5h-1l-1-1-1-1c-1-1-1-1-2-1v-1c0-1 0-1 1-2 1 0 2-1 3-1h1v-1-1l-1 1c-1-1-1 0-2-1h0z" class="B"></path><path d="M520 326h3l1-1 1 1c1 0 2-1 3-1 1-1 1-1 2-1l1 1c-2 2-4 4-6 5h-1l-1-1-1-1c-1-1-1-1-2-1v-1z" class="I"></path><path d="M465 100c1-1-5-5-5-7-1 0 0-1 0-1 7 6 14 13 18 22 3 5 5 10 7 15l1 2c0 1 1 2 1 3 1 1 1 2 1 4-2-2-4-4-5-7-1-2-1-5-2-7s-2-3-2-5c-1-2-4-8-6-9h-1c-1-1-2-3-2-4l-5-6z" class="H"></path><defs><linearGradient id="W" x1="436.827" y1="231.378" x2="417.753" y2="243.746" xlink:href="#B"><stop offset="0" stop-color="#959495"></stop><stop offset="1" stop-color="#bebdbe"></stop></linearGradient></defs><path fill="url(#W)" d="M430 237c4 0 8-3 12-4 2 0 2-1 4 1l-6 3c-3 1-6 2-8 4-2 0-5 1-6 2-6 1-11-2-16-5 7 1 14 0 20-1z"></path><path d="M418 235c-4-1-7-3-10-5 3 0 6 0 9-1l1 1c2 1 3 0 5 2l3-1h4 1 0v1l6-1-2 3-5 2c-4 1-8 1-12-1z" class="L"></path><path d="M426 231h4 1 0v1c-4 2-9 3-13 2-2-1-5-1-6-3 4 1 6 2 10 1h1l3-1z" class="f"></path><path d="M431 232l6-1-2 3-5 2c-4 1-8 1-12-1v-1c4 1 9 0 13-2z" class="o"></path><path d="M451 402v-1c1-4 3-9 4-13 0 4 0 9-2 13l1 1 1-2 1 1c0 7 1 13 2 19v7 1l-1-1c0 2 0 4-1 5 0-8-2-16-2-25h-1c0 2 0 3-1 4-1-3 0-6-1-9z" class="E"></path><defs><linearGradient id="X" x1="514.262" y1="381.762" x2="510.419" y2="400.276" xlink:href="#B"><stop offset="0" stop-color="#313031"></stop><stop offset="1" stop-color="#636362"></stop></linearGradient></defs><path fill="url(#X)" d="M511 382v-5c1-1 0-3 0-4 1-2 0-4 0-7h1v1 1c1 2 1 7 1 10h1v-3h0c1 2 1 8 1 10h0 0c1 2 1 3 0 4v3 10 1l-2-1-1 2v-1c-1 1-1 1-1 2l-1 1v-2l-1 1h0v-3c1 0 1 1 2 0v-6c0-3 0-6 1-8v-2c0-1 0-2-1-4z"></path><path d="M515 392v10h-1c-1-2-1-6-1-8l2-2z" class="O"></path><defs><linearGradient id="Y" x1="516.161" y1="418.93" x2="524.118" y2="437.259" xlink:href="#B"><stop offset="0" stop-color="#b8b6b6"></stop><stop offset="1" stop-color="#e3e3e3"></stop></linearGradient></defs><path fill="url(#Y)" d="M521 415h1c0 2-1 5 1 6l2-2h1s1 0 2-1c0 1-1 2-1 3-1 2-3 3-4 6-4 5-6 11-7 17l-3-7h1 1v-2-7-4c1-2 1-3 1-4l1 1h1c0 1 1 2 0 3v3h1s0-1 1-1c1-2 1-3 1-5v-6z"></path><path d="M490 477v-3-1c0-2 1-6 2-8 0-3 1-8 0-11 0-3 0-5-1-8-1-6-3-12-4-18 1 0 0 0 1 1h0l3 10c1 1 1 1 1 2v2 2c1 1 1 0 1 1v1 1c0 1 0 1 1 2v6l-1 9h1c1-1 2-5 1-7v-1c1-1 1-2 1-3v-1-1c0-1 0-1 1-1h1c-1-2-1-4 0-6v10h0c-1 1-1 1-1 2s0 4 1 5c0-1 0 0 1-1v-1h0l1-1v-1c0-1 0-1 1-2-2 6-5 10-8 15l-3 6z" class="F"></path><path d="M407 373c0-1 0-1-1 0h-1l3-5c-2 1-3 3-5 4h-1c-2 1-3 1-5 2-2 0-4 0-6-1 2 0 4 0 5-1 2-1 5-3 6-6 1-2 2-5 3-8 0 0 0-1 1-2v1c0 2-1 5-2 7 0 2-2 4-1 6l1-1c1-3 3-5 3-8 2-4 1-8 2-12 1 3 1 8 1 12 0 3 0 6-2 9 0 1 0 0 1 1l1-1c0 2-1 5-2 7-1 1-3 3-4 3h-1-1l1-1c2-2 4-4 5-6v-1l-1 1z" class="I"></path><path d="M586 399h0c-1-4-3-8-3-12h1c1 3 1 7 3 10h1v1l3 5v-1c-1-1-1-1-1-2v-1-2l-1-1c-1-4-2-9-1-13 2 1 2 1 4 3v4 2l1 1c0 1-1 0 0 2h0 0c-1-1-1-2-2-3v1l-1-1c0 3 0 5 1 7 1 3 1 5 3 7l1 2v2h-1-1-3c-1-1-2-3-2-5-1-1-2-4-2-6z" class="j"></path><path d="M588 405h3l2 5h-3c-1-1-2-3-2-5z" class="e"></path><path d="M586 399l1-1c0 1 0 0 1 1 0 2 2 4 3 6h-3c-1-1-2-4-2-6z" class="W"></path><defs><linearGradient id="Z" x1="610.609" y1="471.966" x2="599.685" y2="483.283" xlink:href="#B"><stop offset="0" stop-color="#343535"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#Z)" d="M601 488c1-4 2-7 3-10l3-14c1 5 2 11 1 17 2 1 0 3 2 5l-1 3v1l-1 2c-2-2-2 1-4 1 0-1 0-2-1-3s-2-1-3 0l1-2z"></path><path d="M609 489l-1-1v-1l-1 1-1-1c0-2 1-4 2-6 2 1 0 3 2 5l-1 3z" class="O"></path><path d="M407 267l1 1h-1c-2 3-2 6-4 9-2 5-5 11-7 16v4c0 1 0 1-1 2v2c-2 2-4 5-5 8 4-15 4-31 17-42z" class="D"></path><path d="M593 481l1 1c-1 1-1 1 0 2-1 3-3 5-4 7-2 1-3 1-4 3h0l-1 2-3 4-2-1h-4v1c0-2 1-4 2-5 0-2 2-3 3-5v-1h0c1-2 3-5 5-6v1 1l-1 4h0l1-1c3-2 5-4 7-7z" class="B"></path><path d="M579 496c0-2 2-3 4-4v1l5-6c-1 3-3 4-2 7l-1 2-1-1v-1c-2 0-2 1-3 2h-2z" class="T"></path><path d="M579 496h2c1-1 1-2 3-2v1l1 1-3 4-2-1h-4l3-3z" class="W"></path><defs><linearGradient id="a" x1="443.554" y1="410.302" x2="426.252" y2="419.57" xlink:href="#B"><stop offset="0" stop-color="#292a29"></stop><stop offset="1" stop-color="#5a5859"></stop></linearGradient></defs><path fill="url(#a)" d="M439 392c0 2 1 7 0 9 0 2 0 3-1 5v4 3h0c0 2 1 4 0 5h-1l-1 3v1l-1 3-1 7-1-1h0c-1-1-1-2-1-3h-1c-1 1-1 3-2 5v-1c1-5 3-10 4-16l4-17c1-2 1-3 1-6 0-1 1-1 1-1z"></path><path d="M569 636h1c1 1 1 1 2 1v-1c1 0 1 2 2 3v1c0 2 0 3 1 5h0c0 3-1 10-3 12h-2c-2-1-2-5-2-7-1-3-1-6 0-10-1 0-1-2-1-3 1 0 1 0 2-1h0z" class="E"></path><path d="M568 650l3-4c0 3 0 8-1 11-2-1-2-5-2-7z" class="N"></path><path d="M568 640h1c2 1 2 4 2 6l-3 4c-1-3-1-6 0-10z" class="C"></path><defs><linearGradient id="b" x1="458.713" y1="317.391" x2="449.641" y2="329.109" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#464646"></stop></linearGradient></defs><path fill="url(#b)" d="M457 316c1-1 3-3 5-4-1 1-1 2-1 3l-2 2c1-1 1-1 2-1h1l1-1h2s1 0 1-1l1 1c-1 1-3 2-4 3v1c-2 3-4 5-6 7h-1c-2 1-3 3-4 5l-1 1v-2c-1 0-1 0-1 1s-1 2-1 2l-2 1c1-1 1-2 1-3 1-2 1-4 1-5 0-2 4-6 5-7l3-3z"></path><defs><linearGradient id="c" x1="510.305" y1="391.328" x2="504.696" y2="403.172" xlink:href="#B"><stop offset="0" stop-color="#4e4e4d"></stop><stop offset="1" stop-color="#7d7b7c"></stop></linearGradient></defs><path fill="url(#c)" d="M506 381c0-3 0-3 1-5l1-2v-1l1-2v2 1c0 2-1 5 0 7 1 0 1-1 1-1 1 1 1 2 1 4v1-3c1 2 1 3 1 4v2c-1 2-1 5-1 8v6c-1 1-1 0-2 0v3 7c-1-2 0-4-1-6l-1 2v-5c-1 0-1-1-2-2v-1l1-1 1-4v-4-4-6h-1z"></path><path d="M507 395v8c-1 0-1-1-2-2v-1l1-1 1-4z" class="J"></path><path d="M510 393c0 1 0 2 1 3v6c-1 1-1 0-2 0 0-2 0-7 1-9z" class="K"></path><path d="M511 382c1 2 1 3 1 4v2c-1 2-1 5-1 8-1-1-1-2-1-3v-3h0-1c-1-2-1-2-1-4s0-1 1-3l1 1h1v1-3z" class="T"></path><defs><linearGradient id="d" x1="470.985" y1="437.646" x2="462.515" y2="455.354" xlink:href="#B"><stop offset="0" stop-color="#2f2e2f"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#d)" d="M466 434c1 1 1 1 1 2 1 0 1 3 1 4 1 4 2 9 2 13v1c-1 2-1 4-1 7h-1v2l-1-1-1 1 3 9h-1l-1-2h0c-1-3-2-7-2-9-1-4-2-7-3-11 1 1 1 0 2 1v1h1l1-18z"></path><path d="M466 463v-2l-1-5 3 5v2l-1-1-1 1z" class="Y"></path><defs><linearGradient id="e" x1="554.911" y1="432.097" x2="540.238" y2="441.631" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#383737"></stop></linearGradient></defs><path fill="url(#e)" d="M549 422v1s1 1 1 2h0 1v1 3l-1 3c1 3 0 6 0 9l-1 1v-1c-1 2-1 4 0 6h1c0 2 0 7-2 10v-6h-1l-2 2 1 4h-1c-1-2-1-5-1-8h1v-7c1-7 2-14 4-20z"></path><path d="M551 426v3l-1 3v-1c0-2-1-3 1-5z" class="F"></path><path d="M544 449h1v-7 11l1 4h-1c-1-2-1-5-1-8z" class="E"></path><path d="M631 718l1-2v1l1 1s0-1 1-2c1 9 4 17 5 26 0 1 0 3-1 5 0-1 0-1-1-2 0-1-2-3-3-3 0-2 0-2 1-3h0c-1-1-2-2-2-4s0-4-1-6h-2c0-2-1-4 0-7 1-1 1-2 1-4h0z" class="d"></path><path d="M632 729v-10c3 7 4 14 4 21h0l-1-1h0c-1-1-2-2-2-4s0-4-1-6z" class="Q"></path><defs><linearGradient id="f" x1="579.221" y1="420.335" x2="575.304" y2="437.783" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#737272"></stop></linearGradient></defs><path fill="url(#f)" d="M574 412h0c0-3-2-5-3-7l1-1c2 3 5 9 6 13 0 3 1 7 2 10h0c1 2 1 2 1 4l1 1v6l-1 1c-1 1-1 3 0 4v1c1 2 0 6 0 8v1c-2-2 1-9-2-10l-1-1c-1-1 0-4 0-5h-1v2c0-4 0-8-1-12 0-4-1-8-3-12h2c0 1 0 2 1 2h0 0c0-2-1-3-2-5z"></path><path d="M347 769v-1l1-1c0-1 0-1 1-2v-2c1 1 1 2 1 3l1-3 1 1h0c-1 1-1 1 0 2-1 2-1 4 0 7h0c1 0 1 0 2 1v8l-2-2c2 3 2 7 2 10-1-1-1-2-1-3-1-1-2-2-2-3l-2-6c-1 0-1 1-1 1-1 1-1 3 0 4v1 2l1 1h-1c-1-4-3-7-4-11 1-2 2-3 2-5 0-1 0-2 1-2z" class="F"></path><path d="M350 766l1-3 1 1h0c-1 1-1 1 0 2-1 2-1 4 0 7h0c1 0 1 0 2 1v8l-2-2c-1-5 0-9-1-14h-1z" class="G"></path><path d="M347 769v-1l1-1c0-1 0-1 1-2v-2c1 1 1 2 1 3h1c-2 4-3 8-3 11h-1c-1-2-1-6 0-8h0z" class="M"></path><defs><linearGradient id="g" x1="580.218" y1="403.411" x2="594.899" y2="439.43" xlink:href="#B"><stop offset="0" stop-color="#515151"></stop><stop offset="1" stop-color="#b1b0b0"></stop></linearGradient></defs><path fill="url(#g)" d="M580 406v-2h1 1v1c4 5 6 10 9 16 1 3 2 5 2 8 1 1 1 3 2 5 0 1 0 2 1 4l-1 1-1 1v1l-1-1h-1l-1-5c-1-6-3-13-6-17l-5-12z"></path><defs><linearGradient id="h" x1="445.913" y1="393.516" x2="435.587" y2="426.984" xlink:href="#B"><stop offset="0" stop-color="#1f1f1f"></stop><stop offset="1" stop-color="#4c4b4a"></stop></linearGradient></defs><path fill="url(#h)" d="M438 413v-4c1-3 1-7 1-10 2-3 0-6 2-9 1 2 1 3 2 4s0 4 0 6c-1 4-1 7-1 11l-1 2v3c0 5 1 9 1 15-1-2-2-3-4-4 0-1 0-1-1-2h0l-1-1v-3l1-3h1c1-1 0-3 0-5z"></path><path d="M544 611h0c1 1 2 1 2 1v1l1 1h2 3 1l-1 2-3 1c0 1-1 2-2 3l-1 3-2 17c-1 2-2 5 0 7-1 1-2 3-3 4v-1c1-4 1-8 1-13-1 4-2 7-3 11-1-3 3-20 4-23 0-3 1-7 2-9v-2h-1v-3z" class="O"></path><path d="M546 623h-1c0-3 1-5 1-8h0 4l-1 2c0 1-1 2-2 3l-1 3z" class="T"></path><defs><linearGradient id="i" x1="520.124" y1="406.915" x2="513.876" y2="417.585" xlink:href="#B"><stop offset="0" stop-color="#7f7f80"></stop><stop offset="1" stop-color="#afadac"></stop></linearGradient></defs><path fill="url(#i)" d="M520 405h1c0-3 0-5 1-7v1l1 1v6c1 2 1 2 0 4v2h0l-1-3c-1 2-1 4-1 6v6c0 2 0 3-1 5-1 0-1 1-1 1h-1v-3c1-1 0-2 0-3h-1l-1-1c0 1 0 2-1 4v-19l1-2h1l1 8h1c0-3-1-7 1-9h0v3z"></path><path d="M520 426h-1v-5h1 0 1c0 2 0 3-1 5z" class="i"></path><path d="M520 405h1c0-3 0-5 1-7v1l1 1v6l-1 1v-2c-1 1-1 3-1 4l-1-4z" class="Z"></path><defs><linearGradient id="j" x1="548.617" y1="339.01" x2="562.973" y2="353.002" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#353434"></stop></linearGradient></defs><path fill="url(#j)" d="M554 352h1 1c-1-1-1-2-1-3h0c-1-2-1-4-2-6v-1-3h-1-1l2-1v-3c-1-1-1-1-1-2l2 1c1 1 1 2 1 4h1l1-1c3 6 6 13 6 20v-1c-1-1-2-2-2-3-2 0-4 1-5 3 0 1 1 2 1 4h0l-1 3-1-5c0-2-1-4-1-6z"></path><defs><linearGradient id="k" x1="419.693" y1="387.446" x2="428.936" y2="413.508" xlink:href="#B"><stop offset="0" stop-color="#1f1d1f"></stop><stop offset="1" stop-color="#3a3b39"></stop></linearGradient></defs><path fill="url(#k)" d="M428 384l1 2v1c0 1 0 1 1 1-1 4-4 9-3 13-3 5-4 11-6 17 1 0 1 0 1 1s0 1 1 2c-1 2-1 2-4 3 0 2-1 3-2 4v-1c0-1 1-3 1-4 1-3 2-5 2-8 1-1 0-3 0-4v-1l-1 1-1-1v-2c1-1 1-1 1-2h1 1c1-2 1-3 2-6l5-16z"></path><path d="M421 418c1 0 1 0 1 1s0 1 1 2c-1 2-1 2-4 3l2-6z" class="K"></path><defs><linearGradient id="l" x1="595.061" y1="484.033" x2="588.267" y2="496.644" xlink:href="#B"><stop offset="0" stop-color="#4f4e4e"></stop><stop offset="1" stop-color="#727170"></stop></linearGradient></defs><path fill="url(#l)" d="M597 479l1 2 1-1c0-1 1-1 1-1v8l1 1-1 2-1 3h-1-1v-3h-1l-1-1-1 1c-2 3-3 6-7 9h0l-3 3h0l-4 3v-1c1-1 1-2 2-3v-1l3-4 1-2h0c1-2 2-2 4-3 1-2 3-4 4-7l3-5z"></path><path d="M596 487c0-2 1-3 2-5h1v6l-1-2h0-1-1v1z" class="e"></path><path d="M596 487v-1h1 1 0l1 2v1l1 1-1 3h-1-1v-3h-1l-1-1-1 1c0-1 1-2 2-3h0z" class="J"></path><path d="M596 487h1v3h-1l-1-1-1 1c0-1 1-2 2-3z" class="c"></path><path d="M586 494h0c1-2 2-2 4-3-1 5-5 7-6 11l-4 3v-1c1-1 1-2 2-3v-1l3-4 1-2z" class="J"></path><defs><linearGradient id="m" x1="559.508" y1="412.824" x2="574.838" y2="437.964" xlink:href="#B"><stop offset="0" stop-color="#272626"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#m)" d="M559 412c-1-2-2-3-1-5h1l1 1c7 3 11 15 13 22l1 10-1-1h0l-2 2v-2c-1-6-2-13-5-18h-1c-1-3-3-7-5-10l-1 1z"></path><path d="M559 412c-1-2-2-3-1-5h1l1 1c0 2 1 3 2 5s3 5 4 8h-1c-1-3-3-7-5-10l-1 1z" class="F"></path><path d="M592 551v1 1 3c0 1-1 2 0 3 1-3 2-6 4-9h0c3-4 6-7 9-9l-15 26v-4l-1-1h1v-2-1l1-1h0v-2-1-4c0 1 0 1-1 2 0 0-1 0-1 1v2h1c0 2-1 2-1 3-1 1-1 4-2 6v5l-1 1c0 1 0 2-1 4l1-21 1-3c0 1 0 3 1 4 0-2 1-3 2-4 0-1 0-1 1-2 0-1 0-1 1-1v3zm-237 90c1-1 3-1 4-2 0 4 0 8 1 11 0 5 2 12 0 16v1c-1 0-1 1-1 1 0 2-2 6-3 6 0-2 1-3 1-5v-1l-1 1-1 1v-9h0c1-4 0-8 1-12v-6c0-1 0-1-1-2z" class="E"></path><path d="M356 643c2 4 0 11 2 15 0 3 1 8-1 11v-1l-1 1-1 1v-9h0c1-4 0-8 1-12v-6z" class="X"></path><path d="M476 566c1-1 1-5 1-7 1-6 2-11 2-16v-5 2c0 4 1 7 2 10 0 6 0 13 2 20v1c0 3 0 9 1 11v1c0 1 1 5 0 6 0 2 0 3-1 5-1-2 0-3 0-4v-8c-1-2-1-3-1-4v-3c-1-2 0-3-1-5v-4c-1-2 0-6-1-8 0-2 1-9 0-11l-1-2c0 1 1 4 0 6-1 1-1 2-1 3-1 3 1 6 0 8 0 2 0 5-1 7v4c-1 1-1 2-1 4-1 2 1 4 0 6 0 1-1 1 0 3 0 1 1 3 0 4l-1-1v1c0 2 1 7 0 9h0v4c0 1 0 1 1 2v3 1c0-1 0-3-1-4 0-1 0-1-1-2v-12-2 1c0 1 0 2-1 3h0v-2-1-1c1-2-1-4 1-5 0-2-1-4 0-5 0-2 0-4 1-5v-2h0c1-2 1-4 1-6z" class="F"></path><path d="M538 389l-1-7 3 1c1 1 3 3 3 5v1c1 4 2 8 2 13-2 2-2 2-1 4-1 2-3 5-5 7v-7l-1-7v-5l1-1s0-1-1-1v-3z" class="d"></path><path d="M538 389l1-1v1c1 1 1 2 1 3 1 1 1 1 1 2l-1 1h0c1 4 0 7 0 11l-1-1v1l-1-7v-5l1-1s0-1-1-1v-3z" class="c"></path><path d="M538 389l-1-7 3 1c1 1 3 3 3 5v1 2 12l-2-9c0-1 0-1-1-2 0-1 0-2-1-3v-1l-1 1z" class="J"></path><path d="M554 334c0-4-3-9-3-12 1-2-1-5-2-7l1-1c0 1 1 2 1 3s1 5 1 6c1 0 2 2 3 2 2-1 2-3 2-5l-1-2v-2c1 1 2 2 2 3l1 4v1s1 2 1 3l3 8c1 2 2 4 2 6h-1c1 2 1 3 1 5l-7-12-2-2c-1 1 0 2 0 3l1 2-1 1h-1c0-2 0-3-1-4z" class="F"></path><path d="M558 334l1-7 3 9 1-1c1 2 2 4 2 6h-1c1 2 1 3 1 5l-7-12z" class="G"></path><defs><linearGradient id="n" x1="473.794" y1="447.385" x2="469.818" y2="471.049" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#787878"></stop></linearGradient></defs><path fill="url(#n)" d="M472 445c2 3 2 5 2 7v8c1 6 0 11-1 17h-1l-1-2-2-3-3-9 1-1 1 1v-2h1c0-3 0-5 1-7l1 2v1-5-6l1-1z"></path><path d="M466 463l1-1 1 1 3 6c0 1 1 2 1 4 0 0-1 1-1 2l-2-3-3-9z" class="S"></path><defs><linearGradient id="o" x1="545.601" y1="351.163" x2="551.899" y2="356.837" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#3d3d3d"></stop></linearGradient></defs><path fill="url(#o)" d="M547 356v-3-1h-1c1-1 1-2 1-3v-1l-1-4v-1c0-1 0-3 1-3h0c0 1 1 1 1 2 1 1 1 1 1 3 0 0 0 2 1 3v1-4c-1-2-1-2-1-3v-1-3l1 1v3c1 2 0 0 0 2l1 1v2l1 4c1 0 0 0 1-1l1 2c0 2 1 4 1 6l1 5v2c-1 0-1 0-2 1v1l-2-2v1c-1-1-1-2-1-4h-1v-1h-2v-1c0-2 0-3-1-4z"></path><path d="M551 347l1 4c1 0 0 0 1-1l1 2c0 2 1 4 1 6l1 5v2c-1 0-1 0-2 1v1l-2-2v1c-1-1-1-2-1-4s-1-4-1-7c0-1 1 0 1-1 1-1 0-5 0-7z" class="M"></path><path d="M552 365v-5h1l1 4h1c0-2-1-4 0-6l1 5v2c-1 0-1 0-2 1v1l-2-2z" class="K"></path><path d="M601 253l4 1c2 2 4 3 6 6 1 2 2 5 5 6l-2 1-1-1c-1-1-8-5-10-6-1-1-2-1-4-2h0c-3 0-8 1-11-1-1-1-1-1-2-1h-1l2-3c4 1 7 1 11 0h3z" class="p"></path><defs><linearGradient id="p" x1="470.995" y1="425.723" x2="455.005" y2="442.277" xlink:href="#B"><stop offset="0" stop-color="#0e0f0d"></stop><stop offset="1" stop-color="#3a393c"></stop></linearGradient></defs><path fill="url(#p)" d="M461 416h1v-1h1c2 4 0 9 2 13l1-10 1 3c0 4 0 9-1 13l-1 18h-1v-1c-1-1-1 0-2-1h1l-2-2h-1l-1 1h0v-4c1-1 2-1 3-2v-16c0-3 0-6-1-8v-3z"></path><path d="M459 445c1-1 2-1 3-2l1 7-2-2h-1l-1 1h0v-4z" class="K"></path><path d="M621 273c5 6 8 12 11 19 1 2 2 5 2 7h-1c-1-1-4-3-5-4l-1-1-2-1h0v-1c0-2 0-3-1-4h-1l-5-2h3c0-1 0-2-1-3-1-2-1-4-1-6v-1h0 2v-2-1z" class="C"></path><path d="M619 277c2 2 3 4 4 6v3c1 0 1 0 1 2h-1l-5-2h3c0-1 0-2-1-3-1-2-1-4-1-6z" class="H"></path><defs><linearGradient id="q" x1="352.293" y1="745.427" x2="341.207" y2="741.073" xlink:href="#B"><stop offset="0" stop-color="#0c0c0d"></stop><stop offset="1" stop-color="#343332"></stop></linearGradient></defs><path fill="url(#q)" d="M348 722h2l-1 10h1v12c0 5 0 9-2 14-1 3-2 5-3 8-2-5 2-37 3-44h0z"></path><defs><linearGradient id="r" x1="421.945" y1="403.177" x2="428.055" y2="414.823" xlink:href="#B"><stop offset="0" stop-color="#373435"></stop><stop offset="1" stop-color="#4c4e4c"></stop></linearGradient></defs><path fill="url(#r)" d="M429 397h1l1 1h1c-1 2-2 5-1 7l-3 6-2 8c-1 3-1 4-3 6v2l-1 1c0 1 0 1-1 2l-4 3v-5c1-1 2-2 2-4 3-1 3-1 4-3-1-1-1-1-1-2s0-1-1-1c2-6 3-12 6-17l1-3 1-1z"></path><path d="M429 397h1l1 1h1c-1 2-2 5-1 7l-3 6-2 8h-1c0-1 0-1 1-2l2-8c1-3 1-6 1-9 0-1 0-1-1-2l1-1z" class="B"></path><path d="M423 421l1-3c1 1 0 5-1 7v2l-1 1c0 1 0 1-1 2l-4 3v-5c1-1 2-2 2-4 3-1 3-1 4-3z" class="J"></path><path d="M386 781l1 4c1 5 3 12 1 18h-1v-1c-1 1-2 1-3 1l-2 1-1-1c0-2 0-3-1-4v-2-3c1-2 1-7 1-9 0-1 0-2 1-2h1c1-1 2-1 3-2z" class="Y"></path><path d="M386 781l1 4c-1 1-1 2-2 2v-2h-1c0 2 0 3-1 5l-1-7h1c1-1 2-1 3-2z" class="W"></path><path d="M385 787c1 0 1-1 2-2 1 5 3 12 1 18h-1v-1c-1 1-2 1-3 1h0c1-3 1-5 1-8 1 2 1 4 1 6h1v-5l-1-3c-1-2-1-4-1-6z" class="K"></path><path d="M382 783l1 7v1h1l1 4h0c0 3 0 5-1 8h0l-2 1-1-1c0-2 0-3-1-4v-2-3c1-2 1-7 1-9 0-1 0-2 1-2z" class="b"></path><defs><linearGradient id="s" x1="475.469" y1="435.796" x2="493.531" y2="453.204" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#333332"></stop></linearGradient></defs><path fill="url(#s)" d="M481 438l1-1-3-10h1l1 2h1c0-2-1-4-1-6h0c5 12 10 25 9 39 0-1-1-2-1-3h-1c0 1 0 2-1 3h-1c0-3 0-8-1-10v-2l-1-1v-1-1c0-3-3-6-3-9z"></path><defs><linearGradient id="t" x1="546.537" y1="345.579" x2="537.686" y2="360.057" xlink:href="#B"><stop offset="0" stop-color="#131514"></stop><stop offset="1" stop-color="#312f30"></stop></linearGradient></defs><path fill="url(#t)" d="M541 356h-1l1-1c-1-2-1-5-1-7h0c0-2-1-4 0-5 1-2 0-2 1-3l1 1c1 1 1 1 1 2 1 2 1 4 1 6v-1-1h1 0l1 3h-1v2c0 1 1 2 1 4 1 1 1 2 1 4h1v-2h-1v-2c1 1 1 2 1 4v1h2v1c0 3 0 7-2 9v-4s-1-1-1-2h-1v2l-1 1h0l-1-1v-2c-1-1 0-1-1-1h-1l-1-1-1-3h1v-4z"></path><path d="M541 356c1 2 1 5 1 8l-1-1-1-3h1v-4z" class="I"></path><path d="M545 363l-1-9c2 4 2 8 4 10v-3h2v1c0 3 0 7-2 9v-4s-1-1-1-2h-1v2l-1 1h0l-1-1v-2l1-2z" class="k"></path><path d="M545 363v5h0l-1-1v-2l1-2z" class="M"></path><path d="M581 422c1-2 1-4 0-5-2-5-3-10-6-14l1-1v1c1 2 3 3 4 5 1 3 3 7 4 10h1 0c3 4 5 11 6 17h-1v3l-2 1 1 6c-2-1-2-6-2-8 0-1 0-2-1-3 0-5-1-8-3-12h-1c1 1 1 0 1 1 0 4 3 13 1 16-2-1-1-8-2-11-1-1-1-1-1-2v-2-2z" class="W"></path><path d="M588 439v-1c0-4-1-8-2-12l1-1c2 3 2 7 3 10v3l-2 1z" class="Z"></path><path d="M580 427v-11 1c1 2 1 3 1 5v2 2c0 1 0 1 1 2 1 3 0 10 2 11 2-3-1-12-1-16 0-1 0 0-1-1h1c2 4 3 7 3 12 1 1 1 2 1 3 0 2 0 7 2 8v3 4h-1v-2l-1 1v3c0 1 0 1-1 2h-1c0 1-1 1-1 1l-1-1c0-5 0-11-1-15v1l-1 2v-1c-1-1-1-3 0-4l1-1v-6l-1-1c0-2 0-2-1-4z" class="Y"></path><path d="M587 437c0 2 0 7 2 8v3 4h-1v-2l-1 1v3c0 1 0 1-1 2h-1c0 1-1 1-1 1l-1-1 1-1c1-1 0-3 1-5-1-1 0-4 0-5h0c1-1 1-2 1-3v-2h0l1-3z" class="g"></path><path d="M585 450h1v4h1c0 1 0 1-1 2h-1c0 1-1 1-1 1l-1-1 1-1c1-1 0-3 1-5z" class="o"></path><defs><linearGradient id="u" x1="425.327" y1="432.852" x2="439.205" y2="447.649" xlink:href="#B"><stop offset="0" stop-color="#575657"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#u)" d="M429 432v1c1-2 1-4 2-5h1c0 1 0 2 1 3h0l1 1 1-7 1-3v-1 3c-1 3 0 6-1 8v6c0 2 0 3 1 5-1 4-1 9-1 14l-1-1c-5-8-7-15-5-24z"></path><path d="M580 181c4-1 8-4 13-1h1-1-5l-2 1h0 1v1c1 2 2 4 3 5 3 6 6 12 11 16-1 1-3 0-5 0l-1 1-2-1-4-4c-2-1-4-3-5-4l-3-3c2 0 3 1 4 2l2 1h0v-1h2 0c0-2-1-2-2-4 0-1 0-1-1-2-1 0-1-1-1-2h1 0c0-1-1-1-1-2h0l-1-2h0-4v-1z" class="L"></path><path d="M584 195l-3-3c2 0 3 1 4 2l2 1h0c4 2 6 7 9 8l-1 1-2-1-4-4c-2-1-4-3-5-4z" class="S"></path><path d="M568 506v1c-1 2-2 3-4 4l-2 1h0l-1 1h3l-3 3h0l-10 8-6 3h0-3v-1c2-1 3-3 5-5l-6 5v-1l1-1c1-2 4-4 6-6l6-5c4-3 9-5 14-7z" class="h"></path><path d="M568 506v1c-1 2-2 3-4 4l-2 1h0l-1 1c-2 1-4 3-7 5 1-2 4-3 4-5h-4c4-3 9-5 14-7z" class="c"></path><path d="M413 248c3 2 5 5 7 7l-1 1c-1 0-2 1-3 2l1 1c-1 1-2 1-2 1v1 1l1 1-3 1-1 1-2 1-3 1c0-2 0-2-1-2l-8 1c2-1 5-2 6-3 2-2 3-5 4-8 1-2 3-5 5-7z" class="i"></path><path d="M406 265c2-1 4-3 7-3h0l1-1c-2 0-3 0-4 1v-1h2l2-1h1v1 1l1 1-3 1-1 1-2 1-3 1c0-2 0-2-1-2z" class="E"></path><path d="M501 492c1 2 1 4 1 6 0 3 0 8 2 10 1 4 3 7 5 10l4 8h0c0 3 0 10 1 12h0c1 1 1 2 0 3v1l-1 1c-1-7-3-15-6-21-3-5-6-9-7-14v-7c0-3 1-6 1-9z" class="Y"></path><path d="M464 551c-2-3-2-7-2-11l1-20c0-4 0-9 1-12v2c0 1 1 1 2 2 1 4 1 8 1 13v16c0 7 2 13 4 19l-1 1v-2h-1v-4c-1-1-1-1-1-2v-3c-1-1-1-2-1-3v-4h-3l-1-1v5c0 2 1 3 1 4z" class="B"></path><defs><linearGradient id="v" x1="423.275" y1="322.897" x2="427.057" y2="351.325" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#v)" d="M422 323c1 0 2 0 3 1l1 1v1 4c0-1 0-1 1-2l1-4c1 5 0 10 0 14-1 3-1 7 1 10l2 2h0l1-1h1c0 2-1 3-3 5l-3-4c-6-9-7-16-5-27z"></path><path d="M454 92h1c4 1 7 5 10 8l5 6c0 1 1 3 2 4h1c2 1 5 7 6 9 0 2 1 3 2 5s1 5 2 7l-1 1c-2-6-7-10-10-16-1-2-2-3-4-4-2-5-5-8-8-12l-6-8z" class="U"></path><path d="M489 365l1-4c1 1 0 1 1 1v-1c1 2-1 5-2 7 2-2 3-3 4-5 2 0 2 0 3 1-1 1-1 3-2 3-2 3-4 6-6 8l-1-1-4 2c-1 1-2 1-3 2v1h-1c-1-3-1-7-2-10l1 1 1 2c0-2 0-2 1-3 0-1 0-2 1-3h1v-1c1 0 1-1 1-2v-1h3 0v1c1 0 1 1 2 2l-1 2h1c0-1 0-1 1-2z" class="k"></path><path d="M480 369l1 1c0-1 0-2 1-3v2h1c0-2 0-2 1-2h1l1 1h-1v3l-1 2c0-1 1-3 0-4-2 1-3 2-4 4h-1v-1c0-2 0-2 1-3z" class="O"></path><path d="M484 374c2-1 5-6 7-6 1-1 2 0 3-1-2 3-4 6-6 8l-1-1-4 2 1-2z" class="g"></path><path d="M479 372v1h1c1-2 2-3 4-4 1 1 0 3 0 4v1l-1 2c-1 1-2 1-3 2v1h-1c-1-3-1-7-2-10l1 1 1 2z" class="J"></path><defs><linearGradient id="w" x1="483.351" y1="348.404" x2="474.089" y2="367.905" xlink:href="#B"><stop offset="0" stop-color="#222"></stop><stop offset="1" stop-color="#50504f"></stop></linearGradient></defs><path fill="url(#w)" d="M480 346v-1l1 1v2l1 1 1-1v1l1 1-1 5c0 2-1 6 0 8 0 1 0 2-1 2v1h-1c-1 1-1 2-1 3-1 1-1 1-1 3l-1-2-1-1-1-1h-3v-1c0-3 0-6 1-9h1c1-2 0-3 1-5 1 1 1 1 2 1h0c0-2 0-3 1-4h1v-4z"></path><path d="M475 358h0c1 3 4 9 3 12l-1-1-1-1h-3v-1c0-3 0-6 1-9h1z" class="K"></path><defs><linearGradient id="x" x1="513.25" y1="384.007" x2="520.75" y2="404.494" xlink:href="#B"><stop offset="0" stop-color="#3d3c3d"></stop><stop offset="1" stop-color="#737372"></stop></linearGradient></defs><path fill="url(#x)" d="M515 385c0-2 1-5 1-7l1 1v2l1 1v-2l1-1v1c0 1 1 4 1 5l1-1c0-2 0-4 1-6v18c1 0 1 2 0 3v-1c-1 2-1 4-1 7h-1v-3h0c-2 2-1 6-1 9h-1l-1-8h-1-1 0v-1-10-3c1-1 1-2 0-4z"></path><path d="M515 403c1-1 1-2 1-3 1 1 1 2 1 3h0-1-1z" class="Y"></path><path d="M518 380l1-1v1c0 1 1 4 1 5l1-1-1 8-1-5-1-1v5l-1-1c0-2-1-5 0-6l1-1v-1-2z" class="P"></path><path d="M355 641c1 1 1 1 1 2v6c-1 4 0 8-1 12h0-1c0 2 0 4-1 6-1 1-1 2-1 3l-3 8v1l-2 2c0-2 0-4 1-5 1-7 1-14 1-20 1-6 2-11 6-15z" class="G"></path><g class="B"><path d="M352 655c0 4 0 8 1 12-1 1-1 2-1 3l-3 8v1l-2 2c0-2 0-4 1-5v-1c2-2 2-4 3-6 2-4 1-9 1-14h0z"></path><path d="M355 641c1 1 1 1 1 2v6c-1 4 0 8-1 12h0-1c0 2 0 4-1 6-1-4-1-8-1-12l1-4-1 1-1 2c0-2 0-2 1-4l-1-1c0 2 0 5-2 7 1-6 2-11 6-15z"></path></g><path d="M353 651l1-2v1 11c0 2 0 4-1 6-1-4-1-8-1-12l1-4z" class="j"></path><defs><linearGradient id="y" x1="476.968" y1="428.107" x2="464.81" y2="444.696" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#373736"></stop></linearGradient></defs><path fill="url(#y)" d="M469 419l1-2c0 3 1 6 1 8s0 3 1 4c1 4 2 7 2 11 0 2 1 4 1 7l1 2c-1 2-1 4-1 6h1v3l-2 2v-8c0-2 0-4-2-7l-1 1v6 5-1l-1-2v-1c0-4-1-9-2-13 0-1 0-4-1-4 0-1 0-1-1-2 1-4 1-9 1-13l1-2 1 1v-1z"></path><path d="M471 446v-9c1 2 1 5 1 8l-1 1z" class="M"></path><defs><linearGradient id="z" x1="446.509" y1="435.229" x2="433.018" y2="448.245" xlink:href="#B"><stop offset="0" stop-color="#4a494a"></stop><stop offset="1" stop-color="#7b7b7b"></stop></linearGradient></defs><path fill="url(#z)" d="M436 424l1 1h0c1 1 1 1 1 2 2 1 3 2 4 4v1 1 1l-1-1-1 1v4c0 4 1 10 2 13 0-2-1-11 1-12v1c0 2 0 5 1 7v2 5l-1 1c0 2 0 3-1 5-2-3-4-7-5-11 0-2 0-4-1-6s-1-3-1-5v-6c1-2 0-5 1-8z"></path><path d="M478 575l1 1v4 2l1 1h1v4c1 1 1 3 1 5v4 2c0 1 0 2 1 3h0v2c1 1 1 4 1 6 1 1 0 5 0 7 1 1 1 7 1 9h0v-6c1-2 1-9 1-10-1-1-1-3-1-3v-1l-1-1c-1-2 0-1 0-3v1c2 2 0 0 1 2h1l1 1v4c1 2 1 11 0 14h0v-12l-1-2c-1 6 0 12-1 18v8h-1c-1-2 0-18-1-22 0-2 1-5-1-7v4 10l-1-4v-2c-3-12-4-26-3-39z" class="E"></path><path d="M442 432c0-3 1-5 2-7 0 2 0 7 1 8v-5l1-1v3c1 3 1 6 1 8h0c0-1 1-2 1-3 0 4 1 7 1 10s-1 8 0 11v4h-1v-1h0c-1 1-1 2-1 2-2-2-3-4-3-7v-5-2c-1-2-1-5-1-7v-1c-2 1-1 10-1 12-1-3-2-9-2-13v-4l1-1 1 1v-1-1z" class="O"></path><path d="M446 430c1 3 1 6 1 8h0c0-1 1-2 1-3 0 4 1 7 1 10 0 1-1 2-1 3l-1-5c0-2-1-3-1-5v-8z" class="k"></path><defs><linearGradient id="AA" x1="444.663" y1="444.323" x2="447.209" y2="458.787" xlink:href="#B"><stop offset="0" stop-color="#575556"></stop><stop offset="1" stop-color="#787878"></stop></linearGradient></defs><path fill="url(#AA)" d="M444 449c0 1 0 1 1 2v1-2c1-2 1-3 1-5l1-2 1 5c0-1 1-2 1-3 0 3-1 8 0 11v4h-1v-1h0c-1 1-1 2-1 2-2-2-3-4-3-7v-5z"></path><path d="M449 445c0 3-1 8 0 11v4h-1v-1-11c0-1 1-2 1-3z" class="W"></path><path d="M569 193l3-1 1 1s1 1 2 1l2 1s1 1 2 1l2-1 11 9 9 7h0c-1 0-2 0-3-1l-1 1 1 1h-1c-2 1-2 0-3-1-2-1-2-1-4-1l-3-3-3-1c-1-2-2-3-4-4-2-2-3-3-6-4l-3-1c-1-2-3-2-4-4h2z" class="D"></path><path d="M585 203v-1c0-1-1-1-1-2h0c3 0 5 3 7 4s3 3 5 5h0l2 1-1 1c-2-2-5-3-7-5s-3-2-5-3z" class="H"></path><path d="M587 207c-1-1-1-2-2-2v-1-1c2 1 3 1 5 3s5 3 7 5l1 1h-1c-2 1-2 0-3-1-2-1-2-1-4-1l-3-3z" class="C"></path><path d="M587 545v6l-1 3c-1 1-1 2-1 3l-1 1c0 2-1 4-1 7 0 2-1 10 0 12s0 4 1 7v5l-2 1c1 4 1 8 1 12-1-1 0-2-1-3-2-1-2-2-2-4h0c-1-2-1-4-1-5l-1 3v-8c0-3 0-7 1-9l1-3v-2c1-2 1-5 1-8s0-6-1-9v-1h1c0 1 0 1 1 1 3-2 4-6 5-9z" class="O"></path><path d="M583 577c1 2 0 4 1 7v5l-2 1v-4c1-3 1-6 1-9z" class="G"></path><path d="M580 573c0 2 1 4 1 5 1 3-1 5 1 8v4c1 4 1 8 1 12-1-1 0-2-1-3-2-1-2-2-2-4h0c-1-2-1-4-1-5l-1 3v-8c0-3 0-7 1-9l1-3z" class="f"></path><path d="M581 578c1 3-1 5 1 8v4c1 4 1 8 1 12-1-1 0-2-1-3-1-2-2-5-2-8 0-4 0-9 1-13z" class="K"></path><path d="M473 541c0 6 0 12 1 17 0-2 1-5 1-7v-10-12c-1-6-1-12-2-18 0-1 0-2-1-3v-4l-1-2v-1-2-1-1c1 2 0 2 1 4h0v3 1l1 1c0 1 0 4 1 6v4c1 2 1 6 1 9 1 6 2 12 2 18 0 8-1 16-1 23 0 2 0 4-1 6v-6l1-1c0-2 0-9-1-11v2c0 1 0 2-1 3v2 2c-1 1-1 2-1 4h-1v1c-1-2-1-3-1-4s-1-2-1-3l1-1v-2-1c-1-3-2-7-2-11-1-4-1-9 0-13v-3c2 1 3 9 4 11z" class="X"></path><path d="M410 266c1 1 0 1 0 3h2c-1 2-2 3-3 5h0 1l1 1-2 3c-2 2-3 7-5 10l2 5-2 1-2 1-7 6v-2c1-1 1-1 1-2v-4c2-5 5-11 7-16 2-3 2-6 4-9h1l-1-1h0l3-1z" class="R"></path><path d="M396 293h1c0-1 1-2 1-2 1-3 3-8 5-10h0l1-1h0v1l-1 1-2 5v2c-1 1-1 1-1 3l4-4 2 5-2 1-2 1-7 6v-2c1-1 1-1 1-2v-4z" class="S"></path><path d="M400 292l4-4 2 5-2 1h-2c-1 1-2 2-3 2h-1v-2l2-2z" class="f"></path><path d="M616 350l1 1c0 1 0 2 1 3v1 1c1 2 2 3 3 4 1 4 4 7 6 10h1v1c-2-1-2-2-4-2 0 0 0 1 1 2v1l1 2c1 2 3 5 5 6 1 0 2 1 3 1v1c-4 0-7 0-10-2l-1-1c-2-1-3-3-5-5h0c1 0 3 3 5 3-2-2-7-6-8-10v-3-4l1-1c-1-1-1-2-1-3 1-2 1-4 1-6z" class="P"></path><path d="M380 734v-1c1-2 0-2 0-4h0v-1c-1-2 0-3-1-4 0-2 0-2-1-3 0-2 0-2 1-3v-1h1c1 3 2 5 2 8 0 1 0 1 1 2h0c0 1 0 2 1 3v2h0v1l1 1v2c1 1 1 1 1 3h1v2h0c1 1 0 1 1 2 0 1 0 2 1 4 1 1 1 3 2 5 0 2 2 4 2 6h0-1l-1 2h0c-1-1-2 0-3 0-2-2-3-7-3-10-1-2-2-3-2-5l1-1c-1 0-1-1-1-2l-1-2c-1-2-1-4-2-6z" class="E"></path><path d="M384 744c1 5 4 9 5 13 1 1 2 1 3 1l-1 2h0c-1-1-2 0-3 0-2-2-3-7-3-10-1-2-2-3-2-5l1-1z" class="I"></path><path d="M446 345c0-2 1-5 2-7h1l-1 3c-1 3-1 8-1 11h1c1-1 1-4 1-6h1c0 1 1 3 0 5 0 1 0 0 1 1v4c1-1 0-3 2-4v1s0 1 1 1l-2 6c-3 8-7 18-15 22l-3 2c0-1 2-3 2-3h1l2-2c1-1 2-1 3-3h-1v-3c1-1 1-1 1-2 1-2 0-4 1-5 0 1 0 1 1 2v-2h1 0v-5c-1-4-1-9 0-13v5h1v-8z" class="Y"></path><path d="M442 371c1-2 0-4 1-5 0 1 0 1 1 2v-2h1c0 3 0 5-1 7l-1 1c0 1-1 2-1 2h-1v-3c1-1 1-1 1-2z" class="o"></path><path d="M446 345c0-2 1-5 2-7h1l-1 3c-1 3-1 8-1 11h1c1-1 1-4 1-6h1c0 1 1 3 0 5 0 1 0 0 1 1v4c1-1 0-3 2-4v1s0 1 1 1l-2 6-1-1c0 1-1 2-2 2h0c1-2 1-6 0-7-1 1-1 3-1 5-1-1-1-2-1-3 0-3 1-9-1-11z" class="e"></path><path d="M395 769l1 1h0v2c1-1 1-2 1-2l1-1c1 1 3 8 3 11h0l1 3c1 3 1 10 3 12 0 1 1 1 0 1-1-1-2-2-4-2l-2 4-1 1h0l-2-5-3-10h2c0-1-1-5-1-6h0v-6c0-1 0-2 1-3z" class="W"></path><path d="M399 798c-2-5 0-9 1-14 0 4 2 7 1 10l-2 4z" class="b"></path><path d="M394 778l1-1h0c1 2 1 3 1 5v1h1v3c-1 3 0 5-1 8l-3-10h2c0-1-1-5-1-6z" class="G"></path><path d="M395 769l1 1h0v2c1-1 1-2 1-2l1-1c1 1 3 8 3 11h0c0 1 0 2-1 3l-3-8v8h-1v-1c0-2 0-3-1-5h0l-1 1h0v-6c0-1 0-2 1-3z" class="I"></path><path d="M327 583c1-1 1-2 1-3 1 1 1 2 1 4v3c0 8 1 14 2 22 0 2 0 3 1 5l-1 1v-1c-1 0 0 0-1-1l-2-4v1 2 1c0 1 0 1 1 2v2h-1c-1 2 0 4 0 6v1l-1-2-1 2c-2-5-3-11-2-16 1-8-1-17 3-25z" class="j"></path><path d="M327 583c1-1 1-2 1-3 1 1 1 2 1 4-1 1-1 4-1 5h-1v-6zm0 16c1-1 0-3 1-4 0 4-1 10 0 14v1 2 1c0 1 0 1 1 2v2h-1c-1-6-2-12-1-18z" class="E"></path><path d="M327 622c-1-2-2-5-2-8-1-4-1-12 2-15-1 6 0 12 1 18-1 2 0 4 0 6v1l-1-2z" class="M"></path><path d="M532 350l2 2c0 1-1 2 0 3v2-4-1c1-2 1-3 1-4s0-1 1-2v3h1l1 8h0 1l1 3 1 3 1 1h1c1 0 0 0 1 1v2l1 1h0l1-1v-2h1c0 1 1 2 1 2v4l-1 2v7 2l-2-2h-1c0-2-2-3-3-5h-1c-1-2-2-3-2-5l-1-1c0-2-1-4-2-6-1-4-2-8-3-13z" class="E"></path><path d="M538 370h1l-1-3c-2-4-3-9-3-13 1 2 1 5 3 7v1c1 1 1 1 1 2 0 2 0 2 1 4h0c1 2 1 5 2 6 1 2 4 4 3 6h-1c0-2-2-3-3-5h-1c-1-2-2-3-2-5z" class="B"></path><path d="M538 357h1l1 3 1 3 1 1h1c1 0 0 0 1 1v2l1 1h0l1-1v-2h1c0 1 1 2 1 2v4l-1 2v7 2l-2-2c1-2-2-4-3-6-1-1-1-4-2-6h0c-1-2-1-2-1-4 0-1 0-1-1-2v-5z" class="c"></path><path d="M545 368l1-1v-2h1c0 1 1 2 1 2l-1 1c-1 2-1 4-1 6-1 0 0 0-1-1v-2h-1v-1l-1-1v-1c0-1 1-1 1-1l1 1h0z" class="O"></path><path d="M538 357h1l1 3 1 3 1 1h1c1 0 0 0 1 1v2s-1 0-1 1v1c1 2 1 3 1 4h-1l-2-5h-1 0c-1-2-1-2-1-4 0-1 0-1-1-2v-5z" class="M"></path><path d="M543 364c1 0 0 0 1 1v2s-1 0-1 1v-4z" class="P"></path><defs><linearGradient id="AB" x1="569.827" y1="435.644" x2="560.789" y2="452.146" xlink:href="#B"><stop offset="0" stop-color="#323131"></stop><stop offset="1" stop-color="#4b4b4b"></stop></linearGradient></defs><path fill="url(#AB)" d="M559 412l1-1c2 3 4 7 5 10h1c3 5 4 12 5 18v2 6 3c0 1 0 2-1 4 0 1-1 3-2 4-2 1-2 2-4 3l1-2v-2c0-4 1-8 1-12s-1-9-2-13h1 0c1-4-2-9-4-13l-2-7z"></path><path d="M569 448c-1-3 0-6 0-9l-1-2v-1-3h0v2c1 1 1 3 2 4h1v2 6 3l-2-2z" class="K"></path><path d="M569 448l2 2c0 1 0 2-1 4 0 1-1 3-2 4-2 1-2 2-4 3l1-2c3-3 4-7 4-11z" class="Y"></path><path d="M524 283h1l2-1h1c1 0 2 1 3 2h1c1 0 1-1 2-1 1 7-1 11-5 16v3l2 1h0 2c0 1 0 2 2 2h1l-1 1v1h0c2 0 3 1 5 2-2 1-4 2-6 2-1 1-2 1-3 1h0l-1 1-4-3v-2-1h-2c-1 1-1 1 0 2h-2c-1-1-1-2-1-2l-1-1v-3h3v-1l2-2h1 1c1-1 0-2 0-3 1-1 3-2 4-3 0-2 0-3 1-4s1-1 0-2l-3-3h-3 0c-2 0-3 1-4 3 0 1-1 2-2 3 0 1 0 1-1 2h0v-2-1l1-1c0-1 1-2 2-2 0-1 1-2 2-3v-1z" class="X"></path><path d="M529 299v3l2 1h0 2c0 1 0 2 2 2h1l-1 1v1h0c2 0 3 1 5 2-2 1-4 2-6 2-1 1-2 1-3 1h0l-1 1-4-3v-2-1h2c-1-1-3-1-4-2l1-1c1 1 2 1 3 1v-2h-3l4-4z" class="g"></path><path d="M528 307l3 1v1l-3-1c2 3 3 3 6 3-1 1-2 1-3 1h0l-1 1-4-3v-2-1h2z" class="T"></path><defs><linearGradient id="AC" x1="466.271" y1="414.69" x2="450.523" y2="420.777" xlink:href="#B"><stop offset="0" stop-color="#080709"></stop><stop offset="1" stop-color="#464745"></stop></linearGradient></defs><path fill="url(#AC)" d="M455 400h0c1-1 1-2 1-3v-4l1-3 1-1c-1-1-2-3-2-4l1 1c0 1 1 1 2 3 0 0 0 2 1 2v1 2 1l1 1h0l1 1c-1 1-1 1-1 2h0v3h-1c-1 2 0 11 1 14v3c1 2 1 5 1 8v16c-1 1-2 1-3 2-1-9 1-18 0-27l-1 2c-1-6-2-12-2-19l-1-1z"></path><path d="M456 401v-2l1 1c0 6 1 12 2 18l-1 2c-1-6-2-12-2-19z" class="m"></path><path d="M547 620c2 0 3 0 5-1l1 1 1-1c1 2 0 7 0 9v1c-1 2-1 7-2 10v5h-1v-6h-1v5h-1v-3h-1v4c-1-1-1 0-2-1v1c-1 1-2 2-2 3-2-2-1-5 0-7l2-17 1-3z" class="V"></path><path d="M548 640h-2v-1c0-1 0-4 1-6 0-4 1-8 4-12h0c1 2 0 3 0 5v6c-1 2-1 3-1 4v2 5h-1v-3h-1z" class="N"></path><path d="M597 386s0-1 1-1v-1c-1-1-1-2-1-3l-1-1v-1h0v-1l-1-1v-1h0v-1l-1-1c0-1 1-2 0-3v-1c-1-2 0-4 1-6h1v4l1 1c0 2 0 2 1 4v-1-1h0c-1-1-1-1-1-3h1c0 1 0 2 1 3 0-1 0-1 1-1 1 2 1 4 2 6l3 9c1 3 4 5 4 8h-1v1c1 1 1 2 0 3 0 1 0 2 1 3h0v5l-1 1c0-2-1-4-2-6l-3-9-1-4c0 1-1 2-1 3h-1v-1s0-1-1-2v-2l-2 1z" class="X"></path><path d="M599 385l-2-8c2 3 4 6 5 10h0c0 1-1 2-1 3h-1v-1s0-1-1-2v-2z" class="M"></path><defs><linearGradient id="AD" x1="594.923" y1="374.142" x2="612.577" y2="399.858" xlink:href="#B"><stop offset="0" stop-color="#292727"></stop><stop offset="1" stop-color="#4e4e4e"></stop></linearGradient></defs><path fill="url(#AD)" d="M599 371c0-1 0-1 1-1 1 2 1 4 2 6l3 9c1 3 4 5 4 8h-1v1c1 1 1 2 0 3 0 1 0 2 1 3h0v5l-1 1c0-2-1-4-2-6l-3-9h1v-3l-3-8c-1-1-2-3-2-5 1-1 0-3 0-4z"></path><defs><linearGradient id="AE" x1="611.116" y1="502.675" x2="619.309" y2="502.887" xlink:href="#B"><stop offset="0" stop-color="#484747"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#AE)" d="M616 478c1-1 1-2 2-3 0 2 1 4 2 6h-1c2 4 0 9 1 13l-1 10c-1 5-2 10-4 14 0 0-1 1-1 2v3h0c0 1-1 2-2 3v1h0l-1 1-1-1c1-2 2-4 1-6h-1c1-2 1-4 2-7l2-10v-1l1-9 1-5c-1-1-1-3-1-5l1-6z"></path><path d="M616 478c1-1 1-2 2-3 0 2 1 4 2 6h-1l-1-1h0v-1c-1 2-2 3-2 5h-1l1-6z" class="K"></path><path d="M616 489h0l1-4h0c1 2 1 5 1 8h0c-2 2-1 4-2 7h0c0 3-1 7-1 10-1-2 0-5-1-6v-1l1-9 1-5z" class="Y"></path><path d="M587 141c2 5 2 9 2 14 0 4 0 8-1 12l-2 4c3-2 6-4 9-5-2 5-6 10-11 12-2 1-4 2-6 2h-1c-1-1-2-1-4-1l1-2c2 0 5-1 7-2h-3l2-2c2-3 2-6 3-8 2-4 2-8 1-11l2-1v-1c1-2 1-4 0-7v-1c1-1 1-2 1-3z" class="L"></path><path d="M588 167c-1-1-2-2-1-3v-2c-1-2 0-3 0-5 1 0 0 0 1 1v1l1-4c0 4 0 8-1 12z" class="D"></path><path d="M581 175h0 2c1-1 2-1 3-1h1l-1 1c-1 0-2 1-3 2l1 1c-2 1-4 2-6 2h-1c-1-1-2-1-4-1l1-2c2 0 5-1 7-2z" class="H"></path><path d="M430 204l13-8c1 0 1 0 2 1l-2 1h1v2l1-1c1 1 3 1 4 1l1 1h-1v1l1 1-2 1-2 2v1c1 0 0 0 1 1l-3 2h0l-2 2-1-2-2 1v-1l1-1-1-1c-2 1-4 2-6 2v-1l-8 3h-3c1-2 6-6 8-8z" class="S"></path><path d="M433 209l6-3h1v1l3-1h1l-2 2 1 1-2 1-2 1v-1l1-1-1-1c-2 1-4 2-6 2v-1z" class="H"></path><path d="M443 198h1v2l1-1c1 1 3 1 4 1l1 1h-1v1l1 1-2 1-2 2v1c1 0 0 0 1 1l-3 2h0l-2 2-1-2 2-1-1-1 2-2h0l1-1c-2-1-2 0-3-1 0-1 1-1 2-2-1-1-1-1-2-1-4 1-7 5-11 6 4-3 9-5 12-9z" class="g"></path><path d="M445 199c1 1 3 1 4 1l1 1h-1v1l1 1-2 1h-2v-2h-1v-2-1z" class="Y"></path><defs><linearGradient id="AF" x1="442.136" y1="202.281" x2="428.863" y2="202.704" xlink:href="#B"><stop offset="0" stop-color="#9a979c"></stop><stop offset="1" stop-color="#c0c0bd"></stop></linearGradient></defs><path fill="url(#AF)" d="M430 204l13-8c1 0 1 0 2 1l-2 1c-3 4-8 6-12 9 0 1-1 1-2 1-1 1-2 1-3 2l-1 2h-3c1-2 6-6 8-8z"></path><path d="M412 265c1 0 2 0 3 2h0 2v2 3l1 1 1 1-2 1 1 1 1-1c1 1 1 2 0 3h0c-2 2-2 4-4 6-2 0-3 3-4 4v1h1v1c-3 1-6 5-9 6l-1-1 2-1 2-1-2-5c2-3 3-8 5-10l2-3-1-1h-1 0c1-2 2-3 3-5h-2c0-2 1-2 0-3l2-1z" class="c"></path><path d="M409 278h0c1 2 0 5-1 7h0c-1 1-1 2-2 3 2-2 4-3 5-6h1c-2 4-3 8-6 11l-2-5c2-3 3-8 5-10z" class="b"></path><path d="M415 279l1 1c1-2 1-3 3-4v2c-2 2-2 4-4 6-2 0-3 3-4 4v1h1v1c-3 1-6 5-9 6l-1-1 2-1 2-1c3-3 4-7 6-11h1c0-1 1-2 2-3z" class="e"></path><path d="M412 265c1 0 2 0 3 2h0 2v2 3l1 1 1 1-2 1 1 1 1-1c1 1 1 2 0 3h0v-2c-2 1-2 2-3 4l-1-1v-3c-2 1-2 1-3 2v1h-1c1-2 1-3 1-4v-1c0-1 1-2 1-4h0c-1-1 0-1 0-2v-1l-2 1c0-1 1-2 1-3z" class="W"></path><path d="M629 716l2 1v1h0c0 2 0 3-1 4-1 3 0 5 0 7h2c1 2 1 4 1 6s1 3 2 4h0c-1 1-1 1-1 3l-1 1h0c-2 0-2 1-3 2l-1 1c-2 2-3 5-3 8-2-10-1-20-2-30 4-2 1-4 3-7l2-1z" class="M"></path><path d="M629 719c0 3 0 6-1 9 0 2 1 4 1 6 0 3 0 8 1 11l-1 1c-1-2 0-5-1-7-1-3 0-6 0-9-1-4-1-7-1-11h2 0z" class="Y"></path><path d="M629 716l2 1v1h0c0 2 0 3-1 4-1 3 0 5 0 7h2c1 2 1 4 1 6s1 3 2 4h0c-1 1-1 1-1 3l-1 1h0c-2 0-2 1-3 2-1-3-1-8-1-11 0-2-1-4-1-6 1-3 1-6 1-9v-3z" class="R"></path><path d="M630 729h2c1 2 1 4 1 6s1 3 2 4h0c-1 1-1 1-1 3l-1 1h0-1v-2c-1-4-1-8-2-12z" class="h"></path><path d="M328 623c1 1 2 2 2 4l2 7c0 1 1 2 2 3h0l4 9-1 1c-1-2-1-4-2-5-1-2-3-3-3-5l-1-1c-1-1-1-1-1-2 0-2-1-4-2-6 0 1 0 2 1 3v2 1l1 1v2c1 0 1 1 1 1v1c2 6 5 10 3 16-1 1-1 2-2 2-1-1-1-2-1-3s-1-3-1-4c-3-3-6-7-8-11l-1-5h1l2 3h0v-5h0v-4c3 4 4 8 6 11l1 1-1-1-1-3-3-12h0l1-2 1 2v-1z" class="B"></path><path d="M324 637v-5c2 6 6 11 9 17 0 2 0 5 1 6-1 1-1 2-2 2-1-1-1-2-1-3s-1-3-1-4c-3-3-6-7-8-11l-1-5h1l2 3h0z" class="F"></path><path d="M322 634l2 3h0l4 9c2 3 4 5 3 8 0-1-1-3-1-4-3-3-6-7-8-11l-1-5h1z" class="G"></path><path d="M441 109l1 2v5h0l1-3h1l-1 7c0 1 1 1 2 2-1 3-1 6-2 9v5 12 5h-2-1c-2 3-2 4-2 6l-1 1c0-3-1-7-1-10 1-3 2-7 3-10 0-5-1-11 0-16 0-5 1-10 2-15z" class="D"></path><path d="M436 150c1 1 2 1 3 2l2-1c0-1 0-2 1-4h0c1-6-1-9-1-14 0-4 1-10 2-13 0 1 1 1 2 2-1 3-1 6-2 9v5 12 5h-2-1c-2 3-2 4-2 6l-1 1c0-3-1-7-1-10z" class="d"></path><path d="M442 411c1-3 1-5 1-8 1-5 2-9 5-12 1 1 1 0 1 1v1c0 3-1 6-1 8v1 1c0 1-1 1-1 2v1c0-1-1-2-1-3h-1c0 2 0 4 1 6 0 0 1 3 1 4 1 4 1 8 2 12v-1c1 1 1 3 1 4v8c-1-2-1-4-1-6-1 1-1 4-1 5s-1 2-1 3h0c0-2 0-5-1-8v-3l-1 1v5c-1-1-1-6-1-8-1 2-2 4-2 7v-1c0-6-1-10-1-15v-3l1-2z" class="M"></path><path d="M441 416v-3l1 2h0v4l-1-3z" class="B"></path><path d="M442 411c1-3 1-5 1-8 1-5 2-9 5-12 1 1 1 0 1 1v1c0 3-1 6-1 8v1 1c0 1-1 1-1 2v1c0-1-1-2-1-3h-1c0 2 0 4 1 6 0 0 1 3 1 4l-1-1c-1 2-2 6-3 8 0-1 0-2 1-4v-7l-1 1-1 5h0l-1-2 1-2z" class="j"></path><defs><linearGradient id="AG" x1="336.672" y1="597.21" x2="317.291" y2="602.577" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#403f3f"></stop></linearGradient></defs><path fill="url(#AG)" d="M320 594c1-10 5-19 10-27h0c0 4-1 9-2 13 0 1 0 2-1 3-4 8-2 17-3 25-1 5 0 11 2 16h0l3 12 1 3 1 1-1-1c-2-3-3-7-6-11-2-8-3-16-3-24-1-3 0-6-1-9v-1z"></path><path d="M573 583l1-1c0-1 1-2 1-4l1 1v2h0v-1c1 1 1 5 2 5v8l1-3c0 1 0 3 1 5h0c0 2 0 3 2 4 1 1 0 2 1 3 0 1 1 9 0 10-1-2-1-5-3-7v2c0 3 0 6-1 9-1 2-1 3-1 4s0 1-1 2c0-4 0-7 2-10v-1c0-2 0-2-1-4l-2-2-7-4c2-3 3-7 3-9v-5l1-4z" class="R"></path><path d="M575 594v-3c0-2 0-4 1-6v9c1 2 1 4 1 6h0l-1-1-2-4 1-1z" class="i"></path><path d="M573 583l1-1c0-1 1-2 1-4l1 1v2h0v4c-1 2-1 4-1 6v3l-3-2v-5l1-4z" class="h"></path><path d="M572 592l3 2-1 1 2 4-1 1h0v1 1l1 3-7-4c2-3 3-7 3-9z" class="l"></path><path d="M575 602l-3-2v-2c1-1 1-1 1-2v-1h1l2 4-1 1h0v1 1z" class="Z"></path><path d="M578 593l1-3c0 1 0 3 1 5h0c0 2 0 3 2 4 1 1 0 2 1 3 0 1 1 9 0 10-1-2-1-5-3-7v2c0 3 0 6-1 9-1 2-1 3-1 4s0 1-1 2c0-4 0-7 2-10v-1c0-2 0-2-1-4l-2-2-1-3v-1-1h0l1-1 1 1c0 1 1 2 2 2h0l-1-3v-1l1 1h1l-2-6z" class="c"></path><path d="M365 704c0 3-1 6 1 9v2c0 1 1 2 1 4 1 2 1 3 2 5 0-1 0-4-1-5v-3c-1-1-1-2-1-3v-3c-1-1-1-2-1-3v-4h1c0 3 0 5 1 8l-1-10h1v1l1-1c0-2 0-1 1-3s-1-5 1-7c-1-1-1-2-1-3v-1-1c-1-1-1-2-1-3v-1-4-3 2 1 1c1 1 1 1 1 2h0l1 2v4h0v-1c1-1 1-3 1-4 0 2 0 6-1 8 0 1 0 1 1 2 0 1-1 1-1 2v6h0c-1 0-1 0-1 1v4h-1v4 2c1-1 0-5 1-6l1 1c0 2-1 4 0 6 0 1-1 1 0 2 0 1 0 2 1 3v2c-2 2-1 9 0 12v6c2 5 6 16 4 20h-2c-1-3-1-5-1-7-1-1-1-2-2-3-1-5-1-10-2-14 0-2 0-6-1-7-1-3-1-5-2-8l-1-5c-1-2-1-6 0-9z" class="X"></path><path d="M566 195l5 2 3 1c3 1 4 2 6 4 2 1 3 2 4 4l3 1 3 3c2 4 8 7 12 11 1 1 1 2 3 3l-1 1c-1-1-1-1-2-1 0 1 0 1 1 2l-1 1-2-1 1-1c0-1 0-1-1-2v-1c-1-1-3-3-5-4s-4-3-6-5-4-2-6-4h0l-1-1h-1c-1 0-3 0-5-1h-1c-1 0-2 0-3 1-1-1-2-1-3-1 1-1 1-1 2-1l-1-3-3-1c-1-2-1-2-2-3h1 0c-2-1-3-2-5-2l-1-1h2c1-1 2-1 4-1z" class="l"></path><path d="M570 203l1 1h1 3 0 0 0 3c2 1 3 1 5 2-2 0-3 0-4-1h-2c1 1 3 2 4 3-1 0-3 0-5-1h-1c-1 0-2 0-3 1-1-1-2-1-3-1 1-1 1-1 2-1l-1-3z" class="O"></path><path d="M566 195l5 2 3 1h-1-3v1h1c2 0 5 2 7 3h0v1h-1l1 1h-3 0 0 0-3-1l-1-1-3-1c-1-2-1-2-2-3h1 0c-2-1-3-2-5-2l-1-1h2c1-1 2-1 4-1z" class="J"></path><path d="M567 202c-1-2-1-2-2-3h1c2 1 3 1 5 2 2 0 3 1 4 1v2h0-3-1l-1-1-3-1z" class="B"></path><defs><linearGradient id="AH" x1="610.874" y1="419.25" x2="612.19" y2="439.213" xlink:href="#B"><stop offset="0" stop-color="#b1afb1"></stop><stop offset="1" stop-color="#dad9d8"></stop></linearGradient></defs><path fill="url(#AH)" d="M590 410h3 1c1 3 3 5 4 7 2 2 3 4 5 6l2 2 1 1h0l1-1h1 2 1 1v1c1 1 1 1 2 1v3c0 1-1 1 0 2l1 1v1c3 2 6 1 8 2l1 3 3 1h-7c-8-2-15-7-19-13-5-4-9-12-11-17z"></path><path d="M606 426h0l1-1h1 2 1 1v1c1 1 1 1 2 1v3c0 1-1 1 0 2l1 1v1l-2-2-7-6z" class="R"></path><path d="M611 425h1v1c1 1 1 1 2 1v3c0 1-1 1 0 2l1 1v1l-2-2c-1-3-2-3-2-7z" class="h"></path><defs><linearGradient id="AI" x1="590.375" y1="411.911" x2="598.125" y2="418.089" xlink:href="#B"><stop offset="0" stop-color="#686868"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#AI)" d="M590 410h3 1c1 3 3 5 4 7l-1 1c3 5 6 9 10 13h-1l-5-4h0c-5-4-9-12-11-17z"></path><defs><linearGradient id="AJ" x1="564.787" y1="315.925" x2="565.174" y2="340.576" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#313130"></stop></linearGradient></defs><path fill="url(#AJ)" d="M558 319l2-4-1-1h1l3 3c1 0 1 0 2-1-1 4 3 7 4 11h1v-2c1 1 2 3 3 5v6l-1 1v2 3c-1 0-1 0-1 1s0 1-1 2c0-1-1-3-2-4v1h-1l-1-2-1 1h0c0-2-1-4-2-6l-3-8c0-1-1-3-1-3v-1l-1-4z"></path><path d="M570 327v-2c1 1 2 3 3 5v6l-1 1v2h-1-1v-1c0-1 1-1 1-1l-1-4-1-6h1z" class="M"></path><path d="M570 327c1 1 1 2 1 3l-1 3-1-6h1z" class="I"></path><path d="M560 327c3 1 4 6 5 9v-4c2 1 3 5 4 6v2l1 1 2-1-1-1h1v3c-1 0-1 0-1 1s0 1-1 2c0-1-1-3-2-4v1h-1l-1-2-1 1h0c0-2-1-4-2-6l-3-8z" class="T"></path><path d="M458 519h0v-2c0-6-1-13-1-18l1-15v-1 1c1 2 1 4 1 6 1 5 3 10 5 15h-1l-1 8h0c0 3 1 11 0 14l-1 16c-1-2 0-5 0-7l-1-1c-1 0-1 2-1 3-1 2 0 8 0 10v1 11 5-12c-1-5-2-11-1-15v-2h-1l-1-1v-4-1-2h0v7h-1c-1-1 0-6 0-7l1-2v1l1 2c0 1 0 2 1 3v-13z" class="B"></path><path d="M459 490c1 5 3 10 5 15h-1l-1 8h0c0-8-3-15-3-23z" class="F"></path><path d="M531 325h0c-1 4-3 9-6 11-3 0-2 1-3 2-1 0-3 1-4 1h-1v-1l-1 1c-3 0-4 0-6-1l-1 1-3-3h-2l1 2c-4-2-7-7-9-11h1c1 1 1 2 2 2h3 5l2 1h0c5 0 10 0 14-1l1 1h1c2-1 4-3 6-5z" class="c"></path><path d="M513 334h0c2 0 3 0 5 1v1c-2 1-2 1-4 0-1-1-1-1-1-2zm-9 1c1-1 1-2 2-3s1-1 2-1l4 5c0 1 0 1-1 2h0-1l-1 1-3-3h-2v-1z" class="b"></path><path d="M506 336l1-2h1 0c1 2 2 3 3 4h-1l-1 1-3-3z" class="J"></path><path d="M496 327h1c1 1 1 2 2 2h3 5l2 1c-2 0-4 0-6-1l-1 1-1-1v1c0 2 1 2 2 3s0 1 1 2v1l1 2c-4-2-7-7-9-11z" class="k"></path><path d="M531 325h0c-1 4-3 9-6 11-3 0-2 1-3 2-1 0-3 1-4 1v-1c0-1 0-1 1-2l1 1 1-2h2l2-4h0c-1 0-2 2-3 2-1 1-1 0-2 1l-2-1-1 1-2-1-6-3c5 0 10 0 14-1l1 1h1c2-1 4-3 6-5z" class="T"></path><path d="M545 527l6-3c0 2-2 5-3 6h-1l-2 4c0 1 1 2 1 2v3c-1 2-1 4-1 6h1c0-1 1-2 1-2h0v7l-1 1 1 1c1-1 1-2 2-3h1l-1 2c-1 1-2 2-2 4l-1 1c0 1-1 2-1 3s0 2-1 3h0c-2 3-3 6-5 9l-1 2c0-2 0-3-1-5l5-26-1-1v-6c1 0 1-1 1-2 0-2 1-4 2-5l1-1z" class="Y"></path><path d="M545 527l6-3c0 2-2 5-3 6h-1c-1 0-2 2-3 3h-1l3-4-1-1h-1l1-1z" class="D"></path><path d="M544 554l2-4v1 1l-1 2 1 2c0 1-1 2-1 3s0 2-1 3h0c-2 3-3 6-5 9-1-3 0-7 2-10 2-2 3-4 3-7z" class="R"></path><path d="M544 554c-1 2-2 5-4 8 0-6 4-12 3-18 0-4 1-7 2-10 0 1 1 2 1 2v3c-1 2-1 4-1 6h1c0-1 1-2 1-2h0v7l-1 1 1 1c1-1 1-2 2-3h1l-1 2c-1 1-2 2-2 4l-1 1-1-2 1-2v-1-1l-2 4z" class="o"></path><path d="M595 347l2 1c2-4 5-6 6-10 1-1 1 0 2-1-1 8-6 14-10 20-3 4-6 9-7 14 0 3 1 5 3 8 1 1 3 3 5 4l1 1c-10-4-16-8-20-18l-1-3v-3h1c1 1 1 1 2 3h0c0 2 1 2 2 3 1-1 1-2 1-3l1-1c0 2 0 3 1 4v1c2 0 2 0 3-2 1-3 2-5 4-8 1-3 3-6 4-10z" class="Q"></path><path d="M512 404l1-2 2 1h0 1l-1 2v19 4 7 2h-1-1c-3-6-5-8-11-13 0-1 0-2 1-3 1 1 1 1 2 1l1 1v-1c2-2 1-8 1-11v-3l1-2c1 2 0 4 1 6v-7h0l1-1v2l1-1c0-1 0-1 1-2v1z" class="N"></path><path d="M511 425v-2l1 1h0l1-2h0c1 2 1 5 2 6v7c-2 0-1-7-2-8l-2-2z" class="H"></path><path d="M511 412h1c1 3 1 7 1 10h0 0l-1 2h0l-1-1v2 4l-1-3v-5-1h0l1-8z" class="h"></path><defs><linearGradient id="AK" x1="508.3" y1="405.528" x2="507.7" y2="422.489" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#AK)" d="M509 405h0l1-1v2l1-1v6 1l-1 8h0v1 5c-2-1-3-1-4-3v-1c2-2 1-8 1-11v-3l1-2c1 2 0 4 1 6v-7z"></path><path d="M509 405h0l1-1v2l1-1v6 1l-1 8c-1-2-1-6-1-8v-7z" class="Z"></path><defs><linearGradient id="AL" x1="513.54" y1="409.011" x2="513.238" y2="423.985" xlink:href="#B"><stop offset="0" stop-color="#797877"></stop><stop offset="1" stop-color="#a7a7a6"></stop></linearGradient></defs><path fill="url(#AL)" d="M512 404l1-2 2 1h0 1l-1 2v19 4c-1-1-1-4-2-6h0c0-3 0-7-1-10h-1v-1-6c0-1 0-1 1-2v1z"></path><path d="M511 405c0-1 0-1 1-2v1 1c1 2 1 3 0 5l-1 1v-6z" class="b"></path><path d="M512 404l1-2 2 1h0 1l-1 2v2 1h-1l-2-3v-1z" class="c"></path><path d="M500 280c-1-1-1 0-1-1 1-1 2-1 3-1 3-1 5-1 8-1h12c2 0 5 0 7 1h2l1 2-1 2v2c-1-1-2-2-3-2h-1l-2 1h-1c-3 2-4 2-5 5 0 1 0 1-1 1h-1l2-3v-1h0c-2-1-4 0-6 0h0l2 2c-1 1-1 1-1 3 0 1 0 2-1 3l-2 1c-1 1-1 1-3 1l-1-1c1-1 1-2 2-3 0 0 0-1 1-1h0v-2c0-1-1-2-2-2l-1-1h0c-1-1-2-2-3-2-2-1-3-2-4-3h0z" class="B"></path><path d="M511 294c0-1 1-2 1-4v-3l1 1v-1c0-1-1-1-2-2v-1h3c1 0 5-1 6 1l-1 1v-1h0c-2-1-4 0-6 0h0l2 2c-1 1-1 1-1 3 0 1 0 2-1 3l-2 1z" class="F"></path><path d="M500 280c-1-1-1 0-1-1 1-1 2-1 3-1 3-1 5-1 8-1 1 2 1 3 3 3 3 1 5 0 8 1-3 1-6 1-9 1-4-1-8-2-12-2h0z" class="a"></path><path d="M510 277h12c2 0 5 0 7 1h2c-2 2-7 3-10 3-3-1-5 0-8-1-2 0-2-1-3-3z" class="q"></path><path d="M480 882l12 27c-1 2-1 3 0 6v1c2 4 1 10 1 15l-13-33c-2-3-1-6-1-9 1-2 0-5 1-7z" class="W"></path><path d="M499 300c1 0 1 0 1 1h2v-1h-2l-1-2c-1 0-2-1-2-2v-1l1 1v-1c2 2 4 3 6 4-1 1 0 1-1 1 1 1 2 1 4 1v1h2l1 1h-2-1c2 1 3 1 5 1v2h2c0 1-1 2-2 3h-2l-2 1c-2 1-5 2-7 2l-2 1h-1l-1-1-1 1c-2-1-3-1-4-2-2-1-3-2-4-3v-1h-1l1-2 2-2v-1c3-2 4-3 8-3l1 1z" class="f"></path><path d="M490 303v-1c3-2 4-3 8-3l1 1 1 2c-2 0-2 2-3 3v-2-1c1 0 1 0 2-1h-4c-3 1-4 3-7 4l2-2z" class="i"></path><path d="M507 302h2l1 1h-2-1c2 1 3 1 5 1v2h2c0 1-1 2-2 3h-2l-2 1h0l-1-1-1 1v-1l1-1c-2-1-3 0-4 1v1l-1 1h-1l1-2-1-1c2-1 5-1 6-3-2 0-4-1-6-2v-1h2 2 2z" class="X"></path><path d="M598 533h1l3-3 1 1c-1 2-1 3-2 6h1s1-1 1-2c1-1 1-2 2-2 2-2 3-4 4-6v1c0 2-2 4-1 7h0c1-1 1-2 2-2h1c-2 3-5 6-5 9h0c-1 3-3 6-4 8l-8 17c-2 4-5 8-5 12-1-2 0-6 0-9l1-3 15-26c-3 2-6 5-9 9h0c-2 3-3 6-4 9-1-1 0-2 0-3v-3-1-1h1v-2-4c0-2 2-4 3-5v-2c1-2 2-3 2-5z" class="G"></path><path d="M596 540c1-1 4-4 5-4-2 4-4 9-7 13h-1v-4c0-2 2-4 3-5z" class="X"></path><path d="M443 136c1 1 1 4 1 6 2 1 2 1 3 4v3h1 1 1c-1 2-1 4-1 7h0l1 1c-1 0-2 1-2 2v2 2c2 2 3 2 5 2h3v1h-2v1h2c-1 1-1 1-2 1v1l1 1c-2 1-6 1-9 1h-2-1l-2-2c-1 0-2-1-3-1-1-2-1-5-1-8l1-1c0-2 0-3 2-6h1 2v-5-12z" class="K"></path><path d="M447 163h1c2 2 3 2 5 2v1l-2 1h0-4-1l-1-2c1 1 1 1 3 1 0-1-1-2-1-3z" class="I"></path><path d="M451 167l1 2v1c-2 1-3 0-5 0-2-1-2-3-3-5v-1l1 1 1 2h1 4z" class="p"></path><path d="M437 160l1-1c0-2 0-3 2-6 0 5 0 10 1 15v1h0c-1 0-2-1-3-1-1-2-1-5-1-8z" class="S"></path><path d="M444 142c2 1 2 1 3 4v3h1 1 1c-1 2-1 4-1 7h0l1 1c-1 0-2 1-2 2v2 2h-1v-2h-1c-2-3-1-11-1-14 0-2-1-3-1-5z" class="k"></path><path d="M495 323c0-2-1-5 0-7h2 1v1c1 1 1 1 1 3 0-1 1-1 2-1v1c1 0 1 1 2 1h2c0-1 0-2 1-2h1-1l1-1 2 1-1 2h1v-1c0-1 1-1 3-2v1c-1 0-1 1-1 2h2 1l2-1-2-1v-1h1s0-1 1 0h1c1 0 1 1 1 1l1 1s1 1 2 1h1 0c1 1 1 0 2 1l1-1v1 1h-1c-1 0-2 1-3 1-1 1-1 1-1 2v1c1 0 1 0 2 1l1 1c-4 1-9 1-14 1h0l-2-1h-5-3c-1 0-1-1-2-2h-1c0-1-1-3-1-4z" class="j"></path><g class="E"><path d="M504 327c3 0 7 0 9-1 1 0 1 0 2 1h5c1 0 1 0 2 1l1 1c-4 1-9 1-14 1h0l-2-1h-5c0-1 0-2 1-3l1 1z"></path><path d="M495 323c0-2-1-5 0-7h2 1v1c1 1 1 1 1 3 0-1 1-1 2-1v1c1 0 1 1 2 1l-1 1c1 1 1 1 2 1v-1l1 1 6-1 1 1c-1 1-4 1-4 1l-4 3-1-1c-1 1-1 2-1 3h-3c-1 0-1-1-2-2h-1c0-1-1-3-1-4z"></path></g><path d="M497 322c2 0 2 2 4 2h7l-4 3-1-1c-1 1-1 2-1 3h-3c-1 0-1-1-2-2h-1c0-1-1-3-1-4l2-1z" class="Z"></path><path d="M495 323l2-1c0 2 1 3 2 4h3 1c-1 1-1 2-1 3h-3c-1 0-1-1-2-2h-1c0-1-1-3-1-4z" class="I"></path><defs><linearGradient id="AM" x1="498.78" y1="385.833" x2="503.172" y2="405.454" xlink:href="#B"><stop offset="0" stop-color="#323131"></stop><stop offset="1" stop-color="#575857"></stop></linearGradient></defs><path fill="url(#AM)" d="M505 384h0c1 1 1 2 2 3v4 4l-1 4-1 1v1c1 1 1 2 2 2v5 3c0 3 1 9-1 11v1l-1-1c-1 0-1 0-2-1-1 1-1 2-1 3h-1 0v-1-2c-1-2-2-3-2-4l-1-2-1-3h1 1c1-2 2-2 2-5l-2 3v1l-1-1v-2-4l1-8c2-3 2-8 3-12 1 1 1 1 3 0z"></path><path d="M501 402h1 0 0c0 1 1 2 1 3h0c-1 1-2 1-2 2h0l-2 3v1l-1-1v-2h1c1-2 2-4 2-6z" class="Y"></path><defs><linearGradient id="AN" x1="508.194" y1="389.647" x2="499.636" y2="401.589" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#696866"></stop></linearGradient></defs><path fill="url(#AN)" d="M505 384h0c1 1 1 2 2 3v4 4l-1 4-1 1v1c-1 3-1 5-1 7v7h-1v-10h0c0-1-1-2-1-3h0 0-1l3-16 1-2z"></path><path d="M505 384h0c1 1 1 2 2 3v4h-1 0l-1-5h-1l1-2z" class="T"></path><defs><linearGradient id="AO" x1="499.748" y1="408.714" x2="502.881" y2="414.525" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#999899"></stop></linearGradient></defs><path fill="url(#AO)" d="M505 401c1 1 1 2 2 2v5 3c0 3 1 9-1 11v1l-1-1c-1 0-1 0-2-1-1 1-1 2-1 3h-1 0v-1-2c-1-2-2-3-2-4l-1-2-1-3h1 1c1-2 2-2 2-5h0c0-1 1-1 2-2v10h1v-7c0-2 0-4 1-7z"></path><path d="M505 401c1 1 1 2 2 2v5 3c0 3 1 9-1 11 0-2 0-4-1-6v2h0l-1-1v1l-1-1v-2h1v-7c0-2 0-4 1-7z" class="R"></path><path d="M505 401c1 1 1 2 2 2v5 3c-1-1-1 0-1-1 0-2 0-3-1-4l-1 2c0-2 0-4 1-7z" class="Y"></path><path d="M617 387l2 2v-1c1 1 1 1 1 2l3 3 1 1h1v-1-1h0l4 2 1 1s2 0 3 1 2 2 4 2h2v1h-5 0l2 1h4v1 1c1 1 1 1 2 1v1c-2 2-4 5-7 6-2 1-4 1-6 2h-1-4c-1-3-1-5-2-8v-2-4h0c0-2-1-3-1-4h-2v-1c0-2-1-4-2-6z" class="K"></path><path d="M624 394h1v-1-1h0l4 2 1 1h0l2 2h0-2v1 1l-4-3h0l-2-2z" class="E"></path><path d="M617 387l2 2v-1c1 1 1 1 1 2l3 3 1 1 2 2h0c0 2 1 2 2 3v1l-2-2-1 1c1 2 3 4 3 6-2-3-4-4-6-7h0c0-2-1-3-1-4h-2v-1c0-2-1-4-2-6z" class="I"></path><path d="M622 398c2 3 4 4 6 7 0 1 0 1 1 2-1 1-3 2-2 3l1 1v1h0-4c-1-3-1-5-2-8v-2-4z" class="O"></path><path d="M636 400h4v1 1c1 1 1 1 2 1v1c-2 2-4 5-7 6-2 1-4 1-6 2 1-1 1-3 1-4 1-1 5-3 6-4h-5v-1h3l1-1c-2 0-3 0-4-1v-1c2 0 4 1 6 1l-1-1z" class="g"></path><path d="M636 400h4v1 1c1 1 1 1 2 1v1c-2 2-4 5-7 6v-1l3-3v-1s-1 0-1-1l1-1 1-1h-1-2-1c-2 0-3 0-4-1v-1c2 0 4 1 6 1l-1-1z" class="S"></path><defs><linearGradient id="AP" x1="516.268" y1="479.511" x2="537.389" y2="484.249" xlink:href="#B"><stop offset="0" stop-color="#1e1e1d"></stop><stop offset="1" stop-color="#454445"></stop></linearGradient></defs><path fill="url(#AP)" d="M524 462c-1-5-2-9-1-14h0c4 7 5 14 6 23h1v1 3l1 1h0c-1 2 0 3 0 4l1-2v4 4l1 1c1 10 1 18-2 28-1 3-2 7-3 9v1-9c1-4 3-8 2-12 0-1 0-5-1-6v-1c-2-3-1-7-1-11l-1-3c-2-7-2-15-3-21z"></path><path d="M528 486c1 4 2 7 1 11-2-3-1-7-1-11zm2-11l1 1h0c-1 2 0 3 0 4l1-2v4 4-1c0-1-1-2-1-2-1-2-2-6-1-8z" class="G"></path><path d="M407 406c-1-4-3-10-2-14h1l-1-1-1 1h0c2-3 4-6 6-8 0-1 2-3 3-4 0-1 2-2 2-4h1v-2h0c0-2 1-8 1-10-1-1-1-1-1-2h0v-1-8l1-1c1 1 0 3 1 5-1-2-1-4 0-5v5l1 1v4c0 5 0 11-2 16-1 2-3 4-4 6 0 0 0 1-1 2h0-1c0 4 0 8 1 12v5h0l2 14v2c1 1 0 4 0 6v1c-1 0-1 0-1 1-1-3-2-5-3-8-1-4-3-9-3-13z" class="E"></path><path d="M407 406c0-2 1-4 1-6 0-3 0-8 2-10v16c1 5 1 8 3 12l1 1c1 1 0 4 0 6v1c-1 0-1 0-1 1-1-3-2-5-3-8-1-4-3-9-3-13z" class="I"></path><defs><linearGradient id="AQ" x1="440.171" y1="334.588" x2="450.754" y2="348.469" xlink:href="#B"><stop offset="0" stop-color="#4c4d4a"></stop><stop offset="1" stop-color="#807e81"></stop></linearGradient></defs><path fill="url(#AQ)" d="M447 322l1 2-1 2 1 1 1-1c0 1 0 3-1 5 0 1 0 2-1 3l2-1s1-1 1-2 0-1 1-1v2l-2 6h0-1c-1 2-2 5-2 7v8h-1v-5c-1 4-1 9 0 13v5h0-1v2c-1-1-1-1-1-2-1 1 0 3-1 5l-1-1v-12-2l-1 1-2-1h0 0v1c-1-4-2-9-1-13 0-1 0-1 1-1 1-2 2-3 2-5 0-5 5-12 7-16z"></path><path d="M442 346h1c-1 4-1 9-1 14v1l-1-3v-2c1-2 0-6 0-9v-1h1z" class="c"></path><path d="M447 334l2-1s1-1 1-2 0-1 1-1v2l-2 6h0-1c-1 2-2 5-2 7v8h-1v-5c0-5 0-10 2-14z" class="O"></path><path d="M442 360c1-2 1-4 1-6 1 2 1 5 2 7v5h0-1v2c-1-1-1-1-1-2-1 1 0 3-1 5l-1-1v-12l1 3v-1z" class="R"></path><defs><linearGradient id="AR" x1="438.23" y1="334.18" x2="441.77" y2="353.82" xlink:href="#B"><stop offset="0" stop-color="#4e4e4d"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#AR)" d="M447 322l1 2-1 2 1 1c-2 5-4 9-6 13l1 2h-1 0c-1 1-1 2 0 4h-1v1c0 3 1 7 0 9l-1 1-2-1h0 0v1c-1-4-2-9-1-13 0-1 0-1 1-1 1-2 2-3 2-5 0-5 5-12 7-16z"></path><path d="M447 326l1 1c-2 5-4 9-6 13h0c0-4 3-11 5-14z" class="G"></path><path d="M567 605v-1c1-1 2-2 2-3l7 4 2 2c1 2 1 2 1 4v1c-2 3-2 6-2 10 0 2 1 3-1 4 0-1-1-2-1-3l-1 1c1 3 1 5 1 8 0 2 0 5-1 7-1-1-1-3-2-3v1c-1 0-1 0-2-1h-1 0-1v-3c0-1 1-1 1-2l-1-1c1-5 2-9 1-13 0-2 0-4-1-5l-1 2c0-3-1-5-1-8l1-1h0z" class="V"></path><path d="M569 636c1-2 1-3 2-4h0v1h1s0-1 1-2h0 0l1 1h1c0 2 0 5-1 7-1-1-1-3-2-3v1c-1 0-1 0-2-1h-1z" class="H"></path><path d="M578 607c1 2 1 2 1 4v1c-2 3-2 6-2 10 0 2 1 3-1 4 0-1-1-2-1-3l-1 1s0-1-1-1h0-1c0-1 0-4-1-5l-1 1v-2-4h1 2l2 2c0-2 1-3 1-4 1-1 2-2 2-4z" class="g"></path><path d="M567 605v-1c1-1 2-2 2-3l7 4 2 2c0 2-1 3-2 4 0 1-2 1-2 1-1 0-2-1-2-1h-2l-1-1c0-1 0-2-1-3s-1-1-1-2z" class="a"></path><defs><linearGradient id="AS" x1="708.988" y1="236.865" x2="713.661" y2="225.023" xlink:href="#B"><stop offset="0" stop-color="#3d3c3d"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#AS)" d="M687 220c6 1 13 2 19 1l3 1c8 4 15 8 20 16l1 1-2-1-3 3-2 2-1 1c-1-3-3-5-5-7-7-7-17-11-26-14v-1c-1 0-3-1-4-2z"></path><path d="M577 180h1l2 1v1h4 0l1 2h0c0 1 1 1 1 2h0-1c0 1 0 2 1 2 1 1 1 1 1 2 1 2 2 2 2 4h0-2v1h0l-2-1c-1-1-2-2-4-2l3 3c1 1 3 3 5 4l4 4-1 1-11-9c-4-2-10-6-14-7-2 0-5 1-7 0s-4 0-6 0h0-1c1-2 3-3 5-3 1-1 2-2 4-2 4-2 10-1 15-3h0z" class="J"></path><path d="M570 187v-2h-3c-1 1-2 1-4 1h-2l1-1h3l1-1h1c3-1 7-1 10-1 2 0 3 1 5 2l-1 1c-1-1-1-2-3-1 1 1 3 2 4 3l-1 1-3-1c0-1 0-1-1-1-2-1-3-1-5-1v2l-2-1z" class="M"></path><path d="M578 188l3 1 1-1c-1-1-3-2-4-3 2-1 2 0 3 1l2 1h1l-2-2h1l2 1c0 1 0 2 1 2 1 1 1 1 1 2 1 2 2 2 2 4h0-2v1h0l-2-1c-1-1-2-2-4-2l3 3-2-1c-3-1-7-3-10-5-1-1-2-1-3-2h1l2 1v-2c2 0 3 0 5 1 1 0 1 0 1 1z" class="b"></path><path d="M586 188c1 1 1 1 1 2 1 2 2 2 2 4h0-2c-1 0-3-2-3-3v-1c0-1-1-1 0-2h2z" class="l"></path><path d="M572 188v-2c2 0 3 0 5 1 1 0 1 0 1 1v1l2 2h0l-8-3z" class="J"></path><defs><linearGradient id="AT" x1="559.528" y1="447.616" x2="547.472" y2="451.884" xlink:href="#B"><stop offset="0" stop-color="#242528"></stop><stop offset="1" stop-color="#63605d"></stop></linearGradient></defs><path fill="url(#AT)" d="M551 425l1-4c1 0 1 0 1 1v3 6-1c1-1 0-1 1-2v-1c0-2 0-3 1-5v-1c0 5 1 8 1 12 1 3 0 5 0 8 1 3 0 10-1 13v4 1l1-1c1 2 1 4 1 6 0-1 1-1 1-3v3c1 0 1 0 1-1h2c-2 2-3 3-2 6h0c-1 2-3 4-4 6v-4l-1-3v8l-1-1c1-3-1-4-2-7h0l-1-2v-2c0-1 0-3-1-4 0-1-1-2-1-3 2-3 2-8 2-10h-1c-1-2-1-4 0-6v1l1-1c0-3 1-6 0-9l1-3v-3-1z"></path><path d="M550 464l1 1c0 1 1 1 1 3h-1l-1-2v-2z" class="J"></path><path d="M555 440l1-7v1 7c1 3 0 10-1 13v4l-1 1c0-3 0-5 1-7v-1c-1-2 0-5 0-8v1-1-3z" class="M"></path><path d="M550 447c2 2 0 7 2 8 0 3-1 6-1 10l-1-1c0-1 0-3-1-4 0-1-1-2-1-3 2-3 2-8 2-10z" class="W"></path><path d="M555 459l1-1c1 2 1 4 1 6 0-1 1-1 1-3v3c1 0 1 0 1-1h2c-2 2-3 3-2 6h0c-1 2-3 4-4 6v-4-9-3z" class="J"></path><path d="M555 462c2 3 2 5 2 7l-2 2v-9z" class="Y"></path><path d="M551 425l1-4c1 0 1 0 1 1v3 6-1c1-1 0-1 1-2v-1c0-2 0-3 1-5v-1c0 5 1 8 1 12 1 3 0 5 0 8v-7-1l-1 7v-11l-2 9h-1c0-3 0-7-1-9v-3-1z" class="E"></path><path d="M551 429c1 2 1 6 1 9h1v5l-1 12c-2-1 0-6-2-8h-1c-1-2-1-4 0-6v1l1-1c0-3 1-6 0-9l1-3z" class="B"></path><path d="M550 447v-4s0 1 1 1v-3c0 1 1 2 1 3h0l1-1-1 12c-2-1 0-6-2-8z" class="G"></path><path d="M437 482l-3-9c-1-1-1-2-1-3l1 1h1l1 1h1c3 5 6 11 8 17h0c0-2-1-4-1-7 0 1 2 6 2 6l9 40c0 1-1 6 0 7v2c0 1 0 2-1 3-1 2 0 3 0 4v1h-1c0-9-1-16-3-24 0-5-3-14-6-17-1-4-3-6-4-10-2-3-2-8-3-12z" class="M"></path><path d="M437 482c4 6 5 12 8 19h-1c-1-2-1-3-2-5 0-1-1-1-1-2h-1c-2-3-2-8-3-12z" class="B"></path><path d="M414 389c-1-2 3-8 4-10s2-4 2-6h2c-1 4-1 7-3 11l-1 10h1c1 4-1 8 0 12 0 1 0 1-1 2v2l1 1 1-1v1c0 1 1 3 0 4 0 3-1 5-2 8 0 1-1 3-1 4v1 5c-1 1-2 1-3 2l-2 1v-2c-1-1-1-2-1-3l1-1v2h1c1-1 0-2 0-3v-2c0-1 0-1 1-1v-1c0-2 1-5 0-6v-2l-2-14h0v-5c-1-4-1-8-1-12h1v5c1 0 1-1 2-2z" class="M"></path><path d="M419 411l1-1v1c0 1 1 3 0 4l-1-1c-2 4-3 7-4 11v-3l1-1v-3h1c0-3 2-4 2-7z" class="W"></path><path d="M412 403l1-1 1 6c1 0 1-1 1-2v-4c1 5 1 11-1 15l-2-14z" class="B"></path><path d="M418 385v-2l1 1-1 10h1c1 4-1 8 0 12 0 1 0 1-1 2v-2c-1-3 0-7-1-10v-2c0-1 1-2 0-3h0c1-1 1-4 1-6zm-6 18h0v-5c-1-4-1-8-1-12h1v5c1 0 1-1 2-2 2 3-3 10 0 12h0c0-1 0-2 1-4 0 2 1 4 0 5v4c0 1 0 2-1 2l-1-6-1 1z" class="I"></path><path d="M415 425c1-4 2-7 4-11l1 1c0 3-1 5-2 8 0 1-1 3-1 4v1 5c-1 1-2 1-3 2v-1c0-3 0-6 1-9z" class="O"></path><path d="M414 389c-1-2 3-8 4-10s2-4 2-6h2c-1 4-1 7-3 11l-1-1v2 1c-1 4-3 7-3 11-1 2-1 3-1 4h0c-3-2 2-9 0-12z" class="E"></path><path d="M710 221c5 2 9 5 14 9 1 1 3 3 5 4 5 8 8 15 11 23 1 7 2 13 0 19 0-1-1-2-1-3-1 12-5 24-10 35h0c0-3 2-8 3-11 2-4 4-9 4-13 2-7 3-15 2-22 0-2 0-4-1-6-3 2-6 3-8 5l-2-6c-1-4-3-8-5-11l1-1 2-2 3-3 2 1-1-1c-5-8-12-12-20-16l1-1z" class="c"></path><path d="M725 241l3-3 2 1-1-1 4 6c-1 2-1 3-1 4l-2 2h1l-2 3-1-1-1 3c-1-4-3-8-5-11l1-1 2-2z" class="J"></path><path d="M725 241l3-3 2 1c0 2 0 3-1 4l-3 3-2-2-1-1 2-2z" class="R"></path><path d="M725 241l2 1-1 1-2 1-1-1 2-2z" class="l"></path><path d="M710 221c5 2 9 5 14 9 1 1 3 3 5 4 5 8 8 15 11 23 1 7 2 13 0 19 0-1-1-2-1-3a49.23 49.23 0 0 0-6-29l-4-6c-5-8-12-12-20-16l1-1z" class="C"></path><path d="M372 731c-1-3-2-10 0-12 0 2 1 4 2 5h1v-5c1 1 2 3 3 4l2 11c1 2 1 4 2 6l1 2c0 1 0 2 1 2l-1 1c0 2 1 3 2 5 0 3 1 8 3 10l1 4c1 1 1 3 2 5 0 0 0 1-1 1v-1c-1-1-1-2-2-3 0 0-1 0-1-1v-1h-2c-1-1-2-2-3-4v1l-3-3-1-1h-2c2-4-2-15-4-20v-6z" class="G"></path><path d="M385 764c2-3-1-10-2-12 0-2-1-5 0-7 0 2 1 3 2 5 0 3 1 8 3 10l1 4c1 1 1 3 2 5 0 0 0 1-1 1v-1c-1-1-1-2-2-3 0 0-1 0-1-1v-1h-2z" class="E"></path><path d="M378 752c1-2-1-6-1-7v-1c0-1 0-1 1-2-1-1-1-2-1-3 2 5 3 11 5 16 1 1 1 2 1 4l-1 1v1l-3-3-1-1v-3-2z" class="T"></path><path d="M378 752l4 8v1l-3-3-1-1v-3-2z" class="B"></path><path d="M372 731v-1-4l1-1h1 0c1 4 2 7 3 10 0 1 1 3 0 4 0 1 0 2 1 3-1 1-1 1-1 2v1c0 1 2 5 1 7v2 3h-2c2-4-2-15-4-20v-6z" class="M"></path><path d="M527 607c1 0 2 0 3 1h1 4l2 3h1l-1 4-1 6v1 6c1 3-1 11-3 14l-1-6c0-1-1-2-2-3h-2l-1-1c-1 1-1 2-2 2v1c-1 1-2 2-3 4v-1c-1-2-2-7-2-9 1-1 1-1 1-2 1-3 3-6 3-9 0-1 0-1 1-2h0s1-2 1-3h1c0-1 1-2 2-3l-2-3z" class="W"></path><path d="M528 631h2c1-2 1-2 1-4 0-1 1-2 1-3 0-2 1-3 1-4 1 2 1 4 1 6-1 1 0 1 0 2 0 2 0 7-2 8 0-1-1-2-2-3h-2v-2z" class="H"></path><path d="M529 621c1-1 1-3 2-5h2l1 1v3h-1c0 1-1 2-1 4 0 1-1 2-1 3 0 2 0 2-1 4h-2v-4l1-6z" class="D"></path><path d="M527 607c1 0 2 0 3 1h1l-1 1 1 2 1-1h3c1 2 1 3 0 5 0 1-1 3-1 5v-3l-1-1h-2c-1 2-1 4-2 5h-1l-1 5v2l-1-1v-1c0-2 0-3 1-4 0-3-1-4 1-6 0-1 0-2-2-3h1c0-1 1-2 2-3l-2-3z" class="h"></path><path d="M527 607c1 0 2 0 3 1h1l-1 1 1 2 1-1h3c1 2 1 3 0 5l-2-3h-1v1c-1 0-1 0-1-1-1 1-1 1-1 2-1 1-1 2-2 2 0-1 0-2-2-3h1c0-1 1-2 2-3l-2-3z" class="f"></path><path d="M526 613c2 1 2 2 2 3-2 2-1 3-1 6-1 1-1 2-1 4v1l1 1v-2l1-5h1l-1 6v4 2l-1-1c-1 1-1 2-2 2v1c-1 1-2 2-3 4v-1c-1-2-2-7-2-9 1-1 1-1 1-2 1-3 3-6 3-9 0-1 0-1 1-2h0s1-2 1-3z" class="S"></path><path d="M520 629c1-1 1-1 1-2 1-3 3-6 3-9 0-1 0-1 1-2h0c-1 6-3 13-1 18h1v1c-1 1-2 2-3 4v-1c-1-2-2-7-2-9z" class="B"></path><path d="M503 451l1-3v-7c1 2 1 5 3 6v1c0 1 1 1 0 3 0 2 1 5 1 8v8l-1 10c-2-3 1-7-2-9l-1 7-1 2v1c0-1-1-2-1-3v-1l-2 5-3 18c0-2 0-3-1-5v-3h-1 0c-1 1-1 1-1 2v1c-1-1-1-2-2-4 0-5 1-10 2-15h-1v-2c3-5 6-9 8-15h1l1-5z" class="j"></path><path d="M502 474c-1-2-1-5 0-7 2 2 0 6 2 8l-1 2v1c0-1-1-2-1-3v-1zm-7 15c0-4 0-8 1-11 0-7 2-14 6-19l-5 21v1 5l-1 3h-1z" class="G"></path><defs><linearGradient id="AU" x1="495.563" y1="467.403" x2="493.174" y2="490.466" xlink:href="#B"><stop offset="0" stop-color="#494848"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#AU)" d="M502 456v1 2c-4 5-6 12-6 19-1 3-1 7-1 11h0c-1 1-1 1-1 2v1c-1-1-1-2-2-4 0-5 1-10 2-15h-1v-2c3-5 6-9 8-15h1z"></path><defs><linearGradient id="AV" x1="385.06" y1="696.779" x2="400.94" y2="696.727" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#555454"></stop></linearGradient></defs><path fill="url(#AV)" d="M384 678l3-1c2 1 3 1 5 3 0 1 1 1 2 1 3 7 6 14 8 21 1 2 2 7 3 8 0 4 1 8 2 11 1 2 0 4 0 6v-1c0-1-1-1-1-2h0c0-2-1-2-1-3h-1l-1-2-1 1c0 3 4 6 3 10-2-5-4-11-6-16l-15-36z"></path><path d="M524 367c1 0 1 0 1 1l1 1v-1l2 4v1h1c0 1 1 2 2 3 0 2 1 3 2 4 0 1 1 1 1 2h1c2 3 2 4 2 8 0 1 1 2 1 4v5l1 7v7c-1 0-1 1-2 1l-1-3-2-5h-1c-1-2 0-3-2-4v1c-1-1-1-2-1-4h-1l-1 3c0-1 0-3-1-4-1 0-1-2-1-3h0l-2 2 1-7c-1-2-1-5-1-7l1-5c-1-2 0-5 0-7l-1-4z" class="W"></path><path d="M528 373h1c0 1 1 2 2 3 0 2 1 3 2 4h-2c0-1-1-2-1-3h-1c0 1-1 4 0 5v1h0c-1-2-1-4-2-6h0c0-2 0-2 1-4z" class="M"></path><path d="M524 367c1 0 1 0 1 1l1 1v-1l2 4v1c-1 2-1 2-1 4v7h0c-1-2-1-4-1-6h-1c-1-2 0-5 0-7l-1-4z" class="B"></path><path d="M525 390c1 0 0 0 1 1 1 2 1 4 2 7l1-11c1 4 2 8 2 12h0v3 1c-1-1-1-2-1-4h-1l-1 3c0-1 0-3-1-4-1 0-1-2-1-3h0l-2 2 1-7z" class="e"></path><defs><linearGradient id="AW" x1="531.747" y1="386.295" x2="534.253" y2="401.205" xlink:href="#B"><stop offset="0" stop-color="#5f5d5e"></stop><stop offset="1" stop-color="#7b7c7a"></stop></linearGradient></defs><path fill="url(#AW)" d="M531 399c1-6-1-11-1-16h1c0 2 1 5 2 7 0-3-1-7-2-10 2 2 3 5 4 7l1 1v-1c0-2-1-3-2-5h1c2 3 2 4 2 8 0 1 1 2 1 4v5l1 7v7c-1 0-1 1-2 1l-1-3-2-5h-1c-1-2 0-3-2-4v-3z"></path><path d="M538 403c0-1-1-3 0-4l1 7v7c-1 0-1 1-2 1l-1-3-2-5c0-2 0-3 1-5v-2c1 1 2 6 2 7 1-1 1-2 1-3z" class="d"></path><path d="M538 403c0-1-1-3 0-4l1 7v7c-1 0-1 1-2 1l-1-3c0-1 0-1 1-1 1-3 1-4 1-7z" class="V"></path><path d="M552 830h1l-1 2c2-2 4-2 5-5v-1c0-1 0 0 1-1 0-2 0-2 1-4h0c0-1 0 0 1-1v-2l1-1c1-3 1-5 3-7l-43 107-3-2 34-85z" class="P"></path><path d="M474 440c2-3 0-6 0-8h1c1 3 2 8 3 11v1c1-1 1-1 1-2l-2-10 1-1c1 2 1 5 3 7 0 3 3 6 3 9v1 1l1 1v2c1 2 1 7 1 10h1c1-1 1-2 1-3h1c0 1 1 2 1 3l-1 2h0c-1 4-2 7-5 10l-4 4-3-12h0l-1-8v-3h-1c0-2 0-4 1-6l-1-2c0-3-1-5-1-7z" class="I"></path><path d="M482 459v-12c1 2 1 5 1 8l1 6v2l1-1v2l2-1 1 1v-1l1 1h0c-1 1-1 2-2 3l-2-2-1-1c-1 0-1-1-1-2h-1v-3z" class="O"></path><path d="M482 462h1c0 1 0 2 1 2l1 1 2 2c1-1 1-2 2-3-1 4-2 7-5 10v-2-5h-1 0c-1-2-1-3-1-5z" class="e"></path><path d="M484 449l1 1v2c1 2 1 7 1 10h1c1-1 1-2 1-3h1c0 1 1 2 1 3l-1 2-1-1v1l-1-1-2 1v-2l-1 1v-2l-1-6 2-1c0-2 0-3-1-5z" class="W"></path><path d="M485 454c0 2 0 5-1 6v1h0l-1-6 2-1z" class="G"></path><path d="M476 449v3h1v-6h1l1 13h1v-7-2l2 9v3c0 2 0 3 1 5h0 1v5 2l-4 4-3-12h0l-1-8v-3h-1c0-2 0-4 1-6z" class="W"></path><path d="M476 455l3 7v2c1-1 1-3 1-4h1v1c0 1 1 5 1 6-1 1-1 0-1 1 0 2-1 3-1 4-1-2-1-5-2-7l-1 1h0l-1-8v-3z" class="c"></path><path d="M477 466l1-1c1 2 1 5 2 7 0-1 1-2 1-4 0-1 0 0 1-1v4h1v-4h1v5 2l-4 4-3-12z" class="R"></path><defs><linearGradient id="AX" x1="562.598" y1="435.227" x2="550.997" y2="449.539" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#4e4d4d"></stop></linearGradient></defs><path fill="url(#AX)" d="M555 421c0-1 0-2 1-3 1 2 1 4 0 6h1v-3c1-1 1-2 1-2l1-1c0 1 1 2 1 3l2 2 1 3v-1-2h-1c0-2 0-3-1-4 2 4 5 9 4 13h0-1c1 4 2 9 2 13s-1 8-1 12v2l-1 2-2 1-1 1h-2c0 1 0 1-1 1v-3c0 2-1 2-1 3 0-2 0-4-1-6l-1 1v-1-4c1-3 2-10 1-13 0-3 1-5 0-8 0-4-1-7-1-12z"></path><path d="M560 436c1 2 2 5 2 7-1 1-1 2-2 3h0 0c-1 3-1 7-2 10v-1c0-2-1-7 0-9l1-1c1-1 1-3 1-5v-4z" class="P"></path><path d="M556 424h1v-3c1-1 1-2 1-2l1-1c0 1 1 2 1 3l2 2 1 3v-1-2h-1c0-2 0-3-1-4 2 4 5 9 4 13h0-1v-1c-1-1-1-2-2-3 0-1-1-1-2-2l1 3c-1 2-1 5-1 7v4h-1c0-3 1-11 0-13h-1v4h-1v-2c-1-2-1-3-1-5z" class="j"></path><path d="M560 426v-2h1c1 1 1 2 2 3s1 2 1 4c-1-1-1-2-2-3 0-1-1-1-2-2z" class="G"></path><defs><linearGradient id="AY" x1="558.982" y1="427.023" x2="566.664" y2="456.68" xlink:href="#B"><stop offset="0" stop-color="#2d2c2c"></stop><stop offset="1" stop-color="#616060"></stop></linearGradient></defs><path fill="url(#AY)" d="M560 426c1 1 2 1 2 2 1 1 1 2 2 3v1c1 4 2 9 2 13s-1 8-1 12v2l-1 2-2 1-1 1h-2c0 1 0 1-1 1v-3-5c1-3 1-7 2-10h0 0c1-1 1-2 2-3 0-2-1-5-2-7 0-2 0-5 1-7l-1-3z"></path><path d="M561 429c2 5 1 11 2 17 0 1 0 1-1 2v-5c0-2-1-5-2-7 0-2 0-5 1-7z" class="G"></path><path d="M562 443v5c1-1 1-1 1-2 1 4 0 12-1 16l-1 1h-2c0 1 0 1-1 1v-3-5c1-3 1-7 2-10h0 0c1-1 1-2 2-3z" class="k"></path><defs><linearGradient id="AZ" x1="562.955" y1="450.25" x2="555.277" y2="458.501" xlink:href="#B"><stop offset="0" stop-color="#484a46"></stop><stop offset="1" stop-color="#6c696c"></stop></linearGradient></defs><path fill="url(#AZ)" d="M558 456c1-3 1-7 2-10h0s1 1 1 2c1 2 0 7-1 10v4l-1 1h0c0 1 0 1-1 1v-3-5z"></path><path d="M598 368c-1-3 0-6 1-9s3-5 4-8l3-3c0-2 1-4 1-5v-2c2 8 0 17 2 24 0 2 1 4 1 6l1 3 4 9 1 2c-1 0-2 1-3 1h-1l1 2-1 1c-1-1-1-2-2-2l-1-1c1 1 1 3 1 4l2 6v2c-1-1-2-3-3-5 0-3-3-5-4-8l-3-9c-1-2-1-4-2-6-1 0-1 0-1 1-1-1-1-2-1-3z" class="I"></path><path d="M605 385c1-1 1-2 1-2v-1h0v-1c-1-1 0-2 0-3h1l1 1v-1c0-1-1-2-1-3h0l-1-1v-2l1-1c-1-2-2-5-2-7l1-1c0 3 1 5 3 7l1 1 1 3 4 9 1 2c-1 0-2 1-3 1h-1l1 2-1 1c-1-1-1-2-2-2l-1-1c1 1 1 3 1 4l2 6v2c-1-1-2-3-3-5 0-3-3-5-4-8z" class="M"></path><path d="M609 386c-1-1-1-2-1-3-1-2-1-2-1-4l3 6 1-1h1v2l1 2-1 1c-1-1-1-2-2-2l-1-1z" class="P"></path><path d="M611 374l4 9 1 2c-1 0-2 1-3 1h-1v-2h-1l-3-9h0l3 6h1c1-3-2-5-1-7z" class="W"></path><path d="M612 384v-1l1 1 2-1 1 2c-1 0-2 1-3 1h-1v-2z" class="K"></path><defs><linearGradient id="Aa" x1="230.776" y1="270.389" x2="190.01" y2="260.578" xlink:href="#B"><stop offset="0" stop-color="#262525"></stop><stop offset="1" stop-color="#6a6a6b"></stop></linearGradient></defs><path fill="url(#Aa)" d="M197 249l1-1c-1-1-3-2-4-3 1-1 1-1 2-1 2 1 4 1 7 2 1 3 3 3 6 5 7 9 14 20 18 31 0 1 0 1 1 2h-1l-1 1h1l-1 1-1 1h1-2c-1 1-1 2-2 3-1-1-2-5-3-7-3-5-5-11-9-16v-1l-13-17z"></path><defs><linearGradient id="Ab" x1="330.909" y1="599.018" x2="311.423" y2="604.83" xlink:href="#B"><stop offset="0" stop-color="#232322"></stop><stop offset="1" stop-color="#4f4e4e"></stop></linearGradient></defs><path fill="url(#Ab)" d="M315 623v-7c0-7-2-14-1-21h0c1-11 7-22 13-30h1c-1 3-3 5-3 7-3 7-4 14-6 21l1 1v1c1 3 0 6 1 9 0 8 1 16 3 24v4h0v5h0l-2-3h-1l1 5c-1-1-2-2-3-4l-2-2h0c-2-4-3-7-2-10z"></path><path d="M315 623v2c2-2 1-8 2-10l2 13 1 4c1 1 1 1 1 2l1 5c-1-1-2-2-3-4l-2-2h0c-2-4-3-7-2-10z" class="X"></path><path d="M319 628l1 4c1 1 1 1 1 2l1 5c-1-1-2-2-3-4v-7zm0-35l1 1v1c1 3 0 6 1 9 0 8 1 16 3 24v4h0v5h0l-2-3c-1-2-1-5-1-7l-1-14c0-7-2-14-1-20z" class="I"></path><path d="M585 236l3 2c2 1 4 2 7 4h1 1c1 1 3 1 4 1l2-1c2 0 4-1 6 0h0c2 1 1 0 2 2l-1 1 1 1v1h-1l-1 1h1 0l1 1c0 1-1 2-1 4-2 0-3 0-5 1l-4-1h-3c-4 1-7 1-11 0l-6-3h0l-3-2v-2c1 0 1 0 1-1h1s1 0 2 1h0c0-1-1-2-2-2l-2-5-1-1 1 1 1-1 1 1c0-1 0-1 1-2 1 1 1 0 2 0l2-1z" class="L"></path><path d="M609 242c2 1 1 0 2 2l-1 1h-1c-2 1-3 1-5 1v-1h1c2-1 2-1 4-3h0z" class="C"></path><path d="M603 242c2 0 4-1 6 0-2 2-2 2-4 3l-4-2 2-1z" class="I"></path><path d="M610 248h0l1 1c0 1-1 2-1 4-2 0-3 0-5 1l-4-1c3-2 6-2 9-5z" class="X"></path><path d="M601 245l1 1c0 2-1 3-2 4-2 1-5 0-7 0 1 1 2 1 2 3-1-2-3-3-5-4h-1c-1-1-2-2-2-4 2 2 7 5 10 4 1 0 3-2 4-4z" class="H"></path><path d="M586 248c0-2-1-2-1-5l2 2h0c0 2 1 3 2 4h1c2 1 4 2 5 4h3c-4 1-7 1-11 0l-6-3h0l-3-2v-2c1 0 1 0 1-1h1s1 0 2 1h0c2 0 3 1 4 2z" class="h"></path><path d="M586 248c0-2-1-2-1-5l2 2h0c0 2 1 3 2 4h1v3c-1 0-2 0-3-1 0-2 0-1-1-3z" class="d"></path><path d="M578 248v-2c1 0 1 0 1-1h1s1 0 2 1h0c2 0 3 1 4 2 1 2 1 1 1 3l-2-1c-2-1-2-1-4 0h0l-3-2z" class="R"></path><defs><linearGradient id="Ac" x1="584.362" y1="235.293" x2="594.149" y2="250.2" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#a3a3a2"></stop></linearGradient></defs><path fill="url(#Ac)" d="M585 236l3 2c2 1 4 2 7 4h1l2 2c1 0 2 0 3 1-1 2-3 4-4 4-3 1-8-2-10-4h0l-2-2c0 3 1 3 1 5-1-1-2-2-4-2 0-1-1-2-2-2l-2-5-1-1 1 1 1-1 1 1c0-1 0-1 1-2 1 1 1 0 2 0l2-1z"></path><path d="M578 239l-1-1 1 1c2 2 5 3 7 4 0 3 1 3 1 5-1-1-2-2-4-2 0-1-1-2-2-2l-2-5z" class="S"></path><path d="M588 238c2 1 4 2 7 4h1l2 2-1 1h-1-2c-2 0-5-3-6-4v-1h1v-1l-1-1h0z" class="g"></path><defs><linearGradient id="Ad" x1="712.253" y1="294.099" x2="745.05" y2="275.948" xlink:href="#B"><stop offset="0" stop-color="#8f8d8c"></stop><stop offset="1" stop-color="#c7c7c7"></stop></linearGradient></defs><path fill="url(#Ad)" d="M729 261c2-2 5-3 8-5 1 2 1 4 1 6 1 7 0 15-2 22 0 4-2 9-4 13h0c-1 2-2 2-2 4 0 1-1 2-1 4-3 2-5 3-8 5l-1-1c6-15 12-31 9-48z"></path><defs><linearGradient id="Ae" x1="483.674" y1="327.404" x2="465.452" y2="342.212" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#373636"></stop></linearGradient></defs><path fill="url(#Ae)" d="M473 314l1-1 3 3h0l2 2c0 1 0 2 1 3h0v1 1 1h0l-1 1c-3 2-3 12-3 16v2c1-1 1-4 2-5v-1c1 1 1 1 1 2-1 3 0 7 0 11-1 1-1 2-1 4h0c-1 0-1 0-2-1-1 2 0 3-1 5h-1c-1 3-1 6-1 9h-1c0 1 0 3-1 4-1-3-3-5-3-8 1-2 1-8 1-10-1-2 0-3 0-5 0-3 1-6 1-9 0 0 1-2 1-3l-1-1c0-1 1-3 1-4h-1v-1h-1c0-1 0-2 1-2 0-1 1-2 1-3-1 0-1 1-2 1h-1l2-5h1v-3c1-1 2-3 2-4z"></path><path d="M470 330l1-1c1-2 1-4 3-6v5h-1c-1 1-1 2-2 3h-1v-1z" class="I"></path><defs><linearGradient id="Af" x1="467.989" y1="350.33" x2="470.888" y2="368.018" xlink:href="#B"><stop offset="0" stop-color="#403e3f"></stop><stop offset="1" stop-color="#5e5e5d"></stop></linearGradient></defs><path fill="url(#Af)" d="M469 348c0-1 1-1 1-2h2v2c0 1 0 2 1 3l-1 1c1 1 1 2 2 3l1-1v2h-1v2c-1 3-1 6-1 9h-1c0 1 0 3-1 4-1-3-3-5-3-8 1-2 1-8 1-10-1-2 0-3 0-5z"></path><path d="M469 348c0-1 1-1 1-2h2v2c0 1 0 2 1 3l-1 1c1 1 1 2 2 3l1-1v2h-1v2c-1 3-1 6-1 9h-1c0-2 1-5 0-7h0c-1-1-1-4-1-5v-5l-1-1-1 4c-1-2 0-3 0-5z" class="T"></path><defs><linearGradient id="Ag" x1="458.299" y1="414.215" x2="441.988" y2="425.693" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#Ag)" d="M448 401h0c1-2 1-3 2-4v-2h1v2c-1 2-1 3 0 5 1 3 0 6 1 9 1-1 1-2 1-4h1c0 9 2 17 2 25-1 2-1 2-1 4 1 0 1 0 2 1 0 5 1 12-1 18 0 2-1 5-2 7-1-1-1-2-2-3v-1l-1 1v1h-1v-4h-1c-1-3 0-8 0-11s-1-6-1-10c0-1 0-4 1-5 0 2 0 4 1 6v-8c0-1 0-3-1-4v1c-1-4-1-8-2-12 0-1-1-4-1-4-1-2-1-4-1-6h1c0 1 1 2 1 3v-1c0-1 1-1 1-2v-1-1z"></path><path d="M447 413c0-1-1-4-1-4-1-2-1-4-1-6h1c0 1 1 2 1 3l2 11v7 1c-1-4-1-8-2-12z" class="F"></path><defs><linearGradient id="Ah" x1="460.784" y1="441.711" x2="448.965" y2="452.56" xlink:href="#B"><stop offset="0" stop-color="#393a39"></stop><stop offset="1" stop-color="#6e6d6e"></stop></linearGradient></defs><path fill="url(#Ah)" d="M455 436c1 0 1 0 2 1 0 5 1 12-1 18 0 2-1 5-2 7-1-1-1-2-2-3v-1l-1 1v-19c1-2 0-3 1-4h0v18c2-2 0-6 0-9 1-2 0-6 2-7v-1l1-1z"></path><path d="M544 562c0 2-1 4-1 6h1l-1 2 1 2v1s0-1 1-1v3l-2 8v1 1 3h-1c0 1-1 2-1 3-1 3-5 6-6 9-1 1-2 2-3 2-1 2-2 3-2 6-1-1-2-1-3-1l2 3c-1 1-2 2-2 3h-1c0 1-1 3-1 3h0c-1 1-1 1-1 2 0 3-2 6-3 9 0 1 0 1-1 2 1-6 3-15 5-20v-2c-1 0 0 0-1 1-1-2 0-5 0-7 1-1 1-1 1-2s1-3 1-5c1-1 2-3 2-4h-1 0l2-3h0c2-1 2-2 3-3 1-2 2-3 2-4h1c1-1 2-5 3-7l1-2c2-3 3-6 5-9z" class="K"></path><path d="M530 592c3 0 3-4 6-5-1 2-1 3-2 5 0 2-1 3-3 4l-1-1-2 3-1-1 1-1v-1c0-1 1-2 2-3z" class="J"></path><path d="M544 562c0 2-1 4-1 6h1l-1 2c-1 3-3 6-4 10-1 2-1 5-3 7-3 1-3 5-6 5 0-1 1-2 2-4 0 0 0-1 1-2l2-6c1-1 2-5 3-7l1-2c2-3 3-6 5-9z" class="f"></path><path d="M544 562c0 2-1 4-1 6-3 5-5 10-7 15-1 2-1 3-3 3h0l2-6c1-1 2-5 3-7l1-2c2-3 3-6 5-9zm-1 8l1 2v1s0-1 1-1v3l-2 8v1 1 3h-1c0 1-1 2-1 3-1 3-5 6-6 9-1 1-2 2-3 2-1 2-2 3-2 6-1-1-2-1-3-1h0v-1c0-2 1-3 2-4l1-1c2-3 8-7 9-11 1-2 3-4 3-7-1 0-3 4-3 5-4 6-8 10-13 15v-1c1-2 3-4 5-6 2-1 3-2 3-4 1-2 1-3 2-5 2-2 2-5 3-7 1-4 3-7 4-10z" class="h"></path><defs><linearGradient id="Ai" x1="509.016" y1="483.619" x2="533.733" y2="494.283" xlink:href="#B"><stop offset="0" stop-color="#242424"></stop><stop offset="1" stop-color="#4c4b4b"></stop></linearGradient></defs><path fill="url(#Ai)" d="M519 470h1 0v-16c1 0 1 2 1 2v2c1 3 2 6 2 9v-2c1-1 1-2 1-3 1 6 1 14 3 21l1 3c0 4-1 8 1 11v1c1 1 1 5 1 6l-6 16h0-1-2c-2 1-2 3-2 4h-2c2-7 2-14 4-21v-12l-1-14c-1-1 0-4 0-5l-1-1v-1z"></path><path d="M521 491l1 8h0v-1h0v10h-1v-5-12z" class="M"></path><path d="M520 477c2 2 1 3 2 6 1 5 1 10 0 15h0v1h0l-1-8-1-14z" class="G"></path><defs><linearGradient id="Aj" x1="535.172" y1="476.16" x2="513.828" y2="495.84" xlink:href="#B"><stop offset="0" stop-color="#151513"></stop><stop offset="1" stop-color="#353536"></stop></linearGradient></defs><path fill="url(#Aj)" d="M524 462c1 6 1 14 3 21l1 3c0 4-1 8 1 11v1c0 5-2 10-4 14v-2c0-5-1-10-1-15 0-3 1-5 1-8-1-7-2-14-2-20v-2c1-1 1-2 1-3z"></path><path d="M624 724c-1-1-1-4-1-5 0-5-1-9-3-13-1 2-1 4-1 6-2-5-2-9-1-14 1-4 0-9 2-13v-3c1-1 1-2 1-2 0-1 1-1 1-2v-1l1 2h1c0 3 1 6 0 8l1 4h0c0-3-1-7 1-9 0 2 0 4 1 6v2c0 1 0 2 1 2v1l1 1c1 2 2 4 4 6v1c1 1 3 4 3 5l-2 1c0 2 0 4-1 6 1 1 1 2 1 3-1 1-1 2-1 2l-1-1v-1l-1 2v-1l-2-1-2 1c-2 3 1 5-3 7z" class="E"></path><path d="M625 698l1 2c-1 1-2 3-2 4 0 2 1 3 1 5v1l-2-1c-1 0-1 0-1-1-1-1-1-6-1-8h2v-1-1l1 1 1-1z" class="I"></path><path d="M625 698l1 2c-1 1-2 3-2 4 0 2 1 3 1 5-1-1-1-1-1-2-1-2-2-4-2-5v-1l1-1v-1-1l1 1 1-1z" class="T"></path><path d="M623 679h1c0 3 1 6 0 8l1 4 1 3-1 3v1l-1 1-1-1v1l-1-1h-1v-6c0-5 0-9 2-13z" class="B"></path><path d="M625 697c-2-3-2-7-2-10h1l1 4 1 3-1 3z" class="T"></path><path d="M625 691h0c0-3-1-7 1-9 0 2 0 4 1 6v2c0 1 0 2 1 2v1l1 1c1 2 2 4 4 6v1c1 1 3 4 3 5l-2 1c0 2 0 4-1 6 1 1 1 2 1 3-1 1-1 2-1 2l-1-1v-1l-1 2v-1l-2-1-2 1c-2 3 1 5-3 7v-4l1-1c0-1 1-3 3-4 0 0 0-1 1-1 0-2-1-2-2-4h-2v-1c0-2-1-3-1-5 0-1 1-3 2-4l-1-2v-1l1-3-1-3z" class="G"></path><path d="M626 694c0 1 1 3 1 5h1l-2 1-1-2v-1l1-3z" class="k"></path><path d="M629 702c2 3 3 5 3 8-1 0-2-2-3-3v-5z" class="d"></path><path d="M626 700l2-1c0 1 1 2 1 3h0v5c1 1 2 3 3 3v2h0l-1-2-1 1c-2-1-2-1-3-1h-2v-1c0-2-1-3-1-5 0-1 1-3 2-4z" class="e"></path><path d="M629 707c0 1-1 1-1 2l-1-1c0-2-1-5 0-7l2 1h0v5z" class="R"></path><path d="M608 509h2v6c1 0 1 0 2-1-1 3-1 5-2 7h1c1 2 0 4-1 6l1 1 1-1h0v-1c1-1 2-2 2-3l1-1v-3l1 1c0 4-3 9-5 13h-1c-1 0-1 1-2 2h0c-1-3 1-5 1-7v-1c-1 2-2 4-4 6-1 0-1 1-2 2 0 1-1 2-1 2h-1c1-3 1-4 2-6l-1-1-3 3h-1c0 2-1 3-2 5v2c-1 1-3 3-3 5v4 2h-1v-3c-1 0-1 0-1 1-1 1-1 1-1 2-1 1-2 2-2 4-1-1-1-3-1-4v-6c-1-2 0-4 0-6l-1-1c1-2 0-5 2-7h1c3-1 5-3 7-5 0-2 3-4 4-6 1-1 1-2 2-4 0 2 0 3-1 4l2 1 5-11v-1z" class="P"></path><defs><linearGradient id="Ak" x1="607.554" y1="526.575" x2="607.946" y2="515.925" xlink:href="#B"><stop offset="0" stop-color="#484747"></stop><stop offset="1" stop-color="#6a6969"></stop></linearGradient></defs><path fill="url(#Ak)" d="M608 509h2v6c1 0 1 0 2-1-1 3-1 5-2 7-1 4-3 6-6 9l2-8c1-1 2-5 2-7 1-1 1-4 0-5v-1z"></path><path d="M608 510c1 1 1 4 0 5 0 2-1 6-2 7h-1c-2 4-5 7-7 11 0 2-1 3-2 5-1 0-3 2-3 3s1 1 0 2h-1c0-4 1-7 3-10l1 1h0c1-3 4-5 4-8l3-5 5-11z" class="O"></path><path d="M602 516c0 2 0 3-1 4l2 1-3 5c0 3-3 5-4 8h0l-1-1c-2 3-3 6-3 10h1c1-1 0-1 0-2s2-3 3-3v2c-1 1-3 3-3 5v4 2h-1v-3c-1 0-1 0-1 1-1 1-1 1-1 2-1 1-2 2-2 4-1-1-1-3-1-4v-6c-1-2 0-4 0-6l-1-1c1-2 0-5 2-7h1c3-1 5-3 7-5 0-2 3-4 4-6 1-1 1-2 2-4z" class="k"></path><path d="M586 538c1-2 0-5 2-7h1c0 1-1 1-1 2 1 1 1 2 2 2l-1 2h1 2c-1 2-1 5-1 6l-1 1c-1 1-1 4-1 5h-1c0-3 1-8 0-10h-1l-1-1z" class="b"></path><path d="M602 516c0 2 0 3-1 4l2 1-3 5c0 3-3 5-4 8h0l-1-1 3-6c-3 4-5 6-6 10h-2-1l1-2c-1 0-1-1-2-2 0-1 1-1 1-2 3-1 5-3 7-5 0-2 3-4 4-6 1-1 1-2 2-4z" class="e"></path><path d="M600 526v-1-1c0-1 0-3 1-4l2 1-3 5z" class="Z"></path><path d="M596 526c0 3-4 7-6 9-1 0-1-1-2-2 0-1 1-1 1-2 3-1 5-3 7-5z" class="O"></path><path d="M431 473l1-2c-2-4-5-8-6-12 2 4 6 8 9 12h-1l-1-1c0 1 0 2 1 3l3 9c1 4 1 9 3 12 1 4 3 6 4 10 3 3 6 12 6 17h-1l1 2c0 4 1 8 0 11 0 1 1 1 1 1v1c-1 0-1 0-1 1h0l-1-2-4-12-12-29v-4c-1-3-2-7-3-10s-4-6-5-9c0-2-1-3-1-4-1-3-3-6-2-8 3 3 5 8 8 12l1 2z" class="X"></path><path d="M431 473l4 9 3 9c-4-4-6-11-8-16 0-1-1-3-2-4 1-1 1-1 2 0l1 2zm14 50c0-6-2-10-3-15-1-3-1-5-2-7s-2-4-2-6h1l4 8 1 1c3 3 6 12 6 17h-1l1 2c0 4 1 8 0 11 0 1 1 1 1 1v1c-1 0-1 0-1 1h0l-1-2-4-12z" class="I"></path><path d="M376 757h2l1 1 3 3v2c1 1 1 3 2 5s0 4 0 6l1 1 1 6c-1 1-2 1-3 2h-1c-1 0-1 1-1 2 0 2 0 7-1 9v3 2c1 1 1 2 1 4l1 1-1 1c-2 1-2 1-3 3l-1-1v-6-1c0-2 0-5-1-7l-1-3-1-3c-2-4-4-10-5-15 0 0 0-1-1-1 0 1 0 0-1 1h0v-4l1-1 1-1h0 1 3v-2-3l1-2c1 0 1-1 2-2z" class="T"></path><path d="M377 784c2 6 0 12 1 17v1l-1-1v-1c0-2 0-5-1-7v-2c0-3 0-4 1-7z" class="W"></path><path d="M379 783l2 2c0 2 0 7-1 9v3 2c1 1 1 2 1 4l1 1-1 1c-2-7-2-15-2-22z" class="c"></path><path d="M374 775c1 2 1 4 1 6h1v-4c1 3 1 5 1 7-1 3-1 4-1 7v2l-1-3-1-3v-10-2z" class="G"></path><path d="M373 768h1l1-1h1l1 1c1 0 1-1 2-1s0 0 1 1c0 1 1 2 1 3l-2 2-1-1v-1h-1c0 1-1 5-1 6v4h-1c0-2 0-4-1-6v-2h-1v2h0c-2-1-1-5-1-7h1z" class="E"></path><path d="M367 768l1-1 1-1h0 1 3v2h-1c0 2-1 6 1 7h0v-2h1v2 2 10c-2-4-4-10-5-15 0 0 0-1-1-1 0 1 0 0-1 1h0v-4z" class="X"></path><path d="M367 768h1c1 1 1 2 1 4 0 0 0-1-1-1 0 1 0 0-1 1h0v-4z" class="F"></path><path d="M380 768l2-1 1 1h1c1 2 0 4 0 6l1 1 1 6c-1 1-2 1-3 2h-1c-1 0-1 1-1 2l-2-2v-5l1-1c0-1 0-1-1-1h-1v-4l1 1 2-2c0-1-1-2-1-3z" class="T"></path><path d="M380 777h0l1 2 1-1v5c-1 0-1 1-1 2l-2-2v-5l1-1z" class="K"></path><path d="M380 768l2-1 1 1h1c1 2 0 4 0 6l1 1c-1 1-1 2-2 2s-1-1-1-2h-1c0 1 0 2-1 2h0c0-1 0-1-1-1h-1v-4l1 1 2-2c0-1-1-2-1-3z" class="B"></path><path d="M376 757h2l1 1 3 3v2c1 1 1 3 2 5h-1l-1-1-2 1c-1-1 0-1-1-1s-1 1-2 1l-1-1h-1l-1 1h-1v-2-2-3l1-2c1 0 1-1 2-2z" class="J"></path><path d="M377 763c-1 0-1 1-1 1v1l-1-1v-1c0-2 0-3 2-4v4z" class="Z"></path><path d="M379 758l3 3v2c-1 1-2 2-3 2h-2v-2-4l2-1z" class="g"></path><path d="M468 319c1 0 0 0 1-1h1c1-1 1-2 2-3v-1h1c0 1-1 3-2 4v3h-1l-2 5h1c1 0 1-1 2-1 0 1-1 2-1 3-1 0-1 1-1 2-1 5-6 10-8 15-3 4-4 9-6 13h-1c0-2 2-6 2-8-1 1-1 3-2 4-1 0-1-1-1-1v-1c-2 1-1 3-2 4v-4c-1-1-1 0-1-1 1-2 0-4 0-5h-1c0 2 0 5-1 6h-1c0-3 0-8 1-11l1-3h0l2-6 1-1c1-2 2-4 4-5h1c2-2 4-4 6-7 1-1 2-2 3-2l-1 1 1 2 2-1z" class="B"></path><path d="M458 331c2-5 4-9 7-13l1 2c-2 3-4 5-5 8l-2 3h-1 0zm10-12c1 0 0 0 1-1h1c1-1 1-2 2-3v-1h1c0 1-1 3-2 4v3h-1-1c-1 2-3 3-4 5s-3 4-5 6l3-6c2-2 5-4 5-6v-1z" class="E"></path><path d="M454 338v-1c1 0 2-1 2-2l1 1h-1c0 2 0 4-1 5 0 3-2 6-1 10 1-7 3-12 6-18 0 4-1 8-2 12-1 1-3 4-2 5-1 1-1 3-2 4-1 0-1-1-1-1v-1c-2 1-1 3-2 4v-4h0c0-4 1-12 3-14z" class="k"></path><defs><linearGradient id="Al" x1="456.093" y1="321.611" x2="454.177" y2="345.791" xlink:href="#B"><stop offset="0" stop-color="#323131"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#Al)" d="M463 319c1-1 2-2 3-2l-1 1c-3 4-5 8-7 13-2 2-3 4-4 7-2 2-3 10-3 14h0c-1-1-1 0-1-1 1-2 0-4 0-5h-1c0 2 0 5-1 6h-1c0-3 0-8 1-11l1-3h0l2-6 1-1c1-2 2-4 4-5h1c2-2 4-4 6-7z"></path><path d="M451 332l1-1c1 1 1 2 1 3l-2 4c0 2-1 3-2 4l-1-1 1-3h0l2-6z" class="P"></path><defs><linearGradient id="Am" x1="431.759" y1="323.723" x2="439.627" y2="339.079" xlink:href="#B"><stop offset="0" stop-color="#4e4d4d"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#Am)" d="M456 310h-1c0 1 0 1-1 2 2 0 2 0 4-1v1l-1 2v2l-3 3c-1 1-5 5-5 7l-1 1-1-1 1-2-1-2c-2 4-7 11-7 16 0 2-1 3-2 5-1 0-1 0-1 1-1 4 0 9 1 13l-1 1-1-1-1-1c-1-2 0-5-2-7h-1l-1 1h0l-2-2c-2-3-2-7-1-10h1c0-2 0-4 1-6h1 0c0-3 1-7 2-10v3l3-3c3-2 6-5 9-8-1 2-3 4-3 6 0-1 1-2 2-2h1c1 0 3-3 4-4 2-1 4-3 7-4z"></path><path d="M433 343c-1-2 0-4 0-5h0l2 2-1 4v1c0 1 0 2-1 3v-5z" class="R"></path><path d="M429 338c0-2 0-4 1-6h1l-1 9c-1 1 0 1-1 1v-4z" class="b"></path><path d="M433 343v5c1-1 1-2 1-3v-1c0 2 0 4 1 6l1-1v6 2l-1-1c-1-2 0-5-2-7h-1l-1 1h0l-2-2 1-1v-3 1l2 2v-1l1-3z" class="S"></path><path d="M435 340v-2c-1-2 0-2-1-2 0-1-1-2-1-3 1-2 2-4 4-6v1c-1 1 0 2 0 3l-1 3c2-1 2-3 4-4-1 3-3 6-2 9 1 0 1 0 2-1 0 2-1 3-2 5-1 0-1 0-1 1-1 4 0 9 1 13l-1 1-1-1v-2-6l-1 1c-1-2-1-4-1-6l1-4z" class="c"></path><path d="M436 334c2-1 2-3 4-4-1 3-3 6-2 9 1 0 1 0 2-1 0 2-1 3-2 5-1 0-1 0-1 1-1 4 0 9 1 13l-1 1-1-1v-2c1-2 1-5 0-7 0-2 0-6 1-8v-1l-1-1v-4z" class="K"></path><defs><linearGradient id="An" x1="444.973" y1="313.884" x2="448.442" y2="337.542" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#4a4948"></stop></linearGradient></defs><path fill="url(#An)" d="M456 310h-1c0 1 0 1-1 2 2 0 2 0 4-1v1l-1 2v2l-3 3c-1 1-5 5-5 7l-1 1-1-1 1-2-1-2c-2 4-7 11-7 16-1 1-1 1-2 1-1-3 1-6 2-9-2 1-2 3-4 4l1-3c1-2 1-5 3-7 1-2 3-4 5-6 1 0 3-3 4-4 2-1 4-3 7-4z"></path><path d="M447 322l4-4v2l-3 4-1-2z" class="M"></path><path d="M457 314v2l-3 3-2 2v-1c1-3 3-5 5-6z" class="X"></path><path d="M452 320v1l2-2c-1 1-5 5-5 7l-1 1-1-1 1-2 3-4h1z" class="B"></path><path d="M440 324l1 1c0-1 1-2 2-3 0 1-1 2-1 2-1 2-1 3-2 5v1c-2 1-2 3-4 4l1-3c1-2 1-5 3-7z" class="k"></path><path d="M367 765c1 0 1-1 1-2h1l1-1v2c1 0 1 0 2-1v1h1v2h-3-1 0l-1 1-1 1v4c0 5 1 11 1 16 0 2 1 8 0 10 0 2 0 1-1 1l-4 1-1 2c-1 1-1 2-1 3l-1 1h0c-2-2-3-3-3-6h-1c-1-3-2-7-2-10s0-7-2-10l2 2v-8l1-1c0-2-1-2 1-4v-1c2 1 4 1 6 1 0 0 0 1 1 0h2v-3-2l1 1h1z" class="W"></path><path d="M358 797v-4c2 1 2 6 2 8l1 1h1c-1 1-1 2-1 3-3-3-3-5-3-8z" class="e"></path><path d="M357 800v-3c-1-2-1-5-1-8 2 2 2 5 2 8s0 5 3 8l-1 1h0c-2-2-3-3-3-6zm1-26h2l1 2v-1c0 1 1 2 1 2-1 3-1 6-1 9-1-2-1-6-1-8l-1 1v5h-1v-10z" class="T"></path><path d="M352 780l2 2v1c1 1 1 2 2 2v4c0 3 0 6 1 8v3h-1c-1-3-2-7-2-10s0-7-2-10z" class="E"></path><path d="M361 786c0-3 0-6 1-9l1 3c0 2 1 4 1 6-1 1-1 1-2 3l1 5c0 2 0 3 2 5h1c0-1 0 0 1-1v1l-4 1-1 2h-1v-16z" class="c"></path><path d="M356 769v-1c2 1 4 1 6 1l-1 1-1 1v2l1 1v1 1l-1-2h-2 0c-2 1-1 9-2 11-1 0-1-1-2-2v-1-8l1-1c0-2-1-2 1-4z" class="G"></path><path d="M356 769v-1c2 1 4 1 6 1l-1 1-1 1v2c-1 0-2-1-2-2l-2-2z" class="j"></path><path d="M354 774l1-1c1 4 1 7 0 11l-1-1v-1-8z" class="P"></path><path d="M364 777h0c1 4 2 8 4 11 0 2 1 8 0 10 0 2 0 1-1 1v-1c-1 1-1 0-1 1h-1c-2-2-2-3-2-5l-1-5c1-2 1-2 2-3 0-2-1-4-1-6 0-1 0-2 1-3z" class="O"></path><path d="M364 786v2h1v-2h1l1 12c-1 1-1 0-1 1h-1c-2-2-2-3-2-5l-1-5c1-2 1-2 2-3z" class="f"></path><path d="M367 765c1 0 1-1 1-2h1l1-1v2c1 0 1 0 2-1v1h1v2h-3-1 0l-1 1-1 1v4c0 5 1 11 1 16-2-3-3-7-4-11h0c-1 1-1 2-1 3l-1-3s-1-1-1-2v-1l-1-1v-2l1-1 1-1s0 1 1 0h2v-3-2l1 1h1z" class="I"></path><path d="M362 769s0 1 1 0h2c0 2 0 5-1 8h0c0-3 0-4-1-6l-2-1 1-1z" class="G"></path><path d="M361 770l2 1c1 2 1 3 1 6-1 1-1 2-1 3l-1-3s-1-1-1-2v-1l-1-1v-2l1-1z" class="I"></path><path d="M361 770l2 1-1 2v1h-1l-1-1v-2l1-1z" class="F"></path><defs><linearGradient id="Ao" x1="593.637" y1="397.239" x2="602.863" y2="404.761" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#Ao)" d="M593 393l1-6h0c1 1 3 2 4 3v1h2l-1-4c1 1 1 2 1 2v1h1c0-1 1-2 1-3l1 4 3 9c1 2 2 4 2 6v2h2 0l1 1v-1l1 1v1c0 1 1 2 0 2v1c0 2 1 7 0 9h0c-1-1-1-2-1-3l-1-2h0c0-1-1-2-1-3 0 4 0 8 1 11h-2-1l-1 1h0l-1-1-2-2c-2-2-3-4-5-6-1-2-3-4-4-7h1v-2l-1-2c-2-2-2-4-3-7-1-2-1-4-1-7l1 1v-1c1 1 1 2 2 3h0v-2z"></path><path d="M600 391l2 7-1 1-1-2c-1-2-1-4-2-6h2zm-5 17c1 3 2 5 4 8l-1 1h0c-1-2-3-4-4-7h1v-2z" class="G"></path><path d="M601 412h0c0-1-1-1-1-2l-1-8c1 2 2 4 3 7h0 1c1 1 2 2 2 3 1 3 1 5 2 7-3-2-3-6-5-8l-1 1z" class="J"></path><path d="M605 412h1l1 1h0c1-1 0-3 0-4 1 0 1 0 1 1v1h0v1h0c1 1 1 1 1 2h0c0 4 0 8 1 11h-2-1l-1 1h0l-1-1-2-2v-2l-1-1c1-3 1-4-1-8l1-1c2 2 2 6 5 8-1-2-1-4-2-7z" class="Z"></path><path d="M605 425v-1-2-5l3 8h-1l-1 1h0l-1-1z" class="f"></path><path d="M593 393l1-6h0c1 1 3 2 4 3l-1 1v6h-2c0 2 3 11 2 12-2-3-2-6-3-9h-1l1 6c-2-2-2-4-3-7-1-2-1-4-1-7l1 1v-1c1 1 1 2 2 3h0v-2z" class="M"></path><defs><linearGradient id="Ap" x1="598.453" y1="395.119" x2="609.843" y2="400.78" xlink:href="#B"><stop offset="0" stop-color="#3b3a39"></stop><stop offset="1" stop-color="#69686a"></stop></linearGradient></defs><path fill="url(#Ap)" d="M602 387l1 4 3 9c1 2 2 4 2 6v2h2 0l1 1v-1l1 1v1c0 1 1 2 0 2v1c0 2 1 7 0 9h0c-1-1-1-2-1-3l-1-2h0c0-1-1-2-1-3h0c0-1 0-1-1-2h0v-1h0v-1c0-1 0-1-1-1 0 1 1 3 0 4h0l-1-1h-1c0-1-1-2-2-3 0-2-1-3-1-5h1c0 1 0 1 1 2l1-1-2-3v-1h-1c-1-1-1-1-1-2l1-1-2-7-1-4c1 1 1 2 1 2v1h1c0-1 1-2 1-3z"></path><path d="M608 408h2 0l1 1v6c-2-2-2-5-3-7z" class="Y"></path><defs><linearGradient id="Aq" x1="543.598" y1="470.203" x2="529.512" y2="478.234" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#Aq)" d="M533 450c0-1 1-2 1-3l3 5 6 13 4 13c3 8-1 15-3 23l-1 1c0-1-1-2-2-3h0c-4-5-6-10-7-16h0v-3-5c-1-3-1-7-1-10l1-1c0-6-1-10-2-16l1 2z"></path><path d="M543 483l-1-4v-3l1-1c0 1 0 2 1 4 0 4 1 7 0 11-1-2-1-5-1-7z" class="I"></path><path d="M534 464c1 4 1 10 1 15 1 3 1 5 1 8 0-1 0-1-1-2l-1-5v-5c-1-3-1-7-1-10l1-1z" class="E"></path><path d="M533 450c0-1 1-2 1-3l3 5 6 13 4 13-1-1-3-3s0-3-1-4c-1-5-4-10-6-15l-3-5z" class="k"></path><path d="M541 499c0-8-1-16-2-23 0-3-1-6 1-7 1 4 1 10 2 14h1c0 2 0 5 1 7l-1 3c-1 1-1 2-1 3v2l1 3h1l-1 1c0-1-1-2-2-3h0z" class="j"></path><path d="M542 483h1c0 2 0 5 1 7l-1 3c-1 1-1 2-1 3v2c-1-4 0-10 0-15z" class="M"></path><path d="M543 474l3 3 1 1c3 8-1 15-3 23h-1l-1-3v-2c0-1 0-2 1-3l1-3c1-4 0-7 0-11 0-2 0-3-1-5z" class="P"></path><path d="M486 272h0c2-1 2-2 4-3 1 2 2 5 4 7v2c0 1 0 3 1 4 0 2 0 5 1 7s1 4 2 6v1l-1-1v1c0 1 1 2 2 2l1 2h2v1h-2c0-1 0-1-1-1l-1-1c-4 0-5 1-8 3v1l-2 2-1 2h1v1c-2-1-3-3-5-4-1-2-1-3-3-5v-3l-1-1c0-1 0-1-1-2h1v-1c0-1 0-2-1-2v-1-3-1-3l1-1v-2l5-1c1-1 1-2 1-3l1-3z" class="L"></path><path d="M489 294l1-2 2 1v1l-1 1c1 1 1 1 2 1h1l1 1h-3 0l-3 2h-1c1-1 2-2 2-3l-1-1v-1z" class="Q"></path><path d="M488 279h3c1 0 1 0 1 1 1 1 1 2 1 4h-1c-2-1-3-3-4-5z" class="C"></path><path d="M483 304l1-1c1 0 1 0 2-1 2 0 2 0 4 1l-2 2-1 2h1v1c-2-1-3-3-5-4zm-3-22l2-1c1 1 1 1 1 2v2c1 1 1 2 2 2v1l-1 1-1-2-1 1h-2c-1 0-1 1-2 2v-1-3c1 0 1 1 2 1l1-1c0-1 0-1-1-1l-1-1 1-2z" class="N"></path><path d="M479 292h7l-1-1h-2v-2h1 0l2 2c1 0 1 1 2 2l1 1v1c-1 1-3 1-4 2l1 2h-1c-2-1-3 0-5 0v-3l-1-1c0-1 0-1-1-2h1v-1z" class="H"></path><path d="M478 293h1 1c2 0 5 0 7 1-1 0-1 0-2 1-1 0-2 0-2 1h-3l-1-1c0-1 0-1-1-2z" class="D"></path><path d="M486 272h0c2-1 2-2 4-3 1 2 2 5 4 7v2c-2-1-3-2-5-2 0 1-1 1-1 2-1 1-1 1-2 1h-2v1h-1 0-3v2l-1 2 1 1c1 0 1 0 1 1l-1 1c-1 0-1-1-2-1v-1-3l1-1v-2l5-1c1-1 1-2 1-3l1-3z" class="H"></path><defs><linearGradient id="Ar" x1="747.793" y1="307.835" x2="776.271" y2="346.752" xlink:href="#B"><stop offset="0" stop-color="#130e11"></stop><stop offset="1" stop-color="#474c49"></stop></linearGradient></defs><path fill="url(#Ar)" d="M777 288c1 0 1-1 2-2h0l2-2 1 1c-1 3-3 6-4 10l-7 13c-1 4-4 9-5 13 0 0-1 1-1 2v2l-1 1v3l-1 2v2c-1 4-3 9-5 13l-8 20-18 41h0 0c0-1 0-2 1-3l1-3 3-6 3-6v-2-1c1-1 1-2 2-3v-1c1-1 1 0 1-2 0 0 0-1 1-1 0-1 1-2 1-3v-1l2-5c1-1 1-2 1-3-2 2-3 3-5 4l-9 21-5 12c-1 1-1 3-2 4h-1l6-14 45-106z"></path><defs><linearGradient id="As" x1="323.788" y1="588.819" x2="305.559" y2="588.663" xlink:href="#B"><stop offset="0" stop-color="#272726"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#As)" d="M304 631c1-2 2-5 3-8 1-7-1-13-2-20-2-18 13-32 23-46-5 13-12 23-14 36-1 3-1 6-1 10 1 2 1 3 1 5 1 9 0 18-5 24-1 1-1 2-2 2-3 2-5 2-8 3l5-6z"></path><path d="M304 631h1c1-1 1-2 1-3 1-1 1-1 1-2l1 1c0-1 1-2 1-3v-4c1-2 0-4 1-6 0 3 1 7 0 10v1c-1 3-1 5-3 7 0 1-1 2 0 2-3 2-5 2-8 3l5-6z" class="W"></path><path d="M310 614v-1-1-1l-1-1c0-2-1-4-1-5v-2-3h0l2 3h0c0-1 0-1 1-2v1c1 1 1 1 2 1 1 2 1 3 1 5 1 9 0 18-5 24-1 1-1 2-2 2s0-1 0-2c2-2 2-4 3-7v-1c1-3 0-7 0-10z" class="k"></path><defs><linearGradient id="At" x1="230.724" y1="338.82" x2="243.02" y2="291.114" xlink:href="#B"><stop offset="0" stop-color="#4d4c4d"></stop><stop offset="1" stop-color="#727171"></stop></linearGradient></defs><path fill="url(#At)" d="M227 282c2 2 3 5 4 8l7 17 14 33c-3 0-5 2-8 4l-22-54c1-1 1-2 2-3h2-1l1-1 1-1h-1l1-1h1c-1-1-1-1-1-2z"></path><path d="M863 214l10-1 2 1 1 2 1 2c1 2 5 5 7 7h-1c-4 0-10-1-15-1-16-1-30 1-44 9v-2h0v-1h-3-1 0-2v1h-1v1h-1c0-1 1-1 1-2v-1c1-1 3-2 4-2l8-5c1 0 2-1 4-1 7-2 14-4 22-5l2-2h6z" class="b"></path><path d="M829 222c1 0 2-1 4-1 1 1 3 1 5 1-6 2-12 5-18 8h0-2v1h-1v1h-1c0-1 1-1 1-2v-1c1-1 3-2 4-2l8-5z" class="D"></path><path d="M863 214l10-1 2 1 1 2 1 2c-3-1-7-1-11-1-10 0-19 1-28 5-2 0-4 0-5-1 7-2 14-4 22-5l2-2h6z" class="Q"></path><path d="M857 214h6 0l-2 1c-1 1-4 1-6 1l2-2z" class="D"></path><defs><linearGradient id="Au" x1="566.154" y1="313.662" x2="578.459" y2="332.703" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#Au)" d="M568 309c2 0 2 1 3 2s2 2 3 4l1 1c1 2 3 4 3 6l3 8h0l-1-7c1 0 1 1 1 2l2 3v2l1 3c0 2 2 4 2 7h-1l-1 10v5 10 1h0c-1-1-1-2-1-4l-1 1c0 1 0 2-1 3-1-1-2-1-2-3h0c-1-2-1-2-2-3h-1v3l-2-9h0v-1c0-2 0-3 2-5v-1c2-1-1-5 0-7v-1c0-4-2-7-3-9s-2-4-3-5v2h-1c-1-4-5-7-4-11v-2-2h1c1 0 1 0 3 1 0-2 0-3-1-4z"></path><path d="M565 314v-2h1c1 1 3 4 3 7v1h-1l-3-6z" class="X"></path><path d="M580 335v-1c-2-3-1-5-1-8-1-2-1-2-1-4l3 8h0l-1-7c1 0 1 1 1 2l2 3v2l1 3c0 2 2 4 2 7h-1l-1 10v5h-1 0c-1-1-1-2-1-3l-1 1v1c-1-3 0-7-1-10h-1c-1-3-1-7-2-10 1 1 2 3 2 4 1 2 1 4 2 5 1-2 0-6-1-8z" class="c"></path><path d="M581 353l1-10c1 1 1 5 2 6v1 5h-1 0c-1-1-1-2-1-3l-1 1z" class="S"></path><path d="M580 335v-1c-2-3-1-5-1-8-1-2-1-2-1-4l3 8h0l-1-7c1 0 1 1 1 2l2 3v2c-1 1 0 8 1 10-1-1-2-3-3-5h-1z" class="k"></path><path d="M576 339c1 2 1 4 3 6v-1h1c1 3 0 7 1 10v-1l1-1c0 1 0 2 1 3h0 1v10 1h0c-1-1-1-2-1-4l-1 1c0 1 0 2-1 3-1-1-2-1-2-3h0c-1-2-1-2-2-3h-1v3l-2-9h0v-1c0-2 0-3 2-5v-1c2-1-1-5 0-7v-1z" class="h"></path><path d="M576 339c1 2 1 4 3 6v-1h1c1 3 0 7 1 10v5c-1-1-1-7-1-9-2 2-1 9-1 11l-3-13v-1c2-1-1-5 0-7v-1z" class="Z"></path><defs><linearGradient id="Av" x1="492.599" y1="368.725" x2="506.901" y2="385.775" xlink:href="#B"><stop offset="0" stop-color="#1d1d1c"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#Av)" d="M499 369c0-1 1-3 2-4l3-9v7c0 1-1 2-1 3-1 2-1 3-1 5-1 4-2 10-2 15 1-1 1-3 1-4l1 1v1c-1 4-1 9-3 12l-1 8v-2l-1-1c0 1-1 2-2 3h0-1c-1 1-2 1-3 2h-1c-1 0-3 1-4 2-1-1-2-3-2-4v-1c-2-5 0-12 2-17l-2-2c3-3 5-6 8-8h0l7-7z"></path><path d="M494 390v-1c1 2 1 3 1 5h1l1-1v2h0l-3 2-1-1h0-1l2-6z" class="e"></path><path d="M497 395v3 1c1-1 2-2 2-3h0l-1 8v-2l-1-1c0 1-1 2-2 3h0l1-3h-1l-2 2c0-1 0-1-1-3v-1h1 1v-2l3-2z" class="Z"></path><path d="M495 377c1 0 2-1 3-1l-2 4v3c1 2 1 2 1 4 0 1 0 3-1 4-1-1-1-2-1-4v1h-1c0 1-1 1 0 2l-2 6-1 1c0-1 1-2 0-3 0-1-1-2-1-2 0-1 0-2 1-4h-1l-2 5v-1c0-3 2-8 4-10l3-5z" class="k"></path><path d="M488 393l2-5h1c-1 2-1 3-1 4 0 0 1 1 1 2 1 1 0 2 0 3l1-1h1 0l1 1v2h-1-1v1c1 2 1 2 1 3l2-2h1l-1 3h-1c-1 1-2 1-3 2h-1c-1 0-3 1-4 2-1-1-2-3-2-4v-3h0c0-2 0-4 1-6 0-1 1-1 2-2 0 1 0 2 1 3v-3z" class="Z"></path><path d="M491 403c-1-1-1-1-2-1 0-4 0-7 1-10 0 0 1 1 1 2 1 1 0 2 0 3l1-1h1 0l1 1v2h-1-1v1l-1 3z" class="J"></path><path d="M484 401c1-1 2-1 3-1h1l1 5c1 0 1-1 2-2l1-3c1 2 1 2 1 3l2-2h1l-1 3h-1c-1 1-2 1-3 2h-1c-1 0-3 1-4 2-1-1-2-3-2-4v-3z" class="d"></path><path d="M499 369v2c-1 2 0 3-1 5-1 0-2 1-3 1l-3 5c-2 2-4 7-4 10v1 3c-1-1-1-2-1-3-1 1-2 1-2 2-1 2-1 4-1 6h0v3-1c-2-5 0-12 2-17l-2-2c3-3 5-6 8-8h0l7-7z" class="W"></path><path d="M492 376h1c-2 4-5 6-7 9v1l-2-2c3-3 5-6 8-8z" class="l"></path><path d="M485 395c1-8 4-12 9-18h1l-3 5c-2 2-4 7-4 10v1 3c-1-1-1-2-1-3-1 1-2 1-2 2z" class="J"></path><path d="M644 185c2 3 5 6 7 9l-1 21h17c10 0 20 0 30 3 3 1 7 2 9 3-6 1-13 0-19-1 1 1 3 2 4 2v1c-16-2-30-1-46-2-1 1-1 1-1 0v-36z" class="c"></path><path d="M645 221h6c1-1 3-1 4-1 4 0 13 1 16 0-2 0-5 0-7-1 7-1 16 0 23 1 1 1 3 2 4 2v1c-16-2-30-1-46-2z" class="O"></path><defs><linearGradient id="Aw" x1="520.023" y1="487.552" x2="503.031" y2="493.927" xlink:href="#B"><stop offset="0" stop-color="#2f302e"></stop><stop offset="1" stop-color="#4e4c4e"></stop></linearGradient></defs><path fill="url(#Aw)" d="M506 440l5 16c0 1-1 3 0 4v1c1 1 2 4 3 6 1 6-2 14-3 20v11c1 0 1 1 1 1-1 7 1 15 0 21l1 6-4-8c-2-3-4-6-5-10-2-2-2-7-2-10 0-2 0-4-1-6v-2-11c1-1 1-1 1-2-1 0-1 1-2 2l2-5v1c0 1 1 2 1 3v-1l1-2 1-7c3 2 0 6 2 9l1-10v-8c0-3-1-6-1-8 1-2 0-2 0-3 0-3-1-5-1-8z"></path><path d="M506 500c1 1 1 2 2 3 0 2-1-1 0 2 1 1 1 3 1 4 1 4 2 8 3 11l1 6-4-8c1-2-1-9-2-11-1-3-1-5-1-7z" class="e"></path><path d="M508 467h0c1-2 1-4 1-6h0v1 3 1c0 1-1 3 0 5 0 1 1 4 0 5 0 1 0 3-1 4 0 2 0 5 1 7 0 4-1 8-1 13h-1v-3h0c-1 1 0 1-1 2v1c0 2 0 4 1 7 1 2 3 9 2 11-2-3-4-6-5-10-2-2-2-7-2-10 0-2 0-4-1-6v-2-11c1-1 1-1 1-2-1 0-1 1-2 2l2-5v1c0 1 1 2 1 3v-1l1-2 1-7c3 2 0 6 2 9l1-10z" class="P"></path><path d="M504 508h1l-1-2c0-3-1-6 0-9 1 1 1 4 1 6s1 3 1 4h1c1 2 3 9 2 11-2-3-4-6-5-10z" class="O"></path><path d="M504 475l1-7c3 2 0 6 2 9v2c-1 1 0 3-1 4l-1 11h-1c0-5 3-13 1-18h0v-3c-1 6-3 11-4 17v-11c1-1 1-1 1-2-1 0-1 1-2 2l2-5v1c0 1 1 2 1 3v-1l1-2z" class="T"></path><path d="M511 456l1-1c0-1-1-3-1-4l-2-6c3 2 4 9 5 13v7c1-1 1-3 1-5l1 1v1h1 1c1 3 1 5 1 8v1l1 1c0 1-1 4 0 5l1 14v12c-2 7-2 14-4 21h2l-5 12v2h0c-1-2-1-9-1-12h0l-1-6c1-6-1-14 0-21 0 0 0-1-1-1v-11c1-6 4-14 3-20-1-2-2-5-3-6v-1c-1-1 0-3 0-4z" class="B"></path><path d="M514 536v-11c0-1 0-3 1-5v1 7-1c1-1 1-2 2-3h2l-5 12z" class="W"></path><path d="M514 465c1-1 1-3 1-5l1 1v1h1c0 1 0 2-1 3v6c-1 3-1 6-1 9h0c-1-3 0-6 0-9-1-2-1-4-1-6z" class="X"></path><path d="M511 487h1v-1c0-1 0-2 1-3v-3l1-1c0 7 0 14-2 20 0 0 0-1-1-1v-11z" class="M"></path><defs><linearGradient id="Ax" x1="502.135" y1="485.802" x2="530.365" y2="505.698" xlink:href="#B"><stop offset="0" stop-color="#31302f"></stop><stop offset="1" stop-color="#585859"></stop></linearGradient></defs><path fill="url(#Ax)" d="M517 462h1c1 3 1 5 1 8v1l1 1c0 1-1 4 0 5l1 14v12c-2 7-2 14-4 21-1 1-1 2-2 3v1-7-1l1-49v-6c1-1 1-2 1-3z"></path><path d="M354 734c1-3 0-7 3-9v1l1 2c1 2 2 5 2 8 1 3 1 6 3 9l2 12c0 1-1 2 0 4h-1c0 2 0 3 1 5v3h-2c-1 1-1 0-1 0-2 0-4 0-6-1v1c-2 2-1 2-1 4l-1 1c-1-1-1-1-2-1h0c-1-3-1-5 0-7-1-1-1-1 0-2h0l-1-1-1 3c0-1 0-2-1-3v2c-1 1-1 1-1 2l-1 1v1c-1 0-1 1-1 2 0 2-1 3-2 5 0-3 1-7 1-10 1-3 2-5 3-8 2-5 2-9 2-14 1-3 1-6 1-9h1c1-1 1-3 2-4v3z" class="W"></path><path d="M358 728c1 2 2 5 2 8l-1-1-1 3v-10z" class="G"></path><path d="M356 759c0-1-1-2-1-2 1-2 2-2 3-3 0 1 0 2 1 2l2 2h-4l-1 1z" class="P"></path><path d="M356 759l1-1c0 2-1 4 0 6 1 0 2 1 3 1h0l2 4c-2 0-4 0-6-1v-3h-1v3h-1c0-3 0-7 2-9z" class="b"></path><path d="M357 758h4l3 3c0 2 0 3 1 5v3h-2c-1 1-1 0-1 0l-2-4h0c-1 0-2-1-3-1-1-2 0-4 0-6z" class="R"></path><path d="M360 765v-2l1-1 1 1v2h-2 0z" class="o"></path><path d="M354 731v3c0 3 0 8 1 10s2 5 1 7v2c-1 2-2 5-3 8-1 2 0 3 0 5h-1c-1-1-1-1 0-2h0l-1-1-1 3c0-1 0-2-1-3 2-2 3-5 3-8 1-4 0-9 2-13h-1v-1l-1-6c1-1 1-3 2-4z" class="K"></path><path d="M354 731v3c0 3 0 8 1 10v5h-1v-6-1h-1v-1l-1-6c1-1 1-3 2-4z" class="T"></path><path d="M358 738l1-3 1 1c1 3 1 6 3 9l2 12c0 1-1 2 0 4h-1l-3-3-2-2 1-1-2-17z" class="B"></path><defs><linearGradient id="Ay" x1="347.803" y1="753.038" x2="351.422" y2="753.849" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#434242"></stop></linearGradient></defs><path fill="url(#Ay)" d="M350 744c1-3 1-6 1-9h1l1 6v1h1c-2 4-1 9-2 13 0 3-1 6-3 8v2c-1 1-1 1-1 2l-1 1v1c-1 0-1 1-1 2 0 2-1 3-2 5 0-3 1-7 1-10 1-3 2-5 3-8 2-5 2-9 2-14z"></path><defs><linearGradient id="Az" x1="613.299" y1="406.788" x2="619.245" y2="431.561" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#c2c1c1"></stop></linearGradient></defs><path fill="url(#Az)" d="M612 386h1c1 0 2-1 3-1l1 2c1 2 2 4 2 6v1h2c0 1 1 2 1 4h0v4 2l-1 1c1 7 1 15 3 22v2 1 2c1 2 2 3 4 4h1-3-3c-2-1-5 0-8-2v-1l-1-1c-1-1 0-1 0-2v-3c-1 0-1 0-2-1v-1h-1-1c-1-3-1-7-1-11 0 1 1 2 1 3h0l1 2c0 1 0 2 1 3h0c1-2 0-7 0-9v-1c1 0 0-1 0-2v-1l-1-1v1l-1-1h0-2v-2l1-1v-5h0c-1-1-1-2-1-3 1-1 1-2 0-3v-1h1c1 2 2 4 3 5v-2l-2-6c0-1 0-3-1-4l1 1c1 0 1 1 2 2l1-1-1-2z"></path><path d="M614 403c1 5 0 11 1 16h-1c-1-2-1-4-2-7 1 0 0-1 0-2h1l1-4v-3z" class="b"></path><path d="M609 386l1 1c1 0 1 1 2 2l1-1c1 3 2 9 2 12l1 4h0c-1-1-1-2-1-3-1-3-2-10-5-11 0-1 0-3-1-4z" class="W"></path><path d="M609 400h0c-1-1-1-2-1-3 1-1 1-2 0-3v-1h1c1 2 2 4 3 5v-2c1 1 1 6 2 7v3l-2-4h0c-1-2-1-2-2-2h-1z" class="K"></path><path d="M609 400h1c1 0 1 0 2 2h0l2 4-1 4h-1v-1l-1-1v1l-1-1h0-2v-2l1-1v-5z" class="J"></path><path d="M610 408c0-2 0-4 2-6l2 4-1 4h-1v-1l-1-1v1l-1-1z" class="Z"></path><path d="M609 414c0 1 1 2 1 3h0l1 2c0 1 0 2 1 3h0c1-2 0-7 0-9v-1c1 3 1 5 2 7h1c2 4 0 9 4 13-4-1-4-6-5-9h-1c0 1 0 3 1 4-1 0-1 0-2-1v-1h-1-1c-1-3-1-7-1-11z" class="S"></path><path d="M614 427c-1-1-1-3-1-4h1c1 3 1 8 5 9h2c-1-1-1-3-1-5 1 2 2 4 4 5 1 2 2 3 4 4h1-3-3c-2-1-5 0-8-2v-1l-1-1c-1-1 0-1 0-2v-3z" class="V"></path><path d="M621 432c-1-1-1-3-1-5 1 2 2 4 4 5 1 2 2 3 4 4h1-3l-1-1c-2 0-3-1-4-3z" class="N"></path><defs><linearGradient id="BA" x1="613.599" y1="412.225" x2="625.159" y2="415.213" xlink:href="#B"><stop offset="0" stop-color="#8f8e8d"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#BA)" d="M612 386h1c1 0 2-1 3-1l1 2c1 2 2 4 2 6v1h2c0 1 1 2 1 4h0v4 2l-1 1c1 7 1 15 3 22v2 1 2c-2-1-3-3-4-5 0-2-2-4-2-6-1-1-1-3-1-4-1-1-1-1-1-2v-5c0-2 0-2 1-3-1-1-1-2-1-3l-1-4c0-3-1-9-2-12l-1-2z"></path><path d="M619 393v1c1 2 2 5 2 8l-2 1v7h0c0-2-1-5 0-7 0-3 0-6-1-8v-1l1-1z" class="S"></path><path d="M619 394h2c0 1 1 2 1 4h0v4 2l-1 1v-3c0-3-1-6-2-8z" class="G"></path><defs><linearGradient id="BB" x1="612.668" y1="387.171" x2="616.39" y2="391.782" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#727172"></stop></linearGradient></defs><path fill="url(#BB)" d="M612 386h1c1 0 2-1 3-1l1 2c1 2 2 4 2 6l-1 1v1c1 2 1 5 1 8v-1l-1-1-3-1c0-3-1-9-2-12l-1-2z"></path><path d="M448 204l2-1v1l-1 2 1 1-2 2-1 2h-1l-4 3v1c1 0 2-1 3 0l2-1 1-1h1s1-1 2-1v1l-2 2c1 2 2 5 3 7l-1 1c0 1 0 2-1 2l1 1v2h2c0 2 0 2-1 3l-1 1 1 1h0v1h-3l-1-1c-1 0-1 0-2 1-2-2-2-1-4-1-4 1-8 4-12 4v-1l5-2 2-3-6 1v-1h0-1-4l-3 1c-2-2-3-1-5-2l-1-1 3-2 4-2v-1h0c-1-1-2-1-3-1 3-1 5-3 8-5s6-5 10-7l2-1 1 2 2-2h0l3-2c-1-1 0-1-1-1v-1l2-2z" class="K"></path><path d="M437 231h0c1-1 1-1 3-1 1-1 1 0 3 1-3 1-5 3-8 3l2-3z" class="g"></path><path d="M436 220l2-1v1c1 0 4-1 6-1-1 1-2 1-3 2h1c2 0 2 1 3 2v1 2 1 1l-1-1v-1c0-3-1-1-2-3-2 0-3 0-5 1h-1v-1h-2 0-1c2-1 3-1 3-3z" class="c"></path><path d="M439 211l2-1 1 2c-1 1-2 2-3 2-2 1-4 3-5 5-3 2-6 4-10 5-1-1-2-1-3-1 3-1 5-3 8-5s6-5 10-7z" class="R"></path><path d="M447 214l1-1h1s1-1 2-1v1l-2 2c1 2 2 5 3 7l-1 1c0 1 0 2-1 2l1 1v2h2c0 2 0 2-1 3l-1 1 1 1h0v1h-3l-1-1 2-2h-1-1l-1-1 2-3-1-1v-1c0-1 0-2-1-3 1 0 1-1 1-2h-2l-1-1 1-1v-1l-2 1h0c1-2 2-3 3-4z" class="P"></path><path d="M452 231v-1c-1-1-2-1-2-2v-1-1h-1c0-1 1-1 1-2 1-1 0-1 0-2v-2l-1-1c-1 0-1-1-1-1 1-1 1-2 1-3 1 2 2 5 3 7l-1 1c0 1 0 2-1 2l1 1v2h2c0 2 0 2-1 3z" class="M"></path><path d="M436 220c0 2-1 2-3 3h1 0 2v1h1c2-1 3-1 5-1 1 2 2 0 2 3v1l1 1 1 1c-1 1-2 1-3 2h0c-2-1-2-2-3-1-2 0-2 0-3 1h0l-6 1v-1h0-1-4l-3 1c-2-2-3-1-5-2l-1-1 3-2 4-2c2 0 4-1 5-2h1c2-2 4-3 6-3z" class="R"></path><path d="M417 229l3-2 1 2c2 1 5 0 7-1h3v1h0c-2 1-3 1-5 2l-3 1c-2-2-3-1-5-2l-1-1z" class="H"></path><defs><linearGradient id="BC" x1="443.074" y1="228.556" x2="430.391" y2="229.447" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#8b8a8a"></stop></linearGradient></defs><path fill="url(#BC)" d="M442 226h2v1l1 1 1 1c-1 1-2 1-3 2h0c-2-1-2-2-3-1-2 0-2 0-3 1h0l-6 1v-1h0-1-4c2-1 3-1 5-2 1 1 2 0 3 0l8-3z"></path><path d="M436 220c0 2-1 2-3 3h1 0 2v1h1c2-1 3-1 5-1 1 2 2 0 2 3h-2c-1-1-1-1-2 0h-4c1-1 1-1 1-2-2 1-2 1-3 1-4 0-7 2-11 2 1 0 2-1 3-1 1-1 2-1 3-2v-1h1c2-2 4-3 6-3z" class="Z"></path><path d="M579 223c-1 0-3-2-3-3v-1c0-1-1-2-1-2s1-1 0-2l1-1v-1h2c1 1 3 2 4 2 1 1 2 1 3 2h0c3 3 7 5 10 7l5 2 2 1 1-1h1v2 1h-1c-2-1-3-1-4-1v1c1 1 3 2 5 3 1 1 3 1 4 2 2 0 2 0 4 1-2 1-4 1-6 2h-3l3 2c2 0 2 0 3 1 0 1 0 1 1 1l-1 1h0c-2-1-4 0-6 0l-2 1c-1 0-3 0-4-1h-1-1c-3-2-5-3-7-4l-3-2-2 1c-1 0-1 1-2 0-1 1-1 1-1 2l-1-1c0-1-1-1-1-2l-2-3c-1-1 0-1-1-2v-4-1h1v-1-1h2l1-1z" class="Y"></path><path d="M579 223v2s0 1-1 1c0 1-1 3-1 4l-2 1v-4-1h1v-1-1h2l1-1z" class="c"></path><defs><linearGradient id="BD" x1="578.064" y1="229.876" x2="579.728" y2="237.45" xlink:href="#B"><stop offset="0" stop-color="#787677"></stop><stop offset="1" stop-color="#8c8c8b"></stop></linearGradient></defs><path fill="url(#BD)" d="M576 233l1-2h0c1-1 1-1 1-2v-1h3v1l-1 2 2 2 3 3-2 1c-1 0-1 1-2 0-1 1-1 1-1 2l-1-1c0-1-1-1-1-2l-2-3z"></path><path d="M582 233l3 3-2 1c-1-1-2-1-2-2l1-2z" class="f"></path><path d="M581 229c3 3 11 10 14 9v1h-1l2 1v1h-1v1c-3-2-5-3-7-4l-3-2-3-3-2-2 1-2z" class="S"></path><path d="M589 233c-1 0-1 0-2-1l-2-2c-2-1-4-3-6-5 2 1 4 2 7 3-2-2-7-5-7-7h1l5 3 1-1v1c1 0 1 0 2 1 1 0 3 1 3 2v1c1 2 4 5 6 6l5 2c1 0 2 0 4 1h-3-2l-1-1c-4-1-8-4-12-6l-1 1 2 2z" class="R"></path><path d="M579 217l-1-1 1-1 1 1c1 1 3 1 5 1h0c3 3 7 5 10 7h-2c-1-1-2-1-3-1l9 7-4-1h-1l-3-2c0-1-2-2-3-2-1-1-1-1-2-1v-1l-1 1-5-3v-1l-1-3z" class="g"></path><path d="M579 217l-1-1 1-1 1 1c1 1 3 1 5 1h0c3 3 7 5 10 7h-2c-1-1-2-1-3-1h0-2c-1-1-2-2-3-2s-2-1-3-2-1-1-3-2z" class="R"></path><path d="M589 233l-2-2 1-1c4 2 8 5 12 6l1 1h2l3 2c2 0 2 0 3 1 0 1 0 1 1 1l-1 1h0c-2-1-4 0-6 0l-2 1c-1 0-3 0-4-1h-1-1v-1h1v-1l-2-1h1v-1c-1-1-4-3-6-5z" class="h"></path><path d="M606 239c2 0 2 0 3 1 0 1 0 1 1 1l-1 1h0c-2-1-4 0-6 0s-4 0-6-1v-1c1-1 1-1 2-1 1-1 5 0 7 0h0z" class="D"></path><path d="M590 223c1 0 2 0 3 1h2l5 2 2 1 1-1h1v2 1h-1c-2-1-3-1-4-1v1c1 1 3 2 5 3 1 1 3 1 4 2 2 0 2 0 4 1-2 1-4 1-6 2-2-1-3-1-4-1l-5-2c-2-1-5-4-6-6v-1l3 2h1l4 1-9-7z" class="d"></path><path d="M597 234c2-1 4-1 6-1 1 1 2 1 3 2h0c-1 1-2 0-3 0l-1 1h0l-5-2z" class="H"></path><defs><linearGradient id="BE" x1="591.615" y1="487.968" x2="584.677" y2="510.73" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#939392"></stop></linearGradient></defs><path fill="url(#BE)" d="M594 490l1-1 1 1h1v3h1 1v-1c1 0 2 0 3-1l1 1c-1 1-1 2-1 3 0 2-1 3-2 5-2 4-5 12-8 14l-1 1c-2 0-3 2-5 1h0-2c-1 0-2 2-3 2l-2 3c-2 1-3 3-5 4v1l-1 2c0 2 0 3-1 4h-1l-2 2c-1 0-1 0-2-1l1-1 1 1c1-2 2-3 2-5 0-1 2-4 2-6v-2c-1-1-2-2-2-4-2 0-2 0-3-1l-1-1c-1 0-2 1-4 2h0-2l3-3h-3l1-1h3c2-2 4-3 6-5 1-2 3-4 4-6l1-1v-1h4l2 1v1c-1 1-1 2-2 3v1l4-3h0l3-3h0c4-3 5-6 7-9z"></path><path d="M596 496h-1c0-1 0-2 1-3h1 1c0 1-1 2-2 3z" class="Y"></path><path d="M572 515c4-2 7-4 10-6-2 2-4 4-6 5h-1c-1 1-1 1-2 1l-1 1c1 1 1 1 2 1 0-1 0-1 1 0l1-1-2 2 1 1c-1 0-2 2-2 3h0v-2c-1-1-2-2-2-4-2 0-2 0-3-1l-1-1c-1 0-2 1-4 2h0-2l3-3c2 0 4 1 6 1v1h2z" class="V"></path><path d="M578 508c0 2-1 3-3 4-1 1-2 1-3 2v1h-2v-1c-2 0-4-1-6-1h-3l1-1h3c2 0 3-1 5-1h1c2-1 5-2 7-3z" class="o"></path><path d="M576 499h4l2 1v1c-1 1-1 2-2 3v1c0 1-1 2-2 3-2 1-5 2-7 3h-1c-2 0-3 1-5 1 2-2 4-3 6-5 1-2 3-4 4-6l1-1v-1z" class="J"></path><path d="M576 499h4l2 1v1l-2 1-2-2-3 1 1-1v-1z" class="K"></path><path d="M582 501c-1 1-1 2-2 3v1c0 1-1 2-2 3-2 1-5 2-7 3h-1c3-3 7-5 10-9l2-1z" class="g"></path><path d="M575 517v-1c2-1 3-2 5-3 1-1 3-3 5-3-1 1-2 2-3 4h1 2v1l2-2 1 1-2 2h0-2c-1 0-2 2-3 2l-2 3c-2 1-3 3-5 4v1l-1 2c0 2 0 3-1 4h-1l-2 2c-1 0-1 0-2-1l1-1 1 1c1-2 2-3 2-5 0-1 2-4 2-6h0c0-1 1-3 2-3l-1-1 2-2-1 1z" class="i"></path><path d="M582 514h1 2v1l2-2 1 1-2 2h0-2c-1 0-2 2-3 2v-1c-1 0-2 1-3 1-1 1-1 1-2 1v-1c1 0 2-1 3-2l1-1c1-1 1-1 2-1z" class="L"></path><path d="M599 493v-1c1 0 2 0 3-1l1 1c-1 1-1 2-1 3 0 2-1 3-2 5-2 4-5 12-8 14l-1 1c-2 0-3 2-5 1l2-2-1-1-2 2v-1h-2-1c1-2 2-3 3-4h0l3-3v-1l1 2c1 0 1-1 1-2v2c0-1 1-1 1-2 2-1 2-2 3-3h0v-1c0-2 1-3 2-5v-1c1-1 2-2 2-3h1z" class="R"></path><path d="M586 256c1 0 1 0 2 1 3 2 8 1 11 1h0c2 1 3 1 4 2 2 1 9 5 10 6l-1 1c1 1 2 2 2 4l2 2c1 1 2 3 3 3v1c0 2 0 4 1 6 1 1 1 2 1 3h-3c-1 0-2 1-3 0-2 0-3 1-5 2l-6-1-2 1c-3-1-4-4-5-6 0-2-1-4-2-6-1-4-3-10-5-15h-1l-1-2-2-3z" class="Z"></path><path d="M611 275c-1-1-1-2-1-2l1-1h1c0-1 0 0 1-1h0 1l2 2v4h-2c0-1-1-1-1-2l-1-1-1 1z" class="J"></path><path d="M602 285c2 1 4 1 6 1 2-1 2-1 4-1h1l1-1-1-2c-1-1-1-1-1-2 1 0 2 2 3 2l1-1 3 3 1-1c1 1 1 2 1 3h-3c-1 0-2 1-3 0-2 0-3 1-5 2l-6-1-2-1c-2-1-2-2-3-4 1 1 2 1 3 3z" class="i"></path><path d="M597 279h1l1-1 1-2c1 1 1 1 2 1 0-1-1-2 0-3h1v3c2 1 3 2 5 3l-2-3 1-1c1 1 2 2 3 2v1l-1 1c1 1 1 2 2 3-2 0-2-1-4 0v-1l-2 1 1 1c-1 1-3 0-4 1-1-2-2-2-3-3-1 0-1-1-1-2l-1-1z" class="J"></path><path d="M597 279h1l1-1 1-2c1 1 1 1 2 1 0-1-1-2 0-3h1v3 1 4h0-1c-1-1-2-1-3-2l-1-1v1l-1-1z" class="W"></path><defs><linearGradient id="BF" x1="597.635" y1="266.082" x2="610.475" y2="268.267" xlink:href="#B"><stop offset="0" stop-color="#272626"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#BF)" d="M599 258c2 1 3 1 4 2 2 1 9 5 10 6l-1 1c1 1 2 2 2 4h-1 0c-1 1-1 0-1 1h-1l-1 1s0 1 1 2h0c0 1 1 2 1 3h-1 0c-1-2-3-4-5-5 1 2 3 4 4 5-1 0-2-1-3-2l-1 1 2 3c-2-1-3-2-5-3v-3c0 1 0 0 1 0 0-2 0-2-1-3-1-2-2-5-4-7l-2-3 2-2v-1z"></path><path d="M599 258c2 1 3 1 4 2 0 1 0 1-1 2-1-1-2-2-3-2v-1-1z" class="I"></path><defs><linearGradient id="BG" x1="595.785" y1="258.707" x2="593.174" y2="274.539" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#333"></stop></linearGradient></defs><path fill="url(#BG)" d="M586 256c1 0 1 0 2 1 3 2 8 1 11 1h0v1l-2 2 2 3c2 2 3 5 4 7 1 1 1 1 1 3-1 0-1 1-1 0h-1c-1 1 0 2 0 3-1 0-1 0-2-1l-1 2-1 1h-1l1 1c0 1 0 2 1 2 1 2 1 3 3 4l2 1-2 1c-3-1-4-4-5-6 0-2-1-4-2-6-1-4-3-10-5-15h-1l-1-2-2-3z"></path><path d="M599 258h0v1l-2 2 2 3-2-2c0-1-1-2-1-3l3-1z" class="X"></path><path d="M596 277c2-1 3-1 4-2h1c0-2-1-4-1-6 1 0 1 2 3 2 1 1 1 1 1 3-1 0-1 1-1 0h-1c-1 1 0 2 0 3-1 0-1 0-2-1l-1 2-1 1h-1c0-1 0-1-1-2z" class="M"></path><path d="M590 261v-2h1c2 1 3 2 4 3 1 3 2 8 1 11v3c1 1 1 0 0 1 1 1 1 1 1 2l1 1c0 1 0 2 1 2 1 2 1 3 3 4l2 1-2 1c-3-1-4-4-5-6 0-2-1-4-2-6-1-4-3-10-5-15z" class="F"></path><path d="M622 677h0c1-1 2-3 3-4v-2l1-1c0-1 0-2 1-3v-1-1l1-1v-2h0l1-1c0-1 0-1 1-2l1 4 1 7c-1 2-1 3-1 4l-1 1 2 7 2 4h0c1 2 1 1 1 2s1 2 1 4c1 0 1 1 1 2l1 1c2 2 4 3 7 5 1 0 3 1 4 3 0 1 1 4 1 6 2 3 3 7 3 10 1 2 2 5 1 7v2h0v1h-1v-3l-2-6-1 2 1 8v1l-1 3c0 1-1 2-1 3l-3-3c0-1 0-3-1-4l-1-10-1-11c-1 0-2 0-3-1s-2-1-4-1v-1c0-1-2-4-3-5v-1c-2-2-3-4-4-6l-1-1v-1c-1 0-1-1-1-2v-2c-1-2-1-4-1-6-2 2-1 6-1 9h0l-1-4c1-2 0-5 0-8h-1l-1-2z" class="k"></path><path d="M629 681c1 1 3 5 3 7l-1 4c-2-2-1-6-2-10v-1z" class="K"></path><path d="M648 717v-3l1-1c1 2 3 5 2 7l-1 2h-1c0-2-1-3-1-5z" class="L"></path><path d="M643 709l1 1v1h1v-2c1 0 3 2 4 4h0l-1 1v3c-1-1-1-2-2-3v3l-2 3-1-11z" class="l"></path><path d="M638 695c2 2 4 3 7 5 1 0 3 1 4 3 0 1 1 4 1 6h0l-3-3c-2 0-2 0-3 1l-1-1c-1 1-1 1-1 2l-2-2h0c-1-1-1-1-1-2-1-1-2-2-2-3h0c1 1 2 0 3 0s1 0 2-1c-1-2-3-3-4-5z" class="f"></path><path d="M645 700c1 0 3 1 4 3 0 1 1 4 1 6h0l-3-3c-1-1-2-1-3-2v1h-1l-1-1 2-2h3c-1-1-2-1-2-2z" class="H"></path><path d="M646 717v-3c1 1 1 2 2 3 0 2 1 3 1 5h1l1 8v1l-1 3c0 1-1 2-1 3l-3-3c0-1 0-3-1-4l-1-10 2-3z" class="o"></path><path d="M646 717v-3c1 1 1 2 2 3 0 2 1 3 1 5h1l1 8v1l-1 3c-4-4-2-12-4-17z" class="N"></path><path d="M629 681v-10h0c1 1 1 3 1 4l2 7 2 4h0c1 2 1 1 1 2s1 2 1 4c1 0 1 1 1 2l1 1c1 2 3 3 4 5-1 1-1 1-2 1s-2 1-3 0h0c-1 0-1-1-1-1-2-2-3-5-5-8l1-4c0-2-2-6-3-7z" class="P"></path><path d="M631 692l1-4 4 10 1 1v1h1s1 1 2 1c-1 0-2 1-3 0h0c-1 0-1-1-1-1-2-2-3-5-5-8z" class="e"></path><path d="M622 677h0c1-1 2-3 3-4v-2l1-1c0-1 0-2 1-3v-1-1l1-1v-2h0l1-1c0-1 0-1 1-2l1 4 1 7c-1 2-1 3-1 4l-1 1c0-1 0-3-1-4h0v10 1c-1 0-1-2-1-3s0-1-1-1l1 11c0 2 1 3 1 5l-1-1v-1c-1 0-1-1-1-2v-2c-1-2-1-4-1-6-2 2-1 6-1 9h0l-1-4c1-2 0-5 0-8h-1l-1-2z" class="X"></path><path d="M627 678h0c-1-2-1-3-1-5 1-1 1-2 2-4 1-1 1-5 2-7l1 1 1 7c-1 2-1 3-1 4l-1 1c0-1 0-3-1-4h0v10 1c-1 0-1-2-1-3s0-1-1-1z" class="B"></path><path d="M600 490c1-1 2-1 3 0s1 2 1 3c2 0 2-3 4-1l1-2c1 0 3 0 4 1l2 3-1 9v1l-2 10c-1 1-1 1-2 1v-6h-2v1l-5 11-2-1c1-1 1-2 1-4-1 2-1 3-2 4-1 2-4 4-4 6-2 2-4 4-7 5h-1c-2 2-1 5-2 7v-5l-2-1 1-2c-1-1-2-2-2-3l1-1c0-1-1-1-1-1l-1-1 1-1 1-2-3-1-2 1h0l2-3c1 0 2-2 3-2h2 0c2 1 3-1 5-1l1-1c3-2 6-10 8-14 1-2 2-3 2-5 0-1 0-2 1-3l-1-1c-1 1-2 1-3 1v1l1-3z" class="g"></path><path d="M591 519l1 3-1 2-3 2-1-1h0l-3 1c0-1-1-1-1-1 1 0 1-1 2-1 3-1 4-3 6-5z" class="R"></path><path d="M591 524l2 1-5 5-1-1 1-1c-1 1-2 1-3 2h0c-1-1-2-2-2-3l1-1 3-1h0l1 1 3-2z" class="J"></path><path d="M586 516c2 1 3-1 5-1-3 4-4 6-8 8l1-2-3-1-2 1h0l2-3c1 0 2-2 3-2h2 0z" class="h"></path><path d="M581 518c1 0 2-2 3-2h2c-2 2-3 3-5 4l-2 1h0l2-3z" class="D"></path><path d="M595 518l8-12c-2 6-5 15-10 19l-2-1 1-2-1-3 3-2h0l1 1z" class="Y"></path><path d="M591 519l3-2h0l1 1c-1 1-2 3-3 4l-1-3z" class="f"></path><defs><linearGradient id="BH" x1="597.878" y1="512.198" x2="616.573" y2="499.495" xlink:href="#B"><stop offset="0" stop-color="#7d7d7b"></stop><stop offset="1" stop-color="#a7a4a6"></stop></linearGradient></defs><path fill="url(#BH)" d="M609 490c1 0 3 0 4 1l2 3-1 9v1l-2 10c-1 1-1 1-2 1v-6h-2v1l-5 11-2-1c1-1 1-2 1-4h0l1-1 1-1c1-5 4-12 4-17-1-2 0-3 0-5l1-2z"></path><defs><linearGradient id="BI" x1="608.906" y1="513.803" x2="614.387" y2="497.295" xlink:href="#B"><stop offset="0" stop-color="#767575"></stop><stop offset="1" stop-color="#9e9e9d"></stop></linearGradient></defs><path fill="url(#BI)" d="M609 490c1 0 3 0 4 1l2 3-1 9v1l-2 10c-1 1-1 1-2 1v-6h-2 0c1-2 1-3 2-5 2-3 3-6 3-10l-1-1c-2 1-3 3-4 4-1-2 0-3 0-5l1-2z"></path><path d="M609 490c1 0 3 0 4 1l2 3-1 9c-1-3 0-7-1-9l-1-1c-2 1-3 3-4 4-1-2 0-3 0-5l1-2z" class="l"></path><path d="M561 516h2 0c2-1 3-2 4-2l1 1c1 1 1 1 3 1 0 2 1 3 2 4v2c0 2-2 5-2 6 0 2-1 3-2 5l-1-1-1 1c-1 1-1 2-3 2v5 1c-1 1-2 1-3 2 0 1-1 2-2 3v-1h-1l-1 2v1c-1 1-1 2-1 2h-3l-1 2v-2h-1c0 1 0 1-1 2l-1-1 1-2h-1c-1 1-1 2-2 3l-1-1 1-1v-7h0s-1 1-1 2h-1c0-2 0-4 1-6v-3s-1-1-1-2l2-4h1c1-1 3-4 3-6l10-8h0z" class="C"></path><path d="M554 543l-1 1h0l-2-3c1-5 2-12 5-15h1l-1 1c0 2-1 2-1 4s-1 3-1 6v6h0z" class="a"></path><path d="M561 516h2 0c2-1 3-2 4-2l1 1c1 1 1 1 3 1 0 2 1 3 2 4v2c0 2-2 5-2 6 0 2-1 3-2 5l-1-1-1 1c-1 1-1 2-3 2v5 1c-1 1-2 1-3 2 0 1-1 2-2 3v-1h-1l-1 2v1c-1 1-1 2-1 2h-3l-1 2v-2h-1c0 1 0 1-1 2l-1-1 1-2h-1c-1 1-1 2-2 3l-1-1 1-1 1-1 1-2c1 0 1 0 2-1l1-1v3h1v-2l1-3h0c1 0 1 1 2 1v-1h1c1-2 1-3 1-5 0-4 0-8 1-11 1-1 1-2 2-2-1 2-2 6-1 8 2-1 2-2 2-4 1-2 1-3 3-4v-1l-1 1c0-1 0-1-1-2h0c1-3 1-4 3-6h0c-1 0-1 0-2 1h0l-2 1h0c0-1 0-1-1-2v-1h0z" class="Q"></path><path d="M564 535c1-1 1-3 2-4h1c0-1 1-1 2-2l2-1c0 2-1 3-2 5l-1-1-1 1c-1 1-1 2-3 2z" class="V"></path><path d="M566 517l1 2c2 1 3 0 4 2 0 2 0 3-1 5l-1 1-1-2c-1 0-2 2-2 3-1 1-1 1-1 2l-1 1h0c0-2 0-4 1-5v-1-1l-1 1c0-1 0-1-1-2h0c1-3 1-4 3-6z" class="a"></path><path d="M553 546l2 1c2 0 2-3 3-4 1 1 1 1 2 1 0-1 0-2 1-3 1-2 1-4 1-6l1-1v5l1 1v1c-1 1-2 1-3 2 0 1-1 2-2 3v-1h-1l-1 2v1c-1 1-1 2-1 2h-3l-1 2v-2h-1c0 1 0 1-1 2l-1-1 1-2h-1c-1 1-1 2-2 3l-1-1 1-1 1-1 1-2c1 0 1 0 2-1l1-1v3h1v-2z" class="H"></path><path d="M467 275h6c1 1 3 1 4 3h0l-1 1-2 1h0l4 2v3 1 3 1c1 0 1 1 1 2v1h-1c1 1 1 1 1 2h-3c-1-1-2-1-4-1h0v2l1 1c1 0 2 1 3 2h-1c-1 0-1 0-2-1h-2c0 1-1 2-1 2-3 1-5 3-6 5v1c-2 0-2 0-3-1-2-1-4-3-6-4-2 0-3 0-4-1l2-3-2-2c-2 1-3 2-4 2l-1 1h-1c1-2 4-3 6-5h-6l3-2h-1 0v-2l4-2-1-1-2 1h-1l5-4 2-1c2 0 4 0 5-1s1-1 2-1l3-2v-2l3-1z" class="I"></path><path d="M464 294l2-2h0c1-1 2-1 3 0v2h-1l-1-1-1 1c-1 0-2 1-3 1 1 0 0 0 1-1h0zm4 3l1 2-3 2c-1-1-2-1-2-2l4-2z" class="X"></path><path d="M469 292l3 2h0c-2 2-1 3-3 5l-1-2c1-1 0-2 0-3h1v-2z" class="F"></path><path d="M469 299c2-2 1-3 3-5v2l1 1c1 0 2 1 3 2h-1c-1 0-1 0-2-1h-2c0 1-1 2-1 2-3 1-5 3-6 5l-2-1c1-2 3-3 4-3l3-2z" class="T"></path><path d="M466 286c1 1 2 1 2 1l10 2v1c1 0 1 1 1 2v1h-1c1 1 1 1 1 2h-3c-1-1-2-1-4-1l-3-2c-1-1-2-1-3 0h0l-2 2-1-1c-1 1-2 1-3 2-1 0-2 1-2 1h-1l3-3 1-1 2-2c-4 0-4 0-7 3v1l-3 3h0l-2-2c-2 1-3 2-4 2l-1 1h-1c1-2 4-3 6-5l4-2h1c1 0 1-1 2-2s2-1 3-2l1 1c1-1 3-1 4-2z" class="G"></path><path d="M456 293v1l-3 3h0l-2-2s1-1 2-1l3-1zm10-7c1 1 2 1 2 1 0 2 0 2 1 3-3-1-3-1-5 0v-2h-1-1 0c1-1 3-1 4-2z" class="X"></path><path d="M468 287l10 2v1c1 0 1 1 1 2v1h-1c-3 0-7-1-9-3-1-1-1-1-1-3z" class="p"></path><path d="M467 275h6c1 1 3 1 4 3h0l-1 1-2 1h0l4 2v3 1 3l-10-2s-1 0-2-1c-1 1-3 1-4 2l-1-1c-1 1-2 1-3 2s-1 2-2 2h-1l-4 2h-6l3-2h-1 0v-2l4-2-1-1-2 1h-1l5-4 2-1c2 0 4 0 5-1s1-1 2-1l3-2v-2l3-1z" class="P"></path><path d="M448 291c2 0 5-2 6-3 1 0 1-1 2-1 2 0 3-1 4-2l1-1c1 1 2 0 4 1l1 1c-1 1-3 1-4 2l-1-1c-1 1-2 1-3 2s-1 2-2 2h-1l-4 2h-6l3-2z" class="I"></path><path d="M474 280l4 2v3l-1 2c-3 0-7 0-10-1l1-1 1-1-2-1 1-2 6-1z" class="r"></path><path d="M467 275h6c1 1 3 1 4 3h0l-1 1-2 1h0l-6 1-1 2-1 1h-5 0c-1-1-1-1-1-2-2 1-3 3-5 4-2 0-5 3-8 5v-2l4-2-1-1-2 1h-1l5-4 2-1c2 0 4 0 5-1s1-1 2-1l3-2v-2l3-1z" class="B"></path><path d="M467 275h6c1 1 3 1 4 3h0l-1 1-2 1c-3-2-8 0-10-2v-2l3-1z" class="p"></path><path d="M817 229v1c0 1-1 1-1 2h1v-1h1v-1h2 0 1 3v1h0v2c-16 10-28 25-37 42l-5 10-1-1-2 2h0c-1 1-1 2-2 2l-45 106-1-1 30-72c9-21 16-42 28-60 4-8 9-15 16-21l4-4c1 0 7-6 8-7z" class="D"></path><path d="M817 229v1c0 1-1 1-1 2h1v-1h1v-1h2 0c-2 2-5 4-8 6-2 2-3 4-5 5h0c0-1 4-4 5-5h-1l-5 5-1-1 4-4c1 0 7-6 8-7z" class="Q"></path><path d="M787 275l-5 10-1-1-2 2h0c-1 1-1 2-2 2 1-4 3-8 6-12l1 1c1-1 2-1 3-2z" class="k"></path><defs><linearGradient id="BJ" x1="786.8" y1="250.881" x2="821.16" y2="253.616" xlink:href="#B"><stop offset="0" stop-color="#72706e"></stop><stop offset="1" stop-color="#8c8d8f"></stop></linearGradient></defs><path fill="url(#BJ)" d="M820 230h1 3v1h0v2c-16 10-28 25-37 42-1 1-2 1-3 2l-1-1c4-6 7-13 11-19s9-11 13-16c2-1 3-3 5-5 3-2 6-4 8-6z"></path><path d="M451 295l2 2-2 3c1 1 2 1 4 1 2 1 4 3 6 4 1 1 1 1 3 1l1 1c0 2-2 3-3 4v-1h-3l-1 1c-2 1-2 1-4 1 1-1 1-1 1-2h1c-3 1-5 3-7 4-1 1-3 4-4 4h-1c-1 0-2 1-2 2 0-2 2-4 3-6-3 3-6 6-9 8l-3 3v-3c-1 3-2 7-2 10h0-1c-1 2-1 4-1 6h-1c0-4 1-9 0-14l-1 4c-1 1-1 1-1 2v-4-1l-1-1c-1-1-2-1-3-1l2-7h0c0-1 1-2 2-3l2-3c3-3 5-7 8-9 2-1 4-1 5-2h1c2 0 2 0 4-1l1-1c1 0 2-1 4-2z" class="T"></path><path d="M443 309l2-1h0c1 1 2 0 2 0h3c-1 1-1 1-2 1-1 1-1 2-1 3v1c-1 0-2 1-3 1 1-1 1-1 1-2l-8 6 5-6-1-1 2-2z" class="B"></path><path d="M443 309h3c-1 1-2 2-4 3l-1-1 2-2z" class="M"></path><path d="M427 320h1 1l1-1v-1c2-1 5-4 7-6-1 2-2 5-3 7h-1l-3 6-1-1c0-1 1-2 1-3v-1c-2 2-2 3-2 4l-1 4c-1 1-1 1-1 2v-4l1-6z" class="W"></path><path d="M428 324c0-1 0-2 2-4v1c0 1-1 2-1 3l1 1 3-6h1c-1 1-1 2-1 3-1 3-2 7-2 10h0-1c-1 2-1 4-1 6h-1c0-4 1-9 0-14z" class="J"></path><path d="M442 303c2-1 5-1 7-1v1l-4 3h1 0c3 0 6 0 8 1-2 1-2 0-4 0 2 1 3 2 4 2 0 0-1 1-2 1-2 0-4 1-5 2 0-1 0-2 1-3 1 0 1 0 2-1h-3s-1 1-2 0h0l-2 1-2 2c0 1-1 1-2 2l-1-1 5-5-6 3-1 1-1-1 8-6-1-1z" class="I"></path><path d="M455 301c2 1 4 3 6 4 1 1 1 1 3 1l1 1c0 2-2 3-3 4v-1h-3l-1 1c-2 1-2 1-4 1 1-1 1-1 1-2h1l-2-1c-1 0-2-1-4-2 2 0 2 1 4 0-2-1-5-1-8-1h0-1l4-3c2 0 4-1 6-2z" class="r"></path><defs><linearGradient id="BK" x1="433.661" y1="303.221" x2="429.99" y2="324.283" xlink:href="#B"><stop offset="0" stop-color="#2e2d2d"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#BK)" d="M442 303l1 1-8 6c-3 1-5 3-6 6-1 1-1 3-2 4l-1 6v-1l-1-1c-1-1-2-1-3-1l2-7h0c0-1 1-2 2-3 2-1 5-4 7-6 3-2 6-3 9-4z"></path><path d="M451 295l2 2-2 3c1 1 2 1 4 1-2 1-4 2-6 2v-1c-2 0-5 0-7 1-3 1-6 2-9 4-2 2-5 5-7 6l2-3c3-3 5-7 8-9 2-1 4-1 5-2h1c2 0 2 0 4-1l1-1c1 0 2-1 4-2z" class="F"></path><path d="M557 548c0 1 0 2 1 3-2 3-1 6-1 8 0 1-1 2-1 3v2l-1 1v1 1c-1 1-1 3-2 4v1h1 1 0v5c-2 3-2 5-2 9 0 3-2 8-3 11-1 0-2 1-3 2s-1 2-1 3l-1 1-2-1-4 4v1l-1 4h-1l-2-3h-4-1c0-3 1-4 2-6 1 0 2-1 3-2 1-3 5-6 6-9 0-1 1-2 1-3h1v-3-1-1l2-8v-3c-1 0-1 1-1 1v-1l-1-2 1-2h-1c0-2 1-4 1-6h0c1-1 1-2 1-3s1-2 1-3l1-1c0-2 1-3 2-4l1 1c1-1 1-1 1-2h1v2l1-2h3s0-1 1-2z" class="g"></path><path d="M557 548c0 1 0 2 1 3-2 3-1 6-1 8-1 0-1 0-1-1v-3l-1 1-1 1-1-1v-1c1-1 2-3 3-4v-1s0-1 1-2z" class="Y"></path><path d="M549 551l1 1c1-1 1-1 1-2h1v2 1 3l-1 1-1-1c-1 1-2 2-2 3l-1 2c0-1-1-1-2-2 0-1 1-2 1-3l1-1c0-2 1-3 2-4z" class="b"></path><path d="M554 572h1 0v5c-2 3-2 5-2 9 0 3-2 8-3 11-1 0-2 1-3 2 2-6 3-11 4-17 1-4 3-7 3-10z" class="e"></path><path d="M548 559l3 1 1-1c1 0 1 1 2 2l1 1-6 20c-1 5-1 10-2 15l-2 2-4 1v-2-1c1-2 2-5 3-8 0-1 1 0 1-1 1 0 1-2 1-2 1-2 1-4 2-5l3-12c0-2 2-5 1-7h-3l-1 1-1-2h0l1-2z" class="V"></path><path d="M545 559c1 1 2 1 2 2h0l1 2 1-1h3c1 2-1 5-1 7l-3 12c-1 1-1 3-2 5 0 0 0 2-1 2 0 1-1 0-1 1l-3 8c-1 2-1 3-2 4-1 0-1 0-1-1h-2c1 1 1 2 2 2h1c1 1 0 3 0 4v1l-1 4h-1l-2-3h-4-1c0-3 1-4 2-6 1 0 2-1 3-2 1-3 5-6 6-9 0-1 1-2 1-3h1v-3-1-1l2-8v-3c-1 0-1 1-1 1v-1l-1-2 1-2h-1c0-2 1-4 1-6h0c1-1 1-2 1-3z" class="D"></path><path d="M545 559c1 1 2 1 2 2h0c0 2-2 7-3 7h-1c0-2 1-4 1-6h0c1-1 1-2 1-3z" class="S"></path><path d="M544 572c1-1 1-1 2-1h0v-2l1-1h0c-1 4-1 9-2 13 0 2-1 4-2 7v-3-1-1l2-8v-3c-1 0-1 1-1 1v-1z" class="L"></path><path d="M532 602c3 1 4 0 5 2 0 1 0 2-1 4l-2-2s-1 1-1 2h-2-1c0-3 1-4 2-6z" class="q"></path><path d="M573 179c2 0 3 0 4 1h0c-5 2-11 1-15 3-2 0-3 1-4 2-2 0-4 1-5 3h1 0c2 0 4-1 6 0s5 0 7 0c4 1 10 5 14 7l-2 1c-1 0-2-1-2-1l-2-1c-1 0-2-1-2-1l-1-1-3 1h-2c1 2 3 2 4 4l-5-2c-2 0-3 0-4 1h-2l1 1c2 0 3 1 5 2h0-1c1 1 1 1 2 3l3 1 1 3c-1 0-1 0-2 1-2 1-4 1-7 1-3 1-5 2-9 2h-1 0c-2 1-7 2-8 4v1l-2 2c0-2 1-3 1-5l-1 1c-1-2 0-1-1-2l-2 2-1-2v-3c1-3 2-4 4-7h-1v-2c1-2 2-2 3-3l-1-1v-2l-1 1-2 1v-1l2-2 1-1h-2l-1-1c1 0 1-1 2-2h0l1 1c1 0 3-2 4-3l1 1 3-2c2-1 4-2 7-3h2l1-1c4-1 8-1 12-2z" class="d"></path><path d="M554 191l1-1c1-1 2-1 3-1 1 1 1 1 2 0h2c0 1-1 1-2 2h-6z" class="i"></path><path d="M554 191h6l-2 2h2l-4 1-1-1h-3 0c0-1 1-1 2-2h0z" class="S"></path><path d="M542 192h2c1 0 1 0 3-1h0l-1 1 1 1h1c-1 1-2 2-4 3l-1-1v-2l-1 1-2 1v-1l2-2z" class="R"></path><path d="M565 194l-4-3c2 0 5-1 7 0v1l1 1h-2c1 2 3 2 4 4l-5-2-1-1z" class="L"></path><path d="M542 201c1-2 2-2 4-3l2 2-2 1c0 1 0 1 1 2 0-1 2-2 3-2 0 1 0 0 1 1l1-1h1c0 1-1 2-1 2v1l2-1c1 0 1 0 2 1 1 0 1 0 2 1v3c-2 0-3 1-5 2h-1 0c-2 1-7 2-8 4v1l-2 2c0-2 1-3 1-5l-1 1c-1-2 0-1-1-2l-2 2-1-2v-3c1-3 2-4 4-7h0z" class="N"></path><path d="M542 201l1 1c1 2 0 4-1 6s-2 2-4 3v-3c1-3 2-4 4-7h0z" class="a"></path><path d="M547 203c0-1 2-2 3-2 0 1 0 0 1 1l1-1h1c0 1-1 2-1 2v1 1h-1c-2 0-3 1-3 2h-1c-1 1-1 2-2 3l-1-1-1 1-1-1c1-1 3-2 3-4 1 0 1-1 2-2z" class="C"></path><path d="M552 204l2-1c1 0 1 0 2 1 1 0 1 0 2 1v3c-2 0-3 1-5 2h-1 0c-3-1-4 0-6 1l-1-1c1-1 1-2 2-3h1c0-1 1-2 3-2h1v-1z" class="a"></path><path d="M547 207c3-1 5-1 8-1-1 1 0 1-1 1l-2 2v1h0c-3-1-4 0-6 1l-1-1c1-1 1-2 2-3z" class="n"></path><path d="M560 193c1 1 3 1 5 1l1 1c-2 0-3 0-4 1h-2l1 1c2 0 3 1 5 2h0-1c1 1 1 1 2 3l3 1 1 3c-1 0-1 0-2 1-2 1-4 1-7 1-3 1-5 2-9 2 2-1 3-2 5-2v-3c-1-1-1-1-2-1-1-1-1-1-2-1l-2 1v-1s1-1 1-2h-1l-1-1h1c-1-1-1 0-3 0l1-2c-1-1-1 0-1-1 1-2 3-2 5-3h2l4-1z" class="l"></path><path d="M554 194h2c1 2 2 1 3 3l-1 1h-2c-1-1-2 0-3 0h-1-1c1-1 2-3 3-4z" class="g"></path><path d="M553 201c0-1 1-1 1-1v2h1l1-1v1h5c1 1 0 1 2 2h3v1c-2 2-6 2-8 3v-3c-1-1-1-1-2-1-1-1-1-1-2-1l-2 1v-1s1-1 1-2z" class="Q"></path><path d="M560 193c1 1 3 1 5 1l1 1c-2 0-3 0-4 1h-2l1 1c2 0 3 1 5 2h0-1c1 1 1 1 2 3-2 0-3-1-5-2h-4v-1l2-1h-2l1-1c-1-2-2-1-3-3l4-1z" class="Z"></path><defs><linearGradient id="BL" x1="565.445" y1="712.771" x2="547.06" y2="712.718" xlink:href="#B"><stop offset="0" stop-color="#656565"></stop><stop offset="1" stop-color="#828180"></stop></linearGradient></defs><path fill="url(#BL)" d="M584 653l2 1-18 47-2 4c0 2-2 6-1 8 0 2-1 4-1 6l-1 1-11 24-1-2 3-6h-1-1 0l-6-3c0-1 2-3 2-5l7-17c3-7 6-15 10-22l12-30 6-2v-1c1-1 1-1 1-2v-1z"></path><path d="M566 705c0 2-2 6-1 8 0 2-1 4-1 6l-1 1-11 24-1-2 3-6c0-2 2-6 3-8l9-23z" class="V"></path><path d="M583 657l1 1c-3 8-7 16-10 24-2 4-5 6-8 7h-1l12-30 6-2z" class="o"></path><path d="M415 284h1l3-1 2 1-1 1 1 1c-1 1-3 2-3 4v1l-2 1v1h-2v1l1 1h3l2 1c-1 1-2 2-3 2l-3 1c2 1 3 0 4 1l-4 3c-1 0-1 0-2 1 0 1-2 2-3 4l-5 6h0c-2 4-4 8-5 12h1l1-1 2-1v1h0c0 1-1 2 0 3 1 0 2-1 3-1l-5 5c-6 6-8 18-8 26v1c-1-2-2-5-2-8-2-8-2-18-1-26 1-11 4-22 13-29 3-1 6-5 9-6v-1h-1v-1c1-1 2-4 4-4z" class="i"></path><defs><linearGradient id="BM" x1="410.09" y1="302.144" x2="397.831" y2="305.022" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#555454"></stop></linearGradient></defs><path fill="url(#BM)" d="M415 284h1l3-1 2 1-1 1 1 1c-1 1-3 2-3 4v1l-2 1v1h-2v1l1 1h3l2 1c-1 1-2 2-3 2l-3 1h-2c-1 1-2 1-2 2h-3c-3 3-5 7-8 11v1c-2 5-4 10-5 15 0 1 0 1-1 2 1-11 5-22 11-31 0-1 2-2 2-3 2-2 5-4 6-6v-1h-1v-1c1-1 2-4 4-4z"></path><path d="M418 295l2 1c-1 1-2 2-3 2l-3 1h-2c-1 1-2 1-2 2h-3c3-3 5-4 8-6h3z" class="K"></path><path d="M415 284h1l3-1 2 1-1 1 1 1c-1 1-3 2-3 4-5 2-10 6-14 9 0-1 2-2 2-3 2-2 5-4 6-6v-1h-1v-1c1-1 2-4 4-4z" class="R"></path><path d="M415 284h1l3-1 2 1-1 1c-2 1-5 2-8 4h-1v-1c1-1 2-4 4-4z" class="K"></path><path d="M407 301h3c0-1 1-1 2-2h2c2 1 3 0 4 1l-4 3c-1 0-1 0-2 1 0 1-2 2-3 4l-5 6h0c-2 4-4 8-5 12h1l-5 8c0 1-1 2-1 3h-1l1-2c-1-2-1-3-1-5 1-1 1-1 1-2 1-5 3-10 5-15v-1c3-4 5-8 8-11z" class="Y"></path><path d="M396 330c2-2 3-7 4-9s2-7 4-7c-2 4-4 8-5 12h1l-5 8c0-1 0-3 1-4z" class="l"></path><path d="M414 299c2 1 3 0 4 1l-4 3c-1 0-1 0-2 1l-2 1h-1c-2 0-5 4-7 6h0c2-5 8-9 12-12z" class="O"></path><path d="M399 313c0 2 0 3-1 4-1 3-2 9-2 13-1 1-1 3-1 4s-1 2-1 3h-1l1-2c-1-2-1-3-1-5 1-1 1-1 1-2 1-5 3-10 5-15z" class="d"></path><path d="M618 461v-4c-1-2-1-5-1-7l3-6v9c1 2 3 5 4 7-1 4 0 7 2 10 2 6 4 11 5 17 0 2 1 4 0 7 0-2-1-3-1-4v14c0 6-2 12-5 17h-1v-1c-2 2-3 6-5 9l-3 9c0 3 0 15-1 16-1-2-2-5-2-7-1-3 0-6-1-9v-1h-1-1l-4 5h0c0-3 3-6 5-9 2-4 5-9 5-13l-1-1v3l-1 1h0v-3c0-1 1-2 1-2 2-4 3-9 4-14l1-10 1-6c1-1 1-2 2-2v-1c1-7-2-18-5-24z" class="T"></path><path d="M619 529v-2l1-1v-1c1-2 2-3 2-5 1-1 1-2 2-3 4-3 0-15 3-18v9c2-2 1-5 1-7h1v2l1 1c0 6-2 12-5 17h-1v-1c-2 2-3 6-5 9z" class="B"></path><path d="M620 494l1-6c1-1 1-2 2-2 1 4 2 8 2 12-1 1-1 2-1 4 0 1-2 2-2 3s1 3-1 4c-1-2-1-3-2-5l1-10z" class="J"></path><path d="M624 502c0 3 1 8-1 10 0-1 0-2-1-2h0c-1 1-1 2-1 4-1 1-2 3-2 5 0 1-1 2-1 2-2 6-4 11-8 16l-4 5h0c0-3 3-6 5-9 2-4 5-9 5-13l-1-1v3l-1 1h0v-3c0-1 1-2 1-2 2-4 3-9 4-14 1 2 1 3 2 5 2-1 1-3 1-4s2-2 2-3z" class="O"></path><path d="M447 287h1l2-1 1 1-4 2v2h0 1l-3 2h6c-2 2-5 3-6 5h1c-2 1-2 1-4 1h-1c-1 1-3 1-5 2-3 2-5 6-8 9l-2 3c-1 1-2 2-2 3h0l-3 2c-4 3-9 5-12 9h0-1-2c-1 0-2 1-3 1-1-1 0-2 0-3h0v-1l-2 1-1 1h-1c1-4 3-8 5-12h0l5-6c1-2 3-3 3-4 1-1 1-1 2-1l4-3c-1-1-2 0-4-1l3-1c1 0 2-1 3-2l-2-1h-3l-1-1v-1h2v-1l2-1 1 1h0l6 2h2c1-1 4-1 6-1h3l4-2c3-1 5-3 7-4z" class="R"></path><path d="M411 317c3 0 4-3 7-2l-2 2c-1 1-3 2-4 3-2 0-5 4-7 6 1-4 2-6 5-9h0l1-1v1z" class="c"></path><path d="M419 311c1-1 1 0 1-1 2-1 5-4 7-4-1 1-2 2-2 3 1 0 1-1 2 0 1 0 1 0 1 1l-2 3c-1 1-2 2-2 3h0l-3 2h-1c-1 0-2 1-3 1l-1 1v-1-2l2-2c-3-1-4 2-7 2 2-2 4-5 6-5 0-1 1 0 2-1z" class="K"></path><path d="M418 315l1-1v1l4-3c1 2 1 2 1 4h0l-3 2h-1c-1 0-2 1-3 1l-1 1v-1-2l2-2z" class="Z"></path><defs><linearGradient id="BN" x1="418.181" y1="304.925" x2="413.422" y2="315.838" xlink:href="#B"><stop offset="0" stop-color="#4a4b4a"></stop><stop offset="1" stop-color="#646262"></stop></linearGradient></defs><path fill="url(#BN)" d="M414 303l1 1s1 0 2-1v1h0c-1 1-1 1-1 2 1-1 4-3 5-3l1 1h0c-1 1-1 1-1 2l-3 4 1 1c-1 1-2 0-2 1-2 0-4 3-6 5v-1l-1 1h0l2-4-1-1-5 6h0l2-4-1-1c-3 4-6 8-6 12l-1 1h-1c1-4 3-8 5-12h0l5-6c1-2 3-3 3-4 1-1 1-1 2-1z"></path><path d="M413 306c0 3-2 4-3 6l-2 2-1-1c1-3 3-5 6-7z" class="Z"></path><path d="M413 306l2-1h0c0 1-1 1-1 2v1l1-1c1-1 2-1 4-2h0c-1 2-2 4-4 6l-1-1c-2 0-2 1-3 2h0l-5 6h0l2-4 2-2c1-2 3-3 3-6z" class="K"></path><path d="M418 295h3c0 1 1 2 2 2 1 1 3 1 5 1s5 0 7 1c2 0 5-1 6 0-1 1-3 1-5 2-3 2-5 6-8 9 0-1 0-1-1-1-1-1-1 0-2 0 0-1 1-2 2-3-2 0-5 3-7 4 0 1 0 0-1 1l-1-1 3-4c0-1 0-1 1-2h0l-1-1c-1 0-4 2-5 3 0-1 0-1 1-2h0v-1c-1 1-2 1-2 1l-1-1 4-3c-1-1-2 0-4-1l3-1c1 0 2-1 3-2l-2-1z" class="B"></path><path d="M414 299l3-1h1 2 3c-1 1-2 1-2 3h1l1-1 1 1h-1 1l1 1c-1 1-2 1-3 2h0l-1-1c-1 0-4 2-5 3 0-1 0-1 1-2h0v-1c-1 1-2 1-2 1l-1-1 4-3c-1-1-2 0-4-1z" class="P"></path><path d="M427 301c1-1 2-1 4-1l1 1c-1 0 0 0-1 1 1 0 4-2 5-1-3 2-5 6-8 9 0-1 0-1-1-1-1-1-1 0-2 0 0-1 1-2 2-3-2 0-5 3-7 4 0 1 0 0-1 1l-1-1 3-4c0-1 0-1 1-2s2-1 3-2l2-1z" class="T"></path><path d="M427 301v1 3-1c1 0 2-1 4-2v1c-1 0-3 3-4 3-2 0-5 3-7 4 0 1 0 0-1 1l-1-1 3-4c0-1 0-1 1-2s2-1 3-2l2-1z" class="k"></path><path d="M427 301v1c-2 2-4 3-6 4 0-1 0-1 1-2s2-1 3-2l2-1z" class="W"></path><path d="M447 287h1l2-1 1 1-4 2v2h0 1l-3 2h6c-2 2-5 3-6 5h1c-2 1-2 1-4 1h-1c-1-1-4 0-6 0-2-1-5-1-7-1s-4 0-5-1c-1 0-2-1-2-2h-3-3l-1-1v-1h2v-1l2-1 1 1h0l6 2h2c1-1 4-1 6-1h3l4-2c3-1 5-3 7-4z" class="X"></path><path d="M447 287h1l2-1 1 1-4 2v2h0 1l-3 2c-7 2-13 3-20 1h2c1-1 4-1 6-1h3l4-2c3-1 5-3 7-4z" class="J"></path><path d="M447 287h1l2-1 1 1-4 2v2h0c-3 2-6 2-10 2h-1l4-2c3-1 5-3 7-4z" class="M"></path><defs><linearGradient id="BO" x1="408.082" y1="773.095" x2="432.468" y2="749.881" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient></defs><path fill="url(#BO)" d="M405 710c2 4 3 7 5 11l5 13 7 16c3 6 7 12 9 19 4 8 9 17 11 26h-1l2 4-1 1c-1 0-1 0-2 1s0 0 0 1l-1 1v1l-1 1c0-1 0-1-1-1 0-1-1-2-1-2l-31-72c1-4-3-7-3-10l1-1 1 2h1c0 1 1 1 1 3h0c0 1 1 1 1 2v1c0-2 1-4 0-6-1-3-2-7-2-11z"></path><path d="M415 734l7 16c3 6 7 12 9 19l-2-2v-1l-1-1v-2c-1-1-1 0-1-1-1-3-4-6-6-9-1-1-1-4-2-5l-1-1c-1-1-1-3-1-4-1-3-3-6-2-9z" class="S"></path><path d="M537 252v-6c0-1-1-2 0-3 0-1 0-3 1-4l1 1c-1 1-1 1-1 3l1 1v5l1-1h1v2 3 1 1c1 4 3 7 5 10 1 1 1 3 1 5 2 2 2 5 3 8 0 2 0 2 1 4v3c-1 1-1 1-1 2h0l-1 6v1c-2 6-4 10-8 14l-1 1c-2-1-3-2-5-2h0v-1l1-1h-1c-2 0-2-1-2-2h-2 0l-2-1v-3c4-5 6-9 5-16-1 0-1 1-2 1h-1v-2l1-2-1-2h-2l1-1h0c1-1 1-1 1-2 1-1 2-1 3-2 1 0 2-3 3-5h0v-9c0-1 0-2-1-3h1v-3-1z" class="H"></path><path d="M543 294l4-2 1 1v1h-1c-1 0 0 0-1 1h-3 0v-1zm-6-11l2 2c0 1 0 0-1 1 0 1 0 2-1 2h-1c-1-2 0-3 0-5h1z" class="U"></path><path d="M538 292l3 3h2 0l-2 2h-1c-1-1-1 0-2 0h-2v-1l1-1h1v-3z" class="L"></path><path d="M536 305h1c0-1 0-1 1-1-1-1-2-1-3-2v-1c1 0 1 0 2 1s3 2 4 3c-1 1 0 1-1 2 0 0 1 0 1 1l-1 1c-2-1-3-2-5-2h0v-1l1-1z" class="V"></path><path d="M535 276c1-2 3-5 5-7v1h2l1 4h1v-2c1 1 1 2 1 4s1 4 1 6c1 1 1 1 1 3l2 1v1c-1 0-1 1-1 2-1 1-1 2-2 2l-3 1c-1 0 0 0-1 1l1 1v1h-2l-3-3 1-2c0-1 1-3 2-5h-2l-2-2h-1v-2c-1-2-1-2-1-4v-1z" class="D"></path><path d="M535 276c1-2 3-5 5-7v1h2l1 4h1v-2c1 1 1 2 1 4s1 4 1 6c-1-1-2-2-2-3-1-2-1-3-2-5h-2c1 3 1 5 1 8v1l-1-1-3 1h0-1v-2c-1-2-1-2-1-4v-1z" class="L"></path><path d="M540 282c-1-1-2-2-3-2h-1v-1h2c1-1 1-3 2-5h0c1 3 1 5 1 8v1l-1-1z" class="C"></path><path d="M537 252v-6c0-1-1-2 0-3 0-1 0-3 1-4l1 1c-1 1-1 1-1 3l1 1v5l1-1h1v2 3 1 1c1 4 3 7 5 10 1 1 1 3 1 5 2 2 2 5 3 8 0 2 0 2 1 4v3c-1 1-1 1-1 2h0v-3l-2-5c-1-2-1-4-1-6l-2 3c0-2 0-3-1-4v2h-1l-1-4h-2v-1c-2 2-4 5-5 7v1c-1 2-1 4-1 6-1 0-1 1-2 1h-1v-2l1-2-1-2h-2l1-1h0c1-1 1-1 1-2 1-1 2-1 3-2 1 0 2-3 3-5h0v-9c0-1 0-2-1-3h1v-3-1z" class="Y"></path><path d="M539 249l1-1h1v2 3l-1 1-1-1v-4z" class="Z"></path><path d="M537 253l2 1c0 1 1 3 2 4h0c-2 2-2 3-2 5h0l-2-4c0-1 0-2-1-3h1v-3z" class="K"></path><path d="M542 270c1 0 1-1 1-1 0-1 2-1 3-1 1 1 1 3 1 5l-2 3c0-2 0-3-1-4v2h-1l-1-4z" class="g"></path><path d="M530 277h2v-1c1 0 2-1 3-1v1 1c-1 2-1 4-1 6-1 0-1 1-2 1h-1v-2l1-2-1-2h-2l1-1h0z" class="I"></path><path d="M539 263h0c0-2 0-3 2-5 1 3 3 5 4 8h0l-2-2v1c0 1-1 2-1 3h0l-1-1-1 1c-1-2 0-3-1-5z" class="W"></path><path d="M589 317l1-2 1-1 1 1h0c0-1-1-3-2-4v-2h1l3 2v-1c1 0 2 1 3 2l1-1v-1c1 0 2 1 3 3 1 1 1 2 2 3l1 1c1 0 2 0 3-1v-2l1-1-1-2c1 0 2 1 3 2v1 1l1 2c-1 1-1 0-2 0s-1 1-2 1c-1 1-2 0-3 0v2c0 2 0 4 1 6 0 3 1 8 0 11-1 1-1 0-2 1-1 4-4 6-6 10l-2-1c-1 4-3 7-4 10-2 3-3 5-4 8-1 2-1 2-3 2v-1h0v-1-10-5l1-10h1c0-3-2-5-2-7l-1-3v-2h0l1 1h0l-1-1v-2c-1-2-1-4-1-7h1l1 1c2 1 2 1 3 2 0-2-1-2-2-3l1-2h2l-1-1 1-1 1 1v1z" class="c"></path><path d="M593 331l1-2c1-1 1 0 2 0v1 5c-1 1-1 0-1 1v3c0 1-1 1-1 2-1 0-1 2-1 3v-13z" class="R"></path><path d="M589 340c1 1 1 2 1 4h1v-3h1v10c-1 2-2 4-1 6h0c-2 3-3 5-4 8h0c1-3 0-5 1-8v-8c2-2 1-7 1-9z" class="h"></path><defs><linearGradient id="BP" x1="582.031" y1="353.212" x2="587.469" y2="364.288" xlink:href="#B"><stop offset="0" stop-color="#adacac"></stop><stop offset="1" stop-color="#cecccd"></stop></linearGradient></defs><path fill="url(#BP)" d="M584 350l1-10h1c0 2 0 4 1 6v-3c1 2 1 4 1 6v8c-1 3 0 5-1 8h0c-1 2-1 2-3 2v-1h0v-1-10-5z"></path><path d="M587 343c1 2 1 4 1 6v8c-2-2-1-3-1-5 1-2 0-5 0-6v-3z" class="V"></path><path d="M583 328h0l1 1h0l-1-1v-2c-1-2-1-4-1-7h1l1 1c2 1 2 1 3 2 0-2-1-2-2-3l1-2h2l-1-1 1-1 1 1v1c2 1 5 3 5 5l1 3 1 4c-1 0-1-1-2 0l-1 2h0-1l-1-3h0v1c0 2 1 5 0 7h0l-1-5c-2 3-1 6-1 9 0 2 1 7-1 9 0-2 0-4-1-6v3c-1-2-1-4-1-6 0-3-2-5-2-7l-1-3v-2z" class="G"></path><path d="M584 333c2-2 0-5 1-7h0l1 2v-4h1c1 1 1 1 1 3-1 1-1 4-1 6 0 1-1 2-1 3l1 1c0 2-1 4 0 6v3c-1-2-1-4-1-6 0-3-2-5-2-7z" class="O"></path><path d="M588 327l1 1c0 1 1 3 1 3-2 3-1 6-1 9 0 2 1 7-1 9 0-2 0-4-1-6s0-4 0-6l-1-1c0-1 1-2 1-3 0-2 0-5 1-6z" class="e"></path><path d="M589 316v1c2 1 5 3 5 5l1 3 1 4c-1 0-1-1-2 0l-1 2h0-1l-1-3h0v1c0 2 1 5 0 7h0l-1-5s-1-2-1-3l1 1c0-4-2-6-3-9 2 2 3 4 5 6l-3-8v-2z" class="O"></path><path d="M593 331v-2c-1-3-1-4 1-7l1 3 1 4c-1 0-1-1-2 0l-1 2h0z" class="Z"></path><path d="M589 317l1-2 1-1 1 1h0c0-1-1-3-2-4v-2h1l3 2v-1c1 0 2 1 3 2l1-1v-1c1 0 2 1 3 3 1 1 1 2 2 3l1 1c1 0 2 0 3-1v-2l1-1-1-2c1 0 2 1 3 2v1 1l1 2c-1 1-1 0-2 0s-1 1-2 1c-1 1-2 0-3 0v2c0 2 0 4 1 6 0 3 1 8 0 11-1 1-1 0-2 1-1 4-4 6-6 10l-2-1c-1 4-3 7-4 10h0c-1-2 0-4 1-6s1-5 1-7c0-1 0-3 1-3 0-1 1-1 1-2v-3c0-1 0 0 1-1v-5-1l-1-4-1-3c0-2-3-4-5-5z" class="l"></path><path d="M596 330v-1-2-7-1-1c2 1 3 4 3 6v2 7c-1-2-1-3-2-5v8l-1-1v-5z" class="b"></path><path d="M603 330l1-4h1c0 3 1 8 0 11-1 1-1 0-2 1-1 4-4 6-6 10l-2-1c1-2 0-5 1-7v-1c0 1 1 2 1 3 1-2 0-6 1-7 1 2 1 2 1 5l1-1v-1c1-2 1-6 2-7 0 1 0 3 1 4h0c1-2 1-3 0-5z" class="H"></path><path d="M589 317l1-2 1-1 1 1h0c0-1-1-3-2-4v-2h1l3 2v-1c1 0 2 1 3 2l1-1v-1c1 0 2 1 3 3 1 1 1 2 2 3l1 1c1 0 2 0 3-1v-2l1-1-1-2c1 0 2 1 3 2v1 1l1 2c-1 1-1 0-2 0s-1 1-2 1c-1 1-2 0-3 0v2c0 2 0 4 1 6h-1l-1 4c0-2 0-3-1-4v3l-3-5c0-2-1-5-3-6v1 1 7 2 1-1l-1-4-1-3c0-2-3-4-5-5z" class="K"></path><path d="M589 317l1-2 1-1 1 1h0c0-1-1-3-2-4v-2h1l3 2v-1c1 0 2 1 3 2l1-1c2 2 4 4 4 7l-1-1v1 2h0c-2-1-2-4-4-6v1h-1l-2-2v1c-1 1-1 1 0 2 1 3 1 4 1 6v1 2l-1-3c0-2-3-4-5-5z" class="P"></path><path d="M720 309l1 1 8-5c0-2 1-3 1-4 0-2 1-2 2-4h0c-1 3-3 8-3 11h0l-14 34-11 26-6 14-4 10h-1c-2 6-5 14-8 19l-2 1c-2 3-3 3-6 4 1-2 2-4 2-7h0l10-27 31-73z" class="f"></path><defs><linearGradient id="BQ" x1="696.943" y1="388.937" x2="672.344" y2="409.404" xlink:href="#B"><stop offset="0" stop-color="#868787"></stop><stop offset="1" stop-color="#c0bdbc"></stop></linearGradient></defs><path fill="url(#BQ)" d="M689 382c3 1 4 3 6 4v-1c1-1 1-2 1-2 1-1 1-2 1-3h0v-1l1-1 1-2c0-1 1-1 1-2l1-1 2-6 1 1-6 14-4 10h-1c-2 6-5 14-8 19l-2 1c-2 3-3 3-6 4 1-2 2-4 2-7h0l10-27z"></path><path d="M631 525h0c2-1 3-1 4-2 0-1 0-1 2-2h1v1l-2-1v1c1 1 1 1 0 2 1 1 1 2 0 4l-33 83h1l-1 3c-2 3-3 6-4 9l-13 31-2-1v1c0 1 0 1-1 2v1l-6 2c0-3 2-5 3-8 1-2 2-4 3-7l9-22c0-3 2-6 2-8l7-17 18-44 12-28z" class="d"></path><path d="M592 622v4c-2 5-5 11-6 17l4-4c0-1 1-1 2-2-1 4-4 6-6 9-2 0-2 0-3-1v-1l9-22z" class="R"></path><path d="M603 611h1l-1 3c-2 3-3 6-4 9l-13 31-2-1v1c0 1 0 1-1 2v1l-6 2c0-3 2-5 3-8 1-2 2-4 3-7v1c1 1 1 1 3 1 2-3 5-5 6-9 5-8 8-18 11-26z" class="k"></path><path d="M584 654h-1c0 1-1 1-2 1h0c1-4 4-7 6-10l-3 7v1 1z" class="B"></path><path d="M123 188v-3c1 3 4 5 6 7 1 1 2 1 3 1 0 3 0 4-3 6l-2 1h6 0l2-1v-2c1 4-1 14 1 15h2l1 1c2-1 4-1 7 0l-2 1h6c4 1 8 1 12 2v1h1c1 0 3 0 4 1 1 0 3 1 4 2 12 7 23 11 33 21v2l5 8c-3-2-5-2-6-5-3-1-5-1-7-2-1 0-1 0-2 1 1 1 3 2 4 3l-1 1c-11-11-22-18-37-22-12-3-24-2-36-1v-9c-2-3-1-8-1-10v-19z" class="H"></path><path d="M135 197c1 4-1 14 1 15h2l1 1c2-1 4-1 7 0l-2 1h6c4 1 8 1 12 2v1h1c1 0 3 0 4 1 1 0 3 1 4 2-3 0-8-2-11-3s-6-1-9-2c-9-2-19 1-27 2l-1-10 9-1 1 8h1l1-1v-14-2z" class="S"></path><path d="M139 213c2-1 4-1 7 0l-2 1h-8v-1h3z" class="H"></path><path d="M123 188v-3c1 3 4 5 6 7 1 1 2 1 3 1 0 3 0 4-3 6l-2 1h6 0l2-1v14l-1 1h-1l-1-8-9 1 1 10c-2-3-1-8-1-10v-19z" class="M"></path><path d="M123 188v-3c1 3 4 5 6 7 1 1 2 1 3 1 0 3 0 4-3 6l-2 1c-1 0-2 0-3 1-1-1-1-11-1-13h0z" class="J"></path><defs><linearGradient id="BR" x1="182.985" y1="240.388" x2="186.222" y2="230.965" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#908f8e"></stop></linearGradient></defs><path fill="url(#BR)" d="M160 227c5-2 19-1 24 1 1 0 2 1 3 1l1 1c3 3 6 5 10 7l6 6 5 8c-3-2-5-2-6-5-3-1-5-1-7-2-1 0-1 0-2 1 1 1 3 2 4 3l-1 1c-11-11-22-18-37-22z"></path><defs><linearGradient id="BS" x1="616.578" y1="322.068" x2="640.922" y2="328.432" xlink:href="#B"><stop offset="0" stop-color="#b6b5b5"></stop><stop offset="1" stop-color="#e6e5e5"></stop></linearGradient></defs><path fill="url(#BS)" d="M613 292c9 2 15 7 20 15 5 10 5 24 4 35 0 5-1 9-2 13l-1 5c0-8-1-16-5-24-1-2-4-4-6-6-5-5-8-7-14-8-2-1-3-1-5-2v-2c1 0 2 1 3 0 1 0 1-1 2-1s1 1 2 0l-1-2v-1-1c-1-1-2-2-3-2l1 2-1 1v2c-1 1-2 1-3 1l-1-1c-1-1-1-2-2-3-1-2-2-3-3-3-2-1-3-2-4-3-2-1-3-2-5-3h2v-1l-1-2 1-2h0l-1-1h4c1 0 2 0 3-1h4v-2l-5-1 1-1h3c3-1 6-1 9-1l3 1 1-1z"></path><path d="M618 297v-1c4 3 7 5 10 9l-1 1-4-4c-2-1-3-3-5-5z" class="Y"></path><defs><linearGradient id="BT" x1="626.28" y1="304.542" x2="630.339" y2="320.299" xlink:href="#B"><stop offset="0" stop-color="#858483"></stop><stop offset="1" stop-color="#a09f9f"></stop></linearGradient></defs><path fill="url(#BT)" d="M621 303l2-1 4 4 1-1c5 8 6 19 5 28 0-4 0-8-2-11-1 1-1 1-1 2l-1 1v-1h0c-1-4-3-11-5-14l-1-2c2 0 3 3 4 4-1-3-3-6-6-9z"></path><path d="M613 309h1v-1l1-1c1 0 2 1 3 1h2 0c1 1 2 2 4 2 2 3 4 10 5 14h0v1l1-1c1 3 1 7 1 10h0 0l-2 2c-1-2-4-4-6-6-5-5-8-7-14-8-2-1-3-1-5-2v-2c1 0 2 1 3 0 1 0 1-1 2-1s1 1 2 0l-1-2v-1-1c-1-1-2-2-3-2h-1v-1h1 1l1-1 2 1c0-1-1-2-1-3l2 2h1z" class="Y"></path><path d="M621 314c0-1 1-1 2-1 1 1 3 4 4 6h-3 0c-1-2-3-4-3-5z" class="d"></path><path d="M610 307l2 2h1l2 4-1 1c-1-1-2-2-3-2l1 3-2-1v-1c-1-1-2-2-3-2h-1v-1h1 1l1-1 2 1c0-1-1-2-1-3z" class="O"></path><path d="M622 323c-1-2-5-8-5-9h1v-2c2 0 2 0 3 2 0 1 2 3 3 5h-1l-1 4z" class="i"></path><path d="M609 322c1-1 3-1 5-2l1-1 2 1v-2l3 3c1 1 3 4 4 5 2 1 2 2 3 4l1 1h-1c-1 0-1-1-2-2-1 0-1 0-2 1-5-5-8-7-14-8z" class="D"></path><path d="M624 319h0 3l2 5v1l1-1c1 3 1 7 1 10h0 0l-2 2c-1-2-4-4-6-6 1-1 1-1 2-1 1 1 1 2 2 2h1l-1-1c-1-2-1-3-3-4l-2-3 1-4h1z" class="N"></path><path d="M627 330v-1l2 3c1 0 0-1 0-2v-1-1l2 6-2 2c-1-2-4-4-6-6 1-1 1-1 2-1 1 1 1 2 2 2h1l-1-1z" class="C"></path><path d="M624 319h3l2 5v1l1-1c1 3 1 7 1 10h0 0l-2-6v-2c-3-2-4-4-5-7z" class="i"></path><path d="M600 293c3-1 6-1 9-1l3 1c2 0 5 2 6 3v1c2 2 3 4 5 5l-2 1c3 3 5 6 6 9-1-1-2-4-4-4l1 2c-2 0-3-1-4-2h0-2c-1 0-2-1-3-1l-1 1v1h-1-1l-2-2c0 1 1 2 1 3l-2-1-1 1h-1-1v1h1l1 2-1 1v2c-1 1-2 1-3 1l-1-1c-1-1-1-2-2-3-1-2-2-3-3-3-2-1-3-2-4-3-2-1-3-2-5-3h2v-1l-1-2 1-2h0l-1-1h4c1 0 2 0 3-1h4v-2l-5-1 1-1h3z" class="W"></path><path d="M607 304v-1c1 1 2 1 2 1h2v2l-2 1-1-1-1-2z" class="K"></path><path d="M597 293h3 3v1l-1 1h2c-1 1-1 1-3 2h0v-2l-5-1 1-1z" class="E"></path><path d="M614 305l1 1c1-1 0-1 0-2 2 0 3 4 6 2 0 1 1 2 2 2l1 2c-2 0-3-1-4-2h0-2c-1 0-2-1-3-1l-1 1v1h-1-1l-2-2h-1l2-1v-2c1 0 2 1 3 1z" class="J"></path><path d="M611 304c1 0 2 1 3 1-1 1-1 2-1 3h-1v1l-2-2h-1l2-1v-2z" class="e"></path><path d="M609 292l3 1c2 0 5 2 6 3v1c2 2 3 4 5 5l-2 1c3 3 5 6 6 9-1-1-2-4-4-4-1 0-2-1-2-2-2-2-4-3-5-5-2-3-4-4-6-6-1 0-2 0-3-1 1-1 1 0 1-1l1-1z" class="c"></path><path d="M612 296l1-1c2 0 3 1 5 2 2 2 3 4 5 5l-2 1s-1-1-1-2c-3-2-4-4-8-5z" class="g"></path><path d="M609 292l3 1c2 0 5 2 6 3v1c-2-1-3-2-5-2l-1 1-2-1c-1 0-2 0-3-1 1-1 1 0 1-1l1-1z" class="Z"></path><path d="M604 295c1 0 2 0 3 1 2 2 5 3 7 5v1c-2-1-4-1-6-2v2h-3-1l1 1h0c-1 0-2 0-3-1v1l1 1c0 1 1 1 1 2h0-1c-2-1-4-2-5-3s-3-1-4-2h0c-1-1-2-1-3-2h0l-1-1h4c1 0 2 0 3-1h4 0c2-1 2-1 3-2z" class="I"></path><path d="M597 297l-1 2c0 1 0 1 1 1l1 1v1 1c-1-1-3-1-4-2h0c-1-1-2-1-3-2h0l-1-1h4c1 0 2 0 3-1z" class="E"></path><path d="M604 302c-1 0-3-3-4-4h1c2-1 5 0 7 2v2h-3-1zm-13-3c1 1 2 1 3 2h0c1 1 3 1 4 2s3 2 5 3h1 0c0-1-1-1-1-2l-1-1v-1c1 1 2 1 3 1h0l2 1 1 2 1 1h1c0 1 1 2 1 3l-2-1-1 1h-1-1v1h1l1 2-1 1v2c-1 1-2 1-3 1l-1-1c-1-1-1-2-2-3-1-2-2-3-3-3-2-1-3-2-4-3-2-1-3-2-5-3h2v-1l-1-2 1-2z" class="T"></path><path d="M608 306l1 1h1c0 1 1 2 1 3l-2-1-1 1h-1-1v1h1l1 2-1 1v2c-1 1-2 1-3 1l-1-1c1-1 2-1 3-2-1-1-1-1-1-2v-3h0 2v-1l-1-2h2 0z" class="W"></path><path d="M591 299c1 1 2 1 3 2h0c2 2 3 3 4 6l-1 1-2-1-4-3v-1l-1-2 1-2z" class="M"></path><g class="B"><path d="M591 299c1 1 2 1 3 2h-3l3 3s1 1 1 2v1l-4-3v-1l-1-2 1-2z"></path><path d="M594 301c1 1 3 1 4 2s3 2 5 3h1 0c0-1-1-1-1-2 1 1 2 2 2 3-2 0-2-1-4 0h1c1 1 1 2 1 3l1 2v1c-2-1-4-4-5-5s-1 0-1-1c-1-3-2-4-4-6z"></path></g><path d="M456 165c4 0 8-1 12 0 1 0 1 0 2 1h2l1 1c2 1 5 3 6 5l3 5 3 3-1 1 1 1c1 0 1 1 2 1 0 1 2 2 2 3s0 2 1 3l-1 2-1-1c0-1 0-1-1-1 0 1 0 1-1 2h-1c-1 1-1 1-2 1s-2-1-3-2h-1c-1 0-2 1-2 1l-2 1h1c-2 0-2 0-3 1s-2 1-3 1l-1 2-3-1c-2 1-4 0-6 0-1 0-1 1-2 1-1-1-2 0-4 0h-2l-1 2-2 2c-1 0-3 0-4-1l-1 1v-2h-1l2-1c-1-1-1-1-2-1l-13 8-1-1-6 1c1-2 5-4 6-6s3-4 4-6c2-5 2-9 8-13-2-1-6 0-9 0 2-2 3-3 6-4h4l2 1 3-1v-1c-2 0-3 0-4-2v-1h1 2c3 0 7 0 9-1l-1-1v-1c1 0 1 0 2-1h-2v-1h2v-1z" class="k"></path><path d="M453 188h1l-2 2h0 1c-2 1-4 1-6 2v-1l6-3z" class="W"></path><path d="M453 185h4v1c0 1 0 1-1 1s-2 0-2 1h-1c0-1-1-2-2-3h2z" class="M"></path><path d="M462 191h0c0 2 0 2-2 3v1c-1 0-1 1-2 1-1-1-2 0-4 0h-2 0l2-1-5-1c5-1 9 0 13-3z" class="b"></path><path d="M443 196c2 0 4-1 6-2l5 1-2 1h0l-1 2-2 2c-1 0-3 0-4-1l-1 1v-2h-1l2-1c-1-1-1-1-2-1z" class="f"></path><path d="M452 196h0l-1 2-2 2c-1 0-3 0-4-1l-1 1v-2l8-2z" class="e"></path><path d="M451 185c1 1 2 2 2 3l-6 3c-2 2-5 3-8 4 2-2 5-4 5-6l2-2c2 0 3 0 5-2z" class="B"></path><path d="M460 180c1 0 2 1 3 1h1c0 1 1 2 2 2 2 0 3 1 5 1v1c-2 0-5 0-6 1h2c-1 1-1 1-2 1 0 1 1 1 1 2-1 1-2 2-4 2h0c-2-2-5-1-7-1v-1h5c1 0 2-1 3-1v-1h-2-1l2-1v-1-1l-1-1-2-1h0v-2h1z" class="c"></path><path d="M460 180c1 0 2 1 3 1-1 1-1 1-1 2h2v2c-1 1-1 1-2 1v-1-1l-1-1-2-1h0v-2h1z" class="O"></path><path d="M467 180c1 0 5 0 6 1 0 1 1 1 2 1 1 1 1 2 2 2l2 3h0-1c-1 0-1 0-2 1-2-1-4-1-5 0-2 1-3 2-5 2v-1c0-1-1-1-1-2 1 0 1 0 2-1h-2c1-1 4-1 6-1v-1c-2 0-3-1-5-1-1 0-2-1-2-2h0 2l1-1z" class="S"></path><path d="M464 181h0 2c2 0 4 1 5 2 1 0 2 0 3 1 1 0 1 1 2 2h-1c-2 0-3-1-5 1h0c-2 0-3 1-4 2 0-1-1-1-1-2 1 0 1 0 2-1h-2c1-1 4-1 6-1v-1c-2 0-3-1-5-1-1 0-2-1-2-2z" class="g"></path><path d="M477 184c2 1 4 2 5 4l1 1h-1-1l-1-1-1 1 1 1h-1c-1 0-2 1-2 1l-2 1h1c-2 0-2 0-3 1s-2 1-3 1l-1 2-3-1c-2 1-4 0-6 0v-1c2-1 2-1 2-3 2 0 3-1 4-2v1c2 0 3-1 5-2 1-1 3-1 5 0 1-1 1-1 2-1h1 0l-2-3z" class="h"></path><path d="M462 191c2 0 3-1 4-2v1c-1 1-2 2-2 3h5 4c-1 1-2 1-3 1l-1 2-3-1c-2 1-4 0-6 0v-1c2-1 2-1 2-3z" class="R"></path><path d="M449 174c1 0 2 1 3 1h1v2l6 1h1v2h-1v2h0l2 1 1 1v1c-2 0-3-1-5-1l-4 1h-2c-2 2-3 2-5 2l3-2v-1c-2-1-3 0-5 0l1-1c-1-1-1-1-1-2h-1l1-1h-2l-1-1c-2-1-6 0-9 0 2-2 3-3 6-4h4l2 1h2c1 0 3 0 3-1h-1l1-1z" class="P"></path><path d="M459 182h0-4l-1-1c-3 1-5 1-7 1v-1l2-1c1 0 2-1 3-1l2 2h1 3l1 1h0z" class="B"></path><path d="M449 174c1 0 2 1 3 1h1v2l6 1h1v2h-1l-13-4c1 0 3 0 3-1h-1l1-1z" class="i"></path><path d="M449 184v-1c1 1 3 1 4 1 3-1 5-1 8-1l1 1v1c-2 0-3-1-5-1l-4 1h-2c-2 2-3 2-5 2l3-2v-1z" class="K"></path><path d="M441 179l1 1h2l-1 1h1c0 1 0 1 1 2l-1 1c2 0 3-1 5 0v1l-3 2-2 2c0 2-3 4-5 6-1 2-3 4-5 5l-5 3-6 1c1-2 5-4 6-6s3-4 4-6c2-5 2-9 8-13z" class="D"></path><path d="M439 188l-5 3c3-3 6-7 9-10h1c0 1 0 1 1 2l-1 1-2 1c-1 0-2 2-3 3z" class="K"></path><path d="M444 184c2 0 3-1 5 0v1l-3 2-2 2c0 2-3 4-5 6-1 2-3 4-5 5v-1h0c1-3 2-4 4-5 1-1 2-2 2-4 1 0 3-2 4-3-2 0-3 1-5 1 1-1 2-3 3-3l2-1z" class="J"></path><defs><linearGradient id="BU" x1="478.066" y1="182.776" x2="472.204" y2="171.355" xlink:href="#B"><stop offset="0" stop-color="#7b7a7a"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#BU)" d="M456 165c4 0 8-1 12 0 1 0 1 0 2 1h2l1 1c2 1 5 3 6 5l3 5 3 3-1 1 1 1c1 0 1 1 2 1 0 1 2 2 2 3s0 2 1 3l-1 2-1-1c0-1 0-1-1-1 0 1 0 1-1 2h-1c-1 1-1 1-2 1s-2-1-3-2l-1-1 1-1 1 1h1 1l-1-1c-1-2-3-3-5-4-1 0-1-1-2-2-1 0-2 0-2-1-1-1-5-1-6-1l-1 1h-2 0-1c-1 0-2-1-3-1v-2h-1l-6-1v-2h-1c-1 0-2-1-3-1l-1 1h1c0 1-2 1-3 1h-2l3-1v-1c-2 0-3 0-4-2v-1h1 2c3 0 7 0 9-1l-1-1v-1c1 0 1 0 2-1h-2v-1h2v-1z"></path><path d="M456 165c4 0 8-1 12 0 1 0 1 0 2 1h2l1 1c2 1 5 3 6 5l3 5c0 1 0 0-1 1l-4-4v-1l-2 1-3-3 1-1c-1 0-1-1-2-1h0c-2-1-2-2-4-2-3-2-7-1-11-1v-1z" class="R"></path><path d="M467 167c2 0 2 1 4 2h0c1 0 1 1 2 1l-1 1-1 1 1 1-1 1-1-1h-1l4 4-1 1-1-1-2 2h-1l-1 1-1 1h-2 0-1c-1 0-2-1-3-1v-2h-1l-6-1v-2c1 1 3 1 5 2 1 0 2 1 3 1s4-2 6-2c0-1 1-2 1-3s1-1 1-2h0c-1-1-1-3-2-4z" class="d"></path><path d="M469 177h2l-2 2h-1l-1 1-1 1h-2 0-1c-1 0-2-1-3-1v-2h2 3c1-1 3-1 4-1z" class="l"></path><path d="M469 177h2l-2 2h-1l-1 1-1 1h-2v-2c2 0 3-1 5-2h0z" class="f"></path><path d="M474 181c1-1 2-1 3-1l2 1 2-1 2 2c1 1 2 1 4 1 0 1 2 2 2 3s0 2 1 3l-1 2-1-1c0-1 0-1-1-1 0 1 0 1-1 2h-1c-1 1-1 1-2 1s-2-1-3-2l-1-1 1-1 1 1h1 1l-1-1c-1-2-3-3-5-4-1 0-1-1-2-2-1 0-2 0-2-1h1z" class="S"></path><path d="M473 181h1l4 1c3 1 7 4 9 7 0 1 0 1-1 2h-1c-1 1-1 1-2 1s-2-1-3-2l-1-1 1-1 1 1h1 1l-1-1c-1-2-3-3-5-4-1 0-1-1-2-2-1 0-2 0-2-1z" class="H"></path><path d="M456 166c4 0 8-1 11 1 1 1 1 3 2 4h0c0 1-1 1-1 2s-1 2-1 3c-2 0-5 2-6 2s-2-1-3-1c-2-1-4-1-5-2h-1c-1 0-2-1-3-1l-1 1h1c0 1-2 1-3 1h-2l3-1v-1c-2 0-3 0-4-2v-1h1 2c3 0 7 0 9-1l-1-1v-1c1 0 1 0 2-1h-2v-1h2z" class="V"></path><path d="M449 174c1-2 5-1 7-2 1 0 2-1 4-2h3c1 1 1 1 2 3 0 0 0 1-1 1h1v1c-2 0-3 1-5 1v-1c-2-1-7-1-8 0-1 0-2-1-3-1z" class="L"></path><path d="M354 661h1v9l1-1 1-1v1c0 2-1 3-1 5 1 0 3-4 3-6 0 0 0-1 1-1v-1c0 3-1 6 0 8l2-9c0 4 0 8-1 12v2c1-2 2-5 3-8v8c1-1 1-3 2-5 1 3 2 5 2 8v2h0c-1 3 0 7-1 10h0v-3-3c-1-1-1-4 0-6v-1h0c-1-1-1-2-1-3v-2l-1 2v2c2 2-1 7 1 9v2c-1 3 0 7-1 11h0v2h0c-1 3-1 7 0 9l1 5c1 3 1 5 2 8 1 1 1 5 1 7 1 4 1 9 2 14 1 1 1 2 2 3 0 2 0 4 1 7h2c-1 1-1 2-2 2l-1 2v3h-1v-1c-1 1-1 1-2 1v-2l-1 1h-1c0 1 0 2-1 2h-1l-1-1v2c-1-2-1-3-1-5h1c-1-2 0-3 0-4l-2-12c-2-3-2-6-3-9 0-3-1-6-2-8l-1-2v-1c-3 2-2 6-3 9v-3c-1 1-1 3-2 4h-1c0 3 0 6-1 9v-12h-1l1-10c0-3 1-8 0-10s-1-4-1-5c-1-3-1-6-1-8l-1-1c0-1-1-3-1-5 0-4 1-8 1-12l2-2v-1l3-8c0-1 0-2 1-3 1-2 1-4 1-6z" class="G"></path><defs><linearGradient id="BV" x1="358.189" y1="700.746" x2="347.311" y2="682.754" xlink:href="#B"><stop offset="0" stop-color="#252423"></stop><stop offset="1" stop-color="#4c4b4c"></stop></linearGradient></defs><path fill="url(#BV)" d="M352 678c1-3 2-4 3-6 0 3-1 6-2 9 2-3 3-5 5-7-1 3-1 6-2 9s-3 7-3 10l-1 4c-1 3-2 6-1 9l-2 1c-1-3-1-6-1-8v-1c1-1 1-5 0-7h0v-1c1-5 2-8 4-12z"></path><path d="M352 678c1 2 1 2 0 4-1 3-2 5-4 8 1-5 2-8 4-12z" class="J"></path><defs><linearGradient id="BW" x1="346.035" y1="704.147" x2="362.014" y2="725.772" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#474646"></stop></linearGradient></defs><path fill="url(#BW)" d="M353 693l1 1v1c-1 2-1 3 0 5h0v-3l1 1c0 1 0 3 1 4v2 1 3c1 1 1 1 1 2 1 0 0 4 1 4 0 0 1 1 1 2v3c1 5 2 10 1 15l1 2h-1c0-3-1-6-2-8l-1-2v-1c-3 2-2 6-3 9v-3c-1 1-1 3-2 4h-1c0 3 0 6-1 9v-12h-1l1-10c0-3 1-8 0-10s-1-4-1-5l2-1c-1-3 0-6 1-9l1-4z"></path><path d="M352 697l1 1-1 15c-1-2-1-5-1-7-1-3 0-6 1-9z" class="E"></path><path d="M351 730v1c1-1 1-3 1-5h1c0 2 0 3 1 5-1 1-1 3-2 4h-1c0 3 0 6-1 9v-12l1-2z" class="M"></path><path d="M349 707l2-1c0 2 0 5 1 7l-1 17-1 2h-1l1-10c0-3 1-8 0-10s-1-4-1-5z" class="I"></path><path d="M354 700v-3l1 1c0 1 0 3 1 4v2 1 3c1 1 1 1 1 2 1 0 0 4 1 4 0 3 0 7-1 10l-1-6c-2-6-2-12-2-18z" class="X"></path><path d="M363 716c0-1 0 0 1-1v-2 4l2 1c1 3 1 5 2 8 1 1 1 5 1 7 1 4 1 9 2 14 1 1 1 2 2 3 0 2 0 4 1 7h2c-1 1-1 2-2 2l-1 2v3h-1v-1c-1 1-1 1-2 1v-2l-1 1h-1c0 1 0 2-1 2h-1l-1-1v2c-1-2-1-3-1-5h1c-1-2 0-3 0-4l-2-12c-2-3-2-6-3-9h1l-1-2h2c-1-3-2-19-1-21 1 3 0 8 2 10v1-8z" class="O"></path><path d="M369 755c1 1 2 1 2 2 0 2 0 3 2 4v3h-1v-1c-1 1-1 1-2 1v-2l-1-7z" class="P"></path><path d="M366 753c0-3-2-8 0-11 0 5 2 9 3 13l1 7-1 1h-1c0 1 0 2-1 2h-1l-1-1v2c-1-2-1-3-1-5h1c-1-2 0-3 0-4h1v-4z" class="G"></path><path d="M366 753c1 4 1 8 1 12h-1l-1-1v2c-1-2-1-3-1-5h1c-1-2 0-3 0-4h1v-4zm-3-37c0-1 0 0 1-1v-2 4l2 1c1 3 1 5 2 8 1 1 1 5 1 7 1 4 1 9 2 14 1 1 1 2 2 3 0 2 0 4 1 7h2c-1 1-1 2-2 2-1-1-1-3-2-4 0-2-1-4-1-7-1-3-1-8-2-11-2-4-3-9-4-14-1-3-1-5-2-7z" class="T"></path><path d="M362 734c-1-3-2-19-1-21 1 3 0 8 2 10v1c0 1 1 2 1 4s0 5 1 8c1 1 1 4 1 6-2 3 0 8 0 11v4h-1l-2-12c-2-3-2-6-3-9h1l-1-2h2z" class="W"></path><path d="M360 734h2c0 2 0 3 1 4 1 2 0 5 0 7-2-3-2-6-3-9h1l-1-2z" class="T"></path><path d="M579 521l2-1 3 1-1 2-1 1 1 1s1 0 1 1l-1 1c0 1 1 2 2 3l-1 2 2 1v5l1 1c0 2-1 4 0 6-1 3-2 7-5 9-1 0-1 0-1-1h-1v1c1 3 1 6 1 9s0 6-1 8v2l-1 3c-1 2-1 6-1 9-1 0-1-4-2-5v1h0v-2l-1-1c0 2-1 3-1 4l-1 1-1 4v5c0 2-1 6-3 9 0 1-1 2-2 3v1h0l-1 1h-1c-1-1-2-2-4-3l-1 6h-1-1c0-1 0-2-1-3l-3-3h0c0-1-1-1-2-2l-3 3c0 1-1 2-2 2l-2 2c1-1 1-3 0-5l1-1c0-1 0-2 1-3s2-2 3-2c1-3 3-8 3-11 0-4 0-6 2-9v-5h0-1-1v-1c1-1 1-3 2-4v-1-1l1-1v-2c0-1 1-2 1-3 0-2-1-5 1-8-1-1-1-2-1-3v-1l1-2h1v1c1-1 2-2 2-3 1-1 2-1 3-2v-1-5c2 0 2-1 3-2 1 1 1 1 2 1l2-2h1c1-1 1-2 1-4l1-2v-1c2-1 3-3 5-4h0z" class="R"></path><path d="M564 552l2 1c0 1 0 3 1 4-1 1-1 2-2 2-1 1-2 1-2 1-1-1 0-6 1-8z" class="K"></path><path d="M569 556c0-3-1-6 0-9 2 1 3 1 4 3v7c-1 0-1 0-1-1l-1 1h-1-1v-1z" class="Y"></path><path d="M564 552l3-6v-1 2 1h1v-2l1-2h1 0c0 1-1 2-1 3-1 3 0 6 0 9l-1 3h-1v-2c-1-1-1-3-1-4l-2-1z" class="J"></path><path d="M568 559l1 1c-1 1-1 1 0 2v-1c1 0 1-1 2-2l1 1c1-1 2-1 2-3 1 1 2 2 2 3h0-2c-2 2-2 5-3 7h0c-1-1-1-2-1-2h-1l-1 10-1-1c0-4 1-8 1-12h-1v2c-1-1-2-1-2-2s1-2 2-3h1zm-1-26c1 1 1 1 2 1 0 2 0 3-1 4-1 2-1 3-1 5l-1 1h0v-2l-1-1c-1 1-1 2-1 3-1 1-2 2-2 4v2c-1 0-1 0-2 1-1 0 0 2-2 1v-1c-1-1-1-2-1-3v-1l1-2h1v1c1-1 2-2 2-3 1-1 2-1 3-2v-1-5c2 0 2-1 3-2z" class="h"></path><path d="M557 547c1 0 1 1 1 2 1-1 1-2 2-2l1 1h1v2c-1 0-1 0-2 1-1 0 0 2-2 1v-1c-1-1-1-2-1-3v-1z" class="S"></path><path d="M580 540c1 2 0 4 0 6s1 4 1 7h-1v1c1 3 1 6 1 9s0 6-1 8l-1-3-3-8h0l-1-9 1-1c1 3 1 5 2 8v-8l2-10z" class="Z"></path><path d="M580 554c1 3 1 6 1 9h-1-1v-6h1v-3z" class="J"></path><path d="M574 526l1-1c1 1 1 1 1 2l1 1v-1h1l2 2 1 2c0 1 0 2-1 3l1 1-1 2c0 1-1 3 0 3l-2 10-1-1-1-4h0c-1 1-1 3-1 4l-1 1c-1-3-1-5-2-8s0-6 0-10c1-1 1-2 1-4l1-2z" class="N"></path><path d="M574 534c1 0 1 0 2 1v3l-1 1-1-1v-2-2zm3-6v-1h1 0c0 1 0 1 1 2 0 2-1 5-1 7h0c-2-1-2-1-2-3v-1c0-1 0-2 1-3v-1z" class="C"></path><path d="M573 528l1 6v2c-1 3 0 6 0 8v3-1c0-1 0-1 1-2 0-2-1-2 1-4v1l2 1v-3l1 1c0-1 0-1 1-3 0 1-1 3 0 3l-2 10-1-1-1-4h0c-1 1-1 3-1 4l-1 1c-1-3-1-5-2-8s0-6 0-10c1-1 1-2 1-4zm-2 39c1-2 1-5 3-7h2l3 8 1 3v2l-1 3c-1 2-1 6-1 9-1 0-1-4-2-5v1h0v-2l-1-1c0 2-1 3-1 4l-1 1-1 4h0 0v-9c-2-3-1-7-1-11z" class="L"></path><path d="M579 568l1 3v2l-1 3c-1 2-1 6-1 9-1 0-1-4-2-5v1h0v-2l-1-1c0 2-1 3-1 4l-1 1c0-3 1-5 2-7v-3c1 1 2 2 2 4v1c0-4 2-7 2-10z" class="H"></path><path d="M575 576c-1 0-1 0-2-1s-1-6 0-8c0-2 0-4 1-6l1 1c1 1 2 3 2 5 0 1-1 3-2 4v1 1 3z" class="Q"></path><path d="M579 521l2-1 3 1-1 2-1 1 1 1s1 0 1 1l-1 1c0 1 1 2 2 3l-1 2 2 1v5l1 1c0 2-1 4 0 6-1 3-2 7-5 9-1 0-1 0-1-1 0-3-1-5-1-7s1-4 0-6c-1 0 0-2 0-3l1-2-1-1c1-1 1-2 1-3l-1-2-2-2h-1v1l-1-1c0-1 0-1-1-2l-1 1v-1c2-1 3-3 5-4h0z" class="d"></path><path d="M579 521h0l-1 1 1 1v2c0 1 1 2 1 3 1 0 1 1 1 2l1 1c1 2 2 3 2 5l1 1c0 2-1 8-1 10l-1 1h-1l-1-4c0-2 0-5 1-7l-1-2-1-1c1-1 1-2 1-3l-1-2-2-2h-1v1l-1-1c0-1 0-1-1-2l-1 1v-1c2-1 3-3 5-4z" class="V"></path><path d="M568 575l1-10h1s0 1 1 2h0c0 4-1 8 1 11v9h0 0v5c0 2-1 6-3 9 0 1-1 2-2 3v1h0l-1 1h-1c-1-1-2-2-4-3 0-1 1-2 1-2l-2-2c0-2 1-5 2-7l2-12c1-3 1-4 3-5v-1l1 1z" class="H"></path><path d="M567 574l1 1v2c2 1 1 2 1 3v8c0-2 0-4-1-6-1-1-1-2-1-3v-1h0v-3-1z" class="f"></path><path d="M562 592l2-12c1-3 1-4 3-5v3h0v1c0 1 0 2 1 3 1 2 1 4 1 6l-1 1c0 2 0 4-1 6-1 3-2 6-2 9 1 0 1 0 2 1l-1 1h-1c-1-1-2-2-4-3 0-1 1-2 1-2l-2-2c0-2 1-5 2-7z" class="Z"></path><path d="M565 585c2 2 1 6 1 8-1 2-1 3-2 4v1l-1-2 2-11z" class="K"></path><path d="M562 592l2-12c1-3 1-4 3-5v3c-1 2-2 5-2 7l-2 11-1 5-2-2c0-2 1-5 2-7z" class="o"></path><path d="M560 566c0-1 0-3 1-3l1 1c1-1 1-2 2-2v1h0l1-1c0 1 1 1 2 2v-2h1c0 4-1 8-1 12v1c-2 1-2 2-3 5l-2 12c-1 2-2 5-2 7l2 2s-1 1-1 2l-1 6h-1-1c0-1 0-2-1-3l-3-3h0c0-1-1-1-2-2l-3 3c0 1-1 2-2 2l-2 2c1-1 1-3 0-5l1-1c0-1 0-2 1-3s2-2 3-2c1-3 3-8 3-11 0-4 0-6 2-9v-5c1-2 2-5 3-7 0-1 0-2 1-3v3l1 1z" class="L"></path><path d="M560 574c1 2 0 4 1 5 0 1 0 2 1 3h0c0-1 0-1 1-1v1 1c0 1 0 1-1 2h0c-1 1-1 1-1 3-1 1-1 2-1 3l-1-1v-4l1-12z" class="H"></path><path d="M563 565h1l1 1c0 4-1 7-2 10l-1 1v-3c-1-1-1-2-1-3v-1c1-1 1-2 1-3l1-2z" class="D"></path><path d="M559 586v4l1 1c0-1 0-2 1-3 0-2 0-2 1-3h0v3l-1 1v1c0 1 0 2 1 2-1 2-2 5-2 7l2 2s-1 1-1 2l-1 6h-1-1c0-1 0-2-1-3l-3-3h0c1-1 2-2 2-3 0-5 2-10 3-14z" class="i"></path><path d="M560 599l2 2s-1 1-1 2l-1 6h-1-1c0-1 0-2-1-3 3-2 3-4 3-7zm-5-27c1-2 2-5 3-7 0-1 0-2 1-3v3l1 1c-1 3-3 9-1 12 0-2 1-5 1-6 1 1 1 2 0 2l-1 12c-1 4-3 9-3 14 0 1-1 2-2 3 0-1-1-1-2-2l-3 3c0 1-1 2-2 2l-2 2c1-1 1-3 0-5l1-1c0-1 0-2 1-3s2-2 3-2c1-3 3-8 3-11 0-4 0-6 2-9v-5z" class="S"></path><path d="M559 578c0-2 1-5 1-6 1 1 1 2 0 2l-1 12c-1 4-3 9-3 14 0 1-1 2-2 3 0-1-1-1-2-2 1-1 1-2 2-3 3-6 4-14 5-20z" class="b"></path><path d="M555 577c0 2-1 9 0 9v-2c1-2 1-6 2-7 1 6-3 14-4 20h0l1 1c-1 1-1 2-2 3l-3 3c0 1-1 2-2 2l-2 2c1-1 1-3 0-5l1-1c0-1 0-2 1-3s2-2 3-2c1-3 3-8 3-11 0-4 0-6 2-9z" class="g"></path><path d="M547 599c1-1 2-2 3-2l-1 4c0 2-2 3-2 5l-2 2c1-1 1-3 0-5l1-1c0-1 0-2 1-3z" class="K"></path><defs><linearGradient id="BX" x1="317.728" y1="643.695" x2="353.56" y2="468.342" xlink:href="#B"><stop offset="0" stop-color="#3e3f40"></stop><stop offset="1" stop-color="#646362"></stop></linearGradient></defs><path fill="url(#BX)" d="M293 459l2 1c1-1 3-3 5-3h1v-1l31 77 23 56 7 18v-4c4 7 7 15 10 22 1 1 1 4 2 5 1 3 3 6 4 9l-1 1c0 1 1 4 1 4 1 5 1 9 1 14 0 3 1 6 0 9l-36-86-1-2v-2l-2-4c-1-1-1-2-2-4l-45-110z"></path><path d="M355 589l7 18v-4c4 7 7 15 10 22 1 1 1 4 2 5 1 3 3 6 4 9l-1 1-2-3c-3-2-5-7-7-10-1-1-1-3-2-4l-3-6c0-2-2-4-2-6l-2-3c0-2-1-4-2-6 0 0 0-1-1-2h0c0-4-2-6-1-11z" class="J"></path><path d="M362 603c4 7 7 15 10 22 1 1 1 4 2 5 1 3 3 6 4 9l-1 1-2-3-13-30v-4z" class="H"></path><path d="M540 154h1v-2c-1 0-2 0-3-1h0c-1-1-1-2-2-3l1-1v1l1-1 1 1 2 1h0c1-1 1-1 2 0v3c2 0 2-2 3 0l1 1s0 1 1 1l1 2h1v-1c1 0 1 1 2 2h1 2c3-3 7-7 12-7h0c2 1 5 1 8 3h0 2l1-1v1l1 1c1 0 2-1 2-1 1 1 1 1 2 1v-2l1 2c1 3 1 7-1 11-1 2-1 5-3 8l-2 2h3c-2 1-5 2-7 2l-1 2c-4 1-8 1-12 2l-1 1h-2c-3 1-5 2-7 3l-3 2-1-1c-1 1-3 3-4 3l-1-1h0c-1 1-1 2-2 2l1 1h2l-1 1-2 2v1l2-1 1-1v2l1 1c-1 1-2 1-3 3v2h1c-2 3-3 4-4 7v3l1 2 2-2c1 1 0 0 1 2-1 1-1 2-2 3-1 0 0 0-1-1l-1 3c0 1-1 1-1 3-2 0-2 0-3-1l-1-2-2-2-1 1c-1-2 0-6 0-8 1-2 0-3 0-5l-4 4-1-1-1 1-1-1 1-1 2-5-2-4c1-2 0-4 0-6h-1c-1-1-1-2-2-3v-1h2l4-4c0-1 1-3 1-4h1l1-1-1-1v-1c0-3 6-10 7-13v-1l1-1c2-3 2-5 3-7z" class="N"></path><path d="M564 164c-1 1-2 1-4 1-1 0-4 2-5 3h-1c2-3 5-5 8-6l2 2z" class="C"></path><path d="M572 163h1c2 0 4 0 6 1h1c-1 2-2 2-3 3-2 1-4 1-5 0h2l-3-3 1-1z" class="I"></path><path d="M564 165c2 1 2 1 4 1v1c-3 1-5 2-7 2-1 0-2 0-3 1l-1 1-3 1h-1v-1c2-2 3-3 5-4 2 0 4-1 5-1l1-1z" class="U"></path><path d="M562 172c4-3 7-4 11-3v1h2 0l-1 2h0-3c-1 1-2 1-3 1l-3 1v-2h-2-1z" class="h"></path><path d="M577 168h1l1-1c1 0 1 0 2-1l1-2 1 1c-1 2-1 5-3 8l-6-1h0l1-2h0-2v-1l4-1z" class="B"></path><path d="M560 174h0c1-1 1-2 2-2h1 2v2l3-1-3 2c-2 0-3 2-5 2v1l-2 2c-1 1-3 2-4 2v-1-1c1-2 2-3 3-4l3-2z" class="Q"></path><path d="M560 174h0c1-1 1-2 2-2h1 2v2c-2 1-4 1-6 2-1 1-1 0-2 0l3-2z" class="V"></path><path d="M546 182h0c2-1 3-2 4-3s1-1 2-1l2-2c2-2 3-3 6-2l-3 2c-1 1-2 2-3 4h0c-2 1-5 5-7 5-1 1-2 0-3 0 0-1 1-2 2-3z" class="C"></path><path d="M569 158l9 3v1c-2 0-4 0-5 1h-1c-3-1-5 0-8 1l-2-2c1-1 2-2 3-2h0 1c1-2 2-2 3-2z" class="U"></path><path d="M583 154v-2l1 2c1 3 1 7-1 11l-1-1-1 2c-1 1-1 1-2 1l-1 1h-1v-1c1-1 2-1 3-3h-1c-2-1-4-1-6-1 1-1 3-1 5-1v-1l1-1v-1-5c1 0 2-1 2-1 1 1 1 1 2 1z" class="T"></path><path d="M581 153c1 1 1 1 2 1-1 3-2 5-2 7-1 1-2 1-3 1v-1l1-1v-1-5c1 0 2-1 2-1z" class="J"></path><path d="M571 172h3l6 1-2 2h3c-2 1-5 2-7 2-2 1-4 2-7 2h-1l-1 1v-1-1h-5v-1c2 0 3-2 5-2l3-2c1 0 2 0 3-1z" class="a"></path><path d="M571 172h3l6 1-2 2h-1-3-1l-2-3z" class="Q"></path><path d="M560 178v-1c2 0 3-2 5-2v1c1 1 2 1 4 0h1v1c-1 0-2 1-3 2h-1l-1 1v-1-1h-5z" class="U"></path><path d="M567 179c3 0 5-1 7-2l-1 2c-4 1-8 1-12 2l-1 1h-2c-3 1-5 2-7 3l-3 2-1-1c-1 1-3 3-4 3l-1-1h0c-1 1-1 2-2 2v1l-1-1c1-1 3-3 3-5 2-2 2-2 4-3-1 1-2 2-2 3 1 0 2 1 3 0 2 0 5-4 7-5h0v1 1c1 0 3-1 4-2l2-2h5v1 1l1-1h1z" class="L"></path><path d="M567 150c2 1 5 1 8 3h0 2l1-1v1l1 1v5 1l-1 1-9-3c-3 0-5 0-8 1h0-1l4-2h-2-2c0-2 2-2 3-3 0 0 1-1 1-2 1-1 1-1 3-2z" class="F"></path><path d="M564 152c3-1 6 0 8 1h1c1 1 2 1 3 2-4 0-8 0-12 2h-2-2c0-2 2-2 3-3 0 0 1-1 1-2z" class="C"></path><path d="M542 168c2-1 5-5 6-6h3v2 1h0l4-2h0c-1 1-1 2-2 3v1c-2 2-4 5-6 6l-1-1-3 3c0 1 0 1 1 2h-1v2l1 1v-1l1 1-1 1c-2 1-2 2-3 3l-2 1-1 1h-3v-2-1 1l-1-1v-4c1 0 2-2 3-3 1-3 3-5 5-8z" class="L"></path><path d="M535 184h1l3-3h1c0 1 0 2-1 4l-1 1h-3v-2zm7-16c2-1 5-5 6-6h3v2 1c-2 2-3 3-5 4 0 1-1 1-1 2l-2 2c0 1-1 2-1 2-2 3-4 3-6 6 0 0-1 0-1 1l-1-3c1 0 2-2 3-3 1-3 3-5 5-8z" class="C"></path><path d="M540 154h1v-2c-1 0-2 0-3-1h0c-1-1-1-2-2-3l1-1v1l1-1 1 1 2 1h0c1-1 1-1 2 0v3c2 0 2-2 3 0l1 1s0 1 1 1l1 2h1v-1c1 0 1 1 2 2h1 2c-2 1-3 2-4 3h0-1c-1 1-1 1-2 1-2 1-4 3-5 5l-1 2c-2 3-4 5-5 8-1 1-2 3-3 3v4l1 1v-1 1 2h3l1-1 2-1c-1 2-1 4-2 5s-1 2-2 3h0v-1-1l-1 1h0l-2-1-1 1c-1 0-1-1-1-2h-2c0-4-2-6-1-10l1-1-1-1v-1c0-3 6-10 7-13v-1l1-1c2-3 2-5 3-7z" class="h"></path><path d="M540 154h1v-2c-1 0-2 0-3-1h0c-1-1-1-2-2-3l1-1v1l1-1 1 1v2h1c1 0 1 0 1 1 1 1 1 1 1 2v1c0 4-2 6-5 9h-1v-1l1-1c2-3 2-5 3-7z" class="H"></path><path d="M539 148l2 1h0c1-1 1-1 2 0v3c2 0 2-2 3 0l1 1s0 1 1 1l1 2h1v-1c1 0 1 1 2 2v1h-3l-1-1c-1-1-3-1-3-1h-1s-1-1-1-2h0-1v-1c0-1 0-1-1-2 0-1 0-1-1-1h-1v-2z" class="R"></path><path d="M539 189l-1-1 1-1-1-1c-1 2-2 2-4 3 0-2 0-2-1-3-1 1-1 0-2 1v-2c1 0 1-2 1-2v-1-1l-1-1c0-2 0-3 1-4v-1c2-3 4-7 8-8 1-1 5-4 6-5 0-2-1-1 0-2 2-1 3-1 5 0h-1c-1 1-1 1-2 1-2 1-4 3-5 5l-1 2c-2 3-4 5-5 8-1 1-2 3-3 3v4l1 1v-1 1 2h3l1-1 2-1c-1 2-1 4-2 5z" class="H"></path><path d="M521 187h2l4-4c0-1 1-3 1-4h1c-1 4 1 6 1 10h2c0 1 0 2 1 2l1-1 2 1h0l1-1v1 1h0c1-1 1-2 2-3s1-3 2-5c1-1 1-2 3-3l-2 4c0 2-2 4-3 5l1 1v-1l1 1h2l-1 1-2 2v1l2-1 1-1v2l1 1c-1 1-2 1-3 3v2h1c-2 3-3 4-4 7v3l1 2 2-2c1 1 0 0 1 2-1 1-1 2-2 3-1 0 0 0-1-1l-1 3c0 1-1 1-1 3-2 0-2 0-3-1l-1-2-2-2-1 1c-1-2 0-6 0-8 1-2 0-3 0-5l-4 4-1-1-1 1-1-1 1-1 2-5-2-4c1-2 0-4 0-6h-1c-1-1-1-2-2-3v-1z" class="W"></path><path d="M524 206c2-1 3-3 5-5 0 1 0 1 1 1h1c0 1 0 1-1 2h0l-4 4-1-1-1 1-1-1 1-1z" class="T"></path><path d="M521 187h2l4-4c0-1 1-3 1-4h1c-1 4 1 6 1 10h2c0 1 0 2 1 2l1-1 2 1h0l1-1v1 1h0c1-1 1-2 2-3s1-3 2-5c1-1 1-2 3-3l-2 4c0 2-2 4-3 5l1 1c-1 2-3 4-5 6h-1v-1-1c0 1-1 1-2 2s-1 1-1 2c-1 1-2 1-3 1 0-2 0-5-1-7v-6c-1 1-2 2-3 4h-1c-1-1-1-2-2-3v-1z" class="Y"></path><path d="M527 193h0c1-1 1 0 2-1 0 2 1 3 1 4v1l1-1c1-1 1-2 2-3l1 1h1l1-1c0 1-1 2-2 3v-1c0 1-1 1-2 2s-1 1-1 2c-1 1-2 1-3 1 0-2 0-5-1-7z" class="J"></path><path d="M542 194l1-1v2l1 1c-1 1-2 1-3 3v2h1c-2 3-3 4-4 7v3l1 2 2-2c1 1 0 0 1 2-1 1-1 2-2 3-1 0 0 0-1-1l-1 3c0 1-1 1-1 3-2 0-2 0-3-1l-1-2 1-2c-1 0-1-2-1-3h-1v2h-1c0-3 0-7 2-9v-1c2-3 6-7 9-10v-1h0z" class="f"></path><path d="M534 216c0-1 0-1 1-2 0 1 0 2 1 3v-1h1l1 2c0 1-1 1-1 3-2 0-2 0-3-1l-1-2 1-2z" class="k"></path><path d="M541 199v2h1c-2 3-3 4-4 7v3l1 2 2-2c1 1 0 0 1 2-1 1-1 2-2 3-1 0 0 0-1-1l-1 3-1-2v-5h-1v-1-3c-1 1-1 2-2 3h0c0-2 0-3 1-4l1-1c1-3 3-4 5-6z" class="L"></path><path d="M504 225l-1 3h1l2-1v1l-1 2 1-1v1c2-1 2-1 3-1h2l2-2c2 1 1 1 2 2 1 0 1-1 2-1 0 0 1 0 1 1h2l1 1h2v1l3 1 3 3c0 2 0 4 1 5l2 1v7c1 0 2-1 3-1 0 2 1 4 2 5v1 3h-1c1 1 1 2 1 3v9h0c-1 2-2 5-3 5-1 1-2 1-3 2 0 1 0 1-1 2h0l-1 1c-2-1-5-1-7-1h-12c-3 0-5 0-8 1-1 0-2 0-3 1 0 1 0 0 1 1h0c1 1 2 2 4 3 1 0 2 1 3 2-2 0-3-1-4-1-1-1-1-1-2-1l-1 2c-1-1-1-1-1-2-2 0-2-1-4-1-1-1-1-3-1-4v-2c-2-2-3-5-4-7-2 1-2 2-4 3h0v-4-2l1-3 3-3h0c1-1 2-2 2-4 0-3 2-5 2-8v-1c-1-2-1-4-1-7h0c-2 0-3-2-4-3l-1-4h0c2 0 2 0 4-1v1h1 1l1-1c1-1 1-2 2-3v-1c2 0 2 0 3-1l4-2z" class="N"></path><path d="M513 265h1c1 2 1 3 0 4l-1 1c-1-2-2-2-1-3l1-2z" class="L"></path><path d="M498 251c0-1 0-2 1-3 1 1 1 2 1 3 0 3 1 6 3 8l-1 2c-1-1-1-2-1-2l-3-8z" class="H"></path><path d="M502 261l1-2c1 2 1 3 1 5-1 2-3 5-4 7h-1c0-1 1-3 1-4 1-2 2-4 2-6z" class="d"></path><path d="M503 233l2-2c0 6 2 12 1 17-1-2 0-4-1-6-1-3-2-6-2-9z" class="L"></path><path d="M503 270c5 0 12 2 15-2 1-1 1-2 1-3 1 0 1 0 1 1 1 0 0 0 1 1l-1 1v1c2 0 4-1 5 1l1 1h-2-17c-2 0-2 0-4-1z" class="C"></path><path d="M502 245c0 1 1 2 2 3s1 2 1 3l1 2-1 1c1 1 1 1 1 2v4c-2-2-4-5-4-8-1 0-1-1-1-1v-4c0-1 0-1 1-2z" class="U"></path><path d="M524 256c0 1-1 2-1 4-1 1-1 3-1 4l-1-1v-1c-1-3 1-12 3-15h0c1-2 1-2 2-3v-1h0 1v2 1c-1 3-2 6-3 10z" class="a"></path><path d="M501 272l2-2c2 1 2 1 4 1h17 2l3 2h0l2 2c0 1 0 1-1 2h0c-4-2-25-1-30 0l-1-1v-2l1-2h1z" class="n"></path><path d="M500 272h1v2h0c6 0 11 1 17 1 1 0 2-1 3-1 3 0 6 1 8-1h0l2 2c0 1 0 1-1 2h0c-4-2-25-1-30 0l-1-1v-2l1-2z" class="U"></path><path d="M500 277h-1l-1-1c-2-2-2-4-4-6-1-3-2-5-2-8 0-1 0-2 1-3 0-1 0-1 1-2 0-3 2-5 3-7v-7c1-1 2-3 3-4v1h1c0-3 0-5 2-7 0 3 1 6 2 9 1 2 0 4 1 6v4 1l-1-2c0-1 0-2-1-3s-2-2-2-3c-1 1-1 1-1 2h-2v1c-1 1-1 2-1 3l3 8s0 1 1 2c0 2-1 4-2 6 0 1-1 3-1 4h1v1l-1 2v2l1 1z" class="S"></path><path d="M499 247c-1-1-1-2-1-4h2v-1h1c0 1 1 1 1 3-1 1-1 1-1 2h-2zm-5 15c0-2 0-3 1-5h1c0 1 0 1 1 2h0l-1 7v-2c-1-1-2-1-2-2z" class="V"></path><path d="M494 262c0 1 1 1 2 2v2h1l1 1c0-1 1-2 1-3l1 3c0 1-1 3-1 4h1v1l-1 2-3-3v-1c-1-3-2-5-2-8z" class="i"></path><path d="M499 264l1 3c0 1-1 3-1 4h-1c-1-2-1-3-1-5l1 1c0-1 1-2 1-3z" class="L"></path><path d="M498 251l3 8s0 1 1 2c0 2-1 4-2 6l-1-3c0 1-1 2-1 3l-1-1h-1l1-7v-2l1-6z" class="V"></path><path d="M497 259c1 1 1 1 1 2 1-1 1-1 2-1 0 1 0 2-1 4h0c0 1-1 2-1 3l-1-1h-1l1-7z" class="Q"></path><path d="M504 225l-1 3h1l2-1v1l-1 2 1-1v1 1h-1 0l-2 2c-2 2-2 4-2 7h-1v-1c-1 1-2 3-3 4v7c-1 2-3 4-3 7-1 1-1 1-1 2-1 1-1 2-1 3 0 3 1 5 2 8 2 2 2 4 4 6l1 1h1c5-1 26-2 30 0l-1 1c-2-1-5-1-7-1h-12c-3 0-5 0-8 1-1 0-2 0-3 1 0 1 0 0 1 1h0c1 1 2 2 4 3 1 0 2 1 3 2-2 0-3-1-4-1-1-1-1-1-2-1l-1 2c-1-1-1-1-1-2-2 0-2-1-4-1-1-1-1-3-1-4v-2c-2-2-3-5-4-7-2 1-2 2-4 3h0v-4-2l1-3 3-3h0c1-1 2-2 2-4 0-3 2-5 2-8v-1c-1-2-1-4-1-7h0c-2 0-3-2-4-3l-1-4h0c2 0 2 0 4-1v1h1 1l1-1c1-1 1-2 2-3v-1c2 0 2 0 3-1l4-2z" class="Y"></path><path d="M497 233h1l1 1c-1 1-1 2-1 3s-1 2-1 3c-1 1-1 2-1 4-1 1-1 2-2 4v-1c-1-2-1-4-1-7h1l2-1c1-1 0-1 0-2s1-1 1-1v-2-1z" class="B"></path><path d="M496 275v1c1 1 1 2 1 3v2h1c1 0 1 0 2-1h0c1 1 2 2 4 3 1 0 2 1 3 2-2 0-3-1-4-1-1-1-1-1-2-1l-1 2c-1-1-1-1-1-2-2 0-2-1-4-1-1-1-1-3-1-4v-2l2-1z" class="P"></path><path d="M504 225l-1 3h1l2-1v1l-1 2c-1 1-2 1-4 1l-1 1-1 2-1-1h-1v-4-1c2 0 2 0 3-1l4-2z" class="M"></path><path d="M498 233v-2c0-1 1-1 2-2l1 1-1 2-1 2-1-1z" class="I"></path><path d="M490 260c0 1 0 3 1 4 0 1 0 3-1 4 1 1 1 1 2 1v1l1 1c0 1 0 1 1 1 0 2 1 3 2 3l-2 1c-2-2-3-5-4-7-2 1-2 2-4 3h0v-4-2l1-3 3-3h0z" class="e"></path><path d="M490 260c0 2 0 3-1 5h-1c-1 0-1 0-2 1l1-3 3-3z" class="K"></path><path d="M497 229v4 1 2s-1 0-1 1 1 1 0 2l-2 1h-1 0c-2 0-3-2-4-3l-1-4h0c2 0 2 0 4-1v1h1 1l1-1c1-1 1-2 2-3z" class="p"></path><path d="M497 229v4 1l-1 1c-1-1-2-1-2-2l1-1c1-1 1-2 2-3z" class="E"></path><path d="M489 237l2-2h2c1 1 0 4 0 5-2 0-3-2-4-3z" class="j"></path><path d="M523 230v1l3 1 3 3c0 2 0 4 1 5l2 1v7c1 0 2-1 3-1 0 2 1 4 2 5v1 3h-1c1 1 1 2 1 3v9h0c-1 2-2 5-3 5-1 1-2 1-3 2l-2-2c-1-2-1-3-1-5s-1-3-1-4c-1-2-2-2-2-4h0c1-2 1-3 0-4h-1c1-4 2-7 3-10v-1-1c1-2 1-3 0-5-1 1-2 2-2 3v1h-2c0-1-1-2-1-3-1-1-1-7-1-10h2z" class="h"></path><path d="M535 247c0 2 1 4 2 5v1 3h-1c0-1-1-2-2-4-1-1-1-3-2-4 1 0 2-1 3-1z" class="O"></path><path d="M523 230v1l1 1s1 1 2 1c0 1 0 1-1 2 0 1 0 2-1 3l2 1 1-1c1 1 1 2 1 4h1 1c0 3 1 5 1 8-2-2-2-3-2-5v-1l-2 2v-1-1c1-2 1-3 0-5-1 1-2 2-2 3v1h-2c0-1-1-2-1-3-1-1-1-7-1-10h2z" class="L"></path><path d="M527 246l2-2v1c0 2 0 3 2 5 1 4 1 9 3 12 0 1 1 2 1 3s-1 1-1 2v2h-1c0 1-1 2-1 3-2-1-3-3-4-4 0-2-1-3-1-4-1-2-2-2-2-4h0c1-2 1-3 0-4h-1c1-4 2-7 3-10z" class="C"></path><path d="M513 227c2 1 1 1 2 2 1 0 1-1 2-1 0 0 1 0 1 1v2h1l1-1c1 2 0 8 0 11v1 5c-1 4 0 7-1 11 0 2 0 3-1 6-1-1-2-1-2-3-2 0-2 0-3 1h-3c-1 2-2 3-2 5l-1-1c-1-3 0-12 1-16v-2-3-2c-1-1-1-1-1-2l2-1v2c0-3-1-5 0-8v-5h2l2-2z" class="U"></path><path d="M575 258c2 0 3 0 4 1 2 2 4 3 6 4 1 0 2 1 3 2v-1c0-1-1-2-2-2v-1h3 1c2 5 4 11 5 15 1 2 2 4 2 6 1 2 2 5 5 6 3 2 8 4 11 4l-1 1-3-1c-3 0-6 0-9 1h-3l-1 1 5 1v2h-4c-1 1-2 1-3 1h-4l1 1h0l-1 2 1 2v1h-2c2 1 3 2 5 3 1 1 2 2 4 3v1l-1 1c-1-1-2-2-3-2v1l-3-2h-1v2c1 1 2 3 2 4h0l-1-1-1 1-1 2v-1l-1-1-1 1 1 1h-2l-1 2c1 1 2 1 2 3-1-1-1-1-3-2l-1-1h-1c0 3 0 5 1 7v2l1 1h0l-1-1h0l-2-3c0-1 0-2-1-2l1 7h0c-1-2-2-5-3-8 0-2-2-4-3-6l-1-1c-1-2-2-3-3-4s-1-2-3-2l-1-1v-1h0c-3-1-9-6-10-9l-1-1c-2-1-4-2-7-3v-1l1-6h0c0-1 0-1 1-2v-3l1-2v-1-1-1l2-4c1-1 1-2 2-3s2-1 2-3l1-1c1 1 2 1 3 1h1c-1-2-2-2-4-3 1-1 2-1 2-1v-2h1v-1-1c2 0 3 0 4 1h2c1 1 2 1 3 1v-1c1 0 2 0 4 1h2l-1-1c-1 0-1-1-2-1l1-1z" class="M"></path><path d="M574 291h1v2h-3l-2-2h4z" class="B"></path><path d="M570 291v-1c-1-1-1-1-1-2l4-1 1 2v1 1h-4z" class="I"></path><path d="M567 289l1 1c-1 0 0 0-1 1h1l1 1c-2 0-3 0-5-1h0l-2 1h-2l2-3h4 1z" class="B"></path><path d="M560 284h-5l-1-1c4 0 11 1 14-1l1 1v-1l2 1c1 1 1 2 2 3h0c-1 1-3 1-5 1 1-2 0-2 0-3-2 2 0 2-2 3h-2 0l-5-1 1-2z" class="I"></path><path d="M560 284h6l-2 2v1l-5-1 1-2z" class="E"></path><path d="M552 280c4 0 8-1 12 0 2 1 4 1 5 2v1l-1-1c-3 2-10 1-14 1l1 1h5l-1 2h-8v-1-3l1-2z" class="p"></path><path d="M550 287h8c2 0 5 1 7 1 1 0 1 0 2 1h-1-4l-2 3c-2-1-4-2-7-2h-1l3 1c3 2 6 2 9 4l1 1c0 1 1 0 0 1 0 1 0 0-1 1-2 0-6-1-9-2-2-1-4-3-6-3l1-6h0z" class="F"></path><path d="M556 270c1-1 2-1 2-3l1-1c1 1 2 1 3 1h1c1 2 2 3 4 5h0l2 2h0c1 3 4 6 6 9l-3-2c-2 0-6-1-9-2h0-11v-1-1l2-4c1-1 1-2 2-3z" class="j"></path><path d="M556 270l1-1c1 0 1 1 2 1s1-2 2 0v1c0 3 1 4 3 5h1v2c-1 1-1 1-2 1h-11v-1-1l2-4c1-1 1-2 2-3zm9 30h19c-2-4-5-8-6-12v-1l2 1c3 1 5 1 7 2 0 1 0 0 1 1 0 0 0 1-1 2l1 1c-1 0-2 0-2 1 2 0 4 0 6-1h4l5 1v2h-4c-1 1-2 1-3 1h-4l1 1h0l-1 2 1 2v1h-2-1l1 1-1 1-3-1c-3-1-6-2-8-2s-4-1-6-1-4-1-6-2z" class="F"></path><path d="M588 304l-3-1h0c1-1 2-2 3-2v-3h2l1 1h0l-1 2 1 2v1h-2-1z" class="j"></path><path d="M580 288c3 1 5 1 7 2 0 1 0 0 1 1 0 0 0 1-1 2l1 1c-1 0-2 0-2 1h-1c-2-2-3-4-5-7z" class="I"></path><defs><linearGradient id="BY" x1="567.005" y1="295.501" x2="578.632" y2="320.439" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232222"></stop></linearGradient></defs><path fill="url(#BY)" d="M567 307c-3-3-7-5-9-8l1-1 6 2c2 1 4 2 6 2s4 1 6 1 5 1 8 2l3 1 1-1-1-1h1c2 1 3 2 5 3 1 1 2 2 4 3v1l-1 1c-1-1-2-2-3-2v1l-3-2h-1v2c1 1 2 3 2 4h0l-1-1-1 1-1 2v-1l-1-1-1 1 1 1h-2l-1 2c1 1 2 1 2 3-1-1-1-1-3-2l-1-1h-1c0 3 0 5 1 7v2l1 1h0l-1-1h0l-2-3c0-1 0-2-1-2l1 7h0c-1-2-2-5-3-8 0-2-2-4-3-6l-1-1c-1-2-2-3-3-4s-1-2-3-2l-1-1v-1z"></path><path d="M577 303c2 0 5 1 8 2h-1-3-1l1 2c1 1 1 2 1 4 0 0 0 1-1 2l-1-1-2-2c-1-1-2-3-2-4l4 1v-1c-1-1-1-2-3-3z" class="E"></path><path d="M588 304h1c2 1 3 2 5 3 1 1 2 2 4 3v1l-1 1c-1-1-2-2-3-2v1l-3-2h-1v2c1 1 2 3 2 4h0l-1-1-1 1-1 2v-1l-1-1-1 1 1 1h-2l-1 2c1 1 2 1 2 3-1-1-1-1-3-2l-1-1h-1c0 3 0 5 1 7v2l1 1h0l-1-1h0l-2-3c0-1 0-2-1-2 0-2 0-3-1-4v-1h1l1 1c1-1 0-1 1-3l2 2v-1l-1-1 1-1h1v-1l-3-3c0-2 0-3-1-4l-1-2h1 3 1l3 1 1-1-1-1z" class="B"></path><path d="M588 304h1c2 1 3 2 5 3-3 0-4 0-6-1l1-1-1-1z" class="X"></path><path d="M575 258c2 0 3 0 4 1 2 2 4 3 6 4 1 0 2 1 3 2v-1c0-1-1-2-2-2v-1h3 1c2 5 4 11 5 15 1 2 2 4 2 6 1 2 2 5 5 6 3 2 8 4 11 4l-1 1-3-1c-3 0-6 0-9 1h-3l-1 1h-4c-2 1-4 1-6 1 0-1 1-1 2-1l-1-1c1-1 1-2 1-2 0-1 0-1 1-1h1l-1-1v-1c-1-2-2-2-4-3h-1-1c-2-2-6-2-8-5h0l-6-6h0l-2-2h0c-2-2-3-3-4-5s-2-2-4-3c1-1 2-1 2-1v-2h1v-1-1c2 0 3 0 4 1h2c1 1 2 1 3 1v-1c1 0 2 0 4 1h2l-1-1c-1 0-1-1-2-1l1-1z" class="J"></path><path d="M587 268c2 2 3 4 3 7h0l-1-1h-1c-1 0-2-3-2-3 0-1 0-2 1-3z" class="O"></path><path d="M588 291h1c1 1 4 1 6 1-2 1-3 1-4 2h1c-2 1-4 1-6 1 0-1 1-1 2-1l-1-1c1-1 1-2 1-2z" class="G"></path><path d="M591 282l-2-1 1-1h0l2 1h0l-1-2 1-1c0 1 1 3 3 3 0-1-1-2-1-3v-1l1-1c1 2 2 4 2 6-2 2 0 2 0 5-2-1-4-4-6-5h0z" class="Y"></path><path d="M575 258h0c2 2 3 2 5 3 4 2 4 5 7 7-1 1-1 2-1 3 0 0 1 3 2 3l1 4h0l-3-4c-1-1-2-2-2-3s0-2-1-4h-1c-1-2-3-3-4-5h-1l1-1h-1l-1-1c-1 0-1-1-2-1l1-1z" class="P"></path><path d="M591 282h0c2 1 4 4 6 5 0-3-2-3 0-5 1 2 2 5 5 6 3 2 8 4 11 4l-1 1-3-1c-3 0-6 0-9 1h-3c1-1 2-1 3-1v-1h-3c-1-1-2-2-4-3l-4-4c1 0 2 1 4 2v-1l-2-3z" class="l"></path><path d="M561 263v-2h1v-1-1c2 0 3 0 4 1h2c1 1 2 1 3 1v-1c1 0 2 0 4 1h2 1l-1 1h1c1 2 3 3 4 5h1c1 2 1 3 1 4s1 2 2 3l-1 1c1 1 2 3 3 5h-2v1c0 1 1 2 2 3h0c-1 0-2 0-3 1h-1-1c-2-2-6-2-8-5h0l-6-6h0l-2-2h0c-2-2-3-3-4-5s-2-2-4-3c1-1 2-1 2-1z" class="P"></path><path d="M561 263h1 1c1 2 0 1 1 2h0l1-1c4 2 4 4 7 7 2 3 4 5 7 8v1c-3-2-5-3-8-6l-3-3c-1 0 0 0-1 1h0c-2-2-3-3-4-5s-2-2-4-3c1-1 2-1 2-1z" class="G"></path><path d="M561 263v-2h1v-1-1c2 0 3 0 4 1h2c1 1 2 1 3 1v-1c1 0 2 0 4 1h2 1l-1 1h1c1 2 3 3 4 5h1c1 2 1 3 1 4s1 2 2 3l-1 1v-1c-1-1-3-1-3-2-2-1-4-3-5-5-3 1-5-1-7-3h0-1-3 0c-2-1-3-1-4-1h-1z" class="W"></path><path d="M584 271c-1 0-2-1-3-2-2-2-5-4-6-6l2-1h1c1 2 3 3 4 5h1c1 2 1 3 1 4z" class="T"></path><path d="M448 233l1 1h3 2c-1 1-1 1-1 2s0 1-1 2h2l2 1v1h-2l2 1c-1 1-1 1-2 1h0c1 1 2 1 2 1 0 1 0 1-1 2h4v1 1l1 1 1 1-2 1v1l6 1c1 0 2 1 3 2-1 0-2-1-3 0l1 1c1 0 1 1 2 1l-2 1c1 1 1 1 2 1v1h-1c-1-1-2 0-3 0h0l1 1c0 1-1 2-1 3h-2v2 1h3v1h-2v2h-1c-1 1-2 2-2 3l-2 2c1 1 1 1 1 2h1c2-1 5-1 7-1l-3 1v2l-3 2c-1 0-1 0-2 1s-3 1-5 1l-2 1-5 4c-2 1-4 3-7 4l-4 2h-3c-2 0-5 0-6 1h-2l-6-2h0l-1-1v-1c0-2 2-3 3-4l-1-1 1-1-2-1-3 1h-1c2-2 2-4 4-6h0c1-1 1-2 0-3l-1 1-1-1 2-1-1-1-1-1v-3-2h-2 0c-1-2-2-2-3-2l1-1 3-1-1-1v-1-1s1 0 2-1l-1-1c1-1 2-2 3-2l1-1c-2-2-4-5-7-7l1-1 2 2 6-1h3v-1h1c0-2 0-2-1-3h1v-1c1-1 4-2 6-2 2-2 5-3 8-4l6-3c1-1 1-1 2-1z" class="p"></path><path d="M416 249l6-1c0 1 0 3 1 4l-7-3z" class="D"></path><path d="M426 280c1-1 2-2 4-2l-1 4c-1 2-3 5-5 6-1 0-2 0-3 1l-3 1c0-2 2-3 3-4l-1-1 1-1 4-2 1-2z" class="O"></path><path d="M425 282c1 1 0 2 0 3-1 1-2 3-4 4l-3 1c0-2 2-3 3-4l-1-1 1-1 4-2z" class="J"></path><path d="M416 263c2-1 4-1 6-1v2c1 1 2 0 3 1h-1l1 1 1-1h0 3 0l1 1c1 1 1 11 1 12-1 1-1 2-2 3v1l1-4c-2 0-3 1-4 2l-1 2-4 2-2-1-3 1h-1c2-2 2-4 4-6h0c1-1 1-2 0-3l-1 1-1-1 2-1-1-1-1-1v-3-2h-2 0c-1-2-2-2-3-2l1-1 3-1z" class="j"></path><path d="M413 264c2 0 4 1 6 1h1v1l-1 1h1c1 1 1 0 1 1h2 0v2l-1 2-1 1h-1c0 1-1 1-1 2l-1 1-1-1 2-1-1-1-1-1v-3-2h-2 0c-1-2-2-2-3-2l1-1z" class="T"></path><path d="M419 265h1v1l-1 1h1c1 1 1 0 1 1h2 0v2h-1l-2 2c0-1-1-2-1-3l-1-1c0-1 1-2 1-3h0z" class="G"></path><path d="M421 273l1-1v2h1 0l2-1c1 2 0 3 1 4 1-1 2-2 3-4v-1c1 1 1 4 1 5v1c-2 0-3 1-4 2l-1 2-4 2-2-1-3 1h-1c2-2 2-4 4-6h0c1-1 1-2 0-3 0-1 1-1 1-2h1z" class="M"></path><path d="M419 275c0-1 1-1 1-2h1c0 2 1 2 1 5-2 1-3 3-4 5l5-3h3l-1 2-4 2-2-1-3 1h-1c2-2 2-4 4-6h0c1-1 1-2 0-3z" class="P"></path><path d="M448 233l1 1h3 2c-1 1-1 1-1 2s0 1-1 2h2l2 1v1h-2l2 1c-1 1-1 1-2 1h0c1 1 2 1 2 1 0 1 0 1-1 2h4v1 1l1 1 1 1-2 1v1c-1 1-2 1-3 2 1 0 2 1 2 2h0l-1 1c-1 0-2 0-3 1-3 1-5 1-7 3-2 1-5 2-7 4l-1-2h-2c2-4 5-7 9-10l-1-1c-8 3-14 3-22 1-1-1-1-3-1-4h3v-1h1c0-2 0-2-1-3h1v-1c1-1 4-2 6-2 2-2 5-3 8-4l6-3c1-1 1-1 2-1z" class="g"></path><path d="M442 243h3v1c-1 0-1 1-2 1-3 0-6 2-9 3 2-2 3-2 5-3h-1v-1l4-1z" class="f"></path><path d="M432 244c2-1 3-2 4-3h1l1 1v1 1 1h1c-2 1-3 1-5 3-1 0-2 1-4 1v1h-1c-1 0-2 0-3-1 1 0 1 0 1-1 1-2 2-2 4-3l1-1z" class="S"></path><path d="M432 244c2-1 3-2 4-3h1l1 1v1 1 1c-2 0-4 1-6-1z" class="g"></path><path d="M452 247h2l1-1h0c1 1 3 1 4 1l1 1 1 1-2 1v1c-1 1-2 1-3 2 1 0 2 1 2 2h0l-1 1c-1 0-2 0-3 1l-1-1v-1l-2-1-4 2v-1c2-1 7-4 8-7-2 0-3 0-5 1h0 0l2-2z" class="K"></path><path d="M451 254c1-1 2-2 3-2-1 1 0 0 0 1h2c1 0 2 1 2 2h0l-1 1c-1 0-2 0-3 1l-1-1v-1l-2-1z" class="Y"></path><path d="M430 242l1 3c-2 1-3 1-4 3 0 1 0 1-1 1 1 1 2 1 3 1h1c6 1 11 2 17-1v1l-2 1h0c-8 3-14 3-22 1-1-1-1-3-1-4h3v-1h1c0-2 0-2-1-3h1c1-1 3-1 4-2z" class="V"></path><path d="M448 233l1 1h3 2c-1 1-1 1-1 2s0 1-1 2h2l2 1v1h-2l2 1c-1 1-1 1-2 1h0c1 1 2 1 2 1 0 1 0 1-1 2h4v1 1c-1 0-3 0-4-1h0l-1 1h-2-2l-3 2-1-1c-3 1-5 2-8 1h0c2-2 4-2 7-4h0c1-1 2-2 4-2l-1-1-1-1c-1 1-2 1-3 1h-1l-1 1-4 1v-1-1l-1-1h-1c-1 1-2 2-4 3l-1 1-1-3c-1 1-3 1-4 2v-1c1-1 4-2 6-2 2-2 5-3 8-4l6-3c1-1 1-1 2-1z" class="K"></path><path d="M443 237c1-1 2-2 3-2s1 0 1 1-1 1-2 2c0 1 0 1-1 2h4l-1 1h-4 0c-1 0-2-1-3-2 1 0 2-1 3-1v-1z" class="J"></path><path d="M448 233l1 1h3 2c-1 1-1 1-1 2s0 1-1 2h2c-1 1-2 1-3 1h-3v-1l2-2-1-1c-1 0-2-1-3-1 1-1 1-1 2-1z" class="W"></path><path d="M450 247h-4c1-1 2-2 3-2s3-1 3-1l2-2h0c1 1 2 1 2 1 0 1 0 1-1 2h4v1 1c-1 0-3 0-4-1h0l-1 1h-2-2z" class="O"></path><path d="M426 243c1-1 4-2 6-2 2-2 5-3 8-4h3v1c-1 0-2 1-3 1 1 1 2 2 3 2h0 4l1-1c1 0 3 0 4 1-3 2-3 4-7 4 1-1 2-2 4-2l-1-1-1-1c-1 1-2 1-3 1h-1l-1 1-4 1v-1-1l-1-1h-1c-1 1-2 2-4 3l-1 1-1-3c-1 1-3 1-4 2v-1z" class="Z"></path><path d="M430 242l8-3 1-1 1 1c-1 1-2 1-4 2-1 1-2 2-4 3l-1 1-1-3z" class="d"></path><path d="M459 251l6 1c1 0 2 1 3 2-1 0-2-1-3 0l1 1c1 0 1 1 2 1l-2 1c1 1 1 1 2 1v1h-1c-1-1-2 0-3 0h0l1 1c0 1-1 2-1 3h-2v2 1h3v1h-2v2h-1c-1 1-2 2-2 3l-2 2c1 1 1 1 1 2h1c2-1 5-1 7-1l-3 1v2l-3 2c-1 0-1 0-2 1s-3 1-5 1l-2 1-5 4c-2 1-4 3-7 4l-4 2h-3c-2 0-5 0-6 1h-2l-6-2h0c3 0 6-1 9-2l3-6c2-7 3-14 6-22h2l1 2c2-2 5-3 7-4 2-2 4-2 7-3 1-1 2-1 3-1l1-1h0c0-1-1-2-2-2 1-1 2-1 3-2z" class="P"></path><path d="M458 261c1-1 1-1 3-1 0 2 0 2-1 3-2 1-4 2-6 2 1-2 2-2 3-3l1-1h0z" class="O"></path><path d="M459 251l6 1c-2 1-2 1-4 1v1h2v1h-5 0c0-1-1-2-2-2 1-1 2-1 3-2z" class="e"></path><path d="M454 282h1c0-2 3-4 4-5 1 0 1 0 2-1s2 0 3 0v2l-3 2c-1 0-1 0-2 1s-3 1-5 1z" class="G"></path><path d="M440 264c2-2 5-3 7-4h1c-1 1-2 2-3 2v1c0 1 0 1-1 2-3 3-4 6-6 9 0 1 0 1-1 1 1-3 4-7 4-10l-1-1h0z" class="K"></path><path d="M431 284c2-7 3-14 6-22h2l1 2h0c-4 5-8 14-7 20v1h-1l-1-1zm23-27c1-1 2-1 3-1h6c0 1 0 2-1 2-1 1-2 1-2 1-5 1-10 2-15 4v-1c1 0 2-1 3-2h-1c2-2 4-2 7-3z" class="Y"></path><path d="M454 257c1-1 2-1 3-1h6c0 1 0 2-1 2-1 1-2 1-2 1-2-1-3-2-5-2-2 1-5 2-7 3h-1c2-2 4-2 7-3zm-13 26l2-1v1l4-4c1-1 2-1 3 0h0c-1 2-3 3-5 5 2 0 4-2 5-3v1l-1 1v1c1-1 2-1 3-1l-5 4c-2 1-4 3-7 4 1-1 1-2 1-4v1l-1-1c1-1 2-2 3-2h1v-1l-2 1v-1l-1-1z" class="J"></path><path d="M458 261h0l-1 1c-1 1-2 1-3 3v1c-1 3-3 5-5 7l7-5c-1 2-6 7-6 9h1v1l-1 1c-1-1-2-1-3 0l-4 4v-1l-2 1 1 1v1l2-1v1h-1c-1 0-2 1-3 2l1 1v-1c0 2 0 3-1 4l-4 2h-3c-2 0-5 0-6 1h-2l-6-2h0c3 0 6-1 9-2l3-6 1 1h1l2-3h0l6-10v-1c4-6 10-9 17-10z" class="e"></path><path d="M441 272h2c3-4 6-6 11-9-3 2-5 5-7 7-4 3-6 7-8 11 3-3 5-6 8-8v1c-1 1-2 2-2 3-1 2-3 3-5 5-2 0-3 2-4 2l-1-1v-1h0l6-10z" class="c"></path><path d="M435 282v1l1 1c1 0 2-2 4-2-2 1-3 3-5 5-1 0-1 0-2 1 3 0 6-4 8-5l1 1v1l2-1v1h-1c-1 0-2 1-3 2l1 1v-1c0 2 0 3-1 4l-4 2h-3c-2 0-5 0-6 1h-2l-6-2h0c3 0 6-1 9-2l3-6 1 1h1l2-3z" class="Z"></path><path d="M428 290h0c2 0 6 0 7 1l-1 1-1 1c-2 0-5 0-6 1h-2l-6-2h0c3 0 6-1 9-2z" class="f"></path><path d="M568 89c3 0 5-3 7-4 0 1 1 1 0 2l-2 1v1h0v1l-1 1h0l2-2 1 4 5 11 2 4 1-1c3 6 4 14 4 21v13c0 1 0 2-1 3v1c1 3 1 5 0 7v1l-2 1-1-2v2c-1 0-1 0-2-1 0 0-1 1-2 1l-1-1v-1l-1 1h-2 0c-3-2-6-2-8-3h0c-5 0-9 4-12 7h-2-1c-1-1-1-2-2-2v1h-1l-1-2c-1 0-1-1-1-1l-1-1c-1-2-1 0-3 0v-3c-1-1-1-1-2 0h0l-2-1-1-1-1 1v-1l-1 1c1 1 1 2 2 3h0c1 1 2 1 3 1v2h-1c-1-1-1-1-2 0l-1 1v-3c-1-1-2-2-2-3v-1c-1-3 1-5 0-8v-1c-1 0 0-1 0-2l-1-3 1-1 3-3v-1c-1-1-1-1-2-1l4-1c0-2 3-7 4-9 5-11 15-21 24-29z" class="m"></path><path d="M561 112c2 4 1 9 1 14v1l-1-1v-2c-1-1-1-2-1-3 0-2-1-3 0-4l1-5z" class="E"></path><path d="M564 107h6v1l2-2c1 1 2 1 3 1h1 0c0 1 1 1 1 2 0 3 1 5 2 8 0 3 1 6 0 10 0 4 0 8-1 12-1-3-1-6-1-9 0-2-1-3-1-4-1-3 0-5-2-7-1-2 0-4-1-6-1-1 0-1-1-2h0l-1 2-1-1v-1c0-2 0-1-1-2-2 1-2 0-3 0s-3 1-4 1l2-3z" class="X"></path><path d="M570 111v-1c1 0 1-1 2-2l1 1h1v1c1 1 3 3 3 4v4 12c0-2-1-3-1-4-1-3 0-5-2-7-1-2 0-4-1-6-1-1 0-1-1-2h0l-1 2-1-1v-1z" class="F"></path><defs><linearGradient id="BZ" x1="581.891" y1="130.498" x2="581.696" y2="139.515" xlink:href="#B"><stop offset="0" stop-color="#5d5b5b"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#BZ)" d="M579 127l2-1h0c1 1 1 2 1 3h1l1 2v4 6h1l1 4c1 3 1 5 0 7v1l-2 1-1-2v2c-1 0-1 0-2-1 0 0-1 1-2 1l-1-1v-1l-1-1c0-4 1-8 1-12 1-4 1-8 1-12z"></path><path d="M578 153l1-8c1 2 1 4 2 6v2s-1 1-2 1l-1-1z" class="O"></path><path d="M579 127l2-1c0 6 0 13-2 19l-1 8v-1l-1-1c0-4 1-8 1-12 1-4 1-8 1-12z" class="k"></path><path d="M584 135v6h1l1 4c1 3 1 5 0 7v1l-2 1-1-2c-3-6-1-11 1-17z" class="N"></path><path d="M584 141h1l1 4c1 3 1 5 0 7v1l-2-1c-1-4 0-7 0-11z" class="U"></path><path d="M572 91l2-2 1 4 5 11c-1 2-1 3-1 5v8c-1-3-2-5-2-8 0-1-1-1-1-2h0-1c-1 0-2 0-3-1l-2 2v-1h-6c-1-2 2-4 3-6 1-1 1-2 0-4l5-6z" class="P"></path><path d="M573 99h1c1 2 1 3 0 5-1-1-1-2-2-3l1-2z" class="W"></path><path d="M568 102h1v2c1 0 1 1 1 2l1 1c0-2 0-2 2-3v1l1 1 1-2c1 1 2 2 2 4v1c0-1-1-1-1-2h0-1c-1 0-2 0-3-1l-2 2v-1h-6c1-2 3-3 4-5z" class="B"></path><path d="M572 91l2-2 1 4c-3 2-5 6-7 9-1 2-3 3-4 5-1-2 2-4 3-6 1-1 1-2 0-4l5-6z" class="O"></path><path d="M580 104l2 4 1-1c3 6 4 14 4 21v13c0 1 0 2-1 3v1l-1-4h-1v-6-4l-1-2h-1c0-1 0-2-1-3h0l-2 1c1-4 0-7 0-10v-8c0-2 0-3 1-5z" class="C"></path><path d="M580 104l2 4c1 4 1 7 1 10l1 13-1-2h-1c0-1 0-2-1-3h0l-2 1c1-4 0-7 0-10v-8c0-2 0-3 1-5z" class="e"></path><path d="M579 109c2 4 2 7 2 12 0 2 1 3 0 5h0l-2 1c1-4 0-7 0-10v-8z" class="W"></path><path d="M552 130l2-1h1l-1 1c1 1 1 0 2 0v1l-1 1 1 1c1-1 2-1 3-1v-1l1-1 5-1h1v1h-2v1h2c-2 2-5 3-7 6l5-2v-1h2 0v1c-1 1-1 3-1 4l4-1h0c-2 2-5 3-7 5l-1 1c1 1 2 1 5 2h0c1 1 2 1 3 2h-2c1 1 3 2 4 2 2 1 3 2 4 3h0c-3-2-6-2-8-3h0c-5 0-9 4-12 7h-2-1c-1-1-1-2-2-2v1h-1l-1-2c-1 0-1-1-1-1l-1-1c-1-2-1 0-3 0v-3c1-1 1-1 2-1l1-1v-4l1-5v-1h2v-2c1-2 2-3 3-5z" class="b"></path><path d="M565 139l4-1h0c-2 2-5 3-7 5l-1 1c-1 0-2 1-2 2l-5 4c1-3 2-5 4-7s5-3 7-4z" class="V"></path><path d="M547 137h2c-1 2-1 3-1 4l1 1 1-2c0 1 1 2 1 3l2-1c-1 2-1 2-1 4h1 2l-1 2-4 4-1-2v-3h-1v2l-1 1v-3h-1v-4l1-5v-1z" class="d"></path><path d="M548 144l1-1c1 1 1 1 2 1v1 3l1 1 1-2c0 1 0 1 1 1l-4 4-1-2 1-4c-1-1-1-2-2-2z" class="l"></path><path d="M547 137h2c-1 2-1 3-1 4-1 1-1 3-1 4 1-1 1 0 1-1 1 0 1 1 2 2l-1 4v-3h-1v2l-1 1v-3h-1v-4l1-5v-1z" class="f"></path><path d="M564 135v-1h2 0v1c-1 1-1 3-1 4-2 1-5 2-7 4v-1c-1 1-2 2-3 4h-2-1c0-2 0-2 1-4l4-4c1 0 1-1 2-1l5-2z" class="D"></path><path d="M564 135v-1h2 0v1c-1 1-1 3-1 4-2 1-5 2-7 4v-1c0-1 1-2 2-3l3-3h1v-1z" class="B"></path><path d="M553 157c2-3 4-9 8-10 1-1 1 0 2-1h3c1 1 2 1 3 2h-2c1 1 3 2 4 2 2 1 3 2 4 3h0c-3-2-6-2-8-3h0c-5 0-9 4-12 7h-2z" class="S"></path><path d="M552 130l2-1h1l-1 1c1 1 1 0 2 0v1l-1 1 1 1c1-1 2-1 3-1v-1l1-1 5-1h1v1h-2v1h2c-2 2-5 3-7 6-1 0-1 1-2 1l-4 4-2 1c0-1-1-2-1-3l-1 2-1-1c0-1 0-2 1-4v-2c1-2 2-3 3-5z" class="i"></path><g class="Q"><path d="M559 132h1l1 1-9 7c-1-1 0-2 0-4 1-1 3-2 4-3s2-1 3-1z"></path><path d="M568 89c3 0 5-3 7-4 0 1 1 1 0 2l-2 1v1h0v1l-1 1h0l-5 6c1 2 1 3 0 4-1 2-4 4-3 6l-2 3-1 2-1 5c-1 1 0 2 0 4 0 1 0 2 1 3v2l1 1h0l1 2h2l-5 1-1 1v1c-1 0-2 0-3 1l-1-1 1-1v-1c-1 0-1 1-2 0l1-1h-1l-2 1c-1 2-2 3-3 5v2h-2v1l-1 5v4l-1 1c-1 0-1 0-2 1-1-1-1-1-2 0h0l-2-1-1-1-1 1v-1l-1 1c1 1 1 2 2 3h0c1 1 2 1 3 1v2h-1c-1-1-1-1-2 0l-1 1v-3c-1-1-2-2-2-3v-1c-1-3 1-5 0-8v-1c-1 0 0-1 0-2l-1-3 1-1 3-3v-1c-1-1-1-1-2-1l4-1c0-2 3-7 4-9 5-11 15-21 24-29z"></path></g><path d="M567 97c1 2 1 3 0 4-1 2-4 4-3 6l-2 3-1 2-1 5c-1-1-1-1-1-2s0-1-1-2v-4s1-2 2-2c2-4 5-7 7-10z" class="M"></path><path d="M558 109v4c1 1 1 1 1 2s0 1 1 2c-1 1 0 2 0 4 0 1 0 2 1 3v2l1 1h0l1 2h2l-5 1-1 1v1c-1 0-2 0-3 1l-1-1 1-1v-1c-1 0-1 1-2 0l1-1h-1l-2 1v-2-1l1-6 3-7c0-2 1-3 2-5z" class="J"></path><path d="M554 124v-1l1-2c1 1 2 1 2 3 0 1 0 3 1 4-1 1-2 1-2 1v-1c0-1-1-1-1-2v-1l-1-1z" class="Y"></path><path d="M554 124l1 1v1c0 1 1 1 1 2v1s1 0 2-1h0c0 1 0 2-1 3h2v1c-1 0-2 0-3 1l-1-1 1-1v-1c-1 0-1 1-2 0l1-1h-1l-2 1v-2-1c1-1 2-2 2-3z" class="f"></path><path d="M558 109v4c1 1 1 1 1 2s0 1 1 2c-1 1 0 2 0 4 0 1 0 2 1 3v2l1 1h0l1 2h2l-5 1v-1h0v-1c0-1 0-1-1-2 0 1-1 1-1 1-1-3 0-6 0-9 0-2-1-2-2-4 0-2 1-3 2-5z" class="T"></path><path d="M544 118c2 0 4 0 5-2 1 0 1-1 1-2l1 1c0 1-1 3-1 5l1-1h1l1 1v1l-1 6v1 2c-1 2-2 3-3 5v2h-2v1l-1 5v4l-1 1c-1 0-1 0-2 1-1-1-1-1-2 0h0l-2-1-1-1-1 1v-1l-1 1c1 1 1 2 2 3h0c1 1 2 1 3 1v2h-1c-1-1-1-1-2 0l-1 1v-3c-1-1-2-2-2-3v-1c-1-3 1-5 0-8v-1c-1 0 0-1 0-2l-1-3 1-1 3-3v-1c-1-1-1-1-2-1l4-1c0-2 3-7 4-9z" class="V"></path><path d="M547 137c0-3 3-7 3-10l1-1c0 1 0 2 1 2v2c-1 2-2 3-3 5v2h-2z" class="S"></path><path d="M536 128l4-1c0 4-3 7-5 10l-1-3 1-1 3-3v-1c-1-1-1-1-2-1z" class="C"></path><path d="M540 142v-3c-1 1-1 1-2 1h-1c1-2 1-4 3-6 0-1 0-1 1-2v-1l1-1c1-3 2-6 4-9 0 1-1 3-1 4l-1 4h0l5-9c-1 4-2 7-4 10 1 3 0 6 1 8h1l-1 5v4l-1 1c-1 0-1 0-2 1-1-1-1-1-2 0h0l-2-1-1-1-1 1v-1c1-2 1-3 3-5z" class="c"></path><path d="M543 135c1-1 2-3 2-5 1 3 0 6 1 8h1l-1 5v4l-1 1c-1 0-1 0-2 1-1-1-1-1-2 0h0l-2-1-1-1-1 1v-1c1-2 1-3 3-5 0 1 0 1 1 2l-1 2 1 1h1c2-3 1-8 1-11v-1z" class="f"></path><path d="M543 135c1-1 2-3 2-5 1 3 0 6 1 8h1l-1 5v4l-1 1v-7l-1-1c-1-2 0-4-1-5z" class="d"></path><path d="M442 111c4-7 7-14 10-21l2 2 6 8c3 4 6 7 8 12 2 1 3 2 4 4 3 6 8 10 10 16l1-1c1 3 3 5 5 7 1 2 2 5 3 7s1 4 1 6l-2 1h0l-1-1c-1 2-1 3-1 5v1l3 9-3-3-1 2c1 1 1 2 1 2 1 2 1 4 2 5l-1 1v-1h-1c0 1 1 2 1 3 2 2 3 4 4 6l-1 1-2-2-1 1 1 2c-1 0-2-2-3-3h-2l-3-3-3-5c-1-2-4-4-6-5l-1-1h-2c-1-1-1-1-2-1-4-1-8 0-12 0h-3c-2 0-3 0-5-2v-2-2c0-1 1-2 2-2l-1-1h0c0-3 0-5 1-7h-1-1-1v-3c-1-3-1-3-3-4 0-2 0-5-1-6v-5c1-3 1-6 2-9-1-1-2-1-2-2l1-7h-1l-1 3h0v-5z" class="m"></path><path d="M465 116l2-2v1s1 1 1 2l3 5 1 2h-1c-1-1-3-2-4-3v1 2 1c-2-1-3-2-4-4 1-1 1 0 2-1h1c1-2 1-1 1-2l-2-2h0z" class="E"></path><path d="M462 149c0-2-2-2-2-3 2 0 5 0 7 1-2-4-5-6-8-9l9 5c1 2 1 4 2 5l3 5h0l2 3 3 3c-1 0-1 0-2 1l-2-3h0-1l-3-3h-2c-2-1-4-3-6-5z" class="b"></path><path d="M470 154l-6-6 1-1c2 1 3 3 5 5 1 1 1 1 3 1h0l2 3 3 3c-1 0-1 0-2 1l-2-3h0-1l-3-3z" class="S"></path><path d="M456 103v-2c1 0 3 0 4-1 3 4 6 7 8 12l6 13-2-2-1-1h0l-3-5c0-1-1-2-1-2v-1l-2 2c-1-1-2-3-3-3h-1v-2c-2-1-2-2-3-3l-1-1h-1v2c-2-1-2-2-3-2 0-2 0-2 1-2l1 3c1-2 1-3 1-5z" class="I"></path><path d="M456 103c1 1 1 2 2 3 0 1 0 0 1 1 1-1 0-2 1-4 2 1 3 3 4 5 0 1 1 2 1 4 1 0 1 1 2 2l-2 2c-1-1-2-3-3-3h-1v-2c-2-1-2-2-3-3l-1-1h-1v2c-2-1-2-2-3-2 0-2 0-2 1-2l1 3c1-2 1-3 1-5z" class="F"></path><path d="M448 110l1-1v1 2 1h1c-1 3 0 5 0 7-1 4-1 7-2 10 0 3 1 7 1 10l1 9h-1-1-1v-3c-1-3-1-3-3-4 0-2 0-5-1-6v-5c1-3 1-6 2-9v-1c0-3 1-6 2-9l1-2z" class="I"></path><path d="M443 131c1 2 2 9 3 9l1 1v2h1v-1c0-1 0-1 1-2l1 9h-1-1-1v-3c-1-3-1-3-3-4 0-2 0-5-1-6v-5z" class="G"></path><path d="M448 110l1-1v1 2 1h1c-1 3 0 5 0 7-1 4-1 7-2 10 0 3 1 7 1 10-1 1-1 1-1 2v1h-1v-2l-1-2v-1c0-3-1-6 0-9 0-2 0-4 1-6v-11l1-2z" class="E"></path><path d="M442 111c4-7 7-14 10-21l2 2 6 8c-1 1-3 1-4 1v2c0 2 0 3-1 5l-1-3c-1 0-1 0-1 2l-2 2-1 4h-1v-1-2-1l-1 1-1 2c-1 3-2 6-2 9v1c-1-1-2-1-2-2l1-7h-1l-1 3h0v-5z" class="M"></path><path d="M448 110v-2c0-1 4-10 6-10 1 2-3 7-3 11h0l-1 4h-1v-1-2-1l-1 1z" class="I"></path><path d="M453 152c2-1 3-2 4-2 1-1 4-1 5-1 2 2 4 4 6 5h2l3 3c1 1 2 3 2 5-1 1-1 2-2 3l1 1-1 1-1-1h-2c-1-1-1-1-2-1-4-1-8 0-12 0h-3c-2 0-3 0-5-2v-2-2c0-1 1-2 2-2l-1-1 4-2h-2 0c0-1 2-2 2-2z" class="Z"></path><path d="M466 162c3 0 5 1 7 3l1 1-1 1-1-1h-2c-1-1-1-1-2-1 0-1-1-2-2-3z" class="S"></path><path d="M468 154h2l3 3c1 1 2 3 2 5-2-1-4-4-6-6-1-1-1-1-2-1l1 2h0l-2-1h0c0-1 0-1-1-2h3z" class="R"></path><path d="M453 152c2-1 3-2 4-2 1-1 4-1 5-1 2 2 4 4 6 5h-3c1 1 1 1 1 2h0c-4-2-8-4-13-4z" class="o"></path><path d="M450 157c4-2 8-1 11 1 1 1 3 1 3 2l-1 1-2-1c-3 0-6 0-10 1h-3v-2c0-1 1-2 2-2z" class="p"></path><path d="M451 161c4-1 7-1 10-1l2 1c1 0 2 0 3 1s2 2 2 3c-4-1-8 0-12 0h-3c-2 0-3 0-5-2v-2h3z" class="H"></path><path d="M451 161c4-1 7-1 10-1v2c-2 1-5 3-6 2-2 0-3-2-4-3z" class="D"></path><path d="M468 112c2 1 3 2 4 4 3 6 8 10 10 16l1-1c1 3 3 5 5 7 1 2 2 5 3 7s1 4 1 6l-2 1h0l-1-1c-1 2-1 3-1 5v1l3 9-3-3-1 2c1 1 1 2 1 2 1 2 1 4 2 5l-1 1v-1h-1c0 1 1 2 1 3 2 2 3 4 4 6l-1 1-2-2-1 1 1 2c-1 0-2-2-3-3h-2l-3-3-3-5c-1-2-4-4-6-5l1-1-1-1c1-1 1-2 2-3 0-2-1-4-2-5h1 0l2 3c1-1 1-1 2-1l-3-3-2-3h0l-3-5c-1-1-1-3-2-5h0l-2-2c-1-3-5-5-7-8 2 1 5 4 7 4-1-2-4-3-5-5l1-2h2 1c-1-2-1-3-1-4h-1l1-1c1 1 2 1 4 2v-1l-1-1v-1-2-1c1 1 3 2 4 3h1l-1-2h0l1 1 2 2-6-13z" class="d"></path><path d="M466 137c-1-2-4-3-5-5l1-2h2c3 3 6 5 7 9h0l-3-2-1 1-1-1z" class="Q"></path><path d="M469 133c-1-2-1-3-1-4l1-1c3 2 5 4 6 7 0 2 0 3-1 4v1h-1l-1-3c-1-2-2-3-3-4z" class="V"></path><path d="M469 133l1-2 3 3c0 1 0 2-1 3-1-2-2-3-3-4z" class="D"></path><path d="M479 139l2 1 3 8c0 2 1 2 1 4v1h-1v-1 2h-2v-1h-1v-4-2h0l-1-1v-3l-1-4z" class="g"></path><path d="M468 143h0l-2-2c-1-3-5-5-7-8 2 1 5 4 7 4l1 1c1 2 3 6 6 7v-2-1l1 2c2-1 1-2 3-2v-3-2l2 2 1 4v3l1 1h0v2 4 1c-1 0-1-2-1-3h-1v3h0-2l-1 2h-1l-2-3h0l-3-5c-1-1-1-3-2-5z" class="c"></path><path d="M477 145l1 1v4 1c-1 0-1-1-2-1 1-2 1-3 1-5z" class="J"></path><path d="M470 148h0c1-1 1-1 1-2 2 1 3 4 4 6-1 1-1 1-2 1h0l-3-5z" class="T"></path><path d="M468 112c2 1 3 2 4 4 3 6 8 10 10 16l1-1c1 3 3 5 5 7 1 2 2 5 3 7s1 4 1 6l-2 1h0l-1-1c-1 2-1 3-1 5v1h0l-1-1c-1-5-1-10-2-14h-1c-1-6-4-11-7-15h0l5 12h-1c-3-4-4-10-7-14l-6-13z" class="V"></path><path d="M483 131c1 3 3 5 5 7 1 2 2 5 3 7l-1 1v4h0c-3-3-3-7-5-11 0-1-1-2-1-4-1-1-1-2-2-3l1-1z" class="o"></path><path d="M483 131c1 3 3 5 5 7 1 2 2 5 3 7l-1 1-3-6-3-5c-1-1-1-2-2-3l1-1z" class="D"></path><path d="M479 154v-3h1c0 1 0 3 1 3v-1h1v1h2l-2 2 2 1v-2h2c1 1 0 1 1 1l1 1h0l3 9-3-3-1 2c1 1 1 2 1 2 1 2 1 4 2 5l-1 1v-1h-1c0 1 1 2 1 3 2 2 3 4 4 6l-1 1-2-2-1 1 1 2c-1 0-2-2-3-3h-2l-3-3-3-5c-1-2-4-4-6-5l1-1-1-1c1-1 1-2 2-3 0-2-1-4-2-5h1 0l2 3c1-1 1-1 2-1l-3-3h1l1-2h2 0z" class="R"></path><path d="M482 156l2 1v-2h2c1 1 0 1 1 1l1 1h0l3 9-3-3-1 2-1-2c-2-2-4-5-5-7h1z" class="i"></path><path d="M482 156l2 1v-2h2c1 1 0 1 1 1l1 1-1 1-1-2-1 1c1 2 1 4 1 6-2-2-4-5-5-7h1z" class="h"></path><path d="M475 156h1l1-2h2 0c0 2-1 2 1 5 1 2 3 4 5 7v1l-2 3v-1c-2-3-5-4-6-8l2-1-1-1-3-3z" class="g"></path><path d="M473 157h1 0l2 3c1-1 1-1 2-1l1 1-2 1c1 4 4 5 6 8v1l3 2v1c1 1 2 1 3 2 2 2 3 4 4 6l-1 1-2-2-1 1 1 2c-1 0-2-2-3-3h-2l-3-3-3-5c-1-2-4-4-6-5l1-1-1-1c1-1 1-2 2-3 0-2-1-4-2-5z" class="o"></path><path d="M482 177l1-1c-1-1-3-3-4-5 4 2 6 5 8 9h-2l-3-3z" class="S"></path><path d="M475 162c2 1 2 2 3 4 2 1 3 2 4 4-2-2-5-3-7-4h-1l-1-1c1-1 1-2 2-3z" class="f"></path><path d="M486 173c1 1 2 1 3 2 2 2 3 4 4 6l-1 1-2-2v-1c-1-2-3-3-4-4v-2z" class="H"></path><path d="M553 210c4 0 6-1 9-2 3 0 5 0 7-1 1 0 2 0 3 1 1-1 2-1 3-1h1c2 1 4 1 5 1h1l1 1h0c2 2 4 2 6 4s4 4 6 5 4 3 5 4v1c1 1 1 1 1 2l-1 1-5-2c-3-2-7-4-10-7h0c-1-1-2-1-3-2-1 0-3-1-4-2h-2v1l-1 1c1 1 0 2 0 2s1 1 1 2v1c0 1 2 3 3 3l-1 1h-2v1 1h-1v1 4c1 1 0 1 1 2l2 3c0 1 1 1 1 2l-1 1-1-1 1 1 2 5c1 0 2 1 2 2h0c-1-1-2-1-2-1h-1c0 1 0 1-1 1v2l3 2h0l6 3-2 3h1l2 3 1 2h-3v1c1 0 2 1 2 2v1c-1-1-2-2-3-2-2-1-4-2-6-4-1-1-2-1-4-1l-1 1c1 0 1 1 2 1l1 1h-2c-2-1-3-1-4-1v1c-1 0-2 0-3-1h-2c-1-1-2-1-4-1v1 1h-1v2s-1 0-2 1c2 1 3 1 4 3h-1c-1 0-2 0-3-1l-1 1c0 2-1 2-2 3s-1 2-2 3l-2 4v1 1 1l-1 2c-1-2-1-2-1-4-1-3-1-6-3-8 0-2 0-4-1-5-2-3-4-6-5-10v-1-1-3-2h-1l-1 1v-5l-1-1c0-2 0-2 1-3l-1-1c-1 1-1 3-1 4-1 1 0 2 0 3v6c-1-1-2-3-2-5-1 0-2 1-3 1v-7l-2-1c-1-1-1-3-1-5l1-3c-1-1-2-1-2-2s0-2 1-3c0 1 1 1 2 1v-3l-1-8 1-1 2 2 1 2c1 1 1 1 3 1 0-2 1-2 1-3l1-3c1 1 0 1 1 1 1-1 1-2 2-3l1-1c0 2-1 3-1 5l2-2v-1c1-2 6-3 8-4h0 1z" class="B"></path><path d="M553 270l1 3-2 4c0-3 0-5 1-7z" class="I"></path><path d="M553 270l1-2c1 1 1 1 1 2h1c-1 1-1 2-2 3l-1-3z" class="X"></path><path d="M564 236l-1 4v2c-1 1-1 1-3 1-1 1-2 2-2 3h-2l1-2c1-1 2-2 2-3 1-3 3-3 5-5zm-6 29c-1 0-2 0-2-1l-1-1c-1 0-1-1-2-2v-3l1-1c0 2 0 3 1 5v-6c1-1 4-4 6-5 0-2 0-4 1-5l1-1c1 0 2-1 2-2 1-1 0-1 1-2h0c0-1 1-1 2-2h1v2l-1 1h1v2 1 1h0-2l-1 1c1 1 2 2 3 2v1c-2 0-2-1-3 0s-1 1-2 1c-2 0-3 1-4 2l-1 1c-1 0 0 0-1 1 0 1-1 1-1 3l1 1 1 1c0 2-1 3-1 5z" class="M"></path><path d="M564 251c1 0 1 0 2-1s1 0 3 0v-1c-1 0-2-1-3-2l1-1h2c0 1 1 2 2 3v1c1 0 2 0 2 1v1l-1 1-2-1h-2v1s1 1 2 1c2 1 3 3 5 4h0l-1 1c1 0 1 1 2 1l1 1h-2c-2-1-3-1-4-1v1c-1 0-2 0-3-1h-2c-1-1-2-1-4-1v1 1h-1v2s-1 0-2 1v1h-1c0-2 1-3 1-5l-1-1-1-1c0-2 1-2 1-3 1-1 0-1 1-1l1-1c1-1 2-2 4-2z" class="K"></path><path d="M558 265c0-2 1-3 1-5l-1-1-1-1c0-2 1-2 1-3 1-1 0-1 1-1l1-1c1-1 2-2 4-2 0 1 0 2-1 3-1 0-1 0-2 1v1c-1 0-1 0-1 1 1 1 6 1 8 2 1 1 2 1 3 1v1c-1 0-2 0-3-1h-2c-1-1-2-1-4-1v1 1h-1v2s-1 0-2 1v1h-1z" class="P"></path><path d="M549 250v-1l2 1c1-1 1-2 2-3h1v-1h1v-2h2l-1 2c-1 1-1 2-2 3l-1 1v4l-1-1v-2l-1 1c0 1 0 2-1 4v1h-1-1c0 1 0 1 1 2s2 3 3 4v2 3h-1c0 3 0 7 1 10v1 1l-1 2c-1-2-1-2-1-4-1-3-1-6-3-8 0-2 0-4-1-5-2-3-4-6-5-10v-1-1-3c1 1 1 2 2 3h1v-2c0-1 0 0 1-1l1 2c1-1 2-2 3-2z" class="W"></path><path d="M549 264c1 0 2 1 2 2v2c0 3 0 7 1 10v1 1l-1 2c-1-2-1-2-1-4h0v-1c0-1 1-2 0-4-1-3-1-6-1-9z" class="P"></path><path d="M549 264l-2-2v-3-4h1l1 2h-1c0 1 0 1 1 2s2 3 3 4v2 3h-1v-2c0-1-1-2-2-2z" class="T"></path><path d="M544 251c0-1 0 0 1-1l1 2c1-1 2-2 3-2l-1 3h-1v-1l-1 1c0 1 1 4 0 5v3h-1c-1-1-3-3-3-5l-1-2v-1-3c1 1 1 2 2 3h1v-2z" class="e"></path><path d="M541 250c1 1 1 2 2 3h1v-2c1 1 1 3 1 5h0l-1-1-1 1h-1l-1-2v-1-3zm37-11l2 5c1 0 2 1 2 2h0c-1-1-2-1-2-1h-1c0 1 0 1-1 1v2l3 2h0l6 3-2 3h1l2 3 1 2h-3v1c1 0 2 1 2 2v1c-1-1-2-2-3-2-2-1-4-2-6-4-1-1-2-1-4-1h0c-2-1-3-3-5-4-1 0-2-1-2-1v-1h2l2 1 1-1v-1c0-1-1-1-2-1v-1c-1-1-2-2-2-3h0v-1c1-1 1-1 2-3h1 2 1c2-1 2-1 3-3z" class="b"></path><path d="M580 250h1 0l6 3-2 3h1l2 3h-1-2 0l-7-5v-2h0v-2h2z" class="i"></path><path d="M580 250h1 0l6 3-2 3-5-6z" class="j"></path><path d="M578 239l2 5c1 0 2 1 2 2h0c-1-1-2-1-2-1h-1c0 1 0 1-1 1v2l3 2h-1-2v2h0v2l-1-1v1l-3 1-2-2 1-1v-1c0-1-1-1-2-1v-1c-1-1-2-2-2-3h0v-1c1-1 1-1 2-3h1 2 1c2-1 2-1 3-3z" class="c"></path><path d="M575 248c1 0 2 1 3 2v2h0v2l-1-1c-2-1-3-2-3-5h1z" class="S"></path><path d="M571 242h1l1 1c1 0 1 1 1 2 1 1 1 1 2 1l2 2 3 2h-1-2c-1-1-2-2-3-2l-2-1-1 1 1 1h-1c-1 0 0 0-1 1v-1c-1-1-2-2-2-3h0v-1c1-1 1-1 2-3z" class="J"></path><path d="M571 242h1l1 1c-1 1-1 2-1 3h-3v-1c1-1 1-1 2-3z" class="O"></path><path d="M578 239l2 5c1 0 2 1 2 2h0c-1-1-2-1-2-1h-1c0 1 0 1-1 1v2l-2-2c-1 0-1 0-2-1 0-1 0-2-1-2l-1-1h2 1c2-1 2-1 3-3z" class="Y"></path><path d="M576 246v-1l2-2h0c1 0 2 0 2 1 1 0 2 1 2 2h0c-1-1-2-1-2-1h-1c0 1 0 1-1 1v2l-2-2z" class="b"></path><path d="M556 219c1-2 3-3 4-5l3 1v2l2-2c2 1 2 1 3 3 0 2-1 2-1 4v1 1l1 1h0l-1 2v3c-1 1-1 2-2 3 0 1 0 2-1 2v1c-2 2-4 2-5 5 0 1-1 2-2 3h-2v2h-1v1h-1c-1 1-1 2-2 3l-2-1v1c-1 0-2 1-3 2l-1-2c-1 1-1 0-1 1v2h-1c-1-1-1-2-2-3v-2h-1l-1 1v-5l-1-1c0-2 0-2 1-3 0-1 0-1 1-1 0 1 0 2 1 4 0-2-1-4 0-6 0-1 2-2 2-3h2c0-1 1-1 2-2 2-1 3-1 5-2l3-4c1-1 2-2 3-4h-1l1-1-2-2z" class="Q"></path><path d="M550 245c-1-1-1-2-1-3v-1c1 1 0 1 1 1l1-1h1l1-1 1 1h0v1 2h-1-2-1v1z" class="D"></path><path d="M562 220s1 0 2-1c1 0 0-1 1-1 1 1 1 2 1 4-1 0-3 0-3 1-1 0-2 1-2 2l-4 4c1 0 1 1 2 1v1h-3 0c-1 1-3 1-5 2 1-1 3-2 4-4l2-2c1-2 3-5 5-7z" class="V"></path><path d="M556 219c1-2 3-3 4-5l3 1v2l-1 3c-2 2-4 5-5 7l-2 2-3 1 3-4c1-1 2-2 3-4h-1l1-1-2-2z" class="X"></path><path d="M565 215c2 1 2 1 3 3 0 2-1 2-1 4v1 1l1 1h0l-1 2v3c-1 1-1 2-2 3 0 1 0 2-1 2v1c-2 2-4 2-5 5 0 1-1 2-2 3h-2v2h-1v1h-1c-1 1-1 2-2 3l-2-1v1c-1 0-2 1-3 2l-1-2c-1 1-1 0-1 1v2h-1c-1-1-1-2-2-3v-2h-1l-1 1v-5l-1-1c0-2 0-2 1-3 0-1 0-1 1-1 0 1 0 2 1 4 0-2-1-4 0-6 0-1 2-2 2-3h2c0-1 1-1 2-2 2-1 3-1 5-2l3-1c-1 2-3 3-4 4v1h-2c1 2 1 3 3 4l-1 1c-1-1-2-1-3-2 0 1 1 1 0 2l-1-1h-1c0 2 0 5-1 7l2 2c1-1 1-1 1-2v1l2 1v-2-1h1 2 1v-2-1c1 1 2 1 2 1v-2h2l-1-1v-1h2c0-1 0-1-1-2l2 1h0l2-2v-1-2c1-1 0-2 1-3h1l-1-1c1-1 2-2 2-3v-1c0-1 0-1 1-2 0-2 0-3-1-4-1 0 0 1-1 1-1 1-2 1-2 1l1-3 2-2z" class="f"></path><path d="M545 245v3h-1c0-1 0-2-1-3v-2c-1-1-1-4 0-5 1-2 2-3 4-3 0-1 0-1 2-2v1c1 2 1 3 3 4l-1 1c-1-1-2-1-3-2 0 1 1 1 0 2l-1-1h-1c0 2 0 5-1 7z" class="D"></path><path d="M553 210c4 0 6-1 9-2 3 0 5 0 7-1 1 0 2 0 3 1 1-1 2-1 3-1h1c2 1 4 1 5 1h1l1 1h0c2 2 4 2 6 4s4 4 6 5 4 3 5 4v1c1 1 1 1 1 2l-1 1-5-2c-3-2-7-4-10-7h0c-1-1-2-1-3-2-1 0-3-1-4-2h-2v1l-1 1c1 1 0 2 0 2s1 1 1 2v1c0 1 2 3 3 3l-1 1h-2v1 1h-1v1 4c1 1 0 1 1 2l2 3c0 1 1 1 1 2l-1 1-1-1 1 1c-1 2-1 2-3 3h-1-2-1c-1 2-1 2-2 3v-1-2h-1l1-1v-2h-1 1c0-1 1-1 1-1 1-1 1-1 1-2h1v-2c1-3 0-6 1-8v-1c-1 0-1 0-2 1 0 1-1 1-1 2-1 1-1 1-2 1l-1 1v-3l1-2h0l-1-1v-1-1c0-2 1-2 1-4-1-2-1-2-3-3l-2 2v-2l-3-1c-1 2-3 3-4 5-1 0 0 0-1-1l1-1c-1-1-2-2-3-2l-1-1s1-1 1-2c-1 0-2 1-3 1h-1c-1 0-2 1-2 1l-3 1v-1c1-2 6-3 8-4h0 1z" class="W"></path><path d="M571 242v-1h1l-1-1c1-1 1-2 2-3 0-2 2-3 1-5 0-2 0-3 1-5v4c1 1 0 1 1 2l2 3c0 1 1 1 1 2l-1 1-1-1 1 1c-1 2-1 2-3 3h-1-2-1z" class="e"></path><path d="M574 242v-2c1-1 1-2 1-3v-1l1-1 1 1h1c0 1 1 1 1 2l-1 1-1-1 1 1c-1 2-1 2-3 3h-1z" class="c"></path><path d="M553 212l14-2c2-1 4-1 6-1 1 0 2 1 2 1l-1 1c-2 0-4 0-6 1l-8 2c-1 2-3 3-4 5-1 0 0 0-1-1l1-1c-1-1-2-2-3-2l-1-1s1-1 1-2z" class="F"></path><path d="M582 215c-1-1-1-2-2-2 1-1 2-1 3 0l8 4h0c1 1 2 2 4 2v-1c2 1 4 3 5 4v1c1 1 1 1 1 2l-1 1-5-2c-3-2-7-4-10-7h0c-1-1-2-1-3-2z" class="Y"></path><path d="M582 215c-1-1-1-2-2-2 1-1 2-1 3 0l8 4h0v1c0 1 1 1 2 2 1 0 2 1 3 2h-1l-6-3c-1-1-2-1-3-1l-1-1h0c-1-1-2-1-3-2z" class="K"></path><path d="M560 214l8-2c2-1 4-1 6-1v1h2v1c-1 0-1 0-2 1v2l-1 1c0 3 0 5 2 7-1 0-1 1-2 1s-1 0-2 1c0 1-1 1-1 2-1 1-1 1-2 1l-1 1v-3l1-2h0l-1-1v-1-1c0-2 1-2 1-4-1-2-1-2-3-3l-2 2v-2l-3-1z" class="B"></path><path d="M565 215l-1-1h1 1c2-1 5-2 7-1-1 1-1 2-2 3h-1l-1-1c-1-1-3 0-4 0z" class="X"></path><path d="M565 215c1 0 3-1 4 0l1 1c0 3 0 4-1 7h-2v-1c0-2 1-2 1-4-1-2-1-2-3-3h0z" class="P"></path><path d="M569 223h1s1-1 1-2 1-2 2-4c0 3 0 5 2 7-1 0-1 1-2 1s-1 0-2 1c0 1-1 1-1 2-1 1-1 1-2 1l-1 1v-3l1-2h0l-1-1v-1h2z" class="G"></path><path d="M542 213l1-1c0 2-1 3-1 5l2-2 3-1s1-1 2-1h1c1 0 2-1 3-1 0 1-1 2-1 2l1 1c1 0 2 1 3 2l-1 1c1 1 0 1 1 1l2 2-1 1h1c-1 2-2 3-3 4l-3 4c-2 1-3 1-5 2-1 1-2 1-2 2h-2c0 1-2 2-2 3-1 2 0 4 0 6-1-2-1-3-1-4-1 0-1 0-1 1l-1-1c-1 1-1 3-1 4-1 1 0 2 0 3v6c-1-1-2-3-2-5-1 0-2 1-3 1v-7l-2-1c-1-1-1-3-1-5l1-3c-1-1-2-1-2-2s0-2 1-3c0 1 1 1 2 1v-3l-1-8 1-1 2 2 1 2c1 1 1 1 3 1 0-2 1-2 1-3l1-3c1 1 0 1 1 1 1-1 1-2 2-3z" class="F"></path><path d="M530 232l1 3h1v-1l1-2c0-2 1-4 2-5l1 1-1 2c-1 0-1 1-1 1v1 1c-1 1-1 1-1 2v1l-2 2 1 1c1 0 3-1 4-1-2 3-2 6-1 9-1 0-2 1-3 1v-7l-2-1c-1-1-1-3-1-5l1-3z" class="j"></path><path d="M536 238c1-1 1-2 2-2v1c2-2 3-4 5-4l4-1c-1 1-2 1-2 2h-2c0 1-2 2-2 3-1 2 0 4 0 6-1-2-1-3-1-4-1 0-1 0-1 1l-1-1c-1 1-1 3-1 4-1 1 0 2 0 3v6c-1-1-2-3-2-5-1-3-1-6 1-9z" class="J"></path><path d="M542 213l1-1c0 2-1 3-1 5l2-2 3-1s1-1 2-1h1c1 0 2-1 3-1 0 1-1 2-1 2l1 1c1 0 2 1 3 2l-1 1c1 1 0 1 1 1l2 2-1 1c-1 0-1 0-1 1h-1c0 1 0 1-1 1-1 1-3 3-4 3-1 1-2 0-2 1h-2l-1 1h-2-1-1l1-1h2c1-1 1-1 2-1h1c-1-1-2-1-3-2h-1v-1l3-2-1-1h-1v1c-1 1-3 3-4 3l-1-1c-1-1-1-2-1-3h-1c0-2 1-2 1-3l1-3c1 1 0 1 1 1 1-1 1-2 2-3z" class="I"></path><path d="M544 215l3-1s1-1 2-1h1c1 0 2-1 3-1 0 1-1 2-1 2l1 1c-1 1-1 2-1 4h-1c-1-1-2-1-3-1l-1 1 1 1h-2v-1l-1-1-2 3h-1v-4h0l2-2z" class="E"></path><path d="M726 408h1c1-1 1-3 2-4l5-12 9-21c2-1 3-2 5-4 0 1 0 2-1 3l-2 5v1c0 1-1 2-1 3-1 0-1 1-1 1 0 2 0 1-1 2v1c-1 1-1 2-2 3v1 2l-3 6-3 6-1 3c-1 1-1 2-1 3h0L564 810c-2 2-2 4-3 7l-1 1v2c-1 1-1 0-1 1h0c-1 2-1 2-1 4-1 1-1 0-1 1v1c-1 3-3 3-5 5l1-2h-1c1-3 2-6 4-9l10-25 15-36 11-25 18-46 13-30 12-32 11-25 8-21-1-2-8 20h-1c-1 1-2 2-2 3l-2 2 1-4c1-5 4-9 5-14l17-41h1l1-5 12-26c1 2-1 6-1 8h0v-1l1-1c0-1 0-2 1-2v-1c1-1 1-2 2-3v-1-1c0-1 0-1 2-1 0-1 1-4 2-4l5-13 42-101 1 1-6 14z" class="c"></path><path d="M682 511c0-1 1-4 2-4l5-13 42-101 1 1-6 14-72 173-1-2-8 20h-1c-1 1-2 2-2 3l-2 2 1-4c1-5 4-9 5-14l17-41h1l1-5 12-26c1 2-1 6-1 8h0v-1l1-1c0-1 0-2 1-2v-1c1-1 1-2 2-3v-1-1c0-1 0-1 2-1z" class="V"></path><path d="M663 545h1l1-5 12-26c1 2-1 6-1 8h0v-1l1-1c0-1 0-2 1-2v-1c1-1 1-2 2-3v-1-1c0-1 0-1 2-1-2 4-14 32-15 34l-10 25c-1 2-4 7-4 9l-8 20h-1c-1 1-2 2-2 3l-2 2 1-4c1-5 4-9 5-14l17-41z" class="P"></path><path d="M641 600c3-2 4-5 5-8 8-14 9-30 18-43 1-2 2-3 3-4l-10 25c-1 2-4 7-4 9l-8 20h-1c-1 1-2 2-2 3l-2 2 1-4z" class="G"></path><path d="M487 189c1 0 1 0 1 1l1 1c1 1 1 1 1 2l-1 1h0-3v1c2 1 4 3 5 5h0c2 1 3 1 5 3v2l1 3h1l-1 8h0l-1 6v5l1 1v1c-1 1-1 2-2 3l-1 1h-1-1v-1c-2 1-2 1-4 1h0l1 4c1 1 2 3 4 3h0c0 3 0 5 1 7v1c0 3-2 5-2 8 0 2-1 3-2 4h0l-3 3-1 3v2 4l-1 3c0 1 0 2-1 3l-5 1v2l-1 1-4-2h0l2-1 1-1h0c-1-2-3-2-4-3h-6c-2 0-5 0-7 1h-1c0-1 0-1-1-2l2-2c0-1 1-2 2-3h1v-2h2v-1h-3v-1-2h2c0-1 1-2 1-3l-1-1h0c1 0 2-1 3 0h1v-1c-1 0-1 0-2-1l2-1c-1 0-1-1-2-1l-1-1c1-1 2 0 3 0-1-1-2-2-3-2l-6-1v-1l2-1-1-1-1-1v-1-1h-4c1-1 1-1 1-2 0 0-1 0-2-1h0c1 0 1 0 2-1l-2-1h2v-1l-2-1h-2c1-1 1-1 1-2s0-1 1-2h-2v-1h0l-1-1 1-1c1-1 1-1 1-3h-2v-2l-1-1c1 0 1-1 1-2l1-1c-1-2-2-5-3-7l2-2v-1c-1 0-2 1-2 1h-1l-1 1-2 1c-1-1-2 0-3 0v-1l4-3h1l1-2 2-2-1-1 1-2v-1l-1-1v-1h1l-1-1 2-2 1-2h2c2 0 3-1 4 0 1 0 1-1 2-1 2 0 4 1 6 0l3 1 1-2c1 0 2 0 3-1s1-1 3-1h-1l2-1s1-1 2-1h1c1 1 2 2 3 2s1 0 2-1h1c1-1 1-1 1-2z" class="j"></path><path d="M475 264h1v1c0 1 1 1 1 2l1 2h-1v2 2 2 3c-1-2-3-2-4-3l2-2v1l1-1c-2-2-1-6-1-9z" class="M"></path><path d="M460 235c1 2 3 4 4 5 1 2 1 2 2 3l2 1c1 0 1 1 2 2 1 2 2 3 3 4l1 1v1c0 1-1 1-2 2v-4c-1-2-4-3-5-5l-1-1h-1l1 1c-1 1-1 1-1 2v1c0-1-1-1-1-2l-2-2c0-1-1-3-1-3-1-1-2-2-2-3l1-3z" class="E"></path><path d="M460 272c0-1 1-2 2-3h1v-2h2l3 1c2 1 4 4 5 6-4 0-7 1-10-1-1-1-2-1-3-1h0z" class="F"></path><path d="M474 249l1 1c1 2 0 2 2 4h1l1 3 2 1c0 1 0 2 1 3v-2c2 1 1 5 2 7h-1l-1 1c-1-1-1-1-1-2-1 1-1 2-1 4h-1-1l-1-2c0-1-1-1-1-2v-1h-1 0c-1 0-1 0-1-1-1-1-1-3 0-4h1v-4c-1 0-1 0-2-1h-1c1-1 2-1 2-2v-1l-1-1 1-1z" class="M"></path><path d="M479 257l2 1c0 1 0 2 1 3 0 1 0 1-1 2v-3l-1 1v1c-1-2-1-3-1-5z" class="T"></path><path d="M474 249c1 2 1 3 1 5 1 1 1 2 2 2 1 3 0 6 0 9 1-1 1-2 1-3h1l1 4v3h-1-1l-1-2c0-1-1-1-1-2v-1h-1 0c-1 0-1 0-1-1-1-1-1-3 0-4h1v-4c-1 0-1 0-2-1h-1c1-1 2-1 2-2v-1l-1-1 1-1z" class="B"></path><path d="M484 266c1-1 1-2 2-4l1 1-1 3v2 4l-1 3c0 1 0 2-1 3l-5 1v2l-1 1-4-2h0l2-1 1-1h0v-3-2-2-2h1 1 1c0-2 0-3 1-4 0 1 0 1 1 2l1-1h1z" class="c"></path><path d="M477 273l2-3c0 1 1 1 1 1h1v-1c1 2 1 3 0 5-1 1-3 2-4 3h0v-3-2z" class="K"></path><path d="M484 266c1-1 1-2 2-4l1 1-1 3v2 4l-1 3-1-1c0-1-1-2-1-3l-1-1h-1v1h-1s-1 0-1-1l-2 3v-2-2h1 1 1c0-2 0-3 1-4 0 1 0 1 1 2l1-1h1z" class="P"></path><path d="M481 270c1 0 1-1 1-2l1 1 2-2 1 1v4l-1 3-1-1c0-1-1-2-1-3l-1-1h-1z" class="K"></path><path d="M466 237l1 1v1h1l1 1c0 2 1 3 2 4l1 1 1 1s1 1 2 1l1-1v3c1 0 3 1 4 2v2h2l1 1h0 1c1 1 1 1 1 3l1-1c0-1 0-2 1-3h0l1 2c1-1 1-2 2-3v3h0c-1 3-3 5-4 7h-1v-2h-1 0c-1 0-1-1-1-2-1 0 0 0-1 1v2c-1-1-1-2-1-3l-2-1-1-3h-1c-2-2-1-2-2-4l-1-1-1 1c-1-1-2-2-3-4-1-1-1-2-2-2v-3h-1c-1 0-1 0-1-1v-1l-1-1 1-1z" class="O"></path><path d="M449 215l2-2c1 3 1 5 2 7v2h1c1 1 1 2 2 4h0 1v3c1 1 2 4 2 6h1l-1 3c0 1 1 2 2 3 0 0 1 2 1 3l2 2c0 1 1 1 1 2h0c2 2 3 3 4 5l1 1c0 2-1 2-2 4l1 1v1h-1v1l-2 1-1 1-2 1h1c-1 1-1 1-2 1v-2h2c0-1 1-2 1-3l-1-1h0c1 0 2-1 3 0h1v-1c-1 0-1 0-2-1l2-1c-1 0-1-1-2-1l-1-1c1-1 2 0 3 0-1-1-2-2-3-2l-6-1v-1l2-1-1-1-1-1v-1-1h-4c1-1 1-1 1-2 0 0-1 0-2-1h0c1 0 1 0 2-1l-2-1h2v-1l-2-1h-2c1-1 1-1 1-2s0-1 1-2h-2v-1h0l-1-1 1-1c1-1 1-1 1-3h-2v-2l-1-1c1 0 1-1 1-2l1-1c-1-2-2-5-3-7z" class="G"></path><path d="M453 222h1c1 1 1 2 2 4h0 1v3c1 1 2 4 2 6h1l-1 3c-2-2-2-2-3-2l-1-1h1v-2c-1-1 0-1-1-2-2-2 0-1 0-3 0 0-2-2-2-3v-3z" class="j"></path><path d="M456 209l-1-1 1-1 2 2 2 2c-1 0-2 0-3 1l1 1v1c2 0 3 0 4 1 4 3 5 6 8 9v2h1c1 2 2 3 4 4h0v3c-2 1-4 3-6 4v-1c0-1 1-1 1-2-1 0-2 1-4 1h0l1-1c-1-1-1-1-2-1 0 1 0 3 1 4l-1 1 1 1v1c0 1 0 1 1 1h1v3l-2-1c-1-1-1-1-2-3-1-1-3-3-4-5h-1c0-2-1-5-2-6v-3h-1 0c-1-2-1-3-2-4h-1v-2c-1-2-1-4-2-7v-1h1c0-2 0-3 2-5l1 2h1z" class="Y"></path><path d="M457 226v-1h1 1l1 1-1 2c1 1 2 2 2 4 1 1 0 2 2 4 1 0 1 1 2 2l1 1v1c0 1 0 1 1 1h1v3l-2-1c-1-1-1-1-2-3-1-1-3-3-4-5h-1c0-2-1-5-2-6v-3z" class="T"></path><path d="M465 233h-2v-1-1c0-1 0-1 1-1l-1-1-1-1c0-2 1 0 1-2h-1v-1h3c-1-1-2-3-2-4h1l2 2 3 3h1 1c1 2 2 3 4 4h0v3c-2 1-4 3-6 4v-1c0-1 1-1 1-2-1 0-2 1-4 1h0l1-1c-1-1-1-1-2-1z" class="D"></path><path d="M467 230c0-1 0-1-1-2l1-1 2 1 1 1-3 1z" class="N"></path><path d="M470 229c1 1 2 1 3 2v1h-1-2c-2 0-3 0-4-1 0-1 0-1 1-1l3-1z" class="C"></path><path d="M456 209l-1-1 1-1 2 2 2 2c-1 0-2 0-3 1l1 1v1c2 0 3 0 4 1-1 1-1 2-1 3-1 1-2 2-2 4l1 2h-1l-1-1c0 1 0 1 1 2h-1-1v1h-1 0c-1-2-1-3-2-4h-1v-2c-1-2-1-4-2-7v-1h1c0-2 0-3 2-5l1 2h1z" class="j"></path><path d="M455 209h1l1 1v1c-1 0-1 0-1 1l-2 2-1-2c0-2 1-2 2-3z" class="F"></path><path d="M451 212h1c0-2 0-3 2-5l1 2c-1 1-2 1-2 3l1 2v4 4h-1v-2c-1-2-1-4-2-7v-1z" class="E"></path><path d="M458 214c2 0 3 0 4 1-1 1-1 2-1 3-1 1-2 2-2 4l1 2h-1l-1-1c0 1 0 1 1 2h-1-1v1h-1 0c-1-2-1-3-2-4v-4l1-1c0 2 0 2 1 4l1-2-1-1 1-1h2l1-1c-1-1-1-2-2-2z" class="I"></path><path d="M475 230c1 0 4 1 4 2 2 0 4 0 6 1l1-1c1 0 1 0 2 1l1 4c1 1 2 3 4 3h0c0 3 0 5 1 7v1c0 3-2 5-2 8 0 2-1 3-2 4h0l-3 3-1-1c-1 2-1 3-2 4-1-2 0-6-2-7 1-1 0-1 1-1 0 1 0 2 1 2h0 1v2h1c1-2 3-4 4-7h0v-3c-1 1-1 2-2 3l-1-2h0c-1 1-1 2-1 3l-1 1c0-2 0-2-1-3h-1 0l-1-1h-2v-2c-1-1-3-2-4-2v-3l-1 1c-1 0-2-1-2-1l-1-1-1-1c-1-1-2-2-2-4l-1-1h-1v-1l-1-1c-1-1-1-3-1-4 1 0 1 0 2 1l-1 1h0c2 0 3-1 4-1 0 1-1 1-1 2v1c2-1 4-3 6-4v-3h0z" class="k"></path><path d="M479 232c2 0 4 0 6 1v1 3h1v-2h1l1 5c1 0 1 1 2 1h1 1v6l-1-4h0c-1 0-2 1-2 2v1l-1-1-1-2c0-1-1-1-1-2-1 0-1 0-2 1l1-2c0-2-1-3-2-4v-1c0-1 0-2-1-2l-3-1z" class="g"></path><path d="M481 243v-1h-1v1h-1c0-3 1-4 3-7h1c1 1 2 2 2 4l-1 2c1-1 1-1 2-1 0 1 1 1 1 2l1 2 1 1v-1c0-1 1-2 2-2h0l1 4c-1 1-1 3-2 5-1 1-1 2-2 3l-1-2h0c-1 1-1 2-1 3l-1 1c0-2 0-2-1-3h-1 0l-1-1v-4l3-4c-1 0-2 0-3-1l-1-1z" class="l"></path><path d="M481 243v-1h-1v1h-1c0-3 1-4 3-7h1c1 1 2 2 2 4l-1 2c1-1 1-1 2-1 0 1 1 1 1 2 0 2 0 4-1 6l-1-1c0-1 1-3 1-4l-1-1v2c-1 0-2 0-3-1l-1-1z" class="L"></path><path d="M475 230c1 0 4 1 4 2l3 1c1 0 1 1 1 2v1h-1c-2 3-3 4-3 7h1v-1h1v1l1 1c1 1 2 1 3 1l-3 4v4h-2v-2c-1-1-3-2-4-2v-3l-1 1c-1 0-2-1-2-1l-1-1-1-1c-1-1-2-2-2-4l-1-1h-1v-1l-1-1c-1-1-1-3-1-4 1 0 1 0 2 1l-1 1h0c2 0 3-1 4-1 0 1-1 1-1 2v1c2-1 4-3 6-4v-3h0z" class="S"></path><path d="M481 243l1 1c1 1 2 1 3 1l-3 4h-1v-4-2z" class="V"></path><path d="M473 241h-1v-2h-1 0c1-2 2-3 3-5l2-1 3 1 2 1v1h-1c-1 1 0 1-1 2-2 0-3 2-5 3v-1c-1 0-1 1-1 1z" class="L"></path><path d="M482 233c1 0 1 1 1 2v1h-1c-2 3-3 4-3 7h1v-1h1v1 2l-1 1h-3v-2l-1 1-1-1v-1l-1 1-1-1v-1-1s0-1 1-1v1c2-1 3-3 5-3 1-1 0-1 1-2h1v-1l-2-1c1 0 2-1 3-1z" class="h"></path><path d="M474 241c2-1 3-3 5-3 0 1-1 2-1 4l-3 1c-1-1-1-1-1-2z" class="N"></path><path d="M460 211h1 8l8 2 2 1 4 1c1 0 2 0 3 1l1-1 2 1h0v-1l3 3 3-1h0c0-1 0-1 1-2l1 1-1 6v5l1 1v1c-1 1-1 2-2 3l-1 1h-1-1v-1c-2 1-2 1-4 1h0c-1-1-1-1-2-1l-1 1c-2-1-4-1-6-1 0-1-3-2-4-2-2-1-3-2-4-4h-1v-2c-3-3-4-6-8-9-1-1-2-1-4-1v-1l-1-1c1-1 2-1 3-1z" class="j"></path><path d="M486 232c-3-2-4 0-6-1-1 0-1-1-2-2h1 4c2 1 4 1 5 3h4c-2 1-2 1-4 1h0c-1-1-1-1-2-1z" class="E"></path><path d="M477 213l2 1c-1 1-3 2-3 3l-1 1h0c1 1 1 1 1 2l1 2h0l-2 1c0 2 0 2 2 3l1 1h1l-1 1c-3-2-4-3-5-5l-1-1-1-2 2-2c1-2 1-2 3-3h0l1-2z" class="B"></path><path d="M460 211h1 8l8 2-1 2h0c-2 1-2 1-3 3l-2 2 1 2 1 1c-1 0-2 1-3 1-3-3-4-6-8-9-1-1-2-1-4-1v-1l-1-1c1-1 2-1 3-1z" class="p"></path><path d="M468 216c1 0 3 1 5 2l-2 2c-1-2-1-2-3-2v-2z" class="F"></path><path d="M458 213h1 2c1 0 2 1 3 1 1 1 2 1 3 1l1 1v2c2 0 2 0 3 2l1 2 1 1c-1 0-2 1-3 1-3-3-4-6-8-9-1-1-2-1-4-1v-1z" class="E"></path><path d="M468 218c2 0 2 0 3 2l1 2-2 1c-1-2-2-3-2-5z" class="r"></path><path d="M479 214l4 1c1 0 2 0 3 1l1-1 2 1h0v-1l3 3 3-1h0c0-1 0-1 1-2l1 1-1 6v5l1 1v1c-1 1-1 2-2 3l-2-2v1l-1-1v-1c0-1-1-1-1-1 0-2 0-3 1-4-1 0-2-1-3-2 0 1 1 2 0 4h0c1 2 1 2 1 4h-1c-1-1-3-1-5-2h0 0l1-2h2v-1l-2-1-1 1h-2l-2-1c-2-1-2-1-3-2h0l-1-2c0-1 0-1-1-2h0l1-1c0-1 2-2 3-3z" class="T"></path><path d="M493 230l1-1v-2l1-1 1 1 1 1v1c-1 1-1 2-2 3l-2-2zm-4-8l-1-2c0-1 1-1 2-1 2 0 3 1 4 3v1 1c-1 0-1 0-1 1l-1-1c-1 0-2-1-3-2z" class="p"></path><path d="M485 224c1-1 1-1 0-3 0-1-3-3-4-3l1-1c2 2 4 4 5 6l2 3h0c1 2 1 2 1 4h-1c-1-1-3-1-5-2h0 0l1-2h2v-1l-2-1z" class="F"></path><path d="M487 189c1 0 1 0 1 1l1 1c1 1 1 1 1 2l-1 1h0-3v1c2 1 4 3 5 5h0c2 1 3 1 5 3v2l1 3h1l-1 8h0l-1-1c-1 1-1 1-1 2h0l-3 1-3-3v1h0l-2-1-1 1c-1-1-2-1-3-1l-4-1-2-1-8-2h-8-1l-2-2-2-2-1 1 1 1h-1l-1-2c-2 2-2 3-2 5h-1c-1 0-2 1-2 1h-1l-1 1-2 1c-1-1-2 0-3 0v-1l4-3h1l1-2 2-2-1-1 1-2v-1l-1-1v-1h1l-1-1 2-2 1-2h2c2 0 3-1 4 0 1 0 1-1 2-1 2 0 4 1 6 0l3 1 1-2c1 0 2 0 3-1s1-1 3-1h-1l2-1s1-1 2-1h1c1 1 2 2 3 2s1 0 2-1h1c1-1 1-1 1-2z" class="d"></path><path d="M489 201c-1-2-2-3-3-4h-3l-3-4h2c0 1 1 1 3 2v-1l-1-1h1 2c1-1 0 0 1 0l1 1h-3v1c2 1 4 3 5 5l-2 1z" class="R"></path><path d="M478 201c1 0 2 1 3 0l-1-1h1c0 1 1 1 1 2l3 3h0l-1-4c1 0 0 0 1 1 1 0 2 1 3 1l1 3c1 1 1 2 1 4h0c-1-1-1 0-1-1s-1-2-2-3h-1v1h-1c0 1 1 2 2 3l-1 1c-1 1-2 1-4 1v-1-1l-1-2h1v-1l2 1v-1h0c0-1-2-2-3-3-2 0-2-2-3-3z" class="i"></path><path d="M488 203s-1-1-1-2c-1-1-1 0 0-1 1 0 2 3 3 5l1-1c-1-1-1-2-2-3l2-1h0l2 5 2 2c0 1 0 2-1 3h0 0v-3c-2 2-2 4-2 6-1 1-1 1-2 1l-1-1c0-2-1-3-2-4v1c-1-1-2-2-2-3h1v-1h1c1 1 2 2 2 3s0 0 1 1h0c0-2 0-3-1-4l-1-3z" class="S"></path><path d="M469 196c3-1 5-1 8 0h1l2 2c-1 1-3 1-3 3-2-1-3-1-5-1-2 2-4 1-7 1l-3 1c0-1 0 0-1-1v-1c1 0 1-1 2-1s0 0 1-1c-1 0-2-1-3-1h0c-3-1-4-1-7-1 2 0 3-1 4 0 1 0 1-1 2-1 2 0 4 1 6 0l3 1z" class="e"></path><path d="M469 196c3-1 5-1 8 0h1l2 2c-1 1-3 1-3 3-2-1-3-1-5-1-2 2-4 1-7 1h1c3-2 7-2 10-2l-1-1c-1-1-2-2-4-1l-1 1h-3l1-1-1-1-1-1 3 1z" class="f"></path><path d="M465 201c3 0 5 1 7-1 2 0 3 0 5 1h1c1 1 1 3 3 3 1 1 3 2 3 3h0v1l-2-1v1h-1l1 2v1l-10-2c-2 0-4-1-5-1l-5-1h-1l-1-1c1-2 2-2 4-3-1 0-2 0-2-1h-1 1l3-1z" class="Q"></path><path d="M465 201c3 0 5 1 7-1 2 0 3 0 5 1h1c1 1 1 3 3 3 1 1 3 2 3 3h0v1l-2-1v1h-1l-3-3-3-1c-3 0-5-2-7-2-1 1 0 2 0 3-2 0-3 0-4 1 1 0 2 1 3 2l-5-1h-1l-1-1c1-2 2-2 4-3-1 0-2 0-2-1h-1 1l3-1z" class="H"></path><path d="M491 200c2 1 3 1 5 3v2l1 3h1l-1 8h0l-1-1c-1 1-1 1-1 2h0l-3 1-3-3v1h0l-2-1-1 1c-1-1-2-1-3-1l-4-1-2-1-8-2c-1 0-1-1-2-1h-1c-1-1-2-1-4-2v-1l5 1c1 0 3 1 5 1l10 2v1c2 0 3 0 4-1l1-1v-1c1 1 2 2 2 4l1 1c1 0 1 0 2-1 0-2 0-4 2-6v3h0 0c1-1 1-2 1-3l-2-2-2-5z" class="Z"></path><path d="M454 196c3 0 4 0 7 1h0c1 0 2 1 3 1-1 1 0 1-1 1s-1 1-2 1v1c1 1 1 0 1 1h-1 1c0 1 1 1 2 1-2 1-3 1-4 3l1 1h1v1c2 1 3 1 4 2h1c1 0 1 1 2 1h-8-1l-2-2-2-2-1 1 1 1h-1l-1-2c-2 2-2 3-2 5h-1c-1 0-2 1-2 1h-1l-1 1-2 1c-1-1-2 0-3 0v-1l4-3h1l1-2 2-2-1-1 1-2v-1l-1-1v-1h1l-1-1 2-2 1-2h2z" class="k"></path><path d="M456 200c1-1 2-1 4-1v2l-2 1-1-1-1-1z" class="O"></path><path d="M449 200l2-2 2 1v1h3l1 1c-2 0-3 0-4 1s-2 1-3 1l-1-1v-1h1l-1-1z" class="K"></path><path d="M461 211v-2l-4-3c1-1 2-1 3-2 0-1 0-1 1-2h1c0 1 1 1 2 1-2 1-3 1-4 3l1 1h1v1c2 1 3 1 4 2h1c1 0 1 1 2 1h-8z" class="e"></path><path d="M447 211l3-1c0-2 2-3 3-4l-1-1c1-2 5-3 7-3-1 1-3 2-4 4l1 1-1 1 1 1h-1l-1-2c-2 2-2 3-2 5h-1c-1 0-2 1-2 1h-1l-1 1-2 1c-1-1-2 0-3 0v-1l4-3h1z" class="G"></path><path d="M644 185h1c0-1 3 0 4 0h10 51 114 61v29c0 3 1 8 0 11h-1c-2-2-6-5-7-7l-1-2-1-2v-9-9c-10 1-21 0-30 0h-60-33-95v4 4 6h-2c0 1 0 2 1 3l-1 1c3 1 8 0 12 1h-17l1-21c-2-3-5-6-7-9z" class="n"></path><path d="M737 194c7-1 16 0 24 0h49c-2 1-23 1-27 1l2 1h-33c1 0 3 0 4-1-6 0-13 0-19-1z" class="E"></path><path d="M810 194h66v22l-1-2v-9-9c-10 1-21 0-30 0h-60l-2-1c4 0 25 0 27-1zm-159 0c1 0 1 1 2 0 4-1 9 0 14 0h24 1 31 14c6 1 13 1 19 1-1 1-3 1-4 1h-95v4 4 6h-2c0 1 0 2 1 3l-1 1c3 1 8 0 12 1h-17l1-21z" class="m"></path><path d="M651 194c1 0 1 1 2 0 4-1 9 0 14 0h24 1-37c-1 2 0 6 0 8 0 1 1 2 1 3l1-1v6h-2c0 1 0 2 1 3l-1 1c3 1 8 0 12 1h-17l1-21z" class="C"></path><path d="M655 202c0 1 1 2 1 3l1-1v6h-2v-8z" class="F"></path><path d="M204 241c5 5 9 11 13 18 1 2 2 5 4 7v-1c0-1-1-3-2-4l-6-10h1c4 6 7 12 11 18h1c1 2 3 4 5 6v-3l3 4 21 40v1c1 0 1 1 1 1l4 10c1-1 0-1 1 0 2 2 4 5 5 8v1c0 2 2 5 3 7 4 7 8 13 13 19 7 8 16 15 25 21-2 1-4 1-6 1-1-1-3-2-4-3-2-1-4-4-6-5h-2l2 2h-2c-1 0-2-2-3-3-3-1-6-5-8-7l-1 1c-1-1-2-2-2-3l-1 2h0c1 1 1 2 1 3 1 2 3 4 3 7h-1l-1 1-2-3h0c0 1 1 2 1 4 3 3 5 7 7 11 0 1 1 2 1 3l-1 1-1 1 11 25v1c-1 1 0 3 0 5 1 3 3 5 4 8-1 3 0 4 1 7 1 4 3 9 6 14l30 74-1 2-31-77v1h-1c-2 0-4 2-5 3l-2-1-17-42c-2-2-3-5-4-8l-6-14-6-14-16-37c3-2 5-4 8-4l-14-33-7-17c-1-3-2-6-4-8-4-11-11-22-18-31l-5-8v-2z" class="N"></path><path d="M263 366l7 16 3 6c-3 2-4 5-7 7l-6-14c1-5 1-11 3-15z" class="Z"></path><path d="M259 353c2 1 2 3 3 5l2 3h1c2 2 4 5 4 7 0 1 0 1 1 2l4 7c0 1 1 2 1 4 3 3 5 7 7 11 0 1 1 2 1 3l-1 1-1 1 11 25v1c-1 1 0 3 0 5-1-1-2-3-3-5-1-4-3-7-4-10l-3-7-4-8v-2l-5-11-4-10c-2-2-4-5-4-8-1-2-3-4-3-6l-3-8z" class="h"></path><path d="M275 381c3 3 5 7 7 11 0 1 1 2 1 3l-1 1-1 1c-3-6-5-10-6-16z" class="R"></path><path d="M221 266v-1c0-1-1-3-2-4l-6-10h1c4 6 7 12 11 18 3 5 6 10 8 16 1 3 2 6 4 9l11 27 11 27 6 13h-1l-2-3c-1-2-1-4-3-5l-3-6-1-4c-1-1-1-1-1-2l-1-2-1-2-3-6v-1c-2-3-2-6-4-8-1-2 0 0 0-1l-1-2-3-7c-1-2-1-1-1-2l-1-2-3-8-1-5c-1-1-2-3-2-4-1-1-1-3-2-4l-1-3-5-12-1-1c-1-2-2-3-3-5z" class="L"></path><defs><linearGradient id="Ba" x1="255.48" y1="340.224" x2="250.398" y2="375.177" xlink:href="#B"><stop offset="0" stop-color="#2f2e2f"></stop><stop offset="1" stop-color="#4c4c4c"></stop></linearGradient></defs><path fill="url(#Ba)" d="M244 344c3-2 5-4 8-4l11 26c-2 4-2 10-3 15l-16-37z"></path><defs><linearGradient id="Bb" x1="269.307" y1="405.541" x2="302.27" y2="455.84" xlink:href="#B"><stop offset="0" stop-color="#5b5b5b"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#Bb)" d="M266 395c3-2 4-5 7-7l7 17c2 3 3 7 4 10l7 17 10 24v1h-1c-2 0-4 2-5 3l-2-1-17-42c-2-2-3-5-4-8l-6-14z"></path><path d="M266 395c3-2 4-5 7-7l7 17h-1c-1-1-1-3-1-4h-1c0 4-3 3-4 6-1 1 3 8 3 10-2-2-3-5-4-8l-6-14z" class="O"></path><path d="M226 269c1 2 3 4 5 6v-3l3 4 21 40v1c1 0 1 1 1 1l4 10c1-1 0-1 1 0 2 2 4 5 5 8v1c0 2 2 5 3 7 4 7 8 13 13 19 7 8 16 15 25 21-2 1-4 1-6 1-1-1-3-2-4-3-2-1-4-4-6-5h-2l2 2h-2c-1 0-2-2-3-3-3-1-6-5-8-7l-1 1c-1-1-2-2-2-3l-1 2h0c1 1 1 2 1 3 1 2 3 4 3 7h-1l-1 1-2-3h0l-4-7c-1-1-1-1-1-2 0-2-2-5-4-7l-6-13-11-27-11-27c-2-3-3-6-4-9-2-6-5-11-8-16h1z" class="W"></path><path d="M231 272l3 4 21 40v1l-1-1-2-2c0-1-1-2-2-4v1c0 2 1 4 3 6 3 4 4 10 6 15 3 8 6 16 11 23l-1 2c-2-1-5-7-5-10-1-1-1-2-2-3l-5-12c-4-9-8-20-13-28h-1c-1-3-4-7-6-10h0c-1-4-2-8-2-12l-4-7v-3z" class="Z"></path><path d="M235 282c4 4 5 10 7 15 0 2 2 3 2 5 1 0 1 0 1 1l-1 1h0-1c-1-3-4-7-6-10h0c-1-4-2-8-2-12z" class="c"></path><defs><linearGradient id="Bc" x1="302.122" y1="365.308" x2="273.723" y2="370.307" xlink:href="#B"><stop offset="0" stop-color="#5a5858"></stop><stop offset="1" stop-color="#777"></stop></linearGradient></defs><path fill="url(#Bc)" d="M270 355c-5-7-8-15-11-23-2-5-3-11-6-15-2-2-3-4-3-6v-1c1 2 2 3 2 4l2 2 1 1c1 0 1 1 1 1l4 10c1-1 0-1 1 0 2 2 4 5 5 8v1c0 2 2 5 3 7 4 7 8 13 13 19 7 8 16 15 25 21-2 1-4 1-6 1-1-1-3-2-4-3-2-1-4-4-6-5h-2l2 2h-2c-1 0-2-2-3-3-3-1-6-5-8-7l-1 1c-1-1-2-2-2-3l-1-1c-2-3-4-6-5-9l1-2z"></path><path d="M270 355c-5-7-8-15-11-23-2-5-3-11-6-15-2-2-3-4-3-6v-1c1 2 2 3 2 4l2 2 1 1c1 0 1 1 1 1l4 10 3 8c1 2 2 4 4 7-1 0-1 0-2-1 5 13 13 25 24 35l2 2h-2c-1 0-2-2-3-3-3-1-6-5-8-7l-1 1c-1-1-2-2-2-3l-1-1c-2-3-4-6-5-9l1-2z" class="Q"></path><path d="M270 355l5 8-1 3c-2-3-4-6-5-9l1-2z" class="H"></path><path d="M123 185c12-1 25 0 37 0h66 104 52v26c0 3 1 10 0 14h-2l-6-6v-13c0-2 0-11-1-13h-2v1c0 2 0 2-1 3h0l-222-1c-4 0-8 0-12 1h-1v2l-2 1h0-6l2-1c3-2 3-3 3-6-1 0-2 0-3-1-2-2-5-4-6-7z" class="a"></path><path d="M132 193h7 232v1c0 2 0 2-1 3h0l-222-1c-4 0-8 0-12 1h-1v2l-2 1h0-6l2-1c3-2 3-3 3-6z" class="B"></path><path d="M132 193h7c-2 1-4 0-5 1h0c0 1 1 1 1 2h3 10c-4 0-8 0-12 1h-1v2l-2 1h0-6l2-1c3-2 3-3 3-6z" class="P"></path><path d="M657 200v-4h95 33 60c9 0 20 1 30 0v9h-5c-2 0-8 1-10 1-1-1-1-2-2-2h0c-1-1-2-1-3-1h-18c-3 1-7 0-10 0-9 0-17-1-26 2v-1c-7 3-16 8-21 12h-4v-1h-1v-1l-2-1v-2h0 0c-2-2-3-5-5-8h0v-1h5c-3 0-9-1-12 0v1c2 0 3 1 4 2-1 0-2 1-3 1h-2c-1 1-1 1-2 1s-2 1-2 1l-6-2-1 2c-1 0-3 0-4 1h-1c-2 0-3 1-5 2l-1-1c0 1-1 1-1 2 1 2 1 4 3 6 0 1 2 2 2 3v1l1 2 1 1 2 12 1 10-1 18c0-2-1-3-1-4v-2c-1-3-1-7-2-11v-1l-1 1v4c1 2 1 5 0 6v-2l-2 1c-3-8-6-15-11-23-2-1-4-3-5-4-5-4-9-7-14-9l-1 1-3-1c-2-1-6-2-9-3-10-3-20-3-30-3-4-1-9 0-12-1l1-1c-1-1-1-2-1-3h2v-6-4z" class="n"></path><path d="M747 202h14v1c2 0 3 1 4 2-1 0-2 1-3 1h-2c-1 1-1 1-2 1s-2 1-2 1l-6-2-2-1c-3-2-4-2-7-2l6-1z" class="B"></path><path d="M747 202h14v1c-2 0-6-1-7 0s0 1-1 2c-2-1-5-1-6-3z" class="P"></path><path d="M722 202l19 1c3 0 4 0 7 2l2 1-1 2c-1 0-3 0-4 1h-1c-2 0-3 1-5 2l-1-1c0 1-1 1-1 2-2-3-5-5-9-7 0-1-4-2-6-3z" class="m"></path><path d="M748 205l2 1-1 2c-1 0-3 0-4 1h-1c-2 0-3 1-5 2l-1-1c3-2 7-3 10-5z" class="U"></path><path d="M773 202l64 1c-3 1-7 0-10 0-9 0-17-1-26 2v-1c-5 0-12 3-16 5v-1c1-1 2-1 2-2-1-1-2-2-3-2-4 0-7 2-10 4 0 1-1 2-1 3h0c-2-2-3-5-5-8h0v-1h5z" class="j"></path><path d="M773 211c0-1 1-2 1-3 3-2 6-4 10-4 1 0 2 1 3 2 0 1-1 1-2 2v1c4-2 11-5 16-5-7 3-16 8-21 12h-4v-1h-1v-1l-2-1v-2h0z" class="U"></path><path d="M773 211h4 2 0c1-1 1-1 2-1h1l-2 2c-2 1-3 2-4 3h-1v-1l-2-1v-2z" class="L"></path><path d="M657 200c2 3 1 6 1 9 1 0 4 1 5 0h-1v-1-1-1h2c1-1 2-1 3-1 1-1 4-1 5-1 11-3 24-3 36-2h11c1 0 2-1 3 0 2 1 6 2 6 3 4 2 7 4 9 7 1 2 1 4 3 6 0 1 2 2 2 3v1l1 2 1 1 2 12 1 10-1 18c0-2-1-3-1-4v-2c-1-3-1-7-2-11v-1l-1 1v4c1 2 1 5 0 6v-2l-2 1c-3-8-6-15-11-23-2-1-4-3-5-4-5-4-9-7-14-9l-1 1-3-1c-2-1-6-2-9-3-10-3-20-3-30-3-4-1-9 0-12-1l1-1c-1-1-1-2-1-3h2v-6-4z" class="a"></path><path d="M667 205c1-1 4-1 5-1 11-3 24-3 36-2h11l5 5c1 1 1 2 2 2l-2 2c-10-7-22-8-34-8h-11c-3 1-8 2-11 2h-1z" class="E"></path><defs><linearGradient id="Bd" x1="690.235" y1="210.86" x2="689.807" y2="226.598" xlink:href="#B"><stop offset="0" stop-color="#141212"></stop><stop offset="1" stop-color="#363535"></stop></linearGradient></defs><path fill="url(#Bd)" d="M655 210h2c13 0 26-1 39 2 5 1 10 4 14 5 6 3 10 6 15 10l-1 3c-5-4-9-7-14-9l-1 1-3-1c-2-1-6-2-9-3-10-3-20-3-30-3-4-1-9 0-12-1l1-1c-1-1-1-2-1-3z"></path><path d="M656 213c18-1 37 0 54 8l-1 1-3-1c-2-1-6-2-9-3-10-3-20-3-30-3-4-1-9 0-12-1l1-1z" class="q"></path><defs><linearGradient id="Be" x1="696.422" y1="201.801" x2="728.933" y2="227.484" xlink:href="#B"><stop offset="0" stop-color="#1c1a1a"></stop><stop offset="1" stop-color="#4d4c4c"></stop></linearGradient></defs><path fill="url(#Be)" d="M719 202c1 0 2-1 3 0 2 1 6 2 6 3 4 2 7 4 9 7 1 2 1 4 3 6 0 1 2 2 2 3v1l1 2 1 1 2 12 1 10-1 18c0-2-1-3-1-4v-2c-1-3-1-7-2-11v-1l-1 1v4c1 2 1 5 0 6v-2l-2 1c-3-8-6-15-11-23-2-1-4-3-5-4l1-3c-5-4-9-7-15-10 3 0 4 1 6 2v1c2 1 5 2 6 4 2 0 2 1 4 2l1-1c-3-3-6-6-10-7-1-1-2-2-2-3h-1c-10-7-22-9-34-10 3-1 6-1 10-1 3 1 6 0 10 1 8 2 14 5 21 9h4 2l-3-3 2-2c-1 0-1-1-2-2l-5-5z"></path><path d="M727 214c6 5 8 13 11 20v1c-1-1-3-4-3-6-2-6-8-11-14-15h4 2z" class="L"></path><path d="M714 215c13 7 23 17 28 32v1 4c1 2 1 5 0 6v-2l-2 1c-3-8-6-15-11-23-2-1-4-3-5-4l1-3c-5-4-9-7-15-10 3 0 4 1 6 2v1c2 1 5 2 6 4 2 0 2 1 4 2l1-1c-3-3-6-6-10-7-1-1-2-2-2-3h-1z" class="C"></path><path d="M725 227c2 3 5 5 7 8-1 0-1 0-2-1l-1-1v1c-2-1-4-3-5-4l1-3z" class="P"></path><path d="M729 234v-1l1 1c1 1 1 1 2 1 5 6 8 14 10 21l-2 1c-3-8-6-15-11-23z" class="T"></path><path d="M719 202c1 0 2-1 3 0 2 1 6 2 6 3 4 2 7 4 9 7 1 2 1 4 3 6 0 1 2 2 2 3v1l1 2 1 1 2 12 1 10-1 18c0-2-1-3-1-4v-2c-1-3-1-7-2-11v-1c-2-5-2-10-4-14l-1 1c-3-7-5-15-11-20l-3-3 2-2c-1 0-1-1-2-2l-5-5z" class="C"></path><path d="M728 205c4 2 7 4 9 7 1 2 1 4 3 6-1 1-1 2-1 2 0-1-1-2-2-3l-1 1v-1c-3-3-6-7-8-10v-2z" class="a"></path><path d="M724 207c6 4 8 8 11 14 2 4 3 8 4 12l-1 1c-3-7-5-15-11-20l-3-3 2-2c-1 0-1-1-2-2z" class="G"></path><defs><linearGradient id="Bf" x1="501.388" y1="132.452" x2="488.243" y2="162.694" xlink:href="#B"><stop offset="0" stop-color="#9a9898"></stop><stop offset="1" stop-color="#c0bfbe"></stop></linearGradient></defs><path fill="url(#Bf)" d="M507 122l5-6c3 3 5 7 9 9l-1 1v4h1c1 0 2 1 3 1h0v1c1 0 2 1 2 2l1-1h1c1 1 1 2 1 3 1-1 1-2 3-2l-1 1v1h1l2-2 1 3c0 1-1 2 0 2v1c1 3-1 5 0 8v1c0 1 1 2 2 3v3l1-1c1-1 1-1 2 0-1 2-1 4-3 7l-1 1v1c-1 3-7 10-7 13v1l1 1-1 1h-1c0 1-1 3-1 4l-4 4h-2v1c1 1 1 2 2 3h1c0 2 1 4 0 6l2 4-2 5-1 1 1 1 1-1 1 1 4-4c0 2 1 3 0 5 0 2-1 6 0 8l1 8v3c-1 0-2 0-2-1-1 1-1 2-1 3s1 1 2 2l-1 3-3-3-3-1v-1h-2l-1-1h-2c0-1-1-1-1-1-1 0-1 1-2 1-1-1 0-1-2-2l-2 2h-2c-1 0-1 0-3 1v-1l-1 1 1-2v-1l-2 1h-1l1-3-4 2c-1 1-1 1-3 1l-1-1v-5l1-6h0l1-8h-1l-1-3v-2c-2-2-3-2-5-3h0c-1-2-3-4-5-5v-1h3 0l1-1c0-1 0-1-1-2l1-2c-1-1-1-2-1-3s-2-2-2-3c-1 0-1-1-2-1l-1-1 1-1h2c1 1 2 3 3 3l-1-2 1-1 2 2 1-1c-1-2-2-4-4-6 0-1-1-2-1-3h1v1l1-1c-1-1-1-3-2-5 0 0 0-1-1-2l1-2 3 3-3-9v-1c0-2 0-3 1-5l1 1h0l2-1c0-2 0-4-1-6s-2-5-3-7c0-2 0-3-1-4 0-1-1-2-1-3v-1-1c2-1 6-1 8-1l1 1c1-1 1-1 1-2h1v1c1 0 1 0 2-1s1 0 2 0 2-1 3-1c1-1 2-3 3-4z"></path><path d="M504 126c1 1 1 1 0 2v3l-2 1c0-1 0-2-1-2v2l-2 2-1-1c-2-1-1 1-2 1-2 0-2-1-3 0h-1c-1-1-3-3-4-3s-2-1-2-1v-1c2-1 6-1 8-1l1 1c1-1 1-1 1-2h1v1c1 0 1 0 2-1s1 0 2 0 2-1 3-1z" class="U"></path><path d="M492 151l-1 1h1 0l2-4c1 4 3 10 1 15v2l1 1-1 1c0-1-2-3-3-3 0 1 0 2 1 3s1 2 1 2c1 1 1 1 0 1l-3-4-3-9v-1c0-2 0-3 1-5l1 1h0l2-1z" class="D"></path><path d="M507 122v11c1 0 1 0 2-1v-1c1 1 1 1 0 2-1 2-1 4-1 7v8c-1-2-1-4-1-6l-1 1v6c0 2 0 4-1 5h0c-3-3-1-7-3-11v-1h0c-2 2-1 7-1 10h-1v-4l-1-1c-1 1-1 1-1 2l1 1v3h-1c0-1 0-3-1-4 0-1-1-1-1-2l1-1-1-1 1-1h0v2h1v-2h1l1 1h0c0-1 1-4 2-5 0 1 0 1 1 2v1l1-1c0-2 0-5-1-7v-2h-1v-1l2-1v-3c1-1 1-1 0-2 1-1 2-3 3-4z" class="h"></path><path d="M507 122v11c1 0 1 0 2-1v-1c1 1 1 1 0 2-1 2-1 4-1 7-1-2-1-4-1-6s-1-3-2-5c-1 2-1 3-3 4v-1l2-1v-3c1-1 1-1 0-2 1-1 2-3 3-4z" class="L"></path><path d="M498 153h1v-3l-1-1c0-1 0-1 1-2l1 1v4c0 4 0 7 2 11v4l-1 1v1c1 0 1 1 1 2s1 2 1 3h0l-1 1c1 1 1 2 1 4h-1c-2-1-2-2-3-4s-1-3-2-3c0 2 2 5 1 7l-2-5v6c-2-3-3-6-5-9l-1 1c-1-1-1-3-2-5 0 0 0-1-1-2l1-2 3 3 3 4c1 0 1 0 0-1 0 0 0-1-1-2s-1-2-1-3c1 0 3 2 3 3l1-1v-1c0 1 0 1 1 2 0-3 1-6 0-10 0-1 0-1 1-2v-1-1z" class="d"></path><path d="M488 167c2 1 4 2 4 4v1l1 1c0 1 1 1 2 2l-1-2 1-1c0 1 1 1 1 2v6c-2-3-3-6-5-9l-1 1c-1-1-1-3-2-5z" class="S"></path><path d="M499 175c1-1 1-1 1-2 1-1 0-6 0-7l1-1v3 1c1 0 1 1 1 2s1 2 1 3h0l-1 1c1 1 1 2 1 4h-1c-2-1-2-2-3-4z" class="R"></path><path d="M498 153h1v-3l-1-1c0-1 0-1 1-2l1 1v4c0 4 0 7 2 11h-1c-1-2-2-3-3-5 0 4 1 9 0 12h-1c-1-1-1-2-2-3l1-1v-1c0 1 0 1 1 2 0-3 1-6 0-10 0-1 0-1 1-2v-1-1z" class="H"></path><defs><linearGradient id="Bg" x1="502.749" y1="166.5" x2="502.752" y2="141" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#b8b7b7"></stop></linearGradient></defs><path fill="url(#Bg)" d="M500 152h1c0-3-1-8 1-10h0v1c2 4 0 8 3 11h0c1 4 1 7 1 11h0l-1-1-1 1 1 2c0 1 0 2 1 3 1 2 1 6 2 8v6h1l-1 4c-1-1-1-2-1-3-1 1-1 1-1 2v1l-1-2-1-1-2-2-2-5v4l-1-1c-1-1-1-1-1-2 1-2-1-5-1-7 1 0 1 1 2 3s1 3 3 4h1c0-2 0-3-1-4l1-1h0c0-1-1-2-1-3s0-2-1-2v-1l1-1v-4c-2-4-2-7-2-11z"></path><path d="M503 174l1 3h1c0-3-2-5-2-8 1 0 1 1 1 1 0 3 2 5 2 7l2 1v6h1l-1 4c-1-1-1-2-1-3-1 1-1 1-1 2v1l-1-2-1-1-2-2-2-5v4l-1-1c-1-1-1-1-1-2 1-2-1-5-1-7 1 0 1 1 2 3s1 3 3 4h1c0-2 0-3-1-4l1-1z" class="Z"></path><path d="M506 177l2 1v6h1l-1 4c-1-1-1-2-1-3v-1l-1-7z" class="g"></path><path d="M502 183c1-1 1-2 2-2h0c1 1 2 3 3 3v1c-1 1-1 1-1 2v1l-1-2-1-1-2-2z" class="c"></path><path d="M490 172l1-1c2 3 3 6 5 9v-6l2 5c0 1 0 1 1 2l1 1v-4l2 5 2 2 1 1 1 2v1l3 7c-1 1-1 2-1 3h0l-1 2h-2v4c0 1 1 2 1 4l-2 1c-1-2-3-4-5-5-1 1-1 1-1 3h-1l-1-3v-2c-2-2-3-2-5-3h0c-1-2-3-4-5-5v-1h3 0l1-1c0-1 0-1-1-2l1-2c-1-1-1-2-1-3s-2-2-2-3c-1 0-1-1-2-1l-1-1 1-1h2c1 1 2 3 3 3l-1-2 1-1 2 2 1-1c-1-2-2-4-4-6 0-1-1-2-1-3h1v1l1-1z" class="J"></path><path d="M490 172l1-1c2 3 3 6 5 9 1 1 1 2 1 3-2 0-1-2-3-2 0 1 1 4 1 5l-1-1c-1-1-1-2-2-3l1-1c-1-2-2-4-4-6 0-1-1-2-1-3h1v1l1-1z" class="d"></path><path d="M487 183c-1 0-1-1-2-1l-1-1 1-1h2c1 1 2 3 3 3l-1-2 1-1 2 2c1 1 1 2 2 3l-1 1c1 1 1 1 1 2l-1 1-1-2v1c1 2 0 5 0 7l-1 1c-1 0-1-1-2-2l1-1c0-1 0-1-1-2l1-2c-1-1-1-2-1-3s-2-2-2-3z" class="Z"></path><path d="M491 196c1 1 1 2 2 2 1-1 0-2 0-2l1-1c1 1 2 1 3 3v-1l-1-2c1-1 1-2 1-3l1 3c1 1 0 2 0 3-1 1 0 2 0 4 0 1 1 2 1 3-1 1-1 1-1 3h-1l-1-3v-2c-2-2-3-2-5-3h0c-1-2-3-4-5-5v-1h3 0c1 1 1 2 2 2z" class="K"></path><path d="M501 197c-2-4 0-8-2-11h0 1c0 1 1 2 1 3 0 3 2 7 2 10 1 1 0 2 0 3s1 2 2 3c0 1 1 2 1 4l-2 1c-1-2-3-4-5-5 0-1-1-2-1-3 0-2-1-3 0-4l1 1 2-2z" class="O"></path><path d="M501 197c0 2 1 4 1 6-2-1-2-2-3-4l2-2zm-1-15v-4l2 5 2 2 1 1 1 2v1l3 7c-1 1-1 2-1 3h0l-1 2h-2v4c-1-1-2-2-2-3s1-2 0-3c0-3-2-7-2-10l1-1c0-1-1-5-2-6z" class="e"></path><path d="M506 197l-2-1c-1-2-1-4-1-6l1-1c0-1 0-2 1-3l1 2v1l3 7c-1 1-1 2-1 3-1-1-1-2-2-2z" class="O"></path><path d="M506 189l3 7c-1 1-1 2-1 3-1-1-1-2-2-2 0-3-1-5 0-8z" class="T"></path><path d="M509 132c-1-2 0-6 1-8h0v8h1c0-1 1-1 1-2s0-2 1-2v1 3h0l1 1v10h0-1c-1 2-1 5-1 7v3h-1c1 1 1 3 1 4h0v1l1 5 1-1c1 1 1 4 1 5l-1 7c1 2 0 4 1 7v9c0 1-1 3-1 5s-1 4-2 6v1c-2 0-1-1-3 0l-1-3h0c0-1 0-2 1-3l-3-7v-1-1c0-1 0-1 1-2 0 1 0 2 1 3l1-4h-1v-6c-1-2-1-6-2-8-1-1-1-2-1-3l-1-2 1-1 1 1h0c0-4 0-7-1-11 1-1 1-3 1-5v-6l1-1c0 2 0 4 1 6v-8c0-3 0-5 1-7 1-1 1-1 0-2v1z" class="S"></path><path d="M514 133v10h0-1c-1 2-1 5-1 7v-1c-1-1-1-8-1-10h1c0-2 1-3 1-5l1-1z" class="H"></path><path d="M509 132c-1-2 0-6 1-8h0v8h1c0-1 1-1 1-2s0-2 1-2v1 3h0l1 1-1 1c0 2-1 3-1 5h-1v-3c-1 2-1 4-2 6v-9c1-1 1-1 0-2v1z" class="D"></path><path d="M509 177c1-2-1-6-1-8l-1-19v-1l2 4c0-1 0-1 1-2l1 2c1 1 1 3 1 4h0v1l1 5 1-1c1 1 1 4 1 5l-1 7h0v5c-1-2-2-5-3-8v-1c0 4-1 8-1 11v1c-1-1-1-4-1-5z" class="Z"></path><path d="M511 170c-1-5-2-9-2-15 1 3 1 6 3 9l-1-6 1-1h0v1l1 5 1-1c1 1 1 4 1 5l-1 7h0v5c-1-2-2-5-3-8v-1z" class="W"></path><path d="M505 167c2 3 3 7 4 10 0 1 0 4 1 5v-1c0-3 1-7 1-11v1c1 3 2 6 3 8v-5h0c1 2 0 4 1 7v9c0 1-1 3-1 5s-1 4-2 6v1c-2 0-1-1-3 0l-1-3h0c0-1 0-2 1-3l-3-7v-1-1c0-1 0-1 1-2 0 1 0 2 1 3l1-4h-1v-6c-1-2-1-6-2-8-1-1-1-2-1-3z" class="c"></path><path d="M509 184v-1h0c1 1 1 2 2 3l2-2v3h-1c-1 2-1 5-2 7v2h-1l-3-7v-1-1c0-1 0-1 1-2 0 1 0 2 1 3l1-4z" class="e"></path><path d="M509 196h1v-2c1-2 1-5 2-7l2 8c0 2-1 4-2 6v1c-2 0-1-1-3 0l-1-3h0c0-1 0-2 1-3z" class="O"></path><path d="M507 122l5-6c3 3 5 7 9 9l-1 1v4h1c1 0 2 1 3 1h0l-1 1c0 2-1 4-1 6 0 3 1 11 0 14-1 0-1 0-1 1h0c0 4 0 7-1 11l-1 2-1 4v6c0 2-1 5 0 7l1 1h3l-1 3v1 1h-3l-1-3c-1 2-1 2-1 4h-1v-9c-1-3 0-5-1-7l1-7c0-1 0-4-1-5l-1 1-1-5v-1h0c0-1 0-3-1-4h1v-3c0-2 0-5 1-7h1 0v-10l-1-1h0v-3-1c-1 0-1 1-1 2s-1 1-1 2h-1v-8h0c-1 2-2 6-1 8-1 1-1 1-2 1v-11z" class="Z"></path><path d="M518 160c1 2 1 4 1 6l-1 4-2 2c1-4 1-8 2-12zm-6-10c0-2 0-5 1-7h1c-1 2-1 4 0 5l1 5v1c0 1 0 3 2 4l1-1c1 1 0 1 0 2l-2 1h-1c0-3 0-4-2-7l-1 3 1 1h-1 0c0-1 0-3-1-4h1v-3z" class="R"></path><path d="M518 146c2 3 1 8 1 11h1c0-2 0-3 1-4 0 4 0 7-1 11l-1 2c0-2 0-4-1-6v-1c0-1 1-1 0-2l-1 1c-2-1-2-3-2-4v-1l2 2 1-9z" class="S"></path><path d="M518 170v6c0 2-1 5 0 7l1 1h3l-1 3v1 1h-3l-1-3c-1 2-1 2-1 4h-1v-9c1-3 1-6 1-9l2-2z" class="d"></path><defs><linearGradient id="Bh" x1="520.373" y1="151.986" x2="511.022" y2="137.583" xlink:href="#B"><stop offset="0" stop-color="#9b9b9b"></stop><stop offset="1" stop-color="#cdcbcb"></stop></linearGradient></defs><path fill="url(#Bh)" d="M513 129l1 3c1 0 1-1 1-1v-1-2l1-1c1 1 1 4 1 6h1c-1 3-2 7-2 10l1 1 1 2h0l-1 9-2-2-1-5c-1-1-1-3 0-5h0v-10l-1-1h0v-3z"></path><defs><linearGradient id="Bi" x1="516.588" y1="143.8" x2="525.639" y2="138.254" xlink:href="#B"><stop offset="0" stop-color="#b1b2b0"></stop><stop offset="1" stop-color="#cfcbce"></stop></linearGradient></defs><path fill="url(#Bi)" d="M518 133c0-2 0-6 2-7v4h1c1 0 2 1 3 1h0l-1 1c0 2-1 4-1 6 0 3 1 11 0 14-1 0-1 0-1 1h0c-1 1-1 2-1 4h-1c0-3 1-8-1-11h0l-1-2-1-1c0-3 1-7 2-10z"></path><path d="M518 146c0-4 0-8 2-11 0 6-1 12 1 17v1h0c-1 1-1 2-1 4h-1c0-3 1-8-1-11h0z" class="h"></path><path d="M518 133c0-2 0-6 2-7v4 5c-2 3-2 7-2 11l-1-2-1-1c0-3 1-7 2-10z" class="V"></path><path d="M507 122l5-6c3 3 5 7 9 9l-1 1c-2 1-2 5-2 7h-1c0-2 0-5-1-6l-1 1v2 1s0 1-1 1l-1-3v-1c-1 0-1 1-1 2s-1 1-1 2h-1v-8h0c-1 2-2 6-1 8-1 1-1 1-2 1v-11z" class="U"></path><path d="M521 153c0-1 0-1 1-1 1-3 0-11 0-14 0-2 1-4 1-6l1-1v1c1 0 2 1 2 2l1-1h1c1 1 1 2 1 3 1-1 1-2 3-2l-1 1v1h1l2-2 1 3c0 1-1 2 0 2v1c1 3-1 5 0 8v1c0 1 1 2 2 3v3l1-1c1-1 1-1 2 0-1 2-1 4-3 7l-1 1v1c-1 3-7 10-7 13v1l1 1-1 1h-1c0 1-1 3-1 4l-4 4h-2l1-3h-3l-1-1c-1-2 0-5 0-7v-6l1-4 1-2c1-4 1-7 1-11h0z" class="L"></path><path d="M523 163c1 3-1 7-1 11h1c-1 2-1 3-1 4l-1-1c0-5 2-9 2-14z" class="N"></path><path d="M522 181c0-1 0-1 1-2l1 2v-1-1l1-1v-5c1-2 1-4 1-6l1-1v5h1c-1 4-3 8-4 11h-1 0c-1-1 0-1-1-1z" class="V"></path><path d="M521 153c0-1 0-1 1-1 1-3 0-11 0-14 0-2 1-4 1-6l1-1v1l-1 8v10 13c-3 3-2 6-3 9-1 1-1 3-2 4v-6l1-4 1-2c1-4 1-7 1-11h0z" class="H"></path><path d="M529 136c1-1 1-2 3-2l-1 1v1h1l2-2 1 3c0 1-1 2 0 2v1c1 3-1 5 0 8v1c0 1 1 2 2 3v3l1-1c1-1 1-1 2 0-1 2-1 4-3 7l-1 1v1c-1 3-7 10-7 13v1l1 1-1 1h-1c0 1-1 3-1 4l-4 4h-2l1-3v-3c1 0 0 0 1 1h0 1c1-3 3-7 4-11h-1v-5c1-1 0-4 0-5 1-1 1-1 1-2h0v-7c0-2 0-2 1-3 1-3 0-8 0-11 0-1-1-1 0-2z" class="N"></path><path d="M529 165v-1c0-1 1-2 1-3 1-2 1-3 2-5l1 2-2 6h0c-1 0-2 0-2 1zm2-26h1l1 3c1 1 0 3 0 4l-1 1-1-1v-2l-1-1c0-2 0-2 1-4z" class="L"></path><defs><linearGradient id="Bj" x1="524.286" y1="179.875" x2="534.61" y2="159.829" xlink:href="#B"><stop offset="0" stop-color="#9e9d9c"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#Bj)" d="M533 158l1 1 1 1v2h1v1c-1 3-7 10-7 13v1l1 1-1 1h-1c0 1-1 3-1 4l-4 4h-2l1-3v-3c1 0 0 0 1 1h0 1c1-3 3-7 4-11v-6s0 1 1 1v-1c0-1 1-1 2-1h0l2-6z"></path><path d="M516 190c0-2 0-2 1-4l1 3h3v-1c1 1 1 2 2 3h1c0 2 1 4 0 6l2 4-2 5-1 1 1 1 1-1 1 1 4-4c0 2 1 3 0 5 0 2-1 6 0 8l1 8v3c-1 0-2 0-2-1-1 1-1 2-1 3s1 1 2 2l-1 3-3-3-3-1v-1h-2l-1-1h-2c0-1-1-1-1-1-1 0-1 1-2 1-1-1 0-1-2-2l-2 2h-2c-1 0-1 0-3 1v-1l-1 1 1-2v-1l-2 1h-1l1-3-4 2c-1 1-1 1-3 1l-1-1v-5l1-6h0l1-8c0-2 0-2 1-3 2 1 4 3 5 5l2-1c0-2-1-3-1-4v-4h2l1-2 1 3c2-1 1 0 3 0v-1c1-2 2-4 2-6s1-4 1-5h1z" class="b"></path><path d="M523 207l1 1 1-1 1 1c-1 0-2 1-3 2-1 2-2 5-1 7l-1 2c0-1 0-2-1-3-1 3-1 4-1 7 1 0 1 0 2 1 0 2 1 3 2 4 2 1 2 1 3 3v1l-3-1v-1c-1-1-4-3-5-4v-5-1c1-3 1-6 3-8 1-1 1-1 1-2s1-2 1-3z" class="O"></path><path d="M526 232v-1c-1-2-1-2-3-3-1-1-2-2-2-4-1-1-1-1-2-1 0-3 0-4 1-7 1 1 1 2 1 3l2 4c1-1 1 0 2-1 0 1 1 3 1 3l1-2c0 1 1 1 1 2l2-2v1l1 1v3c-1 0-2 0-2-1-1 1-1 2-1 3s1 1 2 2l-1 3-3-3z" class="G"></path><path d="M517 200h3 2c1 1 1 3 1 4l-4 7h0c0 2 0 4-2 5h-1l-1-1v1 3c0 1 0 2-1 2l-1-4c1-1 0-3 0-4h-1c0-3-1-5 0-8l1 1v-2c1-1 1-2 1-3h1v2h1v-1l1-2z" class="i"></path><path d="M513 204c1-1 1-2 1-3h1v2 10h-1l-1-9z" class="L"></path><path d="M517 206v-1l2-1c1-1 1-2 3-2 0 1 0 1 1 2l-4 7h0l-2 2v1h-1v-4c0-2 0-3 1-4z" class="D"></path><path d="M517 206l1 1c0 1 0 2-1 3h-1c0-2 0-3 1-4z" class="Q"></path><path d="M530 204c0 2 1 3 0 5 0 2-1 6 0 8l1 8-1-1v-1l-2 2c0-1-1-1-1-2l-1 2s-1-2-1-3c-1 1-1 0-2 1l-2-4 1-2c-1-2 0-5 1-7 1-1 2-2 3-2l4-4z" class="H"></path><path d="M524 218v-4c1-3 2-5 5-6l1 1-2 3v1c-1 2-1 5-2 6v-1l-1 1-1-1z" class="U"></path><path d="M530 204c0 2 1 3 0 5h0l-1-1c-3 1-4 3-5 6v4c-1 0-2 0-2-1-1-2 0-5 1-7 1-1 2-2 3-2l4-4z" class="V"></path><path d="M516 190c0-2 0-2 1-4l1 3h3v-1c1 1 1 2 2 3h1c0 2 1 4 0 6l-1 1-1-1h-1l-1 1h0-2l-1 2-1 2v1h-1v-2h-1c0 1 0 2-1 3v2l-1-1c-1 3 0 5 0 8h1c0 1 1 3 0 4v1 4h-1c0-2-1-4-1-6h-2c0-1 0-2-1-3l-2-4c0-2-1-3-1-4v-4h2l1-2 1 3c2-1 1 0 3 0v-1c1-2 2-4 2-6s1-4 1-5h1z" class="S"></path><path d="M507 203h2s1 1 1 2c0 2 1 4 1 6h-1c-1-2-3-6-3-8z" class="h"></path><path d="M516 190c0-2 0-2 1-4l1 3h3v-1c1 1 1 2 2 3h1c0 2 1 4 0 6l-1 1-1-1h-1l-1 1h0-2l-1 2-1 2v-1-11z" class="J"></path><path d="M521 188c1 1 1 2 2 3h1c0 2 1 4 0 6l-1 1-1-1h-1c-1 0-1 0-1-1v-1-2c1-2 1-3 1-4v-1z" class="K"></path><path d="M499 205c2 1 4 3 5 5l2-1 2 4c1 1 1 2 1 3h2c0 2 1 4 1 6h1v-4-1l1 4c1 0 1-1 1-2l1 1v6h-1l-1-1c-1 1-1 1-1 2l-2 2h-2c-1 0-1 0-3 1v-1l-1 1 1-2v-1l-2 1h-1l1-3-4 2c-1 1-1 1-3 1l-1-1v-5l1-6h0l1-8c0-2 0-2 1-3z" class="d"></path><path d="M506 209l2 4c1 1 1 2 1 3l-1 2v-2h-1v1c0 2-1 4-3 6v1 1l-4 2c-1 1-1 1-3 1l-1-1v-5c0 1 0 2 1 2h1 0s0 1 1 2c0-1 1-1 2-1v-1c1 0 1-1 1-1 2-2 3-3 3-5 1-3 0-5-1-8l2-1z" class="K"></path><path d="M498 208c1-1 1 0 2 0l1 1c1 2 2 4 2 6s0 3-1 4l-1 1h-1l-1 1c-1-1-1-1-1-2s0-1-1-2v-1l1-8z" class="D"></path><path d="M501 209c1 2 2 4 2 6h-1c-1 1-1 2-2 2h-1v-6l2-2z" class="Q"></path><path d="M509 216h2c0 2 1 4 1 6h1v-4-1l1 4c1 0 1-1 1-2l1 1v6h-1l-1-1c-1 1-1 1-1 2l-2 2h-2c-1 0-1 0-3 1v-1l-1 1 1-2v-1l-2 1h-1l1-3v-1-1c2-2 3-4 3-6v-1h1v2l1-2z" class="J"></path><path d="M504 224v-1c2-2 3-4 3-6v-1h1v2c2 2 2 4 2 6-1 2-1 3-2 4h-2v-1l-2 1h-1l1-3v-1z" class="k"></path><path d="M504 224l1-1c1 0 1-1 2-2h1c-1 2-2 4-2 6l-2 1h-1l1-3v-1z" class="I"></path><path d="M837 203h18c1 0 2 0 3 1h0c1 0 1 1 2 2 2 0 8-1 10-1h5v9l-2-1-10 1h-6l-2 2c-8 1-15 3-22 5-2 0-3 1-4 1l-8 5c-1 0-3 1-4 2s-7 7-8 7l-4 4c-7 6-12 13-16 21-12 18-19 39-28 60l-30 72-42 101-5 13c-1 0-2 3-2 4-2 0-2 0-2 1v1 1c-1 1-1 2-2 3v1c-1 0-1 1-1 2l-1 1v1h0c0-2 2-6 1-8l-12 26-1 5h-1c0-2 5-15 6-17 0 0-1 1-2 1-1 1-1 3-2 5l-6 14c-1 2-2 5-3 7 0 1-1 1-2 1l21-51-1-1c-2 1-2 2-2 3-1 0-1 0-1 1 1-6 4-11 5-16l2-6v1l1-2c2-3 3-6 6-9 1-2 1-3 1-4l1-1 1-3 39-92 11-27c4-15 12-30 18-45 7-14 13-28 20-41 4-7 8-13 12-19h0l1-2c0-1 4-4 4-6 1-2 3-4 4-6h0l1-1h-1l-1-1 1-2h-1l-2 2-6 5-4 2-1-1c-1 0-4 0-5 2l-5 7c-4 5-6 9-8 15 0 2 0 3-1 4l1 1v1h-2v-1c-1 1-3 1-4 1v-1h-1c-1 1-2 1-3 2l-3 6c-1-1-1-3-1-4 0-4 2-7 4-11-2-5 0-8 2-13l4-5c1-1 1-1 1-2 1-3 4-5 5-7 3-4 6-7 9-9l4-4c5-4 14-9 21-12v1c9-3 17-2 26-2 3 0 7 1 10 0z" class="J"></path><path d="M688 468l39-92h2l-13 32-7 15-10 26-5 11c-2 3-4 9-6 12l-1-1 1-3z" class="B"></path><path d="M822 223h1 2l3-1h1l-8 5c-1 0-3 1-4 2s-7 7-8 7c-3 1-5 4-8 6-2 2-5 5-7 8l-6 9-1 1c-1 1-1 2-2 2 0-1 1-2 2-3l1-1h0l1-1v-1h0l3-4h0l1-2v-1h-1c-3 2-7 7-8 10l-1 1h-1c9-16 23-30 40-37z" class="d"></path><path d="M678 486v1l1-2c2-3 3-6 6-9l-2 6h1c0-1 0-1 1-2h0l1 1-1 1h0c0 1 0 1-1 2-3 13-8 27-13 40-1 1-1 3-2 4 0 0-1 1-2 1-1 1-1 3-2 5l-6 14c-1 2-2 5-3 7 0 1-1 1-2 1l21-51-1-1c-2 1-2 2-2 3-1 0-1 0-1 1 1-6 4-11 5-16l2-6z" class="H"></path><defs><linearGradient id="Bk" x1="679.636" y1="501.322" x2="674.662" y2="488.449" xlink:href="#B"><stop offset="0" stop-color="#464848"></stop><stop offset="1" stop-color="#636061"></stop></linearGradient></defs><path fill="url(#Bk)" d="M678 486v1l1-2c2-3 3-6 6-9l-2 6h1c0-1 0-1 1-2h0l1 1-1 1h0c0 1 0 1-1 2-1 0-1 1-2 2l-7 19-1-1c-2 1-2 2-2 3-1 0-1 0-1 1 1-6 4-11 5-16l2-6z"></path><path d="M824 217l12-4c8-2 16-4 24-4 3-1 8-1 11 0 2 1 2 2 2 4l-10 1h-6l-2 2c-8 1-15 3-22 5-2 0-3 1-4 1h-1l-3 1h-2-1c-17 7-31 21-40 37-2 3-4 8-6 12l-19 41-17 38-11 25h-2l11-27c4-15 12-30 18-45 7-14 13-28 20-41 4-7 8-13 12-19h0l1-2c0-1 4-4 4-6 1-2 3-4 4-6h0l1-1c2-2 4-4 7-4 0-1 0-1 1-1 0-1 1-1 1-2 2 0 4-2 6-3 0 0 1-1 2-1 0-1 0-1 1-1 1-1 3-2 5-3h0c1-1 2-1 4-1-1 1-2 1-2 2h-1c-2 3-6 4-8 6l5-2 1-1h0c1 0 1 0 2-1h2z" class="r"></path><path d="M727 376l11-27v1c0 4-3 9-4 13-1 1-2 2-2 4 1-1 1-1 1-2s1-1 1-2v-1c1-2 2-4 3-5 1-2 1-4 3-6l-11 25h-2z" class="I"></path><path d="M822 223c11-5 22-8 35-9l-2 2c-8 1-15 3-22 5-2 0-3 1-4 1h-1l-3 1h-2-1z" class="V"></path><path d="M798 229c2-2 4-4 7-4 0-1 0-1 1-1 0-1 1-1 1-2 2 0 4-2 6-3 0 0 1-1 2-1 0-1 0-1 1-1 1-1 3-2 5-3h0c1-1 2-1 4-1-1 1-2 1-2 2h-1c-2 3-6 4-8 6l5-2 1-1h0c1 0 1 0 2-1h2c-8 4-15 8-22 13l-9 9c-1 1-3 5-5 5h0l1-2c0-1 4-4 4-6 1-2 3-4 4-6h0l1-1z" class="Q"></path><path d="M837 203h18c1 0 2 0 3 1h0c1 0 1 1 2 2 2 0 8-1 10-1h5v9l-2-1c0-2 0-3-2-4-3-1-8-1-11 0-8 0-16 2-24 4l-12 4h-2c-1 1-1 1-2 1h0l-1 1-5 2c2-2 6-3 8-6h1c0-1 1-1 2-2-2 0-3 0-4 1h0c-2 1-4 2-5 3-1 0-1 0-1 1-1 0-2 1-2 1-2 1-4 3-6 3 0 1-1 1-1 2-1 0-1 0-1 1-3 0-5 2-7 4h-1l-1-1 1-2h-1l-2 2-6 5-4 2-1-1c-1 0-4 0-5 2l-5 7c-4 5-6 9-8 15 0 2 0 3-1 4l1 1v1h-2v-1c-1 1-3 1-4 1v-1h-1c-1 1-2 1-3 2l-3 6c-1-1-1-3-1-4 0-4 2-7 4-11-2-5 0-8 2-13l4-5c1-1 1-1 1-2 1-3 4-5 5-7 3-4 6-7 9-9l4-4c5-4 14-9 21-12v1c9-3 17-2 26-2 3 0 7 1 10 0z" class="U"></path><path d="M844 206h1 0 11l-1 1c-3 1-8 1-12 1l2-1c-1 0-1-1-1-1z" class="q"></path><path d="M783 234h2v-1-1c-3-2-6-1-9 0h0l-1-1 1-1c1-1 3-2 5-2 1 1 3 2 4 2h1c0 2 1 2 2 3l-4 2-1-1z" class="G"></path><path d="M796 219c1-1 2-1 3-1 1 1 1 3 2 4-2 1-3 2-4 3l-1 1-2 2-6 5c-1-1-2-1-2-3 0-1 0-3-1-4v-1c4-3 6-4 11-6z" class="m"></path><path d="M796 219c1-1 2-1 3-1 1 1 1 3 2 4-2 1-3 2-4 3 0-1 0-2 1-4l-2-2z" class="N"></path><defs><linearGradient id="Bl" x1="762.82" y1="260.559" x2="760.433" y2="249.604" xlink:href="#B"><stop offset="0" stop-color="#0b0c0d"></stop><stop offset="1" stop-color="#363533"></stop></linearGradient></defs><path fill="url(#Bl)" d="M770 243h3c-4 5-6 9-8 15 0 2 0 3-1 4l1 1v1h-2v-1c-1 1-3 1-4 1v-1h-1c-1 1-2 1-3 2v-2c3-6 9-17 15-20z"></path><path d="M770 243h3c-4 5-6 9-8 15 0 2 0 3-1 4-2 0-3 0-4-1 0-4 6-11 8-14 1-1 2-2 2-4z" class="Q"></path><path d="M799 218l1-1-1-1-4 1c-1 1-1 1-3 1 2-1 5-2 7-2s5-2 7-3c9-3 19-5 28-7 3 0 8-1 11 0h-1c-13 0-25 6-35 12-3 1-6 2-8 4-1-1-1-3-2-4z" class="m"></path><defs><linearGradient id="Bm" x1="818.494" y1="213.472" x2="820.695" y2="218.165" xlink:href="#B"><stop offset="0" stop-color="#d4d3d4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Bm)" d="M801 222c2-2 5-3 8-4 10-6 22-12 35-12 0 0 0 1 1 1l-2 1c-1 0-2 0-4 1h-2c-1 0-1 1-2 1l-5 1s-1 1-2 1h0-1c-1 0-1 1-2 1-2 0-3 0-4 1h0c-2 1-4 2-5 3-1 0-1 0-1 1-1 0-2 1-2 1-2 1-4 3-6 3 0 1-1 1-1 2-1 0-1 0-1 1-3 0-5 2-7 4h-1l-1-1 1-2h-1l1-1c1-1 2-2 4-3z"></path><path d="M767 229c3-4 6-7 9-9 0 0 1 1 1 2l-1 1c-1 0-1 1-1 1l6-3v1h2 0l-1 1c-2 1-3 2-3 3-1 1-1 1-2 1-2 1-4 4-4 6v1c-1 3-2 4-5 6-1 1-3 2-4 4l-1 1c0 1-1 1-1 2l-7 9c-2-5 0-8 2-13l4-5c1-1 1-1 1-2 1-3 4-5 5-7z" class="Q"></path><path d="M767 229c3-4 6-7 9-9 0 0 1 1 1 2l-1 1c-1 0-1 1-1 1l-13 12c1-3 4-5 5-7z" class="G"></path><defs><linearGradient id="Bn" x1="801.133" y1="204.303" x2="802.655" y2="218.257" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242324"></stop></linearGradient></defs><path fill="url(#Bn)" d="M837 203h18c-7 0-13 0-19 1-15 1-30 4-43 10-5 2-9 4-12 7l-6 3s0-1 1-1l1-1c0-1-1-2-1-2l4-4c5-4 14-9 21-12v1c9-3 17-2 26-2 3 0 7 1 10 0z"></path><path d="M148 196l222 1c-1 2-1 15-1 15-2 2-4 1-6 1-8 1-16 5-22 10-3 2-5 4-7 6h-6-1l1-2-1-2c3-5 6-8 11-11v-3c-2 2-6 4-8 7-2 1-3 1-5 2l-1-1h0c-3 4-8 9-9 14h-4l-1-1v-2l-2 1v1c-2 0-4-2-6-3l-1-2h-2c-2 0-7-4-9-6-1 1-1 1-1 2-3-1-6-3-9-4-1 1-3 0-3-1-2 0-4-1-6-1h-2c-2-1-3-1-5-1-3 0-6 0-9-1 2-1 5-1 8-1-3 0-6 0-8-1h-1c-1-1-3 0-5 1l-1-1c-7 3-13 8-16 15-2 3-2 5-3 8-1 2-1 3-1 6s1 6 1 9l2 8v2h-1c0 3 2 6 3 9 0 2 1 4 1 6l-3-4v3c-2-2-4-4-5-6h-1c-4-6-7-12-11-18h-1l6 10c1 1 2 3 2 4v1c-2-2-3-5-4-7-4-7-8-13-13-18-10-10-21-14-33-21-1-1-3-2-4-2-1-1-3-1-4-1h-1v-1c-4-1-8-1-12-2h-6l2-1c-3-1-5-1-7 0l-1-1h-2c-2-1 0-11-1-15h1c4-1 8-1 12-1z" class="a"></path><path d="M338 211l9-6c4-2 9-4 13-5-3 2-5 4-8 6-3 1-5 1-7 2v1l-7 5v-3z" class="e"></path><path d="M302 202l39-1c-1 1-2 1-3 1l-1 1h-3c-11-1-21 1-31 2l2-1h0l8-1h-2c-3-1-5 0-8-1h-1z" class="E"></path><path d="M216 201c8 0 16 1 23 1h0c-5 2-9 4-13 5-1 0-3-1-4-1h0c-1-1-2-1-3-2h0-3v-3z" class="F"></path><path d="M144 210c10-2 22-1 31 2v1h-1c-1 0-3 0-4 1l-8-1h-4-12c-3-1-5-1-7 0l-1-1c2-1 4-1 6-2z" class="K"></path><path d="M138 212c2-1 4-1 6-2v1c6 0 12 0 18 2h-4-12c-3-1-5-1-7 0l-1-1z" class="l"></path><defs><linearGradient id="Bo" x1="191.038" y1="205.132" x2="188.491" y2="217.75" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#454344"></stop></linearGradient></defs><path fill="url(#Bo)" d="M220 234c-1-2-2-5-3-7-14-17-40-22-61-24 3-1 5-1 8-1 5 1 10 1 14 1l1 1c2 1 4 1 6 1s4 1 7 1c10 2 19 9 24 17 2 3 5 8 4 11h0z"></path><path d="M341 201h8c-10 5-18 9-25 18h0-2c0-1 0-2-1-3v-1c0-1 0-1-1-1-1-2-14-1-17-2h-1c-1 0 0 0-1-1 2 1 8 1 9 0v-1c2-1 4-1 6-3h3c3-1 5-2 8-2l7-2h3l1-1c1 0 2 0 3-1z" class="m"></path><path d="M316 207h3c3-1 5-2 8-2-5 2-11 5-17 6v-1c2-1 4-1 6-3z" class="U"></path><path d="M241 201l61 1h1c3 1 5 0 8 1h2l-8 1h0l-2 1h-25c-8 0-19-1-26 1-3 1-7 3-9 5v-1c0-3-1-6-2-9zm-77 1l52-1v3c-2 2-4 4-5 7 2 2 5 4 6 7 0 2-1 3 0 5h-1c-5-8-14-15-24-17-3 0-5-1-7-1s-4 0-6-1l-1-1c-4 0-9 0-14-1z" class="m"></path><path d="M192 206l-8-3c9 1 19 2 27 8 2 2 5 4 6 7 0 2-1 3 0 5h-1c-5-8-14-15-24-17z" class="a"></path><path d="M345 209c7-2 15-2 21 1 1 1 2 2 3 2-2 2-4 1-6 1-8 1-16 5-22 10-3 2-5 4-7 6h-6-1l1-2-1-2c3-5 6-8 11-11l7-5z" class="m"></path><path d="M239 202l2-1c1 3 2 6 2 9v1c2-2 6-4 9-5l-3 3c2 1 3 0 5 0 2-1 4 0 6 0h-1c-2 0-3 0-5 1h3l1-1s1 0 2 1h6 0c-7 0-12 0-18 3-7 3-13 8-16 15-2 3-2 5-3 8-1 2-1 3-1 6s1 6 1 9l2 8v2h-1l-3-7-1-3c0-1 0-2-1-2v-2c-1-2-1-5-1-8-1-3 0-6-2-8l-1-1c0-4-2-8-4-12-1-3-4-5-6-7 1-3 3-5 5-7h3 0c1 1 2 1 3 2h0c1 0 3 1 4 1 4-1 8-3 13-5h0z" class="Q"></path><path d="M239 202l2-1c1 3 2 6 2 9v1c-1 1-2 2-4 3 0-1-1-2-1-3h1c1-2 0-2 0-4v-1-4h0z" class="D"></path><path d="M228 213c0-1 1-2 2-3 3-3 5-3 9-3 0 2 1 2 0 4h-1c-4 5-8 10-11 16v-10h0c0-2 0-3 1-4z" class="M"></path><path d="M228 213c1 0 3-1 4-2 0 1 1 1 2 2h-1c-2 2-3 4-4 7h-1v-1c0-1 0-1-1-2h0c0-2 0-3 1-4z" class="V"></path><path d="M228 213c0-1 1-2 2-3 3-3 5-3 9-3 0 2 1 2 0 4l-1-1-4 3c-1-1-2-1-2-2-1 1-3 2-4 2z" class="X"></path><path d="M232 211c2-1 3-2 5-2l1 1-4 3c-1-1-2-1-2-2z" class="N"></path><path d="M216 204h3 0c1 1 2 1 3 2h0c1 0 3 1 4 1-2 1-3 1-4 3-1 1-1 4-1 5l1 16-1-1c0-4-2-8-4-12-1-3-4-5-6-7 1-3 3-5 5-7z" class="f"></path><path d="M219 204h0c1 1 2 1 3 2h0c1 0 3 1 4 1-2 1-3 1-4 3-1 1-1 4-1 5h0v8c-2-2-1-10-1-12-1 0-1 0-1 1h-1c-1-1-3-2-3-3v-2c1-1 1-1 3-2h0l1-1z" class="Y"></path><defs><linearGradient id="Bp" x1="169.519" y1="215.56" x2="225.087" y2="242.016" xlink:href="#B"><stop offset="0" stop-color="#545353"></stop><stop offset="1" stop-color="#969595"></stop></linearGradient></defs><path fill="url(#Bp)" d="M175 212c15 3 29 9 39 20h0c2 1 3 2 4 3 2 1 2 3 3 4h1c-1-2-1-3-2-5h0c1-3-2-8-4-11h1c-1-2 0-3 0-5 2 4 4 8 4 12l1 1c2 2 1 5 2 8 0 3 0 6 1 8v2c1 0 1 1 1 2l1 3 3 7c0 3 2 6 3 9 0 2 1 4 1 6l-3-4v3c-2-2-4-4-5-6h-1c-4-6-7-12-11-18h-1l6 10c1 1 2 3 2 4v1c-2-2-3-5-4-7-4-7-8-13-13-18-10-10-21-14-33-21-1-1-3-2-4-2-1-1-3-1-4-1h-1v-1c-4-1-8-1-12-2h-6l2-1h12 4l8 1c1-1 3-1 4-1h1v-1z"></path><path d="M200 229c-1-2-4-4-6-6 5 2 11 6 15 10v1l2 3h-1l-10-8z" class="l"></path><path d="M217 218c2 4 4 8 4 12l1 1c2 2 1 5 2 8 0 3 0 6 1 8v2c1 0 1 1 1 2l1 3c-2-3-4-8-6-11-2-4-5-7-7-11h0c2 1 3 2 4 3 2 1 2 3 3 4h1c-1-2-1-3-2-5h0c1-3-2-8-4-11h1c-1-2 0-3 0-5z" class="D"></path><path d="M195 225l5 4 10 8c7 8 13 15 17 24 1 1 2 3 2 5s2 3 2 5v1 3c-2-2-4-4-5-6l-4-7c0-1 0-1 1-2-5-7-10-14-16-21-4-3-7-6-11-9l-2-3 1-2z" class="N"></path><path d="M223 260l8 11v1 3c-2-2-4-4-5-6l-4-7c0-1 0-1 1-2z" class="K"></path><path d="M162 213l8 1c8 3 18 6 25 11h0l-1 2 2 3c4 3 7 6 11 9 6 7 11 14 16 21-1 1-1 1-1 2l4 7h-1c-4-6-7-12-11-18h-1l6 10c1 1 2 3 2 4v1c-2-2-3-5-4-7-4-7-8-13-13-18-10-10-21-14-33-21-1-1-3-2-4-2-1-1-3-1-4-1h-1v-1c-4-1-8-1-12-2h-6l2-1h12 4z" class="T"></path><defs><linearGradient id="Bq" x1="181.071" y1="213.023" x2="168.179" y2="225.241" xlink:href="#B"><stop offset="0" stop-color="#8c8b8a"></stop><stop offset="1" stop-color="#ababab"></stop></linearGradient></defs><path fill="url(#Bq)" d="M162 213l8 1c8 3 18 6 25 11h0l-1 2 2 3c-6-5-12-8-19-10 1 2 3 2 5 3v1c-4-1-7-3-11-4-1-1-3-2-4-2-1-1-3-1-4-1h-1v-1c-4-1-8-1-12-2h-6l2-1h12 4z"></path><path d="M303 205c10-1 20-3 31-2l-7 2c-3 0-5 1-8 2h-3c-2 2-4 2-6 3v1c-1 1-7 1-9 0 1 1 0 1 1 1h1c3 1 16 0 17 2 1 0 1 0 1 1v1c1 1 1 2 1 3h2c-3 4-8 9-9 14h-4l-1-1v-2l-2 1v1c-2 0-4-2-6-3l-1-2h-2c-2 0-7-4-9-6-1 1-1 1-1 2-3-1-6-3-9-4-1 1-3 0-3-1-2 0-4-1-6-1h-2c-2-1-3-1-5-1-3 0-6 0-9-1 2-1 5-1 8-1-3 0-6 0-8-1h-1c-1-1-3 0-5 1l-1-1c6-3 11-3 18-3h0-6c-1-1-2-1-2-1l-1 1h-3c2-1 3-1 5-1h1c-2 0-4-1-6 0-2 0-3 1-5 0l3-3c7-2 18-1 26-1h25z" class="n"></path><path d="M274 211c3-1 5 0 8 0 5 1 10 1 14 3 2 0 2 1 4 2l2-2c0 1 2 2 2 2-2 2-4 2-6 2v1c-3-2-6-4-10-5-2 0-3-1-5-1l-9-2z" class="U"></path><path d="M302 214c-2-2-4-2-6-4 1-1 2 0 4 0l-1-1c5 0 11-1 17-2-2 2-4 2-6 3v1c-1 1-7 1-9 0 1 1 0 1 1 1h1v1c1 1 2 1 3 2h1c2 1 5 1 6 3-1 1-1 1-3 1-1-1-1-1-3-1-1 0-2-1-3-2 0 0-2-1-2-2z" class="D"></path><path d="M303 212c3 1 16 0 17 2 1 0 1 0 1 1v1c1 1 1 2 1 3h2c-3 4-8 9-9 14h-4l-1-1v-2l-2 1c-2-2-4-3-4-5h0l2 2 1-1c-3-3-5-6-9-8v-1c2 0 4 0 6-2 1 1 2 2 3 2 2 0 2 0 3 1 2 0 2 0 3-1-1-2-4-2-6-3h-1c-1-1-2-1-3-2v-1z" class="q"></path><path d="M310 230c-1-2-2-4-2-6l4 1c2 2 2 2 2 5-1 1-1 2-2 2h-2v-2z" class="E"></path><path d="M321 215v1c1 1 1 2 1 3h2c-3 4-8 9-9 14h-4l-1-1h2c1 0 1-1 2-2 0-3 0-3-2-5 2 0 2-1 4-1v-1c-3 0-4 1-6 0l-1-1c3 0 5 1 8-1v-1h1l-1 2h0c1 0 2-1 3-1 1-2 1-4 1-6z" class="B"></path><path d="M303 212c3 1 16 0 17 2 1 0 1 0 1 1 0 2 0 4-1 6-1 0-2 1-3 1h0l1-2h-1v1h-3c-2-1-2 0-3 0-1-1-3-2-4-3 2 0 2 0 3 1 2 0 2 0 3-1-1-2-4-2-6-3h-1c-1-1-2-1-3-2v-1z" class="C"></path><path d="M248 213c6-3 11-3 18-3l8 1 9 2c2 0 3 1 5 1 4 1 7 3 10 5 4 2 6 5 9 8l-1 1-2-2h0c0 2 2 3 4 5v1c-2 0-4-2-6-3l-1-2h-2c-2 0-7-4-9-6-1 1-1 1-1 2-3-1-6-3-9-4-1 1-3 0-3-1-2 0-4-1-6-1h-2c-2-1-3-1-5-1-3 0-6 0-9-1 2-1 5-1 8-1-3 0-6 0-8-1h-1c-1-1-3 0-5 1l-1-1z" class="I"></path><path d="M263 214c10 1 18 2 27 7-1 1-1 1-1 2-3-1-6-3-9-4-1 1-3 0-3-1-2 0-4-1-6-1h-2c-2-1-3-1-5-1-3 0-6 0-9-1 2-1 5-1 8-1z" class="C"></path><path d="M283 213c2 0 3 1 5 1 4 1 7 3 10 5 4 2 6 5 9 8l-1 1-2-2h0c0 2 2 3 4 5v1c-2 0-4-2-6-3-3-8-8-10-14-13-1 0-2-1-3-1h-1l-1-2z" class="H"></path><path d="M653 579l1 2-8 21-11 25-12 32-13 30-18 46-11 25-15 36-10 25c-2 3-3 6-4 9l-34 85-7 18c-2 2-3 6-4 8h-1-1c0-2-1-3-2-4-3 1-4 1-6 4l-4-10c0-5 1-11-1-15v-1c-1-3-1-4 0-6l-12-27-25-57c-3-11-9-21-13-30-2-9-7-18-11-26-2-7-6-13-9-19l-7-16-5-13c-2-4-3-7-5-11-1-1-2-6-3-8-2-7-5-14-8-21-1 0-2 0-2-1-2-2-3-2-5-3l-3 1-5-11c1-3 0-6 0-9 0-5 0-9-1-14 0 0-1-3-1-4l1-1c-1-3-3-6-4-9h2l1 3v-1-3h0c2 2 3 5 6 7 2 5 3 10 6 14l3 5c2 2 2 7 3 10l9 18c-2 2-1 4-2 5l23 54 7 19c3 2 4 6 5 9l1-2h0c1 1 2 3 2 5 1 1 1 3 2 4h1 1l4 9c0 2-1 4 0 6l3 6 3 6 3 6h1l1 3c1-1 2-1 2-2l-1-3c2 1 2 2 4 2h0c1 0 2 0 3 1 2 1 3 1 4 3 1 0 1 0 2 1 2 2 3 3 4 5l1 1c2-1 4-4 6-5h3c0-1 1-1 2-2 4-1 11-1 15 0h1l-1-1h0v-6c4 3 6 8 8 13l3-1c1 3 2 5 2 8v-2c1 0 1 0 1 1 1 1 1 1 2 1v1c1 1 1 0 0 1v3c0 1 0 2-1 4s-1 4-2 6-5 5-5 6l1 1c0-1 1-1 1-1h1l-2 4h0v2l-1 1c-1 4-3 8-4 11-1 1-3 2-3 4-1 1-1 2 0 3l-1 1c-2 1-3 2-4 2-1 1-3 2-3 3v2h1c1 1 2 1 2 2h2c-2 3-3 7-4 10v1l-1 1h0v4l2 4 3 9 2 4c0 2 2 6 3 7h1v2h-1l-1-1-1-3c-1 1-1 4 0 5v1c1 1 1 2 1 2 1 1 1 2 1 3v1c1-1 1-1 1-2l1-1v-2l2-2v-1c0-1 1-2 1-2v-2h1 0c2-4 4-8 6-13l8-21 27-67c0-5 5-11 4-16 1-4 3-8 5-11 1-5 4-10 5-15 4-9 7-18 12-27l-1-1c4-5 5-12 8-17 2-6 5-11 6-16 1-2 2-5 2-7l7-16 4-9c1-1 2-4 3-6 0-1 1-2 1-3l12-29 6-13c2-4 4-9 5-14 2-5 4-11 7-16l2-2c0-1 1-2 2-3h1l8-20z" class="D"></path><path d="M432 761c3 2 4 6 5 9l5 9-1 2c-4-6-7-13-9-20z" class="J"></path><path d="M558 804c1-4 3-8 5-11-1 2-1 5-1 7-2 7-5 14-8 20 0-5 5-11 4-16z" class="P"></path><path d="M447 798c1-1 1-2 1-4-1-1 0-1 0-2l3 6 3 6v4 2c0 3 2 5 3 8l6 15c1 1 1 2 1 3-3-5-5-10-7-15l-10-23z" class="I"></path><path d="M454 810c-2-4-4-7-4-11l1-1 3 6v4 2z" class="E"></path><path d="M437 770l1-2h0c1 1 2 3 2 5 1 1 1 3 2 4h1 1l4 9c0 2-1 4 0 6 0 1-1 1 0 2 0 2 0 3-1 4l-3-10-3-7 1-2-5-9z" class="T"></path><path d="M442 779c2 4 2 5 2 9l-3-7 1-2z" class="O"></path><path d="M580 751c-1 3-1 6-2 9 0 1 0 1-1 2v2l-10 25c-2 4-3 8-5 11 0-2 0-5 1-7 1-5 4-10 5-15 4-9 7-18 12-27z" class="G"></path><path d="M492 909l5 11v2c0 1 1 1 1 2 1 1 1 1 1 2s0 1 1 2v1l1 1c1 2 1 4 2 7-3 1-4 1-6 4l-4-10c0-5 1-11-1-15v-1c-1-3-1-4 0-6z" class="b"></path><path d="M378 644l3 6 2 6 7 17 3 6 1 2c-1 0-2 0-2-1-2-2-3-2-5-3l-3 1-5-11c1-3 0-6 0-9 0-5 0-9-1-14z" class="e"></path><path d="M383 656l7 17-3-3c-2-4-4-9-4-14z" class="Z"></path><path d="M378 644l3 6c-1 3 0 4 0 7 1 5 0 8 2 12 1 4 5 4 7 7 1 2 2 2 3 3l1 2c-1 0-2 0-2-1-2-2-3-2-5-3l-3 1-5-11c1-3 0-6 0-9 0-5 0-9-1-14z" class="O"></path><path d="M640 604l2-2c0-1 1-2 2-3h1l-32 76-36 89v-2c1-1 1-1 1-2 1-3 1-6 2-9l-1-1c4-5 5-12 8-17 2-6 5-11 6-16 1-2 2-5 2-7l7-16 4-9c1-1 2-4 3-6 0-1 1-2 1-3l12-29 6-13c2-4 4-9 5-14 2-5 4-11 7-16z" class="I"></path><path d="M377 633v-1-3h0c2 2 3 5 6 7 2 5 3 10 6 14l3 5c2 2 2 7 3 10l9 18c-2 2-1 4-2 5l23 54c-3-2-3-4-5-6 0 1 0 1-1 2-1-2-2-5-3-7s-2-4-2-6c-2-4-5-9-6-13-2-11-8-21-12-30l-18-43c-1-3-3-6-4-9h2l1 3z" class="B"></path><path d="M377 633v-1-3h0c2 2 3 5 6 7 2 5 3 10 6 14l3 5c2 2 2 7 3 10l-1-1-3-6c0-3-1-4-4-6v1l4 12 2 4h-1c-1 0-2-3-2-5-1-2-2-4-2-6-3-9-7-16-11-25h0z" class="Y"></path><path d="M393 669l-2-4-4-12v-1c3 2 4 3 4 6l3 6 1 1 9 18c-2 2-1 4-2 5l-9-19z" class="O"></path><path d="M392 669h1l9 19 23 54c-3-2-3-4-5-6 0 1 0 1-1 2-1-2-2-5-3-7s-2-4-2-6c-1-4-3-7-4-10-2-5-3-10-5-14l-13-32z" class="V"></path><path d="M454 804l3 6h1l1 3c1-1 2-1 2-2l-1-3c2 1 2 2 4 2h0c1 0 2 0 3 1 2 1 3 1 4 3 1 0 1 0 2 1 2 2 3 3 4 5l1 1c2-1 4-4 6-5h3c0-1 1-1 2-2 4-1 11-1 15 0h1l-1-1h0v-6c4 3 6 8 8 13l3-1c1 3 2 5 2 8v-2c1 0 1 0 1 1 1 1 1 1 2 1v1c1 1 1 0 0 1v3c0 1 0 2-1 4s-1 4-2 6-5 5-5 6l1 1c0-1 1-1 1-1h1l-2 4h0v2l-1 1c-1 4-3 8-4 11-1 1-3 2-3 4-1 1-1 2 0 3l-1 1c-2 1-3 2-4 2-1 1-3 2-3 3v2h1c1 1 2 1 2 2h2c-2 3-3 7-4 10v1l-1 1h0v4l2 4 3 9 2 4c0 2 2 6 3 7h1v2h-1l-1-1-1-3c-1 1-1 4 0 5v1c1 1 1 2 1 2 1 1 1 2 1 3v1c1-1 1-1 1-2l1-1v-2l2-2v-1c0-1 1-2 1-2v-2h1 0l-7 18v-4c1-4-2-9-3-13l-9-19-10-23-14-28c-2-5-5-11-6-16 0-1 0-2-1-3l-6-15c-1-3-3-5-3-8v-2-4z" class="F"></path><path d="M478 854c3 3 3 5 5 9l-1 3c-2-4-4-7-4-12z" class="d"></path><path d="M484 864l2-1v-1h1c0 2 1 3 1 5 1 2 2 4 2 6 1 1 3 4 3 5l1 2 1 1 1 2 1 2h1l2-2h2c-2 3-3 7-4 10v1l-1 1h0v4c-1-3-3-6-4-9l-11-24 1-3 1 1h0 0z" class="i"></path><path d="M493 878l1 2 1 1 1 2-2 3-3-6 2-2z" class="I"></path><path d="M500 883h2c-2 3-3 7-4 10-2-1-3-5-4-7l2-3 1 2h1l2-2z" class="p"></path><path d="M484 864l2-1v-1h1c0 2 1 3 1 5 1 2 2 4 2 6 1 1 3 4 3 5l-2 2c-2-5-4-11-7-16h0z" class="G"></path><path d="M460 808c2 1 2 2 4 2h0c1 0 2 0 3 1 2 1 3 1 4 3 1 0 1 0 2 1 2 2 3 3 4 5l1 1c2-1 4-4 6-5h3c-1 1-3 2-3 3s0 2-1 2c1 1 2 1 3 1-1 2 0 5-1 7 0 1 1 1 2 1h1l1 5c1 1 2 3 4 4l2 2 3 2-2 2 2 1h-1l-1 1h-1c-3-1-6-1-9 0 0 1-1 1-1 2h-2l-1 1-1 1h-2l5 13h0 0l-1-1c-2-4-2-6-5-9h0c-3-5-5-12-8-17-1-2-2-3-2-5v-1c-2-3-3-7-5-9s-3-7-4-9c1-1 2-1 2-2l-1-3z" class="L"></path><path d="M470 830l1-1c0 2 0 3 2 5h1l3 6-3-1-4-9z" class="X"></path><path d="M474 839l3 1v2c2 4 1 6 4 9h-2l-5-12z" class="F"></path><path d="M460 808c2 1 2 2 4 2h0c1 0 2 0 3 1 1 2 1 3 2 5 0 2 0 4-1 5l-1 1c-2-3-4-7-6-11l-1-3z" class="r"></path><path d="M460 808c2 1 2 2 4 2h0c0 2 0 3 1 5 2 2 2 4 3 6l-1 1c-2-3-4-7-6-11l-1-3z" class="X"></path><path d="M474 834h1c1 1 1 2 2 1h2c0 3 0 5 1 8l1 1h2l1 1 2-2v1 1c-1 1-1 2-2 3l-1 1-1 1-1 1c-3-3-2-5-4-9v-2l-3-6z" class="m"></path><path d="M469 816c1 3 2 7 4 9h0c2 2 4 2 5 3l-6 4c3 1 7 1 9 3h0l1 5c0 2 0 3 1 4h-2l-1-1c-1-3-1-5-1-8h-2c-1 1-1 0-2-1h-1-1c-2-2-2-3-2-5l-1 1-2-4-1-4 1-1c1-1 1-3 1-5z" class="F"></path><path d="M468 826h1 1v-1 1c0 1 0 0 1 1v2l-1 1-2-4z" class="j"></path><path d="M485 836l1 1h0c1-1 2-2 3-2 1 1 2 3 4 4l2 2 3 2-2 2 2 1h-1l-1 1h-1c-3-1-6-1-9 0 0 1-1 1-1 2h-2l1-1c1-1 1-2 2-3v-1-1l-2 2-1-1c-1-1-1-2-1-4l-1-5 1 2 1-1h2z" class="b"></path><path d="M482 840l2-1c2 1 4 2 6 2-1 1-1 1-2 1s-1 1-2 1l-2 2-1-1c-1-1-1-2-1-4zm3-4l1 1h0c1-1 2-2 3-2 1 1 2 3 4 4l2 2-1 1c-2-1-3-1-4-2-3-1-5-1-8-3l1-1h2z" class="N"></path><path d="M467 811c2 1 3 1 4 3 1 0 1 0 2 1 2 2 3 3 4 5l1 1c2-1 4-4 6-5h3c-1 1-3 2-3 3s0 2-1 2c1 1 2 1 3 1-1 2 0 5-1 7 0 1 1 1 2 1h1l1 5c-1 0-2 1-3 2h0l-1-1h-2l-1 1-1-2h0c-2-2-6-2-9-3l6-4c-1-1-3-1-5-3h0c-2-2-3-6-4-9-1-2-1-3-2-5z" class="D"></path><path d="M467 811c2 1 3 1 4 3l3 4-1 6v1c-2-2-3-6-4-9-1-2-1-3-2-5z" class="f"></path><path d="M476 822c2 0 4 0 6 1 0 4-1 8 2 12l1 1h0-2l-2-2v-3h-3 0c0-1 1-1 1-1 1-1 1-3 1-4-1-1-1-1-2-1h-4v-1c1-2 0-1 2-2z" class="l"></path><path d="M487 816c-1 1-3 2-3 3s0 2-1 2c1 1 2 1 3 1-1 2 0 5-1 7 0 1 1 1 2 1h1l1 5c-1 0-2 1-3 2h0l-1-1h0l-1-1c-3-4-2-8-2-12-2-1-4-1-6-1 1-1 1-1 2-1 2-1 4-4 6-5h3z" class="U"></path><path d="M485 829c0 1 1 1 2 1h1l1 5c-1 0-2 1-3 2h0l-1-1h0v-1c0-2-1-4 0-6z" class="L"></path><path d="M514 840c0-1 2-4 3-5l1-1 2-2c0 1 0 2-1 4s-1 4-2 6-5 5-5 6l1 1c0-1 1-1 1-1h1l-2 4h0v2l-1 1c-1 4-3 8-4 11-1 1-3 2-3 4-1 1-1 2 0 3l-1 1c-2 1-3 2-4 2-1 1-3 2-3 3v2h1c1 1 2 1 2 2l-2 2h-1l-1-2-1-2-1-1-1-2c0-1-2-4-3-5 0-2-1-4-2-6 0-2-1-3-1-5h-1v1l-2 1-5-13h2l1-1 1-1h2c0-1 1-1 1-2 3-1 6-1 9 0h1c2 0 4 1 5 0 2 0 5-2 7-3h3 1c1-1 1-2 2-4z" class="U"></path><path d="M496 847c2 0 4 1 5 0 2 0 5-2 7-3h3c-4 4-12 9-17 9l-3-1-4-1c0-1-1-1-2-2h0c0-1 1-1 1-2 3-1 6-1 9 0h1z" class="X"></path><path d="M485 849c0-1 1-1 1-2 3-1 6-1 9 0 1 1 2 1 2 3-1 2 0 2-2 2-4-1-5-3-10-3h0z" class="m"></path><path d="M483 849h2 0c1 1 2 1 2 2l4 1c1 1 1 3 2 3h4l1 1c3 1 5-3 7-2h0c-2 2-8 5-9 7v1c0 1-1 2-2 3v-1c-1 1-1 2-2 3v2c-1-1-1-1-1-2h-1c-1-2-2-4-3-5h-1v1l-2 1-5-13h2l1-1 1-1z" class="H"></path><path d="M483 849h2 0c1 1 2 1 2 2v1h-1c-1 0-2 1-3 2l-1-4 1-1z" class="C"></path><path d="M487 851l4 1c1 1 1 3 2 3h4l1 1c-2 1-3 1-5 0-3 0-4-1-6-4v-1z" class="n"></path><path d="M484 856c2 0 3-1 5 0 0 1 1 1 1 2 1 0 1 0 2 1v-1l-1-1h6c1 0 1 0 1 1l-1 1c-1 0-1 1-3 2-3-2-7-2-10-5z" class="C"></path><path d="M481 851l1-1 1 4 1 2c3 3 7 3 10 5v1l-1 1c-1 1-2 2-2 4h-1c-1-2-2-4-3-5h-1v1l-2 1-5-13h2z" class="E"></path><path d="M513 852h0v2l-1 1c-1 4-3 8-4 11-1 1-3 2-3 4-1 1-1 2 0 3l-1 1c-2 1-3 2-4 2-1 1-3 2-3 3v2h1c1 1 2 1 2 2l-2 2h-1l-1-2-1-2-1-1-1-2c0-1-2-4-3-5 0-2-1-4-2-6 0-2-1-3-1-5 1 1 2 3 3 5h1c0 1 0 1 1 2v-2c1-1 1-2 2-3v1l1 1c3-1 6-5 9-8l1 1 2-2c0-2 4-4 6-5z" class="i"></path><path d="M498 867v1 1c-1 0-1 1-1 2h1s0-1 1 0h0c-3 3-4 5-4 10l-1-1v-7c1-2 2-4 4-6z" class="M"></path><path d="M505 859l2-2c0 1-1 2-1 3 1 0 1 0 1 1s0 1 1 2l-4 4v-1-1h-1c0 2-1 3-3 5l-1 1h0c-1-1-1 0-1 0h-1c0-1 0-2 1-2v-1-1-1c3-2 5-4 7-7z" class="O"></path><path d="M506 860c1 0 1 0 1 1s0 1 1 2l-4 4v-1-1h-1l-1 1-1-1c1-1 1-1 1-2 2-1 3-2 4-3z" class="Q"></path><path d="M503 865h1v1 1c-1 1-2 2-3 4l1 1-1 1h1l2 1c-2 1-3 2-4 2-1 1-3 2-3 3v2h1c1 1 2 1 2 2l-2 2h-1l-1-2-1-2c0-5 1-7 4-10l1-1c2-2 3-3 3-5z" class="L"></path><path d="M513 852h0v2l-1 1c-1 4-3 8-4 11-1 1-3 2-3 4-1 1-1 2 0 3l-1 1-2-1h-1l1-1-1-1c1-2 2-3 3-4l4-4c-1-1-1-1-1-2s0-1-1-1c0-1 1-2 1-3 0-2 4-4 6-5z" class="H"></path><path d="M502 872l2-3v1 3h-2-1l1-1zm11-20h0v2l-1 1c-2 2-4 5-4 8-1-1-1-1-1-2s0-1-1-1c0-1 1-2 1-3 0-2 4-4 6-5z" class="D"></path><path d="M504 807c4 3 6 8 8 13l3-1c1 3 2 5 2 8v-2c1 0 1 0 1 1 1 1 1 1 2 1v1c1 1 1 0 0 1v3l-2 2-1 1c-1 1-3 4-3 5-1 2-1 3-2 4h-1-3c-2 1-5 3-7 3-1 1-3 0-5 0l1-1h1l-2-1 2-2-3-2-2-2c-2-1-3-3-4-4l-1-5h-1c-1 0-2 0-2-1 1-2 0-5 1-7-1 0-2 0-3-1 1 0 1-1 1-2s2-2 3-3c0-1 1-1 2-2 4-1 11-1 15 0h1l-1-1h0v-6z" class="N"></path><path d="M509 821c2 2 3 3 3 5s0 3-1 4l-2 2h-1c1-1 2-2 1-4 0-2-1-3-3-4 1-1 1-1 1-2h1c1 0 1 0 1-1z" class="X"></path><path d="M487 816c0-1 1-1 2-2 4-1 11-1 15 0v2c-2 1-5-1-7 0-1 1-1 1-2 1l-1-1c-3 2-4 5-8 6-1 0-2 0-3-1 1 0 1-1 1-2s2-2 3-3z" class="a"></path><defs><linearGradient id="Br" x1="501.998" y1="834.954" x2="517.625" y2="823.929" xlink:href="#B"><stop offset="0" stop-color="#151414"></stop><stop offset="1" stop-color="#2f2f2e"></stop></linearGradient></defs><path fill="url(#Br)" d="M515 819c1 3 2 5 2 8-2 5-3 9-7 11-3 2-7 2-10 2 1-1 2 0 4-1l-1-1c-1-1-1-2-2-3h0c2 0 4 1 6 0s4-3 5-5c2-3 1-7 0-10l3-1z"></path><path d="M488 830c0-2 0-4 1-6 2-3 5-5 8-6h1c1-1 2-1 3-1 4 0 6 2 8 4 0 1 0 1-1 1h-1c0 1 0 1-1 2l-1-1-4-1c-2 0-4 0-5 1-3 1-4 5-5 8 0 3 0 5 2 8h0c-2-1-3-3-4-4l-1-5z" class="I"></path><path d="M505 823c0-1 0-1-1-2v-1h2l1 2c0 1 0 1-1 2l-1-1z" class="F"></path><defs><linearGradient id="Bs" x1="499.866" y1="821.215" x2="510.391" y2="841.058" xlink:href="#B"><stop offset="0" stop-color="#bfbebf"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#Bs)" d="M493 839h0c-2-3-2-5-2-8 1-3 2-7 5-8 1-1 3-1 5-1l1 1c1 0 1 1 2 1 1 1 1 1 1 2l-1 1h0c0 2 0 2-1 4-1 0 0 0-1-1-1 0-1 1-2 2 0 1 0 2 1 3h0c1 1 1 2 2 3l1 1c-2 1-3 0-4 1 3 0 7 0 10-2 4-2 5-6 7-11v-2c1 0 1 0 1 1 1 1 1 1 2 1v1c1 1 1 0 0 1v3l-2 2-1 1c-1 1-3 4-3 5-1 2-1 3-2 4h-1-3c-2 1-5 3-7 3-1 1-3 0-5 0l1-1h1l-2-1 2-2-3-2-2-2z"></path><path d="M514 840c-1 2-1 3-2 4h-1-3c-2 1-5 3-7 3-1 1-3 0-5 0l1-1h1l-2-1 2-2c5 1 11 0 16-3z" class="g"></path><path d="M500 840c-1-1-2-1-3-2-2 0-3-2-3-3-1-3 0-6 1-8 1-1 3-2 4-2 2 0 4 1 5 2 0 2 0 2-1 4-1 0 0 0-1-1-1 0-1 1-2 2 0 1 0 2 1 3h0c1 1 1 2 2 3l1 1c-2 1-3 0-4 1z" class="r"></path><path d="M386 585l-1 2h1l4-1v1c-4 2-6 4-7 8s-1 8 1 12v2l-1 1 2 4h1v1c0 1 3 2 3 3h0c1 1 2 1 3 2l1 1h1s1 0 1 1h1c1 0 2 0 3 1h3l2 1 6-2c-2 1-3 2-5 3h0v1h-1c0 1-1 1-1 2h-4c-1 1-1 2-1 3l3 1h1c1-1 2-1 4-1h-1c-1 1-3 2-4 4h2v1c-1 0-2 1-3 2-2 2-5 4-6 7l1 2v-1c2 0 2 1 3 2 1 0 1 1 2 2v1h0c1 2 1 5 2 6l1 1c2 2 3 4 5 6l-1 1c0 1 1 1 2 2l1 1c0 1 0 1-1 1v1 1c0 1 0 2 1 3l1 2 2-2h1c-2 6 1 17 3 23l1-1c2 7 5 13 9 19 1 2 10 12 10 13 7 6 16 12 26 16l3 1c1 1 2 1 3 2h0 13c5 1 9 3 12 7l1-2h-1l1-1h1v-1c1 2 2 4 2 5l3-1 3 9c0 3 1 5 2 8 2 3 3 7 5 11 0 1-1 2-1 2l2 5c2 2 3 4 4 6 1-2 0-3 0-5l2-1v2l2 3c0 2-1 3 0 5s1 3 0 5l1 2c0 3-1 4-2 6 2 5 4 12 5 17l-2 1v5l-1 3c1 0 1 0 2 1l3-1c-1 1-1 2-1 3-1 1-1 1-1 2-2 1-2 2-3 3-2 2-3 3-4 5l-4 3v-2h0l2-4h-1s-1 0-1 1l-1-1c0-1 4-4 5-6s1-4 2-6 1-3 1-4v-3c1-1 1 0 0-1v-1c-1 0-1 0-2-1 0-1 0-1-1-1v2c0-3-1-5-2-8l-3 1c-2-5-4-10-8-13v6h0l1 1h-1c-4-1-11-1-15 0-1 1-2 1-2 2h-3c-2 1-4 4-6 5l-1-1c-1-2-2-3-4-5-1-1-1-1-2-1-1-2-2-2-4-3-1-1-2-1-3-1h0c-2 0-2-1-4-2l1 3c0 1-1 1-2 2l-1-3h-1l-3-6-3-6-3-6c-1-2 0-4 0-6l-4-9h-1-1c-1-1-1-3-2-4 0-2-1-4-2-5h0l-1 2c-1-3-2-7-5-9l-7-19-23-54c1-1 0-3 2-5l-9-18c-1-3-1-8-3-10l-3-5c-3-4-4-9-6-14l-5-12c-2-2-3-5-4-8-2-4-4-7-5-11v-3c0-1 0-2 1-2l1-1c-1-3-1-5-1-8 2-2 1-4 2-6 3 0 3 0 5 2l5-2h4z" class="n"></path><path d="M468 772c4 2 9 3 13 6-3 0-6 0-8-1l-1-1c-2-1-3-2-4-4z" class="C"></path><path d="M468 780l1 1c5 2 10 1 16 2l1 1h1l1-1c1 1 1 2 1 4l-1 1v1l-1 2h-1c0-1 0-3-1-3-1 1-1 1-1 2l-1-1 1-2v-1l-1-1-1 1c-1-1-4-1-6-1s-4-1-6-2c0 0-1-1-2-3z" class="R"></path><path d="M381 624c3 6 5 13 8 19 5 12 9 25 16 36l8 19c1 3 4 7 5 10h-1l-1-2h0c-1-2-2-3-2-5l-6-13-1-1v-1l-3-5c-1-4-3-7-4-11-1 0-1-1-1-1l-1-2c0 1 0 1 1 3v1c0 2 1 3 2 5l12 27c1 2 3 6 3 8-2-2-3-6-4-9l-8-19-9-18c-1-3-1-8-3-10l-3-5c-3-4-4-9-6-14l-5-12 1 2c1 1 2 3 3 4h0l-1-3v-3z" class="C"></path><path d="M444 763c3 1 5 5 8 7 4 3 8 6 12 8l4 2c1 2 2 3 2 3 2 1 4 2 6 2s5 0 6 1c-1 1-2 1-3 1v1h1c1 0 0 0 1 1s1 1 1 2c-1 2-1 2-3 3-3 1-4 1-6 2-2 0-2 0-3-1 0-1 1-1 1-2-1 0-1 0-2-1l-2-3v-1h-1s-1-2-2-2c0-2-3-3-4-5-2 0-3 0-4 1h-1-1c-1-1-1-2-2-2 0-2 1-4 1-5l-6-7h0c-1-1-2-4-3-5z" class="J"></path><path d="M456 777l4 4c-2 0-3 0-4 1l-1-3 1-2z" class="Q"></path><path d="M453 775l3 2h0l-1 2 1 3h-1-1c-1-1-1-2-2-2 0-2 1-4 1-5z" class="V"></path><path d="M464 778l4 2c1 2 2 3 2 3l-2 2-4-1v-1h2 1v-1c-2-1-2-2-3-4z" class="g"></path><path d="M470 783c2 1 4 2 6 2s5 0 6 1c-1 1-2 1-3 1v1h1c1 0 0 0 1 1s1 1 1 2c-1 2-1 2-3 3-3 1-4 1-6 2-2 0-2 0-3-1 0-1 1-1 1-2-1 0-1 0-2-1l-2-3h2 1c1 0 1-1 3 0h1c1-1 2-1 3-1-2-2-7-1-9-3l2-2z" class="b"></path><path d="M477 792l2 2c-3 1-4 1-6 2-2 0-2 0-3-1 0-1 1-1 1-2h3v2c2-1 2-1 3-3z" class="I"></path><path d="M477 792h-2-1v-1c2-2 4-3 7-2 1 1 1 1 1 2-1 2-1 2-3 3l-2-2z" class="r"></path><path d="M468 772c-4-2-11-6-13-9 1 0 1 0 2 1 4 4 10 8 16 10 2 0 4 1 6 1 2 1 3 2 5 2v-1h1c1 1 1 1 0 3h1l1-1-1-1 1-3 7 9c3 3 6 7 8 10 1 2 2 3 3 5h0c-1 0-2 1-2 2v1c-2-1-4-1-6-2l-9-2v-1h-1c-1 0-2-1-3-1h-2c0-1 1-1 1-2h1l-2-2c0-1 0-1-1-2s0-1-1-1h-1v-1c1 0 2 0 3-1l1-1 1 1v1l-1 2 1 1c0-1 0-1 1-2 1 0 1 2 1 3h1l1-2c1-1 2-4 2-6l-9-5c-4-3-9-4-13-6z" class="p"></path><path d="M482 786l1-1 1 1v1l-1 2 1 1c1 1 1 2 3 3l8 1c-1 1-2 1-4 1h-3v1h-1c-1 0-2-1-3-1h-2c0-1 1-1 1-2h1l-2-2c0-1 0-1-1-2s0-1-1-1h-1v-1c1 0 2 0 3-1z" class="U"></path><path d="M495 794c4 1 7 1 10 4h0c-1 0-2 1-2 2v1c-2-1-4-1-6-2l-9-2v-1-1h3c2 0 3 0 4-1z" class="a"></path><path d="M452 780c1 0 1 1 2 2h1 1c1-1 2-1 4-1 1 2 4 3 4 5 1 0 2 2 2 2h1v1l2 3c1 1 1 1 2 1 0 1-1 1-1 2 1 1 1 1 3 1-3 1-4 2-6 4v1s1 0 1 1h0l-2 4c-2 0-4 0-5 2h-1c-2-3-4-7-5-12 0-2-2-3-3-6v-4h1c-1-2-1-4-1-6z" class="r"></path><path d="M452 780c1 0 1 1 2 2h1 1c1-1 2-1 4-1 1 2 4 3 4 5 0 1 0 1-1 1-1 2 1 5 0 7h-2l-1-1c1-1 1-3 1-4-1-1-1-2-2-3v-1c-1-1-2-2-3-2 0 1 0 1-1 2h0c1 2 0 5 1 7l1-1c0 1 0 2-1 3v2h0-1c0-2-2-3-3-6v-4h1c-1-2-1-4-1-6z" class="i"></path><path d="M459 785v1c1 1 1 2 2 3 0 1 0 3-1 4l1 1h2c1-2-1-5 0-7 1 0 1 0 1-1 1 0 2 2 2 2h1v1l2 3c1 1 1 1 2 1 0 1-1 1-1 2 1 1 1 1 3 1-3 1-4 2-6 4v1s1 0 1 1h0-1c-1-1-1-2-2-2s-2 1-3 1-2-1-2-2l-2-2c0-4 1-8 1-12z" class="S"></path><path d="M466 788h1v1l2 3c1 1 1 1 2 1 0 1-1 1-1 2 1 1 1 1 3 1-3 1-4 2-6 4v1s1 0 1 1h0-1c-1-1-1-2-2-2s-2 1-3 1-2-1-2-2l6 1c0-2 1-3 1-5 0-3 0-4-1-7z" class="G"></path><path d="M389 643l1-2-3-6 1-1 12 4c-2 2-5 4-6 7l1 2v-1c2 0 2 1 3 2 1 0 1 1 2 2v1h0c1 2 1 5 2 6l1 1c2 2 3 4 5 6l-1 1c0 1 1 1 2 2l1 1c0 1 0 1-1 1v1 1c-1 2-2 5-2 7l-2 1c-7-11-11-24-16-36z" class="m"></path><path d="M409 669c-1-1-3-2-5-4-3-4-5-10-8-14l1-1h1l1 2 1-1c1 2 1 5 2 6l1 1c2 2 3 4 5 6l-1 1c0 1 1 1 2 2l1 1c0 1 0 1-1 1z" class="l"></path><path d="M387 621c0-2-1-3-2-4 1 0 2 0 3 1h1 0c1 1 2 1 3 2l1 1h1s1 0 1 1h1c1 0 2 0 3 1h3l2 1 6-2c-2 1-3 2-5 3h0v1h-1c0 1-1 1-1 2h-4c-1 1-1 2-1 3l3 1h1c1-1 2-1 4-1h-1c-1 1-3 2-4 4h2v1c-1 0-2 1-3 2l-12-4-1 1 3 6-1 2c-3-6-5-13-8-19 0-1-1-3-2-4 1-1 2-1 3-1 2 0 2 0 4 1l1 1z" class="F"></path><path d="M382 619c2 0 2 0 4 1l1 1 6 4h-4-1v-1c-3 0-5-3-6-5z" class="d"></path><path d="M391 629c5-2 9-3 14-4v1h-1c0 1-1 1-1 2h-4c-1 1-1 2-1 3h-3c-1-1-2-1-4-2zm-4-8c0-2-1-3-2-4 1 0 2 0 3 1h1 0c1 1 2 1 3 2l1 1h1s1 0 1 1h1c1 0 2 0 3 1h3l2 1c-4 1-7 1-11 1l-6-4z" class="n"></path><path d="M390 630l1-1c2 1 3 1 4 2h3l3 1h1c1-1 2-1 4-1h-1c-1 1-3 2-4 4h2v1c-1 0-2 1-3 2l-12-4-1-1 3-3z" class="C"></path><path d="M390 630l1-1c2 1 3 1 4 2h3l3 1c-3 1-5 1-8 2l-1-1-2-3z" class="q"></path><path d="M444 777l-13-29h0c1 0 1 1 1 1l1 1 1-1 10 14c1 1 2 4 3 5h0l6 7c0 1-1 3-1 5s0 4 1 6h-1v4c1 3 3 4 3 6 1 5 3 9 5 12l1 3c0 1-1 1-2 2l-1-3h-1l-3-6-3-6-3-6c-1-2 0-4 0-6l-4-9z" class="N"></path><path d="M448 786l4 9c2 5 5 10 6 15h-1l-3-6-3-6-3-6c-1-2 0-4 0-6z" class="P"></path><path d="M434 749l10 14c1 1 2 4 3 5h0l6 7c0 1-1 3-1 5s0 4 1 6h-1v4c-1-5-4-10-6-14l-13-26 1-1z" class="p"></path><path d="M447 768h0l6 7c0 1-1 3-1 5s0 4 1 6h-1c-1-1-2-10-3-12 0-2-1-4-2-6z" class="D"></path><defs><linearGradient id="Bt" x1="408.335" y1="727.989" x2="431.319" y2="724.635" xlink:href="#B"><stop offset="0" stop-color="#14171b"></stop><stop offset="1" stop-color="#484441"></stop></linearGradient></defs><path fill="url(#Bt)" d="M402 688c1-1 0-3 2-5l8 19c1 3 2 7 4 9 2 6 5 12 7 18 3 7 6 14 11 20l-1 1-1-1s0-1-1-1h0l13 29h-1-1c-1-1-1-3-2-4 0-2-1-4-2-5h0l-1 2c-1-3-2-7-5-9l-7-19-23-54z"></path><path d="M386 585l-1 2h1l4-1v1c-4 2-6 4-7 8s-1 8 1 12v2l-1 1 2 4h1v1c0 1 3 2 3 3h-1c-1-1-2-1-3-1 1 1 2 2 2 4l-1-1c-2-1-2-1-4-1-1 0-2 0-3 1 1 1 2 3 2 4v3l1 3h0c-1-1-2-3-3-4l-1-2c-2-2-3-5-4-8-2-4-4-7-5-11v-3c0-1 0-2 1-2l1-1c-1-3-1-5-1-8 2-2 1-4 2-6 3 0 3 0 5 2l5-2h4z" class="Q"></path><path d="M378 609v-1h0v-5 1c1 1 2 1 2 2l3 6 2 2h1v1c0 1 3 2 3 3h-1c-1-1-2-1-3-1 1 1 2 2 2 4l-1-1c-2-1-2-1-4-1-2-3-3-7-4-10z" class="C"></path><path d="M386 585l-1 2c-1 1-3 2-3 3-1 0-2 4-2 6-1 1-1 3-1 5-1-2 0-6 0-8 0-1 0-2 1-2-1 0-2 1-3 2l-1 3-1-1v3l-1 1v1-9c1-2 1-3 3-4l5-2h4z" class="I"></path><path d="M370 591c2-2 1-4 2-6 3 0 3 0 5 2-2 1-2 2-3 4v9-1l1-1v-3l1 1c-1 4-1 9 2 13 1 3 2 7 4 10-1 0-2 0-3 1 1 1 2 3 2 4v3l1 3h0c-1-1-2-3-3-4l-1-2c-2-2-3-5-4-8-2-4-4-7-5-11v-3c0-1 0-2 1-2l1-1c-1-3-1-5-1-8z" class="B"></path><path d="M370 591c2-2 1-4 2-6 3 0 3 0 5 2-2 1-2 2-3 4h0c-1 5-1 9 0 13-1-1-2-3-3-5-1-3-1-5-1-8z" class="Q"></path><path d="M370 600l9 20c1 1 2 3 2 4v3l1 3h0c-1-1-2-3-3-4l-1-2c-2-2-3-5-4-8-2-4-4-7-5-11v-3c0-1 0-2 1-2z" class="U"></path><path d="M482 791l2 2h-1c0 1-1 1-1 2h2c1 0 2 1 3 1h1v1l9 2c2 1 4 1 6 2l2 1c2 2 5 4 5 7 3 3 4 6 5 10l-3 1c-2-5-4-10-8-13v6h0l1 1h-1c-4-1-11-1-15 0-1 1-2 1-2 2h-3c-2 1-4 4-6 5l-1-1c-1-2-2-3-4-5-1-1-1-1-2-1-1-2-2-2-4-3-1-1-2-1-3-1h0c-2 0-2-1-4-2h1c1-2 3-2 5-2l2-4h0c0-1-1-1-1-1v-1c2-2 3-3 6-4 2-1 3-1 6-2 2-1 2-1 3-3z" class="o"></path><path d="M470 805v1c-1 0-2 1-2 1-2 1-3-1-4 1l1 1-1 1c-2 0-2-1-4-2h1c1-2 3-2 5-2l4-1z" class="N"></path><path d="M482 803c2 0 5 0 7 1-2 1-4 1-6 2h0c-2 1-6 0-8 0l-1-1h-1l1-1c2-1 5-1 8-1z" class="Q"></path><path d="M479 800c3-1 6 0 9 0l4 3 3 2-6-1c-2-1-5-1-7-1-1 0-2 0-3-1v-2z" class="E"></path><path d="M474 800h0 2 3v2c1 1 2 1 3 1-3 0-6 0-8 1-2 0-3 0-4 1l-4 1 2-4h0c2-1 4-2 6-2zm-2 10l-1-1c3-1 6-1 8-2 7-1 14 0 21 1l1 1h0-1c-6-1-12-2-18-1l-3 3c-1 1-2 2-3 2l-1-1c-1 0-2-1-3-2z" class="B"></path><path d="M501 809v-1c1 0 1 1 2 1s0-1 0-1l1-1v6h0c-3-1-7-1-10-1l-3-1c-2-2-5 0-7-2h-1l-1-1c6-1 12 0 18 1h1 0z" class="U"></path><path d="M482 808l1 1h1c2 2 5 0 7 2l3 1c-3 0-5 1-8 1v-1l-3 3h-1c-2-1-4 1-6 1-1-1-2-2-3-2h0c0-1 0-1-1-2v-1-1c1 1 2 2 3 2l1 1c1 0 2-1 3-2l3-3z" class="N"></path><path d="M482 808l1 1h1c2 2 5 0 7 2-2 1-4 1-7 1h0c-2 0-3-1-5-1l3-3z" class="a"></path><path d="M494 812c3 0 7 0 10 1l1 1h-1c-4-1-11-1-15 0-1 1-2 1-2 2h-3c-2 1-4 4-6 5l-1-1c-1-2-2-3-4-5v-1c1 0 2 1 3 2 2 0 4-2 6-1h1l3-3v1c3 0 5-1 8-1z" class="l"></path><path d="M482 791l2 2h-1c0 1-1 1-1 2h2c1 0 2 1 3 1h1v1l9 2h-4c-1 0-1 1-2 1h-3c-3 0-6-1-9 0h-3-2 0c-2 0-4 1-6 2 0-1-1-1-1-1v-1c2-2 3-3 6-4 2-1 3-1 6-2 2-1 2-1 3-3z" class="Q"></path><path d="M488 797l9 2h-4c-1 0-1 1-2 1h-3c-3 0-6-1-9 0h-3-2 0l-2-1v-1h2 1 1 2c1 0 2-1 3-1 2-1 5 0 7 0z" class="b"></path><path d="M491 800c1 0 1-1 2-1h4c2 1 4 1 6 2l2 1c2 2 5 4 5 7 3 3 4 6 5 10l-3 1c-2-5-4-10-8-13l-1 1s1 1 0 1-1-1-2-1v1l-1-1c-2-1-3-2-5-3l-3-2-4-3h3z" class="P"></path><path d="M492 803c3-1 7 0 10 2l2 2-1 1s1 1 0 1-1-1-2-1v1l-1-1c-2-1-3-2-5-3l-3-2z" class="C"></path><path d="M491 800c1 0 1-1 2-1h4c2 1 4 1 6 2l2 1c2 2 5 4 5 7-2-2-4-3-5-5h-1v1c-5-3-8-4-13-5z" class="c"></path><defs><linearGradient id="Bu" x1="451.27" y1="779.639" x2="449.665" y2="692.344" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#Bu)" d="M407 678c0-2 1-5 2-7 0 1 0 2 1 3l1 2 2-2h1c-2 6 1 17 3 23l1-1c2 7 5 13 9 19 1 2 10 12 10 13 0 2 1 3 2 5 7 7 14 12 23 16 3 3 8 4 11 7 1 0 2 0 2 1h0l9 7-1 1 2 2c1 0 2 2 3 4h-3c1 0 2 3 2 3l-1 3 1 1-1 1h-1c1-2 1-2 0-3h-1-1c-1-1-3-2-4-3l-7-4c-19-12-34-27-46-46-3-5-6-10-8-15-1-3-4-7-5-10l-8-19 2-1z"></path><path d="M418 696c2 7 5 13 9 19 1 2 10 12 10 13 0 2 1 3 2 5l-3-2c-8-10-15-22-19-34l1-1z" class="D"></path><path d="M473 756c1 0 2 0 2 1h0l9 7-1 1 2 2c1 0 2 2 3 4h-3c-3-4-7-6-10-9-2 0-5-3-6-3v-1h1 1l-1-2c1-1 2 0 3 0z" class="R"></path><path d="M473 756c1 0 2 0 2 1h0l-2 1 2 2h1l5 5c-2-1-3-2-5-3h-1c-2 0-5-3-6-3v-1h1 1l-1-2c1-1 2 0 3 0z" class="g"></path><path d="M407 678c0-2 1-5 2-7 0 1 0 2 1 3l1 2v1c1 1 1 2 1 3l-1 1c0 1 0 3 1 4 1 4 3 10 1 13l-8-19 2-1z" class="F"></path><path d="M407 678c0-2 1-5 2-7 0 1 0 2 1 3l1 2v1l-2 5h0c-1-1-2-3-2-4z" class="N"></path><defs><linearGradient id="Bv" x1="448.567" y1="739.885" x2="467.138" y2="757.904" xlink:href="#B"><stop offset="0" stop-color="#686667"></stop><stop offset="1" stop-color="#7c7c7b"></stop></linearGradient></defs><path fill="url(#Bv)" d="M436 731l3 2c7 7 14 12 23 16 3 3 8 4 11 7-1 0-2-1-3 0l1 2h-1-1v1l-1-1c-9-5-17-10-25-17-3-3-6-6-7-10z"></path><path d="M437 728c7 6 16 12 26 16l3 1c1 1 2 1 3 2h0 13c5 1 9 3 12 7l1-2h-1l1-1h1v-1c1 2 2 4 2 5l3-1 3 9c0 3 1 5 2 8 2 3 3 7 5 11 0 1-1 2-1 2l2 5c2 2 3 4 4 6 1-2 0-3 0-5l2-1v2l2 3c0 2-1 3 0 5s1 3 0 5l1 2c0 3-1 4-2 6 2 5 4 12 5 17l-2 1v5l-1 3c1 0 1 0 2 1l3-1c-1 1-1 2-1 3-1 1-1 1-1 2-2 1-2 2-3 3-2 2-3 3-4 5l-4 3v-2h0l2-4h-1s-1 0-1 1l-1-1c0-1 4-4 5-6s1-4 2-6 1-3 1-4v-3c1-1 1 0 0-1v-1c-1 0-1 0-2-1 0-1 0-1-1-1v2c0-3-1-5-2-8-1-4-2-7-5-10 0-3-3-5-5-7l-2-1v-1c0-1 1-2 2-2h0c-1-2-2-3-3-5-2-3-5-7-8-10l-7-9s-1-3-2-3h3c-1-2-2-4-3-4l-2-2 1-1-9-7h0c0-1-1-1-2-1-3-3-8-4-11-7-9-4-16-9-23-16-1-2-2-3-2-5z" class="n"></path><path d="M496 769c-3-2-6-6-8-9l1-2c1 1 2 1 3 1v4l-1 1h1l1-1h4l-1 6z" class="r"></path><path d="M469 747h13c5 1 9 3 12 7h0l-1 1c-2-1-2-2-4-3-6-3-13-3-20-5z" class="B"></path><path d="M496 750c1 2 2 4 2 5 1 2 1 4 2 5 0 3-2 6-3 8l-1 1 1-6h-4c1-3 0-6 0-8l1-1h0l1-2h-1l1-1h1v-1z" class="C"></path><path d="M493 755l1-1c2 3 3 6 3 9h-4c1-3 0-6 0-8z" class="X"></path><path d="M475 757c4 2 7 5 10 7 9 7 17 17 24 25 2 5 5 10 7 15-2 0-6-10-8-12-2-4-6-7-9-11-1-2-3-4-4-6a57.31 57.31 0 0 0-11-11l-9-7h0z" class="K"></path><path d="M518 789v2l2 3c0 2-1 3 0 5s1 3 0 5l1 2c0 3-1 4-2 6l-3-8c-2-5-5-10-7-15 1 1 2 2 3 4 0 1 0 0 1 1l1-1-1-1c0-1-1-1-1-2v-1c2 2 3 4 4 6 1-2 0-3 0-5l2-1z" class="V"></path><path d="M518 789v2l2 3c0 2-1 3 0 5s1 3 0 5l-4-9c1-2 0-3 0-5l2-1z" class="e"></path><path d="M501 754l3 9c0 3 1 5 2 8 2 3 3 7 5 11 0 1-1 2-1 2-3-3-5-6-8-8-2-2-4-5-6-7l1-1c1-2 3-5 3-8-1-1-1-3-2-5l3-1z" class="m"></path><path d="M488 771c-1-2-2-4-3-4l-2-2 1-1a57.31 57.31 0 0 1 11 11c1 2 3 4 4 6 3 4 7 7 9 11 2 2 6 12 8 12 1 2 2 5 3 8 2 5 4 12 5 17l-2 1v5l-1 3c1 0 1 0 2 1l3-1c-1 1-1 2-1 3-1 1-1 1-1 2-2 1-2 2-3 3-2 2-3 3-4 5l-4 3v-2h0l2-4h-1s-1 0-1 1l-1-1c0-1 4-4 5-6s1-4 2-6 1-3 1-4v-3c1-1 1 0 0-1v-1c-1 0-1 0-2-1 0-1 0-1-1-1v2c0-3-1-5-2-8-1-4-2-7-5-10 0-3-3-5-5-7l-2-1v-1c0-1 1-2 2-2h0c-1-2-2-3-3-5-2-3-5-7-8-10l-7-9s-1-3-2-3h3z" class="H"></path><path d="M517 844l4-4c0 1-1 4-2 5v1c0 1-2 2-2 2 0-1 1-2 1-3 0 0 0-1-1-1z" class="N"></path><path d="M517 844c1 0 1 1 1 1 0 1-1 2-1 3l-4 4h0l2-4h-1c1-2 2-3 3-4z" class="C"></path><path d="M503 801v-1c0-1 1-2 2-2 3 2 4 4 6 7-2-2-4-3-6-3l-2-1z" class="q"></path><path d="M485 771h3c4 4 7 7 10 12 0 1 1 2 2 3 0 1 2 3 3 4v1c-3-2-5-6-8-9l-1 1-7-9s-1-3-2-3z" class="b"></path><path d="M522 835c0-3 0-7-1-10-2-9-7-18-11-26-3-4-5-8-8-12h0c2 1 4 4 6 5h0c2 2 6 12 8 12 1 2 2 5 3 8 2 5 4 12 5 17l-2 1v5z" class="Y"></path><path d="M761 202c3-1 9 0 12 0h-5v1h0c2 3 3 6 5 8h0 0v2l2 1v1h1v1h4l-4 4c-3 2-6 5-9 9-1 2-4 4-5 7 0 1 0 1-1 2l-4 5c-2 5-4 8-2 13-2 4-4 7-4 11 0 1 0 3 1 4l-8 25c-3 10-8 19-12 28l-28 67-6 15c-2 3-4 7-5 10 1 3 0 5 0 7l-78 192-2 3v-1l-32 76c-1 2-1 4-2 5l-11 27v-1c0-1 0-1-1-2-1 0-1 0-2 1h0l-1-2v-2c0-2 1-4 1-6-1-2 1-6 1-8l2-4 18-47 13-31c1-3 2-6 4-9l1-3h-1l33-83c1-2 1-3 0-4 1-1 1-1 0-2v-1l2 1v-1h-1c-2 1-2 1-2 2-1 1-2 1-4 2h0l18-45 10-24 20-47h0c0 3-1 5-2 7 3-1 4-1 6-4l2-1c3-5 6-13 8-19h1l4-10 6-14 11-26 14-34c5-11 9-23 10-35 0 1 1 2 1 3 2-6 1-12 0-19l2-1v2c1-1 1-4 0-6v-4l1-1v1c1 4 1 8 2 11v2c0 1 1 2 1 4l1-18-1-10-2-12-1-1-1-2v-1c0-1-2-2-2-3-2-2-2-4-3-6 0-1 1-1 1-2l1 1c2-1 3-2 5-2h1c1-1 3-1 4-1l1-2 6 2s1-1 2-1 1 0 2-1h2c1 0 2-1 3-1-1-1-2-2-4-2v-1z" class="Q"></path><path d="M622 577l1-1c0 6-5 14-7 19 0-6 3-12 6-18zm134-334h1c-2 5-4 8-2 13-2 4-4 7-4 11l-1 4h-1v-9c1-3 3-6 3-10 1-3 3-6 4-9z" class="V"></path><path d="M594 644l2-2c-1 1-1 2-1 4v2l-1 2-9 23v-2c-2-6 6-21 9-27z" class="h"></path><defs><linearGradient id="Bw" x1="650.69" y1="474.416" x2="679.81" y2="452.584" xlink:href="#B"><stop offset="0" stop-color="#8a8885"></stop><stop offset="1" stop-color="#a8a8aa"></stop></linearGradient></defs><path fill="url(#Bw)" d="M673 443s1 1 1 2h1c0 3-2 5-3 7-4 12-9 23-14 33h-1c0-2 2-5 1-8 1-3 3-6 4-9l11-25z"></path><path d="M603 614c1-1 2-3 3-4 3-6 4-12 7-17l3-7v1c1-1 2-4 2-5l1-1 1-2v-1l1-1v-1l1 1c-3 6-6 12-6 18l-7 15c-3 10-7 22-13 31 0-2 0-2-1-4 0-2 3-7 3-9l1-1v-4c1-3 2-6 4-9z" class="d"></path><defs><linearGradient id="Bx" x1="721.222" y1="305.679" x2="737.024" y2="321.571" xlink:href="#B"><stop offset="0" stop-color="#312f30"></stop><stop offset="1" stop-color="#4a4a4b"></stop></linearGradient></defs><path fill="url(#Bx)" d="M739 273c0 1 1 2 1 3 0 4-1 8-2 12 0 2-2 6-2 7h0l3-6v3c-1 1-1 2-1 4 0-1 0-2 1-3 0-1 1-3 3-3 1-1 1-2 2-3 0-1-1 0 0-1-2 10-6 18-9 27-3 7-5 14-9 21h0l-1 1c-1 1-2 2-4 3h-1-1v-1c1 0 1-1 1-2l-4 7h-1l14-34c5-11 9-23 10-35z"></path><path d="M739 273c0 1 1 2 1 3 0 4-1 8-2 12 0 2-2 6-2 7l-8 20c-3 7-6 14-8 20l-4 7h-1l14-34c5-11 9-23 10-35z" class="Q"></path><path d="M720 335c0 1 0 2-1 2v1h1 1c2-1 3-2 4-3l1-1c-1 3-2 5-4 6-2 2-3 5-4 7-6 10-11 22-15 33l-11 24-17 41h-1c0-1-1-2-1-2l21-51 4-10 6-14 11-26h1l4-7z" class="K"></path><path d="M715 342h1l-17 42c0-1 0-1-1-2l6-14 11-26z" class="H"></path><path d="M698 382c1 1 1 1 1 2l-25 61c0-1-1-2-1-2l21-51 4-10z" class="V"></path><path d="M693 416c1 3 0 5 0 7l-78 192-2 3v-1l80-201z" class="F"></path><path d="M658 477c1 3-1 6-1 8h1l-7 21-4 9h0c-1 1-1 2-1 3l-23 58-1 1-1-1v1l-1 1v1l-1 2-1 1c0 1-1 4-2 5v-1l-3 7c-3 5-4 11-7 17-1 1-2 3-3 4l1-3c2-4 3-8 4-12l15-36 14-34c2-4 4-9 6-14l15-38z" class="h"></path><defs><linearGradient id="By" x1="573.049" y1="687.494" x2="579.686" y2="690.881" xlink:href="#B"><stop offset="0" stop-color="#8a8988"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#By)" d="M599 623v4l-1 1c0 2-3 7-3 9 1 2 1 2 1 4v1l-2 2c-3 6-11 21-9 27v2c-2 6-3 11-4 18v2c-1 2-1 4-2 5l-11 27v-1c0-1 0-1-1-2-1 0-1 0-2 1h0l-1-2v-2c0-2 1-4 1-6-1-2 1-6 1-8l2-4 18-47 13-31z"></path><defs><linearGradient id="Bz" x1="574.901" y1="712.799" x2="567.099" y2="713.701" xlink:href="#B"><stop offset="0" stop-color="#939195"></stop><stop offset="1" stop-color="#adadaa"></stop></linearGradient></defs><path fill="url(#Bz)" d="M569 712h0c2-3 5-7 6-10 2-4 3-8 5-11 0 1 0 2-1 4v2 1l-11 27v-1c0-1 0-1-1-2-1 0-1 0-2 1h0l-1-2v-2c0-2 1-4 1-6 1-1 1-2 2-3 1 0 1 1 2 2z"></path><path d="M565 713c1-1 1-2 2-3 1 0 1 1 2 2h-1c-1 3 0 6-2 9h-2v-2c0-2 1-4 1-6z" class="L"></path><path d="M599 623v4l-1 1c0 2-3 7-3 9 1 2 1 2 1 4v1l-2 2-1-1c-2 4-4 8-5 12-4 8-16 42-20 46l18-47 13-31z" class="H"></path><path d="M595 637c1 2 1 2 1 4v1l-2 2-1-1 2-6z" class="R"></path><defs><linearGradient id="CA" x1="632.72" y1="498.386" x2="657.388" y2="496.855" xlink:href="#B"><stop offset="0" stop-color="#686666"></stop><stop offset="1" stop-color="#7f7f7e"></stop></linearGradient></defs><path fill="url(#CA)" d="M685 411c3-5 6-13 8-19h1l-21 51-11 25c-1 3-3 6-4 9l-15 38c-2 5-4 10-6 14l-14 34-15 36c-1 4-2 8-4 12h-1l33-83c1-2 1-3 0-4 1-1 1-1 0-2v-1l2 1v-1h-1c-2 1-2 1-2 2-1 1-2 1-4 2h0l18-45 10-24 20-47h0c0 3-1 5-2 7 3-1 4-1 6-4l2-1z"></path><path d="M685 411c3-5 6-13 8-19h1l-21 51-11 25c-1 3-3 6-4 9l-15 38c0-4 2-8 3-12l8-18c1-4 3-7 3-11-1 0-1-1-2-1-1 1-2 3-3 4v1c-1 2-1 2-3 2l10-24h1c2 0 4 0 5-1 3-2 18-38 20-44z" class="W"></path><defs><linearGradient id="CB" x1="678.092" y1="445.317" x2="666.717" y2="419.874" xlink:href="#B"><stop offset="0" stop-color="gray"></stop><stop offset="1" stop-color="#b0adac"></stop></linearGradient></defs><path fill="url(#CB)" d="M679 409h0c0 3-1 5-2 7 3-1 4-1 6-4l2-1c-2 6-17 42-20 44-1 1-3 1-5 1h-1l20-47z"></path><path d="M761 202c3-1 9 0 12 0h-5v1h0c2 3 3 6 5 8h0 0v2l2 1v1h1v1h4l-4 4c-3 2-6 5-9 9-1 2-4 4-5 7 0 1 0 1-1 2l-4 5h-1l-1-1c-1 2-2 3-2 5-2 4-4 9-5 13v7c-1 3-1 6-2 8v5c-1 1-1 2-1 3s0 2-1 3 0 0 0 1c-1 1-1 2-2 3-2 0-3 2-3 3-1 1-1 2-1 3 0-2 0-3 1-4v-3l-3 6h0c0-1 2-5 2-7 1-4 2-8 2-12 2-6 1-12 0-19l2-1v2c1-1 1-4 0-6v-4l1-1v1c1 4 1 8 2 11v2c0 1 1 2 1 4l1-18-1-10-2-12-1-1-1-2v-1c0-1-2-2-2-3-2-2-2-4-3-6 0-1 1-1 1-2l1 1c2-1 3-2 5-2h1c1-1 3-1 4-1l1-2 6 2s1-1 2-1 1 0 2-1h2c1 0 2-1 3-1-1-1-2-2-4-2v-1z" class="L"></path><path d="M763 217c1-2 3-2 5-3h2v-3l2 1-1 1 1 2-4 6h-3c-1 1-2 3-3 3v-1-3l1-3z" class="O"></path><path d="M765 221c2-3 4-5 6-8l1 2-4 6h-3z" class="V"></path><path d="M773 211h0 0v2l2 1v1h1v1h4l-4 4c-3 2-6 5-9 9l-1-1c1-1 1-1 1-2l1-1c1-1 2-2 3-4l-1-1-4 6-2 4-3 4h-1c0-3 2-4 2-7h0c3-1 4-4 6-6l4-6-1-2 1-1s0-1 1-1z" class="N"></path><path d="M773 211h0 0v2l2 1v1l-2 3c-1-1-1-2-1-2v-1l-1-2 1-1s0-1 1-1z" class="D"></path><path d="M742 256v2c2 10 3 22 0 32-2 0-3 2-3 3-1 1-1 2-1 3 0-2 0-3 1-4v-3l-3 6h0c0-1 2-5 2-7 1-4 2-8 2-12 2-6 1-12 0-19l2-1z" class="B"></path><defs><linearGradient id="CC" x1="767.111" y1="227.33" x2="747.889" y2="229.17" xlink:href="#B"><stop offset="0" stop-color="#bbbab9"></stop><stop offset="1" stop-color="#e3e1e3"></stop></linearGradient></defs><path fill="url(#CC)" d="M761 202c3-1 9 0 12 0h-5v1h0c2 3 3 6 5 8-1 0-1 1-1 1l-2-1v3h-2c-2 1-4 1-5 3v-2c-2 1-3 3-4 4 0 1-1 2-1 4-2 4-2 9-4 13-1 4-2 7-3 10-1 1-1 2-1 3-1 0-1 0-1 1l-1-1 3-15v-1c1-2 3-7 3-10l3-5-1-1c1-1 1-1 1-2h-2l2-4v-1l-1-2s1-1 2-1 1 0 2-1h2c1 0 2-1 3-1-1-1-2-2-4-2v-1z"></path><path d="M765 205c1 1 2 2 3 4-1 1-2 1-3 1l-3-4c1 0 2-1 3-1z" class="P"></path><path d="M756 208s1-1 2-1 1 0 2-1h2l3 4c-3 1-6 4-8 7v1l-1-1c1-1 1-1 1-2h-2l2-4v-1l-1-2z" class="J"></path><path d="M750 206l6 2 1 2v1l-2 4h2c0 1 0 1-1 2l1 1-3 5c0 3-2 8-3 10v1l-3 15h0c0 1 0 1-1 2v-4l-1-10-2-12-1-1-1-2v-1c0-1-2-2-2-3-2-2-2-4-3-6 0-1 1-1 1-2l1 1c2-1 3-2 5-2h1c1-1 3-1 4-1l1-2z" class="g"></path><path d="M750 206l6 2 1 2v1c-2 0-2 1-4 0v-2l-4-1 1-2z" class="C"></path><path d="M751 227c2-3 2-7 4-9h0c0 1-1 3-1 4v1c0 3-2 8-3 10v1-7z" class="H"></path><path d="M748 219v-7h1c1 1 0 16 0 19v6h-1v-18z" class="b"></path><path d="M746 237h1 0v-12-7l1 1v18h1v-6l2-4v7l-3 15h0c0 1 0 1-1 2v-4l-1-10z" class="d"></path><path d="M744 209h1c1 2 0 6 1 8h0v-7h1l1 1c0 2 0 5-1 7v7 12h0-1l-2-12-1-1-1-2v-1c0-1-2-2-2-3-2-2-2-4-3-6 0-1 1-1 1-2l1 1c2-1 3-2 5-2z" class="R"></path><path d="M744 209h1c1 2 0 6 1 8l-1 2h0c-1-2-1-5-1-8h-2l1-1h1v-1z" class="S"></path><path d="M738 210l1 1c2-1 3-2 5-2v1h-1l-1 1c-1 3 1 10 2 14l-1-1-1-2v-1c0-1-2-2-2-3-2-2-2-4-3-6 0-1 1-1 1-2z" class="i"></path><path d="M433 591c0-4-2-6-3-10l2 5c1 0 1 1 1 2h1l10 26 10 24c1 4 2 9 5 12l28 70 4 10c0 1 1 3 1 4v1 4l-1 1-1-1v2c3 3 5 5 6 9v1h-1l-1 1h1l-1 2c-3-4-7-6-12-7h-13 0c-1-1-2-1-3-2l-3-1c-10-4-19-10-26-16 0-1-9-11-10-13-4-6-7-12-9-19l-1 1c-2-6-5-17-3-23h-1l-2 2-1-2c-1-1-1-2-1-3v-1-1c1 0 1 0 1-1l-1-1c-1-1-2-1-2-2l1-1c-2-2-3-4-5-6l-1-1c-1-1-1-4-2-6h0v-1c-1-1-1-2-2-2-1-1-1-2-3-2v1l-1-2c1-3 4-5 6-7 1-1 2-2 3-2v-1h-2c1-2 3-3 4-4h1c-2 0-3 0-4 1h-1l-3-1c0-1 0-2 1-3h4c0-1 1-1 1-2h1v-1h0c2-1 3-2 5-3 0-1 0-1 1-1 0-1 0-1 1-2v-1l3-2c5-6 9-14 9-22l1-1 1-1c0-1 0-2 1-3h0c0-3 1-6 2-8l4 10z" class="U"></path><path d="M475 723l2-1c1 1 1 1 2 3-1 2-1 3-3 4h-1v-1l1-2c1-1 0-1-1-3z" class="k"></path><path d="M423 658c-1 6-2 13-2 19 0-1-1-3 0-5 0-2 0-4-1-6 0-1-1-3 0-4v-3h1 1v-1h1z" class="D"></path><path d="M450 733c5 2 10 3 16 3-1 1-1 2-2 3-1-1-2-1-4 0l-2-1c-3 0-6-3-8-5z" class="P"></path><path d="M426 636c-2 7-6 15-11 20h0c0-7 8-15 11-20z" class="M"></path><defs><linearGradient id="CD" x1="477.684" y1="739.141" x2="467.249" y2="733.109" xlink:href="#B"><stop offset="0" stop-color="#211f21"></stop><stop offset="1" stop-color="#403f3d"></stop></linearGradient></defs><path fill="url(#CD)" d="M484 729c1 2 1 3 0 4l-1 1c-1 1-1 1-1 2v1c-6 0-12 2-18 2 1-1 1-2 2-3 4 0 12-1 16-3 2-1 2-2 2-4z"></path><path d="M426 649c0 3-1 5-1 7-1 2-1 4-1 6-1 6 0 13 1 19l-3 2-1-6c0-6 1-13 2-19l3-9z" class="T"></path><path d="M408 664c2 1 4 2 5 2h3c1 3 0 4-1 7l-1 1h-1l-2 2-1-2c-1-1-1-2-1-3v-1-1c1 0 1 0 1-1l-1-1c-1-1-2-1-2-2l1-1z" class="b"></path><path d="M409 670h2 0l-1 1h1c1-2 4-2 6-3l-4 6-2 2-1-2c-1-1-1-2-1-3v-1z" class="C"></path><path d="M430 618h1c-1 4-3 9-5 12-6 11-18 17-23 28l-1-1c1-2 2-4 4-6 3-5 7-9 11-13s9-9 11-15l2-5z" class="e"></path><defs><linearGradient id="CE" x1="456.845" y1="744.583" x2="451.383" y2="725.706" xlink:href="#B"><stop offset="0" stop-color="#050707"></stop><stop offset="1" stop-color="#2f2b29"></stop></linearGradient></defs><path fill="url(#CE)" d="M418 696c-2-3-3-9-2-13h0c3 15 9 34 22 43l12 7c2 2 5 5 8 5l2 1h4v1h6v3h-1c-2-1-2-1-4 0-1 0-2 0-2 1-10-4-19-10-26-16 0-1-9-11-10-13-4-6-7-12-9-19z"></path><path d="M482 737c3 1 5 2 8 4 3 3 5 5 6 9v1h-1l-1 1h1l-1 2c-3-4-7-6-12-7h-13 0c-1-1-2-1-3-2l-3-1c0-1 1-1 2-1 2-1 2-1 4 0h1v-3h-6v-1h-4c2-1 3-1 4 0 6 0 12-2 18-2z" class="a"></path><path d="M464 739c10 0 19 0 27 8l2 1c-3-1-5-2-7-3h-1-5l2 2h-13 0c-1-1-2-1-3-2l-3-1c0-1 1-1 2-1 2-1 2-1 4 0h1v-3h-6v-1z" class="G"></path><path d="M470 743l1-1c4-1 11 0 15 2l-18-1h1 1z" class="D"></path><path d="M466 745h14l2 2h-13 0c-1-1-2-1-3-2z" class="Q"></path><defs><linearGradient id="CF" x1="439.874" y1="724.652" x2="450.443" y2="690.085" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#323030"></stop></linearGradient></defs><path fill="url(#CF)" d="M424 662c1 1 1 2 1 3 0 2 0 4 1 5l2 1c1 5 1 9 2 14 0 0 0 1 1 1 1 3 1 5 3 7l-2 1c-1 1 0 2 0 3l2 3h0l-1 2c0 1 1 2 1 3l-2-1c4 8 10 13 18 17 1 1 4 2 5 3s1 2 1 3l2 2 3-3 2 1 1-1c0 1 0 2 2 2l1 1c2 0 2 2 3 3h-1c-9 2-19-3-26-8-14-10-18-25-21-41l3-2c-1-6-2-13-1-19z"></path><defs><linearGradient id="CG" x1="418.736" y1="680.178" x2="437.874" y2="689.623" xlink:href="#B"><stop offset="0" stop-color="#bbbcbb"></stop><stop offset="1" stop-color="#dddadc"></stop></linearGradient></defs><path fill="url(#CG)" d="M424 662c1 1 1 2 1 3 0 2 0 4 1 5l2 1c1 5 1 9 2 14 0 0 0 1 1 1 1 3 1 5 3 7l-2 1c-1 1 0 2 0 3l2 3h0l-1 2c0 1 1 2 1 3l-2-1c-4-8-6-14-7-23-1-6-2-13-1-19z"></path><path d="M430 618c1-2 1-4 2-6v-2c1-3 1-6 1-9 2 7 4 13 6 19 1 3 2 5 3 7v1h-1c-1 2-1 5-1 7l1 1v2c1 2 1 3 2 4 1 3 3 6 6 8h-1l-2-1v1l-2-2-4-7c-1-3-2-7-2-10-2 8-4 17-4 26-1 3 0 6-1 10v-1-4c-1 1 0 2-1 3 0 2-1 5 0 7s0 7 2 8l-1 1v1c-3-5-4-11-5-16-1 2 0 3 0 5l-2-1c-1-1-1-3-1-5 0-1 0-2-1-3 0-2 0-4 1-6 0-2 1-4 1-7 0-1 1-2 1-3 1-1 1-1 1-2l2-4c0-1 0-2 1-3h0v3h-1c0 1 0 2 1 3v-1c1-1 1-2 1-3l3-10v-9h-1c0 4-4 11-6 14h-1l2-4 3-7h0c1-2 1-2 1-4 0-1 1-4 0-5-1 1-1 3-2 4h0-1z" class="N"></path><path d="M425 656l1 3v11c-1-1-1-3-1-5 0-1 0-2-1-3 0-2 0-4 1-6z" class="V"></path><path d="M433 682v-1l1-1c-2-1-1-6-2-8s0-5 0-7c1-1 0-2 1-3v4 1h1c2-2 4-3 7-3s7 2 10 5c1 2 1 4 1 6 0 1-1 2-1 2l-1 2c2 0 4-1 6-2 1-2 2-3 2-5v-1h1v-1h1c1 1 1 1 1 3v3c1 3 1 6 0 9h0l-4 4c-2 0-4 1-6 1v4c-1 1-2 0-2 0l-1 1c-1 1-1 2-2 3l-2-1c-1 0-2 0-2-1l-1 1v4c-3-2-5-5-7-8-2-2-2-4-3-7-1 0-1-1-1-1-1-5-1-9-2-14 0-2-1-3 0-5 1 5 2 11 5 16z" class="X"></path><path d="M439 683c1 0 1-1 1-1 1-1 0-3 0-4l-1-5c1-1 1-1 2-1 0 2-1 4 0 6 2 2 0 4 3 6l1 1c1 1 1 2 1 3-3-1-5-3-7-5z" class="M"></path><path d="M440 669l1 1s1 1 0 2h0c-1 0-1 0-2 1l1 5c0 1 1 3 0 4 0 0 0 1-1 1-1-2-3-5-3-8 0-2 0-3 2-5l2-1z" class="B"></path><path d="M433 682v-1l1-1c-2-1-1-6-2-8s0-5 0-7c1-1 0-2 1-3v4 1h1l2 2h0c1 1 1 1 2 1-2 2-2 3-2 5 0 3 2 6 3 8 2 2 4 4 7 5 1 1 2 2 3 4v2l-1 1h-4l-1-1h4c-1-1-1-1-3-1v-1h1c-1-1 0-1-1-1-4-2-8-5-11-9z" class="U"></path><path d="M428 671c0-2-1-3 0-5 1 5 2 11 5 16 3 4 7 7 11 9 1 0 0 0 1 1h-1v1c2 0 2 0 3 1h-4l1 1h4c-1 1-1 2-2 3l-2-1c-1 0-2 0-2-1l-1 1v4c-3-2-5-5-7-8-2-2-2-4-3-7-1 0-1-1-1-1-1-5-1-9-2-14z" class="P"></path><path d="M432 686l-1-2h1 0c3 4 8 6 12 8v1c2 0 2 0 3 1h-4l1 1c-1 0-2 0-3-1-5-1-7-5-9-8z" class="C"></path><path d="M431 686h1c2 3 4 7 9 8 1 1 2 1 3 1h4c-1 1-1 2-2 3l-2-1c-1 0-2 0-2-1l-1 1v4c-3-2-5-5-7-8-2-2-2-4-3-7z" class="I"></path><path d="M434 667c2-2 4-3 7-3s7 2 10 5c1 2 1 4 1 6 0 1-1 2-1 2l-1 2c2 0 4-1 6-2 1-2 2-3 2-5v-1h1v-1h1c1 1 1 1 1 3l-1 6c-2 3-3 5-7 5-2 1-6 1-9-1-1-1 0-1-1-2 1-1 1-1 2-1h2c0-1-1-1-1-2l1-1c1-2 1-3 1-4-1-2-2-2-4-3h0-3l-1-1-2 1c-1 0-1 0-2-1h0l-2-2z" class="C"></path><path d="M440 669c0-1 1-1 2-2h1c0 1 1 1 2 2l-1 1h-3l-1-1z" class="n"></path><path d="M461 685c2-1 2-1 3-2h1-1c1 1 2 0 3 0 1 1 1 3 1 5 0 1 0 2-1 3h1v1h2c0 1 1 1 1 2h0 1 1v-1l2 5c1 5 3 12 0 16 0 1-1 2-2 3h-1v-1h0c-2 2-4 3-4 5 1 2 2 3 4 3 1 0 1 1 2 2-1 1-1 1-3 1-1 1-3 0-4 0l-1-1h-1-1l-1 1-2-1-3 3-2-2c0-1 0-2-1-3s-4-2-5-3c-8-4-14-9-18-17l2 1c0-1-1-2-1-3l1-2h0l-2-3c0-1-1-2 0-3l2-1c2 3 4 6 7 8v-4l1-1c0 1 1 1 2 1l2 1c1-1 1-2 2-3l1-1s1 1 2 0v-4c2 0 4-1 6-1l4-4h0z" class="L"></path><path d="M466 715l1 1 3-1v1l-3 3c-1 1-1 1-2 1l1-3s0-1-1-1l1-1z" class="f"></path><path d="M465 720c1 0 1 0 2-1 0 2-1 3-1 5l3 1v1h-1-2-1c0-1-1-2-1-3-1 0-1-1-1-2l2-1z" class="b"></path><path d="M473 693l2 5-1 10h-1v-4c0-3 0-8-2-10h1 1v-1z" class="D"></path><path d="M467 709c0-2 0-3 1-4v2l1 1c-1 3 0 4-1 5-1-1 0-1 0-2h-1c-1 2-1 2-1 4l-1 1c1 0 1 1 1 1h-1c-2 1-4 1-6-1 2 0 3-1 4-2h0 0c1-2 4-3 4-5z" class="g"></path><path d="M461 707l2-1 4 3c0 2-3 3-4 5h0c-2 0-4 1-6 0v-4c2 0 3-2 4-3z" class="C"></path><path d="M471 694c2 2 2 7 2 10s0 7-2 9v1c-1-1-1-1-1-2s-1-2-1-4l-1-1v-8-1c1-1 2-3 3-4h0z" class="F"></path><path d="M468 692h2c0 1 1 1 1 2-1 1-2 3-3 4v1 8-2c-1 1-1 2-1 4l-4-3v-1-1l2-4c-1-2-2-3-3-4l-1-1h3 1c0-1 1-1 1-1 1-1 1-1 1-2h1z" class="Z"></path><path d="M468 699v8-2c-1 1-1 2-1 4l-4-3v-1l5-4v-2z" class="W"></path><path d="M468 692h2c0 1 1 1 1 2-1 1-2 3-3 4l-1-1v-2h-3 1c0-1 1-1 1-1 1-1 1-1 1-2h1z" class="n"></path><path d="M461 685c2-1 2-1 3-2h1-1c1 1 2 0 3 0 1 1 1 3 1 5 0 1 0 2-1 3h1v1h-1c0 1 0 1-1 2 0 0-1 0-1 1h-1-3l1 1c1 1 2 2 3 4l-2 4v1 1l-2 1c0-1-1-1-1-2l-2 1-1-1c0-2-2-3-3-4-2-2-3-5-3-7v-4c2 0 4-1 6-1l4-4h0z" class="b"></path><path d="M451 694v-4c2 0 4-1 6-1l2 1-3 2h-1 0c0 2-1 3-2 4v1c1 0 1 0 2-1v1c1 2 2 3 4 4 1 0 1 1 2 2h-1c-2-1-3-2-6-2-2-2-3-5-3-7z" class="D"></path><path d="M459 690c1 0 1 0 2-1l2-3v1c0 2-1 4 1 5h3c0 1 0 1-1 2 0 0-1 0-1 1h-1-3v-1h-3l-1 1c1 2 1 2 2 3l1 1 1 1h0-1c-2-1-3-2-4-4s-1-2 0-4l3-2z" class="a"></path><path d="M461 685c2-1 2-1 3-2h1-1c1 1 2 0 3 0 1 1 1 3 1 5 0 1 0 2-1 3h1v1h-1-3c-2-1-1-3-1-5v-1l-2 3c-1 1-1 1-2 1l-2-1 4-4h0z" class="N"></path><path d="M464 683c1 1 2 0 3 0 1 1 1 3 1 5 0 1 0 2-1 3-1 0-1 0-2-1-1-2-1-4-1-7z" class="q"></path><path d="M434 693c2 3 4 6 7 8v-4l1-1c0 1 1 1 2 1l2 1c1-1 1-2 2-3l1-1s1 1 2 0c0 2 1 5 3 7 1 1 3 2 3 4l1 1 2-1c0 1 1 1 1 2-1 1-2 3-4 3v4h-3v-1c-4 0-10-4-13-6v-1c-1 0-1 0-2-1l-5-5h0l-2-3c0-1-1-2 0-3l2-1z" class="n"></path><path d="M448 706h1l2 2-2 1c-1 0-1 0-2-1v-1l1-1z" class="q"></path><path d="M449 694s1 1 2 0c0 2 1 5 3 7 1 1 3 2 3 4l1 1 2-1c0 1 1 1 1 2-1 1-2 3-4 3-2-1-3-3-4-4l-6-3-6-2v-4l1-1c0 1 1 1 2 1l2 1c1-1 1-2 2-3l1-1z" class="r"></path><path d="M447 703c2 1 3 1 5 1l2 1 1 1c0 1 1 1 1 2h1v-3l1 1 2-1c0 1 1 1 1 2-1 1-2 3-4 3-2-1-3-3-4-4l-6-3z" class="B"></path><path d="M434 700l5 5c1 1 1 1 2 1v1c3 2 9 6 13 6v1h3c2 1 4 0 6 0h0c-1 1-2 2-4 2 2 2 4 2 6 1h1l-1 3-2 1c0 1 0 2 1 2 0 1 1 2 1 3h-1l-1 1-2-1-3 3-2-2c0-1 0-2-1-3s-4-2-5-3c-8-4-14-9-18-17l2 1c0-1-1-2-1-3l1-2z" class="S"></path><path d="M459 719l1 1c0 1 1 1 2 1 0 2 0 3-1 5l-3 3-2-2h1c2-1 2-1 4-3l-1-1h0-2-1l-1-2c1 0 2-1 3-2z" class="h"></path><path d="M434 700l5 5c1 1 1 1 2 1v1c3 4 8 6 12 9l-1 3-6-4c-5-2-9-7-12-10 0-1-1-2-1-3l1-2z" class="a"></path><path d="M441 707c3 2 9 6 13 6v1h3c2 1 4 0 6 0h0c-1 1-2 2-4 2 2 2 4 2 6 1h1l-1 3-2 1c0 1 0 2 1 2 0 1 1 2 1 3h-1l-1 1-2-1c1-2 1-3 1-5-1 0-2 0-2-1l-1-1c-1 1-2 2-3 2-1-1-3-1-4-2l1-3c-4-3-9-5-12-9z" class="J"></path><path d="M453 716l6 3c-1 1-2 2-3 2-1-1-3-1-4-2l1-3z" class="V"></path><path d="M429 581l4 10c3 13 8 25 13 37 3 10 7 21 13 30-5-3-10-5-13-8v-1l2 1h1c-3-2-5-5-6-8-1-1-1-2-2-4v-2l-1-1c0-2 0-5 1-7h1v-1c-1-2-2-4-3-7-2-6-4-12-6-19 0 3 0 6-1 9v2c-1 2-1 4-2 6l-2 5c-2 6-7 11-11 15s-8 8-11 13c-2 2-3 4-4 6-1-1-1-4-2-6h0v-1c-1-1-1-2-2-2-1-1-1-2-3-2v1l-1-2c1-3 4-5 6-7 1-1 2-2 3-2v-1h-2c1-2 3-3 4-4h1c-2 0-3 0-4 1h-1l-3-1c0-1 0-2 1-3h4c0-1 1-1 1-2h1v-1h0c2-1 3-2 5-3 0-1 0-1 1-1 0-1 0-1 1-2v-1l3-2c5-6 9-14 9-22l1-1 1-1c0-1 0-2 1-3h0c0-3 1-6 2-8z" class="U"></path><path d="M409 628c5-4 11-9 13-14l1-1 1-1v-1h0v3 1l1-1v-1l2-2 1 1-1 1c-2 1-1 1-2 3 0 1-1 2-1 3h-1c-1 0-2 1-3 2v1c-1 1-1 0-1 1-1 2-5 5-7 6-1 0-2 0-3-1z" class="C"></path><path d="M400 651h1c0 1 0 1 1 2v1h0v-2c2-2 4-5 5-7h1c1-3 5-5 7-7 3-3 5-6 8-9h0c2-3 3-5 5-6-2 6-7 11-11 15s-8 8-11 13c-2 2-3 4-4 6-1-1-1-4-2-6h0z" class="Q"></path><path d="M409 628c1 1 2 1 3 1 2-1 6-4 7-6 0-1 0 0 1-1v-1c1-1 2-2 3-2h1c0-1 1-2 1-3 1-2 0-2 2-3 0 1 0 2-1 3-1 3-3 6-3 9-1 2-5 7-7 8l-1 1c-6 4-9 13-15 16-1-1-1-2-2-2-1-1-1-2-3-2v1l-1-2c1-3 4-5 6-7 1-1 2-2 3-2v-1h-2c1-2 3-3 4-4h1l3-3z" class="D"></path><path d="M433 591c0-4-2-6-3-10l2 5c1 0 1 1 1 2h1l10 26 10 24c1 4 2 9 5 12l28 70 4 10c0 1 1 3 1 4v1 4l-1 1-1-1v2c-3-2-5-3-8-4v-1c0-1 0-1 1-2l1-1c1-1 1-2 0-4 0-3-1-7-3-10-1-1-2-1-3-1-2 0-3 0-4 2-1 1 0 3 0 4l1-1c1 2 2 2 1 3l-1 2v1l-5 3c-1-1-1-3-3-3l-1-1c-2 0-2-1-2-2h1 1l1 1c1 0 3 1 4 0 2 0 2 0 3-1-1-1-1-2-2-2-2 0-3-1-4-3 0-2 2-3 4-5h0v1h1c1-1 2-2 2-3 3-4 1-11 0-16l-2-5v1h-1-1 0c0-1-1-1-1-2h-2v-1h-1c1-1 1-2 1-3 0-2 0-4-1-5-1 0-2 1-3 0h1-1c-1 1-1 1-3 2 1-3 1-6 0-9v-3c0-2 0-2-1-3h-1v1h-1v1c0 2-1 3-2 5-2 1-4 2-6 2l1-2s1-1 1-2c0-2 0-4-1-6-3-3-7-5-10-5s-5 1-7 3h-1c1-4 0-7 1-10 0-9 2-18 4-26 0 3 1 7 2 10l4 7 2 2c3 3 8 5 13 8-6-9-10-20-13-30-5-12-10-24-13-37z" class="F"></path><path d="M440 641l4 7h-2c-1-1-2-1-2-2-1-2-1-3 0-5z" class="I"></path><path d="M482 737h2 0l3-3c1 2 2 3 3 5v2c-3-2-5-3-8-4z" class="B"></path><path d="M468 688h3c1 1 2 3 2 5v1h-1-1 0c0-1-1-1-1-2h-2v-1h-1c1-1 1-2 1-3z" class="U"></path><path d="M468 688h3c-1 2-2 2-3 3h-1c1-1 1-2 1-3z" class="a"></path><path d="M436 660c0-1-1-2 0-2 2-1 6-2 8-1 3 0 5 2 7 3h0l1 3h-4l-3-3c0-1-1-2-2-3l-4 1c-2 0-3 1-3 2z" class="E"></path><path d="M436 660c0 2 0 4-1 5h-1c0-3 1-6 1-8 1-2 0-4 1-5 3 1 5 3 8 3 3 1 6 2 10 3h-1-3 0c1 1 1 1 1 2-2-1-4-3-7-3-2-1-6 0-8 1-1 0 0 1 0 2z" class="U"></path><path d="M451 660c0-1 0-1-1-2h0 3 1c3 2 6 4 7 8l6 17c-1 0-2 1-3 0h1-1c-1 1-1 1-3 2 1-3 1-6 0-9v-3c0-2 0-2-1-3h-1-1c-3-1-4-5-6-7l-1-3h0z" class="a"></path><path d="M451 660c4 3 6 6 7 10-3-1-4-5-6-7l-1-3z" class="B"></path><path d="M229 236c1-3 1-5 3-8 3-7 9-12 16-15l1 1c2-1 4-2 5-1h1c2 1 5 1 8 1-3 0-6 0-8 1 3 1 6 1 9 1 2 0 3 0 5 1h2c2 0 4 1 6 1 0 1 2 2 3 1 3 1 6 3 9 4 0-1 0-1 1-2 2 2 7 6 9 6h2l1 2c2 1 4 3 6 3v-1l2-1v2l1 1h4v2l-1 1c-4 13-5 29-2 43l2 9 2 6 1 3-1-2-3-1c0 1-1 3-1 4 1 3 2 3 2 6v1c-1 1 0 4-1 6l-1 1-1 1h0l-3 1c1 0 2 0 2 1 1 1 1 2 1 4-2 0-4 1-6 2l-2 2c-2-1-2 0-4 0v1c3 1 5 3 6 6 1 2 1 2 0 4-2 3-5 4-8 4v1c-11-2-20-6-27-14h0l-7-9c-1-1-2-3-3-4-2-1-2-3-4-4v3s0 1 1 2v2h0c-1-1-2-2-2-3h-1c1 1 1 2 2 4h-1l-21-40c0-2-1-4-1-6-1-3-3-6-3-9h1v-2l-2-8c0-3-1-6-1-9s0-4 1-6z" class="a"></path><path d="M284 222c1 1 3 2 4 3l1 3c-1-1-3-2-4-3s-1-2-1-3z" class="U"></path><path d="M286 264v2c-1 1-1 1-3 1 1 2 2 2 3 3l-2 1-3 1h-3v-1c0-1 0-3 1-4 2-1 4-2 7-3z" class="C"></path><path d="M262 221h5c-1-1-1-1-2-1h-1l-1-1c-1 0-3 0-4 1h-3l-1-1h3c2-1 7-1 9 0h1l4 1c2 1 4 3 6 4 0 2 2 2 2 4-2-1-4-2-6-4h0c-4-3-8-3-12-3z" class="U"></path><path d="M283 262c5-2 7-5 10-8-2 4-4 7-7 10-3 1-5 2-7 3h-1c-2-2-5-1-8-3 3 0 6 0 10-1 1 0 2-1 3-1z" class="k"></path><path d="M288 225c4 3 7 6 10 10h0c0 3 1 4 2 7h-1v2h0l-1-1v-1c0-1 0-1-1-1h0c-2-5-6-8-8-13l-1-3z" class="Q"></path><path d="M291 242c1 1 1 2 1 3l3 2c0 1-1 3-1 4v1c-1 1 0 1-1 2-3 3-5 6-10 8h-6c2-1 4-2 5-2h1l1-1c-1-1-2-1-4-1 1 0 3-1 4-1 3-1 5-4 6-6v-1c1-2 1-5 1-8z" class="C"></path><path d="M287 250c2-4 2-7 2-12l2 4h0c0 3 0 6-1 8v1c-1 2-3 5-6 6-1 0-3 1-4 1h-2c0-2 2-3 4-4-1-1-1-2-1-3v-1c1-1 2-1 3-1h2l1 1z" class="I"></path><path d="M281 250c1-1 2-1 3-1h2l1 1c-2 1-3 3-5 4-1-1-1-2-1-3v-1z" class="C"></path><path d="M229 236c1-3 1-5 3-8 3-7 9-12 16-15l1 1c2-1 4-2 5-1h1c2 1 5 1 8 1-3 0-6 0-8 1-9 1-14 3-19 10-3 3-5 7-6 11h-1z" class="G"></path><path d="M264 254c2 1 4 3 7 4h0 1 1c1 1 3 1 4 0h1 2c2 0 3 0 4 1l-1 1h-1c-1 0-3 1-5 2h6c-1 0-2 1-3 1-3 0-5 0-8-1-1 0-3-1-4-1l-3-2c-1-1-2-3-3-4v-1l2 1v-1z" class="n"></path><path d="M262 221c4 0 8 0 12 3h0v2h1c-1 2-2 4-4 6v-1l-2-2h-1l-2-2c-2-2-9-5-13-4h-2c4-1 7-1 11-2zm2 33c-1-1-2-2-3-2l-1-1h0c2-1 4-2 5-4 1-1 1-2 1-4h0l1-1c0 1 0 2 1 2h0c1 1 1 1 1 2 2 2 4 4 5 4 1-1 0-1 0-2h0c2 1 3 1 6 2h0 1v1c0 1 0 2 1 3-2 1-4 2-4 4h-1c-1 1-3 1-4 0h-1-1 0c-3-1-5-3-7-4z" class="r"></path><path d="M269 246c2 2 4 4 5 4 1-1 0-1 0-2h0c2 1 3 1 6 2h0 1v1c0 1 0 2 1 3-2 1-4 2-4 4h-1c-1 1-3 1-4 0h-1c0-1 0 0 1-1h0v-4h0c0-1-1-2-2-2l-2-2v-3z" class="F"></path><path d="M273 258c2-1 2-2 3-4 1-1 4-2 5-3 0 1 0 2 1 3-2 1-4 2-4 4h-1c-1 1-3 1-4 0zm-38-23c1-5 3-6 7-9 1-1 1-1 3-2l1-1h1l1-1c1-1 2-1 3-2l1 1c-1 0-2 1-3 2h2 2c4-1 11 2 13 4l2 2h1l-2 1-1 2c-1 0-1 0-2-1h0c-1 0-2 0-2-1-1 0-2-1-3-1h-1c-3-1-6-1-9-1-5 2-11 4-13 9l-1 2c-1 3-1 8-2 10-1-6 0-10 2-14z" class="D"></path><path d="M264 231c0-1 0-2 1-3l2 2-1 2c-1 0-1 0-2-1z" class="U"></path><path d="M235 235c2-3 6-6 9-6 4-2 8-3 12-2 3 0 6 2 8 4-1 0-2 0-2-1-1 0-2-1-3-1h-1c-3-1-6-1-9-1-5 2-11 4-13 9l-1 2c-1 3-1 8-2 10-1-6 0-10 2-14z" class="g"></path><path d="M264 242v-1c2 0 2 0 3 1l-1 1h0c0 2 0 3-1 4-1 2-3 3-5 4h0l1 1c1 0 2 1 3 2v1l-2-1v1c1 1 2 3 3 4l3 2c-1 0-1 1-1 0-2 0-3 0-4-1-2-1-4-3-6-4s-5 0-7-1c-1 0-2 0-3 1-1-1 0-1-1 0h-1l-2-2v-2c4-3 7-6 13-7 1 1 4 1 6 1v-1h1c1-1 1-1 1-3z" class="R"></path><path d="M243 252c4-3 7-6 13-7l-1 1c-1 5-7 4-9 9l1-1c2-1 5-1 7-1 3 2 7 4 9 7-2-1-4-3-6-4s-5 0-7-1c-1 0-2 0-3 1-1-1 0-1-1 0h-1l-2-2v-2z" class="B"></path><path d="M274 224c2 2 4 3 6 4l2 2h2c1 0 1 0 2 1 1 2 2 4 3 7h0c0 5 0 8-2 12l-1-1h-2c-1 0-2 0-3 1h-1 0c-3-1-4-1-6-2-1-2-1-4-3-6h0l1-2h1l1 1 2 1h2 0c1-1 2-1 2-2h-1c1-3 1-5 1-7l-1-1c0-1-1-1-1-3h0l-3-3h-1v-2z" class="E"></path><path d="M280 233v-2c2 1 3 3 4 4l1 1-3 8v1h0l-1 1h-1c-1-1-1-3-2-4 1-1 2-1 2-2h-1c1-3 1-5 1-7z" class="m"></path><path d="M282 230h2c1 0 1 0 2 1 1 2 2 4 3 7h0c0 5 0 8-2 12l-1-1h-2c-1 0-2 0-3 1h-1 0c-3-1-4-1-6-2-1-2-1-4-3-6h0l1-2h1l1 1c2 3 3 6 6 7 3-1 4-2 6-5 0-1 0-2 1-3 0-5-2-6-5-10z" class="Q"></path><path d="M280 248c3-1 4-2 6-5 0 1 1 2 1 3l-1 2h-2c-1 1-2 0-3 0h-1z" class="D"></path><path d="M298 235c1 1 1 2 2 2v4c1 3 2 6 2 9-2 3-1 9-1 13v4c-1 2-1 5-1 7-1 2 0 4-1 6 0 2 1 4 2 6-1 0-2 0-3-1v-1c-2 0-3 0-5-1h-6c-2 1-5 3-7 5v2c-2 0-4 1-6 0 0-1-1-2-1-3l-1-1-1-2 1-2h1v1l1-2c3-1 6-2 8-3l2-3 1-2 1-2h-2l2-1 5-4c5-4 7-12 7-19-1-2-1-4-1-6h0c1 0 1 0 1 1v1l1 1h0v-2h1c-1-3-2-4-2-7h0z" class="T"></path><path d="M291 276c1-1 2-1 4-1v3c-1-1-3-2-4-2z" class="M"></path><path d="M286 271l5-3-3 7-3-2 1-2z" class="C"></path><path d="M285 273l3 2c-1 2-1 3-2 5h-1l-2 1c0-1 0-2-1-3l2-3 1-2z" class="D"></path><path d="M284 275c1 2 1 3 1 5l-2 1c0-1 0-2-1-3l2-3z" class="C"></path><path d="M294 267h2c1 0 2-1 3-2 0 1 0 3-1 5h1c1-1 1-1 1-3h1c-1 2-1 5-1 7-1 2 0 4-1 6 0 2 1 4 2 6-1 0-2 0-3-1v-2c0-1-1-3-1-4 0-2 0-7-1-8l-2 2c-2 1-2 2-4 1 0-2 3-5 4-7z" class="h"></path><path d="M298 235c1 1 1 2 2 2v4c1 3 2 6 2 9-2 3-1 9-1 13v4h-1c0 2 0 2-1 3h-1c1-2 1-4 1-5-1 1-2 2-3 2h-2l3-5c1-4 3-11 1-15-1-2-1-4-1-6h0c1 0 1 0 1 1v1l1 1h0v-2h1c-1-3-2-4-2-7h0z" class="D"></path><path d="M286 280c1-1 1-1 2-1 1-1 1-1 1-3h2c1 0 3 1 4 2 1 2 1 3 2 4 0 1 0 1 1 2-2 0-3 0-5-1h-6c-2 1-5 3-7 5v2c-2 0-4 1-6 0 0-1-1-2-1-3l-1-1-1-2 1-2h1v1l1-2 8-3c1 1 1 2 1 3l2-1h1z" class="I"></path><g class="o"><path d="M282 278c1 1 1 2 1 3-3 2-7 3-10 6l-1-1-1-2 1-2h1v1l1-2c3-1 6-2 8-3z"></path><path d="M247 256c1-1 2-1 3-1 2 1 5 0 7 1s4 3 6 4c1 1 2 1 4 1 0 1 0 0 1 0s3 1 4 1c3 1 5 1 8 1-4 1-7 1-10 1 3 2 6 1 8 3h1c-1 1-1 3-1 4v1h3l3-1h2l-1 2-1 2-2 3c-2 1-5 2-8 3l-1 2v-1h-1l-1 1c0-1 0-2 1-3l-2-1-1-1c0 1-1 2-1 3-3 2-4 2-7 1-2 0-3-1-3-3-1-2-1-3-2-5 0-3-1-10-3-12v-3c-1 0-2 0-2-1l-4-2z"></path></g><path d="M256 268l2-2h1l2 2v-1h1v1c1 1 2 1 3 2l1 1-2 2-2-2v1c0 1 1 2 0 3h-1 0c-1-1-2-2-3-2-1-2-1-3-2-5z" class="h"></path><path d="M263 263c2-1 5 0 7 1h0c3 2 6 1 8 3h1c-1 1-1 3-1 4h-4v-1c-2 0-3 0-5-1-2 0-3 0-5-2h-2-1v-3c1-1 1-1 2-1z" class="B"></path><path d="M278 267h1c-1 1-1 3-1 4h-4v-1c-2 0-3 0-5-1l9-2z" class="L"></path><path d="M261 264c1-1 1-1 2-1l1 1c2 1 5 1 7 2v1h-7-2-1v-3z" class="X"></path><path d="M253 259l2 1c0 2 0 4 1 5v3c1 2 1 3 2 5l3 5h1v-1c1-2 4-2 5-3 1 0 2 0 3-1h1 1c-1 2-2 3-3 5 0 1-1 2-1 3-3 2-4 2-7 1-2 0-3-1-3-3-1-2-1-3-2-5 0-3-1-10-3-12v-3z" class="W"></path><path d="M267 274c-1 2-1 3-3 5-1 0-2 0-3-1h1v-1c1-2 4-2 5-3z" class="l"></path><path d="M247 256c1-1 2-1 3-1 2 1 5 0 7 1s4 3 6 4c1 1 2 1 4 1 0 1 0 0 1 0s3 1 4 1c3 1 5 1 8 1-4 1-7 1-10 1h0c-2-1-5-2-7-1-1 0-1 0-2 1v3 1l-2-2h-1l-2 2v-3c-1-1-1-3-1-5l-2-1c-1 0-2 0-2-1l-4-2z" class="o"></path><path d="M255 260v-2h1c1 0 2 1 2 1 1 1 1 1 2 1l2 1v1h-1c-1 1-1 2 0 2v3 1l-2-2h-1l-2 2v-3c-1-1-1-3-1-5z" class="D"></path><path d="M255 260v-2h1c1 0 2 1 2 1 1 1 1 1 2 1l-2 2c-1 1-2 2-2 3-1-1-1-3-1-5z" class="H"></path><path d="M274 271h4v1h3l3-1h2l-1 2-1 2-2 3c-2 1-5 2-8 3l-1 2v-1h-1l-1 1c0-1 0-2 1-3l-2-1-1-1c1-2 2-3 3-5l1-1 1-1z" class="L"></path><path d="M274 271h4v1l-3 2h-1l-1-2 1-1z" class="a"></path><path d="M281 272l3-1h2l-1 2-1 2-2 3c-2 1-5 2-8 3l-1 2v-1c2-4 4-7 8-10z" class="G"></path><defs><linearGradient id="CH" x1="229.878" y1="282.422" x2="260.116" y2="285.63" xlink:href="#B"><stop offset="0" stop-color="#08080b"></stop><stop offset="1" stop-color="#343330"></stop></linearGradient></defs><path fill="url(#CH)" d="M230 261h1v-2l-2-8c0-3-1-6-1-9s0-4 1-6h1c0 1 0 2-1 4 0 3 0 6 1 9 0 2 1 5 2 7v-7 2c3 2 4 4 7 4h1l3-3v2l2 2h1c1-1 0-1 1 0l4 2c0 1 1 1 2 1v3c-2 0-4 0-6 1-1 1-2 3-2 4-1 3 0 6 0 9 1 0 1 1 1 2 1 2 3 3 3 5l1 4 3 6-1 1-1-1-1-1c1 4 2 7 3 11 1 1 2 4 3 5v3s0 1 1 2v2h0c-1-1-2-2-2-3h-1c1 1 1 2 2 4h-1l-21-40c0-2-1-4-1-6-1-3-3-6-3-9z"></path><path d="M245 279l1-1c1 2 3 3 3 5l1 4 3 6-1 1-1-1-1-1h0l-5-13z" class="L"></path><path d="M245 279c-2-2-6-16-7-19 0-2 0-3 1-4 1 2 2 5 2 8 1 3 2 9 4 12 1 0 1 1 1 2l-1 1z" class="i"></path><path d="M240 255l3-3v2l2 2h1c1-1 0-1 1 0l4 2c0 1 1 1 2 1v3c-2 0-4 0-6 1-1 1-2 3-2 4-1 3 0 6 0 9-2-3-3-9-4-12 0-3-1-6-2-8l1-1z" class="m"></path><path d="M240 255l3-3v2c-1 1-1 1-1 3v2c1 2 0 3-1 5 0-3-1-6-2-8l1-1z" class="F"></path><defs><linearGradient id="CI" x1="226.004" y1="277.741" x2="253.189" y2="285.042" xlink:href="#B"><stop offset="0" stop-color="#c2bebe"></stop><stop offset="1" stop-color="#e0e2e3"></stop></linearGradient></defs><path fill="url(#CI)" d="M230 261h1v-2l-2-8c0-3-1-6-1-9s0-4 1-6h1c0 1 0 2-1 4 0 3 0 6 1 9 0 2 1 5 2 7v-7 2c0 2 0 4 1 6 3 13 9 25 14 37 2 6 4 13 7 18 1 1 1 2 2 4h-1l-21-40c0-2-1-4-1-6-1-3-3-6-3-9z"></path><path d="M245 267c0-1 1-3 2-4 2-1 4-1 6-1 2 2 3 9 3 12 1 2 1 3 2 5 0 2 1 3 3 3 3 1 4 1 7-1 0-1 1-2 1-3l1 1 2 1c-1 1-1 2-1 3l1-1-1 2 1 2 1 1c0 1 1 2 1 3 2 1 4 0 6 0l-1 1c1-1 2-1 3-1v3c-1 1-1 2-1 3l-4 3c-3 2-6 4-10 6h1c3 2 7 4 11 5l-1 1c-2 1-4 1-7 1l-1-1c-1 0-1-1-2-2-2-2-5-3-8-4-1-1-2-3-3-4-2-2-3-6-4-8l-3-6-1-4c0-2-2-3-3-5 0-1 0-2-1-2 0-3-1-6 0-9z" class="r"></path><path d="M256 274c1 2 1 3 2 5-1 2-1 3-2 5-1-2-2-5-3-8l2-2h0 1z" class="H"></path><path d="M245 267h4c-2 6 0 12 3 17-1 1-1 2-2 3l-1-4c0-2-2-3-3-5 0-1 0-2-1-2 0-3-1-6 0-9z" class="D"></path><path d="M245 267c0-1 1-3 2-4 2-1 4-1 6-1 2 2 3 9 3 12h-1 0l-2 2c-1-3-1-7-1-10 0-1 0-1-2-1l-1 2h-4z" class="V"></path><path d="M250 287c1-1 1-2 2-3 2 5 5 11 9 15 1 1 5 4 6 6h1c3 2 7 4 11 5l-1 1c-2 1-4 1-7 1l-1-1c-1 0-1-1-2-2-2-2-5-3-8-4-1-1-2-3-3-4-2-2-3-6-4-8l-3-6z" class="n"></path><path d="M269 278l1 1 2 1c-1 1-1 2-1 3l1-1-1 2 1 2 1 1c0 1 1 2 1 3 2 1 4 0 6 0l-1 1c-2 2-5 5-9 5-2-1-3-5-4-7l-1-2h-2c-3-1-5-1-7-3 1-2 1-3 2-5 0 2 1 3 3 3 3 1 4 1 7-1 0-1 1-2 1-3z" class="D"></path><path d="M269 278l1 1 2 1c-1 1-1 2-1 3l1-1-1 2c-1 2-2 3-4 4l-3-3 1-1h2c1-1 1-2 1-3s1-2 1-3z" class="n"></path><path d="M270 279l2 1c-1 1-1 2-1 3h0l-1 1c-1-2-1-3 0-5z" class="V"></path><path d="M275 226l3 3h0c0 2 1 2 1 3l1 1c0 2 0 4-1 7h1c0 1-1 1-2 2h0-2l-2-1-1-1h-1l-1 2h0c2 2 2 4 3 6h0c0 1 1 1 0 2-1 0-3-2-5-4 0-1 0-1-1-2h0c-1 0-1-1-1-2-1-1-1-1-3-1v1c0 2 0 2-1 3h-1v1c-2 0-5 0-6-1-6 1-9 4-13 7l-3 3h-1c-3 0-4-2-7-4v-2h1c1-2 1-7 2-10l1-2c2-5 8-7 13-9 3 0 6 0 9 1h1c1 0 2 1 3 1 0 1 1 1 2 1h0c1 1 1 1 2 1l1-2 2-1 2 2v1c2-2 3-4 4-6z" class="d"></path><path d="M269 229l2 2c-3 1-3 1-5 1l1-2 2-1z" class="C"></path><path d="M279 232l1 1c0 2 0 4-1 7h1c0 1-1 1-2 2h0-2c0-1 0-1 1-2h1 0c0-1 0-2-1-2 0-1-1-1-1-1-1 1-1 1-1 2-1-1-2-1-2-2 1-1 3-3 3-4l1-1h2z" class="J"></path><path d="M249 228c4 3 10 1 13 5-1 1-1 1-3 1s-3 0-5 1h0-4c-4 0-9-1-12 2l-2 2h-1l1-2c2-5 8-7 13-9z" class="D"></path><path d="M250 235c2-2 4-2 5-3h1c2 0 2 0 3 2-2 0-3 0-5 1h0-4z" class="V"></path><path d="M236 239l2-2c3-3 8-2 12-2h4l4 1c-1 1-1 2-2 3 1 0 1 1 2 1s2-1 3 0l1-1 1 1-1 1 1 1h1c0 2 0 2-1 3h-1v1c-2 0-5 0-6-1-6 1-9 4-13 7l-3 3h-1c-3 0-4-2-7-4v-2h1c1-2 1-7 2-10h1z" class="m"></path><path d="M235 239h1c-1 4-1 8 0 12l2 2v1l1 1c-3 0-4-2-7-4v-2h1c1-2 1-7 2-10z" class="R"></path><path d="M256 239c1 0 1 1 2 1s2-1 3 0l1-1 1 1-1 1 1 1h1c0 2 0 2-1 3h-1v1c-2 0-5 0-6-1-6 1-9 4-13 7l-3 3h-1l-1-1v-1c2-3 3-5 5-8 3-3 9-4 13-6z" class="Q"></path><path d="M257 241l1-1 4 2-1 2c-1-1-3-1-4-2v-1z" class="U"></path><defs><linearGradient id="CJ" x1="303.743" y1="249.037" x2="310.2" y2="282.169" xlink:href="#B"><stop offset="0" stop-color="#afafaf"></stop><stop offset="1" stop-color="#d8d6d6"></stop></linearGradient></defs><path fill="url(#CJ)" d="M280 219c3 1 6 3 9 4 0-1 0-1 1-2 2 2 7 6 9 6h2l1 2c2 1 4 3 6 3v-1l2-1v2l1 1h4v2l-1 1c-4 13-5 29-2 43l2 9 2 6 1 3-1-2-3-1c-2 0-5-1-7-2-3-2-5-8-7-12 1-2 0-4 1-6 0-2 0-5 1-7v-4c0-4-1-10 1-13 0-3-1-6-2-9v-4c-1 0-1-1-2-2-3-4-6-7-10-10-1-1-3-2-4-3l-4-3z"></path><path d="M290 221c2 2 7 6 9 6v2h-1-1l-8-6c0-1 0-1 1-2z" class="Q"></path><path d="M299 227h2l1 2c2 1 4 3 6 3v-1l2-1v2l1 1h4v2l-1 1h-2-4c-1 0-2 0-2 1-1-1-1 0-1-1v-1c-3-2-5-4-6-6v-2z" class="G"></path><path d="M310 230v2l1 1h4v2l-1 1h-2-4c-2-1-2-1-3-2h2 1v-2-1l2-1z" class="L"></path><defs><linearGradient id="CK" x1="300.007" y1="249.613" x2="324.493" y2="278.887" xlink:href="#B"><stop offset="0" stop-color="#0c0c0d"></stop><stop offset="1" stop-color="#444341"></stop></linearGradient></defs><path fill="url(#CK)" d="M312 236h2c-4 13-5 29-2 43l2 9 2 6 1 3-1-2c-1-1-2-3-3-4-7-13-4-30-5-45l-2-9c0-1 1-1 2-1h4z"></path><path d="M308 236h4c-1 1-1 1-2 3h1c0 2-1 4-1 6-1 1-1 1-2 1l-2-9c0-1 1-1 2-1z" class="r"></path><path d="M302 240c0-1 0-3-1-4l1-1c1 3 2 6 2 8 2 9 2 18 2 28 0 5 0 13 2 18 2 1 3 1 4 2h1c1 1 2 3 3 4l-3-1c-2 0-5-1-7-2-3-2-5-8-7-12 1-2 0-4 1-6 0-2 0-5 1-7v-4c0-4-1-10 1-13 0-3-1-6-2-9v-4l2 3z" class="G"></path><path d="M300 237l2 3c4 11 2 23 1 34v-6c0-1-1-3-1-4v-9-5c0-3-1-6-2-9v-4z" class="V"></path><path d="M302 250v5 9c0 1 1 3 1 4v6c0 5 0 11 4 14l1 1h0c2 1 3 1 4 2h1c1 1 2 3 3 4l-3-1c-2 0-5-1-7-2-3-2-5-8-7-12 1-2 0-4 1-6 0-2 0-5 1-7v-4c0-4-1-10 1-13z" class="C"></path><path d="M301 286c-1-2-2-4-2-6 2 4 4 10 7 12 2 1 5 2 7 2 0 1-1 3-1 4 1 3 2 3 2 6v1c-1 1 0 4-1 6l-1 1-1 1h0l-3 1c1 0 2 0 2 1 1 1 1 2 1 4-2 0-4 1-6 2l-2 2c-2-1-2 0-4 0v1c3 1 5 3 6 6 1 2 1 2 0 4-2 3-5 4-8 4v1c-11-2-20-6-27-14h0l-7-9c-1-1-2-3-3-4-2-1-2-3-4-4-1-1-2-4-3-5-1-4-2-7-3-11l1 1 1 1 1-1c1 2 2 6 4 8 1 1 2 3 3 4 3 1 6 2 8 4 1 1 1 2 2 2l1 1c3 0 5 0 7-1l1-1c-4-1-8-3-11-5h-1c4-2 7-4 10-6l4-3c0-1 0-2 1-3v-3c-1 0-2 0-3 1l1-1v-2c2-2 5-4 7-5h6c2 1 3 1 5 1v1c1 1 2 1 3 1z" class="P"></path><path d="M260 305c3 1 6 2 8 4 1 1 1 2 2 2l1 1c-5-1-8-3-11-7z" class="U"></path><path d="M250 292l1 1 1 1c2 6 4 11 8 16v2c-2-1-2-3-4-4-1-1-2-4-3-5-1-4-2-7-3-11z" class="N"></path><path d="M266 315l1-2c3 0 5 2 8 1 2 0 5 2 7 1 1-1 2-2 4-2v1l-3 4c-6 1-12 0-17-3z" class="B"></path><path d="M260 310c2 2 4 4 6 5 5 3 11 4 17 3 3 0 5-2 7-4v2h0c1-1 1-1 1-2l1 1v2l-1 2h1l2-1v1c-2 1-4 1-6 2l-6 1c4 0 8 0 11 3h-8l-16-3h0l2 2-1 1h0l-7-9c-1-1-2-3-3-4v-2z" class="C"></path><path d="M260 312h1c1 1 2 4 4 5 3 4 11 5 16 5h1c4 0 8 0 11 3h-8l-16-3h0l2 2-1 1h0l-7-9c-1-1-2-3-3-4z" class="e"></path><path d="M301 308l1-1c-1-1 0-1-1-1h0l1-1h1c1 2 2 3 1 5 1 1 0 1 1 1l1 1-2 1c1 1 2 1 4 1h0c1 0 2 0 2 1 1 1 1 2 1 4-2 0-4 1-6 2l-2 2c-2-1-2 0-4 0v1h-2l-1 1 3 1h-1c-1 0-3 0-5-1-3-3-7-3-11-3l6-1c2-1 4-1 6-2v-1s1-1 1-2l2-3v-4h1c1 2 2 3 2 5h1c1-2 1-3 1-5l-1-1z" class="R"></path><path d="M301 308l1-1c-1-1 0-1-1-1h0l1-1h1c1 2 2 3 1 5 1 1 0 1 1 1l1 1-2 1c1 1 2 1 4 1h0c1 0 2 0 2 1-1 0-1 0-2 1-2 1-6-2-8 0l-1 1h0l-5 5c-2 0-4 0-6-1 2-1 4-1 6-2v-1s1-1 1-2l2-3v-4h1c1 2 2 3 2 5h1c1-2 1-3 1-5l-1-1z" class="S"></path><path d="M301 308l1-1c-1-1 0-1-1-1h0l1-1h1c1 2 2 3 1 5 1 1 0 1 1 1l1 1-2 1c-1 1-1 1-2 1v-1-4h0l-1-1z" class="f"></path><path d="M290 301c4 0 6 0 9 4 1 1 2 2 2 3l1 1c0 2 0 3-1 5h-1c0-2-1-3-2-5h-1v4l-2 3c0 1-1 2-1 2l-2 1h-1l1-2v-2l-1-1c0 1 0 1-1 2h0v-2l-3-3c-5 2-11 3-16 1 3 0 5 0 7-1l1-1c1 0 2 0 4-1 0-1 1-3 1-4l1-2c2 0 3-1 5-2z" class="N"></path><path d="M285 303c2 0 3-1 5-2 0 1 0 2-1 3-2 1-3 1-5 1l1-2z" class="D"></path><path d="M291 307l-1-1 2-2c2 1 4 3 5 5v4l-2 3c0 1-1 2-1 2l-2 1h-1l1-2v-2l-1-1c0-1-1-3-2-4h1s1 1 2 1c0-2-1-3-1-4z" class="c"></path><path d="M291 307l-1-1 2-2c2 1 4 3 5 5v4l-2 3c0-2 1-5 0-6 0-2-2-3-3-3h-1zm-1-13c3 0 4 0 6 2h1c2 1 3 3 4 5h0c1 2 2 3 2 4h-1l-1 1h0c1 0 0 0 1 1l-1 1c0-1-1-2-2-3-3-4-5-4-9-4-2 1-3 2-5 2l-1 2c0 1-1 3-1 4-2 1-3 1-4 1-4-1-8-3-11-5h-1c4-2 7-4 10-6 2 1 3 0 5 0l1-1c1-1 2-2 3-2l4-2z" class="g"></path><path d="M282 299l1-1h1c2 1 5 1 7 1v1h-1c-1 0-2 1-3 1-2 0-6 1-8 2v-1l2-2 1-1z" class="R"></path><path d="M290 294c3 0 4 0 6 2h1c2 1 3 3 4 5h0l-1 2h-1l-1-1c-1 0-1 0-2-1 0-1 1-3 1-4 0 0-1 0-1-1-2 1-3 1-5 1l-1-3z" class="Z"></path><path d="M277 299c2 1 3 0 5 0l-1 1-2 2v1c2-1 6-2 8-2l-3 2c-3 0-5 1-8 1s-5 0-8 1h0-1c4-2 7-4 10-6z" class="i"></path><path d="M284 303h1l-1 2c0 1-1 3-1 4-2 1-3 1-4 1-4-1-8-3-11-5h0c3-1 5-1 8-1s5-1 8-1z" class="p"></path><path d="M271 324l-2-2h0l16 3h8c2 1 4 1 5 1h1l-3-1 1-1h2c3 1 5 3 6 6 1 2 1 2 0 4-2 3-5 4-8 4v1c-11-2-20-6-27-14l1-1z" class="m"></path><path d="M271 324l-2-2h0l16 3 5 1 3 2c-8 1-16-1-22-4z" class="L"></path><path d="M299 324c3 1 5 3 6 6 1 2 1 2 0 4-2 3-5 4-8 4 0-1 0 0 1-1 1-2 3-3 3-5 0-3-1-3-3-4s-4-1-5 0l-3-2-5-1h8c2 1 4 1 5 1h1l-3-1 1-1h2z" class="Q"></path><path d="M285 325h8c2 1 4 1 5 1h1 2c1 1 3 3 3 4s0 1-1 1h0 0c0-2-1-3-2-4-1 0-2 0-3 1-2-1-4-1-5 0l-3-2-5-1z" class="i"></path><path d="M301 286c-1-2-2-4-2-6 2 4 4 10 7 12 2 1 5 2 7 2 0 1-1 3-1 4 1 3 2 3 2 6v1c-1 1 0 4-1 6l-1 1-1 1h0l-3 1h0c-2 0-3 0-4-1l2-1-1-1c-1 0 0 0-1-1 1-2 0-3-1-5 0-1-1-2-2-4h0c-1-2-2-4-4-5h-1c-2-2-3-2-6-2l-4 2c-1 0-2 1-3 2l-1 1c-2 0-3 1-5 0l4-3c0-1 0-2 1-3v-3c-1 0-2 0-3 1l1-1v-2c2-2 5-4 7-5h6c2 1 3 1 5 1v1c1 1 2 1 3 1z" class="O"></path><path d="M287 283c1 0 3 0 4 1l-3 3h0l-2 3v1l-4-1c-1 0-2 0-3 1l1-1v-2c2-2 5-4 7-5z" class="V"></path><path d="M286 290c-1-1 0-1-1-1l-1-1v-1l1-1 3 1-2 3z" class="N"></path><path d="M300 295v-1c-1-1-2-1-2-2-1-1-1 0-1-1s-2-2-2-2v-1c3 1 6 6 7 8s2 3 3 5c0 1 1 1 1 2s0 1 1 2v3c-1 1-1 2-2 3-1 0 0 0-1-1 1-2 0-3-1-5 0-1-1-2-2-4h0c1 0 1-1 1-1-1-1-1-2-1-3s-1-1-1-2z" class="D"></path><path d="M281 296c3-3 5-5 10-5h5l4 4c0 1 1 1 1 2s0 2 1 3c0 0 0 1-1 1-1-2-2-4-4-5h-1c-2-2-3-2-6-2l-4 2c-1 0-2 1-3 2l-1 1c-2 0-3 1-5 0l4-3z" class="U"></path><path d="M301 286c-1-2-2-4-2-6 2 4 4 10 7 12 2 1 5 2 7 2 0 1-1 3-1 4 1 3 2 3 2 6v1c-1 1 0 4-1 6l-1 1-1 1h0l-3 1h0c-2 0-3 0-4-1l2-1c1-2 2-3 2-6-2-6-6-15-12-19h-1c-1-2-2-2-2-4 2 1 3 1 5 1v1c1 1 2 1 3 1z" class="C"></path><path d="M308 306v-1c1-1 1-2 1-4h1c1 1 1 3 2 4 0 3 0 4-1 6v1 1l-3 1h0c-2 0-3 0-4-1l2-1c1-2 2-3 2-6z" class="q"></path><path d="M301 286c-1-2-2-4-2-6 2 4 4 10 7 12 2 1 5 2 7 2 0 1-1 3-1 4 1 3 2 3 2 6l-1-1-12-17z" class="j"></path><defs><linearGradient id="CL" x1="653.804" y1="574.625" x2="629.945" y2="564.964" xlink:href="#B"><stop offset="0" stop-color="#b3b2b2"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#CL)" d="M796 226h1l-1 2 1 1h1l-1 1h0c-1 2-3 4-4 6 0 2-4 5-4 6l-1 2h0c-4 6-8 12-12 19-7 13-13 27-20 41-6 15-14 30-18 45l-11 27-39 92-1 3-1 1c0 1 0 2-1 4-3 3-4 6-6 9l-1 2v-1l-2 6c-1 5-4 10-5 16 0-1 0-1 1-1 0-1 0-2 2-3l1 1-21 51c1 0 2 0 2-1 1-2 2-5 3-7l6-14c1-2 1-4 2-5 1 0 2-1 2-1-1 2-6 15-6 17l-17 41c-1 5-4 9-5 14l-1 4c-3 5-5 11-7 16-1 5-3 10-5 14l-6 13-12 29c0 1-1 2-1 3-1 2-2 5-3 6l-4 9-7 16c0 2-1 5-2 7-1 5-4 10-6 16-3 5-4 12-8 17l1 1c-5 9-8 18-12 27-1 5-4 10-5 15-2 3-4 7-5 11 1 5-4 11-4 16l-27 67-8 21c-2 5-4 9-6 13h0-1v2s-1 1-1 2v1l-2 2v2l-1 1c0 1 0 1-1 2v-1c0-1 0-2-1-3 0 0 0-1-1-2v-1c-1-1-1-4 0-5l1 3 1 1h1v-2h-1c-1-1-3-5-3-7l-2-4-3-9-2-4v-4h0l1-1v-1c1-3 2-7 4-10h-2c0-1-1-1-2-2h-1v-2c0-1 2-2 3-3 1 0 2-1 4-2l1-1c-1-1-1-2 0-3 0-2 2-3 3-4 1-3 3-7 4-11l1-1 4-3c1-2 2-3 4-5 1-1 1-2 3-3 0-1 0-1 1-2 0-1 0-2 1-3l-3 1c-1-1-1-1-2-1l1-3v-5l2-1c-1-5-3-12-5-17 1-2 2-3 2-6l1 3 1-2h0l3-3 3-8 1-3-2 1v-3c0-3 1-6 1-10 0-2-1-4 1-5 2-2 2-4 2-7h0c2-2 3-7 4-9l10-27 6 3h0 1 1l-3 6 1 2 11-24 1-1v2l1 2h0c1-1 1-1 2-1 1 1 1 1 1 2v1l11-27c1-1 1-3 2-5l32-76v1l2-3 78-192c0-2 1-4 0-7 1-3 3-7 5-10l6-15 28-67c4-9 9-18 12-28l8-25 3-6c1-1 2-1 3-2h1v1c1 0 3 0 4-1v1h2v-1l-1-1c1-1 1-2 1-4 2-6 4-10 8-15l5-7c1-2 4-2 5-2l1 1 4-2 6-5 2-2z"></path><path d="M698 406c1 2 0 3 0 5-2 4-3 8-5 12 0-2 1-4 0-7 1-3 3-7 5-10z" class="E"></path><path d="M634 589l1 2-4 8h0 0l-3 5v2c-1 0-1 0-1 1l-1 1c0 1 0 0-1 1l3-8c2-4 4-9 6-12z" class="V"></path><path d="M704 391l1 1 1-1-8 20c0-2 1-3 0-5l6-15z" class="X"></path><path d="M674 490l13-31h0l-3 9 1 1c1-1 2-1 3-1l-1 3-1 1c0 1 0 2-1 4-3 3-4 6-6 9l-1 2v-1l-2 6-1-1-1-1z" class="o"></path><path d="M678 486h0c1-3 3-7 4-10 1-2 2-5 4-6v1 1c0 1 0 2-1 4-3 3-4 6-6 9l-1 2v-1z" class="f"></path><defs><linearGradient id="CM" x1="760.873" y1="261.977" x2="743.943" y2="288.892" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#252423"></stop></linearGradient></defs><path fill="url(#CM)" d="M755 265c1-1 2-1 3-2h1v1c1 0 3 0 4-1v1h2c-1 2-2 4-5 5 0-1-1-2-2-2-3 2-4 9-6 13l-5 13-2 4-1-1 8-25 3-6z"></path><path d="M517 851c1-2 2-3 4-5 1-1 1-2 3-3-3 7-7 14-11 20-2 3-4 7-6 10s-3 7-5 10h-2c0-1-1-1-2-2h-1v-2c0-1 2-2 3-3 1 0 2-1 4-2l1-1c-1-1-1-2 0-3 0-2 2-3 3-4 1-3 3-7 4-11l1-1 4-3z" class="P"></path><path d="M500 876l2 1c0 1 0 2-1 3h-1l-2 1h-1v-2c0-1 2-2 3-3z" class="X"></path><path d="M513 854l4-3c-2 4-4 7-5 11-1 2-2 3-3 5s-3 4-4 6c-1-1-1-2 0-3 0-2 2-3 3-4 1-3 3-7 4-11l1-1z" class="S"></path><path d="M744 296l1 1-39 94-1 1-1-1 28-67c4-9 9-18 12-28z" class="F"></path><path d="M674 490l1 1 1 1c-1 5-4 10-5 16-4 7-7 16-11 24l-16 39c-3 6-5 13-9 20l-1-2 40-99z" class="d"></path><path d="M796 226h1l-1 2 1 1h1l-1 1h0l-8 7c-4 4-7 7-10 11l-3 5c-1 2-2 2-3 3-2 1-3 5-4 7 0 0 0 1-1 2v-2h-1l-4 7c0 1-1 2-1 2h-1v-2-1h-1c3-1 4-3 5-5v-1l-1-1c1-1 1-2 1-4 2-6 4-10 8-15l5-7c1-2 4-2 5-2l1 1 4-2 6-5 2-2z" class="C"></path><path d="M765 258c2 0 3 0 4-1-1 2-2 5-4 7v-1l-1-1c1-1 1-2 1-4z" class="I"></path><path d="M796 226h1l-1 2 1 1h1l-1 1h0l-8 7v-1l1-1h-1c2-3 4-4 5-7l2-2z" class="a"></path><path d="M773 243l5-7c1-2 4-2 5-2l1 1c-3 3-5 6-7 10-3 4-6 8-8 12-1 1-2 1-4 1 2-6 4-10 8-15z" class="E"></path><path d="M625 609c1-1 1 0 1-1l1-1c0-1 0-1 1-1v-2l3-5h0l-56 140-1-2 1-2c0-1 0-2 1-3l-1-1c-1 1 0 1-1 2 0 1-2 2-3 3 1-3 4-6 4-8 0-1 1-2 1-2 0-1 1-2 1-2 0-4 2-7 3-10 4-10 9-18 13-27l20-48c2-4 3-7 4-11l6-13c0-2 1-5 2-6z" class="D"></path><path d="M571 736c1-1 3-2 3-3 1-1 0-1 1-2l1 1c-1 1-1 2-1 3l-1 2 1 2-33 79h0 0v-1-1c0-3 2-7 3-10 1-1 2-3 1-4 0 1 0 1-1 2 0 1-1 3-1 4-1 1-1 2-2 3v1 2s-1 1-1 2c-1-2 2-8 2-10l-2 7h-1v2c0 1 0 1-1 2v1h-1l-2 1c2-4 4-9 5-13v-2l1-1v-2c4-4 6-10 8-15l9-19 6-18c2-5 4-9 6-13z" class="L"></path><defs><linearGradient id="CN" x1="530.134" y1="868.151" x2="514.479" y2="858.821" xlink:href="#B"><stop offset="0" stop-color="#a6a6a7"></stop><stop offset="1" stop-color="#c4c3c2"></stop></linearGradient></defs><path fill="url(#CN)" d="M539 818v-1c1-1 1-1 1-2v-2h1l2-7c0 2-3 8-2 10 0-1 1-2 1-2v-2-1c1-1 1-2 2-3 0-1 1-3 1-4 1-1 1-1 1-2 1 1 0 3-1 4-1 3-3 7-3 10v1 1h0c-4 12-9 24-14 36-1 1-2 6-3 7l-10 26-11 29-2-4-3-9c0-1 0-2 1-3 0-2 2-5 3-7l7-14 15-32c3-5 5-11 7-17 2-4 4-8 4-11l2-1h1z"></path><path d="M515 887v-3c1-1 0-1 0-2l1-2c0-2 1-3 2-5 1-1 1-1 1-2v-2c2-3 3-8 6-10l-10 26z" class="H"></path><path d="M536 819l2-1h1l-24 53c-2 6-6 10-7 16-3 8-4 16-6 25l-3-9c0-1 0-2 1-3 0-2 2-5 3-7l7-14 15-32c3-5 5-11 7-17 2-4 4-8 4-11z" class="S"></path><path d="M613 617v1l2-3-31 77-40 100-15 37c-1 2-3 6-3 9l-3 1c-1-1-1-1-2-1l1-3v-5l2-1c-1-5-3-12-5-17 1-2 2-3 2-6l1 3 1-2h0l3-3 3-8 1-3c1-1 1-3 2-4 1 1 1 2 2 3l-1 3h1v2c1 1 2 1 3 1 3-7 7-13 9-20l3-8 19-45 11-27c1-1 1-3 2-5l32-76z" class="G"></path><path d="M521 806l1 3 4 14c1 5 1 9-1 14v1c-1-3-1-6-1-9-1-5-3-12-5-17 1-2 2-3 2-6z" class="o"></path><path d="M532 789c1 1 1 2 2 3l-1 3h1v2c1 1 2 1 3 1l-3 9-6 17h-1l-1-1h0l-4-14 1-2h0l3-3 3-8 1-3c1-1 1-3 2-4z" class="L"></path><path d="M527 806c1-2 2-2 3-3l-3 9c-1-1-1-2-2-3l2-3z" class="G"></path><path d="M533 795h1v2c1 1 2 1 3 1l-3 9c0-3 0-7-2-9l1-3z" class="i"></path><path d="M526 804l1 2-2 3c1 1 1 2 2 3-1 4 0 8-1 11h0l-4-14 1-2h0l3-3z" class="I"></path><path d="M526 804l1 2-2 3v1l-1 1-1-4h0l3-3z" class="X"></path><path d="M532 789c1 1 1 2 2 3l-1 3-1 3-2 5c-1 1-2 1-3 3l-1-2 3-8 1-3c1-1 1-3 2-4z" class="B"></path><path d="M563 720l1-1v2l1 2h0c1-1 1-1 2-1 1 1 1 1 1 2v1l-19 45-3 8c-2 7-6 13-9 20-1 0-2 0-3-1v-2h-1l1-3c-1-1-1-2-2-3-1 1-1 3-2 4l-2 1v-3c0-3 1-6 1-10 0-2-1-4 1-5 2-2 2-4 2-7h0c2-2 3-7 4-9l10-27 6 3h0 1 1l-3 6 1 2 11-24z" class="Z"></path><path d="M558 734v3c0 2-2 4-3 6-1 5-3 9-5 13-1 2-3 5-3 7 0 1 1 5 2 7l-3 8v-2c1-3 1-10 0-13h0c1-6 3-10 5-15l5-11v-1c1 0 1-1 2-2z" class="d"></path><path d="M539 786c3-6 3-14 7-19v9 2c-2 7-6 13-9 20-1 0-2 0-3-1v-2h-1l1-3c1 1 1 2 3 2 0-1 0-1 1-2 0-2 1-4 1-6z" class="J"></path><defs><linearGradient id="CO" x1="569.019" y1="742.201" x2="543.633" y2="749.609" xlink:href="#B"><stop offset="0" stop-color="#939495"></stop><stop offset="1" stop-color="#b7b4b3"></stop></linearGradient></defs><path fill="url(#CO)" d="M563 720l1-1v2l1 2h0c1-1 1-1 2-1 1 1 1 1 1 2v1l-19 45c-1-2-2-6-2-7 0-2 2-5 3-7 2-4 4-8 5-13 1-2 3-4 3-6v-3c0-1 0 0 1-1s1-1 1-2 0-1 1-2v-1c0-1 1-3 1-4l2-3-1-1z"></path><path d="M546 733l6 3h0 1 1l-3 6 1 2-5 12c-1 2-2 5-3 7-1 3-7 15-6 17l1 1v5c0 2-1 4-1 6-1 1-1 1-1 2-2 0-2-1-3-2s-1-2-2-3c-1 1-1 3-2 4l-2 1v-3c0-3 1-6 1-10 0-2-1-4 1-5 2-2 2-4 2-7h0c2-2 3-7 4-9l10-27z" class="o"></path><path d="M532 769c1 1 1 1 1 2h1l-4 17c0 1-1 2-2 3 0-3 1-6 1-10 0-2-1-4 1-5 2-2 2-4 2-7z" class="D"></path><path d="M533 788l3-9 7-17 1 1c-1 3-7 15-6 17l1 1v5c0 2-1 4-1 6-1 1-1 1-1 2-2 0-2-1-3-2s-1-2-2-3l1-1z" class="i"></path><path d="M532 789l1-1c1 2 3 3 5 4-1 1-1 1-1 2-2 0-2-1-3-2s-1-2-2-3z" class="G"></path><defs><linearGradient id="CP" x1="584.268" y1="716.322" x2="593.816" y2="720.197" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#4e4e4e"></stop></linearGradient></defs><path fill="url(#CP)" d="M671 508c0-1 0-1 1-1 0-1 0-2 2-3l1 1-21 51c1 0 2 0 2-1 1-2 2-5 3-7l6-14c1-2 1-4 2-5 1 0 2-1 2-1-1 2-6 15-6 17l-17 41c-1 5-4 9-5 14l-1 4c-3 5-5 11-7 16-1 5-3 10-5 14l-6 13-12 29c0 1-1 2-1 3-1 2-2 5-3 6l-4 9-7 16c0 2-1 5-2 7-1 5-4 10-6 16-3 5-4 12-8 17l1 1c-5 9-8 18-12 27-1 5-4 10-5 15-2 3-4 7-5 11 1 5-4 11-4 16l-27 67-8 21c-2 5-4 9-6 13h0-1v2s-1 1-1 2v1l-2 2v2l-1 1c0 1 0 1-1 2v-1c0-1 0-2-1-3 0 0 0-1-1-2v-1c-1-1-1-4 0-5l1 3 1 1h1v-2h-1c-1-1-3-5-3-7l11-29 10-26c1-1 2-6 3-7 5-12 10-24 14-36h0l33-79 56-140h0l4-8c4-7 6-14 9-20l16-39c4-8 7-17 11-24z"></path><path d="M605 674c1 2 1 2 0 4-1 4-3 7-3 11l-3 4-1 1c0 1-1 2-1 3l-1 2h0v-4c1-2 2-5 3-8l6-13z" class="H"></path><path d="M633 607l2-2v3l-1 1c0 2-1 2-2 4v1c-1 2-2 3-3 6-3 8-7 17-9 26-1-1 0-2 0-3 0-2 0-2-1-2 0-3 2-7 3-9 4-9 7-17 11-25z" class="b"></path><path d="M602 689l2-3 1-1h0 1l-4 9c-1 1-2 2-2 3-2 6-6 12-9 18-2 5-4 10-7 15-1 1-2 2-2 3 0-2 1-4 1-5l9-21c1-4 2-8 4-12v4h0l1-2c0-1 1-2 1-3l1-1 3-4z" class="S"></path><path d="M596 695v4h0l1-2c0-1 1-2 1-3l1-1c-1 5-4 11-6 15v-2h0l-1 1h0c1-4 2-8 4-12z" class="o"></path><defs><linearGradient id="CQ" x1="617.323" y1="657.314" x2="610.533" y2="655.643" xlink:href="#B"><stop offset="0" stop-color="#817f81"></stop><stop offset="1" stop-color="#9f9d9c"></stop></linearGradient></defs><path fill="url(#CQ)" d="M619 641c1 0 1 0 1 2 0 1-1 2 0 3l1 1v-1l1 1-12 29c0 1-1 2-1 3-1 2-2 5-3 6h-1 0l-1 1-2 3c0-4 2-7 3-11 1-2 1-2 0-4l14-33z"></path><path d="M569 766c-1 1-1 2-1 3v1h-1l1 1 2-3c2-4 4-9 6-12 1-1 1-2 1-2 1-2 1-3 2-4l1 1c-5 9-8 18-12 27-1 5-4 10-5 15-2 3-4 7-5 11h-1v-1c0-1 1-1 1-1v-1c0-1 1-2 1-3-2 2-4 5-5 7s-3 2-4 4-2 5-2 7l-6 14c-1 1-2 5-3 6 1-3 2-5 3-8l6-15 12-29c2-6 4-12 7-17h1v-1h1z" class="J"></path><path d="M583 728c0 1-1 3-1 5 0-1 1-2 2-3 3-5 5-10 7-15 3-6 7-12 9-18 0-1 1-2 2-3l-7 16c0 2-1 5-2 7-1 5-4 10-6 16-3 5-4 12-8 17-1 1-1 2-2 4 0 0 0 1-1 2-2 3-4 8-6 12l-2 3-1-1h1v-1c0-1 0-2 1-3h-1v1h-1l16-39z" class="b"></path><path d="M569 766c2 0 5-14 7-17 2-4 3-8 5-11l7-12c0-1 1-2 2-4h0c0-1 1-1 1-2s1-2 2-3c-1 5-4 10-6 16-3 5-4 12-8 17-1 1-1 2-2 4 0 0 0 1-1 2-2 3-4 8-6 12l-2 3-1-1h1v-1c0-1 0-2 1-3z" class="Y"></path><defs><linearGradient id="CR" x1="639.855" y1="602.622" x2="634.845" y2="599.337" xlink:href="#B"><stop offset="0" stop-color="#6f6e70"></stop><stop offset="1" stop-color="#888686"></stop></linearGradient></defs><path fill="url(#CR)" d="M654 556c1 0 2 0 2-1 1-2 2-5 3-7l6-14c1-2 1-4 2-5 1 0 2-1 2-1-1 2-6 15-6 17l-17 41c-1 5-4 9-5 14l-1 4c-3 5-5 11-7 16-1 5-3 10-5 14l-6 13-1-1v1l-1-1c2-9 6-18 9-26 1-3 2-4 3-6v-1c1-2 2-2 2-4l1-1v-3l-2 2 15-36 6-15z"></path><path d="M629 620c1-2 3-7 5-8 0 1 0 2-1 3v1h0c-2 2-3 3-3 6-1 4-2 7-2 12l-6 13-1-1v1l-1-1c2-9 6-18 9-26z" class="Y"></path><path d="M371 193h2c1 2 1 11 1 13v13l6 6c-6 0-14-1-21 1-11 8-18 18-20 33-2 10 0 22 3 32 5 17 12 33 19 49l39 97 102 252 21 53c3 8 7 17 9 27h0c0 3 0 5-2 7-2 1-1 3-1 5 0 4-1 7-1 10v3l2-1-1 3-3 8-3 3h0l-1 2-1-3-1-2c1-2 1-3 0-5s0-3 0-5l-2-3v-2l-2 1c0 2 1 3 0 5-1-2-2-4-4-6l-2-5s1-1 1-2c-2-4-3-8-5-11-1-3-2-5-2-8l-3-9-3 1c0-1-1-3-2-5-1-4-3-6-6-9v-2l1 1 1-1v-4-1c0-1-1-3-1-4l-4-10-28-70c-3-3-4-8-5-12l-10-24-10-26c-2-6-4-12-7-17-2-5-3-9-6-13l-14-36-19-47-30-74c-1-1-1-3-2-3l-4-13-24-56-11-32-1-3-2-6-2-9c-3-14-2-30 2-43l1-1v-2c1-5 6-10 9-14h0l1 1c2-1 3-1 5-2 2-3 6-5 8-7v3c-5 3-8 6-11 11l1 2-1 2h1 6c2-2 4-4 7-6 6-5 14-9 22-10 2 0 4 1 6-1 0 0 0-13 1-15h0c1-1 1-1 1-3v-1z" class="q"></path><path d="M517 745c2 4 3 8 4 12 1 2 2 3 3 5v1 1c1 2 2 3 3 5v1 1h-1c0 2 2 5 2 8l1 2c0 4-1 7-1 10v3l2-1-1 3-3 8-3 3h0l-1 2-1-3-1-2c1-2 1-3 0-5s0-3 0-5l-2-3 3 1c0 2 2 5 2 8l2-5c1-3 3-8 2-11v-1c2-4-9-28-11-34h0l1 1v1l1-1c-1-2-1-3-1-5z" class="C"></path><path d="M530 793l-1 3-3 8-3 3h0l-1 2-1-3-1-2c1-2 1-3 0-5s0-3 0-5l3 10c2-3 4-7 5-10l2-1z" class="P"></path><path d="M359 226c-4-1-9 0-14 0 7-6 16-9 25-9l4 2 6 6c-6 0-14-1-21 1z" class="e"></path><path d="M371 193h2c1 2 1 11 1 13v13l-4-2h0c-3-2-8-1-11 0-6 1-9 3-14 7l-3 3-1-1c1 0 1-1 2-2-1 0-2 0-2-1 6-5 14-9 22-10 2 0 4 1 6-1 0 0 0-13 1-15h0c1-1 1-1 1-3v-1z" class="R"></path><path d="M363 213c2 0 5 1 6 0l1 2c-3 1-8 0-12 1h-1c-4 1-12 4-14 8-1 0-2 0-2-1 6-5 14-9 22-10z" class="N"></path><path d="M371 193h2c1 2 1 11 1 13l-2 2-1 7h-1 0l-1-2c-1 1-4 0-6 0 2 0 4 1 6-1 0 0 0-13 1-15h0c1-1 1-1 1-3v-1z" class="b"></path><path d="M429 533l1 1-3-8h0l1-1c1 2 1 4 2 6l7 18 16 37 9 24 15 36c1 2 2 5 3 7-1 1 0 1-1 1l-7-16c-1-2-1-4-2-5h-1c-1-1-1-2-1-3l-39-97z" class="L"></path><path d="M328 252v7h1v1c0 7 0 16 2 23l1 3 1 4 1 5c1 1 2 3 2 5l2 5 1 2 1 4 16 37 18 44 5 13 9 24c1 3 3 5 4 8l4 11c1 3 3 6 4 9l6 14 2 6 5 10 13 32c0 2 1 4 2 6l-1 1h0l3 8-1-1-29-70-33-80-22-54c-5-12-10-25-14-38-3-12-6-27-3-39z" class="Q"></path><defs><linearGradient id="CS" x1="416.576" y1="514.604" x2="437.901" y2="506.101" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232221"></stop></linearGradient></defs><path fill="url(#CS)" d="M338 211v3c-5 3-8 6-11 11l1 2-1 2h1 6c2-2 4-4 7-6 0 1 1 1 2 1-1 1-1 2-2 2l1 1c-7 6-13 16-14 25h0c-3 12 0 27 3 39 4 13 9 26 14 38l22 54 33 80 29 70 39 97c0 1 0 2 1 3h1c1 1 1 3 2 5l7 16c1 0 0 0 1-1 4 8 7 18 10 26l9 23 6 15c4 10 9 19 12 28 0 2 0 3 1 5l-1 1v-1l-1-1h0c2 6 13 30 11 34v1c-5-9-8-19-12-28l-19-47-73-181-61-151-19-46c-6-15-13-30-17-45-1-1-1-2-2-3h0v-3h0c-1-2-1-1-2-2 1 2 1 4 1 6l-3-7-3-3v-1c-1-1-2-2-3-4v-2 12h0l-1-1-1 1h0c-3-14-2-30 2-43l1-1v-2c1-5 6-10 9-14h0l1 1c2-1 3-1 5-2 2-3 6-5 8-7z"></path><path d="M341 223c0 1 1 1 2 1-1 1-1 2-2 2-4 4-8 8-11 12l-1-1c1-1 1-2 2-3 2-2 3-3 3-5 2-2 4-4 7-6z" class="d"></path><path d="M469 633h1c1 1 1 3 2 5l7 16c1 0 0 0 1-1 4 8 7 18 10 26l9 23 6 15c4 10 9 19 12 28 0 2 0 3 1 5l-1 1v-1l-1-1h0c-1-1-43-105-47-116z" class="N"></path><defs><linearGradient id="CT" x1="328.006" y1="243.483" x2="313.229" y2="273.424" xlink:href="#B"><stop offset="0" stop-color="#8c8c8b"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#CT)" d="M338 211v3c-5 3-8 6-11 11l1 2-1 2h1 6c0 2-1 3-3 5-1 1-1 2-2 3l1 1c-8 16-8 31-4 48-1-1-1-2-2-3h0v-3h0c-1-2-1-1-2-2 1 2 1 4 1 6l-3-7-3-3v-1c-1-1-2-2-3-4v-2 12h0l-1-1-1 1h0c-3-14-2-30 2-43l1-1v-2c1-5 6-10 9-14h0l1 1c2-1 3-1 5-2 2-3 6-5 8-7z"></path><path d="M316 241v1h0c4-2 8-1 11-2 0 1 0 2-1 2-3 0-6 0-9 1-1 1-2 1-2 3h0v-4-1h1z" class="f"></path><path d="M318 237c0 1 0 2 1 2h1l1-1c1-2 3-1 5-2s3-1 5-2c-1 1-1 2-2 3 0 1-1 2-2 3-3 1-7 0-11 2h0v-1c0-2 1-3 2-4z" class="b"></path><path d="M338 211v3c-5 3-8 6-11 11l1 2-1 2h1 6c0 2-1 3-3 5-2 1-3 1-5 2s-4 0-5 2l-1 1h-1c-1 0-1-1-1-2-1 1-2 2-2 4h-1v1c-3 7-3 13-3 21 0 2 0 5 1 8v-8c2 1 3 4 4 6 2 2 3 5 3 8l-3-3v-1c-1-1-2-2-3-4v-2 12h0l-1-1-1 1h0c-3-14-2-30 2-43l1-1v-2c1-5 6-10 9-14h0l1 1c2-1 3-1 5-2 2-3 6-5 8-7z" class="N"></path><path d="M338 211v3c-5 3-8 6-11 11l-9 12c-1 1-2 2-2 4h-1c0-2 1-3 2-5 2-8 8-13 13-18 2-3 6-5 8-7z" class="R"></path><path d="M327 225l1 2-1 2h1 6c0 2-1 3-3 5-2 1-3 1-5 2s-4 0-5 2l-1 1h-1c-1 0-1-1-1-2l9-12z" class="k"></path><path d="M314 279h0v-12 2c1 2 2 3 3 4v1l3 3 3 7c0-2 0-4-1-6 1 1 1 0 2 2h0v3h0c1 1 1 2 2 3 4 15 11 30 17 45l19 46 61 151 73 181 19 47c4 9 7 19 12 28 1 3-1 8-2 11l-2 5c0-3-2-6-2-8l-3-1v-2l-2 1c0 2 1 3 0 5-1-2-2-4-4-6l-2-5s1-1 1-2c-2-4-3-8-5-11-1-3-2-5-2-8l-3-9-3 1c0-1-1-3-2-5-1-4-3-6-6-9v-2l1 1 1-1v-4-1c0-1-1-3-1-4l-4-10-28-70c-3-3-4-8-5-12l-10-24-10-26c-2-6-4-12-7-17-2-5-3-9-6-13l-14-36-19-47-30-74c-1-1-1-3-2-3l-4-13-24-56-11-32-1-3-2-6-2-9h0l1-1 1 1z" class="n"></path><path d="M312 279h0l1-1 1 1 2 10h0 0c-1-1-1-1-2-1l-2-9z" class="C"></path><path d="M516 780c2 3 4 8 5 12l-3-1v-2l-3-7 1-2z" class="U"></path><path d="M506 763l3 3h0l7 14-1 2c-2-2-2-4-3-5-2-5-4-9-6-14z" class="N"></path><path d="M353 382l8 21h-1l-2-2h0c-1-1-1-3-2-3l-4-13c0-1 0-2 1-3z" class="d"></path><path d="M316 294c3 2 4 4 5 7l10 29-3-1-11-32-1-3z" class="N"></path><path d="M444 614c2 0 3 4 4 6h0c0-2 0-2-1-4v-1-1l19 48-1 1-5-13h-1c-3-3-4-8-5-12l-10-24z" class="S"></path><path d="M328 329l3 1 22 52c-1 1-1 2-1 3l-24-56zm60 146c1-2-1-4 0-6l25 60h-1c-2-2-2-4-4-7h-1l-19-47z" class="h"></path><path d="M504 763c0-8-5-17-8-24l-6-15c-1-2-2-3-1-5l7 18c4 9 8 18 10 26 2 5 4 9 6 14 1 1 1 3 3 5l3 7-2 1c0 2 1 3 0 5-1-2-2-4-4-6l-2-5s1-1 1-2c-2-4-3-8-5-11-1-3-2-5-2-8z" class="c"></path><path d="M511 782c3 2 4 5 5 8 0 2 1 3 0 5-1-2-2-4-4-6l-2-5s1-1 1-2z" class="O"></path><path d="M358 401h0l2 2h1l27 66c-1 2 1 4 0 6l-30-74z" class="H"></path><path d="M407 522h1c2 3 2 5 4 7h1l19 49 10 22c2 5 4 10 5 14v1 1c1 2 1 2 1 4h0c-1-2-2-6-4-6l-10-26c-2-6-4-12-7-17-2-5-3-9-6-13l-14-36z" class="i"></path><path d="M459 650h1l5 13 1-1c3 5 5 10 7 15l16 42h0c-1 2 0 3 1 5l6 15c3 7 8 16 8 24l-3-9-3 1c0-1-1-3-2-5-1-4-3-6-6-9v-2l1 1 1-1v-4-1c0-1-1-3-1-4l-4-10-28-70z" class="H"></path><path d="M492 734l9 20-3 1c0-1-1-3-2-5-1-4-3-6-6-9v-2l1 1 1-1v-4-1z" class="X"></path><path d="M313 294l3 1 1 2 11 32 24 56 4 13c1 0 1 2 2 3l30 74 19 47 14 36c3 4 4 8 6 13 3 5 5 11 7 17h-1c0-1 0-2-1-2l-2-5c1 4 3 6 3 10l-4-10c-1 2-2 5-2 8h0c-1 1-1 2-1 3l-1 1-1 1c0 8-4 16-9 22l-3 2v1c-1 1-1 1-1 2-1 0-1 0-1 1l-6 2-2-1h-3c-1-1-2-1-3-1h-1c0-1-1-1-1-1h-1l-1-1c-1-1-2-1-3-2h0c0-1-3-2-3-3v-1h-1l-2-4 1-1v-2c-2-4-2-8-1-12s3-6 7-8v-1l-4 1h-1l1-2h-4l-5 2c-2-2-2-2-5-2-1 2 0 4-2 6 0 3 0 5 1 8l-1 1c-1 0-1 1-1 2v3c1 4 3 7 5 11 1 3 2 6 4 8l5 12c-3-2-4-5-6-7h0v3 1l-1-3h-2c-1-1-1-4-2-5-3-7-6-15-10-22v4l-7-18-23-56 1-2-30-74c-3-5-5-10-6-14-1-3-2-4-1-7-1-3-3-5-4-8 0-2-1-4 0-5v-1l-11-25 1-1 1-1c0-1-1-2-1-3-2-4-4-8-7-11 0-2-1-3-1-4h0l2 3 1-1h1c0-3-2-5-3-7 0-1 0-2-1-3h0l1-2c0 1 1 2 2 3l1-1c2 2 5 6 8 7 1 1 2 3 3 3h2l-2-2h2c2 1 4 4 6 5 1 1 3 2 4 3 2 0 4 0 6-1-9-6-18-13-25-21-5-6-9-12-13-19-1-2-3-5-3-7v-1c-1-3-3-6-5-8-1-1 0-1-1 0l-4-10s0-1-1-1v-1h1c-1-2-1-3-2-4h1c0 1 1 2 2 3h0v-2c-1-1-1-2-1-2v-3c2 1 2 3 4 4 1 1 2 3 3 4l7 9h0c7 8 16 12 27 14v-1c3 0 6-1 8-4 1-2 1-2 0-4-1-3-3-5-6-6v-1c2 0 2-1 4 0l2-2c2-1 4-2 6-2 0-2 0-3-1-4 0-1-1-1-2-1l3-1h0l1-1 1-1c1-2 0-5 1-6v-1c0-3-1-3-2-6 0-1 1-3 1-4z" class="p"></path><path d="M377 467l4-1 1 1v2c-1 1-2 1-3 2h0c-1-1-1-1-2-1l1-2-1-1z" class="C"></path><path d="M367 479c1 0 1-1 2-2 2-1 4-2 6-1-1 1-1 2-1 4h-1 0l-4 1h-1l-1-2z" class="Q"></path><path d="M397 507c1 3 3 7 4 10-2 0-3-1-5-2v-2c0-1 0-1-1-2 1-1 2-2 2-4z" class="N"></path><path d="M314 305c1 4 1 7 0 11l-3 3c0-2 0-3-1-4 0-1-1-1-2-1l3-1h0l1-1 1-1c1-2 0-5 1-6z" class="U"></path><path d="M375 476c3-2 5-2 9-2 1 1 0 1 0 2s-1 1-2 1-1 1-2 1h0v2c-1 0-2 0-2-1l-2-1h1l-2-2z" class="N"></path><path d="M378 479v-2h1l1 1h0v2c-1 0-2 0-2-1z" class="n"></path><path d="M314 316c0 1 0 2 1 3l2-2c1 0 3 0 4 2 1 1 1 2 1 4-2-1-3-3-5-3-4-1-6 1-9 3-1-1-2-1-3-2 2-1 4-2 6-2l3-3z" class="E"></path><path d="M421 558c3 4 4 8 6 13 3 5 5 11 7 17h-1c0-1 0-2-1-2l-2-5c1 4 3 6 3 10l-4-10c0-1-1-3-2-4-2-6-6-12-9-19 3 2 9 16 10 20h1c0-1 0-1-1-2l-1-3-1-1c0-1 0-2-1-3v-1-1h-1v-2c-1-1-1-2-1-2-1-2-1 0-1-2h0c-1-2-1-2-1-3z" class="j"></path><path d="M375 476l2 2h-1l2 1c0 1 1 1 2 1v-2c4 3 8 6 10 10-2 2-2 2-3 4-2-6-7-10-13-12 0-2 0-3 1-4z" class="a"></path><path d="M390 488c3 6 5 13 7 19 0 2-1 3-2 4l-1-2c-1-3-2-7-3-10-2-2-3-5-4-7 1-2 1-2 3-4z" class="C"></path><path d="M391 499c1 3 2 7 3 10l1 2c1 1 1 1 1 2l-1 2c-1 1-2 1-3 1h-3-1c-1 0-3-1-4-1l-1-1v-1l2-3h-1 0l2-2c1-1 3-3 3-4h1v1l1-1c0-2 0-3-1-4l1-1z" class="H"></path><path d="M388 516c1-1 1-1 2-1 1-1 1 0 1-1 1 0 2-1 2-2 1 0 0-2 0-3h1l1 2c1 1 1 1 1 2l-1 2c-1 1-2 1-3 1h-3-1z" class="h"></path><path d="M386 508c1-1 3-3 3-4h1v1l1-1c0 2 0 2-1 4l-1 1c-1 2-2 3-4 4-1 1-1 1-1 2l-1-1v-1l2-3h-1 0l2-2z" class="a"></path><path d="M385 506l1 2-2 2h0 1l-2 3v1l1 1c1 0 3 1 4 1h1c5 4 14 7 17 13-1 1-1 1-2 1-2 0-3-1-4-1l-3-3c-3-1-5-2-7-2-2-1-3-1-4-2l-10-3c-1-1-3-2-5-3-1-1-4-1-5-1-2-1-5-3-8-3l1-1c2-1 3 0 5 0 1-1 1-1 2-1 2 1 3 0 5 0l3-1 2 3 3-1c0-1 5-4 6-5z" class="q"></path><path d="M385 506l1 2-2 2h0c-1 1-3 2-6 3h-1l2-2c0-1 5-4 6-5z" class="Q"></path><path d="M371 510l3-1 2 3c-4 1-7 1-11-1l1-1c2 1 3 0 5 0z" class="F"></path><path d="M371 516c7 1 14 3 21 6 1 1 3 2 5 4-3-1-5-2-7-2-2-1-3-1-4-2l-10-3c-1-1-3-2-5-3z" class="f"></path><path d="M326 348c2 1 4 2 7 4 0 1 1 2 1 4-1 3-3 6-6 7l1 1c6 1 11 4 14 9 5 8 9 18 8 27-1-2-2-3-3-5l-1-5c-7-12-19-17-31-23-1 1-4-2-6-2-1 0-2-1-2-1v-3c4 2 8 2 12 3v-1c-1 0 0 0-1-1h2c1 0 0 0 1-1h1 2c2 0 6-5 7-7v-1c-1-1-2-1-3-1l1 1v1c-1 2-2 3-3 4l-4 2v-3c-2 0-4-1-6-1 1-1 2-1 3-2h1v-1l1-1c2-1 3-2 4-4z" class="U"></path><path d="M316 367l9 3c7 3 13 5 17 11 2 2 4 6 5 9-7-12-19-17-31-23z" class="B"></path><path d="M320 364v-1c-1 0 0 0-1-1h2c1 1 1 2 3 2h1c6 2 13 6 16 11l1 1c-2-2-4-4-6-5-7-3-15-3-22-7l7 1v-1h-1z" class="W"></path><path d="M365 480v2c1-1 1-2 2-3l1 2h1l4-1h0 1c6 2 11 6 13 12 1 2 2 5 4 7l-1 1c1 1 1 2 1 4l-1 1v-1h-1c0 1-2 3-3 4l-1-2c-1 1-6 4-6 5l-3 1-2-3-3 1c-2 0-3 1-5 0 0 0 1-1 1-2h-1l-2-1h0c5-2 6-7 11-10h0l-1-2s1 0 1-1c-1-1-5-2-7-2-1 0-2 0-3-1h0l-2-1 2-1h5c2 0 2 0 2-2 0 0-1-1-1-2h-1-2c-1 0-2 0-3-1-1-2-1-2 0-4z" class="Z"></path><path d="M370 485c2 0 6 0 7 2l2 1c1 1 1 2 1 3-1 0-1 0-2-1l-2 1h0-4l-2-2c2 0 2 0 2-2 0 0-1-1-1-2h-1z" class="L"></path><path d="M377 487l2 1c1 1 1 2 1 3-1 0-1 0-2-1l-2 1h0l-1-1v-2c2 0 1 1 3 1 0-1 0-1-1-2h0z" class="a"></path><path d="M365 480v2c1-1 1-2 2-3l1 2h1l4-1h0 1c0 1 1 1 1 2l3 1 1 2c2 1 4 1 4 3-1 0-2-1-3-2-3-1-5-2-8-2-1-1-2-1-3-1l-1 2c-1 0-2 0-3-1-1-2-1-2 0-4z" class="R"></path><path d="M379 488c5 3 9 6 11 12 1 1 1 2 1 4l-1 1v-1h-1c0 1-2 3-3 4l-1-2 2-2h0c1-2 0-2-1-3v-1c-1 0-1-1-1-1-1-2-3-4-5-5-1-1-2-2-4-3l2-1c1 1 1 1 2 1 0-1 0-2-1-3z" class="C"></path><path d="M365 489h5l2 2h4 0c2 1 3 2 4 3 2 1 4 3 5 5 0 0 0 1 1 1v1c1 1 2 1 1 3h0l-2 2c-1 1-6 4-6 5l-3 1-2-3-3 1c1-2 2-3 4-3 1-1 1 0 2-1 1 0 5-4 6-6-1-1-1-3-2-4-2-2-3-2-6-2-1-1-5-2-7-2-1 0-2 0-3-1h0l-2-1 2-1z" class="c"></path><path d="M365 489h5l2 2h-7l-2-1 2-1z" class="S"></path><path d="M374 509c3 0 6-2 9-4v-1 1l-2 2h1c1 0 2-1 3-2l1-1h1l-2 2c-1 1-6 4-6 5l-3 1-2-3z" class="X"></path><path d="M375 494c3 0 4 0 6 2 1 1 1 3 2 4-1 2-5 6-6 6-1 1-1 0-2 1-2 0-3 1-4 3-2 0-3 1-5 0 0 0 1-1 1-2h-1l-2-1h0c5-2 6-7 11-10h0l-1-2s1 0 1-1zm-67-171c3-2 5-4 9-3 2 0 3 2 5 3 2 4 3 8 4 13 1 2 0 5 1 7 0 1 0 2-1 4h-1v1h1c-1 2-2 3-4 4h-1l-2-2h0l-1-1 2-2c1 0 1-1 2-1l-1-1c-1 1-2 2-3 2s-1 0-2 1h-2l-1 1c-1 0-1 0-2-1 1-1 2 0 4-1-1-1-2-2-3-2-3-1-6 1-9-1v-1h-2c0-1-1-1-1-2l-1-1-2-1v-1c3 0 6-1 8-4 1-2 1-2 0-4-1-3-3-5-6-6v-1c2 0 2-1 4 0l2-2c1 1 2 1 3 2z" class="a"></path><path d="M314 322h0 2c4 4 6 7 6 12 0 3-1 6-3 8-3 2-7 3-10 2h-2c3 0 6 0 8-2 2-1 4-3 4-5v-1c-2 2-4 4-6 5 3-3 5-6 7-9-1-5-1-6-6-10z" class="O"></path><path d="M303 323l2-2c1 1 2 1 3 2l-1 1c1 2 1 2 2 3 1 0 2 0 3-1h-1l2-1c2 1 2 1 3 3 0 2 0 4-2 6-2 3-4 5-8 6h-7l-2-1v-1c3 0 6-1 8-4 1-2 1-2 0-4-1-3-3-5-6-6v-1c2 0 2-1 4 0z" class="F"></path><path d="M303 323l2-2c1 1 2 1 3 2l-1 1c1 2 1 2 2 3 1 0 2 0 3-1 0 1 1 2 2 2 1 1 0 0 1 2-1 0-1 0-2 1-2 1-4 0-6-1 0-1-1-2-1-3 0-2-3-2-3-4z" class="p"></path><path d="M308 323c3-2 5-4 9-3 2 0 3 2 5 3 2 4 3 8 4 13 1 2 0 5 1 7 0 1 0 2-1 4h-1v1h1c-1 2-2 3-4 4h-1l-2-2h0l-1-1 2-2c1 0 1-1 2-1l-1-1c-1 1-2 2-3 2s-1 0-2 1h-2l-1 1c-1 0-1 0-2-1 1-1 2 0 4-1-1-1-2-2-3-2-3-1-6 1-9-1h4 2c3 1 7 0 10-2 2-2 3-5 3-8 0-5-2-8-6-12h-2 0v1c-1 0-1 0-2 1-1 0-2 0-2 1h3l-2 1h1c-1 1-2 1-3 1-1-1-1-1-2-3l1-1z" class="Q"></path><path d="M326 336c1 2 0 5 1 7 0 1 0 2-1 4h-1v1h1c-1 2-2 3-4 4h-1l-2-2h0l1-1c2 0 4-3 5-5v-1c1-2 1-5 1-7z" class="X"></path><path d="M256 308c2 1 2 3 4 4 1 1 2 3 3 4l7 9h0c7 8 16 12 27 14l2 1 1 1c0 1 1 1 1 2h2v1c3 2 6 0 9 1 1 0 2 1 3 2-2 1-3 0-4 1 1 1 1 1 2 1l1-1h2c1-1 1-1 2-1s2-1 3-2l1 1c-1 0-1 1-2 1l-2 2 1 1h0l2 2h1l-1 1v1h-1c-1 1-2 1-3 2 2 0 4 1 6 1v3l4-2c1-1 2-2 3-4v-1l-1-1c1 0 2 0 3 1v1c-1 2-5 7-7 7h-2-1c-1 1 0 1-1 1h-2c1 1 0 1 1 1v1c-4-1-8-1-12-3-6-2-12-5-18-9l-9-6-1-1-3-3 1-2c-3-3-6-6-9-10-1-2-5-5-6-7-1-4-4-8-6-12h-1v-3z" class="n"></path><path d="M317 356c2 0 4 1 6 1 0 1 0 1-1 1-2 1-4 0-6 0v-2h1z" class="q"></path><path d="M297 348c7 3 15 5 22 2l2 2h1l-1 1-3 1c-7 1-15-2-21-6z" class="B"></path><defs><linearGradient id="CU" x1="303.421" y1="360.626" x2="304.42" y2="352.211" xlink:href="#B"><stop offset="0" stop-color="#191917"></stop><stop offset="1" stop-color="#353234"></stop></linearGradient></defs><path fill="url(#CU)" d="M278 340c9 8 18 14 29 17 5 2 10 4 16 3h0l4-2c1-1 2-2 3-4v-1l-1-1c1 0 2 0 3 1v1c-1 2-5 7-7 7h-2-1c-1 1 0 1-1 1h-2c1 1 0 1 1 1v1c-4-1-8-1-12-3-6-2-12-5-18-9l-9-6-1-1-3-3 1-2z"></path><path d="M256 308c2 1 2 3 4 4 1 1 2 3 3 4l7 9h0c7 8 16 12 27 14l2 1 1 1c0 1 1 1 1 2h2v1c3 2 6 0 9 1 1 0 2 1 3 2-2 1-3 0-4 1 1 1 1 1 2 1l1-1h2c1-1 1-1 2-1s2-1 3-2l1 1c-1 0-1 1-2 1l-2 2 1 1h0c-7 3-15 1-22-2-2 0-3-2-5-3h0l-2-2c-2 0-3-1-3-1l-6-4c-2-2-4-3-6-5 0-1 0-1-1-1-1 1-1 0-2 1 0-1-1-2-1-3h-2c-1-2-5-5-6-7-1-4-4-8-6-12h-1v-3z" class="U"></path><path d="M263 316l7 9c-1 1-1 1-2 1-2-2-3-2-3-4l-1-1c0-1 0-1-1-2v-1h0l-1-1 1-1z" class="a"></path><path d="M365 440c-1-3-2-5-2-8l1 2c1 1 2 2 2 3 1 1 1 2 1 3 1 1 1 1 0 2 1 3 1 6 1 9h0 1c0 3 0 4-1 7h1l1 1v-2c0-2 0-7-1-9v-2-1c-1-2-1-4-1-6l-1-1c0-1 0-2-1-3h1c1 2 2 5 2 7 1 6 2 12 2 19v3c2 2 5 0 6 3h0l1 1-1 2c1 0 1 0 2 1h0c-1 1-3 2-5 2-4 2-7 3-9 7-1 2-1 2 0 4 1 1 2 1 3 1h2 1c0 1 1 2 1 2 0 2 0 2-2 2h-5l-2 1c-2 0-3 0-5 1s-3 2-5 2c-1 0-1-1-2-1v-1c0-1 0-3-1-4 0-2 1-5 1-8l2-2v-3c1 0 3 0 4-1l1-2-1-1-1-1-1-1h-1l-1-1c-2-1-3-3-5-5l-2-6c-1-3-1-5-1-8l1 1h1 2l1-1v1c2 1 3 2 5 2 1-2 1-3 1-4v-1c1 2 1 2 1 4h1v-2c0-2-1-3-2-4 0-2 0-3-1-5 2 2 3 3 3 6 1 1 1 2 1 3h2v3l1-3 2-4v-3l1-1z" class="Q"></path><path d="M371 464v1c-1 0-2 1-3 1v-3c1-1 1-2 3-2v3zm6 3h0l1 1-1 2c1 0 1 0 2 1h0c-1 1-3 2-5 2l-1-1v-1c-1 0-3 2-4 3l-1-1c1-1 2-3 4-3l1-1 1-1h2l1-1z" class="a"></path><path d="M356 478c1 0 3-1 3-2v-1c1-2 2-3 4-4v2c1 0 2-1 2-2h1l1-2 1 1-3 3c-1 1-1 2-2 2l-1 2c0 1-1 2-1 3s0 2-1 4c-1 0-1 3-1 4-1 0-1 1-1 1h-1c1-2 1-2 1-3l1-1v-4c0-2-2-2-3-3z" class="H"></path><path d="M365 440c-1-3-2-5-2-8l1 2c1 2 2 4 2 7h0c1 6 1 12 1 18l-1-1c-2 4-3 7-6 11h-2l-1 1-1-1-1-1 1-1c1 1 2 1 4 1 1-2 3-4 3-7 1-2 1-6 2-8 0-5 1-8 0-13z" class="e"></path><path d="M353 477c1 0 2 0 3 1s3 1 3 3v4l-1 1c0 1 0 1-1 3h1s0-1 1-1c0-1 0-4 1-4h1v1c-1 1 0 2 0 3l4 1-2 1c-2 0-3 0-5 1s-3 2-5 2c-1 0-1-1-2-1v-1c0-1 0-3-1-4 0-2 1-5 1-8l2-2z" class="h"></path><path d="M355 439c2 2 3 3 3 6 1 1 1 2 1 3h2v3l1-3 2-4v-3l1-1c1 5 0 8 0 13-1 2-1 6-2 8 0 3-2 5-3 7-2 0-3 0-4-1l-1 1h-1l-1-1c-2-1-3-3-5-5l-2-6c-1-3-1-5-1-8l1 1h1 2l1-1v1c2 1 3 2 5 2 1-2 1-3 1-4v-1c1 2 1 2 1 4h1v-2c0-2-1-3-2-4 0-2 0-3-1-5z" class="N"></path><path d="M352 464c-1-2-2-3-3-4 1-2 0-2 1-3 1 1 2 2 4 3h1c1 0 1 1 2 1l1-1 1 1v1c1 1 1 1 1 2v1h-2c-1-1-4-1-6-1z" class="q"></path><path d="M345 448l1 1h1 2l1-1v1c2 1 3 2 5 2 0 1 1 4 0 5 0 0-1 1-1 2 0 0 1 1 1 2h-1c-2-1-3-2-4-3-1 1 0 1-1 3 1 1 2 2 3 4 2 1 3 2 4 3l-1 1h-1l-1-1c-2-1-3-3-5-5l-2-6c-1-3-1-5-1-8z" class="H"></path><path d="M338 450v1c2 1 3 2 4 3h1v-1l2 2c0 2 1 4 1 5l2 2c2 2 3 4 5 5l1 1h1l1 1 1 1 1 1-1 2c-1 1-3 1-4 1v3l-2 2c0 3-1 6-1 8 1 1 1 3 1 4v1c1 0 1 1 2 1 2 0 3-1 5-2s3-1 5-1l2 1h0c1 1 2 1 3 1 2 0 6 1 7 2 0 1-1 1-1 1l1 2h0c-5 3-6 8-11 10h0l2 1h1c0 1-1 2-1 2-1 0-1 0-2 1-2 0-3-1-5 0l-1 1c3 0 6 2 8 3 1 0 4 0 5 1 2 1 4 2 5 3l-14-4v1c9 3 18 5 26 9l-1 1 1 1-2 2h0l-6-2c-11-4-22-6-32-14-3-1-5-4-7-6-3-4-8-10-9-15 0-1-1-2-2-3v-1c-1-1-1-1-1-2 0-2 0-3-1-4-2-3-2-6-3-9l-2-4 1-1v-1c1-2 2-3 3-4l1 1v1c1 0 3 0 4 1l2-4c1-3 2-5 2-7h-1v-1c0-2 1-3 3-4z" class="g"></path><path d="M335 491c2 4 4 8 7 11 2 2 4 5 6 6 1 1 5 3 5 4h-1c-8-6-13-11-19-19 1-1 2-1 2-2z" class="D"></path><path d="M332 492l1 1c6 8 11 13 19 19h-1c-2-1-3-1-4-3-1 0-2 0-3-1v1c2 1 4 2 4 4-3-1-5-4-7-6-3-4-8-10-9-15z" class="B"></path><path d="M348 513c0-2-2-3-4-4v-1c1 1 2 1 3 1 1 2 2 2 4 3h1 1c1 2 3 2 5 3 9 5 20 6 29 11l1 1-2 2h0l-6-2c-11-4-22-6-32-14z" class="r"></path><defs><linearGradient id="CV" x1="354.809" y1="480.44" x2="347.216" y2="507.471" xlink:href="#B"><stop offset="0" stop-color="#c0bebf"></stop><stop offset="1" stop-color="#f6f5f6"></stop></linearGradient></defs><path fill="url(#CV)" d="M338 475l1 1h1l1 1c-1 4-2 8-1 12 2 6 4 11 10 14 5 3 9 4 14 4h0l2 1h1c0 1-1 2-1 2-1 0-1 0-2 1-2 0-3-1-5 0l-1 1c-6-2-13-6-17-12-3-4-6-14-5-19l1-1c0-2 0-4 1-5z"></path><path d="M338 450v1c2 1 3 2 4 3h1c0 2 1 3 1 5-1 1-1 1-1 2s0 2-1 4h-1 0c-2 4-4 7-6 10s-1 8-1 12l1 4c0 1-1 1-2 2l-1-1c0-1-1-2-2-3v-1c-1-1-1-1-1-2 0-2 0-3-1-4-2-3-2-6-3-9l-2-4 1-1v-1c1-2 2-3 3-4l1 1v1c1 0 3 0 4 1l2-4c1-3 2-5 2-7h-1v-1c0-2 1-3 3-4z" class="V"></path><path d="M338 461c0-2 1-4 2-5h1v2c0 1-1 2-1 3v1h-2v-1z" class="a"></path><path d="M338 461v1h2v3l-1 1c-1 0-2 1-3 2 0-1 0-2-1-3 1-2 2-3 3-4z" class="C"></path><path d="M338 450v1c0 1 1 2 0 4-1 1-1 2-1 3 0 2-1 3-2 4h-1c1-3 2-5 2-7h-1v-1c0-2 1-3 3-4z" class="D"></path><path d="M335 465c1 1 1 2 1 3-1 2-2 5-3 7-2 3-1 7 0 10 0 1 1 1 1 2l1 4c0 1-1 1-2 2l-1-1c0-1-1-2-2-3v-1c-1-1-1-1-1-2v-4c0-2 2-4 1-7 1-1 1-2 2-3 1-2 2-5 3-7z" class="U"></path><path d="M327 463l1 1v1c1 0 3 0 4 1h0v1c0 2-1 2-1 3-1 1-1 2-1 3h0v2c1 3-1 5-1 7v4c0-2 0-3-1-4-2-3-2-6-3-9l-2-4 1-1v-1c1-2 2-3 3-4z" class="G"></path><path d="M332 466v1c0 2-1 2-1 3-1 1-1 2-1 3h0v2c1 3-1 5-1 7v4c0-2 0-3-1-4 0-5 0-12 4-16z" class="C"></path><path d="M343 454v-1l2 2c0 2 1 4 1 5l2 2c2 2 3 4 5 5l1 1h1l1 1 1 1 1 1-1 2c-1 1-3 1-4 1v3l-2 2c0 3-1 6-1 8 1 1 1 3 1 4v1c1 0 1 1 2 1 2 0 3-1 5-2s3-1 5-1l2 1h0c1 1 2 1 3 1 2 0 6 1 7 2 0 1-1 1-1 1l1 2h0c-5 3-6 8-11 10-5 0-9-1-14-4-6-3-8-8-10-14-1-4 0-8 1-12l-1-1h-1l-1-1 4-10c1-2 1-3 1-4s0-1 1-2c0-2-1-3-1-5z" class="r"></path><path d="M350 475l3-1v3l-2 2c0 3-1 6-1 8 1 1 1 3 1 4v1c1 0 1 1 2 1 2 0 3-1 5-2s3-1 5-1l2 1h0c1 1 2 1 3 1 2 0 6 1 7 2 0 1-1 1-1 1h-1-3-2-3c-2 0-5 1-7 1v1c-3 0-6 0-8-1-4-2-6-5-7-9v-1h0v-1c0-3 1-4 2-7 1 0 1-1 2-2l3-1z" class="L"></path><path d="M348 494c-1-2-2-3-2-5v-1c2 2 3 3 5 3v1c1 0 1 1 2 1v1c-2 0-2-1-5 0z" class="b"></path><path d="M353 494h1l8-3c0 1 0 1-1 1 1 1 1 1 2 1l2 2c-2 0-5 1-7 1h-1-2c-3 0-4 0-7-2 3-1 3 0 5 0z" class="o"></path><path d="M353 493c2 0 3-1 5-2s3-1 5-1l2 1h0c1 1 2 1 3 1 2 0 6 1 7 2 0 1-1 1-1 1h-1-3-2-3l-2-2c-1 0-1 0-2-1 1 0 1 0 1-1l-8 3h-1v-1z" class="R"></path><path d="M350 475l3-1v3l-2 2h-1c0 1-1 2-2 3-1 2-2 4-4 5h-1v-1h0v-1c0-3 1-4 2-7 1 0 1-1 2-2l3-1z" class="a"></path><path d="M347 476l3-1c0 1-1 4-3 5h0l-2-2c1 0 1-1 2-2z" class="N"></path><path d="M343 454v-1l2 2c0 2 1 4 1 5l2 2c2 2 3 4 5 5l1 1h1l1 1 1 1 1 1-1 2c-1 1-3 1-4 1l-3 1-3 1c-1 1-1 2-2 2-1 3-2 4-2 7v1h0c-1-2 0-2-2-3v2c0 1-1 2-1 4-1-4 0-8 1-12l-1-1h-1l-1-1 4-10c1-2 1-3 1-4s0-1 1-2c0-2-1-3-1-5z" class="G"></path><path d="M346 460l2 2h0c0 2 1 3 1 5 0 3-2 5-4 7h0c0 1-1 2-2 2 1-3 3-7 4-11 0-2 0-2-1-3l-1 4c0-2 0-4 1-6z" class="L"></path><path d="M343 454v-1l2 2c0 2 1 4 1 5-1 2-1 4-1 6 1 3-3 8-4 11l-1-1h-1l-1-1 4-10c1-2 1-3 1-4s0-1 1-2c0-2-1-3-1-5z" class="C"></path><path d="M348 462c2 2 3 4 5 5l1 1h1l1 1 1 1 1 1-1 2c-1 1-3 1-4 1l-3 1-3 1h-1l-2 2 1-4h0c2-2 4-4 4-7 0-2-1-3-1-5h0z" class="j"></path><path d="M357 470h0l-2 2h-5v-2-3c2 0 2 1 3 2h3l1 1z" class="p"></path><path d="M317 403c4 1 7 2 11 3l8 5c1 0 3 2 3 2 1 0 2 0 3 1l1-1c1 1 2 2 4 2h0c2 2 3 3 5 4 2 2 4 4 7 5 0 2 4 6 4 8 0 3 1 5 2 8l-1 1v3l-2 4-1 3v-3h-2c0-1 0-2-1-3 0-3-1-4-3-6 1 2 1 3 1 5 1 1 2 2 2 4v2h-1c0-2 0-2-1-4v1c0 1 0 2-1 4-2 0-3-1-5-2v-1l-1 1h-2-1l-1-1c0 3 0 5 1 8l2 6-2-2c0-1-1-3-1-5l-2-2v1h-1c-1-1-2-2-4-3v-1c-2 1-3 2-3 4v1h1c0 2-1 4-2 7l-2 4c-1-1-3-1-4-1v-1l-1-1v-2c-1 0-2 0-2-1-2-1-1-3-1-5l-1-1-7-10v-1c0-2-1-3-1-5 0-4 0-7 3-10v-1h-2c-3-2-5-3-7-5l-8-8h2v-2l1-1-1-1h1-2v-1l2-3h1l1-1h-3l1-1 13-1z" class="C"></path><path d="M344 425l2 2v1h-2-2v-1c1 0 1-1 2-2z" class="U"></path><path d="M334 426l-2-1-6-5h1c1 0 3 1 4 1l1 1c1 1 3 2 4 3-1-1-2-1-3-2 0 1 1 2 1 3z" class="a"></path><path d="M334 426c0-1-1-2-1-3 1 1 2 1 3 2h0c4 2 6 4 10 6l2 1h0c-1-2-1-3-2-4v-1l3 2v1c1 1 2 2 2 3 1 2 3 4 4 6s1 3 1 5c1 1 2 2 2 4v2h-1c0-2 0-2-1-4-1-3-4-5-6-7h2c-2-3-6-4-9-7h-1c-2-2-3-3-5-4l-3-2z" class="n"></path><path d="M331 427l6 1c2 1 3 2 5 4h1c3 3 7 4 9 7h-2c-5-3-8-4-13-4h0 3v-1c-5 0-8 0-13 2l-1-1v-1c3-3 5-3 9-4l-7-1h3l-1-1 1-1z" class="Y"></path><path d="M331 427l6 1c2 1 3 2 5 4l-7-2-7-1h3l-1-1 1-1z" class="D"></path><path d="M301 414h2c4 1 9 4 13 4 2-1 4-1 6-1h1 1 1c1 1 1 1 2 3-1 1-3 2-4 2h-2v1c3 1 6 3 10 4l-1 1c-4-2-8-2-12 0h0v-1h-2c-3-2-5-3-7-5l-8-8z" class="Z"></path><path d="M316 418c2-1 4-1 6-1h1 1l-1 4-7-3z" class="U"></path><path d="M309 422c1-1 3-3 5-3 1 0 2 1 2 1 1 0 1 0 2 1 1 0 2 0 3 1h0v1c3 1 6 3 10 4l-1 1c-4-2-8-2-12 0h0v-1h-2c-3-2-5-3-7-5z" class="l"></path><path d="M318 428h0c4-2 8-2 12 0l1 1h-3l7 1c-4 1-6 1-9 4v1l1 1c-2 2-5 4-5 8 0 2 0 4 1 6v4l-7-10v-1c0-2-1-3-1-5 0-4 0-7 3-10z" class="U"></path><path d="M318 429h1 1c0 2 0 2-1 2l-1 1h-1l1-3z" class="Q"></path><path d="M328 429l7 1c-4 1-6 1-9 4v1l1 1c-2 2-5 4-5 8 0 2 0 4 1 6v4l-7-10c4-3 5-9 8-12 1-1 3-2 4-3z" class="f"></path><path d="M327 436c5-2 8-2 13-2v1h-3 0c5 0 8 1 13 4 2 2 5 4 6 7v1c0 1 0 2-1 4-2 0-3-1-5-2v-1l-1 1h-2-1l-1-1c0 3 0 5 1 8l2 6-2-2c0-1-1-3-1-5l-2-2v1h-1c-1-1-2-2-4-3v-1c-2 1-3 2-3 4v1h1c0 2-1 4-2 7l-2 4c-1-1-3-1-4-1v-1l-1-1v-2c-1 0-2 0-2-1-2-1-1-3-1-5l-1-1v-4c-1-2-1-4-1-6 0-4 3-6 5-8z" class="m"></path><path d="M345 448v-1c1-1 2-1 3-2 3 0 6 0 8 2 0 1 0 2-1 4-2 0-3-1-5-2v-1l-1 1h-2-1l-1-1z" class="U"></path><path d="M327 436c5-2 8-2 13-2v1h-3 0l-2 1h-1c0 1-1 2-2 3s-4 3-4 4c-1 1-1 4 0 5 0 1 1 1 2 2l-1 1 1 5-1 1h-1l-2-1-1-2c0-2-1-3-2-4-1-2-1-4-1-6 0-4 3-6 5-8z" class="a"></path><path d="M329 457l-3-7c-1-3-1-6 0-8 2-4 5-5 9-6h-1c0 1-1 2-2 3s-4 3-4 4c-1 1-1 4 0 5 0 1 1 1 2 2l-1 1 1 5-1 1z" class="E"></path><path d="M330 450c2-1 4-2 6-2l2 2c-2 1-3 2-3 4v1h1c0 2-1 4-2 7l-2 4c-1-1-3-1-4-1v-1l-1-1v-2c-1 0-2 0-2-1-2-1-1-3-1-5l-1-1v-4c1 1 2 2 2 4l1 2 2 1h1l1-1-1-5 1-1z" class="C"></path><path d="M335 455h1c0 2-1 4-2 7l-2 4c-1-1-3-1-4-1v-1l-1-1v-2c-1 0-2 0-2-1-2-1-1-3-1-5v1c2 2 3 3 5 3 3 0 4-2 6-4z" class="F"></path><path d="M317 403c4 1 7 2 11 3l8 5c1 0 3 2 3 2 1 0 2 0 3 1l1-1c1 1 2 2 4 2h0c2 2 3 3 5 4 2 2 4 4 7 5 0 2 4 6 4 8 0 3 1 5 2 8l-1 1v3l-2 4-1 3v-3h-2c0-1 0-2-1-3 0-3-1-4-3-6-1-2-3-4-4-6 0-1-1-2-2-3v-1l-3-2-2-2c-6-4-13-10-20-11-5-1-9-1-13-2-3 0-5-1-7-1l-1-1h1-2v-1l2-3h1l1-1h-3l1-1 13-1z" class="U"></path><defs><linearGradient id="CW" x1="323.686" y1="408.346" x2="324.786" y2="423.763" xlink:href="#B"><stop offset="0" stop-color="#100e0f"></stop><stop offset="1" stop-color="#3a3b39"></stop></linearGradient></defs><path fill="url(#CW)" d="M304 406h1c3 2 11 1 15 2 1 0 3 0 4 1l1 1c9 4 20 10 24 19l-3-2-2-2c-6-4-13-10-20-11-5-1-9-1-13-2-3 0-5-1-7-1l-1-1h1-2v-1l2-3z"></path><path d="M304 406h1c3 2 11 1 15 2 1 0 3 0 4 1l1 1c-7-1-14-2-21 0h-2v-1l2-3z" class="L"></path><path d="M339 413c1 0 2 0 3 1l1-1c1 1 2 2 4 2h0c2 2 3 3 5 4 2 2 4 4 7 5 0 2 4 6 4 8 0 3 1 5 2 8l-1 1v3l-2 4-1 3v-3-1c-1-4-2-8-4-11-5-10-10-16-18-23z" class="T"></path><path d="M342 414l1-1c1 1 2 2 4 2h0c2 2 3 3 5 4 0 2 1 3 1 4l-3-2c-3-2-6-5-8-7z" class="Z"></path><path d="M352 419c2 2 4 4 7 5 0 2 4 6 4 8 0 3 1 5 2 8l-1 1v3l-2 4v-9c-1-5-5-12-9-16 0-1-1-2-1-4z" class="H"></path><path d="M256 311h1c2 4 5 8 6 12 1 2 5 5 6 7 3 4 6 7 9 10l-1 2 3 3 1 1 9 6c6 4 12 7 18 9v3s1 1 2 1c2 0 5 3 6 2 12 6 24 11 31 23l1 5c1 2 2 3 3 5 1 7 6 12 10 18 3 6 5 11 6 17h-1c1 1 1 2 1 3l1 1c0 2 0 4 1 6v1 2c1 2 1 7 1 9v2l-1-1h-1c1-3 1-4 1-7h-1 0c0-3 0-6-1-9 1-1 1-1 0-2 0-1 0-2-1-3 0-1-1-2-2-3l-1-2c0-2-4-6-4-8-3-1-5-3-7-5-2-1-3-2-5-4h0c-2 0-3-1-4-2l-1 1c-1-1-2-1-3-1 0 0-2-2-3-2l-8-5v-1c-1-1-2-1-3-2l1-1-2-1-10-4v-1h-1l2-1c-1-2-3-3-4-5h0c-4-1-6-3-10-5 2 0 4 0 6-1-9-6-18-13-25-21-5-6-9-12-13-19-1-2-3-5-3-7v-1c-1-3-3-6-5-8-1-1 0-1-1 0l-4-10s0-1-1-1v-1h1c-1-2-1-3-2-4h1c0 1 1 2 2 3h0v-2c-1-1-1-2-1-2z" class="a"></path><path d="M342 410c4 1 6 3 9 5h0v-2-1c3 4 6 8 8 12-3-1-5-3-7-5-2-1-3-2-5-4l-5-5z" class="N"></path><path d="M288 358c0-2-8-7-8-10 5 5 10 10 16 14 15 11 36 18 47 34 1 1 2 3 2 5l-7-8c-1-1-1-2-2-3-2 0-2 0-3 1l-3-3-15-12-17-10c-3-2-8-5-10-8z" class="b"></path><path d="M330 388l1-1c3 0 2 2 5 3-2 0-2 0-3 1l-3-3z" class="f"></path><path d="M256 311h1c2 4 5 8 6 12 1 2 5 5 6 7 3 4 6 7 9 10l-1 2 3 3c-5-1-8-9-13-12 1 3 8 11 11 13h1c1 1 1 1 1 2 0 3 8 8 8 10l-1-1c-3 0-4-2-6-3l-2-2c0 2 4 5 5 6s2 2 2 3c-8-6-14-16-20-24v-1c-1-3-3-6-5-8-1-1 0-1-1 0l-4-10s0-1-1-1v-1h1c-1-2-1-3-2-4h1c0 1 1 2 2 3h0v-2c-1-1-1-2-1-2z" class="C"></path><path d="M256 311h1c2 4 5 8 6 12 1 2 5 5 6 7 3 4 6 7 9 10l-1 2c-5-5-9-10-13-15l-8-11c-1-2-1-3-2-4h1c0 1 1 2 2 3h0v-2c-1-1-1-2-1-2z" class="W"></path><path d="M256 318c3 4 5 8 7 11 2 4 5 7 7 10l11 13c1 1 5 3 6 5-3 0-4-2-6-3l-2-2c0 2 4 5 5 6s2 2 2 3c-8-6-14-16-20-24v-1c-1-3-3-6-5-8-1-1 0-1-1 0l-4-10z" class="G"></path><path d="M286 361c0-1-1-2-2-3s-5-4-5-6l2 2c2 1 3 3 6 3l1 1c2 3 7 6 10 8l17 10 15 12 3 3h1c0 2 1 3 3 5h-1l-2-2h-1v-1l-1 1c-2-1-4-2-5-3l-11-8c-6-5-14-9-20-13-4-3-7-6-10-9z" class="r"></path><path d="M266 337c6 8 12 18 20 24 3 3 6 6 10 9 6 4 14 8 20 13l11 8c1 1 3 2 5 3l1-1v1h1l2 2h1c-2-2-3-3-3-5h-1c1-1 1-1 3-1 1 1 1 2 2 3l7 8c2 2 5 6 5 9l1 2v1 2h0c-3-2-5-4-9-5l5 5h0c-2 0-3-1-4-2l-1 1c-1-1-2-1-3-1 0 0-2-2-3-2l-8-5v-1c-1-1-2-1-3-2l1-1-2-1-10-4v-1h-1l2-1c-1-2-3-3-4-5h0c-4-1-6-3-10-5 2 0 4 0 6-1-9-6-18-13-25-21-5-6-9-12-13-19-1-2-3-5-3-7z" class="n"></path><path d="M334 391c6 6 11 13 16 19l1 2v1c-5-8-12-14-19-19l1-1v1h1l2 2h1c-2-2-3-3-3-5z" class="B"></path><path d="M333 391c1-1 1-1 3-1 1 1 1 2 2 3l7 8c2 2 5 6 5 9-5-6-10-13-16-19h-1z" class="S"></path><path d="M307 384l35 26 5 5h0c-2 0-3-1-4-2l-1 1c-1-1-2-1-3-1 0 0-2-2-3-2l-8-5v-1c-1-1-2-1-3-2l1-1-2-1-10-4v-1h-1l2-1c-1-2-3-3-4-5h0c-4-1-6-3-10-5 2 0 4 0 6-1z" class="c"></path><path d="M311 390c2 0 3 1 5 2l2 2h1c1 3 4 4 5 7l-10-4v-1h-1l2-1c-1-2-3-3-4-5z" class="b"></path><path d="M274 377h0l2 3 1-1h1c0-3-2-5-3-7 0-1 0-2-1-3h0l1-2c0 1 1 2 2 3l1-1c2 2 5 6 8 7 1 1 2 3 3 3h2l-2-2h2c2 1 4 4 6 5 1 1 3 2 4 3 4 2 6 4 10 5h0c1 2 3 3 4 5l-2 1h1v1l10 4 2 1-1 1c1 1 2 1 3 2v1c-4-1-7-2-11-3l-13 1-1 1h3l-1 1h-1l-2 3v1h2-1l1 1-1 1v2h-2l8 8c2 2 4 3 7 5h2v1c-3 3-3 6-3 10 0 2 1 3 1 5v1l7 10 1 1c0 2-1 4 1 5 0 1 1 1 2 1v2c-1 1-2 2-3 4v1l-1 1 2 4c1 3 1 6 3 9 1 1 1 2 1 4 0 1 0 1 1 2v1c1 1 2 2 2 3 1 5 6 11 9 15-2 1-2 2-2 3h-1v1h-1c-1-1-2-2-2-4h-1l-1 1-7-14v1l1 4 1 1v5l-7-15c0-1-3-8-3-9 0-2-1-5-2-7l-3-8h-1c0-3-3-7-5-10l-1 2-10-22c-1-3-3-5-4-8 0-2-1-4 0-5v-1l-11-25 1-1 1-1c0-1-1-2-1-3-2-4-4-8-7-11 0-2-1-3-1-4z" class="C"></path><path d="M277 371c2 1 3 2 4 4v3c-2-2-3-4-4-7z" class="N"></path><path d="M285 380h2c1 1 1 1 1 2 1 1 1 1 2 1s2 1 4 1h0l-1 2c-3-2-5-3-8-6z" class="Q"></path><path d="M288 391l2-1c0 1 0 2 1 4 1 0 1 0 1-1 1 3 2 4 3 6 0 1 1 2 1 3l-1-1c-1 1-1 1-2 1l-5-11z" class="j"></path><path d="M297 388l1 2h0c2 0 1 0 2 1h3c-3 1-5 1-7 3v1 1l2 4-3-1c-1-2-2-3-3-6 1-1 3-2 4-4l1-1z" class="N"></path><path d="M288 391c-1-4-4-8-4-12l1 1c3 3 5 4 8 6l4 2-1 1c-1 2-3 3-4 4 0 1 0 1-1 1-1-2-1-3-1-4l-2 1z" class="E"></path><path d="M290 390l-3-6c3 2 5 4 9 5-1 2-3 3-4 4 0 1 0 1-1 1-1-2-1-3-1-4z" class="m"></path><path d="M293 402c1 0 1 0 2-1l1 1c1 2 2 3 3 5 1-1 3-1 4-1h1l-2 3v1h2-1l1 1-1 1v2h-2l-2 1c-3-4-4-9-6-13z" class="E"></path><path d="M300 410h3l1 1-1 1v2h-2c-1-1-1-2-1-4z" class="N"></path><path d="M299 407c1-1 3-1 4-1h1l-2 3v1h2-1-3 0c0-1-1-2-1-3h0z" class="M"></path><path d="M289 377h2c2 1 4 4 6 5 1 1 3 2 4 3 4 2 6 4 10 5h0c1 2 3 3 4 5l-2 1-8-4v-1h0v-1-1c-1 0-3-1-4-2l-10-8-2-2z" class="S"></path><path d="M303 391c1 1 1 1 2 1l8 4h1v1l10 4 2 1-1 1c1 1 2 1 3 2v1c-4-1-7-2-11-3l-13 1-1 1h3l-1 1h-1-1c-1 0-3 0-4 1-1-2-2-3-3-5 0-1-1-2-1-3l3 1-2-4v-1-1c2-2 4-2 7-3z" class="C"></path><path d="M317 403c-6-1-13-1-20-2h0c0-1 1-1 2-1l13 1c4 0 9 0 13 2 1 1 2 1 3 2v1c-4-1-7-2-11-3z" class="K"></path><path d="M295 399l3 1h1c-1 0-2 0-2 1h0c7 1 14 1 20 2l-13 1-1 1h3l-1 1h-1-1c-1 0-3 0-4 1-1-2-2-3-3-5 0-1-1-2-1-3z" class="R"></path><path d="M303 391c1 1 1 1 2 1l8 4h1v1c-6-1-12 0-18-1v-1-1c2-2 4-2 7-3z" class="E"></path><path d="M299 415l2-1 8 8c2 2 4 3 7 5h2v1c-3 3-3 6-3 10 0 2 1 3 1 5v1l7 10 1 1c0 2-1 4 1 5 0 1 1 1 2 1v2c-1 1-2 2-3 4v1l-1 1c-1-6-5-11-7-16l-17-38z" class="m"></path><path d="M274 377h0l2 3 1-1h1c0-3-2-5-3-7 0-1 0-2-1-3h0l1-2c0 1 1 2 2 3v1c1 3 2 5 4 7v1c0 2 0 4 1 6 3 8 7 16 10 23l23 51c-1-1-2-2-2-3l-3-6c-1-2-2-3-2-4s-1-2-2-4v-1-1c-1 0 0 0-1-1 0-1 0-2-1-3s-1-2-2-4l-5-10c-1 4 4 9 4 14v1c1 3 3 6 4 9l-1 1 3 9-1 2-10-22c-1-3-3-5-4-8 0-2-1-4 0-5v-1l-11-25 1-1 1-1c0-1-1-2-1-3-2-4-4-8-7-11 0-2-1-3-1-4z" class="K"></path><path d="M282 396l1-1 13 30c2 3 3 8 5 12 1 3 3 6 4 9l-1 1 3 9-1 2-10-22c-1-3-3-5-4-8 0-2-1-4 0-5v-1l-11-25 1-1z" class="H"></path><path d="M281 397l1-1 3 10c3 5 5 9 7 14v2c1 2 1 4 2 5 2 5 4 11 7 14l3 6 3 9-1 2-10-22c-1-3-3-5-4-8 0-2-1-4 0-5v-1l-11-25z" class="J"></path><path d="M301 437v-1c0-5-5-10-4-14l5 10c1 2 1 3 2 4s1 2 1 3c1 1 0 1 1 1v1 1c1 2 2 3 2 4s1 2 2 4l3 6c0 1 1 2 2 3 3 6 5 12 8 17h0c1 0 1 1 2 3 0-2-1-3 0-4v-2c1 3 1 6 3 9 1 1 1 2 1 4 0 1 0 1 1 2v1c1 1 2 2 2 3 1 5 6 11 9 15-2 1-2 2-2 3h-1v1h-1c-1-1-2-2-2-4h-1l-1 1-7-14v1l1 4 1 1v5l-7-15c0-1-3-8-3-9 0-2-1-5-2-7l-3-8h-1c0-3-3-7-5-10l-3-9 1-1c-1-3-3-6-4-9z" class="P"></path><path d="M325 473c1 3 1 6 3 9 1 1 1 2 1 4 0 1 0 1 1 2v1h-2c0-1-1-2-1-3-1-4-3-7-4-10h0c1 0 1 1 2 3 0-2-1-3 0-4v-2z" class="N"></path><path d="M305 446l8 17 1 2-1 1h-1c0-3-3-7-5-10l-3-9 1-1z" class="d"></path><path d="M328 489h2c1 1 2 2 2 3 1 5 6 11 9 15-2 1-2 2-2 3h-1l-10-21z" class="Q"></path><path d="M313 466l1-1 7 17 4 8c0 1 1 3 1 4v1l1 4 1 1v5l-7-15c0-1-3-8-3-9 0-2-1-5-2-7l-3-8z" class="H"></path><path d="M388 525c-8-4-17-6-26-9v-1l14 4 10 3c1 1 2 1 4 2 2 0 4 1 7 2l3 3c1 0 2 1 4 1 1 0 1 0 2-1 3 4 4 9 5 13 2 5 4 11 7 16 3 7 7 13 9 19 1 1 2 3 2 4-1 2-2 5-2 8h0c-1 1-1 2-1 3l-1 1-1 1c0 8-4 16-9 22l-3 2v1c-1 1-1 1-1 2-1 0-1 0-1 1l-6 2-2-1h-3c-1-1-2-1-3-1h-1c0-1-1-1-1-1h-1l-1-1c-1-1-2-1-3-2h0c0-1-3-2-3-3v-1h-1l-2-4 1-1v-2c-2-4-2-8-1-12s3-6 7-8v-1l-4 1h-1l1-2h-4l-5 2c-2-2-2-2-5-2-1 2 0 4-2 6 0-6-1-12 2-17l5-5h1c2-2 2-2 5-3-1-1-1-2 0-3l3-8c0-1 0-3-1-4l-4 2v-1l4-3v-1c1-1 1-2 1-4s-1-2-2-3v-4c1 0 1 0 1-1l-2-2c-1 0-1 0-1-1h2l-1-2h1c1-1 1-2 2-2h0l2-2-1-1 1-1z" class="C"></path><path d="M417 565c3 2 8 10 9 13 0 1 0 2-1 2v5c-1-7-6-13-8-20z" class="D"></path><path d="M397 562h1 0l1 2v-1h1c0 3-1 8 0 11 1 1 1 2 1 3-2 0-4 0-5-2-2-1-2-2-2-4l2-8 1-1z" class="U"></path><defs><linearGradient id="CX" x1="408.396" y1="547.923" x2="399.604" y2="554.077" xlink:href="#B"><stop offset="0" stop-color="#141210"></stop><stop offset="1" stop-color="#2e2e2f"></stop></linearGradient></defs><path fill="url(#CX)" d="M402 540h1v1c1 1 2 2 3 4 2 7 3 15-1 22l-3-27z"></path><path d="M403 577c0-1 0-2 1-2 4-2 3-5 6-8h0c-1 2-1 4-1 6-1 1-1 2-1 3 0-1 0-2 1-2 0-1 0-1 1-2s2-3 3-4v-1c0-1-1-3 1-4v1 4c-1 3-1 9 0 12 0 1 1 2 0 3-1 0-1 1-2 2l-3-6c-2-1-2-2-5-2h-1z" class="V"></path><path d="M388 525c-8-4-17-6-26-9v-1l14 4 10 3c1 1 2 1 4 2 2 0 4 1 7 2l3 3c3 4 5 10 6 16-1-2-2-3-3-4v-1h-1 0c-2-3-3-7-5-9-2-3-6-4-9-6z" class="j"></path><path d="M416 576c-1-4-1-10-1-14 1 1 1 2 2 3 2 7 7 13 8 20v8l-1 1v-4l-1-1c-1 2-1 3-3 5h-1l-1-2 1-1h-1v-1c0-1-1-2-1-3v-2l-2 1-1-3c1-1 0-2 0-3 1 0 2 0 3-1l-1-3z" class="p"></path><path d="M416 576c2 1 3 3 4 5v3c-2-2-2-3-3-5l-1-3z" class="F"></path><path d="M414 580c1 0 2 0 3-1 1 2 1 3 3 5 2 1 2 2 3 5-1 2-1 3-3 5h-1l-1-2 1-1h-1v-1c0-1-1-2-1-3v-2l-2 1-1-3c1-1 0-2 0-3z" class="U"></path><path d="M417 585l1-1 1 1v1 3c1 0 1 1 1 2h-1 0-1v-1c0-1-1-2-1-3v-2z" class="q"></path><path d="M388 527c4 2 8 6 10 10 2 7 2 13 0 19 0 2-1 5-1 6l-1 1-7 3h0v-1-1c-1 1-2 1-2 2h-4c-1-1-1-2 0-3l3-8c1 1 2 2 3 2 2-1 2-2 4-1 0-1 0-1 1-2 3-4 3-11 2-16-2-5-5-7-10-9l2-2z" class="F"></path><path d="M386 555c1 1 2 2 3 2 2-1 2-2 4-1-2 2-7 6-9 7h-1l3-8z" class="N"></path><path d="M386 529h0c5 2 8 4 10 9 1 5 1 12-2 16-1 1-1 1-1 2-2-1-2 0-4 1-1 0-2-1-3-2 0-1 0-3-1-4l-4 2v-1l4-3v-1c1-1 1-2 1-4s-1-2-2-3v-4c1 0 1 0 1-1l-2-2c-1 0-1 0-1-1h2l-1-2h1c1-1 1-2 2-2z" class="C"></path><path d="M391 549c0 2 0 3-2 5h-1v-1l1-2c0-1 1-1 1-3h1v1z" class="V"></path><path d="M384 531c3 1 4 2 6 4 2 4 3 7 2 11l-1 3v-1c0-2 0-3-1-5-1-3-3-9-6-10l-1-2h1z" class="T"></path><path d="M387 566c0-1 1-1 2-2v1 1h0l7-3-2 8c0 2 0 3 2 4 1 2 3 2 5 2h2 1l-5 1c-1 2-1 3-1 4 1 2 3 1 4 2l-12 3v-1l-4 1h-1l1-2h-4l-5 2c-2-2-2-2-5-2-1 2 0 4-2 6 0-6-1-12 2-17l5-5h1c2-2 2-2 5-3h4z" class="l"></path><path d="M386 577c1 0 2 0 4 1h0c0 2 0 5-2 6v1c-1-2 0-4-1-5l-1-3z" class="D"></path><path d="M383 571l3 2c2 1 3 2 4 4v1h0c-2-1-3-1-4-1l-4-4 1-2z" class="Q"></path><path d="M387 566c0-1 1-1 2-2v1 1h0l7-3-2 8c-1-1-1-1-1-2v-1h-1l-3 1c-2 0-4 0-6-1 0-1 2-1 2-1 1 0 1-1 2-1z" class="T"></path><path d="M377 569h1 2l3 2-1 2 4 4 1 3c0 1 0 2-1 3v2h-4l-5 2c-2-2-2-2-5-2-1 2 0 4-2 6 0-6-1-12 2-17l5-5z" class="L"></path><path d="M380 569l3 2-1 2-4-1h-1c0-1 0-2 1-2l2-1z" class="a"></path><path d="M376 576c-1 1-2 2-4 2h0l1-3c2-1 2-2 5-3l4 1 4 4 1 3c0 1 0 2-1 3h-1v-1c1-1 1-2 1-3l-2-2h-3l-1-1c-2-1-3-1-4 0z" class="O"></path><path d="M376 576c1-1 2-1 4 0l1 1h3l2 2c0 1 0 2-1 3v1h1v2h-4c-2-2-3-2-6-2-2 0-2 1-4 1l1-1 1-3c1-3 3-1 5-3-1-1-2-1-3-1z" class="V"></path><path d="M373 583l6-3c1 0 2-1 3-1l3 3v1h1v2h-4c-2-2-3-2-6-2-2 0-2 1-4 1l1-1z" class="k"></path><path d="M376 583c3-1 6-1 9 0h1v2h-4c-2-2-3-2-6-2z" class="o"></path><path d="M404 577c3 0 3 1 5 2l3 6c1-1 1-2 2-2l1 3 2-1v2c0 1 1 2 1 3v1h1l-1 1 1 2h1c2-2 2-3 3-5l1 1v4c0 8-4 16-9 22l-3 2v1c-1 1-1 1-1 2-1 0-1 0-1 1l-6 2-2-1h-3c-1-1-2-1-3-1h-1c0-1-1-1-1-1h-1l-1-1c-1-1-2-1-3-2h0c0-1-3-2-3-3v-1h-1l-2-4 1-1v-2c-2-4-2-8-1-12s3-6 7-8l12-3c-1-1-3 0-4-2 0-1 0-2 1-4l5-1z" class="m"></path><path d="M404 600c0 1 1 2 0 4h0c-3-1-5-1-7-1h-1l-3 3-1-1c1-2 2-2 4-3s3-2 5-1c2 0 2 0 3-1z" class="a"></path><path d="M389 601c1 1 0 1 1 1h1c2 0 3-2 4-2 2-1 4-1 6-2 1 1 2 1 3 2-1 1-1 1-3 1-2-1-3 0-5 1s-3 1-4 3l1 1-1 4c-1 1-1 2-1 3v2c0-1 0-1-1-2v-1-1c-1-1 0-1-1-2-1-2-1-3-1-5l1-3z" class="H"></path><path d="M386 600c0-4 3-7 5-9 2 1 2 0 4 0h2c3 0 6 0 8 2 2 1 2 2 3 4l-1-1c-1 0-3-3-5-3h-4c1 2 3 3 3 5-2 1-4 1-6 2-1 0-2 2-4 2h-1c-1 0 0 0-1-1l-1 3c-1-2-1-3-2-4z" class="F"></path><path d="M386 600c0-4 3-7 5-9 2 1 2 0 4 0h2c-3 2-7 4-8 9v1l-1 3c-1-2-1-3-2-4z" class="L"></path><path d="M415 586l2-1v2c0 1 1 2 1 3v1h-1v1c0 5-2 12-6 16-1 1-3 1-4 2h-1c-2 1-6 0-8-1v-1c2-4 6-2 9-5 1-1 2-2 2-4 1-2 1-4 2-6 0 1 1 3 2 4l1 1c1-1 2-2 2-4l-1-8z" class="N"></path><path d="M411 593c0 1 1 3 2 4-1 3-3 5-5 8l-1 1-1 2c-2 0-4 0-5-1l-1 1h0c2 1 4 1 5 1h2v1h-1c-2 1-6 0-8-1v-1c2-4 6-2 9-5 1-1 2-2 2-4 1-2 1-4 2-6z" class="a"></path><path d="M386 600c1 1 1 2 2 4 0 2 0 3 1 5 1 1 0 1 1 2v1 1c1 1 1 1 1 2v-2c0-1 0-2 1-3 0 3 1 5 4 7 1 1 2 1 3 1 5 1 9 1 13 0v1c-1 1-1 1-1 2-1 0-1 0-1 1l-6 2-2-1h-3c-1-1-2-1-3-1h-1c0-1-1-1-1-1h-1l-1-1c-1-1-2-1-3-2h0c0-1-3-2-3-3v-1h-1l-2-4 1-1 1 1c0-4 0-7 1-10z" class="q"></path><path d="M399 618c5 1 9 1 13 0v1c-1 0-2 0-2 1h-2c-2 1-3 1-6 1-2-1-2-1-3-3z" class="a"></path><path d="M386 600c1 1 1 2 2 4 0 2 0 3 1 5 1 1 0 1 1 2v1 1h-1v-1l-1 1 1 2h-1l-1-1c-1-1-2-2-2-4 0-4 0-7 1-10z" class="C"></path><path d="M404 577c3 0 3 1 5 2l3 6c1-1 1-2 2-2l1 3 1 8c0 2-1 3-2 4l-1-1c-1-1-2-3-2-4-1 2-1 4-2 6l-1-2c-1-2-1-3-3-4-2-2-5-2-8-2h-2c-2 0-2 1-4 0-2 2-5 5-5 9-1 3-1 6-1 10l-1-1v-2c-2-4-2-8-1-12s3-6 7-8l12-3c-1-1-3 0-4-2 0-1 0-2 1-4l5-1z" class="F"></path><path d="M391 591c4-3 8-4 13-3 3 0 5 2 7 5h0c-1 2-1 4-2 6l-1-2c-1-2-1-3-3-4-2-2-5-2-8-2h-2c-2 0-2 1-4 0z" class="Q"></path><path d="M404 577c3 0 3 1 5 2l3 6c1-1 1-2 2-2l1 3 1 8c-1-2-2-5-4-6-3-3-6-4-10-4-1-1-3 0-4-2 0-1 0-2 1-4l5-1z" class="W"></path><path d="M406 580h1c1 2 0 2 0 3h-1-2v-1l2-2z" class="M"></path><path d="M296 436l10 22 1-2c2 3 5 7 5 10h1l3 8c1 2 2 5 2 7 0 1 3 8 3 9l7 15v-5l-1-1-1-4v-1l7 14 1-1h1c0 2 1 3 2 4h1v-1h1c0-1 0-2 2-3 2 2 4 5 7 6 10 8 21 10 32 14l6 2c-1 0-1 1-2 2h-1l1 2h-2c0 1 0 1 1 1l2 2c0 1 0 1-1 1v4c1 1 2 1 2 3s0 3-1 4v1l-4 3v1l4-2c1 1 1 3 1 4l-3 8c-1 1-1 2 0 3-3 1-3 1-5 3h-1l-5 5c-3 5-2 11-2 17 0 3 0 5 1 8l-1 1c-1 0-1 1-1 2v3c1 4 3 7 5 11 1 3 2 6 4 8l5 12c-3-2-4-5-6-7h0v3 1l-1-3h-2c-1-1-1-4-2-5-3-7-6-15-10-22v4l-7-18-23-56 1-2-30-74c-3-5-5-10-6-14-1-3-2-4-1-7z" class="K"></path><path d="M365 598c1 2 3 4 3 6v4h1c1 4 3 7 4 11h0c1 2 1 4 1 6-4-4-4-10-6-15-1-5-3-7-3-12z" class="c"></path><path d="M345 552l1-1c1 2 2 4 4 6h1c4 3 5 13 6 18 1 2 2 4 2 7-3-6-5-12-7-17-1-2-3-4-3-5l1-2-2-2c-1-1-2-3-3-4z" class="R"></path><path d="M368 604l1 1c1 4 3 7 5 11 1 3 2 6 4 8l5 12c-3-2-4-5-6-7h0v3 1l-1-3c-1-1-1-3-2-5 0-2 0-4-1-6h0c-1-4-3-7-4-11h-1v-4z" class="b"></path><path d="M333 531l10 23 16 41c1 2 3 6 3 8v4l-7-18-23-56 1-2z" class="L"></path><path d="M296 436l10 22 1-2c2 3 5 7 5 10h1l3 8c1 2 2 5 2 7 0 1 3 8 3 9l7 15c3 5 6 11 8 17 0 1-1 1 0 3 0 0 0 1 1 1v2l-1 1c-1-1-1-2-2-3h0c1 4 3 8 5 13 1 1 2 3 2 5v1l1 2 2 5c1 4 10 24 9 25 0-1-1-1-1-2l-12-28c-1-4-3-7-4-10l-7-18c-1-2-2-5-2-7l-11-24-8-21c-1-3-3-9-5-10-3-5-5-10-6-14-1-3-2-4-1-7z" class="V"></path><path d="M307 456c2 3 5 7 5 10h1l3 8c1 2 2 5 2 7 0 1 3 8 3 9l-1 4c-2-4-3-8-5-12-1-3-3-7-4-11-1-5-3-9-5-13l1-2z" class="Y"></path><path d="M312 466h1l3 8c1 2 2 5 2 7-2-2-5-6-5-9-1-2-2-3-2-5l1-1z" class="l"></path><path d="M321 490l7 15c3 5 6 11 8 17 0 1-1 1 0 3 0 0 0 1 1 1v2l-1 1c-1-1-1-2-2-3h0l-3-5-2-7-6-13c-1-2-1-3-1-5l-2-2 1-4z" class="c"></path><path d="M330 512c1 2 2 4 2 7l-1 2-2-7s0-1 1-2z" class="b"></path><path d="M322 496c4 4 6 10 8 16-1 1-1 2-1 2l-6-13c-1-2-1-3-1-5z" class="g"></path><path d="M326 494l7 14 1-1h1c0 2 1 3 2 4h1l1 1c5 10 8 21 13 32 0 1 0 3 1 4l-2 2c0 1 0 2 1 2h1c1 1 3 2 5 3v1 2 1c2 1 4 3 4 5v2c0 1 0 1 1 2l-1 4c1 1 2 1 4 1 1 0 3 0 4-1l1 1s1 0 1 1c-3 5-2 11-2 17 0 3 0 5 1 8l-1 1c-1 0-1 1-1 2v3l-1-1c0-2-2-4-3-6l-6-16c0-3-1-5-2-7-1-5-2-15-6-18h-1c-2-2-3-4-4-6l-1 1h-1l-2-5-1-2v-1c0-2-1-4-2-5-2-5-4-9-5-13h0c1 1 1 2 2 3l1-1v-2c-1 0-1-1-1-1-1-2 0-2 0-3-2-6-5-12-8-17v-5l-1-1-1-4v-1z" class="Q"></path><path d="M334 526h0c1 1 1 2 2 3l1-1v-2c-1 0-1-1-1-1-1-2 0-2 0-3l9 20 4 10c1 2 1 3 2 5h-1c-2-2-3-4-4-6l-1 1h-1l-2-5-1-2v-1c0-2-1-4-2-5-2-5-4-9-5-13z" class="e"></path><path d="M333 508l1-1h1c0 2 1 3 2 4h1l1 1c5 10 8 21 13 32 0 1 0 3 1 4l-2 2-18-42z" class="I"></path><path d="M352 552h1c1 1 3 2 5 3v1 2 1c2 1 4 3 4 5v2c0 1 0 1 1 2l-1 4c1 1 2 1 4 1 1 0 3 0 4-1l1 1s1 0 1 1c-3 5-2 11-2 17 0 3 0 5 1 8l-1 1-18-48z" class="r"></path><path d="M339 510c0-1 0-2 2-3 2 2 4 5 7 6 10 8 21 10 32 14l6 2c-1 0-1 1-2 2h-1l1 2h-2c0 1 0 1 1 1l2 2c0 1 0 1-1 1v4c1 1 2 1 2 3s0 3-1 4v1l-4 3v1l4-2c1 1 1 3 1 4l-3 8c-1 1-1 2 0 3-3 1-3 1-5 3h-1l-5 5c0-1-1-1-1-1l-1-1c-1 1-3 1-4 1-2 0-3 0-4-1l1-4c-1-1-1-1-1-2v-2c0-2-2-4-4-5v-1-2-1c-2-1-4-2-5-3h-1c-1 0-1-1-1-2l2-2c-1-1-1-3-1-4-5-11-8-22-13-32l-1-1v-1h1z" class="C"></path><path d="M354 545v-6l2 2c0 1 1 2 1 3l-1 1h-2z" class="G"></path><path d="M338 510h1c3 4 5 7 9 10 1 1 3 3 5 4 1 1 2 1 2 2v1h1 0c0 3 4 6 6 8v1c-1 1-5-4-7-5-1 0-1-1-2-1l-3-3c-2-4-5-6-7-10-1-1-2-4-3-4l-1-1-1-1v-1z" class="J"></path><path d="M356 541c1 1 3 2 4 4 1 3 2 6 1 9-1 1-1 2-2 2v-1h-1c-2-1-4-2-5-3h-1c-1 0-1-1-1-2l2-2c-1-1-1-3-1-4l1 1h1 2l1-1c0-1-1-2-1-3z" class="S"></path><path d="M356 541c1 1 3 2 4 4 1 3 2 6 1 9h-1v-2c-2-2-3-5-4-7l1-1c0-1-1-2-1-3z" class="T"></path><path d="M355 531c2 1 6 6 7 5 3 2 6 5 7 8 2 4 5 7 7 11l-1 1-1-3c-1 1-1 2-1 3 0 0 0 1 1 2v1c-1 1-2 1-4 1l1-3h-2v-4-3c-1-3-3-6-6-7-3-4-6-8-8-12z" class="Y"></path><path d="M371 557c0-2 0-4 1-6h0l2 2c-1 1-1 2-1 3 0 0 0 1 1 2v1c-1 1-2 1-4 1l1-3z" class="H"></path><path d="M363 543c3 1 5 4 6 7v3 4h2l-1 3v4h-5l-1 1-1 3c-1-1-1-1-1-2v-2c0-2-2-4-4-5v-1-2-1h1v1c1 0 1-1 2-2 1-3 0-6-1-9 1-1 1-1 2-1 0 1 1 2 1 3 0 0 0 1 1 1l1-1v-1c-1-1-1-2-1-3h-1z" class="N"></path><path d="M363 556v-2c2-2 4-1 6-1v4c-1 1-2 3-3 4-1 0-2 1-3 0v-5z" class="U"></path><path d="M363 556l1-1v2c0 1 0 1 1 2 0 0 0 1 1 1v1c-1 0-2 1-3 0v-5z" class="n"></path><path d="M358 556c1 2 3 4 5 5 1 1 2 0 3 0 1-1 2-3 3-4h2l-1 3v4h-5l-1 1-1 3c-1-1-1-1-1-2v-2c0-2-2-4-4-5v-1-2z" class="B"></path><path d="M381 553l4-2c1 1 1 3 1 4l-3 8c-1 1-1 2 0 3-3 1-3 1-5 3h-1l-5 5c0-1-1-1-1-1l-1-1c-1 1-3 1-4 1-2 0-3 0-4-1l1-4 1-3 1-1h5v-4c2 0 3 0 4-1v-1c-1-1-1-2-1-2 0-1 0-2 1-3l1 3 1-1h1c2 0 3-1 4-1v-1z" class="D"></path><path d="M370 564c0 1 0 2-1 3h-3c0-1-1-2-1-3h5z" class="P"></path><path d="M367 569h1c1-1 2-2 4-2l2 1c-1 2-2 3-4 4h-2l-1-3z" class="H"></path><path d="M363 568l1-3 1-1c0 1 1 2 1 3h-1c0 1 1 2 2 2l1 3-2 1c-2 0-3 0-4-1l1-4z" class="h"></path><path d="M377 555c2 0 3-1 4-1v2h-2 0l-2 1v1c0 3 0 6-1 9l1 2-5 5c0-1-1-1-1-1l-1-1c-1 1-3 1-4 1l2-1h2c2-1 3-2 4-4 1-5 1-8 1-12l1-1h1z" class="X"></path><path d="M381 553l4-2c1 1 1 3 1 4l-3 8c-1 1-1 2 0 3-3 1-3 1-5 3h-1l-1-2c1-3 1-6 1-9v-1l2-1h0 2v-2-1z" class="m"></path><path d="M355 526l14 7c3 2 6 3 9 5 1 1 3 1 4 3h2c1 1 2 1 2 3s0 3-1 4v1l-4 3v1 1c-1 0-2 1-4 1h-1c-2-4-5-7-7-11-1-3-4-6-7-8v-1c-2-2-6-5-6-8h0-1v-1z" class="K"></path><path d="M382 541h2c1 1 2 1 2 3s0 3-1 4c-1 0-2-2-3-3l2-1v-1c-2 0-2 0-4-1l1-1h1z" class="e"></path><path d="M368 536c2 1 4 2 6 4 3 1 6 3 8 5 1 1 2 3 3 3v1l-2-1c-1 0-2 0-3-1-4-4-9-7-12-11z" class="Y"></path><path d="M362 536v-1c-2-2-6-5-6-8 4 3 8 8 12 9 3 4 8 7 12 11 1 1 2 1 3 1l2 1-4 3c-1 0 0-1-1-2s-3-2-3-3c-3-3-6-6-9-8-2-1-4-3-6-3z" class="g"></path><path d="M362 536c2 0 4 2 6 3 3 2 6 5 9 8 0 1 2 2 3 3s0 2 1 2v1 1c-1 0-2 1-4 1h-1c-2-4-5-7-7-11-1-3-4-6-7-8z" class="Q"></path><path d="M369 544c2 1 3 2 5 4 1 2 3 4 3 7h0-1c-2-4-5-7-7-11z" class="L"></path><path d="M339 510c0-1 0-2 2-3 2 2 4 5 7 6 10 8 21 10 32 14l6 2c-1 0-1 1-2 2h-1l1 2h-2c0 1 0 1 1 1l2 2c0 1 0 1-1 1v4h-2c-1-2-3-2-4-3-3-2-6-3-9-5l-14-7c0-1-1-1-2-2-2-1-4-3-5-4-4-3-6-6-9-10z" class="q"></path><path d="M380 527l6 2c-1 0-1 1-2 2h-1c-3 0-2-1-4-3l1-1z" class="a"></path><path d="M369 533h4c2 1 6 5 8 4l-1-2h1l3 2v4h-2c-1-2-3-2-4-3-3-2-6-3-9-5z" class="D"></path></svg>