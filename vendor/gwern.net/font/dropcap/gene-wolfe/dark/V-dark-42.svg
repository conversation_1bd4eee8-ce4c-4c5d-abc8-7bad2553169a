<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="138 72 766 928"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#640605}.C{fill:#9b0907}.D{fill:#cd120e}.E{fill:#19100f}.F{fill:#6a0705}.G{fill:#980705}.H{fill:#9f0908}.I{fill:#a80c0b}.J{fill:#da1511}.K{fill:#db2823}.L{fill:#8a0706}.M{fill:#e93b36}.N{fill:#c8110e}.O{fill:#f26f6c}.P{fill:#740605}.Q{fill:#362f2e}.R{fill:#cb1a18}.S{fill:#360504}.T{fill:#5f0505}.U{fill:#0c0708}.V{fill:#473e3d}.W{fill:#270504}.X{fill:#b40d0a}.Y{fill:#b20c0c}.Z{fill:#f56a67}.a{fill:#420405}.b{fill:#eb5c59}.c{fill:#830706}.d{fill:#443a37}.e{fill:#292423}.f{fill:#282322}.g{fill:#504845}.h{fill:#1f0203}.i{fill:#645b58}.j{fill:#392e2b}.k{fill:#5d5351}.l{fill:#f15953}.m{fill:#f38d8d}.n{fill:#f3aaa8}.o{fill:#c0b3b0}.p{fill:#d3c5c1}.q{fill:#f09c9a}.r{fill:#f48281}.s{fill:#e2beba}.t{fill:#908683}.u{fill:#a99f9b}.v{fill:#efe0dd}</style><path d="M407 804l2 1 1 1c-1 1-1 2-2 3l-1-1c-1-2-1-2 0-4z" class="C"></path><path d="M551 549l1-3h1 1v5h-3v-2z" class="V"></path><path d="M351 559h0c1 0 2 1 3 2l-1 2h-1-1c-1-1 0-3 0-4z" class="C"></path><path d="M682 611l1 2c0 2 0 3-1 3l-2-1v-3l2-1z" class="D"></path><path d="M655 203l2 1 1 3c-1 1-2 3-3 5 1-2 1-3 1-5l-2-1 1-3z" class="i"></path><path d="M565 233v6l-1 1c-1-1-2-1-1-2v-1-3c1 0 1 0 2-1z" class="p"></path><path d="M551 555h3l-1 6v1l-1-1c0-2-1-4-1-6z" class="d"></path><path d="M508 292h1l2 2v5l-1 1-2-8z" class="p"></path><path d="M416 225v-1l1-1c1 1 2 0 4 1l-2 3 3 3c-2 0-5-4-6-5z" class="Q"></path><path d="M554 551c1 0 1 0 2 1l-2 2c-1 0-3 0-4-1v-1l1-1h3z" class="o"></path><path d="M513 647l1 1 1 3-1 3c0 3 0 5-1 7-1 0 0 0-1-1v-2c1-1 1-2 1-3v-1c0-1 0-1 1-2 0-1-1-4-1-5z" class="Q"></path><path d="M534 649h3l1 1v2c-2 0-4 0-5-1h-1v-1l2-1z" class="o"></path><path d="M690 205c2 1 3 2 3 4v3c-2 1-2 1-4 0 1-1 2-2 2-3-1-1-1-3-1-4z" class="U"></path><path d="M591 793v5h0c-1 7 1 15-1 22v-24l1-3z" class="S"></path><path d="M422 263l5-1 1 1-2 1h0v1h1l-2 2c-1-1-1-1-2-1h-2v-1s1 0 2-1l-1-1z" class="U"></path><path d="M766 263c4 0 8 1 11 0l-2 2h-8 0l-1-2z" class="s"></path><path d="M840 318h2c1 1 1 1 1 3l-1 1h-2c-1-1-1-1-1-3l1-1z" class="Z"></path><path d="M590 796c0-11-1-21 1-31 0 3 1 7 0 9v1 3l-1 1c0 4 0 10 1 14l-1 3z" class="E"></path><path d="M533 651c1 1 3 1 5 1l-3 7-2-8z" class="j"></path><path d="M591 775l1 27c0 4 0 8-1 11v-15h0v-5c-1-4-1-10-1-14l1-1v-3z" class="W"></path><path d="M464 298c0 5-2 10-4 15v-3c1-1 0-1-1-2 1-3 4-8 5-10z" class="E"></path><path d="M491 634l3 1s1 1 1 2c-1 1-2 2-2 4l-1 1c-1-2-2-3-3-5v-1l2-2z" class="t"></path><path d="M441 418l1-2h-1 0l1-2h1c0-1 1-2 1-2h1c-2 6-5 13-8 19 1-4 2-7 3-10 0-1 1-2 1-3z" class="U"></path><path d="M619 217l6-1 1 3c-1 2-3 4-5 5v-1c0-1 1-2 2-3v-2c-2 0-3 0-4 1v-2z" class="E"></path><path d="M333 583c2 1 2 2 2 4v16h0c-1-7-2-13-2-20z" class="X"></path><path d="M296 447c0 2 1 3 1 4v1c1 3 1 6 3 8l-1 2-1-1c-3-5-3-9-2-14z" class="l"></path><path d="M535 320h0c2 2 0 26 0 30h-1l1-30z" class="U"></path><path d="M685 439c1-2 3-4 5-6-2 6-2 10-1 15l-1 1c0-2 0-3-1-5v-2c0-2-1-2-1-3h-1z" class="G"></path><path d="M594 466v1c0 3-2 4-3 6l-3-5v-3h1l1 1 1-1c1 1 1 1 3 1z" class="U"></path><path d="M696 159c3 3 5 7 7 11h-1c-3 0-5-4-6-7l-2-3c1 0 1 1 2 1v1c0 1 1 2 2 2 0-1 0-1-1-2 0-1-1-1-1-3h0zm-78 150h5l1 1h1c0 1 1 2 1 3-1 1-2 1-3 2s-1 0-1 1l-4-7z" class="q"></path><path d="M684 161c2 2 2 5 4 8l2 7-2 1c-1-5-4-11-6-16 1 2 2 2 2 4h1c0-2-2-2-1-4z" class="s"></path><path d="M514 646l2 1h1c1 1 2 2 2 3 0 2-3 6-4 8h0l-1-4 1-3-1-3v-2z" class="i"></path><path d="M514 646l2 1h1c-1 2-1 3-2 4l-1-3v-2z" class="k"></path><path d="M760 261l6 2 1 2h0 8l-4 1c-1 0-1 0-2 1l-12-3c1-1 1 0 2-1h4c-2 0-2-1-3-2z" class="q"></path><path d="M409 300l6 1c-4 2-6 4-9 7l-1-1-1-1-1-1c2-2 4-3 6-5z" class="Z"></path><path d="M710 396c4-4 6-6 11-7l-11 11h-1c0-2 1-3 1-4z" class="J"></path><path d="M697 480c2 0 3 0 4 1h6v2h1l1 1c-2 2-3 5-5 7 0-1 1-3 0-5-1-3-4-4-7-6z" class="M"></path><path d="M387 210l5 7c2 3 5 5 7 7 1 2 3 4 4 6v1l-1-1c-1 0-1 0-2-1v-1c-1-1-2-1-2-2-3-1-2-2-3-4l-1-1-3-4c-2-2-4-4-4-7z" class="u"></path><path d="M834 300l2 1 1 4c0 1 0 2 1 3h0c-1 4-2 7-4 10-1 1-4 6-6 6 3-5 7-8 8-14 0-3-1-5-3-7v-1c0-1-1-1 0-2h1z" class="R"></path><path fill="#807572" d="M489 258c1 1 2 3 3 4l-1 1c-2 1-5 2-5 5-1 3 0 5 1 8h0c-3-2-3-6-3-10 0 0 0-1 1-2h0l4-6z"></path><path d="M535 262l3-4c2 3 4 6 5 9-1 3-1 5-2 8l-2 3c0-5 3-10 0-14-1-1-2-1-3-2h-1z" class="t"></path><path d="M296 447v-1c1-4 4-8 7-10l1 1c-1 1-1 1-1 2-3 2-4 6-4 10v3h-2v-1c0-1-1-2-1-4z" class="Z"></path><path d="M581 357c-5-7-9-18-11-26 4 7 8 15 11 22l-2-2c1 2 2 4 2 6z" class="j"></path><path d="M461 330c1 4 1 8 0 12l-3 6v1h-2c0 1 0 2-1 3l-1-1 7-21z" class="E"></path><path d="M526 279v-1c2 1 3 2 4 3 0 1 0 0 1 1v-3l1 1v7c1 6-1 9-4 13 1-5 3-10 2-15 0-3-2-5-4-6h0z" class="o"></path><path d="M286 248c0 1 1 2 1 4l-4 4-3 2c-1-1-3-1-6-1 5-3 8-6 12-9z" class="Z"></path><path d="M150 265c-1-1-2-3-3-5-3-5-6-12-4-18 1 7 2 11 7 16-1 2 0 5 0 7z" class="J"></path><path d="M462 472c1 5 0 10 2 14v7c0 5 0 11-1 16h-2c1-4 1-9 1-13v-24z" class="e"></path><path d="M459 308c1 1 2 1 1 2v3c0 2-1 4-2 5-2 2-3 5-4 7l-2 2v-1c1-2 2-4 2-6v-2l5-10z" class="U"></path><path d="M251 393c3-5 7-7 12-9l1 2c-1 1-3 2-5 3-3 2-5 4-8 6h0l-1-1 1-1z" class="E"></path><path d="M721 467c2 2 5 4 5 7s-6 8-8 10c2-3 4-6 4-10-1-2-2-3-3-4l2-3z" class="Z"></path><path d="M678 225c-2 0-3 0-4 2h-1c-2 1-3 2-4 3h-1l3-3c1-1 2-1 3-2s3-2 4-4c1-1 2-1 3-2l1-1c1-1 3-2 4-2 0 2-1 4-3 6l-1 1c-2 1-3 1-4 2zm-318-1l-2-2-2-2c0-1-1-1-1-2-1-1-1 0-1-1-1-1-1-2-2-2 0-1-1-1-1-2-1-1-1-2-2-3l-1-1-1-1-1-1c-1-1-1-3-2-4 1-1 1-1 1-2l2 1-1-3v-1c1 1 1 1 1 2 1 1 0 0 1 2h1v1c1 2 1 2 1 3l-1 1h0c-1-1-2-2-3-4h0 0c0 1 0 2 1 3l1 1c1 2 2 3 4 4v1c1 1 1 1 1 2l1 1c2 3 4 6 7 9h-1z" class="E"></path><path d="M197 455h0c1 2 1 4 1 6h0v-1l1-3c0 7 1 12 3 18l3 6c-4-2-8-10-9-15h0c2-3 1-9 1-11z" class="R"></path><path d="M380 196l7 13v1c0 3 2 5 4 7h-1c-2-1-3-2-4-3s-2-3-3-4l-2-3h1 1l-3-10v-1z" class="V"></path><path d="M690 556v-39-7l1 32c0 9 1 19 0 28l-1-25v3 7 1z" class="F"></path><path d="M512 320c0-2 0-4 1-6 0 1 0 1 1 2l-1 37c-1-3-1-7-1-10v-7-16z" class="v"></path><path d="M825 460c1 0 1-1 2-1 0 1 0 2 1 4h1c-2 7-6 13-10 19 1-2 2-5 3-7 2-5 3-10 3-15z" class="D"></path><path d="M688 449l1-1c2 5 5 9 10 11 1 1 3 1 4 2h-1c1 1 2 1 3 2l-2 2h-1c0-1 0-1 1-2-2-1-4-1-6-1h-1c-2-1-6-6-7-8 0-2 0-4-1-5z" class="C"></path><path d="M440 278l1 1c-5 5-12 11-12 19 0 3 1 5 1 8l-4-5c0-1 1-3 0-5 1-1 1-2 1-3l6-8 7-7z" class="r"></path><path d="M374 670v11 37c0 4-1 11 0 14l2 2c0 1-1 1-2 3h0c-1-1-1-1-1-2-1-1-1-1 0-2 1-4 0-9 0-13v-31c0-6-1-13 1-19z" class="D"></path><path d="M665 226c-1 2-3 4-5 4l-1 1-2 2c-1 0-2 2-3 3h-1v1h0c0 1-1 0-1 0h-1c-1 1-1 1-2 1h0l-1-1 3-3c1 0 2-1 3-2h1l2-1v-1c1-1 1-1 2-1l1-2c1-1 3-2 4-3l3-3 1 1-3 4h0z" class="j"></path><path d="M516 627h1 0c0-1 0-2 1-2v-1-2-1c1-1 1-1 1-2v-1c0-2 0-3 1-4v-4c1-1 1-3 1-5v-6l1 2c-1 15-6 30-6 46l-2-1c1-4 0-8 0-12 2-2 2-4 2-7z" class="i"></path><path d="M303 436h4c1 1 2 2 2 4h-1 0c-2 1-3 1-4 2 0 1-1 2-1 3l-1 3c0 1-2 2-2 4v3l-1-3v-3c0-4 1-8 4-10 0-1 0-1 1-2l-1-1z" class="J"></path><path d="M441 357c1-5 5-10 8-15 2-5 4-11 7-15-1 4-2 9-4 14-1 1-1 3-2 5h-1-1c0 1-1 2-1 3-1 4-3 6-6 8z" class="e"></path><path d="M616 745c1 1 0 8 0 10l1 30c0 7-1 14 0 20 1 2 0 3-1 4l-1-1c-1-2 1-4 1-6v-38c0-6-1-13 0-19z" class="X"></path><path d="M595 354c4 3 7 7 10 11 5 4 10 7 15 11-9-3-16-9-22-16-2-1-4-3-6-5l1-1 1 1 1-1z" class="j"></path><path d="M269 380c2 1 2 0 4 0v2h-2l5 1c-4 0-9 0-13 1-5 2-9 4-12 9l-1-1c2-3 4-4 5-6 1-1 1-2 2-3l1-1 11-2z" class="K"></path><path d="M597 378c4 5 8 10 13 14 2 2 6 4 8 6-8-2-15-8-20-15h0c0-2-1-2-1-3v-1c1 0 0 0 0-1z" class="j"></path><path d="M422 263l1 1c-1 1-2 1-2 1v1h2c1 0 1 0 2 1s1 1 1 2l1 1c-1 0-1 0-2 1l-1 1h-1l1-1c-1 0-1 0-2 1h-1c0 1 0 0-1 1s-2 1-4 1c-1 1-2 1-3 1h0c2-1 3-2 4-4l4-2v-1h-4-1-3v-2h2c2 0 3 0 5-1v-1h0l2-1z" class="f"></path><path d="M417 268c2-1 4-1 6-1l2 2-8 5h-1c-1 1-2 1-3 1h0c2-1 3-2 4-4l4-2v-1h-4z" class="e"></path><path d="M548 231c1 1 1 1 2 1 1-3 3-4 5-6 2 1 3 1 5 2h1c2 2 3 3 4 5-1 1-1 1-2 1-2-2-3-3-6-3-2 0-4 1-5 2l-3 3c1 1 1 1 0 2v-3l1-1c-1-1-1-1-2-1-1 1-1 2-2 3v-3l2-2z" class="v"></path><path d="M649 666h1v17 31 14 5h1v1 1c0 1 0 2-1 2h-1l-1-1c-2-2 0-2 1-4v-4-11-51z" class="N"></path><path d="M871 262h0c7-7 10-12 10-22 2 3 1 8 0 11-1 7-4 12-10 16l-1 1v-1c-1 0-1 0-2 1v-1l1-1v-1c1-1 2-1 2-3z" class="D"></path><path d="M407 804h0c1-9 0-19 0-29l1-42v45 18c0 3 0 6 1 9l-2-1z" class="F"></path><path d="M581 357c0-2-1-4-2-6l2 2c6 9 10 17 16 25 0 1 1 1 0 1v1c0 1 1 1 1 3-7-8-12-17-17-26z" class="V"></path><defs><linearGradient id="A" x1="422.441" y1="223.111" x2="411.423" y2="217.21" xlink:href="#B"><stop offset="0" stop-color="#322a2a"></stop><stop offset="1" stop-color="#625653"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M409 217c7 1 21 2 26 9-1 0-2 0-2-1-1 0-2-1-2-1l-1-1h-1 0l-1-1h-1 0-1c-1-1-1-1-2-1-1 1 0 1-1 2h0c0 1-1 1-1 1h-1c-2-1-3 0-4-1l-1 1v1c-3-3-5-5-7-8z"></path><path d="M346 532v2 2c-1 4-2 7-2 11-1 7-2 62 0 65v1l-1 3h-1v-1c-2-2-1-2 0-4v-3-41l1-11c0-6 0-13 2-19l1-2v-3z" class="D"></path><path d="M447 328c9-15 16-32 22-49 0 7-3 13-5 19-1 2-4 7-5 10l-5 10-6 10h-1z" class="g"></path><path d="M350 221l-3-4-6-12c4 2 7 6 9 9l7 8h-1l6 6-1 1-2-2c-4-2-6-4-9-6z" class="f"></path><path d="M150 265c0-2-1-5 0-7 2 3 5 5 8 7l-1 1c3 2 6 2 9 4 1 0 2 3 3 4-7-2-13-4-19-9z" class="K"></path><path d="M555 208v1c1 1 0 3 0 5-1 1-2 5-1 6h1v-2-2h1v-3c0-1 0-1 1-2v-2l1 1h0c-1 5-2 9-2 14l5 4h-1c-2-1-3-1-5-2-2 2-4 3-5 6-1 0-1 0-2-1 3-7 5-15 7-23z" class="o"></path><path d="M688 177l2-1 3 8c1 4 2 9 2 12-1 2-2 4-4 6-1-1-1-1-2 0-1 0-1 0-2 1l-1-1h1l5-5c1-1 1-2 1-4-1 0-1-1-2-1-1-1-2 0-2 0h-1l1-1c2-5 1-10-1-14z" class="p"></path><path d="M376 168l-1-8c0-6 0-13 1-19 0-1 0-3 1-4h1c1 3 2 8 1 11v4 1l-1 1v5 1l1 1h0v3l1 2h-1c-1-1-1-1-1-2v-2c0 1 0 2-1 3v-1 1h0l-1 3z" class="E"></path><path d="M149 303l2 1c-9 9-13 20-13 33v5c-2-4-2-9-3-13h1 1l1-8c2-8 5-13 11-18z" class="X"></path><path d="M649 666v-18-30-19-10c1 2 1 5 1 8v13 40c0 5 1 11 0 16h-1z" class="E"></path><path d="M143 267l2 2c3 2 9 6 13 7h8v1l-6 3h0c-1-1-5-2-7-2-5-2-10-5-13-9l3-2z" class="a"></path><path d="M608 296c3 0 4 1 6 2h1c3 3 6 4 8 8 0 1 1 2 2 4h-1l-1-1h-5c-3-3-6-6-9-8l3-3-4-2z" class="O"></path><path d="M858 273l-1 2c1 1 3 1 4 2 6-1 13-5 18-8l2-2c0 1 0 2 2 2-5 6-13 9-20 10-1 0-2-1-3-1-2-1-5-2-6-4l2-1h2z" class="a"></path><path d="M336 445v1c1 4 0 7-2 10-3 3-5 4-8 6-2 0-4 0-6 1v2h1l-3 1-5-1-1-1v-1h3c1-1 3-1 4-1h1l1-1h-3 0c3 0 6-1 8-3 6-3 8-7 10-13z" class="D"></path><path d="M315 463h5v2h1l-3 1-5-1-1-1v-1h3z" class="I"></path><path d="M191 296h1 3c4 0 10 3 14 5h0v1c-5-2-11-4-16-2-2 1-4 4-5 6-1 4 1 9 3 12 2 2 4 5 6 7-4-2-7-7-9-10l-2-7 1-1v-1c1-2 1-3 1-4v-1c1-1 1-2 2-3l1-2z" class="J"></path><path d="M158 265c12 5 24 5 37 5-5 2-11 1-16 3l-7 2h-2c0-1 0-1-1-1-1-1-2-4-3-4-3-2-6-2-9-4l1-1z" class="O"></path><path d="M584 278h1l3 3 7 8c1 1 3 3 4 5 0-1 0-1 1-2l2 3c1 1 1 2 3 2 1 0 2-1 3-2v1l4 2-3 3c-2-1-3-2-4-2h-4l-7 7c1-3 3-6 3-9 0-8-8-14-13-19z" class="Z"></path><path d="M600 292l2 3c1 1 1 2 3 2l-1 1h-3c-1 1-1 1-2 1v-2-3c0-1 0-1 1-2zm109 108h1c-5 8-11 15-16 23-1 3-3 7-4 10-2 2-4 4-5 6 0-2 2-7 3-9 4-8 8-14 13-21 3-3 5-6 8-9z" class="M"></path><path d="M441 357c3-2 5-4 6-8 0-1 1-2 1-3h1c-3 7-5 13-9 20-6 9-14 18-22 25h0c2-4 6-7 8-10 5-5 8-10 11-16 1-2 3-5 4-8z" class="V"></path><defs><linearGradient id="C" x1="661.558" y1="188.709" x2="659.714" y2="204.204" xlink:href="#B"><stop offset="0" stop-color="#2f2929"></stop><stop offset="1" stop-color="#5a524f"></stop></linearGradient></defs><path fill="url(#C)" d="M655 203l6-12c2-5 3-11 5-16v-2h0v3c0 1-1 2-1 3 0 2-1 4-1 6l-1 1-1 3h1 1 1 0c0-2 1-3 1-4l1-2v-2l1-1v-1c0-1 0-2 1-3v1 2 1c-2 9-7 19-11 27l-1-3-2-1z"></path><path d="M635 209h0c0 4-5 8-7 11 2 0 4-3 6-5v-1l1-1 3-3-1 3c-2 2-4 4-6 7-6 6-13 11-22 14v-1l1-1c4-1 8-5 11-8 2-1 4-3 5-5 1-1 2-1 3-2h0c1-1 2-2 2-3h1l1-2c1-1 1-2 2-3z" class="Q"></path><path d="M869 301v-1l1-1c9 7 14 15 16 26v3h1v-4l1 2c1 5-1 12-3 16v-14c-2-11-7-20-16-27z" class="X"></path><defs><linearGradient id="D" x1="430.506" y1="409.385" x2="442.994" y2="406.115" xlink:href="#B"><stop offset="0" stop-color="#121512"></stop><stop offset="1" stop-color="#241c1e"></stop></linearGradient></defs><path fill="url(#D)" d="M449 381h1v1c-1 1-2 2-2 4h0 0c-4 6-6 14-9 20l-6 12c-1 2-2 4-2 6l-1-1c0-2 1-4 2-6l5-11c1-3 1-5 3-8 0-1 0-1 1-1v-1-2l1-1v-2h1v-1c0-1 1-2 2-3s2-4 4-6z"></path><path d="M613 265c2-1 3-1 4-1h1s1 0 2-1c2-1 5 0 7 0v-1h-4-3 0l-1 1c-1 0-3-1-5-1-4 0-7-1-11-2h0 1c5 1 10 1 14 0 1 0 4 0 4 1 3-1 7-1 10-1l5 2h-3v2h-1c-3 0-5 1-8 2-1-1-3-1-4 0h-3l-1-1-1 1-3-1z" class="Q"></path><path d="M622 261c3-1 7-1 10-1l5 2h-3v2h-1c-2-2-3-3-6-3h-4-1z" class="s"></path><path d="M415 295c1 0 6-2 7-3 2 0 4 1 5 1 0 1 0 2-1 3 1 2 0 4 0 5h-1c-4-2-7-1-10 0l-6-1 2-2c1-1 3-2 4-3z" class="l"></path><path d="M415 295c1 0 6-2 7-3 2 0 4 1 5 1 0 1 0 2-1 3-4-1-8 0-12 2h-3c1-1 3-2 4-3z" class="D"></path><defs><linearGradient id="E" x1="594.946" y1="413.191" x2="601.554" y2="398.809" xlink:href="#B"><stop offset="0" stop-color="#070d0f"></stop><stop offset="1" stop-color="#332420"></stop></linearGradient></defs><path fill="url(#E)" d="M580 372c2 1 7 14 9 18 3 5 6 11 10 16 4 6 8 11 12 18-4-3-7-7-10-10-9-13-15-27-21-42z"></path><path d="M577 326h1c4 4 6 9 9 13 2 2 3 4 5 6l3 3c8 7 15 14 22 20-2-1-5-2-7-3-4-3-8-6-11-10-1-1-3-2-4-3 0 0 0-1-1-1 0-1-1-1-1-2-1-2-3-4-4-6l-4-6c-1 0-1-1-2-2 0-1 0 0-1-1s-1-3-2-4h-1l-2-4z" class="E"></path><path d="M735 245c8 8 15 13 25 16 1 1 1 2 3 2h-4c-1 1-1 0-2 1-4 0-10-3-13-6-4-2-5-5-7-9-1-2-2-3-2-4z" class="m"></path><path d="M300 455v-3c0-2 2-3 2-4l1-3c0 5 0 8 4 12 3 2 7 3 11 4h0 3l-1 1h-1c-1 0-3 0-4 1h-3v1l1 1c-1 0-2 0-2 1-5-3-9-6-11-11z" class="C"></path><path d="M354 215c1 0 1 1 2 2 0 1 1 1 1 2l1 1c1-1 1-2 1-3l-2-2s-1-1-1-2l-1-1h-1l-2-2h1 0v-1c-1-1-1-3-2-4v-1l11 15 9 11c1 1 2 2 3 4 1 1 2 1 3 2h-2c0 1 1 2 2 2-2 0-5-3-6-4-2-2-5-6-8-7l-3-3h1c-3-3-5-6-7-9z" class="Q"></path><path d="M697 470h6l1-1c1 0 2 0 3-1l5 5c0 4-1 7-3 11l-1-1h-1v-2h-6c-1-1-2-1-4-1h0 0v-2h2-1v-1h1c1 0 2 0 4 1h0v-3h-2c2-1 3 0 5 0h0l1-1c0-2-1-2-2-3-3-1-6-1-8-1z" class="N"></path><path d="M705 471c2 1 4 1 5 3 1 1 1 2 0 4l-1 1h-1l-1-1c-1-1-3-2-4-3h-2c2-1 3 0 5 0h0l1-1c0-2-1-2-2-3z" class="W"></path><path d="M672 217v-2c0-1 2-2 3-3 0 0-1-1 0-2s1-2 1-3l4-5 1-3 3-3v1l-3 6c-1 2-3 3-4 6 2-2 3-3 4-5 0-1 1-2 2-3 1 2 0 3 0 4-1 2-2 3-3 4-1 2-1 3-2 5 0 1-2 3-2 5-1 0-1 1-2 1 0-1 1-1 1-2l-3 2c0 1-1 1-1 2l-1 1h-1 0c-1 1-2 2-4 3h0 0l3-4-1-1c1-1 3-3 5-4z" class="E"></path><path d="M672 217v-1c2-1 4-4 7-5l-6 8c-1 0 0 0-1 1 0 1-1 1-1 2l-1 1h-1 0c-1 1-2 2-4 3h0 0l3-4-1-1c1-1 3-3 5-4z" class="f"></path><path d="M521 283c0-2 2-3 3-4 0-1 1-1 1-2l-1-1c-1-1-1 0-1 0-1-1-1-2-2-2 0 0-2-1-3-1-1-1-1-2-1-3 1-2 2-2 3-2 1-1 1-1 2-1 1 2 2 2 4 3l1-1h1 1c0 2 0 3 1 5 1 1 2 3 2 4l-1 1v3c-1-1-1 0-1-1-1-1-2-2-4-3v1c-2 1-3 2-4 3l-1 1h0z" class="d"></path><defs><linearGradient id="F" x1="654.608" y1="188.413" x2="650.392" y2="205.587" xlink:href="#B"><stop offset="0" stop-color="#322826"></stop><stop offset="1" stop-color="#5c524f"></stop></linearGradient></defs><path fill="url(#F)" d="M653 192l3-8c1-4 2-10 5-13 0 2-1 3-1 5 0 1-1 1-1 2v2c-1 1-1 2-1 4v1h-1v2l-1 2h2c0-1 1-2 1-3v-1l1-1v-2l1-1c0-1 0-3 1-4v-1l1-1v-1 1 2c-3 7-5 14-8 22-2 3-4 7-6 10v-2h0c-2 0-3 0-4 1l7-11c1-2 2-3 1-5z"></path><path d="M596 223c7-4 15-5 23-6v2l-6 3c-2 2-8 8-10 7l-1-1c0-1-1-3-3-4 0-1-2-1-3-1z" class="Q"></path><path d="M297 452h2l1 3c2 5 6 8 11 11l-1 1-3 1c-2 0-4 2-5 3-1 2-1 3-1 5v1c1 3 3 5 5 7-3-1-7-6-9-9 0-5 4-6 6-9l-3-3-1-1 1-2c-2-2-2-5-3-8z" class="O"></path><path d="M300 460l1 1 2 1v1h-1-2l-1-1 1-2z" class="b"></path><path d="M476 338v-1c1-4 1-8 1-12 0-3 0-5-1-8h1c0 2 0 3 1 5h-1c0 2 0 3 1 5 0 1 0 10-1 12v4c0 1 0 1 1 3 0 0-1 4-1 6 0 7 1 15 0 23l-1 45h0v-14-48c1-7 0-14 0-20z" class="E"></path><path d="M640 206l8-20c0 1-1 3-1 5-1 2-2 4-3 7h2 0 1l1 1 1-2v-1l1-2c1-1 1-3 2-3l1 1-1 1-1 2v1c-1 0-1 1-1 2v-1c1-2 2-3 3-5h0c1 2 0 3-1 5l-7 11c-2 3-5 5-8 7h-1l2-2h-1l1-3 2-4z" class="Q"></path><path d="M640 206h3c-1 2-3 5-5 7h-1l1-3 2-4z" class="g"></path><path d="M620 213c1-3 2-5 3-7l3-11 1-3 1-4 2-6v-2-2c1-1 0-3 0-4 1-4 1-7 1-10 0-1-1-2 0-3 2 2 1 7 1 10v1c0 2 0 6-1 8 1 1 1 2 1 3-1 1-1 2-1 3v1h-1v2c-1 3-1 5-2 8-1 1-1 2-1 3-1 1-1 2-1 3l-1 2c-1 3 0-1-1 1v2l-1 2c0 1-1 1-2 2h2 1 0c0-1 1-2 1-3 1-1 0-3 1-4l1 2c1-2 0-4 3-5 0 1 0 1-1 1 0 2-1 3-1 4l-1 2c-1 1-1 2-1 4h1 2v2h0v2c-1 1-2 1-3 2l-1-3 2-2h0c-2-1-4-1-7-1z" class="f"></path><path d="M592 355c-1-1-3-5-4-6-9-11-17-24-23-37-1-3-2-6-4-10l-3-9 1-1 4 12 1 2 1 3v1c2 0 2 0 2 1v1 1l2 4c1 0 1 0 1 1 2 3 3 6 5 9 6 10 12 19 20 27l-1 1-1-1-1 1z" class="d"></path><path fill="#f0ece9" d="M511 277c0 3 0 6 1 9v-6l1-1v16h0c0 6 0 12 1 17 1-11 1-20 7-29h0c-5 11-5 22-7 33-1-1-1-1-1-2-1 2-1 4-1 6-1-3 0-6-1-9 0-4-1-8-1-11l1-1v-5c0-4-2-8-3-11l2-2c1-1 1-2 1-4z"></path><path d="M511 277c0 3 0 6 1 9v16l-1-3v-5c0-4-2-8-3-11l2-2c1-1 1-2 1-4z" class="d"></path><path d="M333 583l-1-17v-2c1-3 0-6 0-8 0-8 0-16 1-23v20c0 7 0 16 2 24 0-21 2-45 18-61-4 7-9 13-11 21-6 16-6 33-7 50 0-2 0-3-2-4z" class="H"></path><defs><linearGradient id="G" x1="638.51" y1="144.855" x2="654.49" y2="164.645" xlink:href="#B"><stop offset="0" stop-color="#141416"></stop><stop offset="1" stop-color="#362a26"></stop></linearGradient></defs><path fill="url(#G)" d="M645 160l2-23h0c2 2 2 4 2 6v18c-1 9-3 18-5 26-2 6-4 12-7 17h0v-1l1-2v-2l1-1v-1c1-2 1-4 2-5 0-2 1-4 2-6v-2l1-1v-2l1-5c1-5 1-11 0-16z"></path><path d="M522 438v-45-23c0-3 0-6 1-8l-1-2h-1v-1c-1 1-1 3-1 4v1h-1v-7-2-1-2l1-1v-1-4l1-4c1 1 1 1 1 2v4 1h0c0-1 0-1 1-2 0-4 0-9 1-13l1-1h0v4 4c-1 4-2 8-2 12v58 18 9h-1z" class="U"></path><path d="M511 256v1l1 19c0-2 1-3 1-5v-4 20c0 3 1 6 0 8h0v-16l-1 1v6c-1-3-1-6-1-9 0 2 0 3-1 4l-2 2-1-2c-1-2-2-6-4-8l-1-3h0c0-1-1-1-1-2h3l1-2 5 2h1v-12z" class="o"></path><path d="M502 270c1 1 3 1 4 1s4 0 5 1v5c0 2 0 3-1 4l-2 2-1-2c-1-2-2-6-4-8l-1-3z" class="Q"></path><path d="M502 270c1 1 3 1 4 1 0 2 0 3 1 5 1 1 1 2 1 4 0 1 0 1-1 1-1-2-2-6-4-8l-1-3z" class="g"></path><path d="M497 269h1 1c1 2 2 3 3 5 1-1 1 0 1-1 2 2 3 6 4 8l1 2c1 3 3 7 3 11l-2-2h-1c-1-3-2-7-4-10-1-2-3-1-5-1-1 0-2 1-2 2-3 6-1 13 2 18l-4-4c-2-3-2-7-1-11 0-2 3-8 5-10 1 0 2 2 3 2-2-2-3-6-5-9z" class="o"></path><path d="M405 216h0c-1 2 0 4 1 6h0v1l-2-1-2-2c-1-1-2-1-2-3-1-1-1 0-1-1h-1l-1-2c1-1 1-1 3-2h2c-1-1-2-2-2-3l-2-5v-2c0-2-2-5-2-7h0c-1-1-1-2-1-3l-1-2 1-1-1-1c0-1-1-2-1-4-1-1 0-1 0-2-1-1-1-1-1-2 1-2 1-3 1-5l-1-1c0-5 0-11 1-16 0-2-1-2 1-4h0v3h0c-1 2 0 4 0 5 0 5-1 9 0 13v4c1 1 1 2 1 3v1c0 2 1 4 1 6l1 3 1 3 2 5 2 7v1l-2-2h0l1 3c0 1 2 2 3 3h1c-3 1-5 1-7 2l7 2z" class="Q"></path><path d="M703 461h5c3 0 7-2 9-5 2-2 4-6 3-9 0-2-1-3-2-5-1-1-2-1-3-2l1-2c4 2 5 3 8 7 1 3 1 7-1 10-2 4-5 8-10 9v1l-1-1h-4 0v-1h0-3c-1-1-2-1-3-2h1z" class="D"></path><path d="M412 260h8 0c-2 1-7 1-10 2h-1 0c-2 1-3 0-4 1l1 1 1-1h1 0 1c1 0 1 1 2 0 2 0 1 0 3 1h6v1c-2 1-3 1-5 1h-2v2l-2 1c-1-1-1-1-2-1h-2-1-1 0c-1 1-1 1-2 1h0c-1-1-2-1-3-1h-2c1-1 2-1 3-1v-1-1h-1-5l-1-1c-1 0-2 0-3-1 3-1 5-2 8-2 2-1 5-1 7-1h6z" class="Q"></path><path d="M401 266c2 0 2-2 4-1h1 1 1 3l1 1c-1 0-1 0-3 1-1 0-1 0-2 1h-1-1 0c-1 1-1 1-2 1h0c-1-1-2-1-3-1h-2c1-1 2-1 3-1v-1z" class="d"></path><defs><linearGradient id="H" x1="436.09" y1="274.954" x2="434.782" y2="270.741" xlink:href="#B"><stop offset="0" stop-color="#e6b8b2"></stop><stop offset="1" stop-color="#d9c6c3"></stop></linearGradient></defs><path fill="url(#H)" d="M424 276h1c14-4 24-13 32-25h0c-2 4-4 7-5 11-4 6-7 12-11 17l-1-1 2-1c-1-1-1-1-1-2s-1-1-2-2c-3 2-6 4-9 5l-1-1c-3 1-6 1-9 2v-1c1-1 3-1 4-2z"></path><path d="M439 273l10-8-7 12c-1-1-1-1-1-2s-1-1-2-2z" class="L"></path><path d="M448 328l-2 3-2 4c-1 1-2 3-3 4 0 1-1 1-1 2v1c-1 0-1 1-2 1-1 3-4 6-6 8h0c-1 1-1 2-2 2-3 2-4 4-6 5-5 5-11 8-17 11 3-3 6-5 10-8l8-8c8-8 14-17 22-25h1z" class="e"></path><path d="M574 333h1c0 1 1 1 1 2 1 5 5 9 8 13l4 7 7 7c7 8 15 16 24 23h-1c-5-2-11-7-16-11-3-3-6-5-9-8-3-4-5-7-7-11-3-4-5-7-7-11s-4-7-5-11z" class="E"></path><path d="M591 798v15l1 59v18c0 2-1 6 0 8v3l-1 1c-1-1-2-1-2-3 0 0 0-1 1-2v-64c0-4-1-10 0-13 2-7 0-15 1-22z" class="a"></path><path d="M777 263l10-1c9-2 14-7 19-14h0c-1 3-2 5-4 7-1 2-1 3-3 5-3 3-6 5-9 7-1 1-2 1-3 2l-3 1-1-1-2-1h1v-1c-5 1-9 1-13 0 1-1 1-1 2-1l4-1 2-2z" class="n"></path><path d="M782 267c3-1 5-1 8-3h1c4-2 8-6 11-9-1 2-1 3-3 5-3 3-6 5-9 7-1 1-2 1-3 2l-3 1-1-1-2-1h1v-1z" class="O"></path><path d="M511 677c1-3 0-6 0-9 0-5 0-10-2-15 0-1-1-3-1-4s1-2 1-3c1-3 1-5 1-8 0-7-2-15-3-22l-1-14h1v6 4c0 2 0 3 1 4v4c0 1 0 2 1 4v4c1 1 1 3 1 4v1c0 4 1 9 0 13l-1 1v1h0v2l1 3s0 1 1 2v2c0 1 0 2 1 3h0c1 1 0 1 1 1 1-2 1-4 1-7l1 4h0c0 4-1 18 1 20 1 1 1 2 2 3-2 3-3 5-4 9h-1v-2c-1-3-3-5-4-7l2-4z" class="d"></path><path d="M511 677h2v1 1l1 2c-1 0-1 1-1 1v6c-1-3-3-5-4-7l2-4z" class="k"></path><path d="M881 267v-1c4-6 6-15 4-22-1-6-5-11-8-16 6 4 11 9 13 17 1 7 0 15-5 21l-2 3c-2 0-2-1-2-2z" class="M"></path><path d="M140 269c0-1-2-3-2-4-4-5-5-13-4-19 1-8 6-13 13-18-3 5-6 10-8 15-2 7 0 17 4 24h0l-3 2z" class="K"></path><defs><linearGradient id="I" x1="463.625" y1="367.823" x2="484.529" y2="379.634" xlink:href="#B"><stop offset="0" stop-color="#484343"></stop><stop offset="1" stop-color="#645751"></stop></linearGradient></defs><path fill="url(#I)" d="M472 371c0-5-1-12 0-18 1 0 2-1 3-1v2 3l1 1v48l-1-1c0-3 1-7-1-9v-2-1c0 1 0 1-1 2 0-6 1-13 0-17 0 1 0 2-1 3v-10z"></path><path d="M716 438c1-1 1-1 3-1s4 1 6 2v1c3 3 4 6 4 10 0 5-2 10-5 13-2 1-3 2-5 2 0 1 1 1 2 2l-2 3v-1c-3-2-5-2-8-1 0-2 1-2 2-3v-1c5-1 8-5 10-9 2-3 2-7 1-10-3-4-4-5-8-7z" class="b"></path><path d="M716 438c1-1 1-1 3-1s4 1 6 2v1h-1l-2-1h-1c1 2 2 3 3 6-3-4-4-5-8-7z" class="O"></path><path d="M433 886c-1-5-1-9-1-14v-24l1-76c1 2 1 5 1 7v25 2 33 17 1 23 5c0 1 1 1 1 2h1c-1 1-1 2-2 3-1-1-2-1-2-2s0-2 1-2z" class="E"></path><path d="M433 886c1-5 0-12 0-17v-30c1 6 0 12 1 17v1 23 5c0 1 1 1 1 2h1c-1 1-1 2-2 3-1-1-2-1-2-2s0-2 1-2z" class="C"></path><defs><linearGradient id="J" x1="648.479" y1="189.208" x2="626.521" y2="193.292" xlink:href="#B"><stop offset="0" stop-color="#2b2520"></stop><stop offset="1" stop-color="#423a3c"></stop></linearGradient></defs><path fill="url(#J)" d="M636 197h0l1-1v-3-1l1-3c1-2 2-4 2-6 1-1 0-1 1-1l1-2v-2h1v-2-1-1c1-2 1-5 1-7v-1c0-2 0-4 1-6 1 5 1 11 0 16l-1 5v2l-1 1v2c-1 2-2 4-2 6-1 1-1 3-2 5v1l-1 1v2l-1 2v1c0 2-1 3-2 5-1 1-1 2-2 3l-1 2h-1c0 1-1 2-2 3h0v-2h0v-2c-1-1-1-2-1-2v-1-1c1-1 1-2 2-3 0-1 0-1 1-1v-1c1 0 2-1 3-2v-2h1c0-1 0-2 1-3z"></path><path d="M629 213c-1-1-1-2-1-2v-1-1c1-1 1-2 2-3 0-1 0-1 1-1v-1c1 0 2-1 3-2v-2h1c0-1 0-2 1-3 0 4-2 6-4 9l-3 6c1 1 1 2 1 3h-1 0v-2z" class="Q"></path><defs><linearGradient id="K" x1="346.165" y1="173.854" x2="375.67" y2="189.871" xlink:href="#B"><stop offset="0" stop-color="#180f0b"></stop><stop offset="1" stop-color="#5b585c"></stop></linearGradient></defs><path fill="url(#K)" d="M366 207c-3-7-6-14-8-22-2-6-4-14-4-21 0-2 0-5 1-6h0c1 1 1 3 2 5l2 10c0 4 2 9 3 14 2 6 5 11 7 17l-3 3z"></path><path d="M686 439c0 1 1 1 1 3v2c1 2 1 3 1 5 1 1 1 3 1 5 1 2 5 7 7 8h1c2 0 4 0 6 1-1 1-1 1-1 2-3-1-7 0-10-1-3 0-5-1-7-2-2-2-3-5-4-8v-3l3-9s1-2 2-3z" class="B"></path><path d="M686 439c0 1 1 1 1 3v2c1 2 1 3 1 5 1 1 1 3 1 5 1 2 5 7 7 8h1c-2 0-6 0-8-1v-1c-2-1-3-2-3-4v-1c-1-4-1-8-2-12v-1s1-2 2-3z" class="L"></path><defs><linearGradient id="L" x1="384.264" y1="212.395" x2="376.805" y2="229.447" xlink:href="#B"><stop offset="0" stop-color="#776667"></stop><stop offset="1" stop-color="#a19b95"></stop></linearGradient></defs><path fill="url(#L)" d="M366 207l3-3c6 9 12 18 20 26 1 2 4 5 6 7h2c1 0 3 1 4 2-1 1-2 1-3 1h0c-1 1-1 1-2 1-13-8-22-21-30-34z"></path><defs><linearGradient id="M" x1="639.6" y1="219.38" x2="645.147" y2="226.013" xlink:href="#B"><stop offset="0" stop-color="#6e6361"></stop><stop offset="1" stop-color="#a09591"></stop></linearGradient></defs><path fill="url(#M)" d="M654 206l2 1c0 2 0 3-1 5-4 9-13 18-21 25-2 2-5 4-8 5h-1c0-1-1-1-2-2 1-1 4-2 5-4l1-1 1-1c3-2 5-5 7-7 2-1 4-3 6-5 4-5 8-10 11-16z"></path><path d="M686 202l1 1c1-1 1-1 2-1 1-1 1-1 2 0 3 5 4 10 4 16 0 1-1 5-1 5l-1 4v1c-2-1-2-1-3-1l-2 1v1c-1 2 0 4-1 6-1-2-1-3-1-5l1-1h-2l-1 1c-1-1-1-1-2-1 1-1 1-2 2-2l2-2h0v-1l-1-1h0-1l-1 1-2 1c-1 1-1 1-2 1-1 1-1 1-2 1s-2 1-3 2c-2 1-2 3-4 3v1h-2l-1 1v-1c1-1 2-1 3-2 2-1 3-2 5-3l1-1 2-2c1-1 2-1 4-2l1-1c2-2 3-4 3-6l3-4c2 1 2 1 4 0v-3c0-2-1-3-3-4-1-1-3-1-4-2v-1z" class="e"></path><path d="M690 223c1 0 1 1 2 1s1-1 2-1l-1 4v1c-2-1-2-1-3-1l-2 1h-4l6-5z" class="n"></path><path d="M683 224l8-9h1v1c0 1-1 2-2 3-1 2-1 4-4 5l-1-1h0-1l-1 1z" class="E"></path><path d="M686 202l1 1c1-1 1-1 2-1 1-1 1-1 2 0 3 5 4 10 4 16 0 1-1 5-1 5-1 0-1 1-2 1s-1-1-2-1l1-1c2-4 3-9 2-13 0-2-1-3-3-4-1-1-3-1-4-2v-1z" class="s"></path><defs><linearGradient id="N" x1="449.795" y1="324.519" x2="473.029" y2="352.051" xlink:href="#B"><stop offset="0" stop-color="#140f0e"></stop><stop offset="1" stop-color="#463e3c"></stop></linearGradient></defs><path fill="url(#N)" d="M461 330c3-8 6-17 6-25h1c2 8-1 18-2 26l-4 18c-1 6-3 12-5 18l-2-1v-3-3c2-3 4-9 3-12l3-6c1-4 1-8 0-12z"></path><path d="M512 343c0 3 0 7 1 10 1 4 0 9 0 14v8c0 1 1 1 1 2 0 3-1 7 1 10v1c-1 4 0 8 0 13v28 11 3 1h0v2 1 1 1 2h0c0 1 1 1 0 2v1h0c-1 2 0 4 0 5v1c0 1 1 2 0 3v16 7 9c0 2-1 4 0 6v3c-2-6-1-13-1-18v-40c-1-2 1-17-1-21h0v4l-1 1v-14-30-13c0-2 1-4 0-6-1-7-1-17 0-24z" class="e"></path><path d="M522 267h0c10-6 19-16 21-28 1-2 1-4-1-6 0-1-1-1-1-2l5 2v3c1-1 1-2 2-3 1 0 1 0 2 1l-1 1v3c1-1 1-1 0-2l2 1 1-1 1 1v1h-2c-1 5 0 10 0 15 0 1 0 3-1 3 0 1-1 2-2 2v-1c3-3 2-9 1-13l-1-5-10 19-3 4c-2 2-5 4-6 7h-1-1l-1 1c-2-1-3-1-4-3z" class="p"></path><path d="M593 279c3 1 5 2 7 4h0c3 1 5 3 8 5l4 2 6 6 2 2-1 1c2 2 4 4 4 6v1c-2-4-5-5-8-8h-1c-2-1-3-2-6-2v-1c-1 1-2 2-3 2-2 0-2-1-3-2l-2-3c-1 1-1 1-1 2-1-2-3-4-4-5 0-2-1-3-1-4h2c1 1 1 2 3 2h1c-2-2-5-3-6-5 0-1 0-2-1-3z" class="J"></path><path d="M600 292l-1-1 1-1v1c2 2 5 3 8 4-1 1-2 2-3 2-2 0-2-1-3-2l-2-3z" class="O"></path><path d="M593 279c3 1 5 2 7 4h0c3 1 5 3 8 5l-1 1c2 1 4 2 6 4l-1 1c-3 0-8-3-10-5-1-1-2-1-2-2-2-2-5-3-6-5 0-1 0-2-1-3z" class="N"></path><path d="M593 279c3 1 5 2 7 4h0v1c2 2 4 3 6 5h-4c-1-1-2-1-2-2-2-2-5-3-6-5 0-1 0-2-1-3z" class="G"></path><defs><linearGradient id="O" x1="646.043" y1="214.547" x2="620.209" y2="235.267" xlink:href="#B"><stop offset="0" stop-color="#7c706e"></stop><stop offset="1" stop-color="#a99f9c"></stop></linearGradient></defs><path fill="url(#O)" d="M645 208c1-1 2-1 4-1h0v2c-1 2-2 3-3 5l-9 13c-2 2-4 5-7 7l-1 1-1 1-6 3v-2c0-1 0-2-1-3 3-1 6-3 7-5 1-1 1-1 2-1l1-1c0-1 1-2 2-3 1-2 3-4 4-6h-2v-1h0c0-1 0-1 1-2h1c3-2 6-4 8-7z"></path><path d="M645 208c1-1 2-1 4-1h0v2c-1 2-2 3-3 5l-1-1s-1-1-1-2c-1 1-1 0-1 1-2 3-4 5-6 6h-2v-1h0c0-1 0-1 1-2h1c3-2 6-4 8-7z" class="k"></path><path d="M374 207c1 0 1 1 2 1l1-1c3 1 3 3 6 3 1 1 2 3 3 4s2 2 4 3h1l3 4 1 1c1 2 0 3 3 4 0 1 1 1 2 2v1c1 1 1 1 2 1l1 1 1 1-1 1h-1l-3-3c-2-2-4-3-6-5-1-1-1-2-2-2h0l-1 1h-1c1 1 2 2 2 3v1c0 1 1 2 2 3s1 2 2 3c0 1 1 2 2 3h-2c-2-2-5-5-6-7-2-4-6-8-8-11l-7-12z" class="t"></path><path fill="#807572" d="M374 207c1 0 1 1 2 1l1-1c3 1 3 3 6 3 1 1 2 3 3 4s2 2 4 3h1l3 4h-4c-1-2-3-4-4-6-1-1-1 0-2-1l-1-1h-1v1l3 3c1 0 1 0 2 1l-1 1h0v-1l-2-1-1-1c0 1-1 2-2 3l-7-12z"></path><path d="M377 165v1 2 1h1v-3 1c1 1 1 2 1 4l1 1v1 2l1 1 1 3v1c1 1 1 2 1 3 1 2 1 3 3 4v1h0c1 1 1 2 1 3h0v1 1c1 1 1 0 1 2h1v2h1v2h-1v1c1 1 1 1 1 2v1c1 0 2 0 2 1l1 1c1-1 1-1 2-1l1-1c1 1 1 1 1 3h-2l1 2v2c0 1-1 2-1 4h0c1 1 1 2 2 3h0c0 1 1 1 1 2l3 3v1l1 1v1h-1c-3-4-6-10-9-15-1-1-2-3-3-5l-2-3h-1c-1-1-1-2-1-3l-1-1-1-3c-4-9-5-18-7-27l1-3z" class="j"></path><path d="M473 233c2 1 3 2 4 4 3 8 8 14 12 21l-4 6h0-1-1c-2-1-3-1-4-2s-3-3-4-5c-1-1 0-4 0-5v-16h-4v-1c1 0 1 0 1-1l1-1z" class="E"></path><path d="M458 348c1 3-1 9-3 12v3l-2 1-3 12c-1 1-1 4-1 5-2 2-3 5-4 6 0-1 1-4 2-5h-1c1-1 1-1 1-2l1-2c1-1 1-2 1-3h0v-1c1-1 1-2 1-4l-1 2v1c-1 0-1 1-1 2s0 1-1 2v1l-1 3-1 1c0 1-1 2-1 2 0 1 0 1-1 2v1l-4 10c-1 1-1 2-2 3s-1 2-2 4l-6 8h0v-1l2-4 1-2c1-2 2-3 2-4s0-2 1-2c0-1 0-1 1-2 0-3 2-5 3-8 0-1 1-3 2-4v-1l1-2 3-8 1-1 5-13 3-9 1 1c1-1 1-2 1-3h2v-1z" class="U"></path><path d="M458 348c1 3-1 9-3 12v3l-2 1c1-5 3-10 5-15v-1z" class="Q"></path><defs><linearGradient id="P" x1="535.538" y1="489.66" x2="568.912" y2="529.882" xlink:href="#B"><stop offset="0" stop-color="#332829"></stop><stop offset="1" stop-color="#47403b"></stop></linearGradient></defs><path fill="url(#P)" d="M552 479v-2h0l1 1v5l1 1h0 0v62h-1-1l-1 3v-8-17c0-12-2-23-1-34 1-2 2-4 2-7-1-1-1-3 0-4z"></path><path d="M149 303c8-7 19-11 29-15l1 2h2 0c3 1 6 3 8 5 0 1 0 2 1 3-1 1-1 2-2 3v1c0 1 0 2-1 4v1l-1 1c-2-4-4-7-7-10-5-2-10-3-15-1h-1c-5 2-9 4-12 7l-2-1z" class="C"></path><path d="M181 290c3 1 6 3 8 5 0 1 0 2 1 3-1 1-1 2-2 3v1c0 1 0 2-1 4v1l-1 1c-2-4-4-7-7-10 1-1 0 0 2-1v1c1 1 1 2 2 2 1-1 1-2 1-3-2-1-1-2-1-3-1-1-2-2-2-3v-1z" class="G"></path><path d="M818 295c3-1 7-2 11-1 3 1 4 1 5 3l2 2h0l-1 1-1-2v2h-1c-1 1 0 1 0 2l-2-1c-5-3-11-2-15 0-8 5-11 10-12 19l-1 8v-4l-1 1h-2v3c-2-1-2-1-2-2h1v-4c0-2 1-4 1-6 2-4 3-8 6-12 3-3 7-6 12-9z" class="X"></path><path d="M829 294c3 1 4 1 5 3l2 2h0l-1 1-1-2v2h-1c-1 1 0 1 0 2l-2-1c-5-3-11-2-15 0l-1-1h1c0-1 0-1 1-2 4-3 10-2 14-1h1 0c-1-1-1-1-2-1l-2-1 1-1z" class="K"></path><path d="M449 354v1 2h0l1-1h0l-3 8-3 7-1 1c-2 3-3 7-5 10-4 7-10 15-18 19h-1v1l-1-1v-2l1-3c4-6 11-11 15-17 2-2 3-3 4-5 1-3 3-6 5-9 1-1 1-3 2-4s1-2 2-3v-1c1-1 1-2 2-3z" class="E"></path><path d="M462 472l-1-93v-19c0-2 0-6 1-7 1 3 1 7 1 11l1 35v87c-2-4-1-9-2-14z" class="d"></path><path d="M362 605v22 40l1 113v30c0 2-1 6 0 8 0 0 1 1 1 2s-1 3-2 4l-3-4 1-1h0c1-1 1-1 1-2 1-3 0-9 0-12v-30-89c0-18-1-36 1-53 0-4-1-7-1-9s1-3 1-5c-1-4-1-10 0-14z" class="F"></path><path d="M191 385v-1c-1-2-2-3-3-4l-4-1 1-1h2 0c1 0 3 1 3 1 1 2 0 2 2 3 0 1 1 1 2 1-1-2-1-3-2-4 3 0 5 1 6 4 1 2 2 5 2 8 0 2 0 4 1 6-2 4-4 8-7 12-6 6-12 7-20 9l6-3c6-3 11-10 13-16 2-5 1-9-2-14z" class="r"></path><path d="M192 379c3 0 5 1 6 4 1 2 2 5 2 8 0 2 0 4 1 6-2 4-4 8-7 12 0-2 1-3 1-5 2-2 3-4 3-7 1-5-1-10-4-14-1-2-1-3-2-4z" class="M"></path><path d="M335 433c2 1 3 2 5 4 2 4 4 10 5 15v7h-1v1c-1 1-2 1-3 2-1 0-2 0-3 1l-2 1c-2 0-4-1-6 0h-1-4 0l1-2c3-2 5-3 8-6 2-3 3-6 2-10v-1h0c0-4 0-8-1-12z" class="c"></path><path d="M473 217v1c2 5 4 11 7 15l5-4v1l2-1c0-1 0-1 1-1v1c-1 1-1 2-2 3 0 2-1 5-2 6v6c1 4 4 7 6 10 3 4 7 8 11 10l4 2-1 2h-3c0 1 1 1 1 2h0l1 3c0 1 0 0-1 1-1-2-2-3-3-5h-1-1c-1-2-3-4-5-7-1-1-2-3-3-4-4-7-9-13-12-21-1-2-2-3-4-4h0v-1l1-3h1v-1-3l-1-3c-1-2-1-3-1-5z" class="p"></path><path d="M485 230l2-1c0-1 0-1 1-1v1c-1 1-1 2-2 3 0 2-1 5-2 6v6c-1-2-1-3-1-5-1-3 0-6 2-9z" class="V"></path><path d="M680 612c1-5 1-12 1-17 0-16 0-30-1-45-1-5-1-10-2-15-2-7-4-13-7-19v-1c16 21 16 47 17 72h1l1-31v-1-7-3l1 25-1 26c-1 3 0 6-1 8-2-6-2-12-2-19l-3-31c-1-7-2-17-6-23 2 9 3 18 3 28l1 29v16 7l-2 1z" class="I"></path><path d="M439 273c1 1 2 1 2 2s0 1 1 2l-2 1-7 7-6 8c-1 0-3-1-5-1l2-2c-3 1-5 2-7 2-3 0-4-1-6-3 1 0 3-1 4-2h1c1-1 3-2 5-3v-1h1c-2-1-5 0-7 0h-1l1-1c-3-1-6 0-9 0 2-1 5-1 7-1s5-1 7-2c3-1 6-1 9-2l1 1c3-1 6-3 9-5z" class="P"></path><path d="M424 290c1 0 3-1 4-2 2-1 3-2 5-3l-6 8c-1 0-3-1-5-1l2-2z" class="X"></path><path d="M420 279c3-1 6-1 9-2l1 1c-4 2-11 4-15 4-3-1-6 0-9 0 2-1 5-1 7-1s5-1 7-2z" class="n"></path><defs><linearGradient id="Q" x1="451.16" y1="401.591" x2="435.798" y2="391.065" xlink:href="#B"><stop offset="0" stop-color="#0e0b0a"></stop><stop offset="1" stop-color="#3d3533"></stop></linearGradient></defs><path fill="url(#Q)" d="M455 363v3l2 1c-4 15-6 30-12 45h-1s-1 1-1 2h-1l-1 2h0 1l-1 2c0-1 0-1-1-1-3 5-6 10-10 14l2-3c2-6 5-11 8-17l5-16c1-2 1-4 2-6 1-1 1-2 1-3h0c0-2 1-3 2-4v-1h-1c0-1 0-4 1-5l3-12 2-1z"></path><path d="M871 262c0 2-1 2-2 3v1l-1 1v1c1-1 1-1 2-1v1c-2 1-3 2-5 3h-2l-5 2h-2l-2 1c1 2 4 3 6 4v2h-1c-3-1-7-2-10-3-6-2-12-3-18-4l-2 2-17-2v-2c-2-1-3-1-5-2 9 1 17 1 25 1 9 0 18 0 27-2 4-1 8-3 12-6z" class="m"></path><path d="M856 273h0v-3l5-1 2 2-5 2h-2z" class="l"></path><path d="M864 268c2-1 3-2 5-3v1l-1 1v1c1-1 1-1 2-1v1c-2 1-3 2-5 3h-2l-2-2 3-1z" class="M"></path><path d="M864 268c1 1 1 1 1 2v1h-2l-2-2 3-1zm-52 3l19 2-2 2-17-2v-2z" class="O"></path><path d="M367 193v-1c-3-4-4-11-5-15 0-2-1-4-1-5v1c1 1 1 3 2 4l1 4v1c1-1 2-2 2-3 0-2-1-3-1-4s-1-1-1-2v-1l3 8c1-1 1 0 1-1h0c0 1 1 2 1 2 1 0 1 1 2 2v1 1h0v1 2 1s1 1 2 1v1c1-1 1-1 1-2v-1s0-1-1-1c0-1-1-2-1-3h0v-2h-1l1-1-1-2-1-1v-1-1h0c1 2 1 4 3 6v-2c1 2 1 3 2 5v1c1 1 1 3 2 4l1 1c0-1-1-2-1-2v-3l-1-4c0-1-1-1-1-2v-1l5 17v1l3 10h-1-1l2 3c-3 0-3-2-6-3l-1 1c-1 0-1-1-2-1-2-1-3-5-4-7s-2-5-3-7z" class="Q"></path><path d="M374 197l1 1 1-1c1 1 2 4 2 5h-2c-2-2-2-2-2-4v-1z" class="V"></path><path d="M367 193c2 0 3 4 4 4 0-3-1-6-2-9 2 2 4 6 5 9v1l-1 1v1c1 1 2 3 1 4-1 0-1-1-2-2l-1-2h-1c-1-2-2-5-3-7z" class="g"></path><path d="M374 198c0 2 0 2 2 4h2l3 5 2 3c-3 0-3-2-6-3l-1 1c-1 0-1-1-2-1-2-1-3-5-4-7h1l1 2c1 1 1 2 2 2 1-1 0-3-1-4v-1l1-1z" class="i"></path><path d="M286 248c3-3 6-6 8-10 9-14 12-32 18-47 1 2 1 3 1 6-1 1-1 2-1 4-2 4-2 7-2 11 0 2-1 4-1 5 0 4 0 7-1 10l-1 5-1-8v-3c0 2-1 3-1 4-1 2-1 5-2 7-1 4-3 7-6 10-1 2-2 4-4 5-1 1-2 1-3 1-1 1-1 2-2 4h-1c0-2-1-3-1-4z" class="N"></path><path d="M290 248l9-15c2-5 3-9 5-14 0-3 1-7 3-9 0 4-1 7-1 11 0 2-1 3-1 4-1 2-1 5-2 7-1 4-3 7-6 10-1 2-2 4-4 5-1 1-2 1-3 1z" class="E"></path><defs><linearGradient id="R" x1="299.338" y1="399.814" x2="310.284" y2="384.072" xlink:href="#B"><stop offset="0" stop-color="#f5332d"></stop><stop offset="1" stop-color="#eb706e"></stop></linearGradient></defs><path fill="url(#R)" d="M269 380c11-1 22-1 32 2 4 1 8 3 12 4l7 4c1 1 2 2 3 2s2 0 4 1l2 2 1-1c2 2 4 3 6 4 1 1 2 2 1 4h0l-4-2h1c0 2 1 2 2 3v1 1 1 2c-8-7-19-16-29-19-1-1-2-1-3-1-4-2-7-3-11-4-6-1-11-1-17-1l-5-1h2v-2c-2 0-2 1-4 0z"></path><path d="M323 392c1 0 2 0 4 1l2 2 1-1c2 2 4 3 6 4 1 1 2 2 1 4h0l-4-2-10-8z" class="C"></path><path d="M226 261l-4-4c-1-2-5-7-4-10 3 7 10 12 17 14 13 5 27 2 39-4 3 0 5 0 6 1l-2 2 2 2c-5 3-11 5-17 7-2 0-7 1-9 2v1h0c-3-1-11-3-12-5-1-1-3-2-5-2h-1c0-1-1-2-3-2-1 0 0 0-1-1-2 0-3-1-4-1 0 1 1 2 2 2l1 1c1 0 2 1 3 2h-1 0c-3-1-5-3-7-5z" class="q"></path><path d="M242 267h2c11 1 24-2 34-7l2 2c-5 3-11 5-17 7-2 0-7 1-9 2v1h0c-3-1-11-3-12-5z" class="D"></path><defs><linearGradient id="S" x1="619.536" y1="223.862" x2="623.135" y2="233.906" xlink:href="#B"><stop offset="0" stop-color="#827673"></stop><stop offset="1" stop-color="#998e8d"></stop></linearGradient></defs><path fill="url(#S)" d="M637 213h1l-2 2c-1 1-1 1-1 2h0v1h2c-1 2-3 4-4 6-1 1-2 2-2 3l-1 1c-1 0-1 0-2 1-1 2-4 4-7 5 1 1 1 2 1 3v2c-6 3-11 5-18 6h-5v-1c-2 0-4-4-4-5l2-1c1 0 3-1 5-1l7-3c9-3 16-8 22-14 2-3 4-5 6-7z"></path><path d="M597 238c1 2 3 1 5 1h2c1-1 1 0 2-1 0 0 0-1 1-1h2l2 2 2-1h1 0c-2 1-8 2-10 4-1 0-4 1-5 2h0c-2 0-4-4-4-5l2-1z" class="t"></path><path d="M621 234c1 1 1 2 1 3v2c-6 3-11 5-18 6h-5v-1h0c1-1 4-2 5-2 2-2 8-3 10-4l5-2 2-2z" class="u"></path><path d="M621 234c1 1 1 2 1 3l-1 1c-1 0-1-1-2-2l2-2z" class="o"></path><path d="M306 221v3l1 8 1-5v4h1 0l1 14-1 1c0 3 0 7 1 10v2c0 3 1 5 1 7l-2-6c-1 0-2 0-2-1v1l-1 1 1 1-1 2c-1-3-3-5-5-8h0c-1-1-3-2-4-3 0-1 1-2 1-2l-1-1-1 1c-1 1-3 1-4 1-2 1-2 1-4 1 1-2 1-3 2-4 1 0 2 0 3-1 2-1 3-3 4-5 3-3 5-6 6-10 1-2 1-5 2-7 0-1 1-2 1-4z" class="P"></path><path d="M292 251c1-2 2-3 4-4v1c2 0 3-2 4-3 0 2 0 1-1 2v3h0l1-1c1-2 1-3 2-5l1-1c0 4-1 10 1 13-1 0-2-1-3-1-1-1-3-2-4-3 0-1 1-2 1-2l-1-1-1 1c-1 1-3 1-4 1z" class="C"></path><path d="M305 236c1 1 0 3 0 5v12c0 2 0 4 2 5v1l-1 1 1 1-1 2c-1-3-3-5-5-8h0c1 0 2 1 3 1-2-3-1-9-1-13 0-2 1-5 2-7z" class="I"></path><path d="M306 224l1 8c0 9 0 19 2 27-1 0-2 0-2-1-2-1-2-3-2-5v-12c0-2 1-4 0-5v-1c0-1 1-1 0-2h0l1-1v-4-4z" class="B"></path><path d="M308 227v4h1 0l1 14-1 1c0 3 0 7 1 10v2c0 3 1 5 1 7l-2-6c-2-8-2-18-2-27l1-5z" class="M"></path><path d="M550 489c-1-3 0-5 0-8l-1-14v-8h0c-1-3 0-7 0-10l-1 1c-1-2 0-2-1-4v-4-5l1-2-1-5v-8c1-10 0-21 0-31 0-3 0-7 1-10v-1-1h0v-37h1v15-1c1-3 0-7 0-11v-5-1h1v19 32l1 21h-1v28 30c1-1 1-2 1-4 1 5 0 9 1 14-1 1-1 3 0 4 0 3-1 5-2 7v-1z" class="f"></path><path d="M551 465c1 5 0 9 1 14-1 1-1 3 0 4 0 3-1 5-2 7v-1c1-6 0-14 0-20 1-1 1-2 1-4z" class="d"></path><path d="M549 443v4c-2-3-1-66 0-74v4c0 3 0 6 1 9v4l1 21h-1v28 4h-1z" class="j"></path><path d="M549 443c-1-4 0-8 0-12v-14c0-2 0-5 1-8v2 28 4h-1z" class="e"></path><path d="M662 558c1 7 0 14 0 20v41l1 206s1 1 1 2c-1 1-1 2-2 3l-2-2c0-2 1-1 1-3 1-1 0-5 0-6v-22-105-98l1-20c0-5-1-11 0-16z" class="S"></path><path d="M577 265c-3-3-8-9-8-13 6 8 12 14 21 19 3-1 5-1 8 0h2 0l-1-1h0c0-1-1-1-1-2 1 0 2-1 3-1h1c1 1 1 1 0 2l1 2h1c1 0 2 1 3 1l1 1c1 1 2 1 3 2h-2c1 1 2 1 3 1l-2 2v1h-1v1h-1c-3 0-6-1-9-1-2 0-4-1-5-1l-2 1h1c1 1 1 2 1 3 1 2 4 3 6 5h-1c-2 0-2-1-3-2h-2c0 1 1 2 1 4l-7-8-3-3h-1l-3-3-3-6c-1-1-1-2-1-4z" class="n"></path><defs><linearGradient id="T" x1="603.284" y1="273.01" x2="606.327" y2="278.698" xlink:href="#B"><stop offset="0" stop-color="#2d2b2b"></stop><stop offset="1" stop-color="#483d3c"></stop></linearGradient></defs><path fill="url(#T)" d="M595 274h3c3-1 5 0 8 0 1 0 2 0 3 1s2 1 3 1l-2 2v1h-1c-5-1-9-3-14-5z"></path><path d="M577 265c6 5 10 9 17 13l-2 1c-2-1-5-3-7-3h-1c-1-1-2-2-3-4-1-1-1-2-2-2l-1-1c-1-1-1-2-1-4z" class="E"></path><path d="M578 269l1 1c1 0 1 1 2 2 1 2 2 3 3 4h1c2 0 5 2 7 3h1c1 1 1 2 1 3 1 2 4 3 6 5h-1c-2 0-2-1-3-2h-2c0 1 1 2 1 4l-7-8-3-3h-1l-3-3-3-6z" class="I"></path><path d="M601 267h1c1 1 1 1 0 2l1 2h1c1 0 2 1 3 1l1 1c1 1 2 1 3 2h-2c-1-1-2-1-3-1-3 0-5-1-8 0h-3l-5-3c3-1 5-1 8 0h2 0l-1-1h0c0-1-1-1-1-2 1 0 2-1 3-1z" class="f"></path><path d="M831 273c6 1 12 2 18 4 3 1 7 2 10 3h1v-2c1 0 2 1 3 1l4 3c10 6 18 15 21 26 1 6 2 13 0 18l-1-2v-1c-2-16-11-27-23-37-12-6-22-9-35-11l2-2z" class="J"></path><path d="M831 273c6 1 12 2 18 4 3 1 7 2 10 3h1v-2c1 0 2 1 3 1l4 3h-1c-1 1-2 1-3 1l-1-1h-1c-3 0-8-3-10-4l-1 1h1 1v1l3 1c3 1 6 3 9 5h1-1c-12-6-22-9-35-11l2-2z" class="K"></path><path d="M391 263c1 1 2 1 3 1l1 1h5 1v1 1c-1 0-2 0-3 1h2c1 0 2 0 3 1h0c1 0 1 0 2-1h0 1 1 2c1 0 1 0 2 1l2-1h3 1 4v1l-4 2c-1 2-2 3-4 4h0v2h7l1-1h3c-1 1-3 1-4 2v1c-2 1-5 2-7 2s-5 0-7 1l-3-1c-2 0-4 0-5-1v-2c-2-1-6-2-8-4h-1c0-2-1-4-1-6 1-2 2-3 3-5z" class="V"></path><path d="M390 274h1 1c2 1 3 1 5 2h0 3s1 1 2 1c2 0 3 0 5 1h6v1c-4 1-10 0-15-1-2-1-6-2-8-4z" class="k"></path><path d="M416 271h1c-1 2-2 3-4 4h0v2h7l1-1h3c-1 1-3 1-4 2l-7 1v-1h-1c-1-1-2-1-3-2l1-1c0-1 1-1 2-1 0-2-1-1-1-2h1 1l3-1z" class="Q"></path><path d="M413 279l7-1v1c-2 1-5 2-7 2s-5 0-7 1l-3-1c-2 0-4 0-5-1v-2c5 1 11 2 15 1z" class="s"></path><path d="M417 268h4v1l-4 2h-1l-3 1-1-1c-3 1-9 0-13 2v-1h-4-2v-1h1l-1-2c1 0 1 0 2 1 0-1 1-1 1-1l1-1 1 2c1 0 2-1 3 0h0l2-1h0c1 0 1 0 2-1h0 1 1 2c1 0 1 0 2 1l2-1h3 1z" class="k"></path><path d="M417 268h4v1l-4 2h-1v-2l-1 1s-1-1-2 0h-1v-1c-1 1-1 1-2 1-2 0-5 0-7-1 1 0 1 0 2-1h0 1 1 2c1 0 1 0 2 1l2-1h3 1z" class="V"></path><path d="M391 263c1 1 2 1 3 1l1 1h5 1v1 1c-1 0-2 0-3 1h2c1 0 2 0 3 1l-2 1h0c-1-1-2 0-3 0l-1-2-1 1s-1 0-1 1c-1-1-1-1-2-1l1 2h-1v1l-1 2h-1-1-1c0-2-1-4-1-6 1-2 2-3 3-5z" class="k"></path><path d="M390 274h0c0-2 1-3 0-5v-1c3-1 7-1 11-1-1 0-2 0-3 1h2c1 0 2 0 3 1l-2 1h0c-1-1-2 0-3 0l-1-2-1 1s-1 0-1 1c-1-1-1-1-2-1l1 2h-1v1l-1 2h-1-1z" class="g"></path><defs><linearGradient id="U" x1="216.252" y1="427.744" x2="197.662" y2="422.696" xlink:href="#B"><stop offset="0" stop-color="#ca130f"></stop><stop offset="1" stop-color="#f14643"></stop></linearGradient></defs><path fill="url(#U)" d="M213 397c5-2 8-6 13-8l1 1c-1 1-1 1-1 2l-1 1c-5 6-11 10-15 17-2 3-4 5-5 8-6 11-9 24-8 37 0 2 1 8-1 11l-1-3c-6-20 1-38 10-55l8-11z"></path><defs><linearGradient id="V" x1="455.217" y1="303.797" x2="490.673" y2="331.281" xlink:href="#B"><stop offset="0" stop-color="#090c0d"></stop><stop offset="1" stop-color="#4e3c38"></stop></linearGradient></defs><path fill="url(#V)" d="M471 371l-1-54v-21c0-4 1-8 1-12 1-1 1-3 2-4 2 4 0 9 2 14 1-2 0-8 0-10h1c1 5 0 11 0 16v38c0 6 1 13 0 20l-1-1v-3-2c-1 0-2 1-3 1-1 6 0 13 0 18h-1z"></path><path d="M491 634l-1-226v6 12 45c0 7-1 15 0 22v4 11c0 2 0 3 1 5 0 2-1 11 0 13v11c1 0 1-1 2-1v-38c0-3 0-12-1-14 0-2-1-6 1-7-1-2-1-2-1-3l1-1c-1-1-1-1-1-2v-45h1l1 152v40c0 5-1 11 0 17l-3-1zm142-370h1c1 1 1 2 2 3v3c-1 3-1 4-3 6l-2 1c-7 2-15 3-21 2v-1l2-2c-1 0-2 0-3-1h2c-1-1-2-1-3-2l-1-1c-1 0-2-1-3-1h-1l-1-2c1-1 1-1 0-2h-1c-1-1-2-1-2-2 2 0 4 1 7 1 2-1 4-1 6-1h1l3 1 1-1 1 1h3c1-1 3-1 4 0 3-1 5-2 8-2z" class="d"></path><path d="M632 272c2 0 2-1 4-2-1 3-1 4-3 6l-1-2-1-2h1z" class="i"></path><path d="M631 272h0c-2 0-3 1-4 1l-2 1h-3v-1h2l1-1c1 0 2-1 3-1h0 2 1l1 1h-1z" class="V"></path><path d="M633 264h1c1 1 1 2 2 3-2 0-3 1-3 1-1 0-2-1-2-1-2-1-6 0-7 0l1-1c3-1 5-2 8-2z" class="k"></path><path d="M632 274l1 2-2 1c-7 2-15 3-21 2v-1l2-2 2-1v1l1-1h3c1 1 2 1 3 1 3-1 8 0 11-2z" class="V"></path><path d="M601 267c-1-1-2-1-2-2 2 0 4 1 7 1 2-1 4-1 6-1h1l3 1 1-1 1 1h3c1-1 3-1 4 0l-1 1c-1 0-1 0-2 1h-2v1h5v-1h4c-2 2-5 2-6 4-2 0-3 0-4-1h-5c-2 1-3 1-5 0h-1c0-1-1-1-1-2v-1h-2-1l-2 1c1-1 1-1 0-2h-1z" class="g"></path><path d="M601 267c-1-1-2-1-2-2 2 0 4 1 7 1 2-1 4-1 6-1-1 1-2 1-3 1h-4c1 1 4 2 5 2h1v1c-1 0-3-1-4-1h0-2-1l-2 1c1-1 1-1 0-2h-1z" class="e"></path><path d="M839 286c7 1 15 4 21 8 4 1 7 3 10 5l-1 1v1c-5-3-12-7-18-5-5 1-8 3-11 7-1 2-1 3-2 5h0c-1-1-1-2-1-3l-1-4-2-1v-2l1 2 1-1h0l-2-2c-1-2-2-2-5-3-4-1-8 0-11 1-1-1-1-1-1-2h0v-3l14-2v-1h8v-1z" class="G"></path><path d="M839 297h1c1 3-1 5-2 8h-1l-1-4 3-4z" class="F"></path><path d="M834 297l2-2c2-3 4-4 8-5h2v1l-1 1c-1 0-5 3-6 5l-3 4-2-1v-2l1 2 1-1h0l-2-2z" class="D"></path><path d="M831 288c4 0 9 0 13 1h0v1c-4 1-6 2-8 5l-2 2c-1-2-2-2-5-3-4-1-8 0-11 1-1-1-1-1-1-2h0v-3l14-2z"></path><defs><linearGradient id="W" x1="411.12" y1="230.438" x2="406.173" y2="240.743" xlink:href="#B"><stop offset="0" stop-color="#8e817f"></stop><stop offset="1" stop-color="#b4aba7"></stop></linearGradient></defs><path fill="url(#W)" d="M386 202h1l2 3c1 2 2 4 3 5 3 5 6 11 9 15h1v1c7 8 20 10 30 13-1 2-2 4-4 6-10 0-18-1-27-6-1-1-3-2-4-2-1-1-2-2-2-3-1-1-1-2-2-3s-2-2-2-3v-1c0-1-1-2-2-3h1l1-1h0c1 0 1 1 2 2 2 2 4 3 6 5l3 3h1l1-1-1-1v-1c-1-2-3-4-4-6-4-8-10-14-13-22z"></path><path d="M313 465l5 1c2 0 3 0 5 1 5-1 9 1 14 2h2l5 2h-5l-1 1 5 2h-4l-1 2c-2-1-4-1-7-1l3 1-1 1c0 1 0 2-1 3-1 0-3 0-4 1-3 0-6 1-7 3-2 3-1 7 0 10l-6-10c-2-3-5-7-3-11 1-1 2-3 2-4v-1l-4-1 1-1c0-1 1-1 2-1z" class="N"></path><path d="M313 465l5 1c2 0 3 0 5 1-1 0-2 0-3 1h-1 0-5l-4-1 1-1c0-1 1-1 2-1z" class="K"></path><path d="M323 467c5-1 9 1 14 2h2l5 2h-5l-1 1 5 2h-4l-1 2c-2-1-4-1-7-1h-6c-3 1-7 2-8 4l-1 1c-1-1-2-4-3-5l4-2c1-1 2-1 3-2l-1-1c1-1 2-1 4-2h0-4 1c1-1 2-1 3-1z" class="B"></path><path d="M323 467c5-1 9 1 14 2h2l5 2h-5l-1 1c-1 0-3-1-4-1-3 0-7 0-10-1l-4 1-1-1c1-1 2-1 4-2h0-4 1c1-1 2-1 3-1z" class="K"></path><path d="M323 467c5-1 9 1 14 2-4 0-9-1-13 1h-2 2l-4 1-1-1c1-1 2-1 4-2h0-4 1c1-1 2-1 3-1z" class="R"></path><path d="M561 363h0c1-2 1-5 1-7v-11 1c0 2 0 4 1 6v7 1l1 32v48 32c0 7 0 13-1 20 1 1 2 1 2 2-1 1-1 1-2 1h0-2l-1-1c1-1 0-1 2-1l-1-130z" class="V"></path><path d="M483 352c1 3 1 81 1 92h0v-1l1 1 1-3 1 1v-3c-1-1-1-23-1-26 1-5 0-8 0-12l1-21h0l1 76v66l-1-1c-1-3-1-6-1-10-1-4-2-9-2-14-1-11-1-22-1-33V352z" class="Q"></path><path d="M225 350l1 1c0 1 0 3 1 4-5 5-9 10-14 13l-6 4-3 2c-1 1-3 1-4 1l-4 2h-2c-1 1-2 1-3 1l1 1c1 1 1 2 2 4-1 0-2 0-2-1-2-1-1-1-2-3 0 0-2-1-3-1h0-2l-1 1 4 1c1 1 2 2 3 4v1l-1-1c-7-7-18-7-27-7h-1c9-1 18-2 27-4 12-3 21-13 27-23 2 1 3 2 4 2-1 3-4 6-5 8 4-3 7-7 10-10z" class="O"></path><path d="M191 378c-2-1-4 0-5-1v-1c2-2 6-2 9-3 0 1 0 1 1 2h4l-4 2h-2c-1 1-2 1-3 1z" class="r"></path><path d="M195 373l10-5c3-1 5-3 7-5 0 1 0 1-1 2l-2 2c1 0 2 0 3-1s1-1 2-1h0c-2 2-4 3-6 4l1 1h-1c0 1-1 2-1 2l-3 2c-1 1-3 1-4 1h-4c-1-1-1-1-1-2z" class="m"></path><path d="M225 350l1 1c0 1 0 3 1 4-5 5-9 10-14 13l-6 4s1-1 1-2h1l-1-1c2-1 4-2 6-4h0c-1 0-1 0-2 1s-2 1-3 1l2-2c1-1 1-1 1-2l3-3c4-3 7-7 10-10z" class="Z"></path><defs><linearGradient id="X" x1="467.108" y1="450.978" x2="478.822" y2="449.94" xlink:href="#B"><stop offset="0" stop-color="#191818"></stop><stop offset="1" stop-color="#544a49"></stop></linearGradient></defs><path fill="url(#X)" d="M471 371h1v10c1-1 1-2 1-3 1 4 0 11 0 17 1-1 1-1 1-2v1 2c2 2 1 6 1 9l1 1v14l-1 4-1 99h-1c0-1 0-2-1-3-1-15 0-32 0-48l-1-101z"></path><path d="M475 405l1 1v14l-1 4h0v8c-2-3-1-23 0-27z" class="i"></path><defs><linearGradient id="Y" x1="685.962" y1="472.272" x2="678.172" y2="506.144" xlink:href="#B"><stop offset="0" stop-color="#c50001"></stop><stop offset="1" stop-color="#e93d38"></stop></linearGradient></defs><path fill="url(#Y)" d="M697 470c2 0 5 0 8 1 1 1 2 1 2 3l-1 1h0c-2 0-3-1-5 0h2v3h0c-2-1-3-1-4-1h-1v1h1-2v2h0c-8 0-14 2-20 7-5 5-9 11-11 17l-2-1c-2-1-2-1-4-1 2-6 4-14 8-20l-2-2h0c11-5 19-8 31-10z"></path><defs><linearGradient id="Z" x1="692.126" y1="469.396" x2="690.142" y2="480.594" xlink:href="#B"><stop offset="0" stop-color="#190101"></stop><stop offset="1" stop-color="#330607"></stop></linearGradient></defs><path fill="url(#Z)" d="M697 470c2 0 5 0 8 1 1 1 2 1 2 3l-1 1h0c-2 0-3-1-5 0-9-1-17-1-24 5-1 1-4 5-6 5 1-2 1-4 2-5v-1c-2 0-4 2-5 3l-2-2h0c11-5 19-8 31-10z"></path><path d="M534 649h0v-6-11-54-86-54c1 2 1 4 1 7 0 0-1 2 0 2 0 1 1 1 1 2v-12h0v57l1 30v97 20c0 2-1 6 0 8h-3z" class="d"></path><defs><linearGradient id="a" x1="625.391" y1="354.993" x2="638.186" y2="354.068" xlink:href="#B"><stop offset="0" stop-color="#dfa6a4"></stop><stop offset="1" stop-color="#f6d4d1"></stop></linearGradient></defs><path fill="url(#a)" d="M622 316c0-1 0 0 1-1s2-1 3-2c2 2 2 3 5 5 0 2 1 3 2 5l2 6 1 6 2 5c1 12 2 24 1 36l-3 13-2 10c-1-1-2-2-4-3 1-7 3-14 4-20 2-21-3-42-12-60z"></path><path d="M195 270h15 0l-1 1h1v1l-4 1-7 1c-21 3-41 10-55 28-6 9-8 17-8 27h-1c-1-6-1-12 0-18 3-15 13-23 25-31h0l6-3v-1l4-1h2l7-2c5-2 11-1 16-3z" class="D"></path><path d="M160 280l6-3-1 3v1h0l-3 2h-1l-1-3z" class="O"></path><path d="M166 276l4-1 1 1 3 1-9 4v-1l1-3v-1z" class="Z"></path><path d="M170 275h2l7-2h6v1c-3 1-8 2-11 3l-3-1-1-1z" class="n"></path><defs><linearGradient id="b" x1="204.013" y1="275.802" x2="190.987" y2="267.698" xlink:href="#B"><stop offset="0" stop-color="#e96c6a"></stop><stop offset="1" stop-color="#fe9191"></stop></linearGradient></defs><path fill="url(#b)" d="M195 270h15 0l-1 1h1v1l-4 1-7 1c-2 0-3 0-5-1-3 1-6 1-9 1v-1h-6c5-2 11-1 16-3z"></path><path d="M304 388c1 0 2 0 3 1 2 2 6 5 9 7 8 6 17 12 23 20l6 15c2 5 4 10 4 15 0 1 0 2-1 3h0l-1-1c-1-1-2-3-2-4h-1c0 2 1 4 2 7l-1 1c-1-5-3-11-5-15-2-2-3-3-5-4-2-5-5-10-8-14-4-7-9-15-14-21-3-3-7-6-9-10z" class="X"></path><path d="M621 255c4-2 8-4 12-7s7-7 10-11c7-8 14-17 19-26v-1h1l-1 2-1 1v1s-1 0-1 1c-1 1 0 1-1 2l-2 3s-1 1-1 2c-1 1-1 0-1 1-1 1-1 2-2 2-1 1 0 1-1 2s-2 2-3 4c-1 1-2 1-2 2-1 1-2 2-2 3-1 1-2 1-2 2-1 2-2 3-3 4v1c-1 1-1 0-1 1h0l2-1h0l1 1c-1 1 0 1-1 2h-1c-1 1-1 1-2 1s-1 0-2 1-3 2-4 3-2 1-3 2c2-1 5-1 7-3h1l1-1 2-2s1-1 2-1v1h0c1 0 1 0 2-1h1 1 1l3-3h1l2-1 1-1h1s1-1 2-1l1-1 3-3h1l1-1 1 1-3 3-1 2c0 1 0 2 1 2 2 0 4-2 6-3h1v-1c1 0 1-1 2-1s1-1 2-1l2-2c1-1 2-2 3-2 1-1 2-2 4-2h0l1-1 1 1c0 1-1 1-1 2v-1h-1v1c-1 1-2 2-3 2-1 1-2 1-3 2-2 1-3 4-5 4-1 1-1 2-3 2-1 1-1 2-2 2v2h1c-2 2-4 2-5 3-5 2-11 4-16 7-1 0-2 0-3 1v-1h-2c-2 2-5 1-8 3-3 0-7 0-10 1 0-1-3-1-4-1 1 0 2 0 2-1h1 0c-4 0-7-1-12-2 4 0 8-1 11-2h1z" class="Q"></path><path d="M621 255c3 0 6-1 10-1 1 0 4-2 5-1 0 0 0 1 1 0 2-1 2-1 4-1s5-1 7-1c3-1 8-5 11-5-1 3-17 8-20 10 1 0 1 0 1 1-2 2-5 1-8 3-3 0-7 0-10 1 0-1-3-1-4-1 1 0 2 0 2-1h1 0c-4 0-7-1-12-2 4 0 8-1 11-2h1z" class="k"></path><path d="M639 256c1 0 1 0 1 1-2 2-5 1-8 3-3 0-7 0-10 1 0-1-3-1-4-1 1 0 2 0 2-1h1 0c3-1 7-1 10-2l8-1z" class="f"></path><path d="M264 377c2 0 3-1 5-1h11 8c5 1 10 2 15 4h2v1c-2 1-3 1-4 1-10-3-21-3-32-2l-11 2c-2 1-4 1-5 2-5 2-8 3-11 7-1 1-3 2-3 3l-1 1c0 2-1 5-2 6-8-1-14-1-21 4-1 2-4 5-5 5 4-7 10-11 15-17l1-1c0-1 0-1 1-2l-1-1c-5 2-8 6-13 8 4-4 10-8 14-11l16-8c1 1 2 2 4 2h3c4 0 10-2 14-3z"></path><defs><linearGradient id="c" x1="236.727" y1="377.681" x2="232.678" y2="391.646" xlink:href="#B"><stop offset="0" stop-color="#870000"></stop><stop offset="1" stop-color="#b40f0e"></stop></linearGradient></defs><path fill="url(#c)" d="M227 386l16-8c1 1 2 2 4 2h3c-6 2-10 4-15 6-4 2-6 5-10 7l1-1c0-1 0-1 1-2l-1-1c-5 2-8 6-13 8 4-4 10-8 14-11z"></path><defs><linearGradient id="d" x1="801.614" y1="426.063" x2="825.1" y2="419.918" xlink:href="#B"><stop offset="0" stop-color="#de2922"></stop><stop offset="1" stop-color="#f34c49"></stop></linearGradient></defs><path fill="url(#d)" d="M779 376l9 3h0v-1h2 3c4 1 7 4 10 7 5 3 8 6 11 10l3 4c2 2 4 6 4 9v2c8 15 14 34 8 52v1h-1c-1-2-1-3-1-4v-8c0-10-2-20-6-29-1-2-2-4-3-5s-2-3-4-4l-8-8c2-1 2-1 3-2-8-8-17-18-28-21l1-1c2 1 5 2 7 2l-3-3c-2-1-1 0-2-1s-3-2-5-3z"></path><path d="M814 400c0-1 0-1 1-2l2 1c2 2 4 6 4 9v2c-2-4-5-7-7-10z" class="W"></path><path d="M809 403c3 4 6 9 9 14-1-1-2-3-4-4l-8-8c2-1 2-1 3-2z" class="E"></path><path d="M779 376l9 3h0v-1h2 3c4 1 7 4 10 7 5 3 8 6 11 10l3 4-2-1c-1 1-1 1-1 2l-9-8c-5-3-10-6-16-9l-3-3c-2-1-1 0-2-1s-3-2-5-3z" class="B"></path><path d="M805 392h1l2 2h1l-3-4h0c2 1 3 3 5 4l1 1 1 1c1 0 1 0 1-1l3 4-2-1c-1 1-1 1-1 2l-9-8z" class="a"></path><path d="M788 379h0v-1h2 3c4 1 7 4 10 7-4-1-11-3-15-6z" class="S"></path><path d="M765 386h1c2 0 3 1 4 2 2 1 4 1 5 3 2 2 4 4 5 6 1 1 2 3 4 4v1l1 1h0c6-2 15-2 20 1l1 1 8 8c2 1 3 3 4 4s2 3 3 5c4 9 6 19 6 29v8c-1 0-1 1-2 1l-1-9c-2-13-8-29-19-37-5-3-10-4-16-3-4 1-8 3-11 6-3 4-8 12-12 13 1-2 4-4 6-6 3-5 5-13 5-18-1-9-6-15-12-20z" class="R"></path><path d="M770 388c2 1 4 1 5 3 2 2 4 4 5 6 1 1 2 3 4 4v1l1 1h0c6-2 15-2 20 1l1 1 8 8-11-6c-8-3-16-1-23 3 0-3 0-7-1-9l-1-1c-2-5-4-8-8-12z" class="D"></path><path d="M213 368l-1 3 1 1h2l-2 2h4v1h2c4-1 9-1 13-1 3-1 6-2 9-1l-14 3c2 0 10-1 11 0h-1 1 4l2 1-1 1-16 8c-4 3-10 7-14 11l-8 11v-2c0-1 1-3 1-4l-1-1-3 2h0l3-6c-1-1-1-2-2-3l-2 3c-1-2-1-4-1-6 0-3-1-6-2-8-1-3-3-4-6-4l-1-1c1 0 2 0 3-1h2l4-2c1 0 3 0 4-1l3-2 6-4z" class="h"></path><path d="M219 375c4-1 9-1 13-1 3-1 6-2 9-1l-14 3c-8 1-14 3-19 10-2 2-4 5-5 8l-2 3c-1-2-1-4-1-6l1-1c1-4 6-8 9-11 1 0 2 0 3-1v-1l-1-1 7-1z" class="G"></path><path d="M237 376h1 4l2 1-1 1-16 8v-1l-1-1 1-1c-3 1-6 3-8 4-3 1-4 3-7 3-1 1-1 2-2 2 0-1-1-1-1-2 1-1 2-2 4-3 1 0 1-1 2-1 1-1 2-3 4-4l1 1 1-1c1 0 2-1 3-1 1-1 1 0 2-1h2l2-1s1 0 2-1h2c1-1 2-1 3-2z" class="S"></path><path d="M210 392c1 0 1-1 2-2 3 0 4-2 7-3 2-1 5-3 8-4l-1 1 1 1v1c-4 3-10 7-14 11l-8 11v-2c0-1 1-3 1-4l-1-1-3 2h0l3-6c2-1 3-3 5-5z" class="h"></path><path d="M210 392c1 0 1-1 2-2 3 0 4-2 7-3 2-1 5-3 8-4l-1 1c-8 4-15 11-21 17h0l-3 2h0l3-6c2-1 3-3 5-5z" class="C"></path><path d="M213 368l-1 3 1 1h2l-2 2h4v1h2l-7 1 1 1v1c-1 1-2 1-3 1-3 3-8 7-9 11l-1 1c0-3-1-6-2-8-1-3-3-4-6-4l-1-1c1 0 2 0 3-1h2l4-2c1 0 3 0 4-1l3-2 6-4z" class="W"></path><path d="M213 368l-1 3 1 1h2l-2 2h4v1h2l-7 1-18 1h2l4-2c1 0 3 0 4-1l3-2 6-4z" class="Z"></path><path d="M213 368l-1 3 1 1h2l-2 2c-3 0-6 1-9 0l3-2 6-4z" class="E"></path><defs><linearGradient id="e" x1="470.401" y1="473.388" x2="485.634" y2="474.576" xlink:href="#B"><stop offset="0" stop-color="#393133"></stop><stop offset="1" stop-color="#544943"></stop></linearGradient></defs><path fill="url(#e)" d="M477 375h1c0 1 0 1 1 2 0-1 1-2 2-2v-34h0l-2 163 1 42v19c-1 5-3 9-3 14-2-5-1-11-1-16v-24-26-26-67l1-45z"></path><path d="M522 438h1l-1 163h0l-1-2v6c0 2 0 4-1 5v4c-1 1-1 2-1 4v1c0 1 0 1-1 2v1 2 1c-1 0-1 1-1 2h0-1v-4c1-2 1-3 1-5 1-4 1-9 1-14v-22-17c0-3 0-6-1-9v-5-16c0-2 1-11 0-13l-1-14c0-2 1-4 1-7h-2c-1-2 0-4 0-6v-9-7-2-1l2-2v-1h0l1 1 1-1c1 1 1 0 1 1l1 1h0v-2-3c0-1 1 0 1-1v-7-24z" class="d"></path><path d="M236 401c1-1 2-4 2-6l1-1c0-1 2-2 3-3 3-4 6-5 11-7 1-1 3-1 5-2l-1 1c-1 1-1 2-2 3-1 2-3 3-5 6l1 1-1 1 1 1c-3 3-2 7-3 10 0 10 2 16 9 23l2 2v1c-3-2-5-4-8-7-1-2-3-5-5-7-4-5-12-7-18-6-8 1-15 7-18 13-5 8-8 17-10 25 0 1-1 8-1 8l-1 3v1h0c0-2 0-4-1-6h0c-1-13 2-26 8-37 1-3 3-5 5-8 1 0 4-3 5-5 7-5 13-5 21-4z" class="D"></path><path d="M229 406c-4 0-7 1-10 3h-1v-1h0l1-1 2-1c1 0 1-1 3-1h1v-1h2 1 1c1-1 3 0 4-1l1-1 1 1c1 0 2 1 3 1s1 0 2-1h1l2 1-1 2h-5-8z" class="C"></path><path d="M253 385c1-1 3-2 4-2-1 1-1 2-2 3-1 2-3 3-5 6l1 1-1 1 1 1c-3 3-2 7-3 10l-3 3v5c0-1 0-2-1-3v-3h-1c0 2 1 3 0 4l-1 1v-1-4h-2-6-1l-4-1h8 5l1-2c0-2 1-6 2-8l3-3c2-3 3-6 5-8z" class="J"></path><path d="M245 408c1-3 1-7 2-10 1-2 1-3 3-4l1 1c-3 3-2 7-3 10l-3 3z" class="I"></path><path d="M236 401c1-1 2-4 2-6l1-1c0-1 2-2 3-3 3-4 6-5 11-7 1-1 3-1 5-2l-1 1c-1 0-3 1-4 2-2 2-3 5-5 8l-3 3c-1 2-2 6-2 8l-2-1h-1l-4-2z" class="T"></path><path d="M241 403h1v-1l-1-1c0-3 0-5 1-7 3-5 6-8 11-9-2 2-3 5-5 8l-3 3c-1 2-2 6-2 8l-2-1z" class="F"></path><defs><linearGradient id="f" x1="501.374" y1="781.088" x2="528.605" y2="743.044" xlink:href="#B"><stop offset="0" stop-color="#d70607"></stop><stop offset="1" stop-color="#ee3b35"></stop></linearGradient></defs><path fill="url(#f)" d="M490 680c3 8 4 15 7 22l15 53c4 10 6 20 11 30h1c1-3 2-7 3-10l6-16v2h1v-1-2l1-1c0-1 0-2 1-4h0l1-2v-1c1-2 0-2 2-4l-4 12-1 5 1 1v3c-2 6-4 12-5 18-1 5-1 9 0 13 1 1 1 3 1 4-1 0-2 0-3-1l-3-2c-2-1-3-3-4-4l-4-4c-1-3-2-6-4-8v-1l-6-18c-2-3-3-8-4-12h1l-4-15c-2-5-3-10-4-14s-2-7-3-11l1-1c0-1-1-3-1-4-2-4-4-10-4-14h0l1 1 1 5 8 24v1h3c0-2-1-3-1-4s0-1-1-2v-2s-1-1-1-2v-2c-1-1-1 0-1-1s0-2-1-3c0-2 0-3-1-5l-1-3c0-1-1-1-1-2v-2c-1-1-1-3-2-4 1-1 0-2 0-2 0-1-1-2-1-3v-2c-1-2-1-3-1-5z"></path><path d="M533 759v2h1v-1-2l1-1c0-1 0-2 1-4h0l1-2v-1c1-2 0-2 2-4l-4 12-1 5c-2 5-3 10-5 14v-2-1c-1 3-3 9-5 11 1-3 2-7 3-10l6-16z" class="J"></path><path d="M503 752h1l-4-15c-2-5-3-10-4-14s-2-7-3-11l1-1 17 61h0c-1-1-2-1-2-3l-1-2v-3h-1c-2-3-3-8-4-12z" class="D"></path><defs><linearGradient id="g" x1="539.108" y1="370.326" x2="564.855" y2="373.735" xlink:href="#B"><stop offset="0" stop-color="#211e23"></stop><stop offset="1" stop-color="#554741"></stop></linearGradient></defs><path fill="url(#g)" d="M550 327v-1c-1-3 0-8 0-11 0-10-1-21 0-31 0-3 1-5 2-8 1 5 1 8 2 13v5 3l1 23v19 110l-1 35h0 0l-1-1v-5l-1-1h0v2c-1-5 0-9-1-14 0 2 0 3-1 4v-30-28h1l-1-21v-32-19-12z"></path><path d="M555 449l-1 35h0 0l-1-1v-5l-1-1h0v2c-1-5 0-9-1-14v-9l1 5v1 2 2l1-5h0 0c1-1 1-7 1-8 0-2 0-2 1-4z" class="j"></path><path d="M550 327h1c1 2 1 9 1 12v15c1 8 0 17 0 24v37l-1 1c-1-1 0-4 0-5l-1-21v-32-19-12z" class="g"></path><path d="M550 327h1c1 2 1 9 1 12-2 2-1 3-1 5 0 4 0 10-1 14v-19-12z" class="V"></path><path d="M292 251c1 0 3 0 4-1l1-1 1 1s-1 1-1 2c1 1 3 2 4 3h0c2 3 4 5 5 8 0 1 0 1 1 2 0 3-2 4-3 6-1-1-2-1-3-2l1 4-1 2-1 1h0l1 2h-1l-1 3v1h1v1s-1 1-1 0h-7l-2-1v3 1l-2-1v1l-9-5h-1c-2-1-6-1-8-3-1 0-2-1-2-2-3 0-6-1-9-3l-5-1v-1c2-1 7-2 9-2 6-2 12-4 17-7l-2-2 2-2 3-2 4-4h1c2 0 2 0 4-1z"></path><path d="M280 258l3-2v3-1l1 1-4 3-2-2 2-2z" class="J"></path><path d="M292 251c1 0 3 0 4-1l1-1 1 1s-1 1-1 2c1 1 3 2 4 3h0c-4-1-6-1-9 0s-6 3-8 4l-1-1v1-3l4-4h1c2 0 2 0 4-1z" class="X"></path><path d="M259 273l2 1c7 0 16-4 22-6 4-1 8-3 11-3s5 2 7 4l1 4-1 2-1 1h0l1 2h-1l-1 3v1h1v1s-1 1-1 0h-7l-2-1v3 1l-2-1v1l-9-5h-1c-2-1-6-1-8-3-1 0-2-1-2-2-3 0-6-1-9-3z" class="I"></path><path d="M268 276c10 2 21 6 31 6h1v1s-1 1-1 0h-7l-2-1v3 1l-2-1v1l-9-5h-1c-2-1-6-1-8-3-1 0-2-1-2-2z" class="K"></path><path d="M279 281c2-1 3 0 5 0l6 1v3 1l-2-1v1l-9-5z" class="N"></path><path d="M307 389c10 3 21 12 29 19 11 10 23 21 31 34l3 4c1 1 5 6 6 8h-1l-2-3c0 2 2 5 1 7l-1-1v2 9c-1 1-1 1-2 1l-7-1c-5-2-9-5-12-11h-1l-1-1v1 2c-2-3-3-7-2-10h0c1-1 1-2 1-3 0-5-2-10-4-15l-6-15c-6-8-15-14-23-20-3-2-7-5-9-7z"></path><path d="M367 442l3 4-1 5-3-6c1-1 1-2 1-3z" class="E"></path><path d="M349 446c1 4 1 8 3 11h-1l-1-1v1 2c-2-3-3-7-2-10h0c1-1 1-2 1-3z" class="I"></path><path d="M370 446c1 1 5 6 6 8h-1l-2-3c0 2 2 5 1 7l-1-1v2c-1-1-1-1-1-2 0-2-1-4-3-6l1-5z" class="W"></path><path d="M512 430l1-1v-4h0c2 4 0 19 1 21v40c0 5-1 12 1 18v-3h2c0 3-1 5-1 7l1 14c1 2 0 11 0 13v16 5c1 3 1 6 1 9v17 22c0 5 0 10-1 14 0 2 0 3-1 5v4c0 3 0 5-2 7 0 4 1 8 0 12v2l-1-1v-1-1h0l1-6-1 1h0c-1-1-2-1-2-2v-2c-1-1 0-2 0-3-1-1-1-7-1-8v-1c1-1 1-4 1-5 1-1 0-5 0-6-1-1 0-11 0-12v-2-6c-1 0-1-1-1-1 1-2 0-3 0-4 1-4 1-9 0-13v-2c1-4 1-11 0-16v-13-4-4h1v-6c-1-1-1-2-1-2 1-2 0-2 1-3l1 1v-2-20c1-2 0-14 0-17-1-1 0-2 0-3v-13-9-7-7-18z" class="j"></path><path d="M515 501h2c0 3-1 5-1 7l1 14-1 4v-8l-1 4h0v-18-3z" class="f"></path><path d="M517 556c1 3 1 6 1 9v17 22c0 5 0 10-1 14 0 2 0 3-1 5v4c0 3 0 5-2 7 0-4 1-8 2-12 1-3 1-7 1-11v-21-34z" class="g"></path><path d="M346 451c-1-3-2-5-2-7h1c0 1 1 3 2 4l1 1c-1 3 0 7 2 10v-2-1l1 1h1c3 6 7 9 12 11l7 1c1 0 1 0 2-1l1-1c0 1 1 1 1 2s0 2 1 2c3 4 4 8 6 12 2 3 7 9 8 13h-1c-3-4-6-7-9-11v1c0 1 1 2 1 3h1c1 1 2 2 2 3 1 1 1 2 1 3 1 0 1 1 1 2l1 1c0 1 1 2 1 3l2 1 7 10-2 2 4 6c-1 0-2 0-3 1l-5-7c-1-2-2-4-4-6l-1-1c-2-2-4-6-6-7 0-2-1-2-2-4-1 0-1 0-2 1h0l-4-7c-1-1-2-2-4-3-2-2-5-4-8-5-1-2-4-3-6-4-3-1-7-3-11-4l-5-2 1-1h5l-5-2h-2c-5-1-9-3-14-2-2-1-3-1-5-1l3-1h-1v-2c2-1 4-1 6-1l-1 2h0 4 1c2-1 4 0 6 0l2-1c1-1 2-1 3-1 1-1 2-1 3-2v-1h1v-7l1-1z" class="N"></path><path d="M346 451c-1-3-2-5-2-7h1c0 1 1 3 2 4l1 1c-1 3 0 7 2 10h0v1c1 4 4 4 6 7v1c2 0 3 2 4 3l-1 1c-1-1-1-1-2-1v-1h-2-2c-1-2-3-3-4-6l-3-13z" class="G"></path><path d="M326 462l-1 2h0 4 1c2-1 4 0 6 0l2-1c1-1 2-1 3-1 1-1 2-1 3-2v-1h1v1c0 2 0 2-2 4-1 0-1 1-2 2-2 0-4 0-6 1l-1-1h-7 0 1 1 2l1 1h0c2 0 3 0 4 1l3 1h-2c-5-1-9-3-14-2-2-1-3-1-5-1l3-1h-1v-2c2-1 4-1 6-1z" class="J"></path><path d="M326 462l-1 2h0 4 1c2-1 4 0 6 0l2-1c1-1 2-1 3-1 1-1 2-1 3-2v-1h1v1c-2 3-4 4-8 5-5 1-11 0-16 0h-1v-2c2-1 4-1 6-1z" class="H"></path><path d="M344 471c16 5 27 15 39 26l1 3c-4-3-8-7-12-10-1-1-2-2-4-3-2-2-5-4-8-5-1-2-4-3-6-4-3-1-7-3-11-4l-5-2 1-1h5z" class="l"></path><path d="M372 490c4 3 8 7 12 10l-1-3 5 4 2 1 7 10-2 2 4 6c-1 0-2 0-3 1l-5-7c-1-2-2-4-4-6l-1-1c-2-2-4-6-6-7 0-2-1-2-2-4-1 0-1 0-2 1h0l-4-7z" class="W"></path><path d="M383 497l5 4 2 1 7 10-2 2-11-14-1-3z" class="K"></path><path d="M682 161l-8-11c-14-19-34-36-57-41-9-2-18-2-26-3 14-2 26-2 40 1 21 6 39 19 54 36 4 5 8 10 11 16h0c0 2 1 2 1 3 1 1 1 1 1 2-1 0-2-1-2-2v-1c-1 0-1-1-2-1l2 3-1 1 1 1c-2 2-4 4-7 5l-1-1h0c-2-3-2-6-4-8-1 2 1 2 1 4h-1c0-2-1-2-2-4z" class="v"></path><path d="M684 161c-2-4-5-8-6-11 2-1 3-2 5-3 5 2 8 9 11 13l2 3-1 1 1 1c-2 2-4 4-7 5l-1-1h0c-2-3-2-6-4-8z" class="n"></path><path d="M791 334h0c0-2-1-4-1-6l1 1 3-3c2-1 3-4 3-6h1v6c0 1 0 1 2 2v-3h2l1-1v4 6c2 12 8 23 17 31 12 10 27 10 42 12h-1c-9 0-21 0-28 7-2 3-3 7-3 11 0 6 4 13 9 17 3 2 6 4 9 5h1c-6 0-14-3-18-7-2-2-4-4-5-6-2-3-3-6-4-9-1-1 0-1-1-2 0-1-1-3-2-4v-1c0-1-1-2-1-2l-2-2v-2l-3-3 1-1-1-1c-2 0-5-1-8-1l-1-2h6c2 0 7 1 9 0l-1-1 1-1h3 1c-2-2-3-1-4-3h-1 0l-2-1-1-1-2-3c-1 0-2-1-2-1l-1-2-1-1c-4-3-8-8-11-12-3-5-6-9-7-14z" class="Z"></path><path d="M800 328v-3h2l1-1v4 6-4c-3 3-1 6-2 9h-1v-11z" class="K"></path><path d="M823 372c1 0 2 1 3 1 2 1 4 1 7 2 1 0 3 1 5 1l-9 2-3-1h-1c-1-1-3-1-5-1h0l-1-1 1-1h0-1l-1-1 1-1h3 1z" class="m"></path><path d="M822 395c1-4 1-8 3-11s3-4 6-5c0 2-2 4-3 6-2 2-2 5-3 8v1c0 4 2 6 1 10-2-3-3-6-4-9z" class="M"></path><path d="M819 374h1 0l-1 1 1 1h0c2 0 4 0 5 1h1l3 1h3l-1 1c-3 1-4 2-6 5s-2 7-3 11c-1-1 0-1-1-2 0-1-1-3-2-4v-1c0-1-1-2-1-2l-2-2v-2l-3-3 1-1-1-1c-2 0-5-1-8-1l-1-2h6c2 0 7 1 9 0z" class="E"></path><path d="M819 374h1 0l-1 1 1 1h0c2 0 4 0 5 1h1-13c-2 0-5-1-8-1l-1-2h6c2 0 7 1 9 0z" class="Z"></path><path d="M791 334h0c0-2-1-4-1-6l1 1 3-3c2-1 3-4 3-6h1v6c0 1 0 1 2 2v11h1c1 5 2 11 4 16 2 2 3 3 4 5-4-3-8-8-11-12-3-5-6-9-7-14z" class="C"></path><defs><linearGradient id="h" x1="677.222" y1="252.748" x2="637.428" y2="267.435" xlink:href="#B"><stop offset="0" stop-color="#332a29"></stop><stop offset="1" stop-color="#4d4341"></stop></linearGradient></defs><path fill="url(#h)" d="M690 227c1 0 1 0 3 1v-1 3c1 2 0 4 0 6-1 2-3 4-2 6-3 5-5 9-7 14l-5 6c-7 7-13 12-21 16-5-1-9 2-13 1h-5c-1-1-2-1-3-1h-1-1c-2 0-3 0-5 2l1-3 2-1c2-2 2-3 3-6v-3c-1-1-1-2-2-3v-2h3l-5-2c3-2 6-1 8-3h2v1c1-1 2-1 3-1 5-3 11-5 16-7 1-1 3-1 5-3l4-3h3 1v1l1 1-1 1v3 1h1v1l1 1h1 1c1-1 2-3 3-4s1-2 1-2l1-1 5-9v-2c1-2 1-2 3-4v-3l-1-1z"></path><path d="M640 257h2v1c1-1 2-1 3-1h4 2-1l-1 1h-1c-1 0-2 1-3 2-1-1 0-1-1 0h-3-1v-3z" class="V"></path><path d="M634 264v-2h3c3 3 4 5 4 9 0 2-1 5-2 7h-2-1-1c-2 0-3 0-5 2l1-3 2-1c2-2 2-3 3-6v-3c-1-1-1-2-2-3z" class="n"></path><defs><linearGradient id="i" x1="663.132" y1="244.618" x2="674" y2="269.05" xlink:href="#B"><stop offset="0" stop-color="#e76666"></stop><stop offset="1" stop-color="#f79b99"></stop></linearGradient></defs><path fill="url(#i)" d="M690 227c1 0 1 0 3 1v-1 3c1 2 0 4 0 6-1 2-3 4-2 6-3 5-5 9-7 14l-5 6c-7 7-13 12-21 16-5-1-9 2-13 1h-5c-1-1-2-1-3-1h2 1c1 0 3-1 4-1 5-1 10-3 14-6 16-8 28-23 33-40v-3l-1-1z"></path><path d="M432 507h1l-1-2c0-1 0-3-1-4 0-1 0-2-1-3 0-1-1-3-1-5 0-1-1-1-1-2 0-2-1-3-1-5 1-1 1-1 3-1 1 1 1 2 2 4v2l1 1c0 1 1 3 0 4l1 1c0 1 0 1 1 2v3l2 5v2s1 1 1 2c1 1 1 3 2 5v1 2l2 3c0 2 0 4 1 6l1-1c2 5 3 11 5 16l8 28 28 92 3 12c1 2 2 4 2 5 0 2 0 3 1 5v2c0 1 1 2 1 3 0 0 1 1 0 2 1 1 1 3 2 4v2c0 1 1 1 1 2l1 3c1 2 1 3 1 5 1 1 1 2 1 3s0 0 1 1v2c0 1 1 2 1 2v2c1 1 1 1 1 2s1 2 1 4h-3v-1l-8-24-1-5-1-1h0l-17-53-3-11-7-23c-1-4-2-8-4-12l-26-87z" class="O"></path><defs><linearGradient id="j" x1="347.753" y1="128.161" x2="393.661" y2="157.173" xlink:href="#B"><stop offset="0" stop-color="#dececa"></stop><stop offset="1" stop-color="#fef4f5"></stop></linearGradient></defs><path fill="url(#j)" d="M319 174c3-7 7-13 11-19 13-19 31-35 53-44 15-7 34-8 50-4-8 0-16 1-25 2-21 4-38 17-52 33-8 10-15 20-19 33-2 4-3 9-3 14l1 2c-1 0-1 0-2-1-1 1-2 3-1 4 0 2 0 3 2 4 2 2 4 3 6 6-3 0-4-1-7 0l-2 2v-1l1-1c1-2 2-2 4-2 0-1-2-2-3-3l-2-2c-2-4 1-16 2-20v-3l1-1v-1h0l-1-1-4-1c-1-1-1-2-2-3l-1 1-1 1h-1c-1 1-3 5-5 5z"></path><path d="M331 160c0 1 0 1-1 2 0 0-1 1-1 2v2h4c1 0 2 1 3 1 1-2 0-1 1-2v-1l1 1-5 12v-3l1-1v-1h0l-1-1-4-1c-1-1-1-2-2-3l-1 1-1 1h-1c2-3 4-7 7-9z" class="O"></path><path d="M331 160c2-4 6-7 9-11l3-3c2 1 2 1 3 3-1 3-3 5-4 7-2 3-3 6-4 9h0l-1-1v1c-1 1 0 0-1 2-1 0-2-1-3-1h-4v-2c0-1 1-2 1-2 1-1 1-1 1-2z" class="q"></path><defs><linearGradient id="k" x1="777.887" y1="376.737" x2="755.096" y2="404.083" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#1c0002"></stop></linearGradient></defs><path fill="url(#k)" d="M758 376c2-1 10-1 12 0h9c2 1 4 2 5 3s0 0 2 1l3 3c-2 0-5-1-7-2l-1 1c11 3 20 13 28 21-1 1-1 1-3 2l-1-1c-5-3-14-3-20-1h0l-1-1v-1c-2-1-3-3-4-4-1-2-3-4-5-6-1-2-3-2-5-3-1-1-2-2-4-2h-1c-5-2-10-3-15-3h-11c-5 1-11 2-15 4-1 1-2 1-3 2-5 1-7 3-11 7l-13 8-9 9-2-3 1-2s-1 0-2 1h-1l-1-1c6-6 13-11 20-16l-1-1 2-2c1-1 2-1 4-2 3-1 5-3 8-4l1-1-1-1h0c4-2 9-2 13-3 4 0 7-1 11-2h18z"></path><path d="M772 385l6 3 6 6c2 1 3 2 5 3l-3 3-2-2h0l1 2-1 1v1-1c-2-1-3-3-4-4-1-2-3-4-5-6 0-2-2-3-2-4-1 0-1-2-1-2z" class="f"></path><path d="M772 385l6 3c1 5 5 7 6 13-2-1-3-3-4-4-1-2-3-4-5-6 0-2-2-3-2-4-1 0-1-2-1-2z" class="H"></path><path d="M758 376c2-1 10-1 12 0h9c2 1 4 2 5 3s0 0 2 1l3 3c-2 0-5-1-7-2l-1 1c-8-3-15-5-23-6z" class="B"></path><path d="M770 376h9c2 1 4 2 5 3s0 0 2 1l3 3c-2 0-5-1-7-2l-12-5z" class="T"></path><path d="M742 379c9-1 20 1 28 5 1 0 2 0 2 1 0 0 0 2 1 2 0 1 2 2 2 4-1-2-3-2-5-3-1-1-2-2-4-2h-1c-5-2-10-3-15-3 2-1 3-1 5-1h1 0l-1-1c-1 0-3 0-4-1h-1l-8-1z" class="J"></path><path d="M770 384c1 0 2 0 2 1 0 0 0 2 1 2 0 1 2 2 2 4-1-2-3-2-5-3-1-1-2-2-4-2 0-1-1-1-2-1v-1h4 2z" class="G"></path><defs><linearGradient id="l" x1="710.316" y1="397.365" x2="705.669" y2="390.674" xlink:href="#B"><stop offset="0" stop-color="#fd625f"></stop><stop offset="1" stop-color="#ef8f8f"></stop></linearGradient></defs><path fill="url(#l)" d="M726 381c5-1 11-1 16-2l8 1h1c1 1 3 1 4 1l1 1h0-1c-2 0-3 0-5 1h-11c-5 1-11 2-15 4-1 1-2 1-3 2-5 1-7 3-11 7l-13 8-9 9-2-3 1-2s-1 0-2 1h-1l-1-1c6-6 13-11 20-16l-1-1 2-2c1-1 2-1 4-2 3-1 5-3 8-4l1-1c3-1 5-1 9-1z"></path><path d="M717 382c3-1 5-1 9-1l-4 2c-6 2-13 5-19 9l-1-1 2-2c1-1 2-1 4-2 3-1 5-3 8-4l1-1z" class="H"></path><path d="M699 399h2c-1 2-2 3-3 4l-1 1-9 9-2-3 1-2 12-9z" class="W"></path><path d="M699 399c2-2 5-3 7-4 6-4 11-6 18-8-1 1-2 1-3 2-5 1-7 3-11 7l-13 8 1-1c1-1 2-2 3-4h-2z" class="B"></path><path d="M726 381c5-1 11-1 16-2l8 1h1c1 1 3 1 4 1l1 1h0-1c-2 0-3 0-5 1h-11c-1-1-2-1-4 0h-1c-1 0-3-1-5-1v-1c-2 1-4 1-6 2h-1l4-2z" class="M"></path><defs><linearGradient id="m" x1="537.108" y1="457.604" x2="547.392" y2="457.396" xlink:href="#B"><stop offset="0" stop-color="#362f2e"></stop><stop offset="1" stop-color="#534946"></stop></linearGradient></defs><path fill="url(#m)" d="M541 306c1 1 0 7 1 9 1 0 2 0 3-1v-5c1 4 0 10 0 14v40 105 29c0 4 1 10 0 14l-1 1c0 1 0 2-1 3v-2c1-1 1-2 0-4l-1 1c0 1 0 5 1 7-1 6-1 12-1 18l-1 57v12h1l1 1-2 1h-1c0-1 0-1-1-2v-18-68-14l1-27v-61l1-110z"></path><defs><linearGradient id="n" x1="505.646" y1="158.851" x2="617.96" y2="225.809" xlink:href="#B"><stop offset="0" stop-color="#cfbeba"></stop><stop offset="1" stop-color="#faece8"></stop></linearGradient></defs><path fill="url(#n)" d="M541 141c14 29 34 57 66 69 4 1 9 2 13 3 3 0 5 0 7 1h0l-2 2-6 1c-8 1-16 2-23 6-2 1-4 2-6 4v1c0-2 2-6 1-8-2-8-10-14-16-18-3-1-5-3-8-4-1-1-2-1-4-1-3 2-4 9-5 13l-1-1v2c-1 1-1 1-1 2v3h-1v2 2h-1c-1-1 0-5 1-6 0-2 1-4 0-5v-1c1-7 2-13 2-19 0-8-3-18-9-23v-1c0-3-2-3-4-5v-1c-1-1-1-1-1-3 1 0 2 0 3 1v-1l-1-1-2-1h-1c-1 0-1 0-2-1-2-1-4 0-6 0h-2v1h-2l-1-1h-2v-1c-2-1-3-2-6-3-1 1-2 1-3 1l-1 1h-1l-1-1 1-1h1 0l-3-1v-5c1 0 2 0 2-1h1v1c2 0 3 1 4 2s2 0 4 0l6 1c3 0 5 1 8 2h1c1 1 2 1 3 1 0-1 0-2-1-3-1-2-1-3-1-4v-1z"></path><defs><linearGradient id="o" x1="600.623" y1="385.661" x2="569.156" y2="390.422" xlink:href="#B"><stop offset="0" stop-color="#0e0a09"></stop><stop offset="1" stop-color="#3e3432"></stop></linearGradient></defs><path fill="url(#o)" d="M561 363v-26c-1-9-3-17-4-26 0-3-1-6-1-9v-1h0c0 2 0 1 1 3 0 0 0 2 1 3l1 9 6 18c2 9 5 17 8 25 2 6 4 13 7 19 7 16 17 33 27 46l1 1s-2 3-2 4l-5 10c-1 3-3 5-4 8-2 3-3 7-4 11 0 3-1 5 1 8h0c-2 0-2 0-3-1l-1 1-1-1 1-2c0-5 2-10 3-15v-13c-1-8-6-15-10-23-3-6-5-13-8-20-4-12-7-24-9-36-1-4-2-7-3-11 0-1 0-5-1-6 0 2 0 5 1 7 0 4 1 9 1 13l-1 1v-1-7c-1-2-1-4-1-6v-1 11c0 2 0 5-1 7h0z"></path><defs><linearGradient id="p" x1="228.766" y1="307.877" x2="204.945" y2="326.295" xlink:href="#B"><stop offset="0" stop-color="#8c0b0a"></stop><stop offset="1" stop-color="#ce1616"></stop></linearGradient></defs><path fill="url(#p)" d="M214 287l4 1 4 1v1c1 1 2 2 2 3h1c1 2 1 2 3 3h0c3 0 5 2 7 4 3 3 5 7 6 11 0 2-1 4 1 6-2 14-6 27-15 38-1-1-1-3-1-4l-1-1c-3 3-6 7-10 10 1-2 4-5 5-8-1 0-2-1-4-2 5-9 6-19 5-29-1-1-1-3-1-4-2-7-5-11-11-15v-1h0c-4-2-10-5-14-5h-3-1l-1 2c-1-1-1-2-1-3-2-2-5-4-8-5h0c1-1 4-1 6-2 7 0 13 1 20 1h0c2-1 5-1 7-2z"></path><path d="M221 321c1 2 2 4 2 6 1 7 1 19-3 25-1 0-2-1-4-2 5-9 6-19 5-29z" class="b"></path><path d="M218 291l6 2h1c1 2 1 2 3 3h0c4 4 7 9 8 16h-1 1v3s-1-1-1-2c-4-1-8-5-13-6h-1c-1-2-3-3-3-5h0c2-1 2-2 3-4-1-1-2-2-2-4 1-1 0-2-1-3z" class="P"></path><defs><linearGradient id="q" x1="243.998" y1="313.86" x2="215.923" y2="333.163" xlink:href="#B"><stop offset="0" stop-color="#d22322"></stop><stop offset="1" stop-color="#eb3f40"></stop></linearGradient></defs><path fill="url(#q)" d="M228 296c3 0 5 2 7 4 3 3 5 7 6 11 0 2-1 4 1 6-2 14-6 27-15 38-1-1-1-3-1-4l-1-1c2-4 4-7 6-11 3-7 8-20 5-27-1-7-4-12-8-16z"></path><path d="M214 287l4 1 4 1v1c1 1 2 2 2 3l-6-2c1 1 2 2 1 3 0 2 1 3 2 4-1 2-1 3-3 4h0c-4-3-9-7-13-8-5-1-9-1-14 2l-1 2c-1-1-1-2-1-3-2-2-5-4-8-5h0c1-1 4-1 6-2 7 0 13 1 20 1h0c2-1 5-1 7-2z"></path><path d="M214 287l4 1 4 1v1c1 1 2 2 2 3l-6-2-11-2h0c2-1 5-1 7-2z" class="b"></path><defs><linearGradient id="r" x1="423.27" y1="394.349" x2="402.037" y2="399.087" xlink:href="#B"><stop offset="0" stop-color="#fe7977"></stop><stop offset="1" stop-color="#efcfcb"></stop></linearGradient></defs><path fill="url(#r)" d="M403 305l1 1 1 1 1 1c-11 16-10 37-7 56 2 14 6 28 10 43l23 81 7 24c2 5 4 11 5 15l-1 1c-1-2-1-4-1-6l-2-3v-2-1c-1-2-1-4-2-5 0-1-1-2-1-2v-2l-2-5v-3c-1-1-1-1-1-2l-1-1c1-1 0-3 0-4l-1-1v-2c-1-2-1-3-2-4-2 0-2 0-3 1 0 2 1 3 1 5 0 1 1 1 1 2 0 2 1 4 1 5 1 1 1 2 1 3 1 1 1 3 1 4l1 2h-1l-28-98-2-1c-1 3 1 8 1 12h0l-2 2-4-13h-1c-1-6-3-11-5-15l1-3-1-5c0-2 0-2 1-4v-1l-1-5-1-5c-3-11-4-23-2-34h0l1-3v-2c1-2 1-3-1-5l3-5 5-10h2c2-3 3-5 5-7z"></path><path d="M396 312h2c-2 5-4 10-5 15v-2c-1-1-1-2-2-3l5-10z" class="D"></path><path d="M391 322c1 1 1 2 2 3v2c-3 23 2 45 8 67l3 15-2-1c-1 3 1 8 1 12h0l-2 2-4-13h-1c-1-6-3-11-5-15l1-3-1-5c0-2 0-2 1-4v-1l-1-5-1-5c-3-11-4-23-2-34h0l1-3v-2c1-2 1-3-1-5l3-5z" class="H"></path><path d="M392 391l-1-5c0-2 0-2 1-4 2 13 7 25 11 38h0l-2 2-4-13h-1c-1-6-3-11-5-15l1-3z" class="B"></path><path d="M391 394l1-3 5 18h-1c-1-6-3-11-5-15z" class="H"></path><defs><linearGradient id="s" x1="513.223" y1="189.302" x2="400.803" y2="197.971" xlink:href="#B"><stop offset="0" stop-color="#c0b2af"></stop><stop offset="1" stop-color="#fef6f2"></stop></linearGradient></defs><path fill="url(#s)" d="M405 212l10-2c26-7 46-28 59-50 3-5 6-11 9-17v1c0 2 0 3-1 4 6 2 13 0 19 0l2-1c2 0 3 2 5 2l1-1h1l1 3v12h-2l-1 1-1-1h-2-5c-5-3-11-6-16-4-3 0-6 2-7 4s-1 3-1 4c-8 11-8 24-6 36 1 5 3 10 3 14 0 2 0 3 1 5l1 3v3 1h-1l-1 3v1c-2-1-4-2-5-1-6 0-3 5-6 8-1-2-1-3-1-5 0-3 2-5 4-8 2-2 3-3 4-5l1 1h1c-2-7-3-21-9-26-2-1-4 0-6 1-6 4-13 10-18 15-1 2-3 4-3 6-1 2 0 5 0 7h0c-5-7-19-8-26-9l-4-1-7-2c2-1 4-1 7-2z"></path><path d="M465 227c2-2 3-3 4-5l1 1h1c0 2 1 3 1 5l-1-1h-6z" class="i"></path><path d="M660 502c2 0 2 0 4 1l2 1-3 10c0 1-1 3-1 4l-59 183c-1 1-2 2-2 3 0 2-1 3-2 5v1 1l-2 4v1c-1 0-2-1-4 0v-1l3-8c0-2 1-4 1-6v-1l10-32v-2-1h0v-1l1-1v-1c0-1 1-2 1-2v-1l2-6h0l2-5v-1-1l1-1v-1c0-1 0-1 1-2h0v-2-1h1v-1-1-1c1-1 1-1 1-2l-3 7h-1l3-8 5-17 8-26 3-3 20-61 2-6 1-2v-1l1-4 4-10v-1z" class="Z"></path><path d="M660 502c2 0 2 0 4 1l2 1-3 10c0 1-1 3-1 4l-1-1v-1h1v-1-1-1c1-1 1-2 1-3h1v-1-2l-4-1c-2 3-2 8-4 10l-1 1 1-4 4-10v-1z" class="M"></path><path d="M632 587l-8 27v-2l-1 2c0 1-1 2-2 2l8-26 3-3z" class="D"></path><path d="M621 616c1 0 2-1 2-2l1-2v2l-17 54v-2-1h0v-1l1-1v-1c0-1 1-2 1-2v-1l2-6h0l2-5v-1-1l1-1v-1c0-1 0-1 1-2h0v-2-1h1v-1-1-1c1-1 1-1 1-2l-3 7h-1l3-8 5-17z" class="K"></path><path d="M324 169h1l1-1 1-1c1 1 1 2 2 3l4 1 1 1h0v1l-1 1v3c-1 4-4 16-2 20l2 2c1 1 3 2 3 3-2 0-3 0-4 2l-1 1-2 7c0 2 0 4 1 5 0 2 1 4 1 6 0 3 0 5 1 8 1 4 2 10 3 15 2 3 3 7 6 11h-3-2v1c-1 1-2 1-2 3h0-1c0-2 1-6 0-8s-2-2-3-2c-1-1-2-2-3-2-1 4 0 8-2 12v-4l-1 1-1-2c-1 1-2 4-2 6l-1 1-1-1c0-2-1-5 0-8l2-6-1 1c-1 1-2 3-3 4l-1 2h-1l-1-2v-6h-1v3h-1l-1 2v-5l-1 1v-2h-1l1-1-1-14h0-1v-4c1-3 1-6 1-10 0-1 1-3 1-5 0-4 0-7 2-11 0-2 0-3 1-4 0-3 0-4-1-6l5-13c1-1 1-2 2-4 2 0 4-4 5-5z"></path><path d="M317 227c0-2 1-4 2-6l1 1 2 5-3 6c-1-1-1-1-1-3l-2-1 1-2z" class="B"></path><path d="M320 249v-2c0-3 0-7 2-10h3c2 1 3 2 4 2v1c-2 0-2-1-4 0l-4 8-1 1z" class="J"></path><path d="M319 254v1c3-2 4-5 6-7v9l-1 1-1-2c-1 1-2 4-2 6l-1 1-1-1c0-2-1-5 0-8z" class="G"></path><path d="M324 208l1-1 1 1v2c1-1 1-1 1-2 0 1 1 3 2 4 0 2 0 4 1 5 0 2 1 4 1 6 0 3 0 5 1 8 1 4 2 10 3 15-2-2-3-4-4-6-4-11-5-21-7-32z" class="R"></path><path d="M322 227l3 10h-3c-2 3-2 7-2 10v2c-1 1-2 3-3 4l-1 2h-1l-1-2v-6-8c2-1 2-3 3-5l2-1 3-6z" class="I"></path><path d="M317 234h3c0 2 0 3-1 4s-2 3-3 4c0 2 0 9 1 11h0l-1 2h-1l-1-2v-6-8c2-1 2-3 3-5z" class="R"></path><path d="M316 205c0 2 0 4 1 5 0-1 0-2 1-2l1-2c0-2 1-2 2-3h1c-1 2-2 3-2 5 0 1 0 0-1 1v2c-2 5-4 10-5 15v2c1 0 2 0 3-1l-1 2 2 1c0 2 0 2 1 3l-2 1c-1 2-1 4-3 5v8h-1v3h-1l-1 2v-5l-1 1v-2-1c2-1 0-8 1-11h0v-2c1-1 1-2 1-3v-1c0-1 0-2 1-3 0-1 0-3 1-4v-3c0-2 0-6 1-8h0c0-2 1-3 1-5z" class="G"></path><path d="M314 226v2c1 0 2 0 3-1l-1 2 2 1c0 2 0 2 1 3l-2 1c-1 2-1 4-3 5v8h-1v-1c-1-6 0-13 1-20z" class="W"></path><path d="M316 229l2 1c0 2 0 2 1 3l-2 1c-1 2-1 4-3 5 0-3 1-7 2-10z" class="H"></path><path d="M312 201c0 2-1 4-1 6v2h1 0c1-1 1-2 1-3s1-2 1-3v-1-1l1-3 1-1v-1c1-1 1-2 1-3h1v-1-1c1-1 1-2 1-2 1-1 1-2 2-3 0 1-2 5-2 5 1 3-1 5-1 8-1 2-1 4-2 6 0 2-1 3-1 5h0c-1 2-1 6-1 8v3c-1 1-1 3-1 4-1 1-1 2-1 3v1c0 1 0 2-1 3v2h0c-1 3 1 10-1 11v1h-1l1-1-1-14h0-1v-4c1-3 1-6 1-10 0-1 1-3 1-5 0-4 0-7 2-11z" class="C"></path><path d="M309 231l1 2v7 5l-1-14z" class="c"></path><path d="M312 201c0 2-1 4-1 6v2h1 0c1-1 1-2 1-3s1-2 1-3v-1-1l1-3 1-1v-1c1-1 1-2 1-3h1v-1-1c1-1 1-2 1-2 1-1 1-2 2-3 0 1-2 5-2 5-6 12-9 27-10 40h-1v-4c1-3 1-6 1-10 0-1 1-3 1-5 0-4 0-7 2-11z" class="b"></path><path d="M323 182c1-1 1-1 1-2l1 1-1 1v3c1 1 0 2 0 3h1c1 3 0 5 2 7v1c1 2 3 2 4 4l2-1c1 1 3 2 3 3-2 0-3 0-4 2l-1 1-2 7c-1-1-2-3-2-4 0 1 0 1-1 2v-2l-1-1-1 1c-1-1-2 0-3 1l-2 2v-2c1-1 1 0 1-1 0-2 1-3 2-5h-1c-1 1-2 1-2 3l-1 2c-1 0-1 1-1 2-1-1-1-3-1-5 1-2 1-4 2-6 0-3 2-5 1-8 0 0 2-4 2-5 1-1 2-3 2-4z" class="Y"></path><path d="M322 203c2-1 2-1 4-1 0 2-2 2-1 3 0 1 1 1 1 1v2l-1-1-1 1c-1-1-2 0-3 1l-2 2v-2c1-1 1 0 1-1 0-2 1-3 2-5z" class="J"></path><path d="M323 182c1-1 1-1 1-2l1 1-1 1v3c1 1 0 2 0 3h1c1 3 0 5 2 7v1c1 2 3 2 4 4l2-1c1 1 3 2 3 3-2 0-3 0-4 2l-1 1-2 7c-1-1-2-3-2-4v-5c1-1 2-1 2-1l1-1h-2c-1-1-4-4-5-6-1-1 0-5-1-7h0c1-2 2-4 1-5v-1z" class="M"></path><path d="M324 169h1l1-1 1-1c1 1 1 2 2 3l4 1 1 1h0v1l-1 1v3c-1 4-4 16-2 20l2 2-2 1c-1-2-3-2-4-4v-1c-2-2-1-4-2-7h-1c0-1 1-2 0-3v-3l1-1-1-1c0 1 0 1-1 2 0 1-1 3-2 4s-1 2-2 3c0 0 0 1-1 2v1 1h-1c0 1 0 2-1 3 0 1 0-1 0 1l-1 1-1 3v1 1c0 1-1 2-1 3s0 2-1 3h0-1v-2c0-2 1-4 1-6s0-3 1-4c0-3 0-4-1-6l5-13c1-1 1-2 2-4 2 0 4-4 5-5z" class="l"></path><path d="M317 178c2 1 2 1 3 2 1 4-1 6-3 9-2 2-2 5-4 8 0-3 0-4-1-6l5-13z" class="K"></path><defs><linearGradient id="t" x1="647.975" y1="271.394" x2="658.068" y2="294.741" xlink:href="#B"><stop offset="0" stop-color="#490201"></stop><stop offset="1" stop-color="#790705"></stop></linearGradient></defs><path fill="url(#t)" d="M684 256l1 1c1 0 2-1 3-1h1v3c1 2 1 4 1 7 0 2-1 9 0 11 1 1 0 2 0 4l-1 2v2h0c-1 2-2 3-3 5l-2 2-7 3c-8 3-19 6-28 4-12-1-24-7-34-13v1l1 1v1 1l-1 1c-1-1-2-1-3-1l-4-2c-3-2-5-4-8-5h0c-2-2-4-3-7-4h-1l2-1c1 0 3 1 5 1 3 0 6 1 9 1h1v-1h1c6 1 14 0 21-2l-1 3c2-2 3-2 5-2h1 1c1 0 2 0 3 1h5c4 1 8-2 13-1 8-4 14-9 21-16l5-6z"></path><path d="M684 268c1-1 2-2 4-3-1 2-1 4-2 5s-2 1-3 1h0l1-1v-2z" class="B"></path><path d="M635 280l1-1 1 1c2 2 4 1 6 1 1 0 2 1 3 1-3 1-8 3-11 2v-4z" class="M"></path><path d="M645 279c4 1 8-2 13-1-4 2-8 3-12 4-1 0-2-1-3-1-2 0-4 1-6-1 3 0 5 0 8-1z" class="l"></path><path d="M684 256l1 1c1 0 2-1 3-1h1v3c-1 2-1 4-1 6-2 1-3 2-4 3l-1-1 1-1v-1h-3c-1 0-2-1-2-3l5-6z" class="a"></path><path d="M610 279c6 1 14 0 21-2l-1 3c2-2 3-2 5-2h1 1c1 0 2 0 3 1h5c-3 1-5 1-8 1l-1-1-1 1c-4 2-8 2-13 3h-1c-2 1-5 1-7 2l1 1v1l1 1v1 1l-1 1c-1-1-2-1-3-1l-4-2c-3-2-5-4-8-5h0c-2-2-4-3-7-4h-1l2-1c1 0 3 1 5 1 3 0 6 1 9 1h1v-1h1z" class="m"></path><path d="M601 280l11 3h10-1c-2 1-5 1-7 2l1 1v1h-2c-2-1-3-2-5-3-3-1-5-2-7-4z" class="B"></path><path d="M610 279c6 1 14 0 21-2l-1 3c-4 1-8 1-13 1h-6l-3-1h1v-1h1z" class="q"></path><path d="M594 278c1 0 3 1 5 1l2 1c2 2 4 3 7 4 2 1 3 2 5 3h2l1 1v1 1l-1 1c-1-1-2-1-3-1l-4-2c-3-2-5-4-8-5h0c-2-2-4-3-7-4h-1l2-1z" class="F"></path><path d="M688 265c0-2 0-4 1-6 1 2 1 4 1 7 0 2-1 9 0 11 1 1 0 2 0 4l-1 2v2h0c-1 2-2 3-3 5l-2 2-7 3c-8 3-19 6-28 4 1 0 3 0 5-1 2 0 3-4 5-5 4-2 9-3 13-5 7-4 11-11 14-18 1-1 1-3 2-5z" class="H"></path><path d="M667 292h4v2 1c-3 1-6 2-9 2-2 1-3 1-4 0v-1l6-3 3-1z" class="L"></path><defs><linearGradient id="u" x1="511.78" y1="152.532" x2="526.586" y2="54.196" xlink:href="#B"><stop offset="0" stop-color="#e3d8d4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#u)" d="M483 143l10-25 14-48c2-7 3-15 5-22 7 32 17 63 29 93v1c0 1 0 2 1 4 1 1 1 2 1 3-1 0-2 0-3-1h-1c-3-1-5-2-8-2l-6-1c-2 0-3 1-4 0s-2-2-4-2v-1h-1c0 1-1 1-2 1v-14-2-5h-1 0l-2 1c0 1 1 5 0 7v1c0 1-1 3 0 5 0 4 1 10 0 15l-1-3h-1l-1 1c-2 0-3-2-5-2l-2 1c-6 0-13 2-19 0 1-1 1-2 1-4v-1z"></path><defs><linearGradient id="v" x1="354.619" y1="209.483" x2="373.676" y2="278.305" xlink:href="#B"><stop offset="0" stop-color="#130e0d"></stop><stop offset="1" stop-color="#504644"></stop></linearGradient></defs><path fill="url(#v)" d="M377 238v1h2c1 1 1 2 3 2l3 3v-1c-1-1-2-2-3-4l-2-2-1-1-3-3c-1-1-1-2-2-3v-1c-1 0-1 0-1-1l-1-1h0l-1-1v-1h-1v-2c-1-1-1 0-2-1v-1l-1-1-3-5c0-1 0-1-1-2h0l-4-8v-1c0-1-1-2-1-3v-1c-1-1-1-2-1-3 1 1 1 2 1 3 0 0 0 1 1 2 7 16 17 31 30 44 5 3 9 5 14 8 4 1 8 2 12 2-1 1-2 2-3 2v2h-6c-2 0-5 0-7 1-3 0-5 1-8 2-1 2-2 3-3 5 0 2 1 4 1 6h0c-1 1-2 2-3 2h-1v2h0l-1 1c-1 0-4 0-5 1-3 0-6-2-10-1-2-1-6-2-8-4h0c-6-3-12-8-16-13l-4-5c-3-4-4-8-6-11-1-5-2-11-3-15-1-3-1-5-1-8 0-2-1-4-1-6-1-1-1-3-1-5l2-7v1l2-2v3 3c1 1 2 1 2 2v1c5 1 7 8 12 9h1 1 1c0 1 0 1 1 1v-1c-1-1-1 0-1-1 3 2 5 4 9 6l2 2 1-1-6-6h1l2 2 6 6h1-1c0-1-1-2-2-3h0c3 1 6 5 8 7 1 1 4 4 6 4z"></path><path d="M389 246c5 3 9 5 14 8l-2 1c-3-1-7-1-8-2-3-2-6-2-9-3 1 0 1-1 2 0 2 0 4 0 5 1h1l1-1c-1-1-2-1-3-2 0-1-1-2-1-2zm-55-23l2-2c-1-3-2-6-2-9h1v1c1 5 2 6 4 9l2 2v4h-2c0-2-4-4-5-5z" class="e"></path><path d="M395 260l4 1c-3 0-5 1-8 2-1 2-2 3-3 5 0 2 1 4 1 6h0c-1 1-2 2-3 2h-1l-2-4c0-3 1-6 4-9 2-2 4-3 8-3z" class="n"></path><path d="M383 272l1 1h3l2 1c-1 1-2 2-3 2h-1l-2-4z" class="m"></path><path d="M331 206l2-2v3 3c1 1 2 1 2 2h-1c0 3 1 6 2 9l-2 2c1 1 5 3 5 5l-7-1c0-1-1-4-1-4 0-2-1-4-1-6-1-1-1-3-1-5l2-7v1z" class="U"></path><path d="M331 205v1c-1 6-1 11 2 16l1 1c1 1 5 3 5 5l-7-1c0-1-1-4-1-4 0-2-1-4-1-6-1-1-1-3-1-5l2-7z" class="p"></path><path d="M384 255l-3-1c-1 0-4-1-4-2 1 0 2 0 3 1 3 1 6 1 9 1h2c1 1 1 1 2 1 2 0 2 0 4 1l1-1 2 1c1-1 2 0 3 0l1 1h4v1c1 1 3 0 4 0v2h-6c-2 0-5 0-7 1l-4-1c-1 0-2-1-2-1-3-1-7-1-9-4z" class="i"></path><path d="M384 255c4 1 9 3 13 3h6 5c1 1 3 0 4 0v2h-6c-2 0-5 0-7 1l-4-1c-1 0-2-1-2-1-3-1-7-1-9-4z" class="e"></path><path d="M408 258c1 1 3 0 4 0v2h-6-6v-1l3-1h5z" class="E"></path><defs><linearGradient id="w" x1="358.552" y1="253.223" x2="350.956" y2="268.845" xlink:href="#B"><stop offset="0" stop-color="#e97c7b"></stop><stop offset="1" stop-color="#f79d9a"></stop></linearGradient></defs><path fill="url(#w)" d="M331 223s1 3 1 4c4 17 15 32 30 42 2 2 5 3 8 4 0 1 2 1 2 2 4 1 9 2 13 3l-1 1c-1 0-4 0-5 1-3 0-6-2-10-1-2-1-6-2-8-4h0c-6-3-12-8-16-13l-4-5c-3-4-4-8-6-11-1-5-2-11-3-15-1-3-1-5-1-8z"></path><path d="M361 275c1 0 2-1 3-1 0 1 0 1 1 1h1c2 1 4 1 5 2h4l-1-1c-1 0-1 0-2-1 4 1 9 2 13 3l-1 1c-1 0-4 0-5 1-3 0-6-2-10-1-2-1-6-2-8-4z" class="m"></path><defs><linearGradient id="x" x1="723.806" y1="200.349" x2="683.477" y2="185.464" xlink:href="#B"><stop offset="0" stop-color="#de3130"></stop><stop offset="1" stop-color="#fb7a76"></stop></linearGradient></defs><path fill="url(#x)" d="M696 163c1 3 3 7 6 7h1c5 9 9 18 12 27 2 7 4 15 7 23l-1 1v-1l-1 1c1 5 2 9 3 13v4l-1-3h-1c0-1-1-2-2-3l-1 8h0l-1-2-2 15 1 2h0c-1 2-1 3 0 5-1 3-1 8-3 11-1 2-3 4-4 7 0 1 0 3-1 4-2 3-4 4-4 8-1 1-1 2-2 2v1c0 1 0 2-1 3l-1 4c-1 1-1 2-2 3v2c-1-1 0-2 0-3s1-2 1-3v-3c0-1 0-2 1-3s1-1 1-3l1-1h0v-1c0-1 0-1 1-2v-2-1c-1-1-1-2-3-2l-2 3-3-3-2 3c-2 2-4 4-7 6 1-2 2-3 3-5h0v-2l1-2c0-2 1-3 0-4-1-2 0-9 0-11 0-3 0-5-1-7v-3h-1c-1 0-2 1-3 1l-1-1c2-5 4-9 7-14-1-2 1-4 2-6 0-2 1-4 0-6v-3l1-4s1-4 1-5c0-6-1-11-4-16 2-2 3-4 4-6 0-3-1-8-2-12l-3-8-2-7h0l1 1c3-1 5-3 7-5l-1-1 1-1z"></path><path d="M717 220c-1-6-1-12-3-17 0-2 0-4-1-5h1l7 22-1 1c1 5 2 9 3 13v4l-1-3h-1c0-1-1-2-2-3l-1 8h0l-1-2c1-6 1-12 0-18z" class="F"></path><path d="M719 232v-12-1l1 2c1 5 2 9 3 13v4l-1-3h-1c0-1-1-2-2-3z" class="C"></path><path d="M703 184c4 9 7 18 9 27 1 4 1 7 2 11l1 7c0 2 0 6-1 7l-1-11-2 2h0 0l-1-5c1-2 1-3 1-5l-1-4c0-1-1-4-1-5l-3-10-2-10c-1-1-1-2-1-4z" class="S"></path><path d="M710 213c2 4 3 8 3 12l-2 2h0 0l-1-5c1-2 1-3 1-5l-1-4z" class="D"></path><path d="M693 184c0-1 0-2 2-2h2l4-3c1 1 2 3 2 5h0c0 2 0 3 1 4 0 3-2 4-3 6h-1l-1 1h-3l-1 1c0-3-1-8-2-12z" class="M"></path><path d="M706 198l3 10c0 1 1 4 1 5l1 4c0 2 0 3-1 5l-2-5c-2-6-4-8-10-11h0v-3h0c0-2 1-3 2-4 0 0 1-1 1-2l5 1z" class="N"></path><path d="M706 198l3 10c-2 0-2 0-3-1 0-1 0-2-1-2 0-1-1-1-2-2l-1 1h0c-2-1-3-1-4-1h0c0-2 1-3 2-4 0 0 1-1 1-2l5 1z" class="K"></path><path d="M700 199c2 1 2 2 3 4l-1 1h0c-2-1-3-1-4-1h0c0-2 1-3 2-4z" class="G"></path><path d="M704 188l2 10-5-1c0 1-1 2-1 2-1 1-2 2-2 4h0v3h0c0 2 1 4 1 7-1 0-1 0-2 1 0 0 0 1-1 1v1s0 1-1 2c0-6-1-11-4-16 2-2 3-4 4-6l1-1h3l1-1h1c1-2 3-3 3-6z" class="R"></path><path d="M698 206c6 3 8 5 10 11l2 5 1 5h0v3l-1 1h-1-1s-1-1-1-2l-1 1-1 1-6 3c-1 2-1 3-2 5h0c-2 0-3 1-4 1h-1l-1 2c-1-2 1-4 2-6 0-2 1-4 0-6v-3l1-4s1-4 1-5c1-1 1-2 1-2v-1c1 0 1-1 1-1 1-1 1-1 2-1 0-3-1-5-1-7z"></path><path d="M701 230l4-8c2 3 3 6 4 9h-1s-1-1-1-2l-1 1-1 1-6 3 2-4z" class="C"></path><path d="M695 218c1-1 1-2 1-2v-1c1 0 1-1 1-1 1-1 1-1 2-1-1 5-1 10-2 15l1 1h0l3 1-2 4c-1 2-1 3-2 5h0c-2 0-3 1-4 1h-1l-1 2c-1-2 1-4 2-6 0-2 1-4 0-6v-3l1-4s1-4 1-5z" class="K"></path><path d="M692 240c2-4 3-8 5-12l1 1h0l3 1-2 4c-1 2-1 3-2 5h0c-2 0-3 1-4 1h-1z" class="E"></path><path d="M714 222l1-1v1h1v-2h1c1 6 1 12 0 18l-2 15 1 2h0c-1 2-1 3 0 5-1 3-1 8-3 11-1 2-3 4-4 7 0 1 0 3-1 4-2 3-4 4-4 8-1 1-1 2-2 2v1c0 1 0 2-1 3l-1 4c-1 1-1 2-2 3v2c-1-1 0-2 0-3s1-2 1-3v-3c0-1 0-2 1-3s1-1 1-3l1-1h0v-1c0-1 0-1 1-2v-2-1c-1-1-1-2-3-2l-2 3-3-3-2 3c-2 2-4 4-7 6 1-2 2-3 3-5h0v-2l1-2c0-2 1-3 0-4-1-2 0-9 0-11 0-3 0-5-1-7v-3h-1c-1 0-2 1-3 1l-1-1c2-5 4-9 7-14l1-2h1c1 0 2-1 4-1h0c1-2 1-3 2-5l6-3 1-1 1-1c0 1 1 2 1 2h1 1l1-1v-3h0l2-2 1 11c1-1 1-5 1-7l-1-7z" class="R"></path><path d="M702 235c1 2 1 3 1 6h-2l-1-2 2-4z" class="D"></path><path d="M703 242l3-3c1 2 1 6 1 9h-1c-1-1-2-4-3-6z" class="N"></path><path d="M705 231l-3 3v1l-2 4-1 1-2-1h0c1-2 1-3 2-5l6-3z" class="I"></path><path d="M705 272h1c0 3-1 7-2 10l-1 1c-1-1-1-2-3-2l1-1c2-2 3-5 4-8z" class="C"></path><path d="M704 250c1 1 2 2 3 4v6h0c-2-1-1-2-2-3v-1l-3 3 2-9z" class="I"></path><path d="M700 239l1 2h2v1c1 2 2 5 3 6h1c1 0 1 1 2 2-1 1-1 2-2 4-1-2-2-3-3-4-1-4-2-7-5-10l1-1z" class="X"></path><path d="M711 230c1 4 2 8 1 13l-1 4h-1c-2-5 0-10-2-15v-1h1 1l1-1z" class="B"></path><path d="M714 222l1-1v1h1v-2h1c1 6 1 12 0 18l-2 15c-1 5-2 10-4 15h0v-1l2-11v-1c2-7 4-18 2-26l-1-7z" class="b"></path><path d="M711 227h0l2-2 1 11c1-1 1-5 1-7 2 8 0 19-2 26v1h-1v3l-1 1c0 1 0 2-1 3-1-4 2-9 2-13 0-1 0-2 1-4 0-1 0-2-1-3 1-5 0-9-1-13v-3z" class="J"></path><path d="M702 259l3-3v1c0 4 1 8-1 11l1 2v2c-1 3-2 6-4 8l-1 1-2 3-3-3c1-1 2-2 2-3l2-3c0-1 1-3 1-4l1-3v-2c1-2 1-3 1-5v-2z" class="X"></path><path d="M699 275v2h1c-1 1-1 1-1 2h1 1v1l-1 1-2 3-3-3c1-1 2-2 2-3l2-3z" class="K"></path><path d="M697 239l2 1c3 3 4 6 5 10l-2 9v2c0 2 0 3-1 5v2l-1 3c0 1-1 3-1 4l-2 3c0 1-1 2-2 3l-2 3c-2 2-4 4-7 6 1-2 2-3 3-5h0v-2l1-2c0-2 1-3 0-4-1-2 0-9 0-11 0-3 0-5-1-7v-3h-1c-1 0-2 1-3 1l-1-1c2-5 4-9 7-14l1-2h1c1 0 2-1 4-1z" class="Y"></path><path d="M696 251l-1-1 2-2c1 0 1 0 1 1 1 1 1 0 1 1 2 2 2 8 2 10 0 1 0 1 1 1 0 2 0 3-1 5v2l-1-2v-2c0-1 1-2 0-3-1 2-1 4-2 5v2l-1-1v-3c1-2 0-8 0-10v-3h-1z" class="H"></path><path d="M697 239l2 1c3 3 4 6 5 10l-2 9v2c-1 0-1 0-1-1 0-2 0-8-2-10 0-1 0 0-1-1 0-1 0-1-1-1l-2 2 1 1v3h-1v-2h-1v6h-1c-1-2 0-6-1-7-1 1-1 2-1 3 1 8 0 15-1 23-1-2 0-9 0-11 0-3 0-5-1-7v-3h-1c-1 0-2 1-3 1l-1-1c2-5 4-9 7-14l1-2h1c1 0 2-1 4-1z"></path><path d="M417 292c2 0 4-1 7-2l-2 2c-1 1-6 3-7 3-1 1-3 2-4 3l-2 2c-2 2-4 3-6 5s-3 4-5 7h-2l-5 10-3 5c2 2 2 3 1 5v2l-1 3h0c-2 11-1 23 2 34l1 5 1 5v1c-1 2-1 2-1 4l1 5-1 3c-2-2-3-4-6-4-1 0-2 0-3 1-3 3-4 6-4 10 0 2 0 5 1 7l2 9h-1c-1-1-1-2-2-4 0-1-1-2-1-4l-5-7-1-2c0-3-2-4-2-7 0-5-4-9-5-14v-1l-2-2c-3-5-5-11-6-17-1-4-1-9-1-13v-5c1 0 1-1 2-2v-3l1-6c2-11 7-18 16-24l3-1c2-1 3-2 5-4 2 0 5-1 8-2l9-3 2 1c1-1 1-1 2-1 2-2 6-5 9-6 1 2 1 2 2 3l3-1z" class="C"></path><path d="M362 362c1 0 1 1 1 2l1 2v6 2l-1 1-2-10v-2h1v-1z" class="F"></path><path d="M361 347v8c0 2 1 4 1 7v1h-1v2l-2-7-1-5 2-2 1-4z" class="B"></path><path d="M358 353l2-2c1 2 1 5-1 7h0l-1-5z" class="T"></path><path d="M357 339v-3l1 7c1-2 2-3 3-6h0v3c-1 2 0 5 0 7l-1 4-2 2c-1-5-1-10-1-14z" class="F"></path><path d="M388 327c2 2 2 3 1 5v2l-1 3h0-1c-1 1-1 4-2 5-1 2-3 4-5 5 1-2 2-3 2-5 1-1 3-2 3-4v-5c1-1 1-2 2-3v-1l1-2zm-24 45c0-3 0-7 1-10 1 0 1-1 1-1h1c1 2 1 3 1 4v4c0 4 1 9 2 13l-1 1c-1-5-2-9-2-13l-1 1v1c-1 3 0 6-1 8l-1-1v-1l-1-3 1-1v-2z" class="G"></path><defs><linearGradient id="y" x1="364.409" y1="351.112" x2="352.091" y2="361.888" xlink:href="#B"><stop offset="0" stop-color="#d6221f"></stop><stop offset="1" stop-color="#f24e4c"></stop></linearGradient></defs><path fill="url(#y)" d="M362 376c-3-5-5-11-6-17-1-4-1-9-1-13v-5c1 0 1-1 2-2 0 4 0 9 1 14l1 5 2 7 2 10 1 3-2-2z"></path><path d="M364 379l1 1c1-2 0-5 1-8v-1l1-1c0 4 1 8 2 13 1 1 1 2 1 3 2 5 3 10 5 15 0 2 2 5 2 8l-5-7-1-2c0-3-2-4-2-7 0-5-4-9-5-14z" class="N"></path><path d="M374 306l3-1c1 0 2 1 3 1 2 1 3 2 4 3 0 2 0 3-1 5l-3 7-5 11v-1c0-6 3-11 2-16-1-1-1-1-3-1-6 6-11 14-13 23h0c-1 3-2 4-3 6l-1-7 1-6c2-11 7-18 16-24z"></path><path d="M417 292c2 0 4-1 7-2l-2 2c-1 1-6 3-7 3-1 1-3 2-4 3l-2 2c-2 2-4 3-6 5s-3 4-5 7h-2l-5 10-3 5-1 2-1-2 2-3 1-2v-1-1h1v-1h0v-1c0-1 0 0 1-1 0-1 0-1 1-2h0v-1l1-1v-1c0-1 1-1 1-2v-1c-1 1-1 2-2 3l-2 2c-3 4-4 8-6 11l-9 15c-1 1-1 2-1 3-1 1 0 1-1 2 0-2 0-2 1-3v-1c0-1 1-2 1-3h-1v-1-2l1-1v-1-1h0l5-11 3-7c1-2 1-3 1-5-1-1-2-2-4-3-1 0-2-1-3-1 2-1 3-2 5-4 2 0 5-1 8-2l9-3 2 1c1-1 1-1 2-1 2-2 6-5 9-6 1 2 1 2 2 3l3-1z" class="G"></path><path d="M380 321l3-7 1 1h1 0c0 2-1 5-3 6h-2z" class="Y"></path><path d="M399 296l2 1c-2 2-6 5-7 7-2 3-4 4-6 7h-1c-1 1-1 3-2 4h0-1l-1-1c1-2 1-3 1-5-1-1-2-2-4-3-1 0-2-1-3-1 2-1 3-2 5-4 2 0 5-1 8-2l9-3z" class="h"></path><path d="M380 306c2-1 5 0 7 0h2c0 2-1 3-2 5-1 1-1 3-2 4h0-1l-1-1c1-2 1-3 1-5-1-1-2-2-4-3z" class="J"></path><path d="M417 292c2 0 4-1 7-2l-2 2c-1 1-6 3-7 3-1 1-3 2-4 3l-2 2c-2 2-4 3-6 5s-3 4-5 7h-2l-5 10-3 5-1 2-1-2 2-3 1-2v-1l6-11c1-2 3-4 4-7-2 0-4 1-5 1 1-2 5-5 7-7 1-1 1-1 2-1 2-2 6-5 9-6 1 2 1 2 2 3l3-1z" class="L"></path><path d="M396 312c0-1 0-2 1-3 1-2 3-3 5-5 1-2 3-5 6-6 1 0 2-2 4-3h3c-1 1-3 2-4 3l-2 2c-2 2-4 3-6 5s-3 4-5 7h-2z" class="I"></path><defs><linearGradient id="z" x1="508.544" y1="179.274" x2="479.143" y2="221.114" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252222"></stop></linearGradient></defs><path fill="url(#z)" d="M476 167c3-2 6-3 10-2 5 0 10 4 13 8 12 17 11 42 12 63v20 12h-1l-5-2-4-2c-4-2-8-6-11-10-2-3-5-6-6-10v-6c1-1 2-4 2-6 1-1 1-2 2-3v-1c-1 0-1 0-1 1l-2 1v-1l-5 4c-3-4-5-10-7-15v-1c0-4-2-9-3-14-2-12-2-25 6-36z"></path><path d="M500 209c1 1 2 1 2 2l-1 4-1-6z" class="U"></path><path d="M479 197h1l3 6c-1 5 4 9 3 13h-2c-1-2-2-5-3-7v1c0 1 0 2 1 3v1c-1-1-2-2-2-3l-1 1-2-1 2-2h0v-1c0-1-1-2-1-3h4v-1s-3-6-3-7z" class="Q"></path><path d="M482 214v-1c-1-1-1-2-1-3v-1c1 2 2 5 3 7h2c1-4-4-8-3-13l1 2 3 6c0 1 1 2 1 3v1c-1 1-1 1-1 2l1 1v2c0 1 0 1 1 2 0 1 0 1-1 2l1 1c-1 1-2 3-3 3l-1 1-5 4c-3-4-5-10-7-15h3v1l2-1c-1-1-1-1 0-2h0v1l1 1 1-2h1c-1-1-1-1-1-2l1-1v1 2 2h1v-4z" class="g"></path><path d="M478 222h1c0 1 1 2 2 3v3h-1l-1-1c0-1-1-2-2-2v-2l1-1z" class="V"></path><path d="M479 197c0-1-1-2-1-3h-1v-3h1l-1-1 2-1c2-1 3-1 6 0l-1 1h1v-2h1l1 1c1 0 0 0 1 1v1l-2 1v1c0 1 1 2 2 2l3 7c1 2 1 1 1 3h-1l2 1v1c0 1 0 2 1 3s1 2 1 4h-2l-1-1c0-1 0-2-1-2v-2h-1v-2h-1l-1 1h1c0 2 1 4 1 6h-1-1c0-1-1-2-1-3l-3-6-1-2-3-6h-1z" class="E"></path><path d="M484 205l2-1c-1-4-2-8-4-11v-1h0l6 11c0 1 0 1 1 2 1-1 1-1 2-3 1 2 1 1 1 3h-1l2 1v1c0 1 0 2 1 3s1 2 1 4h-2l-1-1c0-1 0-2-1-2v-2h-1v-2h-1l-1 1h1c0 2 1 4 1 6h-1-1c0-1-1-2-1-3l-3-6z" class="Q"></path><path d="M488 195c-1 0-2-1-2-2v-1l2-1v1h2l1 1c1 1 2 2 3 4v2l3 3v2c1 2 2 4 3 5l1 6 2 7c-1 0-2 0-2-1l-1 1v1h0-3v2h-2-1l1-1c0-2 0-3-1-5v-1-1l-2-4 1 1h2c0-2 0-3-1-4s-1-2-1-3v-1l-2-1h1c0-2 0-1-1-3l-3-7z" class="g"></path><path d="M492 213l1 1h1c0 1 1 1 1 2v3h-1v-1-1l-2-4z" class="V"></path><path d="M493 204l2-1v1 1h0l1 2 1-1c1 2 2 3 2 4v3l-1-1c-1 1-1 0-2 1v-3l-1-1 2-1-1-1-1 1c-1-1-1-2-2-4z" class="Q"></path><path d="M488 195c-1 0-2-1-2-2v-1l2-1v1h2l1 1c1 1 2 2 3 4v2c0 1 1 2 2 3v1 1c1 1 1 2 1 2l-1 1-1-2h0v-1-1l-2 1c-1-2-3-8-5-9z" class="f"></path><path d="M488 214h1 1c0-2-1-4-1-6h-1l1-1h1v2h1v2c1 0 1 1 1 2l2 4v1 1c1 2 1 3 1 5l-1 1h1 2v-2h3 0v-1l1-1c0 1 1 1 2 1l3 15s0-1 1-1h3 1v20 12h-1l-5-2-4-2c-4-2-8-6-11-10-2-3-5-6-6-10v-6c1-1 2-4 2-6 1-1 1-2 2-3v-1c-1 0-1 0-1 1l-2 1v-1l1-1c1 0 2-2 3-3l-1-1c1-1 1-1 1-2-1-1-1-1-1-2v-2l-1-1c0-1 0-1 1-2v-1z" class="i"></path><g class="k"><path d="M490 254h1c1-1 2-1 2-3h0l1-1v3c1 0 1 1 2 1v1l1 1v4h1v1c1 0 2 1 3 1v2c-4-2-8-6-11-10zm10-31v-1l1-1c0 1 1 1 2 1l3 15-1 2h-1c-1-1-1-2 0-3 0-2-1-3-1-4l-1-1c0 1 0 1-1 2h-1l1-1c-1-1 0-1-1-2l2-1c0-2 0-3-1-4v-1l-1-1z"></path><path d="M488 214h1 1c0-2-1-4-1-6h-1l1-1h1v2h1v2c1 0 1 1 1 2l2 4v1 1c1 2 1 3 1 5l-1 1h1 2v-2h3c-1 2-2 3-4 4v2l1 1 1-1-1-1v-1l1-1c1 1 1 3 1 4l-1 1h-1c-1 1-1 2-1 3h-2l1 1h1v1l-2 2-1 1c1-1 0-1 1-1v-1l-1-1 1-1c-1-1-1-1-1-2v-2-1h-1v-1c-2 0-2 1-4 2v2h1v1l-1 1 1 1v1l1 1v1h-1v-1h-5c1-1 2-4 2-6 1-1 1-2 2-3v-1c-1 0-1 0-1 1l-2 1v-1l1-1c1 0 2-2 3-3l-1-1c1-1 1-1 1-2-1-1-1-1-1-2v-2l-1-1c0-1 0-1 1-2v-1z"></path></g><path d="M488 214h1 1c0-2-1-4-1-6h-1l1-1h1v2h1v2c1 0 1 1 1 2h-1v2c1 1 1 1 1 2s1 2 1 3 0 2-1 4c-2-1-2-1-2-3s-1-4-2-6v-1z" class="V"></path><defs><linearGradient id="AA" x1="516.065" y1="245.919" x2="496.783" y2="258.068" xlink:href="#B"><stop offset="0" stop-color="#352f2f"></stop><stop offset="1" stop-color="#706563"></stop></linearGradient></defs><path fill="url(#AA)" d="M511 236v20 12h-1l-5-2-4-2v-2c-1 0-2-1-3-1v-1h0c1-1 1-1 2-1 1 1 1 1 1 2l2-1c0-2 0-1-1-2h0 0v-1h1v-1c-1 0-1-1-2-2h1c-1-1-1-1-1-2h1c-1-1 0-1-1-2h1c-1-2 0-2-1-3v-1c0-1 0-1 1-2v-1h-1 0l1-1h0l1-1h0c1 2 0 5 1 8v-10h1l1-2s0-1 1-1h3 1z"></path><path d="M697 404l13-8c0 1-1 2-1 4-3 3-5 6-8 9-5 7-9 13-13 21-1 2-3 7-3 9h1c-1 1-2 3-2 3l-3 9v3c1 3 2 6 4 8 2 1 4 2 7 2 3 1 7 0 10 1h1l2-2h3 0v1h0 4l1 1c-1 1-2 1-2 3h-4c-1 1-2 1-3 1l-1 1h-6c-12 2-20 5-31 10h0c-4 2-8 5-12 8-3 2-6 5-9 7l-3 3-14 16-3 4h-1c-2 0-2 0-3-1l-6 10-2 4c0 1-2 3-2 4l-4 8c-4 6-7 14-10 21-1 2-2 6-4 7l11-25c0-2 2-4 3-6 0-1 0-3 1-4 0-1 0-1 1-2v-1-2h1v-3-2c-1 0 0-2 0-2 1-1 1-2 1-3 0 0 0-1 1-1 0-2 1-3 1-4 1-2 1-3 2-5v-1l1-1c0-2 1-3 1-5h-1 0c1-3 3-6 4-9l14-38c0-1 0-3 1-3l3-10 1 1h0c0 1 0 1 1 2 2-6 5-13 9-18h1l2-2h1c0 1-1 2-1 3-1 3-2 5-2 9l-3 5h2l13-20s2-3 2-4l3-2h2c1-1 2-1 4-2h0l-2 2v1l-2 2h0l-1 2h1l11-12 3-3 1 1h1c1-1 2-1 2-1l-1 2 2 3 9-9z" class="Y"></path><path d="M682 441c0-1 1-2 2-3 0 0 0-1 1-2v-2h1v-1c0-2 1-2 2-3-1 2-3 7-3 9h1c-1 1-2 3-2 3l-3 9c0-1-1-1-1-2 0 0 1-2 1-3 1-2 1-3 1-5z" class="N"></path><path d="M669 469c1-1 3-1 5-2l6-3h0c-3 3-5 3-7 6-1 1-1 2-2 2l-3 2-2-1h-1c1-2 2-3 4-4z" class="H"></path><path d="M677 458v-3c-1-2 0-3 1-5 2-2 1-3 2-6h0c1-1 1-2 1-3h1c0 2 0 3-1 5 0 1-1 3-1 3 0 1 1 1 1 2v3l-2 1v2c-1 0-1 0-1 1h-1z" class="D"></path><g class="C"><path d="M677 458h1c0 2 1 4 2 6l-6 3c-2 1-4 1-5 2l-2-1c2-1 5-2 7-4h0c2-2 2-3 2-5 0-1 1-1 1-1z"></path><path d="M679 457v-2l2-1c1 3 2 6 4 8l-2 2 1 1c-1 0-2 0-4-1h0c-1-2-2-4-2-6 0-1 0-1 1-1z"></path></g><path d="M678 458c0-1 0-1 1-1 1 3 1 4 4 6v1l1 1c-1 0-2 0-4-1h0c-1-2-2-4-2-6z" class="K"></path><path d="M665 473l-2 1h-1v-1-1l-2 1c-3 2-7 2-10 5h-1 0c1-2 4-4 7-5 1 0 2-1 4-2s4-2 7-3l2 1c-2 1-3 2-4 4z" class="G"></path><path d="M638 500l2-1c1 0 1 0 2-1l-14 16-3 4h-1c-2 0-2 0-3-1l2-2 1-1c2-3 4-6 7-7 1-1 2-1 2-1l2-3 1-1c1-1 1-2 2-2z" class="N"></path><defs><linearGradient id="AB" x1="674.851" y1="483.674" x2="671.527" y2="474.201" xlink:href="#B"><stop offset="0" stop-color="#e11e19"></stop><stop offset="1" stop-color="#f05855"></stop></linearGradient></defs><path fill="url(#AB)" d="M705 463h3 0v1h0 4l1 1c-1 1-2 1-2 3h-4c-1 1-2 1-3 1l-1 1h-6c-12 2-20 5-31 10h0c-4 2-8 5-12 8-3 2-6 5-9 7l-3 3c-1 1-1 1-2 1l-2 1c1-2 3-3 4-5 12-13 28-22 45-26 0-2-2-3-3-4l-1-1 2-2c2 1 4 2 7 2 3 1 7 0 10 1h1l2-2z"></path><path d="M705 463h3 0v1h0 4l-25 5c0-2-2-3-3-4l-1-1 2-2c2 1 4 2 7 2 3 1 7 0 10 1h1l2-2z" class="N"></path><path d="M680 411l3-3 1 1h1c1-1 2-1 2-1l-1 2 2 3c-4 5-6 12-8 18s-4 12-5 19c-2 6-3 10-8 14-3 3-10 6-14 7-1-1-1-2-2-3l1-2 3-12c1-1 2-2 2-3l1-1c0-1 1-2 1-3 1-3 2-6 2-9 1-2 3-4 4-5h0c-2-1-2-1-3 0l7-10 11-12z"></path><path d="M680 411l3-3 1 1h1c1-1 2-1 2-1l-1 2-4 4-2-3z" class="Z"></path><path d="M671 451c0 2-1 4-1 6v1h0l1-2c0-2 0-2 1-3l1-1c0-1 1-1 2-2-2 6-3 10-8 14v-1l4-12z" class="E"></path><defs><linearGradient id="AC" x1="665.637" y1="421.064" x2="680.315" y2="420.393" xlink:href="#B"><stop offset="0" stop-color="#de2724"></stop><stop offset="1" stop-color="#f9504a"></stop></linearGradient></defs><path fill="url(#AC)" d="M680 411l2 3-17 19h0c-2-1-2-1-3 0l7-10 11-12z"></path><path d="M669 440c1-2 2-4 4-6v1 1 4 1c-1 2-1 4-1 6-1 1-1 3-1 4l-4 12v1c-3 3-10 6-14 7-1-1-1-2-2-3l1-2v1h1c1-7 5-13 8-19v-1c1-2 4-4 7-5l1-2z" class="T"></path><path d="M669 440c1-2 2-4 4-6v1 1 4 1l-1 1-6 18c-1 1-1 3-2 5h-3c4-7 5-16 8-24v-1z" class="E"></path><path d="M669 440v1c-3 8-4 17-8 24h-1v-1l-1-1h-1l2-6c1-3 2-6 1-9v-1c1-2 4-4 7-5l1-2z" class="B"></path><path d="M661 448v-1c1-2 4-4 7-5-3 7-6 14-8 22l-1-1h-1l2-6c1-3 2-6 1-9z" class="h"></path><defs><linearGradient id="AD" x1="634.019" y1="453.273" x2="643.981" y2="456.227" xlink:href="#B"><stop offset="0" stop-color="#e02e2c"></stop><stop offset="1" stop-color="#fa4b44"></stop></linearGradient></defs><path fill="url(#AD)" d="M673 416h0l-2 2v1l-2 2h0l-1 2h1l-7 10c1-1 1-1 3 0h0c-1 1-3 3-4 5 0 3-1 6-2 9 0 1-1 2-1 3l-1 1c0 1-1 2-2 3l-3 12-1 2c1 1 1 2 2 3-2 0-2 0-3 2v1l-2 2c-5 6-10 13-16 18l-4 6c-1 3-2 5-3 8-1 2-1 4-2 7l-2 2-6 10-2 4c0 1-2 3-2 4l-4 8c-4 6-7 14-10 21-1 2-2 6-4 7l11-25c0-2 2-4 3-6 0-1 0-3 1-4 0-1 0-1 1-2v-1-2h1v-3-2c-1 0 0-2 0-2 1-1 1-2 1-3 0 0 0-1 1-1 0-2 1-3 1-4 1-2 1-3 2-5v-1l1-1c0-2 1-3 1-5h-1 0c1-3 3-6 4-9l14-38c0-1 0-3 1-3l3-10 1 1h0c0 1 0 1 1 2 2-6 5-13 9-18h1l2-2h1c0 1-1 2-1 3-1 3-2 5-2 9l-3 5h2l13-20s2-3 2-4l3-2h2c1-1 2-1 4-2z"></path><path d="M638 444l1 1h0c0 1 0 1 1 2l-4 10-1-1 1-1h-1l-1 2h0c0-1 0-3 1-3l3-10z" class="D"></path><path d="M649 444l-14 27c-1 4-2 9-4 13-2 2-3 6-5 8-2 1-4 6-5 9-1 1-1 3-2 5l-2 4c0 1-1 2-1 3l-1 2c-1 4-3 7-4 10 0 1 0 2-1 3v-2c-1 0 0-2 0-2 1-1 1-2 1-3 0 0 0-1 1-1 0-2 1-3 1-4 1-2 1-3 2-5v-1l1-1c0-2 1-3 1-5l1-1 6-15c1-2 2-3 3-4 6-9 11-20 16-30 1-3 2-7 4-10h2z" class="I"></path><path d="M631 485l2-4v1c0 2-1 3-1 5v1l2-2c0-1 1-2 2-3l-2 6c-1 1-2 3-2 5l-4 6-3 8c-1 2-1 4-2 7l-2 2-6 10-2 4c0 1-2 3-2 4l-4 8c-4 6-7 14-10 21-1 2-2 6-4 7l11-25c0-2 2-4 3-6 0-1 0-3 1-4 0-1 0-1 1-2v-1-2h1v-3c1-1 1-2 1-3 1-3 3-6 4-10l1-2c0-1 1-2 1-3l2-4c1-2 1-4 2-5 1-3 3-8 5-9 2-2 3-6 5-8v1z" class="T"></path><path d="M615 527l-1-1c1-3 3-5 4-8 0-1 0-3 2-4 0-1 1-1 1-2 1-3 4-5 5-8l2-4-3 8c-1 2-1 4-2 7l-2 2-6 10z" class="L"></path><path d="M631 484v1l-10 24-8 17c-2 4-3 10-6 14 0-1 0-3 1-4 0-1 0-1 1-2v-1-2h1v-3c1-1 1-2 1-3 1-3 3-6 4-10l1-2c0-1 1-2 1-3l2-4c1-2 1-4 2-5 1-3 3-8 5-9 2-2 3-6 5-8z" class="K"></path><defs><linearGradient id="AE" x1="645.651" y1="436.785" x2="660.147" y2="452.832" xlink:href="#B"><stop offset="0" stop-color="#b3070a"></stop><stop offset="1" stop-color="#f73631"></stop></linearGradient></defs><path fill="url(#AE)" d="M673 416h0l-2 2v1l-2 2h0l-1 2h1l-7 10c1-1 1-1 3 0h0c-1 1-3 3-4 5 0 3-1 6-2 9 0 1-1 2-1 3l-1 1c0 1-1 2-2 3l-3 12-1 2c1 1 1 2 2 3-2 0-2 0-3 2v1l-2 2c-5 6-10 13-16 18 0-2 1-4 2-5l2-6c-1 1-2 2-2 3l-2 2v-1c0-2 1-3 1-5v-1l-2 4v-1c2-4 3-9 4-13l14-27 13-20s2-3 2-4l3-2h2c1-1 2-1 4-2z"></path><path d="M658 443l-1 3c0 1 0 3 1 4l-1 1h-1c-1-1-2-1-3-1l5-7z" class="h"></path><path d="M658 443c0-2 2-3 3-5 0 3-1 6-2 9 0 1-1 2-1 3-1-1-1-3-1-4l1-3z" class="E"></path><path d="M653 450c1 0 2 0 3 1h1c0 1-1 2-2 3h-1c-3 4-3 6-7 8h0c0 2-1 2-2 3-2 2-3 5-4 8-2 3-4 6-5 10-1 1-2 2-2 3l-2 2v-1c0-2 1-3 1-5v-1c0-2 4-7 5-9 4-8 10-15 15-22z" class="a"></path><path d="M638 472l1 1c1-3 2-5 4-7v1 1c-1 0-1 0-1 1l-1 2v2c-2 3-4 6-5 10-1 1-2 2-2 3l-2 2v-1c0-2 1-3 1-5v-1c0-2 4-7 5-9z" class="B"></path><path d="M641 473c1-3 2-6 4-8 1-1 2-1 2-3h0c4-2 4-4 7-8h1l-3 12-1 2c1 1 1 2 2 3-2 0-2 0-3 2v1l-2 2c-5 6-10 13-16 18 0-2 1-4 2-5l2-6c1-4 3-7 5-10z" class="c"></path><path d="M648 476v-4c0-1 2-3 2-4h1c1 1 1 2 2 3-2 0-2 0-3 2v1l-2 2z" class="R"></path><path d="M639 376c2 4 2 7 2 11 0 2 0 6 2 8v1l2 1h3c0 2-2 5-4 7h2l1-1c0 14-7 27-9 41l-3 10c-1 0-1 2-1 3l-14 38c-1 3-3 6-4 9h0 1c0 2-1 3-1 5l-1 1v1c-1 2-1 3-2 5 0 1-1 2-1 4-1 0-1 1-1 1 0 1 0 2-1 3 0 0-1 2 0 2v2 3h-1v2 1c-1 1-1 1-1 2-1 1-1 3-1 4-1 2-3 4-3 6l-11 25-1 1c0 1 0 1-1 2 0 1 0 1-1 2h0v-1l-1-1-2 8-3 9c-1 3-2 6-3 8-1 1-2 2-2 4 1 1 0 3 1 4h1c0 1 1 2 0 3s-1 1-1 2v2c-1 2 0 4-1 6l-1 1s0 1-1 1v1 2h0c0 1 0 2-1 3-4 10-8 20-10 31l-4 10-1 6-1 2-4 13-1 2-1 4v2l-1 1v2l-1 2-1 3v1 1s0 1-1 2v3l-4 12-1 2c-1 5-3 10-5 14 0 1-1 5-1 5-2 2-1 2-2 4v1l-1 2h0c-1 2-1 3-1 4l-1 1v2 1h-1v-2l97-363c2 1 3 2 4 3l2-10 3-13z" class="N"></path><path d="M616 504h1c0 2-1 3-1 5l-1 1v1c-1 2-1 3-2 5 0 1-1 2-1 4-1 0-1 1-1 1 0 1 0 2-1 3 0 0-1 2 0 2v2 3h-1v2 1c-1 1-1 1-1 2-1 1-1 3-1 4-1 2-3 4-3 6l-11 25-1 1c0 1 0 1-1 2 0 1 0 1-1 2h0v-1l-1-1c0-1 1-2 1-3l1-5 1-1c0-2 0-2 1-3l1-4c2-3 3-7 3-10l1-4v-2l1-2c0-5 2-11 3-16v3 2l-1 1c0 1 0 2 1 4 0-1 0-2 1-3l2-7h0l1-1v1h0 1v1l3-4 6-17z" class="C"></path><path d="M606 524h0 1v1l3-4-12 32c-2 3-3 6-5 9l1-4c2-3 3-7 3-10l1-4v-2l1-2c0-5 2-11 3-16v3 2l-1 1c0 1 0 2 1 4 0-1 0-2 1-3l2-7h0l1-1v1z" class="a"></path><path d="M602 524v3 2l-1 1c0 1 0 2 1 4 0-1 0-2 1-3l2-7h0l1-1v1c-1 2-2 5-3 8s-2 5-2 8c0 1-1 2-1 4-1 2-1 3-3 4l1-4v-2l1-2c0-5 2-11 3-16z" class="X"></path><path d="M639 376c2 4 2 7 2 11 0 2 0 6 2 8v1l2 1h3c0 2-2 5-4 7h2l1-1c0 14-7 27-9 41l-3 10c-1 0-1 2-1 3l-14 38c-1 3-3 6-4 9h0l-6 17-3 4v-1h-1 0v-1l-1 1h0l-2 7c-1 1-1 2-1 3-1-2-1-3-1-4l1-1v-2-3l5-20 27-104v-1l2-10 3-13z" class="H"></path><path d="M635 410c1-1 2-2 2-3l1 2c1 0 2-1 2-1-1 1-4 5-6 5 0-1 1-2 1-3z" class="F"></path><path d="M639 376c2 4 2 7 2 11 0 2 0 6 2 8v1l2 1h3c0 2-2 5-4 7-1 1-2 3-4 4 0 0-1 1-2 1l-1-2c0 1-1 2-2 3v-2l-1 1c0-3 1-7 0-9v-1l2-10 3-13z" class="G"></path><path d="M634 399l2-10 1 4 1 1c0 3-1 6-1 9l-2 5-1 1c0-3 1-7 0-9v-1z" class="I"></path><path d="M637 403c1-1 1-3 2-4s1-2 1-2v-1h1 2l2 1c-1 1-1 2-2 4l-3 3-3 3c0 1-1 2-2 3v-2l2-5z" class="c"></path><path d="M645 397h3c0 2-2 5-4 7-1 1-2 3-4 4 0 0-1 1-2 1l-1-2 3-3 3-3c1-2 1-3 2-4z" class="T"></path><path d="M646 404l1-1c0 14-7 27-9 41l-3 10c-1 0-1 2-1 3l-14 38c-1 3-3 6-4 9h0l-6 17-3 4v-1h-1 0v-1c1-2 2-5 2-7l4-10 7-24 9-34c2-10 3-21 7-30 3-5 7-9 11-14z" class="U"></path><path d="M612 506c0 4-2 10-4 14l1-1v-1c1-1 1-2 1-3l1-1h0v-2l1-1c0-1 1-2 1-3 1-1 1-2 2-4h1 0l-6 17-3 4v-1h-1 0v-1c1-2 2-5 2-7l4-10z" class="S"></path><path d="M698 284l2-3c2 0 2 1 3 2v1 2c-1 1-1 1-1 2v1h0l-1 1c0 2 0 2-1 3s-1 2-1 3v3c0 1-1 2-1 3s-1 2 0 3c-1 1-1 2-2 3l-7 22c-2 3-4 7-5 10l-4 7-2 3-1-2c-2 4-5 8-7 13-2 4-3 9-5 13h-1-1l-1 3c-1 5-4 9-6 14l-2 4c-1 1-2 0-3 0h-8c-2-2-2-6-2-8 0-4 0-7-2-11 1-12 0-24-1-36l-2-5-1-6-2-6c-1-2-2-3-2-5-3-2-3-3-5-5 0-1-1-2-1-3-1-2-2-3-2-4v-1c0-2-2-4-4-6l1-1-2-2-6-6c1 0 2 0 3 1l1-1v-1-1l-1-1v-1c10 6 22 12 34 13 9 2 20-1 28-4l7-3 2-2c3-2 5-4 7-6l2-3 3 3z" class="Y"></path><path d="M695 281l3 3-3 3-2-3 2-3z" class="J"></path><path d="M684 292c1 0 3 0 4-1l1 1c-2 1-4 3-6 4l-1-1c-2 1-3 0-5 0l7-3z" class="K"></path><path d="M686 290c3-2 5-4 7-6l2 3c-2 1-4 3-6 5l-1-1c-1 1-3 1-4 1l2-2z" class="R"></path><path d="M677 348c5-10 10-19 14-29 1-3 3-9 5-11l-7 22c-2 3-4 7-5 10l-4 7-2 3-1-2z" class="O"></path><defs><linearGradient id="AF" x1="650.504" y1="289.121" x2="652.275" y2="302.487" xlink:href="#B"><stop offset="0" stop-color="#db3733"></stop><stop offset="1" stop-color="#f36565"></stop></linearGradient></defs><path fill="url(#AF)" d="M615 286c10 6 22 12 34 13 9 2 20-1 28-4 2 0 3 1 5 0l1 1c-11 5-21 7-33 6 0 2 0 2-1 2-2 1-5 1-7 1l-7-3-2-1c-3-1-6-3-8-4-1-1-3-2-4-2l-3 1-6-6c1 0 2 0 3 1l1-1v-1-1l-1-1v-1z"></path><path d="M631 297l5 2c-1 1-1 1-1 3l-2-1-2-3v-1z" class="B"></path><path d="M636 299c5 2 9 3 14 3 0 2 0 2-1 2-2 1-5 1-7 1l-7-3c0-2 0-2 1-3z" class="S"></path><path d="M616 288c5 3 10 7 15 9v1l2 3c-3-1-6-3-8-4-1-1-3-2-4-2l-3 1-6-6c1 0 2 0 3 1l1-1v-1-1z" class="T"></path><defs><linearGradient id="AG" x1="635.396" y1="300.88" x2="626.378" y2="309.86" xlink:href="#B"><stop offset="0" stop-color="#790000"></stop><stop offset="1" stop-color="#a21010"></stop></linearGradient></defs><path fill="url(#AG)" d="M618 296l3-1c1 0 3 1 4 2 2 1 5 3 8 4l2 1 7 3h0 4c-2 1-5 3-6 5s1 7 2 9h-1-2c1 2 1 4 1 6v1h-1v1l-1-1c-2-1-3-8-5-10l-1 1c1 2 1 3 1 6h0c-1-2-2-3-2-5-3-2-3-3-5-5 0-1-1-2-1-3-1-2-2-3-2-4v-1c0-2-2-4-4-6l1-1-2-2z"></path><path d="M638 326c1-2 0-7-2-9-1-2-2-3-2-5l1 2c1 1 2 2 3 4h0v-1c-1-1 0-2 0-3 0 2 1 3 1 5 1 2 1 4 1 6v1h-1v1l-1-1z" class="L"></path><path d="M641 319c-1-3-3-7-4-10 0-1 0-1 1-2s3-2 4-2h4c-2 1-5 3-6 5s1 7 2 9h-1z" class="K"></path><path d="M623 306v-1c0-2-2-4-4-6l1-1c4 6 9 12 11 20-3-2-3-3-5-5 0-1-1-2-1-3-1-2-2-3-2-4z" class="M"></path><defs><linearGradient id="AH" x1="667.352" y1="320.793" x2="643.34" y2="346.959" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2f0505"></stop></linearGradient></defs><path fill="url(#AH)" d="M646 305c2 0 4 0 6 1v1c6 3 11 9 13 15 1 4 1 8 2 11 1 8 0 18-1 26l-3 15-1 3-3 1h-1l1-6v-1c1-1 2-3 2-4l1-8 1-4c1-7 1-15-2-23-2-5-4-10-8-14-1-2-2-3-3-4-1 0-1 1-2 1h-2l-1 1c0 2 0 3-1 4h-1l-1-1c-1-2-3-7-2-9s4-4 6-5z"></path><path d="M663 355c0 2 0 4 1 5 0 2 0 3-1 5h0c1-1 1 0 1-1s0-2 1-2l-1-2 2-1-3 15-1 3-3 1h-1l1-6v-1c1-1 2-3 2-4l1-8 1-4z" class="F"></path><defs><linearGradient id="AI" x1="663.054" y1="332.405" x2="641.475" y2="348.154" xlink:href="#B"><stop offset="0" stop-color="#600100"></stop><stop offset="1" stop-color="#a1110f"></stop></linearGradient></defs><path fill="url(#AI)" d="M645 316l1-1h2c1 0 1-1 2-1 1 1 2 2 3 4 4 4 6 9 8 14 3 8 3 16 2 23l-1 4-1 8c0 1-1 3-2 4v1l-1 6h1l3-1c-1 5-4 9-6 14l-2 4c-1 1-2 0-3 0h-8c-2-2-2-6-2-8 0-4 0-7-2-11 1-12 0-24-1-36l-2-5-1-6-2-6h0c0-3 0-4-1-6l1-1c2 2 3 9 5 10l1 1v-1h1v-1c0-2 0-4-1-6h2 1l1 1h1c1-1 1-2 1-4z"></path><path d="M659 366v-1c0-3 0-6 1-8v1c0-1 0 0 1-1l1 2-1 8c0 1-1 3-2 4v-5z" class="L"></path><path d="M645 316c1 4 5 16 3 18l-5-14h1c1-1 1-2 1-4z" class="h"></path><path d="M654 367v-2c0 1 0 1 1 1v-4l1-1v-3-3h1l1 1c1 3 0 6 0 9l1 1v5 1l-2 3c-1 2-2 4-4 5h-4c-1-1-1-2-2-3v-1c0-1 1-1 2-2h3c0-1 1-2 2-3v-4z" class="G"></path><path d="M655 373l1-6c1 0 1 0 2 1 0 2-1 4-1 7-1 2-2 4-4 5h-4c-1-1-1-2-2-3 1 1 3 1 4 2 2-1 2-2 3-3l1-3z" class="I"></path><path d="M654 367h1v6l-1 3c-1 1-1 2-3 3-1-1-3-1-4-2v-1c0-1 1-1 2-2h3c0-1 1-2 2-3v-4z" class="H"></path><path d="M639 319h2 1l1 1 5 14c3 10 6 19 6 30h-1v-1c1-13-8-26-13-37v-1c0-2 0-4-1-6z" class="I"></path><path d="M639 319h2c0 1 1 2 1 4h-1l-1 2c0-2 0-4-1-6z" class="H"></path><path d="M659 372l-1 6h1l3-1c-1 5-4 9-6 14l-2 4c-1 1-2 0-3 0h-8c-2-2-2-6-2-8 1-3 1-7 2-9l1-1c1-1 1-2 2-3l1 2v1c1 1 1 2 2 3h4c2-1 3-3 4-5l2-3z" class="P"></path><path d="M633 323h0c0-3 0-4-1-6l1-1c2 2 3 9 5 10l1 1v-1h1c5 11 14 24 13 37v1 7h1c-1 1-2 2-2 3h-3c-1 1-2 1-2 2l-1-2c-1 1-1 2-2 3l-1 1c-1 2-1 6-2 9 0-4 0-7-2-11 1-12 0-24-1-36l-2-5-1-6-2-6z" class="G"></path><path d="M635 329s1 1 2 1c0 1 1 2 0 3v1l-1 1-1-6z" class="H"></path><path d="M639 327v-1h1c5 11 14 24 13 37-1 2 0 4-1 6v-3c-1-1 0-2-1-3 0-1-1-1-1-2v-3c-1-4-2-7-3-11-3-7-7-14-8-20z" class="F"></path><path d="M638 340v-5-3h0c5 9 8 20 8 31 0 3-1 7 0 11-1 1-1 2-2 3l-1 1c-1 2-1 6-2 9 0-4 0-7-2-11 1-12 0-24-1-36z" class="L"></path><defs><linearGradient id="AJ" x1="515.835" y1="171.41" x2="547.884" y2="286.178" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2423"></stop></linearGradient></defs><path fill="url(#AJ)" d="M511 151c1-5 0-11 0-15-1-2 0-4 0-5v-1c1-2 0-6 0-7l2-1h0 1v5 2 14 5l3 1h0-1l-1 1 1 1h1l1-1c1 0 2 0 3-1 3 1 4 2 6 3v1h2l1 1h2v-1h2c2 0 4-1 6 0 1 1 1 1 2 1h1l2 1 1 1v1c-1-1-2-1-3-1 0 2 0 2 1 3v1c2 2 4 2 4 5v1c6 5 9 15 9 23 0 6-1 12-2 19-2 8-4 16-7 23l-2 2-5-2c0 1 1 1 1 2 2 2 2 4 1 6-2 12-11 22-21 28h0c-1 0-1 0-2 1-1 0-2 0-3 2 0 1 0 2 1 3 1 0 3 1 3 1 1 0 1 1 2 2 0 0 0-1 1 0l1 1c0 1-1 1-1 2-1 1-3 2-3 4-6 9-6 18-7 29-1-5-1-11-1-17 1-2 0-5 0-8v-20 4c0 2-1 3-1 5l-1-19v-1-20c-1-21 0-46-12-63-3-4-8-8-13-8-4-1-7 0-10 2 0-1 0-2 1-4s4-4 7-4c5-2 11 1 16 4h5 2l1 1 1-1h2v-12z"></path><path d="M534 203v1l-2 5c-1 3-1 4-1 7l1-1 1-1c0-2 1-3 2-5h1v2c-1 1-1 4-1 6 1 0 1 1 1 2h-2c-1 1-2 1-4 1v-2h-1c-1-1-2-1-2-2h0l-1 2-1 1c1-2 1-3 1-5l1 1h1l1-3 5-9z" class="E"></path><path fill="#f0ece9" d="M511 151c1-5 0-11 0-15-1-2 0-4 0-5v-1c1-2 0-6 0-7l2-1h0 1v5 2 14 5l3 1h0-1l-1 1 1 1h1l1-1c1 0 2 0 3-1 3 1 4 2 6 3v1h2l1 1h2v-1h2c2 0 4-1 6 0 1 1 1 1 2 1h1l2 1 1 1v1c-1-1-2-1-3-1 0 2 0 2 1 3v1c2 2 4 2 4 5v1c-5-2-10-2-14 0-11 5-15 20-18 30s-2 23-2 33l-1 38v4c0 2-1 3-1 5l-1-19v-1-20c-1-21 0-46-12-63-3-4-8-8-13-8-4-1-7 0-10 2 0-1 0-2 1-4s4-4 7-4c5-2 11 1 16 4h5 2l1 1 1-1h2v-12z"></path><path d="M500 163h5 2l1 1 1-1h2v12 7c-3-7-6-14-11-19z" class="u"></path><defs><linearGradient id="AK" x1="524.768" y1="174.906" x2="529.33" y2="151.528" xlink:href="#B"><stop offset="0" stop-color="#938886"></stop><stop offset="1" stop-color="#c2b8b3"></stop></linearGradient></defs><path fill="url(#AK)" d="M514 148l3 1h0-1l-1 1 1 1h1l1-1c1 0 2 0 3-1 3 1 4 2 6 3v1h2l1 1h2v-1h2c2 0 4-1 6 0 1 1 1 1 2 1h1l2 1 1 1v1c-1-1-2-1-3-1 0 2 0 2 1 3v1c-5-2-10-2-15 0-10 4-12 15-15 24v-36z"></path><path d="M309 246h1v2l1-1v5l1-2h1v-3h1v6l1 2h1l1-2c1-1 2-3 3-4l1-1-2 6c-1 3 0 6 0 8l1 1 1-1c0-2 1-5 2-6l1 2 1-1v4c2-4 1-8 2-12 1 0 2 1 3 2 1 0 2 0 3 2s0 6 0 8h1 0c0-2 1-2 2-3v-1h2 3l4 5c4 5 10 10 16 13h0c2 2 6 3 8 4 4-1 7 1 10 1 1-1 4-1 5-1l1-1h0v-2h1c1 0 2-1 3-2h0 1c2 2 6 3 8 4v2c1 1 3 1 5 1l3 1c3 0 6-1 9 0l-1 1h1c2 0 5-1 7 0h-1v1c-2 1-4 2-5 3h-1c-1 1-3 2-4 2 2 2 3 3 6 3l-3 1c-1-1-1-1-2-3-3 1-7 4-9 6-1 0-1 0-2 1l-2-1-9 3c-3 1-6 2-8 2-2 2-3 3-5 4l-3 1c-9 6-14 13-16 24l-1 6v3c-1 1-1 2-2 2v5c0 4 0 9 1 13 1 6 3 12 6 17-2-1-3-4-4-6l-8-15c-1 0-2-1-3-2h-1v1l-3-3c0 1 0 2-1 3l1 1-2 1h0c-4-6-5-14-9-19l-4-7c-1-2-3-4-5-6l-1-2c0-1 0-3-1-5v-1l-3-3h0c-6-8-15-14-21-21l-9-6v-1l2 1v-1-3l2 1h7c0 1 1 0 1 0v-1h-1v-1l1-3h1l-1-2h0l1-1 1-2-1-4c1 1 2 1 3 2 1-2 3-3 3-6-1-1-1-1-1-2l1-2-1-1 1-1v-1c0 1 1 1 2 1l2 6c0-2-1-4-1-7v-2c-1-3-1-7-1-10z" class="Y"></path><path d="M325 283c1 0 1 1 3 2h2l1 1 2 2h-2c-1 0-1 0-2-1h-1c-1-1-2-2-3-4z" class="R"></path><path d="M322 280c2 0 2 0 3 1 1 0 2 1 3 1l2 3h-2c-2-1-2-2-3-2l-3-3z" class="K"></path><path d="M313 247h1v6c0 2-1 3-1 3h-1l-1-4 1-2h1v-3z" class="H"></path><path d="M322 280c-1-2-2-5-3-7l9 9c-1 0-2-1-3-1-1-1-1-1-3-1z" class="N"></path><path d="M321 262c0-2 1-5 2-6l1 2c0 2 0 4 1 6v4l1 2-2-2v-2-3h-2v-1-1h-1v1z" class="I"></path><path d="M338 337h1 1 1c3 5 7 12 9 18-1 0-2-1-3-2h-1c-3-5-6-11-8-16z" class="b"></path><path d="M325 302h1c1 1 3 7 3 8l12 27h-1-1-1l-13-35z" class="O"></path><defs><linearGradient id="AL" x1="353.522" y1="288.157" x2="354.043" y2="302.079" xlink:href="#B"><stop offset="0" stop-color="#e43636"></stop><stop offset="1" stop-color="#f16968"></stop></linearGradient></defs><path fill="url(#AL)" d="M328 287h1c1 1 1 1 2 1h2c12 9 25 12 40 11 6-1 12-2 18-4 1 0 4-1 5-1v1c-2 1-4 2-6 4-3 1-6 2-8 2-2 2-3 3-5 4l-3 1-2-2 2-2-9 1c-14-1-27-6-37-16z"></path><path d="M391 295c1 0 4-1 5-1v1c-2 1-4 2-6 4-3 1-6 2-8 2-2 2-3 3-5 4l-3 1-2-2 2-2h2c4-4 11-3 15-6v-1z" class="M"></path><path d="M376 302l6-1c-2 2-3 3-5 4l-3 1-2-2 2-2h2z" class="G"></path><path d="M325 257v4c2-4 1-8 2-12 1 0 2 1 3 2 1 0 2 0 3 2s0 6 0 8h1 0c0-2 1-2 2-3v-1h2l1 1v1 3c-1 2 0 4-1 6l1 2h0c0 5 8 14 12 17s14 3 17 8v1c1 1 1 0 2 0 0 1 2 2 3 3-15 1-28-2-40-11l-2-2v-1l-3-5h0v-1c-1-1-1-3-1-5l-1-4-1-2v-4c-1-2-1-4-1-6l1-1z" class="I"></path><path d="M325 257v4c2-4 1-8 2-12 1 0 2 1 3 2 1 0 2 0 3 2s0 6 0 8h1 0c0-2 1-2 2-3v-1h2l1 1v1 3c-1 2 0 4-1 6-1-1-1-2-3-2-1 2-1 5-1 6l1 2v5h0c-1-2-2-4-2-7h-2v-4c-1-1-2-1-4-1h-1c1 2 2 5 1 7l-1-4-1-2v-4c-1-2-1-4-1-6l1-1z" class="B"></path><path d="M325 257v4c2-4 1-8 2-12 1 0 2 1 3 2h0c0 1 0 2-1 3h0l1 3v1c0 2 1 4 2 5 0 3 0 6 1 9h-2v-4c-1-1-2-1-4-1h-1c1 2 2 5 1 7l-1-4-1-2v-4c-1-2-1-4-1-6l1-1z" class="C"></path><path d="M307 258c0 1 1 1 2 1l2 6c0 4 3 8 5 11l6 18c1 3 1 5 3 8h0l13 35c2 5 5 11 8 16v1l-3-3c0 1 0 2-1 3l1 1-2 1h0c-4-6-5-14-9-19l-4-7c-1-2-3-4-5-6l-1-2c0-1 0-3-1-5v-1l-3-3h0c-6-8-15-14-21-21l-9-6v-1l2 1v-1-3l2 1h7c0 1 1 0 1 0v-1h-1v-1l1-3h1l-1-2h0l1-1 1-2-1-4c1 1 2 1 3 2 1-2 3-3 3-6-1-1-1-1-1-2l1-2-1-1 1-1v-1z" class="c"></path><path d="M338 346h1l1-1 3 6c0 1 0 2-1 3l-4-8z" class="B"></path><path d="M315 298v-4l1-1 1 1v1c1 1 1 1 1 2v1c1 1 1 0 1 1 0 2 2 3 2 6l3 6 1 2h-1c-1-1-1-3-2-4-1 1-2 1-2 3l-1-1c1-2-1-4-2-6 0-3-2-5-2-7z" class="L"></path><path d="M321 316h1c3 4 6 9 8 14 0 1 1 2 2 3 3 4 4 9 6 13l4 8 1 1-2 1h0c-4-6-5-14-9-19l-4-7c-1-2-3-4-5-6l-1-2c0-1 0-3-1-5v-1z" class="H"></path><path d="M321 317c3 3 7 9 7 13-1-2-3-4-5-6l-1-2c0-1 0-3-1-5z" class="b"></path><path d="M307 265l1 4v1c2 3 2 5 3 8v2c0 1 1 3 1 4v4c1 2 1 5 2 6s1 3 1 4c0 2 2 4 2 7 1 2 3 4 2 6l1 1 2 3v1h-1l-3-3h0c-6-8-15-14-21-21l-9-6v-1l2 1v-1-3l2 1h7c0 1 1 0 1 0v-1h-1v-1l1-3h1l-1-2h0l1-1 1-2-1-4c1 1 2 1 3 2 1-2 3-3 3-6z" class="X"></path><path d="M292 284c3 1 5 1 8 1v1c0 1-1 2-2 3-1-1-2-2-4-2-1 0-1 0-2-1s-1 0-1-1l1-1z" class="H"></path><path d="M312 288c1 2 1 5 2 6s1 3 1 4c0 2 2 4 2 7l-6-9 1-1c1-2 0-5 0-7z" class="B"></path><path d="M300 282c2-1 2-1 3-1 1 2 2 3 3 4v1c-1 1-1 2-2 2-2 0-2-1-3-2v-1h-1c-3 0-5 0-8-1v-1h7c0 1 1 0 1 0v-1z" class="R"></path><path d="M297 292c2 1 6 3 8 6h0c1 3 7 7 9 9 0-1 0-2-1-2l-1-1c-1-1-2-2-2-3-2-2-2-3-4-4h2c2 2 3 4 4 6 1 1 3 2 4 4 1 1 2 3 3 4l1 1 2 3v1h-1l-3-3h0c-6-8-15-14-21-21z" class="D"></path><path d="M307 265l1 4v1c2 3 2 5 3 8v2c0 1 1 3 1 4v4c0 2 1 5 0 7l-1 1-2-4-3-6v-1c-1-1-2-2-3-4-1 0-1 0-3 1h-1v-1l1-3h1l-1-2h0l1-1 1-2-1-4c1 1 2 1 3 2 1-2 3-3 3-6z" class="T"></path><path d="M309 275v3c1 3 1 6 2 9v3h0c-1-1-2-3-3-5h0 1v-2c0-3-1-6 0-8z" class="F"></path><path d="M299 282v-1l1-3h1l-1-2h0l1-1 1 2h0l2-1c1 1 0 0 1 2l1 7c-1-1-2-2-3-4-1 0-1 0-3 1h-1z" class="L"></path><path d="M307 265l1 4v1l1 5c-1 2 0 5 0 8v2h-1c-1-2-1-4-1-5-1-2-2-2-2-4 0-1 0-1-1-2h-1l-1-1-1-4c1 1 2 1 3 2 1-2 3-3 3-6z" class="B"></path><path d="M307 265l1 4c0 1 0 2-1 4-1 0-2-1-3-1v-1c1-2 3-3 3-6z" class="S"></path><defs><linearGradient id="AM" x1="370.387" y1="275.427" x2="362.76" y2="293.259" xlink:href="#B"><stop offset="0" stop-color="#5c0201"></stop><stop offset="1" stop-color="#860807"></stop></linearGradient></defs><path fill="url(#AM)" d="M338 257h3l4 5c4 5 10 10 16 13h0c2 2 6 3 8 4 4-1 7 1 10 1 1-1 4-1 5-1l1-1h0v-2h1c1 0 2-1 3-2h0 1c2 2 6 3 8 4v2c1 1 3 1 5 1l3 1c3 0 6-1 9 0l-1 1h1c2 0 5-1 7 0h-1v1c-2 1-4 2-5 3h-1c-1 1-3 2-4 2 2 2 3 3 6 3l-3 1c-1-1-1-1-2-3-3 1-7 4-9 6-1 0-1 0-2 1l-2-1-9 3c2-2 4-3 6-4v-1c-1 0-4 1-5 1-6 2-12 3-18 4-1-1-3-2-3-3-1 0-1 1-2 0v-1c-3-5-13-5-17-8s-12-12-12-17h0l-1-2c1-2 0-4 1-6v-3-1l-1-1z"></path><path d="M338 257h3l4 5-1 1-1 1v-1c-2 2-3 3-3 6v1l-1 1v-1h0l-1-2c1-2 0-4 1-6v-3-1l-1-1z" class="F"></path><path d="M339 270c1-2 1-7 2-9 1 0 2 1 3 2l-1 1v-1c-2 2-3 3-3 6v1l-1 1v-1h0z" class="T"></path><path d="M385 278c2 0 3 0 5 2h1v2 1c-8 0-15-1-22-4 4-1 7 1 10 1 1-1 4-1 5-1l1-1h0z" class="O"></path><path d="M385 278c2 0 3 0 5 2h0l-1 1c-1 0-1 0-2-1h-5-3c1-1 4-1 5-1l1-1h0z" class="r"></path><path d="M390 274c2 2 6 3 8 4v2c1 1 3 1 5 1l3 1c3 0 6-1 9 0l-1 1h1c2 0 5-1 7 0h-1v1c-2 1-4 2-5 3h-1c-1 1-3 2-4 2 2 2 3 3 6 3l-3 1c-1-1-1-1-2-3-3 1-7 4-9 6-1 0-1 0-2 1l-2-1-9 3c2-2 4-3 6-4v-1l-1-1h0c1-2 5-2 6-3v-1c-3-2-7-1-9-1-2-1-2-2-4-2-2-1-4-1-6-2h2 1c2 0 4 0 6-1v-1-2h-1c-2-2-3-2-5-2v-2h1c1 0 2-1 3-2h0 1z" class="a"></path><path d="M401 290v-1c-3-2-7-1-9-1-2-1-2-2-4-2-2-1-4-1-6-2h2 1c2 1 5 1 7 3 1 0 6 1 7 0 1 0 1 0 2-1l3 1c0 1-1 2-2 3h-1z" class="B"></path><path d="M406 290v1l-7 5h0l-9 3c2-2 4-3 6-4v-1l-1-1 11-3z" class="J"></path><path d="M406 290c0-1 2-1 2-2 4-2 9-3 13-5v1c-2 1-4 2-5 3h-1c-1 1-3 2-4 2 2 2 3 3 6 3l-3 1c-1-1-1-1-2-3-3 1-7 4-9 6-1 0-1 0-2 1l-2-1h0l7-5v-1z" class="c"></path><path d="M390 274c2 2 6 3 8 4v2c1 1 3 1 5 1l3 1c3 0 6-1 9 0l-1 1c-5-1-9 0-15-1-3 0-5-1-8-2h-1c-2-2-3-2-5-2v-2h1c1 0 2-1 3-2h0 1z" class="q"></path><path d="M218 288h4 2c2 1 4 1 6 1 2 1 3 1 5 1l8 3 7 5h1l8 5c1 0 2 1 3 2 2 0 3 1 5 1 1 0 3 1 4 2 1 0 3 1 4 1l-2 2 6 3c1 2 2 3 4 4l-1 2c5 3 9 8 14 11 8 8 16 17 21 27 3 6 5 12 8 17 5 7 11 13 15 21l-5-4h0c-1 1-2 1-3 1 1 2 3 4 4 5-2-1-4-2-6-4l-1 1-2-2c-2-1-3-1-4-1s-2-1-3-2l-7-4c-4-1-8-3-12-4 1 0 2 0 4-1v-1h-2c-5-2-10-3-15-4h-8-11c-2 0-3 1-5 1-4 1-10 3-14 3h-3c-2 0-3-1-4-2l1-1-2-1h-4-1 1c-1-1-9 0-11 0l14-3c-3-1-6 0-9 1-4 0-9 0-13 1h-2v-1h-4l2-2h-2l-1-1 1-3c5-3 9-8 14-13 9-11 13-24 15-38-2-2-1-4-1-6-1-4-3-8-6-11-2-2-4-4-7-4h0c-2-1-2-1-3-3h-1c0-1-1-2-2-3v-1l-4-1z"></path><path d="M248 332l1-2v1c0 1-1 6 0 7l5-10c0 2-1 6 0 8v1h-1c-2 0-4 1-5 3l-1 1h-1c-1 1 0 2-2 3h-1v1 1h0c-2 1-3 2-3 3-1-1-1-2-3-2l7-12v-1c1-1 3-2 4-2z" class="F"></path><path d="M244 335v-1c1-1 3-2 4-2-1 2-2 5-3 8h0c-1-2-1-3-1-5z" class="E"></path><path d="M268 337c3-2 5-2 9-2h1c9-1 16 6 22 12 3 3 6 7 8 10l-1 2c-2-2-4-5-5-7a57.31 57.31 0 0 0-11-11c-3-1-3-2-5-2l-2-1c-4-1-7-1-12-1v1c2 0 3 0 4 1l-1 2 1 1 1 1h0c-1 0-1 0-3-1l-2 1h-3l-1 2h-1v-2l-1-1-1-1c-1 1-2 1-3 1 2-2 4-3 6-5z" class="a"></path><path d="M268 337v1c1 1 0 2 0 3v1c1-1 2-2 2-3h1 0c1 1 1 1 2 1v-1h1l1 2 1 1 1 1h0c-1 0-1 0-3-1l-2 1h-3l-1 2h-1v-2l-1-1-1-1c-1 1-2 1-3 1 2-2 4-3 6-5z" class="P"></path><path d="M254 337l3-10c1 3 0 7 0 10 1-3 3-7 5-10l1 1c0 1 0 2-1 3v3c1-1 2-2 2-3h1c1 0 6-2 8-2 2-1 5-1 8-1-4 1-7 2-10 4-3 1-5 3-7 5-1 2-2 3-3 6h1c0 1-1 2-1 3h-1c-1-1-1-1-1-2h-1v3c-1-1-2-1-3-1l-1-1v-2l-1-1v-1-4h1z" class="B"></path><path d="M253 341h0 2c1 0 2 0 3-1 0-1 3-2 3-3 1 0 2-2 2-3 2-1 5-3 7-3h0c-2 1-3 2-4 2-1 1-1 2-2 3v1c-1 2-2 3-3 6h1c0 1-1 2-1 3h-1c-1-1-1-1-1-2h-1v3c-1-1-2-1-3-1l-1-1v-2l-1-1v-1z" class="P"></path><path d="M276 339c-1-1-2-1-4-1v-1c5 0 8 0 12 1l2 1c2 0 2 1 5 2a57.31 57.31 0 0 1 11 11c1 2 3 5 5 7l3 3c1 1 1 1 1 2 1 0 2 1 3 2h0 0c0-1 0-2-1-2 0-1 0-2-1-2l-1-2c0-1-2-3-2-4v-1c4 5 7 10 8 16 0 1 0 1-1 1-2 0-3-1-4-2-4-6-8-12-13-17l-6-6-9-5-2-2v-1h0l-2 1c-1-1-2-1-4-1z" class="E"></path><path d="M284 342c2 0 3-1 5 0l2 2c1 0 1 3 2 3l-9-5z" class="h"></path><path d="M276 339c2 0 3 0 4 1l2-1h0v1l2 2 9 5 6 6c5 5 9 11 13 17-3 0-5 0-8-2-3-1-5-1-7-4-1-1-1-2-2-3 0-2-4-6-5-7-2-3-4-4-7-5v-3l-2-1c-1-1-2-2-4-2h0l-1-1-1-1 1-2z" class="B"></path><path d="M276 339c2 0 3 0 4 1l2-1h0v1l-1 2c1 1 2 1 3 2l1 1-2 1-2-1c-1-1-2-2-4-2h0l-1-1-1-1 1-2z" class="T"></path><defs><linearGradient id="AN" x1="231.959" y1="352.969" x2="240.66" y2="363.758" xlink:href="#B"><stop offset="0" stop-color="#5d0503"></stop><stop offset="1" stop-color="#920a08"></stop></linearGradient></defs><path fill="url(#AN)" d="M237 347c2 0 2 1 3 2 0-1 1-2 3-3h0v-1-1h1c2-1 1-2 2-3h1l1-1c1-2 3-3 5-3v4 1l1 1v2l1 1c1 0 2 0 3 1h-1l-2-1c-1 0-2 0-2 1l-1 3h-1v-2l-1-1c-1 1 0 1 0 2s-2 2-2 3c-1 2-1 3-2 4 0 1 0 1 1 1l-5 6v1c-1 1-3 3-4 3-2-1-2 0-4 1h-1c-1 1-1 2-2 3h0c-2 0-3 0-4 1h-2-1-1l-1 2h-5-4l2-2h1c3-1 5-3 7-5 6-6 10-13 14-20z"></path><path d="M262 342c1 0 2 0 3-1l1 1 1 1v2h1l1-2h3l2-1c2 1 2 1 3 1 2 0 3 1 4 2l2 1v3c3 1 5 2 7 5 1 1 5 5 5 7 1 1 1 2 2 3 2 3 4 3 7 4 3 2 5 2 8 2 1 1 2 2 4 2-3 1-7 0-10 0l-13-2-17-1c-10 0-20 0-29 1l-25 4 1-2h1 1 2c1-1 2-1 4-1h0c1-1 1-2 2-3h1c2-1 2-2 4-1 1 0 3-2 4-3v-1l5-6c-1 0-1 0-1-1 1-1 1-2 2-4 0-1 2-2 2-3s-1-1 0-2l1 1v2h1l1-3c0-1 1-1 2-1l2 1h1v-3h1c0 1 0 1 1 2h1c0-1 1-2 1-3v-1z" class="H"></path><path d="M272 343l2-1c2 1 2 1 3 1 2 0 3 1 4 2l2 1v3c3 1 5 2 7 5 1 1 5 5 5 7h0c-2-2-4-3-5-5 0-1-1-2-2-3-1-2-2-3-5-3 0 1 1 1 2 2 0 1 1 1 1 2h1v1h-1l-3-3c-1-1-3-1-4-2v-1c-1-1-2-2-3-2-1-1-1-1-1-2l-1-1h0l-2-1z" class="L"></path><path d="M272 343l2-1c2 1 2 1 3 1 2 0 3 1 4 2l2 1v3c-1 0-1 0-2-1l-1-1-1 1-1-1-3-2-1-1h0l-2-1z" class="F"></path><path d="M262 342c1 0 2 0 3-1l1 1 1 1v2h1l1-2h3l2 1h-1-3c-1 0 0 0-1 1l-1 2h-1 0c0 1 0 2 1 3h0v3h-1c0-1-1-2-1-3-1-1-1-2-2-2l-1 1c-1 2-3 3-5 4-2 0-4 0-6-1-2 2-3 4-5 5-1 0-1 0-1-1 1-1 1-2 2-4 0-1 2-2 2-3s-1-1 0-2l1 1v2h1l1-3c0-1 1-1 2-1l2 1h1v-3h1c0 1 0 1 1 2h1c0-1 1-2 1-3v-1z" class="L"></path><defs><linearGradient id="AO" x1="289.37" y1="312.849" x2="266.364" y2="379.573" xlink:href="#B"><stop offset="0" stop-color="#f07271"></stop><stop offset="1" stop-color="#f39999"></stop></linearGradient></defs><path fill="url(#AO)" d="M218 288h4 2c2 1 4 1 6 1 2 1 3 1 5 1l8 3 7 5h1l8 5c1 0 2 1 3 2 2 0 3 1 5 1 1 0 3 1 4 2 1 0 3 1 4 1l-2 2 6 3c1 2 2 3 4 4l-1 2c5 3 9 8 14 11 8 8 16 17 21 27 3 6 5 12 8 17 5 7 11 13 15 21l-5-4h0c-1 1-2 1-3 1 1 2 3 4 4 5-2-1-4-2-6-4l-1 1-2-2c-2-1-3-1-4-1s-2-1-3-2l-7-4c-4-1-8-3-12-4 1 0 2 0 4-1v-1h-2c-5-2-10-3-15-4h-8-11c-2 0-3 1-5 1-4 1-10 3-14 3h-3c-2 0-3-1-4-2l1-1-2-1h-4-1 1c-1-1-9 0-11 0l14-3c-3-1-6 0-9 1-4 0-9 0-13 1h-2v-1h5l25-4c9-1 19-1 29-1l17 1 13 2c3 0 7 1 10 0 1 0 1 0 1-1-1-6-4-11-8-16h0c-1-4-4-7-6-9-13-17-32-31-51-40-3 0-5 0-7 2-2 3-2 5-3 9-2-2-1-4-1-6-1-4-3-8-6-11-2-2-4-4-7-4h0c-2-1-2-1-3-3h-1c0-1-1-2-2-3v-1l-4-1z"></path><path d="M319 380c3 2 6 3 9 6 0 1 1 2 1 4h0c-2 0-2 0-3-1s-4-2-5-3l1-1 1-1-4-4z" class="S"></path><path d="M328 386c2 1 5 4 7 6h0c-1 1-2 1-3 1 1 2 3 4 4 5-2-1-4-2-6-4-2-1-3-3-4-4h-1l1-1c1 1 1 1 3 1h0c0-2-1-3-1-4z" class="L"></path><path d="M247 298h3 1l8 5c1 0 2 1 3 2 2 1 3 3 5 5l-20-12z" class="R"></path><path d="M262 305c2 0 3 1 5 1 1 0 3 1 4 2 1 0 3 1 4 1l-2 2 6 3c1 2 2 3 4 4l-1 2c-3-3-8-6-12-8-1-1-2-2-3-2-2-2-3-4-5-5z" class="D"></path><path d="M218 288h4 2c2 1 4 1 6 1 2 1 3 1 5 1l8 3 7 5h-3c-8-3-16-7-25-9l-4-1z" class="Y"></path><defs><linearGradient id="AP" x1="314.402" y1="378.026" x2="311.907" y2="381.984" xlink:href="#B"><stop offset="0" stop-color="#180100"></stop><stop offset="1" stop-color="#320e0d"></stop></linearGradient></defs><path fill="url(#AP)" d="M300 375c6 1 13 2 19 5l4 4-1 1-1 1c-6-4-14-6-21-10h0v-1z"></path><path d="M225 293c6 2 13 5 19 8 2 2 6 3 8 5-3 0-5 0-7 2-2 3-2 5-3 9-2-2-1-4-1-6-1-4-3-8-6-11-2-2-4-4-7-4h0c-2-1-2-1-3-3z" class="H"></path><path d="M245 308c0-2 0-1-1-2-2-1-3-3-4-4v-1h4c2 2 6 3 8 5-3 0-5 0-7 2z" class="F"></path><path d="M289 374c3 0 8 0 11 1v1h0c7 4 15 6 21 10 1 1 4 2 5 3l-1 1h1c1 1 2 3 4 4l-1 1-2-2c-2-1-3-1-4-1s-2-1-3-2l-7-4c-4-1-8-3-12-4 1 0 2 0 4-1v-1h-2c-5-2-10-3-15-4l1-2z" class="P"></path><path d="M241 373c17-2 32-2 48 1l-1 2h-8-11c-2 0-3 1-5 1-4 1-10 3-14 3h-3c-2 0-3-1-4-2l1-1-2-1h-4-1 1c-1-1-9 0-11 0l14-3z" class="W"></path><path d="M244 377c5-1 10-2 15-2 1 0 3 0 5 1v1c-4 1-10 3-14 3h-3c-2 0-3-1-4-2l1-1z" class="P"></path><path d="M379 408c-1-2-1-5-1-7 0-4 1-7 4-10 1-1 2-1 3-1 3 0 4 2 6 4 2 4 4 9 5 15h1l4 13 2-2h0c0-4-2-9-1-12l2 1 28 98 26 87c2 4 3 8 4 12l7 23c-1 1-1 2-1 4v1l-2-4-3-7c-1-3-2-5-3-8-1 1-1 2-2 2l-9-19c-1-1-2-3-3-5 0 3 2 6 2 9-4-3-7-11-9-16l-1-1c-1 1-1 2-1 2l-1-1h-1c1 3 3 5 3 8l-1 2-2-4-1 1c0-1 0-2-1-3 0 1-1 1-2 1l-2-6-24-50-9-14c1-1 2-1 3-1l-4-6 2-2-7-10-2-1c0-1-1-2-1-3l-1-1c0-1 0-2-1-2 0-1 0-2-1-3 0-1-1-2-2-3h-1c0-1-1-2-1-3v-1c3 4 6 7 9 11h1c-1-4-6-10-8-13-2-4-3-8-6-12-1 0-1-1-1-2s-1-1-1-2l-1 1v-9-2l1 1c1-2-1-5-1-7l2 3h1l7 11 5 7 5 9c2 0 3 0 4-2l-4-9h-1c-2-4-5-8-7-13-2-2-3-7-5-9l-6-12v-3-3-1l2-1c1 2 2 4 4 6 1 1 1 2 2 3h1 1 0l-1-3c-1-1-1-2-1-4v-1l-3-7c-1-4-3-8-4-12-1-3-3-5-4-7l1-1 5 7c0 2 1 3 1 4 1 2 1 3 2 4h1l-2-9z" class="D"></path><path d="M454 592l1 3-1 2-1-3 1-2z" class="R"></path><path d="M449 584l2 2 3 6-1 2-5-9 1 1v-2z" class="I"></path><path d="M430 518l5 19-1 1c-2-6-5-12-6-19h1 0l1-1z" class="T"></path><path d="M435 537l8 23h-2c0-2-1-3-2-5 0-3-2-6-3-9s-2-6-2-8l1-1z" class="B"></path><path d="M438 556l1-1c1 2 2 3 2 5h2c2 2 3 6 4 8 0 2 1 4 2 7-2 2-1 3-1 5-4-7-7-16-10-24z" class="Y"></path><path d="M441 560h2c2 2 3 6 4 8-1 1-1 2-2 3l-4-11z" class="C"></path><path d="M433 553h1 0l1-4c0 1 2 6 3 7 3 8 6 17 10 24l1 4v2l-1-1-14-30-1-2z" class="F"></path><path d="M403 420l27 98-1 1h0-1l-12-43-11-42c-1-4-3-8-4-12l2-2z" class="a"></path><path d="M396 409h1l4 13c1 4 3 8 4 12l11 42c0 1-1 2-1 3l1 1v5c-2-5-4-11-5-16-1-3-1-6-2-8l-13-52z" class="G"></path><defs><linearGradient id="AQ" x1="455.733" y1="589.992" x2="443.567" y2="593.911" xlink:href="#B"><stop offset="0" stop-color="#c81113"></stop><stop offset="1" stop-color="#ff4941"></stop></linearGradient></defs><path fill="url(#AQ)" d="M433 560l-1-4h0v-1h2l14 30 5 9 1 3 1-2 2 2h0l2 2c0-1-1-3-2-4l1-1c2 4 3 8 4 12l7 23c-1 1-1 2-1 4v1l-2-4-3-7c-1-3-2-5-3-8-1 1-1 2-2 2l-9-19c-1-1-2-3-3-5-1-3-3-6-4-9v-4c0-2-2-4-3-6l-6-14z"></path><path d="M455 595l2 2 3 7-1 1c-2-1-4-6-5-8l1-2z" class="C"></path><path d="M442 580l9 17v-1c-2-1-3-2-3-4l-1-1c0 3 2 4 2 7-1-1-2-3-3-5-1-3-3-6-4-9v-4z" class="L"></path><path d="M449 598c0-3-2-4-2-7l1 1c0 2 1 3 3 4v1c4 5 7 12 9 18-1 1-1 2-2 2l-9-19z" class="F"></path><path d="M396 451c1 1 1 3 3 3 2 5 2 11 4 15v-2l2 2v3l2 1c2 3 2 7 4 10 0-2-1-9-2-11v-1-2l-1-3-1-4v-1h2c1 2 1 5 2 8 1 5 3 11 5 16v-5l-1-1c0-1 1-2 1-3l12 43c1 7 4 13 6 19 0 2 1 5 2 8s3 6 3 9l-1 1c-1-1-3-6-3-7l-1 4h0-1c-1-1-2-4-2-5l-31-75c0-2 0-3-1-5h1l1-1c-1-1-1-2-2-3v-2-1c-1-2-1-4-2-6 0-2-1-3-1-4z" class="H"></path><path d="M417 502l2 3 1-1c2 3 2 9 2 12-2-4-4-9-5-14z" class="F"></path><path d="M405 472l2 1c2 3 2 7 4 10l1 2c1 1 1 2 1 2 1 2 2 3 2 5 1 1 2 3 2 5 0 1 0 1 1 2l2 5-1 1-2-3-12-30z" class="B"></path><path d="M409 461c1 2 1 5 2 8 1 5 3 11 5 16l2 7h0-1l1 2v5c-1-1-1-1-1-2 0-2-1-4-2-5 0-2-1-3-2-5 0 0 0-1-1-2l-1-2c0-2-1-9-2-11v-1-2l-1-3-1-4v-1h2z" class="F"></path><defs><linearGradient id="AR" x1="421.992" y1="507.398" x2="411.121" y2="510.498" xlink:href="#B"><stop offset="0" stop-color="#860000"></stop><stop offset="1" stop-color="#570c11"></stop></linearGradient></defs><path fill="url(#AR)" d="M396 451c1 1 1 3 3 3 2 5 2 11 4 15 1 5 3 9 4 13 3 9 6 18 10 26 1 2 2 5 4 7 1 2 2 3 3 5l6 18c1 3 3 7 5 11h0l-1 4h0-1c-1-1-2-4-2-5l-31-75c0-2 0-3-1-5h1l1-1c-1-1-1-2-2-3v-2-1c-1-2-1-4-2-6 0-2-1-3-1-4z"></path><defs><linearGradient id="AS" x1="409.678" y1="484.332" x2="400.239" y2="489.211" xlink:href="#B"><stop offset="0" stop-color="#e52d2b"></stop><stop offset="1" stop-color="#eb6763"></stop></linearGradient></defs><path fill="url(#AS)" d="M376 428c1 2 2 4 4 6 1 1 1 2 2 3h1 1 0l-1-3c-1-1-1-2-1-4v-1c2 2 3 4 4 7l6 17 8 20 31 75h-2l-5-12h-1c-3-4-4-8-7-12l-2-5c-1-2-3-4-4-6h0-1-1c-2-2-4-7-5-10-1-2-2-5-3-7l-4-9-3-6c2 0 3 0 4-2l-4-9h-1c-2-4-5-8-7-13-2-2-3-7-5-9l-6-12v-3-3-1l2-1z"></path><path d="M387 449l18 46h-1-1-1l-3-8c0-1-1-2-2-2l-1 2-3-6c2 0 3 0 4-2l-4-9-7-19 1-2z" class="C"></path><path d="M397 479c2 3 3 7 4 10l3 6h-1-1l-3-8c0-1-1-2-2-2l-1 2-3-6c2 0 3 0 4-2z" class="R"></path><path d="M396 487l1-2c1 0 2 1 2 2l3 8h1 1 1l19 41h-1c-3-4-4-8-7-12l-2-5c-1-2-3-4-4-6h0-1-1c-2-2-4-7-5-10-1-2-2-5-3-7l-4-9z" class="K"></path><path d="M376 428c1 2 2 4 4 6 1 1 1 2 2 3l5 12-1 2 7 19h-1c-2-4-5-8-7-13-2-2-3-7-5-9l-6-12v-3-3-1l2-1z" class="O"></path><path d="M376 428c1 2 2 4 4 6v3 1 1l-6-9v-1l2-1z" class="K"></path><path d="M380 434c1 1 1 2 2 3l5 12-1 2c-1-1-1-3-2-3l-4-9v-1-1-3z" class="J"></path><path d="M379 408c-1-2-1-5-1-7 0-4 1-7 4-10 1-1 2-1 3-1 3 0 4 2 6 4 2 4 4 9 5 15l13 52h-2v1l1 4 1 3v2 1c1 2 2 9 2 11-2-3-2-7-4-10l-2-1v-3l-2-2v2c-2-4-2-10-4-15-2 0-2-2-3-3 0 1 1 2 1 4 1 2 1 4 2 6v1 2c1 1 1 2 2 3l-1 1h-1c1 2 1 3 1 5l-8-20-6-17c-1-3-2-5-4-7l-3-7c-1-4-3-8-4-12-1-3-3-5-4-7l1-1 5 7c0 2 1 3 1 4 1 2 1 3 2 4h1l-2-9z"></path><path d="M393 417c-1-3-2-6-2-9 1 1 2 3 2 5 2 5 2 11 5 16 2 3 3 6 4 9-1 0-2 0-3 1 0-1-1-3-1-4l-5-18z" class="L"></path><path d="M371 403l1-1 5 7c0 2 1 3 1 4 1 2 1 3 2 4h1l-2-9c1 0 1 0 2 1l-1-6v-1c2 2 2 4 2 6 1 2 2 4 3 7 2 4 3 8 4 13 0 1 0 1 1 2l1 3 1 3 1 3v3l1 1c0 1 0 2 1 3v2h1v3c0 1 1 2 1 4 1 2 1 4 2 6v1 2c1 1 1 2 2 3l-1 1h-1c1 2 1 3 1 5l-8-20-6-17c-1-3-2-5-4-7l-3-7c-1-4-3-8-4-12-1-3-3-5-4-7z" class="F"></path><path d="M386 423l2 6 1 4v3c-1-1-2-2-3-4-1-1-1-5-2-8l2-1z" class="U"></path><path d="M379 408c1 0 1 0 2 1s4 8 3 9l-1 2c-1-1-1-2-2-3l-2-9z" class="W"></path><path d="M386 432c1 2 2 3 3 4v-3c1 2 1 3 2 5l1 6 1 2c0 1 0 2 1 3v1 3h-1s-1-1-1 0l-6-17v-4z" class="S"></path><path d="M371 403l1-1 5 7c0 2 1 3 1 4 1 2 1 3 2 4h1c1 1 1 2 2 3l1-2 2 5-2 1c1 3 1 7 2 8v4c-1-3-2-5-4-7l-3-7c-1-4-3-8-4-12-1-3-3-5-4-7z" class="c"></path><path d="M383 420l1-2 2 5-2 1-1-4z" class="h"></path><path d="M399 454l-10-35c-1-5-3-11-3-15h0l2 6c3 2 3 4 4 7h1l5 18c0 1 1 3 1 4 1-1 2-1 3-1 1 5 3 9 4 14 0 3 1 6 1 9v1l1 4 1 3v2 1c1 2 2 9 2 11-2-3-2-7-4-10l-2-1v-3l-2-2v2c-2-4-2-10-4-15z" class="G"></path><path d="M388 410c3 2 3 4 4 7h1l5 18c0 1 1 3 1 4l8 34-2-1v-3l-17-59z" class="U"></path><path d="M399 439c1-1 2-1 3-1 1 5 3 9 4 14 0 3 1 6 1 9v1l1 4 1 3v2 1c1 2 2 9 2 11-2-3-2-7-4-10l-8-34z" class="C"></path><path d="M374 458c1-2-1-5-1-7l2 3h1l7 11 5 7 5 9 3 6 4 9c1 2 2 5 3 7 1 3 3 8 5 10h1 1 0c1 2 3 4 4 6l2 5c3 4 4 8 7 12h1l5 12h2c0 1 1 4 2 5l1 2h-2v1h0l1 4 6 14c1 2 3 4 3 6v4c1 3 3 6 4 9 0 3 2 6 2 9-4-3-7-11-9-16l-1-1c-1 1-1 2-1 2l-1-1h-1c1 3 3 5 3 8l-1 2-2-4-1 1c0-1 0-2-1-3 0 1-1 1-2 1l-2-6-24-50-9-14c1-1 2-1 3-1l-4-6 2-2-7-10-2-1c0-1-1-2-1-3l-1-1c0-1 0-2-1-2 0-1 0-2-1-3 0-1-1-2-2-3h-1c0-1-1-2-1-3v-1c3 4 6 7 9 11h1c-1-4-6-10-8-13-2-4-3-8-6-12-1 0-1-1-1-2s-1-1-1-2l-1 1v-9-2l1 1z" class="H"></path><path d="M428 552l3 6 1 2c0 1-1 1-1 2h-1c-1-3-2-6-4-9l2-1z" class="c"></path><path d="M436 581h1 0c1 3 3 5 4 7l1-1v-1c-1-2-2-4-2-5v-1c0 1 1 2 2 4 1 3 3 6 4 9 0 3 2 6 2 9-4-3-7-11-9-16l-3-5z" class="Y"></path><path d="M388 472l5 9 3 6 4 9c1 2 2 5 3 7l-1 1c-1-2-2-5-3-8-3-5-7-10-10-15v-1h1c0-1-1-2-1-3-1-2-1-3-1-5z" class="S"></path><path d="M390 502l1-1v1l1 1c1 1 1 1 1 2h1c2 1 4 4 5 6s2 4 3 7h0v2h0c-1 1-1 1-1 2v1l-2-3-4-6 2-2-7-10z" class="C"></path><path d="M397 512l5 6v2h0c-1 1-1 1-1 2v1l-2-3-4-6 2-2z" class="Y"></path><path d="M374 458c1-2-1-5-1-7l2 3h1l7 11 5 7c0 2 0 3 1 5 0 1 1 2 1 3h-1l-11-16c-1-2-3-4-4-6z" class="E"></path><path d="M403 503c1 3 3 8 5 10l16 30c-1 1-1 1-1 2-1-3-4-4-5-7l-10-21c-2-4-4-8-6-13l1-1z" class="L"></path><path d="M425 565h2v1c2 5 6 12 9 15l3 5-1-1c-1 1-1 2-1 2l-1-1h-1c1 3 3 5 3 8l-1 2-2-4-1 1c0-1 0-2-1-3 0 1-1 1-2 1l-2-6h2c0-3-2-5-2-7l-4-13z" class="B"></path><path d="M430 578h1c1 1 4 5 5 7v1h-1c-1 0-4-5-5-6v-2z" class="c"></path><path d="M429 578h1v2c1 1 4 6 5 6 1 3 3 5 3 8l-1 2-2-4-1 1c0-1 0-2-1-3 0 1-1 1-2 1l-2-6h2c0-3-2-5-2-7z" class="B"></path><path d="M429 578h1v2c1 4 2 7 3 10 0 1-1 1-2 1l-2-6h2c0-3-2-5-2-7z" class="W"></path><path d="M408 513h1 1 0c1 2 3 4 4 6l2 5c3 4 4 8 7 12h1l5 12h2c0 1 1 4 2 5l1 2h-2v1h0l1 4h-1l-1-2-3-6-2 1-3-8c0-1 0-1 1-2l-16-30z" class="M"></path><path d="M423 545c0-1 0-1 1-2l4 9-2 1-3-8z" class="P"></path><path d="M429 548h2c0 1 1 4 2 5l1 2h-2v1h0l1 4h-1l-1-2c1-3-1-8-2-10z" class="b"></path><defs><linearGradient id="AT" x1="417.438" y1="550.999" x2="412.041" y2="554.14" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#150102"></stop></linearGradient></defs><path fill="url(#AT)" d="M399 520l2 3c9 13 18 27 24 42l4 13c0 2 2 4 2 7h-2l-24-50-9-14c1-1 2-1 3-1z"></path><path d="M805 288h3l1 1-1 2h2c2-1 4-1 6-2l1 1v3h0c0 1 0 1 1 2-5 3-9 6-12 9-3 4-4 8-6 12 0 2-1 4-1 6v4h-1v-6h-1c0 2-1 5-3 6l-3 3-1-1c0 2 1 4 1 6h0c1 5 4 9 7 14 3 4 7 9 11 12l1 1 1 2s1 1 2 1l2 3 1 1 2 1h0 1c1 2 2 1 4 3h-1-3l-1 1 1 1c-2 1-7 0-9 0h-6l1 2-4-1-7 1c0 1-1 1-1 2h-3-2v1h0l-9-3h-9c-2-1-10-1-12 0h-18c-4 1-7 2-11 2-4 1-9 1-13 3h0l1 1-1 1c-3 1-5 3-8 4-2 1-3 1-4 2l-2 2 1 1c-7 5-14 10-20 16l-3 3-11 12h-1l1-2h0l2-2v-1l2-2h0c1-2 3-4 4-6v-2-2h0l1-2s0-1 1-2h0v-1l-1-1c-1-4 11-20 13-25 0-1 0 0 1-1v-3c1-1 1-2 2-3l2-4c0-1 0-2 1-2 0-2 2-4 2-6 0-1 0-1 1-2l1-1v-3l2-2v-1c1-1 3-2 4-4h0 2l6-6v-1l6-5v1c1 0 1-1 2 0h0c1 1 1 2 1 4 22-20 47-37 75-46 0 0 4-1 5-2h1z"></path><path d="M765 332c1-2 0-4 2-5 1 3 2 5 4 7v2 1h-1c-3-1-3-3-4-5h-1z" class="T"></path><path d="M771 334v-1c1-1 0-3 1-5 1 0 3 2 3 3 1 2 2 3 2 5-1 0-1 0-2 1-2-2-2-2-2-5h-1v3l-1 1v-2z" class="a"></path><path d="M721 356v-1c0-1 0-1 1-2l1-1-1-1v-1h0l4-7h2 1c1 1 1 0 2 0 1 1 1 1 1 2l-11 11h0z" class="E"></path><path d="M766 346c-1-2-2-5-3-6-4-8-10-9-17-11v-1c5-1 10 1 14 4l2 1 2 2h0v1c1 1 2 2 2 3h-2c1 2 3 4 2 7z" class="L"></path><path d="M771 336l1-1v-3h1c0 3 0 3 2 5 1-1 1-1 2-1l3 3v-3c1 1 1 2 2 2l5 9c-2-2-5-2-7-3v-1c-1-1-1-2-2-3-1 1-1 1-2 1-1-2-3-3-5-4v-1z" class="F"></path><path d="M728 343c1-1 3-2 4-4 2-1 3-2 6-3 0-1 1-1 2-2 1 0 2-1 4-1 1-1 2-1 3-1l1 1c2 0 4 1 6 2-1 0-1 0-2 1h-1-1-1c-1-1-3 0-4 0h0c-1 1-1 1-2 1s-1 1-2 2c-2 0-2 0-4 2-1 1-4 4-5 4 0-1 0-1-1-2-1 0-1 1-2 0h-1z" class="h"></path><path d="M795 351c1-2-2-4-2-5v-3-1l4 6h1c3 4 7 9 11 12l1 1 1 2s1 1 2 1l2 3 1 1 2 1h0 1c1 2 2 1 4 3h-1-3l-1 1c-1-1-4-2-5-3-7-5-14-12-18-19z" class="O"></path><path d="M764 335v-2c-1-1-1-1-1-2v-2l-1-1h1l2 4h1c1 2 1 4 4 5h1c2 1 4 2 5 4 1 0 1 0 2-1 1 1 1 2 2 3v1c2 1 5 1 7 3 4 7 8 12 13 18h-1l-6-6h0c-1-1-2-1-3-1-1 1-1 0-2 0-2-2-4-4-7-5h0c-1-2-3-3-4-4s-1-2-2-3c-3-2-5-5-9-7 0-1-1-2-2-3v-1h0z" class="B"></path><path d="M799 293l2 1-2 1c-6 4-10 10-11 17-1 4 0 8 0 12 1 3 1 8 3 10 1 5 4 9 7 14h-1l-4-6v1 3c0 1 3 3 2 5-2-3-3-5-5-7-3-8-5-15-7-23l-1-10c1-1 2-3 2-4 1-4 3-6 6-9 2-1 3-1 4-3 1-1 3-1 5-2z" class="M"></path><path d="M784 307c0 4 1 10-1 14l-1-10c1-1 2-3 2-4z" class="K"></path><path d="M788 324c1 3 1 8 3 10 1 5 4 9 7 14h-1l-4-6v1 3c0 1 3 3 2 5-2-3-3-5-5-7 1-1 1-3 1-3 0-2-2-5-2-7-1-2-2-5-2-7v1h1v-4z" class="b"></path><defs><linearGradient id="AU" x1="802.775" y1="294.174" x2="788.73" y2="326.667" xlink:href="#B"><stop offset="0" stop-color="#640200"></stop><stop offset="1" stop-color="#9e110d"></stop></linearGradient></defs><path fill="url(#AU)" d="M805 288h3l1 1-1 2h2c2-1 4-1 6-2l1 1v3h0c0 1 0 1 1 2-5 3-9 6-12 9-3 4-4 8-6 12 0 2-1 4-1 6v4h-1v-6h-1c0 2-1 5-3 6l-3 3-1-1c0 2 1 4 1 6h0c-2-2-2-7-3-10 0-4-1-8 0-12 1-7 5-13 11-17l2-1-2-1h1v-2l-1-1s4-1 5-2h1z"></path><path d="M808 291h2l-2 1v1c1 1 4 1 5 2l-2 1h0c0-1 0 0-1-1h-1c-3-1-7 0-9 1l-1-1 2-1 7-3z" class="S"></path><path d="M805 288h3l1 1-1 2-7 3-2-1h1v-2l-1-1s4-1 5-2h1z" class="O"></path><path d="M816 289l1 1v3h0c0 1 0 1 1 2-5 3-9 6-12 9 0-2 4-6 5-8h0l2-1c-1-1-4-1-5-2v-1l2-1c2-1 4-1 6-2z" class="E"></path><defs><linearGradient id="AV" x1="771.335" y1="340.48" x2="760.908" y2="379.1" xlink:href="#B"><stop offset="0" stop-color="#8a0605"></stop><stop offset="1" stop-color="#ba0f0e"></stop></linearGradient></defs><path fill="url(#AV)" d="M737 341c2-2 2-2 4-2 1-1 1-2 2-2s1 0 2-1h0c1 0 3-1 4 0h1 1 3c3 0 5 2 7 4s3 4 4 6h1 0c1-3-1-5-2-7h2c4 2 6 5 9 7 1 1 1 2 2 3s3 2 4 4h0c3 1 5 3 7 5 1 0 1 1 2 0 1 0 2 0 3 1h0l6 6h1l8 7 2 2h-6c-13-2-27-5-40-4l-9-1-31 1-6 1-6 1c3-6 5-11 9-16h0l11-11c1 0 4-3 5-4z"></path><path d="M792 370l-2-2-1-1h2 3l1 1c2 0 3 1 4 2l1 1-8-1zm-17-17l2 2c0 1 1 3 3 3 0 1 1 1 2 2h1l1 1 3 3v1c-1 0-2 0-3-1h-1c-3-1-4-2-5-4v-1l-1-3c-1-1-2-2-2-3z" class="H"></path><path d="M737 341c2-2 2-2 4-2 1-1 1-2 2-2s1 0 2-1h0c1 0 3-1 4 0h1 1 3c-2 2-3 2-5 2 0 1 0 2-1 3v2h-1v-1c-1-1-2-1-2-2-2 1-3 3-5 5h-1c1-1 1-2 2-2v-1l3-3h-1l-1 1c-1 0-1 0-2 1h-1-2z" class="B"></path><path d="M755 369h13 7 0c3 0 4 0 7 1h2 1 1c1 0 2 0 3 1h1c-1-1-2-1-4-1v-1c2 0 4 1 6 1l8 1c3 0 5 1 8 1l2 2h-6c-13-2-27-5-40-4l-9-1z" class="D"></path><path d="M737 341h2 1c1-1 1-1 2-1l1-1h1l-3 3v1c-1 0-1 1-2 2h1l-2 2c-1 4-3 5-5 8l-7 9c-1 2-2 4-2 6l-6 1-6 1c3-6 5-11 9-16h0l11-11c1 0 4-3 5-4z" class="P"></path><path d="M718 371c-1-1-1-1-3-1 1 0 2-1 3 0h2c1-1 1-2 1-2l2-1c1 0 3-3 3-5 1-1 1 0 1-1 1-1 2-3 2-4 1-2 3-6 5-7 1-1 3-2 4-3-1 4-3 5-5 8l-7 9c-1 2-2 4-2 6l-6 1z" class="L"></path><path d="M799 290l1 1v2h-1c-2 1-4 1-5 2-1 2-2 2-4 3-3 3-5 5-6 9 0 1-1 3-2 4l-1-1v-1c-2-2-4-2-6-3l-5 1c-5 2-10 6-15 9-18 11-31 24-42 43-2 3-5 7-6 11-1 1-1 1-1 2h6l6-1 6-1 31-1 9 1c13-1 27 2 40 4l1 2-4-1-7 1c0 1-1 1-1 2h-3-2v1h0l-9-3h-9c-2-1-10-1-12 0h-18c-4 1-7 2-11 2-4 1-9 1-13 3h0l1 1-1 1c-3 1-5 3-8 4-2 1-3 1-4 2l-2 2 1 1c-7 5-14 10-20 16l-3 3-11 12h-1l1-2h0l2-2v-1l2-2h0c1-2 3-4 4-6v-2-2h0l1-2s0-1 1-2h0v-1l-1-1c-1-4 11-20 13-25 0-1 0 0 1-1v-3c1-1 1-2 2-3l2-4c0-1 0-2 1-2 0-2 2-4 2-6 0-1 0-1 1-2l1-1v-3l2-2v-1c1-1 3-2 4-4h0 2l6-6v-1l6-5v1c1 0 1-1 2 0h0c1 1 1 2 1 4 22-20 47-37 75-46z" class="m"></path><path d="M717 376l9-1c-2 2-4 3-7 3h0l-2-2z" class="S"></path><path d="M685 395l4-3 2 2c-1 1-1 2-3 3h-2s-1-1-1-2z" class="P"></path><path d="M693 389c2-2 5-4 7-6 0 1-1 2-1 3l2 1-1 1-2 2h-1c-2 0-3 0-4-1z" class="S"></path><path d="M689 392l4-3c1 1 2 1 4 1h1l2-2 1 1c1 0 1 0 1 1l2-1-2 2c-2 1-5 4-7 4-1 0-2 0-3 1l-1 1h-3c2-1 2-2 3-3l-2-2z" class="C"></path><path d="M689 392l4-3c1 1 2 1 4 1h1l-7 4-2-2z" class="F"></path><path d="M764 370c13-1 27 2 40 4l1 2-4-1c-3 0-7-1-11-1l-27-3c3-1 6 0 9 0h1c1 1 3 1 4 0h1l1 1c1 0 3-1 4 0s1 0 3 0h0c2 1 3 1 4 1h-1l-8-1c-2-1-4 0-6-1-3-1-6 0-8 0l-3-1z" class="r"></path><path d="M726 375c1-1 4-1 5-1l15-2c-3 1-4 2-6 4-4 1-7 2-11 2-4 1-9 1-13 3h0l1 1-1 1c-1 0-1-1-2-2l1-1 4-2h0c3 0 5-1 7-3z" class="T"></path><path d="M794 295c-1 2-2 2-4 3-3 3-5 5-6 9 0 1-1 3-2 4l-1-1v-1c-2-2-4-2-6-3l-5 1c2-2 5-3 7-4 6-3 11-5 17-8z" class="H"></path><path d="M775 306c2-1 3-1 4-2 2 0 2 0 4 1 0 2-1 3-2 5v-1c-2-2-4-2-6-3z" class="C"></path><path d="M700 383c4-4 12-6 17-7l2 2-4 2-1 1c1 1 1 2 2 2-3 1-5 3-8 4-2 1-3 1-4 2l-2 1c0-1 0-1-1-1l-1-1 1-1-2-1c0-1 1-2 1-3z" class="W"></path><path d="M714 381c1 1 1 2 2 2-3 1-5 3-8 4-2 1-3 1-4 2l-2 1c0-1 0-1-1-1l-1-1 1-1c4-2 8-4 13-6z" class="B"></path><path d="M702 391l1 1c-7 5-14 10-20 16l-3 3-11 12h-1l1-2h0l2-2v-1l2-2h0c1-2 3-4 4-6v-2-2h0l1-2s0-1 1-2h0l6-7c0 1 1 2 1 2h2 3l1-1c1-1 2-1 3-1 2 0 5-3 7-4z" class="R"></path><path d="M688 397h3l1-1c1-1 2-1 3-1-2 2-3 3-5 4-3 2-6 3-9 6-1 2-3 3-4 5v-2-2h0l1-2s0-1 1-2h0l6-7c0 1 1 2 1 2h2z" class="H"></path><defs><linearGradient id="AW" x1="767.975" y1="372.215" x2="767.589" y2="376.989" xlink:href="#B"><stop offset="0" stop-color="#1f0001"></stop><stop offset="1" stop-color="#380202"></stop></linearGradient></defs><path fill="url(#AW)" d="M763 371l27 3c4 0 8 1 11 1l-7 1c0 1-1 1-1 2h-3-2v1h0l-9-3h-9c-2-1-10-1-12 0h-18c2-2 3-3 6-4l17-1z"></path><path d="M715 336l6-5v1c1 0 1-1 2 0h0c1 1 1 2 1 4l-10 11h0c-3 5-6 9-9 14-2 5-3 10-6 14-4 7-10 13-15 19-1 2-3 5-5 7l-1-1c-1-4 11-20 13-25 0-1 0 0 1-1v-3c1-1 1-2 2-3l2-4c0-1 0-2 1-2 0-2 2-4 2-6 0-1 0-1 1-2l1-1v-3l2-2v-1c1-1 3-2 4-4h0 2l6-6v-1z" class="Y"></path><path d="M723 332h0c1 1 1 2 1 4l-10 11h0l-1-1 2-1c0-1-1 0 0-1l3-3h-1 0c1-4 4-6 6-9z" class="N"></path><path d="M715 336l6-5v1c-4 4-8 9-11 13l-13 18-1 1c0-1 0-2 1-2 0-2 2-4 2-6 0-1 0-1 1-2l1-1v-3l2-2v-1c1-1 3-2 4-4h0 2l6-6v-1z" class="L"></path><defs><linearGradient id="AX" x1="227.787" y1="270.965" x2="235.713" y2="342.114" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#1c0505"></stop></linearGradient></defs><path fill="url(#AX)" d="M226 261c2 2 4 4 7 5h0 1c-1-1-2-2-3-2l-1-1c-1 0-2-1-2-2 1 0 2 1 4 1 1 1 0 1 1 1 2 0 3 1 3 2h1c2 0 4 1 5 2 1 2 9 4 12 5h0l5 1c3 2 6 3 9 3 0 1 1 2 2 2 2 2 6 2 8 3h1l9 5 9 6c6 7 15 13 21 21h0l3 3v1c1 2 1 4 1 5l1 2c2 2 4 4 5 6l4 7c4 5 5 13 9 19h0l2-1-1-1c1-1 1-2 1-3l3 3v-1h1c1 1 2 2 3 2l8 15c1 2 2 5 4 6l2 2v1c1 5 5 9 5 14 0 3 2 4 2 7l1 2-1 1c1 2 3 4 4 7 1 4 3 8 4 12l3 7v1c0 2 0 3 1 4l1 3h0-1-1c-1-1-1-2-2-3-2-2-3-4-4-6l-2 1v1 3 3l6 12c2 2 3 7 5 9 2 5 5 9 7 13h1l4 9c-1 2-2 2-4 2l-5-9-5-7-7-11c-1-2-5-7-6-8l-3-4c-8-13-20-24-31-34v-2-1-1-1c-1-1-2-1-2-3h-1l4 2h0c1-2 0-3-1-4s-3-3-4-5c1 0 2 0 3-1h0l5 4c-4-8-10-14-15-21-3-5-5-11-8-17-5-10-13-19-21-27-5-3-9-8-14-11l1-2c-2-1-3-2-4-4l-6-3 2-2c-1 0-3-1-4-1-1-1-3-2-4-2-2 0-3-1-5-1-1-1-2-2-3-2l-8-5h-1l-7-5c-3-1-5-2-8-3-2 0-3 0-5-1-2 0-4 0-6-1h-2-4l-4-1c-2 1-5 1-7 2h0c-7 0-13-1-20-1-2 1-5 1-6 2h-2l-1-2c-10 4-21 8-29 15-6 5-9 10-11 18l-1 8h-1c0-10 2-18 8-27 14-18 34-25 55-28l7-1 4-1v-1h-1l1-1h0 2c2-1 5-1 8-2 2-1 3-4 4-6l2-1z"></path><path d="M318 313l3 3v1c1 2 1 4 1 5l-5-6c0 1 1 1 2 1v-1s0-1-1-2v-1z" class="l"></path><path d="M236 268l5 3c-3 0-6 1-9 1l2-3 2-1z" class="h"></path><path d="M220 268c0 2 0 2 1 3-5 1-10 2-15 2l4-1v-1h-1l1-1h0 2c2-1 5-1 8-2z" class="l"></path><path d="M323 324c2 2 4 4 5 6l4 7v1c-1 1-1 1-2 1l-2-3-7-9c1 1 2 1 3 2l1 1c0 1 1 1 1 2h2l-3-3v-1c-1-1-1-2-2-3v-1z" class="F"></path><path d="M226 261c2 2 4 4 7 5l3 2-2 1-2 3-3-1h-8c-1-1-1-1-1-3 2-1 3-4 4-6l2-1z" class="a"></path><path d="M226 261c2 2 4 4 7 5l3 2-2 1-2 3-3-1v-2c-1-2-3-5-5-7l2-1z" class="S"></path><path d="M200 284c6-1 11 0 17 1 2 0 4 0 7 1 6 1 11 2 16 4 3 0 5 1 7 2-1 1-2 1-3 1h-1c-3-1-5-2-8-3-2 0-3 0-5-1-2 0-4 0-6-1h-2-4l-4-1h-2c-2 0-4-1-6-2v-1h-6z" class="B"></path><path d="M178 288c7-2 14-4 22-4h6v1c2 1 4 2 6 2h2c-2 1-5 1-7 2h0c-7 0-13-1-20-1-2 1-5 1-6 2h-2l-1-2z" class="P"></path><path d="M254 289c-7-2-14-5-21-7l-19-3c6-1 16-1 22 0l18 6h-1c-2 0-3 0-5-1l1 1v1c0 1 1 1 2 1 1 1 2 1 3 2z" class="B"></path><path d="M247 292c9 5 19 9 29 14 5 3 9 7 14 10h-1c-1 0-4-2-4-2-4-3-8-7-12-7l-2 1c-1-1-3-2-4-2-2 0-3-1-5-1-1-1-2-2-3-2l-8-5h-1l-7-5h1c1 0 2 0 3-1z" class="C"></path><path d="M251 298l1-1 1 1c4 2 9 3 14 6l1 1c-3-1-6-3-9-2l-8-5z" class="P"></path><path d="M233 266h0 1c-1-1-2-2-3-2l-1-1c-1 0-2-1-2-2 1 0 2 1 4 1 1 1 0 1 1 1 2 0 3 1 3 2h1c2 0 4 1 5 2 1 2 9 4 12 5h0l5 1c3 2 6 3 9 3 0 1 1 2 2 2 2 2 6 2 8 3h1l9 5 9 6c6 7 15 13 21 21h0v1c1 1 1 2 1 2v1c-1 0-2 0-2-1-9-9-19-18-30-25-14-8-31-14-46-20l-5-3-3-2z" class="O"></path><path d="M254 289c-1-1-2-1-3-2-1 0-2 0-2-1v-1l-1-1c2 1 3 1 5 1h1c8 2 16 4 24 7 6 3 10 6 15 9 11 8 19 16 28 26l7 9 2 3 3 4c1 1 2 3 3 4 2 7 7 13 8 20v1c0 2 3 5 2 7-1 0-1 0-2-2l-2-4h-1c-2-1-5-7-6-9-4-7-8-14-13-20l-3-3c-4-6-9-11-14-15-4-3-7-7-11-9-13-10-26-18-40-24z" class="I"></path><path d="M328 336l2 3 3 4s-1 0-2 1c0-2-2-3-2-4-1-2-1-3-1-4z" class="G"></path><path d="M271 308l2-1c4 0 8 4 12 7 0 0 3 2 4 2h1c1 0 6 4 8 5l-4-8c4 2 7 6 11 9 5 4 10 9 14 15l3 3c5 6 9 13 13 20 1 2 4 8 6 9h1l2 4c1 2 1 2 2 2 1-2-2-5-2-7v-1c-1-7-6-13-8-20-1-1-2-3-3-4l-3-4c1 0 1 0 2-1v-1c4 5 5 13 9 19h0l2-1-1-1c1-1 1-2 1-3l3 3v-1h1c1 1 2 2 3 2l8 15c1 2 2 5 4 6l2 2v1c1 5 5 9 5 14 0 3 2 4 2 7l1 2-1 1c1 2 3 4 4 7 1 4 3 8 4 12l3 7v1c0 2 0 3 1 4l1 3h0-1-1c-1-1-1-2-2-3-2-2-3-4-4-6l-2 1v1 3 3l6 12c2 2 3 7 5 9 2 5 5 9 7 13h1l4 9c-1 2-2 2-4 2l-5-9-5-7-7-11c-1-2-5-7-6-8l-3-4c-8-13-20-24-31-34v-2-1-1-1c-1-1-2-1-2-3h-1l4 2h0c1-2 0-3-1-4s-3-3-4-5c1 0 2 0 3-1h0l5 4c-4-8-10-14-15-21-3-5-5-11-8-17-5-10-13-19-21-27-5-3-9-8-14-11l1-2c-2-1-3-2-4-4l-6-3 2-2c-1 0-3-1-4-1z" class="I"></path><path d="M275 309l17 13 5 5c1 0 1 1 1 2v1c0 1 2 2 2 2 2 2 4 5 6 7 1 1 1 2 2 3h0l-12-12v1c-5-3-9-8-14-11l1-2c-2-1-3-2-4-4l-6-3 2-2z" class="N"></path><path d="M337 376c1 1 2 2 2 4 1 2 3 4 4 6 2 2 3 5 5 8 2 2 3 4 4 6 0 2 1 3 1 4 3 4 4 9 6 13s5 8 6 13c-1-2-3-4-5-5v-3l-2-2c-1-3-3-5-4-8-3-5-5-11-7-16-1-4-4-6-6-9 0-2-1-3-2-4 0-1-1-1-1-2 0-2-1-3-1-5z" class="L"></path><path d="M354 399c2 4 4 9 7 13 1 3 3 6 5 10s4 10 8 14l6 12h-2c0-1 0-2-1-2h-1 0c-2-4-4-8-7-11l-4-5c-1-5-4-9-6-13s-3-9-6-13c0-2 0-4 1-5z" class="F"></path><defs><linearGradient id="AY" x1="366.373" y1="428.789" x2="352.125" y2="436.444" xlink:href="#B"><stop offset="0" stop-color="#e12729"></stop><stop offset="1" stop-color="#ef7063"></stop></linearGradient></defs><path fill="url(#AY)" d="M333 400l4 2c7 7 15 13 20 20l3 3c2 1 4 3 5 5l4 5c3 3 5 7 7 11h0 1c1 0 1 1 1 2h2c2 2 3 7 5 9 2 5 5 9 7 13h1l4 9c-1 2-2 2-4 2l-5-9-5-7-7-11c-1-2-5-7-6-8l-3-4c-8-13-20-24-31-34v-2-1-1-1c-1-1-2-1-2-3h-1z"></path><path d="M357 422l3 3c2 1 4 3 5 5l4 5h-2c-2-1-4-5-6-7-1-2-3-3-4-6z" class="K"></path><path d="M383 465v-1c1-3-1-5-2-9v-1h0 0c2 2 3 5 5 8l6 10v-2h1l4 9c-1 2-2 2-4 2l-5-9-5-7z" class="M"></path><path d="M271 308l2-1c4 0 8 4 12 7 0 0 3 2 4 2h1c1 0 6 4 8 5l-4-8c4 2 7 6 11 9 5 4 10 9 14 15l3 3c5 6 9 13 13 20 1 2 4 8 6 9l5 10 4 10 4 10c-1 1-1 3-1 5 0-1-1-2-1-4-1-2-2-4-4-6-2-3-3-6-5-8-1-2-3-4-4-6 0-2-1-3-2-4l-1-1c-3-2-4-4-5-7-2-4-5-8-8-11-1-1 0-1-1-3 0 0-1-1-1-2l-8-9c-1-2-3-3-4-5-4-4-7-8-12-11l-5-5-17-13c-1 0-3-1-4-1z" class="B"></path><path d="M292 322c2 1 3 1 5 3l2 2c1 0 1 1 2 2l1-1c2 1 3 3 4 4l3 3h0c2 0 3 1 4 2 1 2 1 3 2 5h0 0l-2-1h0c-1-1-1-1-2-1-1-1-2-1-2-2-4-4-7-8-12-11l-5-5z" class="L"></path><path d="M309 338c0 1 1 1 2 2 1 0 1 0 2 1h0l2 1h0c3 3 9 9 9 13h1c1 2 2 3 3 5 3 5 6 10 8 15-3-2-4-4-5-7-2-4-5-8-8-11-1-1 0-1-1-3 0 0-1-1-1-2l-8-9c-1-2-3-3-4-5z" class="C"></path><path d="M322 340c5 6 9 13 13 20 1 2 4 8 6 9l5 10 4 10c-3-2-4-5-6-8l-2-2c-1-3-3-5-4-8 0-1 0-1-1-2s-1-1-1-2c-1-2-1-4-2-5s0-2-1-4c-1 0-1 0-2-1h2c-2-3-5-8-8-11-1 0-1 0-1-1-1-2-2-3-2-5z" class="P"></path><path d="M332 337c4 5 5 13 9 19h0l2-1-1-1c1-1 1-2 1-3l3 3v-1h1c1 1 2 2 3 2l8 15c1 2 2 5 4 6l2 2v1c1 5 5 9 5 14 0 3 2 4 2 7l1 2-1 1c1 2 3 4 4 7 1 4 3 8 4 12l3 7v1c0 2 0 3 1 4l1 3h0-1-1c-1-1-1-2-2-3-2-2-3-4-4-6l-2 1v1 3 3c-4-4-6-10-8-14s-4-7-5-10c-3-4-5-9-7-13l-4-10-4-10-5-10h1l2 4c1 2 1 2 2 2 1-2-2-5-2-7v-1c-1-7-6-13-8-20-1-1-2-3-3-4l-3-4c1 0 1 0 2-1v-1z" class="K"></path><path d="M366 408c1-2-1-5-1-8 3 2 4 5 6 8v1l-2-2h-1c1 1 1 2 2 3h-1 0 0c-1-1-1-2-2-2h0 0c1 2 1 3 1 5l-1-1c0-1-1-3-1-4z" class="R"></path><path d="M368 413c0-2 0-3-1-5h0 0c1 0 1 1 2 2h0 0 1c-1-1-1-2-2-3h1l2 2v-1c1 1 2 2 4 2 1 4 3 8 4 12l3 7v1c0 2 0 3 1 4l1 3h0-1-1c-1-1-1-2-2-3-2-2-3-4-4-6l-6-10c0-2-2-3-2-5z" class="M"></path><path d="M371 408c1 1 2 2 4 2 1 4 3 8 4 12-4-3-5-9-8-13v-1z" class="D"></path><path d="M343 351l3 3v-1h1c1 1 2 2 3 2l8 15c1 2 2 5 4 6l2 2v1c1 5 5 9 5 14 0 3 2 4 2 7l1 2-1 1c-4-4-6-10-10-14-2-3-3-6-5-9l-7-14-6-11-1-1c1-1 1-2 1-3z" class="L"></path><path d="M343 351l3 3c0 2 1 4 2 6 0 2 1 3 1 5v1l-6-11-1-1c1-1 1-2 1-3z" class="F"></path><path d="M347 353c1 1 2 2 3 2l8 15c-1 2-1 4-1 6-4-6-5-14-9-20 0-1-1-2-1-3z" class="M"></path><path d="M358 370c1 2 2 5 4 6l2 2v1c1 5 5 9 5 14 0 3 2 4 2 7l-14-24c0-2 0-4 1-6z" class="b"></path><path d="M332 337c4 5 5 13 9 19h0l21 46 4 6c0 1 1 3 1 4l1 1c0 2 2 3 2 5l6 10-2 1v1 3 3c-4-4-6-10-8-14s-4-7-5-10c-3-4-5-9-7-13l-4-10-4-10-5-10h1l2 4c1 2 1 2 2 2 1-2-2-5-2-7v-1c-1-7-6-13-8-20-1-1-2-3-3-4l-3-4c1 0 1 0 2-1v-1z" class="P"></path><path d="M341 369h1l2 4c1 2 1 2 2 2 1-2-2-5-2-7v-1l8 19h0c-2-2-4-5-6-7l-5-10z" class="J"></path><path d="M346 379c2 2 4 5 6 7h0c2 2 4 6 5 9-1 0-1 1-1 1 0 2 2 3 1 4h-1c0 4 4 7 5 12-3-4-5-9-7-13l-4-10-4-10z" class="D"></path><path d="M357 395l4 8 1-1 4 6c0 1 1 3 1 4l1 1c0 2 2 3 2 5l6 10-2 1v1 3 3c-4-4-6-10-8-14s-4-7-5-10c-1-5-5-8-5-12h1c1-1-1-2-1-4 0 0 0-1 1-1z" class="J"></path><path d="M361 403l1-1 4 6c0 1 1 3 1 4l-1 2-5-11z" class="T"></path><path d="M367 412l1 1c0 2 2 3 2 5l6 10-2 1c-3-4-6-11-8-15l1-2z" class="P"></path><defs><linearGradient id="AZ" x1="796.072" y1="271.254" x2="791.044" y2="335.058" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#150000"></stop></linearGradient></defs><path fill="url(#AZ)" d="M720 221l1-1v1l1-1c1 4 2 7 4 10l9 15c0 1 1 2 2 4 2 4 3 7 7 9 3 3 9 6 13 6l12 3c4 1 8 1 13 0v1h-1l2 1 1 1 3-1c1-1 2-1 3-2 3-2 6-4 9-7v1c1 1 3 4 3 5 1 1 3 2 5 3s3 1 5 2v2l17 2c13 2 23 5 35 11 12 10 21 21 23 37v1 4h-1v-3c-2-11-7-19-16-26-3-2-6-4-10-5-6-4-14-7-21-8v1h-8v1l-14 2-1-1c-2 1-4 1-6 2h-2l1-2-1-1h-3-1c-1 1-5 2-5 2-28 9-53 26-75 46 0-2 0-3-1-4h0c-1-1-1 0-2 0v-1l-6 5v1l-6 6h-2 0c-1 2-3 3-4 4v1l-2 2v3l-1 1c-1 1-1 1-1 2 0 2-2 4-2 6-1 0-1 1-1 2l-2 4c-1 1-1 2-2 3v3c-1 1-1 0-1 1-2 5-14 21-13 25l1 1v1h0c-1 1-1 2-1 2l-1 2h0v2 2c-1 2-3 4-4 6-2 1-3 1-4 2h-2l-3 2c0 1-2 4-2 4l-13 20h-2l3-5c0-4 1-6 2-9 0-1 1-2 1-3h-1l-2 2h-1c-4 5-7 12-9 18-1-1-1-1-1-2h0l-1-1c2-14 9-27 9-41l-1 1h-2c2-2 4-5 4-7h-3l-2-1v-1h8c1 0 2 1 3 0l2-4c2-5 5-9 6-14l1-3h1 1c2-4 3-9 5-13 2-5 5-9 7-13l1 2 2-3 4-7c1-3 3-7 5-10l7-22c1-1 1-2 2-3v-2c1-1 1-2 2-3l1-4c1-1 1-2 1-3v-1c1 0 1-1 2-2 0-4 2-5 4-8 1-1 1-3 1-4 1-3 3-5 4-7 2-3 2-8 3-11-1-2-1-3 0-5h0l-1-2 2-15 1 2h0l1-8c1 1 2 2 2 3h1l1 3v-4c-1-4-2-8-3-13z"></path><path d="M748 308h1c-2 2-4 3-6 5v1c1 0 1-1 2 0-2 2-5 4-7 5l-4-1 3-2c4-3 7-6 11-8z" class="G"></path><path d="M781 268l2 1 1 1c-4 2-9 5-14 6-2-2-4 1-7 0h-1-2l6-2 6-3 9-3z" class="Z"></path><path d="M768 298c1-1 3-2 5-3 6-2 12-4 19-6 2 0 3-1 5-1-2 2-3 2-6 2-1 1-3 2-4 3-4 1-7 2-10 3-2 1-4 3-6 3h-1l-2-1z" class="P"></path><path d="M784 280c4-1 9-2 13-2 5 0 11-1 16 1-10 1-20 4-30 6 0-1 1-1 2-2h2v-1c0-1 1-1 1-2h-3-1z" class="B"></path><path d="M706 318c1-2 3-3 5-5v1l-1 1c-1 1-2 4-3 6-2 2-4 4-5 6v1h0c2-2 3-4 5-5l2-2h0l-14 17-2-2 5-8 8-10z" class="a"></path><path d="M768 298l2 1h1l-6 2c-2 1-4 2-5 4h-1c-4 3-10 6-14 9-1-1-1 0-2 0v-1c2-2 4-3 6-5h-1c3-3 6-4 10-5h0l10-5z" class="C"></path><path d="M768 298l2 1h1l-6 2c-2 1-4 2-5 4h-1c-2 0-4 1-5 2l-1-1 5-3h0 0l10-5z" class="L"></path><path d="M738 309h-1c-1 2-2 3-3 5l-3 3h0l1-1-1-1c-1 2-7 8-7 10h1v-1c2 0 3-2 4-3s0 0 1 0l2-2-1-1c2-1 3-2 6-2l-3 2-13 13-6 5v-1c1-4 5-8 7-11 2-2 4-4 5-7 3-3 7-6 11-8z" class="S"></path><path d="M799 287c6-1 10-1 16-2 3 0 6-1 9-1 5 0 10 1 15 2v1h-8v1l-14 2-1-1c-2 1-4 1-6 2h-2l1-2-1-1h-3-1c-2 0-3 0-5-1z" class="D"></path><path d="M805 288c3-1 7-2 10 0h3c-1 1-1 1-2 1-2 1-4 1-6 2h-2l1-2-1-1h-3z" class="b"></path><path d="M799 260v1c1 1 3 4 3 5 1 1 3 2 5 3s3 1 5 2v2l-4-1c-6 1-13 1-18 0 0-1-1-1-1-2v-1h-2c1-1 2-1 3-2 3-2 6-4 9-7z" class="a"></path><path d="M798 265c1 2 2 3 3 4l-2 2c-1-1-2-2-4-2h0l-1-1c1-2 2-2 4-3z" class="F"></path><path d="M799 261c1 1 3 4 3 5s-1 1 0 2l2 2-3-1c-1-1-2-2-3-4l1-4z" class="D"></path><path d="M799 260v1l-1 4c-2 1-3 1-4 3l1 1c-1 0-2 0-2-1l1-2-4 1c3-2 6-4 9-7z" class="B"></path><path d="M802 266c1 1 3 2 5 3s3 1 5 2v2l-4-1c-2 0-3-1-4-2l-2-2c-1-1 0-1 0-2z" class="J"></path><path d="M762 288l22-8h1 3c0 1-1 1-1 2v1h-2c-1 1-2 1-2 2-16 6-32 13-45 24-4 2-8 5-11 8l-1-1c0-1 1-2 2-3 4-3 8-6 13-9 2-3 5-5 8-7 4-3 10-4 14-8 1-1 2-1 4-2h0 0c-1 0-2 1-4 1h-1z" class="C"></path><path d="M760 276h2 1c3 1 5-2 7 0-7 3-15 6-23 10-10 5-19 11-27 18-3 3-7 6-9 9-2 2-4 3-5 5-1-1-1-2-1-3v-1c1-1 3-2 4-3l7-8c9-9 20-17 32-22 2-1 4-1 5-2 3-1 5-2 7-3z" class="r"></path><path d="M797 288l2-1c2 1 3 1 5 1-1 1-5 2-5 2-28 9-53 26-75 46 0-2 0-3-1-4h0c-1-1-1 0-2 0v-1l13-13 4 1c2-1 5-3 7-5 4-3 10-6 14-9h1c1-2 3-3 5-4l6-2c2 0 4-2 6-3 3-1 6-2 10-3 1-1 3-2 4-3 3 0 4 0 6-2z" class="J"></path><path d="M734 318l4 1-10 8-5 5h0c-1-1-1 0-2 0v-1l13-13z" class="C"></path><path d="M717 274l1-7c1 0 2 1 2 1 0 2-1 3 2 4l-1 3c0 1-1 4 0 5l2 1h0c8 1 16 0 24-2 1 0 3-1 5-2l1 2c-1 1-3 1-5 2-12 5-23 13-32 22l-7 8c-1 1-3 2-4 3 0-1 0-1 1-2v-1l2-4c1-1 1-2 1-3 1-1 1-1 1-2h-1c0-1 4-14 5-17l3-11z" class="N"></path><path d="M752 277l1 2c-1 1-3 1-5 2-2-1-6 1-8 1-4 1-12 3-15 1-1-1-1-1-2-1v-1c8 1 16 0 24-2 1 0 3-1 5-2z" class="M"></path><path d="M717 274l1-7c1 0 2 1 2 1 0 2-1 3 2 4l-1 3c0 1-1 4 0 5 0 5-2 8-5 12l-6 10h-1c0-1 4-14 5-17l3-11z" class="B"></path><path d="M717 274l1-7c1 0 2 1 2 1 0 2-1 3 2 4l-1 3h0c-1 1-1 2-2 3h-1l-1-4z" class="W"></path><path d="M719 232c1 1 2 2 2 3h1l1 3v4l-1 7c0 3-1 6-3 9l2 2c-1 2-2 4-3 7l-1 7-3 11c-1 3-5 16-5 17h1c0 1 0 1-1 2 0 1 0 2-1 3l-2 4v1c-1 1-1 1-1 2v1c0 1 0 2 1 3l-8 10-5 8-5 9-2-1-2 4c-1-3 0-5 0-8 1-3 3-7 5-10l7-22c1-1 1-2 2-3v-2c1-1 1-2 2-3l1-4c1-1 1-2 1-3v-1c1 0 1-1 2-2 0-4 2-5 4-8 1-1 1-3 1-4 1-3 3-5 4-7 2-3 2-8 3-11-1-2-1-3 0-5h0l-1-2 2-15 1 2h0l1-8z" class="G"></path><path d="M713 284v-2-2l3-8c0-3 1-8 2-9 1-2 1-3 1-5l2 2c-1 2-2 4-3 7l-1 7-3 11v-1h-1 0z" class="I"></path><path d="M719 232c1 1 2 2 2 3v3c1 3 0 6 0 9v1c-1 4-3 8-5 12-1-2-1-3 0-5h0l-1-2 2-15 1 2h0l1-8zm-6 52h0 1v1c-1 3-5 16-5 17h1c0 1 0 1-1 2 0 1 0 2-1 3l-2 4v1c-1 1-1 1-1 2v1c0 1 0 2 1 3l-8 10-5 8-5 9-2-1c3-4 4-8 8-12 1-1 1-2 2-4 0-4 3-8 5-12 2-2 3-5 4-8 3-8 5-16 8-24z" class="X"></path><path d="M698 328v-1c1-4 3-8 7-12 0 1 0 2 1 3l-8 10z" class="O"></path><path d="M720 221l1-1v1l1-1c1 4 2 7 4 10l9 15c0 1 1 2 2 4 2 4 3 7 7 9 3 3 9 6 13 6l12 3c4 1 8 1 13 0v1h-1l-9 3-6 3-6 2c-2 1-4 2-7 3l-1-2c-2 1-4 2-5 2-8 2-16 3-24 2h0l-2-1c-1-1 0-4 0-5l1-3c-3-1-2-2-2-4 0 0-1-1-2-1 1-3 2-5 3-7l-2-2c2-3 3-6 3-9l1-7v-4-4c-1-4-2-8-3-13z" class="D"></path><path d="M720 221l1-1v1l6 16v2h-2l-2-5c-1-4-2-8-3-13z" class="U"></path><path d="M723 234l2 5h2v-2l8 14h-2c-2-2-5-4-6-6h-1c-2-1-1-1-2-1 0-1-1-1-1-2v-4-4z" class="W"></path><path d="M723 234l2 5 2 6h-1c-2-1-1-1-2-1 0-1-1-1-1-2v-4-4z" class="D"></path><path d="M723 242c0 1 1 1 1 2 1 0 0 0 2 1h1c1 2 4 4 6 6h2c1 1 2 3 3 4l-2 1-5-2h-4c-3 1-4 3-6 6l-2-2c2-3 3-6 3-9l1-7z" class="I"></path><path d="M723 242c0 1 1 1 1 2v8-1c-2-1-1-1-2-2l1-7z" class="J"></path><path d="M722 249c1 1 0 1 2 2v1l-1 1h1c1 0 1-1 2-1h1v2h0c-3 1-4 3-6 6l-2-2c2-3 3-6 3-9zm5-4c1 2 4 4 6 6h2c1 1 2 3 3 4l-2 1-5-2h-3v-1c1 0 2 0 3-1l-3-3c-2-1-2-3-2-4h1z" class="N"></path><path d="M731 254l5 2c4 3 8 6 12 8 7 3 14 5 21 6v1c-2 2-8 2-10 1l-16-4c-5-1-12-4-16-2-2 1-4 3-5 5v1c-3-1-2-2-2-4 0 0-1-1-2-1 1-3 2-5 3-7 2-3 3-5 6-6h4z"></path><path d="M722 271c1-2 3-4 5-5 4-2 11 1 16 2l16 4c2 1 8 1 10-1v-1h1l2 1-6 3-6 2c-2 1-4 2-7 3l-1-2c-2 1-4 2-5 2-8 2-16 3-24 2h0l-2-1c-1-1 0-4 0-5l1-3v-1z" class="X"></path><path d="M752 277l8-3h6l-6 2c-2 1-4 2-7 3l-1-2z" class="N"></path><path d="M743 268l16 4c-1 1-3 1-4 1-1 1-2 1-3 1s-1 0-2-1h2l1-1-6-1h0l2 1v1c-2-1-4-1-6-2h-1 0l1-3zm-21 4v-1l1 1c0 2 0 6 2 8 4 0 7 0 11-1 3 0 8-2 11 0-8 2-16 3-24 2h0l-2-1c-1-1 0-4 0-5l1-3z" class="G"></path><path d="M762 288h1c2 0 3-1 4-1h0 0c-2 1-3 1-4 2-4 4-10 5-14 8-3 2-6 4-8 7-5 3-9 6-13 9-1 1-2 2-2 3l1 1c-1 3-3 5-5 7-2 3-6 7-7 11v1 1l-6 6h-2 0c-1 2-3 3-4 4v1l-2 2v3l-1 1c-1 1-1 1-1 2 0 2-2 4-2 6-1 0-1 1-1 2l-2 4c-1 1-1 2-2 3v3c-1 1-1 0-1 1-2 5-14 21-13 25l1 1v1h0c-1 1-1 2-1 2l-1 2h0v2 2c-1 2-3 4-4 6-2 1-3 1-4 2h-2l-3 2c0 1-2 4-2 4l-13 20h-2l3-5c0-4 1-6 2-9 0-1 1-2 1-3h-1l-2 2h-1c-4 5-7 12-9 18-1-1-1-1-1-2h0l-1-1c2-14 9-27 9-41l-1 1h-2c2-2 4-5 4-7h-3l-2-1v-1h8c1 0 2 1 3 0l2-4c2-5 5-9 6-14l1-3h1 1c2-4 3-9 5-13 2-5 5-9 7-13l1 2 2-3 4-7c0 3-1 5 0 8l2-4 2 1 5-9 2 2 14-17c14-15 33-28 53-33z" class="Y"></path><path d="M661 408c0 2-1 3-1 5-1 1-1 2-3 3v1l-2 3v1l2-3 1 1h0c0-1 0-1 1-1v-1c1-1 1-1 2-1h0c-1 1-2 4-3 5 0 1-2 2-2 2 0 2 1 2 0 3-2 1-3 3-4 4 0-1 1-2 1-3h-1l-2 2h-1c1-4 4-6 5-10l7-11z" class="M"></path><path d="M670 404h1l-1 1v4l-6 11c0 1-2 4-2 4-2 2-3 2-4 4 0 1-1 2-2 3v-1c1-1 2-2 2-4v-1c4-7 9-13 12-21z" class="T"></path><path d="M652 430c1-1 2-3 4-4 1-1 0-1 0-3 0 0 2-1 2-2v4h0v1c0 2-1 3-2 4v1c1-1 2-2 2-3 1-2 2-2 4-4l-13 20h-2l3-5c0-4 1-6 2-9z" class="B"></path><path d="M652 430c1-1 2-3 4-4 1-1 0-1 0-3 0 0 2-1 2-2v4c-3 4-5 10-8 14 0-4 1-6 2-9z" class="O"></path><path d="M658 397l9-13-1 6h0v2 1l1 1c0 2-6 13-6 14l-7 11c-1-2 0-3 1-5l1-1c1-2 2-3 3-5h-1l-1 1h-1c-1-3 0-6 1-8-1-1 0-3 1-4z" class="D"></path><path d="M666 393l1 1c0 2-6 13-6 14l-7 11c-1-2 0-3 1-5l1-1c1-2 2-3 3-5l7-15z" class="S"></path><path d="M693 336l2 2v1c-4 5-8 10-11 16-4 7-6 15-10 23l-7 16-1-1v-1c2-3 4-8 6-12l-2-2c0-1 3-10 4-11v-1c1-2 2-7 3-8v-3l3-8 4-7c0 3-1 5 0 8l2-4 2 1 5-9z" class="F"></path><path d="M684 348l2-4 2 1-7 14-1-1c0-4 2-7 4-10z" class="I"></path><path d="M680 347l4-7c0 3-1 5 0 8-2 3-4 6-4 10l1 1c-1 1-1 2-2 3v-1l-1 1v-1l-1 1-1-1c1-1 1-1 1-2v-1-3l3-8z" class="H"></path><path d="M672 380l-2-2c0-1 3-10 4-11v-1c1-2 2-7 3-8v1c0 1 0 1-1 2l1 1 1-1v1l1-1v1l-5 13c0 2-1 4-2 5z" class="I"></path><path d="M677 348l1 2 2-3-3 8v3c-1 1-2 6-3 8v1c-1 1-4 10-4 11l2 2c-2 4-4 9-6 12v-2h0l1-6-9 13h-1l2-4h-2l-1-2c2-5 5-9 6-14l1-3h1 1c2-4 3-9 5-13 2-5 5-9 7-13z" class="b"></path><path d="M659 393c3-4 5-8 7-12 4-8 6-19 11-26v3c-1 1-2 6-3 8v1c-1 1-4 10-4 11l2 2c-2 4-4 9-6 12v-2h0l1-6-9 13h-1l2-4z" class="C"></path><path d="M654 395l2-4 1 2h2l-2 4h1c-1 1-2 3-1 4-1 2-2 5-1 8h1l1-1h1c-1 2-2 3-3 5l-1 1c-1 2-2 3-1 5-1 4-4 6-5 10-4 5-7 12-9 18-1-1-1-1-1-2h0l-1-1c2-14 9-27 9-41l-1 1h-2c2-2 4-5 4-7h-3l-2-1v-1h8c1 0 2 1 3 0z" class="R"></path><path d="M654 395l2-4 1 2h2l-2 4c-1 0-2 3-3 3v-5z" class="M"></path><path d="M657 401c-1 2-2 5-1 8h1l1-1h1c-1 2-2 3-3 5l-1 1-4 2 1 1-3 3c1-2 0-3 1-5 0-1 1-2 1-3l1-4c1-1 1-1 1-2v-1c1-1 2-3 4-4z" class="Y"></path><path d="M726 316l1 1c-1 3-3 5-5 7-2 3-6 7-7 11v1 1l-6 6h-2 0c-1 2-3 3-4 4v1l-2 2v3l-1 1c-1 1-1 1-1 2 0 2-2 4-2 6-1 0-1 1-1 2l-2 4c-1 1-1 2-2 3v3c-1 1-1 0-1 1-2 5-14 21-13 25l1 1v1h0c-1 1-1 2-1 2l-1 2h0v2 2c-1 2-3 4-4 6-2 1-3 1-4 2h-2l-3 2 6-11v-4l1-1h-1c0-3 3-6 4-9 3-8 5-15 9-22 3-6 6-12 9-17l9-11c6-8 13-19 22-26l3-3z" class="P"></path><path d="M692 371v3c-1 1-1 0-1 1-2 5-14 21-13 25l1 1v1h0c-1 1-1 2-1 2l-1 2h0v2 2c-1 2-3 4-4 6-2 1-3 1-4 2h-2l-3 2 6-11c2-3 4-6 5-10l3-6c1-4 4-8 6-11s4-5 6-8l2-3z" class="I"></path><path d="M343 474c4 1 8 3 11 4 2 1 5 2 6 4 3 1 6 3 8 5 2 1 3 2 4 3l4 7h0c3 7 4 15 7 22 2 7 5 14 6 21h-1v1l102 333-2 1 6 20h1s1 1 2 1l10 26c1 4 3 10 7 11 1 1 2 0 2 0 4-5 7-12 9-17h0c2-5 4-11 6-16l9-30 46-145 20-60c2-8 4-17 7-24h1l3-7c0 1 0 1-1 2v1 1 1h-1v1 2h0c-1 1-1 1-1 2v1l-1 1v1 1l-2 5h0l-2 6v1s-1 1-1 2v1l-1 1v1h0v1 2l-10 32v1c0 2-1 4-1 6l-3 8v1c2-1 3 0 4 0v-1l2-4v-1-1c1-2 2-3 2-5 0-1 1-2 2-3l-14 43-77 235c-3-12-7-23-11-35l-25-73-66-199-38-115-13-40c-2-9-5-17-9-25-5-7-10-10-18-12 1-1 1-2 1-3l1-1-3-1c3 0 5 0 7 1l1-2h4z" class="J"></path><path d="M343 474c4 1 8 3 11 4l-1 1c1 6 4 13 6 19l-5-8c-4-6-8-11-16-14l1-2h4z" class="W"></path><path d="M331 475c3 0 5 0 7 1 8 3 12 8 16 14-2 1-3 1-4 2-5-7-10-10-18-12 1-1 1-2 1-3l1-1-3-1z" class="C"></path><path d="M589 744c-2 0-2 0-4-1v-3l7-24c2-5 3-11 5-16v1c0 2-1 4-1 6l-3 8v1c2-1 3 0 4 0v-1l2-4v-1-1c1-2 2-3 2-5 0-1 1-2 2-3l-14 43z" class="M"></path><path d="M495 895s1 1 2 1l10 26c1 4 3 10 7 11 1 1 2 0 2 0 4-5 7-12 9-17h0l-10 32c-3-5-5-10-7-15l-13-38z"></path><defs><linearGradient id="Aa" x1="469.293" y1="654.772" x2="385.742" y2="705.683" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#1d0001"></stop></linearGradient></defs><path fill="url(#Aa)" d="M360 482c3 1 6 3 8 5 2 1 3 2 4 3l4 7h0c3 7 4 15 7 22 2 7 5 14 6 21h-1v1l102 333-2 1-22-67-21-65-36-109-28-89-13-42c-2-6-4-12-6-17 0-2-2-3-2-4h0z"></path><path d="M368 487c2 1 3 2 4 3l4 7h0c3 7 4 15 7 22 2 7 5 14 6 21h-1v1l-13-39c-2-5-4-10-7-15z" class="c"></path><defs><linearGradient id="Ab" x1="513.495" y1="485.018" x2="604.239" y2="909.307" xlink:href="#B"><stop offset="0" stop-color="#8c0905"></stop><stop offset="1" stop-color="#e4090c"></stop></linearGradient></defs><path fill="url(#Ab)" d="M666 480l2 2c-4 6-6 14-8 20v1l-4 10-1 4v1l-1 2-2 6-20 61-3 3-8 26-5 17-3 8c-3 7-5 16-7 24l-20 60-46 145-9 30c-2 5-4 11-6 16h0c-2 5-5 12-9 17 0 0-1 1-2 0-4-1-6-7-7-11l-10-26c-1 0-2-1-2-1h-1l-6-20 2-1-102-333v-1h1c-1-7-4-14-6-21-3-7-4-15-7-22 1-1 1-1 2-1 1 2 2 2 2 4 2 1 4 5 6 7l1 1c2 2 3 4 4 6l5 7 9 14 24 50 2 6c1 0 2 0 2-1 1 1 1 2 1 3l1-1 2 4 1-2c0-3-2-5-3-8h1l1 1s0-1 1-2l1 1c2 5 5 13 9 16 0-3-2-6-2-9 1 2 2 4 3 5l9 19c1 0 1-1 2-2 1 3 2 5 3 8l3 7 2 4v-1c0-2 0-3 1-4l3 11 17 53c0 4 2 10 4 14 0 1 1 3 1 4l-1 1c1 4 2 7 3 11s2 9 4 14l4 15h-1c1 4 2 9 4 12l6 18v1c2 2 3 5 4 8l4 4c1 1 2 3 4 4l3 2c1 1 2 1 3 1 0-1 0-3-1-4-1-4-1-8 0-13 1-6 3-12 5-18v-3l-1-1 1-5 4-12s1-4 1-5c2-4 4-9 5-14l1-2 4-12v-3c1-1 1-2 1-2v-1-1l1-3 1-2v-2l1-1v-2l1-4 1-2 4-13 1-2 1-6 4-10c2-11 6-21 10-31 1-1 1-2 1-3h0v-2-1c1 0 1-1 1-1l1-1c1-2 0-4 1-6v-2c0-1 0-1 1-2s0-2 0-3h-1c-1-1 0-3-1-4 0-2 1-3 2-4 1-2 2-5 3-8l3-9 2-8 1 1v1h0c1-1 1-1 1-2 1-1 1-1 1-2l1-1c2-1 3-5 4-7 3-7 6-15 10-21l4-8c0-1 2-3 2-4l2-4 6-10c1 1 1 1 3 1h1l3-4 14-16 3-3c3-2 6-5 9-7 4-3 8-6 12-8z"></path><path d="M542 747v6c-1 1-1 3-1 4-1 2-2 3-2 4l-1 1v1c0 1 0 1-1 2h0v-2c1-2 1-2 1-3v-1l1-1h-1c-1-2 2-8 2-9h1c0-1 1-1 1-2z" class="D"></path><path d="M530 785c2 3 1 4 1 6 0 1 1 2 1 3 2 3 3 7 4 10-2-2-3-4-6-6-1-4-1-8 0-13z" class="I"></path><path d="M486 732c3 6 5 14 7 21l-1 4c-2-9-6-16-7-25h1z" class="L"></path><path d="M626 562h1l-1 1v1h1l1 1h1c1 1 1 1 1 3v1h-1v1h1c-1 1-1 1-2 1l-1 1h-4v-1h0l-1-1v-2h1c1-1 2-2 2-3s1-2 1-3z" class="G"></path><path d="M625 565c1 1 2 3 1 5 0 1 0 1-1 1h-2l-1-1v-2h1c1-1 2-2 2-3z" class="X"></path><path d="M490 874l7 22c-1 0-2-1-2-1h-1l-6-20 2-1z" class="E"></path><path d="M434 593l1-1 2 4c4 7 7 16 9 24l-1-1s0-1-1-2v-2c-1-2-2-4-2-5l-1-2v-2h-1c-1-1-1-2-2-3 0 1 0 1 1 2v2h0c1 1 1 1 1 2v1c0 1 0 1 1 2h0c0 1 1 3 1 4s0 1 1 2v1h0v1l1 1h0c-1 1-1 2 0 3-5-10-7-21-10-31z" class="F"></path><path d="M587 605h1l-9 27-4 6-1 1h-1l4-11 6-13c2-3 3-6 4-10z" class="P"></path><path d="M493 753c1 4 2 8 4 11l8 30c1 1 1 1 1 2v2h0s0-1-1-2l-1-2-12-37 1-4z" class="G"></path><path d="M597 611c-1 4-3 9-4 13-2 12-2 24-6 35 0-1-1-1-1-2h0v-3c0 1 1 2 1 2s0-1 1-2h0v-1-1c0-1 0 0 1-1v-1-2h0c0-1 1-2 1-2v-5c0-1 0-1 1-2v-3-3-1c1-1 1-2 1-3h-1-1v1 2c-1 1-1 3-2 5-1 0 0 1 0 2-1 1-1 1-1 2v2l-2 2 1-3v-1-2l1-2h0c0-1 1-2 1-2v-2s1-1 1-2l4-13h0l1-2c0-1 0-1 1-2 0-1 0-1 1-2v-1h1z" class="H"></path><path d="M655 491l2-2h1c-1 2-2 5-3 7-1 1-1 4-2 5l-3 10v-4l1-2v-2c0-1 1-1 1-2l-1 1v2c-1 0-1 0-1 1s-1 2-2 3v1c-1 1-2 2-3 4 0 1-1 2-1 3l-1-1c0-1-1-2-1-3s0 0 1-1c0 1 0 2 1 3 0-2 1-3 2-4 1-2 2-4 2-5l1-1v-3c-1 1-2 3-3 3-1 2-2 3-3 4h-1l13-17z" class="C"></path><path d="M538 758h1l-1 1v1c0 1 0 1-1 3v2c-1 2-1 3-1 4l1 1v1h-1l1 1c-1 2-1 4-2 7l-3 15c0-1-1-2-1-3 0-2 1-3-1-6 1-6 3-12 5-18v-3l-1-1 1-5 2 3 1-3z" class="Y"></path><path d="M535 758l2 3-2 6v-3l-1-1 1-5z" class="K"></path><path d="M567 668l2-3v2h0l-1 1v1h0c0 2-2 5-2 8v1h-1c0 1 0 2-1 3 0 1 0 2-1 3v1 1c-1 1-1 0-1 1v1c-1 1-1 1-1 2v1h-1l-1 2v1c-1 1-1 1-1 2v4c0 2 0 4-1 6-2 1-2 4-3 6 0 1 0 1-1 2v1c0 1-1 2-2 3v-4c1 0 0 0 1-1l-2 1 2-4h1c0-1 0-2 1-3 0-2 1-3 1-4s0-1 1-2c1-3 1-5 2-8 2-3 3-7 3-11 1-2 1-5 4-7v-1c0-3 1-4 2-6z" class="C"></path><path d="M433 590c1 1 1 2 1 3 3 10 5 21 10 31l9 42h0l-22-75c1 0 2 0 2-1z" class="S"></path><path d="M467 673l19 59h-1l-19-56c1-1 1-1 1-3z" class="B"></path><path d="M573 639h1l1-1 4-6c-2 4-5 9-4 12-1 1-2 3-3 4s-1 3-2 5c-2 5-3 9-3 15-1 2-2 3-2 6v1c-3 2-3 5-4 7 0 4-1 8-3 11-1 3-1 5-2 8-1 1-1 1-1 2s-1 2-1 4c-1 1-1 2-1 3h-1c1-8 4-16 6-24 5-16 9-32 15-47z" class="L"></path><path d="M437 596l1-2 12 30c1 4 2 9 4 12 1 4 3 8 4 12 3 8 5 17 9 25 0 2 0 2-1 3l-20-56c-2-8-5-17-9-24z" class="T"></path><path d="M437 587s0-1 1-2l1 1c2 5 5 13 9 16l2 4v2h-1v-1c0-1 0-1-1-2v1l-1 1 1 3c1 1 0 2 0 3 1 3 4 8 3 11l1 1c2 2 3 6 4 9 0 1 1 3 1 3h0l-2-2-1-1c0-2 0-3-1-4-1 0 0 1 0 2 1 1 1 2 1 4h0c-2-3-3-8-4-12l-12-30c0-3-2-5-3-8h1l1 1z" class="C"></path><path d="M437 587s0-1 1-2l1 1c2 5 5 13 9 16l2 4v2h-1v-1c0-1 0-1-1-2v1l-1 1 1 3c1 1 0 2 0 3l-11-26z" class="T"></path><path d="M605 562c1 1 2 2 2 3 1-1 1-1 1-2l1-1h1l-8 31h-2c-2 2-2 7-3 10-2 6-4 11-6 17v-2c0-1 0-1 1-2v-2c1-1 1-1 1-2l1-1v-1c0-1 1-2 1-2v-1-1l1-1v-2-1l1-1v-3c0-1 0-1 1-2v-1-1c0-2 1-1 0-2v1l-2 3-1 5c-1 3-3 6-4 9 0 1-1 2-1 3v1h-1l3-7 2-7 3-7-1-1c-1 1-1 1-2 1 1-2 2-5 4-7l4-11c0-2 1-4 2-5 1-3 1-5 1-8z" class="I"></path><path d="M602 575l1 1v4l-1 2c-1 1-1 3-3 4h-1l4-11z" class="D"></path><path d="M576 628h1l-4 11c-6 15-10 31-15 47-2 8-5 16-6 24l-2 4 2-1c-1 1 0 1-1 1v4c0 1-1 2-1 4v2c1 4-5 17-7 22l-1 1c0 1-1 1-1 2h-1c0 1-3 7-2 9l-1 3-2-3 4-12s1-4 1-5c2-4 4-9 5-14l1-2 4-12v-3c1-1 1-2 1-2v-1-1l1-3 1-2v-2l1-1v-2l1-4 1-2 4-13 1-2 1-6 4-10c2-11 6-21 10-31z" class="M"></path><path d="M550 714l2-1c-1 1 0 1-1 1v4c0 1-1 2-1 4v2c1 4-5 17-7 22l-1 1c0 1-1 1-1 2h-1l7-22 3-13z" class="Y"></path><path d="M386 507l1 1c2 2 3 4 4 6l5 7 9 14c-3-2-4-5-5-7s-2-3-3-5c-1 1 0 2 0 2v1 1h1v2l1 1 1 3v1c1 1 1 2 1 3 1 1 0 2 0 3v1l3 8c0 1 0 3 1 4l6 22 1 2 1 4 1 5c1 1 1 2 1 3l4 14 9 30 3 12c1 1 1 1 0 2 0-1-1-2-1-3l-5-19c-2-7-5-14-7-20-2-9-4-18-7-26l-3-10-1-5-3-8c0-1 0-2-1-3l-1-5c-3-10-6-19-10-28h-1c1 2 2 4 2 6v1-1l-1-1v-1c-1-2-1-1-1-2l-1-3c0-1-1-2-1-3l-2 1-1-1v-2l-1-1c0-2 0-4 1-6z" class="B"></path><path d="M386 507l1 1c0 3 1 6 2 8l-2 1-1-1v-2l-1-1c0-2 0-4 1-6z" class="T"></path><path d="M602 593c-2 6-3 12-5 18h-1v1c-1 1-1 1-1 2-1 1-1 1-1 2l-1 2h0l-4 13c0 1-1 2-1 2v2s-1 1-1 2h0l-1 2v2 1l-1 3c0 1 0 2-1 3v1c0 1-1 2-1 2v2s-1 1-1 2v1c0 1-1 2-1 2l-1 5s-1 1-1 2 0 2-1 2v2 1c0 1-1 2-1 2 0 1 0 2-1 3v1 2l-1 1v2c-1 1-1 2-1 3v1s-1 1-1 2v1c0 1-1 2-1 2 0 1 0 2-1 3v2l-1 2v1 2c-1 1-1 2-1 2l-1 2v2 1l-2 5v1 2c-1 1-1 2-1 2v1c0 1-1 2-1 2v2l-2 4v2l-1 3v1l-1 3-1 4-2 5c0 2 0 5-1 7l-1-1v-1l1-4 1-2v-2l3-9v-3l1-1v-3l4-12v-3l5-16 1-4v-3l8-28 4-13 2-6 1-4 1-2 1-6c1-1 1-2 1-3 1-2 1-3 2-5 2-6 4-11 6-17 1-3 1-8 3-10h2z" class="G"></path><path d="M454 636h0c0-2 0-3-1-4 0-1-1-2 0-2 1 1 1 2 1 4l1 1 2 2h0l45 137v1-1-1l-1-1c0-1-1-1-1-1v-2l-2-6h0l-1-3-2-5c0-2-1-4-1-5l-1-1v1l1 2v3l3 7v2h0c-2-3-3-7-4-11-2-7-4-15-7-21l-19-59c-4-8-6-17-9-25-1-4-3-8-4-12z" class="D"></path><path d="M376 497c1-1 1-1 2-1 1 2 2 2 2 4 2 1 4 5 6 7-1 2-1 4-1 6v1l1 1v1 2c1 1 1 0 1 2v2h1v2c0 1 0 1 1 2 0 1-1 2 0 3v1h1v2 1l1 1h0c-1 1 0 2 0 3v1l23 76 7 23c1 5 3 10 4 14h-1l-35-111c-1-7-4-14-6-21-3-7-4-15-7-22z" class="U"></path><path d="M380 500c2 1 4 5 6 7-1 2-1 4-1 6v1l1 1v1 2c1 1 1 0 1 2v2h1v2c0 1 0 1 1 2 0 1-1 2 0 3v1h1v2 1l1 1h0c-1 1 0 2 0 3v1c-2-5-3-11-5-17l-6-21z" class="B"></path><path d="M614 630c1 1 1 2 2 3l-3 8c-3 7-5 16-7 24l-20 60-46 145-9 30c-2 5-4 11-6 16h0l89-286z" class="U"></path><path d="M666 480l2 2c-4 6-6 14-8 20v1l-4 10-1 4v1l-1 2-2 6-20 61-3 3-8 26-5 17c-1-1-1-2-2-3l6-20 12-35 10-34c3-10 6-20 8-30l3-10c1-1 1-4 2-5 1-2 2-5 3-7h-1l-2 2-1-3c4-3 8-6 12-8z" class="W"></path><path d="M655 501c1-2 2-7 4-8 1 3-1 5-2 8l1 2h2l-4 10-1 4v1l-1 2-2 6c0-1 0-2-1-3s0-3 0-4l-1 1c0 1 0 3-1 4l-1 1c2-8 4-16 7-24z" class="c"></path><path d="M654 520l-2 2-1-2c1-2 1-2 3-4l1 2-1 2z" class="P"></path><path d="M654 516c0-2 0-4 1-5 0 1 0 1 1 2l-1 4v1l-1-2z" class="F"></path><path d="M657 501l1 2h2l-4 10c-1-1-1-1-1-2l1-4c1-2 1-4 1-6z" class="B"></path><defs><linearGradient id="Ac" x1="658.544" y1="484.012" x2="664.89" y2="500.319" xlink:href="#B"><stop offset="0" stop-color="#130000"></stop><stop offset="1" stop-color="#310606"></stop></linearGradient></defs><path fill="url(#Ac)" d="M666 480l2 2c-4 6-6 14-8 20v1h-2l-1-2c1-3 3-5 2-8-2 1-3 6-4 8 0-1 0 0-1-1 0 1 0 0-1 1h0c1-1 1-4 2-5 1-2 2-5 3-7h-1l-2 2-1-3c4-3 8-6 12-8z"></path><path d="M648 525l1-1c1-1 1-3 1-4l1-1c0 1-1 3 0 4s1 2 1 3l-20 61-3 3 19-65z" class="G"></path><path d="M654 488l1 3-13 17c-3 4-4 9-5 14l-7 20c-1 5-3 11-4 16v3 1c0 1-1 2-1 3s-1 2-2 3h-1v2h-1c0-6-1-12 0-19 2-6 3-13 5-19 1-3 1-7 3-9l-1-1-4 5c-1 0-3 5-4 5l-1-1-2 2s-1 1-1 2c-1 1-1 2-2 3h-1-2 0v-3c0-1 2-3 2-4l2-4 6-10c1 1 1 1 3 1h1l3-4 14-16 3-3c3-2 6-5 9-7z" class="U"></path><path d="M642 498l3-3c-2 6-6 11-9 17-1 2-1 5-1 8h-1 0c-1 1-2 2-2 3 0-2 0-3 1-5h1v-1l-1-1-3 3c1 1 1 1 1 2h-1c0 1-1 1-1 2h0l-1-1c1-4 4-6 6-9l-1-1c-2 1-3 3-4 5h-1l1-1-1-2 14-16z" class="B"></path><path d="M628 514l1 2-1 1h1c1-2 2-4 4-5l1 1c-2 3-5 5-6 9l-4 5c-1 0-3 5-4 5l-1-1-2 2s-1 1-1 2c-1 1-1 2-2 3h-1-2 0v-3c0-1 2-3 2-4l2-4 6-10c1 1 1 1 3 1h1l3-4z" class="F"></path><path d="M617 533c2-3 3-7 5-8l2 2c-1 0-3 5-4 5l-1-1-2 2z" class="G"></path><path d="M613 531l2-1c1-1 2-1 3-2-2 4-4 7-5 10h-2 0v-3c0-1 2-3 2-4z" class="N"></path><path d="M621 517c1 1 1 1 3 1h1l-7 10c-1 1-2 1-3 2l-2 1 2-4 6-10z" class="J"></path><path d="M629 523h0c0-1 1-1 1-2h1c0-1 0-1-1-2l3-3 1 1v1h-1c-1 2-1 3-1 5 0-1 1-2 2-3h0l-6 25c0 3-1 6-2 9v4 3 1c0 1-1 2-1 3s-1 2-2 3h-1v2h-1c0-6-1-12 0-19 2-6 3-13 5-19 1-3 1-7 3-9z" class="F"></path><path d="M616 535c-1 1-1 2-1 3l2 1-3 10-1 4c-1 2-2 8-3 9h-1l-1 1c0 1 0 1-1 2 0-1-1-2-2-3 0 3 0 5-1 8-1 1-2 3-2 5l-4 11c-2 2-3 5-4 7-2 3-3 6-4 9-1 1-1 3-2 3h-1c-1 4-2 7-4 10l-6 13h-1c1-1 1-2 1-3h0v-2-1c1 0 1-1 1-1l1-1c1-2 0-4 1-6v-2c0-1 0-1 1-2s0-2 0-3h-1c-1-1 0-3-1-4 0-2 1-3 2-4 1-2 2-5 3-8l3-9 2-8 1 1v1h0c1-1 1-1 1-2 1-1 1-1 1-2l1-1c2-1 3-5 4-7 3-7 6-15 10-21l4-8v3h0 2 1c1-1 1-2 2-3z" class="L"></path><path d="M611 535v3h0c0 3-3 7-4 10v-1l-1 1v-1c0-1 1-3 1-4l4-8z" class="D"></path><path d="M605 562c2-3 3-7 5-10h2l1 1c-1 2-2 8-3 9h-1l-1 1c0 1 0 1-1 2 0-1-1-2-2-3z" class="G"></path><path d="M581 614c2-5 4-11 7-16 1-1 2-3 3-4 0 4-2 7-4 11-1 4-2 7-4 10l-6 13h-1c1-1 1-2 1-3 1-1 1-2 2-3l2-8z" class="D"></path><path d="M607 543c0 1-1 3-1 4v1l1-1v1c-2 4-5 8-7 12l-9 22c-3 9-7 17-9 26 0 2-1 4-1 6l-2 8c-1 1-1 2-2 3h0v-2-1c1 0 1-1 1-1l1-1c1-2 0-4 1-6v-2c0-1 0-1 1-2s0-2 0-3h-1c-1-1 0-3-1-4 0-2 1-3 2-4 1-2 2-5 3-8l3-9 2-8 1 1v1h0c1-1 1-1 1-2 1-1 1-1 1-2l1-1c2-1 3-5 4-7 3-7 6-15 10-21z" class="X"></path><path d="M628 522l1 1c-2 2-2 6-3 9-2 6-3 13-5 19-1 7 0 13 0 19l-19 61c0 1 0 2-1 3v2l-2 4v-1-1h0l-1-1h1v-3c1-1 2-4 1-4 0-2 0-3 1-5l-1-3-1 3v2l-1 2v2 1c-1 1-1 2-2 3-2 5-3 11-5 17l-5 16c-1 2-2 5-3 8h0l4-17c4-11 4-23 6-35 1-4 3-9 4-13 2-6 3-12 5-18l8-31c1-1 2-7 3-9l1-4 3-10-2-1c0-1 0-2 1-3 0-1 1-2 1-2l2-2 1 1c1 0 3-5 4-5l4-5z"></path><path d="M617 533l2-2 1 1-3 7-2-1c0-1 0-2 1-3 0-1 1-2 1-2z" class="H"></path><path d="M596 635c2-9 5-18 8-27l8-27c2-6 4-12 7-18-2 10-5 20-7 30l-6 22-5 15 1 1c0 1 0 2-1 3v2l-2 4v-1-1h0l-1-1h1v-3c1-1 2-4 1-4 0-2 0-3 1-5l-1-3-1 3v2l-1 2v2 1c-1 1-1 2-2 3z" class="G"></path><path d="M601 630v-2c-1 0 0-2 0-2 1-3 2-6 3-10h0v1c1 0 0-1 0-1 1-1 1-1 1-2l1 1-5 15z" class="H"></path><path d="M446 593c1 2 2 4 3 5l9 19c1 0 1-1 2-2l3 8 3 7 2 4v-1c0-2 0-3 1-4l3 11 17 53c0 4 2 10 4 14 0 1 1 3 1 4l-1 1c1 4 2 7 3 11s2 9 4 14l4 15h-1c1 4 2 9 4 12l6 18c0 3 1 5 2 8h1c1 2 3 4 4 6h-1c0-1 0-1-1-1-1-1-1-1-2-1s-1 1-1 1v2c-2 0-3-3-5-4l-3-6c-1-5-3-9-5-13l-45-137s-1-2-1-3c-1-3-2-7-4-9l-1-1c1-3-2-8-3-11 0-1 1-2 0-3l-1-3 1-1v-1c1 1 1 1 1 2v1h1v-2l-2-4c0-3-2-6-2-9z" class="G"></path><path d="M446 593c1 2 2 4 3 5l9 19c1 0 1-1 2-2l3 8 3 7 2 4v-1c0-2 0-3 1-4l3 11 17 53c0 4 2 10 4 14 0 1 1 3 1 4l-1 1c1 4 2 7 3 11s2 9 4 14l4 15h-1l-36-113c-4-11-10-23-17-33l-2-4c0-3-2-6-2-9z" class="R"></path><path d="M463 623l3 7h-2l-3-6 2-1z" class="G"></path><path d="M460 615l3 8-2 1-3-7c1 0 1-1 2-2z" class="c"></path><path d="M466 630l2 4v1c1 1 1 3 2 5h-1l-5-10h2z" class="C"></path><path d="M469 629l3 11c-1 0-1 1-1 1l-1 1-1-2h1c-1-2-1-4-2-5v-1-1c0-2 0-3 1-4z" class="J"></path></svg>