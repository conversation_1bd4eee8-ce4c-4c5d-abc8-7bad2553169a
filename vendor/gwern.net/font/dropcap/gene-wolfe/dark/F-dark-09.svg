<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="234 61 572 876"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#282726}.C{fill:#333334}.D{fill:#2b2a29}.E{fill:#3f3d3c}.F{fill:#191918}.G{fill:#232120}.H{fill:#ccc8c1}.I{fill:#565452}.J{fill:#7d7a77}.K{fill:#bebbb8}.L{fill:#62615f}.M{fill:#393837}.N{fill:#c3bcb3}.O{fill:#141413}.P{fill:#433c37}.Q{fill:#4f4e4c}.R{fill:#494849}.S{fill:#aaa299}.T{fill:#beb7ad}.U{fill:#5f5e5c}.V{fill:#11100f}.W{fill:#a09e9a}.X{fill:#97938d}.Y{fill:#908c88}.Z{fill:#6f6c6a}.a{fill:#d7d1ca}.b{fill:#c8c3b9}.c{fill:#b2a899}.d{fill:#040404}.e{fill:#848281}.f{fill:#a74f1f}.g{fill:#838281}.h{fill:#1f1c18}.i{fill:#ab4413}.j{fill:#6c584a}.k{fill:#f0e5d3}.l{fill:#b5a08d}.m{fill:#453930}.n{fill:#8a3e17}.o{fill:#af410c}.p{fill:#f3eee7}.q{fill:#703113}.r{fill:#d56d30}.s{fill:#78675b}.t{fill:#533e2f}.u{fill:#c75e22}.v{fill:#69635a}.w{fill:#9f5d35}.x{fill:#e6d8c0}.y{fill:#4b2e1d}.z{fill:#d25716}.AA{fill:#f7e7c9}.AB{fill:#a4907e}.AC{fill:#b05021}.AD{fill:#9c4719}.AE{fill:#897464}.AF{fill:#6f3316}.AG{fill:#edbc82}.AH{fill:#e9853d}.AI{fill:#c29e80}.AJ{fill:#edc79b}.AK{fill:#a68164}.AL{fill:#684b3b}.AM{fill:#f26f15}.AN{fill:#eb630c}.AO{fill:#f4781c}.AP{fill:#d1510e}.AQ{fill:#5e351f}.AR{fill:#f5d8ac}.AS{fill:#f7b15f}.AT{fill:#f8dfb3}.AU{fill:#ed8a3d}.AV{fill:#45220d}</style><path d="M673 425c-1 1-1 2-1 4-2-1-3-1-3-2 1-2 2-2 4-2z" class="N"></path><path d="M372 365l6-3c0 2 0 4-1 5l-1-1c-2 0-3 0-4-1z" class="g"></path><path d="M261 126l11 1c-1 0-2 1-3 1h1s1 0 2 1l-9-1-2-2z" class="O"></path><path d="M419 615h5c-1 1-3 2-4 3h-1c-2 0-3 0-5-1 0-1 0-1 1-2l1 1 3-1z" class="a"></path><path d="M574 321c2 1 5 1 7 1 0 2 0 3-1 4l-2-2h-3-1c-1-1-2-1-3-1 1-2 2-1 3-2z" class="D"></path><path d="M421 739l2 1c0 1 0 2-1 3v1c1 0 0 1 1 2v1h0c-2 0-2 1-4 0l1-7 1-1z" class="H"></path><path d="M417 318h5c0 1-1 2-1 3h0l-2 1h-3-1c0 1 0 1-1 2v-4c1-1 2-2 3-2z" class="N"></path><path d="M367 452c2 1 3 1 5 2 1 3 2 4 5 5h0c-1 1 0 1-2 1l-3-3c-1 0-3 0-4 1l-1-1v-5z" class="L"></path><path d="M574 280h1v-8h1v8l2 2c0 1 0 1-1 1-2 0-5 0-7-1l-1-1c1-1 2-1 3-1h0v-1l1-1c0 1 0 1 1 2z" class="T"></path><path d="M629 453c0 2 0 4-1 6-2 0-6-1-8-1-1 1-3 2-4 2 1 0 1-1 2-2h1l1-1h1 0c3-2 5-4 8-4z" class="R"></path><path d="M621 370c-3-2-3-5-3-8l8 4v4h-1c-2-1-3 0-4 0z" class="L"></path><path d="M375 453l6 3c-1 1-1 2-3 3h0-1 0c-3-1-4-2-5-5v-1h3z" class="Q"></path><path d="M715 322c1-1 1-3 1-4 2 2 1 13 1 16h0v1 12l-1 1v-3c-1-1-1-2-1-3v-20z" class="H"></path><path d="M419 789v3l2-2c1 1 0 4 0 6 0 1-1 2-1 3-1 1-1 2-1 3s-1 2-2 3h0l-2-2c0-3 4-8 4-10h-1-1l2-4z" class="T"></path><path d="M349 143l3-1h2c0 3-1 7-3 9l-3 2c-2 0-1-1-2-3l-2-2 2-2 1 1h1 0v-2l1-2z" class="Y"></path><path d="M411 815l-1-1c1-3 3-3 4-6h0 1l-1-1c0-2 0-2 1-3l1 1c1 2 1 3 1 4h0c1-1 1-2 1-3l1 1h0v1 3l-1 2-4 4c-1 0-2-1-3-2z" class="K"></path><path d="M272 127l29 3c-2 1-2 1-4 1h-1l-2 1h-1l-21-3c-1-1-2-1-2-1h-1c1 0 2-1 3-1z" class="B"></path><path d="M419 786c2-11 3-22 3-33l1-1c1 2 1 3 1 5 0 8 0 16-2 24-1 2 0 5-1 6h-2v-1z" class="K"></path><path d="M625 199c1 0 1-1 2-1l2-2c0 1 0 1 1 2v1l1 1c0 1 0 1 1 2h0v-3-2l3 10 1 1h1v1h-3v-2h-1l-1 1h-1l-2-2-1 2c0-1 1-3 0-4v-2l-2-2-1-1z" class="L"></path><path d="M578 350h2v1h0 2c3 1 8 1 12 1 2 0 4 1 7 1v2c-2 0-17-1-19-2h-1c-1 0-2 1-3 1l-1 1v-1c-1 0-2-1-3-1s-2 0-3 1h-1l-2-2h-1l7-1c1 1 2 1 3 1l1-2z" class="E"></path><path d="M424 337c0-1 0 0-1-1l-1 1h-2c0 1-1 2-2 3h-6-1v-1h2c0-1 0-1 2-1 0-1 2-2 2-3s0 0 1-1h1 2c1 0 2 1 4 0h3 3l1 2h3c1 1 1 2 1 4-1 0-1 0-2-1l-2 1h0l-1-1v-1h-1l-1-1h-2v2h-1c-1 0-1-1-2-2z" class="V"></path><path d="M683 232c1-1 2-1 3-1h0c2 1 5-1 7 1l1 1c0-1 0-1 1-1h6c0 1-2 2-2 2v2l-1-1-1 8c-5-4-9-8-14-11zm-78 617l2-2c1 0 1 0 2 1 0 1 0 1 1 2 1 2 1 1 2 2h2c0 1 1 1 1 2 1 2 2 2 4 3v1l-3 3c-1 0-3-2-4-3 0 1-1 1-1 1l-2-2h0c-1-1-1-2-3-2-1-2 0-1-2-1 0 0-1-1-1-2s1-2 2-3z" class="S"></path><path d="M612 858v-1h1c2 0 3 1 5 1h1l-3 3c-1 0-3-2-4-3z" class="T"></path><path d="M601 204h5v-1l-1-1v-1c2 1 2 3 5 3h1c-1-1-1-2-2-2s-2-1-2-2v-1l1-1c1 2 3 4 4 6 2 1 3 1 5 1h1 0c1-1 2-1 3-1s1-1 2-1h1v-1c1-1 1-2 1-3l1 1 2 2v2c1 1 0 3 0 4h0c-1 0-2 0-3-1l-1-1v1c-2 1-5 0-7 0l-16-1v-2z" class="j"></path><path d="M390 177c1-2 1-2 3-3 1-1 3-2 4-3h2 1c1 2 3 3 4 5l-8 7c-1 0-1 0-2-1l1-2-1-1-1 2-2-1c-1-1-1-1-1-3z" class="S"></path><path d="M717 334v44h0v-13c-2 8-2 14-2 22h0c0-6 0-13-1-19v-20l1-6c0 1 0 2 1 3v3l1-1v-12-1z" class="a"></path><path d="M420 280c0-3 1-11-1-13 0-1 1-1 2-2h1c1 1 2 2 2 3v1h5c0 2 0 3-1 4-1 2 0 4-1 6 0 1 0 0 1 1h-4-4z" class="F"></path><path d="M651 456l9-9c0-1 2-2 2-2v-2l1-1 4 4v1h0l-1 2-1-1-1 1 1 2h1c0 2 0 2-1 3l-2 1v2h-2l1-1c-1 0-2 0-2-1-2 1-2 2-3 3l-1-1 1-2-1 1-1-1-1-1-1 1c-1 1-1 1-2 1z" class="W"></path><path d="M657 455c1 0 1-1 2-1h0 0v-1c-1-1-1-2-1-3 2-1 2-2 5-2 0 1 1 2 2 3h0 1c0 2 0 2-1 3l-2 1v2h-2l1-1c-1 0-2 0-2-1-2 1-2 2-3 3l-1-1 1-2z" class="S"></path><path d="M715 322v20l-1 6c-1 1 0 0 0 1-1 2-1 3-2 4v-7c-1 1-1 2-2 4l-1 6-2 6-2 5-1 2-2 4c0 1-1 1-1 2l-1 1c-1 1-2 2-2 3h-1c2-5 5-8 7-13 0-1 0-2 1-3l3-7 7-34z" class="I"></path><path d="M360 499v1c1 0 1 1 2 1h2l1-1v1h1c0-1 0-3 1-4v1c0 1 0 1 1 2v2c1-1 2-1 3-1l1 1c1 0 1 0 2-1h3 0c1 1 1 1 1 3l-10-1h-6v2h-1v-1h-8-3-1c-1-1-1-2-2-3v-2c1-2 2-1 4-1h1v1c1-1 1-1 2-1h1 2v2h3v-1z" class="a"></path><path d="M350 504l1-3 1-1h0c1 1 0 0 1 0h1 1c0 2-1 2-2 4h-3z" class="k"></path><path d="M424 757c1 1 0 2 0 3 0 3-1 11 1 13v3l1 1v1 3h1-1l-1-2h0v4h1 2 4v1h-5l-1 1v7c-1 1-1 1-1 2-2 1-2 2-3 3-1 2-2 3-2 5h-1 0c0-1 0-2 1-3 0-1 1-2 1-3 0-2 1-5 0-6l-2 2v-3-3 1h2c1-1 0-4 1-6 2-8 2-16 2-24z" class="X"></path><defs><linearGradient id="A" x1="414.759" y1="367.379" x2="398.712" y2="360.692" xlink:href="#B"><stop offset="0" stop-color="#272728"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M413 357h1v1 2h2 3c1 0 1 0 1-1l1 1v3c-1 0-2 1-2 1 0 1 1 2 0 3-7-1-15 1-21 2 0-1 0 0-1-1-1-2-3-1-5-2l21-3v-6z"></path><path d="M569 768v-11-4l3 1c1 4 0 10 0 15 0 15 3 28 9 42v1c-2-4-4-8-6-13-2-6-4-12-4-18l-2-13zm-147-59v3 2c1 1 0 2 1 3v2h1v4 3 1h0c-1 3-1 7-1 10-1 1-2 0-3 0-2-2-1-10-1-14-1 0-2-1-3-2 0-2 0-2 1-4h3 0v-1c0-3 0-5 2-7z" class="K"></path><path d="M419 723c-1 0-2-1-3-2 0-2 0-2 1-4h3l1 1 1-1v1c0 1 0 2-1 3h0c0 2 0 3-1 5h0c0-1 0-2-1-3z" class="H"></path><path d="M577 345h1c5 1 10 1 15 1h4c1 0 1 1 1 1 1 2 3 4 3 6-3 0-5-1-7-1-4 0-9 0-12-1h-2 0v-1h-2c-1-2-1-4-1-5z" class="L"></path><path d="M578 350v-3l8 1-1 1h0c-1 0-2 1-3 1h-2-2z" class="V"></path><path d="M586 348c3 0 6-1 8 0 1 0 1 1 2 1v2h-1 0c-4-1-9 0-13-1 1 0 2-1 3-1h0l1-1z" class="F"></path><path d="M428 189h2l1-1h1v1c1 1 1 2 1 3l-1 1c-1 0-1-1-2-1 0 0-2 1-2 2h0c1 1 1 1 2 1v1c-2 0-3-1-5 0h-9c3 4 1 7 3 11h-1c0 2 0 5-1 6 0-2-3-4-3-6h1 1c0-1 0-2-1-3 0 1-1 1-1 2-3-3-6-7-7-9l1-1c-1-1-1-2-1-4h2c3 2 6 2 9 3 2 0 3-1 4-2 1 0 2-1 3-1 2-1 3-2 3-3z" class="L"></path><path d="M616 864c1-2 2-4 4-6 1 0 2 1 2 2h1c1 0 1 1 3 2l1 1v1c-2 3-3 5-4 7-1 1-2 2-2 3h0v2c-1-1-2-1-2-1-2 0-2 0-3 1h0 1v1l-2-1-5-1v-2c1-1 0-2 1-3 0-2 3-5 5-6z" class="b"></path><path d="M623 871c0-1-1-1-1-1 0-2 1-3 1-4v-1c1-1 2-1 4-1-2 3-3 5-4 7z" class="c"></path><path d="M616 864h0c1 2 2 2 3 3v1c0 1 1 2 1 3h0l-2-1v3c-1 1-1 1-2 1s-1 1-1 2l-5-1v-2c1-1 0-2 1-3 0-2 3-5 5-6z" class="W"></path><path d="M334 506h-1c1-2 8-1 10-1h11c2 0 4 0 6 1h0 0c0 2 1 2 1 4v1c0 1 1 2 1 4-2-1-2-2-3-3l-23-1h0-1v-4l-1-1z" class="C"></path><path d="M334 506h-1c1-2 8-1 10-1h11c2 0 4 0 6 1v2c-3 1-8-1-11 0-1-1-1-1-2-1s-2-1-3-1h-1c-1 1-2 1-2 3-1 0-4 0-5-1 1 0 2 0 3-1h1v-1h-6z" class="I"></path><defs><linearGradient id="C" x1="393.894" y1="362.069" x2="367.362" y2="375.963" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#858484"></stop></linearGradient></defs><path fill="url(#C)" d="M370 370l3-1c-2 0-2 0-3-2 0-1 1-1 2-2 1 1 2 1 4 1l1 1c0 1 0 2 1 3 4-1 9-2 14-4 2 1 4 0 5 2 1 1 1 0 1 1l-13 4c-3 1-6 2-8 3-3 2-6 5-7 8-1-1-1-2-1-4 0-1 1-4 1-6l1-2c0-1 0-1-1-2z"></path><path d="M349 504l-32-1c-5 0-11 1-16 0 5-1 10-1 14-2 2-1 3-3 6-4 1 0 2 0 4 1-1 1-1 1-1 2v1l1-2v-1h9c-1 1-2 1-3 1l1 2 1-1h1c1-1 1-1 1-2h4 1 2v1c0-1 1-1 1-1l1 1-1 1c3 0 1-1 3-2l1 1v2c1 1 1 2 2 3z" class="H"></path><path d="M500 853h1 2c1 1 3 0 5 0h1 1v1 47 1l-1-1v-31h-1v31 1c-2-2-1-9-1-11l-1 2-1-33h0c0-2 0-3-1-4h-1c-2 1-1 3-2 4v-3h-1v-1c1-2 1-2 0-3z" class="C"></path><path d="M500 853h1 2c1 1 3 0 5 0h1 1v1c-1 0-3 0-5 1-1 0-3 0-5 1 1-2 1-2 0-3z" class="V"></path><path d="M599 843l3-3v1 4c1 1 1 1 2 1l-1-1 1-1c1 1 2 1 2 3-1 0-1 1-1 2-1 1-2 2-2 3s1 2 1 2c2 0 1-1 2 1 2 0 2 1 3 2h0l-1 1 3 3h0l-2 2c-2 0-2-1-3-2l-1 1h-1 0c-2-1-3-1-4-2s-2-2-3-2l-1-1c-1-1-2-2-4-3l-2-2c0-1 1-1 2-2l3-4 1-1c1-1 2-2 3-2z" class="e"></path><path d="M603 852c0 1 1 2 1 2 2 0 1-1 2 1h0v1h-1c-1 1-1 2-1 3l-1 1v-1-2l-1 2h-2v-2l1-1h0-1-1c1-1 3-2 3-4h1z" class="Y"></path><path d="M599 856l-2 1-2-2-1-2c1-1 2-2 3-2h1v-1l1-1c1 1 1 2 2 3h1c0 2-2 3-3 4z" class="J"></path><defs><linearGradient id="D" x1="366.144" y1="453.746" x2="359.891" y2="431.887" xlink:href="#B"><stop offset="0" stop-color="#5e5e5e"></stop><stop offset="1" stop-color="#787778"></stop></linearGradient></defs><path fill="url(#D)" d="M355 430c2 0 2 1 4 1v1h0-2c0 1 0 2 1 2 0 1 1 1 2 2h0l1-1h3c4 2 7 5 10 7v2c0 1 2 2 1 2l-1 1-3 1c1 2 2 3 4 5h-3v1c-2-1-3-1-5-2l-12-10v-12z"></path><defs><linearGradient id="E" x1="372.129" y1="446.56" x2="365.612" y2="436.428" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#E)" d="M364 435c4 2 7 5 10 7v2c0 1 2 2 1 2l-1 1-3 1h0c-2-1-3-2-4-3-3-1-5-3-7-5-1-1-1-2 0-4h0l1-1h3z"></path><path d="M360 436c2 0 4 1 5 2 1 2 1 5 2 7-3-1-5-3-7-5-1-1-1-2 0-4h0z" class="d"></path><defs><linearGradient id="F" x1="495.68" y1="876.472" x2="509.883" y2="881.862" xlink:href="#B"><stop offset="0" stop-color="#a7a8a6"></stop><stop offset="1" stop-color="#d3cfcf"></stop></linearGradient></defs><path fill="url(#F)" d="M500 857h1v3c1-1 0-3 2-4h1c1 1 1 2 1 4h0l1 33v8c-1 0-2 0-3 1l-1-1-1 1-1-1v-44z"></path><path d="M362 494c-3-1-6 0-9 0h-20l5-3c0-1 1-3 2-4 3-4 15-1 20-2 6-2 9-9 16-9l-1 1a19.81 19.81 0 0 0-11 11c-1 2-1 4-2 7v-1z" class="K"></path><path d="M353 487h2c0 2 0 2-1 3l1 1c1-1 1-1 1-2s1-1 2-2l-1 3v2c-2 1-5 1-8 1l2-2c1 0 1 1 2 0l-1-2 1-2z" class="a"></path><path d="M419 207c-2-4 0-7-3-11h9c2-1 3 0 5 0 2 1 3 2 4 3-1 0-1 1-1 1v1h1l1-1c0 2-1 6-2 8-5 3-9 5-15 6l-1-1c1-1 1-4 1-6h1z" class="Y"></path><path d="M419 207c1-2 1-5 1-7l4 5c1 1 3 1 4 1h1v1h-1c-1 1-3 1-4 2v1l-1 1h-2v-3c-1 0-2 0-3-1h1z" class="S"></path><path d="M433 200v1h1l1-1c0 2-1 6-2 8-5 3-9 5-15 6l-1-1c1-1 1-4 1-6 1 1 2 1 3 1v3h2l1-1v-1c1-1 3-1 4-2h1v-1c0-1 1-2 2-3 0 0 0-1 1-2l1-1z" class="X"></path><path d="M621 370c1 0 2-1 4 0h1l13 9v15 1h-2-1l-2 2h-1-4 0c0-2 0-3 1-5 0-1 0-2-1-3v-1c0-1 1-2 1-3v-1c0-1-1-1-2-3s-2-4-2-6h-2c-1 0-1 0-2-1l-2-2v-2h1z" class="g"></path><path d="M621 370c1 0 2-1 4 0-1 1-2 1-3 2h-2c0-1 0-1 1-2z" class="J"></path><path d="M626 375l1-1c3 1 7 4 9 6-3 1-5 1-8 1-1-2-2-4-2-6z" class="d"></path><path d="M636 380v1c1 3 1 7 1 10-1 1-3 1-5 1l-1-1c-1-2 0-5 0-7 0-1 4-1 4-2s0-1 1-2z" class="O"></path><path d="M436 183c2 0 3-3 6-4l1 1c1 1 2 2 2 3l1 3h0v3 1l1 1c1 2 1 2 0 4s1 4-1 7h0v1h0-2l-4-2h0-3v2h-1v-2l-1-1-1 1h-1v-1s0-1 1-1c-1-1-2-2-4-3v-1c-1 0-1 0-2-1h0c0-1 2-2 2-2 1 0 1 1 2 1l1-1c0-1 0-2-1-3v-1h-1c2-2 3-4 5-5z" class="U"></path><path d="M446 194c-1-1-2-2-3-4 0-1 0-1 1-2 1 0 2 1 2 1v1 4z" class="L"></path><path d="M446 186c-1 0-2 0-2-1-2-2-2-3-1-5 1 1 2 2 2 3l1 3h0z" class="I"></path><path d="M446 190l1 1c1 2 1 2 0 4s1 4-1 7c-2-1-2-1-3-2 1-1 1-2 2-3h1v-3-4z" class="E"></path><path d="M361 505h1v1c0 1 1 1 1 2v1 2l3 7c1 4 4 8 8 10 0 1 0 2 2 3l1 1h-1c-3-1-5-3-7-5-2-1-5-5-8-5-6-1-14 0-20-1-1-1-2-2-2-3-1-2-3-4-5-6 3 0 6 1 9 1h16v-1c1 1 1 2 3 3 0-2-1-3-1-4v-1c0-2-1-2-1-4h0l1-1z" class="D"></path><path d="M590 187c2 1 2 2 3 4l3 3c2 0 3 1 3 3 0 1 1 1 2 2l1 1v1c-1 1-2 1-2 2l1 1v2l-6-1h-12l-10-1c2-1 4-3 6-6 1-1 3-3 4-5l7-6z" class="L"></path><path d="M583 193c3 1 5 3 7 4v1c1 1 2 1 2 2s1 1 1 2h0-1l-3-1v1l1 1c2 0 3 1 5 2h-12l-10-1c2-1 4-3 6-6 1-1 3-3 4-5z" class="Q"></path><path d="M579 198c1 1 4 3 5 4s1 1 2 1v1h-3v1h0l-10-1c2-1 4-3 6-6z" class="P"></path><path d="M629 397h4 1l2-2h1 2v-1 30c-1-2-1-4-1-7l-1-1v11c-1 1-2 1-3 1v1 1 1h-2c-1 2 1 1-2 2-1-7 0-16 0-23 0-5 0-9-1-13z" class="J"></path><path d="M632 401h1c2 0 3 0 4 1v10c-2 0-3 0-5-1v-10zm2 27c-1 0-2 0-2-1 0-2-1-10 1-12 2 0 3 0 4 1v11c-1 1-2 1-3 1z" class="d"></path><path d="M362 505v-2h6l10 1v1l-1 1v-1c-1 1-1 3-1 4l1 1v3c0 1 0 1 1 2v2c0 1 0 1 1 2v1c0 1 1 2 2 3l-1 1h0l-1 1c1 2 2 3 4 4l1 1h-4l-1-1c-2 0-4-1-5-1-4-2-7-6-8-10l-3-7v-2-1c0-1-1-1-1-2v-1z" class="G"></path><path d="M368 503l10 1v1l-1 1v-1l-1-1h-2c-3 3 0 8-1 11-2-2 0-6-3-9-1-1-1-1-2-3z" class="E"></path><path d="M314 134c6 0 12 1 17 1 3 1 5 1 8 1 5 0 11 1 16 2v3l-1 1h-2l-3 1-1 2v2h0-1l-1-1-2 2c-1-1-2-1-3-2-2 0-3 0-4 2l-1 1c-2-1-2-2-3-3-2-1-4 1-6 1-1-2-1-3-1-5-1-1-2-1-4-1-1 0-5-1-7-1-1-2-1-2-1-4v-2z" class="G"></path><path d="M349 143v-1c1-2 2-3 4-4 0 2 0 2 2 3l-1 1h-2l-3 1z" class="B"></path><path d="M314 136h1l1 1h4c2 0 5 1 7 1l-1 1c-1 1-1 1-3 1-1 0 0 0-1 1-1 0-5-1-7-1-1-2-1-2-1-4z" class="P"></path><path d="M327 138h6c1-1 3 0 5 0h1c1 1 2 2 3 2h3l1 1c1 1 1 4 2 4v2h0-1l-1-1-2 2c-1-1-2-1-3-2v-1c-1-2-1-3-2-4-1 0-1 1-2 1h-1l1-1-2-1c-1 0-1 2-2 3l-1-3h0c-2 0-4 0-6-1l1-1z" class="B"></path><path d="M346 141c1 1 1 4 2 4v2h0-1l-1-1-2 2c-1-1-2-1-3-2v-1c0-2 0-3 1-4h1 1c1 1 1 0 2 0z" class="J"></path><path d="M326 139c2 1 4 1 6 1h0l1 3c1-1 1-3 2-3l2 1-1 1h1c1 0 1-1 2-1 1 1 1 2 2 4v1c-2 0-3 0-4 2l-1 1c-2-1-2-2-3-3-2-1-4 1-6 1-1-2-1-3-1-5-1-1-2-1-4-1 1-1 0-1 1-1 2 0 2 0 3-1z" class="S"></path><path d="M600 180c1 0 2 2 3 3 3 3 5 7 8 10h0c1 1 0 1 1 2h1 0l-1-2c1 0 1 0 2-1 0 1 0 1 1 1h1l-1-1c1-1 1-1 3-2l1 2c0 1-1 2-2 4l2 3v1c-1-1-1-1-2-1v2c1 1 1 2 1 4h-1c-2 0-3 0-5-1-1-2-3-4-4-6l-1 1v1c0 1 1 2 2 2s1 1 2 2h-1c-3 0-3-2-5-3v1l1 1v1h-5l-1-1c0-1 1-1 2-2v-1l-1-1c-1-1-2-1-2-2 0-2-1-3-3-3l-3-3c-1-2-1-3-3-4l1-1 1-1 2-1 2-2 2-2h2z" class="s"></path><path d="M591 186l1-1c2 2 4 3 6 5 1 1 2 3 3 5 2 1 3 1 4 2 0 1-1 2-2 2h-1c1-1 1-1 2-1-1-1-1-1-2-1-3-2-9-8-11-11z" class="B"></path><path d="M600 180c1 0 2 2 3 3 3 3 5 7 8 10h0l-4 4c-2-2-4-5-6-7-3-1-4-4-7-6h0l2-2 2-2h2z" class="M"></path><path d="M598 180l7 8c1 2 3 3 4 6h-1c-2 0-2 0-3-2-2-2-4-5-6-7 0-1-1-3-3-3l2-2z" class="L"></path><path d="M356 138h5l26 3c-1 1-3 1-4 1l-1 1 1 1-6-1-3-1-3 8-4 8h-1-1c-3 1-5-2-6-4-1 0-2 1-2 2h-1c-1 0-2 0-3-1v-2-1l-2-1c2-2 3-6 3-9l1-1v-3h1z" class="V"></path><path d="M360 143h3 2l1-1c1 1 1 3 1 4v1l-1 1c-1 0-2-1-3-1-1-2-1-2-1-4-1 1 0 1-1 1l-1-1z" class="L"></path><path d="M353 153c1-3 2-6 4-8 1-2 2-2 3-2l1 1c-1 3-1 5-1 7 1 1 2 1 4 1 0 1 0 1-1 2h1c2 0 2 0 3 1l-1 3h-1c-3 1-5-2-6-4-1 0-2 1-2 2h-1c-1 0-2 0-3-1v-2z" class="e"></path><path d="M356 138h5l26 3c-1 1-3 1-4 1l-1 1 1 1-6-1-3-1-3 8-4 8h-1l1-3c-1-1-1-1-3-1 2-3 4-6 4-9v-2l2-1h0l-1-1h-6l-1-1c-1 0-1 0-2-1-1 0-2-1-4-1z" class="B"></path><path d="M368 145c1 0 1 0 1 1 1-1 1-2 2-3h1c0 2-1 4-2 5l-3 7c-1-1-1-1-3-1 2-3 4-6 4-9z" class="Z"></path><path d="M684 415l12 7 3 1c-1 3-6 12-9 14v1l-1 1-15-8-2-2c0-2 0-3 1-4l7-9c1-1 2-1 4-1z" class="B"></path><path d="M680 416c0 2-1 2-1 4-1 0-1 1-1 2h2c1-1 1-2 1-3h1l2 1c-1 1-2 1-2 3s0 3 2 4c1 1 3 2 4 3 2-1 2-2 3-3h0c0 2 0 2-1 4l1 1v1c-1 1-1 2-1 5l-1 1-15-8-2-2c0-2 0-3 1-4l7-9z" class="b"></path><path d="M678 423s1 0 1 1c1 1 1 1 1 2l-2 1-2-1v-1l2-2z" class="a"></path><path d="M430 317l7 1c2 0 4 1 5 2h0v8c-1 2-1 4 0 6v6 4c0-1-1-3-1-3l-1-1v-2c0-1 0-5-1-5v2l-1 9h0c0-1 0-3-1-4h-1c0-2 0-3-1-4h-3l-1-2h-3-3c-2 1-3 0-4 0h-2-1v-9h-4v-1c1-1 1-1 1-2h1 3l2-1h0c0-1 1-2 1-3 3 0 6 0 8-1z" class="M"></path><path d="M414 324c1-1 1-1 1-2h1 3 0c2 1 8 0 8 2v1 2l-1 1h0v1 4c-1-2 0-4-1-5-2 0-2 0-3 1v-1l-1-1c-2 1-1 4-2 6h0v1h-1v-9h-4v-1z" class="G"></path><path d="M421 321h18v1 1 4c-1 1 0 1-1 2h-1-2c-1-1 0-2 0-3l-1-1c-1 0-2 1-2 2l-1-1-1 1c-1 0 0 0-1 1-1 0-2 0-3 1v-1h0l1-1v-2-1c0-2-6-1-8-2h0l2-1z" class="C"></path><path d="M429 328v-2l1-2h0c2-1 2-2 4-2l1 1h2v1l1-1v-1h0l1 1v4c-1 1 0 1-1 2h-1-2c-1-1 0-2 0-3l-1-1c-1 0-2 1-2 2l-1-1-1 1c-1 0 0 0-1 1z" class="h"></path><path d="M430 317l7 1c2 0 4 1 5 2h0v8c-1 2-1 4 0 6v6 4c0-1-1-3-1-3l-1-1v-2c0-1 0-5-1-5v2-8-4-1-1h-18 0c0-1 1-2 1-3 3 0 6 0 8-1z" class="H"></path><path d="M695 394c2 0 2-1 4 0l1 1h0c2 2 4 4 5 6h0v2c1-1 0-1 2-1l-1 4c-1 2-2 5-3 7h-1v1c0 2-1 5-2 7l-1-1c-1-1 0-1-1-1-1 1-1 2-2 3l-12-7-6-3 5-3c2-1 2-2 3-4-1-1-2-1-2-3l1-1 2 1 3-3 5-5z" class="N"></path><path d="M698 412v-1l-2-2c1 0 2 1 3 1 0-1-1-1-2-2h0l1-1 2 2 1-1c1 1 1 1 2 3 0 0 0 1-1 2v1l-3-1-1-1z" class="W"></path><path d="M705 403c1-1 0-1 2-1l-1 4c-1 2-2 5-3 7h-1c1-1 1-2 1-2-1-2-1-2-2-3l-3-2h1c1 0 2-1 3-1l2 1h0l1-3z" class="C"></path><path d="M690 399c5 0 8 3 12 6-1 0-2 1-3 1h-1c-2-1-5-3-7-4-1-1-1-1-1-3z" class="Q"></path><path d="M695 394c2 0 2-1 4 0l1 1h0c2 2 4 4 5 6h0v2l-1 3h0l-2-1c-4-3-7-6-12-6h0l5-5z" class="K"></path><path d="M695 394c2 0 2-1 4 0l1 1h0c-2 2-1 3-2 5 0-1-1-1-2-2l-1-4z" class="k"></path><path d="M700 395c2 2 4 4 5 6h0c-3 0-4 0-7-1 1-2 0-3 2-5z" class="H"></path><path d="M686 405c3 3 8 5 12 7l1 1 3 1c0 2-1 5-2 7l-1-1c-1-1 0-1-1-1h0c-1-1-2-2-2-3-5-2-8-5-13-7 2-1 2-2 3-4z" class="B"></path><path d="M699 413l3 1c0 2-1 5-2 7l-1-1c-1-1 0-1-1-1h0l1-1c0-1-1-2-1-2-1-1-2-1-3-2v-1h4z" class="G"></path><path d="M683 409c5 2 8 5 13 7 0 1 1 2 2 3h0c-1 1-1 2-2 3l-12-7-6-3 5-3z" class="K"></path><path d="M623 165c1 1 2 2 2 4 1 3 3 6 4 9 1 2 1 4 2 6h2l3 11 2 2h1 1c0 1 1 2 1 2 2-1 2-1 2-2 1 0 1 0 2-1-2 0-2 0-3 1h-1l2-2h-3c-1 1-1 0-2 0h0c1-1 3-1 5-2h3v1h2v2h1v-3h1v3h1v-3h3v2h1v-2h1c0 2 1 4 1 7-1 0-2 1-3 1h-1v-1c1-1 2-1 2-2h-2v-1h2v-1h-2l-2 1h1v4l1 2 1-1c1 0 1-1 2-1l1 1h0c-1 1-3 1-4 2v2h1v-2h3v2c-1 1-2 1-3 2v3h2c1 0 1 0 2 1 0 2 1 4 0 6-2-1-3-2-5-4 0-2 0-4-1-6 0-3-1-6-1-9h-1c0 5 2 9 2 15l-5-2c0-1 0-1-1-1v1l-1-1v-1-2-2c-2 2 0 4-2 5v-1h-3v1l-1-1c-1-1-2-3-2-4l-6-15-1 1c-2-3-2-8-4-11s-4-5-5-9c0-1-1-2-1-4h0l3-2v-1z" class="E"></path><path d="M631 184h2l3 11 2 2h1 1c0 1 1 2 1 2h2c-1 1-2 1-3 0l-1 1c0 1 0 2 1 3 2 0 3 0 5-1v1c-1 0-2 1-3 1l1 1h1c-1 1-1 1-1 2l-1-1v-1l-1-1-1 1c1 2 2 3 3 5h-3l-9-26z" class="h"></path><path d="M623 165c1 1 2 2 2 4 1 3 3 6 4 9 1 2 1 4 2 6l9 26v1l-1-1c-1-1-2-3-2-4l-6-15-1 1c-2-3-2-8-4-11s-4-5-5-9c0-1-1-2-1-4h0l3-2v-1z" class="v"></path><path d="M620 168h0l3-2c0 2 1 4 1 6 2 4 3 8 4 11 1 2 3 6 3 8l-1 1c-2-3-2-8-4-11s-4-5-5-9c0-1-1-2-1-4z" class="G"></path><path d="M575 856h1 2c2-1 3-1 4-1h1 1c1 0 3-3 4-4l1 1-2 1 1 1 2-2 2 2c2 1 3 2 4 3l1 1c1 0 2 1 3 2s2 1 4 2h0 1l1-1c1 1 1 2 3 2l2-2h0l-3-3 1-1 2 2s1 0 1-1c1 1 3 3 4 3-2 3-4 7-7 10l-1 1 2 1v2h-3-1l-10-3c-2-1-4-2-6-2-4-4-10-6-14-9-2 0-4 0-5-2h3c1 0 1-1 1-2v-1z" class="J"></path><path d="M596 872l2-2h3l-3-1 1-1 3 1h1l-3-2 1-1 4 2h1c1 0 2 1 2 2l1 1-1 1 2 1v2h-3-1l-10-3z" class="e"></path><path d="M608 872l2 1v2h-3l1-3z" class="X"></path><path d="M605 862l1-1c1 1 1 2 3 2l2-2h0l-3-3 1-1 2 2s1 0 1-1c1 1 3 3 4 3-2 3-4 7-7 10l-1-1c0-1-1-2-2-2l-1-1c-2-1-3-1-5-2 0-1-1-1-1-2l-3-1 1-1c2 1 3 3 5 3 2 1 4 2 5 3h1l-1-1h2v-1c-1-1-3-2-4-3z" class="W"></path><defs><linearGradient id="G" x1="697.168" y1="364.096" x2="719.604" y2="384.654" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#G)" d="M697 379h1c0-1 1-2 2-3l1-1c0-1 1-1 1-2l2-4 1-2 2-5 2-6 1-6c1-2 1-3 2-4v7c1-1 1-2 2-4 0-1-1 0 0-1v20c0 5-1 8-2 12 0 7-1 15-5 22h0c-2 0-1 0-2 1v-2h0c-1-2-3-4-5-6h0c1-4-5-10-5-14l2-2z"></path><path d="M712 353c1-1 1-2 2-4 0-1-1 0 0-1v20c0 5-1 8-2 12-2-9-1-19 0-27z" class="Z"></path><path d="M620 168c0 2 1 3 1 4 1 4 3 6 5 9s2 8 4 11c1 1 3 4 2 5v2 3h0c-1-1-1-1-1-2l-1-1v-1c-1-1-1-1-1-2l-2 2c-1 0-1 1-2 1 0 1 0 2-1 3v1h-1c-1 0-1 1-2 1s-2 0-3 1h0c0-2 0-3-1-4v-2c1 0 1 0 2 1v-1l-2-3c1-2 2-3 2-4l-1-2c-2 1-2 1-3 2l1 1h-1c-1 0-1 0-1-1-1 1-1 1-2 1l1 2h0-1c-1-1 0-1-1-2h0c-3-3-5-7-8-10-1-1-2-3-3-3 1-2 3-2 4-3 1-2 1-2 3-2 2 1 3 4 4 6l1-1-1-1c0-2-1-3-2-4v-2h1c1 1 1 1 2 3l1-1-1-3 1-1c3 1 6 11 8 14 1 2 2 4 3 5h1c-3-5-7-12-8-17v-1-2l3-2z" class="I"></path><path d="M613 175v1c3 3 6 7 8 11l-1 1c-1-1-2-1-2-2v-3c-1 0-2-2-3-2-1-2-2-4-3-5l1-1z" class="E"></path><path d="M619 192l2 2c0 1 1 4 1 5l-1-1s0-1-1-1v3 3h-1c-1-2-1-2 0-3v-1l-2-3c1-2 2-3 2-4z" class="j"></path><path d="M611 181l1-1-1-1c0-2-1-3-2-4v-2h1c1 1 1 1 2 3 1 1 2 3 3 5 1 0 2 2 3 2v3l-1-2-1 1 2 3h0v1h-1v-1l-2-2c-1-2-4-3-4-5z" class="Q"></path><path d="M674 431l15 8c1 2-3 7-4 9-7 10-16 20-26 27h-1l7-6c2-2 7-5 8-8-1 1-4 3-5 3-1-2-2-5-1-8-1 0-3-1-4-1l2-1c1-1 1-1 1-3h-1l-1-2 1-1 1 1 1-2h0v-1l-4-4c1 0 1-1 2-1l-2-2 1-1c2-2 3-2 5-2 1-1 2-3 3-5h2 0z" class="D"></path><path d="M677 448l3-3h0l1-1-2-1h-1l-1-1 1-1 7 5-4 3c-1 0-2 0-4-1z" class="K"></path><path d="M669 436c1-1 2-3 3-5h2v2h-1 0c4 2 5 4 7 6h2c1 1 1 2 1 4-1 0-5-2-5-2-1 0-1 0-1-1-3-1-5-2-8-4z" class="Z"></path><path d="M665 441l-2-2 1-1c2-2 3-2 5-2 3 2 5 3 8 4 0 1 0 1 1 1h0l-1 1 1 1h1l2 1-1 1h0l-3 3c-2-1-5-3-7-4s-3-3-5-3z" class="T"></path><path d="M667 446l9 5c1 1 2 1 3 2l-3 4h-1l-2 4c-1 1-4 3-5 3-1-2-2-5-1-8-1 0-3-1-4-1l2-1c1-1 1-1 1-3h-1l-1-2 1-1 1 1 1-2h0v-1z" class="S"></path><path d="M665 451l-1-2 1-1 1 1c1 1 1 2 1 4 1 1 1 1 1 3h-1c-1 0-3-1-4-1l2-1c1-1 1-1 1-3h-1z" class="N"></path><path d="M668 456c1 1 2 1 3 2l2-1-1-2c1 1 2 1 3 2l-2 4c-1 1-4 3-5 3-1-2-2-5-1-8h1z" class="V"></path><path d="M441 247l1 1c1 1 1 3 2 4h0c0 1-1 2-1 4h0c3 1 5 1 7 2h2l-1 2h1v2l-1 1-3-1v-1c-1-1-3 0-5-1h-1l-1 2v6 2c0 2 0 3-1 5h-4l1 2-1 1-1-1c-1 2-2 3-3 3h-4c-1-1-1 0-1-1 1-2 0-4 1-6 1-1 1-2 1-4h0l-1-1 1-2c1 1 1 1 2 1v-2c-2-1-5-1-7-1-3-1-5-1-7 0-1 1-1 0-1 1l-1-1c-1-1-1-2-2-3h0c0-2 0-2 1-3s0-1 1-1c1-1 2-3 3-4h2 11 1c1-1 2-1 3-1 2-1 3-1 4-1s1-1 2-1l-1-2 1-1z" class="G"></path><path d="M429 269h2 0v2l2 2-1 1v-1h-3c-1 1-1 2-1 4l1 1c1 0 1 0 1-1 1-1 2-1 3-2l1 1 1 1c-1 2-2 3-3 3h-4c-1-1-1 0-1-1 1-2 0-4 1-6 1-1 1-2 1-4h0z" class="O"></path><path d="M434 276l-1-13c1-1 2-1 3-1v6h2 0l-1 2h0l-1 5 1 2-1 1-1-1-1-1z" class="I"></path><path d="M436 262l2-2h0 2c1 1 1 1 1 2v6 2c0 2 0 3-1 5h-4l1-5h0l1-2h0-2v-6z" class="h"></path><path d="M437 270h0 1v3 1c-1-1-1-3-1-4z" class="O"></path><path d="M441 247l1 1c1 1 1 3 2 4h0c0 1-1 2-1 4h0c3 1 5 1 7 2h2l-1 2h1v2l-1 1-3-1v-1c-1-1-3 0-5-1h-1c-2 0-4 0-6-1l-23 2h0c0-2 0-2 1-3s0-1 1-1c1-1 2-3 3-4h2 11 1c1-1 2-1 3-1 2-1 3-1 4-1s1-1 2-1l-1-2 1-1z" class="H"></path><path d="M450 258h2l-1 2h1v2l-1 1-3-1v-1c-1-1-3 0-5-1h-1c-2 0-4 0-6-1 4 0 10 1 14-1z" class="P"></path><path d="M441 247l1 1c1 1 1 3 2 4h0c0 1-1 2-1 4h0c-9 0-20-1-29 2 1-1 0-1 1-1 1-1 2-3 3-4h2 11 1c1-1 2-1 3-1 2-1 3-1 4-1s1-1 2-1l-1-2 1-1z" class="I"></path><path d="M398 369c6-1 14-3 21-2h0 1 0v1l-1 1v3 5 1l-3 1h-1 0-2l-3 3-1-1-1 1c-1 1-2 1-4 1-9 0-19-1-28 1-2 1-4 1-6 2v-2c1-3 4-6 7-8 2-1 5-2 8-3l13-4z" class="d"></path><path d="M555 299c1-1 2-3 3-3 2-2 5-2 7-2h13c-1 1-2 2-2 3 1 2 0 6 0 8v2c1 1 1 2 1 4v2 2c1 2 1 3 3 4 1 1 1 1 1 3h0c-2 0-5 0-7-1h-16-2l-1-1c-1 2-1 3-3 5v-10c0-1 0-1 1-1h2v-7-8z" class="B"></path><path d="M572 314v2h-3v-1l1-1c-1 0-2-1-3-1v2s-1 0-2 1v-1c0-2 0-4 1-5h1v1l1 1 1-1c1 2 1 2 3 3zm-9-16h1 1 3v3 3c-1 1-1 2-2 3h-1v-2c-1-2-1-3-2-5v-2z" class="V"></path><path d="M568 301c2 0 3 1 4 1 2 0 3 2 4 3v2c1 1 1 2 1 4-2-1-3-2-4-3v3l-1 1v2c-2-1-2-1-3-3l-1 1-1-1v-1l1-1-1-1 1-2v-2-3z" class="F"></path><path d="M568 301c2 0 3 1 4 1 2 0 3 2 4 3v2c1 1 1 2 1 4-2-1-3-2-4-3l1-4h0v-1l-1 1-1 1c-1 1-2 1-4 1v-2-3z" class="O"></path><path d="M563 298v2 11c0 2 0 4-1 6 1 0 1 0 1 1h-5l1-3h1v-1l-1-1c0-1 1-2 1-3h-1c-1-1 0-2 0-3l-1-1v-1h1v1h1v-1c0-2-1-3 0-4 1-2 1-2 3-3z" class="I"></path><path d="M563 318c6-1 12-1 17 1 1 1 1 1 1 3h0c-2 0-5 0-7-1h-16-2l-1-1c1 0 2-1 3-2h5z" class="c"></path><path d="M555 307c0-1 1-2 1-3 1-1 1-2 1-3l1 2v2 1l1 1c0 1-1 2 0 3h1c0 1-1 2-1 3l1 1v1h-1l-1 3c-1 1-2 2-3 2-1 2-1 3-3 5v-10c0-1 0-1 1-1h2v-7z" class="O"></path><path d="M555 299c1-1 2-3 3-3 2-2 5-2 7-2h13c-1 1-2 2-2 3 1 2 0 6 0 8-1-1-2-3-4-3-1 0-2-1-4-1v-3h-3-1-1c-2 1-2 1-3 3-1 1 0 2 0 4v1h-1v-1h-1v-2l-1-2c0 1 0 2-1 3 0 1-1 2-1 3v-8z" class="h"></path><defs><linearGradient id="H" x1="631.637" y1="168.635" x2="652.593" y2="191.73" xlink:href="#B"><stop offset="0" stop-color="#46433e"></stop><stop offset="1" stop-color="#645c53"></stop></linearGradient></defs><path fill="url(#H)" d="M649 159l2 16 2 10h2c1 2 1 4 1 6-3 1-6 1-10 2h-3c-2 1-4 1-5 2h0c1 0 1 1 2 0h3l-2 2h1c1-1 1-1 3-1-1 1-1 1-2 1 0 1 0 1-2 2 0 0-1-1-1-2h-1-1l-2-2-3-11h-2c-1-2-1-4-2-6-1-3-3-6-4-9l2-3c0-1 1-2 1-2 1 0 2 0 2 1 1 1 1 2 1 4l5 18h1c1-1 1-1 0-2l1-1h1v-3-1l-3-19h2c1 0 4-1 5-1l2-1h4z"></path><path d="M625 169l2-3c0 2 1 3 2 5s2 6 3 9c0 1 1 2 1 4h-2c-1-2-1-4-2-6-1-3-3-6-4-9z" class="B"></path><path d="M649 159l2 16v7h-1c0-3 0-5-1-7h-1c-1-2-2-13-3-16h4z" class="Q"></path><path d="M636 161h2c1 2 1 4 2 7l3 18c-1 1-1 1-3 1 0-2 0-5-1-7l-3-19z" class="F"></path><path d="M643 160l2-1c1 3 2 14 3 16 0 3 2 7 1 10-1 1-1 1-2 0l-3-16c-1-4-1-6-1-9z" class="h"></path><path d="M556 321h2 16c-1 1-2 0-3 2 1 0 2 0 3 1h1c1 1 1 2 1 3s0 2-1 3h1l1-1c2 2 0 6 1 8l5 1 1 1-1 1c-2-1-3 0-5-2l-2 1v1c1 0 1 1 2 1 0 1 0 1-1 2v2c0 1 0 3 1 5l-1 2c-1 0-2 0-3-1l-7 1h1c-2 1-3 1-4 1s-2 1-3 1l-2-1-1-1v-1c0-2 1-2 2-4h1l-1-2-1-1-2 1v-4l-1-12v-8z" class="P"></path><path d="M575 342l-1-1c-1-1-2-1-3-3 1-1 1-1 2-1l3 2v1c1 0 1 1 2 1 0 1 0 1-1 2l-2-1z" class="O"></path><path d="M571 323c1 0 2 0 3 1h1c1 1 1 2 1 3l-1-1v3h-1c0-2 0-3-1-4-1 1-1 1-1 3h-3v-3l2-2z" class="B"></path><path d="M556 329h3l2-2v3h-1c-1 1-1 2-2 3 0 1 0 0 1 1 0 1 1 1 1 2l3 1c0 1-1 1-1 2l1 1h1l2 1-2 1-1-1v1l2 2v1l-2-2h-1l-1 1 1 1h-2l-1-1-2 1v-4l-1-12z" class="C"></path><path d="M557 341v-1l3-3h1v1 1l1 1c-2 1-2 2-2 4h1l1 1h-2l-1-1-2 1v-4z" class="D"></path><path d="M556 321h2 16c-1 1-2 0-3 2l-2 2h-1l-1 3v-1l-1-1c-1-1 0-1-1-1l-1 1h0c0 1 0 1 1 2v1l-2-2-1-1c-1 0-1 1-1 1l-2 2h-3v-8z" class="G"></path><path d="M564 326l-1-1h-1c-1 0-2-1-2-1 0-1 0-1 1-1 0-1 0-1 1-1l2 1c2 0 2 0 3-1l1 1v2l-1 3v-1l-1-1c-1-1 0-1-1-1l-1 1z" class="V"></path><path d="M566 341c3 0 4 1 6 3 0-1 0-1-1-2l1-1c1 0 2 1 2 1h1l2 1v2c0 1 0 3 1 5l-1 2c-1 0-2 0-3-1l-7 1h1c-2 1-3 1-4 1s-2 1-3 1l-2-1-1-1v-1c0-2 1-2 2-4h1l-1-2h2l-1-1 1-1h1l2 2v-1l-2-2v-1l1 1 2-1z" class="p"></path><path d="M559 353l-1-1v-1c0-2 1-2 2-4l1 2h3c-1 1-1 1-2 1l1 1c3 0 8-1 11 0l-7 1h1c-2 1-3 1-4 1s-2 1-3 1l-2-1z" class="P"></path><path d="M355 430v-51c5-3 10-7 15-9 1 1 1 1 1 2l-1 2c0 2-1 5-1 6 0 2 0 3 1 4v2c-1 1-1 1-2 1s-1 1-2 1v4h0c1 2 1 2 1 4l-2 2v2 1 12 14 6l-1 1v1h-3l-1 1h0c-1-1-2-1-2-2-1 0-1-1-1-2h2 0v-1c-2 0-2-1-4-1z" class="g"></path><path d="M369 380c0 2 0 3 1 4v2c-1 1-1 1-2 1l-1-4h-2l1-1 3-2z" class="X"></path><path d="M358 401c0-1-1-1 0-2 3-1 5-1 7-1v2 1h-1-1-5z" class="L"></path><path d="M364 401h1v12c-2 2 1 10-1 12-1-1-1-7-1-9l1-3c0-1 0-1-1-2v-8h0l1-2z" class="X"></path><path d="M363 411c1 1 1 1 1 2l-1 3h0-5l-1-1c-1-1 0-2 0-4h1c2 1 3 0 5 0z" class="e"></path><path d="M358 387v4c2 1 4 1 6 1 0-1 1-1 1-2s0-1 1-2v4h0c1 2 1 2 1 4-4-1-7 1-10-1 1-1 0-3 0-4 0-2 0-3 1-4z" class="X"></path><g class="d"><path d="M370 374c0 2-1 5-1 6l-3 2h-1c-2-1-3-2-4-3 2-3 6-4 9-5z"></path><path d="M358 387c0-2 0-5 1-6 1 1 5 3 5 5 1 1 1 2 1 4 0 1-1 1-1 2-2 0-4 0-6-1v-4zm0 14h5 1l-1 2h0v8c-2 0-3 1-5 0h-1c0-3 0-7 1-10z"></path></g><path d="M358 401h5 1l-1 2h0l-1-1c-1 0-3 0-4 1v8h-1c0-3 0-7 1-10z" class="Z"></path><path d="M358 416h5v12c-1 1-2 1-4 1 0-1-1-1-1-1-2-3-1-9 0-12z" class="d"></path><defs><linearGradient id="I" x1="548.815" y1="293.195" x2="574.488" y2="281.827" xlink:href="#B"><stop offset="0" stop-color="#b5a594"></stop><stop offset="1" stop-color="#dacdba"></stop></linearGradient></defs><path fill="url(#I)" d="M561 264h4c2-1 4 0 6 0l2 1c0 2 0 5 1 7v8c-1-1-1-1-1-2l-1 1v1h0c-1 0-2 0-3 1l1 1c2 1 5 1 7 1h0c2 2 2 8 1 10v1h-13c-2 0-5 0-7 2-1 0-2 2-3 3 0 0-1 0-1 1h-1c-1-2-1-3 0-5l-1-1c-1 0-1 1-1 2v4h0c-1-3-1-5-1-8 0-2 0-6-1-8 0-1-1-3 1-4 0 1 0 3 1 4h0c1-2 0-4 1-6 1 1 2 3 4 3l-3-3c-1-2-1-3-1-4 1-2 1-2 2-3v1c1 2 2 4 4 5 1-3 1-6 1-9h1c0-1 0-1-1-2l2-2z"></path><path d="M554 271v1c1 2 2 4 4 5 1-3 1-6 1-9h1v3 6h0-1c0 2 6 8 8 9 0 0 1 0 2 1h0c-2 0-2 0-4-1h0c1 5 2 1 4 4h0-4c0-1-1-2-2-2l-1-1-6-5v-1l-3-3c-1-2-1-3-1-4 1-2 1-2 2-3z" class="N"></path><path d="M561 264h4c2-1 4 0 6 0l2 1c0 2 0 5 1 7v8c-1-1-1-1-1-2l-1 1v1h0c-1 0-2 0-3 1l1 1c2 1 5 1 7 1h0l-2 2h1v1c-2 1-5 0-7 1-1-1-2-1-2-1-2-1-8-7-8-9h1 0v-6-3c0-1 0-1-1-2l2-2z" class="B"></path><path d="M569 281v-1h-4 0-1-1l-1-1h-1v-1h1c1-1 0-3 1-5h3v2h3v1l3 3v1h0c-1 0-2 0-3 1z" class="d"></path><path d="M561 264h4c2-1 4 0 6 0l2 1c0 2 0 5 1 7v8c-1-1-1-1-1-2l-1 1-3-3v-1h-3v-2h2v-1h-1-2-1c-1-1-1 0-1-1h-3v-3c0-1 0-1-1-2l2-2z" class="F"></path><path d="M563 271c0-2-1-3 1-4l1-1v1h0c1 2 1 4 2 5h-2-1c-1-1-1 0-1-1z" class="V"></path><defs><linearGradient id="J" x1="556.866" y1="600.521" x2="570.955" y2="600.048" xlink:href="#B"><stop offset="0" stop-color="#0e0d0d"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#J)" d="M580 583h3v3h-1c-2 1-3 1-5 0 0 5 1 20 0 22v7c1 1 1 1 2 1l1-1h1v2c-1 1-3 0-4 1l-1 1v-1h-2-4c-4 0-8 0-12-1v-1h2l-2-1c-1-3 0-6-1-9-1-1-1-3-1-4l1-15v-1c1 1 1 1 2 0 4-1 8 0 12 0 1-1 3-1 4-3 1 1 3 1 4 0h1z"></path><path d="M580 583h3v3h-12c1-1 3-1 4-3 1 1 3 1 4 0h1z" class="K"></path><path d="M571 589v-2h1 3c1 0 0 21 1 24v1h-1c-2-1-4-1-7-1l2-1-1-2c0-1 0-1 1-2h1c0 1 0 1 1 2v-1-5c-1-1 0-3 0-5 0-3-1-5-1-8z" class="F"></path><path d="M562 604c1 0 2 0 3 1 2 1 2 2 4 3l1 2-2 1c3 0 5 0 7 1h1v-1l1-3v7c1 1 1 1 2 1l1-1h1v2c-1 1-3 0-4 1l-1 1v-1h-2-4c-4 0-8 0-12-1v-1h2 1c0-4 0-2-2-4l1-1h5l1 1c1-1 1-2 2-3h-1s-1 1-2 1h-2c0-1 2-1 2-2v-1l-3-3z" class="K"></path><path d="M561 616c0-4 0-2-2-4l1-1v1c2 1 3 1 4 1s1 0 2-1l1 1c1 0 2 1 3 1 2 0 4 0 6-1v2h0c-3 0-4 0-6-1l-2 2-1-1c-1 0-5 1-6 1z" class="B"></path><path d="M562 604c-1 0-1-1-2-1v-5c1-2 0-5 1-8 1-1 1-1 3-1 0 1 1 1 2 2h0v1c1 1 1 2 2 2s1 0 2 1v-5l1-1c0 3 1 5 1 8 0 2-1 4 0 5v5 1c-1-1-1-1-1-2h-1c-1 1-1 1-1 2-2-1-2-2-4-3-1-1-2-1-3-1z" class="P"></path><path d="M570 595v2c-1 0-3 0-4-1l-1-1h-1c0-1 1-1 1-1v-1-1h1c1 1 1 2 2 2s1 0 2 1z" class="D"></path><path d="M424 337c1 1 1 2 2 2h1v-2h2l1 1h1v1l1 1h0l2-1c1 1 1 1 2 1h1c1 1 1 3 1 4h0l1 18-3 1h-2c-3-1-5 0-8 0h-3-2v-3l-1-1c0 1 0 1-1 1h-3-2v-2-1h-1c0-1 1-2 0-3h-4-2c-4 0-10 1-13 0l3-7c7-2 14 0 21-2 1-2 1-2 1-4h1 1l2-2h1v-2z" class="g"></path><path d="M432 340l2-1c1 1 1 1 2 1h1c1 1 1 3 1 4l-2-1-1-1c-1-1-2-2-3-2z" class="D"></path><path d="M408 351h-8l-1-1 1-2c3-2 12-1 15-1v3c-1 1-4 0-5 1h-2z" class="F"></path><path d="M415 347h3l-1 4v1c1 0 1 1 2 2l1-1h3v1c1 0 1 0 2-1 1 0 2 0 3 1v2l-1 1v1h-1l-2 2-1-1h-2v1l-1-1c0 1 0 1-1 1h-3-2v-2-1h-1c0-1 1-2 0-3h-4-2l1-1h-9c1-2 6-1 8-2h1 2c1-1 4 0 5-1v-3z" class="D"></path><path d="M409 354c2-1 4 0 6 0 0 1 0 0 1 1l1 1 1 1v1l1-1s0-1 1-1h1 0c-1 2-1 2-1 3s0 1-1 1h-3-2v-2-1h-1c0-1 1-2 0-3h-4z" class="F"></path><path d="M421 345c-1 0-1-1-1-1 1-2 1-1 3-2 3 0 4-1 6 0s4 2 4 3l-1 2 1 1h0l1-1c1 1 1 1 1 2l1 1c-1 1-2 1-4 1v-1l-2 1h0-1c-4 1-9 0-12 0l1-4c0-1 0-1 1-2l1 1 1-1z" class="H"></path><path d="M421 345c-1 0-1-1-1-1 1-2 1-1 3-2 3 0 4-1 6 0 0 1 0 1-1 1l4 4v1c-1-1-2-1-2-2-1 1-1 1-1 3h-2c1-1 0-1 1-2-1-1-2-1-3 0-3 0-3 2-5 3l-1-1v-1l2-2v-1z" class="p"></path><path d="M436 343l2 1h0l1 18-3 1h-2c-3-1-5 0-8 0h-3-2v-3-1h2l1 1 2-2h1v-1l1-1v-2c-1-1-2-1-3-1-1 1-1 1-2 1v-1h-3l-1 1c-1-1-1-2-2-2v-1c3 0 8 1 12 0h1 0l2-1v1c2 0 3 0 4-1l-1-1c1-1 0-2 2-2l-1-2h-1v-1l1-1z" class="E"></path><path d="M428 354c0-1 1 0 2-1 0 0 0-1 1-1l1 1c2 1 3 0 5 1 0 1-1 1-1 2v3h-2c-1 0-2 1-3 0l-1 1v1h0l-1-2-1-1-1 2v-1-1-1l1-1v-2z" class="h"></path><defs><linearGradient id="K" x1="598.308" y1="459.888" x2="642.624" y2="433.169" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#K)" d="M637 416l1 1c0 3 0 5 1 7 0 6 1 13 0 19-3 3-7 5-10 10-3 0-5 2-8 4h0-1l-1 1h-1c-1 1-1 2-2 2v1l-6 3v8h-1l-1-2h-8l1-3c1-2 1-6 2-8h-1l-1 1v-1l1-1v-4h2l1-1c1 0 1 0 2-1 0-1 1-3 0-4h-4v-2c0-1 0-4 1-5 1 0 3 0 5 1h1c1 2 2 2 4 2 5-1 13-6 16-10v-1c3-1 1 0 2-2h2v-1-1-1c1 0 2 0 3-1v-11z"></path><path d="M603 459h1l1 1h-1l-1 1h2c0 1 0 1-1 2l1 1h0c-1 0-2 1-2 2h1v1c-1 0-2 0-3 1l1 1 1-1h3c-1 1-2 1-2 1 1 0 3 0 4 1h-8l1-3c1-2 1-6 2-8z" class="E"></path><path d="M634 436h1c1 1 0 2 0 4-1 2-2 3-4 4l-2 1-1-1c0-2 0-2 1-4s2-3 5-4zm-13 11h1c1 0 2 0 2 1v2c-3 2-7 4-10 6-1 1-2 1-3 1 0-3 1-5 1-7l9-3z" class="d"></path><path d="M701 232c1 1 2 1 4 1l12 4c2 0 3 0 4 1l1 1v1s0-1 1-1c2 0 3 2 5 2 1 1 2 1 3 2h1c1 1 2 1 4 1 1 1 0 1 1 1-1 4-3 7-5 11-1 1-5 7-5 8s0 1-1 3h0l1-1h1l-6 6c0-1-1-2-1-2-2-2-3-4-4-6l-9-10s-10-9-11-11l2-7v-2s2-1 2-2z" class="H"></path><path d="M727 264c0 1 0 1-1 3h0l1-1h1l-6 6c0-1-1-2-1-2l6-6z" class="B"></path><path d="M727 242h0l3 3h-1c-1 0-1 1-2 2h0 1 1c0 1-1 2-1 3l-1 1c-1 0-2-1-3-1 1-1 0-1 1-2h-1 0-1l1-3 3-3z" class="k"></path><path d="M721 238l1 1v1s0-1 1-1l3 3 1-1v1l-3 3v-1l-1-1c-1 2-2 3-3 4l-2 3h-1c0-1 0-1-1-1h0-1l2-2-1-1c0-3 1-2 1-4 1-1 2-1 3-2l1-2z" class="a"></path><path d="M701 232c1 1 2 1 4 1l12 4c2 0 3 0 4 1l-1 2c-1 1-2 1-3 2 0 2-1 1-1 4l-2 2-1 1c0-1 0-1-1-2l-2 2c-1-1-1-1-1-2l-3-1h0c-1 1-1 1-2 1l-1 1c1 1 5 4 5 6h0s-10-9-11-11l2-7v-2s2-1 2-2z" class="b"></path><path d="M712 247l2-2h-1l-1 1-1-1c2-1 3-2 4-4-1-2-1 0-3 0 1-2 2-2 4-3 1 1 2 1 3 2h1c-1 1-2 1-3 2 0 2-1 1-1 4l-2 2-1 1c0-1 0-1-1-2z" class="x"></path><path d="M391 843l2-3h2v1c-1 1 0 1 0 2h1l2 2 1 1v1 1l2 2c1 1 1 2 1 3 1 0 1 1 2 2l1 1c-1 1-1 2-2 3h-1s-1 1-1 2c-1 1-3 2-3 4-1-1-1-1-2-1h0c-1 1-1 0-1 1l-1 1c1 1 2 1 2 2h2l1-1h2c1-1 1-1 2-1v1c-1 1-1 1-1 2l-12 4-6 2h-3v1l-4 1h-1c1-1 1-1 2-1h1v-2h-1-1l-1-1c-1-1-2-1-2-2-2 0-2 1-4 1h0l-1 1c-2 2-2 2-4 2-1 0-1 0-1-1l-1-1h0c-1 1-1 0-1 1-1 0-2-1-2-2-1-1-1-1-1-2 1-2 1-2 0-4 1-1 3-2 5-2h1c0-1 1-1 2-1l-1-1c1-1 2-2 3-2 4-2 8-6 11-9 2 0 5-3 6-4 2-2 3-3 5-4z" class="a"></path><path d="M377 874c0-2 0-2 1-3 1 0 1 1 2 2l-1 1h-1-1z" class="k"></path><path d="M370 872l-1-1s-1-1-1-2l1-2c1 0 2-1 3-1 1 1 1 0 1 1v2c0 1 0 1 1 2-2 0-2 1-4 1h0z" class="H"></path><path d="M378 860h1c1 0 1 1 2 1v1c2 1 2 1 3 2-1 1-1 2-3 2 0-1-1-2-1-3h-1v2h0c-1-1-2-2-3-4l2-1z" class="b"></path><path d="M392 852h0c1 0 2-1 2-1l1 2c0 1-3 4-4 5h-1l-3 3h0l-1 2h-1v-1l-2-2 1-1c3-1 5-6 8-7z" class="H"></path><path d="M391 843c1 2 1 2 1 5l-1 1h0 0c-1 1-1 2-2 2-2 1-3 3-5 3 0 1-1 4-1 4-1-1-1-1-1-2s0-1-1-2c0-1 0-2-1-3h0c2 0 5-3 6-4 2-2 3-3 5-4z" class="c"></path><path d="M391 843l2-3h2v1c-1 1 0 1 0 2h1l2 2 1 1v1 1l2 2c1 1 1 2 1 3 1 0 1 1 2 2l1 1c-1 1-1 2-2 3h-1s-1 1-1 2c-1 1-3 2-3 4-1-1-1-1-2-1h0c-1 1-1 0-1 1l-1 1c1 1 2 1 2 2h2l1-1h2c1-1 1-1 2-1v1c-1 1-1 1-1 2l-12 4s-1-1-2-1c-2-2 2 1-1-1l1-1c0-2 1-3 2-4 1 0 1-1 2-1l3-3h1l-1-1c-4 1-4 5-8 6v-1h-1-1c1-2 2-3 3-4l-1-1h0l3-3h1c1-1 4-4 4-5l-1-2s-1 1-2 1h0v-1-1l-1-1h0 0l1-1c0-3 0-3-1-5z" class="K"></path><path d="M391 843l2-3h2v1c-1 1 0 1 0 2 1 1 2 1 2 3-2 1-3 2-4 2h-1c0-3 0-3-1-5z" class="b"></path><defs><linearGradient id="L" x1="615.926" y1="484.059" x2="645.962" y2="475.018" xlink:href="#B"><stop offset="0" stop-color="#4a494a"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#L)" d="M651 456c1 0 1 0 2-1l1-1 1 1 1 1 1-1-1 2 1 1c1-1 1-2 3-3 0 1 1 1 2 1l-1 1h2v-2c1 0 3 1 4 1-1 3 0 6 1 8 1 0 4-2 5-3-1 3-6 6-8 8l-7 6h1c-3 3-8 7-12 9-1 1-5 2-5 3l1 2-1 1c-7-6-12-9-21-11h-1c-2-1-4-1-6-2h-4c-2-1-4-1-7-1h-2c1-1 1-1 1-2h5l1-2h1 1v3h1c9-1 17-4 25-8l15-11z"></path><path d="M644 478l-1-1h-1c-1-1-1-2-1-3 0-2 0-2 1-4h2c2 0 3 0 4 2s1 2 1 4h-1c-1-1-1-1-1-3l-1-1c0 1-1 2-1 3 0 0 1 0 1 1h-1c-1-1-1-1-2 0h0c1 1 1 1 2 1l1 1h-2z" class="Z"></path><path d="M644 478h2l-1-1c-1 0-1 0-2-1h0c1-1 1-1 2 0h1c0-1-1-1-1-1 0-1 1-2 1-3l1 1c0 2 0 2 1 3h1v1l2-2v1c1-1 2-1 2-2v-1h1c0 1 0 2-1 3 0 1 0 2-1 3h1l5-4h1c-3 3-8 7-12 9-1-1 0-1-1-1v-1l1-1-1-1c-1 1-2 1-3 1l-1-1h0v-2h2z" class="L"></path><path d="M673 461c-1 3-6 6-8 8l-7 6-5 4h-1c1-1 1-2 1-3 1-1 1-2 1-3h-1v1c0 1-1 1-2 2v-1l-3-6h1c0-1 1-2 1-3 1-1 1-1 3-1l1 1 1 2c1 1 2 2 2 4v1c2 0 8-7 10-8l1-1c1 0 4-2 5-3z" class="J"></path><path d="M663 455c1 0 3 1 4 1-1 3 0 6 1 8l-1 1c-2 1-8 8-10 8v-1c0-2-1-3-2-4l-1-2 2-1s0-1-1-1h0v-3l1 1v1-1c1-2 3-4 5-5h2v-2z" class="Y"></path><path d="M656 465s0-1-1-1h0v-3l1 1v1c1 1 1 1 3 2h0c1 1 2 1 3 1-1 2-2 2-4 3-1-1-1-2-2-3v-1z" class="W"></path><defs><linearGradient id="M" x1="640.626" y1="464.502" x2="662.331" y2="460.441" xlink:href="#B"><stop offset="0" stop-color="#6f6d6e"></stop><stop offset="1" stop-color="#abaaa8"></stop></linearGradient></defs><path fill="url(#M)" d="M651 456c1 0 1 0 2-1l1-1 1 1 1 1 1-1-1 2 1 1c1-1 1-2 3-3 0 1 1 1 2 1l-1 1c-2 1-4 3-5 5v1-1l-1-1v3h0c1 0 1 1 1 1l-2 1-1-1c-2 0-2 0-3 1 0 1-1 2-1 3h-1-2v-1h-2c-2-1-5 0-6 1-1 0-2 1-2 1l-1-1 2-1h0l-1-1 15-11z"></path><defs><linearGradient id="N" x1="444.771" y1="222.181" x2="439.085" y2="222.979" xlink:href="#B"><stop offset="0" stop-color="#302f2d"></stop><stop offset="1" stop-color="#4c4a48"></stop></linearGradient></defs><path fill="url(#N)" d="M435 200l1 1v2h1v-2h3 0l4 2h-1v1c1 10 1 21 1 31 0 4-2 9-2 13l-1-1-1 1 1 2c-1 0-1 1-2 1s-2 0-4 1c-1 0-2 0-3 1h-1-11-2c1-2 0-3 1-5 0-1 1-5 1-7 1-3 0-6 1-10 1-1 2-1 4-1l-3-1v-1-1c0-3-3-6-3-10h0v-1-1l-1-1c6-1 10-3 15-6 1-2 2-6 2-8z"></path><path d="M435 200l1 1v2h1v-2h3 0l4 2h-1-1c0 2 1 7-1 9-1-2 1-6-1-9v1c0 1 0 4-1 5h-1-1v2h1v-1h1v5c0 1 1 3 0 5l1 1v5h0c-2-1-1-4-1-6l-1-1h0c-1 2 1 5 0 7l-1-1-1-1-1-3v-3-1l-2 2h-1l-1 1h0-1v-1-5-1l-1-1c1-1 2-2 3-2h1v3h0l1-4-1-1c1-2 2-6 2-8z" class="I"></path><path d="M433 208l1 1-1 4h0v-3h-1c-1 0-2 1-3 2l1 1v1 5 1h1 0l1-1h1c2 2 1 4 1 7 1 0 1 1 2 1-1 1-6 0-8 0s-4 0-6 1v-1c0-3-3-6-3-10h0v-1-1l-1-1c6-1 10-3 15-6z" class="R"></path><path d="M420 215c1 0 2-1 4-1l1 13h0 3 0c-2 0-4 0-6 1v-1l1-1v-2c0-1 0-2-1-3 1-2 1-4 1-6-2 1-2 1-3 0z" class="J"></path><path d="M419 215h1c1 1 1 1 3 0 0 2 0 4-1 6 1 1 1 2 1 3v2l-1 1c0-3-3-6-3-10h0v-1-1z" class="U"></path><path d="M421 231c1-1 2-1 4-1h3c2 0 5 0 7 1v2l1 1 1-1v1h3s1-1 1-2c0 1 1 1 1 2s0 1-1 2v2c0 1 0 1-1 2v2h0c1-1 1 0 2-1 0 1-1 5-1 6l-1 1 1 2c-1 0-1 1-2 1s-2 0-4 1c-1 0-2 0-3 1h-1-11-2c1-2 0-3 1-5 0-1 1-5 1-7 1-3 0-6 1-10z" class="K"></path><path d="M421 231c1-1 2-1 4-1h3c1 2 1 2 0 3-1 2-2 3-3 4h-1c-1-1-1-1-1-2v-2h-1c0-1 0-1-1-2z" class="H"></path><path d="M437 234h3s1-1 1-2c0 1 1 1 1 2s0 1-1 2v2c0 1 0 1-1 2v2h0c-1 1-1 2-1 3l-2 1c-1 1-2 1-3 1v-1c-3 0-4 2-7 2 1-1 3-3 3-5v1l-1-1 4-3h1v-1h-1v-1h-2v-1c1 0 2-1 3-1v1c2 0 3-1 4-1v-1h-2l1-1z" class="Y"></path><path d="M437 234h3s1-1 1-2c0 1 1 1 1 2s0 1-1 2v2c0 1 0 1-1 2-2 1-6 5-8 5 1-2 2-2 3-3l1-1c-1-1-1-1-1-2h-1-1v-1h-2v-1c1 0 2-1 3-1v1c2 0 3-1 4-1v-1h-2l1-1z" class="Q"></path><path d="M440 242h0c1-1 1 0 2-1 0 1-1 5-1 6l-1 1 1 2c-1 0-1 1-2 1s-2 0-4 1c-1 0-2 0-3 1h-1-11c1-1 2-1 3-1h1l-1-1 1-1c-1-1-2-1-2-2v-1-2h2c1 0 0 1 1 3h2 0c3 0 4-2 7-2v1c1 0 2 0 3-1l2-1c0-1 0-2 1-3h0z" class="N"></path><path d="M424 250c-1-1-2-1-2-2v-1-2h2c1 0 0 1 1 3l1 1h3 0c0 1 0 1 1 2-2 0-4-1-6-1z" class="H"></path><path d="M440 242h0c1-1 1 0 2-1 0 1-1 5-1 6l-1 1 1 2c-1 0-1 1-2 1s-2 0-4 1c0-1-1-1-1-1-1-1-2 0-3 0 2-1 4-3 6-4v-1l2-1c0-1 0-2 1-3h0z" class="J"></path><path d="M425 155c1 1 1 2 3 3 0 1 0 3 1 5 0-1 1-1 1-2l1 1v2l1-1c1 1 1 2 2 3 1-1 3-1 5-1l1 1c1 0 2 0 2-1 1 2 2 2 3 3h0l2 2 1 1v1l1 1 1-1c0 2 0 1-1 2l2 2v1l-1 1-1-1-1 1 1 1c-1 1-3-1-4 1h2v1l-2 2c0-1-1-2-2-3l-1-1c-3 1-4 4-6 4-2 1-3 3-5 5l-1 1h-2c0 1-1 2-3 3-1 0-2 1-3 1-1 1-2 2-4 2-3-1-6-1-9-3h-2l-3-1v-1-1c-1 0-2-1-3-1l1 1-1 1c-1-2-2-2-2-4 0-1 1-2 2-3-1-1 0-1-1 0-1 0-2 1-3 1 0 0-1 0-1-1l8-7c1-2 1-2 3-3h1v-1c1-2 2-3 3-4v1l3-1h1l-2-1c4-4 8-7 12-12z" class="Y"></path><path d="M427 185c0-2-1-3 0-5h1l2 2c0 1 0 1-1 1l-1 1-1 1z" class="S"></path><path d="M411 184l2 1c2-2 5-4 7-7l2 2c0 1-1 1-1 2l1 1c1-1 2-2 3-4h1l-2 4c0 1 1 1 2 2h1l1-1 3 2-3 1c-1 0-1 1-2 0-2-1-5-1-7-2l1-1-1-1-1 1h0c-1 1-2 2-4 2-1 1-3 2-4 3h-1l-2-2 4-3z" class="c"></path><path d="M410 189c1-1 3-2 4-3 2 0 3-1 4-2h0l1-1 1 1-1 1c2 1 5 1 7 2 1 1 1 0 2 0v2c0 1-1 2-3 3-1 0-2 1-3 1-1 1-2 2-4 2-3-1-6-1-9-3h-2l-3-1v-1l3-3 2 2h1z" class="W"></path><path d="M407 187l2 2h1c1 0 2 0 3 1 1 0 1 1 2 1s2 0 4 1v2l-1 1c-3-1-6-1-9-3h-2l-3-1v-1l3-3z" class="H"></path><path d="M407 187l2 2h1c1 0 2 0 3 1-1 1-3 1-4 2h-2l-3-1v-1l3-3z" class="b"></path><path d="M431 164l1-1c1 1 1 2 2 3v1l1-1 1 2-1 1v2 1c1 0 1 1 2 2v1l1 1c0 2 0 3-1 4 0 1 0 2-1 3-2 1-3 3-5 5l-1 1h-2v-2l3-1 2-3h0c-2-2-3-3-4-5 1-1 1-2 1-3l-2 2c-2-1-3 0-4 1v1l-1-1 2-2c-1 0-1 0-1-1l1-2h0l2-2c0-1 1-1 1-2h-1c-1 0-2-1-2-2l1-1c1 1 1 1 2 1h0c1 1 1 1 2 1h1l-1-1v-1-1l1-1z" class="v"></path><path d="M427 171l1 1c0 2 0 3-1 4l-1-1-1-2h0l2-2z" class="L"></path><path d="M433 183l1-2c0-1 1-1 1-2s-2-3-2-4v-1l3 3h1c-1-1-1-1 0-2l1 1c0 2 0 3-1 4 0 1 0 2-1 3-2 1-3 3-5 5l-1 1h-2v-2l3-1 2-3h0z" class="J"></path><defs><linearGradient id="O" x1="427.452" y1="161.576" x2="419.478" y2="167.834" xlink:href="#B"><stop offset="0" stop-color="#65605a"></stop><stop offset="1" stop-color="#7f7a74"></stop></linearGradient></defs><path fill="url(#O)" d="M425 155c1 1 1 2 3 3 0 1 0 3 1 5 0-1 1-1 1-2l1 1v2l-1 1v1 1l1 1h-1c-1 0-1 0-2-1h0c-1 0-1 0-2-1l-1 1c0 1 1 2 2 2h1c0 1-1 1-1 2l-2 2v-4h-2-1c-2 2-4 3-6 4l-2-1c-1-1-2-1-3-3l3-1h1l-2-1c4-4 8-7 12-12z"></path><defs><linearGradient id="P" x1="442.098" y1="169.231" x2="437.781" y2="174.831" xlink:href="#B"><stop offset="0" stop-color="#484747"></stop><stop offset="1" stop-color="#605f5e"></stop></linearGradient></defs><path fill="url(#P)" d="M440 166c1 0 2 0 2-1 1 2 2 2 3 3h0l2 2 1 1v1l1 1 1-1c0 2 0 1-1 2l2 2v1l-1 1-1-1-1 1 1 1c-1 1-3-1-4 1h2v1l-2 2c0-1-1-2-2-3l-1-1c-3 1-4 4-6 4 1-1 1-2 1-3 1-1 1-2 1-4l-1-1v-1c-1-1-1-2-2-2v-1-2l1-1-1-2-1 1v-1c1-1 3-1 5-1l1 1z"></path><path d="M434 166c1-1 3-1 5-1v3h-1-1l-2-2-1 1v-1zm6 0c1 0 2 0 2-1 1 2 2 2 3 3h0l2 2c-2 0-3 0-4-1s-1-1-2-1c-1-1 0-1-1-2z" class="C"></path><path d="M448 172l1 1 1-1c0 2 0 1-1 2l2 2v1l-1 1-1-1-1 1 1 1c-1 1-3-1-4 1h2v1l-2 2c0-1-1-2-2-3l-1-1c-3 1-4 4-6 4 1-1 1-2 1-3 1-1 1-2 1-4h1l3 2h1v-2l-1-1c0-1 1-2 1-3l2 1c1-1 2-1 3-1z" class="Q"></path><path d="M404 176c1-2 1-2 3-3h1v-1c1-2 2-3 3-4v1c1 2 2 2 3 3l2 1c2-1 4-2 6-4l2 1c0 2 0 2-1 4s-3 3-5 4c-2 2-4 4-7 6l-4 3-3 3v-1c-1 0-2-1-3-1l1 1-1 1c-1-2-2-2-2-4 0-1 1-2 2-3-1-1 0-1-1 0-1 0-2 1-3 1 0 0-1 0-1-1l8-7z" class="E"></path><path d="M404 176c1-2 1-2 3-3h1v-1c1-2 2-3 3-4v1c1 2 2 2 3 3h-3c-1 1-2 2-2 3-1 2 0 1-1 3v-1l-1-1c-2 2-4 5-6 7-1-1 0-1-1 0-1 0-2 1-3 1 0 0-1 0-1-1l8-7z" class="D"></path><path d="M387 141l12 2 7 2h2l-1 1c0 1 0 1 1 1 1 1 2 2 3 2l2 1v1c1 0 2 0 3-1l2-1 1 1h-1l1 1c1 1 2 1 3 1l-1 1c1 1 2 2 3 2 1-1 1-1 2-1l-1 1c-4 5-8 8-12 12l2 1h-1l-3 1v-1c-1 1-2 2-3 4v1h-1c-2 1-2 1-3 3-1-2-3-3-4-5h-1-2c-1 1-3 2-4 3-2 1-2 1-3 3v-1l-2 1h-1v-3h0c-1 0-1 1-1 1l-1 1c-1-1-1-1-1-3l1-1h-1l-2 2c-2-1-2-1-3-2 0-2 2-3 3-4-3 0-3-3-5-5-1 0-4-1-6-1-2-1-3 0-5 0 0-1 1-3 1-4l4-8 3-8 3 1 6 1-1-1 1-1c1 0 3 0 4-1z" class="Q"></path><path d="M374 152h-1v-3l1-1c1 2 2 2 3 3-1 1-2 0-3 1z" class="e"></path><path d="M387 155h0c2-1 3-2 4-3v1l2 2c0 1-2 2-3 4l-2 2h-1c1-1 1-1 1-2-1-1-1-1 0-2 0-1 0-1-1-2z" class="U"></path><path d="M377 151h2c2 0 4-2 5-4h1c0 2 0 3-1 4h3v-2c2 0 2 1 3 2-1 2-2 2-4 3h-1c-2 1-6 1-8 1l-3-3c1-1 2 0 3-1z" class="J"></path><path d="M371 150l1 2 2 2c1 1 3 2 5 3h-1-2l-1 1v1c-1 0-1 0-2 1-1 0-1 1-2 2-2-1-3 0-5 0 0-1 1-3 1-4l4-8z" class="e"></path><path d="M372 152l2 2h0c0 1 0 2-1 3-1 0-2 0-3-1 1-1 2-3 2-4z" class="W"></path><path d="M387 141l12 2 7 2-2 3c-1 1-1 1-1 3h-1v-1l-1-2c-1 1-1 1-1 2l1 1c-2 0-3 1-5 2 0 1-1 1-1 1v-2c-2 1-2 0-3 0 1-1 1-1 1-2h-1l2-2v-1h-2l-1-1c-2-1-3-1-5 0l-1-1c2 0 2 0 3-1h-5l-1-1 1-1c1 0 3 0 4-1z" class="O"></path><path d="M387 155c1 1 1 1 1 2-1 1-1 1 0 2 0 1 0 1-1 2h1l2-2h0c-2 4-4 7-8 9-3 0-3-3-5-5-1 0-4-1-6-1 1-1 1-2 2-2 1-1 1-1 2-1v-1l1-1h2 1c2-1 6-1 8-2z" class="J"></path><path d="M390 159h0c-2 4-4 7-8 9v-2c-1-2-3-3-3-5v-1h1 1c1 0 2 0 3 1h-3v1l3 3 1-1v-2c0-2 1-2 2-3h1c0 1 0 1-1 2h1l2-2z" class="d"></path><path d="M375 159c2 0 3-1 5-1 0 1 0 1-1 2v1c0 2 2 3 3 5v2c-3 0-3-3-5-5-1 0-4-1-6-1 1-1 1-2 2-2 1-1 1-1 2-1z" class="X"></path><path d="M406 145h2l-1 1c0 1 0 1 1 1 1 1 2 2 3 2l2 1v1c1 0 2 0 3-1l2-1 1 1h-1l1 1c1 1 2 1 3 1l-1 1c1 1 2 2 3 2 1-1 1-1 2-1l-1 1c-4 5-8 8-12 12l2 1h-1l-3 1v-1c-1 1-2 2-3 4v1h-1c-2 1-2 1-3 3-1-2-3-3-4-5h-1-2c-1 1-3 2-4 3-2 1-2 1-3 3v-1l-2 1h-1v-3h0c-1 0-1 1-1 1l-1 1c-1-1-1-1-1-3l1-1h-1l-2 2c-2-1-2-1-3-2 0-2 2-3 3-4 4-2 6-5 8-9l4-3c0 2 1 2 1 4l2-2h0c-1 2-1 3-3 4v2c1 0 1 1 2 2l3 1 6-5v-1c-1 0-2-1-3-2h0c0-1-1-1-1-2-1 0-1-1-2-1 0-2 1-2 1-3 1-1 1-2 1-2l-1-1c0-1 0-1 1-2l1 2v1h1c0-2 0-2 1-3l2-3z" class="B"></path><path d="M412 160c1-1 1-2 2-2l2 2-1 2c-2-1-2-1-3-2z" class="J"></path><path d="M412 160c-1-1-2-2-3-4l4-3 3 1c0 2-1 2-2 4-1 0-1 1-2 2z" class="U"></path><path d="M400 153l2-1 3 2c0 2-2 2-2 3 1 0 2 0 3-1l1 1c0 2 0 2-1 3-2 0-3-1-4-1 0-1-1-1-1-2-1 0-1-1-2-1 0-2 1-2 1-3z" class="J"></path><path d="M413 153c1-1 2-1 3-2h0 3c1 1 2 1 3 1l-1 1c-1 2-3 5-5 7l-2-2c1-2 2-2 2-4l-3-1z" class="I"></path><path d="M390 159l4-3c0 2 1 2 1 4l-11 12-2 2c-2-1-2-1-3-2 0-2 2-3 3-4 4-2 6-5 8-9z" class="X"></path><path d="M385 172l8-8 4 5-2 2-2 2c-2 0-2 2-3 3l-2 1h-1v-3h0c-1 0-1 1-1 1l-1 1c-1-1-1-1-1-3l1-1z" class="c"></path><path d="M400 171h0v-1l-1-1 3-4c2-1 2-1 4-1h0v-1h-1l1-1 3-3c1 1 3 2 4 4l-1 1-2-1h0l2 3v1h1l2 1h-1l-3 1v-1c-1 1-2 2-3 4v1h-1c-2 1-2 1-3 3-1-2-3-3-4-5z" class="U"></path><defs><linearGradient id="Q" x1="670.353" y1="406.576" x2="681.492" y2="489.186" xlink:href="#B"><stop offset="0" stop-color="#7e7a73"></stop><stop offset="1" stop-color="#c1c0bf"></stop></linearGradient></defs><path fill="url(#Q)" d="M714 368c1 6 1 13 1 19-1 8-1 16-3 24l-8 24c-1 5-4 10-7 15-4 7-8 15-14 21-1 2-3 4-5 5-1 1-3 3-5 4 0 1-1 1-2 2-1 0-1 0-2 1h0c-4 2-6 4-9 6-2 2-5 3-6 5-1 0-2 1-3 1-2 0-4-1-6-2 0-1-1-2-2-4l-1-2c0-1 4-2 5-3 4-2 9-6 12-9 10-7 19-17 26-27 1-2 5-7 4-9l1-1v-1c3-2 8-11 9-14l-3-1c1-1 1-2 2-3 1 0 0 0 1 1l1 1c1-2 2-5 2-7v-1h1c1-2 2-5 3-7l1-4h0c4-7 5-15 5-22 1-4 2-7 2-12z"></path><defs><linearGradient id="R" x1="717.421" y1="401.489" x2="703.579" y2="394.011" xlink:href="#B"><stop offset="0" stop-color="#70706b"></stop><stop offset="1" stop-color="#8b8583"></stop></linearGradient></defs><path fill="url(#R)" d="M714 368c1 6 1 13 1 19-1 8-1 16-3 24l-8 24v-1c1-3 2-6 2-8 2-8 3-15 4-22l-1-1v2c-1-1-1-1-1-2v-1c0 1-1 2-2 3v1l1-4h0c4-7 5-15 5-22 1-4 2-7 2-12z"></path><path d="M442 260h1c2 1 4 0 5 1v1 3l1 34 1 10v11c1 2 1 4 1 6-2 0-4-1-6-1h0-1-1c0-2 0-2 1-3l1-1v-1h-1v-1c-1 1-1 1-2 1h0c-1-1-3-2-5-2l-7-1c-2 1-5 1-8 1h-5c1-3 1-5 2-8v-1c-1-1-1-1-1-2 1-2 1-3 2-5-1-1 0-3 0-4-1-1-2-1-3-2v-2-8c1-1 2-1 3-1l1-1c-2 0-3 0-4-1v-3h3 4 4 4c1 0 2-1 3-3l1 1 1-1-1-2h4c1-2 1-3 1-5v-2-6l1-2z" class="T"></path><path d="M441 268c1-1 0-2 2-3 0 1 0 1 2 2 1 1 0 7-1 9l-1 2h-1 0c1-2 2-5 2-7s-1-2-2-2l-1 1v-2z" class="l"></path><path d="M442 313l1 3c1 2 2 2 2 4h-1v-1c-1 1-1 1-2 1h0c-1-1-3-2-5-2h0c2-2 4-3 5-5z" class="D"></path><path d="M442 260h1c2 1 4 0 5 1v1 3c-2-1-3-1-5 0s-1 2-2 3v-6l1-2z" class="AB"></path><path d="M449 299l1 10v11c1 2 1 4 1 6-2 0-4-1-6-1h3 0l1-1c0-1-1 0-2-1 0-1 0-1 1-1h0v-7-3c1-2 1-2 0-4v-2c1-2 0-4 1-7z" class="c"></path><defs><linearGradient id="S" x1="430.612" y1="268.621" x2="429.787" y2="293.409" xlink:href="#B"><stop offset="0" stop-color="#beb1a7"></stop><stop offset="1" stop-color="#d4d1c7"></stop></linearGradient></defs><path fill="url(#S)" d="M441 270l1-1c1 0 2 0 2 2s-1 5-2 7h0c-1 1-2 2-3 4s-4 5-6 6-3 3-4 5h0v1h-12v-8c1-1 2-1 3-1l1-1c-2 0-3 0-4-1v-3h3 4 4 4c1 0 2-1 3-3l1 1 1-1-1-2h4c1-2 1-3 1-5z"></path><path d="M436 275h4l-6 6c-2 2-4 4-6 5h-3c0-1 0-1-1-1v1h-1c-1-1-2-1-3-1l1-1c-2 0-3 0-4-1v-3h3 4 4 4c1 0 2-1 3-3l1 1 1-1-1-2z" class="D"></path><path d="M420 280h4 4 4v2c-2 0-2 0-2 1-1 0-1-1-2-1 0 1 0 1-2 1-2 1-6 1-9 0v-3h3z" class="N"></path><path d="M429 294c2 0 4 0 6 2l5 2c0 5 0 10 2 15-1 2-3 3-5 5h0l-7-1c-2 1-5 1-8 1h-5c1-3 1-5 2-8v-1c-1-1-1-1-1-2 1-2 1-3 2-5-1-1 0-3 0-4-1-1-2-1-3-2v-2h12z" class="F"></path><path d="M420 302v-4h3 1 1v1c0 1 0 2-1 3h-2v3l2-1v3h2v1h-2v4s0 1-1 1l1 1 1 1-1 1h1l2-1h0l1 1c1-1 1-3 1-4 1 1 1 1 1 2v3c-2 1-5 1-8 1h-5c1-3 1-5 2-8v-1c-1-1-1-1-1-2 1-2 1-3 2-5z" class="M"></path><path d="M435 296l5 2c0 5 0 10 2 15-1 2-3 3-5 5h0l-7-1v-3c0-1 0-1-1-2l1-1v-2l-1-1v-1c1 0 2 0 2-1h1v-6l1-1 1 1c1 0 1-1 2-1 0-1 0-2-1-3z" class="h"></path><path d="M430 314l1 2h2v-4l1-1v3l2-1 1 1h0v4h0l-7-1v-3z" class="E"></path><path d="M429 312l1-1v-2l-1-1v-1c1 0 2 0 2-1h1v-6l1-1 1 1h0c0 1 0 2 1 3h0v3 2l1 1-1 1c-1 1-1 0-1 1l-1 1v4h-2l-1-2c0-1 0-1-1-2z" class="D"></path><path d="M537 212c4 1 7 0 11 1v2c0 1 1 1 1 2 2 0 3 0 5-1l2 2c-1 2-1 2 0 4h0 1c1 0 2 0 3-1l-1-1c1-1 1-2 2-3 0 1 0 2 2 3h1c0-1 1-1 2-1l1 1h8v10c0 2 1 5 0 7v1h-1c1 1 1 1 1 2l1 1v1h-1c0 1-1 1-2 2h3c1 3 1 6 2 10v1h1c1 0 2 1 2 1l1 2c0 1 1 2 0 3 0 2 0 2-1 3h-2 0l1 1-1 1h-1l-1 1h0l-1-1c-1 0-1 1-2 0l-1-1-2-1c-2 0-4-1-6 0h-4l-2 2c1 1 1 1 1 2h-1c0 3 0 6-1 9-2-1-3-3-4-5v-1-6-3l-1-2h-1l-1-1h2l3-3h-1-2c-1 0-2 0-3 1-3 1-5 0-7 1v1h-2l-1-1v-2c0-1-1-3-1-5v-4l-2 1v-1l1-2c0-4-1-8-1-12 0-5-1-15 2-19h-3l1-2z" class="j"></path><path d="M556 256c5 0 12-1 17 0h-6c-1 0-2 1-3 1v2h-11l3-3z" class="b"></path><path d="M573 256c3 1 6 1 9 2 0 1 1 2 0 3-5-1-11-1-17-2h-1v-2c1 0 2-1 3-1h6z" class="k"></path><defs><linearGradient id="T" x1="573.482" y1="266.308" x2="569.343" y2="257.312" xlink:href="#B"><stop offset="0" stop-color="#060604"></stop><stop offset="1" stop-color="#261e19"></stop></linearGradient></defs><path fill="url(#T)" d="M565 259c6 1 12 1 17 2 0 2 0 2-1 3h-2 0l1 1-1 1h-1l-1 1h0l-1-1c-1 0-1 1-2 0l-1-1-2-1c-2 0-4-1-6 0h-4c1-1 1-2 1-3v-1l3-1z"></path><path d="M553 259h11 1l-3 1v1c0 1 0 2-1 3l-2 2c1 1 1 1 1 2h-1c0 3 0 6-1 9-2-1-3-3-4-5v-1-6-3l-1-2h-1l-1-1h2z" class="V"></path><path d="M553 259h11 1l-3 1v1c0 1 0 2-1 3l-2 2c-1-1-1-1-1-3 1-1 1-1 1-2h-4l-1 1-1-2h-1l-1-1h2z" class="D"></path><path d="M570 242c1 0 1 0 1-1s0-1-1-2l-2-1 1-1v-2l1-1c0 2 0 3 1 4h0v-1c1-1 2-1 3 0h1v1h-1c1 1 1 1 1 2l1 1v1h-1c0 1-1 1-2 2h3c1 3 1 6 2 10-1-2-3-3-5-3l1 2h0-1-2l-1-2c-1 1-1 2-2 2l-1-1-1-1v1c-2-1-4-3-5-4h-1c0 1 0 1 1 2l2 2s0 1-1 1l-3-2c0-1 0-3-1-4-1 0-1 1-2 2 0-1 1-7 1-8 1-1 1-2 1-3 1-1 1-1 3-1 1 2 1 2 2 3 3 0 5 2 7 4v-2z" class="a"></path><path d="M558 238c1-1 1-1 3-1 1 2 1 2 2 3 2 1 5 3 6 5h-2v1c-2-1-2-2-3-4-1 1-1 2-2 3 0 1 1 2 2 3s2 1 4 2l1 1v-1c0-1 0-1 1-2v3c-1 1-1 2-2 2l-1-1-1-1v1c-2-1-4-3-5-4h-1c0 1 0 1 1 2l2 2s0 1-1 1l-3-2c0-1 0-3-1-4-1 0-1 1-2 2 0-1 1-7 1-8 1-1 1-2 1-3z" class="G"></path><path d="M559 220c1-1 1-2 2-3 0 1 0 2 2 3h1c0-1 1-1 2-1l1 1h8v10c0 2 1 5 0 7h-1c-1-1-2-1-3 0v1h0c-1-1-1-2-1-4l-1 1v2l-1 1 2 1c1 1 1 1 1 2s0 1-1 1v2c-2-2-4-4-7-4-1-1-1-1-2-3l1-2-1-1c-1-1 0-2 0-3h-1l-2-3c1-1 1-3 1-4l-1-2h-1c1 0 2 0 3-1l-1-1z" class="N"></path><path d="M559 220c1-1 1-2 2-3 0 1 0 2 2 3h1c0-1 1-1 2-1l1 1h0l1 1c0 1-1 1 0 2h-1c-1 1-2 2-3 2l-1 1h1l2-1h1l-1 2h1v1h-2c1 1 0 1 1 1 1 1 1 0 2 1-1 1-3 1-4 1 1 1 3 1 4 1 0 1-1 1-2 1 1 1 0 1 1 2 0 1 0 1-1 2h-1v1c2 1 4 2 5 4v2c-2-2-4-4-7-4-1-1-1-1-2-3l1-2-1-1c-1-1 0-2 0-3h-1l-2-3c1-1 1-3 1-4l-1-2h-1c1 0 2 0 3-1l-1-1z" class="P"></path><path d="M559 220c1-1 1-2 2-3 0 1 0 2 2 3h1c0-1 1-1 2-1l-1 1 1 1h0c-1 1-2 1-2 3h-1l-2 7h-1l-2-3c1-1 1-3 1-4l-1-2h-1c1 0 2 0 3-1l-1-1z" class="D"></path><path d="M537 212c4 1 7 0 11 1v2c0 1 1 1 1 2 2 0 3 0 5-1l2 2c-1 2-1 2 0 4h0 1 1l1 2c0 1 0 3-1 4l2 3h1c0 1-1 2 0 3l1 1-1 2c-2 0-2 0-3 1 0 1 0 2-1 3 0 1-1 7-1 8 1-1 1-2 2-2 1 1 1 3 1 4v2c-2 0-2 0-2-1-1 2-2 3-2 4h-2c-1 0-2 0-3 1-3 1-5 0-7 1v1h-2l-1-1v-2c0-1-1-3-1-5v-4l-2 1v-1l1-2c0-4-1-8-1-12 0-5-1-15 2-19h-3l1-2z" class="l"></path><path d="M539 231h0l1 1-1 1c0 2 0 4 1 6 0 1 0 2-1 2l-1 1v1 2c0-4-1-8-1-12l2-2z" class="AI"></path><path d="M541 259l1-1c-1-2-1-2-1-4v-4h0c-1-1-1-1-1-2h2v2l2-1c0 1 0 2-1 3v1 2 3 1h-2z" class="AK"></path><path d="M552 248h1c0 1-1 1-1 2h-2-1l-1 1h1v1l-1-1-1 1c0 1 0 0-1 1v-1h-1v2l-2 1v-2-1c1-1 1-2 1-3l-2 1v-2c3 0 7 1 10 0z" class="AL"></path><path d="M558 222l1 2c0 1 0 3-1 4l2 3h1c0 1-1 2 0 3l1 1-1 2c-2 0-2 0-3 1 0 1 0 2-1 3 0 1-1 7-1 8 1-1 1-2 2-2 1 1 1 3 1 4v2c-2 0-2 0-2-1-1 2-2 3-2 4h-2c-1 0-2 0-3 1-3 1-5 0-7 1v-3l2-1v-2h1v1c1-1 1 0 1-1l1-1 1 1v-1h-1l1-1h1 2c0-1 1-1 1-2h-1c-1-8 3-15 4-23 0-1 1-1 2-3z" class="j"></path><path d="M558 228l2 3h1c0 1-1 2 0 3l-4-1c0-1 1-3 1-5z" class="G"></path><path d="M557 233l4 1 1 1-1 2c-2 0-2 0-3 1 0-1-1-1-2-2l1-3z" class="D"></path><path d="M556 236c1 1 2 1 2 2s0 2-1 3c0 1-1 7-1 8 1-1 1-2 2-2 1 1 1 3 1 4v2c-2 0-2 0-2-1-1 2-2 3-2 4h-2c-1 0-2 0-3 1-3 1-5 0-7 1v-3l2-1v-2h1v1c1-1 1 0 1-1l1-1 1 1v-1h-1l1-1h1l3 3h2v-4c-1-2 0-5 0-7l1-6z" class="t"></path><path d="M543 255l2-1v-2h1v1c1 2 1 2 3 3 0-1 1-1 2-2h1l1 2c-1 0-2 0-3 1-3 1-5 0-7 1v-3z" class="j"></path><path d="M537 212c4 1 7 0 11 1v2c0 1 1 1 1 2 2 0 3 0 5-1l2 2c-1 2-1 2 0 4h0c0 1 0 2-1 3h0v2c-1 3-2 4-3 7l-2 2h0c0 3-1 5-1 9h1v1h-2v1c-2 0-2 0-3-1h-1l1-1v-5h1c0-2 0-2-1-4h0c1-1 1-1 2-1l-1-1-3-3 1-1c0-2 0-3-1-5h0c-2 0-2-1-3-2v1c0 2 0 4-1 7h0l-2 2c0-5-1-15 2-19h-3l1-2z" class="AB"></path><path d="M543 219c1 0 1 0 3 1-1 1-1 1 0 1v1h4l-1 2h1c0 1-1 1-1 2l-1 1v1l-1-1c1 0 1 0 1-1v-1c-1 0-1 1-2 1v-3c-1 1-1 1-2 1-1-2-2-3-1-5z" class="l"></path><path d="M539 214c1-1 4 0 6 0-1 1-2 3-4 3h-1-1v1c2 2 0 10-1 12l1 1-2 2c0-5-1-15 2-19z" class="AK"></path><path d="M537 212c4 1 7 0 11 1v2c0 1 1 1 1 2 2 0 3 0 5-1l2 2c-1 2-1 2 0 4-4-1-7-1-10-1v-2c1-2 1-4 0-5h-1c-2 0-5-1-6 0h-3l1-2z" class="G"></path><defs><linearGradient id="U" x1="429.6" y1="607.832" x2="442.528" y2="610.562" xlink:href="#B"><stop offset="0" stop-color="#1f1d1e"></stop><stop offset="1" stop-color="#383836"></stop></linearGradient></defs><path fill="url(#U)" d="M439 583h0c0-2 1-4 1-6 1 0 1 1 3 2h-1c0 2 1 3 1 5v17c0 3 0 6 1 8 0 8 1 18 1 26l-1 7c-1 2-2 4-4 5l-1-2v23 1 16l-1 1h-1c1-4 0-9 0-13 0-1 1-3 1-5-1-2 0-6-1-8-1-1-1-2-1-3h-3v-3h3l-1-1h-11l-2 3h-3v2l-1 1-1 1c0-3 0-5 1-8l-1-1h-3-1c0-2 0-2 1-3l1-1 3-1c0-5-1-25 1-28h1c1-1 3-2 4-3h-5l-1-1v-1c-1-1-1-6-1-8l1-1v1c2-2 1-10 1-12s-1-4 0-6h0c-2 0-4 0-5-1h-1 4l1-1c1-1 6 0 8 0l2-2h3 1 1l1 1h0 3l1 1h1v-2z"></path><path d="M420 612l3-1c2 1 2 1 4 3h-9v-1l2-1z" class="K"></path><path d="M432 590l1-2h3c1 1 1 4 1 5s-2 1-2 2c-2 0-2 0-3 1h-1l-1-1c1 0 2-1 2-1 1-1 1-3 0-4z" class="F"></path><path d="M432 610l3 1 1 2c-1 0 0 0-1 1h-8c-2-2-2-2-4-3 2 0 6-1 7 0h1l1-1z" class="S"></path><path d="M424 615h12l1 1-1 1c-2 1-6 0-8 1h-8c1-1 3-2 4-3z" class="N"></path><defs><linearGradient id="V" x1="428.349" y1="623.81" x2="415.273" y2="633.289" xlink:href="#B"><stop offset="0" stop-color="#0f0d10"></stop><stop offset="1" stop-color="#252726"></stop></linearGradient></defs><path fill="url(#V)" d="M420 618h8c-2 2-5 0-5 5v2c0 1 1 1 2 2h-1l-1 1v2c1 1 1 4 1 5l2 2c0 2-1 2-2 3h0c1 0 2-1 3-1h0c-1 1-3 3-4 3-2 1-2 4-5 4 0-5-1-25 1-28h1z"></path><defs><linearGradient id="W" x1="434.437" y1="643.898" x2="447.397" y2="578.465" xlink:href="#B"><stop offset="0" stop-color="#818082"></stop><stop offset="1" stop-color="#b2b1ae"></stop></linearGradient></defs><path fill="url(#W)" d="M439 583h0c0-2 1-4 1-6 1 0 1 1 3 2h-1c0 2 1 3 1 5v17c0 3 0 6 1 8 0 8 1 18 1 26l-1 7c-1 2-2 4-4 5l-1-2v-11-6-43-2z"></path><path d="M431 583h1 1l1 1h0 3l1 1c-1 1-2 1-4 1-2 1-2 1-2 3v1c1 1 1 3 0 4 0 0-1 1-2 1l1 1h1v1 1l3-2h1l-1 2c1 1 1 1 0 2 0 2 0 3 1 4v3h-3 0l-1 1-1 1h1v1l-1 1h-1c-1-1-5 0-7 0l-3 1-2 1c-1-1-1-6-1-8l1-1v1c2-2 1-10 1-12s-1-4 0-6h0c-2 0-4 0-5-1h-1 4l1-1c1-1 6 0 8 0l2-2h3z" class="B"></path><path d="M431 583h1 1l1 1h0 3l1 1c-1 1-2 1-4 1h-17l1-1c1-1 6 0 8 0l2-2h3z" class="H"></path><path d="M419 593v-5l1-1v1 1 2c1 5 0 11 0 17 1 1 0 2 0 4l-2 1c-1-1-1-6-1-8l1-1v1c2-2 1-10 1-12z" class="R"></path><path d="M427 603c-1-1-2-1-4 0v2c1 1 1-1 3 0-1 2-3 1-3 3l1 2h0l-2-2 1-13c0-2-1-4 1-6h4l1 2c1 0 1 0 2 1 0-1 0-2 1-3v1c1 1 1 3 0 4 0 0-1 1-2 1l1 1h1v1 1l3-2h1l-1 2c1 1 1 1 0 2 0 2 0 3 1 4v3h-3 0l-1 1-1 1h1v1h-1-5v-1-3c1-1 1-1 1-2v-1z" class="L"></path><path d="M427 603c1 0 2 0 3 1 0 1-3 4-4 5l6-5h1v1c-1 1-1 1-1 3l-1 1h1v1h-1-5v-1-3c1-1 1-1 1-2v-1z" class="W"></path><path d="M426 637c0-1 1-2 2-4 1-1 1-2 1-3h-1-1c2-1 3-3 4-4l1 1c-1 1-1 1-1 2 1 2 2 2 4 3h1v-1l2 2v-2l1-2v-1 6 11 23 1 16l-1 1h-1c1-4 0-9 0-13 0-1 1-3 1-5-1-2 0-6-1-8-1-1-1-2-1-3h-3v-3h3l-1-1h-11l-2 3h-3v2l-1 1-1 1c0-3 0-5 1-8l-1-1h-3-1c0-2 0-2 1-3l1-1 3-1c3 0 3-3 5-4 1 0 3-2 4-3h0c-1 0-2 1-3 1h0c1-1 2-1 2-3z" class="F"></path><path d="M436 654l1-1v3l-1 1h-3v-3h3z" class="V"></path><path d="M418 659c0-2-1-4 0-6h6l-2 3h-3v2l-1 1z" class="K"></path><path d="M439 628v6h0c-1 2 0 5-1 7-1 0-1-1-1-1-2-1-5 1-6 0v-2c1 0 1-1 1-2 1-1 1-1 2-1l2-3v-1l2 2v-2l1-2v-1z" class="C"></path><path d="M426 637c0-1 1-2 2-4 1-1 1-2 1-3h-1-1c2-1 3-3 4-4l1 1c-1 1-1 1-1 2 1 2 2 2 4 3h1l-2 3c-1 0-1 0-2 1 0 1 0 2-1 2-1 1-1 2-2 3h2v2h-1-1v1c0 1-1 1-2 2h-4v1c3 0 6 0 9 1 1 0 3-1 4 0l1 1c-1 1-3 1-5 1h-10l-9 1c0-2 0-2 1-3l1-1 3-1c3 0 3-3 5-4 1 0 3-2 4-3h0c-1 0-2 1-3 1h0c1-1 2-1 2-3z" class="Y"></path><path d="M422 648h2c2 0 7 0 8 1v1h-10 0 2l1-1h-2l-1-1z" class="K"></path><path d="M415 647c2 1 5 1 7 1l1 1h2l-1 1h-2 0l-9 1c0-2 0-2 1-3l1-1z" class="k"></path><defs><linearGradient id="X" x1="461.869" y1="685.772" x2="426.763" y2="705.267" xlink:href="#B"><stop offset="0" stop-color="#111112"></stop><stop offset="1" stop-color="#343435"></stop></linearGradient></defs><path fill="url(#X)" d="M424 653h11l1 1h-3v3h3c0 1 0 2 1 3 1 2 0 6 1 8 0 2-1 4-1 5 0 4 1 9 0 13h1l1-1v-16-1c5 0 9 0 14-1 1 3 0 9 0 12v5 1l1 2-2-1v4c-1 8 1 19-2 27-1 4-3 7-5 10v1c-1 2-3 5-5 7-3 2-10 3-14 3h-1c-2 0-4 0-5-1 1 0 2 1 3 0 0-3 0-7 1-10h0v-1-3-4h-1v-2c-1-1 0-2-1-3v-2-3c1-1 2-3 3-4l2-2 1-2v-1-2l-1-1h-1-1c-1 1-1 1-2 0-2 0-4 1-6 0l1-2v-1c-1-3-1-7-1-11v-23l1-1 1-1v-2h3l2-3z"></path><path d="M448 677c1-1 3-1 4 0l-1 1h-2c-1 0-3 0-4-1h3z" class="G"></path><path d="M437 693h1v-2h1v2l1 1c1-2 0-2 0-3h2c1 1 1 2 0 4h-5-1l1-1v-1z" class="V"></path><path d="M431 698c1 0 2-1 3-1h1l1 1 1 1c2 2 2 3 2 5-1-1-2-1-3-3 0-1 0 0-1-1-1-2-3-2-4-2h0z" class="C"></path><path d="M431 698c1 0 3 0 4 2 1 1 1 0 1 1 1 2 2 2 3 3v3l-1-2c-2-2-3-2-5-2v-1c-1 0-2 1-3 1v1-1l1-1h-1c-1 0-1 0-2-1v-1c1-1 2-1 3-2z" class="I"></path><path d="M428 701c1 1 1 1 2 1h1l-1 1v1-1c1 0 2-1 3-1v1c-1 1-2 1-4 3-1 2 0 2 0 4l3 4v1l-1-1c-1-1-2-1-3-1l-1-1c-1-2-1-3 0-6v-3l1-2z" class="J"></path><path d="M442 716c2-3 3-8 6-12v9 1c-1 2-2 4-3 5-1 0-3-1-3-3z" class="F"></path><path d="M430 716h6v1l-1 1v1 5l-4 1-2-9h1z" class="M"></path><path d="M438 723h-1c0-3 3-5 5-7 0 2 2 3 3 3l-1 2h1v1l-1 1c-1 1-2 3-2 5l-1 1h-1v-3l-2-1 1-1c1 0 1 0 2-1h0-3z" class="B"></path><path d="M418 694h6 1c1 1 1 1 2 0h1 5c1 2-1 3-2 4h0c-1 1-2 1-3 2v-2l-1-1h-1-1c-1 1-1 1-2 0-2 0-4 1-6 0l1-2v-1z" class="G"></path><path d="M433 703c2 0 3 0 5 2l1 2v1c0 2 1 3 0 4s-3 3-4 3h-3v-1l-3-4c0-2-1-2 0-4 2-2 3-2 4-3z" class="Q"></path><path d="M433 703c2 0 3 0 5 2l1 2v1l-2 4c-1-1-3 0-4-1-1 0-2-2-3-3l1-1c0 1 1 1 1 2 2-1 2-2 3-4-1-1 0 1-2 0h1l-1-1c-1 1-2 1-3 2h-1c2-2 3-2 4-3z" class="B"></path><path d="M438 723h3 0c-1 1-1 1-2 1l-1 1 2 1v3h1l1-1 2-4 1 2v1 1c-1 2-3 5-5 7-3 2-10 3-14 3l2-1h1c0-3-1-6 0-10l-1-1c2-1 5-1 7-1 1 0 2 0 3-1v-1z" class="R"></path><path d="M429 727l-1-1c2-1 5-1 7-1v1c-1 1-1 1-1 3 1 2 1 3 0 5v1c-1 0-2 0-3-2-1-1-1-3-1-5l-1-1z" class="B"></path><path d="M438 723h3 0c-1 1-1 1-2 1l-1 1 2 1v3h1l1-1 2-4 1 2v1 1c-1 2-3 5-5 7-1 0-2-1-3 0h0c1-2 1-2 2-3-1-2-1-4-1-6h-3v-1c1 0 2 0 3-1v-1z" class="M"></path><path d="M427 703v3c-1 3-1 4 0 6l1 1c1 1 1 2 2 3h-1l2 9 4-1h3c-1 1-2 1-3 1-2 0-5 0-7 1l1 1c-1 4 0 7 0 10h-1l-2 1h-1c-2 0-4 0-5-1 1 0 2 1 3 0 0-3 0-7 1-10h0v-1-3-4h-1v-2c-1-1 0-2-1-3v-2-3c1-1 2-3 3-4l2-2z" class="Y"></path><path d="M425 705c1 2 1 3 0 5l-1 2c-1 1-1 3-1 5-1-1 0-2-1-3v-2-3c1-1 2-3 3-4z" class="N"></path><path d="M431 686v-5-2-1-1l-1-2c0-1 1-3 1-4 0-4-1-12 1-14h1 3c0 1 0 2 1 3 1 2 0 6 1 8 0 2-1 4-1 5 0 4 1 9 0 13h1l1-1v-16l1 1c0 4-1 10 0 14v4h1 0l1 1v2h-2c0 1 1 1 0 3l-1-1v-2h-1v2h-1-1v-1c-2 0-3 0-4 1-1 0-2-1-2-1h-1v-1c-1-1-1-1-2-1h-2v4h-1c-1-1-2-2-1-3 0-1 0-1-1-1l-1-1h0l4-1c1 0 1 0 3-1l-1-1h1 3z" class="G"></path><path d="M436 668l1 5v14h-1c1 1 1 1 2 1v1c-3 0-7 0-10 1h-1-2v4h-1c-1-1-2-2-1-3 0-1 0-1-1-1l-1-1h0l4-1c1 0 1 0 3-1l-1-1h1 3c2 0 4 1 6 0l-1-1v-1c-2-4-1-12 0-16z" class="C"></path><path d="M431 686v-5-2-1-1l-1-2c0-1 1-3 1-4 0-4-1-12 1-14h1c2 1 2 4 3 6 0 2-1 3 0 5-1 4-2 12 0 16v1l1 1c-2 1-4 0-6 0z" class="E"></path><path d="M424 653h11l1 1h-3v3h-1c-2 2-1 10-1 14 0 1-1 3-1 4l1 2v1 1 2 5h-3-1l1 1c-2 1-2 1-3 1l-4 1h0l1 1c1 0 1 0 1 1-1 1 0 2 1 3h-6c-1-3-1-7-1-11v-23l1-1 1-1v-2h3l2-3z" class="B"></path><path d="M422 656l1 1 1 1h1c0 1 0 2-1 3l1 1h0c1 1 2 1 3 1v1h-2c0 1 1 2 2 2h0v1c-1 0-2 1-3 2h1c1 1 2 7 2 9h-2l-1 1 3 1-1 1v1h2c-1 1-2 1-3 1v1h1v1c-1 1-1 0-1 1h1l1 1c-2 1-2 1-3 1-1-1-1-1-2-1v-1-6c-1-6 0-12-1-18h-1l1-1v-3-2z" class="W"></path><path d="M418 659l1-1v-2h3v2 3l-1 1h1c1 6 0 12 1 18v6 1c1 0 1 0 2 1l-4 1h0l1 1c1 0 1 0 1 1-1 1 0 2 1 3h-6c-1-3-1-7-1-11v-23l1-1z" class="p"></path><path d="M664 144l2-2 1 4c1 1 3 1 5 1l10 1c2 1 5 1 6 2 5 1 9 2 13 2 1 0 1-1 2-1h0c0 1-1 1-2 2l-2 2c1 1 2 1 3 2s3 1 4 2c1 0 2 1 3 2l-1 1 3 2v-1h1 1l1-2 2-1 3 2v4c0 1 0 2-1 3h0-1l-4-3-1 1 1 1h0c1 0 2 1 2 2 0 0-1 0-2 1l-2-2-1 1c1 0 1 1 1 1 1 0 2 1 3 2 1 0 1 1 2 1 2 1 3 3 5 5l4 4h0c2 2 4 4 5 7h-1c-3-2-4-7-8-8l-1 1v-1h0v-1c0-2-2-2-3-3 0-1-1-1-1-2-5-3-8-7-14-8l-1 2h-2c1 1 1 2 2 3l-3 3c0 1 0 2-1 4 0 2-2 2 0 4-1 2-4 5-6 6h0l-2-2c-1-1 0-2 0-3 1-2 2-3 1-5h0-1c-1 1-1 2-1 4v1h0c-1 2-2 3-2 5l-8-3h-1c-1-1-2-1-3-1h-4l-2-1h-3c-1-1-2-1-3-1l-1-2v-7c0-5-1-11 0-15h1l1-1h0c-1 0-3-1-4 0v-1h-3c-2 0-2 0-4 1h-3-4l-2 1c-1 0-4 1-5 1h-2l3 19v1 3h-1l-1 1c1 1 1 1 0 2h-1l-5-18c0-2 0-3-1-4 0-1-1-1-2-1 0 0-1 1-1 2l-2 3c0-2-1-3-2-4v1l-3 2h0l-3 2v2 1c1 5 5 12 8 17h-1c-1-1-2-3-3-5-2-3-5-13-8-14l-1 1 1 3-1 1c-1-2-1-2-2-3h-1v2c1 1 2 2 2 4l1 1-1 1c-1-2-2-5-4-6-2 0-2 0-3 2-1 1-3 1-4 3h-2l-2 2-2 2-2 1-1 1-1 1-7 6c-1 2-3 4-4 5-2 3-4 5-6 6l-2-1c1-2 3-3 4-5 1-1 0-1 1-2l2-2c2-2 6-7 9-8 3-2 5-4 7-7l6-3c2-2 6-5 9-6 2-1 4-3 6-4 3-2 6-3 10-5v-1h-1l-2-1c8-5 19-7 28-7l9-1 2-1s0-1 1 0h0l1-1v-4-1h1z" class="O"></path><path d="M623 165c1 0 1 0 2-1h1 2s-1 1-1 2l-2 3c0-2-1-3-2-4z" class="F"></path><path d="M659 151c6 0 12 0 18 1-2 2-5 1-6 2l-21 1v-1h1l-1-1c1 0 3 0 4-1h-4l9-1z" class="M"></path><path d="M636 161l3 19v1 3h-1l-1 1c-1-3-2-7-2-9l-3-13c1-1 2-1 4-2z" class="P"></path><path d="M677 152c11 1 21 6 31 10l3 2c-2 0-3-1-4 0-3-1-7-3-10-4-9-4-17-5-26-6 1-1 4 0 6-2z" class="E"></path><path d="M622 159c8-5 19-7 28-7h4c-1 1-3 1-4 1l1 1h-1v1c-5 2-12 2-17 4-3 1-6 2-8 2v-1h-1l-2-1z" class="B"></path><path d="M664 144l2-2 1 4c1 1 3 1 5 1l10 1c2 1 5 1 6 2 5 1 9 2 13 2 1 0 1-1 2-1h0c0 1-1 1-2 2l-2 2c1 1 2 1 3 2s3 1 4 2c1 0 2 1 3 2l-1 1c-10-4-20-9-31-10-6-1-12-1-18-1l2-1s0-1 1 0h0l1-1v-4-1h1z" class="b"></path><path d="M659 158c16 0 34 1 49 9 1 1 1 1 2 1h1l-1-1v-1c-1-1-2-2-3-2 1-1 2 0 4 0v-1h1 1l1-2 2-1 3 2v4c0 1 0 2-1 3h0-1l-4-3-1 1 1 1h0c1 0 2 1 2 2 0 0-1 0-2 1l-2-2-1 1c1 0 1 1 1 1 1 0 2 1 3 2 1 0 1 1 2 1 2 1 3 3 5 5l4 4h0c2 2 4 4 5 7h-1c-3-2-4-7-8-8l-1 1v-1h0v-1c0-2-2-2-3-3 0-1-1-1-1-2-5-3-8-7-14-8-9-4-19-6-28-7l-2 1-3 18c0 2 1 4 1 6l-2-1h-3c-1-1-2-1-3-1l-1-2v-7c0-5-1-11 0-15h1l1-1h0c-1 0-3-1-4 0v-1z" class="U"></path><path d="M661 182l1 1h2 1c1 1 1 1 2 1 0-1 1-2 1-3s0-1 1-2v-2c1-1 0-2 0-3l1-1v-3c0-1 1-2 1-3 0-2 1-4 0-5v-1h3l-2 1-3 18c0 2 1 4 1 6l-2-1h-3c-1-1-2-1-3-1l-1-2z" class="s"></path><path d="M714 161l2-1 3 2v4c0 1 0 2-1 3h0l-1-1c-2-1-4-3-6-5h1 1l1-2z" class="c"></path><path d="M714 161l2-1 3 2v4c0 1 0 2-1 3h0l-1-1c0-1 0-2 1-3v-2c-2 0-3 1-5 0l1-2z" class="H"></path><defs><linearGradient id="Y" x1="669.961" y1="173.441" x2="699.708" y2="177.49" xlink:href="#B"><stop offset="0" stop-color="#75685e"></stop><stop offset="1" stop-color="#b2a191"></stop></linearGradient></defs><path fill="url(#Y)" d="M674 161c9 1 19 3 28 7l-1 2h-2c1 1 1 2 2 3l-3 3c0 1 0 2-1 4 0 2-2 2 0 4-1 2-4 5-6 6h0l-2-2c-1-1 0-2 0-3 1-2 2-3 1-5h0-1c-1 1-1 2-1 4v1h0c-1 2-2 3-2 5l-8-3h-1c-1-1-2-1-3-1h-4c0-2-1-4-1-6l3-18 2-1z"></path><path d="M641 515h2l24 16-2 1h0c-1 1-1 2-1 2-2 0-4 1-6 0 0 1 0 1 1 1 2 2 5 6 8 8 1 1 1 2 1 3 1 1 4 4 3 5h0c3 4 6 8 8 12 8 14 11 29 13 45h-3l-1 1c-3-3-5-6-8-7-2 0-5-1-7-1-1 0-1 0-1-1-1-1 1-3 1-4 1-3 3-4 5-6l1-1-1-1-1 1v-1l1-1v-1l-4 2c-2 2-6 3-9 5l-10-21-2-3-1 1c-3-1-9-10-11-12l1-2h0-3c-3-1-7-4-9-6l-9-4c1 0 2-1 3-2v-1c1-2 3-3 4-5l-1-1v-1l1-1c1-1 1-1 2-1 0-1 1-1 2-2h1c3-1 6-2 8-5h1l1-1c-3-1-4-2-6-3v-1c1-1 2-1 3-2 1 0 1 0 2-1v-2c1-1 1 0 1-1l-2-1z" class="B"></path><path d="M681 589h1v1c0 2 0 5-1 6-1 0-3-1-3-1l-2 2s0 1-1 1v1h-1v-1c1-1 1-2 1-3h0l1-2c2-1 3-3 5-4z" class="F"></path><path d="M677 565c3 6 5 11 7 17-1 1-6 3-6 4l-4 2v-1c2-1 2-2 3-3 1-3-1-12-2-15l2-4z" class="J"></path><defs><linearGradient id="Z" x1="630.692" y1="535.469" x2="632.105" y2="547.58" xlink:href="#B"><stop offset="0" stop-color="#3d3d3d"></stop><stop offset="1" stop-color="#545556"></stop></linearGradient></defs><path fill="url(#Z)" d="M624 544c2-2 4-5 6-6s3-1 4-2 2-2 3-2c2-1 3 0 5 1-2 1-3 2-5 3-2 3-5 6-6 9 0 1-1 2-1 3l-9-4c1 0 2-1 3-2z"></path><path d="M637 538l2 1v-1c2-1 3-1 4-1s1 0 2 1h0c2 1 4 2 5 4v4c-2 4-5 7-8 9v1h-3c-3-1-7-4-9-6 0-1 1-2 1-3 1-3 4-6 6-9z" class="D"></path><path d="M637 546c1-1 2 0 4 0h0l-2 1-1 1-1-1v-1z" class="F"></path><path d="M631 547l1 1 2-4h1c-1 1-2 2-2 3 1 2 2 3 4 4h4c2-2 3-4 4-6 0-1 0-2 1-3h4v4c-2 4-5 7-8 9v1h-3c-3-1-7-4-9-6 0-1 1-2 1-3z" class="I"></path><defs><linearGradient id="a" x1="645.733" y1="516.29" x2="656.934" y2="537.472" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#656566"></stop></linearGradient></defs><path fill="url(#a)" d="M641 515h2l24 16-2 1h0c-1 1-1 2-1 2-2 0-4 1-6 0 0 1 0 1 1 1 2 2 5 6 8 8 1 1 1 2 1 3 1 1 4 4 3 5h0c-3-3-6-7-9-10-6-5-13-10-19-15-3-1-4-2-6-3v-1c1-1 2-1 3-2 1 0 1 0 2-1v-2c1-1 1 0 1-1l-2-1z"></path><defs><linearGradient id="b" x1="653.602" y1="550.044" x2="659.386" y2="565.786" xlink:href="#B"><stop offset="0" stop-color="#696969"></stop><stop offset="1" stop-color="#858484"></stop></linearGradient></defs><path fill="url(#b)" d="M651 550c3-2 6-4 9-5 2 1 4 2 6 5l3 3 3 6c-1 1-2 1-3 2-5 2-11 5-16 8l-1 1c-3-1-9-10-11-12l1-2c3-1 7-4 9-6z"></path><path d="M651 550c3-2 6-4 9-5 2 1 4 2 6 5-1-1-2-1-4-1l1 1 2 2v1c1 1 1 2 2 4-1 1-1 2-2 3h-1c-1 1-1 1-2 1h-1c-2 0-4 0-5-1-2-2-3-5-3-8h0l-2-2z" class="U"></path><defs><linearGradient id="c" x1="660.693" y1="567.889" x2="675.141" y2="585.039" xlink:href="#B"><stop offset="0" stop-color="#343435"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#c)" d="M669 553c2 1 2 2 3 3 0 1 0 2 1 3v1h1c1 1 2 1 2 3h0l1 2-2 4c1 3 3 12 2 15-1 1-1 2-3 3v1c-2 2-6 3-9 5l-10-21-2-3c5-3 11-6 16-8 1-1 2-1 3-2l-3-6z"></path><path d="M676 563l1 2-2 4c0-1-1-2-1-2v-1-2l2-1z" class="R"></path><path d="M669 561v1c-1 1-3 2-4 3l-10 7-2-3c5-3 11-6 16-8z" class="V"></path><defs><linearGradient id="d" x1="624.569" y1="899.147" x2="627.008" y2="864.395" xlink:href="#B"><stop offset="0" stop-color="#b2aaa0"></stop><stop offset="1" stop-color="#d5ccbe"></stop></linearGradient></defs><path fill="url(#d)" d="M621 874c3-3 5-7 7-11 22 13 44 22 69 27l16 2c3 1 7 1 11 2h-44-77-46c-8 0-17 1-25 0h-1c0-2 1-7 1-9h1v2 1h2v-1h3 0c2 0 3 0 4-1 8-2 17-6 25-6 8-1 17-3 25-2 1 0 2-1 3-1-1 0-1 0 0-1h2l1 1c1-1 1-1 1-2l-2-1v-1c3 1 6 2 9 2h1 3l5 1 2 1v-1h-1 0c1-1 1-1 3-1 0 0 1 0 2 1v-2z"></path><path d="M597 873c3 1 6 2 9 2h1 3l5 1 2 1 8 2h2l3 1h-2l-15-3h-18c-1 0-1 0 0-1h2l1 1c1-1 1-1 1-2l-2-1v-1z" class="L"></path><path d="M567 880c8-1 17-3 25-2h1 1c3-1 7 0 10 0v11h1v-9h1v2h1v-1-1c-1-1-1-1-2-1v-1h2l1 1h0v10h1 0v-1-7l1-1v1 8h1c0-3-1-8 1-10v4h1v-1c0-1 1-2 2-3h0 2v1c-1 3 0 6-1 9h-1c0-3 0-6 1-9l-1-1c-1 2-1 10-1 10 0 1-2 1-3 1h-18-1v-4l-1-1v3c1 1 0 0 0 1h0-1v-6-2c-2 2 0 6-2 9-1-1 0-7 0-9h-1c0 2 0 7-1 9 0-1-1-1-1-1-1-1 0-6 0-8h-1c1 2 0 5 0 7v1h0-1v-7-1h0c0-1 0 0 1-1h1 0-3c-4-1-11 2-15 0z" class="J"></path><path d="M592 886v-5c1 0 1-1 1-1h1c0 2 0 0-1 2 0 1 0 1 1 2h0c-1 2-1 4-1 6h-1v-4z" class="e"></path><path d="M532 885h1v2 1h2v-1h3 0c2 0 3 0 4-1 8-2 17-6 25-6 4 2 11-1 15 0h3 0-1c-1 1-1 0-1 1h0v1 7h1 0v-1c0-2 1-5 0-7h1c0 2-1 7 0 8 0 0 1 0 1 1 1-2 1-7 1-9h1c0 2-1 8 0 9 2-3 0-7 2-9v2 6h1 0c0-1 1 0 0-1v-3l1 1v4l-33 2c-6 0-13-1-19 1-3 0-6 0-8 1h-1c0-2 1-7 1-9z" class="L"></path><path d="M660 143v-1l1-1v1c1 1 2 1 3 2h-1v1 4l-1 1h0c-1-1-1 0-1 0l-2 1-9 1c-9 0-20 2-28 7l2 1h1v1c-4 2-7 3-10 5-2 1-4 3-6 4-3 1-7 4-9 6l-6 3c-2 3-4 5-7 7-3 1-7 6-9 8l-2 2c-1 1 0 1-1 2-1 2-3 3-4 5l-2 3c-2 4-4 7-7 11h-1c-1 1-1 2-2 3l1 1c-1 1-2 1-3 1h-1 0c-1-2-1-2 0-4l-2-2c-2 1-3 1-5 1 0-1-1-1-1-2v-2c-4-1-7 0-11-1-1-1-1-1 0-2l-1-1v-6-5l1-1 3-4 1-1 13-15c4-5 9-12 14-16 4-3 8-6 13-8l3-1c1 0 1 0 1-1-1-2-2-3-3-5v-2h0l1 1v2l3 6c2 2 4 5 5 9l-1 1v1c13-6 25-12 39-15h1l1-1c2-1 4-1 6-1s5-1 8-2h4c1 0 2-2 2-2 3 1 4 1 7 0h2z" class="D"></path><path d="M651 143c3 1 4 1 7 0h2v3c-1 1 0 1-1 1v-2c-4 0-6 1-9 1l-20 3 1-1c2-1 4-1 6-1s5-1 8-2h4c1 0 2-2 2-2z" class="I"></path><path d="M590 171l1 1 2-1c0 1 0 2 1 3-1 0-2 1-2 2v1l-17 17-2 2c-1 0 0 0-1 1h-1c0-1-1-1-1-1h-4l3-5c2 1 3 2 4 3l1-1c-1-1-2-2-4-3l9-9c4-3 7-7 11-10z" class="Y"></path><path d="M660 143v-1l1-1v1c1 1 2 1 3 2h-1v1 4l-1 1h0c-1-1-1 0-1 0l-2 1-9 1c-9 0-20 2-28 7h-2l-2-2v-1-1l20-6c7-1 14-2 21-2 1 0 0 0 1-1v-3z" class="T"></path><path d="M541 192h2c0 3 1 4 3 6h0c0-2-1-3-2-4h0l8-10c1 2 3 3 5 4l-1 1 1 1h2l-1 1v2c-3 5-7 9-10 13l-1 1c-1 1-3 1-4 0-3-2-4-8-7-9l1-1 3-4 1-1z" class="j"></path><path d="M537 210c3-2 9-1 12-1h2c3 0 6 0 8-1v-2l7-10h4s1 0 1 1h1c1-1 0-1 1-1-2 4-4 6-6 11l2-1c-2 4-4 7-7 11h-1c-1 1-1 2-2 3l1 1c-1 1-2 1-3 1h-1 0c-1-2-1-2 0-4l-2-2c-2 1-3 1-5 1 0-1-1-1-1-2v-2c-4-1-7 0-11-1-1-1-1-1 0-2z" class="S"></path><path d="M567 207l2-1c-2 4-4 7-7 11h-1c-1 1-1 2-2 3l1 1c-1 1-2 1-3 1h-1 0c-1-2-1-2 0-4l-2-2c-2 1-3 1-5 1 0-1-1-1-1-2v-2l16-1 3-5z" class="h"></path><path d="M556 218l1-1c1 1 1 0 1 1s1 1 1 2l1 1c-1 1-2 1-3 1h-1 0c-1-2-1-2 0-4z" class="F"></path><path d="M603 162l10-5 5-2v1 1l2 2h2l2 1h1v1c-4 2-7 3-10 5-2 1-4 3-6 4-3 1-7 4-9 6l-6 3c-2 3-4 5-7 7-3 1-7 6-9 8l-2 2c-1 1 0 1-1 2-1 2-3 3-4 5l-2 3-2 1c2-5 4-7 6-11l2-2 17-17v-1c0-1 1-2 2-2-1-1-1-2-1-3l-2 1-1-1 11-7 2-2z" class="G"></path><g class="W"><path d="M590 171l11-7c2 1 2 1 3 2l1 1-13 9c0-1 1-2 2-2-1-1-1-2-1-3l-2 1-1-1z"></path><path d="M603 162l10-5 5-2v1 1c0 1 0 1 1 2-1 1-3 2-4 2-2 1-3 2-4 3-1 0-3 1-4 2l-2 1-1-1c-1-1-1-1-3-2l2-2z"></path></g><path d="M603 162c2 1 2 1 3 2l1 2-2 1-1-1c-1-1-1-1-3-2l2-2z" class="H"></path><path d="M582 144h0l1 1v2l3 6c2 2 4 5 5 9l-1 1v1c-3 2-6 5-9 7-2 2-5 5-8 6l-15 16v-2l1-1h-2l-1-1 1-1c-2-1-4-2-5-4l-8 10h0c1 1 2 2 2 4h0c-2-2-3-3-3-6h-2l13-15c4-5 9-12 14-16 4-3 8-6 13-8l3-1c1 0 1 0 1-1-1-2-2-3-3-5v-2z" class="v"></path><path d="M571 174l2-2c1 0 3-2 4-2v2l3-3c2-1 3-3 6-4 0 1-4 3-5 5v1c-2 2-5 5-8 6l1-3h0 0-2-1zm15-21c2 2 4 5 5 9l-1 1v-2c0-1-2-3-2-5l-1 1 1 2c-2 2-2 1-3 3h-1 0c-1-1-1-2-2-2l-1 3c-1-1-1-2-2-4l2-1c1-1 1-2 2-3h0l3-2z" class="I"></path><path d="M559 190c0-2 1-3 2-5-1 0-1 0-2-1h0 2l-2-2v-1l1-1c0 1 1 2 2 3 1 0 1-1 2-1h0c1-2 0-1 0-2l1-1h0l-2-2c0-1 0-2 1-2l1-2 1-1c1 1 1 2 2 2v1h1c0-1 0-1-1-2l1-1 1 1 1 1h0 1 2 0 0l-1 3-15 16v-2l1-1z" class="U"></path><path d="M582 144h0l1 1v2l3 6-3 2h0c-1 1-1 2-2 3l-2 1c1 2 1 3 2 4v2h-1c-1-1-1-2-2-4h-1c0 2 0 2 1 4-1-1-2-1-2-2-1 2 0 4 0 6h0-1l-2-3-1-1v2c0 1 1 2 1 3-1 0-2-1-2-1-2 0-2 1-3 0 1-2 3-3 4-4l2-3v-1c-4 2-5 7-9 8-1 1-2 3-3 3l-9 11c1 1 2 4 4 4h1v1h-1c-2-1-4-2-5-4l-8 10h0c1 1 2 2 2 4h0c-2-2-3-3-3-6h-2l13-15c4-5 9-12 14-16 4-3 8-6 13-8l3-1c1 0 1 0 1-1-1-2-2-3-3-5v-2z" class="B"></path><path d="M474 200c2 4 1 8 2 12v10h-1c0 1 0 2-1 3 0 1-1 2-2 2h-1v4 7l2 1v6h1c0 4 0 8 1 12 0 2-1 3-1 5v1c-1 1-2 1-2 3v2l1 1c0 1-1 1-1 3-1 1 0 5-1 8 0 2 0 2-1 4l-1 1v11 15 5 3h-2-2v-2-1l-1 1h-1l1-2c-1 0-1-1-2-2v2 6 11c-1 1-3 2-4 3h0l-1 1-3 2v3l-1 1v-17l-2 1c0-2 0-4-1-6v-11l-1-10-1-34v-3l3 1 1-1v-2h-1l1-2h-2c-2-1-4-1-7-2h0c0-2 1-3 1-4h0c-1-1-1-3-2-4 0-4 2-9 2-13 0-10 0-21-1-31v-1h1 2c1 2 2 3 4 3 2 1 3 2 5 3 1 1 3 1 4 2h4c2 1 4 1 5 1s1 0 2 1l2-1h0 1c1-1 1-2 0-3v-1-1c0-3 0-4 1-7z" class="l"></path><path d="M469 264c1 1 1 3 1 5h1c1 3-1 7 0 11 0 2 0 2-1 4l-1 1v-21z" class="w"></path><path d="M463 308c1 0 2-1 3-2 0 4-1 10 1 13h-2v-2-1l-1 1h-1l1-2c-1 0-1-1-2-2 1-1 1-2 2-3l-1-2z" class="f"></path><path d="M463 293l3-2v15c-1 1-2 2-3 2v-3h0v-3c1-3 1-6 0-9zm8-55l2 1v6h1c0 4 0 8 1 12 0 2-1 3-1 5v1c-1 1-2 1-2 3v2l1 1c0 1-1 1-1 3-1 1 0 5-1 8-1-4 1-8 0-11h-1c0-2 0-4-1-5v-15l1-1c1-3 0-6 1-10z" class="AC"></path><path d="M471 238l2 1v6h0c-1 2-1 9 0 11 0 2-1 4-2 6 0-3 1-10-1-12v2l-1-3 1-1c1-3 0-6 1-10z" class="AD"></path><path d="M474 200c2 4 1 8 2 12v10h-1c0 1 0 2-1 3 0 1-1 2-2 2h-1v4 7c-1 4 0 7-1 10l-1-8-1-3v-4l1 1v-14-4h0l-1 1c0 1 0 0-1 1l1 2c0 3 0 8-1 11 0 2 0 4-1 5v3c0 1-1 2-1 2v2c1 1 0 2 0 3-1-1-1-1-1-2l1-1-1-1v3 2h0 1v1 1 2c0 1 1 2 1 3v2l-1 2v-1c-1-1-2-2-4-2h-2l3-5h1c0-2 0-2-1-3h-6 0l-1-1 1-2v-2-1h1l3-3 1-4h0c1-2 1-5 1-8-1-2-1-4 0-6 1 0 1-1 2-2-1-3-2-5-5-7h4c2 1 4 1 5 1s1 0 2 1l2-1h0 1c1-1 1-2 0-3v-1-1c0-3 0-4 1-7z" class="c"></path><path d="M459 211h4v2 1c2 2 3 3 4 5l-2 6-1-7c-1-3-2-5-5-7z" class="AE"></path><path d="M463 250l1 1c0 1 0 1-1 2 1 1 2 1 3 1v2l-1 2v-1c-1-1-2-2-4-2h-2l3-5h1 0z" class="J"></path><path d="M474 200c2 4 1 8 2 12v10h-1c0 1 0 2-1 3 0 1-1 2-2 2h-1v4 7c-1 4 0 7-1 10l-1-8 1-27 2-1h0 1c1-1 1-2 0-3v-1-1c0-3 0-4 1-7z" class="AF"></path><path d="M474 200c2 4 1 8 2 12v10h-1c-2-1-2-1-2-3l1-1v-1h-1c0 1 0 1-1 3l-1-1v-2l1-1c-1-1-1-1-1-2l1-2h1c1-1 1-2 0-3v-1-1c0-3 0-4 1-7z" class="I"></path><path d="M462 220c1 0 1-1 2-2l1 7 2-6c1 5 0 10-1 14l-3 8v4 3 2h0c0-2 0-2-1-3h-6 0l-1-1 1-2v-2-1h1l3-3 1-4h0c1-2 1-5 1-8-1-2-1-4 0-6z" class="J"></path><path d="M463 238v1c0 1-1 1-1 2-1 2 0 2 1 4v3 2h0c0-2 0-2-1-3s-1-2-1-3v-4h0l2-2z" class="AE"></path><path d="M456 244v-2-1h1c0 1 0 1 1 1l3-2v4c0 1 0 2 1 3h-6 0l-1-1 1-2z" class="s"></path><path d="M462 220c1 0 1-1 2-2l1 7c0 4 0 7-2 11v2l-2 2h0l-3 2c-1 0-1 0-1-1l3-3 1-4h0c1-2 1-5 1-8-1-2-1-4 0-6z" class="P"></path><path d="M462 226v6l1 1v3 2l-2 2h0l-3 2c-1 0-1 0-1-1l3-3 1-4h0c1-2 1-5 1-8z" class="m"></path><defs><linearGradient id="e" x1="455.292" y1="227.749" x2="442.234" y2="228.315" xlink:href="#B"><stop offset="0" stop-color="#6f665f"></stop><stop offset="1" stop-color="#918c85"></stop></linearGradient></defs><path fill="url(#e)" d="M444 203h2c1 2 2 3 4 3 2 1 3 2 5 3 1 1 3 1 4 2 3 2 4 4 5 7-1 1-1 2-2 2-1 2-1 4 0 6 0 3 0 6-1 8h0l-1 4-3 3h-1v1 2l-1 2 1 1h0 6c1 1 1 1 1 3h-1l-3 5h-6l-1 1v2h-2c-2-1-4-1-7-2h0c0-2 1-3 1-4h0c-1-1-1-3-2-4 0-4 2-9 2-13 0-10 0-21-1-31v-1h1z"></path><path d="M457 235h1l2-1h1l-1 4-3 3h-1v1 2-2c-1-1-1-2-1-4 1-1 0-2 1-4l1 1zm0-23v1c1 3 4 3 4 7-1 1-2 1-3 2v-1h-2c-1-2-1-4-1-6h2v-3z" class="j"></path><path d="M444 203h2c1 2 2 3 4 3 2 1 3 2 5 3 1 1 3 1 4 2 3 2 4 4 5 7-1 1-1 2-2 2l-3 3-1-1c1-1 2-1 3-2 0-4-3-4-4-7v-1h0c-2-3-6-4-9-6-2-1-2-2-5-2v-1h1z" class="G"></path><path d="M458 222l1 1 3-3c-1 2-1 4 0 6 0 3 0 6-1 8h0-1l-2 1h-1l-1-1h-1c-1-1 0-3 0-5 0-1 0-1 1-2v-4l2-1z" class="I"></path><path d="M456 227l2 1v4l-3-3c0-1 0-1 1-2z" class="s"></path><path d="M455 229l3 3c0 1 0 2-1 3l-1-1h-1c-1-1 0-3 0-5z" class="v"></path><path d="M462 247c1 1 1 1 1 3h-1l-3 5h-6l-1 1v2h-2c-2-1-4-1-7-2h0c0-2 1-3 1-4h0l1-1c3-1 5-1 9-1v-2-1h2 6z" class="s"></path><path d="M462 247c1 1 1 1 1 3h-1c-2 0-6 0-7-1l-1-2h2 6z" class="t"></path><path d="M443 256h0c0-2 1-3 1-4l4 2c1 1 2 1 3 1v1h1v2h-2c-2-1-4-1-7-2z" class="G"></path><path d="M459 255h2c2 0 3 1 4 2v1l1 3v30l-3 2c1 3 1 6 0 9v3h0v3l1 2c-1 1-1 2-2 3v2 6 11c-1 1-3 2-4 3h0l-1 1-3 2v3l-1 1v-17l-2 1c0-2 0-4-1-6v-11l-1-10-1-34v-3l3 1 1-1v-2h-1l1-2v-2l1-1h6z" class="j"></path><path d="M459 289v-1c1-1 2-3 2-5 2 2 2 5 1 7v1l-1-2h-2z" class="s"></path><path d="M461 255c2 0 3 1 4 2v1l1 3h0c-1 2-1 4-1 6h-1l-1-1v-1-1-1h-1l-1-1h0v-1-3c-1-1 0-2 0-3z" class="AL"></path><path d="M466 261v30l-3 2c1-2 0-6 1-9v-2-1-1c-1-1-1-1-2-1h0l-1-2h3v-2l-2-1 1-1h1c-1-1 0-1-1-1v-1c1 0 1 0 1-2h-1l-1-1 2-1h1c0-2 0-4 1-6h0z" class="w"></path><path d="M459 289h2l1 2v5 10c-2 0-4 0-6-1h0c-1-2-1-5-1-7v-6c1-2 2-2 3-3h1z" class="AE"></path><path d="M458 289c-2 3-1 4-1 7v2c-1 2-1 5-1 7-1-2-1-5-1-7v-6c1-2 2-2 3-3z" class="s"></path><path d="M459 255h2c0 1-1 2 0 3-1 1-2 3-3 4 0 2 1 5 0 8 0 1-2 2-3 3v-1c-1 0-2-1-3-2 0-1 0-5 1-6 0-1-1-2-1-2v-2h-1l1-2v-2l1-1h6z" class="y"></path><path d="M454 262l2-1v1c0 1 1 1 1 3v4s0 1-1 2c-1-1-1-2-1-4v-1-1c0-1 0-2-1-3z" class="j"></path><path d="M454 262c1 1 1 2 1 3v1 1c0 2 0 3 1 4l-1 1c-1 0-2-1-3-2 0-1 0-5 1-6l1-2z" class="s"></path><path d="M452 262s1 1 1 2c-1 1-1 5-1 6 1 1 2 2 3 2v1 1h-1v1h1v1h0c-1 0-1 0-1 1-1 2-1 7 0 9v2 3c1 0 1 0 1 1v6c0 2 0 5 1 7h0v2h6c0 1 0 1-1 1v7h1v6 11c-1 1-3 2-4 3h0l-1 1-3 2v3l-1 1v-17l-2 1c0-2 0-4-1-6v-11l-1-10-1-34v-3l3 1 1-1z" class="m"></path><path d="M450 309c0 1 2 3 2 4v4c0 1 0 1-2 3v-11z" class="t"></path><path d="M453 299c0-3-1-8 1-11v3c1 0 1 0 1 1v6 2h-1l-1-1z" class="P"></path><path d="M455 298c0 2 0 5 1 7h0v2 8c-1-2-1-4-1-6h-1c0-1-1-1-1-2s1-2 0-3v-5l1 1h1v-2z" class="t"></path><path d="M454 309h1c0 2 0 4 1 6l1 8c0 2 0 5 1 7v2c-1 1-1 2 0 3h0l-1 1-3 2v3l-1 1v-17l1-1c-1-2 0-6 0-9v-6z" class="j"></path><path d="M454 315c2 3 2 11 2 14h0c-2-1 0-2-1-3 0-1-1-1-1-2-1-2 0-6 0-9z" class="AL"></path><path d="M456 307h6c0 1 0 1-1 1v7h1v6 11c-1 1-3 2-4 3-1-1-1-2 0-3v-2c-1-2-1-5-1-7l-1-8v-8z" class="AB"></path><path d="M461 315h1v6 11c-1 1-3 2-4 3-1-1-1-2 0-3h0l1 1 1-1v-2-12c0-2 0-2 1-3zm-5-8h6c0 1 0 1-1 1-1 1-2 1-3 3l-1 5h1c0 1 1 1 1 2-1 4 0 8-1 12-1-2-1-5-1-7l-1-8v-8z" class="AE"></path><path d="M408 145c8 2 15 3 22 6 4 1 6 4 10 4 3 1 10 0 13-1s10-1 13 0h5c1-1 2-1 3-2 5 1 9 2 14 4 4 1 8 3 12 6 3 3 6 5 8 9v1h-2-1-1c0 1-1 1-2 1 2 2 2 2 2 4l1 2c-1 4-1 9-2 13v4c-1 0-1 0-1 1l-1 2h-1c1 1 0 1 1 2h0l-2 1h-4-5v2l-1 2h-2c-1-1-1-1-1-2s-1-1-2-2h-1l-3-1c-2 2-1 5-1 7v1h-1 0c-1 0-1 0-2 1v2c-1-4 0-8-2-12-1 3-1 4-1 7v1 1c1 1 1 2 0 3h-1 0l-2 1c-1-1-1-1-2-1s-3 0-5-1h-4c-1-1-3-1-4-2-2-1-3-2-5-3-2 0-3-1-4-3h0v-1h0c2-3 0-5 1-7s1-2 0-4l-1-1v-1-3h0l-1-3 2-2v-1h-2c1-2 3 0 4-1l-1-1 1-1 1 1 1-1v-1l-2-2c1-1 1 0 1-2l-1 1-1-1v-1l-1-1-2-2h0c-1-1-2-1-3-3 0 1-1 1-2 1l-1-1c-2 0-4 0-5 1-1-1-1-2-2-3l-1 1v-2l-1-1c0 1-1 1-1 2-1-2-1-4-1-5-2-1-2-2-3-3l1-1c-1 0-1 0-2 1-1 0-2-1-3-2l1-1c-1 0-2 0-3-1l-1-1h1l-1-1-2 1c-1 1-2 1-3 1v-1l-2-1c-1 0-2-1-3-2-1 0-1 0-1-1l1-1z" class="V"></path><path d="M408 147h4c0 1 1 2 1 3l-2-1c-1 0-2-1-3-2z" class="h"></path><path d="M460 160c0-1 1-2 2-3h3l1 1-1 2h0l-1-1c-1 0-3 1-4 1z" class="C"></path><path d="M428 154h1c1 0 1-1 2-1 1 1 1 3 3 2h1c1 2 1 3 2 4h2v-1c1-1 2-1 3-1-1 1-1 1-2 3h-2c-2-1-3-1-3-3-1-1-5 0-6-2v-1h-1z" class="G"></path><path d="M474 152c5 1 9 2 14 4v1c0 1 0 1-1 1-5-2-11-3-16-4 1-1 2-1 3-2z" class="I"></path><path d="M426 154h2 1v1c1 2 5 1 6 2 0 2 1 2 3 3h2l1 1c-1 1-4 1-6 1l-3 1-1 1v-2l-1-1c0 1-1 1-1 2-1-2-1-4-1-5-2-1-2-2-3-3l1-1z" class="D"></path><path d="M488 156c4 1 8 3 12 6 3 3 6 5 8 9v1h-2-1-1c-4-6-11-10-17-14 1 0 1 0 1-1v-1z" class="G"></path><defs><linearGradient id="f" x1="456.24" y1="160.475" x2="456.297" y2="169.015" xlink:href="#B"><stop offset="0" stop-color="#4a4643"></stop><stop offset="1" stop-color="#6f6962"></stop></linearGradient></defs><path fill="url(#f)" d="M442 157c4 1 8 1 12 0 1 1 1 2 1 3h5c1 0 3-1 4-1l1 1-2 5-4 5c-2 1-2-1-4-1v1h-1c-1 2 0 3 0 5-2-2-5-6-9-7-1-1-2-1-3-3 0 1-1 1-2 1l-1-1c-2 0-4 0-5 1-1-1-1-2-2-3l3-1c2 0 5 0 6-1l-1-1c1-2 1-2 2-3z"></path><path d="M435 162c2 1 2 1 4 1l2-1 1 1v-1c1 1 1 0 2 1 1 2 2 2 4 3s3 2 5 3h2v1h-1c-1 2 0 3 0 5-2-2-5-6-9-7-1-1-2-1-3-3 0 1-1 1-2 1l-1-1c-2 0-4 0-5 1-1-1-1-2-2-3l3-1z" class="J"></path><defs><linearGradient id="g" x1="459.383" y1="191.639" x2="476.087" y2="186.683" xlink:href="#B"><stop offset="0" stop-color="#80817a"></stop><stop offset="1" stop-color="#af9e8e"></stop></linearGradient></defs><path fill="url(#g)" d="M463 165s1 0 2-1v1 2c-1 1-1 2-1 3v1 2 1 3l3 3 2 3h-1c4 5 7 9 12 12-1 2-1 3-2 4l1 2h1c-2 2-1 5-1 7v1h-1 0c-1 0-1 0-2 1v2c-1-4 0-8-2-12l-4-4c-1-1-2-2-2-4l-2 1c-2-1-2-1-3-3-3-5-7-7-10-11-1-1-2-1-3-1h0l1-1v-1l-2-2c1-1 1 0 1-2l-1 1-1-1v-1l-1-1-2-2h0c4 1 7 5 9 7 0-2-1-3 0-5h1v-1c2 0 2 2 4 1l4-5z"></path><path d="M479 201h1c-2 2-1 5-1 7v1l-2-3v-3h1l1-2z" class="Y"></path><path d="M463 165s1 0 2-1v1 2c-1 1-1 2-1 3v1 2 1 3l3 3 2 3h-1l-9-10-4-3v-1c2 0 2 2 4 1l4-5z" class="D"></path><path d="M455 169c2 0 2 2 4 1 2 0 3 1 4 1v5c-1-1-2-3-4-3l-4-3v-1z" class="M"></path><path d="M445 168c4 1 7 5 9 7 5 6 10 11 14 17l-2 1c-2-1-2-1-3-3-3-5-7-7-10-11-1-1-2-1-3-1h0l1-1v-1l-2-2c1-1 1 0 1-2l-1 1-1-1v-1l-1-1-2-2h0z" class="D"></path><path d="M445 183l2-2v-1h-2c1-2 3 0 4-1l-1-1 1-1 1 1h0c1 0 2 0 3 1 3 4 7 6 10 11 1 2 1 2 3 3l2-1c0 2 1 3 2 4l4 4c-1 3-1 4-1 7v1 1c1 1 1 2 0 3h-1 0l-2 1c-1-1-1-1-2-1s-3 0-5-1h-4c-1-1-3-1-4-2-2-1-3-2-5-3-2 0-3-1-4-3h0v-1h0c2-3 0-5 1-7s1-2 0-4l-1-1v-1-3h0l-1-3z" class="I"></path><path d="M468 208l-1-1v-1c-1-1 0-1-1-2 2-1 2-1 3-3v6l-1 1z" class="v"></path><path d="M466 193l2-1c0 2 1 3 2 4v2c-1 1-1 1-2 1h0c0-1-1-2-2-3l-1 1h-1c1-1 2-3 2-4z" class="G"></path><path d="M470 196l4 4c-1 3-1 4-1 7v1 1c1 1 1 2 0 3h-1 0l-2 1c-1-1-1-1-2-1 0 0-1 0-1-1l1-1v-2l1-1c1-3 0-5 1-8h0v-1-2z" class="D"></path><path d="M449 203h0c1 0 2 0 3 1l3 1v-1c-1 0-2-1-3-2 1-1 1-2 1-3 0-2 1-2 3-3l1 1-1 1v3c1 0 2-2 3-3v5l-2 2h3c1 0 2 1 2 1h2c1 0 1 2 2 2h1c0 1-1 2-2 2-7-1-12-6-19-7v-1l3 1z" class="L"></path><path d="M445 183l2-2v-1h-2c1-2 3 0 4-1l-1-1 1-1 1 1h0c1 0 2 0 3 1 3 4 7 6 10 11-1 1-1 2-2 3 0 2-2 3-2 5-1 1-2 3-3 3v-3l1-1-1-1c-2 1-3 1-3 3 0 1 0 2-1 3 1 1 2 2 3 2v1l-3-1c-1-1-2-1-3-1h0l-3-1h0c2-3 0-5 1-7s1-2 0-4l-1-1v-1-3h0l-1-3z" class="E"></path><path d="M446 186v-1c1-1 2-1 2-3 0-1 0 0 1-1h0v2h1c-1 1-1 2-1 3s-1 2-1 3c-1 1 0 1-1 2l-1-1v-1-3h0z" class="P"></path><path d="M447 191c1-1 0-1 1-2 0-1 1-2 1-3s0-2 1-3v8h1l1-1c1 1 1 4 2 4v1h-2c1 1 1 2 0 3 0 1-1 1-2 1v1l1 1-2 2-3-1h0c2-3 0-5 1-7s1-2 0-4z" class="Q"></path><path d="M464 173c1-4 2-10 5-13 1-1 3-1 5-1 3 0 7 1 9 2l1 1 15 8 3 3c2 2 2 2 2 4l1 2c-1 4-1 9-2 13v4c-1 0-1 0-1 1l-1 2h-1c1 1 0 1 1 2h0l-2 1h-4-5v2l-1 2h-2c-1-1-1-1-1-2s-1-1-2-2h-1l-3-1h-1l-1-2c1-1 1-2 2-4-5-3-8-7-12-12h1l-2-3-3-3v-3-1z" class="m"></path><path d="M469 183c4 4 12 12 17 12l2 2c-3 0-5-1-8-2-5-3-8-7-12-12h1z" class="G"></path><path d="M480 195c3 1 5 2 8 2h7 7l-1 2h-1c1 1 0 1 1 2h0l-2 1h-4-5v2l-1 2h-2c-1-1-1-1-1-2s-1-1-2-2h-1l-3-1h-1l-1-2c1-1 1-2 2-4z" class="AB"></path><path d="M483 202l1-1 2-1c3 0 6 0 9 1v1h-5-6-1z" class="AK"></path><path d="M484 202h6v2l-1 2h-2c-1-1-1-1-1-2s-1-1-2-2z" class="q"></path><path d="M495 197h7l-1 2h-1c1 1 0 1 1 2h0l-2 1h-4v-1l-1-1h-2 0l1-1c1 0 1-1 2-2z" class="AI"></path><path d="M417 506v-4-3h0c0 1 1 1 1 2l1 1c1 1 2 7 2 9 1 3 2 5 5 7l2 1 1 1h-1v1l-1 1c2 0 5-1 6 0 2 2 2 1 4 2h3c2 1 2 2 3 3v1c1 1 2 1 4 1h1c0 1 0 2 1 3v12 9 1h-6v4c0 4-2 13 1 16v1 2c-1 3-1 5-1 7 0-2-1-3-1-5h1c-2-1-2-2-3-2 0 2-1 4-1 6h0v2h-1l-1-1h-3 0l-1-1h-1-1-3l-2 2c-2 0-7-1-8 0l-1 1h-4v-1c0-1 0-3 1-4v2h2v-4-2h0v-1h-2c0 1 0 1-1 2 0-1 0-2 1-3 1 0 3-1 5-1v-6c-1-3 0-6 0-9-8-5-16-9-23-15-3-3-7-4-11-6l-9-6h1l-1-1c-2-1-2-2-2-3 1 0 3 1 5 1l1 1h4l-1-1c-2-1-3-2-4-4l1-1h0l1-1c0-1 0-1 1-2v1c1 0 2 0 2-1v-1l1-1h0l-1-1 1-4 1-2h3v1l1 1h1v-3-3-2c-1-1 0-2 0-3h3 1c2 0 7 0 9 1v1h1v-2h2v-1c2 1 3 1 5 1 0 2 0 3 1 4v4h1c0-1 1-1 1-2l1 1 1-1v-3z" class="D"></path><path d="M425 556h9v1h-2v6h2c-1-1 0-1-1-1l1-1c0 1 0 1 1 3h-3v1h2v7h-12l3-3h1v-2c0-3-1-5 0-8h1l-1-1-1-2z" class="U"></path><path d="M426 540l1-2c1-2 1-2 3-2l1 1h-1-1l-1 1h6v9c0 3 0 6 1 8l-1 1h-9 0v-2 1l1-1 1-2-1-4v-1c1-2 0-5 0-7z" class="J"></path><path d="M426 548h5v1c0 1 0 2 1 3v1h2l-1 2c-1 0-1-1-2-1h-1l1-1c0-2 0-2-1-3-1 0-2 1-3 2l-1-4z" class="g"></path><path d="M427 552c1-1 2-2 3-2 1 1 1 1 1 3l-1 1h1c1 0 1 1 2 1h2l-1 1h-9 0v-2 1l1-1 1-2z" class="Y"></path><path d="M422 540h2 2c0 2 1 5 0 7v1l1 4-1 2-1 1v-1 2h0l1 2 1 1h-1c-1 3 0 5 0 8v2h-1l-3 3v1h-2v-4-3c1-2 1-5 1-7v-17-1l1-1z" class="F"></path><path d="M422 540h2l2 1v3c-1 1-2 1-2 2l-2 1c-1-2-1-3-1-5v-1l1-1z" class="V"></path><path d="M426 558l-2-1c0 1-1 2-1 3h0l-1 1c0-3 0-5 1-8 1 0 1-1 1-1v-1l-1-1s-1 0-1-1 0-1 1-2h3v1l1 4-1 2-1 1v-1 2h0l1 2z" class="B"></path><path d="M420 569v4h2c2-1 11 0 14 0 0 1 1 1 1 1v1c-2 1-6 1-8 2 1 2 2 4 2 6h-3l-2 2c-2 0-7-1-8 0l-1 1h-4v-1c0-1 0-3 1-4v2h2v-4-2h0v-1h-2c0 1 0 1-1 2 0-1 0-2 1-3 1 0 3-1 5-1v-6l1 1z" class="N"></path><path d="M416 583h2l1-1-1-1v-2l2-1h1c0 1-1 3 0 4 2-1 1-3 1-5l1 2v1l1 1h1 0v2h3l-2 2c-2 0-7-1-8 0l-1 1h-4v-1c0-1 0-3 1-4v2h2z" class="a"></path><path d="M427 522c2 0 5-1 6 0 2 2 2 1 4 2h3c2 1 2 2 3 3v1c1 1 2 1 4 1h1c0 1 0 2 1 3v12 9 1h-6v4c0 4-2 13 1 16v1 2c-1 3-1 5-1 7 0-2-1-3-1-5h1c-2-1-2-2-3-2 0 2-1 4-1 6h0l-1-50 1-6c-6-1-14 0-20 0-2 0-9 2-11 1l1-1 4-2c1-1 1-2 3-2 3-1 8 0 11-1z" class="N"></path><path d="M427 522c2 0 5-1 6 0 2 2 2 1 4 2-8 0-16-1-24 1 1-1 1-2 3-2 3-1 8 0 11-1z" class="R"></path><path d="M443 528c1 1 2 1 4 1h1c0 1 0 2 1 3v12 9 1h-6v-16c-1-3-1-6 0-10zm-57-16h3v1l1 1h1l1 1h0v2c1-1 1-2 3-2 0 2 0 3 1 4 1 2 2 2 3 2s2 1 2 1h2c2 1 3 1 5 1v-2h1v6l-1 1c2 1 9-1 11-1 6 0 14-1 20 0l-1 6v2h-4v3h-6l1-1h1 1l-1-1c-2 0-2 0-3 2l-1 2h-2-2v-1l-1-1h0c0-2 1-4 0-6l-1 1v5c-2 1-2 0-3 1v-4-1c0-1 0-2-1-3s-1-1-3-1c-1 1-1 1-2 1h-1-4c-2-1-5-1-8-1h-12-2l-1-1c-2-1-3-2-4-4l1-1h0l1-1c0-1 0-1 1-2v1c1 0 2 0 2-1v-1l1-1h0l-1-1 1-4 1-2z" class="F"></path><path d="M384 521l1 1c1 1 3 1 4 3v1c-3-1-5-1-6-3 0 0-1 0-1-1 1 0 2 0 2-1zm8-4c1-1 1-2 3-2 0 2 0 3 1 4-1 1-1 2-1 2-1 1-4 1-4 1h-1c-1 0-2 0-3-1h0c0-1 0-1 1-2l1 1c1 0 2 0 3-1v-2z" class="G"></path><path d="M386 512h3v1l1 1h1l1 1h0v2 2c-1 1-2 1-3 1l-1-1c-1 1-1 1-1 2h0l-2-2h0l-1-1 1-4 1-2z" class="P"></path><path d="M386 512h3v1l1 1h1l1 1c-3 0-5 0-7-1l1-2z" class="M"></path><path d="M426 530h1 2c0-1 1-1 2 0l2-1c1 2 1 4 1 6v3h-6l1-1h1 1l-1-1c-2 0-2 0-3 2l-1 2h-2-2v-1h0c1-1 1-1 1-2 2-2-1-5 1-7h2z" class="L"></path><path d="M422 539c1-1 1-1 1-2 2-2-1-5 1-7h2c0 2 1 8-1 10l-3-1z" class="D"></path><path d="M417 506v-4-3h0c0 1 1 1 1 2l1 1c1 1 2 7 2 9 1 3 2 5 5 7l2 1 1 1h-1v1l-1 1c-3 1-8 0-11 1-2 0-2 1-3 2l-4 2v-6h-1v2c-2 0-3 0-5-1h-2s-1-1-2-1-2 0-3-2c-1-1-1-2-1-4-2 0-2 1-3 2v-2h0l-1-1v-3-3-2c-1-1 0-2 0-3h3 1c2 0 7 0 9 1v1h1v-2h2v-1c2 1 3 1 5 1 0 2 0 3 1 4v4h1c0-1 1-1 1-2l1 1 1-1v-3z" class="P"></path><path d="M395 512h7c1 0 1 0 2 1l1-1v1h-1l1 1-1 1h-2l-1-1c-1-1-1-1-2-1l-2 2h-2v-3z" class="D"></path><path d="M417 509l1 3c0 1 1 1 1 2l1 1c0 1 1 2 1 3v1l-3-3h-1 0c-2 0-3-1-4-2l1-1h0c1 1 1 1 2 1l2 1v-1c-2-1-3-1-4-3 0-1 1-1 1-2l1 1 1-1z" class="C"></path><path d="M391 503h3l2 1c-1 1-1 1-2 1l1 1c3 0 4 1 6-1 1 1 0 1 1 3 1 0 1 1 1 2-1 1 0 1-2 1s-5 0-7 1h1v3c-2 0-2 1-3 2v-2h0l-1-1v-3-3-2c-1-1 0-2 0-3z" class="F"></path><path d="M391 511h1v-2c2-1 0 0 2 0 1 0 4 0 6-1 1 1 1 1 1 3-2 0-5 0-7 1h1v3c-2 0-2 1-3 2v-2h0l-1-1v-3z" class="B"></path><path d="M394 512h1v3c-2 0-2 1-3 2v-2-2c1 0 1 0 2-1h0z" class="O"></path><path d="M405 514h0c2 0 3-2 4-3v-1h0c2 1 2 1 2 3 1 2 2 3 3 4v1c1 0 2 1 3 2v-1-1c-1 0 0 0-1-1l3 2c2 0 5 0 7-1l2 1 1 1h-1v1l-1 1c-3 1-8 0-11 1-2 0-2 1-3 2l-4 2v-6h-1v2c-2 0-3 0-5-1h-2s-1-1-2-1-2 0-3-2c-1-1-1-2-1-4h2l2-2c1 0 1 0 2 1l1 1h2l1-1z" class="E"></path><path d="M414 518c1 0 2 1 3 2v-1-1c-1 0 0 0-1-1l3 2c2 0 5 0 7-1l2 1 1 1h-1v1l-1 1c-3 1-8 0-11 1-2 0-4 0-5-1s-1-2-2-3c1 1 2 1 3 1l-1-2c2 1 3 1 4 2h1l-2-2h0z" class="F"></path><path d="M374 528c1 0 3 1 5 1l1 1h4 2 12c3 0 6 0 8 1h4 1c1 0 1 0 2-1 2 0 2 0 3 1s1 2 1 3v1 4c1-1 1 0 3-1v-5l1-1c1 2 0 4 0 6h0l1 1v1l-1 1v1 17c0 2 0 5-1 7v3l-1-1c-1-3 0-6 0-9-8-5-16-9-23-15-3-3-7-4-11-6l-9-6h1l-1-1c-2-1-2-2-2-3z" class="B"></path><path d="M406 531h4c0 1 0 0-1 1 0 1 0 1-1 1l-2-2z" class="D"></path><path d="M406 545h0c-3 0-6-3-9-4l-1-1c-2-1-3-3-4-4v-1h2l1-1v1 2c1 2 1 1 2 2l9 6z" class="M"></path><path d="M376 532h1l8 3c2 0 7 2 8 4l1 1c1 1 2 2 2 4-3-3-7-4-11-6l-9-6z" class="E"></path><path d="M397 539c2 0 4-1 5 0h1 2c4 1 10 1 14 2v10c-2-3 1-7-2-8-2-1-4-1-6-1-2-1-4-1-6-1 2 2 6 3 7 4 2 3 4 5 6 7-2 0-3-1-5-2s-5-5-7-5l-9-6z" class="F"></path><defs><linearGradient id="h" x1="666.335" y1="602.208" x2="716.692" y2="595.636" xlink:href="#B"><stop offset="0" stop-color="#464850"></stop><stop offset="1" stop-color="#bdb9b1"></stop></linearGradient></defs><path fill="url(#h)" d="M652 508c2 2 4 4 6 5 8 5 16 11 23 17l1 2c2 0 4 1 5 3h1 0c5 4 9 9 11 15 2 2 3 4 5 6v1c1 2 1 3 3 4l-1 1h1 0c1 0 3-1 4 0 1 0 2 0 2 1l1-8c1 2 2 4 1 6v2 2-1c1 1 1 0 1 1-1 1-1 2-2 3 0 2 1 4-1 6h1c1 2 1 3 0 5v1c1 1 0 4 1 6 1 1 1 1 1 2v3 1c1-3 1-6 0-8 0-4 1-8 0-11 0-3 0-5 1-7 1 9 0 17 0 26v42 29c0 4 1 9 0 14v8l-7-26c-4-9-7-17-11-26-1-2-2-5-4-7 0-1-2-3-2-4-1-1 0-3 0-4 0-3-1-6-1-9v-1c-2-16-5-31-13-45-2-4-5-8-8-12h0c1-1-2-4-3-5 0-1 0-2-1-3-3-2-6-6-8-8-1 0-1 0-1-1 2 1 4 0 6 0 0 0 0-1 1-2h0l2-1-24-16c4-2 7-4 9-7z"></path><path d="M703 584h0c-1-1-1-2-2-3 0-1 0-2-1-2 0 2 2 4 2 6h-1l-1-1-1-4c0-3-2-5-2-7-2-3-4-6-5-9-1-2-1-5-3-7l1-1c4 6 6 12 9 18 1 3 3 6 4 10z" class="e"></path><path d="M710 659h0l3 7v2c2-3 3-5 3-8v-1c1-2 1-7 0-9 0-1 0-3 1-4h0v20 11 8l-7-26z" class="b"></path><path d="M699 550c2 2 3 4 5 6v1c1 2 1 3 3 4l-1 1h1c0 1 1 4 2 5s1 0 2 1c-1 0-1 0-2 1h1c1 1 1 1 2 1-1 1-1 0-2 0l-1 1c1 1 2 1 3 2h0l-2 1v1h2l-1 1v1h1v1h-1c0 1 1 1 2 2-1 0-1 0-1 1v7c-1-3-2-6-2-9-2-5-4-13-7-18h-1l-2-5 1-1c-1-1-2-3-2-5z" class="P"></path><path d="M699 550c2 2 3 4 5 6v1 3c-1 0-1 0-2-1l-1-4c-1-1-2-3-2-5z" class="I"></path><path d="M652 508c2 2 4 4 6 5 8 5 16 11 23 17l1 2c2 0 4 1 5 3h1 0c5 4 9 9 11 15 0 2 1 4 2 5l-1 1 2 5-1-1c-1-2-2-3-3-5l-1-2c0-1-2-3-3-5l-1-1c0-1 0-1-1-1h0c1 1 1 1 1 2-1 0-1 0-2 1l1 1 2 4c1 2 3 4 4 6 1 3 1 5 2 8 2 4 4 8 5 13l1 1c2 5 3 11 4 16h-1c-1 2-1 2 0 4v2c-1-1-1-2-1-3-1-5-3-12-5-17-1-4-3-7-4-10-3-6-5-12-9-18-1-2-3-4-4-5-5-8-12-14-19-20l-24-16c4-2 7-4 9-7z" class="b"></path><path d="M682 532c2 0 4 1 5 3h1 0c5 4 9 9 11 15 0 2 1 4 2 5l-1 1c-5-9-11-17-18-24z" class="E"></path><path d="M404 383c2 0 3 0 4-1l1-1 1 1 3-3h2 0 1c-1 1-1 1 0 2s3 1 5 1v21c-1 2-1 3-1 5v3c0 6 1 13 0 20-2 0-3 0-3 2-1 1-1 3-1 4l1 1h0 3c-1 2-2 3-2 4l-11 11c-4 3-6 6-11 7 0 1-1 2-1 3v9h-1c0-1 0-1-1-2-1 0-2 0-3-1h-4 0c-1-2-1-6-1-8h-1c-2 0-3 0-5-2h-1c2-1 2-2 3-3l-6-3c-2-2-3-3-4-5l3-1 1-1c1 0-1-1-1-2v-2c-3-2-6-5-10-7v-1l1-1v-6-14-12-1-2l2-2c0-2 0-2-1-4h0v-4c1 0 1-1 2-1s1 0 2-1c2-1 4-1 6-2 9-2 19-1 28-1z" class="d"></path><defs><linearGradient id="i" x1="390.25" y1="378.459" x2="371.974" y2="395.777" xlink:href="#B"><stop offset="0" stop-color="#413e3d"></stop><stop offset="1" stop-color="#767879"></stop></linearGradient></defs><path fill="url(#i)" d="M370 386c2-1 4-1 6-2 9-2 19-1 28-1v1l-1 1c-2 1-4 2-6 2-5 1-10 0-15 1s-9 4-13 3h-2l-1 1h0v-4c1 0 1-1 2-1s1 0 2-1z"></path><path d="M404 384l-1 1c-2 1-4 2-6 2 0-1-1-2-1-3h-3 11z" class="P"></path><defs><linearGradient id="j" x1="424.881" y1="401.95" x2="400.89" y2="387.684" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#404142"></stop></linearGradient></defs><path fill="url(#j)" d="M404 383c2 0 3 0 4-1l1-1 1 1 3-3h2 0 1c-1 1-1 1 0 2s3 1 5 1v21c-1 2-1 3-1 5v3c-1 0-1 0-1 1-1-1-2-2-3-4-1 0-1-2-2-2h-2c-4 0-6 1-9-1-4 0-8-1-11 0v-1c2-2 5 0 8-2-2 0-7 1-8-1v-3c2-3 3-5 5-8l3-3 3-2 1-1v-1z"></path><path d="M397 390l3-3 2 1c-1 1-1 2-2 2l-1 1h-1l-1-1z" class="R"></path><path d="M400 402h14c2 0 3 0 5 1-1 1-1 0-1 1h-6c-3 1-7 0-10 0h-1c1 1 7 0 7 1h-5c-4 0-8-1-11 0v-1c2-2 5 0 8-2h0z" class="F"></path><path d="M397 390l1 1h1c1 2 1 2 2 3l2 1v1c-1 0-2-1-3-1h0c1 2 2 2 3 4l-3 3h0c-2 0-7 1-8-1v-3l5-8z" class="Q"></path><defs><linearGradient id="k" x1="420.347" y1="423.857" x2="373.374" y2="427.079" xlink:href="#B"><stop offset="0" stop-color="#29292b"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#k)" d="M392 405c3-1 7 0 11 0 3 2 5 1 9 1h2c1 0 1 2 2 2 1 2 2 3 3 4 0-1 0-1 1-1 0 6 1 13 0 20-2 0-3 0-3 2-1 1-1 3-1 4l1 1h0 3c-1 2-2 3-2 4l-11 11c-4 3-6 6-11 7 0 1-1 2-1 3v9h-1c0-1 0-1-1-2-1 0-2 0-3-1h-4 0c-1-2-1-6-1-8h-1c-2 0-3 0-5-2h-1c2-1 2-2 3-3l-6-3c-2-2-3-3-4-5l3-1 1-1c1 0-1-1-1-2v-2c3 1 11 2 13 1 1-1 1-4 1-5h2v-1h-2c0-1 0-1 1-2h2c2-1 0-4 1-5h1 4 1 0c-2-1-3-1-5-1h-1v-1h0c2 0 4-1 6-1v-1c-2 0-4 1-6 0 1-2 4-1 6-1-1-1-5-1-6-1 0-1 0-2 1-4 1 1 3 1 5 0h-6v-1c2 0 4 0 6-1l-6-1v-6c2 0 4 1 6 0-1-2-4 0-6-1l1-1h5v-1c-2 0-4 1-6 0v-3z"></path><path d="M406 439c2-1 8-1 11-1h3c-1 2-2 3-2 4l-11 11c-4 3-6 6-11 7 0 1-1 2-1 3v9h-1c0-1 0-1-1-2-1 0-2 0-3-1h-4 0c-1-2-1-6-1-8h-1c-2 0-3 0-5-2h-1c2-1 2-2 3-3l-6-3c-2-2-3-3-4-5l3-1c2 0 3 0 5 1 1-1 1-2 1-2l2-1c3 2 7 2 10 2s3-1 4-3h0l1-1h2v-1h-2 0 1 2c1 0 3-1 3-2l1-1c1 0 0 0 1 1l-1 2h1c1-1 1-1 1-3z" class="E"></path><path d="M374 447c2 0 3 0 5 1s5 2 6 4v5h-1l-3-1-6-3c-2-2-3-3-4-5l3-1z" class="d"></path><path d="M406 439c2-1 8-1 11-1h3c-1 2-2 3-2 4l-11 11c-4 3-6 6-11 7h0c-1-2-1-4-1-6l1-1c2 0 3 0 4 1v1h-1c-1 1 0 1 0 3 1 0 2-1 3-1 0-2 0-3 1-4v-3h-1v-1l-1 1v4c-1-2-1-2-3-2-1 0-1 0-2 1l-1-1c0-3 1-5 1-8h0l1-1h2v-1h-2 0 1 2c1 0 3-1 3-2l1-1c1 0 0 0 1 1l-1 2h1c1-1 1-1 1-3z" class="Q"></path><path d="M407 440c2-1 3-1 6 0l1 1c1-1 1-1 2-1-1 3-5 4-7 5v-1-1h1c1 0 2-1 3-2-2 1-3 1-5 1-1 0-1-1-1-2z" class="E"></path><path d="M396 444h0l1-1h2v-1h-2 0 1 2c1 0 3-1 3-2l1-1c1 0 0 0 1 1l-1 2h1c1-1 1-1 1-3l1 1c0 1 0 2 1 2 2 0 3 0 5-1-1 1-2 2-3 2h-1v1 1h-1v-1h-3v1c0 2-2 1 0 3h0c-1 1-2 1-2 2h-1v-1l-1 1v4c-1-2-1-2-3-2-1 0-1 0-2 1l-1-1c0-3 1-5 1-8z" class="R"></path><path d="M583 475h2c3 2 5 2 9 2h0c2 0 4 0 5 2 3 1 5 3 6 6 1-3-3-4-4-7v-2h2c3 0 5 0 7 1h4c2 1 4 1 6 2h1c9 2 14 5 21 11l1-1c1 2 2 3 2 4 2 1 4 2 6 2 1 0 2-1 3-1h0v1h4v1l-2 2h1 2l-1 1 3 3h6l-1 1h-10c2 1 3 1 5 1v1h-1c-1 0-2 0-4 1h-1c-2-1-3-1-5-1l1 3v-1l1 1c-2 3-5 5-9 7h-2l2 1c0 1 0 0-1 1v2c-1 1-1 1-2 1-1 1-2 1-3 2v1c2 1 3 2 6 3l-1 1h-1c-2 3-5 4-8 5h-1c-1 1-2 1-2 2-1 0-1 0-2 1l-1 1v1l1 1c-1 2-3 3-4 5v1c-1 1-2 2-3 2-4-2-12-6-17-6-9 4-16 11-26 15l-1 1v-13l-2-1v2c-1 1-2 2-2 3h-1v-7l-2-1-1-1v-3-5c-2-3-5-1-7-2h2l11-1c4 0 7 1 11 2l1-1-1-2c-1 0-2-1-4-1h-2l2-1v-1h3 0-2c1-1 0-1 1-1s0 0 1-1h4c-1-2-1-3-1-6 1-1 1-4 1-6l-1-1 1-1-1-1v-2h3 1v-1-2c-3 0-7 1-9 0h-1c-1 1-1 0-2 0-3 0-5 1-8 1h-5v2c-1-1-2-2-2-3l-1 1h0c0-2 1-4 0-6v-1h2c2-1 3-2 5-2s3 0 5-1h-1v-1h1c1-2 1-2 1-3h1 1c2 0 3-1 5-1l1-1h0c1 1 2 2 3 2-1-5-1-8-4-12l-1-1z" class="G"></path><path d="M621 505h7 19v1h-1-4c-1 2-1 2 0 4h-1-8v-1h0l-1-2c-2 1-6 1-9 1-1-1-1-2-2-3z" class="R"></path><path d="M647 505h3l1 3v-1l1 1c-2 3-5 5-9 7h-2 0l-1-1h0l-1-1 1-1h-5l1-1h3 1l1-1h1c-1-2-1-2 0-4h4 1v-1z" class="E"></path><path d="M631 501c5 0 10-1 15-1 1 0 4 0 5-1h0v-1h1v2h4c1 0 1-1 2-1l3 3h6l-1 1h-10-20c-5 0-12 2-17 0v-1c1 0 2 0 2-1h10z" class="W"></path><path d="M617 521v-1c2-5 3-10 4-15 1 1 1 2 2 3 3 0 7 0 9-1l1 2h0v1h8l-1 1h-1-3l-1 1h5l-1 1c-3 0-7 1-10 0l-1 1c-1 1-1 0-1 2h1c-1 1-3 3-4 3-2 1-4 0-6 1l-1 1z" class="C"></path><path d="M617 521v-1c2-5 3-10 4-15 1 1 1 2 2 3 3 0 7 0 9-1l1 2h0v1c-2 0-6 0-7 1l-2-1c-1 1-1 2-2 4l-4 6-1 1z" class="P"></path><path d="M620 479h1c9 2 14 5 21 11l1-1c1 2 2 3 2 4 2 1 4 2 6 2 1 0 2-1 3-1h0v1h4v1l-2 2h1 2l-1 1c-1 0-1 1-2 1h-4v-2h-1v1h0c-1 1-4 1-5 1-5 0-10 1-15 1h-10v-3l-1-6c2 0 2 0 3-1v-2h-1c-1-3-1-5-3-8h0-1v-1h2v-1z" class="L"></path><path d="M621 498c1-1 1-2 3-3h1 2v4l-1 1h5v1h-10v-3z" class="R"></path><path d="M627 499c1-1 0-1 1-2h0 1v1h1c1-1 1-1 1-2 2-1 4-1 6-1l1 3s1 1 2 0h3v-1h-1v-1l2-1v-1l1-1c2 1 4 2 6 2 1 0 2-1 3-1h0v1h4v1l-2 2h1 2l-1 1c-1 0-1 1-2 1h-4v-2h-1v1h0c-1 1-4 1-5 1-5 0-10 1-15 1v-1h-5l1-1z" class="U"></path><path d="M620 479h1c9 2 14 5 21 11l1 1c0 1 1 1 1 2-1 0-2-1-3 0h-12-1c-1-1-1-1-2-1h0l-4-7c-1-2-1-3-3-4h0-1v-1h2v-1z" class="T"></path><path d="M631 486v-2c2 0 3 2 4 3l1 1h0-1v1l-2-1v1h-1l-1-3z" class="k"></path><path d="M620 479h1c9 2 14 5 21 11l1 1-1 1v-1l-1 1c-2-1-3-2-5-4h0l-1-1c-1-1-2-3-4-3v2l-3-2c-1-1-2-1-3-1s-2 2-3 2c-1-2-1-3-3-4h0-1v-1h2v-1z" class="a"></path><path d="M620 479h1v1l4 3c-1 0-2 2-3 2-1-2-1-3-3-4h0-1v-1h2v-1z" class="K"></path><path d="M603 476c3 0 5 0 7 1h4c2 1 4 1 6 2v1h-2v1h1 0c2 3 2 5 3 8h1v2c-1 1-1 1-3 1l1 6v3c0 1-1 1-2 1v1c-2 2-2 8-3 11s-2 6-4 8c0-1 1-2 1-4h-1c0 1-1 2-1 3l-4 4h-1c0-1 1-3 2-4h0-1-1-1c-3-1-5-1-8-1h-1 0c1-1 1-2 1-4h0v-3l-1 1v4h-1v-5c0-2 0-4 2-6-2 0-4 1-5 1-1-1-1-1-2-1-1 1-1 1-1 2l-1-1 1-1-1-1v-2h3 1v-1-2h21c1-1 0-2 0-3v-3c-1-9-2-13-10-19z" class="E"></path><path d="M597 520l1-1v-2h1c2 1 3 1 4 1 2 0 3 1 4 1h2c0 1 0 1-1 2h0-1-1-1c-3-1-5-1-8-1z" class="C"></path><path d="M597 507h1l1 2h-1v2c1 1 2 0 3 0v-1l2-2h1l3 1v1h1v1c-1 1-1 1-2 1-1 1-1 1-1 2-1-1-1-1-1-2h-1c0 1 0 2 1 3l-2 1c-1-1 0-3-1-4-1 0-2 1-2 2v2h-1-1v-3l-1 1v4h-1v-5c0-2 0-4 2-6z" class="Q"></path><path d="M592 503l16 1c1 0 4-1 4 0h1v2l-2 2h-1-6-1l-2 2v1c-1 0-2 1-3 0v-2h1l-1-2h-1c-2 0-4 1-5 1-1-1-1-1-2-1-1 1-1 1-1 2l-1-1 1-1-1-1v-2h3 1v-1z" class="B"></path><path d="M613 495c1-1 1-2 1-3 1-1 0-3-1-4v-4c-1-1-2-2-1-3h0l3 6c0 1 0 2 1 3 0 1 0 1 1 2l1-3 1 2h-1c-1 2 0 8 0 10l1 1v1c-2 2-2 8-3 11s-2 6-4 8c0-1 1-2 1-4h1c0-2 1-4 2-6 0-3 2-6 0-9-1 0-1 0-2 1 0 4 0 7-1 11l-1-1 1-8v-2h-1c0-1-3 0-4 0l-16-1v-2h21c1-1 0-2 0-3v-3z" class="Y"></path><path d="M603 476c3 0 5 0 7 1h4c2 1 4 1 6 2v1h-2v1h1 0c2 3 2 5 3 8h1v2c-1 1-1 1-3 1l1 6v3c0 1-1 1-2 1l-1-1c0-2-1-8 0-10h1l-1-2-1 3c-1-1-1-1-1-2-1-1-1-2-1-3l-3-6h0c-1 1 0 2 1 3v4c1 1 2 3 1 4 0 1 0 2-1 3-1-9-2-13-10-19z" class="a"></path><path d="M618 489c-1-3-2-7-4-10 3 3 6 9 6 13l1 6v3c0 1-1 1-2 1l-1-1c0-2-1-8 0-10h1l-1-2z" class="C"></path><path d="M583 475h2c3 2 5 2 9 2h0c2 0 4 0 5 2 3 1 5 3 6 6 1-3-3-4-4-7v-2h2c8 6 9 10 10 19v3c0 1 1 2 0 3h-21c-3 0-7 1-9 0h-1c-1 1-1 0-2 0-3 0-5 1-8 1h-5v2c-1-1-2-2-2-3l-1 1h0c0-2 1-4 0-6v-1h2c2-1 3-2 5-2s3 0 5-1h-1v-1h1c1-2 1-2 1-3h1 1c2 0 3-1 5-1l1-1h0c1 1 2 2 3 2-1-5-1-8-4-12l-1-1z" class="J"></path><path d="M591 480c1 1 2 3 3 5h0c1 1 1 3 1 4v1l-4-1-1-7 1-2zm-6 6h0c1 1 2 2 3 2v2c-3 1-6 0-8-1l-1 2h-3c1-2 1-2 1-3h1 1c2 0 3-1 5-1l1-1z" class="H"></path><path d="M590 496v1c-1 1-2 1-4 1h-4-9-3c-1 0-3-1-4-1l1-1c1 0 2 1 3 1v-1c3 0 8 1 11-1h1v1h1c2 1 3 1 4 1s1-1 2-1h1z" class="L"></path><path d="M566 495l4 1v1c-1 0-2-1-3-1l-1 1c1 0 3 1 4 1h3c0 1 0 3-1 4h-5v2c-1-1-2-2-2-3l-1 1h0c0-2 1-4 0-6v-1h2z" class="B"></path><path d="M570 498h3c0 1 0 3-1 4h-5v-1l1-2h2v-1z" class="E"></path><path d="M585 475c3 2 5 2 9 2h0c2 0 4 0 5 2v1l1 1-1 1h-1-2l-1 1-3-3h-1l-1 2-1-1h0l1 3h0v5c-2-2-2-5-2-7-1-3-2-4-3-7z" class="a"></path><path d="M594 477c2 0 4 0 5 2v1l1 1-1 1h-1c0-1-1-1-1-2-1 0-2-1-3-1h0v-2z" class="k"></path><path d="M599 479c3 1 5 3 6 6 1 0 1 0 1 1l-2 2-1 1h-4-2l-1-1c-1-2-1-3-1-5l1-1h2 1l1-1-1-1v-1z" class="p"></path><path d="M590 496l-1-4 1-1h1c0 1 1 2 1 4 0 1 0 2 1 3h20c0 1 1 2 0 3h-21c-3 0-7 1-9 0h-1c-1 1-1 0-2 0-3 0-5 1-8 1 1-1 1-3 1-4h9 4c2 0 3 0 4-1v-1z" class="C"></path><path d="M601 476h2c8 6 9 10 10 19v3h-20c-1-1-1-2-1-3 0-2-1-3-1-4h7 11c-2-2-3-2-6-2l1-1 2-2c0-1 0-1-1-1 1-3-3-4-4-7v-2z" class="U"></path><path d="M601 478c3 0 5 1 6 3 2 2 4 5 4 8v1h-1l-1 1c-2-2-3-2-6-2l1-1 2-2c0-1 0-1-1-1 1-3-3-4-4-7z" class="k"></path><path d="M609 491l1-1c1 1 1 2 1 4v1h0-1 0c-2 0-5 0-7-1h-5c-2 0-3-1-5-1h-1v2h0c0-2-1-3-1-4h7 11z" class="Q"></path><defs><linearGradient id="l" x1="600.748" y1="520.719" x2="601.466" y2="537.917" xlink:href="#B"><stop offset="0" stop-color="#050404"></stop><stop offset="1" stop-color="#2e2e2f"></stop></linearGradient></defs><path fill="url(#l)" d="M592 508c1 0 3-1 5-1-2 2-2 4-2 6v5h1v-4l1-1v3h0c0 2 0 3-1 4h0 1c3 0 5 0 8 1h1 1 1 0c-1 1-2 3-2 4v1c2 0 2 0 3-1h1c2 0 5 0 6-1h-2v-1c1 0 3 0 4-1h-2l1-1 1-1c2-1 4 0 6-1 1 0 3-2 4-3h-1c0-2 0-1 1-2l1-1c3 1 7 0 10 0l1 1h0l1 1h0l2 1c0 1 0 0-1 1v2c-1 1-1 1-2 1-1 1-2 1-3 2v1c2 1 3 2 6 3l-1 1h-1c-2 3-5 4-8 5h-1c-1 1-2 1-2 2-1 0-1 0-2 1l-1 1v1l1 1c-1 2-3 3-4 5v1c-1 1-2 2-3 2-4-2-12-6-17-6-9 4-16 11-26 15l-1 1v-13l-2-1v2c-1 1-2 2-2 3h-1v-7l-2-1-1-1v-3-5c-2-3-5-1-7-2h2l11-1c4 0 7 1 11 2l1-1-1-2c-1 0-2-1-4-1h-2l2-1v-1h3 0-2c1-1 0-1 1-1s0 0 1-1h4c-1-2-1-3-1-6 1-1 1-4 1-6 0-1 0-1 1-2 1 0 1 0 2 1z"></path><path d="M580 535v2 1h-2v-6c1 0 2 1 3 1l-1 2z" class="O"></path><path d="M615 533h3l-1 1h1c0 1 1 1 1 1 1 1 0 1 1 2h2l-2 2c-1-1-3-2-4-3 0-1 1-1-1-3zm-34 0c1-1 1-1 3-1h0l2 2c1 0 1 1 2 2h-1v1l-1 1v-1h-2v-2l-1 1v1l-1-1c0-1 0-1-2-1l1-2z" class="B"></path><path d="M582 525c2-1 3-1 4-1h0 9c1-1 0-1 1-1h1c2-1 3-1 5-1h1 2v1c-2 0-4 1-5 1-5 2-9 0-14 1v1c-1 0-2-1-4-1z" class="F"></path><path d="M572 540l19-1-3 2c-4 0-7 1-11 0h-3v1h1v2c-1 1-2 2-2 3h-1v-7z" class="V"></path><path d="M596 518v-4l1-1v3h0c0 2 0 3-1 4h0 1c3 0 5 0 8 1h1c-1 1-2 1-4 1s-3 0-5 1h-1c-1 0 0 0-1 1h-9 0c-1 0-2 0-4 1h-2l2-1v-1h3 0-2c1-1 0-1 1-1s0 0 1-1h4 0c1 0 1 1 2 0 1 0 1-1 1-1l1-1h1v2l1 1v-2s1-1 1-2zm32-4h2v1c-1 1-1 2-1 3h1c1 0 0 0 2 1h0c1 0 2 0 3 1h0c1 0 2 0 4-1 0 1 1 1 1 1-1 1-2 1-3 2v1l-2-1 1-1h-2c-2 2-3 2-5 2l2-2-13 1h-2l1-1 1-1c2-1 4 0 6-1 1 0 3-2 4-3h-1c0-2 0-1 1-2z" class="B"></path><path d="M628 514l1-1c3 1 7 0 10 0l1 1h0l1 1h0l2 1c0 1 0 0-1 1v2c-1 1-1 1-2 1 0 0-1 0-1-1-2 1-3 1-4 1h0c-1-1-2-1-3-1h0c-2-1-1-1-2-1h-1c0-1 0-2 1-3v-1h-2z" class="C"></path><path d="M628 514l1-1c3 1 7 0 10 0l1 1h0l1 1c-2 1-3 2-5 2h-4c-1-1-1 0-2 0v-2-1h-2z" class="L"></path><path d="M630 515l2-1h0c2 1 2 1 4 0l1 1v1l-1 1h-4c-1-1-1 0-2 0v-2z" class="Z"></path><path d="M592 508c1 0 3-1 5-1-2 2-2 4-2 6v5h1c0 1-1 2-1 2v2l-1-1v-2h-1l-1 1s0 1-1 1c-1 1-1 0-2 0h0c-1-2-1-3-1-6 1-1 1-4 1-6 0-1 0-1 1-2 1 0 1 0 2 1z" class="C"></path><path d="M589 509c0-1 0-1 1-2 1 0 1 0 2 1s1 3 1 5c0 1-1 1-1 2v2c0 1 0 1-1 2h0v-1-1c-1 0-2 1-2 1l-1-3c1-1 1-4 1-6z" class="L"></path><defs><linearGradient id="m" x1="577.585" y1="551.006" x2="624.028" y2="536.818" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient></defs><path fill="url(#m)" d="M595 539v-1s1-1 2-1v2c4-2 9-4 13-6 2 0 3-1 4 0-1 1-1 2-1 4 2 1 3 3 5 4h1 1c2-1 3-2 5-4l1-1c0 1 0 1-1 2v1h0l2-2 1 1c-1 2-3 3-4 5v1c-1 1-2 2-3 2-4-2-12-6-17-6-9 4-16 11-26 15l-1 1v-13l-2-1h-1v-1h3c4 1 7 0 11 0l-1 1c3-1 6-2 8-3z"></path><path d="M595 539h1l-14 9c-2 2-4 3-4 6v1l-1 1v-13l-2-1h-1v-1h3c4 1 7 0 11 0l-1 1c3-1 6-2 8-3z" class="F"></path><path d="M595 539h1l-14 9v-1l1-1v-1l-2 1-1-1h-2v-2h3c2-1 4-1 6-1h0l8-3z" class="V"></path><path d="M649 159h3c2-1 2-1 4-1h3v1c1-1 3 0 4 0h0l-1 1h-1c-1 4 0 10 0 15v7l1 2c1 0 2 0 3 1h3l2 1h4c1 0 2 0 3 1h1l8 3c0-2 1-3 2-5h0v-1c0-2 0-3 1-4h1 0c1 2 0 3-1 5 0 1-1 2 0 3l2 2h0c2-1 5-4 6-6-2-2 0-2 0-4 1-2 1-3 1-4l3-3c-1-1-1-2-2-3h2l1-2c6 1 9 5 14 8 0 1 1 1 1 2 1 1 3 1 3 3v1h0v1l1-1c4 1 5 6 8 8h1c1 1 1 2 1 3s1 1 1 2h1c1-1 1-2 2-2 3 6 6 12 6 20 2 10 0 22-4 32-1 0 0 0-1-1-2 0-3 0-4-1h-1c-1-1-2-1-3-2-2 0-3-2-5-2-1 0-1 1-1 1v-1l-1-1c-1-1-2-1-4-1l-12-4c-2 0-3 0-4-1h-6c-1 0-1 0-1 1l-1-1c-2-2-5 0-7-1h0c-1 0-2 0-3 1l-1-1c0-1 1-1 0-2l-1 1c-1-1-1-1-2-1l-4-3c-2-1-11-5-12-7h-2l-1-1c-1-3-1-7-1-11l-2-15-1-1c0-2 0-4-1-6h-2l-2-10-2-16z" class="Z"></path><path d="M690 224c1 2 2 4 4 4h1l2 1-1 1-1-1-1 1h-1-2-4c-1-1-1-1-2-1v1h-1 0l-1-1 1-1h2l1-2c1 0 2-1 3-2z" class="J"></path><path d="M675 216v-3c1-1 1-2 1-3v-1c1 0 2 1 2 1v1l3 3-1 1h1c0 1 0 1-1 2 0 0 0 1-1 1l-1 1v-1h-1c-1-1-2-1-2-2z" class="v"></path><path d="M678 194l5 2 6 3v1c3 1 5 5 7 8 1-2 2-3 3-4h0l-2 4-5 4h0c0-1-1-2 0-3h1 1c-1-2-2-5-3-6 0 0-1-1-2-1-1-1-2-2-3-2v1l-2-2h1c-1-1-4-1-6-1l-1-1v-3z" class="h"></path><path d="M697 208c-1 2-1 3-1 6 1 0 1 0 3 1h-1c-1 1-1 2-3 3l1 1c-1 3-2 4-4 6v1-1l3 3h-1c-2 0-3-2-4-4l4-4c-1-2-1-2-3-4v-1-1c1-1 1-1 1-2l5-4z" class="Y"></path><path d="M674 193c2 0 3 0 4 1v3l1 1c2 0 5 0 6 1h-1c-1 0-2 1-3 1v1c2 0 3 2 5 2l1 1-1 1c-3-2-4-3-7-4-1 1-1 2-1 3l-1 1 1 1c2 1 4 2 6 4h0c-3-1-4-2-6-3h-1c1 2 2 2 4 3l2 1v1l-2 1h0c0-2-2-2-3-3 0 0-1-1-2-1v1c0 1 0 2-1 3v3c-1 2-3 4-5 4h-1 0-1c1-1 1-1 0-2-1 0-1 0-2 1l1 1h0l-2-1v-1c2-2 3-2 6-2v1l1-1v-1c-1-1-1-2-2-3 0 0 0-1-1-1h-1l1-1c1 0 2 1 3 1h2c-1-1-1-1-2-1s-2-1-3-1h0c-1-1-2-1-4-2l1-1 1 1c3 0 1-1 3-2h5 0c-2-1-4-1-5-1l-1-1c1-1 0-1 1-1h0c0-1-1-1-1-2h-1v-3h1l1 1c1 0 2 0 3-1h-1c0-2 1-3 2-4z" class="L"></path><path d="M659 159c1-1 3 0 4 0h0l-1 1h-1c-1 4 0 10 0 15v7l1 2v7h0 2c1 0 2 1 3 1h2c1 1 3 0 4 1h1c-1 1-2 2-2 4h1c-1 1-2 1-3 1l-1-1h-1v3h1c0 1 1 1 1 2h0c-1 0 0 0-1 1l1 1c1 0 3 0 5 1h0-5c-2 1 0 2-3 2l-1-1-1 1c2 1 3 1 4 2h0c1 0 2 1 3 1s1 0 2 1h-2c-1 0-2-1-3-1l-1 1h1c1 0 1 1 1 1 1 1 1 2 2 3v1l-1 1v-1c-3 0-4 0-6 2v1l2 1h0l-1-1c1-1 1-1 2-1 1 1 1 1 0 2h1l1 2h1c1 1 1 1 4 2v2c-2-1-11-5-12-7h-2c0-1 1-1 1-2v-8c-1-2 0-5-1-7-2-13 0-27-2-41v-2z" class="Q"></path><path d="M649 159h3c2-1 2-1 4-1h3v1 2c2 14 0 28 2 41 1 2 0 5 1 7v8c0 1-1 1-1 2l-1-1c-1-3-1-7-1-11l-2-15-1-1c0-2 0-4-1-6h-2l-2-10-2-16z" class="J"></path><path d="M656 158h3v1 2c-2 3-3 5-2 9h-1v-2l-1 8c-1-5-1-12-1-17l2-1z" class="s"></path><path d="M649 159h3c2-1 2-1 4-1l-2 1c0 5 0 12 1 17s1 11 2 16l-1-1c0-2 0-4-1-6h-2l-2-10-2-16z" class="G"></path><path d="M686 190c0-2 1-3 2-5h0v-1c0-2 0-3 1-4h1 0c1 2 0 3-1 5 0 1-1 2 0 3l2 2h0c2-1 5-4 6-6l1 1c1 1 3 1 4 2-1 2-2 3-3 4h-1l2 2c-1 1-2 1-3 2v1h0l3 3 1 1c1 0 1 1 2 2h0l1 1 1 1-1 1c-2-1-1-1-3-1h-2 0c-1 1-2 2-3 4-2-3-4-7-7-8v-1l-6-3-5-2c-1-1-2-1-4-1h-1c-1-1-3 0-4-1h-2c-1 0-2-1-3-1h-2 0v-7c1 0 2 0 3 1h3l2 1h4c1 0 2 0 3 1h1l8 3z" class="AB"></path><path d="M698 185c1 1 3 1 4 2-1 2-2 3-3 4h-1 0l-2 2c-2 0-2-1-2-2 0-2 1-3 2-4s1-1 2-1v-1z" class="S"></path><path d="M662 184c1 0 2 0 3 1h3l2 1h4c1 0 2 0 3 1h1l8 3c3 1 8 3 10 6-2 0-5-3-7-4-2 1-2 2-2 4 0 1 1 2 2 3l-6-3-5-2c-1-1-2-1-4-1h-1c-1-1-3 0-4-1h-2c-1 0-2-1-3-1h-2 0v-7z" class="Z"></path><path d="M683 196v-2c-1-1-1-3-2-4h0l1-1c2 1 4 1 7 3-2 1-2 2-2 4 0 1 1 2 2 3l-6-3z" class="J"></path><path d="M699 204h2v1c0 1 1 2 2 2 0 1-1 2-1 3l-1 3h0c1 0 1 0 2-1h0c0 1 0 2 1 2v3l1 1c1-3 0-6 3-8-1 0-1-1-1-1 1-1 1-2 2-2 1 1 0 0 2 1v-1c1 1 2 1 2 2h1 1 2c-1 1-1 2-2 2v1h1c0 1 0 0-1 1 0 1-1 2-1 3v1l3 3c0-1 0-2 2-3v1c0 2 1 4 0 6l-1 3h-1 0l3 5-4-1c-1-1-2-1-3-1s-2-1-3-1c0 2 2 3 2 5l-10-2c-2-2-6-1-8-2l1-1 1 1 1-1-2-1-3-3v1-1c2-2 3-3 4-6l-1-1c2-1 2-2 3-3h1c-2-1-2-1-3-1 0-3 0-4 1-6l2-4z" class="c"></path><path d="M706 219l2 1h1c0-1 0-1 1-2 1 0 1 0 2 1v1c-1 1-1 4-2 5v1c-1 1-1 1 0 3h0c0 2 2 3 2 5l-10-2v-4c-1-1-1-1-2-1h-1v-1h1c0-1 1-1 1-2s0-2 1-2v-1l1-1c1-1 1-1 3-1z" class="X"></path><path d="M702 228c1 0 0 0 1-1h1l1 1 1 1c2-2 0-1 1-3 0-1 1-1 1-3v-1h1c0 2 0 2 1 3v1c-1 1-1 1 0 3h0c0 2 2 3 2 5l-10-2v-4z" class="S"></path><path d="M708 210c-1 0-1-1-1-1 1-1 1-2 2-2 1 1 0 0 2 1v-1c1 1 2 1 2 2h1 1 2c-1 1-1 2-2 2v1h1c0 1 0 0-1 1 0 1-1 2-1 3v1l3 3c0-1 0-2 2-3v1c0 2 1 4 0 6l-1 3h-1 0l3 5-4-1c-1-1-2-1-3-1s-2-1-3-1h0c-1-2-1-2 0-3v-1c1-1 1-4 2-5v-1c-1-1-1-1-2-1-1 1-1 1-1 2h-1l-2-1c0-3 1-6 2-9z" class="T"></path><path d="M735 193c3 6 6 12 6 20 2 10 0 22-4 32-1 0 0 0-1-1-2 0-3 0-4-1h-1c-1-1-2-1-3-2-1-2-5-3-7-4s-4-2-6-2c-1-1-2-1-3-1 0-2-2-3-2-5 1 0 2 1 3 1s2 0 3 1l4 1-3-5h0 1l1-3c1-2 0-4 0-6v-1c-2 1-2 2-2 3l-3-3v-1c0-1 1-2 1-3 1-1 1 0 1-1h-1v-1c1 0 1-1 2-2 0-1 0-1 1-1s3-1 4-2l5-5h3c0-1 1-2 1-2l-2-2v-1c2 0 2 0 4 1v-1l-1-1h1c1-1 1-2 2-2z" class="N"></path><path d="M710 229c1 0 2 1 3 1s2 0 3 1l4 1h2l1 1c-1 1 0 1-1 1h-1c1 1 2 1 3 2h-2s-1 0-1 1c-2-1-4-2-6-2-1-1-2-1-3-1 0-2-2-3-2-5z" class="H"></path><path d="M735 193c3 6 6 12 6 20-1-1-1-2-1-3v-2-1c-1-1-1-2-1-3-1-1-1-1-1-2l-1-2h-1-1l-1 1h1v1l1 1 1-1v1c-1 1-2 0-2 1h0l2 2h-1c-1 2-3 3-5 4 1 1 0 1 1 1 1 2 1 5 1 7 1 1 1 4 1 5l-1 1c0-1 0-2-1-2 0 1 0 2-1 3h-2c0 1 1 3 0 4-1-1-1-2-1-4l-2 1-1-1v1l-1 1-2 1c0-1-1-1 0-2v-2c-2 1-3 2-4 3l1-3c1-2 0-4 0-6v-1c-2 1-2 2-2 3l-3-3v-1c0-1 1-2 1-3 1-1 1 0 1-1h-1v-1c1 0 1-1 2-2 0-1 0-1 1-1s3-1 4-2l5-5h3c0-1 1-2 1-2l-2-2v-1c2 0 2 0 4 1v-1l-1-1h1c1-1 1-2 2-2z" class="c"></path><path d="M702 168c6 1 9 5 14 8 0 1 1 1 1 2 1 1 3 1 3 3v1h0v1l1-1c4 1 5 6 8 8h1c1 1 1 2 1 3s1 1 1 2l1 1v1c-2-1-2-1-4-1v1l2 2s-1 1-1 2h-3l-5 5c-1 1-3 2-4 2s-1 0-1 1h-2-1-1c0-1-1-1-2-2v1c-2-1-1 0-2-1-1 0-1 1-2 2 0 0 0 1 1 1-3 2-2 5-3 8l-1-1v-3c-1 0-1-1-1-2h0c-1 1-1 1-2 1h0l1-3c0-1 1-2 1-3-1 0-2-1-2-2v-1c2 0 1 0 3 1l1-1-1-1-1-1h0c-1-1-1-2-2-2l-1-1-3-3h0v-1c1-1 2-1 3-2l-2-2h1c1-1 2-2 3-4-1-1-3-1-4-2l-1-1c-2-2 0-2 0-4 1-2 1-3 1-4l3-3c-1-1-1-2-2-3h2l1-2z" class="l"></path><path d="M712 176h1v1h1c0 2-1 2-2 2h0l-2-2 2-1z" class="T"></path><path d="M723 197l3 3c-1 1-3 3-5 3v-1-2c1-1 1-2 2-3zm-8 12c0-2 0-3 1-5v-1l1-1v1c1-1 1-1 2-1-1 2 0 2 0 3l-1 1v2c-1 0-1 0-1 1h-2z" class="N"></path><path d="M713 183c1-1 1-2 2-2 1-2 1-2 2-3 1 1 3 1 3 3v1h0v1l1-1c4 1 5 6 8 8h1c1 1 1 2 1 3s1 1 1 2l1 1v1c-2-1-2-1-4-1v1l2 2s-1 1-1 2h-3v-1h-1l-3-3 2-1-1-1c-1 0-1 0-2-1l3-3v-1c-1 0-1-1-2-2l-1 1h-2l-2 1-1 1-9 9h-1l1-2-2 1v-1c-2-1-2 1-4 2l-1-1 1-2h1l2-3c1-2 2-3 3-4 0-2 1-2 2-3 1-2 2-3 3-4z" class="S"></path><path d="M725 196h1l3 3v1h-2-1l-3-3 2-1z" class="c"></path><path d="M713 183v3h1 0c0 1-1 2-1 2v2c-1 1-2 1-3 1s-1 0-2-1h0c0-2 1-2 2-3 1-2 2-3 3-4z" class="X"></path><path d="M595 364l22 6h3v2l2 2c1 1 1 1 2 1h2c0 2 1 4 2 6s2 2 2 3v1c0 1-1 2-1 3v1c1 1 1 2 1 3-1 2-1 3-1 5h0c1 4 1 8 1 13 0 7-1 16 0 23v1c-3 4-11 9-16 10-2 0-3 0-4-2h-1c-2-1-4-1-5-1-1 1-1 4-1 5v2h4c1 1 0 3 0 4-1 1-1 1-2 1l-1 1h-2v4l-1 1v1l1-1h1c-1 2-1 6-2 8l-1 3h8l1 2h-1l-1 2h-5c0 1 0 1-1 2v2c1 3 5 4 4 7-1-3-3-5-6-6-1-2-3-2-5-2h0c-4 0-6 0-9-2h-2l1 1c-2 1-2 2-3 3 0 1 0 1-1 2-1-2-2-3-3-4-1-3-1-7-2-11v-22-41-21h1c2 0 4 0 6-1v-1-1c-2 0-4 0-6-1 1-2 0-4 0-6h1c1-1 1-2 0-3v-1l1-1c2-1 3-1 4 0 3 0 6 1 9 1 1 0 2-1 3 0h0c1-1 1-2 1-3h-1l1-1z" class="d"></path><path d="M597 408l6 1c1 1 1 1 0 3-2-1-3 0-5 0l-1-1v-3z" class="P"></path><path d="M617 375h2l1 1v-1h4 2c0 2 1 4 2 6s2 2 2 3v1c0 1-1 2-1 3v1c1 1 1 2 1 3l-1-1h-1-6c2-1 3-3 5-4l-1-2c-2-5-5-7-9-10z" class="Z"></path><defs><linearGradient id="n" x1="595.337" y1="372.349" x2="618.441" y2="368.362" xlink:href="#B"><stop offset="0" stop-color="#424343"></stop><stop offset="1" stop-color="#747372"></stop></linearGradient></defs><path fill="url(#n)" d="M595 364l22 6h3v2l2 2c1 1 1 1 2 1h-4v1l-1-1h-2c-1 0-6-2-7-2-6-2-12-4-19-5 1 0 2-1 3 0h0c1-1 1-2 1-3h-1l1-1z"></path><path d="M617 370h3v2l2 2c-1 0-2-1-3-1-2-1-2-2-2-3z" class="J"></path><defs><linearGradient id="o" x1="596.636" y1="376.16" x2="619.972" y2="394.424" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#6a6a69"></stop></linearGradient></defs><path fill="url(#o)" d="M622 391c-2-1-5-2-8-3-5-1-10-1-15-1-2 0-4 0-6-1-2 0-3-1-4-2v-1h26c3 1 8 3 11 2l1 2c-2 1-3 3-5 4z"></path><defs><linearGradient id="p" x1="574.381" y1="390.841" x2="603.78" y2="395.035" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#4c4c4c"></stop></linearGradient></defs><path fill="url(#p)" d="M582 379c3 1 6 4 8 7h0l4 2c3 1 4 2 5 4l1 4c2 0 3 1 4 2v4 3l-1-1h-2c-2-1-5-1-7-2h-18v1h-1v-21h1c2 0 4 0 6-1v-1-1z"></path><path d="M594 402h10v3l-1-1h-2c-2-1-5-1-7-2z" class="V"></path><path d="M590 386l4 2c3 1 4 2 5 4h-1c-1 0-2 0-3-1h-1l-1 1h0l1 3h0c-1 1 0 1-1 2 3 1 6-1 8 1-2 1-3 1-5 1l-1-1c-2 0-4 1-6-2h0 1v-3c1-1 1-1 2-1v-1c-1-2-2-3-2-5z" class="E"></path><path d="M582 379c3 1 6 4 8 7h0c0 2 1 3 2 5v1c-1 0-1 0-2 1v3h-1 0v-4h0v-1h-4c-1-1-2-7-2-9-2 0-5 1-6 0h-1c2 0 4 0 6-1v-1-1z" class="P"></path><path d="M576 403v-1h18c2 1 5 1 7 2h2l1 1v2h-6l-1 1v3l1 1v1c1 0 4-1 5 0v1h-5v1h5v1h-5c1 1 4 0 5 2h-5c1 1 5 0 5 2h-5v1c1 0 3 1 5 0 1 1 0 2 0 4-1 0-4 0-5 1 1 1 5 0 5 0 1 1 1 2 1 2 0 3 1 6 2 8 1 1 2 0 3 2v4c-2-1-4-1-5-1-1 1-1 4-1 5v2h4c1 1 0 3 0 4-1 1-1 1-2 1l-1 1h-2v4l-1 1v1l1-1h1c-1 2-1 6-2 8l-1 3h8l1 2h-1l-1 2h-5c0 1 0 1-1 2v2c1 3 5 4 4 7-1-3-3-5-6-6-1-2-3-2-5-2h0c-4 0-6 0-9-2h-2l1 1c-2 1-2 2-3 3 0 1 0 1-1 2-1-2-2-3-3-4-1-3-1-7-2-11v-22-41h1z" class="C"></path><path d="M581 479l-3-3c1-1 2-1 2-2l3 1 1 1c-2 1-2 2-3 3z" class="K"></path><path d="M596 477c0-3 2-5 3-8 1-1 1-2 2-2l-1 3-1 3v3c-1 0-2 1-3 1z" class="G"></path><path d="M608 470l1 2h-1l-1 2h-5c0 1 0 1-1 2v2c1 3 5 4 4 7-1-3-3-5-6-6-1-2-3-2-5-2h0 2c1 0 2-1 3-1v-3l1-3h8z" class="M"></path><path d="M608 470l1 2h-1c-2 0-4-1-7-1l-2 2 1-3h8z" class="C"></path><path d="M576 403v-1h18c2 1 5 1 7 2-5 1-11 0-16 0-2 0-5 0-7 1h-1-1v1-3z" class="F"></path><path d="M602 458v-11l-1-1v-3c-2 0-3 0-4-2v-1-1c0-1 0-1 1-2l1-1-1-1 1-1c0 2-1 2 1 3 1-1 3 0 6-1 1 1 2 0 3 2v4c-2-1-4-1-5-1-1 1-1 4-1 5v2h4c1 1 0 3 0 4-1 1-1 1-2 1l-1 1h-2v4z" class="P"></path><path d="M715 387h0c0-8 0-14 2-22v13h0v145 30c0 4 1 10 0 13-1 2-1 4-1 7 1 3 0 7 0 11 1 2 1 5 0 8v-1-3c0-1 0-1-1-2-1-2 0-5-1-6v-1c1-2 1-3 0-5h-1c2-2 1-4 1-6 1-1 1-2 2-3 0-1 0 0-1-1v1-2-2c1-2 0-4-1-6l-1 8c0-1-1-1-2-1-1-1-3 0-4 0h0-1l1-1c-2-1-2-2-3-4v-1c-2-2-3-4-5-6-2-6-6-11-11-15h0-1c-1-2-3-3-5-3l-1-2c-7-6-15-12-23-17-2-1-4-3-6-5l-1-1v1l-1-3c2 0 3 0 5 1h1c2-1 3-1 4-1h1v-1c-2 0-3 0-5-1h10l1-1h-6l-3-3 1-1h-2-1l2-2v-1h-4v-1h0c1-2 4-3 6-5 3-2 5-4 9-6h0c1-1 1-1 2-1 1-1 2-1 2-2 2-1 4-3 5-4 2-1 4-3 5-5 6-6 10-14 14-21 3-5 6-10 7-15l8-24c2-8 2-16 3-24z" class="b"></path><path d="M715 387h0c0-8 0-14 2-22v13 31h-1v-4-4c0 2 0 4-1 6v-2-1c-1 1-1 4-1 5-1 1-1 2-1 3h-1v-1c2-8 2-16 3-24z" class="H"></path><path d="M710 434l4-15c0 4-1 7-2 10v3l1-1h1c0 1-1 4-1 5-1 5-1 10-3 14 0 1-1 2-1 4v1l-1-1-2 2h-1l-1-2c1-4 4-8 5-12v-1c1-2 1-4 1-7z" class="K"></path><path d="M707 464h1l1 1-2 1v1l1 1c0 1-1 1-1 2s0 1-1 2v1s1-1 2-1h3l1 1c-1 1-3 0-4 0v1c2 0 2-1 4 1-2 1-4 0-6 0l1 1h3 1l1 1-1 2h1l1 1c-2 1-4 1-5 2h3l1 1v1h-2 0c-2 1-3 1-4 1-3 1-4 1-6 1-1 0-1-1-2-1 0-1 1-1 1-2 1-1 1-3 2-5v-2h1c0-2 2-3 2-5h0l1-1v-1c1-1 1-1 1-2 0-2 0-2 1-3h0z" class="T"></path><defs><linearGradient id="q" x1="689.241" y1="444.384" x2="700.747" y2="453.612" xlink:href="#B"><stop offset="0" stop-color="#b1ac9d"></stop><stop offset="1" stop-color="#c4c4c4"></stop></linearGradient></defs><path fill="url(#q)" d="M714 409c0-1 0-4 1-5v1c-1 3-1 9-1 13-1 1 0 0 0 1l-4 15c0 3 0 5-1 7v1c-1 4-4 8-5 12l1 2h1l2-2 1 1-1 1h1 1l-1 1v1l1 1-1 1h0l-1 1v1l-1 1v1h0c-1 1-1 1-1 3 0 1 0 1-1 2v1l-1 1-1 1h-1v-2c-1 1-1 2-1 4-1 1-2 1-3 2h0v1l-1 1c0 1-1 2-1 3 0 2-2 3-3 4l-2-2h1c0-1 0-1-1-2h0l-1 1h-1-2v1c-2-1-2-1-2-3h-1c-1 0-1 1-2 2l-1-1h0c-1 1-2 1-2 1l5-6c-4 2-8 6-12 6h-1c1-1 2-1 2-2 2-1 4-3 5-4 2-1 4-3 5-5 6-6 10-14 14-21 3-5 6-10 7-15l8-24v1h1c0-1 0-2 1-3z"></path><path d="M714 409c0-1 0-4 1-5v1c-1 3-1 9-1 13-1 1 0 0 0 1l-4 15c-5 17-14 33-25 46h-1c-1 0-1 1-2 2l-1-1h0c-1 1-2 1-2 1l5-6c17-17 27-42 30-67z" class="E"></path><path d="M710 434c0 3 0 5-1 7v1c-1 4-4 8-5 12l1 2h1l2-2 1 1-1 1h1 1l-1 1v1l1 1-1 1h0l-1 1v1l-1 1v1h0c-1 1-1 1-1 3 0 1 0 1-1 2v1l-1 1-1 1h-1v-2c-1 1-1 2-1 4-1 1-2 1-3 2h0v1l-1 1c0 1-1 2-1 3 0 2-2 3-3 4l-2-2h1c0-1 0-1-1-2h0l-1 1h-1-2v1c-2-1-2-1-2-3 11-13 20-29 25-46z" class="W"></path><path d="M707 464h-2c-1 0-2-1-3-2 1-1 1-2 3-3v1h2c-1 2-1 2 0 3v1h0z" class="K"></path><path d="M697 478h-1c-2-1-3 0-5 2v-1h0v-4h0-1l1-1c1-1 2-1 3 0h1c1 0 0 0 1 1h-1c-1 0-2 1-2 2h5l-1 1z" class="Y"></path><path d="M698 477v-1h0c1-1 2-1 3-2 0-2 0-3 1-4v2h1l1-1h0c0 2-2 3-2 5h-1v2c-1 2-1 4-2 5 0 1-1 1-1 2 1 0 1 1 2 1 2 0 3 0 6-1 1 0 2 0 4-1 1 1 2 1 3 2h-1-1c-1 0-1 1-2 1h-9l-1 1h1 0c3-1 10-1 12 0v1c-3 0-9-1-12 1h0 1c3 0 10-1 12 1-4 2-9-1-13 1v1h1c1-1 2-1 4-1h2 1c1 1 3 1 4 0l1 1c-2 1-5 1-7 1-1-1-1-1-2-1-2 0-6 1-7 2h3 7v1h-1c-3 0-7 1-10 2h0c2 0 5-1 7-1h9l1 1c-4 3-12-1-16 2h-2v1c1 0 2 0 3-1 4-1 11 1 14-1l1 1c-3 2-8 1-11 1-1 1-2 1-3 1h0c3 0 12 0 14 1-2 2-8 0-10 1h-2c-1 1 0 1-2 1h0l-1-1h-1v1l2 1v3c-2-1-5-4-6-5s-1-1-1-2c-1 0-2 1-3 1v1c2 1 3 4 5 5v1h1l1 1c-1 0-2-1-2-1l-6-6-2-1h-1v1c-2-1-6-1-8-1h-1-3l1-1h1 4c0-1-1-1-1-2-3 0-5 0-8 1-1 0-3 0-4-1h-3-1l-1 2-3-3 1-1h-2-1l2-2v-1h-4v-1h0c1-2 4-3 6-5 3-2 5-4 9-6h0c1-1 1-1 2-1h1c4 0 8-4 12-6l-5 6s1 0 2-1h0l1 1c1-1 1-2 2-2h1c0 2 0 2 2 3v-1h2 1l1-1h0c1 1 1 1 1 2h-1l2 2c1-1 3-2 3-4 0-1 1-2 1-3l1-1z" class="J"></path><path d="M673 496h3l1 1h-5v1c2 0 4 0 5 1l-11 1h-3l3-1 2-2c1 0 3-1 5-1z" class="E"></path><path d="M661 498v-1c5 0 7-3 11-6 1 1 3 1 5 2l1-1c1 0 2 0 3 1h-2c1 1 0 1 1 1v1c-2 0-5 0-7 1-2 0-4 1-5 1l-2 2-3 1h-1l-1-1v-1z" class="I"></path><path d="M661 498l7-1-2 2-3 1h-1l-1-1v-1z" class="C"></path><path d="M684 480h1c0 2 0 2 2 3v-1h2 1l1-1h0c1 1 1 1 1 2h-1l2 2-1 2-1 3-2 2-3 4v-1h-1l-2 2c-1 0-2 0-4-1l1-1v-1c-1 0 0 0-1-1h2c-1-1-2-1-3-1l-1 1c-2-1-4-1-5-2 4-3 9-7 12-11z" class="Y"></path><path d="M689 492h-2c-1-1-2 0-3-1v-2l5-1 2 2-2 2z" class="S"></path><path d="M692 487l-2-1c-2 1-4 1-6 1h0l-2-1 2-1c0 1 0 0 1 1 0-1 2-2 3-2h2l1-1h0l2 2-1 2zm-21-5h1c4 0 8-4 12-6l-5 6s1 0 2-1h0l1 1c1-1 1-2 2-2-3 4-8 8-12 11s-6 6-11 6v1 1l1 1-1 2-3-3 1-1h-2-1l2-2v-1h-4v-1h0c1-2 4-3 6-5 3-2 5-4 9-6h0c1-1 1-1 2-1z" class="K"></path><path d="M654 494c1-2 4-3 6-5 3-2 5-4 9-6h0c-2 3-5 4-7 6h4c3-2 3-2 6-2-5 4-9 6-14 8h-4v-1h0z" class="X"></path><path d="M679 482s1 0 2-1h0l1 1c1-1 1-2 2-2-3 4-8 8-12 11s-6 6-11 6v1 1l1 1-1 2-3-3 1-1h-2-1l2-2v-1c5-2 9-4 14-8 2-1 5-3 7-5z" class="M"></path><path d="M663 500h3c1 1 3 1 4 1 3-1 5-1 8-1 0 1 1 1 1 2h-4-1l-1 1h3 1c2 0 6 0 8 1v-1h1l2 1 6 6s1 1 2 1l-1-1h-1v-1c-2-1-3-4-5-5v-1c1 0 2-1 3-1 0 1 0 1 1 2s4 4 6 5h1c1 3 5 6 7 9 1 1 2 2 3 4-2 1-3 1-4 2h0c2 0 3 0 4-1l1 1c-1 1-2 1-3 1l-1 1h-3l-1 1 1 1v-1h3l2-1h1 0c-2 2-6 3-9 4l1 1c2-1 3-2 5-2l1-1h1c1 0 2 0 2-1 0 1 1 1 1 2v1c2 2 2 7 2 10h-1l-1-1h-6v1c1 2 2 2 4 3h3l-1 1h-1-2c1 1 2 2 3 4h0c-2-1-3-2-5-4h0c1 2 2 4 4 5h1l1 1h-2v1c1 1 2 1 2 2-1 1-1 0-3 1h3l1 1-1 8c0-1-1-1-2-1-1-1-3 0-4 0h0-1l1-1c-2-1-2-2-3-4v-1c-2-2-3-4-5-6-2-6-6-11-11-15h0-1c-1-2-3-3-5-3l-1-2c-7-6-15-12-23-17-2-1-4-3-6-5l-1-1v1l-1-3c2 0 3 0 5 1h1c2-1 3-1 4-1h1v-1c-2 0-3 0-5-1h10l1-1h-6l1-2h1z" class="Z"></path><path d="M666 503h3l-1 1h-3l-2 2c2 0 3-1 5-1l-1 1v1c1 0 2-1 3 0h2c-1 2-2 0-3 2l-2 2h-2c-1-1-2-1-2-1-2 0-3-2-4-2l-2-2h-1c2-1 3-1 4-1h1v-1c-2 0-3 0-5-1h10z" class="C"></path><path d="M650 505c2 0 3 0 5 1h1 1l2 2c1 0 2 2 4 2 0 0 1 0 2 1h2c2-1 2-1 3 1h1l-2 1v1h0c-2-1-4-2-6-2h-1l2 1h-6c-2-1-4-3-6-5l-1-1v1l-1-3z" class="M"></path><path d="M681 519c-2-1-5-3-6-5h0c-1-1-2-1-3-2v-1c1 0 1-1 2-1 0-1-1-1-1-2 1-1 1-1 2-1s3 1 4 2l1 1 8 8c2 2 4 4 5 6s2 3 3 4v1c1 1 2 3 3 4 2 4 3 8 6 11v1c0 1 1 1 1 2h0c-2 0-2 0-3-1h-1l-1-3-2-2c-1-1-1-2-1-3v-1h-1c-1 0-1-1-2-1-2 0-4-1-6-2h0l-1 1h0-1c-1-2-3-3-5-3l-1-2c-7-6-15-12-23-17h6l-2-1h1c2 0 4 1 6 2h0c4 1 8 5 12 5z" class="X"></path><path d="M688 518c2 2 4 4 5 6-1 0-1 1-2 1h0c-1-1-2-1-3-2 0-1-1-2-2-3l2-2z" class="K"></path><path d="M680 510l8 8-2 2c-3-1-7-4-10-6l1-1 2 1c1-1 1-1 2-1l2 1c0-1-2-2-3-4z" class="T"></path><path d="M664 513l-2-1h1c2 0 4 1 6 2h0c4 1 8 5 12 5 1 1 1 2 2 3s3 2 4 2l1 1v1h-2c0 1 2 2 3 3s3 1 4 1h0c-3 1-5-1-8-1 3 2 7 4 10 7-2 0-4-1-6-2h0l-1 1h0-1c-1-2-3-3-5-3l-1-2c-7-6-15-12-23-17h6z" class="E"></path><path d="M681 530h0c0-3-2-4-4-6l-1-1h1 1l1 1c2 1 4 4 6 5h0c3 2 7 4 10 7-2 0-4-1-6-2h0l-1 1h0-1c-1-2-3-3-5-3l-1-2z" class="C"></path><path d="M664 513l-2-1h1c2 0 4 1 6 2h0c4 1 8 5 12 5 1 1 1 2 2 3s3 2 4 2l-1 1h-2v1l-2-1c-2-1-5-5-9-6-1 0-3-1-4-2-2-1-4-2-5-4zm-112 12h3c-1 1-3 2-5 2-1 0-3 1-4 2 3 0 5-1 7-1v5h3l1-1v-5h6 12l-11 1h-2c2 1 5-1 7 2v5 3l1 1 2 1v7h1c0-1 1-2 2-3v-2l2 1v13 18c1 0 4 0 4 1 2 2 1 6 2 8h-3-1c-1 1-3 1-4 0-1 2-3 2-4 3-4 0-8-1-12 0-1 1-1 1-2 0v1l-1 15c0 1 0 3 1 4l-1 24v13 6 1c-1 1-1 1-1 2 1 4 0 9 1 14v1c-1 1-1 1-3 1h-10l-1-2v-4l-1-2v-3c-1 1-1 2-1 4-1 0-1 1-1 2v1h0c-1-1-1-1-1-2v-1c1-2 1-6 0-7l-1-1c-1 1-1 2-1 3l-1 1c0 1 0 1 1 2 0 2 0 5-1 6v3l-1 2h0c-1-1-1-4-1-6v-1c-1-1-1-3 0-5 0-1 0-1-1-2l-1-1h0c-1 0-2-1-2-1h-3c-1 1-1 1-1 3 0-1 0-2-1-2v-2-1c-1-3-1-5-1-8v-1h2 1 1l-1-1 3 1h2v-4h-2v-2c1-2 1-3 1-5v-2c-1-1-2-2-3-2l2-2v-2l-3-3-2-2 1-1c-2-6-1-15-1-21 0-8-2-17-1-26v-3l1-12v-2h1 2v-1-3l-1-2v-2h3 2 2c0 1 0 2 1 2 2-1 2-1 3-3v-1-1h1c-2-2-1-4-1-6h1l2-1 1-1c-1-1-1-1-1-2 0-2 1-3 2-5 1 1 1 1 2 1 0-2 0-2 1-3 2-1 4-1 7-1z" class="B"></path><path d="M534 670v-6c1-2 1-2 1-3-1-2-1-3-1-4 1-2 1-2 1-3-1-1-1-5-1-7l1-1c2 1 2 1 3 3-1 1-1 2-1 4-1 1-1 2-1 3l-1 1c0 1 0 1 1 2 0 2 0 5-1 6v3l-1 2zm4-16c0-2 1-4 2-6h0 1v1h1c1-1 2-1 2-3 0-1 0-1 1-2v2h1c1-2 1-3 1-5v-1c1 1 1 2 1 3-2 3-4 6-5 9l-2 2c0 1 1 6 1 8l-1-2v-3c-1 1-1 2-1 4-1 0-1 1-1 2v1h0c-1-1-1-1-1-2v-1c1-2 1-6 0-7z" class="O"></path><path d="M526 642l3 1v1h1l1 1h-1-1c-1-1-2 0-3 0v1l5 1v6l-2 1h-3c-1 1-1 1-1 3 0-1 0-2-1-2v-2-1c-1-3-1-5-1-8v-1h2 1 1l-1-1z" class="C"></path><path d="M526 646l5 1v6c-2-1-3-1-5-1h-1l1-1v-5z" class="K"></path><defs><linearGradient id="r" x1="512.633" y1="577.81" x2="537.885" y2="602.189" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#505152"></stop></linearGradient></defs><path fill="url(#r)" d="M524 556v-2h1v3c1 1 1 1 1 3v6 19l1 22c0 3-1 9 1 11h0c-1 1-2 1-2 3l-2-2 1-1c-2-6-1-15-1-21 0-8-2-17-1-26v-3l1-12z"></path><path d="M524 556v-2h1v3c0 3 0 9-1 12l-1-1 1-12z" class="G"></path><path d="M549 639l1 1 3 6v1l1 1c1 1 0 1 2 2-1 1-1 1-1 2 1 4 0 9 1 14v1c-1 1-1 1-3 1h-10l-1-2v-4c0-2-1-7-1-8l2-2c1-3 3-6 5-9v-2c0-1 0-1 1-2z" class="M"></path><path d="M543 652v1c1 1 1 1 2 1l-1 1c0 1 0 2-1 3h0v4c0 1 0 3-1 4v-4c0-2-1-7-1-8l2-2z" class="D"></path><path d="M550 640l3 6v1l1 1c1 1 0 1 2 2-1 1-1 1-1 2 1 4 0 9 1 14l-1 1-2-1c-1 0-3 1-5 0l1-17c0-2 0-4 1-5v-1-2-1z" class="F"></path><path d="M550 644c1 1 1 1 1 3 0 1-1 2-1 4h-1v-2c0-2 0-4 1-5z" class="O"></path><g class="Q"><path d="M553 647l1 1c1 1 0 1 2 2-1 1-1 1-1 2 1 4 0 9 1 14l-1 1-2-1c0-5 1-11 0-16v-3z"></path><path d="M542 606v-1c2 4 2 10 3 13 1 1 3 3 5 3v4l1 1v-5c1 1 2 2 2 4l2 1c0 2 0 2 1 4v13 6 1c-2-1-1-1-2-2l-1-1v-1l-3-6-1-1-2-3-1-1-3 3c-1 3-2 4-5 4h-1c0-9 0-17 2-26 1-2 1-4 2-6l1-4z"></path></g><path d="M545 633c1 1 1 1 1 2l-3 3-1-2-1-1 1-1c1 0 2 0 3-1z" class="I"></path><path d="M550 621v4l1 1-1 11h-1c-1-5 0-11 1-16zm-8 13c0-2 0-7 2-8 1 0 1-1 2 0v3c0 1 0 3-1 4s-2 1-3 1z" class="B"></path><path d="M547 636v-2l1 2h0v-2c0 2 0 3 2 5h1c2 0 4 4 5 4v6 1c-2-1-1-1-2-2l-1-1v-1l-3-6-1-1-2-3z" class="L"></path><path d="M553 646c1-1 1-1 2-1v3l1 1v1c-2-1-1-1-2-2l-1-1v-1z" class="J"></path><path d="M551 621c1 1 2 2 2 4l2 1c0 2 0 2 1 4v13c-1 0-3-4-5-4l-1-2 1-11v-5z" class="Z"></path><path d="M540 558h3c-1 2-1 4-1 6h0c-1 1-1 1-2 1 0 4 0 5 2 8l2 9c0 2 1 3 1 4 2 2 2 4 2 6 0 4-1 10 0 13 0 2 3 5 4 6l1 1-1 1v1 7 5l-1-1v-4c-2 0-4-2-5-3-1-3-1-9-3-13v1l-1 4c-1 2-1 4-2 6h0v-2h0l-1-1c-1-1 0-1 0-3-2 1-2 0-3 0v1h1l1 1h-1-1l-1 1-1-1v-1-32c0-3 1-7 1-10 0-1-1-1-1-2l1 1h3l1 2v-2c1-3 2-6 2-10z" class="G"></path><path d="M546 607l2 2c-1 1-1 4-1 6h-1c0-3-1-5 0-8z" class="C"></path><path d="M540 601h1c0 2 0 3 1 5h0l-1 4c-1-1-1-4-1-6v-3z" class="F"></path><path d="M540 596h0c0-1 0-6 1-7l1 2v9l-1-2c0-1 0-1-1-2z" class="B"></path><path d="M550 621c0-1 0-3-1-4v-1c1-2 1-2 2-2v7 5l-1-1v-4z" class="F"></path><path d="M540 604c-1-2-2-7-1-9 0-3-1-7 1-9 1 0 1 1 2 2v3l-1-2c-1 1-1 6-1 7h0v5 3z" class="C"></path><path d="M540 558h3c-1 2-1 4-1 6h0c-1 1-1 1-2 1 0 4 0 5 2 8l-2 2c1 2 0 7 2 9l-2 1-1-1c-1-1 0-2 1-3 1-3-2-8-2-11v-2c1-3 2-6 2-10z" class="Z"></path><path d="M540 585l2-1c-2-2-1-7-2-9l2-2 2 9c0 2 1 3 1 4 2 2 2 4 2 6 0 4-1 10 0 13 0 2 3 5 4 6l1 1-1 1c-2-1-2-2-3-4l-2-2c-1-2-2-4-2-6h0l1 1h1c-1-1 0-2 0-3v-9c-1-1-2-3-4-4l-2-1z" class="U"></path><path d="M544 558c1-1 6 0 8 1 1 3 2 11 0 14h-1c-1 2 0 2 0 4l1 1 2 2c0-1 1-1 2-1v-4h-2l3-3v11 4l-1 15c0 1 0 3 1 4l-1 24c-1-2-1-2-1-4l-2-1c0-2-1-3-2-4v-7-1l1-1-1-1c-1-1-4-4-4-6-1-3 0-9 0-13 0-2 0-4-2-6 0-1-1-2-1-4l-2-9c-2-3-2-4-2-8 1 0 1 0 2-1h0c0-2 0-4 1-6h1z" class="O"></path><path d="M543 558h1c0 5-1 10 1 15h-1c-2-1-2-2-2-3 1-3 1-3 0-6h0c0-2 0-4 1-6z" class="C"></path><path d="M542 573c-2-3-2-4-2-8 1 0 1 0 2-1 1 3 1 3 0 6 0 1 0 2 2 3h1 0c0 3 1 6 2 9v2c0 1 2 2 2 2 0 1-1 5-2 6 0-2 0-4-2-6 0-1-1-2-1-4l-2-9z" class="G"></path><defs><linearGradient id="s" x1="543.265" y1="611.314" x2="566.468" y2="592.595" xlink:href="#B"><stop offset="0" stop-color="#727173"></stop><stop offset="1" stop-color="#a3a29f"></stop></linearGradient></defs><path fill="url(#s)" d="M552 578l2 2c0-1 1-1 2-1v-4h-2l3-3v11 4l-1 15c0 1 0 3 1 4l-1 24c-1-2-1-2-1-4l-2-1c0-2-1-3-2-4v-7-1l1-1v-34z"></path><path d="M552 525h3c-1 1-3 2-5 2-1 0-3 1-4 2 3 0 5-1 7-1v5l-1 21v5c-2-1-7-2-8-1h-1-3c0 4-1 7-2 10v2l-1-2h-3l-1-1 1-4h-1 0c-2-2-1-8-1-10h-5v-3l-1-2v-2h3 2 2c0 1 0 2 1 2 2-1 2-1 3-3v-1-1h1c-2-2-1-4-1-6h1l2-1 1-1c-1-1-1-1-1-2 0-2 1-3 2-5 1 1 1 1 2 1 0-2 0-2 1-3 2-1 4-1 7-1z" class="P"></path><path d="M534 563v-1l1-3v-1l2 2v1l1 1v6 2l-1-2h-3l-1-1 1-4z" class="U"></path><path d="M540 536l2-1c0 2 0 1-1 2v5 1 3h-1c-1-1-2-1-3-1v-1-1h1c-2-2-1-4-1-6h1l2-1z" class="V"></path><path d="M540 558c0-2 0-5 1-7 0 0 1 1 1 2 2 1 4 1 7 1h3v5c-2-1-7-2-8-1h-1-3z" class="W"></path><path d="M527 550l-1-2v-2h3 2 2c0 1 0 2 1 2v3 1l2-1v1h2c-1 1-2 2-2 3 1 0 1 1 2 2 0 1 0 2-1 3l-2-2v1l-1 3v1h-1 0c-2-2-1-8-1-10h-5v-3z" class="F"></path><path d="M536 555c1 0 1 1 2 2 0 1 0 2-1 3l-2-2v1l-1 3v1h-1c0-3 0-5 1-8l1 1 1-1z" class="M"></path><path d="M527 550l-1-2v-2h3 2c1 2 1 3 1 5v1c-1-1-1-2-1-3h-1l-1 2h0l-2-1z" class="B"></path><path d="M546 529c3 0 5-1 7-1v5l-1 21h-3c-3 0-5 0-7-1v-6c0-2 1-4 1-7v-2-1-3c1-1 1-3 1-4l2-1h0z" class="O"></path><path d="M543 538h2c0-2-1-2-1-3l1-1 1 1v-1l1-1 1 1c1 6 0 13 1 20-3 0-5 0-7-1v-6c0-2 1-4 1-7v-2z" class="B"></path><path d="M543 540h1c0 2 1 6 0 7h-2c0-2 1-4 1-7z" class="O"></path><path d="M557 532v-5h6 12l-11 1h-2c2 1 5-1 7 2v5 3l1 1 2 1v7h1c0-1 1-2 2-3v-2l2 1v13 18c1 0 4 0 4 1 2 2 1 6 2 8h-3-1c-1 1-3 1-4 0-1 2-3 2-4 3-4 0-8-1-12 0-1 1-1 1-2 0v1-4-11l-3 3h2v4c-1 0-2 0-2 1l-2-2-1-1c0-2-1-2 0-4h1c2-3 1-11 0-14v-5l1-21h3l1-1z" class="F"></path><path d="M561 532v-2c2-1 6-1 8 0v5-1h-2v-1c-1-1-4-1-6-1z" class="I"></path><path d="M561 532c2 0 5 0 6 1v1h2v1 3l-1 1-3 1v1h3v2c0 1 0 1-1 1h-3c-1-1-1-1-2-1v-1l-1-1c0-1 1-2 0-3v-6z" class="J"></path><path d="M561 532c2 0 5 0 6 1v1h2v1 3l-1 1c-1-1-1-3-1-4h-3v1l-2 2h-1v-6z" class="Z"></path><path d="M565 557h5 0l-1 1h1v10c0 1 1 3 2 4l-1 1v1h-1l-1-1h-4v1l-1-1c1-1 1-1 2-1v-4c-2 1-1 1-2 3l-1-1c0 2 0 2-1 3h0 0l-1-2v-6-1c2 0 3 0 5-1 1 0 0 0 1-1-2 0-2 0-4 1h-1l1-1v-1h-1c1-1 1-2 1-3h2c2 1 1 1 2 3 0-2-1-3-2-4z" class="R"></path><path d="M561 565h2c0 3-1 4-2 6v-6z" class="C"></path><path d="M569 538l1 1c0 3 1 9-1 12 1 2 2 3 1 5v1h0-5c-1-1-3 0-4-1 1-5 1-9 1-14v1c1 0 1 0 2 1h3c1 0 1 0 1-1v-2h-3v-1l3-1 1-1z" class="L"></path><path d="M569 538l1 1c0 3 1 9-1 12 1 2 2 3 1 5v1h0v-5c-1 1-1 1-2 1l1-2h-1v-5h1l1-1h-2v-2-2h-3v-1l3-1 1-1z" class="I"></path><path d="M570 539l2 1v7h1c0-1 1-2 2-3v-2l2 1v13 18h-4-2v-1l1-1c-1-1-2-3-2-4v-10h-1l1-1v-1c1-2 0-3-1-5 2-3 1-9 1-12z" class="F"></path><path d="M575 542l2 1v13 18h-4l2-1c0-2-1-4-1-7v-10-4h0l-1-1v-4c0-1 1-2 2-3v-2z" class="C"></path><path d="M573 547c0-1 1-2 2-3 0 3 1 10-1 12v-4h0l-1-1v-4z" class="G"></path><path d="M553 533h3l1-1v14l1 1c0 6-1 15 1 21v4s1 1 2 1h1 0 0c1-1 1-1 1-3l1 1c1-2 0-2 2-3v4c-1 0-1 0-2 1l1 1v-1h4l1 1h1 2 4c1 0 4 0 4 1 2 2 1 6 2 8h-3-1c-1 1-3 1-4 0-1 2-3 2-4 3-4 0-8-1-12 0-1 1-1 1-2 0v1-4-11l-3 3h2v4c-1 0-2 0-2 1l-2-2-1-1c0-2-1-2 0-4h1c2-3 1-11 0-14v-5l1-21z" class="H"></path><path d="M557 546l1 1c0 6-1 15 1 21v4s1 1 2 1h1 0 0l-3 1-1 1c2 1 8 1 11 1 0 3 0 3-1 5h-4v-1l1-2v-1l-3 2c0-1 0-1-1-1-2 1-3 3-4 5v-11-26z" class="B"></path><path d="M562 579c0 1 0 1 1 2s1 1 2 1c2 0 2 0 3-1l1 2 2-2h-1l-1-1c1-1 1-2 1-4h4c-1 2-2 4-2 6h2 0v-6h4c1 1 1 1 2 3v4h-1c-1 1-3 1-4 0-1 2-3 2-4 3-4 0-8-1-12 0-1 1-1 1-2 0v1-4c1-2 2-4 4-5 1 0 1 0 1 1z" class="O"></path><path d="M559 586l-1-1 1-1c2 0 4-1 6-1h4 8 1v-1h-2v-1l1-1 1 1h0l-1-1v-1h1l-1-1-1 1v-1l2-2c1 1 1 1 2 3v4h-1c-1 1-3 1-4 0-1 2-3 2-4 3-4 0-8-1-12 0z" class="N"></path><path d="M553 533h3l1-1v14 26l-3 3h2v4c-1 0-2 0-2 1l-2-2-1-1c0-2-1-2 0-4h1c2-3 1-11 0-14v-5l1-21z" class="T"></path><path d="M505 179l1-1c1 1 1 3 1 4l1 16-1-1v12c0 2 0 4 1 6v7l1 4c-1 3-1 5-1 8v3 20 11c0 1 0 2 1 3v2 6 67l-1 1h-1l-2-1c-1 2-3 3-3 5-1 1 0 1 0 3h0c1 1 2 2 3 4h3v1l-4 1-4 2v-5h-1c0-3 1-8 0-11h-1c0 2 0 4-1 6l1 2c0 1 0 2-1 3v1c-1-1-1-1-1-2l-1-1v7h-1v9c0-1-1-2-1-4h0c-1 0-1 2-1 3 0-1 0-2-1-3 1-3 0-6 1-9v-14h-1c-3-3-1-7-1-10l-2-2c-1 1-1 2-1 3v1h0l-1 1-2 1h-3-1l1-2c-1-1-1-2 0-4h0c-1-1-1-1-2-1l1-1c-1-1-1-2-2-3 1 0 1-1 1-1l-1-1h-1v-1c-1-2-1-4-1-6h-1v-1c-1 1-2 1-3 1 0 0-1-1-1-2h0-2v-5-15-11l1-1c1-2 1-2 1-4 1-3 0-7 1-8 0-2 1-2 1-3l-1-1v-2c0-2 1-2 2-3v-1c0-2 1-3 1-5-1-4-1-8-1-12h-1v-6l-2-1v-7-4h1c1 0 2-1 2-2 1-1 1-2 1-3h1v-10-2c1-1 1-1 2-1h0 1v-1c0-2-1-5 1-7l3 1h1c1 1 2 1 2 2s0 1 1 2h2l1-2v-2h5 4l2-1h0c-1-1 0-1-1-2h1l1-2c0-1 0-1 1-1v-4c1-4 1-9 2-13z" class="AM"></path><path d="M492 358c1-1 1-1 1-2v-2-2h1v10 9c0-1-1-2-1-4h0c-1 0-1 2-1 3 0-1 0-2-1-3 1-3 0-6 1-9z" class="z"></path><path d="M495 202h4 1l-1 4h-1c-3 1-7 1-9 0l1-2v-2h5z" class="n"></path><path d="M490 204c1 0 1-1 2-1 2 1 3 2 5 2h1l1 1h-1c-3 1-7 1-9 0l1-2z" class="o"></path><path d="M500 345h3l2 1c-1 2-3 3-3 5-1 1 0 1 0 3h0c1 1 2 2 3 4h3v1l-4 1-4 2v-5-1-11z" class="AS"></path><path d="M486 207c2 0 3 1 5 0l1 1c1 0 5 0 6 1v3 2h-1-2-1c-2 0-3-1-4-1s-2-1-3-1v-3h0c-1-1-1-1-1-2z" class="o"></path><path d="M486 207c2 0 3 1 5 0 0 1 1 1 2 2l1 1c-1 2-2 1-3 1 0 1-1 2-1 2-1 0-2-1-3-1v-3h0c-1-1-1-1-1-2z" class="q"></path><path d="M498 212c0 1 1 3 1 4l-1 1v1 2c0 2 1 4 0 6v1 2 5c0 1 0 2-1 3v1c1 2 1 5 0 8v2h0l-1 1v-2-11c0-2 1-6-1-8v4c-1 1-1 2-1 3v7h0v-1c-1-1-1-1-2-1v-1l1-2-1-8 1-1v-4h1 1v1h-1l1 2v-1c2-2 1-7 1-10l-1-2h2 1v-2z" class="r"></path><path d="M505 179l1-1c1 1 1 3 1 4l1 16-1-1-1-1c-1 0-1 0-2 1l-1 3c-1 5 0 10-1 14v19c-1 6 1 12-2 17v2c-1-2-1-4-1-6s1-4 1-6v-13c0-2 0-5 1-7 0-1 0-1-1-2h0l1-1c0-2 0-3 1-4v-11h-2-1l2-1h0c-1-1 0-1-1-2h1l1-2c0-1 0-1 1-1v-4c1-4 1-9 2-13z" class="AS"></path><path d="M502 233v15c-1 5 0 12 0 18v32 23c0 5-1 9 0 14 1 2 1 7 0 9l1 1h-3c-1-3-1-9-1-13 1-7 1-15 0-22v-27-20c0-4 0-8 1-11v-2c3-5 1-11 2-17z" class="AG"></path><path d="M494 242h0v-7c0-1 0-2 1-3v-4c2 2 1 6 1 8v11 2l1-1v11c0 3 1 6-1 8l1 2h-1v2l-1 65h0c-1 0-1-1-1-1-1-2 0-5 0-7v-19c-2 3 0 9-2 13l1 1c-1 1-1 1-2 1v-5c0-2 0-3-1-5v-1c-1-1-1-2-1-3v-2c1-1 0-5 1-6v-7-4l1-13c-1-2-1-4-1-6-1 0-1-1-2-1 0-1-1-2-2-3l1-26c0-1 1-1 2-2v1h-1v1 1l2-1c1-1 1-1 1-3h1v1c1 0 1 0 2 1v1z" class="AO"></path><path d="M496 236v11 2l1-1v11c0 3 1 6-1 8l1 2h-1v2-35z" class="AN"></path><path d="M490 272c1-3 0-5 0-8s0-6 1-7l1-1c-1-3-1-6 0-8l1-1c0 1 1 2 1 3v7h-2v3h0c-1 4 0 8-1 12v1 5c-1-2-1-4-1-6z" class="r"></path><path d="M489 310v-2c1-1 0-5 1-6v-7-4c1 2 2 3 1 5 0 2 1 4 1 6v9c0 2 0 5-1 8 0-2 0-3-1-5v-1c-1-1-1-2-1-3z" class="AM"></path><path d="M491 239h1v1c1 0 1 0 2 1v1 8c0-1-1-2-1-3l-1 1c-1 2-1 5 0 8l-1 1c-1 1-1 4-1 7s1 5 0 8c-1 0-1-1-2-1 0-1-1-2-2-3l1-26c0-1 1-1 2-2v1h-1v1 1l2-1c1-1 1-1 1-3z" class="AP"></path><path d="M502 233v-19c1-4 0-9 1-14l1-3c1-1 1-1 2-1l1 1v12c0 2 0 4 1 6v7l1 4c-1 3-1 5-1 8v3 20 11c0 1 0 2 1 3v2 6 67l-1 1h-1l-2-1-2-1-1-1c1-2 1-7 0-9-1-5 0-9 0-14v-23-32c0-6-1-13 0-18v-15z" class="AA"></path><path d="M502 233v-19c1-4 0-9 1-14l1-3c1-1 1-1 2-1l1 1v12c0 2 0 4 1 6v7l1 4c-1 3-1 5-1 8v3c0-3-1-5-2-8v-3l-1-2-1 1v5c-1 2 0 4 0 5v15 6c-1 6 0 13 0 19l-1 26c0 6 1 12 1 19 1 6 0 13 1 20l1 1h2c1-4 0-10 0-14l1-34v-10-4 67l-1 1h-1l-2-1-2-1-1-1c1-2 1-7 0-9-1-5 0-9 0-14v-23-32c0-6-1-13 0-18v-15z" class="AT"></path><path d="M479 208c0-2-1-5 1-7l3 1h1c1 1 2 1 2 2s0 1 1 2l-1 1c0 1 0 1 1 2h0v3c1 0 2 1 3 1s2 1 4 1h1l1 2c0 3 1 8-1 10v1l-1-2h1v-1h-1-1v4l-1 1 1 8-1 2h-1c0 2 0 2-1 3l-2 1v-1-1h1v-1c-1 1-2 1-2 2l-1 26c1 1 2 2 2 3 1 0 1 1 2 1 0 2 0 4 1 6l-1 13v4 7c-1 1 0 5-1 6v2c0 1 0 2 1 3v1c1 2 1 3 1 5v5 8c2 3 2 9 1 12h-1c-3-3-1-7-1-10l-2-2c-1 1-1 2-1 3v1h0l-1 1-2 1h-3-1l1-2c-1-1-1-2 0-4h0c-1-1-1-1-2-1l1-1c-1-1-1-2-2-3 1 0 1-1 1-1l-1-1h-1v-1c-1-2-1-4-1-6h-1v-1c-1 1-2 1-3 1 0 0-1-1-1-2h0-2v-5-15-11l1-1c1-2 1-2 1-4 1-3 0-7 1-8 0-2 1-2 1-3l-1-1v-2c0-2 1-2 2-3v-1c0-2 1-3 1-5-1-4-1-8-1-12h-1v-6l-2-1v-7-4h1c1 0 2-1 2-2 1-1 1-2 1-3h1v-10-2c1-1 1-1 2-1h0 1v-1z" class="AH"></path><path d="M482 333c1-2 1-3 2-4v7 2h-3-1l1-2h1v-2-1z" class="u"></path><path d="M483 228c2 4 1 9 1 13 0-2-2-2-3-3 2-3 1-7 2-10z" class="f"></path><path d="M478 229c1-1 2-1 2-2h1 1l1 1c-1 3 0 7-2 10l-1-1v-1-1l-2 1v-2l-2-2h1v-1c0-1 1-2 1-2z" class="n"></path><path d="M479 208c0-2-1-5 1-7l3 1h1c1 1 2 1 2 2s0 1 1 2l-1 1c0 1 0 1 1 2h0v3l-2-1v-1c1-1 0-3 0-4l-1-1v5l-1 1c0-2 1-5 0-7h-1c-1 2 0 4 0 6v2h1c0 3-1 4-1 7v8h-1-1c0 1-1 1-2 2v-1l-1-1c-1-1-1-3-1-5v-10-2c1-1 1-1 2-1h0 1v-1z" class="j"></path><path d="M479 209v-1c0 4 1 8 0 11s-1 6-1 9l-1-1c-1-1-1-3-1-5v-10-2c1-1 1-1 2-1h0 1z" class="X"></path><path d="M486 216c-1-1-1-1 0-2 1 0 1-1 2 0s1 2 2 3h1c0-1 0-1-1-2 1 0 2 1 3 1 2 3 2 5 2 8h-1-1v4l-1 1 1 8-1 2h-1c0 2 0 2-1 3l-2 1v-1-1h1v-1c-1 1-2 1-2 2v-9c-1-3-1-5-1-8-1-3 0-6 0-9z" class="i"></path><path d="M491 239h-1c0-3-1-8 0-11h1v3h1v-2l1 8-1 2h-1z" class="o"></path><path d="M486 216c-1-1-1-1 0-2 1 0 1-1 2 0s1 2 2 3h1c0-1 0-1-1-2 1 0 2 1 3 1-1 2-1 3-1 4v3h-1c1 1 1 1 1 2s0 1-1 2c0-1 0-1-1-2l-1 1h-1v-3c-1-1-1-1-1-2 0-2 0-3-1-4v-1z" class="n"></path><path d="M475 222h1c0 2 0 4 1 5l1 1v1s-1 1-1 2v1h-1l2 2v2l2-1v1 1l1 1c1 1 3 1 3 3v3h-1l-1 2v2l-1 2 1 2c-2 1-3 3-3 5h-1v-2h-1l-1 2h-1c-1-4-1-8-1-12h-1v-6l-2-1v-7-4h1c1 0 2-1 2-2 1-1 1-2 1-3z" class="o"></path><path d="M475 222h1c0 2 0 4 1 5l1 1v1s-1 1-1 2v1h-1l2 2v2h0c0 1 0 1 1 2v3 5h1l-1 1c-2-2-1-3-1-5h-1 0c-1-1-1-1-1-2-1 1-1 3-2 4v1h-1v-6l-2-1v-7-4h1c1 0 2-1 2-2 1-1 1-2 1-3z" class="i"></path><path d="M476 232v2l-1-1c-1-2-1-4 0-6h1 1l1 1v1s-1 1-1 2v1h-1z" class="q"></path><path d="M471 231v1c1 1 1 2 2 3 2 3 2 6 1 9v1h-1v-6l-2-1v-7z" class="f"></path><path d="M486 268c1 1 2 2 2 3 1 0 1 1 2 1 0 2 0 4 1 6l-1 13v4 7c-1 1 0 5-1 6v2c0 1 0 2 1 3v1c1 2 1 3 1 5v5 8c2 3 2 9 1 12h-1c-3-3-1-7-1-10l-2-2c-1 1-1 2-1 3v1h0l-1 1v-1c-1-2-1-4-1-7v-15l1-8v-4-4c1-7 0-15 0-21v-9z" class="r"></path><path d="M489 310c0 1 0 2 1 3v1c-1 2-2 4-1 5 0 2-1 10-2 11h0c0-6 0-13 1-20h1z" class="z"></path><path d="M488 271c1 0 1 1 2 1 0 2 0 4 1 6l-1 13v4 7c-1 1 0 5-1 6v2h-1v-31c0-3-1-5 0-8z" class="u"></path><path d="M487 330h0c1-1 2-9 2-11-1-1 0-3 1-5 1 2 1 3 1 5v5 8c2 3 2 9 1 12h-1c-3-3-1-7-1-10l-2-2c-1 1-1 2-1 3v1h0l-1 1v-1c-1-2-1-4-1-7v1c1 0 1 1 1 2l1 1v-3z" class="AN"></path><path d="M476 257l1-2h1v2h1c0-2 1-4 3-5l-1-2 1-2v-2l1-2h1v6c1 5 0 10 0 15v59 5c-1 1-1 2-2 4v1 2h-1c-1-1-1-2 0-4h0c-1-1-1-1-2-1l1-1c-1-1-1-2-2-3 1 0 1-1 1-1l-1-1h-1l1-1h1c1-2 0-5 0-7 2-3 1-9 1-12v-9c0-2 0-7 1-8 0-3 0-5-1-7v-10-5-1-1c-1-4-2-2-4-4v-3z" class="z"></path><path d="M480 305l3 1-1 2c-1 2 0 6 0 8 1 3 0 7 0 10v-1c1 0 1 0 2-1v5c-1 1-1 2-2 4v1 2h-1c-1-1-1-2 0-4h0c-1-1-1-1-2-1l1-1c-1-1-1-2-2-3 1 0 1-1 1-1l-1-1h-1l1-1h1c1-2 0-5 0-7 2-3 1-9 1-12z" class="AP"></path><path d="M484 324v5c-1 1-1 2-2 4v-4-3-1c1 0 1 0 2-1z" class="r"></path><path d="M476 257l1-2h1v2h1c0-2 1-4 3-5l-1-2 1-2v-2l1-2h1v6 12 5s-1 1-1 2c-1 3 1 6-1 9 0 2 0 2 1 3v4c0 1-1 1-1 2v2h-1v-1c0-3 0-5-1-7v-10-5-1-1c-1-4-2-2-4-4v-3z" class="i"></path><path d="M475 257h1v3c2 2 3 0 4 4v1 1 5 10c1 2 1 4 1 7-1 1-1 6-1 8v9c0 3 1 9-1 12 0 2 1 5 0 7h-1l-1 1v-1c-1-2-1-4-1-6h-1v-1c-1 1-2 1-3 1 0 0-1-1-1-2h0-2v-5-15-11l1-1c1-2 1-2 1-4 1-3 0-7 1-8 0-2 1-2 1-3l-1-1v-2c0-2 1-2 2-3v-1c0-2 1-3 1-5z" class="AG"></path><path d="M472 297v-12c2 2 3 3 3 5-1 2 0 11 0 14h0v2c-1 0-1 0-1-1l-2 1v-9z" class="AJ"></path><path d="M469 296l2-7v6c1 1 0 1 1 2v9c0 2-1 7 0 9l1 1 2 1c-1 1-2 1-3 1 0 0-1-1-1-2h0-2v-5-15z" class="z"></path><path d="M477 289v-2l1 1v2h1v4s1 1 1 2v9c0 3 1 9-1 12 0 2 1 5 0 7h-1l-1 1v-1c-1-2-1-4-1-6v1-1-14h0v1c1 2 1 6 2 7v-16c-1-2 0-5-1-7z" class="AM"></path><path d="M475 257h1v3c2 2 3 0 4 4v1 1 5 10c1 2 1 4 1 7-1 1-1 6-1 8 0-1-1-2-1-2v-4h-1v-2l-1-1v2l-1 2v-1h-1c0-2-1-3-3-5v12c-1-1 0-1-1-2v-6l-2 7v-11l1-1c1-2 1-2 1-4 1-3 0-7 1-8 0-2 1-2 1-3l-1-1v-2c0-2 1-2 2-3v-1c0-2 1-3 1-5z" class="u"></path><path d="M474 264h1l1 2h0c1 3 0 7 0 10 0 1 0 3 1 5h0l-1 1c0-1-1-1-1-2 1-1 1-1 1-2-1-1-1-2-1-4v-5h-1v-1-1-3z" class="f"></path><path d="M475 257h1v3c2 2 3 0 4 4-2 3-1 9-1 13-2-3 0-8-1-11h-2 0l-1-2h-1v-1-1c0-2 1-3 1-5z" class="n"></path><path d="M480 264v1 1 5 10c1 2 1 4 1 7-1 1-1 6-1 8 0-1-1-2-1-2v-4-13c0-4-1-10 1-13z" class="AH"></path><defs><linearGradient id="t" x1="466.752" y1="769.428" x2="438.987" y2="802.864" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#535354"></stop></linearGradient></defs><path fill="url(#t)" d="M444 642c1-1 1-2 2-3 4-3 8-2 12-2v1 7 5l-1 1c1 1 1 1 1 3v1 25c0 4 1 10-1 13v6l1 1 1 40c0 6-1 14 0 20v1l1 1h3 1 5 0 1 2 1c-1 3-1 6-3 9h-1c0 1 0 0 1 2 1 3-1 5 0 8-1 2-1 2 0 4h2v3h-1l1 2h-3v4c-2 1-4 1-6 1h-1l-2-1c-1 2-1 4-1 6-2 7-3 15-7 21-1 3-3 6-5 9-7 10-16 20-27 27-3 1-8 5-10 5l1-2h-1c-1-3-3-5-5-7h-1c0-1-1-2-2-2 0-2-2-3-3-4v-1l-1-1-2-2h-1c0-1-1-1 0-2v-1h-2v-1l11-14c1-2 2-3 3-5s3-3 4-5c1 1 2 2 3 2l4-4 1-2v-3-1h0l-1-1c0 1 0 2-1 3h0c0-1 0-2-1-4h1 0c1-1 2-2 2-3h0 1c0-2 1-3 2-5 1-1 1-2 3-3 0-1 0-1 1-2v-7l1-1h5v-1h-4-2-1v-4h0l1 2h1-1v-3-1l-1-1v-3c-2-2-1-10-1-13 0-1 1-2 0-3 0-2 0-3-1-5 4 0 7-1 10-4 2-1 2-2 3-2h0c-1 0-2 1-3 1-5 3-9 4-14 4l-1-1 1-2h4v-1h0v-1c-1-1 0-2-1-2v-1c1-1 1-2 1-3l-2-1h3l1-1h1c4 0 11-1 14-3 2-2 4-5 5-7v-1c2-3 4-6 5-10 3-8 1-19 2-27v-4l2 1-1-2v-1-5c0-3 1-9 0-12-5 1-9 1-14 1v-23l1 2c2-1 3-3 4-5z"></path><path d="M445 765c2-1 5-1 7 0-4 1-7 1-11 1 1 0 2-1 4-1z" class="M"></path><path d="M435 763h-1l1-1-5-1v-1l1 1c3 1 14-1 15 2h-11z" class="I"></path><path d="M459 778l3 1-1 1h-1v6 4h-2v-5c1-1 1-3 1-4v-3z" class="B"></path><path d="M460 786v-6h1c1 1 1 2 2 2l1 1h-1v2c-1 1-1 3-1 4v1h-2v-4z" class="R"></path><path d="M460 786l2-1v4 1h-2v-4z" class="C"></path><path d="M450 791l1-1c1-1 1-2 2-2v2h0l2 2v1c-1 1-1 1-1 2s-1 1-1 2c0 0 0 1-1 2 0 1 0 2-1 3h-1c-2-1-3-3-5-4l2-2c0-1 1-3 1-4l1-1h1z" class="E"></path><path d="M439 781c2 0 2 0 4 1 1 0 3 0 5 1h-5 0c1 1 2 1 3 1l2 2c0 1-1 2 0 3 0 0 1-1 2-1v-1 1 3h-1l-1 1h-1c-1 0-1 0-1 1-1 1-1 2-1 3-1 3-5 7-7 8-2 2-6 5-7 7-2 0-3-1-4-2v-1l4 2v-1-1l1-1c-1-1-2-1-3-3l1-1s1-1 2-1h0l1-1h1l1-1-1-1h1l4 3 1-1-1-1-1-2c-1-1-2-2-3-4v-1h-1l1-2c-1-2-2-2-2-3h1v-1l-2-1-2-1c-1 0-2 0-3-1h5v-1h-4l-1-1h12v-1z" class="P"></path><path d="M435 800c2 1 2 1 3 3-1 1-1 2-3 3l-1-1c0 1 0 1 1 2l-2 1s-1 0-1-1c-1-1-2-1-3-3l1-1s1-1 2-1h0l1-1h1l1-1z" class="g"></path><path d="M432 783h7v1h-3c1 1 2 1 3 1l1 1s-1 1-2 1c0 1 0 1-1 2v1c3 1 4 3 6 6l-7-5c2 2 3 4 5 6 0 1-1 1-1 2l-1 1-1-2c-1-1-2-2-3-4v-1h-1l1-2c-1-2-2-2-2-3h1v-1l-2-1-2-1c-1 0-2 0-3-1h5v-1z" class="L"></path><path d="M426 792v-7l1-1c1 1 2 1 3 1l2 1 2 1v1h-1c0 1 1 1 2 3l-1 2h1v1c1 2 2 3 3 4l1 2 1 1-1 1-4-3h-1l1 1-1 1h-1l-1 1h0c-1 0-2 1-2 1l-1 1c1 2 2 2 3 3l-1 1v1 1l-4-2v1c1 1 2 2 4 2v1l-1 1-1-1-1-1h-1 0l-1 1h1v1h-2-1-1c0 1 1 2 1 2h-1c-1 0-2-1-2-2l-1 1-2-1 1-2v-3-1h0l-1-1c0 1 0 2-1 3h0c0-1 0-2-1-4h1 0c1-1 2-2 2-3h0 1c0-2 1-3 2-5 1-1 1-2 3-3 0-1 0-1 1-2z" class="Z"></path><path d="M428 800l1-1 3-3c0 1 0 2-1 2l1 2-4 4v1c0 1-1 1-1 1h-3-1l3-3c0-1 1-2 2-3z" class="e"></path><path d="M426 792v-7l1-1c1 1 2 1 3 1l2 1c-1 2-2 1-3 3h1l1 1c-1 1-1 2-2 3l1 1c0 1-1 2-2 2v1l-1-1-2-2c0-1 0-1 1-2z" class="g"></path><path d="M426 792l1-1c0-1 0-1 1-2h0v2 1h1c0-1 1-2 1-3l1 1c-1 1-1 2-2 3l1 1c0 1-1 2-2 2v1l-1-1-2-2c0-1 0-1 1-2z" class="W"></path><path d="M425 794l2 2 1 1c-2 1-2 1-3 3 1 0 1 0 1-1 1-1 2-2 3-2h1l-2 3c-1 1-2 2-2 3l-3 3h1 3s1 0 1-1l3 3v1 1l-4-2v1c1 1 2 2 4 2v1l-1 1-1-1-1-1h-1 0l-1 1h1v1h-2-1-1c0 1 1 2 1 2h-1c-1 0-2-1-2-2l-1 1-2-1 1-2v-3-1h0l-1-1c0 1 0 2-1 3h0c0-1 0-2-1-4h1 0c1-1 2-2 2-3h0 1c0-2 1-3 2-5 1-1 1-2 3-3z" class="S"></path><path d="M459 761l1 1h3 1 5 0 1 2 1c-1 3-1 6-3 9h-1c0 1 0 0 1 2 1 3-1 5 0 8-1 2-1 2 0 4h2v3h-1l1 2h-3v4c-2 1-4 1-6 1h-1v-1h1v-4h-1v-1c0-1 0-3 1-4v-2h1l-1-1c-1 0-1-1-2-2l1-1-3-1c0-1-1-2-1-3l1-1v-2l-1-1-1-1c-1-1-1-5 0-6 0-2 1-2 2-3z" class="Q"></path><path d="M463 785c2 0 3 0 4 1v4h-4-1v-1c0-1 0-3 1-4z" class="H"></path><path d="M462 779v-7c1-1 4-1 5 0h1l-1 1v8 1h-4c-1 0-1-1-2-2l1-1z" class="W"></path><path d="M459 761l1 1h3 1 5 0 1 0c-2 3-2 6-2 8-3 1-5 1-7 1-1-1-2-1-3 0l-1-1c-1-1-1-5 0-6 0-2 1-2 2-3z" class="e"></path><path d="M462 764h4v1c-1 1 0 3 0 5h-4v-5-1z" class="k"></path><path d="M459 761l1 1h3 1 5c-1 1-1 2-3 2h-4v1l-1 3h0-3l-1-1v3c-1-1-1-5 0-6 0-2 1-2 2-3z" class="L"></path><path d="M464 762h5c-1 1-1 2-3 2h-4c-1 0-2 0-3-1l1-1h3 1z" class="p"></path><defs><linearGradient id="u" x1="439.678" y1="760.551" x2="422.795" y2="766.436" xlink:href="#B"><stop offset="0" stop-color="#525153"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#u)" d="M423 752c4 0 7-1 10-4l2 2 2-1 1 2-1 1c6 1 12 1 17 1v1h-10c-3-1-6-1-9 0 2 2 13 0 13 2h-15v1c3 0 8-1 11 1h0c-3 0-11-1-13 1h5c3 0 7 0 10 1h1c-6 1-11 0-17 0v1l5 1-1 1h1-5v1l15 1c-2 0-3 1-4 1-4 0-8-1-11 0h0c2 1 17 1 17 1-2 2-16 0-17 1v1l14 1c1-1 2-1 3 0h1l-2 1h0l2 1-1 1-2-1c-4 2-11-1-16 1 2 2 9 1 12 1h6c-1 1-1 1-2 1v1h2l-1 1h-1v1h1c0 1 0 1-1 1l1 1h-1c-1 0-3-1-3-1l-1 1h-14c4 1 8 1 12 1v1h-12l1 1h-2-1v-4h0l1 2h1-1v-3-1l-1-1v-3c-2-2-1-10-1-13 0-1 1-2 0-3 0-2 0-3-1-5z"></path><path d="M444 770c1-1 2-1 3 0h1l-2 1h0l2 1-1 1-2-1h-11c-1-1-3 0-5-1 5-1 11 1 15-1z" class="D"></path><path d="M441 774h6c-1 1-1 1-2 1v1h2l-1 1h-1v1h1c0 1 0 1-1 1l1 1h-1c-1 0-3-1-3-1-3 0-11 1-13-1 4-1 9 0 13 0v-1c-4-1-8-1-13-1l1-1h11v-1z" class="C"></path><defs><linearGradient id="v" x1="460.531" y1="702.062" x2="441.963" y2="701.047" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#3a3a3b"></stop></linearGradient></defs><path fill="url(#v)" d="M444 642c1-1 1-2 2-3 4-3 8-2 12-2v1 7 5l-1 1c1 1 1 1 1 3v1 25c0 4 1 10-1 13v6l1 1 1 40c0 6-1 14 0 20-1 1-2 1-3 0-3 0-6 1-9 1v-1h-1c-3-1-7-1-10-1h-5c2-2 10-1 13-1h0c-3-2-8-1-11-1v-1h15c0-2-11 0-13-2 3-1 6-1 9 0h10v-1c-5 0-11 0-17-1l1-1-1-2-2 1-2-2c2-1 2-2 3-2h0c-1 0-2 1-3 1-5 3-9 4-14 4l-1-1 1-2h4v-1h0v-1c-1-1 0-2-1-2v-1c1-1 1-2 1-3l-2-1h3l1-1h1c4 0 11-1 14-3 2-2 4-5 5-7v-1c2-3 4-6 5-10 3-8 1-19 2-27v-4l2 1-1-2v-1-5c0-3 1-9 0-12-5 1-9 1-14 1v-23l1 2c2-1 3-3 4-5z"></path><path d="M448 730l1 2c0 2 0 3-2 4 0 1-1 1-2 1 1 2 1 2 2 3-2 1-2-1-3 1-1 0-2-1-2-1 2-3 4-7 6-10z" class="C"></path><path d="M436 746l8 1h-6v1c2 0 9 0 9 1-1 0-8-1-9 1 2 1 5-1 6 1h-6l-1-2-2 1-2-2c2-1 2-2 3-2z" class="Q"></path><path d="M457 639c0-1 0-1 1-1v7 5l-1 1c-2 0-5 0-6-1s-3-4-3-6 2-3 3-4c2-1 3-1 6-1z" class="O"></path><path d="M457 639c0-1 0-1 1-1v7h-2v-1c-1-1-2-1-3-2h-1l1-1h2c1 0 2 0 2-1v-1z" class="F"></path><path d="M444 642c1-1 1-2 2-3 4-3 8-2 12-2v1c-1 0-1 0-1 1-3 0-4 0-6 1l-1-1c-1 0-2 2-2 3h-2c-1 1-1 2-2 3l4 20c0 1 0 0 1 0l5 1v1h-1c-5 1-9 1-14 1v-23l1 2c2-1 3-3 4-5z" class="C"></path><path d="M444 642c1-1 1-2 2-3 4-3 8-2 12-2v1c-1 0-1 0-1 1-3 0-4 0-6 1l-1-1c-1 0-2 2-2 3h-2c-1 1-1 2-2 3s-1 1-2 3v6h-1v-6c-1 1-1 5-1 6v-7c2-1 3-3 4-5z" class="B"></path><path d="M442 654v-6c1-2 1-2 2-3l4 20c-2 1-4 1-6 0-1-1 0-7 0-10v-1zm3 74v-1c2-3 4-6 5-10 3-8 1-19 2-27v-4l2 1c0 1 0 2-1 3v10 8c0 3 0 7-1 9 0 5-2 9-4 13-2 3-4 7-6 10-1 1-2 2-4 3 0-2 2-3 4-4l-1-1-2 3h-2l-1 2c-1 2-3 3-5 4-1-1-1-1-2-1h0c-2-1-2-5-2-7h-3l1-1h1c4 0 11-1 14-3 2-2 4-5 5-7z" class="F"></path><defs><linearGradient id="w" x1="442.38" y1="844.319" x2="396.631" y2="837.036" xlink:href="#B"><stop offset="0" stop-color="#6f6f70"></stop><stop offset="1" stop-color="#c0bebb"></stop></linearGradient></defs><path fill="url(#w)" d="M436 814h-1c0-1 2-2 2-4l7-8c2 0 2 0 3 1 0 3-1 4-2 6l-1 1h1c1-1 3-2 4-3 1 1 0 1 0 2 1 1 3 3 4 3 0 2 0 3 1 4l-1 1h0l-1 2v2c-1 3-3 6-5 9-7 10-16 20-27 27-3 1-8 5-10 5l1-2h-1c-1-3-3-5-5-7h-1c0-1-1-2-2-2 0-2-2-3-3-4v-1l-1-1-2-2h-1c0-1-1-1 0-2v-1h-2v-1l11-14c1-2 2-3 3-5s3-3 4-5c1 1 2 2 3 2l4-4 2 1 1-1c0 1 1 2 2 2h1s-1-1-1-2h1 1 2v-1h-1l1-1h0 1l1 1 1 1c-2 2-4 3-7 5 3 1 5-2 7-3v-1c1 0 2 0 3-1l-2 2v2c2 1 2-2 5-2l2 2h0 1l-3-3z"></path><path d="M410 824h0v1l1 1-4 4h-1 0v-2l1-1c1 0 2-1 2-2l1-1z" class="N"></path><path d="M411 815c1 1 2 2 3 2l3 3-1 1c-1 1-2 1-2 0h-2l-1 1h0l-1-1-2 1-1 2c-1 0-2 1-3 1 1-2 2-3 3-5s3-3 4-5z" class="W"></path><path d="M425 827h1c1 0 3-3 4-3 1-2 3-3 5-4l1 1c0 1 0 1-1 2l-2 2c0 2 2 3 2 5-1 0-1-1-2-1v1 1 1h-1-1 0v2c0 1-1 2-2 3 0 1-4 5-5 7-1-1-1-2-2-3l1-1 1 1v-2c-1 0 0 0-1-1v-1h-1v1 1c-1-1-1-2-1-3h1c0-1 0-2 1-3h0l-1-1-1 1v-1c1-2 2-3 4-5z" class="Y"></path><path d="M436 814l1-1c1 0 1 0 1 1 1 2 2 3 4 5 0 2-3 4-4 7h-2c0-1-1-1-1-3h0c1-1 1-1 1-2l-1-1c-2 1-4 2-5 4-1 0-3 3-4 3h-1c0-1 3-3 3-3h-1l-6 6h-2-1c-1-2-2-3-2-5 2-3 5-5 7-7 3 1 5-2 7-3v-1c1 0 2 0 3-1l-2 2v2c2 1 2-2 5-2l2 2h0 1l-3-3z" class="J"></path><path d="M419 829l11-10c1 0 2-1 3 0l-5 5h-1l-6 6h-2v-1z" class="e"></path><path d="M423 818c3 1 5-2 7-3v-1c1 0 2 0 3-1l-2 2v2l-2 1v-1c-3 1-4 3-6 5v1h0c-1 1-1 2-1 2l-1 1c-1 1-2 2-2 3v1h-1c-1-2-2-3-2-5 2-3 5-5 7-7z" class="g"></path><path d="M436 814h-1c0-1 2-2 2-4l7-8c2 0 2 0 3 1 0 3-1 4-2 6l-1 1h1c1-1 3-2 4-3 1 1 0 1 0 2 1 1 3 3 4 3 0 2 0 3 1 4l-1 1h0l-1 2v2c-1 3-3 6-5 9h-2-1v-1c0-1-1-1-2-2s-2 0-3 1l-5 4h-1v-1-1-1c1 0 1 1 2 1 0-2-2-3-2-5l2-2h0c0 2 1 2 1 3h2c1-3 4-5 4-7-2-2-3-3-4-5 0-1 0-1-1-1l-1 1z" class="U"></path><path d="M534 195c1 0 1 0 2 1 1-2 2-2 4-3l-3 4-1 1v5 6l1 1c-1 1-1 1 0 2l-1 2h3c-3 4-2 14-2 19 0 4 1 8 1 12l-1 2v1l2-1v4c0 2 1 4 1 5v2l1 1h2v-1c2-1 4 0 7-1 1-1 2-1 3-1h2 1l-3 3h-2l1 1h1l1 2v3 6c-1 1-1 1-2 3 0 1 0 2 1 4l3 3c-2 0-3-2-4-3-1 2 0 4-1 6h0c-1-1-1-3-1-4-2 1-1 3-1 4 1 2 1 6 1 8 0 3 0 5 1 8h0v-4c0-1 0-2 1-2l1 1c-1 2-1 3 0 5h1c0-1 1-1 1-1v8 7h-2c-1 0-1 0-1 1v10l-1-1v2c1 1 1 0 3 0v1c-3 0-3 0-5-2h-1l-1-1c-1-1-1-1-2-1-2 0-2 0-3 1v4 5 1h-1v-2h-1c0 1-1 2-1 3 1 1 1 0 1 2l-2-1c-1 2-1 3-3 4h-1c-1 1-2 2-3 4l1 1v1l-1 1h0-1l-1 1v2h0l-1 1v3l-2-1v1 2c0 1 0 1-1 2 0 1 1 2 2 3v1c0 1-2 2-2 2 0 2 1 3 0 5v12c0 2 0 5 1 7l-2 1v6l-1-1v-3c-1 0-2 1-3 2h0c0 2 0 3-1 4v3 3h-2l-2-1v-1h-3l1-2v-3-1c1-2 1-3 0-5h0v-1-1-1-2c0-3 0-6-1-9h1v-1c0-2 0-3-1-4v-1-1c-1-2-1-3-1-5v-3l-3-2v-1h-3c-1-2-2-3-3-4h0c0-2-1-2 0-3 0-2 2-3 3-5l2 1h1l1-1v-67-6-2c-1-1-1-2-1-3v-11-20-3c0-3 0-5 1-8l-1-4v-7c-1-2-1-4-1-6v-12l1 1c1 0 2-1 3 0h0c2 0 3 0 5 1l-1 1h-2c0 1 0 1 1 2 3 0 7 0 10-1-1 0-1-1-2-1v-1h0l1-2h1 2 2c2 0 4-1 6-2z" class="AN"></path><path d="M516 292v-8c0-1 1-1 1-2 0-6-1-21 2-25v15c-1 5 0 9 0 14v12 11c-1-3 0-7-1-10h-1v7c-1-5 0-10-1-14z" class="AU"></path><path d="M508 198c1 0 2-1 3 0h0c2 0 3 0 5 1l-1 1h-2c0 1 0 1 1 2h-2-2l1 2-1 1h1v5 2c1 1 0 3 0 5l1 3c0 5-1 25 1 28l-1 2v1 3c2 2 1 7 1 9-1-1-2-1-2-2v-2-8-3-1h-1l-1-21h0l-1-4v-7c-1-2-1-4-1-6v-12l1 1z" class="AM"></path><path d="M508 198c1 0 2-1 3 0h0c2 0 3 0 5 1l-1 1h-2c0 1 0 1 1 2h-2l-1-1h-3v2h0c1 1 0 2 1 4 0 3-1 6 0 9v6c1 1 0 2 0 4h0l-1-4v-7c-1-2-1-4-1-6v-12l1 1z" class="AG"></path><path d="M514 202c3 0 7 0 10-1 1 2 1 4 1 6l-1 1 1 3c-1 0-2 1-3 2h-1c-1 2-3 3-5 4v-1h-1c-1 3 1 8-1 11l-2-2v-5l-1-3c0-2 1-4 0-5v-2-5h-1l1-1-1-2h2 2z" class="u"></path><path d="M521 213c-1 1-3 1-4 1v1h-1c-1-2-1-3-2-4h-1c1-1 2-2 4-2s5-1 7-1l1 3c-1 0-2 1-3 2h-1z" class="n"></path><path d="M514 202c3 0 7 0 10-1 1 2 1 4 1 6h-2c-3 0-6 1-8 0-1-1-2-1-3-2 3-2 6-1 9-1l-1-1h-6v-1z" class="q"></path><path d="M521 213h1c1-1 2-2 3-2v12c-1 1 0 1 0 2l-1 1v8 2h0v3c0 1 0 4-1 6h0 1v1 2l-3 2c-1 1-3 2-3 3v3h-1l-1-23v-10-6c2-1 4-2 5-4z" class="o"></path><path d="M516 223c1 0 1 0 3-1h1l1 1h-1c0 1-1 2-1 3 1 1 3 1 4 2-2 0-2 0-4 1 1 1 0 2 0 3s3 0 1 2l-1 2h0v2l-1 2h2c0 1-1 1-1 2 1 1 1 1 0 1 0 2 1 5 1 6s1 1 1 1c-1 1-3 2-3 3v3h-1l-1-23v-10z" class="i"></path><path d="M513 263v3c1 3 1 6 0 9 0 2 0 4 1 6s1 7 0 9v2l1 1v-1-2 2h1c1 4 0 9 1 14v-7h1c1 3 0 7 1 10v-11c0-1 0-3 1-4 0 3-1 10 1 12l1 1v8 3c0 4 1 8 0 12-1 2 0 5-1 8v1h-2-2 0l-3 1c0-2 1-2 0-3l-1-14v-4-18c0-3 1-6 0-9-1-2-1-3-1-4v-8c0-6 0-11 1-17z" class="AM"></path><path d="M517 306v-7h1c1 3 0 7 1 10-1 5-1 10-1 14 0 2 0 4-1 5v1-23z" class="AH"></path><path d="M519 298c0-1 0-3 1-4 0 3-1 10 1 12l1 1v8 3c0 4 1 8 0 12-1 2 0 5-1 8v1h-2-2c-1-4-1-7 0-10v-1c1-1 1-3 1-5 0-4 0-9 1-14v-11z" class="AO"></path><path d="M518 323c1 4 1 7 1 10v4h1c1-1 0-4 0-6h1v7 1h-2-2c-1-4-1-7 0-10v-1c1-1 1-3 1-5z" class="AU"></path><path d="M509 226h0l1 21h1v1 3 8 2c0 1 1 1 2 2h0c-1 6-1 11-1 17v8c0 1 0 2 1 4 1 3 0 6 0 9v18 4l1 14-1-1v-2c-1-3-1-5-2-7v20c0 1 0 2-1 4 0 2 0 5 1 7v3l-3-2v-1h-3c-1-2-2-3-3-4h0c0-2-1-2 0-3 0-2 2-3 3-5l2 1h1l1-1v-67-6-2c-1-1-1-2-1-3v-11-20-3c0-3 0-5 1-8z" class="AJ"></path><path d="M511 316v11 20c0 1 0 2-1 4 0 2 0 5 1 7v3l-3-2v-1h-3c-1-2-2-3-3-4 1-1 2-1 3-1 0 1 1 2 2 3l1-1c1-3 1-5 1-8 1-3 1-7 1-11v-9-10l1-1z" class="AG"></path><path d="M510 247h1v1 3 8 2c0 1 1 1 2 2h0c-1 6-1 11-1 17v8c0 1 0 2 1 4 1 3 0 6 0 9v18 4l1 14-1-1v-2c-1-3-1-5-2-7v-11l-1-69z" class="AO"></path><path d="M524 249l1-1c1 0 2 0 2-1 1-1 2-1 3-1v7l2 3v6c-1 1-1 2-1 2 0 1-1 2-1 3l-3-3 1 11v6h0c0 6 1 13 0 20-1-2 0-4-1-5-2 3-1 10-1 14h0c-1 1-3 1-3 2l-1 3v-8l-1-1c-2-2-1-9-1-12-1 1-1 3-1 4v-12c0-5-1-9 0-14v-15-1c1-4 2-5 5-7z" class="AH"></path><path d="M524 249l1-1c1 0 2 0 2-1 1-1 2-1 3-1v7l2 3v6c-1 1-1 2-1 2 0 1-1 2-1 3l-3-3-2-2c0-2-2-4-3-6l-1-1c-1 2-1 3-1 4 0 3 1 11-1 13v-15-1c1-4 2-5 5-7z" class="o"></path><path d="M522 256c1 0 2 0 3 1h1l-1-1h1v2l1 1-2 3c0-2-2-4-3-6z" class="f"></path><path d="M530 253l2 3v6c-1 1-1 2-1 2l-2-1v-2c1-1 1-2 1-3v-5z" class="i"></path><path d="M522 297v-11-20c0-2 0-5 1-7v1c1 1 2 3 2 5v15c1 0 2 1 3 1h0c0 6 1 13 0 20-1-2 0-4-1-5-2 3-1 10-1 14h0c-1 1-3 1-3 2l-1 3v-8l-1-1c0-3 0-6 1-9z" class="o"></path><path d="M522 297c1 4 1 6 0 10l-1-1c0-3 0-6 1-9z" class="r"></path><path d="M525 280c1 0 2 1 3 1h0c0 6 1 13 0 20-1-2 0-4-1-5-2 3-1 10-1 14h0l-1-30z" class="AU"></path><path d="M511 327c1 2 1 4 2 7v2l1 1c1 1 0 1 0 3l3-1h0 2v5c0 5 0 11-1 16l1 5h0l1-3h1v3s1 0 1 1l1 2v-3h1v1c0 1 0 2 1 3v12c0 2 0 5 1 7l-2 1v6l-1-1v-3c-1 0-2 1-3 2h0c0 2 0 3-1 4v3 3h-2l-2-1v-1h-3l1-2v-3-1c1-2 1-3 0-5h0v-1-1-1-2c0-3 0-6-1-9h1v-1c0-2 0-3-1-4v-1-1c-1-2-1-3-1-5v-3-3c-1-2-1-5-1-7 1-2 1-3 1-4v-20z" class="z"></path><path d="M513 389c0-1 1-1 1-2s0-2 1-3l-1-1c0-1 0-2 1-3 0 1 0 1 1 1l1-3h0c0 2 0 4 1 5-1 2-2 3-2 4l-3 3h0v-1z" class="AC"></path><path d="M517 339h2v5c0 5 0 11-1 16v8h-1v-14c-1 0-1 1-1 2h0v-1-4c-1 1-1 1-2 1l1-2v-6c0-1-1-1-1-2v-1-1l3-1h0z" class="AH"></path><path d="M514 340l3-1v15c-1 0-1 1-1 2h0v-1-4c-1 1-1 1-2 1l1-2v-6c0-1-1-1-1-2v-1-1z" class="u"></path><path d="M511 327c1 2 1 4 2 7v2l1 1c1 1 0 1 0 3v1 1c0 1 1 1 1 2v6l-1 2c1 2 0 3 0 5v7l-1 5v6c0-2 0-3-1-4v-1-1c-1-2-1-3-1-5v-3-3c-1-2-1-5-1-7 1-2 1-3 1-4v-20z" class="AH"></path><path d="M514 341v1c0 1 1 1 1 2v6l-1 2c1 2 0 3 0 5v7l-1 5c0-2 0-4-1-6h0l1-2v-4-2h0v-7-3c0-2 0-2 1-4z" class="AN"></path><path d="M517 368h1v-8l1 5h0c1 1 1 2 1 4l-1 1v6 21 3 3h-2l-2-1v-1h-3l1-2v-3-1c1-2 1-3 0-5l3-3c0-1 1-2 2-4-1-1-1-3-1-5v-10z" class="r"></path><path d="M518 383v9h-2v-5c0-1 1-2 2-4z" class="f"></path><path d="M513 390l3-3v5h2v4c0 1 0 2 1 4v3h-2l-2-1v-1h-3l1-2v-3-1c1-2 1-3 0-5z" class="i"></path><path d="M516 392h2v4l-2 1v-5z" class="AD"></path><path d="M518 396c0 1 0 2 1 4v3h-2l-2-1v-1-1c0-1 0-2 1-3l2-1z" class="n"></path><path d="M519 365l1-3h1v3s1 0 1 1l1 2v-3h1v1c0 1 0 2 1 3v12c0 2 0 5 1 7l-2 1v6l-1-1v-3c-1 0-2 1-3 2h0c0 2 0 3-1 4v-21-6l1-1c0-2 0-3-1-4z" class="AD"></path><path d="M523 368v-3h1v1c0 1 0 2 1 3v12c0 2 0 5 1 7l-2 1c-1-4 0-9-1-13v-8z" class="o"></path><path d="M534 195c1 0 1 0 2 1 1-2 2-2 4-3l-3 4-1 1v5 6l1 1c-1 1-1 1 0 2l-1 2h3c-3 4-2 14-2 19 0 4 1 8 1 12l-1 2v1l2-1v4c0 2 1 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 2 1-2 1-5 1-7 1v1l-1-1v-6l-2-3v-7c-1 0-2 0-3 1 0 1-1 1-2 1l-1 1v-1-2-1h-1 0c1-2 1-5 1-6v-3h0v-2-8l1-1c0-1-1-1 0-2v-12l-1-3 1-1c0-2 0-4-1-6-1 0-1-1-2-1v-1h0l1-2h1 2 2c2 0 4-1 6-2z" class="AD"></path><path d="M526 245c1-2 1-4 1-6 2-4 1-9 1-13 1-2 0-3 1-4h1v3c-1 3-1 6 0 8v3h0v1c0 3-1 5-3 8h-1z" class="n"></path><path d="M532 233v13h-2c-1 0-2 0-3 1 0 1-1 1-2 1l-1 1v-1-2-1h-1 0c1-2 1-5 1-6 1-1 0 0 1 0 1 2 0 5 1 6h1c2-3 3-5 3-8 1-1 2-2 2-4z" class="f"></path><path d="M534 195c1 0 1 0 2 1 1-2 2-2 4-3l-3 4-1 1v5l-2 2v7c-1 0-1 0-1-1h-1c0-2 1-10 0-11l-3 1v2c-1-1-1 0-2-1h0c-2 7 0 14-1 21h-1v-12l-1-3 1-1c0-2 0-4-1-6-1 0-1-1-2-1v-1h0l1-2h1 2 2c2 0 4-1 6-2z" class="S"></path><path d="M533 211c0-3-1-9 1-12h0c0-2 0-1 1-2h2l-1 1v5l-2 2v7c-1 0-1 0-1-1z" class="AK"></path><path d="M536 203v6l1 1c-1 1-1 1 0 2l-1 2h3c-3 4-2 14-2 19 0 4 1 8 1 12l-1 2v1l2-1v4c0 2 1 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 2 1-2 1-5 1-7 1v1l-1-1v-6l-2-3v-7h2v-13-3-6-13h1c0 1 0 1 1 1v-7l2-2z" class="AI"></path><path d="M532 224l2 1v2h0c0 2 0 2-1 4v1l-1-2v-6z" class="S"></path><path d="M530 246h2v10l-2-3v-7z" class="AC"></path><path d="M536 203v6c-1 2-1 3-1 5h0v4l-1 1h0v4 2l-2-1v-13h1c0 1 0 1 1 1v-7l2-2z" class="l"></path><path d="M536 209l1 1c-1 1-1 1 0 2l-1 2h3c-3 4-2 14-2 19 0 4 1 8 1 12l-1 2v1l2-1v4c0 2 1 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 2 1-2 1-5 1-7 1 1 0 2-1 2-1-1-6 1-13 0-19v-4c0-3 1-7 1-11s0-8-1-13h0c0-2 0-3 1-5z" class="w"></path><path d="M537 247v1l2-1v4c0 2 1 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 2 1-2 1-5 1-7 1 1 0 2-1 2-1 4-4 1-10 2-14z" class="AK"></path><path d="M555 256h1l-3 3h-2l1 1h1l1 2v3 6c-1 1-1 1-2 3 0 1 0 2 1 4l3 3c-2 0-3-2-4-3-1 2 0 4-1 6h0c-1-1-1-3-1-4-2 1-1 3-1 4 1 2 1 6 1 8 0 3 0 5 1 8h0v-4c0-1 0-2 1-2l1 1c-1 2-1 3 0 5h1c0-1 1-1 1-1v8 7h-2c-1 0-1 0-1 1v10l-1-1v2c1 1 1 0 3 0v1c-3 0-3 0-5-2h-1l-1-1c-1-1-1-1-2-1-2 0-2 0-3 1v4 5 1h-1v-2h-1c0 1-1 2-1 3 1 1 1 0 1 2l-2-1c-1 2-1 3-3 4h-1c-1 1-2 2-3 4l1 1v1l-1 1h0-1l-1 1v2h0l-1 1v3l-2-1v1 2c0 1 0 1-1 2 0 1 1 2 2 3v1c0 1-2 2-2 2 0 2 1 3 0 5-1-1-1-2-1-3v-1h-1v3l-1-2c0-1-1-1-1-1v-3h-1l-1 3h0l-1-5c1-5 1-11 1-16v-5h2v-1c1-3 0-6 1-8 1-4 0-8 0-12v-3l1-3c0-1 2-1 3-2h0c0-4-1-11 1-14 1 1 0 3 1 5 1-7 0-14 0-20h0v-6l-1-11 3 3c0-1 1-2 1-3 0 0 0-1 1-2l1 1v-1c2 0 5 0 7-1-1-1-1-1-2-1 1-1 1-2 1-3l1-1v2l1 1h2v-1c2-1 4 0 7-1 1-1 2-1 3-1h2z" class="i"></path><path d="M534 340v-1c-1-1-1-1-1-2l1-1 1-1 3 1c-1 2-1 3-3 4h-1z" class="q"></path><path d="M528 281v1c1 1 1 2 2 3 0 1 0 2-1 3 2 1 1 4 2 7l-1 1c1 2 0 3 0 5s2 2 1 4l-1 1v2h0c1 1 1 1 2 1l-2 3c-2 3 1 9-1 13l2 1c-1 1-2 2-2 3v1c1 1 1 1 1 2h-1v-1c0-1-1-1-2-2l1-28c1-7 0-14 0-20z" class="u"></path><path d="M541 305c1 0 1 1 1 2-1 1-1 1-1 2 1 2 1 4 1 6s0 3-1 4v2c1 2 0 4 1 7v5 1h-1v-2h-1c0 1-1 2-1 3 1 1 1 0 1 2l-2-1-3-1-1-1c-1-1-1-2-1-3l-1-1c-1-1 1-2 1-3 0 0-1 0-1-1v-2l1-1c1-1 1-2 0-3v-8l1-1c0-2-1-3-2-4l1-1 3-1 2 1h3v-1z" class="r"></path><path d="M541 305c1 0 1 1 1 2-1 1-1 1-1 2 1 2 1 4 1 6s0 3-1 4v-2c0-1 0-1-1-2 1-1 1-2 1-2-1-1-2-2-2-3l-1-1h-1c0 1 0 0-1 1v3h-1c0-1-1-2 0-3v-2h0c1-1 2-1 3-2h3v-1z" class="w"></path><path d="M539 279c2 1 3 2 4 4v4 2l1 1c0 2 0 5 1 8v1 6c-1 3-1 7-2 10h-1c0-2 0-4-1-6 0-1 0-1 1-2 0-1 0-2-1-2v1h-3l-2-1-3 1c-1-1-1-1-1-2s1-1 1-2h-1v-7c0-2 1-4 1-6h-1v-2c0-1 1-2 2-3 1-2 3-2 4-4l1-1z" class="u"></path><path d="M535 284c1 0 2 0 3 1 0 1 0 1-1 3h-1l-1-2v-2z" class="w"></path><path d="M538 289c1 1 2 1 3 1 0 1-1 2-2 2v3h-1l-1-1c0-2 0-3 1-5z" class="r"></path><path d="M539 279c2 1 3 2 4 4v4 2l-1-2-1 1c0 1 0 1 1 1l-1 1c-1 0-2 0-3-1h-1l2-2c0-2 0-5-1-7l1-1z" class="f"></path><path d="M544 290c0 2 0 5 1 8v1 6c-1 3-1 7-2 10h-1c0-2 0-4-1-6 0-1 0-1 1-2 0-1 0-2-1-2 1-2 0-2 0-3v-1c0-1 1-1 3-2v-4c-1-2-1-3 0-5z" class="q"></path><path d="M533 289h3 0c-2 3-2 3-3 6l1 1 2-1h1c0 4 0 6-1 10h0l-3 1c-1-1-1-1-1-2s1-1 1-2h-1v-7c0-2 1-4 1-6z" class="f"></path><path d="M540 261c1-1 2-1 3-1h1v5c0 1 1 2 1 3 1 2 0 6 0 8 0 3 0 5-1 7l1 2-2 2v-4c-1-2-2-3-4-4l-1 1c-1 2-3 2-4 4-1 1-2 2-2 3l-1-1v-6l-2-1-1-4-1-11 3 3c0-1 1-2 1-3 0 0 0-1 1-2l1 1v-1c2 0 5 0 7-1z" class="w"></path><path d="M543 260h1v5c0 1 1 2 1 3 1 2 0 6 0 8 0 3 0 5-1 7l1 2-2 2v-4-15-8z" class="AF"></path><path d="M532 262l1 1h0 5 1l1 1c0 2 0 5-1 8v1 3c-1-1-1-2-1-3l-1-1v-1l-2-2c-2 0-3 1-4 1l1-1-2-2c0-1 1-2 1-3 0 0 0-1 1-2z" class="n"></path><path d="M527 264l3 3 2 2-1 1c1 0 2-1 4-1l2 2v1l1 1c0 1 0 2 1 3v3l-1 1c-1 2-3 2-4 4-1 1-2 2-2 3l-1-1v-6l-2-1-1-4-1-11z" class="f"></path><path d="M537 272l1 1c0 1 0 2 1 3v3l-1 1c-1 2-3 2-4 4-1 1-2 2-2 3l-1-1v-6h0c0-2 1-2 3-2 0-1 1-2 1-3 1 0 1-1 1-2l1-1z" class="AD"></path><path d="M534 278c0 1 0 2-1 3-1 0-1 1-1 2h0l2 1c-1 1-2 2-2 3l-1-1v-6h0c0-2 1-2 3-2z" class="o"></path><path d="M526 310c0-4-1-11 1-14 1 1 0 3 1 5l-1 28v12c0 3 1 6 0 8s-1 2-1 4v1 2c0 1 0 1-1 2 0 1 1 2 2 3v1c0 1-2 2-2 2 0 2 1 3 0 5-1-1-1-2-1-3v-1h-1v3l-1-2c0-1-1-1-1-1v-3h-1l-1 3h0l-1-5c1-5 1-11 1-16v-5h2v-1c1-3 0-6 1-8 1-4 0-8 0-12v-3l1-3c0-1 2-1 3-2h0z" class="z"></path><path d="M526 310c0-4-1-11 1-14 1 1 0 3 1 5l-1 28v12c0 3 1 6 0 8-1-2-1-7-1-10v-29z" class="AS"></path><path d="M522 318c3 3 2 11 2 15v3l1 1c1 0 1 1 1 2 0 3 0 8 1 10-1 2-1 2-1 4v1c0-1 0-2-1-3h0v-2-1c-1-1-1-2-1-3l-1 1v3h0c-1 0-1 0-2-1v-3-3h-1v2h-1v-5h2v-1c1-3 0-6 1-8 1-4 0-8 0-12z" class="AC"></path><path d="M519 344h1v-2h1v3 3c1 1 1 1 2 1h0v-3l1-1c0 1 0 2 1 3v1 2h0c1 1 1 2 1 3v2c0 1 0 1-1 2 0 1 1 2 2 3v1c0 1-2 2-2 2 0 2 1 3 0 5-1-1-1-2-1-3v-1h-1v3l-1-2c0-1-1-1-1-1v-3h-1l-1 3h0l-1-5c1-5 1-11 1-16z" class="o"></path><path d="M523 349h0v-3l1-1c0 1 0 2 1 3v1 2h0c1 1 1 2 1 3v2c0 1 0 1-1 2 0 1 1 2 2 3v1c0 1-2 2-2 2 0 2 1 3 0 5-1-1-1-2-1-3v-1h-1v3l-1-2c0-2 0-4 1-5 1-4 0-8 0-12z" class="i"></path><path d="M555 256h1l-3 3h-2l1 1h1l1 2v3 6c-1 1-1 1-2 3 0 1 0 2 1 4l3 3c-2 0-3-2-4-3-1 2 0 4-1 6h0c-1-1-1-3-1-4-2 1-1 3-1 4 1 2 1 6 1 8 0 3 0 5 1 8h0v-4c0-1 0-2 1-2l1 1c-1 2-1 3 0 5h1c0-1 1-1 1-1v8 7h-2c-1 0-1 0-1 1v10l-1-1v2c1 1 1 0 3 0v1c-3 0-3 0-5-2h-1l-1-1c-1-1-1-1-2-1-2 0-2 0-3 1v4c-1-3 0-5-1-7v-2c1-1 1-2 1-4h1c1-3 1-7 2-10v-6-1c-1-3-1-6-1-8l-1-1v-2l2-2-1-2c1-2 1-4 1-7 0-2 1-6 0-8 0-1-1-2-1-3v-5h-1c-1 0-2 0-3 1-1-1-1-1-2-1 1-1 1-2 1-3l1-1v2l1 1h2v-1c2-1 4 0 7-1 1-1 2-1 3-1h2z" class="S"></path><path d="M553 265h1v6c-1 1-1 1-2 3 0 1 0 2 1 4l3 3c-2 0-3-2-4-3h0c-2-2-2-5-2-8v-1c1-1 1-2 2-3h1v-1z" class="l"></path><path d="M545 276l1 4c0-2 0-4 1-5v-1c0 9 0 18-1 27v4h-1v-6-1c-1-3-1-6-1-8l-1-1v-2l2-2-1-2c1-2 1-4 1-7z" class="AQ"></path><path d="M555 256h1l-3 3h-2l1 1h1l1 2v3h-1-2c-1-1-1-1-1-2-1 1-1 2-1 3 0 2-1 5-2 7v1 1c-1 1-1 3-1 5l-1-4c0-2 1-6 0-8 0-1-1-2-1-3v-5h-1c-1 0-2 0-3 1-1-1-1-1-2-1 1-1 1-2 1-3l1-1v2l1 1h2v-1c2-1 4 0 7-1 1-1 2-1 3-1h2z" class="AB"></path><path d="M544 260h3v3c-1 1-2 1-3 2v-5z" class="y"></path><path d="M544 265c1-1 2-1 3-2v10 1 1c-1 1-1 3-1 5l-1-4c0-2 1-6 0-8 0-1-1-2-1-3z" class="t"></path><path d="M546 301c1 2 2 3 2 6h0c1 3 0 6 1 8l3 1v-1 10l-1-1v2c1 1 1 0 3 0v1c-3 0-3 0-5-2h-1l-1-1c-1-1-1-1-2-1-2 0-2 0-3 1v4c-1-3 0-5-1-7v-2c1-1 1-2 1-4h1c1-3 1-7 2-10h1v-4z" class="n"></path><path d="M546 301c1 2 2 3 2 6 0 2 0 5-1 7-1 1-1 3-1 5v-14-4z" class="l"></path><path d="M548 307h0c1 3 0 6 1 8l3 1v-1 10l-1-1v2c1 1 1 0 3 0v1c-3 0-3 0-5-2h-1l-1-1c-1-1-1-1-2-1 0-2 0-3 1-4 0-2 0-4 1-5 1-2 1-5 1-7z" class="AI"></path><path d="M552 315v10l-1-1v2c1 1 1 0 3 0v1c-3 0-3 0-5-2v-3c1-2 1-4 3-6v-1z" class="l"></path><path d="M555 320l1 1v8l1 12v4l2-1 1 1 1 2h-1c-1 2-2 2-2 4v1l1 1 2 1c1 0 2-1 3-1s2 0 4-1l2 2h1c1-1 2-1 3-1s2 1 3 1v1s-1 1-1 2v1c2-1 2-1 4-1v3h-1 0c-1 1-1 1-1 2l2 1c1-2 1-3 1-5 0-1-1-1-1-2v-1h1c1 3 1 5 1 7l13 2h0l-1 1h1c0 1 0 2-1 3h0c-1-1-2 0-3 0-3 0-6-1-9-1-1-1-2-1-4 0l-1 1v1c1 1 1 2 0 3h-1c0 2 1 4 0 6 2 1 4 1 6 1v1 1c-2 1-4 1-6 1h-1v21 41 22c1 4 1 8 2 11 1 1 2 2 3 4 1-1 1-1 1-2 1-1 1-2 3-3 3 4 3 7 4 12-1 0-2-1-3-2h0l-1 1c-2 0-3 1-5 1h-1-1c0 1 0 1-1 3h-1v1h1c-2 1-3 1-5 1s-3 1-5 2h-2v1c1 2 0 4 0 6 0-2 0-3-1-4-1 2-2 4-2 6v2l-1-1c0-4-1-8-2-12h0l-1 2c-1-2-1-5-1-7l-1 1c-1-1-1-4-1-5-1-6-2-8-5-13h0c0-1-1-3-2-4v-1c-1 0-1-1-2-1 0-1 0-2 1-3l1 1 6 2v-1c-2 0 0-2-1-3h-2l-1 1h-2l1-1v-1l-2-1v-2c-1-1-1-1-1-2s-1-2-1-3h0 0c1-3 1-8 0-10l-1-15v-1c-1 0-2-1-3-1l-1-1-1 1-2 1c0-2 0-4-1-5v-1l-1-3v-2c1-2 1-3 1-5l-1 1h-2v1c0 1 0 0-1 1h0l-1-3v-4c0-1 0-1-1-1v-1h-1v4l-1-1v-9-1c-1-3 1-7 0-10l-1 1c-1-2-1-5-1-7v-12c1-2 0-3 0-5 0 0 2-1 2-2v-1c-1-1-2-2-2-3 1-1 1-1 1-2v-2-1l2 1v-3l1-1h0v-2l1-1h1 0l1-1v-1l-1-1c1-2 2-3 3-4h1c2-1 2-2 3-4l2 1c0-2 0-1-1-2 0-1 1-2 1-3h1v2h1v-1-5-4c1-1 1-1 3-1 1 0 1 0 2 1l1 1h1c2 2 2 2 5 2v-1c-2 0-2 1-3 0v-2l1 1c2-2 2-3 3-5z" class="T"></path><path d="M557 455h0c1 1 1 4 1 6l1 1c1 1 1 1 1 3h-1c0 1-1 2-1 3h-1c-1-4 0-9 0-13z" class="G"></path><path d="M555 472c2 7 2 14 3 21l-1 2c-1-2-1-5-1-7l-1 1c-1-1-1-4-1-5 2-2 1-9 1-12z" class="E"></path><path d="M545 465c0-1 0-2 1-3l1 1 2 2c2 2 6 4 6 7s1 10-1 12c-1-6-2-8-5-13h0c0-1-1-3-2-4v-1c-1 0-1-1-2-1z" class="C"></path><path d="M557 468h1l3 6c2 5 2 11 3 17h-2-1c-1-1-1-4-1-5-1-4-1-9-3-13-1-1-1-2-2-3l2-2z" class="H"></path><path d="M557 419c0 2 1 4 1 6l1 1v2c0 1 1 2 1 3v20 10l-1 1-1-1c0-2 0-5-1-6h0v-36z" class="D"></path><path d="M547 420l2 2v3h1l1-1c1-1-1-1 1 0h1c0 2 1 4 0 7-2 0-4 0-6-2h-1l1-2h-1v1l-1-1c1-1 1-2 1-4h0l-1 1c0 1 0 1-1 2 0 2 1 4 1 6v4l1 1h1c1 0 2 0 3 1 1 0 1 0 1 1 1 1 1 1 1 2l-1 1h-2c-2 0-4-1-5 0l-1-15v-1c-1-1-1-1 0-2l1-2h3v-2z" class="M"></path><defs><linearGradient id="x" x1="554.116" y1="404.554" x2="562.489" y2="403.018" xlink:href="#B"><stop offset="0" stop-color="#232120"></stop><stop offset="1" stop-color="#3c3831"></stop></linearGradient></defs><path fill="url(#x)" d="M558 380l1 4c0-1 0-1 1-1v8 16 8 16c0-1-1-2-1-3v-2l-1-1c0-2-1-4-1-6-1-3 0-7 0-10 0-7-1-14 0-21 2-2 1-5 1-8z"></path><path d="M557 345l2-1 1 1 1 2h-1c-1 2-2 2-2 4v1l1 1 2 1c1 0 2-1 3-1s2 0 4-1l2 2h1c1-1 2-1 3-1s2 1 3 1v1s-1 1-1 2l-2-1v1h-2v4c-1-1-1-1-2-1l-1 1v1l-2-2h0c-1 0-1 1-1 2h-1v-1l-1-1c-1 1-3 2-3 3h1v1h0l-2 2c0 1 0 1-1 1h0l-1 1v7 5c0 3 1 6-1 8v-43z" class="B"></path><path d="M568 352l2 2h1c1-1 2-1 3-1s2 1 3 1v1s-1 1-1 2l-2-1v1h-2v4c-1-1-1-1-2-1l-1 1v1l-2-2 2-1c0-2 0-3-1-4l-1 1h0c-1 0-2 0-3-1v-1c-1 1-1 1-2 1s-2 0-3 1h0c0-1 0-1-1-2 0 0 0-1 1-1l2 1c1 0 2-1 3-1s2 0 4-1z" class="F"></path><path d="M545 432c2 1 3 2 5 2h3c1 4 1 26 0 30-2 0 0-2-1-3h-2l-1 1h-2l1-1v-1l-2-1v-2c-1-1-1-1-1-2s-1-2-1-3h0 0c1-3 1-8 0-10 1-1 3 0 5 0h2l1-1c0-1 0-1-1-2 0-1 0-1-1-1-1-1-2-1-3-1h-1l-1-1v-4z" class="G"></path><path d="M545 432c2 1 3 2 5 2-1 0-2 1-2 1-1 1-2 1-3 1v-4z" class="O"></path><path d="M546 457c0-1 0-3 1-5 1 0 2 1 2 2 1 2 1 5 1 7l-1 1h-2l1-1v-1l-2-1v-2z" class="R"></path><path d="M559 462l1-1c0 1 1 2 2 4l-1 1v2l1 1c-1 3 1 6 2 9l1 1c0 1-1 2 0 3 0 0 1 1 1 2l1-1c0-1 0-2 1-3 0-2 0-5-1-7l1 3h1 1c1-1 0-1 1-1l1 1v3c2 2 1 3 1 5h2c1 1 2 2 4 3 2 0 4-1 6-1l-1 1c-2 0-3 1-5 1h-1-1c0 1 0 1-1 3h-1v1h1c-2 1-3 1-5 1s-3 1-5 2h-2v1c1 2 0 4 0 6 0-2 0-3-1-4 1-3 1-5 1-7-1-6-1-12-3-17l-3-6c0-1 1-2 1-3h1c0-2 0-2-1-3z" class="I"></path><path d="M572 479c2 2 1 3 1 5h2c1 1 2 2 4 3 2 0 4-1 6-1l-1 1c-2 0-3 1-5 1h-1-1c0 1 0 1-1 3h-1c0-1 0-3-1-4-2 0-4-1-6 0h-1-1c1-4 3-2 5-4 0-1 0-2 1-4z" class="b"></path><path d="M560 415l1 1v5h1v7 1l1 2h1 0l1-1v3l1 1 1-2v28 13h0c1 2 1 5 1 7-1 1-1 2-1 3l-1 1c0-1-1-2-1-2-1-1 0-2 0-3l-1-1c-1-3-3-6-2-9l-1-1v-2l1-1c-1-2-2-3-2-4v-10-20-16z" class="X"></path><path d="M561 468h4c1 1 1 3 1 4h-1c0-1 0-1-1-2l-2-1-1-1z" class="J"></path><path d="M562 429l1 2h1 0l1-1v3l1 1 1-2v28c-2-4-1-13-1-17-1 1-1 2-1 4v11c0 2 0 3-1 5h-1l-1-1v-21c-1-2 0-10 0-12z" class="W"></path><path d="M562 429l1 2h1 0l1-1v3 6l-1 1h0v1c1 0 0 0 1 1v1l-1-1s-1-1-2-1c-1-2 0-10 0-12z" class="H"></path><path d="M576 357v1c2-1 2-1 4-1v3h-1 0c-1 1-1 1-1 2l2 1c1-2 1-3 1-5 0-1-1-1-1-2v-1h1c1 3 1 5 1 7l13 2h0l-1 1h1c0 1 0 2-1 3h0c-1-1-2 0-3 0-3 0-6-1-9-1-1-1-2-1-4 0l-1 1v1c1 1 1 2 0 3h-1c0 2 1 4 0 6l-1 1-1 2h-1-2c-1 0-2 0-3 1l-1 1v6 3h-4-2l-1-1v-8c-1 0-1 0-1 1l-1-4v-5-7l1-1h0c1 0 1 0 1-1l2-2h0v-1h-1c0-1 2-2 3-3l1 1v1h1c0-1 0-2 1-2h0l2 2v-1l1-1c1 0 1 0 2 1v-4h2v-1l2 1z" class="M"></path><path d="M568 370v-1c0-1 0-3 1-4v1l1 1v-3l1 1h0c0 1 0 2 1 4 1 0 1 0 2 1-1 1-3 1-5 1h-1v-1z" class="O"></path><path d="M558 368h1v2h1 1l1 2c1 0 1 0 2-1v2h2c1 1 0 1 0 2v4l1 1h1 1 2l2 1h-2c-1 0-2 0-3 1l-1 1v6 3h-4-2l-1-1v-8c-1 0-1 0-1 1l-1-4v-5-7z" class="X"></path><path d="M562 380v-1c-1-2 0-3 1-5h1v4l-2 2z" class="c"></path><path d="M558 368h1v2h1v13c-1 0-1 0-1 1l-1-4v-5-7z" class="P"></path><path d="M564 378h2v1l1 1h1 1 2l2 1h-2c-1 0-2 0-3 1l-1 1v6 3h-4-2l1-2v-10l2-2z" class="W"></path><path d="M562 390v-1c1-2 0-5 2-7h1c0 4 1 7-2 10h-2l1-2z" class="b"></path><path d="M576 357v1c2-1 2-1 4-1v3h-1 0c-1 1-1 1-1 2l2 1c1-2 1-3 1-5 0-1-1-1-1-2v-1h1c1 3 1 5 1 7l13 2h0l-1 1h1c0 1 0 2-1 3h0c-1-1-2 0-3 0-3 0-6-1-9-1-1-1-2-1-4 0l-1 1v1c1 1 1 2 0 3h-1c0 2 1 4 0 6l-1 1-1 2h-1l-2-1h-2-1l-1-1 1-1c0-2 1-4-1-5 0-2 0-2 1-3v1h1c2 0 4 0 5-1 1 0 1 0 2-1v-3l1-1h0l2-2-1-1c0 1 0 1-1 1l-1-1h-2c-1-1-4 0-5 0v-1l1-1c1 0 1 0 2 1v-4h2v-1l2 1z" class="C"></path><path d="M568 380l-1-1 1-1c0-2 1-4-1-5 0-2 0-2 1-3v1 1h1 1v1s1 1 1 2c1-1 1-1 1-2l1 1v5c-1 1-1 1-2 1h-2-1z" class="F"></path><path d="M569 380v-2l1-1h1l1 1 1 1c-1 1-1 1-2 1h-2z" class="O"></path><path d="M574 381l1-2 1-1c2 1 4 1 6 1v1 1c-2 1-4 1-6 1h-1v21 41 22c1 4 1 8 2 11 1 1 2 2 3 4 1-1 1-1 1-2 1-1 1-2 3-3 3 4 3 7 4 12-1 0-2-1-3-2h0c-2 0-4 1-6 1-2-1-3-2-4-3h-2c0-2 1-3-1-5v-3l-1-1c-1 0 0 0-1 1h-1-1l-1-3h0v-13-28l-1 2-1-1v-3l-1 1h0-1l-1-2v-1-7h-1v-5l-1-1v-8-16l1 1h2 4v-3-6l1-1c1-1 2-1 3-1h2 1z" class="G"></path><path d="M568 382c1-1 2-1 3-1v1 6h-1l-1-2c0-2-1-3-1-4z" class="O"></path><path d="M567 389v-6l1-1c0 1 1 2 1 4v4c-1 2-1 4-1 6 1 1 2 1 2 2v2c-1-1-1-2-2-2h0l-1-3v-6z" class="D"></path><path d="M567 395h0l1 3h0c1 0 1 1 2 2v24 26c0 4 0 9 1 14h-1-1v-2l1-1v-7l-1-3c0-1-1-1-1-2v15c0 3 0 6-1 9v-13-28-21c1-4 0-9 0-13v-3z" class="M"></path><path d="M567 389v6h0v3c0 4 1 9 0 13v21l-1 2-1-1v-3l-1 1h0-1l-1-2v-1-7h-1v-5l-1-1v-8-16l1 1h2 4v-3z" class="K"></path><path d="M565 404l2-6c0 4 1 9 0 13h-1c0 5 1 10 0 15-1-3 0-4 0-6-1-3-1-5-1-7v-2l-2-2 2-1v-4z" class="X"></path><path d="M564 396c1 0 2-1 3-2v1 3l-2 6v4l-2 1 2 2v2-1c-1 0-2-1-3-2-1-2 0-4 0-6v-7c1 0 2 0 2-1z" class="T"></path><path d="M564 396c1 0 2-1 3-2v1 3l-2 6c0-3 0-5-1-8z" class="S"></path><path d="M567 389v6h0v-1c-1 1-2 2-3 2 0 1-1 1-2 1v7c0 2-1 4 0 6v1c1 1 0 1 1 2-2 3 0 5-1 8h-1v-5l-1-1v-8-16l1 1h2 4v-3z" class="Y"></path><path d="M574 381l1-2 1-1c2 1 4 1 6 1v1 1c-2 1-4 1-6 1h-1v21 41 22c1 4 1 8 2 11 1 1 2 2 3 4 1-1 1-1 1-2 1-1 1-2 3-3 3 4 3 7 4 12-1 0-2-1-3-2h0c-2 0-4 1-6 1-2-1-3-2-4-3h-2c0-2 1-3-1-5v-3l-1-1 1-1v-2l2-1v-2-60c0-9 1-19 0-28z" class="a"></path><path d="M574 469c0 3 1 6 1 9 2 0 2 1 3 2l-1 1-2-2v1h0-1v-1c-1-1-1-2-2-3l-1-1 1-1v-2l2-1v-2z" class="D"></path><path d="M545 323c1 0 1 0 2 1l1 1h1c2 2 2 2 5 2l-1 68v29h-1c-2-1 0-1-1 0l-1 1h-1v-3l-2-2v2h-3l-1 2c-1 1-1 1 0 2-1 0-2-1-3-1l-1-1-1 1-2 1c0-2 0-4-1-5v-1l-1-3v-2c1-2 1-3 1-5l-1 1h-2v1c0 1 0 0-1 1h0l-1-3v-4c0-1 0-1-1-1v-1h-1v4l-1-1v-9-1c-1-3 1-7 0-10l-1 1c-1-2-1-5-1-7v-12c1-2 0-3 0-5 0 0 2-1 2-2v-1c-1-1-2-2-2-3 1-1 1-1 1-2v-2-1l2 1v-3l1-1h0v-2l1-1h1 0l1-1v-1l-1-1c1-2 2-3 3-4h1c2-1 2-2 3-4l2 1c0-2 0-1-1-2 0-1 1-2 1-3h1v2h1v-1-5-4c1-1 1-1 3-1z" class="q"></path><path d="M533 397c3-1 2-2 3-5h0c-1-3 0-7 0-9 1 4 1 7 0 12 0 1 0 2-1 3l1 1-1 3c-1 3-1 6-1 9h-2v1c0 1 0 0-1 1h0l-1-3v-4-9c0 1 0 2 2 3l1-1-1-2h1z" class="n"></path><path d="M538 354c2 0 2-1 4-1 0 2 0 2 1 4v2 1c0 1 0 1-1 2h0c2 6 1 13 1 19h-1l-1-1-2 1h-1v-23-4z" class="c"></path><path d="M532 356l1-1 1 1v1l1 1 1-1v1c0 5 1 9 0 14h1v2c1 3-1 6-1 9 0 2-1 6 0 9h0c-1 3 0 4-3 5h-1v-8-8-17-8z" class="AC"></path><path d="M533 387c2-3 1-10 2-14h0c-1-1-1-3-1-4h1v3h1 1v2c1 3-1 6-1 9 0 2-1 6 0 9h0c-1 3 0 4-3 5h-1v-8l1-2z" class="i"></path><path d="M532 389l1-2c1 3 1 6 0 8v2h-1v-8z" class="f"></path><path d="M536 399v4 1 1l1 1h1v1c1 1 2 1 2 1 2 0 2-1 3 1v2l2 1 7 4c-1 1-1 2-1 3-1 0-2 0-2-1-1 1-2 1-2 2v2h-3l-1 2c-1 1-1 1 0 2-1 0-2-1-3-1l-1-1-1 1-2 1c0-2 0-4-1-5v-1l-1-3v-2c1-2 1-3 1-5l-1 1c0-3 0-6 1-9l1-3z" class="Y"></path><path d="M545 412l7 4c-1 1-1 2-1 3-1 0-2 0-2-1-1 1-2 1-2 2v2h-3l-1-1v-2c-1-3 0-4 2-7z" class="h"></path><path d="M536 399v4 1 1l1 1h1v1c0 3 0 13 1 15 2 0 3 1 4-1h0l1 1-1 2c-1 1-1 1 0 2-1 0-2-1-3-1l-1-1-1 1-2 1c0-2 0-4-1-5v-1l-1-3v-2c1-2 1-3 1-5l-1 1c0-3 0-6 1-9l1-3z" class="D"></path><path d="M536 399v4 1 1l1 1v11 2l-2 1-1-3v-2c1-2 1-3 1-5l-1 1c0-3 0-6 1-9l1-3z" class="AF"></path><path d="M535 410c1 3 1 6 2 7v2l-2 1-1-3v-2c1-2 1-3 1-5z" class="m"></path><path d="M539 381l2-1 1 1h1v3l4-1h2l1 1v1l1 1c0 1 0 1-1 2h1l-1 3c-1 1-3 2-4 3l-1-1v1h-1l-1 2c0 3 0 6-1 9 1 2 2 3 3 4 2 1 6 2 7 4v3l-7-4-2-1v-2c-1-2-1-1-3-1 0 0-1 0-2-1v-1-3c-1-5 0-11 0-15h1c1-2 0-3 1-5 0-1 0-1-1-2z" class="S"></path><path d="M539 381l2-1 1 1h1v3 12c0 3 0 6-1 9 0-2 0-4-1-6 0-3 2-7-1-10h-1c-2 2-1 11-1 14-1-5 0-11 0-15h1c1-2 0-3 1-5 0-1 0-1-1-2z" class="Y"></path><path d="M547 383h2l1 1v1l1 1c0 1 0 1-1 2h1l-1 3c-1 1-3 2-4 3l-1-1v1h-1l-1 2v-12l4-1z" class="t"></path><path d="M547 383h2l1 1v1h0c-1 0-1 1-2 1h-1l-2 2h0-1l3-5z" class="F"></path><path d="M550 388h1l-1 3c-1 1-3 2-4 3l-1-1v1h-1c1-3 4-4 6-6z" class="D"></path><path d="M550 384h2 0l1-1c1 3-1 8 0 12v29h-1c-2-1 0-1-1 0l-1 1h-1v-3l-2-2c0-1 1-1 2-2 0 1 1 1 2 1 0-1 0-2 1-3v-3c-1-2-5-3-7-4-1-1-2-2-3-4 1-3 1-6 1-9l1-2h1v-1l1 1c1-1 3-2 4-3l1-3h-1c1-1 1-1 1-2l-1-1v-1z" class="B"></path><path d="M544 404h5c1 1 1 1 1 2v3h1l1 2h-1c-1-1 0-1-1-2s-2-1-3-2l-1 1-2-2v-2z" class="J"></path><defs><linearGradient id="y" x1="547.948" y1="392.359" x2="547.178" y2="403.547" xlink:href="#B"><stop offset="0" stop-color="#625b53"></stop><stop offset="1" stop-color="#756f6c"></stop></linearGradient></defs><path fill="url(#y)" d="M551 388h0c1 2 0 5 0 8 0 2 1 4 0 6s0 5-1 7v-3c0-1 0-1-1-2h-5c1-3 1-6 2-10 1-1 3-2 4-3l1-3z"></path><path d="M526 353l2 1v-3l1-1h0v-2l1-1h1v2h1l-1 2v2h0v3h1v8 17 8 8l1 2-1 1c-2-1-2-2-2-3v9c0-1 0-1-1-1v-1h-1v4l-1-1v-9-1c-1-3 1-7 0-10l-1 1c-1-2-1-5-1-7v-12c1-2 0-3 0-5 0 0 2-1 2-2v-1c-1-1-2-2-2-3 1-1 1-1 1-2v-2-1z" class="AF"></path><path d="M529 373c2 2 2 3 2 6 0 1 0 1 1 2v8 8l1 2-1 1c-2-1-2-2-2-3v-8-3c0-4-1-9-1-13zm-4-4c1-2 0-3 0-5 0 0 2-1 2-2 0 2 0 3-1 5 0 1 0 1 1 2v8 3l1 1v9 10 4 4l-1-1v-9-1c-1-3 1-7 0-10l-1 1c-1-2-1-5-1-7v-12z" class="AD"></path><path d="M526 353l2 1v-3l1-1h0v-2l1-1h1v2h1l-1 2v2h0v3h1v8 17c-1-1-1-1-1-2 0-3 0-4-2-6v-2c1-4 0-7 0-11h-1v21l-1-1v-3-8c-1-1-1-1-1-2 1-2 1-3 1-5v-1c-1-1-2-2-2-3 1-1 1-1 1-2v-2-1z" class="f"></path><path d="M531 356c0 1 0 3-1 4h0c0-2 0-4-1-5 0-1 0-1 1-2h1v3z" class="u"></path><path d="M545 323c1 0 1 0 2 1l1 1h1c2 2 2 2 5 2l-1 68c-1-4 1-9 0-12l-1 1h0-2l-1-1h-2l-4 1v-3c0-6 1-13-1-19h0c1-1 1-1 1-2v-1-2c-1-2-1-2-1-4-2 0-2 1-4 1v4h-2v-1l-1 1-1-1v-1l-1-1-1 1h-1v-3h0v-2l1-2h-1v-2h0l1-1v-1l-1-1c1-2 2-3 3-4h1c2-1 2-2 3-4l2 1c0-2 0-1-1-2 0-1 1-2 1-3h1v2h1v-1-5-4c1-1 1-1 3-1z" class="t"></path><path d="M549 379h2v3 1l-2-1v-3z" class="h"></path><path d="M552 328h1c0 3 1 7-1 9l-1-1v-4c1-1 0 0 0-1h0l-1-1 2-2z" class="B"></path><path d="M544 342c0 1 1 2 0 3 1 1 1 2 2 2h0c2 2 3 2 4 5l2-1v1c-1 1-2 1-3 1v1h1c-1 1-1 1-2 1-1-1-2-1-3-1l1-3v-2c-1 0-1 0-1 1 0 2-1 4-2 6v1c-1-2-1-2-1-4h0c0-1 0-1 1-1 1-2 1-2 1-4l-1-1v-2c0-1 0-2 1-3z" class="AE"></path><path d="M545 323c1 0 1 0 2 1l1 1s-1 1-1 2c-1 1 1 3-1 5 0 3 0 7 2 9h1l1 1h-1c0 1-1 2-1 3l1 1v1l-2-1-1 1c-1 0-1-1-2-2 1-1 0-2 0-3 0-2 0-4 1-6h-2v-2l-1-1v-5-4c1-1 1-1 3-1z" class="X"></path><path d="M545 323c1 0 1 0 2 1v1l-1 1c-1 1-1 8-1 10h-2v-2l-1-1v-5-4c1-1 1-1 3-1z" class="y"></path><path d="M543 359c1 1 1 1 2 1h2l2 1v-1c1 0 1 0 2 1 2 2 1 7 1 10h0-1c-1-1-2-1-2-2-1 1-1 2-1 3l1 1h0 2l1 2c-1 1-4 0-5 0 1 1 3 0 4 1-2 2-4 0-6 2l1 1v2l1 1c1-2 0-3 1-4l1 1v3 1h-2l-4 1v-3c0-6 1-13-1-19h0c1-1 1-1 1-2v-1z" class="m"></path><path d="M542 333l1 1v2h2c-1 2-1 4-1 6-1 1-1 2-1 3v2l1 1c0 2 0 2-1 4-1 0-1 0-1 1h0c-2 0-2 1-4 1v4h-2v-1l-1 1-1-1v-1l-1-1-1 1h-1v-3h0v-2l1-2h-1v-2h0l1-1v-1l-1-1c1-2 2-3 3-4h1c2-1 2-2 3-4l2 1c0-2 0-1-1-2 0-1 1-2 1-3h1v2h1v-1z" class="n"></path><path d="M537 341h2v3h1l1-1h1c0 2 0 3-1 4l3 1c0 2 0 2-1 4-1 0-1 0-1 1h0c-2 0-2 1-4 1v-5h0c1-4 1-4-1-7v-1z" class="AB"></path><path d="M541 347l3 1c0 2 0 2-1 4-1 0-1 0-1 1-1-1-1-2-1-3-1-1-1-2 0-3z" class="y"></path><path d="M534 340h1l2 1v1c2 3 2 3 1 7h0v5 4h-2v-1l-1 1-1-1v-1l-1-1-1 1h-1v-3h0v-2l1-2h-1v-2h0l1-1v-1l-1-1c1-2 2-3 3-4z" class="w"></path><defs><linearGradient id="z" x1="459.486" y1="840.595" x2="434.703" y2="891.647" xlink:href="#B"><stop offset="0" stop-color="#565758"></stop><stop offset="1" stop-color="#8e8b89"></stop></linearGradient></defs><path fill="url(#z)" d="M472 790h1v2c-1 1-1 2-1 3s0 1 1 1c2-1 3-1 5-2h1 4v3l1 2v33 4c0 3 1 6 2 9-1 0-1 0-1 1l4 19v4l-6 6-1 1c-3 5-3 9-2 14l2 4c-3 1-6 0-8 0h-15-59-38c-5 0-12 1-17 0h-8-17-72c1 0 9-1 10-1 24-3 48-6 71-14l17-6c3-2 7-4 10-5h1 1l1 2c0 1 0 1 1 2 0 1 1 2 2 2 0-1 0 0 1-1h0l1 1c0 1 0 1 1 1 2 0 2 0 4-2l1-1h0c2 0 2-1 4-1 0 1 1 1 2 2l1 1h1 1v2h-1c-1 0-1 0-2 1h1l4-1v-1h3l6-2 12-4c0-1 0-1 1-2v-1c-1 0-1 0-2 1h-2l-1 1h-2c0-1-1-1-2-2l1-1c0-1 0 0 1-1h0c1 0 1 0 2 1 0-2 2-3 3-4 0-1 1-2 1-2h1c1-1 1-2 2-3l-1-1c-1-1-1-2-2-2 0-1 0-2-1-3l-2-2v-1c1 1 3 2 3 4 1 0 2 1 2 2h1c2 2 4 4 5 7h1l-1 2c2 0 7-4 10-5 11-7 20-17 27-27 2-3 4-6 5-9 4-6 5-14 7-21 0-2 0-4 1-6l2 1h1c2 0 4 0 6-1v-4h3z"></path><path d="M457 874l8 3c-2 1-5-2-7-1v1h2c-1 1 0 1-1 2-1 0-1 0-1 1 1 2 6 2 9 4h0l-17-4-19-4h-1c3-1 6 0 9 1 4 1 8 2 13 2 2-1 3-3 5-5z" class="C"></path><path d="M392 876c1-1 2-1 3-1h1c3-1 7-1 10-1-1 1-2 1-3 1l1 1h1l-1 1h1v1 2c-1-1-1-1-1-2h-1v1 2 1c-1-2 0-4-2-5v5h-1v-4h0c-1 1-1 2-1 4h-1c0-2 0-3-1-5-1 2 0 5 0 7v4h-3c-2-1-1-4-1-6h-1l1-1c-1-1-1-3-1-4h0v-1z" class="K"></path><path d="M441 851c7-6 15-11 20-19 0-1 0-1 1-1v1c-1 1-1 2-2 3l-5 7-10 8c-11 10-26 16-41 20-2 0-8 2-10 3-3 1-7 2-10 2-1 0-2 0-3 1v-1h3l6-2 12-4c14-4 27-9 39-18z" class="C"></path><path d="M460 835h1c1-1 1-2 2-4v1l1 1 2 1c1 0 1-1 2-1v1c-1 2 0 4 1 6l7 9c0 1 1 1 1 2 1 1 2 2 3 4 3 3 6 6 8 9 0 1 0 1 1 1v4l-6 6c0-1 1-2 2-4 1 0 0 0 1-1-1-1-2-1-3-3-1-3-13-22-17-23h0c-1-1-1-2-1-2h-1c0 1-1 2-2 2h0c-1-1-3-2-5-2h-2 0l5-7z" class="I"></path><defs><linearGradient id="AA" x1="491.671" y1="821.276" x2="468.534" y2="842.536" xlink:href="#B"><stop offset="0" stop-color="#27282a"></stop><stop offset="1" stop-color="#555"></stop></linearGradient></defs><path fill="url(#AA)" d="M473 796c2-1 3-1 5-2h1 4v3l1 2v33 4c0 3 1 6 2 9-1 0-1 0-1 1l4 19c-1 0-1 0-1-1-2-3-5-6-8-9-1-2-2-3-3-4 0-1-1-1-1-2l-7-9c-1-2-2-4-1-6v-1c-1 0-1 1-2 1l-2-1-1-1v-1c-1 2-1 3-2 4h-1c1-1 1-2 2-3l2-3 1-2c1-2 2-5 3-8 0-1 1-2 1-4 1 0 1-1 1-1 1-2 1-3 1-5l1-1v-4-1l1-1v-2h0v-4z"></path><path d="M481 819c0 4 0 8 2 10 0 1 1 2 1 3v4c0 3 1 6 2 9-1 0-1 0-1 1h-1v-2c-3-6-3-11-4-17 0-3 0-5 1-8z" class="C"></path><defs><linearGradient id="AB" x1="477.482" y1="793.776" x2="484.025" y2="829.814" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#282828"></stop></linearGradient></defs><path fill="url(#AB)" d="M473 796c2-1 3-1 5-2h1 4v3l1 2v33c0-1-1-2-1-3-2-2-2-6-2-10-1-7-4-13-8-19v-4z"></path><defs><linearGradient id="AC" x1="447.338" y1="799.326" x2="463.33" y2="840.388" xlink:href="#B"><stop offset="0" stop-color="#1b1a1a"></stop><stop offset="1" stop-color="#505152"></stop></linearGradient></defs><path fill="url(#AC)" d="M472 790h1v2c-1 1-1 2-1 3s0 1 1 1v4h0v2l-1 1v1 4l-1 1c0 2 0 3-1 5 0 0 0 1-1 1 0 2-1 3-1 4l-3 8-1 2-2 3v-1c-1 0-1 0-1 1-5 8-13 13-20 19-12 9-25 14-39 18 0-1 0-1 1-2v-1c-1 0-1 0-2 1h-2l-1 1h-2c0-1-1-1-2-2l1-1c0-1 0 0 1-1h0c1 0 1 0 2 1 0-2 2-3 3-4 0-1 1-2 1-2h1c1-1 1-2 2-3l-1-1c-1-1-1-2-2-2 0-1 0-2-1-3l-2-2v-1c1 1 3 2 3 4 1 0 2 1 2 2h1c2 2 4 4 5 7h1l-1 2c2 0 7-4 10-5 11-7 20-17 27-27 2-3 4-6 5-9 4-6 5-14 7-21 0-2 0-4 1-6l2 1h1c2 0 4 0 6-1v-4h3z"></path><defs><linearGradient id="AD" x1="455.475" y1="826.168" x2="437.758" y2="821.179" xlink:href="#B"><stop offset="0" stop-color="#121313"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#AD)" d="M452 821c4-6 5-14 7-21 0-2 0-4 1-6l2 1h1l1 1-1 4h0v4c-1 2-2 4-2 6-1 2-2 5-3 7-2 3-4 7-6 10s-4 6-5 10l-3 3c-3 4-14 15-19 16-2 0-3 1-5 1h0c11-7 20-17 27-27 2-3 4-6 5-9z"></path><defs><linearGradient id="AE" x1="438.222" y1="861.744" x2="406.922" y2="853.694" xlink:href="#B"><stop offset="0" stop-color="#505051"></stop><stop offset="1" stop-color="#929190"></stop></linearGradient></defs><path fill="url(#AE)" d="M444 840c0 4-5 5-5 8l3 1c0 1 0 0-1 1v1c-12 9-25 14-39 18 0-1 0-1 1-2v-1c-1 0-1 0-2 1h-2l-1 1h-2c0-1-1-1-2-2l1-1c0-1 0 0 1-1h0c1 0 1 0 2 1 0-2 2-3 3-4 0-1 1-2 1-2h1c1-1 1-2 2-3l-1-1c-1-1-1-2-2-2 0-1 0-2-1-3l-2-2v-1c1 1 3 2 3 4 1 0 2 1 2 2h1c2 2 4 4 5 7h1l-1 2c2 0 7-4 10-5h0c2 0 3-1 5-1 5-1 16-12 19-16z"></path><defs><linearGradient id="AF" x1="364.934" y1="925.475" x2="337.566" y2="860.525" xlink:href="#B"><stop offset="0" stop-color="#aba097"></stop><stop offset="1" stop-color="#fffce6"></stop></linearGradient></defs><path fill="url(#AF)" d="M345 894h-8-17-72c1 0 9-1 10-1 24-3 48-6 71-14l17-6c3-2 7-4 10-5h1 1l1 2c0 1 0 1 1 2 0 1 1 2 2 2 0-1 0 0 1-1h0l1 1c0 1 0 1 1 1 2 0 2 0 4-2l1-1h0c2 0 2-1 4-1 0 1 1 1 2 2l1 1h1 1v2h-1c-1 0-1 0-2 1h1l4-1c1-1 2-1 3-1 3 0 7-1 10-2-1 2-3 2-5 3h-2c-1 1-3 1-4 1-1 1 0 8 0 10l1 1h1l-1-1v-5h1l1 2h0c-1 2 0 3 0 4h1v-8-3h1c1 3-2 9 1 11 2-2-1-9 1-11h0v11h1c1-1 1-9 0-11h0l1-1v1h0c0 1 0 3 1 4l-1 1h1c0 2-1 5 1 6h3c2 0 3 0 5 1h7c2-1 3 0 5-1h5v-7h1c0 2-1 6 0 7 3 0 5 0 8 1h4l-1 1 13 1c-1 1-3 1-4 1l2 1v1h-67-19-11z"></path><path d="M414 888h5v-7h1c0 2-1 6 0 7 3 0 5 0 8 1h4l-1 1 13 1c-1 1-3 1-4 1h-6c-8-2-17-1-25-1h0l9-1c1 0 2 1 3 0 2-1 1-1 4-1h0c-4-1-7-1-11-1z" class="X"></path><path d="M442 320c1 0 1 0 2-1v1h1v1l-1 1c-1 1-1 1-1 3h1 1 0c2 0 4 1 6 1l2-1v17l1-1v-3l3-2 1-1c0 2 0 3 1 4 1 3 1 10 3 12h-2v1c1 3 2 10 0 13v47 11 3 4c1 2 0 6 0 9l1 15c1 3 0 5 0 8 1 2 1 3 1 5l-2 1-1-2s-2 0-2-1l-2-2v1c-1 1-3 2-4 4-2 2-3 5-4 7l-1 3v4l-1 1v3 3l1-1v4c0 1 0 1 1 2h-1c0 2 0 4 1 6 0 1 1 3 1 5 1 4 3 8 5 12 1 2 1 3 3 5h-1c-1-1-2-2-3-2 1 2 2 2 4 4h-1l-3-2-1 1h0v1c1 1 2 3 3 4s1 2 1 3c-1-1-3-3-4-3s-3 0-4 1c-2 0-3 0-4-1v-1c-1-1-1-2-3-3h-3c-2-1-2 0-4-2-1-1-4 0-6 0l1-1v-1h1l-1-1-2-1c-3-2-4-4-5-7 0-2-1-8-2-9l-1-1c0-1-1-1-1-2h0v3 4 3l-1 1-1-1c0 1-1 1-1 2h-1v-4c-1-1-1-2-1-4-2 0-3 0-5-1v1h-2v2h-1v-1c-2-1-7-1-9-1h-1-3c0 1-1 2 0 3v2 3 3h-1l-1-1v-1h-3l-1 2-1 4 1 1h0l-1 1v1c0 1-1 1-2 1v-1c-1 1-1 1-1 2-1-1-2-2-2-3v-1c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-3l-1-1c0-1 0-3 1-4v1l1-1v-1c0-2 0-2-1-3h0-3c-1 1-1 1-2 1l-1-1c-1 0-2 0-3 1v-2c-1-1-1-1-1-2v-1c-1 1-1 3-1 4h-1v-1l-1 1h-2c-1 0-1-1-2-1v-1c0-2 0-3 2-5v1c1-3 1-5 2-7a19.81 19.81 0 0 1 11-11l1 1v-1c1 0 2-1 3-1l1 1v1h1c1-1 2-1 3-2h1c1-1 3-1 4-1-1-1-2-2-3-2v-4h0 4c1 1 2 1 3 1 1 1 1 1 1 2h1v-9c0-1 1-2 1-3 5-1 7-4 11-7l11-11c0-1 1-2 2-4h-3 0l-1-1c0-1 0-3 1-4 0-2 1-2 3-2 1-7 0-14 0-20v-3c0-2 0-3 1-5v-21c-2 0-4 0-5-1s-1-1 0-2l3-1v-1-5-3l1-1v-1h0-1 0c1-1 0-2 0-3 0 0 1-1 2-1h2 3c3 0 5-1 8 0h2l3-1-1-18 1-9v-2c1 0 1 4 1 5v2l1 1s1 2 1 3v-4-6c-1-2-1-4 0-6v-8z" class="K"></path><path d="M441 417h2c1 2 1 2 1 3l-1 1c1 1 1 1 2 1 0 1-1 2-1 3v1h-1c0 1 0 1-1 3v2h-1v-14z" class="h"></path><path d="M436 363l3-1-1 13c0 2 1 4 0 6-1 0 0-5-1-7 0-2 0-8-1-10v-1z" class="M"></path><path d="M429 473l1 1 1 1v1h1c1-1 1-2 2-3v-3s0-1 1-2h0 1 2c-1 2-3 5-4 8-1 2-2 5-2 8l-1 1h-3l1-1-1-1h0l-1-1v-1h0l-1-1 2-2h-1v-3h0l-1-1c1-1 2-1 3-1zm5 26c0-2 0-5 1-6 1 1 1 2 2 4v5c1 2 1 3 2 4s1 1 1 2c0 4 2 6 3 10h-1l-1-2h-2-1c-1-2-1-6-3-8v-1c-1-2-1-6-1-8z" class="I"></path><path d="M435 507l1-1c2 1 2 1 3 3v1l2 6h-2-1c-1-2-1-6-3-8v-1z" class="D"></path><path d="M437 441v2l1 4h0l1-7v25c0 1 0 2-1 3h-2-1v-4-17l-1-3 1-3h2z" class="P"></path><path d="M436 468v-3-1l2 2 1-1c0 1 0 2-1 3h-2z" class="Q"></path><path d="M437 441v2 9c-1 3-2 6-1 9h1v1l-1 1-1 1v-17l-1-3 1-3h2z" class="L"></path><defs><linearGradient id="AG" x1="450.698" y1="509.107" x2="432.053" y2="488.114" xlink:href="#B"><stop offset="0" stop-color="#1c1e1f"></stop><stop offset="1" stop-color="#424040"></stop></linearGradient></defs><path fill="url(#AG)" d="M439 481h1v-3h1 0c0 1 0 1 1 1 0 1 0 2-1 3v3c0 2 0 2 1 3v1c-1 1 0 1-1 1-1 2-1 3-1 5l1 1c0 1-1 4 0 6h0c0 1 0 2 1 3 1 2 1 5 2 7l1 4 1 1c-1 0-2 0-3 1-1-4-3-6-3-10 0-1 0-1-1-2l-1-4c-1-7 0-15 1-21z"></path><path d="M421 469c2 0 2 0 2-1l1-1c1-2 2-3 4-4l1 2v8c-1 0-2 0-3 1l1 1h0v3h1l-2 2 1 1h0v1l1 1-1 2h-2v-2h-1v-2c-1 0-1 0-2 1l-1-1-1-1 1-10v-1z" class="M"></path><path d="M421 481c1-1 1-2 1-2l1-1h2 1v2l1 1h0v1l1 1-1 2h-2v-2h-1v-2c-1 0-1 0-2 1l-1-1zm0-12c2 0 2 0 2-1l1-1c1-2 2-3 4-4l1 2v8c-1 0-2 0-3 1l1 1h0v3c-1-2-2-3-3-3l-1-2c0-1-1-1-1-2-1 0-1-1-1-1v-1zm20-36l1 1h5l1 1v4c-1 0-2 1-3 1l-1 1 1 1v1 1l2 1v1l-1 8v3l2 1c-1 2-2 2-2 4l-1 3c-1 0-2 1-3 1-1-2-1-4-1-6v-16c1-3 1-7 0-11z" class="B"></path><path d="M450 462c1 0 2 0 3-1l2 2v1c-1 1-3 2-4 4-2 2-3 5-4 7l-1 3v4l-1 1v1 2l-1 1v5h0c-2-1-1-3-1-5l-1 1c-1-1-1-1-1-3v-3c1-1 1-2 1-3-1 0-1 0-1-1h0-1v3h-1c0-2 0-5 1-7 1-3 4-8 8-9 0-1 0-1 1-2h0l1-1z" class="P"></path><path d="M433 438c1 2 1 3 1 5v1l1 3v17 4h0c-1 1-1 2-1 2v3c-1 1-1 2-2 3h-1v-1l-1-1-1-1v-8l-1-2h0v-14-8l3 1v1c0 1-1 1-1 2h0 2 1v-7z" class="W"></path><path d="M432 453c1 1 1 2 1 3v3c1 1 1 2 0 3v1l-1-1c-1 2-2 2-3 3l-1-2h0c2-1 3-2 4-2v-1l-2-1v-2h1l1-4z" class="N"></path><path d="M433 438c1 2 1 3 1 5v1l1 3-2 9c0-1 0-2-1-3v-1c0-1-1-1-2-2v-1c0-1 1-1 1-2 1-1 1-1 1-2h1v-7z" class="K"></path><path d="M435 447v17 4h0c-1 1-1 2-1 2v3c-1 1-1 2-2 3h-1v-1l-1-1-1-1v-8c1-1 2-1 3-3l1 1v-1c1-1 1-2 0-3v-3l2-9z" class="X"></path><path d="M445 442c2 0 5 0 7-1h0 1l1 9 4 3c1 1 2 1 3 1 1 3 0 5 0 8 1 2 1 3 1 5l-2 1-1-2s-2 0-2-1l-2-2-2-2c-1 1-2 1-3 1l-1 1-4 2 1-3c0-2 1-2 2-4l-2-1v-3l1-8v-1l-2-1v-1-1z" class="D"></path><path d="M446 462l2-3 1 1h0c0 1 1 1 1 2l-1 1-4 2 1-3z" class="M"></path><path d="M458 453c1 1 2 1 3 1 1 3 0 5 0 8 1 2 1 3 1 5l-2 1-1-2v-1c-1-1-1-1-1-2-1-2 0-3-2-5v-3l2-2z" class="E"></path><path d="M436 364c1 2 1 8 1 10 1 2 0 7 1 7l1 49v10l-1 7h0l-1-4v-2h-2v-28c0-6 1-13 0-19v-7-18h0c0-2 0-3 1-5z" class="v"></path><path d="M437 428l2 2v10l-1 7h0l-1-4v-2-13z" class="M"></path><path d="M437 374c1 2 0 7 1 7l1 49-2-2v-54z" class="G"></path><path d="M445 483v3 3l1-1v4c0 1 0 1 1 2h-1c0 2 0 4 1 6 0 1 1 3 1 5 1 4 3 8 5 12 1 2 1 3 3 5h-1c-1-1-2-2-3-2 1 2 2 2 4 4h-1l-3-2-1 1h0v1c1 1 2 3 3 4s1 2 1 3c-1-1-3-3-4-3s-3 0-4 1c-2 0-3 0-4-1v-1c-1-1-1-2-3-3h-3c-2-1-2 0-4-2h4l1-2h1l1 1v-1c-1-1-1-2-2-3v-1h1 2l1 2h1c1-1 2-1 3-1l-1-1-1-4c-1-2-1-5-2-7-1-1-1-2-1-3h0c-1-2 0-5 0-6l-1-1c0-2 0-3 1-5 1 0 0 0 1-1v-1l1-1c0 2-1 4 1 5h0v-5l1-1v-2-1z" class="M"></path><path d="M438 516h1 2l1 2h1c1-1 2-1 3-1 1 1 1 2 2 3h-2 0c1 1 2 1 3 1v1h-2c1 1 2 1 4 1v1c1 1 2 3 3 4s1 2 1 3c-1-1-3-3-4-3s-3 0-4 1c-2 0-3 0-4-1v-1c-1-1-1-2-3-3h-3c-2-1-2 0-4-2h4l1-2h1l1 1v-1c-1-1-1-2-2-3v-1z" class="F"></path><path d="M433 522h4 3c2 0 4 2 7 2v2h-1-1v-2h-5-3c-2-1-2 0-4-2z" class="C"></path><path d="M451 524c1 1 2 3 3 4s1 2 1 3c-1-1-3-3-4-3s-3 0-4 1c-2 0-3 0-4-1v-1c-1-1-1-2-3-3h5v2h1 1v-2h4z" class="U"></path><path d="M433 389l2-2v7c1 6 0 13 0 19v28l-1 3v-1c0-2 0-3-1-5v7h-1-2 0c0-1 1-1 1-2v-1l-3-1c0-4 0-8 1-12l-1-13v-2c0-7-1-16 0-23 1 1 1 1 1 2l1 1h0 1v-2h-1l2-1c0-1 1-2 1-2z" class="W"></path><path d="M433 403h0c0 2 0 3 1 5l-1 1-1-1-1 1-1-1c0-1 0-2 1-2 1-1 1-1 1-2l1-1z" class="N"></path><path d="M428 416h1c1-1 0-1 1-1v-2h1v1c1 0 2 1 3 1v1l-1 1c0 3 1 5 0 9h0v-1c-1 1-2 1-3 2v2h1l1 1-1 1-2-2-1-13z" class="S"></path><path d="M432 417h0c1 1 1 5 1 6h-1c0-1 0 0-1-1l-1-1c0-1 1-2 2-4zm-3 12l2 2 1-1-1-1h-1v-2c1-1 2-1 3-2v1c0 3-1 10 0 12v7h-1-2 0c0-1 1-1 1-2v-1l-3-1c0-4 0-8 1-12z" class="N"></path><path d="M419 367c1-1 0-2 0-3 0 0 1-1 2-1h2 3c3 0 5-1 8 0h2v1c-1 2-1 3-1 5h0v18l-2 2s-1 1-1 2l-2 1h1v2h-1 0l-1-1c0-1 0-1-1-2-1 7 0 16 0 23h-1-1v-3c-1-3 0-5 0-7 0-5 1-13 0-18h0v-4c0-2-1-2-2-3h-1 0-2v-1l-1-1v2 1c0 1-2 0-2 0h-1c1-1 1 0 1-1h1v-1-1-5-3l1-1v-1h0-1 0z" class="C"></path><path d="M429 376c2 2 2 5 3 6l-1 1 2 2c0 1 1 2 0 4 0 0-1 1-1 2l-2 1h1v2h-1 0l-1-1c0-1 0-1-1-2-1-4 0-10 1-15z" class="X"></path><path d="M430 388c0-1-1-5 0-6l1 1 2 2h-1c-1 1-1 2-2 3z" class="K"></path><path d="M430 388c1-1 1-2 2-3h1c0 1 1 2 0 4 0 0-1 1-1 2l-2 1-1-1c0-2 0-2 1-3z" class="T"></path><path d="M432 371h0c1-1 1-1 1-2h1 1v18l-2 2c1-2 0-3 0-4l-2-2 1-1c-1-1-1-4-3-6l1-1h-1v-2c0-1 0-2 1-2 0 1-1 1 0 2l2-2z" class="S"></path><path d="M429 373c0-1 0-2 1-2 0 1-1 1 0 2l1 1c1 0 2-1 3-2 0 2 0 7-1 10h-1c-1-1-1-4-3-6l1-1h-1v-2z" class="K"></path><path d="M419 367c1-1 0-2 0-3 0 0 1-1 2-1h2 3c3 0 5-1 8 0h2v1c-1 2-1 3-1 5h0-1-1c0 1 0 1-1 2h0l-2 2c-1-1 0-1 0-2-1 0-1 1-1 2h-2-1v1l2 2c-1 1-2 1-3 1l-1-1c-1 0-1 0-1-1l1-1-1-1 1-1-1-1h0c-2 0-3 1-4 1v-3l1-1v-1h0-1 0z" class="G"></path><path d="M423 363h3c3 0 5-1 8 0h2v1c-1 2-1 3-1 5h0-1-1c0 1 0 1-1 2h0c-1-2-1-3-1-5 0-1 0-1-1-2v1h-1v-1c-1 1-1 2-1 3l-1 1v-1l-1-1-1 2h0c0-2 0-3-1-4l-1-1z" class="B"></path><defs><linearGradient id="AH" x1="408.605" y1="390.799" x2="436.775" y2="440.987" xlink:href="#B"><stop offset="0" stop-color="#161419"></stop><stop offset="1" stop-color="#393935"></stop></linearGradient></defs><path fill="url(#AH)" d="M423 379h1c1 1 2 1 2 3v4h0c1 5 0 13 0 18 0 2-1 4 0 7v3h1 1v2l1 13c-1 4-1 8-1 12v8 14h0c-2 1-3 2-4 4l-1 1c0 1 0 1-2 1 0-1 0-3 1-4v-4-78l1-1h1l-1-1v-2z"></path><path d="M426 448c1 3-1 5 0 8-1 1-1 1-1 2-1-1-1-1-2-1-1-2-1-3-1-5 1-1 2-1 2-2l2-2z" class="D"></path><path d="M426 448h0c1-1 1-2 2-3v4 14h0c-2 1-3 2-4 4l-1 1c0 1 0 1-2 1 0-1 0-3 1-4v-4l3-3c0-1 0-1 1-2-1-3 1-5 0-8z" class="C"></path><path d="M426 456c0 2 0 4-1 6l-3 3v-4l3-3c0-1 0-1 1-2z" class="F"></path><path d="M420 480l1 1 1 1c1-1 1-1 2-1v2h1v2h2l1-2h0l1 1-1 1h3l1-1v2h-2c1 1 3 1 3 3s-1 4 0 6v5h1v-1c0 2 0 6 1 8v1c2 2 2 6 3 8v1c1 1 1 2 2 3v1l-1-1h-1l-1 2h-4c-1-1-4 0-6 0l1-1v-1h1l-1-1-2-1c-3-2-4-4-5-7 0-2-1-8-2-9-1-8-1-14 1-22z" class="Q"></path><path d="M431 495v3h0c-3 0-6 0-8-1l1-1h1c2 0 4-1 6-1z" class="X"></path><path d="M428 483h0l1 1-1 1h3l1-1v2h-2c1 1 3 1 3 3s-1 4 0 6v5h1v-1c0 2 0 6 1 8v1l-1-1v-4h-1c0 1 0 1-1 2l-1-1h1v-2c0-1-1-1-1-2v-1-1h0v-3c0-2 1-4 0-6h-4-1 0-4v-3c1-1 2-1 3-1h2l1-2z" class="W"></path><path d="M423 504c0 2 1 4 1 6 1 1 3 2 3 3 0 2 2 5 3 7l1-1-1-2c0-1-1-3-2-4v-1c-1-1-2-1-2-2v-1c2 1 2 2 3 4 1 1 1 1 1 2 2 1 2 2 2 3s0 1 1 1h0v-1c0-1-1-2-1-3h0c-1-1-2-2-2-4-1 0-1-1-2-2h1c1 1 1 0 2 0s1 1 2 2v1l1 2h-1c1 1 1 2 2 3 0 1 0 2 1 3h2l-1 2h-4c-1-1-4 0-6 0l1-1v-1h1l-1-1-2-1c-3-2-4-4-5-7 1 0 2-1 2-1l-1-2c0-2 0-3 1-4z" class="O"></path><defs><linearGradient id="AI" x1="427.776" y1="503.393" x2="413.724" y2="491.107" xlink:href="#B"><stop offset="0" stop-color="#131413"></stop><stop offset="1" stop-color="#3b393b"></stop></linearGradient></defs><path fill="url(#AI)" d="M420 480l1 1 1 1c1-1 1-1 2-1v2h1v2c-1 0-2 0-3 1v3l-1 1 1 1h2c2-1 2-1 4 0h1v1c0 1-1 1-1 1l-1 1v1c-1 0-1-1-2-1v-1l-1-1h-1l1 1v1l-2-2c-1 1-1 2-1 3v1c1 3 1 5 2 8-1 1-1 2-1 4l1 2s-1 1-2 1c0-2-1-8-2-9-1-8-1-14 1-22z"></path><path d="M442 320c1 0 1 0 2-1v1h1v1l-1 1c-1 1-1 1-1 3h1 1 0c2 0 4 1 6 1l2-1v17l1-1v-3l3-2 1-1c0 2 0 3 1 4 1 3 1 10 3 12h-2v1c1 3 2 10 0 13v47 11 3 4c1 2 0 6 0 9l1 15c-1 0-2 0-3-1l-4-3-1-9h-1 0c-2 1-5 1-7 1l-1-1 1-1c1 0 2-1 3-1v-4l-1-1h-5l-1-1v-2h1v-2c1-2 1-2 1-3h1v-1c0-1 1-2 1-3-1 0-1 0-2-1l1-1c0-1 0-1-1-3h-2l1-2v-45-12l-1-1c1-4 1-9 1-13v-4-6c-1-2-1-4 0-6v-8z" class="T"></path><path d="M456 355h1c1 0 1 1 2 2l-1 3h-1c0-1-1-2-1-3l-1-1 1-1z" class="N"></path><path d="M451 373c0 4 1 8 3 12-1 1-2 1-3 1v-4c0-3-2-6 0-9z" class="M"></path><path d="M444 426h1v1c1-1 0-2 1-3l1 1v4c-1 1-4 2-5 2v-2c1-2 1-2 1-3h1z" class="O"></path><path d="M451 386c1 0 2 0 3-1 2 4 1 10-1 14l-2 3h0v-16z" class="D"></path><path d="M451 429h3c1 2 1 3 1 5 0 1 0 2-1 2 0 2 0 2-1 2l-1 1h0c-1 0-2 1-3 1h0 0c1-2 1-5 0-7h0v-1c1-1 1-2 2-3z" class="B"></path><path d="M451 429h3c1 2 1 3 1 5l-1-1-1 1h-1c0-1 1-2 0-4l-1-1z" class="M"></path><path d="M457 336l1-1c0 2 0 3 1 4 1 3 1 10 3 12h-2v1c1 3 2 10 0 13 0-7 0-15-2-22l-4-2v-3l3-2z" class="AL"></path><path d="M457 336v4c0 1 1 2 1 3h0l-4-2v-3l3-2z" class="y"></path><path d="M453 441c0 1 2 1 2 2l2 2h2v1-1-6-2-6-1h1c1 2 0 6 0 9l1 15c-1 0-2 0-3-1l-4-3-1-9z" class="K"></path><path d="M455 446h1c1 0 2 1 2 1l-1 1-2 1v-1-2z" class="T"></path><path d="M449 413c1-2 1-4 3-4 0-1 1-1 1-1 1 1 1 2 1 4 1 3 1 7 0 9h-1-2c0 2 1 2 1 4 0 0-1 2-1 3h-1v-7h-1v8h-1v-6c-1 0-1 1-1 2l-1-1c-1 1 0 2-1 3v-1h-1v-1c0-1 1-2 1-3-1 0-1 0-2-1l1-1c0-1 0-1-1-3l1-1c1 0 3 0 4 1l1-1v-3z" class="D"></path><path d="M451 416h1v2c0 1-1 2-2 2h-1 0v-1-1l2-2z" class="d"></path><path d="M446 390c1-1 1-1 2-1v-4l-2-1h1 2c0-1 0-1 1-2h1v4 16h0l-2 4c-1 3-2 7-1 10h0l1-3v3l-1 1c-1-1-3-1-4-1l-1 1h-2l1-2c1-1 1-1 1-2h2v-5l1-1c1-1 1-1 0-3-1-3 0-6-1-9 1-2 0-3 1-4l2 1v-1l-2-1z" class="L"></path><path d="M442 370v-1h1 2v1h1c0 1-1 3 0 4l1 1-2 2h1 3v2c1 1 1 2 1 3-1 1-1 1-1 2h-2-1l2 1v4c-1 0-1 0-2 1l2 1v1l-2-1c-1 1 0 2-1 4 1 3 0 6 1 9 1 2 1 2 0 3l-1 1v5h-2c0 1 0 1-1 2v-45z" class="C"></path><path d="M445 408v-1h-2v-4l1-1 1 1v-8c1 3 0 6 1 9 1 2 1 2 0 3l-1 1z" class="P"></path><path d="M446 390c0-1 0 0-1-1-1 0-1 0-1-1-1-1 0-4 1-5v-1-1h0l2-1v-1h-2l1-1c1 0 1 0 2 1h1c1 1 1 2 1 3-1 1-1 1-1 2h-2-1l2 1v4c-1 0-1 0-2 1z" class="Q"></path><path d="M442 320c1 0 1 0 2-1v1h1v1l-1 1c-1 1-1 1-1 3h1 1 0c2 0 4 1 6 1l2-1v17h0v2c1 2 2 7 1 9l-4 4c0 1 0 2 2 2 2 2 3 2 4 4v3c0 2 1 5 0 7l-1 1c-2-1-3-1-4-3v2c-2 3 0 6 0 9h-1c0-1 0-2-1-3v-2h-3-1l2-2-1-1c-1-1 0-3 0-4h-1v-1h-2-1v1-12l-1-1c1-4 1-9 1-13v-4-6c-1-2-1-4 0-6v-8z" class="E"></path><path d="M445 342c0-1 1-1 1-2 3-1 4-7 4-10h1c0 1 0 2-1 3 0 1 0 3 1 4 0 2 1 4 0 5 0 1 0 3-1 4l2 2v1c-1-1-2-1-3-1 0 1 2 2 2 4h-1l-2-2v-3-1c-1-1-1-3-3-4h0z" class="S"></path><path d="M442 320c1 0 1 0 2-1v1h1v1l-1 1c-1 1-1 1-1 3h1 1 0c2 0 4 1 6 1l2-1v17h0l-1 1-1-1c1-1 0-3 0-5-1-1-1-3-1-4 1-1 1-2 1-3h-1c0 3-1 9-4 10 0 1-1 1-1 2h0c-1-1 0-2 0-3l-3 1v-6c-1-2-1-4 0-6v-8z" class="G"></path><path d="M442 334v-4h1l1 1 2 2c1 1 1 2 0 4l-1 1v1l-3 1v-6z" class="V"></path><path d="M446 357h1c1-1 1-2 1-4h-1l1-1 1 1h0c1 1 0 1 1 2h0c-1 2-1 3-1 5v-1c1 2 2 3 3 5l1-1c0 1 1 1 1 2h1 0c1 1 1 0 1 1 0 2 1 5 0 7l-1 1c-2-1-3-1-4-3v2c-2 3 0 6 0 9h-1c0-1 0-2-1-3v-2h-3-1l2-2-1-1c-1-1 0-3 0-4h-1v-1h-2-1v1-12l-1-1 1-2h2v-2c1 0 1 0 2 1v1l-1 1c1 0 1 0 1 1z" class="U"></path><path d="M452 364l1-1c0 1 1 1 1 2h1 0c1 1 1 0 1 1 0 2 1 5 0 7l-1 1c-2-1-3-1-4-3l1-1 1-2c-1-1-1-2-2-3h-2v-1h3z" class="M"></path><path d="M441 357l1-2h2v-2c1 0 1 0 2 1v1l-1 1c1 0 1 0 1 1l-1 1 2 1c0 1 0 1-1 2h1v1c-1 0-1 0-1 1s-1 2 0 4l-1 1s1 1 1 2h-1v-1h-2-1v1-12l-1-1z" class="E"></path><path d="M441 357l1-2h2v-2c1 0 1 0 2 1v1l-1 1c1 0 1 0 1 1l-1 1v1 4l-1 1v-3h-1v5h-1v-8l-1-1z" class="B"></path><path d="M419 378v1h-1c0 1 0 0-1 1h1s2 1 2 0v-1-2l1 1v1h2 0v2l1 1h-1l-1 1v78 4c-1 1-1 3-1 4v1l-1 10c-2 8-2 14-1 22l-1-1c0-1-1-1-1-2h0v3 4 3l-1 1-1-1c0 1-1 1-1 2h-1v-4c-1-1-1-2-1-4-2 0-3 0-5-1v1h-2v2h-1v-1c-2-1-7-1-9-1h-1-3c0 1-1 2 0 3v2 3 3h-1l-1-1v-1h-3l-1 2-1 4 1 1h0l-1 1v1c0 1-1 1-2 1v-1c-1 1-1 1-1 2-1-1-2-2-2-3v-1c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-3l-1-1c0-1 0-3 1-4v1l1-1v-1c0-2 0-2-1-3h0-3c-1 1-1 1-2 1l-1-1c-1 0-2 0-3 1v-2c-1-1-1-1-1-2v-1c-1 1-1 3-1 4h-1v-1l-1 1h-2c-1 0-1-1-2-1v-1c0-2 0-3 2-5v1c1-3 1-5 2-7a19.81 19.81 0 0 1 11-11l1 1v-1c1 0 2-1 3-1l1 1v1h1c1-1 2-1 3-2h1c1-1 3-1 4-1-1-1-2-2-3-2v-4h0 4c1 1 2 1 3 1 1 1 1 1 1 2h1v-9c0-1 1-2 1-3 5-1 7-4 11-7l11-11c0-1 1-2 2-4h-3 0l-1-1c0-1 0-3 1-4 0-2 1-2 3-2 1-7 0-14 0-20v-3c0-2 0-3 1-5v-21c-2 0-4 0-5-1s-1-1 0-2l3-1z" class="H"></path><path d="M412 503l2-1h0c-1-1-1-1-2-1v-4c1-1 1-1 2-1v-4l1-1c0 2 0 3-1 5 0 1 0 1 1 2 1 2 1 6 2 8v3l-1 1-1-1c0 1-1 1-1 2h-1v-4c-1-1-1-2-1-4z" class="B"></path><path d="M421 403v35h-1-3 0l-1-1c0-1 0-3 1-4 0-2 1-2 3-2 1-7 0-14 0-20v-3c0-2 0-3 1-5z" class="D"></path><path d="M418 469l1 1c0 4-1 8-2 12h0l-2 2v1h0l1 1c-1 1-1 2-2 3l-1-1c0-1-1-1-2-1l-1-1c0-1 0-1-1-2 0-1 1-2 2-3l1 1 1-1v-1c1-1 1-2 1-2l1-1h0v1 1 2c1-1 1-1 1-2v-1h1v-2-1l1-1v-2-2-1z" class="a"></path><path d="M413 463c1 2 2 3 4 5 0 0 0 1 1 1v1 2 2l-1 1v1 2h-1v1c0 1 0 1-1 2v-2-1-1h0c-1-1-1-1-1-2-1 0-2-1-3-1-1-3-1-4 0-7 1-1 2-3 2-4z" class="N"></path><path d="M420 438h1c0 10 0 20-1 30v5c0 3-1 7-3 9 1-4 2-8 2-12l-1-1c-1 0-1-1-1-1-2-2-3-3-4-5v-3l2-2h1 1 1c1-2 1-3 0-5-2 0-5 1-7 2h-1l-1-1v-1l2-1h0c1-2 2-2 3-3 0 1-2 3-2 4 3-1 3-5 5-7 1-1 1-2 1-4 0-1 1-2 2-4z" class="M"></path><path d="M413 463v-3l2-2h1 1c1 4 1 6 0 10-2-2-3-3-4-5z" class="D"></path><path d="M418 442c0 2 0 3-1 4-2 2-2 6-5 7 0-1 2-3 2-4-1 1-2 1-3 3h0l-2 1v1l1 1h1c2-1 5-2 7-2 1 2 1 3 0 5h-1-1-1l-2 2v3c0 1-1 3-2 4 0 1-2 1-2 1-1 1 0 2-2 2l-1-1c-1 0-1 0-2-1h0l-1 1v5 3h0l-1-1h-1c-1-1-1-1-3-1h-2c-1-1-1-2-1-3v-9c0-1 1-2 1-3 5-1 7-4 11-7l11-11z" class="G"></path><path d="M398 475h1v-3h1v-1c2 0 2-1 3-2v5 3h0l-1-1h-1c-1-1-1-1-3-1z" class="M"></path><path d="M407 470c2 0 1-1 2-2 0 0 2 0 2-1-1 3-1 4 0 7l-2-1-2 5c-1 1-2 3-3 5 0 0-1 2-1 3h-1-1l1 1v1 2s-1 1-1 2v1h-3v1h4v1h-6v1c2 0 4 0 6 1h0c0 1 0 1 1 2l-1 2c-1 1-2 0-3 0-1-1-1-1-1-2h-1l-1 2-2 1v-1c-2 1-2 1-3 1l-1-1 1-1c-2-1-2-1-4-1l-1 1 1 1c-2 1-5-1-6 2 1 1 2 0 4 0h6 0c0 1-1 2 0 3v2 3 3h-1l-1-1v-1h-3l-1 2-1 4 1 1h0l-1 1v1c0 1-1 1-2 1v-1c-1 1-1 1-1 2-1-1-2-2-2-3v-1c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-3l-1-1c0-1 0-3 1-4v1l1-1v-1c0-2 0-2-1-3h0-3c-1 1-1 1-2 1l-1-1c-1 0-2 0-3 1v-2c-1-1-1-1-1-2v-1c-1 1-1 3-1 4h-1v-1l-1 1h-2c-1 0-1-1-2-1v-1c0-2 0-3 2-5v1c1-3 1-5 2-7a19.81 19.81 0 0 1 11-11l1 1v-1c1 0 2-1 3-1l1 1v1h1c1-1 2-1 3-2h1c1-1 3-1 4-1-1-1-2-2-3-2v-4h0 4c1 1 2 1 3 1 1 1 1 1 1 2h1c0 1 0 2 1 3h2c2 0 2 0 3 1h1l1 1h0v-3-5l1-1h0c1 1 1 1 2 1l1 1z" class="B"></path><path d="M384 509l3 1 2 2h-3-2v-3z" class="I"></path><path d="M407 470c2 0 1-1 2-2 0 0 2 0 2-1-1 3-1 4 0 7l-2-1-2 5c0-1 0-1-1-1-1-1 0-2-1-3v-4h2z" class="E"></path><path d="M405 474c1-1 0-1 1-1h3l-2 5c0-1 0-1-1-1-1-1 0-2-1-3z" class="R"></path><path d="M385 503h6 0c0 1-1 2 0 3v2 3 3h-1l-1-1v-1l-2-2-3-1c1-1 1-2 1-2l-1-2h1v-2z" class="E"></path><path d="M391 508v3 3h-1l-1-1v-1l-2-2 2-1 1-1h1z" class="R"></path><path d="M394 472h1c0 1 0 2 1 3h2c2 0 2 0 3 1h1l1 1h-3l1 2h0l-2-1c-2 1-4 3-5 5 0 1-1 2-1 3 0 2-1 3-2 4 0 1 0 4-1 4h-7c1-2 2-5 3-7l-1-1c1-1 1-3 2-4 1-2 4-2 4-4v-2h0c1 0 1 0 1-1l1-2 1-1z" class="H"></path><path d="M394 472h1c0 1 0 2 1 3h2c2 0 2 0 3 1h1l1 1h-3l1 2h0l-2-1c-3 0-4 0-7 1l-1 1c-2 1-4 4-5 6v1l-1-1c1-1 1-3 2-4 1-2 4-2 4-4v-2h0c1 0 1 0 1-1l1-2 1-1z" class="I"></path><path d="M386 469h0 4c1 1 2 1 3 1 1 1 1 1 1 2l-1 1-1 2c0 1 0 1-1 1h0v2c0 2-3 2-4 4-1 1-1 3-2 4-1 2-1 4-2 6h-1c0-3 3-6 4-9 0-2 1-3 2-4h0v-1c-2 1-2 3-3 4-2 3-3 6-5 9v1 1l-1 1c-1 2 0 5 0 7 1 1 1 1 1 2h-1v-1c-1-3-1-8-1-11-1 0-1 1-2 1 0 1 1 2 0 3h-1c0-1 1-3 0-4h0c0 1-1 1-1 2v1l-1 1c-1-2 0-4 0-5-1 1-1 2-2 4h-6c0-2 0-4 1-6v-1c2-4 5-7 10-9v-1c1 0 2-1 3-1l1 1v1h1c1-1 2-1 3-2h1c1-1 3-1 4-1-1-1-2-2-3-2v-4z" class="H"></path><path d="M386 469h0 4c1 1 2 1 3 1 1 1 1 1 1 2l-1 1-1 2c0 1 0 1-1 1h0-1c-4 2-6 3-9 6l-1 1v-1c0-1 1-3 2-3l3-3h0c1-1 3-1 4-1-1-1-2-2-3-2v-4z" class="M"></path><defs><linearGradient id="AJ" x1="491.599" y1="750.451" x2="627.941" y2="813.434" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#AJ)" d="M557 606c1 3 0 6 1 9l2 1h-2v1c4 1 8 1 12 1h4 2v1l1-1v28l3 1 1 1c1 1 1 1 1 3h0-3l-2 3 1 4v36 3c-1 1-2 2-3 1-3 0-6-1-9-1v3c2 1 5 5 6 7s3 3 5 5l1 1v12 10c0 3 0 9-1 11h0c-5 2-12 0-16 0l1 2 2 1 5 4v4 11l2 13c0 6 2 12 4 18 2 5 4 9 6 13v-1c1 1 2 3 2 4l7 11 5 7 3 4 2 2c0 2 0 2-1 4-1 0-2 1-3 2l-1 1-3 4c-1 1-2 1-2 2l-2 2-1-1 2-1-1-1c-1 1-3 4-4 4h-1-1c-1 0-2 0-4 1h-2-1v1c0 1 0 2-1 2h-3c1 2 3 2 5 2 4 3 10 5 14 9 2 0 4 1 6 2l10 3c-3 0-6-1-9-2v1l2 1c0 1 0 1-1 2l-1-1h-2c-1 1-1 1 0 1-1 0-2 1-3 1-8-1-17 1-25 2-8 0-17 4-25 6-1 1-2 1-4 1h0-3v1h-2v-1-2h-1c0-7-2-9-7-14-1-1-2-2-2-3-1 0 0-3 0-4l2-14v-5l-1-1h0v-1-1c1-2 1-1 1-2 1-2 0-3 0-4 2-2 0-5 1-7s1-1 1-2v-1l1 1 1-2c-2-2-1-10-1-13 0-1 0-3-1-4v-1-1-5h1l1-1c0-1 1-2 2-3h-1l-1-1c0-1 1-2 1-3h-6l-2-1h-1c1-2 1-4 1-6h-1-1-1v-1c1-1 1-2 1-3-1-1-1-1-1-2 1 0 2-1 3-2l1-2-4-2c0-1 0-1 1-2v2h2c-1-2-1-4-1-5 0-4 0-8 1-11 0-1 1-2 3-2v-2l1-1 1-1c-1-1-1 0-2 0s-2-1-2-2v-5c1-1 1-1 2-1l1-2c3 1 5 0 7 0l1-72h0l1-2v-3c1-1 1-4 1-6-1-1-1-1-1-2l1-1c0-1 0-2 1-3l1 1c1 1 1 5 0 7v1c0 1 0 1 1 2h0v-1c0-1 0-2 1-2 0-2 0-3 1-4v3l1 2v4l1 2h10c2 0 2 0 3-1v-1c-1-5 0-10-1-14 0-1 0-1 1-2v-1-6-13l1-24z"></path><path d="M545 677l2-1h1c1 1 1 1 1 3h0c-1-1-2-1-4-2z" class="G"></path><path d="M542 693l1-1c1 3 1 5 1 8h-1v-1l-1-1v-5h0z" class="F"></path><path d="M548 703v3c1 3 1 10 4 12l2-1 1 2h1c-1 0-3 0-4 1-1 0-1 0-1-1-4-2-3-9-4-13 0-1 0-2 1-3z" class="O"></path><path d="M542 748c-2 0-2-1-4-2v-1-3l1-1c1 1 3 1 5 1-1 2-1 4-2 6z" class="V"></path><path d="M559 696v-1c0-1 0-1-1-2l1-2h5l1 2c0 2 0 2-1 4l-5-1z" class="E"></path><path d="M540 827c1-1 1-1 3-1 1 1 1 2 2 3v1 4l2 2v1 3h0 0c0-2-2-3-3-4s-1-3-2-4c0-1-1-2-1-3h-1v-2z" class="R"></path><path d="M563 714c4 1 6 1 9 3v1h0l-1 2 1 1v1h-3c-3-1-5-1-7-1v-1h2c1-1 2-1 3-2-1-3-2-3-4-4z" class="E"></path><path d="M569 722c-1-2-1-2 0-4 0 0-1-1-1-2 1 0 2 1 3 2h1 0l-1 2 1 1v1h-3z" class="Q"></path><path d="M549 679c0 1 0 1 1 2h0v3h-1c-1 1-3 1-4 0-3-1-3-2-4-4v-1c0-1 0-1 1-2l1 1h0 1l1-1c2 1 3 1 4 2z" class="F"></path><path d="M555 774l-1 1c-1 0-2 1-3 1-2 0-4 1-6 1 0-1 0-1 1-1-1-1 0-1-1-2 1 0 1 0 2-1h-1v-1h9s1 0 1 1h11v1h-12z" class="C"></path><path d="M566 860c8 3 16 8 24 10 2 0 4 1 6 2l10 3c-3 0-6-1-9-2-9-3-18-4-27-9l3 1c-1-2-3-3-4-3s-1 0-2-1c-1 0-1 0-1-1z" class="B"></path><path d="M565 693v-2l1 1 1 2h3 3 5v3c-1 1-2 2-3 1-3 0-6-1-9-1v3l-2-1-1-1c-2-1-1-1-3 0-1 0-1-1-2-1l1-1 5 1c1-2 1-2 1-4z" class="F"></path><path d="M564 749l5 4v4 11l-1-1c-3-2-11-1-15-1v-1h14v-1h-10v-1c2 0 8 0 10-1h-2l-1-1v-1h3c-1-1-2-1-3-1l-1-1h3 0c1-2 1-2 0-3v-2c0-1-1-2-2-3v-1z" class="Z"></path><path d="M532 775c1 0 1 0 2-2v2h0l-1 1v1c1 1 2 2 2 3v2 1 4c-1 1-2 2-2 4v1 1c-2 0-2 1-3 2l1 1c1 2 0 3-1 4v1c-1 1-2 1-2 3l-1 2v-5h1l1-1c0-1 1-2 2-3h-1l-1-1c0-1 1-2 1-3 2-1 1 0 2-1l-1-1h-1v-1l-1-2h0l1-1h1v-2-1h0v-1c0-1 0-1 1-2l-1-1c1-1 1-1 1-2h-2v-1h1v-1c-1 0-1 0-1-1h-1 3z" class="B"></path><path d="M562 714h0c-2 0-3-1-4-2 2-1 2 1 4 0v-1c-2-1-2-1-3-2l1-1 2 2h1c0-1 0-1 1-1l1-1h1v-2l-2-2c-2 0-2 0-4 1l-1 1v-1c0-1-1-2-1-2v-1c1-1 2-2 3-2s2 0 3 1c1 0 1 1 2 1h1c1 1 2 1 2 3-1 0-1 1-1 1h0c0 2 0 4-1 5-1 2-2 2-4 2-1 0 0 0-1 1z" class="P"></path><path d="M560 852c3 0 6 5 10 7v-2c1-1 1-2 3-2 1 0 1 1 2 1v1c0 1 0 2-1 2h-3c1 2 3 2 5 2 4 3 10 5 14 9-8-2-16-7-24-10-1-2-1-2-2-2l1-1c-2-1-4-1-6-2v-1l7 3c-2-2-4-3-6-5h0z" class="U"></path><path d="M561 755h1v-1l-1-1c0-1-1-1-1-2l1-1c-1 0-2-1-2-2v-1c0-1-1-1-2-2-1 0 0 0-1-1 1 0 3 1 4 2h1l1 2 2 1v1c1 1 2 2 2 3v2c1 1 1 1 0 3h0-3l1 1c1 0 2 0 3 1h-3v1l1 1h-17v-1h8c2 0 4 0 5-1h0-3v-1h3v-1h-4v-1h4c-1-1-2-1-4-1v-1h4z" class="I"></path><path d="M562 748l2 1v1c1 1 2 2 2 3v2c1 1 1 1 0 3h0 0c-1-1-2-1-3-1l1-2v-3l-1-1c0-1 0-1-1-2v-1h0z" class="L"></path><path d="M556 773c4-1 9-1 13-1-2-1-3-1-4-1-2 0-4-1-6-1h0l9-1c-3-1-8-1-11-1 2-2 8-1 11-1l1 1 2 13h-3 0c-4-2-10 0-14-2h14c-1-1-3-1-5-2-2 0-3 1-5 0l9-1h0c-3-2-7 0-11-1l-1-1h12v-1h-11z" class="J"></path><path d="M525 744c4 0 7-1 10 2 0 1 0 3-1 5-2 1-3 1-5 1v1h2v1h0-5v-1l1-1c-1-1-1 0-2 0s-2-1-2-2v-5c1-1 1-1 2-1z" class="I"></path><path d="M570 864h0c-3-1-6-2-9-4-8-4-17-10-22-19l-3-4v-1c-1-2-2-3-2-5l-1-1v-1-2c1 0 1 1 1 1v1l1 2c2 6 6 13 12 17s12 9 18 11l1 1c0 1 0 1 1 1 1 1 1 1 2 1s3 1 4 3l-3-1z" class="G"></path><path d="M562 733h-2c-1-1-3-1-3-2-2-1-2-2-3-4 1-1 1-2 2-3 2-2 3-2 5-2h2 0v1l1 2v1c0 1 1 1 2 1-1 0-1-1-1-1l1-1 2 2c0 2-1 3-1 4 0 2 0 3 1 4h-7l1-1v-1z" class="Q"></path><path d="M562 733h-2c-1-1-3-1-3-2-2-1-2-2-3-4 1-1 1-2 2-3 2-2 3-2 5-2 0 1 1 2 1 2 1 2 2 2 1 4-1 1-1 1-2 0v-2c-1 0-1 1-2 2 0 1 0 1 1 2h0-2v1h0c2 0 3 1 4 2z" class="P"></path><path d="M558 675v-14c1-1 1-1 1-2v-1h2c2 0 2 0 3 1v13 4 9 2l1 1v1 1h-5 0c1-1 1-1 3-1v-1h-2v-1h1c-1-1-2-1-3-1-1-3 0-8-1-11z" class="E"></path><path d="M558 675v-14c1-1 1-1 1-2v-1h2l-2 28c-1-3 0-8-1-11z" class="B"></path><defs><linearGradient id="AK" x1="568.026" y1="725.175" x2="577.054" y2="724.977" xlink:href="#B"><stop offset="0" stop-color="#868484"></stop><stop offset="1" stop-color="#afadab"></stop></linearGradient></defs><path fill="url(#AK)" d="M564 699l2 1c2 1 5 5 6 7s3 3 5 5l1 1v12 10h-10c-1-1-1-2-1-4 0-1 1-2 1-4l-2-2-1 1s0 1 1 1c-1 0-2 0-2-1v-1l-1-2v-1c3 1 8 2 10 0 0-1 0-2-1-4h0v-1c-3-2-5-2-9-3h-1c1-1 0-1 1-1 2 0 3 0 4-2 1-1 1-3 1-5h0s0-1 1-1c0-2-1-2-2-3l-3-3h0z"></path><path d="M572 707c1 2 3 3 5 5l1 1c-1 2 0 3-1 5-1-2-2-3-2-5h0l-1-2c-2-1-3-2-2-4z" class="T"></path><path d="M568 706c1 2 2 2 2 4-1 2 1 5 2 7-3-2-5-2-9-3h-1c1-1 0-1 1-1 2 0 3 0 4-2 1-1 1-3 1-5z" class="e"></path><path d="M543 734s-1 1-2 1c-1-1-1-1-1-2l1-1c-1-2-3-3-3-5v-1c-1-1-1-2-1-3 1-1 1-2 1-3 1-1 0-3 0-4v-2c1-4 1-7 1-11v-6l-1-1h2v-2l1-1h1 0v5l1 1v1l-2 1v1h3c0 1 0 2 1 3v5c0 1-1 2-1 3h0-2-1c2 2 3 4 4 6 0 2 0 2 1 4h0l5 8c1 0 3 2 3 3-2 0-3 0-4 1h-1l-2-1c0-1 0-1-1-2h-1c0 1-1 1-1 2h-1z" class="O"></path><path d="M542 693v5l-1-1h-1l1-2c0-1 0-1 1-2z" class="G"></path><path d="M545 710h0c-1 2-2 2-3 2 0-1-1-2-1-2 0-2 0-5 1-6l3 1v5z" class="V"></path><path d="M543 734h1c0-1 1-1 1-2h1c1 1 1 1 1 2v2c1 0 1 1 2 2v1c2 0 3 1 4 1 1 1 2 2 2 3h0c-1 1-1 1-2 1l-1 1h-3l-1 1h1c3 0 6 1 8 2h-4s-1 0-1-1l-1 1c2 1 4 1 6 1v1h-5 0c1 1 2 1 4 1 1 1 3 1 4 2l-11-1h1c2 1 9 2 11 3h-4v1c2 0 3 0 4 1h-4v1h4v1h-3v1h3 0c-1 1-3 1-5 1-4-2-8-1-12-2h0l8-1h0c-3-1-6-1-9-1h-3c-1-4 0-6 2-9 1-2 1-4 2-6 0-2 0-5-1-8z" class="D"></path><path d="M549 739c2 0 3 1 4 1 1 1 2 2 2 3h0c-1 1-1 1-2 1h-4l1-1h2l1-1-2-1c-1 1-1 1-2 1v-3z" class="C"></path><path d="M543 757c2-2 6 0 9-1l-1-1h-7l-1-1c4 0 11-1 14 1v1c2 0 3 0 4 1h-4v1h4v1h-3v1h3 0c-1 1-3 1-5 1-4-2-8-1-12-2h0l8-1h0c-3-1-6-1-9-1z" class="M"></path><path d="M525 756c1-1 3-1 4 0h2c1 3 0 6 0 9 0 4 1 7 1 10h-3 1c0 1 0 1 1 1v1h-1v1h2c0 1 0 1-1 2l1 1c-1 1-1 1-1 2v1h0v1 2h-1l-1 1h0l1 2v1h1l1 1c-1 1 0 0-2 1h-6l-2-1h-1c1-2 1-4 1-6h-1-1-1v-1c1-1 1-2 1-3-1-1-1-1-1-2 1 0 2-1 3-2l1-2-4-2c0-1 0-1 1-2v2h2c-1-2-1-4-1-5 0-4 0-8 1-11 0-1 1-2 3-2z" class="E"></path><path d="M525 785h-2l1-1v-1c-1-1-1-4 0-5h2c0 1 1 5 0 6l-1 1z" class="X"></path><path d="M526 778h4 2c0 1 0 1-1 2l1 1c-1 1-1 1-1 2v1h0v1h-6l1-1c1-1 0-5 0-6zm3-3c-1 0-2 0-4-1l1-13c0-2-1-4 0-5h3 2c1 3 0 6 0 9 0 4 1 7 1 10h-3z" class="p"></path><defs><linearGradient id="AL" x1="556.05" y1="732.423" x2="562.81" y2="747.639" xlink:href="#B"><stop offset="0" stop-color="#1f1e1e"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#AL)" d="M545 719c3 6 6 12 13 15l3 1h7 10c0 3 0 9-1 11h0c-5 2-12 0-16 0h-1c-1-1-3-2-4-2 1 1 0 1 1 1 1 1 2 1 2 2v1c0 1 1 2 2 2l-1 1c0 1 1 1 1 2l1 1v1h-1c-2-1-9-2-11-3h-1l11 1c-1-1-3-1-4-2-2 0-3 0-4-1h0 5v-1c-2 0-4 0-6-1l1-1c0 1 1 1 1 1h4c-2-1-5-2-8-2h-1l1-1h3l1-1c1 0 1 0 2-1h0c0-1-1-2-2-3-1 0-2-1-4-1v-1c-1-1-1-2-2-2v-2l2 1h1c1-1 2-1 4-1 0-1-2-3-3-3l-5-8h0c-1-2-1-2-1-4z"></path><path d="M572 738h5c1 2 0 5 0 8h0-2v-1c-1 0-2-1-2-1v-1-2-1c0-1 0-1-1-2z" class="k"></path><defs><linearGradient id="AM" x1="557.155" y1="739.06" x2="572.571" y2="729.296" xlink:href="#B"><stop offset="0" stop-color="#0e0e10"></stop><stop offset="1" stop-color="#2d2b2a"></stop></linearGradient></defs><path fill="url(#AM)" d="M545 719c3 6 6 12 13 15l3 1h7 10c0 3 0 9-1 11 0-3 1-6 0-8h-5-6c-5 0-9-1-12-4 0-1-2-3-3-3l-5-8h0c-1-2-1-2-1-4z"></path><path d="M576 653l1 1 1 4v36h-5-3-3l-1-2s1 0 1-1h-1v-4-2h0-2v-9-4-13c1 1 1 1 2 1h3 0c1 0 1 0 2-1l-1-1v-3l2-1c1-1 3-1 4-1z" class="W"></path><path d="M571 687h2v1h-4c1 1 4 1 4 2-2 2-3 1-4 3h0l1 1h-3l-1-2s1 0 1-1h-1v-4h5z" class="M"></path><path d="M564 676l1 1h1l1 1h3 1v1h-2v1l1 1h0c-1 1-1 2-1 4l-2 1h0c2 0 3 1 4 1h-5v-2h0-2v-9z" class="B"></path><path d="M570 658v-3l2-1c0 2 0 6-1 8l2 2h-3v2 6c0 2-1 4 0 6h-3l-1-1h-1l-1-1v-4-13c1 1 1 1 2 1h3 0c1 0 1 0 2-1l-1-1z" class="R"></path><path d="M564 659c1 1 1 1 2 1v1h0v3 1c-1 2 0 5 0 8 0 1 0 1 1 2 0 1 0 1-1 2h0-1l-1-1v-4-13z" class="F"></path><path d="M573 694h0c0-1 1-1 1-2s1-3 0-4c0-1-1-1-1-1v-2c0-1 0-2 1-2 0-3 0-3-1-5 1-3 1-10 0-13h0l2-2c-1 0-1-1-2-2l2-2 1-1h-2v-1c2 0 2 1 4 1v36h-5z" class="H"></path><path d="M557 606c1 3 0 6 1 9l2 1h-2v1c4 1 8 1 12 1h4 2v1l1-1v28l3 1 1 1c1 1 1 1 1 3h0-3l-2 3-1-1c-1 0-3 0-4 1l-2 1v3l1 1c-1 1-1 1-2 1h0-3c-1 0-1 0-2-1s-1-1-3-1h-2v1c0 1 0 1-1 2v14h-1v-2h0l-1 2v1h0-1v1 7l-1 2c0 1 1 0 2 1l-1 1v2c0 1-1 2-1 4v2l-1-1c0-1-1-1-1-2-1-3-2-8 0-10h0 1l-1-2v-3l-1-3c0-2 0-5 2-7 2 0 2 0 3-1v-1c-1-5 0-10-1-14 0-1 0-1 1-2v-1-6-13l1-24z" class="O"></path><path d="M561 654l3-1v4h-1l-2-2v-1z" class="M"></path><path d="M563 657h-3l-1-1c1-2 1-2 2-2v1l2 2z" class="D"></path><path d="M564 657c3 0 2-2 3-3 0 0 2-1 3-1 1-1 0-1 2-1 1 0 3 1 4 1-1 0-3 0-4 1l-2 1v3c-3 0-5 0-7-1h1z" class="B"></path><path d="M574 618h2v1l1-1v28h-2c-2 0-3-4-4-6v-1c-1-2-4-3-5-5 0-1 0-1 1-2 4 4 6 9 9 14-1-9 0-18-1-28h-1z" class="L"></path><path d="M582 651c-8 1-17-1-24-1h-1v-1c1-1 1-1 1-2 1 0 2 0 3-1h-1-1c0-1 0-2 1-3s2-1 3-1v-1c-1 0-2 0-3-1h1 3 0 1c-2-1-3 0-5-2 1-1 1 0 2 0l-2-2 1-1c1 1 3 3 5 3 0-1-6-5-6-7v-1-1l1-1h0 2 1 0 2l1-1v1 2l-1 1 1 1c-1 1-1 1-1 2 1 2 4 3 5 5v1c1 2 2 6 4 6h2l3 1 1 1c1 1 1 1 1 3z" class="C"></path><defs><linearGradient id="AN" x1="551.073" y1="825.4" x2="589.048" y2="825.606" xlink:href="#B"><stop offset="0" stop-color="#5b5b5b"></stop><stop offset="1" stop-color="#8d8c8b"></stop></linearGradient></defs><path fill="url(#AN)" d="M540 829h-1l-1-1-2-4v-2l-1-1c0-1 0-1 1-2h0l-2-1v-3c-1-2-1-4-1-6h-1c1-1 1-3 1-4v1l1 5h0c0-4-1-9 0-12 0-2 0-4 1-5h0v-1-1l1-1v-1c0-3 1-7 0-9h0c0-1 1-1 2-2v-2h1c1 2 2 2 2 4 0 1 0 2 1 4h1l1-2c1 0 3-1 4-1h1v-1h-2v-1h2c2-1 3-1 5-1 4 2 10 0 14 2h0 3c0 6 2 12 4 18 2 5 4 9 6 13v-1c1 1 2 3 2 4l7 11 5 7 3 4 2 2c0 2 0 2-1 4-1 0-2 1-3 2l-1 1-3 4c-1 1-2 1-2 2l-2 2-1-1 2-1-1-1c-1 1-3 4-4 4h-1-1c-1 0-2 0-4 1h-2-1c-1 0-1-1-2-1-2 0-2 1-3 2v2c-4-2-7-7-10-7-1-1-3-2-4-3v-1c-1-1-4-3-4-5-2-2-2-2-3-5h2l-1-1-1 1-2-1v-1l-2-2v-4-1c-1-1-1-2-2-3-2 0-2 0-3 1v2z"></path><path d="M589 841v-1c0-2 0-4 1-7l1 7-2 1z" class="Y"></path><path d="M563 802l-1-2c2 0 2 2 4 2h1c1 2 1 3 3 4h0v1l-1-1-1 1 2 2v1c-1-2-3-3-4-4h0l-1-1c0-1-1-1-1-2h-1v-1z" class="Z"></path><path d="M590 833c-1-1-1-2-2-4l1-3h1l5 7-2 2c-1 1-2 3-2 5l-1-7z" class="X"></path><path d="M545 830l12 6 2 1-1 1h1l-1 1-1-1c-1-1-2-1-4-1l-2-2h-1l-1 1h1l2 2v1h-1-1c1 2 1 2 2 3v1c-2-2-2-2-3-5h2l-1-1-1 1-2-1v-1l-2-2v-4z" class="I"></path><path d="M591 840c0-2 1-4 2-5l2-2 3 4 2 2c0 2 0 2-1 4-1 0-2 1-3 2l-1 1-1-1h-1c0-1 0-1-1-2h0l-2 2c0-1 0-1-1-2v-2l2-1z" class="S"></path><path d="M598 837l2 2c0 2 0 2-1 4-1 0-2 1-3 2l-1 1-1-1h-1c0-1 0-1-1-2h0c1-2 3-3 5-5l1-1z" class="Y"></path><path d="M556 825l-10-10-2-2h-1l2-2h0l1-1v-1h0l6 6h1c1 1 2 1 4 2 1 1 3 2 5 3h-2c-1 0-2-1-4-2-1 0-2-1-3-2l-4-2 3 3 3 3c3 3 9 4 12 8-4-2-7-3-11-6h-1c1 2 3 3 5 4-1 0-2 0-3-1h-1z" class="E"></path><path d="M555 788c3 0 6 0 9-1l1 1c2 1 2 1 4 0l1 1c-2 0-3 1-5 1l1 1c1 0 2 0 4-1v1c-2 1-3 1-5 1l1 1h2v1h-3v1h2l-3 1c0 3 0 4 2 6-2 0-2-2-4-2l1 2c-2 0-4-2-5-4h0l-1-2c-1-2-2-3-2-4h1l1-1v-1c2 0 3 0 4-1-2 0-4 0-6-1z" class="U"></path><path d="M557 791h6c-2 1-3 1-5 1l1 1h4v1h-3c-1 0-2 1-3 1v1c-1-2-2-3-2-4h1l1-1z" class="I"></path><path d="M568 781h3c0 6 2 12 4 18 2 5 4 9 6 13v-1c1 1 2 3 2 4h-1c-1-2-1-2-2-3-1 0-1-2-1-2-2-1-2-3-4-4v-1c-1 0-1-1-1-2h0c-1-2-2-3-3-5v-3-2h0l-1-1-2 1h-2l-1-1c2 0 3 0 5-1v-1c-2 1-3 1-4 1l-1-1c2 0 3-1 5-1l-1-1c-2 1-2 1-4 0 1 0 3 0 4-1h-7l7-2v-1h-1-2c0-1 2-1 3-1-2-2-7 0-9-2h8z" class="Y"></path><path d="M540 827l-1-1 2-2v-1-1-1-3h0v-1l2 2h1c2 1 4 3 6 4s3 2 6 3h0 0c1 1 3 2 4 3h0-1c-4-2-8-4-11-6l-3-3h-1c0 1 2 2 3 3 1 2 3 2 4 3l13 9h0c-4-1-6-3-9-5s-6-3-8-5h0c4 4 10 8 15 11h0c-4-1-8-4-12-6h-1c4 3 8 5 12 8h0l-2-1-2-1-12-6v-1c-1-1-1-2-2-3-2 0-2 0-3 1z" class="E"></path><path d="M544 783c1 0 3-1 4-1h1v-1h-2v-1h2c2-1 3-1 5-1 4 2 10 0 14 2h0-8c2 2 7 0 9 2-1 0-3 0-3 1h2 1v1l-7 2h7c-1 1-3 1-4 1l-1-1c-3 1-6 1-9 1-1-1-1-1-3-1h0c-1 1 0 1-1 2h0v4l-1-1-1-1c-1 0-1-1-2-1h-1c-1-2-1-2 0-3 0-1-1-1-2-1v-3z" class="M"></path><path d="M546 787l1-1 1 1h0c0 1 1 1 1 2 1 1 1 2 1 3l-1-1c-1 0-1-1-2-1h-1c-1-2-1-2 0-3z" class="E"></path><path d="M553 784c2-1 5-1 7-1v-1h-4 0c1-1 2-1 4-1 2 2 7 0 9 2-1 0-3 0-3 1h2-1c-3 1-11 1-14 0z" class="I"></path><path d="M552 787h-1v-1l5-1h-7v-1h4c3 1 11 1 14 0h1 1v1l-7 2h7c-1 1-3 1-4 1l-1-1c-3 1-6 1-9 1-1-1-1-1-3-1z" class="Q"></path><defs><linearGradient id="AO" x1="535.004" y1="798.947" x2="553.114" y2="800.411" xlink:href="#B"><stop offset="0" stop-color="#252526"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#AO)" d="M540 829h-1l-1-1-2-4v-2l-1-1c0-1 0-1 1-2h0l-2-1v-3c-1-2-1-4-1-6h-1c1-1 1-3 1-4v1l1 5h0c0-4-1-9 0-12 0-2 0-4 1-5h0v-1-1l1-1v-1c0-3 1-7 0-9h0c0-1 1-1 2-2v-2h1c1 2 2 2 2 4 0 1 0 2 1 4h1l1-2v3c1 0 2 0 2 1-1 1-1 1 0 3h1c1 0 1 1 2 1l1 1 1 1v-4h0c1-1 0-1 1-2h0c2 0 2 0 3 1 2 1 4 1 6 1-1 1-2 1-4 1v1l-1 1h-1c0 1 1 2 2 4l1 2h0l-2 3v1c-1 1-1 2-2 4h0v2c-2 1-2 2-2 4 0 1 0 2 1 2 1 1 3 1 4 3-2-1-3-1-4-2h-1l-6-6h0v1l-1 1h0l-2 2h1l2 2 10 10v1h0c-3-1-4-2-6-3s-4-3-6-4h-1l-2-2v1h0v3 1 1 1l-2 2 1 1v2z"></path><path d="M553 806c0 1 0 1-1 1-4-3-8-9-9-15-1-2-1-4-1-6h1c0 3 2 6 3 9h0 1s1 0 1 1c2 3 3 4 2 8 1 1 2 2 3 2z" class="C"></path><defs><linearGradient id="AP" x1="545.2" y1="796.303" x2="555.992" y2="797.572" xlink:href="#B"><stop offset="0" stop-color="#313132"></stop><stop offset="1" stop-color="#545455"></stop></linearGradient></defs><path fill="url(#AP)" d="M543 785l1-2v3c1 0 2 0 2 1-1 1-1 1 0 3h1c1 0 1 1 2 1l1 1 1 1v-4h0c1-1 0-1 1-2h0c2 0 2 0 3 1 2 1 4 1 6 1-1 1-2 1-4 1v1l-1 1h-1c0 1 1 2 2 4l1 2h0l-2 3c-2 2-3 3-3 5-1 0-2-1-3-2 1-4 0-5-2-8 0-1-1-1-1-1h-1 0c-1-3-3-6-3-9h0v-1z"></path><path d="M462 313c1 1 1 2 2 2l-1 2h1l1-1v1 2h2 2v-3h2 0c0 1 1 2 1 2 1 0 2 0 3-1v1h1c0 2 0 4 1 6v1h1l1 1s0 1-1 1c1 1 1 2 2 3l-1 1c1 0 1 0 2 1h0c-1 2-1 3 0 4l-1 2h1 3l2-1 1-1h0v-1c0-1 0-2 1-3l2 2c0 3-2 7 1 10h1v14c-1 3 0 6-1 9 1 1 1 2 1 3 0-1 0-3 1-3h0c0 2 1 3 1 4v-9h1v-7l1 1c0 1 0 1 1 2v-1c1-1 1-2 1-3l-1-2c1-2 1-4 1-6h1c1 3 0 8 0 11h1v5l4-2 4-1 3 2v3c0 2 0 3 1 5v1 1c1 1 1 2 1 4v1h-1c1 3 1 6 1 9 0 2 0-1 0 2v1 1 1h0c1 2 1 3 0 5v1 3l-1 2h3v1l2 1h2v-3-3c1-1 1-2 1-4h0c1-1 2-2 3-2v3l1 1v-6l2-1 1-1c1 3-1 7 0 10v1 9l1 1v-4h1v1c1 0 1 0 1 1v4l1 3h0c1-1 1 0 1-1v-1h2l1-1c0 2 0 3-1 5v2l1 3v1c1 1 1 3 1 5l2-1 1-1 1 1c1 0 2 1 3 1v1l1 15c1 2 1 7 0 10h0 0c0 1 1 2 1 3s0 1 1 2v2l2 1v1l-1 1h2l1-1h2c1 1-1 3 1 3v1l-6-2-1-1c-1 1-1 2-1 3 1 0 1 1 2 1v1c1 1 2 3 2 4h0c3 5 4 7 5 13 0 1 0 4 1 5l1-1c0 2 0 5 1 7l1-2h0c1 4 2 8 2 12l1 1v-2c0-2 1-4 2-6 1 1 1 2 1 4h0l1-1c0 1 1 2 2 3v-2h5c3 0 5-1 8-1 1 0 1 1 2 0h1c2 1 6 0 9 0v2 1h-1-3v2l1 1-1 1 1 1c0 2 0 5-1 6 0 3 0 4 1 6h-4c-1 1 0 1-1 1s0 0-1 1h2 0-3v1l-2 1h2c2 0 3 1 4 1l1 2-1 1c-4-1-7-2-11-2h-12-6v5l-1 1h-3v-5c-2 0-4 1-7 1 1-1 3-2 4-2 2 0 4-1 5-2h-3c-3 0-5 0-7 1-1 1-1 1-1 3-1 0-1 0-2-1-1 2-2 3-2 5 0 1 0 1 1 2l-1 1-2 1h-1c0 2-1 4 1 6h-1v1 1c-1 2-1 2-3 3-1 0-1-1-1-2h-2-2-3v2l1 2v3 1h-2-1v2l-1-2c-1-1-2-1-4-1l1-4h3l1-1c-1-2-1-3-1-4v-1h-2v-20c-1-2 0-4-2-5l1-1-2-1c1-2 1-4 1-6 0 2-1 4-2 6l2 1c0 1-1 2-1 3h-1c-1-1-1-2-1-3h-13l-2-1c-2-1-4 0-6 0h-1v2l-2-1c-2 1-2 3-2 5 0-2 0-3 1-5 0-2-1-3-2-4s-1-4-1-6v-5c0-3 0-11-1-12v-2h0v-3l1-1c1-2 1-4 1-7v-2l1-6c-1-1-1-4-1-5v-12-1c-1-1-2-2-2-3s1-1 0-2h-1-5v-1h-2l1-1s1 0 1-1h-1c-1 0-1 0-2-1v-2c-1 0-1-1-2-1l-2 1v1h-1s0-1-1-1v-1-1l-1 1v3c-2 1-3 0-5-1v1c0 1 0 1-1 2h1c1 0 0 0 1 1h-1l-1 1h-1-1v1l1 1v2 4c-1 0-1 1-2 1v-4c-2-8 1-17-2-25v-3-11-47c2-3 1-10 0-13v-1h2c-2-2-2-9-3-12-1-1-1-2-1-4h0c1-1 3-2 4-3v-11-6-2z" class="H"></path><path d="M512 452h0c-1 2-2 2-3 2-2 0-4-1-5-2-2-2-2-4-2-6h1l1 2c1 1 3 3 4 3s2 0 3-1c0 1 1 1 1 2z" class="x"></path><path d="M504 448h1v-1c0-2 1-2 2-4h2l1 1v2c1 1 1 2 2 3l-1 1c-1 1-2 1-3 1s-3-2-4-3z" class="c"></path><path d="M510 433h1c1 1 1 1 3 1h4l-1 2c-2-1-2-1-4-1l-1 1-1 1c1 0 0 0 1 1 2 0 3 0 4 1h2v1c-1 0-1 1-1 2-1 0-1-1-2-1h-1-4l-2-2c-2-1-3 1-5-1 2 0 3 1 5 0 1 0 1-2 2-2v-1h-1v-1l1-1z" class="N"></path><path d="M494 449l1-1c1 8 0 17 0 25v33c0-2 0-3-1-4v-13-24c0-5 0-10-1-14h-2 1l2-2z" class="E"></path><path d="M518 439l1 1v2l-2 7v1l-1 3h0-1 0l-2-1h-1c0-1-1-1-1-2l1-1c-1-1-1-2-2-3v-2l-1-1 1 1v-1l-1-1 1-1h4 1c1 0 1 1 2 1 0-1 0-2 1-2v-1z" class="S"></path><path d="M512 449l1-1c1-2 0-4 1-6 1 0 1 1 2 2 0 1 0 3 1 4v1 1l-1 3h0-1 0l-2-1h-1c0-1-1-1-1-2l1-1z" class="T"></path><path d="M491 451h2c1 4 1 9 1 14v24 13c1 1 1 2 1 4v10h-1l-2-1c2-10 0-22 0-33 1-3 0-6 0-10 0-7 0-14-1-21z" class="J"></path><path d="M494 502c1 1 1 2 1 4v10h-1v-14zm-3-58l3 1c0 1 1 2 1 3l-1 1-2 2h-1c1 7 1 14 1 21 0 4 1 7 0 10s0 7 0 10c-2-2-1-10-1-13 0-2 0-4-1-6v-4c-1-1-1-4-1-5v-12-1c-1-1-2-2-2-3s1-1 0-2h-1-5v-1c2 0 4 1 6 0l4-1z" class="C"></path><path d="M492 451l-3-3 1-1 1-1c2 1 2 2 3 3l-2 2z" class="F"></path><path d="M490 469v4c1 2 1 4 1 6 0 3-1 11 1 13 0-3-1-7 0-10 0 11 2 23 0 33l2 1v2l-2-1c-2 1-2 3-2 5 0-2 0-3 1-5 0-2-1-3-2-4s-1-4-1-6v-5c0-3 0-11-1-12v-2h0v-3l1-1c1-2 1-4 1-7v-2l1-6z" class="R"></path><path d="M487 485l1-1c1-2 1-4 1-7v-2c1 6 0 11 0 16 0 3 0 5-1 7v4c0-3 0-11-1-12v-2h0v-3z" class="I"></path><path d="M490 473c1 2 1 4 1 6 0 3-1 11 1 13 0-3-1-7 0-10 0 11 2 23 0 33h-1c0-1 0-2-1-3s0-6 0-9v-30z" class="E"></path><path d="M487 428l1 3c0 2-1 3 1 5l2 1c1-1 1-2 2-4 0 1 1 2 2 3h1l1-1c2 0 2 0 4 1v1h0c-2 1-6 0-7 2v1c0 2 0 2-1 3l-1 1h-1l-4 1c-2 1-4 0-6 0h-2l1-1s1 0 1-1h-1l1-1v-1-3c1-1 1 0 2-1l-2-1h-1 0c1-2 0-2 1-3s1-2 2-4c0-1 0-1 1-1h3z" class="m"></path><path d="M490 438c2 0 2 0 2 1 0 2 0 2-1 3v1h-3v-3h0c1-2 1-2 2-2z" class="D"></path><path d="M494 403c2-2 1-5 1-8 1 2 1 3 1 5h2v5c1 0 1 0 1 1 1 1 0 2 1 3v13 8l-1 1c1 1 0 1 0 2 3 0 5-1 7 0h4 0l-1 1h-8v1 1h0c-2-1-2-1-4-1l-1 1h-1c-1-1-2-2-2-3-1 2-1 3-2 4l-2-1c-2-2-1-3-1-5l-1-3h0c-1-2-1-3-1-5 1-2 1-4 1-6v-4l1-2h1c1 1 2 3 3 4v-3c-1-3-1-3-3-4h-1v-3h1c0-2 0-4 1-6l1 1 1-1 2 2v2z" class="AF"></path><path d="M497 435l-1-1v-11h1c1 2 0 4 1 7h0c0 1 0 2 1 2v1c3 0 5-1 7 0h4 0l-1 1h-8v1 1h0c-2-1-2-1-4-1z" class="AQ"></path><path d="M488 405h1c0-2 0-4 1-6l1 1 1-1 2 2v2 4h1v9h0c-2-2-1-7-1-7l-3-1h-2-1v-3z" class="i"></path><path d="M494 403c2-2 1-5 1-8 1 2 1 3 1 5h2v5c1 0 1 0 1 1 1 1 0 2 1 3v13 8l-1 1c-2-5 0-10-1-15 0-1-1-2-1-2 0-1 1-1 1-2h-1c-1-1-1-4-1-6h-1v1h-1v-4z" class="o"></path><path d="M487 413l1-2h1c1 1 2 3 3 4 0 4-1 10 1 13l-1 4 1 1c-1 2-1 3-2 4l-2-1c-2-2-1-3-1-5l-1-3h0c-1-2-1-3-1-5 1-2 1-4 1-6v-4z" class="AV"></path><path d="M501 394h3 4l1 1h-1c1 1 1 1 2 1 1 2 1 4 1 5h1 3v1c1 7-1 14 0 21l-1 4v6 1c-2 0-2 0-3-1h-1 0-4c-2-1-4 0-7 0 0-1 1-1 0-2l1-1v-8-13-7l1-8z" class="AJ"></path><path d="M501 410l1 1-1 1h3v1c-1 1-1 1-1 2-1-1-1-1-2-1h0c-1-2-1-2 0-4z" class="l"></path><path d="M500 409l1 1c-1 2-1 2 0 4h0c1 0 1 0 2 1 0 1 0 5-1 6l-1 1h2v1 1h0c2 1 1 3 1 4v1c-2 0-2 1-3 2v1h0c1-1 2 0 3 0v-1l1-1 1 1c1 0 2 0 2 1h1l1-1v2h-4c-2-1-4 0-7 0 0-1 1-1 0-2l1-1v-8-13z" class="AI"></path><path d="M509 419c0-1-1-3-1-4h0v-2-2-2c0-1-1-1-1-2h-1c0-1 1-1 1-1v-3h1c0 1 0 2 1 3v-5h2v16c-1 2-1 7 0 9v1 6h-1 0v-2l-1 1h-1c0-1-1-1-2-1l-1-1-1 1v1c-1 0-2-1-3 0h0v-1c1-1 1-2 3-2h3l1 1 1-1v-10z" class="a"></path><path d="M504 394h4l1 1h-1c1 1 1 1 2 1 1 2 1 4 1 5h-2v5c-1-1-1-2-1-3h-1v3s-1 0-1 1h1c0 1 1 1 1 2v2 2 2h0c0 1 1 3 1 4h-1l-1-1c-1-3-1-5-2-8l-2-1h-2v-1l1-1v-2c1-1 0 0 0-2v-2h1c0-1 0-1 1-2h0v-1h-3v-1h0l3-3z" class="AG"></path><path d="M512 401h3v1c1 7-1 14 0 21l-1 4v6 1c-2 0-2 0-3-1v-6-1c-1-2-1-7 0-9v-16h1z" class="w"></path><path d="M517 450c1-1 2-1 3-1v1 17c0 3 0 6 1 9h0c-1 1-1 2-2 3-1 8 0 17 0 25v6c0 2-1 4-2 6l2 1c0 1-1 2-1 3h-1c-1-1-1-2-1-3h-13l-2-1h10v-16l-1-29c0-5 0-10 1-15 0-2 1-3 2-4l2 1h0 1 0l1-3z" class="b"></path><path d="M517 450c1-1 2-1 3-1v1 17c0 3 0 6 1 9h0c-1 1-1 2-2 3-1 8 0 17 0 25v6c0 2-1 4-2 6l2 1c0 1-1 2-1 3h-1c-1-1-1-2-1-3h-13l-2-1h10 6l-1-63 1-3z" class="B"></path><path d="M494 362h1v-7l1 1c0 1 0 1 1 2v-1c1-1 1-2 1-3l-1-2c1-2 1-4 1-6h1c1 3 0 8 0 11h1v5l4-2 4-1 3 2v3c0 2 0 3 1 5v1 1c1 1 1 2 1 4v1h-1c1 3 1 6 1 9 0 2 0-1 0 2v1 1 1h0c1 2 1 3 0 5v1 3l-1 2h-1c0-1 0-3-1-5-1 0-1 0-2-1h1l-1-1h-4-3l-1 8v7c-1-1 0-2-1-3 0-1 0-1-1-1v-5h-2c0-2 0-3-1-5 0 3 1 6-1 8v-2-12h0l1-1c1-1 0-3 0-5-2-3 0-5-1-9v-3-9z" class="AN"></path><path d="M500 362l4-2c1 1 2 3 2 5h-1c-1 2 0 3-1 5s0 9 0 12v1l-2 2h1c1 0 2 1 3 1h1l1 1-1 1h-4v-1h-3v-2-8-2c-1-4 0-9 0-13z" class="AG"></path><path d="M495 383v-14l1-1c0 2 1 2 0 4v7h3c0 1 1 2 0 4l-1 1 1 1h0 1v2h3v1h-1-1c-1 2-1 4 0 6l-1 8v7c-1-1 0-2-1-3 0-1 0-1-1-1v-5h-2c0-2 0-3-1-5 0 3 1 6-1 8v-2-12h0l1-1c1-1 0-3 0-5z" class="AC"></path><path d="M498 395v-5h-1c-1-1-1-2-1-3 1-2-1-6 0-8h3c0 1 1 2 0 4l-1 1 1 1h0 1v2h3v1h-1-1c-1 2-1 4 0 6l-1 8c0-1 0-2-1-3 0-1-1-3-1-4z" class="AP"></path><path d="M498 395c0-2 1-4 1-6v1-1c0-2 0-1 1-2h3v1h-1-1c-1 2-1 4 0 6l-1 8c0-1 0-2-1-3 0-1-1-3-1-4z" class="z"></path><path d="M508 359l3 2v3c0 2 0 3 1 5v1 1c1 1 1 2 1 4v1h-1c1 3 1 6 1 9 0 2 0-1 0 2v1 1 1h0c1 2 1 3 0 5v1 3l-1 2h-1c0-1 0-3-1-5-1 0-1 0-2-1h1l-1-1h-4-3c-1-2-1-4 0-6h1 1 4l1-1-1-1h-1c-1 0-2-1-3-1h-1l2-2v-1c0-3-1-10 0-12s0-3 1-5h1c0-2-1-4-2-5l4-1z" class="AR"></path><path d="M508 387h1c1 2 1 3 1 6h-1l-2-2v-3l1-1z" class="AA"></path><path d="M511 364c0 2 0 3 1 5v1 1c1 1 1 2 1 4v1h-1c0 1 0 3-1 4v4c0 3 1 8 0 11l-1-23c1-1 1-3 1-4v-4z" class="AO"></path><path d="M511 395c1-3 0-8 0-11v-4c1-1 1-3 1-4 1 3 1 6 1 9 0 2 0-1 0 2v1 1 1h0c1 2 1 3 0 5v1 3l-1 2h-1c0-1 0-3-1-5l1-1z" class="r"></path><path d="M508 359l3 2v3 4c0 1 0 3-1 4h0c0 4 1 9-1 13-1 0-1-1-2-2s-2-1-3-1c0-3-1-10 0-12s0-3 1-5h1c0-2-1-4-2-5l4-1z" class="AJ"></path><path d="M507 372c1 1 2 1 2 2v2 4l-1 1c0-3 0-6-1-9z" class="AA"></path><path d="M507 372s0-1-1-1l1-2 1-1c2 3 2 5 1 8v-2c0-1-1-1-2-2z" class="AR"></path><path d="M508 368h3c0 1 0 3-1 4h0c0 4 1 9-1 13-1 0-1-1-2-2 0-1 0-2 1-2l1-1v-4c1-3 1-5-1-8z" class="AT"></path><path d="M526 388l1-1c1 3-1 7 0 10v1 9l1 1v-4h1v1c1 0 1 0 1 1v4l1 3h0c1-1 1 0 1-1v-1h2l1-1c0 2 0 3-1 5v2l1 3v1c1 1 1 3 1 5l2-1 1-1 1 1c1 0 2 1 3 1v1l1 15c-2 1-2 2-2 4-1 0-1 1-1 2h0c-1 1-2 1-2 2l-1 3 1 1c-1 0-1 0-2 1v-1c-1 0-1 0-1-1l-1-1-3 3v-2c1-1 3-2 5-4-2-1-3-1-4-1l-1-2-1-1h0c-2-1-3-1-5-1l-1 1h3v1c-2 0-2 0-4-1l-4 3v2h0v-1c-1 0-2 0-3 1v-1l2-7v-2l-1-1h-2c-1-1-2-1-4-1-1-1 0-1-1-1l1-1 1-1c2 0 2 0 4 1l1-2h-4v-1-6l1-4c-1-7 1-14 0-21l2 1h2v-3-3c1-1 1-2 1-4h0c1-1 2-2 3-2v3l1 1v-6l2-1z" class="q"></path><path d="M528 404h1v1c1 0 1 0 1 1v4l-1-2c0 1 0 1-1 2-1 0-1 0-1 1l-1 1-1 4-2 2-1-1c0-1 0-2 1-2 0-2 1-3 2-4s1-1 1-2h2v-1-4z" class="AQ"></path><path d="M514 427c2-2 2-3 2-5v-5l1 1c0 1 0 2 1 4v1 1 6l2 1c-2 1-3 1-4 2h-2v-6z" class="AV"></path><path d="M523 418l2-2v1 4 1c-1 3 0 9 0 13-1-1-1-2-1-3s0-1-1-2v-3l-3-1v-1c1-3 0-6 2-8l1 1z" class="y"></path><path d="M522 417l1 1c0 1-1 2 0 3 1 2 1 4 1 6h-1l-3-1v-1c1-3 0-6 2-8z" class="AV"></path><path d="M526 388l1-1c1 3-1 7 0 10v1h-1c-1 2 0 5-1 8v-2c0-2 0-5-1-7v8c-1-1-1-2-1-3-1 0-1 0-2 1v-1c-2 2-1 6-2 9h0c-2-3 0-6-2-8h2v-3-3c1-1 1-2 1-4h0c1-1 2-2 3-2v3l1 1v-6l2-1z" class="i"></path><path d="M527 411c0-1 0-1 1-1 1-1 1-1 1-2l1 2 1 3h0c1-1 1 0 1-1v-1h2l1-1c0 2 0 3-1 5v2l1 3v1c1 1 1 3 1 5 0 1 0 0-1 1l-1 1 1 2-1 1c-1 0-1 1-2 1v1l-2 2h-5c0-4-1-10 0-13v-1-4-1l1-4 1-1z" class="m"></path><path d="M525 417c1 0 1 0 2 1h2l1 3h-5v-4z" class="J"></path><path d="M525 422h4c2 4 0 9 1 13h-5c0-4-1-10 0-13z" class="c"></path><path d="M527 411c0-1 0-1 1-1 1-1 1-1 1-2l1 2 1 3h0c1-1 1 0 1-1v-1h2l1-1c0 2 0 3-1 5v2l1 3v1h-2l-1-1c0 1-1 1-1 2l-1-1-1-3h-2c-1-1-1-1-2-1v-1l1-4 1-1z" class="y"></path><path d="M534 411l1-1c0 2 0 3-1 5v2h-2c-1-1 0 0-1 1v-5h0c1-1 1 0 1-1v-1h2z" class="AQ"></path><path d="M527 411h2v7h-2c-1-1-1-1-2-1v-1l1-4 1-1z" class="AE"></path><path d="M538 425l1-1 1 1c1 0 2 1 3 1v1l1 15c-2 1-2 2-2 4-1 0-1 1-1 2h0c-1 1-2 1-2 2l-1 3 1 1c-1 0-1 0-2 1v-1c-1 0-1 0-1-1l-1-1-3 3v-2c1-1 3-2 5-4-2-1-3-1-4-1l-1-2-1-1h0c-2-1-3-1-5-1l-1 1h3v1c-2 0-2 0-4-1l-4 3v2h0v-1c-1 0-2 0-3 1v-1l2-7v-2l-1-1h-2c-1-1-2-1-4-1-1-1 0-1-1-1l1-1 1-1c2 0 2 0 4 1l1-2h-4v-1h2c1-1 2-1 4-2v-5l3 1v3c1 1 1 1 1 2s0 2 1 3h5l2-2v-1c1 0 1-1 2-1l1-1-1-2 1-1c1-1 1 0 1-1l2-1z" class="P"></path><path d="M531 443c-2 0-4 0-6-1v-2-1c2-1 3-1 5 0 1 0 1 1 1 2v2z" class="W"></path><path d="M530 435l2-2v-1c1 0 1-1 2-1l1-1-1-2 1-1c1 3 1 5 0 8-1 1-2 1-4 1l-1-1z" class="D"></path><path d="M519 442v2c2-1 1-2 2-1l3 2-4 3v2h0v-1c-1 0-2 0-3 1v-1l2-7z" class="m"></path><path d="M521 443l3 2-4 3c0-2 0-4 1-5z" class="I"></path><path d="M531 441c1 1 1 2 1 3 1-2 1-4 2-6h1c1 1 1 3 0 5v2 2l1 1 1-1h2v1c-1 1-1 1-2 1-2-1-3-1-4-1l-1-2v-1c-1-1-1-1-1-2h0v-2z" class="B"></path><path d="M520 426l3 1v3c1 2 1 4 0 6-2 1-3 1-4 1l-1-3h-4v-1h2c1-1 2-1 4-2v-5z" class="m"></path><path d="M538 425l1-1 1 1c1 0 2 1 3 1v1l1 15c-2 1-2 2-2 4-1 0-1 1-1 2-1-2-2-3-2-5l-1-18z" class="X"></path><path d="M543 427l1 15c-2 1-2 2-2 4-1 0-1 1-1 2-1-2-2-3-2-5s1-3 2-4l1-1v-7h-2v-4l2 1 1-1z" class="S"></path><defs><linearGradient id="AQ" x1="472.261" y1="380.112" x2="478.669" y2="379.824" xlink:href="#B"><stop offset="0" stop-color="#cac1b0"></stop><stop offset="1" stop-color="#eeeae7"></stop></linearGradient></defs><path fill="url(#AQ)" d="M462 313c1 1 1 2 2 2l-1 2h1l1-1v1 2h2 2v-3h2 0c0 1 1 2 1 2 1 0 2 0 3-1v1h1c0 2 0 4 1 6v1h1l1 1s0 1-1 1c1 1 1 2 2 3l-1 1c1 0 1 0 2 1h0c-1 2-1 3 0 4l-1 2h1 3l2-1 1-1h0v-1c0-1 0-2 1-3l2 2c0 3-2 7 1 10h1v14c-1 3 0 6-1 9 1 1 1 2 1 3 0-1 0-3 1-3h0c0 2 1 3 1 4v3c1 4-1 6 1 9 0 2 1 4 0 5l-1 1h0v12l-2-2-1 1-1-1c-1 2-1 4-1 6h-1v3h1c2 1 2 1 3 4v3c-1-1-2-3-3-4h-1l-1 2v4c0 2 0 4-1 6 0 2 0 3 1 5h0-3c-1 0-1 0-1 1-1 2-1 3-2 4s0 1-1 3h0 1l2 1c-1 1-1 0-2 1v3 1l-1 1c-1 0-1 0-2-1v-2c-1 0-1-1-2-1l-2 1v1h-1s0-1-1-1v-1-1l-1 1v3c-2 1-3 0-5-1v1c0 1 0 1-1 2h1c1 0 0 0 1 1h-1l-1 1h-1-1v1l1 1v2 4c-1 0-1 1-2 1v-4c-2-8 1-17-2-25v-3-11-47c2-3 1-10 0-13v-1h2c-2-2-2-9-3-12-1-1-1-2-1-4h0c1-1 3-2 4-3v-11-6-2z"></path><path d="M477 324v1h1l1 1s0 1-1 1v4c-2-1-3-2-4-3v-1l-1-1c1-1 2-1 4-2z" class="i"></path><path d="M478 327c1 1 1 2 2 3l-1 1c1 0 1 0 2 1h0c-1 2-1 3 0 4l-1 2h1v7c-3 5-2 13-2 18l-1-3v-29h0v-4z" class="r"></path><path d="M473 360v-1-1l1-2v53h-1c-2-2-1-2-1-4h-1c-1-2 0-4 0-6v-6h0c0-1-1-2-1-3v-3h-2v-1h5v-1c-1-1 0-2 0-3 1-2 0-5 0-7v-15z" class="j"></path><path d="M478 360l1 3v7l1-1v6c1 3 0 5 0 8-1 2 0 4-1 5 0 3-1 11 1 12 1-2 0-8 1-10v2l1 2c0 5 0 9-1 14l-1 1c-1-1-1-2-2-3v3h0v-49z" class="f"></path><path d="M479 370l1-1v6c1 3 0 5 0 8-1 2 0 4-1 5v-18z" class="r"></path><path d="M466 382h1c1 1 2 1 2 2l-1 2h0v1h2v3c0 1 1 2 1 3h0v6c0 2-1 4 0 6h1c0 2-1 2 1 4h1v2l-1-1h0c-2-1-5 0-6 1l-1-29z" class="U"></path><path d="M469 399h2c0 2-1 4 0 6 0 1 0 1-1 2l-2-1c1-2 1-4 1-7h0z" class="J"></path><path d="M468 387h2v3c0 1 1 2 1 3h0v6h-2c-2-3-1-9-1-12zm-2-28h3v1c-1 1-1 1-1 2 3 0 3-1 5-2v15c0 2 1 5 0 7 0 1-1 2 0 3v1h-5 0l1-2c0-1-1-1-2-2h-1v-15-8z" class="s"></path><path d="M466 367h1v-3c1 1 1 2 1 4l1 1-1 1v3l1 1c-1 1-1 1-1 2 1 1 1 2 1 3v1h-2v1c1 0 2 0 3 1h-3-1v-15z" class="j"></path><path d="M478 409h0c0 2 0 4 1 6 1 0 3 1 4 1h-1l-1 2h2c1-1 2-1 3-1h1c0 2 0 4-1 6 0 2 0 3 1 5h0-3c-1 0-1 0-1 1-1 2-1 3-2 4s0 1-1 3h0 1l2 1c-1 1-1 0-2 1v3 1l-1 1c-1 0-1 0-2-1v-2c-1 0-1-1-2-1l-2 1c-1-4 0-11 0-16h1v6-4l1-2c-1-1-1-2 0-2 1-2 0-5 0-6l1-1v-4l1-2z" class="P"></path><path d="M474 424h1v6-4l1-2c-1-1-1-2 0-2 1-2 0-5 0-6l1-1v-4 12c0 5 1 11-1 16l-2 1c-1-4 0-11 0-16z" class="K"></path><path d="M467 411c1-1 4-2 6-1h0l1 1v13c0 5-1 12 0 16v1h-1s0-1-1-1v-1-1l-1 1v3c-2 1-3 0-5-1h0l1-2h-1l1-2-1-1v-3-4l1-11-1-2c0-2 0-3 1-5z" class="v"></path><path d="M467 418c2-1 3-1 5-1v4 1 3 1h-1-3v1l1 1h1v2c-1-1-1 0-2-1v2h-1c0-1 0-2-1-2l1-11z" class="J"></path><path d="M472 425l-1-1c-1-1-2-1-3-1h-1c1-1 1-2 3-2 0 0 1 1 2 1h0v3z" class="U"></path><path d="M467 411c1-1 4-2 6-1h0l1 1v13c0 5-1 12 0 16v1h-1s0-1-1-1v-1-1-7c1-3 0-6 1-9h0l-1-1v-4c-2 0-3 0-5 1l-1-2c0-2 0-3 1-5z" class="Z"></path><path d="M472 438v-7c1-3 0-6 1-9 0 4 0 16 1 19h-1s0-1-1-1v-1-1z" class="E"></path><path d="M474 327v1c0 3 1 7 0 11v16 1l-1 2v1 1c-2 1-2 2-5 2 0-1 0-1 1-2v-1h-3 0c-1-3-1-5-1-7l1-1h-2c-1 1-1 0-2 0-2-2-2-9-3-12 1 1 3 2 4 4v1l1 1v-3h0v-1c1 0 1-2 2-3 1 1 2 1 3 2 1 0 1 1 3 1v-3-2c0-1 0-2 1-2 1-2 0-5 1-7z" class="AE"></path><path d="M459 339c1 1 3 2 4 4v1l1 1v-3h0c1-1 2-1 3-1v1h-1v1 16c-1-3-1-5-1-7l1-1h-2c-1 1-1 0-2 0-2-2-2-9-3-12z" class="q"></path><path d="M474 327v1c0 3 1 7 0 11v16h-3v-1l-2 2h-1c0-1 1-3 2-4l-1-1h-1v-1c1-1 3-2 3-4h-1v-3c-1-1-1-2-1-3 1 0 1 1 3 1v-3-2c0-1 0-2 1-2 1-2 0-5 1-7z" class="w"></path><path d="M474 327v1c0 3 1 7 0 11v1c-1 1-1 3-1 5l-1 1v-2-3-3-2c0-1 0-2 1-2 1-2 0-5 1-7z" class="u"></path><path d="M480 375l2-4h1c0 2 0 6 1 7v-2l1 1v4c0 2 0 5 1 6 0 1 0 1 1 2v11 4l1 1v3h1c2 1 2 1 3 4v3c-1-1-2-3-3-4h-1l-1 2v4h-1c-1 0-2 0-3 1h-2l1-2h1c-1 0-3-1-4-1-1-2-1-4-1-6v-3c1 1 1 2 2 3l1-1c1-5 1-9 1-14l-1-2v-2c-1 2 0 8-1 10-2-1-1-9-1-12 1-1 0-3 1-5 0-3 1-5 0-8z" class="AC"></path><path d="M482 394v-10l1 1v5c1 4-1 10 1 13 0-1 0-2 1-3 0 2 0 6 2 8v-4l1 1v3h1c2 1 2 1 3 4v3c-1-1-2-3-3-4h-1l-1 2v4h-1c-1 0-2 0-3 1h-2l1-2h1c-1 0-3-1-4-1-1-2-1-4-1-6v-3c1 1 1 2 2 3l1-1c1-5 1-9 1-14z" class="AF"></path><path d="M487 404l1 1v3h1c2 1 2 1 3 4v3c-1-1-2-3-3-4h-1l-1 2v-1c-1-1-1-1-2-1h0c-1-1-1-2 0-3h2v-4z" class="q"></path><path d="M462 313c1 1 1 2 2 2l-1 2h1l1-1v1 2h2 2v-3h2 0c0 1 1 2 1 2 1 0 2 0 3-1v1h1c0 2 0 4 1 6-2 1-3 1-4 2l1 1c-1 2 0 5-1 7-1 0-1 1-1 2v2 3c-2 0-2-1-3-1-1-1-2-1-3-2-1 1-1 3-2 3v1h0v3l-1-1v-1c-1-2-3-3-4-4s-1-2-1-4h0c1-1 3-2 4-3v-11-6-2z" class="i"></path><path d="M464 328v-3l1-1v1c0 2 0 2 1 4v9h0c-1 1-1 3-2 3-2-1 0-2 0-3s-3-3-3-4c1 0 2 0 2-1 1-1 1-3 1-5z" class="AF"></path><path d="M462 321c0 2 1 3 1 4s0 2 1 3c0 2 0 4-1 5 0 1-1 1-2 1 0 1 3 3 3 4s-2 2 0 3v1h0v3l-1-1v-1c-1-2-3-3-4-4s-1-2-1-4h0c1-1 3-2 4-3v-11z" class="n"></path><path d="M469 316h2 0c0 1 1 2 1 2 1 0 2 0 3-1v1h1c0 2 0 4 1 6-2 1-3 1-4 2l1 1c-1 2 0 5-1 7-1 0-1 1-1 2v2h-1l-2-1v-4c0-4-1-10 1-13l-1-1v-3z" class="AD"></path><path d="M471 328l1 8v2h-1l-2-1v-4h0l2-5z" class="n"></path><path d="M475 317v1h1c0 2 0 4 1 6-2 1-3 1-4 2v-2h1c-2-2-1-4-2-6 1 0 2 0 3-1z" class="f"></path><path d="M470 320h2c1 2-1 6-1 8l-2 5h0c0-4-1-10 1-13z" class="q"></path><path d="M462 351c1 0 1 1 2 0h2l-1 1c0 2 0 4 1 7h0v8 15l1 29c-1 2-1 3-1 5l1 2-1 11v4 3l1 1-1 2h1l-1 2h0v1c0 1 0 1-1 2h1c1 0 0 0 1 1h-1l-1 1h-1-1v1l1 1v2 4c-1 0-1 1-2 1v-4c-2-8 1-17-2-25v-3-11-47c2-3 1-10 0-13v-1h2z" class="P"></path><path d="M486 337l1-1h0v-1c0-1 0-2 1-3l2 2c0 3-2 7 1 10h1v14c-1 3 0 6-1 9 1 1 1 2 1 3 0-1 0-3 1-3h0c0 2 1 3 1 4v3c1 4-1 6 1 9 0 2 1 4 0 5l-1 1h0v12l-2-2-1 1-1-1c-1 2-1 4-1 6h-1l-1-1v-4-11c-1-1-1-1-1-2-1-1-1-4-1-6v-4l-1-1v2c-1-1-1-5-1-7h-1l-2 4v-6l-1 1v-7c0-5-1-13 2-18v-7h3l2-1z" class="u"></path><path d="M486 354h1l1-1v1 11h0c0 2-1 6 0 8h0v7h0v1h-1v8c-1-1-1-1-1-2-1-1-1-4-1-6l1-1c1-1 0-5 0-6v-20z" class="z"></path><path d="M481 345v4 2l1-1v7c0 2 1 3 1 5v9h0-1l-2 4v-6l-1 1v-7c0-5-1-13 2-18z" class="f"></path><path d="M481 345v4 2c1 3 0 8 0 11s0 5-1 7l-1 1v-7c0-5-1-13 2-18z" class="AH"></path><path d="M486 337l1-1h0v-1c0-1 0-2 1-3l2 2c0 3-2 7 1 10h1v14c-1 3 0 6-1 9v-3c-1-1-1-1-2-1l-1 2h0v-11-1l-1 1h-1v-4h-4l-1 1v-2-4-7h3l2-1z" class="AP"></path><path d="M484 338l2-1c1 2 1 3 1 5l-1 1v1c-1-1-1-1-1-2h-1c0 1 0 2 1 3h-1v1h1 1l1-1 1 2h-1c-1 1-1 2-1 3h0-4l-1 1v-2-4-7h3zm4 27l1-2c1 0 1 0 2 1v3c1 1 1 2 1 3 0-1 0-3 1-3h0c0 2 1 3 1 4v3c1 4-1 6 1 9 0 2 1 4 0 5l-1 1h0v12l-2-2-1 1-1-1c-1 2-1 4-1 6h-1l-1-1v-4-11-8h1v-1h0v-7h0c-1-2 0-6 0-8z" class="o"></path><path d="M488 365l1-2c1 0 1 0 2 1v3c1 1 1 2 1 3 0-1 0-3 1-3h0c0 2 1 3 1 4v3c1 4-1 6 1 9 0 2 1 4 0 5l-1 1h0-2v-6c-1-1-1-2-2-3 0-1-1-5 0-6h1l-1-1h-2 0c-1-2 0-6 0-8z" class="i"></path><path d="M492 370c0-1 0-3 1-3h0c0 2 1 3 1 4v3c1 4-1 6 1 9 0 2 1 4 0 5l-1 1v-5c-1 1-1 2-1 3l1 1h-1v-3-1l-1-2 1-1c-2-3-1-8-1-11z" class="o"></path><path d="M541 448c0-1 0-2 1-2 0-2 0-3 2-4 1 2 1 7 0 10h0 0c0 1 1 2 1 3s0 1 1 2v2l2 1v1l-1 1h2l1-1h2c1 1-1 3 1 3v1l-6-2-1-1c-1 1-1 2-1 3 1 0 1 1 2 1v1c1 1 2 3 2 4h0c3 5 4 7 5 13 0 1 0 4 1 5l1-1c0 2 0 5 1 7l1-2h0c1 4 2 8 2 12l1 1v-2c0-2 1-4 2-6 1 1 1 2 1 4h0l1-1c0 1 1 2 2 3v-2h5c3 0 5-1 8-1 1 0 1 1 2 0h1c2 1 6 0 9 0v2 1h-1-3v2l1 1-1 1 1 1c0 2 0 5-1 6 0 3 0 4 1 6h-4c-1 1 0 1-1 1s0 0-1 1h2 0-3v1l-2 1h2c2 0 3 1 4 1l1 2-1 1c-4-1-7-2-11-2h-12-6v5l-1 1h-3v-5c-2 0-4 1-7 1 1-1 3-2 4-2 2 0 4-1 5-2h-3c-3 0-5 0-7 1-1 1-1 1-1 3-1 0-1 0-2-1-1 2-2 3-2 5 0 1 0 1 1 2l-1 1-2 1h-1c0 2-1 4 1 6h-1v1 1c-1 2-1 2-3 3-1 0-1-1-1-2h-2-2-3v2l1 2v3 1h-2-1v2l-1-2c-1-1-2-1-4-1l1-4h3l1-1c-1-2-1-3-1-4v-1h-2v-20c-1-2 0-4-2-5l1-1-2-1c1-2 1-4 1-6v-6c0-8-1-17 0-25 1-1 1-2 2-3h0c-1-3-1-6-1-9v-17h0v-2l4-3c2 1 2 1 4 1v-1h-3l1-1c2 0 3 0 5 1h0l1 1 1 2c1 0 2 0 4 1-2 2-4 3-5 4v2l3-3 1 1c0 1 0 1 1 1v1c1-1 1-1 2-1l-1-1 1-3c0-1 1-1 2-2h0z" class="G"></path><path d="M557 504v4h1c0 4-1 8-2 11-1 1-2 1-3 1l1-1c0-1-1-1-1-1v-3h1c1-3 2-8 3-11z" class="Q"></path><path d="M552 525c0-1 1-1 2-1 5-3 10-3 15-3h16c-1 1 0 1-1 1s0 0-1 1h2 0-3v1l-2 1c-8-1-17-2-24 0h-1-3z" class="M"></path><path d="M555 489l1-1c0 2 0 5 1 7v9c-1 3-2 8-3 11h-1 0l-2 4 1 1c0 1-1 1-2 1l-2 1c0 1-1 1-2 1v-2c2-3 4-7 5-10l3-7c1-5 1-10 1-15z" class="B"></path><path d="M554 504c0 1 0 3-1 5h0l-4 11-1 2c0 1-1 1-2 1v-2c2-3 4-7 5-10l3-7z" class="O"></path><path d="M555 525h1c7-2 16-1 24 0h2c2 0 3 1 4 1l1 2-1 1c-4-1-7-2-11-2h-12-6v5l-1 1h-3v-5c-2 0-4 1-7 1 1-1 3-2 4-2 2 0 4-1 5-2z" class="S"></path><path d="M557 495l1-2h0c1 4 2 8 2 12l1 1v-2c0-2 1-4 2-6 1 1 1 2 1 4h0l1-1c0 1 1 2 2 3h1c1 0 1 0 1 1-1 0-1 1-2 1l-2 7-1 6h1v1h-4l-1-1h-1c1-4 2-7 2-10h0c-1 3-2 6-3 8h0c0-4 1-7 1-11l-1 2h-1v-4-9z" class="L"></path><path d="M564 502l1-1c0 1 1 2 2 3h1c1 0 1 0 1 1-1 0-1 1-2 1l-2 7-1 6h1v1h-4l-1-1c2-5 3-11 4-17z" class="F"></path><path d="M547 487l2 1h-1c0 2 1 3 1 4 0 3 0 6 1 10v-8h2c0 2-1 6-1 8 0 1 1 2 1 3 0 2-1 3-1 6-1 3-3 7-5 10l-2 1h-1c0-1 0-2-1-2v-1c0-1 0-1 1-2v2h1c0-1 0-1 1-2h0c-1-1-1-2-2-2s-1 0-2-1l1-1c0 1 1 1 1 2l1-1c1-4 2-8 3-13 0-3-1-8 0-11 1-1 1-2 0-3z" class="E"></path><path d="M545 465c1 0 1 1 2 1v1c1 1 2 3 2 4h0c3 5 4 7 5 13 0 1 0 4 1 5 0 5 0 10-1 15l-3 7c0-3 1-4 1-6 0-1-1-2-1-3 0-2 1-6 1-8h-2v8c-1-4-1-7-1-10 0-1-1-2-1-4h1l-2-1c-1-2-1-4-1-6v-4h0c0-2 0-3-1-5 0-1 1-2 1-3s0-1-1-2v-2z" class="I"></path><path d="M550 494c0-2 1-7 1-9 1 2 1 6 1 9h-2zm-4-17h2c1 2 0 4 0 6h-1c0-1 0-1-1-2v-4z" class="R"></path><path d="M551 485v-1-4c0-3-1-5-3-8l1-1c3 5 4 7 5 13 0 1 0 4 1 5 0 5 0 10-1 15l-3 7c0-3 1-4 1-6 0-1-1-2-1-3 0-2 1-6 1-8 0-3 0-7-1-9z" class="L"></path><path d="M572 502c3 0 5-1 8-1 1 0 1 1 2 0h1c2 1 6 0 9 0v2 1h-1-3v2l-1 1h0l-2 10-1 1-20 1 1-6 2-7c1 0 1-1 2-1 0-1 0-1-1-1h-1v-2h5z" class="U"></path><path d="M569 505c1 0 2 1 3 1 1 1 3-1 5 1 1 0 1-1 2 0-1 1-2 2-3 2h-1-1-1l-3 3-1-1h1l-1-1c-2 1-1 2-4 3l2-7c1 0 1-1 2-1z" class="R"></path><path d="M577 510l2-1 1 1 2-1c2-1 4-2 5-2l-2 10c-1 0-1 0-1-1-1 0-2-1-2-1l-2 2h0c-2-2-3-5-3-7z" class="L"></path><path d="M572 502c3 0 5-1 8-1 1 0 1 1 2 0h1c2 1 6 0 9 0v2 1h-1-3v2l-1 1h0c-1 0-3 1-5 2l-2 1-1-1-2 1-1-1c1 0 2-1 3-2-1-1-1 0-2 0-2-2-4 0-5-1-1 0-2-1-3-1 0-1 0-1-1-1h-1v-2h5z" class="J"></path><path d="M568 504c1-1 3-1 4-1 4 0 9 0 13 1h0-5l-1 1 1 1s0 1-1 1c-1-1-1 0-2 0-2-2-4 0-5-1-1 0-2-1-3-1 0-1 0-1-1-1z" class="E"></path><path d="M585 504c3 0 5-1 7 0h-1-3v2l-1 1h0c-1 0-3 1-5 2l-2 1-1-1-2 1-1-1c1 0 2-1 3-2 1 0 1-1 1-1l-1-1 1-1h5 0z" class="C"></path><defs><linearGradient id="AR" x1="532.052" y1="486.546" x2="536.542" y2="485.941" xlink:href="#B"><stop offset="0" stop-color="#322f34"></stop><stop offset="1" stop-color="#3b3c38"></stop></linearGradient></defs><path fill="url(#AR)" d="M541 448c0-1 0-2 1-2 0-2 0-3 2-4 1 2 1 7 0 10h0 0c0 1 1 2 1 3s0 1 1 2v2l2 1v1l-1 1h2l1-1h2c1 1-1 3 1 3v1l-6-2-1-1c-1 1-1 2-1 3v2c1 1 1 1 1 2s-1 2-1 3c1 2 1 3 1 5l-1-1h-1l-1-1c0 3 2 3 2 5s-1 4-1 6c0 1 0 4-1 4h-1c1 1 0 1 1 1v1 2 1 1l-1 5-1 1 1 1h0l-1 1h0l-2-1v1s0 1 1 1l1 1c0 1 0 1-1 2v-2h-1 0v2l1 1c-1 1-1 3-2 4-1 0-1 0-2 1l-1-1v-10-1c-1 1-1 3-1 4 0 4 1 8 0 12-1-3 0-6 0-9l-1-15 1-21c0-5-1-9 0-14h0c-1-2-1-3-2-4l3-3 1 1c0 1 0 1 1 1v1c1-1 1-1 2-1l-1-1 1-3c0-1 1-1 2-2h0z"></path><path d="M532 455l3-3 1 1v4h2l-1 1c-1 0-1 0-1 1-1 1-1 3-1 4 1 0 1 0 2 1h-1c-1 1-1 2-1 3-2-2-1-5-1-8h0c-1-2-1-3-2-4z" class="M"></path><path d="M541 448c0-1 0-2 1-2 0-2 0-3 2-4 1 2 1 7 0 10h0 0c0 1 1 2 1 3s0 1 1 2v2l2 1v1l-1 1h2l1-1h2c1 1-1 3 1 3v1l-6-2-1-1c-1 1-1 2-1 3v2c1 1 1 1 1 2s-1 2-1 3c1 2 1 3 1 5l-1-1v-2c-1-1-1-1-1-2l1-1v-1c-1-2-2-4-1-6v-1c-2-1-3 0-5 0h0v-1-4c-1 0-1 1-1 2h-1v-2l1-1h-2v-4c0 1 0 1 1 1v1c1-1 1-1 2-1l-1-1 1-3c0-1 1-1 2-2h0z" class="D"></path><path d="M536 453c0 1 0 1 1 1v1c1-1 1-1 2-1l-1-1 1-3c0-1 1-1 2-2l1 1c0 1 1 2 1 3v1h0l-1 2h1l1 1c-2 1-4 1-6 1h-2v-4z" class="R"></path><path d="M541 502c-1-1-2-1-3-1-2 0-1 0-2-1v-7-2c0-4-1-13 1-17h1c1 0 1 1 2 1l-3 1h0c2 0 4 1 5 2v1h1v2h-1l-1-1h-1l1-1-1-1h-1c-1 2-1 2 0 4 0 0 1 1 1 2v1l4 1c0 1 0 4-1 4h-1c1 1 0 1 1 1v1 2 1 1l-1 5-1 1z" class="J"></path><path d="M543 496h0c-2 1-2 1-4 1l-1-3c0-1 0-2 1-3l-1-2h0 2l1 2h-1l2 2h-2c0 1 1 2 2 2h1v1z" class="U"></path><path d="M540 485l4 1c0 1 0 4-1 4h-1c1 1 0 1 1 1v1 2 1h-1c-1 0-2-1-2-2h2l-2-2h1l-1-2 1-1-1-3z" class="g"></path><path d="M524 445c2 1 2 1 4 1v-1h-3l1-1c2 0 3 0 5 1h0l1 1 1 2c1 0 2 0 4 1-2 2-4 3-5 4v2c1 1 1 2 2 4h0c-1 5 0 9 0 14l-1 21 1 15c0 3-1 6 0 9v22c3-1-1-17 2-19h0v5c1 1 1 2 1 3l-1 1c0 1 0 3 1 5v-1l1 1v2h-1c0 2-1 4 1 6h-1v1 1c-1 2-1 2-3 3-1 0-1-1-1-2h-2-2-3v2l1 2v3 1h-2-1v2l-1-2c-1-1-2-1-4-1l1-4h3l1-1c-1-2-1-3-1-4v-1h-2v-20c-1-2 0-4-2-5l1-1-2-1c1-2 1-4 1-6v-6c0-8-1-17 0-25 1-1 1-2 2-3h0c-1-3-1-6-1-9v-17h0v-2l4-3z" class="D"></path><path d="M526 517c2 4 2 6 2 11v7c0 2-1 6 0 7h-2 0v-25z" class="W"></path><path d="M524 445c2 1 2 1 4 1v-1h-3l1-1c2 0 3 0 5 1h0l1 1 1 2h-2c-2 0-4 0-5 1l-1 1h0v-2h0c-1 2-1 6-1 9v16c0 2 0 5-1 7 0-1-1-3-2-4h0c-1-3-1-6-1-9v-17h0v-2l4-3z" class="v"></path><path d="M521 476c1 1 2 3 2 4v2h1v18c0 6-1 12-2 18v15 8c2-4-1-21 2-23 1 2 0 20 0 24l-1 1h-2v-20c-1-2 0-4-2-5l1-1-2-1c1-2 1-4 1-6v-6c0-8-1-17 0-25 1-1 1-2 2-3z" class="Q"></path><path d="M521 476c1 1 2 3 2 4v2c-1-1-2-2-4-3 1-1 1-2 2-3z" class="F"></path><path d="M519 510v-6c3 5 2 13 2 19-1-2 0-4-2-5l1-1-2-1c1-2 1-4 1-6z" class="E"></path><defs><linearGradient id="AS" x1="520.453" y1="516.387" x2="538.143" y2="521.509" xlink:href="#B"><stop offset="0" stop-color="#9d9b9a"></stop><stop offset="1" stop-color="#b8b7b5"></stop></linearGradient></defs><path fill="url(#AS)" d="M526 490v3l2 2 5 1v-2l1 15c0 3-1 6 0 9v22l-1 2c-1 1-3 1-5 0-1-1 0-5 0-7v-7c0-5 0-7-2-11v-27z"></path><path d="M533 448c1 0 2 0 4 1-2 2-4 3-5 4v2c1 1 1 2 2 4h0c-1 5 0 9 0 14l-1 21v2l-5-1-2-2v-3-24l-1-16h0l1-1c1-1 3-1 5-1h2z" class="H"></path><path d="M525 450l1-1c1-1 3-1 5-1-1 3-2 4-1 7v3h1 0 1l-2 2c-1-1-1-2-2-2h-1v4l-1 1v3l-1-16h0z" class="W"></path><path d="M525 450l1-1c1-1 3-1 5-1-1 3-2 4-1 7h-1-2v-4l-2-1z" class="c"></path><path d="M261 126l-20-3c-4 0-10 0-13-2h73 112 338c2 0 6-1 8 1 1 4 1 9 1 14v24 100l-1 81v24c0 6 1 12 0 17l-6-31c-3-17-9-32-14-48l-12-23-5-8 6-6h-1l-1 1h0c1-2 1-2 1-3s4-7 5-8c2-4 4-7 5-11 4-10 6-22 4-32 0-8-3-14-6-20-1 0-1 1-2 2h-1c0-1-1-1-1-2s0-2-1-3c-1-3-3-5-5-7h0l-4-4c-2-2-3-4-5-5-1 0-1-1-2-1-1-1-2-2-3-2 0 0 0-1-1-1l1-1 2 2c1-1 2-1 2-1 0-1-1-2-2-2h0l-1-1 1-1 4 3h1 0c1-1 1-2 1-3v-4l-3-2-2 1-1 2h-1-1v1l-3-2 1-1c-1-1-2-2-3-2-1-1-3-1-4-2s-2-1-3-2l2-2c1-1 2-1 2-2h0c-1 0-1 1-2 1-4 0-8-1-13-2-1-1-4-1-6-2l-10-1c-2 0-4 0-5-1l-1-4-2 2c-1-1-2-1-3-2v-1l-1 1v1h-2c-3 1-4 1-7 0 0 0-1 2-2 2h-4c-3 1-6 2-8 2s-4 0-6 1l-1 1h-1c-14 3-26 9-39 15v-1l1-1c-1-4-3-7-5-9l-3-6v-2l-1-1h0v2c1 2 2 3 3 5 0 1 0 1-1 1l-3 1c-5 2-9 5-13 8-5 4-10 11-14 16l-13 15-1 1c-2 1-3 1-4 3-1-1-1-1-2-1-2 1-4 2-6 2h-2-2-1l-1 2h0v1c1 0 1 1 2 1-3 1-7 1-10 1-1-1-1-1-1-2h2l1-1c-2-1-3-1-5-1h0c-1-1-2 0-3 0l-1-16c0-1 0-3-1-4l-1 1-1-2c0-2 0-2-2-4 1 0 2 0 2-1h1 1 2v-1c-2-4-5-6-8-9-4-3-8-5-12-6-5-2-9-3-14-4-1 1-2 1-3 2h-5c-3-1-10-1-13 0s-10 2-13 1c-4 0-6-3-10-4-7-3-14-4-22-6h-2l-7-2-12-2-26-3h-5-1c-5-1-11-2-16-2-3 0-5 0-8-1-5 0-11-1-17-1l-21-2h1l2-1h1c2 0 2 0 4-1l-29-3-11-1z" class="k"></path><path d="M530 153c4-2 8-3 12-4l-2 3-6 2-4-1z" class="t"></path><path d="M413 138l1 1c1-1 2-1 4-1h0c1 1 2 1 3 1 1 1 1 2 1 3v1 1c-2 0-7-2-9-2h0 1 4c-1-1-1-1-1-2v-1h-4v-1z" class="p"></path><path d="M526 137h2c1-1 1-2 2-2v-1l1 1-1 2h1v5c2 1 5 0 6 0-4 1-7 0-11 0v-2-3z" class="AA"></path><path d="M500 162c1 0 2 0 4 1 1 0 1 0 3-1l2 2 2 2c-1 1-2 3-3 5-2-4-5-6-8-9z" class="H"></path><path d="M426 136h0v-2l1-1v-3l2-1-1 1v3l2 1c1 0 1 1 1 2l1 1c-1 1-2 2-5 2h-1l-1-1h1v-2z" class="x"></path><path d="M742 146h0 2v1l1 2 1 1-1 1h0l1 1 1 1c0 1 0 1-1 2 0 1 1 2 2 3v3h-1l-2-1c0-1 0-2 1-3 0-2-2-3-3-4l-1-1c-1-3-1-3 0-6z" class="p"></path><path d="M738 142c0 1 1 1 1 2 1 1 2 1 2 1l1 1h0c-1 3-1 3 0 6l1 1c1 1 3 2 3 4-1 1-1 2-1 3l2 1v3c-2-3-4-7-6-9v-2c-1-3-1-5-3-7v-4z" class="H"></path><path d="M521 157l9-4 4 1-8 4-6 4 1-5z" class="P"></path><path d="M531 137h6l2 1 2-1c0 1 0 2 1 3l-1 1h1c-2 2-3 1-5 1-1 0-4 1-6 0v-5z" class="AJ"></path><path d="M505 157c-7-5-14-8-22-10 2-1 5 1 7 1 3 0 6-1 9-1 1 1 1 2 1 3h0c1 1 2 1 3 2 1 0 5 4 6 6h-1l-2-2-1 1z" class="AG"></path><path d="M345 134l24 3-1 1h-1c-1-1-3-1-5-1l-1 1h-5-1c-5-1-11-2-16-2 2-2 4-1 6-2z" class="E"></path><path d="M565 139v2c-3 2-5 4-8 5-1 1-2 3-3 4-5 0-10 1-14 2l2-3c3 0 7 0 10-1 1-1 3-4 5-5s5-3 8-4z" class="m"></path><path d="M369 137l30 4v2l-12-2-26-3 1-1c2 0 4 0 5 1h1l1-1z" class="Q"></path><path d="M737 267v1c0 1 0 1 1 2v1l-1 1c-2 0-2 0-4 1-1 0-1 1-2 2l-2 1-1 1 1 1c2 1 3 0 4 2 0 1-1 1-1 2l-1 1h-1l2 4 1 1v-2h0 0l3 6v1l-1-1 3 8c0 1 1 2 1 3l-12-23v-3-2s1 0 2-1c-1-1-1-1-1-2l2-2c0-1 0 0 1-1 0 2 0 2 1 3 1-1 0-2 1-4v1l2-1v2c1-1 1-1 2-3z" class="N"></path><path d="M741 194c1 1 2 4 4 5 2 0 2 1 3 2l1-1h1v4c0 2 0 6-1 8l-1 1c-1-1-2-3-2-5-1-2-2-4-2-6s-1-3-1-4h-1c0 2 0 3 1 5s1 4 2 6v3c1 3 1 12 0 15 0-6 0-11-1-16-1-6-3-10-5-15l2-2z" class="K"></path><path d="M749 200h1v4c0 2 0 6-1 8-2-1-3-7-4-9l3-2 1-1z" class="B"></path><path d="M301 130l44 4c-2 1-4 0-6 2-3 0-5 0-8-1-5 0-11-1-17-1l-21-2h1l2-1h1c2 0 2 0 4-1z" class="C"></path><path d="M423 130c1 0 1 0 2-1v-1l1 1h3l-2 1v3l-1 1v2h0c-1-1-1-1-3-1-1 1 0 2-2 4-1 0-2 0-3-1h0c-2 0-3 0-4 1l-1-1-1-3c0-1-1-1-1-1h-1-1v2h-1c0-2-1-5 0-7 2 0 3 1 5 0h4 5l1 1z" class="AA"></path><path d="M417 129h5l1 1c-1 1-1 2-1 4h0c-2 0-3 0-4-1h-1v-4h0z" class="x"></path><path d="M499 147h16 11c-3 2-7 5-12 6-2 1-3 3-4 5v2c-2 0-3-2-5-3l1-1 2 2h1c-1-2-5-6-6-6-1-1-2-1-3-2h0c0-1 0-2-1-3z" class="l"></path><path d="M499 147h16v2h-1v-1h0c-1 0-1 0-1 1-1 0-1 0-1-1-1 1-1 1-1 2h-11 0c0-1 0-2-1-3z" class="AJ"></path><path d="M399 141c8 1 15 3 23 5 6 1 11 5 17 5 3 1 6 0 10 0 8 0 17-1 25 1-1 1-2 1-3 2h-5c-3-1-10-1-13 0s-10 2-13 1c-4 0-6-3-10-4-7-3-14-4-22-6h-2l-7-2v-2z" class="t"></path><path d="M743 239c0 3 0 5-2 8l-1 2 1 1 1-1v-2c0-1 1-2 1-2 0 2-1 5-2 8 2 1 3 1 5 2l-1 8v1 15c0 2 0 6-1 7-1-2 1-6-1-8v-15h0c0 1 0 2-1 4v10h-2-1l2-2c0-1 0-2-1-3v-10c-1 0-2-1-2-1l-1 1c-1 1-1 1-2 1l-1 3 1 1h2c-1 2-1 2-2 3v-2l-2 1v-1c-1 2 0 3-1 4-1-1-1-1-1-3-1 1-1 0-1 1l-2 2c0 1 0 1 1 2-1 1-2 1-2 1v2 3l-5-8 6-6c7-6 12-18 15-27z" class="T"></path><path d="M666 138c19 0 39 1 58-1 4 0 9-2 13-3l1 8v4c0 8 3 15 4 22 1 2 1 5 2 7v1c-1 0-2 1-3 1l1-3c0-3-1-6-2-8l-3-13v-2c0-2-2-4-4-6l-3-1v-4l-1 3-1-1v-1c-1 0-1 1-2 1h-1 0l-2-1v3c-1-1-2-1-2-2-2 1-3 1-3 1h-2c-1 0-1-1-2-1-1-1-2-1-3-2h-1c-2 0-4 0-5 1h-4c-1 0-4 1-5 0-1 0-1-1-2-1h-4-15c-3 0-7-1-10-1-2 0-3 0-4-1h5z" class="G"></path><path d="M711 140h0c4 0 9-1 13 0l1 2h0l-2-1v3c-1-1-2-1-2-2-2 1-3 1-3 1h-2c-1 0-1-1-2-1-1-1-2-1-3-2z" class="D"></path><path d="M730 140l-1-1 6-2v6c0 1-1 1-2 2l-3-1v-4z" class="B"></path><path d="M635 137h20c4 1 8 0 11 1h-5c1 1 2 1 4 1 3 0 7 1 10 1h15 4c1 0 1 1 2 1h-12l-18-1v2l-2 2c-1-1-2-1-3-2v-1l-1 1v1h-2c-3 1-4 1-7 0 0 0-1 2-2 2h-4c-3 1-6 2-8 2s-4 0-6 1l-2-2-1-3-1-2-3-3h3c2-1 6 1 8-1z" class="m"></path><path d="M648 140h14 4v2l-2 2c-1-1-2-1-3-2v-1l-1 1v1h-2c-3 1-4 1-7 0-1-1-1-1-2-1 0-1 0-1-1-2z" class="C"></path><defs><linearGradient id="AT" x1="634.826" y1="136.842" x2="638.359" y2="145.918" xlink:href="#B"><stop offset="0" stop-color="#0d0b0b"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#AT)" d="M627 138c7 2 14 0 21 2 1 1 1 1 1 2 1 0 1 0 2 1 0 0-1 2-2 2h-4c-3 1-6 2-8 2s-4 0-6 1l-2-2-1-3-1-2-3-3h3z"></path><path d="M738 146c2 2 2 4 3 7v2c2 2 4 6 6 9 0 4 1 7 1 11 2 7 2 13 3 20 0 3 0 6-1 9v-4h-1l-1 1c-1-1-1-2-3-2-2-1-3-4-4-5-1-4-2-7-4-10h0c0-2 1-3 2-4l1-1s1-1 1-2c1 0 2-1 3-1v-1c-1-2-1-5-2-7-1-7-4-14-4-22z" class="X"></path><path d="M741 155c2 2 4 6 6 9 0 4 1 7 1 11-1-1-1-3-2-5-2-5-3-10-5-15z" class="a"></path><path d="M744 175c1 1 1 2 2 4 1 5 2 12 2 17 0 2 1 3 1 4l-1 1c-1-1-1-2-3-2 2-4 1-14 0-18l-1-5v-1z" class="N"></path><path d="M744 176l1 5c1 4 2 14 0 18-2-1-3-4-4-5-1-4-2-7-4-10h0c0-2 1-3 2-4l1-1s1-1 1-2c1 0 2-1 3-1z" class="G"></path><path d="M741 188l3-3c0 2 0 5-1 6-1 0-1-1-2-3z" class="V"></path><path d="M744 176l1 5-1 4-3 3v-1c-1-2 0-6-1-8 0 0 1-1 1-2 1 0 2-1 3-1z" class="O"></path><path d="M565 139c14-6 30-7 45-5l25 3c-2 2-6 0-8 1h-3l3 3c-9-1-18-2-27-1l-1 2-2-1-1 1-2-1-1 1c-2 1-3 2-4 3-2 0-4 1-6 2v-2l-1-1h0l-1-4v-1c-1-1-1-1-3-2-2 0-5 1-7 2l-6 2v-2z" class="E"></path><path d="M578 137c12-1 26-4 38 1-3 0-7-1-9-1-4 0-8 1-11 1-4 0-10 0-13 3-1 1-1 2-1 3h0l-1-4v-1c-1-1-1-1-3-2z" class="V"></path><path d="M616 138c1-1 2-1 4-1l1 1h3 0l3 3c-9-1-18-2-27-1l-1 2-2-1-1 1-2-1-1 1c-2 1-3 2-4 3-2 0-4 1-6 2v-2l-1-1c0-1 0-2 1-3 3-3 9-3 13-3 3 0 7-1 11-1 2 0 6 1 9 1z" class="M"></path><path d="M583 145v-2c2-1 5-2 7-2 1 1 1 1 2 1h1c-2 1-3 2-4 3-2 0-4 1-6 2v-2z" class="Q"></path><path d="M725 142h1c1 0 1-1 2-1v1l1 1 1-3v4l3 1c2 2 4 4 4 6v2l3 13c-1 0-3 0-4 1h-1-3v1h-1l-1 1c0 1-1 2-2 3l-1 1c-1-1-2-1-2-2-1-1 0-1 0-2v-3h-6v-4c0-2-1-4-1-6l2-1h0l-1-1h-1l1-1h-1v-1l1-1c1-2 1-3 1-4l-2-2h0v-2s1 0 3-1c0 1 1 1 2 2v-3l2 1h0z" class="N"></path><path d="M734 157v-3c1 0 2-1 3-1v3c-1 0-2 0-2 1h-1zm-2 3h4c1 1 1 1 2 3h-2c-2-1-3-1-4-3z" class="x"></path><path d="M725 142h1c1 0 1-1 2-1v1l1 1 1-3v4c-3 0-3 1-5 3 2 2 7-1 8 4h0v1c0 1-1 1-2 2h0c-1 1-1 1-2 1h0c-1-1-1-1-2-1l-2-2v-1h-2c0 1 1 2-1 2-1-1-2 0-3 0h0-1v-1l1-1c1-2 1-3 1-4l-2-2h0v-2s1 0 3-1c0 1 1 1 2 2v-3l2 1h0z" class="T"></path><path d="M718 143s1 0 3-1c0 1 1 1 2 2v-3l2 1v2s-1 2-2 2c-1 2-3 4-4 7h0-1v-1l1-1c1-2 1-3 1-4l-2-2h0v-2z" class="B"></path><defs><linearGradient id="AU" x1="680.34" y1="137.964" x2="709.159" y2="147.543" xlink:href="#B"><stop offset="0" stop-color="#131216"></stop><stop offset="1" stop-color="#3a3530"></stop></linearGradient></defs><path fill="url(#AU)" d="M696 141c1 1 4 0 5 0h4c1-1 3-1 5-1h1c1 1 2 1 3 2 1 0 1 1 2 1h2v2h0l2 2c0 1 0 2-1 4l-1 1v1h1l-1 1h1l1 1h0l-2 1c0 2 1 4 1 6l-3-2-2 1-1 2h-1-1v1l-3-2 1-1c-1-1-2-2-3-2-1-1-3-1-4-2s-2-1-3-2l2-2c1-1 2-1 2-2h0c-1 0-1 1-2 1-4 0-8-1-13-2-1-1-4-1-6-2l-10-1c-2 0-4 0-5-1l-1-4v-2l18 1h12z"></path><path d="M689 149c0-1 0-2 1-3s3-1 5-1c3-1 5-1 8-1h4c1-1 2-1 3-1 2 1 1 2 3 4h-2v-3c-1 1-1 3-1 4h-1v-3c-1 0-1 0-1 1l-2-1c-1 1-1 2-1 3l-1 1c-1-2-2-3-2-4h-1c-1 1-4 3-4 5l-1-1h0c-1-1-1-2-2-2s-3 1-5 1v1z" class="Y"></path><path d="M689 149v-1c2 0 4-1 5-1s1 1 2 2h0l1 1c0-2 3-4 4-5h1c0 1 1 2 2 4 0 1 0 2 1 4v1c-1 0-2 0-4-1 1-1 2-1 2-2h0c-1 0-1 1-2 1-4 0-8-1-13-2l1-1z" class="l"></path><path d="M666 140l18 1-1 1v1h-10l8 2c1 1 1 2 1 3l-10-1c-2 0-4 0-5-1l-1-4v-2z" class="F"></path><path d="M704 149l1-1c0-1 0-2 1-3l2 1c0-1 0-1 1-1v3h1c0-1 0-3 1-4v3h2l2-2 1 1h1v-1h1 0l2 2c0 1 0 2-1 4l-1 1v1h1l-1 1h1l1 1h0l-2 1c0 2 1 4 1 6l-3-2-2 1-1 2h-1-1v1l-3-2 1-1c-1-1-2-2-3-2-1-1-3-1-4-2s-2-1-3-2l2-2c2 1 3 1 4 1v-1c-1-2-1-3-1-4z" class="N"></path><path d="M701 153c2 1 3 1 4 1v1 1h1 0l3-3c1 1 1 0 2 1l-3 3h0c1 1 1 1 3 1 2 1 2 1 3 3l-1 2h-1-1v1l-3-2 1-1c-1-1-2-2-3-2-1-1-3-1-4-2s-2-1-3-2l2-2z" class="x"></path><path d="M716 146h1v-1h1 0l2 2c0 1 0 2-1 4l-1 1v1h1l-1 1h1l1 1h0l-2 1c0 2 1 4 1 6l-3-2-2 1c-1-2-1-2-3-3 0-1 0-1 1-2v-2h0c1-1 1-2 1-3h2c0-2 0-3 1-5z" class="T"></path><defs><linearGradient id="AV" x1="598.252" y1="137.305" x2="612.231" y2="158.21" xlink:href="#B"><stop offset="0" stop-color="#59524d"></stop><stop offset="1" stop-color="#9e9a93"></stop></linearGradient></defs><path fill="url(#AV)" d="M596 142l1-1 2 1 1-2c9-1 18 0 27 1l1 2 1 3 2 2-1 1h-1c-14 3-26 9-39 15v-1l1-1c-1-4-3-7-5-9l-3-6c2-1 4-2 6-2 1-1 2-2 4-3l1-1 2 1z"></path><path d="M629 146l2 2-1 1h-1l-1-2 1-1z" class="S"></path><path d="M596 142l1-1 2 1 1-2c9-1 18 0 27 1l1 2c-6-1-12-1-18-1-2 0-4 1-6 1h-1c0 1 0 2 1 4h0 0c-1-1-2-2-3-4h-1v2h0l-1-2h-1c-1-1-1-1-2-1z" class="Q"></path><defs><linearGradient id="AW" x1="743.735" y1="232.941" x2="724.181" y2="226.749" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#625b51"></stop></linearGradient></defs><path fill="url(#AW)" d="M718 169c1-1 1-2 1-3h6v3c0 1-1 1 0 2 0 1 1 1 2 2l1-1c1-1 2-2 2-3l1-1h1v-1h3 1c1-1 3-1 4-1 1 2 2 5 2 8l-1 3c0 1-1 2-1 2l-1 1c-1 1-2 2-2 4h0c2 3 3 6 4 10l-2 2c2 5 4 9 5 15 1 5 1 10 1 16l-2 12c-3 9-8 21-15 27h-1l-1 1h0c1-2 1-2 1-3s4-7 5-8c2-4 4-7 5-11 4-10 6-22 4-32 0-8-3-14-6-20-1 0-1 1-2 2h-1c0-1-1-1-1-2s0-2-1-3c-1-3-3-5-5-7h0l-4-4c-2-2-3-4-5-5-1 0-1-1-2-1-1-1-2-2-3-2 0 0 0-1-1-1l1-1 2 2c1-1 2-1 2-1 0-1-1-2-2-2h0l-1-1 1-1 4 3h1 0z"></path><path d="M725 183c2 1 4 3 6 5 1 1 3 3 4 5-1 0-1 1-2 2h-1c0-1-1-1-1-2s0-2-1-3c-1-3-3-5-5-7z" class="T"></path><path d="M732 181h1l1 1c1 0 2 1 3 2h0c2 3 3 6 4 10l-2 2-1-2c-2-4-5-8-8-11 1-1 2-1 2-2z" class="b"></path><path d="M734 182c1 0 2 1 3 2h0l-1 1c-1 0-1 0-2-1s-1-1 0-2zm-16-13c1-1 1-2 1-3h6v3c0 1-1 1 0 2 0 1 1 1 2 2l1-1-1 2h1 2c1 1 1 2 1 3 1 1 1 1 2 1v1l-1 1v1c0 1-1 1-2 2-2-3-5-5-7-8-2-2-3-4-5-6z" class="x"></path><path d="M740 166c1 2 2 5 2 8l-1 3c0 1-1 2-1 2l-1 1c-1 1-2 2-2 4-1-1-2-2-3-2l-1-1h-1v-1l1-1v-1c-1 0-1 0-2-1 0-1 0-2-1-3h-2-1l1-2c1-1 2-2 2-3l1-1h1v-1h3 1c1-1 3-1 4-1z" class="T"></path><path d="M733 179c1 0 2-1 3-1l1 1h1 0c1 1 0 1 1 1-1 1-2 2-2 4-1-1-2-2-3-2l-1-1h-1v-1l1-1z" class="N"></path><path d="M740 166c1 2 2 5 2 8-1-1-1-1-2-1l-2 2h0v-2l-2 1v-2c1-2 0-4 0-5 1-1 3-1 4-1z" class="S"></path><path d="M728 172c1-1 2-2 2-3l1-1h1v-1h3 1c0 1 1 3 0 5v2c-2 1-2 1-3 3v1c-1 0-1 0-2-1 0-1 0-2-1-3h-2-1l1-2z" class="c"></path><path d="M471 143c-1 0-3 0-4-1h-2l-1 1c-2 0-5 0-7-1v-2-1h-2v-1l-1 2c0 1 0 2-1 2l-1 1c0-1-1-1-1-2-1-1-1-2-2-3-1 0-2 0-3 1 0 2 0 2 1 3l-1 1h-1c-1 0-1 1-1 1-1 0-4 0-5-1v-3c0-3 2-1 4-3-1-1-1-1-1-4l-1 1v-1l-1 1-1-1c0-1 0-3 1-4 3-1 8 0 12 0 1-1 2-1 3-1 7 2 14 0 21 1l1 1c2 0 3-1 4-1h1c1 0 2 0 3 1h1l2-1h1c2 0 6-1 8-1 1 1 2 1 4 1 0-1 1 0 1 0v3l1-1h1v2c0 1 0 1 1 2 0-3-1-4 1-6h1c1 0 2-1 3-1l5 1c2-1 3-1 5 0h6 2c1-1 2-1 2-1 1 0 1 1 2 1s2 0 3-1l1 2h1v-1l1-1c2 0 5 1 7 0 2 2 1 4 1 6 1 0 1 0 2-1v2c-2 0-3 0-4 1 0 1 0 0 1 1h2v1l2-1-1 1c-2 1-4 2-6 2-1-1-1-2-1-3l-2 1-2-1h-6-1l1-2-1-1v1c-1 0-1 1-2 2h-2v3 2c-2 0-5 1-7 0-1 1-1 1-2 1-3 0-6-1-9 0-1 0-2 0-3-1v-2h-3v1 1l-1 1h0c-2-1-2 0-4 0h0-3-1-1v-1l-2-2c-1 0-1 0-1 1l-1 2h-6c0 1 0 1-1 2l-1-1 1-1c-2-1-4-1-6-1-1 0-1 0-3 1h-1z" class="x"></path><path d="M468 134c1 1 2 0 3 1 1 0 1 0 1 1v1h-2-1c-1 0-3 0-4-1 1-1 2 0 3-2z" class="k"></path><path d="M515 129c2-1 3-1 5 0-1 1-1 1-1 3-2 1-2 1-4 0v-3z" class="AA"></path><path d="M477 130c2 0 3-1 4-1 0 2 1 4 0 6l-1-1h-2-1v-4z" class="p"></path><path d="M532 129c1 0 2 0 3-1l1 2h1v-1l1-1c0 2 0 2-1 3h0c0 1-1 3-1 3l-1 1c-1-1-3-1-3-1v-5z" class="AA"></path><path d="M471 143c-1-1-2-1-3-2 0-1 0-2 1-3 1 0 2-1 3 0h4c-1 1 0 2-1 4-1 0-1 0-3 1h-1z" class="AR"></path><path d="M494 143c-1-2-1-2-1-4v-1c1-1 1-1 1-2h-1c0-2 0-1 1-2h0 1l1 1h0c1 0 1 0 2 1v1 2c0 1 0 3-1 4h-3zm8-3v-2h1c1 0 1-1 1-2 1 0 2-1 2-2l1-1 2-2c0 2 0 3 1 5h-2v7c-1 0-2 0-3-1v-2h-3z" class="AA"></path><path d="M476 138c1 0 2 0 4-1 1 0 1 0 3 1 0 2 0 3-1 5 0 1 0 1-1 2l-1-1 1-1c-2-1-4-1-6-1 1-2 0-3 1-4z" class="AT"></path><path d="M508 136c2 2 3 3 5 4 1-1 1-1 1-2l2-1h0c1 0 1 0 2-1 1 1 1 1 2 1h2 2 2 0v3 2c-2 0-5 1-7 0-1 1-1 1-2 1-3 0-6-1-9 0v-7z" class="AR"></path><defs><linearGradient id="AX" x1="531.48" y1="157.246" x2="545.846" y2="177.875" xlink:href="#B"><stop offset="0" stop-color="#2c2926"></stop><stop offset="1" stop-color="#5a524e"></stop></linearGradient></defs><path fill="url(#AX)" d="M565 141l6-2c2-1 5-2 7-2 2 1 2 1 3 2v1l1 4v2c1 2 2 3 3 5 0 1 0 1-1 1l-3 1c-5 2-9 5-13 8-5 4-10 11-14 16l-13 15-1 1c-2 1-3 1-4 3-1-1-1-1-2-1-2 1-4 2-6 2h-2-2-1l-1 2h0v1c1 0 1 1 2 1-3 1-7 1-10 1-1-1-1-1-1-2h2l1-1c-2-1-3-1-5-1h0c-1-1-2 0-3 0l-1-16c0-1 0-3-1-4l-1 1-1-2c0-2 0-2-2-4 1 0 2 0 2-1h1 1 2v-1c1-2 2-4 3-5 3-4 6-6 10-9l-1 5 6-4 8-4 6-2c4-1 9-2 14-2 1-1 2-3 3-4 3-1 5-3 8-5z"></path><path d="M516 169c1 1 2 1 3 2l-3 6-1 1c0-1-1-1-1-2l1-1c0-1-1-2-1-4l2-2z" class="m"></path><path d="M520 162l6-4c1 0 2 1 3 1-3 3-6 4-8 6l1 2-1 1c-1 0-1 0-2-1v2h-1v-1c-1 1-1 0-2 1l-2 2c-1 2-1 3-2 4 0 1 0 1-1 1h0v-1c0-5 5-9 9-13z" class="h"></path><path d="M565 141l6-2c2-1 5-2 7-2 2 1 2 1 3 2v1c-3-1-10 1-13 2-3 2-7 4-10 7-1 0-2 0-3 1h-1c1-1 2-3 3-4 3-1 5-3 8-5z" class="D"></path><path d="M554 150h1c1-1 2-1 3-1-3 4-6 6-11 9h0l1-1 1-1v-1h-7-1c-2-1-10 3-12 4-1 0-2-1-3-1l8-4 6-2c4-1 9-2 14-2z" class="F"></path><path d="M511 166c3-4 6-6 10-9l-1 5c-4 4-9 8-9 13v1h0c1 0 1 0 1-1 1-1 1-2 2-4 0 2 1 3 1 4l-1 1c0 1 1 1 1 2-1 1-2 0-3 0 0 1 0 1 1 2-1 2-2 2-3 4h-1l-1-1-1-1c0-1 0-3-1-4l-1 1-1-2c0-2 0-2-2-4 1 0 2 0 2-1h1 1 2v-1c1-2 2-4 3-5z" class="y"></path><path d="M508 183c1-2 1-4 3-6h2 1l-1-1h1c0 1 1 1 1 2-1 1-2 0-3 0 0 1 0 1 1 2-1 2-2 2-3 4h-1l-1-1z" class="P"></path><path d="M525 192c1 0 0 0 2-1h0c1 0 2-1 2-2 3-1 6-3 8-5s5-5 7-8c2-1 3-2 5-3 2-2 3-4 5-6 2-3 6-6 8-10h0c1-1 2-1 3-1l-3 3-27 32c0 1 0 3-1 4-2 1-4 2-6 2h-2-2 0v-1c-1-1-1-1-1-2l2-1v-1z" class="m"></path><path d="M524 197c4 0 8-4 11-6 0 1 0 3-1 4-2 1-4 2-6 2h-2-2 0z" class="l"></path><path d="M507 182l1 1 1 1h1l-1 1c1 1 1 2 3 3h4c1 1 0 2 0 4l1-1c2 0 6-2 7-3l1-1 1 1h0l-2 3s-1 0-2 1l-1 1c0 1 1 1 1 1h1c0-1 1-2 2-2v1l-2 1c0 1 0 1 1 2v1h0-1l-1 2h0v1c1 0 1 1 2 1-3 1-7 1-10 1-1-1-1-1-1-2h2l1-1c-2-1-3-1-5-1h0c-1-1-2 0-3 0l-1-16z" class="t"></path><path d="M511 197h12l-1 2h0v1c1 0 1 1 2 1-3 1-7 1-10 1-1-1-1-1-1-2h2l1-1c-2-1-3-1-5-1h0v-1z" class="AI"></path><path d="M507 182l1 1 1 1h1l-1 1c1 1 1 2 3 3h4c1 1 0 2 0 4-1 0-2 0-3 1h1v2h1l1 1c-2 0-4 0-5 1v1c-1-1-2 0-3 0l-1-16z" class="AL"></path><defs><linearGradient id="AY" x1="550.809" y1="163.319" x2="559.691" y2="183.181" xlink:href="#B"><stop offset="0" stop-color="#978f81"></stop><stop offset="1" stop-color="#aea8a2"></stop></linearGradient></defs><path fill="url(#AY)" d="M565 156h0c1-2 1-3 1-5v-1c0-1 1-2 2-3 4-2 10-4 14-3v2c1 2 2 3 3 5 0 1 0 1-1 1l-3 1c-5 2-9 5-13 8-5 4-10 11-14 16l-13 15-1 1c-2 1-3 1-4 3-1-1-1-1-2-1 1-1 1-3 1-4l27-32 3-3z"></path><path d="M565 156h0c1-2 1-3 1-5v-1c0-1 1-2 2-3 4-2 10-4 14-3v2c1 2 2 3 3 5 0 1 0 1-1 1l-3 1h0c-2 0-2 0-4 1h0-1v-3c-2 2-4 3-6 3-3 2-5 4-8 5l3-3z" class="U"></path><path d="M575 149h-2l-1 1v1c-2 0-2-1-3-2 1-1 2-2 4-2h1c1-1 1-1 2-1 1-1 4 0 6 0 1 2 2 3 3 5 0 1 0 1-1 1l-3 1h0c-2 0-2 0-4 1h0-1v-3h0l-1-2z" class="Z"></path><path d="M575 149c1-1 2-1 4-1 1 1 2 1 3 2s2 1 2 2l-3 1h0c-2 0-2 0-4 1h0-1v-3h0l-1-2z" class="Y"></path><path d="M460 426c3 8 0 17 2 25v4c1 0 1-1 2-1v-4-2l-1-1v-1h1 1l1-1h1c-1-1 0-1-1-1h-1c1-1 1-1 1-2v-1c2 1 3 2 5 1v-3l1-1v1 1c1 0 1 1 1 1h1v-1l2-1c1 0 1 1 2 1v2c1 1 1 1 2 1h1c0 1-1 1-1 1l-1 1h2v1h5 1c1 1 0 1 0 2s1 2 2 3v1 12c0 1 0 4 1 5l-1 6v2c0 3 0 5-1 7l-1 1v3h0v2c1 1 1 9 1 12v5c0 2 0 5 1 6s2 2 2 4c-1 2-1 3-1 5 0-2 0-4 2-5l2 1v-2h1c2 0 4-1 6 0l2 1h13c0 1 0 2 1 3h1c0-1 1-2 1-3l-2-1c1-2 2-4 2-6 0 2 0 4-1 6l2 1-1 1c2 1 1 3 2 5v20h2v1c0 1 0 2 1 4l-1 1h-3l-1 4c2 0 3 0 4 1l1 2-1 12v3c-1 9 1 18 1 26 0 6-1 15 1 21l-1 1 2 2 3 3v2l-2 2c1 0 2 1 3 2v2c0 2 0 3-1 5v2h2v4h-2l-3-1 1 1h-1-1-2v1c0 3 0 5 1 8v1 2c1 0 1 1 1 2 0-2 0-2 1-3h3s1 1 2 1h0l1 1c1 1 1 1 1 2-1 2-1 4 0 5v1c0 2 0 5 1 6l-1 72c-2 0-4 1-7 0l-1 2c-1 0-1 0-2 1v5c0 1 1 2 2 2s1-1 2 0l-1 1-1 1v2c-2 0-3 1-3 2-1 3-1 7-1 11 0 1 0 3 1 5h-2v-2c-1 1-1 1-1 2l4 2-1 2c-1 1-2 2-3 2 0 1 0 1 1 2 0 1 0 2-1 3v1h1 1 1c0 2 0 4-1 6h1l2 1h6c0 1-1 2-1 3l1 1h1c-1 1-2 2-2 3l-1 1h-1v5 1 1c1 1 1 3 1 4 0 3-1 11 1 13l-1 2-1-1v1c0 1 0 0-1 2s1 5-1 7c0 1 1 2 0 4 0 1 0 0-1 2v1 1h0c-1 4-5 6-8 7-2 1-4 1-6 2h-1-1c-2 0-4 1-5 0h-2-1l-1-1c-2 0-4-1-5-1h-1c-3-1-6-3-7-6h0c-1-3-2-6-2-9v-4-33l-1-2v-3h-4-1c-2 1-3 1-5 2-1 0-1 0-1-1s0-2 1-3v-2h-1l-1-2h1v-3h-2c-1-2-1-2 0-4-1-3 1-5 0-8-1-2-1-1-1-2h1c2-3 2-6 3-9h-1-2-1 0-5-1-3l-1-1v-1c-1-6 0-14 0-20l-1-40-1-1v-6c2-3 1-9 1-13v-25-1c0-2 0-2-1-3l1-1v-5-7-1c-4 0-8-1-12 2-1 1-1 2-2 3l1-7c0-8-1-18-1-26-1-2-1-5-1-8v-17c0-2 0-4 1-7v-2-1c-3-3-1-12-1-16v-4h6v-1-9-12c-1-1-1-2-1-3h-1c1-1 3-1 4-1s3 2 4 3c0-1 0-2-1-3s-2-3-3-4v-1h0l1-1 3 2h1c-2-2-3-2-4-4 1 0 2 1 3 2h1c-2-2-2-3-3-5-2-4-4-8-5-12 0-2-1-4-1-5-1-2-1-4-1-6h1c-1-1-1-1-1-2v-4l-1 1v-3-3l1-1v-4l1-3c1-2 2-5 4-7 1-2 3-3 4-4v-1l2 2c0 1 2 1 2 1l1 2 2-1c0-2 0-3-1-5 0-3 1-5 0-8l-1-15c0-3 1-7 0-9v-4z" class="G"></path><path d="M468 477c1 0 2 0 3 1v7c-1-1-2-3-3-3l-1 1c0-2-1-3-1-4l2-2z" class="g"></path><path d="M467 483l1-1c1 0 2 2 3 3v10h-2v-3c1 0 1 0 1-1 0-2 0-5-1-6h-2v-2z" class="Y"></path><path d="M477 657v3 2 1h-1v-2-1c-1-2 1-5 0-7l-1-2c0-1 1-1 1-2 0-3-1-7 0-9v-3l-2 2h0l1-2c0-1 1-1 1 0l1 1v1c-1 2-1 7 1 9h0v1c0 1 0 2-1 3s0 4 0 5z" class="V"></path><path d="M459 610c-1-5 0-13 0-18l1-1v28h-1v-9z" class="J"></path><path d="M472 439v1c1 0 1 1 1 1h1v-1l2-1c1 0 1 1 2 1v2c1 1 1 1 2 1h1c0 1-1 1-1 1l-1 1h2v1l-3 2-3 1c1-2 1-4 1-6-1-1-1-1-2-1-2 1-1 2-1 4l-1 1v-8z" class="B"></path><path d="M464 631l1-3s0-1 1-1h1c1 1 1 2 1 4v2h0c0 1 1 2 1 3-1 1-1 1-2 1h-4v-3l1-3zm16-108c1-2 1-4 1-6 1-2 2-3 4-4l1 1v3c0 2-1 4-1 5-1 1-3 1-5 1z" class="S"></path><path d="M465 469l1-1c2 1 2 0 4 1l1 1v3 2c-1 1-2 0-2 1l1 1 1 1c-1-1-2-1-3-1h-1v-1h-2v-2h0c0-1 1-2 2-2h-2l-1-2c1 0 1 0 1-1z" class="L"></path><path d="M477 547c0-2 0-3 1-5l1 1 2 1-1 1h1l1 1c-1 2-2 5-2 8 0 0-1 1-1 2-1 4 1 22-1 25v-30c0-1 0-2 1-3l-2-1z" class="C"></path><path d="M464 514c1-1 0-1 1-1h1c2-1 2-1 4 0h1v6 5h0-6v-1c0-2-1-5 0-7l-1-2z" class="e"></path><path d="M465 516v1 3h3l3-1v5h0-6v-1c0-2-1-5 0-7z" class="J"></path><path d="M471 519v5h0l-1-1h-1c0-2 0-2-1-3l3-1z" class="Z"></path><path d="M470 692c2 0 3 0 5 1l1-1 1 1c0 2 0 5 1 7h-3c-2 1-4 1-6 1v-8l1-1z" class="B"></path><path d="M475 693l1-1 1 1c0 2 0 5 1 7h-3c0-2 0-4 1-6h1v-1h-2z" class="F"></path><path d="M472 438v1 8l-1 26v-3c0-3 1-12-1-14-1-2 0-2 0-3-1-1-2-1-3-2l-2 2v-2h1v-1h-1v-1c1 0 1 0 1-1h0l-1-1c1 0 1 0 2-1h-1v-1h1c-1-1 0-1-1-1h-1c1-1 1-1 1-2v-1c2 1 3 2 5 1v-3l1-1z" class="Q"></path><path d="M465 453l2-2c1 1 2 1 3 2 0 1-1 1 0 3 2 2 1 11 1 14l-1-1c-2-1-2 0-4-1l-1 1h0v-3l1-1c-1 0-1 0-2-1 2 0 2 0 2-1h-1v-1s1 0 2-1h-1c-1-1-1-1-1-2-1-1 0-1 0-2 1 0 1 0 2-1 0-1-1-1-2-3z" class="U"></path><path d="M465 459l1-1c1 0 2 0 3 1h0v4h0-2-1-1v-1s1 0 2-1h-1c-1-1-1-1-1-2zm10 167l-1-75 1-1c1 2 0 6 0 8v23l1 28c0 3 1 15-1 17h0z" class="J"></path><path d="M476 609l1 1v10c0 2 0 5 2 8h0 4v1h-2c-1 1-1 1-1 3l1 1h-1c2 1 3 1 5 1 0 0 0 1-1 2 0 1 1 2 1 4v2h-4l-1 1v-1c-2 3 0 5-2 7v-1c0-2-1-5 0-7l1-1c-1-2-1-6-1-9h1l-4-5h0c2-2 1-14 1-17z" class="D"></path><path d="M485 640l-4-1c-1-1-1-1 0-3 1-1 2 0 3 0 0 1 1 2 1 4z" class="T"></path><path d="M460 426c3 8 0 17 2 25v4c1 0 1-1 2-1v-4-2l-1-1v-1h1 1l1-1v1h1c-1 1-1 1-2 1l1 1h0c0 1 0 1-1 1v1h1v1h-1v2c1 2 2 2 2 3-1 1-1 1-2 1 0 1-1 1 0 2 0 1 0 1 1 2h1c-1 1-2 1-2 1v1h1c0 1 0 1-2 1 1 1 1 1 2 1l-1 1v3h0c0 1 0 1-1 1l1 2h-1c0-1 0-1-1-2l-1 1c-1-1-2-1-2-1l1-1-1-1 2-1c0-2 0-3-1-5 0-3 1-5 0-8l-1-15c0-3 1-7 0-9v-4z" class="C"></path><path d="M477 547c-1 0-1 0-2-1-1-2-1-5-1-8v-13h4c0-1 1-1 2-1v2 1 1c-1 5 0 9 0 14v1h-1l-1-1c-1 2-1 3-1 5z" class="E"></path><path d="M464 631l-1 3v3h4c1 1 1 0 1 2h0l1 1v2c1 1 1 2 2 3 0 2 0 5 1 8 0 1 0 2-1 3-1 3 0 7 0 11v13c0 2 1 6 0 8-1 1-1 2-1 2l-1 1v-1c2-4 1-7 1-11-1-1-1-2-1-3 2-3 0-9 1-13h0c0-2 1-7-1-8l-1-1c1-1 1-1 0-2 0-2 1-4 0-6 0-1 0-1 1-2l-1-1v-2h-3l-1-1-1 2c-2 0-1 0-2 1l-1-1v-3l1-1-1-1-1-4v-1l1-1c1 2 1 3 1 5v1s0 1 1 1v-1c1-3 1-3-1-5v-1h2 1z" class="D"></path><path d="M479 495h0v14 7 7c-2 1-2 1-4 1-1-2-1-5-1-7 1-6 1-12 1-18h0c0-1 1-2 2-2h1l1-2z" class="Q"></path><path d="M460 542l1 5v15 6h-1c-3 0-4 1-6 2l-1-1v-2-4c0-1 0-1 1-2 1 1 2 1 3 2h1c0-1 0-4-1-5 0-1-2-1-2-1-1 0-1 0-2 1h-1l-1-1v-1c-1-1-1-2-2-2v-1c2 0 3-1 4-3 2-3 4-5 4-8 1 1 1 0 2 1 0-1 0-1 1-1h0z" class="e"></path><path d="M453 563c0-1 0-1 1-2 1 1 2 1 3 2h1c0 2 0 3-1 5h-1-1c0-1-1-1-2-1v-4z" class="C"></path><path d="M460 542l1 5c-2 1-3 2-4 3l-1 1 1 1-1 1-1-1-1-1-1-1c2-3 4-5 4-8 1 1 1 0 2 1 0-1 0-1 1-1h0z" class="Z"></path><path d="M479 495c0-2 0-4 1-5h2 3 2c1 1 1 9 1 12v5c0 1-1 3-1 4l-3-1-3 2c-1 2-1 3-2 4v-7-14z" class="K"></path><path d="M482 490h3v4c-2-1-4-2-5-4h2z" class="H"></path><path d="M481 512h0v-5c0-3-1-10 0-13 2 0 2 0 3 1 0 1-1 2 0 3v1c0 2-1 8 1 10v1h-1l-3 2z" class="a"></path><path d="M487 490c1 1 1 9 1 12v5c0 1-1 3-1 4l-3-1h1v-1c-2-2-1-8-1-10l1-5v-4h2z" class="N"></path><path d="M460 637l1 1-1 1v3l1 1c1-1 0-1 2-1l1-2 1 1h3v2l1 1c-1 1-1 1-1 2 1 2 0 4 0 6 1 1 1 1 0 2l1 1c2 1 1 6 1 8h0c-1 4 1 10-1 13 0 1 0 2 1 3 0 4 1 7-1 11v1 1c-1 0-2 0-3-1 0-1 0-1 1-2h1v-10-17l-1-2h0c-2 0-3-1-5-1v-5c-1 1-2 1-4 1v-1c0-2 0-2-1-3l1-1v-5-7-1h2z" class="E"></path><path d="M462 654c2 0 4 0 6 1v1 3l-1 1c-2 0-3-1-5-1v-5z" class="H"></path><path d="M463 642l1-2 1 1h3v2l1 1c-1 1-1 1-1 2 1 2 0 4 0 6-1 0-4 1-5-1 0-1-1-3 0-5h1c-1-2-1-3-1-4z" class="N"></path><path d="M463 512h0c-2-3-1-9-1-13 0-8-1-18 2-26l1 1h0v2h2v1h1l-2 2c0 1 1 2 1 4v2h2c1 1 1 4 1 6 0 1 0 1-1 1v3h2c0 2 1 6 0 8v1 7c-1 1 0 1-1 1v1c-2-1-2-1-4 0h-1c-1 0 0 0-1 1l-1-2z" class="C"></path><path d="M465 493c0-4-1-10 0-13v-1-1l-1-1h3 1l-2 2c0 1 1 2 1 4v2h2v1h-2l-1 1h1v1h-1l-1 5z" class="Z"></path><path d="M471 504v7c-1 1 0 1-1 1h-5v-7c2-1 4-1 6-1zm-2-19c1 1 1 4 1 6 0 1 0 1-1 1v3h2c0 2 1 6 0 8-2 0-3 1-5 0s-1-8-1-10l1-5h1v-1h-1l1-1h2v-1z" class="g"></path><path d="M469 495h-3c1-2 1-4 2-6l1 3v3z" class="W"></path><path d="M466 503v-1c0-2 1-4 1-5 1-1 3-1 4-1v7c-2 0-3 1-5 0z" class="X"></path><path d="M490 624l1 1-2 1v28c0 4-2 9-1 14 0 2-1 4-1 6v7h-1v-1l-1 1v-12-6l1-1c0-1 0-1-1-2h-1l-1-1-2-1c-3 0-2 1-4-1 0-1-1-4 0-5s1-2 1-3c2-2 0-4 2-7v1l1-1h4v-2c0-2-1-3-1-4 1-1 1-2 1-2v-1l-1-1c0-1 0-2 1-3s2-1 2-2h0l1-2c0-1 1-1 2-1z" class="B"></path><path d="M480 643l1-1h4c-1 2 0 3-2 4h-2c0-1-1-2-1-3z" class="X"></path><path d="M487 511c0-1 1-3 1-4 0 2 0 5 1 6s2 2 2 4c-1 2-1 3-1 5 1 6 1 12 0 18 0 2 0 5-1 8 0 2 1 5 1 7h-1v-1c-1 0-1-1-2-1v-1c-1-1-1-2-2-3l2-3h0v-1h-6-1l1-1-2-1h1v-1c0-5-1-9 0-14v-1-1-2-1c2 0 4 0 5-1 0-1 1-3 1-5v5h1c-1-4 0-7 0-11z" class="I"></path><path d="M480 523c2 0 4 0 5-1v4c-1 1-3 0-5 0v-2-1z" class="K"></path><path d="M480 527h4l1 1c1 4 1 9 0 13v2h-5v-1c0-5-1-9 0-14v-1z" class="N"></path><path d="M480 542c1-2 1-4 2-6l3 5v2h-5v-1zm0-15h4l1 1c0 2-1 4-1 6-1 1-2 0-3 0-1-2-1-4-1-6v-1z" class="K"></path><path d="M458 655c2 0 3 0 4-1v5c2 0 3 1 5 1h0l1 2v17 10h-1c-1 1-1 1-1 2 1 1 2 1 3 1h1l-1 1c-2 1-2 3-2 6v1c0 1-1 0-2 0l-3 1c-2 0-3 0-4-1l-1-1v-6c2-3 1-9 1-13v-25z" class="H"></path><path d="M462 694c1 1 2 1 4 1v2 1c0 1 0 1-1 2l-3 1v-7z" class="p"></path><path d="M458 655c2 0 3 0 4-1v5c2 0 3 1 5 1h0-4c-1 1-1 1-1 3v23 5c-1 2-4 0-4 2l3 1h1v7c-2 0-3 0-4-1l-1-1v-6c2-3 1-9 1-13v-25zm22-101v26 2l-1 12 2-1c1 0 3 0 4 1v2 3 2l1 1v1l1 1c2-1 1-6 1-8 1-1 1-3 1-4v17c0 2 0 13 1 15-1 0-2 0-2 1l-1 2h0c0 1-1 1-2 2s-1 2-1 3l1 1v1c-2 0-3 0-5-1h1l-1-1c0-2 0-2 1-3h2v-1h-4 0c-2-3-2-6-2-8v-10-18c0-4 1-7 1-11 2-3 0-21 1-25 0-1 1-2 1-2z" class="I"></path><path d="M481 593c1 0 3 0 4 1v2 3 2c-2 0-4 1-6 0v-7l2-1z" class="a"></path><path d="M479 619v-1h8c2-2 1-3 2-5v-4c0 2 0 13 1 15-1 0-2 0-2 1l-1 2h0c0 1-1 1-2 2v-4h0c-1-1-1-1-2-1l-1-1h3v-4h-6z" class="R"></path><path d="M479 619h6v4h-3l1 1c1 0 1 0 2 1h0v4c-1 1-1 2-1 3l1 1v1c-2 0-3 0-5-1h1l-1-1c0-2 0-2 1-3h2v-1h-4 0c0-1-1-2 0-3v-1-1h2 0 0l-2-1v-3z" class="N"></path><path d="M479 604c1 0 1-1 1-1h6l1 1v1c-1 3 0 8 0 12h-7c-2-2-1-10-1-13z" class="H"></path><path d="M479 604h1 2v1h0c-2 3 1 8-2 12h0c-2-2-1-10-1-13zm2-59h6v1h0l-2 3c1 1 1 2 2 3v1c1 0 1 1 2 1v1h1c1 3 0 5 0 8v11 11c-1 1 0 2-1 3v4c0 1 0 3-1 4 0 2 1 7-1 8l-1-1v-1l-1-1v-2-3-2c-1-1-3-1-4-1l-2 1 1-12v-2-26c0-3 1-6 2-8l-1-1z" class="N"></path><path d="M480 580c0-2 1-3 1-4v-1-6c0-4 1-8 1-12 1 1 1 2 1 3v-4c1 4 0 11 0 15 0 3 2 8 1 11h-4v-2z" class="K"></path><path d="M481 545h6v1h0l-2 3c1 1 1 2 2 3v1c1 0 1 1 2 1v1h1c1 3 0 5 0 8v11 11c-1 1 0 2-1 3v4c0 1 0 3-1 4 0 2 1 7-1 8l-1-1v-1l-1-1v-2-3-2c-1-1-3-1-4-1v-1l4-1v-3l-3-1v-1h1 1v-1c1-1 1-1 1-2l-1-1h2v-10h0v-6-12c0-1-3-4-3-5 0-2 1-2 1-3h-2l-1-1z" class="R"></path><path d="M486 554c2 5 0 12 2 17 1 3 0 7 0 10 1 2 1 5 1 7h0v4c0 1 0 3-1 4 0 2 1 7-1 8l-1-1v-1c2-1 1-3 1-5 1-3 0-6 1-9 0-3 1-9-1-11-1-2 0-3-1-5v-6-12z" class="U"></path><path d="M486 572c1 2 0 3 1 5 2 2 1 8 1 11-1 3 0 6-1 9 0 2 1 4-1 5l-1-1v-2-3-2c-1-1-3-1-4-1v-1l4-1v-3l-3-1v-1h1 1v-1c1-1 1-1 1-2l-1-1h2v-10h0z" class="L"></path><path d="M451 523h0l1-1 3 2h1c-2-2-3-2-4-4 1 0 2 1 3 2h1 1c1 0 1 0 3 1 0-1 0-1 1-2l1 1h1l2 1v1h6 0v1 14c-2 0-3 0-4-1v1l1 1c1 0 2 1 3 1v2l-1 1c-1-1-1-1-1-2h-1v1h-1l-1 1v3h-1l-1-1c-1 0-1 1-1 1-1-1-1-1-2-1 0-2 1-4 1-6h-3c0 1 0 1 1 2h0c-1 0-1 0-1 1-1-1-1 0-2-1 0 3-2 5-4 8-1 2-2 3-4 3v-9-12c-1-1-1-2-1-3h-1c1-1 3-1 4-1s3 2 4 3c0-1 0-2-1-3s-2-3-3-4v-1z" class="C"></path><path d="M461 532c0-2-1-3 0-5v2h2v1c0 1-1 1-2 2z" class="D"></path><path d="M467 538h-2c-1-4 0-9 0-13h6v14c-2 0-3 0-4-1z" class="e"></path><path d="M451 523h0l1-1 3 2h1c-2-2-3-2-4-4 1 0 2 1 3 2h1 1c1 0 1 0 3 1 0-1 0-1 1-2l1 1v1l1 1c-1 1 0 1-1 2l-1 1c-1 2 0 3 0 5s0 5 1 7c1 0 2 0 2 1 1 1 1 2 1 3v1l-1 2c-1 0-1 1-1 1-1-1-1-1-2-1 0-2 1-4 1-6h-3c0 1 0 1 1 2h0c-1 0-1 0-1 1-1-1-1 0-2-1v-1-1h0l1-1h3v-1h-6v-3-1-1c1 1 1 2 3 2-1-1-2-2-2-3l-1-1c0-1 0-2-1-3s-2-3-3-4v-1z" class="B"></path><path d="M455 526l3 1v-2c1 1 2 2 2 4h-3l-2-3z" class="G"></path><path d="M454 528c0-2-1-2-1-3l1-1 1 2 2 3c2 2 2 4 2 7-2 0-2 0-4-1v-1-1c1 1 1 2 3 2-1-1-2-2-2-3l-1-1c0-1 0-2-1-3z" class="R"></path><path d="M447 529c1-1 3-1 4-1s3 2 4 3l1 1c0 1 1 2 2 3-2 0-2-1-3-2v1 1 3h6v1h-3l-1 1h0v1 1c0 3-2 5-4 8-1 2-2 3-4 3v-9-12c-1-1-1-2-1-3h-1z" class="C"></path><path d="M447 529c1-1 3-1 4-1s3 2 4 3l1 1c0 1 1 2 2 3-2 0-2-1-3-2l-1-1h-1v1h0c-1-1-1-2-1-3h-1c-1-1-1-1-2-1l1 2-1 1c-1-1-1-2-1-3h-1zm2 15c0 2 0 3 1 5 1 1 1 1 2 0 1-2 1-4 1-7 1-2 0-4 1-7v-1h0 1v1 3h6v1h-3l-1 1h0v1 1c0 3-2 5-4 8-1 2-2 3-4 3v-9z" class="G"></path><path d="M481 446h5 1c1 1 0 1 0 2s1 2 2 3v1 12c0 1 0 4 1 5l-1 6v2c0 3 0 5-1 7l-1 1v3h0v2h-2-3-2c-1 1-1 3-1 5h0l-1 2h-1c-1 0-2 1-2 2v-39c0-3-1-7 0-11h0l3-1 3-2z" class="I"></path><path d="M475 449l3-1v5c0 2 0 3-1 5 0 2 0 4-1 6 0-4 0-9 1-14l-2-1h0z" class="E"></path><path d="M479 495h-1c-1 1-1 1-2 1l-1-1c2-2 1-4 1-6 1-1 2-1 3-1h1 4 3v2h-2-3-2c-1 1-1 3-1 5h0z" class="M"></path><path d="M478 453c0 2 0 3 1 4v12 2h8v1l-2 1h-5c-1 1-1 2-1 4v10c-1 0-2 1-2 1-2-2-1-20-1-24 1-2 1-4 1-6 1-2 1-3 1-5z" class="P"></path><path d="M487 471h1c1-2 1-4 1-7h0c0 1 0 4 1 5l-1 6v2c0 3 0 5-1 7l-1 1v3h0-3-4-1v-1-10c0-2 0-3 1-4h5l2-1v-1z" class="H"></path><path d="M487 471h1c1-2 1-4 1-7h0c0 1 0 4 1 5l-1 6v2c0 3 0 5-1 7l-1 1v-12h-2 0l2-1v-1z" class="L"></path><path d="M480 473h5 0-2c-1 2 0 5 0 8h1l1-1v-6h1c1 1 0 6 0 8l-1 1h-1c-1 1-2 1-2 3l2 2h-4-1v-1-10c0-2 0-3 1-4z" class="T"></path><path d="M480 473v1h1v2 10l-1 2h-1v-1-10c0-2 0-3 1-4z" class="K"></path><path d="M481 446h5 1c1 1 0 1 0 2s1 2 2 3v1 12h0c0 3 0 5-1 7h-1-8v-2-12c-1-1-1-2-1-4v-5l3-2z" class="Z"></path><path d="M479 457h4l1 1c1 0 1 0 2-1h0v-3c1 1 1 3 1 4l-1 1h0c0 1 0 1-1 2l1 1c-2 1-4 1-5 2s-1 1-1 3c0 1-1 1-1 2v-12z" class="H"></path><path d="M481 464v-4h0v-1h3l1 2 1 1c-2 1-4 1-5 2z" class="a"></path><path d="M486 462h1c0 1 1 1 0 2 0 2 1 5 0 7h-8v-2c0-1 1-1 1-2 0-2 0-2 1-3s3-1 5-2z" class="b"></path><path d="M455 463l2 2c0 1 2 1 2 1l1 2 1 1-1 1s1 0 2 1l1-1c1 1 1 1 1 2h1 2c-1 0-2 1-2 2l-1-1c-3 8-2 18-2 26 0 4-1 10 1 13h0l1 2 1 2c-1 2 0 5 0 7l-2-1h-1l-1-1c-1 1-1 1-1 2-2-1-2-1-3-1h-1c-2-2-2-3-3-5-2-4-4-8-5-12 0-2-1-4-1-5-1-2-1-4-1-6h1c-1-1-1-1-1-2v-4l-1 1v-3-3l1-1v-4l1-3c1-2 2-5 4-7 1-2 3-3 4-4v-1z" class="I"></path><path d="M451 497h2v2c0 1 1 2 1 3v1h-2l-1-1c1-1 0-4 0-5z" class="Q"></path><path d="M463 512l1 2 1 2c-1 2 0 5 0 7l-2-1v-10z" class="D"></path><path d="M458 485c1-1 1-3 1-4 0-2 1-4 0-5l2-1v1c1 0 0 1 0 2v9c-1 0-1-2-1-2 0-2 1-2 0-3 0 1-1 2-2 3z" class="M"></path><path d="M455 463l2 2c0 1 2 1 2 1l1 2 1 1-1 1-3-2-1 1c1 1 2 2 4 2l-1 1v2s-1 1-2 1c0-1-1-1-2-2h-5v-2c0-1 1-2 1-3h0c1-2 3-3 4-4v-1z" class="D"></path><g class="B"><path d="M455 463l2 2c0 1 2 1 2 1l1 2 1 1-1 1-3-2-1-1-1 1v1h-1v-2c1-1 1-2 1-3v-1z"></path><path d="M451 468h0c0 1-1 2-1 3v2h5c1 1 2 1 2 2s0 1-1 2c-2 0-3 1-4 3-1 4-1 8-1 12 0 0-1 0-1-1-2 0-2 0-4 1v-4l-1 1v-3-3l1-1v-4l1-3c1-2 2-5 4-7z"></path></g><path d="M451 468h0c0 1-1 2-1 3v2h5c1 1 2 1 2 2h-3 0c-2 0-2 0-4-1 0 1-1 1-2 2h0c-1 2-1 4-2 6v-4l1-3c1-2 2-5 4-7zm7 17c1-1 2-2 2-3 1 1 0 1 0 3 0 0 0 2 1 2v18c0 3 0 7 1 11v6l-1-1-3-3c1-1 1-4 0-6h-1c-2-2-2-6-4-8h-1v-1h2c1 0 2 0 2 1 3-3 1-13 2-17v-1-1z" class="C"></path><path d="M454 503c1 0 2 0 2 1 3-3 1-13 2-17v-1 26h-1c-2-2-2-6-4-8h-1v-1h2z" class="R"></path><path d="M446 492c2-1 2-1 4-1 0 1 1 1 1 1v5c0 1 1 4 0 5l1 1v1h1c2 2 2 6 4 8h1c1 2 1 5 0 6l3 3c-1 1-1 1-1 2-2-1-2-1-3-1h-1c-2-2-2-3-3-5-2-4-4-8-5-12 0-2-1-4-1-5-1-2-1-4-1-6h1c-1-1-1-1-1-2z" class="F"></path><path d="M452 504h1c2 2 2 6 4 8h1c1 2 1 5 0 6-4-4-5-8-6-14z" class="E"></path><path d="M446 492c2-1 2-1 4-1 0 1 1 1 1 1v5c0 1 1 4 0 5v1 3l1 2-1 1c-1-3-2-7-2-9l-1-1v6c0-2-1-4-1-5-1-2-1-4-1-6h1c-1-1-1-1-1-2z" class="G"></path><path d="M443 554h6c1 0 1 1 2 2v1l1 1h1c1-1 1-1 2-1 0 0 2 0 2 1 1 1 1 4 1 5h-1c-1-1-2-1-3-2-1 1-1 1-1 2v-2l-1 1c0 3 0 7 1 10h0c2 1 6 1 7 0 0 5 1 10 0 14v3l-2 1 1 1h1l-1 1c0 5-1 13 0 18v9h1v12l-1 1v1l1 4h-2c-4 0-8-1-12 2-1 1-1 2-2 3l1-7c0-8-1-18-1-26-1-2-1-5-1-8v-17c0-2 0-4 1-7v-2-1c-3-3-1-12-1-16v-4z" class="D"></path><path d="M446 633c0-2 0-7 1-8l1 1c0 2 2 4 4 5l-1 1 1 1c-2 1-4 0-6 0z" class="E"></path><path d="M453 631l1-1c0 2 0 2-1 3l1 1h0 2l1-1 1 2-1 1h1v-1-1l1-1 1 4h-2c-4 0-8-1-12 2-1 1-1 2-2 3l1-7v4h1v-3c0-2-1-2 0-3 2 0 4 1 6 0l-1-1 1-1 1 1v-1z" class="U"></path><path d="M453 572h0c2 1 6 1 7 0 0 5 1 10 0 14l-1-1c-1-2-1-5-1-8v-2c0-1-1-1-1-1h-2c-2 3-4 5-5 9v6c0 9 0 16-4 23v-2c3-8 2-18 3-26 1-4 2-7 2-10l2-2z" class="g"></path><path d="M450 627c-3-3-4-5-3-9 1-2 2-4 4-5l1-1c2-1 4-1 7-2v9h1v12l-1 1v1l-1 1v1 1h-1l1-1-1-2-1 1h-2 0l-1-1c1-1 1-1 1-3l-1 1-3-4z" class="I"></path><path d="M449 618v-2c1-1 2-2 3-2 2-1 2-1 4 0v1l-1 1v1c-1-1-1-1-2-1l-1 1c-1 1-1 0-2 1h-1z" class="B"></path><path d="M449 618h1c1-1 1 0 2-1l1-1c1 0 1 0 2 1 0 1-1 2-2 3h2c-2 1-2 1-3 1-2-1-2-1-3-3z" class="F"></path><path d="M450 627c0-2 0-2-1-4-1-1-1-2-1-3h0c2 2 3 3 5 4h2l1-2h1v3l-2 1v1c2 0 3-3 4-2 0 2 0 3-1 5v1l1 1v1l-1 1v1 1h-1l1-1-1-2-1 1h-2 0l-1-1c1-1 1-1 1-3l-1 1-3-4z" class="R"></path><path d="M443 554h6c1 0 1 1 2 2v1l1 1h1c1-1 1-1 2-1 0 0 2 0 2 1 1 1 1 4 1 5h-1c-1-1-2-1-3-2-1 1-1 1-1 2v-2l-1 1c0 3 0 7 1 10l-2 2c0 3-1 6-2 10-1 8 0 18-3 26h0l-1 1-1-2c-1-2-1-5-1-8v-17c0-2 0-4 1-7v-2-1c-3-3-1-12-1-16v-4z" class="G"></path><path d="M443 554h6c1 0 1 1 2 2v1l1 1h-2-7v-4z" class="g"></path><path d="M452 558h1c1-1 1-1 2-1 0 0 2 0 2 1 1 1 1 4 1 5h-1c-1-1-2-1-3-2-1 1-1 1-1 2v-2l-1 1c0 3 0 7 1 10l-2 2h0c-1-2-1-4 0-5h0c0-3 0-8-1-11h2z" class="B"></path><path d="M469 693v8c2 0 4 0 6-1h3 0c1 1 1 0 1 1l1 9h0v-6c1-1 1-4 1-5h0v6c-1 1 0 3 0 5 0 3 0 6-1 10h-1c-1 2 0 4-1 6v8c0 1 1 1 2 1 1 1 2 1 3 1 1 1 1 8 1 9v-32-9-5h0l1 40c0 15 1 31-1 47l1-2h0 2v7 2-2c-2 0-2 0-3 1v3 4l-1-2v-3h-4-1c-2 1-3 1-5 2-1 0-1 0-1-1s0-2 1-3v-2h-1l-1-2h1v-3h-2c-1-2-1-2 0-4-1-3 1-5 0-8-1-2-1-1-1-2h1c2-3 2-6 3-9h-1-2-1 0-5-1-3l-1-1v-1c-1-6 0-14 0-20l-1-40c1 1 2 1 4 1l3-1c1 0 2 1 2 0v-1c0-3 0-5 2-6z" class="V"></path><path d="M472 746c0-6 0-12 1-17 1 3 1 9 0 13l-1 4zm2 30l2 1h0l1-5-3 3h-1v-1h0v-1-1l8-1v1c-1 4-1 9 0 13-1-1-1-1-2-1-3-2-4-5-5-8z" class="F"></path><path d="M470 781v-1-1l1-3c1 0 2-1 3 0 1 3 2 6 5 8 1 0 1 0 2 1l2 2c1-1 1 0 1-1l1-2h0 2v7 2-2c-2 0-2 0-3 1v3 4l-1-2v-3h-4-1c-2 1-3 1-5 2-1 0-1 0-1-1s0-2 1-3v-2h-1l-1-2h1v-3h-2c-1-2-1-2 0-4z" class="d"></path><path d="M473 792v1c1-1 2-1 3-3l1 1c0 1 0 1 1 2h-1c-1 1-3 1-4 2v1c-1 0-1 0-1-1s0-2 1-3z" class="O"></path><path d="M484 786l1-2h0 2v7 2-2c-2 0-2 0-3 1v3c-1-5-3-8-5-11 1 0 1 0 2 1l2 2c1-1 1 0 1-1z" class="I"></path><path d="M476 759l-1-1 1-24c1 0 0 0 1 1 2 1 4 1 6 2v9c-2 0-4-1-5-2l-1 1v1c1 1 6 1 6 2 1 1 0 2 0 3v13c-1 1-1 1-2 1s-2 0-3 1v1l-2 1c2 2 3 2 4 2-1 0-2 0-4-1h0c-1 1-1 1-2 1l-4 1c2-3 2-6 3-9h1v2h1c0-2-1-3 1-4v-1z" class="F"></path><path d="M476 759c2 0 3-1 4 1v2c-1 1-2 1-3 1v2h-1v-5-1z" class="d"></path><path d="M469 693v8 1h3c1 2 1 4 1 6v15 6c-1 5-1 11-1 17 1 4 2 12 0 15v1h-2-1 0-5-1-3l-1-1v-1c-1-6 0-14 0-20l-1-40c1 1 2 1 4 1l3-1c1 0 2 1 2 0v-1c0-3 0-5 2-6z" class="D"></path><path d="M468 703c1 1 2 1 2 2 1 4 0 11 1 15 0 12 1 24 0 36 0 2-1 4-2 6h0-5c2-1 3-2 4-3v-56z" class="R"></path><path d="M463 762c-1-1-1-2-1-3-1-7-1-14-1-21 1-12 0-24 1-35h6v56c-1 1-2 2-4 3h-1z" class="H"></path><path d="M517 520h1c0-1 1-2 1-3l-2-1c1-2 2-4 2-6 0 2 0 4-1 6l2 1-1 1c2 1 1 3 2 5v20h2v1c0 1 0 2 1 4l-1 1h-3l-1 4c2 0 3 0 4 1l1 2-1 12v3c-1 9 1 18 1 26 0 6-1 15 1 21l-1 1 2 2 3 3v2l-2 2c1 0 2 1 3 2v2c0 2 0 3-1 5v2h2v4h-2l-3-1 1 1c-2-1-2-1-2-2-1-1-1-1-1-2 0-4 1-10 0-14v-1h0c-1-4-4-6-7-8 0 2 1 5 0 7v1c0 2 0 3-1 4v1 1h-1-21l-1 1v1 1-7l-2-1-1-1c-1-2-1-13-1-15v-17-4c1-1 0-2 1-3v-11-11c0-3 1-5 0-8 0-2-1-5-1-7 1-3 1-6 1-8 1-6 1-12 0-18 0-2 0-4 2-5l2 1v-2h1c2 0 4-1 6 0l2 1h13c0 1 0 2 1 3z" class="D"></path><path d="M496 551v-7c2 0 5-1 7 0h2c4-1 8 0 11 0h0v7c0 1 0 1-1 1h-19v-1z" class="T"></path><path d="M516 544v7c0 1 0 1-1 1h-19v-1c2 0 11 0 12-1l-1-2 1-1 1 1 1-1v-1c0-1 2 0 4-1h1 0l1-1h0z" class="K"></path><path d="M516 544v7h-2c-1-1 0-1-2-1h-1c1-1 1-1 1-2l-1-1c0 1 0 1-1 2l-1-1 1-1v-1c0-1 2 0 4-1h1 0l1-1h0z" class="N"></path><path d="M493 626v-39-3-17-5c0-1 1-1 1-2l1 1v7 29-2l1 1v22 10 1h14 6v1h-1-21l-1 1v1 1-7z" class="C"></path><path d="M503 517h13c0 1 0 2 1 3-2 0-3 0-4-1l-1 1-1-1h-1-6v2h0l6 1 6-1 1 22c-7-1-14-1-21 0v-22h7l-1-2s-1 0-1 1h-2l-1-1h-1-1v-1c1-2 5-1 7-1z" class="H"></path><path d="M504 521l6 1c-1 6 0 12-1 19h-1-1c-1-1 0-4 0-5-1-4 0-9-1-14l-2-1z" class="W"></path><path d="M496 596c0-1 0-1 1-2h5c4 1 9 0 12 1h2v33 1h-6-14v-1-10-22z" class="b"></path><path d="M502 594c4 1 9 0 12 1h-4c-1 3 0 7 0 10v21 3h-14v-1c2 1 6 0 8 0 0-4 1-8 1-12s-1-8-1-11v-9l-2-2z" class="K"></path><path d="M514 595h2v33 1h-6v-3-21c0-3-1-7 0-10h4z" class="b"></path><path d="M521 543h2v1c0 1 0 2 1 4l-1 1h-3l-1 4c2 0 3 0 4 1l1 2-1 12v3c-1 9 1 18 1 26 0 6-1 15 1 21l-1 1 2 2 3 3v2l-2 2c1 0 2 1 3 2v2c0 2 0 3-1 5v2h2v4h-2l-3-1 1 1c-2-1-2-1-2-2-1-1-1-1-1-2 0-4 1-10 0-14v-1h0c-1-4-4-6-7-8 0 2 1 5 0 7v1c0 2 0 3-1 4v-33h-2c-3-1-8 0-12-1h-5-1v-10c-1-1 0-1 0-2v-29h14 5v-1c1 0 1 0 1-1v-7h2l3-1z" class="B"></path><path d="M521 543h2v1c-1 0-2 0-2 1l-1 1h-2l-1-1 1-1 3-1z" class="O"></path><path d="M519 553c2 0 3 0 4 1l1 2-1 12v3c-1 0-2 0-4-1v-17z" class="R"></path><path d="M526 642c-2-4-1-14-1-19-1-3-3-5-5-7-1 0-1 0-2-1 1 0 2 0 3 1l3 3 2 2 3 3v2l-2 2c1 0 2 1 3 2v2c0 2 0 3-1 5v2h2v4h-2l-3-1z" class="I"></path><path d="M530 632l-1 1c-1-1-2-1-3-2v-3h1c1 0 2 1 3 2v2z" class="E"></path><g class="b"><path d="M510 553h5l2 1v28c-2 0-4 0-5-1-1 0-1-1-1-1-1-3 0-7 0-10l-1-17z"></path><path d="M496 582h1c2-2 11 0 15-1 1 1 3 1 5 1v3 9l-13-1c-3 0-5 0-8 1v-10c-1-1 0-1 0-2z"></path></g><path d="M496 582h1c2-2 11 0 15-1 1 1 3 1 5 1v3h-8l-1 1h-1c-2-1-2-1-2-2-3 1-7 0-9 0-1-1 0-1 0-2z" class="R"></path><path d="M496 582v-29h14l1 17c0 3-1 7 0 10 0 0 0 1 1 1-4 1-13-1-15 1h-1z" class="H"></path><path d="M516 628c1-1 1-2 1-4v-1c1-2 0-5 0-7 3 2 6 4 7 8h0v1c1 4 0 10 0 14 0 1 0 1 1 2 0 1 0 1 2 2h-1-1-2v1c0 3 0 5 1 8v1 2c1 0 1 1 1 2 0-2 0-2 1-3h3s1 1 2 1h0l1 1c1 1 1 1 1 2-1 2-1 4 0 5v1c0 2 0 5 1 6l-1 72c-2 0-4 1-7 0l-1 2c-1 0-1 0-2 1v5c0 1 1 2 2 2s1-1 2 0l-1 1-1 1v2c-2 0-3 1-3 2-1 3-1 7-1 11 0 1 0 3 1 5h-2v-2c-1 1-1 1-1 2l4 2-1 2c-1 1-2 2-3 2 0 1 0 1 1 2 0 1 0 2-1 3v1h1 1 1c0 2 0 4-1 6h1l2 1h6c0 1-1 2-1 3l1 1h1c-1 1-2 2-2 3l-1 1h-1v5 1 1c1 1 1 3 1 4 0 3-1 11 1 13l-1 2-1-1v1c0 1 0 0-1 2s1 5-1 7c0 1 1 2 0 4 0 1 0 0-1 2v1 1h0c-1 4-5 6-8 7-2 1-4 1-6 2h-1-1c-2 0-4 1-5 0h-2-1l-1-1c-2 0-4-1-5-1h-1c-3-1-6-3-7-6h0c-1-3-2-6-2-9v-4-33-4-3c1-1 1-1 3-1v2-2-7h-2 0l-1 2c2-16 1-32 1-47l-1-40 1-10v-8l1-1v1h1v-7c0-2 1-4 1-6-1-5 1-10 1-14v-28l2-1 2 1v7-1-1l1-1h21 1v-1-1z" class="V"></path><path d="M511 693l4 1c0 3 0 7 1 11 1 5 0 12 0 17 0 2 1 5 0 6-1-1 1-17-1-20v-2h0v-1l-1-1c-1-1-1-1-2 0h0v-1l-1-1v-1l1-1c0-2 0-4-1-6v-1zm1 138c1-4 1-9 1-13 0-2 0-4 1-6 1 0 3 1 5 0h1l-1 1h-3v1h-2v8 18c1-1 4-3 4-5h2l3-3v1 2 2c-1-1-1-1-2-1l-6 7-3 3h-1v-1l1-1c0-1 1-1 0-2v-11z" class="G"></path><path d="M509 839c1 1 1 0 2 1v1c0-4-1-7 1-10v11c1 1 0 1 0 2l-1 1v1h1l3-3 6-7c1 0 1 0 2 1-1 1-1 2-1 4h0c-2 5-5 5-8 8h-4c-1-2-1-3-1-5v-5z" class="O"></path><path d="M509 683h4 2v2h1c1 1 1 1 1 2h-1l2 2v4c-1 1-1 1-3 1l-4-1h-2c-3 0-8 1-11 0h-3-1-1v-4-3l1-1c1-1 3-1 4-2h5 3 3z" class="p"></path><path d="M493 686l1-1c1-1 3-1 4-2h5l-3 1c0 1-1 2-1 3 0 2-1 2-2 3h-1-1l-1-1h-1 0v-3z" class="I"></path><path d="M509 683h4 2v2h1c1 1 1 1 1 2h-1l2 2v4c-1 1-1 1-3 1l-4-1h-2c-3 0-8 1-11 0h-3-1-1v-4h0 1l1 1 1 1h1 2c4 1 8 0 12 0-1-3-1-5-2-8z" class="U"></path><path d="M512 704c1-1 1-1 2 0l1 1v1h0v2c2 3 0 19 1 20v43c1 9 2 19 1 28h-2v1c0 1 0 1-1 1-1-1-1-2-1-3v-2c-1-9 0-19-1-28l1-51-1-7v-5-1z" class="B"></path><path d="M512 705l2-1c0 4 1 25-1 27v-11-3l-1-7v-5zm10 87l2 1h6c0 1-1 2-1 3l1 1h1c-1 1-2 2-2 3l-1 1h-1v5 1 1c1 1 1 3 1 4 0 3-1 11 1 13l-1 2-1-1v1c0 1 0 0-1 2s1 5-1 7c0 1 1 2 0 4 0 1 0 0-1 2v1 1h0c-1 4-5 6-8 7-2 1-4 1-6 2h-1-1c-2 0-4 1-5 0h-2-1l-1-1c-2 0-4-1-5-1h-1c-3-1-6-3-7-6h1 2c2 1 4 3 7 3 1 0 2 1 3 1h3v-1c-1-2-1-3-1-5h0 0l3 3h1c-1-1 0-1-1-1 1-1 1-1 2-1l1-1 1 1h1c0 2 0 3 1 5h4c3-3 6-3 8-8h0c0-2 0-3 1-4v-2-2-1l-3 3h-2l3-3 1-2v-2c2-6 1-13 0-19 0-3 1-5 0-8-1-1-1-3-1-4h1l1-1h0c-1-2 0-2-1-4z" class="C"></path><path d="M522 792l2 1c2 0 2 0 3 2 0 1 0 1-1 2 0 2 1 1-1 3h0-2l-1 1c-1-1-1-3-1-4h1l1-1h0c-1-2 0-2-1-4z" class="G"></path><path d="M501 843h0l3 3h1c-1-1 0-1-1-1 1-1 1-1 2-1l1-1 1 1h1c0 2 0 3 1 5h4-3c-3 1-5 1-7 1-2-1-3-1-5-1h3v-1c-1-2-1-3-1-5h0z" class="B"></path><path d="M502 774h-1c0-1 0-4 1-5v-1-1c2 0 5-1 6 0v4c1 1 1 2 2 3-1 2-1 4-1 6v3c1 2 1 4 0 7v2l2 1c1 1 0 3 2 4l-1-3h0v-16-9-1c1 9 0 19 1 28v2c0 1 0 2 1 3-2 1-2 1-2 3v1h0c0 1 1 2 1 2h1l1 1c0 1 0 3-1 4-1 2-1 4-1 6 0 4 0 9-1 13-2 3-1 6-1 10v-1c-1-1-1 0-2-1v5h-1l-1-1-1 1c-1 0-1 0-2 1 1 0 0 0 1 1h-1l-3-3h0v-8-16-12h0v-1c-1-1-1-1-2-1s0 0-1-1v-1h5 0v-2h0c-1 0-2 0-3-1h0v-1-1l-1-1v-2c1-2 1-2 2-2 1-2 1-6 1-9-1-1 0-4 0-6-1-1-1 0 0-1v-3z" class="F"></path><path d="M501 807v-1c-1-1-1-1-2-1s0 0-1-1v-1h5 8v1l-2 2c-2 0-2 1-2 2 2 2 2 3 2 6l-1 1c0 1 0 2 1 3v10 11 5h-1c1-3 0-7 0-9 0-9 0-18-1-27-2-1-4-1-6-1h0z" class="E"></path><path d="M502 774h5l1 1h0c-1 1-1 1-1 2s0 0 1 1v6c1 2-1 7 0 9 1 1 2 1 2 2 1 1 1 0 0 1 0 1 2 1 1 2-1 0 0 0-1 1l-1 1h-9v-1-1l-1-1v-2c1-2 1-2 2-2 1-2 1-6 1-9-1-1 0-4 0-6-1-1-1 0 0-1v-3z" class="D"></path><defs><linearGradient id="AZ" x1="508.243" y1="813.316" x2="499.757" y2="840.684" xlink:href="#B"><stop offset="0" stop-color="#919191"></stop><stop offset="1" stop-color="#b0acaa"></stop></linearGradient></defs><path fill="url(#AZ)" d="M501 807c2 0 4 0 6 1 1 9 1 18 1 27 0 2 1 6 0 9l-1-1-1 1c-1 0-1 0-2 1 1 0 0 0 1 1h-1l-3-3h0v-8-16-12z"></path><path d="M501 819c1-2 1-5 1-7 0-1 1-2 2-2l2 2v1l-2-2c-2 3-2 27-1 30v2c1 1 2 1 3 1-1 0-1 0-2 1 1 0 0 0 1 1h-1l-3-3h0v-8-16z" class="g"></path><path d="M501 807c2 0 4 0 6 1 1 9 1 18 1 27 0 2 1 6 0 9l-1-1-1-30v-1l-2-2c-1 0-2 1-2 2 0 2 0 5-1 7v-12z" class="L"></path><path d="M526 654h3s1 1 2 1h0l1 1c1 1 1 1 1 2-1 2-1 4 0 5v1c0 2 0 5 1 6l-1 72c-2 0-4 1-7 0-1 0-3 0-4-1l2-59c1-6 0-11 0-17 0-2 0-4 1-5l1-1-2-1 1-1c0-2 0-2 1-3z" class="U"></path><path d="M526 654h3s1 1 2 1h0l1 1v1c0 1-1 1-2 2h-2v4h3l1-1c0 1 0 2-1 2v8c1 7 1 14 0 20v-16c0-3 0-7-1-10v-1l-1-1-1 1c-1 3-1 5-1 8l-1 18c0 4 1 9 0 14-1-11-1-22 0-33l1-13v-4l-1-1z" class="K"></path><path d="M526 705c1-5 0-10 0-14l1-18c0-3 0-5 1-8l1-1 1 1v1c1 3 1 7 1 10v16 50h-5c-2-3-1-32 0-37z" class="H"></path><path d="M512 703v1h0v1 5l1 7-1 51v1 9 16h0l1 3c-2-1-1-3-2-4l-2-1v-2c1-3 1-5 0-7v-3c0-2 0-4 1-6-1-1-1-2-2-3v-4c-1-1-4 0-6 0v1 1c-1 1-1 4-1 5h1v3c-1 1-1 0 0 1 0 2-1 5 0 6-1 2-1 4-1 6l-1 2h-1-1v1 1l-1-1c0-1 0 0-1-1 0 0 1-1 1-2 1-1 1-8 0-10v-7-67l1-1c1 0 2 0 3 2 0 1 0 1-1 2 0 3 0 5 2 7l1 1v-1l1 1c1 0 1 0 2-1v1l1 1c1-2 1-2 0-3v-1c0-1 1-1 2-2v-1c-1-1-1-1-3-2 1-1 1-2 2-3v-2c1 0 2 0 4-1z" class="d"></path><path d="M512 703v1h0v1 5c0-1-1-2-1-3l-1-1h-2v-2c1 0 2 0 4-1z" class="F"></path><path d="M494 752l1-9c0-2 0-4-1-5v-5h1v18l1 1v-6-14-25l1-1v67 7c1 2 1 9 0 10 0 1-1 2-1 2 1 1 1 0 1 1l1 1v-1-1h1 1l1-2c0-2 0-4 1-6 0 3 0 7-1 9-1 0-1 0-2 2v2l1 1v1 1h0c1 1 2 1 3 1h0v2h0-5v1c1 1 0 1 1 1s1 0 2 1v1h0v12 16 8h0c0 2 0 3 1 5v1h-3c-1 0-2-1-3-1-3 0-5-2-7-3h-2-1 0c-1-3-2-6-2-9v-4-33-4-3c1-1 1-1 3-1v2c2-1 2-1 3-2l1-2c1 0 2-1 3-1v-2l1-17c0-3-1-6-1-9v-8z" class="O"></path><path d="M490 791c2-1 3-1 5-1v1c-1 1-5 4-7 4h0l-1 3v-5c2-1 2-1 3-2z" class="E"></path><path d="M501 835c-1-3-1-8-1-11v-3c-1-3 0-5-1-7 0-1 0-2 1-3 0-2 0-3 1-4h0v12 16zm1-51c0 3 0 7-1 9-1 0-1 0-2 2v2l1 1v1 1l-1-1c-1 0-2 0-3-1v-2-1l-2 2c-1 2-1 6 0 8v1 2c1 1 1 3 2 5v3c0 2 0 4 1 5v2c-2 8 1 13 1 20h0c0-1-1-2-1-3-2-7-2-13-5-20l1-1c-2-2-4-4-3-6v-1c1 2 1 3 3 4h0c0 1 1 1 2 2v-1h1v-2h-1v-1c-3-5-3-10-3-15v-4c1-2 3-2 4-3 1 1 1 0 1 1l1 1v-1-1h1 1l1-2c0-2 0-4 1-6z" class="G"></path><defs><linearGradient id="Aa" x1="479.025" y1="796.165" x2="490.883" y2="816.677" xlink:href="#B"><stop offset="0" stop-color="#575758"></stop><stop offset="1" stop-color="#969595"></stop></linearGradient></defs><path fill="url(#Aa)" d="M484 795v-3c1-1 1-1 3-1v2 5 28c0 6-1 13 1 18l1 1h0-2-1 0c-1-3-2-6-2-9v-4-33-4z"></path><defs><linearGradient id="Ab" x1="482.207" y1="821.272" x2="488.896" y2="835.953" xlink:href="#B"><stop offset="0" stop-color="#9b9d9c"></stop><stop offset="1" stop-color="#bfbabc"></stop></linearGradient></defs><path fill="url(#Ab)" d="M484 836c1-5 0-11 1-16h1l1 6c0 6-1 13 1 18l1 1h0-2-1 0c-1-3-2-6-2-9z"></path><path d="M491 625l2 1v7-1-1l1-1h21 1v5 42h1 2v1c-1 1-1 2-2 3h-1l-10 1v1h-3-5c-1 1-3 1-4 2l-1 1v3 4h1 1 3c3 1 8 0 11 0h2v1c1 2 1 4 1 6l-1 1v1l1 1c-2 1-3 1-4 1v2c-1 1-1 2-2 3 2 1 2 1 3 2v1c-1 1-2 1-2 2v1c1 1 1 1 0 3l-1-1v-1c-1 1-1 1-2 1l-1-1v1l-1-1c-2-2-2-4-2-7 1-1 1-1 1-2-1-2-2-2-3-2l-1 1-1 1v25 14 6l-1-1v-18h-1v5c1 1 1 3 1 5l-1 9v8c0 3 1 6 1 9l-1 17v2c-1 0-2 1-3 1l-1 2c-1 1-1 1-3 2v-2-7h-2 0l-1 2c2-16 1-32 1-47l-1-40 1-10v-8l1-1v1h1v-7c0-2 1-4 1-6-1-5 1-10 1-14v-28l2-1z" class="V"></path><path d="M492 652l1-10c0 1 0 2 1 2v4 1c-1 1-1 2-1 4h-1v-1z" class="D"></path><path d="M509 711l-1 1c-2 0-4 0-6-1v-1-1c2 1 2 0 3 0h1c2 1 2 1 3 2z" class="a"></path><path d="M485 681l1-1v1h1v19l-1-11h-1v-8z" class="E"></path><path d="M494 648v29l4 1v3h-1c-1-1-1-1-2-1l-1 1h-1v-2c-1-1-1-2-3-2h0c-1-1-1-3 0-4h0v3c1 0 1 1 2 1h1 0l-1-1h0c-1-2-1-2-1-3 1 1 1 2 2 2h0v-7-5h-1c-1-2 0-9 0-11v1h1c0-2 0-3 1-4v-1z" class="B"></path><path d="M491 789l1-54c1 2 0 7 0 10v25 3c-1 2-1 5 0 8h0c1 1 1 2 1 4h1v-8l-1-1v-2c0-2-1-6 0-8v-4c1-1 1-3 1-4 1-2-1-4 0-6v8c0 3 1 6 1 9l-1 17v2c-1 0-2 1-3 1z" class="G"></path><path d="M494 644h1 2l1 1-1 1c1 1 1 1 3 1-2 1-2 2-3 2v2l-1 2h4 1v1l-1 1c-1 1-1 2-1 4v1h-2v2l-1 1c2 0 4 0 6-1v1h-2v7l-1 1h-1l-1 1c0 1 1 2 1 3-1 1 0 2 0 3l-4-1v-29-4z" class="L"></path><path d="M495 693h3c3 1 8 0 11 0h2v1c1 2 1 4 1 6l-1 1v1l1 1c-2 1-3 1-4 1v2c-1 1-1 2-2 3h-1l-1-2h-1l-3-3h-1c-2-2-1-3-2-5v-1-3c1 1 1 1 1 3h1 0c0-2 1-2 0-3s-2-2-4-2z" class="B"></path><path d="M500 704l1-2v-2h1c0-2 0-2 1-2l1 1c1 0 1 0 2 1v1c0 1 0 1-1 2 3 0 4 0 6-1l1 1c-2 1-3 1-4 1v2c-1 1-1 2-2 3h-1l-1-2h-1l-3-3z" class="E"></path><defs><linearGradient id="Ac" x1="479.194" y1="737.061" x2="491.321" y2="735.561" xlink:href="#B"><stop offset="0" stop-color="#3d403e"></stop><stop offset="1" stop-color="#5c5a5c"></stop></linearGradient></defs><path fill="url(#Ac)" d="M485 689h1l1 11v91-7h-2 0l-1 2c2-16 1-32 1-47l-1-40 1-10z"></path><path d="M493 633v-1-1l1-1h21 1v5 42h1 2v1c-1 1-1 2-2 3h-1l-10 1v1h-3-5c-1 1-3 1-4 2l-1 1h0v-5h1l1-1c1 0 1 0 2 1h1v-3c0-1-1-2 0-3 0-1-1-2-1-3l1-1h1l1-1v-7h2v-1c-2 1-4 1-6 1l1-1v-2h2v-1c0-2 0-3 1-4l1-1v-1h-1-4l1-2v-2c1 0 1-1 3-2-2 0-2 0-3-1l1-1-1-1h-2-1c-1 0-1-1-1-2v-9z" class="J"></path><path d="M493 686h0v-5h1c4 1 8 1 12 1v1h-3-5c-1 1-3 1-4 2l-1 1z" class="F"></path><path d="M516 677h1 2v1c-1 1-1 2-2 3h-1l1-1c-1-1-2 0-3 0s-1-1-2-1h-1c0-2 0-2 1-3l1 1h3 0z" class="D"></path><path d="M513 634h1v8 2 4c-1 2-1 3-1 4 0-1-1-2-1-2 0-2 1-1 1-3h-6v-5-2h2 3c0-2 0-2-1-4 1 0 1-1 2-2h0z" class="S"></path><path d="M500 640c2 0 4 1 6 3l1-1v5h6c0 2-1 1-1 3 0 0 1 1 1 2l1 1h-1c-1 0-4 0-5 2 0 0-1 1-1 2l1 1c1-1 1-2 1-3h4l1 2c-1 0-1 1-2 1v1h1c-1 1-2 1-3 2h-2c-2 0-6 1-8 0l1-1h7c1-1-2-2-2-3v-1c-2-1-4-2-5-2v-1h-1-4l1-2v-2c1 0 1-1 3-2h0c-1-2 0-5 0-7z" class="H"></path><path d="M500 640c2 0 4 1 6 3l1-1v5c-1 0-1 0-2-1v2c-1 1-1 1-1 2l-1-2v2l1 2h-3v1h-1-4l1-2v-2c1 0 1-1 3-2h0c-1-2 0-5 0-7z" class="N"></path><path d="M500 640c2 0 4 1 6 3l1-1v5c-1 0-1 0-2-1s-2-2-4-3c0 2 0 3 1 4v1c0 1 0 2-1 3-1-1-1-1-1-2v-2c-1-2 0-5 0-7z" class="b"></path><path d="M500 670v-7h2v-1c-2 1-4 1-6 1l1-1v-2h2v-1c0-2 0-3 1-4l1-1c1 0 3 1 5 2v1c0 1 3 2 2 3h-7l-1 1c2 1 6 0 8 0h2 4v1c-2 0-4 0-6 1l-1 1v5c0 1 1 1 1 1h4l2 1c-1 1-1 1-1 2v2h-1l-2 1v1c-3 0-5 1-7 0h-3v-4h-1l-2-1 1-1h1l1-1z" class="T"></path><path d="M505 665c1-1 1-1 2-1v5c-1 0-2 0-2-1v-2h0v-1z" class="H"></path><path d="M505 665v1h0v2c-2-1-3-1-4-2l1-1h3z" class="a"></path><path d="M500 670c1 0 1 0 2 1 2 1 3 2 5 3v1c-1 0-2 1-3 1l-1 1h0-3v-4h-1l-2-1 1-1h1l1-1z" class="K"></path><path d="M493 633v-1-1l1-1h21 1v5l-2-1h-1 0c-1 1-1 2-2 2 1 2 1 2 1 4h-3-2v2l-1 1c-2-2-4-3-6-3 0 2-1 5 0 7h0c-2 0-2 0-3-1l1-1-1-1h-2-1c-1 0-1-1-1-2v-9z" class="R"></path><path d="M515 630h1v5l-2-1h-1c-2-1-5-1-7-1s-7-1-9 0v1h-2-1v-2c2-1 7 0 10 0 1-1 8-1 10-1l1-1z" class="E"></path><path d="M495 634h2v-1c2-1 7 0 9 0l-5 1h-2v5l1 1c0 2-1 5 0 7h0c-2 0-2 0-3-1l1-1-1-1h-2v-10z" class="Z"></path><path d="M497 644c0-1 0-2 1-3v-1h-1v-1h2l1 1c0 2-1 5 0 7h0c-2 0-2 0-3-1l1-1-1-1z" class="C"></path><path d="M499 639v-5h2l5-1c2 0 5 0 7 1h0c-1 1-1 2-2 2 1 2 1 2 1 4h-3-2v2l-1 1c-2-2-4-3-6-3l-1-1z" class="b"></path><path d="M499 639v-5h2c-1 0-1 1 0 1 1 2 3 3 5 4 0 1 1 1 1 1v2l-1 1c-2-2-4-3-6-3l-1-1z" class="T"></path></svg>