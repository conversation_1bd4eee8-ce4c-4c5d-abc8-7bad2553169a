<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="50 40 558 624"><!--oldViewBox="0 0 648 752"--><style>.B{fill:#b1b0b1}.C{fill:#c6c5c6}.D{fill:#959595}.E{fill:#acabab}.F{fill:#a1a0a1}.G{fill:#777677}.H{fill:#848385}.I{fill:#1f1e1f}.J{fill:#c3c2c3}.K{fill:#cfcecf}.L{fill:#d6d4d5}.M{fill:#8d8d8d}.N{fill:#848484}.O{fill:#2f2e2f}.P{fill:#bfbebe}.Q{fill:#282829}.R{fill:#a7a6a6}.S{fill:#686868}.T{fill:#636363}.U{fill:#888889}.V{fill:#0e0e0e}.W{fill:#505050}.X{fill:#dddcdd}.Y{fill:#464546}.Z{fill:#262525}.a{fill:#6f6f70}.b{fill:#d1d0d1}.c{fill:#5d5c5d}.d{fill:#141414}.e{fill:#fefefe}.f{fill:#3c3c3c}.g{fill:#e9e8e9}.h{fill:#1a1a1b}.i{fill:#363536}.j{fill:#585858}.k{fill:#c2c1c3}.l{fill:#414142}</style><path d="M554 197l1 3-2 2-1-5h2z" class="I"></path><path d="M198 262l2-4h2l-3 6h0v-1-1h0-1z" class="Q"></path><path d="M516 278l4 2v2h-1l-5-3c1 0 1 0 2-1z" class="Z"></path><path d="M139 465l1 2v-2l1 1-1 5c-1 1-1 2-2 3l1-9z" class="O"></path><path d="M510 274l6 4c-1 1-1 1-2 1-1-1-3-2-4-4v-1z" class="I"></path><path d="M582 264c0 1 0 1 1 1-1 2-3 4-5 6 0-1 0-1-1-2l5-5z" class="i"></path><path d="M139 465l4-15v4h0l-2 12-1-1v2l-1-2z" class="Z"></path><path d="M312 142h1v24h1l-1 40h-1v-49-15z" class="I"></path><path d="M312 157c1 3 0 7 0 10v22c0 1 0 7 1 8v-1c1-2 0-5 0-7v-23h1l-1 40h-1v-49z" class="Q"></path><path d="M258 232c-1-7-1-14 0-21 0-4 0-9 2-12 0-2 1-4 3-4h3c5 1 9 5 13 8 3 2 5 3 7 5h-1l-3-1c-4-3-9-8-14-10-1-1-3-1-4-1-2 1-2 2-3 4-1 3 0 8-1 12v13c1 2 1 3 1 5l1 2 3 6h0v1 1c1 1 1 1 1 2l-1 1v-2h-1l-1-1c0-1 0-1-1-1 0 0 0-1-1-1v-2c-1-2-2-3-3-4z" class="d"></path><path d="M577 269c1 1 1 1 1 2-8 9-19 15-32 16-7 0-15 0-21-2l-6-3h1v-2c6 2 11 4 18 5 10 0 20-2 28-7 4-3 8-6 11-9z" class="Y"></path><defs><linearGradient id="A" x1="538.456" y1="622.664" x2="552.318" y2="644.987" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#383638"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M558 616l1-1c0-1 1-3 0-4v-1c1 0 1 1 2 1v31l-41-1v-2h4 1 23c3 0 7 0 10-1v-22z"></path><path d="M120 113l1 1v4c0 5-1 10-2 15-2 12-6 23-9 35-3 8-6 15-8 23-4 16-2 35 4 50 0 1 0 1-1 1-2-4-3-9-4-14-4-14-2-29 1-43l8-25c5-15 9-30 10-47z"></path><path d="M372 85l50-1h116 44 9 3s0 1 1 1h-2l1 1h-1-3-7-19-85-42c5-1 11-1 16-1 1 0 4 1 5 0h-86z" class="Z"></path><path d="M593 86h1l-1-1h2v94 31c0 10 0 21-2 31-2 8-5 16-10 24-1 0-1 0-1-1 4-6 8-14 9-22 3-16 2-33 2-49v-70-37z"></path><path d="M106 241l5 8h0c3 3 5 7 8 10 10 11 25 18 39 20 13 1 24-2 34-10 2-2 4-4 5-6l1-1h1 0v1 1 1c-3 6-11 12-17 14-12 5-29 4-41-1-10-5-20-11-27-21-2-2-4-5-5-8-2-2-3-4-4-7 1 0 1 0 1-1z" class="Y"></path><path d="M106 241l5 8-1-1h-1v-1l-1-1h0c0 1 1 2 1 3-2-2-3-4-4-7 1 0 1 0 1-1z" class="Z"></path><path d="M121 85h251 86c-1 1-4 0-5 0-5 0-11 0-16 1-4 1-10 0-15 0h-35-111-108-31c-5 0-11 1-15 0l-1-1h0 0z"></path><defs><linearGradient id="C" x1="233.386" y1="273.766" x2="269.747" y2="253.222" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#C)" d="M258 232c1 1 2 2 3 4v2c1 0 1 1 1 1 1 0 1 0 1 1l1 1h1v2l1-1c0-1 0-1-1-2v-1-1c1 1 2 2 2 4-1 1-1 2-3 2-1 2-2 3-3 5l-3 3c-3 4-4 10-5 15-1 2-2 5-2 7-1 3-1 6 0 8v6 1c0 1-3 4-4 4h0-1l-1 1v-1l1-1-1-2h0l1-2h-1-1c0-2 1-4 0-5v-4-7c0-4 0-8 1-11 3-11 7-20 13-29z"></path><path d="M255 245v1 2c0 3-2 5-3 8-1 2-2 5-3 8 0 2-1 4-1 6 0 3 1 9-1 12-1-1 0-4 0-5 0-6 1-12 3-17 1-4 2-6 3-10 1-1 1-3 2-5z" class="c"></path><path d="M261 236v2c1 0 1 1 1 1 1 0 1 0 1 1l-1 2-2 2h-1l-1-1-1 1v1 1c0 1-1 2-1 3 0 0 0-1-1-1v-2-1-1c-1 0-1 0-1-1h0v-1 1c1-1 3-2 4-3s2-3 3-4z" class="O"></path><path d="M255 246l1-1v-1c1-2 3-3 4-4h1l-1 1c1 0 1 0 2 1l-2 2h-1l-1-1-1 1v1 1c0 1-1 2-1 3 0 0 0-1-1-1v-2z" class="I"></path><path d="M258 243h0c0-1 1-1 2-2 1 0 1 0 2 1l-2 2h-1l-1-1z" class="f"></path><path d="M255 248c1 0 1 1 1 1-2 6-4 11-6 16v4h0c0 2-1 4 1 5-1 3-1 6 0 8l-1 1c0 1-1 1-1 2h-1c0-1 0-2-1-3 2-3 1-9 1-12 0-2 1-4 1-6 1-3 2-6 3-8 1-3 3-5 3-8z" class="d"></path><path d="M265 238c1 1 2 2 2 4-1 1-1 2-3 2-1 2-2 3-3 5l-3 3c-3 4-4 10-5 15-1 2-2 5-2 7-2-1-1-3-1-5h0v-4c2-5 4-10 6-16 0-1 1-2 1-3v-1-1l1-1 1 1h1l2-2 1-2 1 1h1v2l1-1c0-1 0-1-1-2v-1-1z"></path><path d="M260 244c-1 2-2 3-3 5l1 1v-1l5-7 1-1v1c0 1-1 2-1 3-2 3-5 5-7 7-3 5-4 12-6 17v-4c2-5 4-10 6-16 0-1 1-2 1-3v-1-1l1-1 1 1h1z" class="i"></path><path d="M260 212c1-4 0-9 1-12 1-2 1-3 3-4 0 1 2 1 2 2 7 3 12 11 17 17l9 10 4 4v1c-2-1-2 0-3 0v-1h-2l-1-1h-2v-1h-3c-2 1-3 1-5 2h0c-1 1-1 1-1 2l-2 1-1 1-1 1c-2 2-6 5-7 8l-1 1v-1c0-2-1-3-2-4h0l-3-6-1-2c0-2 0-3-1-5v-13z" class="I"></path><path d="M268 208l2 1-4 1v-1l2-1z" class="c"></path><path d="M266 209v1c-1 0-2 1-4 2v-1c1-1 2-2 4-2z" class="S"></path><path d="M283 225c1-1 2-1 4-1h0c0 1-2 1-3 2l-1-1z" class="T"></path><path d="M273 207h1v1c-2 0-3 1-4 1l-2-1 1-1h4z" class="j"></path><path d="M275 213l1 1h2 0c0 1-2 1-3 2v-1h-2c1-1 1-1 2-1v-1z" class="W"></path><path d="M267 213c2-1 5-2 7-2 1 0 2 0 3 1h-1-3 0c-1 1-3 2-4 2 0-1-1-1-2-1z" class="T"></path><path d="M281 225h2l1 1c-2 0-4 1-5 1-2 0-3 0-4 1l-1-1c1-1 4-2 5-2h2z" class="S"></path><path d="M277 223c2-1 4-3 6-3v1c-1 1-2 1-4 2s-5 3-8 4l1-2 5-2z" class="a"></path><path d="M263 212c3-1 8-3 12-3v1c-2 1-5 1-7 1-2 1-4 3-6 3h-1 0l2-2z" class="S"></path><path d="M274 213h1v1c-1 0-1 0-2 1h2v1c-3 0-5 2-8 3-2 1-3 1-4 2l-1-1h1l1-1 7-4c0-1 1-1 3-2z" class="c"></path><path d="M266 225l11-4c1-1 2-1 3-2v1c0 1-2 1-4 2h0c-3 1-6 3-9 4-1 1-3 2-4 1v-1c1 0 2-1 2-1h1z" class="a"></path><path d="M279 223h1c1 0 1 0 2-1h2v1s-1 0-2 1h-1v1h0-2c-1 0-4 1-5 2-4 1-7 2-10 5h-1v-1c1-2 6-3 8-4 3-1 6-3 8-4z" class="i"></path><path d="M269 214c1 0 3-1 4-2l1 1c-2 1-3 1-3 2l-7 4v-1l1-1c1 0 2 0 2-1h0c0-1 0-1-1 0-1 0-2 1-4 1v-1-1l5-2c1 0 2 0 2 1z" class="l"></path><path d="M267 213c1 0 2 0 2 1l-7 2v-1l5-2z" class="S"></path><defs><linearGradient id="D" x1="262.774" y1="197.581" x2="266.482" y2="208.179" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#D)" d="M260 212c1-4 0-9 1-12 1-2 1-3 3-4 0 1 2 1 2 2h0c2 3 5 6 7 9h-4l-1 1-2 1c-2 0-3 1-4 2v1h0-2z"></path><path d="M262 232l-1-2c0-2 0-3-1-5v-13h2 1l-2 2h0 1v1 1 1c2 0 3-1 4-1 1-1 1-1 1 0h0c0 1-1 1-2 1l-1 1v1l-1 1h-1l1 1c1-1 2-1 4-2h0c2 0 4-1 5-2 2 0 5-1 6-2l1 1h1c-3 2-7 3-10 4-1 0-2 1-3 2 2-1 3-2 5-2 2-1 5-2 7-3l1 1c-2 2-9 4-12 5-1 1-2 1-2 2h-1s-1 1-2 1v1c1 1 3 0 4-1 3-1 6-3 9-4h0l1 1-5 2-1 2c-2 1-7 2-8 4v1h-1z" class="W"></path><path d="M262 232v-2c1-2 7-4 10-5l-1 2c-2 1-7 2-8 4v1h-1z" class="G"></path><path d="M278 228c4 0 7-3 11-3v1l-1 1h-3c-2 1-3 1-5 2h0c-1 1-1 1-1 2l-2 1-1 1-1 1c-2 2-6 5-7 8l-1 1v-1c0-2-1-3-2-4h0l-3-6h1 1c3-3 6-4 10-5l1 1c1-1 2-1 4-1l-1 1z" class="Y"></path><path d="M275 231h0l-8 6c-1 0-1 0-1-1 1-1 2-1 4-2s3-2 5-3z" class="G"></path><path d="M274 227l1 1c-2 1-5 1-6 2-2 0-3 1-4 2h-1 0c3-3 6-4 10-5z" class="H"></path><path d="M278 228c4 0 7-3 11-3v1l-1 1h-3c-2 1-3 1-5 2h0c-1 1-1 1-1 2l-2 1v-1h-2 0l-1-1-5 3h0c2-3 3-2 5-4 1 0 2-1 4-1z" class="a"></path><path d="M274 230h0c2-1 4-1 6-1-1 1-1 1-1 2l-2 1v-1h-2 0l-1-1z" class="W"></path><path d="M264 232v2h0c1 0 1 0 1-1 2-1 3-2 5-3-1 2-3 4-5 5v1h1c0 1 0 1 1 1l8-6h2v1l-1 1-1 1c-2 2-6 5-7 8l-1 1v-1c0-2-1-3-2-4h0l-3-6h1 1 0z" class="S"></path><path d="M275 231h2v1l-1 1-1 1c-2 0-3 1-5 2 0 1 0 1-1 1l-2 1v-1l8-6z" class="Y"></path><path d="M175 632l1 5h0l1 1 49 1h12 8 4 14 52 209-1-4v2h-37-308v-9z"></path><path d="M175 632l1 5h0l1 1 49 1h12 8-1-65l-1 1 1 1c1 0 2 0 3-1 2 0 5 0 8 1h3 8 41 170 46 16 8-308v-9z" class="O"></path><path d="M84 560c-20-29-22-66-21-100-1-1-2-2-2-3-8-10-10-25-8-38 2-11 10-23 20-30s23-10 35-7c10 1 18 6 24 14 4 6 5 14 5 21v8c-2 9-8 14-14 19h0 0v1c1 1 1 2 1 4-1 3-3 6-7 6-1 0-2-1-3 0v3c0 1-1 2-2 2l-2-1c-3-2-5-2-9-2-4 2-8 4-11 7-9 10-11 24-11 37l1 2 2 1v1c-1 2-1 4 0 6h-1c0 2 0 3 1 5 1 1 1 3 2 4l3 11c4 15 12 29 23 41l7 7c2 2 6 5 8 6 5 3 9 6 14 9 3 1 6 3 9 4 4 2 8 4 12 5s8 2 12 4c8 2 17 3 26 4 15 1 30 2 45-2 10-3 20-8 29-13 4-2 8-5 11-8 8-6 14-13 20-21 4-5 8-11 10-17 0 1 0 2-1 4l-5 10c1-1 2-2 3-4v1l-1 1v1 1c-10 18-25 31-42 40l-15 6c-16 4-32 6-49 6-2-1-4-1-6-1h-8c-4-2-8-2-12-3v1c-15-3-29-9-42-16-3-2-7-4-10-6-1 1-1 1-2 1l-4-3c-1 1-1 1-1 2-7-5-13-11-18-18-5-6-10-13-13-20-3-6-5-13-8-19 0 1 0 1-1 2 2 4 3 7 5 11s4 9 6 13h-1 0l-1-1-1-1h-1v1l-1 1z"></path><path d="M66 457c0 2 0 2 1 3v1c-1 0-1 0-2-1l1-3z" class="K"></path><path d="M100 562l3 4-1 1-3-4c1 0 1-1 1-1z" class="R"></path><path d="M88 451l2 1v1c-2 1-3 2-4 3 0-2 1-3 2-5z" class="M"></path><path d="M95 557c0-1 0-1 1-1 1 2 3 4 4 6 0 0 0 1-1 1l-4-6z" class="P"></path><path d="M118 587l7 4c-1 1-1 1-2 1l-4-3-2-1 1-1z" class="D"></path><path d="M75 528c0-1 1-1 1-1l3 7c0 1 0 1-1 2l-3-8z" class="C"></path><path d="M79 501l1 2 2 1v1c-1 2-1 4 0 6h-1c-1-3-2-6-2-10z" class="g"></path><path d="M74 518l2 9s-1 0-1 1c-1-2-2-5-2-7l1-1v-2z" class="P"></path><path d="M68 510h0 1v-1h0l2 13h0-1l-2-12z" class="J"></path><path d="M89 393c4 0 7-1 10 0h2v1h-4l-4 1h-2l-2-2z" class="D"></path><path d="M72 507l2 11v2l-1 1-2-11h1v-3z" class="C"></path><path d="M103 566l7 7c-2 1-2 1-3 0l-5-6 1-1z" class="F"></path><path d="M99 393c5 0 13 1 17 4h-1c-4-3-12-3-18-3h4v-1h-2z" class="U"></path><path d="M70 495h0c1 2 2 5 1 7v3 2c1-3 0-5 1-8v-2h0v10 3h-1l-1-15z" class="K"></path><path d="M68 447v-1l1 2h1l-1 3-2 9c-1-1-1-1-1-3 0-3 2-7 2-10z" class="J"></path><path d="M68 447v-1l1 2h1l-1 3h0v-1h-1v-3z" class="K"></path><path d="M67 461l-2 21h-1c0-7 0-15 1-22 1 1 1 1 2 1z" class="k"></path><path d="M73 403c5-6 9-8 16-10l2 2c-5 1-9 2-13 5-2 1-3 2-5 3z" class="R"></path><path d="M102 573v-1c1 1 1 0 1 1v-1l1-1c4 6 8 11 14 16l-1 1c-3-3-7-6-10-9-2-2-3-4-5-6z" class="B"></path><path d="M95 557c-2-2-2-5-4-8-2-5-4-11-6-17-1-2-1-5-1-7v1c2 8 6 15 9 23 1 2 2 4 3 7-1 0-1 0-1 1z" class="J"></path><path d="M110 573l11 10c-1 1-2 1-3 0l-1 1c-4-3-8-7-10-11 1 1 1 1 3 0z" class="U"></path><path d="M71 522c3 9 6 17 10 26 2 4 4 7 5 10h-1c-1-3-3-5-4-8l-6-13c-2-5-4-9-5-15h1 0z" class="E"></path><path d="M88 451c2-2 4-5 5-7 5-6 12-7 19-8-1 0-1 1-2 2-1 0-4 0-5 1-4 1-8 2-10 7 0 0-1 1-1 2l-2 2-2 2-2-1z" class="G"></path><path d="M101 573h1c2 2 3 4 5 6 3 3 7 6 10 9l2 1c-1 1-1 1-1 2-7-5-13-11-18-18h1z" class="V"></path><path d="M81 527l8 21h-1v1h-1 0l1 3-3-6c-2-5-5-11-6-17h1v2c1 2 2 6 4 9 0-1 0-1 1-1l-1-2h0v-2c-1-1-1-1-1-2l-1-1h0v-2c-1-1-1-2-1-3z" class="B"></path><path d="M85 546c1-1 1-1 1-2h1v1c0 1 1 2 1 3v1h-1 0l1 3-3-6z" class="R"></path><path d="M143 597l23 9h-1-3c-1-1-3-1-4-2-1 0-3-1-4-1-2-1-3-2-5-2v1l2 1h1l1 1h1c1 0 1 0 1 1 1 0 2 0 3 1h0 1c1 1 2 1 3 1 1 1 3 1 3 2-9-2-18-6-26-11 1-1 2-1 4-1z" class="B"></path><path d="M132 424v1c-1 0-1 0-1 1-2 6-8 12-14 15l-14 6h0v1h-2c0-2 2-2 4-3 6-3 14-6 19-12 3-2 5-6 8-9z" class="K"></path><path d="M69 398s1 0 1-1v2c-1 2-2 3-3 5-4 5-7 10-8 16s-1 13 1 19c1 3 1 5 2 8 1 0 2 2 1 3-2-4-4-10-5-14-2-13 1-24 8-34 1-1 3-2 3-4z" class="R"></path><path d="M91 395h2c-2 0-2 0-3 1v1c-2 0-2 0-3 1h1c-6 3-12 6-16 11l-2-2s0-1 1-2c0-1 1-2 2-2 2-1 3-2 5-3 4-3 8-4 13-5z" class="d"></path><defs><linearGradient id="E" x1="122.91" y1="580.903" x2="135.671" y2="599.775" xlink:href="#B"><stop offset="0" stop-color="#7c7d7f"></stop><stop offset="1" stop-color="#b5b4b3"></stop></linearGradient></defs><path fill="url(#E)" d="M121 583c3 2 5 4 8 6 4 3 9 5 14 8-2 0-3 0-4 1h0c-3-1-7-4-10-6-4-2-9-5-12-8l1-1c1 1 2 1 3 0z"></path><defs><linearGradient id="F" x1="100.526" y1="573.571" x2="89.433" y2="547.546" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#a8a8a9"></stop></linearGradient></defs><path fill="url(#F)" d="M89 548c2 3 3 6 5 9 2 4 5 8 7 11l3 3-1 1v1c0-1 0 0-1-1v1h-1c-5-6-10-14-13-21l-1-3h0 1v-1h1z"></path><path d="M65 399c0-2 3-4 5-6 1 1 3 1 4 1-1 1-2 2-4 3 0 1-1 1-1 1l-2 2h0c-4 5-8 11-10 19-2 4-2 9-2 13 1 7 5 15 8 21h-1c-3-4-8-18-8-23 0-7 2-13 4-19 2-4 4-8 7-12z" class="B"></path><path d="M65 399c0-2 3-4 5-6 1 1 3 1 4 1-1 1-2 2-4 3 0 1-1 1-1 1l-2 2h0c-2 0-3 2-4 4 0 0-1 0-1 1h0 0c1-2 3-4 3-6z" class="C"></path><defs><linearGradient id="G" x1="61.555" y1="488.708" x2="75.862" y2="484.189" xlink:href="#B"><stop offset="0" stop-color="#a7a7a8"></stop><stop offset="1" stop-color="#cac9ca"></stop></linearGradient></defs><path fill="url(#G)" d="M68 461c1 0 2 1 3 2-2 11-3 21-2 33v9 4h0v1h-1 0c-1-3-2-7-2-10-1-13 0-27 2-39z"></path><path d="M70 495c-1-14 1-27 4-40 2-6 4-12 7-16 1-1 2-2 4-2s4 1 6 2c0 3-4 8-6 11-1 2-3 5-4 8-1 4-3 7-4 11-3 9-5 18-5 28h0v2c-1 3 0 5-1 8v-2-3c1-2 0-5-1-7h0z" class="e"></path><path d="M70 393c8-7 19-11 30-11 10 0 20 3 27 10 6 6 9 14 8 22 0 4 0 8-1 12-2 6-9 12-15 16-2 1-6 2-9 2 2-1 5-2 8-3 5-2 11-8 13-14l1-2v-1-1c1-9 0-17-5-24s-15-11-24-12c-10-2-20 1-29 7-1 0-3 0-4-1z" class="g"></path><path d="M119 397c-3-2-5-3-7-3-7-3-13-5-21-3-9 1-18 6-23 15-3 4-6 10-7 16h0v7 8l1 2c-2-3-2-9-2-12 0-2 0-4 1-6h0c0-2 0-3 1-4 1-4 2-8 5-11 0-2 2-3 3-5s2-3 3-5c3-2 6-4 9-5 10-5 20-5 30-1 8 3 14 8 17 15 3 5 3 14 1 19-4 8-12 13-19 17-2 1-5 2-6 4-2 1-4 1-4 3h0l-1-1-1 1-4 2h0-3l2-2c0-1 1-2 1-2 2-5 6-6 10-7 1-1 4-1 5-1 1-1 1-2 2-2h1c2-1 3-2 5-4v1 2c3-4 6-6 9-11 2-4 2-10 1-15-2-5-5-10-9-12z" class="L"></path><path d="M112 436h1c2-1 3-2 5-4v1 2c-1 1-2 1-3 2-2 1-4 1-6 2-5 1-11 4-14 9l-1 1 1 1h0-3l2-2c0-1 1-2 1-2 2-5 6-6 10-7 1-1 4-1 5-1 1-1 1-2 2-2z" class="C"></path><path d="M307 564c1-1 2-2 3-4v1l-1 1v1 1c-10 18-25 31-42 40l-15 6c-16 4-32 6-49 6-2-1-4-1-6-1h-8c-4-2-8-2-12-3l-12-3c0-1-2-1-3-2-1 0-2 0-3-1h-1 0c-1-1-2-1-3-1 0-1 0-1-1-1h-1l-1-1h-1l-2-1v-1c2 0 3 1 5 2 1 0 3 1 4 1 1 1 3 1 4 2h3 1l12 4c3 0 7 1 10 1 14 2 28 3 41 1 4 0 8-1 12-2 9-2 18-6 26-10 9-5 18-11 25-19 6-5 10-11 15-17z" class="J"></path><path d="M103 448c2-1 5 0 8 1 2 0 6-1 8-1 2-1 2-3 3-3 1 2 1 3 0 4 0 2-1 3-3 4h-6v6c-4-2-8-5-13-4-8 3-12 9-16 16-7 14-7 33-5 49 1 2 2 5 2 7 0 1 0 2 1 3v2h0l1 1c0 1 0 1 1 2v2h0l1 2c-1 0-1 0-1 1-2-3-3-7-4-9v-2h-1c-2-5-4-11-4-16-2-11-2-24 0-35l3-9 3-6c1-3 3-5 5-7 1-1 2-2 4-3v-1l2-2h3 0l4-2 1-1 1 1h0 2z" class="e"></path><path d="M95 450l4-2 1-1 1 1-11 5v-1l2-2h3 0z" class="F"></path><path d="M68 446c2-7 4-14 8-19 4-7 10-11 18-13 5-1 13 0 17 3 3 2 5 5 5 8 0 2 0 3-1 4-2 2-5 2-8 3v-1c1-2 1-4 0-7-1-2-3-4-5-4-4-1-8 0-11 1v1h1c2 1 5 3 7 5 0 2 0 6-1 8-3 0-4-1-7-2-4-2-8 2-10 4-5 8-8 17-10 26-1-1-2-2-3-2l3-13c2-7 4-15 10-20 1-1 3-2 4-3h0l-3 1c-7 4-10 14-12 22h-1l-1-2z" class="g"></path><path d="M97 394c6 0 14 0 18 3h1 3c4 2 7 7 9 12 1 5 1 11-1 15-3 5-6 7-9 11v-2-1c0-3 1-6 0-8-2-4-4-7-8-8-7-4-14-4-22-1-7 3-14 10-17 17-1 4-2 7-3 10-2-3-3-7-3-11-1-9 1-15 7-22 4-5 10-8 16-11h-1c1-1 1-1 3-1v-1c1-1 1-1 3-1l4-1z" class="e"></path><path d="M88 398h-1c1-1 1-1 3-1h3l-5 1z" class="V"></path><path d="M97 394c6 0 14 0 18 3h1 3c4 2 7 7 9 12 1 5 1 11-1 15-3 5-6 7-9 11v-2l1-1c5-5 7-12 6-19 0-4-3-8-6-10-8-6-17-7-26-6h-3v-1c1-1 1-1 3-1l4-1z"></path><path d="M119 397c4 2 7 7 9 12-2-1-3-4-4-6l-3-3v1l-4-2-2-2h1 3z" class="d"></path><path d="M230 298l3-3c3-2 6-5 9-8l1-1c1-1 0-4 0-5 0-3 0-6 1-9h0v7 4c1 1 0 3 0 5h1 1l-1 2h0l1 2-1 1v1l1-1h1c-3 4-7 7-11 10-2 2-4 4-7 6-6 5-12 9-16 15-1 0-2 1-3 2l1 1h0c0 3-4 5-5 8-3 3-6 7-8 11 0 1-1 2-2 4l1 1c0 1 0 1 1 2-1 1-2 2-2 3 0 0-1 0-2 1 0 0 0 1-1 1v4l-9 21-1 4-9 28h0c-1 2-1 3-2 4l-2 6v1l1 1c0 1 0 1-1 2 0 2-1 3-1 5l3 1c1 2-1 5-1 7-1 3-1 5-2 7l-2 7c-1 7-3 14-3 21l1 16v6 1h0c1 1 1 2 1 4l1-1c0-1 0-1 1-2v-1l1-4h1v1h0l1 1s1-1 1-2c0-2 1-4 2-6s1-5 2-7c2-3 4-6 6-10 1-2 2-3 3-5 2-3 4-6 7-9h0c0-1-1-1-1-2l1-2h-1l1-3h0c0-1 1-1 2-2l1-1c-1-1-1-1 0-2 0-1 1-1 2-2s1-1 1-2l3-3h2v-1l4-1h1 0c0 1 0 1 1 1l1 1 3-1c0-1 1-1 2-2s2-1 4-2v1c0 1 0 2 1 3 7-5 16-6 25-7v1h14 0 2l2 1 1-1 8 2h2 1c1 1 2 1 3 1v1c1 0 0 0 1-1l2 2h2 0c1 1 1 2 1 3h1v3h1c-1 1-2 1-2 1s1 0 2 1c1 0 1 1 2 1 3 2 6 3 7 6l2 1 3 4c1 1 2 3 3 4 0-2-1-2-2-3h0l1-1c11 14 15 33 16 50v22l-1 2-2 11h-1c-1 2-1 5-1 7-2 6-6 12-10 17-6 8-12 15-20 21-3 3-7 6-11 8-9 5-19 10-29 13-15 4-30 3-45 2-9-1-18-2-26-4-4-2-8-3-12-4s-8-3-12-5c-3-1-6-3-9-4-5-3-9-6-14-9-2-1-6-4-8-6l-7-7c-11-12-19-26-23-41l-3-11c-1-1-1-3-2-4-1-2-1-3-1-5h1c-1-2-1-4 0-6v-1-4c0-5 3-11 5-16 4-7 10-14 18-17 7-2 15-1 21 2 7 3 9 8 11 14l1-9c1-1 1-2 2-3l1-5 2-12h0v-4c2-11 5-22 8-32l5-11c15-42 40-81 74-109z"></path><path d="M145 512v1 1h-1l-1-1 2-1z" class="D"></path><path d="M149 518v-1l1 1v1-1-1h1v2 1l-1 1-1-3z" class="R"></path><path d="M150 529l1-1 2 3h-2l-1-2z" class="D"></path><path d="M140 553l3 2c-1 1-1 1-2 1l-1-1v-1-1h0z" class="B"></path><path d="M162 538l1 3h-1s-1-2-2-2l2-1z" class="M"></path><path d="M262 533l1 1c0 1 0 3-1 4l-1-2v-1c1 0 1-1 1-2z" class="O"></path><path d="M236 558c1 0 1 0 1 1l1 1c-1 2-1 3-2 5v-7z" class="N"></path><path d="M151 519l1 2c0 1 0 2-1 3 0-1-1-2-1-3l1-1v-1z" class="F"></path><path d="M135 494l2 1v5l-1-1h0l-1-5z" class="d"></path><path d="M154 562l3 1h0l-1 1h0v1c0-1-1-1-1-1l-1 1-1-1c0-1 0-2 1-2z" class="E"></path><path d="M260 541c1 1 0 4 1 5-1 2-2 3-4 4 2-3 3-5 3-9z" class="c"></path><path d="M227 563c0 2 0 4-1 5h-1l-1-3c1-1 2-1 3-2z" class="a"></path><path d="M152 521l2 4v3l-3-4c1-1 1-2 1-3z" class="U"></path><path d="M141 496l1 1c-1 2 0 4 0 6h-1 0c0 1 0 1-1 2v-7s0 1 1 1v-3z" class="d"></path><path d="M143 492v8h-1v3h0c0-2-1-4 0-6h0v-3c0-1 0-1 1-2zm66 57c1 1 2 1 3 1h0l2 3c-1 1-2 1-3 1l-2-5z" class="D"></path><path d="M98 516v3c1 0 1 0 1-1v2l1 5h-1 0c-1-1-2-3-2-4v-1c1-1 1-2 1-4z" class="I"></path><path d="M208 544c1 1 1 2 3 3l-1-1 1-1c1 2 1 3 1 5h0c-1 0-2 0-3-1l-1-5z" class="E"></path><path d="M170 544c1 1 2 3 3 4 0 1 0 1 1 1h1l2 5c-2-1-4-4-5-6l-2-3v-1z" class="h"></path><path d="M139 506l1-1 1 6c0 1 1 3 1 3l-1 2h0l-2-10z" class="H"></path><path d="M209 553l3 12h-1l-4-7h2 0v-5z" class="M"></path><path d="M241 549v1c1 2 1 3 1 5h0v-1l1-1h0 0c1 2-2 8-3 10 0-2 1-5 1-7v-7z" class="H"></path><path d="M112 499c0-2 1-5 2-7 0-2 2-3 3-5l-1 3c-2 4-3 9-3 13 0-2 0-3-1-4h0z" class="J"></path><path d="M193 574l9 1h-3l2 1h-2 1c-2 0-3 1-4 0h-3c-1-1-2-1-3-2h3zm-14-77l1 1-3 8v3c-1 0-1-1-2-1l1-1v-1l3-9z" class="L"></path><path d="M176 506v-1c1 0 1-1 1-1h1l-1 1v1 3c-1 0-1-1-2-1l1-1v-1z" class="b"></path><path d="M260 536h1l1 2c0 3 0 5-1 8-1-1 0-4-1-5v-1-4z" class="T"></path><path d="M145 494v-3l1 6v-3h1v8 2h-1v-1l-1-9z" class="E"></path><path d="M234 541c2 0 2 0 3 2l1-1v1 2h-1v1s1 2 0 3h-1l-2-8z" class="U"></path><path d="M238 542v1 2h-1v-2l1-1z" class="E"></path><path d="M232 553c1 0 1-1 2 0v1 10l-1 1v-5c0-3-1-5-1-7zm-57-19h0l1 2v-1h1l2 10-1 1c-2-4-3-8-3-12z" class="H"></path><path d="M247 547h0v4h2c-1 4-2 7-5 10 0 0 1-2 1-3 2-3 2-7 2-11z" class="G"></path><path d="M171 384l2 1-4 10c0 1 0 0-1 0 1 0 1-1 1-2h0c1-1 1-1 1-2v-2l-2 6h-1l4-11z" class="D"></path><path d="M142 503v-3h1l2 12-2 1c0-3-1-7-1-10z" class="F"></path><path d="M251 526v-1c1 4 2 8 2 12l-2-2c-1-1 0-3-1-4v-1-3c0-1 1-1 1-1h0z" class="N"></path><path d="M162 541h1l11 16h0c-1-1-2-1-2-2l-10-14z" class="D"></path><path d="M108 503c1-6 3-14 8-17v-1l-3 6c-1 1-1 2-1 3-1 3-2 6-2 9-1 1-1 1-2 0z" class="B"></path><path d="M235 569l4-2c2-1 4-3 7-2v1l-6 3-3 1h0c-1-1-1-1-2-1z" class="d"></path><path d="M247 525c2 0 1-2 2-3 1 0 1 0 2-1v5h0s-1 0-1 1v3h-2l-1-5z" class="E"></path><path d="M172 513v7h-1v3c-1-1-1-2-1-3h-1v-5l2-2h1z" class="a"></path><path d="M188 549c1 2 2 5 4 8l5 7c0 1 1 2 2 4-1-2-3-3-4-5-2-4-7-9-8-13l1-1z" class="M"></path><path d="M201 541l2 9c0 2 0 2-1 3-2-3-3-6-4-9 1 0 1 1 1 1l1 1 1-1-1-1c1-1 1-2 1-3z" class="R"></path><path d="M199 553h0c2 4 4 8 6 11 1 2 4 4 4 6-3-1-9-12-11-15v-1c1 0 1-1 1-1z" class="D"></path><path d="M235 529l3 13-1 1c-1-2-1-2-3-2l-3-11c2 2 3 5 3 7h0c1 1 2 1 2 2h0c1 0 0-1 0-2v-2c-1 0-1 0-1-1v-1-4z" class="B"></path><path d="M99 520h2l2 16c1 1 1 3 1 4-3-5-4-10-4-15l-1-5z" class="a"></path><path d="M172 569l21 5h-3c1 1 2 1 3 2l-2-1h-1-1c-1-1-2-1-2-1h-3c0-1-1-1-2-1s-3-1-4-1l-5-2h-1v-1z" class="C"></path><path d="M143 466c1-7 2-15 5-22l-4 22h0-1v1-1z" class="U"></path><path d="M99 518c1-6 1-12 3-17 0-2 0-4 1-5l-2 24h-2v-2z" class="S"></path><path d="M179 491h1c0 1 0 1-1 2l1 1c0 1-1 2-1 3l-3 9v1l-1-3c1-3 1-7 3-11h0c0-1 0-1 1-2z" class="k"></path><path d="M179 491h1c0 1 0 1-1 2l-1 2h0v-2h0c0-1 0-1 1-2z" class="J"></path><path d="M183 534c0 1 1 1 1 2h1s0-1 1-1c0 5 1 9 2 14l-1 1c-2-6-3-11-4-16z" class="B"></path><path d="M145 513l3 9c1 2 3 4 3 6l-1 1c-3-3-6-10-6-15h1v-1z" class="H"></path><path d="M139 506c-1-6-1-12-1-18 1-2 0-5 1-7 1 1 1 14 1 17v7h0l-1 1z" class="G"></path><defs><linearGradient id="H" x1="234.4" y1="555.849" x2="240.458" y2="548.471" xlink:href="#B"><stop offset="0" stop-color="#7f7c7e"></stop><stop offset="1" stop-color="#8e9190"></stop></linearGradient></defs><path fill="url(#H)" d="M238 543c1 6 1 11 0 17l-1-1c0-1 0-1-1-1v-9h1c1-1 0-3 0-3v-1h1v-2z"></path><path d="M154 525c3 3 4 6 6 9 0 1 2 3 2 4l-2 1c0-1-1-2-1-3-2-2-4-5-5-8v-3z" class="H"></path><path d="M179 545c3 7 6 14 12 19 1 1 2 1 3 2l3 3c-1-1-3-3-5-4-2 0-3-2-4-3-4-4-8-9-10-15v-1l1-1z" class="U"></path><path d="M214 553c1 4 2 8 2 12 1 2 1 3 1 5-2-4-3-7-5-11 0-2-1-3-1-5 1 0 2 0 3-1z" class="H"></path><path d="M147 502l4 15h-1v1 1-1l-1-1v1c-1-1-1-3-1-4-1-3-2-7-2-11v1h1v-2z" class="D"></path><path d="M235 569c1 0 1 0 2 1h0l3-1 1 1-1 1h-1-1c-1 1-2 1-3 1l-1 1h-1-1 0c-1 1-1 1-2 1 0 1-1 1-2 1h-1l-4 1h0l1-1c1 0 0 0 1-1 1 0 1 1 2 0-2 0-4 1-6 0l13-3c0-1 0-1 1-2z" class="L"></path><path d="M235 569c1 0 1 0 2 1h0l-3 1c0-1 0-1 1-2z" class="I"></path><path d="M143 555c1 1 2 1 2 2l9 5c-1 0-1 1-1 2l-4-2-1-1h-1c-1 0-1-1-2-2h0l-1-1c-1 0-2-1-3-2 1 0 1 0 2-1z" class="R"></path><path d="M143 555c1 1 2 1 2 2v1c0-1-1-1-1-1h0l1 1h0v1l-1-1c-1 0-2-1-3-2 1 0 1 0 2-1z" class="E"></path><path d="M203 550c2 4 3 9 5 12l3 3h1l3 6c-3-2-5-6-8-9-2-3-4-5-5-9 1-1 1-1 1-3z" class="G"></path><path d="M203 544c1 1 2 3 3 4h0v-3l3 8v5h0-2c-1-5-3-9-4-14z" class="D"></path><path d="M173 496c1 2 1 2 2 3v3l-2 5-1 6h-1c0-2-1-4-1-5 0-2 1-4 1-6 1-2 2-4 2-6z" class="H"></path><path d="M173 507h-1c0-2 0-4 1-6h1v1-1l1 1-2 5z" class="B"></path><path d="M158 522c1 3 3 5 4 7 2 3 3 7 5 11 1 1 2 2 3 4v1l-2-2-3-5c-3-4-6-10-8-14v-1h1v-1zm-5 9c2 4 5 9 8 13 1 2 3 3 4 5l3 3c2 3 3 4 5 7-9-8-16-18-22-28h2z" class="M"></path><path d="M242 535c0 1 1 3 1 5 1 4 2 9 0 13h0 0l-1 1v1h0c0-2 0-3-1-5v-1l-1-7h1v-3c1-1 0-2 0-2l1-2z" class="N"></path><path d="M242 535c0 1 1 3 1 5v3h0l-2-4c1-1 0-2 0-2l1-2z" class="J"></path><path d="M182 483l1 1h0l-1 3c-1 1-2 2-2 4h-1c-1 1-1 1-1 2-2 1-2 4-3 6-1-1-1-1-2-3l2-6h2 1 0l1-2v-1c1-1 1-3 3-4h0z" class="E"></path><path d="M182 483l1 1h0l-1 3c-1 1-2 2-2 4h-1l1-4c1-1 1-2 2-4h0z" class="e"></path><path d="M97 520c-1-4-1-10 0-15 2-7 5-15 12-19-2 2-4 4-5 6-5 7-6 16-6 24 0 2 0 3-1 4z" class="G"></path><path d="M221 574c2 1 4 0 6 0-1 1-1 0-2 0-1 1 0 1-1 1l-1 1h0c-1 0-3 0-5 1h-4c-4 0-9 0-13-1h-1-1 2l-2-1h3l19-1z" class="e"></path><defs><linearGradient id="I" x1="200.02" y1="552.13" x2="193.109" y2="542.141" xlink:href="#B"><stop offset="0" stop-color="#8b8e8c"></stop><stop offset="1" stop-color="#acabac"></stop></linearGradient></defs><path fill="url(#I)" d="M195 526h0c1 4 0 8 1 12 0 5 2 10 3 15 0 0 0 1-1 1-2-3-3-6-4-9 0-1-1-3-1-4h0 1v-2c2-3 1-9 1-13h0z"></path><path d="M193 541l2 1c0 1 0 2-1 3 0-1-1-3-1-4z" class="E"></path><path d="M190 545c0-1 0-2 1-2h0c0 1 0 1 1 1h0c2 7 5 13 9 19 2 2 4 5 6 7-8-5-14-16-17-25z" class="F"></path><path d="M126 543c1-3-1-3-1-5h1l14 15h0v1 1l-1-1c-1 0-1-1-2-1-3-1-5-5-7-7-2-1-3-2-4-3z" class="J"></path><defs><linearGradient id="J" x1="251.522" y1="548.184" x2="250.397" y2="531.247" xlink:href="#B"><stop offset="0" stop-color="#757575"></stop><stop offset="1" stop-color="#949494"></stop></linearGradient></defs><path fill="url(#J)" d="M248 530h2v1c1 1 0 3 1 4l2 2c0 6-1 13-3 18v-6c1-7 0-13-2-19z"></path><path d="M103 505l2-10c0-2 2-4 3-5l2-2c2-2 5-5 9-5h0-1-1c-2 1-6 5-7 7v1c0 2 0 3-1 5l-1-1-3 7h-1v3h-1z" class="c"></path><path d="M104 502c2-5 3-9 6-13 0 2-1 3-1 5l-1 1-3 7h-1z"></path><defs><linearGradient id="K" x1="140.235" y1="480.773" x2="144.57" y2="481.607" xlink:href="#B"><stop offset="0" stop-color="#7a787a"></stop><stop offset="1" stop-color="#999b99"></stop></linearGradient></defs><path fill="url(#K)" d="M143 466v1-1h1 0l-1 26c-1 1-1 1-1 2v3h0l-1-1v-6-17c1-2 1-5 2-7z"></path><path d="M226 520v4h0v-3l2-1v1 3c1-1 1-6 1-7h0l1 14v4 1h-2 0c0-2-1-2-2-3v-1c-1-2-1-10 0-12z" class="C"></path><defs><linearGradient id="L" x1="232.873" y1="552.146" x2="229.549" y2="536.97" xlink:href="#B"><stop offset="0" stop-color="#818081"></stop><stop offset="1" stop-color="#adacad"></stop></linearGradient></defs><path fill="url(#L)" d="M230 531c2 7 4 15 4 23v-1c-1-1-1 0-2 0-1-3-1-6-2-8-1-4-3-8-4-12 1 1 2 1 2 3h0 2v-1-4z"></path><path d="M177 528c1 0 2 2 3 2v1h0l1-1v1c1 4 1 8 2 12 2 8 7 13 11 20l-1 1c-2-3-4-6-5-8-6-9-10-18-11-28z" class="D"></path><path d="M264 537c1-1 0-3 1-4v1h1v2c1 2 0 4 1 6v2c-2 2-3 6-6 9-2 3-4 5-8 7 1-2 2-3 3-4 1-2 3-3 4-5 3-4 4-8 4-14z" class="G"></path><path d="M266 536c1 2 0 4 1 6v2c-2 2-3 6-6 9 0-4 5-8 5-12-1-1 0-3 0-5zm-64-29c0 3-1 6-1 9 0 6-1 12 0 17 0 4 1 7 2 11 1 5 3 9 4 14l4 7-3-3c-2-3-3-8-5-12l-2-9c-2-11-3-23 1-34z"></path><defs><linearGradient id="M" x1="226.347" y1="561.861" x2="218.191" y2="546.155" xlink:href="#B"><stop offset="0" stop-color="#757575"></stop><stop offset="1" stop-color="#b2b1b2"></stop></linearGradient></defs><path fill="url(#M)" d="M217 545c1 0 1 1 2 1h0c1 0 1-1 2-1v3l1-1c1 5 5 11 5 15v1c-1 1-2 1-3 2l-1-2-6-18z"></path><path d="M227 562v1c-1 1-2 1-3 2l-1-2c1 0 2 0 4-1z" class="G"></path><defs><linearGradient id="N" x1="250.801" y1="548.983" x2="243.168" y2="533.068" xlink:href="#B"><stop offset="0" stop-color="#7b7c7b"></stop><stop offset="1" stop-color="#a8a6a8"></stop></linearGradient></defs><path fill="url(#N)" d="M241 522c1 2 1 2 1 4 1 2 2 4 2 6h3c-1-1-1-1-1-2h0v-2c-1 0 0-1-1-2s0-2 0-3v-1c3 10 5 19 4 29h-2v-4h0c-1-4-1-8-2-11-2-5-4-9-4-14z"></path><path d="M108 504v-1c1 1 1 1 2 0 0 10 2 18 7 26l-1 1h-1v1l-3-7c-1-2-1-4-3-5v-1c-1-3-1-6-2-9v-6l1 1h0z" class="E"></path><path d="M109 519v-1c-1-3-1-6-2-9v-6l1 1h0c0 2 1 4 1 6 0 3 0 6 1 8l5 12v1l-3-7c-1-2-1-4-3-5z" class="G"></path><defs><linearGradient id="O" x1="173.231" y1="524.062" x2="176.411" y2="523.425" xlink:href="#B"><stop offset="0" stop-color="#828282"></stop><stop offset="1" stop-color="#999a9a"></stop></linearGradient></defs><path fill="url(#O)" d="M173 518c0-5 0-9 2-14l1 3-1 1c1 0 1 1 2 1 0 1-1 2-1 3v3 12c0 2 1 5 1 8h-1v1l-1-2h0c-2-5-2-11-2-16z"></path><path d="M175 508c1 0 1 1 2 1 0 1-1 2-1 3v3s0 1-1 1v-3-3-2z" class="J"></path><path d="M175 508c1 0 1 1 2 1 0 1-1 2-1 3h0l-1-2v-2z" class="k"></path><path d="M173 518c0-5 0-9 2-14l1 3-1 1v2 3l-1 1v-2c-1 2 0 5-1 6z" class="M"></path><path d="M117 529c2 3 3 9 6 11 1 0 1 1 2 2 0 0 0-1 1 0v1c1 1 2 2 4 3 2 2 4 6 7 7 1 0 1 1 2 1l1 1 1 1c1 1 2 2 3 2l1 1h0c-1 0-2 0-3-1l-4-3-1-1c-1 0-1-2-2-2-3-2-5-5-7-7-1-1-2-2-3-2h0l1 4h0-1s-1-1-1-2l-1 1-2-3-3-3 1-1c0-1-1-2-2-3s-2-3-2-5v-1h1l1-1z" class="D"></path><path d="M121 543h0c1 0 1 1 2 1l1 1-1 1-2-3z" class="F"></path><path d="M172 520c0 15 2 31 12 42l3 3h0c-4-3-7-7-10-11l-2-5c-4-7-4-16-6-24v-5h1c0 1 0 2 1 3v-3h1z" class="N"></path><path d="M169 520h1c0 1 0 2 1 3v1l-1-1v1s0 1-1 1v-5z" class="M"></path><path d="M191 525c1 0 2 0 2-1 1-1 1-2 1-3s0-2 1-3v-1c0-1 0-2 1-3 0-1-1-2 0-3v-1-1l1-3 1-1v-1c0-1 1-1 1-1v-1l1-1c0 1 0 2-1 3v1h0l-1 1-1 3v2l-1 3v7 3c-1 1-1 1-1 2h0c0 4 1 10-1 13v2h-1c-1-3-1-6-2-9v-7z" class="K"></path><path d="M252 524v-3h1c3 2 4 9 4 13h0v1c1 6 0 12-3 18-1 1-3 5-4 5v-1c2-2 3-5 4-7v-6c1-6 0-13-1-20h-1z" class="N"></path><defs><linearGradient id="P" x1="219.296" y1="567.935" x2="213.758" y2="539.464" xlink:href="#B"><stop offset="0" stop-color="#676767"></stop><stop offset="1" stop-color="#babbbb"></stop></linearGradient></defs><path fill="url(#P)" d="M215 538c0 4 1 8 2 12 2 5 3 9 4 13 0 2 1 4 1 6h-1s-1 0-1-1l-9-30c1 1 2 1 3 1l1-1z"></path><defs><linearGradient id="Q" x1="237.1" y1="534.36" x2="239.455" y2="519.046" xlink:href="#B"><stop offset="0" stop-color="#b3b1b3"></stop><stop offset="1" stop-color="#d2d3d2"></stop></linearGradient></defs><path fill="url(#Q)" d="M235 513v1c0 1 0 3 1 4l2 2c0-1 0-3 1-4 0 6 2 12 3 19l-1 2s1 1 0 2v3h-1l-2-6c-1-4-2-9-3-13v-10z"></path><path d="M238 536h1v-3h1v1c1 1 1 2 1 3 0 0 1 1 0 2v3h-1l-2-6z" class="F"></path><defs><linearGradient id="R" x1="232.964" y1="563.325" x2="222.207" y2="542.116" xlink:href="#B"><stop offset="0" stop-color="#727272"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#R)" d="M225 534c3 10 7 21 5 32h0-1c0-2 0-4-1-7-1-8-6-15-7-24 1 2 1 4 2 7l3-2v-1c-1 0-1-1-1-1v-4z"></path><path d="M264 517h1c1 1 2 3 2 5h1c1 1 1 2 1 4l1 1v2 1 1c0 3 0 6-1 8l-1 1s0 1-1 1v3-2c-1-2 0-4-1-6v-2h-1v-1c-1 1 0 3-1 4v-10l-1-5h1v-3h1c-1-1-1-1-1-2z" class="W"></path><path d="M267 522h1c1 1 1 2 1 4l1 1v2 1 1c0 3 0 6-1 8-1-1-1-4 0-6 0-4-1-7-2-11z"></path><path d="M264 519h1c3 6 2 16 2 23-1-2 0-4-1-6v-2h-1v-1c-1 1 0 3-1 4v-10l-1-5h1v-3z" class="V"></path><path d="M263 522h1c1 3 2 9 2 12h-1v-1c-1 1 0 3-1 4v-10l-1-5z" class="S"></path><path d="M230 298l3-3c3-2 6-5 9-8l1-1c1-1 0-4 0-5 0-3 0-6 1-9h0v7 4c1 1 0 3 0 5h1 1l-1 2-2 2-2 2-4 3h-1c-1 1-3 2-4 3h0c-5 3-9 8-14 11l1-1h-1c2-2 3-4 5-5 3-2 5-5 8-7h-1z" class="V"></path><path d="M108 495l1 1c-1 2-1 5-2 7v6c1 3 1 6 2 9v1c0 1 0 4-1 5v2l-2-2-2-1v-4c-1-2-1-4-1-5l-1-1v-3c0-2 1-4 1-5h1v-3h1l3-7z" class="L"></path><path d="M104 502h1v8l-1 3h0v-2h0l-1-1h-1c0-2 1-4 1-5h1v-3z" class="I"></path><path d="M103 505h1v6h0l-1-1h-1c0-2 1-4 1-5z" class="H"></path><path d="M102 510h1l1 1h0v2h0l1-3c0 5 0 10 1 14l-2-1v-4c-1-2-1-4-1-5l-1-1v-3z" class="Q"></path><path d="M102 510h1l1 1v8c-1-2-1-4-1-5l-1-1v-3z" class="U"></path><path d="M270 531h1c0 7-1 16-6 22-2 0-3 1-4 2l2 1v2s-1 1-2 1l-2 2h0c-2 1-4 3-5 3-2 1-4 3-6 3l-1-1h-1v-1c2-1 5-3 7-5 4-2 6-4 8-7 3-3 4-7 6-9v-3c1 0 1-1 1-1l1-1c1-2 1-5 1-8z" class="L"></path><defs><linearGradient id="S" x1="110.245" y1="524.855" x2="124.356" y2="510.246" xlink:href="#B"><stop offset="0" stop-color="#a7a9a9"></stop><stop offset="1" stop-color="#dedbdd"></stop></linearGradient></defs><path fill="url(#S)" d="M112 499h0c1 1 1 2 1 4 1 8 3 16 7 24 2 4 4 7 6 11h-1c0 2 2 2 1 5v-1c-1-1-1 0-1 0-1-1-1-2-2-2-1-3-3-6-5-9-5-10-8-21-6-32z"></path><path d="M141 516l1-2c3 13 8 24 17 33l12 12c-2-1-4-2-5-3-2-3-5-4-7-7-3-3-6-7-9-11h-1-1c-1-1-1-1-1 0-2-2-3-5-4-8-1-4-3-7-4-11v-2l2-1h0z" class="D"></path><defs><linearGradient id="T" x1="145.189" y1="516.398" x2="143.266" y2="536.823" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242427"></stop></linearGradient></defs><path fill="url(#T)" d="M139 517l2-1h0c1 8 5 15 9 22h-1-1c-1-1-1-1-1 0-2-2-3-5-4-8-1-4-3-7-4-11v-2z"></path><defs><linearGradient id="U" x1="189.778" y1="512.995" x2="175.552" y2="503.52" xlink:href="#B"><stop offset="0" stop-color="#a5a4a4"></stop><stop offset="1" stop-color="#d0d0d1"></stop></linearGradient></defs><path fill="url(#U)" d="M185 489h0 1 0 1 0v2l-1 1c-5 12-6 26-5 38l-1 1h0v-1c-1 0-2-2-3-2v-4c0-11 1-20 5-30 1-1 1-3 3-5z"></path><path d="M138 518l1 1c1 4 3 7 4 11 1 3 2 6 4 8 0-1 0-1 1 0h1 1c3 4 6 8 9 11 2 3 5 4 7 7-1 0-1 1-1 1 0 1 1 1 2 2l2 1 3 2 2 1h-1l-7-4c-3-1-5-3-7-5-3-2-6-5-8-7l-1-2c-1-2-4-5-5-8-3-5-6-12-7-18v-1z" class="W"></path><path d="M147 538c0-1 0-1 1 0h1s-1 0-1 1c1 2 3 3 3 5-2-2-3-4-4-6z" class="I"></path><path d="M149 538h1c3 4 6 8 9 11 2 3 5 4 7 7-1 0-1 1-1 1-6-3-10-8-14-13 0-2-2-3-3-5 0-1 1-1 1-1z" class="d"></path><path d="M171 384l12-23c2-4 5-9 8-14 8-12 17-24 28-34l8-6 6-6c4-3 7-5 11-8v1c-3 3-7 5-10 8l-5 5-8 6c-6 5-12 11-17 18-1 1-2 3-4 4-5 7-9 15-14 23-5 9-9 18-13 27l-2-1z" class="M"></path><path d="M252 519v1h1s1 1 2 1c-1-1-1-1-1-2l3-3h1 0 1 5v1c0 1 0 1 1 2h-1v3h-1l1 5h-1v7l-1-1c0 1 0 2-1 2v1h-1v4c-1-2 0-3-1-4l-1-2-1 1v-1h0c0-4-1-11-4-13h-1v3h0l-1 1v1-5s0-1 1-2z" class="d"></path><path d="M255 521v-2l2-2 1 1-1 1v1h-1l-1 1z" class="f"></path><path d="M261 532v3 1h-1v-3l1-1z" class="G"></path><path d="M258 518l1 3-1-1h0 0v2l-1 1h0l-2-2 1-1h1v-1l1-1z" class="W"></path><path d="M258 526h1c1 1 1 2 2 4v2l-1 1c-1-2-1-5-2-7z" class="H"></path><path d="M257 523l1-1v-2h0 0l1 1c1 3 1 6 2 9-1-2-1-3-2-4h-1s-1-2-1-3z" class="a"></path><path d="M264 516v1c0 1 0 1 1 2h-1v3h-1l1 5h-1v7l-1-1v-7c-1-2-1-4-2-6 0-1-1-2-1-3h2c1-1 2-1 3-1z" class="Y"></path><path d="M264 516v1c0 1 0 1 1 2h-1v3h-1l1 5h-1c-1-1-1-5-1-7 0-1-1-2-1-3 1-1 2-1 3-1z" class="Q"></path><path d="M263 522v-3h1v3h-1z" class="O"></path><defs><linearGradient id="V" x1="225.551" y1="526.969" x2="212.708" y2="523.46" xlink:href="#B"><stop offset="0" stop-color="#adacac"></stop><stop offset="1" stop-color="#e1e1e1"></stop></linearGradient></defs><path fill="url(#V)" d="M217 505h1c1 0 1-1 1-1h1l1 1h0l-2 6v1 2h0c-1 2-1 4-1 6 0 6 1 12 2 18 1 3 2 6 2 9l-1 1v-3c-1 0-1 1-2 1h0c-1 0-1-1-2-1h0c-1-3-1-6-2-9-1-10-2-22 2-31z"></path><path d="M218 311c5-3 9-8 14-11h0c-9 8-19 16-27 26-9 10-16 22-23 34s-13 25-18 39c-3 8-6 16-7 24 0-1-1-2-1-3 0-2 1-5 2-8l9-24c3-8 8-17 12-25 4-7 8-15 12-21 5-7 10-13 16-19 3-4 7-9 11-12z" class="E"></path><defs><linearGradient id="W" x1="212.641" y1="530.785" x2="191.075" y2="512.647" xlink:href="#B"><stop offset="0" stop-color="#bab9ba"></stop><stop offset="1" stop-color="#e6e5e5"></stop></linearGradient></defs><path fill="url(#W)" d="M203 499c1-1 1-2 2-3h1v1c-1 2-1 3-2 5-1 1-1 3-2 4v1h0c-4 11-3 23-1 34 0 1 0 2-1 3l1 1-1 1-1-1s0-1-1-1c-1-1-2-17-2-20v-3-7l1-3v-2l1-3 1-1h0v-1c1-1 1-2 1-3h0c1 0 1-1 1-1 1-1 1-1 2-1z"></path><path d="M203 499c1-1 1-2 2-3h1v1c-1 2-1 3-2 5h-1l1-1v-1-1s1 0 1-1l-1 1h-1z" class="e"></path><defs><linearGradient id="X" x1="147.329" y1="458.775" x2="151.46" y2="459.255" xlink:href="#B"><stop offset="0" stop-color="#858585"></stop><stop offset="1" stop-color="#a4a3a4"></stop></linearGradient></defs><path fill="url(#X)" d="M145 494c-1-3 0-5 0-8 0-10 1-20 3-30l3-21c1-5 3-10 5-15 0 1 1 2 1 3-4 14-6 27-8 40-1 11-2 21-2 31h-1v3l-1-6v3z"></path><path d="M156 565v-1h0l1-1 4 2c4 1 8 3 11 4v1h1l5 2c1 0 3 1 4 1s2 0 2 1h3s1 0 2 1h1 1l2 1h3c1 1 2 0 4 0h1c4 1 9 1 13 1h4c2-1 4-1 5-1l4-1h1c-1 1-2 1-3 1s-2 1-3 1l-4 1h-4-2-7c-2 0-3 0-4 1h0 0v1h-2c-1-1-1 0-2 0-2-1-3-1-5-1l-7-2h-1-1c-1 0-2 0-3-1h-2l1-1h0-1c-1-1-1-1-2-1h-1l-2-1c-2-1-3-1-5-2l-3-1 1-1h-4v-1h-2v-1l-3-1-1-1h-2l1-1s1 0 1 1z" class="F"></path><path d="M156 565v-1h0l1-1 4 2c0 1 2 1 2 2-1 1-2 0-3 0h0l-1-1c-1 0-1 0-1-1h-2z" class="B"></path><path d="M166 569c1 1 1 1 2 1h1l5 2h1c1 0 1 0 2 1h1 2c-2 1-3 1-4 1h-1l-2-1c-2-1-3-1-5-2l-3-1 1-1zm28 8h3c1 1 4 0 5 0 3 1 7 1 10 1h-7c-2 0-3 0-4 1h0 0v1h-2c-1-1-1 0-2 0-2-1-3-1-5-1v-2h2z" class="L"></path><path d="M180 573l1 1h2s1 1 2 0v1h2s1 0 2 1h2l3 1h-2v2l-7-2h-1-1c-1 0-2 0-3-1h-2l1-1h0-1c-1-1-1-1-2-1 1 0 2 0 4-1z" class="K"></path><path d="M126 547h0l-1-4h0c1 0 2 1 3 2 2 2 4 5 7 7 1 0 1 2 2 2l1 1 4 3c1 1 2 1 3 1 1 1 1 2 2 2h1l1 1 4 2 1 1h2l1 1 3 1v1h2v1h4l-1 1s-1 0-2 1v1h-1c-3-1-6-3-8-4h-1c2 1 13 7 14 8l-1 1s-1-1-2-1h-3s-2-1-2-2c-3-1-6-3-8-5l-8-6c-3-2-6-5-9-7-3-3-6-6-8-9z" class="k"></path><path d="M148 561l1 1s0 1-1 1l1 1h-1l-1-1h0v-1l1-1zm6 7v-1c1 0 2 1 4 2 1 1 3 2 5 2v1h-1c-3-1-6-3-8-4z" class="C"></path><path d="M143 563v-1c1 0 2 0 2 1l1 1h1c1 1 4 3 4 4v1l-8-6z" class="K"></path><defs><linearGradient id="Y" x1="198.712" y1="510.57" x2="182.416" y2="501.603" xlink:href="#B"><stop offset="0" stop-color="#aaa9aa"></stop><stop offset="1" stop-color="#dbdadb"></stop></linearGradient></defs><path fill="url(#Y)" d="M196 481c2-1 2-1 3-2v-1h1c1 0 1 1 2 1-4 4-9 9-11 14-1 2-1 5-2 7-5 11-5 24-3 35-1 0-1 1-1 1h-1c0-1-1-1-1-2-2-14-2-28 4-41 2-5 6-9 9-12z"></path><defs><linearGradient id="Z" x1="220.822" y1="517.701" x2="205.936" y2="512.274" xlink:href="#B"><stop offset="0" stop-color="#b8b7b7"></stop><stop offset="1" stop-color="#dcdbdb"></stop></linearGradient></defs><path fill="url(#Z)" d="M214 492h0l1-1c1-1 1-2 2-3l1 1h-1l1 1 4-4h0l-2 2c-1 0 0 0 0 1-1 1-1 1-2 1v1l-1 1h1 0 1c-1 1-2 3-3 4v2l-1 1-1 1-1 3s-1 2-1 3l-1 3h0v2c-2 2 0 6-1 7l-1 1c-1 1-1 3-1 5 0 5 1 11 2 16l1 5-1 1 1 1c-2-1-2-2-3-3l-1-1c-1-3-1-7-2-10-1-10 0-18 3-28 1-1 1-3 2-5 1-3 3-5 4-8h0z"></path><path d="M252 518l3-3c2-2 5-4 9-3 4 0 6 1 9 4 3 6 4 12 2 18-1 4-3 8-5 12-1 1-2 3-3 4 0 1-1 3-2 3 5-6 6-15 6-22h-1v-1-1-2l-1-1c0-2 0-3-1-4h-1c0-2-1-4-2-5h-1v-1h-5-1 0-1l-3 3c0 1 0 1 1 2-1 0-2-1-2-1h-1v-1-1z"></path><path d="M265 517l1-1c2 2 2 3 3 5h0c1 1 1 1 1 2v2c1 2 1 4 1 5v1h-1v-1-1-2l-1-1c0-2 0-3-1-4h-1c0-2-1-4-2-5z" class="I"></path><defs><linearGradient id="a" x1="208.949" y1="510.641" x2="190.365" y2="499.721" xlink:href="#B"><stop offset="0" stop-color="#a3a3a4"></stop><stop offset="1" stop-color="#dedddd"></stop></linearGradient></defs><path fill="url(#a)" d="M217 471c1 1 1 1 2 1h0v1h0l-2 2-1 1s-1 1-2 1c0 1-1 1-1 1l-1 1-1 1-2 2c0 1-1 1-1 1h-1l-1 2h-1c-1 0-1 1-1 2-1 1-3 2-4 4l-1 2v-1c-2 2-2 4-3 6-4 9-7 18-6 28 0 6 0 12 2 18h0c-1 0-1 0-1-1h0c-1 0-1 1-1 2-4-15-6-30 0-44l2-5 2-2v-1l1-1c0-1 0 0 1-1l1-1 1-1c1-1 1-1 1-2l2-2 1-2c3-2 6-5 9-8 2-1 4-3 6-4z"></path><defs><linearGradient id="b" x1="141.805" y1="444.042" x2="184.215" y2="465.848" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#a7a8a9"></stop></linearGradient></defs><path fill="url(#b)" d="M167 395h1l2-6v2c0 1 0 1-1 2h0c0 1 0 2-1 2 1 0 1 1 1 0-6 20-10 41-14 61-1 7-2 14-2 21h0c1-1 1-3 1-4v2 3c0 9 0 18 1 27l-1-1s-1 0-1 1l1 4c1 5 2 9 4 13v1h-1v1c-2-4-4-7-5-11v-2c-2-3-2-7-2-11-1-5-1-11-1-17 0-12 2-25 5-37 2-14 6-29 10-42 1-3 2-7 3-9z"></path><path d="M154 509c1 5 2 9 4 13v1h-1v1c-2-4-4-7-5-11l2 2c0 2 1 4 2 6v-2c-1-1-1-1-1-2-1-2 0-4-1-5-1 0 0-2 0-3z" class="B"></path><defs><linearGradient id="c" x1="147.879" y1="485.693" x2="159.482" y2="490.632" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#251f1f"></stop></linearGradient></defs><path fill="url(#c)" d="M153 477h0c1-1 1-3 1-4v2 3c0 9 0 18 1 27l-1-1s-1 0-1 1c-1-7-1-13-1-19 0-3 0-6 1-9z"></path><path d="M233 470c3-2 9-4 13-4h6l1 1c1 0 2 0 4 1h3l4 1h1 1 0c0 1 0 1-1 1-2 0-5-1-8-1-2 1-7 1-8 2v1h-2-1l-1 1h0c-1 0-2 0-3 1h0-2l-1 1v2h-1-1v-1c-2 1-2 2-4 2l-1 1-1-1h0c-1 0-2 1-3 1v2c0 1-2 3-2 3-2 2-4 4-5 6l-1 1-1 1h-1 0-1l1-1v-1c1 0 1 0 2-1 0-1-1-1 0-1l2-2h0l-4 4-1-1h1l-1-1c-1 1-1 2-2 3l-1 1h0c-3 4-5 9-7 14-2 8-3 15-3 24l2 15v3h0c-1-1-2-3-3-4-1-4-2-7-2-11-1-5 0-11 0-17 0-3 1-6 1-9h0v-1c1-1 1-3 2-4 1-2 1-3 2-5v-1h-1c-1 1-1 2-2 3-1 0-1 0-2 1 0 0 0 1-1 1h0l-1 1v1s-1 0-1 1v1l-1 1-1 3v1 1c-1 1 0 2 0 3-1 1-1 2-1 3v1c-1 1-1 2-1 3s0 2-1 3c0 1-1 1-2 1-1-1 0-3 0-5 0-10 4-19 8-28v1l1-2c1-2 3-3 4-4 0-1 0-2 1-2h1l1-2h1s1 0 1-1l2-2 1-1 1-1s1 0 1-1c1 0 2-1 2-1l1-1c1-1 2-1 2-1l4-2h2l1 1-1 1h1c2 0 4-3 7-4z" class="X"></path><path d="M233 470c3-2 9-4 13-4h6l1 1c-1 1-2 1-3 1h0c-3-1-7 0-9 1h0l2-1h2c1-1-1 0 1-1h1 2v-1c-4 0-8 2-12 3-2 1-3 1-4 1z" class="C"></path><path d="M200 501h0c0-1 0-2 1-3l1-1c1-2 3-4 4-6v-1c1 0 3-2 3-2l1 1c0 1-1 5-2 6l-1 1v1h-1v-1h-1c-1 1-1 2-2 3-1 0-1 0-2 1 0 0 0 1-1 1z" class="L"></path><path d="M253 467c1 0 2 0 4 1h3l4 1h1 1 0c0 1 0 1-1 1-2 0-5-1-8-1-2 1-7 1-8 2v1h-2-1l-1 1h0c-1 0-2 0-3 1h0-2l-1 1v2h-1-1v-1c-2 1-2 2-4 2l2-2s1 0 1-1h0-1v-1h0 1c-1-1-1-1-1-2 1-1 5-2 6-2v-1c2-1 6-2 9-1h0c1 0 2 0 3-1z" class="g"></path><path d="M257 468h3l4 1h1 1 0c0 1 0 1-1 1-2 0-5-1-8-1h4 1c-2 0-3 0-5-1h0z" class="L"></path><path d="M136 520c4 16 10 30 23 41-2-1-5-2-8-4-13-8-22-18-28-32-2-4-4-7-5-11-1-7-3-17 1-24 2-2 5-6 8-6 3-1 6 1 8 3h1c0 2-1 5-1 7l1 5h0l1 1c0 3 0 6 1 8 0 2 1 4 1 6v3 2l-1-1v1l-2 1z" class="e"></path><path d="M136 520c-1-6-1-14-1-20v-6l1 5h0l1 1c0 3 0 6 1 8 0 2 1 4 1 6v3 2l-1-1v1l-2 1z" class="O"></path><path d="M138 518v-1c0-1 0-2 1-3v3 2l-1-1z" class="Y"></path><path d="M83 503c0-9 4-19 10-27 5-5 11-8 18-9 6 0 14 1 19 6 4 3 6 9 6 14h-1c0-2-1-3-2-3-5-4-14-7-20-5-6 1-11 5-14 10-6 9-7 22-5 32l2 8c1-1 1-2 1-3 1-1 1 0 1-1v-1c-1 0-1 0-1-1h0v-2c0 1 1 3 2 4h0 1c0 5 1 10 4 15s6 10 10 16c0 1 1 2 2 3v1l-1 1-1-2c-1 1 0 1 0 2-2 1-4-1-6 0 0-1-1-1-1-2-1-1-1-1-2-1v-1h0c-5-5-8-11-11-17-3-8-5-18-6-26 0-6 0-11 1-17 0-2 1-5 2-7h0-1c-2 3-2 6-3 9-1 8 0 16 2 25l-1 1c-1-1-2-2-2-4l-1-1c-1 0-1-3-2-4-1-2-1-4-1-5-1-2-1-4 0-6v-1-4 4l1-1z" class="g"></path><path d="M82 500v4l1-1v1c0 6 1 11 2 16-1 0-1-3-2-4-1-2-1-4-1-5-1-2-1-4 0-6v-1-4z" class="I"></path><path d="M82 500v4l1-1v1c-1 2 0 4-1 6v-5-1-4z" class="d"></path><defs><linearGradient id="d" x1="100.593" y1="543.486" x2="110.225" y2="541.23" xlink:href="#B"><stop offset="0" stop-color="#141315"></stop><stop offset="1" stop-color="#323334"></stop></linearGradient></defs><path fill="url(#d)" d="M97 521c0 1 1 3 2 4h0 1c0 5 1 10 4 15s6 10 10 16c0 1 1 2 2 3v1l-1 1-1-2c-8-9-14-18-18-30 1-1 1-2 1-3 1-1 1 0 1-1v-1c-1 0-1 0-1-1h0v-2z"></path><defs><linearGradient id="e" x1="251.398" y1="533.161" x2="228.956" y2="477.998" xlink:href="#B"><stop offset="0" stop-color="#c2c1c2"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#e)" d="M249 472c1 0 1 0 2 1l1 1v1c3 0 6-1 10 0 2 0 5 2 8 2 1 0 3 1 4 2 0 1 0 2-1 2h-1l1 1c0 1-1 1-1 1-1 1-2 0-3 1v1c4 1 9 2 12 5l-1 1-2-2-1 1h-1l-1-1c-2 0-3 0-5 1-4-1-7-1-11 0-7 1-11 5-15 11l-1 2c-3 4-4 9-4 13-1 1-1 3-1 4l-2-2c-1-1-1-3-1-4v-1 10c-1 0-1 1-1 2l1 4v4 1c0 1 0 1 1 1v2c0 1 1 2 0 2h0c0-1-1-1-2-2h0c0-2-1-5-3-7-1-8-2-17 1-25 0-2 1-3 2-5h0c-4 4-5 12-5 17h0c0 1 0 6-1 7v-3-1l-2 1v3h0v-4-5-1 1 1c-2 6-2 12-1 18v4s0 1 1 1v1l-3 2c-1-3-1-5-2-7-1-1-1-3-2-4-1-6-1-11 0-17h0v-2-1l2-6h0l-1-1h-1s0 1-1 1h-1v1c-2 3-2 6-3 9-1 8-1 15 1 23l-1 1c-1 0-2 0-3-1-2-6-2-12-2-19l1-1c1-1-1-5 1-7v-2h0l1-3c0-1 1-3 1-3l1-3 1-1 1-1v-2c1-1 2-3 3-4l1-1 1-1c1-2 3-4 5-6 0 0 2-2 2-3v-2c1 0 2-1 3-1h0l1 1 1-1c2 0 2-1 4-2v1h1 1v-2l1-1h2 0c1-1 2-1 3-1h0l1-1h1 2z"></path><path d="M238 483h2v1 1 1h-2l-1-1 1-2z" class="g"></path><path d="M254 480h-1 0v-2c1-1 2-1 4-2v1 1h-1c-1 0-1 1-1 1l-1 1zm-17 5h0c-1-1-1-2-1-2v-1h0c0-1 1-1 2-2h1c1-1 1-1 2-1v-1h1v1c-1 1-3 3-4 3v1l-1 2z" class="X"></path><path d="M241 496v1l-4 9-2 5v2 10c-1 0-1 1-1 2v-1c-2-10 1-19 7-28z" class="G"></path><defs><linearGradient id="f" x1="266.527" y1="480.802" x2="261.464" y2="473.947" xlink:href="#B"><stop offset="0" stop-color="#bebcc1"></stop><stop offset="1" stop-color="#dbdbd9"></stop></linearGradient></defs><path fill="url(#f)" d="M262 475c2 0 5 2 8 2 1 0 3 1 4 2 0 1 0 2-1 2h-1l-3-1h-1-1s0-1-1-1h-1c-2 0-6-1-8 0-1 0-1 1-2 1h-1l1-1s0-1 1-1h1v-1-1h0c2 1 3 0 5-1z"></path><path d="M233 489h0c0-1 1-2 2-3h0 1c0 1-1 1-2 3 0 1-1 1-1 2s-1 1-1 2h0c0 2-1 3-1 4-1 1-1 1-1 2v1c-1 1 0 2-1 2-1 2 0 5-2 7v-1h0v-1h0c1-1 0-2 0-3l1-1v-4l-1 1-1 1v1s-1 1-1 2c0 0 0 1-1 1-1-1 0-2-1-4h0c2-1 2-3 4-4h0c0-1 1-2 1-2 0-1 1-2 1-2 0-1 1-1 1-2h0v-1c1 0 1-1 2-1h1z" class="X"></path><path d="M229 493c0-1 1-1 1-2h0v-1c1 0 1-1 2-1h1l-1 3-1 1v1c0 1-1 1-1 2h-1v-3z" class="e"></path><path d="M241 496h0c1-3 6-6 8-8 6-4 14-5 20-3 4 1 9 2 12 5l-1 1-2-2-1 1h-1l-1-1c-2 0-3 0-5 1-4-1-7-1-11 0-7 1-11 5-15 11l-1 2c-3 4-4 9-4 13-1 1-1 3-1 4l-2-2c-1-1-1-3-1-4v-1-2l2-5 4-9v-1z" class="X"></path><path d="M237 506h0c0 1 1 1 1 2s-2 2-3 3l2-5z" class="K"></path><path d="M241 496h0c1-3 6-6 8-8 6-4 14-5 20-3 4 1 9 2 12 5l-1 1-2-2c-7-3-14-5-21-3s-12 5-16 11v-1z" class="N"></path><path d="M190 354l3-6c5-8 11-15 17-22l1 1h0c0 3-4 5-5 8-3 3-6 7-8 11 0 1-1 2-2 4l1 1c0 1 0 1 1 2-1 1-2 2-2 3 0 0-1 0-2 1 0 0 0 1-1 1v4l-9 21-1 4-9 28h0c-1 2-1 3-2 4l-2 6v1l1 1c0 1 0 1-1 2 0 2-1 3-1 5l3 1c1 2-1 5-1 7-1 3-1 5-2 7l-2 7c-1 7-3 14-3 21l1 16v6 1h0c1 1 1 2 1 4l1-1c0-1 0-1 1-2v-1l1-4h1v1h0l1 1s1-1 1-2c0 1 0 2-1 3v1 1 1c0 2-1 4-1 6 0 1 1 3 1 5l-2 2v5 5c2 8 2 17 6 24h-1c-1 0-1 0-1-1-1-1-2-3-3-4-1-2-2-3-3-4-2-4-3-8-5-11-1-2-3-4-4-7-2-4-3-8-4-13l-1-4c0-1 1-1 1-1l1 1c-1-9-1-18-1-27 2-18 5-35 9-53 2-11 5-23 9-34 2-6 5-11 8-17 2-5 4-9 7-13l1-3 2-4z"></path><path d="M188 358l1 1-1 3-1-1 1-3zm-23 161v2l-1 2c0-1 0-2-1-3l2-1z" class="U"></path><path d="M190 354l2 1-3 4-1-1 2-4z" class="H"></path><path d="M160 520l3 8c-2-3-3-5-4-8h1z" class="M"></path><path d="M158 510l2 10h-1c-2-3-2-5-3-9 1 1 1 1 1 2 0 2 1 3 1 5 1-1 0-2 0-3h0v-2h0v-3z" class="D"></path><path d="M157 499l1 11v3h0v2h0c0 1 1 2 0 3 0-2-1-3-1-5 0-1 0-1-1-2v-5-2l1 1v-6z" class="F"></path><path d="M165 521c2 7 2 13 4 19h-1c-1-4-2-9-3-13 0-1-1-2-1-4l1-2z" class="N"></path><path d="M165 499v1h0c1 1 1 2 1 4l1-1c0-1 0-1 1-2v-1l-2 14c0-5-1-10-1-15z" class="J"></path><path d="M170 497l1 1s1-1 1-2c0 1 0 2-1 3v1 1 1c0 2-1 4-1 6 0 1 1 3 1 5l-2 2 1-9h-1c-1-3 1-6 1-9h0z" class="T"></path><path d="M196 350l1 1c0 1 0 1 1 2-1 1-2 2-2 3 0 0-1 0-2 1 0 0 0 1-1 1-3 3-4 8-6 11h-1l3-6 7-13z" class="N"></path><path d="M163 502l2 17-2 1c0-2-1-3-1-5-1-1-1-3-1-4h1v-2c1-2 0-3 1-5v-2z" class="F"></path><path d="M161 511h1v-2c1-2 0-3 1-5v7 3s-1 0-1 1c-1-1-1-3-1-4z" class="E"></path><path d="M165 437l2 2-1 2-1 8c0 1-1 3 0 4h0-2l-1 1 3-15v-2z" class="R"></path><path d="M165 437l2 2-1 2h0c-1 0-1-1-1-2v-2z" class="E"></path><path d="M190 354l3-6c5-8 11-15 17-22l1 1c-3 3-6 7-9 10-4 6-7 12-10 18l-2-1z" class="M"></path><path d="M169 418h0c1 1 1 1 1 2 0-1 0-1 1-1h0 1l-2 6v1l1 1c0 1 0 1-1 2 0 2-1 3-1 5l-2 5-2-2 4-19z" class="B"></path><path d="M171 419h1l-2 6v1l-1-1h0c0-1 1-4 2-6h0z" class="L"></path><path d="M167 439l2-5 3 1c1 2-1 5-1 7-1 3-1 5-2 7l-2 7-1-1c0-1 1-2 0-3l-1 1h0c-1-1 0-3 0-4l1-8 1-2z"></path><defs><linearGradient id="g" x1="175.96" y1="385.694" x2="186.387" y2="391.848" xlink:href="#B"><stop offset="0" stop-color="#818180"></stop><stop offset="1" stop-color="#b1afb1"></stop></linearGradient></defs><path fill="url(#g)" d="M193 358v4l-9 21-1 4-9 28h0c-1 2-1 3-2 4h-1 0c-1 0-1 0-1 1 0-1 0-1-1-2h0c3-13 8-26 13-39 1-3 2-7 4-10h1c2-3 3-8 6-11z"></path><path d="M183 387l-1-1v-2c0-1 1-1 1-2l1 1-1 4z" class="D"></path><path d="M171 419c0-2 0-3 1-4h0 1 1c-1 2-1 3-2 4h-1z" class="B"></path><defs><linearGradient id="h" x1="167.773" y1="483.286" x2="156.841" y2="480.836" xlink:href="#B"><stop offset="0" stop-color="#b4b2b3"></stop><stop offset="1" stop-color="#d3d3d3"></stop></linearGradient></defs><path fill="url(#h)" d="M165 453l1-1c1 1 0 2 0 3l1 1c-1 7-3 14-3 21l1 16c-1-1-1-3-2-3v2 10 2c-1 2 0 3-1 5v2h-1c-4-18-2-39 1-57l1-1h2z"></path><path d="M166 455l1 1c-1 7-3 14-3 21l1 16c-1-1-1-3-2-3v2-18l3-19z" class="I"></path><defs><linearGradient id="i" x1="146.833" y1="413.514" x2="196.02" y2="451.171" xlink:href="#B"><stop offset="0" stop-color="#838280"></stop><stop offset="1" stop-color="#b9b9bc"></stop></linearGradient></defs><path fill="url(#i)" d="M187 361l1 1c-9 18-16 37-21 57-2 7-2 15-4 22l-4 21c-2 12-2 25-2 37v6l-1-1v2l-1-1c-1-9-1-18-1-27 2-18 5-35 9-53 2-11 5-23 9-34 2-6 5-11 8-17 2-5 4-9 7-13z"></path><path d="M243 503l1-2c4-6 8-10 15-11 4-1 7-1 11 0 2-1 3-1 5-1l1 1h1l1-1 2 2 1-1c4 2 6 3 10 7l2 4 3 9-1 1c0 3 1 6 1 9h0 1v1h1l1 2-1 2c0 2 0 3-1 5h0l-2 5-1 2-4 12c-2 3-4 6-6 8 0 1 0 1-1 1s0-1 0-2l-1-1h-1c1 0 1-1 2-1l-2-1 1-2v-1l-1 1c-3 4-6 7-10 10-3 3-7 6-11 9-8 5-17 9-26 11l-8 2 2-2h1 1c1-1 2-1 3-2 1 0 1-1 2-1l-1-1c-1 1-2 1-3 1h-1v-1c-2 0-4 1-7 1v1c-3-1-6 0-9-1h0 4l4-1c1 0 2-1 3-1s2 0 3-1c1 0 2 0 2-1 1 0 1 0 2-1h0 1 1l1-1c1 0 2 0 3-1h1 1l1-1-1-1 6-3h1l1 1c2 0 4-2 6-3 1 0 3-2 5-3h0l2-2c1 0 2-1 2-1v-2l-2-1c1-1 2-2 4-2 1 0 2-2 2-3 1-1 2-3 3-4 2-4 4-8 5-12 2-6 1-12-2-18-3-3-5-4-9-4-4-1-7 1-9 3l-3 3v1c-1 1-1 2-1 2-1 1-1 1-2 1-1 1 0 3-2 3l-3-8-1-1 1 3 1 3v1c0 1-1 2 0 3s0 2 1 2v2h0c0 1 0 1 1 2h-3c0-2-1-4-2-6 0-2 0-2-1-4l-1-1c-1-5 1-12 3-17v-1z" class="J"></path><path d="M278 531l-1 1h0c0-3 0-6 2-8 0 3-1 5-1 7z" class="B"></path><path d="M234 577h0 1 2c0-1 1-1 1-1-1 1-2 1-3 2 1 0 2-1 4-1 0 1-1 1-1 1h-2c-1 1-1 1-2 1l-1 1h0l1 1-8 2 2-2h1 1c1-1 2-1 3-2 1 0 1-1 2-1l-1-1z" class="X"></path><path d="M240 569l6-3h1l1 1-6 3c0 1-1 1-1 1h-1l1-1-1-1z" class="C"></path><path d="M230 574h3c-2 1-4 2-6 2l-4 1c1 1 1 1 2 0h1 2c1-1 2 0 3-1h1c-1 1-1 1-2 1-2 0-4 1-7 1v1c-3-1-6 0-9-1h0 4l4-1c1 0 2-1 3-1s2 0 3-1c1 0 2 0 2-1z" class="g"></path><path d="M244 517v-5-1c1 0 1-1 1-1 1-1 1-2 2-3 0-1 0-2 1-2l1-1v-1c0-1 1-1 1-1v-1h1l3-3c1 0 2-1 3-1v-1h2c0-1 0 0 1-1 0 0 2 0 3-1 2 0 5 0 7 1h1l5 2v1h1c0 1 1 1 2 2l3 3c0 1 0 2 1 2 2 2 2 4 4 7v2 1c-1 1 0 5 0 7h-1c0-1 1-5 0-6v-1h-1c0 2 1 4 0 6v2c-1 0-1 1-1 1v1-6l-1 1v2s0 1-1 2h0c0 1-1 3-1 4-1 1-2 3-3 4v-1c0-2 1-4 1-7 0-1-1-2-1-3s-1-1-2-1v-3h0c-1-1-1-2-2-2l-1 1c-3-3-5-4-9-4-4-1-7 1-9 3l-3 3v1c-1 1-1 2-1 2-1 1-1 1-2 1-1 1 0 3-2 3l-3-8z" class="e"></path><path d="M276 509h1l2 1v1 1s0 1-1 2h-1c-1-2-2-3-3-4h1c0-1 0 0 1-1h0zm-32 8v-5-1c1 0 1-1 1-1 1-1 1-2 2-3 0-1 0-2 1-2l1-1v-1c0-1 1-1 1-1v-1h1l3-3c1 0 2-1 3-1v-1h2c0-1 0 0 1-1 0 0 2 0 3-1 2 0 5 0 7 1h1l5 2v1h1c0 1 1 1 2 2l3 3c0 1 0 2 1 2 2 2 2 4 4 7v2 1c-1 1 0 5 0 7h-1c0-1 1-5 0-6v-1h-1c0 2 1 4 0 6v2c-1 0-1 1-1 1v1-6c-1-1 0-2-1-3 0-2 0-3-1-4v-1c-1 0-1-1-1-1l-1-1c0-1 0 0-1-1v-1c0-1-1-1-1-2-1-1-3-3-4-3l-1-1h-1c-1 0-1 0-1-1h-2-4c0 1 0 1-1 2s-4 2-6 2h-1v-1c1 0 2-1 3-2l1-1h1 0-2-2c0 1-1 1-2 2h0c-3 1-4 4-6 6v1c-1 1-1 2-1 2h0c0 3-1 8 0 10h0l2-2 1-1v1c-1 1-1 2-1 2-1 1-1 1-2 1-1 1 0 3-2 3l-3-8z" class="X"></path><defs><linearGradient id="j" x1="274.641" y1="554.308" x2="259.955" y2="490.755" xlink:href="#B"><stop offset="0" stop-color="#aaa9ab"></stop><stop offset="1" stop-color="#dcdbdb"></stop></linearGradient></defs><path fill="url(#j)" d="M243 503l1-2c4-6 8-10 15-11 4-1 7-1 11 0 2-1 3-1 5-1l1 1h1l1-1 2 2 1-1c4 2 6 3 10 7l2 4 3 9-1 1c0 3 1 6 1 9h0 1v1h1l1 2-1 2c0 2 0 3-1 5h0l-2 5-1 2-4 12c-2 3-4 6-6 8 0 1 0 1-1 1s0-1 0-2l-1-1h-1c1 0 1-1 2-1l-2-1 1-2v-1l-1 1v-1c-1 1-1 0-1 1l-3 3h0c2-2 2-3 3-5h-1v1s-1 1-1 2h-1c0 1-1 2-2 2h0l1-2h1v-1l1-1v-2l1-1c0-1 0-1 1-2l2-5 2-4 1-1v-2l1-2c0-1 0-1 1-2v-3c1-1 1-4 2-5v-5c-1-1-1-1-1-2s-1-2-1-3v-2l-2-2c0-1-1-1-1-2s-1-1-2-2l-3-3c-1-1-2-1-2-2h-1v-1l-5-2h-1c-2-1-5-1-7-1-1 1-3 1-3 1-1 1-1 0-1 1h-2v1c-1 0-2 1-3 1l-3 3h-1v1s-1 0-1 1v1l-1 1c-1 0-1 1-1 2-1 1-1 2-2 3 0 0 0 1-1 1v1 5l-1-1 1 3 1 3v1c0 1-1 2 0 3s0 2 1 2v2h0c0 1 0 1 1 2h-3c0-2-1-4-2-6 0-2 0-2-1-4l-1-1c-1-5 1-12 3-17v-1z"></path><path d="M244 519v1h-1c-1-1-1-3-2-5l1-1v1l1-1v-2 4h0l1 3z" class="X"></path><path d="M243 503l1-2c4-6 8-10 15-11 4-1 7-1 11 0 2-1 3-1 5-1l1 1h1l1-1 2 2 1-1c4 2 6 3 10 7l2 4 3 9-1 1c0 3 1 6 1 9h0 1v1h1l1 2-1 2c0 2 0 3-1 5h0l-2 5-1 2-4 12c-2 3-4 6-6 8 0 1 0 1-1 1s0-1 0-2l-1-1h-1c1 0 1-1 2-1l-2-1 1-2v-1c2-3 4-7 5-11 4-10 8-20 4-30-1-3-3-7-5-9s-5-4-7-5c-2-2-4-3-6-4-5-2-10-2-15-1-7 2-11 8-15 14v-1z" class="V"></path><path d="M292 531l1 1v2l-2-1 1-2z" class="R"></path><path d="M277 493c1-1 2-1 2-1 2 1 4 2 5 3 0 1 0 1-1 2l-6-4z" class="L"></path><path d="M291 533l2 1c-1 1-2 2-2 3-1 3-2 7-4 9v-1-1c1-3 3-7 4-11z" class="J"></path><defs><linearGradient id="k" x1="281.936" y1="552.07" x2="288.351" y2="550.295" xlink:href="#B"><stop offset="0" stop-color="#252221"></stop><stop offset="1" stop-color="#3a3c3e"></stop></linearGradient></defs><path fill="url(#k)" d="M293 537h1l-4 12c-2 3-4 6-6 8 0 1 0 1-1 1s0-1 0-2l-1-1h-1c1 0 1-1 2-1l-2-1 1-2 5-7v1 1c2-2 3-6 4-9l1 1 1-1z"></path><path d="M287 544v1 1 1c-1 2-3 5-4 7l-2-1 1-2 5-7z" class="C"></path><path d="M293 537h1l-4 12c-2 3-4 6-6 8 0-1 2-3 3-5 0-1 1-3 2-4v-1l-1 1-1-1v-1c2-2 3-6 4-9l1 1 1-1z" class="J"></path><path d="M291 537l1 1-3 10v-1l-1 1-1-1v-1c2-2 3-6 4-9z" class="i"></path><path d="M293 507l2 4c0 3 1 6 1 9h0 1v1h1l1 2-1 2c0 2 0 3-1 5h0l-2 5-1 2h-1l-1 1-1-1c0-1 1-2 2-3v-2l-1-1c1-5 2-8 2-13 0-3-1-6-2-9 0-1 1-1 1-2z" class="F"></path><path d="M293 507l2 4c0 3 1 6 1 9h-1v-1-4l-1 5h0v-2c0-3-1-6-2-9 0-1 1-1 1-2z" class="E"></path><path d="M297 520v1h1l1 2-1 2c0 2 0 3-1 5 0-1 0-1-1-2v2h-1c1-4 1-7 1-10h1z" class="d"></path><path d="M297 521h1l1 2-1 2h-1v-4z" class="D"></path><path d="M297 525h1c0 2 0 3-1 5 0-1 0-1-1-2l1-3z" class="R"></path><path d="M296 528c1 1 1 1 1 2h0l-2 5-1 2h-1l-1 1-1-1c0-1 1-2 2-3v-2c1 0 1-2 2-2h1v-2z" class="Y"></path><path d="M294 535h1l-1 2h-1c0-1 1-2 1-2z" class="R"></path><path d="M296 528c1 1 1 1 1 2h0l-2 5h-1c0-2 1-3 2-5v-2z" class="F"></path><path d="M270 490c2-1 3-1 5-1l1 1h1l1-1 2 2 1-1c4 2 6 3 10 7l2 4 3 9-1 1-2-4c0 1-1 1-1 2l-1-2c-2-4-4-7-8-10 1-1 1-1 1-2-1-1-3-2-5-3 0 0-1 0-2 1-2-2-4-3-7-3z" class="d"></path><path d="M292 505c1 1 1 1 1 2s-1 1-1 2l-1-2 1-1v-1z" class="J"></path><path d="M284 495c4 3 6 6 8 10v1l-1 1c-2-4-4-7-8-10 1-1 1-1 1-2z" class="B"></path><path d="M270 490c2-1 3-1 5-1l1 1h1l1-1 2 2c4 2 7 3 10 7-4-2-5-6-11-6 0 0-1 0-2 1-2-2-4-3-7-3z" class="C"></path><path d="M102 513l1 1c0 1 0 3 1 5v4l2 1 2 2v-2c1-1 1-4 1-5 2 1 2 3 3 5l3 7c0 2 1 4 2 5s2 2 2 3l-1 1 3 3 2 3 1-1c0 1 1 2 1 2h1c2 3 5 6 8 9 3 2 6 5 9 7l8 6c2 2 5 4 8 5 0 1 2 2 2 2h3c1 0 2 1 2 1l1-1c-1-1-12-7-14-8h1c2 1 5 3 8 4h1v-1c1-1 2-1 2-1l3 1c2 1 3 1 5 2l2 1h1c1 0 1 0 2 1h1 0l-1 1h2c1 1 2 1 3 1h1 1l7 2c2 0 3 0 5 1 1 0 1-1 2 0h2v-1h0 0c1-1 2-1 4-1h7 2 0c3 1 6 0 9 1v-1c3 0 5-1 7-1v1h1c1 0 2 0 3-1l1 1c-1 0-1 1-2 1-1 1-2 1-3 2h-1-1l-2 2 8-2c9-2 18-6 26-11 4-3 8-6 11-9 4-3 7-6 10-10l1-1v1l-1 2 2 1c-1 0-1 1-2 1h1l1 1c0 1-1 2 0 2s1 0 1-1c2-2 4-5 6-8l4-12 1-2 2-5h0c1-2 1-3 1-5l1-2h0 1c-1 2-1 3 0 5l-1 5-1 3c1 1 1 1 1 2-1 5-3 9-5 14-1 3-3 6-5 9s-4 6-7 9-6 5-8 8c-14 10-29 18-46 21-8 1-17 1-25 0 0 0-1 0-1-1h-3c-1 1-7-1-9-1l-1 1h0c-3 0-6-1-8-1s-3-1-5-1c-4-1-9-2-14-4l1-1-2-1-6-3c-2-2-5-2-7-4-4-2-7-4-11-7-3-2-7-4-9-8l-1 1-9-8c0-1-1-1-1-2h-1c-1-1-2-2-2-3-4-6-7-11-10-16 0-1 0-3-1-4h1c-2-8-3-15-2-23z"></path><path d="M110 527l2 5s-1 0-2 1l-1-4c1-1 1-1 1-2z" class="B"></path><path d="M124 545c0 1 1 2 1 2l2 2-1 2c-2-2-2-4-3-5l1-1z" class="R"></path><path d="M153 576v-1c3 2 5 3 7 5-1 0-2-1-3-1-1-1-1-1-2-1-1-1-2-1-2-2z" class="O"></path><path d="M173 585c1 1 5 2 5 2 1 1 1 2 1 2-2 0-5-1-8-3l2-1z" class="D"></path><path d="M161 576h3c1 0 2 1 2 1 2 1 3 2 5 2v2c-3-1-7-3-10-5z" class="B"></path><path d="M161 583c4 2 9 3 12 5-2 0-3 0-5-1h0-1c-1-2-5-2-7-3 0-1 1-1 1-1z" class="R"></path><path d="M171 586l-3-1c-2-1-6-2-7-4-1-1-1-1-1-2l1-1c4 1 8 4 11 6v1h1l-2 1z" class="E"></path><path d="M109 519c2 1 2 3 3 5l3 7c0 2 1 4 2 5s2 2 2 3l-1 1c-5-6-7-13-9-20 0 2 0 5 1 7 0 1 0 1-1 2v-1-1h-1v1h-2v-4l2 2v-2c1-1 1-4 1-5z" class="k"></path><path d="M127 549l23 21v1l-1 1-12-9c-2-2-4-4-6-7-2-1-4-3-5-5l1-2z" class="J"></path><defs><linearGradient id="l" x1="182.871" y1="597.051" x2="189.021" y2="588.48" xlink:href="#B"><stop offset="0" stop-color="#949794"></stop><stop offset="1" stop-color="#b2acb1"></stop></linearGradient></defs><path fill="url(#l)" d="M177 590c3 1 5 1 8 2l16 3h7c-2 0-3 1-5 1-1 0-2-1-4-1h-1 0c0 1-1 1-1 2h0c-8-1-15-3-23-6h1-1c1-1 2-1 3-1z"></path><path d="M112 532l11 18c2 3 4 5 7 8l12 11c3 2 5 5 8 7 3 3 7 5 11 7 0 0-1 0-1 1h-3 0c-3-2-7-5-10-7s-6-4-9-7c-9-7-15-16-21-26-2-4-5-7-7-11 1-1 2-1 2-1z" class="F"></path><path d="M109 528v1l1 4c2 4 5 7 7 11 6 10 12 19 21 26 3 3 6 5 9 7s7 5 10 7h0 3c2 1 6 1 7 3h0c3 1 7 2 10 3-1 0-2 0-3 1h1-1c8 3 15 5 23 6l2 1c-1 1-7-1-9-1l-1 1h0c-3 0-6-1-8-1s-3-1-5-1c-4-1-9-2-14-4l1-1-2-1h0l-1-2c-2-1-3-2-4-3-1 0-2 0-3-1-1 0-2-1-3-1l-3-2c0-1 0-1 1-2-1-1-3-2-4-3-4-3-7-5-10-9-7-6-12-13-17-20h-2s0-1-1-1c0-2-1-2-3-3-1-3-2-5-3-8 1-1 1-2 1-3l-1-4h1z"></path><path d="M147 581c0-1 0-1 1-2l3 2-1 2-3-2z" class="D"></path><path d="M182 595l8 2-1 1h0c-3 0-6-1-8-1l1-2z" class="B"></path><path d="M151 581c4 2 6 5 9 7h0c-2-1-3-2-4-3-1 0-2 0-3-1-1 0-2-1-3-1l1-2z" class="N"></path><path d="M157 584h3c2 1 6 1 7 3h0c-1 0-1 0-2 1-2 0-6-3-8-4z" class="G"></path><path d="M167 587c3 1 7 2 10 3-1 0-2 0-3 1h1-1c-3 0-6-2-9-3 1-1 1-1 2-1z" class="D"></path><path d="M160 588c5 2 10 3 14 5-2 1-2 0-3 0-3 0-6-2-8-2l-2-1h0l-1-2h0z" class="U"></path><path d="M109 532c2 5 5 10 8 15h-2s0-1-1-1c0-2-1-2-3-3-1-3-2-5-3-8 1-1 1-2 1-3zm54 59c2 0 5 2 8 2 1 0 1 1 3 0l8 2-1 2c-2 0-3-1-5-1-4-1-9-2-14-4l1-1z" class="D"></path><path d="M274 564c1 0 2 1 2 2 1 0 1 0 1 1h1l-2 2-2 1c-6 5-13 10-20 13-2 1-5 2-7 3-6 2-11 4-17 5-9 3-19 3-29 2-5 0-10-1-15-2-2-1-5-2-7-2 0 0 0-1-1-2 0 0-4-1-5-2h-1v-1l15 3c13 3 24 4 37 2 3-1 6-2 8-2 4-1 7-2 11-3l12-6 7-3c0-1 10-8 12-9h0v-1-1z" class="K"></path><path d="M255 578l7-3h0v1h0c-1 1-2 2-3 2h0c-2 0-2 1-4 0z" class="L"></path><path d="M274 564c1 0 2 1 2 2 1 0 1 0 1 1h1l-2 2-2 1 1-1v-1h0c0-1 0-1-1-2h0v-1-1z" class="P"></path><path d="M102 513l1 1c0 1 0 3 1 5v4l2 1v4h2v-1h1v1h-1l1 4c0 1 0 2-1 3l3 8c2 1 3 1 3 3 1 0 1 1 1 1h2c5 7 10 14 17 20 3 4 6 6 10 9 1 1 3 2 4 3-1 1-1 1-1 2l3 2c1 0 2 1 3 1 1 1 2 1 3 1 1 1 2 2 4 3l1 2h0l-6-3c-2-2-5-2-7-4-4-2-7-4-11-7-3-2-7-4-9-8l-1 1-9-8c0-1-1-1-1-2h-1c-1-1-2-2-2-3-4-6-7-11-10-16 0-1 0-3-1-4h1c-2-8-3-15-2-23z"></path><path d="M106 528h2v-1h1v1h-1l1 4c0 1 0 2-1 3l-2-7z" class="R"></path><path d="M147 581c-2-1-4-2-5-3-6-4-11-7-15-13 3 2 6 5 9 7-1-2-2-3-3-4l1-1c3 4 6 6 10 9 1 1 3 2 4 3-1 1-1 1-1 2z" class="H"></path><defs><linearGradient id="m" x1="101.565" y1="527.337" x2="107.022" y2="525.149" xlink:href="#B"><stop offset="0" stop-color="#828281"></stop><stop offset="1" stop-color="#9c9b9e"></stop></linearGradient></defs><path fill="url(#m)" d="M102 513l1 1c0 1 0 3 1 5v4c1 7 3 15 6 21v1h-1l-4-8s0-1-1-1c-2-8-3-15-2-23z"></path><path d="M111 543c2 1 3 1 3 3 1 0 1 1 1 1h2c5 7 10 14 17 20l-1 1c-8-6-18-15-22-25z" class="M"></path><defs><linearGradient id="n" x1="104.412" y1="540.3" x2="115.276" y2="554.734" xlink:href="#B"><stop offset="0" stop-color="#616162"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#n)" d="M104 536c1 0 1 1 1 1l4 8h1v-1l5 8 9 12c1 1 2 3 4 4l-1 1-9-8c0-1-1-1-1-2h-1c-1-1-2-2-2-3-4-6-7-11-10-16 0-1 0-3-1-4h1z"></path><defs><linearGradient id="o" x1="246.01" y1="564.011" x2="257.629" y2="587.56" xlink:href="#B"><stop offset="0" stop-color="#aeacae"></stop><stop offset="1" stop-color="#e6e5e5"></stop></linearGradient></defs><path fill="url(#o)" d="M299 523h1c-1 2-1 3 0 5l-1 5-1 3c1 1 1 1 1 2-1 5-3 9-5 14-1 3-3 6-5 9s-4 6-7 9-6 5-8 8c-14 10-29 18-46 21-8 1-17 1-25 0 0 0-1 0-1-1h-3l-2-1h0c0-1 1-1 1-2h0 1c2 0 3 1 4 1 2 0 3-1 5-1h5l11-1c5-1 11-2 16-4 3-1 6-2 10-4 3-1 6-2 9-4 6-3 12-8 18-13h-1l2-2h-1c0-1 0-1-1-1 0-1-1-2-2-2l2-3 4-4 1-1v-1h1l1 1c0 1-1 2 0 2s1 0 1-1c2-2 4-5 6-8l4-12 1-2 2-5h0c1-2 1-3 1-5l1-2h0z"></path><path d="M282 562l1 1-6 6h-1l2-2h-1l1-1c2-1 2-3 4-4z" class="Z"></path><path d="M298 536c1 1 1 1 1 2-1 5-3 9-5 14v-2l1-1h0v-2h0l1-1h0v-1c1 0 1-1 1-1 0-1 0-2 1-3v-3c-1 4-2 9-5 13v-1l5-14z" class="k"></path><path d="M280 557c1 1 1 1 1 3v1h1v1c-2 1-2 3-4 4l-1 1c0-1 0-1-1-1 0-1-1-2-2-2l2-3 4-4z"></path><path d="M290 549v1 4c-2 3-4 7-7 9l-1-1v-1h-1v-1c0-2 0-2-1-3l1-1v-1h1l1 1c0 1-1 2 0 2s1 0 1-1c2-2 4-5 6-8z" class="h"></path><path d="M280 557l1-1c1 1 1 1 1 3l-1 1c0-2 0-2-1-3z" class="V"></path><path d="M299 523h1c-1 2-1 3 0 5l-1 5-1 3-5 14-3 4v-4-1l4-12 1-2 2-5h0c1-2 1-3 1-5l1-2h0z" class="O"></path><path d="M299 523h1c-1 2-1 3 0 5l-1 5-1 1v-3h1v-3l-1 3h-1v-1h0c1-2 1-3 1-5l1-2h0z" class="Z"></path><path d="M282 550v1l-1 2 2 1c-1 0-1 1-2 1v1l-1 1-4 4-2 3v1 1h0c-2 1-12 8-12 9l-7 3-12 6c-4 1-7 2-11 3-2 0-5 1-8 2-13 2-24 1-37-2v-1l-9-2-7-3v-2c-2 0-3-1-5-2l1-1c-1-1-12-7-14-8h1c2 1 5 3 8 4h1v-1c1-1 2-1 2-1l3 1c2 1 3 1 5 2l2 1h1c1 0 1 0 2 1h1 0l-1 1h2c1 1 2 1 3 1h1 1l7 2c2 0 3 0 5 1 1 0 1-1 2 0h2v-1h0 0c1-1 2-1 4-1h7 2 0c3 1 6 0 9 1v-1c3 0 5-1 7-1v1h1c1 0 2 0 3-1l1 1c-1 0-1 1-2 1-1 1-2 1-3 2h-1-1l-2 2 8-2c9-2 18-6 26-11 4-3 8-6 11-9 4-3 7-6 10-10l1-1z" class="h"></path><path d="M274 560l7-7 2 1c-1 0-1 1-2 1v1l-1 1-4 4v-2c0 1-1 1-2 1h0z" class="B"></path><path d="M167 576c9 4 19 8 29 9h11c2 0 5 0 7 1l1 1-2 2h-8c-6-1-12-1-18-3l-9-2-7-3v-2c-2 0-3-1-5-2l1-1z" class="L"></path><path d="M171 579c1 1 3 2 5 2h0c0 1 1 2 2 3l-7-3v-2z" class="k"></path><path d="M274 560h0c1 0 2 0 2-1v2l-2 3v1c-3 2-6 5-9 7-7 5-17 9-25 12-3 1-12 3-14 2l-1-1c1-2 6-2 8-3 9-2 17-5 25-10 6-3 10-8 16-12z" class="L"></path><defs><linearGradient id="p" x1="196.877" y1="568.471" x2="198.463" y2="585.056" xlink:href="#B"><stop offset="0" stop-color="#c0c0bf"></stop><stop offset="1" stop-color="#eae9eb"></stop></linearGradient></defs><path fill="url(#p)" d="M165 570l3 1c2 1 3 1 5 2l2 1h1c1 0 1 0 2 1h1 0l-1 1h2c1 1 2 1 3 1h1 1l7 2c2 0 3 0 5 1 1 0 1-1 2 0h2v-1h0 0c1-1 2-1 4-1h7 2 0c3 1 6 0 9 1v-1c3 0 5-1 7-1v1h1c1 0 2 0 3-1l1 1c-1 0-1 1-2 1-1 1-2 1-3 2h-1-1l-2 2c-17 3-35 1-50-5-4-1-8-2-11-4-1 0-2-1-3-2h1v-1c1-1 2-1 2-1z"></path><path d="M234 577l1 1c-1 0-1 1-2 1h-4 0l1-1h1c1 0 2 0 3-1z" class="K"></path><path d="M165 574h3c0 1 1 1 2 1l-1-1c1 1 3 1 3 2 1 1 1 1 2 1s1 1 2 1c-4-1-8-2-11-4z" class="k"></path><path d="M212 578h2 0c3 1 6 0 9 1l-1 1c-1 0-2-1-2 0-5 1-11 0-16 0l-1-1h-2 0 0c1-1 2-1 4-1h7zm8-139c7-5 16-6 25-7v1h14 0 2l2 1 1-1 8 2h2 1c1 1 2 1 3 1v1c1 0 0 0 1-1l2 2h2 0c1 1 1 2 1 3h1v3h1c-1 1-2 1-2 1s1 0 2 1c1 0 1 1 2 1 3 2 6 3 7 6l2 1 3 4c1 1 2 3 3 4 0-2-1-2-2-3h0l1-1c11 14 15 33 16 50v22l-1 2-2 11h-1c-1 2-1 5-1 7-2 6-6 12-10 17-6 8-12 15-20 21-3 3-7 6-11 8-9 5-19 10-29 13-15 4-30 3-45 2-9-1-18-2-26-4-4-2-8-3-12-4s-8-3-12-5c-3-1-6-3-9-4-5-3-9-6-14-9-2-1-6-4-8-6l-7-7c-11-12-19-26-23-41l-3-11c-1-1-1-3-2-4-1-2-1-3-1-5h1c0 1 0 3 1 5 1 1 1 4 2 4l1 1c0 2 1 3 2 4l1-1c-2-9-3-17-2-25 1-3 1-6 3-9h1 0c-1 2-2 5-2 7-1 6-1 11-1 17 1 8 3 18 6 26 3 6 6 12 11 17h0v1c1 0 1 0 2 1 0 1 1 1 1 2 2-1 4 1 6 0 0-1-1-1 0-2l1 2 1-1v-1h1c0 1 1 1 1 2l9 8 1-1c2 4 6 6 9 8 4 3 7 5 11 7 2 2 5 2 7 4l6 3 2 1-1 1c5 2 10 3 14 4 2 0 3 1 5 1s5 1 8 1h0l1-1c2 0 8 2 9 1h3c0 1 1 1 1 1 8 1 17 1 25 0 17-3 32-11 46-21 2-3 5-5 8-8s5-6 7-9 4-6 5-9c2-5 4-9 5-14 0-1 0-1-1-2l1-3 1-5c-1-2-1-3 0-5h-1 0l-1-2h-1v-1h-1 0c0-3-1-6-1-9l1-1-3-9-2-4c-4-4-6-5-10-7-3-3-8-4-12-5v-1c1-1 2 0 3-1 0 0 1 0 1-1l-1-1h1c1 0 1-1 1-2-1-1-3-2-4-2-3 0-6-2-8-2-4-1-7 0-10 0v-1l-1-1c-1-1-1-1-2-1v-1c1-1 6-1 8-2 3 0 6 1 8 1 1 0 1 0 1-1h0-1-1l-4-1h-3c-2-1-3-1-4-1l-1-1h-6c-4 0-10 2-13 4-3 1-5 4-7 4h-1l1-1-1-1h-2l-4 2s-1 0-2 1l2-2h0v-1h0c-1 0-1 0-2-1-2 1-4 3-6 4-3 3-6 6-9 8l-1 2-2 2c0 1 0 1-1 2l-1 1-1 1c-1 1-1 0-1 1l-1 1v1l-2 2-2 5-1-1c1-2 1-5 2-7 2-5 7-10 11-14-1 0-1-1-2-1h-1v1c-1 1-1 1-3 2-3 3-7 7-9 12l-1-1 1-1v-2h0-1 0-1 0-1c-2 3-3 6-4 9l-1-1c0-1 1-2 1-3l-1-1c1-1 1-1 1-2 0-2 1-3 2-4l1-3h0l-1-1h0c-2 1-2 3-3 4v1l-1 2h0-1-2l-2 6c0 2-1 4-2 6v-1-1-1c1-1 1-2 1-3 0-2 1-4 2-6s1-5 2-7c2-3 4-6 6-10 1-2 2-3 3-5 2-3 4-6 7-9h0c0-1-1-1-1-2l1-2h-1l1-3h0c0-1 1-1 2-2l1-1c-1-1-1-1 0-2 0-1 1-1 2-2s1-1 1-2l3-3h2v-1l4-1h1 0c0 1 0 1 1 1l1 1 3-1c0-1 1-1 2-2s2-1 4-2v1c0 1 0 2 1 3z" class="X"></path><path d="M303 466h0 1s0 1 1 1v-2c0 1 1 2 1 3 0 0-1 1-1 0l-2-2z" class="g"></path><path d="M289 457h0 2 1c0 1 0 1 1 1v1h-1l-3-2z" class="e"></path><path d="M303 548l1-2c0 2 0 4-1 5s-2 1-3 2l1-1c0-1 0-2 1-3 0 0 1 0 1-1z" class="C"></path><path d="M304 472c1 2 3 4 3 6 1 1 0 2 0 3-1-3-2-5-4-7l1-2z" class="K"></path><path d="M291 562c1 0 1 1 2 1l-6 6c0-1 1-2 1-3l3-4z" class="Q"></path><path d="M225 440c1 1 2 1 4 1l1-1 2 1h-3 1 1 0c-1 1-2 1-3 2h-2c-1-1-3-1-3-2l2-1z" class="g"></path><path d="M314 543c0-2 1-4 1-5 1-3 1-4 2-6l-2 11h-1z" class="b"></path><path d="M295 453l2 1 3 4h-2s-1 0-1-1c-1 1-1 1-1 2 0 0-1-1-2-1l-1-1h0 0 1v-1-1l1 1v-1c-1 0-1-1-1-2l1 2 1-1-1-1h0z" class="g"></path><path d="M245 433h14 0c-2 1-4 0-6 0-1 0-2 1-4 1s-6 0-8 1-4 0-5 1c-2 1-4 0-5 0s-1 1-2 1l1-1c5-1 10-3 15-3z" class="b"></path><path d="M271 588h1l2 2c-3 2-6 2-9 4-4 1-7 2-10 3h-3c4-2 9-2 13-4 2-2 4-3 6-5z"></path><path d="M296 459c0-1 0-1 1-2 0 1 1 1 1 1h2c1 1 2 3 3 4v1l2 2v2c-1 0-1-1-1-1h-1 0c-1 0-2-1-2-2-1-2-4-3-5-5z" class="e"></path><path d="M289 457l3 2c4 4 9 8 12 13l-1 2-2-3c-2-3-6-5-8-8l-4-3-1-1c0-1 0-1 1-2z" class="F"></path><path d="M250 598l1-1c0 1 0 1-1 2 0 1 1 1 1 2h1l-1 1c-3 1-5 2-9 2h-1 0l-1-2v-1c2-2 7-3 10-3z" class="Z"></path><path d="M281 452l1 1c2 0 4 2 6 2h1c1 0 2 1 2 2h0-2 0c-1 1-1 1-1 2l1 1-1 1c-1-1-2-1-3-2l-2-1h0c-1-1-1-2-2-2l-3-1v-1h0l1-1s1 0 2-1z" class="K"></path><path d="M278 454h0c4 1 8 2 10 5l1 1-1 1c-1-1-2-1-3-2l-2-1h0c-1-1-1-2-2-2l-3-1v-1z" class="G"></path><path d="M253 441v-1c2 0 2 1 3 1h4c1 0 3 0 5 1h3 1l3 1 1 1h2c0 1 1 1 2 1l1 1h0-1 0c-1-1-3-1-4-1 0 1 0 1 1 1l1 1h1c1 1 2 1 3 1v1c-1 0-2-1-4-1l-1-1h0l-2-1c-2 0-5 0-6-2-1-1-3 0-4-1h1 0v-1h-1c-3 0-6-1-8 0l-1-1z" class="C"></path><path d="M264 433l8 2h2 1c1 1 2 1 3 1v1c1 0 0 0 1-1l2 2h2 0c1 1 1 2 1 3h1v3h1c-1 1-2 1-2 1l-1-1h-2c0-1-1-1-1-1l-3-1c-1 0-1 0-2-1h0 1 0 4v1h1c1 1 2 1 3 1 0 0 0-1-1-1-2-1-5-3-8-4l-8-3h-1c-2 0-3-1-5-1v-1l2 1 1-1z" class="K"></path><path d="M281 438h2 0c1 1 1 2 1 3l-6-3h3z" class="I"></path><path d="M264 433l8 2h2 1c1 1 2 1 3 1v1c1 0 0 0 1-1l2 2h-3c-2 0-4-1-6-2-3-1-6-2-9-2l1-1z" class="Q"></path><path d="M82 511c0 1 0 3 1 5 1 1 1 4 2 4l1 1c0 2 1 3 2 4l1-1v3c2 4 2 7 4 11 4 12 13 23 21 33v-1c-2 0-3-1-4-3-3-4-6-7-9-11l-1-3-1-1-1-1-4-8h0l-1-1c0-2-1-3-1-4l-1-1v-2s0-1-1-1v-2-2c-1 0-1-1-1-1h-1l-1-1c0 2 1 3 1 5s1 3 2 4c3 9 7 18 13 26 2 2 4 4 6 7 1 1 3 2 4 4v-1l1 1v1c1 1 3 3 3 4l-7-7c-11-12-19-26-23-41l-3-11c-1-1-1-3-2-4-1-2-1-3-1-5h1z" class="B"></path><path d="M89 524v3 1h-1v-3l1-1z" class="X"></path><path d="M89 524c-2-9-3-17-2-25 1-3 1-6 3-9h1 0c-1 2-2 5-2 7-1 6-1 11-1 17 1 8 3 18 6 26 3 6 6 12 11 17h0c3 5 7 10 11 14 16 15 37 27 59 31 5 1 9 2 14 2 7 2 15 2 22 3 4 0 7 0 10-1 1-1 0-2 1-3 4-1 10-2 13-1l3 3c-5 3-13 3-20 3-14 0-29-2-44-5-23-5-43-16-60-32-8-10-17-21-21-33-2-4-2-7-4-11v-3z" class="V"></path><path d="M116 559h1c0 1 1 1 1 2l9 8 1-1c2 4 6 6 9 8 4 3 7 5 11 7 2 2 5 2 7 4l6 3 2 1-1 1c5 2 10 3 14 4 2 0 3 1 5 1s5 1 8 1h0l1-1c2 0 8 2 9 1h3c0 1 1 1 1 1 8 1 17 1 25 0 17-3 32-11 46-21l1 1c-13 9-26 16-41 20-12 2-24 3-36 2-6-1-11-2-16-3-14-3-29-6-41-14-3-1-5-3-7-5-7-5-13-11-19-18l1-1v-1z"></path><path d="M190 597c2 0 8 2 9 1h3c0 1 1 1 1 1-5 0-10 1-14-1h0l1-1z" class="F"></path><path d="M116 559h1c0 1 1 1 1 2l9 8v1c1 0 1 0 1 1l2 1v2c-5-3-10-9-14-14v-1z" class="B"></path><path d="M128 568c2 4 6 6 9 8 4 3 7 5 11 7 2 2 5 2 7 4l6 3 2 1-1 1c-2-1-4-1-7-2-9-4-18-9-25-16v-2l-2-1c0-1 0-1-1-1v-1l1-1z" class="J"></path><path d="M274 455c1 0 2 1 3 1l6 2 2 1c1 1 2 1 3 2l1-1 4 3c2 3 6 5 8 8l2 3c2 2 3 4 4 7 2 3 4 7 5 11 1 5 1 9 1 14 0 4 0 9-1 14-1 8-4 15-7 23 0 1-1 3-1 3l-1 2c0 1-1 1-1 1l-1-1 1-2c1-2 1-3 1-5h0c0-4 1-8 2-11l1-4v-19l-1-2 1-1c1 1 1 2 1 3l1 1h1v-2h1c-1-1-1-4-2-5-2-8-5-16-10-22l1-1-1-2c-3-4-8-8-12-10-1-1-2-1-4-2-1-1-3-2-4-2-7-3-14-4-20-5v-1c4 0 7 1 11 2v-1h1c0-1 0-1 1-1h2l1-1z"></path><path d="M310 506v3 3 5h-1v5h-1v-10l-1-5 1 1h1v-2h1z" class="V"></path><defs><linearGradient id="q" x1="303.463" y1="513.256" x2="310.026" y2="524.783" xlink:href="#B"><stop offset="0" stop-color="#696d6b"></stop><stop offset="1" stop-color="#878386"></stop></linearGradient></defs><path fill="url(#q)" d="M305 505l1-1c1 1 1 2 1 3l1 5v10c0 5-1 10-2 15-1 2-1 3-1 5-1 2-2 4-2 6 0 1-1 1-1 1l-1-1 1-2c1-2 1-3 1-5h0c0-4 1-8 2-11l1-4v-19l-1-2z"></path><path d="M305 505l1-1c1 1 1 2 1 3l1 5c-1-2-1-3-2-5l-1-2z" class="O"></path><path d="M306 526h0v4c-1 2-1 5-1 7h1c-1 2-1 3-1 5-1 2-2 4-2 6 0 1-1 1-1 1l-1-1 1-2c1-2 1-3 1-5h0c0-4 1-8 2-11l1-4zm-48-70c4 0 7 1 11 2 3 0 6 1 10 3 2 0 4 1 6 2 3 2 7 5 10 8 5 6 9 12 12 19 1 3 2 5 3 8-1-1-2-3-2-4l-5-10c-1-2-2-4-4-6l-1-2c-3-4-8-8-12-10-1-1-2-1-4-2-1-1-3-2-4-2-7-3-14-4-20-5v-1z" class="a"></path><path d="M274 455c1 0 2 1 3 1l6 2 2 1c1 1 2 1 3 2l1-1 4 3c2 3 6 5 8 8h-1c-2 0-3-3-5-3 0-1-1-1-1-1l-7-5h-2c1 1 1 1 2 1l3 3 1 1c1 1 3 2 4 3v1c-3-3-7-6-10-8-2-1-4-2-6-2-4-2-7-3-10-3v-1h1c0-1 0-1 1-1h2l1-1z" class="I"></path><path d="M289 460l4 3-1 1c-2 0-3-2-4-3l1-1z" class="j"></path><path d="M274 455c1 0 2 1 3 1v2c1 0 1 0 1 1h0c-3-1-6-1-8-2 0-1 0-1 1-1h2l1-1z" class="T"></path><path d="M274 455c1 0 2 1 3 1v2c-2 0-3-1-4-2l1-1z" class="V"></path><defs><linearGradient id="r" x1="266.656" y1="519.301" x2="286.665" y2="482.539" xlink:href="#B"><stop offset="0" stop-color="#b8b8b9"></stop><stop offset="1" stop-color="#dbdadb"></stop></linearGradient></defs><path fill="url(#r)" d="M257 469c3 0 6 1 8 1l13 5c-1-1-1-2-1-3h1l7 3c2 2 5 4 7 6 6 7 11 15 13 24l1 2v19l-1 4c-1 3-2 7-2 11h0c0 2 0 3-1 5l-1 2 1 1c-1 1-1 2-1 3l-1 1-5 7c-1 1-2 2-2 3-1 0-1-1-2-1l-3 4c0 1-1 2-1 3-3 4-9 8-12 10l-1-1c2-3 5-5 8-8s5-6 7-9 4-6 5-9c2-5 4-9 5-14 0-1 0-1-1-2l1-3 1-5c-1-2-1-3 0-5h-1 0l-1-2h-1v-1h-1 0c0-3-1-6-1-9l1-1-3-9-2-4c-4-4-6-5-10-7-3-3-8-4-12-5v-1c1-1 2 0 3-1 0 0 1 0 1-1l-1-1h1c1 0 1-1 1-2-1-1-3-2-4-2-3 0-6-2-8-2-4-1-7 0-10 0v-1l-1-1c-1-1-1-1-2-1v-1c1-1 6-1 8-2z"></path><path d="M252 474c1-1 2-1 3-1 5 0 10 1 15 4-3 0-6-2-8-2-4-1-7 0-10 0v-1z" class="D"></path><path d="M299 523v-1c0-4-1-9-2-13-1-2-2-4-2-7 3 6 4 10 5 16v10c-1-2-1-3 0-5h-1z" class="O"></path><path d="M274 479c1 0 2 0 3 1 3 1 5 2 8 5 0 1 1 1 2 2v1c0 1 2 2 3 3h-1c-5-3-10-5-15-8h0l-1-1-1-1h1c1 0 1-1 1-2z" class="I"></path><path d="M274 479c1 0 2 0 3 1-1 0-3 2-3 3h0l-1-1-1-1h1c1 0 1-1 1-2z" class="L"></path><path d="M290 491c3 3 5 6 5 11 0 3 1 5 2 7 1 4 2 9 2 13v1h0l-1-2h-1v-1h-1 0c0-3-1-6-1-9l1-1-3-9-2-4h1v-3l-3-3h1z" class="G"></path><path d="M292 494c0 1 1 3 2 4v2s0 1-1 1l-2-4h1v-3z" class="B"></path><path d="M295 511l1-1c0 3 1 7 1 10h-1 0c0-3-1-6-1-9z" class="V"></path><defs><linearGradient id="s" x1="289.55" y1="487.765" x2="273.536" y2="492.12" xlink:href="#B"><stop offset="0" stop-color="#b6b6b6"></stop><stop offset="1" stop-color="#dcd9d9"></stop></linearGradient></defs><path fill="url(#s)" d="M269 485v-1c1-1 2 0 3-1 0 0 1 0 1-1l1 1h0c5 3 10 5 15 8l3 3v3h-1c-4-4-6-5-10-7-3-3-8-4-12-5z"></path><path d="M257 469c3 0 6 1 8 1l13 5c-1-1-1-2-1-3h1l7 3c2 2 5 4 7 6 6 7 11 15 13 24l1 2v19l-1 4c-1 3-2 7-2 11h0c0 2 0 3-1 5l-1 2 1 1c-1 1-1 2-1 3l-1 1-5 7c-1 1-2 2-2 3-1 0-1-1-2-1l-3 4c0 1-1 2-1 3-3 4-9 8-12 10l-1-1c2-3 5-5 8-8s5-6 7-9 4-6 5-9c2-5 4-9 5-14 2-5 4-10 4-16 0-7-1-14-3-20s-4-12-9-16c-4-4-9-7-14-9-5-3-9-5-14-6s-9 0-14 0c1-1 6-1 8-2z" class="Y"></path><path d="M299 542l5-14c0 1 0 1 1 2-1 3-2 7-2 11h0l-1-1-3 2z" class="B"></path><path d="M299 542l3-2 1 1h0 0c0 2 0 3-1 5l-1 2 1 1c-1 1-1 2-1 3l-1 1-5 7c-1 1-2 2-2 3-1 0-1-1-2-1l-3 4v-2c1-3 4-5 5-7 2-3 2-5 3-8 1-2 3-4 3-7z" class="C"></path><path d="M303 541h0c0 2 0 3-1 5l-1 2 1 1c-1 1-1 2-1 3-1-1-2-1-2-2 1-3 3-6 4-9h0z" class="h"></path><path d="M291 562l8-12c0 1 1 1 2 2l-1 1-5 7c-1 1-2 2-2 3-1 0-1-1-2-1z" class="O"></path><defs><linearGradient id="t" x1="302.346" y1="497.263" x2="284.494" y2="501.938" xlink:href="#B"><stop offset="0" stop-color="#afafaf"></stop><stop offset="1" stop-color="#dcd9db"></stop></linearGradient></defs><path fill="url(#t)" d="M278 475c-1-1-1-2-1-3h1l7 3c2 2 5 4 7 6 6 7 11 15 13 24l1 2v19l-1 4c-1-1-1-1-1-2 1-6 1-12-1-18-3-11-6-21-16-28-3-3-6-5-9-7z"></path><path d="M240 455h1c0 1 0 1 1 1v1c1 0 1 0 1 1l3-1c2 0 5-1 8 0h4c6 1 13 2 20 5 1 0 3 1 4 2 2 1 3 1 4 2 4 2 9 6 12 10l1 2-1 1c5 6 8 14 10 22 1 1 1 4 2 5h-1v2h-1l-1-1c0-1 0-2-1-3l-1 1c-2-9-7-17-13-24-2-2-5-4-7-6l-7-3h-1c0 1 0 2 1 3l-13-5c1 0 1 0 1-1h0-1-1l-4-1h-3c-2-1-3-1-4-1l-1-1h-6c-4 0-10 2-13 4-3 1-5 4-7 4h-1l1-1-1-1h-2l-4 2s-1 0-2 1l2-2h0v-1h0c-1 0-1 0-2-1-2 1-4 3-6 4-3 3-6 6-9 8l-1 2-2 2c0 1 0 1-1 2l-1 1-1 1c-1 1-1 0-1 1l-1 1v1l-2 2-2 5-1-1c1-2 1-5 2-7 2-5 7-10 11-14 5-6 12-12 19-16l2-1 4-2 6-3 5-1 2-1z" class="d"></path><path d="M230 461h0l2-2 2 2-3 1c0-1 0-1-1-1z" class="H"></path><path d="M239 457h0 3c1 0 1 0 1 1l-4 1v-2z" class="j"></path><path d="M230 461c1 0 1 0 1 1l-3 1c-1 0-1 0-2-1h0l3-1h1z" class="M"></path><path d="M235 458l4-1v2l-3 1-2 1-2-2h2l1-1z" class="a"></path><path d="M232 459h2c1 0 1 0 2 1l-2 1-2-2z" class="G"></path><path d="M240 455h1c0 1 0 1 1 1v1h-3 0l-4 1-2-1 5-1 2-1z" class="d"></path><path d="M227 460l6-3 2 1-1 1h-2l-2 2h0-1-2v-1z" class="I"></path><g class="H"><path d="M234 462c3-2 9-3 13-4 0 1 1 1 2 2l-10 2h0 0c-1 0-2 1-3 1l-1-1h0-1z"></path><path d="M248 458c4-1 9-1 13 0l12 3-1 1h-1c-6-1-11-3-17-3l-5 1c-1-1-2-1-2-2h1z"></path></g><path d="M247 458h1 3c1 0 1 1 2 1h1l-5 1c-1-1-2-1-2-2z" class="N"></path><path d="M254 459c6 0 11 2 17 3 1 2 2 2 3 3l3 1-1 1-4-2-5-3c-7-2-13-3-20-1-3 0-6 0-8 1h0 0l10-2 5-1z" class="Q"></path><path d="M247 461c7-2 13-1 20 1l-2 2c-4-2-13-2-17-1l-7 1c2-1 6-2 8-3h0 0-2z" class="T"></path><path d="M222 468c3-2 7-4 10-5l1 1-2 1c-1 0-1 0-2 1 0 0-1 1-1 2-1 0-2 1-3 1l-1 1c0 1-1 1-1 2l-4 2s-1 0-2 1l2-2h0v-1h0c-1 0-1 0-2-1 1-1 3-3 5-3z" class="E"></path><path d="M222 468c3-2 7-4 10-5l1 1-2 1c-1 0-1 0-2 1 0 0-1 1-1 2-1 0-2 1-3 1 0-1 2-2 3-3h1c-2-1-5 1-7 1v1z" class="F"></path><path d="M247 461h2 0 0c-2 1-6 2-8 3v1h0c-2-1-2 0-3 0l-2 1-5 2c-2 0-3 1-5 2h-2l1-1c1 0 2-1 3-1 0-1 1-2 1-2 1-1 1-1 2-1l2-1-1-1h0l2-1h1 0l1 1c1 0 2-1 3-1 2-1 5-1 8-1z" class="D"></path><path d="M234 462h1 0l1 1-3 1c-1 1-2 2-4 3v-1c1-1 1-1 2-1l2-1-1-1h0l2-1z" class="N"></path><path d="M223 462l4-2v1h2l-3 1h0c1 1 1 1 2 1l-2 1c-2 1-5 2-6 4-4 2-9 6-12 9-2 2-5 4-6 6l-1 2-2 2c0 1 0 1-1 2l-1 1-1 1c-1 1-1 0-1 1l-1 1v1l-2 2-2 5-1-1c1-2 1-5 2-7 2-5 7-10 11-14 5-6 12-12 19-16l2-1z" class="O"></path><path d="M205 477h3c-2 2-5 4-6 6l-1 2-2 2c0-2 1-2 1-3s-1 0-1 0l6-7z" class="C"></path><path d="M199 484s1-1 1 0-1 1-1 3c0 1 0 1-1 2l-1 1-1 1c-1 1-1 0-1 1l-1 1v1l-2 2c0-5 4-8 7-12z" class="L"></path><defs><linearGradient id="u" x1="215.038" y1="468.093" x2="216.791" y2="471.692" xlink:href="#B"><stop offset="0" stop-color="#908f91"></stop><stop offset="1" stop-color="#c3c3c4"></stop></linearGradient></defs><path fill="url(#u)" d="M219 466c2-2 5-3 7-4 1 1 1 1 2 1l-2 1c-2 1-5 2-6 4-4 2-9 6-12 9h-3c4-4 9-9 14-11z"></path><path d="M219 466c2-2 5-3 7-4 1 1 1 1 2 1l-2 1-1-1c-1 1-4 3-6 3z" class="H"></path><path d="M248 463c4-1 13-1 17 1l2-2 5 3 4 2 2 1c1 1 2 2 4 3v1h0l1 1c0 1 1 1 2 2h0l-7-3h-1c0 1 0 2 1 3l-13-5c1 0 1 0 1-1h0-1-1l-4-1h-3c-2-1-3-1-4-1l-1-1h-6c-4 0-10 2-13 4-3 1-5 4-7 4h-1l1-1-1-1h-2c0-1 1-1 1-2h2c2-1 3-2 5-2l5-2 2-1c1 0 1-1 3 0h0v-1l7-1z" class="Q"></path><path d="M248 463h2v1h0c-2 0-4 0-5 1-1 0-2 1-3 1-1-1-3-1-4-1 1 0 1-1 3 0h0v-1l7-1z" class="E"></path><path d="M238 465c1 0 3 0 4 1l-8 3c-1-1-2-1-3-1l5-2 2-1z" class="J"></path><path d="M246 466c5-2 13 0 18 1-1 0-3 0-4 1h-3c-2-1-3-1-4-1l-1-1h-6z" class="B"></path><path d="M231 468c1 0 2 0 3 1-2 1-7 3-8 5h-1l1-1-1-1h-2c0-1 1-1 1-2h2c2-1 3-2 5-2z" class="P"></path><path d="M264 467c5 1 10 2 14 5h-1c0 1 0 2 1 3l-13-5c1 0 1 0 1-1h0-1-1l-4-1c1-1 3-1 4-1z" class="k"></path><path d="M248 463c4-1 13-1 17 1l2-2 5 3 4 2 2 1c1 1 2 2 4 3v1h0l1 1-8-4c-9-4-16-5-25-5h0v-1h-2z" class="B"></path><path d="M267 462l5 3c-3 0-5 0-7-1l2-2z" class="N"></path><path d="M273 461c2 1 5 2 7 3l6 3a34.47 34.47 0 0 1 12 12c5 6 8 14 10 22 1 1 1 4 2 5h-1v2h-1l-1-1c0-1 0-2-1-3l-1 1c-2-9-7-17-13-24-2-2-5-4-7-6h0c-1-1-2-1-2-2l-1-1h0v-1c-2-1-3-2-4-3l-2-1 1-1-3-1c-1-1-2-1-3-3h1l1-1z"></path><path d="M278 468l3 1c4 2 8 5 11 8 2 1 4 3 5 5 4 4 6 9 7 14-3-5-6-11-11-16-2-2-5-4-8-5-1-1-2-1-2-2l-1-1h0v-1c-2-1-3-2-4-3z" class="D"></path><path d="M273 461c2 1 5 2 7 3l6 3a34.47 34.47 0 0 1 12 12c5 6 8 14 10 22 1 1 1 4 2 5h-1c0-2-1-4-2-6-2-6-4-14-9-19l-1 1c-1-2-3-4-5-5-3-3-7-6-11-8l-3-1-2-1 1-1-3-1c-1-1-2-1-3-3h1l1-1z" class="M"></path><path d="M276 464l3 2h-2l-3-1 2-1z" class="O"></path><path d="M271 462h1l4 2-2 1c-1-1-2-1-3-3z" class="l"></path><path d="M279 466c1 1 3 1 4 2l-2 1-3-1-2-1 1-1h2z" class="I"></path><path d="M281 469l2-1c1 1 1 1 2 1h0c3 2 5 4 8 6 2 1 4 4 5 6h0l-1 1c-1-2-3-4-5-5-3-3-7-6-11-8z" class="d"></path><path d="M220 439c7-5 16-6 25-7v1c-5 0-10 2-15 3l-1 1-4 3-2 1c0 1 2 1 3 2h2 2 0-1 3c1 0 1 0 2-1h0 2 2c2 0 3-1 5-1 1 1 2 0 2 1h2s1 0 1 1h0 1c0-1 0 0-1-1h0c2-1 3-1 5-1l1 1c2-1 5 0 8 0h1v1h0-1c1 1 3 0 4 1 1 2 4 2 6 2l2 1-2 2 4 1c2 1 3 2 5 2-1 1-2 1-2 1l-1 1h0v1l3 1c1 0 1 1 2 2h0l-6-2c-1 0-2-1-3-1l-1 1h-2c-1 0-1 0-1 1h-1v1c-4-1-7-2-11-2v1h-4c-3-1-6 0-8 0l-3 1c0-1 0-1-1-1v-1c-1 0-1 0-1-1h-1l-2 1-5 1-6 3-4 2-2 1c-7 4-14 10-19 16-1 0-1-1-2-1h-1v1c-1 1-1 1-3 2-3 3-7 7-9 12l-1-1 1-1v-2h0-1 0-1 0-1c-2 3-3 6-4 9l-1-1c0-1 1-2 1-3l-1-1c1-1 1-1 1-2 0-2 1-3 2-4l1-3h0l-1-1h0c-2 1-2 3-3 4v1l-1 2h0-1-2l-2 6c0 2-1 4-2 6v-1-1-1c1-1 1-2 1-3 0-2 1-4 2-6s1-5 2-7c2-3 4-6 6-10 1-2 2-3 3-5 2-3 4-6 7-9h0c0-1-1-1-1-2l1-2h-1l1-3h0c0-1 1-1 2-2l1-1c-1-1-1-1 0-2 0-1 1-1 2-2s1-1 1-2l3-3h2v-1l4-1h1 0c0 1 0 1 1 1l1 1 3-1c0-1 1-1 2-2s2-1 4-2v1c0 1 0 2 1 3z" class="b"></path><path d="M220 449h1v1h-1v-1z" class="L"></path><path d="M220 457c0-1 0-1-1-2v-1h1c1-1 1-1 2-1 0 1 0 1-1 1v2l-1 1z" class="e"></path><path d="M195 458l3-2 1 1-2 2h-2v-1z" class="B"></path><path d="M186 471c0 2-1 3-2 5-1 1-1 2-2 4v-2l1-2-1-1s1-1 1-2c1-1 2-1 3-2z" class="D"></path><path d="M182 475l1 1-1 2v2l-3 6c-1 1-2 3-2 4h-2l3-6c1-3 3-6 4-9z" class="F"></path><path d="M193 467l1-2c0-1 2-2 2-3 1-1 2-2 4-3 1-1 2-3 4-4h1l-1 1 1 1c0-1 1-1 2-1l1-1h1v1h-1c-2 1-4 3-7 4-1 0-2 1-3 2l-1 1h0l-1 1-3 3z" class="C"></path><path d="M230 444h2c-1 1 0 2-1 3 0 1-1 1-1 1-1 0-1 0-2 1h-1c-1 1-1 0-1 1h-1l-2 1-1-1 2-1h-1-1v-1l1-1 5-2 2-1z" class="X"></path><path d="M230 452c6-3 14-4 21-4 4 0 7 0 10 1h-3l1 1h-11-6c-2 1-4 1-6 1s-4 2-6 1z" class="j"></path><path d="M230 452c2 1 4-1 6-1s4 0 6-1c0 1 1 1 1 1v1h-2l-4 1c-5 0-11 3-15 5v1-1h-2l-6 3h-2l4-2 4-2 1-1c3-1 6-3 9-4z" class="B"></path><path d="M217 443c1 0 3-1 4-1 0-1 1-1 2-1 0 1 2 1 3 2h0c-3 0-6 3-8 4h-1s-1 0-1 1c-1 0-2 0-2 1l-3 1c-1 0-1 1-2 1 0 1 0 0-1 1h-1c0 1 0 1-1 1l-1 1h-1l-4 4v-1c1-1 1-1 1-2l2-2c2-1 3-2 4-4l1-1h1v1-2h1c2 0 2 0 3-1s2-2 4-3z" class="X"></path><path d="M253 441l1 1c2-1 5 0 8 0h1v1h0-1c1 1 3 0 4 1 1 2 4 2 6 2l2 1-2 2c0-1-1-1-2-1 0 0-1 0-1-1h-1-2-1l-1-1h-1c-2 0-4 0-5-1h-3c-5-1-10 1-15 1v-1l1-2c1 0 2-1 3 0h1 1c0 1 2 0 2 0h1c0-1 0 0-1-1h0c2-1 3-1 5-1z" class="e"></path><path d="M192 459l2-2 1 1v1c-1 2-2 4-4 5-1 2-4 5-5 7-1 1-2 1-3 2 0 1-1 2-1 2-1 3-3 6-4 9l-3 6-2 6c0 2-1 4-2 6v-1-1-1c1-1 1-2 1-3 0-2 1-4 2-6s1-5 2-7c2-3 4-6 6-10 1-2 2-3 3-5 2-3 4-6 7-9h0z" class="c"></path><path d="M192 459l2-2 1 1v1c-1 2-2 4-4 5l-1 1-1-1 1-1c1-1 2-2 2-3v-1h0z" class="T"></path><path d="M220 439c7-5 16-6 25-7v1c-5 0-10 2-15 3l-1 1-4 3-2 1c-1 0-2 0-2 1-1 0-3 1-4 1l-2-1c-6 4-13 8-17 14l-3 2-1-1 18-14 3-2c2-1 3-1 5-2z" class="j"></path><defs><linearGradient id="v" x1="223.768" y1="441.273" x2="219.106" y2="438.343" xlink:href="#B"><stop offset="0" stop-color="#c0bbbf"></stop><stop offset="1" stop-color="#c9cdc9"></stop></linearGradient></defs><path fill="url(#v)" d="M215 442h0c4-2 10-5 15-6l-1 1-4 3-2 1c-1 0-2 0-2 1-1 0-3 1-4 1l-2-1z"></path><path d="M209 455h0c2-1 5-3 7-4h1c-1 2-2 2-2 4 0 1 0 2 1 4l-4 2s-1 0-1 1l-3 2c-1-1-1-2-2-2l2-2s1 0 1-1h0l-3 2c-4 2-7 4-10 7l-3 3v1l-1 1-1 1c0 1-1 1-2 2l-5 8h-1 0 0l-1-1c1-1 2-3 2-3 1-1 1-1 1-2v-1h1c0-1 1-2 1-3h1c0-1 1-2 2-3l3-4 3-3 1-1h0l1-1c1-1 2-2 3-2 3-1 5-3 7-4h1v-1z" class="L"></path><path d="M219 435v1c0 1 0 2 1 3-2 1-3 1-5 2l-3 2-18 14-2 2c0-1-1-1-1-2l1-2h-1l1-3h0c0-1 1-1 2-2l1-1c-1-1-1-1 0-2 0-1 1-1 2-2s1-1 1-2l3-3h2v-1l4-1h1 0c0 1 0 1 1 1l1 1 3-1c0-1 1-1 2-2s2-1 4-2z" class="F"></path><path d="M212 443v-2c1-1 2-2 4-2v1l-1 1-3 2z" class="B"></path><path d="M207 438h1l-3 3s-1 1-2 1h0v-3l4-1z" class="f"></path><path d="M192 452c0-1 1-1 2-2 0 0 0 1 1 1 0 1-1 1-2 2 0 1 0 2-1 2h-1l1-3h0z" class="B"></path><path d="M199 451c-2 2-4 4-6 5 1-1 1-2 2-3 1-2 1-2 3-4l1 2z" class="l"></path><path d="M198 449c1 0 1-1 2-1v-1l2-2c1 0 2-1 3-2h1c-1 1-2 2-3 2v1h2 0c-2 2-4 3-6 5h0l-1-2z" class="T"></path><path d="M261 449c6 1 12 3 17 5v1l3 1c1 0 1 1 2 2h0l-6-2c-1 0-2-1-3-1l-1 1h-2c-1 0-1 0-1 1h-1v1c-4-1-7-2-11-2v1h-4c-3-1-6 0-8 0l-3 1c0-1 0-1-1-1v-1c-1 0-1 0-1-1h-1l-2 1-1-2h1 0-1v-1l4-1h2v-1s-1 0-1-1h6 11l-1-1h3z" class="D"></path><path d="M255 451l10 1c-1 1-1 1-1 2l-5-1h-5c-1-1-2-1-3-2h4z" class="V"></path><path d="M242 450h6c2 2 5 0 7 1h-4c1 1 2 1 3 2h-1-8v-1h-2v-1s-1 0-1-1z" class="Z"></path><path d="M242 450h6c2 2 5 0 7 1h-4-8s-1 0-1-1z" class="M"></path><path d="M265 452l9 3-1 1h-2c-1 0-1 0-1 1h-1l-3-1c0-1 0-1 1-1l-3-1c0-1 0-1 1-2z"></path><path d="M267 455c1 0 3 0 4 1-1 0-1 0-1 1h-1l-3-1c0-1 0-1 1-1z" class="c"></path><path d="M261 449c6 1 12 3 17 5v1c-2 0-4-1-6-2-4-2-9-3-13-3l-1-1h3z" class="Y"></path><path d="M241 452h2 2v1h8 1 5l5 1 3 1c-1 0-1 0-1 1l3 1v1c-4-1-7-2-11-2v1h-4c-3-1-6 0-8 0l-3 1c0-1 0-1-1-1v-1c-1 0-1 0-1-1h-1l-2 1-1-2h1 0-1v-1l4-1z"></path><path d="M242 456h3l1 1-3 1c0-1 0-1-1-1v-1z" class="W"></path><path d="M245 456c4-1 9-1 13 0v1h-4c-3-1-6 0-8 0l-1-1z" class="Y"></path><path d="M254 453h5l5 1 3 1c-1 0-1 0-1 1-2 0-5-1-7-1l-12-1c2 0 6 0 7-1h-1 1z" class="W"></path><path d="M259 453l5 1 3 1c-1 0-1 0-1 1-2 0-5-1-7-1l1-1c1 0 2 1 3 0h-1c-1 0-2 0-3-1h0z" class="Y"></path><path d="M241 452h2 2v1h8 1c-1 1-5 1-7 1l-6 1h-1l-2 1-1-2h1 0-1v-1l4-1z" class="a"></path><path d="M237 454h1c1 0 1 0 2 1l-2 1-1-2z" class="G"></path><path d="M241 452h2 2v1h0l-7 1h-1v-1l4-1z" class="Z"></path><path d="M241 452h2 2v1h0c-2 0-3 0-4-1z" class="h"></path><path d="M222 459v-1c4-2 10-5 15-5v1h1 0-1l1 2-5 1-6 3-4 2-2 1c-7 4-14 10-19 16-1 0-1-1-2-1h-1v1c-1 1-1 1-3 2-3 3-7 7-9 12l-1-1 1-1v-2h0-1 0-1 0-1c-2 3-3 6-4 9l-1-1c0-1 1-2 1-3l-1-1c1-1 1-1 1-2 0-2 1-3 2-4l1-3h0 1l5-8c1-1 2-1 2-2l1-1 1-1v-1l3-3c3-3 6-5 10-7l3-2h0c0 1-1 1-1 1l-2 2c1 0 1 1 2 2l3-2c0-1 1-1 1-1h2l6-3h2v1z" class="K"></path><path d="M184 489c1-1 1-2 2-3h0c1 0 2-2 2-2 1-1 1-2 2-2v-1l2-2v1h0l-3 4c0 1-1 1-1 1v3h1l-2 3v-2h0-1 0-1 0-1z" class="B"></path><path d="M212 461h2l6-3h2v1c-2 1-5 2-7 3l-4 4-6 4v-1h-1s0 1-1 1-2 0-2 1l-1 1c-2 1-4 3-5 5h-1v-1l6-6h0l4-4c2-1 3-2 4-2l3-2c0-1 1-1 1-1z" class="P"></path><path d="M222 459v-1c4-2 10-5 15-5v1h1 0-1l1 2-5 1-6 3-4 2-2 1c-7 4-14 10-19 16-1 0-1-1-2-1h-1v1c-1 1-1 1-3 2-3 3-7 7-9 12l-1-1 1-1 2-3 16-18 6-4 4-4c2-1 5-2 7-3z" class="i"></path><path d="M219 461c1 0 2 0 3-1 0 1 1 1 1 2l-2 1c0-1-1-1-2-2z" class="D"></path><path d="M222 460c1-2 5-2 6-3l9-3 1 2-5 1-6 3-4 2c0-1-1-1-1-2z" class="N"></path><defs><linearGradient id="w" x1="214.377" y1="459.433" x2="205.63" y2="480.199" xlink:href="#B"><stop offset="0" stop-color="#9e9d9a"></stop><stop offset="1" stop-color="#cac8cf"></stop></linearGradient></defs><path fill="url(#w)" d="M196 481c5-6 10-11 15-15 3-2 5-3 8-5 1 1 2 1 2 2-7 4-14 10-19 16-1 0-1-1-2-1h-1v1c-1 1-1 1-3 2z"></path><path d="M120 113c1-4 1-9 1-14l-1-9c0-1-1-4 0-5h1 0 0l1 1c4 1 10 0 15 0h31 108 111 35c5 0 11 1 15 0h42 85 19 7 3v37 70c0 16 1 33-2 49-1 8-5 16-9 22l-5 5c-3 3-7 6-11 9-8 5-18 7-28 7-7-1-12-3-18-5l-4-2-6-4v1c-4-3-7-7-8-12-3-8-2-16 2-23 3-8 11-13 19-16 7-2 15-1 21 3 8 4 11 11 14 19 0-6 1-11 1-17-1-9-3-18-6-27l2-2-1-3h-2c-5-9-10-18-17-25-5-5-11-10-17-14-12-7-28-12-42-14-17-2-35-2-52-2v16 260 74c0 17-1 35 2 52s9 34 21 47c22 21 56 20 84 20l29-1c1 0 1 0 1 1-1 0-1-1-2-1v1c1 1 0 3 0 4l-1 1v22c-3 1-7 1-10 1h-23-209-52-14-4-8-12l-49-1-1-1h0l-1-5v-11-1h-1c-5-1-11-2-17-4-5-1-10-3-15-5-7-3-13-7-19-11-10-7-19-15-27-24-1-1-2-2-2-3-4-4-7-9-10-13l1-1v-1h1l1 1 1 1h0 1c-2-4-4-9-6-13s-3-7-5-11c1-1 1-1 1-2 3 6 5 13 8 19 3 7 8 14 13 20 5 7 11 13 18 18 0-1 0-1 1-2l4 3c1 0 1 0 2-1 3 2 7 4 10 6 13 7 27 13 42 16v-1c4 1 8 1 12 3h8c2 0 4 0 6 1 17 0 33-2 49-6l15-6c17-9 32-22 42-40v-1-1l1-1v-1c-1 2-2 3-3 4l5-10c1-2 1-3 1-4 0-2 0-5 1-7h1l2-11 1-2v-22c-1-17-5-36-16-50l-1 1h0c1 1 2 1 2 3-1-1-2-3-3-4l-3-4-2-1c-1-3-4-4-7-6-1 0-1-1-2-1-1-1-2-1-2-1s1 0 2-1h-1v-3h-1c0-1 0-2-1-3h0-2l-2-2c-1 1 0 1-1 1v-1c-1 0-2 0-3-1h-1-2l-8-2-1 1-2-1h-2 0-14v-1c-9 1-18 2-25 7-1-1-1-2-1-3v-1c-2 1-3 1-4 2s-2 1-2 2l-3 1-1-1c-1 0-1 0-1-1h0-1l-4 1v1h-2l-3 3c0 1 0 1-1 2s-2 1-2 2c-1 1-1 1 0 2l-1 1c-1 1-2 1-2 2h0l-1 3h1l-1 2c0 1 1 1 1 2h0c-3 3-5 6-7 9-1 2-2 3-3 5-2 4-4 7-6 10-1 2-1 5-2 7s-2 4-2 6c0 1-1 2-1 2l-1-1h0v-1h-1l-1 4v1c-1 1-1 1-1 2l-1 1c0-2 0-3-1-4h0v-1-6l-1-16c0-7 2-14 3-21l2-7c1-2 1-4 2-7 0-2 2-5 1-7l-3-1c0-2 1-3 1-5 1-1 1-1 1-2l-1-1v-1l2-6c1-1 1-2 2-4h0l9-28 1-4 9-21v-4c1 0 1-1 1-1 1-1 2-1 2-1 0-1 1-2 2-3-1-1-1-1-1-2l-1-1c1-2 2-3 2-4 2-4 5-8 8-11 1-3 5-5 5-8h0l-1-1c1-1 2-2 3-2 4-6 10-10 16-15 3-2 5-4 7-6 4-3 8-6 11-10h0c1 0 4-3 4-4v-1-6c-1-2-1-5 0-8 0-2 1-5 2-7 1-5 2-11 5-15l3-3c1-2 2-3 3-5 2 0 2-1 3-2v1l1-1c1-3 5-6 7-8l1-1 1-1 2-1c0-1 0-1 1-2h0c2-1 3-1 5-2h3v1h2l1 1h2v1c1 0 1-1 3 0v-1l-4-4-9-10c-5-6-10-14-17-17 0-1-2-1-2-2 1 0 3 0 4 1 5 2 10 7 14 10l3 1h1c3 1 9 0 13-1h3c3 0 7 0 10-1h1l1-40h-1v-24h-1c-8-1-16 0-24 0-21 0-42 2-63 7-18 4-35 11-51 23-4 4-8 8-11 14-10 14-17 30-18 48l1 1c4-8 10-13 19-15 8-2 17 0 24 4 6 4 11 10 13 17 1 5 1 10 0 15v2h-2l-2 4-1 1c-1 2-3 4-5 6-10 8-21 11-34 10-14-2-29-9-39-20-3-3-5-7-8-10h0l-5-8c-6-15-8-34-4-50 2-8 5-15 8-23 3-12 7-23 9-35 1-5 2-10 2-15v-4l-1-1z"></path><path d="M228 623l2 1h0l-1 1h-2c0-1 0-1 1-2z" class="N"></path><path d="M116 215h2c0 1 0 2-1 2s-1-1-1-2z" class="g"></path><path d="M328 195l1 2c0 1 0 1-1 2l-1-2 1-2z" class="B"></path><path d="M285 100l4 1-2 2-2-1h0v-2z" class="X"></path><path d="M146 111l4-2-2 3h0 0c-1 0-2-1-2-1z" class="H"></path><path d="M418 105l-1-1c1-1 2-1 3-1v1h4l-6 1z" class="W"></path><path d="M333 112h2l1 1c-2 0-3 1-4 1h0l1-2h0z" class="R"></path><path d="M507 101l3 1v1h-1-2-1v-1l1-1z" class="X"></path><path d="M327 483c0-1 1-2 2-2l1 1c-1 1-2 1-3 2v-1z" class="N"></path><path d="M137 115l1 1-2 3-1-1c0-1 1-2 2-3z" class="f"></path><path d="M384 296v4s0 1 1 1v2c0 1 0 1-1 1h0 0v-3-5z" class="G"></path><path d="M234 112h2v1l-1 1h-1-1c1-1 0-1 1-2z" class="H"></path><path d="M239 114c1 1 1 1 1 2l-2 4c0-2 1-4 1-6z" class="M"></path><path d="M353 519c0 1 0 0 1 1h0c0 1-1 2-1 3h-1l1-4z" class="j"></path><path d="M407 236c1 0 1-1 1-2 0 3 0 4-1 7l-1-2 1-3z" class="F"></path><path d="M463 126l1-1c1 0 1 0 2 1v1c-2 0-2 0-3-1z" class="D"></path><path d="M222 624s1-1 2-1h0 4 0c-1 1-1 1-1 2l-5-1z" class="H"></path><path d="M384 262v-2c2-1 2-1 3 0v1h-2l-1 1z" class="b"></path><path d="M409 503l1 1-1 4v-2c-1 0-1 0-2 1l2-4z" class="E"></path><path d="M466 127v1c-1 1-2 1-3 1v-1c-1-1-1-1 0-2 1 1 1 1 3 1z" class="U"></path><path d="M358 207c1-1 1-1 2-1s1 0 2 1v1l-2-1v1c-1-1-1-1-2-1z" class="j"></path><path d="M404 108c1 1 3 1 5 1l-1 2-2-1h-3v-1l1-1z" class="E"></path><path d="M354 514c1 1 1 1 1 2l-1 4h0c-1-1-1 0-1-1l1-5z" class="T"></path><path d="M311 113l1-1v1 1c1 0 1 0 2 1h-2v2 1l-1-2v-3z" class="M"></path><path d="M224 100c2 0 2 0 4 1l-5 2v-2l1-1z" class="G"></path><path d="M349 292l3 3c-1 0-2 1-3 0s-1-2-2-2c0 0 1 0 2-1z" class="V"></path><path d="M123 160v-1c-1-2 0-4 1-6v1l1 2-2 4z" class="Z"></path><path d="M577 189l1 3-1 1v1l-2-3 2-2z" class="X"></path><path d="M401 292h2c0 2 0 2-1 3h-1l-1-1c0-1 0-1 1-2z" class="L"></path><path d="M344 616h0 1l3 3-1 1c-1-1-2-3-3-4z" class="R"></path><path d="M343 117h1l1 1-5 2h-1l1-1h-1l1-1 3-1z" class="M"></path><path d="M442 110h0c2 1 4 1 6 2-2 0-5-1-7 0 0-1 0-2 1-2z" class="Y"></path><path d="M556 118c1 0 1 0 2 1l-2 5v-2h0l-1-1c0-1 1-2 1-3z" class="E"></path><path d="M218 104c1-1 3-2 5-3v2c-2 1-3 1-5 1z" class="j"></path><path d="M315 105l4-1v1l1 1-3 1-2-2z" class="C"></path><path d="M136 99c0-1 0-1 1 0l-2 3v1c-1-1-1-2-1-4h2zm245 177h1c1 1 0 4 0 5h-1v-1c-1-1 0-3 0-4z" class="E"></path><path d="M374 212h2v3h-1s-1 0-1-1c-1-1-1-1 0-2z" class="D"></path><path d="M345 125h1 1c0 2-1 2-1 3-1 0-2-1-2-1v-1l1-1z" class="C"></path><path d="M365 199v1 9h1v3h-1l-1-2 1-11z" class="B"></path><path d="M559 113c1 0 1-1 2-1h1c-1 2-2 3-3 4-1 0-1 0-1-1s0-1 1-2z" class="H"></path><path d="M348 376l2 1c-2 1-3 3-4 5l-1-1 3-5z" class="L"></path><path d="M387 260c1 1 1 1 0 2l-2 2-1-1v-1l1-1h2v-1z" class="P"></path><path d="M440 109l2 1c-1 0-1 1-1 2-2 0-4-2-5-3h2 0 1 0 1z" class="W"></path><path d="M355 508l2 2-1 1-1 5c0-1 0-1-1-2 1-2 1-4 1-6z" class="H"></path><path d="M305 610c3-2 6 0 8 1h-3-2c-1-1-2-1-3-1z" class="D"></path><path d="M325 180l1 6-1 1v2-3c0-2-1-5 0-6z" class="B"></path><path d="M116 215c-1-2 0-3 0-5h1c1 1 1 3 1 5h-2z" class="e"></path><path d="M252 616l2-1 2 2c-3 1-5 2-8 2h-1l1-1h1c1 0 3 0 4-1l-1-1z" class="F"></path><path d="M365 195c2 2 2 4 2 6h-1l-1 1v-2-1l-1-2c1-1 1-1 1-2z" class="C"></path><path d="M572 114c1-1 1-1 2-1l1 1c-1 1-1 3-2 4-1-1 0-1 0-2 0 0-1-1-1-2z" class="F"></path><path d="M339 152c1 2 1 4 2 6l-2 1c-1-2-1-4-1-6l1 2h0v-3z" class="X"></path><path d="M165 243c-2 1-6 2-9 2l6-3c1 1 2 1 3 1z" class="W"></path><path d="M544 624c2-1 2 0 3 0 1 1 1 2 1 4h-1c-1-1-2-3-3-4z" class="X"></path><path d="M384 222c1-2 3-4 4-6 0 2 0 3-1 5 0-1 0 0-1-1 0 1 0 2-1 2h-1z" class="T"></path><path d="M396 368h1l1 1c-1 2-1 2-2 3h-1c0-1 0-1-1-2l2-2z" class="C"></path><path d="M143 113l3-2s1 1 2 1h0c-2 1-3 2-4 3-1 0-1-1-1-2z" class="N"></path><path d="M371 432l5-2c0 1 0 2 1 2-2 1-3 1-5 1-1 0-1 0-1-1z" class="R"></path><path d="M345 498h1c1 1 1 1 1 2s0 1-1 2l-2-1h0c0-1 1-2 1-3z" class="K"></path><path d="M327 483v1l1 3c0 1-1 1-2 2v-1c-1-2 0-4 1-5z" class="U"></path><path d="M288 626h1c1 1 1 1 1 2l-1 1s-1 0-2-1c-1 0-1 0-1-1l2-1z" class="B"></path><path d="M289 101l5 1h0v1h0-7l2-2z" class="C"></path><path d="M378 546h4 0l-2 2c0 1-1 1-1 1-1-1-1-1-1-2v-1z" class="L"></path><path d="M365 200v2l1-1h1c-1 3-1 5-1 8h-1v-9z" class="P"></path><path d="M390 495h0 5 1v1l-7 2c1-2 1-1 2-2h-3l2-1z" class="G"></path><path d="M349 374c0 1 0 0 1 1 1-2 1-2 3-2-1 1-1 3-3 4l-2-1 1-2z" class="b"></path><path d="M381 176v1 1c2 1 3 1 5 0 1 0 1 0 1 1-1 0-2 1-3 1-2 0-2 0-4-1 0-1 0-2 1-3z" class="G"></path><path d="M335 173c-1-1 0-2 0-2 0-1 0-2 1-2 1-1 1-1 2-1 0 1 1 2 1 3h-3l-1 1v1z" class="g"></path><path d="M349 486h1l1 1c2 0 3 2 5 2h-4 0 0c-1-1-1-1-2-1l-1-1v-1z" class="V"></path><path d="M361 321c1 2 3 6 3 8l-4-6-1-1h1l1-1z" class="e"></path><path d="M397 192h0c0 1-1 3-2 3 0 1-1 1-2 1l-1-1c1-1 3-2 5-3z" class="H"></path><path d="M147 131c1-1 2-1 3-2l1 1c0 1-1 1-2 2l-2 1c-1-1-1-1-1-2h1z" class="M"></path><path d="M574 165h1c1 1 1 2 1 3l-1 1h0c-1 0-2-1-2-2s0-1 1-2z" class="C"></path><path d="M182 140l1 1-7 5h0 0l1-1-1-1 6-4z" class="E"></path><path d="M404 420h6c-1 1-2 2-4 2h-2-1v-2h1z" class="D"></path><path d="M403 420h1 0c1 1 1 1 2 1v1h-2-1v-2z" class="F"></path><path d="M393 280v-2l2-2v-1 1h2v1h4-4c-1 1-3 2-4 3z" class="Q"></path><path d="M145 131h1c0 1 0 1 1 2l-2 1-3 1h0l-1-1c2-1 3-2 4-3z" class="H"></path><path d="M145 131h1c0 1 0 1 1 2l-2 1v-3z" class="U"></path><path d="M409 109l4 1v3l-5-2 1-2z" class="F"></path><path d="M393 549v-2h1v1c1 0 1 0 2 1s1 1 1 2c-1 1-1 1-2 1h-1l1-2h-1-1v-1z" class="J"></path><path d="M365 272l10-1-1 1h1l-8 1h-2v-1zm11 186c4 0 9-1 12 0h-5v1 1 1l-1-1c-1-1-2-1-3-1h-1c0-1-2-1-2-1zm26-187c2 2 4 3 5 5 0 0-1 0-1-1-1-1-3-1-4-1h-2-1l1-1c0-1 1-1 2-2z" class="E"></path><path d="M335 172c1 1 1 1 2 1l2-1h0c0 1 1 1 0 3l-1 1c-2-1-2-1-3-2v-1-1z" class="X"></path><path d="M138 190l1-1c0 1 1 1 1 2s0 1-1 2h0c-1 0-1 0-2-1 0-1 0-1 1-2z" class="D"></path><path d="M352 489h4c1 1 2 1 4 2h1v1h-1c-3-1-6-1-9-2l1-1z" class="I"></path><path d="M392 249v-2c0-1 0 0 1-1 2 1 2 1 3 2 0 0 0 1-1 1l-1 1c-1 0-1 0-2-1z" class="D"></path><path d="M392 249l1-1h0 2v1l-1 1c-1 0-1 0-2-1z" class="E"></path><path d="M384 161c2 0 2 0 3 1 0 1 0 1-1 2h-1c-1 0-1 0-2-1v-1l1-1zm22 122l1-2c1 3 3 5 3 8v1l-1-1c0-3-1-4-3-6z" class="J"></path><path d="M392 183c1-2 4-5 5-6 0 2-1 4-1 5l-3 2-1-1z" class="l"></path><path d="M401 199l2-1v2l-2 4v1c-1 0-1-1-1-1h0l-1-1 2-4z" class="B"></path><path d="M393 163c2 0 2 0 3 1v1l-2 2c-1 0-1 0-2-1 0-2 0-2 1-3z" class="J"></path><path d="M421 103c1 0 3-1 4-1 0 1 0 1 1 1s1 1 2 1h-4-4v-1h1z" class="j"></path><path d="M251 117c-1-2-1-3-2-5h1c1 0 1 1 2 1 0 1 1 2 1 3-1 0-1 1-2 1h0z" class="G"></path><path d="M544 624h0l-4-4c2 0 4 1 6 1 1 1 1 2 1 3-1 0-1-1-3 0z" class="K"></path><path d="M329 515v10c0 2 0 3-1 5 0-1 1-3 0-5h-1l1-8c0 1 0 2 1 3v-5z" class="G"></path><path d="M311 615c-2-1-4-3-6-4l-2-1h1 1c1 0 2 0 3 1v1l3 1c1 1 0 1 1 1l-1 1z" class="Q"></path><path d="M403 195c1 0 1-1 2-1v-1h1l-3 7v-2l-2 1 2-4z" class="F"></path><path d="M394 461c1 0 3 1 4 3h-1-3c-1-1-2-1-2-2 1 0 1-1 2-1z" class="Y"></path><path d="M382 382h1l1 2c0 1 0 3-1 4-1 0-1 0-2-1v-1c0-1 0-2 1-4z" class="G"></path><path d="M230 624c3 0 8 0 11 2l-12-1 1-1h0z" class="H"></path><path d="M142 148h1c1 1 1 1 1 2s0 1-1 2h-2c-1-1-1-1-1-3 1 0 1-1 2-1z" class="e"></path><path d="M510 102c2 1 5 3 6 4l1 2-4-3c-1 0-3-1-4-1v-1h1v-1z" class="K"></path><path d="M336 133h2v2c0 1-1 1-2 2h-1c-1-1 0-1-1-2 1-1 1-2 2-2z" class="C"></path><path d="M357 272h8v1h2l-9 1v-1h-1v-1z" class="B"></path><path d="M348 619l4 1h3v1c-3 0-5 1-8-1l1-1z" class="F"></path><path d="M391 473c2 2 3 5 3 7l1 2c-2-3-3-5-6-6v-1c1 0 2 0 2-2z" class="N"></path><path d="M557 111h1l1 1v1c-1 1-1 1-1 2s0 1 1 1l-1 3c-1-1-1-1-2-1l1-7z" class="U"></path><path d="M559 157c1-1 1-1 1-2l4 6c0 1 0 1 1 2l-1 1-5-7z" class="L"></path><path d="M393 507c1-1 1-1 2 0 0 2 0 3-1 4h-1l-1-1v-1c0-1 0-2 1-2z" class="E"></path><path d="M524 134l8 8h-2 0 0c-1-1-2-1-2-2-1-2-3-4-5-5l1-1z" class="j"></path><path d="M577 111h0 3c0 2 0 2-1 4l-1 1h-1v-5z" class="X"></path><path d="M405 188v3c0-1 0-1 1-2h1c0 1 0 3-1 4h-1v1c-1 0-1 1-2 1l2-7z" class="G"></path><path d="M354 220h1c1 0 2 1 3 1h1l-1 1v2c-2 0-3-2-4-2s-2 0-2-1h1s1 0 1-1z" class="M"></path><path d="M354 220h1v1h-2s1 0 1-1z" class="D"></path><path d="M159 106h2c1 0 1 0 1 1s0 1-1 2l-1 1-2-2c0-1 0-1 1-2z" class="C"></path><path d="M381 411c1 1 2 1 2 2l-2 2h-1c-1-1-1-1-1-2s0-1 2-2z" class="B"></path><path d="M332 111c2-1 4-2 6-2 1 0 1 0 1 1s-3 2-3 3l-1-1h-2v-1h-1 0z" class="H"></path><path d="M392 494c1-1 2-2 4-2 1 0 2 1 3 1 1 1 1 3 1 4h-1-1v-1c0-1 0-2-1-3-1 0-3 1-5 1h0z" class="B"></path><path d="M135 118l1 1-4 9h-1v-3l4-7z" class="W"></path><path d="M390 459l4 2c-1 0-1 1-2 1l-2-1v1s-1 0-1 1l-2-1h0l3-3z" class="F"></path><path d="M363 367l2-1c0 1 1 1 1 2s0 2-1 3h-1c-1-1-1 0-2-1 0-2 1-2 1-3z" class="B"></path><path d="M570 108c0-2 1-4 2-6 2-1 3-2 4-2s2 0 3 1l-1 1c-1-1-2-1-4 0h0l-2 2c-1 1-1 3-2 4z" class="C"></path><path d="M352 523h1v1l-1 2c-1 2-2 6-4 8 0-1 3-9 4-11z" class="W"></path><path d="M134 160l1-1 2 2c0 1-1 2-2 3h-1c-1-1-1-1-1-2 0-2 0-2 1-2z" class="E"></path><path d="M375 124c4 0 7 1 10 3h0v1h-2c-1 0-1-1-2-1l-6-3z" class="T"></path><path d="M343 507h3c0 2-3 4-4 6v-3c0-2 0-2 1-3z" class="M"></path><path d="M325 189v-2l1-1c0 3 1 6 2 8v1h0l-1 2c-1-2-2-5-2-8z" class="k"></path><path d="M357 268c2 0 3-3 4-4 0 2-1 4-3 7h-2-1l1-1 1-2z" class="Y"></path><path d="M399 349h1c1 1 1 1 1 2 0 2 0 2-1 3h-2c0-1-1-1-1-2 1-2 1-2 2-3z" class="C"></path><path d="M130 123c0-2 2-6 3-8h1l-3 9h-1v-1z" class="Y"></path><path d="M380 291l4 2c1 1 1 1 2 1l-1 7c-1 0-1-1-1-1v-4-2l-4-2v-1z" class="H"></path><path d="M261 104c-3-2-7-4-10-6 4 1 8 2 12 4l-2 2zm131 390h0c2 0 4-1 5-1 1 1 1 2 1 3v1l-2-1v-1h-1-5 0l2-1z" class="T"></path><path d="M137 115h0c1-3 3-4 5-6h1c-1 1-2 2-3 4-1 1-1 3-2 3l-1-1z" class="i"></path><path d="M383 232c2 2 2 4 3 6l1 1h-2 0 0v2h-1c-1-2-1-6-1-9z" class="O"></path><path d="M297 454h2l3 4-1 1h0c1 1 2 1 2 3-1-1-2-3-3-4l-3-4z" class="k"></path><path d="M181 258c1-1 1-1 1-2h1 4v1c-1 1-2 2-4 2 0 0-1 0-2-1z" class="M"></path><path d="M366 396h1l2 2c0 1 0 1-1 2h-1c-1 0-2-1-2-2s0-1 1-2z" class="C"></path><path d="M372 190c3 2 7 4 11 3l1 1h-1c-4 1-9-1-12-3l1-1z" class="c"></path><path d="M129 174h1c1 1 1 1 1 2 0 2-1 3-2 4l-1 1v-1c0-2 0-4 1-6z" class="P"></path><path d="M387 462l2 1c0-1 1-1 1-1v-1l2 1c0 1 1 1 2 2-1 0-1 1-2 2 0-1-2-1-3-2h-4l2-1v-1z" class="h"></path><path d="M379 201h1c1 0 2 1 3 2l-2 2h-2c-1 0-1 0-1-1 0-2 0-2 1-3z" class="b"></path><path d="M523 257h2c0 2 1 4 1 7 0-1-1-1-2-1-1-1-1-1-1-3v-3z" class="B"></path><path d="M405 253h1c1 0 2 0 2 1v2h-2c-1 0-2 0-3-1l2-2z" class="E"></path><path d="M328 530v6c0-2 0-3-1-5 0-2-2-5-1-7h1v1h1c1 2 0 4 0 5z" class="M"></path><path d="M189 615h8c2 0 4 0 6 1h-9c-3 0-5 1-8 0v-1h3z" class="f"></path><path d="M272 121l1-1c2 0 2 0 3 1-1 1-3 2-4 4-2 0-3 1-4 1h0-1 0c0-1 1-1 1-1 1 0 1 0 1-1h1c1 0 2-1 3-2l-1-1z" class="h"></path><path d="M384 335c0 5 1 10 0 15-1-3-2-10-1-13h0l1-2z" class="f"></path><path d="M510 122s1 0 2 1h1c2 1 3 2 5 4-2 1-3-1-4-2h-2l-3-1 1-1v-1z" class="G"></path><path d="M314 115l1-1c0 2 0 2 1 4h0 1c1 1 2 0 3 0h0c1 0 2-1 3-1l-1 1c-1 1-3 2-5 1-1 0-2-1-4-1l-1-1v-2h2z" class="Z"></path><path d="M563 116s1 1 2 1v1c-1 1-1 2-2 3h-2c-1-1-1-1-1-2 1-1 1-2 3-3z" class="H"></path><path d="M340 142h1c-1 4-1 7-2 10v3h0l-1-2v-6c1-2 1-4 2-5z" class="g"></path><path d="M534 264c1 1 1 1 2 1v3c-1 1-1 1-2 1h-3v-1l1-1s1-1 2-1v-2z" class="B"></path><path d="M534 264c1 1 1 1 2 1v3c-1 0-2-2-2-2v-2z" class="E"></path><path d="M354 268h0 1v1l1 1-1 1-1 1h0c1 1 2 0 3 0v1h-6c0-1 0-2 1-4h2v-1z" class="S"></path><path d="M169 104c1 0 2 0 2 1 1 1 1 2 0 4h-1c-1 0-1 0-1-1-1 0-1-1-2-2l2-2z" class="X"></path><path d="M386 361c-2-2-1-6-1-9h0c0-2 1-6 2-7h0c0 1 0 2-1 2v1 3 3 1h0c1 2 0 4 0 6zm-64-243l1 1c-1 0-2 1-3 2h1-2c-3 0-4-1-6-3 2 0 3 1 4 1 2 1 4 0 5-1z" class="l"></path><path d="M349 188h1c1 1 1 2 0 3s-1 1-2 1-1 0-1-1c-1 0-1 0-1-1 1-1 2-2 3-2z" class="E"></path><path d="M224 100c4-2 9-2 13-2l-9 3c-2-1-2-1-4-1h0z" class="N"></path><path d="M359 381h1l2 2c-1 1-1 2-2 3h-1c-1-1-2-1-2-2 0-2 1-2 2-3z" class="J"></path><path d="M288 447c2 0 2 0 3 1 1 0 1 0 1 1l2-1c2 2 3 4 5 6h-2l-2-1c-1-3-4-4-7-6zm285-215h2c0 2-2 5-2 7l-2 6v1h0c0-2 1-4 1-7 0-2 0-5 1-7z" class="B"></path><path d="M351 538c0 1 0 1 1 2-1 1-5 7-5 7-1-1-1-1-2-1l6-8z" class="E"></path><path d="M378 513h1c1 1 1 2 1 3-1 1-1 2-2 3l-1-1c-1-1-1-1-1-2s1-2 2-3z" class="L"></path><path d="M330 482c1 1 3 3 3 4v1c-2 0-2-2-3-3h0-1c0 1 1 3 2 4v1l-2-1v1c-1-1 0-2-1-2l-1-3c1-1 2-1 3-2z" class="a"></path><path d="M388 105l13-6v2c-4 1-8 3-12 5l-1-1z" class="E"></path><path d="M151 156l1 1c-2 2-5 6-6 9l-1-1v-1h0-1c2-3 4-5 7-8z" class="K"></path><path d="M221 107h2c1 0 1 1 2 2l-2 2h-3v-1c0-2 0-2 1-3z" class="P"></path><path d="M378 112c3-3 7-6 10-7l1 1c-2 1-4 2-5 3v1l-4 2h-2z" class="W"></path><path d="M474 123s1 1 2 1c3 3 7 5 10 8-3-1-5-3-7-4l-7-4 2-1z" class="F"></path><path d="M555 149c2 2 4 4 5 6 0 1 0 1-1 2l-5-7c0-1 1-1 1-1z" class="K"></path><path d="M402 533h1c1 0 2 2 4 3h-1-1c-2 0-5 0-6-1 1-1 2-2 3-2z" class="F"></path><path d="M369 125l1-1h5l6 3v1l-2-1c-2-1-6-2-8-1v1c-1 0-1 0-2-1h0v-1z" class="a"></path><path d="M381 176c0-1 1-2 2-3 1 1 2 2 2 3 1 0 1 1 1 2h0c-2 1-3 1-5 0v-1-1z" class="S"></path><path d="M329 197c3 4 6 7 10 10-4-2-8-4-11-8 1-1 1-1 1-2z" class="J"></path><path d="M365 111c2 3 5 6 5 9h0v-1c-2-2-4-3-6-4 0-1-1-2-2-2l3-2z" class="M"></path><path d="M126 223c0 5 1 10 3 15-1-1-2-4-3-5 0 0-1-1-1-2h0l-1-3 1-2c0-1 0-2 1-3z" class="K"></path><path d="M526 264c1 0 1 0 1 1-1 0-1 1-2 1-1 1-3 1-4 0h0c1-2 1-3 2-6 0 2 0 2 1 3 1 0 2 0 2 1z" class="D"></path><path d="M379 396h2l2 2c-1 2-1 2-2 3h-2l-2-2c1-1 1-2 2-3z" class="F"></path><path d="M364 351h2c0 1 1 1 1 2 0 2-1 3-2 4h-1c-1-1-1-2-1-4 0-1 1-1 1-2z" class="E"></path><path d="M533 111c2 1 3 2 4 3 0 1 0 2-1 3h-1c-1 0-1 0-2-1 0-1 0-2-1-3 1-1 1-1 1-2z" class="P"></path><path d="M394 470c1 1 2 3 3 5 0 1 1 3 0 4 0 0-1-2-1-3-1-1-3-3-4-3h-1 0c-1 0-1-1-2-1h1 2v-1s1 0 1-1h1z" class="H"></path><path d="M387 345c0-2 0-4 1-5v6l-1 11c0 1 0 3-1 4 0-2 1-4 0-6h0v-1-3-3-1c1 0 1-1 1-2h0z" class="W"></path><path d="M169 246c-1 0-3-1-4-1 1-1 3-2 5-2v1h1c1 0 2 0 3 1v1h-1s-1 0-1 1l-3-1z" class="M"></path><path d="M169 246l1-1c1 0 2 0 3 1 0 0-1 0-1 1l-3-1z" class="E"></path><path d="M372 527c2 0 2 0 3 1s1 1 1 2c-1 1-1 1-3 2h-1c-1-1-2-2-2-3l2-2z" class="B"></path><path d="M407 161c0-3 0-7-1-10 0-1-1-4-1-5h1c1 0 1 4 1 5s0 1 1 2h0c1 4 0 8 0 11h0c0-1 0-2-1-3z" class="G"></path><path d="M353 209c1-2 4-3 5-4s2-3 4-4h0c0 1 0 1-1 2h0l1 1h0c0 2-1 1-2 1s-2 1-3 2h1c1 0 1 0 2 1h-1c-1 0-2 1-3 0-1 0-1 1-2 2l-1-1zm178 60h0c-1 1-3 2-4 2v-1c0-2 3-4 5-6h2v2c-1 0-2 1-2 1l-1 1v1z" class="N"></path><path d="M366 112c1-1 3 0 4-1h0c-1-1-1-1-2-1 0-1 1-1 2-1s2 1 3 2l-3 3h0c1 0 1 1 1 1v1c-2-1-4-3-5-4z" class="D"></path><path d="M176 146c-2 3-4 4-8 4v-1c2-2 5-4 8-5l1 1-1 1h0 0z" class="F"></path><path d="M358 403h2l1 1c1 0 1 1 1 2-1 1-1 2-2 2h-2c-1-1-1-1-1-2s0-2 1-3z" class="R"></path><path d="M194 97c1 0 3-1 4 1 1 0 1 1 1 2v1l-2-1-3-1h-3 0c1-1 2-2 3-2z" class="g"></path><path d="M572 154c-2-2-5-4-5-7v-1l2 1h0c2 2 4 4 5 6l-2 1z" class="S"></path><path d="M401 99c4-1 8-1 12-2v1h-1l1 1c-1 0-2 1-3 0h-1c-2 0-5 1-8 2v-2zm-204 36c3 0 5 1 7 1l-2 1c0 1 0 2-1 2-2 1-3 0-4 0-1-1-1-2-1-3s2 2 4 2l-3-3z" class="F"></path><path d="M578 158c-3-4-6-9-6-14l8 15c-1 1 0 1-1 1l-1-2z" class="B"></path><path d="M544 159l9 10h-1c-3 0-6-4-8-6 1-1 0-2 0-3v-1z" class="j"></path><path d="M322 593c4-2 8-2 11-3l1 1h6c-7 1-13 1-19 4 0-1 1-1 1-2z" class="J"></path><path d="M279 100c1 1 3 2 4 2h2l2 1h7 1c-4 0-7 2-11 1-2 0-3-1-4-1l-2-2 1-1z" class="N"></path><path d="M556 106l1-1c0 1 0 0 1 1v-2h1c1 1 1 3 1 5 0 1 0 2-1 3l-1-1h-1v-5h-1z" class="T"></path><path d="M557 106h1c1 1 0 4 0 5h-1v-5z" class="a"></path><path d="M283 102h0c-4-2-5-4-6-7 2 1 6 4 8 5v2h0-2z" class="K"></path><path d="M210 121h1v1s0 1 1 1v1l-4 4c-1 0-1 0-2-1v-1c1-1 2-3 4-5z" class="G"></path><path d="M364 481h3c1 0 1 1 2 1s3 0 4 1v-1h0c1 1 2 2 2 3-1 0-2-1-3-2 1 1 2 2 2 3-2-1-3-2-5-3h0c0 1 0 2 1 2h0v1l-2-2c-1-1-3-2-4-3z" class="T"></path><path d="M450 112v-2c2-2 8-5 10-6-2 4-7 6-10 8z" class="l"></path><path d="M163 127h2c-1 3-5 8-4 11 0 1 0 1-1 2 0 1 0 1-1 2 0-3 1-7 2-9 0-2 1-4 1-5v-1l1 1v-1z" class="B"></path><path d="M127 155h1c-2 6-3 12-4 19l-1-1c0-3 1-5 1-8 1-4 2-7 3-10z" class="G"></path><path d="M419 117c2 1 4 4 7 4 2 1 3 2 4 2l3 3v1c-4-2-7-4-10-6 0-1-4-2-4-4zm25 500l1-1c2 0 3-1 5-2l6 3c-1 0-2 0-3 1h-2l-3-1h0-1-1 0-2z" class="D"></path><path d="M177 612c4 1 8 1 12 3h-3v1c-3 0-7 0-10-1 1-1 1-1 2-1l-1-1v-1z" class="l"></path><path d="M323 117c1-1 2-1 3-2v-1c1-2 2-2 4-2-1 1-2 3-3 4-1 3-3 4-5 5h-1-1c1-1 2-2 3-2l-1-1 1-1z" class="f"></path><path d="M285 441c3 2 6 5 9 7l-2 1c0-1 0-1-1-1-1-1-1-1-3-1-1 0-1-1-2-1-1-1-2-1-2-1s1 0 2-1h-1v-3z" class="C"></path><path d="M328 487c1 0 0 1 1 2 3 2 5 6 7 10l-10-10c1-1 2-1 2-2z" class="H"></path><path d="M328 517c0-1-1-2-1-4-1-1-2-2-2-4 1-1 1-1 2-1h1c1 3 1 5 1 7v5c-1-1-1-2-1-3z" class="U"></path><path d="M391 308c2-2 3-5 6-7h0c0 3-1 7-1 10-1-1 0-2-1-3h-1l1-1h-1c0 1-1 1-1 2l-1 1-1-2z" class="P"></path><path d="M149 124c1-1 3-1 4 0v2c-2 2-5 3-7 4l-2 1c0-2 2-4 3-5s3 0 5-2h-3z" class="F"></path><path d="M263 96l-2-1v-1c6 0 13 4 18 6l-1 1c-2-1-4-1-6-2-3-1-6-3-9-3z" class="D"></path><path d="M462 112c2 0 9-2 10-1s2 1 2 2h-3l-2 1h-5s0 1-1 1l-1-3z" class="Q"></path><path d="M395 423l1-1h1 3c0 1-1 1-2 1l-1 1h2l6 3h-3c-2-1-4-3-6-2v1c-1 0-1 1-2 2h-2v1l-1-1 2-2c1 0 1 0 1-1l1-2z" class="E"></path><path d="M555 121l1 1h0v2 4l1 7s0 1-1 2l-2-10h0 1v-4-2z" class="P"></path><path d="M555 121l1 1h0v2 4-4h0l-1-1v-2z" class="k"></path><path d="M397 109l6 1h3v1h-3l-1 1-14-1c2-1 3-1 4-1 2-1 4-1 5-1z" class="G"></path><path d="M323 609v-1c-3-3-8-2-11-2 1-1 3-1 5-2 2 0 4 0 6 1l3 3h-1c-1 0-1 0-2 1z" class="H"></path><path d="M373 482l1-1c2 1 2 0 4 2v2h0-1c-1 0-1-1-2-1 1 0 1 1 1 1h0c-1 2 0 4 0 5v2l-1 1h0v-1-3l-1-1v-1-1c0-1-1-2-2-3 1 1 2 2 3 2 0-1-1-2-2-3z" class="V"></path><path d="M543 249h0c-1-1-2-1-3-2v-1c2 0 3 1 4 1l1-1 3 3c1 1 1 1 1 2h-3 0c-1 0-2-1-3-2z" class="f"></path><path d="M546 251h0c-1-1-2-2-3-4 2 1 3 2 5 2h0c1 1 1 1 1 2h-3 0z" class="V"></path><path d="M378 112h2v1h1c-1 1-1 1-2 1 0 1-1 1-1 1v1c-1 1-4 2-4 4l-4 4c2-5 5-8 8-12z" class="S"></path><path d="M367 462c1 0 3 0 5-2 1 0 2-1 3-2v2 2c-1 1-1 1-2 1v1c-2 0-5 1-6-1v-1z" class="C"></path><path d="M375 460v2c-1 1-1 1-2 1v-2l2-1z" class="X"></path><path d="M341 158c2 4 7 7 10 10l-1 1c-1-2-3-3-5-4-3-2-5-3-6-6l2-1z" class="K"></path><path d="M210 121c1-1 3-1 5 0 1 0 2 1 3 2 0 1-1 2-2 3s-2 2-2 3v-1h-1c1-2 2-3 4-4h-5v-1c-1 0-1-1-1-1v-1h-1z" class="S"></path><path d="M344 117h0c3-1 7-1 10 0 4 1 8 3 11 6h0-1c-2-2-5-3-8-4-1-1-3-1-4-1-2-1-5 0-7 0l-1-1z" class="H"></path><path d="M332 599h1c1-1 2-1 2 0 1 1 2 1 2 2-1 2-1 2-2 3-1 0-2 0-4-1 0-1-1-1-1-2 1-1 1-2 2-2z" class="C"></path><path d="M282 117l1 1c-1 0-1 0-2 1h1 0c-4 3-8 5-11 8h-1v-1l2-1c1-2 3-3 4-4 2-2 4-3 6-4z" class="a"></path><path d="M364 210l1 2h1c0 1-1 3-1 4-1 5-3 10-6 14 1-3 2-7 4-10 1-3 1-7 1-10z" class="D"></path><path d="M162 128c-1-3-1-4-1-6-1-2-1-4-1-5 1-1 1-1 3-1 0 1 1 1 1 2 0 2-1 7-2 9v1z" class="X"></path><path d="M393 550v2c0 1 1 1 1 1 1 1 2 1 3 0s2-3 2-5l1 4c-1 1-2 3-3 3-1 1-3 1-4 1l-2-2c0-2 1-4 2-5v1z" class="K"></path><path d="M269 103h0l-6-4c-2-1-4-2-6-4l1-1c2 0 7 6 10 7 2 1 4 2 7 2l-2 2c-2 0-3-1-4-2z" class="c"></path><path d="M362 480s1 1 2 1c1 1 3 2 4 3 0 1 1 1 1 3h-1 0c-1-1-1-1-2-1s-1 1-1 1l-3-3c-1-1-2-2-2-3 1 0 1-1 2-1z" class="V"></path><path d="M132 141l1 1c0-1 0-1 1-1v1h0c1-1 1-2 3-2-4 4-7 9-9 14v1h-1c2-4 3-8 4-12l1-2z" class="S"></path><path d="M388 458l2 1-3 3h0v1l-2 1h0c-1 0-2 0-3-1l1-1v-1-1-1-1h5z" class="P"></path><path d="M383 460c1 1 3 1 4 2h0v1l-2 1h0c-1 0-2 0-3-1l1-1v-1-1z" class="V"></path><path d="M325 186c-1-2-1-5-1-7 0-6 0-13 1-19v5h1l-1 10c0 1 1 4 0 5s0 4 0 6z" class="U"></path><path d="M523 255c-1 2-2 3-2 5 0 1 0 3-1 4h-1c0-1 0-2 1-3v-2c0-3 0-5 1-7 1-1 2-3 2-4l2 2-1 1c0 1-1 2-1 3v1z" class="G"></path><path d="M419 102s1 1 2 1h-1c-1 0-2 0-3 1l1 1h0c-5 0-10 1-15 0v-1h3l13-2z" class="f"></path><path d="M133 115h0c1-3 1-6 2-8 1 0 2-1 3-1 3-2 5-4 8-6-4 5-10 9-12 15h-1z" class="J"></path><path d="M300 622c1 0 3 1 4 1 2 1 3 1 4 3l-1 1h-1c-2-2-4-2-6-2-1 0-2 0-3-1 1-1 2-1 3-2zM263 96c3 0 6 2 9 3 2 1 4 1 6 2l2 2h-1c-5-1-12-3-16-7z" class="G"></path><path d="M393 184l3-2c-2 4-3 7-7 9l-5 3-1-1c1-1 2-1 2-2h0c4-2 6-3 8-7z" class="i"></path><path d="M263 102h0v-1c1 1 3 1 4 2 0 1-1 1-1 2v1h0-1c-1 0-3 0-4-1-3 0-5 0-8 1l-1-1c3-1 5-1 8-1h1 0l2-2z" class="G"></path><path d="M385 464h4c1 1 3 1 3 2l5 5-1 1-1-1c-1-1-1-2-2-2h-1-3l-1-1c0-1-1-1-1-2 0 0 0-1-1-1l-1-1h0 0z" class="E"></path><path d="M385 464h4l1 2h0l1 1v1h-3c0-1-1-1-1-2 0 0 0-1-1-1l-1-1h0 0z" class="B"></path><path d="M492 116l1 1-1 1c1 1 1 0 2 1 1 0 1 1 1 2-1 1-2 2-4 2-1 0-2 0-4-1l-3-1c3-1 6 0 8-2-1-1-1-1-2-1 1-1 2-1 2-2z" class="G"></path><path d="M398 419c2 0 4 1 5 1v2h1v1c1 0 1 1 2 1h-2c-1 0-3 0-4-1l-1 1h-2l1-1c1 0 2 0 2-1h-3-1l-1 1h-1c1-2 2-3 4-4z" class="H"></path><path d="M398 419c2 0 4 1 5 1v2c-1-1-4-1-5-1s-1 0-1 1h0-1l-1 1h-1c1-2 2-3 4-4z" class="C"></path><path d="M130 123v1h1 0v1 3h1c-1 1-1 2-1 3l-2 2c0 2-1 4-2 6 0-1 0-3 1-4v-4c0-3 1-5 2-8z" class="l"></path><path d="M129 129l2 2-2 2v-4z" class="O"></path><path d="M129 129h0l2-5v1 3h1c-1 1-1 2-1 3l-2-2z" class="i"></path><path d="M419 585h0l3 7 2 4-2 1v1h-1v-1l-1 1-2-11c1-1 1-1 1-2z" class="L"></path><path d="M422 592l2 4-2 1v1h-1v-3c0-1 1-2 1-3z" class="K"></path><path d="M579 230h1c0 2-1 5-1 6h0v-1c1 0 1-1 1-1v-1-1-1c1 0 0 1 0 2 0 6-3 16-8 20 3-8 6-15 7-23z" class="e"></path><path d="M356 134c1-1 2-2 3-2h1c1 1 0 3 0 4v3c0 1 0 1-1 2l-1-2v1l-1 2c0-1-1-1-1-2s1-2 1-3 0-1-1-2v-1z" class="R"></path><path d="M356 135v-1h0c1 1 1 2 2 3l1 1c0-1 1-1 1-2v3c0 1 0 1-1 2l-1-2v1l-1 2c0-1-1-1-1-2s1-2 1-3 0-1-1-2z" class="G"></path><path d="M144 131l2-1 1 1h-1-1c-1 1-2 2-4 3l1 1h0c-2 2-4 3-5 5h0c-2 0-2 1-3 2h0v-1c-1 0-1 0-1 1l-1-1 1-2c0 1 0 0 1 1 3-4 7-7 10-9z" class="a"></path><path d="M295 103c2 1 4 0 7 0l-6 2-6 2v-1c-1-1-1-1-3-1 0 1-1 1-2 1h-1v-1-1c4 1 7-1 11-1z" class="I"></path><path d="M535 153h1c3 3 5 7 8 10 2 2 5 6 8 6h1v4c-4-2-6-5-8-8l-10-12z" class="D"></path><path d="M358 173c-1-1-1-1 0-2 0-1 1-1 2-2l1 1c1 4 2 7 4 10l-1 1c-1-1-2-3-3-4-1-2-2-3-3-4z" class="B"></path><path d="M358 173c-1-1-1-1 0-2 0-1 1-1 2-2l1 1h-1c-1 3 1 4 1 7-1-2-2-3-3-4z" class="R"></path><path d="M430 118h10 3l1 1h1l-2 1c-4 1-7 0-10 0l-1 1h-1l-2-2 1-1h0z" class="T"></path><path d="M430 118h0c2 1 4 1 7 1 2 1 4 1 6 1-4 1-7 0-10 0l-1 1h-1l-2-2 1-1z" class="Q"></path><path d="M512 125h2c1 1 2 3 4 2 2 2 4 5 6 7l-1 1c-1-2-2-2-3-2l-8-8z" class="c"></path><path d="M348 216v1l2-1h0 1l9 3v1c-2 0-7-1-9-2l-1-1h-1c1 1 4 2 5 3 0 1-1 1-1 1h-1c-1 0-3-1-4-2h-1c0 1 0 1-1 1v-1c1-1 1-2 2-3z" class="F"></path><path d="M532 264l3-3s0-1 1-1v-1l2 1v-1s0-1-1-1-1 0-2-1h0c1-1 2-1 3 0 1 0 2 1 2 2 0 2-1 3-2 5-1 0-1 1-2 1s-1 0-2-1h-2z" class="D"></path><path d="M344 532c2-7 5-13 8-20-1 7-3 14-7 20h0-1z" class="B"></path><path d="M375 478c1 0 2 0 3 1 2 1 3 1 5 1v-1h1c1 3 2 5 2 8h0l-1-1v1c-1 0-1 0-2 1l-1 1-1-1h1c0-1 0-2 1-3-1-2-1-2-2-3l-1-1c-1 0-4-3-5-3h0z" class="T"></path><path d="M380 481c1 0 2 1 3 2s2 2 2 3v1c-1 0-1 0-2 1l-1 1-1-1h1c0-1 0-2 1-3-1-2-1-2-2-3l-1-1z" class="d"></path><path d="M330 112l2-1h0 1v1h0c-1 0-1 0-2 1-4 4-4 9-10 9-3 1-6 0-8-2 0-1-1-1-1-2v-1l1 1c2 2 3 3 6 3h2 1c2-1 4-2 5-5 1-1 2-3 3-4z" class="N"></path><path d="M363 434c-1-2-3-4-4-6v-1c1 2 2 3 3 4 3 2 6 1 9 1 0 1 0 1 1 1l1 1h-1v1h0c2 0 2 1 3 2h-1c-1 0 0-1-1-1h-3l-1-1h-2 0c-1 0-2 0-3-1h0-1z" class="D"></path><path d="M355 505c1-1 2-5 3-5l1 1c0 1 0 1 1 2l1 1c-1 2-1 3-2 4h-1c0 1 0 2-1 2l-1 1 1-1-2-2v-3z" class="W"></path><path d="M355 505l1 1h1v4l-2-2v-3z" class="N"></path><path d="M358 508c0-2 0-4 1-5h1l1 1c-1 2-1 3-2 4h-1z" class="U"></path><path d="M355 505c1-1 2-5 3-5l1 1c-1 1-2 3-2 5h-1l-1-1z" class="H"></path><path d="M362 207c0 1 1 1 0 2 0 1 0 1 1 2h-2-5c-1 0-1 1-2 1-1 1-2 1-3 3-1 0-1 0-1 1l-2 1v-1l6-6c1-1 1-2 2-2 1 1 2 0 3 0h1v-1l2 1v-1z" class="V"></path><path d="M583 122h1v1c-2 2-4 4-5 6v3c1-1 2-1 3-1h0c-1 1-2 2-4 2 0 2-1 4-1 6l1 6-2-4c-1-4 1-6 2-10 0-2 1-4 2-5v-1l3-3z" class="b"></path><path d="M556 104l-2-2 1-1c1-1 2-3 3-4 1 0 2 1 2 1v5l-1 1h0-1v2c-1-1-1 0-1-1l-1 1v-2z" class="j"></path><path d="M558 102c-1-2 0-3 0-4h1l1 1c-1 1-1 1-1 2s-1 1-1 1z"></path><path d="M558 102s1 0 1-1c0 1 0 1-1 1v1l1 1h0-1v2c-1-1-1 0-1-1l-1 1v-2c0-1 1-2 2-2z" class="Y"></path><path d="M410 289l2 5c0 1 1 2 1 4-1 1-1 1-3 2 0 0-1 0-1-1-1-1 0-7 0-10l1 1v-1z" class="K"></path><path d="M398 520h1c1-1 2-3 4-4h0c-1 1-1 2-1 3v1c-2 2-5 4-8 6-2 2-3 4-6 5h-1c4-3 7-7 11-11z" class="N"></path><path d="M288 115l2-1h2c-2 2-4 4-6 5-1 1-2 3-3 3h0v-1-1c0 1-1 1-2 1h0l1-1v-1h0-1c1-1 1-1 2-1l-1-1 5-3 1 1z" class="j"></path><path d="M287 114l1 1-6 4h-1c1-1 1-1 2-1l-1-1 5-3zm53 69l3-2 1 2h0l2 1h-2c-3 1-6 3-8 6l-1 2h-1c0-1 0-1-1-2 1-2 2-3 4-4l3-3z" class="H"></path><path d="M340 183l3-2 1 2h0-4z" class="S"></path><path d="M294 102v-1h-3c-2-2-3-3-4-5 5 2 10 6 16 6v1 1l-1-1c-3 0-5 1-7 0h-1 0v-1h0z" class="R"></path><path d="M509 104c1 0 3 1 4 1l4 3c2 1 3 1 4 3-2 0-3-1-5-1h0c-4-2-7-4-11-5 0 0 1-1 2-1h0 2z" class="G"></path><path d="M516 110l1-1c-1 0-2-1-3-1-1-1-2-1-3-2l1-1h1l4 3c2 1 3 1 4 3-2 0-3-1-5-1z" class="T"></path><path d="M314 543h1c-1 7-2 15-6 21v-1-1l1-1v-1c-1 2-2 3-3 4l5-10c1-2 1-3 1-4 0-2 0-5 1-7z" class="B"></path><defs><linearGradient id="x" x1="433.566" y1="109.015" x2="432.515" y2="101.751" xlink:href="#B"><stop offset="0" stop-color="#565555"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#x)" d="M429 102c4 0 8 4 11 7h-1 0-1 0-2c-2-2-5-4-8-5-1 0-1-1-2-1s-1 0-1-1h4z"></path><path d="M143 113c0 1 0 2 1 2v1h1c-1 2-3 2-3 4l-3 2h-1l1-2c-1 0-1 0-2 1l-3 5-1 1c2-6 5-10 10-14z" class="M"></path><path d="M144 116h1c-1 2-3 2-3 4l-3 2h-1l1-2h0c1-2 3-3 5-4z" class="B"></path><path d="M346 220c1 0 1 0 1-1h1c1 1 3 2 4 2 0 1 1 1 2 1 1 1 2 2 4 3l-1 1h-1l1 2h0c-2 0-3-2-5-2l-3-3c0-1-1-2-2-2 0 0-1 0-2 1v-1l1-1z" class="P"></path><path d="M183 241h1c1 1 2 4 2 6 0 0 0 1-1 1-1 1-3 1-5 1h0l-2-2h-1c-1 0-1 0-2-1 3 0 5 1 7-1l1-1 1-1c0-1-1-1-1-2h0z" class="D"></path><path d="M557 135c2 6 3 12 5 18 1 4 3 7 4 11l-1-1c-1-1-1-1-1-2 0-2-2-6-3-8-1-3-2-5-3-8 0-3-1-6-2-8 1-1 1-2 1-2z" class="B"></path><path d="M124 161l6-19 1 1c-1 4-2 8-4 12-1 3-2 6-3 10 0 3-1 5-1 8h0-1-1l3-12z" class="Q"></path><path d="M325 149c0-2 0-5 1-7v14c1-1 1-2 1-3v-3h0c1 1 1 3 1 4-1 1-1 2-1 2-1 3-1 6-1 9h-1v-5-11h0z" class="B"></path><path d="M325 149c0-2 0-5 1-7v14 1-2c-1 1-1 2-1 2v-8z" class="R"></path><path d="M281 121h0c1 0 2 0 2-1v1 1h0c-2 2-4 5-7 7-1 1-2 2-4 2 0-1 0-2 1-2l-1-1c1-2 3-3 5-4 1-1 2-2 4-3z" class="T"></path><path d="M537 135h1 2c6 4 11 8 15 14 0 0-1 0-1 1-4-7-11-11-17-15z" class="L"></path><path d="M393 280c1-1 3-2 4-3h4c2 2 4 3 6 4l-1 2c-1-1-2-2-3-2-3-1-7-1-10 0v1l-1-1 1-1z" class="k"></path><path d="M190 106c1 3 2 8 0 11-1 2-4 4-7 5h0 0v-3s1 0 1-1c3 0 4-3 5-5h0 0c0-2 0-3 1-4v-3z" class="B"></path><path d="M351 490c-5-2-11-3-16-6l-1-1c-2-1-3-2-4-3-3-2-5-5-7-7v-1l4 4c2 2 4 2 5 4s4 3 6 4c1 0 1 1 2 1 2 0 4 1 6 2s4 2 6 2h0l-1 1z" class="O"></path><path d="M163 127c2-3 4-6 6-8 2-3 4-7 7-8 2-1 2 0 3 0 0 2-2 2-3 3-4 4-9 7-11 13h-2z" class="C"></path><path d="M366 112c0-2-1-4-1-6s1-3 2-4 3-2 5-1c1 0 2 0 2 1 2 1 2 4 2 6v1c0-2-1-4-2-6-1-1-2-1-3-1s-2 0-3 1-1 2-1 3c1 1 1 2 2 3h1c-1 0-2 0-2 1 1 0 1 0 2 1h0c-1 1-3 0-4 1z" class="N"></path><defs><linearGradient id="y" x1="371.483" y1="255.187" x2="378.214" y2="264.383" xlink:href="#B"><stop offset="0" stop-color="#929393"></stop><stop offset="1" stop-color="#b5b3b4"></stop></linearGradient></defs><path fill="url(#y)" d="M375 252h1v3h0v14l1 2c-1 0-1 0-1-1h-1v1h0v-3l-2-16h1 0 1z"></path><path d="M375 268v-1c1 0 1 1 1 2l1 2c-1 0-1 0-1-1h-1v1h0v-3z" class="P"></path><defs><linearGradient id="z" x1="443.292" y1="122.747" x2="443.712" y2="127.274" xlink:href="#B"><stop offset="0" stop-color="#555758"></stop><stop offset="1" stop-color="#6f6d6d"></stop></linearGradient></defs><path fill="url(#z)" d="M436 122c3 2 6 2 10 3 1 0 4 0 6 1 0 1-1 1-2 2l-1 1v-1c-1-1-4-1-5-1l-12-3v-1h0c1-1 2 0 3 0l1-1z"></path><defs><linearGradient id="AA" x1="358.147" y1="112.771" x2="355.204" y2="103.135" xlink:href="#B"><stop offset="0" stop-color="#acacad"></stop><stop offset="1" stop-color="#dcdcdb"></stop></linearGradient></defs><path fill="url(#AA)" d="M343 104l1-1h1c3 1 4 2 7 1 4 2 9 4 13 7l-3 2c-5-5-12-8-19-9z"></path><path d="M384 110c5-2 10-3 16-2h4l-1 1v1l-6-1c-1 0-3 0-5 1-1 0-2 0-4 1-3 0-5 0-8 2v-1l4-2z" class="D"></path><path d="M400 108h4l-1 1v1l-6-1c1 0 2 0 2-1h-2 3z" class="R"></path><path d="M407 507c1-1 1-1 2-1v2c-1 4-4 8-7 12v-1c0-1 0-2 1-3h0c-2 1-3 3-4 4h-1l9-13z" class="F"></path><path d="M514 123l-1-1 1-1c8 5 17 8 26 14h-2-1l-2-1-6-3-15-8zM387 231v2c1 2 3 3 3 5 1 1 1 3 0 4s-2 2-3 2c-1-1-2-1-3-3h1v-2h0 0 2l-1-1 1-7z" class="C"></path><path d="M385 241v-2h0 0 2v3h0c-1 0-2 0-2-1z" class="I"></path><path d="M354 212h6c1 0 1 0 1 1v2h-5c2 1 3 1 4 1l1 1c-1 0-1 1-2 1-2-1-4-1-6-2h-2-1 0c0-1 0-1 1-1 1-2 2-2 3-3zM203 625h13c4 0 8 1 12 2-4 0-8-1-12-1h-20-1-3v-1h1 0 10z" class="U"></path><path d="M193 625h6c-1 1-2 1-3 1h-1-3v-1h1z" class="R"></path><path d="M178 242c2 0 3-1 5-1h0c0 1 1 1 1 2l-1 1-1 1c-2 2-4 1-7 1h-1v-1c-1-1-2-1-3-1h-1v-1h3 5v-1z" class="H"></path><path d="M173 243h5 2 0l-1 1h-1c-2 1-3 1-4 1-1-1-2-1-3-1h-1v-1h3z" class="F"></path><path d="M173 243h5 2 0l-1 1h-1 0c-1-1-2-1-3-1-1 1-2 0-2 0z" class="D"></path><defs><linearGradient id="AB" x1="387.975" y1="134.88" x2="381.416" y2="141.815" xlink:href="#B"><stop offset="0" stop-color="#999a9c"></stop><stop offset="1" stop-color="#bbb9b6"></stop></linearGradient></defs><path fill="url(#AB)" d="M386 132c2 3 2 7 1 11-1 2-3 4-4 6h-2l1-1v-1-2-1c1 0 2-1 2-2 2-3 1-6 1-9l1-1z"></path><path d="M384 301v3h0 0c1 0 1 0 1-1l-1 32-1 2h0c-1-12-1-24 1-36z" class="c"></path><path d="M565 163l1 1 11 25-2 2c-1-6-3-11-5-17-2-3-4-7-6-10l1-1z" class="C"></path><path d="M560 174c1 2 3 5 4 7v1c0 1 0 2 1 3v4h0c0 1 0 1-1 2 0 0 0 1-1 1-1-1-1-2-1-3-1-2-1-4-1-6l-1-9z" class="Q"></path><path d="M562 180h1c0 2 0 3 1 4v5c-1 0-1-1-1-1v-2c0-1 0-1-1-2v-1-1-2z" class="I"></path><path d="M197 121l1 1c-1 1-2 3-3 5l-3 3c-1 2-7 11-8 12l-1-1h0l-1-1 2-2c1-1 3-3 5-4 1-3 3-5 5-8 1-2 2-4 3-5z" class="H"></path><path d="M236 622c5 2 9 2 13 2 4-1 9-1 13-2 1 0 1 0 2 1-5 1-11 2-17 3-2 0-4 0-6-1l-6-2 1-1z" class="M"></path><path d="M85 558h1l1 1 1 1h0 1c2 2 3 4 5 6l5 8h0-1l-4-6c0-1-1-1-1-1-1-1-1-1-1-2l-2-3h0l-1 1-1 1v-1l-1-1h0l-1-1h0l1 2c1 2 3 4 4 6l5 5 1 1-1 1c-1-1-2-2-2-3-4-4-7-9-10-13l1-1v-1z" class="d"></path><path d="M340 402l1-2c1 2 0 3 1 5v3h0v1l3 8h0c-4 0-4-4-6-7v1c-1-1-1-5 0-6l-1-1h1 1v-2h0z" class="O"></path><path d="M339 405l-1-1h1 1v-2h0c0 1 0 2 1 4v3c-1-1-2-3-2-4z" class="V"></path><path d="M351 206c2-3 5-5 8-7 1-2 3-4 5-4h1c0 1 0 1-1 2l-5 4-6 7-1-1-1-1z" class="b"></path><path d="M352 207l1-2c3-1 3-3 6-4l-6 7-1-1z" class="k"></path><path d="M294 434c0 3 2 5 3 7s2 3 3 5c1 3 4 6 6 9s5 5 7 8l7 8c-3-2-4-4-7-6-2-3-4-5-6-7-4-4-7-10-10-15l-4-8 1-1z" class="Y"></path><path d="M345 546c1 0 1 0 2 1-6 8-12 15-18 22-2 3-3 5-5 7 2-5 5-9 8-13l13-17zm227-392l2-1c1 2 2 4 3 5h1l1 2c1 0 0 0 1-1 3 6 4 13 4 19h0c-2-8-6-17-12-24z" class="J"></path><path d="M162 242c2-1 4-2 5-3 0-1 1-2 2-2h1c1 2 2 2 4 3 1 0 2 0 3 1-2 0-3 0-4 1 2 1 4 0 5 0v1h-5-3v-1l-3 1h-2c-1 0-2 0-3-1z" class="T"></path><path d="M167 243l-1-1c0-1 1-1 2-2 1 0 2 1 4 1l-2 1-3 1z" class="M"></path><path d="M418 115c2 0 3 0 4 1 3 1 6 1 8 2l-1 1 2 2h1l4 1-1 1c-1 0-2-1-3 0h0v1l-2-1c-1 0-2-1-4-2h1l-5-3c-1-1-2-2-4-3z" class="W"></path><path d="M422 116c3 1 6 1 8 2l-1 1 2 2h0c-3 0-7-3-9-5h0z" class="O"></path><path d="M516 106h0c0-2-1-2-2-3v-5s1 0 2-1v1c1 2 1 5 2 7 2 4 6 7 10 11l4 5c-1 0-3-2-3-3l-7-6h0l-1-1c-1-2-2-2-4-3l-1-2z" class="C"></path><path d="M395 275c1 0 3 0 4-1v-1c-2 1-3 1-5 1l4-4c2-1 4-1 6 0s4 2 5 4v1c-2-2-4-3-6-4h-1c-1 1-2 1-2 2l-1 1h1 2c2 1 4 3 5 5-3-2-6-4-10-3h-2v-1z" class="D"></path><path d="M144 115c1-1 2-2 4-3h0l1 1c1 1 3 2 3 4-2 0-3 0-5 1-3 1-6 5-8 8l-1-1c1-1 3-3 4-5 0-2 2-2 3-4h-1v-1z" class="R"></path><path d="M430 123l2 1 12 3c1 0 4 0 5 1v1h0c-5 1-12 0-16-2v-1l-3-3zM319 592v1c1 0 2 0 3-1v1c0 1-1 1-1 2l-7 3-9 6v-1l6-6 8-5z" class="H"></path><path d="M252 105l1 1c-3 1-7 2-9 5-1 1-1 2-1 3s1 3 2 3c1 1 2 1 3 1l3-1h0c-1 2-2 2-5 2-1 0-4-1-5-3h0-1c0-1 0-1-1-2v-1c2-4 9-7 13-8z" class="N"></path><path d="M404 364h5c1 1 2 3 3 4l-2-1h-2c-1 0-2 1-2 2h-1c0 1 0 3 1 4h-1c-1 0-2-1-3-2s-1-2-1-3c0-2 2-3 3-4z" class="J"></path><path d="M351 168c2 2 4 5 7 5h0c1 1 2 2 3 4 1 1 2 3 3 4l1-1c3 3 5 5 9 7v1h-2c-3-2-6-5-8-6-2 0-4-2-5-2 0-1 0-1-1-1l1-1h0c-2-4-6-6-9-9l1-1z" class="N"></path><path d="M336 510c1-3 2-6 2-10l3 3 1 3s0 1 1 1c-1 1-1 1-1 3l-1 1h0-1v-1h-1l-1 3h0c-1-1-2-2-2-3z" class="E"></path><path d="M341 503l1 3s0 1 1 1c-1 1-1 1-1 3l-1 1h0-1v-1h-1c0-2 1-3 2-4v-3z" class="T"></path><path d="M369 156h1l1 1v4 13c0 2 0 5 1 8v2c-1-1-2-4-2-5-1-4-2-20-1-23z" class="N"></path><path d="M382 145v2 1l-1 1h2c-3 3-6 6-10 6h-1c-1 0-1-1-1-1-1 0-2 0-3-1h-1l1-1c1 1 1 1 2 1s1 0 1-1l1 1c1 0 2 0 3-1l1-1 1-2c1-1 2-1 2-2l3-2z" class="T"></path><path d="M382 145v2 1l-1 1c-1 1-3 2-5 2h0l1-2c1-1 2-1 2-2l3-2z" class="C"></path><path d="M307 598l-1 2c1 0 2-2 4-3h0 1l-6 6v1c-1 1-4 3-5 4h-2-1v1c-1 0-1 0-2 1 1-1 2-1 3-3-1 0-1 1-2 1h0-1l12-10z" class="a"></path><path d="M384 222h1c1 0 1-1 1-2 1 1 1 0 1 1 0 3-1 7 0 10l-1 7c-1-2-1-4-3-6-1-4-1-6 1-10z" class="c"></path><path d="M357 552l1 1-10 8h1c1-1 2-2 3-2l2-2 2-2c1 0 1-1 2-1h1 0l-22 19c0-1-1-1-1-2l21-19z" class="P"></path><path d="M345 271c1 1 0 2 2 2v1h2v1c-1 1-1 2-1 3v1h2c2 1 5 1 7 2l-1 1c-2-1-6-1-8-1h-5v-1-1c0-1 1-2 1-3s1-1 1-2c-1 0-1 0-2-1h0l2-2z" class="C"></path><path d="M406 110l2 1 5 2c2 1 3 1 5 2s3 2 4 3l5 3h-1c-3 0-5-3-7-4h-1l-1 1-3-2-2 1-3-3-7-2 1-1h3v-1z" class="N"></path><path d="M409 114c2 0 3 1 5 2l-2 1-3-3z" class="T"></path><path d="M406 110l2 1 5 2c2 1 3 1 5 2s3 2 4 3l5 3h-1c-3 0-5-3-7-4h-1c-4-3-8-5-12-6v-1z" class="Q"></path><path d="M407 236c0-3 0-7-1-10-2-2-4-5-6-5-2-1-5 0-6 1 0 1 1 2-1 3v-1c-1-1-1-2 0-3 1-2 3-3 5-3s5 2 7 3c3 4 3 9 3 13 0 1 0 2-1 2z" class="X"></path><path d="M359 613c3-6 1-8-1-13 2 2 5 6 5 10 0 3-3 9-5 11h-3v-1h-3l1-2c2 0 3-1 5-2 1 0 2-1 2-3h-1z" class="c"></path><path d="M374 320v-10c0-5 0-10 1-14v7c1 0 0 0 1-1v33 8 3c-1 1-1 3-1 4h0v-4 3-18c0-4 0-8-1-11z" class="B"></path><path d="M362 503l2-10 4 3c0 5-2 9-5 13v-1-1c-1-1-1-2-1-4z" class="F"></path><path d="M494 101c2-2 6-1 8-1s3 1 5 1l-1 1v1h1 2v1h-2 0c-1 0-2 1-2 1l-6-1h-2c-2 0-3-1-4-1v-1l1-1z" class="J"></path><path d="M494 101v1h1c2 0 5-1 7 0-1 0-3 0-5 1v1c-2 0-3-1-4-1v-1l1-1z" class="F"></path><path d="M502 102l4 1h1-3 0c-1 1-3 1-5 1h-2v-1c2-1 4-1 5-1z" class="M"></path><path d="M499 104c2 0 4 0 5-1h0 3 2v1h-2 0c-1 0-2 1-2 1l-6-1z" class="U"></path><path d="M133 139c4-7 9-13 16-15h3c-2 2-4 1-5 2s-3 3-3 5c-3 2-7 5-10 9-1-1-1 0-1-1z" class="J"></path><path d="M394 423h1l-1 2c0 1 0 1-1 1l-2 2 1 1v-1h2c1-1 1-2 2-2h1c1 0 2 1 4 2h0-1l-3-1h-2c1 1 2 1 2 2h0c-4 1-7 3-10 3h-2c1-1 2-1 3-2 1 0 1 0 2-1h0-1l-1 1h-1-2-1c2-2 4-3 7-6 1 0 2 0 3-1z" class="B"></path><path d="M286 421c1 2 1 3 2 4v2l1-1v-1 1l1-1c1 1 1 3 1 4 1 2 2 4 3 5l-1 1 4 8c-2-1-4-4-5-7h0v1c0 1 1 2 1 2v2c0-1-1-3-2-4l-1-1v-1c-1-1-1-2-1-2-1-2-2-3-2-4 0-3-1-5-2-8h1z" class="h"></path><path d="M290 425c1 1 1 3 1 4 1 2 2 4 3 5l-1 1c-2-3-3-6-4-9l1-1z" class="S"></path><path d="M365 123l3 1 1 1v1h-1-5c-7 1-15 5-19 11l-3 5h-1v-1c6-11 14-14 25-18h0z" class="M"></path><path d="M142 109l2-2c5-6 9-9 16-10 2 0 6 0 8 2h0-10l-1 1c-2 1-3 2-5 3s-3 2-5 3l-4 3h-1z" class="l"></path><path d="M153 100c2-1 3-1 5-1l-1 1h-4z" class="O"></path><path d="M153 100h4c-2 1-3 2-5 3s-3 2-5 3c0-2 1-3 2-3 2-2 3-2 4-3z" class="Q"></path><path d="M280 103c1 0 2 1 4 1v1 1c-2 0-4 1-6 1-4 1-9 0-13-1h1 0v-1c0-1 1-1 1-2h2c1 1 2 2 4 2l2-2 3 1h1v-1h1z" class="S"></path><path d="M280 103c1 0 2 1 4 1v1c-2 0-4-1-6-1h1v-1h1z" class="V"></path><path d="M406 553c1 4 3 8 5 11 3 3 6 5 9 8-3-1-7-2-10-5l-3-2c-1-1-1-3-2-4h2c-1-2-1-4-2-6 1-1 1-1 1-2z" class="F"></path><path d="M408 563c1 1 2 1 2 2v1 1l-3-2 1-2z" class="D"></path><path d="M407 561l1 2-1 2c-1-1-1-3-2-4h2z" class="N"></path><path d="M175 117c2-1 5-2 7-3 3 0 4-1 5-3 0-1 2-3 2-4-1 0-2-2-2-2-1-1-2-3-2-4 2 1 4 3 5 5v3c-1 1-1 2-1 4-2 2-4 5-8 5h0-2-1c-2 0-3 0-5-1h2z" class="b"></path><path d="M175 117h2 0c2-1 2-1 3 0l1 1h-2-1c-2 0-3 0-5-1h2z" class="X"></path><path d="M161 138l4-8c1-2 1-4 2-6 2-3 4-5 6-7 2 1 3 1 5 1h1c-6 2-8 4-11 9l-11 20c1-2 1-4 2-5s1-1 1-2c1-1 1-1 1-2z" class="K"></path><path d="M307 598l29-27c0 1 1 1 1 2l-27 24h0c-2 1-3 3-4 3l1-2zm43-332l2 1 2 1v1h-2c-1 2-1 3-1 4h6 1v1c-1 0-3 0-4 1 0 1 0 1-1 1h-1-1l-1 1h2 0l-2 2h0-2v-1c0-1 0-2 1-3v-1h-2v-1-5l1 1h1v-1-2h1z" class="E"></path><path d="M350 266l2 1 2 1v1h-2c-1 2-1 3-1 4-1 0-1 1-2 1v-5-1-2h1z" class="B"></path><path d="M350 266l2 1c0 1 0 1-1 2l-2-1v-2h1z" class="C"></path><path d="M396 406c1-4 3-8 4-12v-7c1 0 2 1 2 2 2 3 2 5 1 8l-1 1v3l-1-1-1 1h0v2c-1 1-1 1-1 2h-1v1h-2z" class="B"></path><path d="M412 117l2-1 3 2 1-1h1c0 2 4 3 4 4v1c2 1 3 2 4 3 1 0 2 1 2 2s0 1-1 1c-2 0-4-1-5-2h-1c0-2-1-2-2-3l-4-3c-1-1-3-2-4-3z" class="W"></path><path d="M416 120l2-1c0 1 1 2 2 2v2l-4-3z" class="O"></path><path d="M420 121c2 2 6 4 7 6h-1c0-1-1-2-2-1h-1-1c0-2-1-2-2-3v-2z" class="I"></path><path d="M333 100c7 1 13 2 19 4-3 1-4 0-7-1h-1l-1 1c-8-1-15 0-23 2l-1-1v-1l3-1c1 1 4 0 5-1 2 0 3 0 5-1h-1c1 0 2-1 2-1z" class="L"></path><path d="M223 127c2-2 3-4 5-6-2 6-4 12-10 15-2 1-4 2-6 1-2 0-3-1-4-2l2-1h2s1-1 2-1h2 0c1-1 1-1 2-1h0c2-1 3-2 4-3 1 0 1-1 1-2z" class="M"></path><path d="M208 135l2-1h2c1 1 2 0 3 1-1 1-2 1-3 2-2 0-3-1-4-2z" class="D"></path><path d="M524 621c2 1 5 1 7 2s3 1 4 3c0 1-1 2-2 2-2 1-3 2-5 2-1 0-1-1-1-1v-1l2-2-2-1c-1 0-4-2-5-2-2 0-6 2-8 3 3-2 6-4 10-5z" class="J"></path><path d="M319 592l8-5c9-6 18-12 27-15v1h1c-6 3-12 6-17 9-6 3-11 7-16 10-1 1-2 1-3 1v-1z" class="P"></path><path d="M333 533c2-1 3-7 4-9h1c-2 7-5 13-8 19-2 6-4 10-7 15-1 1-2 2-2 3l-1-1c2-3 5-7 6-10 2-5 2-11 4-15l1-1v1l2-2z" class="T"></path><defs><linearGradient id="AC" x1="371.818" y1="243.011" x2="380.083" y2="236.169" xlink:href="#B"><stop offset="0" stop-color="#999899"></stop><stop offset="1" stop-color="#b7b6b8"></stop></linearGradient></defs><path fill="url(#AC)" d="M378 227h0l1 1c-2 9-3 18-3 27h0v-3h-1-1 0-1c0-4 0-9 1-12 0-2 1-5 1-6l3-7z"></path><path d="M374 240v6c0 1 1 1 1 2v4h-1 0-1c0-4 0-9 1-12z" class="H"></path><path d="M373 482c-1 0-1-1-2-1-2-1-5-2-8-4-2-1-5-1-7-3h1c3 1 5 3 8 4l2-2c2 2 5 2 7 3 1 0 1 0 1-1 1 0 4 3 5 3l1 1c1 1 1 1 2 3-1 1-1 2-1 3h-1c0-1 0-2-1-2v2h-1l-1-3v-2c-2-2-2-1-4-2l-1 1h0z" class="S"></path><path d="M365 478l2-2c2 2 5 2 7 3 1 0 1 0 1-1 1 0 4 3 5 3l1 1h-1s-1-1-2-1h-2c-4 0-7-2-11-3z" class="C"></path><path d="M392 183l1 1c-2 4-4 5-8 7h0c-2 1-6 1-8 0-1 0-3-1-4-1l-1-1v-1h2v-1l5 1h2c5 0 8-2 11-5z" class="S"></path><path d="M374 187l5 1-1 1h-2c1 1 1 1 2 1h-2 1c0 1 0 1 1 1h-1c-1 0-3-1-4-1l-1-1v-1h2v-1z" class="G"></path><path d="M372 188h2 0c1 0 1 1 2 1h-2l-1 1-1-1v-1z" class="S"></path><path d="M195 626l-2 1h18c5 0 10 1 15 1-4 1-7 0-11 0-10-1-20-1-30 2 0-3 0-6 1-9l2 1h0-1l-1 1c2 1 6 1 7 2h0-1v1h3z" class="D"></path><path d="M344 183c2-1 4-2 6-2s6 2 8 1c5 1 9 6 14 7l1 1c1 0 3 1 4 1 2 1 6 1 8 0 0 1-1 1-2 2-4 1-8-1-11-3l-1 1c-4-2-8-5-12-6-4-2-9-2-13-1l-2-1h0z" class="G"></path><path d="M358 182c5 1 9 6 14 7l1 1c1 0 3 1 4 1 2 1 6 1 8 0 0 1-1 1-2 2-4 1-8-1-11-3l-14-8z"></path><path d="M262 622c12-2 23-7 33-14h1 0c1 0 1-1 2-1-1 2-2 2-3 3 1-1 1-1 2-1v-1h1 2c-7 5-14 8-22 11-4 1-9 4-14 4h0c-1-1-1-1-2-1z" class="T"></path><path d="M197 121l3-6c3-3 7-6 9-9 1-2 2-6 2-8v-1c1 0 1 0 1 1 1 3-1 6-2 8 4-2 9-4 14-6h0l-1 1c-2 1-4 2-5 3-7 3-16 11-20 18l-1-1z" class="J"></path><path d="M392 530c3-3 6-5 9-8 4-4 7-10 10-15-1 2-1 5-1 7-2 5-6 9-10 12-2 2-4 4-7 6l-1-2z" class="E"></path><defs><linearGradient id="AD" x1="386.84" y1="222.077" x2="382.274" y2="213.217" xlink:href="#B"><stop offset="0" stop-color="#bfbfc2"></stop><stop offset="1" stop-color="#e8e6e6"></stop></linearGradient></defs><path fill="url(#AD)" d="M387 211c1-1 1-1 2-1 1-1 2-1 4-2l-1 1v1h0c-1 1-3 2-4 3-4 2-7 6-8 11l-1 4-1-1h0c-2-1-3-2-5-3 1-1 3-2 4-3 3-3 6-8 10-10z"></path><path d="M340 116c5-2 13-4 18-2 4 1 8 6 10 10-2-1-4-4-6-6h0c-1-1-5-2-7-2l-1 1h0c-3-1-7-1-10 0h0-1l-3 1h-2l2-2z" class="T"></path><path d="M338 118l2-2 1 1h0 2l-3 1h-2z" class="G"></path><path d="M535 127c-6-2-10-7-16-9-3-2-6-4-10-5-2-1-4-1-5-3 0-1 0-2 1-3h0c2 2 3 2 5 3 9 4 16 11 24 16 0 1 1 1 1 1z" class="P"></path><path d="M323 609c1-1 1-1 2-1h1c0 2-1 3-2 5-1 1-4 1-6 1 0 1 0 2 1 2 0 2 2 3 3 4h-5l-1-1v-1c-1-1-3-3-5-3l1-1c-1 0 0 0-1-1l-3-1v-1h2 3c2 0 3 1 4 1 3 0 4-1 6-3z" class="F"></path><path d="M308 611h2l6 4c-2 0-3 0-4-1-1 0 0 0-1-1l-3-1v-1z" class="h"></path><path d="M359 178h0l-1 1c1 0 1 0 1 1 1 0 3 2 5 2 2 1 5 4 8 6v1c-5-1-9-6-14-7-2 1-6-1-8-1s-4 1-6 2l-1-2c2-1 5-2 8-2 2-1 6 0 8-1z" class="j"></path><path d="M214 129c-1 1-1 0 0 1 3 0 6-1 9-3 0 1 0 2-1 2-1 1-2 2-4 3h0c-1 0-1 0-2 1h0-2c-1 0-2 1-2 1h-2l-2 1-4 1c-2 0-4-1-7-1-1-1 0-1-1 0 0-2-2-4-1-6h1v2c1 1 3 2 4 3 4 1 8-2 12-4 0-1 0-2 1-2h1v1zm130 153c4 0 11 4 14 6s8 7 10 10c-7-4-11-10-18-13-1 0-2-1-3-1s-2 1-2 1h-1v1c7 7 17 14 21 25-3-3-5-6-7-9s-4-5-6-7l-3-3-5-4c-1-1-2-2-3-2v2c0 1 0 0-1 1v-1l-1-1h1c1-1 1-2 1-2v-1l2 1c0-1-1-1-1-2h2v-1z" class="E"></path><path d="M362 503c0 2 0 3 1 4v1 1l-2 4h0l-3 6c-1 1-2 2-2 3-1 2-2 3-3 5h0v-3-1c0-1 1-2 1-3l1-4 1-5 1-1c1 0 1-1 1-2h1c1-1 1-2 2-4h1v-1z" class="O"></path><path d="M358 508h1v3c0 1 0 1-1 2-1 2-1 4-2 6 0-3 1-6 1-9 1 0 1-1 1-2z" class="S"></path><path d="M362 503c0 2 0 3 1 4v1 1l-2 4c0-2-1-3-1-4l2-5v-1z" class="U"></path><path d="M325 149c0-6-1-13 1-18 1-6 7-10 12-13h2l-1 1h1l-1 1h1c-2 1-4 2-5 4-4 3-9 9-9 14v4c-1 2-1 5-1 7h0z" class="H"></path><defs><linearGradient id="AE" x1="353.809" y1="145.241" x2="370.614" y2="145.573" xlink:href="#B"><stop offset="0" stop-color="#626262"></stop><stop offset="1" stop-color="#8c8b8c"></stop></linearGradient></defs><path fill="url(#AE)" d="M356 135c1 1 1 1 1 2s-1 2-1 3 1 1 1 2c1 3 2 6 5 8 1 1 3 2 5 3h1c1 1 2 1 3 1 0 0 0 1 1 1-5 1-9-1-13-3-3-2-5-5-5-9-1-3 0-6 2-8z"></path><defs><linearGradient id="AF" x1="199.795" y1="622.812" x2="199.984" y2="621.162" xlink:href="#B"><stop offset="0" stop-color="#6a6b6b"></stop><stop offset="1" stop-color="#838084"></stop></linearGradient></defs><path fill="url(#AF)" d="M186 621c2-1 6 0 8 0l19 1h0-4-1v1c2 0 5-1 7 0h-1-3v1c1 0 3 0 5 1h-13-10c-1-1-5-1-7-2l1-1h1 0l-2-1z"></path><path d="M198 624h0c5-1 10-1 16-1h-3v1c1 0 3 0 5 1h-13c-1-1-3-1-5-1z" class="d"></path><path d="M193 625c-1-1-5-1-7-2l1-1h1 3 0c1 1 3 0 4 1h2l-1 1h2c2 0 4 0 5 1h-10z" class="T"></path><path d="M375 271h0v-1h1c0 1 0 1 1 1 3 1 7 1 9 2-3 0-6 0-10-1 0 6 0 12 1 17l3 2v1l-4-2v1 11c-1 1 0 1-1 1v-7-7c-6-3-13-5-19-7l1-1 18 7v-16h-1l1-1z" class="K"></path><path d="M407 141h1c1 7 1 15 1 22 0 9 0 17-2 26h-1c-1 1-1 1-1 2v-3c1-9 2-18 2-27 1 1 1 2 1 3h0c0-3 1-7 0-11h0c-1-1-1-1-1-2 1-2 0-7 0-10z" class="T"></path><defs><linearGradient id="AG" x1="234.621" y1="622.415" x2="234.321" y2="618.021" xlink:href="#B"><stop offset="0" stop-color="#7d7b7e"></stop><stop offset="1" stop-color="#a1a2a3"></stop></linearGradient></defs><path fill="url(#AG)" d="M213 622c6-1 12-1 18-2l21-4 1 1c-1 1-3 1-4 1h-1l-1 1h1l-12 3-1 1c-2-2-5 0-6 0h-1-4 0c-1 0-2 1-2 1l-7-1c-2-1-5 0-7 0v-1h1 4 0z"></path><path d="M125 591c3 2 7 4 10 6 13 7 27 13 42 16l1 1c-1 0-1 0-2 1l-17-6c-8-2-15-6-22-9-5-3-9-5-14-8 1 0 1 0 2-1z" class="M"></path><path d="M577 194v-1l1-1c1 5 3 9 3 14 1 6 1 12 1 18 0 3-1 7-2 9 0-1 1-2 0-2v1 1 1s0 1-1 1v1h0c0-1 1-4 1-6h-1c0-3 1-6 1-9 0-9-1-18-3-27z" class="g"></path><path d="M298 435l2 2 7 13c3 5 6 8 10 12 8 11 19 18 32 24v1c-4-2-6-3-9-5-3-1-6-2-9-4-6-5-12-11-17-17-6-8-12-16-16-24v-1-1z" class="G"></path><defs><linearGradient id="AH" x1="428.969" y1="118.171" x2="428.282" y2="110.004" xlink:href="#B"><stop offset="0" stop-color="#7c7a7a"></stop><stop offset="1" stop-color="#969798"></stop></linearGradient></defs><path fill="url(#AH)" d="M413 110c9 2 17 5 26 6h0 8c-3 1-4 2-7 2h-10 0c-2-1-5-1-8-2-1-1-2-1-4-1-2-1-3-1-5-2v-3z"></path><path d="M361 241l1 2c2 5 2 9 1 14l-1 5-1 2c-1 1-2 4-4 4v-1c1-1 1-2 2-3-1-1-1-2-1-3h0-1-1v-1c1-1 1-2 1-4h0c1-1 1-1 2-1h0c1-2 1-2 0-3 1-1 1-1 1-2h-1 0c0-1 1-1 2-1 1-2-1-4 0-5v-3z" class="E"></path><path d="M362 252l-1 2v1c0-1 0-1-1-2v-1h2z" class="F"></path><path d="M358 258v-1c1-1 1-2 3-2l-1 4-1-2-1 1z" class="B"></path><path d="M358 258l1-1 1 2h0c0 2-1 3-1 5-1-1-1-2-1-3h0 0v-3z" class="D"></path><path d="M361 255v-1c2 1 2 1 2 3l-1 5-2-3h0l1-4z" class="S"></path><path d="M360 259l2 3-1 2c-1 1-2 4-4 4v-1c1-1 1-2 2-3 0-2 1-3 1-5z" class="W"></path><path d="M362 243c2 5 2 9 1 14 0-2 0-2-2-3l1-2v-9z" class="c"></path><path d="M344 532h1c-7 18-19 34-31 49l-1-2 12-14c5-7 9-15 13-23l6-10z" class="E"></path><path d="M481 102c3 1 5 2 7 3 9 4 17 11 26 16h0l-1 1 1 1h-1-1c-1-1-2-1-2-1v1l-1 1c0-1-1-2-2-2v5c-1-1-1-4-2-6 0-1-1-3-2-4-7-7-14-10-22-14v-1z" class="a"></path><path d="M507 122l1-1c1 0 1 0 2 1v1l-1 1c0-1-1-2-2-2z" class="H"></path><path d="M471 113h3l7 2c3 0 9-1 11 1h0c0 1-1 1-2 2 1 0 1 0 2 1-2 2-5 1-8 2l-2-1c-1-1-2-1-3-2l-5-3h-2c-1 0-1-1-1-2z" class="F"></path><path d="M471 113h3l7 2h-3c-1 1-3 0-4 0h0-2c-1 0-1-1-1-2z" class="D"></path><path d="M479 118c2-1 3 0 5 0s4-1 6 0c1 0 1 0 2 1-2 2-5 1-8 2l-2-1c-1-1-2-1-3-2z" class="N"></path><path d="M123 228h0l1 3v-1-1-1l1 3h0c0 1 1 2 1 2 0 2 1 4 2 5 3 8 8 14 13 21l-6-3c-8-8-11-17-12-28z" class="X"></path><path d="M151 156c7-7 15-13 22-19l14-11 7-6c2-1 4-4 6-5-1 2-2 4-4 5-3 4-8 7-12 10-11 8-22 17-32 27l-1-1z" class="L"></path><path d="M344 616l-2-1h-5 0v-1c1-3 6-2 8-3 2 0 3-1 4-1 1 1 1 3 2 4 2 1 3 1 4 1 2 0 3-1 4-2h1c0 2-1 3-2 3-2 1-3 2-5 2l-1 2-4-1-3-3h-1 0z" class="F"></path><path d="M345 616h1 0c2 1 4 2 7 2l-1 2-4-1-3-3z" class="W"></path><path d="M520 133c1 0 2 0 3 2 2 1 4 3 5 5 0 1 1 1 2 2h0c1 2 1 3 2 4h0c3 0 4 3 7 4 0 1 0 1 1 2v1 1s1 1 1 2l3 3v1c0 1 1 2 0 3-3-3-5-7-8-10h-1l-1-1c-2-2-3-5-5-7-3-4-7-8-9-12z" class="Y"></path><path d="M532 146c3 0 4 3 7 4 0 1 0 1 1 2v1 1s1 1 1 2l3 3v1l-12-14z" class="G"></path><path d="M530 142h0 2c3 3 6 5 10 6h0c3 2 7 3 8 7 1 1 1 3 1 4-1 1-1 1-2 1-3-1-7-5-9-7v-1c-1-1-1-1-1-2-3-1-4-4-7-4h0c-1-1-1-2-2-4z" class="U"></path><path d="M541 151c1 0 2 0 2 1h1l1 1h2 0c1 1 1 2 2 3 0 0 0 1-1 2 0-1-1-2-2-2-2-1-4-3-5-5z" class="H"></path><path d="M530 142h0 2c3 3 6 5 10 6h0l-1 1c1 1 1 1 2 1l1 1c1 1 2 1 3 2h-2l-1-1h-1c0-1-1-1-2-1l-2-1c-3-1-4-4-7-4h0c-1-1-1-2-2-4z" class="c"></path><defs><linearGradient id="AI" x1="304.849" y1="443.565" x2="299.496" y2="446.094" xlink:href="#B"><stop offset="0" stop-color="#030201"></stop><stop offset="1" stop-color="#27282a"></stop></linearGradient></defs><path fill="url(#AI)" d="M293 427c1 3 2 7 5 10 4 8 10 16 16 24l-1 2c-2-3-5-5-7-8s-5-6-6-9c-1-2-2-3-3-5s-3-4-3-7c-1-1-2-3-3-5l2-2z"></path><path d="M393 309c0-1 1-1 1-2h1l-1 1h1c1 1 0 2 1 3-1 2-1 5-3 7 0 3 0 6-1 8-1 0-1-1-2-2 0 4-1 7-1 11-1 1-1 3-1 5v-5c-1-2 0-4 0-6 0-5 0-9 1-13 1-3 2-5 2-8l1 2 1-1z" class="S"></path><path d="M391 308l1 2 1-1c-1 3-2 5-3 7h-1c1-3 2-5 2-8z" class="G"></path><path d="M392 317h1s-1 1 0 2v-1h0 0c0 3 0 6-1 8-1 0-1-1-2-2l2-7z" class="g"></path><path d="M394 308h1c1 1 0 2 1 3-1 2-1 5-3 7h0 0v1c-1-1 0-2 0-2h-1c0-3 1-6 2-9z" class="e"></path><path d="M523 248h1c2-2 6-6 10-6v1l1 1v1c1 0 4-1 6 0 1 0 3 1 4 1l-1 1c-1 0-2-1-4-1v1c1 1 2 1 3 2h0c-3 0-9-2-11 0-2 1-3 2-4 3s-2 4-3 5h-2c2-5 6-8 11-12h-1c-4 3-7 6-10 10v-1c0-1 1-2 1-3l1-1-2-2z" class="U"></path><defs><linearGradient id="AJ" x1="302.909" y1="115.816" x2="315.71" y2="108.866" xlink:href="#B"><stop offset="0" stop-color="#888788"></stop><stop offset="1" stop-color="#acabac"></stop></linearGradient></defs><path fill="url(#AJ)" d="M301 113c2-2 6-3 8-5l6-3 2 2c-1 1-2 1-2 2 1 2 3 2 5 3l-5 2-1 1c-1-1-1-1-2-1v-1-1l-1 1c-2 2-4 4-7 6 0-1-1-1-1-1-1-1-1-2-2-2v-3z"></path><path d="M301 113c1 1 2 1 2 2v3c-1-1-1-2-2-2v-3z" class="T"></path><path d="M409 503c1-3 3-8 1-11-1-2-3-3-4-5-1 0-2-1-3-2-1-2 0-7 1-8v-1h2c1 1 2 2 2 4v2h0 0c2-1 2-2 4-2l1 1c1 2 1 3 0 4v1c-1 0-2-1-2 0 1 6 1 11-1 18l-1-1z" class="L"></path><path d="M363 142l3-4h0v2c0 1 0 2 1 4v1h0c1 0 2 0 3 1l-1 2 1 2 1 1v1h0c0 1 0 1-1 1s-1 0-2-1l-1 1c-2-1-4-2-5-3-3-2-4-5-5-8l1-2v-1l1 2h0l4 1z"></path><path d="M359 141l4 1c-2 2-1 4-1 6-1-2-2-4-3-7zm9 11h-1c-1-4-2-9-1-12 0 1 0 2 1 4v1h0c1 0 2 0 3 1l-1 2 1 2 1 1v1h0c0 1 0 1-1 1s-1 0-2-1z" class="F"></path><path d="M367 145c1 0 2 0 3 1l-1 2h-1c-1-1-1-1-1-3z" class="C"></path><path d="M510 109c-1 0-2 0-2-1v-1c2 1 6 3 7 3h1 0c2 0 3 1 5 1l1 1h0l7 6c0 1 2 3 3 3s2 2 4 2c0 1 1 1 1 2 1 0 1 1 2 1l1 1v1l-2-1c0-1-1-1-1-1h-1v1 1l-1-1s-1 0-1-1c-8-5-15-12-24-16v-1z" class="h"></path><path d="M522 114a30.44 30.44 0 0 1 8 8l-4-4c-2-1-4-2-6-4h2z" class="c"></path><path d="M510 109c-1 0-2 0-2-1v-1c2 1 6 3 7 3h1 0c2 0 3 1 5 1l1 1h0v2h-2c-1 0-2-1-3-2-2-1-5-2-7-3z" class="W"></path><path d="M522 112l7 6c0 1 2 3 3 3s2 2 4 2c0 1 1 1 1 2 1 0 1 1 2 1l1 1v1l-2-1c0-1-1-1-1-1h-1v1 1l-1-1s-1 0-1-1 0-2-1-2c0-1-2-2-3-2a30.44 30.44 0 0 0-8-8v-2z" class="d"></path><path d="M343 463s1 0 1 1l4 4c1 0 1 1 1 1l1 1h1l1 1 1 1c1 0 3 2 4 2h-1c2 2 5 2 7 3 3 2 6 3 8 4 1 0 1 1 2 1v1c-1-1-3-1-4-1s-1-1-2-1h-3c-1 0-2-1-2-1-1 0-1 1-2 1l-1-2h-1s-2-1-3-2c0 0-2-1-3-1l-1-1c-1-1-2-2-3-2l-2-2c-1-1 0 0-1 0 0-1-1-1-1-2-1 0-1-1-2-1l-3-2c2-2 3 0 4-1s0-2 0-2z" class="P"></path><path d="M359 479h0v-2h0 0c1 1 2 1 3 1 2 1 3 2 5 3h-3c-1 0-2-1-2-1-1 0-1 1-2 1l-1-2z" class="G"></path><path d="M359 479c2 0 2 0 3 1-1 0-1 1-2 1l-1-2z"></path><path d="M331 534l5-24c0 1 1 2 2 3h0l1-3h1v1h1 0l1-1v3c-1 2-2 5-3 8 0 0-1 2-1 3h-1c-1 2-2 8-4 9l-2 2v-1z" class="G"></path><path d="M339 510h1v1h1 0l1-1v3c-1 2-2 5-3 8 0 0-1 2-1 3h-1c-1 2-2 8-4 9 2-7 4-13 5-20l1-3z" class="i"></path><path d="M174 246h1c1 1 1 1 2 1 2 2 5 5 5 8 1 0 0 1 0 1 0 1 0 1-1 2v3c-2 2-4 5-6 5-2 1-6 1-7 0-2-1-4-4-4-6 0-1 1-1 1-2v-1h-2c-1-1-1-1-1-2l1-1h3 0c0 1-1 1-2 1h0l3 1c1 0 2 1 3 2-1 2-2 2-3 3 0 1 1 2 2 2 1 1 2 2 4 1 2 0 3-1 5-3 1-1 2-3 1-5 0-4-4-7-7-9 0-1 1-1 1-1h1z" class="J"></path><path d="M579 101c0 1 1 2 1 3v1h-1c-3 0-4 0-6 2-1 2-2 5-2 7l1 1v-1c0 1 1 2 1 2 0 1-1 1 0 2l-1 4c-1 3-1 6-1 10 1 3 3 7 2 10-2-3-3-9-4-13s-2-18 1-21c1-1 1-3 2-4l2-2h0c2-1 3-1 4 0l1-1z" class="c"></path><path d="M579 101c0 1 1 2 1 3v1h-1c-1-1-4-3-5-3h0c2-1 3-1 4 0l1-1zm-7 13c0 1 1 2 1 2 0 1-1 1 0 2l-1 4c-1-1-2-2-2-3h0-1c1-1 1-2 1-3h1c0-1 0-1 1-1v-1z" class="H"></path><path d="M570 119h0-1c1-1 1-2 1-3h1l1 1v1c-1 1-1 1-2 1z" class="f"></path><path d="M369 126c1 1 1 1 2 1l1 5c1 2 2 3 2 5l-1 2-1-2c0 1 0 2-1 3h0 0 0c0 1-1 2-1 3v3c-1-1-2-1-3-1h0v-1c-1-2-1-3-1-4v-2h0l-3 4-4-1h0c1-1 1-1 1-2 4-4 7-8 9-13z" class="J"></path><path d="M370 136v-1c-1-1-1-1 0-3h1c0 1 1 2 1 3h-2v1z" class="F"></path><path d="M370 136v-1h2v2c0 1 0 2-1 3h0 0 0c0 1-1 2-1 3v-7z" class="N"></path><path d="M298 107c2-1 4-1 6-1h0v1l-1 1-6 3-5 3h-2l-2 1-1-1-5 3c-2 1-4 2-6 4-1-1-1-1-3-1h1c2-1 3-2 5-3 1-1 2-2 2-3h-1l1-1h-1-1 0v-2l7-2 1 1c2-1 3-1 6-1l5-2z" class="I"></path><path d="M302 107l2-1v1l-1 1-6 3c-1-1-1-1-1-2l6-2z" class="F"></path><path d="M302 107l2-1v1l-1 1h0c-1 0-1-1-1-1z" class="R"></path><path d="M287 114h0c3-2 5-4 9-5 0 1 0 1 1 2l-5 3h-2l-2 1-1-1z" class="G"></path><path d="M279 111l7-2 1 1c2-1 3-1 6-1-4 2-8 3-12 5h-1l1-1h-1-1 0v-2z" class="S"></path><path d="M279 111l7-2 1 1c-2 0-5 1-7 3h-1 0v-2z" class="B"></path><path d="M385 127c6 2 11 7 13 13 4 9 6 20 2 30-2 3-5 10-9 12h-2l-1-1c3-3 6-5 8-9 3-5 4-12 4-19s-3-16-9-21c-2-2-4-3-6-4v-1z" class="G"></path><path d="M79 534c3 6 5 13 8 19 3 7 8 14 13 20 5 7 11 13 18 18l18 10c5 3 10 6 16 8 5 2 11 3 16 5 1 0 3 0 4 1h-1c-4 0-8-2-12-3-2-1-5-1-7-2-11-4-20-9-29-15-6-4-11-7-16-12-3-2-5-6-8-9h0l-5-8c-2-2-3-4-5-6-2-4-4-9-6-13s-3-7-5-11c1-1 1-1 1-2zm305 4c4-1 10 0 14 2 3 2 7 4 8 7v1 2 3c0 1 0 1-1 2 1 2 1 4 2 6h-2 0c-2-3-2-4-2-7-1-2-1-4-1-5l-2-3c-5-4-8-6-14-6-3 0-5 1-8 2l-1-1c1 0 2-1 3-1 2-1 2-1 3-2h1z" class="P"></path><path d="M403 546h1l2 2v2l-2-1-1-3z" class="E"></path><path d="M383 538h1c1 0 2 0 3 1h-3 0 2v1c-3 0-5 1-8 2l-1-1c1 0 2-1 3-1 2-1 2-1 3-2z" class="J"></path><path d="M402 549v-1h0c0-1-1-2-2-3-2-1-3-3-4-4 3 1 5 2 7 5l1 3 2 1v3c0 1 0 1-1 2 1 2 1 4 2 6h-2 0c-2-3-2-4-2-7-1-2-1-4-1-5z" class="a"></path><path d="M404 549l2 1v3c0 1 0 1-1 2l-1-6z" class="J"></path><path d="M375 458h1s2 0 2 1h1c1 0 2 0 3 1l1 1v1l-1 1c1 1 2 1 3 1h0l1 1c1 0 1 1 1 1 0 1 1 1 1 2l1 1h3l2 1h-1c0 1-1 1-1 1v1h-2-1c1 0 1 1 2 1 0 2-1 2-2 2l-2-1-1-1-2 1-1-1 1-1c0 1 1 0 1 1 0-1-1-1-1-2h0 0 1 3l-1-1c-2-2-8-2-11-2h-2 0c1-2 3-3 4-4h-5v-1c1 0 1 0 2-1v-2-2z" class="b"></path><path d="M382 460l1 1v1l-1 1h0l-2-1c-1 0-1-1-2-2h0 4z" class="e"></path><path d="M532 113v-2c-1-2-1-5 1-7 1-2 3-4 5-4h2c2 2 2 5 4 7 2 3 5 5 7 8h0c2 4 2 8 3 12h0c-1-2-2-6-3-8s-3-4-4-6c0 3 2 5 3 8-2-1-6-4-7-6v-2c1 0 2-1 2-2l-4-4s-1-2-2-2h-1c-1 0-2 1-2 2-2 1-3 2-3 4 0 1 0 1-1 2zM352 252l1 1h2l-1 1-2 2h1 1 0c1 0 1-1 2-2v-1 2c1 1 1 2 0 3v2 1h1 1 0c0 1 0 2 1 3-1 1-1 2-2 3v1l-1 2-1-1v-1h-1 0l-2-1-2-1h-1v2 1h-1l-1-1v5c-2 0-1-1-2-2v-3l-1-1v-2c1 0 1-1 1-1v-1c0-1 1-3 2-4l1 1h-1c-1 2-1 2-1 4h1 1 1c1-1 1-1 1-2s0-2 1-3v-1h-3l3-2c0-1 1-3 1-4z" class="b"></path><path d="M356 261h1 1 0c0 1 0 2 1 3-1 1-1 2-2 3v1l-1 2-1-1v-1h-1 0l-2-1-2-1v-2h1c0 1 1 1 2 1v-1l1-2h-1c2 0 2 0 3-1z" class="H"></path><path d="M358 261c0 1 0 2 1 3-1 1-1 2-2 3 0-2 0-4 1-6z" class="G"></path><path d="M356 261h1-1c-1 2-1 3-1 5l-2-2 1-2h-1c2 0 2 0 3-1z" class="F"></path><path d="M350 266v-2h1c0 1 1 1 2 1v-1l2 2-1 1h-1l-1-1v1l-2-1z" class="D"></path><path d="M424 604l1 3 4 6c4 7 10 10 17 12 1 0 2 0 2-1h0c2-1 4 0 6 0 1 0 2 1 3 1h2v1h-2c-7 3-14 2-21-2-6-3-10-9-12-16l-2-4h2z" class="C"></path><path d="M424 604l1 3v1l-1-2v2l-2-4h2z" class="X"></path><path d="M446 625c1 0 2 0 2-1h0c2-1 4 0 6 0 1 0 2 1 3 1h2v1h-2 0c-4 0-7 0-11-1z" class="l"></path><path d="M133 127l1-1 3-5c1-1 1-1 2-1l-1 2h1l3-2c-1 2-3 4-4 5l1 1-5 7c-1 3-2 6-4 9l-6 19h0l-1-1h0l2-4-1-2v-1-1c1-3 1-6 3-9 0-1 1-3 1-4l2-5c0-1 1-3 2-4h0v-1c0-1 1-2 1-2z" class="O"></path><path d="M142 120c-1 2-3 4-4 5s-2 3-3 4c0-2 2-6 3-7h1l3-2z" class="U"></path><path d="M138 125l1 1-5 7-1-1 2-3c1-1 2-3 3-4z" class="D"></path><path d="M130 134l1 1h0v1c0 1-1 1-1 2l-3 6c0 2-1 7-3 9v-1c1-3 1-6 3-9 0-1 1-3 1-4l2-5z" class="i"></path><path d="M125 156c0-3 1-4 2-6l6-18 1 1c-1 3-2 6-4 9l-6 19h0l-1-1h0l2-4z" class="N"></path><path d="M455 116c3 0 7 0 10 1 6 1 11 5 16 9 3 1 5 3 8 5h1 0l2 2c1 0 1 0 2 1l-1 1c-1-1-1-1-2-1-2 0-3-2-4-3-3-2-8-6-11-7-1 0-2-1-2-1l-2 1-3-2c0 1 1 1 1 2 5 3 10 6 14 9-6-2-10-5-15-8-5-4-11-6-16-8l2-1z" class="D"></path><path d="M469 122c-1-1-2-1-3-2l1-1c3 1 4 3 7 4l-2 1-3-2z" class="R"></path><path d="M389 532l3-2 1 2-10 6h0c-1 1-1 1-3 2-1 0-2 1-3 1l1 1-2 1c-3 2-5 3-7 4l-7 5c-1 0-2 2-3 2h0-1c-1 0-1 1-2 1l-2 2-2 2c-1 0-2 1-3 2h-1l10-8-1-1c5-4 10-8 15-11l8-4 7-6h1l1 1z" class="B"></path><path d="M376 543l-1-1c0-1 1-1 2-1l1 1-2 1z" class="P"></path><path d="M380 537l7-6h1l1 1c-2 1-5 4-7 5h-2z" class="S"></path><path d="M183 141l1 1c-4 5-9 10-14 15a30.44 30.44 0 0 0-8 8l-7 8c-1 2-2 3-4 5v-1l-1-1c0-6 18-22 24-27 3-2 6-4 9-7v-1z" class="R"></path><path d="M323 100h10s-1 1-2 1h1c-2 1-3 1-5 1-1 1-4 2-5 1h-1-1c-1 0-3 1-4 1l-12 3v-1h0c-2 0-4 0-6 1l-5 2c-3 0-4 0-6 1l-1-1 4-2 6-2 6-2 1 1v-1-1l20-2z" class="E"></path><path d="M323 100h10s-1 1-2 1h1c-2 1-3 1-5 1-1-1-2 0-3 0l1-1h2c-1-1-3 0-4-1h0z" class="B"></path><path d="M290 107l6-2c-1 2-2 2-4 2 1 0 2 1 3 0s2 0 3 0l-5 2c-3 0-4 0-6 1l-1-1 4-2z" class="F"></path><path d="M272 121c-4 2-7 4-11 6-5 2-10 3-15 4 4-3 9-5 14-8 6-3 12-9 19-12v2h0 1 1l-1 1h1c0 1-1 2-2 3-2 1-3 2-5 3h-1l-1 1z" class="M"></path><path d="M280 113h1l-1 1h1c0 1-1 2-2 3-2 1-3 2-5 3v-1l-1-1c-2 1-4 3-5 4-3 1-6 3-8 4l-1-1 10-6c4-2 7-4 10-6h0 1z" class="W"></path><path d="M273 118c1 0 1 0 2-1h0c1 0 1-1 1-1 1 0 2 0 3 1-2 1-3 2-5 3v-1l-1-1z" class="c"></path><defs><linearGradient id="AK" x1="400.229" y1="269.167" x2="391.122" y2="263.361" xlink:href="#B"><stop offset="0" stop-color="#7c7c80"></stop><stop offset="1" stop-color="#9b9a99"></stop></linearGradient></defs><path fill="url(#AK)" d="M384 293c1 0 1-1 1-2 0-3 1-7 3-11 1-5 3-10 5-16l13-25 1 2c-2 6-5 11-8 17l-4 9c-4 9-7 17-9 27-1 0-1 0-2-1z"></path><path d="M413 97l7-2-4 4 27-1c2 0 7-1 9 1l-21 1c-1 0-2 1-3 1l1 1h-4c-1 0-3 1-4 1s-2-1-2-1l-13 2h0v-1h-1l-8 2c3-3 8-4 12-6h1c1 1 2 0 3 0l-1-1h1v-1z" class="R"></path><path d="M419 101c0-1 2 0 3 0l9-1c-1 0-2 1-3 1l1 1h-4c-1 0-3 1-4 1s-2-1-2-1l1-1h-1z" class="Z"></path><path d="M405 103l14-2h1l-1 1-13 2h0v-1h-1z" class="V"></path><defs><linearGradient id="AL" x1="393.353" y1="142.084" x2="391.076" y2="113.073" xlink:href="#B"><stop offset="0" stop-color="#49494b"></stop><stop offset="1" stop-color="#7f7e7f"></stop></linearGradient></defs><path fill="url(#AL)" d="M381 113c2 0 9 0 10 1 1 0 1 1 2 2l2 2c3 2 7 4 9 8 0 1 1 2 1 3 2 3 3 8 3 12h-1c-1-2-1-4-2-7-3-7-9-13-17-16l-1-1c-5 0-9 0-13 3 0-2 3-3 4-4v-1s1 0 1-1c1 0 1 0 2-1z"></path><path d="M404 126c0 1 1 2 1 3h-1l-1-1c1-1 1-1 1-2h0z" class="T"></path><defs><linearGradient id="AM" x1="370.324" y1="497.234" x2="372.757" y2="536.63" xlink:href="#B"><stop offset="0" stop-color="#848280"></stop><stop offset="1" stop-color="#a7a9ae"></stop></linearGradient></defs><path fill="url(#AM)" d="M351 538l9-14c4-7 9-13 13-19l6-6c2-2 6-2 9-3h3c-1 1-1 0-2 2-3 1-5 2-7 3-6 4-10 11-14 17l-16 22c-1-1-1-1-1-2z"></path><path d="M282 383v3 9c0 3 0 6 1 9v-1h1l1 3 1-1 1 3v3l2 4 1 7c1 2 2 3 3 5l-2 2c0-1 0-3-1-4l-1 1v-1 1l-1 1v-2c-1-1-1-2-2-4h-1l-1-4c-1-1-1-2-1-3v-1c-1-1-1-2-1-3l-1-1c-1-3-1-5-1-8l-1 5h0c0-5 0-10 1-15l1-5 1-3z"></path><path d="M280 391c2 4 1 8 1 13 1 1 1 2 1 3l1 5 1 2v1c1 1 1 2 1 3l1 3h-1l-1-4c-1-1-1-2-1-3v-1c-1-1-1-2-1-3l-1-1c-1-3-1-5-1-8l-1 5h0c0-5 0-10 1-15z" class="I"></path><path d="M283 404v-1h1l1 3 1-1 1 3v3l2 4 1 7c1 2 2 3 3 5l-2 2c0-1 0-3-1-4l-1 1v-1l-6-21z" class="H"></path><path d="M285 406l1-1 1 3v3l2 4-1 1h-1l-2-10z"></path><path d="M289 415l1 7c1 2 2 3 3 5l-2 2c0-1 0-3-1-4l-3-9h1l1-1z" class="V"></path><path d="M352 226c2 0 3 2 5 2h0c3 2 3 7 4 10v3 3c-1 1 1 3 0 5-1 0-2 0-2 1h0 1c0 1 0 1-1 2 1 1 1 1 0 3h0c-1 0-1 0-2 1h0c0 2 0 3-1 4v-2c1-1 1-2 0-3v-2 1c-1 1-1 2-2 2h0-1-1l2-2 1-1h-2l-1-1h0l2-4v-1c1-1 1-2 2-2 2 1 2 1 4 1v-1l-2-1c-2-1-3-1-5-1l2-1v-1h0s1-1 2-1h0c0-1-1-1-1-2h-1v-2c-1-1 0-1-1-1v-2l3 3c0-1 0-1-1-2v-1c-1-1-3-2-4-4h0l1-2-1-1z" class="C"></path><path d="M356 248h1l1 1-1 1c-1 0-1 0-2-1l1-1z" class="X"></path><path d="M359 255v-1h-1l-1-1h1c1-1 1-1 1-2v1c1 1 1 1 0 3h0z" class="P"></path><path d="M352 252c1 0 2-1 3 0h0v1h-2l-1-1h0z" class="e"></path><path d="M353 243l2-1v-1h0c1 1 1 1 2 1l1 1v1c-2-1-3-1-5-1z" class="X"></path><path d="M352 226c2 0 3 2 5 2h0c3 2 3 7 4 10-1-1-1-2-3-2h0v-2h1c-1-3-4-6-6-7l-1-1z" class="M"></path><path d="M123 160l1 1h0l-3 12h1 1 0l1 1-1 10v21c0 2-1 6-1 7l1 1v13c-1-3 0-6-1-9s-1-5-1-8c-2-14-3-27 0-42 1-2 1-4 2-7z" class="c"></path><path d="M122 179c0 1 0 3-1 3h0 0c-1-2 0-5 0-6 0 1 0 2 1 3z" class="W"></path><path d="M121 173h1 1l-1 4v2c-1-1-1-2-1-3v-3z" class="i"></path><path d="M123 173h0l1 1-1 10c0-1-1-1 0-2 0-1 0-1-1-2v-3l1-4z" class="a"></path><path d="M342 408c1 3 3 7 5 10 1 2 2 5 4 7 0-3-4-7-5-9s-1-4-2-6c-1-4-2-7-2-11 1 1 1 4 2 6 1 4 2 9 5 13 0-2-2-5-2-6-2-4-3-9-3-13v-3c1 2 1 6 2 8 1 5 3 9 5 13h1c-3-7-6-14-7-23h1c1 3 1 6 2 10l3 9c2 6 4 12 8 17l-1 1-2-2c0-1-1-1-1-2h0l-1 1h1c1 1 1 3 2 4 0 1 1 1 1 2l-1 1c-4-6-9-10-12-17v-1c-1-2-2-5-3-8v-1h0z" class="J"></path><path d="M460 104c3-1 5-4 8-4 0 0 1-1 2-1 4 0 8 2 11 3v1c-4-1-7-2-10-2h-1v3 2c-2 1-3 2-5 3l-5 2-6 3c-1 1-3 2-3 2h4l-2 1c-1 1-1 1-2 0h-2c-1 1-3 2-4 2h-1l-1-1h-3c3 0 4-1 7-2h-8 0 6c2-1 4-2 5-4 3-2 8-4 10-8z" class="G"></path><path d="M461 108h-2c2-3 4-3 6-4 1-1 2-2 4-3h1v3c-3-1-4 0-6 1l-3 3h0z" class="U"></path><path d="M461 108l3-3c2-1 3-2 6-1v2c-2 1-3 2-5 3h-2-1c-1 0-1-1-1-1z" class="H"></path><path d="M461 108h0s0 1 1 1h1 2l-5 2-6 3c-1 1-3 2-3 2h4l-2 1c-1 1-1 1-2 0h-2v-1c1-2 2-2 3-3 1 0 2-1 3-2l6-3z" class="c"></path><path d="M461 108h0s0 1 1 1h1 2l-5 2-6 3h0l3-3h1c1-1 2-1 2-2h0 1v-1z" class="G"></path><path d="M144 164h1 0v1l1 1c1-1 2-2 3-1-1 1-2 1-3 1h0l-1 1c-1 3-5 6-7 9-8 13-13 31-12 47-1 1-1 2-1 3l-1 2v1 1 1l-1-3h0v-2-13c0-5 1-10 3-15 3-12 10-25 18-34z" class="L"></path><path d="M336 213v1h1l-1-1v-1h3l1-1h1c3-2 5-4 7-5 4-4 8-9 14-11-7 6-13 11-18 19h0l7-8 1 1 1 1c-3 3-7 7-9 11h0 0c3-4 6-7 9-10l1 1-6 6c-1 1-1 2-2 3v1l-1 1v1h0v1l-1 2h0 1v1l-2 2-2-2h0s-1 0-1-1 1-1 2-3v-1c1 0 1-1 1-1-1-1-1-1-2-1h-1v1c-2-1-3-2-5-4-1-1-2-2-2-3h3z" class="C"></path><path d="M320 560l1 1-9 13c-2 3-4 7-7 10l-7 8c-1 2-3 3-4 5 8-5 13-11 19-18l1 2c-7 10-18 16-28 22-6 4-12 8-19 10-3 2-7 3-11 4h0l-2-2c8-2 15-5 21-9 6-3 12-6 18-10 11-10 19-23 27-36z" class="D"></path><path d="M399 203l1 1h0s0 1 1 1h0c2 0 5 1 6 3 1 0 2 1 2 2h-1c-5-1-10-1-14 3-3 2-5 8-6 11 0 4 1 7 3 9s3 3 5 3c3 0 6-1 8-3 0 1-1 2-1 3-2 2-4 4-7 4h-3c-1-1-2-1-3-2 0-2-2-3-3-5v-2c-1-3 0-7 0-10 1-2 1-3 1-5l1-1c1-2 3-3 4-5h-1v-1l1-1c-2 1-3 1-4 2-1 0-1 0-2 1 2-3 7-4 10-6l2-2z" class="X"></path><path d="M399 203l1 1h0s0 1 1 1h0 0c-2 1-3 4-5 3h0 0c1-1 1-1 2-1 0-1-1-1-1-2l2-2z" class="K"></path><path d="M357 435l1-1c0-1-1-1-1-2-1-1-1-3-2-4h-1l1-1h0c0 1 1 1 1 2l2 2 1-1c1 2 3 4 5 5l-1-1h1 0c1 1 2 1 3 1h0c1 1 1 2 2 2l1 1 3 2c0 1 0 0 1 1l1 1h0c3 2 6 4 9 5 3 2 4 3 8 3-2 2-4 2-7 1h-1c-1 1-3 0-4 0s-2 0-3-1v-1h-1c-2-1-4-3-5-4l-2 1c-4-3-10-6-12-11z" class="W"></path><path d="M373 444c2 1 4 2 5 3 3 1 5 2 7 4-4-2-9-3-12-6v-1z" class="D"></path><path d="M363 434h1 0c1 1 2 1 3 1h0c1 1 1 2 2 2l1 1 3 2c0 1 0 0 1 1l1 1h0c3 2 6 4 9 5h-1c-5-1-8-4-12-6v-1l-1-1-4-2v-1c-1 0-1-1-2-1l-1-1z" class="B"></path><path d="M359 430c1 2 3 4 5 5 1 0 1 1 2 1v1l4 2 1 1v1c0 1 1 2 2 3v1l-9-7-1-1c-1-1-1 0-1-1l-4-5 1-1z" class="E"></path><path d="M357 435l1-1c0-1-1-1-1-2-1-1-1-3-2-4h-1l1-1h0c0 1 1 1 1 2l2 2 4 5c0 1 0 0 1 1l1 1c1 3 5 4 7 7l-2 1c-4-3-10-6-12-11z" class="B"></path><defs><linearGradient id="AN" x1="375.968" y1="382.612" x2="362.395" y2="383.029" xlink:href="#B"><stop offset="0" stop-color="#9c9c9d"></stop><stop offset="1" stop-color="#d9d9d8"></stop></linearGradient></defs><path fill="url(#AN)" d="M374 320c1 3 1 7 1 11v18-3 4h0c0-1 0-3 1-4v1 19c0 18 1 40-10 55l-4-3c4-3 7-7 9-11 0-3 0-6 1-8l1-17 1-34v-28z"></path><path d="M367 273l8-1v16l-18-7c-2-1-5-1-7-2h0l2-2h0-2l1-1h1 1c1 0 1 0 1-1 1-1 3-1 4-1l9-1z"></path><path d="M366 567c9-4 24-4 33 0 4 2 9 4 12 7 6 4 10 11 13 17l3 8 3 6c-2-2-3-4-4-7-2-3-3-7-5-10 0-1-1-2-2-3h0c0 1 0 1-1 2l-3-5h-1c0-1 0-1-1-2l-1-1-1 1c-2-3-5-5-8-7-10-8-23-6-35-4l-13 4h-1v-1c4-2 8-4 12-5z" class="b"></path><path d="M415 582v-1h1l-1-1h1l3 5c0 1 0 1-1 2l-3-5z" class="e"></path><path d="M354 572c4-2 8-4 12-5h0c-1 2-4 2-6 3h2c1 0 0 0 1-1h2 0c1-1 2 0 3 0l-13 4h-1v-1z" class="C"></path><path d="M281 375v3c0 2-1 5-1 8h1l-1 5c-1 5-1 10-1 15h0l1 12c1 2 1 3 1 4 0 3 1 5 1 8l1 8h0-2l-2-2c-1 1 0 1-1 1v-1c-1 0-2 0-3-1h-1-2v-5-2s-1 0-1-1 1-1 1-1v-3l1-1 1 1v-1c0-1 0-2 1-3h3v-4l-1 1v-5c1-1 0-2 1-3 0-1 0-2-1-3v-9h0v-1c0-2 1-2 1-4h0c0-1 1-3 1-4 0-4 1-8 2-12z" class="F"></path><path d="M279 406h0l1 12-1 1v4c-2-5 0-11 0-17z" class="G"></path><path d="M280 418c1 2 1 3 1 4 0 3 1 5 1 8v2l-1-1v1l-1-1h0l-1-1v-7-4l1-1z" class="T"></path><path d="M281 431v-2c-1-3-2-4-1-6l1-1c0 3 1 5 1 8v2l-1-1z" class="G"></path><path d="M276 421v-1h1v4c1 4 0 10 3 13h1c-1-3-2-5-2-7l1 1h0l1 1v-1l1 1v-2l1 8h0-2l-2-2c-1 1 0 1-1 1v-1h0c0-1 0-2-1-3v-1l-1-6v-4-1z" class="a"></path><path d="M274 422l2-1v1 4l1 6v1c1 1 1 2 1 3h0c-1 0-2 0-3-1h-1-2v-5-2s-1 0-1-1 1-1 1-1v-3l1-1 1 1v-1z" class="B"></path><path d="M274 435v-2h1v2h-1z" class="D"></path><path d="M274 422l2-1v1l-1 1c0 2-1 2-2 4h-1v-1-3l1-1 1 1v-1z" class="U"></path><path d="M341 259c2 0 5 0 7-1h3v1c-1 1-1 2-1 3s0 1-1 2h-1-1-1c0-2 0-2 1-4h1l-1-1c-1 1-2 3-2 4v1s0 1-1 1v2l1 1v3l-2 2h0c1 1 1 1 2 1 0 1-1 1-1 2s-1 2-1 3v1 1h0c0 1 0 1 1 1v1h-2c0 1 1 1 1 2l-2-1v1s0 1-1 2h-1 0-1-2l-1 1-2-1c-2-1-2-1-4-1h-2l3-3v-3h2c2-1 3-2 5-4 1-1 2-3 3-4l-2-1c1-2 0-5 0-7 0 0 0 1 1 2h0v-4h0s0-1 1-1c0-1 0-1 1-2z" class="e"></path><path d="M341 280l1-1v1 1h1c0 1 0 1 1 1v1h-2-1v-1-2z" class="C"></path><path d="M339 262c1 3 1 7 1 10l-2-1c1-2 0-5 0-7 0 0 0 1 1 2h0v-4h0z" class="h"></path><path d="M341 280l-1-1h-1v1c0 1-1 2-1 2h-1v-3c1 0 1 0 1-1h0 0c1-1 2-2 3-2 1-1 1 0 2 0h0c0-1 1-1 1-2-1 0-1 0-1-1h0 0c1 1 1 1 2 1 0 1-1 1-1 2s-1 2-1 3v1 1h0-1v-1-1l-1 1z" class="K"></path><path d="M330 280h2v1 1c1 1 1 1 2 1 1-1 1-1 2-1v2c0 1 1 2 1 2 1 1 1 1 2 1h-1-2l-1 1-2-1c-2-1-2-1-4-1h-2l3-3v-3z" class="I"></path><path d="M337 286h-1c-2 0-3-1-4-3-1-1 0-1 0-2v1c1 1 1 1 2 1 1-1 1-1 2-1v2c0 1 1 2 1 2z" class="K"></path><path d="M353 243c2 0 3 0 5 1l2 1v1c-2 0-2 0-4-1-1 0-1 1-2 2v1l-2 4h0c0 1-1 3-1 4l-3 2c-2 1-5 1-7 1-1 1-1 1-1 2-1 0-1 1-1 1h0v4h0c-1-1-1-2-1-2l-1-1c0-2-1-5-1-8s1-6 3-8 5-3 7-3c2-1 5-1 7-1z" class="d"></path><path d="M345 250c1 1 1 2 1 4h0 0c-2 1-2 1-3 0h1v-1c-1-1 0-2 1-3z" class="E"></path><path d="M351 250v2l1-1c0-1 1-2 2-3l-2 4h0c0 1-1 3-1 4h-2-1l1-1c1-2 1-3 2-5z" class="I"></path><path d="M344 257h0c2-1 3-1 5-2l-1 1h1 2l-3 2c-2 1-5 1-7 1 1 0 2-1 3-2zm1-7v-1c2-1 2-1 5-1-1 2-2 4-4 6h0c0-2 0-3-1-4z" class="Q"></path><path d="M350 248h1v2c-1 2-1 3-2 5-2 1-3 1-5 2h0c-1 0-2-1-3-1-1-1-2-2-2-3 0-2 0-2 1-3h1v2c0 2 1 2 2 2 1 1 1 1 3 0h0c2-2 3-4 4-6z" class="B"></path><path d="M454 114l6-3 1 1h1l1 3c1 0 1-1 1-1h5l2-1c0 1 0 2 1 2h2l5 3c1 1 2 1 3 2h-1c4 3 8 7 12 10 3 2 7 3 10 5 2 1 4 1 6 3 1 0 2 2 3 3-5 0-9-2-13-4-3-1-5-1-8-3 1 0 1 0 2 1l1-1c-1-1-1-1-2-1l-2-2h0-1c-3-2-5-4-8-5-5-4-10-8-16-9-3-1-7-1-10-1h-4s2-1 3-2z" class="F"></path><path d="M471 113c0 1 0 2 1 2h2l5 3c1 1 2 1 3 2h-1c-1 0-2-1-3-2l-6-3h-1c-1 0-2 0-3 1l-4-1h1l4-1 2-1z" class="S"></path><path d="M454 114l6-3 1 1h1l1 3c1 0 1-1 1-1h5l-4 1h-1l4 1c4 3 9 6 13 8 3 2 6 5 10 6 1 0 2 1 3 2-1 0-3-1-4-1h-1c-3-2-5-4-8-5-5-4-10-8-16-9-3-1-7-1-10-1h-4s2-1 3-2z" class="h"></path><path d="M461 112h1l1 3c1 0 1-1 1-1h5l-4 1h-1-6v-1h2c0-1 0-2 1-2z" class="i"></path><path d="M384 430h1 2 1l1-1h1 0c-1 1-1 1-2 1-1 1-2 1-3 2h2v1c2-1 5-2 7-2 2-1 3-1 4-1h1c-1 1-2 2-3 2l-7 3-3 1c-1 1-3 2-4 2 1 2 2 4 4 5 4 1 6 1 10-1l3-2 1 1c-1 0-1 1-1 1-1 2-2 3-3 5-1 1-3 2-4 3-4 0-5-1-8-3-3-1-6-3-9-5h0l-1-1c-1-1-1 0-1-1l-3-2-1-1c-1 0-1-1-2-2h2l1 1h3c1 0 0 1 1 1h1c-1-1-1-2-3-2h0v-1h1c3 0 8-3 11-4z" class="E"></path><path d="M383 435c0-1 1-1 2-1l1 1h0-2-1z" class="J"></path><path d="M386 435c0-1 1-1 2-1l1 1-3 1v-1h0z" class="B"></path><path d="M383 435h1 2v1c-1 1-3 2-4 2s-1 0-1 1c-1-1-2-1-2-2s0-1 1-1l3-1z" class="C"></path><path d="M383 435h1c0 1-1 1-2 1h0-2l3-1z" class="B"></path><path d="M375 442c2 1 5 3 7 3v-1-1h0c1 0 1 0 2 1h0 3c5 2 8 0 12-2-1 2-2 3-3 5-1 1-3 2-4 3-4 0-5-1-8-3-3-1-6-3-9-5z" class="O"></path><path d="M382 445v-1-1h0c1 0 1 0 2 1h0 1 1c2 2 5 2 7 1h2 0c0 1-2 2-3 2-2 1-5 0-7-1 1 1 1 1 2 1v1c-2 0-3-2-5-3z" class="F"></path><path d="M292 410h0l2 7v-1l1 1h0l2 1c1 2 1 6 3 8v1 2c1 4 4 6 5 10v2c1 2 3 5 4 7 6 10 14 20 23 26 3 3 7 5 10 6 4 2 8 3 11 5-4 1-18-7-21-10-6-3-10-8-14-13h-1c-4-4-7-7-10-12l-7-13-2-2v1 1c-3-3-4-7-5-10-1-2-2-3-3-5l1-1 2 3h1l-3-12 1-2z" class="D"></path><path d="M291 421l2 3h1l7 12-1 1-2-2v1 1c-3-3-4-7-5-10-1-2-2-3-3-5l1-1z" class="Q"></path><path d="M291 421l2 3c2 4 3 8 5 11v1 1c-3-3-4-7-5-10-1-2-2-3-3-5l1-1z" class="N"></path><path d="M300 437l1-1c3 6 7 12 11 18l6 8h-1c-4-4-7-7-10-12l-7-13z" class="V"></path><path d="M294 417v-1l1 1h0l2 1c1 2 1 6 3 8v1 2c1 4 4 6 5 10v2c-3-4-5-9-7-14-2-3-3-6-4-10z" class="I"></path><path d="M295 417l2 1c1 2 1 6 3 8v1 2c-1-3-2-5-4-7 0-2-1-3-1-5z" class="H"></path><path d="M371 127v-1c2-1 6 0 8 1l2 1v-1c1 0 1 1 2 1 1 1 2 2 3 4l-1 1c0 3 1 6-1 9 0 1-1 2-2 2v1l-3 2c0 1-1 1-2 2l-1 2-1 1c-1 1-2 1-3 1l-1-1h0v-1l-1-1-1-2 1-2v-3c0-1 1-2 1-3h0 0 0c1-1 1-2 1-3l1 2 1-2c0-2-1-3-2-5l-1-5z"></path><path d="M381 127c1 0 1 1 2 1 1 1 2 2 3 4l-1 1c-1-2-2-4-4-5v-1z" class="F"></path><path d="M373 131h1c3 1 5 1 7 4 1 2 1 5 0 7 0 2-1 3-1 4-1 0 0 0-1 1 0 1-1 1-2 2 1-3 1-7 1-10 0-4-2-5-5-8z" class="B"></path><path d="M373 131c3 3 5 4 5 8 0 3 0 7-1 10l-1 2-1 1c-1 1-2 1-3 1l-1-1h0v-1l-1-1-1-2 1-2v-3c0-1 1-2 1-3h0 0 0c1-1 1-2 1-3l1 2 1-2c0-2-1-3-2-5l1-1z" class="h"></path><path d="M374 137c0 2 1 4 0 6-1-2-1-3-1-4l1-2z" class="E"></path><path d="M373 147h2c1 1 0 3 0 5-1 1-2 1-3 1l-1-1h0v-1l-1-1v-1l1-1h1l1-1z" class="V"></path><path d="M371 148h1c0 1 0 3-1 4v-1l-1-1v-1l1-1z" class="D"></path><path d="M372 137l1 2c0 1 0 2 1 4l-1 4-1 1h-1l-1 1v1l-1-2 1-2v-3c0-1 1-2 1-3h0 0 0c1-1 1-2 1-3z" class="B"></path><path d="M370 143c0-1 1-2 1-3h0 0v3c1 1 0 3 0 5l-1 1v1l-1-2 1-2v-3z" class="S"></path><path d="M409 383l1-1c1 1 1 6 1 7 0 5 0 11-1 15h-1c-3 6-7 10-12 15h1c-2 1-3 2-4 4-1 1-2 1-3 1-3 3-5 4-7 6-3 1-8 4-11 4l-1-1c2 0 3 0 5-1 5-2 10-6 14-10-1 0-1-1-1-2s2-3 3-3v-1-1l-1-1 1-1c2-1 3-4 3-7h2v-1h1c0-1 0-1 1-2v-2h0l1-1 1 1v-3l1-1h0l4-8 2-6z" class="O"></path><path d="M409 393v6c0-1 0-3-1-4l1-2z" class="i"></path><path d="M406 402h0c0-2 1-3 1-4s0-2 1-3c1 1 1 3 1 4s-1 2-1 3h0l-1-1c-1 2-1 2-2 3l1-2z" class="f"></path><path d="M404 402l1-1h0l1 1-1 2c1-1 1-1 2-3l1 1c-1 2-2 4-4 5-1 1-2 2-4 3 1-2 1-3 2-5l1-2 1-1z" class="c"></path><path d="M404 402l1-1h0l1 1-1 2-1 1h-2l1-2 1-1z" class="T"></path><path d="M391 424c1-2 3-4 5-7 1-2 3-4 5-6 1-1 2-2 4-3 1-1 2-2 3-4h1c-3 6-7 10-12 15h1c-2 1-3 2-4 4-1 1-2 1-3 1z" class="R"></path><path d="M409 383h1c0 4 0 6-1 10l-1 2c-1 1-1 2-1 3s-1 2-1 4h0l-1-1h0l-1 1-1 1h-1v1l-1 1c0-2 0-3 1-4v-3l1-1h0l4-8 2-6z" class="j"></path><path d="M407 389h1c-1 3-3 7-3 9 0 1-1 3-1 4l-1 1h-1v1l-1 1c0-2 0-3 1-4v-3l1-1h0l4-8z" class="U"></path><path d="M399 405c0-1 0-1 1-2v-2h0l1-1 1 1c-1 1-1 2-1 4l1-1v-1h1l-1 2c-1 2-1 3-2 5l-9 12c-1 0-1-1-1-2s2-3 3-3v-1-1l-1-1 1-1c2-1 3-4 3-7h2v-1h1z" class="R"></path><path d="M396 406h2v-1h1c0 2-2 3-2 5 0 3-2 5-4 7v-1-1l-1-1 1-1c2-1 3-4 3-7z" class="D"></path><path d="M339 411v-1c2 3 2 7 6 7h0v1c3 7 8 11 12 17 2 5 8 8 12 11l2-1c1 1 3 3 5 4h1v1c1 1 2 1 3 1h-2-1 0c-1 0-1 0-1-1h-1-1c-1-1-1-1-2-1 1 1 2 1 3 2h1 0c0 1 1 1 1 1l1 1v1c-2-1-3-1-4-2h-2c-1 0 0-1-1-1h-1l-1 1v1l-1-1c-1 0-2 0-3-1h-2c-1 0-2-1-2-1l-1-1 1 2v1c-1-1-2-2-3-2h-1c0-1-1-2-2-2l-5-4c0-1-2-2-2-2v-2c-2-3-5-3-6-7v-1h0v-1c1 1 1 2 2 3 0 1 5 6 6 6-1-2-4-5-5-7l-2-2 1-1h0c1 2 2 2 2 3l5 5v-1l-3-3h0c-2-2-3-3-3-5v-1c0 1 1 1 1 1 0 1 1 1 1 2h0l2 2h0c-1-2-3-4-4-7-1-1-2-3-2-5-1-1-1-2-1-3-1-3-3-5-3-7z" class="Q"></path><path d="M371 445c1 1 3 3 5 4h1v1c-3-1-5-2-8-4l2-1z" class="D"></path><path d="M350 440c-1-2-4-5-5-7l-2-2 1-1h0c1 2 2 2 2 3l5 5c1 2 3 4 5 6h1l1 1c0 1 0 0 1 1v1c2 1 5 1 5 3h-2c-2-1-6-4-7-6-1 0-1-1-2-2l-1-1c-1-1-1-1-2-1z" class="i"></path><path d="M339 411v-1c2 3 2 7 6 7h0v1 2c1 2 2 3 3 5 2 3 4 5 6 8l1 1v1c2 2 3 3 4 5 1 1 2 2 3 2 2 2 4 4 7 6v1c-1 0-2-1-3-2s-3-2-5-3-5-4-6-6c0-1-1-2-2-3 0-1 0-1-1-1-1-3-3-5-4-7-2-2-3-5-4-6l1 4c2 3 5 7 7 10h0c-1 0-2-1-3-2-1-2-3-4-4-7-1-1-2-3-2-5-1-1-1-2-1-3-1-3-3-5-3-7z" class="c"></path><defs><linearGradient id="AO" x1="462.838" y1="622.653" x2="465.106" y2="608.01" xlink:href="#B"><stop offset="0" stop-color="#a5a4a5"></stop><stop offset="1" stop-color="#d3d2d4"></stop></linearGradient></defs><path fill="url(#AO)" d="M424 596c2 6 5 9 9 13 3 3 7 5 10 7l1 1h2 0 1 1 0l3 1h2c1-1 2-1 3-1 4 2 8 3 12 4 7 1 14 1 21 1 7-1 13-2 20-2v1h-1l-1 2c-2 1-5 2-7 2-2 1-5 0-8 0-1 0-2-1-3 0 2 1 6 1 8 1 1 1 2 1 3 1h0-2c-5 0-9-1-13-2-7-1-14-2-21-2-2 1-3 2-4 3h-1v-1h-2c-1 0-2-1-3-1-2 0-4-1-6 0h0c0 1-1 1-2 1-7-2-13-5-17-12l-4-6-1-3h-2c-1-2-1-4-2-6l1-1v1h1v-1l2-1z"></path><path d="M503 622c2-1 3-1 5-1l-1 2-1-1-2 1-1-1z" class="f"></path><path d="M499 623c1-1 3-1 4-1l1 1c-1 0-2 1-3 1-1-1-1-1-2-1z" class="O"></path><path d="M497 624h-5c2-1 5-1 7-1 1 0 1 0 2 1h-4z" class="h"></path><path d="M504 623l2-1 1 1c-2 1-5 2-7 2v-1c-1 1-2 0-3 0h4c1 0 2-1 3-1z" class="l"></path><path d="M446 617h0 1 1 0l3 1h2c1-1 2-1 3-1 4 2 8 3 12 4h-1-1c-2 0-2 0-4-1 0 0-1 1-2 0-1 0-3-2-4-2h-4c-1 1-5 0-6-1z" class="B"></path><path d="M456 622l8 1c-2 1-3 2-4 3h-1v-1h-2c-1 0-2-1-3-1l2-1v-1z" class="f"></path><path d="M420 598l1-1v1h1v-1l3 8c3 5 9 10 14 12h1c-1 1-1 1-3 1h1c-1-1-1-1-2-1h-1s-1 0-1-1l-1-1c-1 0 0 1-1 0l-1-1c-1-1-1-1-2-1l-4-6-1-3h-2c-1-2-1-4-2-6z" class="Y"></path><path d="M420 598l1-1v1h1l2 6h-2c-1-2-1-4-2-6z" class="g"></path><defs><linearGradient id="AP" x1="451.043" y1="615.656" x2="435.892" y2="623.461" xlink:href="#B"><stop offset="0" stop-color="#1a181b"></stop><stop offset="1" stop-color="#474848"></stop></linearGradient></defs><path fill="url(#AP)" d="M429 613c1 0 1 0 2 1l1 1c1 1 0 0 1 0l1 1c0 1 1 1 1 1h1c1 0 1 0 2 1h-1c2 0 2 0 3-1 5 2 10 4 16 5v1l-2 1c-2 0-4-1-6 0h0c0 1-1 1-2 1-7-2-13-5-17-12z"></path><defs><linearGradient id="AQ" x1="411.956" y1="383.767" x2="371.866" y2="360.186" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#AQ)" d="M393 326c0-1 2-3 3-5 3-4 6-9 11-12-1 4-4 8-4 12s-3 7-5 10l-3 6c2-1 3-2 5-2v1c0 3-5 7-6 10-2 3-3 7-4 10-2 12 1 23 2 34l1 23-1 1 1 1v1 1c-1 0-3 2-3 3s0 2 1 2c-4 4-9 8-14 10-1 0-1-1-1-2 3-1 5-2 7-5 2-1 4-3 4-5 2-4 2-8 2-11 1-8 1-16 0-24l-3-24c1-1 1-3 1-4l1-11v-6h0c0-2 0-4 1-5 0-4 1-7 1-11 1 1 1 2 2 2h1z"></path><path d="M388 340c0-2 0-4 1-5 0-4 1-7 1-11 1 1 1 2 2 2h1l-1 2h0c-1 0-1 0-1 1-1 2-1 3-1 4 0 2 0 3-1 4v3l1-1h0v2c-1 0-1 0-1 1h0v1c0 1-1 1-1 2v1-6h0z" class="K"></path><path d="M368 569c12-2 25-4 35 4 3 2 6 4 8 7l1-1 1 1c1 1 1 1 1 2 1 1 2 2 2 3 3 6 3 14 1 20s-7 13-13 16-14 4-20 2c-4-1-9-5-12-8-4-6-7-13-13-17-3-3-8-4-12-6l-7-1h-6l-1-1c-3 1-7 1-11 3v-1c5-3 10-7 16-10 5-3 11-6 17-9l13-4z"></path><path d="M376 599h1c1 1 1 2 0 3v1c-1-1-2-1-2-1v-1l1-2z" class="b"></path><path d="M408 578c2 2 3 5 3 8-1 0-1 0-1-1v-1h-1c0-2 0-2-1-4v-1-1z" class="J"></path><path d="M408 579c-1-1-1-1-2-1 1 2 0 4 0 6l-1-7c-1-1-1-1-1-2 2 0 3 2 4 3v1z" class="C"></path><path d="M398 607c1 0 5 0 6 1v1c-1 0-2 1-2 1h-4v-3z" class="g"></path><path d="M391 572h2c1 0 2 1 2 2s0 1-1 2l-1 1c-2-1-2-2-3-4l1-1z" class="C"></path><path d="M392 583c0 1 2 7 1 7-2-1-4-2-5-4h0c1-1 2-1 3-2h0l1-1z" class="B"></path><path d="M333 590c5 0 10 0 15 1l-1 1-7-1h-6l-1-1z" class="K"></path><path d="M390 579c1 1 1 3 2 4l-1 1h0c-1 1-2 1-3 2h0l-1-1c0-2 0-2 1-3v-2l1 1 1-2z" class="E"></path><path d="M388 580h0c-3-5-10-6-15-7h1c6 0 12 2 16 6l-1 2-1-1z" class="P"></path><path d="M367 577c-7-2-12-1-19 2-1 1-2 2-3 2 1-1 2-1 3-2 6-4 12-6 19-4h-1l1 1h-1 0l1 1z" class="B"></path><path d="M367 575c3 1 7 3 9 7v1c0 1-1 1-2 1s-4-2-5-3l7 3c-3-4-5-6-9-7l-1-1h0 1l-1-1h1zm24 41c-2 0-4-1-6-4 0-1 0-1 1-2 0-1 1-1 1-1 1 0 2 2 2 3 3 3 6 5 10 5h0 0c-3 1-5 0-8-1z" class="k"></path><path d="M408 580c1 2 1 2 1 4h1v1c0 1 0 1 1 1 0 1-1 1-1 2-1 1-2 3-4 3-1 0-2 0-3-1 0-3 2-3 4-6 0-1 1-2 1-4z" class="b"></path><path d="M389 620c-1 0-2-1-3-1-3-2-7-4-8-7 0-1 1-2 1-2s1-1 1 0c1 0 3 3 4 3 1 2 4 3 6 4h1l-1 1-6-2c1 1 6 4 6 4h-1z" class="B"></path><defs><linearGradient id="AR" x1="355.521" y1="584.663" x2="365.406" y2="578.44" xlink:href="#B"><stop offset="0" stop-color="#8a8c8c"></stop><stop offset="1" stop-color="#bab9ba"></stop></linearGradient></defs><path fill="url(#AR)" d="M358 578c2 1 5 1 7 2 2 0 4 1 4 3 0 1 0 1-1 2-2 0-5-2-7-4-5-1-9 0-14 1 3-3 8-3 11-4z"></path><path d="M391 616c3 1 5 2 8 1h0 0c1 0 4-1 5 0-1 1-1 1-2 1l1 1c-3 2-5 2-8 3-2 0-4 0-6-1v-1h1l-6-4 6 2 1-1v-1z" class="O"></path><path d="M399 617c1 0 4-1 5 0-1 1-1 1-2 1l1 1c-3 2-5 2-8 3l-1-1c1-1 7-2 8-3l-1-1h-2 0z" class="Q"></path><path d="M411 580l1-1 1 1c1 1 1 1 1 2 1 1 2 2 2 3 3 6 3 14 1 20s-7 13-13 16-14 4-20 2c-4-1-9-5-12-8-4-6-7-13-13-17-3-3-8-4-12-6l1-1c8 1 13 6 18 12 0-3 0-6 3-9 0-1 1-1 1-1 1 0 2 0 2 1 0 2 0 2-1 3s-2 2-2 4c-1 3 2 8 4 11 4 4 10 7 16 9 2 1 4 1 6 1 3-1 5-1 8-3 2-1 4-3 5-4 7-8 7-16 7-26-1-3-2-6-4-9z" class="b"></path><path d="M411 580l1-1 1 1c1 1 1 1 1 2 1 1 2 2 2 3v1h0l-1-1v-1c-1-1-1-1-1-2-1 1 0 1 0 2h0c0 1 0 2 1 2v1 2h0c-1-3-2-6-4-9z" class="K"></path><path d="M120 113c1-4 1-9 1-14l-1-9c0-1-1-4 0-5h1 0 0l1 1c4 1 10 0 15 0h31 108 111 35c5 0 11 1 15 0h42 85 19 7 3v37 70c0 16 1 33-2 49-1 8-5 16-9 22l-5 5c-3 3-7 6-11 9-8 5-18 7-28 7-7-1-12-3-18-5l-4-2-6-4v1c-4-3-7-7-8-12-3-8-2-16 2-23 3-8 11-13 19-16 7-2 15-1 21 3 8 4 11 11 14 19 0-6 1-11 1-17-1-9-3-18-6-27l2-2-1-3h-2c-5-9-10-18-17-25-5-5-11-10-17-14-12-7-28-12-42-14-17-2-35-2-52-2v16 260 74c0 17-1 35 2 52s9 34 21 47c22 21 56 20 84 20l29-1c1 0 1 0 1 1-1 0-1-1-2-1v1c1 1 0 3 0 4l-1 1v22c-3 1-7 1-10 1h-23-209-52-14-4-8-12l-49-1-1-1c1-5 0-11 0-17l6 1v12h371v-15h-28-21c-6-1-14-1-21-2-15-2-29-9-41-20-6-7-12-15-16-24-2-4-3-8-4-12-2-6-4-12-5-18-1-7-1-15-1-23l-1-33 1-79V235v-70l-1-32c7 1 14 0 21 0 16 1 33 1 48 4 5 1 10 2 15 4 6 2 12 4 18 7 5 3 11 6 15 10 7 6 14 12 18 19 2 3 4 6 5 9 6 12 10 27 11 41 1 5 1 10 0 15-1 8-6 19-11 25-1 1-2 3-4 4-1 1-4 0-5 1 2-4 4-7 5-11 1-7 1-16-3-22-3-4-7-7-12-8-7-1-14 0-19 4-6 4-9 11-10 17 0 5 1 11 5 15 5 7 14 11 22 12 13 1 25-2 35-10 7-6 13-16 15-24 3-9 3-17 3-26v-23-68-23c0-4-1-9 0-13v-1l-460 1c0 1 0 4 1 6v10c0 8-2 16-3 24-1 6-3 11-4 17l-7 22c-3 10-7 20-8 30s0 24 4 33c5 14 18 29 32 35 12 4 26 7 38 2 6-3 12-8 14-14 2-5 2-12 0-17-2-6-8-10-14-13-6-2-12-2-18 1-5 3-10 8-12 13-2 7-1 16 2 23-1-1-3-1-4-2-3-1-6-11-7-14-6-16-2-38 5-53 6-11 13-22 23-31 8-6 16-11 25-15 4-2 7-4 10-5l16-3c28-7 58-9 87-9h13v75l-7-1 1-40h-1v-24h-1c-8-1-16 0-24 0-21 0-42 2-63 7-18 4-35 11-51 23-4 4-8 8-11 14-10 14-17 30-18 48l1 1c4-8 10-13 19-15 8-2 17 0 24 4 6 4 11 10 13 17 1 5 1 10 0 15v2h-2l-2 4-1 1c-1 2-3 4-5 6-10 8-21 11-34 10-14-2-29-9-39-20-3-3-5-7-8-10h0l-5-8c-6-15-8-34-4-50 2-8 5-15 8-23 3-12 7-23 9-35 1-5 2-10 2-15v-4l-1-1z" class="e"></path><path d="M120 113c1-4 1-9 1-14l-1-9c0-1-1-4 0-5h1 0 0l1 1c-1 1 0 3 0 4v7 15c0 2 0 4-1 6v-4l-1-1z" class="V"></path><path d="M146 235c4-8 10-13 19-15 8-2 17 0 24 4 6 4 11 10 13 17 1 5 1 10 0 15v2h-2c2-8 2-16-2-22-4-8-10-12-18-15-7-2-16 0-22 3-7 4-12 11-15 19v-5-4h1v2h0v1l2-2zm407-33l2-2c5 16 8 37 2 53 0-8-2-14-7-20-4-5-11-8-17-8-8-1-16 2-22 7s-9 13-10 20c0 9 3 16 9 22v1c-4-3-7-7-8-12-3-8-2-16 2-23 3-8 11-13 19-16 7-2 15-1 21 3 8 4 11 11 14 19 0-6 1-11 1-17-1-9-3-18-6-27z"></path><path d="M422 182v-43c39 0 84-1 113 28 8 9 15 19 19 30h-2c-5-9-10-18-17-25-5-5-11-10-17-14-12-7-28-12-42-14-17-2-35-2-52-2v16-9l-1-1v24c0 3 0 7-1 10z" class="Y"></path><path d="M423 148v-5-1l1 1v6l-1-1z" class="Z"></path><path d="M143 238v-3c1-24 11-48 29-65 17-16 42-24 65-27l21-3c18-1 37-2 56-2v28h-1v-24h-1c-8-1-16 0-24 0-21 0-42 2-63 7-18 4-35 11-51 23-4 4-8 8-11 14-10 14-17 30-18 48l1 1-2 2v-1h0v-2h-1v4z" class="Y"></path><path d="M423 148l1 1v9 260 74c0 17-1 35 2 52s9 34 21 47c22 21 56 20 84 20l29-1c1 0 1 0 1 1-1 0-1-1-2-1v1c1 1 0 3 0 4l-1 1v-5l-34 2c-27 0-58-1-78-22-12-12-19-28-21-44-4-21-3-43-3-64v-69-232c1-3 1-7 1-10v-24z"></path><path d="M264 196c1 0 3 0 4 1 5 2 10 7 14 10l3 1h1c3 1 9 0 13-1h3c3 0 7 0 10-1h1l7 1v1h0 1c3 1 6 1 8 2 3 1 5 1 7 3h-3c0 1 1 2 2 3 2 2 3 3 5 4v-1h1c1 0 1 0 2 1 0 0 0 1-1 1v1c-1 2-2 2-2 3s1 1 1 1h0l2 2 2-2v-1h-1 0l1-2v-1h0c1-1 2-1 2-1 1 0 2 1 2 2l3 3 1 1-1 2h0c1 2 3 3 4 4v1c1 1 1 1 1 2l-3-3v2c1 0 0 0 1 1v2h1c0 1 1 1 1 2h0c-1 0-2 1-2 1h0v1l-2 1c-2 0-5 0-7 1-2 0-5 1-7 3s-3 5-3 8 1 6 1 8l1 1c0 2 1 5 0 7l2 1c-1 1-2 3-3 4-2 2-3 3-5 4h-2v3l-3 3h2c2 0 2 0 4 1l2 1 1-1h2 1 0l1 1v1c1-1 1 0 1-1v-2c1 0 2 1 3 2l5 4c-1 1-2 1-2 1h0c2 4 5 7 6 11l3 6 5 11-1 1h-1l1 1c0 2 1 4 1 6 1 7 2 16 1 23-1-4 0-10-3-14 1 6 1 11 1 16-1 2-1 4-1 7-1 1-1 3-2 4-1 3-2 5-4 8-2 0-2 0-3 2-1-1-1 0-1-1l-1 2-3 5 1 1c-2 3-5 6-7 10-6 13-5 29 0 42 3 11 9 21 19 26 3 2 6 2 9 2v1c1 2 4 1 6 1h5c-1 1-3 2-4 4h0 2c3 0 9 0 11 2l1 1h-3-1 0 0c0 1 1 1 1 2 0-1-1 0-1-1l-1 1 1 1 2-1 1 1h0c0 1 1 2 2 2l-1 1h-1c0 1 1 1 2 2-1 0-2-1-3-1s-2 1-2 1h-1v1c-2 0-3 0-5-1-1-1-2-1-3-1h0c0 1 0 1-1 1-2-1-5-1-7-3l-2 2c-3-1-5-3-8-4-1 0-3-2-4-2l-1-1-1-1h-1l-1-1s0-1-1-1l-4-4c0-1-1-1-1-1s1 1 0 2-2-1-4 1l3 2c1 0 1 1 2 1 0 1 1 1 1 2 1 0 0-1 1 0l2 2c1 0 2 1 3 2l1 1c1 0 3 1 3 1 1 1 3 2 3 2h1l1 2c0 1 1 2 2 3l3 3v2c-2 0-5-1-6-1-1-2-3-3-4-3h-1c-8-4-18-9-25-15-7-7-13-15-19-23l-1 1c-1-2-3-5-4-7v-2c-1-4-4-6-5-10v-2-1c-2-2-2-6-3-8l-2-1h0l-1-1v1l-2-7h0l-1 2 3 12h-1l-2-3-1 1-1-7-2-4v-3l-1-3-1 1-1-3h-1v1c-1-3-1-6-1-9v-9-3l-1 3h-1c0-3 1-6 1-8v-3c-1 4-2 8-2 12 0 1-1 3-1 4h0c0 2-1 2-1 4v1h0v9c1 1 1 2 1 3-1 1 0 2-1 3v5l1-1v4h-3c-1 1-1 2-1 3v1l-1-1-1 1v3s-1 0-1 1 1 1 1 1v2 5l-8-2-1 1-2-1h-2 0-14v-1c-9 1-18 2-25 7-1-1-1-2-1-3v-1c-2 1-3 1-4 2s-2 1-2 2l-3 1-1-1c-1 0-1 0-1-1h0-1l-4 1v1h-2l-3 3c0 1 0 1-1 2s-2 1-2 2c-1 1-1 1 0 2l-1 1c-1 1-2 1-2 2h0l-1 3h1l-1 2c0 1 1 1 1 2h0c-3 3-5 6-7 9-1 2-2 3-3 5-2 4-4 7-6 10-1 2-1 5-2 7s-2 4-2 6c0 1-1 2-1 2l-1-1h0v-1h-1l-1 4v1c-1 1-1 1-1 2l-1 1c0-2 0-3-1-4h0v-1-6l-1-16c0-7 2-14 3-21l2-7c1-2 1-4 2-7 0-2 2-5 1-7l-3-1c0-2 1-3 1-5 1-1 1-1 1-2l-1-1v-1l2-6c1-1 1-2 2-4h0l9-28 1-4 9-21v-4c1 0 1-1 1-1 1-1 2-1 2-1 0-1 1-2 2-3-1-1-1-1-1-2l-1-1c1-2 2-3 2-4 2-4 5-8 8-11 1-3 5-5 5-8h0l-1-1c1-1 2-2 3-2 4-6 10-10 16-15 3-2 5-4 7-6 4-3 8-6 11-10h0c1 0 4-3 4-4v-1-6c-1-2-1-5 0-8 0-2 1-5 2-7 1-5 2-11 5-15l3-3c1-2 2-3 3-5 2 0 2-1 3-2v1l1-1c1-3 5-6 7-8l1-1 1-1 2-1c0-1 0-1 1-2h0c2-1 3-1 5-2h3v1h2l1 1h2v1c1 0 1-1 3 0v-1l-4-4-9-10c-5-6-10-14-17-17 0-1-2-1-2-2z" class="X"></path><path d="M227 378h0l1 1h-1v-1z" class="L"></path><path d="M243 408h1v1h-1v-1z" class="e"></path><path d="M228 403l2-2c0 2 0 2-1 4l-1-2z" class="J"></path><path d="M331 455l1 3v1l-1-1h0v-3z" class="K"></path><path d="M349 371v-3c0-1 1-1 2-1l-2 4z" class="b"></path><path d="M339 324h1v4h-1v-4z" class="Z"></path><path d="M372 471v1h0l1 1v2c-1-1-1-1-2-1l-1-1v-1s1 0 1-1h1z" class="L"></path><path d="M301 351h2 0 1l-1 2-1 2v-1-1l-1-1v-1z" class="J"></path><path d="M191 429v-1h0c0-1 0-2 1-4 0-1 1-1 2-1-1 2-1 4-3 6z" class="k"></path><path d="M356 469c1 0 1 1 2 1l2 2h1v1l-2-1-2-1h-1v-2z" class="g"></path><path d="M230 345h2c-1 2-2 4-3 5h0l1-2-1 1c-1-1 0-2 1-3v-1z" class="C"></path><path d="M358 328h0c0 1 0 1 1 2v1c0 1 0 2-1 3h-1v-1c0-1 0-3 1-4v-1h0z" class="e"></path><path d="M227 414h1 0c0 1-1 2-1 3-2 1-3 2-4 3h0v-1l1-1c2-1 2-3 3-4z" class="G"></path><path d="M198 405c1-1 1-3 2-4l1 1 1 2c-1 0-2 1-2 2h0c-1 0-1-1-2-1z" class="C"></path><path d="M310 426h3v3h1l-1 1v1 1l1 1h-1 0v-1s0-1-1-1v-1l-2-4z" class="J"></path><path d="M187 424v-2l1-1h0c1 1 1 1 0 2 0 1 0 1 1 2h1c-2 1-2 3-3 4l-1-1c1-1 2-3 1-4z" class="L"></path><path d="M330 324v1c0 2 0 5-1 7h0c0-3 0-6 1-8z" class="S"></path><path d="M332 341h0 0c0 2 0 5-2 6h-1l3-6z" class="Y"></path><path d="M304 364l1 1c0 1 0 1 1 2-1 1-2 1-3 2l-1 1v-1l1-1v-2l1-2z" class="b"></path><path d="M198 405c1 0 1 1 2 1h0c0-1 1-2 2-2l-1 5h0c0-1 0-1-1-2h-1-2l1-2h0z" class="I"></path><path d="M348 355h1v1c1 0 1 1 1 1l-1 2c-1-1-1-1-3-1h0v-1c1-2 1-1 2-2z" class="g"></path><path d="M226 354h0 1c0 1 0 1 1 1l-2 3v1h-1v-1c-1-1 0-3 1-4z" class="J"></path><path d="M274 393h1c-1 3-1 6-2 8h-1c0-3 1-5 2-8z" class="l"></path><path d="M325 324h1 0c-1 3 0 6-2 9h-1v-1c1-1 2-3 2-5v-3z" class="a"></path><path d="M226 354v-2c1 0 2-1 3-2h0v3l-1 2c-1 0-1 0-1-1h-1 0z" class="P"></path><path d="M258 364c1 0 2 1 3 2h0v1l-1 1h0c-2 0-2-1-3-1v-2l1-1z" class="g"></path><path d="M340 357c1 1 1 2 2 3-1 1-2 2-4 3l-1-1v-1c1-2 2-3 3-4z" class="V"></path><path d="M242 388v-1-1c1-1 1-2 2-3h0 2v1h-2v5c-1 1-2 1-3 1 0 0 0-1 1-1v-1h0z" class="J"></path><path d="M299 329c1 0 1 0 1 1 1 1-2 5-3 7 0 0 0 1-1 1l-1 2v1h-1v-1c1 0 1-1 1-1v-1l2-2v-1l1-1c1-2 1-3 1-5z" class="C"></path><path d="M358 460c3 2 6 2 9 2v1h-2l-1 1h0-1c-1 0-2 0-2-1-2 0-3-1-3-3z" class="K"></path><path d="M357 338h0v5c0 4-1 6-3 9h0-1l2-4c1-3 1-7 2-10z" class="l"></path><path d="M280 323h3 0c0 3-1 5-1 7-1 0-1 0-2-1h0v-1c0-1 0-1 1-1h0l1 2c-1-1-1-2 0-3v-2c-2 0-1 0-2 2l-1 1 1-4z" class="K"></path><path d="M314 339h2 1c-1 2-1 2-2 3-1 2-2 3-3 4l-1 1h0v-1l2-2v-2-1c0-1 1-2 1-2z" class="J"></path><path d="M207 389h1v2 1h0v1c-1 1 0 1-1 1v-1h0l-2 3h-1c-1 1 0 1-1 1v-2c1-1 2-3 2-4h1c0-1 1-1 1-2z" class="L"></path><path d="M186 428l1 1c1-1 1-3 3-4-1 2-2 4-2 6-1 2-1 3-2 5h-1-1c0-1 1-2 1-2 0-2 1-4 1-6z" class="C"></path><path d="M229 380v1c0 2-3 4-4 6-1 0-1 1-1 1-1 1 0 2-1 3l-2 2s0 1-1 1h0v-1l1-1c1-1 1-1 1-2l-1 1-1-1c0-1 1-1 2-2 1 0 1 0 1-1l2-3h1v-1l1-1 2-2z" class="L"></path><path d="M239 349l1 1h0c0 2 0 3-1 4l1 1-3 3h-1v-1c0-1-1-1-1-2l2-3 2-3z" class="C"></path><path d="M325 451h1 2c1 1 1 1 1 2l1 1c1 0 1 0 1 1h0v3h0c-2-2-4-3-5-6l-1-1z" class="L"></path><path d="M321 367c-1-1 0-1-1-2-3 1-3 5-5 7h0v-1c0-1 1-2 1-3l2-3c2-1 2-4 4-5h2c-1 2-3 5-3 7z" class="b"></path><path d="M234 346c0 2 0 4-1 5l-2 2v4l1 1-1 1h-1 0c1-2 1-4 1-6-2 2-3 4-5 6v-1l2-3 1-2 5-7z" class="I"></path><path d="M202 413h1l1-1 1 1c0 1-1 1-1 2l-1 1h0l-2 2v1h1c0-1 1-2 2-3l1-2 1 1v1c-1 1-1 2-1 2l-3 3c-1 0-2-1-2-2 0-2 2-3 3-5v-1h-1z" class="P"></path><path d="M343 352l3-3c-1 4-2 8-4 11-1-1-1-2-2-3 0-1 1-2 1-2h0l2-3z" class="h"></path><path d="M304 315v3l-1 6c0-1-1-3 0-4h0 0c-1 0-2 1-2 2h-1v1c-1 2-2 3-4 4l1-1c1-1 1-2 2-3 0-1 1-2 2-3l-1-1c1-1 1-2 1-3 1 0 2 0 3-1z" class="b"></path><path d="M304 315v3c-1 0-2 1-3 2l-1-1c1-1 1-2 1-3 1 0 2 0 3-1z" class="B"></path><path d="M299 314h2c0 1-1 4-1 5l1 1c-1 1-2 2-2 3-1 1-1 2-2 3l-1 1c0 1-1 2-1 3l-1-1c1-1 1-2 1-3v-1c1 0 1 0 1-1l1-1h1c1-2 1-2 1-4-1 0-1 0-2-1l2-4z" class="E"></path><path d="M232 345c1-1 3-3 4-5 0 0 1-1 1-2l1 1c0 1-1 1-1 2v1c-1 0-1 1-1 2l-2 2-5 7v-3c1-1 2-3 3-5z" class="F"></path><path d="M224 432l2-2v-3c0-1 0-1 1-1h2 0c1-1 1 0 1-1h0 2v1c-1 1-1 1-3 2h2v2c-1 1-1 1 0 1h1 1c-1 0-2 1-3 1-1-1 0-1 0-2v-1h-1l-1 1h-1c-1 0-1 1-2 2h-1z" class="C"></path><path d="M185 417l5-14v3h1 0c0 2-1 3-1 4-1 2-2 4-2 5-1 1-1 1-1 2-1 0-1 1-1 2l-1-2zm-2 6c1 0 1 0 2 1 0 1 0 1-1 1v1c-1 0-1 0-1 1-1 1-1 1 0 2h0l1 1c0-1 1-1 1-3v-1-1h1l-3 9v-1h-1v2l-1-1-1-1 3-10z" class="L"></path><path d="M335 461h3v-3l3 3 2 2s1 1 0 2-2-1-4 1c-1-2-3-3-4-5z" class="b"></path><path d="M348 355v-1c-1 0 2-7 2-8h1v1c0 1-1 2-1 4h2c0 3-1 6-2 8-1 1-1 2-1 2-1 2-2 3-3 4l-1-1c2-1 3-4 4-5l1-2s0-1-1-1v-1h-1z" class="K"></path><path d="M337 360l-1-1c2-4 4-7 6-10 0 0 1 2 1 3l-2 3h0s-1 1-1 2c-1 1-2 2-3 4v-1z" class="O"></path><path d="M337 360v-1c2-2 2-3 4-4 0 0-1 1-1 2-1 1-2 2-3 4v-1z" class="Y"></path><path d="M249 358h1v1c1 1 1 2 2 3h1c0-1 1-1 1-2v-1h1c0 2-1 3-1 4l-1 1s-1 1-1 2 0 1-1 1h-1l1-1c-1-1-1-2-2-2h-1-1 0c0-1 1-2 1-3s0-1 1-1v-2z" class="L"></path><path d="M236 410v-1c1 1 1 3 1 4 1 0 2-1 2-1 1-1 0-1 1-1 0-1 1-1 1-1v1l-3 3c-1 1-2 1-3 1l-1-1s-1 1-2 1h-1-1v-1l1-1h0c1-1 1-2 3-2l2-1z" class="C"></path><path d="M281 375h0c0-2 0-3 1-5v-1c1-1 1-2 1-2 0-1 0-1 1-2v-1l1 1-1 1h0c0 1-1 2-1 3v1h0l-1 1v4h1c0-1 1-1 2-2 0 0 1-1 1-2 0 1 0 3-1 3-1 3-4 6-3 9l-1 3h-1c0-3 1-6 1-8v-3z" class="b"></path><path d="M253 400c1 0 1 0 1 1l-1 1v1c-1 1-2 3-3 3s0 0-1-1c-1 1-2 2-2 3h0 0v-3h0c0 1 0 1-1 2h-1-1v-1l3-4v-1s1 0 1-1h1v2l1 1c2 0 2-1 2-2l1-1z" class="C"></path><path d="M324 360l1-2c1 1 1 5 1 6s0 3-1 3c-1 1-3 2-4 2v-2c0-2 2-5 3-7z"></path><path d="M272 356h1v1c0 1 1 2 1 2h1l2-2h0c0 2 1 6 0 8h-1v-2h0-2c-1-1-2-1-2-2h-2-1c0 1 0 1-1 1h0c0-1 0-1 1-2 0-1 1-2 2-3l1-1z" class="L"></path><path d="M218 353c1-1 2-1 2-2h0l1 1c1-1 1-2 1-3h0 1 1 0c-1 1-1 0-1 1h0l-2 2c-1 2-2 3-3 5-2 3-3 7-5 9 0 1-1 1-2 1 0 1-1 2-2 3 2-6 7-11 9-17h0z" class="C"></path><path d="M228 406c0 2 0 2-1 3 0 1-1 2-2 2h0v-1c1-1 1-1 1-2h1v-1c-1-1-1-1-1-2l-3 3c-1 0-1 1-1 1l-2 2h0c0-2 1-3 2-4 1-2 5-7 7-8v1 1h1l-2 2 1 2-1 1z" class="b"></path><path d="M228 406h-1c1-1 1-2 1-3l1 2-1 1z" class="P"></path><defs><linearGradient id="AS" x1="236.151" y1="332.707" x2="234.192" y2="345.242" xlink:href="#B"><stop offset="0" stop-color="#a5a5a3"></stop><stop offset="1" stop-color="#c7c5c8"></stop></linearGradient></defs><path fill="url(#AS)" d="M235 337c1-2 1-4 3-4 1 0 1 0 2 1l-3 4c0 1-1 2-1 2-1 2-3 4-4 5h-2l1-2c1-1 3-5 4-6z"></path><path d="M351 367l1-3h0c1 0 1 0 0 1v1c0 3-2 6-3 8l-1 2-3 5 1 1c-2 3-5 6-7 10v-2c0-1 1-3 2-4l5-8c1-2 2-4 3-7l2-4z" class="D"></path><path d="M289 318c2 1 2 1 3 3h0c1 0 1-1 2-2h1v1c1 0 1 1 1 0l1-2c1 1 1 1 2 1 0 2 0 2-1 4h-1l-1 1c0 1 0 1-1 1v1c0-1 0-1-1-1 0 1-1 2-2 2v-1s0-1 1-1v-1h1c0-1 0-1 1-1h1v-1l-1-1h-2c0 1 0 1-1 1-1 1-2 0-3 0l-1 1c0 1 0 1-1 2h0l-1 1c1-1 1-2 1-2 0-1 1-2 1-3l1-3z" class="C"></path><path d="M289 318c2 1 2 1 3 3h0c-2 1-3 1-4 0l1-3zm42 85l7-19c0-1 0-1 1-2l1 1-1 1c-1 2-2 4-3 7l-5 12v4l-4 4c0-2 0-5 1-7h0v-1h3z" class="B"></path><path d="M328 404v-1h3-1c0 1 0 3-1 4-1-1 0-2-1-3z" class="C"></path><path d="M317 440s0 1 1 1c1 1 1 1 2 1l1 1c0 1 2 3 2 5h-1v1h0c-1 0-1 1-2 1-2-2-4-6-6-9h1v-1 2h1c0-1 0-2 1-2z" class="P"></path><path d="M318 441c1 1 1 1 2 1l1 1c0 1 2 3 2 5h-1v-2c-1-1-1-2-2-3h-1v1 1h-1v-4z" class="L"></path><path d="M201 409h1c1 0 1-1 1-1l1-1c0-1 1-1 2-2v1c-1 1-1 2-2 3l-3 3v1c-1 1 0 1-1 2v1c0-1 1-2 2-3h1v1c-1 2-3 3-3 5l-1 2h-1c0 1 0 1-1 2l-2 1c0-1 1-1 1-2l-1 1-1-1c1-1 2-2 2-3l2-3c1 0 1-2 2-3v-3h0l1-1h0z" class="B"></path><path d="M203 413v1c-1 2-3 3-3 5l-1 2h-1c0 1 0 1-1 2l-2 1c0-1 1-1 1-2l-1 1-1-1c1-1 2-2 2-3l1 1c3-2 4-4 6-7z" class="D"></path><path d="M372 471v-1h1c1 1 4 0 5 1 2 1 4 3 6 3l2-1 1 1h0c0 1 1 2 2 2l-1 1h-1c0 1 1 1 2 2-1 0-2-1-3-1-2-1-4-3-6-3-2-1-5-2-8-3h0v-1z" class="C"></path><path d="M372 472l8 3c2 0 4 2 6 3-1 0-2 1-2 1h-1v1c-2 0-3 0-5-1-1-1-2-1-3-1l-1-1h0 1 1l-2-2h-1v-2l-1-1z" class="O"></path><path d="M377 475c3 1 5 3 6 4v1c-2 0-3 0-5-1 0-1 1-1 1-1-1-1-1-2-1-3h-1z" class="P"></path><path d="M373 473l4 2h1c0 1 0 2 1 3 0 0-1 0-1 1-1-1-2-1-3-1l-1-1h0 1 1l-2-2h-1v-2z" class="K"></path><path d="M206 415l1 1c-1 0-1 1-1 2v1 1h0c1-1 1-2 2-3l3-3v1l-1 1v2c0 1-1 2-1 2l-4 4-2 3c-1 1-1 1-3 1l1-3 1-2v-1h-2l-1-1 1-2c0 1 1 2 2 2l3-3s0-1 1-2v-1z" class="J"></path><path d="M200 419c0 1 1 2 2 2l1 1c-1 1-1 2-1 3h1l4-4c0-1 0-1 1-1h1l-4 4-2 3c-1 1-1 1-3 1l1-3 1-2v-1h-2l-1-1 1-2z" class="E"></path><path d="M234 346l2-2h0c2 1 3 2 4 2 0 1 0 2-1 2v1l-2 3-2 3-3 3-1-1v-4l2-2c1-1 1-3 1-5z"></path><path d="M237 349l2-1v1l-2 3c0-1 0-1-1-2l1-1z" class="I"></path><path d="M236 344c2 1 3 2 4 2 0 1 0 2-1 2l-2 1 1-1c0-1-1-1-1-1 0-1 0-2-1-3z" class="d"></path><path d="M353 304l3 6h-1c-1 0-1-1-1-2h0-1v2h0c0 1 0 2 1 2v2h0c-1 0-1-1-1-2l-1-1c-1 0-1 0-2 1 0 1 0 1 1 2v2c0 2 1 4 1 5l-1 1c-2-1 0-6-1-6l-2-2c0-1 0-2 1-3h1c-1 0-1-1-1-1h-1-1c-1 2 0 3-2 4v-1c-1 1-1 1-2 1-1 1-2 0-2 2h0l-1 1c-1 1-1 1-1 2h0-1v-2h0c1-1 0-2 1-3 1 0 1 0 2 1 0-1 1-1 1-1h0l1-1c1 0 1-1 2-1h1v-2c1-1 2-1 3 0 1-1 2-1 3-2l-1-2 2-2z" class="B"></path><path d="M239 385c0 1 0 2 1 3h2 0v1c-1 0-1 1-1 1v3 1c0 3-2 4-3 6 0 1 1 3 2 3l1-1h0 0v1c0 1-1 2-2 3l-1-1v-1h-5l1-2c1 0 1-1 1-1v-1l2-2v-2c1-1 1-2 2-3v-2h0c-1-1-1-2-2-2v-1l1-2 1-1z" class="b"></path><path d="M231 391c1-2 4-7 6-7l2 1-1 1-1 2-1 2-3 3-2 2h-1v-1l-1-1-6 6c-1 2-2 3-3 5l-2 2c-1 0 0 0-1 1l-2 2h0c0-1 1-2 1-3h0l3-3 4-5c1-1 0-1 1-1 0-1 2-2 2-3 2-1 3-2 5-3z" class="B"></path><path d="M231 391c1-2 4-7 6-7l2 1-1 1c-1 0-1 0-1 1-2 2-3 4-5 6l-1-1v-1z" class="C"></path><path d="M291 367c1-2 2-5 3-8 0 0 0-1 1-1h0l-3 9c0 1 0 3 1 3h0l2-5c1-1 2-4 3-4v1 1l1 1c-1 1-2 5-3 7l-1-1-2 5h-1v-1l-1 1v2c0-1 0-2-1-3v3-1c-1-1-1-2-2-3 0-1 1-2 1-3 0 0 1-2 2-3z" class="W"></path><path d="M289 370c1 2 1 3 1 4v3-1c-1-1-1-2-2-3 0-1 1-2 1-3z" class="Q"></path><path d="M295 370c0-3 1-6 3-8v1l1 1c-1 1-2 5-3 7l-1-1zm-29 51l-1 1h-3v-2-2 1 1c0 1-1 1 0 2h0l-2 2-2-1c0-2 0-3 1-4v-1c-1-1-1-1-1-2s1-2 1-2c1-1 2 0 4-1h0l1-1h2v1-1l1-1v-1h1c1 0 2 0 3-1 0 0 1-1 1-2h0v-1c0-1 0-1-1-2h0c1 0 1 1 2 1v-1l1 1v2h0c1-1 2-1 2-2v-7s1-1 1-2v9 1c-1 1-2 1-3 2-1-1 0-1-1-2 0 1-1 2-1 3 0 0 0 1-1 1h0c-1 0-1 0-2 1h-1c-1 1-1 2-1 3h-1l-1-1h0c-1 0-2 1-2 2v6c1 1 2 0 3 0z" class="P"></path><path d="M261 416h1v1c-1 1-1 4-2 5 0 1 0 0-1 0 0-2 0-4 2-6z" class="S"></path><path d="M305 425c1 0 2 0 4 1 0-1-1-2 0-2s1 1 1 2l2 4v1c1 0 1 1 1 1v1h0c1 2 1 2 2 3l2 4c-1 0-1 1-1 2h-1v-2 1h-1c-4-5-6-10-9-16z" class="B"></path><path d="M191 420h-3c0-2 1-4 2-5 1-4 3-8 4-11 1-1 1-2 1-3s1-1 1-2h0c0-1 1-2 1-3l1-1c1-2 1-5 3-6h0v1h0c0 1-1 2-1 3l-1 1v2c-1 0-1 1-1 1v1l-2 6c0 1 0 1-1 2v1c1 0 2-1 3-2l-1 2-1 2c-1 1-2 3-3 5l-1 3c-1 1-1 2-1 3z" class="E"></path><path d="M196 404c0 1 0 1-1 2v1c1 0 2-1 3-2l-1 2-1 2c-1 0-2 1-3 1l3-6zm104-40s0 1 1 1l-1 1c-1 2-2 4-2 8h-1v1 1l-1 1v4c-1 0 0 0 0 1v4h0l1-1c1 0 1 0 2 1v8s-1 2 0 3v2c1 0 1 1 2 1h0c-1 1-1 0-1 1v1c0 1 0 2 1 3 1 0 1 0 1 1v1l1-1s0-1-1-2c0-1 0-2-1-3v-3c0 1 1 1 1 2l1 3v1 4l-3-3c-2-3-2-8-2-12 0-2 0-5-1-7h0c-1 2-1 5-1 7v3h-1c-2-8 0-18 2-26l3-6z" class="C"></path><path d="M215 425c0 1 0 1 1 2h0c1 0 1 0 2-1 0-1 1-1 1-1 2 0 2 0 3-1l1-1c1-1 4-3 5-4h2c2 0 2 0 3 1v1 1c-1 1-1 1-2 1-2 1-3 2-4 2s-1 0-2-1c0 0 1-1 1-2l-1 1h0 0v1c-1 1-2 2-3 2h-1-1 0-1 0c-1 1-2 1-3 2v1 2l-2 2h0c-1-1 0-2-1-4v1h-2 0-1l1-1s0-1 1-1c1-1 2-1 2-1 1-1 1-1 1-2z" class="K"></path><path d="M227 425c-1 0-1-1-1-1l1-1v-1l2-2c1 0 2-1 3 0v1c0 1-1 2-1 2-2 1-3 2-4 2z" class="B"></path><path d="M199 421l1 1h2v1l-1 2-1 3h0c-1 2-3 2-5 3v1h-2c-1 1-1 1-2 1l-1-1h1v-3c2-2 2-4 3-6h1l1-1c0 1-1 1-1 2l2-1c1-1 1-1 1-2h1z" class="Q"></path><path d="M191 432c2-2 2-4 4-6v1c0 1-1 3 0 5h-2c-1 1-1 1-2 1l-1-1h1z" class="P"></path><path d="M195 427h1l3-4h2v2l-1 3h0c-1 2-3 2-5 3v1c-1-2 0-4 0-5z" class="V"></path><path d="M199 407h1c1 1 1 1 1 2l-1 1h0v3c-1 1-1 3-2 3l-2 3c0 1-1 2-2 3l-1-2h-2c0-1 0-2 1-3l1-3c1-2 2-4 3-5l1-2h2z"></path><path d="M193 414c2 0 2-2 4-2h0c-1 1-1 2-2 3l-2 2h-1l1-3z" class="Z"></path><path d="M193 417v1 1h3c1-2 1-2 2-3l-2 3c0 1-1 2-2 3l-1-2h-2c0-1 0-2 1-3h1z" class="I"></path><path d="M199 407h1c1 1 1 1 1 2l-1 1h0v3h-1l-2-1h0c-2 0-2 2-4 2 1-2 2-4 3-5l1-2h2z" class="h"></path><path d="M199 407h1c1 1 1 1 1 2l-1 1h0c-1 1-1 1-2 1h-1l2-4z" class="V"></path><path d="M303 417c-1-1-1-1 0-2v1c1-1 2-1 2-1 1 2 2 6 3 8l1-1c-1-1-1-2-2-3v-1-1c0-1 0-1-1-2h0v-2l-1-1v-2c0-1 0-2-1-3v-1-1c0-1 0-1-1-2 0 0 1-1 0-2v-1c0-2-1-4 0-5 0-1 0-2 1-3v2c0 2 0 2 1 3-1 0-1 0-1 1v3h0c0 1 1 4 1 4v2 1c1 1 1 2 1 3v1c1 1 1 2 1 3v1l1 4c1 0 1 1 1 1h1l1 1h0l1 1s1 1 1 2v1h-3c0-1 0-2-1-2s0 1 0 2c-2-1-3-1-4-1 0-2-2-6-2-8z" class="C"></path><path d="M246 336h0 0c1 1 0 1 1 3l1-1v2c-1 1 0 1 0 2v1h-1l-1-1s-1 0-1 1c0 0-2 4-2 5h0c-1 2-2 4-3 7l-1-1c1-1 1-2 1-4h0l-1-1v-1c1 0 1-1 1-2-1 0-2-1-4-2h0c0-1 0-2 1-2 3-1 3-3 5-5 0-1 0-1 1-1l1 1-2 2v2h1l3-5z" class="P"></path><path d="M246 336h0 0c1 1 0 1 1 3-1 1-1 2-2 2-1 1-1 1-2 1h-1-1 0l1-1h1l3-5z" class="B"></path><path d="M252 319h1c0 1 1 2 1 3v1c-2 2-3 3-4 5v1h0c-1 2-3 5-4 7l-3 5h-1v-2l2-2-1-1c-1 0-1 0-1 1-2 2-2 4-5 5v-1c0-1 1-1 1-2l3-6 11-14z" class="D"></path><path d="M241 333h1c2-1 4-3 6-4v2l-1 1c-1 2-2 3-3 5l-1-1c-1 0-1 0-1 1-2 2-2 4-5 5v-1c0-1 1-1 1-2l3-6z" class="E"></path><path d="M187 424c1 1 0 3-1 4 0 2-1 4-1 6 0 0-1 1-1 2s-1 1-1 2c-1 4-3 6-4 10v1c-1 3-7 22-6 24h1c-1 1-1 2-1 2l-1-1 1-8v-7c1-2 1-4 2-6h0c1-1 1-3 1-4v-4l1-2c2-1 2-3 4-5 0-1 0-2 1-3v-2h1v1l3-9 1-1z" class="D"></path><path d="M175 456l1-1-1 6h-1c0-2 0-3 1-5z" class="J"></path><path d="M182 435v-2h1v1l-1 4h-1c0-1 0-2 1-3zm-5 13s1 0 1 1l-2 6-1 1v-3c1-1 1-3 1-4l1-1z" class="e"></path><path d="M177 443c2-1 2-3 4-5h1c0 2-1 4-2 5 0 1 0 2-1 3l-1 3c0-1-1-1-1-1l-1 1v-4l1-2z" class="L"></path><path d="M177 448c0-1 1-2 1-3l1 1-1 3c0-1-1-1-1-1z" class="K"></path><path d="M286 371c0-2 1-5 1-6 1-1 1-2 2-2l-1 2v1l3 1c-1 1-2 3-2 3 0 1-1 2-1 3l-1 3-1-2c-1 11-2 21 0 31l-1 1-1-3h-1v1c-1-3-1-6-1-9v-9-3c-1-3 2-6 3-9 1 0 1-2 1-3z" class="V"></path><path d="M286 374l2-9v1l3 1c-1 1-2 3-2 3 0 1-1 2-1 3l-1 3-1-2z" class="k"></path><path d="M282 383c-1-3 2-6 3-9l-1 17c0-2 0-3-1-5h0c1-1 0-2 0-3 0 1 0 2-1 3v-3z" class="T"></path><path d="M282 386c1-1 1-2 1-3 0 1 1 2 0 3h0c1 2 1 3 1 5v12h-1v1c-1-3-1-6-1-9v-9z" class="a"></path><path d="M261 326l1-1c1 0 2 1 2 1 0 3-1 5-3 6v1h2-3l-1-1-1 1h0c-1 2-1 3-2 4s-1 2-1 3-1 1-1 1l-3 6c0-1 1-3 1-5v-1-1c1 0 1-1 1-1v-1h-1v-1h-1v2l-1 1v1h0c-1 0-1 0-2 1 0-1-1-1 0-2v-2l-1 1c-1-2 0-2-1-3h0 0c1-2 3-5 4-7h1l2-2v1s1 1 1 2v1c1 1 2 1 4 1h0c2-2 2-4 3-6z" class="J"></path><path d="M238 327h1v-1c1-1 3-2 4-4l2-2v1s-2 3-3 4c-2 2-4 5-6 7l-1 1v1c-1 1-1 1-2 3h2c-1 1-3 5-4 6l-1 1h0l-3 6-1 1h-1l-4 8c1-2 1-4 2-6v-1l1-1h0v-2h-1-1 0c0 1 0 2-1 3l-1-1h0c0 1-1 1-2 2h0v-3l1-1 1-3 1-1 1 1-1 2c1 0 1 0 2-1l7-9-1-1 2-4 3-3h0l1-2s1 0 1-1h2z" class="S"></path><path d="M233 337h2c-1 1-3 5-4 6l-1 1h0l-3 6-1 1h-1c1-2 2-3 3-5 2-3 3-7 5-9z" class="E"></path><path d="M230 338l1-3h1l-3 8c-2 3-4 7-6 10v-1l1-1h0v-2h-1-1 0c0 1 0 2-1 3l-1-1h0c0 1-1 1-2 2h0v-3l1-1 1-3 1-1 1 1-1 2c1 0 1 0 2-1l7-9z" class="J"></path><path d="M220 346l1-1 1 1-1 2-3 5h0v-3l1-1 1-3z" class="c"></path><defs><linearGradient id="AT" x1="243.833" y1="313.542" x2="247.577" y2="330.582" xlink:href="#B"><stop offset="0" stop-color="#6a6a67"></stop><stop offset="1" stop-color="#9b9a9d"></stop></linearGradient></defs><path fill="url(#AT)" d="M256 308l1 1-2 3 1 1v1l-2 2c-1 1-2 2-2 3l-11 14-3 6-1-1 3-4c-1-1-1-1-2-1-2 0-2 2-3 4h-2c1-2 1-2 2-3v-1l1-1c2-2 4-5 6-7 1-1 3-4 3-4v-1l-2 2c-1 2-3 3-4 4v1h-1c5-7 12-13 18-19z"></path><path d="M255 312l1 1v1l-2 2c-1 1-2 2-2 3l-11 14-3 6-1-1 3-4c1-1 3-4 3-6l12-16z"></path><path d="M249 411c1-1 2 0 3 1v1c1 1 0 1 0 2s1 1 1 2h2c0 1 2 1 3 1v1c1 0 0 0 0 1s0 2-1 3h0l-2 2c-1 0-1 0-2-1 0-1 1-2 1-2 1-1 1-2 1-2 0-1 1-1 1-1-1 1-2 1-2 2-2 1-2 2-3 2l-1 1-1 1c-1 0-1 0-1 1-1 0-1 0-2 1-1 0-1 0-2 1v-1l-1 1c0 1-1 1-1 1 0 1-1 0-2 0l-2-1c-1-1-1-2 0-3h1c1 0 1 1 2 1l1-1h1v1c0-1 1-1 1-1 1 0 1 0 1-1v-2-1-2l-1-1s1-1 2-1v-2c-1 1-2 1-3 2l-1 1h0-2v-1l2-1 1-1 1-1c1 0 1-1 2-1 1 1 0 3 0 4 1 0 2 0 2 1h1v-1c0-1-1-1-2-2v-2l2-2zm-31-61v3c-2 6-7 11-9 17-1 4-2 9-5 13h-1v-1c0-1 0-2-1-3 1-1 1-1 1-2v-1c-2 2-3 6-3 8-1 2-2 3-2 4v1h0-1c0-1 0-1 1-2s1-2 1-3 0-1 1-1v-5c1-1 1-3 3-3 1-1 1-1 1-2h0c-1 0-1 0-1 1h-2v1s-1 1-1 2v1s0 1-1 2v1 1h0l-1 1v1h0v2h-1v3 1l-1 1v2h0c-1 1-2 3-2 4l-1 1-1 3v1h-1c0-1 1-2 1-3l3-6c2-6 3-13 6-20h2l13-23 1 1 1-1z" class="C"></path><path d="M218 350v3c-2 6-7 11-9 17-1 4-2 9-5 13h-1v-1c2-3 5-9 5-12 1-3 3-6 4-9 1-1 1-1 0-3 2-2 3-5 5-7l1-1z" class="U"></path><path d="M315 293c0 1 1 1 0 2 0 1 0 2-1 2v1l-2 2h0 0c0 1-1 4-1 5l-1 6c0 3-1 6 0 8 0 2 0 4-1 6 0 1-1 1-1 2h-1v-2h0l-3 1c-1 0-1 1-2 1h0l1-3 1-6v-3-3c1-4 1-8 2-11h1l1-1 1-1 1-1v1l5-6z" class="B"></path><path d="M306 301h1l1-1 1-1c-3 6-3 11-4 17h0l-1 10c-1 0-1 1-2 1h0l1-3 1-6v-3-3c1-4 1-8 2-11z" class="Q"></path><path d="M305 316c1 1 1 2 2 3 1-2 0-2 2-3h0c0 3 1 5-1 7 0 2-1 3-1 4v-2h0l-3 1 1-10z" class="C"></path><path d="M315 293c0 1 1 1 0 2 0 1 0 2-1 2v1l-2 2h0 0c0 1-1 4-1 5l-1-1h0c0 1 0 1-1 2 0 2-1 4-1 6s0 3-2 5l1-7c0-3 0-5 1-7s2-3 2-4l5-6z" class="I"></path><path d="M312 300c0 1-1 4-1 5l-1-1h0c0 1 0 1-1 2 0-3 1-4 3-6z" class="U"></path><path d="M286 405c-2-10-1-20 0-31l1 2 1-3c1 1 1 2 2 3v1c-1 2-2 4-1 6 1 1 0 2 0 4 0 4 0 7 1 11l1 9c0 1 0 2 1 3l-1 2 3 12h-1l-2-3-1 1-1-7-2-4v-3l-1-3z" class="I"></path><path d="M287 406l1 4c0 1 0 1-1 1v-3-2z" class="H"></path><path d="M289 408h1l1-1c0 1 0 2 1 3l-1 2-2-4z" class="B"></path><path d="M288 410l3 11-1 1-1-7-2-4c1 0 1 0 1-1z" class="U"></path><path d="M289 383c1 1 0 2 0 4 0 4 0 7 1 11l1 9-1 1h-1v-5c-2-6-1-14 0-20z" class="D"></path><path d="M286 405c-2-10-1-20 0-31l1 2v17 13 2l-1-3z" class="U"></path><path d="M296 393c0-2 0-5 1-7h0c1 2 1 5 1 7 0 4 0 9 2 12l3 3v-4-1l-1-3c0-1-1-1-1-2v-1-1-1h0c-1-2-1-4 0-5 1 1 2 0 3 1v1c-1 1-1 2-1 3-1 1 0 3 0 5v1c1 1 0 2 0 2 1 1 1 1 1 2v1 1c1 1 1 2 1 3v2l1 1v2h0c1 1 1 1 1 2v1 1c1 1 1 2 2 3l-1 1c-1-2-2-6-3-8 0 0-1 0-2 1v-1c-1 1-1 1 0 2-1 1 0 5 1 7l3 8v1l-1-2v1h-1c-5-12-9-23-10-36h1v-3z" class="B"></path><path d="M296 393c0-2 0-5 1-7h0c1 2 1 5 1 7 0 4 0 9 2 12l2 10c-2-4-2-7-3-11s-2-7-3-11z" class="i"></path><path d="M311 286h1l1-1h1v1 1c-1 0-1 0-1 1h3 3l-1 1 1 1h0l2 2h0s-1 0-2 1h0-1c-1 0-1-1-2-1l-1 1-5 6v-1l-1 1-1 1-1 1h-1c-1 3-1 7-2 11v3c-1 1-2 1-3 1 0 1 0 2-1 3 0-1 1-4 1-5h-2v-2l1-4h-1v1c-1-1-1-1-1-2h-1l2-5-1-1c0-2 1-3 3-4v-1-1-1h0l3-3h-1c1-1 3-2 4-3 1 0 2-1 3-1l1-1z" class="d"></path><path d="M304 291h1l2 1c-2 1-3 2-4 3l-2-1 3-3z" class="O"></path><path d="M310 288c0 1 0 2-1 3h-1c-1 0-1 1-1 1l-2-1c2-1 4-2 5-3z" class="Z"></path><path d="M311 286h1l1-1h1v1 1c-1 0-1 0-1 1h-3c-1 1-3 2-5 3h-1-1c1-1 3-2 4-3 1 0 2-1 3-1l1-1z" class="E"></path><path d="M308 296h1v-2c1 0 2-1 3-1l1 1c-1 1-2 3-3 4l-1 1-1 1-1 1h-1c0-1 1-1 1-2 1-1 1-2 1-3z" class="Z"></path><path d="M316 288h3l-1 1 1 1h0l2 2h0s-1 0-2 1h0-1c-1 0-1-1-2-1l-1 1-5 6v-1c1-1 2-3 3-4 2-2 3-3 3-6z" class="D"></path><path d="M316 292c1-1 1-1 2-1s1 1 1 2h0-1c-1 0-1-1-2-1z" class="Z"></path><path d="M305 300c1-1 2-3 3-4 0 1 0 2-1 3 0 1-1 1-1 2-1 3-1 7-2 11v3c-1 1-2 1-3 1 0 1 0 2-1 3 0-1 1-4 1-5h-2v-2l1-4h-1v1c-1-1-1-1-1-2h-1l2-5 1-2v-1h1c0 1 0 1 1 2v-1l1-1c0-1 0-1 1-1 1 1-1 2 1 2z" class="I"></path><path d="M303 299c0 2 0 4-1 5l-1 8v2h-2v-2l1-4c1 0 2-7 2-8l1-1z" class="U"></path><path d="M299 312l2-2v2 2h-2v-2z" class="D"></path><path d="M300 300v-1h1c0 1 0 1 1 2v-1c0 1-1 8-2 8h-1v1c-1-1-1-1-1-2h-1l2-5 1-2z" class="Z"></path><path d="M300 300c0 2-1 6-1 8v1c-1-1-1-1-1-2h-1l2-5 1-2z" class="G"></path><path d="M305 300c1-1 2-3 3-4 0 1 0 2-1 3 0 1-1 1-1 2-1 3-1 7-2 11v3c-1 1-2 1-3 1l1-5c0-3 2-7 3-11z" class="N"></path><path d="M302 311c1 0 1-1 1-1l1 2v3c-1 1-2 1-3 1l1-5z" class="M"></path><path d="M307 432l-3-8c-1-2-2-6-1-7 0 2 2 6 2 8 3 6 5 11 9 16 2 3 4 7 6 9 1 0 1-1 2-1h0v-1h1c1 1 1 2 2 3l1 1c1 3 3 4 5 6l1 1h0c1 1 2 1 3 2 1 2 3 3 4 5l3 2c1 0 1 1 2 1 0 1 1 1 1 2 1 0 0-1 1 0l2 2c1 0 2 1 3 2l1 1c1 0 3 1 3 1 1 1 3 2 3 2h1l1 2c0 1 1 2 2 3l3 3v2c-2 0-5-1-6-1-1-2-3-3-4-3h-1l1-1-1-1h1 0c-4-3-9-5-14-8l2-1-7-4-5-5-8-8c-2-2-3-5-5-7 0-1-1-2-1-3-4-5-7-10-10-15z" class="c"></path><path d="M343 474c3 2 5 4 9 6 1 1 3 1 5 3h-2c-4-3-9-5-14-8l2-1z" class="F"></path><path d="M352 476c1 0 3 1 3 1 1 1 3 2 3 2h1l1 2c0 1 1 2 2 3h-1l-3-3c-2-1-4-2-7-3 1-1 1-1 1-2z" class="D"></path><path d="M322 448h1c1 1 1 2 2 3l1 1c1 3 3 4 5 6l1 1h0v1 2l2 2 3 3c1 1 1 1 0 1-4-2-10-8-12-12-2-2-3-4-5-6 1 0 1-1 2-1h0v-1z" class="J"></path><path d="M322 448h1c1 1 1 2 2 3l1 1c-1 0-2 0-2-1l-1 1c1 0 2 1 2 2v1 1c-2-2-3-4-5-6 1 0 1-1 2-1h0v-1z" class="B"></path><path d="M332 459c1 1 2 1 3 2 1 2 3 3 4 5l3 2c1 0 1 1 2 1 0 1 1 1 1 2 1 0 0-1 1 0l2 2c1 0 2 1 3 2l1 1c0 1 0 1-1 2l-10-7c-2-1-3-2-4-3 1 0 1 0 0-1l-3-3-2-2v-2-1z" class="F"></path><path d="M258 299l1 1-3 3h1c1 0 2-1 2-2v2 1h0 1v1 1l-3 3-1-1c-6 6-13 12-18 19h-2c0 1-1 1-1 1l-1 2h0l-3 3-2 4 1 1-7 9c-1 1-1 1-2 1l1-2-1-1-1 1c-1-2 0-3 1-5l-1-1c1-1 2-2 2-3l4-5c2-1 4-3 5-4 0-1-1-3-1-4l6-6 13-13c2 0 4-3 5-4l1 1 3-3z"></path><path d="M233 327c1 1 1 1 2 1l-1 2h0-2l1-3z" class="D"></path><path d="M232 330h2l-3 3h-1l2-3z" class="F"></path><path d="M221 341c1 0 1 0 2-1l1-1v1l-3 5-1 1c-1-2 0-3 1-5z" class="D"></path><path d="M230 333h1l-2 4 1 1-7 9c-1 1-1 1-2 1l1-2c2-5 5-9 8-13z" class="N"></path><path d="M231 328h1l-5 8c-1 1-2 3-3 4v-1l-1 1c-1 1-1 1-2 1l-1-1c1-1 2-2 2-3l4-5c2-1 4-3 5-4z" class="M"></path><defs><linearGradient id="AU" x1="231.961" y1="318.788" x2="240.223" y2="320.354" xlink:href="#B"><stop offset="0" stop-color="#757370"></stop><stop offset="1" stop-color="#848689"></stop></linearGradient></defs><path fill="url(#AU)" d="M254 301l1 1c-6 6-13 11-18 18-2 3-4 5-5 8h-1c0-1-1-3-1-4l6-6 13-13c2 0 4-3 5-4z"></path><defs><linearGradient id="AV" x1="240.44" y1="312.686" x2="241.611" y2="326.925" xlink:href="#B"><stop offset="0" stop-color="#76766f"></stop><stop offset="1" stop-color="#8f8e96"></stop></linearGradient></defs><path fill="url(#AV)" d="M259 301v2 1h0 1v1 1l-3 3-1-1c-6 6-13 12-18 19h-2c0 1-1 1-1 1-1 0-1 0-2-1 2-4 6-7 9-11 5-4 10-8 14-13h1c1 0 2-1 2-2z"></path><path d="M259 301v2 1h0 1v1 1l-3 3-1-1c-6 6-13 12-18 19h-2c3-4 6-7 9-10 4-5 9-9 12-14 1 0 2-1 2-2z" class="V"></path><path d="M259 304h1v1 1l-3 3-1-1 3-4z" class="c"></path><path d="M303 291h0 1l-3 3h0v1 1 1c-2 1-3 2-3 4l1 1-2 5h1c0 1 0 1 1 2v-1h1l-1 4v2l-2 4-1 2c0 1 0 0-1 0v-1h-1c-1 1-1 2-2 2h0c-1-2-1-2-3-3l-1 3c0 1-1 2-1 3 0 0 0 1-1 2 0 1-1 2-1 3l-2-1c0 2-1 4-2 5h-1l2-3c0-2 1-4 1-7h0-3v-2-1c0-3 2-8 4-11 1-1 2-4 4-5 2-2 6-6 7-8v-1c0-1 1-2 2-2 1-1 2-1 3-2h1 1 1z" class="i"></path><path d="M285 310c1-2 2-4 3-5h1l-2 6-2-1z" class="G"></path><path d="M285 310l2 1-2 4s-1-1-2-1l2-4z" class="N"></path><path d="M284 320c1 0 1-1 2-1v2l1 1h0v2s0 1-1 2c0 1-1 2-1 3l-2-1c1-3 0-5 1-8z" class="B"></path><defs><linearGradient id="AW" x1="283.838" y1="315.064" x2="280.925" y2="322.05" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#a9a9a8"></stop></linearGradient></defs><path fill="url(#AW)" d="M283 314c1 0 2 1 2 1 0 3-2 6-2 8h0 0-3v-2c1-3 2-5 3-7z"></path><defs><linearGradient id="AX" x1="290.476" y1="310.828" x2="285.463" y2="317.193" xlink:href="#B"><stop offset="0" stop-color="#858584"></stop><stop offset="1" stop-color="#a1a0a1"></stop></linearGradient></defs><path fill="url(#AX)" d="M284 320c2-3 3-7 5-11 1 1 1 1 1 2-1 4-2 7-3 11h0l-1-1v-2c-1 0-1 1-2 1z"></path><path d="M292 306l1-1 1 1v1l-1 2-2 6c-1 0-1 2-2 3l-1 3c0 1-1 2-1 3v-2c1-4 2-7 3-11 0-1 0-1-1-2l1-2 1-2 1 1z" class="Q"></path><path d="M291 305l1 1-1 2-1-1 1-2z" class="H"></path><path d="M290 307l1 1-1 3c0-1 0-1-1-2l1-2z" class="N"></path><path d="M303 291h0 1l-3 3h0c-1 1-2 1-3 2 0 2 0 3-1 4h0l-1 2c-1 2-2 3-2 4l-1-1-1 1-1-1 1-1v-2c1-1 3-4 4-6h-1v-1c0-1 1-2 2-2 1-1 2-1 3-2h1 1 1z" class="G"></path><path d="M295 299l3-3c0 2 0 3-1 4h0c-1 0-1 0-2-1z" class="Z"></path><path d="M295 299c1 1 1 1 2 1l-1 2c-1 2-2 3-2 4l-1-1c0-1 0-1 1-2 0-2 0-2 1-4z" class="h"></path><path d="M297 293c1-1 2-1 3-2v2c-1 1-2 2-4 3h-1v-1c0-1 1-2 2-2z" class="Y"></path><path d="M298 296c1-1 2-1 3-2v1 1 1c-2 1-3 2-3 4l1 1-2 5h1c0 1 0 1 1 2v-1h1l-1 4v2l-2 4-1 2c0 1 0 0-1 0v-1h-1c-1 1-1 2-2 2h0c-1-2-1-2-3-3 1-1 1-3 2-3l2-6 1-2v-1c0-1 1-2 2-4l1-2h0c1-1 1-2 1-4z" class="f"></path><path d="M298 301l1 1-2 5v-3c0-1 0-2 1-3z" class="I"></path><path d="M296 302c1 2-1 4-1 6v1h-2l1-2v-1c0-1 1-2 2-4z" class="S"></path><path d="M294 307v-1l1 2v1h-2l1-2z" class="a"></path><path d="M291 315h2v2l-1 4c-1-2-1-2-3-3 1-1 1-3 2-3z" class="F"></path><path d="M293 309h2c-1 2-1 3-1 5-1 1-1 2-1 3v-2h-2l2-6z" class="M"></path><defs><linearGradient id="AY" x1="295.956" y1="308.278" x2="296.973" y2="317.042" xlink:href="#B"><stop offset="0" stop-color="#868384"></stop><stop offset="1" stop-color="#9d9e9c"></stop></linearGradient></defs><path fill="url(#AY)" d="M298 307c0 1 0 1 1 2v-1h1l-1 4v2l-2 4-1 2c0 1 0 0-1 0v-1h-1c1-4 2-8 4-12z"></path><path d="M299 308h1l-1 4v2l-2 4-1 2h0l3-11v-1z" class="I"></path><path d="M327 286h2c2 0 2 0 4 1l-1 1v1c1 1 2 1 2 2l-1 1v1c-1 0-1 1-2 1l-2-1v1l-2-1c1 3 0 5 0 8 1 2 1 5 0 7v4l-1 1h0c0 1 0 1 1 2l1 1v2l-1 1-1-1h0v-2c0-1-1-1-1-2v-1l-2 1c0 1 1 0 1 1s-1 2-1 2c1 1 1 1 1 2h-1v1h-2v-1c0-2-1-2-2-3 0 1 0 1-1 1v1 1l-1 1-1-1h0c1-1 0-3 0-5h0c-1 1-1 2-1 3l-1 2-2-1h-1c0 1 0 1-1 1-1-2 0-5 0-8l1-6c0-1 1-4 1-5h0 0l2-2v-1c1 0 1-1 1-2 1-1 0-1 0-2l1-1c1 0 1 1 2 1h1 0c1-1 2-1 2-1h0l-2-2h0l-1-1 1-1h2l4-1 2-1z"></path><path d="M324 300l1-2v-1c1 1 0 2 1 4 1 1 1 4 1 6l-1 1v-4h0l-1 1c0-2 0-2-1-3v-2z" class="S"></path><path d="M317 299c0-1-1-2 0-3l3-3v8c0-1 0-1-1-2v2 1h-2v-3z" class="Z"></path><path d="M319 301l-1-1c0-1-1-2 0-3h1v2 2z" class="I"></path><path d="M321 301v-1c0-1 0-2 1-3v1s0 1 1 2h1v2c-2 3 0 6-2 8v-3l-1-1c1-1 1-2 1-3l-1-1v-1z" class="T"></path><path d="M319 299c1 1 1 1 1 2h1v1l1 1c0 1 0 2-1 3v4 3h-1-1l-1-2c1-2 0-6 1-9v-1-2z" class="a"></path><path d="M320 301h1v1l1 1c0 1 0 2-1 3v4 1h-1v-8-2z" class="I"></path><path d="M321 306l1 1v3 4c1 2 1 3 1 6h-2v-1c0-2-1-2-2-3 0 1 0 1-1 1v1l-1-1v-1-3c0-1 1-1 1-2h0l1 2h1 1v-3-4z" class="R"></path><path d="M317 313c0-1 1-1 1-2h0l1 2c0 1 0 3-1 4v1l-1-1v-1-3z" class="Q"></path><path d="M322 310c2-2 0-5 2-8 1 1 1 1 1 3l1-1h0v4 4h1l-1 1h0c0 1 0 1 1 2l1 1v2l-1 1-1-1h0v-2c0-1-1-1-1-2v-1l-2 1c0 1 1 0 1 1s-1 2-1 2c1 1 1 1 1 2h-1v1c0-3 0-4-1-6v-4z" class="E"></path><path d="M325 305l1-1h0v4 4h1l-1 1v-4l-1-1c-1 0-1 1-2 2l1-3h1v-2h0z" class="N"></path><path d="M322 310c2-2 0-5 2-8 1 1 1 1 1 3h0v2h-1l-1 3c0 1 0 2-1 4v-4z" class="I"></path><path d="M317 299v3h2c-1 3 0 7-1 9h0c0 1-1 1-1 2v3c0-2 0-3-2-5v-2l-1-1v-8c1 0 2 0 2-1h1z" class="U"></path><path d="M317 302h2c-1 3 0 7-1 9h0c0 1-1 1-1 2 0-2 1-6 0-8v-3z" class="O"></path><path d="M314 300c1 0 2 0 2-1 0 3 0 7-1 10h0l-1-1v-8z" class="Z"></path><path d="M312 300h0 0 1 1v8l1 1v2c2 2 2 3 2 5v1l1 1v1l-1 1-1-1h0c1-1 0-3 0-5h0c-1 1-1 2-1 3l-1 2-2-1h-1c0 1 0 1-1 1-1-2 0-5 0-8l1-6c0-1 1-4 1-5z" class="E"></path><path d="M314 308l1 1v2c0 2-1 5-2 7v-4c1-2 1-4 1-6z" class="d"></path><path d="M313 300h1v8c0 2 0 4-1 6v-3-2l-1 1 1-10z" class="B"></path><path d="M312 300h0 0 1l-1 10c-1 3-2 6-2 9-1-2 0-5 0-8l1-6c0-1 1-4 1-5z" class="Z"></path><path d="M327 286h2c2 0 2 0 4 1l-1 1v1c1 1 2 1 2 2l-1 1v1c-1 0-1 1-2 1l-2-1v1l-2-1h-1l-1 1-1-1-2-1v2l-1 1c0-1 0-1 1-1 0-1 0-1-1-2h0l-2-2h0l-1-1 1-1h2l4-1 2-1z" class="b"></path><path d="M329 286c2 0 2 0 4 1l-1 1v1c1 1 2 1 2 2l-1 1c-1-1-1-2-2-3v-2h-2 0v-1z" class="P"></path><path d="M327 286h2v1l-4 2-3 3h0v2l-1 1c0-1 0-1 1-1 0-1 0-1-1-2h0l-2-2h0l-1-1 1-1h2l4-1 2-1z" class="O"></path><path d="M319 290l6-1-3 3h0v2l-1 1c0-1 0-1 1-1 0-1 0-1-1-2h0l-2-2z" class="R"></path><path d="M277 405c1 1 1 2 1 3-1 1 0 2-1 3v5l1-1v4h-3c-1 1-1 2-1 3v1l-1-1-1 1v3s-1 0-1 1 1 1 1 1v2 5l-8-2-1 1-2-1h-2 0-14v-1c-9 1-18 2-25 7-1-1-1-2-1-3v-1c-2 1-3 1-4 2s-2 1-2 2l-3 1-1-1c-1 0-1 0-1-1 1-1 3-2 4-3v-3c1-1 1-1 1-2v-1c1 2 0 3 1 4h0l2-2v-2c1 0 1-1 2-1 1-1 2 0 3-1 1 0 2 1 3 0l1 1h0v1c-1 0-1 1-1 1l-1 1c1 0 0 0 1 1h1c1-1 1-2 2-2h1l1-1h1v1c0 1-1 1 0 2 1 0 2-1 3-1h3c3-1 6 0 9-2h0c1 0 2-1 3-1v-1c1 0 2-1 2-1h1 2c1 0 1 0 1 1h0c2 1 1 2 2 4l1-1v-1h0c1-2 0-2 0-3l1-1c1 0 1 1 3 1v2 2h-1c0 1 0 1 1 1h1v-3h0c1-1 0-2-1-3h0v-1h1c1 0 2 0 3 1v-1s0-1 1-1c1-1 1-1 2-1h1v-1-1c1 0 1-2 1-3v-3h0c-1 2-1 4-1 5s-1 1-1 1c-1 0-1 1-2 1h0c-1 0-2 1-3 0v-6c0-1 1-2 2-2h0l1 1h1c0-1 0-2 1-3h1c1-1 1-1 2-1h0c1 0 1-1 1-1 0-1 1-2 1-3 1 1 0 1 1 2 1-1 2-1 3-2v-1z" class="J"></path><path d="M268 424l1 1c0 1 0 3-1 4v1c1 0 1 1 0 1h0l-1-2c0-2 0-3 1-5z" class="l"></path><path d="M269 411c1 1-1 6-1 8h-1c-1-1 1-4 0-5 0-1 0-2 1-3h1z" class="W"></path><path d="M216 429c1 0 1-1 2-1 1-1 2 0 3-1 1 0 2 1 3 0l1 1h0v1c-1 0-1 1-1 1v-2h-1c-1 0-2 1-2 2h0-1c-1-1 0-1-1-2-1 0-1 1-1 1h-1c0 1 0 1-1 1v1-2z" class="P"></path><path d="M268 424l2-1v-3h1v2h2l-1 1v3s-1 0-1 1 1 1 1 1v2c-1 1-3 0-4 1 1 0 1-1 0-1v-1c1-1 1-3 1-4l-1-1z" class="F"></path><path d="M277 405c1 1 1 2 1 3-1 1 0 2-1 3v5c-1 0-1 1-3 1v-1c-1-1-1-6 0-8h0c1-1 2-1 3-2v-1z" class="D"></path><path d="M274 408c1-1 2-1 3-2 0 1 0 2-1 3 0 0 1 1 0 2v2h-1v-4l-1-1h0z" class="F"></path><path d="M212 435h0c2 0 2 0 3-1h1v1h2v-1c0-1 1-2 2-2h1c0 1 0 1 1 2l-2 1h-1c-2 1-3 1-4 2s-2 1-2 2l-3 1-1-1c-1 0-1 0-1-1 1-1 3-2 4-3z" class="C"></path><path d="M247 431h0v-2c1 0 2-1 3-2h1l1 1 1 1s0-1 2-1v2l-1 1v1h8 0c1 1 1 1 2 1l-1 1-2-1h-2 0-14v-1h1l1-1z" class="M"></path><path d="M246 432l1-1 2 1h0c-2 1-2 1-3 0z" class="a"></path><path d="M251 427l1 1 1 1c0 1 0 1-1 2h-3v-1c0-1 1-2 2-3z" class="K"></path><path d="M340 289c1-1 1 0 1-1v-2c1 0 2 1 3 2l5 4c-1 1-2 1-2 1h0c2 4 5 7 6 11l-2 2 1 2c-1 1-2 1-3 2-1-1-2-1-3 0v2h-1c-1 0-1 1-2 1l-1 1h0s-1 0-1 1c-1-1-1-1-2-1-1 1 0 2-1 3h0v2h1 0c0-1 0-1 1-2v2s-1 1-1 2v1l-1-1-1-1v-2l-1 1h-2c0 1-1 1-1 1h-3c0-1 0-1-1-1v1c-2 0-2 0-4-1 0-1 1-2 1-3v2h0l1 1 1-1v-2l-1-1c-1-1-1-1-1-2h0l1-1v-4c1-2 1-5 0-7 0-3 1-5 0-8l2 1v-1l2 1c1 0 1-1 2-1v-1l1-1c0-1-1-1-2-2v-1l1-1 2 1 1-1h2 1 0l1 1v1z"></path><path d="M335 307v-2h1 0l1 3-1 1v-2l-2 1 1-1z" class="M"></path><path d="M330 300l1-2c1 1 1 4 1 5l1 1h-2l-1-4z" class="N"></path><path d="M340 300c-1-1-1-5-1-6v-1c1 2 1 5 2 7h-1z" class="H"></path><path d="M343 289c2 2 0 4 2 6v1l-1 1c0-2-1-3-1-5 0-1-1-2 0-3z" class="Z"></path><path d="M347 306l1 2h0l1 2c-1-1-2-1-3 0h0l-1-3 2-1z" class="E"></path><path d="M343 303c-1-3-2-7-2-10h1c0 3 1 5 2 8 0-1 0-1-1-1v3z" class="U"></path><path d="M337 296l1 4c1 1 1 2 0 3 0 0 0 1-1 1-1-2 0-6 0-8zm6 7v-3c1 0 1 0 1 1l2 4s0 1 1 1l-2 1-2-4z" class="D"></path><path d="M335 307c0-1-1-2-1-3-1-1-1-3 0-5v-1-3c-1 0-1 0 0-1 1 2 1 3 1 5 0 1 1 2 1 3v3h0-1v2z" class="N"></path><path d="M335 288l1-1h2 1 0l1 1v1c-2 2-5 3-7 4v-1l1-1c0-1-1-1-2-2v-1l1-1 2 1z" class="k"></path><path d="M335 288l1-1h2 1 0l1 1c-2 1-3 0-5 0z"></path><defs><linearGradient id="AZ" x1="339.313" y1="304.081" x2="344.745" y2="310.373" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#aaabab"></stop></linearGradient></defs><path fill="url(#AZ)" d="M340 300h1l2 5c1 2 2 3 2 5v1 1c-1 0-1 1-2 1l-1 1c-1-5-1-9-2-14z"></path><path d="M344 288l5 4c-1 1-2 1-2 1h0c2 4 5 7 6 11l-2 2 1 2c-1 1-2 1-3 2l-1-2v-3c-1-1-2-2-2-4-1-1-1-3-2-4l1-1v-1c-2-2 0-4-2-6l1-1z" class="E"></path><path d="M348 305h2c1 0 1 1 1 1l1 2c-1 1-2 1-3 2l-1-2v-3z" class="b"></path><path d="M344 288l5 4c-1 1-2 1-2 1h0-1-1c1 1 1 2 1 2 2 2 3 3 4 5v1c-2-1-2-3-3-4 0 2 1 4 2 6h0c-2-1-3-3-3-5v-1c-1 0-1-1-1-1v-1c-2-2 0-4-2-6l1-1z" class="I"></path><defs><linearGradient id="Aa" x1="329.782" y1="300.227" x2="335.311" y2="317.477" xlink:href="#B"><stop offset="0" stop-color="#828082"></stop><stop offset="1" stop-color="#bab9b9"></stop></linearGradient></defs><path fill="url(#Aa)" d="M327 293l2 1c-1 2-1 5 0 7l1-1 1 4h2v4l1 1v-1l2-1v2l1-1s1 0 1-1-1-2 1-2c1 1 2 7 2 8l1 1s-1 0-1 1c-1-1-1-1-2-1-1 1 0 2-1 3h0v2h1 0c0-1 0-1 1-2v2s-1 1-1 2v1l-1-1-1-1v-2l-1 1h-2c0 1-1 1-1 1h-3c0-1 0-1-1-1v1c-2 0-2 0-4-1 0-1 1-2 1-3v2h0l1 1 1-1v-2l-1-1c-1-1-1-1-1-2h0l1-1v-4c1-2 1-5 0-7 0-3 1-5 0-8z"></path><path d="M329 301l1-1 1 4c0 2 0 4 1 6l-1 1-1-1c-1-3 0-6-1-9z" class="I"></path><path d="M299 364h1l-3 6c-2 8-4 18-2 26 1 13 5 24 10 36h1v-1l1 2v-1c3 5 6 10 10 15 0 1 1 2 1 3 2 2 3 5 5 7l8 8 5 5 7 4-2 1c5 3 10 5 14 8h0-1l1 1-1 1c-8-4-18-9-25-15-7-7-13-15-19-23l-1 1c-1-2-3-5-4-7v-2c-1-4-4-6-5-10v-2-1c-2-2-2-6-3-8l-2-1h0l-1-1v1l-2-7h0c-1-1-1-2-1-3l-1-9c-1-4-1-7-1-11 0-2 1-3 0-4-1-2 0-4 1-6v-3c1 1 1 2 1 3v-2l1-1v1h1l2-5 1 1c1-2 2-6 3-7z" class="f"></path><path d="M300 426c0-1 0-1 1-2l2 5-1 1c-1-1-1-2-2-3v-1z" class="B"></path><path d="M323 457l8 8-2-1v1 1c-3-2-5-6-7-8 1 0 1 0 1-1z" class="R"></path><path d="M313 446h1c1 1 1 1 1 2 1 1 2 1 3 2 2 2 3 5 5 7 0 1 0 1-1 1-2-2-3-3-4-5s-3-4-5-7z" class="B"></path><path d="M329 466v-1-1l2 1 5 5 7 4-2 1c-4-2-8-6-12-9z" class="J"></path><path d="M305 432h1v-1l1 2v-1c3 5 6 10 10 15 0 1 1 2 1 3-1-1-2-1-3-2 0-1 0-1-1-2h-1c-3-4-6-9-8-14z" class="R"></path><path d="M300 427c1 1 1 2 2 3l1-1v1c2 3 3 6 4 10 5 7 10 15 16 21a79.93 79.93 0 0 0 13 13c4 2 8 5 12 6l6 3 1 1-1 1c-8-4-18-9-25-15-7-7-13-15-19-23l-1 1c-1-2-3-5-4-7v-2c-1-4-4-6-5-10v-2z" class="E"></path><path d="M305 439l5 8-1 1c-1-2-3-5-4-7v-2z" class="h"></path><path d="M295 370l1 1c-3 14-4 28 1 42l3 8c0 1 1 2 1 3-1 1-1 1-1 2-2-2-2-6-3-8l-2-1h0l-1-1v1l-2-7h0c-1-1-1-2-1-3l-1-9c-1-4-1-7-1-11 0-2 1-3 0-4-1-2 0-4 1-6v-3c1 1 1 2 1 3v-2l1-1v1h1l2-5z" class="E"></path><path d="M294 414h1v-1c1 1 2 1 2 2v3l-2-1h0c0-1-1-3-1-3z" class="F"></path><path d="M293 409l1 5s1 2 1 3l-1-1v1l-2-7s1 0 1-1z" class="O"></path><path d="M291 393c0 5 1 11 2 16 0 1-1 1-1 1h0c-1-1-1-2-1-3l-1-9h1v-5z" class="Q"></path><path d="M291 377v-2l1-1v1c-1 6-1 12-1 18v5h-1c-1-4-1-7-1-11 0-2 1-3 0-4-1-2 0-4 1-6v-3c1 1 1 2 1 3z" class="I"></path><path d="M290 374c1 1 1 2 1 3l-2 10c0-2 1-3 0-4-1-2 0-4 1-6v-3z" class="N"></path><path d="M287 287h0c1-1 2-2 3-2 2 0 3-1 5-1h0 4l-1 1h3 1l-1 1v2h-1l-1 1c0 1-1 1-2 1l-1 2 1 1c-1 0-2 1-2 2v1c-1 2-5 6-7 8-2 1-3 4-4 5-2 3-4 8-4 11v1 2l-1 4c-1 2-2 7-4 8h-1l2-4h-1l-1 1c-1 0-1-1-2-1l-1 4h-1v-1-1c0-1-1-1-2-1l-1 1-1 1h-2l-1 1-1-1h0-2v-1h3-2v-1c2-1 3-3 3-6 1 0 2-4 2-4 1-2 2-4 2-6 1-2 2-4 1-5 0-3 2-7 4-9v-2c0-1 1-1 1-2 3-3 5-4 8-5v-1c0-2 0-2 1-3h0c1-1 2-1 4-1v-1z" class="W"></path><path d="M271 323h2c-1 2-1 4-2 5l-2-1 2-4z" class="R"></path><path d="M269 327l2 1 2 1c-1 1-2 3-3 5v-1c0-1-1-1-2-1l-1 1 2-6z" class="P"></path><path d="M286 293h0c-1 2-2 4-3 5-2 2-3 3-4 5l-1 1c-1 1-1 4-2 5-1 2-3 4-3 6 0 1 0 1-1 1 0-2 2-4 2-5l5-10v-2l1-1c1-2 3-4 6-5z" class="I"></path><path d="M273 319c1-1 2-3 2-4 2-2 3-4 5-5h0l-5 11h0v2c-1-1-1 0-1-1l-1 1h-2c1-2 1-3 2-4z" class="M"></path><path d="M273 319v2h1v-1h1v1 2c-1-1-1 0-1-1l-1 1h-2c1-2 1-3 2-4z" class="H"></path><path d="M289 292c1 0 3-1 3-2h1c1 0 2-1 3 0 0 0-1 0-1 1l-2 1c-3 2-5 5-7 8-3 2-5 4-6 7l-1 1h-1c0-2 2-5 3-6l8-10z" class="I"></path><path d="M296 290v-1-1c1 0 1 0 2 1-1 0-1 1-1 1l-1 2 1 1c-1 0-2 1-2 2-3 4-6 7-9 10-4 5-8 10-10 16-1 2-1 3-2 5 0 2-1 3-2 5l-1 4h-1v-1c1-2 2-4 3-5l-2-1c1-1 1-3 2-5l1-1c0 1 0 0 1 1v-2h0l5-11h0l2-2h0l-1-1c1-2 3-3 4-5l6-7c2-1 4-3 4-4s1-1 1-1z" class="Q"></path><path d="M273 323l1-1c0 1 0 0 1 1-1 2-1 4-2 6l-2-1c1-1 1-3 2-5z" class="E"></path><path d="M287 287h0c1-1 2-2 3-2 2 0 3-1 5-1h0 4l-1 1h3 1l-1 1v2h-1l-1 1c0 1-1 1-2 1 0 0 0-1 1-1-1-1-1-1-2-1v1 1c-1-1-2 0-3 0h-1c0 1-2 2-3 2h-2 0l-1-1h-1l-3 1c0-2 0-2 1-3h0c1-1 2-1 4-1v-1z" class="N"></path><path d="M283 289h0c1-1 2-1 4-1-2 1-3 3-5 4 0-2 0-2 1-3z" class="Y"></path><defs><linearGradient id="Ab" x1="295.18" y1="286.898" x2="291.141" y2="283.712" xlink:href="#B"><stop offset="0" stop-color="#373436"></stop><stop offset="1" stop-color="#505252"></stop></linearGradient></defs><path fill="url(#Ab)" d="M287 287h0c1-1 2-2 3-2 2 0 3-1 5-1h0 4l-1 1c-4 1-8 3-11 5l-1-1 2-1-1-1z"></path><path d="M286 291c5-2 10-4 15-5v2h-1l-1 1c0 1-1 1-2 1 0 0 0-1 1-1-1-1-1-1-2-1v1 1c-1-1-2 0-3 0h-1c0 1-2 2-3 2h-2 0l-1-1z" class="O"></path><path d="M295 295v1c-1 2-5 6-7 8-2 1-3 4-4 5-2 3-4 8-4 11v1 2l-1 4c-1 2-2 7-4 8h-1l2-4h-1l-1 1c-1 0-1-1-2-1 1-2 2-3 2-5 1-2 1-3 2-5 2-6 6-11 10-16 3-3 6-6 9-10z" class="M"></path><path d="M276 321c1 0 2 0 3-1v4l-1 2h-1c-1-1-1-1-3 0 1-2 1-3 2-5z" class="E"></path><path d="M274 326c2-1 2-1 3 0h1l-1 3c0 1-1 2-1 2h0-1l-1 1c-1 0-1-1-2-1 1-2 2-3 2-5z" class="C"></path><path d="M282 292l3-1c0 1 0 1 1 2-3 1-5 3-6 5l-1 1v2l-5 10c0 1-2 3-2 5 1 0 1 0 1-1l1 1-1 1c-1 2-2 3-2 5h0l-1 1-4 8c0 1-1 2 0 3h-2l-1 1-1-1h0-2v-1h3-2v-1c2-1 3-3 3-6 1 0 2-4 2-4 1-2 2-4 2-6 1-2 2-4 1-5 0-3 2-7 4-9v-2c0-1 1-1 1-2 3-3 5-4 8-5v-1z" class="i"></path><path d="M274 303c0-1 1-2 2-3h1 0c0 1-1 3-2 4l-1-1z" class="c"></path><path d="M268 319l3 3-1 1c-1 1-1 1-2 1 0 0-1 1-2 1 1-2 1-4 2-6z" class="M"></path><path d="M282 292l3-1c0 1 0 1 1 2-3 1-5 3-6 5l-1 1h-1c0-1 1-1 1-2 1-1 1-2 2-3 0 0 1 0 1-1h0 0v-1z" class="f"></path><path d="M266 325c1 0 2-1 2-1 1 0 1 0 2-1l-4 8c0 1-1 2 0 3h-2l-1 1-1-1h0-2v-1h3l3-8z" class="B"></path><path d="M275 304l-3 6h1 0c1-2 2-4 3-5v-2c1-1 2-2 3-2l-5 10c0 1-2 3-2 5 1 0 1 0 1-1l1 1-1 1c-1 2-2 3-2 5h0l-3-3 1-4c1-1 2-3 2-4-1-3 2-5 3-8l1 1z" class="S"></path><path d="M204 430l2-2c1-1 2-1 3-2v-1c2-1 2-2 3-3l2-1c1 1 1 3 1 4s0 1-1 2c0 0-1 0-2 1-1 0-1 1-1 1l-1 1h1 0 2c0 1 0 1-1 2v3c-1 1-3 2-4 3h0-1l-4 1v1h-2l-3 3c0 1 0 1-1 2s-2 1-2 2c-1 1-1 1 0 2l-1 1c-1 1-2 1-2 2h0l-1 3h1l-1 2c0 1 1 1 1 2h0c-3 3-5 6-7 9-1 2-2 3-3 5-2 4-4 7-6 10-1 2-1 5-2 7s-2 4-2 6c0 1-1 2-1 2l-1-1h0v-1h-1l2-13c1-3 1-6 2-8 0 0 0-1 1-2l8-21v-2-3h0c1-2 1-3 2-4l-1 1v-1-1l1-3h0l3-3c0-1 1-2 2-3v1h1v-2l1 1c1 0 1 0 2-1h2v-1c2-1 4-1 5-3h0c2 0 2 0 3-1 0 1 1 1 1 2v1z" class="b"></path><path d="M203 427c0 1 1 1 1 2h-2c0-1-1-1-2-1h0c2 0 2 0 3-1z" class="B"></path><path d="M184 461l1-1 2 2v2h-2c0-1 0 0-1-1-1 0 0-1 0-2z" class="J"></path><path d="M186 450l-1-1c1-2 2-3 2-4v-1h1v2h1c-1 2-2 3-3 4z" class="E"></path><path d="M207 432h1l1 1c-1 1-2 2-2 3-1 1-1 0-2 0l-1-1 3-3z" class="O"></path><path d="M174 481c0-2 0-4 1-6 1 0 1-1 1-2s1-1 2-1v1 1c-1 1-1 1-1 2l-2 5h-1z" class="g"></path><path d="M190 446v1c-1 1-2 2-1 3v1l1 1h-3-1c0 1-1 2-2 2l-1 1c1-2 2-4 3-5s2-2 3-4h1z" class="Q"></path><path d="M184 461c0 1-1 2 0 2 1 1 1 0 1 1s0 1-1 2c-2 0-2 1-3 2v1c-1 2-1 4-3 5h0v-1c0-1 1-2 1-3l2-4c0-1 1-2 1-3 1-1 1-2 2-2zm29-31c0 1 0 1-1 2v3c-1 1-3 2-4 3h0-1l3-3h0l-3 1c0-1 1-2 2-3l-1-1 3-2h2z" class="M"></path><path d="M213 430c0 1 0 1-1 2 0 0 0 1-1 1h0l-1-1-1 1-1-1 3-2h2z" class="Y"></path><path d="M183 455l1-1c1 0 2-1 2-2h1 3 2 0l-1 3-3 4h-1c0-1-1-1-2-1h0c-1-1-1-1-2-1h-1c0-1 1-2 1-2z" class="d"></path><path d="M204 430l2-2c1-1 2-1 3-2v-1c2-1 2-2 3-3l2-1c1 1 1 3 1 4s0 1-1 2c0 0-1 0-2 1-1 0-1 1-1 1l-1 1h1 0l-3 2h-1c0-1-1-1-2-1s-2 2-4 4v-1h0l3-4h0z" class="C"></path><path d="M212 428v-1c0-1 0-1 1-2l1-1 1 1c0 1 0 1-1 2 0 0-1 0-2 1z" class="D"></path><path d="M182 452l-4 13c-1 2-2 4-3 7 0 2-2 3-2 5s-1 4 0 5c0 2-1 6 0 8h1c-1 2-2 4-2 6 0 1-1 2-1 2l-1-1h0v-1h-1l2-13c1-3 1-6 2-8 0 0 0-1 1-2l8-21z" class="Q"></path><path d="M170 497l3-15c0 2-1 6 0 8h1c-1 2-2 4-2 6 0 1-1 2-1 2l-1-1z" class="E"></path><path d="M190 442s1-1 1-2c1 0 1-1 2-2l3-3h1c-1 1-1 1-1 2h1v2l1 1h-1c-1 1-1 2-1 3s0 1 1 1l1-1c0 1 0 1-1 2s-2 1-2 2c-1 1-1 1 0 2l-1 1c-1 1-2 1-2 2h-2l-1-1v-1c-1-1 0-2 1-3v-1h-1-1v-2h1v-1l1-1z" class="P"></path><path d="M189 451c1-1 2-1 2-2 1-1 2-2 2-3h1l1 1c-1 1-1 1 0 2l-1 1c-1 1-2 1-2 2h-2l-1-1zm1-9l1 1c0-1 0-1 1-2 0-1 1-1 2-1l1 1c-1 1-1 2-1 3l-2 1h-1l-1 1h-1-1v-2h1v-1l1-1z" class="C"></path><path d="M191 455h1l-1 2c0 1 1 1 1 2h0c-3 3-5 6-7 9-1 2-2 3-3 5-2 4-4 7-6 10-1 2-1 5-2 7h-1c-1-2 0-6 0-8-1-1 0-3 0-5v3l1 1h1l2-5c0-1 0-1 1-2h0c2-1 2-3 3-5v-1c1-1 1-2 3-2 1-1 1-1 1-2h2v-2c1 0 1-1 1-2v-1l3-4z" class="k"></path><path d="M226 331v1l-4 5c0 1-1 2-2 3l1 1c-1 2-2 3-1 5l-1 3-1 1-1 1-1-1-13 23h-2c-3 7-4 14-6 20l-3 6c0 1-1 2-1 3l-1 1-5 14 1 2h0c-1 1-2 1-3 1v3l-3 10 1 1 1 1c-1 1-1 2-1 3-2 2-2 4-4 5l-1 2v4c0 1 0 3-1 4h0c-1 2-1 4-2 6v7l-1 8 1 1c-1 2-1 5-2 8l-2 13-1 4v1c-1 1-1 1-1 2l-1 1c0-2 0-3-1-4h0v-1-6l-1-16c0-7 2-14 3-21l2-7c1-2 1-4 2-7 0-2 2-5 1-7h1l1 1c1-6 2-12 5-17l3-7 1-4 13-37h1c1 0 1-1 1-1 0-1 1-3 1-4l5-8c0-1 1-2 1-3v-1l1-1c0-2 2-3 3-4 0-2 2-3 3-5 2-1 3-3 5-5v2l3-3c1-2 3-5 6-7z" class="W"></path><path d="M183 420l2-3 1 2h0c-1 1-2 1-3 1z" class="C"></path><path d="M177 443l3-10 1 1 1 1c-1 1-1 2-1 3-2 2-2 4-4 5z" class="K"></path><path d="M220 340l1 1c-1 2-2 3-1 5l-1 3-1 1-1 1-1-1h-1v-3c0-2 2-4 3-5l1 1c0-1 1-2 1-3z" class="U"></path><path d="M217 345h1c0 1 0 3 1 4l-1 1-1 1-1-1h-1l2-5z" class="E"></path><path d="M220 340l1 1c-1 2-2 3-1 5l-1 3c-1-1-1-3-1-4h-1c1 0 1-1 2-2 0-1 1-2 1-3z" class="F"></path><path d="M176 445v4c0 1 0 3-1 4h0c-1 2-1 4-2 6v7h-1c-1 1-1 3-2 4 0-4 1-8 2-12 0-4 1-8 3-10 1-1 1-2 1-3h0z" class="B"></path><path d="M215 347v3h1l-13 23h-2c2-5 5-9 7-14 3-4 5-8 7-12z" class="F"></path><path d="M179 419l1-1c1 0 1-2 2-3v2h1v1c0 3-1 5-2 8l-6 19-1 2c0-1-1-4-1-5h-2 0c0-2 2-5 1-7h1l1 1c1-6 2-12 5-17z" class="L"></path><path d="M172 435h1l1 1h0v9h1l-1 2c0-1-1-4-1-5h-2 0c0-2 2-5 1-7z" class="E"></path><path d="M171 442h2c0 1 1 4 1 5-3 8-4 15-5 23-1 6-2 12-1 18 0-3 0-6 1-8 0-3 0-6 1-10 1-1 1-3 2-4h1l-1 8 1 1c-1 2-1 5-2 8l-2 13-1 4v1c-1 1-1 1-1 2l-1 1c0-2 0-3-1-4h0v-1-6l-1-16c0-7 2-14 3-21l2-7c1-2 1-4 2-7h0z" class="K"></path><path d="M171 442h0v1 2c1 1 1 2 0 3 0 1-1 1-2 1 1-2 1-4 2-7z" class="C"></path><defs><linearGradient id="Ac" x1="187.191" y1="366.702" x2="209.023" y2="393.254" xlink:href="#B"><stop offset="0" stop-color="#9f9d9b"></stop><stop offset="1" stop-color="#cfced3"></stop></linearGradient></defs><path fill="url(#Ac)" d="M205 355c3-4 6-8 9-11 1-1 1-3 3-4l-6 12-6 11c-1 2-3 4-4 6-3 7-5 16-7 23-2 4-3 8-5 11l-6 15v-1h-1v-2c-1 1-1 3-2 3l-1 1 3-7 1-4 13-37h1c1 0 1-1 1-1 0-1 1-3 1-4l5-8c0-1 1-2 1-3z"></path><path d="M183 408l2 2c-1 0-1 1-2 1l-1 1 1-4z" class="P"></path><path d="M285 253h0c1 1 1 1 1 2h0l1-1v-2h0l2-2c-1 1-1 2 0 3 0 1-1 2-1 3l-1 1c0 1 1 1 2 3v1h1c1 0 1-1 2-1v1h0c0 1 0 1 1 2l1-1c1-1 1-1 2-1 0 0 0 1 1 1s1 0 2 1h0c1 1 1 1 2 1v-1h1l1 1h1c-1 1-1 2-2 3h0v1 1 1h0 1c-1 2-1 3-1 5v1l2-2h1l-1 1v1l2-1h1 0c0 1 1 1 2 1h1l-1 1 2 1v1h0v2l1 1c1 1 1 1 1 2l1 1h-1l-1 1h-1l-1 1c-1 0-2 1-3 1-1 1-3 2-4 3h0-1-1-1c-1 1-2 1-3 2l-1-1 1-2c1 0 2 0 2-1l1-1h1v-2l1-1h-1-3l1-1h-4 0c-2 0-3 1-5 1-1 0-2 1-3 2h0v1c-2 0-3 0-4 1h0c-1 1-1 1-1 3v1c-3 1-5 2-8 5 0 1-1 1-1 2v2c-2 2-4 6-4 9 1 1 0 3-1 5 0 2-1 4-2 6 0 0-1 4-2 4 0 0-1-1-2-1l-1 1c-1 2-1 4-3 6h0c-2 0-3 0-4-1v-1c0-1-1-2-1-2v-1l-2 2h-1 0v-1c1-2 2-3 4-5v-1c0-1-1-2-1-3h-1c0-1 1-2 2-3l2-2v-1l-1-1 2-3 3-3v-1-1h-1 0v-1-2c0 1-1 2-2 2h-1l3-3-1-1h0 0l2-4c1-1 0-2 1-3l3-7h1c3-4 5-8 7-12 0-1 1-1 1-2h0c0-1 1-3 2-4 0-2 1-4 3-6l1 1c1-1 1-3 3-3 1 0 1 0 1-1v-1-1l1-2c1 0 1-1 1-1z" class="h"></path><path d="M283 273l1 1-1 2h-1c0-1 1-2 1-3z" class="O"></path><path d="M282 276h1c-1 2-1 2-2 3h-1v-1l2-2z" class="Q"></path><path d="M286 268c0 1 0 2 1 2v1l-3 3-1-1c1-1 3-3 3-5z" class="j"></path><path d="M259 309c0 2-1 4-2 5 0 2-1 3-1 4v-2-1l-2 1 2-2c1-2 2-3 3-5z" class="a"></path><path d="M259 304c2-2 4-4 5-6l1-1h1c-1 2-1 2-2 3h-1c0 2-2 4-3 5v-1h-1 0z" class="W"></path><path d="M260 306c0 1 0 1-1 2v1c-1 2-2 3-3 5v-1l-1-1 2-3 3-3z" class="V"></path><path d="M254 316l2-1v1 2l-2 5v-1c0-1-1-2-1-3h-1c0-1 1-2 2-3z" class="G"></path><path d="M271 299s1-1 2-1c0 0 1-1 1-2v2c0 1-1 1-1 2v2h-1c-1 0-2 1-3 2-1 0-1 0-1 1-1 0-1 1-1 1l-1-1c1-2 3-4 5-6z" class="W"></path><path d="M282 271l1 1h0l-2 4c-2 3-5 5-7 8s-2 6-5 8c0 1 0 1 1 2-1 0-2 1-2 2h-1c1-2 1-3 2-5 1-1 2-2 2-3v-2l2-2c1-2 3-3 4-5 2-2 3-5 5-7v-1z" class="l"></path><path d="M281 271l1 1c-2 2-3 5-5 7-1 2-3 3-4 5l-2 2v2c0 1-1 2-2 3-1 2-1 3-2 5h-2c1-3 3-5 4-7s2-3 2-5c2-2 4-5 6-7l1-1c1-2 2-4 3-5z" class="d"></path><path d="M266 292c0-1 1-3 2-4 3-4 5-8 8-12l1 1c-2 2-4 5-6 7 0 2-1 3-2 5s-3 4-4 7c-2 2-4 4-6 5 0 1-1 2-2 2h-1l3-3c1 0 1-1 2-2l3-4 2-2z" class="f"></path><path d="M271 299h-1c-2 1-2 3-4 4-1 0-3 1-3 3h-2 0 0c0-2 5-6 6-6 1-1 3-2 3-3 2-3 3-3 4-6h1l1 1c0 1 1 1 2 2-1 1-3 1-4 2 0 1-1 2-1 2-1 0-2 1-2 1z" class="O"></path><path d="M278 270l1-1v1c-1 1-1 3-1 4-1 1-1 2-2 2-3 4-5 8-8 12-1 1-2 3-2 4l-2 2-3 4c-1 1-1 2-2 2l-1-1h0 0l2-4c1-1 0-2 1-3l3-7h1l-1 2v1 1h1c1-2 3-4 4-6 3-4 6-8 9-13z" class="d"></path><path d="M264 294c-1 0-1 0-1-1s1-1 2-2v1h1l-2 2z" class="I"></path><path d="M261 292l3-7h1l-1 2v1c-1 1-1 1-1 2v1c-2 2-1 6-5 8h0l2-4c1-1 0-2 1-3z" class="O"></path><path d="M279 262c1-1 1-3 3-3l-2 3 1 1c-1 3-2 5-3 7-3 5-6 9-9 13-1 2-3 4-4 6h-1v-1-1l1-2c3-4 5-8 7-12 0-1 1-1 1-2h0c0-1 1-3 2-4 0-2 1-4 3-6l1 1z" class="Y"></path><path d="M279 262c1-1 1-3 3-3l-2 3h0c-2 4-3 9-6 12v-1c0-1 1-1 1-2 0-2 1-5 3-7 0-1 1-1 1-2h0z" class="I"></path><path d="M272 273c0-1 1-1 1-2v2 1c0 2-2 3-3 5-1 3-3 7-6 9v-1l1-2c3-4 5-8 7-12z"></path><path d="M292 260v1h0c0 1 0 1 1 2l1-1c1-1 1-1 2-1 0 0 0 1 1 1s1 0 2 1h0v3c-1 1-1 1-1 2v1c0 1 0 2 1 3l-1 1c-1 0-1 1-1 0-1 0-1 0-2 1v-1-1c-1 0-1-1-1-2-2 0-2 0-3 1l-7 7-6 6c-1 1-2 2-3 2 2-4 7-6 10-9l-1-1h-1l1-2 3-3c1 0 2-2 3-3h-1v-1c1-1 1-1 1-2v-1l1-1h1v-1-1-1z" class="G"></path><path d="M295 272l1-1h1v2c-1 0-1 0-2 1v-1-1z" class="W"></path><path d="M290 268h1c0 2-4 6-5 8l-1 1h0l-1-1h-1l1-2 3-3c1 0 2-2 3-3z" class="Z"></path><path d="M292 260v1h0c0 1 0 1 1 2l1-1c1-1 1-1 2-1 0 0 0 1 1 1s1 0 2 1h0v3c-1 1-1 1-1 2v1c-1 0-1 1-2 1l-1-1h0-1c-1-1-2-1-2-2l-1-1-1 1h-1c1-1 1-1 1-2v-1l1-1h1v-1-1-1z" class="C"></path><path d="M292 260v1h0c0 1 0 1 1 2h0c-1 1-1 2-1 2 0 1 0 1-1 1l-1-2 1-1h1v-1-1-1z" class="b"></path><path d="M294 262c1-1 1-1 2-1 0 0 0 1 1 1s1 0 2 1c-1 0-1 0-2 1-1 0-1 0-1 1l-1 2-1 1v-1c0-1 0-2 1-4v-1h-1z" class="L"></path><path d="M297 264c1-1 1-1 2-1h0v3c-1 1-1 1-1 2v1c-1 0-1 1-2 1l-1-1 1-1v-1l1-1c0-1 0-1 1-2h-1z" class="K"></path><path d="M285 253h0c1 1 1 1 1 2h0l1-1v-2h0l2-2c-1 1-1 2 0 3 0 1-1 2-1 3l-1 1c0 1 1 1 2 3v1h1c1 0 1-1 2-1v1 1 1h-1l-1 1v1c0 1 0 1-1 2v1h1c-1 1-2 3-3 3v-1c-1 0-1-1-1-2h-1l-2 4-1-1v1l-1-1c-1 1-2 3-3 5l-1 1-1-1c1 0 1-1 2-2 0-1 0-3 1-4v-1l-1 1c1-2 2-4 3-7l-1-1 2-3c1 0 1 0 1-1v-1-1l1-2c1 0 1-1 1-1z" class="a"></path><path d="M281 271c1-2 2-5 3-7h1c-1 1-1 2-1 3-1 2-1 3-2 4v1l-1-1z" class="Q"></path><path d="M282 261h1c-1 5-3 9-5 13 0-1 0-3 1-4v-1l-1 1c1-2 2-4 3-7l1-2z" class="I"></path><path d="M289 260v1h1c1 0 1-1 2-1v1 1 1h-1l-1 1v1c0 1 0 1-1 2v1h1c-1 1-2 3-3 3v-1c-1 0-1-1-1-2h-1v-1c0-1 1-2 2-3 0-1 1-1 2-3v-1z" class="c"></path><path d="M289 260v1h1c1 0 1-1 2-1v1 1 1h-1l-1 1v1c-1 0-1 1-2 1v1l-1-1v-2c0-1 1-1 2-3v-1z" class="B"></path><path d="M285 253h0c1 1 1 1 1 2h0l1-1v-2h0l2-2c-1 1-1 2 0 3 0 1-1 2-1 3l-1 1c0 1 1 1 2 3-2 1-2 1-2 2-1 1-1 1-1 2h-1c0-1 1-4 1-6-1 2-2 3-2 4h-1c0-1 1-2 1-3h0c-1 0-1 1-2 2l-1 2-1-1 2-3c1 0 1 0 1-1v-1-1l1-2c1 0 1-1 1-1z" class="K"></path><defs><linearGradient id="Ad" x1="264.659" y1="307.876" x2="254.158" y2="330.392" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#a6a5a6"></stop></linearGradient></defs><path fill="url(#Ad)" d="M269 304c1-1 2-2 3-2h1c-2 2-4 6-4 9 1 1 0 3-1 5 0 2-1 4-2 6 0 0-1 4-2 4 0 0-1-1-2-1l-1 1c-1 2-1 4-3 6h0c-2 0-3 0-4-1v-1c0-1-1-2-1-2v-1l-2 2h-1 0c2-2 3-4 4-6 2-4 4-8 7-12v-1c1-2 2-2 4-3h0l1-2 1 1s0-1 1-1c0-1 0-1 1-1z"></path><path d="M261 314c0 1 0 1 1 1-1 2-2 4-2 5-1 1-2 2-3 2 0 0 1-1 1-2 1-2 1-4 3-6z" class="Y"></path><path d="M261 311v-1c1-2 2-2 4-3l-1 2c-1 2-1 3-2 5v1c-1 0-1 0-1-1v-3z" class="i"></path><path d="M262 314v-1h0c-1-1 0-3 1-4h1c-1 2-1 3-2 5z" class="Q"></path><path d="M269 304c1-1 2-2 3-2h1c-2 2-4 6-4 9 1 1 0 3-1 5 0 2-1 4-2 6 0 0-1 4-2 4 0 0-1-1-2-1l-1 1c-1 2-1 4-3 6v-1c1-2 2-3 2-5l6-17c0-1-1-1-1-2l1-2 1 1s0-1 1-1c0-1 0-1 1-1z" class="Z"></path><path d="M266 305l1 1-1 3c0-1-1-1-1-2l1-2z" class="G"></path><path d="M263 321h3v1s-1 4-2 4c0 0-1-1-2-1l-1 1 2-5z" class="B"></path><defs><linearGradient id="Ae" x1="266.01" y1="304.363" x2="269.379" y2="318.817" xlink:href="#B"><stop offset="0" stop-color="#676365"></stop><stop offset="1" stop-color="#868888"></stop></linearGradient></defs><path fill="url(#Ae)" d="M269 304c1-1 2-2 3-2h1c-2 2-4 6-4 9 1 1 0 3-1 5 0 2-1 4-2 6v-1h-3l6-17z"></path><path d="M299 263c1 1 1 1 2 1v-1h1l1 1h1c-1 1-1 2-2 3h0v1 1 1h0 1c-1 2-1 3-1 5v1l2-2h1l-1 1v1l2-1h1 0c0 1 1 1 2 1h1l-1 1 2 1v1h0v2l1 1c1 1 1 1 1 2l1 1h-1l-1 1h-1l-1 1c-1 0-2 1-3 1-1 1-3 2-4 3h0-1-1-1c-1 1-2 1-3 2l-1-1 1-2c1 0 2 0 2-1l1-1h1v-2l1-1h-1-3l1-1h-4 0c-2 0-3 1-5 1-1 0-2 1-3 2h0v1c-2 0-3 0-4 1h0c-1 1-1 1-1 3v1c-3 1-5 2-8 5v-2c1-1 3-1 4-2-1-1-2-1-2-2l-1-1h-1c1-2 3-3 4-5l7-6-1-1v-1l7-7c1-1 1-1 3-1 0 1 0 2 1 2v1 1c1-1 1-1 2-1 0 1 0 0 1 0l1-1c-1-1-1-2-1-3v-1c0-1 0-1 1-2v-3z" class="Q"></path><path d="M287 283c2 0 2 0 2 1l-2 1c-1 0-3 2-4 3v1c-1 1-1 1-1 3h-1c1-1 1-1 1-3s1-3 3-4c1 0 1-1 2-2z" class="f"></path><path d="M291 284l3-2 1 1v1c-2 0-3 1-5 1-1 0-2 1-3 2h0v1c-2 0-3 0-4 1h0v-1c1-1 3-3 4-3l2-1h1 1z" class="Z"></path><path d="M279 288c1 0 1-1 2-1 0 1-1 2-1 3 1 0 1 0 2-1 0 2 0 2-1 3h1v1c-3 1-5 2-8 5v-2c1-1 3-1 4-2-1-1-2-1-2-2l-1-1 1-2c2 0 2 0 3-1z" class="T"></path><path d="M282 289c0 2 0 2-1 3h-1l-1-1 1-1c1 0 1 0 2-1z" class="O"></path><path d="M279 288l-2 2 2 2v1l-1 1c-1-1-2-1-2-2l-1-1 1-2c2 0 2 0 3-1z" class="i"></path><path d="M291 271c1-1 1-1 3-1 0 1 0 2 1 2v1 1c-1 1-1 1-1 3l-1 2-1-1-2 2v1c-1 0-2 1-2 1l-1-1-2 1h-1c-1 1-2 3-4 3l-1 1-3 3-1 2h-1c1-2 3-3 4-5l7-6-1-1v-1l7-7z" class="T"></path><path d="M295 273v1c-1 1-1 1-1 3l-1 2-1-1-2 2v1c-1 0-2 1-2 1l-1-1-2 1h-1c2-2 5-5 7-6 1-1 2-2 4-3z" class="I"></path><path d="M295 273v1c-1 1-1 1-1 3l-1 2-1-1c0-1 1-2 1-3l-2 1c1-1 2-2 4-3z" class="O"></path><path d="M291 271l2 1c-1 3-6 7-8 8l-1-1v-1l7-7z" class="Q"></path><path d="M299 263c1 1 1 1 2 1v-1h1l1 1h1c-1 1-1 2-2 3h0v1 1 1h0 1c-1 2-1 3-1 5v1h1 0v1 1c1 0 1 1 1 2-4 0-10 1-13 3v1h-1-1c0-1 0-1-2-1l1-1h2v-1-1l2-2 1 1 1-2c0-2 0-2 1-3s1-1 2-1c0 1 0 0 1 0l1-1c-1-1-1-2-1-3v-1c0-1 0-1 1-2v-3z" class="i"></path><path d="M294 277s1 0 1-1 0 0 1-1v1l1 2v1h-2c-1 1-3 1-4 2v-1l2-1 1-2z" class="Z"></path><path d="M299 272h0c0 1 1 2 0 3-1 0-1 0-1 1h-2 0v-1c-1 1-1 0-1 1s-1 1-1 1c0-2 0-2 1-3s1-1 2-1c0 1 0 0 1 0l1-1z" class="j"></path><path d="M302 270h1c-1 2-1 3-1 5v1h1l-1 1h-2-1l-1-1c0-1 0-1 1-1 1-1 0-2 0-3h0c0-1 0-1 1-2v2l1 1c0-1 1-2 1-3h0z" class="G"></path><path d="M299 277l1-1h0l1-1h1s-1 1 0 1v1h-2-1z" class="Y"></path><path d="M299 263c1 1 1 1 2 1v-1h1l1 1h1c-1 1-1 2-2 3h0v1 1 1c0 1-1 2-1 3l-1-1v-2c-1 1-1 1-1 2-1-1-1-2-1-3v-1c0-1 0-1 1-2v-3z" class="X"></path><path d="M302 276l2-2h1l-1 1v1l2-1h1 0c0 1 1 1 2 1h1l-1 1 2 1v1h0v2l1 1c1 1 1 1 1 2l1 1h-1l-1 1h-1l-1 1c-1 0-2 1-3 1-1 1-3 2-4 3h0-1-1-1c-1 1-2 1-3 2l-1-1 1-2c1 0 2 0 2-1l1-1h1v-2l1-1h-1-3l1-1h-4 0v-1l-1-1-3 2v-1c3-2 9-3 13-3 0-1 0-2-1-2v-1-1h0-1z" class="C"></path><path d="M306 275h1 0c0 1 1 1 2 1h1l-1 1 2 1-1 1c0-1-1-1-1-1l-1 1-1-1h0c0-1-1-2-1-3z" class="B"></path><path d="M302 276l2-2h1l-1 1v1l2-1c0 1 1 2 1 3l-3 2c0-1 0-2-1-2v-1-1h0-1z" class="S"></path><path d="M294 282l7-1v2h2v1l-2 1h-3l1-1h-4 0v-1l-1-1z" class="i"></path><path d="M295 283h4 0v1h-4 0v-1z" class="Y"></path><path d="M301 281c3-1 7-1 9 0h1l-1 1c-2 1-3 1-5 2h-2v-1h-2v-2z" class="f"></path><path d="M308 284l2 1 1 1-1 1c-1 0-2 1-3 1-1 1-3 2-4 3h0-1-1-1c-1 1-2 1-3 2l-1-1 1-2c1 0 2 0 2-1l1-1h1v-2l1-1h2 2l2-1z" class="I"></path><path d="M306 285v2c-1 0-1 0-1 1l-1-3h2z" class="Q"></path><path d="M308 284l2 1 1 1-1 1c-1 0-2 1-3 1-1 1-3 2-4 3h0c0-1 0-1-1-1h0 0c1-1 2-2 3-2 0-1 0-1 1-1v-2l2-1z" class="l"></path><path d="M306 285l2-1c0 2 0 2-1 3h-1v-2z" class="Z"></path><path d="M285 227h3v1h2l1 1h2v1c1 0 1-1 3 0v-1l1 1c-2 1-2-1-3 2h0c-1 0-2 0-2 2l-2 1v-1l-1 1-1-1c-1 1-2 2-2 4-1 0-1 0-1 1v1l-7 11-1 2 1 1c-1 2-1 3-2 5s-1 3-2 5v1c0 1 1 1 1 2-1 1-2 3-2 4h0c0 1-1 1-1 2-2 4-4 8-7 12h-1l-3 7c-1 1 0 2-1 3l-2 4h0 0l-3 3-1-1c-1 1-3 4-5 4l-13 13-6 6c0 1 1 3 1 4-1 1-3 3-5 4v-1c-3 2-5 5-6 7l-3 3v-2c-2 2-3 4-5 5-1 2-3 3-3 5-1 1-3 2-3 4l-1 1v1c0 1-1 2-1 3l-5 8c0 1-1 3-1 4 0 0 0 1-1 1h-1l-13 37-1 4-3 7c-3 5-4 11-5 17l-1-1h-1l-3-1c0-2 1-3 1-5 1-1 1-1 1-2l-1-1v-1l2-6c1-1 1-2 2-4h0l9-28 1-4 9-21v-4c1 0 1-1 1-1 1-1 2-1 2-1 0-1 1-2 2-3-1-1-1-1-1-2l-1-1c1-2 2-3 2-4 2-4 5-8 8-11 1-3 5-5 5-8h0l-1-1c1-1 2-2 3-2 4-6 10-10 16-15 3-2 5-4 7-6 4-3 8-6 11-10h0c1 0 4-3 4-4v-1-6c-1-2-1-5 0-8 0-2 1-5 2-7 1-5 2-11 5-15l3-3c1-2 2-3 3-5 2 0 2-1 3-2v1l1-1c1-3 5-6 7-8l1-1 1-1 2-1c0-1 0-1 1-2h0c2-1 3-1 5-2z"></path><path d="M198 364c1 1 1 1 1 2s-1 3-1 4c0 0 0 1-1 1h-1c1-2 1-5 2-7z" class="H"></path><path d="M224 327h0c2-2 4-4 6-5-1 3-3 5-5 6h-1v-1zm2 4l4-7c0 1 1 3 1 4-1 1-3 3-5 4v-1z" class="S"></path><path d="M224 327h-1c1-2 11-12 13-13v1l-1 1h0 1l-6 6c-2 1-4 3-6 5h0z" class="H"></path><path d="M246 303c2-1 5-3 6-5l2-1h0c0 1-1 2-2 2v1c-2 1-4 3-5 5a57.31 57.31 0 0 0-11 11h-1 0l1-1v-1c3-4 6-7 10-11z" class="U"></path><path d="M198 364l4-9 2-2c1-2 11-16 14-16 0 1-1 1-1 2-2 2-3 4-5 5-1 2-3 3-3 5-1 1-3 2-3 4l-1 1v1c0 1-1 2-1 3l-5 8c0-1 0-1-1-2z" class="N"></path><path d="M206 342c-1 2-2 5-3 8 2-5 5-9 8-12l3-3h0l1 1c0 2-2 3-3 5l-7 9h0l-1-1c-1 1-2 3-3 5l-1-1h0c1-1 1-2 0-3 1 0 1-1 1-2 1-3 2-5 4-7l1 1z" class="G"></path><path d="M259 288l1 1v1l1 2c-1 1 0 2-1 3l-2 4h0 0l-3 3-1-1c-1 1-3 4-5 4h-2c1-2 3-4 5-5v-1c1 0 2-1 2-2h0l-2 1c-1 2-4 4-6 5h-2c2-3 6-5 8-8 0-1 0-1 1-1l1 2h2c0-1 0-2 1-2v-1l1-1c0-2 1-3 1-4z" class="h"></path><path d="M259 288l1 1v1l-2 4h-1v-1l1-1c0-2 1-3 1-4z" class="O"></path><path d="M254 301c1 0 1-1 2-1l1-1v-1-2h1c0-1 1-1 2-1l-2 4h0 0l-3 3-1-1z" class="I"></path><defs><linearGradient id="Af" x1="186.828" y1="369.518" x2="197.434" y2="378.128" xlink:href="#B"><stop offset="0" stop-color="#858384"></stop><stop offset="1" stop-color="#ababac"></stop></linearGradient></defs><path fill="url(#Af)" d="M200 350c1 1 1 2 0 3h0l1 1c1-2 2-4 3-5l1 1c-3 4-5 8-7 12-4 10-7 21-10 31l-1-2h-3-1c0 2 0 3-1 3 4-15 10-29 18-44z"></path><path d="M190 373h1c0 3-2 6-3 8h0 0c0-2 2-5 2-8z" class="M"></path><defs><linearGradient id="Ag" x1="170.774" y1="397.305" x2="185.569" y2="426.735" xlink:href="#B"><stop offset="0" stop-color="#adaaa8"></stop><stop offset="1" stop-color="#dadbde"></stop></linearGradient></defs><path fill="url(#Ag)" d="M182 394c1 0 1-1 1-3h1 3l1 2c-3 5-5 11-7 17l-5 12c-1 4-2 9-3 13h-1l-3-1c0-2 1-3 1-5 1-1 1-1 1-2 0-4 3-9 4-13l7-20z"></path><path d="M249 294c-4 3-7 6-11 9l-9 8c-1 1-3 2-4 3-2 1-4 3-6 5-3 3-6 6-7 9 4-3 7-7 11-10l6-6c1-1 3-3 5-3 2-1 6-5 9-7l3-3c2-2 4-5 7-6v1c-1 0-1 0-1 1-2 3-6 5-8 8-3 2-6 4-8 7-2 1-3 4-5 5-2 4-5 7-8 10-2 2-4 3-6 5-4 4-7 8-11 12l-1-1c-2 2-3 4-4 7 0 1 0 2-1 2-8 15-14 29-18 44l-7 20c-1 4-4 9-4 13l-1-1v-1l2-6c1-1 1-2 2-4h0l9-28 1-4 9-21v-4c1 0 1-1 1-1 1-1 2-1 2-1 0-1 1-2 2-3-1-1-1-1-1-2l-1-1c1-2 2-3 2-4 2-4 5-8 8-11 1-3 5-5 5-8h0l-1-1c1-1 2-2 3-2 4-6 10-10 16-15 3-2 5-4 7-6 4-3 8-6 11-10h0l2 1z" class="a"></path><path d="M200 348v-1c3-5 5-10 9-15l3-3c4-3 7-6 11-10 2-1 4-3 6-5s4-4 6-5c-3 5-8 9-11 13-3 2-5 5-7 7-3 3-6 6-9 8l-2 2s-1 1-1 2c-2 2-3 4-4 7 0 1 0 2-1 2-8 15-14 29-18 44l-7 20c-1 4-4 9-4 13l-1-1v-1l2-6c1-1 1-2 2-4h0l9-28 1-4 9-21v-4c1 0 1-1 1-1 1-1 2-1 2-1 0-1 1-2 2-3-1-1-1-1-1-2l-1-1c1-2 2-3 2-4 1 1 1 1 1 2h1z"></path><path d="M193 358c1 0 1-1 1-1 1-1 2-1 2-1l-3 6v-4zm5-12c1 1 1 1 1 2h1 0 1l-3 5c-1-1-1-1-1-2l-1-1c1-2 2-3 2-4z" class="H"></path><path d="M206 339c0-3 3-4 4-6s3-3 4-4c3-3 6-6 10-7-3 2-5 5-7 7-3 3-6 6-9 8l-2 2z" class="O"></path><path d="M285 227h3v1h2l1 1h2v1c1 0 1-1 3 0v-1l1 1c-2 1-2-1-3 2h0c-1 0-2 0-2 2l-2 1v-1l-1 1-1-1c-1 1-2 2-2 4-1 0-1 0-1 1v1l-7 11-1 2 1 1c-1 2-1 3-2 5s-1 3-2 5v1c0 1 1 1 1 2-1 1-2 3-2 4h0c0 1-1 1-1 2-2 4-4 8-7 12h-1l-3 7-1-2v-1l-1-1c0 1-1 2-1 4l-1 1v1c-1 0-1 1-1 2h-2l-1-2v-1c-3 1-5 4-7 6l-3 3c-3 2-7 6-9 7-2 0-4 2-5 3l-6 6c-4 3-7 7-11 10 1-3 4-6 7-9 2-2 4-4 6-5 1-1 3-2 4-3l9-8c4-3 7-6 11-9l-2-1c1 0 4-3 4-4v-1-6c-1-2-1-5 0-8 0-2 1-5 2-7 1-5 2-11 5-15l3-3c1-2 2-3 3-5 2 0 2-1 3-2v1l1-1c1-3 5-6 7-8l1-1 1-1 2-1c0-1 0-1 1-2h0c2-1 3-1 5-2z"></path><path d="M253 285h1v-2 6 1h-1v-5z" class="O"></path><path d="M255 281l1 1c-1 2-1 4-1 6l-1 1v-6c0-1 0-2 1-2z" class="V"></path><path d="M274 248c0 1 0 1 1 2l-4 5-1-1c1-2 2-4 4-6h0z" class="a"></path><path d="M258 292v-2c-1-2-1-3 0-4 0-2 1-2 2-3 0 2 0 3-1 5 0 1-1 2-1 4z" class="h"></path><path d="M256 282c0-2 1-3 2-4h0 1c-1 2-2 4-2 6-1 1-1 3-1 5l-1 1v-2c0-2 0-4 1-6z" class="Q"></path><path d="M273 244h0v1 1c1 0 0 0 1 1l-1 1c-2 1-5 6-7 9h0l6-13h1z" class="S"></path><path d="M271 255h-1c-3 5-5 10-6 15-1 2-1 5-3 6h0l3-12c0-1 1-3 2-4 1-2 3-4 4-6l1 1zm-18 12l1 1c-1 3-2 7-2 11-1 5 1 10-3 15l-2-1c1 0 4-3 4-4v-1-6c-1-2-1-5 0-8 0-2 1-5 2-7z" class="j"></path><path d="M253 267c1-5 2-11 5-15l3-3c1-2 2-3 3-5 2 0 2-1 3-2v1s-1 1-2 1c-1 1-2 3-3 5l-3 3c-3 5-3 10-5 16l-1-1z" class="W"></path><path d="M258 278l3-12c1-3 3-7 3-9v-1c-1-1 2-7 3-8s2-2 3-4c0 1-1 1 0 2h0c-1 2-2 5-3 6s-2 3-3 4l1 1-6 21h-1 0z" class="c"></path><path d="M253 285c0-8 1-14 3-21l3-9c1-1 1-3 2-4h1l-5 16c-1 3-3 9-2 12v2c-1 0-1 1-1 2v2h-1z" class="f"></path><path d="M274 252l2-3c0 1 1 1 1 2h1l-1 2 1 1c-1 2-1 3-2 5s-1 3-2 5v1c0 1 1 1 1 2-1 1-2 3-2 4h0c0 1-1 1-1 2-2 4-4 8-7 12h-1l-3 7-1-2v-1l-1-1c1-2 1-3 1-5s2-5 3-7c3-8 5-18 11-24z"></path><path d="M274 252l2-3c0 1 1 1 1 2l-2 4h-1l1-2-1-1z" class="H"></path><path d="M270 271h0c0 1-1 2-2 3-2 3-3 6-5 9 0 1-1 1-1 2l1-4c1-3 3-7 5-8 1 0 1-1 2-2zm-7 5h1c-1 3-3 7-2 10l-2 3-1-1c1-2 1-3 1-5s2-5 3-7z" class="l"></path><path d="M271 272l1 1c-2 4-4 8-7 12h-1l7-13z" class="W"></path><path d="M274 255h0 1c-1 4-3 8-5 11-1 2-1 4-2 6h-1c1-4 2-7 4-11l3-6z" class="S"></path><path d="M263 276c3-8 5-18 11-24l1 1-1 2h0-1c-2 4-5 8-6 13-1 3-2 5-3 8h-1z" class="Y"></path><path d="M270 271l7-18 1 1c-1 2-1 3-2 5s-1 3-2 5v1c0 1 1 1 1 2-1 1-2 3-2 4h0c0 1-1 1-1 2l-1-1 1-2-2 1h0z" class="j"></path><path d="M274 265c0 1 1 1 1 2-1 1-2 3-2 4h0c0 1-1 1-1 2l-1-1 1-2c0-2 1-3 2-5z" class="I"></path><path d="M285 227h3v1h2l1 1h2v1c1 0 1-1 3 0v-1l1 1c-2 1-2-1-3 2h0c-1 0-2 0-2 2l-2 1v-1l-1 1-1-1c-1 1-2 2-2 4-1 0-1 0-1 1v1l-7 11h-1c0-1-1-1-1-2l1-1c-1 0-2 1-2 2-1-1-1-1-1-2h0-1l1-1c-1-1 0-1-1-1v-1-1h0-1v-1l-2 3h0c-1-1 0-1 0-2l3-3-1-1c-1 2-3 4-5 5v-1l2-2h-1c1-3 5-6 7-8l1-1 1-1 2-1c0-1 0-1 1-2h0c2-1 3-1 5-2z" class="a"></path><path d="M286 231l1-1c0 1-1 2-2 3v1c-1 0-1 0-2 1 0-1 0-1-1-1 2-1 3-2 4-3z" class="H"></path><path d="M275 242v1c0 1-1 1-1 2 2 0 2-1 2-2h0c1-1 2-1 3-1l-2 2-3 4h0-1l1-1c-1-1 0-1-1-1v-1-1h0c0-1 1-1 2-2z" class="T"></path><path d="M272 243c1-2 3-4 4-6h0 1v2l1-1s0-1 1-2h2l-4 4c0 1 0 1-1 1h0l-1 1c-1 1-2 1-2 2h-1v-1z" class="W"></path><path d="M276 233c1 1 6-2 8-2 0-1 1 0 2 0-1 1-2 2-4 3-1 0-1 0-2 1h-2-1c-1 2-3 3-5 4-1 1-2 3-3 3h-1c1-3 5-6 7-8l1-1z" class="f"></path><path d="M285 227h3v1h2l1 1-1 1h-3l-1 1c-1 0-2-1-2 0-2 0-7 3-8 2l1-1 2-1c0-1 0-1 1-2h0c2-1 3-1 5-2z" class="Q"></path><path d="M280 229h0l1 1h0c1-1 2-1 2 0-1 0-3 1-4 1 0-1 0-1 1-2z" class="Y"></path><path d="M280 229c2-1 3-1 5-2v1h2c0 1 0 1-1 1l-3 1c0-1-1-1-2 0h0l-1-1z" class="f"></path><path d="M291 229h2v1c1 0 1-1 3 0v-1l1 1c-2 1-2-1-3 2h0c-1 0-2 0-2 2l-2 1v-1l-1 1-1-1c-1 1-2 2-2 4-1 0-1 0-1 1v1l-7 11h-1c0-1-1-1-1-2l1-1c-1 0-2 1-2 2-1-1-1-1-1-2l3-4 2-2v-1l3-4h2c1-1 1-1 1-3 0-1 4-3 5-4l1-1z" class="M"></path><path d="M296 229l1 1c-2 1-2-1-3 2h0c-1 0-2 0-2 2l-2 1v-1l-1 1-1-1c2-2 3-3 5-4 1 0 1-1 3 0v-1z" class="P"></path><path d="M279 241l1 2v-1h0l1 1h0c-1 1-1 1-1 2l-3 3h0c-1 0-2 1-2 2-1-1-1-1-1-2l3-4 2-2v-1z" class="N"></path><path d="M264 196c1 0 3 0 4 1 5 2 10 7 14 10l3 1h1c3 1 9 0 13-1h3c3 0 7 0 10-1h1l7 1v1h0 1c3 1 6 1 8 2 3 1 5 1 7 3h-3c0 1 1 2 2 3 2 2 3 3 5 4v-1h1c1 0 1 0 2 1 0 0 0 1-1 1v1c-1 2-2 2-2 3s1 1 1 1h0l2 2 2-2v-1h-1 0l1-2v-1h0c1-1 2-1 2-1 1 0 2 1 2 2l3 3 1 1-1 2h0c1 2 3 3 4 4v1c1 1 1 1 1 2l-3-3v2c1 0 0 0 1 1v2h1c0 1 1 1 1 2h0c-1 0-2 1-2 1h0v1l-2 1c-2 0-5 0-7 1-2 0-5 1-7 3s-3 5-3 8 1 6 1 8l1 1c0 2 1 5 0 7l2 1c-1 1-2 3-3 4-2 2-3 3-5 4h-2v3l-3 3-2 1-4 1h-2-3-3c0-1 0-1 1-1v-1-1l-1-1c0-1 0-1-1-2l-1-1v-2h0v-1l-2-1 1-1h-1c-1 0-2 0-2-1h0-1l-2 1v-1l1-1h-1l-2 2v-1c0-2 0-3 1-5h-1 0v-1-1-1h0c1-1 1-2 2-3h-1l-1-1h-1v1c-1 0-1 0-2-1h0c-1-1-1-1-2-1s-1-1-1-1c-1 0-1 0-2 1l-1 1c-1-1-1-1-1-2h0v-1c-1 0-1 1-2 1h-1v-1c-1-2-2-2-2-3l1-1c0-1 1-2 1-3-1-1-1-2 0-3l-2 2h0v2l-1 1h0c0-1 0-1-1-2h0s0 1-1 1l-1 2v1 1c0 1 0 1-1 1-2 0-2 2-3 3l-1-1c-2 2-3 4-3 6 0-1-1-1-1-2v-1c1-2 1-3 2-5s1-3 2-5l-1-1 1-2 7-11v-1c0-1 0-1 1-1 0-2 1-3 2-4l1 1 1-1v1l2-1c0-2 1-2 2-2h0c1-3 1-1 3-2l-1-1-4-4-9-10c-5-6-10-14-17-17 0-1-2-1-2-2z" class="e"></path><path d="M348 241h1v1h-1v-1z" class="X"></path><path d="M303 237c1 0 1 0 2 1l-1 1h-1-1l1-2z" class="C"></path><path d="M346 235h1v2h-1s-1 0-1-1l1-1z" class="B"></path><path d="M299 257h1 0c0 2 0 3-1 4h-1c1-2 1-3 1-4z" class="C"></path><path d="M311 259h1v1h0 4-8l1-1h2 0z" class="V"></path><path d="M336 227c1 0 2-1 3-1v1c0 1 1 1 1 2v1h-1s-1-1-1-2c-1 0-1 0-2-1z" class="B"></path><path d="M313 239h1v2h1v-1l1 1v1c0 1 0 1-1 1l-2-1v-3z"></path><path d="M319 276c0 1 1 1 1 2l1 1c1-1 1-1 1-2l1 1c-1 0-1 1-1 1v1h-1-1-2-1v-1c1-1 1-2 2-3z" class="P"></path><path d="M338 228c-2 2-1 5-2 6h0c-1 0-2-4-2-4 0-1 1-1 1-2l1-1c1 1 1 1 2 1z" class="X"></path><path d="M325 287v-1c0-1-1-2 0-3 2-1 3 0 5 0l-3 3-2 1z" class="b"></path><path d="M338 271l2 1c-1 1-2 3-3 4h-2c1-1 1-1 1-3-1-1-7 0-9 0v-1h-3 10 3c1 0 1 0 1-1z" class="V"></path><path d="M339 244v-1l1-2-1-1c0-1 1-1 1-2h1v2 2h-1v1 1l3-1c1-1 1-1 2-1s1 1 1 2c-2 0-5 1-7 3v-1-1-1z" class="L"></path><path d="M339 244h1v1h-1v-1zm-31-32c4 2 8 4 11 7-1 0-2-1-3-2l-5-2c2 2 8 5 9 8-3-2-5-5-8-7-1-1-3-2-5-3l1-1z" class="K"></path><path d="M303 254v1c1 0 2 0 2-1l1 1 2 2s-1 0-1 1c1 1 3 1 4 1h0-2l-1 1h-1c-2-1-5-2-6-4l2-2z" class="I"></path><path d="M303 255c1 0 2 0 2-1l1 1 2 2s-1 0-1 1c-2-1-3-2-4-3z" class="K"></path><path d="M329 210c3 1 5 1 7 3h-3c0 1 1 2 2 3 2 2 3 3 5 4l-1 1-4-4c-2-2-5-5-8-5l-3-1v-1l1 1c1-1 1-1 2-1h2z" class="H"></path><path d="M345 222c1-1 2-1 2-1 1 0 2 1 2 2l3 3 1 1-1 2h0 0v2 1h0c-2-1-1-2-1-4-1 0-2-1-2-2v-2h-1c0 1-1 1-1 2-1 0-1-1-1-1l-1 1v-1h-1 0l1-2v-1h0zm-63-15l3 1c2 2 4 2 6 2v1l2 1h0-2v2c0 1-1 1-2 2l-2-2c0-1-1-1-1-2-1-1-2-1-2-2h-1c-1-1-1-2-1-3z" class="b"></path><path d="M291 214h-2 0l-2-2c1 0 2-1 3 0h3 0-2v2z" class="X"></path><path d="M321 208c3 1 6 1 8 2h-2c-1 0-1 0-2 1l-1-1v1l3 1c2 2 4 4 7 6h1c0 1-1 1-1 1 0 1 1 2 1 3h0l1 1v1h0c-2-2-3-5-5-5-1 0-1 0 0 1h-1c-1 0-1 0-1-1-1 0-1-1-1-2-1-2-3-3-5-4l2-1c-1 0-3-1-4-2h1c-1 0-1-1-1-1h-1l1-1z" class="K"></path><path d="M321 208c3 1 6 1 8 2h-2c-1 0-1 0-2 1l-1-1v1l3 1c2 2 4 4 7 6-3 0-7-4-9-6-1 0-3-1-4-2h1c-1 0-1-1-1-1h-1l1-1z" class="U"></path><path d="M294 232c1 1 2 0 2 1s-1 2-1 2l-1 1c0 1-2 3-2 5 0 0 1 1 2 1 0 1 0 1-1 2h-1v-2h-1c-1 2-4 5-3 8h1l-2 2h0v2l-1 1h0c0-1 0-1-1-2h0l1-2h-1l-1 1h-1v-1c1 0 1 0 1-1 1 0 2-1 2-2s1-2 1-3c1-1 2-2 2-3 1-1 0-1 0-2v-1-2l1-2 2-1c0-2 1-2 2-2h0z" class="L"></path><path d="M289 242h1c0 1 0 2-1 3h0-2c1-1 2-2 2-3z" class="X"></path><path d="M292 234c0-2 1-2 2-2 0 1 0 2-1 3-1 0-1-1-1-1z" class="K"></path><path d="M320 212l8 5c0 1 0 2 1 2h-1c-2-2-4-4-6-4 0 0 1 0 1 1l4 4v1c1 1 1 1 1 2s0 1 1 1c0 1 1 2 1 3h0c-1 0-1-1-1-1l-2-2c0 2 2 3 2 4-1 0-1 0-2-1 0-1 0-2-1-3h-1 1v1c0 1 1 2 1 3h-1v-1c-1-1-2-2-2-3v-1l-2-2c-2-2-4-4-7-6 0-1 0-1-1-2h2c4 2 7 5 10 8h0c-1-3-4-5-7-7 0-1 1-1 1-2z" class="L"></path><path d="M312 206h1l7 1v1h0 1l-1 1h1s0 1 1 1h-1c1 1 3 2 4 2l-2 1c2 1 4 2 5 4l-8-5c0 1-1 1-1 2 3 2 6 4 7 7h0c-3-3-6-6-10-8h-2c1 1 1 1 1 2 3 2 5 4 7 6l2 2v1c-1-2-3-3-5-5-3-3-7-5-11-7-1-1-2-2-4-3-1-1-2-1-2-2 3 0 7 0 10-1z" class="I"></path><path d="M323 213c-2-1-5-3-6-5 1 1 3 1 4 2s3 2 4 2l-2 1z" class="C"></path><path d="M319 214h0c-3-2-6-4-7-6 3 1 5 3 8 4 0 1-1 1-1 2z" class="B"></path><path d="M315 215l-8-5h0 3 1l5 3h-2c1 1 1 1 1 2zm-26 20l1-1v1l-1 2v2 1c0 1 1 1 0 2 0 1-1 2-2 3 0 1-1 2-1 3s-1 2-2 2c0 1 0 1-1 1v1h1l1-1h1l-1 2s0 1-1 1l-1 2v1 1c0 1 0 1-1 1-2 0-2 2-3 3l-1-1c-2 2-3 4-3 6 0-1-1-1-1-2v-1c1-2 1-3 2-5s1-3 2-5l-1-1 1-2 7-11v-1c0-1 0-1 1-1 0-2 1-3 2-4l1 1z" class="C"></path><path d="M278 254c1-1 1-3 2-5h1c-1 2-1 3-2 5 0 1 0 2-1 3 0 1-1 2-1 4 1-2 3-4 4-5-1 2-2 4-3 5-2 2-3 4-3 6 0-1-1-1-1-2v-1c1-2 1-3 2-5s1-3 2-5z" class="Z"></path><path d="M289 235l1-1v1l-1 2v2 1c0 1 1 1 0 2 0 1-1 2-2 3 0 1-1 2-1 3l-2-1 1-1c1-2 3-4 3-5v-1c-2 2-3 4-4 6 0 0-1 0-1 1h-1v1c0 1-1 1-1 1h-1c-1 2-1 4-2 5l-1-1 1-2 7-11v-1c0-1 0-1 1-1 0-2 1-3 2-4l1 1z" class="B"></path><path d="M288 234l1 1c-1 2-1 2-2 3h-1c0-2 1-3 2-4z" class="K"></path><path d="M304 264l1 1h0 1v1h0c0 1-1 2 0 3 0 1 0 1 1 1h1l1 1v1c0-1 1-1 1-1h2v1h0l2-2v1 5h0c1-1 2-3 2-4h1v2h1c0 1 0 1 1 2-1 1-1 2-2 3v1h1 2 1-1-1 0c-1 1-2 1-3 2h-2c-1 1-1 1 0 2h3 0c1 0 1 0 1-1h4c1 1 1 1 1 2h-1 0c-1-1-2-1-3-1-1 2-1 2 0 4h2-2-3-3c0-1 0-1 1-1v-1-1l-1-1c0-1 0-1-1-2l-1-1v-2h0v-1l-2-1 1-1h-1c-1 0-2 0-2-1h0-1l-2 1v-1l1-1h-1l-2 2v-1c0-2 0-3 1-5h-1 0v-1-1-1h0c1-1 1-2 2-3z" class="J"></path><path d="M314 285h2v1 1h1c0-1 0-2 1-3h1c-1 2-1 2 0 4h2-2-3-3c0-1 0-1 1-1v-1-1z" class="P"></path><path d="M302 270l1-1h0l1 1h1 1c0 1 0 1 1 1h0 1c1 1 0 1 1 2 1 0 1-2 2-1h0c-1 2-1 2-1 4h-1c-1 0-2 0-2-1h0-1l-2 1v-1l1-1h-1l-2 2v-1c0-2 0-3 1-5h-1z" class="C"></path><path d="M299 207h3c0 1 1 1 2 2 2 1 3 2 4 3l-1 1c2 1 4 2 5 3 3 2 5 5 8 7h0v1c-2 1-1 2-2 3l-2-3c-2-1 0 1-1 0l-1-1-1-1h-1l-1-1h-1c0-1-1 0-1-1h-2c-2-1-3-2-4-3-1 0-1 0-2-1h0l-3-1-2-1-3-2h0 0l-2-1v-1c-2 0-4 0-6-2h1c3 1 9 0 13-1z" class="c"></path><path d="M299 207h3c0 1 1 1 2 2 2 1 3 2 4 3l-1 1c-3-1-5-2-7-3v-1c-1-1-1-1-1-2z" class="R"></path><path d="M299 207h3c0 1 1 1 2 2v1c-2 0-3 0-4-1s-1-1-1-2z" class="l"></path><path d="M291 211c2-1 3 0 4 0h1l14 8 1-1c-1-1-3-1-4-2-3-2-7-4-10-5h0c2 0 3 1 5 1 2 1 5 2 7 3 4 2 7 6 10 8h1v1c-2 1-1 2-2 3l-2-3c-2-1 0 1-1 0l-1-1-1-1h-1l-1-1h-1c0-1-1 0-1-1h-2c-2-1-3-2-4-3-1 0-1 0-2-1h0l-3-1-2-1-3-2h0 0l-2-1z" class="K"></path><path d="M301 256c-1-2-2-4-3-5-2-1-5-1-8-3 8-2 17-5 25-1 3 1 6 5 7 8s1 5 1 8c0 2-2 7 0 9h1 0 3v1c2 0 8-1 9 0 0 2 0 2-1 3h2c-2 2-3 3-5 4h-2c-1 0-2-2-4-3l-3-3c-2-3-1-6-1-9-1-2-2-4-4-6-1 0-1 0-2 1h-4 0v-1h-1c-1 0-3 0-4-1 0-1 1-1 1-1l-2-2-1-1c0 1-1 1-2 1v-1l-2 2z"></path><path d="M314 250c0 1 1 1 1 1 1 1 1 3 0 4 0 2-1 3-3 3v1h-1c-1 0-3 0-4-1 0-1 1-1 1-1h3c1 0 2-1 3-2v-5z" class="L"></path><path d="M304 248h1c0 1 0 1 1 2h2-1c-1 1-1 1-1 2s0 1-1 2h0c0 1-1 1-2 1v-1l-1-3c-1-1-1-1 0-3h2z" class="B"></path><path d="M304 248h1c0 1 0 1 1 2h2-1c-1 1-1 1-1 2s0 1-1 2c-1-1-1-3-1-4s1-1 0-2z" class="V"></path><path d="M308 250h2c0 1 0 1 1 2v1c1 0 1-1 2-1h0v-1l1-1v5c-1 1-2 2-3 2h-3l-2-2-1-1h0c1-1 1-1 1-2s0-1 1-2h1z" class="h"></path><path d="M306 252c0-1 0-1 1-2h1c1 2 1 3 2 5h-1-3l-1-1h0c1-1 1-1 1-2z" class="F"></path><path d="M306 252l3 3h-3l-1-1h0c1-1 1-1 1-2z" class="d"></path><path d="M327 273c2 0 8-1 9 0 0 2 0 2-1 3l-2 2h-1c0 1-1 1-1 1-3-2-5-3-6-6h0 2z" class="P"></path><path d="M336 273c0 2 0 2-1 3l-2 2v-3c1 0 1-1 1-1v-1h2z" class="C"></path><path d="M332 278h-1c-1-1-1-1-1-2l2-2c1 0 1 1 1 1v3h-1z" class="J"></path></svg>