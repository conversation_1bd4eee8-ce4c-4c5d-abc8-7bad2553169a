<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="55 46 552 620"><!--oldViewBox="0 0 652 752"--><style>.B{fill:#9d896a}.C{fill:#393127}.D{fill:#a08e6e}.E{fill:#161310}.F{fill:#1f1a17}.G{fill:#a29278}.H{fill:#3d342a}.I{fill:#907e62}.J{fill:#26201b}.K{fill:#2b241e}.L{fill:#c7b184}.M{fill:#3a3228}.N{fill:#b9a37c}.O{fill:#84745e}.P{fill:#685a47}.Q{fill:#332d26}.R{fill:#b09975}.S{fill:#736451}.T{fill:#aa9572}.U{fill:#bda67c}.V{fill:#d1b989}.W{fill:#7d6c57}.X{fill:#43382d}.Y{fill:#897a60}.Z{fill:#504538}.a{fill:#b69e75}.b{fill:#5f5343}.c{fill:#f5e4b8}.d{fill:#524231}.e{fill:#332921}.f{fill:#241e19}.g{fill:#5d4f3e}.h{fill:#ebd8b3}.i{fill:#0b0a07}.j{fill:#f2ead0}.k{fill:#1b1613}.l{fill:#c9b284}.m{fill:#776b59}.n{fill:#f6f0da}.o{fill:#cdc3aa}.p{fill:#665c4e}.q{fill:#fefaee}</style><path d="M599 513l2-1c0 3 1 5 1 7l-1-2h-1c0-1-1-3-1-4z" class="W"></path><path d="M58 320c1 1 0 2 1 3l2 1-1 1c-1 2-1 3-1 5-1-3-1-7-1-10zm539 185c0-1 1-1 2-2 0 3 1 7 2 9l-2 1-2-8z" class="Y"></path><path d="M212 632c2-1 4-1 5-1h5c-2 1-3 1-4 1h-1l1 1h-1l2 2c-3 0-6-1-9-3h2z" class="D"></path><path d="M60 325l1 2h0c-1 2 0 4-1 6v1 5h-1v-9c0-2 0-3 1-5z" class="O"></path><path d="M420 540v1c0 2-1 4-3 6l-2 2h1 1c1-1 2-2 3-4 1-1 1-2 3-2-2 2-4 5-6 7h12 0-9c-1 0-3 0-5 1-1 0-1-1-2-1 3-3 5-6 7-10z" class="c"></path><path d="M477 584h1l-12 10h-1c3-2 5-5 7-8 2 0 3-1 5-2z" class="E"></path><path d="M212 474c1 3 2 7 3 10 0 1 2 5 2 6-1 1-1 1-1 2v1c0-2-1-3-1-4-1-2-2-4-2-6l-1-1c0-2-1-3-2-4 1-1 1-2 2-4z" class="P"></path><path d="M77 477c2-1 3-1 4 0h0c1 0 1 1 2 1 2 1 4-1 7 1-6 1-13 0-19 0-1 0-5 1-6 0v-1h3 3c2-1 4-1 6-1z" class="d"></path><path d="M176 105l25-12-1 3c-3 1-5 2-7 3l-9 5c-2 0-3 0-4 1h-4z" class="g"></path><path d="M330 350l3 3 1 1 1 1-4 3h-1c-2 2-5 3-7 4v1h-1l-1 1c-1 1-2 2-3 2h0 0l5-5h1c0-1 1-1 1-1 2-1 3-2 5-3h0v-1c-1 0-2-1-3-2-1 0-2 1-4 1l-1 1c-3 1-4 3-6 4-3 2-6 4-9 7v-1l2-2s1 0 1-1c1-1 0 0 1 0 0-1 1-1 1-1l3-3h1l1-1c1-1 2-2 3-2l1-1h1c1-1 1 0 1-1 1-1 3-2 5-2 1-1 1-1 2-1v-1z" class="h"></path><path d="M329 353h2s1 0 1 1l-1 1h-2v-1-1z" class="c"></path><path d="M59 287v8c0 1 0 1 1 1v3 2 1 2 4c1 1 0 1 0 2s2 0 1 2c0 1-1 2-1 2l1 1v3 3h1v2c0 1-1 0 0 1v1l-1-1-2-1c-1-1 0-2-1-3v-19c0-5 0-9 1-14z" class="W"></path><path d="M272 549l17 5c3 1 5 0 7 1l-2 1h-1-1c0 1 0 2-1 3 0-1-1-2-1-2-2-1-4-1-5-2l-2 1c-1 0-3 1-4 0h-1c-2-1-2-1-3-1-2 0-2 0-3 1h0v-4l-2-1 2-2z" class="H"></path><path d="M272 552c2 1 4 2 6 2v1h7l-2 1c-1 0-3 1-4 0h-1c-2-1-2-1-3-1-2 0-2 0-3 1h0v-4z" class="D"></path><path d="M431 531v7c0 4 0 8-1 11 0 0-1 0-1 1h-12c2-2 4-5 6-7 1-1 1-3 2-4l6-8z"></path><path d="M176 105h4c1-1 2-1 4-1-3 2-6 3-9 5-4 1-7 3-10 4-7 4-13 9-19 13-11 8-21 17-30 27-1 1-4 3-5 5v-1-1c1-2 4-5 6-7 9-10 20-18 31-26 9-7 18-13 28-18z" class="d"></path><path d="M518 216l1-6h0l11 36h-11v-1c-1-1-1-2-1-4 0 1-1 1-1 2h0 0-1c0-1 0-1-1-2h0c1-1 2-1 3-1h0c1-1 0-3 0-4 1-1 0-3 0-4v-7-9z"></path><path d="M518 225c1 3 1 14 0 16 0 1-1 1-1 2h0 0-1c0-1 0-1-1-2h0c1-1 2-1 3-1h0c1-1 0-3 0-4 1-1 0-3 0-4v-7z" class="Z"></path><path d="M571 455l1-1c1 1 2 2 3 4 7 9 12 19 17 29-2 1-7 1-9 1h-6l-1 4-1 1c-1 0-2 1-3 0h0v6h-1l-1-33c0 1 0 2 1 2v1-14z" class="E"></path><path d="M572 472h0c1 6 0 11 0 17h2c1-1 2-1 3-1l-1 4-1 1c-1 0-2 1-3 0h0v-21z" class="C"></path><path d="M571 455c1 1 1 4 1 6v11 21 6h-1l-1-33c0 1 0 2 1 2v1-14z" class="G"></path><path d="M502 549c1 0 3 1 4 3 0 2-12 17-14 19-3 3-6 7-9 9l-2 2 1-18c-1-4-1-9 0-13v1-3h-1 7 9c1 0 3 1 5 0z"></path><path d="M502 549c1 0 3 1 4 3h-7c-5-1-9-1-14-1 2 0 3 0 5-1h-3 0 3l-2-1h0 9c1 0 3 1 5 0z" class="B"></path><path d="M481 549h7 0l2 1h-3 0 3c-2 1-3 1-5 1h-2c-1 2-1 29 0 29l-2 2 1-18c-1-4-1-9 0-13v1-3h-1z" class="a"></path><path fill="#e31d12" d="M112 526l1 1h-1l-1 20c0 2 1 6 0 8s0 5 0 7c-8-8-14-17-20-26-2-3-4-6-5-9h0 18c2 0 6 0 8-1z"></path><defs><linearGradient id="A" x1="86.76" y1="470.015" x2="87.234" y2="479.533" xlink:href="#B"><stop offset="0" stop-color="#100e0b"></stop><stop offset="1" stop-color="#2d2622"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M73 466h34 3c0 1 0 1 1 1 0 3 0 5-1 7v5s0 1-1 1h0l-19-1c-3-2-5 0-7-1-1 0-1-1-2-1h0c-1-1-2-1-4 0-2 0-4 0-6 1h-3-3l-1-2-1-4-1-6c2 0 4 1 5 0h2 0 4z"></path><path d="M109 475c0 1 0 1-1 2h-2c0-1 0-1 1-2h1 1z" class="C"></path><path d="M102 474c1 0 2-1 2-2h0 1 1 2c1 1 1 2 1 3h-1-1c-1 1-1 1-1 2-1 1-1 1-2 1h0 0c0-2 0-3-1-4h-1z" class="d"></path><path d="M105 474l1-1c1 1 2 1 2 2h-1-2v-1z" class="g"></path><path d="M102 474c1 0 2-1 2-2h0 1 1v1h-2v1h1v1h2c-1 1-1 1-1 2-1 1-1 1-2 1h0 0c0-2 0-3-1-4h-1z" class="b"></path><path d="M64 476c1 0 1 1 2 1h1 4 0l2-1c0-1 1-1 2-2h4c1 1 4 1 6 1-1 0-1 1-2 0-2 0-4 0-5 1l-1 1c-2 0-4 0-6 1h-3-3l-1-2z" class="J"></path><path d="M67 466h2c0 2 1 3 3 4l-1 1c-3-1-5 0-7 0 0 0 0 1-1 1l-1-6c2 0 4 1 5 0z" class="d"></path><path d="M78 469h3c1-1 2-2 3-2 2 1 6 0 8 2h8c-3 1-6 0-8 2-1 0-1-1-2-1h-2c-1 0-1 0-2 1h0-3-1-2 0-5-1c-1 0-1 0-2-1 1 0 2 0 3-1h2c0-1 0 0 1 0z" class="X"></path><path d="M78 469h3c1-1 2-2 3-2 2 1 6 0 8 2-2-1-6-1-7-1-1 1-2 1-2 2-1 0-2 0-2-1-1 1-1 1-2 1l-1-1z" class="g"></path><path d="M93 475l2-1v-2h1l1 1c1 0 1-1 2-1 1-1 0 0 1 0 2 0 3-1 4-1s1 1 1 1h-1 0c0 1-1 2-2 2h1c1 1 1 2 1 4h0 0-2-3-3-1c-1-1-1-1-1-2l-1-1z" class="M"></path><path d="M94 476c2-1 3-1 4-2h0 3 0c-1 1 0 1-1 1s-2 1-3 3h-1-1c-1-1-1-1-1-2z" class="b"></path><path d="M102 474h1c1 1 1 2 1 4h0 0-2-3-3 1c1-2 2-3 3-3s0 0 1-1h0 1z" class="S"></path><path d="M90 474l1 1h1v-1h1v1l1 1c0 1 0 1 1 2h1 3 3c1 2 4 1 7 2l-19-1c-3-2-5 0-7-1-1 0-1-1-2-1h0c-1-1-2-1-4 0l1-1c1-1 3-1 5-1 1 1 1 0 2 0v-1h2s1 1 2 0h0 1z" class="Z"></path><path d="M85 475v-1h2s1 1 2 0h0 1l-1 1v1c-3 0-5 1-8 1h0c-1-1-2-1-4 0l1-1c1-1 3-1 5-1 1 1 1 0 2 0z" class="Q"></path><path d="M73 466h34 3c0 1 0 1 1 1 0 3 0 5-1 7v-6h-1s0 1-1 1-2-1-3 0h-1v-1c-2 0-2 1-2 1-1 0-1-1-2 0h-8c-2-2-6-1-8-2-1 0-2 1-3 2h-3c-1 0-1-1-1 0h-2c-1 1-2 1-3 1-2-1-3-2-3-4h0 4z" class="S"></path><path d="M69 466h0l1 1h1 2c0 1 1 1 1 2h1c-1 1-2 1-3 1-2-1-3-2-3-4z" class="g"></path><path d="M592 487c3 4 6 11 7 16-1 1-2 1-2 2l2 8c0 1 1 3 1 4h-20-8 0c-1-1 0-4 0-6v-12-6h0c1 1 2 0 3 0l1-1 1-4h6c2 0 7 0 9-1z" class="G"></path><path d="M578 512h2v1l-1 1-1-1v-1z" class="R"></path><path d="M593 503v-1c1 1 0 1 1 1h2c-1 2 0 3-1 5h0l-1-1v-2h-1c-1 1-1 2-1 3-1-2-1-3-1-4h-2l1-1h2 1z" class="B"></path><path d="M592 487c3 4 6 11 7 16-1 1-2 1-2 2-1-1-1-1-1-2s-2-3-2-5v-1h-1c-1-2-2-7-3-8h-2c-1 0-5 2-6 3v1l-2-1c-1 1-2 1-3 2h0 0v1h-1v1h1v1h0-2v1h1l-1 1v1 1c0 1 0 3-1 3 0 1 0 1-1 1v5l-1 1v-12-6h0c1 1 2 0 3 0l1-1 1-4h6c2 0 7 0 9-1z" class="D"></path><path d="M577 488h6c-2 1-4 2-5 4-1 1-1 1-2 1v-1l1-4z" class="I"></path><path d="M576 492v1c0 2-1 2-2 3h-1v1c1 1 1 1 0 2l1 1 1-1v2c0 1 0 3-1 3 0 1 0 1-1 1v5l-1 1v-12-6h0c1 1 2 0 3 0l1-1z" class="O"></path><path d="M112 490v8 20c0 2-1 6 0 8h0c-2 1-6 1-8 1H86h0c0-1 0-1-1-2h0c-3-3-5-8-6-11-2-2-2-4-3-6l-5-10h8 12 19c0 1 1 1 1 1 1-2 0-5 1-8v-1z"></path><path d="M112 490v8 20c0 2-1 6 0 8h0c-2 1-6 1-8 1H86h0c0-1 0-1-1-2h0c-3-3-5-8-6-11h31v-15l-31-1h0 12 19c0 1 1 1 1 1 1-2 0-5 1-8v-1z" class="l"></path><path d="M85 525h9c-2 1-3 1-4 1h0c-1 0-3 0-4 1h0c0-1 0-1-1-2z" class="q"></path><path d="M94 525h10c2 0 5 0 7 1h1 0c-2 1-6 1-8 1H86c1-1 3-1 4-1h0c1 0 2 0 4-1z" class="n"></path><path d="M432 529c2-5 7-10 10-14l45 25c1 0 1 1 2 1l13 8c-2 1-4 0-5 0h-9-7-4-18-22-6l1-20z"></path><path d="M481 539c2 1 4 1 6 1 1 0 1 1 2 1-2 1-5 1-8 1l-16-1h15c1 0 1-1 1-2z" class="a"></path><path d="M465 541c-3 0-31 1-32 0v-3c5-1 11 0 16 0 11 0 22 0 32 1 0 1 0 2-1 2h-15z" class="h"></path><path d="M368 433c1 1 1 1 1 2 1 1 3 2 4 4l10 9c2 1 3 3 5 5 5 4 11 7 16 12-3-1-6-2-9-2-2 9-5 18-8 26-4 14-7 27-13 40-1 2-2 5-4 7-3 3-5 4-8 4-4 0-6-1-9-3-1-2-2-5-2-8 0-2 1-5 3-6 3-3 6-2 9-2l2-10 3-35c1-12 2-23 1-34 0-3-1-6-1-9zm120-278l3 3v1c7 8 12 17 17 27 2 3 3 6 5 9 2 5 5 10 6 15h0 0l-1 6v9 7c0 1 1 3 0 4 0 1 1 3 0 4h0c-1 0-2 0-3 1h0c1 1 1 1 1 2h1 0 0c0-1 1-1 1-2 0 2 0 3 1 4v1c0 1-1 1-2 0h-10-10l2 1h-13v-1h1c1-2 1-3 1-5v-9-41-36z"></path><path d="M507 212l1 6v23c2 0 3 0 5-1l1 1h1 0c1 1 1 1 1 2h1 0 0c0-1 1-1 1-2 0 2 0 3 1 4v1c0 1-1 1-2 0h-10c0-1 1-2 1-3s-1-2-1-4v-27z" class="C"></path><defs><linearGradient id="C" x1="514.779" y1="200.186" x2="501.981" y2="207.004" xlink:href="#B"><stop offset="0" stop-color="#7f715b"></stop><stop offset="1" stop-color="#a48c6c"></stop></linearGradient></defs><path fill="url(#C)" d="M508 186c2 3 3 6 5 9 2 5 5 10 6 15h0 0l-1 6c-1-1-2-2-2-4 0-1 0 0-1-1v-2c-1 1-2 2-3 2l1 1h-1 1l1 1-2 2h-1v1h0v1h0v1l-1 1h0l-1-1-1 1v-1l-1-6v-14c0-4 0-9 1-12z"></path><path d="M512 211v-1c1 0 0 0 0-1l-1-1v-1h1c1 1 1 1 2 1h0l1 1c-1 1-2 2-3 2z" class="O"></path><defs><linearGradient id="D" x1="495.775" y1="200.279" x2="482.078" y2="202.232" xlink:href="#B"><stop offset="0" stop-color="#e6ca9d"></stop><stop offset="1" stop-color="#fdfad1"></stop></linearGradient></defs><path fill="url(#D)" d="M488 155l3 3v1 1c-1 3-1 6-1 9v21l1 56h6l2 1h-13v-1h1c1-2 1-3 1-5v-9-41-36z"></path><path d="M515 209v2c1 1 1 0 1 1 0 2 1 3 2 4v9 7c0 1 1 3 0 4 0 1 1 3 0 4h0c-1 0-2 0-3 1h-1l-1-1c-2 1-3 1-5 1v-23 1l1-1 1 1h0l1-1v-1h0v-1h0v-1h1l2-2-1-1h-1 1l-1-1c1 0 2-1 3-2z" class="B"></path><path d="M515 209v2c1 1 1 0 1 1 0 2 1 3 2 4v9 7c0 1 1 3 0 4v-1-4l-1-1h0l-2-2h-2l-1-1h1l-1-1c2-1 4-2 5-2l1-1-1-1c-1 0-2 0-3 1h-1 0c1-2 2-2 2-4-1 0-1 1-2 2l-1-1v-1c1-1 2 0 3-1l-1-1h-3v-1h0v-1h1l2-2-1-1h-1 1l-1-1c1 0 2-1 3-2z" class="D"></path><path d="M514 213v1 2h-3v-1h1l2-2z" class="B"></path><path d="M513 227l2-1c1 0 1 1 2 1-1 1-1 1-2 1h-2l-1-1h1z" class="G"></path><path d="M600 517h1l1 2v3l1 3v4c1 13 0 29-4 43l-6 12c-3 9-8 17-14 25l-6 7c-1-1 0-3 0-4v-11l-1-52v-2l2-4 1-1c0-1 1-2 1-2-1-1-2-2-4-2v-2c-1-3-1-9 0-12v-7h0 8 20z" class="E"></path><path d="M588 533c1 0 2 0 2-1h3c-1 2-2 2-4 2l-1-1z" class="F"></path><path d="M576 540l2 1c1-1 2-1 3-1s1 0 2 1h1c0 1 0 2 1 2-1 1-1 1-2 1 0 0-1 0-1 1-1 1 0 1-1 2 0 1-2 1-2 1-1-1-1-1-2-1-2 1-3 2-5 2v-2l2-4 1-1c0-1 1-2 1-2z" class="Q"></path><path d="M578 541c1-1 2-1 3-1s1 0 2 1c-1 1-3 2-4 2 0 0 0-1-1-1l-2 1v-1c1-1 1-1 2-1z" class="p"></path><path d="M572 547l2-4c0 1 1 2 1 3h2 0 1l-1 1c-2 1-3 2-5 2v-2z" class="S"></path><path d="M588 527l1-1-1-1h3v2h1 2c1 1 2 2 4 2-2 1-3 3-5 3h-3c0 1-1 1-2 1h0c-1-1-3-3-4-3-1 1-2 2-4 3l-1-1c1-1 2-1 2-1l1-1c-2-1-1 0-3-1 0 0-1 0-1-1h-1v-1h3 3 1 2 1 1z" class="K"></path><path d="M577 528v-1h3-1v1h2l1 1h1v1h-1l-1-1c-1-1-2-1-3-1h-1z" class="M"></path><path d="M580 527h3 1 2 1 1l-2 2c-1-1-1-1-2-1h0-2v1l-1-1h-2v-1h1z" class="H"></path><path d="M602 522l1 3v4l-1-1c-1 0-3 1-4 1-2 0-3-1-4-2h-2-1v-2h-3l1 1-1 1h-1-1-2-1-3-3v1h-1c-1-1-1-1-1-2h-1-2v10c-1-3-1-9 0-12 5 1 12 0 18 0 3-1 7-1 10-1 1 0 2 0 2-1z" class="p"></path><path d="M572 526c1-1 11-1 14-1 0 0 1 0 1 1v1h-1-2-1-3-3v1h-1c-1-1-1-1-1-2h-1-2z" class="J"></path><path d="M586 525h11c2 0 4-1 6 0v4l-1-1c-1 0-3 1-4 1-2 0-3-1-4-2h-2-1v-2h-3l1 1-1 1h-1v-1c0-1-1-1-1-1z" class="Q"></path><path d="M600 517h1l1 2v3c0 1-1 1-2 1-3 0-7 0-10 1-6 0-13 1-18 0v-7h0 8 20z" class="i"></path><path d="M431 531h0l1-2-1 20h6 22 18 4 1v3-1c-1 4-1 9 0 13l-1 18-3 2h-1c-2 1-3 2-5 2-2 3-4 6-7 8h1c-5 4-10 8-16 11 0 1-1 1-2 2l-17 11c-1-2 0-14 0-17l-1 1v-36-11c0-1 1-3 0-5h-1 0c0-1 1-1 1-1 1-3 1-7 1-11v-7z"></path><path d="M448 607v-2c2-3 2-7 4-9h0c0 3 0 6-2 8v1h0c0 1-1 1-2 2z" class="X"></path><path d="M477 549h4 1v3-1c-3-1-20 0-24-1h6 13l1-1h-1z" class="j"></path><path d="M459 549h18 1l-1 1h-13-6 0c-1 0-1 0-2-1h3 0z" class="h"></path><path d="M437 549h22 0-3c1 1 1 1 2 1h-9-16c2 0 5 1 7-1h-3 0z" class="c"></path><path d="M431 531h0l1-2-1 20h6 0 3c-2 2-5 1-7 1h-1c-1 1-1 3-1 4v7 29 11l-1 1v-36-11c0-1 1-3 0-5h-1 0c0-1 1-1 1-1 1-3 1-7 1-11v-7z" class="h"></path><path d="M432 593v-2-7h45c-2 1-3 2-5 2-2 3-4 6-7 8h-24c-2 0-7 0-9-1z" class="N"></path><path d="M432 593c1 0 2 1 3 0s1-2 2-3c0-2-1-2-1-3v-1h1c1 1 3 2 4 3-1 1-2 1-2 3h1c1 0 1 1 1 2-2 0-7 0-9-1z" class="B"></path><path d="M112 526h62l-2 1h0-2v1 14 28 33c0 2 1 8 0 9h-1-1-1c-1-1-2-2-2-3l-22-15-10-9c-2-2-4-4-6-5h0l-3-3c-3-4-7-8-10-12-1 0-2-2-3-3v-7c1-2 0-6 0-8l1-20h1l-1-1h0z"></path><path d="M139 575v-1c1 0 1 0 1-1s0-2 1-4c1 0 1-1 1-2l2 2v3s0 1-1 2l1 1h-1l-1-1c0-1 1-2 0-3h0l-1 1v3l-1 1-1-1z" class="E"></path><path d="M140 576c1 1 3 2 4 3s1 4 1 5c-2-2-4-5-6-7-1-1-4-3-5-4h0-1v-1c1-1 1-1 1-2v-1c1-1 1-1 3-1 0 0 0 1 1 1l1 1-2 1 1 1c0 1 0 2 1 3l1 1z" class="C"></path><path d="M112 526h62l-2 1h0-2v1 14 28 33c0 2 1 8 0 9h-1-1-1c-1-1-2-2-2-3v-5-11l1-44c-1-7-1-14-1-22h0-35c-1 2-1 3-1 5v10 38h0l-1-1-1 1h0l-3-3c-3-4-7-8-10-12-1 0-2-2-3-3v-7c1-2 0-6 0-8l1-20h1l-1-1h0z" class="c"></path><path d="M113 527h11v41c0 3 1 7 0 9-3-4-7-8-10-12-1 0-2-2-3-3v-7c1-2 0-6 0-8l1-20h1z"></path><path d="M445 235c1-6 1-14 1-20v-55-44c0-1 0-2 1-3l5 5c2 1 3 3 4 4s2 1 2 1l7 7c2 1 3 2 4 4 1 1 3 2 4 3l5 5c1 2 2 3 3 4l7 9v36 41 9c0 2 0 3-1 5h-1v1h-4c-1-1-1-1-2-1-1-1-5-1-6-1l-1-1h-1c-1 0-1 0-2 1h0c-1 0-1 1-2 1-3 1-7-1-10 1h0-4-4c-1-1-3 0-4 0-1-2-1-3-2-4l1-1v-7z" class="T"></path><path d="M452 118c2 1 3 3 4 4v2l-4-4-1 5c0-2 0-5 1-7z" class="W"></path><path d="M456 122c1 1 2 1 2 1-1 4 0 8 0 12v18c-1 4 0 8 0 12 0 3 0 5-1 7v1h0v-2c-2-6-1-13-1-19v-28-2z" class="L"></path><path d="M445 235c1-6 1-14 1-20v-55-44c0-1 0-2 1-3l5 5c-1 2-1 5-1 7v70 26c0 7-1 14 0 22h1v-2h1v2c1 1 2 1 2 2v1l-1 1h-4c-1-1-3 0-4 0-1-2-1-3-2-4l1-1v-7z"></path><path d="M453 243c1 1 2 1 2 2v1l-1 1h-4l1-3h2v-1z" class="D"></path><path d="M458 123l7 7c2 1 3 2 4 4v16l1 25v46 14c0 2-1 4 0 5v5c-1 0-1 1-2 1-3 1-7-1-10 1h0v-5h-1c0-1 0-1 1-2h-1v-1-1-1s1-1 1-2v-35c-1-2-1-5-1-8v-2-4c-1-1-1-3-2-3v-1-9-3l1-1v2 2h1 0v-1c1-2 1-4 1-7 0-4-1-8 0-12v-18c0-4-1-8 0-12z"></path><path d="M458 153c1 9 0 18 0 27v8 1 3 8c-1-2-1-5-1-8v-2-4c-1-1-1-3-2-3v-1-9-3l1-1v2 2h1 0v-1c1-2 1-4 1-7 0-4-1-8 0-12z" class="G"></path><defs><linearGradient id="E" x1="451.11" y1="180.506" x2="490.358" y2="197.039" xlink:href="#B"><stop offset="0" stop-color="#99896e"></stop><stop offset="1" stop-color="#c1a77a"></stop></linearGradient></defs><path fill="url(#E)" d="M469 134c1 1 3 2 4 3l5 5c1 2 2 3 3 4l7 9v36 41 9c0 2 0 3-1 5h-1v1h-4c-1-1-1-1-2-1-1-1-5-1-6-1l-1-1h-1c-1 0-1 0-2 1h0v-5c-1-1 0-3 0-5v-14-46l-1-25v-16z"></path><path d="M476 163h1l1 1-1 1h-1v-2z" class="R"></path><path d="M476 204h1v2c-1 0-1 1-1 1v1l-2-1h0l2-3z" class="l"></path><path d="M475 171c0-1 0-2 1-2h1c1 2 1 2 3 2v3l-1-2h-1c-1-1-1-1-2-1h-1z" class="G"></path><path d="M475 171h1c-1 2-1 3 0 5h1 0v1c-1 0-1 1-1 2h-1l-1-1h1v-4c-1-1 0-2 0-3z" class="D"></path><path d="M478 142c-2 1-3-1-5-2h-2c1-2 1-2 2-3l5 5z" class="I"></path><path d="M480 182h1l-1 11c0-2 0-4-1-5h-2 0 1c1-1 1-2 2-4v-2z" class="T"></path><path d="M480 171v-7c-1-1-1-3-1-4 2-2 1-6 1-8 0-1 0-2 1-3v33h-1v-8-3z" class="I"></path><path d="M476 171c1 0 1 0 2 1h1v3 2s0 1 1 2v2l-3 3-2-2 1-1c1 0 2-1 2-1-1-2-2-1-1-3v-1h0-1c-1-2-1-3 0-5z" class="N"></path><path d="M474 221v-1-1h0v-4c0-1 1-2 1-3h0c1-1 1-2 1-3l2 3v1h0v1h2c1 2-1 9 1 10v1c-1 5 0 10-1 14h0c-1 1-1 2-1 3h-1v-1c-1 1-2 2-3 2s-1 0-2-1l-1-2v-2c0-2 0-3-1-4v-2-2c1-1 1-2 2-3 1 0 2 0 2-1s0-2-1-2v-1c-1-1-1-1 0-2z" class="l"></path><path d="M476 226h2c0 1 0 1-1 2h-1v-2z" class="L"></path><path d="M474 221h0 2 1l-3 3v-1c-1-1-1-1 0-2z" class="U"></path><path d="M475 231h1 3 0 1c-1 1-1 1-1 2h-1c-1 1-2 1-4 1h-1l1-1h0l1-2z" class="L"></path><path d="M478 241l-1-1c0-1-1-1-1-1 0-2 1-1 1-2v-2h1c1 0 1 1 2 1l-1 2 1 1h0c-1 1-1 2-1 3h-1v-1z" class="V"></path><path d="M481 146l7 9v36 41 9c0 2 0 3-1 5h-1v1h-4c-1-1-1-1-2-1-1-1-5-1-6-1l-1-1h-1c-1 0-1 0-2 1h0v-5c0 1 0 2 1 2h2c1 1 1 1 2 1s2-1 3-2v1h1c0-1 0-2 1-3h0c1-4 0-9 1-14v-1l-1-31 1-11v-33-3z"></path><path d="M481 225h0c1 7 0 14 0 21h5v1h-4c-1-1-1-1-2-1-1-1-5-1-6-1l-1-1h-1c-1 0-1 0-2 1h0v-5c0 1 0 2 1 2h2c1 1 1 1 2 1s2-1 3-2v1h1c0-1 0-2 1-3h0c1-4 0-9 1-14z" class="Y"></path><path d="M109 158l1 1 1 4-1 71v5h0l1-16h0c-1 3 0 8 0 12v1 10h1v1 2c-1 4-1 9-1 13v5l-1-1v6h0v23c-2 1-4 1-7 1H93 72h-4 0-8c-1 0-1 0-1-1v-8l1-20 3-14c0-1 0-4 1-4 1-1 2-1 2-2h0-1l-1-1c2-3 2-7 3-11 2-4 3-9 5-13 7-18 17-36 29-52 2-4 5-8 8-12z"></path><path d="M103 276c1 1 2 0 3 0l2 1c-2 1-2 1-4 1h-2c1-1 1-1 1-2z" class="q"></path><path d="M101 271v-1c1-1 2-1 4-1v2c-1 0-2 0-3 1 0-1-1-1-1-1z" class="c"></path><path d="M68 296v-1-3h0c1 1 2 1 3 1 1 1 1 1 1 3h-4z" class="k"></path><path d="M102 278h0c-1-1-2-2-2-3-1-2 0-3 1-4 0 0 1 0 1 1-1 1-1 2 0 3l1 1c0 1 0 1-1 2zm3-9c1 0 2 1 3 2s1 3 0 4v2l-2-1 1-2c0-1-1-2-2-3v-2z" class="j"></path><path d="M82 239h1 1l-3 3-5 1h-1 0c1-1 2-2 4-3l3-1z" class="E"></path><path d="M102 272c1-1 2-1 3-1 1 1 2 2 2 3l-1 2c-1 0-2 1-3 0l-1-1c-1-1-1-2 0-3z"></path><path d="M71 252h2l1 1 1-1c2 0 5 0 6 1 0 0 0 1-1 1s-2 0-3 1h0c-3 0-5 1-8 2h0l2-5z" class="E"></path><path d="M110 234v5h0l1-16h0c-1 3 0 8 0 12v1 10h1v1 2c-1 4-1 9-1 13v5l-1-1v6h0v-25l-13 1c-8 0-18 1-26 0v-1h6 13l20-1c-1-4-1-8 0-12z" class="B"></path><path d="M311 73c14-1 28 0 41 1 2 1 5 1 7 2h2c2 0 3 0 5 1h2 0l-1 40h0v9 3l-1 1v2c0 1 0 2 1 3h0 1v22h0 0c-1-3-1-6-1-8-1 1 0 3 0 5v5 10c-5-2-11-5-17-6-8-3-17-4-25-5-4 0-9 1-14 0v-2c-1 1 0 2-1 2h-1V73h2z"></path><path d="M347 114v1h1l1 2-1 1c-1 0-1-1-2-1 0-1 0-2 1-3z" class="C"></path><path d="M347 110c1 1 1 1 2 1v1 1l1 1-1 1h-1-1v-1-4z" class="e"></path><path d="M332 108h1v1s0 1 1 1v1c1 1 1 3 1 4-1 0-1 0-2-1s-1-4-1-6z" class="E"></path><path d="M364 150h1c-1 2-1 5-1 7-1 0-1 1-1 1v2h-1c-1-2 1-6 1-9 0 0 0-1 1-1z" class="K"></path><path d="M322 103c1 1 3 3 3 4s-1 1-2 2v-1c-1 0-2 0-3-1v-1h0c1-1 2-1 2-1v-1-1z" class="k"></path><path d="M366 113h1v4 9 3l-1 1v-17z" class="h"></path><path d="M353 124v1c1 1 2 2 3 4s2 7 1 10h0c-1 1-1 1-1 2l-1-3c0-1 0-1-1-2h0c1-2-1-5-2-6 1-1 0-2 0-4 0 0-1 0-1-1l2-1z" class="P"></path><path d="M323 109c1-1 2-1 2-2l2 2c1 0 1 1 2 2h1v2h-1-2 0l-1 1c0 1 0 1-1 1-2-1-4 1-6 0v-2h2 0c0-1 1-2 1-2v-1l1-1z" class="i"></path><path d="M321 113h0v1h3c-2 0-3 0-5 1v-2h2z" class="f"></path><path d="M330 111l-1 1h-2-1v-2h0v-1l1 1v1h2 1z" class="C"></path><path d="M322 111c1 1 2 1 2 2v1h-3v-1c0-1 1-2 1-2z" class="H"></path><path d="M366 77h2 0l-1 40h0v-4h-1V77z" class="o"></path><path d="M355 97l1-1c1 1 1 3 1 5-3 3-6 6-8 11v-1c-1 0-1 0-2-1 0-1 0-1 1-2v-1c0-2 0-3 1-5h-1c1-2 5-4 7-5h0z" class="X"></path><path d="M349 102h-1c1-2 5-4 7-5h0v1l-3 4h0c0 1-1 1-1 2l-1 1c0 1 0 1-1 1h0l1-2v-1l-1-1z" class="H"></path><path d="M359 76h2v35c-1 0-1 1-2 0V76z" class="c"></path><path d="M319 105v1h1v1c1 1 2 1 3 1v1l-1 1v1s-1 1-1 2h0-2 0-1c-1 1-1 1-3 1-1 0-2-1-4 0v2h0v-2c1-2 0-5 0-8h2c1 1 3 1 4 1l1-1h0l1-1z" class="k"></path><path d="M319 105v1h1v1c1 1 2 1 3 1v1l-1 1v1s-1 1-1 2h0c-1-2-2-4-3-5v-1h-1l1-1h0l1-1z" class="J"></path><path d="M313 98v-2h3l1 1c2 2 3 4 5 6v1 1s-1 0-2 1h0-1v-1l-1 1h0l-1 1c-1 0-3 0-4-1s0-6 0-8z" class="e"></path><path d="M318 103h1v2l-1 1h0l-1-1c0-1 0-1 1-2z" class="F"></path><path d="M320 102l2 2v1s-1 0-2 1h0-1v-1-2h-1l2-1z" class="K"></path><path d="M317 97c2 2 3 4 5 6v1l-2-2h-1c-2-1-2-3-2-5z" class="J"></path><path d="M365 143c0-2-1-4 0-6 0-2 1-3 1-5 0 1 0 2 1 3h0 1v22h0 0c-1-3-1-6-1-8-1 1 0 3 0 5v5c-1 0-2-1-4-1 0 0 0-1 1-1 0-2 0-5 1-7h-1v-4-1s0-1 1-2z" class="E"></path><path d="M365 143v-4h0c1 1 1 3 1 4s-1 1-1 1v2 4h-1v-4-1s0-1 1-2zm-54-27h0c0 1 0 9 1 10 2 0 2 1 3 2 3 5 4 10 5 15l-1 1v-1h-3-1-4v1-28z" class="J"></path><path d="M309 73h2v13 6 1c1 2 0 4 0 6 1 0 1 0 2-1 0 2-1 7 0 8h-2c0 3 1 6 0 8v2 28 14-2c-1 1 0 2-1 2h-1V73z" class="j"></path><path d="M311 92v1c1 2 0 4 0 6 1 0 1 0 2-1 0 2-1 7 0 8h-2c0 3 1 6 0 8V92z" class="J"></path><path d="M333 120c1-1 2 0 3-1 2 0 3 0 4-1 2 1 4 1 6 1 2 1 3 2 5 3l2 2-2 1c0 1 1 1 1 1 0 2 1 3 0 4 1 1 3 4 2 6h0c1 1 1 1 1 2l1 3 1 1c-1 2-2 5-4 7l-2 2c-3 3-8 4-12 4-5 0-10-2-13-6s-5-8-4-13c0-6 3-10 7-14l4-2z" class="L"></path><g class="U"><path d="M336 141c1 0 2 1 2 1h1c0 1-1 0-1 1v2l-1 1v-1c-1-1-1-2-1-4z"></path><path d="M333 142h1c1 1 1 2 1 3v2h0l2-1h0v3l-2-1v2c1 0 2 0 3 1h-1c0 1-1 1-2 1l1-1h1c-2 0-2 0-2-1v-1c0-1-1-2-1-2v-1c-1 2-1 3-2 4h0 0c0-2 1-2 1-4l1-1c0-1 0-1-1-2h0v-1z"></path></g><path d="M349 140l2 2h0c-1-1-1-1-1-2 1 0 1-1 1-1v-1s1 0 1-1h0 2l-1 1 1 1 1-1 1 3 1 1c-1 2-2 5-4 7v-1c0-1 0-1 1-2v-1h-1 0c-1-1-1-1-2-1h-3 0c0-1 0-1 1-2v-1-1z" class="T"></path><path d="M337 129h1v-2h2 0l1 1 1-1c1 1 1 1 1 2h2 0c-1 2-1 3-2 5 0 0-1 1-1 2v-1c-1 1-1 1-2 1s-1-1-1-2h-1l-1 1h-1-1c1-1 1-1 1-2h1l1-1h-1v-2-1h0z" class="N"></path><path d="M340 129c1 0 2 1 3 1 0 1-1 1-2 1l1 1v1l-1 1c-2-1-1-1-1-2s-1 0-1-1l1-2z" class="G"></path><path d="M345 129l1 1 1-1c-1-1-1-1-1-2 1 0 2 1 3 1 1 1 1 2 1 3h1v-1h1 0c1 1 3 4 2 6h0c1 1 1 1 1 2l-1 1-1-1 1-1h-2 0c0 1-1 1-1 1v1s0 1-1 1c0 1 0 1 1 2h0l-2-2v-1l-5 1-1-1c-1-1 0-1-1-3 0-1 1-2 1-2 1-2 1-3 2-5z" class="B"></path><path d="M342 136c1 0 1-1 1-1 1 0 1-1 1-1l1-1v1c1 0 1-1 2-1 0 1 1 1 0 3 0 1-1 2-2 3h-1v1l-1-1c-1-1 0-1-1-3z" class="a"></path><path d="M345 129l1 1 1-1c-1-1-1-1-1-2 1 0 2 1 3 1 1 1 1 2 1 3h1v-1l1 1c0 2-1 2-1 3l-1 1v-2h-1v-2h-2l-1 1s1 0 1 1c-1 0-1 1-2 1v-1l-1 1s0 1-1 1c0 0 0 1-1 1 0-1 1-2 1-2 1-2 1-3 2-5z" class="G"></path><path d="M333 120c1-1 2 0 3-1 2 0 3 0 4-1 2 1 4 1 6 1 2 1 3 2 5 3l2 2-2 1c0 1 1 1 1 1 0 2 1 3 0 4h0-1v1h-1c0-1 0-2-1-3-1 0-2-1-3-1 0 1 0 1 1 2l-1 1-1-1h0-2c0-1 0-1-1-2l-1 1-1-1h0-2v2h-1c0-1-1-3-1-4h2c-1 0-1-1-1-2-1 0-2 1-2 1l-1-1v-1h-1c-1 0-1 0-2 1h0l-2-1 4-2z" class="R"></path><path d="M343 129c0-2 0-2-1-3v-1h2 1v4h-2z" class="G"></path><path d="M351 122l2 2-2 1c0 1 1 1 1 1 0 2 1 3 0 4h0-1v1h-1c0-1 0-2-1-3-1 0-2-1-3-1 0 1 0 1 1 2l-1 1-1-1h0v-4l1-1s0 1 1 1c0 1 0 1 1 1 0-1 1-1 1-2v-1l2-1z" class="D"></path><path d="M351 122l2 2-2 1v1h-1s0-1-1-1v-1-1l2-1z" class="B"></path><path d="M333 120c1-1 2 0 3-1 2 0 3 0 4-1 2 1 4 1 6 1-1 0-2 0-3 1v1h0l2 1h0v1l-1 1h-2l-1 1c0 1-1 1-2 1h-1v-4l1-1c-2 0-2 0-3 1h0v-1-1l-1 1-2-1z" class="G"></path><path d="M236 81c1 3 0 7 0 10v21c-1 2 0 8 0 10h-3l-1-1h0c-1 1-1 2 0 3h0c0 1 0 1 1 2l3 3h0c1 6-1 12 0 17v11s0 1-1 1c0 1-1 0-2 0-2 0-5 1-7 0h-4 0-22c-3 1-7 0-9 0h-24-21-24c-4 0-7 1-11 0 1-2 4-4 5-5 9-10 19-19 30-27 6-4 12-9 19-13 3-1 6-3 10-4 3-2 6-3 9-5l9-5c2-1 4-2 7-3l1-3 30-11c2 0 4 0 5-1z"></path><path d="M125 152h2l1 1-1 1h-2 0v-2z" class="F"></path><path d="M221 157h0l1-1c0-1 2 0 2 0l-2 2h0l-1-1z" class="k"></path><path d="M186 113c1-1 2-2 3-2l1 1-4 4v-3z" class="G"></path><path d="M217 97v1h1c0 1 1 1 1 2 1 0 1 0 2 1h-1c-1 0-3 1-4 0v-2c0-1 0-1 1-2z" class="f"></path><path d="M150 131h1c-1 0-1 1-1 1s-1 0-1 1c0 0-1 0-1 1-2 0-3 1-4 2l-1-1 1-1c1-1 2-1 2-1 1-1 3-2 4-2z" class="E"></path><path d="M189 118l-1-2c1 0 1-1 2-2 1 0 1-2 2-2l1-1c1-1 1-2 2-2h0c0 1-1 2-2 2v1c1 1 1 1 0 2 0 1-2 2-2 3l-2 1z" class="J"></path><path d="M184 112l1 2s1 0 1-1v3c-1 1-3 2-4 4h-1c0-3 2-6 3-8z" class="R"></path><path d="M224 156l1 1 1-1-1-1h-3 0c0-1 6-1 7-1l1 1v1l-3 1h1-1l-1 1h-4l2-2zm-19-52h1c0-1 1 0 1-1h1c1-1 1 0 2 0h1 1 1c1 0 1 1 1 1l-1 2 1 1c1 0 2 0 3-1 1 0 1-1 1-1v-1c1 0 1 0 1-1 1 1 2 1 3 1l-1 1h-1v-1h-1v2l-3 1-1 2c-1 1-1 2-2 2h0-1v-2h-1v-1l2-2-1-1c1 0 1 0 1-1h-1s0 1-1 1c0-1-1-1-1-1-1-1-1 1-1 1h-2c-1 0-1-1-1-1h-1z" class="F"></path><path d="M186 136c2 1 8 0 11 0v1c0 1 0 2-1 3l1 1-1 1v2c0 1 1 1 0 1v1l-1-1h-1v-2h-1l-1 1h-1l-1-1h-1l-1-1 1-1c1-1 3-2 4-3v-2l-6 1-1-1z" class="P"></path><path d="M193 136h2c1 1 1 2 1 3-1 0-1 0-2 1l-1 1h-3v2h-1l-1-1 1-1c1-1 3-2 4-3v-2z" class="d"></path><path d="M191 105c1 0 2-1 3 0v1c1 0 1 0 1-1l1 1-6 6-1-1c-1 0-2 1-3 2 0 1-1 1-1 1l-1-2h0c1-2 1-4 2-5s2-1 3-1c0 0 1-1 2-1z" class="I"></path><path d="M189 106s1-1 2-1l2 1h0l-2 1v1h-1l-2 2v-1c1-1 1-2 1-3z" class="B"></path><path d="M188 110l2-2h1l-2 3c-1 0-2 1-3 2 0 1-1 1-1 1l-1-2h0c2-1 3-1 4-2z" class="N"></path><path d="M184 112c1-2 1-4 2-5s2-1 3-1c0 1 0 2-1 3v1c-1 1-2 1-4 2z" class="T"></path><path d="M205 104h1s0 1 1 1h2s0-2 1-1c0 0 1 0 1 1 1 0 1-1 1-1h1c0 1 0 1-1 1l1 1-2 2-1-1v-2h-1c-1 0-2 1-2 2l-1-1-4 4h0-1c-1 0-1 1-1 1h0-1-2l1 1-1 1h-1-1l-2 1c1-1 1-1 0-2v-1c1 0 2-1 2-2h0c1 0 1-1 2-1s2 0 3-1c0-1 0-1 1-1 2 0 3-1 4-2z" class="C"></path><path d="M202 110l4-4 1 1c0-1 1-2 2-2h1v2l1 1v1l-1 1h-2l-1 1 1 1c-1 1-2 2-2 3-1 0-1 0-2 1v1h0 0-2c0-1 1-1 1-1 0-1 0-1-1-1 1-1 1-1 1-2l-1-2h-1v1l-1 1c0-1-1-1-1-1h0-1l-1-1h2 1 0s0-1 1-1h1 0z" class="d"></path><path d="M210 107l1 1v1l-1 1h-2v-1c0-1 2-1 2-2z" class="M"></path><path d="M202 110l2-1c1 0 1 0 2-1v1 1l1 1h-1c0 1-1 2-1 3h0c-1 0-1 1-1 1v2h0-2c0-1 1-1 1-1 0-1 0-1-1-1 1-1 1-1 1-2l-1-2h-1v1l-1 1c0-1-1-1-1-1h0-1l-1-1h2 1 0s0-1 1-1h1 0z" class="Y"></path><path d="M211 109h1v2h1 0c0 2 1 2 1 4l-2 2-1 2c0 1-1 1-2 1l-1-1c0-1-1-1-1-1l-4 3-1-1v-2c1 0 1-1 2-1h0 0v-1c1-1 1-1 2-1 0-1 1-2 2-3l-1-1 1-1h2l1-1z" class="K"></path><path d="M207 118l2-3h1c1 1 0 3 0 4l-1 1-1-1c0-1-1-1-1-1z" class="P"></path><path d="M211 109h1v2h1 0c0 2 1 2 1 4l-2 2v-3-1c-1 0-1-1-1-1-1 0-1-1-1-1v-1l1-1z" class="M"></path><path d="M211 109h1v2h1 0l-1 1-2-1v-1l1-1z" class="f"></path><path d="M210 143c0 1 0 1 1 2h0c0 1 0 2 1 3h0l1 1v1h-3l-1 1h0l-2 1-2-2h0l-1-1h-1 0-1c0 1-1 2-2 3h2v1c-2 2-3 1-6 1h0 1v-1l1-1 2-2 1-1h0l2-2v-1l2-2h1s1 0 2 1v-1h1l1-1z" class="K"></path><path d="M210 143c0 1 0 1 1 2h0c0 1 0 2 1 3h0l1 1v1h-3l-1 1h0c0-1-1-1-2-1 0-1 0-1-1-1l-1-1v1h-1v-1l1-1v-1h3v-1-1h1l1-1z" class="X"></path><path d="M210 143c0 1 0 1 1 2h0c0 1 0 2 1 3h0c-1 0-1 1-1 1-1 0-1-1-2-1h-2v-1l1-1h0v-1-1h1l1-1z" class="P"></path><path d="M210 143c0 1 0 1 1 2h0l-1 1h-1-1 0v-1-1h1l1-1z" class="b"></path><path d="M167 158l2-2h10 16c3 0 4 1 6 0 1 0 2-1 3-1h1c1 0 2 0 2 1h-3-1c-1 0-3 0-3 1 1 0 2 1 3 0 4 0 8 0 12 1 1 0 3-1 4-1h0c0-1-1-1-2-1h3v1h0 1l1 1h-22c-3 1-7 0-9 0h-24z" class="F"></path><path d="M192 154h-6c-1-1-3-1-5-1h0c3-2 4-4 5-7 1-2 1-3 2-4l1 1h1l1 1h1l1-1h1v2h1l1 1 1 1h-1l-1 1h3v1 1l1-1 1-1v1h1 0l-1 1-2 2-1 1v1h-1-4z" class="g"></path><path d="M190 149s0-1-1-1l1-1h4v-1l1-1 1 1 1 1h-1l-1 1h3v1h-2l-1-1h-3c-1 1-1 1-2 1z" class="Z"></path><path d="M192 148h3l1 1h2v1l1-1 1-1v1h1 0l-1 1-2 2-1 1v1h-1-4v-1l1 1 1-1-1-1h-1l-1-1 1-1s-1-1-2-1c1 0 1 0 2-1z" class="d"></path><path d="M192 148h3l1 1v1c-2 0-2 0-4-2z" class="g"></path><path d="M197 136h1 15v1h0l1 2c0 1-1 1-1 2l-1 1v1h1l-2 2c-1-1-1-1-1-2l-1 1h-1v1c-1-1-2-1-2-1h-1l-2 2v1l-2 2h-1v-1l-1 1-1 1v-1-1h-3l1-1h1l-1-1v-1c1 0 0 0 0-1v-2l1-1-1-1c1-1 1-2 1-3v-1z" class="O"></path><path d="M199 141l2 1-1 1-1 1c1 1 1 1 2 1 0 1 0 1-1 2 0-1-1-1-1-1h-1l1-2h-1c0-1 0-2 1-3z" class="Y"></path><path d="M197 136h1v2c1 1 2 1 1 2-1 0-1 0-2 1h2c-1 1-1 2-1 3h-2v-2l1-1-1-1c1-1 1-2 1-3v-1z" class="m"></path><path d="M203 146l-1-1 1-1v-2h1c1-1 1-4 1-5h2c0 1-1 1-2 2v3 2l-2 2z" class="P"></path><path d="M198 144h1l-1 2h1s1 0 1 1c1-1 1-1 1-2l2 2-2 2h-1v-1l-1 1-1 1v-1-1h-3l1-1h1l-1-1v-1c1 0 0 0 0-1h2z" class="S"></path><path d="M207 137h1 2 3 0l1 2c0 1-1 1-1 2l-1 1v1h1l-2 2c-1-1-1-1-1-2l-1 1h-1v1c-1-1-2-1-2-1h-1v-2-3c1-1 2-1 2-2z" class="H"></path><path d="M206 141c2 0 2 0 3 1v1c-1 0-1 1-1 0-1 0-2-1-2-1v-1z" class="K"></path><path d="M210 137h3 0c0 1-1 2-1 3h-2l-1-2 1-1z" class="Q"></path><path d="M207 137h1l1 1c-1 1-2 1-2 2s1 0-1 1v1 2h-1v-2-3c1-1 2-1 2-2z" class="b"></path><path d="M213 137l1 2c0 1-1 1-1 2l-1 1v1h1l-2 2c-1-1-1-1-1-2h0c0-2 0-2 2-2v-1c0-1 1-2 1-3z" class="P"></path><path d="M223 132c1 0 1 0 2-1 0 4-1 5-2 9h2v1h0v1 1 1l1-1v-1l1 1v2l-1 1 1 1 1 1v2 1h1c0 1-1 2-1 3h-4s-1-1-2-1c-1 1-1 0-2 0-1 1-3 0-4 0l1 2v1c-1 0-2 1-2 0v-1c-1 0-1 0-2-1v2c-1 0-2 0-3 1-1-1-1-1-1-2 0 0 0-1 1-1s2-1 2-1v-2c-2 0-4 1-5 2-2 0-3 0-4-1v-1c2 0 1 1 4 1h0l2-1h0l1-1h3v-1l-1-1h0c-1-1-1-2-1-3h0l2-2h-1v-1l1-1c0-1 1-1 1-2l-1-2h0v-1h3 6v-3l1-1z" class="k"></path><path d="M223 153h-2-2c-1 0-3 0-4-1h0l1-1h2c1 0 3 0 4 1h0l1 1z" class="M"></path><path d="M227 147l1 1v2 1 2h-1-2-2 0l-1-1h0c-1-1-3-1-4-1 1 0 2 0 3-1 0 1 1 1 2 1h0c1-2 0-3 2-4h2z" class="e"></path><path d="M227 147l1 1c0 1-1 2-2 3h-3c1-2 0-3 2-4h2z" class="H"></path><path d="M223 140h2v1h0v1 1 1l1-1v-1l1 1v2l-1 1 1 1h-2 0l-1-1h-1c-1-1-2-1-2-2-1 1-2 2-2 3v-3h2v-2c1-1 2-1 2-2z" class="e"></path><path d="M223 132c1 0 1 0 2-1 0 4-1 5-2 9 0 1-1 1-2 2v2h-2v3l-1 1h-2v1h-1-2l-1-1h0c-1-1-1-2-1-3h0l2-2h-1v-1l1-1c0-1 1-1 1-2l-1-2h0v-1h3 6v-3l1-1z" class="m"></path><path d="M216 141c1 1 1 1 1 2h0l-2-1-1-1h2z" class="O"></path><path d="M215 142l2 1v2l1 1v2h-2v-2l-1-1-1-1h1 0v-2h0z" class="P"></path><path d="M213 136h3c-1 1-1 2 0 3h1v1l-1 1h-2 0-1c0-1 1-1 1-2l-1-2h0v-1z" class="S"></path><path d="M213 141h1 0l1 1h0v2h0-1l1 1 1 1v2 1h-1-2l-1-1h0c-1-1-1-2-1-3h0l2-2h-1v-1l1-1z" class="W"></path><path d="M215 145l1 1v2 1h-1-2l-1-1h0 1v-1l2-2z" class="S"></path><path d="M223 132c1 0 1 0 2-1 0 4-1 5-2 9 0 1-1 1-2 2v2h-2v3l-1 1v-2l1-1-1-1v-1h2v-4c2-1 2-1 2-3v-3l1-1z" class="b"></path><path d="M198 112h1 0s1 0 1 1l1-1v-1h1l1 2c0 1 0 1-1 2 1 0 1 0 1 1 0 0-1 0-1 1h2c-1 0-1 1-2 1v2l1 1c0 2-1 5-3 5v1h0c-1 1-1 2-1 3 1 1 1 1 1 2l-3 1h-8-3l-1-1c-1-2-2-4-1-6 0-3 2-5 5-8l2-1c0-1 2-2 2-3l2-1h1 1l1-1z" class="O"></path><path d="M192 130c-1-1-1-1-1-2l-1-1 1-1h0l1-1c1 0 1 1 2 1s1 0 1-1h0 1l1-2c0 1 0 2 1 3h0c-2 0-3 1-5 1l-1 1v1 1h0z" class="Z"></path><path d="M202 117h2c-1 0-1 1-2 1v2l1 1c0 2-1 5-3 5v1h0c0-1-1 0-1-1h-1 0 0c-1-1-1-2-1-3 1 0 2 0 3-1v-2c1-1 1 0 2 0l-1-2 1-1z" class="e"></path><path d="M192 130h0v-1-1l1-1c2 0 3-1 5-1h0 1c0 1 1 0 1 1-1 1-1 2-1 3 1 1 1 1 1 2l-3 1h-8-3l-1-1h3v-1h2c1 0 1 0 2-1z" class="f"></path><path d="M199 130c1 1 1 1 1 2l-3 1h-8-3l-1-1h3v-1h2l-1 1h1c1 1 5 1 6 0 1 0 3-2 3-2z" class="P"></path><path d="M191 117l-1 1c0 1-2 6-1 7l1 2v1 1h0l-2 2v1h-3c-1-2-2-4-1-6 0-3 2-5 5-8l2-1z" class="N"></path><path d="M198 112h1 0s1 0 1 1l1-1v-1h1l1 2c0 1 0 1-1 2 1 0 1 0 1 1 0 0-1 0-1 1l-1 1v1h-3 0v1 1s-1 1-2 1v1h0v1h-2 0c-1 0-1 0-2-1-1 0-1 0-1-1v-1c0-1 1-1 1-1v-1c1-1 1-1 1-2v-1h1v-1c1 0 1-1 1-1v-1h1 1l1-1z" class="T"></path><path d="M207 118s1 0 1 1l1 1h0c1 1 2 1 3 2h1c1 1 1 1 2 1h0l2 1h1v1h-1-1c0 1 0 1-1 2 0 1 0 2-1 2v1 1l3 1h0c1 0 3 0 4 1h1v3h-6-3-15-1c-3 0-9 1-11 0v-3h3 8l3-1c0-1 0-1-1-2 0-1 0-2 1-3h0v-1c2 0 3-3 3-5l4-3z" class="m"></path><path d="M207 130c0-1 0-1-1-2v-1h2l-1-1h1c1 0 1 0 1 1v2l-2 1z" class="d"></path><path d="M209 129v1 1c0 1 0 1 1 1v-1h2v-1c1 0 1 1 2 1l3 1h0c-2 0-9 1-9 0v-1h-1v-1l2-1z" class="M"></path><path d="M209 120c1 1 2 1 3 2h1c-1 1-2 2-3 2l1 1-1 2h-1c0-1 0-1-1-1v-2l-1-1h1 0c0-1 1-2 1-3z" class="C"></path><path d="M209 127h1 0 4 1c0 1 0 2-1 2v1 1c-1 0-1-1-2-1v1h-2v1c-1 0-1 0-1-1v-1-1-2z" class="H"></path><path d="M210 127h4 1c0 1 0 2-1 2h-1c-1 0-2-1-3-2z" class="b"></path><path d="M213 122c1 1 1 1 2 1h0l2 1h1v1h-1-1c0 1 0 1-1 2h-1-4 0l1-2-1-1c1 0 2-1 3-2z" class="P"></path><path d="M221 133h1v3h-6-3-15-1c-3 0-9 1-11 0v-3h3 8 16 8z" class="j"></path><path d="M227 90h3v1h1v1c0 1 1 0 1 2h-2-1v1c1 1 2 1 3 2-1 0-1 1-1 1v2h1v2c1 1 1 1 1 2v1c-1 2 0 3 0 4 1 1 1 4 1 5l-1 1c-1-1-2-1-3-1 0-1-1-1-1-1v2c-1 0-1 0-1 1h-1l-1 1v4c-1 0-2-1-2-1-2 0-2 1-3 2h0c-1 0-2-1-2-1h-2l-1-1h-1c0 1 1 2 0 3h0c-1 0-1 0-2-1h-1c-1-1-2-1-3-2h0c1 0 2 0 2-1l1-2 2-2c0-2-1-2-1-4h0 0c1 0 1-1 2-2l1-2 3-1v-2h1v1h1l1-1h2c2 0 3 1 5 2 1 2 3 4 4 6v1-1h0c0-1 0-2-1-3 0-1-1-2-1-3s-1-1-1-1c-1-1-1-2-1-2h-2c-1 0-2-1-2-1h-3l-1-1c-1-1-1-1-2-1 0-1-1-1-1-2h-1v-1c1-1 1-3 2-3h2c1 1 1 0 1 1 1 0 2 0 3 1h0c1 0 2 0 3-1h0v-1h-1c-1 0-2-1-3-2v-1h1c1 0 1 0 2-1z" class="K"></path><path d="M227 90h3c-1 1-2 1-3 1v-1z" class="C"></path><path d="M227 109h1c1 1 1 1 1 2h-1c-1 0-1 0-1-1v-1h0z" class="H"></path><path d="M229 106c1 2 3 4 4 6-1-1-2-1-2-2-1-1-1-1-1-2l-2 1h-1l1-1c0-1 0-1 1-1v-1z" class="F"></path><path d="M220 96h2v1c1 1 1 2 2 2v-2h0 1v1c0 1-1 1-1 2h-2c-1-1-1-1-1-3h-1v-1z" class="Q"></path><path d="M222 102h2l1-2c1 1 1 1 2 1s1 0 2 1 1 0 0 1c1 1 3 2 3 3v1 2c0-1-1-2-1-3s-1-1-1-1c-1-1-1-2-1-2h-2c-1 0-2-1-2-1h-3z" class="J"></path><path d="M222 104h2v1c0 1 1 1 1 1l1 1c0 1-1 1-2 2h0v-1-1l-2 2h0-1c0-1-1-2-2-3 1-1 2 1 3-1h-1l1-1z" class="M"></path><path d="M216 107l1 1 1 1-1 2 1 1h2v1l-2 2h-1l-1-1h-1l-1 1c0-2-1-2-1-4h0 0c1 0 1-1 2-2l1-2z" class="C"></path><path d="M216 107l1 1 1 1-1 2-1-1h-1c0 1 0 1-1 2l-1-1c1 0 1-1 2-2l1-2zm-1 7h1l1 1h1 1c1 0 1 0 1-1h1v3h1s1-1 2-1h0v1h-1c0 1 0 1 1 1 0 1 0 1 1 1l-1 1c-2 0-2 1-3 2h0c-1 0-2-1-2-1h-2l-1-1h-1c0 1 1 2 0 3h0c-1 0-1 0-2-1h-1c-1-1-2-1-3-2h0c1 0 2 0 2-1l1-2 2-2 1-1z" class="M"></path><path d="M212 122c1-1 2-1 3-2 0 1 1 2 0 3h0c-1 0-1 0-2-1h-1z" class="X"></path><path d="M215 114h1l1 1h1 1c1 0 1 0 1-1h1v3 3h-1v-1c-2 0-1 1-2 1v-1l1-1v-1h-3l-1 1c-2 0-2-1-2 1l-1 1-1-1 1-2 2-2 1-1z" class="H"></path><path d="M215 114h1l1 1v1h-2v-2z" class="e"></path><path d="M221 122h0c1-1 1-2 3-2 0 0 1 1 2 1h0c1 0 1 0 2 1h1s1-1 1-2l2 1c-1 1-1 2 0 3h0c0 1 0 1 1 2l3 3h0c1 6-1 12 0 17v11s0 1-1 1c0 1-1 0-2 0-2 0-5 1-7 0l1-1h1c0 1 1 1 2 1l1-1c1 1 2 1 2 1h1 1c1-1 0-4 0-6 1-1 0 0 0-1v-3h-1 0c-1 1-2 2-2 3h-1-2-1v-1-2l-1-1-1-1 1-1v-2l-1-1v1l-1 1v-1-1-1h0v-1h-2c1-4 2-5 2-9-1 1-1 1-2 1l-1 1h-1c-1-1-3-1-4-1h0l-3-1v-1-1c1 0 1-1 1-2 1-1 1-1 1-2h1 1v-1h-1l-2-1c1-1 0-2 0-3h1l1 1h2s1 1 2 1z" class="f"></path><path d="M230 120l2 1c-1 1-1 2 0 3-1 0-2-1-3-1s-2-1-3-2c1 0 1 0 2 1h1s1-1 1-2z" class="C"></path><path d="M222 125c1-1 2-2 3-2s1 1 2 1l-1 2c-2 0-2 0-4-1z" class="d"></path><path d="M230 136h1 2 0 0 1c0 1 0 1-1 2 0 0-1 0-2 1-2 1-3 1-6 2v-1l1-1c1 0 1-1 2-1l2-2z" class="E"></path><path d="M215 120h1l1 1h2s1 1 2 1c-1 0-1 1-2 2v1c-1 1-2 3-4 3h0v1l-1 1v-1c1 0 1-1 1-2 1-1 1-1 1-2h1 1v-1h-1l-2-1c1-1 0-2 0-3z" class="C"></path><path d="M227 145c2 0 4 1 7 2v1h0c-1 1-2 2-2 3h-1-2-1v-1-2l-1-1-1-1 1-1z" class="E"></path><path d="M219 129c1-2 1-3 3-4 2 1 2 1 4 1 1 0 2 0 3 1l-1 1h-1c-1 1-1 1-1 2l-1 1c-1 1-1 1-2 1l-1 1h-1c-1-1-3-1-4-1h0l2-3z" class="P"></path><path d="M219 129h0c2-1 3-2 3-3h1v1h1c-1 0-1 1-1 1v1 3l-1 1h-1c-1-1-3-1-4-1h0l2-3z" class="S"></path><path d="M227 124c1 1 2 1 2 2 1 0 2 2 3 3 0 1-2 2-2 3h1c0 2-1 3-1 4l-2 2c-1 0-1 1-2 1l-1 1h-2c1-4 2-5 2-9l1-1c0-1 0-1 1-2h1l1-1c-1-1-2-1-3-1l1-2z" class="H"></path><defs><linearGradient id="F" x1="211.824" y1="95.134" x2="240.209" y2="103.171" xlink:href="#B"><stop offset="0" stop-color="#181310"></stop><stop offset="1" stop-color="#352b21"></stop></linearGradient></defs><path fill="url(#F)" d="M236 81c1 3 0 7 0 10v21c-1 2 0 8 0 10h-3l-1-1h0l-2-1c0 1-1 2-1 2h-1c-1-1-1-1-2-1h0v-4l1-1h1c0-1 0-1 1-1v-2s1 0 1 1c1 0 2 0 3 1l1-1c0-1 0-4-1-5 0-1-1-2 0-4v-1c0-1 0-1-1-2v-2h-1v-2s0-1 1-1c-1-1-2-1-3-2v-1h1 2c0-2-1-1-1-2v-1h-1v-1h-3c-1 1-1 1-2 1h-1v1c1 1 2 2 3 2h1v1h0c-1 1-2 1-3 1h0c-1-1-2-1-3-1 0-1 0 0-1-1h-2c-1 0-1 2-2 3s-1 1-1 2c-1-1-2-2-3-4l-2-2c-1 1-6 3-8 3h-3c-2 1-4 3-7 3h0c2-1 4-2 7-3l1-3 30-11c2 0 4 0 5-1z"></path><path d="M230 116c1 0 2 1 3 1 0 1 0 2-1 3v1h0l-2-1v-1l1-1-1-1v-1z" class="d"></path><path d="M230 119l1-1c0 1 1 2 1 2v1h0l-2-1v-1z" class="K"></path><path d="M223 87h1c-1 1-2 2-3 4-1 0-1 1-3 1h-4c-1 1-2 1-3 1 1-1 3-2 4-2 3 0 6-2 8-4z" class="e"></path><path d="M228 116h2v1l1 1-1 1v1c0 1-1 2-1 2h-1c-1-1-1-1-2-1h0v-4l1-1h1z" class="H"></path><path d="M228 116h2v1l1 1-1 1h0c-1 0-1-1-1-2h-1c0 1 0 1-1 2h0l-1-2 1-1h1z" class="C"></path><path d="M201 95l6-3c1 0 2 0 3-1h1l5-2h2c1-1 3-2 5-2-2 2-5 4-8 4-1 0-3 1-4 2-2 2-7 3-10 2z" class="M"></path><path d="M201 93l30-11c0 1-1 2-2 3h-1c-1 0-3 1-4 2h-1c-2 0-4 1-5 2h-2l-5 2h-1c-1 1-2 1-3 1l-6 3-1 1 1-3z" class="d"></path><path d="M262 75c11-1 21-2 32-2 5 0 10-1 15 0v85c-14 0-25 5-37 11-4 2-7 4-10 6-1-2 0-4-1-5h-1v-3c-1-1-2-1-2-1l-1-3v-1-1c0-1 1-1 1-1v-1l-1 1v1h-5-5c-1 0-2 0-3 1h0-2v-3h-6v-2-11c-1-5 1-11 0-17h0l-3-3c-1-1-1-1-1-2h0c-1-1-1-2 0-3h0l1 1h3c0-2-1-8 0-10V91c0-3 1-7 0-10 2-1 4-2 6-2l10-2c1-1 3-1 4-1s1 0 2-1h4z"></path><path d="M260 127h1c0 3 0 7 1 10 1-2 1-4 1-6v9 14c-1 7-2 14-1 21-1-2 0-4-1-5h-1v-3c-1-1-2-1-2-1l-1-3v-1-1c0-1 1-1 1-1h2c1-2 0-8 0-10v-23z" class="I"></path><path d="M258 160h2v7c-1-1-2-1-2-1l-1-3v-1-1c0-1 1-1 1-1z" class="J"></path><path d="M277 130c1-1 2-1 3 0 2 0 3 2 4 3 1 2 1 4 0 5 0 1-2 2-4 3h-2c-2 0-4-1-5-3 0-1-1-2 0-4 0-2 2-3 4-4z" class="c"></path><defs><linearGradient id="G" x1="266.917" y1="108.149" x2="256.592" y2="106.288" xlink:href="#B"><stop offset="0" stop-color="#c1b789"></stop><stop offset="1" stop-color="#f0c899"></stop></linearGradient></defs><path fill="url(#G)" d="M262 76c1 3 1 7 1 11v25 12 7c0 2 0 4-1 6-1-3-1-7-1-10h-1v-7-17-15c0-3-1-6 0-9 1-1 1 0 2-1v-2z"></path><path d="M260 79l1 1v2 7 38h-1v-7-17-15c0-3-1-6 0-9z" class="V"></path><path d="M236 81c2-1 4-2 6-2l10-2c1-1 3-1 4-1s1 0 2-1h4v1 2c-1 1-1 0-2 1-1 3 0 6 0 9v15 17 7 23c0 2 1 8 0 10h-2v-1l-1 1v1h-5-5c-1 0-2 0-3 1h0-2v-3h-6v-2-11c-1-5 1-11 0-17h0l-3-3c-1-1-1-1-1-2h0c-1-1-1-2 0-3h0l1 1h3c0-2-1-8 0-10V91c0-3 1-7 0-10z" class="c"></path><path d="M242 159h8c2 0 6-1 8 0l-1 1v1h-5-5c-1 0-2 0-3 1h0-2v-3z" class="V"></path><path d="M232 121l1 1h3c0-2-1-8 0-10v24c0 3 1 7 0 10-1-5 1-11 0-17h0l-3-3c-1-1-1-1-1-2h0c-1-1-1-2 0-3h0z" class="X"></path><path d="M110 295v-3l1 1 1 5-1 1v4l1 6c-1 2-1 4-1 5v3 4h0v5c1 0 2 0 3 1 0 1 0 2 1 2 0 1 1 1 1 2v1h-2c0 2 0 2-1 3-1 0-1-1-1-1h-1c0 1 0 2 1 3v1l1 1c-1 0-1 1-1 1v45l-1 14c0 2 1 5 0 7v59h19c2 0 6 0 8-1v1h-3c-2 1-5 0-7 1h-10-6l-1 1c-1 0-1 0-1-1h-3-34-4 0-2c-1 1-3 0-5 0-1-1-1-2-1-3 0-4-1-8-1-12-1-4-1-8-1-13l-1-50v-21l1-28h1v-5-1c1-2 0-4 1-6h0l-1-2 1-1 1 1v-1c-1-1 0 0 0-1v-2h-1v-3-3l-1-1s1-1 1-2c1-2-1-1-1-2s1-1 0-2v-4-2-1-2-3h8 0 4 21 10c3 0 5 0 7-1z"></path><path d="M65 374c1 0 1 1 1 1h2 0l-1 1h0c-2 0-2-1-3-1 1-1 0-1 1-1z" class="i"></path><path d="M61 463l1 1c2 1 4 1 6 1v1h-1c-1 1-3 0-5 0-1-1-1-2-1-3z" class="j"></path><path d="M72 339h2c1 0 1 0 2-1 0-1-1-1-1-2h1 1c0 1 0 2 1 3h0 2l-1 1H69l1-1h2z" class="V"></path><path d="M88 417h0l2 1h0 1l-2 2s-1 1-2 1h-1l-2-2h0 1l3-2zm-24-36h5c3 0 12 0 13 1-5 0-11 1-15 0h-1c-1 0-2 0-2-1z" class="f"></path><path d="M63 418c1 0 4 0 5 1v2l-1 2 2 2h-1l-1-1h-1c-1 0-2 0-3-1v-1h2s1 0 1-1v-1l-3-2z" class="F"></path><path d="M98 345c-1 1-1 1-1 2 1 2 2 4 4 5h1v1c-4-1-5-4-7-5-1 0-2-1-2-2 1-1 1-1 2-1s2 0 3-1h0v1z" class="E"></path><path d="M92 338h5c-1 1-1 1-1 2h0-17l1-1h3 3c2-1 4 0 6-1z" class="a"></path><path d="M68 465h5 35 0-6 3l-1 1h0 3-34-4 0-2 1v-1z" class="L"></path><path d="M68 465h5 3v1h-3-4 0-2 1v-1z" class="V"></path><path d="M98 344c2 0 3 0 5-1 2 1 3 2 4 3h0c0 1 1 1 1 2h2v-2 16 12c0 2 1 4 0 6v-9-17l-1-1h-3c-1 0-2 0-3-1h0c-1-1-1-2-2-3h0v-2l1-1c-2-1-2-1-4-1v-1z" class="F"></path><path d="M106 353c-1-1-1-2-1-3h2c1 1 2 1 2 2v1h-3z" class="K"></path><path d="M110 362l1-1c1 11 0 22 0 33v12 59h19c2 0 6 0 8-1v1h-3c-2 1-5 0-7 1h-10-6l-1 1c-1 0-1 0-1-1h-3-3 0l1-1h-3 6 0 1c0-1 1-1 1-1v-4-11-69c1-2 0-4 0-6v-12z" class="h"></path><path d="M69 325l1-1h0 1c0 1 0 1 1 2l1 1v1l4-1v1h2v2c0 1-1 2-2 3v1 1h0v1h-1-1c0 1 1 1 1 2-1 1-1 1-2 1h-2-2l-1 1H59v33c0 5 0 11-1 15v-21l1-28h1v-5-1c1-2 0-4 1-6h0l-1-2 1-1 1 1v-1h1v1h1 1c1 0 1 0 2 1 0-1 0-2 1-2h0c1 0 1 0 1 1z" class="Y"></path><path d="M67 333c0-1 0-1 1-2v-1c0-1 2-3 4-4l1 1v1 1h0l-1-1-2 2c-1 1-1 1-3 2v1z" class="b"></path><path d="M60 334h2c0-1 0-1 1-1l1 1c0 1 0 3 1 4v1c-2 0-3-1-5 0v-5z" class="H"></path><path d="M65 325c1 0 1 0 2 1 0-1 0-2 1-2h0c1 0 1 0 1 1l1 1h-2v2h-1v2c-1 0-2 1-2 2v1 1h-1l-1-1c-1 0-1 0-1 1h-2v-1c1-2 0-4 1-6h0l-1-2 1-1 1 1v-1h1v1h1 1z" class="T"></path><path d="M63 329h0 1v-1-2h0 1v2h2v2c-1 0-2 1-2 2v1 1h-1l-1-3v-2z" class="G"></path><path d="M61 324l1 1h0c0 1 1 1 1 1v1c-1 1-1 1 0 2v2l1 3-1-1c-1 0-1 0-1 1h-2v-1c1-2 0-4 1-6h0l-1-2 1-1z" class="d"></path><path d="M77 327v1h2v2c0 1-1 2-2 3v1 1h0v1h-1-1c0 1 1 1 1 2-1 1-1 1-2 1h-2-2-2-2l1-1c-1-1-1-2-1-3s0-2 1-2v-1c2-1 2-1 3-2l2-2 1 1h0v-1l4-1z" class="e"></path><path d="M68 339l-1-1 3-3c1 1 2 1 2 2v2h0-2-2z" class="F"></path><path d="M67 332c2-1 2-1 3-2l2-2 1 1v9l-1 1v-2c0-1-1-1-2-2 0 0 0-1 1-1h0v-2h-1-3z" class="X"></path><path d="M77 327v1h2v2c0 1-1 2-2 3v1 1h0v1h-1-1c0 1 1 1 1 2-1 1-1 1-2 1h-2 0l1-1v-9h0v-1l4-1z" class="L"></path><path d="M77 328h2v2c0 1-1 2-2 3v-5z" class="O"></path><path d="M77 335h-3l-1-1c1 0 2 0 3-1-1-1-1-1-2-1h0l1-1v-2l1-1h0c1 2 1 5 1 6v1z" class="V"></path><path d="M110 295v-3l1 1 1 5-1 1v4l1 6c-1 2-1 4-1 5v3 4h0v5c1 0 2 0 3 1 0 1 0 2 1 2 0 1 1 1 1 2v1h-2c0 2 0 2-1 3-1 0-1-1-1-1h-1c0 1 0 2 1 3v1l1 1c-1 0-1 1-1 1v45l-1 14c0 2 1 5 0 7v-12c0-11 1-22 0-33l-1 1v-16-6H96h0c0-1 0-1 1-2h-5c-2 1-4 0-6 1h-3-3-2 0c-1-1-1-2-1-3v-1h0v-1-1c1-1 2-2 2-3v-2h-2v-1l-4 1v-1l-1-1c-1-1-1-1-1-2h-1 0l-1 1c0-1 0-1-1-1h0c-1 0-1 1-1 2-1-1-1-1-2-1h-1-1v-1h-1c-1-1 0 0 0-1v-2h-1v-3-3l-1-1s1-1 1-2c1-2-1-1-1-2s1-1 0-2v-4-2-1-2-3h8 0 4 21 10c3 0 5 0 7-1z" class="l"></path><path d="M84 320l2-1h0c0 2 0 2-1 3 0-1-1-1-1-2zm25 12l2-2h0v3l-1-1h-1zm2-11c0-1-1-1-1-1v-3c0-1 1-1 1-2v2 4z" class="L"></path><path d="M71 310c1-1 1-1 1-2h1v2l1 1h-2l-1-1z" class="U"></path><path d="M74 301l-1-1c1-1 0-2 0-3 2 2 3 4 3 7l-1-1c0-1-1-2-1-2zm30 9h2v1l-1-1v3 1c1 0 1-1 2-1l1 1-2 2h0c-1 0-2-2-2-2-1-1 0-3 0-4z" class="N"></path><path d="M91 318h0c1 1 1 2 2 3 0-1 0 0 1-1 1 1 1 2 2 3l-1 1h-1c-2-1-3-1-3-3-1-1-1-2 0-3z" class="L"></path><path d="M96 334l1-1h2c3 3 4 4 7 4l1 1c-1 1-4 0-5 0-1-1-2-1-3-2l-3-2z" class="C"></path><path d="M104 310c-1-2 0-3 0-4v-1l1 1 1 2c0 1 1 1 1 1 1 1 0 2 1 3h1-1v1 1l-1-1c-1 0-1 1-2 1v-1-3l1 1v-1h-2z" class="U"></path><path d="M104 310c-1-2 0-3 0-4v-1l1 4h2v2h-1v-1h-2z" class="T"></path><path d="M65 300h0 1v-1-2h1v1c0 1 1 1 1 2-1 1-1 1-2 1h0-1c-1 1-1 3-1 5v-1c0-1 0-1-1-1h-1v1h1v1h-2v-2h-1v-2h1c2-1 3-1 4-2z" class="U"></path><path d="M60 304h1v2h2v-1h-1v-1h1c1 0 1 0 1 1v1c0 2 1 3 0 5l-1-1-1 1v1h-1c1-2-1-1-1-2s1-1 0-2v-4z" class="I"></path><path d="M74 301s1 1 1 2l1 1h1l-1 1v1 2l1 1c-1 0-2 1-2 2h-1l-1-1v-2-1h1s-1 0-1-1v-2c0-1 1-2 1-3z" class="L"></path><path d="M73 308v-1h1l1 1c0 1-1 1-2 2v-2z" class="V"></path><path d="M60 296h8-1-1c-1 0-1 1-2 1 0 1 1 2 1 3-1 1-2 1-4 2h-1v-1-2-3z" class="a"></path><path d="M77 315h1c2 2 4 4 6 5 0 1 1 1 1 2h0c-1 0-1 1-1 2l-5-4-1 1h-1l1-1 1-1c1-1-1-1-1-2s-1-1-1-2z" class="Y"></path><path d="M111 326c1 0 2 0 3 1 0 1 0 2 1 2 0 1 1 1 1 2v1h-2c0 2 0 2-1 3-1 0-1-1-1-1h-1v-1-3-4z" class="B"></path><path d="M115 329c0 1 1 1 1 2v1h-2v-1l1-2z" class="I"></path><path d="M110 332v1l-2 1h-1l-2-1s-1-1-1-2v-1h-1v2h-1c-1 0-1-1-1-2l1-1h3 1v-1-1c0-1 0-2 1-2h1l-1 1c1 1 2 1 2 2v1c-1-1-1-1-2-1 0 1 0 2-1 2 1 1 1 2 3 2h0 1z" class="U"></path><path d="M64 306h1 2 0l4 4 1 1c0 1 0 2 1 3h-1c-1 0-1 0-2-1h0-2-1l-2-2h-1c1-2 0-3 0-5z" class="D"></path><path d="M76 305h1c0 1 0 2 1 3 0 1-1 1-1 2 1 0 1 1 1 2h-1v3c0 1 1 1 1 2s2 1 1 2l-1 1-1 1v5 1l-4 1v-1-2c0-2 0-4 1-6l1-1c0-1-1-1-1-2-1 0 0-1-1-2h0c-1-1-1-2-1-3h2 1c0-1 1-2 2-2l-1-1v-2-1z" class="V"></path><path d="M78 321l1-1 5 4c0-1 0-2 1-2 2 2 4 3 6 5s5 5 8 6h-2l-1 1-11-7v2h-1l-1-1-1 1h-2l-1 1v-2h-2v-1-1-5h1z" class="H"></path><path d="M85 322c2 2 4 3 6 5-1 0-2 0-3-1h-1c-1-1-2-1-3-2 0-1 0-2 1-2z" class="O"></path><path d="M77 321h1c0 1 1 1 1 1l6 5v2h-1l-1-1-1 1h-2l-1 1v-2h-2v-1-1-5z" class="T"></path><path d="M77 321h1c0 1 1 1 1 1v1c0 1 1 1 1 2s-1 1 0 2h2v2h-2l-1 1v-2h-2v-1-1-5z" class="D"></path><path d="M77 326h2v2h-2v-1-1z" class="Y"></path><path d="M62 312v-1l1-1 1 1h1l2 2h1 2 0c1 1 1 1 2 1h1 0c1 1 0 2 1 2 0 1 1 1 1 2l-1 1c-1 2-1 4-1 6v2l-1-1c-1-1-1-1-1-2h-1 0l-1 1c0-1 0-1-1-1h0c-1 0-1 1-1 2-1-1-1-1-2-1h-1-1v-1h-1c-1-1 0 0 0-1v-2h-1v-3-3l-1-1s1-1 1-2h1z" class="B"></path><path d="M62 312v-1l1-1 1 1h1l2 2h1 2 0c1 1 1 1 2 1h1 0c1 1 0 2 1 2 0 1 1 1 1 2l-1 1-1-1-2-1-1-1c-1 0-2 0-3-1v1h-1c-1 0-1-1-2-1v1h-1v-1c0-1 0-2-1-3z" class="O"></path><path d="M62 312v-1l1-1 1 1h1l-1 2 1 1-1 1h-1c0-1 0-2-1-3z" class="g"></path><path d="M73 314c1 1 0 2 1 2 0 1 1 1 1 2l-1 1-1-1-2-1-1-1c-1 0-2 0-3-1v-1c1 0 3 2 4 2h2v-2z" class="U"></path><path d="M62 321v-1-1c1-1 1-1 2-1 1 1-1 1 0 3l1-1h1v1h0c1 0 1-1 2-1s1-1 2-1v-1c1 0 1 0 2 1l1-1 1 1c-1 2-1 4-1 6v2l-1-1c-1-1-1-1-1-2h-1 0l-1 1c0-1 0-1-1-1h0c-1 0-1 1-1 2-1-1-1-1-2-1h-1-1v-1h-1c-1-1 0 0 0-1v-2z" class="G"></path><path d="M63 324c1-1 3-2 4-2h2v1l-1 1h0c-1 0-1 1-1 2-1-1-1-1-2-1h-1-1v-1z" class="a"></path><path d="M65 325c1-1 1-1 1-2h3l-1 1h0c-1 0-1 1-1 2-1-1-1-1-2-1z" class="B"></path><path d="M85 327l11 7 3 2c1 1 2 1 3 2-1 0-2 1-3 0h-2-5c-2 1-4 0-6 1h-3-3-2 0c-1-1-1-2-1-3v-1h0v-1-1c1-1 2-2 2-3l1-1h2l1-1 1 1h1v-2z" class="a"></path><path d="M81 331c1-1 2-1 3-1v1l-1 1 1 1h1v1c-1 1-1 1-2 1h0c0-1 0-1-1-1 0-1-1-1-1-1l1-1-1-1z" class="T"></path><path d="M99 336c1 1 2 1 3 2-1 0-2 1-3 0h-2-5 2v-1-1h0l1 1c1 0 3 0 4-1z" class="B"></path><path d="M85 336h1 1c1-1 2-1 2-1h1c1 1 2 1 4 1v1 1h-2c-2 1-4 0-6 1v-1h0c0-1 0-1-1-2z" class="g"></path><path d="M85 334v2c1 1 1 1 1 2h0v1h-3-3-2v-1c1-1 1-2 2-2 1-1 2-1 3-1s1 0 2-1z" class="Z"></path><path d="M80 336c1 0 2 0 3 1v2h-3-2v-1c1-1 1-2 2-2z" class="C"></path><path d="M79 330l2 1 1 1-1 1s1 0 1 1c1 0 1 0 1 1h0c-1 0-2 0-3 1-1 0-1 1-2 2v1h0c-1-1-1-2-1-3v-1h0v-1-1c1-1 2-2 2-3z" class="D"></path><path d="M77 335c1 0 3-1 3-1l1 1h2 0c-1 0-2 0-3 1-1 0-1 1-2 2v1h0c-1-1-1-2-1-3v-1z" class="b"></path><path d="M393 540l1 1v-2h1c0 4 0 7 3 10 3 2 5 3 9 3l4-1 2-1c1 0 1 1 2 1 2-1 4-1 5-1h9 1c1 2 0 4 0 5v11 36l1-1c0 3-1 15 0 17h-1l-31 15c-2 2-4 2-7 3-1 0-2 1-4 1h0c-6 2-12 4-19 5-12 3-24 4-36 5-3 0-7 1-9 0h-1v-13h1v-1c0-3-1-8 0-12v-3-9l-1-1v-51h1c10 0 20-2 31-4 3 0 6-1 9-2l28-11h1z"></path><path d="M411 551l2-1c1 0 1 1 2 1-1 1-1 1-2 1s-1-1-2-1z" class="h"></path><path d="M407 552l4-1c1 0 1 1 2 1-2 1-3 1-5 2 0-1-1-1-1-2z" class="V"></path><path d="M404 569c4-1 9-1 12 0h1l-1 1-2 1-1 1v-1l-1-1h0c1 0 1 0 2-1-3-1-6 0-10 0z" class="J"></path><path d="M352 576c1 0 2 0 3 1 0 0 1 0 1 1 0 0 0 1 1 1h12 5-26 3l1-1v-1-1z" class="S"></path><path d="M393 540l1 1v-2h1c0 4 0 7 3 10 3 2 5 3 9 3 0 1 1 1 1 2-2 0-5 0-7-1-1-1-3-1-4-2h-9l9-1c-3-3-3-6-4-10z" class="L"></path><path d="M381 569c7-1 15 0 22 0h0-5l-2 2h0 2 0v1s-1 1-1 2v1h1 0l-1 1v3h-4c0-2-1-2-2-3 0-1 2-2 2-4 1-1 1-2 0-3h-7-5z" class="S"></path><path d="M348 569h28v1h-19c0 3-1 6 0 9-1 0-1-1-1-1 0-1-1-1-1-1-1-1-2-1-3-1 0-1 0-1-1-2s-2-2-3-2c0-1 1-2 2-2l-2-1z" class="m"></path><path d="M353 573h2v1 2h-1c0-1-1-2-1-3z" class="O"></path><path d="M350 570h2l2 1c0 1-1 2-2 3h-1c-1-1-2-2-3-2 0-1 1-2 2-2z" class="Y"></path><path d="M386 569h7c1 1 1 2 0 3 0 2-2 3-2 4 1 1 2 1 2 3h-1-6s1 0 1-1 0-2-1-4h0v-4-1z" class="O"></path><path d="M386 570c0 1 1 2 1 2v2h1l1 1v-1c1 0 1 1 2 1v1h0c1 1 2 1 2 3h-1-6s1 0 1-1 0-2-1-4h0v-4z" class="m"></path><path d="M389 575v-1c1 0 1 1 2 1v1h0c-1 0-1 1-2 1h0l-1-1 1-1z" class="D"></path><path d="M391 576h0 0c1 1 2 1 2 3h-1c-1-1-1-1-1-3z" class="Y"></path><path d="M409 572c1 0 2-1 3-2h0l1 1v1l1-1v1l1 1 1 1c1 1 2 1 2 2v1l-1-1c-1 1-1 2-2 3h-7-6 2c0-1 1-1 1-1h1v-1-1c1-1 2-2 3-4z" class="i"></path><path d="M409 576l1-1c0-1 1-2 2-3v2c1 0 1 0 1 1-1 1-2 1-4 1z" class="E"></path><path d="M409 572c1 0 2-1 3-2h0l1 1v1h-1c-1 1-2 2-2 3l-1 1c0 1-1 2-1 3h-6 2c0-1 1-1 1-1h1v-1-1c1-1 2-2 3-4z" class="W"></path><path d="M330 579h-1c-1 0-2 1-3 0-1-2-1-8 0-9l1-1c2 1 3 1 4 1h1v1c1 1 1 1 1 2v1 5h-1-1-1 0 0z" class="R"></path><path d="M330 579c-1 0-2 0-3-1v-3c0-1 0-1 1-2l1 1v2c0 1 0 1 1 2v1h0z" class="U"></path><path d="M376 569h5 5v1 4h0c1 2 1 3 1 4s-1 1-1 1h-12l1-2c0-1 0-2 1-3v-4-1z" class="I"></path><path d="M404 569c4 0 7-1 10 0-1 1-1 1-2 1-1 1-2 2-3 2-1 2-2 3-3 4v1 1h-1s-1 0-1 1h-2-5v-3l1-1h0-1v-1c0-1 1-2 1-2v-1h0-2 0l2-2h5 0 1z" class="Y"></path><path d="M398 575h0c1-1 2-1 3-2h2 0l1 1h-2l-2 2-2-1h0z" class="I"></path><path d="M403 573s0-1 1-2c1 0 2-1 2-1l2 1c-1 1-2 2-4 3 2-1 3-2 5-2h0c-1 2-2 3-3 4h-2l1-1-1-1-1-1z" class="B"></path><path d="M404 574l1 1-1 1h2v1 1h-1s-1 0-1 1h-2-5v-3l1-1 2 1 2-2h2z" class="O"></path><path d="M357 579c-1-3 0-6 0-9h19v4c-1 1-1 2-1 3l-1 2h0-5-12z" class="B"></path><path d="M370 575v-1c-1 0-1 0-1-1 1 0 2-1 2-1l1-1 1 1c-1 1-2 2-2 3v1h-1v-1z" class="W"></path><defs><linearGradient id="H" x1="369.185" y1="576.261" x2="373.85" y2="575.124" xlink:href="#B"><stop offset="0" stop-color="#7e6f59"></stop><stop offset="1" stop-color="#968468"></stop></linearGradient></defs><path fill="url(#H)" d="M373 572c1 1 0 2 1 4 1-1 1 0 1-1 0 0 0-1 1-1-1 1-1 2-1 3l-1 2h0-5c0-1 1-2 1-4v1h1v-1c0-1 1-2 2-3z"></path><path d="M327 569h8 13l2 1c-1 0-2 1-2 2 1 0 2 1 3 2s1 1 1 2v1 1l-1 1h-3-16 1v-5-1c0-1 0-1-1-2v-1h-1c-1 0-2 0-4-1z" class="O"></path><path d="M348 572c1 0 2 1 3 2s1 1 1 2v1c-2-1-3-2-3-3-1-1-1-1-1-2z" class="P"></path><path d="M340 570h5l1 1v1h-2c0 1 0 1 1 2 0 1-4 3-5 4v-1-1-1c1-1 1-4 0-5z" class="D"></path><path d="M335 570h4 1c1 1 1 4 0 5v1 1 1 1h-7v-5-1c0-1 0-1-1-2v-1h-1 4z" class="T"></path><path d="M335 570h4 1c1 1 1 4 0 5l-1 1c-1 0-1 0-2-1-1 0-1 0-1-1v-1-1c0-1 0-2-1-2z" class="R"></path><path d="M335 570h4c-1 0-1 1-1 2h-1c0 1 1 1 1 2l-1 1c-1 0-1 0-1-1v-1-1c0-1 0-2-1-2z" class="G"></path><path d="M323 557h1v46l105-1h1l1-1c0 3-1 15 0 17h-1v-9H324l-1-1v-51z" class="c"></path><path d="M324 609h106v9l-31 15c-2 2-4 2-7 3-1 0-2 1-4 1h0c-6 2-12 4-19 5-12 3-24 4-36 5-3 0-7 1-9 0h-1v-13h1v-1c0-3-1-8 0-12v-3-9z"></path><defs><linearGradient id="I" x1="370.242" y1="618.35" x2="344.962" y2="652.167" xlink:href="#B"><stop offset="0" stop-color="#d0b183"></stop><stop offset="1" stop-color="#dbcda5"></stop></linearGradient></defs><path fill="url(#I)" d="M323 634h1v-1c0-3-1-8 0-12v13l60-1h10 5c-2 2-4 2-7 3-1 0-2 1-4 1h0c-6 2-12 4-19 5-12 3-24 4-36 5-3 0-7 1-9 0h-1v-13z"></path><path d="M324 647v-8c1-1 2 0 3 0h8l30-1c7 0 14 1 20-1h3c-6 2-12 4-19 5-12 3-24 4-36 5-3 0-7 1-9 0zm44-570c19 5 39 12 56 22 2 1 5 2 6 3l2 1 5 3 5 4c1 1 4 2 5 3-1 1-1 2-1 3v44 55c0 6 0 14-1 20v7l-1 1c1 1 1 2 2 4h-6-5-13v-1c-3-13-6-27-14-38-1-2-5-6-5-8-6-8-13-15-21-20-4-4-9-7-13-10h-1v-13h0v-22h-1 0c-1-1-1-2-1-3v-2l1-1v-3-9h0l1-40h0z"></path><path d="M367 117h0v11c1 2 0 5 1 7h-1 0c-1-1-1-2-1-3v-2l1-1v-3-9z" class="V"></path><path d="M423 232c1 0 2-1 2 0 1 0 2 7 3 9v1h-1c-2-3-3-7-4-10h0z" class="K"></path><path d="M430 218h1v5 23c1 0 3 0 4 1h-13v-1c2 1 5 0 7 0l1-28z" class="c"></path><path d="M430 164l1 5v8c0-1 1-3 0-4v-1h0c0-1 0-3 1-4v15 2 2c-1 4 0 9 0 13l-1 23v-5h-1v-54z" class="R"></path><path d="M430 102l2 1c-1 4 0 8 0 12v42 11c-1 1-1 3-1 4h0v1c1 1 0 3 0 4v-8l-1-5v-62zm-27 98h0c1-3 0-6 0-9v-35h5v52c-1-2-5-6-5-8z" class="h"></path><path d="M432 103l5 3 5 4c1 1 4 2 5 3-1 1-1 2-1 3v44 55c0 6 0 14-1 20v7l-1 1c1 1 1 2 2 4h-6-5c-1-1-3-1-4-1v-23l1-23c0-4-1-9 0-13v-2-2-15-11-42c0-4-1-8 0-12z"></path><path d="M441 221c0-1 0-2 1-3 0-1 0-1 2-1 1 1 0 4 1 5 1 4 0 9 0 13v7l-1 1c1 1 1 2 2 4h-6v-1c1-1 1-3 1-4-1-2 0-18 0-21z" class="N"></path><path d="M441 242l1 1s1 0 1 1l1-1-1-1h1v1c1 1 1 2 2 4h-6v-1c1-1 1-3 1-4z" class="U"></path><path d="M436 178c1 9 0 18 0 27v19l-1 14v8c2 1 4 0 5 0v1h-5c-1-1-3-1-4-1v-23l1-23c0-4-1-9 0-13v-2-2 1c1 1 0 1 1 1v-1h0c1 1 1 2 2 2h0v-5c1 0 1-2 1-3z" class="S"></path><path d="M432 185c1 1 2 1 3 2h0c-1 1-1 1-3 1v-1-2z" class="g"></path><path d="M432 103l5 3c-1 2-1 3-1 4-1 1 0 1 0 2l-1 1-2-2h-1v3l1-1 1 1h1 1v64c0 1 0 3-1 3v5h0c-1 0-1-1-2-2h0v1c-1 0 0 0-1-1v-1-15-11-42c0-4-1-8 0-12z" class="W"></path><path d="M442 110c1 1 4 2 5 3-1 1-1 2-1 3v44 55c0 6 0 14-1 20 0-4 1-9 0-13-1-1 0-4-1-5-2 0-2 0-2 1-1 1-1 2-1 3l1-111z" class="B"></path><path d="M209 303c0 1 0 2 1 3v56l-1 76 1 16 1 7v3l-1 1h0 0c2 1 2 3 2 4v5c-1 2-1 3-2 4 1 1 2 2 2 4l1 1c0 2 1 4 2 6 0 1 1 2 1 4v-1c0-1 0-1 1-2 1 4 3 7 4 10 3 5 6 10 9 14s6 8 10 12c-2 1-5 0-8 1v-1l-18 1h-1c-3-1-7-1-10-1h-3-13-13-62c-1-2 0-6 0-8v-20-8-2c-1-1-1-3-1-4v-3-1h-2 0c1 0 1-1 1-1v-5c1-2 1-4 1-7l1-1h6 10c2-1 5 0 7-1h3 1 4 8 9c1-1 2-1 3-1 1 1 2 1 3 1h6 12 11 0c-3-1-7 0-10-1l1-40v-33-11-27c0-4 0-9 1-13h1 2v-1h11l8-36z"></path><path d="M126 520c1 0 2 1 2 1v1l-1 1h-1-1c0-2 0-2 1-3z" class="C"></path><path d="M204 474v-2h1l1-1 1 2h1c1 0 1 0 2 1h-1c-2-1-2-1-3 0l-1-1-1 1h0z" class="i"></path><path d="M139 471h8-1c-1 0-4 0-4 1v1h0-1-1c-1 0-1 0-2-1l1-1z" class="I"></path><path d="M131 489h2 1c-1 1-2 2-3 2-1 1-2 1-3 0h0 1c0-1 1-1 1-2h1z" class="E"></path><path d="M138 503c1 1 1 1 2 1l-2 3c-1 0-2 0-3 1 1-2 2-3 3-5z" class="V"></path><path d="M155 485h6l2 1-1 1c-2-1-4 0-6-1l-1-1z" class="E"></path><path d="M126 487h1l1 1h2v1c0 1-1 1-1 2h-1l-1-1h0c-1-1-1-1-1-2v-1z" class="F"></path><path d="M212 469v5c-1 2-1 3-2 4l-1-2v-2h1 0c1 0 1-1 1-1 0-2 0-2 1-4z" class="E"></path><path d="M135 508c1-1 2-1 3-1l-3 9v-1-3c1-1 0-1 0-1v-3z" class="L"></path><path d="M174 497c3-1 6-2 9-2v2l-7 1h-1 0v-1h-1z" class="h"></path><path d="M121 500c1 0 1-1 2-1l1 1 1 1c-1 1-1 1-2 1l-3 1-1-2s1-1 2-1z" class="P"></path><path d="M183 495c3-1 8-1 12 0l-12 2v-2z" class="o"></path><path d="M141 498c1 1 1 2 1 3l-1 2-1 1c-1 0-1 0-2-1h-1v-1l2-2 2-2z" class="l"></path><path d="M131 499h1v2 1h2v1c-1 1-2 1-3 1l-1-1c-1 0-2-1-2-2 1-1 2-2 3-2z" class="F"></path><path d="M207 485c0-1 0-2 1-3 0 2 0 2 1 4l1 1-1 1c0 1 1 2 2 2l1 1 2 2c-3-1-4-1-6-3h-1c-1-1 0-1 0-1v-4z" class="G"></path><path d="M166 501c2-1 5-3 8-4h1v1h0 1c-4 2-9 4-13 6l-1-1c2 0 3-1 4-2z" class="o"></path><path d="M204 474l1-1 1 1c1-1 1-1 3 0v2c-1 1-1 2-1 4v2c-1 1-1 2-1 3h-1c1-2 1-4 0-5h1v-3c0-2-1-1-2-1 0-1 0-2-1-2z" class="J"></path><path d="M124 485h1 0c0 1 0 1 1 2v1c0 1 0 1 1 2h0c0 1-1 1-2 1v2h-1-1l-2-7 3-1z" class="C"></path><path d="M124 485h1 0c0 1 0 1 1 2v1c-1 0-1 1-2 1v-4z" class="H"></path><path d="M149 484h3v1h-4l-1 1 1 1v1h-1l2 1v-1h0l1-1v2c-2 0-4 0-5 1-1 0-1-1-2-1h0-1c-1 0-1-1-2-1 0 0-1 0-1-1h1l1 1c1-1 1-1 2-1 1-1 2-1 3-2 1 0 2 0 3-1z" class="E"></path><path d="M208 482v-2c0-2 0-3 1-4l1 2c1 1 2 2 2 4l1 1c-2 0-2 1-3 2h0l-1 1c-1-2-1-2-1-4z" class="T"></path><path d="M212 482l1 1c-2 0-2 1-3 2h0l-1 1c-1-2-1-2-1-4 1 1 1 1 2 1v-1h2z" class="R"></path><path d="M153 499c1 0 2 2 3 2h1c2 2 3 2 5 2l1 1h-1-3v-1c-2 0-8-1-10 0-1 1-1 1-2 1h-1 0c1-1 3-3 4-3 1-1 2-2 3-2z" class="p"></path><path d="M160 465c1-1 2-1 3-1 1 1 2 1 3 1h6 0-8c-1 1-1 1-2 1-1-1-2 0-3 0h-3v1c-2 0-5 0-8-1h-1c-3-1-5 0-8-1h4 8 9z" class="k"></path><path d="M210 485h0c1-1 1-2 3-2 0 2 1 4 2 6 0 1 1 2 1 4h-2l-2-2-1-1c-1 0-2-1-2-2l1-1-1-1 1-1z" class="L"></path><path d="M210 485c1 1 2 1 3 2l-1 1c-1 0-1 0-2-1l-1-1 1-1z" class="V"></path><path d="M201 495l3-1c1 0 1 0 1 1h2c0 1 0 1 1 2h1l1 1c2 1 5 4 6 5-5 1-10 1-15 1-3 0-6 0-9-1h12c2 0 4 1 5 0h1 0c0-1 0-1-1-2h-2c-1-1-1-2-2-2v-3h-2l-1-1h-1z" class="B"></path><path d="M121 486v-4c-1-2-1-4-1-6 0-1 1-2 1-3-1-1-1 0-1-2h3c0 1-1 1-1 2s0 1-1 2c0 1 0 2 1 3v1l1 1v-2l2 1 1 1h0c1 2 1 3 2 5l2 3h-2l-1-1h-1c-1-1-1-1-1-2h0-1l-3 1z" class="M"></path><path d="M123 478l2 1 1 1h0c1 2 1 3 2 5-1 0-1 0-2-1l-2 1-1-1h0l-1-1c0-1 0-1 1-2v-1-2z" class="C"></path><path d="M123 484h1c0-1 0-1 1-2l1 1v1l-2 1-1-1zm0-6l2 1 1 1h0c-1 1-2 1-3 1v-1-2z" class="e"></path><path d="M155 484l-1-1c-2 0-2 0-3-1h0 3 1l3 1h3 5 0l9 1h1 3l1 1v1c-1 0-2 0-3-1h-3v1h0-3c-2 0-5-1-8 0l-2-1h-6v-1z" class="Z"></path><path d="M155 484l-1-1c-2 0-2 0-3-1h0 3 1l3 1h3 5 0-4c0 1 1 1 2 1h2l1 1c-1 1-4 0-6 0h-6v-1z" class="W"></path><path d="M126 478c1 1 2 1 3 1h1v1h1l-1 1 1 1v1h1 1 0 1l1-1v1l1-1h1c1 1 0 1 0 2h-1v2h0v1l-1 1-1-1-1 1 1 1h-1-2-1v-1l-2-3c-1-2-1-3-2-5h0l-1-1 1-1z" class="p"></path><path d="M130 480h1l-1 1 1 1c0 1 0 1-1 1h-1c-1-1-1-1-1-3h2z" class="Z"></path><path d="M131 489v-1l-1-1c0-1-1-2-1-3h1l1 1h1l1 1s-1 1-1 2v1l1-1 1 1h-1-2z" class="C"></path><path d="M136 482h1c1 1 0 1 0 2h-1v2h0v1l-1 1-1-1-1 1-1 1v-1c0-1 1-2 1-2l-1-1v-2h1 0 1l1-1v1l1-1z" class="g"></path><path d="M136 482h1c1 1 0 1 0 2h-1v2l-2-1c-1 0-1-1-1-2h1l1-1v1l1-1z" class="b"></path><path d="M153 499v-1h-2v-1h1 1v1l1-1c1-1 2-1 3-2h1c1-1 1-1 3-1h1v1h-1s-1 0-1 1c1 1 1-1 2 1l-1 1v1l1 1 2-1 2 2c-1 1-2 2-4 2s-3 0-5-2h-1c-1 0-2-2-3-2z" class="C"></path><path d="M156 501l-1-1c2-1 3-2 4-2h1 0v1c-1 1-2 1-3 2h-1z"></path><path d="M111 467l1-1v7c0 1 1 2 1 2 2 2 2 3 3 4h0-3l-1 1v3c0 1 0 3 1 5v1c1 1 3-1 5 0 0 1 1 2 1 2 0 1-1 2-1 2-1 1-1 1-2 1-1 1-2 1-3 1l-1 1v2-8-2c-1-1-1-3-1-4v-3-1h-2 0c1 0 1-1 1-1v-5c1-2 1-4 1-7z" class="C"></path><path d="M112 488h0v2h2s0 1 1 1 0-1 2 0v2c-1 0-1 0-2-1v2l-2 1-1 1v2-8-2z" class="S"></path><path d="M111 467l1-1v7 15h0c-1-1-1-3-1-4v-3-1h-2 0c1 0 1-1 1-1v-5c1-2 1-4 1-7z" class="U"></path><path d="M142 481c0-1 1-1 1-1h2v1h2v-1-1 1l4 1v1h0c1 1 1 1 3 1l1 1c-1 1-2 1-3 0h-3c-1 1-2 1-3 1-1 1-2 1-3 2-1 0-1 0-2 1l-1-1h-1l-1 1c0-1-1-1-2-1v-1h0v-2h1c0-1 1-1 0-2h-1 0c-1 0-1-1-2-1h0l-1-1h3c1 0 1 1 2 1s2-1 3-2v1c0 1 0 1 1 1z" class="X"></path><path d="M137 484c1 0 2 1 3 0 1 0 0-1 2-1l1 1c-2 1-3 1-4 2h-1v1l-2-1h0v-2h1z" class="d"></path><path d="M138 481c1 0 2-1 3-2v1l-1 2 2 1c-2 0-1 1-2 1-1 1-2 0-3 0 0-1 1-1 0-2h-1 0c-1 0-1-1-2-1h0l-1-1h3c1 0 1 1 2 1z" class="W"></path><path d="M142 481c0-1 1-1 1-1h2v1h2v-1-1 1l4 1v1h0c1 1 1 1 3 1l1 1c-1 1-2 1-3 0h-3-4-2l-1-1-2-1 1-2c0 1 0 1 1 1z" class="Y"></path><path d="M141 480c0 1 0 1 1 1 0 0 0 1 1 1 0 0 1-1 1 0s1 1 1 2h-2l-1-1-2-1 1-2z" class="O"></path><path d="M209 303c0 1 0 2 1 3-2 3-2 9-3 12l-3 16c0 3-1 5-1 8-1 4 0 9-1 13v-5c-1-1-1-1-1-2s1-3 1-4h0v-1-1h0v-2c-1 2-2 3-2 5 1 1 1 3 0 4h0v-8-1h-10v-1h11l8-36z" class="c"></path><path d="M179 484h3c1 0 1 1 2 1 2 0 4 0 6 1l4 2c1 0 2 0 4 1 1 0 2 0 3 1h1v-1l2 1-1 1h-2-1c0 1 0 2 1 2l-1 1c-1 0-1 0-1-1h-2 0c-4-1-7-3-11-4l-12-3h0v-1h3c1 1 2 1 3 1v-1l-1-1z" class="p"></path><path d="M190 486l4 2c1 0 2 0 4 1 1 0 2 0 3 1h1v-1l2 1-1 1h-2-1c0 1 0 2 1 2l-1 1c-1 0-1 0-1-1h-2 0v-1c-1-1-3-2-4-2h-1l-4-2h-1c1-1 2-1 3-2z" class="M"></path><path d="M150 471h2 7-3l-1 2c-1 0-1 1-1 1l-1 1c-1 0-1-1-1-1-2 0-3 0-4 2v2l-1 2v-1 1 1h-2v-1h-2s-1 0-1 1c-1 0-1 0-1-1v-1c-1 1-2 2-3 2v-3h-1l2-1-2-2c1 0 1-1 2-1s2 0 3-1h0v-1c0-1 3-1 4-1h1 3z" class="R"></path><path d="M144 477c1-1 1-2 2-2v2h-1-1z" class="N"></path><path d="M147 471h3 0c-1 0-2 1-3 1s-2 0-3 1v1h-2l-1 1h-1c0 1 0 1-1 2l-2-2c1 0 1-1 2-1s2 0 3-1h0v-1c0-1 3-1 4-1h1z" class="G"></path><path d="M139 477c1-1 1-1 1-2h1l1 1v1h2 1l-1 2h1 2 0v1 1h-2v-1h-2s-1 0-1 1c-1 0-1 0-1-1v-1c-1 1-2 2-3 2v-3h-1l2-1z" class="B"></path><path d="M139 477c1-1 1-1 1-2h1l1 1c-1 0-1 1-1 2-1 0-1 0-1 1h1c-1 1-2 2-3 2v-3h-1l2-1z" class="I"></path><path d="M145 490c1-1 3-1 5-1 3 1 6 2 8 2 8 1 16 0 24 1h0l-24 1c-1 0-2 1-3 1l-6 3c-2 2-3 4-5 6l-3 3-1 1h0c0-1 1-2 1-3v-1l1-2c0-1 0-2-1-3l-2 2c-1 0-1-1-1-1 0-1 0-2 1-3s3-2 5-3c1 0 6 0 7 1l5-2c-3-1-7-2-11-2z" class="m"></path><path d="M145 495c2 0 4-1 6-1-1 2-3 3-5 4-1 1-3 4-5 6v-1l1-2c0-1 0-2-1-3h0l1-1c1-1 2-2 3-2z" class="F"></path><path d="M141 498h0l1-1c1-1 2-2 3-2l-3 6c0-1 0-2-1-3z" class="o"></path><path d="M200 349h0c1-1 1-3 0-4 0-2 1-3 2-5v2h0v1 1h0c0 1-1 3-1 4s0 1 1 2v5 9l-1 30-1 30c0 2 1 6 0 8v2c-2-1-1-11-1-14v-6-15l1-36v-14z" class="q"></path><path d="M200 349h0c1-1 1-3 0-4 0-2 1-3 2-5v2h0v1 1h0c0 1-1 3-1 4s0 1 1 2v5 9c-1 0-1 0-1-1v2h0v5 1 8h-1v-11c0-3 1-5 0-8v3-14z" class="n"></path><path d="M123 471h3 1c3-1 8 0 10 0h2l-1 1c1 1 1 1 2 1h1 1c-1 1-2 1-3 1s-1 1-2 1l2 2-2 1h1v3c-1 0-1-1-2-1h-3l1 1h0c1 0 1 1 2 1h0l-1 1v-1l-1 1h-1 0-1-1v-1l-1-1 1-1h-1v-1h-1c-1 0-2 0-3-1l-1 1-2-1v2l-1-1v-1c-1-1-1-2-1-3 1-1 1-1 1-2s1-1 1-2z" class="Z"></path><path d="M122 478s0-1 1-2v2 2l-1-1v-1z" class="b"></path><path d="M123 476c0-1 0-1 1-2l-1-1h1c1 1 1 2 3 2v1c-1 0-2 0-2-1l-1 1c1 1 2 1 2 2l-1 1-2-1v-2z" class="P"></path><path d="M137 471h2l-1 1c1 1 1 1 2 1h1 1c-1 1-2 1-3 1s-1 1-2 1h-2l-2-1h-2-1-1l-1 1s-1 0-1-1v-1l2-1v-1h1l1 1s0 1 1 1c0 0 0-1 1-1 2 0 2 0 4-1z" class="W"></path><path d="M128 475l1-1h1 1 2l2 1h2l2 2-2 1h1v3c-1 0-1-1-2-1h-3l1 1h0c1 0 1 1 2 1h0l-1 1v-1l-1 1h-1 0-1-1v-1l-1-1 1-1h-1v-1c0-1-1-1-2-2v-1-1z" class="m"></path><path d="M133 474l2 1h-1v1h0 0v3h-1v-1-4z" class="O"></path><path d="M135 475h2l2 2-2 1h1v3c-1 0-1-1-2-1h-3 0-1l1-1h1v-3h0 0v-1h1z" class="S"></path><path d="M135 475h2l2 2-2 1h1l-1 1h-1v-2-1h-2 0v-1h1z" class="Y"></path><defs><linearGradient id="J" x1="203.758" y1="494.564" x2="186.592" y2="509.602" xlink:href="#B"><stop offset="0" stop-color="#ad936f"></stop><stop offset="1" stop-color="#c4b48e"></stop></linearGradient></defs><path fill="url(#J)" d="M192 503c-3 1-7 0-10 0-1 0-3 0-3-1 4-5 13-3 20-6l2-1h1l1 1h2v3c1 0 1 1 2 2h2c1 1 1 1 1 2h0-1c-1 1-3 0-5 0h-12z"></path><path d="M159 471h2 2 13c3 0 7 0 10 1h10c1 0 2 1 3 1h1 1 0v-1c1 1 1 0 1 1 0 0 1 1 2 1h0c1 0 1 1 1 2 1 0 2-1 2 1v3h-1c1 1 1 3 0 5h-1c1 1 1 1 0 2l-1 3-2-1v1h-1c-1-1-2-1-3-1-2-1-3-1-4-1l-4-2c-2-1-4-1-6-1-1 0-1-1-2-1h-3-3-1l-9-1h0-5-3l-3-1h-1-3v-1l-4-1 1-2v-2c1-2 2-2 4-2 0 0 0 1 1 1l1-1s0-1 1-1l1-2h3z" class="L"></path><path d="M161 471h2l1 1v1h-4v-1h1v-1zm-2 1v1s1 0 1 1l1 1-1 1h-2c0-1 0-1 1-1 0-1-1-1 0-3z" class="V"></path><path d="M199 486s1 1 2 1c1 1 3 0 4 0l-1 3-2-1v1l-2-2-1-1v-1z" class="I"></path><path d="M196 476h-2v-1c2-1 3 0 4 0h0l6 3 3 2h-1c-4-1-6-3-10-4z" class="D"></path><path d="M201 473h0v-1c1 1 1 0 1 1 0 0 1 1 2 1h0c1 0 1 1 1 2 1 0 2-1 2 1v3l-3-2h0v-1c-1-2-1-1-3-2 1 0 1 0 1-1l-1-1z" class="a"></path><path d="M186 472h10c1 0 2 1 3 1h1c-1 1-4 0-5 0-1 1-2 1-3 1-1 1-3-1-4-1-1-1-1-1-2-1z" class="R"></path><path d="M149 479v-3h1c1 0 1 0 2 1h1l1 1h0c-1 1-1 2-1 3l1 1h-3v-1l-4-1 1-2 1 1z" class="a"></path><path d="M148 478l1 1h0l1 1 1-1v1 1l-4-1 1-2z" class="N"></path><path d="M196 476c4 1 6 3 10 4 1 1 1 3 0 5h-1c-1 0-3-2-3-2l-2-1s-1-1-2-1l-2-1v-1h0v-1l-1-1 1-1z" class="l"></path><path d="M203 481h2v2c-1 0-1-1-2-1v-1z" class="L"></path><path d="M166 476h2v-1h3v2h-2c0 1 0 1 1 1-2 1-2 1-3 2v1 1l-1 1h-5-3l-3-1v-2h1c0-1 0-1 1-2-1 0-1-1-1-1l1-1h0c1 0 1 1 1 1l1 1s1 0 1-1c1 0 2 0 2 1h0l1-1c1 0 2 0 3-1z" class="V"></path><path d="M155 482v-2h1l1 1 1-1c1 1 0 2 0 3l-3-1z" class="R"></path><path d="M166 476h2v-1h3v2h-2c0 1 0 1 1 1-2 1-2 1-3 2v1 1l-1 1h-5c0-1 0-3 1-4l1 1s1 0 1-1c1 0 1-1 1-1 1 0 1 0 2-1l-1-1z" class="a"></path><path d="M161 483c0-1 0-3 1-4l1 1v2h1l1-1c1 0 1 1 2 1l-1 1h-5z" class="U"></path><path d="M172 476l1-1h1c1 0 1 1 2 1l4 1c1 0 2 1 4 1s4 2 6 2l6 3h0 2v1l-1 1c1 0 1 0 2 1v1l1 1 2 2h-1c-1-1-2-1-3-1-2-1-3-1-4-1v-1c1 0 1-1 1-2l-1-1h-1-2 0c-2-2-3-1-5-2-1-1-2-1-3-1 0 0-1-1-2-1h-1-3c-1 0-2-1-3-2v-2h-2 0z" class="G"></path><path d="M174 476h0c1 1 1 1 2 1s1 0 2 1c1 0 1 0 2 1v1h-3c-1 0-2-1-3-2v-2z" class="a"></path><path d="M171 475l1 1h0 2v2c1 1 2 2 3 2h3 1c1 0 2 1 2 1 1 0 2 0 3 1 2 1 3 0 5 2h0 2 1l1 1c0 1 0 2-1 2v1l-4-2c-2-1-4-1-6-1-1 0-1-1-2-1h-3-3-1l-9-1h0l1-1v-1-1c1-1 1-1 3-2-1 0-1 0-1-1h2v-2z" class="V"></path><path d="M171 475l1 1h0v2s-1 0-1 1v1l-1-2c-1 0-1 0-1-1h2v-2z" class="l"></path><path d="M170 478l1 2h1v1h0-1l-1-1-1 1 1 1h1 2l1-2h1v2h1 2c1 1 2 1 3 2h1-3-3-1l-9-1h0l1-1v-1-1c1-1 1-1 3-2z" class="N"></path><path d="M190 340h10v1 8 14l-1 36v15 6c0 3-1 13 1 14v-2l-1 32c1 1 2 1 2 1h1-7 0c-3-1-7 0-10-1l1-40v-33-11-27c0-4 0-9 1-13h1 2z"></path><path d="M199 420c0 3-1 13 1 14v-2l-1 32c1 1 2 1 2 1h1-7 0c1 0 2 0 3-1l1-44z" class="n"></path><path d="M226 158c2 1 5 0 7 0 1 0 2 1 2 0 1 0 1-1 1-1v2h6v3h2 0c1-1 2-1 3-1h5 5v-1l1-1v1s-1 0-1 1v1 1l1 3s1 0 2 1v3 6l-2 1-15 13-2 2c-3 5-7 9-11 14l-3 4c-1 2-2 5-4 6v2c-3 5-5 11-7 17l-2 7c0 1 0 3-1 4v1 2c0-1 0-1-1-2h-18-2-1c-3 1-6 0-9 2-1-1-1-1-2-1 0 0 0 1-1 1s-1-1-2-1c0 1-2 1-2 2-2 1-5 0-7 0-1 1-2 1-3 2l-1-1c0-2-1-2-2-3l-9-1c-5 1-10 1-15 0-1 0-2 1-3 1h-2-2c-1 0-4 0-5 1h0-7-3c-2 0-2 0-3-1 0 0 0 1-1 1v-2-1h-1v-10-1c0-4-1-9 0-12h0l-1 16h0v-5l1-71-1-4-1-1c1-1 1-1 2-1v1c4 1 7 0 11 0h24 21 24c2 0 6 1 9 0h22 0 4z"></path><path d="M185 235h0c1 0 1 0 2 1h0c-1 1-2 2-2 3v-1l-1-1h0l1-1v-1z" class="f"></path><path d="M182 182h1c0 1 1 2 1 2v4h1v1c-1 0-2 0-2-1-1 0-1-5-1-6z" class="i"></path><path d="M184 237l1 1v1h1s0 1 1 1v1h-1c0 1-1 2-1 3h-1c0-1 0-2-1-3h0c0-1 0-2 1-4z" class="e"></path><path d="M184 164h0c1 3 1 6 1 8l-1 1c0-1 0-1-1-2v-1-2l-1-1c0-1 1-2 2-3z" class="F"></path><path d="M183 231h1c1 0 2 0 3 1v1h-2v1 1 1l-1 1-1-1h0 0l-1-1v-1c0-1 0-2 1-3z" class="J"></path><path d="M183 236l1-2h1v1 1l-1 1-1-1z" class="K"></path><path d="M183 223c1 0 3 0 3 1 1 0 1 1 1 1l-1 2c0 1 1 1 1 1h1l1 1h-3c-1 1-2 1-2 2h-1v-4h0c0-2 0-3-1-4h1z" class="E"></path><path d="M111 235h45c9 0 18 1 26 0l1 1c-7 1-15 0-22 0h-34-16v-1z" class="C"></path><defs><linearGradient id="K" x1="130.524" y1="241.292" x2="134.167" y2="252.208" xlink:href="#B"><stop offset="0" stop-color="#786757"></stop><stop offset="1" stop-color="#998560"></stop></linearGradient></defs><path fill="url(#K)" d="M112 246h39 0c1 0 1 1 2 0v1c-5 1-10 1-15 0-1 0-2 1-3 1h-2-2c-1 0-4 0-5 1h0-7-3c-2 0-2 0-3-1 0 0 0 1-1 1v-2-1z"></path><defs><linearGradient id="L" x1="171.215" y1="249.739" x2="172.773" y2="243.026" xlink:href="#B"><stop offset="0" stop-color="#a0895d"></stop><stop offset="1" stop-color="#a99e8b"></stop></linearGradient></defs><path fill="url(#L)" d="M151 246h45 0c-1 0-2 1-3 1h0 1-2-1c-3 1-6 0-9 2-1-1-1-1-2-1 0 0 0 1-1 1s-1-1-2-1c0 1-2 1-2 2-2 1-5 0-7 0-1 1-2 1-3 2l-1-1c0-2-1-2-2-3l-9-1v-1c-1 1-1 0-2 0h0z"></path><path d="M162 248c1 0 2 1 3 1h4c2 0 4 0 6 1-2 1-5 0-7 0-1 1-2 1-3 2l-1-1c0-2-1-2-2-3z" class="g"></path><path d="M111 163h0v15 9 34l42 1h17c4 0 9-1 13 1h-1-71 0l-1 16h0v-5l1-71z" class="j"></path><path d="M109 158c1-1 1-1 2-1v1c4 1 7 0 11 0h24 21 24c2 0 6 1 9 0v1l-1 57h8 7c-5 1-11 0-16 0-1-6 0-13 0-19v-38h-86v17c0 3 0 7-1 10v1-9-15h0l-1-4-1-1z" class="G"></path><path d="M214 216h9v2c-3 5-5 11-7 17l-2 7c0 1 0 3-1 4v1 2c0-1 0-1-1-2h-18-1 0c1 0 2-1 3-1h0 2v-10-20c5 0 11 1 16 0z"></path><path d="M214 216h9v2c-2-1-6-1-9-1h-15l1 29h13v1 2c0-1 0-1-1-2h-18-1 0c1 0 2-1 3-1h0 2v-10-20c5 0 11 1 16 0z" class="V"></path><path d="M111 223h71c1 1 1 2 1 4h0v4c-1 1-1 2-1 3v1c-8 1-17 0-26 0h-45c0-4-1-9 0-12z"></path><path d="M226 158c2 1 5 0 7 0 1 0 2 1 2 0 1 0 1-1 1-1v2h6v3h2 0c1-1 2-1 3-1h5 5v-1l1-1v1s-1 0-1 1v1 1l1 3s1 0 2 1v3 6l-2 1-15 13-2 2c-3 5-7 9-11 14l-3 4c-1 2-2 5-4 6h-9-7-8l1-57v-1h22 0 4z" class="k"></path><path d="M207 201c1 0 0-1 1 0v1h-1v-1z" class="F"></path><path d="M229 181h1l1 1-1 1h-1-1s0-1 1-2z" class="K"></path><path d="M228 171h1l2 2v1c-1 0-2-1-3-2v-1z" class="J"></path><path d="M217 203c2 1 3 0 4-1v-1h2 0c0 1 0 2-1 3h0-1-2l-1 1-1-2z" class="M"></path><path d="M231 195l1-2c1 0 1-1 2-1h1l1-1c2-1 1-3 3-4 1-1 3-2 3-2v-5h0v8c0 1 0 2 1 2l-2 2-3-1c-1 0-3 1-3 2l-4 2z" class="Q"></path><path d="M226 158c2 1 5 0 7 0 1 0 2 1 2 0 1 0 1-1 1-1v2c-2 1-6 1-8 1h-17c-4 0-7-1-11-1v-1h22 0 4z" class="Z"></path><path d="M209 207c1-1 5-3 6-3v1h1v-1l1-1 1 2 1-1h2 1 0-1c0 1 1 1 1 2l-1 1c-1 0-2 1-3 1v1c1 0 1 0 2 1 1 0 1-1 2-1 0 2 0 2 1 2v1h-3c-1-1-3-1-4-1-1-1-4-3-5-3s-1 0-2-1z" class="C"></path><path d="M207 216v-1-1-1-2-2l1-1h0c0-1 0-1 1-1 1 1 1 1 2 1s4 2 5 3c1 0 3 0 4 1h3v-1c1-1 2-1 3-2l1 1c-1 2-2 5-4 6h-9-7z" class="f"></path><defs><linearGradient id="M" x1="230.546" y1="190.621" x2="227.938" y2="210.694" xlink:href="#B"><stop offset="0" stop-color="#4e4133"></stop><stop offset="1" stop-color="#726350"></stop></linearGradient></defs><path fill="url(#M)" d="M231 195l4-2c0-1 2-2 3-2l3 1c-3 5-7 9-11 14l-3 4-1-1c-1 1-2 1-3 2-1 0-1 0-1-2-1 0-1 1-2 1-1-1-1-1-2-1v-1c1 0 2-1 3-1l1-1c0-1-1-1-1-2h1c1-1 1-2 1-3h1c2-3 5-5 7-6z"></path><path d="M223 201h1 0c0 1-1 2-1 3l1-1c1-1 2-2 3-2-1 3-3 5-6 6l1-1c0-1-1-1-1-2h1c1-1 1-2 1-3z" class="b"></path><path d="M258 159v1s-1 0-1 1v1 1l1 3s1 0 2 1v3 6l-2 1-15 13c-1 0-1-1-1-2v-8-4-11-3h2 0c1-1 2-1 3-1h5 5v-1l1-1z" class="N"></path><path d="M242 165c2 1 3-2 6-2v1h0c-1 0-2 0-2 1s0 1 1 2h-1c-1 0-1 0-2-1 0 0 0 1-1 2h1c-1 1-1 1-1 2 0 2 0 5-1 6v-11z" class="T"></path><path d="M258 159v1s-1 0-1 1v1 1l1 3s1 0 2 1v3 6l-2 1h-1l1-2c-1 0-2-1-2-2h0 0l-2 1s-1 0-1-1v-1h1v-1l-1-2c0-1 1-2 1-3h-1l-2-2 1-3h5v-1l1-1z" class="a"></path><path d="M258 159v1s-1 0-1 1v1 1 3h0c-1 0-1-1-1-1h-1l1-2h-1c-1 0-1 1-2 2h0v1l-2-2 1-3h5v-1l1-1z" class="T"></path><path d="M257 166v-3l1 3s1 0 2 1v3 6l-2 1h-1l1-2v-2c-1-1-2 0-3-1l1-1v-2h1v-1l-1-1 1-1z" class="Q"></path><path d="M245 169h2c1-1 1-1 2-1v-1s1-1 2-1l1 1-1 1h-2l1 1v1c-2 0-4 1-6 1 1 0 2 1 3 0 0 1 1 3 1 3-1 0-1 0-1 1h0-1-1l2 1c0-1 1-1 1-2 0 0 1 0 1-1h2l1 2c1 0 1-1 1 0h1 1c0 1 0 1 1 1h1v1h1l-15 13c-1 0-1-1-1-2v-8-4c1-1 1-4 1-6 0-1 0-1 1-2 0 1 1 1 1 1z" class="R"></path><path d="M244 168c0 1 1 1 1 1l-1 2-1-1c0-1 0-1 1-2zm5 9c2 0 2 0 3 1 0 1 0 1-1 2l-5 3-1 1c-1 0-1 1-2 1v-5c1 0 2-1 4-2l2-1z" class="L"></path><path d="M252 175c1 0 1-1 1 0h1 1c0 1 0 1 1 1h1v1h1l-15 13c-1 0-1-1-1-2h1c2-1 3-3 3-5l5-3c1-1 1-1 1-2-1-1-1-1-3-1h0c1-1 2-2 3-2z" class="I"></path><path d="M252 175c1 0 1-1 1 0h1 1c0 1 0 1 1 1h1c-2 2-4 3-6 4 1-1 1-1 1-2-1-1-1-1-3-1h0c1-1 2-2 3-2z" class="V"></path><path fill="#e31d12" d="M470 245h0c1-1 1-1 2-1h1l1 1c1 0 5 0 6 1 1 0 1 0 2 1h4 13l-2-1h10 10c1 1 2 1 2 0h11l3 16c4 24 3 48 3 72v55l-112-63v-57l-1-16c-1-2-1-4-1-6h13 5 6c1 0 3-1 4 0h4 4 0c3-2 7 0 10-1 1 0 1-1 2-1z"></path><path d="M470 245h0c1-1 1-1 2-1h1l1 1c1 0 5 0 6 1 1 0 1 0 2 1h-24c3-2 7 0 10-1 1 0 1-1 2-1z" class="D"></path><path d="M325 316v-1c-4-6-4-27-4-35 0-3 0-7 1-10h1c1 0 3 2 4 3 6 5 11 11 16 17l2 3h0c5-1 12-1 17 0l4 1s1-1 1-2l4-5c4-4 7-8 12-12 1-1 3-3 5-2 1 1 1 2 1 3 1 6 0 13-1 19l-1 10c0 1-1 3 0 3 0 1 0 2 1 2l3 5 12 20c2 3 4 7 6 10 2 2 4 3 6 3l10 6c1 0 3 1 3 2v6 28l1 96v14 16c0 1-1 4-2 6l-7 18c-2 4-4 7-7 10l-2 1-4 1c-4 0-6-1-9-3-3-3-3-6-3-10 0-1 1-2 2-3 2-2 5-2 8-2 3-21 6-41 7-62 0-1 1-4 0-6h-4c-4-2-7-5-11-8l-15-12c-5-4-10-9-14-14-2-2-3-6-5-9-2-6-4-12-5-18-1-3-1-7-1-10l-1-27c-5-1-11-5-15-8l-3 3c-3 4-6 7-8 11 0 1-1 1-1 2l-1 1c4-9 11-15 16-22l-1-1-4 5-1-1c-1 0-1 0-1-1l-2-2-1-1-1-1-3-3s-1-1-2-1h-5c-4 1-9 3-13 5-2 1-6 4-8 4v-1l7-4c3-2 9-4 13-5 1 0 3-1 4 0 1 0 1 0 1-1-1-1-1-2-2-3-2-1-2-4-3-6-1-6-1-16 3-21v-1z"></path><path d="M382 315c0 1 1 1 2 3 0 2-2 5-4 7l-1-1c0-1 0-1 1-2 0-1 1-1 1-2v-1-1c0-1 0-2 1-3z" class="c"></path><path d="M330 314c3-1 7-1 10 1v1l-1-1-1 1v1c-1 1-2 1-3 2-1 0-1-1-2-1 0-1 0-2-1-3h0v-1h-2z" class="h"></path><path d="M409 380c1 1 2 2 2 3h-1c0 1 0 2-1 3l-1 1h1v3 1c1 3 0 8-1 12v-10c0-2-1-3 0-5h-1v-1c0-1 0-1 1-1v-1h-1v-1c0-1 1-2 2-2v-2h0z" class="H"></path><path d="M362 320h1c0 1-1 2-1 3-1 2-1 4-2 6 0 2-2 11-3 12h-1v-1c1-2 2-8 2-10 1-4 2-7 4-10z" class="n"></path><path d="M345 324v-1l1-1c1 3 1 7 2 10 0 2 0 5 1 7l7 1v1h1c-1 1-2 2-2 3h-1c-1 0-1 0-1-1h-2l-1 1c-4-5-4-14-5-20z" class="h"></path><path d="M367 346l-1-4c1-1 6-1 7-1s1 1 1 1h0c-1 1-4 0-6 1v1c5 1 10 1 14 2 11 1 19 5 27 12 2 2 4 3 5 5-4-2-6-5-10-7-5-4-12-7-19-9-2 0-5-1-7-1 5 2 10 4 15 8-7-4-13-7-21-8h-1c-1-1-3-1-4 0z" class="o"></path><path d="M362 320c2-3 5-6 8-7 4-1 8 0 12 2-1 1-1 2-1 3v1 1c0 1-1 1-1 2-1 1-1 1-1 2l1 1c-3 3-9 5-13 5-2 0-3-1-4-2-1-2-1-3-1-5 0-1 1-2 1-3h-1z" class="h"></path><path d="M375 315h1v1c1 3 0 7-1 10-1 0-2 1-3 1h-4c-1-4 0-7 0-11 1 1 2 2 3 2 1 1 1 1 2 0s2-2 2-3z"></path><path d="M355 344v3c1 1 4 1 6 1 1-1 1-4 1-6v-1c1-1 1-1 3 0 1 1 0 1 0 3 0 0-1 0-1 1h0c1 0 1 0 2 1h0 1c1-1 3-1 4 0h-4c0 1-1 1-2 1s-2 1-3 2v1h1 0c1-1 1-1 2-1l3-3c2 1 5 2 7 3 8 3 15 7 20 14 3 2 5 5 7 8l3 3c1 1 3 4 4 6h0c-3-2-4-6-7-8-2-1-4-5-6-7-6-7-13-11-21-15-2 0-4-1-6-2-1 0-2 1-3 2h0l5 2c2 1 8 3 8 5h-1s-2-1-3-2c-3-2-7-3-11-4l4 3c7 4 12 11 17 17 4 4 7 9 9 14-3-2-4-6-6-8-5-7-10-15-18-20-2-2-4-3-7-5 3 4 6 7 9 11 1 3 9 18 9 21-1-1-2-4-3-5-3-7-6-14-11-21l-5-5c-1 1-4 3-5 2-2 0-4-2-5-3-1 1-4 3-5 4l-3-2c0-1-1-1-1-1-1 0-1-1-2-1s-2 0-2 1l-1 2-1-1s-1-1-1-2h1c1-1 2-1 4-1h1v-2s-1-1-2-1h-3c-3 0-7-1-9 1h-5c-4 1-9 3-13 5-2 1-6 4-8 4v-1l7-4c3-2 9-4 13-5 1 0 3-1 4 0 1 0 1 0 1-1-1-1-1-2-2-3h13c1 0 3-1 4-1s1 0 1 1h1l-1 1h-1c-1-1-1 0-2 1 1 1 1 0 2 0l1 1c2 0 6 1 8 0 0-1 0-2-1-3l1-1h2c0 1 0 1 1 1h1z" class="G"></path><path d="M351 343h2c0 1 0 1 1 1 0 2 0 3-1 4l-1-1c-1-1-1-2-1-4z"></path><path d="M326 316c1-1 2-2 4-2h2v1h0c1 1 1 2 1 3 1 0 1 1 2 1 1-1 2-1 3-2v-1l1-1 1 1v-1c3 1 5 3 5 6l1 1-1 1v1c1 6 1 15 5 20 1 1 1 2 1 3-2 1-6 0-8 0l-1-1c-1 0-1 1-2 0 1-1 1-2 2-1h1l1-1h-1c0-1 0-1-1-1s-3 1-4 1h-13c-2-1-2-4-3-6-1-6-1-16 3-21v-1h1z"></path><path d="M325 316h1c1 3 2 6 5 8v3c-3-2-5-5-6-8v-2-1z" class="L"></path><path d="M326 316c1-1 2-2 4-2h2v1c-1 2 0 6 0 8l-1 1c-3-2-4-5-5-8z" class="c"></path><path d="M340 315c3 1 5 3 5 6l1 1-1 1v1h0c-1 2 0 5-3 6-2 1-6 0-8-1l-3-2v-3l1-1c0-2-1-6 0-8h0c1 1 1 2 1 3 1 0 1 1 2 1 1-1 2-1 3-2v-1l1-1 1 1v-1z" class="j"></path><path d="M332 315h0c1 1 1 2 1 3 1 0 1 1 2 1 1-1 2-1 3-2v-1l1-1c1 4 1 9 0 12-3 0-6-1-7-4 0-2-1-6 0-8z"></path><path d="M240 526l1 2 8 8 2 1h0c2 1 6 3 7 5 1 1 2 1 2 1l1 1c1 0 2 2 4 2l1 2 6 1-2 2 2 1v4h0c1-1 1-1 3-1 1 0 1 0 3 1h1c1 1 3 0 4 0l2-1c1 1 3 1 5 2 0 0 1 1 1 2 1-1 1-2 1-3h1 1l2-1 11 1c4 1 8 0 12 1h4v51l1 1v9 3c-1 4 0 9 0 12v1h-1v13c-7 0-14 1-20 1-13 0-25-1-37-3h-2l-13-3-6-1-12-2-12-3-2-1-2-2h1l-1-1h1c1 0 2 0 4-1h-5c-1 0-3 0-5 1h-2-1c-6-2-12-4-17-7-2-1-3-1-4-2h-1-2v-1c-2-1-4-2-7-3 0-1-1-1-2-1-2-2-5-4-8-6h1 1c1-1 0-7 0-9v-33-28-14-1h2 0l2-1h13 13 3c3 0 7 0 10 1h1l18-1v1c3-1 6 0 8-1z" class="E"></path><path d="M246 576c2 1 3 2 4 3l-1 1h-1c-1-2-1-3-2-4z" class="k"></path><path d="M240 584c1-1 1-1 2-1h0l3 3-1 1h-1 0l-1-1c0-1-1-1-2-2zm-46 12h0 1c1-2 2-2 2-4v-1h0l1-1v3 3h0-1c-1 0-1 1-1 1h-1v-1h-1z" class="F"></path><path d="M194 596h1v1h1s0-1 1-1h1v3l-1 1-2-1-2 1c0-1 0-1-1-2h0c-1-1-1-1-1-2l2 1 1-1z" class="C"></path><path d="M218 574h2c2-1 3-1 5-1 5 1 9 4 12 7h0-1l-2-1c-2-1-3-2-5-3-1 0-1 1-1 2h0-1c-1 0-1 0-1-1h-2v1l-2-1h0l-1 1-2-1c-1-1-2-1-3-1h0l-2-2h4z" class="g"></path><path d="M218 574h2c2-1 3-1 5-1l-3 1v2h-1c-1 0 0-1-1-2h-2z" class="Z"></path><path d="M224 577h-1 0c1-1 1-2 2-2h3c0 1 0 1 1 1h0c-1 0-1 1-1 2h0-1c-1 0-1 0-1-1h-2z" class="H"></path><path d="M198 590h1c0-2 1-3 2-5 2-4 8-9 13-11l2 2h0c1 0 2 0 3 1l2 1-1 1c-1 1-1 1-2 1s-1-1-3-1l-1 1-2-1c0-1-1-1-1-2l-1 1c-2 1-4 3-5 4-2 2-3 3-4 6l2 1h-1v2l-2 1h-1v6 1h-1v-3-3-3z" class="W"></path><path d="M201 588l2 1h-1v2l-2 1h-1l2-4z" class="Q"></path><path d="M211 577h1 2l1 1c2-1 3-1 4-1l2 1-1 1c-1 1-1 1-2 1s-1-1-3-1l-1 1-2-1c0-1-1-1-1-2z" class="D"></path><path d="M229 576c2 1 3 2 5 3l2 1h1 0c1 1 2 3 3 4s2 1 2 2l1 1c0 1 1 2 1 3l-1 1v-1h0c-2 1-2 1-3 1-2-1-3-1-4-2l-1 2-1 1-2-1c0-1 0-1 1-2v-1l-1-1h0-1-1c-1 0-1 0-3-1v-1-1h0c-1-1-1-1-1-3l1-1-1-1c-1 0-1 0-2-1v-1h2c0 1 0 1 1 1h1 0c0-1 0-2 1-2z" class="Q"></path><path d="M232 579v1l1 1c1 0 1 1 2 2h-2c-1-1-1-2-1-4z" class="X"></path><path d="M240 591v-1-3h1c1 0 1 1 1 2l1 1h0c-2 1-2 1-3 1z" class="K"></path><path d="M232 587s1 0 1-1h-1l-1-1s0-1 1-1c0 1 1 1 1 1h2 1c-1 2-1 2-1 4h-2v-1l-1-1z" class="X"></path><path d="M224 577h2c0 1 0 1 1 1h1c1 0 2 1 4 1 0 2 0 3 1 4l-1 1c-1 0-1 1-1 1l1 1h1c0 1-1 1-1 1h0-1-1c-1 0-1 0-3-1v-1-1h0c-1-1-1-1-1-3l1-1-1-1c-1 0-1 0-2-1v-1z" class="b"></path><path d="M230 583v2 2c-1 0-1 0-3-1v-1h2l1-2z" class="W"></path><path d="M226 579c1 1 2 1 3 2 1 0 1 1 1 2l-1 2h-2v-1h0c-1-1-1-1-1-3l1-1-1-1z" class="O"></path><path d="M211 577c0 1 1 1 1 2l2 1 1-1c2 0 2 1 3 1s1 0 2-1l1-1 1-1h0l2 1c1 1 1 1 2 1l1 1-1 1c0 2 0 2 1 3h0v1 1c2 1 2 1 3 1h1 1 0l1 1v1c-1 1-1 1-1 2l2 1-1 1v3c-1 0-1 1-2 1l-1-1-1 1-2-1-1-1v-1c-1 0-2-1-3-1v1h-1v-2h-1v2c-1 0-2-1-3-2v-1-1h-3-1l-1-1v1l-1-1h-1l-1-1c1-1 1-1 1-2-1 0-1 0-2-1h-1v-1c1 0 1 0 2-1h0-1-2c-1-1-2-1-2-1 1-1 3-3 5-4l1-1z" class="B"></path><path d="M227 584h-1c-1-1-1-1-2-1v-1l2-1c0 2 0 2 1 3z" class="I"></path><path d="M220 579l1 1c-1 1-2 1-3 1h-1-3v-1l1-1c2 0 2 1 3 1s1 0 2-1z" class="Y"></path><path d="M221 578l1-1h0l2 1c1 1 1 1 2 1h-1c0 1-1 1-1 2h-2 0v-2h0-1v1l-1-1 1-1z" class="I"></path><path d="M227 586c2 1 2 1 3 1h1c0 1 0 1-1 2s-2 2-3 2h-2c-2 0-2-1-4-2v-1h2l1 1h1v-1l-1-1v-1l3 3h1c-1-1-1-2-1-3h0z" class="O"></path><path d="M227 586c2 1 2 1 3 1h1c0 1 0 1-1 2-1 0-1-1-1-1h-1v1h-1 1c-1-1-1-2-1-3h0z" class="Y"></path><path d="M231 587h1 0l1 1v1c-1 1-1 1-1 2l2 1-1 1v3c-1 0-1 1-2 1l-1-1-1 1-2-1-1-1v-1c-1 0-2-1-3-1v1h-1v-2h-1l-1-1c0-1 1-2 1-2 2 1 2 2 4 2h2c1 0 2-1 3-2s1-1 1-2z" class="M"></path><path d="M227 594l1-1 1 1h0c0 1 0 2 1 2l-1 1-2-1-1-1 1-1z" class="f"></path><path d="M231 597v-2c1-1 1-2 2-2v3c-1 0-1 1-2 1z" class="Q"></path><path d="M223 593h-1v-1c1-1 2 0 3 0s1 1 2 2l-1 1v-1c-1 0-2-1-3-1z" class="J"></path><path d="M231 587h1 0l1 1v1c-1 1-1 1-1 2v1l-1-1c-1 1-2 1-3 1l-1-1c1 0 2-1 3-2s1-1 1-2z" class="P"></path><path d="M211 577c0 1 1 1 1 2l2 1v1 1c2 1 2 2 2 4 1 1 1 1 2 1s2 1 3 1v1s-1 1-1 2l1 1v2c-1 0-2-1-3-2v-1-1h-3-1l-1-1v1l-1-1h-1l-1-1c1-1 1-1 1-2-1 0-1 0-2-1h-1v-1c1 0 1 0 2-1h0-1-2c-1-1-2-1-2-1 1-1 3-3 5-4l1-1z" class="g"></path><path d="M214 587l3 1h1c-1 1-2 1-3 2h-1l-1-1v-1l1-1h0z" class="Q"></path><path d="M218 588c0 1 1 2 2 2v1l1 1v2c-1 0-2-1-3-2v-1-1h-3c1-1 2-1 3-2z" class="C"></path><path d="M210 583h2c1 0 1 1 1 2h2 0c0 1-1 1-2 2h1 0l-1 1v1 1l-1-1h-1l-1-1c1-1 1-1 1-2-1 0-1 0-2-1h-1v-1c1 0 1 0 2-1h0z" class="X"></path><path d="M211 577c0 1 1 1 1 2 0 0 0 1 1 1 0 2 1 1 1 2l-1 1v2c0-1 0-2-1-2h-2-1-2c-1-1-2-1-2-1 1-1 3-3 5-4l1-1z" class="Z"></path><path d="M210 578v1l-1 1v1h2v1c-2 0-3 0-4 1-1-1-2-1-2-1 1-1 3-3 5-4z" class="J"></path><path d="M235 591l1-2c1 1 2 1 4 2 1 0 1 0 3-1h0v1l1-1c2 5 1 8-1 13-2 6-6 11-13 14v1h-3c-1 1 0 1-1 1-2 0-4 1-5 1-4-1-7 0-10-1-2-1-4-1-5-3l-2-1v-3l1-1v-1c1 0 2 0 3 1h0 2c2 1 3 0 4 2 1 0 1-2 2-2 1-1 1-1 2-1l2-1 1-1c1 0 1 0 2-1v-1h1l2-1v-1-2h2v-1h0c1-2 1-3 1-4l1-1 1 1c1 0 1-1 2-1v-3l1-1 1-1z" class="F"></path><path d="M236 591h1 1c0 1-1 1-1 2s0 1-1 2v-3-1z" class="Q"></path><path d="M232 600h-1l-1-1v-1h1 0c1 1 1 1 2 1v3l-1-1v-1zm2 10c1 0 1 0 2 1l2-1-3 3h-3l2-1v-2h0z" class="J"></path><path d="M235 591h1v1c0 1-1 1-1 2v1l-2 1v-3l1-1 1-1z" class="f"></path><path d="M232 600v1s0 1 1 2l1 1s0 1 1 2h0l1 1-2 1v1 1c-1 0-2 0-3-1s-1-2-1-4h0v-1-1h1c0-1 0-2 1-3z" class="i"></path><path d="M227 607c1-1 2-1 2-2h0 1 0c0 2 0 3 1 4s2 1 3 1h0v2l-2 1h3c-2 2-4 2-5 3-1 0-1-1-2-1v-1h0l-1-2h0-2v-2l2-2h1l-1-1z" class="X"></path><path d="M227 608v1c1 0 1 1 2 1h0c0 1 0 1-1 1v1h-1 0-2v-2l2-2z" class="K"></path><path d="M232 613h-1c0-1 1-2 1-2v-1h-2c0-1-1-2-2-3 1 0 2-1 2-1v-1c0 2 0 3 1 4s2 1 3 1h0v2l-2 1z" class="F"></path><path d="M235 606c-1-2 1-4 0-6h0c1-2 2-3 3-4 0-1 0-3 1-4h0 2v2c-1 1 0 1 0 2v1 2c-1 1-1 3-2 5-1 1-2 4-4 5h-1v-1l2-1-1-1h0z" class="K"></path><path d="M238 599c0-1 1-1 1-2h2v2h-3z" class="H"></path><path d="M239 592h2v2c-1 1 0 1 0 2h-2v-4z" class="M"></path><path d="M238 599h3c-1 1-1 3-2 5-1 1-2 4-4 5h-1v-1l2-1-1-1v-1h2v-1s0-1-1-1h0v-1h2v-1h0v-2z" class="Q"></path><path d="M243 591l1-1c2 5 1 8-1 13-2 6-6 11-13 14v1h-3c-1 1 0 1-1 1-2 0-4 1-5 1-4-1-7 0-10-1-2-1-4-1-5-3l-2-1v-3l1-1v-1c1 0 2 0 3 1h0 2c2 1 3 0 4 2 1 0 1-2 2-2 1-1 1-1 2-1l2-1 1-1c1 0 1 0 2-1v2c2-1 3-2 4-2l1 1h-1l-2 2v2h2 0l1 2h0v1c1 0 1 1 2 1 1-1 3-1 5-3l3-3c2-3 3-5 4-8 2-4 3-7 1-11z" class="P"></path><path d="M228 615c1 0 1 1 2 1l-4 2c0-2 1-2 2-3z" class="Z"></path><path d="M212 616c2 1 2 0 4 0l-1 2h-2-3v-1h1l1-1z" class="W"></path><path d="M205 610c1 0 2 0 3 1h0 2c2 1 3 0 4 2v1h-2v2l-1 1h-1v1h3l-2 1c-2-1-4-1-5-3l-2-1v-3l1-1v-1z" class="C"></path><path d="M205 610c1 0 2 0 3 1h0v1h-4l1-1v-1z" class="Q"></path><path d="M206 614v-1h1l1 1c0-1 0-1 1-1l2 2v-1h1v2l-1 1h-1v1h3l-2 1c-2-1-4-1-5-3v-2z" class="O"></path><path d="M206 614c0 1 2 2 2 2 1 1 2 1 2 1v1h3l-2 1c-2-1-4-1-5-3v-2z" class="S"></path><path d="M227 607l1 1h-1l-2 2v2h2 0l1 2h0v1c-1 1-2 1-2 3h0-1-2l-1-1c0 1-1 1-2 1h0-1-1l-1-1-1-1c-2 0-2 1-4 0v-2h2v-1c1 0 1-2 2-2 1-1 1-1 2-1l2-1 1-1c1 0 1 0 2-1v2c2-1 3-2 4-2z" class="M"></path><path d="M223 614c1 1 1 1 2 1 0 1 0 1-1 1s-1 0-2-1l1-1z" class="F"></path><path d="M220 615c1 1 1 1 2 0 1 1 1 1 2 1-1 0-1 0-2 1h0c0 1-1 1-2 1h0l-1-1 1-2z" class="C"></path><path d="M220 615h-3v-1c1-1 2 0 3 0h3l-1 1c-1 1-1 1-2 0z" class="K"></path><path d="M217 617c-1-1-2-1-2-2v-1c1 0 2 0 4-2h1 1 1 0v1l-2 1c-1 0-2-1-3 0v1h3l-1 2 1 1h-1-1l-1-1z" class="H"></path><path d="M216 611v1h0c1 0 1-1 2-1h1 1c1 0 1-1 2-1 0 0 0 1 1 1l-1 1h-1-1-1c-2 2-3 2-4 2v1c0 1 1 1 2 2l-1-1c-2 0-2 1-4 0v-2h2v-1c1 0 1-2 2-2z" class="b"></path><path d="M227 607l1 1h-1l-2 2v2h2 0l1 2h-2l-2-2s0-1-1-1c0 1 0 1-1 1h0l1-1c-1 0-1-1-1-1-1 0-1 1-2 1h-1-1c-1 0-1 1-2 1h0v-1c1-1 1-1 2-1l2-1 1-1c1 0 1 0 2-1v2c2-1 3-2 4-2z" class="C"></path><path d="M205 582s1 0 2 1h2 1 0c-1 1-1 1-2 1v1h1c1 1 1 1 2 1 0 1 0 1-1 2l1 1h1l1 1v-1l1 1h1 3v1 1c1 1 2 2 3 2v-2h1v2h1v-1c1 0 2 1 3 1v1l1 1 2 1c0 1 0 2-1 4h0v1h-2v2 1l-2 1h-1v1c-1 1-1 1-2 1l-1 1-2 1c-1 0-1 0-2 1-1 0-1 2-2 2-1-2-2-1-4-2h-2 0c-1-1-2-1-3-1-1-2-2-2-3-3-2-2-3-3-4-5v-3-3h0v3h1v-1-6h1l2-1v-2h1l-2-1c1-3 2-4 4-6z" class="p"></path><path d="M220 600l1-1s1-1 1-2h1v2 1c0 1-1 1-1 2l-2-2zm-8 4c3-1 4-2 7-3v3h-3v1c-1 0-1 0-2-1l-1 1-1-1z" class="J"></path><path d="M208 601c1 1 2 2 4 3l1 1h0c-2 0-3-1-5-1l-1-1h-2 0v-1c1 0 2-1 3-1z" class="K"></path><path d="M227 596l2 1c0 1 0 2-1 4h0-2v-1h-2 0c0-1 0-1 1-2v-2c1 1 1 1 2 1h0v-1z" class="S"></path><path d="M225 598v-2c1 1 1 1 2 1h0 1l-2 2-1-1z" class="W"></path><path d="M216 598h2c2-1 0-1 1-2h2c-1 2-2 4-4 5s-4 1-7 1l-1-1v-1h2 0c1-1 3-2 4-2v1c0-1 0-1 1-2v1z" class="Q"></path><g class="F"><path d="M215 598v1h1l-1 1h-4c1-1 3-2 4-2z"></path><path d="M216 592v-1h2v1c1 1 2 2 3 2v2h-2c-1 1 1 1-1 2h-2v-1c-1 1-1 1-1 2v-1c-1-1-1-2-1-3v-1l1-2h1z"></path></g><path d="M215 592h1l2 3c-1 0-1 1-1 1-1 1-1 1-1 2v-1c-1 1-1 1-1 2v-1c-1-1-1-2-1-3v-1l1-2z" class="e"></path><path d="M223 600h1 0 2v1h2v1h-2v2 1l-2 1h-1v1c-1 1-1 1-2 1l-1 1-2 1c1-1 1-1 2-1v-2c1 0 1-1 2-2h0-2v-1c-1 1-1 1-2 1h-1 0-1v-1h3v-3l1-1 2 2c0-1 1-1 1-2z" class="P"></path><path d="M223 600h1 0v3h-3v-1h1c0-1 1-1 1-2z" class="b"></path><path d="M219 601l1-1 2 2h-1v1l-1 1h-1v-3z" class="Q"></path><path d="M213 605l1-1c1 1 1 1 2 1h1 0 1c1 0 1 0 2-1v1h2 0c-1 1-1 2-2 2v2c-1 0-1 0-2 1-1 0-1 0-2 1-1 0-1 2-2 2-1-2-2-1-4-2h-2 0v-1h2c1 0 1 0 1-1 0-2 1-3 2-4h0z" class="W"></path><path d="M216 606h1v1 2h-1l-1 1h0-2-1l1-2c1-2 1-1 2-1l1-1z" class="B"></path><path d="M198 596h0v3h1l1 2v2c1 1 1 1 2 1h0c0-1 0-1 1-2 0 0 1 1 2 1h0 2l1 1c2 0 3 1 5 1-1 1-2 2-2 4 0 1 0 1-1 1h-2v1c-1-1-2-1-3-1-1-2-2-2-3-3-2-2-3-3-4-5v-3-3z" class="Q"></path><path d="M208 604c2 0 3 1 5 1-1 1-2 2-2 4 0-1-2-1-2-1h-1l-1-1h1c1-1 0-1 0-3z" class="F"></path><path d="M202 604h0c0-1 0-1 1-2 0 0 1 1 2 1h0 2l1 1c0 2 1 2 0 3h-1v-1c-1-1-1-1-3-1h-1-1v-1z" class="E"></path><path d="M198 596h0v3h1l1 2v2c1 1 1 1 2 1v1h1 1c0 1 0 1-1 1 0 0 0 1 1 1 1 1 2 2 4 2v1 1c-1-1-2-1-3-1-1-2-2-2-3-3-2-2-3-3-4-5v-3-3z" class="p"></path><path d="M210 588h0l1 1h1l1 1v-1l1 1h1 3v1h-2v1h-1l-1 2v1c0 1 0 2 1 3-1 0-3 1-4 2h0-2 0l-1-1v-3-1-1c-1-2 0-3 1-5 0 0 1 0 1-1z" class="K"></path><path d="M210 592h1 1c-1 1-1 2-2 2v-2z" class="f"></path><path d="M209 589c1 1 1 1 1 2v1 2l-1 1h0l-1 1v-1-1c-1-2 0-3 1-5z" class="Q"></path><path d="M211 589h1l1 1v-1l1 1h1 3v1h-2v1h-1l-1 2v1h-2l-1-1c1-1 1-2 2-2-1-2-1-2-2-3z" class="M"></path><path d="M214 590h1 3v1h-2v1h-1c-1 0-1-1-2-2h1zm-9-8s1 0 2 1h2 1 0c-1 1-1 1-2 1v1h1c1 1 1 1 2 1 0 1 0 1-1 2h0c-1 0-2 1-3 2-1 2-1 5-1 7l1 3 1 1c-1 0-2 1-3 1v1c-1 0-2-1-2-1-1 1-1 1-1 2h0c-1 0-1 0-2-1v-2l-1-2v-1-6h1l2-1v-2h1l-2-1c1-3 2-4 4-6z" class="J"></path><path d="M200 601c0-1 1-1 2-1v1h2c1-1 2-1 3-1l1 1c-1 0-2 1-3 1v1c-1 0-2-1-2-1-1 1-1 1-1 2h0c-1 0-1 0-2-1v-2z" class="F"></path><path d="M200 601c0-1 1-1 2-1v1c0 1-1 1-2 2v-2z" class="k"></path><path d="M205 582s1 0 2 1h2 1 0c-1 1-1 1-2 1v1h1l1 2-1 1h0l-1-1c-1 0-2 2-4 3l-1-1-2-1c1-3 2-4 4-6z" class="F"></path><path d="M203 589l1 1v2s1 0 0 1c0 2 1 4 0 6-1 0-1 1-2 1s-2 0-2 1l-1-2v-1-6h1l2-1v-2h1z" class="m"></path><path d="M199 592h1v1h2c-1 1 0 1-1 1-1 1-1 2-1 4l1 1 1-1 2 1c-1 0-1 1-2 1s-2 0-2 1l-1-2v-1-6z" class="Z"></path><defs><linearGradient id="N" x1="208.044" y1="534.803" x2="227.916" y2="555.56" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#312b27"></stop></linearGradient></defs><path fill="url(#N)" d="M240 526l1 2 8 8 2 1h0c2 1 6 3 7 5 1 3 1 7 1 10l-1 15c0 3 1 7 1 10l-1 1h1l1 1c0 1 0 1-1 2l-1-3c-4-6-10-9-16-11-10-1-22-4-32 0v-1c1-1 1-1 2-1l1-1v-2-8-1-3-5-17-1h1l18-1v1c3-1 6 0 8-1z"></path><path d="M214 527l18-1-1 1h-7v10 7c0 3 0 15-1 17h0c1 1 0 2 0 2v1-2-3l-1-2v-13-17h-8 0z" class="j"></path><path d="M222 544h0c1-5 0-9 1-13v9c0 2 0 3 1 4 0 3 0 15-1 17h0c1 1 0 2 0 2v1-2-3l-1-2v-13z" class="h"></path><path d="M213 527h1 0v17 1l1 19h7v-7l1 2v3 2-1s1-1 0-2h0c1-2 1-14 1-17v-7 26h8v1h1l7 1h0v-7l1 8h1 3l-3 1h0c-10-1-22-4-32 0v-1c1-1 1-1 2-1l1-1v-2-8-1-3-5-17-1z" class="a"></path><path d="M213 550c1 2 0 5 0 8 1 1 1 1 1 2v-2c0 2 1 4 0 6l-1 1v-3-9-3z" class="n"></path><path d="M213 527h1 0v17 9c0 2 1 3 0 5v2c0-1 0-1-1-2 0-3 1-6 0-8v-5-17-1z" class="c"></path><g class="i"><path d="M231 527c1 2 1 7 1 10v26h-8v-26-10h7z"></path><path d="M240 526l1 2 1 38h-1l-1-8v7h0l-7-1h-1v-1-26c0-3 0-8-1-10l1-1v1c3-1 6 0 8-1z"></path></g><path d="M232 526v1h1v37h-1v-1-26c0-3 0-8-1-10l1-1z" class="h"></path><path d="M240 526l1 2 1 38h-1l-1-8v-30l-7-1h-1c3-1 6 0 8-1z" class="j"></path><path d="M241 528l8 8 2 1h0c2 1 6 3 7 5 1 3 1 7 1 10l-1 15c0 3 1 7 1 10l-1 1h1l1 1c0 1 0 1-1 2l-1-3c-4-6-10-9-16-11h0l3-1h-3l-1-38z" class="F"></path><path d="M249 536l2 1h0v2 30s0 1 1 1l2 2 5 5-1 1h1l1 1c0 1 0 1-1 2l-1-3c-4-6-10-9-16-11h0l3-1v1c2 0 3 1 5 2v-21c0-4 0-8-1-12z" class="L"></path><path d="M251 569v1l-1-1v-12-18h1v30z" class="c"></path><path d="M241 528l8 8c1 4 1 8 1 12v21c-2-1-3-2-5-2v-1h-3l-1-38z" class="E"></path><path d="M243 534h0 1l1 1c1 0 2 2 2 3v1h-1c-1-1-2-2-2-3-1 0-1-1-1-1v-1z" class="Z"></path><path d="M258 542c1 1 2 1 2 1l1 1c1 0 2 2 4 2l1 2 6 1-2 2 2 1v4h0c1-1 1-1 3-1 1 0 1 0 3 1h1l1 1h1c0 1 0 1 1 1v1l-1 1c-1 1-2 2-3 2h-1c-1 0-1 1-2 2h0v-3h-2l-1 1v1 1l2-1c0 1 0 2 1 4h-1-1c-1-2-1-2-3-3h-1-1l1 3v1c-1 1-3 2-3 4v21 6 3h0l-1 7 1 8c-1 4-1 7-1 11v4h-1v1h1c0 3 0 10-1 12l-13-3-6-1-12-2-12-3-2-1-2-2h1l-1-1h1c1 0 2 0 4-1h-5c-1 0-3 0-5 1h-2-1c-6-2-12-4-17-7h1c1-1 1 0 2-1l-2-14c-2-6-5-12-4-17 1 1 0 3 1 4v-6c1 1 0 3 1 4v1c0 1 0 1 1 2h0c1 1 1 1 1 2l2-1 2 1 1-1v3c1 2 2 3 4 5 1 1 2 1 3 3v1l-1 1v3l2 1c1 2 3 2 5 3 3 1 6 0 10 1v1h6 0c1 0 1 0 2 1h0c5-2 10-2 15-5 7-4 14-11 16-19v-1c2-5 1-11-1-16 1-1 1-1 1-2l-1-1h-1l1-1c0-3-1-7-1-10l1-15c0-3 0-7-1-10z" class="D"></path><path d="M190 597v-6c1 1 0 3 1 4v1c0 1 0 1 1 2h0c1 1 1 1 1 2l2-1 2 1 1-1v3c1 2 2 3 4 5 1 1 2 1 3 3v1l-1 1v3l2 1c1 2 3 2 5 3 3 1 6 0 10 1v1h6 0c1 0 1 0 2 1h0c-6 1-16 1-22-1-3-2-6-5-9-8l-4-4c-2-4-2-8-4-12h0z" class="F"></path><path d="M193 600l2-1 2 1 1-1v3c1 2 2 3 4 5 1 1 2 1 3 3v1l-1 1v3c-1-1-1 0-1 0-1 0-1-1-2-1 0-1 0-2-1-3l-1-1h1c-3-1-4-3-5-5v-2c-1 0-2 0-3-1l1-2z" class="f"></path><path d="M193 600l2-1 2 1 1-1v3l-1-1-1 1c1 0 2 1 2 1 1 2 0 4 2 5 1 1 1-1 2 1v1h-2c-3-1-4-3-5-5v-2c-1 0-2 0-3-1l1-2zm0 10l4 4c3 2 6 6 10 8 3 1 6 2 9 2 5 0 9 0 13-1-1 1-1 2-1 3h2 4-3-2v1 2h-1c-1 0-2 0-2 1h-1-1-1c-1 1-5 1-6 1s-3 0-5 1h-2-1c-6-2-12-4-17-7h1c1-1 1 0 2-1l-2-14z" class="i"></path><path d="M216 624c5 0 9 0 13-1-1 1-1 2-1 3h2 4-3-2v1 2h-1c-3-1-4-1-7 0l-2-1c2-1 5-1 7-1v-1h0c-2 0-5-1-6 0h0-1v-1c-1-1-2-1-3-1z" class="J"></path><path d="M212 632v-2l-1-2h0 1l1 1h1c0-1 1-1 1-1h2 2l2 1c3-1 4-1 7 0-1 0-2 0-2 1h-1-1-1c-1 1-5 1-6 1s-3 0-5 1z" class="H"></path><path d="M214 629c0-1 1-1 1-1h2 2l2 1h-1c-1 0-2 0-3 1h-2c0-1 0-1-1-1z" class="e"></path><path d="M193 610l4 4c-1 0-1 1-1 2l1 1c0 1 1 3 1 4h0c0 2 1 1 1 3v1c0 1 1 2 2 3h2s0 1 1 1c0 0 1 0 1 1h1 0c2 0 2 1 3 2-6-2-12-4-17-7h1c1-1 1 0 2-1l-2-14z" class="K"></path><path d="M199 625c-1-1-2-2-2-3l1-1c0 2 1 1 1 3v1zm52-11c5-5 10-13 11-20v-8c-1-3-2-5-2-7v-35h0 1c1 0 2 2 4 2l1 2 6 1-2 2 2 1v4h0c1-1 1-1 3-1 1 0 1 0 3 1h1l1 1h1c0 1 0 1 1 1v1l-1 1c-1 1-2 2-3 2h-1c-1 0-1 1-2 2h0v-3h-2l-1 1v1 1l2-1c0 1 0 2 1 4h-1-1c-1-2-1-2-3-3h-1-1l1 3v1c-1 1-3 2-3 4v21 6 3h0l-1 7c-1 1 1 6 0 8 0 1-1 1-2 1h-1l-1-1c-1 1-1 1-2 1h-1c-1 0-1 0-1 1l-1-1-1 1h-2c0 1-1 1-1 2-1-1-1-1-2-1h0l1-1s0-1-1-1l-2-2 1-1 2-1z" class="E"></path><path d="M256 616c1 1 1 1 3 2h-1c-1 0-1 0-1 1l-1-1v-2z" class="F"></path><path d="M251 614l1 1h1v1h0v1h1s1 0 1-1h1v2l-1 1h-2c0 1-1 1-1 2-1-1-1-1-2-1h0l1-1s0-1-1-1l-2-2 1-1 2-1z" class="J"></path><path d="M249 615v2l1-1h1c1 1 0 2 0 3h0s0-1-1-1l-2-2 1-1z" class="H"></path><path d="M265 546l1 2 6 1-2 2 2 1v4h-1-1c-1 0-3 0-4-1h0l-1 5v-6-8z" class="B"></path><path d="M266 548l6 1-2 2c-1-1-3-1-4-2v-1z" class="X"></path><path d="M265 560l1-5h0c1 1 3 1 4 1h1 1 0c1-1 1-1 3-1 1 0 1 0 3 1h1l1 1h1c0 1 0 1 1 1v1l-1 1c-1 1-2 2-3 2h-1c-1 0-1 1-2 2h0v-3h-2l-1 1v1 1l2-1c0 1 0 2 1 4h-1-1c-1-2-1-2-3-3h-1-1l1 3v1c-1 1-3 2-3 4v21 6 3h0c-1-4-1-9-1-13v-29z" class="T"></path><path d="M268 559l-1-1c1-1 1-1 2-1l1 1-2 1z" class="B"></path><path d="M266 572c0-3-1-6 0-8h1l1 1v-1l1 3v1c-1 1-3 2-3 4zm5-16h1 0c1 1 3 1 3 2l-1 1c-2 1-3 2-5 3h0v-2l-1-1 2-1c0-1 0-1 1-2z" class="R"></path><path d="M272 556c1-1 1-1 3-1 1 0 1 0 3 1h1l1 1h1c0 1 0 1 1 1v1l-1 1c-1 1-2 2-3 2h-1v-2-1c-1-1-2 0-3 0l1-1c0-1-2-1-3-2z" class="B"></path><path d="M259 618c1 0 1 0 2-1l1 1h1c1 0 2 0 2-1 1-2-1-7 0-8l1 8c-1 4-1 7-1 11v4h-1v1h1c0 3 0 10-1 12l-13-3-6-1-12-2-12-3-2-1-2-2h1l-1-1h1c1 0 2 0 4-1h-5c1 0 5 0 6-1h1 1 1c0-1 1-1 2-1h1v-2-1h2 3-4-2c0-1 0-2 1-3l11-3 4-2 4-2 2 2c1 0 1 1 1 1l-1 1h0c1 0 1 0 2 1 0-1 1-1 1-2h2l1-1 1 1c0-1 0-1 1-1h1z" class="f"></path><path d="M254 625v-3h1c1 1 1 1 1 3 1 0 3-2 4-3l1 1 1-1s1 0 1 1v2l-2-2v1h-2s0 1-1 1v1 1 1l-1-1v1-1c-1 0-2-1-2-1h-1v-1z" class="C"></path><path d="M254 625v-3h1c0 2 1 3 0 4h-1v-1z" class="H"></path><path d="M229 629c0-1 0-1 1-2 0 1 1 2 2 2h1c2 0 4 0 6-1 0 1 1 1 1 2h6l-10 1h-10-4-5c1 0 5 0 6-1h1 1 1c0-1 1-1 2-1h1z" class="C"></path><path d="M246 630c2 1 4 1 6 1h1 2c1 0 3 0 5 1h4v1h-4c-6 1-14 0-20 0-2 0-5 0-7 1 0 1-1 3 0 5h0l-12-3-2-1-2-2h1l-1-1h1c1 0 2 0 4-1h4 10l10-1z" class="S"></path><path d="M222 631h4 10l1 1h-1c-1 1-1 1-2 1h-9c-1 0-3 0-4 1v2l-2-1-2-2h1l-1-1h1c1 0 2 0 4-1z" class="L"></path><path d="M218 633h7c-1 0-3 0-4 1v2l-2-1-2-2h1z" class="Y"></path><defs><linearGradient id="O" x1="248.998" y1="629.147" x2="246.977" y2="635.447" xlink:href="#B"><stop offset="0" stop-color="#aa986f"></stop><stop offset="1" stop-color="#cdaa7d"></stop></linearGradient></defs><path fill="url(#O)" d="M246 630c2 1 4 1 6 1h1 2c1 0 3 0 5 1h4v1h-4-8-18c1 0 1 0 2-1h1l-1-1 10-1z"></path><path d="M248 616l2 2c1 0 1 1 1 1l-1 1h0c1 0 1 0 2 1h0c0 1 0 1 1 2v1 1h1v1h1s1 1 2 1v1-1l1 1v-1-1-1c1 0 1-1 1-1h2v-1l2 2v3l2 2-2 2v-1h-3c-1-1-1-1-2-1h-1 0c-3-1-6 0-9 0v-1c-1 0-2-1-4-1 0 0-1 0-1-1h-4c-2 0-3-1-5-1h-4-2c0-1 0-2 1-3l11-3 4-2 4-2z" class="k"></path><path d="M261 626h0c0 1 0 1 1 2-1 1-1 1-2 1l-1-1h1s0-1 1-2z" class="J"></path><path d="M253 624v1h1v1c-1 0-1 0-2-1h-2c1-1 2-1 3-1zm5 1c1 0 1-1 1-1h2v-1 3h0c-1 0-2 0-3-1z" class="K"></path><path d="M252 621c0 1 0 1 1 2v1c-1 0-2 0-3 1h0c-2-1-3-1-4-1 3 0 4-1 6-3z" class="Q"></path><path d="M248 616l2 2c1 0 1 1 1 1l-1 1h0c1 0 1 0 2 1h0c-2 2-3 3-6 3-2-1-3-2-5-3v-1h-1l4-2 4-2z" class="H"></path><path d="M244 620h1c2 0 2-1 4-2v2l-1 1v1c-2-1-3-1-4-1v-1z" class="Z"></path><path d="M248 616l2 2c1 0 1 1 1 1l-1 1h-1v-2c-2 1-2 2-4 2h-1v-2l4-2z" class="X"></path><defs><linearGradient id="P" x1="263.926" y1="642.174" x2="236.636" y2="630.276" xlink:href="#B"><stop offset="0" stop-color="#020101"></stop><stop offset="1" stop-color="#382f27"></stop></linearGradient></defs><path fill="url(#P)" d="M264 633h1c0 3 0 10-1 12l-13-3-6-1-12-2h0c-1-2 0-4 0-5 2-1 5-1 7-1 6 0 14 1 20 0h4z"></path><path d="M233 634h2v4l-2 1c-1-2 0-4 0-5z" class="C"></path><path d="M245 641h0c-1-1-1-1-1-2v-3h3l1-1h3v1h0v6l-6-1z" class="J"></path><path d="M187 526h13 3c3 0 7 0 10 1v1 17 5 3 1 8 2l-1 1c-1 0-1 0-2 1v1c-6 2-13 7-16 12-2 4-4 9-4 12v6c-1-1 0-3-1-4-1 5 2 11 4 17l2 14c-1 1-1 0-2 1h-1c-2-1-3-1-4-2h-1-2v-1c-2-1-4-2-7-3 0-1-1-1-2-1-2-2-5-4-8-6h1 1c1-1 0-7 0-9v-33-28-14-1h2 0l2-1h13z" class="i"></path><path d="M174 526h13v1 51h-1v-51h-8l1 11-1 49v20 11 1c0-1-1-1-2-1 1-7 1-15 1-21v-40-12-9-9h-5 0l2-1z" class="n"></path><path d="M170 528v-1h2 5v9 9 12 40c0 6 0 14-1 21-2-2-5-4-8-6h1 1c1-1 0-7 0-9v-33-28-14z"></path><path d="M170 528l1 4h0c1 0 2 1 2 2h0c0 1 1 2 1 3s-1 1-2 1h-1l-1 1v3-14z" class="f"></path><path d="M177 536v9 12-9h-2l-1 1h0c-1-1-1-3-2-4 1-1 1-2 1-4h0v-1l1-1c1 0 2 0 3-1v-2z" class="F"></path><path d="M187 527h8v48c0 1-2 5-3 7-1 3-3 7-3 11-1 5 2 11 4 17l2 14c-1 1-1 0-2 1h-1c-2-1-3-1-4-2h-1-2v-1c1-2 1-5 1-7-1-3 0-6 0-9v-22-6h1v-51z"></path><path d="M186 578h1v7-1h-1v-6z" class="h"></path><path d="M186 615v-5h1c0 2 1 3 0 5 0 2 0 6 1 8h-1-2v-1c1-2 1-5 1-7z" class="n"></path><path d="M185 623l1-1v-1c0-2 0-4 1-6h0c0 2 0 6 1 8h-1-2z" class="j"></path><path d="M186 584h1v1 17c0 3 1 6 0 8h-1v5c-1-3 0-6 0-9v-22z" class="c"></path><path d="M187 526h13 3c3 0 7 0 10 1v1 17 5 3 1 8 2l-1 1c-1 0-1 0-2 1v1c-6 2-13 7-16 12-2 4-4 9-4 12v6c-1-1 0-3-1-4 0-4 2-8 3-11 1-2 3-6 3-7v-48h-8v-1z"></path><path d="M187 526h13 3v1h-7l1 47c2-2 4-4 7-6h0v-14l1 4c0-2 0-4 1-6v-1 16l4-1v1c-6 2-13 7-16 12-2 4-4 9-4 12v6c-1-1 0-3-1-4 0-4 2-8 3-11 1-2 3-6 3-7v-48h-8v-1z" class="o"></path><defs><linearGradient id="Q" x1="199.093" y1="532.589" x2="217.544" y2="559.59" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#383025"></stop></linearGradient></defs><path fill="url(#Q)" d="M203 526c3 0 7 0 10 1v1 17 5 3 1 8 2l-1 1c-1 0-1 0-2 1l-4 1v-16 1c-1 2-1 4-1 6l-1-4v-6c0-7 1-14-1-21v-1z"></path><path d="M203 526c3 0 7 0 10 1v1c-3-1-5-1-8-1l1 17v7 1c-1 2-1 4-1 6l-1-4v-6c0-7 1-14-1-21v-1z" class="j"></path><path d="M296 555l11 1c4 1 8 0 12 1h4v51l1 1v9 3c-1 4 0 9 0 12v1h-1v13c-7 0-14 1-20 1-13 0-25-1-37-3h-2c1-2 1-9 1-12h-1v-1h1v-4c0-4 0-7 1-11l-1-8 1-7h0v-3-6-21c0-2 2-3 3-4v-1l-1-3h1 1c2 1 2 1 3 3h1 1c-1-2-1-3-1-4l-2 1v-1-1l1-1h2v3h0c1-1 1-2 2-2h1c1 0 2-1 3-2l1-1v-1c-1 0-1 0-1-1h-1l-1-1c1 1 3 0 4 0l2-1c1 1 3 1 5 2 0 0 1 1 1 2 1-1 1-2 1-3h1 1l2-1z" class="U"></path><path d="M287 593l1-2 1 1h0v1 1c-1 0-1 0-2-1h0z" class="N"></path><path d="M275 578h-1c0-1-1-2-1-3h1 1v3z" class="l"></path><path d="M293 593h-1v-2h2l-1 2z" class="L"></path><path d="M273 585c1 0 2 1 3 1v2l-1-1h0c-1 0-1 0-2 1l-1-1v-1l1-1z" class="B"></path><path d="M291 601v-1c2-2 0-2 1-4h1v1c0 1 1 1 1 2l-2 1-1 1h0zm-8-1l1-1v-1h1v1c0 1-1 2-1 4l-1 1v-2h-1l-1 1h0l-1-1 1-1 2-1z" class="D"></path><path d="M279 583l1 1v1h1 1 1l-2 2c-1 0-1 1-1 1h-1c-1 0-1 0-1-1s0-1 1-1v-1l-1-1 1-1z" class="T"></path><path d="M292 600v1h2 1c0 1 1 1 1 2-2 0-2-1-3-1v1h-3v-1c0-1-1-2-1-2l1-1h0v1l1 1h0l1-1z" class="L"></path><path d="M267 595l1-2v-1c0-1 1-2 1-4h0v4 2c0 1 1 2 1 4h-1l-1 1-1-1c0-2 0-2 1-3h-1z" class="l"></path><path d="M297 592v-1h1c0-1-1-1-1-2h1c1 0 1 0 1 1l-1 1h1 1c1 0 2 1 4 1-1 1-1 2-1 3v2h-1v-1h-3v-1h2 1 0v-1l-2-1v-1h-1-2zm-16-4l1 1h2 0v2l1 1 1-1 1 2h0-1v1h1s1 1 2 1c-2 2-2 1-3 1s-1 1-2 1h-1v-1-1l-1 1v-3-3h-1v-2z" class="R"></path><path d="M283 595v-1l1-1s1 0 1 1-1 1-1 1l-1 1v-1z" class="B"></path><path d="M271 600l1-1v-2l1-1v-1h0c-1 0 0-1-1-1 0-1 0-1 1-2 0 0-1-1-1-2 1 0 1 0 2-1h3l1 1h1v2c-1 1-1 2-3 2 0 1-1 2-1 3s-1 1-1 2v1h-1-2z" class="D"></path><path d="M278 590h1v2c-1 1-1 2-3 2v-1c-1 0-1 0-2 1l-1-1 1-1v-2h4z" class="B"></path><path d="M277 589c2 0 2 0 3-1h1v2h1v3 3s-1 0-1 1l1 1-1 1c-1 0-1 1-2 0h0l-2-1c-1 1-1 1-2 1l-1 1v-1c0-1 1-1 1-2s1-2 1-3c2 0 2-1 3-2v-2h-1l-1-1z" class="G"></path><path d="M282 593l-1 1h-1 0v-2c1-1 1-2 2-2v3z" class="B"></path><path d="M279 592v2c-1 1-2 1-3 1v1h1c1 0 2 0 2 1h2l1 1-1 1c-1 0-1 1-2 0h0l-2-1c-1 1-1 1-2 1l-1 1v-1c0-1 1-1 1-2s1-2 1-3c2 0 2-1 3-2z" class="I"></path><path d="M266 593l1 2h1c-1 1-1 1-1 3l1 1 1-1c0 2 0 3 1 5h0 1v3h0-3v4h0v2s0 1-1 2v2l-1 1h1v1h-1v-1l-1-8 1-7h0v-3-6z" class="a"></path><path d="M267 614v-4-1-3h1 0v4h0v2s0 1-1 2zm-1-21l1 2h1c-1 1-1 1-1 3l1 1h-1c0 1 0 1 1 2v2h-1c0-1 0-2-1-4v-6z" class="N"></path><path d="M295 606v-1l1-1c1-1 1-2 1-4h1 0v1l1 1-1 1 1 1v2c0 1 0 1 1 1s1 0 2-2h-1c0-1-1-2-1-4 0 0 0-1-1-1 0-1 0-2 1-2 0 0 1 1 2 1h0v4h1v-1-1l1-1c1 1 0 3 1 4v1 1s-1 0-1 1c1 1 1 1 1 2v1l1 1c2 0 1-3 4-1 0 2 0 3 1 4h1v5 3h0l-1 1-1-1h-1l1 1-2 2h-2c-1 1-1 1-2 1v-1l-2-1v-1h3l-3-2-2 1h0-2l-1-1c0 1 0 1-1 1s-1-1-2-2v-1h-2v-2h0v-2c-1-1 0-3-1-4l1-1v-1h0v-1-2c0-1 0-1 1-1l1-1v2h1z" class="l"></path><path d="M301 616h2v1 1h-1c-1-1-1-1-1-2z" class="V"></path><path d="M297 611c0-1 1-1 1-2h1v1h1v-2c2 0 2 0 3 1h0-2c0 1 0 1 1 2-2 0-3-1-4 0h-1z" class="U"></path><path d="M305 610l1 1c2 0 1-3 4-1 0 2 0 3 1 4h1v5 3h0l-1 1-1-1h-1l1 1-2 2h-2c-1 1-1 1-2 1v-1l-2-1v-1h3 0l2-2-1-1c0-3-1-5-1-8 0 0-1-1-1-2l1-1v1z" class="N"></path><path d="M308 612h1c0 2 0 3-1 5l-1-1 1-1v-1c-1-1-2-1-2-2 1-1 1 0 2 0z" class="B"></path><path d="M311 614h1v5 3h0l-1 1-1-1h-1c1-1 0-2 0-3l1-1v-1-1s1-1 1-2z" class="D"></path><path d="M311 614h1v5 3h0c-1-1 0-3-1-4l-1-1v-1s1-1 1-2z" class="B"></path><path d="M307 621v2c1 0 1 0 2-1h0l1 1-2 2h-2c-1 1-1 1-2 1v-1l-2-1v-1h3 0l2-2z" class="W"></path><path d="M293 605l1-1v2h1c1 1 0 2 1 3v1h-2 0v1h0 3 1v1h2v3h1v1c0 1 0 1 1 2v1h-1 0c-1-1-1-1-2-1h-1c0 1 1 1 2 2 0 0 1 0 2 1l-2 1h0-2l-1-1c0 1 0 1-1 1s-1-1-2-2v-1h-2v-2h0v-2c-1-1 0-3-1-4l1-1v-1h0v-1-2c0-1 0-1 1-1z" class="U"></path><path d="M297 616c0-1-1-2 0-3h1c0 1 1 2 0 3h0-1z" class="l"></path><path d="M294 611h3 1v1 1h-2l-2-2zm-2 4h3v1l-1 1 2 3 2-2-2-1 1-1h1v2c0 1 1 1 2 2 0 0 1 0 2 1l-2 1h0-2l-1-1c0 1 0 1-1 1s-1-1-2-2v-1h-2v-2h0v-2z" class="a"></path><path d="M293 605l1-1v2h1c1 1 0 2 1 3v1h-2 0v1 3 1l2-1h0c0 1 0 1-1 2v-1h-3c-1-1 0-3-1-4l1-1v-1h0v-1-2c0-1 0-1 1-1z" class="N"></path><path d="M293 605l1-1v2 2h-1v-2-1z" class="D"></path><path d="M274 600l1-1c1 0 1 0 2-1l2 1h0c1 1 1 0 2 0l2 1-2 1-1 1 1 1h0l1-1h1v2 1s1 0 1 1v-2h1 1v2c2 0 2-1 3 0l1 1v2 1s0 1 1 1c1 1 0 3 1 4v2h0v2h2v1c1 1 1 2 2 2v2 1l-1 1h-1v2-2h-1c-1 1-1 1-2 1h0l-1 1h0l-2 2v1h0-1c0-1 0-1 1-2l-1-1v1c-1-1-2-2-2-3s0-1-1-1v-1h-2-1l-2 1h-1 0-1-1c-1 0-1 0-2 1h-1s-1 0-2 1l-1-1h0c-1-1-2 0-3-1v-2-1c-1-1-1-3-1-4h1v-1h-1l1-1v-2c1-1 1-2 1-2v-2h0v-4h3 0v-3l1-1c0-1-1-1-1-2h2 1z" class="G"></path><path d="M290 617h1 1l-1 1h-1 0v-1h0z" class="B"></path><path d="M278 604v-1c0-1 1-1 2-2v2c-1 0-1 1-2 1z" class="I"></path><path d="M278 605c2 0 2 1 3 1 0 1-1 1-1 2l-1-1c-1-1-1-2-1-2zm3 12c2 0 2 0 4 1l-1 1 1 1-1 1-1-1c-1-1-1-2-2-3zm3 7h1v-1c0-1 1-2 1-2h1l2 2 1 1h-3l-1 1c-1-1-1-1-2 0v-1z" class="Y"></path><path d="M290 618l-2-1v1c0 1 1 1 1 2h0c-2 0-2 0-3-1h0c0-1 1-1 1-2s0-2 1-3c1 1 1 1 1 2 0 0 1 0 1 1h0v1z" class="D"></path><path d="M290 624c1 1 0 1 1 2v1l-1 1h0l-2 2v1h0-1c0-1 0-1 1-2l-1-1v1c-1-1-2-2-2-3s0-1-1-1c1-1 1-1 2 0l1-1h3z" class="d"></path><g class="I"><path d="M290 624c1 1 0 1 1 2v1l-1 1h0-1l-3-3 1-1h3z"></path><path d="M294 619v1c1 1 1 2 2 2v2 1l-1 1h-1v2-2h-1c-1 1-1 1-2 1h0v-1c-1-1 0-1-1-2l-1-1 1-2v-1h1c1 0 1 0 1 1h0 1c1-1 0-1 0-2h1z"></path></g><path d="M294 620c1 1 1 2 2 2v2 1c-1-1-2-1-3-1l1-1v-3z" class="Y"></path><path d="M290 621c1 0 1 1 2 1 0 1-1 2 0 2h1c1 0 2 0 3 1l-1 1h-1v2-2h-1c-1 1-1 1-2 1h0v-1c-1-1 0-1-1-2l-1-1 1-2z" class="S"></path><path d="M279 607l1 1h1v1h1 1v-2c1 0 1 1 2 1h0 1s1-1 2-1v1 1c-1 0-3 0-4 1h3c1 0 2 0 2 1s0 1-1 2v-1h-2c0 1-1 1-2 1v1l-1-1-2 1h0-1-1v-2l-1-1 1-1h0-2v-1c0-1 1-2 2-2z" class="D"></path><path d="M274 600l1-1c1 0 1 0 2-1l2 1h0c1 1 1 0 2 0l2 1-2 1-1-1-1 1h1c-1 1-2 1-2 2v1l-1 1h1s0 1 1 2c-1 0-2 1-2 2v1h2 0l-1 1 1 1v2h1 1 0l2-1 1 1v-1c2 1 2 1 2 2v2c-1 0-1 1-1 1-2-1-2-1-4-1h0-1c0 1 0 2-1 2h0c-1-1-1-2-3-2h0v-1h1v-1h-1c-1 0-1-1-2-2h0-1l-1 1h-1-2v-3l-1-1h0v-4h3 0v-3l1-1c0-1-1-1-1-2h2 1z" class="B"></path><path d="M274 600l1-1c1 0 1 0 2-1l2 1-1 2c-1 0-1 0-2-1 0 1-1 1-1 2h1l-1 1v1c-1 1-1 2-1 3l1 1h1c0 1-1 2-2 4h1c0 1 1 1 1 2 1 0 1 1 1 2h-1 1v-1h-1c-1 0-1-1-2-2h0-1l-1 1h-1-2v-3l-1-1h0v-4h3 0v-3l1-1c0-1-1-1-1-2h2 1z" class="G"></path><path d="M271 600h2l1 1c0 1-1 1-1 2-1 0-1 0-1-1s-1-1-1-2z" class="I"></path><path d="M268 610c1-1 1-1 2-1l1 1c-1 1-1 1-1 2 1 0 1 1 1 2h-2v-3l-1-1h0z" class="a"></path><path d="M273 610l-1 1c-1-1-1-1-1-2s-1-2-1-2h0v-1h2v-1h1v1s0 1-1 2c0 1 0 1 1 2z" class="B"></path><path d="M273 605c0 1 1 1 1 2l1 1h1c0 1-1 2-2 4v-2h-1c-1-1-1-1-1-2 1-1 1-2 1-2v-1z" class="D"></path><path d="M268 610l1 1v3h2 1l1-1h1 0c1 1 1 2 2 2h1v1h-1v1h0c2 0 2 1 3 2h0c1 0 1-1 1-2h1 0c1 1 1 2 2 3l1 1v1l-2 2h-1l-2 1h-1 0-1-1c-1 0-1 0-2 1h-1s-1 0-2 1l-1-1h0c-1-1-2 0-3-1v-2-1c-1-1-1-3-1-4h1v-1h-1l1-1v-2c1-1 1-2 1-2v-2z" class="l"></path><path d="M270 626c0-1 0-2 1-3 1 0 1 0 1 2v1h-2 0z" class="V"></path><path d="M273 626v-1l3-3v1c0 1 1 1 1 2h0-1c-1 0-1 0-2 1h-1z" class="D"></path><path d="M273 614h1v2 1c0 1-1 2 0 3l1 1-1 1h-1s0-1-1-1c0-1-1-1-1-1 1-1 1-1 1-2s-1-1-1-2 1-1 2-2zm10 6l1 1v1l-2 2h-1l-2 1h-1 0c0-1-1-2-1-2v-1c1-1 1 1 2 1l1-1h1 1 0v-2h1zm13-65l11 1c4 1 8 0 12 1h-6l-1 1h0v8 11 4l-2 1v1c1 0 1 0 2 1l-1 1h0s-1 0-1 1l-1 1h-2v1 1c-1 1-2 1-3 2-1 0-1 0-2-1-1 0-1 0-2 1h-1-1l1-1c0-1 0-1-1-1h-1c0 1 1 1 1 2h-1v1c-1 0-1 0-1 1l-1-1v1 1h-1l-1-1 1-2c0-2 0-2-1-3h-1c-1-1-1-1-1-2h-1l-1 1h-2l1-1v-1l-1 2c-1-1-1-1-1-2v1c-1 1-1 1-2 1v-2h-1-1-1-1v-1l-1-1h-3v-2h-2v-1h1v-2-3h1c0 1 0 2 1 2h0c1-1 1-1 1-3l-2-1v-1l-2-2c0 1 0 1-1 2h-1c-1 1-1 1-2 1v-2l1 1h0v-4c-1-1-1-1-2-1l-1-3h1 1c2 1 2 1 3 3h1 1c-1-2-1-3-1-4l-2 1v-1-1l1-1h2v3h0c1-1 1-2 2-2h1c1 0 2-1 3-2l1-1v-1c-1 0-1 0-1-1h-1l-1-1c1 1 3 0 4 0l2-1c1 1 3 1 5 2 0 0 1 1 1 2 1-1 1-2 1-3h1 1l2-1z" class="a"></path><path d="M292 575c0-1 0-1 1-1h0v1h0-1z" class="G"></path><path d="M289 580c1 0 1 0 1 1v1 2 1h-1v-1-4z" class="L"></path><path d="M289 568h-1-2 0-1v-2h1 0l1 1 1-1 1 2h0zm-5 2h2l2 2s0-1 1-1v3h0-1c-1-1-3-2-4-4z" class="D"></path><path d="M278 567h1v4c1 0 1 0 1 1l-1 1-1-1v-2l-2-1h0l2-2z" class="L"></path><path d="M289 568l1-1h1v1c1 1 0 1 1 1s1-1 1-2c1 0 1 0 1-1 0 1 1 2 0 3 0 1-1 1-2 2-2-1-3-1-3-3h0zm15 0l1-1v1l1 4h2c0 1-1 2-1 2h-2c0-1-1-2-2-2l1-1v-1-1c-1 0-1 0-1-1h1z" class="U"></path><path d="M275 580l1-1c1 1 1 2 2 2l1 1c1-2-1-3 2-3h1 1 0c-1 1-1 0-1 1-1 0-1 1-2 2v2l-1-1h-3v-2h-2v-1h1z" class="G"></path><path d="M292 575h1c1 1 1 2 1 3h1l1 1v1h-1v1h1c1 0 1 1 1 1v1h0l-1-1c-1 0-1 1-2 1v1h-1v-1-1-3h0c0-1-1-2-2-2 0-1 0-1 1-1v-1z" class="R"></path><path d="M301 564c0-1 1-2 2-2 0 1 0 2 1 2 1 1 2 1 2 2l-1 2v-1l-1 1-1-1c-1 0-1 1-1 1h-1c0-1-1-1-1-1h1 0v-3z" class="T"></path><path d="M295 563h1l1-1 1 1c1 0 1 1 3 1v3h0-1l-1 1v-3l-3 2h-1l-1-1c1-1 1-2 1-3z" class="B"></path><path d="M281 560l2 2 3-1v2c-1 0-2 1-3 1v-1l-1-1c0 1-1 1-2 1h0l-1 2h0l-2-1v1h-1v-1h-1c1-1 1-2 2-2h1c1 0 2-1 3-2z" class="D"></path><path d="M293 575c1-1 0-2 1-3 0-1 2-2 3-2s1 0 2 1v1 2 1h1 0v4h-2l1-1v-1c0-1-1-1-1-2-1 0-2 0-3 1v1h1v-1h1l1 1-1 2v2h-2v-1h1v-1l-1-1h-1c0-1 0-2-1-3h0z" class="U"></path><path d="M296 572h1l1 1-1 1h-1v-1-1z" class="D"></path><defs><linearGradient id="R" x1="282.169" y1="556.039" x2="293.331" y2="566.903" xlink:href="#B"><stop offset="0" stop-color="#9f8867"></stop><stop offset="1" stop-color="#b8a278"></stop></linearGradient></defs><path fill="url(#R)" d="M285 555c1 1 3 1 5 2 0 0 1 1 1 2l1 2c1 1 2 1 3 2 0 1 0 2-1 3h0c0 1 0 1-1 1 0 1 0 2-1 2s0 0-1-1v-1h-1l-1 1-1-2c0-1-1-2-2-3v-2l-3 1-2-2 1-1v-1c-1 0-1 0-1-1h-1l-1-1c1 1 3 0 4 0l2-1z"></path><path d="M290 557s1 1 1 2l1 2h-2l-1-2c0-1 0-1 1-2z" class="G"></path><path d="M309 572c0 1 0 1-1 3l2 1h0c1 0 1 0 2 1v4l-2 1v1c1 0 1 0 2 1l-1 1h0s-1 0-1 1l-1 1h-2v1 1c-1 1-2 1-3 2-1 0-1 0-2-1l1-1v-1c0-2 1-3 0-4l-1 1c0 1 0 1-1 1h-1c0-1 0-2-1-3 0-1 1-1 0-2h-1l-1-2 1-2-1-1h-1v1h-1v-1c1-1 2-1 3-1 0 1 1 1 1 2v1l-1 1h2v-4h3v-1-1-1c1 0 2 1 2 2h2s1-1 1-2h1z" class="D"></path><path d="M306 576v1 1c-1 1-1 1-2 0 1 0 1-1 2-2z" class="T"></path><path d="M304 578s-1 0-1-1h1c1-1 1-1 2-1-1 1-1 2-2 2z" class="B"></path><path d="M302 581c1 1 1 1 2 1v-1l1 1v2h0c-1-1-1-1-3-1v-2z" class="N"></path><path d="M308 579h1l-1 2 1 1h1v1l-1-1v1l-1-1c-1-1-1-2-1-3h1z" class="B"></path><path d="M309 572c0 1 0 1-1 3l2 1h0c1 0 1 0 2 1v4l-2 1h-1l-1-1 1-2h-1l-1-1h-1v-1l2-2h-3v-1h2s1-1 1-2h1z" class="G"></path><path d="M307 578c0-1 1-1 2-1s0 1 2 1h0c0 1 0 1-1 1 0 1 0 2-1 3l-1-1 1-2h-1l-1-1z" class="N"></path><path d="M296 555l11 1c4 1 8 0 12 1h-6l-1 1h0v8 11c-1-1-1-1-2-1h0l-2-1c1-2 1-2 1-3h-1-2l-1-4 1-2c0-1-1-1-2-2-1 0-1-1-1-2-1 0-2 1-2 2-2 0-2-1-3-1l-1-1-1 1h-1c-1-1-2-1-3-2l-1-2c1-1 1-2 1-3h1 1l2-1z" class="e"></path><path d="M302 560h0l1-1c1-1 2-1 3-1 1 1 2 1 3 2h0v2h-1c0 2 0 3-2 4 0-1-1-1-2-2-1 0-1-1-1-2-1-1-1-1-1-2z" class="S"></path><path d="M291 559c1-1 1-2 1-3h1l1 2h0c1 0 2 0 2-1l1 1c1 1 2 1 4 1v1h1c0 1 0 1 1 2-1 0-2 1-2 2-2 0-2-1-3-1l-1-1-1 1h-1c-1-1-2-1-3-2l-1-2z" class="I"></path><path d="M294 558h0c1 0 2 0 2-1l1 1c1 1 2 1 4 1v1h-1l-1 1c-1-1-2-2-3-2 0 1-1 1-1 1-1 0-1-1-1-2z" class="W"></path><path d="M306 566c2-1 2-2 2-4h1c0 2 0 3 1 5h0c1 0 1 0 1-1l-1-1c1-1 1-1 1-2l1 3v11c-1-1-1-1-2-1h0l-2-1c1-2 1-2 1-3h-1-2l-1-4 1-2z" class="B"></path><path d="M309 572v-1c1 0 1 1 2 2-1 1-1 2-1 3h0l-2-1c1-2 1-2 1-3z" class="R"></path><path d="M310 567h0v3h-1-1v1l-1-1v-2h2c0-1 0-1 1-1z" class="D"></path><path d="M319 557h4v51l1 1v9 3c-1 4 0 9 0 12v1h-1v13c-7 0-14 1-20 1-13 0-25-1-37-3h-2c1-2 1-9 1-12h-1v-1h1v-4c0-4 0-7 1-11v1c0 1 0 3 1 4v1 2c1 1 2 0 3 1h0l1 1c1-1 2-1 2-1h1c1-1 1-1 2-1h1 1 0 1l2-1h1 2v1c1 0 1 0 1 1s1 2 2 3v-1l1 1c-1 1-1 1-1 2h1 0v-1l2-2h0l1-1h0c1 0 1 0 2-1h1v2-2h1l1-1v-1-2c1 0 1 0 1-1l1 1h2 0l2-1 3 2h-3v1l2 1v1c1 0 1 0 2-1h2l2-2-1-1h1l1 1 1-1h0v-3-5h-1c-1-1-1-2-1-4-3-2-2 1-4 1l-1-1v-1c0-1 0-1-1-2 0-1 1-1 1-1v-1-1c-1-1 0-3-1-4v-2c0-2 0-2 1-3l-1-2v-1c-2 0-3-1-4-1 1-1 1-1 2-1 1 1 1 1 2 1 1-1 2-1 3-2v-1-1h2l1-1c0-1 1-1 1-1h0l1-1c-1-1-1-1-2-1v-1l2-1v-4-11-8h0l1-1h6z"></path><path d="M272 635c1 0 1 0 1-1h1 2 0v1h1 1c1 0 1 0 0 1l-1 2-5-3z" class="g"></path><path d="M298 640h1c1 1 2 1 4 1l1 1 1-1h1v1c-2 2-5 0-8 0h0-1-1l2-2z" class="K"></path><path d="M282 634h1l1 1h1l-3 1v1c1 1 1 0 1 2h-4l-2-1 1-2 1 1 3-3h0z" class="d"></path><path d="M279 639c1-2 2-3 3-3v1c1 1 1 0 1 2h-4z" class="H"></path><path d="M323 634v-26l1 1v9 3c-1 4 0 9 0 12v1h-1z" class="o"></path><path d="M266 634h0v-2c2 0 3 1 4 2 1 0 1 1 1 1v1h1l2 1 2 1-1 1h-1-3c-1 0-2-1-3-1h0c-1-1-2-1-2-1v-3zm42-1c1 0 1-1 2-1h0 1l1 1 1 3c-2 0-1 0-3 1h1c1 1 1 3 1 4s-1 1 0 2h-1 0-1l-1-1v1h-2v-1h-1v-1h-1l-1 1-1-1c-2 0-3 0-4-1v-1c-1 0-1-1-2-2l1-1h0l-1-1v-1l2-2h1l1 1v1h1 1l1-1 1 1c2 0 2 0 3-1z" class="E"></path><path d="M298 636l1 1v2c-1 0-1-1-2-2l1-1h0z" class="M"></path><path d="M266 617v1c0 1 0 3 1 4v1 2c1 1 2 0 3 1h0l1 1c1-1 2-1 2-1h1c1-1 1-1 2-1h1 1 0 1l2-1h1 2v1c1 0 1 0 1 1s1 2 2 3c-2 0-3-1-4 1v1l-2 2h1v1h0l-3 3-1-1c1-1 1-1 0-1h-1-1v-1h0-2-1c0 1 0 1-1 1h-1s0-1-1-1c-1-1-2-2-4-2v2h0v3c-1 2-1 5 0 8h-2c1-2 1-9 1-12h-1v-1h1v-4c0-4 0-7 1-11z" class="P"></path><path d="M273 626h1c1-1 1-1 2-1h0c-1 1-1 1-2 1l3 3h1l1 2h0-3l-1-1c-1 1-3 3-5 3v-1c1 0 1-1 2-1s1-1 2-2c0-1 0-1-1-1h-1l-1-1c1-1 2-1 2-1z" class="I"></path><path d="M266 617v1c0 1 0 3 1 4v1 2c1 1 2 0 3 1h0l1 1 1 1c-1 1-1 1-2 0l-1 1v1h1v1h0c-1 0-2 1-3 1 0-1 0-1 1-2l-1-1h-1v4 1 3c-1 2-1 5 0 8h-2c1-2 1-9 1-12h-1v-1h1v-4c0-4 0-7 1-11z" class="B"></path><path d="M282 624h2v1c1 0 1 0 1 1s1 2 2 3c-2 0-3-1-4 1v1l-2 2h1v1h0l-3 3-1-1c1-1 1-1 0-1h-1-1v-1h1v-2h1c-1 0-2-1-2-1h3 0l-1-2h-1l-3-3c1 0 1 0 2-1h0 1 1 0 1l2-1h1z" class="S"></path><path d="M282 624h2v1c1 0 1 0 1 1-1 1-2 2-3 2l1-1c-1-1-1-2-2-2v-1h1z" class="I"></path><path d="M278 632h1c1-1 2-1 4-2h0v1l-2 2h1v1h0l-1-1-1 1c-1-1-2-1-3-2h1z" class="g"></path><path d="M277 632c1 1 2 1 3 2l1-1 1 1-3 3-1-1c1-1 1-1 0-1h-1-1v-1h1v-2z" class="X"></path><path d="M310 582l2-1v38-5h-1c-1-1-1-2-1-4-3-2-2 1-4 1l-1-1v-1c0-1 0-1-1-2 0-1 1-1 1-1v-1-1c-1-1 0-3-1-4v-2c0-2 0-2 1-3l-1-2v-1c-2 0-3-1-4-1 1-1 1-1 2-1 1 1 1 1 2 1 1-1 2-1 3-2v-1-1h2l1-1c0-1 1-1 1-1h0l1-1c-1-1-1-1-2-1v-1z" class="I"></path><path d="M310 598c1 1 1 2 1 2l1 1-1 2h-1s0-1-1-1v-1c-1 0-1 0-1-1 0 0 0-1 1-1v1h1v-2z" class="G"></path><path d="M308 593c1-1 1-1 2-1v3 2 1 2h-1v-1c-1 0-1 1-1 1l-1 1h-1-1v3c-1-1 0-3-1-4v-2h2l1-1h1v-3-1z" class="R"></path><path d="M306 601c0-1 0-1 1-1v-2h2v-2h1v1 1 2h-1v-1c-1 0-1 1-1 1l-1 1h-1z" class="U"></path><path d="M308 600c0 1 0 1 1 1v1c0 1 1 1 1 2v2l1 2-1 1h0-1c-1-1-1-1-2-1 0-1 0-1-1-1 0 0 0 1 1 1l-2 2v-1c0-1 0-1-1-2 0-1 1-1 1-1v-1-1-3h1 1l1-1z" class="T"></path><path d="M305 604v-3h1 1c-1 1-1 2-1 3l-1 1v-1z" class="B"></path><path d="M310 609c-1-1-1-2-1-3-1 0-1 0-1-1 1-1 1-1 2-1v2l1 2-1 1h0z" class="R"></path><path d="M309 587l1-1 2 1v1l-1 1v2c-1 0-1 0-2-1 0 1-1 2-1 3v1 3h-1l-1 1h-2c0-2 0-2 1-3l-1-2v-1c-2 0-3-1-4-1 1-1 1-1 2-1 1 1 1 1 2 1 1-1 2-1 3-2v-1-1h2z" class="G"></path><path d="M309 587l1-1 2 1v1l-1 1h-2-1c1-1 1-2 1-2z" class="R"></path><path d="M307 597c-1-1-1-1-1-3 0-1 0-2-1-2v-1h1c1 0 2-1 3-1 0 1-1 2-1 3v1 3h-1z" class="U"></path><path d="M302 621l3 2h-3v1l2 1v1c1 0 1 0 2-1h2l2-2-1-1h1l1 1 1-1h0c0 3 0 7-1 10h-1 0c-1 0-1 1-2 1-1 1-1 1-3 1l-1-1-1 1h-1-1v-1l-1-1h-1l-2 2v1l1 1h0l-1 1c1 1 1 2 2 2v1h-1l-2 2h0-1-3c-1 0-1-1-2-2-1 0-1 1-2 2l-1-1-1-1h-2c0-1 0-1-1-1 0-2 0-1-1-2v-1l3-1h-1l-1-1h-1v-1h-1l2-2v-1c1-2 2-1 4-1v-1l1 1c-1 1-1 1-1 2h1 0v-1l2-2h0l1-1h0c1 0 1 0 2-1h1v2-2h1l1-1v-1-2c1 0 1 0 1-1l1 1h2 0l2-1z" class="X"></path><path d="M284 640c2-1 1-1 1-2l1-1h1v2h0l2-2c0 1 1 2 1 3-1 0-1 1-2 2l-1-1-1-1h-2z" class="E"></path><path d="M287 629v-1l1 1c-1 1-1 1-1 2h1l-1 1c0 1 0 1 1 2h1v1h-2c0-1 0-1-1-1v1h-1-1l-1-1h-1v-1h-1l2-2v-1c1-2 2-1 4-1z" class="Z"></path><path d="M283 631h1v1c-1 1-1 1 0 2h-1-1v-1h-1l2-2z" class="X"></path><path d="M284 634l1-2h1v2 1h-1-1l-1-1h1z" class="b"></path><path d="M289 637c1 0 1-1 3-1l1-1c-1 0-1 0-1-1h-1 2v1 1h1c1-1 2 0 4 0l-1 1c1 1 1 2 2 2v1h-1l-2 2h0-1-3c-1 0-1-1-2-2 0-1-1-2-1-3z" class="e"></path><path d="M298 640h-3 0 0l1-1c0-1 0-2 1-2h0c1 1 1 2 2 2v1h-1z" class="F"></path><path d="M302 621l3 2h-3v1l2 1v1h-4v3c-1 0-1 0-1 1l1 1 1 1v2-1l-1-1h-1l-2 2v1l1 1h0c-2 0-3-1-4 0h-1v-1-1c1 0 1-1 1-1l1-2-1 1c-1-1-1-2-1-2l1-1h-1-2l-1-1 1-1h0c1 0 1 0 2-1h1v2-2h1l1-1v-1-2c1 0 1 0 1-1l1 1h2 0l2-1z" class="H"></path><path d="M291 627h0c1 0 1 0 2-1h1v2h1 0l1-1h1c0 1 0 1 1 1v1 1c-1 0-1-1-2-1 0 0 0 1-1 1s-1 0-1-1h-1-2l-1-1 1-1z" class="P"></path><path d="M300 631l1 1v2-1l-1-1h-1l-2 2v1l1 1h0c-2 0-3-1-4 0h-1v-1-1c1 0 1-1 1-1 1 0 1 0 1 1h1 0v-1-1l2 1 2-2z" class="J"></path><path d="M302 621l3 2h-3v1c-1 0-2 1-3 2s-1 1-1 2c-1 0-1 0-1-1h-1l-1 1h0-1v-2h1l1-1v-1-2c1 0 1 0 1-1l1 1h2 0l2-1z" class="m"></path><path d="M296 624h1s1-1 2-1h0l-1 1 1 2c-1 1-1 1-1 2-1 0-1 0-1-1h-1l-1 1h0-1v-2h1l1-1v-1z" class="W"></path><path d="M312 622h0c0 3 0 7-1 10h-1 0c-1 0-1 1-2 1-1 1-1 1-3 1l-1-1-1 1h-1-1v-2l-1-1-1-1c0-1 0-1 1-1v-3h4c1 0 1 0 2-1h2l2-2-1-1h1l1 1 1-1z" class="e"></path><path d="M308 628h1c1 0 2 0 2 1v1h-2c-1 0-1 0-1-2z" class="b"></path><path d="M302 629c1 0 1 1 2 1v2h-1c0 1 0 1-1 2h-1v-2h1c-1-1-1-2 0-3z" class="K"></path><path d="M304 632h1c1-1 2-1 3-1v2h0c-1 1-1 1-3 1l-1-1-1 1h-1c1-1 1-1 1-2h1z" class="k"></path><path d="M304 626c1 0 1 0 2-1h2v2c-1 0-2 1-3 2h0c0-1 0-1-1-2-1 0-1 1-2 1l-1 1h1c-1 1-1 2 0 3h-1l-1-1-1-1c0-1 0-1 1-1v-3h4z" class="P"></path><path d="M153 247l9 1c1 1 2 1 2 3l1 1c1-1 2-1 3-2 2 0 5 1 7 0 0-1 2-1 2-2 1 0 1 1 2 1s1-1 1-1c1 0 1 0 2 1 3-2 6-1 9-2h1 2 18c1 1 1 1 1 2l-4 38c0 5-1 10 0 15v1l-8 36h-11v1h-2-1c-1 4-1 9-1 13v27 11 33l-1 40c3 1 7 0 10 1h0-11-12-6c-1 0-2 0-3-1-1 0-2 0-3 1h-9-8-4-1v-1c-2 1-6 1-8 1h-19v-59c1-2 0-5 0-7l1-14v-45s0-1 1-1l-1-1v-1c-1-1-1-2-1-3h1s0 1 1 1c1-1 1-1 1-3h2v-1c0-1-1-1-1-2-1 0-1-1-1-2-1-1-2-1-3-1v-5h0v-4-3c0-1 0-3 1-5l-1-6v-4l1-1-1-5-1-1v3-23h0v-6l1 1v-5c0-4 0-9 1-13 1 0 1-1 1-1 1 1 1 1 3 1h3 7 0c1-1 4-1 5-1h2 2c1 0 2-1 3-1 5 1 10 1 15 0z"></path><path d="M111 293h4c-1 2-1 3-1 5v1h-3l1-1-1-5z" class="B"></path><path d="M187 339h1v-4l2-14 6-29c0-1 1-10 2-11h1 0c1 2 0 3 0 5l-2 9c-1 5-8 43-7 44v1h-2l-1-1z" class="n"></path><path d="M115 293h58v6h-59v-1c0-2 0-3 1-5z" class="U"></path><path d="M111 303l8 1h53l-1 6h-27c-4 0-8 0-11-1h-21l-1-6z" class="l"></path><path d="M111 303l8 1h-5l-1 1h0c1 1 0 3 0 3v1h4 10 6-21l-1-6z" class="B"></path><path d="M111 314c6 1 12 1 17 1h40 2c1 1 0 4 0 6h-28-11-5-10-5 0v-4-3z" class="N"></path><path d="M111 314c6 1 12 1 17 1h40c-5 1-11 0-16 0h-38c0 2-1 3-1 5v1c4 0 9-1 13 0h-10-5 0v-4-3z" class="I"></path><path d="M110 272v-6l1 1v13h1c1 1 5 1 6 0h10 1 14 21 11l-1 8h-63v5l-1-1v3-23h0z" class="R"></path><path d="M110 272v-6l1 1v13 1c0 1 0 2-1 4v-13z" class="N"></path><path d="M129 280h14 21l-1 1h-5-1c-2 0-3 1-4 2h-1 0c0-1 0-2-1-2h-14c-2 0-5 0-8-1z" class="T"></path><path d="M111 280h1c1 1 5 1 6 0h10c-2 2-3 3-6 3-1-1-2 0-4 0l-1-1c-2 0-2 0-3 1h0l1-1h0v-1c-2 0-2 0-3 1 0 1 0 4-1 4v-5-1z" class="B"></path><defs><linearGradient id="S" x1="128.483" y1="343.251" x2="156.999" y2="315.65" xlink:href="#B"><stop offset="0" stop-color="#907c60"></stop><stop offset="1" stop-color="#c2a97f"></stop></linearGradient></defs><path fill="url(#S)" d="M131 327l38-1c0 3 0 7-1 9h-1v-1c-1 1-1 2-2 2h-7 0 0c-1 1-1 1-1 2v1h5-24l-13-1-12 1-1-1v-1c-1-1-1-2-1-3h1s0 1 1 1c1-1 1-1 1-3h2v-1c0-1-1-1-1-2-1 0-1-1-1-2h11c2 0 4-1 6 0z"></path><path d="M150 336l1-1c1 0 2-1 3 0h1c1 1 2 1 3 1h0-8z" class="B"></path><path d="M158 336l1-2h4 2 1 1c-1 1-1 2-2 2h-7z" class="D"></path><path d="M118 330l2 2c1 0 1 0 2-1v1 1h0-3v1c1 1 4 1 5 1h0-7c0-1 1-2 1-3h-2v-1l2-1z" class="m"></path><path d="M114 327h11c2 0 4-1 6 0h0-14v3h1l-2 1c0-1-1-1-1-2-1 0-1-1-1-2z" class="O"></path><path d="M114 332h2 2c0 1-1 2-1 3h7 4c5 0 9 1 14 1 2 0 5 1 7 0h1 8c-1 1-1 1-1 2v1h5-24l-13-1-12 1-1-1v-1c-1-1-1-2-1-3h1s0 1 1 1c1-1 1-1 1-3z" class="k"></path><path d="M114 332h2 2c0 1-1 2-1 3h7 4v1h-2-1c-1-1-3 0-3 0h-3c-1 0-2 0-2 1v1-1c1 0 3 1 4 0l1 1h3 0l-12 1-1-1v-1c-1-1-1-2-1-3h1s0 1 1 1c1-1 1-1 1-3z" class="C"></path><path d="M114 332h2 2c0 1-1 2-1 3h0c-2 1-2 1-3 0h-2v2c-1-1-1-2-1-3h1s0 1 1 1c1-1 1-1 1-3z" class="D"></path><path d="M172 339h2c1-1 1-3 1-4l2-9 5-36c1-3 1-6 2-9 0-1 0-2 1-2v1 3l-2 10-7 46h11l1 1h-1c-1 4-1 9-1 13v27 11 33l-1 40c3 1 7 0 10 1h0-11-12-6v-1h7v-17-51-40c0-5 1-11 0-16l-1-1z" class="j"></path><path d="M175 340h10l-1 125-10-1 1-124z"></path><path d="M153 247l9 1c1 1 2 1 2 3l1 1c1-1 2-1 3-2 2 0 5 1 7 0 0-1 2-1 2-2 1 0 1 1 2 1s1-1 1-1c1 0 1 0 2 1 3-2 6-1 9-2h1 2 18c1 1 1 1 1 2l-4 38c-1-3 1-5-2-8-1-1-2-1-2-3-6-1-11-3-16-5-2 0-7 0-9-2l-3-1s-1 0-1 1v3l-1 8h-11-21-14-1-10c-1 1-5 1-6 0h-1v-13-5c0-4 0-9 1-13 1 0 1-1 1-1 1 1 1 1 3 1h3 7 0c1-1 4-1 5-1h2 2c1 0 2-1 3-1 5 1 10 1 15 0z" class="e"></path><path d="M151 260c1 0 1 0 1 1v2h-1v-3z" class="Q"></path><path d="M135 266h1l1 1c-1 0-1 1-2 1l-1 1c0-1 1-2 1-3z" class="M"></path><path d="M132 251v2l1 1h0-1l-1 1c0-1-1-2-2-2h0l1-1 2-1zm6 2h1c1 0 1 0 1 1 1 0 1 1 0 2h-1l-1-1v-2z" class="F"></path><path d="M129 250v-1h1 2c0 1 0 1 1 1l-1 1-2 1h-1v-1l1-1h-1z" class="H"></path><path d="M146 259c1 0 2 0 2 1v1c0 1 0 1-1 2h0-1v-2h1l-1-1v-1zm2 5h1 0v1c0 1 1 1 1 2-1 0-1-1-2-1s-1 1-3 1h-1-1v-1s1 0 1-1h1 1 0c1 0 2 0 2-1z" class="M"></path><path d="M169 267v-2c1-1 2 0 2-2h1s0 2 1 2 1 1 2 2c0 0 1 0 2 1h-1c-2-1-3-1-5-1 0 1-2 0-2 0zm-31-18h1v2h0-1c-1 1-1 1-2 1h-1l-2 2-1-1v-2l1-1c1-1 3-1 5-1z" class="K"></path><path d="M132 253s0-1 1-1c1-1 1 0 2 0l-2 2-1-1zm48 1c1 0 1 1 2 1s1 0 2 1c0 1 0 1-1 2 0-1-1-1-1-1h0c-1 0-1 1-1 1v2c1 0 1 1 1 1l-1 1-1-2-1-1-2 1h-1-1s-1 0-1-1v-1-1-1c2 0 2-1 4 0 1 0 1-1 2-2z" class="M"></path><path d="M175 250c0-1 2-1 2-2 1 0 1 1 2 1s1-1 1-1c1 0 1 0 2 1 3-2 6-1 9-2-1 1-1 2-2 2-1 1-4 0-6 2-1 0-3 1-4 2l-1-1c0-1-1-1-2-1s-1 1-2 1c-2-2-4-1-6-2 2 0 5 1 7 0z" class="d"></path><path d="M117 264c1 0 2 0 3 1h1c0-1-1-1-1-2s1-1 2-1h0v1 1h1c0-1 0-1 1-2 1 0 1 0 2 1 0 1 0 1-1 1l1 1v2l1 1c-1 1-2 1-3 2h0-2v-1-1h-1l-1 2v1h-2v-2l-1-1v-2-1h1l-1-1z" class="f"></path><path d="M112 249c1 0 1-1 1-1 1 1 1 1 3 1 0 1 0 2-1 2 0 1-1 0-1 1 1 0 1 0 2-1v1c1 0 1 1 1 1l-2 2h1v1l-1 2s1 0 1 1l-1 1c0 1 1 1 1 1-1 2-2 0-1 3h0v1c-1 1 0 2 0 3h0l-1 1h-2 0v3 8h-1v-13-5c0-4 0-9 1-13z" class="b"></path><path d="M114 251c0 2 0 2 1 3l-1 2h-1v-1c0-1 0-2 1-4z" class="S"></path><path d="M153 247l9 1c1 1 2 1 2 3l1 1-1 1c-1 0 0-1-1-2h-3-1c-1 0-1 0-2-1l-1-1h-3 0l-1 1v1c-2 1-2 0-4 0v1h0v1-1l-1 1h-1v-1l-1-1c0-1 0-1-1-2l-1 1h-2c0-1-1-1-2-2l-1 1c-2 0-4 0-5 1-1 0-1 0-1-1h-2-1v1l-2 1h-1l-2-2v1 1h-1-1 0c-2-1-2 1-4 1h0c0-1 1-1 1-2v-1h7 0c1-1 4-1 5-1h2 2c1 0 2-1 3-1 5 1 10 1 15 0z" class="X"></path><path d="M148 250c1 0 1 0 1-1 1 1 1 0 2 0 0 0 1 0 1 1v1c-2 1-2 0-4 0v-1z" class="H"></path><path d="M145 251c1 0 1 0 2-1h-1c0-1 0-1 1-2h4-4l1 1c-1 0-1 0-1 1h1v1 1h0v1-1l-1 1h-1v-1l-1-1z" class="M"></path><path d="M194 247h18c1 1 1 1 1 2l-4 38c-1-3 1-5-2-8-1-1-2-1-2-3-6-1-11-3-16-5-2 0-7 0-9-2h0 1 1 1s1 0 1 1h1v-1h0 2 2v1h1c-1-1-1-2-2-2v1h-1s-1 0-1-1h-1s0-1-1-2v-1c1-1 1-1 1-2h0 2 1l-1-2 1-1c1 0 1 0 2-1 0-2-1-1-1-2s1-2 0-3c1-2 2-3 3-4 0 0-1 0-1-1s0-1 1-2h2z" class="E"></path><path d="M115 268c1-1 1-1 1-2l-1-1 1-1h1l1 1h-1v1 2l1 1v2h2v-1l1-2h1v1 1h2 2 0 1l1-1c1 0 2-1 3 0 1 0 1-1 2-1v1c2 0 3 0 5 1 0-1 1-1 1-1h1c2 1 7-1 10 0l3 1c0-1 0-1 1-2l2 1c1 0 1 0 1-1l1 1 1-1c2 0 3-1 4-1h2c1-1 3 0 4 0 0 0 2 1 2 0 2 0 3 0 5 1h1 0s-1 0-1 1v3l-1 8h-11-21-14-1-10c-1 1-5 1-6 0v-8-3h0 2l1-1h0z" class="k"></path><path d="M147 275c1-1 1 0 2 0l1 1h-4-1-2v-1h4z" class="f"></path><path d="M112 272v-3h0 2l1-1v1s1 0 1 1c-1 1-2 2-2 3v1l-1-1h0l-1-1z" class="C"></path><path d="M112 272l1 1h0l1 1v1 5h4 0c-1 1-5 1-6 0v-8z" class="Z"></path><path d="M162 339h5v-1h0c1 0 1 1 1 1 2 1 3 0 4 0l1 1c1 5 0 11 0 16v40 51 17h-7v1c-1 0-2 0-3-1-1 0-2 0-3 1h-9-8 0l7-1v-96c-1-3 0-6-1-9v-18c-2 0-8-1-10 0v-1h-1v-1h24z"></path><path d="M150 368v-22c0-2-1-4 0-6h2 0l-1 124 9 1h-9-8 0l7-1v-96z" class="j"></path><path d="M162 339h5v-1h0c1 0 1 1 1 1 2 1 3 0 4 0l1 1c1 5 0 11 0 16v40 51 17h-7v1c-1 0-2 0-3-1V345c-1-1-1-3-1-5-3 0-6 1-9 0h-1 0-2c-1 2 0 4 0 6v22c-1-3 0-6-1-9v-18c-2 0-8-1-10 0v-1h-1v-1h24z"></path><path d="M162 339h5v-1h0c1 0 1 1 1 1 2 1 3 0 4 0l1 1h-9v30c0 4 1 9 0 14-1-7 1-42 0-44h-1-1c1 2 1 4 1 5-1-1-1-3-1-5-3 0-6 1-9 0h-1 0-2c-1 2 0 4 0 6v22c-1-3 0-6-1-9v-18c-2 0-8-1-10 0v-1h-1v-1h24z" class="N"></path><path fill="#fff" d="M163 345c0-1 0-3-1-5h1 1c1 2-1 37 0 44v80h2v1c-1 0-2 0-3-1V345z"></path><path d="M113 339l12-1 13 1v1h1v1c2-1 8 0 10 0v18c1 3 0 6 1 9v96l-7 1h0-4-1v-1c-2 1-6 1-8 1h-19v-59c1-2 0-5 0-7l1-14v-45s0-1 1-1z"></path><path d="M138 463l-1-1v-17c0-1-1 0-2 0h-2c-1-1-1-1-2-1-1-1-1-2-1-3 2 1 5 2 7 3h0c0-3 0-21 1-22v29 8 4z" class="C"></path><path d="M113 339l12-1 13 1v1 4c0-1 0-3-1-4h-18-7s0-1 1-1z" class="o"></path><g class="q"><path d="M138 340h1v1 20-1-6h-1c0 4 1 31 0 33h0c-1-4 0-9 0-13v-30-4z"></path><path d="M138 387h0c1-2 0-29 0-33h1v6 1 51l1 37c0 5-1 10 0 15 1 1 2 1 3 1h0-4-1v-1-1-4-8-29-15c-1-2 0-4 0-6v-14z"></path></g><path fill="#fff" d="M138 387h0c1-2 0-29 0-33h1v6 1 51 1 19c-1-1 0-9 0-10v-44c-1 0-1 38-1 42 0 3 1 8 0 11v-15c0-3 1-7 0-9s0-4 0-6v-14z"></path><path d="M139 341c2-1 8 0 10 0v18c1 3 0 6 1 9v96l-7 1c-1 0-2 0-3-1-1-5 0-10 0-15l-1-37v-51-20zm432 276h0c-1 2-4 4-6 6-8 6-16 10-26 14-11 3-34 10-46 4-4-2-8-6-9-10-3-11 12-15 19-18 3-1 7-3 10-5 8-4 14-10 19-18 5-10 8-23 5-34-3-9-8-17-15-24-10-9-24-16-37-22l-44-24s-1 4-2 5l-5 14c-1 3-2 6-4 9l-1-158 31 18 50 26 16 11c8 5 15 10 22 17 7 6 12 12 18 19l4 4v4 11l1 33h1v12c0 2-1 5 0 6v7c-1 3-1 9 0 12v2c2 0 3 1 4 2 0 0-1 1-1 2l-1 1-2 4v2l1 52v11c0 1-1 3 0 4l-2 1z"></path><path d="M500 420h0l1 1v2h-2-1 0c1-1 1-2 2-3h0zm51 64h1c0 2 0 3 1 4v1h0-1c-1-1-1-2-1-2v-1h-2c0-1 1-1 2-2z" class="i"></path><path d="M516 473v1h-1c-1-3-3-5-3-7l1-1h1v2c0 2 1 4 2 5z" class="E"></path><path d="M510 419h1c0 1 0 1 1 2h0c-1 1-1 2-1 3s-1 1-1 2h-1v-2-1h0c1-2 1-3 1-4zm35 71h1 0l-1 1c0 1 0 2 1 3-1 1-1 2-2 3l-1-1c1-1 1-2 1-3s-1-1-1-2l1-1v1l1-1zm-31-69l1 1c0 1 1 3 0 4h-2 0l-1-2c0-1 1-2 2-3z" class="i"></path><path d="M494 476h2 2v1c-1 1-3 1-4 2-1 0-1-1-2-1l-1 1h-1c0-1 1-1 2-2h1l1-1z" class="C"></path><path d="M493 477l-1-1c1-2 1-4 2-4v1h1c1 0 1-1 1-2l-1-1 1-1v1c1 1 1 2 1 3h0v1c-1 1-2 1-3 1v1h0l-1 1zm58-8v-1s-1-1-1-2h-2v-1h0c2 0 3 1 4 1l1-1c-1 0-1-1-2-2 1 0 1 0 1-1v-1l1-1c1 2 0 3 0 5 0 1 0 2-1 2 1 2 1 2 0 3h-1v-1z" class="E"></path><path d="M514 466h1v1h2v2l1 1c0 1-1 2-2 3-1-1-2-3-2-5v-2zm37 3v1h0c0 2 2 1 3 2h0c-1 1-1 1-2 1v-1h1 0-2c-1 1-1 1-1 2v1l1-1h0c1 1 1 1 1 2l1 1c0 1 0 1 1 2h0c0 1-1 2-1 3h-1v-2-1c-1-2-2-3-3-5h0l1-3v-1h-1c1-1 1-1 2-1z" class="i"></path><path d="M540 483l1 1 1 1c-1 0 0 0-1 1h3 1c1 1 1 1 1 3-1 1-2 0-3 1l-2-1v1c-1 0-1 0-2-1h0c2-1 1-4 1-6z" class="k"></path><path d="M540 483h0c-1-1-1 0-2-1 1-1 1 0 2-1s-1-3 2-4v2h1c0 1 1 2 1 3h1v1c-1 1-1 1-2 1s-1 1-1 1l-1-1-1-1z" class="i"></path><path d="M506 445h1l1 1 1-1c1 0 3 2 4 2v2c1 0 2 1 2 2h0l-1-1c-1 0-1 0-1-1h-1-1l-1 1c-1-1 0-1-1-2l-3 1h-1c0-2 1-2 1-4h0z" class="E"></path><path d="M571 499h1v12c0 2-1 5 0 6v7c-1 3-1 9 0 12v2h0l-1-1v4 18 8-68z" class="N"></path><path d="M571 567v-8-18-4l1 1h0c2 0 3 1 4 2 0 0-1 1-1 2l-1 1-2 4v2l1 52v11c0 1-1 3 0 4l-2 1v-50z" class="j"></path><path d="M572 540c1 1 2 1 2 1l1 1-1 1-2 4v-7z" class="O"></path><path d="M572 538c2 0 3 1 4 2 0 0-1 1-1 2l-1-1s-1 0-2-1v-2h0z" class="m"></path></svg>