<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="56 40 566 628"><!--oldViewBox="0 0 654 752"--><style>.B{fill:#2a2723}.C{fill:#b7a890}.D{fill:#030303}.E{fill:#d8c6a8}.F{fill:#4d4640}.G{fill:#c5b498}.H{fill:#e3cfae}.I{fill:#d2be9f}.J{fill:#2f2b26}.K{fill:#a3957d}.L{fill:#cdba9e}.M{fill:#cab79c}.N{fill:#a89b87}.O{fill:#2c2825}.P{fill:#48423b}.Q{fill:#dacbb2}.R{fill:#322e29}.S{fill:#35312d}.T{fill:#161513}.U{fill:#e1d1b2}.V{fill:#e6d8be}.W{fill:#0f0e0c}.X{fill:#ede2ca}.Y{fill:#efe3c7}.Z{fill:#f0e2c6}.a{fill:#b19f87}.b{fill:#ddd1b9}.c{fill:#9c8c78}.d{fill:#bdaa8e}.e{fill:#bcad8f}.f{fill:#544c41}.g{fill:#736859}.h{fill:#c4b89f}.i{fill:#776e63}.j{fill:#1c1816}.k{fill:#a19380}.l{fill:#23211d}.m{fill:#847b6e}.n{fill:#1c1a18}.o{fill:#8e816f}.p{fill:#4d4842}.q{fill:#696057}.r{fill:#59524a}.s{fill:#29261f}.t{fill:#7c7568}.u{fill:#282623}.v{fill:#f3ede5}.w{fill:#f4ede4}.x{fill:#f2e7d1}.y{fill:#3d3734}.z{fill:#625c53}.AA{fill:#887b6d}.AB{fill:#3e372d}.AC{fill:#9a8872}.AD{fill:#948572}.AE{fill:#c2aa8f}.AF{fill:#5d554f}.AG{fill:#6a655b}</style><path d="M507 618c0 2 0 2 1 4h1l-1 1h1 0l-2 1c-1-2 0-5 0-6z" class="d"></path><path d="M70 583h1c0 2 0 2-1 2h-1-1v-1l2-1zm31 15h3 0c-1 1-1 2-1 3h-1l-1-1v-2z" class="Z"></path><path d="M233 624c1 1 2 3 2 4-1 0-2-1-3-2h0c0-1 1-1 1-1v-1z" class="r"></path><path d="M277 640v-2h0c1-1 1-1 2-1v3c1 1 0 1 0 2v-2h-1-1v2h0v-1-1z" class="U"></path><path d="M126 551l1-2 1 1v1c1 0 1 1 2 1v2 1l-4-4z" class="c"></path><path d="M179 626h1l1 1c-1 0-2 0-3 1 1 1 2 1 2 2h-1c-1 0-1-1-1-1h-2c-1 0-1 0-1-1 1 0 2-1 3-1l1-1z" class="X"></path><path d="M250 87v-2c1-1 3-1 4-1 1 1 0 1 0 2 1 1 0 3 0 4v-4c-1 1-2 1-4 1z" class="Q"></path><path d="M60 301l1-6c1 0 1 0 1 1h2l-3 6h0l-1-1z" class="B"></path><path d="M84 241l5 2v1c-1 0-1 1-2 1h-1-1v-1c0-1-1-2-1-3z" class="v"></path><path d="M241 644h2c0 1 0 2 1 2 0 1 1 2 1 3s0 1-1 1l-2-2-1-1h1c0-2 0-2-1-3z" class="w"></path><path d="M60 306v-5l1 1h0c1 0 2 1 4 2h0c-2 0-3 1-5 2h0z" class="d"></path><path d="M114 606c1 0 1 1 2 2l2-1c1 1 1 0 2 1v1c-1 0-1 1-2 1l-1-1h-4v-2c0-1 0-1 1-1z" class="Y"></path><path d="M203 635h1 1c0 1-1 2 0 2v1l-1 1-2-1c-2 0-1-1-2-2 1-1 2-1 3-1zm8-19c1 0 2 1 4 2h-2 0l-1 1c0 1-1 1-2 2l-1-1c1 0 1 0 2-1-1-1-2 0-3-1l1-1c1 0 1 0 2-1h0z" class="w"></path><path d="M419 640c1 0 2 0 3 1 0 1-1 1-2 2h-1v-1c-1 1-3 2-4 2 1-2 2-3 4-4z" class="Y"></path><path d="M101 600c-1-1-1-2-1-4h0c-1 0-1 0-1 1h0l-1-1v-2h1l1 1v-1c0-1 1-1 2-2 1 2-1 2-1 3v3 2z" class="v"></path><path d="M376 651c1-1 3-1 4 0 1 0 2 1 2 2h0c2 0 2-1 4-1v1h-2c-1 1-2 0-3 0h-2c0-1-1-1-1-1l-2 1v-1-1z" class="Y"></path><path d="M250 87c2 0 3 0 4-1v4 1-1c-2 0-3 0-4 1 1-1 0-2 0-2v-2z" class="G"></path><path d="M50 570c1 0 2 1 2 2h2v-2h1c1 1 1 2 2 3h0c0 1-1 2-2 2l-2-2c-1 0-2-1-3-1v-2z" class="v"></path><path d="M552 492l1 1-3 7c0 1-1 2-1 3h-1c0-1 0-2-1-3 1 0 2-1 2-2h0 1c0-1 0-3 1-3 0-1 1-2 1-3z" class="s"></path><path d="M570 410h1l2 2c1 1 1 2 0 3v1c-1-1-1-2-2-2-3 0-1-1-2-3l1-1z" class="AB"></path><path d="M453 623c1 0 1-1 1-1l2-1c2-1 5-2 7-2h1c-1 1-3 2-4 3 0 0-1 0-2 1 0-1-1-1-1-1l-2 2c-1 0-1 0-2-1zm-220 1l1-1h1l1 2c1 0 2 0 3-1v4 1 1c0-1 0 0-1-1h0v-1l1-1h-2c-1 0-1 0-2 1 0-1-1-3-2-4z" class="w"></path><path d="M188 576h1l1 1c1 1 1 1 1 2-1 1 0 1 0 2l-2 3c-1-1 0-1 0-2h-2c-1 0-1 0-1-1v-1c2 0 3-1 4-2l-1-1h-2 0l1-1z" class="X"></path><path d="M555 231c1 1 1 1 1 2 1 1 1 4 1 5 0 0-1-1-2-1 0-1 0-1-1-1 0-1-1-1-1-2s1-2 2-3z" class="a"></path><path d="M571 406l2-1 1 1c0 1 0 2-1 4v2l-2-2h-1l-1-2c0-1 1-1 2-2z" class="U"></path><path d="M453 623c1 1 1 1 2 1-3 2-5 4-7 6l-2-3h0c1 0 1-1 2-1 2 0 4-1 5-3z" class="X"></path><path d="M562 270c2-1 3 0 5 0l-1 1h-1-1c0 1 1 1 2 2l2-2c0 1 1 1 1 2l-1 1 1 1c-1 1-1 2-2 2 0-1-2-2-3-4v-1l-2-2z" class="R"></path><path d="M419 640l1-1c0-1-1-1-2-1 2-1 3-1 5 0v2s-1 1-1 2h1v1 2h-1c-1 1-2 2-3 2v-1h1l-1-1v-2h1c1-1 2-1 2-2-1-1-2-1-3-1z" class="v"></path><path d="M112 605c-1 1-1 1-1 2h-1l-2-2h0c-1-1 0-3-1-5h0v-2c1 1 1 2 2 3 0 0 1 0 1 1h1l1 1v2z" class="X"></path><path d="M111 602l1 1v2h-2l-1-1c1 0 1-1 2-1v-1z" class="v"></path><path d="M260 632h3v1h-2c-1 0-4 0-5 1h-3c-2 0-7 0-8-1l1-1h3 1 1c3 1 7 1 9 0z" class="AB"></path><g class="Y"><path d="M446 627l2 3c-3 1-5 2-7 3-1 0-1-1-2-1l-1 1-1-1h1c2 0 5-4 8-5z"></path><path d="M436 632l1 1v1c0 1 0 2-1 2-1 1-3 2-4 1h-1c0 1 0 1-1 1h-1c-1 0-1 0-2 1-1 0-1 0-1-1h0c1 0 1-1 2-1 2-1 3-1 4-2l1-2h3v-1z"></path></g><path d="M244 630c5 0 10 1 15-1l1 3c-2 1-6 1-9 0h-1-1c-2-1-3-1-5-2z" class="L"></path><path d="M282 625h3 1c2 0 11 0 13 1v1c2 0 2-1 3 0 1 2 0 5 0 7v-1c-1-1-1-2-1-2v-3-1c-2 0-5 1-7 0h-9c-1 0-2-1-3-2z" class="d"></path><path d="M59 310c2 1 4 1 6 1l-1 2v4h-1l-1 1-2-2s-1 1-1 2h0v-6-2z" class="Q"></path><path d="M59 310c2 1 4 1 6 1l-1 2h-2v1h-2c0-1 0-1-1-2h0v-2z" class="H"></path><path d="M569 275c0 1 0 2 1 2 0 2 0 4 1 5v1 1l-2 2v-1c-1 1-1 2-1 2-1 1-1 1-2 0v-1l-1 1v-1l1-1c0-1 0-1 1-1 1-2 1-4 0-7 1 0 1-1 2-2z" class="AB"></path><path d="M570 277c0 2 0 4 1 5v1 1l-2 2v-1c-1 1-1 2-1 2-1 1-1 1-2 0v-1c2-3 4-5 4-9zM186 627h1 2c1 0 1 0 2 1 2 0 2 2 2 4 2 0 2 1 3 1l1 1c0-1 0-1 1-1v-1h1v1l-1 2c1 0 1 1 1 2l-1 1h0c0-2-1-2-2-3l-2-2c-1 0-1 0-2 1 0-1-1-1-1-2h1v-2s-1-1-1-2c0 0-1 1-2 1l-1-1h-1l1 2c-1 0-1 1-2 1v1h-1v-2l1-3z" class="X"></path><path d="M563 282c1 1 1 3 2 3h1l-1 1v1l1-1v1c1 1 1 1 2 0 0 0 0-1 1-2v1l2-2v3 1l-1 1v1h0-1-3l-1-1h-1c-1 0-1 0-2 1v-1h-1-1l1-1h1c1-2 1-4 1-6z" class="C"></path><path d="M565 289c3 0 3-1 5 0v1h0-1-3l-1-1z" class="V"></path><path d="M240 90l1-1v-2c0-1 0-1 1-1h4l-1 2 2 1v3h-1v1h-2-1c0 1 1 1 0 2h-2 0v-2h-1v-3z" class="Q"></path><path d="M241 93c0-1 0-1 1-2 1 1 1 1 1 2s1 1 0 2h-2 0v-2z" class="Z"></path><path d="M243 93l1-2c-1-1-1-1-1-2l1-1h1l2 1v3h-1v1h-2-1z" class="d"></path><path d="M91 244h1 2c1 1 1 2 1 3s0 0 1 1c0 1-1 1-1 2v1c1 1 1 2 0 3h-1-4c1-2 1-4 1-6v-4z" class="Z"></path><path d="M91 248v-2h1c0 2-1 3 0 5h1c1 1 1 2 1 3h1-1-4c1-2 1-4 1-6z" class="H"></path><path d="M531 254c2 0 4 0 5 1 1-1 2-1 3-1v3 1 1h-1c0 1-1 2-1 3-1 0-3-1-4-1l-2-7z" class="v"></path><path d="M68 361c1 0 1 1 1 2l-8 9-4 4c-1 1-4 4-6 5l6-6v-1c1-3 8-11 11-13z" class="T"></path><path d="M65 304c1 0 3 1 5 1h-3l2 1 1 1c-1 1-2 1-3 1 0 1-1 2-1 3h0-1c-2 0-4 0-6-1v-2l1-2h0c2-1 3-2 5-2z" class="N"></path><path d="M65 304c1 0 3 1 5 1h-3l2 1c-2 0-6 1-9 0 2-1 3-2 5-2z" class="W"></path><path d="M59 308h8c0 1-1 2-1 3h0-1c-2 0-4 0-6-1v-2z" class="B"></path><path d="M135 172c-1-3-5-3-7-5v-2s-1-1-2-1c-1-1-4-2-6-3 2-1 4 1 5 1s1-1 2-1h0l-4-3v-1c2 1 4 3 7 4h0-1v1h-1l1 1h2c1 1 2 1 3 1h0l1 8z" class="L"></path><path d="M131 165c1 1 2 1 3 1v2h0c-2-1-2-1-3-3z" class="V"></path><path d="M131 163c1 1 2 1 3 1h0v2c-1 0-2 0-3-1h0c-1 0-2-1-2-1l2-1zm119-74s1 1 0 2c1-1 2-1 4-1v1 2c-1 1-1 1-3 2h0-1l2 1c-1 1-2 3-3 4h0l-3-3v-2-2-1h1v-3h3z" class="Y"></path><path d="M250 91c1-1 2-1 4-1v1 2c-1 1-1 1-3 2v-1l1-1v-1h-1l-1-1z" class="e"></path><path d="M155 577l1-1h6c1 1 2 1 2 3h0 2c0 1 1 4 0 5v1c-1 0-2 0-3-1-1 0-4-3-6-4v-1c-1-1-1-2-2-2z" class="T"></path><path d="M155 577l1-1h6c1 1 2 1 2 3h0-6l-1 1v-1c-1-1-1-2-2-2zM68 437h1v-1c1 0 1 1 2 1l1-1 1 1c1 0 1 0 1-1 1 1 1 1 3 1h-1v1c0 2-2 4-3 5s-2 4-2 5v1l-1-1-2-6 1-1c0-1-1-1-1-2v-2z" class="X"></path><path d="M515 255c0-2 0-4 1-6 0-2 1-4 3-5h4c4 2 6 7 8 10l2 7v1 1h-1c-1 1-1 0-1 1l-2 2h-2l3-3 1-1h0c1-2-1-6-2-8s-3-5-5-7c-1-2-2-2-4-2-3 2-3 4-3 6l-1 6v1c-1-1-1-2-1-3z" class="R"></path><path d="M72 351l2 1c0 1-1 2-1 3h0c-2 1-3 2-5 3v-1l-1-1c-4 3-6 8-10 10v-1-3-4c1 0 1 1 2 1 1-1 2-1 3-2l1 1 4-4h1l4-3z" class="F"></path><path d="M72 351c0 2 0 3-1 4-1 0-2 0-3-1h0l4-3z" class="l"></path><path d="M57 358c1 0 1 1 2 1 1-1 2-1 3-2l1 1c-2 2-4 4-6 7v-3-4z" class="v"></path><path d="M166 568c0 1 1 2 1 3l2 2c0 1 1 2 2 3h-1-4-4-6l-1 1c-3 0-4-3-7-5l11 1c2-1 5 0 7-1l-1-3 1-1z" class="D"></path><path d="M166 568c0 1 1 2 1 3l2 2c0 1 1 2 2 3h-1-4l1-2c-1-2-7 0-8-1 2-1 5 0 7-1l-1-3 1-1z" class="O"></path><path d="M69 363l1 1c0 2 1 3 1 4v1l-6 6c-2 1-3 3-5 4h-1-2l1-2h0l-1-1 4-4 8-9z" class="x"></path><path d="M517 251c0-2 0-4 3-6 2 0 3 0 4 2 2 2 4 5 5 7l-1 1c-4-1-8 0-11-1-1-1 0-2 0-3z" class="Y"></path><path d="M78 346l7-4v1c-2 2-6 4-9 5l-1 1c-1 1-2 2-3 2l-4 3h-1l-4 4-1-1c-1 1-2 1-3 2-1 0-1-1-2-1 0-2-1-4 0-6h1c2 0 5-1 7-1l1 1c0-2 1-2 1-4v2l1-1 1 1 1 1 1-1c2 0 3-2 5-3 0 0 1-1 2-1z" class="p"></path><path d="M57 352v2c2 0 4 0 6-1 1 0 2 0 3-1h0l1 2-4 4-1-1c-1 1-2 1-3 2-1 0-1-1-2-1 0-2-1-4 0-6z" class="Z"></path><path d="M59 318c0-1 1-2 1-2l2 2 1-1v10h0v4 1 2s0 1-1 1l2 1h-2l-2 1c-1 1-1 1-2 1v-2-9l1-9h0z" class="E"></path><path d="M59 318c0-1 1-2 1-2l2 2v1 1c-1-1-2-2-3-2z" class="I"></path><path d="M58 327l2 2v3 1 1s-1 1-1 2h-1v-9z" class="Q"></path><path d="M560 267c0 1 1 1 1 2 1-2 4-3 6-3v2h0l-1-1c-2 1-3 2-5 2v1h1l2 2v1c1 2 3 3 3 4 1 3 1 5 0 7-1 0-1 0-1 1h-1c-1 0-1-2-2-3 0-1-1-2-1-3-2-1-3-2-4-2l-2-2 1-2 1 1 1-2v1c0-1 1-1 1-2-1-1-1-2-2-3l1-1h1z" class="AB"></path><path d="M560 271c2 2 4 4 4 7h0 0c-2-2-4-3-6-4l1-2v1c0-1 1-1 1-2z" class="I"></path><path d="M557 273l1 1c2 1 4 2 6 4-1 0-2 0-2 1-2-1-3-2-4-2l-2-2 1-2z" class="P"></path><path d="M565 285h0v-8c0-2-1-2-1-4 1 2 3 3 3 4 1 3 1 5 0 7-1 0-1 0-1 1h-1z" class="H"></path><path d="M71 369l1 2v3l-8 9c-2 2-4 4-5 6h-1v-2l-1-2c0-2 2-4 3-6 2-1 3-3 5-4l6-6z" class="D"></path><path d="M516 257l1-6c0 1-1 2 0 3 3 1 7 0 11 1l1-1c1 2 3 6 2 8h0c-1 0-2-1-3-1-1-1-2-1-3-2-2 0-3 2-4 3h0c-1 1-2 1-3 1h-2v-1c-2-1 0-1 0-3-1 0-1 0-1-1h1v-1z" class="H"></path><path d="M516 257l1-6c0 1-1 2 0 3 3 1 7 0 11 1l-1 1c-2 0-5-1-7 0v-1h-2l1 1h0-2l-1 1z" class="E"></path><path d="M516 259c1 0 1 0 2-1l1 1v2l2 1h0c-1 1-2 1-3 1h-2v-1c-2-1 0-1 0-3z" class="I"></path><path d="M521 221h1c0-1 1-1 1-2l1 1-1 1h1l1-1v2h0c2 0 2 1 3 1 1 1 1 1 2 1l1-1v1h1c1 1 1 1 2 1h2l1-3h0v2h0 0v1 3l1 1h1c0 1 0 1 1 1 0 0 0 1-1 2l-5-2-16-6-4-1c-1-1-2-1-3-2 1 0 2-1 2-1 3 0 5 0 7 1h1z" class="d"></path><path d="M513 220c3 0 5 0 7 1v1c-1 0-2 0-3-1h-1v1c1 0 2 0 3 1h0c0 1 0 1-1 1l-4-1c-1-1-2-1-3-2 1 0 2-1 2-1z" class="K"></path><path d="M521 221h1c0-1 1-1 1-2l1 1-1 1h1l1-1v2h0c2 0 2 1 3 1 1 1 1 1 2 1l1-1v1h1c1 1 1 1 2 1l1 1v1c-2 0-2 0-3-1-1 0-2 0-3-1 0 0-1 0-2-1-1 0-2 0-3-1h0-1-1l-1-1v-1z" class="AA"></path><path d="M64 336c0 1 1 1 2 1h0v3 2c0 1 1 3 0 4v1h2l-1 1c0 2-1 2-1 4l-1-1c-2 0-5 1-7 1v-2c-1-3 0-6 0-9v-1c1 0 2-1 2-2v-1l2-1h2z" class="v"></path><path d="M65 346l1 1h2l-1 1c0 2-1 2-1 4l-1-1h1c0-1-1-3-1-5z" class="q"></path><path fill="#fff" d="M60 342l1-2c0 1 1 1 1 1l1 1h1v2h-2v1l-2 2h0v-2c0-1 0-1 1-2v-1h-1z"></path><path d="M64 336c0 1 1 1 2 1h0v3 2c0 1 1 3 0 4v1l-1-1c-1-3-1-7-1-10z" class="AF"></path><path d="M62 336c0 2-1 3-2 4v1 1h1v1c-1 1-1 1-1 2v2l-1 2-1 1c-1-3 0-6 0-9v-1c1 0 2-1 2-2v-1l2-1z" class="Z"></path><path d="M73 373c1 1 1 2 1 4h0c1 1 1 1 1 2l-11 13c-1 1-4 5-5 5l-1-1c1-2 0-4 0-7h1c1-2 3-4 5-6l8-9 1-1z" class="x"></path><path d="M74 377c1 1 1 1 1 2l-11 13c-1 1-4 5-5 5v-1h0c0-3 12-16 15-19z" class="T"></path><path d="M521 262c1-1 2-3 4-3 1 1 2 1 3 2 1 0 2 1 3 1l-1 1-3 3 1 1h0l-1 1v1l-1 1-1 2h-2 0l-1 1c0 1 0 2-1 3-1-1-1-1-3-2l-1-1-2-3c1 0 1 0 1 1h1c1-1 1-1 1-2h0-3l-3 3-1-1c1-2 1-2 2-3l1-2c1-1 2-2 2-3h2c1 0 2 0 3-1h0z" class="R"></path><path d="M519 264h1 0c1 1 0 0 1 0v2 1c-1 0-1 0-2 1h-1c0-1 1-2 1-3v-1z" class="AB"></path><path d="M516 263h2c1 0 2 0 3-1-1 1-1 2-1 2h-1-2c0 2-1 3-2 5l-3 3-1-1c1-2 1-2 2-3l1-2c1-1 2-2 2-3z" class="C"></path><path d="M523 267l2 1c0 1 1 1 1 2l-1 2h-2 0l-1 1c0 1 0 2-1 3-1-1-1-1-3-2h1v-1-2c1-2 2-3 4-4z" class="K"></path><path d="M523 267l2 1c-1 1-1 2-2 2s-2 1-2 2h-1 0l-1 1v-2c1-2 2-3 4-4z" class="m"></path><path d="M521 262c1-1 2-3 4-3 1 1 2 1 3 2 1 0 2 1 3 1l-1 1-3 3 1 1h0l-1 1v1l-1 1c0-1-1-1-1-2l-2-1h-2v-1-2c-1 0 0 1-1 0h0s0-1 1-2h0z" class="N"></path><path d="M527 263l1-1h0c1 0 1 1 2 1l-3 3-1-1c-1-1-1-2-1-3 1 1 1 1 2 1h0z" class="f"></path><path d="M521 262c1-1 2-3 4-3 1 1 2 1 3 2 1 0 2 1 3 1l-1 1c-1 0-1-1-2-1h0l-1 1-2-2c0 1-1 1-1 2v2c-1 0-2 0-3 1v-2c-1 0 0 1-1 0h0s0-1 1-2h0z" class="r"></path><path d="M119 180c0-3 2-6 3-9-1 0-3-1-4 0h0-3l1-1h2 0c1-1 3-1 4 0h4c1 1 3 0 4 1 1 0 2 0 3 1h1 1 0v5 3l-1 1h-4-1-6c-2 0-3 0-4-1z" class="Z"></path><path d="M156 564c2 0 8 1 9 0 0-1 0-1 1-2h0 0c2-1 2 0 3 0 0 1-1 1-1 2h0c1 1 1 1 2 1v1 1 1c1 1 1 2 1 3h1 0 1c0 1 0 1 1 2l-2 2v1h-1c-1-1-2-2-2-3l-2-2c0-1-1-2-1-3l-1 1h-20c-2-1-3-3-5-4h6c3-1 7-1 10-1z" class="D"></path><path d="M156 564c2 0 8 1 9 0 0-1 0-1 1-2h0 0c2-1 2 0 3 0 0 1-1 1-1 2-1 1-1 1-1 2 1 2 0 2 0 3 1 1 2 2 2 3v1l-2-2c0-1-1-2-1-3s0-2-1-3c-2-1-4 0-7 0h-12c3-1 7-1 10-1z" class="t"></path><path d="M169 573v-1c0-1-1-2-2-3 0-1 1-1 0-3 0-1 0-1 1-2h0c1 1 1 1 2 1v1 1 1c1 1 1 2 1 3h1 0 1c0 1 0 1 1 2l-2 2v1h-1c-1-1-2-2-2-3z" class="F"></path><path d="M170 568c1 1 1 2 1 3h1 0 1c0 1 0 1 1 2l-2 2v-1-1-2l-1 1c-1-1-1-3-1-4z" class="S"></path><path d="M504 251h1v-3c1 1 1 2 1 3h1l1-2v1c1 1 1 2 2 2 2 1 3 2 4 3h1c0 1 0 2 1 3h-1c0 1 0 1 1 1 0 2-2 2 0 3v1c0 1-1 2-2 3l-1 2-1-1h0c-2-1-3-2-3-2l-3-2c0-1-1-2-2-2l-2-2-1-1c0-1 0-2-1-3 0-1-1-1-1-2h-2l1-1h1 3v-1h2z" class="y"></path><path d="M508 250c1 1 1 2 2 2v4 2h-2v-1h-2v-2h0l2 2v-2-2-3z" class="G"></path><path d="M504 251h1v-3c1 1 1 2 1 3h1l1-2v1 3 2c0-1 0-1-1-2h-1v1c-1 0-1 0-2-1h-1c-1 0-1 0-1-1v-1h2z" class="h"></path><path d="M498 252h1 3c0 1 0 1 1 1h1c1 1 1 1 2 1-1 1-1 2-1 4 0 1 1 1 2 2l1 2-2 1c0-1-1-2-2-2l-2-2-1-1c0-1 0-2-1-3 0-1-1-1-1-2h-2l1-1z" class="G"></path><path d="M510 252c2 1 3 2 4 3h1c0 1 0 2 1 3h-1c0 1 0 1 1 1 0 2-2 2 0 3v1c0 1-1 2-2 3l-1 2-1-1h0c-2-1-3-2-3-2l-3-2 2-1h1 0c0-2-1-3-1-4h2v-2-4z" class="r"></path><path d="M510 258c1 0 2-1 3-2h1l-2 2h1v2l-1 1c-1 0-1 0-2-1v-2z" class="T"></path><path d="M510 260c1 1 1 1 2 1v1c1 1 1 1 1 2h0l1 2-1 2-1-1h0c-1-3-2-5-2-7z" class="F"></path><path d="M513 264l1 2-1 2-1-1c0-2 0-2 1-3z" class="AB"></path><path d="M510 252c2 1 3 2 4 3v1h-1c-1 1-2 2-3 2h0v-2-4z" class="s"></path><path d="M508 258h2 0v2c0 2 1 4 2 7-2-1-3-2-3-2l-3-2 2-1h1 0c0-2-1-3-1-4z" class="e"></path><path d="M166 576h4 1 8c3 0 6 0 8 1h0 0l-2 2c-1 2-2 3-3 4l-1 1-2 2-1 1c-1 2-3 2-3 4l-1 1-1-1v-3l-1-1h1c0-1 1-2 1-2v-1s-1 0-1-1l-1-1c0 1 0 2-1 3l-2-1h-2c0 1 0 2 1 2 0 1 1 2 2 3h-1c-1-1-2-1-3-3v-1-1c1-1 0-4 0-5h-2 0c0-2-1-2-2-3h4z" class="Y"></path><path d="M169 584c0-1 0-1 1-2 2 0 1-1 2-2 2 1 1 2 2 3h1c-1-1-1-1-1-2 1 0 2-1 2-1h1v1c-1 1-1 2-2 3l-1 1v-1s-1 0-1-1l-1-1c0 1 0 2-1 3l-2-1z" class="v"></path><path d="M177 580c1 1 2 3 2 4 0 0 0 1-1 1-1 2-4 4-5 6v-3l-1-1h1c0-1 1-2 1-2l1-1c1-1 1-2 2-3v-1z" class="w"></path><path d="M74 327c1 0 1 0 2 1l-1 2c1 1 1 2 1 4l-1 1h0c-1 1-1 3-2 4v1c-1 0-1 1-2 2 0 1 1 1 1 2 1 2-1 1 1 3h3c-2 1-3 3-5 3l-1 1-1-1-1-1-1 1v-2l1-1h-2v-1c1-1 0-3 0-4v-2-3h0c-1 0-2 0-2-1l-2-1c1 0 1-1 1-1v-2c2-1 4-2 5-3h2c1 0 1-1 1-2h3z" class="n"></path><path d="M63 332c2-1 4-2 5-3h0c0 1 0 1 1 1l-1 1v3h0l-1 1h0c0 1 0 2-1 2h0c-1 0-2 0-2-1l-2-1c1 0 1-1 1-1v-2z" class="F"></path><path d="M63 334h1c1 0 1-1 1-2h1c1 1 1 2 1 3h0c0 1 0 2-1 2h0c-1 0-2 0-2-1l-2-1c1 0 1-1 1-1z" class="r"></path><path d="M69 339c1 0 1 0 2-1v1 1l2-2-1-1h-1 0v-1c0-1 0-1 1-2l1-1c1 0 1 0 2 1v1h0c-1 1-1 3-2 4v1c-1 0-1 1-2 2 0 1 1 1 1 2 1 2-1 1 1 3h3c-2 1-3 3-5 3l-1 1-1-1-1-1-1 1v-2l1-1h-2v-1c1-1 0-3 0-4v-2-3c1 0 1-1 1-2 1 1 1 1 1 2s1 1 1 2z" class="J"></path><path d="M69 347l1-1v1c0 1-1 2-1 3l-1-1-1 1v-2l1-1h1z" class="y"></path><path d="M67 335c1 1 1 1 1 2s1 1 1 2h0l-1 1c-1 0-1 1-1 1 0 2 0 3 1 4v1l1 1h-1-2v-1c1-1 0-3 0-4v-2-3c1 0 1-1 1-2z" class="R"></path><path d="M61 414c1 1 1 1 2 1 2 1 7 1 10 1h1v4l-4 8v2 1c1 0 1 2 1 3l1 2-1 1c-1 0-1-1-2-1v1h-1l-1-1-1 1h-1c-1-2-1-4-1-6l-3-17z" class="F"></path><path d="M70 417h1c0 3-1 5-2 9l-2 7h-1 0c0-2 1-4 2-6 0-2 1-3 1-5s0-4 1-5z" class="AF"></path><path d="M73 416h1v4l-4 8v2 1c1 0 1 2 1 3l1 2-1 1v-2l-1-1v-3h-1c0-3 2-6 2-9 1-2 1-4 2-6z" class="q"></path><path d="M83 228l2-1c1 1 1 1 1 2s1 1 1 2v1h1l1-1h3v1h0s1 0 1-1l1 1 1 1c1 1 1 3 1 3h-1c1 1 0 2 0 3v1 2c0 2 1 3 1 5v1c-1-1-1 0-1-1s0-2-1-3h-2-1s-1-1-2-1l-5-2c-2-1-5-2-7-2h0v-1-2c1 0 2 0 3 1h0c1-1 1-1 2-1v-2l-1-2h-1c0-2 2-2 2-4v-1l1 1z" class="AA"></path><path d="M95 240h-1c0-2-2-3-2-4l1-2c1 1 2 1 2 2 1 1 0 2 0 3v1z" class="L"></path><path d="M88 232l1-1h3v1h0v2c-2 0-3 0-4-1v-1z" class="Z"></path><path d="M83 228l2-1c1 1 1 1 1 2s1 1 1 2v1h1v1c-2 0-4 0-5-1 0-2 1-3 1-4h-1z" class="Y"></path><path d="M81 232l10 4c-4 1-8 1-11 2h-3v-2c1 0 2 0 3 1h0c1-1 1-1 2-1v-2l-1-2z" class="w"></path><path d="M86 241c-1-1-3-2-5-2 2 0 3 0 4-1h2c1 1 1 1 2 1h0l-1-1c1-1 2-1 3-1s1 1 1 2c1 2 0 2 0 3l-1 1-5-2z" class="b"></path><path d="M86 241l1-2c2 1 2 1 3 1l1 1 1 1-1 1-5-2z" class="Z"></path><defs><linearGradient id="A" x1="539.859" y1="231.821" x2="535.349" y2="239.507" xlink:href="#B"><stop offset="0" stop-color="#aca085"></stop><stop offset="1" stop-color="#c7b4a0"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M556 240h1c2 1 2 2 2 4h0l-43-15 1-3v-1l15 6h2v-1l5 2 10 3 6 2c1 0 2 1 2 1l-1 2z"></path><path d="M534 230l5 2 10 3 6 2c1 0 2 1 2 1l-1 2-24-9h2v-1z" class="Z"></path><path d="M90 205c1-1 1-2 2-4 1 0 3 2 4 0l-2-1s-1 0 0-1v-1l1-1v2h1v-2l1-1c1 0 2-1 2-2l1 1c0 2 0 4-1 5 0 1 0 3 1 4-1 0-1 1-2 1 0 1 1 1 1 2h1c0 1-1 2-1 4-1 1-1 2-1 4h1v1 1c-1 0-2 1-3 1l1 2h-1s0-1-1-1c0 1-1 1-1 1h-2v1l-2 1h-3s-1-1-1-2c-1 1-2 1-2 1 0-2 2-2 3-3v-1c-1 0-1 0-2-1 0-1 0-1 1-2h2c0-2-1-2-1-3l1-2c1-1 1-2 2-3v-1z" class="i"></path><path d="M88 209c1-1 1-2 2-3l2 2c-1 1-3 1-4 1z" class="w"></path><path d="M90 210h3c0 1 1 1 0 3v1c-1 0-1 1-2 1v-3c0-1-1-1-1-2z" class="U"></path><path d="M87 211c1 0 2 0 3-1 0 1 1 1 1 2v3c-2 0-3 0-5-1h2c0-2-1-2-1-3z" class="Z"></path><path d="M88 219l1-1v-1c1-1 2-1 4-1h0l-1 1c0 1 0 1 1 2l2-1h0 1l1 2h-1s0-1-1-1c0 1-1 1-1 1h-2v1l-2 1h-3s-1-1-1-2c0 0 1-1 2-1z" class="X"></path><path d="M86 220s1-1 2-1 1 0 2 1v2h-3s-1-1-1-2z" class="x"></path><path d="M90 205c1-1 1-2 2-4 1 0 3 2 4 0l-2-1s-1 0 0-1v-1l1-1v2h1v-2l1-1c1 0 2-1 2-2l1 1c0 2 0 4-1 5 0 1 0 3 1 4-1 0-1 1-2 1 0 1 1 1 1 2h1-1s0 1-1 1v-1c-1-1-1-1-3-1h0c0 1 0 1 1 1v2c0 1-1 1-1 2h2c0 2-1 1-1 3v2h-1c-2-2 0-4-1-6h0l1-1-1-1h0c-1 0-1 0-1-1 1-1 1-2 2-2h0v-1c-2 0-2 1-4 2l-1-1z" class="b"></path><path d="M504 219c3 0 6 0 9 1 0 0-1 1-2 1 1 1 2 1 3 2h0v2c-1 0-2 0-2 1s0 1-1 2c-1 0-1 0-2 1 1 2 2 4 4 6h0c-1 2-1 3-1 5h-1-1c0 1 0 1 1 2 0 1-1 1-1 2l-1 1h0l-3-6v5c1 1 2 2 1 3 0 1 0 2 1 2l-1 2h-1c0-1 0-2-1-3v3h-1-2v1h-3v-4s1 0 2-1c0-1 1-1 1-2v-4-8-8-3l2 1v-4z" class="N"></path><path d="M502 233l2-1v3 11c-1-3 0-6-1-7-1 0-1 1-1 2v-8z" class="R"></path><path d="M507 228c1 4 3 8 4 12h1-1-1c0 1 0 1 1 2 0 1-1 1-1 2l-3-5v-3c0-1-1-2-2-3 0-2 0-3 2-5z" class="v"></path><path d="M502 241c0-1 0-2 1-2 1 1 0 4 1 7v5h-2v1h-3v-4s1 0 2-1c0-1 1-1 1-2v-4z" class="B"></path><path d="M499 248s1 0 2-1c0-1 1-1 1-2v6 1h-3v-4z" class="X"></path><path d="M504 219c3 0 6 0 9 1 0 0-1 1-2 1 1 1 2 1 3 2h0v2c-1 0-2 0-2 1s0 1-1 2c-1 0-1 0-2 1 1 2 2 4 4 6h0c-1 2-1 3-1 5h-1c-1-4-3-8-4-12 0-1-1-1-1-2l-1 1c0 2 0 2-1 4v4-3l-2 1v-8-3l2 1v-4z" class="AC"></path><path d="M506 221c3 1 4 3 5 6v1c-1 0-1 0-2 1-1-2-4-6-4-8h1z" class="b"></path><path d="M504 219c3 0 6 0 9 1 0 0-1 1-2 1 1 1 2 1 3 2h0v2c-1 0-2 0-2 1s0 1-1 2v-1c-1-3-2-5-5-6 0-1-1-1-2-1v1c1 3 0 7 0 10v4-3l-2 1v-8-3l2 1v-4z" class="F"></path><path d="M502 222l2 1v2h-2v-3z" class="n"></path><path d="M502 225h2v7l-2 1v-8z" class="J"></path><path d="M511 227v-1c0-1 0-3-1-4 0 0-1-1-2-1v-1c1 0 2 0 3 1s2 1 3 2h0v2c-1 0-2 0-2 1s0 1-1 2v-1z" class="C"></path><path d="M537 222c1-1 1-3 2-4s2-2 2-4h1c0-1 1-3 1-4v-1c1-2 1-2 1-3 2 3 2 5 3 8 1 1 1 1 2 1h0c1 1 1 2 1 3h0l-1 1v2c1 0 1 0 2 1 1 2 3 4 3 6l-1 2 2 1c-1 1-2 2-2 3s1 1 1 2c1 0 1 0 1 1l-6-2-10-3c1-1 1-2 1-2-1 0-1 0-1-1h-1l-1-1v-3-1h0 0v-2h0z" class="i"></path><path d="M544 216l2 1 1-1 1 1c-1 2-1 3-2 4h-1c0-1 0-2-1-3v-2z" class="X"></path><path d="M546 225v2h2l2-2c0 1 0 1 1 2l1 1-1 1h-2c-1 0-3-1-3-2v-2z" class="E"></path><path d="M549 221c1 0 1 0 2 1 0 2 2 4 1 6l-1-1c-1-1-1-1-1-2l-2 2h-2v-2h1l2-1c-1 0-1 0-1-1h-1l1-1c0-1 1-1 1-1z" class="Z"></path><path d="M544 227v4h1 0v-1c1-1 1-1 2-1v1h0v1c1 0 1 1 2 1v-1l-1-1h2 1v1c1 1 1 0 1 1v2h-2l-1 1-10-3c1-1 1-2 1-2 1 0 1 0 2-1l2-2z" class="e"></path><path d="M552 232v2h-2l-2-1h1l3-1z" class="I"></path><path d="M537 222c1-1 1-3 2-4s2-2 2-4h1c0-1 1-3 1-4v-1c1-2 1-2 1-3 2 3 2 5 3 8 1 1 1 1 2 1h0c-1 1-1 1-1 2l-1-1-1 1-2-1v2c1 1 1 2 1 3h1l-1 2c-1 2 0 3-1 4l-2 2c-1 1-1 1-2 1s-1 0-1-1h-1l-1-1v-3-1h0 0v-2h0z" class="a"></path><path d="M544 216l-1-1 1-1v-1h-1c1-1 1-1 1-2s0-1 1-2v1 2c1 0 1 0 1 1v1h1c1 1 1 1 2 1h0c-1 1-1 1-1 2l-1-1-1 1-2-1z" class="v"></path><path d="M538 222c1 0 1-1 1-1 1-2 2-3 3-4v-1h1v2h1c1 1 1 2 1 3h1l-1 2c-1 2 0 3-1 4l-2 2c-1 1-1 1-2 1v-3-1h-2v-4z" class="Y"></path><path d="M540 230v-3-1h-2v-4l1 3v1l2-2s3-1 4-1c-1 2 0 3-1 4l-2 2c-1 1-1 1-2 1z" class="AE"></path><path d="M141 555h6l19-1v2 1s0 1 1 2c-1 0-1 0-1 1v2h0c-1 1-1 1-1 2-1 1-7 0-9 0-3 0-7 0-10 1h-6c-1-2-3-3-4-5l-5-4c2-1 7-1 10-1z" class="j"></path><path d="M166 560v2h0c-1 1-1 1-1 2-1 1-7 0-9 0-3 0-7 0-10 1h-6c-1-2-3-3-4-5h21c3-1 6 0 9 0z" class="w"></path><path d="M166 560v2h0c-1 1-1 1-1 2-1 1-7 0-9 0 2-1 4 0 6 0 1 0 2 0 3-1v-1h-3c-1-1-1-1-1-2h-4c3-1 6 0 9 0z" class="v"></path><path d="M279 630v-2c1-2 2-2 3-3 1 1 2 2 3 2h9c2 1 5 0 7 0v1 3s0 1 1 2v1 9 2l-16-2c-2 0-5 0-7-1 0-1 1-1 0-2v-3-7z" class="N"></path><path d="M492 252h3 3l-1 1h2c0 1 1 1 1 2 1 1 1 2 1 3l1 1 2 2c1 0 2 1 2 2l3 2s1 1 3 2h0l1 1c-1 1-1 1-2 3l1 1c-1 2-3 5-3 7h0v1l-3 1h0l-2 2c-1 1-2-1-3-1-1-1-2-1-3-1v-2h0-3c-1 0-1 0-2-1 1-1 1-1 0-1l-1-1h-1s-1 0-1-1-1-3-1-5l-1-2c1-1 1-2 2-4l1-1c-1-1-1-1-3-1 1-1 1-2 2-2l1-2c0-1 1-1 1-2h0v-1h0-1l1-3z" class="a"></path><path d="M501 269c0 3 1 5 2 7h-1v1h-1c0-2 0-3-1-4 0-2 0-3 1-4z" class="R"></path><path d="M500 262l2 1v1c-1 1-2 2-1 3v2c-1 1-1 2-1 4l-1-4v-6l1-1z" class="S"></path><path d="M499 269l1 4c1 1 1 2 1 4v1h0l-3-3h-1 0l1-1h0v-2l1-1v-2z" class="C"></path><g class="S"><path d="M502 259l2 2c1 0 2 1 2 2l3 2h-2c-2-1-4-2-5-3v-3z"></path><path d="M509 265s1 1 3 2h0l1 1c-1 1-1 1-2 3h0v-1c0-1 0-1-1-2h-1-1c-1 1-1 0-3 0v1l1 3h-2v-1c0-2-1-4-1-6 1 0 2 0 3 1l1-1h2z"></path></g><path d="M509 265s1 1 3 2h0l1 1c-1 1-1 1-2 3h0v-1c0-1 0-1-1-2h-1c-1-1-1-2-3-2l1-1h2z" class="O"></path><defs><linearGradient id="C" x1="499.362" y1="263.7" x2="494.717" y2="265.801" xlink:href="#B"><stop offset="0" stop-color="#2a2829"></stop><stop offset="1" stop-color="#434038"></stop></linearGradient></defs><path fill="url(#C)" d="M497 261l3 1-1 1v6 2l-1 1v2h0c-1-2-3-4-3-7s1-4 2-6z"></path><path d="M499 263v6 2l-1 1c-1-1-1-1-1-2v-1-3c0-2 1-2 2-3z" class="G"></path><path d="M492 252h3 3l-1 1h2c0 1 1 1 1 2 1 1 1 2 1 3l1 1v3c-2-1-3-1-4-2l-1 1c-1 2-2 3-2 6s2 5 3 7l-1 1h0 0c-2-1-3-2-3-3-1-2-2-6-1-8v-1h-1c0 1 0 1-1 2l-1-1 1-1c-1-1-1-1-3-1 1-1 1-2 2-2l1-2c0-1 1-1 1-2h0v-1h0-1l1-3z" class="J"></path><path d="M497 275l-2-3c-1-2-2-5-1-7v-1c0-2 1-2 2-4h2l-1 1c-1 2-2 3-2 6s2 5 3 7l-1 1z" class="G"></path><path d="M492 252h3c2 1 3 2 4 3s1 2 2 3l-1 1-1-1c-1 1-1 1-2 1s-1-1-1-1l-1-1-1 2c-1 1-2 3-3 4-1-1-1-1-3-1 1-1 1-2 2-2l1-2c0-1 1-1 1-2h0v-1h0-1l1-3z" class="AE"></path><path d="M490 260h1 0c1-1 2-1 3-1-1 1-2 3-3 4-1-1-1-1-3-1 1-1 1-2 2-2z" class="H"></path><path d="M495 257c-1 0-2-3-2-4 2 2 4 3 6 5-1 1-1 1-2 1s-1-1-1-1l-1-1z" class="y"></path><path d="M491 265c1-1 1-1 1-2h1v1c-1 2 0 6 1 8 0 1 1 2 3 3h0 1l3 3h0v-1h1v-1h1 1l1-1-1-3h2l-1-3v-1c2 0 2 1 3 0h1 1c1 1 1 1 1 2v1h0l1 1c-1 2-3 5-3 7h0v1l-3 1h0l-2 2c-1 1-2-1-3-1-1-1-2-1-3-1v-2h0-3c-1 0-1 0-2-1 1-1 1-1 0-1l-1-1h-1s-1 0-1-1-1-3-1-5l-1-2c1-1 1-2 2-4l1 1z" class="k"></path><path d="M502 277v-1h1 1c0 1 0 2-1 3-1-1-1-1-1-2h0z" class="l"></path><path d="M506 272l1 3v1c1 0 1 0 2 1l-3 3c0-2-1-3-1-5l-1-3h2z" class="B"></path><path d="M499 279h1l2 2c1 0 1 0 2-1 1 0 2 1 2 1h0l-2 2c-1 1-2-1-3-1-1-1-2-1-3-1v-2h1z" class="K"></path><path d="M491 276v-1h3c2 1 4 2 5 4h-1 0-3c-1 0-1 0-2-1 1-1 1-1 0-1l-1-1h-1z" class="l"></path><path d="M488 268c1-1 1-2 2-4l1 1c-1 2 0 4 0 5 1 2 2 3 3 5h-3v1s-1 0-1-1-1-3-1-5l-1-2z" class="R"></path><path d="M506 272l-1-3v-1c2 0 2 1 3 0h1 1c1 1 1 1 1 2v1c0 2-2 4-2 6-1-1-1-1-2-1v-1l-1-3z" class="C"></path><path d="M507 275c1-1 1-3 1-4 1 0 2-1 3-1v1c0 2-2 4-2 6-1-1-1-1-2-1v-1z" class="J"></path><path d="M248 622c1 1 2 1 3 1 2 0 4 0 6-1h0c2 0 2 1 4 0h3c2 0 2-1 3 0 2 1 4 0 5 0h4l1 4v4 1c1 0 1 0 2-1v7c-1 0-1 0-2 1h0v2 1 1c-1 1-3 0-4 0-3-1-6-2-10-3h0v-1-1h-1c-2 0-6 0-8-1 0-1 0-1-1-2h3c1-1 4-1 5-1h2v-1h-3l-1-3c-5 2-10 1-15 1h-5v-1-1-4-1h0c1-1 3 0 4 0 2 0 3-1 5-1z" class="j"></path><path d="M270 636l1 1v1l-1 1h-2c0-1 0-1-1-2 1 0 2 0 3-1z" class="H"></path><path d="M262 637h0c3-1 5-1 8-1-1 1-2 1-3 1 1 1 1 1 1 2l-5-1v-1h-1z" class="b"></path><path d="M240 627h3c2 1 4 1 5 2-2 0-6-1-9 0v-1l1-1z" class="C"></path><path d="M253 624c1 0 2 0 3-1 1 0 2 0 3 1v2h-6l-1-1h0l1-1z" class="I"></path><path d="M267 623c2 0 6-1 8 0v1 1h-6l-2-2z" class="C"></path><path d="M259 624h0c3-1 5-1 8-1l2 2c-3 1-6 0-8 1h-2v-2z" class="M"></path><path d="M262 627c3-1 5 0 7-1 1 0 1 0 2 1 1-1 2-1 3-1 0 1 0 2-1 2h0c-1 0-3 0-4 1v-1c-2 0-5 1-7 1 0-1 0-1-1-1l1-1z" class="G"></path><path d="M239 623c1 1 5 1 6 1h8l-1 1h0l1 1h-13c-1 1 0 1 0 1l-1 1v-4-1z" class="H"></path><path d="M243 627h5c2-1 3 0 5 0 3 0 6-1 9 0l-1 1c1 0 1 0 1 1h-2c-1 0-3 0-4-1-2 1-4 1-6 1h-2c-1-1-3-1-5-2z" class="d"></path><path d="M264 634c2-1 2-2 4-1h0 4v2h-1c-1 0-2 0-3-1l-1 1c-1 1-5 0-7 1-2-1-3-2-5-1l-1 1c0-1 0-1-1-2h3c1-1 4-1 5-1s2 0 3 1z" class="I"></path><path d="M259 629c1 0 1 0 2 1 1 0 4-1 6-1h6v3c-2 1-4 0-6 0h-1l-2 2c-1-1-2-1-3-1h2v-1h-3l-1-3z" class="C"></path><path d="M277 626v4 1c1 0 1 0 2-1v7c-1 0-1 0-2 1h0v2 1c-1-1-2-1-2-2-2-3 1-10 2-13z" class="g"></path><path d="M277 640c0-2-1-8 0-10v1c1 0 1 0 2-1v7c-1 0-1 0-2 1h0v2z" class="I"></path><path d="M320 630h3 0 4s1 0 1-1h1v1h4v2h1l-1 1h1v5h0l1 2h0c1 0 2-1 2-2l1 1c1 0 1 1 2 1h0 4s0-1-1-2c1 0 1-1 2-1s1 0 2-1h1v-1l5 4c2 1 4 2 5 5 0 0 0 1-1 1h0c-3 2-6 1-9 1h-3-1c-1 0-3-1-4 0h-9-3-3-2c-1 0-3-1-4 0h-9-4v-3-2l3-2h0v-9c2-1 5 0 7 0h3 1z" class="M"></path><path d="M348 635l5 4c-1 1-2 1-4 1 0-1 0-1-1-1v1h-1-1-2s0-1-1-2c1 0 1-1 2-1s1 0 2-1h1v-1z" class="h"></path><path d="M309 639c1-1 0-1 1-2l24 1 1 2c-7 1-14 0-20 0h-5c-1 0-1 0-1-1h0z" class="B"></path><path d="M320 630h3 0 4s1 0 1-1h1v1h4v2h1l-1 1h1v5h0l-24-1c-1 1 0 1-1 2v-9c2-1 5 0 7 0h3 1z" class="r"></path><path d="M323 633c-2-1-4-1-6-1v-1h5l3 1h-2v1z" class="z"></path><path d="M323 635c1 1 2 1 3 1h6 1 0l-2 1h-1-7l-1-1h0l1-1z" class="p"></path><path d="M325 632h4 4 1l-1 1h0c-2-1-7 0-10 0v-1h2z" class="q"></path><path d="M333 633h1v5c-1-1-1-1-1-2h-1-6c-1 0-2 0-3-1h0-1c3-2 9 1 11-2h0z" class="g"></path><path d="M320 630h3 0 4s1 0 1-1h1v1h4v2h-4-4l-3-1c-1 0-2 0-3-1h0 1z" class="P"></path><path d="M539 254c4 1 9 0 13 0 2-1 4 0 6-1 1 0 2 1 3 1 1 1 2 2 2 3 1 1 1 2 2 4 0 1 1 2 2 3v2c-2 0-5 1-6 3 0-1-1-1-1-2h-1l-1 1c1 1 1 2 2 3 0 1-1 1-1 2v-1l-1 2-1-1-1 2 2 2c1 0 2 1 4 2 0 1 1 2 1 3 0 2 0 4-1 6h-1v-3l-3 1v-1c-1 0-2-1-3-2-1 0-4 0-5-1l-2-2-1-2c0-1 0-1-1-2 0-1 0-1-1-2 1 0 1-1 2-1-1-1-1-1-2-1h-1l-1-1v-1h-1-1 0v-1h-1v-4c-2-2-5-2-7-2v-1-1c1 0 3 1 4 1 0-1 1-2 1-3h1v-1-1-3z" class="u"></path><path d="M539 257c4-1 7-2 11-2l-1 1h0c-1 0-3 1-4 2h0l1 1c0 1-1 1-1 2v1l-1 1h-1-1-3l-2-1c0-1 1-2 1-3h1v-1-1z" class="E"></path><path d="M537 262c0-1 1-2 1-3 0 1 0 1 1 1s1 1 2 1l-2 2-2-1z" class="Z"></path><path d="M541 261s2-2 3-2c0-1 1-1 1-1l1 1c0 1-1 1-1 2v1l-1 1h-1-1-3l2-2z" class="U"></path><path d="M543 263v-2h1l1 1-1 1h-1z" class="Z"></path><path d="M561 254c1 1 2 2 2 3 1 1 1 2 2 4 0 1 1 2 2 3v2c-2 0-5 1-6 3 0-1-1-1-1-2s0-2 1-3c0-1 0-2-1-3-2-3-5-4-8-5h0c1-1 3-1 4-1 2 0 3 1 4 2h0c1-1 1-2 1-3z" class="M"></path><path d="M561 254c1 1 2 2 2 3 1 1 1 2 2 4 0 1 1 2 2 3-1 1-4 1-5 2 0 1 0 1-1 1v-1l2-3-3-6h0c1-1 1-2 1-3z" class="l"></path><path d="M545 262c2-1 2-3 5-3 3-1 3-1 6 1 1 0 3 1 3 3 1 1 0 2 0 4l-1 1c1 1 1 2 2 3 0 1-1 1-1 2v-1l-1 2-1-1-1 2c-1-2-2-3-3-4 0-1-1-3-1-5l-1-2c-2 0-4 1-6 1h-1v-1c-1 0-1 0-2-1h1 1l1-1z" class="L"></path><path d="M559 272c-1 0-1 0-2-1v-1h0v-2h1c1 1 1 2 2 3 0 1-1 1-1 2v-1z" class="M"></path><path d="M554 262h2v1h0l-1 1v2h0 0c-1-1-1-2-1-2h-1c0 2 0 3 1 5 1 1 2 3 3 4l-1 2c-1-2-2-3-3-4 0-1-1-3-1-5l-1-2c-2 0-4 1-6 1h-1v-1c1-1 3-1 4-1 2 0 4-1 6-1z" class="B"></path><path d="M545 262c2-1 2-3 5-3 3-1 3-1 6 1-1 0-2 1-3 1h-1c1 0 2 0 2 1-2 0-4 1-6 1-1 0-3 0-4 1-1 0-1 0-2-1h1 1l1-1z" class="M"></path><path d="M552 261c1 0 2 0 2 1-2 0-4 1-6 1h0c0-1 0-1 1-2h3 0z" class="H"></path><path d="M533 261c1 0 3 1 4 1l2 1h3c1 1 1 1 2 1v1h1c2 0 4-1 6-1l1 2c0 2 1 4 1 5 1 1 2 2 3 4l2 2c1 0 2 1 4 2 0 1 1 2 1 3 0 2 0 4-1 6h-1v-3l-3 1v-1c-1 0-2-1-3-2-1 0-4 0-5-1l-2-2-1-2c0-1 0-1-1-2 0-1 0-1-1-2 1 0 1-1 2-1-1-1-1-1-2-1h-1l-1-1v-1h-1-1 0v-1h-1v-4c-2-2-5-2-7-2v-1-1z" class="P"></path><path d="M552 266c0 2 1 4 1 5l-1 2c-1-2-1-4-2-6 1 0 1 0 2-1z" class="e"></path><path d="M553 271c1 1 2 2 3 4l2 2h0-3l1 1c-2-1-3-3-4-5l1-2z" class="K"></path><path d="M558 277c1 0 2 1 4 2 0 1 1 2 1 3-3-1-5-2-7-4l-1-1h3 0z" class="d"></path><path d="M541 269c2-1 5-2 7-1 1 1 2 2 2 3l-1 1c-2-1-3-2-6-1v-1h-1-1 0v-1zm4-4c2 0 4-1 6-1l1 2c-1 1-1 1-2 1-3-1-6 0-9 1v-1c1-1 3-1 4-2z" class="I"></path><path d="M550 271c2 6 5 8 10 11l2 1v1l-1 1-3 1v-1c0-1 0-1 1-2h0v-1h-3 0c-2-1-4-2-5-4v-2h0c-1-1-1-1-1-2h0l-1-2 1-1z" class="a"></path><path d="M559 283c1 0 2 0 2 1h1l-1 1-3 1v-1c0-1 0-1 1-2z" class="U"></path><path d="M543 271c3-1 4 0 6 1l1 2h0c0 1 0 1 1 2h0v2c1 2 3 3 5 4h0 3v1h0c-1 1-1 1-1 2-1 0-2-1-3-2-1 0-4 0-5-1l-2-2-1-2c0-1 0-1-1-2 0-1 0-1-1-2 1 0 1-1 2-1-1-1-1-1-2-1h-1l-1-1z" class="c"></path><path d="M547 273h0 2l1 1c-1 1-1 2-1 2 0 1-1 2-2 2 0-1 0-1-1-2 0-1 0-1-1-2 1 0 1-1 2-1z" class="K"></path><path d="M96 247c1 3 0 5 0 8l1 1 2 2v1c-2 3-5 5-7 8s-4 6-7 8v-1c-1 1-1 1-3 2h-1-5 0-9-1l-1-2c2-2 2-5 2-7 0-1 0-1 1-1 1-2 1-4 1-6 1-4 2-7 3-11h0l18 5h4 1c1-1 1-2 0-3v-1c0-1 1-1 1-2v-1z" class="I"></path><path d="M67 267h0l1 6c4 1 8 1 12 1h5c-1 1-1 1-3 2h-1-5 0-9-1l-1-2c2-2 2-5 2-7z" class="B"></path><path d="M85 274c-1 1-1 1-3 2h-1-5l-1-1 1-1h9zm11-27c1 3 0 5 0 8l1 1 2 2-8 9-1-1c-1-1 0-4-1-6l-3 9c0-2-1-5-1-7 0 0 0-2-1-2v-1c-1 3-1 9-3 10h-8c-1-3-1-9 0-13h1c1 1 0 2 0 3 1-1 0-3 1-4v-2h5c3 1 4 2 6 2h2c-1 1-1 2 0 3h0l1-1h1v2c0 1 0 2 1 4v-1c1-1 0-1 1-1l1-1v-2-1-2l1-1h1c1-1 1-2 0-3v-1c0-1 1-1 1-2v-1z" class="D"></path><path d="M96 247c1 3 0 5 0 8l1 1c0 1 0 2-1 3 0 1-1 2-2 2-1 1-2 3-3 4h0v-2-1c1-1 0-1 1-1l1-1v-2-1-2l1-1h1c1-1 1-2 0-3v-1c0-1 1-1 1-2v-1z" class="L"></path><path d="M74 259c1-1 0-3 1-4v-2h5c3 1 4 2 6 2h2c-1 1-1 2 0 3h0l-2 7c0-2 0-7-1-8l-1-1c-2 2-3 6-3 9v3h-1c-1 0-1 0-1-1-1-2 1-9-1-11h0c-1 4 0 9-2 11l-1 1v-1l-1-8z" class="E"></path><path d="M99 207c1-1 1-2 2-3s3-3 5-4l1 1v4l1 1 1 1-2 2h0v4l-1 1c0 1-1 1-2 1v1h1 2v2l-1 1c0 1 1 1 1 1 0 2 0 3-1 4h1c0 2 0 4 1 6h0c-1 0-1 1-1 1v1h1v2h-1v4 2h0c1 1 1 3 1 5-1 0-1 1-1 2h1v1h0c-2 2-3 4-5 6h0c-1 2-2 4-4 5v-1l-2-2-1-1c0-3 1-5 0-8 0-2-1-3-1-5v-2-1c0-1 1-2 0-3h1s0-2-1-3l-1-1-1-1c0 1-1 1-1 1h0v-1h-3c0-1 0-1-1-2v-2-1c-1 0-2-1-2-1v-1h1 1v-1l-1-1h3l2-1v-1h2s1 0 1-1c1 0 1 1 1 1h1l-1-2c1 0 2-1 3-1v-1-1h-1c0-2 0-3 1-4 0-2 1-3 1-4h-1z" class="Z"></path><path d="M104 206h1c0 1 0 2-1 3 0-1-1-1-1-1l1-2z" class="H"></path><path d="M96 236c2-1 3-3 5-5v3s-1 0-1 1c0 0 1 1 1 2 1 1 1 2 0 3v3c0 2-1 7 0 9l1 1c0 1 1 1 1 1-1 2-2 4-4 5v-1l-2-2-1-1c0-3 1-5 0-8 0-2-1-3-1-5v-2-1c0-1 1-2 0-3h1z" class="V"></path><path d="M101 237c1 1 1 2 0 3v3c-1 0-1 1-2 1 0 1-1 2-1 2h-1 0v-1s1 0 1-1c1 0 1-1 1-2h-1v-2l3-3z" class="E"></path><path d="M96 236c2-1 3-3 5-5v3s-1 0-1 1c0 0 1 1 1 2l-3 3v2l-1 1c0-1-1-1-2-1v-2-1c0-1 1-2 0-3h1z" class="C"></path><path d="M95 239h1c1-1 0-2 1-2 1 1 0 2 1 3v2l-1 1c0-1-1-1-2-1v-2-1z" class="e"></path><path d="M97 246h1s1-1 1-2c1 0 1-1 2-1 0 2-1 7 0 9l1 1c0 1 1 1 1 1-1 2-2 4-4 5v-1l-2-2-1-1 1-1c1-1 2-2 2-3v-4l-2-1z" class="U"></path><path d="M96 218c1 0 2-1 3-1v2 1h0v2h1c0 1-1 1-1 2h0l1 1c-1 1 0 1 0 1v3h0v2h1c-2 2-3 4-5 5 0 0 0-2-1-3l-1-1-1-1c0 1-1 1-1 1h0v-1h-3c0-1 0-1-1-2v-2-1c-1 0-2-1-2-1v-1h1 1v-1l-1-1h3l2-1v-1h2s1 0 1-1c1 0 1 1 1 1h1l-1-2z" class="N"></path><path d="M92 222l2-1c1 1 1 1 1 2s0 1-1 2l-2-1v-2z" class="Q"></path><path d="M92 224l2 1v1c0 1 1 2 1 3v1l1 1c-1 1-1 1-2 1l-1-1c0 1-1 1-1 1h0v-1c0-1 0-1-1-2 0-1-1-1-2-1l1-1s1 0 1-1h0 1v-2h0z" class="e"></path><path d="M92 221v1h0v2h0v2h-1 0c0 1-1 1-1 1l-1 1c1 0 2 0 2 1 1 1 1 1 1 2h-3c0-1 0-1-1-2v-2-1c-1 0-2-1-2-1v-1h1 1v-1l-1-1h3l2-1z" class="U"></path><path d="M92 221v1h0v2h0c-1 0-2-1-4-1l-1-1h3l2-1z" class="z"></path><path d="M75 379c1 1 2 3 2 4 2 1 1 0 3 0h1c1 0 1-2 2-1 0 1-1 1-2 2h0c-1 0-2 2-3 2 3 2 2 0 4 0l-3 2 1 1h0l1 2h-1c1 2 2 3 2 4v-1c1 1 2 3 2 4v1 12 5h-5-5-1c-3 0-8 0-10-1-1 0-1 0-2-1 0-1-1-2 0-3 0-1 0-2-1-2v-2c-1-2-1-4 0-5v-1c0-1 0-1-1-2v-2c1 0 4-4 5-5l11-13z" class="x"></path><path d="M60 402c1-1 1-2 2-3-1 0-1-1-2-2h1l2 1-1 2h2l1 1c-1 1-4 6-5 6-1-2-1-4 0-5z" class="J"></path><path d="M77 383c2 1 1 0 3 0h1c1 0 1-2 2-1 0 1-1 1-2 2h0c-1 0-2 2-3 2-2 3-4 5-6 7-3 3-5 5-7 8l-1-1h-2l1-2c1 0 2-2 3-3l6-7c2-1 3-3 5-5z" class="s"></path><path d="M75 379c1 1 2 3 2 4-2 2-3 4-5 5l-6 7c-1 1-2 3-3 3l-2-1h-1c1 1 1 2 2 2-1 1-1 2-2 3v-1c0-1 0-1-1-2v-2c1 0 4-4 5-5l11-13z" class="V"></path><path d="M61 411l17-19c1-1 1-1 2-1 1 2 2 3 2 4-2 3-5 5-8 8-3 4-7 9-11 12-1 0-1 0-2-1 0-1-1-2 0-3z" class="D"></path><path d="M82 395v-1c1 1 2 3 2 4v1 12 5h-5-5-1c-3 0-8 0-10-1 4-3 8-8 11-12 3-3 6-5 8-8z" class="Z"></path><path d="M229 606c1 0 3-1 5 0-1 1-1 1-1 2 1 1 1 1 2 1s1 0 2 1h2v1c1-2 4-1 6-2h5 12c3-1 8 0 12-1h3v1l-1 1c0 1 1 1 1 2v3 2 2l-1 2c-1 1-2 1-4 1-1 0-3 1-5 0-1-1-1 0-3 0h-3c-2 1-2 0-4 0h0c-2 1-4 1-6 1-1 0-2 0-3-1-2 0-3 1-5 1-1 0-3-1-4 0l-1-1h-1 0l2-1v-3h0c-1-1-3 0-4 0-2 0-16 2-18 1 1-3 0-7 0-11v-1c4 0 8-1 12-1z" class="AA"></path><path d="M246 616h-6l-1-1h1c1-1 2-1 4 0 1 0 2 0 2 1z" class="H"></path><path d="M239 618c3 0 7-1 10-1v2h-1 0-3c-2 0-4 1-6-1h0z" class="b"></path><path d="M261 617v2h-2-4c-1 0-4 1-5 0h0-1v-2h0 11 1z" class="h"></path><path d="M261 617v2h-2l-1-1v-1h2 0 1z" class="U"></path><path d="M239 618c2 2 4 1 6 1h3 0c-1 0-2 0-3 1h2l1 2c-2 0-3 1-5 1-1 0-3-1-4 0l-1-1h-1 0l2-1v-3z" class="L"></path><path d="M277 617v2l-1 2c-1 1-2 1-4 1-1 0-3 1-5 0l2-2v-1h-2c-1-1-1 0-1 0h-5v-2c2-1 5 0 7-1h4c1 1 4 1 5 1z" class="M"></path><path d="M261 619h5s0-1 1 0h2v1l-2 2c-1-1-1 0-3 0h-3c-2 1-2 0-4 0h0c-2 1-4 1-6 1-1 0-2 0-3-1l-1-2h-2c1-1 2-1 3-1h1 1 0c1 1 4 0 5 0h4 2z" class="I"></path><path d="M217 608h7c1-1 5-1 7 0 1 1 1 4 1 6v2 1c1 1 2 1 3 1-2 0-16 2-18 1 1-3 0-7 0-11z" class="k"></path><path d="M274 608h3v1l-1 1c0 1 1 1 1 2v3h-6-2-1-3 0-1-3c-3 1-7 1-10 1-2 0-3-1-5 0 0-1-1-1-2-1v-1c0-1 0-1-1-2v1c-2 0-3 0-4-1l1-1h-1c1-2 4-1 6-2h5 12c3-1 8 0 12-1z" class="h"></path><path d="M274 608h3v1l-1 1h-4-8-2c-3 0-17 2-18 0l-1 1h-3-1c1-2 4-1 6-2h5 12c3-1 8 0 12-1z" class="AF"></path><path d="M272 610h4c0 1 1 1 1 2v3h-6-2-1-3 0-1-3c-3 1-7 1-10 1-2 0-3-1-5 0 0-1-1-1-2-1v-1l1-1h10 4c2-1 3-1 5-1h0l1 1s0-1 1-1h6l-2-1c1 0 1 0 2-1z" class="I"></path><path d="M272 610h4c0 1 1 1 1 2h-3-2l-2-1c1 0 1 0 2-1z" class="E"></path><path d="M541 274c1-1 2-1 4 0 1 1 1 1 1 2 1 1 1 1 1 2l1 2 2 2c1 1 4 1 5 1 1 1 2 2 3 2v1l3-1v3l-1 1h1 1v1c1-1 1-1 2-1h1l1 1h3 1l2 1c0 2 0 3-1 4l1 1v2 1h-3-1v1h3 1c-1 1-2 1-3 1-1 1-3 1-4 1v1h6s0 1 1 2h0v3h-1c-1 0-2 1-4 1h-1c-1 0-2 0-3 1h-2 0-2-2 0-6v1c-3 1-8 1-11 1v-1c-1 0-1 0-2-1 1 0 1 0 2-1-1-1-1-2-1-4h1c0-2 0-2-1-4v-3c0-1 0-3-1-4v-7c0-1-1-1 0-2 0-2 0-2 2-4v-1l-1-1h0l1-1c0-1 0-2-1-3 1-1 1-1 2-1z" class="I"></path><path d="M551 297l2-1c1 1 2 1 2 1-1 0-2 0-3 1h-1v-1z" class="h"></path><path d="M553 296h7v1h-5s-1 0-2-1z" class="C"></path><path d="M568 300l-1 1-1-1v-1-1h-2l1-1h3v2h1-1v1z" class="Z"></path><path d="M544 305c1-1 2 0 3 0h4 4-1c-2 1-4 0-6 1h0-3 0-1v-1z" class="Q"></path><path d="M560 296c1-1 1-1 3 0h0l1 1c-1 1 0 2-1 3h-1-6-1l1-1h1 3v-2h0v-1z" class="L"></path><path d="M560 297c1 1 1 0 2 1l-1 1h-1v-2z" class="Q"></path><path d="M540 281c-1 2 0 3 0 4v1c0 1 0 1 1 2h0 3c-1 1-2 2-3 2h0l1-1c-1-1-2-1-3-1v-1h-1c0 1 1 1 1 2s0 3-1 4h1v3 2c0-1 0-3-1-4v-7c0-1-1-1 0-2 0-2 0-2 2-4z" class="E"></path><path d="M558 294h5v1 1c-2-1-2-1-3 0h-7l-2 1-1-1v-1h4c1-1 1-1 2-1h2zm7 9h6s0 1 1 2h0c-2 0-4 0-5 1h-3-1l-1-1h-4l-1-1h3c1-1 3-1 5-1z" class="b"></path><path d="M564 306h3c1-1 3-1 5-1v3h-1c-1 0-2 1-4 1h-1c-1 0-2 0-3 1h-2 0-2-2 0c0-1 0-1 1-1l-1-1-6 1h0l-1-1c2 0 3 0 4-1h1 4l5-1z" class="e"></path><path d="M558 286l3-1v3l-1 1h1 1v1c1-1 1-1 2-1h1l1 1h3 1l2 1c0 2 0 3-1 4l1 1h-2-2-5 0v-1-1h-5-2c-1 0-1 0-2 1h-4-4c-1 0-1 0-2 1h-1v-1c0-1 1-1 1-1 1-1 2-1 2-2h1 1v-1h-2l-1 1h-2c2 0 1-1 2-1 2-1 3 0 4-1s2-1 2-2h2l1 2h1 3l1-1c0-1-1-2-1-3z" class="Q"></path><path d="M561 289h1v1c1-1 1-1 2-1h1l1 1h3c-1 0-2 0-3 1h-2c-1 0-3 0-4-1l1-1z" class="b"></path><path d="M558 294c1-1 2 0 3-1v-1h4 3v-1c1 1 2 1 2 2h-3-1v1 1h-3v-1h-5z" class="E"></path><path d="M572 291c0 2 0 3-1 4l1 1h-2-2-5 0v-1h3v-1-1h1 3l1-1 1-1z" class="V"></path><path d="M571 292v2c-1 0-1-1-2 0s-2 1-3 1v-1-1h1 3l1-1z" class="I"></path><path d="M541 274c1-1 2-1 4 0 1 1 1 1 1 2 1 1 1 1 1 2l1 2 2 2c1 1 4 1 5 1 1 1 2 2 3 2v1c0 1 1 2 1 3l-1 1h-3-1l-1-2h-2c0 1-1 1-2 2s-2 0-4 1c-1 0 0 1-2 1 0-1-1-1-2-1v-1c1 0 2-1 3-2h-3 0c-1-1-1-1-1-2v-1c0-1-1-2 0-4v-1l-1-1h0l1-1c0-1 0-2-1-3 1-1 1-1 2-1z" class="z"></path><path d="M541 275h3l2 3h-2l-3-3z" class="C"></path><path d="M541 291l5-1 1-1c1-1 3-1 4-1 0 1-1 1-2 2s-2 0-4 1c-1 0 0 1-2 1 0-1-1-1-2-1z" class="h"></path><path d="M553 288h0c0-2-2-2-3-2v-1c1-1 2-1 3-1 2 1 3 1 4 2 0 1 1 2 2 3l-1 1h-3-1l-1-2z" class="e"></path><path d="M557 286c0 1 1 2 2 3l-1 1h-3v-2-1l2-1z" class="M"></path><path d="M539 275h2l3 3h2 0c0 1 1 2 1 3 1 1 1 2 2 3v2l-2 1h0l-3 1h-3 0c-1-1-1-1-1-2v-1c0-1-1-2 0-4v-1l-1-1h0l1-1c0-1 0-2-1-3z" class="c"></path><path d="M546 278h0c0 1 1 2 1 3 1 1 1 2 2 3v2l-2 1h0l-1-1 1-1-2-2h0c1 0 1 1 2 1h0v-1l-1-2c-1 0-1-1-2-2v-1h2z" class="K"></path><path d="M540 281v-1l-1-1h0c1 1 2 0 2 1 1 1 2 3 3 5l2 1 1 1-3 1h-3 0c-1-1-1-1-1-2v-1c0-1-1-2 0-4z" class="M"></path><path d="M544 285l2 1 1 1-3 1h-3 0c-1-1-1-1-1-2 1 0 1 0 2 1h0 2v-2h0z" class="h"></path><path d="M84 411v1c1 0 0 3 1 3h3c0 1 1 1 2 1h7v1c1 1 1 2 1 3 1 3 0 6 1 9v6h0 1c1 1 0 3 1 4v6 6h-5v1h-4v-1l-1 1h-9-1v-1l-1-1v-1c-1 1-2 1-3 2v1h0l-3-3s-1 0-1 1h0l-2-1v-1c0-1 1-4 2-5s3-3 3-5v-1h1c-2 0-2 0-3-1 0 1 0 1-1 1l-1-1-1-2c0-1 0-3-1-3v-1-2l4-8v-4h5 5v-5z" class="y"></path><path d="M83 422l2 1c0 1-1 1-2 2v-2-1z" class="AB"></path><path d="M88 432h1c1 0 2 1 2 2h-1l-2-2z" class="P"></path><path d="M83 423l-1-1v-1c0-1 0-1 1-1l1 1-1 1v1z" class="f"></path><path d="M86 422h1c1 0 1 0 2 1l-1 1c-1 0-2-1-3-1l1-1z" class="S"></path><path d="M77 420c1-1 0-3 2-4 0 2 0 4-1 6l-1-2z" class="AF"></path><path d="M83 420l1-1s1 1 2 1 1-1 2-2c1 1 1 2 1 2 0 1-2 1-3 1h-2l-1-1z" class="S"></path><path d="M84 428h0c2 0 3 0 4-1v1c-1 0-2 1-2 3v1c1 1 1 2 0 3l-1-1c0 1-1 1-1 2h-1c0-2 1-3 0-4v-1h-1c0-1 1-2 2-3h0z" class="P"></path><path d="M83 431l1-2h1v1 4h0c0 1-1 1-1 2h-1c0-2 1-3 0-4v-1z" class="O"></path><path d="M96 417h1c1 1 1 2 1 3 1 3 0 6 1 9v6h0-2v-6c0-4-1-8-1-12z" class="t"></path><path d="M81 424l1 1c0 2 1 2 2 3h0c-1 1-2 2-2 3h1v1c1 1 0 2 0 4h1c0-1 1-1 1-2l1 1h1 4v2-1l-6 1h-1-2 0-4v-1-1l1 1h1c0-3 0-5 1-7h1v1-1-1l-1-1v-3z" class="r"></path><path d="M81 424l1 1c0 2 1 2 2 3h0c-1 1-2 2-2 3s0 4-1 5h0c0-2 0-4 1-6v-1-1l-1-1v-3z" class="S"></path><path d="M90 416h7v1h-1 0c-1 0-1 0-1 1-1 0-1 0-2 1 0 1 1 4 1 5l-1 1v2 3 1c-1 0-1 0-2-1l1-1h0l-1 1c-1-2 0-2 0-4l-1-1c0-1-1-3 0-5h2c0-1 0-3-1-4h-1z" class="P"></path><path d="M91 435l5 1c0 4-1 9 0 14v1 1h-4v-1l-1-14v-2z" class="Z"></path><path d="M99 435h1c1 1 0 3 1 4v6 6h-5v-1c1-4 0-7 0-10 0-2 0-3 1-4l2-1z" class="D"></path><path d="M74 416h5 0c-2 1-1 3-2 4l1 2v1h1l1-3h0l1 1-1 3h1v3l1 1v1 1-1h-1c-1 2-1 4-1 7h-1l-1-1v1 1 1c-1 2-3 4-4 6l-1-1c1-1 3-3 3-5v-1h1c-2 0-2 0-3-1 0 1 0 1-1 1l-1-1-1-2c0-1 0-3-1-3v-1-2l4-8v-4z" class="p"></path><path d="M77 420l1 2v1c-2 1-4 4-4 6v1h0c1-1 1-4 3-5 0 2-1 4-2 6v2 1c-1-1-1-1-1-2s0-1-1-2c0-1 0-2 1-3 1-2 2-4 3-7z" class="z"></path><path d="M74 416h5 0c-2 1-1 3-2 4-1 3-2 5-3 7-1 1-1 2-1 3 1 1 1 1 1 2l-1 1c0-1 0-2-1-3v-1l-1-1v1c0 2 1 3 1 4l-1 1c0-1 0-3-1-3v-1-2l4-8v-4z" class="O"></path><path d="M85 437l6-1v1l1 14-1 1h-9-1v-1l-1-1v-1c-1 1-2 1-3 2v1h0l-3-3s-1 0-1 1h0l-2-1v-1c0-1 1-4 2-5l1 1c1-2 3-4 4-6v-1h4 0 2 1z" class="B"></path><path d="M84 437h1v1l1 2h-1c-1-1-1-2-1-3z" class="S"></path><path d="M82 437h0c0 1 1 2 1 3v1c0 2 0 6-1 8v3h-1v-1-3c1-2 0-4 0-6 1-1 1-2 1-3-1-1-1-1 0-2z" class="AB"></path><path d="M85 438h2c1 1 1 3 1 4s0 2-1 3l1 1h0-1l-2 2-1-1v-1-1h0v-2h1v-1s-1-1 0-2h1l-1-2z" class="J"></path><path d="M78 437h4c-1 1-1 1 0 2 0 1 0 2-1 3 0 2 1 4 0 6v3l-1-1v-1c-1 1-2 1-3 2l1-1v-10-2-1z" class="s"></path><path d="M78 440h1l1 1h0v3 1l-1 1c0 1 1 2 1 3-1 1-2 1-3 2l1-1v-10z" class="P"></path><path d="M79 440l1 1h0v3h-1c-1-1-1-2 0-4z" class="R"></path><path d="M78 438v2 10l-1 1v1h0l-3-3s-1 0-1 1h0l-2-1v-1c0-1 1-4 2-5l1 1c1-2 3-4 4-6z" class="o"></path><path d="M192 567l1 1h0c0 1 1 2 1 3l1-1h1c0 1-1 2 0 3v-1h1c2 0 3 0 4 1 0 1 1 1 2 1l-1 1 1 1h1 0l3 1 2-1c1 1 2 1 3 1h1v1l1-1s1 0 1-1h1l1 1-1 1v6 3c0 1 0 2 1 3v3 14 1c0 4 1 8 0 11l-1-1h-1c-2-1-3-2-4-2l-2-2-5-2-1 1-1-1c0-2-1-2-2-3h-1c-1-1-2-2-2-3v-1h-1l-1 1-1-1c1 0 1-1 2-2h-1 0c-1 0-2 1-3 2h0 0c-1-1-1-2-2-2 0-1 0-2 1-3-1 0-1 0-1-1l-3 3v-1l-1-1h-1-1v-1l1-1 3-3 1-1 1-2v-1h-1c-3 2-5 4-7 7l-1-1c1-1 1-1 1-2l-1-1c-1 1-1 2-2 2v-1l10-11 2-3c0-1-1-1 0-2 0-1 0-1-1-2l-1-1h-1 1l1-1 1-2c0-1 0-2 1-3v-3z" class="Y"></path><path d="M207 577l2-1c1 1 2 1 3 1h1v1l1-1v3h-2c0 1 0 0 1 1v1c0 1 0 1-1 2h0-1c0-1-1-1-2-1v-2c-2 0-5 5-6 6l-10 12c-1 1-2 3-3 4 0-1 0-2 1-3h0c3-3 18-19 18-22 0-1-1-1-2-1z" class="P"></path><path d="M211 584v-3h2v1c0 1 0 1-1 2h0-1z" class="f"></path><path d="M192 567l1 1h0c0 1 1 2 1 3l1-1h1c0 1-1 2 0 3v-1h1c2 0 3 0 4 1 0 1 1 1 2 1l-1 1 1 1h1 0 0c-3 1-6 1-8 0-2 3-15 18-15 18-1 1-1 2-2 2v-1l10-11 2-3c0-1-1-1 0-2 0-1 0-1-1-2l-1-1h-1 1l1-1 1-2c0-1 0-2 1-3v-3z" class="F"></path><path d="M189 576h6l-4 5c0-1-1-1 0-2 0-1 0-1-1-2l-1-1z" class="Z"></path><path d="M197 572c2 0 3 0 4 1 0 1 1 1 2 1l-1 1c-1 0-4 0-5-1l-1-2h1z" class="m"></path><path d="M192 567l1 1h0c0 1 1 2 1 3l1-1h1c0 1-1 2 0 3v-1l1 2c-2 1-3 0-4 1h-3l1-2c0-1 0-2 1-3v-3z" class="r"></path><path d="M203 587c1-1 4-6 6-6v2 4c1 2 1 4 0 7l1 2h0c-2 4-5 7-8 10l-3 3c-1-1-2-2-2-3v-1h-1l-1 1-1-1c1 0 1-1 2-2h-1 0c-1 0-2 1-3 2h0 0c-1-1-1-2-2-2 1-1 2-3 3-4l10-12z" class="Y"></path><path d="M203 587c1-1 4-6 6-6v2 4c1 2 1 4 0 7v-4h0l-2 1c0 1 1 0 0 1s-2 2-3 2h0l1-2 3-2c0-1 0-1 1-1v-1l-1-1h-2v-2c-1 1-1 2-3 2h0z" class="H"></path><path d="M215 576h1l1 1-1 1v6 3c0 1 0 2 1 3v3 14 1c0 4 1 8 0 11l-1-1h-1c-2-1-3-2-4-2l-2-2-5-2-1 1-1-1c0-2-1-2-2-3h-1l3-3c3-3 6-6 8-10h0l-1-2c1-3 1-5 0-7v-4c1 0 2 0 2 1h1 0c1-1 1-1 1-2v-1c-1-1-1 0-1-1h2v-3s1 0 1-1z" class="AB"></path><path d="M210 596v-3h1l2 1c-1 2 0 4-1 5h-2v-3h0z" class="P"></path><path d="M215 576h1l1 1-1 1v6 4h-1v-4c0-2 0-3-1-4v-3s1 0 1-1z" class="n"></path><path d="M209 583c1 0 2 0 2 1h1 0v1c0 2 4 2 2 6 1 1 1 1 1 2-1 1-2 1-2 1l-2-1h-1v3l-1-2c1-3 1-5 0-7v-4z" class="F"></path><path d="M209 587l3 2c0 1 0 2 1 3l-2 1h-1v3l-1-2c1-3 1-5 0-7z" class="q"></path><path d="M210 596v3h2v5c1 3 1 6 1 8-1 1-1 1-1 2h2c1 1 1 2 1 3l1 1h-1c-2-1-3-2-4-2l-2-2-5-2-1 1-1-1c0-2-1-2-2-3h-1l3-3c3-3 6-6 8-10z" class="Y"></path><path d="M210 596v3h2v5c1 3 1 6 1 8-1 1-1 1-1 2h2c1 1 1 2 1 3l1 1h-1c-2-1-3-2-4-2l-2-2c1-1 2-4 1-6-1-1 0-3 0-4v-6l-10 11h-1l3-3c3-3 6-6 8-10z" class="F"></path><path d="M306 576h0l2 1c1 3 0 6 1 8v2 6h2v1c-1 1-1 1-1 2h-1l-20-3c4 3 7 5 11 7l9 6v1 2 1 3 2 3l1 1c1 1 1 1 2 1h-1v1h0c-1 1-1 1-1 2h0c-1 1 0 2 0 4v1h1 0 4l5 1v1h-1-3c-2 0-5-1-7 0v-6c-1-1-4 0-5-1h-7c-5-1-11-3-16-5-1 1-3 2-5 4h-4c2 0 3 0 4-1l1-2v-2-2-3c0-1-1-1-1-2l1-1v-1h-3c-4 1-9 0-12 1-1-1-1-1-1-2l1-1h0c0-1 0-1-1-2h3l1-1c1-1 3 0 4 0 1-1 2-1 3-1 0-1 1-1 2-2 1 0 2 1 3 0h0c1-1 1-1 1-2 1-1 1-1 1-2 1 0 3 1 4 0 0-1 0-2-1-3s-2 0-3 0h-1c0-1-1-1-1-2h-4 0c1-1 3 0 5-1v1h0 1c-1-1-1-2-2-2l1-1c0-1-1-2-1-3-1 0-2 0-2-1h2v-1h-3c1 0 2-1 3-1v-1h-1-3v-1h0c1 0 1 0 2-1h0 2 0v-1h0-1c1-1 1-1 2-1v-1h28z" class="Y"></path><path d="M273 580h4v1h0-1-3v-1z" class="x"></path><path d="M309 593h2v1c-1 1-1 1-1 2h-1v-3z" class="q"></path><path d="M309 618l1 1c1 1 1 1 2 1h-1v1h0c-1 1-1 1-1 2h0l-1-1v-4z" class="AF"></path><path d="M277 619c5-3 10-5 14-10 1-2 2-4 3-5 0-2 0-4 1-5l1 1c-1 5-4 10-8 14l-6 3c9 4 18 6 27 5l1 1c-1 1 0 2 0 4v1h1 0 4l5 1v1h-1-3c-2 0-5-1-7 0v-6c-1-1-4 0-5-1h-7c-5-1-11-3-16-5-1 1-3 2-5 4h-4c2 0 3 0 4-1l1-2z" class="q"></path><path d="M277 600h0c0 1 0 2 1 3h-1v1h-2l-1 1h3v1c-1 1-2 1-3 1v1c-4 1-9 0-12 1-1-1-1-1-1-2l1-1h0c0-1 0-1-1-2h3l1-1c1-1 3 0 4 0 1-1 2-1 3-1 0-1 1-1 2-2 1 0 2 1 3 0z" class="E"></path><path d="M277 600h0c0 1 0 2 1 3h-1l-1-1h-4c0-1 1-1 2-2 1 0 2 1 3 0z" class="I"></path><path d="M262 606h11c1-1 2-1 4-1v1c-1 1-2 1-3 1v1c-4 1-9 0-12 1-1-1-1-1-1-2l1-1h0z" class="d"></path><path d="M514 163h1v2c1 1 3-1 4 1h0c1 2 1 2 1 4h2c1 2 3 5 4 7l12 19c1 2 3 5 4 7v1c0 1 1 1 2 1v1c0 1 0 1-1 3v1c0 1-1 3-1 4h-1c0 2-1 3-2 4s-1 3-2 4h0 0l-1 3h-2c-1 0-1 0-2-1h-1v-1l-1 1c-1 0-1 0-2-1-1 0-1-1-3-1h0v-2l-1 1h-1l1-1-1-1c0 1-1 1-1 2h-1-1c-2-1-4-1-7-1-3-1-6-1-9-1-1-2 0-5 0-6v-14c1-4-1-9 1-13h0c0-1 0-1 1-2 0-1 0-2 1-2v-2l-2-9h1c1 1 2 1 4 1h2v-1h-2s1-1 1-2l1-1 1-3v-2h1z" class="U"></path><path d="M518 204h1c-1 1-2 1-2 2l-1-1h0 0l1-1h1z" class="X"></path><path d="M519 166c1 2 1 2 1 4-1 0-2-1-3-1v-1l1-1 1-1z" class="v"></path><path d="M528 204h1l-1 1c-2 2-4 5-6 7h-1c0-1 1-2 2-3l5-5z" class="Z"></path><path d="M529 205l1 1c0 1-3 3-4 5s-3 3-4 5h-1v-1c2-4 5-7 8-10z" class="V"></path><path d="M510 183l8 21h-1 0c-2-3-3-6-4-9s-3-8-3-12h0z" class="I"></path><path d="M514 163h1v2c1 1 3-1 4 1-1 0-2 1-2 1l-3 3-1 1h-1-2s1-1 1-2l1-1 1-3v-2h1z" class="Y"></path><path d="M514 163c1 2 0 3-1 5h-1l1-3v-2h1z" class="E"></path><path d="M512 168h1s0 1 1 2l-1 1h-1-2s1-1 1-2l1-1z" class="H"></path><path d="M512 172h2 0l1-1h0 2 0 2 0c-1 1-3 1-5 2-3 0-4 2-6 3v2c1 2 2 4 2 5h0c-1-1-1-2-2-3h-1l-2-9h1c1 1 2 1 4 1h2z" class="M"></path><path d="M528 223c1 0 2-2 2-3 2-4 5-7 6-10 1-2 2-5 3-6 0-1 0-1 1-1v2l1 1 1-2c0 1 1 1 2 1v1c0 1 0 1-1 3v1c0 1-1 3-1 4h-1c0 2-1 3-2 4s-1 3-2 4h0 0l-1 3h-2c-1 0-1 0-2-1h-1v-1l-1 1c-1 0-1 0-2-1z" class="o"></path><path d="M540 203v2l1 1v2h0v2c-1 2-2 3-3 5v1c-1-1-1-2-1-3 0-2 1-3 1-5 1-2 2-3 2-5z" class="N"></path><path d="M538 215c0-3 1-4 1-6v-1h1 1v2c-1 2-2 3-3 5z" class="m"></path><path d="M537 213c0 1 0 2 1 3h0l-2 2c-1 0-1 1-1 2 0 0 0 1 1 1v1h1 0 0l-1 3h-2c-1 0-1 0-2-1h-1c2-4 4-7 6-11z" class="L"></path><path d="M532 224l1 1 1-1c1-1 2-1 3-2l-1 3h-2c-1 0-1 0-2-1z" class="G"></path><path d="M542 204c0 1 1 1 2 1v1c0 1 0 1-1 3v1c0 1-1 3-1 4h-1c0 2-1 3-2 4s-1 3-2 4h-1v-1c-1 0-1-1-1-1 0-1 0-2 1-2l2-2h0v-1c1-2 2-3 3-5v-2h0v-2l1-2z" class="V"></path><path d="M542 204c0 1 1 1 2 1-1 2-1 3-2 3h-1v-2l1-2z" class="v"></path><defs><linearGradient id="D" x1="237.129" y1="584.492" x2="218.403" y2="594.494" xlink:href="#B"><stop offset="0" stop-color="#706154"></stop><stop offset="1" stop-color="#8c7d6c"></stop></linearGradient></defs><path fill="url(#D)" d="M216 578l14 3 9 2c0-1 0-1 1-2h0 0l1-1h7c1-1 2-1 4-1v1h4c2-1 5 1 7 0 3-1 7-1 10 0h0v1h3 1v1c-1 0-2 1-3 1h3v1h-2c0 1 1 1 2 1 0 1 1 2 1 3l-1 1c1 0 1 1 2 2h-1 0v-1c-2 1-4 0-5 1h0 4c0 1 1 1 1 2h1c1 0 2-1 3 0s1 2 1 3c-1 1-3 0-4 0 0 1 0 1-1 2 0 1 0 1-1 2h0c-1 1-2 0-3 0-1 1-2 1-2 2-1 0-2 0-3 1-1 0-3-1-4 0l-1 1h-3c1 1 1 1 1 2h0l-1 1c0 1 0 1 1 2h-12-5c-2 1-5 0-6 2v-1h-2c-1-1-1-1-2-1s-1 0-2-1c0-1 0-1 1-2-2-1-4 0-5 0-4 0-8 1-12 1v-14-3c-1-1-1-2-1-3v-3-6z"></path><path d="M250 587l22-1 2 2-2 1c-2-1-5 0-7 0h-19c-2 0-6 1-7 0v-1c2-2 8-1 11-1h0z" class="V"></path><defs><linearGradient id="E" x1="222.894" y1="579.423" x2="220.199" y2="586.624" xlink:href="#B"><stop offset="0" stop-color="#5e5147"></stop><stop offset="1" stop-color="#7a6c5b"></stop></linearGradient></defs><path fill="url(#E)" d="M216 578l14 3h-2s0 1 1 1v1c1 2 6 2 8 2-1 1-3 1-4 0h-3c-1 0-3 1-4 1s-3 0-4 1c-1 0-1 1-2 0-1 0-2 1-3 2v1c-1-1-1-2-1-3v-3-6z"></path><path d="M216 587l3-3h1v1l2 2c-1 0-1 1-2 0-1 0-2 1-3 2v1c-1-1-1-2-1-3z" class="t"></path><defs><linearGradient id="F" x1="258.9" y1="580.391" x2="257.764" y2="587.633" xlink:href="#B"><stop offset="0" stop-color="#e6e0c8"></stop><stop offset="1" stop-color="#f5e8e8"></stop></linearGradient></defs><path fill="url(#F)" d="M238 585c1-1 1-1 2-1 4-1 9-1 13-1 7 0 14-1 21 0h3v1h-2c0 1 1 1 2 1v1h-5l-22 1h-6v-1h4 1c-3-1-7 0-10 0l-1-1z"></path><path d="M275 584c0 1 1 1 2 1v1h-5l-22 1h-6v-1h4 1l26-2z" class="D"></path><path d="M240 581l1-1h7c1-1 2-1 4-1v1h4c2-1 5 1 7 0 3-1 7-1 10 0h0v1h3 1v1c-1 0-2 1-3 1-7-1-14 0-21 0-4 0-9 0-13 1-1 0-1 0-2 1h0-1c-2 0-7 0-8-2v-1c-1 0-1-1-1-1h2l9 2c0-1 0-1 1-2h0 0z" class="P"></path><path d="M240 581h11c1 0 1 0 2 1l-14 1c0-1 0-1 1-2h0 0z" class="v"></path><path d="M240 581l1-1h7c1-1 2-1 4-1v1h4c2-1 5 1 7 0 3-1 7-1 10 0h0v1h3l-20 1h-3c-1-1-1-1-2-1h-11z" class="V"></path><path d="M251 581h5v1h-3c-1-1-1-1-2-1z" class="X"></path><path d="M277 585c0 1 1 2 1 3l-1 1c1 0 1 1 2 2h-1 0v-1c-2 1-4 0-5 1h0 4c0 1 1 1 1 2h1c1 0 2-1 3 0s1 2 1 3c-1 1-3 0-4 0l-6 1h-7c-2 0-4 1-6-1h-2-2-7 0c-1 1-3 1-4 0v1h-1c-1-1-2 0-3-1l-2 1v1l-1 1v1c0-1 0-2-1-3-2 0-4 0-6 1v-1-1h4 2c1-1 1-2 2-3v-2h7l20-1h5 0 6v-1c-1-1-2-1-3-1l-2-2h5v-1z" class="b"></path><path d="M279 593c1 0 2-1 3 0s1 2 1 3c-1 1-3 0-4 0l-6 1h-7c-2 0-4 1-6-1h7l8-1c2 0 4 1 5 0l1-1h-2v-1z" class="q"></path><path d="M239 593c1 1 3 1 5 1 1-1 4-1 5-1h1c2-1 5 0 7-1 1 0 1 0 2 1h0c1-1 2-1 3-1 2 0 4 1 7 0h3 1 3 1c-2 2-3 1-4 1-2 1-5 1-7 1v-1c-2 2-5 1-7 2h-1v1h-2-7 0c-1 1-3 1-4 0v1h-1c-1-1-2 0-3-1l-2 1v1l-1 1v1c0-1 0-2-1-3-2 0-4 0-6 1v-1-1h4 2c1-1 1-2 2-3z" class="a"></path><path d="M241 596h-2c2-2 4-1 6-2 1 1 1 0 1 1h1c-1 0-1 0-2 1h0v1h-1c-1-1-2 0-3-1z" class="h"></path><path d="M252 594h3 3c3 0 5 0 8-1-2 2-5 1-7 2h-1v1h-2c-1-3-3 0-4-2z" class="G"></path><path d="M247 595c1-1 3-1 5-1 1 2 3-1 4 2h-7 0c-1 1-3 1-4 0h0c1-1 1-1 2-1z" class="U"></path><path d="M217 593c2 0 5 0 7-1l15-1v2c-1 1-1 2-2 3h-2-4v1 1c2-1 4-1 6-1 1 1 1 2 1 3l1 1c-1 1-1 2 0 3-1 1-2 1-3 1l-2 1c-2-1-4 0-5 0-4 0-8 1-12 1v-14z" class="L"></path><path d="M229 606c0-1 0-1 1-2 1 0 1 0 2-1h0l1 2h3l-2 1c-2-1-4 0-5 0z" class="G"></path><path d="M231 598h-4v-1c1-1 1 0 2 0h1l2-2 3 1h-4v1 1z" class="d"></path><path d="M256 596h2 2c2 2 4 1 6 1h7l6-1c0 1 0 1-1 2 0 1 0 1-1 2h0c-1 1-2 0-3 0-1 1-2 1-2 2-1 0-2 0-3 1-1 0-3-1-4 0l-1 1h-3c1 1 1 1 1 2h0l-1 1c0 1 0 1 1 2h-12-5c-2 1-5 0-6 2v-1h-2c-1-1-1-1-2-1s-1 0-2-1c0-1 0-1 1-2l2-1c1 0 2 0 3-1-1-1-1-2 0-3l-1-1v-1l1-1v-1l2-1c1 1 2 0 3 1h1v-1c1 1 3 1 4 0h0 7z" class="i"></path><path d="M239 602h5l1 1h-1c-1 0-4 1-5 0v-1z" class="X"></path><path d="M242 600c-1 0-2 0-3-1v-1h4 1 6c-2 1-4 1-7 1h0c2 0 3 0 5 1h-6z" class="L"></path><path d="M246 601h2c1 0 1 1 2 0s5 0 6 0h1v1h4l-16 1-1-1h-5c2-1 5 0 7-1z" class="H"></path><path d="M246 608l12-1h3c0 1 0 1 1 2h-12-5-1c0-1 0-1 1-1h1z" class="M"></path><path d="M245 609h-1c0-1 0-1 1-1h1 5l1 1h-2-5zm17-3h-7c-2 1-6 0-8 0-3 0-5 1-7 0l-1-1c1-1 10-1 12-1h4l1-1v1h3 2c1 1 1 1 1 2h0z" class="H"></path><path d="M266 597h7l-2 1v1h-5l-14 1h-4c-2-1-3-1-5-1h0c3 0 5 0 7-1l16-1z" class="U"></path><path d="M273 597l6-1c0 1 0 1-1 2 0 1 0 1-1 2h0c-1 1-2 0-3 0-1 1-2 1-2 2-1 0-2 0-3 1-1 0-3-1-4 0l-1 1c-1-2-1-2-3-2h-4v-1h-1c-1 0-5-1-6 0s-1 0-2 0h-2c-2 0-4 1-6 0v-1h2 6 4l14-1h5v-1l2-1z" class="D"></path><path d="M273 597l6-1c0 1 0 1-1 2 0 1 0 1-1 2h0v-1c-1-1-5 0-6 0v-1l2-1z" class="b"></path><path d="M257 601l2-1c1 1 11 1 12 0h3c-1 1-2 1-2 2-1 0-2 0-3 1-1 0-3-1-4 0l-1 1c-1-2-1-2-3-2h-4v-1z" class="U"></path><path d="M533 263c2 0 5 0 7 2v4h1v1h0 1 1v1l1 1h1c1 0 1 0 2 1-1 0-1 1-2 1-2-1-3-1-4 0-1 0-1 0-2 1 1 1 1 2 1 3l-1 1h0l1 1v1c-2 2-2 2-2 4-1 1 0 1 0 2v7c1 1 1 3 1 4v3c1 2 1 2 1 4h-1c0 2 0 3 1 4-1 1-1 1-2 1 1 1 1 1 2 1v1c-2 0-4-1-6-1-3 0-6 1-9 0h-7-16c-2 0-6 1-8 0h0c0-1 0-1-1-1-1-1-1-2-1-3 1-1 1-1 2-1 0-1 0-2 1-2v-1c-1-2-4-1-6-2v-1-3l1-5h0l1-3c2-4 4-7 7-10h0v2c1 0 2 0 3 1 1 0 2 2 3 1l2-2h0l3-1v-1h0c0-2 2-5 3-7l3-3h3 0c0 1 0 1-1 2h-1c0-1 0-1-1-1l2 3 1 1c2 1 2 1 3 2 1-1 1-2 1-3l1-1h0 2l1-2 1-1v-1l1-1h0l-1-1h2l2-2c0-1 0 0 1-1h1z" class="H"></path><path d="M529 285h1l2 2v1c-1 0-1 0-2-1 0 0 0-1-1-2z" class="Y"></path><path d="M525 298l1 1v1h-1-1-1c0-1 1-2 2-2zm10-11h1c1 2 0 3 0 5h-1c0-1 0-1-1-2 0-1 1-2 1-3z" class="I"></path><path d="M531 294h1v1 2h-2c0-1 0-2 1-3z" class="P"></path><path d="M532 274h0l1 1c1 1 1 0 2 1v3-1h-1v-1c-1-1-2-1-3-2h-1c1 0 1 0 2-1z" class="I"></path><path d="M533 280h0c1 1 1 2 0 3l-1 1c0-1-1-1-1-2 0-2 0-2 2-2z" class="g"></path><path d="M523 278h2l1 2c-1 0-1 0-2 1 0 0 0 1-1 1v2l-1-1 1-5z" class="o"></path><path d="M523 272l1 2c-1 1-1 1 0 3h1v1h-2c0-1-1-1-2-2 1-1 1-2 1-3l1-1z" class="M"></path><path d="M538 310h-1v-3-1l2-1c0 2 0 3 1 4-1 1-1 1-2 1z" class="P"></path><path d="M510 304l1-1 1 1c-1 2-2 4-3 5-1 0-1-1-1-2v-1-2h2 0z" class="D"></path><path d="M540 265v4h1v1h0 1 1v1l1 1h1c1 0 1 0 2 1-1 0-1 1-2 1-2-1-3-1-4 0v-1l-1-1v-7z" class="a"></path><path d="M508 291c4-1 5-8 9-10v1 1c-2 1-2 1-3 2v1l1 1c0 1-1 0-2 2 0 0 1 0 1 1h1c-2 2-5 0-6 2h-1v-1z" class="R"></path><path d="M508 291c1-4 5-10 7-13h0c0 2-1 3-2 4v2l2-2v-1h1 1c-4 2-5 9-9 10z" class="L"></path><path d="M518 311c1-1 1-1 2-1s1-1 2 0h1v-1-1h2l1 2c1 0 4 0 5-1l1-1c1 1 1 1 1 2-1 1-6 1-8 1h-7z" class="M"></path><path d="M511 293c2 1 3 1 3 2 0 2 0 4-1 6l-1 3-1-1v-1c-1 0-2-1-2-1v-1h1c1 0 1-1 2-1 0 0 1 0 1-1-1-1-3-1-4-2 1 0 2 0 2-1v-1-1z" class="n"></path><path d="M525 290c-1 2-4 3-4 6l-1 2v1c-1 1 0 1-1 2h-1l1-1v-2c0-1 0-2 1-2v-1-1-1-1-1c1-2 2-5 2-8l1 1h0l1 1v-1c1 2 0 4 1 6z" class="E"></path><path d="M523 284l1 1v-1c1 2 0 4 1 6h-2-1v-2s0-1 1-2v-2z" class="I"></path><path d="M527 266h2l2-2c0-1 0 0 1-1v1l1 1c1 1 1 2 1 3 0 2 0 5-2 6h0c-1 1-1 1-2 1-1 1-3 1-4 2h-1-1c-1-2-1-2 0-3l-1-2h0 2l1-2 1-1v-1l1-1h0l-1-1z" class="F"></path><path d="M532 271c-1 1-1 1-1 2h-1l-1-1c1-1 0-2 1-3l2 2z" class="R"></path><path d="M530 268v-2l2-1h0v1c0 1 1 1 1 2s-1 2-1 3l-2-2c1 0 0 0 0-1h0z" class="s"></path><path d="M527 268h2 1 0c0 1-1 1-1 2v3l-2 2h1c1 0 1-1 2-1h2c-1 1-1 1-2 1-1 1-3 1-4 2h-1-1c-1-2-1-2 0-3l-1-2h0 2l1-2 1-1v-1z" class="K"></path><path d="M527 269v1h0c-1 1-1 2-1 3l1 1c-1 0-1 0-2 1l-1-1-1-2h0 2l1-2 1-1z" class="o"></path><path d="M503 301h6s1 1 2 1v1l-1 1h0-2v2 1c0 1 0 2 1 2l-1 1h-1-8l-5 1h0c0-1 0-1-1-1-1-1-1-2-1-3 1-1 1-1 2-1 0-1 0-2 1-2v-1h1 1l6-2z" class="S"></path><path d="M499 310c-2 0-3 1-4 0l-1-1 1-1 12 1v1h0-8z" class="V"></path><g class="E"><path d="M500 305c3 0 6 0 8 1v1c-2 1-10 1-13 0l-1-1c2-1 4-1 6-1z"></path><path d="M503 301h6s1 1 2 1v1l-1 1h0-2v2c-2-1-5-1-8-1h-3v-1h1l-1-1 6-2z"></path></g><path d="M498 304c4-1 8 0 12 0h0-2v2c-2-1-5-1-8-1h-3v-1h1z" class="j"></path><path d="M515 269h3 0c0 1 0 1-1 2h-1c0-1 0-1-1-1l2 3c-1 1-1 2-1 2l-1 3c-2 3-6 9-7 13v1h1l2 1v1 1c0 1-1 1-2 1 1 1 3 1 4 2 0 1-1 1-1 1-1 0-1 1-2 1h-1v1h-6l-6 2h-1-1c-1-2-4-1-6-2v-1-3l1-5h0l1-3c2-4 4-7 7-10h0v2c1 0 2 0 3 1 1 0 2 2 3 1l2-2h0l3-1v-1h0c0-2 2-5 3-7l3-3z" class="u"></path><path d="M510 294h-2l-2-2h0v-1c0-1 1-1 1-1l1 1v1h1l2 1v1h-1z" class="B"></path><path d="M504 286h1c0 1 0 1-1 1 1 1 1 2 2 2-1 0-1 1-2 1h-2c-1 0-2 0-3-1 2-1 3-2 5-3z" class="C"></path><path d="M504 287c1 1 1 2 2 2-1 0-1 1-2 1v-3z" class="d"></path><path d="M509 279h0v1 1 2h1c-1 2-3 4-4 6-1 0-1-1-2-2 1 0 1 0 1-1h-1l5-6v-1z" class="AE"></path><path d="M506 281l3-1-5 6c-2 1-3 2-5 3 0 0-1 1-2 1 1-2 3-4 4-6l-2-1c1-1 1-1 2-1s2 2 3 1l2-2h0z" class="T"></path><path d="M515 269h3 0c0 1 0 1-1 2h-1c0-1 0-1-1-1l2 3c-1 1-1 2-1 2h-1l-5 8h-1v-2-1-1h0 0c0-2 2-5 3-7l3-3z" class="G"></path><path d="M515 269h3 0c0 1 0 1-1 2h-1c0-1 0-1-1-1-2 2-5 6-6 9h0 0c0-2 2-5 3-7l3-3z" class="j"></path><path d="M498 279h0v2c1 0 2 0 3 1-1 0-1 0-2 1l2 1c-1 2-3 4-4 6v4h-1v-1h-1c-1 1-1 2-2 4 0 0 0 1-1 1-1 1-1 2-2 2l-1 1v-1-3l1-5h0l1-3c2-4 4-7 7-10z" class="s"></path><path d="M489 297c2-1 2-2 3-4 1-1 2-3 3-4 1-2 2-5 4-6l2 1c-1 2-3 4-4 6v4h-1v-1h-1c-1 1-1 2-2 4 0 0 0 1-1 1-1 1-1 2-2 2l-1 1v-1-3z" class="M"></path><path d="M492 298c1 0 1-1 1-1 1-2 1-3 2-4h1v1h1c2 1 4 0 7 1 2 0 4-1 6-1h1v1c0 1-1 1-2 1 1 1 3 1 4 2 0 1-1 1-1 1-1 0-1 1-2 1h-1v1h-6l-6 2h-1-1c-1-2-4-1-6-2l1-1c1 0 1-1 2-2z" class="a"></path><path d="M496 300c2 0 5 0 7 1l-6 2h-1v-2l-1-1h1z" class="W"></path><path d="M489 301l1-1c1 0 1-1 2-2h1v1c1 0 2 1 3 1h-1l1 1v2h-1c-1-2-4-1-6-2z" class="T"></path><path d="M492 298c1 0 1-1 1-1 1-2 1-3 2-4h1v1l-1 1v1c3 2 11 1 13 1l1-1c1 1 3 1 4 2 0 1-1 1-1 1-1 0-1 1-2 1v-1c-1 0-1 0-2-1h-6-9-1z" class="B"></path><path d="M510 294h1v1c0 1-1 1-2 1l-1 1c-2 0-10 1-13-1v-1l1-1h1c2 1 4 0 7 1 2 0 4-1 6-1z" class="U"></path><path d="M555 410c0-1 0-2 1-3h1v2h0 2l1 1 2 2c1-1 1-2 2-2l2 1h1l1 1 1-1c1 2-1 3 2 3v2s2 1 2 2v2c0 2 0 4-1 6h0c0 1 0 5-2 5v1 3l-10 34v3c-1 1-1 2-1 2l-1 4-1 2-1 3-1 5-2 5-1-1c0 1-1 2-1 3-1 0-1 2-1 3h-1 0c0 1-1 2-2 2 1 1 1 2 1 3h1l-6 12h0c-1-1-3-1-4-2-1 0-1-1-2-1-1-1-2-1-3-2-1-2-2-4-2-5-1-2-2-5-3-7l-1 1c0 1-1 1-1 2h-1v3h-1v-4c-1 1-1 2-2 3-1 0-1 0-1-1h0c0-1 0-2-1-3h0l-1 1c-3-2-4-4-5-7-1-2-2-4-2-6 2-2 4-5 7-7v-2h-1c-2-1-1-2-1-4-1-1-1-3-2-4v-1c-1-1-1-2-2-3h2l2-1-1-2 1-1h-1-1c-1 1-1 1-2 1h0 0v-1h0l2-3 2-2h-2v-2h1l1-1 1-2-2-1c1-1 1-2 2-4v-2c1-1 1-2 1-3l-1 1v-2-1-3c-1-1-1-2-1-3h0c-1-1-1-2-1-3s0-2-1-2v-5c0-1-1-3 0-4h3 1c2 0 2-1 3-2l1 1 1-1h2 1v-2h3c1 1 2 1 3 0v2h-1l1 2v1l1 1c0-1 1-1 1-2h1c0 1 0 1 1 1h0c2 1 3 1 5 1h0c1 0 2 0 3 1 1 0 2 0 2 1 1-1 2-1 3-2s1-2 2-2l1-1c0-1 0-1-1-2h-2 0v-2l-2-1h-1c1-2 1-4 2-5l2 1c-1 1-2 1-2 2 1 1 1 1 2 1 0 0 1-1 1-2v-1h0 2z" class="V"></path><path d="M540 467c2 0 2 0 3-1 0-1 0-1 1-2 1 1 1 1 1 2 2 0 3 0 4 1-2 0-3-1-5 1l-2 2v-1c-1-1-1-2-2-2z" class="G"></path><path d="M518 465h0c1-1 1-1 1-2 1-1 1-1 2-1l1 1v3l1-1h0v1h0l-1 1-1-1h-3c0 1-1 0-2 0l2-1z" class="Q"></path><path d="M543 453h3c-1 2-2 2-4 2s-4 1-7 0c0 1 0 1-1 2h0c-2-1-3 0-4 0h-1c-2 0-3 2-5 2l-1-1h1l1 1 1-1h1l1-1h0l1-1h1 0c-1 0-2 0-3 1h-9 0-2v-2h1 3 1c0 1 1 1 1 0h1 1l1-1h0c1 0 2 0 3 1h-1 1 4 1l5-1 5-1z" class="I"></path><path d="M520 455h1c0 1 1 1 1 0h1 1l1-1h0c1 0 2 0 3 1h-1 1c-3 1-7 1-10 2h0-2v-2h1 3z" class="AD"></path><path d="M531 467c1-1 2-1 3-1h3v1l1-1c1 1 1 1 2 1s1 1 2 2c-1 1-1 1-2 1l-2 2h1c0 1-1 1-1 1h-3-1-4s0-1-1-1c1-1 1-1 1-2h0l1-2v-1z" class="M"></path><path d="M531 468h2v3h-2l-1-1 1-2z" class="O"></path><path d="M535 471h0v-2c0-1 1-1 2-2 0 1 0 3-1 4h-1z" class="S"></path><path d="M537 467l1-1c1 1 1 1 2 1s1 1 2 2c-1 1-1 1-2 1l-2 2h0 0-1 0l-2-1h1c1-1 1-3 1-4h0z" class="e"></path><path d="M523 466h2v-1h1c0 1 0 1 1 1h0 2 1v1h1v1l-1 2h0s-1 0-1 1h-1c-2 1-2 1-3 2v-1c-1 1-2 1-3 1h0c0 1 0 2-1 3v1c-1 1-1 1-2 1-2-1-1-2-1-4-1-1-1-3-2-4v-1c-1-1-1-2-2-3h2c1 0 2 1 2 0h3l1 1 1-1h0z" class="M"></path><path d="M517 469h3v2h-2c-1-1-1-1-1-2z" class="n"></path><path d="M523 468l1 1v2h-1-1c0-1 0-2 1-3z" class="R"></path><path d="M518 466h3v1s0 1-1 1v1h-3l1-3z" class="K"></path><path d="M523 466h2v-1h1c0 1 0 1 1 1h0 2 1v1h1v1l-1 2h0s-1 0-1 1h-1c-2 1-2 1-3 2v-1c-1 1-2 1-3 1v-1l1-1h1v-2l-1-1-1-1h2l-1-1h0z" class="C"></path><path d="M527 467l2 1v3h-1-2c0-2 0-2 1-4z" class="s"></path><path d="M527 466h2 1v1h1v1l-1 2h0s-1 0-1 1v-3l-2-1v-1z" class="e"></path><defs><linearGradient id="G" x1="550.974" y1="441.264" x2="539.758" y2="434.914" xlink:href="#B"><stop offset="0" stop-color="#0c0a09"></stop><stop offset="1" stop-color="#2e2a21"></stop></linearGradient></defs><path fill="url(#G)" d="M546 423c1 0 2 0 2 1h0v3l1 1h0v1c-1 1-1 1-1 2v8c-1 5-1 9-2 14h0-3-1 0v-2c-1-1-1-4-1-5h0c1-1 1-3 1-4 1-1 1-2 1-4h0l1-4v-3l1-5c0-2 0-2 1-3z"></path><path d="M543 438l1-4v4c1 1 0 2 0 3l-1 8c0 2-1 3-1 4h0v-2c-1-1-1-4-1-5h0c1-1 1-3 1-4 1-1 1-2 1-4h0z" class="f"></path><path d="M549 467c3 0 9-1 11 1v1 3c-1 1-1 2-1 2l-1 4-1 2-1 3c-1-1-3-1-4-2h-6v-1c-2 0-3-1-4-2h-1c0 1 0 1-1 2h-3v-1l2-2-1-1h-1 0c1-1 2-2 2-3h-1s1 0 1-1h-1l2-2c1 0 1 0 2-1v1l2-2c2-2 3-1 5-1z" class="W"></path><path d="M542 469v1c-1 2-2 5-3 7l-1-1h-1 0c1-1 2-2 2-3h-1s1 0 1-1h-1l2-2c1 0 1 0 2-1z" class="h"></path><path d="M544 475c1-1 4 0 5 1s1 2 1 3h-1c-1-1-2-2-4-2-1-1-1-1-1-2z" class="N"></path><path d="M559 474l-1-1h-1v-1c-3-1-5-2-7-3 4 0 7 0 10 3-1 1-1 2-1 2z" class="G"></path><path d="M557 480h0c-3-1-5-5-7-7-2-1-4-2-5-3h0c0-1 1-1 1-1 4 1 9 6 11 9h0 1l-1 2z" class="h"></path><path d="M555 410c0-1 0-2 1-3h1v2h0 2l1 1 2 2c1-1 1-2 2-2l2 1h1l1 1 1-1c1 2-1 3 2 3v2s2 1 2 2v2c0 2 0 4-1 6h0c0 1 0 5-2 5v1c-1 0-1 0-2-1h0 1l1-1c-1 0-2-1-3-2-1 1-1 2-2 2 1 1 1 1 1 2h-2v-3l-1-1c-1 0-1 0-2 1v2l1 1h-2v-2h-1s-1 0-1 1c-1 0-1 1-1 1h-1c-1-1 0-2-1-3l-3-1v3h1 1 0c-1 1-1 1-2 1s-1 0-2-1h-1-1c0-1 0-1 1-2v-1h0l-1-1v-3h0c1-1 2-1 3-2s1-2 2-2l1-1c0-1 0-1-1-2h-2 0v-2l-2-1h-1c1-2 1-4 2-5l2 1c-1 1-2 1-2 2 1 1 1 1 2 1 0 0 1-1 1-2v-1h0 2z" class="B"></path><path d="M569 424v-1s2 1 2 2v1c-1 1-2 0-2 0l-1-1 1-1h0zm-14-2h0c0-1 0-1-1-2l1-1h1l1 1c0 1 0 1-1 2h-1z" class="h"></path><path d="M564 420s1-1 2-1l1 2c0 1-1 1-2 1h-1v-2z" class="H"></path><path d="M551 423l2-2c1 0 1 1 2 1 0 2-1 2-3 2l-1-1z" class="G"></path><path d="M563 416h0c1 0 1 1 2 2l-2 1h0c-1 0-1-1-2-2 0-1 1-1 2-1z" class="b"></path><path d="M548 424c1 0 2 0 3 1h0c0 2 0 2-2 3l-1-1v-3z" class="H"></path><path d="M560 413h1c1 0 1 1 1 2l-1 1h-1c-1-1-1-1-1-2l1-1z" class="b"></path><path d="M570 420h3c0 2 0 4-1 6v-3c-1-1-2-1-3-2h0l1-1z" class="L"></path><path d="M564 411h1c1 1 2 2 2 3l-1 1v1c-2-1-2-2-3-3l1-2z" class="I"></path><path d="M555 410c0-1 0-2 1-3h1v2h0 2c-1 2 0 1 0 3l-1 1h-1c0-1-1-2-1-3h-1z" class="h"></path><path d="M569 424h-2c0 2-1 3-2 3h-1v-1h0c1-2 2-3 4-4l1 1v1z" class="H"></path><path d="M554 412h1c1 1 1 1 1 2s-1 2-2 3c-1-1-1 0-2-2l2-3z" class="C"></path><path d="M570 420l1-1-1-1h0c-1 1-1 1-2 1l-2-2v-1c1 0 1 0 2-1h0l1 1s1 0 1 1l1-1s2 1 2 2v2h-3z" class="f"></path><path d="M559 430c-1-1-2-1-3-2v-4c1-1 1-2 2-3h2c1 1 2 0 4-1v2h-1c-1 1-2 1-3 1l-1 1c0 1 1 4 1 4l-1 1c1 1 0 1 1 1h-1z" class="k"></path><path d="M529 471c0-1 1-1 1-1 0 1 0 1-1 2 1 0 1 1 1 1h4 1 3 1c0 1-1 2-2 3h0 1l1 1-2 2v1c-1 2-2 4-4 7l-3 3-2 5c-1 1-1 1-1 2-1 1-2 3-2 3-1 1-1 2-2 3-1 0-1 0-1-1h0c0-1 0-2-1-3h0l-1 1c-3-2-4-4-5-7-1-2-2-4-2-6 2-2 4-5 7-7v-2h-1c1 0 1 0 2-1v-1c1-1 1-2 1-3h0c1 0 2 0 3-1v1c1-1 1-1 3-2h1z" class="a"></path><path d="M527 481c-1 0-1-1-1-1 0-1 0-2 1-2s1 0 2 1l-2 2z" class="l"></path><path d="M528 486l-2-2v-1l2-1c1 2 1 2 0 3v1z" class="B"></path><path d="M522 473h2v2c-1 1-1 1-2 1h-1c1-1 1-2 1-3z" class="J"></path><path d="M529 471c0-1 1-1 1-1 0 1 0 1-1 2v1h-4c1-1 1-1 3-2h1z" class="H"></path><path d="M530 473h4c-1 1-1 2-1 2-1 1-1 1-2 1l-1-3z" class="J"></path><path d="M522 490l-1-1c1-1 1-2 1-2h2v1c0 1 0 1-1 1l-1 1z" class="O"></path><path d="M535 473h3 1c0 1-1 2-2 3h0l-2-1v-1-1z" class="F"></path><path d="M529 479h1v1 2h0v2h0l1 1v2h-1-2v-1h0v-1c1-1 1-1 0-3h-2l1-1 2-2z" class="C"></path><path d="M533 482h1l1-1v-3h0l1-1h1v2h0v1c-1 2-2 4-4 7l-3 3c-1 0-1 0-1-2 1 0 1 0 1-1h1v-2l-1-1 1-1c1-1 1-1 2-1z" class="N"></path><path d="M530 484l1-1c1-1 1-1 2-1l-1 4-1-1-1-1z" class="n"></path><path d="M528 487h2c0 1 0 1-1 1 0 2 0 2 1 2l-2 5c-1 1-1 1-1 2l-1-1h0c-1-2-2-2-4-2l-1-1 1-3 1-1c1 0 1 0 1-1l2-1h2z" class="K"></path><path d="M528 487h2c0 1 0 1-1 1-1 1-1 1-3 1v-2h2z" class="R"></path><path d="M522 490c1 1 3 2 4 1h2 0v1h-1v1 1s0 1 1 1c-1 1-1 1-1 2l-1-1h0c-1-2-2-2-4-2l-1-1 1-3z" class="N"></path><path d="M521 476h1v1l2 1v2l1 1h1-1c0 1 0 1-1 1 0 1-2 1-3 0h-1 0s0 1-1 1h0v3h1c-1 1-2 2-2 3l2 1-1 1h0l-1 1v-1h0-1s-1 0-1 1c-1 0-1 0-1 1-1-2-2-4-2-6 2-2 4-5 7-7v-2h-1c1 0 1 0 2-1v-1z" class="C"></path><path d="M522 477l2 1v2h-2c-1-1 0-2 0-3z" class="s"></path><path d="M515 493c0-1 0-1 1-1 0-1 1-1 1-1h1 0v1l1-1 2 2 1 1c2 0 3 0 4 2h0l1 1c-1 1-2 3-2 3-1 1-1 2-2 3-1 0-1 0-1-1h0c0-1 0-2-1-3h0l-1 1c-3-2-4-4-5-7z" class="c"></path><path d="M526 496l1 1c-1 1-2 3-2 3-1 1-1 2-2 3-1 0-1 0-1-1h0c0-1 0-2-1-3h2l1-1c1 0 2-1 2-2z" class="d"></path><path d="M515 493c0-1 0-1 1-1 0-1 1-1 1-1h1 0v1l2 2c-1 1-1-1-2 1 0 0 1 1 1 2h1l1 1v1l-1 1c-3-2-4-4-5-7z" class="H"></path><path d="M540 480c1-1 1-1 1-2h1c1 1 2 2 4 2v1h6c1 1 3 1 4 2l-1 5-2 5-1-1c0 1-1 2-1 3-1 0-1 2-1 3h-1 0c0 1-1 2-2 2 1 1 1 2 1 3h1l-6 12h0c-1-1-3-1-4-2-1 0-1-1-2-1-1-1-2-1-3-2-1-2-2-4-2-5-1-2-2-5-3-7l-1 1c0 1-1 1-1 2h-1v3h-1v-4s1-2 2-3c0-1 0-1 1-2l2-5 3-3c2-3 3-5 4-7h3z" class="D"></path><path d="M536 488c0-1 1-2 1-3h0l2 2c0-1 0-1 1-1 0 1 0 1-1 1v3l2 4 1 1-1 1c-1 0-1-1-1-1h-1-1c-1-2-1-4-2-6h-1l1-1z" class="l"></path><path d="M536 488h1 0c0 3 2 4 4 6h0l1 1-1 1c-1 0-1-1-1-1h-1-1c-1-2-1-4-2-6h-1l1-1z" class="m"></path><path d="M533 498h1c0-1-1-3 0-3 0 0 1 0 1 1s0 1 1 2v1c1 1 1 1 1 2 1 0 1 1 1 2h1v2l1 1c0 1 1 3 1 4h0c-1 0-1-1-1-1-1-1-1-1-2 0h0c-1-2-2-4-3-5 0-2-1-4-2-6z" class="T"></path><path d="M538 495h1 1s0 1 1 1v2l3 3c-1 1-2 1-3 2l1 2c1 0 1 1 1 2l1 1v3l-1 1v-2c0-1-1-2-1-3l-1-2c-1-1-1-1-1-2-1-1-2-2-2-3 0-2-1-2-2-3v-1c1 0 1 1 2 1v-2z" class="y"></path><path d="M541 498l3 3c-1 1-2 1-3 2l-1-3c-1-1-1-1-1-2h2z" class="u"></path><path d="M532 497v-1c1 1 1 1 1 2 1 2 2 4 2 6 1 1 2 3 3 5h0c0 1 1 2 1 4-1 0-1-1-2-1-1-1-2-1-3-2-1-2-2-4-2-5-1-2-2-5-3-7 1-1 1-1 3-1z" class="N"></path><path d="M529 498c1-1 1-1 3-1-1 1-1 2 0 3l1 2s0 1 1 2l-2 1c-1-2-2-5-3-7z" class="n"></path><path d="M532 505l2-1c1 2 3 5 3 7v1c-1-1-2-1-3-2-1-2-2-4-2-5z" class="W"></path><path d="M540 480c1-1 1-1 1-2h1c1 1 2 2 4 2v1h6c1 1 3 1 4 2l-1 5-1-1h-4l-2-1c-1 0-2 0-3-1h-2c-1-1-3-2-4-4h1c3 2 7 4 11 5v-1c-3-2-7-2-10-5h-1z" class="c"></path><path d="M541 489h1c0 1 1 2 1 3 2 3 2 9 4 11h1 1l-6 12v-2-1l1-1v-3l-1-1c0-1 0-2-1-2l-1-2c1-1 2-1 3-2l-3-3v-2l1-1-1-1-2-4v-1h2z" class="l"></path><path d="M539 490v-1h2l1 4c1 1 1 2 1 4 1 2 4 6 4 9h-1l-2-5-3-3v-2l1-1-1-1-2-4z" class="k"></path><path d="M537 485c0-1 0 0 1-1v-1-1l1-1c1 2 3 3 4 4h2c1 1 2 1 3 1l2 1h4l1 1-2 5-1-1c0 1-1 2-1 3-1 0-1 2-1 3h-1 0c0 1-1 2-2 2 1 1 1 2 1 3h-1c-2-2-2-8-4-11 0-1-1-2-1-3h-1-2v1-3c1 0 1 0 1-1-1 0-1 0-1 1l-2-2h0z" class="m"></path><path d="M548 486l2 1h-2v2h1 0c1 1 1 2 2 2 0 1 0 2-1 3h0c-1-2-2-3-2-5v-3z" class="B"></path><path d="M550 487h4l1 1-2 5-1-1v-1c0-1-1-2-3-2h-1v-2h2z" class="j"></path><path d="M547 500c0-2 0-4-1-6 0-1-2-3-1-4h1c2 1 1 2 2 4l1 1v3c0 1-1 2-2 2z" class="T"></path><path d="M537 485c0-1 0 0 1-1v-1-1l1-1c1 2 3 3 4 4h2c1 1 2 1 3 1v3c-1-1-2-1-3-1-1-1-3-1-4-2l-1-1c-1-1-1 0-3 0z" class="R"></path><path d="M531 416c1 1 2 1 3 0v2h-1l1 2v1l1 1c0-1 1-1 1-2h1c0 1 0 1 1 1h0c2 1 3 1 5 1h0c1 0 2 0 3 1-1 1-1 1-1 3l-1 5v3l-1 4h0c0 2 0 3-1 4 0 1 0 3-1 4h0c0 1 0 4 1 5v2h0 1l-5 1-5 1h-1-4-1 1c-1-1-2-1-3-1h0l-1 1h-1-1c0 1-1 1-1 0h-1-3l1-1 1-2-2-1c1-1 1-2 2-4v-2c1-1 1-2 1-3l-1 1v-2-1-3c-1-1-1-2-1-3h0c-1-1-1-2-1-3s0-2-1-2v-5c0-1-1-3 0-4h3 1c2 0 2-1 3-2l1 1 1-1h2 1v-2h3z" class="J"></path><path d="M520 446l-1 2v2l1-1c0-1 1-1 2-1l-1 1-2 3-2-1c1-1 1-2 2-4l1-1z" class="P"></path><path d="M534 421l1 1c0-1 1-1 1-2h1c0 1 0 1 1 1h0c-2 1-3 2-4 4-1 1-1 2-2 3s-1 2-2 3c0 1-1 2-2 2-2 3-2 6-5 8-1 1-2 4-3 5h0l-1 1v-2c1-1 1-2 1-3l-1 1v-2-1l1-1c0-1 0-2 1-3h1 2v-1c1-1 2-2 2-3 1-1 1-1 2-1 0 0 2-4 3-4 1-2 2-4 1-6h2z" class="f"></path><path d="M521 436h1 2v-1c1-1 2-2 2-3 1-1 1-1 2-1l-1 3c-1 1-2 1-2 3h-1-1c-1 2-2 3-3 5l-1 1v-2-1l1-1c0-1 0-2 1-3z" class="t"></path><path d="M543 422h0c1 0 2 0 3 1-1 1-1 1-1 3l-1 5v3l-1 4-2-1c1-1 1-2 1-3l-1 1c-1 0-1-1-2 0v1l-3 3c-1 1-2 1-2 3h-1c-1 0-2 1-3 2v-2l-2-2c2-2 4-5 6-7 3-3 5-7 8-9 1-1 1-1 1-2z" class="I"></path><path d="M537 435l-1 4c-1 1-2 1-2 3h-1c-1 0-2 1-3 2v-2c2-2 4-5 7-7z" class="K"></path><path d="M537 435c1-2 4-3 5-6l1-2c0-1 1-1 2-1l-1 5v3l-1 4-2-1c1-1 1-2 1-3l-1 1c-1 0-1-1-2 0v1l-3 3 1-4z" class="c"></path><path d="M542 434s1 0 2-1v-2 3l-1 4-2-1c1-1 1-2 1-3z" class="g"></path><path d="M528 440l2 2v2c1-1 2-2 3-2l-2 4s-1 0-1 1v1h1 1l1 1c1 0 0 1 0 1v3 2h-1-4-1 1c-1-1-2-1-3-1h0l-1 1h-1-1c0 1-1 1-1 0h-1-3l1-1 1-2 2-3 1-1c1-1 3-4 4-5s1-2 2-3h0z" class="c"></path><path d="M529 448h0c0 1 0 1-1 2h1v1h-2c-1 0-1 1-1 1-2 1-4 1-5 2 0-1 0-1 1-2l2-2 2-1c1-1 1 0 2 0l1-1z" class="K"></path><path d="M528 440l2 2v2c-1 1-2 2-1 4l-1 1c-1 0-1-1-2 0l-2 1h0c0-1 0-1 1-1 1-1 0-1 1-2 0-1 1-1 1-2s-1-1-1-2c1-1 1-2 2-3h0z" class="C"></path><path d="M528 440c1 2 0 3 0 5h-1c0-1-1-1-1-2 1-1 1-2 2-3z" class="c"></path><path d="M530 448h1 1l1 1c1 0 0 1 0 1v3 2h-1-4-1 1l1-1c-1-1-2-1-2-1 0-1 1-1 2-1 0-1 0-2 1-3v-1z" class="AA"></path><path d="M530 448h1l1 1-3 3c0-1 0-2 1-3v-1z" class="AC"></path><path d="M532 455c-1 0-1 0-2-1 1-1 2-3 3-4v3 2h-1z" class="g"></path><path d="M522 448c1-1 3-4 4-5 0 1 1 1 1 2s-1 1-1 2c-1 1 0 1-1 2-1 0-1 0-1 1h0l-2 2c-1 1-1 1-1 2l-1 1h-3l1-1 1-2 2-3 1-1z" class="d"></path><path d="M521 449h0v2c0 1 0 1 1 1-1 1-1 1-1 2l-1 1h-3l1-1 1-2 2-3z" class="AE"></path><path d="M536 439l3-3v-1c1-1 1 0 2 0l1-1c0 1 0 2-1 3l2 1h0c0 2 0 3-1 4 0 1 0 3-1 4h0c0 1 0 4 1 5v2h0 1l-5 1-5 1v-2-3s1-1 0-1l-1-1h-1-1v-1c0-1 1-1 1-1l2-4h1c0-2 1-2 2-3z" class="i"></path><path d="M533 445h0c0-1 0-2 1-2 0-1 0-1 1-2h0v2 3l-2-1z" class="q"></path><path d="M533 445l2 1h2l-1 2h-4-1v-1l2-2z" class="P"></path><path d="M541 437l2 1h0c0 2 0 3-1 4 0 1 0 3-1 4h0-3v1h0v1h-2l1-2h-2v-3-2h3 0c2-1 3-3 3-4z" class="F"></path><path d="M535 441h3v1h1v1h-1v-1h-1c0 1 0 1-1 1h-1v-2z" class="P"></path><path d="M541 437l2 1h0v1h-2s-1 0-1 1 0 1-1 2h-1v-1h0c2-1 3-3 3-4zm-5 11h2v-1h0v-1h3c0 1 0 4 1 5v2h0 1l-5 1-5 1v-2-3s1-1 0-1l-1-1h4z" class="q"></path><path d="M533 453s1-2 2-2h2c1-1 1-1 2-1v1c0 1-1 1-1 3l-5 1v-2z" class="F"></path><path d="M531 416c1 1 2 1 3 0v2h-1l1 2v1h-2c1 2 0 4-1 6-1 0-3 4-3 4-1 0-1 0-2 1 0 1-1 2-2 3v1h-2-1c-1 1-1 2-1 3l-1 1v-3c-1-1-1-2-1-3h0c-1-1-1-2-1-3s0-2-1-2v-5c0-1-1-3 0-4h3 1c2 0 2-1 3-2l1 1 1-1h2 1v-2h3z" class="a"></path><path d="M520 429c0-1 0-2 1-3h0c0 1 0 2-1 3h0z" class="K"></path><path d="M519 431h-1c1-1 1-2 2-2v2h0-1z" class="G"></path><g class="d"><path d="M520 431c1 2 1 2 1 4h-2v-4h1z"></path><path d="M521 426c1-1 1-1 2-1 0 2 0 4-1 5l-2 1v-2h0c1-1 1-2 1-3z"></path></g><path d="M521 435h1l1-2c0 2 0 2-2 3-1 1-1 2-1 3l-1 1v-3c-1-1-1-2-1-3l1 1h2z" class="k"></path><path d="M531 416c1 1 2 1 3 0v2h-1l1 2v1h-2c1 2 0 4-1 6-1 0-3 4-3 4-1 0-1 0-2 1 0 1-1 2-2 3v1h-2-1c2-1 2-1 2-3l1-1v-2-2c0-1 1-2 2-2h0c-1-1-2-2-2-3h0-2v-1-1-1h1 1v-1l1-1h2 1v-2h3z" class="o"></path><path d="M527 419h1l1 1v1h-1-3 2v-2z" class="K"></path><path d="M528 423l1-1h0c1 1 1 2 1 3v1h-1c0-1 0-2-1-3z" class="F"></path><path d="M531 418h2l1 2v1h-2c-1-1-1-1-1-3z" class="P"></path><path d="M524 430l1-1c1-1 1-2 2-2l1 2c-1 1-2 2-2 3h-2v-2z" class="K"></path><path d="M524 419l1-1 2 1v2h-2v1h-3v-1-1h1 1v-1z" class="k"></path><path d="M522 421h3v1h-3v-1z" class="AC"></path><path d="M531 416c1 1 2 1 3 0v2h-1-2l-1 1-1-1h-2 0 1v-2h3z" class="h"></path><path d="M525 422h3v1c1 1 1 2 1 3s-1 2-1 2v1l-1-2c-1 0-1 1-2 2l-1 1v-2c0-1 1-2 2-2h0c-1-1-2-2-2-3h0-2v-1h3z" class="N"></path><path d="M459 356h7 16 2 0c2 1 3 1 5 1h2c1 1 1 1 1 2l1 1v-3h3 5c1 1 3 3 3 4 1 1 1 1 1 2l1-1c0-1-1-2-2-3 0-1-1-1-1-2h7 6 1s-1 0-1-1h1 3 47 5c1 1 1 1 1 2s-1 1-1 2h0l1 1v3c0 1-1 2-1 3 1 1 1 1 1 2v1c0 1-1 1-1 2l1 1v3 4 1 6c1 1 0 2 0 3v6 1 2 2 1c0 1-1 2-2 3v1c-1 1-2 1-2 2l1 2-1 1-1 1-1-1h-1l-2-1c-1 0-1 1-2 2l-2-2-1-1h-2 0v-2h-1c-1 1-1 2-1 3h-2 0v1c0 1-1 2-1 2-1 0-1 0-2-1 0-1 1-1 2-2l-2-1c-1 1-1 3-2 5h1l2 1v2h0 2c1 1 1 1 1 2l-1 1c-1 0-1 1-2 2s-2 1-3 2c0-1-1-1-2-1-1-1-2-1-3-1h0c-2 0-3 0-5-1h0c-1 0-1 0-1-1h-1c0 1-1 1-1 2l-1-1v-1l-1-2h1v-2c-1 1-2 1-3 0h-3v2h-1-2l-1 1-1-1c-1 1-1 2-3 2h-1c-2 0-3-1-5 0h-1v-3 1c-2 1-6 0-7 0h-1v-1c-4 0-8 0-11-1h-2-1c-1 0-1 0-1 1 0-2 0-3-1-4h-2c-1-1-2-4-3-5 0-1 0-2-1-3h0v-1l-1-1c-1-2-3-4-2-6l-2-1v1 1h-1c0-1-1-1-1-1h-1l1-1h0l-1-1v-1l1-1h0c-2 0-3-1-4-1h-3-8-1v-1h0c0-2 0-2-1-3h0v-3-1 1l1-2h-1v-3-1h0c0-1 1-2 0-3 1-1 1-2 1-3v-1-4h-1c0-1 0-1-1-2h-2c1-1 2-2 2-4v-7l1 1z" class="r"></path><path d="M493 364c1 0 1 1 2 2l-2 1h0v-3z" class="T"></path><path d="M493 362l2 1-1 1h1v1 1c-1-1-1-2-2-2h-1l1-2z" class="j"></path><path d="M493 360v-3h3 0v1 1s1 1 1 2c1 1 0 1 1 1v1h-1l1 3v1c-1-1-2-1-3-2v-1h-1l1-1-2-1v-2z" class="J"></path><path d="M493 360v-3h3 0v1h-1c-1 1-1 1-1 2l1 1v1 1l-2-1v-2z" class="W"></path><path d="M493 371h0l1-1c0-1 1-2 2-4 0 1 0 1 1 2-1 0-1 0-2 1v1h1c-1 1-2 1-1 2 1 0 1 0 2 1l3 1c1 1 2 3 3 3l1 2c-1-1-1-1-2-1s-1-1-2-1h-1-1l1-1h0c-1-1-4-2-5-2v1h-1v-4z" class="J"></path><path d="M489 372h3l-1-1v-1h2v1 4h1v-1c1 0 4 1 5 2h0l-1 1-1-1h-1l1 2h-1 0c-1 0-2 0-3-1 0-1-1-1-2-1h0-2s0 1-1 1l-1-1v-1c0-2 0-2 1-3h1z" class="m"></path><path d="M493 377c1 0 1-1 2-1h1l1 2h-1 0c-1 0-2 0-3-1z" class="t"></path><path d="M487 376v-1c0-2 0-2 1-3v1c1 1 2 1 4 2-2 0-3 0-5 1h0z" class="J"></path><path d="M504 370h3c0 1 0 2-1 2l1 1v3h-1v-1h-1v1 1 1 1l-1 1h0v-1l-1-2c-1 0-2-2-3-3h0v-1s-1 0-1-1l-2-1h1 1 1c-1-1-1 0-1-1h3l1 1 1-1z" class="f"></path><path d="M488 377c1 0 1-1 1-1h2 0c1 0 2 0 2 1 1 1 2 1 3 1h0 1l-1-2h1l1 1h1 1c1 0 1 1 2 1s1 0 2 1v1h0c1 1 1 2 1 3h1c0 1-1 2-1 3h-2l-1-1v1c-1 0-3 0-3 1h3l1 1h-5c-2 1-2 1-4 1v-2h-1c0-1 0-1 1-2h0c-1-1-1-2-2-2-1-1-2-2-3-2l-1-1h-1v-1h0 2v-1l-1-1z" class="F"></path><path d="M492 381h1 1 1c1 1 2 1 3 1h2 1 0 0v1h-2c-3 0-5 0-7-2z" class="O"></path><path d="M494 385h1v1 1h4 3l1 1h-5c-2 1-2 1-4 1v-2h-1c0-1 0-1 1-2z" class="g"></path><path d="M502 378c1 0 1 0 2 1v1h0c1 1 1 2 1 3h1c0 1-1 2-1 3h-2l-1-1h-2-1v-1h3 0l-1-1v-1h0c1 0 1-1 1-1l-1-1 1-1v-1z" class="l"></path><path d="M488 377c1 0 1-1 1-1h2 0c1 0 2 0 2 1 1 1 2 1 3 1h0 1l-1-2h1l1 1h1 1c1 0 1 1 2 1v1l-1 1 1 1s0 1-1 1h0-1-2c-1 0-2 0-3-1h-1-1-1c-1-1-2-1-3-2l-2 1v-1h0 2v-1l-1-1z" class="AB"></path><path d="M488 377c1 0 1-1 1-1h2 0v1c1 1 1 2 2 2l-1 1-3-2-1-1z" class="o"></path><path d="M496 376h1l1 1h1 1c1 0 1 1 2 1v1l-1 1 1 1s0 1-1 1h0c-2-2-3-1-5-2v-2h0 1l-1-2z" class="S"></path><path d="M496 376h1l1 1h1 1c1 0 1 1 2 1v1c-2 0-4 0-6-1h1l-1-2z" class="g"></path><path d="M488 380l1 1c1 0 2 1 3 2 1 0 1 1 2 2h0c-1 1-1 1-1 2h1v2c2 0 2 0 4-1h5l-1 1h1v1c0 1 1 1 1 1 0 1-1 1-1 2h-1 0c-1 1-1 1-2 1 1 1 1 2 2 2-1 1-2 1-3 1h-1v-1l-2 1c0-1 0-1-1-1h0-1c-2 0-5 1-7 0v-1-2c-1 1-1 0-2 0l4-2c-1-1-2-2-2-3h0v-1c0-1 1-3 1-4s-1-1-1-2c1 0 1 0 1-1z" class="f"></path><path d="M497 395c-1 0-1-1-1-1v-1h3c0 1-1 1-2 2h0z" class="S"></path><path d="M499 393l1 1c1 1 1 2 2 2-1 1-2 1-3 1h-1v-1l-1-1h0c1-1 2-1 2-2z" class="O"></path><path d="M489 384c1 2 2 3 3 4v2h-1s-2-2-3-2c0-1 1-1 1-1 0-1-1-1-1-1 0-1 1-2 1-2zm-2 11c0-1 1-1 1-1 1-1 0-1 1-2h1l1 1v2c1 0 2 0 3 1h0c-2 0-5 1-7 0v-1z" class="i"></path><path d="M488 380l1 1c1 0 2 1 3 2 1 0 1 1 2 2h0c-1 1-1 1-1 2h1v2c2 0 2 0 4-1v1h-2v1h-2 0-2v-2c-1-1-2-2-3-4l-1-1c0-1-1-1-1-2 1 0 1 0 1-1z" class="S"></path><path d="M487 381c2 0 2 1 3 2s2 3 2 5c-1-1-2-2-3-4l-1-1c0-1-1-1-1-2z" class="o"></path><defs><linearGradient id="H" x1="479.789" y1="373.454" x2="490.548" y2="376.73" xlink:href="#B"><stop offset="0" stop-color="#0d090c"></stop><stop offset="1" stop-color="#212019"></stop></linearGradient></defs><path fill="url(#H)" d="M484 356c2 1 3 1 5 1h2c1 1 1 1 1 2l1 1v2l-1 2c0 1 0 3 1 4l-1 1c-1 0-2-2-3-2h-2v1l2 1v1h-2v1c1 1 2-1 3 0l-1 1h-1c-1 1-1 1-1 3v1l1 1 1 1v1h-2 0v1h1c0 1 0 1-1 1 0 1 1 1 1 2s-1 3-1 4v1h0c0 1 1 2 2 3l-4 2h0c-1 0-1 0-1-1v-17-11-8z"></path><path d="M489 357h2c1 1 1 1 1 2l1 1v2l-1 2c0 1 0 3 1 4l-1 1c-1 0-2-2-3-2h-2v1c-1-1-1-1-1-2h1 0c0-1 0-1-1-1v-4c1-1 1-1 1-2v-1h0 2v1l1-1 1-1h0-2 0z" class="i"></path><path d="M487 359c1 1 2 2 4 3v1h-3c0-1-1-2-2-2 1-1 1-1 1-2z" class="B"></path><path d="M486 361c1 0 2 1 2 2h3-1-2v1c1 1 2 1 4 1-1 1-1 1-2 1h-2v1h1-2v1c-1-1-1-1-1-2h1 0c0-1 0-1-1-1v-4z" class="S"></path><path d="M506 383v-1c1 1 2 3 2 4 1 1 2 1 3 2v-1l1 1c1-1 1-1 2-1h1c0 1 1 2 1 3l1-1v-2c0 1 0 3 1 3 0 1 0 4-1 4s-1 0-2 1h-2-3 0c-1 2-1 4-2 5h0v2h-1-1l-1 1 1 1h-1c0 1-1 2-2 3l-1-3c1 0 1-1 1-2h-1v-1l2-3-2-1v-1c-1 0-1-1-2-2 1 0 1 0 2-1h0 1c0-1 1-1 1-2 0 0-1 0-1-1v-1h-1l1-1-1-1h-3c0-1 2-1 3-1v-1l1 1h2c0-1 1-2 1-3z" class="m"></path><path d="M509 392c1 0 2 1 3 1-1-2-3-2-3-4 2 1 3 3 5 4v1l-1 1h-3 0-1l-1-1-1-1c1 0 1 0 1 1h1l1-1s-1 0-1-1h0z" class="R"></path><path d="M514 387h1c0 1 1 2 1 3l1-1v-2c0 1 0 3 1 3 0 1 0 4-1 4s-1 0-2 1h-2l1-1v-1h1c-2-2-3-3-4-5v-1l1 1c1-1 1-1 2-1z" class="F"></path><path d="M514 387h1c0 1 1 2 1 3v1h-1c-1 0-2-2-3-3 1-1 1-1 2-1z" class="AD"></path><path d="M502 385l1 1h2c0 1 0 3 1 4 1 0 3 2 3 2h0c0 1 1 1 1 1l-1 1h-1c0-1 0-1-1-1h-1c-1 1-2 3-2 5l-2-1v-1c-1 0-1-1-2-2 1 0 1 0 2-1h0 1c0-1 1-1 1-2 0 0-1 0-1-1v-1h-1l1-1-1-1h-3c0-1 2-1 3-1v-1z" class="s"></path><path d="M504 398c0-2 1-4 2-5h1l1 1 1 1h1c-1 2-1 4-2 5h0v2h-1-1l-1 1 1 1h-1c0 1-1 2-2 3l-1-3c1 0 1-1 1-2h-1v-1l2-3z" class="f"></path><path d="M508 394l1 1h1c-1 2-1 4-2 5h0l-2-2-1 1c0-1 0-1 1-2l1 1c1-2 1-2 1-4z" class="F"></path><path d="M505 399l1-1 2 2v2h-1-1l-1 1 1 1h-1c0 1-1 2-2 3l-1-3c1 0 1-1 1-2 1-1 1-2 2-3z" class="R"></path><path d="M485 397v-1c1 0 1 0 2-1v1c2 1 5 0 7 0h1 0c1 0 1 0 1 1l2-1v1h1c1 0 2 0 3-1v1l2 1-2 3v1h1c0 1 0 2-1 2l1 3c1-1 2-2 2-3 1 2 2 5 2 6 0 2 1 4 1 5v1c1 1 1 1 2 1s1 0 2-1h0l1 1v1c-2 1-6 0-7 0h-1v-1c-4 0-8 0-11-1h-2-1c-1 0-1 0-1 1 0-2 0-3-1-4h-2c-1-1-2-4-3-5 0-1 0-2-1-3v-1c0-1 0-2 1-3v-1-1-2h1z" class="B"></path><path d="M486 403v-1-1c0-1 0-1 1-2v4h1c0 1 1 1 2 2 0 0 1 1 0 2 0 1-1 0 0 2v2l-1-2h-1c-1-1-1-3-1-4v-1l-1-1z" class="i"></path><path d="M489 398c1-1 2 0 3 0v2l-1-1-1 6c-1-1-2-1-2-2h-1v-4c0-1 1-1 2-1z" class="F"></path><path d="M489 398h1l-2 5h-1v-4c0-1 1-1 2-1z" class="c"></path><path d="M490 405l1-6 1 1c1 2 1 4 1 6v5h0v3s-1 1-1 2h-1c-1 0-1 0-1 1 0-2 0-3-1-4h0l1-2v-2c-1-2 0-1 0-2 1-1 0-2 0-2z" class="C"></path><path d="M491 416c0-1 1-1 0-2v-1l1-1s0-1 1-1v3s-1 1-1 2h-1z" class="h"></path><path d="M485 397v-1c1 0 1 0 2-1v1c2 1 5 0 7 0h1v1 1c0 3 0 6 1 9-1-1-1-1-1-2-1 0-1 1-2 1 0-2 0-4-1-6v-2c-1 0-2-1-3 0-1 0-2 0-2 1-1 1-1 1-1 2v1 1l-1-1c1-1 0-4 0-5z" class="R"></path><path d="M495 396h0c1 0 1 0 1 1l2-1v1h1c1 0 2 0 3-1v1l2 1-2 3v1l-1 1-1 2h-1c-1 1-1 2-2 3-1-1-1 0-1-1-1-3-1-6-1-9v-1-1z" class="AG"></path><path d="M498 401l1 1c0 1 0 2-1 3 0 1 0 1-1 1h-1 1v-4l1-1z" class="B"></path><path d="M495 396h0c1 0 1 0 1 1l2-1v1h1c1 0 2 0 3-1v1l2 1-2 3v1l-1 1h0v-3h0c-1 0-1 1-2 2l-1-1-1 1h0l-1-4s0-1-1-1v-1z" class="y"></path><path d="M502 397l2 1-2 3v-1c-1-1 0-2 0-3z" class="J"></path><path d="M496 398h1 1 1c0 1 0 2-1 3l-1 1h0l-1-4z" class="r"></path><path d="M502 402h1c0 1 0 2-1 2l1 3c1-1 2-2 2-3 1 2 2 5 2 6 0 2 1 4 1 5v1c1 1 1 1 2 1s1 0 2-1h0l1 1v1c-2 1-6 0-7 0h-1v-1c-4 0-8 0-11-1h-2c0-1 1-2 1-2v-3h0v-5c1 0 1-1 2-1 0 1 0 1 1 2 0 1 0 0 1 1 1-1 1-2 2-3h1l1-2 1-1z" class="S"></path><path d="M501 411c1 1 1 1 1 2s-1 1-1 2h-2v-1l2-3z" class="o"></path><path d="M502 402h1c0 1 0 2-1 2 0 1 0 1-1 2 0 3-1 4-3 6h-1c1-2 2-4 3-7l1-2 1-1z" class="AD"></path><path d="M493 411c1 0 1 0 2-1h0c0 1 1 1 1 2h1v1 1 2h-3 0-2c0-1 1-2 1-2v-3h0z" class="n"></path><path d="M493 406c1 0 1-1 2-1 0 1 0 1 1 2 0 1 0 0 1 1 1-1 1-2 2-3h1c-1 3-2 5-3 7h0-1c0-1-1-1-1-2h0c-1 1-1 1-2 1v-5z" class="s"></path><path d="M501 406c1-1 1-1 1-2l1 3c1-1 2-2 2-3 1 2 2 5 2 6 0 2 1 4 1 5v1c1 1 1 1 2 1s1 0 2-1h0l1 1v1c-2 1-6 0-7 0h-1v-1c-1 0-1-1-2-1 1-1 1-2 2-2l-2-2-1 1c0-1 0-1-1-2l1-3c0-1 0-1-1-2z" class="t"></path><path d="M505 411l2-1c0 2 1 4 1 5h-1c-1 0-1-3-2-4z" class="F"></path><path d="M501 406c1-1 1-1 1-2l1 3v3c0 1 0 1-1 1v-3c0-1 0-1-1-2z" class="o"></path><path d="M505 417c-1 0-1-1-2-1 1-1 1-2 2-2 0 1 2 3 2 3l-2 1v-1z" class="O"></path><path d="M503 407c1-1 2-2 2-3 1 2 2 5 2 6l-2 1s0-1-1-1h-1v-3z" class="P"></path><path d="M518 390v2 1h0c0 1 1 1 1 1 1 0 2 0 2 1h1 1 1 0c1 0 1 0 2-1h2v2h3c0-1-1-1 0-2h1 1 2c2 1 1 2 3 0 1 1 1 2 1 2 1 2 2 4 1 6v4l-2 2c0 1-1 2-2 2 0 1-1 2-2 3-1 0-1 1-3 1 0 1 1 1 0 2h-3v2h-1-2l-1 1-1-1c-1 1-1 2-3 2h-1c-2 0-3-1-5 0h-1v-3l-1-1h0c-1 1-1 1-2 1s-1 0-2-1v-1c0-1-1-3-1-5 0-1-1-4-2-6h1l-1-1 1-1h1 1v-2h0c1-1 1-3 2-5h0 3 2c1-1 1-1 2-1s1-3 1-4z" class="b"></path><path d="M515 404c1 0 1-1 3 0v1c0 2 1 3 1 4l1 1c-1 1-1 1-2 1 0-2-1-4-2-5v1l-2 2h0c0-2 0-3 1-5z" class="Q"></path><path d="M524 407c1 2 2 2 4 3v1c-1 1-1 1-2 1h-1 0-2-1l-2-2-1-1c1 0 1 0 2 1h1 2 0v-2-1z" class="AG"></path><path d="M531 409v3h1v1h-1l-1 1h1c0 1 1 1 0 2h-3v2h-1-2l-1 1-1-1v-1h1c1 0 1-1 2 0h1 0c-2-1-2-2-3-2h-1v-1h2v-2h1c1 0 1 0 2-1h1l2-2z" class="H"></path><path d="M530 414h1c0 1 1 1 0 2h-3 0v-1c1-1 2-1 2-1z" class="Q"></path><path d="M518 390v2 1h0c0 1 1 1 1 1 1 0 2 0 2 1h1 1 1 0c1 0 1 0 2-1h2v2l-1 1c-1 0-1 1-2 2l-3 3c1 2 1 4 2 5v1 2h0-2-1c-1-1-1-1-2-1 0-1-1-2-1-4v-1c-2-1-2 0-3 0-1-1 0-4 0-5v-1-3c1-1 1-1 2-1s1-3 1-4z" class="F"></path><path d="M522 395h1l1 2c-1 0-2 1-3 1 0 0-1 2-2 2v-2l1-1 2-2z" class="g"></path><path d="M523 395h1 0c1 0 1 0 2-1h2v2l-1 1c-1-1-2-1-3-1v1l-1-2z" class="z"></path><path d="M522 410c-1-1-1-2-1-3h0-1c-1-1 0-1 0-1v-4l1-1c0 1 0 2 1 2v-1c1 2 1 4 2 5v1 2h0-2z" class="f"></path><path d="M522 403v-1c1 2 1 4 2 5v1h-1-1v-1c-1-1-1-1-1-2s1-1 1-1v-1z" class="z"></path><path d="M515 398h3 1v2c-1 1-1 3-1 5v-1c-2-1-2 0-3 0-1-1 0-4 0-5v-1z" class="I"></path><path d="M518 390v2 1h0c0 1 1 1 1 1 1 0 2 0 2 1h1l-2 2-1 1h-1-3v-3c1-1 1-1 2-1s1-3 1-4z" class="C"></path><path d="M519 394c1 0 2 0 2 1h1l-2 2s-1 0-1-1v-2h0z" class="I"></path><path d="M510 395h0 3 2v3 1c0 1-1 4 0 5-1 2-1 3-1 5h0c-1 2-1 3-1 5h0c-1 1-1 1-1 2h0 0c-1 1-1 1-2 1s-1 0-2-1v-1c0-1-1-3-1-5 0-1-1-4-2-6h1l-1-1 1-1h1 1v-2h0c1-1 1-3 2-5z" class="M"></path><path d="M511 406h0l1 1-1 1v1c-1-1-1-2 0-3z" class="K"></path><path d="M515 399c0 1-1 4 0 5-1 2-1 3-1 5h0-1v-2-1-2-3-1h1l1-1z" class="C"></path><path d="M510 395h0 3 2v3 1l-1 1h-1v1h0 0l-1 2c-1 1-1 2-2 2 0 1 0 1-1 1l1-3c-1-1-2-1-2-3 1-1 1-3 2-5z" class="c"></path><path d="M510 403l1-2c1 0 1-2 1-2 0-1-1-1-2-2h1c1 1 2 1 2 3v1h0 0l-1 2c-1 1-1 2-2 2 0 1 0 1-1 1l1-3z" class="N"></path><path d="M508 400h0c0 2 1 2 2 3l-1 3-1 1c0 1 0 2 2 3v1l2-1v2h0c0 1 1 1 0 2l-1 1 1 1h0 0c-1 1-1 1-2 1s-1 0-2-1v-1c0-1-1-3-1-5 0-1-1-4-2-6h1l-1-1 1-1h1 1v-2z" class="K"></path><path d="M508 400h0c0 2 1 2 2 3l-1 3-1 1v1h-1v-4h-1l-1-1 1-1h1 1v-2z" class="p"></path><path d="M533 394h2c2 1 1 2 3 0 1 1 1 2 1 2 1 2 2 4 1 6v4l-2 2c0 1-1 2-2 2 0 1-1 2-2 3-1 0-1 1-3 1h-1l1-1h1v-1h-1v-3l-2 2h-1v-1c-2-1-3-1-4-3-1-1-1-3-2-5l3-3c1-1 1-2 2-2l1-1h3c0-1-1-1 0-2h1 1z" class="F"></path><path d="M533 394h2c2 1 1 2 3 0 1 1 1 2 1 2 1 2 2 4 1 6-1-3-2-5-5-7l-2-1z" class="G"></path><path d="M532 394h1l2 1v2c1 2 2 1 1 3-1-2-3-3-5-4 0-1-1-1 0-2h1z" class="P"></path><g class="N"><path d="M534 404c0 1 0 1 1 2h0c-1 2-2 3-3 5h0l1 1c1-1 2-2 3-2 0 1-1 2-2 3-1 0-1 1-3 1h-1l1-1h1v-1h-1v-3-1c2-2 3-2 3-4z"></path><path d="M528 396h3c2 1 4 2 5 4v2 2l-1 2c-1-1-1-1-1-2v-1-1h0c-1-1-1-2-2-2-1-1-2-1-4-1-1 0-2 1-3 2v-2c1-1 1-2 2-2l1-1z"></path></g><path d="M534 403l1-1h1v2l-1 2c-1-1-1-1-1-2v-1z" class="h"></path><path d="M525 401c1-1 2-2 3-2 2 0 3 0 4 1 1 0 1 1 2 2h0v1 1c0 2-1 2-3 4v1l-2 2h-1v-1c-2-1-3-1-4-3-1-1-1-3-2-5l3-3v2z" class="m"></path><path d="M527 403h1c1-1 1-2 1-3 1 0 1 1 1 2 2 0 2 1 3 2h0-3v-1c-1 1-1 1-2 1s-1 0-1-1z" class="K"></path><path d="M525 401c1-1 2-2 3-2 2 0 3 0 4 1-1 0-1 1-2 2 0-1 0-2-1-2 0 1 0 2-1 3h-1-2v-2z" class="f"></path><path d="M522 402l3-3v2 2h2c0 1 0 1 1 1h1v3h-1l1 1h2v1l-2 2h-1v-1c-2-1-3-1-4-3-1-1-1-3-2-5z" class="K"></path><path d="M529 408h2v1l-2 2c0-1 0-1-1-1v-1l1-1z" class="h"></path><path d="M525 403h2c0 1 0 1 1 1h1v3h-1v-1h-2c-1-1-1-2-1-3z" class="z"></path><path d="M459 356h7 16 2 0v8 11 17c0 1 0 1 1 1h0c1 0 1 1 2 0v2c-1 1-1 1-2 1v1h-1v2 1 1c-1 1-1 2-1 3v1h0v-1l-1-1c-1-2-3-4-2-6l-2-1v1 1h-1c0-1-1-1-1-1h-1l1-1h0l-1-1v-1l1-1h0c-2 0-3-1-4-1h-3-8-1v-1h0c0-2 0-2-1-3h0v-3-1 1l1-2h-1v-3-1h0c0-1 1-2 0-3 1-1 1-2 1-3v-1-4h-1c0-1 0-1-1-2h-2c1-1 2-2 2-4v-7l1 1z" class="AC"></path><path d="M470 374h1v2h-1c-1 0-1 0-1-1l1-1z" class="o"></path><path d="M463 374h2l1 1-1 1h-1c-1-1-1-1-1-2h0z" class="t"></path><path d="M468 368l2 2v2c-1 0-2 1-3 1l-1-1 2-4z" class="P"></path><path d="M463 365l1-1c1 0 2 0 3 1v2c1 0 1 0 1-1 1 1 1 2 2 2s1 1 2 1c1 1 3 1 4 2h-1v2h-1v-1h-2-1 0v-2h-1l-2-2h0c-1 0-1 0-1-1-1 0-2-1-3-2h-1z" class="k"></path><path d="M459 356h7v1h1 0v1c1 0 1 1 1 1v1 1-1h-1c0 1 1 2 2 3h-1-1v1h1 2 0l1 2v2h-1c-1 0-1-1-2-2 0 1 0 1-1 1v-2c-1-1-2-1-3-1l-1 1h0c0 2 0 2 1 4h1c0 1 1 1 1 1-1 1-3 1-4 1v2h-2v-1-4h-1c0-1 0-1-1-2h-2c1-1 2-2 2-4v-7l1 1z" class="a"></path><path d="M458 355l1 1c0 4 1 8 1 12h-1c0-1 0-1-1-2h-2c1-1 2-2 2-4v-7z" class="T"></path><path d="M460 383v-1-4h0 7 4l4 1h1v2h1v3c-1 1 0 2 0 3 0 2-1 3 0 4v1l-1 1h0c-2 0-3-1-4-1h-3-8-1v-1h0c0-2 0-2-1-3h0v-3-1 1l1-2z" class="AE"></path><g class="a"><path d="M466 383v-1h0 3v1c-1 0-1 0-1 1v1h-1c0-1 0-1-1-2z"></path><path d="M463 385c1 0 1 1 2 1s1 1 2 1c0 1 0 2-1 2h-1s0-1-1-2h0l-1-1v-1z"></path></g><path d="M460 383v-1-4h0 7 4l-1 2 2 2h-2v5-2-1s-1-1-1-2h-3 0v1h-2 0c0 1 0 1-1 1v1 1h-2c-1 0-1-1-2-1l1-2z" class="M"></path><path d="M467 378h4l-1 2c-1-1-1 0-2 0s-1 0-1-1v-1z" class="G"></path><path d="M471 378l4 1h1v2h1v3c-1 1 0 2 0 3 0 2-1 3 0 4v1l-1 1h0c-2 0-3-1-4-1h-3l-2-1h1c0-1 1-1 2-1v1l1-1-1-1 1-1-1-1v-5h2l-2-2 1-2z" class="I"></path><path d="M473 383h1c0 1-1 2-2 2h-1c0-1 1-1 2-2zm-7-27h16 2 0v8 11 17c0 1 0 1 1 1h0c1 0 1 1 2 0v2c-1 1-1 1-2 1v1h-1v2 1 1c-1 1-1 2-1 3v1h0v-1l-1-1c-1-2-3-4-2-6l-2-1v1 1h-1c0-1-1-1-1-1h-1l1-1h0l-1-1v-1l1-1h0 0l1-1v-1c-1-1 0-2 0-4 0-1-1-2 0-3v-3h-1v-2h-1l1-1v-1-1l1-1-1-2v-2c-1-1-3-1-4-2-1 0-1-1-2-1h1v-2l-1-2h0-2-1v-1h1 1c-1-1-2-2-2-3h1v1-1-1s0-1-1-1v-1h0-1v-1z" class="G"></path><path d="M481 382h0c1 1 0 1 0 2h-1c0-1 0-1 1-2z" class="d"></path><path d="M476 377c1 0 2 0 2-1 1 1 1 1 1 3h-3-1l1-1v-1z" class="K"></path><path d="M477 381v-1c0 1 0 0 1 1v4c-1 1 1 3 0 5v1h-1c-1-1 0-2 0-4 0-1-1-2 0-3v-3zm1-14l1-1c0 1 0 2-1 3v2 1l1 1c0 1 0 1-1 2v1c0 1-1 1-2 1v-1l1-1-1-2v-2c-1-1-3-1-4-2-1 0-1-1-2-1h1 1l2-2h1l1 1h2z" class="C"></path><path d="M476 367h2c0 1-1 1-2 2v-2z" class="L"></path><path d="M474 366h1l1 2h-3-1l2-2z" class="K"></path><path d="M484 364v11 17c0 1 0 1 1 1h0c1 0 1 1 2 0v2c-1 1-1 1-2 1v1h-1v2 1 1c-1 1-1 2-1 3v1h0v-1l-1-1c-1-2-3-4-2-6l-2-1v1 1h-1c0-1-1-1-1-1h-1l1-1h0l-1-1v-1l1-1h0 0l1-1v-1h1v-1c2 0 3 0 5-1-1 0-1 0-1-1l2-1v-23z" class="s"></path><path d="M479 394c2 0 3 1 4 1v2h-1v1h1c0 2-1 2 0 3v3l-1-1c-1-2-3-4-2-6h0c0-1 0-1-1-2v-1z" class="g"></path><path d="M478 391l1 1-1 1h0l1 1v1c1 1 1 1 1 2h0l-2-1v1 1h-1c0-1-1-1-1-1h-1l1-1h0l-1-1v-1l1-1h0 0l1-1v-1h1z" class="C"></path><path d="M478 391l1 1-1 1h-2l1-1v-1h1z" class="L"></path><path d="M478 397h0c-1-2-1-2-1-4h1l1 1v1c1 1 1 1 1 2h0l-2-1v1z" class="N"></path><path d="M483 389c-1 0-1 0-1-1l2-1c0 3 0 6-1 8-1 0-2-1-4-1l-1-1h0l1-1-1-1v-1c2 0 3 0 5-1z" class="e"></path><path d="M478 390c2 0 3 0 5-1-1 1-1 2-2 2 0 1-1 1-2 1l-1-1v-1z" class="E"></path><path d="M466 356h16s0 1-1 2v9 1c-1-1-1-4-1-5h0l-1 1v1 1l-1 1h-2l-1-1h-1l-2 2h-1v-2l-1-2h0-2-1v-1h1 1c-1-1-2-2-2-3h1v1-1-1s0-1-1-1v-1h0-1v-1z" class="d"></path><path d="M478 361h0v2h-1s0 1-1 0c0-1 1-1 2-2z" class="K"></path><path d="M471 366h3c1-1 1-2 3-2h1c-1 2-2 2-4 2l-2 2h-1v-2z" class="e"></path><path d="M517 356h3l1 3c3 0 5 0 8 1l7 1h4c1 1 2 1 3 2h0 3 1v1c0 1 1 1 2 2h0c-1 1-1 1-1 2 1 0 1 0 2 1 0 0-1 1-1 2h0c1 4 0 8 1 11v9c1 1 1 1 1 2l-1 1c-2 0-5-1-7-2-2 0-4 1-5 0h0c0 2 1 2 1 4 0 0 0-1-1-2-2 2-1 1-3 0h-2-1-1c-1 1 0 1 0 2h-3v-2h-2c-1 1-1 1-2 1h0-1-1-1c0-1-1-1-2-1 0 0-1 0-1-1h0v-1-2c-1 0-1-2-1-3v2l-1 1c0-1-1-2-1-3h-1c-1 0-1 0-2 1l-1-1v1c-1-1-2-1-3-2 0-1-1-3-2-4v1h-1c0-1 0-2-1-3l1-1v-1-1-1-1h1v1h1v-3l-1-1c1 0 1-1 1-2h-3c-2-1-3-1-5-3v-3s0-1-1-1v-1c-1 0 0 0-1-1 0-1-1-2-1-2v-1-1h0 5c1 1 3 3 3 4 1 1 1 1 1 2l1-1c0-1-1-2-2-3 0-1-1-1-1-2h7 6 1s-1 0-1-1h1z" class="I"></path><path d="M534 362h4v1h-1c-1 1-1 1-2 1l-1-1h-1l1-1z" class="E"></path><path d="M533 389l1-1v-1h1 0l2 2h0c1 0 2 1 2 1h0c-1 0-4-1-6-1z" class="G"></path><path d="M518 371c2 0 5 0 7 1 1 1 1 1 2 1h0c3 0 5 0 8 1l1 1h-4c0 1 1 1 1 2 2 0 3 0 5 1h1s1 0 0 1v2h1 2v1 2h-2c1 0 1 1 2 1 0 1 0 1-1 2-1 0-1 0-1-1-1 0-1-1-2-1l-2-2-3-2-1 1v2l-1 1-1-1h0c0-1 0-1-1-2 0 0-1-1-2-1h-5c1 1 2 1 2 1 1 1 1 2 1 3l-1 1c-1-1-1-1-1-2h-1 0c1 1 1 1 1 2h-1v1h0l-5-1v-6-1l1-5v-1-2z" class="L"></path><path d="M527 381v-1h2c0 1 1 1 1 1 1 0 1 0 2 1l-2 2c0-1 0-1-1-2 0 0-1-1-2-1zm0-8c3 0 5 0 8 1-1 0-1 1-2 1h-1v1c-1-1-2-1-2-2h-3v-1z" class="AE"></path><path d="M517 380h2v1l-1 1v1h1c1 1 2 3 3 4l-5-1v-6zm15-2c1 0 3 0 3 1l2 1c-1 1-1 1-2 1 1 1 1 1 1 2l-3-2c-1-1-1-2-1-3z" class="e"></path><path d="M535 379l2 1c-1 1-1 1-2 1l-2-2h0 2z" class="M"></path><path d="M532 378c-3 0-7 0-9-2h0 1 2s0 1 1 1h5 0 3c1 1 1 1 2 1 1 1 1 0 2 1v1l-1 1c-1 0-1 0-1-1l-2-1c0-1-2-1-3-1z" class="C"></path><path d="M507 376l2-1 1 1c1 0 2 0 3 1h1c1 0 1 0 2 1 0 0 1 0 1 1v1 6l5 1h0l11 2c2 0 5 1 6 1l11 2v2c-2 0-5-1-7-2-2 0-4 1-5 0h0c0 2 1 2 1 4 0 0 0-1-1-2-2 2-1 1-3 0h-2-1-1c-1 1 0 1 0 2h-3v-2h-2c-1 1-1 1-2 1h0-1-1-1c0-1-1-1-2-1 0 0-1 0-1-1h0v-1-2c-1 0-1-2-1-3v2l-1 1c0-1-1-2-1-3h-1c-1 0-1 0-2 1l-1-1v1c-1-1-2-1-3-2 0-1-1-3-2-4v1h-1c0-1 0-2-1-3l1-1v-1-1-1-1h1v1h1z" class="R"></path><path d="M504 380l1-1v-1-1-1-1h1v1 2l1 1v-1h2l2 1c1 1 1 2 1 3h1s1 0 1 1h1c0 1 1 1 1 2v1 1h-1-1c-1 0-1 0-2 1l-1-1v1c-1-1-2-1-3-2 0-1-1-3-2-4v1h-1c0-1 0-2-1-3z" class="S"></path><path d="M512 384l2 3c-1 0-1 0-2 1l-1-1v-1c0-1 0-1 1-2z" class="K"></path><path d="M511 386c-1 0-1-1-1-1 0-1-1-1-1-2s-1-1-1-2h-1v-1c2 1 4 3 5 4-1 1-1 1-1 2z" class="c"></path><path d="M517 387c3 0 7 1 10 2l16 3c-2 0-4 1-5 0h0c0 2 1 2 1 4 0 0 0-1-1-2-2 2-1 1-3 0h-2-1-1c-1 1 0 1 0 2h-3v-2h-2c-1 1-1 1-2 1h0-1-1-1c0-1-1-1-2-1 0 0-1 0-1-1h0v-1-2c-1 0-1-2-1-3z" class="h"></path><path d="M528 394c1 0 3-1 4 0h-1c-1 1 0 1 0 2h-3v-2h0z" class="f"></path><path d="M518 393l2-1v-1h-2c0-1 1-2 1-3l1 1h1c1 0 1 1 2 1h1s1 1 1 2c1 0 2-1 2-1h3l1 2h-3v1h0-2c-1 1-1 1-2 1h0-1-1-1c0-1-1-1-2-1 0 0-1 0-1-1h0z" class="Q"></path><path d="M496 357h5c1 1 3 3 3 4 1 1 1 1 1 2l1-1c0-1-1-2-2-3 0-1-1-1-1-2h7 6 1c0 1-1 2-2 3h-1l1-2h0-1c-1 1-1 1-1 2-1 0-3 0-4 1 1 1 1 1 2 0h1c1 0 3 0 5-1 1 1 1 1 1 2v2h0v2c-1 1-2 0-3-1-1 0-1-1-1-2l-2 1v1c1 0 6 3 6 4s0 1-1 2h1v2 1l-1 5c0-1-1-1-1-1-1-1-1-1-2-1h-1c-1-1-2-1-3-1l-1-1-2 1v-3l-1-1c1 0 1-1 1-2h-3c-2-1-3-1-5-3v-3s0-1-1-1v-1c-1 0 0 0-1-1 0-1-1-2-1-2v-1-1h0z" class="T"></path><path d="M504 359c0-1-1-1-1-2h7v1h2v1c-1 0-3 1-4 1h-1c-1-1-1-1-2-1h-1z" class="B"></path><path d="M496 357l1 1h0c0 1 1 2 1 3 1-1 1-1 1-2h-1v-1h2c2 2 3 3 4 5l1 2c1 1 2 2 2 3l1 1-1 1h0-3c-2-1-3-1-5-3v-3s0-1-1-1v-1c-1 0 0 0-1-1 0-1-1-2-1-2v-1-1z" class="g"></path><path d="M507 368l-2-1c-2-1-3-1-4-2 1-1 1-1 1-2l2 2h1c1 1 2 2 2 3z" class="y"></path><path d="M498 362h3 1c-1 1-2 2-2 3v1c2 1 6 2 7 4h0-3c-2-1-3-1-5-3v-3s0-1-1-1v-1z" class="s"></path><path d="M509 369c0-2-1-2 0-3s1 0 2 0c2 2 2 5 4 7h3v1l-1 5c0-1-1-1-1-1-1-1-1-1-2-1h-1c-1-1-2-1-3-1l-1-1-2 1v-3l-1-1c1 0 1-1 1-2h0l1-1h1z" class="m"></path><path d="M509 369c0 2-1 2 1 4v1 1h0c2 0 2 0 3 1v-1c0-2-1-2-2-4 0-1-1-2-1-4h0l3 6c0 1 1 2 2 4h1v-1l-1-1v-1h3l-1 5c0-1-1-1-1-1-1-1-1-1-2-1h-1c-1-1-2-1-3-1l-1-1-2 1v-3l-1-1c1 0 1-1 1-2h0l1-1h1z" class="u"></path><path d="M520 356h47 5c1 1 1 1 1 2s-1 1-1 2h0l1 1v3c0 1-1 2-1 3 1 1 1 1 1 2v1c0 1-1 1-1 2l1 1v3 4 1 6c1 1 0 2 0 3v6 1 2 2 1c0 1-1 2-2 3v1c-1 1-2 1-2 2l1 2-1 1-1 1-1-1h-1l-2-1c-1 0-1 1-2 2l-2-2-1-1h-2 0v-2h-1c-1 1-1 2-1 3h-2 0v1c0 1-1 2-1 2-1 0-1 0-2-1 0-1 1-1 2-2l-2-1c-1 1-1 3-2 5h1l2 1v2h0 2c1 1 1 1 1 2l-1 1c-1 0-1 1-2 2s-2 1-3 2c0-1-1-1-2-1-1-1-2-1-3-1h0c-2 0-3 0-5-1h0c-1 0-1 0-1-1h-1c0 1-1 1-1 2l-1-1v-1l-1-2h1v-2c-1 1-2 1-3 0 1-1 0-1 0-2 2 0 2-1 3-1 1-1 2-2 2-3 1 0 2-1 2-2l2-2v-4c1-2 0-4-1-6 0-2-1-2-1-4h0c1 1 3 0 5 0 2 1 5 2 7 2l1-1c0-1 0-1-1-2v-9c-1-3 0-7-1-11h0c0-1 1-2 1-2-1-1-1-1-2-1 0-1 0-1 1-2h0c-1-1-2-1-2-2v-1h-1-3 0c-1-1-2-1-3-2h-4l-7-1c-3-1-5-1-8-1l-1-3z" class="V"></path><path d="M542 407l1-1h0c0 1 0 1-1 2v-1z" class="Y"></path><path d="M545 396h1s0 1 1 1l-1 1h-2c0-1 1-2 1-2z" class="U"></path><path d="M573 376v4l-2-1v-1h1v-1h1v-1z" class="Z"></path><path d="M540 406c0 1 0 1 1 1h1v1h0-3v1l-1-1 2-2z" class="Q"></path><path d="M572 382l-1-1s-1-1-1-2h1l2 1v1h0l-1 1z" class="v"></path><path d="M555 401v1 1c1 1 0 2 0 3v1-1h-1v-2h0c-1-1-1-1-1-2 1 0 1 0 2-1z" class="Z"></path><path d="M573 381v4c-1 1-1 1-3 1 0-2 1-3 2-4l1-1z" class="K"></path><path d="M557 392l1-1 1 1c0 1 0 2-1 4h-1v-1h0c-1-1-1-2 0-3z" class="Z"></path><path d="M566 411c0-1 0-2 1-3h2l1 2-1 1-1 1-1-1h-1z" class="b"></path><path d="M567 366c0-1 1-1 1-2h0 1c0 1 0 2 1 2v1h-1s0 1-1 1v-1l-2 1c-1-1-3-1-4-2 2 0 2-1 3-1l2 1z" class="C"></path><path d="M567 366c0-1 1-1 1-2h0 1c0 1 0 2 1 2l-1 1c-1 0-1-1-2-1z" class="Z"></path><path d="M569 364h4c0 1-1 2-1 3 1 1 1 1 1 2-2 0-3 0-5-1 1 0 1-1 1-1h1v-1c-1 0-1-1-1-2z" class="AC"></path><path d="M553 402l-2-2h-1c0-1-1-1-1-3 0 0 1 0 1-1h1c-1 1-1 1-1 2 1 1 1 1 2 1 0 0 0-1 1-1 0-1 0 0 1-1 0 1 1 1 1 2v1 1c-1 1-1 1-2 1z" class="H"></path><path d="M552 410v-3c-1 0-2 0-3-1 0-1 0-3 1-5 1 1 0 3 0 4l3 3v2h0v1c0 1-1 2-1 2-1 0-1 0-2-1 0-1 1-1 2-2z" class="U"></path><path d="M551 422l-1-1v-1c-1 0-1 1-1 1 0-1 0-3-1-4h0c1-1 2 0 3 0h0 2c1 1 1 1 1 2l-1 1c-1 0-1 1-2 2z" class="F"></path><path d="M536 410c1 0 2-1 2-2l1 1c0 1 0 1-1 2h1 1c1 0 1 1 0 2 0 1 0 1-1 2h0 0c1 1 0 1 0 2h0c0 1-1 3-1 4h0c-1 0-1 0-1-1h-1c0 1-1 1-1 2l-1-1v-1l-1-2h1v-2c-1 1-2 1-3 0 1-1 0-1 0-2 2 0 2-1 3-1 1-1 2-2 2-3z" class="U"></path><path d="M538 411h1c0 1 0 2-1 3l-1-1c0-1 0-1 1-2z" class="X"></path><path d="M534 416v1c1 0 1 0 1 1 1 0 3-1 4-2v1c0 1-1 3-1 4h0c-1 0-1 0-1-1h-1c0 1-1 1-1 2l-1-1v-1l-1-2h1v-2z" class="q"></path><path d="M520 356h47c-1 1-2 2-3 2v-1c-2-1-4-1-6 0v2c1 1 1 1 2 1l1 1-2 2h1l1 1h-1v3l-1 1-1-1v2h1c1 0 1 1 2 2-1 1 0 2-1 2h-2l-1-1h-1-1c-1-1-2-1-3-1-1 1-2 1-2 1 0 3 1 7 0 10-1-3 0-7-1-11h0c0-1 1-2 1-2-1-1-1-1-2-1 0-1 0-1 1-2h0c-1-1-2-1-2-2v-1h-1-3 0c-1-1-2-1-3-2h-4l-7-1c-3-1-5-1-8-1l-1-3z" class="H"></path><path d="M555 358c0-1 1-1 2-1 0 1 0 3-1 4v1c-1 0-1 0-2-1 0 0-1-1-1-2l2-1z" class="Y"></path><path d="M554 361l-1 1h-2c-1-1-1-2-1-3 2-1 3-1 5-1l-2 1c0 1 1 2 1 2z" class="k"></path><path d="M552 371c-1 0-1-1-1-2h0v-2l1-1c0-1 0-1-1-2v-1c1 0 2 0 2 1 1 1 1 2 2 3l-1 2s1 0 1 1v1h1c1 0 0 0 0 1h-1c-1-1-2-1-3-1z" class="M"></path><path d="M490 275c0 1 1 1 1 1h1l1 1c1 0 1 0 0 1 1 1 1 1 2 1h3c-3 3-5 6-7 10l-1 3h0l-1 5v3 1c2 1 5 0 6 2v1c-1 0-1 1-1 2-1 0-1 0-2 1 0 1 0 2 1 3 1 0 1 0 1 1h0c2 1 6 0 8 0h16 7c3 1 6 0 9 0 2 0 4 1 6 1 3 0 8 0 11-1v-1h6 0 2 2 0 2c1-1 2-1 3-1h1c2 0 3-1 4-1 0 2 0 2 1 3v32 9c0 1-1 3 0 4h-5-47-3-1c0 1 1 1 1 1h-1-6-7c0 1 1 1 1 2 1 1 2 2 2 3l-1 1c0-1 0-1-1-2 0-1-2-3-3-4h-5-3v3l-1-1c0-1 0-1-1-2h-2c-2 0-3 0-5-1h0-2-16-7l-1-1v7h0c-5-1-11-1-17-1-2-1-4-1-5-1l-1-1v-11-4l-1 2-1-1v-5-1h0v-1-3c1-1 1-3 1-4h0l1-2v-2h-1c-1-1-1-1-2-1-1-1-1-1-1-2 0 0 1-1 1-2v-1s-1-1-2-1h0 0c0-1 1-2 1-2l-1-2 1-1h4c1-1 2-1 2-3 0-1 0-1 1-2h0c0-1 0-1-1-1h0c0-2 1-2 2-3h0c1-2 2-3 2-4h-1-1c1-2 2-3 3-5v-1h-1v-3c1-2 1-1 0-3 1-1 1-1 1-2v-1-3-1-3-1h2l2-1h2 3 5l11-1h9c0 1 1 0 1 0 1 0 2 0 2 1 1-1 1-1 1-2l1 1h1 4c2 0 3-1 4-2z" class="D"></path><path d="M561 310h2c1-1 2-1 3-1l-2 1v1h2l-5 1v-2z" class="U"></path><path d="M566 309h1c2 0 3-1 4-1 0 2 0 2 1 3h-6-2v-1l2-1z" class="X"></path><path d="M551 311v-1h6 0 2 2 0v2h-8c0-1-1-1-2-1z" class="h"></path><path d="M484 356h8l1-12h0v12c2 0 23-1 24 0h-1c0 1 1 1 1 1h-1-6-7c0 1 1 1 1 2 1 1 2 2 2 3l-1 1c0-1 0-1-1-2 0-1-2-3-3-4h-5-3v3l-1-1c0-1 0-1-1-2h-2c-2 0-3 0-5-1h0z" class="N"></path><g class="Y"><path d="M494 311h0c2 1 6 0 8 0h16 7c3 1 6 0 9 0 2 0 4 1 6 1 3 0 8 0 11-1 1 0 2 0 2 1v30h-41c-6 0-13-1-19 0 1-2 0-4 0-6v-19-6h1z"></path><path d="M490 275c0 1 1 1 1 1h1l1 1c1 0 1 0 0 1 1 1 1 1 2 1h3c-3 3-5 6-7 10l-1 3h0l-1 5v3 1c2 1 5 0 6 2v1c-1 0-1 1-1 2-1 0-1 0-2 1 0 1 0 2 1 3 1 0 1 0 1 1h-1v6 19c0 2 1 4 0 6h-7-28v8 4 1 7h0c-5-1-11-1-17-1-2-1-4-1-5-1l-1-1v-11-4l-1 2-1-1v-5-1h0v-1-3c1-1 1-3 1-4h0l1-2v-2h-1c-1-1-1-1-2-1-1-1-1-1-1-2 0 0 1-1 1-2v-1s-1-1-2-1h0 0c0-1 1-2 1-2l-1-2 1-1h4c1-1 2-1 2-3 0-1 0-1 1-2h0c0-1 0-1-1-1h0c0-2 1-2 2-3h0c1-2 2-3 2-4h-1-1c1-2 2-3 3-5v-1h-1v-3c1-2 1-1 0-3 1-1 1-1 1-2v-1-3-1-3-1h2l2-1h2 3 5l11-1h9c0 1 1 0 1 0 1 0 2 0 2 1 1-1 1-1 1-2l1 1h1 4c2 0 3-1 4-2z"></path></g><path d="M433 339v1c1 0 1 1 1 2h1c1 0 6-1 8 0h-7c-1 1-1 1-1 2l-1 2-1-1v-5-1z" class="V"></path><path d="M457 310l1 1v1c0 3 0 7 1 11v12h-1v-8-15l-1-1v-1z" class="N"></path><path d="M444 359h3 1v-4-7-4 15l7 1h0 3v-6 1 7h0c-5-1-11-1-17-1-2-1-4-1-5-1l-1-1h9z" class="b"></path><path d="M489 307c1 0 2-1 2-1l1 1c0 1 0 2 1 3 1 0 1 0 1 1h-1v6 19c0 2 1 4 0 6h-7 6v-31h-14c3-1 6-1 9-1 1 0 2 1 3 0l-1-3z" class="f"></path><path d="M439 306l1 1v1h1v1h0c1-1 2-1 3-2l1 3h0c1 1 2 1 3 1h-8c-2 2-3 7-3 9-1 2-2 5-2 7h-1c-1-1-1-1-2-1-1-1-1-1-1-2 0 0 1-1 1-2v-1s-1-1-2-1h0 0c0-1 1-2 1-2l-1-2 1-1h4c1-1 2-1 2-3 0-1 0-1 1-2h0c0-1 0-1-1-1h0c0-2 1-2 2-3z" class="K"></path><path d="M430 320l2-2 1 1-1 2s-1-1-2-1h0z" class="t"></path><path d="M432 318l3-2v3 1c-1 0-1 0-1-1h-1l-1-1z" class="AA"></path><path d="M441 309h0c1-1 2-1 3-2l1 3c-2-1-4 0-6 0v-1h2z" class="G"></path><path d="M430 316l1-1h4v1l-3 2-2 2h0c0-1 1-2 1-2l-1-2z" class="r"></path><path d="M459 335c2 0 20 0 21 1v2h0-15-19c-3 0-6 0-9-1v-2h6 11c1 0 2 1 4 0h0 1zm-11 9l1-2h6c0 6-1 12 0 17v1h0l-7-1v-15zm-5-2h1v7 10h-9v-11-4c0-1 0-1 1-2h7zm11-54c1 0 3-1 5-1h0c0 1 0 1 1 1v-2h7l-3 17-1 8h-4c-1 1 0 2 0 4v8c-1-4-1-8-1-11v-1l-1-1v1h-7-2c-1 0-2 0-3-1h0l-1-3c-1 1-2 1-3 2h0v-1h-1v-1l-1-1h0c1-2 2-3 2-4h-1-1c1-2 2-3 3-5v-1h-1v-3c1-2 1-1 0-3 1-1 1-1 1-2 1 0 2 0 4 1l8-1z" class="D"></path><path d="M457 310c-1 0-2-1-2-1-2-2-3-5-3-7 1-3 2-5 4-7 2-1 4-2 6-2 1 0 2 0 2 1h0-1c-2 0-4 0-5 1-2 1-3 3-4 5 0 3 0 5 1 7s3 3 4 4c-1 1 0 2 0 4v8c-1-4-1-8-1-11v-1l-1-1z" class="E"></path><path d="M441 293c1-2 1-1 0-3 1-1 1-1 1-2 1 0 2 0 4 1l8-1c-4 3-7 7-7 12v6c0 1 1 3 1 4 1 1 2 1 2 1h-2c-1 0-2 0-3-1h0l-1-3c-1 1-2 1-3 2h0v-1h-1v-1l-1-1h0c1-2 2-3 2-4h-1-1c1-2 2-3 3-5v-1h-1v-3z" class="k"></path><path d="M444 307h2c0 1 0 2 1 3h1c1 1 2 1 2 1h-2c-1 0-2 0-3-1h0l-1-3h0z" class="g"></path><path d="M441 302l1-1h1s1 0 0 2v1l1 1v2h0c-1 1-2 1-3 2h0v-1h-1v-1l-1-1h0c1-2 2-3 2-4z" class="C"></path><path d="M441 293c1-2 1-1 0-3 1-1 1-1 1-2 1 0 2 0 4 1 0 1 0 2-1 4h1c-1 1-2 1-2 2h0 1c-1 1-1 1-2 1l-1 1v-1h-1v-3z" class="a"></path><path d="M490 275c0 1 1 1 1 1h1l1 1c1 0 1 0 0 1 1 1 1 1 2 1h3c-3 3-5 6-7 10l-1 3h0l-1 5v3 1c2 1 5 0 6 2v1c-1 0-1 1-1 2-1 0-1 0-2 1l-1-1s-1 1-2 1l1 3c-1 1-2 0-3 0-3 0-6 0-9 1h0-5-8l1-9-2 1 3-17h-7v2c-1 0-1 0-1-1h0c-2 0-4 1-5 1l-8 1c-2-1-3-1-4-1v-1-3-1-3-1h2l2-1h2 3 5l11-1h9c0 1 1 0 1 0 1 0 2 0 2 1 1-1 1-1 1-2l1 1h1 4c2 0 3-1 4-2z" class="x"></path><path d="M479 287h9l-1 1c-1 0-1 1-2 1h-3-1l-2-2z" class="K"></path><path d="M473 288l6-1 2 2c-1 0-2 1-4 1 0 0-1 0-1 1l-3-3z" class="N"></path><path d="M465 280h5c1 1 7 1 7 1-1 0-3 0-4 1v1h-8-1v-1h2v-1l-1-1z" class="a"></path><path d="M455 280h8 2l1 1v1h-2v1h1l-1 1c-1-1-3-1-5-1h-10c-2 0-4 1-6 0l2-1h0c3-1 7-1 10-2z" class="C"></path><path d="M490 275c0 1 1 1 1 1h1l1 1c1 0 1 0 0 1 1 1 1 1 2 1h3c-3 3-5 6-7 10v-1c-1 0-2 0-3-1h1v-2l-1-1-2-1v-2h-3 0-6s-6 0-7-1h-5-2-8c-3 1-7 1-10 2h0l-2 1s-1 0-1 1v-1-3-1h2l2-1h2 3 5l11-1h9c0 1 1 0 1 0 1 0 2 0 2 1 1-1 1-1 1-2l1 1h1 4c2 0 3-1 4-2z" class="B"></path><path d="M442 280h13c-3 1-7 1-10 2h0l-2 1s-1 0-1 1v-1-3z" class="N"></path><path d="M463 280c2-1 4-1 6-1h16 0c1 1 1 1 1 2h-3 0-6s-6 0-7-1h-5-2z" class="K"></path><path d="M492 276l1 1c1 0 1 0 0 1 1 1 1 1 2 1h3c-3 3-5 6-7 10v-1c-1 0-2 0-3-1h1v-2l-1-1-2-1v-2h-3 3c0-1 0-1-1-2h0 2c1 0 4-1 4-1 1-1 0-1 1-2z" class="C"></path><path d="M489 285c1-3 3-5 4-7 1 1 1 1 2 1-2 3-4 5-6 8h0v-2z" class="P"></path><path d="M492 276l1 1c1 0 1 0 0 1-1 2-3 4-4 7l-1-1-2-1v-2h-3 3c0-1 0-1-1-2h0 2c1 0 4-1 4-1 1-1 0-1 1-2z" class="L"></path><path d="M466 302c0-3 1-13 3-15 1 0 2 0 4 1l3 3c4 7 2 13 2 20h-5-8l1-9z" class="D"></path><path d="M473 301h1l1 1h0c1 0 1 1 1 1v1h-4l-1 1h-1c1-1 2-2 2-3l1-1z" class="W"></path><path d="M488 287h0c1 1 2 1 3 1v1l-1 3h0l-1 5v3 1c2 1 5 0 6 2v1c-1 0-1 1-1 2-1 0-1 0-2 1l-1-1s-1 1-2 1l1 3c-1 1-2 0-3 0-3 0-6 0-9 1h0c0-7 2-13-2-20 0-1 1-1 1-1 2 0 3-1 4-1h1 3c1 0 1-1 2-1l1-1z" class="X"></path><path d="M489 307v-3h1 5c-1 0-1 1-1 2-1 0-1 0-2 1l-1-1s-1 1-2 1z" class="y"></path><path d="M488 287h0c1 1 2 1 3 1v1l-1 3h0l-2-2c-1 0-1-1-2 0l-1-1c1 0 1-1 2-1l1-1z" class="L"></path><path d="M487 288c1 0 2 0 2 1l-1 1c-1 0-1-1-2 0l-1-1c1 0 1-1 2-1z" class="Q"></path><defs><linearGradient id="I" x1="254.313" y1="146.466" x2="125.658" y2="123.502" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292623"></stop></linearGradient></defs><path fill="url(#I)" d="M134 147v-3-7-66l32 53 3-3 3-1h0l1-1 1-1 3-1 1-1 1-1c1 0 2-1 3-1l3-2 3-2 2-1 2-1h1l6-4h0c1 0 2-1 3-1l6-3c2 2 0 6 1 8h3V98c2-1 6-4 9-4v1h1v-1h0l1-1c1 0 1 0 3 1h0 1v-1-1h2 0l1-1c0-1 1-1 2-1h1c1 0 2 0 3-1 0 0 1 0 1 1l1 1c1 0 1 0 2-1v3h1v2h0 2c1-1 0-1 0-2h1 2v2 2l3 3h0c1-1 2-3 3-4v1c0 1-1 3-2 4l-1 2c0 1 0 1 1 2l-1 1v1l2 2v2-2l1 1c0-1 0-1 1-1 0-1 0-1 1-1h0v5c0 2 0 4 1 5v8c-1 6 0 12 0 18v9h-1c1 2 1 3 2 5h0c-1 1-1 3-1 4l1 1c-1 1-1 2-1 3v4 3 2c-1 0-2 0-3 1 1 0 1 1 2 1 0 1 0 1-1 1 0 1-1 1-2 1l-1 2c-1 1-3 2-3 3h0c1 0 1-1 3-1 0 1 0 2-1 2v1c-1 0-3 1-4 1h-6v-1h-1v1h-1-8-1-1-2 0-4-6c-2 0-4 1-6 0v-1h-1 0v1h-6-7c-1 1-2 1-3 1h-11-1-2-2c0-1-1-2-1-3v-2h0c-1-1-1-2-1-3s-1-2-2-3c0-2-2-4-2-5h-1c-3-4-5-5-9-7-3 1-5 1-7 2l-4 4c-1 0-1-1-2-1h-2l-2 1-1-2-2 2-1-1h-1c-1 0-1-1-2-1-1 1-1 2-1 3h0 0l-1-8h0c-1 0-2 0-3-1h-2l-1-1h1v-1h1 0l1-1c0-1 0-2 1-2v-1c0-1 1-2 2-4v-6z"></path><path d="M241 133l1-1v1 1h-1v-1h0zm1 14c0 1 0 1 1 1v1l-1 1h-1v-1h0l1-2z" class="D"></path><path d="M182 150l3 6-1-1c-2-1-2-3-2-5z" class="j"></path><path d="M142 160l-2-2h0c1-1 2-1 3-1v2c0 1 0 1-1 1z" class="O"></path><path d="M174 147h0c1 0 1 0 2-1l1 1-1 1v1h2-1l1 1h0-1v1h1c-1 1-1 1-2 1 0-1-1-2-1-4h0-1v-1z" class="W"></path><path d="M176 152c1 0 1 0 2-1h-1v-1h1l2 2-1 1c0 1 1 1 1 2s0 1-1 2l-2-2 1-1 1-1h-1-1l-1-1z" class="n"></path><path d="M140 165h-1c-1-1 0-1-1-3v-1c0-1 0-1 1-2h1v2h1v1h-1c0 1 1 2 1 2 0 1 0 1-1 1z" class="B"></path><path d="M169 121l1 1v1c-1 1-1 5-1 6h0l-3-5 3-3z" class="m"></path><path d="M182 147c1 2 4 4 5 7l1 1-2 2-1-1-3-6c0-1-1-2-1-2l1-1z" class="K"></path><path d="M193 177s1 0 1-1l-1-1v-1l3 2v-1c1 1 2 3 2 3-1 1-1 2-2 3h-1l-1-1c0-1-1-2-1-3z" class="W"></path><path d="M143 157h-1c-1-1-1-1-1-2h-1v1h0v-1-1h2c-1-1-2-1-3-1v-1-1h2 0v1h1c0-1 0-2-1-3 0-1-1-2-2-3h0c0-1 0-2-1-3v-1h0 1 0c0 1 0 2 1 3 0 1 0 1 1 2l4 7h-1-1v2 1z" class="R"></path><path d="M167 136c1 0 1 1 2 1l1 1v1c0 1 2 2 2 2 1 0 1 1 1 2 1 1 2 2 3 2v1c-1 1-1 1-2 1h0c-2-3-4-5-6-8l-1-3zm84-25v-2l1 1c0-1 0-1 1-1 0-1 0-1 1-1h0v5h0v-4h0-1 0c0 1 0 2-1 2-1 1-1 1-1 2v1l1-1h0 1 0c0 1 0 2-1 2l-3 6c-1 1-2 3-3 5-1-1-1-1-2-1 1-2 1-3 2-4l1-1c1-1 1-1 2-3 0 1 0 1 1 2v-1-1-1c1-1 1-1 1-2l-1-1v-1l1-1z" class="n"></path><path d="M252 148h0c0-2 2-3 3-4v9h-1c-1 1-2 4-3 5 0 1-1 1-1 1h-1 0c-1 1-2 1-3 2 0 1-1 1-2 2l-1-1c1-2 3-4 5-5v-1h-1c1-1 1-1 1-2l1-1h1c1 0 2-2 3-3h0c0-1 0-2 1-2v-1c-1 0-2 0-2 1z" class="W"></path><path d="M206 181v-1l1-1 1-1h1l-1-1v-1c1-1 2-1 2-1h1c0-1 1-1 1-1v-2h2 0l-1 2c1 1 1 2 1 3l1 1c0 1-1 2-2 2 0 1-2 1-2 2v2h-1c-1 0-3-1-4 0-1-1 0-1 0-2v-1z" class="j"></path><path d="M214 177l1 1c0 1-1 2-2 2v-1c0-1-1-1-1-2h2z" class="u"></path><path d="M206 181c1-1 1-1 2-1l2 2h-1-1c0 1 0 1 1 2h1 0c-1 0-3-1-4 0-1-1 0-1 0-2v-1z" class="W"></path><path d="M241 120h0l1 1h1v1h1c0-1 1-1 1-2l1 1c-1 1-1 2-2 4 1 0 1 0 2 1 0 0 0 1-1 1v1l-3 3h-2v-1h0-1v-1h-1l1-1h-3l2-4 1-2 2-2z" class="R"></path><path d="M241 125c1 0 0-1 1-1l1 1h1c1 0 1 0 2 1 0 0 0 1-1 1v1l-3 3h-2v-1h0-1v-1h-1l1-1v-1-1l2-1z" class="B"></path><path d="M240 130h1l1-1c1-1 2-1 3-1l-3 3h-2v-1z" class="R"></path><path d="M241 125c1 0 0-1 1-1l1 1h1c1 0 1 0 2 1 0 0 0 1-1 1h-2v1h-2v1l-1-1c1-1 2-1 2-2l-1-1z" class="J"></path><path d="M221 154l1 1s0 1 1 1h-1c0 1 0 2-1 3 0 1 0 1-1 2 0 0 0 1-1 1 0 1 0 1-1 2l-1 1h2l1 2-1 1v2c-1 1-1 1-3 1l2 2c-1 1-1 2-2 2h0v-1-1c-1-1-1-1-1-2h0 0l-1-2c-2-1-3-1-5-1h2c1 0 2-1 3-1l7-13z" class="W"></path><path d="M214 167v1c1 0 1 0 2-1h1 1c-1 1-2 2-3 2h-1c-2-1-3-1-5-1h2c1 0 2-1 3-1z" class="n"></path><path d="M249 106v1l2 2v2l-1 1v1l1 1c0 1 0 1-1 2v1 1 1c-1-1-1-1-1-2-1 2-1 2-2 3l-1 1-1-1c0 1-1 1-1 2h-1v-1h-1l-1-1 2-3 1-3 4-6 1-2z" class="u"></path><path d="M249 106v1l2 2v2l-1 1c-1 0-1 0-1-1l1-2-2-1 1-2z" class="R"></path><path d="M134 153v-6l1 17 1-1h2v1 1 2h-1v-2c0-1 0-1-1-1h-1v4h1 0c1 0 1 0 2 1h1v-2l-1-1h1 1 2 0c1 1 1 2 2 3l1 1-2 1-1-2-2 2-1-1h-1c-1 0-1-1-2-1-1 1-1 2-1 3h0 0l-1-8h0c-1 0-2 0-3-1h-2l-1-1h1v-1h1 0l1-1c0-1 0-2 1-2v-1c0-1 1-2 2-4z" class="P"></path><path d="M139 170c1-1 1-1 0-3h1 1v2h1l-2 2-1-1z" class="s"></path><path d="M130 161l1-1c0-1 0-2 1-2v-1c0-1 1-2 2-4v4 7c-1 0-2 0-3-1h-2l-1-1h1v-1h1 0z" class="v"></path><path d="M134 157v7c-1 0-2 0-3-1h-2l-1-1h1v-1h1c2 1 3 2 4 2v-6z" class="C"></path><path d="M179 157c1-1 1-1 1-2s-1-1-1-2l1-1c1 3 3 6 5 8 1 2 2 4 4 6 1 2 4 4 5 6v1l2 2v1l-3-2v1l1 1c0 1-1 1-1 1l-2-3c-2-3-4-5-6-8-1-1-2-2-3-4-1-1-2-3-3-5zm-34-3l2 2h0c1 1 1 2 2 2v1h1v2 1h-1 0c0 1 0 1 1 2h-1 0l2 1c1 1 1 1 1 2h1l-4 4c-1 0-1-1-2-1h-2l-1-1c-1-1-1-2-2-3h0-2v-1c1 0 1 0 1-1 0 0-1-1-1-2h1v-1h-1 3l-1-1c1 0 1 0 1-1v-2h0v-1-2h1 1z" class="B"></path><path d="M143 159c1 2 0 2 1 4h-1c-1 0-1-1-2 0 0 1 1 2 2 2 1 1 1 2 2 2l-1 2c-1-1-1-2-2-3h0-2v-1c1 0 1 0 1-1 0 0-1-1-1-2h1v-1h-1 3l-1-1c1 0 1 0 1-1z" class="s"></path><path d="M254 113h0c0 2 0 4 1 5v8c-1 6 0 12 0 18-1 1-3 2-3 4h0 0v1h-1l-2 2-1-1c1 0 1 0 1-1l-1-1v-1c0-1 0-1-1-1-1 1-1 0-2 1-1 0-1 0-2-1v-1-1l-1 1v1h0v-2c-1-1-1-1-1-2v-1c0-3 4-7 5-10 0-1 0 0 1-1l1-1c0-1 1-1 1-2 1 0 1 0 2-1v-1c1 0 1 0 2-1 0-1 0-1 1-2v-9z" class="D"></path><path d="M233 133l3-5h3l-1 1h1v1h1 0v1h2c-1 1-1 1-1 2h0c-1 1-2 3-3 4v1l-12 21v1h-3v1h2c-1 2-5 3-3 5h0c0 1-1 1-2 1l-1-2h-2l1-1c1-1 1-1 1-2 1 0 1-1 1-1 1-1 1-1 1-2 1-1 1-2 1-3h1c-1 0-1-1-1-1l-1-1c1-1 2-3 2-4 1-2 2-3 3-5l6-9c0-1 1-2 1-3z" class="O"></path><path d="M239 130h1c-1 1-1 2-2 3h0v1c-1-1-1-1-1-2v-2h2zm-16 20c1 1 1 2 1 3-1 1-1 2-1 3-1 0-1-1-1-1l-1-1c1-1 2-3 2-4z" class="T"></path><path d="M240 130v1h2c-1 1-1 1-1 2h0c-1 1-2 3-3 4 0-1 0-1 1-2 0-1-1-2-1-2 1-1 1-2 2-3h0z" class="J"></path><path d="M155 137h1v-1-1c-1-1-2-3-2-5v-1-1h-2l1-1-1-1h0c0-1 0-1-1-1v-1l-1-2c1-1 1-1 2-1h3l2 2h1l-1-1h0c-1-1-1-2-2-3s-2-2-3-4h-1c-1-1-3-4-3-6h0l-3-3h1s0 1 1 1c0 1 1 1 1 1 3 5 5 8 8 12 1 1 10 16 11 16l1 3c2 3 4 5 6 8v1h1 0c0 2 1 3 1 4l1 1h1 1l-1 1-1 1 2 2c1 2 2 4 3 5 1 2 2 3 3 4 2 3 4 5 6 8l-2-2c0-1-1-2-2-3-1-2-3-4-5-6h0l-1-1v-1h-1v2c1 0 1 0 1 1s-1 2-1 2c-1 1-3 1-4 0h0v1l1 2v1c1 1 1 1 2 1h2l2 2v1l-2-2h0-3 0c-1-1-2-3-2-4h-1v-2c-1-1-3-2-4-4 0-1-1-2-2-2v-1h0-1c0-2-1-2-1-3-1-2-2-4-3-5s-2-2-2-4v-1l-1-1c-1-2-2-3-3-5-1-1-1-2-2-2 0-1-1-1-1-1h0z" class="D"></path><path d="M249 159h1s1 0 1-1c1-1 2-4 3-5 1 2 1 3 2 5h0c-1 1-1 3-1 4l1 1c-1 1-1 2-1 3v4 3 2c-1 0-2 0-3 1 1 0 1 1 2 1 0 1 0 1-1 1 0 1-1 1-2 1l-1 2c-1 1-3 2-3 3h0c1 0 1-1 3-1 0 1 0 2-1 2v1c-1 0-3 1-4 1h-6v-1h-1v1h-1-8-1-1-2 0-4-6c1-1 3-4 3-6 1-1 1-2 2-4l1 1c1-1 2-2 3-2h1l2-2h-1c2-2 6-5 7-7h1 1 1v-1c2 1 1 2 2 3l1-1 1 1c1 0 2 0 3 1h1l1-1c0-1 1-2 1-3l1 1 2-2v-2c1 0 1-1 1-1-1 0-3 2-4 3s-1 1-2 0c0-1 1-1 1-2l4-4z" class="T"></path><path d="M238 182h1 1l-2 2h0c-1-1 0-1 0-2z" class="O"></path><path d="M238 184h-1-3v-1-1c1 1 3 0 4 0 0 1-1 1 0 2z" class="S"></path><path d="M223 181l1-1h0l2 2v1c0 1-1 1-2 2h0l-1-1c-1 1-1 1-2 1l3-3-1-1zm6 6h2v-1-1h2 1 1 0c1 0 1 1 2 2h-8z" class="B"></path><path d="M241 170h1l1 1h2c0 1-2 2-3 3 0 2-1 3-3 4h-1 0c1-1 2-2 3-4 1 0 2-1 2-3-1 1-2 0-2 0v-1zm4 5l1-1v3h-2c1 1 1 1 1 2h-1-1c-1 0-1 0-2 1l-1 1h-1c1-1 1-3 2-3h1c1 0 2-3 3-3z" class="J"></path><path d="M229 174v-1c1-1 3-1 5-1v3c-1 1-2 1-3 2l-1 1c0-1 0-2-1-2v-2z" class="u"></path><path d="M229 174h3 0c-1 0-2 1-3 2v-2z" class="J"></path><path d="M252 176v-3c-1-3 0-4 0-7h3v4 3 2c-1 0-2 0-3 1z" class="l"></path><path d="M227 174v1 1 1h1l2 3v1c-1-1-1-1-2-1s-1 1-2 1v-1l1-2s-1 1-2 1v1h-1l-1 1 1 1-3 3c1 0 1 0 2-1l1 1s1 1 1 2h-4-6c1-1 3-4 3-6 1-1 1-2 2-4l1 1c1-1 2-2 3-2h1l2-2z" class="O"></path><path d="M221 183v-1-1h-1l1-1c1 0 1-1 2-1h1l1 1h-1l-1 1h-1v2h-1z" class="J"></path><path d="M223 181l1 1-3 3v1h-1c0-2 0-2 1-3h1v-2h1z" class="y"></path><path d="M139 142v-1c-1-1-1-3-1-4s1 0 2-1v-1c1 0 1-1 2-1v2l1-1c0-2 0-4-1-6-1-1-1-1-1-2v-4h1v-2c0-1 2-1 2-3h0v1c0 1 1 1 1 1v-2-1-2c-1-1-1-2-1-3v-3h0v2c1 2 2 4 2 7 0 0 0 1 1 2h0c0 1 0 1 1 2s2 3 3 5v1c0 2 2 2 2 4v1l1 1v2l1 1h0s1 0 1 1c1 0 1 1 2 2 1 2 2 3 3 5l1 1v1c0 2 1 3 2 4s2 3 3 5c0 1 1 1 1 3h1 0v1c1 0 2 1 2 2 1 2 3 3 4 4v2h1c0 1 1 3 2 4h0 3 0l2 2c1 1 1 0 1 1 1 1 2 2 3 4h0 0c1 2 3 4 3 6 2 0 2 1 3 2h2c-1 1-2 1-3 1h-11-1-2-2c0-1-1-2-1-3v-2h0c-1-1-1-2-1-3s-1-2-2-3c0-2-2-4-2-5h-1c-3-4-5-5-9-7-3 1-5 1-7 2h-1c0-1 0-1-1-2l-2-1h0 1c-1-1-1-1-1-2h0 1v-1-2h-1v-1c-1 0-1-1-2-2h0l-2-2-4-7c-1-1-1-1-1-2-1-1-1-2-1-3h0z" class="n"></path><path d="M172 168c1 2 2 3 3 4l-1 1c0 1 0 1-1 2v-1-2-2c-1 0-1-1-1-2z" class="R"></path><path d="M176 168c0 1 1 3 2 4h-2c0 1 0 2 1 3v1l-3-3 1-1h0c0-1 1-1 1-2l-1-1 1-1z" class="S"></path><path d="M155 162h-1v-1c0-1-3-5-4-6v-1-1h1 0c1 2 2 4 3 5 2 2 4 4 5 6h0l-4-2z" class="R"></path><path d="M181 172l2 2c1 1 1 0 1 1 1 1 2 2 3 4h0 0l-4 1c-1-1-1-1-1-3h-2v1h0v2l1 1-2 2 1 1v1 1 2h-2-2c0-1-1-2-1-3v-2h0c-1-1-1-2-1-3 2 1 2 1 2 2v-2 1l2 2v-1-4c-1-1-1-1-1-2l2-1v-1h1c0-1 1-1 1-2z" class="B"></path><path d="M180 186v2h-2c0-1-1-2-1-2h3z" class="f"></path><path d="M176 188c0-1-1-2-1-3v-2h0c-1-1-1-2-1-3l3 6s1 1 1 2h-2z" class="z"></path><path d="M181 172l2 2c1 1 1 0 1 1 1 1 2 2 3 4h0l-1-1-1-1h-2-1v-1l1-1v1l1-1c-1-1-2 0-3 0l-1-1c0-1 1-1 1-2z" class="T"></path><path d="M181 181l-1-1v-2h0v-1h2c0 2 0 2 1 3l4-1c1 2 3 4 3 6 2 0 2 1 3 2h2c-1 1-2 1-3 1h-11-1v-2-1-1l-1-1 2-2z" class="R"></path><path d="M181 181v3h-1l-1-1 2-2z" class="p"></path><path d="M184 183h0c0-1 2-2 3-2 0 0 0 1 1 2h0-1c-1-1-1-1-1 0h-2z" class="B"></path><path d="M184 183h2v1l1 1c1-1 1-1 2-1v1h1c2 0 2 1 3 2h2c-1 1-2 1-3 1h-11-1v-2-1h1c1 0 0 1 0 2h1l1-2h0l1-2z" class="z"></path><path d="M184 183h2v1l1 1c1-1 1-1 2-1v1s1 1 1 2h-1l-1-2h-3v1h-1l-1-1h0l1-2zm-25-19h1c0 1 1 1 1 1h1v-1h-1v-2l-1-1-4-5-1-2c0-1-1-1-1-1-1-3-3-5-4-8v-1c-3-4-4-9-6-13 0-2-2-4-2-6l1-1h0c1 1 1 1 1 2 0 2 1 4 2 6s1 4 2 6c3 5 5 11 8 16 2 2 4 5 5 8 1 1 2 3 4 3l3 3c1 1 2 2 2 4h-1c-3-4-5-5-9-7-3 1-5 1-7 2h-1c0-1 0-1-1-2l-2-1h0 1c-1-1-1-1-1-2h0 1v-1c1 0 1 1 2 1h0c1 1 1 2 2 2l1 1 1-2h0c-1 0-1 0-1-1l4 2h0z" class="O"></path><path d="M154 134v2l1 1h0s1 0 1 1c1 0 1 1 2 2 1 2 2 3 3 5l1 1v1c0 2 1 3 2 4s2 3 3 5c0 1 1 1 1 3h1 0v1c1 0 2 1 2 2 1 2 3 3 4 4v2h1l-1 1 1 1c0 1-1 1-1 2h0c-1-1-2-2-3-4h0l-2-4h-1c-1-2-3-4-5-6l-3-5c0-1-1-1-1-2h0l-2-4c0-1 0-1-1-2 0-1-1-2-1-3-1 0-1-1-1-1 0-1-2-2-2-3s1-3 1-4z" class="W"></path><path d="M236 89s1 0 1 1l1 1c1 0 1 0 2-1v3h1v2h0 2c1-1 0-1 0-2h1 2v2 2l3 3h0c1-1 2-3 3-4v1c0 1-1 3-2 4l-1 2c0 1 0 1 1 2l-1 1-1 2-4 6-1 3-2 3h0l-2 2-1 2-2 4-3 5c0 1-1 2-1 3l-6 9c-1 2-2 3-3 5 0 1-1 3-2 4l-7 13c-1 0-2 1-3 1h-2-1c-2 1-3 1-5 1h-6-1c-1 0-1 0-2-1l-8-11 2-2-1-1c-1-3-4-5-5-7l-1 1-2-2c-1-2-1-4-2-5l-1-1h0l-3-4v-2h0v-3h-1c-1 0-2 0-2-1h0v-7-1l-1-1 3-1h0l1-1 1-1 3-1 1-1 1-1c1 0 2-1 3-1l3-2 3-2 2-1 2-1h1l6-4h0c1 0 2-1 3-1l6-3c2 2 0 6 1 8h3V98c2-1 6-4 9-4v1h1v-1h0l1-1c1 0 1 0 3 1h0 1v-1-1h2 0l1-1c0-1 1-1 2-1h1c1 0 2 0 3-1z" class="X"></path><path d="M227 105h1v1l1 2c0 1 1 3 0 5v4h0c-1 0-1 0-1-1h0v-8c0-1-1-2-1-3z" class="b"></path><path d="M227 92h2v10 1h0-1c0-4-1-7-1-11z" class="E"></path><path d="M227 120h1c1 2 1 5 1 7v4c0 1-1 1-1 2v1c0-5-1-10-1-14z" class="U"></path><path d="M232 125l1 1c0 2-1 5 0 7 0 1-1 2-1 3-1-4-1-7 0-11z" class="N"></path><path d="M232 90h1v3 12 1c-1 0-1-1-2-1 0-1 1-2 0-3 0 0 0-1 1-1 0-2-1-3-1-5 1-1 1-2 1-3v-3zm-1 17h2v1h0v4 4c1 1 1 1 0 1v4 5l-1-1c0-1 0-2-1-3 0-2 0-3 1-4-1-2 0-3-1-4v-1h1c-1-2-1-5-1-6z" class="a"></path><path d="M213 161h2 1c-1 2-2 4-4 5l-5 1h0v-2l1-1v-2-1h5z" class="W"></path><path d="M197 158h0c1-1 1-1 2-1v2c0 3-1 6-1 9 1 0 1 0 1 1h2c1 0 1-1 2-1 1-2 3-5 3-8h0l1-1c2 1 5 0 7 0h0c-2 1-3 1-5 1h0l4 1h-5v1 2l-1 1v2h0l-1 1h2c-2 1-3 1-5 1h-6 0c1-3 0-9 0-11z" class="O"></path><path d="M203 155l1-1h2l1 1v3 1l-1 1h0c0 3-2 6-3 8-1 0-1 1-2 1h-2c0-1 0-1-1-1 0-3 1-6 1-9l1-1c1 0 1-1 1-1v-1h0 1c1 0 1-1 1-1z" class="y"></path><path d="M203 155l1-1h2c1 1 1 1 1 2l-1 1h0c-1-1-4-1-5-1h1c1 0 1-1 1-1z" class="b"></path><path d="M203 162c-1-1-2-2-2-3s1-1 1-2h4 0c-1 1-2 2-2 4l-1 1z" class="W"></path><path d="M200 158c1 1 0 8 0 10l-1 1c0-1 0-1-1-1 0-3 1-6 1-9l1-1zm6-4l1 1v3 1l-1 1h0c0 3-2 6-3 8-1 0-1 1-2 1v-1c-1-2 1-5 2-6l1-1c0-2 1-3 2-4l1-1c0-1 0-1-1-2z" class="k"></path><path d="M222 94h1l1 13v28c0 2-1 5 0 7h0v6l-1-1c-1-3-1-6-1-8v-1-2h-1v-2h1c0-2 0-3-1-4 1-3 0-6 0-8V94v1h1v-1h0z" class="G"></path><path d="M222 94h1l1 13-1 1v-1c0-1-1-2-1-4v-9h0z" class="c"></path><path d="M221 122c1 1 0 3 1 3h1c1 1 1 2 1 2 0 3-1 6 0 8 0 2-1 5 0 7h0v6l-1-1c-1-3-1-6-1-8v-1-2h-1v-2h1c0-2 0-3-1-4 1-3 0-6 0-8z" class="N"></path><path d="M215 144h1 0c1 1 1 2 2 2l3 1h2l1 1c-2 2-6 11-8 11h-2c-2 0-5 1-7 0v-1l1-1h0c0-1-1-2 0-3v-1-1l-1-1c1-1 0-2 0-2 1-1 1-1 1-2s1-1 2-1c1-1 3 0 5-2z" class="D"></path><path d="M215 144h1 0c1 1 1 2 2 2l3 1c-4 0-9 1-13 0 0-1 1-1 2-1 1-1 3 0 5-2z" class="H"></path><path d="M204 138h11c2 0 4-1 6 0h0l1 1c0 2 0 5 1 8h-2l-3-1c-1 0-1-1-2-2h0-1c-2 2-4 1-5 2-1 0-2 0-2 1s0 1-1 2c0 0 1 1 0 2h-2-2c-1 0-2 0-3-1 1-1 1-2 2-3h0c-1-3 1-6 2-8v-1h0z" class="O"></path><path d="M207 149h0l-1-1c0-1 0-3 1-4h1c0 1 0 2 1 2h1c-1 0-2 0-2 1s0 1-1 2z" class="C"></path><path d="M204 139h1c2 1 0 8 0 10h0l-1 1c-1 0-1-1-2-1l2-10z" class="G"></path><path d="M208 144h0l-1-1c0-1 0-2 1-2 1-2 5-1 7-1l-1 2h-2l-1 1c1 0 1 1 1 1h3c-2 2-4 1-5 2h-1c-1 0-1-1-1-2z" class="k"></path><path d="M221 138l1 1c0 2 0 5 1 8h-2l-3-1c-1 0-1-1-2-2h0-1-3s0-1-1-1l1-1h2l1-2h5v4 1c1-2 0-4 1-6v-1z" class="X"></path><path d="M236 89s1 0 1 1l1 1c1 0 1 0 2-1v3h1v2h0 2c1-1 0-1 0-2h1 2v2 2l3 3h0c1-1 2-3 3-4v1c0 1-1 3-2 4l-1 2c0 1 0 1 1 2l-1 1-1 2-4 6-1 3-2 3h0l-2 2-1 2-2 4-3 5c-1-2 0-5 0-7v-5-4c1 0 1 0 0-1v-4-4h0v-1h-2v-1-1c1 0 1 1 2 1v-1-12-3c1 0 2 0 3-1z" class="U"></path><path d="M237 121c1 0 2 1 2 1l-1 2c-1-1-1-2-1-3z" class="b"></path><path d="M236 107l1 1v2l1 2v2 2c0 1-1 2-2 2h0v-1-7-3z" class="AD"></path><path d="M243 113v1h1 0l-1 3-2 3h0l-2 2s-1-1-2-1c-1-1-1-1-2-1 0-1 0-2 1-3v1h0c1 0 2-1 2-2v-2c1 1 1 1 3 1l-1-1h1v1h1c1-1 1-1 1-2z" class="L"></path><path d="M241 116l2 1-2 3h0c0-1-1-1-1-1 0-1 1-2 1-3z" class="C"></path><path d="M243 113v1h1 0l-1 3-2-1h0l-2 1-1-1v-2c1 1 1 1 3 1l-1-1h1v1h1c1-1 1-1 1-2z" class="H"></path><path d="M236 102h1v-1h1c0 1 0 3-1 4h3v1 3 1c1 2 2 1 1 4h-1l1 1c-2 0-2 0-3-1v-2l-1-2v-2l-1-1v-5z" class="I"></path><path d="M237 105h3v1 3s0 1-1 1v-1c0-1 0-2-1-3h0l-1-1z" class="Q"></path><path d="M236 102h1v-1h1c0 1 0 3-1 4l1 1-1 4v-2l-1-1v-5z" class="c"></path><path d="M236 89s1 0 1 1 0 2 1 3h0v3 5h-1v1h-1l-1-2c-1-2-1-5-2-7v-3c1 0 2 0 3-1z" class="V"></path><path d="M236 89s1 0 1 1 0 2 1 3h0-2c-1 1 0 1-1 3 0-2 0-5 1-7z" class="c"></path><path d="M235 96c1-2 0-2 1-3h2v3 5h-1v1h-1l-1-2v-4z" class="AD"></path><path d="M237 90l1 1c1 0 1 0 2-1v3h1v2h0 2c1-1 0-1 0-2h1 2v2 2l3 3h0c1-1 2-3 3-4v1c0 1-1 3-2 4l-1 2c0 1 0 1 1 2l-1 1-1 2-4 6h0-1v-1c0 1 0 1-1 2h-1v-1c1-3 0-2-1-4v-1-3-1h-3c1-1 1-3 1-4v-5-3h0c-1-1-1-2-1-3z" class="M"></path><path d="M242 104h1 2l-1 1c1 0 2 0 3-1l-1 2h-1l-1-1v1 2c0 1 0 1-1 1s-1-1-1-2v-1h0v-2z" class="G"></path><path d="M246 97l3 3h0c0 1-1 2-2 3v1c-1 1-2 1-3 1l1-1c1-2 1-5 1-7zm3 6c0 1 0 1 1 2l-1 1-1 2-4 6h0-1v-1c0-2 0-2 2-3 0-2 3-5 4-7z" class="Q"></path><path d="M243 93h1 2v2 2c0 2 0 5-1 7h-2-1l-1-6v-3h0 2c1-1 0-1 0-2z" class="b"></path><path d="M241 95h0c1 1 2 1 2 2v1h-2v-3z" class="X"></path><path d="M244 93h2v2 2c0 2 0 5-1 7h-2c1-1 1-3 1-4h0v-7z" class="C"></path><path d="M237 90l1 1c1 0 1 0 2-1v3h1v2 3l1 6v2h-2v-1h-3c1-1 1-3 1-4v-5-3h0c-1-1-1-2-1-3z" class="H"></path><path d="M240 93h1v2 3l1 6v2h-2v-1-12z" class="e"></path><path d="M212 108V98c2-1 6-4 9-4v28c0 2 1 5 0 8 1 1 1 2 1 4h-1v2h1v2 1l-1-1h0c-2-1-4 0-6 0h-11c-1-1-5-1-6-1v1h-1l-1-3v-1l-1-1h1l2-2c-1-1 0-1 0-2 0 0 1-2 1-3s0-3-1-5c0 0 1-1 1-2v-1-14h0c1 0 2-1 3-1l6-3c2 2 0 6 1 8h3z" class="B"></path><path d="M208 100c2 2 0 6 1 8 1 1 2 1 1 2 0 1 0 1-1 1l-1-1v-10z" class="x"></path><path d="M209 108h3l1 22h-5v-20l1 1c1 0 1 0 1-1 1-1 0-1-1-2z" class="Z"></path><path d="M199 104c1 0 2-1 3-1v4c-1 2 0 6 0 8v13 2h-1-1s-1 0-2-1c0 0 1-2 1-3s0-3-1-5c0 0 1-1 1-2v-1-14h0z" class="q"></path><path d="M199 104l1 1v8c0 3 1 4 0 7l-1-1v-1-14h0z" class="F"></path><path d="M199 119l1 1c0 1 0 2 1 3v-2c0 2 0 5 1 7v2h-1-1s-1 0-2-1c0 0 1-2 1-3s0-3-1-5c0 0 1-1 1-2z" class="i"></path><path d="M199 126h1s1 1 0 2v1 1s-1 0-2-1c0 0 1-2 1-3z" class="S"></path><path d="M199 119l1 1c0 1 0 2 1 3-1 1-1 2-1 3h-1c0-1 0-3-1-5 0 0 1-1 1-2z" class="p"></path><path d="M198 129c1 1 2 1 2 1h1v1h1s1 1 2 1l9 1c2 0 5-1 6 1h2v2h1v2 1l-1-1h0c-2-1-4 0-6 0h-11c-1-1-5-1-6-1v1h-1l-1-3v-1l-1-1h1l2-2c-1-1 0-1 0-2z" class="H"></path><path d="M196 135h2c2 0 4 1 5 1h1c-2 1-4 1-6 1v1h-1l-1-3z" class="Q"></path><path d="M219 134h2v2h-5-13c-1 0-3-1-5-1h5v-1h6l9 1 1-1z" class="F"></path><path d="M198 129c1 1 2 1 2 1h1v1h1s1 1 2 1l9 1c2 0 5-1 6 1l-1 1-9-1h-6v1h-5-2v-1l-1-1h1l2-2c-1-1 0-1 0-2z" class="w"></path><path d="M198 129c1 1 2 1 2 1h1v1h1s1 1 2 1l-6-1c-1-1 0-1 0-2z" class="s"></path><path d="M196 133l1 1h6v1h-5-2v-1l-1-1h1z" class="B"></path><path d="M193 108l6-4v14 1c0 1-1 2-1 2 1 2 1 4 1 5s-1 3-1 3c0 1-1 1 0 2l-2 2h-1l1 1v1l1 3h1v-1c1 0 5 0 6 1h0v1c-1 2-3 5-2 8h0c-1 1-1 2-2 3 1 1 2 1 3 1h2 2l1 1v1 1c-1 1 0 2 0 3h0l-1 1v-3l-1-1h-2l-1 1s0 1-1 1h-1 0v1s0 1-1 1l-1 1v-2c-1 0-1 0-2 1h0c0 2 1 8 0 11h0-1c-1 0-1 0-2-1l-8-11 2-2-1-1c-1-3-4-5-5-7l-1 1-2-2c-1-2-1-4-2-5l-1-1h0l-3-4v-2h0v-3h-1c-1 0-2 0-2-1h0v-7-1l-1-1 3-1h0l1-1 1-1 3-1 1-1 1-1c1 0 2-1 3-1l3-2 3-2 2-1 2-1h1z" class="J"></path><path d="M196 169v-1c0-2-1-9 1-11v1c0 2 1 8 0 11h0-1z" class="N"></path><path d="M188 155c1 2 3 8 4 9 1-1 1-2 1-2l1-1-1-1c0-1 0-1 1-2h0c1 1 1 2 0 3v7l-8-11 2-2z" class="C"></path><path d="M190 151h3v1c1 1 2 1 3 2 2 0 5 0 7 1 0 0 0 1-1 1h-1 0-8-1 0v4h0l-5-8h2l1-1z" class="X"></path><path d="M186 146h6 2 1 0l2 2c0 1-1 3-1 4h-1-2v-1h-3l-1 1h-2l-1-1c-1-2-2-3-3-4h2l1-1z" class="i"></path><path d="M186 151c1-1 2-1 4 0l-1 1h-2l-1-1z" class="x"></path><path d="M195 146l2 2c0 1-1 3-1 4h-1v-3-3z" class="a"></path><path d="M186 146h6 2v1l-1 1h0-3l-1-1c-2 1-2 0-4 0l1-1z" class="k"></path><path d="M177 141c3 1 6 1 9 1h3v-1l1 1c1 1 1 1 2 1l1-1v1l2 1v2h-1-2-6l-1 1h-2l-1-1h-1l1 1-1 1-2-2c-1-2-1-4-2-5z" class="y"></path><path d="M180 144c2 1 4 1 6 2l-1 1h-2l-1-1h-1l1 1-1 1-2-2c1-1 1-1 1-2z" class="N"></path><path d="M177 141c3 1 6 1 9 1h3v-1l1 1c1 1 1 1 2 1l1-1v1l2 1h-6c-3 0-6 0-9-1v1c0 1 0 1-1 2-1-2-1-4-2-5z" class="h"></path><path d="M193 132c1 1 1 1 2 0v1l1 1v1l1 3h1v-1c1 0 5 0 6 1h0v1c-1 2-3 5-2 8h0c-1 1-1 2-2 3 1 1 2 1 3 1h2 2l1 1v1 1c-1 1 0 2 0 3h0l-1 1v-3l-1-1h-2l-1 1c-2-1-5-1-7-1-1-1-2-1-3-2h2 1c0-1 1-3 1-4l-2-2h0v-2l-2-1v-1l-1 1c-1 0-1 0-2-1 1-1 1-1 1-2 0-2 1-3 1-4l1 1v-5z" class="F"></path><path d="M203 151h2 2l1 1c-2 1-4 1-5 1-1-1-2 0-4 0 0-1 1-1 1-2h3z" class="L"></path><path d="M193 132c1 1 1 1 2 0v1l1 1c0 2 0 4-1 6v-2c-1-1-1 0-2 0v-1-5z" class="Q"></path><path d="M192 136l1 1v1c1 0 1-1 2 0v2l1 1h-1v1l-2 1v-1l-1 1c-1 0-1 0-2-1 1-1 1-1 1-2 0-2 1-3 1-4z" class="V"></path><path d="M192 136l1 1v1 4l-1 1c-1 0-1 0-2-1 1-1 1-1 1-2 0-2 1-3 1-4z" class="B"></path><path d="M196 141h0c1-1 1-2 2-2l1 1-1 3c0 1-1 1-1 1v1h1c0 1 0 2-1 3l-2-2h0v-2l-2-1 2-1v-1h1z" class="H"></path><path d="M199 153c-1 0-1 0-1-1s1-3 1-4c0-3 1-6 2-9h3c-1 2-3 5-2 8h0c-1 1-1 2-2 3 1 1 2 1 3 1h-3c0 1-1 1-1 2z" class="M"></path><path d="M179 129l1 1h6 1 2c2 0 2 1 3 2v4c0 1-1 2-1 4 0 1 0 1-1 2l-1-1v1h-3c-3 0-6 0-9-1l-1-1h0l-3-4v-2h0v-3-1h2v1h1 1 0l1-1h0l1-1z" class="p"></path><path d="M185 132h2c0 1-1 2-2 2h0l-1-1 1-1z" class="X"></path><path d="M190 138h1v2c0 1 0 1-1 2l-1-1 1-3z" class="M"></path><path d="M181 132c2-1 2-1 4 0l-1 1 1 1c1 2 0 4 0 6-1 0-1 0-2 1v-1c1-1 1-1 1-3h-1l-1-1c0-1 0-2-1-3h0v-1z" class="Z"></path><path d="M179 129l1 1h6 1 2c2 0 2 1 3 2v4c0 1-1 2-1 4v-2h-1v-3-3c-1-1-2 0-3 0h-2c-2-1-2-1-4 0h-2v-2h-1l1-1z" class="C"></path><path d="M178 130h1v2h2v1h0c1 1 1 2 1 3l1 1v1c0 1-1 1-2 2h-3-2 0l-3-4v-2h0v-3-1h2v1h1 1 0l1-1h0z" class="H"></path><path d="M173 136v-2h0l9 2 1 1v1c-3 0-5-1-8-2h-2z" class="y"></path><path d="M178 130h1v2h2v1h0c1 1 1 2 1 3l-9-2v-3-1h2v1h1 1 0l1-1h0z" class="V"></path><path d="M193 108l6-4v14 1c0 1-1 2-1 2 1 2 1 4 1 5s-1 3-1 3c0 1-1 1 0 2l-2 2h-1v-1c-1 1-1 1-2 0v5l-1-1v-4c-1-1-1-2-3-2h-2-1-6l-1-1-1 1h0l-1 1h0-1-1v-1h-2v1h-1c-1 0-2 0-2-1h0v-7-1l-1-1 3-1h0l1-1 1-1 3-1 1-1 1-1c1 0 2-1 3-1l3-2 3-2 2-1 2-1h1z" class="z"></path><path d="M193 119v-3h1v8c-1 0 0-3 0-4l-1-1z" class="AA"></path><path d="M193 119l1 1c0 1-1 4 0 4v3 2l-2-2 1-8z" class="m"></path><path d="M193 108c-1 1-1 1 0 2s1 2 2 4c0 0 0 1-1 2h-1v3l-1 8h-1c0-2 1-5 1-7v-12h1z" class="AG"></path><path d="M197 120v-1-4c0 1 0 2 1 3v-3c0 1 0 2 1 3v1c0 1-1 2-1 2 1 2 1 4 1 5s-1 3-1 3c0 1-1 1 0 2l-2 2h-1v-1c-1 1-1 1-2 0l-1-1v-1h0 2l1-1h-1v-2c2-3 1-7 1-10h1c0 1 0 2 1 3z" class="AA"></path><path d="M192 131l1-1h4 0c0 1-1 2-2 2-1 1-1 1-2 0l-1-1z" class="X"></path><path d="M197 120v-1-4c0 1 0 2 1 3v-3c0 1 0 2 1 3v1c0 1-1 2-1 2l-1 1v7h0c-2-3-1-6-1-8 0-1 0-1 1-1z" class="q"></path><path d="M190 109l2-1v12c0 2-1 5-1 7h1l2 2h1l-1 1h-2 0v1l1 1v5l-1-1v-4c-1-1-1-2-3-2h-2-1l-1-1v-1h-1l1-12v-4l3-2 2-1z" class="O"></path><path d="M191 119h1v1c0 2-1 5-1 7v2h-1 0l1-10z" class="i"></path><path d="M190 109l2-1v12-1h-1c-1-3 0-6 0-8l-1-1v4-5z" class="q"></path><path d="M188 110l2-1v5l-1 7c0 2-1 7 0 9h-2 0l1-1v-2c-1 0-1 0-1-1l1-1c1-2 0-6 0-9 0-2 1-3 1-5 0 0-1 0-1-1z" class="m"></path><path d="M188 110c0 1 1 1 1 1 0 2-1 3-1 5 0 3 1 7 0 9l-1 1c0 1 0 1 1 1v2l-1 1h0-1l-1-1v-1h-1l1-12v-4l3-2z" class="z"></path><path d="M185 128h0v-3c0-2 1-7 2-9v10 1c-1 1 0 1 0 2v1h0-1l-1-1v-1z" class="o"></path><defs><linearGradient id="J" x1="184.076" y1="122.593" x2="181.211" y2="121.008" xlink:href="#B"><stop offset="0" stop-color="#6d6d62"></stop><stop offset="1" stop-color="#807575"></stop></linearGradient></defs><path fill="url(#J)" d="M182 114l3-2v4l-1 12h1v1l1 1h-6l-1-1-1 1h0l-1 1h0-1-1v-1h-2v1h-1c-1 0-2 0-2-1h0v-7-1l-1-1 3-1h0l1-1 1-1 3-1 1-1 1-1c1 0 2-1 3-1z"></path><path d="M181 123v2 1c1-1 2-1 3-1v4l1 1v-1l1 1h-6v-1-1c1-2 0-2 0-3l1-2z" class="t"></path><path d="M182 114v5c0 1-1 1-1 2v2l-1 2c0 1 1 1 0 3v1 1l-1-1-1 1h0l-1 1h0l-1-1 1-1c0-1 1-3 0-4v-3c0-2 1-3 0-5l1-1 1-1c1 0 2-1 3-1z" class="z"></path><path d="M179 121c1 1 1 2 1 4 0 1 1 1 0 3v1 1l-1-1v-8z" class="m"></path><path d="M182 114v5c0 1-1 1-1 2v2l-1 2c0-2 0-3-1-4 1-2 1-3 1-5l-1-1c1 0 2-1 3-1z" class="AA"></path><path d="M177 117l1-1c1 5 0 9 0 14l-1 1h0l-1-1 1-1c0-1 1-3 0-4v-3c0-2 1-3 0-5z" class="o"></path><path d="M177 117c1 2 0 3 0 5v3c1 1 0 3 0 4l-1 1 1 1h-1-1v-1h-2v1h-1c-1 0-2 0-2-1h0v-7-1l-1-1 3-1h0l1-1 1-1 3-1z" class="c"></path><path d="M174 125c0 1 1 1 1 3v1h-1v-4z" class="p"></path><path d="M172 120h0v9c-1 1-1 1-2 1v-7-1l-1-1 3-1z" class="AA"></path><path d="M177 117c1 2 0 3 0 5v3c1 1 0 3 0 4l-1 1c0-1 0-1-1-1v1l-1-1h1v-1c0-2-1-2-1-3l-1-6 1-1 3-1z" class="m"></path><path d="M174 118c1 1 1 1 1 2-1 2 0 5 0 8 0-2-1-2-1-3l-1-6 1-1z" class="f"></path><path d="M498 468h1c3-3 7-3 11-2h4c1 1 1 2 2 3v1c1 1 1 3 2 4 0 2-1 3 1 4h1v2c-3 2-5 5-7 7 0 2 1 4 2 6 1 3 2 5 5 7l1-1h0c1 1 1 2 1 3h0c0 1 0 1 1 1 1-1 1-2 2-3v4h1v-3h1c0-1 1-1 1-2l1-1c1 2 2 5 3 7 0 1 1 3 2 5 1 1 2 1 3 2 1 0 1 1 2 1 1 1 3 1 4 2-1 3-3 5-5 8-2 4-4 8-7 11h0c-1 1-1 2-1 3l88 106h-70l-8-15c0-2-2-6-3-7h-1-4l-9 1c-5 0-10 0-14 1h-1l1-1h-1c-1-2-1-2-1-4v-10-1h-1c0-1-19-1-22-1h-10l-1-1-1-1 13-11c0-2 1-3 1-4 2-6 5-11 5-17l-3-9c-3-5-5-11-4-16s4-7 7-10c-3-4-6-9-7-15-1-5 0-9 3-13-3-5-4-10-3-16 0-3 1-6 3-9h-1c1-1 1-2 2-2 0-1 1-1 1-2 1-1 2-4 1-6v-1l3-2c1 0 1 1 2 1l3-3v-1z" class="D"></path><path d="M510 466h4c1 1 1 2 2 3v1l-1-1c-1 0-2-1-2-1-1-1-2-2-3-2z" class="K"></path><path d="M504 495h1c2 0 3 0 4 1v1l-1 1c-1 0-2 1-3 0-1 0-1-1-2-2l1-1z" class="AE"></path><path d="M513 517c-2-2-10-11-10-14h0c1 1 1 1 2 1h0c1 1 1 2 2 3l3 3v1c1 2 2 3 4 5h0l-1 1z" class="e"></path><path d="M507 493c-1 1-1 1-2 0-3-1-3-2-5-5l3 1h6v1 2c-1 0-2 1-2 1z" class="C"></path><path d="M531 611l3 6c-1 0-1 0-2 1-1 0-1 1 0 2l1 1h3-4-3c0-2-1-4 0-6 1-1 2-2 2-4z" class="s"></path><path d="M511 498h0v4c0 1 1 2 0 2-1 1-2 1-3 2l-1 1c-1-1-1-2-2-3 0-1 1-2 2-3 0-1 3-2 4-3z" class="I"></path><path d="M511 488h0c0 4 2 7 4 10h0l-3-1-5-4s1-1 2-1v-2-1l2-1z" class="N"></path><path d="M511 488s1 0 2-1c0 2 1 4 2 6 1 3 2 5 5 7h-2c-1 0-2 0-4-1h-1c-1-1-1-1-2-1h0s1 0 1-1l3 1h0c-2-3-4-6-4-10h0z" class="W"></path><path d="M494 536v1c1 0 1 4 1 5v17c-1 5-1 9 0 13 0 3 1 5 2 7 0 0-1 0-1-1-1-1-2-4-2-6-1-2 0-4 0-6v-13c0-5-1-11 0-17z" class="p"></path><path d="M499 491h0c1 1 2 2 3 2l1 1h0c-1 1-2 1-3 1l-1 1h0 2c1 1 2 2 2 3 1 1 2 2 1 3 0 0-1-1-2-1h0-1c-2 0-4-4-5-5l1-1c1-1 1-2 2-4z" class="AE"></path><path d="M486 484c1-1 1-2 2-2h1l1 1h0c-1 1-1 1-1 2v1h1c0-2 1-2 2-3 1 0 1 0 2 1 1 4 2 8 2 11-1 0-2-1-3-2l-6-9h-1z" class="U"></path><path d="M511 498c1 0 1 0 2 1h1l1 7-1 10h0c-2-2-3-3-4-5v-1l-3-3 1-1c1-1 2-1 3-2 1 0 0-1 0-2v-4z" class="s"></path><path d="M507 507l1-1c1-1 2-1 3-2 0 2 0 5-1 6l-3-3z" class="G"></path><path d="M504 472h3c2 0 3 1 4 3 1 1 1 2 1 3-1 2-2 3-3 4s-3 1-4 0c-1 0-2-1-3-2s-1-3-1-4c0-2 2-3 3-4z" class="X"></path><path d="M524 600h2l2 4 3 7c0 2-1 3-2 4-1 2 0 4 0 6h3l-9 1c0-7-1-14 1-21v-1z" class="C"></path><path d="M524 600h2l2 4-2 1-1 1-1-1v-4-1z" class="AE"></path><path d="M528 604l3 7c0 2-1 3-2 4v-1-3c-1-2-1-3-1-4-1-1-1-1 0-2h-2l2-1z" class="k"></path><path d="M522 593l4 7h-2v1c-2 7-1 14-1 21-5 0-10 0-14 1h-1l1-1h-1c-1-2-1-2-1-4v-10-1-2-4l-1-5v-2c5-1 11-1 16-1z" class="D"></path><path d="M507 601h1c0 2 0 3 1 5 0-1-1-1-2-1v-4z" class="r"></path><path d="M506 596h1c0 1 1 1 2 3 0 1-1 2-1 2h-1l-1-5h0z" class="f"></path><path d="M522 593l4 7h-2c-3-1-7-2-10-3-2-1-5-2-7-3l-1 2h0v-2c5-1 11-1 16-1z" class="i"></path><defs><linearGradient id="K" x1="506.62" y1="610.557" x2="513.397" y2="618.682" xlink:href="#B"><stop offset="0" stop-color="#95846f"></stop><stop offset="1" stop-color="#b9a489"></stop></linearGradient></defs><path fill="url(#K)" d="M507 605c1 0 2 0 2 1l2 1a30.44 30.44 0 0 0 8 8l-10 7h-1c-1-2-1-2-1-4v-10-1-2z"></path><path d="M507 605c1 0 2 0 2 1l2 1h-1v2h-1c-1 0-2-1-2-1v-1-2z" class="q"></path><path d="M529 498c1 2 2 5 3 7 0 1 1 3 2 5 1 1 2 1 3 2 1 0 1 1 2 1 1 1 3 1 4 2-1 3-3 5-5 8-2 4-4 8-7 11h0c-1 1-1 2-1 3l-4-5-13-15 1-1 1-10-1-7c2 1 3 1 4 1h2l1-1h0c1 1 1 2 1 3h0c0 1 0 1 1 1 1-1 1-2 2-3v4h1v-3h1c0-1 1-1 1-2l1-1z" class="Y"></path><path d="M526 532l3-3h0c0 3 1 3 2 5-1 1-1 2-1 3l-4-5z" class="w"></path><path d="M521 499h0c1 1 1 2 1 3h0c0 1 0 1 1 1v1h-1l-7-2v4h0l-1-7c2 1 3 1 4 1h2l1-1z" class="C"></path><path d="M521 499h0c1 1 1 2 1 3h0c-1 0-2 0-3-1l-1-1h2l1-1z" class="I"></path><path d="M525 500v4h1v-3h1c0-1 1-1 1-2 1 2 1 3 2 4 1 3 2 4 3 6-2-1-4-2-6-2-1-1-3-3-4-3v-1c1-1 1-2 2-3z" class="T"></path><path d="M485 593c9-9 17-18 25-27h0c0 4 3 8 4 11l8 16c-5 0-11 0-16 1v2l1 5v4 2h-1c0-1-19-1-22-1h-10l-1-1-1-1 13-11z" class="I"></path><path d="M499 583h1v1h-2v1c0 3 3 5 4 7s1 4 1 6c-1-1-1-1-1-2-2-3-3-7-5-10h-1c0-2 0-2 1-3h1 1z" class="G"></path><path d="M473 605c0-2 7-7 9-9 2 3 3 6 5 9h1-4c0-2-1-3-2-5-1 0-1-1-1 0-3 0-6 3-7 6l-1-1z" class="D"></path><path d="M474 606c1-3 4-6 7-6 0-1 0 0 1 0 1 2 2 3 2 5v1h-10z" class="G"></path><path d="M153 167c2-1 4-1 7-2 4 2 6 3 9 7h1c0 1 2 3 2 5 1 1 2 2 2 3s0 2 1 3h0v2c0 1 1 2 1 3h2 2 1 11c1 0 2 0 3-1h7 6v-1h0 1v1c2 1 4 0 6 0h6 4 0 2 1 1 8 1v-1h1v1h6c1 0 3-1 4-1v-1c1 0 1-1 1-2-2 0-2 1-3 1h0c0-1 2-2 3-3l1-2c1 0 2 0 2-1 1 0 1 0 1-1-1 0-1-1-2-1 1-1 2-1 3-1v1 2l-1 3v2 1 2l-1 10 1 14v1 3l-1 9c1 0 1 0 2 1-3 3-6 7-9 10-1 2-2 4-3 5 0 1-1 3-2 3l-6 18v11 9l-9-3c-1 0-1-1-2-1 1 3 3 3 5 4h6v1c1 3 1 5 2 7v3l5 16 2 5 3 8v2c1 1 1 2 2 3v2l3 8c0 1 0 2 1 2v1l2 6c-3 0-5-2-8-3h-3-1l-2-1s-1-1-2-1h0c-3-1-6-3-8-3-2-1-4-1-5-2s-2-1-4-1v1l3 1c0 1 0 1-1 2-2-1-3-2-5-2l2 4c-1-1-3-2-3-4l-2 2h-3c0 1-2 1-2 2l-1-1h-1-1-1c-1-1-4-1-5 0l-2 2c-1-1-1-1-1-2l-2-1h-1 0l1 1v1l-2 1c-1 0-2 0-2-1h-2-3c-1-1-2-1-3-1s-1 0-1-1h0c-1 0-2 1-3 1h-2c0-1 1-2 1-3 2-1 6-3 6-5h0c-2 1-4 3-5 4-1 0-2-1-2 0-1 1-2 1-3 1l-4 1-1 1c-2 0-3 1-5 1l-8 4-10 4c-12 5-22 12-33 18-5 3-10 6-15 10h1c-1 1-2 2-3 2l-5 3c-1 1-1 1-2 1 0 1-1 1-2 2-1 0-1 1-2 1l-1 1c-1 0-2 1-3 2h-1c-2 0-1 2-4 0 1 0 2-2 3-2h0c1-1 2-1 2-2-1-1-1 1-2 1h-1c-2 0-1 1-3 0 0-1-1-3-2-4 0-1 0-1-1-2h0c0-2 0-3-1-4l-1 1v-3l-1-2v-1c0-1-1-2-1-4l-1-1c0-1 0-2-1-2v-2-2 1c2-1 3-2 5-3h0c0-1 1-2 1-3l-2-1c1 0 2-1 3-2l1-1c3-1 7-3 9-5v-1l-7 4c-1 0-2 1-2 1h-3c-2-2 0-1-1-3 0-1-1-1-1-2 1-1 1-2 2-2v-1c1-1 1-3 2-4h0l1-1c0-2 0-3-1-4l1-2c-1-1-1-1-2-1h-3c0 1 0 2-1 2h-2c-1 1-3 2-5 3v-1-4h0v-10h1v-4l1-2h1 0c0-1 1-2 1-3 1 0 2 0 3-1l-1-1-2-1h3c-2 0-4-1-5-1h0c-2-1-3-2-4-2l3-6h-2c0-1 0-1-1-1l1-7c0-1 1-3 1-4 1-4 2-6 2-10l1 2h1 9 0 5 1c2-1 2-1 3-2v1c3-2 5-5 7-8s5-5 7-8c2-1 3-3 4-5h0c2-2 3-4 5-6h0v-1h-1c0-1 0-2 1-2 0-2 0-4-1-5h0v-2-4h1v-2h-1v-1s0-1 1-1h0c-1-2-1-4-1-6h-1c1-1 1-2 1-4 0 0-1 0-1-1l1-1v-2h-2-1v-1c1 0 2 0 2-1l1-1v-4h0l2-2-1-1-1-1v-4l-1-1 1-1v-2c0-4 0-8-1-12h0v-2c0-1 0-1 1-2h4c2-1 5 0 8-1 1 1 2 1 4 1h6 1 4l1-1v-3-5c0-1 0-2 1-3 1 0 1 1 2 1h1l1 1 2-2 1 2 2-1h2c1 0 1 1 2 1l4-4z" class="D"></path><path d="M72 318c1 1 1 2 1 3h-1v-3z" class="O"></path><path d="M153 311h1l1 1h0-3-2l3-1z" class="j"></path><path d="M180 238h0c1 2 2 3 1 4h0c-1 0-1-2-2-3 0 0 1 0 1-1zm-33-24h1c1 0 2 0 3 1l-1 1h-1l-1-1h-1v-1zm74 11c1 1 2 1 2 2h1l-1 1h0c-1-1-2-1-3-2l1-1z" class="T"></path><path d="M161 240l-1-2h0c2 0 3 1 4 3-1 0-1 0-2 1 0-1 0-2-1-2z" class="j"></path><path d="M150 289c-1 0-2 0-3-1v-1c1-1 2 0 3 0v2z" class="J"></path><path d="M72 297c0 1 0 1 1 2l-1 3h-1l-1-3h1c1-1 1-1 1-2z" class="V"></path><path d="M114 240h2l-4 3c0-1 0-1 1-3h1z" class="Q"></path><path d="M160 234l1 1c-1 0-1 1-1 1v1h-2-1v-2h0c1 0 2 1 3 1v-2z" class="W"></path><path d="M83 282c1 2 2 2 3 3v1h-2-1v-4z" class="B"></path><path d="M160 185l4 1 1 1c-2 1-3 1-5 0v-2z" class="l"></path><path d="M182 261c1 0 1-1 2-1l1 4h-1v1l-2-4z" class="C"></path><path d="M215 231h0l1 1c-1 0-1 1-1 2h1l-2 2h-1c0-2 1-3 2-5z" class="W"></path><path d="M201 328v2h0c1 0 1 1 2 1 0 0-1 0 0 1v1l-1 1-2-2 1-4z" class="B"></path><path d="M164 186c1 0 2-1 4-2l-1 1v1h2v2l-4-1-1-1z" class="O"></path><path d="M198 328l2-1v1h1l-1 4c-1 0-1-1-2-1v-3h0z" class="S"></path><path d="M108 248c0 1 0 1 1 2-1 1-3 2-4 4h-2c2-2 3-4 5-6z" class="W"></path><path d="M210 307v-3h1c1 3 1 6 1 8l-1-2h-1l1-1-1-2zm-140-8c0-2-1-5-1-7h0c1 1 2 3 3 4v1c0 1 0 1-1 2h-1z" class="b"></path><path d="M149 280c2 0 4 1 6 1 0 1 0 2 1 3h-4l1-1c0-1-1-1-1-1-2-1-2-1-3-2z" class="F"></path><path d="M145 215h1v1c1 1 2 2 4 2h0 0v1 3h-1l-1-1v-1c0-1-1-1-1-1-1 0-1-1-1-1v-1s0-1-1-2z" class="W"></path><path d="M71 315h0l2-1v1c0 1-1 1-1 3h0v3h-1-1l1-1c0-1-1-1 0-3h-1c0-1 1-1 1-2z" class="T"></path><path d="M127 237c1-3 1-5 4-8v1c1 0 0 1 0 1-1 1-2 2-2 3h0v3h-2z" class="J"></path><path d="M85 274v1l-4 5-1-1c1-1 0-2 1-3h1c2-1 2-1 3-2z" class="AE"></path><path d="M214 328c-2 0-2 0-4-1l1-1c2-1 9 0 12 0-3 1-7 0-10 0 1 1 2 1 3 1-1 1-2 1-2 1z" class="r"></path><path d="M75 312c1 1 1 2 1 2-1 1-1 2-1 2 1 1 1 1 2 1-1 1-2 3-2 4v1h-1v-2h0v-2-1c0-2 1-3 1-5z" class="B"></path><path d="M192 304c1 1 2 1 3 2h0c-1 0-2 1-4 2v2l-3-1h1c1-1 1-3 3-5z" class="S"></path><path d="M177 262c1 2 2 3 3 5h0-1c-1 0-1 1-2 1v-6z" class="T"></path><path d="M195 187h7v1c-7 0-14 1-21 0h11c1 0 2 0 3-1z" class="F"></path><path d="M75 312h0v-2l1-1 1 1v1l-1 1v1 1c1 0 1 0 1-1h1v2h-1c0 2 2 2 2 3h-1l-1-1c-1 0-1 0-2-1 0 0 0-1 1-2 0 0 0-1-1-2z" class="s"></path><path d="M134 247l3 2h-1l1 1h-1c-1 0-2 0-2-1h0-1v1 1c-1-1-2-2-2-3v-1h3z" class="B"></path><path d="M156 239h2 0v3h-3-1l-1-1h0l-1-1c2 0 2 0 4-1z" class="R"></path><path d="M136 214h1v2l1 1v1h-1l-1 1c1 1 1 1 1 2l-1 1v-1h0c0-1-1-1 0-2h0c-1 0-1 0-2 1h0 1 0v1h-1-1 0l2-4h0c1-1 1-2 1-3z" class="J"></path><path d="M143 220l-1-1c-1 0-1 0-2-1v-1c1-1 1 0 2-1h2l1 1h-1v1c1 1 1 3 2 4h-1 0v-1h0l-1-2h0v1h-1z" class="n"></path><path d="M68 286h9c0 1 0 1-1 2s-5 0-7 0h1l-2-2z" class="M"></path><path d="M121 254h1c0 2 0 4-1 5 0 2-1 4-2 6h-1l1-4c1-2 1-5 2-7z" class="B"></path><path d="M78 280v1l2-2 1 1c0 1-2 5-3 5h-2v-3c0-1 1-1 2-2z" class="E"></path><path d="M140 226l1 1h0c1 2 1 1 2 2v1c-2 1-3 1-5 1v-1s1-1 2-1h-2v-2l2-1z" class="R"></path><path d="M72 296h0c0-2-1-4 0-5l3-1-2 9c-1-1-1-1-1-2v-1z" class="L"></path><path d="M159 280h7 1l1-1h1v1c-1 0-1 0-2 1h-1-3l-1 1h4c0 1 0 1-1 1h-1-3s0-1-1-1c0 0-1-1-1-2z" class="W"></path><path d="M155 192l1-1 1 1h1l1-1 1 1 1 1v-1c1 1 1 2 2 4h0c-1 0-1 0-2-1 0 1 0 1-1 0-1 0-1 0-2-1h0 0c-1-1-2-2-3-2z" class="T"></path><path d="M130 264c1 1 2 1 3 2h2c0 1 0 1-1 2 1 0 1 1 2 1 1 1 1 2 2 2 1 1 2 3 3 4l-5-4-1-2c-1 0-1 0-2-1l-3-2-1-1 1-1z" class="F"></path><path d="M142 238v-1h1v1c1 0 1 0 1-1h1v1h-1v1 1h0l-1 1c2 1 3 1 5 1-1 1-1 1-2 1-1-1-3-1-4-2-1 0-1 1-2 0-1 0-1-1-1-1l-1-1h0c1 0 1 0 1-1h0 1 0l1 1h1v2h1v-1c0-1 0-1-1-2z" class="B"></path><path d="M118 195l1-1c0 1 0 2-1 4h1 0c1-1 2-1 3-1v-1c1 1 1 1 2 1l1 1c-2 1-6 1-8 0-1 0-2-1-3-1l-1-1c1-1 1-1 3-1v1h1l1-1z" class="t"></path><path d="M136 206c1 1 2 1 3 2l-2 2v3h0c1 1 2 1 2 2v1 2h-1c-1 1-1 2-1 2v1h0c0-1 0-1-1-2l1-1h1v-1l-1-1v-2c0-1-1-3-1-4v-4z" class="n"></path><path d="M77 317l1 1c0 2 0 2-1 4l1 1-5 2v-1-1h1v-1h1v-1c0-1 1-3 2-4z" class="T"></path><path d="M150 287l8 1h0c0-1 0-1 1-1h0c1 1 2 1 3 1s5-1 5 0c-1 1-2 1-4 1v-1l-2 2h0v1c0-1 0-1-1-2h-4-6v-2z" class="y"></path><path d="M132 280l-1-1h1 0c1 1 2 1 2 1 2 2 3 3 5 4s3 1 5 0c1 1 0 1 1 1s1 0 2 1l-2 2c-3-1-6-3-9-5-1-1-3-2-4-3z" class="R"></path><path d="M186 236h0c1 2 1 3 2 4 0 1 1 2 0 3h-1 0l1 1c0 2 1 4 2 4 1 1 1 0 1 1l-1 1c0 1 1 1 1 2h-1l-1-1c-1-1-1-2-1-3 0-2-1-3-2-4h0c0-1-1-2-1-3h1l1 1h0c1-1-1-4-1-6z" class="W"></path><path d="M241 242h0c-2 1-4 2-6 2-2 1-3 0-4 1-2 1-4 3-5 4h-1c1-1 2-2 2-3 2 0 3-2 5-2 1-1 3-2 4-2s2 0 3-1c1 0 2-1 4-2 0 1-1 3-2 3z" class="AF"></path><path d="M182 261l-4-7c0-2-1-3-1-5h1v1c1 1 1 1 2 1 1 3 3 5 4 9-1 0-1 1-2 1z" class="a"></path><path d="M127 237h2v-3h0c0-1 1-2 2-3 0 1-1 2-1 3v1h0v1 1h2 1 1l1-1 1 1 1-1h1 1v1c0 1 0 2-1 2h0-1 0c-1 0-2 0-2-1v-1h-1c-2 1-2 0-4 0l1 1c-1 0-1 1-2 1h-2 0v1l-1-1 1-2z" class="j"></path><path d="M237 288v3 2h-2l-1 1h0c-2 0-3 0-4-1h1c0-2-1-1-2-2 2-2 5-1 7-1l1-2z" class="W"></path><path d="M192 304h1 1c1 0 2 0 3-1h1s1 0 1-1c1 0 2 0 3-1v1h0l1 2c-2 1-2 2-4 2l-1 1-1-1-2 1v-1h0c-1-1-2-1-3-2z" class="O"></path><path d="M109 239c1 0 2-1 3-1 1 1 1 1 2 1h1l-1 1h-1c-1 2-1 2-1 3l-4 5v-1h-1c0-1 0-2 1-2 0-1 1-1 1-1v-1-4z" class="b"></path><defs><linearGradient id="L" x1="220.957" y1="324.968" x2="223.155" y2="328.906" xlink:href="#B"><stop offset="0" stop-color="#9b948e"></stop><stop offset="1" stop-color="#b5ad9c"></stop></linearGradient></defs><path fill="url(#L)" d="M224 323h1v3h2v2h-1-2c0 1-1 2-1 2v-1c-2-2-5 0-7-2-1 0-2 0-3-1 3 0 7 1 10 0h0c1-1 1-2 1-3z"></path><path d="M116 234l2-2v1c1 1 2 1 3 1l-2 2c0 1-2 2-2 3l-1 1h-2l1-1h-1c-1 0-1 0-2-1 2-1 3-2 4-4z" class="E"></path><path d="M116 237c0 1 0 2 1 2l-1 1h-2l1-1 1-2z" class="U"></path><path d="M118 236h1c0 1-2 2-2 3-1 0-1-1-1-2l2-1z" class="h"></path><path d="M116 234l2-2v1c1 1 2 1 3 1l-2 2h-1v-1c-1-1-2 0-2-1z" class="H"></path><path d="M119 189l2-1h2l-1 4v4 1c-1 0-2 0-3 1h0-1c1-2 1-3 1-4v-3-1-1z" class="h"></path><path d="M156 281c1 0 2 0 3-1 0 1 1 2 1 2 1 0 1 1 1 1v2c1 0 1-1 2-1h5l1 1c-2 1-3 0-5 0-1 0-2 0-3 1h-2v-1l1-1h-2-2 0c-1-1-1-2-1-3h1z" class="S"></path><path d="M156 281c1 0 2 0 3-1 0 1 1 2 1 2l-1 1c-2-1-2-1-3-2z" class="l"></path><path d="M130 181h4 1v3h0l2 1c0 1-1 1-1 2h0l-1-1c-1-1-2-1-4 0h0c-1 0-2 0-3-1 1-1 2-2 2-4z" class="u"></path><path d="M107 201l1 1h1l1-1c1 0 1 0 1 1h1 1l-1 1c1 1 1 1 2 1v-1l1-1v1 3h1-1-7l-1-1v-4z" class="W"></path><path d="M131 238l-1-1c2 0 2 1 4 0h1v1c0 1 1 1 2 1h0 1l1 1s0 1 1 1c1 1 1 0 2 0 0 1-1 1-2 1 0 1-1 1-2 2v-1h-1 0-2l-1 1h-1v-1-1-1h2c0-1-1-1-1-2h-1-1l-1-1z" class="u"></path><path d="M133 242h2c2-1 1-2 3-2v1c0 1 0 2-1 2h0-2l-1 1h-1v-1-1z" class="B"></path><path d="M142 238l-1-1 1-1c1 1 2 1 3 1v-1c1-1 1 0 2 0 0-1 1-2 2-2s1 0 1 1h0c0 1 0 2 1 2v1h1l4 1c-2 1-2 1-4 1s-3 1-4 0c-1 0-1-1-2-2h-1v-1h-1c0 1 0 1-1 1v-1h-1v1z" class="j"></path><path d="M149 235h0l1 1c-1 1-1 1-3 1h-1c0-1 1-1 2-1 0 0 0-1 1-1z" class="O"></path><path d="M152 240c-2-1-4 0-5-1v-1h5l4 1c-2 1-2 1-4 1z" class="S"></path><path d="M137 220c1 0 1-1 1-1l2-1c0 1 0 1 1 1l1 1h1c1 1 1 1 1 2h0c-1 1 0 2-1 2l-1 1h-2s0-1-1-2h1 2v-1h-1l-1 1h-1v2l-1 1v-1h-1c-1 0-1 0-1-1v-1l1-1h-1l1-1h0v-1z" class="l"></path><path d="M137 243h1v1c1-1 2-1 2-2 1 0 2 0 2-1 1 1 3 1 4 2 1 0 1 0 2-1h2 2 1c1 0 1 1 2 1h-1-2-2 0c-1 0-2 0-2 1h-3-2c-1 1-2 1-3 1-1 1-2 0-3 1-1 0-2-1-3-1h-1 0v1h-1l-1-1 2-2v1h1l1-1h2 0z" class="j"></path><path d="M137 243h1v1c1-1 2-1 2-2 1 0 2 0 2-1 1 1 3 1 4 2-2 0-4 1-5 1h-1c-2 1-4 1-6 1v-1l1-1h2 0z" class="AB"></path><path d="M162 242l-1 1h2l2-2-1-1h1l-1-1v-1h2l1 1h1 1l-1-2h1 0c1 1 1 2 2 3 1 0 1 1 2 1l-1 1h-1l-2-2v1c-2 0-2 0-3 1-1 0-1 1-1 2h0 1v-1c1-1 1-1 2-1s0 0 1 1c0 0 1 1 2 1 0 1 0 2-1 2h-1c1 0 1 0 1-1h0c0-1 0-1-1-1v-1h-1l-1 1h0-1c0 1-1 1-1 1l-1-1h-1 0l-1-1h-1-2 0v-2l1 1h0v-1c0-1 1-1 1-1h0c1 0 1 1 1 2z" class="W"></path><path d="M184 265v-1h1l3 9v11c0 2 0 3-1 4h0c-1-8-1-16-3-23z" class="Q"></path><path d="M76 276h5c-1 1 0 2-1 3l-2 2v-1c-1 1-2 1-2 2v3h0-6c1-2 3-3 4-5 1-1 2-2 3-4h-1 0z" class="H"></path><path d="M76 276h5c-1 1 0 2-1 3l-2 2v-1c0-1 1-1 1-3h-1c-2 2-5 5-7 8h5-6c1-2 3-3 4-5 1-1 2-2 3-4h-1 0z" class="a"></path><path d="M240 320h7v2h-3-12c-2 1-5 1-7 1h-1l-12 1c4-2 10-2 15-3l13-1z" class="N"></path><path d="M133 206h0 3v4c0 1 1 3 1 4h-1c-1-1-2 0-3-1v-1l-1-1h0-1-1v-2c-1 0-2-1-3-2v-1h6z" class="e"></path><path d="M133 206h0 3v4c-1-1-1-1-3-1h0v-1-2z" class="k"></path><path d="M145 227h2c0 1 0 1 1 1l1-1 1 1v1c1 1 2 3 4 4h0v1h0c-1 0-1-1-2-1v1h-1v-1c-1 1-1 1-2 1 0-1-1-1-1-2l1-1h1v-1h0l-1-1c0 1 0 1-1 1h0c0 1-1 1-1 1h0c-1 1 1 1 1 3-1 0-1 1-2 1h-1l-1 1c0-1 0-1-1-2l1-1v-2l1-1c-1-1-1-1-1-2l1-1z" class="B"></path><path d="M69 310h1c1 0 1 0 2 1l-2 3s0 1 1 1h0c0 1-1 1-1 2h1c-1 2 0 2 0 3l-1 1 1 2c1 1 1 2 1 3-1 0-1 0-2 1h0c0-1 0-3-1-4h-1c-1 0-1 0-1-1l1-1v-1l-1-1c0-2 0-3 1-5 0-1 0-2 1-4z" class="y"></path><path d="M71 315h0c0 1-1 1-1 2h1c-1 2 0 2 0 3l-1 1 1 2h-2l1-2-1-1s1-1 1-2l-1-1c0-2 1-1 2-2z" class="J"></path><path d="M133 221h0c0 1 0 1-1 2h0c1 0 1 0 1-1 1 1 1 1 2 1v1l-1 1 1 1h2 1v3h-1-1c-1 0-2 1-2 2-1 0-1 0-2 1v-2-1h0l-2-1v-1c-1 0-1 1-2 2h-2l3-3 1-2 1-2 2-1z" class="B"></path><path d="M133 225c1 1 2 2 2 3v1l-1-1s-1-1-1-2v-1z" class="J"></path><path d="M129 226h1 1v-1c1-1 1-1 2-1h0v1 1c-1 0-2 0-2 1l1 2h0l-2-1v-1c-1 0-1 1-2 2h-2l3-3z" class="T"></path><path d="M192 282v7c1 1 0 1 0 2h0c1 1 1 2 1 3v1h1c1 1 1 1 1 2h-1-1 1v-1h-1v2s1 0 1 1h1l1 1h1 0c0 1 1 1 1 1v1l-1-1c-1-1-2-1-3-1-1 1 0 2-1 3-1 0-1 0-1-1-1-1-1-1-2-1h-1c1-1 1 0 1-1v-4c-1-2-2-2-2-5 0 1 1 2 1 3l1-1c1-1 1-2 2-3l-1-1c1-1 1-5 1-7z" class="l"></path><path d="M205 283v13h0v2 1 3 1c-1-1-1-1-3-1v-1l-1-1c0-1 0-1 1-2-1-3 0-6 0-9 1-2 1-4 3-6z" class="s"></path><path d="M202 187h6v-1h0 1v1c2 1 4 0 6 0h6 4 0 2 1 1 8 1v-1h1v1c-1 1-2 2-4 2-1-1-1-1-2-1h-10-21v-1z" class="P"></path><path d="M237 291l5 16c-1 0-4-1-6 0-2 0-4 0-6 1h-3-2l1-1h1 2c1 0 1 0 1-1 1 0 1 1 2 0l2-1c1 1 1 1 2 0h0 3 1l1-1s0-1-1-1v-1-1c-1 0-1-1-1-2s-1-1-1-2h0c0-1 0-2-1-2-1 2-3 2-4 3h2c0-1 0 0 1 0h-1 0c-2 1-3 1-4 1s-1 0-2-1c2-1 6-1 8-3h-1l-2-1 1-1h2v-2z" class="j"></path><path d="M123 188l3 3c0 1 0 2 1 2h1l3 3c1 0 2 0 2 1s1 1 1 2h0c-1 0-1-1-2-1h0-4-3l-1-1c-1 0-1 0-2-1v-4l1-4z" class="F"></path><path d="M124 197h3 0c-1-2-3-2-3-5h1c1 1 1 3 3 4 0 0 1 0 1 1h2l1 1h-4-3l-1-1z" class="AG"></path><path d="M211 272c1-9 4-16 9-24-3 8-7 15-7 23-1 4-1 8-1 11v4c0 1-1 1-1 1-1-1-1-3-1-5s0-5 1-7v-3z" class="Q"></path><path d="M211 275c0 2-1 6 1 8v-1 4c0 1-1 1-1 1-1-1-1-3-1-5s0-5 1-7z" class="I"></path><path d="M66 311c1 0 2 0 3-1-1 2-1 3-1 4-1 2-1 3-1 5l1 1v1l-1 1c0 1 0 1 1 1v1h-3v1h-1c-1 1-1 1-1 2h0v-10h1v-4l1-2h1 0z" class="f"></path><path d="M68 320l-1 1h-1v-2h1l1 1z" class="z"></path><path d="M66 311c1 0 2 0 3-1-1 2-1 3-1 4-1 1-1 1-2 1h-1c0 1 1 1 1 2 0 0-1 2-1 3h-1v-3-4l1-2h1 0z" class="O"></path><path d="M66 311c1 0 2 0 3-1-1 2-1 3-1 4-1 1-1 1-2 1l-1-1c0-1 0-2 1-3h0z" class="AF"></path><path d="M244 312l3 8h-7 0 3c0-1 0-1-1-1v-1l3-1v-1h-1v-1h-1v-1c-1 0-2 0-3 1h-1-3l-8 3h-1-1c-1 0-2 1-3 1s-3 0-4 1h-2-1-3c2-1 3-1 4-2h2 2c2-1 4-1 5-1l1-1h2l9-2h1c1-1 0-1 1-1h2l2-1z" class="O"></path><path d="M188 273c0 3 3 5 4 9 0 2 0 6-1 7l1 1c-1 1-1 2-2 3l-1 1c0-1-1-2-1-3-1-1-1-2-1-3h0c1-1 1-2 1-4v-11z" class="J"></path><path d="M242 195h4c2 1 3 0 4 1v1h-2-5 0c-1 0-1 0-2 1h-1-1-1c-1 1-1 0-2 1h0c-1 0-1 1-2 1 0 0-1 0-1 1h0 1 1l1-1c1 0 2-1 2-1h1 1l2-1c1 0-1 0 1 0h1l1-1h0c2 1 4 0 5 1 0 1 0 2-1 3h0-1v-1h1v-1c-1-1-4-1-6 0-4 0-10 2-13 4-1 1-1 1-2 3 0-1 0-1-1-1-1 1 0 1-1 2h-1v-2c0-1-1 0-2-1 0-1 0-1 1-2h1c1-1 2 0 3 0v1h1l1-1 2-1c1-1 2-2 3-2v-1h2 1c0-1 1-1 1-1h2 1c1 0 1 0 1-1h0l-1-1z" class="j"></path><path d="M230 197c2 0 7-4 9-4 1-1 3-1 4-1-1 1-2 1-2 2h-2c0 1-1 1-1 1v1l4-1 1 1h0c0 1 0 1-1 1h-1-2s-1 0-1 1h-1-2v1c-1 0-2 1-3 2l-2 1-1 1h-1v-1c-1 0-2-1-3 0h-1l2-3c2 0 2-2 4-2z" class="J"></path><path d="M64 296c0-2 1-4 2-5h0 1c0 4 2 7 3 11v3c-2 0-4-1-5-1h0c-2-1-3-2-4-2l3-6z" class="E"></path><path d="M70 302v3c-2 0-4-1-5-1h0c1-1 3-1 4-1l1-1z" class="e"></path><path d="M228 206c1-2 1-2 2-3 3-2 9-4 13-4 2-1 5-1 6 0v1h-1v1h1 0c0 1-1 1-1 1-1 0-2 1-3 1h-3 0c-3 0-5 1-8 1l-2 1h-3c0 1 0 1-1 1z" class="q"></path><path d="M243 201h0c1 0 2 1 3 1h2c-1 0-2 1-3 1h-3 0 2l-1-2z" class="AF"></path><path d="M234 204s1-1 2-1h0c2-1 3-1 5-1v-1h2l1 2h-2c-3 0-5 1-8 1z" class="t"></path><path d="M138 279c0-1-9-8-10-9h1c1 1 4 2 5 3l1 1v-1h2c2 1 3 3 5 4h1c1 1 6 2 6 3 1 1 1 1 3 2 0 0 1 0 1 1l-1 1c-5-1-9-3-14-5z" class="p"></path><path d="M62 288c0-1 1-3 1-4 1-4 2-6 2-10l1 2h1 9 1c-1 2-2 3-3 4-1 2-3 3-4 5l-2 1 2 2h-1-7z" class="E"></path><path d="M211 272v3c-1 2-1 5-1 7h0c0 1 0 2-1 2v1 1 1c0 1-1 1-1 2v8s-1 1-1 2v3h0c0-1-1-1-1-2v-1h-1v-1-2h0v-13-4c0-2 0-4 1-6h1l2 1 1-1 1-1z" class="F"></path><path d="M207 278h1v3h-1l-1-1c0-1 0-1 1-1v-1z" class="O"></path><path d="M206 285v-3h1c1 0 1 1 1 2l-1 1h-1z" class="AB"></path><path d="M206 285h1c1 1 1 2 1 4v3l-1 1c0-2 0-4-1-5v-3z" class="l"></path><path d="M206 288c1 1 1 3 1 5l1-1v-3h0v8s-1 1-1 2v3h0c0-1-1-1-1-2v-1h-1v-1h1c1-2 0-4 0-6v-4z" class="S"></path><path d="M206 288c1 1 1 3 1 5l1-1c0 1 0 2-1 3 0-1 0-2-1-3v-4z" class="s"></path><path d="M244 322h3c1 1 1 2 2 3v2h-8-6l-8 1v-2h-2v-3c2 0 5 0 7-1h12z" class="n"></path><path d="M244 322h3c1 1 1 2 2 3v2h-8-6l-8 1v-2h3l14-1v-3z" class="N"></path><path d="M244 322h3c1 1 1 2 2 3h-5v-3z" class="j"></path><path d="M227 326h3c3 1 8-1 11 1h-6l-8 1v-2z" class="a"></path><path d="M210 282c0 2 0 4 1 5 0 0 1 0 1-1l-1 13v5h-1v3l1 2-1 1h-6c0-1 1-1 1-1 1-1 1-1 2-1-1-1-2-1-3-1h-4s-1 0-2 1v-1l1-1c2 0 2-1 4-2l-1-2h0c2 0 2 0 3 1v-1-3h1v1c0 1 1 1 1 2h0v-3c0-1 1-2 1-2v-8c0-1 1-1 1-2v-1-1-1c1 0 1-1 1-2h0z" class="AF"></path><path d="M210 293c0 3-1 8 0 11v3l1 2-1 1h-6c0-1 1-1 1-1 1-1 1-1 2-1l1-1s1 0 1-1v-2c0-4 0-8 1-11z" class="AA"></path><path d="M210 282c0 2 0 4 1 5 0 0 1 0 1-1l-1 13v5h-1v3-3c-1-3 0-8 0-11v-11h0z" class="AE"></path><path d="M210 304v-5h1v5h-1v3-3z" class="L"></path><path d="M206 300c0 1 1 1 1 2h0v-3c0-1 1-2 1-2 0 2 1 5 0 8h0v2l-1 1c-1-1-2-1-3-1h-4s-1 0-2 1v-1l1-1c2 0 2-1 4-2l-1-2h0c2 0 2 0 3 1v-1-3h1v1z" class="y"></path><path d="M205 299h1v1c0 2 0 3-1 5h0-1c0-1 0-1 1-2v-1-3z" class="F"></path><path d="M106 183c2 1 2 1 3 2v-1l6 3h1l1-1 2 3v1 1 3l-1 1-1 1h-1v-1c-2 0-2 0-3 1-1 0-2 1-4 1l-2 2v-2c0-4 0-8-1-12h0v-2z" class="m"></path><path d="M117 186l2 3v1 1 3l-1 1h-1l-1-1h1l-1-1c1-2 1-2 0-4 0-1 0-1-1-2h1l1-1z" class="c"></path><path d="M117 194h1v-2l1-1v3l-1 1h-1l-1-1h1z" class="q"></path><path d="M109 184l6 3c1 1 1 1 1 2v1l-1-1h-1v2c0 1-1 1-1 2h-1c-1-2-2-5-3-7v-1-1z" class="j"></path><path d="M106 185h1l3 6c1 1 1 2 2 4-1 1-3 2-4 3l-1-1c0-4 0-8-1-12z" class="W"></path><path d="M119 180c1 1 2 1 4 1h6 1c0 2-1 3-2 4l-3 2 1 3c0 1 0 1 1 1 0 1 1 1 1 2h-1c-1 0-1-1-1-2l-3-3h-2l-2 1-2-3-1 1h-1l-6-3v1c-1-1-1-1-3-2 0-1 0-1 1-2h4c2-1 5 0 8-1z" class="o"></path><path d="M119 183l1-1h0c1 0 2 1 2 1 0 1 0 2 1 2-1 1-1 1-1 2-1-1-2-3-3-4z" class="c"></path><path d="M119 184v-1h0c1 1 2 3 3 4l-1 1-2 1-2-3v-1c1 0 1 0 2-1z" class="y"></path><path d="M129 181h1c0 2-1 3-2 4l-3 2 1 3c0 1 0 1 1 1 0 1 1 1 1 2h-1c-1 0-1-1-1-2l-3-3h-2l1-1c0-1 0-1 1-2 0 1 1 1 1 1l2-1c1-1 2-1 2-2 0 0 0-1 1-1v-1z" class="d"></path><path d="M106 183c0-1 0-1 1-2h4 6v1h-1 1l2 2c-1 1-1 1-2 1v1l-1 1h-1l-6-3v1c-1-1-1-1-3-2z" class="J"></path><path d="M115 184c-1 0-1-1-2-1 1-1 2-1 3-1h1l2 2c-1 1-1 1-2 1l-2-1z" class="C"></path><path d="M109 184h0c1-1 5 1 6 0l2 1v1l-1 1h-1l-6-3z" class="a"></path><path d="M187 322c1-1 2-1 3 0v1c1 0 1 0 2 1 0 1-2 3-2 3h-3v1h-1c-1 1-1 1-1 2l2-1v1c0 1 0 2-1 2l-1 1v1h2l-1 1h0c-2-1-3 0-4 1-1 0-1 0-2-1 2-1 6-3 6-5h0c-2 1-4 3-5 4-1 0-2-1-2 0-1 1-2 1-3 1l-4 1-1 1c-2 0-3 1-5 1h0l2-1c3-2 6-2 9-5h-1-1c-1-1-1-1-2 0h-1c-1-1-2-1-3-1l-1 1-1-1h0c1 0 1-1 2-1 2 0 3 0 5-1h3c-2-1-4-1-6-1l2-1-1-1h3v-1h1c1 1 2 1 2 1 3 0 6 1 8-1h-1c1-1 1-1 2-1v1c1 0 1 1 2 0 0-1 1-1 1-2h-4-2l-3-1c2 0 4 1 6 0z" class="B"></path><path d="M172 326h3l1 1h2v1s-1 0-1 1c-2-1-4-1-6-1l2-1-1-1z" class="F"></path><path d="M70 307h1c4-2 10-3 14-3 8-2 16-2 24-3s15-1 23-1c10 0 22 2 32 4l9 3c1 0 1 0 2 1h-1l-14-3c-7-2-14-3-22-4-10 0-21 0-31 1-12 1-24 3-36 6h-2v1h0c1 1 1 0 1 1h-1c-1 1-2 1-3 1 0-1 1-2 1-3 1 0 2 0 3-1z" class="q"></path><path d="M182 336h2v1h3c1 0 1 0 1 1h4l-1-2c2 0 3 0 5-1l1 1c1 0 1 0 2 1h1l1-1c0-1 1-1 1-1h1s0 1 1 1h1c1 1 4 1 5 0h0 1c1-1 3 0 4-1h-1 0-1c-2-1-3-1-5-1-1-1-2-1-3-2h0 1c1 0 1 1 2 1h0 3 0 0 2-1-2v-1l-2-1h0-1-1v-1h0c0-1 0-1 1-2 1 1 1 1 2 1l3 1 1-1 2 1-1 1c3 1 5 1 8 3l3 1c0 1 0 1-1 2-2-1-3-2-5-2l2 4c-1-1-3-2-3-4l-2 2h-3c0 1-2 1-2 2l-1-1h-1-1-1c-1-1-4-1-5 0l-2 2c-1-1-1-1-1-2l-2-1h-1 0l1 1v1l-2 1c-1 0-2 0-2-1h-2-3c-1-1-2-1-3-1s-1 0-1-1h0c-1 0-2 1-3 1h-2c0-1 1-2 1-3 1 1 1 1 2 1z" class="n"></path><path d="M197 337c1 0 2 0 2 1h2l1-2h1s0 1 1 1c3 1 7 0 9 0 0 1-2 1-2 2l-1-1h-1-1-1c-1-1-4-1-5 0l-2 2c-1-1-1-1-1-2l-2-1z" class="S"></path><path d="M218 335h0c-1-2-4-2-6-3s-4-2-6-2c3 0 5 1 8 1 3 1 5 1 8 3l3 1c0 1 0 1-1 2-2-1-3-2-5-2l2 4c-1-1-3-2-3-4z" class="C"></path><path d="M252 176c1-1 2-1 3-1v1 2l-1 3v2 1 2l-1 10v-4h0c0-1 1-2 0-2-1-1-1-1-2-1h-6c-2 0-3 0-5 1 0 1-1 0-2 0s-1 1-2 1h-1s-1 1-2 1l-2 2v1c1-1 1-1 2-1l1-1s1 0 1-1c1 0 2-1 3-1 1-1 3-1 4-1h1 2l1-1h3 2 1v1h1l-1 1h-1c-2 0-7 0-8 1-1 0-3 0-4 1-2 0-7 4-9 4v-1l-1-1v-1c1-2 4-4 6-5 2 0 3-1 4-2h6c1 0 3-1 4-1v-1c1 0 1-1 1-2-2 0-2 1-3 1h0c0-1 2-2 3-3l1-2c1 0 2 0 2-1 1 0 1 0 1-1-1 0-1-1-2-1z" class="n"></path><path d="M252 176c1-1 2-1 3-1v1 2l-1 3v-3h1l-1-1c-1 0-1-1-2-1z" class="u"></path><path d="M249 185l1 1c1 1 1 1 2 1v1h-3l-1-1h-3c1 0 3-1 4-1v-1z" class="f"></path><path d="M230 196c7-4 13-6 21-6v1c-2 0-7 0-8 1-1 0-3 0-4 1-2 0-7 4-9 4v-1z" class="m"></path><path d="M239 187h6 3l1 1c-6 0-14 1-18 5l-1 1c0 1 0 1-1 1v-1c1-2 4-4 6-5 2 0 3-1 4-2z" class="q"></path><path d="M138 195c3 1 4 2 7 3l4 1 1-1c2 1 3 2 4 2s2 0 3 1h1c1 1 0 1 0 2v2l-1 1s-1-1-2-1c-1-1-1-1-2-1 0 0-1 0-1-1h-2 0l1 1c1 1 1 2 2 3 0 1-1 0-2 1h-2l-1 1h1c-1 2-1 2-1 3h-1v-1l-1 1v2h-1l-1 1c0 1-1 1-1 1-1-1-1-1-1-2-1 0-1-1-1-1-1-1-2-1-2-2h0c1-1 1-1 2-1s1 0 1-1h0 1 0l1-1v1c2-1 1-2 2-3l1-1v-2h-1c-1 0-2-1-3-1s-1 0-2-1h-1-2c-2 0-1-1-2-2l1-1h-1c0-1 0-1 1-2h1v-1z" class="W"></path><path fill="#f6e070" d="M235 271h-1c-5 1-9-1-13-3l1-3c2-7 6-11 13-14v9h0v11z"></path><path d="M78 323c9-2 18-5 28-7l11-1c2 0 4 0 6-1 12-1 25 2 37 3l24 4c1 0 2 0 3 1-2 1-4 0-6 0h0l-28-5c-5-1-11-1-15-1h-1l-6-1c-18 1-34 4-51 9l-2 1v1c0 1-1 1-2 2-1-1-1-1-2-1h-3c0 1 0 2-1 2h-2c-1 1-3 2-5 3v-1-4c0-1 0-1 1-2h1v-1h3v-1h1c1 1 1 3 1 4h0c1-1 1-1 2-1l1-1 5-2z" class="P"></path><path d="M74 327l4-2v1c0 1-1 1-2 2-1-1-1-1-2-1z" class="j"></path><path d="M68 323h1c1 1 1 3 1 4h-1l-1 1h-1l1-2c-1 0-1-1 0-2v-1z" class="F"></path><path d="M63 327c0-1 0-1 1-2 1 1 1 2 2 3-1 1-1 1-1 2h-1l-1 1v-4z" class="O"></path><path d="M137 185c4 0 8 1 12 2 2 1 5 2 7 3v1l-1 1c1 0 2 1 3 2h0-1c-2 0-2 0-2 2h0l3 3v1h-1v1c-1-1-2-1-3-1s-2-1-4-2l-1 1-4-1c-3-1-4-2-7-3-1 0-2 0-2-1 1-1 0-2 0-3v-3-1h0c0-1 1-1 1-2z" class="B"></path><path d="M151 191c1 0 5 2 6 3-2 0-2 0-2 2h0l-1 1v-1c-1-1-3-2-4-3h2 0 0l-1-1v-1h0z" class="l"></path><defs><linearGradient id="M" x1="149.596" y1="199.298" x2="149.68" y2="193.22" xlink:href="#B"><stop offset="0" stop-color="#0f0d11"></stop><stop offset="1" stop-color="#262522"></stop></linearGradient></defs><path fill="url(#M)" d="M154 200c-2-2-5-4-8-5-2-1-3-1-5-3 2 0 4 1 6 1 1 1 1 1 2 1s1 1 2 1l3 2 1-1 3 3v1h-1v1c-1-1-2-1-3-1z"></path><path d="M136 188h5l6 1c1 1 3 1 4 2h0v1l1 1h0 0-2-1c-1 0-1 0-2-1h-1l-4-1c-1-1-1-1-2-1h-1c-1 0-1-1-2 0l-1 1v-3z" class="S"></path><path d="M137 185c4 0 8 1 12 2 2 1 5 2 7 3v1l-1 1c1 0 2 1 3 2h0-1c-1-1-5-3-6-3-1-1-3-1-4-2l-6-1h-5v-1h0c0-1 1-1 1-2z" class="AG"></path><path d="M147 188s-1 0-1-1h1 2c2 1 5 2 7 3v1l-1 1c-3-2-5-3-8-4z" class="J"></path><path d="M137 185c4 0 8 1 12 2h-2-1c0 1 1 1 1 1-3 0-8-2-11-1 0-1 1-1 1-2z" class="S"></path><path d="M135 172c0-1 0-2 1-3 1 0 1 1 2 1l3 3c4 2 6 5 9 6l3 3c2 1 3 2 5 3h2v2h-1c1 3 4 2 2 5v1l-1-1-1-1-1 1h-1l-1-1v-1c-2-1-5-2-7-3-4-1-8-2-12-2l-2-1h0v-3h-1l1-1v-3-5z" class="t"></path><path d="M140 176c2 1 3 3 5 4-3-1-5-1-7-2l2-2z" class="B"></path><path d="M150 179l3 3h-1c0 1 1 1 1 2h0c-2-1-3-2-5-3 1 0 2-1 2-2z" class="O"></path><path d="M153 182c2 1 3 2 5 3-1 0-3 0-3 1l-2-2h0c0-1-1-1-1-2h1z" class="f"></path><path d="M135 172c1 0 1 1 2 1v1c1 1 2 2 3 2l-2 2-2-1h-1v-5zm8 9c2 0 3 0 5 2 1 0 2 1 3 2h-1l-6-1c0-2 0-2-1-3z" class="F"></path><path d="M158 185h2v2h-1c1 3 4 2 2 5v1l-1-1c0-2-1-2-2-3 0-1 0-1-1-1l-2-2c0-1 2-1 3-1z" class="B"></path><path d="M135 172c0-1 0-2 1-3 1 0 1 1 2 1l3 3h0v2h2l-1 1h1l-1 1c-2-1-3-3-5-4-1 0-1-1-2-1z" class="S"></path><path d="M141 173c4 2 6 5 9 6 0 1-1 2-2 2-1-1-2-2-4-2 0-1-1-2-2-2l1-1h-1l1-1h-2v-2h0z" class="B"></path><path d="M135 177h1v1h0c1 0 2 0 3 1 1 0 3 2 4 2 1 1 1 1 1 3l-1-1-1-1h-3v1c-1-1-2-1-2-1v-1h-2-1l1-1v-3z" class="P"></path><path d="M135 181h2v1s1 0 2 1v-1h3l1 1 1 1 6 1h1l8 6-1 1h-1l-1-1v-1c-2-1-5-2-7-3-4-1-8-2-12-2l-2-1h0v-3z" class="J"></path><path d="M135 184h1c6 0 11 1 16 4 2 0 4 1 5 2 1 0 1 1 1 1l-1 1-1-1v-1c-2-1-5-2-7-3-4-1-8-2-12-2l-2-1z" class="i"></path><path d="M191 310v-2c2-1 3-2 4-2v1l2-1 1 1v1c1-1 2-1 2-1h4c1 0 2 0 3 1-1 0-1 0-2 1 0 0-1 0-1 1h6 1l1 2v2c0 1-1 1-1 2-2 1-3 3-5 4l-1 2-2 2h0l-2 1v2 1h0v2h0v-2h-1v-1l-2 1v-3c-2-5-7-8-10-12h0v-1c-1-1-1-1-1-2h0l1-1 3 1z" class="X"></path><path d="M191 310v-2c2-1 3-2 4-2v1l2-1 1 1v1c1-1 2-1 2-1h4c1 0 2 0 3 1-1 0-1 0-2 1 0 0-1 0-1 1h-2-11z" class="i"></path><path d="M204 307c1 0 2 0 3 1-1 0-1 0-2 1 0 0-1 0-1 1h-2l-1-2c1-1 2-1 3-1z" class="m"></path><path d="M188 312c4 0 8 1 12 1 5 0 9-1 13 0-4 1-7 1-11 2l-2 10 5-3-2 2h0l-2 1v2 1h0v2h0v-2h-1v-1l-2 1v-3l1-1c1-2 1-6 2-9-4-1-9-2-13-2h0v-1z" class="j"></path><path d="M188 313c4 0 9 1 13 2-1 3-1 7-2 9l-1 1c-2-5-7-8-10-12z" class="w"></path><path d="M131 315l6 1h1c4 0 10 0 15 1l28 5h0l3 1h2 4c0 1-1 1-1 2-1 1-1 0-2 0v-1c-1 0-1 0-2 1-4 0-10 0-14-2-2 0-7 1-9 1l-22 4h-2c-1-2-3-4-4-7l-3-6z" class="D"></path><path d="M131 315l6 1h1-5-1c1 1 2 1 3 2l2 2h0 0-1c-1 0-1 0-2 1l-3-6z" class="s"></path><path d="M134 321c1-1 1-1 2-1h1v2h0 1 1l-1 1v3c1 0 1 0 2 1v1h-2c-1-2-3-4-4-7z" class="R"></path><path d="M241 327h8l3 8c0 1 0 2 1 2v1l2 6c-3 0-5-2-8-3h-3-1l-2-1s-1-1-2-1h0c-3-1-6-3-8-3-2-1-4-1-5-2s-2-1-4-1v1c-3-2-5-2-8-3l1-1h1 1c-1-1-2-1-3-2 0 0 1 0 2-1 2 2 5 0 7 2v1s1-1 1-2h2 1l8-1h6z" class="k"></path><path d="M224 328h2 2l1 2c-2-1-3-1-5-2z" class="u"></path><path d="M227 328l8-1v2c1 1 2 1 3 2h0v1c-3-1-5-2-7-2h-2l-1-2h-2 1z" class="J"></path><path d="M228 328h3v2h-2l-1-2z" class="n"></path><path d="M216 327c2 2 5 0 7 2l-6 1c2 1 3 2 5 3h0v1c-3-2-5-2-8-3l1-1h1 1c-1-1-2-1-3-2 0 0 1 0 2-1z" class="f"></path><path d="M244 335h2c2 1 5 1 7 3h0l2 6c-3 0-5-2-8-3h-3c-1-1-2-1-2-2h0v-1-1l1 1c1-1 1-1 1-2v-1z" class="l"></path><path d="M244 335h2c0 1-1 3-1 4l-2-1c1-1 1-1 1-2v-1z" class="S"></path><path d="M242 339h0v-1-1l1 1 2 1h1l1-1 1 1c0 1 0 1-1 2h-3c-1-1-2-1-2-2z" class="AF"></path><path d="M242 339l-7-3c-2-1-5-1-7-3v-1h-1v1h0c-1 0-2-1-3-1v-1h0c1-1 2-1 3 0 1 0 3 0 4 1l13 3v1c0 1 0 1-1 2l-1-1v1 1h0z" class="B"></path><path d="M241 327h8l3 8h-1c-2 0-5-1-8-2l-5-1v-1h0c-1-1-2-1-3-2v-2h6z" class="j"></path><path d="M131 238l1 1h1 1c0 1 1 1 1 2h-2v1 1l-2 2 1 1h1l1 1h-3v1c0 1 1 2 2 3h0 1c0 1-1 2-1 2h-2v-1 2h1c0 1 0 1-1 2 2 0 2 1 3 2v1c1 0 1 0 2 1 0 0 1 0 1 1 1 0 2 1 3 2l1 1c1 0 1 1 2 1l1 1-1 1v-1h-3v-1h-1c-1-2-2-3-4-4l-2-2h-1c-1 0-1-1-1-1v2h1v1c-1 1 0 2 0 3h0l1 2c-1-1-2-1-3-2l-1 1 1 1 3 2c1 1 1 1 2 1l1 2h-2v1l1 1v1l-1-1c-1-1-4-2-5-3h-1c1 1 10 8 10 9h0v1h0l1 1c1 0 2 0 3 1v1h-1c1 1 2 1 3 1-2 1-3 1-5 0s-3-2-5-4c0 0-1 0-2-1h0-1l1 1h-1v-2c-1 0-1 0-2-2h0l1 1 1-1c0-1 0-1-1-1l-2-2h-1l-1-1c0-1-1-2-2-3v-3-1h-1 0c1-2 0-4 0-7v-3-1h1v-1h0v-2-1h0-1v1c0-1-1-1-1-1 1-4 2-7 3-10l1-1 1 1v-1h0 2c1 0 1-1 2-1z" class="T"></path><path d="M129 258h1v2h-2c-1 0-1 0-1-1 0 0 1 0 2-1h0z" class="R"></path><path d="M124 253c1-1 1-2 1-3h1v1c0 1 1 1 1 2h-3z" class="s"></path><path d="M126 257h0-2c-1-1 0-2 0-3h1l2 2s-1 0-1 1h0z" class="l"></path><path d="M125 254c1 0 2-1 3 0 0 1 0 1 1 2 0 1-1 1-1 2-1 0-1-1-2-1h0c0-1 1-1 1-1l-2-2z" class="R"></path><path d="M130 266c-1 0-2 0-3 1h0v-1-1h0c0-1 0-1 1-2v-1h1 0c0 1 0 1 1 2l-1 1 1 1z" class="n"></path><path d="M125 240c1 1 1 1 1 2-1 1-1 2-1 3l1-1h2c1 0 1 0 2 1v2h-1v-1-1h-1c-1 1-1 2-2 4l1 1h3v3l1 1-1 1h-1v-4h-3v-1h-1c0 1 0 2-1 3v-2-1h0-1v1c0-1-1-1-1-1 1-4 2-7 3-10z" class="l"></path><path d="M153 167c2-1 4-1 7-2 4 2 6 3 9 7h1c0 1 2 3 2 5 1 1 2 2 2 3s0 2 1 3h0v2c0 1 1 2 1 3h-1-3-3v-2h-2v-1l1-1c-2 1-3 2-4 2l-4-1h-2c-2-1-3-2-5-3l-3-3c-3-1-5-4-9-6l-3-3h1l1 1 2-2 1 2 2-1h2c1 0 1 1 2 1l4-4z" class="t"></path><path d="M145 171l3 1-2 2h-1v-3zm5 4l-1 1c-1 0-1 0-2-1 0-1 1-2 2-2l1 1v1z" class="l"></path><path d="M145 170h2c1 0 1 1 2 1l-1 1-3-1h-2l2-1z" class="O"></path><path d="M142 169l1 2h2v3c-1-1-3-1-4-2 0-1 0-1-1-1l2-2z" class="R"></path><path d="M166 179v-1c1 1 2 2 3 2 1 1 0 1 0 1 0 1-1 2-2 3h-2-2c0-1 0-2 1-2v-1c0-1 1-2 2-2zm0-5l1 1v-1c-2-2-4-5-6-6l-2-2c4 1 7 3 10 7h0c0 1 1 1 1 2l1 2v3h-1l-2-1c-1-1-1-2-2-3 0-1-1-1-1-1h-2c1-1 2-1 3-1z" class="u"></path><path d="M169 172h1c0 1 2 3 2 5 1 1 2 2 2 3s0 2 1 3h0v2c0 1 1 2 1 3h-1-3-3v-2h-2v-1l1-1c1-1 2-2 4-3v-1h-1v-3l-1-2c0-1-1-1-1-2h0v-1z" class="R"></path><path d="M167 185h4c0-1 1-2 1-3h0c1 1 1 1 1 2l-1 1c1 0 2 0 2 1 1 1 1 1 1 2h-3-3v-2h-2v-1z" class="z"></path><path d="M167 186c1 0 2 0 2-1l3 3h-3v-2h-2z" class="P"></path><path d="M151 170c2-2 4-3 7-3 1 0 2 1 2 1 2 1 4 4 6 6-1 0-2 0-3 1h2v1h-3c-1-1-1-1-2-1l-1 1h-1-1c-1 0-1-1-2-1-1-1-1-1-2-1h0v-1-1l-2-2z" class="B"></path><path d="M149 173c0-1 1-2 2-3l2 2v1 1h0c1 0 1 0 2 1 1 0 1 1 2 1h1 1l1-1c1 0 1 0 2 1h3v-1s1 0 1 1h0v2c-3 1-4 4-5 6-1-1-1-1-1-2h-1v1h-1l-1-1h1l-1-1-1 1-1-1s-1-1-2-1h0l-1-2-1 1-2-2 1-1v-1-1l-1-1z" class="J"></path><path d="M158 176l-1 1c0 1-1 1-2 1-1-1-1-1-1-2 1-1 2 0 3 0h1z" class="z"></path><path fill="#f6e070" d="M177 268c-8 2-17 3-24-1-6-3-10-9-12-15 6-5 14-4 21-3-2 7-2 12-1 19l1-1c3-5 4-10 3-16v-1c3 2 5 3 8 5 2 3 3 5 4 7v6z"></path><path d="M126 206h1 0v1c1 1 2 2 3 2v2h1 1 0l1 1v1c1 1 2 0 3 1 0 1 0 2-1 3h0l-2 4-2 1-1 2-1 2-3 3-5 5c-1 0-2 0-3-1v-1l-2 2c-1 2-2 3-4 4-1 0-2 1-3 1v4 1s-1 0-1 1c0-2 0-4-1-5h0v-2-4h1v-2h-1v-1s0-1 1-1h0c-1-2-1-4-1-6h-1c1-1 1-2 1-4 0 0-1 0-1-1l1-1v-2h-2-1v-1c1 0 2 0 2-1l1-1v-4h0l2-2-1-1h7 1c1 0 1 0 2 1h0 0c1-1 1-1 2-1 2 1 4 0 6 0z" class="i"></path><path d="M120 210c1-1 2-1 3-1 0 1 1 1 1 2h-2l-2-1z" class="E"></path><path d="M124 213l1-1 2 2-1 1h-1c-1 0-1-1-1-1v-1z" class="G"></path><path d="M112 233l1-1c1 1 1 1 1 2s0 1-1 1h-1s0-1-1-1l1-1z" class="Q"></path><path d="M122 228h-2v-1c1-1 1-1 2-1l1 1h1c-1 1-1 1-2 1z" class="E"></path><path d="M125 210l2-1c1 1 2 1 2 2s-1 1-2 1-1-1-2-1v-1z" class="G"></path><path d="M124 216h3v1l-1 1h-1c-1 0-1 0-2-2h1z" class="H"></path><path d="M112 220h1l2 2-1 1-1 1c-1-1-1-1-1-3v-1z" class="E"></path><path d="M116 217c0-1 0-1-1-2l1-1h2c1 0 1 1 1 2h0l-3 1z" class="Q"></path><path d="M130 216h-1c-1-1-1-2-1-3h2l2 3h-2z" class="G"></path><path d="M124 224c0-1-1-2 0-4h1c1 1 1 2 2 3h0l-2 2v-1h-1z" class="b"></path><path d="M112 215h2c0 1 1 1 1 3l-1 1h-2l-1-1c0-2 1-2 1-3z" class="I"></path><path d="M115 229h1c1 0 1 0 2 1 0 1-1 2-2 3h-1c0-1-1-2-1-3l1-1z" class="Y"></path><path d="M118 207h0c1 1 1 1 1 2v1 2c-1-1-2-1-3-2v-2c1-1 1-1 2-1z" class="G"></path><path d="M120 218c1 0 1-1 2-1 1 1 1 2 1 3-1 1-1 1-1 2-1 0-2-2-3-2l1-1v-1z" class="d"></path><path d="M119 216l1 2v1l-1 1s-1-1-2 0h-2 0v-1-1h2l1-1h-2l3-1h0z" class="L"></path><path d="M117 221h1c1 0 2 1 2 2v1h-2c-1 0-1 0-2-1 0-1 0-1 1-2z" class="E"></path><path d="M122 211h2v2h-1-1v1s1 0 1 1l-1 1c-2-1-2-1-3-2 0-1-2-1-3-1v-1h3l1 1c0-1 1-1 2-1v-1z" class="G"></path><path d="M132 216h1l1-1c-1 0-3-1-3-3l1-1 1 1v1c1 1 2 0 3 1 0 1 0 2-1 3h0c-1 0-2 0-3 1-1 0-1-1-2 0l-1-1s0-1 1-1h2z" class="H"></path><path d="M112 208l-1-1h5v1c-1 1-2 1-3 2h0c0 1 1 1 1 1 1 0 1 0 1 1s-1 2-1 2h-1c-1 0-2-1-2-2l2-2-1-2z" class="E"></path><path d="M118 207c1-1 1-1 2-1 2 1 4 0 6 0l1 3h0l-2 1c0-1-1-1-1-2l-1 1c-1 0-2 0-3 1h-1v-1c0-1 0-1-1-2z" class="M"></path><path d="M116 206c1 0 1 0 2 1h0c-1 0-1 0-2 1h0v-1h-5l1 1c-2 1-1 3-3 5l-2 1v-1-4h0l2-2-1-1h7 1z" class="K"></path><path d="M130 218c1-1 1 0 2 0 1-1 2-1 3-1l-2 4-2 1-1 2-1-1c0-1 0-1-1-1-1-1-2-2-2-3 1-1 2-1 3-2l1 1z" class="b"></path><path d="M130 218c1-1 1 0 2 0 1-1 2-1 3-1l-2 4-2 1h0-2 0c0-1 1-2 1-2v-1-1z" class="C"></path><path d="M107 220v1 2h1l1-1c2 1 3 3 5 3 0 1 1 2 1 3l-1 1h-1c-1-2-3-4-5-5v1s1 1 2 1v1 1l-1 1c0 1-1 1-1 1-1-2-1-4-1-6h-1c1-1 1-2 1-4z" class="d"></path><path d="M107 220s-1 0-1-1l1-1v-2h-2-1v-1c1 0 2 0 2-1l1-1v1l2-1 2 1v2l-2 1 1 1c1 1 1 1 1 2v1h-4v-1z" class="E"></path><path d="M110 227c1 1 2 2 2 3s-1 2-1 3h1l-1 1c1 0 1 1 1 1-1 2-1 2-2 3h-1v1 4 1s-1 0-1 1c0-2 0-4-1-5h0v-2-4h1v-2h-1v-1s0-1 1-1h0s1 0 1-1l1-1v-1z" class="L"></path><path d="M129 223l1 1-1 2-3 3-5 5c-1 0-2 0-3-1v-1h1 1c1-1 0-1 0-3h2v-1c1 0 1 0 2-1v-1c-1 0-3-1-3-1v-1h3 1v1l2-2 1 1 1-1z" class="Q"></path><path d="M127 223l1 1-3 3v-2l2-2z" class="X"></path><path d="M80 324c17-5 33-8 51-9l3 6c1 3 3 5 4 7-18 2-37 6-53 14l-7 4c-1 0-2 1-2 1h-3c-2-2 0-1-1-3 0-1-1-1-1-2 1-1 1-2 2-2v-1c1-1 1-3 2-4h0l1-1c0-2 0-3-1-4l1-2c1-1 2-1 2-2v-1l2-1z" class="D"></path><path d="M78 325l2-1v4h-1v-1l-1-1v-1zm-5 15v-1c1-1 1-3 2-4v1l2 2c-2 1-2 0-3 1v2 1c0-1-1-2-1-2z" class="W"></path><path d="M76 347h-3c-2-2 0-1-1-3 0-1-1-1-1-2 1-1 1-2 2-2 0 0 1 1 1 2h-2c0 1 0 1 1 2h1 0-1v2c1 1 1 0 2 0h2 1c-1 0-2 1-2 1z" class="T"></path><path d="M140 328l22-4c2 0 7-1 9-1 4 2 10 2 14 2h1c-2 2-5 1-8 1 0 0-1 0-2-1h-1v1h-3l1 1-2 1c2 0 4 0 6 1h-3c-2 1-3 1-5 1-1 0-1 1-2 1h0s-3 0-4 1h-2-1c-1 1-1 1-2 1l-1 1-1-1h0 1-1c-1 0-3 0-4 1-1 0-1 1-2 1-2 0-5 1-7 0l-1-1c-2 1-5 1-8 2-4 1-7 3-11 5-13 6-26 13-38 20l-7 4 1 1-1 1h-1 0l-1 2c-1 0-1 0-2 1l-1 1h-1l-1-2v-1c0-1-1-2-1-4l-1-1c0-1 0-2-1-2v-2-2 1c2-1 3-2 5-3h0c0-1 1-2 1-3l-2-1c1 0 2-1 3-2l1-1c3-1 7-3 9-5v-1c16-8 35-12 53-14h2z" class="N"></path><path d="M73 371l-1-2c1-1 3-2 5-3l1-1 1 1-1 1h-1 0l-1 2c-1 0-1 0-2 1l-1 1z" class="J"></path><path d="M76 348c1 1 2 1 2 2v1l1 1-1 1-5 2c0-1 1-2 1-3l-2-1c1 0 2-1 3-2l1-1z" class="O"></path><path d="M76 348c1 1 2 1 2 2v1l1 1-1 1-3-1v-2-1l1-1zm66-14l14-2c6-1 12-3 18-3-2 1-3 1-5 1-1 0-1 1-2 1h0s-3 0-4 1h-2-1c-1 1-1 1-2 1l-1 1-1-1h0 1-1c-1 0-3 0-4 1-1 0-1 1-2 1-2 0-5 1-7 0l-1-1z" class="T"></path><path d="M74 356l7-3c0 1 0 2-1 3v1 1c2 0 2 0 3 1l1 1c-2 2-5 3-6 4s-2 2-3 2c-1 1-3 2-4 2h0c0-1-1-2-1-4l-1-1c0-1 0-2-1-2v-2l6-3z" class="u"></path><path d="M76 357h0 2 0v1h-1c-1 1-1 1-2 1v-1l1-1z" class="S"></path><path d="M74 356l7-3c0 1 0 2-1 3v1 1c2 0 2 0 3 1l1 1c-2 2-5 3-6 4-1-1-2-1-2-3h2v-3-1h0-2 0l-2-1z" class="W"></path><path d="M81 353c3-2 7-4 11-5 11-5 22-7 33-10l-41 22-1-1c-1-1-1-1-3-1v-1-1c1-1 1-2 1-3zm59-25l22-4c2 0 7-1 9-1 4 2 10 2 14 2h1c-2 2-5 1-8 1 0 0-1 0-2-1h-1v1h-3l1 1-2 1h-2l-15 3-11 2-15 4-17 3c-11 3-22 7-32 12l-1-1v-1c0-1-1-1-2-2 3-1 7-3 9-5v-1c16-8 35-12 53-14h2z" class="D"></path><path d="M169 328h0v-1h-1l1-1h3l1 1-2 1h-2z" class="B"></path><path d="M140 328l22-4c2 0 7-1 9-1 4 2 10 2 14 2h1c-2 2-5 1-8 1 0 0-1 0-2-1h-1-3v-1c-4 0-9 2-13 2-18 3-36 5-54 10-7 1-13 4-20 7v-1c16-8 35-12 53-14h2z" class="m"></path><path d="M156 333h0l1 1 1-1c1 0 1 0 2-1h1 2c1-1 4-1 4-1l1 1 1-1c1 0 2 0 3 1h1c1-1 1-1 2 0h1 1c-3 3-6 3-9 5l-2 1h0l-8 4-10 4c-12 5-22 12-33 18-5 3-10 6-15 10h1c-1 1-2 2-3 2l-5 3c-1 1-1 1-2 1 0 1-1 1-2 2-1 0-1 1-2 1l-1 1c-1 0-2 1-3 2h-1c-2 0-1 2-4 0 1 0 2-2 3-2h0c1-1 2-1 2-2-1-1-1 1-2 1h-1c-2 0-1 1-3 0 0-1-1-3-2-4 0-1 0-1-1-2h0c0-2 0-3-1-4l-1 1v-3h1l1-1c1-1 1-1 2-1l1-2h0 1l1-1-1-1 7-4c12-7 25-14 38-20 4-2 7-4 11-5 3-1 6-1 8-2l1 1c2 1 5 0 7 0 1 0 1-1 2-1 1-1 3-1 4-1h1-1z" class="D"></path><path d="M78 365l7-4 1 1c0 1-1 1-1 2h-2c-1 1-1 1-2 1v1h-1l-1 1h1c1 1 1 1 2 1h1 3 0l-1 2 1 1-2 2s-1 0-1 1h-2l-1-1h-1l-1-1h-1 0c1-1 1-1 1-2h-1 0-2l1-1 1-2h0 1l1-1-1-1z" class="T"></path><path d="M156 333h0l1 1 1-1c1 0 1 0 2-1h1 2c1-1 4-1 4-1l1 1 1-1c1 0 2 0 3 1h1c1-1 1-1 2 0h1 1c-3 3-6 3-9 5l-2 1c-1 0-1 0-2 1h-1-1c-1 0-2 1-4 1l-1 1-6 1c0 1-1 1-1 1h-1l-3 1h-2l-1 1h-1-1-1c-1 1-2 1-3 1s-1 1-1 1h-1-1l-2 1h-1l-2 1-1 1h0c-1 0-2 0-3 1-1 0-2 0-3 1l-3 1-3 1c-1 1-1 1-2 1-2 1-5 2-7 4l-3 2-1-1 2-2h1c1-1 1-1 2-1s1-1 2-1l1-1h1c1-1 2-1 3-2h2c0-1 1-1 1-1h1l1-1c1 0 1 0 2-1 1 0 2 0 2-1 3-1 7-2 10-3 1 0 1 0 2-1h1l4-1s1 0 1-1h1 2c1 0 0 0 1-1h2c2-1 5-2 8-2l-1-2h-1l-4 1h-1-1l-4 1-2 1h-1c-2 1-3 1-4 2l-9 3-6 3-1 1c-1 0-1 1-2 1l-1-1c1-1 3-1 5-2 1-1 3-2 4-2 3-2 6-3 9-4 1 0 2-1 2-1 1 0 2-1 3-1 2 0 4-1 5-2h1 2l1-1h1v-1c0-1 1-1 1-2h1 1 0c1 0 1 0 2-1z" class="y"></path><path d="M154 338l1-1h2l2-1h3l1 1-1 1-3 1h-1c-1 0-1 0-2 1h0l-1-2h-1z" class="B"></path><path d="M156 333h0l1 1 1-1c1 0 1 0 2-1h1 2c1-1 4-1 4-1l1 1 1-1c1 0 2 0 3 1-1 0-2 0-3 1h0-3-2c-1 1-1 1-2 1l-3 1h-2v1h-1-1l-4 1v-1c0-1 1-1 1-2h1 1 0c1 0 1 0 2-1z" class="j"></path><path d="M166 338h0l-8 4-10 4c-12 5-22 12-33 18-5 3-10 6-15 10h1c-1 1-2 2-3 2l-5 3c-1 1-1 1-2 1 0 1-1 1-2 2-1 0-1 1-2 1l-1 1c-1 0-2 1-3 2h-1c-2 0-1 2-4 0 1 0 2-2 3-2h0c1-1 2-1 2-2-1-1-1 1-2 1h-1c-2 0-1 1-3 0 0-1-1-3-2-4 0-1 0-1-1-2h0c0-2 0-3-1-4l-1 1v-3h1l1-1c1-1 1-1 2-1l-1 1h2 0 1c0 1 0 1-1 2h0 1l1 1h1l1 1h2c0-1 1-1 1-1l2-2c2-1 4-3 6-4l2-2h1l2-2c1 0 2-1 3-1l1-1 2-1 1 1 3-2c2-2 5-3 7-4 1 0 1 0 2-1l3-1 3-1c1-1 2-1 3-1 1-1 2-1 3-1h0l1-1 2-1h1l2-1h1 1s0-1 1-1 2 0 3-1h1 1 1l1-1h2l3-1h1s1 0 1-1l6-1 1-1c2 0 3-1 4-1h1 1c1-1 1-1 2-1z" class="D"></path><path d="M89 376v-1h0l-1-1v-1h0c1 0 2 0 2 1v1c1 0 1 1 1 1v2 1h0c-1 1-1 1-2 0l-1-1 1-1v-1z" class="T"></path><path d="M91 379c2-1 4-2 6-4l1-1h2 1c-1 1-2 2-3 2l-5 3c-1 1-1 1-2 1 0 1-1 1-2 2-1 0-1 1-2 1h0c0-1 1-1 1-2 1 0 1-1 1-1-1-1-1-1-2-1v1h-1 0c-1-1-2-1-3 0v-1h0c1-1 2-2 2-3h2 1 1v1l-1 1 1 1c1 1 1 1 2 0h0z" class="R"></path><path d="M85 376h2c0 1-1 1-1 1v1 2c-1-1-2-1-3 0v-1h0c1-1 2-2 2-3z" class="J"></path><path d="M86 371c2-1 4-3 6-4l2-2h1l2-2c1 0 2-1 3-1l1-1 2-1 1 1c-1 0-2 1-3 1-1 1-2 2-3 2-1 1-2 2-3 2-1 1-1 2-2 2-1 1-2 1-3 2-1 0-1 1-2 1 0 1-1 1-1 1-1 1-2 1-2 2h0 2 0l-2 2c0 1-1 2-2 3h0v1c1-1 2-1 3 0h0 1v-1c1 0 1 0 2 1 0 0 0 1-1 1 0 1-1 1-1 2h0l-1 1c-1 0-2 1-3 2h-1c-2 0-1 2-4 0 1 0 2-2 3-2h0c1-1 2-1 2-2-1-1-1 1-2 1h-1c-2 0-1 1-3 0 0-1-1-3-2-4 0-1 0-1-1-2h0c0-2 0-3-1-4l-1 1v-3h1l1-1c1-1 1-1 2-1l-1 1h2 0 1c0 1 0 1-1 2h0 1l1 1h1l1 1h2c0-1 1-1 1-1l2-2z" class="S"></path><path d="M83 380c1-1 2-1 3 0h0v1h-3v-1zm-7-11l-1 1c0 1-1 2-2 2v1l-1 1v-3h1l1-1c1-1 1-1 2-1zm7 10c-1 0-1 1-2 2h-1v-2c1 0 1 0 1-1 1-1 1-1 1-2h0v-1c1 0 2-1 3-1h0 2 0l-2 2c0 1-1 2-2 3h0z" class="B"></path><path fill="#c31f21" d="M314 289v-13l77 92v1l5 6 11 13c3 4 5 7 9 10h1l1 1h0c1 1 2 2 2 3h1c1 0 2 2 3 3 1-1 1-2 1-3v-1h0v-2-3h3 3c1-1 2-1 3-2h0 1l2-1h1 2 4c2-1 3-1 5 0l4-1h-1v-3c3-1 4-4 7-5h0v1 3h0c1 1 1 1 1 3h0v1h1 8 3c1 0 2 1 4 1h0l-1 1v1l1 1h0l-1 1h1s1 0 1 1h1v-1-1l2 1c-1 2 1 4 2 6l1 1v1h0c1 1 1 2 1 3 1 1 2 4 3 5h2c1 1 1 2 1 4 0-1 0-1 1-1h1 2c3 1 7 1 11 1v1h1c1 0 5 1 7 0v-1 3h1c2-1 3 0 5 0h-3c-1 1 0 3 0 4v5c1 0 1 1 1 2s0 2 1 3h0c0 1 0 2 1 3v3 1 2l1-1c0 1 0 2-1 3v2c-1 2-1 3-2 4l2 1-1 2-1 1h-1v2h2l-2 2-2 3h0v1h0 0c1 0 1 0 2-1h1 1l-1 1 1 2-2 1h-2-4c-4-1-8-1-11 2h-1v1l-3 3c-1 0-1-1-2-1l-3 2v1c1 2 0 5-1 6 0 1-1 1-1 2-1 0-1 1-2 2h1c-2 3-3 6-3 9-1 6 0 11 3 16-3 4-4 8-3 13 1 6 4 11 7 15-3 3-6 5-7 10s1 11 4 16l3 9c0 6-3 11-5 17 0 1-1 2-1 4l-13 11c-28 20-59 33-92 39-6 1-13 3-19 2-1 0-2-1-3-1-1-3-3-4-5-5l-5-4v1h-1c-1 1-1 1-2 1s-1 1-2 1c1 1 1 2 1 2h-4 0c-1 0-1-1-2-1l-1-1c0 1-1 2-2 2h0l-1-2h0v-5h-1l1-1h-1v-2h-4v-1h-1c0 1-1 1-1 1h-4 0-3v-1l-5-1h-4 0-1v-1c0-2-1-3 0-4h0c0-1 0-1 1-2h0v-1h1c-1 0-1 0-2-1l-1-1v-3-2-3-1-2-1l-9-6c-4-2-7-4-11-7l20 3h1 2c3 1 7 1 11 1h10 0v-7-1-4l1-8 6-1c-1-1-1-3-1-4v-12c1-2 3-4 4-7 1-2 1-5 2-8h6 0c-1 0-3 0-4-1v-1c-1-1-1-1-1-2h0l-1-1v-2c-1-2-2-6-3-8v-1-1l-1-2h0c0-1-1-2-1-3-1-2-1-3-2-5v-4h1v-4c-1-1-1-2-1-3v-7l4 3 1 1h2 2l1-1h1c0-2 0-2 1-3 1 1 1 1 2 1v-1l-2-2c-1 0-1-1-1-2 0 0 1 0 1-1 1 0 2 0 2-1l1-1c0 1 1 1 2 1 0-1 0-1 1-2h1l1 1h2l2-2h1v-1 2h1l2-1-1-1c1-1 2-1 4-1v-1l7-8v-1c1-1 1-3 1-5v-8-5c0-1-1-1-1-1h-2 0c-1 1-2 0-2 0h-1c2-1 3-1 4-2l2-2v-1c1-1 0-8 0-9v-3-1c-1-2 0-4-1-5l-1-2 3-5c0-2 1-3 3-5 0-2 1-3 2-5s5-5 5-7v-1c-2-4-7-10-10-12l-2-2c0-2-45-56-49-59-3-5-8-11-12-14-1-3-1-8 0-12v-4c5-1 11-1 16-1 3 1 5 1 8 1v-1c-2-1-5-1-7-1-5 0-11-2-17-1h0c0-3 0-16-1-17z"></path><path d="M390 548h1c0 2 0 4-2 6l-2-4c1-1 1-2 3-2z" class="X"></path><path d="M388 412l2 2-1 1c-1 0-1 2-2 3-2 2-3 3-4 5l-2 3v-1c0-2 1-3 2-5s5-5 5-7v-1z" class="w"></path><path d="M334 624c5 3 10 7 14 11v1c-1-1-2-1-2-2-1 0-2-1-3-1 0-1-2-2-2-2-1 0-1 0-1-1-1 0-1-1-2-1h0c-1-1-1-2-2-2 0 1 0 1-1 2h0l-1-1h0v-4z" class="M"></path><path d="M386 564c-1 3-3 7-6 10-1-2-1-2-1-3 1-3 5-6 7-7z" class="H"></path><path d="M309 607c3 1 5 2 7 4l6 4c-3 0-7-1-9 0h-4 0v-2-3-1-2z" class="P"></path><path d="M311 612c2 0 4 0 6 1v1h-5-2l-1 1h0v-2l2-1z" class="r"></path><path d="M309 607c3 1 5 2 7 4h-5l-1 1h1l-2 1v-3-1-2z" class="p"></path><path d="M335 629h0c1-1 1-1 1-2 1 0 1 1 2 2h0c1 0 1 1 2 1 0 1 0 1 1 1 0 0 2 1 2 2 1 0 2 1 3 1 0 1 1 1 2 2h-1c-1 1-1 1-2 1s-1 1-2 1c1 1 1 2 1 2h-4 0c-1 0-1-1-2-1l-1-1c0 1-1 2-2 2h0l-1-2h0v-5h-1l1-1v-4l1 1z" class="d"></path><path d="M335 629h0c1-1 1-1 1-2 1 0 1 1 2 2h0c1 0 1 1 2 1 0 1 0 1 1 1 0 0 2 1 2 2h0c-1 0-1 0-2-1l-2-1v1s1 1 1 2c-1 0-1 0-2 1h0v-1h-1s-1 1-2 1v-2h-1-1l1-1v-4l1 1z" class="C"></path><path d="M335 629h0c1-1 1-1 1-2 1 0 1 1 2 2h0c-1 1-1 2-2 3l-1-1v-2z" class="N"></path><path d="M309 615h4c2-1 6 0 9 0 2 1 3 2 5 3l4 4 2 2h1v4h0v4h-1v-2h-4v-1h-1c0 1-1 1-1 1h-4 0-3v-1l-5-1h-4 0-1v-1c0-2-1-3 0-4h0c0-1 0-1 1-2h0v-1h1c-1 0-1 0-2-1l-1-1v-3h0z" class="F"></path><path d="M311 621h2c0 1 0 2-1 2h-2c0-1 0-1 1-2z" class="p"></path><path d="M313 620h7v1c-2 1-5 0-7 0h-2 0v-1h1 1z" class="r"></path><path d="M313 619c2-1 3-1 4-1h10l4 4 2 2c-2-1-5-1-7-1h-1v-2-1h-5 0-7v-1z" class="P"></path><path d="M309 615h4c2-1 6 0 9 0 2 1 3 2 5 3h-10c-1 0-2 0-4 1v1h-1c-1 0-1 0-2-1l-1-1v-3h0z" class="z"></path><path d="M310 619c1-1 2-1 3 0v1h-1c-1 0-1 0-2-1z" class="F"></path><path d="M315 624c1-1 9-1 11-1s5 0 7 1h1v4h0v4h-1v-2h-4v-1h-1c0 1-1 1-1 1h-4 0-3v-1l-5-1h5c-1-1-1-1-2-1h0-2-1 6s-1 0-2-1h-2-1c-1 0-1 0-2-1-1 0-1 0-1-1h2z" class="AF"></path><path d="M326 626c0 1 1 1 2 2h-1-3v-1l2-1z" class="F"></path><path d="M326 626h5 1 1v2h1 0v4h-1v-2h-4v-1l-1-1c-1-1-2-1-2-2z" class="z"></path><path d="M330 627l3 1v1h-2s-1-1-2-1l1-1z" class="p"></path><path d="M332 626h1v2l-3-1h-1v-1h2 1z" class="AG"></path><path d="M315 624c1-1 9-1 11-1s5 0 7 1h1v4h-1v-2h-1l-1-1h-1c-2-1-4-1-6-1l-1 2h-7c-1 0-1 0-2-1-1 0-1 0-1-1h2z" class="q"></path><path d="M315 624h9l-1 2h-7c-1 0-1 0-2-1-1 0-1 0-1-1h2z" class="p"></path><path d="M377 543h3 2 0v-2l1-4v7l-1 1 1 1c1 0 2 0 2 1l2 3 2 4v2c0 1-1 3-2 5h0 0c0 1-1 2-1 3-2 1-6 4-7 7 0 1 0 1 1 3l-2 3h0c-2 2-4 4-6 5-2 2-4 3-5 4l-8 5-2 1-3 1c-6 3-14 4-21 4v-7-1-4l1-8 6-1c-1-1-1-3-1-4v-12c1-2 3-4 4-7 1-2 1-5 2-8h6l22 1v-1-3c1-1 1-2 1-2l1-1 1 1h2l-2 3h0 1z" class="Y"></path><path d="M379 571c0 1 0 1 1 3l-2 3h0l-1-1h0c-1 0-2 1-3 1 1-1 1-1 0-2h0l1-2h2l1-1 1-1z" class="E"></path><path d="M371 554l1 1 3-3c2 2 4 5 6 7 1 2 2 3 4 5-2 1-7 5-7 7l-3 2-1-1c1-1 2-1 2-2l-1-1c0-1-1-2-1-3l-1-1h0 0c1 1 2 1 2 2 1 1 1 2 2 2s3-2 4-3 2-1 2-2-1-2-2-3l-1-1c0-1 1 0 0-1h0l-2-3h-2c-1 0-1-1-2-2 0 1-1 1-1 1-1 1-1 1-2 0v-1zm3 23c1 0 2-1 3-1h0l1 1c-2 2-4 4-6 5-2 2-4 3-5 4l-8 5-2 1c0-1-1-1-1-2l-1-1v-1-1c-1-1-1-2-2-3v-1l-1-1c0-1 0-2-1-3l1-1v1l1-1c2 0 3 1 5 0l1-1 1 1v1c1 0 2 1 2 2 1 2 2 3 2 4v2c1-1 2-1 2-1v-1h0c0-1 0-1-1-2h0c-1-1-2-4-3-6h5 2c2 1 4 1 5 0z" class="M"></path><path d="M356 590v-1h2 0c0 1 0 1 1 2l-2 1c0-1-1-1-1-2z" class="Q"></path><path d="M377 543h3 2 0v-2l1-4v7l-1 1 1 1c1 0 2 0 2 1l2 3 2 4v2c0 1-1 3-2 5h0 0c0 1-1 2-1 3-2 1-6 4-7 7l-1 1-1 1 1-2c0-2 5-6 7-7-2-2-3-3-4-5-2-2-4-5-6-7l-3 3-1-1c0-2 1-2 2-3l-2-1 1-1v-1s1 0 1-1v-1-1-3c1-1 1-2 1-2l1-1 1 1h2l-2 3h0 1z" class="R"></path><path d="M382 557c1 1 2 1 3 2h1 0v3h-1c-1-1-2-3-3-5z" class="E"></path><path d="M387 554v-1h0l2 3c0 1-1 3-2 5h0 0v-7z" class="w"></path><path d="M377 551l1-1v-1s1-1 2-1v2c-1 2 1 5 1 6h0l-5-4c0-1 1-1 1-1z" class="I"></path><path d="M385 551c1 1 1 2 1 3v5h0-1c-1-1-2-1-3-2l-1-4h1c1 0 1 1 1 2 1 0 1 0 1 1l1-1c-1 0-1-1-1-2 1 0 1-1 1-2z" class="U"></path><path d="M383 546c1 0 2 0 2 1l2 3 2 4v2l-2-3h0v1h-1c0-1 0-2-1-3v-1c-2-2-2-2-2-4z" class="D"></path><path d="M377 543h3 2 0v-2l1-4v7l-1 1 1 1c0 2 0 2 2 4v1c0 1 0 2-1 2 0 1 0 2 1 2l-1 1c0-1 0-1-1-1 0-1 0-2-1-2h-1c0-2 0-8-1-9-1 0-2 0-3-1z" class="H"></path><path d="M385 550v1c0 1 0 2-1 2s-1-1-1-2l2-1z" class="Y"></path><path d="M375 539l1 1h2l-2 3h0 1c1 1 2 1 3 1h0v4c-1 0-2 1-2 1v1l-1 1s-1 0-1 1c-1-1-3-3-4-3v-1s1 0 1-1v-1-1-3c1-1 1-2 1-2l1-1z" class="M"></path><path d="M377 551c-1-1-1-1-1-2 1-2 2-2 3-3s0-1 1-2v4c-1 0-2 1-2 1v1l-1 1z" class="d"></path><path d="M351 545c1 7 0 15 0 23v8h-1l-4 1-6-1c-1-1-1-3-1-4v-12c1-2 3-4 4-7 1-2 1-5 2-8h6z" class="D"></path><path d="M350 576h1l2 2h0l-1 1v-1l-1 1c1 1 1 2 1 3l1 1v1c1 1 1 2 2 3v1 1l1 1c0 1 1 1 1 2l-3 1c-6 3-14 4-21 4v-7-1-4l1-8 6-1 6 1 4-1z" class="d"></path><path d="M350 576h1l2 2h0l-1 1v-1l-1 1c1 1 1 2 1 3l1 1v1c1 1 1 2 2 3v1 1l1 1c0 1 1 1 1 2l-3 1-2-2s0-1-1-2v-1c-1-1 0-1-1-1l-1-3-1-2v-1c-1-1-2-3-2-4l4-1z" class="L"></path><path d="M350 576h1l2 2h0l-1 1v-1l-1 1v-1h-2c1-1 1-1 1-2z" class="h"></path><path d="M381 426l1 1h1v-3l1-1c1-3 5-5 6-8h1c0 3-6 6-6 9 3-1 4-6 7-7l2 2v12 27 1l-1 1h1v40c0 12 1 24-1 36 0 3 0 7-1 10l-1 2h-1c-2 0-2 1-3 2l-2-3c0-1-1-1-2-1l-1-1 1-1v-7l-1 4v2h0-2-3-1 0l2-3h-2l-1-1-1 1s0 1-1 2v3 1l-22-1h0c-1 0-3 0-4-1v-1c-1-1-1-1-1-2h0l-1-1v-2c-1-2-2-6-3-8v-1-1l-1-2h0c0-1-1-2-1-3-1-2-1-3-2-5v-4h1v-4c-1-1-1-2-1-3v-7l4 3 1 1h2 2l1-1h1c0-2 0-2 1-3 1 1 1 1 2 1v-1l-2-2c-1 0-1-1-1-2 0 0 1 0 1-1 1 0 2 0 2-1l1-1c0 1 1 1 2 1 0-1 0-1 1-2h1l1 1h2l2-2h1v-1 2h1l2-1-1-1c1-1 2-1 4-1v-1l7-8v-1c1-1 1-3 1-5v-8-5c0-1-1-1-1-1h-2 0c-1 1-2 0-2 0h-1c2-1 3-1 4-2l2-2v-1c1-1 0-8 0-9v-3-1c-1-2 0-4-1-5l-1-2 3-5c0-2 1-3 3-5v1z" class="x"></path><path d="M375 435l3-5c1 2 0 3 0 5l-1 8v-1c-1-2 0-4-1-5l-1-2zm1 89l1 5c0 2 2 9 1 11h-2l-1-1c1-2 1-5 1-7 1-2 0-6 0-8z" class="H"></path><path d="M374 527c1-2 0-4 0-6l2-1v4c0 2 1 6 0 8 0 2 0 5-1 7l-1 1s0 1-1 2l1-15z" class="L"></path><path d="M377 466v9c0 1 1 2 1 3 1 4 2 10 1 14v2 1c-1 0-2-1-2 0v-2h0v-2c-1-1-1-3-1-4v-7-1c1-1 1-3 1-5v-8z" class="H"></path><path d="M374 491c1 1 2 0 2 0 1 1 0 1 1 2v2c0 8 1 17-1 25l-2 1c0 2 1 4 0 6 0-3 0-8-1-10v-1c1-2 1-4 1-6v-19z" class="d"></path><path d="M376 480v7c0 1 0 3 1 4v2h0c-1-1 0-1-1-2 0 0-1 1-2 0v19c0 2 0 4-1 6v1h0-6v-3h0c-1-2-1-6-1-9s-1-7 0-10v-2-2l-1-1c1-1 2-1 4-1v-1l7-8z" class="E"></path><path d="M366 491l-1-1c1-1 2-1 4-1l-2 1 1 10c-1-2 0-5-2-7v-2z" class="c"></path><path d="M366 493c2 2 1 5 2 7l-1 17h4 2-6v-3h0c-1-2-1-6-1-9s-1-7 0-10v-2z" class="K"></path><path d="M371 517v-8-8c0-3-1-6-1-9h1v2c1-1 1-3 0-4v-1h2v1l1 1v19c0 2 0 4-1 6v1h0-2 0z" class="L"></path><path d="M371 517c1-2 0-4 1-6 0 2 0 3 1 5v1h0-2 0z" class="G"></path><path d="M373 490l1 1v19c0 2 0 4-1 6-1-2-1-3-1-5h0v-21h1z" class="f"></path><path d="M372 511c1-2 1-5 1-6v-1c0 2 0 4 1 6 0 2 0 4-1 6-1-2-1-3-1-5h0z" class="O"></path><path d="M383 537v-6-11-72-10c0-1-1-2 0-3 1 0 2-1 3 0v24h8l-1 1h-7v59 16 6 2c-1 0-2 1-3 1v-7z" class="D"></path><path d="M386 541v-6-16-59h7 1v40c0 12 1 24-1 36 0 3 0 7-1 10l-1 2h-1c-2 0-2 1-3 2l-2-3c0-1-1-1-2-1l-1-1 1-1c1 0 2-1 3-1v-2z" class="x"></path><path d="M387 544c2 0 3 0 5 2l-1 2h-1c-2-1-2-2-3-4z" class="J"></path><path d="M386 541l1 3h0c1 2 1 3 3 4-2 0-2 1-3 2l-2-3c0-1-1-1-2-1l-1-1 1-1c1 0 2-1 3-1v-2z" class="H"></path><path d="M383 544c1 0 2-1 3-1l-1 4c0-1-1-1-2-1l-1-1 1-1z" class="I"></path><path d="M363 490v2h1l2-1v2 2c-1 3 0 7 0 10s0 7 1 9h0v3h6 0c1 2 1 7 1 10l-1 15v3 1l-22-1h0c-1 0-3 0-4-1v-1c-1-1-1-1-1-2h0l-1-1v-2c-1-2-2-6-3-8v-1-1l-1-2h0c0-1-1-2-1-3-1-2-1-3-2-5v-4h1v-4c-1-1-1-2-1-3v-7l4 3 1 1h2 2l1-1h1c0-2 0-2 1-3 1 1 1 1 2 1v-1l-2-2c-1 0-1-1-1-2 0 0 1 0 1-1 1 0 2 0 2-1l1-1c0 1 1 1 2 1 0-1 0-1 1-2h1l1 1h2l2-2h1v-1z" class="Z"></path><path d="M342 503l1 1h2 2l1 2h-1l-1 1h1-1c-2-1-3-2-4-4z" class="x"></path><path d="M363 490v2h1v2l-1 20v-8c0-5-1-10 0-15v-1z" class="N"></path><path d="M364 492l2-1v2 2c-1 3 0 7 0 10s0 7 1 9h0v3h-16 1 8 3v-3l1-20v-2z" class="AF"></path><path d="M339 510c-1-1-1-2-1-3v-7l4 3c1 2 2 3 4 4l2 4c-3 2-6 6-10 7v-4h1v-4z" class="D"></path><path d="M373 517c1 2 1 7 1 10l-1 15v3 1l-22-1h0c-1-6 0-11 0-17 0-3-1-7 0-11h16 6 0z" class="W"></path><path d="M452 389c3-1 4-4 7-5h0v1 3h0c1 1 1 1 1 3h0v1h1 8 3c1 0 2 1 4 1h0l-1 1v1l1 1h0l-1 1h1s1 0 1 1h1v-1-1l2 1c-1 2 1 4 2 6l1 1v1h0c1 1 1 2 1 3 1 1 2 4 3 5h2c1 1 1 2 1 4 0-1 0-1 1-1h1 2c3 1 7 1 11 1v1h1c1 0 5 1 7 0v-1 3h1c2-1 3 0 5 0h-3c-1 1 0 3 0 4v5c1 0 1 1 1 2s0 2 1 3h0c0 1 0 2 1 3v3 1 2l1-1c0 1 0 2-1 3v2c-1 2-1 3-2 4l2 1-1 2-1 1h-1v2h2l-2 2-2 3h0v1h0 0c1 0 1 0 2-1h1 1l-1 1 1 2-2 1h-2-4c-4-1-8-1-11 2h-1v1l-3 3c-1 0-1-1-2-1l-3 2v1c1 2 0 5-1 6 0 1-1 1-1 2-1 0-1 1-2 2l-17-21c-3-3-6-7-8-10l-45-55h1l1 1h0c1 1 2 2 2 3h1c1 0 2 2 3 3 1-1 1-2 1-3v-1h0v-2-3h3 3c1-1 2-1 3-2h0 1l2-1h1 2 4c2-1 3-1 5 0l4-1h-1v-3z" class="X"></path><path d="M425 399v-3h3c-1 1-1 3-1 4l-2 1v-2z" class="U"></path><path d="M452 389c3-1 4-4 7-5h0v1 3h0v4-2h-1l-1 1c-1 0-3 1-4 1h-1v-3z" class="l"></path><path d="M459 385v3h0v4-2h-1l-1 1-1-1h-1c1-2 2-3 4-5z" class="G"></path><path d="M489 423c1 1 1 2 1 3v1h-20c-1 0-9 1-10 0h-1c0-1 0-3 1-3l29-1zm-2 7l2 1c1 1 1 2 1 3h0v1 4 1h0l-30 1h-1v-1c0-2 0-7 1-9l27-1z" class="D"></path><path d="M470 434h6c-1 1-2 1-3 2 0 0-1 0-1 1-1-1-1-2-2-3z" class="T"></path><path d="M460 431v4c2 1 7-1 10-1 1 1 1 2 2 3-4 1-8-1-11 0l-1 1-1 2c0-2 0-7 1-9z" class="J"></path><path d="M489 431c1 1 1 2 1 3h0v1 4 1l-1-2c-1-1-5-1-7-1h-21c3-1 7 1 11 0 0-1 1-1 1-1 1-1 2-1 3-2h9 4v-3z" class="B"></path><path d="M461 437h21c2 0 6 0 7 1l1 2h0l-30 1h-1v-1l1-2 1-1z" class="D"></path><path d="M459 388c1 1 1 1 1 3h0v1h1 8 3c1 0 2 1 4 1h0l-1 1v1l1 1h0l-1 1h1s1 0 1 1h1v-1-1l2 1c-1 2 1 4 2 6l1 1v1h0c1 1 1 2 1 3 1 1 2 4 3 5h2c1 1 1 2 1 4 0 1 0 1-1 2h-2l-13 1c-4 1-10 0-14 0l-1-14c0-3 0-7 1-10l-1-4v-4z" class="K"></path><path d="M470 411c0-1 1-1 1-1 1 0 1 0 2 1h-2 0-1z" class="N"></path><path d="M470 411h1l-1 1c-1 1-1 1-2 0v-1h2z" class="AC"></path><path d="M474 411v-1c0-1 1-1 2-2 0 1 0 2 1 2l-3 1z" class="C"></path><path d="M462 403v3l-2 2h0v-2-3h2z" class="AA"></path><path d="M468 408h2v1c0 1-4 1-5 1h-2l-1-1h1 1c2 0 3 0 4-1z" class="c"></path><path d="M476 402c1-1 1-1 2-1v1 1c-1 1 0 2 0 4h-1l-2-2c-1 0-1 0-2-1h1c1 1 2 1 3 1v-1l-1-1v-1z" class="L"></path><path d="M469 402h2s0 1 1 2l-2 2h-1-1v-2h0c1-1 0-1 1-1v-1h0z" class="C"></path><path d="M478 402h1c1 2 1 4 2 6 0 2-1 2-2 2v-2l-1 2h-1c-1 0-1-1-1-2l1-1h1c0-2-1-3 0-4v-1z" class="P"></path><path d="M477 407h1c0-2-1-3 0-4 0 2 0 3 1 5l-1 2h-1c-1 0-1-1-1-2l1-1z" class="e"></path><path d="M477 398h1l1 2 1 2 2 5-1 1c-1-2-1-4-2-6h-1v-1c-1 0-1 0-2 1v-2h-1l-1 3h-1l-1-2c0-2 1-2 2-3 0 1 0 1 1 1h1l1-1z" class="d"></path><path d="M477 398h1l1 2h-2c-1 0-1-1-1-1l1-1z" class="e"></path><path d="M460 396c1 1 2 1 3 1l1-1h0v1 2c1 0 2-1 3 0h1-1c-1 1-2 0-3 0l-3 3 1 1h-2v3h-1c0-3 0-7 1-10z" class="AD"></path><path d="M478 397v-1l2 1c-1 2 1 4 2 6l1 1v1h0c1 1 1 2 1 3 1 1 2 4 3 5l-4-1-3 1s0 1-1 1c0-1 0-2 1-3h0l-1-1c1 0 2 0 2-2l1-1-2-5-1-2-1-2v-1z" class="AB"></path><path d="M482 407c1 2 2 3 1 5h0l-3 1s0 1-1 1c0-1 0-2 1-3h0l-1-1c1 0 2 0 2-2l1-1z" class="p"></path><path d="M459 388c1 1 1 1 1 3h0v1h1 8 3c1 0 2 1 4 1h0l-1 1v1l1 1h0c-1 0-2 0-3 1v1l-1 1h-4-1c-1-1-2 0-3 0v-2-1h0l-1 1c-1 0-2 0-3-1l-1-4v-4z" class="o"></path><path d="M464 393h1c1 0 1 1 1 1v1c-1 0-1 0-2-1v-1z" class="m"></path><path d="M472 392c1 0 2 1 4 1h0l-1 1v1c-1 0-2-1-3-2v-1z" class="K"></path><path d="M461 392h8 3v1h-2-2c0 1 0 1-2 1 0 0 0-1-1-1h-1l-3-1z" class="AC"></path><path d="M467 399c1-1 1-2 2-2v-3h1c1 1 1 3 2 5h-4-1z" class="r"></path><path d="M479 408v2l1 1h0c-1 1-1 2-1 3 1 0 1-1 1-1l3-1 4 1h2c1 1 1 2 1 4 0 1 0 1-1 2h-2l-13 1c-3 0-6 1-9 0v-1 1h3c0-1 0-1 1-2s0-2 1-3c0-1 1-1 1-1v-3h2 1l3-1h1l1-2z" class="o"></path><path d="M483 412l4 1h2c1 1 1 2 1 4 0 1 0 1-1 2h-2-9l1-5c1 0 1-1 1-1l3-1z" class="D"></path><path d="M490 440h1v2l1 1h0c1 0 1 1 1 2l-2 3c1 1 1 2 1 3v1l2 1c-1 1-2 1-2 2-1 0-1 1-1 2v1c0 2 0 2 1 3h1c-1 2-2 1-2 3l1 1h3c-1 1-3 3-3 4v1l1 1-3 2v1c1 2 0 5-1 6 0 1-1 1-1 2-1 0-1 1-2 2l-17-21c-3-3-6-7-8-10 0 0 0-1 1-1l-1-1v-1l-2-2h0v-3h0c1-1 1-1 1-2v-1h-1v-1h1l30-1z" class="V"></path><path d="M480 473h1c2 0 5 0 7 1-3 0-5 0-8-1z" class="b"></path><path d="M491 442l1 1h0c1 0 1 1 1 2l-2 3v-1c0-1 1-2 0-3h-1c-3 0-16-1-18 0h0-10 0-1c-1 1-1 1-1 3h1c0 1 0 2 1 3v1 1l-1-1v-1l-2-2h0v-3h0c1-1 1-1 1-2h2c3 0 26 1 27 0s2-1 2-1z" class="L"></path><defs><linearGradient id="N" x1="477.705" y1="437.106" x2="474.94" y2="446.012" xlink:href="#B"><stop offset="0" stop-color="#cbc7ac"></stop><stop offset="1" stop-color="#f4d9bc"></stop></linearGradient></defs><path fill="url(#N)" d="M490 440h1v2s-1 0-2 1-24 0-27 0h-2v-1h-1v-1h1l30-1z"></path><path d="M490 417c0-1 0-1 1-1h1 2c3 1 7 1 11 1v1h1c1 0 5 1 7 0v-1 3h1c2-1 3 0 5 0h-3c-1 1 0 3 0 4v5c1 0 1 1 1 2s0 2 1 3h0c0 1 0 2 1 3v3 1 2l1-1c0 1 0 2-1 3v2c-1 2-1 3-2 4l2 1-1 2-1 1h-1v2h2l-2 2-2 3h0v1h0 0c1 0 1 0 2-1h1 1l-1 1 1 2-2 1h-2-4c-4-1-8-1-11 2h-1v1l-3 3c-1 0-1-1-2-1l-1-1v-1c0-1 2-3 3-4h-3l-1-1c0-2 1-1 2-3h-1c-1-1-1-1-1-3v-1c0-1 0-2 1-2 0-1 1-1 2-2l-2-1v-1c0-1 0-2-1-3l2-3c0-1 0-2-1-2h0l-1-1v-2h-1 0v-1-4-1h0c0-1 0-2-1-3l-2-1c1 0 2 1 3 0v-3h0v-1c0-1 0-2-1-3h1c1-1 0-2 0-3l-1-1c1-1 1-1 1-2z" class="N"></path><path d="M510 440v1s-1 0-1-1v-1h1v1z" class="C"></path><path d="M514 450c1-1 1-1 2-1v2h-2v-1z" class="a"></path><path d="M507 442h1 1c-1 2-1 2-3 2 0-1 0-2 1-2z" class="C"></path><path d="M517 443c-1 0-1-1-1-1 1-1 2-1 3-1v2h-1-1z" class="d"></path><path d="M512 455h0c3-1 1-1 2-3h1l1 1v2h-4zm5-12h1l-1 5-2-1c0-1 1-1 2-2v-2z" class="C"></path><path d="M502 445c1-1 1-2 2-1 1 0 1 0 1 1v1h-3-1l1-1z" class="e"></path><path d="M512 455h4 0v2h-1-2-2v-1h0l1-1z" class="M"></path><path d="M507 432l1 1c0 1-1 2-2 3h0c-1-1-1-1 0-2h-1v1l-1-1 1-1c1-1 1-1 2-1z" class="g"></path><path d="M515 437h1v2l-1 1-2-1c-1 0-1 1-3 1v-1l1-1c2 0 3 0 4-1z" class="i"></path><path d="M506 441l1 1c-1 0-1 1-1 2l-1 1c0-1 0-1-1-1-1-1-1 0-2 1v-1h0l-1-1h-1v-1h0c2 0 2 0 3 1h1c0-1 1-1 2-2z" class="a"></path><path d="M510 434c1 0 2-1 2-2h3l1 1c0 2-1 2-2 3h-1c-1 0-2 0-3-1v-1z" class="o"></path><path d="M515 432h1l1-1c0 1 0 2 1 3h0c0 1 0 2 1 3v3h0c-1 0-2 0-3-1v-2h-1l-1-1c1-1 2-1 2-3l-1-1z" class="e"></path><path d="M519 443l1-1c0 1 0 2-1 3v2c-1 2-1 3-2 4l2 1-1 2-1 1h-1 0v-2h0v-2c0-1 0-2 1-3l1-5h1z" class="g"></path><path d="M517 451l2 1-1 2-2-1c1-1 1-1 1-2z" class="f"></path><path d="M503 431h1v2h1l-1 1v1l-1 1v1c1 1 1 1 1 2h0v1h1l1 1c-1 1-2 1-2 2h-1c-1-1-1-1-3-1h0c0-1 0-1-1-2l1-1v-1c1-1 3-3 2-5 1-1 1-1 1-2z" class="K"></path><path d="M493 442c2 0 2-1 4-1v2l1 1h1l1-2v1h1l1 1h0v1l-1 1-1 1c1 1 1 2 1 2v1h-1v1l-1 1c-2 0-2 1-3 2l-1-1h1l-1-1-1 1-2-1v-1c0-1 0-2-1-3l2-3c0-1 0-2-1-2 0-1 0-1 1-1h0z" class="C"></path><path d="M502 444v1l-1 1-1 1s-1-1-1-2c1 0 2-1 3-1z" class="F"></path><path d="M494 448l1-1 3-3 1 1c0 1 1 2 1 2 1 1 1 2 1 2v1h-1l-1-1h-2c-1 0-2-1-3-1z" class="M"></path><path d="M494 448c1 0 2 1 3 1h2l1 1v1l-1 1c-2 0-2 1-3 2l-1-1h1l-1-1-1 1-2-1v-1h2v-3z" class="K"></path><defs><linearGradient id="O" x1="503.164" y1="450.845" x2="505.784" y2="467.109" xlink:href="#B"><stop offset="0" stop-color="#ad9e85"></stop><stop offset="1" stop-color="#d7c7ad"></stop></linearGradient></defs><path fill="url(#O)" d="M500 451v1l1-1v1h1 0c0-1 0-1 1-1h2c-1 1-1 1-1 2h2l-1 1v1h3 2c-1 1-3 0-4 1 2 0 2 1 4 0l1 1h0 2 2 1 2l-2 2-2 3h0v1h0 0c1 0 1 0 2-1h1 1l-1 1 1 2-2 1h-2-4c-4-1-8-1-11 2h-1v1l-3 3c-1 0-1-1-2-1l-1-1v-1c0-1 2-3 3-4h-3l-1-1c0-2 1-1 2-3h-1c-1-1-1-1-1-3v-1c0-1 0-2 1-2 0-1 1-1 2-2l1-1 1 1h-1l1 1c1-1 1-2 3-2l1-1z"></path><path d="M491 464l1-1c2 0 2 0 4 1l-1 1h-3l-1-1z" class="u"></path><path d="M514 463h0c1 0 1 0 2-1h1 1l-1 1c-2 1-4 1-7 1v-1h4z" class="L"></path><path d="M492 461c-1-1-1-1-1-3v-1c0-1 0-2 1-2v1 1c1-1 2-1 3-2v1h1 1 0 0l-1 2h1 0 1 1 0c-1 0-1 0-1 1l-2 1h-1v-1h-1 0l-2 2z" class="e"></path><path d="M496 464c1-1 1-1 2-1l1 1c0 1-1 2-1 4v1l-3 3c-1 0-1-1-2-1l-1-1v-1c0-1 2-3 3-4l1-1z" class="T"></path><path d="M492 470c1-1 3-2 4-3h1l1 2-3 3c-1 0-1-1-2-1l-1-1z" class="C"></path><path d="M506 456c2 0 2 1 4 0l1 1h0 2 2 1 2l-2 2-2 3h0v1h0-4c-1 0-2 0-3-1v-1h0v-1h0l-1 1-2-1v-1l1-1v-1l1-1z" class="K"></path><path d="M515 457h1 2l-2 2-2 3-2-1c-1-1-1-1-1-2s0-1 1-1v1c1 0 1 0 2-1h0s1 0 1-1h0z" class="N"></path><path d="M511 459c0 1 0 1 1 2l2 1h0v1h0-4c-1 0-2 0-3-1v-1h0 2l2-2zm-21-42c0-1 0-1 1-1h1 2c3 1 7 1 11 1v1h1c1 0 5 1 7 0v-1 3h1c2-1 3 0 5 0h-3c-1 1 0 3 0 4v5c1 0 1 1 1 2l-1 1h-1-3c0 1-1 2-2 2h0v1c0 1 1 2 2 2h0-2l-1-1h0v-1c0-1 0-2-1-2l-1-1c-1 0-1 0-2 1h-1v-2h-1c0 1 0 1-1 2 1 2-1 4-2 5v1l-1 1c1 1 1 1 1 2l-1 2h-1l-1-1v-2c-2 0-2 1-4 1h0c-1 0-1 0-1 1h0l-1-1v-2h-1 0v-1-4-1h0c0-1 0-2-1-3l-2-1c1 0 2 1 3 0v-3h0v-1c0-1 0-2-1-3h1c1-1 0-2 0-3l-1-1c1-1 1-1 1-2z" class="I"></path><path d="M504 431h1l1-1h1 0v2c-1 0-1 0-2 1h-1v-2z" class="G"></path><path d="M494 432h2c1 2 0 3 0 5v1h1c1 0 1 0 1-1h0l-1-1v-1-2h1 1c0-1 1-2 2-3 0 1 1 1 1 1h1c0 1 0 1-1 2 1 2-1 4-2 5v1l-1 1c1 1 1 1 1 2l-1 2h-1l-1-1v-2c-2 0-2 1-4 1h0c-1 0-1 0-1 1h0l-1-1v-2h-1 0v-1-4-1h3v-1l1-1z" class="e"></path><path d="M493 442l-1-1v-1h0c1 0 1 0 2 1l-1 1h0z" class="M"></path><path d="M490 434h3l-1 1h-2v-1z" class="H"></path><path d="M500 438c-1 0-1 0-1-1h0l-2-2 2-2c1 1 2 1 3 0 1 2-1 4-2 5z" class="o"></path><path d="M494 432h1c1 1 1 2 1 3-1 2-2 1-3 3h1v-1h1v1c-1 1-1 1-3 1v-2s1-1 1-2v-2l1-1z" class="AG"></path><path d="M315 325c4 3 9 9 12 14 4 3 49 57 49 59l2 2c3 2 8 8 10 12v1c0 2-4 5-5 7s-2 3-2 5c-2 2-3 3-3 5l-3 5 1 2c1 1 0 3 1 5v1 3c0 1 1 8 0 9v1l-2 2c-1 1-2 1-4 2h1s1 1 2 0h0 2s1 0 1 1v5 8c0 2 0 4-1 5v1l-7 8v1c-2 0-3 0-4 1l1 1-2 1h-1v-2 1h-1l-2 2h-2l-1-1h-1c-1 1-1 1-1 2-1 0-2 0-2-1l-1 1c0 1-1 1-2 1 0 1-1 1-1 1 0 1 0 2 1 2l2 2v1c-1 0-1 0-2-1-1 1-1 1-1 3h-1l-1 1h-2-2l-1-1-4-3v7c0 1 0 2 1 3v4h-1v4c1 2 1 3 2 5 0 1 1 2 1 3h0l1 2v1 1c1 2 2 6 3 8v2l1 1h0c0 1 0 1 1 2v1c1 1 3 1 4 1h0-6c-1 3-1 6-2 8-1 3-3 5-4 7v12c0 1 0 3 1 4l-6 1-1 8v4 1 7h0-10c-4 0-8 0-11-1h-2c0-1 0-1 1-2v-1h-2v-6-2c-1-2 0-5-1-8l-2-1h0-28v1c-1 0-1 0-2 1h1 0v1h0-2 0c-1 1-1 1-2 1-3-1-7-1-10 0-2 1-5-1-7 0h-4v-1c-2 0-3 0-4 1h-7l-1 1h0 0c-1 1-1 1-1 2l-9-2-14-3 1-1-1-1h-1c0 1-1 1-1 1l-1 1v-1h-1c-1 0-2 0-3-1l-2 1-3-1h0-1l-1-1 1-1c-1 0-2 0-2-1-1-1-2-1-4-1h-1v1c-1-1 0-2 0-3h-1l-1 1c0-1-1-2-1-3h0l-1-1v3c-1 1-1 2-1 3l-1 2-1 1h-1l-1 1h0c-2-1-5-1-8-1h-8-1 1 1v-1l2-2c-1-1-1-1-1-2h-1 0-1c0-1 0-2-1-3v-1-1-1c-1 0-1 0-2-1h0c0-1 1-1 1-2-1 0-1-1-3 0h0v-2c0-1 0-1 1-1-1-1-1-2-1-2v-1-2l-19 1h-6c-3 0-8 0-10 1l-1-1v-1-2c-1 0-1-1-2-1v-1l-1-1-1 2-3-4-13-15-6-9-7-10c-2-4-4-9-7-14l-4-9c-3-7-6-14-8-21v-6l-1-2v-4-5h0v-1c1-1 2-1 3-2v1l1 1v1h1 9l1-1v1h4v-1h5v-6-6c-1-1 0-3-1-4h-1 0v-6c-1-3 0-6-1-9 0-1 0-2-1-3v-1h-7c-1 0-2 0-2-1h-3c-1 0 0-3-1-3v-1-12-1c0-1-1-3-2-4v1c0-1-1-2-2-4h1l-1-2h0l-1-1 3-2h1c1-1 2-2 3-2l1-1c1 0 1-1 2-1 1-1 2-1 2-2 1 0 1 0 2-1l5-3c1 0 2-1 3-2h-1c5-4 10-7 15-10 11-6 21-13 33-18l10-4 8-4c2 0 3-1 5-1l1-1 4-1c1 0 2 0 3-1 0-1 1 0 2 0 1-1 3-3 5-4h0c0 2-4 4-6 5 0 1-1 2-1 3h2c1 0 2-1 3-1h0c0 1 0 1 1 1s2 0 3 1h3 2c0 1 1 1 2 1l2-1v-1l-1-1h0 1l2 1c0 1 0 1 1 2l2-2c1-1 4-1 5 0h1 1 1l1 1c0-1 2-1 2-2h3l2-2c0 2 2 3 3 4l-2-4c2 0 3 1 5 2 1-1 1-1 1-2l-3-1v-1c2 0 3 0 4 1s3 1 5 2c2 0 5 2 8 3h0c1 0 2 1 2 1l2 1h1 3c3 1 5 3 8 3l-2-6v-1c2 0 5 1 7 2 1 0 2 1 4 1 1 1 3 2 4 2s2 1 3 1l1 1h1s1 1 2 1l1 1c1 0 2 0 3 1l2 1h1c1 1 2 1 2 1l1 1 2 1c1 0 2 1 3 1 1 1 1 1 2 1l1 1c1 0 1 0 2 1h1c1 1 2 1 3 2h1s0 1 1 1h1c3 2 6 3 9 5h0c2 1 2 2 3 2 1 1 1 0 1 1v-41z" class="D"></path><path d="M113 401h2v1l-1 1v1h0-1c-1-1-1-2 0-3zm120 37h0 2s1 0 1 1c1 0 1 1 1 1l-1 2s-1-1-2-1 1 0-1 0l1-1c0-1-1-1-1-1v-1h0z" class="T"></path><path d="M170 536l1 8s0 1-1 1h0l-1-4v-2h0 1v-2 2-3z" class="V"></path><path d="M194 536c2 2 3 4 4 6v1h-2c-1-2 0-5-2-6h-1 0l1-1z" class="o"></path><path d="M169 541l1 4h0c1 0 1-1 1-1v1h7-1-3v1c3 0 6-1 9 0h-9-5v-5z" class="AE"></path><path d="M113 410c0-1 0-2 1-4h4v3l-2-1-1 1-1-1v1c0 1-1 1-1 1z" class="P"></path><path d="M175 339v3l1 1c1 0 1 0 2-1 0 1 1 2 1 3-1 1-1 2-1 2-1-1-1-1-2-1h1v-1l-1-1h-1v-2l-1-1c-1 0-2 1-2 1-1 1-2 1-3 1v1c2-2 4-4 6-5z" class="B"></path><path d="M208 349h1v7h0c-1-1-1-1-1-2v-1h-1-1-1c0-1 1-2 1-3s1-1 2-1z" class="T"></path><path d="M255 473c-1-1-2-2-3-2v-4-1h1c1 0 1-1 2-1v1c-1 0-2 1-2 1l1 2c1 1 2 1 2 1l1-1c-1 2-1 3-2 4z" class="S"></path><path d="M233 497v11 1l1-1c1 2 1 2 0 4v4h0v1l-1-1v-2c-1-6-1-11 0-17z" class="N"></path><path d="M265 430c0 4 0 8-1 11v-1h-1 0-1c1-3 1-6 2-9l1-1z" class="S"></path><path d="M168 488l6 12-1 1c0-1 0 0-1-1l-3-1v-2h1c1-1 0-1 0-2v-1c-1 0-1-1-1-1v-1c-1 0-1-1-2-2 0-1 0-1 1-2z" class="V"></path><path d="M93 379c1 1 1 2 1 3l1 2h0c-1 0-2 0-3 1 0 0 0 1-1 1 0-1-1-3-2-4 1-1 2-1 2-2 1 0 1 0 2-1z" class="W"></path><path d="M177 349l-1 2h-1l1-2h-1-2l1-1h-1-1v-1c1 0 1 0 1-1 0 0 0-1-1-1l1-1v-1h1l-1 2h0l3-1 1 1v1h-1c1 0 1 0 2 1l-1 2z" class="j"></path><path d="M176 346c1 0 1 0 2 1l-1 2h-1v-3z" class="O"></path><path d="M186 522c1 1 1 3 2 4 1 2 3 5 4 7h0c-1 0-1-1-2-1l-1 1-1-1c1 0 1-1 2-2-1-1-1-2-2-3-1 2 0 2-1 4h0c-2-2-1-6-1-9z" class="V"></path><path d="M261 422c-1-2 0-5 0-6l-1-5v-3-1l1 1c1 1 0 3 1 5v3h1v6h0-1-1z" class="j"></path><path d="M260 388v1c0 2-1 2-1 3v2 1h1c0 1-1 1-1 1l1 1 1 2-1 2v2h0v2 1h-1v-2c0-1 0-1-1-2 0-1 0-2-1-3s-1-1-1-2c1 0 1 0 2-1v-1h1v-1l-1-1c0-1 0-2 1-2v-1-1 1h-1v-1 1c1-1 1-1 1-2h1z" class="n"></path><path d="M126 404h1 0v-2c2 0 4-1 5 0 0 1-1 2-2 3h-1 0c-1 0-2 1-2 2-1 0-1 0-2-1h-2c-1-2-1-2-2-3-1 0-2-1-2-1-1 0-2 1-3 1v1h-1c0-1 1-2 3-3l2 1c2 1 3 2 4 3h1c0-1-1-2-1-3h1c0 1 0 1 1 2z" class="W"></path><path d="M125 405c0-1-1-2-1-3h1c0 1 0 1 1 2v1c1 0 1-1 1-1 1-1 0-1 1-2h1c0 2-1 3-2 4-1 0-1 0-2-1z" class="J"></path><path d="M162 373h2 0c1 2 1 3 2 4 0 0 1 1 2 1h-1v1l3 3c1 2 3 4 3 6h0l1 1c1 1 1 1 1 2v1c-2-1-2-2-3-3-3-5-7-11-10-16z" class="u"></path><path d="M190 348v-1h1 1c0 1 1 1 2 2 0 0 0 1 1 1v3s-1-1-1-2h-2s0-1-1-1c-2-1-4-1-5-1-2 0-4-1-6-1l-1 1c0-1 0-3 1-4h1c0 1-1 1-1 2 1 0 2 0 3 1h1 1c1 0 1-1 2-1h1l2 1z" class="T"></path><path d="M185 348c1 0 1-1 2-1h1l2 1h1v1c-1 1-2 0-4 0-1 0-1 0-2-1z" class="O"></path><path d="M180 536c-1-5-1-11-1-17h1 1c1 1 0 4 0 6v13c-1-1-1-1-1-2z" class="E"></path><path d="M169 533l-1-9c0-2 0-4 1-5v-1c1 0 1 1 1 1v3 14h0v3-2 2h-1 0v-6z" class="v"></path><path d="M251 407l1 1c0 9 2 19 1 28-1 1-1 3-2 5h-1c0-2 1-3 1-5 1-2 1-5 1-8 0-7-2-14-1-21z" class="P"></path><path d="M260 388h1v2h0c1 2 1 5 2 7 0 0 0 1-1 1h1v4h0 0c-1 1-2 1-3 1v-2l1-2-1-2-1-1s1 0 1-1h-1v-1-2c0-1 1-1 1-3v-1z" class="O"></path><path d="M178 545c5-1 10-1 15-1h7l1 1c-2 1-14 1-18 1-3-1-6 0-9 0v-1h3 1z" class="M"></path><path d="M181 538l1 1v-1l1 1c2 0 2 0 4-1-1-1 0-2 0-3 1-1 4 0 6-1h0l1 2-1 1h0-4-1l-1 1c2 1 5 1 6 0l1 1v1h-8c-2 0-5 1-7 0l1-4c0 1 0 1 1 2z" class="V"></path><path d="M164 500c2 1 7 2 9 1l1-1 2 4-1 1c0-1-1-1-1-2v1c-1 1-2 1-2 1-1 0-3 0-4 1 0 0-1 1-1 2s-1 2-1 2c-2-2-1-5-1-7h0l1-1h0-1 1l-2-2z" class="M"></path><path d="M178 508c1 2 2 3 3 5 0 1 1 3 1 4 3 4 2 10 2 14v4c1-1 1-1 2-1v1c0 2 0 2-1 2h-1v-1l-1 1h-1l1-5c0-2 0-13-1-15h-4v-3-6z" class="v"></path><path d="M249 400l-5-19h0v-1h0c-1-5-2-11-4-16h1 0c2 4 3 9 4 13l1 7 2 1h1v-1l1 1c-1 1-1 2 0 3v1l-2-1v1c0 2 1 5 1 7v4z" class="F"></path><path d="M246 384l2 1c0 1 0 1-1 2l-1-3z" class="y"></path><path d="M248 385h1v-1l1 1c-1 1-1 2 0 3v1l-2-1v1l-1-2c1-1 1-1 1-2z" class="J"></path><path d="M263 403l2 2v14c0 4 1 8 0 11l-1 1-1-1h-1c1-3 0-6-1-8h1 1 0v-6-2-2c0-1 0-3 1-4 0-2 0-1-1-2v-3z" class="O"></path><path d="M176 504c2 3 1 9 1 13l-1 1 2 1v16 6h-2v-10c0-8 0-17-1-25v-1l1-1z" class="E"></path><path d="M251 388v-2h1v1c0 4 2 10 3 14v1c1 1 1 1 0 2l1 1c1 1 1 2 1 2 1 1 1 1 1 2h-1v-2h-1v2l1 1h-1v1l-1-1c-1-1 0-2 0-3l-1-1c0-1 0-1 1-2l-2-2-2 1c1 1 1 1 1 2l-1 1h-1c0-2-1-4-1-6v-4c0-2-1-5-1-7v-1l2 1v-1h1z" class="B"></path><path d="M250 388h1l2 9c0 1 0 3 1 4-1 1-2 1-3 1v-2h-1l-1-4c0-2-1-5-1-7v-1l2 1v-1z" class="D"></path><path d="M248 389v-1l2 1v6c1 2 1 3 0 5l-1-4c0-2-1-5-1-7z" class="n"></path><path d="M125 448c5 0 11-2 16-4 2-1 5-2 7-3l3 8c-2 1-4 0-5 0h-11c-3-1-7 0-11 0l1-1z" class="Y"></path><path d="M239 577h12 5v-1l22-3v3 1c-1 0-1 0-2 1h1 0v1h0-2 0c-1 1-1 1-2 1-3-1-7-1-10 0-2 1-5-1-7 0h-4v-1c1 0 3 1 5 0h2v-1h-4c-5-1-11 0-16-1z" class="B"></path><path d="M255 578l15-1h8c-1 0-1 0-2 1h1 0v1h0-2 0c-1 1-1 1-2 1-3-1-7-1-10 0-2 1-5-1-7 0h-4v-1c1 0 3 1 5 0h2v-1h-4z" class="X"></path><path d="M213 337h3c-1 1-3 2-4 3-5 3-13 6-19 5-5 0-9-2-14-4l1-1h5c0-1 0-1 1-1 0 1 1 1 2 1 0 0 1 1 2 1h1 1 0c1 1 3 1 4 1 1-1 3-1 4-1s3-2 4-2c2 1 5 0 7 0 0-1 2-1 2-2z" class="x"></path><defs><linearGradient id="P" x1="221.594" y1="579.901" x2="253.265" y2="571.221" xlink:href="#B"><stop offset="0" stop-color="#e9dabc"></stop><stop offset="1" stop-color="#f4ede4"></stop></linearGradient></defs><path fill="url(#P)" d="M223 571v-1c4 3 7 4 11 6 2 1 3 1 5 1 5 1 11 0 16 1h4v1h-2c-2 1-4 0-5 0-2 0-3 0-4 1h-7l-1 1h0 0c-1 1-1 1-1 2l-9-2-14-3 1-1-1-1c1-1 3-2 4-4 1 0 2-1 3-1z"></path><path d="M220 572c1 0 2-1 3-1v6h-6l-1-1c1-1 3-2 4-4z" class="T"></path><path d="M128 415h52 6 2l1-1v-1h0c1 2 2 5 1 6-1-1-1-3-3-3-1-1-4 0-6 0h-13c-17 0-34-1-50 0v1h12c-3 1-7 1-10 1h-1l-19 1c0-1-1-2-1-4h0 0c1 0 2 0 3-1l1 1h0c1-1 3-1 4-1l1 1 1-1s0 1 1 1h3 9 5 1z" class="t"></path><path d="M99 415c2 0 6 1 8 2h18c-1 1-4 1-5 1h-1l-19 1c0-1-1-2-1-4z" class="x"></path><path d="M166 338c2 0 3-1 5-1l1-1 4-1c1 0 2 0 3-1 0-1 1 0 2 0 1-1 3-3 5-4h0c0 2-4 4-6 5 0 1-1 2-1 3h2c1 0 2-1 3-1h0c0 1 0 1 1 1s2 0 3 1h3 2c0 1 1 1 2 1l2-1v-1l-1-1h0 1l2 1c0 1 0 1 1 2l2-2c1-1 4-1 5 0h1 1 1l1 1c-2 0-5 1-7 0-1 0-3 2-4 2s-3 0-4 1c-1 0-3 0-4-1h0-1-1c-1 0-2-1-2-1-1 0-2 0-2-1-1 0-1 0-1 1h-5l-1 1s-3-1-4-2c-2 1-4 3-6 5h0c-6 4-11 10-15 16-6 9-11 18-15 28v1-2c0-2 1-4 2-6 3-7 7-13 11-20 1-1 2-3 3-4 4-5 7-10 12-14 3-2 7-4 9-6-8 2-15 5-22 8-4 1-7 2-10 4l-24 13-3 2c-1 1-2 1-3 2 0 0-1 1-2 1l-3 2-8 5h-1c5-4 10-7 15-10 11-6 21-13 33-18l10-4 8-4z" class="r"></path><path d="M158 473c1 0 2-1 3 0l3 7 4 8c-1 1-1 1-1 2 1 1 1 2 2 2v1s0 1 1 1v1c0 1 1 1 0 2h-1v2l3 1c1 1 1 0 1 1-2 1-7 0-9-1-1 0-2-1-3-2-1 0-2-1-2-2-1 1-2 1-3 1-1-1-2-2-2-4l-1-5c0-5 3-10 6-13v-1h-2l1-1z" class="B"></path><path d="M162 494c2 2 4 4 7 5l3 1c1 1 1 0 1 1-2 1-7 0-9-1-1 0-2-1-3-2 0 0 1 0 1-1l1-1h-1-1l-1-1 1-1h1z" class="D"></path><path d="M158 473c1 0 2-1 3 0l3 7h-1l-1-2-1-2c-3 4-6 7-6 12 0 3 1 5 3 7l1 1c-1 1-2 1-3 1-1-1-2-2-2-4l-1-5c0-5 3-10 6-13v-1h-2l1-1z" class="C"></path><path d="M163 480h1l4 8c-1 1-1 1-1 2 1 1 1 2 2 2v1s0 1 1 1v1c0 1 1 1 0 2h-1v2c-3-1-5-3-7-5v-1c-1-2-2-4-2-6s0-4 2-5c0-1 1-1 1-2h0z" class="E"></path><path d="M144 473h14l-1 1h2v1c-3 3-6 8-6 13l1 5c0 2 1 3 2 4 1 0 2 0 3-1 0 1 1 2 2 2 1 1 2 2 3 2l2 2h-1 1 0l-1 1h0c-2 1-5 2-7 1h-1c-1 0-1 0-2 1-2 0-4 1-6 0-1-1-2-1-3-1h1c0-1-1-1-2-2v-4c0-4 0-8 1-13 0-2 0-3 1-5v-1c-1-1 0-2-1-3 1 0 1-1 1-1-1-2-3-1-5-1 0-1 1-1 2-1z" class="Q"></path><path d="M151 480l-3-1h0l1-1c1 0 2 0 3 1l-1 1z" class="L"></path><path d="M153 477v-1c-1-1-2 0-3 0h0l-1-1v-1c1 0 3 1 4 1s2-1 3-1l-3 3z" class="H"></path><path d="M157 474h2v1c-3 3-6 8-6 13l1 5h-1c0 1 1 2 1 4v1h-1v-1c-1-1-1-1-3-1h0v-1c-2-5-1-10 1-15l1-1 1-2 3-3h1z" class="O"></path><path d="M150 496c2 0 2 0 3 1v1h1v-1c0-2-1-3-1-4h1c0 2 1 3 2 4 1 0 2 0 3-1 0 1 1 2 2 2 1 1 2 2 3 2l2 2h-1 1 0l-1 1h0c-2 1-5 2-7 1h-1c-1 0-1 0-2 1-2 0-4 1-6 0 0-2-1-5 0-7 0-1 0-1 1-2h0z" class="D"></path><path d="M150 496h0c1 2 2 3 3 4s3 2 4 4c-1 0-1 0-2 1l-2-2c-2-2-3-3-4-5 0-1 0-1 1-2z" class="H"></path><path d="M149 498c1 2 2 3 4 5l2 2c-2 0-4 1-6 0 0-2-1-5 0-7z" class="u"></path><path d="M159 496c0 1 1 2 2 2 1 1 2 2 3 2l2 2h-1 1 0l-1 1h-1c-1 0-3-1-4-2h0c-2-1-3-2-4-4 1 0 2 0 3-1z" class="L"></path><path d="M204 556v-1c1-1 2-1 3-2l3 5 7 6 4 4 2 2v1c-1 0-2 1-3 1-1 2-3 3-4 4h-1c0 1-1 1-1 1l-1 1v-1h-1c-1 0-2 0-3-1l-2 1-3-1h0-1l-1-1 1-1c-1 0-2 0-2-1-1-1-2-1-4-1h-1v1c-1-1 0-2 0-3l1-1 1-1v-1c2-3 4-8 5-12h1v1z" class="y"></path><path d="M201 573c0-1 1-1 2-1 0 1 0 1 1 2h-1c-1 0-2 0-2-1zm1-5c1 1 3 2 3 3l1 1h0-2 0l-3-3h0 1v-1z" class="R"></path><path d="M211 568l1-1h2v1h1v1h-1c-1 0-2 1-3 1h-1v-1l1-1z" class="E"></path><path d="M198 568v1c1-1 2-1 2-1l1 1h0l-1 1c1 1 1 1 3 2-1 0-2 0-2 1-1-1-2-1-4-1h-1v1c-1-1 0-2 0-3l1-1 1-1z" class="J"></path><path d="M197 569c0 1 1 1 1 2v1h-1-1v1c-1-1 0-2 0-3l1-1z" class="s"></path><path d="M209 576s-1-1-2-1h0l1-1h1v-3h2c0 1 1 2 1 4v1h3c0 1-1 1-1 1l-1 1v-1h-1c-1 0-2 0-3-1z" class="AA"></path><path d="M203 555h1v1h0c0 1 0 1-1 2v2h0c-1 1 0 1 0 2h2 0c0-1 1-1 2-1h0l-2 2h-1c1 1 1 1 2 1l2 2h-2c-1 0-1-2-3-2v-1l-1 1s1 0 1 1h-1c-1 0-1 0-1 1h0c1 1 1 1 1 2v1h-1l-1-1s-1 0-2 1v-1-1c2-3 4-8 5-12z" class="u"></path><path d="M221 568l2 2v1c-1 0-2 1-3 1-1 2-3 3-4 4h-1-3v-1c0-2-1-3-1-4v-1c1 0 2-1 3-1h1c2 0 3 0 5-1h1z" class="H"></path><path d="M215 569c2 0 3 0 5-1l-2 2-4 3c0-1 1-2 0-4h1z" class="T"></path><path d="M221 568l2 2v1c-1 0-2 1-3 1 0-1-1-1-2-2l2-2h1z" class="b"></path><path d="M211 570c1 0 2-1 3-1 1 2 0 3 0 4l-2 2c0-2-1-3-1-4v-1z" class="D"></path><path d="M204 556v-1c1-1 2-1 3-2l3 5h0c-1 1-2 1-3 2v1c1 0 1-1 2-1s1 0 0 1v3c1 1 1 1 1 2s-1 1-1 1l-1-1-2-2c-1 0-1 0-2-1h1l2-2h0c-1 0-2 0-2 1h0-2c0-1-1-1 0-2h0v-2c1-1 1-1 1-2h0z" class="B"></path><path d="M210 558l7 6 4 4h-1c-2 1-3 1-5 1v-1h-1v-1h-2l-1 1c0-1 0-1-1-2 0-1 0-1-1-2v-3c1-1 1-1 0-1s-1 1-2 1v-1c1-1 2-1 3-2h0z" class="L"></path><path d="M216 566c-1 0-3-1-4-2s-1-3-1-4c2 1 3 3 5 5h0c1 0 2 1 2 2l-2-1z" class="B"></path><path d="M217 564l4 4h-1c-2 1-3 1-5 1v-1h-1v-1h0 2v-1l2 1c0-1-1-2-2-2l1-1z" class="G"></path><path d="M262 450l1 1h1c1 1 2 2 3 2 3 2 5 4 8 5 2 1 6 1 7 2h0l1 1v1c-1 1-1 1-2 1v-1l-2-1v2h0v1c1 1 1 1 2 1h1c-2 3-3 5-3 8v3h0v1h-4c-3 1-7 2-10 1-3-2-7-4-10-5 1-1 1-2 2-4l5-19z" class="U"></path><path d="M237 343c2 2 5 3 7 5l3 3h1l6 5 3 2v3c1 1 0 2 1 4l2 1c0 3 1 5 1 7v3h-1 0v1h-1v-1c-1 0-2-1-2-1l-1 1c1 1 1 1 1 2v2h1v1 2h0c1 1 1 2 1 3h0c-1 1-1 2-1 3v1l-1-1c1-1 1-2 1-3 0 0 1 0 1-1s-1-1-1-1h0c-1 1-1 2-2 2v1h-1l-1-1s0 1 1 1c0 1 1 1 1 3 0 1 0 1-1 2h-1c-1-1 0-2-1-3 0 2 1 3 1 5v2c0 1 1 2 1 3v2c-1-4-3-10-3-14v-1h-1v2h-1c-1-1-1-2 0-3l-1-1v1h-1l-2-1-1-7h0l1-1-4-13c-1-1-1-2-1-2v-1c-1-1 0-2 0-3-1-3-1-6 0-8l1 2 1-1-1-1h-2c-1 1-1 1-1 2l-1-1-1-1 2-1h-1c0-2-1-3-1-5z" class="W"></path><path d="M253 368l1 2c-2 1-2 1-4 1v-1h3v-2z" class="T"></path><path d="M255 379l1 1c0 1 0 1-1 2h0c-1-1-1-1 0-3z" class="u"></path><path d="M246 376c0 2 0 4 1 6h1c1 1 1 1 1 2l2-2v-1h1 1v3 1l-3-1v1l-1-1v1h-1l-2-1-1-7h0l1-1z" class="B"></path><path d="M242 363l1-1c0 3 2 6 2 9 0 1 1 1 1 2 1 3 2 6 2 9h-1c-1-2-1-4-1-6l-4-13z" class="S"></path><path d="M246 366v-1c2 1 2 4 4 5v1c0 1 0 2 1 2v3h0v2 1c0-1-1-1-1-2v-1c-1-2-3-5-3-7l-1-3zm11 5h0 1c0 1 0 1-1 2 2 0 2 1 3 2v1h0v1h-1v-1c-1 0-2-1-2-1l-1 1v-2c0 1 0 1-1 1v2l-1 1c-1-2 0-4 0-5v-1c1-1 1-1 2-1h1z" class="O"></path><path d="M257 371h0 1c0 1 0 1-1 2h-1c-1 0-1 0-2-1 1-1 1-1 2-1h1z" class="s"></path><path d="M257 371h0 1c0 1 0 1-1 2h-1s0-1 1-2z" class="B"></path><path d="M245 359c1 1 2 1 3 1l2-1v1 2c1 0 1 0 2 1v2h1v1c-1 1 0 2 0 2v2h-3c-2-1-2-4-4-5v1-1s0-1 1-1v-2h-1l-2-2 1-1z" class="D"></path><path d="M246 366v-1s0-1 1-1l1 2h2v4c-2-1-2-4-4-5v1z" class="u"></path><path d="M254 356l3 2v3c1 1 0 2 1 4l2 1c0 3 1 5 1 7v3h-1v-1c-1-1-1-2-3-2 1-1 1-1 1-2h-1 0-1v-4h0v-3h-1l-1 1v1c0 1 0 2 1 3l-1 1-1-2s-1-1 0-2v-1h-1v-1-1-3l-1-1 1-1 2 1v-3z" class="B"></path><path d="M256 367v-1h1l1 2h1v-1 3c0 1-1 1-1 1h-1 0-1v-4z" class="O"></path><path d="M258 365l2 1c0 3 1 5 1 7v3h-1v-1c-1-1-1-2-3-2 1-1 1-1 1-2 0 0 1 0 1-1v-3l-1-2z" class="J"></path><path d="M237 343c2 2 5 3 7 5l3 3h1l6 5v3l-2-1-1 1 1 1v3 1 1-2c-1-1-1-1-2-1v-2-1l-2 1c-1 0-2 0-3-1l-1 1 2 2h-2l-1-1v1l-1 1c-1-1-1-2-1-2v-1c-1-1 0-2 0-3-1-3-1-6 0-8l1 2 1-1-1-1h-2c-1 1-1 1-1 2l-1-1-1-1 2-1h-1c0-2-1-3-1-5z" class="R"></path><path d="M241 357c0 1-1 2 0 2 1-1 2-2 3-2h0v1c-1 0-1 1-1 2v1 1l-1 1c-1-1-1-2-1-2v-1c-1-1 0-2 0-3zm3-9l3 3h1l6 5v3l-2-1-1 1 1 1v3 1 1-2c-1-1-1-1-2-1v-2-1l-2 1c-1 0-2 0-3-1h1c0-1-1-1-1-2s1-1 1-2l-1-1c-1-1-1-3-1-4v-2z" class="n"></path><path d="M247 351h1c-1 1-1 1-1 2h-1v-2h1z" class="T"></path><path d="M89 382c1 1 2 3 2 4l1 1v1h0c1 1 1 2 1 3h0l3 3c0 1 0 1 1 1h0c1 1 1 1 1 2h0c2 1 3 3 5 4l3 3h0v2h1c1 0 2 1 3 2 0 1 1 1 1 2 1 1 1 1 2 1h0v-1s1 0 1-1v-1l1 1 1-1 2 1h-2c1 0 1 1 2 1v1c-1 1-2 0-3 1 1 0 1 1 2 1 0 0 1 0 1 1h4c2 0 4 0 6 1h-1-5-9-3c-1 0-1-1-1-1l-1 1-1-1c-1 0-3 0-4 1h0l-1-1c-1 1-2 1-3 1h0l-2 1h-7c-1 0-2 0-2-1h-3c-1 0 0-3-1-3v-1-12-1c0-1-1-3-2-4v1c0-1-1-2-2-4h1l-1-2h0l-1-1 3-2h1c1-1 2-2 3-2l1-1c1 0 1-1 2-1z" class="R"></path><path d="M96 407h3l1 1-1 1c-1 0-2 0-3-1v-1z" class="O"></path><path d="M93 391l3 3c0 1 0 1 1 1-1 0-1 0-1 1-1 0-1-1-1-1h-1v1l-1-2c-1 1-1 1-3 1h1v-1c0-1 1-1 2-1v-1-1z" class="J"></path><path d="M92 409c1 0 2 1 3 2h1 2v3c1 0 1-1 2-1s1 0 2 1c-1 1-2 1-3 1h0l-2 1c0-3-3-5-5-7z" class="P"></path><path d="M89 382c1 1 2 3 2 4l1 1v1h0c1 1 1 2 1 3h0v1 1c-1 0-2 0-2 1-1 0-2 0-3 1h0-1v-1c2-1 2-1 4-1v-1h0-2 0-2c-1-1-3 0-4-1 0-1 0-1-1-2h-2 0l-1-1 3-2h1c1-1 2-2 3-2l1-1c1 0 1-1 2-1z" class="O"></path><path d="M89 382c1 1 2 3 2 4l1 1v1c-1 0-1 1-3 1v-1h-1c0-1 0-1-1-2h0c0-1 0-1-1-2l1-1c1 0 1-1 2-1z" class="B"></path><path d="M87 386l1-1 2 1c0 1-1 1-1 2h-1c0-1 0-1-1-2z" class="J"></path><path d="M84 398c3 3 5 8 8 11 2 2 5 4 5 7h-7c-1 0-2 0-2-1h-3c-1 0 0-3-1-3v-1-12-1z" class="V"></path><path d="M84 399c3 5 4 11 4 16h-3c-1 0 0-3-1-3v-1-12z" class="D"></path><path d="M286 545l2-1c0 1 0 2 1 3 0 0 0 1 1 2h9 6v-1c1 0 1 1 1 1v1c-1 3 0 6 0 9v17h-28v-3c4-1 12-2 15-5 1-1 0-1 0-3-2-4-11-2-15-4h0 0c0-4 0-10 1-14h1 2 2l-1-1c1-1 2-1 3-1z" class="E"></path><path d="M286 545l2-1c0 1 0 2 1 3 0 0 0 1 1 2h-10l-1-2h1 2 2l-1-1c1-1 2-1 3-1z" class="g"></path><path d="M286 545l2-1c0 1 0 2 1 3-2 1-2 0-3 0h-2l-1-1c1-1 2-1 3-1z" class="a"></path><path d="M279 530h1v1 12 4h-1c-1 4-1 10-1 14h0c-6-1-11-5-17-6-7-1-18 2-24-2-3-2-4-5-4-8l5 1c2 0 5 2 7 1v-1c1-1 1-4 1-6h1 0v-1h14 1 3c3 0 11 1 13-1 2-1 1-6 1-8z" class="V"></path><path d="M253 551h1c1 0 4 1 5 1v1h-6 0v-2z" class="D"></path><path d="M279 530h1v1 12h-1v-1h-3c-3 0-7 0-9-1h-2c-1 0-1 0-2-1h-16 0v-1h14 1 3c3 0 11 1 13-1 2-1 1-6 1-8z" class="E"></path><path d="M111 451h4c0-1 1-1 1-2h2l1-1h0c1 1 4 1 5 1 4 0 8-1 11 0h11c1 0 3 1 5 0l10 24c-1-1-2 0-3 0h-14-9-10-6v-13c0-3 1-6 0-9h-8z" class="c"></path><path d="M129 451h6v7 15h-10-6v-13c0-3 1-6 0-9h1 8 1z" class="u"></path><path d="M128 451h1c0 6-1 12 1 18h0l-1 1c-3-5 0-13-1-18v-1z" class="B"></path><path d="M135 451v7 10h0c-1-3-2-13-1-17h1z" class="T"></path><path d="M119 451h1v8 14h1 4-6v-13c0-3 1-6 0-9z" class="F"></path><path d="M166 524v-5h1c1 1 0 5 0 7l1 15h0 0l1-8v6 2 5h5 9c4 0 16 0 18-1 0 1 1 2 1 2 1 2 3 4 5 6-1 1-2 1-3 2v1-1h-1c-1 4-3 9-5 12v1l-1 1-1 1h-1l-1 1c0-1-1-2-1-3h0l-1-1v3c-1 1-1 2-1 3l-1 2-1 1h-1l-1 1h0c-2-1-5-1-8-1h-8-1 1 1v-1l2-2c-1-1-1-1-1-2h-1 0-1c0-1 0-2-1-3v-1-1-1c-1 0-1 0-2-1h0c0-1 1-1 1-2-1 0-1-1-3 0h0v-2c0-1 0-1 1-1-1-1-1-2-1-2v-1-2h0l-1-2c0-1 0-3 1-4-1-2-1-5-1-7v-15l1 3v-5z" class="p"></path><path d="M166 524v-5h1c1 1 0 5 0 7l1 15v5h-1 0c-1-7 0-15-1-22z" class="C"></path><path d="M201 545c0 1 1 2 1 2-1 1-1 1-2 1 0-1-1-1-2-1h-2-1 0c-2-1-5 0-7 0h-4-3c-2 0-3 1-5 1-1 0-1-1-1-1-2 1-2 1-3 0h0l2-1h9c4 0 16 0 18-1z" class="B"></path><path d="M174 556h2 0c0 1 0 1-1 1 1 1 1 1 2 1v3 1l-1-1v-1h-1v1 3c1 0 1-1 2-1 0 2-1 3-2 5h0v-2h-1s0 1-1 1h0c-1 2-1 2-1 4h-1c0-1 0-2-1-3v-1-1c1-1 1-2 1-3s1-1 1-2c1-1 1-2 2-2v-2-1z" class="AF"></path><path d="M172 561c1-1 1-2 2-2 0 3-1 5-1 8-1 2-1 2-1 4h-1c0-1 0-2-1-3v-1-1c1-1 1-2 1-3s1-1 1-2z" class="B"></path><path d="M166 548c1 0 3 0 4 1h3c0 1 0 2-1 3h1 1c-1 1-1 1-1 3 1 0 1 0 1 1v1 2c-1 0-1 1-2 2 0 1-1 1-1 2s0 2-1 3v-1c-1 0-1 0-2-1h0c0-1 1-1 1-2-1 0-1-1-3 0h0v-2c0-1 0-1 1-1-1-1-1-2-1-2v-1-2h0l-1-2c0-1 0-3 1-4z" class="P"></path><path d="M166 548c1 0 3 0 4 1v1l-3 4c-1-1 0-3-1-5h0l-1 3c0-1 0-3 1-4z" class="AC"></path><path d="M167 554l3-4 1 2-1 2h0 1v3l-1 1s1 1 1 2c-2-1-3-4-4-6z" class="K"></path><path d="M170 549h3c0 1 0 2-1 3h1 1c-1 1-1 1-1 3 1 0 1 0 1 1v1 2c-1 0-1 1-2 2l-1-1c0-1-1-2-1-2l1-1v-3h-1 0l1-2-1-2v-1z" class="B"></path><path d="M171 554v-1c1 1 1 3 2 5l1-1v2c-1 0-1 1-2 2l-1-1c0-1-1-2-1-2l1-1v-3z" class="R"></path><path d="M202 547c1 2 3 4 5 6-1 1-2 1-3 2v1-1h-1c-1 4-3 9-5 12v1l-1 1-1 1h-1l-1 1c0-1-1-2-1-3h0l-1-1v3l-1-2v-1c0-1 0-3-1-5h-1v-2c1-1 0-1 0-2h0c-1-1-1-1-1-2h-1l1-2c-1 0-1 0-1-1-1-1-2-3-2-5h0c1 0 2 2 2 3s2 2 2 3c0 0 0 1 1 1h0v-1c0-1 0-3-1-3-1-1-2-2-2-3h1c1 1 4 0 5 1 1-1 1-1 2-1l1 1h0l1-1s1 0 1 1l2-1c1 0 1 0 2-1z" class="S"></path><path d="M190 562c1 0 1 1 2 1h1v3c0 1-1 0-1 1v3l-1-2v-1c0-1 0-3-1-5z" class="O"></path><path d="M195 570c0-1 0-2-1-3v-2l1-1c1 0 1 1 1 1 0 1 1 1 1 2h1v1l-1 1-1 1h-1z" class="F"></path><path d="M197 567v-1-2h2c0-2 0-3 1-4 0-1 1-2 1-3-1-1-1-1 0-2h2c-1 4-3 9-5 12h-1z" class="f"></path><path d="M190 554c0-1 0-3-1-3-1-1-2-2-2-3h1c1 1 4 0 5 1l2 2h1l-1 2v1l1 1 2-2v2 1 1c-1 0-1 1-2 2v2h-1v1c-1 0-1 0-1-1l-3-5h-1l1-1-1-1z" class="B"></path><path d="M194 561v-2c-1-1-1-1 0-3 0-1-2-3-2-4l1-1v1l1 3v1h1v-3 1l1 1 2-2v2 1 1c-1 0-1 1-2 2v2h-1v1c-1 0-1 0-1-1zm-14-11v-2c1 1 1 1 1 2l1 3c1 1 2 2 2 3l1 1c0 1 0 2 1 2v1h1v-1l-2-4c-1-1-1-1 0-1l-1-1c0-1 0-1-1-2 0-1-1-2-1-3 1 1 1 1 1 2l1 1c1 1 1 2 1 3l1 1h0c0 2 1 3 2 4v1h0v5h1c1 1 1 2 1 3h1l1 2c-1 1-1 2-1 3l-1 2-1 1h-1l-1 1h0c-2-1-5-1-8-1h-8-1 1 1v-1l2-2c-1-1-1-1-1-2h-1 0c0-2 0-2 1-4h0c1 0 1-1 1-1h1v2h0c1-2 2-3 2-5h2c0-1-1-1-1-2s0-1 1-2v-1c1 0 1 1 1 1 0 1 1 2 2 2h0c0-2 0-2-2-3 0-1 1-1 0-2v-1-1c-1-1-1-2-2-3v-1h0 0l2 1h0v-1z" class="J"></path><path d="M179 563c0-1-1-1-1-2s0-1 1-2l1 4h0-1z" class="f"></path><path d="M186 572l1-1c2 0 2 0 2 2l-1 2c0-1-1-1-1-1l-1-2z" class="l"></path><path d="M172 575l2-2c0 1 1 1 1 2v1h-4-1 1 1v-1z" class="u"></path><path d="M181 565h1 1 0c0 1 1 2 2 3s1 3 1 4l1 2h-1l-1-3-1-1c-1-2-1-3-3-5z" class="q"></path><path d="M173 567h0c1 0 1-1 1-1h1v2h0c0 3 0 4 1 7h0-1c0-1-1-1-1-2-1-1-1-1-1-2h-1 0c0-2 0-2 1-4z" class="f"></path><path d="M180 550l1 2c1 3 3 6 4 8 0 1 0 1 1 2l1 1-1 1-1-1h0c0 1 0 1 1 1l1 1c0 1 0 1-1 2 0-1-1-2-1-2l-1-1c-1 0-1 0-2-1v-2h0c0-2 0-2-2-3 0-1 1-1 0-2v-1-1c-1-1-1-2-2-3v-1h0 0l2 1h0v-1z" class="F"></path><path d="M180 576c0-1 0-2-1-2 0 0 0 1-1 1h0c0-1-1-1-1-2s-1-3-1-4 1-3 2-3v-1c0-1 0-1 1-1h1v2h0l1-1c2 2 2 3 3 5l1 1 1 3h1s1 0 1 1l1-2h2l-1 2-1 1h-1l-1 1h0c-2-1-5-1-8-1h1z" class="AB"></path><path d="M185 571l1 3h1s1 0 1 1l1-2h2l-1 2-1 1h-1l-1 1h0c-2-1-5-1-8-1h1 3c-1-1-1-1-1-2h2 0c0-1 0-1 1-3z" class="B"></path><path d="M233 481v-5l17 7c-1 2 0 4 0 6v3l3 15c0 2 0 6 1 8l-1 1v4c-1 2 0 4 0 6v13h9-1-14v1h0-1c0 2 0 5-1 6v1c-2 1-5-1-7-1l-5-1c-1-3-1-7-1-10 0-7 0-14 1-21v2l1 1v-1h0v-4c1-2 1-2 0-4l-1 1v-1-11-16z" class="U"></path><path d="M246 515h3v1c0 1-1 1-2 1l-1-2z" class="I"></path><path d="M245 489c1 1 1 2 2 3-1 1-2 2-2 3-1-2-1-4 0-6z" class="AE"></path><path d="M237 481h2c1 0 1 1 2 2 0 1 0 1-1 2s-2 1-3 0c0 0-1-1-1-2s0-1 1-2z" class="t"></path><path d="M234 508c1-1 2-1 3-1 2 1 2 1 2 3 0 1 0 1-1 2-2 1-3 0-4 0 1-2 1-2 0-4z" class="P"></path><path d="M242 489h3c-1 2-1 4 0 6-1 0-2 1-3 1-1-1-1-1-2-1 0-1 0-1-1-1v-3c1-1 2-1 3-2z" class="a"></path><path d="M240 495c1-1 1-2 2-2 0 1 1 1 0 3-1-1-1-1-2-1z" class="K"></path><path d="M242 489l2 2-1 1h-1v1c-1 0-1 1-2 2 0-1 0-1-1-1v-3c1-1 2-1 3-2z" class="AC"></path><path d="M246 500h1c1 1 1 1 1 2v1c0 1 0 2-1 2-2 0-3 1-5 0v-4c1-1 3-1 4-1z" class="C"></path><path d="M246 500h1c1 1 1 1 1 2-1 0-1 0-2-1v-1z" class="AE"></path><path d="M233 481c0 1 0 4 1 5s2 2 2 4c1 0 0 2 0 2l1 8v2h1c0 1-1 1-1 2s-1 2-1 2c0 1 1 1 1 1-1 0-2 0-3 1l-1 1v-1-11-16z" class="G"></path><path d="M234 516c1 0 2 0 3 1 1 0 3 1 4 0l-1-1v-2c2-1 2-1 4-1 1 1 1 1 1 2h1l1 2-1 1v9 13c0 2 0 5-1 6v1c-2 1-5-1-7-1l-5-1c-1-3-1-7-1-10 0-7 0-14 1-21v2l1 1v-1z" class="AD"></path><path d="M240 536h1v1 2h-2c0-2 0-2 1-3z" class="AA"></path><path d="M245 515h1l1 2-1 1h-3 0v-1c1 0 1-1 2-2h0z" class="E"></path><path d="M243 518c1 2 1 7 0 9 0 1 0 1-1 2v1 1-2h0c2-3 1-6 1-9h-1v-2h1z" class="AA"></path><path d="M240 529h2v2c-1 0-1 1-1 1h-2 0c-2-1-2-1-3-2l1-1h2 1z" class="g"></path><path d="M240 529h2v2c-1 0-1 1-1 1-1-1-2-2-2-3h1z" class="r"></path><path d="M238 546l-1-1h0c1-1 1-1 2-1l1 1v-1h1l1 1c1 1 2 1 3 1v1c-2 1-5-1-7-1z" class="p"></path><path d="M242 520h1c0 3 1 6-1 9h0-2c0-1 0-1 1-2l1-1-1-1h-1c1-1 1-1 1-2l-1-1c0-1 1-2 2-2z" class="t"></path><path d="M99 415h0c0 2 1 3 1 4l19-1c1 1 4 1 6 2l15-3c1 8 4 15 7 22l-27 9h5l-1 1c-1 0-4 0-5-1h0l-1 1h-2c0 1-1 1-1 2h-4c-2 0-4 0-5 1l-1-1c-1 1-3 1-4 0v-6-6c-1-1 0-3-1-4h-1 0v-6c-1-3 0-6-1-9 0-1 0-2-1-3v-1l2-1z" class="Z"></path><defs><linearGradient id="Q" x1="116.272" y1="429.109" x2="96.822" y2="418.297" xlink:href="#B"><stop offset="0" stop-color="#0c0a0b"></stop><stop offset="1" stop-color="#3b3631"></stop></linearGradient></defs><path fill="url(#Q)" d="M99 415h0c0 2 1 3 1 4l19-1c1 1 4 1 6 2l-25 2 1 17c-1-1 0-3-1-4h-1 0v-6c-1-3 0-6-1-9 0-1 0-2-1-3v-1l2-1z"></path><path d="M165 503c0 2-1 5 1 7-1 5-1 9-1 14h-15c-1 0-3 1-5 1-3-1-7-3-10-5v9c4 0 6-2 10-3 3-1 7-1 10-1 4 0 7-1 10-1v2 15c0 2 0 5 1 7-1 1-1 3-1 4l1 2h0l-19 1h-6c-3 0-8 0-10 1l-1-1v-1-2c-1 0-1-1-2-1v-1l-1-1-1 2-3-4-13-15 13-6c4-2 8-4 11-6 1-1 0-2 0-3v-13h12c1 0 2 0 3 1 2 1 4 0 6 0 1-1 1-1 2-1h1c2 1 5 0 7-1z" class="Y"></path><path d="M124 544c-1-4-1-10-1-14h17c1 2 0 4 1 7h-1v-2c-2 0-5 0-7-1-2 0-5 1-7 0h-1v1h0v1l2 1h-3v7z" class="AC"></path><path d="M125 535h0v-1h1c2 1 5 0 7 0 2 1 5 1 7 1v2h1v18c-3 0-8 0-10 1l-1-1v-1-2c-1 0-1-1-2-1v-1l-1-1-1 2-3-4 1-2v-1-7h3l-2-1v-1z" class="AA"></path><path d="M131 540c2 0 3 0 4 1h0-1-3 2v-1h-2z" class="AC"></path><path d="M125 535c2 0 5 0 8 1h0c-1 1-4 1-6 1l-2-1v-1z" class="K"></path><path d="M124 545c2 0 6-1 8 1 1 0 1 0 1 1h-1-1c-2-1-3-1-5-1v1l1 1v1l-1 2-3-4 1-2z" class="k"></path><path d="M130 551c2 1 5 1 7 0l3 3c-3 0-8 1-10 0v-2-1z" class="j"></path><path d="M127 549v-1l-1-1v-1c2 0 3 0 5 1h1c2 1 3 3 5 4-2 1-5 1-7 0v1c-1 0-1-1-2-1v-1l-1-1z" class="F"></path><path d="M128 550h4l3 1h0-5v1c-1 0-1-1-2-1v-1z" class="N"></path><path d="M127 549v-1l-1-1v-1c2 0 3 0 5 1h-1c0 1 1 1 1 2l1 1h-4l-1-1z" class="AD"></path><path d="M101 445v6c1 1 3 1 4 0l1 1c1-1 3-1 5-1h8c1 3 0 6 0 9v13h6 10 9c-1 0-2 0-2 1 2 0 4-1 5 1 0 0 0 1-1 1 1 1 0 2 1 3v1c-1 2-1 3-1 5-1 5-1 9-1 13v4c1 1 2 1 2 2h-1-12v13c0 1 1 2 0 3-3 2-7 4-11 6l-13 6-6-9-7-10c-2-4-4-9-7-14l-4-9c-3-7-6-14-8-21v-6l-1-2v-4-5h0v-1c1-1 2-1 3-2v1l1 1v1h1 9l1-1v1h4v-1h5v-6z" class="y"></path><path d="M97 467h1v2l-1 2c0-1 0-2-1-3v-1h1z" class="B"></path><path d="M86 484c2-1 7 0 10 0v1h-1c-2 0-8 1-9-1zm11-5h-4l-1-1h-2c-2 0-6 1-8 0 1-1 1 0 0-1h0c4-1 9-1 13 0h0c1-1 1-2 1-2l1 4z" class="F"></path><path d="M77 451c1-1 2-1 3-2v1l1 1v1h1 9l1-1v1h4v1 3 1 4c-1 2 0 4 0 6v1c-2 1-6 1-9 1h-4-1l-1-1h8 5c-1-1-3-1-4-1h0 4v-1c-3 0-5 1-7 1v-1c1 0 2 1 3-1h0c-4-1-7-1-11-1v2h0v1c-1 0-1 1-1 2v-6l-1-2v-4-5h0v-1z" class="p"></path><path d="M77 461h2c1 1 2 0 3 0h3 4c1 0 2 0 2 1h2v1H78l-1-2z" class="P"></path><path d="M85 461h4c1 0 2 0 2 1-2 0-6 1-9-1h3z" class="f"></path><defs><linearGradient id="R" x1="86.048" y1="455.574" x2="87.194" y2="450.626" xlink:href="#B"><stop offset="0" stop-color="#3c2d2c"></stop><stop offset="1" stop-color="#413f37"></stop></linearGradient></defs><path fill="url(#R)" d="M77 451c1-1 2-1 3-2v1l1 1v1h1 9l1-1v1h4v1 3-2l-1-1c-2 1-3 1-5 1-4 1-9 2-12 1v1l-1 1v-5h0v-1z"></path><path d="M77 451c1-1 2-1 3-2v1l1 1v1h1 9l1-1v1c-1 1-2 1-3 1h-8-3v2 1l-1 1v-5h0v-1z" class="AG"></path><path d="M77 451c1-1 2-1 3-2v1l1 1v1h-4v-1z" class="J"></path><path d="M78 456c5 1 11-1 16 1v1h1v1h-1v1l-1 1h-4-4-3c-1 0-2 1-3 0h-2v-4l1-1z" class="y"></path><path d="M79 461v-1h6v1h-3c-1 0-2 1-3 0z" class="S"></path><path d="M94 459h-4 0c1-1 3-1 4-1h1v1h-1z" class="f"></path><path d="M85 460c2-1 5 0 7 0h1v1h-4-4v-1z" class="B"></path><path d="M101 445v6c1 1 3 1 4 0l1 1c1-1 3-1 5-1h8c1 3 0 6 0 9v13h-3-6 0-3-2-2-2-1-3v-1-1l1-2v-2h-1-1c0-2-1-4 0-6v-4-1-3-1-1h5v-6z" class="R"></path><path d="M109 465l1-3v3l1 8h-1 0c-1 0-1-1-1-2v-6z" class="B"></path><g class="l"><path d="M107 453l1-1h0l1 1c0 4-1 9 0 12v6c0-1-1-2-1-3l-1-15zm3 12c0-3-1-8 0-11h1c1 6 0 12 1 18v1h-1l-1-8z"></path><path d="M106 456c0-1 0-2 1-3l1 15c0 1 1 2 1 3s0 2 1 2h-3-2v-1-4-4c0-2 0-6 1-8z"></path></g><path d="M108 468c0 1 1 2 1 3s0 2 1 2h-3-2v-1-4 4h1l1-1v1h1v-4z" class="J"></path><path d="M106 456c0-1 0-2 1-3l1 15v4h-1v-1l-1-15z" class="B"></path><path d="M111 451h8c1 3 0 6 0 9v13h-3-6 1 1v-1c1-1 1-1 1-2v-3-12c-1-1-1-1-1-2v-1l-1-1z" class="J"></path><path d="M113 467l1-1v7h0c-1-1-1-2-1-3v-3z" class="n"></path><path d="M113 455v-3h1v6 8l-1 1v-12z" class="l"></path><path d="M101 445v6c1 1 3 1 4 0l1 1h2l1 1-1-1h0l-1 1c-1 1-1 2-1 3-1 2-1 6-1 8v4 4 1h-2-2-1-3v-1-1l1-2v-2h-1-1c0-2-1-4 0-6v-4-1-3-1-1h5v-6z" class="R"></path><path d="M96 453h1v3l-1 1v-1-3z" class="B"></path><path d="M98 469h0l1 1c0 2 0 2-1 3l-1-1v-1l1-2z" class="n"></path><path d="M101 460c0-2-1-5 0-7h0s1 0 1 1v5l-1 1z" class="l"></path><path d="M102 459h0c0 5 0 10-1 14h-1 1v-1-2-1-1h0v-8l1-1z" class="B"></path><path d="M96 457l1-1c0 3-1 8 0 11h-1c0-2-1-4 0-6v-4z" class="u"></path><path d="M102 459c0-1 0-2 1-4v8 10h-2c1-4 1-9 1-14z" class="F"></path><path d="M98 469v-16-1h1v1 5 11 1h0l-1-1z" class="T"></path><path d="M105 451l1 1h2l1 1-1-1h0l-1 1c-1 1-1 2-1 3-1 2-1 6-1 8v4 4l-1-1v-11c0-2 0-5 1-7v-2z" class="B"></path><path d="M105 451l1 1h2l1 1-1-1h0l-1 1c-1 1-1 2-1 3-1 2-1 6-1 8v-11-2z" class="J"></path><path d="M110 473h6l1 18c0 3 0 7-1 9l-1 1-17 9c-1-1 0-6 0-8l-1-23-1-4 1-1c1-1 8-1 10-1h3 0z" class="Z"></path><path d="M115 501h1c0 1 1 1 1 1l8 1h9v1 13c0 1 1 2 0 3-3 2-7 4-11 6l-13 6-6-9-7-10c2-1 4-2 5-2l7-4 7-4h0-1c-4 1-7 4-11 6l-2 1c-1 0-2 0-3 1h-1-1l1-1 17-9z" class="W"></path><path d="M112 509c1 0 1 0 1 1-2 2-4 2-6 4-1 0-1 1-2 1v-1c1 0 1 0 1-1-1 1-1 1-2 1l8-5z" class="j"></path><path d="M125 503h9v1 13c0 1 1 2 0 3-3 2-7 4-11 6l-13 6-6-9s1-1 2-1l2-1h1c0-1 0 0 1-1h1l4-2 1-1h1v-1c2-1 4-1 5-2l3-2 1-1c1 0 2 0 2-1 1 0 2-1 2-1l2-2h0-2l-1 1c-1 1-2 1-3 2h0-2 0 0c1-1 2-1 3-2 0 0 1 0 2-1 1 0 2-1 2-1 1 0 1 0 1-1h0l2 1c0-2 0-2-1-3-2 0-8 0-10 1v-1h2z" class="D"></path><path d="M135 473h9c-1 0-2 0-2 1 2 0 4-1 5 1 0 0 0 1-1 1 1 1 0 2 1 3v1c-1 2-1 3-1 5-1 5-1 9-1 13v4c1 1 2 1 2 2h-1-12v-1h-9l-8-1s-1 0-1-1h-1l1-1c1-2 1-6 1-9l-1-18h3 6 10z" class="E"></path><path d="M144 499l1-1v4c1 1 2 1 2 2h-1-12v-1c2 0 8 0 10-1v-3z" class="e"></path><path d="M144 499v-1c-1-3-1-7 0-11 0-3 0-5 2-7h1c-1 2-1 3-1 5-1 5-1 9-1 13l-1 1z" class="I"></path><path d="M339 510v4h-1v4c1 2 1 3 2 5 0 1 1 2 1 3h0l1 2v1 1c1 2 2 6 3 8v2l1 1h0c0 1 0 1 1 2v1c1 1 3 1 4 1h0-6c-1 3-1 6-2 8-1 3-3 5-4 7v12c0 1 0 3 1 4l-6 1-1 8v4 1 7h0-10c-4 0-8 0-11-1h-2c0-1 0-1 1-2v-1h-2v-6-2c-1-2 0-5-1-8l-2-1h0v-17c0-3-1-6 0-9v-1s0-1-1-1v1h-6-9c-1-1-1-2-1-2-1-1-1-2-1-3l-2 1c-1 0-2 0-3 1l1 1h-2-2v-4-12-1h-1c0 2 1 7-1 8-2 2-10 1-13 1h-3-9v-13c0-2-1-4 0-6v-4l1-1h2c1 0 1-1 1-1v1h1l1-1v-1 2h1l3-1c1 0 3 0 4 1 2 2 7 1 10 1l1-1h0c3 0 6-1 9-1h16 0 0l1-1h23c3 0 10 0 11-1l1-2z" class="V"></path><path d="M304 520v-1c1-1 3 0 4-1h2c-1 1-2 2-2 3l1 1h-1 0c-2-2-2-2-4-2z" class="j"></path><path d="M310 518h2l1 1v1 1s0 1-1 1c-1 1-2 1-3 1l-1-1h1l-1-1c0-1 1-2 2-3z" class="e"></path><path d="M279 530c2-3 1-9 1-12h2l6 2h0c-2 0-5 0-7 1 0 2 1 5 0 8h1c-1 1-2 1-2 1h-1z" class="N"></path><path d="M317 518h1 0 3 7v2 1l-2 1c0-1-1-1-2-1h0v-2h-1v1c-1 1-1 1-2 1h-2l-1-1s-1-1-1-2z" class="p"></path><path d="M317 518h1 0 3l-2 2h-1s-1-1-1-2z" class="u"></path><path d="M313 519l1-1h3c-1 2-1 2 0 3l-1 1h0-1c-1 2-1 2-2 3h-1c0 1-1 2-2 4 0 2-2 4-3 6h0l-1 1c-1-1-1-2-1-3v-2-4h0l-1-1v-1c1 0 1-1 1-1h1v-1h-3c1 0 2 0 3-1l-2-2c2 0 2 0 4 2h0l1 1c1 0 2 0 3-1 1 0 1-1 1-1v-1-1h0z" class="E"></path><path d="M313 519l1-1h3c-1 2-1 2 0 3h-1c-2 0-2-1-3-2z" class="l"></path><path d="M306 522v2l1 1h2v1l-1 1-1-1v1h-2l-1-1v-1c1 0 1-1 1-1h1v-1h-3c1 0 2 0 3-1z" class="G"></path><path d="M307 527h1c0 1-1 1-1 2h-1l1 1 1-1c0-1 1-1 1-1 1 0 0 1 0 2s-2 3-2 5l-1 1c-1-1-1-2-1-3v-2-4h0 2z" class="U"></path><path d="M339 510v4h-1v4c1 2 1 3 2 5 0 1 1 2 1 3h0l-1 1v-1c0-1-1-2-1-3v-1c-1-1-1-1-2-1h-1-1 0c0-1-1-2-1-3h0-1 0c0-1 1-1 1-1v-2c-1 0-2 0-3-1-3 0-5 1-8 0h0l-1 1c-2-1-2-1-3 0-1 0-2-1-3-1l-1 1c-2 0-10 1-12-1l1-1h23c3 0 10 0 11-1l1-2z" class="W"></path><path d="M323 514h7 1c2 0 6-1 7 0v4c1 2 1 3 2 5 0 1 1 2 1 3h0l-1 1v-1c0-1-1-2-1-3v-1c-1-1-1-1-2-1h-1-1 0c0-1-1-2-1-3h0-1 0c0-1 1-1 1-1v-2c-1 0-2 0-3-1-3 0-5 1-8 0h0z" class="E"></path><path d="M288 520c3 0 5 1 7 1 2 1 5 1 7 2h1 3v1h-1s0 1-1 1v1l-1 1h1l-1 1h-3v2h-1l-1 1h-3v-1h-1v-1l-1-1-11 1h-1c1-3 0-6 0-8 2-1 5-1 7-1h0z" class="G"></path><path d="M302 523h1 3v1h-1s0 1-1 1v1l-1 1h1l-1 1h-3v-1h0c1-1 0-2 1-2h0 1c0-1-1 0-1-1 0 0 1 0 1-1h-1 0 1z" class="C"></path><path d="M300 527h3 1l-1 1h-3v-1h0z" class="q"></path><path d="M293 528l7-1v1 2h-1l-1 1h-3v-1h-1v-1l-1-1z" class="AG"></path><path d="M296 529h0c1 0 2 0 2-1l1 1v1l-1 1h-3v-1c0-1 0-1 1-1z" class="o"></path><path d="M295 530c0-1 0-1 1-1l2 2h-3v-1z" class="c"></path><path d="M304 526l1 1h0v4 2c0 1 0 2 1 3l1-1h0c-2 2-2 4-2 7v3 3 1h-6-9c-1-1-1-2-1-2-1-1-1-2-1-3l-2 1c-1 0-2 0-3 1l1 1h-2-2v-4-12-1s1 0 2-1l11-1 1 1v1h1v1h3l1-1h1v-2h3l1-1h-1l1-1z" class="i"></path><path d="M281 536h0c2 0 2 0 3 1 0 2 0 3-1 4l-1 1c-1-2-1-2 0-4 0-1-1-2-1-2z" class="q"></path><path d="M280 531c1 3 0 6 0 8v2l2 2s0 1 1 1 2 1 3 1c-1 0-2 0-3 1l1 1h-2-2v-4-12z" class="AC"></path><path d="M282 547c0-1 0-1 1-2v1l1 1h-2zm0-4h2l1-1h2c0-1 1-1 0-3h-1c1-1 1-1 1-2 0 0 1-2 0-3 0-1-1-1-2-2v-2h1v1c1-1 1-1 2-1 0-1 1-1 2 0v-1l-1 18h0c3 0 6 1 9 0l1 2h-9c-1-1-1-2-1-2-1-1-1-2-1-3l-2 1c-1 0-2-1-3-1s-1-1-1-1z" class="o"></path><path d="M290 529h4v1 1c1 1 1 2 1 3 1 4 3 9 3 13-3 1-6 0-9 0h0l1-18z" class="L"></path><path d="M294 531c1 1 1 2 1 3-1 0-1 1-2 1v-1c0-1 0-2 1-3z" class="d"></path><path d="M304 526l1 1h0v4 2c0 1 0 2 1 3l1-1h0c-2 2-2 4-2 7v3 3 1h-6l-1-2c0-4-2-9-3-13 0-1 0-2-1-3v-1h1v1h3l1-1h1v-2h3l1-1h-1l1-1z" class="i"></path><path d="M304 526l1 1h0c0 1 0 2-1 3h-1l-1-1h1v-1l1-1h-1l1-1z" class="g"></path><path d="M300 533h1l1 1c-1 2-1 3-3 4h0-1v-1c1-2 2-3 2-4z" class="F"></path><path d="M303 528v1h-1 0c-1 1-1 2-3 2v1l1 1c0 1-1 2-2 4v1l-1-1c0-1-1-2-1-4 0 0 0-1-1-2h3l1-1h1v-2h3z" class="t"></path><path d="M333 518h1 0c0 1 1 2 1 3h0 1 1c1 0 1 0 2 1v1c0 1 1 2 1 3v1l1-1 1 2v1 1c1 2 2 6 3 8v2l1 1h0c0 1 0 1 1 2v1c1 1 3 1 4 1h0-6c-1 3-1 6-2 8-1 3-3 5-4 7-1-2-1-8-1-10v-3h-1 0l-12 2-10 1c-2 1-4 1-6 1l-1 1h0v-2h-2v-1s0-1-1-1v-3-3c0-3 0-5 2-7 1-2 3-4 3-6 1-2 2-3 2-4h1c1-1 1-1 2-3h1 0l1-1c-1-1-1-1 0-3h0c0 1 1 2 1 2l1 1h2c1 0 1 0 2-1v-1h1v2h0c1 0 2 0 2 1l2-1v-1-2h2 3z" class="U"></path><path d="M324 540v1s-1 1-1 2c0 0 0 1-1 2s-1 1-2 0c1-1 3-4 4-5z" class="G"></path><path d="M324 540c1-2 2-4 4-5h2v1h-1-1v1h1c1 1 1 0 1 1l-1 1c-1 2-2 5-3 6l-1 1-1-1-1-2c0-1 1-2 1-2v-1z" class="I"></path><path d="M326 545c-1-1 1-2 1-4h-1c0-1 0-1 1-2l1 1v-1h1c-1 2-2 5-3 6z" class="E"></path><path d="M306 549l19-1c4-1 8-1 12-1h0l-12 2-10 1c-2 1-4 1-6 1l-1 1h0v-2h-2v-1z" class="O"></path><path d="M333 518h1 0c0 1 1 2 1 3h0v1l-1-1c0 1-1 1-1 1l-1 1v3c1 1 1 2 2 3v1l1 4v1c0 1 1 1 1 2s0 2 1 3v3 1c-1-1-1-1-1-2 0-2-1-4-1-6l-1-1h0v-2h0v-1c-1-1-1-1-1-2-1-1-1-2-1-3-1 0-2 1-3 2 0 0-1 0-1-1s1-1 1-2v-1c-1 0-1 0-1 1h0-1v-2c1-1 1-1 1-2v-1-1-2h2 3z" class="G"></path><path d="M329 526v-1l1-1h0c1 0 1 1 1 1 0 1 1 1 1 2-1 0-2 1-3 2 0 0-1 0-1-1s1-1 1-2z" class="Q"></path><path d="M333 518h1 0c0 1 1 2 1 3h0v1l-1-1h-1c-1-1-2-1-3-1v1l-2-1v-2h2 3z" class="P"></path><path d="M328 518h2v2 1l-2-1v-2z" class="S"></path><path d="M321 521c1 0 1 0 2-1v-1h1v2h0c1 0 2 0 2 1l2-1v1c0 1 0 1-1 2v2h1 0c0-1 0-1 1-1v1c0 1-1 1-1 2s1 1 1 1l1 1c-1 1-2 1-2 2-2 4-6 6-8 10 0 1-1 2-2 3 0 1 0 1-1 0-1 0-2 1-3 0l3-3h1c-1-1-1-2 0-2 1-2 1-4 2-6-2 1-4 3-5 5s-2 4-4 6v-1l1-1v-1c1-1 1-2 2-3 0 0 0-1 1-1 0-1 0-2 1-2v-1l-1-1c1-2 3-4 5-6 1-1 2-2 2-3s-1-1-1-2v-2z" class="I"></path><path d="M324 521c1 0 2 0 2 1v2h-1c-1-1-1-2-1-3z" class="Q"></path><path d="M318 542l1-2c1-1 1-2 2-3 0 0 0-1 1-1v-2h-3l4-6c1 0 1-1 2-2v1s0 1-1 1c0 1 0 1-1 2l-1 2h0c3 0 3-3 5-5l1 1h0c0 1 1 1 1 1l1 1c-1 1-2 1-2 2-2 4-6 6-8 10 0 1-1 2-2 3 0 1 0 1-1 0-1 0-2 1-3 0l3-3h1z" class="G"></path><path d="M328 528h0c0 1 1 1 1 1l1 1c-1 1-2 1-2 2l-1-1c0 1-1 1-1 2s-1 1-1 1h-1c1-2 2-4 4-6z" class="h"></path><path d="M324 534h1s1 0 1-1 1-1 1-2l1 1c-2 4-6 6-8 10 0 1-1 2-2 3 0 1 0 1-1 0h1v-2l3-5 3-4z" class="M"></path><path d="M317 518h0c0 1 1 2 1 2l1 1h2v2c0 1 1 1 1 2s-1 2-2 3c-2 2-4 4-5 6l1 1v1c-1 0-1 1-1 2-1 0-1 1-1 1-1 1-1 2-2 3v1l-1 1v1l-1 1c-1-1-2 0-4-1 2-1 2-2 3-4l-1-1c-1 2-1 3-3 5v-3c0-3 0-5 2-7 1-2 3-4 3-6 1-2 2-3 2-4h1c1-1 1-1 2-3h1 0l1-1c-1-1-1-1 0-3z" class="U"></path><path d="M305 542c4-2 3-6 4-9l3-3c0-2 1-3 2-4l2-2h0c0 2-1 3-2 4l-1 1c0 1 0 1-1 1 0 1-1 2-1 3l-2 4c0 1-1 2-1 3-1 2-1 3-3 5v-3z" class="I"></path><path d="M309 537h1c2-1 3-5 5-7 1-1 2-3 3-4v1l-1 1v1c0 1 0 0-1 1l-1 1v1l-3 6c1-1 2-2 3-4l1 1v1c-1 0-1 1-1 2-1 0-1 1-1 1-1 1-1 2-2 3v1l-1 1v1l-1 1c-1-1-2 0-4-1 2-1 2-2 3-4l-1-1c0-1 1-2 1-3z" class="E"></path><path d="M337 547h1v3c0 2 0 8 1 10v12c0 1 0 3 1 4l-6 1-1 8v4 1 7h0-10c-4 0-8 0-11-1h-2c0-1 0-1 1-2v-1h-2v-6-2c-1-2 0-5-1-8l-2-1h0v-17c0-3-1-6 0-9h2v2h0l1-1c2 0 4 0 6-1l10-1 12-2h0z" class="D"></path><path d="M325 549l12-2c0 1 0 4-1 5-1 4-3 8-7 10-3 2-6 3-10 2-3-1-6-3-8-6l-3-6 1-1c2 0 4 0 6-1l10-1z" class="Y"></path><path d="M315 550l10-1v2c1 1 7 0 9 1h1v1h-1c-1-1-3 0-4-1h-10c-1-1-3 0-5-2z" class="L"></path><path d="M325 549l12-2c0 1 0 4-1 5h-1-1c-2-1-8 0-9-1v-2z" class="Z"></path><path d="M338 550c0 2 0 8 1 10v12c0 1 0 3 1 4l-6 1-1 8v4 1 7h0-10c-4 0-8 0-11-1h-2c0-1 0-1 1-2v-1h-2v-6-2c-1-2 0-5-1-8l-2-1h32v-26z" class="q"></path><path d="M326 579v-1l1 1h-1c-2 1-4 1-6 1h-10-1v-1h17z" class="R"></path><g class="r"><path d="M325 581c3 0 6-1 8 0v4h-5l-1-1 1-1-2-1 1-1h-2z"></path><path d="M325 581h2l-1 1 2 1-1 1h-5-10-3v-2-1c6 0 11 1 16 0z"></path></g><path d="M326 582l2 1-1 1h-5l-2-1h0 2c1 0 3 0 4-1z" class="q"></path><path d="M325 581h2l-1 1c-1 1-3 1-4 1l-13-1v-1c6 0 11 1 16 0z" class="F"></path><path d="M322 584h5l1 1h0v1l-2 1h-14-3v-2h0v-1h3 10z" class="z"></path><path d="M322 584h5l1 1h0-2-2c-4 0-8 1-12 0h-3v-1h3 10z" class="P"></path><path d="M309 584h3c1 0 3 0 3 1h-3-3v-1z" class="F"></path><path d="M333 586v-1 4 1 7h0-10c-4 0-8 0-11-1h-2c0-1 0-1 1-2v-1h-2v-6h3 14l2-1h2 3z" class="p"></path><path d="M311 593h7l1 1h-8v-1z" class="r"></path><path d="M312 587h14 1c-1 1-2 1-3 1h0c-3 0-13 0-14-1h2z" class="B"></path><path d="M333 586v-1 4 1h-5l-1-1h-1v-1c-1 0-2 1-2 0h0c1 0 2 0 3-1h-1l2-1h2 3z" class="AG"></path><path d="M330 586h3v2h-1-2v-2z" class="p"></path><path d="M328 586h2v2c-1 0-2 0-3-1h-1l2-1z" class="F"></path><path d="M327 589h6v1h-5l-1-1z" class="P"></path><path d="M318 593c3 0 14 0 15-1v-1c-2 0-6 0-7-1h1 1 5v7h0v-3h0-8-6l-1-1z" class="AF"></path><path d="M325 594h8 0v3h-10c-4 0-8 0-11-1h-2c0-1 0-1 1-2h8 6z" class="y"></path><path d="M325 594h8 0v3h-10c-4 0-8 0-11-1h7 4c2-1 5-1 8-2h-6z" class="p"></path><path fill="#fff" d="M315 325c4 3 9 9 12 14 4 3 49 57 49 59l2 2c3 2 8 8 10 12v1c0 2-4 5-5 7s-2 3-2 5c-2 2-3 3-3 5l-3 5 1 2c1 1 0 3 1 5v1 3c0 1 1 8 0 9v1l-2 2c-1 1-2 1-4 2h1s1 1 2 0h0 2s1 0 1 1v5 8c0 2 0 4-1 5v1l-7 8v1c-2 0-3 0-4 1l1 1-2 1h-1v-2 1h-1l-2 2h-2l-1-1h-1c-1 1-1 1-1 2-1 0-2 0-2-1l-1 1c0 1-1 1-2 1 0 1-1 1-1 1 0 1 0 2 1 2l2 2v1c-1 0-1 0-2-1-1 1-1 1-1 3h-1l-1 1h-2-2l-1-1-4-3v7c0 1 0 2 1 3l-1 2c-1 1-8 1-11 1h-23l-1 1h0 0-16c-3 0-6 1-9 1h0l-1 1c-3 0-8 1-10-1-1-1-3-1-4-1l-3 1h-1v-2 1l-1 1h-1v-1s0 1-1 1h-2c-1-2-1-6-1-8l-3-15v-3c0-2-1-4 0-6h0c3 2 7 4 10 4 4 1 9 1 12 1 3-1 6-2 8-4 0-1 5-1 6-1s2-1 3-2c0 0 0-1-1-1-1-2-4-2-5-3h-3v-2-3c0-1 1-2 1-3 1-2 3-3 5-4 1-1 2-1 3-1v-1c-3 0-4 1-7 2h-1c-1 0-1 0-2-1v-1h0v-2l2 1v1c1 0 1 0 2-1v-1l-1-1h0c-1-1-5-1-7-2-3-1-5-3-8-5-1 0-2-1-3-2h-1l-1-1 2-9c1-3 1-7 1-11 1-3 0-7 0-11v-14l-2-2v-1h0 0v-4h-1c1 0 1-1 1-1-1-2-1-5-2-7h0v-2h-1-1c0 1 0 1-1 2v-1c0-1 0-2 1-3h0c0-1 0-2-1-3h0v-2-1h-1v-2c0-1 0-1-1-2l1-1s1 1 2 1v1h1v-1h0 1v-3c0-2-1-4-1-7l-2-1c-1-2 0-3-1-4v-3l-3-2-6-5h-1l-3-3c-2-2-5-3-7-5h-1l-1 1v-1c0-1 0-1-1-1s-1 0-2 1v-2-1h-1v1c-1-1-2-1-3-2 0 0 0-1-1-1l-3-1c1-1 1-1 1-2l-3-1v-1c2 0 3 0 4 1s3 1 5 2c2 0 5 2 8 3h0c1 0 2 1 2 1l2 1h1 3c3 1 5 3 8 3l-2-6v-1c2 0 5 1 7 2 1 0 2 1 4 1 1 1 3 2 4 2s2 1 3 1l1 1h1s1 1 2 1l1 1c1 0 2 0 3 1l2 1h1c1 1 2 1 2 1l1 1 2 1c1 0 2 1 3 1 1 1 1 1 2 1l1 1c1 0 1 0 2 1h1c1 1 2 1 3 2h1s0 1 1 1h1c3 2 6 3 9 5h0c2 1 2 2 3 2 1 1 1 0 1 1v-41z"></path><path d="M264 396l1 9-2-2v-1h0v-1-1c1-1 1-2 1-4z" class="AB"></path><path d="M355 446l1 1c-1 1 0 2 0 3-1 0-1 0-2 1 0-1-1-2-1-2l2-3z" class="AD"></path><path d="M350 452l3-3s1 1 1 2c1 1 1 1 1 2-2 0-3-1-5-1z" class="d"></path><path d="M332 464c-1 0-1 0-1-1 2 0 4-1 5-1v2h0-1c0 1-1 2-1 3l-2-3z" class="H"></path><path d="M325 482l1 5h0c0 1 0 2 1 3 0 1 0 1-1 2v1h-1v-11z" class="X"></path><path d="M261 373l1 8s-1-1-2-1v1l-1-1h-1-1v-2c0-1 0-1-1-2l1-1s1 1 2 1v1h1v-1h0 1v-3z" class="AB"></path><path d="M256 376l1-1s1 1 2 1v1h1v-1c0 2 1 3 0 4-1-1-1-1-2-1 0-1 0-1-1-1 0-1 0-1-1-2z" class="u"></path><path d="M258 380h1l1 1v-1c1 0 2 1 2 1l2 15c0 2 0 3-1 4v1 1h0v-4h-1c1 0 1-1 1-1-1-2-1-5-2-7h0v-2h-1-1c0 1 0 1-1 2v-1c0-1 0-2 1-3h0c0-1 0-2-1-3h0v-2-1z" class="S"></path><path d="M282 460c2 0 5 2 6 2 3 0 5 0 8 1 1 0 2 1 2 1v1 1l-1-1s-1 0-2-1c0 1 0 2 1 3h0c-1 0-2 0-4 1l-1 1-1-1c-1 0-1 0-1-1l-1 1c-2-1-2-2-2-3 1-1 2-1 3-1v-1c-3 0-4 1-7 2h-1c-1 0-1 0-2-1v-1h0v-2l2 1v1c1 0 1 0 2-1v-1l-1-1h0z" class="G"></path><path d="M292 464h0 2 1c0 1 0 2 1 3h0c-1 0-2 0-4 1l-1 1-1-1c-1 0-1 0-1-1l-1 1c-2-1-2-2-2-3 1-1 2-1 3-1h3z" class="E"></path><path d="M286 465c1-1 2-1 3-1h3c-1 1-1 1-1 2l-2 1-1 1c-2-1-2-2-2-3z" class="I"></path><path d="M296 463c2 0 7 0 8 1 1 0 2 1 3 1h0v5l-1 1s-1 0-1-1c-1 1-1 1-1 2h-3c-1 0-1 1-2 1h0v-1-1c0-1-1-2-2-3l-1-1h0c-1-1-1-2-1-3 1 1 2 1 2 1l1 1v-1-1s-1-1-2-1z" class="AE"></path><path d="M296 463c2 0 7 0 8 1h-1v2l-2-2h0c-1 1 0 1 0 2s-3 2-4 2l-1-1h0c-1-1-1-2-1-3 1 1 2 1 2 1l1 1v-1-1s-1-1-2-1z" class="I"></path><path d="M304 464c1 0 2 1 3 1h0v5l-1 1s-1 0-1-1c0 0 0-1-1-1v-2l-1-1v-2h1z" class="M"></path><path d="M329 478h-3c-2 0-4-1-5-3s-1-4 0-5c1-2 2-3 4-4s3-1 5 0c2 2 3 3 4 5-1 3-1 4-3 6l-2 1z" class="D"></path><path d="M222 333c2 0 3 0 4 1s3 1 5 2c2 0 5 2 8 3h0c1 0 2 1 2 1l2 1c2 2 6 2 8 3 1 1 3 3 5 3v1c1 1 1 3 1 5 0 0 1 1 1 2 2 2 14 6 14 8l-14-7h0c1 4 1 7 2 10l-2-1c-1-2 0-3-1-4v-3l-3-2-6-5h-1l-3-3c-2-2-5-3-7-5h-1l-1 1v-1c0-1 0-1-1-1s-1 0-2 1v-2-1h-1v1c-1-1-2-1-3-2 0 0 0-1-1-1l-3-1c1-1 1-1 1-2l-3-1v-1z" class="k"></path><path d="M257 358c0-2-4-4-6-6-1 0-1-1-2-2h0 0c2 2 7 4 9 6 1 4 1 7 2 10l-2-1c-1-2 0-3-1-4v-3z" class="S"></path><path d="M241 340l2 1c2 2 6 2 8 3l-1 1h-1c0-1 0-1-1-1v2 2h-1c-1-1-1-1-2-1h0c-1 0-1-1-1-1-1 0-2-1-2-1l-1-1s-1 0-1-1l1-1c0-1 1-1 0-2z" class="i"></path><path d="M241 340l2 1c2 2 6 2 8 3l-1 1h-1c0-1 0-1-1-1v2l-1-1s-1 0-1 1h0-1c0-1 1-1 0-2-1-2-2-2-4-2 0-1 1-1 0-2z" class="B"></path><path d="M222 333c2 0 3 0 4 1s3 1 5 2c2 0 5 2 8 3h0c1 0 2 1 2 1 1 1 0 1 0 2l-1 1c-1 0-1 0-1-1h0v-1h0l-1 1c-1-1-2-2-3-2-3-1-7-5-10-5l-3-1v-1z" class="J"></path><path d="M251 344c1 1 3 3 5 3v1c1 1 1 3 1 5-2 0-7-4-9-5v-2-2c1 0 1 0 1 1h1l1-1z" class="p"></path><path d="M251 344c1 1 3 3 5 3v1c0 1 0 1-1 1-2 0-5-2-7-3v-2c1 0 1 0 1 1h1l1-1z" class="O"></path><path d="M374 403l2-5 2 2c3 2 8 8 10 12v1c0 2-4 5-5 7s-2 3-2 5c-2 2-3 3-3 5l-3 5c-1 2-3 4-4 7-2-1-3 0-5-1v-1-3s-1 0-1-1h-2l1-1v-1l-1-1 2-4 3-7 2-3 1-3c0-1 0-2 1-3v-2l1-3h0c1-1 1-2 1-3v-1-1z" class="j"></path><path d="M376 424v-1c2 0 4-1 6-2-1 2-2 4-4 5h-1c0-1 0-1-1-2h0z" class="e"></path><path d="M363 433l2-4 1 1h0c2-1 3 0 5 0v1c-2 1-3 0-4 1v1h0c-1 0-2 0-3 1h0l-1-1z" class="d"></path><path d="M367 433h4c0 1-1 2-1 3-1 1-2 1-3 1h-1s-1 0-1-1h-2l1-1v-1h0c1-1 2-1 3-1h0z" class="G"></path><path d="M364 434h0c1-1 2-1 3-1l1 1v1h0c-1 0-1 0-2 1h-2v-1-1z" class="L"></path><path d="M384 415c0-1 1-2 2-3h0v1 1c-1 2-3 5-4 7-2 1-4 2-6 2v1h-2c-2 0-4-1-6-2l2-3 1-3c0-1 0-2 1-3 2 1 5 1 6 3h0l1 1h1c1-1 1-2 2-3v-1h0l2 2z" class="I"></path><path d="M370 419l1-3c0 2 2 3 3 3l2 1h0l1 1c-1 0-2 0-3-1-1 0-3 0-4-1z" class="L"></path><path d="M378 416l1 1h1c1-1 1-2 2-3v-1h0l2 2h0c-1 1-1 2-2 3l-1 1c0 1-1 1-1 2h-3l-1-1c1-1 1-3 2-4h0z" class="M"></path><path d="M372 413c2 1 5 1 6 3-1 1-1 3-2 4h0l-2-1c-1 0-3-1-3-3 0-1 0-2 1-3z" class="n"></path><path d="M374 403l2-5 2 2c3 2 8 8 10 12v1l-2 1v-1-1h0c-1 1-2 2-2 3l-2-2h0v1c-1 1-1 2-2 3h-1l-1-1h0c-1-2-4-2-6-3v-2l1-3h0c1-1 1-2 1-3v-1-1z" class="x"></path><path d="M374 403l2-5 2 2v3c-1 1-3 1-4 0z" class="AB"></path><path d="M373 408c1 0 2 0 3 1h0c1 0 1 1 2 1s2 1 3 0c0-1 1-3 1-4l1 1-2 4v1c-1 0-1 0-2 1s-1 2-1 3h0c-1-2-4-2-6-3v-2l1-3z" class="I"></path><path d="M253 337c2 0 5 1 7 2 1 0 2 1 4 1 1 1 3 2 4 2s2 1 3 1l1 1h1s1 1 2 1l1 1c1 0 2 0 3 1l2 1h1c1 1 2 1 2 1l1 1 2 1c1 0 2 1 3 1 1 1 1 1 2 1l1 1c1 0 1 0 2 1h1c1 1 2 1 3 2h1s0 1 1 1h1c3 2 6 3 9 5h0c2 1 2 2 3 2 1 1 1 0 1 1v3l-1-1c-10-2-21-5-31-10l-26-11h-1c-2 0-4-2-5-3-2-1-6-1-8-3h1 3c3 1 5 3 8 3l-2-6v-1z" class="Y"></path><path d="M244 341h3c3 1 5 3 8 3v1c1 1 3 2 4 2l6 2c10 5 20 10 31 14 4 1 9 2 13 3 2 1 4 1 5 1v1c-10-2-21-5-31-10l-26-11h-1c-2 0-4-2-5-3-2-1-6-1-8-3h1z" class="i"></path><path d="M315 325c4 3 9 9 12 14l1 30c3 0 7-1 10 0h-10v36h-13v-36-3-41z" class="D"></path><path d="M347 455c1 1 2 2 2 3 1 1 1 1 2 1v-2l-1 1h0v-1-1c1 0 1-1 1-1l-2-1h1v-2h0c0 1 1 1 1 2 1 7 1 14 1 21v1l1 9v3 1h0v1c2 0 4-1 5 1l-1 1h-1c-1 1-1 1-1 2-1 0-2 0-2-1l-1 1c0 1-1 1-2 1 0 1-1 1-1 1 0 1 0 2 1 2l2 2v1c-1 0-1 0-2-1-1 1-1 1-1 3h-1l-1 1h-2-2l-1-1-4-3v7c0 1 0 2 1 3l-1 2c-1 1-8 1-11 1-1 0-2 0-2-1v-19h1v-1l2 1c1 0 1 0 2-1v2s0 1 1 1h0v-1l1-1v-2h-2c-1 0 0-1-1-2-1-2-1-2 0-4 0-1 0-2 1-3l2-2c-1 0-2-1-2-2h-1l2-1c2-2 2-3 3-6-1-2-2-3-4-5 1 0 1-1 2-2l2 3c0-1 1-2 1-3h1 0v-2c1 0 2-1 4-1h0c0-1 0-1 1-2h0v2h0c1 1 1 2 1 3v1c2 0 2 0 3-1v-1c-1-1-2-1-3-2l1-1c1-1 2-1 3-3 0-1 0-1 1-2z" class="w"></path><path d="M333 490h1v2l1 1-1 1-1-1v-3h0 1-1z" class="v"></path><path fill="#fff" d="M332 499l1 1c0 1-1 2-1 2v1h1 0c-1 1-1 1-2 0v-1c-1-1-1-1 0-2h0l1-1z"></path><path d="M332 481v3-1l-2 2v1c0 1 0 1-1 2v1c-1-2-1-2 0-4 0-1 0-2 1-3l2-1z" class="Y"></path><g class="Z"><path d="M332 481c1 0 1-1 2-1l1 1v2s1 1 0 2h-1l-1 1h-1v-2-3z"></path><path d="M334 471v6l1 1c-1 1-1 2-2 2s-2-1-2-2h-2l2-1c2-2 2-3 3-6z"></path></g><path d="M347 489h2 1 0c2-1 2-1 3-1v1h0l-1 1h-3c-1 1-3 1-4 3l1 1v1c-1 0-1-1-2-1 0 0-1 1-2 1h0l-1 1 6-7z" class="x"></path><path d="M326 492l2 1c1 0 1 0 2-1v2s0 1 1 1l1 3v1l-1 1h-2-1v-2-3h-1c0-1-1-2-1-2v-1z" class="Y"></path><path fill="#fff" d="M328 495l1-1c1 2 1 3 1 4v1c-1 0-2-1-2-1v-3z"></path><path d="M336 462c1 0 2-1 4-1h0c0-1 0-1 1-2h0v2h0c0 2 0 11 1 12 0 2 0 3-1 4-1 0-2 1-2 1v1l5 5c1 1 3 2 4 4l-1 1-6 7-2 2h-1v-3c0-5 1-10 0-15 0-1-1-1-1-2-1-2 0-5 0-6l-1-2h0l-1-1c-1 0-1-1-1-2s1-2 1-3h1 0v-2z" class="r"></path><path d="M334 467c0-1 1-2 1-3h1 0l1 8-1-2h0l-1-1c-1 0-1-1-1-2z" class="V"></path><path d="M338 495c1-4 0-8 0-11 0-2 0-4 1-5l5 5c1 1 3 2 4 4l-1 1-6 7-2 2h-1v-3z" class="D"></path><path d="M347 455c1 1 2 2 2 3 1 1 1 1 2 1v-2l-1 1h0v-1-1c1 0 1-1 1-1l-2-1h1v-2h0c0 1 1 1 1 2 1 7 1 14 1 21v1l1 9v3c-1 0-1 0-3 1h0-1-2l1-1c-1-2-3-3-4-4l-5-5v-1s1-1 2-1c1-1 1-2 1-4-1-1-1-10-1-12 1 1 1 2 1 3v1c2 0 2 0 3-1v-1c-1-1-2-1-3-2l1-1c1-1 2-1 3-3 0-1 0-1 1-2z" class="w"></path><path d="M342 473v1 3h0c1 1 1 2 1 3l1 1c1 1 2 1 4 1 1 1 2 1 3 2h1v-3-5l1 9v3c-1 0-1 0-3 1h0-1-2l1-1c-1-2-3-3-4-4l-5-5v-1s1-1 2-1c1-1 1-2 1-4z" class="x"></path><path d="M344 484h4c1 0 2 0 3 1h1 1v3c-1 0-1 0-3 1h0-1-2l1-1c-1-2-3-3-4-4zm3-29c1 1 2 2 2 3 1 1 1 1 2 1v-2l-1 1h0v-1-1c1 0 1-1 1-1l-2-1h1v-2h0c0 1 1 1 1 2 1 7 1 14 1 21h-4-1l-1-2-1-1 1-1v-3c-1-1-2-1-3-2h1c0-1 1-1 2-1 0-1 0-2-1-3s-1 0-2-2c1-1 2-1 3-3 0-1 0-1 1-2z" class="Z"></path><path d="M346 473h2c0 1 1 1 0 2h0-1l-1-2z" class="AG"></path><path d="M346 457c0-1 0-1 1-2l1 4h-2v-2z" class="F"></path><path d="M347 455c1 1 2 2 2 3 1 1 1 1 2 1v-2l-1 1h0v-1-1c1 0 1-1 1-1l-2-1h1v-2h0c0 1 1 1 1 2 1 7 1 14 1 21h-4 0c1-1 1-2 1-3l1-1v-1-1-3-1l-1-1-1-1c0-1 0-1 1-1 0 0 1 0 1-1h1c-1-1-2-2-3-2l-1-4z" class="v"></path><path d="M363 433l1 1v1l-1 1h2c0 1 1 1 1 1v3 1c2 1 3 0 5 1 1-3 3-5 4-7l1 2c1 1 0 3 1 5v1 3c0 1 1 8 0 9v1l-2 2c-1 1-2 1-4 2h1s1 1 2 0h0 2s1 0 1 1v5 8c0 2 0 4-1 5v1l-7 8v1c-2 0-3 0-4 1l1 1-2 1h-1v-2 1h-1l-2 2h-2l-1-1 1-1c-1-2-3-1-5-1v-1h0v-1-3l-1-9v-1c0-7 0-14-1-21 0-1-1-1-1-2 2 0 3 1 5 1 0-1 0-1-1-2 1-1 1-1 2-1 0-1-1-2 0-3l-1-1 1-1c2-3 5-6 6-9 0-1 0-2 1-3h0z" class="G"></path><path d="M376 453h1v2c-3 2-5 4-8 5-2 0-4 0-6 1 3-1 5-2 7-4l2-2h0c2 0 3-1 4-2z" class="j"></path><path d="M356 462c1 0 4-1 5 0v1c0 1 0 3 1 4l1 1h0l2-1h1l-4 6-3 2h-2c-1-1 0-5 0-7-1-1-1-2-2-4v-2h1z" class="d"></path><path d="M356 462c1 0 4-1 5 0v1h0c-1 0-1 1-1 1 0 1-1 2-2 2h-1v-1l2-2h-3c0 1-1 1-1 1v-2h1z" class="a"></path><path d="M366 467l1-1c2 1 5 4 6 5 2 1 2 2 3 3v1l-3 1c-2 1-4 1-5 2h0l-4-2h-5v-1l3-2 4-6z" class="k"></path><path d="M364 476l-1-1h-1-1c1-1 1-1 2-1h2c2 1 4 1 6 0 1 0 1 1 2 2-2 1-4 1-5 2h0l-4-2z" class="a"></path><path d="M366 467l1-1c2 1 5 4 6 5-3 1-8 2-11 2l4-6z" class="W"></path><path d="M373 476l3-1v4 1l-7 8c-1 0-4-3-5-4-1-2-5-6-6-8h1 5l4 2h0c1-1 3-1 5-2z" class="D"></path><defs><linearGradient id="S" x1="350.517" y1="478.01" x2="359.66" y2="473.797" xlink:href="#B"><stop offset="0" stop-color="#918270"></stop><stop offset="1" stop-color="#ae9b81"></stop></linearGradient></defs><path fill="url(#S)" d="M351 454l2 2c2 0 3 1 4 2h0c-1 0-1 0-2 1 1 1 1 2 1 3h-1v2c1 2 1 3 2 4 0 2-1 6 0 7h2v1h-1c1 2 5 6 6 8 1 1 4 4 5 4v1c-2 0-3 0-4 1l1 1-2 1h-1v-2 1h-1l-2 2h-2l-1-1 1-1c-1-2-3-1-5-1v-1h0v-1-3l-1-9v-1c0-7 0-14-1-21z"></path><path d="M353 460h2v-1c1 1 1 2 1 3h-1c0-1-1-1-2-1h0v-1z" class="e"></path><path d="M354 485c1 1 1 2 2 3l1 1 1-1-1 1h-4v-4h1z" class="g"></path><path d="M353 456c2 0 3 1 4 2h0c-1 0-1 0-2 1v1h-2v-4z" class="C"></path><path d="M354 485c2-1 2-1 3-1 1 2 1 2 1 4l-1 1-1-1c-1-1-1-2-2-3z" class="P"></path><path d="M353 489l10 1h0v1h-1l-2 2h-2l-1-1 1-1c-1-2-3-1-5-1v-1z" class="X"></path><path d="M364 484c1 1 4 4 5 4v1c-2 0-3 0-4 1l1 1-2 1h-1v-2h0l-1-2c1 0 1 0 1-1h1v-3z" class="i"></path><path d="M363 433l1 1v1l-1 1h2c0 1 1 1 1 1v3 1c2 1 3 0 5 1 1-3 3-5 4-7l1 2c1 1 0 3 1 5v1 3c0 1 1 8 0 9v-2h-1c-1 1-2 2-4 2h0l-2 2c-3 0-6 1-9 1-1 1-1 1-3 1l-1-1h0c-1-1-2-2-4-2l-2-2c0-1-1-1-1-2 2 0 3 1 5 1 0-1 0-1-1-2 1-1 1-1 2-1 0-1-1-2 0-3l-1-1 1-1c2-3 5-6 6-9 0-1 0-2 1-3h0z" class="k"></path><path d="M366 454c1 0 2 0 3-1h0c1 0 2 0 4-1 1 0 2-1 3-2v3c-1 1-2 2-4 2h-6v-1z" class="I"></path><path d="M356 447l2 2c1 0 2 0 4 1h-2l1 1c1 0 2 0 4 1h2l-2 1c-2 0-3 0-4-1h-1c-1-1-3-1-4-2 0-1-1-2 0-3z" class="P"></path><path d="M350 452c2 0 3 1 5 1 1 1 3 2 5 2 2-2 4-1 6-1v1h6 0l-2 2c-3 0-6 1-9 1-1 1-1 1-3 1l-1-1h0c-1-1-2-2-4-2l-2-2c0-1-1-1-1-2z" class="s"></path><path d="M360 455c2-2 4-1 6-1v1h6 0c-4 0-8 1-12 0z" class="K"></path><path d="M371 442c1-3 3-5 4-7l1 2c1 1 0 3 1 5v1 3 3h0v-2h-1c-3 3-5 5-9 5h0-2c-2-1-3-1-4-1l-1-1h2 2l-1-1v-1s1 0 1-1h1 1c2-1 2-1 3-2l2-3z" class="R"></path><path d="M369 445l2-1v1 1l4-1c-4 3-7 4-11 5l-1-1v-1s1 0 1-1h1 1c2-1 2-1 3-2z" class="L"></path><path d="M371 442c1-3 3-5 4-7l1 2c1 1 0 3 1 5-1 1-2 2-2 3l-4 1v-1-1l-2 1 2-3z" class="U"></path><path d="M371 442c1-3 3-5 4-7l1 2s-1 1-1 2c-1 2-2 4-4 5l-2 1 2-3z" class="M"></path><path d="M363 433l1 1v1l-1 1h2c0 1 1 1 1 1v3 1c2 1 3 0 5 1l-2 3c-1 1-1 1-3 2h-1-1c0 1-1 1-1 1v1l1 1h-2c-2-1-3-1-4-1l-2-2-1-1 1-1c2-3 5-6 6-9 0-1 0-2 1-3h0z" class="C"></path><path d="M355 446l1-1c1 0 1 0 2 1 0 0 1 1 2 1h2c-2 0-2 0-4 2l-2-2-1-1z" class="M"></path><path d="M364 447h1-1c0 1-1 1-1 1v1l1 1h-2c-2-1-3-1-4-1 2-2 2-2 4-2h2z" class="G"></path><path d="M364 446h-1c-1 0-2-1-2-1 0-2 1-3 2-4l-1-1c1-1 1-1 2-1l1 1h0 1v1c2 1 3 0 5 1l-2 3c-1 1-1 1-3 2h-1-1v-1z" class="d"></path><path d="M366 441c2 1 3 0 5 1l-2 3c-1 1-1 1-3 2h-1-1v-1h1c0-2 0-3 1-5z" class="W"></path><path d="M307 465l1-1c3-1 7 0 11 0-2 4-4 7-3 12 1 1 2 3 3 5l5-1c1 1 1 1 1 2v11 19c0 1 1 1 2 1h-23l-1 1h0 0-16c-3 0-6 1-9 1h0l-1 1c-3 0-8 1-10-1-1-1-3-1-4-1l-3 1h-1v-2 1l-1 1h-1v-1s0 1-1 1h-2c-1-2-1-6-1-8l-3-15v-3c0-2-1-4 0-6h0c3 2 7 4 10 4 4 1 9 1 12 1 3-1 6-2 8-4 0-1 5-1 6-1s2-1 3-2c0 0 0-1-1-1-1-2-4-2-5-3h-3v-2-3c0-1 1-2 1-3 1-2 3-3 5-4 0 1 0 2 2 3l1-1c0 1 0 1 1 1l1 1 1-1c2-1 3-1 4-1l1 1c1 1 2 2 2 3v1 1h0c1 0 1-1 2-1h3c0-1 0-1 1-2 0 1 1 1 1 1l1-1v-5z" class="H"></path><path d="M251 487l1 4c0 1-1 1-2 1v-3c0-1 1-1 1-2z" class="D"></path><path d="M263 495c1 0 2 0 3 1h1 0c0 1-1 1-2 1-1-1-2-1-2-2z" class="g"></path><path d="M250 483h0c1 2 1 3 1 4s-1 1-1 2c0-2-1-4 0-6z" class="W"></path><path d="M260 500c1 0 3 0 4 1 0 1 0 2-1 3l-3 1v-1c-1-2-1-2 0-4z" class="o"></path><path d="M263 495c-1-1-1-1-1-2s1-2 2-2c0-1 1-1 2 0 1 0 2 1 2 2s1 2 0 3h-1 0-1c-1-1-2-1-3-1z" class="F"></path><path d="M260 515h-1v-1c0-1 1-3 1-4 1-1 2-2 3-2h3c1 2 1 4 1 6h-4l-3 1z" class="C"></path><path d="M252 491l4 24h-2c-1-2-1-6-1-8l-3-15c1 0 2 0 2-1z" class="n"></path><path d="M303 500h0v1 1c-1 3 0 7 0 11l-30 1v-9-1c4-2 10-2 15-2 1-1 4-1 5-1l10-1z" class="D"></path><path d="M273 504c4-2 10-2 15-2-2 1-2 1-4 1h0 2 1 0c1 0 2 0 2 1l-16 1v-1z" class="M"></path><path d="M303 500h0v1c-3 1-6 2-9 2l-5 1c0-1-1-1-2-1h0-1-2 0c2 0 2 0 4-1 1-1 4-1 5-1l10-1z" class="v"></path><path d="M288 502c1-1 4-1 5-1l1 1c1 0 3-1 5-1-1 1-2 1-3 1h-1s-1 0-1 1h0l-5 1c0-1-1-1-2-1h0-1-2 0c2 0 2 0 4-1z" class="Q"></path><path d="M272 488l31-1v8 5l-10 1c-1 0-4 0-5 1-5 0-11 0-15 2l-1-11v-5z" class="D"></path><path d="M280 475v-3c0-1 1-2 1-3 1-2 3-3 5-4 0 1 0 2 2 3l1-1c0 1 0 1 1 1l1 1 1-1c2-1 3-1 4-1l1 1c1 1 2 2 2 3v1 1h0c1 0 1-1 2-1h3c0-1 0-1 1-2 0 1 1 1 1 1l1-1c0 5 0 7 3 11h-6v8 11h-1 0v-5-8l-31 1h0c3-1 6-2 8-4 0-1 5-1 6-1s2-1 3-2c0 0 0-1-1-1-1-2-4-2-5-3h-3v-2z" class="Y"></path><path d="M286 476c0-1 1-2 1-2l2 1c1 1 1 2 2 3 1 0 1 0 1 1h-1c-1 1 0 1-2 1v1s0-1-1-1c-1-2-4-2-5-3h1v-1h2z" class="h"></path><path d="M286 476c0-1 1-2 1-2l2 1c1 1 1 2 2 3h-1c-2 0-3-1-4-2z" class="D"></path><path d="M303 475c1 2 0 4 2 5h0c0 1-1 1-1 1v8h-1v-3c-3-1-3-5-5-6h-4c-1 0-1 0-1-1h2c1 0 3-1 4 0h0l3-3c0-1 0 0 1-1z" class="M"></path><path d="M305 470c0 1 1 1 1 1l1-1c0 5 0 7 3 11h-6s1 0 1-1h0c-2-1-1-3-2-5-1 1-1 0-1 1l-3 3h0l2-3-3-1 1-2c1 0 1-1 2-1h3c0-1 0-1 1-2z" class="N"></path><path d="M305 470c0 1 1 1 1 1v2h-1c0-1 0-1-1-1h0c0-1 0-1 1-2z" class="I"></path><path d="M303 475h1c2 1 3 3 3 5h-2c-2-1-1-3-2-5z" class="D"></path><path d="M299 473c1 0 1-1 2-1h3 0v2c0 1-1 0-2 0-1 1-1 2-1 2l-3-1 1-2z" class="E"></path><path d="M291 469l1-1c2-1 3-1 4-1l1 1c1 1 2 2 2 3v1 1h0l-1 2s-1 1-2 1c-2 1-4 0-5-1v-1c-1-2 0-3 0-5z" class="S"></path><path d="M280 475v-3c0-1 1-2 1-3 1-2 3-3 5-4 0 1 0 2 2 3l1-1c0 1 0 1 1 1l1 1c0 2-1 3 0 5v1h-2l-2-1s-1 1-1 2h-2v1h-1-3v-2z" class="a"></path><path d="M284 466c1 1 1 1 2 1v1c0 1-1 1-1 2h0c-1 0-1-1-1-1v-3z" class="e"></path><path d="M284 473h1v1l-2 1 1 1v1h-1-3v-2h1l1-1c1-1 1-1 2-1z" class="L"></path><path d="M288 468l1-1c0 1 0 1 1 1l1 1c0 2-1 3 0 5v1h-2l-2-1s-1 1-1 2h-2l-1-1 2-1v-1h-1v-1-1l1 1h1c0-1-1-1-1-2 1 0 2-1 3-2z" class="Q"></path><path d="M307 465l1-1c3-1 7 0 11 0-2 4-4 7-3 12 1 1 2 3 3 5l5-1c1 1 1 1 1 2v11 19h-5-12-4v-12-11-8h6c-3-4-3-6-3-11v-5z" class="D"></path><path fill="#c31f21" d="M256 91l3-9c13-3 27-5 41-7 11-1 23-2 35-2 9 0 17 0 25 2 8 0 15 2 23 3 38 8 74 25 103 51 1 0 1 1 1 1l1 1 3 3 1 1 4 4 2 2c2 2 3 4 5 6l1 1c1 1 2 2 2 3l7 8c1 1 2 2 2 3h0v1h-1-1v2l-1 3-1 1c0 1-1 2-1 2h2v1h-2c-2 0-3 0-4-1h-1l2 9v2c-1 0-1 1-1 2-1 1-1 1-1 2h0c-2 4 0 9-1 13v14c0 1-1 4 0 6v4l-2-1v3 8 8 4c0 1-1 1-1 2-1 1-2 1-2 1v4h-1-3-3l-1 3h1 0v1h0c0 1-1 1-1 2l-1 2c-1 0-1 1-2 2 2 0 2 0 3 1l-1 1c-1 2-1 3-2 4l1 2c0 2 1 4 1 5-1 1-2 2-4 2h-4-1l-1-1c0 1 0 1-1 2 0-1-1-1-2-1 0 0-1 1-1 0h-9l-11 1h-5-3-2l-2 1h-2v1 3 1 3 1c0 1 0 1-1 2 1 2 1 1 0 3v3h1v1c-1 2-2 3-3 5h1 1c0 1-1 2-2 4h0c-1 1-2 1-2 3h0c1 0 1 0 1 1h0c-1 1-1 1-1 2 0 2-1 2-2 3h-4l-1 1 1 2s-1 1-1 2h0 0c1 0 2 1 2 1v1c0 1-1 2-1 2 0 1 0 1 1 2 1 0 1 0 2 1h1v2l-1 2h0c0 1 0 3-1 4v3 1h0v1 5l1 1 1-2v4 11l1 1c1 0 3 0 5 1 6 0 12 0 17 1h0c0 2-1 3-2 4h2c1 1 1 1 1 2h1v4 1c0 1 0 2-1 3 1 1 0 2 0 3h0v1 3h1l-1 2v-1h0c-3 1-4 4-7 5v3h1l-4 1c-2-1-3-1-5 0h-4-2-1l-2 1h-1 0c-1 1-2 1-3 2h-3-3v3 2h0v1c0 1 0 2-1 3-1-1-2-3-3-3h-1c0-1-1-2-2-3h0l-1-1h-1c-4-3-6-6-9-10l-11-13-5-6v-1l-77-92v13c1 1 1 14 1 17h0c6-1 12 1 17 1 2 0 5 0 7 1v1c-3 0-5 0-8-1-5 0-11 0-16 1v4c-1 4-1 9 0 12v41c0-1 0 0-1-1-1 0-1-1-3-2h0c-3-2-6-3-9-5h-1c-1 0-1-1-1-1h-1c-1-1-2-1-3-2h-1c-1-1-1-1-2-1l-1-1c-1 0-1 0-2-1-1 0-2-1-3-1l-2-1-1-1s-1 0-2-1h-1l-2-1c-1-1-2-1-3-1l-1-1c-1 0-2-1-2-1h-1l-1-1c-1 0-2-1-3-1s-3-1-4-2c-2 0-3-1-4-1-2-1-5-2-7-2-1 0-1-1-1-2l-3-8v-2c-1-1-1-2-2-3v-2l-3-8-2-5-5-16v-3c-1-2-1-4-2-7v-1h-6c-2-1-4-1-5-4 1 0 1 1 2 1l9 3v-9-11l6-18c1 0 2-2 2-3 1-1 2-3 3-5 3-3 6-7 9-10-1-1-1-1-2-1l1-9v-3-1l-1-14 1-10v-2-1-2l1-3v-2-1-2-3-4c0-1 0-2 1-3l-1-1c0-1 0-3 1-4h0c-1-2-1-3-2-5h1v-9c0-6-1-12 0-18v-8c-1-1-1-3-1-5v-5h0c-1 0-1 0-1 1-1 0-1 0-1 1l-1-1v2-2l-2-2v-1l1-1c-1-1-1-1-1-2l1-2c1-1 2-3 2-4v-1l-2-1h1 0c2-1 2-1 3-2v1l-1 1v4c1-1 1-2 1-3s1-3 2-4v-1z"></path><path d="M373 148l3 2-1 1h-2v-1c-1-1-1-1 0-2z" class="H"></path><path d="M491 255h1 0v1h0c0 1-1 1-1 2l-1 2c-1 0-1 1-2 2v-1h-1-16l-1 1v-1-1h11c2 0 5 0 6-1 1 0 1-1 1-1l3-3z" class="V"></path><path d="M327 133c2 0 6 0 8 1h1l1 1v3l-1 1-1-1c-1 1-2 1-3 1v-1c-1-1-2-3-4-4 0-1 0-1-1-1z" class="U"></path><path d="M335 134h1l1 1v3l-1 1-1-1v-1c-1-1 0-2 0-3z" class="O"></path><path d="M487 150l3 3 2 4v1l1 2v1h0c-1 0-1 1-1 1-1 2-4 5-5 6-1-1-1-2 0-3h0c1-1 2-2 2-3-1-1-1-3-2-4 0-2-1-6 0-8h0z" class="G"></path><path d="M492 158l1 2v1h0c-1 0-1 1-1 1-1-1-1-2-2-3h1 1v-1z" class="W"></path><path d="M487 150l3 3 2 4v1 1h-1-1l-3-9h0z" class="D"></path><path d="M366 144l7 4c-1 1-1 1 0 2 0 2-1 2-2 3h-1 0-1-1l-2-3v-1h-1v1c-1 0-2-1-2-1h-1s0-1 1-2v-1h0c1-1 2-2 3-2h0z" class="L"></path><path d="M363 146c1-1 2-2 3-2l1 3h-1-2l-1-1z" class="V"></path><path d="M363 146l1 1h2l-1 2v1c-1 0-2-1-2-1h-1s0-1 1-2v-1h0z" class="Q"></path><path d="M366 149l1-1h1l1 1h2v2 1l-1 1h0-1-1l-2-3v-1z" class="Y"></path><path d="M336 134c3 0 7 1 10 2s8 2 11 4h-3l-1 1v1h-3l1 1c-1 0-1 1-2 1h0l-1-1v1l-1-1v1h-2 0c-1-1-2-1-3-1 0-1 0-2-1-2h-1v-1c-1 1-2 1-3 1 0-2 0-2-1-2l1-1v-3l-1-1z" class="C"></path><path d="M337 135c2 0 5 3 8 4h0-1s-2 0-2-1h-1v2 1h-1v-1c-1 1-2 1-3 1 0-2 0-2-1-2l1-1v-3z" class="L"></path><path d="M340 140c-1-1-1-1-1-2h2v2 1h-1v-1z" class="d"></path><path d="M341 138h1c0 1 2 1 2 1h1c2 1 4 2 6 2h2v1h-3l1 1c-1 0-1 1-2 1h0l-1-1v1l-1-1v1h-2 0c-1-1-2-1-3-1 0-1 0-2-1-2v-1-2z" class="E"></path><path d="M350 142l1 1c-1 0-1 1-2 1v-2h1z" class="h"></path><path d="M341 140h1 2c0 1 1 2 1 4-1-1-2-1-3-1 0-1 0-2-1-2v-1z" class="L"></path><path d="M321 133h6c1 0 1 0 1 1 2 1 3 3 4 4v1l-1 1c0 1 0 2 1 3-1 0-2 1-2 2-1-1-1-2-2-2h0v-2h-2c-1 0-1-1-1-1h-2s-1 0-1-1h-1c-2 0-2 1-3 2-1-1-2-1-3-2h0 0l1-1c0-2 0-2-1-4v-1h6z" class="b"></path><path d="M328 134c2 1 3 3 4 4v1l-1 1c-1 0-2-1-3-2h0c-1-1 0-2 0-4z" class="P"></path><path d="M315 133h6l1 2c1 1 3 2 4 3h0l-1 2h-2s-1 0-1-1h-1c-2 0-2 1-3 2-1-1-2-1-3-2h0 0l1-1c0-2 0-2-1-4v-1z" class="p"></path><path d="M315 133h6l1 2c0 1 0 2-1 3-1 0-2 0-3-1-1 0-1-1-2-2v-1h-1v-1z" class="w"></path><path d="M303 134h6s1 0 1 1v-1c1-1 3-1 5-1v1c1 2 1 2 1 4l-1 1h0 0c1 1 2 1 3 2 1-1 1-2 3-2h1l-2 2v2s-2 0-2 1v1c-1 1-2 1-3 2l1 1-1 1h-1-1l-2-2h1c0-1-1-1-1-1 0-1-1-1-1-2h-4s1 0 1 1c-1 1-1 2-3 3v1l-1-1v-4c0-2-1-4 0-5v-5z" class="v"></path><path d="M311 141c-1 0-2 0-3-1 1-1 1-2 2-2v-2l2 2c1 1 1 1 1 3-1 1-1 0-2 0zm-8-2h0l1-1h1c1 1 1 2 2 3 0 1 0 1-1 2h-1l-2-2v3c0-2-1-4 0-5z" class="B"></path><path d="M303 144v-3l2 2h1v1s1 0 1 1c-1 1-1 2-3 3v1l-1-1v-4z" class="w"></path><path d="M312 138c1 0 2 0 3 1h0 0c1 1 2 1 3 2 1-1 1-2 3-2h1l-2 2v2s-2 0-2 1v1c-1 1-2 1-3 2l1 1-1 1h-1-1l-2-2h1c1-2 1-2 0-3l-1-1c1 0 1-1 0-2 1 0 1 1 2 0 0-2 0-2-1-3z" class="V"></path><path d="M312 144c1 0 3 2 3 3l1 1-1 1h-1-1l-2-2h1c1-2 1-2 0-3z" class="B"></path><path d="M443 260h27v1 1l-19 4v3h-1-2 0v1l1 1c0 1-1 1-2 2l1 1-1 1 2 2c1 0 1-1 1 0l1 1h-3-2l-2 1c-1-1-2-2-2-3h0c0-1 1-1 1-1v-1-1-1-1s1-2 1-3l-1-1c-1-1-1-2-1-3 1-1 1-2 1-3v-1z" class="Y"></path><path d="M470 261v1l-19 4v3h-1-2 0v1l1 1c0 1-1 1-2 2l1 1-1 1 2 2c1 0 1-1 1 0l1 1h-3-2l-2 1c-1-1-2-2-2-3h0c0-1 1-1 1-1v-1-1-1-1s1-2 1-3l-1-1c3 0 7-1 10-2 5-1 11-2 17-4z" class="E"></path><path d="M450 269c0-1-1-1-2-1 1-1 2-1 3-2v3h-1z" class="Y"></path><path d="M448 270l1 1c0 1-1 1-2 2l-1-1v-1l2-1z" class="U"></path><path d="M443 273v-1h2v1h0v2l1 1-1 1c0-1 0-1-1-2l-1-1v-1z" class="b"></path><path d="M443 274l1 1c1 1 1 1 1 2 1 0 1 1 1 1l-2 1c-1-1-2-2-2-3h0c0-1 1-1 1-1v-1z" class="H"></path><path d="M335 138l1 1c1 0 1 0 1 2 1 0 2 0 3-1v1 1c-1 1-1 3-1 4l-1 4h0v2 1l-1-1c0 2-1 2-2 3l-2-2-2 1-2 2c0-1-1-1-2-1h-1l-3-3c-1 0-2 0-2 1-1-2-1-3-1-4l-2-1-1-1-1 1-1-1c1-1 2-1 3-2v-1c0-1 2-1 2-1v-2l2-2c0 1 1 1 1 1h2s0 1 1 1h2v2h0c1 0 1 1 2 2 0-1 1-2 2-2-1-1-1-2-1-3l1-1c1 0 2 0 3-1z" class="U"></path><path d="M332 147v1s1 1 0 2h0-1v-2l1-1z" class="w"></path><path d="M321 146c1-1 2-1 2-2l2 3c-1 0-2 0-3 1l-1-2z" class="k"></path><path d="M322 139c0 1 1 1 1 1l1 2h0c-2 0-3 0-4 1v-2l2-2z" class="w"></path><path d="M327 155v-2c0-1 0-1 1-2h0c2 1 3 2 3 3l-2 2c0-1-1-1-2-1z" class="r"></path><path d="M334 150l3 2c0 2-1 2-2 3l-2-2v-1c0-1 0-1 1-2z" class="F"></path><path d="M323 144l2-1c1 0 2 1 2 2l1 1v1s0 1-1 1-2-1-2-1l-2-3zm7 1c0-1 1-2 2-2h0l3 3-2 2h-1v-1c-1-1-2 0-3-1l1-1zm-12-1c1 0 2 1 3 2l1 2s-1 0-2 1l-2-1-1-1-1 1-1-1c1-1 2-1 3-2v-1z" class="P"></path><path d="M335 146s1-1 2-1v1h2l-1 4h0v2 1l-1-1-3-2c-1-1-1 0-2 0h0c1-1 0-2 0-2h1l2-2z" class="V"></path><g class="x"><path d="M337 146h2l-1 4c-1-1-1-1-2-1 0-2 0-2 1-3z"></path><path d="M335 138l1 1c1 0 1 0 1 2 1 0 2 0 3-1v1 1c-1 1-1 3-1 4h-2v-1c-1 0-2 1-2 1l-3-3h0c-1-1-1-2-1-3l1-1c1 0 2 0 3-1z"></path></g><path d="M332 143h1l1-3c0 1 0 1 1 2v1l1 1h1v1c-1 0-2 1-2 1l-3-3z" class="U"></path><path d="M340 140v1 1c-1 1-1 3-1 4h-2v-1-1h-1c0-2 0-2 1-3 1 0 2 0 3-1z" class="Q"></path><path d="M487 130l1 1 3 3 1 1 4 4 2 2c2 2 3 4 5 6l-3 2c-1 3-3 8-5 11h-1-1l-1-2v-1l-2-4-3-3v-1c-1-1-1-16 0-19z" class="E"></path><path d="M488 131l3 3c-1 3-1 7-1 10v4h1c-1 0-1 0-1 1l-2-1v-8-9z" class="D"></path><path d="M491 134l1 1 4 4v5l-1 4h0 0-2l-1 1h-2c0-1 0-1 1-1h-1v-4c0-3 0-7 1-10z" class="H"></path><path d="M492 135l4 4v5l-1 4h0 0-2v-5c0-2 0-5-1-8z" class="D"></path><path d="M496 139l2 2c2 2 3 4 5 6l-3 2c-1 3-3 8-5 11h-1-1l-1-2v-1l-2-4 1-2-1-1v-1c1 1 3 1 5 1l1-1h-4l1-1h2 0 0l1-4v-5z" class="j"></path><path d="M495 148l1-4 2 2v2h-3z" class="H"></path><path d="M494 155c1 1 1 2 1 4l-1 1h-1l-1-2v-1c0-1 1-1 2-2z" class="L"></path><path d="M496 139l2 2v5l-2-2v-5z" class="X"></path><path d="M491 151l3 4c-1 1-2 1-2 2l-2-4 1-2z" class="M"></path><path d="M373 150v1h2l1-1c7 6 12 13 14 22-2 1-7 1-9 1s-5 0-6-1l-1-2-1-2v-1c1 0 1-1 1-1 0-1 0-1-1-1 0-1-1-1-1-2s1 0 2-1c0 0-1-1-2-1-1-2-2-2-3-4l1-1c0-1 0-1-1-1h0-1 0v-2h1 1 0 1c1-1 2-1 2-3z" class="x"></path><path d="M373 150v1h2c-1 2-1 4-2 5h0 0l1 1v2l-1 1-1 1c-1-2-2-2-3-4l1-1c0-1 0-1-1-1h0-1 0v-2h1 1 0 1c1-1 2-1 2-3z" class="b"></path><path d="M370 156c1 0 1-1 2 0h1c0 1 0 1-1 2v1l1 1-1 1c-1-2-2-2-3-4l1-1z" class="Y"></path><path d="M490 189c1 0 2-1 3 0h0c-1 1-1 3-1 5v9 36c0 4 1 8 0 12h-1c-1 1-2 1-3 2v1-64c1-1 1-1 2-1z" class="w"></path><path d="M357 140c3 0 5 1 7 3l2 1h0c-1 0-2 1-3 2h0v1c-1 1-1 2-1 2-1 1-1 1-2 1v2h-2c0-1 0-1-1-2l-2 2h-1v2h-2c-1 2-2 2-3 3v1l-1 1c0-1-1-1-1-1-1 1-2 1-3 1s-2 1-3 1h0l-2-2-2 1-1-1c0-1-1-2-1-3 1-1 2-1 2-3l1 1v-1-2h0l1-4c0-1 0-3 1-4v-1h1c1 0 1 1 1 2 1 0 2 0 3 1h0 2v-1l1 1v-1l1 1h0c1 0 1-1 2-1l-1-1h3v-1l1-1h3z" class="M"></path><path d="M352 149c1-1 1-1 1-2l1 1v1 1l-2-1z" class="C"></path><path d="M357 149c0-1 0-2 1-2s1 0 2 1c0 1 0 1-2 2l-1-1z" class="H"></path><path d="M345 144h2l1 2c0 1-1 2-2 3l-2-1 2-2c0-1 0-1-1-2z" class="e"></path><path d="M349 153h-1l-1-1c2-1 3-2 4-3h1 0l2 1c-1 0-2 1-2 2h0-4l1 1z" class="L"></path><path d="M363 146h0v1c-1 1-1 2-1 2-1 1-1 1-2 1v2h-2c0-1 0-1-1-2l-2 2-1-1c1-1 2-2 3-2l1 1c2-1 2-1 2-2l3-2z" class="G"></path><path d="M350 142h3c1 0 1 1 2 1-1 2-3 3-4 4l-3-1-1-2v-1l1 1v-1l1 1h0c1 0 1-1 2-1l-1-1z" class="H"></path><path d="M340 141h1c1 0 1 1 1 2 1 0 2 0 3 1h0c1 1 1 1 1 2l-2 2c-1 1-2 1-4 2l-1-1-1 1h0l1-4c0-1 0-3 1-4v-1z" class="a"></path><path d="M342 144h1c0 1 0 2-1 3h0-1v-2l1-1z" class="AC"></path><path d="M340 141h1c1 0 1 1 1 2h0v1l-1 1h0c0 2-1 3-2 4l-1 1h0l1-4c0-1 0-3 1-4v-1z" class="L"></path><path d="M340 141h1c1 0 1 1 1 2h0v1l-1 1h0v-1c0-1-1-1-1-2h0v-1z" class="C"></path><path d="M357 140c3 0 5 1 7 3l2 1h0c-1 0-2 1-3 2h0 0v-2h0c-1 0-2 1-2 1l-1-1v1c0 1 0 1-1 1h0c-1-1-3-1-4-3-1 0-1-1-2-1v-1l1-1h3z" class="AE"></path><path d="M360 145l-2-1c-1 0-1-1-2-2 2 0 4 1 5 2h0c1-1 2-1 3-1l2 1h0c-1 0-2 1-3 2h0 0v-2h0c-1 0-2 1-2 1l-1-1v1z" class="I"></path><path d="M338 150l1-1 1 1 1 2h0c1 1 1 1 1 2h2-1-1c0 1 1 1 1 2h1 1 1s0-1-1-1c1 0 1-1 2-1s1 0 1 1c2 0 2 0 3-1v-1h-2 0l-1-1h4 2v2h-2c-1 2-2 2-3 3v1l-1 1c0-1-1-1-1-1-1 1-2 1-3 1s-2 1-3 1h0l-2-2-2 1-1-1c0-1-1-2-1-3 1-1 2-1 2-3l1 1v-1-2z" class="e"></path><path d="M347 154c1 0 1 0 1 1s1 2 0 3h-1c0-2-1-2 0-4zm-9-4l1-1 1 1 1 2-2 2-1-1v-1-2z" class="C"></path><path d="M337 152l1 1c0 1 0 1 1 2 0 1-1 1 0 2h0v1h0l-2 1-1-1c0-1-1-2-1-3 1-1 2-1 2-3z" class="E"></path><path d="M339 158l2-1v-2h1c0 1 0 1 2 2 0 0 0-1 1 0 1 0 1 1 2 1-1 1-2 1-3 1s-2 1-3 1h0l-2-2h0z" class="N"></path><path d="M424 260c6-1 13 0 19 0v1c0 1 0 2-1 3 0 1 0 2 1 3l1 1c0 1-1 3-1 3v1 1 1 1s-1 0-1 1l-1-1v1l-1 2c-1 0-1-1-1-2l-2 2c-1 0-1 1-2 1l1 2h0c-1 0-1 0-2 1-1-1-2-2-3-2s-2-1-3-1h0 0v-2-2c-2-1-3 0-5 0h-8c-1 0-1 0-1 1v-1 1l-2 1c-1-1-2 0-3-2h0c0-1 1-3 2-4v-1h0v-1c-1 1-2 1-2 1v1h-1l-1-4v-6-1h4c3 0 5 1 8 0h5z" class="l"></path><path d="M429 274h0c1-1 2-1 3-1s1-1 2-2l1 1c0 2-1 3 0 5h0l-1 1 1 1 1 2h0c-1 0-1 0-2 1-1-1-2-2-3-2s-2-1-3-1h0v-6h0c1 0 1 0 1 1z" class="j"></path><path d="M428 273c1 0 1 0 1 1s-1 2 0 3 2 1 2 1c2 1 3 2 4 3v-1h0c0-1-1-1-1-2l1 1 1 2h0c-1 0-1 0-2 1-1-1-2-2-3-2s-2-1-3-1h0v-6h0z" class="s"></path><path d="M437 271c2-2 3-5 5-7 0 1 0 2 1 3l1 1c0 1-1 3-1 3v1 1 1 1s-1 0-1 1l-1-1v1l-1 2c-1 0-1-1-1-2l-2 2c-1 0-1 1-2 1l-1-1 1-1h0c-1-2 0-3 0-5l2-1z" class="c"></path><path d="M437 271c1 0 1 1 2 0h0c0-1 2-3 2-4h1c-1 3-2 4-3 6-1 1-3 2-3 4 1-1 2-1 2-2l2-2c1-1 2-3 3-4 0 2-1 3-2 4l-2 3-2 2c-1 0-1 1-2 1l-1-1 1-1h0c-1-2 0-3 0-5l2-1z" class="f"></path><path d="M424 260c6-1 13 0 19 0v1c-1 0-1 0-1 1-2 2-4 5-6 6 1-2 3-5 5-7h-1c-3 2-4 5-7 7-2 1-4 0-5 1l-2 1h-4l2-1h0l-5-1h9c0-2 0-3 1-5-1 0-1-1-1-1l1-1h-1-2s-1 0-2-1z" class="Q"></path><path d="M429 263l2-2h1c-1 2-2 3-3 4l1 1c1-2 2-4 4-5h2c1 2-3 5-4 7h1c-2 1-4 0-5 1l-2 1h-4l2-1h0l-5-1h9c0-2 0-3 1-5z" class="AA"></path><path d="M407 260h4c3 0 5 1 8 0h5c1 1 2 1 2 1h2 1l-1 1s0 1 1 1c-1 2-1 3-1 5h-9l5 1h0l-2 1h4l2-1v4h0v6h0v-2-2c-2-1-3 0-5 0h-8c-1 0-1 0-1 1v-1 1l-2 1c-1-1-2 0-3-2h0c0-1 1-3 2-4v-1h0v-1c-1 1-2 1-2 1v1h-1l-1-4v-6-1z" class="i"></path><path d="M415 265c0 1-1 1 0 3-1 1 0 1-1 2 0 1-1 2-2 2l-1 1v2c1 0 1-1 2 0l1 1-2 1c-1-1-2 0-3-2h0c0-1 1-3 2-4l3-3v-1c0-1 1-1 1-2z" class="I"></path><path d="M414 263c1-1 3-1 5-1l5-1-9 7c-1-2 0-2 0-3l-1-2zm5 5h-2 0c3-3 7-5 11-6 0 0 0 1 1 1-1 2-1 3-1 5h-9z" class="X"></path><path d="M426 270l2-1v4h0c-2 1-4 1-6 1-2-1-6 0-8-1 0-2 0-2 1-3h7 4z" class="v"></path><path d="M426 270l2-1v4h0c-2 1-4 1-6 1 0-1 0-1 1-2 0-1 3-2 3-2z" class="w"></path><path d="M407 261h4v1c1 1 2 1 3 1l1 2c0 1-1 1-1 2v1l-3 3v-1h0v-1c-1 1-2 1-2 1v1h-1l-1-4v-6z" class="J"></path><path d="M470 262l1-1h16 1v1c2 0 2 0 3 1l-1 1c-1 2-1 3-2 4l1 2c0 2 1 4 1 5-1 1-2 2-4 2h-4-1l-1-1c0 1 0 1-1 2 0-1-1-1-2-1 0 0-1 1-1 0h-9l-11 1h-5l-1-1c0-1 0 0-1 0l-2-2 1-1-1-1c1-1 2-1 2-2l-1-1v-1h0 2 1v-3l19-4z" class="Q"></path><path d="M478 272l1 1-1 1c-1 0-1-1-1-2h1z" class="Z"></path><path d="M457 273l2 2c-1 0-3 0-3-1l1-1z" class="L"></path><path d="M479 273h1 0v2h-1l-1-1 1-1z" class="V"></path><path d="M477 270v-1h1 0 2v3l-1-1h0l-2-1zm5 0h2l2 2v2c-1 0-2-1-2-1h-1c-1-1-1-1-2-1h0c0-1 0-1 1-2z" class="Y"></path><path d="M455 270c1-1 1-2 2-2s2 1 2 1c1 0 1-1 1 0l1 2h-3-3v-1z" class="C"></path><path d="M448 270v-1h0 2l1 1h-1c1 1 1 1 2 1l-1 1v2h-3l-1-1c1-1 2-1 2-2l-1-1z" class="h"></path><path d="M469 267h2l1 1 2 1-1 1c1 0 1 1 1 1h2l1-1 2 1-1 1h-1-1c-1 0-1 1-1 2h-1l-1-1-1 1c1 1 1 1 2 1v1h-2l-1-1c-1 0 0 0-1 1h-2l-1-1 1-1h1 0c-1 0-2-1-2-1-1 0-1 1-2 1h-2-3 0l-1 1h0 0l-2-2h1 3l1-2h2 0 1c1 0 2 1 3 0s1-2 1-4z" class="V"></path><path d="M472 268l2 1-1 1c1 0 1 1 1 1h2-1-1c-2 1-2 1-3 2h0c-1-1-1-1-1-2 2-1 2-2 2-3z" class="H"></path><path d="M469 267h2l1 1c0 1 0 2-2 3h-2c1-1 1-2 1-4z" class="M"></path><path d="M470 262l1-1h16 1v1c2 0 2 0 3 1l-1 1c-1 2-1 3-2 4 0-1-1-1-2-1s-1 0-2 1v-1-1l-1 1c-1 0-2 0-3-1l-2 1c-1 0-1 0-1-1-1 0-2 0-2 1l-1 2-2-1-1-1h-2c0 2 0 3-1 4s-2 0-3 0h-1 0-2-1l-1-2c0-1 0 0-1 0 0 0-1-1-2-1s-1 1-2 2l-1-1s0-1-1-1-1 1-2 1v-3l19-4z" class="x"></path><path d="M453 268c2-1 4-1 6-1l-2 1c-1 0-1 1-2 2l-1-1s0-1-1-1zm35-6c2 0 2 0 3 1l-1 1c-1 2-1 3-2 4 0-1-1-1-2-1h2v-5z" class="V"></path><path d="M464 267h1 4 0c0 2 0 3-1 4s-2 0-3 0h-1 0-2-1l-1-2c0-1 0 0-1 0 0 0-1-1-2-1l2-1h2v1c1-1 2-1 3-1z" class="E"></path><path d="M461 268c1-1 2-1 3-1 0 1-1 1-2 2 0-1 0-1-1-1z" class="G"></path><path d="M460 269h3l1 2h-2-1l-1-2z" class="Y"></path><path d="M365 149h1v1l2 3v2h0 1 0c1 0 1 0 1 1l-1 1c1 2 2 2 3 4 1 0 2 1 2 1-1 1-2 0-2 1s1 1 1 2c1 0 1 0 1 1 0 0 0 1-1 1v1l1 2v1 1h-2 0l1-1v-1l-1-1h-2l-1-1s-1 0-2 1c0 0 0 1 1 1v3 1l-1-1c-5 1-11 0-16 0h-4 0-5 0c-1-1 0-1 0-2h0c-1 0-1 0-1 1-1 0-1-1-1-1l-1-1c-1 0-1 1-3 1 1-1 1-2 1-3h1v-1-2-2h-1l-1-2h-1v-2l1-1 1 1 2-1 2 2h0c1 0 2-1 3-1s2 0 3-1c0 0 1 0 1 1l1-1v-1c1-1 2-1 3-3h2v-2h1l2-2c1 1 1 1 1 2h2v-2c1 0 1 0 2-1h1s1 1 2 1v-1z" class="C"></path><path d="M352 171c1 0 1-1 2-1l1 2h0-3 0v-1z" class="Q"></path><path d="M352 160c1 0 1 1 2 2v1h-1c-1 0-1 0-2-1 0-1 0-1 1-2z" class="b"></path><path d="M363 160h1c1 1 1 2 2 2l-1 1h-1c-1 0-1-1-2-2l1-1z" class="Z"></path><path d="M368 158v1c0 1 0 1-1 2h-1l-1-2c1-1 1-1 3-1z" class="Y"></path><path d="M373 168c-2 0-3 0-4-1 1-1 2-2 3-2 0 1 1 1 1 2v1z" class="Z"></path><path d="M360 157c0 1 1 2 1 3v1h-1c-1 0-2-1-2-2h0l2-2zm-10 12h0c1-1 2-2 2-3 1 1 1 1 1 2v1c-1 0-1 1-1 2v1l-1-1h-1v-2z" class="b"></path><path d="M356 172c0-1-1-3-1-4l2-1 1 1v1c0 1-1 2-1 3h-1z" class="H"></path><path d="M359 164v-2h2c1 1 1 2 2 2v1 1c-1 0-2 0-3-1h-1v-1z" class="I"></path><path d="M355 155h0l1-1c1 1 2 1 3 2l1 1-2 2h0c-1-1-2-1-2-2s-1-1-1-2z" class="G"></path><path d="M358 165l-1 1h-2c0 1 0 1-1 1-1-1-1-1-1-3 1 0 2-1 3-1h1v-1h-1c-1-1-3-2-3-3l2-2c0 1 1 1 1 2l2 2v2 2h0z" class="I"></path><path d="M364 165c2-2 4-3 6-5h0l1 1v1c0 1 0 2-1 3h-1s-1 0-1-1v-1h-1v1c0 1 1 1 1 3l-1 1c-1 0-2-2-3-3z" class="V"></path><path d="M363 152c1 0 2-1 3 0 1 0 1 1 1 1 0 1 0 1-1 1l1 1h1 0 0 1l-1 2v1c-2 0-2 0-3 1l-1-1-2 1c0-1-1-1-1-2h0v-1c-1 0-1-1-2-1 0-1 1-1 2-2h0l1-1h0 1z" class="G"></path><g class="Z"><path d="M359 155c0-1 1-1 2-2h0l1-1c1 1 1 2 1 3-1 0-1 1-2 1s-1-1-2-1z"></path><path d="M363 152c1 0 2-1 3 0 1 0 1 1 1 1 0 1 0 1-1 1l1 1h1 0 0 1l-1 2h-3 0c0-1 0-1-1-1 0-2 0-3-1-4z"></path></g><path d="M359 164v1h1c1 1 2 1 3 1v-1l1 1v-1c1 1 2 3 3 3v1s0 1 1 1v3 1l-1-1c-5 1-11 0-16 0h-4v-1c0-1 0-1 1-2l1-1h1v2h1l1 1h0 3 0 1 1c0-1 1-2 1-3v-1-3h0l1-1z" class="G"></path><path d="M363 167h1c1 1 1 1 1 2l-1 1h-1l-1-1s0-1 1-2zm-4-2c1 1 2 1 3 2l-1 2h-1l-1-1v-3z" class="Q"></path><path d="M365 149h1v1l2 3v2h0-1l-1-1c1 0 1 0 1-1 0 0 0-1-1-1-1-1-2 0-3 0h-1 0l-1 1h0c-1 1-2 1-2 2-1-1-1-2-2-2 0 0-1 0-1 1l-1 1h0c-1 1-1 2-2 2-1 2-3 4-4 5l-1 1-1 2-2 3-1 1c-1 0-1 1-2 2h0c-1 0-1 0-1 1-1 0-1-1-1-1l-1-1c-1 0-1 1-3 1 1-1 1-2 1-3h1v-1-2-2h-1l-1-2h-1v-2l1-1 1 1 2-1 2 2h0c1 0 2-1 3-1s2 0 3-1c0 0 1 0 1 1l1-1v-1c1-1 2-1 3-3h2v-2h1l2-2c1 1 1 1 1 2h2v-2c1 0 1 0 2-1h1s1 1 2 1v-1z" class="E"></path><path d="M344 169l-2-2 2-2v3h1l-1 1z" class="I"></path><path d="M339 158l2 2-1 1h0-2l-1-2 2-1z" class="b"></path><path d="M347 158s1 0 1 1h0c-1 0-1 1-2 1-1 1-2 1-4 1l-1-1c1 0 2-1 3-1s2 0 3-1z" class="L"></path><path d="M336 158l1 1 1 2c0 1 0 1-1 2l-1-2h-1v-2l1-1z" class="F"></path><path d="M306 144h4c0 1 1 1 1 2 0 0 1 0 1 1h-1l2 2h1 1l1-1 1-1 1 1 2 1c0 1 0 2 1 4 0-1 1-1 2-1l3 3h1c1 0 2 0 2 1l2-2 2-1 2 2c0 1 1 2 1 3l-1 1v2h1l1 2h1v2 2 1h-1c-1 0-2 0-3-1h-1l-1 1h-3l-2-1-1 1h0l-1 1-1 1-3-2h0c-1 0-1 0-1 1h-2c0-1-1-1-1-1v2h1c-1 1-1 2-2 2-2 0-4 0-6 1h0-2c1 1 1 2 0 3h-2c-1 0-2 0-3-1v-2l1-1c-1-2-1-5-1-7v-1-6c1 0 2 1 2 0l1-2c-1-1-2-1-2-1l-1-2c1-1 0-4 0-5l1 1v-1c2-1 2-2 3-3 0-1-1-1-1-1z" class="V"></path><path d="M307 162h1 1c0-1 1-1 1-2h0 1v-1c0 1 0 1 1 1v1c0 1-1 1-1 1-1 1-2 0-3 0h-1z" class="x"></path><path d="M313 149h1v3l1 1-2 2-1-1v-1-1-2h-1c1 0 1-1 1-1h1z" class="w"></path><path d="M315 153h2c0 1 1 2 1 2v1c-1 1-1 1-2 1l-2-2h-1 0l2-2z" class="P"></path><path d="M303 164h1l1-1 1-1h1 1c0 1 1 2 1 3l-1 1h-1c-1-1-2-1-4-1v-1z" class="J"></path><path d="M307 145l3 3v1s-1 0-1 1c-2-1-3-1-4-1h-1v-1c2-1 2-2 3-3z" class="S"></path><path d="M309 151c0 1 0 1 1 2l1 1c1 1 1 2 1 2v1c-1 0-1 1-2 1l-2-2h0l-1 1c0-1 1-2 2-3v-3z" class="R"></path><path d="M312 160h0c1 1 2 2 3 2h1c0 1 0 2-1 3h-2-1c-1-1-1-2-1-3 0 0 1 0 1-1v-1z" class="p"></path><path d="M316 148l1-1 1 1 2 1c0 1 0 2 1 4l-1 1h-1v1h-1s-1-1-1-2h-2l-1-1v-3h1l1-1z" class="E"></path><path d="M318 148l2 1c0 1 0 2 1 4l-1 1-1-1v-2l-1-1-1-1 1-1z" class="w"></path><path d="M303 148l1 1h1c1 0 2 0 4 1v1 3c-1 1-2 2-2 3l-1-1c-1-1-2-1-2-1l-1-2c1-1 0-4 0-5zm13 14h1v-2l1-1c0 1 1 2 2 2h2l1 1h-1v1h1 1l2 2v1 2h0l-1 1-1 1-3-2h0c-1 0-1 0-1 1h-2c0-1-1-1-1-1-1-1-1-2-1-3h-1c1-1 1-2 1-3h0z" class="E"></path><path d="M321 168h1c1-1 1-2 1-3 1 1 1 2 2 3h1l-1 1-1 1-3-2h0z" class="b"></path><path d="M316 162h1v-2l1-1c0 1 1 2 2 2 1 1 1 2 1 3l-1 1c-1 0-2-2-4-2v-1z" class="p"></path><path d="M321 153c0-1 1-1 2-1l3 3h1c1 0 2 0 2 1h0c1 1 1 1 1 2v1l-1 1c0 1 0 2 1 2l-2 1-2 2-2-2h-1-1v-1h1l-1-1-1-2v-1l-2 1c-1-1 0-1-1-3v-1h1v-1h1l1-1z" class="w"></path><path d="M325 160h3 1c0 1 0 2 1 2l-2 1-1-1-2-2z" class="K"></path><path d="M323 162v-1c0-1 1-1 1-2l1 1 2 2 1 1-2 2-2-2h-1-1v-1h1z" class="P"></path><path d="M321 153c0-1 1-1 2-1l3 3c-2 1-2 1-4 1l-2-2v1h-1v-1h1l1-1z" class="F"></path><path d="M333 153l2 2c0 1 1 2 1 3l-1 1v2h1l1 2h1v2 2 1h-1c-1 0-2 0-3-1h-1l-1 1h-3l-2-1-1 1v-2-1l2-2 2-1c-1 0-1-1-1-2l1-1v-1c0-1 0-1-1-2h0l2-2 2-1z" class="V"></path><path d="M328 163l2-1 1 2h-1c0 1 0 1-1 2h-3v-1l2-2z" class="Q"></path><path d="M333 153l2 2c0 1 1 2 1 3l-1 1v2h1c-1 1-1 2-2 2s-1 1-2 1h-1l-1-2c-1 0-1-1-1-2l1-1v-1c0-1 0-1-1-2h0l2-2 2-1z" class="b"></path><path d="M333 157l-1-1 1-1h1v2l-1 1v-1z" class="w"></path><path d="M331 159c1-1 1-1 1-2h1v1l1 1c0 1 0 0 1 0v2h1c-1 1-1 2-2 2-1-2-1-3-3-4z" class="H"></path><path d="M330 159h1c2 1 2 2 3 4-1 0-1 1-2 1h-1l-1-2c-1 0-1-1-1-2l1-1z" class="F"></path><path d="M308 162c1 0 2 1 3 0 0 1 0 2 1 3h1 2 1c0 1 0 2 1 3v2h1c-1 1-1 2-2 2-2 0-4 0-6 1h0-2c1 1 1 2 0 3h-2c-1 0-2 0-3-1v-2l1-1c-1-2-1-5-1-7 2 0 3 0 4 1h1l1-1c0-1-1-2-1-3z" class="U"></path><path d="M308 171c0-1 0-1 1-2 1 0 2 1 2 2h0l-1 2h-2s-1-1-2-1c0 0-2 1-3 1l1-1h0c2 0 3 0 4-1z" class="y"></path><path d="M311 171v1h2v-1c0-1 1-1 1-1 0-1 0-1 1-1v-1l2 2h1c-1 1-1 2-2 2-2 0-4 0-6 1h0l1-2z" class="p"></path><path d="M303 173c1 0 3-1 3-1 1 0 2 1 2 1 1 1 1 2 0 3h-2c-1 0-2 0-3-1v-2z" class="U"></path><path d="M312 165h1 2 1c0 1 0 2 1 3v2l-2-2-1-1v1l-1 1h0c-1-1-2-2-2-3v-1h1z" class="V"></path><path d="M303 165c2 0 3 0 4 1 0 1 0 1 1 2v3c-1 1-2 1-4 1h0c-1-2-1-5-1-7z" class="b"></path><path d="M289 138l14-4v5c-1 1 0 3 0 5v4c0 1 1 4 0 5l1 2s1 0 2 1l-1 2c0 1-1 0-2 0v6 1c0 2 0 5 1 7l-1 1v2h0-18-3-2v-5-5c0-1 0-4 1-6-1-1-1-2-1-3-1-1 0-3 0-4s-1-3 1-5c-1 0-1 0-1-1v-2l1-3h0l8-3z" class="D"></path><path d="M303 153l1 2s1 0 2 1l-1 2c0 1-1 0-2 0v-5z" class="T"></path><path d="M281 147c2-2 5-4 9-4 3 1 6 2 8 5 2 2 2 5 2 8-1 3-3 5-5 7-2 1-5 2-8 1s-5-2-6-5c-1-1-1-2-1-3-1-1 0-3 0-4s-1-3 1-5z" class="X"></path><path d="M503 147l1 1c1 1 2 2 2 3l7 8c1 1 2 2 2 3h0v1h-1-1v2l-1 3-1 1c0 1-1 2-1 2h2v1h-2c-2 0-3 0-4-1h-1l2 9v2c-1 0-1 1-1 2-1 1-1 1-1 2h0c-2 4 0 9-1 13v14c0 1-1 4 0 6v4l-2-1v3 8 8 4c0 1-1 1-1 2-1 1-2 1-2 1v4h-1-3-3v-13-36-9c0-2 0-4 1-5h0c-1-1-2 0-3 0v-1c0-1 0-4 1-5 0-2 1-5 2-6v-1-1l-1 1s0 1-1 2c0 0-1 1-1 2-1 2-1 6-2 8h-1v-3c-1-2-1-4 0-6v-2c0-2-1-4 0-5 0-1 0-1 1-1v-1c0-1 1-2 2-3 1 0 1 0 2-1v-1c-2 1-3 3-5 4v-1c1-1 4-4 5-6 0 0 0-1 1-1h0v-1h1 1c2-3 4-8 5-11l3-2z" class="D"></path><path d="M495 180l2 1c1 0 1 1 1 1-1 2-2 3-3 5v-7z" class="C"></path><path d="M505 165c2 2 3 3 3 4l1 2c-3-1-4-2-6-4l2-2z" class="a"></path><path d="M504 160h0c0 2 1 4 1 5l-2 2-1-4v-1h0c0-1 1-2 2-2z" class="k"></path><path d="M504 160h0v3h-2v-1h0c0-1 1-2 2-2z" class="N"></path><path d="M499 178s0 1 1 2h1v-1c0 2 0 3 1 4v2l1 1-2 2-1-1c-1-1 0-2-1-3v-4-2z" class="c"></path><path d="M499 178s0 1 1 2h1c0 1 0 2-1 3 0-1 0-1-1-2v-1-2z" class="k"></path><path d="M504 160v-3h1l1 1c-1 3 0 4 1 7l3 5v-1h1c0 1-1 2-1 2h0-1l-1-2c0-1-1-2-3-4 0-1-1-3-1-5h0z" class="W"></path><path d="M495 180c0-2 1-5 1-8h-1 0c0 1 0 2-1 2h0c0-2 2-4 2-6h0c1 3 2 6 2 8v6s0-1-1-1l-2-1z" class="M"></path><path d="M502 202c1 3 0 8 1 12v-1c1-4 0-10 1-14h0v14c0 1-1 4 0 6v4l-2-1v-6-14z" class="O"></path><path d="M505 186h-1c-1-1-1-3-1-5h0 0 0c0-2 0-4 1-6h0c2 1 1 4 2 6l1 1c-1 0-1 1-1 2-1 1-1 1-1 2h0z" class="N"></path><path d="M496 166l1 1c-1 1-1 2-2 2-1 2-2 5-4 7s-3 5-3 7l-1 2c-1-2-1-4 0-6 1-3 3-4 5-7l4-6z" class="M"></path><path d="M504 149v-1c1 1 2 2 2 3-2 3-5 7-6 11 0 1-1 1 0 2 0 2 1 4 1 5 1 3 1 7 0 9v1 1h-1c-1-1-1-2-1-2v-2l-2-11h0v2l-1-1v-1-1c0-1 2-3 3-4 2-2 5-9 5-11z" class="k"></path><path d="M499 176v-2h1c0-1 0-1 1-2 1 2 0 4 0 6v1 1h-1c-1-1-1-2-1-2v-2z" class="N"></path><path d="M500 162c1-4 4-8 6-11l7 8c1 1 2 2 2 3h0v1h-1-1v2l-1 3-1 1h-1v1l-3-5c-1-3-2-4-1-7l-1-1h-1v3c-1 0-2 1-2 2h0-2z" class="a"></path><path d="M510 159l1-1c1 2 2 4 4 4v1h-1-1v2l-1 3-1 1h-1v-2c-1-2-1-3-2-5l1-3h1z" class="D"></path><path d="M510 159l1-1c1 2 2 4 4 4v1h-1-1v2l-3-6z" class="e"></path><path d="M500 162c1-4 4-8 6-11l7 8c1 1 2 2 2 3h0c-2 0-3-2-4-4l-2-3-3 3h0l-1-1h-1v3c-1 0-2 1-2 2h0-2z" class="T"></path><path d="M503 147l1 1v1c0 2-3 9-5 11-1 1-3 3-3 4v1 1l-4 6c-2 3-4 4-5 7v-2c0-2-1-4 0-5 0-1 0-1 1-1v-1c0-1 1-2 2-3 1 0 1 0 2-1v-1c-2 1-3 3-5 4v-1c1-1 4-4 5-6 0 0 0-1 1-1h0v-1h1 1c2-3 4-8 5-11l3-2z" class="D"></path><path d="M488 170l3-3h1l2-2h1v1c-2 4-6 7-8 11 0-2-1-4 0-5 0-1 0-1 1-1v-1z" class="M"></path><path d="M503 147l1 1v1c-1 1-2 3-3 5s-2 4-4 6v1c-1 1-2 1-3 1l-1-1v-1h1 1c2-3 4-8 5-11l3-2z" class="a"></path><path d="M499 248v-45c0-3-1-13 0-14h1 2c1 1 0 3 0 5v8 14 6 3 8 8 4c0 1-1 1-1 2-1 1-2 1-2 1z" class="x"></path><path d="M256 91l3-9c13-3 27-5 41-7 11-1 23-2 35-2 9 0 17 0 25 2l-71 62v1l-8 3h0l-1 3v2c0 1 0 1 1 1-2 2-1 4-1 5s-1 3 0 4c0 1 0 2 1 3-1 2-1 5-1 6v5 5 2l-2-2-1 1s-1-1-2-1h0c-1 0-1 0-2-1h-2l-1-1v1h-1c-1 0-2-1-3-1h-1l2 2h-1 0c-1-1-1-1-2-1h0l-1 1c-2 1-4 2-5 5h1c-1 1-2 2-3 2s-1 0-2 1v-2l1-3v-2-1-2-3-4c0-1 0-2 1-3l-1-1c0-1 0-3 1-4h0c-1-2-1-3-2-5h1v-9c0-6-1-12 0-18v-8c-1-1-1-3-1-5v-5h0c-1 0-1 0-1 1-1 0-1 0-1 1l-1-1v2-2l-2-2v-1l1-1c-1-1-1-1-1-2l1-2c1-1 2-3 2-4v-1l-2-1h1 0c2-1 2-1 3-2v1l-1 1v4c1-1 1-2 1-3s1-3 2-4v-1z" class="D"></path><path d="M268 146c1-1 3-2 4-3l1 2-2 2s-1 0-1-1h-2z" class="m"></path><path d="M272 143l5-2v1h1v1l-1 1c-1 0-2 1-3 1 0 1 0 1-1 1v-1l-1-2z" class="c"></path><path d="M289 137v1l-8 3h0l-1 3h-1l-1-1v-1h-1v-1l12-4z" class="k"></path><path d="M256 91c-1 6 0 13 0 19v31h-1c0-5 1-10 0-15v-8c-1-1-1-3-1-5v-5h0c-1 0-1 0-1 1-1 0-1 0-1 1l-1-1v2-2l-2-2v-1l1-1c-1-1-1-1-1-2l1-2c1-1 2-3 2-4v-1l-2-1h1 0c2-1 2-1 3-2v1l-1 1v4c1-1 1-2 1-3s1-3 2-4v-1z" class="v"></path><path d="M252 102l3-4v20c-1-1-1-3-1-5v-5h0c-1 0-1 0-1 1-1 0-1 0-1 1l-1-1v2-2l-2-2v-1l1-1c-1-1-1-1-1-2l1-2 2 1z" class="J"></path><path d="M250 101l2 1-2 3c-1-1-1-1-1-2l1-2z" class="V"></path><path d="M255 126c1 5 0 10 0 15h1v12h1 0c3-3 7-5 11-7h2c0 1 1 1 1 1l2-2v1c1 0 1 0 1-1 1 0 2-1 3-1l1-1 1 1h1v2c0 1 0 1 1 1-2 2-1 4-1 5s-1 3 0 4h-4-1v1c1 2 1 2 1 3h-1l-1-2h-1l-1 1-2 2v-2 1h-2l-2 2c-2 1-3 2-3 4-1-1-1-1-1-2l-1-1c-1 1-1 1-2 1h-1 1c0-1-1-1-1-2-1 0-1 1-2 1l-1-1c0-1 0-3 1-4h0c-1-2-1-3-2-5h1v-9c0-6-1-12 0-18z" class="z"></path><path d="M275 152h1v2h-2-1v-1l2-1z" class="AD"></path><path d="M265 154c1-1 1-1 3-1h0v1c0 1-1 1-1 2l-1-1s-1 0-1-1z" class="t"></path><path d="M270 159c0-1 0-1 1-2v1c1 1 1-1 2-1v1l-1 1-2 2v-2z" class="f"></path><path d="M270 151l2-1 1 1h0c-1 1-1 1-1 2-1 1-1 1-2 1v-1l1-1-1-1z" class="F"></path><path d="M260 160c1 0 1 1 2 1l-1 2c-1 1-1 1-2 1h-1 1c0-1-1-1-1-2l1-1 1 1v-1h0v-1z" class="t"></path><path d="M264 159v-2h1c2 1 3 2 5 3h-2c-1 0-2 0-2-1h0-1c0 1 1 1 0 1l-1-1z" class="r"></path><path d="M279 144h1v2c0 1 0 1 1 1-2 2-1 4-1 5l-4-4c1-1 2-3 3-4z" class="m"></path><path d="M256 158l1-1c-1 0-1 0-1-1h-1l1-1v-1c1 1 1 1 2 1h1v1c0 1 1 2 0 3l-2 2v-2h-1v-1z" class="a"></path><path d="M260 160s0-1 1-1v-2c1 1 2 1 2 1l1 1 1 1c1 0 0 0 0-1h1 0c0 1 1 1 2 1l-2 2c-2 1-3 2-3 4-1-1-1-1-1-2l-1-1 1-2c-1 0-1-1-2-1z" class="i"></path><path d="M262 161h2v1l-2 2-1-1 1-2z" class="r"></path><path d="M278 143l1 1c-1 1-2 3-3 4l-1 2h-1v1l-1 1v-1h0l-1-1-2 1c-1 0-2 1-2 2h0c-2 0-2 0-3 1l-1 1h0l-1-1 2-1c2-1 4-3 6-6l2-2v1c1 0 1 0 1-1 1 0 2-1 3-1l1-1z" class="P"></path><path d="M278 143l1 1c-1 1-2 3-3 4l-1 2h-1v1l-1 1v-1h0l1-1c0-3 2-4 3-6l1-1z" class="N"></path><path d="M268 146h2c0 1 1 1 1 1-2 3-4 5-6 6h-2c-1 0-1-1-2-1s-2 1-3 1h-1c3-3 7-5 11-7z" class="K"></path><path d="M270 146c0 1 1 1 1 1-2 3-4 5-6 6h-2l-1-1c1 0 1 0 1-1 1 0 2-1 3-1l2-2v-1l2-1z" class="M"></path><path d="M275 157v-1h1 4c0 1 0 2 1 3-1 2-1 5-1 6v5 5 2l-2-2-1 1s-1-1-2-1h0c-1 0-1 0-2-1h-2l-1-1v1h-1c-1 0-2-1-3-1h-1l2 2h-1 0c-1-1-1-1-2-1h0l-1 1c-2 1-4 2-5 5h1c-1 1-2 2-3 2s-1 0-2 1v-2l1-3v-2-1-2-3-4c0-1 0-2 1-3 1 0 1-1 2-1 0 1 1 1 1 2h-1 1c1 0 1 0 2-1l1 1c0 1 0 1 1 2 0-2 1-3 3-4l2-2h2v-1 2l2-2 1-1h1l1 2h1c0-1 0-1-1-3z" class="g"></path><path d="M275 170l-1-2v-1c1 1 2 2 2 3h-1z" class="K"></path><path d="M272 171c1-1 1-2 2-3l-1-1h1v1l1 2-1 1-1 1c-1 0-1-1-1-1z" class="AD"></path><path d="M276 164l1-2 2 2c0 1-1 1-1 1v3h0c-1 0-2-1-3-2 0-1 1-1 1-2z" class="N"></path><path d="M278 165h2v5h-1c-1 1-1 2-2 3l-1-1v-2c1 0 1 0 2-1v-1h0v-3z" class="AD"></path><path d="M270 159v2l2-2v2c0 1-1 1-1 1 0 1 1 1 1 1l1-1h1v1h0l-1 2h-2v-2h-3-1l-1-1 2-2h2v-1z" class="t"></path><path d="M270 159v2l-2 2h-1l-1-1 2-2h2v-1z" class="F"></path><path d="M271 169l1 2s0 1 1 1l1-1c1 0 2 0 2 1l1 1c1-1 1-2 2-3h1v5 2l-2-2-1 1s-1-1-2-1h0c-1 0-1 0-2-1h-2l-1-1 1-2c-1 0-1 0-1-1l1-1z" class="q"></path><path d="M271 171l2 3h0-2l-1-1 1-2zm3 0c1 0 2 0 2 1l1 1h-1c-1 0-2-1-3-1l1-1z" class="c"></path><path d="M279 170h1v5 2l-2-2-1 1s-1-1-2-1h0 1l3-3v-2z" class="K"></path><path d="M275 157v-1h1 4c0 1 0 2 1 3-1 2-1 5-1 6h-2s1 0 1-1l-2-2-1 2v-1h-2 0 0v-1h-1l-1 1s-1 0-1-1c0 0 1 0 1-1v-2l1-1h1l1 2h1c0-1 0-1-1-3z" class="i"></path><path d="M275 157v-1h1 4c0 1 0 2 1 3-1 2-1 5-1 6h-2s1 0 1-1h0c0-2-1-2-2-3v-1h0 1c0-1 1-1 1-2h1-1s-1-1-2-1h0-1-1z" class="o"></path><path d="M263 166c0-2 1-3 3-4l1 1h-1c1 0 1 0 2 1s1 1 2 1c1 1 1 1 1 2v1h1c0 1 0 1-1 1l-1 1c0 1 0 1 1 1l-1 2v1h-1c-1 0-2-1-3-1h-1l2 2h-1 0c-1-1-1-1-2-1h0l-3-3-1 1h-1v-1l1-1 1-1h0c0-1 1-2 2-3z" class="r"></path><path d="M266 163c1 0 1 0 2 1s1 1 2 1c1 1 1 1 1 2v1h1c0 1 0 1-1 1l-1 1h-1-1l-1-1h0l-2-1c0-1-1-2 0-3h0c1 1 2 1 2 2h0 1v-1l-2-1v-2z" class="K"></path><path d="M267 169v-1h3l-1 2h-1l-1-1h0z" class="N"></path><path d="M271 167v1h1c0 1 0 1-1 1l-1 1h-1l1-2 1-1z" class="i"></path><path d="M261 171s1-1 2-1l1 2h1v-1c-1-1-2-3-4-4 2 0 4 1 5 2v1l1-1 1 1h1 1c0 1 0 1 1 1l-1 2v1h-1c-1 0-2-1-3-1h-1l2 2h-1 0c-1-1-1-1-2-1h0l-3-3z" class="AC"></path><path d="M268 170h1 1c0 1 0 1 1 1l-1 2v1h-1c-1 0-2-1-3-1 1-1 2-2 2-3z" class="t"></path><path d="M258 162c0 1 1 1 1 2h-1 1c1 0 1 0 2-1l1 1c0 1 0 1 1 2-1 1-2 2-2 3h0l-1 1-1 1v1h1l1-1 3 3-1 1c-2 1-4 2-5 5h1c-1 1-2 2-3 2s-1 0-2 1v-2l1-3v-2-1-2-3-4c0-1 0-2 1-3 1 0 1-1 2-1z" class="i"></path><path d="M258 164h1l-1 2c-1 0-1 0-2 1v-1l2-2z" class="m"></path><path d="M256 173v-1c1-1 1-1 2-1v3c-1 0-1-1-2-1z" class="c"></path><path d="M255 170l2-1c1 1 1 0 1 1v1c-1 0-1 0-2 1v1h-1v-3z" class="AD"></path><path d="M259 164c1 0 1 0 2-1l1 1c0 1 0 1 1 2-1 1-2 2-2 3h0-1c-1-1-2-2-2-3l1-2z" class="g"></path><path d="M259 176c1-1 1-1 2-1-1-1-1-1-1-2h1c0 1 1 1 2 2-2 1-4 2-5 5h1c-1 1-2 2-3 2s-1 0-2 1v-2l1-3v-2-2c1 0 1 0 1 1 1 0 2 0 3 1z" class="a"></path><path d="M255 176v-2c1 0 1 0 1 1 1 0 2 0 3 1h0l-2 2h-2v-2z" class="AC"></path><path fill="#fff" d="M332 168l1-1h1c1 1 2 1 3 1 0 1 0 2-1 3 2 0 2-1 3-1l1 1s0 1 1 1c0-1 0-1 1-1h0c0 1-1 1 0 2h0 5 0 4c5 0 11 1 16 0l1 1v-1-3c-1 0-1-1-1-1 1-1 2-1 2-1l1 1h2l1 1v1l-1 1h0 2v-1-1l1 2c1 1 4 1 6 1s7 0 9-1c1 4 3 9 3 13v1 2c1 3 1 14 1 18v15 26c0 3-1 8 0 12 0 0 0 1 1 1h12v1 6l1 4h1v-1s1 0 2-1v1h0v1c-1 1-2 3-2 4h0c1 2 2 1 3 2l2-1v-1 1c0-1 0-1 1-1h8c2 0 3-1 5 0v2 2h0 0c1 0 2 1 3 1s2 1 3 2c1-1 1-1 2-1h0l-1-2c1 0 1-1 2-1l2-2c0 1 0 2 1 2l1-2v-1l1 1h0c0 1 1 2 2 3h-2v1 3 1 3 1c0 1 0 1-1 2 1 2 1 1 0 3v3h1v1c-1 2-2 3-3 5h1 1c0 1-1 2-2 4h0c-1 1-2 1-2 3h0c1 0 1 0 1 1h0c-1 1-1 1-1 2 0 2-1 2-2 3h-4l-1 1 1 2s-1 1-1 2h0 0c1 0 2 1 2 1v1c0 1-1 2-1 2 0 1 0 1 1 2 1 0 1 0 2 1h1v2l-1 2h0c0 1 0 3-1 4v3 1h0v1 5l1 1 1-2v4 11l1 1c1 0 3 0 5 1 6 0 12 0 17 1h0c0 2-1 3-2 4h2c1 1 1 1 1 2h1v4 1c0 1 0 2-1 3 1 1 0 2 0 3h0v1 3h1l-1 2v-1h0c-3 1-4 4-7 5v3h1l-4 1c-2-1-3-1-5 0h-4-2-1l-2 1h-1 0c-1 1-2 1-3 2h-3-3v3 2h0v1c0 1 0 2-1 3-1-1-2-3-3-3h-1c0-1-1-2-2-3h0l-1-1h-1c-4-3-6-6-9-10l-11-13-5-6v-1l-77-92v13c1 1 1 14 1 17h0c6-1 12 1 17 1 2 0 5 0 7 1v1c-3 0-5 0-8-1-5 0-11 0-16 1v4c-1 4-1 9 0 12v41c0-1 0 0-1-1-1 0-1-1-3-2h0c-3-2-6-3-9-5h-1c-1 0-1-1-1-1h-1c-1-1-2-1-3-2h-1c-1-1-1-1-2-1l-1-1c-1 0-1 0-2-1-1 0-2-1-3-1l-2-1-1-1s-1 0-2-1h-1l-2-1c-1-1-2-1-3-1l-1-1c-1 0-2-1-2-1h-1l-1-1c-1 0-2-1-3-1s-3-1-4-2c-2 0-3-1-4-1-2-1-5-2-7-2-1 0-1-1-1-2l-3-8v-2c-1-1-1-2-2-3v-2l-3-8-2-5-5-16v-3c-1-2-1-4-2-7v-1h-6c-2-1-4-1-5-4 1 0 1 1 2 1l9 3v-9-11l6-18c1 0 2-2 2-3 1-1 2-3 3-5 3-3 6-7 9-10-1-1-1-1-2-1l1-9v-3-1l-1-14 1-10v-2-1c1-1 1-1 2-1s2-1 3-2h-1c1-3 3-4 5-5l1-1h0c1 0 1 0 2 1h0 1l-2-2h1c1 0 2 1 3 1h1v-1l1 1h2c1 1 1 1 2 1h0c1 0 2 1 2 1l1-1 2 2v-2h2 3 18 0c1 1 2 1 3 1h2c1-1 1-2 0-3h2 0c2-1 4-1 6-1 1 0 1-1 2-2h-1v-2s1 0 1 1h2c0-1 0-1 1-1h0l3 2 1-1 1-1h0l1-1 2 1h3z"></path><path d="M266 212h1c1 0 2 0 3 1l-3 2c-1-1-2-1-2-2l1-1z" class="g"></path><path d="M275 206l1 1 1 4-5 2c0-2 1-3 2-4h0c1-1 1-2 1-3h0z" class="C"></path><path d="M290 204v-8l4-1 1 7-1 2v-3c-1-1-1-2-1-3v-1h-2s0 2 1 2c0 1 1 2 1 3v1 1c-1 1-1 1-2 1l-1 1v-2z" class="X"></path><path d="M393 349h0c0 2 0 4 1 6v2s1 1 1 2v4 4l-1 1v-1h-2l-1 2v-1-1-3c2-5 2-10 2-15z" class="E"></path><path d="M297 195c2 1 3 1 4 1l1 9h-1c-1 0-5 1-6 0l-1-1 1-2v-6c1-1 1 0 2-1z" class="J"></path><path d="M310 195l5 1h3v1l-1 1c-1 2-2 3-3 5l-1 1h0-2c-1 1-3 0-4 0 1-1 0-3 0-5l1-1c-1-1-1-1-1-2h-1 1c1 0 2 0 3-1z" class="G"></path><path d="M310 195l5 1c-1 1-2 1-2 3h-1v-1h-1c-1-1-2-1-3-1 0 1 0 2 1 3v1l-2-2 1-1c-1-1-1-1-1-2h-1 1c1 0 2 0 3-1z" class="d"></path><path d="M315 196h3v1l-1 1c-1 2-2 3-3 5v-1c-1-1-2-3-3-4h1v1h1c0-2 1-2 2-3z" class="a"></path><path d="M315 196h3v1l-1 1c-1 0-2 0-3 1h-1c0-2 1-2 2-3z" class="k"></path><path fill="#c83d3a" d="M314 289c1 1 1 14 1 17h0c6-1 12 1 17 1 2 0 5 0 7 1v1c-3 0-5 0-8-1-5 0-11 0-16 1v4c-1-2-1-4-1-6l-31-1c10-1 21 0 31 0v-17z"></path><path d="M254 211l1 1c1 0 2 1 4 2l3-2h0 4l-1 1c0 1 1 1 2 2l-9 6-2 1-1 2c-1-1-1-1-2-1l1-9v-3z" class="K"></path><path d="M254 220v-2s0-1 1-1c1-2 2-1 4-1-1 0-1 0-2 1h1c-2 1-2 1-3 2h0l-1 1z" class="f"></path><defs><linearGradient id="T" x1="258.952" y1="219.468" x2="262.842" y2="212.874" xlink:href="#B"><stop offset="0" stop-color="#4a4337"></stop><stop offset="1" stop-color="#665a4d"></stop></linearGradient></defs><path fill="url(#T)" d="M265 213c0 1 1 1 2 2l-9 6-2 1-2-2 1-1h0c1-1 1-1 3-2h-1c1-1 1-1 2-1l6-3z"></path><path d="M255 219l2-1c1 1 1 2 1 3l-2 1-2-2 1-1h0z" class="O"></path><path d="M310 173h0v1 21c-1 1-2 1-3 1h-1 1c0 1 0 1 1 2l-1 1c0 2 1 4 0 5h-1l-1-1c-1 0-2 0-3 1v1l-1-9c-1 0-2 0-4-1 1-2 3-3 4-5 1-1 1-3 2-5 1-1 1-2 1-4 0-1-1 0-2-1h1c1-1 1-2 1-2l2-2h0 2c1-1 1-2 0-3h2z" class="AD"></path><path d="M310 173h0v1c0 2 1 5-1 7v-4c-1-1-1-1-3-1h0 2c1-1 1-2 0-3h2z" class="C"></path><path d="M302 196h4v8h0l-1-1c-1 0-2 0-3 1v1l-1-9h1z" class="Z"></path><path d="M302 204v-4c2-1 2 0 3 0l1 4h0l-1-1c-1 0-2 0-3 1z" class="w"></path><path d="M306 176c2 0 2 0 3 1v4h0v3 2h0l-1 1h0-2c-2 1-2 1-3 3l-1 1v5h-1c-1 0-2 0-4-1 1-2 3-3 4-5 1-1 1-3 2-5 1-1 1-2 1-4 0-1-1 0-2-1h1c1-1 1-2 1-2l2-2z" class="m"></path><path d="M306 184h3v2h0l-1 1h0-2c-2 1-2 1-3 3l-1 1c0-1 1-2 2-4 0-1 1-2 2-3z" class="AA"></path><path d="M386 306c0 1 0 2 1 3 1-1 2-1 3-1l1 1h1 2c0 1 0 1-1 2-1 0-2 0-2 1h-3c0 1 0 1-1 1v1h1 1c0-1 0-1 1-1h1c0-1 1-1 2-1h1v3 25l-1 9h0-1l-6-5v-1c1-1 0-2 0-3 0-2 1-5 0-7v-1c-1-1 0-5 0-6 1 1 1 2 2 3h1c1 0 2 0 2-1v-1c-1 0-2-1-4-2h0v-2c-1 0-1-1-1-1 1-2 1-4 2-6l2-1v-1h-1-1-1 0-1v-8z" class="Y"></path><path d="M394 315v-3h1l3 3c1 3 5 6 5 8v1l3 4v2c-1 0-2 0-3 1l1 1c0 1 1 1 1 2h0v1c-1-1-2-2-2-4-2 2-2 5-2 7h0c0 1-1 2-1 3h-1l-1 1h0v3 6 2 2 2c0 1 0 2-1 3h-1l-1-1c0-1-1-2-1-2v-2c-1-2-1-4-1-6l1-9v-25z" class="f"></path><path d="M398 320l5 11 1 1c0 1 1 1 1 2h0v1c-1-1-2-2-2-4 0-1-2-2-2-3-1-1-1-2-2-3 0-1-1-1-1-1-1 1-1 4-1 5l-1 11c0 2 0 4-1 6 0 0 0 1-1 1v-1 1-6 2-1s0-1 1-1c0-2 0-4 1-6 0-4 1-7 1-11v-2c1-1 1-1 1-2z" class="S"></path><path d="M399 325c1 1 1 2 2 3 0 1 2 2 2 3-2 2-2 5-2 7h0c0 1-1 2-1 3h-1l-1 1h0v3 6 2 2 2c0 1 0 2-1 3h-1l-1-1c0-1-1-2-1-2 1-1 1-1 2-1v-3l1-1h-1c-1-1 0-4 0-5v-2l1-1c0-4 0-8 1-12 0-2 1-4 1-7h-1 1z" class="g"></path><path d="M399 341v-5c1-2 0-7 2-8 0 1 2 2 2 3-2 2-2 5-2 7h0c0 1-1 2-1 3h-1z" class="AD"></path><path d="M394 315v-3h1l3 3c1 3 5 6 5 8v1l3 4v2c-1 0-2 0-3 1l-5-11c0 1 0 1-1 2v2c0 4-1 7-1 11-1 2-1 4-1 6-1 0-1 1-1 1v1-2-1-25z" class="j"></path><path d="M398 320c-1-2-2-4-2-6l7 9v1l3 4v2c-1 0-2 0-3 1l-5-11z" class="Q"></path><path d="M317 168s1 0 1 1h2c0-1 0-1 1-1h0l3 2s1 1 1 2c-1 0-1 1-2 1h0l-2 2c1 1 2 1 2 2v8c0 1 0 3-1 4v1c0 2 0 2-1 3 0 1 0 1-1 2v1c-1 0-1 0-1 1h-1v-1h-3l-5-1v-21-1c2-1 4-1 6-1 1 0 1-1 2-2h-1v-2z" class="O"></path><path d="M317 168s1 0 1 1h2c0-1 0-1 1-1h0l3 2s1 1 1 2c-1 0-1 1-2 1h0l-2 2c1 1 2 1 2 2v8c0 1 0 3-1 4v1c0 2 0 2-1 3 0 1 0 1-1 2v1c-1 0-1 0-1 1h-1v-1-12c0-4 1-7 0-11l-2-1c1 0 1-1 2-2h-1v-2z" class="C"></path><path d="M317 168s1 0 1 1h2v1h-2-1v-2z" class="b"></path><path d="M321 168l3 2s1 1 1 2c-1 0-1 1-2 1-2-1-2-1-3-2 0-2 0-2 1-3z" class="B"></path><path d="M318 184l1-8h1c1 2 0 4 0 6v4h2v3 1c0 2 0 2-1 3 0 1 0 1-1 2v1c-1 0-1 0-1 1h-1v-1-12z" class="L"></path><path d="M320 186h2v3 1h-1c-1-2-1-3-1-4z" class="a"></path><path d="M400 341c0-1 1-2 1-3h0c0-2 0-5 2-7 0 2 1 3 2 4v1l8 12c2 3 3 6 5 8 1 1 1 1 1 2h-1c-2 2-5 2-8 3l-2 1-2 1c-1 0-2 1-3 1l-2-3h0-3l-1-1c1-1 1-2 1-3v-2-2-2-6-3h0l1-1h1z" class="C"></path><path d="M400 349l2-1c0-1 0-1-1-2 1-1 0-3 1-4v4 4 1c-1 0-1 0-2-1v-1z" class="N"></path><path d="M400 341c0-1 1-2 1-3h0c0-2 0-5 2-7 0 2 1 3 2 4v1c0-1-1-1-1-1-1 1-1 2-1 3-1 0-1 0-1 1-1 1-2 2-2 4v3-2c-1 0-1-2 0-3z" class="c"></path><path d="M406 352h-1c-1-1-1-1-2-3l2-2c0 1 1 2 1 2h3c2-1 2-1 4-1 2 3 3 6 5 8 1 1 1 1 1 2h-1c-2 2-5 2-8 3v-5c-1-2-2-3-4-4z" class="G"></path><path d="M410 356c0 1 1 1 1 1h0 1c2 0 4 0 5 1h1c-2 2-5 2-8 3v-5z" class="C"></path><path d="M399 341h1c-1 1-1 3 0 3v2 3 1c1 1 1 1 2 1 1 1 1 2 3 2l1-1c2 1 3 2 4 4v5l-2 1-2 1c-1 0-2 1-3 1l-2-3h0-3l-1-1c1-1 1-2 1-3v-2-2-2-6-3h0l1-1z" class="o"></path><path d="M401 361c2 1 3 0 5 2-1 0-2 1-3 1l-2-3z" class="i"></path><path d="M406 352c2 1 3 2 4 4v5l-2 1c-1-1-1-3-2-4h0l1-1c0-2 0-2-1-3h1l-1-1h-1l1-1z" class="k"></path><path d="M399 341h1c-1 1-1 3 0 3v2 3 1c1 1 1 1 2 1 1 1 1 2 3 2h-1c0 2-1 3 0 4 0 2 0 2 1 3v1h-2l-1-1c-1 0-2-1-3-1 0-1 0-4-1-4v-2-2-6-3h0l1-1z" class="AC"></path><path d="M280 175h2 3 18 0c1 1 2 1 3 1h0l-2 2s0 1-1 2h-1c1 1 2 0 2 1 0 2 0 3-1 4-1 2-1 4-2 5-1 2-3 3-4 5-1 1-1 0-2 1v6l-1-7-4 1v8c0-2 1-6 0-8h-1c-1 0-2 0-4-1h-2-2v-3-7h0v-3l-1-4v-1-2z" class="AA"></path><path d="M299 184c1 0 1 0 1 1v1l-1 1-1-1v-1l1-1zm-9 8c1 1 2 1 2 3h-1l-2-2v-1h1z" class="t"></path><path d="M285 175h18c-2 1-5 0-7 1-1 0-3 3-4 4 2 2 3 3 5 4l-1 1c0-1-1-1-2-1 0 1-1 1-2 1 0 1 0 1-1 1h0 0l-1-1-1 2c-1-3 1-4 2-7v-4c-2 0-4 0-6-1h0z" class="i"></path><path d="M290 185l1-3h1c0 2-1 3-1 4l-1-1z" class="t"></path><path d="M280 175h2 3 0c2 1 4 1 6 1v4c-1 3-3 4-2 7 0 0-1 1-1 2s1 2 2 3h-1v1c0 1 0 1-1 1 0 1 1 1 2 2h-1c-1 0-2 0-4-1h-2-2v-3-7h0v-3l-1-4v-1-2z" class="m"></path><path d="M280 175h2c0 1 0 1-1 2 0 1 0 1-1 1v-1-2z" class="o"></path><path d="M285 184v-2l1-1c0 1 0 1 1 1 0 1-1 1-1 2v1 2 1c-1-1-1-1-2-1h-1v-1h1 1c0-1 1-1 0-2z" class="g"></path><path d="M281 185v-4h2l1 1c-1 0-1 1-1 1l2 1c1 1 0 1 0 2h-1-1v1h1c1 0 1 0 2 1 0 0 1 1 2 1 0 1 1 2 2 3h-1c-1-1-3-3-5-3-1 0-2-1-2-1-1-1-1-2-1-3h0 0z" class="i"></path><path d="M281 185h0c0 1 0 2 1 3 0 0 1 1 2 1 2 0 4 2 5 3v1c0 1 0 1-1 1 0 1 1 1 2 2h-1c-1 0-2 0-4-1h-2-2v-3-7z" class="o"></path><path d="M284 193h0c1 0 1 0 2-1l2 2c0 1 1 1 2 2h-1c-1 0-2 0-4-1l-1-1v-1z" class="i"></path><path d="M281 192c1-1 1-1 2-1l1 2v1l1 1h-2-2v-3z" class="g"></path><path d="M406 328v1l1-1-1-1c2 0 3 2 5 3h0c0 1 1 1 2 1-1-1-1-2-1-3l3 3 1-1h2 0 2c1 0 1 0 2-1h1c1 0 2-1 3-1h1 0 3l4-1h1v2l-1 2h0c0 1 0 3-1 4v3 1h0v1 5l1 1 1-2v4c0 1-1 1-1 1-1 0-1 0-1 1v1 3l-1 1h1v2 1l-1 1v-1h-2l-3 1-2 1h-1c-1 0-3-1-4-1l-1-1c0-1 0-1-1-2-2-2-3-5-5-8l-8-12v-1-1h0c0-1-1-1-1-2l-1-1c1-1 2-1 3-1v-2z" class="c"></path><path d="M417 339h1c1 1 3 1 4 1l-2 1v1h-1-1c-1 0-3-1-3-2 0 0 1-1 2-1z" class="o"></path><path d="M418 339c1 1 3 1 4 1l-2 1h-3v-1l1-1z" class="c"></path><path d="M405 334l2 2c1 1 1 2 2 3l5 7c2 4 4 6 4 10h0c-2-2-3-5-5-8l-8-12v-1-1z" class="J"></path><path d="M416 338h3c1 1 2 0 3 1h3l2-1h0c1 1 1 1 1 2 1 0 1 1 2 1l-4 4c-1 1-2 3-3 3 0-1-1 0-3-1h-1 0c1-1 2-1 3-1 1-1 1-1 1-2h-2v-1-1h-1v-1l2-1c-1 0-3 0-4-1h-1v-1h-1 0z" class="k"></path><path d="M422 340l3 1c1 1 1 1 1 3h-1-2-2v-1-1h-1v-1l2-1z" class="m"></path><path d="M421 342h0 3s1 0 1 1h-3-1v-1z" class="N"></path><path d="M406 328v1l1-1-1-1c2 0 3 2 5 3h0c0 1 1 1 2 1 0 0 1 0 1 1 2 2 5 1 7 3h-1c2 2 3 1 5 2 1 1 1 1 2 1l-2 1h-3c-1-1-2 0-3-1h-3-1-2c-2 1-2 0-4 1-1-1-1-2-2-3l-2-2h0c0-1-1-1-1-2l-1-1c1-1 2-1 3-1v-2z" class="AC"></path><path d="M405 334c1 0 3 1 4 1l2 2 4 1h-2c-2 1-2 0-4 1-1-1-1-2-2-3l-2-2h0z" class="K"></path><path d="M407 336l6 2c-2 1-2 0-4 1-1-1-1-2-2-3z" class="AD"></path><path d="M406 328v1l1-1-1-1c2 0 3 2 5 3h0c0 1 1 1 2 1 0 0 1 0 1 1 2 2 5 1 7 3h-1-3l-5-1c-2-2-4-2-5-4h0-1v2h-2l-1-1c1-1 2-1 3-1v-2z" class="G"></path><path d="M434 327h1v2l-1 2h0c0 1 0 3-1 4v3 1h-1l-2 2c-1 0-1-1-2-1 0-1 0-1-1-2h0c-1 0-1 0-2-1-2-1-3 0-5-2h1c-2-2-5-1-7-3 0-1-1-1-1-1-1-1-1-2-1-3l3 3 1-1h2 0 2c1 0 1 0 2-1h1c1 0 2-1 3-1h1 0 3l4-1z" class="E"></path><path d="M434 327h1v2l-1 2h0 0c-2 0-3 0-5 1-1 0-2-1-2-1-1 0-1 0-2-1h-2v-1c1 0 2-1 3-1h1 0 3l4-1z" class="U"></path><path d="M434 327h1v2h-2c-1 1-2 1-4 0v-1h-2 0 3l4-1z" class="E"></path><path d="M412 328l3 3c3 2 6 2 9 3h3c2 1 4 0 6 1v3 1h-1l-2 2c-1 0-1-1-2-1 0-1 0-1-1-2h0c-1 0-1 0-2-1-2-1-3 0-5-2h1c-2-2-5-1-7-3 0-1-1-1-1-1-1-1-1-2-1-3z" class="C"></path><path d="M427 338h1l2-1h1v1h2v1h-1l-2 2c-1 0-1-1-2-1 0-1 0-1-1-2z" class="N"></path><path d="M430 341l2-2h1 0v1 5l1 1 1-2v4c0 1-1 1-1 1-1 0-1 0-1 1v1 3l-1 1h1v2 1l-1 1v-1h-2l-3 1-2 1h-1v-1-3-2-1c-1-1-1-1-1-2v-3c1 0 2-2 3-3l4-4z" class="l"></path><path d="M426 345c1 1 1 1 2 1-1 1-2 2-2 4 0 1 0 2-1 3 0-1-1-2-2-2v-3c1 0 2-2 3-3z" class="P"></path><path d="M423 351c1 0 2 1 2 2 1 0 2 1 3 2s2 2 4 3h-2l-3 1-2 1h-1v-1-3-2-1c-1-1-1-1-1-2z" class="o"></path><path d="M424 356l1 1v3h-1v-1-3zm4-1c1 1 2 2 4 3h-2l-3 1c-1 0-1-2-2-3 1 0 2 0 2-1h1z" class="g"></path><path d="M430 341l2-2h1 0v1 5l1 1 1-2v4c0 1-1 1-1 1-1 0-1 0-1 1v1c-1-1-2-1-3-2 0 0 1 1 2 0h0l-2-3c-1 0-1-1-1-1h-1v1c-1 0-1 0-2-1l4-4z" class="r"></path><path d="M433 345l1 1 1-2v4c0 1-1 1-1 1-1 0-1 0-1 1v-5z" class="Q"></path><path d="M430 341l2-2h1 0v1c0 1-1 2-1 3l-2-1c0 1 1 2 2 2l-2 2c-1 0-1-1-1-1h-1v1c-1 0-1 0-2-1l4-4z" class="J"></path><path d="M264 174h0c1 0 1 0 2 1h0 1l-2-2h1c1 0 2 1 3 1h1v-1l1 1h2c1 1 1 1 2 1h0c1 0 2 1 2 1l1-1 2 2v1l1 4v3h0v7 3h2 2c2 1 3 1 4 1h1c1 2 0 6 0 8v2c-2 2-5 2-8 3l-5 2-1-4-1-1h0c0 1 0 2-1 3h0c-1 1-2 2-2 4h-2c-1-1-2-1-3-1h-1-4 0l-3 2c-2-1-3-2-4-2l-1-1v-1l-1-14 1-10v-2-1c1-1 1-1 2-1s2-1 3-2h-1c1-3 3-4 5-5l1-1z" class="H"></path><path d="M270 174h0c0 1-1 1-1 2h0v1l-1 1c-1 0-1 0-2-1h1c0-1 1-2 2-3h1z" class="K"></path><path d="M254 184c3 0 6 1 9 2h1c-3 0-7-1-10 0v-2z" class="G"></path><path d="M275 206c0 1 0 2-1 3h0-2-1-1-3c1 0 3-1 4-1 2-1 3-1 4-2z" class="d"></path><path d="M277 176l1-1 2 2v1l1 4c-1-1-1-2-1-3l-1-1-1 1c-2 0-2-1-3-2 1 0 2 0 2-1z" class="C"></path><path d="M254 210c1 0 1 0 2 1l2-1c1 1 2 1 4 2l-3 2c-2-1-3-2-4-2l-1-1v-1z" class="E"></path><path d="M275 206l3-2c1 1 2 2 2 3s0 1 1 1v-3h0c0 1 0 3 1 4l-5 2-1-4-1-1z" class="L"></path><path d="M270 173l1 1h2c1 1 1 1 2 1h0c1 0 2 1 2 1 0 1-1 1-2 1h-1v1h-1v-1h-1v1h-1l-2-1v-1h0c0-1 1-1 1-2h0v-1z" class="c"></path><path d="M270 173l1 1h2c1 1 1 1 2 1h0-2c-1 0-1 2-2 1h-2 0c0-1 1-1 1-2h0v-1z" class="m"></path><path d="M272 209h2c-1 1-2 2-2 4h-2c-1-1-2-1-3-1h-1-4c2-2 3-2 5-3h3 1 1z" class="C"></path><path d="M272 209h2c-1 1-2 2-2 4h-2c-1-1-2-1-3-1 2-1 3-2 5-3z" class="AA"></path><path d="M264 174h0c1 0 1 0 2 1h0 1l-2-2h1c1 0 2 1 3 1-1 1-2 2-2 3h-1l-7 3h-1c1-3 3-4 5-5l1-1z" class="g"></path><path d="M281 195h2 2c2 1 3 1 4 1h1c1 2 0 6 0 8v2c-2 2-5 2-8 3-1-1-1-3-1-4h0v-10z" class="u"></path><path d="M281 195h2 2c2 1 3 1 4 1h0-5-1 0v2c-1 0-1 1-2 1v6h0v-10z" class="z"></path><path d="M433 350c0-1 0-1 1-1 0 0 1 0 1-1v11l1 1c1 0 3 0 5 1 6 0 12 0 17 1h0c0 2-1 3-2 4h2c1 1 1 1 1 2h1v4 1c0 1 0 2-1 3 1 1 0 2 0 3h0v1 3h1l-1 2v-1h0c-3 1-4 4-7 5v3h1l-4 1c-2-1-3-1-5 0h-4-2-1l-2 1h-1 0c-1 1-2 1-3 2h-3-3v3 2h0v1c0 1 0 2-1 3-1-1-2-3-3-3h-1c0-1-1-2-2-3h0l-1-1h-1c-4-3-6-6-9-10l-11-13-5-6 1-2h2v1l1-1v-4-4l1 1h1l1 1h3 0l2 3c1 0 2-1 3-1l2-1 2-1c3-1 6-1 8-3h1l1 1c1 0 3 1 4 1h1l2-1 3-1h2v1l1-1v-1-2h-1l1-1v-3-1z" class="Q"></path><path d="M417 378h3s0 1 1 1l-1 1h-2c-1 0-1 0-1-1v-1z" class="H"></path><path d="M395 363l1 3v5c0 1 1 2 1 3h-1l-1-1c0-1 0-2-1-2 0-2 1-1 1-2v-2-4z" class="K"></path><path d="M395 359l1 1h1l1 1h0l-1 1v1l1 1v2h-2l-1-3v-4z" class="t"></path><path d="M391 369l1-2h2v1l1-1v2c0 1-1 0-1 2 1 0 1 1 1 2l1 1v1l-5-6z" class="h"></path><path d="M417 398l-1-1h0l2-1v-1c1 0 1 0 2 1l1-1c1 0 2 0 3 1h-2l-1 1h0v2h-2l-1-1v1l-1-1z" class="I"></path><path d="M432 394l-1-1v-2c0-2-2-5-2-7 1 1 1 3 2 3 1 1 1 1 2 1l1-1v1h0 1l-3 6z" class="n"></path><path d="M398 361h3 0l2 3-2 1c-1 0-2 0-3 1v-2l-1-1v-1l1-1h0z" class="q"></path><path d="M398 366v-2l-1-1v-1l1-1v1h0l3 3h0c-1 0-2 0-3 1z" class="g"></path><path d="M418 399v-1l1 1h2v-2h0l1-1h2v1h0-1-1c1 1 1 1 1 2h1 1v2h0v1c0 1 0 2-1 3-1-1-2-3-3-3h-1c0-1-1-2-2-3h0z" class="h"></path><path d="M433 350c0-1 0-1 1-1 0 0 1 0 1-1v11l1 1c1 0 3 0 5 1 6 0 12 0 17 1h0c0 2-1 3-2 4h2c1 1 1 1 1 2h1v4 1c0 1 0 2-1 3 1 1 0 2 0 3h0v1 3h1l-1 2v-1h0c-3 1-4 4-7 5v3h1l-4 1c-2-1-3-1-5 0h-4-2-1l-2 1h-1 0-2l3-6h-1 0v-1l-3-7-1-2-1-1c-1-1-1-2 0-3h0l2-2h0c-2-3-2-6-2-10 0-1 0-1 1-2v-1-1h2v1l1-1v-1-2h-1l1-1v-3-1z" class="W"></path><path d="M434 360v1l1 1v2h-1v-4z" class="T"></path><path d="M438 383c0-1 1-2 2-2l1 2c-1 0-1 1-2 2l-1-2z" class="M"></path><path d="M439 366h-2v-1-1l4 1h1l-1 1h-2z" class="b"></path><path d="M437 393c1-1 2-3 4-4v1c0 1 0 2-1 3h-2-1z" class="E"></path><path d="M442 365c1 0 3 1 5 1l-1 2-7-2h2l1-1z" class="H"></path><path d="M447 366c1 0 1 0 2 1h4v1h-1l-2 1c-1 0-2 0-4-1l1-2z" class="C"></path><path d="M438 383l-2-3c-1-1-1-1-1-2h1c2 0 3 1 4 3-1 0-2 1-2 2z" class="a"></path><path d="M437 373v-2c0-1 0-1 1-2 1 0 2-1 3 0v1l-1 1h0l-1 2-1-1h-1v1z" class="N"></path><path d="M444 383c-1-2-4-5-4-6h1c1 0 2 1 3 2s1 2 2 3l-2 2v-1z" class="G"></path><path d="M441 383l1 2h1c0 2 1 3 2 4v1l-1 1c-2-2-3-4-5-6 1-1 1-2 2-2z" class="C"></path><path d="M435 388c1-1 1-3 3-3h1c0 3-3 7-5 8l1 1h-1 0-2l3-6z" class="M"></path><path d="M444 383v1c1 2 5 5 5 8v1c-2-1-3-1-5 0v-2l1-1v-1c-1-1-2-2-2-4h-1l1-1s0-1 1-1z" class="T"></path><path d="M433 350c0-1 0-1 1-1 0 0 1 0 1-1v11l1 1h-2v4l-1 1c-1-1-1-4-2-6h-1v-1h2v1l1-1v-1-2h-1l1-1v-3-1z" class="H"></path><path d="M444 379c1 1 2 2 3 2l2 1c0 1 1 2 2 3l1 2v1 1h0v3h1l-4 1v-1c0-3-4-6-5-8l2-2c-1-1-1-2-2-3h0z" class="C"></path><path d="M444 379c1 1 2 2 3 2l2 1c0 1 1 2 2 3l1 2v1 1h-1c-2-1-4-5-5-7-1-1-1-2-2-3h0z" class="l"></path><path d="M429 362c0-1 0-1 1-2 0 1 0 1 1 1 1 2 1 4 1 6 1 1 2 3 2 5 0 0-1 0-1 1h0c1 1 1 1 1 2h1c0 1 0 1-1 2v2c-1 1-2 1-3 1l-1-2-1-1c-1-1-1-2 0-3h0l2-2h0c-2-3-2-6-2-10z" class="n"></path><path d="M432 367c1 1 2 3 2 5h-1c-1-2-2-3-3-5h2z" class="I"></path><path d="M429 362c0-1 0-1 1-2 0 1 0 1 1 1 1 2 1 4 1 6h-2c0-1 0-3-1-4v-1h0z" class="e"></path><path d="M431 372c1 2 1 5 1 7h-1l-1-1-1-1c-1-1-1-2 0-3h0l2-2h0z" class="I"></path><path d="M441 361c6 0 12 0 17 1h0c0 2-1 3-2 4s-2 2-3 2v-1h-4c-1-1-1-1-2-1-2 0-4-1-5-1h-1v-1h-1c-1-1-2-1-2-2h1 1l1-1z" class="D"></path><path d="M458 362h0c0 2-1 3-2 4s-2 2-3 2v-1h-4 2c2-1 5-3 7-5z" class="M"></path><path d="M441 364h-1c-1-1-2-1-2-2h1 1c2 0 8 0 9 1l-1 1c-2 1-5 0-7 0z" class="U"></path><path d="M450 369l2-1v1 1l1 1-1 1v3l-1 1h1l-1 1c0 1 1 1 1 1l-3 4-2-1c-1 0-2-1-3-2h0c-1-1-2-2-3-2l1-1v-1c-2-1-4-1-5-2v-1h1l1 1 1-2c2 1 5 1 7 1 2-1 3-1 4-2l-1-1z" class="h"></path><path d="M442 375c2 0 3 1 4 2h1l-3 1v1h0c-1-1-2-2-3-2l1-1v-1z" class="B"></path><path d="M452 372v3l-1-1c-4 2-8 0-11-1 3 0 9 1 12 0v-1z" class="P"></path><path d="M451 376h1l-1 1c0 1 1 1 1 1l-3 4-2-1c-1 0-2-1-3-2v-1l3-1h-1c2 0 3 0 5-1z" class="J"></path><path d="M447 377l2 1h0c0 1-1 2-2 3-1 0-2-1-3-2v-1l3-1z" class="h"></path><path d="M456 366h2c1 1 1 1 1 2h1v4 1c0 1 0 2-1 3 1 1 0 2 0 3h0v1 3h1l-1 2v-1h0c-3 1-4 4-7 5h0v-1-1l-1-2c-1-1-2-2-2-3l3-4s-1 0-1-1l1-1h-1l1-1v-3l1-1-1-1v-1-1h1c1 0 2-1 3-2z" class="s"></path><path d="M452 378h1v1c-1 1-1 2-1 3s-1 2-1 3c-1-1-2-2-2-3l3-4z" class="e"></path><path d="M452 388h0c1-2 1-3 3-4 1-2 2-3 4-5v1 3h1l-1 2v-1h0c-3 1-4 4-7 5h0v-1z" class="a"></path><path d="M456 366h2c1 1 1 1 1 2l-1 2h0 0 1 0v3l-1 1c1 1 1 1 1 2-1 2-2 4-4 5h0c-1-1 0-3 0-4l1-1h0c-1 0-2 2-3 3v-1h-1s-1 0-1-1l1-1h-1l1-1v-3l1-1-1-1v-1-1h1c1 0 2-1 3-2z" class="AC"></path><path d="M456 366h2c-1 2-2 2-2 4-1 2-3 3-2 5l1-1c0 2-1 2-2 3h-1v-1h-1l1-1v-3l1-1-1-1v-1-1h1c1 0 2-1 3-2z" class="l"></path><path d="M452 369h1l1 1-1 1-1-1v-1z" class="e"></path><path d="M332 168l1-1h1c1 1 2 1 3 1 0 1 0 2-1 3 2 0 2-1 3-1l1 1s0 1 1 1c0-1 0-1 1-1h0c0 1-1 1 0 2h0 5 0 4c5 0 11 1 16 0l1 1v-1-3c-1 0-1-1-1-1 1-1 2-1 2-1l1 1h2l1 1v1l-1 1h0 2v-1-1l1 2c1 1 4 1 6 1s7 0 9-1c1 4 3 9 3 13v1 2c1 3 1 14 1 18v15 26c0 3-1 8 0 12 0 0 0 1 1 1h12v1 6l1 4h1v-1s1 0 2-1v1h0v1c-1 1-2 3-2 4h0c1 2 2 1 3 2l2-1v-1 1c0-1 0-1 1-1h8c2 0 3-1 5 0v2 2h0 0c1 0 2 1 3 1s2 1 3 2c1-1 1-1 2-1h0l-1-2c1 0 1-1 2-1l2-2c0 1 0 2 1 2l1-2v-1l1 1h0c0 1 1 2 2 3h-2v1 3 1 3 1c0 1 0 1-1 2 1 2 1 1 0 3v3h1v1c-1 2-2 3-3 5h1 1c0 1-1 2-2 4h0c-1 1-2 1-2 3h0c1 0 1 0 1 1h0c-1 1-1 1-1 2 0 2-1 2-2 3h-4l-1 1 1 2s-1 1-1 2h0 0c1 0 2 1 2 1v1c0 1-1 2-1 2 0 1 0 1 1 2 1 0 1 0 2 1l-4 1h-3 0-1c-1 0-2 1-3 1h-1c-1 1-1 1-2 1h-2 0-2l-1 1-3-3c0 1 0 2 1 3-1 0-2 0-2-1h0c-2-1-3-3-5-3l1 1-1 1v-1l-3-4v-1c0-2-4-5-5-8l-3-3h-1v3-3h-1c-1 0-2 0-2 1h-1c-1 0-1 0-1 1h-1-1v-1c1 0 1 0 1-1h3c0-1 1-1 2-1 1-1 1-1 1-2h-2-1l-1-1c-1 0-2 0-3 1-1-1-1-2-1-3 0-12 0-24-1-36 0-3 0-6-1-8 0-3-2-7-2-9 0-1 0-2-1-3 0-1-1-3-1-4-1-3-3-6-5-9v-2c-2-3-4-6-6-8-10-12-25-19-40-22-4-1-9-1-13-1 1-3 2-5 4-7h-1c0-1 0-1 1-1v-1c1-1 1-1 1-2 1-1 1-1 1-3v-1c1-1 1-3 1-4v-8c0-1-1-1-2-2l2-2h0c1 0 1-1 2-1 0-1-1-2-1-2l1-1 1-1h0l1-1 2 1h3z" class="H"></path><path d="M368 200h1v1l-1 1-1-1 1-1zm16 19c1 0 1 0 2 1h0l-1 2h-1-1c1-1 1-2 1-3z" class="E"></path><path d="M395 264h2c0 1 0 1-1 2v1-1c-1 0-1-1-1-2zm-4 44v-1l2-1 2 2-1 1h-2l-1-1zm-16-111h2v3l-1 1v-1c-1-1-1-2-1-3z" class="I"></path><path d="M391 287h1v1c0 1 0 2-1 3 0 0 0-1-1-1 0-2 1-2 1-3zm0 21h-3v-1l1-2c1 0 3 0 4 1l-2 1v1zm20-1l-1 2h-1c-1 0-1-1-2-1 0-2 0-4 1-5v3c0 1 1 1 2 1h0 1zm-25-84h1c1 1 1 2 1 3s-1 1-2 2h-2v-1c1-1 2-1 3-2-1 0-1 0-1-1v-1z" class="E"></path><path d="M391 294h1c1 1 2 1 3 3l-2 1h0c-1-1-2-2-3-2h0l-1-1 2-1z" class="L"></path><path d="M407 278h1v1h1c1 2 1 3 0 5h0l-2-1v-5zm-35-69h1c1 1 3 1 4 1 0 1 1 2 1 2v2c-1 0-2-1-3-1 0 0 0-1-1-1l-1-1-1-2z" class="E"></path><path d="M407 267l1 4h1v-1s1 0 2-1v1h0v1c-1 1-2 3-2 4h0 0l-1-1h0v4h-1v-11z" class="AB"></path><path d="M357 200h1 1v1l-1 1h-1c-1 0-3 1-4 2h0v1h-1 0v-2c1-1 2-2 2-3h3 0z" class="E"></path><path d="M384 244c-1-1-2-5-3-6 3 2 5 3 7 5v1h-1l-2-1c0 2 0 3-1 3v-2z" class="T"></path><path d="M412 298l1 1-1 1c0 1-2 3-2 4h0v1c-1 0-1 0-1 1h1v1h0c-1 0-2 0-2-1v-3c1-2 2-3 4-5z" class="r"></path><path d="M388 243l1 1c0 2-5 7-7 9 0-1 0-2-1-3h1 0 1c0-2 0-4 1-6v2c1 0 1-1 1-3l2 1h1v-1z" class="S"></path><path d="M364 190h1c0 1 1 1 1 2 1 2 2 2 3 3s1 1 2 0l1 1-2 2h0c-1-1-2-2-4-3h0l-1 1c-1 0-2 1-3 2 0 1 0 1-1 2l-1-1v-1c0-1 0-1 1-2 1 0 2-1 3-1 1-2 0-3 0-5z" class="I"></path><path d="M375 237c2-1 3 0 4 0 2 1 3 5 4 7 0 2-1 4-1 6h-1c0-1-1-3-1-4-1-3-3-6-5-9z" class="D"></path><path d="M408 278v-4h0l1 1h0c1 2 2 1 3 2l2-1v-1 1l1 1c-1 1-1 1 0 2v1 2c-1 1-2 1-3 3h0l-1-1c-1 0-1 0-1 1l-1-1c1-2 1-3 0-5h-1v-1z" class="S"></path><path d="M414 276l1 1c-1 1-1 1 0 2v1 2c-1 1-2 1-3 3h0l-1-1c0-1 1-2 1-4 0 0-1 0-1-1h3v-1-2z" class="k"></path><path d="M367 176s0-1-1-1v-1h0c1 0 2 0 4 1 8 2 15 7 23 11v2l-26-12z" class="D"></path><path d="M367 169c1-1 2-1 2-1l1 1h2l1 1v1l-1 1h0 2v-1-1l1 2c1 1 4 1 6 1s7 0 9-1c1 4 3 9 3 13v1c-8-4-15-9-23-11l-2-1v-1-3c-1 0-1-1-1-1z" class="Y"></path><path d="M367 169c1-1 2-1 2-1l1 1h2l-1 1v2c-1 0-2 0-3 1v-3c-1 0-1-1-1-1z" class="U"></path><path d="M404 310c1 0 1 0 1 1 1 0 2 0 3-1v1s0 1 1 1c1 1 1 2 2 3l1 3 6 11v1h-2l-1 1-3-3c0 1 0 2 1 3-1 0-2 0-2-1h0c-2-1-3-3-5-3l1 1-1 1v-1l-3-4v-1c0-2-4-5-5-8l2 2c0 1 1 1 1 2 1-1 1-1 1-2l-1-1c-1-2-1-3-3-5h0 2 1c1-1 2-1 3-1z" class="AD"></path><path d="M404 310c1 0 1 0 1 1l2 3-2-1-1-2v-1z" class="K"></path><path d="M405 311c1 0 2 0 3-1v1s0 1 1 1c0 1 0 2-1 3 0-1-1-1-1-1l-2-3z" class="E"></path><path d="M409 312c1 1 1 2 2 3 0 1-1 2 0 3l-1 1c-1-1-1-2-2-4 1-1 1-2 1-3z" class="d"></path><path d="M411 315l1 3 6 11v1h-2 0 1c0-1-1-2-2-3-1-3-3-5-5-8l1-1c-1-1 0-2 0-3z" class="C"></path><path d="M400 311h1c1-1 2-1 3-1v1h-1c1 3 4 6 6 9 3 3 5 7 7 10h0l-1 1-3-3c0-1-4-5-5-6l-1-2-2-2-2-3v-1l-1-1-1-2z" class="G"></path><path d="M398 315l2 2c0 1 1 1 1 2 1-1 1-1 1-2l-1-1c-1-2-1-3-3-5h0 2l1 2 1 1v1l2 3 2 2 1 2c1 1 5 5 5 6s0 2 1 3c-1 0-2 0-2-1h0c-2-1-3-3-5-3l1 1-1 1v-1l-3-4v-1c0-2-4-5-5-8z" class="M"></path><path d="M347 173h4c5 0 11 1 16 0l1 1 2 1c-2-1-3-1-4-1h0v1c1 0 1 1 1 1l-3-1-4 7c-1 3-2 7-4 10 0 1-1 3-2 4h-12v-17-6h5 0z" class="n"></path><path d="M347 179h3c0 1-1 2-2 2 0 1 0 1-1 1h-1l1-1v-1-1z" class="u"></path><path d="M354 175h2c0 1 0 1 1 1l-1 1c-1 0-2 0-4 1v-1c1 0 2-1 2-2h0z" class="l"></path><path d="M347 173h4c5 0 11 1 16 0l1 1 2 1c-2-1-3-1-4-1h0v1c1 0 1 1 1 1l-3-1-4 7v-2c0-1 0-2-1-2l1-1h1c0 1 0 1-1 1l1 1c0-1 1-3 1-4 1-1 1 0 1-1-1 0-1 1-2 0h-3c-1 1-1 1-1 2-1 0-1 0-1-1h-2l-1-1c-1 0-1 1-2 1v-1h0-2v1h-1v-1s-1 0-1-1h0z" class="J"></path><path d="M332 168l1-1h1c1 1 2 1 3 1 0 1 0 2-1 3 2 0 2-1 3-1l1 1s0 1 1 1c0-1 0-1 1-1h0c0 1-1 1 0 2h0v6 17l-6-1-10 1-1-2c-1-1 0-4 0-6h0v-1l-1-4h0v1l-1 1v-8c0-1-1-1-2-2l2-2h0c1 0 1-1 2-1 0-1-1-2-1-2l1-1 1-1h0l1-1 2 1h3z" class="n"></path><path d="M323 177c0-1-1-1-2-2l2-2 3 1-1 20c-1-1 0-4 0-6h0v-1l-1-4h0v1l-1 1v-8z" class="b"></path><path d="M323 177l1-2h1v13h0v-1l-1-4h0v1l-1 1v-8z" class="X"></path><path d="M332 168l1-1h1c1 1 2 1 3 1 0 1 0 2-1 3h0v3h0-7-3l-3-1h0c1 0 1-1 2-1 0-1-1-2-1-2l1-1 1-1h0l1-1 2 1h3z" class="U"></path><path d="M332 168l1-1h1c1 1 2 1 3 1 0 1 0 2-1 3h0-1c-1 0-1 0-2-1 0 0-1-1-1-2z" class="z"></path><path d="M332 168l1-1h1c0 2 0 2-1 3 0 0-1-1-1-2z" class="r"></path><path d="M326 168l1-1 2 1 1 1c0 1 0 1-1 2v3h-3l-3-1h0c1 0 1-1 2-1 0-1-1-2-1-2l1-1 1-1h0z" class="F"></path><path d="M326 168c0 1 0 2 1 3l-2 1c0-1-1-2-1-2l1-1 1-1h0z" class="C"></path><path d="M327 171c1 0 2 1 2 0v3h-3l-3-1h0c1 0 1-1 2-1l2-1zm9 0c2 0 2-1 3-1l1 1s0 1 1 1c0-1 0-1 1-1h0c0 1-1 1 0 2h0v6 17l-6-1 1-21h-1 0v-3h0z" class="L"></path><path d="M340 171s0 1 1 1c0-1 0-1 1-1h0c0 1-1 1 0 2h0v6c-1-1-1-3-1-4h-1c0-1-1-2-1-2l1-2z" class="G"></path><path d="M336 171c2 0 2-1 3-1l1 1-1 2-1 1h-2v-3h0z" class="Q"></path><path d="M414 276c0-1 0-1 1-1h8c2 0 3-1 5 0v2 2h0 0c1 0 2 1 3 1s2 1 3 2l1 2 1 1v2l1 2c-1 0-2 1-2 1h-2l-1 1v1c-1 0-1 0-2 1h-2v2 2h0l-1 1h0v1 1c-1-1-1-1-2-1l-1-1h0-7-2-1c0 1-1 1-1 1h0l-1-1c1-1 1-4 3-5-2-1-2-1-2-2h-1c-1 1-2 3-3 5l-1-1c-1-1-1-2-1-2v-1-2c0-2 1-4 2-6h0l1 1c0-1 0-1 1-1l1 1h0c1-2 2-2 3-3v-2-1c-1-1-1-1 0-2l-1-1z" class="p"></path><path d="M429 290h4l-1 1v1c-1-1-2-1-3-1v-1z" class="o"></path><path d="M407 290l1 1h0c1-2 3-4 4-6v2h1c-1 3-3 5-5 6v2c-1-1-1-2-1-2v-1-2z" class="G"></path><path d="M429 286h0l7 1c-1 1 0 1-1 1-2 1-5 0-7 0h0c-1 0-6 1-7 0h0 5 2l1-2z" class="h"></path><path d="M409 284h0l1 1c0-1 0-1 1-1l1 1h0c-1 2-3 4-4 6h0l-1-1c0-2 1-4 2-6z" class="O"></path><path d="M413 291c0-1 0-1 1-2h1 11c-2 0-4 0-6 1h-2c1 0 1 0 1 1l1 1h-2-2l-1 1c-2-1-2-1-2-2z" class="E"></path><path d="M416 292l1-1h2l1 1h-2-2z" class="x"></path><path d="M426 289h2v3c-1 1 0 1-1 0h-9 2l-1-1c0-1 0-1-1-1h2c2-1 4-1 6-1z" class="X"></path><path d="M415 285c2 0 7-1 9 0 1 1 2 2 2 3h-5 0c-2 0-5 1-7 0v-1c1-1 1-1 1-2z" class="x"></path><path d="M415 285h0v-1c1-1 1 0 2 0h13 2c1 1 2 1 3 1v-1l1 1v2l-7-1h0l-1 2h-2c0-1-1-2-2-3-2-1-7 0-9 0z" class="F"></path><path d="M424 285c1 0 3-1 4 0l1 1-1 2h-2c0-1-1-2-2-3z" class="X"></path><path d="M426 278c1-1 1-1 2-1v2h0 0c1 0 2 1 3 1s2 1 3 2l1 2v1c-1 0-2 0-3-1h-2-13l2-1v-1l1-1c1 0 2-1 3-2 1 0 2-1 3-1z" class="Q"></path><path d="M426 278c1-1 1-1 2-1v2c0 1 0 1-1 2-1 0-1 0-2-1v-1c1 0 1 0 2-1h-1z" class="w"></path><path d="M414 276c0-1 0-1 1-1h8c2 0 3-1 5 0v2c-1 0-1 0-2 1-1 0-2 1-3 1-1 1-2 2-3 2l-1 1v1l-2 1c-1 0-1-1-2 0v1h0c0 1 0 1-1 2h0-1-1v-2h0 0c1-2 2-2 3-3v-2-1c-1-1-1-1 0-2l-1-1z" class="x"></path><path d="M428 275v2c-1 0-1 0-2 1-1 0-2 1-3 1v-1-2h0l1-1h4z" class="v"></path><path d="M412 285h0c1-2 2-2 3-3v1h1c1-1 2-1 3-1v1l-2 1c-1 0-1-1-2 0v1h0c0 1 0 1-1 2h0-1-1v-2h0z" class="b"></path><path d="M420 281h-1c-1-1-1-1-2-1h0c0-1-1-2-1-3h1c2-1 1 0 3 0h2l1 1v1c-1 1-2 2-3 2zm8 8l1 1v1c1 0 2 0 3 1-1 0-1 0-2 1h-2v2 2h0l-1 1h0v1 1c-1-1-1-1-2-1l-1-1h0-7-2-1c0 1-1 1-1 1h0l-1-1c1-1 1-4 3-5l1-1h2 9c1 1 0 1 1 0v-3z" class="X"></path><path d="M428 289l1 1v1c1 0 2 0 3 1-1 0-1 0-2 1h-2v2-1c-4-1-9-1-13 0-1 1-1 1-1 3h4c2 0 6 0 9 1h0v1 1c-1-1-1-1-2-1l-1-1h0-7-2-1c0 1-1 1-1 1h0l-1-1c1-1 1-4 3-5l1-1h2 9c1 1 0 1 1 0v-3z" class="t"></path><path d="M440 278l1-2v-1l1 1h0c0 1 1 2 2 3h-2v1 3 1 3 1c0 1 0 1-1 2 1 2 1 1 0 3v3h1v1c-1 2-2 3-3 5h1 1c0 1-1 2-2 4h0c-1 1-2 1-2 3h0c1 0 1 0 1 1h0c-1 1-1 1-1 2 0 2-1 2-2 3h-4l-1 1 1 2s-1 1-1 2h0 0c1 0 2 1 2 1v1c0 1-1 2-1 2 0 1 0 1 1 2 1 0 1 0 2 1l-4 1h-3 0-1c-1 0-2 1-3 1h-1c-1 1-1 1-2 1h-2 0v-1l-6-11-1-3c-1-1-1-2-2-3-1 0-1-1-1-1h3l1-2v-2h-1-1v-1h-1c0-1 0-1 1-1v-1h0c0-1 2-3 2-4l1-1h0s1 0 1-1h1 2 7 0l1 1c1 0 1 0 2 1v-1-1h0l1-1h0v-2-2h2c1-1 1-1 2-1v-1l1-1h2s1-1 2-1l-1-2v-2l-1-1-1-2c1-1 1-1 2-1h0l-1-2c1 0 1-1 2-1l2-2c0 1 0 2 1 2z" class="m"></path><path d="M425 299c1 0 1 0 2 1v1h-3 0c1-1 1-2 1-2z" class="X"></path><path d="M430 308h-1c1 0 1-1 1-1 0-1 1-1 2-1v1l-1 1h-1z" class="K"></path><path d="M441 296h1v1c-2 1-3 3-4 5l-2-1 1-1c1-1 2-1 2-2s1-2 2-2z" class="c"></path><path d="M412 300l2 1c0 2 0 2-1 3s-2 0-3 0h0c0-1 2-3 2-4z" class="Q"></path><path d="M436 301l2 1h-1v1c0 1-3 3-4 4 0-1 0-2 1-2 0-1-1-1 0-2l2-2z" class="AA"></path><path d="M438 302c1-2 2-4 4-5-1 2-2 3-3 5l-3 3c0 1-2 2-3 3l-1 1v-1h-1l1-1h1c1-1 4-3 4-4v-1h1z" class="g"></path><path d="M415 303c2-1 9-1 11 0h1c-1 1-1 2-1 3h-1l-1-1s-1-1-2-1h0-4c-1-1-2-1-3-1z" class="Q"></path><path d="M426 303h1c-1 1-1 2-1 3h-1l-1-1s-1-1-2-1c1-1 2-1 4-1z" class="V"></path><path d="M425 306h1 0 2 1c0 1-2 2-2 4l-1 1h-1v4l-1-1c-1-1-1-2-1-4v-1l1-1h0l1-2z" class="N"></path><path d="M424 308h0c1 0 1 1 1 2h0-2v-1l1-1z" class="C"></path><path d="M414 298h1 2 7 0l1 1s0 1-1 2h0-3-6c-1-1-2-1-2-2 0 0 1 0 1-1z" class="V"></path><path d="M414 298h1 2 7 0l-3 1-1 1h-2c-1 0-1-1-2-2h-2z" class="U"></path><path d="M415 303c1 0 2 0 3 1h4 0c1 0 2 1 2 1l1 1-1 2h0l-1 1h-4 0-1l-2-1c-1 0-1-1-1-2h-1c0-2 1-2 1-3z" class="X"></path><path d="M416 308h0c1-1 1-1 2-1 1 1 1 1 2 1l2-2 1-1v1 1l1 1-1 1h-4 0-1l-2-1z" class="Z"></path><path d="M440 302h1c0 1-1 2-2 4h0c-1 1-2 1-2 3h0c1 0 1 0 1 1h0c-1 1-1 1-1 2 0 2-1 2-2 3h-4l-1 1 1 2s-1 1-1 2h0 0l-1 1-1-1h-1l-1-1h0-1c-1-1-1-1-1-2v-2-1l1 1v-4 2l1 1c0-1 1-1 1-2 1 1 1 1 2 1v-1c0-2 1-2 1-3v-1h1 1v1l1-1c1-1 3-2 3-3l3-3h1z" class="q"></path><path d="M425 313l1 1c1 1 1 2 1 3h0c-1 0-1 1-1 1v1h0c-1-1-1-2-1-3v-3z" class="o"></path><path d="M425 315v-4 2 3c0 1 0 2 1 3h-1c-1-1-1-1-1-2v-2-1l1 1z" class="c"></path><path d="M424 314l1 1v2h-1v-2-1z" class="k"></path><path d="M434 309h1v3h1 1c0 2-1 2-2 3h-4l-1 1s0 1-1 1v-1-1h2c0-1 0-2 1-2v1h1l2-2c0-1 0-2-1-3z" class="g"></path><path d="M440 302h1c0 1-1 2-2 4h0c-1 1-2 1-2 3h0c1 0 1 0 1 1h0c-1 1-1 1-1 2h-1-1v-3h-1c-1 1-1 2-2 2h0v-2l1-1c1-1 3-2 3-3l3-3h1z" class="t"></path><path d="M439 302h1c-1 3-3 5-6 7l-1-1c1-1 3-2 3-3l3-3z" class="N"></path><path d="M440 278l1-2v-1l1 1h0c0 1 1 2 2 3h-2v1 3 1 3 1c0 1 0 1-1 2 1 2 1 1 0 3h0l-2 2h-1c0 1-1 2-1 2l-2 1-1 1h0c-1 2-4 5-6 5h0c0-1 1-2 2-3h-1l-1-3v-1h0v-2-2h2c1-1 1-1 2-1v-1l1-1h2s1-1 2-1l-1-2v-2l-1-1-1-2c1-1 1-1 2-1h0l-1-2c1 0 1-1 2-1l2-2c0 1 0 2 1 2z" class="f"></path><path d="M433 290h2c0 1 0 1-1 1v1l-2-1 1-1z" class="g"></path><path d="M434 299h-2l-1-1v-1l1-1 1 1c1 1 1 1 2 1h0l-1 1z" class="q"></path><path d="M434 282c1-1 1-1 2-1l2 3v4l-1 1-1-2v-2l-1-1-1-2z" class="u"></path><path d="M432 291l2 1c-1 0-1 1-1 1 0 1-2 2-2 2 0 1-1 2-2 3h-1v-1h0v-2-2h2c1-1 1-1 2-1v-1z" class="AG"></path><path d="M428 297l2-2-1-1h1l1 1c0 1-1 2-2 3h-1v-1h0z" class="g"></path><path d="M437 297c-1 0-1 0-1-1h-1s-1-1-1-2l1-1s1-1 1-2h2 1l2 2-2 2h-1c0 1-1 2-1 2z" class="F"></path><path d="M438 295h-1 0v-2h1c0 1 0 1 1 2h-1z" class="f"></path><path d="M440 278l1-2v-1l1 1h0c0 1 1 2 2 3h-2v1 3 1 3-3h-1v1l-1 1c-1 0-1 0-1-1h1v-1h-2l-2-3h0l-1-2c1 0 1-1 2-1l2-2c0 1 0 2 1 2z" class="F"></path><path d="M440 280h1v1 2h-1c-1 0-1-1-2-2 1 0 1-1 2-1z" class="m"></path><path d="M440 278l1-2v-1l1 1h0c0 1 1 2 2 3h-2l-1 1h-1c-1 0-1 1-2 1h0-2l-1-2c1 0 1-1 2-1l2-2c0 1 0 2 1 2z" class="t"></path><path d="M439 276c0 1 0 2 1 2l-1 1h-1l-1-1 2-2zm1 4c0-1 1-3 2-4 0 1 1 2 2 3h-2l-1 1h-1z" class="AD"></path><path d="M410 306h2c1 0 1-1 1-1l1 1h1c0 1 0 2 1 2l2 1h1 0 4v1c0 2 0 3 1 4v1 2c0 1 0 1 1 2h1 0l1 1h1l1 1 1-1c1 0 2 1 2 1v1c0 1-1 2-1 2 0 1 0 1 1 2 1 0 1 0 2 1l-4 1h-3 0-1c-1 0-2 1-3 1h-1c-1 1-1 1-2 1h-2 0v-1l-6-11-1-3c-1-1-1-2-2-3-1 0-1-1-1-1h3l1-2v-2h-1-1v-1z" class="o"></path><path d="M427 327l2-2 1 1h0v1 1h-3v-1z" class="k"></path><path d="M425 326v-1c1 1 2 1 2 2v1h0-1l-1-2z" class="i"></path><path d="M426 319h0l1 1h1l1 1-2 4c-1-1-2-2-2-3h-1v-2l1-1h1z" class="R"></path><path d="M426 319h0l1 1-2 2h-1v-2l1-1h1z" class="g"></path><path d="M419 309h4v1c0 2 0 3 1 4v1 2c0 1 0 1 1 2l-1 1v2h0-1c-1-2-2-5-2-7l-2-6z" class="c"></path><path d="M424 320c-1-2-1-3-2-4v-1h0 2v2c0 1 0 1 1 2l-1 1z" class="m"></path><path d="M419 309h4v1l-1-1v1s0 1-1 2h0c1 1 1 1 1 2l-1 1-2-6z" class="K"></path><path d="M418 309h1-1c0 2 1 4 2 6 0 1 0 2 1 2 1 3 2 6 4 9l1 2c-1 0-2 1-3 1h-1c0-2-2-4-2-6l-3-7c-1-1-1-2 0-3 0-1 0-1-1-1l1-1c0-1 0-1 1-2z" class="k"></path><path d="M413 317l1 1h0c1-1 2-1 3-2l3 7c0 2 2 4 2 6-1 1-1 1-2 1h-2 0v-1l-6-11 1-1z" class="m"></path><path d="M417 325c-1 0-1-1-1-1-1-2 0-3 0-5h1l1 2c0 2-1 2-1 3v1z" class="c"></path><path d="M417 325v-1c1 1 1 2 2 2 1 1 1 2 2 3h1c-1 1-1 1-2 1h-2 0v-1l-1-4z" class="K"></path><path d="M410 306h2c1 0 1-1 1-1l1 1h1c0 1 0 2 1 2l2 1c-1 1-1 1-1 2l-1 1c1 0 1 0 1 1-1 1-1 2 0 3-1 1-2 1-3 2h0l-1-1-1 1-1-3c-1-1-1-2-2-3-1 0-1-1-1-1h3l1-2v-2h-1-1v-1z" class="c"></path><path d="M413 310h0c1 0 1 0 2 1v1c-1-1-2-1-2-2z" class="a"></path><path d="M412 307h0v2c1 0 2-1 2-1 1 1 1 2 2 3h-1c-1-1-1-1-2-1h0l-1-1v-2z" class="K"></path><path d="M415 306c0 1 0 2 1 2l2 1c-1 1-1 1-1 2l-1 1v-1c-1-1-1-2-2-3l1-2z" class="d"></path><path d="M410 306h2c1 0 1-1 1-1l1 1h1l-1 2s-1 1-2 1v-2h0-1-1v-1z" class="F"></path><path d="M411 311h1v1l1 1h0-2 0l2 4-1 1-1-3c-1-1-1-2-2-3-1 0-1-1-1-1h3z" class="i"></path></svg>