<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="60 40 516 629"><!--oldViewBox="0 0 624 752"--><style>.B{fill:#383838}.C{fill:#434243}.D{fill:#1c1b1c}.E{fill:#2e2e2e}.F{fill:#494849}.G{fill:#292929}.H{fill:#141415}.I{fill:#3d3d3d}.J{fill:#202020}.K{fill:#555455}.L{fill:#333233}.M{fill:#4e4e4f}.N{fill:#242425}.O{fill:#0e0e0e}.P{fill:#010000}.Q{fill:#070707}.R{fill:#656565}.S{fill:#f0eff0}.T{fill:#585858}.U{fill:#e9e8e9}.V{fill:#5c5b5c}.W{fill:#696969}.X{fill:#fff}.Y{fill:#cdcbcd}.Z{fill:#777778}</style><path d="M104 376h-1c-3 2-6 3-9 3-1 1-5 1-6 0 0-1 1 0 2 0 7-1 13-4 19-9 0 3 0 3-2 5-1 0-2 1-3 1z" class="F"></path><path d="M175 294c-3-1-8-1-10-3h0c6 1 11-1 15-4h4 0l-8 4v1h2v1c-1 1-2 1-3 1z" class="D"></path><path d="M145 318c1 0 2 0 2-1-5-1-9-1-12-5l3 2c5 2 12 2 17 0-6 0-12-1-16-5 5 2 10 4 16 4v-1l-1-1h2c3 0 5 1 7 1v1c-5 3-10 4-15 5-1 1-2 1-2 0h-1z" class="P"></path><path d="M164 303c-6-1-10-4-13-9 1 1 2 1 3 2 5 3 14 4 21 2h0c2 0 4-1 6-1-2 1-2 2-4 3h0c-2 1-4 1-6 1h3c-1 1-2 1-3 1l5 1c-2 2-4 3-6 3h-2-3 1c1 0 1 0 2-1l-1-1c-1 0-2-1-3-1z" class="Q"></path><path fill="#a6a3a5" d="M171 302c-6 0-12-1-17-4 6 0 11 2 17 3h0 3c-1 1-2 1-3 1z"></path><path d="M154 311h-1c-1 0-2-1-2-1-4-2-7-4-9-8 6 3 10 5 17 4l-5-1c-3-1-5-4-6-7 3 3 5 5 9 6 1 0 5 1 6 0l1-1c1 0 2 1 3 1l1 1c-1 1-1 1-2 1h-1 3 2l-2 1v1c-1 0-1 0-2 1-2 0-5 0-6 1h0c-2 0-2 0-4 1h-2z" class="I"></path><path d="M162 308h-7v-1c1-1 3 0 5 0h2l-1 1h1z" class="D"></path><path d="M167 304l1 1c-1 1-1 1-2 1h-1 3 2l-2 1c-2 0-4 0-6 1h-1l1-1h-2l4-1c2 0 2-1 3-2z" class="O"></path><path d="M189 290c1 0 2 0 3-1v1h0l1 1-1 1h0v2c-2 1-4 3-5 4l-7 4h0v-1h-1c-1 1-3 1-3 2l-5-1c1 0 2 0 3-1h-3c2 0 4 0 6-1h0c2-1 2-2 4-3-2 0-4 1-6 1h0c-2 0-4 0-5-1-5-1-10-3-14-5 2 0 3 1 5 2 5 1 9 2 15 1h3c0 1 0 1 1 1 2-1 4-3 6-4 1-1 2-2 3-2z" class="H"></path><path d="M181 300v-1h-1l2-3 2 2c-1 1-2 2-3 2z" class="G"></path><path d="M189 290c1 0 2 0 3-1v1h0l1 1-1 1h0v2c-2 1-4 3-5 4l-7 4h0v-1l1-1c1 0 2-1 3-2l-2-2s1-1 2-1l5-5z" class="L"></path><path d="M189 290c1 0 2 0 3-1v1h0c-2 1-7 6-8 5h0l5-5zm3 2v2c-2 1-4 3-5 4l-7 4h0v-1l1-1c1 0 2-1 3-2 2-1 3-2 4-3 1-2 2-2 4-3z" class="F"></path><path d="M206 273v2s0 1 1 2c-2 1-3 3-6 4h2v1 1c-1 1-2 3-4 4 1 0 1 0 1 1l-3 3h-1-1-2l-1-1h0v-1c-1 1-2 1-3 1s-2 1-3 2c-2 1-4 3-6 4-1 0-1 0-1-1h-3l-1-1c1 0 2 0 3-1v-1h-2v-1l8-4h0-4l4-3h1c1 0 3-2 5-3v1l2-1c3 0 5-3 7-4h1 1c2-2 2-3 5-4z" class="C"></path><path d="M194 289v-1c0-1-1-1 0-2 1 0 2 0 2 1l-2 2z" class="F"></path><path d="M198 286l5-3c-1 1-2 3-4 4l-1-1z" class="E"></path><path d="M206 275s0 1 1 2c-2 1-3 3-6 4l-4 3c1-2 4-4 6-6 1-1 2-3 3-3z" class="H"></path><path d="M198 286l1 1c1 0 1 0 1 1l-3 3h-1-1-2l-1-1h0c1 0 2-1 2-1l2-2 2-1z" class="J"></path><path d="M206 273v2c-1 0-2 2-3 3l-1-1-3 3c-2 1-3 3-5 4s-3 2-4 3l-1-1c1-1 3-2 4-4h0l-6 3c0 1-2 2-3 2h0-4l4-3h1c1 0 3-2 5-3v1l2-1c3 0 5-3 7-4h1 1c2-2 2-3 5-4z" class="E"></path><path d="M180 301v1h0l7-4v1c-1 2-2 3-4 5l1 1v3 1h0l-2 5-1 2h-1c0-1-1-2-1-2h-1v3h-2v-3h0c-1 1-3 2-4 3v-1l-3 1h0v-1c-1 1-2 1-3 1l-6 3c-1-1-1-1-2-1h0c3-1 6-2 8-4h0c0-1-1-1-1-1v-1c-1 0-1-1-2-1-2 0-4-1-7-1 2-1 2-1 4-1h0c1-1 4-1 6-1 1-1 1-1 2-1v-1l2-1c2 0 4-1 6-3 0-1 2-1 3-2h1z" class="H"></path><path d="M170 313c0-2 2-3 3-3v1h0c-1 1-2 2-3 2z" class="J"></path><path d="M174 312c1 0 2 0 2 1h0v1h0c-1 1-3 2-4 3v-1l-3 1h0v-1c-1 1-2 1-3 1 2-1 4-2 5-3l3-2z" class="C"></path><path d="M176 303c0-1 2-1 3-2v2h1 1l-3 3v-3c-2 0-3 2-4 3-1 0-2 1-3 1s-2 1-3 1v-1l2-1c2 0 4-1 6-3z" class="N"></path><path d="M160 310c2 0 4 1 6 1 1 0 2 0 3-1 2 0 3-1 5-1l-1 1c-1 0-3 1-3 3-2 0-3 1-4 2h0c0-1-1-1-1-1v-1c-1 0-1-1-2-1-2 0-4-1-7-1 2-1 2-1 4-1h0z" class="F"></path><path d="M180 301v1h0l7-4v1c-1 2-2 3-4 5l1 1v3 1h0l-2 5-1 2h-1c0-1-1-2-1-2h-1v3h-2v-3-1h0c0-1-1-1-2-1h0c1-2 3-4 4-6l3-3h-1-1v-2h1z" class="E"></path><path d="M179 308c0-2 2-3 3-4 0 1-1 3-1 4-1 1-1 2-1 3s1 1 1 2v1h1l-1 2h-1c0-1-1-2-1-2 0-2 0-5 1-6 0 0 0-1 1-1-1 0-2 1-2 1z" class="N"></path><path d="M179 308s1-1 2-1c-1 0-1 1-1 1-1 1-1 4-1 6h-1v3h-2v-3-1c1-1 2-3 3-5z" class="H"></path><path d="M163 312c1 0 1 1 2 1v1s1 0 1 1h0c-2 2-5 3-8 4h0l-11 6c-4 1-7 3-10 5 0 0-1 0-1 1-1 0-2 1-3 1h0c-2 1-4 3-6 4l-4 4c-3 2-6 4-9 4l6-3c5-3 9-8 14-11-5 0-10 0-14-2h0c4 1 10 1 14 0 1 0 3-1 4-2h-3c-8 0-15-2-20-7 2 0 4 1 6 2 4 2 10 1 15 1-4-1-11-3-15-7v-1c3 2 6 3 9 4 5 1 10 1 15 0h1c0 1 1 1 2 0 5-1 10-2 15-5v-1z" class="P"></path><path d="M163 312c1 0 1 1 2 1v1s1 0 1 1h0c-2 2-5 3-8 4h0l-11 6c-4 1-7 3-10 5 0 0-1 0-1 1-1 0-2 1-3 1h0v-1h1c2-2 5-4 7-5 1 0 2-1 3-1v-1c-1 1-2 1-4 1h-1c-2 0-7 1-8 0h-2c-1 0-3-1-3-2h0c1 1 2 0 3 1 9 0 18 0 25-5l-9 1c-1 1-2 0-3 1h-1-3c-1-1-3 0-4-1h0c-2 0-3-1-4-2 5 1 10 1 15 0h1c0 1 1 1 2 0 5-1 10-2 15-5v-1z" class="E"></path><defs><linearGradient id="A" x1="152.926" y1="618.684" x2="136.572" y2="637.728" xlink:href="#B"><stop offset="0" stop-color="#11100e"></stop><stop offset="1" stop-color="#474749"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M103 604l3 3c5 2 8 6 12 10l16 11 17 8 2 1c2 0 4 1 7 1 0 1 2 1 3 1 2 1 5 2 8 2v-1h0 2c3 2 7 3 11 4s7 1 11 1c-15 2-30 1-44-3-6-2-12-5-18-9-3-2-6-4-10-6-1-1-4-4-6-4-1 0-5-6-6-7h0c2 2 5 5 7 6v-1l-16-16 1-1z"></path><path d="M134 628l17 8 2 1h-1-1 0c-4-1-8-2-11-4-2-1-4-2-5-3h0c-1-1-1-1-1-2z" class="C"></path><path d="M166 317c1 0 2 0 3-1v1h0l3-1v1l-4 3-7 6c-1 0-2 2-3 2l-1-1c-1 1-3 2-4 3-1 0-1-1-2-1l-2 2c-2 1-4 3-7 5h-2v1-2h-2c-2 2-4 4-5 6-2 2-5 4-6 7l-1-1c-4 6-9 11-14 16-1 1-2 3-4 4-2 2-5 3-8 4h-1c-2 1-7 1-9 1s-6 1-7 0v-1c5 0 10 0 14-1 12-3 19-13 24-23l-5 3h-1c4-3 8-5 10-9s5-6 8-9c1 0 2-1 3-1 0-1 1-1 1-1 3-2 6-4 10-5l11-6c1 0 1 0 2 1l6-3z" class="P"></path><path d="M148 327h0c-4 3-7 5-10 8-2 2-4 4-5 6-2 2-5 4-6 7l-1-1c3-5 7-11 12-15 3-2 7-4 10-5z" class="T"></path><path d="M166 317c1 0 2 0 3-1v1h0l3-1v1l-4 3-7 6c-1 0-2 2-3 2l-1-1c-1 1-3 2-4 3-1 0-1-1-2-1l-2 2c-2 1-4 3-7 5h-2v1-2h-2c3-3 6-5 10-8h0v-1l12-6 6-3z" class="F"></path><path d="M151 328v1l-2 2c-2 1-4 3-7 5h-2c2-2 4-4 6-5l-1 1h1l5-4z" class="L"></path><path d="M148 327h2l-4 4c-2 1-4 3-6 5v1-2h-2c3-3 6-5 10-8z" class="Q"></path><path d="M151 328c2-2 4-2 6-3s3-2 5-3h1l-6 5c-1 1-3 2-4 3-1 0-1-1-2-1v-1z" class="C"></path><defs><linearGradient id="C" x1="118.039" y1="620.396" x2="109.025" y2="627.397" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#434344"></stop></linearGradient></defs><path fill="url(#C)" d="M92 594c1 1 3 2 4 3l6 8 16 16v1c-2-1-5-4-7-6h0c1 1 5 7 6 7 4 6 9 11 15 14 3 2 6 2 8 3-8 0-22-2-28-9-1 0-3-2-4-3-5-5-9-10-13-16h2l2 2c0-4-3-7-5-9l1-1 2 2h0 0c-1-1-2-2-3-4l-1-3v-1c0-1 0-3-1-4z"></path><path d="M92 594c1 1 3 2 4 3-1 1 0 2 0 3l1 1c1 1 3 3 4 5h-1v1c1 1 0 1 1 1v1l1 1c0 1 1 2 2 3h0c-3-2-4-5-7-7h0c-1-1-2-2-3-4l-1-3v-1c0-1 0-3-1-4z" class="D"></path><path d="M95 612h2l2 2c1 0 1 1 1 2h1c1 0 1 0 1 1 1 0 2 1 3 1 2 2 3 5 5 7 0-1 1-1 1-1 1 1 2 1 2 3 0 1-1 2-2 3h1v1h0c-1 0-3-2-4-3-5-5-9-10-13-16z" class="C"></path><defs><linearGradient id="D" x1="59.347" y1="473.539" x2="73.901" y2="481.333" xlink:href="#B"><stop offset="0" stop-color="#1f1f20"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#D)" d="M71 449v2l1-2c0 2 0 3 1 4l-1 1h0l1-1 1 1c-1 1-2 1-2 3h0v4c2 0 3-3 4-4h1l-1 1 1 1h2v1 7h0l1 1c-2 4-3 8-4 12-1 2-2 4-2 6l-1-1-3 12c-2-2 0-6 0-8h0-1v-1c-1 1-1 3-2 4-1 2-2 4-3 7s-2 8-2 12c-1-2 0-6-1-7h-1 0l3-21c0-4 1-7 2-11 1-8 4-15 6-23z"></path><path d="M76 458l1 1h0v3l-2 2 1-2c-1-1 0-1-1-1h0c0-1 0-2 1-3z" class="D"></path><path d="M65 472c0 3-1 5-1 7 0 0 0-1 1-2v-2c1-1 1-2 1-2v-2l1 1c-1 2-2 9-4 11h0c0-4 1-7 2-11z" class="G"></path><path d="M77 459h2v1 7h0l1 1c-2 4-3 8-4 12-1 2-2 4-2 6l-1-1-3 12c-2-2 0-6 0-8 1-5 3-11 5-16 1-2 2-4 2-6l-1-1c0 1-1 2-1 2v-4l2-2v-3h0z" class="O"></path><path d="M157 327l1 1-1 1-2 1v2h2l-9 11c-1 2-3 4-4 7-1 2-2 6-4 8h-1v-1h0c0 1-1 2-2 3l-3 8c-1 1-1 1-1 2 0 3-1 6-2 9h-1c-1 1-2 2-2 4l-1 2v1c-2 3-4 5-6 8v1c0-3 2-4 3-6h0l-3 3c0 1-1 1-2 1l-1 1h0c-2 0-6 3-8 5-1 1-3 3-5 3h-1s-1 1-2 1c-1 1-3 2-5 3-2 2-6 6-9 7v-2h-1c0-6 2-12 5-18h-8 0l6-1h2c1 0 3-5 3-6 2-3 6-7 9-10 1 0 2-1 3-1 2-2 2-2 2-5l1-1h0c-7 3-13 6-21 5l11-2-1-1h1c3-1 6-2 8-4 2-1 3-3 4-4 5-5 10-10 14-16l1 1c1-3 4-5 6-7 1-2 3-4 5-6h2v2-1h2c3-2 5-4 7-5l2-2c1 0 1 1 2 1 1-1 3-2 4-3z" class="C"></path><path d="M140 342h0c1-1 2-2 3-2-2 2-4 5-6 8h-1 0l-1-1c1-2 3-3 5-5z" class="K"></path><path d="M149 331l1 1-2 1-9 8c1-2 2-3 3-5 3-2 5-4 7-5z" class="R"></path><path d="M157 327l1 1-1 1-2 1c-1 1-2 1-3 2s-2 1-4 1l2-1-1-1 2-2c1 0 1 1 2 1 1-1 3-2 4-3z" class="K"></path><path d="M105 398v1h0c1 0 2 0 3-1l3-3c1 0 1-1 2-1v1h0c-1 1-3 2-3 3v1c-1 1-3 3-5 3h-1s-1 1-2 1l1-2h0c0-1 1-2 2-3z" class="B"></path><path d="M123 385v-1c1-1 2-3 4-4v2l1 1-1 2v1h-1c-1 1-2 2-4 3l-1 1c-1-2 1-2 2-4v-1z" class="F"></path><path d="M122 389l5-7 1 1-1 2v1h-1c-1 1-2 2-4 3z" class="E"></path><path d="M137 348h1c0 1 0 1-1 2h1c0 3-2 5-4 7l-1-1 1-1v-2c0-1 2-3 2-5h1z" class="K"></path><path d="M137 348h1c0 1 0 1-1 2l-3 5v-2c0-1 2-3 2-5h1z" class="B"></path><path d="M134 353v2l-1 1-2 3c-1 1-7 8-7 9l-4 5-1-1c0-1 1-2 2-3l5-7 8-9z" class="H"></path><path d="M132 352l3-5 1 1h0c0 2-2 4-2 5l-8 9v-1h-1c0-1 2-3 3-4l-1-1v-1c1-1 3-3 5-3z" class="M"></path><path d="M127 356v-1c1-1 3-3 5-3-1 2-2 3-3 5-1 1-2 3-3 4h-1c0-1 2-3 3-4l-1-1z" class="D"></path><path d="M155 332h2l-9 11-1-1c-2 1-3 5-6 5 0 0-1 0-1-1 1-1 2-2 4-3l11-11z" class="O"></path><defs><linearGradient id="E" x1="141.23" y1="338.297" x2="125.045" y2="345.407" xlink:href="#B"><stop offset="0" stop-color="#010404"></stop><stop offset="1" stop-color="#2c2829"></stop></linearGradient></defs><path fill="url(#E)" d="M138 335h2v2l-10 10c-1 2-2 3-4 3h-1l2-2c1-3 4-5 6-7 1-2 3-4 5-6z"></path><path d="M114 365c3-3 6-4 8-6s3-4 5-6a30.44 30.44 0 0 1 8-8c1-2 3-3 5-3-2 2-4 3-5 5l-3 5c-2 0-4 2-5 3v1c-1 2-3 4-5 6l-3 3s0-1 1-2c-1 0-1 1-2 1s-1 0-2 1h-2z" class="H"></path><path d="M127 356l1 1c-1 1-3 3-3 4h1v1l-5 7c-1 1-2 2-2 3l1 1c-2 2-4 4-6 5h-1l2-2c-1-1-1-1-2-1 0-1 3-3 4-4-1-1 1-4 1-5l1-1 3-3c2-2 4-4 5-6z" class="I"></path><defs><linearGradient id="F" x1="141.877" y1="355.909" x2="135.509" y2="353.479" xlink:href="#B"><stop offset="0" stop-color="#121313"></stop><stop offset="1" stop-color="#393738"></stop></linearGradient></defs><path fill="url(#F)" d="M140 346c0 1 1 1 1 1 3 0 4-4 6-5l1 1c-1 2-3 4-4 7-1 2-2 6-4 8h-1v-1h0c0 1-1 2-2 3-1 0-1 2-2 3s-2 2-3 4h-1l-1 1h0c-1 0-1 1-2 1 2-2 3-4 4-6 4-4 6-9 9-13 0-1 1-1 1-2l-2 1-1-1c0-1 0-1 1-2z"></path><path d="M135 363c1-1 1-3 2-3l-3 8c-1 1-1 1-1 2 0 3-1 6-2 9h-1c-1 1-2 2-2 4l-1-1v-2c-2 1-3 3-4 4v1c-1 0-1 1-2 2l-1-1c0-1 2-4 2-5l3-3c2-2 3-5 5-7 0-1 1-1 1-2 2-2 3-4 4-6z" class="T"></path><path d="M133 370c0 3-1 6-2 9h-1c-1 1-2 2-2 4l-1-1v-2l6-10z" class="G"></path><path d="M114 365h2c1-1 1-1 2-1s1-1 2-1c-1 1-1 2-1 2l-1 1c0 1-2 4-1 5-1 1-4 3-4 4 1 0 1 0 2 1l-2 2h1c2-1 4-3 6-5l4-5v2c-1 2-3 3-4 5v1l-1 1h1c-1 4-4 6-6 9h0c-1 1-1 0-1 1-1 0-1 1-2 1v1c-1 0-1 0-1 1h-1c1 2 2 2 1 3-1 2-3 4-5 5h0c-1 1-2 2-2 3h0l-1 2c-1 1-3 2-5 3-2 2-6 6-9 7v-2h-1c0-6 2-12 5-18h-8 0l6-1h2c1 0 3-5 3-6 2-3 6-7 9-10 1 0 2-1 3-1 2-2 2-2 2-5l1-1h0c-7 3-13 6-21 5l11-2c2 0 3-1 5-2h1l8-5z" class="P"></path><path d="M97 406l4-8h1c0 1-1 3-1 4l2-1-1 2c-1 1-3 2-5 3z" class="J"></path><path d="M109 379h0v-1l-2 1h-2c3-2 7-5 9-8 2-2 3-3 4-5 0 1-2 4-1 5-1 1-4 3-4 4-1 2-2 3-4 4h0z" class="E"></path><path d="M101 398c3-5 7-10 12-13l1 1h0c-1 1-1 0-1 1-1 0-1 1-2 1v1c-1 0-1 0-1 1h-1c1 2 2 2 1 3-1 2-3 4-5 5h0c-1 1-2 2-2 3h0l-2 1c0-1 1-3 1-4h-1z" class="L"></path><path d="M102 398c2-2 3-4 5-6 0-1 0-1 1-1 0 2-2 5-3 7h0c-1 1-2 2-2 3h0l-2 1c0-1 1-3 1-4z" class="Q"></path><path d="M124 368v2c-1 2-3 3-4 5v1l-1 1c-6 6-13 10-19 14-3 2-4 3-6 5l-1-1c1-1 2-2 4-3 0-1 1-1 2-2l-5 2h-1c2-2 5-3 7-5 3-2 6-5 9-8h0c2-1 3-2 4-4 1 0 1 0 2 1l-2 2h1c2-1 4-3 6-5l4-5z" class="M"></path><path d="M194 140h-2c-1 1-2 2-2 3-25 28-33 71-19 105 2 4 3 7 5 9 2 4 7 11 7 15-3-5-7-11-10-17-6-11-9-23-10-34-1-24 4-48 17-68 4-5 8-11 13-15 5-7 12-12 19-17l15-9c14-7 29-11 45-14 15-4 30-4 45-4l50-1h94 36c9 0 18 1 27 0l1 15c-2 1-4 1-6 1-3 1-5 2-7 2v1h1 0c2 1 4 1 6 2l9 3c18 9 30 25 37 43 5 13 6 29 4 43 0 6-2 13-5 19-7 16-20 30-36 35-13 5-27 5-39-1-7-3-12-9-15-17-2-6-2-13 1-19 3-4 7-9 12-11 4-1 9-1 12 1s5 4 6 7c1 4 0 7-2 10-1 3-3 5-7 5-2 1-5 1-7 1-1 0-3-1-4-1 1 4 2 6 6 9 3 2 8 2 12 1 5-1 10-5 12-9 4-6 5-14 3-21-1-7-7-14-14-18l-8-4c6-4 10-7 11-13 3-10 1-22-4-31-4-6-10-13-17-15-2 2-4 5-6 7-7 11-12 23-15 35-1 7-2 13-2 20l-3 41v67l-1 58v34 77c0 23 1 46 4 69 0 6 1 11 2 17 3 16 11 32 22 44 14 15 32 24 52 27h0v12l-258-3c-7 1-14 2-21 2h-4c-8 2-15 4-24 5h0c-16 2-33 1-49-1-4-1-7-2-10-2h-2 0v1c-3 0-6-1-8-2-1 0-3 0-3-1-3 0-5-1-7-1l-2-1-17-8-16-11c-4-4-7-8-12-10l-3-3-1 1-6-8c-1-1-3-2-4-3 1 1 1 3 1 4v1l1 3c1 2 2 3 3 4h0 0l-2-2-1 1c2 2 5 5 5 9l-2-2h-2c-1-1-2-3-3-3l6 10-8-7c-3-3-6-7-9-11l-5-9c-6-10-9-19-12-30-1-3-1-6-2-9-1-7-2-15-2-22-1-1-1-4-1-5l1-22h0 1c1 1 0 5 1 7 0-4 1-9 2-12s2-5 3-7c1-1 1-3 2-4v1h1 0c0 2-2 6 0 8l3-12 1 1c0-2 1-4 2-6 1-4 2-8 4-12l-1-1h0v-7-1h-2l-1-1 1-1h-1c-1 1-2 4-4 4v-4h0c0-2 1-2 2-3l-1-1-1 1h0l1-1c-1-1-1-2-1-4l-1 2v-2c1-12 4-22 12-32 1-2 4-4 4-6h1v2c3-1 7-5 9-7 2-1 4-2 5-3 1 0 2-1 2-1h1c2 0 4-2 5-3 2-2 6-5 8-5h0l1-1c1 0 2 0 2-1l3-3h0c-1 2-3 3-3 6v-1c2-3 4-5 6-8v-1l1-2c0-2 1-3 2-4h1c1-3 2-6 2-9 0-1 0-1 1-2l3-8c1-1 2-2 2-3h0v1h1c2-2 3-6 4-8 1-3 3-5 4-7l9-11h-2v-2l2-1 1-1c1 0 2-2 3-2l7-6 4-3c1-1 3-2 4-3h0v3h2v-3h1s1 1 1 2h1l1-2 2-5h0v-1-3l-1-1c2-2 3-3 4-5v-1c1-1 3-3 5-4v-2h0l1-1h2 1 1l3-3c0-1 0-1-1-1 2-1 3-3 4-4v-1-1h-2c3-1 4-3 6-4-1-1-1-2-1-2v-2c-3 1-3 2-5 4h-1-1c-2 1-4 4-7 4l-2 1v-1c-2 1-4 3-5 3h-1c2-3 3-5 2-10 0-3 0-6-1-9s-3-6-5-9l-6-12c-4-9-6-17-6-27-1-19 2-38 11-55 4-7 9-14 15-20v-2z" class="P"></path><path d="M138 457c1-1 1-2 1-3h1l1 1-3 2z" class="O"></path><path d="M164 372s1 0 2-1v1l-1 4v-1h0c-1-1-1-2-1-3z" class="N"></path><path d="M281 364c0 2 1 3 1 4-1 1-1 1-2 1 0-1 0-2-1-3l2-2z" class="J"></path><path d="M214 423h7 0l-1 1h-2v1c-1 0-3-1-4-2z" class="O"></path><path d="M272 335l2 5-1 1-3-6 2 2v-2z" class="I"></path><path d="M329 260h0v4c-1 1-2 0-3 0v-2h1l2-2z" class="D"></path><path d="M262 634c1-1 1-2 1-3l1-1h5c-3 1-5 3-7 4z" class="L"></path><path d="M262 427c1 0 2-1 3 0h0l2 5-2-1c-1-1-2-2-3-4z" class="H"></path><path d="M303 333s-1 1-2 1-4-2-5-2h7v1z" class="S"></path><path d="M169 547c1 1 1 2 1 3-2 2-4 1-6 1l-1-1h3c1-1 2-1 3-3zm108-119c1 3 1 5 1 8l-1 1h-2c1-3 2-5 2-9z" class="H"></path><path d="M288 211h-9c3-1 7-1 10-1h2 0l-1 1h-2z" class="D"></path><path d="M160 448h4 0c0 1 0 1 1 1l-6 1h-2c1 0 1-1 1-1l2-1z" class="M"></path><path d="M151 636c4 0 8 1 12 3-1 0-3 0-3-1-3 0-5-1-7-1l-2-1z" class="J"></path><path d="M171 522c0 2 1 3 1 4 1 2 2 3 1 5-1-2-2-2-2-4-1-2-1-3 0-5z" class="O"></path><path d="M200 415l1-1c2 1 3 3 4 4v-1c2 1 3 2 4 3-2 0-5-1-6-2 0-1-2-2-3-3z" class="H"></path><path d="M216 524c1 3 2 6 2 10h-1c-1 0-1-5-1-6s0-3-1-4h1z" class="D"></path><path d="M200 259h1v1c0 1 0 1-1 2h-3l-1-1v-1c2-1 3-1 4-1z" class="S"></path><path d="M192 408h0c-1-1-1-2-1-3 1 0 2-1 2 0h2c1 0 1 0 2 1h-2l1 1v1c-1-1-2-1-2-2h-1l1 2h-1-1z" class="H"></path><path d="M147 451h0c-1 1-2 1-3 1v-3l5-2c0 1 0 1-1 1v1l-1 1v1z" class="D"></path><path d="M233 420h1c0-1-1-2-1-3l1-1h1v1 7-3c-1 0-1-1-2 0-1 0-1 0-2 1 0 0 0 1-1 1h0v-1s0-1 1-1c0-1 1-1 2-1z" class="H"></path><path d="M194 402h0c-1-3 1-4 1-7 0 0 0-1 1-1 0 3 0 6 1 9h-1-2v-1z" class="G"></path><path d="M186 605l8-1c0 1 0 1 1 1-3 1-6 1-9 2 0-1-1-1-2-1h1 0-1c1-1 2-1 2-1z" class="W"></path><path d="M282 217c4 0 8 1 11 3h1c-2 1-3 0-5 0h-1c-1-1-3-2-5-2 0 0-1 0-1-1h0zm-23 23v2l1 1h0v1h0v1l1 1h-1-2l-1 2h-1c0-2 0-3 1-4h0c1-1 1-1 1-3 1 0 1-1 1-1z" class="D"></path><path d="M158 449s0 1-1 1h2l-9 4-2-1 10-4z" class="F"></path><path d="M246 264c0-2 3-4 5-6l-3 8h0-1l-1 1h-1c1-1 1-1 1-3z" class="D"></path><path d="M133 443c1 1 2 1 2 2-1 3 2 8 0 10h0c-1-4-2-8-2-12z" class="N"></path><path d="M197 526v-4h1v1h1v4l2 2c1 1 2 1 3 0h1v1l-1 1c-1 0-2 0-3-1-2 0-3-2-4-4z" class="R"></path><path d="M287 241h0c-2-1-11-1-12-3 1 0 2 1 3 1h4c2 0 6 0 7 1 1 0 1 0 1 1h-3z" class="L"></path><path d="M285 562h1c-1 3-2 8-4 11v-3l1-1v-1l1-1h0v-2h0l-1 1c0 1-1 1-1 2s-1 1-1 2h-1c1-3 3-6 5-8z" class="B"></path><path d="M238 257c1-2 6-7 8-8h1c-3 3-6 6-8 9 0 0 0 1-1 1 0 1 0 1-1 1v1c-1-1 1-3 2-4h-1z" class="O"></path><path d="M288 314l-1-1c0-4 3-8 6-11-1 2-2 5-4 7v1h0 2 1 0c-1 1-2 1-2 2-1 0-2 1-2 2z" class="S"></path><path d="M283 302c0-1 1-2 2-2l1-1c0 2 0 2-1 4l-3 6h0v-2c0-2 1-3 1-5z" class="H"></path><path d="M233 503c2 1 3 1 5 2v-1l2 2-2 1c2 1 5 2 6 4l1 1c-2-2-4-3-7-5-1-1-3-1-5-2l-1-1c1 0 1 0 1-1z" class="M"></path><path d="M290 205c1 0 5 3 7 3h1c-2 1-2 0-4-1h-1s-1 0-1-1h-1 0c-1-1-2 0-3 0h1 0 1 1c-1 1-2 1-3 1l-1-1-8 1c3-2 6-2 11-2z" class="J"></path><path d="M255 230h0c0-1 1-2 1-3l1 4v3c-1 1 0 3-1 4v-6h-1c-1 3 0 7-2 10l-1-1c1 0 1-1 1-2 1 0 0-1 1-2v-1-1c1-1 0-3 1-5z" class="H"></path><path d="M244 425s0-1 1-2v1l1 4c1 0 1 0 1-1l1 2h0c1 0 1 2 1 2 1 0 2 0 3 1-1 0-1 1-2 1h0c-4-2-5-4-6-8zm-6 89c4 1 7 4 9 7l2 2-3-1-8-8z" class="B"></path><path d="M67 492c1-1 1-3 2-4v1h1 0c0 2-2 6 0 8 0 2-1 5-1 7l-1-12h-1z" class="D"></path><path d="M158 408c2 0 6 3 7 5h0l1 1h-3c0-1-1-2-2-2l-1-1v1 4c-1-1-1-4-1-5l1-1h-1 0v-1h-1v1-2z" class="H"></path><path d="M237 261c2 0 2 1 3-1 1-1 1-1 2-1l1-1h0l1-1c0-1 1-1 2-1 1 1 1 2 0 3 0 1-1 2-1 3h0l-1-1h0c-2-1 0-1 0-3l-5 4c-1 0-1 0-2-1z" class="O"></path><path d="M336 217c-1 3-4 6-7 9-1 0-1-1-1-1 1-2 2-3 2-4 2-2 3-4 5-6 0 0 0 1-1 2 0 1-3 4-3 6v-1c1-2 3-4 5-5z" class="U"></path><path d="M177 446h4c0 1-1 1-2 1l-14 2c-1 0-1 0-1-1h0l13-2z" class="K"></path><path d="M153 428h0c2 3 5 5 7 8l-3 3s0-1-1-1h1c0-1 0-2-1-2v-2c-1 0 0 0-1-1s-2-3-2-5z" class="E"></path><path d="M170 560c3 0 5 0 8-1 3 0 6-1 9-1-5 3-10 3-16 4-1-1-1-1-1-2z" class="R"></path><path d="M258 567c0-1 3-5 3-6 0 3 0 6-2 9-2 2-3 4-5 5l2-3 2-3v-2z" class="O"></path><path d="M277 358l1 1c1-2-1-4 1-5 0 1 0 2 1 3 0 2 1 5 1 7l-2 2c0-2-2-5-2-7v-1z" class="H"></path><path d="M182 404h1 0v-2l1-1v1h1v-2l1 1c1 3 3 6 5 9h-1c-1 0-2-1-2-2-2-1-3-3-4-5 0 1 1 2 0 3l-2-1v-1z" class="J"></path><path d="M285 588v-1c0-1 1-2 2-3h1c-1 2-2 5-1 7l1 1 1-1h2c0 1 0 3-2 3v-1c-1 0-2 1-3 1-1-2 0-4-1-6h0zm-64-165c4 1 15 3 18 5l-1 1c-1 0-1 0-2-1h-1-1c-3-1-5-2-8-3-2 0-4-1-6-1l1-1z" class="D"></path><path d="M272 305c0-2 1-4 3-6v1h0c0 1-1 2-1 3 2-2 3-3 4-5l-1 4c-1 2-1 4-2 7v-3c-1 0-1 2-1 3h0l-1-1v-2l-1-1z" class="N"></path><path d="M299 247h1 1c0 1 1 1 2 2h2c1-1 1-1 2-1l-1 1-2 2h1v1l-5 1h-1l3-3h-2s-1-1-1-2h0-1c0-1 1-1 1-1z" class="H"></path><path d="M290 552v2s-1 2-1 3l-2 7v1c-2 4-4 7-5 10h-1l1-2c2-3 3-8 4-11l4-10z" class="R"></path><path d="M244 425v-4c0-1 1-2 2-2h1v1h1v2l-1 3v2c0 1 0 1-1 1l-1-4v-1c-1 1-1 2-1 2z" class="J"></path><path d="M245 424h0c1-1 1-3 2-4v1l1 1-1 3v2c0 1 0 1-1 1l-1-4z" class="G"></path><path d="M274 340c1 3 3 7 4 10v1l-2-2v1h-1l-2-6v-3l1-1z" class="J"></path><path d="M281 608l1-1h1c-1 3-3 6-4 9-1 1-3 2-3 4l-5 1h0c4-3 9-8 10-13z" class="I"></path><path d="M143 419c-1-2-1-3-1-4-1-4 0-9 1-13 1 5 1 11 1 17 0-1 0-2-1-2v2z" class="B"></path><path d="M556 196c0-3-1-7 0-9l1-1c1 1 1 3 2 4 0 3 0 5-2 8h0 0v-1 1c0-1-1-2-1-2z" class="Y"></path><path d="M171 522c0-3 0-6 1-8 2 2 2 3 2 7h0c0 1 0 2-1 3 0 1 0 1-1 2 0-1-1-2-1-4z" class="G"></path><path d="M167 445h5c2 0 4 0 5 1l-13 2h-4l-1-1c3-1 5-1 8-2z" class="H"></path><path d="M255 230v-1-1c1-1 1-1 2-1v4c1-1 1-1 1-2 1 1 0 1 0 1l1 1c-1 3-1 5 0 8v1s0 1-1 1c0 2 0 2-1 3h0c0-2 0-4-1-6 1-1 0-3 1-4v-3l-1-4c0 1-1 2-1 3h0z" class="J"></path><path d="M192 408h1 1l-1-2h1c0 1 1 1 2 2 1 3 2 5 4 7 1 1 3 2 3 3-4-2-9-6-11-10z" class="E"></path><path d="M130 429c1 1 2 1 3 2h0v-1c0-3-1-6-1-8 1 2 2 5 3 8 0 3 2 6 2 9-1-2-2-4-4-5 0-1 0-1-1-2h0 0v1h0c-1-1-1-2-2-4z" class="D"></path><path d="M327 241c0-2 0-3-1-5v-5h-1l2-2h1 1c0 1 1 2 1 3s-1 2-1 2c-1 1-1 1-1 2l1-2 1 1-1 2h-1l-1-1h0v1h0c1 2 0 2 0 4z" class="J"></path><path d="M171 640v-1h2c1 0 1-1 1-1 1 0 3 0 3 1h10v1c0 1-4 0-4 1v1c-4-1-7-2-10-2h-2zm64-223l1 1h1c2 2 2 5 3 7 1 1 2 2 3 4-3-1-4-3-6-4-1 0-1-1-2-1v-7z" class="G"></path><path d="M298 305c3-2 6-6 9-8h2c1 1 2 1 2 1 0 1-2 2-4 2l-8 6-1-1z" class="X"></path><path d="M318 262c2 1 4 1 6 3h1v1h1v-2c1 0 2 1 3 0v1c-1 1-2 1-3 1v2c-1 0-3 1-4 1h-1 0c0-3-1-4-2-6l-1-1z" class="H"></path><path d="M119 523c1 3 1 5 2 7 1 5 5 9 8 13h1c0 1 0 2 1 3h-2c-1-1-2-2-2-3-1-2-3-4-4-6l-3-6v-2c0-2-1-3-2-4l1-2z" class="K"></path><path d="M195 606l20-3c-2 2-5 2-7 3s-4 1-5 1c-2 0-5 0-7 1 0-1 0-1-1-2z" class="W"></path><path d="M319 257v-1c2-2 3-4 2-7v-4c2 2 3 4 3 8 0 1-1 3-2 5-1 0-1 0-2 1l-1-1h-1l1-1z" class="U"></path><path d="M141 455l7-2 2 1c-4 3-10 5-14 7 1-1 2-3 2-4l3-2z" class="B"></path><path d="M255 621c2-2 6-4 8-6 2-1 3-1 5-2-5 6-11 10-18 12v-1l5-3h0z" class="M"></path><path d="M301 540l1 1h1l-1 4c-1 2-1 5-1 7l-3 6h0l-1-1c0-1 1-3 1-4 1-5 2-9 3-13z" class="F"></path><defs><linearGradient id="G" x1="278.512" y1="356.547" x2="273.611" y2="364.074" xlink:href="#B"><stop offset="0" stop-color="#0d0d10"></stop><stop offset="1" stop-color="#343430"></stop></linearGradient></defs><path fill="url(#G)" d="M272 352c3 1 4 2 5 5v1 1c0 2 2 5 2 7 1 1 1 2 1 3-1 1-1 1-1 2l-1-1 1-1-1-1c0-2-2-5-2-7 0-1 0-1-1-2 0-2-1-3-2-5l-1-2z"></path><path d="M255 342h0 0v-2-1c-1-1-1-2-1-3h1v1c1 1 1 2 2 3h0l1 1c1 3 2 7 2 9 0 1-1 1-1 1v-2c-1 0-1-1-2-2s-2-3-2-5z" class="G"></path><path d="M252 432c1 0 2 1 4 1h0c1 1 3 3 4 5 1 1 3 2 4 3l-5-2-6-3-3-3c1 0 1-1 2-1z" class="D"></path><path d="M556 180c0-7-2-13-3-19 4 5 5 10 4 16 0 2 0 3-1 4v-1z" class="S"></path><path d="M306 284v-1l15-5h1c1-1 2-1 3-2h2s1 0 1 1h0l-21 8-1-1z" class="H"></path><path d="M246 264c0 2 0 2-1 3h1l1-1h1c-1 1-2 3-3 5v-1c-3 3-5 7-7 10v-3h0c2-4 5-8 8-13z" class="G"></path><path d="M143 419v-2c1 0 1 1 1 2s1 3 1 4c-1 1 0 3 0 4 1 3 2 7 4 10 1 1 2 4 2 5-2-1-3-3-3-5-3-5-4-10-6-16l1-1v-1z" class="F"></path><path d="M526 211h1c1 1 2 3 3 5s2 10 1 13l-1-1-2-7v-1c1 0 1 0 2-1l-2-2-1-1c-1-1-1-3-1-5z" class="S"></path><path d="M280 221v-1c-1-1-1-3 0-4h1l1 1h0 0c0 1 1 1 1 1 2 0 4 1 5 2h1v1h-1 2v1l-2 1c0-1-1-1-2-1l-6-1z" class="G"></path><path d="M280 221v-1c-1-1-1-3 0-4h1l1 1h0 0c0 1 1 1 1 1 2 0 4 1 5 2-2-1-5-2-8 0v1z" class="O"></path><path d="M265 520c0-1 0-1-1-2 0-1 0-3-1-4v-1c0-1-1-1-1-2s0-1 1-2c4 9 7 22 5 31h0c-1-1-1-3-1-5 0-5-1-10-2-15z" class="W"></path><path d="M198 631l27-6c-4 2-8 3-12 4h-1v1h-2v1h1-8c-1 1-2 1-4 1l1-1h-2z" class="R"></path><path d="M90 595h-1v-1c2 2 3 3 4 5l1 3c1 2 2 3 3 4h0 0l-2-2-1 1c2 2 5 5 5 9l-2-2c0-1-1-2-2-3-1-2-2-4-3-5h1l-3-4v-1c-1-1-1-2 0-4z" class="D"></path><path d="M90 595h-1v-1c2 2 3 3 4 5l1 3c1 2 2 3 3 4h0 0l-2-2-1 1c-2-3-3-6-4-10zm164-267c0-1 0-2 1-3l-1-2v-5h0c1 3 1 7 2 10 1 2 2 4 2 7v-1c-1 2 1 5 1 7h-1l-1-1-3-12z" class="E"></path><path d="M335 269h1v3c-1 1-2 1-3 1 0 1-1 2-1 2-1 1-2 2-4 2h0c1 0 2-1 2-1l1-1h1l-1-1-1 1v-1c-2 0-5 0-7 1-1 0-2 0-3 1h0-1v-1c1-1 1-1 2-1h1v-1h0 1c3-1 5-1 8-1 2 0 2 0 4-1 1-1 0-1 0-2z" class="O"></path><path d="M109 486v1c1-2 0-5 0-7h1v2c2 0 2 0 3 1 0 4-1 8-2 12 0 1-1 2-1 4 0-3 1-8-1-10v2-3-2zm83-84c-1 0-1-1-2-1-2-4-4-12-3-16l1-1v2h1v1c0-1 1-1 1-2h0 1c-1 4-1 7-1 11 0 2 2 5 2 6z" class="I"></path><path d="M533 204c1-1 1-3 1-4 1-1 4-1 5 0 2 0 2 2 3 4s1 3 1 5c0 1-1 1-1 1l-1-1h0v-1c0-3-2-4-4-6l-3 3h0l-1-1z" class="U"></path><path d="M310 532c1-10 1-21 0-31h1c1 2 1 6 1 9 0 8 0 16-1 24h0c-1 0-1-1-1-2z" class="M"></path><path d="M288 264h0c2-1 4-2 7-3v1c-3 2-6 4-10 6-2 1-3 1-4 2-1 0-2 0-3 1-4 0-10 2-14 1l11-2h1c5-1 8-3 12-6z" class="C"></path><path d="M301 540l2-21c1-5 1-9 0-14 0-3-1-6-2-8h1c1 2 1 5 2 7 1 12 0 25-1 37h-1l-1-1z" class="W"></path><path d="M245 289c1 1 0 2 0 3-3 9-6 19-1 28 0 1 1 3 2 4h0-2c-4-5-4-13-4-19 1-5 3-11 5-16z" class="K"></path><path d="M240 531c2 4 3 9 2 14 0 1 0 3-1 3v-1l-1 1v2l-2-2h0c2-5 2-12 2-17z" class="L"></path><path d="M240 548v-1c0-1 1-5 1-6l1 4c0 1 0 3-1 3v-1l-1 1z" class="I"></path><defs><linearGradient id="H" x1="286.107" y1="233.636" x2="293.085" y2="242.973" xlink:href="#B"><stop offset="0" stop-color="#1f1e1d"></stop><stop offset="1" stop-color="#38373a"></stop></linearGradient></defs><path fill="url(#H)" d="M282 239c-2-2-6-1-8-3v-1c1 1 2 1 3 1 4 1 8 0 12 1 1 1 2 0 3 1 1 0 2 0 3 1 1 0 2 1 3 1h-1c-1 0-6 1-7 1 0-1 0-1-1-1-1-1-5-1-7-1z"></path><path d="M230 187h2v6c0 3 0 5-2 8h-1-1c0-3 0-6 1-9 0-2 0-3 1-5z" class="U"></path><path d="M122 545h1l-4-5c-2-2-4-5-5-8-1 0-2-2-2-3v-1c6 10 13 19 21 27h-1l-1-1-1 1-2-2c0-3-4-5-6-8z" class="B"></path><path d="M92 609l-1-1v-1c-3-3-4-8-6-12-1-4-2-7-3-12l7 16v2h1v1c1 1 1 4 3 5 0 1 1 2 1 3h1v-1c1 1 2 2 2 3h-2c-1-1-2-3-3-3z" class="E"></path><path d="M169 404c-1-4-4-6-5-10-2-5-2-10-1-15v-3c0-1-1-2-1-3-1-1-1-3-2-4l4 3c0 1 0 2 1 3h0l-1-1-1 1c1 1 1 2 2 2-1 2-1 3-1 5 0 3-1 7 0 10s2 5 4 8l3 4h-1-1z" class="B"></path><path d="M201 409c-1-1-1-1-1-2 1-1 2-2 3-2h0l4 5c0 1 0 1 1 1 1 4 4 6 7 8-2-1-3-1-5-2-1-1-4-5-6-6 0 1 0 1 1 1v1h-1c-1 0-1-1-2-2l-1-2h0z" class="F"></path><path d="M201 409c-1-1-1-1-1-2 1-1 2-2 3-2h0v4l1 1v1c-1-1-2-2-2-3h-1v1z" class="J"></path><path d="M291 310c2-2 3-6 4-9 2 0 3-1 4-2-1 2-2 4-4 6l6-6c2-1 4-2 5-3-1 2-10 9-11 10 1 0 2 0 3-1l1 1c-1 1-6 3-6 5-2 1-3 2-5 3 0-1 1-2 2-2 0-1 1-1 2-2h0-1z" class="Y"></path><path d="M250 316v-6c-1-7 1-13 3-18 1-4 2-8 5-10-3 7-6 15-7 23v10l-1 1z" class="I"></path><path d="M283 302c0 2-1 3-1 5-3 6-4 13-2 20 1 2 2 4 3 7l-3-6c-3-7-4-16-1-22 0-1 1-3 2-3-1 2-2 5-2 7 1-1 1-3 2-4 0-1 1-2 2-4z" class="C"></path><path d="M212 630l1-1c4 0 7-1 10-2l11-3c4-1 8-3 12-4v1c-2 2-7 3-9 4-9 2-17 5-26 6h-1v-1h2z" class="Z"></path><path d="M287 579v-1l1-3v1l1-1h0v1 3l-1 5h0-1c-1 1-2 2-2 3v1c-2 3-4 7-6 9h-1l5-8-1-2 5-8z" class="I"></path><path d="M287 579c0 2-1 5-2 6l-2 4-1-2 5-8z" class="O"></path><path d="M218 460c2 1 5 3 7 3l1-1c4 2 7 5 11 8 2 1 4 2 5 4-1 0-1-1-2-2h-1l-5-4c-1 0-2-1-2-1s-1 1 0 1c0 1 0 1 1 1l3 2v1c-2-1-5-3-7-4l-6-3c0-1-1-2-2-2-1-1-2-1-3-3z" class="L"></path><path d="M207 499c3 0 6 1 9 1 2 1 4 1 6 2h2l6 2c4 2 10 6 12 10-3-2-6-5-10-7-8-5-17-6-26-6 1 0 1-1 2-1h0-3l2-1z" class="W"></path><path d="M127 394c1 2 1 5 2 7 0 1 2 4 2 5v3c1 1 1 2 2 3v-1c0-1-1-2-1-4h0c2 3 4 7 7 10 0 1 1 1 0 2 0 0-1-1-1-2l-3-3h-1 0v2 2h0v-1c0-1-1-1-1-2-1-1-1-3-2-4-2-2-2-4-3-6 0-3 0-6-1-8v-3z" class="T"></path><path d="M521 197c2 1 5 3 7 5 0 2-1 5-2 7v2h-1c-2-5-3-9-4-14z" class="U"></path><path d="M273 622h2v1c-1 1-5 4-7 4s-4 1-6 1v1h-6c1-2 5-3 7-4l10-3z" class="F"></path><path d="M314 216h1l2 1c2 2 4 5 5 7s1 5 2 7c0 2 1 5 2 7-1-2-2-3-3-5h-1c-1 1-1 1-1 2h0v-2c0-2-1-4-2-5l-1-2h2l-1-4c-1-3-3-4-5-6z" class="G"></path><path d="M318 226h2c1 2 2 3 2 4s1 2 1 3h-1c-1 1-1 1-1 2h0v-2c0-2-1-4-2-5l-1-2z" class="J"></path><path d="M267 535c0 2 0 4 1 5h0c0 7-3 15-7 21 0 1-3 5-3 6l-8 10h-1c1-2 3-4 4-6l8-12c2-3 4-8 4-10 1-2 1-3 1-5 0-1 1-1 1-1v-8z" class="V"></path><path d="M390 151h1c1 2 2 6 1 8 0 2 0 2-1 3h-1v-2l-2 2v1c-2 0-3-1-4-2 1-2 2-3 3-5s2-4 3-5z" class="S"></path><defs><linearGradient id="I" x1="233.942" y1="525.011" x2="240.816" y2="539.72" xlink:href="#B"><stop offset="0" stop-color="#363636"></stop><stop offset="1" stop-color="#525153"></stop></linearGradient></defs><path fill="url(#I)" d="M237 535c0-4-1-10-2-14 3 3 4 6 5 10 0 5 0 12-2 17l-1 3h0v-1c1-5 1-10 0-15z"></path><path d="M164 488v2 1c1-1 1-2 2-3h0c2-2 4-4 5-6 1 0 2-1 2-1 1 0 1-1 2-1s2-1 3-1v1c0 1 0 1-1 1-2 1-4 3-6 4l-4 4c-3 4-4 9-6 13 0-3 0-6 1-8 0-2 1-4 2-6z" class="N"></path><path d="M222 554l2-1c6-8 8-18 7-27-1-3-2-6-2-8 2 2 3 9 3 12 0 4 0 9-1 12-2 6-5 13-9 17-1-1 0-3 0-5z" class="R"></path><path d="M197 390c1 1 1 2 2 2h2l1 1h0v-1c0 2 1 4 1 5 1 1 1 2 1 4v1l-2 1-1-1c-3-3-4-7-4-12z" class="B"></path><path d="M201 402h1l-1-5c1 2 2 3 3 4v1l-2 1-1-1zm-5 6v-1l-1-1h2c0 1 1 3 2 4 1 0 1 0 2-1l1 2c1 1 1 2 2 2h1v-1c-1 0-1 0-1-1 2 1 5 5 6 6-1 0-2 0-3-1l-1 1h-1v1c-1-1-2-3-4-4l-1 1c-2-2-3-4-4-7z" class="D"></path><path d="M246 613h1c6-2 11-6 15-10l1 1c-2 2-6 3-7 6-7 6-15 8-23 11l13-8h0z" class="F"></path><path d="M244 237c1-5 3-8 6-11 0 2-2 5-2 8-2 4-2 9-4 13v-1c-1-1-1-2-1-3h-1v1l-1-2 3-5z" class="J"></path><path d="M248 343h1v2l1 3 1 2 3 6c1 1 0 3 1 4 0 2 1 3 1 5v-1c-2-1-4-2-4-4-1-1-4-4-6-5h-2c0-1 1-2 2-3 0 1 0 1 1 2 3 1 4 4 7 5-1-2-3-4-4-6-1-3-1-5-2-7v-3z" class="D"></path><path d="M116 486c1 1 0 2 0 4 0 1-1 3-1 5v4c0 8 1 17 4 24l-1 2v-1c-2-3-2-6-3-9-1-6-3-13-1-20 0-3 1-6 2-9h0z" class="W"></path><path d="M116 486c1 1 0 2 0 4 0 1-1 3-1 5v4c0 1 0 2-1 3v-1-6c0-3 1-6 2-9h0z" class="Z"></path><path d="M355 614l1 1v1c-5 9-19 14-29 16h0 0c3-2 6-3 9-4 7-3 14-8 19-14z" class="S"></path><path d="M265 259h1 1c1 0 1 1 1 1h2c3 1 7 2 10 2 0-1 1 0 1 0 3 0 4-2 6-3l1 2c-1 0-4 1-6 1h0c-1 1-3 0-4 1-2 1-4 1-6 1h1c-2-1-4-1-6-1-1-1-3-1-5-2h-8c1-1 3-1 4-1 2 0 5 0 7-1h0z" class="G"></path><path d="M130 555l1-1 1 1h1l12 6c4 2 8 5 12 5 4 1 8 1 12 1-1 0-5 0-6 1-2 0-4-1-7-1-1-1-2-1-3-1-2 0-5-2-8-2-1 0-3-1-4-2h0c-1-1-2-1-3-2-1 0-2-1-4-2-1-1-3-2-4-3z" class="L"></path><path d="M163 504h2 0c0 1-1 3-1 4h1v-1c1-1 0-1 1-1 0-1 0-2 1-2h0c-3 7-5 12-5 20 0 1 0 2 1 4h0v1l1 4-1 1c-4-10-3-20 0-30z" class="R"></path><path d="M132 433h0v-1h0 0c1 1 1 1 1 2 2 1 3 3 4 5h0c2 3 4 6 5 9h0-2 0c0 2 1 3 1 4-1 0-1 0-1-1-1-2-2-3-3-4l-2-2c0-1-1-1-2-2h0c1 0 1-1 2-1-1-1-2-3-2-4 0-2 0-3-1-5z" class="N"></path><path d="M135 442c2 1 3 3 3 5h0-1l-2-2c0-1-1-1-2-2h0c1 0 1-1 2-1z" class="L"></path><path d="M451 153h1c0 1-1 4 0 5 0 1 1 2 2 3 1 0 1 1 2 2 0 0 0 1-1 1 0 1-1 2-2 2l-1-1h0l-1 1v1c-2 0-2 0-2-1-1-1-1-2-1-3 0-3 1-8 3-10z" class="U"></path><path d="M308 254c2 3 5 6 9 8-1 1-1 1-3 2h-2 0-1-1v1c0-1-1-1-1-2s-1-1-2-1-1 0-1-1c-1-1-1-2-1-3s0-1-1-2c1 0 3-1 4-2z" class="C"></path><defs><linearGradient id="J" x1="188.672" y1="442.216" x2="190.355" y2="450.05" xlink:href="#B"><stop offset="0" stop-color="#292828"></stop><stop offset="1" stop-color="#414242"></stop></linearGradient></defs><path fill="url(#J)" d="M180 444h9c4 1 10 1 14 3l3 1v1c-9-2-18-2-27-2 1 0 2 0 2-1h-4c-1-1-3-1-5-1l8-1z"></path><defs><linearGradient id="K" x1="236.61" y1="534.755" x2="232.221" y2="545.067" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#3a393a"></stop></linearGradient></defs><path fill="url(#K)" d="M232 530c2 2 2 3 2 5 1 1 2 3 3 4v-1-1-2c1 5 1 10 0 15-2-2-1-5-2-7l-1-1h-1v3h-1l-1 1h0v-1h0v-3c1-3 1-8 1-12z"></path><path d="M146 421c0-6-1-12-1-18h1 0c4 3 3 10 3 14l1 2-1 1v3c1 1 1 2 1 2v1l1 1v2l1 1v1l-2-2h0c0-1-1-1-1-2h0v-2l-1-1v-1l-1-4h0l-1 2z" class="B"></path><path d="M320 259h-2c-2 0-5-2-6-4s-2-4-1-6c1-1 3-3 5-4 1 4 1 8 3 12l-1 1h1l1 1z" class="Y"></path><path d="M238 257h1c-1 1-3 3-2 4h0c1 1 1 1 2 1l5-4c0 2-2 2 0 3h0l1 1c-1 1-3 4-4 4s-2 2-3 3c-2 4-4 7-6 11h0c1-3 2-7 4-10 2-2 3-4 4-6h0c-2 1-4 3-5 5 0 1-1 2-2 3l1-1v-1h1l-1-1h-1l3-3-1-1v-2h0l-1-1c1 0 1-1 1-1v-1c0-1 2-2 3-3z" class="J"></path><path d="M534 205h0l3-3c2 2 4 3 4 6v1h0l1 1c-1 1-1 2-2 3h-2l-1-1v-2c-2 0-3 1-4 1s-2-1-2-2c0-2 1-3 2-5l1 1z" class="Y"></path><path d="M534 205h0l3-3c2 2 4 3 4 6v1h0-1c-1-2-1-3-3-4h0v2l-1 1c-1 0-2 0-3-1 0-1 1-1 1-2z" class="P"></path><path d="M235 323c2 0 2 1 3 1 0 1-1 1-2 2h0 0c4 3 7 12 8 17h0c-1 0-1-1-2-1h0 0c-1-4-2-7-4-10-1 1-1 4-1 5-1 1-1 1-1 2l-2-6c1-1 1-1 1-3-1-2-3-1-5-3h0 2 1c1 1 2 1 3 2v1l1-1c0-2-2-2-3-3h-2c-1-1-1-1-3-1 2 0 4-1 6-1v-1z" class="G"></path><path d="M188 476c15-4 30-2 44 5h-1c-1 0-2 0-3-1-3-1-6-2-10-2l-13-2c-4-1-8 0-12 0-1 1-3 1-5 1v-1z" class="R"></path><path d="M263 623l2-1c1-1 2-2 3-2 2-2 4-4 5-6l1-1 3-3c2 0 3-1 4-2-1 5-6 10-10 13h0v1h2l-10 3-1-1 1-1z" class="H"></path><path d="M263 623c2 2 4-3 7-3v1l-1 1h0 2 2l-10 3-1-1 1-1z" class="O"></path><defs><linearGradient id="L" x1="243.253" y1="251.153" x2="229.707" y2="251.484" xlink:href="#B"><stop offset="0" stop-color="#0b0a0a"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#L)" d="M242 238l2-1-3 5 1 2v-1h1c0 1 0 2 1 3v1c-1 2-5 5-7 7-3 2-5 5-7 7v-1c1-1 2-4 3-6h0c0-1 1 0 2-1 0-1 1-2 2-3h1l-2-2v-1c1-1 2-1 2-3h0 1 1l2-3v-1-2z"></path><path d="M241 282l6-10h0c-1 4-3 7-5 11-4 7-7 15-7 24-1 3-1 7 1 10 0 1 1 2 1 3v1c-3-3-4-6-5-9 0-8 2-15 5-22l3-6 1-1v-1z" class="F"></path><path d="M275 310h1c0-2 0-4 1-5h1c0 2-1 5-2 7v9l1 6h-1v2h-1c-1-1-1-5-2-7 0-1 0-2-1-3h0c0-2-1-4 0-6l1-5 1 1h0c0-1 0-3 1-3v3 1z" class="J"></path><path d="M276 327h0c-1-2-1-4-2-6 1 0 1 0 1-1l1 1 1 6h-1z" class="E"></path><path d="M274 309h0c0-1 0-3 1-3v3 1c-1 2 0 6-2 8v-1c0-2 0-5 1-8z" class="L"></path><path d="M217 319c2 3 5 5 10 6 2 1 3 1 5 1h2c1 1 3 1 3 3l-1 1v-1c-1-1-2-1-3-2h-1-2 0c2 2 4 1 5 3 0 2 0 2-1 3l-3-3c-1 0-3 0-4-1h-1v1c-1 0-2-1-3-1v-1c-3-2-6-5-7-7 0-1 0-1 1-2zm65-123c0-2 1-4 1-7h1c0-1 1-4 3-5 1 0 2 1 4 0v-1h-1-1l3-2v3c-1 1-1 4-1 5-2 0-4-1-6 0 2 1 3 1 5 1 1 1 0 3 0 4h-2l-1 1h2v1h-7z" class="D"></path><path d="M290 190c1 1 0 3 0 4h-2 1l1-1c-1-1-3-1-4-2h4v-1z" class="H"></path><path d="M258 341h1l8 21 1 17-1-1v1l-1-1v-5s0-1 1-2c0-1-1-4-1-5l-2-2-4-14c0-2-1-6-2-9z" class="F"></path><path d="M266 373s0-1 1-2v7 1l-1-1v-5z" class="N"></path><path d="M274 412h1 0c1 1 2 1 3 1 0 3 0 4 1 6-1 3-2 6-2 9 0 4-1 6-2 9h-1c-1-1-2-2-2-4h1c0-1 0-2 1-3 1-3 1-8 1-12l-1-1v-5z" class="E"></path><path d="M274 412h1v6l-1-1v-5z" class="M"></path><path d="M146 421l1-2h0l1 4v1l1 1v2h0c0 1 1 1 1 2h0l2 2v-1l-1-1v-2l-1-1v-1s0-1-1-2v-3l1-1 3 9c0 2 1 4 2 5s0 1 1 1v2c1 0 1 1 1 2h-1l-1-1h0c1 1 1 1 1 2l-1 1-1-1v1h-1c-1-1-2-2-2-4-3-5-4-10-5-15z" class="I"></path><path d="M483 609c2 0 5 4 7 5 3 3 6 6 10 8 4 3 8 4 13 6h-1l-1 1c-5-1-10-1-14-3 0-1 1 0 2-1-1-1-2-2-3-2l-8-7c-1-1-3-3-4-5 0 0-1-1-1-2z" class="S"></path><path d="M306 284l1 1-28 11c-3 1-7 1-10 3-1 0-1 1-2 2 0 2 0 4 1 6v4l3-3c0 2 0 6-2 8h0c0-1-1-4-2-5s-1-2-1-4c0-1-1-1-1-2s0-1 1-1v-3l1-2c1-1 5-2 6-3l11-4 22-8z" class="J"></path><path d="M126 419h1c1 3 1 6 3 9v1c1 2 1 3 2 4 1 2 1 3 1 5 0 1 1 3 2 4-1 0-1 1-2 1-2-2-3-4-4-6s-2-4-2-7h0c-1-1-1-2-2-3-1-2-1-5-1-8h1v2c1-1 1-2 1-2z" class="C"></path><path d="M126 419h1c1 3 1 6 3 9v1c1 2 1 3 2 4 1 2 1 3 1 5-1-2-3-4-4-6-1-4-3-8-4-11 1-1 1-2 1-2z" class="L"></path><defs><linearGradient id="M" x1="251.229" y1="627.712" x2="260.997" y2="634.955" xlink:href="#B"><stop offset="0" stop-color="#232223"></stop><stop offset="1" stop-color="#424142"></stop></linearGradient></defs><path fill="url(#M)" d="M262 629v-1c2 0 4-1 6-1l-4 3-1 1c0 1 0 2-1 3-2 1-4 2-5 2-3-1-8-1-9-3h0v-1h-1v-1c3 0 6-1 9-1 2 0 4-1 6-1h0z"></path><path d="M279 625l-4 1c0-1 1-2 2-3 2-2 2-4 4-6 1 0 1 0 3 1 1 1 2 2 3 4 0 2 0 4-1 6l-4-1c-2 1-2 0-3 2-2-1-4-1-5-1 1-1 4-1 5-3h0z" class="C"></path><path d="M279 625v-1c0-1 1-1 2-1h1v1c0 1-1 1-1 1h-2 0z" class="K"></path><path d="M281 625h3c-1 1-2 1-3 2h1c-2 1-2 0-3 2-2-1-4-1-5-1 1-1 4-1 5-3h2z" class="L"></path><path d="M287 622c-1 1 0 2 0 3h-1c-2-1-2-1-3-3-1 0-1-1-2-1v-1c1-1 1-2 3-2 1 1 2 2 3 4z" class="K"></path><path d="M92 594c-3-4-5-8-7-12-3-6-6-13-8-19-1-2-2-4-2-6h1c1 2 1 5 2 7 1 3 2 6 4 8 2 5 5 9 8 14 2 3 4 7 7 10 2 3 4 6 6 8l-1 1-6-8c-1-1-3-2-4-3z" class="T"></path><path d="M236 339c0-1 0-1 1-2 0-1 0-4 1-5 2 3 3 6 4 10h0c0 3 1 6 1 9v3 2c-1 0-1 1-1 2h0c-2-3-2-7-4-10 0-3-2-6-2-9z" class="I"></path><path d="M243 354c-1 0-2-1-2-1v-2h2v3z" class="M"></path><path d="M273 593l4-4h3c-3 5-8 9-13 13-3 3-7 5-11 8 1-3 5-4 7-6l-1-1c-4 4-9 8-15 10h-1c6-4 12-7 17-12l6-3c1-2 3-3 4-5z" class="V"></path><defs><linearGradient id="N" x1="307.574" y1="247.775" x2="299.705" y2="240.055" xlink:href="#B"><stop offset="0" stop-color="#1b191b"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#N)" d="M303 242c2 0 4-1 6-1l2 1h0l2 1c0 1 0 1-1 2-1 0-2 1-3 1l-2 2c-1 0-1 0-2 1h-2c-1-1-2-1-2-2h-1-1s-1 0-1 1l-5 1h-1-3 1c2-1 5-1 6-3h0l-1-1h1v-1h1 0c1-1 0-1 1-1s2 0 3-1h0l1 1 1-1h0z"></path><path d="M299 247v-1c2-1 2-1 4-1l-1 1c1 1 1 1 2 1h-4-1z" class="B"></path><path d="M305 245l4-2 3 2c-1 0-2 1-3 1h0v-1h-4z" class="L"></path><path d="M305 245h4v1h0l-2 2c-1 0-1 0-2 1h-2c-1-1-2-1-2-2h-1 4c-1 0-1 0-2-1l1-1h2z" class="G"></path><path d="M305 245h4v1l-5 1c-1 0-1 0-2-1l1-1h2z" class="I"></path><path d="M278 597h1c-4 6-7 12-11 16-2 1-3 1-5 2-2 2-6 4-8 6-1 0-2 1-3 1h0c-1 1-2 1-3 1h-4c1-1 2-1 3-1l4-2c2-1 5-3 6-5 8-5 14-10 20-18z" class="R"></path><path d="M233 242l3-3h1c0 1-2 2-2 4h0c1-1 2-1 3-2h0c1-2 3-3 2-5h0c2-1 2-4 3-5-2 2-3 3-5 4h-1l3-2v-1c0-4 5-9 7-11 1 0 2-1 2-1-1 4-3 8-5 12-1 0-1 1-1 1v3h0l-1 2c-1 1-2 3-4 5-2 1-3 3-5 4l2-2v-1c-1 1-2 2-3 2-1 2-3 3-5 4-1 1-2 3-4 3l10-10v-1z" class="H"></path><path d="M172 505h1 1l-2 4c-5 9-8 18-4 28 1 5 3 9 8 11 6 3 11 3 18 3 1 0 2 0 2 1-6 2-14 0-20-3s-10-9-13-15l1-1-1-4c1-2 0-6 1-8v3l1 1c0-2 1-4 1-7 2-4 4-9 6-13z" class="Z"></path><path d="M163 529c1-2 0-6 1-8v3l1 1v9l-1-1-1-4z" class="F"></path><path d="M139 417v-1c0-5 1-11 1-16 0 1 0 2 1 3-1 4-1 9-1 13v1 2h1s1 1 2 1l-1 1-2-2c1 7 3 14 6 20 0 1 0 2 1 3h0v1l-3-3v1c0 1 1 1 1 2h-1-1c-1-2-2-6-2-8-2-6-3-12-3-18 0 1 1 2 1 2 1-1 0-1 0-2z" class="L"></path><path d="M109 491v-2c2 2 1 7 1 10-1 6-2 12-2 18 0 2 0 3 1 4s3 5 3 7v1c0 1 1 3 2 3 1 3 3 6 5 8l4 5h-1c-2-1-4-4-5-6l-1 1c-5-6-9-15-9-22-1-8 1-15 1-22 1 0 1-5 1-5z" class="K"></path><path d="M108 517c0 2 0 3 1 4s3 5 3 7v1c0 1 1 3 2 3 1 3 3 6 5 8l4 5h-1c-2-1-4-4-5-6-4-4-7-9-8-15-1-3-1-4-1-7z" class="G"></path><path d="M249 577h1c-9 11-20 17-33 23-1 1-3 2-4 2-2 1-4 1-6 1l-12 2c-1 0-1 0-1-1 6-1 12-3 19-5 13-4 26-10 35-22h1z" class="R"></path><path d="M171 428l-3-3-3-3h0c-2-1-2-5-3-7l-1-1 1-1 1 1h3c1 3 3 6 6 8 2 2 4 3 6 4 1 0 3 0 4 1-2 1-7 3-9 2 0 0-1 0-1-1h-1z" class="B"></path><path d="M163 414h3c1 3 3 6 6 8v1c0 1 4 3 4 4-1 0-1 0-2-1-5-2-8-7-11-12z" class="G"></path><path d="M174 426c1 1 1 1 2 1 0-1-4-3-4-4v-1c2 2 4 3 6 4 1 0 3 0 4 1-2 1-7 3-9 2 0 0-1 0-1-1v-1c-1-1-3-2-4-4h0c1 1 2 2 4 3l2 1v-1z" class="E"></path><path d="M537 212l1 1c2 5 1 11-2 15-4 9-10 13-18 16l4-4c4-3 9-8 11-12 3-5 4-11 4-16z" class="S"></path><path d="M257 323c0-1 0-2 1-2 1 1 1 2 1 4l2 4v2 1c1 3 3 5 5 7h0v-1c0-1-1-1-1-2h0l-1-1v-1-4-1l3 6c1 1 2 3 2 4h-1v1h0c0 1 1 1 1 2 2 2 2 4 3 6-1 0-3-2-4-2-1-1-2-4-3-4l-2-2c-2-1-4-3-5-5 0-3-1-5-2-7l2-1-1-4z" class="O"></path><path d="M256 328l2-1c1 4 3 9 6 12l-1 1c-2-1-4-3-5-5 0-3-1-5-2-7z" class="I"></path><defs><linearGradient id="O" x1="295.016" y1="586.516" x2="289.554" y2="578.176" xlink:href="#B"><stop offset="0" stop-color="#322f33"></stop><stop offset="1" stop-color="#525350"></stop></linearGradient></defs><path fill="url(#O)" d="M294 575h0c2-1 2-3 3-4 1-4 3-8 4-12 0 0 1-1 1-2h0 0l-11 34h-2l-1 1-1-1c-1-2 0-5 1-7h0l1-5 1-2 1-1c1-1 1-3 2-4v-1 4h1z"></path><defs><linearGradient id="P" x1="294.117" y1="586.405" x2="286.912" y2="574.274" xlink:href="#B"><stop offset="0" stop-color="#413d40"></stop><stop offset="1" stop-color="#606160"></stop></linearGradient></defs><path fill="url(#P)" d="M290 577l1-1c1-1 1-3 2-4v-1 4c0 3-1 5-2 8-1 2-1 5-3 7h0c-1-2 0-4 0-6l1-5 1-2z"></path><path d="M209 399h0v2c1 0 1 0 2 1l2 4 1-1-2-4h1v1h1v-1c1 1 2 2 2 3h-1v2l1 1h-1-1v1 3h0v4c-2-1-4-3-6-4-1 0-1 0-1-1l-4-5h0v-1h1v-1c1-1 2-2 4-3l1-1z" class="I"></path><path d="M213 402h1v-1c1 1 2 2 2 3h-1v2l-2-4z" class="J"></path><path d="M209 401c1 0 1 0 2 1-2 1-2 2-2 4l-1 1-2-1v1c-1-1-1-1-1-3 2-1 3-2 4-3z" class="E"></path><path d="M209 399h0v2c-1 1-2 2-4 3 0 2 0 2 1 3 0 1 1 2 1 3l-4-5h0v-1h1v-1c1-1 2-2 4-3l1-1z" class="H"></path><path d="M329 260c2 3 3 5 6 7h0v2c0 1 1 1 0 2-2 1-2 1-4 1-3 0-5 0-8 1h-1 0-3c-1 0-2 1-3 1-1-1-2 0-4 0v-1c1-1 2-2 2-3h0 1v2h2 0c1-1 2-2 3-2v-1h1 0 1c1 0 3-1 4-1v-2c1 0 2 0 3-1v-1-4z" class="N"></path><path d="M335 271c-1 0 0 0-1-1s-2-2-4-3l1-1h1c1 1 1 1 3 1h0 0v2c0 1 1 1 0 2z" class="D"></path><path d="M317 272l4-1c1 0 2-1 3-1 2 0 5-1 6 0v1c-3 0-5 0-9 1 1 1 0 1 1 1h0-3c-1 0-2 1-3 1-1-1-2 0-4 0v-1c1-1 2-2 2-3h0 1v2h2 0z" class="H"></path><path d="M165 377c-1 0-1-1-2-2l1-1 1 1v1c1 1 1 2 2 3h0c1-1 1-2 1-3l1 2h0v4c-2 4-1 11 0 15 1 1 1 3 1 4h1c0 1 1 2 1 2 2 1 4 0 4 3h-3 0l-1-1c-1 0-1 0-1-1h0l-3-4c-2-3-3-5-4-8s0-7 0-10c0-2 0-3 1-5z" class="Q"></path><path d="M165 377c-1 0-1-1-2-2l1-1 1 1v1c1 1 1 2 2 3h0c1-1 1-2 1-3l1 2h0v4c-2 4-1 11 0 15 1 1 1 3 1 4-3-6-3-13-3-19v-1l-2-4z" class="M"></path><path d="M278 581h2v1 1h0c-1 2-1 3-3 4 0 1-1 1-2 2 0 1-2 2-2 4-1 2-3 3-4 5l-6 3h-1c-5 4-12 8-18 12-3 2-7 5-11 5 0-1 1-1 2-1l5-3c15-8 30-19 38-33z" class="K"></path><path d="M280 583h0c-1 2-1 3-3 4 0 1-1 1-2 2v-1c1-2 3-4 5-5z" class="B"></path><path d="M275 588v1c0 1-2 2-2 4-1 2-3 3-4 5l-6 3h-1c2-2 3-3 5-4 3-3 5-6 8-9z" class="E"></path><path d="M246 522l3 1c4 8 6 18 3 27 0 1 0 2-1 2-2 5-3 11-6 15-2 6-8 12-13 15v1l-1-1c5-4 8-9 11-14l1-1c1 0 1-1 1-1 1-1 2-2 2-3 1-3 3-6 3-8v-1c1-4 2-9 2-13 0-3-1-6-1-9v-1-1c0-1-1-2-1-3-1-2-2-3-3-5z" class="F"></path><path d="M317 262h1l1 1c1 2 2 3 2 6h-1v1c-1 0-2 1-3 2h0-2v-2h-1c-5 1-11 4-16 5 1-2 3-3 5-5h0v-1l9-5h0 2c2-1 2-1 3-2z" class="J"></path><path d="M320 270v-1h-4l-1-1h-3-1c1-2 2-2 3-3 2-1 3-1 5-1 1 1 1 3 1 4v1 1z" class="E"></path><defs><linearGradient id="Q" x1="253.744" y1="415.278" x2="249.898" y2="430.657" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#464546"></stop></linearGradient></defs><path fill="url(#Q)" d="M247 410c1 2 2 3 3 4h1c0-2 0-2 1-3h0 1v1l1 1v9s-1 1-1 2l1 3c0 2 1 4 2 6h0c-2 0-3-1-4-1-1-1-2-1-3-1 0 0 0-2-1-2h0l-1-2v-2l1-3v-2c0-2 0-4 1-6l-3-3 1-1z"></path><path d="M251 414l1 1v6 1l-1-2v1-7z" class="B"></path><path d="M252 411h1v1l1 1v9s-1 1-1 2l-1-3v-6l-1-1c0-1 1-2 1-3h0z" class="N"></path><path d="M252 411h1v1c0 1-1 2-1 3l-1-1c0-1 1-2 1-3h0z" class="E"></path><path d="M195 382h1v-5c0 1 1 1 1 2h0c2 2 1 5 1 7 0 0 0 1-1 1h0v1c-1 2-1 4-1 6-1 0-1 1-1 1 0 3-2 4-1 7h0v1c-1 0-1 0-2-1 0-1-2-4-2-6 0-4 0-7 1-11 1-2 1-3 2-4s1-2 1-3c1 1 1 2 1 2v1 1z" class="B"></path><path d="M197 379c2 2 1 5 1 7 0 0 0 1-1 1h0v1l-1 1c0-3-1-8 1-10z" class="C"></path><path d="M194 378c1 1 1 2 1 2v1 1c-3 5-4 12-3 17 1 1 1 2 2 3v1c-1 0-1 0-2-1 0-1-2-4-2-6 0-4 0-7 1-11 1-2 1-3 2-4s1-2 1-3z" class="H"></path><path d="M256 351c-2-5-3-11-5-16v-1c2 3 3 5 4 8 0 2 1 4 2 5s1 2 2 2v2s1 0 1-1l4 14 2 2c0 1 1 4 1 5-1 1-1 2-1 2v5l-1 1c-1-2-2-4-3-7 0-3 0-6-1-10s-3-7-5-11z" class="I"></path><path d="M259 351s1 0 1-1l4 14 2 2c0 1 1 4 1 5-1 1-1 2-1 2h-1v2h-1c-1-3-1-8-2-11-1-4-2-9-3-13z" class="O"></path><path d="M264 364l2 2c0 1 1 4 1 5-1 1-1 2-1 2h-1v2h-1c2-2 1-9 0-11z" class="E"></path><path d="M171 562c-4 0-9 0-13-1-13-2-27-10-35-20-2-3-5-9-5-11 1 1 2 3 3 5 1 3 3 5 6 8 0 1 1 2 2 3h2c2 2 4 4 8 5 4 2 8 4 13 5l9 3c-2 0-4 0-6-1s-4-1-6-1c-3-1-5-3-8-4v1s1 0 2 1c4 2 9 4 14 5h6 7c0 1 0 1 1 2z" class="T"></path><path d="M253 306c1-7 3-14 7-20-1 2-2 4-2 6l-3 8c-1 3-2 9-2 12 2-2 2-6 2-8 1 1 1 2 1 3s1 1 1 1v7c-1 2-1 5 0 8l1 4-2 1c-1-3-1-7-2-10h0v5l1 2c-1 1-1 2-1 3l-3-10c0-1-1-1-1-2l1-1v-10l1 1h1z" class="B"></path><path d="M250 316l1-1v-10l1 1h1l-2 12c0-1-1-1-1-2z" class="O"></path><path d="M215 549l7-13c0 2 0 5-1 7 0 2-1 4-2 6-1 1-1 2-1 3 2-3 4-5 5-8h0c0 3-2 6-4 9 0 1-2 3-2 4l1 1c-1 1-1 2-2 2l-1-2c-3 0-5 4-8 5l-1-1-7 4-1-1c3-1 6-3 9-5 1 0 1-1 2-2h1c1-1 1-1 1-2-1 0-2 1-3 2h-1l1-1c2-2 5-4 7-6v-2z" class="B"></path><path d="M265 218v-2h2v-1-1l1-2c0 2-2 11-3 13v16l8 1h0l-1 1c1 1 2 1 3 2h0c-1 0-1 0-1 1h2l-1 1c-5-1-9-1-14-1l-1-1v-1h0c2-1 2-3 2-5v-3-6c0-2 1-10 2-11h1v-1z" class="E"></path><path d="M262 236v-6c0-2 1-10 2-11h1v-1c-1 5-1 10-1 15-1 3 0 5 0 8-1-2-1-5-1-7l-1 2z" class="D"></path><path d="M262 236l1-2c0 2 0 5 1 7 0 1 0 2 1 3 3 1 6 1 9 2h2l-1 1c-5-1-9-1-14-1l-1-1v-1h0c2-1 2-3 2-5v-3z" class="Q"></path><path d="M278 400c1 0 1 0 2 1h1 0c1 0 1 0 1 1h1 1 0 0l-1 1h1 0v1c-1 1 0 4 0 6l1 7h-1v4-1-1c-1-2-1-3-1-5 0-1 0-2-1-2v-1c0 1-1 1-1 1l-1 1c0 1 0 1 1 1 0 2-1 6 0 8 0 1 0-1 0 1h0v-2c1 0 1-1 1-1v-1l1 1v1c-1 1-1 1-1 2v1h-1c-1-2-1-6-1-9h-1v1 1 2c-1-2-1-3-1-6-1 0-2 0-3-1h0-1v-1c0-1-2-2-3-3h-1v-2l3 3c1-1 1-2 1-3 1 1 1 1 1 2h1v-1c0-2 1-4 2-7z" class="J"></path><path d="M284 417c-1-1 0-2-1-3 0-3-2-5-1-8l1-3v6l1 1 1 7h-1zm-8-10h1v1 3l1-1c0-2 0-5 1-7h1c-1 5-2 10-1 14v2c-1-2-1-3-1-6-1 0-2 0-3-1h0-1v-1c0-1-2-2-3-3h-1v-2l3 3c1-1 1-2 1-3 1 1 1 1 1 2h1v-1z" class="B"></path><path d="M276 410c0 1 1 0 2 1v2c-1 0-2 0-3-1l1-2z" class="J"></path><path d="M270 406l3 3c1 0 1 1 2 1h1 0 0l-1 2h0-1v-1c0-1-2-2-3-3h-1v-2z" class="C"></path><defs><linearGradient id="R" x1="250.236" y1="297.699" x2="244.736" y2="296.987" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#3e3e3d"></stop></linearGradient></defs><path fill="url(#R)" d="M251 279c0-2 1-4 3-5l-3 6 1 1 1-1 1 1c-3 7-6 14-7 21-1 3-1 5-1 7 0 3 0 7-1 9-2-5-2-11-1-17 0-3 1-6 2-9h-1c0-1 1-2 0-3 1-3 4-7 6-10z"></path><path d="M246 292s1-1 1-2l1 1h0c-1 2-1 3-1 5-1 2-2 4-2 5h-1c0-3 1-6 2-9z" class="C"></path><path d="M251 279v1c-1 4-2 7-3 11l-1-1c0 1-1 2-1 2h-1c0-1 1-2 0-3 1-3 4-7 6-10z" class="B"></path><path d="M176 389c1-1 1-2 1-3h1v3c0 1 1 4 2 5l2-2c0 3-2 9 0 12v1l2 1c1-1 0-2 0-3 1 2 2 4 4 5 0 1 1 2 2 2h1c2 4 6 7 9 10l-1 1v-1c-3 0-5-1-8-3-4-4-8-8-11-14 0 0-1-1-1-2l-2-1-1-3h0c1-1 0-5 0-7v-1z" class="E"></path><path d="M176 389c1-1 1-2 1-3h1v3c-1 4-1 7 1 11v1l-2-1-1-3h0c1-1 0-5 0-7v-1z" class="J"></path><path d="M557 198h0 0c-1 4 0 6 1 9l1 1c-1 1-2 1-3 2 0 2-2 6-1 7h1 1c-1 2-4 3-5 5v1c0 2 0 4-1 6h-1c-1-1-2-1-2-2 1-4 3-7 5-11 3-6-2-18-4-24l2-1c2 0 2 0 4 1v2c0 3-1 4-2 5 0 3 1 5 2 7h1v-4-6s1 1 1 2v-1 1h0z" class="U"></path><path d="M257 323c-1-3-1-6 0-8v-7s-1 0-1-1 0-2-1-3c0 2 0 6-2 8 0-3 1-9 2-12h1l2 2v6-2l1 1v1-4l2-1c0 2-1 4-1 5l1 1c1 2 0 3 0 4v7l-1 1c1 1 1 2 1 3h1l1-1h0c0 1 0 2 1 4v2 1 4 1l1 1h0c0 1 1 1 1 2v1h0c-2-2-4-4-5-7v-1-2l-2-4c0-2 0-3-1-4-1 0-1 1-1 2z" class="D"></path><path d="M260 308l1 1c1 2 0 3 0 4v7l-1 1v-13z" class="I"></path><path d="M235 321l7 4-2-4c-6-12-1-23 2-34 2-5 5-11 8-15 1 1-2 6-3 7-4 9-9 20-8 30 0 2 0 4 1 7v1 2h-1c2 1 1 0 1 1l1 1v1h1c0-1 0-2-1-2v-1-1l-1-1h0v-2h0c0-3-1-7 0-10 0 6 0 14 4 19h2 0c3 3 5 6 7 9l-8-4c-2-2-4-4-7-5-1 0-1-1-3-1h-1v-1h0l-1-1v-1c1 0 2 1 2 1z" class="L"></path><path d="M168 504c1 0 1-2 1-2 1-2 3-4 4-5 2-2 3-4 5-5h0c-1 2-2 4-3 5s-1 1-1 2v1h1v1c0 1-1 2-1 4h0-1-1c-2 4-4 9-6 13 0 3-1 5-1 7l-1-1v-3c-1 2 0 6-1 8v-1h0c-1-2-1-3-1-4 0-8 2-13 5-20h1z" class="C"></path><path d="M162 524h0l1-1v-3l1-1 1 1-1 1c-1 2 0 6-1 8v-1h0c-1-2-1-3-1-4z" class="M"></path><path d="M168 504c1 0 1-2 1-2 1-2 3-4 4-5 2-2 3-4 5-5h0c-1 2-2 4-3 5s-1 1-1 2v1h1v1c0 1-1 2-1 4h0-1-1 0c-2 2-5 7-6 10v1h-1c1-1 1-3 1-4 1-2 3-5 4-7l-1-1-2 6c-1 0-1 1-1 1h-1l3-7z" class="K"></path><path d="M218 164c3 3 5 6 7 9 0 1 1 3 2 5 3-3 5-6 6-11-3 0-7 0-9-2-1-2 0-6-1-8 0-1-2-3-3-4h0l1-1c2 1 5 4 7 6-1 2-1 5 0 7h4c1 0 1-1 1-1 1-1 1-2 0-3 0-1-1-2-2-3h0c1 0 2 0 4 1 1 1 1 3 1 5 0 3-1 6-2 8-2 3-4 5-6 8l-1 2c0 2 1 4 1 5v8h0c-2-4 0-7-1-11h0v-2-1h0c0-1 1-1 1-2v-1h1c1-2 0-1 1-1h0c0-1 1-2 1-3v1c-1 1-2 2-4 3h-1l-1-2v-1h0c-1-2-2-4-3-5l-1-1s0-1-1-1l-3-3 1-1z" class="S"></path><path d="M170 441l2-1 7-1-1 1h-2l3 1c-1 0-3 0-4 1 4 1 8 0 12 1 1 0 3 0 4 1h-2-9l-8 1h-5c-3 1-5 1-8 2-4 1-8 2-12 4v-1l1-1v-1c1 0 1 0 1-1 1-1 2-1 3-2 3-1 6-3 9-3h0c3 0 6-1 9-1z" class="K"></path><path d="M167 445l5-2h5c1 1 1 1 2 1h1l-8 1h-5z" class="F"></path><path d="M161 442h0c3 0 6-1 9-1-4 1-7 2-11 3-3 1-6 3-8 4l-3 1v-1c1 0 1 0 1-1 1-1 2-1 3-2 3-1 6-3 9-3z" class="B"></path><path d="M149 447c1-1 2-1 3-2l-1 2h0v1l-3 1v-1c1 0 1 0 1-1z" class="N"></path><path d="M297 240h2 1v1l-1 1h2 0c-1 1-2 1-3 1s0 0-1 1h0-1v1h-1l1 1c-2 1-4 1-6 1-5 0-9 0-14-1h-2c0-1 0-1 1-1h0c-1-1-2-1-3-2l1-1 3 1h0l-3-2 7 1c2-1 5 0 7-1h0 3c1 0 6-1 7-1z" class="F"></path><path d="M297 240h2 1v1l-1 1h2 0c-1 1-2 1-3 1s0 0-1 1h0-1v1h-1l1 1c-2 1-4 1-6 1 1 0 1 0 2-1v-1c-1-1-3-1-4-2h1-1v-1h-8c2-1 5 0 7-1h0 3c1 0 6-1 7-1z" class="B"></path><path d="M297 240h2 1v1l-1 1c-2 0-4 1-6 1-2-1-3-1-5-1h-8c2-1 5 0 7-1h0 3c1 0 6-1 7-1z" class="N"></path><defs><linearGradient id="S" x1="271.301" y1="639.756" x2="277.652" y2="626.249" xlink:href="#B"><stop offset="0" stop-color="#2e2e30"></stop><stop offset="1" stop-color="#525151"></stop></linearGradient></defs><path fill="url(#S)" d="M282 627l4 1v1c-2 3-4 5-7 6l1 1h1c-7 1-14 2-21 2h-4 0l7-1 1-1v-1h1c3-2 5-5 8-7h1c1 0 3 0 5 1 1-2 1-1 3-2z"></path><path d="M282 627l4 1v1h-7c1-2 1-1 3-2z" class="N"></path><path d="M222 400l1 1 3 9c0 1 0 2 1 4 0 0 1 1 1 2h-1c1 1 1 1 1 2h-1c1 2 2 3 3 4v1c-1-1-3-1-4-2-2-1-3-3-4-4l2 4h0 0c-1 0-1 0-2-1-3-3-5-7-6-12l1-2 1-1h1l1-2h2v-3z" class="F"></path><path d="M218 407h0l2-2 1-1v1s0 1 1 1h0l-1 1c0 2 0 2-1 3h-1v-1l-1-2z" class="B"></path><path d="M222 400l1 1 3 9c0 1 0 2 1 4 0 0 1 1 1 2h-1c1 1 1 1 1 2h-1-1c-3-3-3-7-4-12h0c-1 0-1-1-1-1v-1l-1 1-2 2h0l-1-1 1-1h1l1-2h2v-3z" class="E"></path><path d="M227 416l-3-5c-2-3-2-6-2-10h1l3 9c0 1 0 2 1 4 0 0 1 1 1 2h-1z" class="C"></path><defs><linearGradient id="T" x1="284.255" y1="269.133" x2="296.773" y2="277.609" xlink:href="#B"><stop offset="0" stop-color="#0d0e0b"></stop><stop offset="1" stop-color="#2f2f32"></stop></linearGradient></defs><path fill="url(#T)" d="M293 269h1c1 0 2 0 3-1h0c2-1 4-2 5-3 0-1 0-1 1-1l1-1c1-1 2-1 3-1s2 0 2 1 1 1 1 2v-1h1 1 0 0l-9 5v1c-5 2-9 4-13 6-2 1-5 2-7 2-1 1-3 1-5 1l-1-1c3-2 8-1 10-4h0l1-1h0v-1l-1 1h-2-1c-1 1-2 0-3 1l-3 1h-1-1-1c1-1 1-1 2-1h1l2-1c1 0 1-1 2-1v-1h1v1h0c2 0 5 0 7-1 1-1 2-1 3-2z"></path><path d="M293 269c1 0 1 1 2 1 0 1-1 1-2 2h0c-1 1-2 1-3 2l-1-1c0-1 1-1 1-2 1-1 2-1 3-2z" class="J"></path><path d="M293 269h1c1 0 2 0 3-1h0c2-1 4-2 5-3 0-1 0-1 1-1l1-1c1-1 2-1 3-1s2 0 2 1 1 1 1 2v-1h1 1 0 0l-9 5v-1h-1l-8 4h-1c1-1 2-1 2-2-1 0-1-1-2-1z" class="L"></path><path d="M302 268c1-2 3-4 4-5 2 0 2 0 3 1-1 0-1 0-1 1-2 0-3 2-5 3h-1z" class="N"></path><path d="M169 378v-2l1 1c1 2 0 5 0 7 0 5 1 12 3 17h1l2 2h1l1 1c0 1-1 2-2 2l-2 1-1 1 1 1s0-1 1 0h0c-1 1-1 2-1 2l-1 1-2-1h-1c-1-1-2-2-2-3l-1 1c1 2 2 5 4 7 1 1 2 3 4 4h0l1 1 4 3h1 1c0 1 1 1 2 1h0v1h0c-3 1-3-1-5-2-1 0-2-2-3-2-3-1-5-5-7-7l-3-6c-1-1-1-1-1-2v-1l-1-2h1v-2c2 1 3 2 4 3v1h1 1l-2-2h1 1 0c0 1 0 1 1 1l1 1h0 3c0-3-2-2-4-3 0 0-1-1-1-2h-1c0-1 0-3-1-4-1-4-2-11 0-15v-4h0z" class="D"></path><path d="M171 404h0c0 1 0 1 1 1l1 1h0s0 1-1 1v1c-1-1-1-2-2-2h1l-2-2h1 1z" class="O"></path><path d="M168 408c0-1-1-2-1-3h0v-1c1 1 2 3 4 4h0l1 1h1v-1l1 1s0-1 1 0h0c-1 1-1 2-1 2l-1 1-2-1h-1c-1-1-2-2-2-3z" class="E"></path><path d="M203 375v-1h1v5c1 1 1 1 1 3v4c0 2 0 4 1 6h0 1v1c0 1 1 2 1 3 1 0 1 0 2 1h0 0v2l1 2h1l2 4-1 1-2-4c-1-1-1-1-2-1v-2h0-2c-1-1-2-2-3-4 0-1 0-1-1-1v1c1 0 1 1 1 2h-1c0-1-1-3-1-5v1h0l-1-1h-2c-1 0-1-1-2-2v-3c1 0 1-1 1-1 0-2 1-5-1-7h0v-2h2v-3c1 2 1 4 3 5v-2l1-2z" class="I"></path><path d="M203 375v-1h1v5c-1 3-1 7-1 10h0c-1-2-1-6-1-8 1-2 1-4 1-6z" class="C"></path><path d="M202 390v-1c1 0 1 1 1 2 1 2 2 5 3 7v-6h1v1c0 1 1 2 1 3 1 0 1 0 2 1h0 0v2l1 2h1l2 4-1 1-2-4c-1-1-1-1-2-1v-2h0-2c-1-1-2-2-3-4 0-1 0-1-1-1v1c1 0 1 1 1 2h-1c0-1-1-3-1-5v-2z" class="J"></path><path d="M197 379v-2h2v-3c1 2 1 4 3 5-1 3-2 8 0 11v2 1h0l-1-1h-2c-1 0-1-1-2-2v-3c1 0 1-1 1-1 0-2 1-5-1-7h0z" class="M"></path><path d="M291 561h0c2 2-1 3 2 5-2 2-2 2-2 5 0 1-1 2-1 3-1 1 0 2 0 3l-1 2v-3-1h0l-1 1v-1l-1 3v1l-5 8-2 2h0-3l-4 4c0-2 2-3 2-4 1-1 2-1 2-2 2-1 2-2 3-4h0v-1-1h-2l3-6h1c1-3 3-6 5-10 0 2-1 3-1 4h1v-1c1-1 1-1 2-1 1-1 2-4 2-6z" class="F"></path><path d="M280 583l7-10c-1 5-3 8-5 12-1 0-2 2-3 2 0 1 0 1-1 1l-1-1c2-1 2-2 3-4z" class="R"></path><defs><linearGradient id="U" x1="285.486" y1="260.275" x2="301.583" y2="269.594" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#U)" d="M304 256c1 1 1 1 1 2s0 2 1 3c0 1 0 1 1 1-1 0-2 0-3 1l-1 1c-1 0-1 0-1 1-1 1-3 2-5 3h0c-1 1-2 1-3 1h-1c-1 1-2 1-3 2-2 1-5 1-7 1h0v-1h-1v1c-2 0-2 0-4-1 1-1 2-1 3-1 1-1 2-1 4-2 4-2 7-4 10-6v-1-1c1 0 1-1 2-1 3-1 5-2 7-3z"></path><path d="M277 254h6c3-1 5-2 7-2l-9 3h7c2-1 3-1 5 0 1-1 1-1 2-1-2 2-5 4-8 5-2 1-3 3-6 3 0 0-1-1-1 0-3 0-7-1-10-2h-2s0-1-1-1h-1-1-10v-1c3-1 8-1 12 0 1 0 4 1 6 0h6l-15-3c2-2 7 0 9 0s3 0 4-1z" class="B"></path><path d="M205 496c1 0 3-1 5 0h6l2 1h1l1 2h3l10 4c0 1 0 1-1 1-3-1-6-3-9-3h-1c1 0 2 0 2 1h0-2c-2-1-4-1-6-2-3 0-6-1-9-1l-2 1h3 0c-1 0-1 1-2 1-4 1-8 1-12 3l-1-1h-1-2v-1h-1c2-2 4-3 5-4 2 0 3-1 4-1 3 0 5-1 7-1z" class="R"></path><path d="M218 497h1l1 2-4-1 2-1z" class="M"></path><path d="M210 496h6l2 1-2 1-2-1v-1h-4 0z" class="V"></path><path d="M203 499l1-1h-2v-1h3c2 1 4 1 6 1h-4-2c-1 1-1 1-2 1z" class="T"></path><path d="M192 501c1-1 2-2 4-2h3c-3 1-5 2-7 2z" class="K"></path><path d="M203 499c1 0 1 0 2-1h2l-1 1h1l-2 1c-5 0-8 1-12 3h-1-2v-1l2-1c2 0 4-1 7-2h4z" class="F"></path><path d="M193 503c4-2 7-3 12-3h3 0c-1 0-1 1-2 1-4 1-8 1-12 3l-1-1z" class="Z"></path><path d="M211 498c3 0 8 1 12 3h-1c1 0 2 0 2 1h0-2c-2-1-4-1-6-2-3 0-6-1-9-1h-1l1-1h4z" class="L"></path><path d="M396 628l-1-2 1-1v1h18 10c2 0 5 0 8 1 0 1-1 2-1 3h-42c2 0 5 1 6 0l1-2z" class="U"></path><path d="M409 628l-1-1h8l1 1-1 1c-1 0-1-1-2-1h0c-1 1-3 1-4 1l-1-1z" class="Y"></path><path fill="#a6a3a5" d="M396 628l-1-2 1-1v1h18 10c-3 1-5 1-8 1h-8l1 1c-2 1-2 1-4 1h-1-8v-1z"></path><path d="M197 526v-4c0-2 2-3 3-4 3-2 5-2 8-1s6 4 8 7h-1l-1-1c0 2 1 4 1 6 0 4-2 9-5 11l-2 2c-2 1-4 3-6 4 1-2 2-2 3-3h0c0-1 1-2 2-2 1-2 2-5 2-7v-2c0-3 0-6 1-10h0c-1-1-3-2-4-2h0-2c-2 0-2 0-3 1-2 2-2 4-2 6v-4h-1v-1h-1v4z" class="B"></path><path d="M206 520c1-1 1-1 2 0 1 0 2 0 2 1l1 1c0 1 0 3 1 4 0 1 1 7 0 8-1 3-3 5-4 8-2 1-4 3-6 4 1-2 2-2 3-3h0c0-1 1-2 2-2 1-2 2-5 2-7v-2c0-3 0-6 1-10h0c-1-1-3-2-4-2z" class="C"></path><path d="M268 360c-1-1-1-4-2-6l-3-10h0 0c1 0 1 0 1 1l1 2c1 1 3 1 4 1 1 1 2 2 3 4l1 2-2-2v1l-1 1v1c-1 3 1 8 2 12s2 9 2 13c0-1 0-2-1-3v-2-1-1c0 2 0 4-1 6h0c0 2-1 5-2 6v-1-1c-1-1-2-2-2-3v-1l-1-17 1-1v-1z" class="D"></path><path d="M268 360l1 1c2 6 3 11 3 18 0 2-1 5-2 6v-1-1c-1-1-2-2-2-3v-1l-1-17 1-1v-1z" class="O"></path><path d="M268 360l1 1c2 6 3 11 3 18 0 2-1 5-2 6v-1-1h0c1-7 0-16-2-22v-1z" class="F"></path><path d="M181 635c-4 0-8-1-12-1l-17-3c-8-3-15-6-22-10-3-1-7-3-10-6 7 2 13 7 19 9 4 2 9 3 13 3 10 3 21 4 32 5 5 0 9-1 14-1h2l-1 1c-5 0-12 2-18 1-1-1-3 0-4-1-4 0-8-1-12-1v1h1c1 0 3 0 4 1h3c2 1 5 0 7 1h0 5v1h-4z" class="W"></path><path d="M125 392v1c1 1 1 2 2 4s1 5 1 8l-1-1v6 9h-1s0 1-1 2v-2h-1v-1c-1 1-1 1-1 2h0l-1 4c-1 4-4 7-6 10h0-1c2-4 4-6 5-10 1-1 1-2 1-4l1-9h0c-1-4 1-8 1-11h0l-1 1-1-1h0-1v-1c1-1 1-1 1-3 2-1 3-2 4-4z" class="B"></path><path d="M125 393c1 1 1 2 2 4s1 5 1 8l-1-1v6 9h-1s0 1-1 2v-2c-2-7-2-13 0-20v-6z" class="H"></path><path d="M126 419c-1-6-1-12 0-17l1 8v9h-1z" class="I"></path><path d="M115 481c1 1 1 2 1 4 1-1 1-2 0-3h1c1 2 1 4 1 7 0-2 0-4 1-5h1v3c-1 6-2 11-2 16l1 3c1-1 1-3 1-4h1c0 2 0 4 1 5v4h-1v1-1 3 2h0l-1 2 1 1h-1c0 3 1 7 2 10 0-1 0-1-1-1v2c-1-2-1-4-2-7-3-7-4-16-4-24v-4c0-2 1-4 1-5 0-2 1-3 0-4v-1c-1-1-1-2-1-3v-1z" class="Q"></path><path d="M115 481c1 1 1 2 1 4 1-1 1-2 0-3h1c1 2 1 4 1 7 0 2-1 4-1 7h0l-1-3c1 0 1-2 1-3-1 2-1 4-2 5h0c0-2 1-4 1-5 0-2 1-3 0-4v-1c-1-1-1-2-1-3v-1z" class="N"></path><path d="M117 496h0v8l1-1 1 3c-1 2 0 6 1 8v1 3l1 1h-1c-2-8-3-15-3-23z" class="V"></path><path d="M120 502h1c0 2 0 4 1 5v4h-1v1-1 3 2h0l-1 2v-3-1c-1-2-2-6-1-8 1-1 1-3 1-4z" class="C"></path><defs><linearGradient id="V" x1="122.73" y1="487.159" x2="114.153" y2="496.161" xlink:href="#B"><stop offset="0" stop-color="#535353"></stop><stop offset="1" stop-color="#747375"></stop></linearGradient></defs><path fill="url(#V)" d="M118 489c0-2 0-4 1-5h1v3c-1 6-2 11-2 16l-1 1v-8c0-3 1-5 1-7z"></path><path d="M171 372c2 1 5 1 8 1v2h-1l-1-1c0 2-1 3-1 4 0 2-1 3-1 4v1c0 2 0 6 1 7v-1 1c0 2 1 6 0 7h0l1 3 2 1c0 1 1 2 1 2h-1-1v-1l-1 1h0-1l-2-2h-1c-2-5-3-12-3-17 0-2 1-5 0-7l-1-1v2l-1-2c1-1 1-2 2-3l1-1z" class="J"></path><path d="M175 394c0-3-1-8 0-11 0 2 0 6 1 7v-1 1c0 2 1 6 0 7h0c-1-1-1-2-1-3z" class="E"></path><path d="M172 394v-5l1 1h0c0 2 1 2 2 4 0 1 0 2 1 3l1 3 2 1c0 1 1 2 1 2h-1-1v-1l-1 1h0-1l-2-2c-1-2-1-4-2-7z" class="Q"></path><path d="M177 400l2 1c0 1 1 2 1 2h-1-1v-1l-1 1v-3z" class="H"></path><path d="M170 373h2v2l1 1v-2h1v4c-2 2-2 12-2 15v1c1 3 1 5 2 7h-1c-2-5-3-12-3-17 0-2 1-5 0-7l-1-1v2l-1-2c1-1 1-2 2-3z" class="F"></path><path d="M288 261c4-3 10-4 14-6 2-1 4-2 6-1-1 1-3 2-4 2-2 1-4 2-7 3-1 0-1 1-2 1v1c-3 1-5 2-7 3h0c-4 3-7 5-12 6h-1 0l-1-1-1 1h0-1-4c-3-1-5-1-8-3 3 0 6 1 10 1v-1h-2l-6-3c3-1 7 0 10 0 2 0 4 0 6-1 1-1 3 0 4-1h0c2 0 5-1 6-1z" class="E"></path><path d="M274 269s3 0 3-1h0 2v-1c1 0 2 0 3-1h0c2-2 4-2 6-2-4 3-7 5-12 6h-1 0l-1-1z" class="G"></path><path d="M278 263h4 0c-1 1-2 2-3 2-2 1-3 2-5 2h0c-1 1-1 1-2 0 1 0 1 0 2-1h-2c-1 0-3 0-4 1l-6-3c3-1 7 0 10 0 2 0 4 0 6-1z" class="B"></path><path d="M258 230c1 0 1-1 0-2h0v-1h0v-1-2s1 0 1-1c1 1 1 2 1 3 0-1 0-2-1-3v-1-1 1c-1 0-1 0-1 1h-1v2c-1 1-1 1-2 1h0v1c-1 0-1 1-1 2s0 1-1 2v1 2-1-5-1c1-1 1-1 1-2h0v-1h1v-2h-1v1c-1 1 0 2-1 3v1h-1c2-7 4-14 8-20h0c0 1 0 1-1 2v1l-1 1c0 1-1 1-1 2v1l-1 1h1c1 0 2-1 3-1h0 2c1 1 1 2 2 3h-1l1 1h0v-1c1-1 0-4 0-5l1 1v2h0c1 1 0 3-1 4s-2 9-2 11v6 3c0 2 0 4-2 5h0 0v-1h0l-1-1v-2-1c-1-3-1-5 0-8l-1-1z" class="E"></path><path d="M259 231v-2c1 2 1 7 0 9v1c-1-3-1-5 0-8z" class="B"></path><path d="M259 242h1v-16c1 2 1 3 1 5v8h1c0 2 0 4-2 5h0 0v-1h0l-1-1z" class="J"></path><path d="M222 393c0 1 1 3 1 4 1 0 1 1 2 2v2h0c1-2 1-3 2-5 0 2-1 5 0 6 0 2 2 4 3 5v4h1c0-1 1-2 1-3h1c1 2 3 4 3 5 2 2 4 2 6 4h-1s-1 0-1-1h-1v1h0c1 1 2 1 2 2 1 1 1 3 1 4 0 2 1 5 2 7h0l-1-1c-1-2-2-3-3-4-1-2-1-5-3-7h-1l-1-1v-1h-1l-1 1c0 1 1 2 1 3h-1c-1 0-2 0-2 1-1 0-1 1-1 1-1-1-2-2-3-4h1c0-1 0-1-1-2h1c0-1-1-2-1-2-1-2-1-3-1-4l-3-9-1-1c1 0 1 0 1-1-1-2-1-3-1-6z" class="B"></path><path d="M239 417l-3-2c-1 0-1-1-1-2h1c2 2 4 2 6 4h-1s-1 0-1-1h-1v1h0z" class="D"></path><path d="M222 393c0 1 1 3 1 4 1 0 1 1 2 2v2c1 3 1 6 2 9l3 6h0c-1-2-2-5-4-6l-3-9-1-1c1 0 1 0 1-1-1-2-1-3-1-6z" class="N"></path><path d="M226 410c2 1 3 4 4 6h0c1 1 2 2 3 4-1 0-2 0-2 1-1 0-1 1-1 1-1-1-2-2-3-4h1c0-1 0-1-1-2h1c0-1-1-2-1-2-1-2-1-3-1-4z" class="B"></path><path d="M230 416c1 1 2 2 3 4-1 0-2 0-2 1-1 0-1 1-1 1-1-1-2-2-3-4h1s1 0 2 1h0l1-1c-1 0-1-1-1-2h0z" class="D"></path><defs><linearGradient id="W" x1="287.892" y1="623.092" x2="300.297" y2="620.083" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#W)" d="M290 621c1-3 1-6 3-9h2c3 1 5 3 6 6v4 3l-1 1v1l-4 2c-1 0-3 1-3 2 3 0 7 0 10 1h-9-5c0-2 0-3 1-5v-6z"></path><path d="M295 626l1-1c2-1 3-2 5-3v3l-1 1v1l-4 2c-1-1 0-2-1-3z" class="N"></path><path d="M295 626h2 2 1v1l-4 2c-1-1 0-2-1-3z" class="D"></path><path d="M290 621c1-3 1-6 3-9h2-2l1 1-2 5h1v1c-1 2-1 3-2 5v-1-2h-1z" class="N"></path><path d="M238 280c2-3 4-7 7-10v1l-1 2c0 2 0 3-1 4s-1 2-1 3c-1 1-1 1-1 2v1l-1 1-3 6c-3 7-5 14-5 22v2h0l-1-1h0c1 1 1 2 1 3v1h0c-1 0-1-1-1-1-1-2-1-4-2-7 0-3 0-6 1-9l1-6v-2-1c0-2 2-5 3-7 1-1 1-2 2-3l2-4h0v3z" class="G"></path><path d="M238 283c0 3-1 4-2 7l-1 1c0-3 1-6 3-8z" class="L"></path><path d="M238 280c2-3 4-7 7-10v1l-1 2c-2 3-5 7-6 10-2 2-3 5-3 8-1 3-3 6-4 9h-1 0l1-6v-2-1c0-2 2-5 3-7 1-1 1-2 2-3l2-4h0v3z" class="I"></path><path d="M238 277h0v3c0 1-1 2-1 3h0c-1 1-1 2-2 2l-1-1c1-1 1-2 2-3l2-4z" class="B"></path><path d="M231 292v-1c0-2 2-5 3-7l1 1c-1 2-1 3-2 5 0 1 0 2-1 3l-1-1z" class="E"></path><defs><linearGradient id="X" x1="276.294" y1="539.149" x2="262.157" y2="535.228" xlink:href="#B"><stop offset="0" stop-color="#504f4f"></stop><stop offset="1" stop-color="#6e6f71"></stop></linearGradient></defs><path fill="url(#X)" d="M256 484c10 15 18 33 19 50 0 9-2 18-3 26l-1 3c0-1 0-2-1-2v-1h0l-1-1-2 5v-2-2l3-9c1-7 2-15 2-23 0-2-1-5-2-8-1-5-2-10-4-14-3-8-7-14-11-21l1-1z"></path><path d="M306 548l4-16c0 1 0 2 1 2h0v2c0 1-1 3-1 4 0 2 1 3 1 4l1 1c-1 4-2 8-4 12l-6 18c-1 1-2 4-3 5h0l-2 2v-2-1l-1 2v-1c0-1 0-2 1-3v-1l1-1v-1c0-1 1-1 1-2v-1l1-1v-2c1 0 1 0 1-1h0v-1l1-2v-1c0-1 0-1 1-2v-1-1h0c0-1 0-1 1-2v-1c0-1 0-1 1-2h0v-1-2s1 0 0-1h0l1-2z" class="J"></path><path d="M306 548l4-16c0 1 0 2 1 2h0v2c0 1-1 3-1 4l-5 20c-2 5-3 10-5 15h-1c0-3 2-5 2-7l3-10c1-3 2-7 2-10z" class="C"></path><defs><linearGradient id="Y" x1="180.502" y1="406.108" x2="182.225" y2="421.994" xlink:href="#B"><stop offset="0" stop-color="#353434"></stop><stop offset="1" stop-color="#505051"></stop></linearGradient></defs><path fill="url(#Y)" d="M177 403l1-1v1l1 2c2 6 8 12 14 15 1 1 3 1 5 1l-5 2h-3-2c-3 1-5 1-7-2-2-1-3-2-5-3 0 0-2-3-3-3-1-1-2-3-3-4h1l2 1 1-1s0-1 1-2h0c-1-1-1 0-1 0l-1-1 1-1 2-1c1 0 2-1 2-2l-1-1h0z"></path><path d="M177 403l1-1v1l1 2-1 1v2s-1-1-2-1c-1 1 0 2-1 0h-1l2-1c1 0 2-1 2-2l-1-1h0z" class="G"></path><path d="M173 415v-1h1c0 1 1 1 2 1 0 1 1 1 1 1 2 0 2 1 3 2-1-1-3-1-3 0h-1s-2-3-3-3z" class="M"></path><path d="M176 418h1c0-1 2-1 3 0 1 0 2-1 2 0h0 0s1 0 1 1h0c1 2 5 2 6 3s3 0 4 1h-3-2c-3 1-5 1-7-2-2-1-3-2-5-3z" class="T"></path><path d="M181 421c1 0 1 0 2 1h1s1 0 2 1h0 4-2c-3 1-5 1-7-2z" class="M"></path><defs><linearGradient id="Z" x1="202.309" y1="559.792" x2="167.241" y2="572.194" xlink:href="#B"><stop offset="0" stop-color="#4a4a4b"></stop><stop offset="1" stop-color="#666567"></stop></linearGradient></defs><path fill="url(#Z)" d="M215 549v2c-2 2-5 4-7 6l-1 1h1c1-1 2-2 3-2 0 1 0 1-1 2h-1c-1 1-1 2-2 2-3 2-6 4-9 5l1 1 7-4 1 1c-4 2-8 4-13 6-8 2-20 2-29-1h-2c1-1 5-1 6-1h1c5 0 11-1 16-2 12-2 21-8 29-16z"></path><path d="M251 398h1c0 1 1 1 1 2l1-1h0v-1 1c1 1 2 1 2 2h1 1 0l-1-2h0c1 1 2 3 3 3v-1h1v1c0 2 0 4-1 5h0c0 2-1 3 0 5v2 1l1 3v5h-1v3c0 2 0 3 1 5 1 1 2 2 3 4-2-1-4-3-5-5h0c-2-1-2-2-3-3s-1-1-2-1v1l-1-3c0-1 1-2 1-2v-9l-1-1v-1h-1v-2c0-1 1-1 2-2-1-1-1-3-2-5-1-1-1-2-1-4z" class="E"></path><path d="M254 422c2-2 1-7 2-9 0 3-1 6 0 9v1 4c-1-1-1-1-2-1v1l-1-3c0-1 1-2 1-2z" class="D"></path><path d="M251 398h1c0 1 1 1 1 2l1-1h0v-1 1c1 1 2 1 2 2h1v2l-1 1c-1 2 0 7 0 9-1 2 0 7-2 9v-9l-1-1v-1h-1v-2c0-1 1-1 2-2-1-1-1-3-2-5-1-1-1-2-1-4z" class="H"></path><path d="M254 399c0 2 1 3 1 5h0c-1-1-1-3-2-4l1-1z" class="N"></path><path d="M254 407v6l-1-1v-1h-1v-2c0-1 1-1 2-2z" class="D"></path><path d="M257 423c0-4 0-17 2-20h1v4c0 2-1 3 0 5v2 1l1 3v5h-1v3c0 2 0 3 1 5 1 1 2 2 3 4-2-1-4-3-5-5h0c-2-1-2-2-3-3v-4c1-1 0-2 1-3v2 1z" class="F"></path><path d="M260 415l1 3v5h-1v3c-2-3 0-8 0-11z" class="G"></path><path d="M256 423c1-1 0-2 1-3v2 1c1 2 2 5 2 7h0c-2-1-2-2-3-3v-4z" class="N"></path><defs><linearGradient id="a" x1="340.659" y1="249.591" x2="328.384" y2="258.313" xlink:href="#B"><stop offset="0" stop-color="#2f2e2f"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient></defs><path fill="url(#a)" d="M330 235c1 1 2 1 2 2 1 7-1 15 3 21l4 4c3 3 6 5 8 9-2 1-5 1-7 1l-5-5h0c-3-2-4-4-6-7h0c-2-4-2-6-2-10v-9c0-2 1-2 0-4h0v-1h0l1 1h1l1-2z"></path><path d="M248 343l-2-5-2-7c2 1 6 1 7 3v1c2 5 3 11 5 16 2 4 4 7 5 11s1 7 1 10c1 3 2 5 3 7l1-1 1 1v-1l1 1v1c0 1 1 2 2 3v1l-1 1c-5-3-8-10-10-14l-3-6c0-2-1-3-1-5-1-1 0-3-1-4l-3-6-1-2-1-3v-2h-1z" class="Q"></path><path d="M267 378l1 1v1l-1 1h-1l-1-2 1-1 1 1v-1z" class="G"></path><path d="M249 339h1c0 1 1 1 1 2 0 2 2 3 2 4 1 2 1 3 1 5l2 2v3h-1c-1-1-1-2-1-3-2-4-4-8-5-13z" class="L"></path><path d="M248 343l-2-5-2-7c2 1 6 1 7 3v1c2 5 3 11 5 16v1l-2-2c0-2 0-3-1-5 0-1-2-2-2-4 0-1-1-1-1-2h-1 0c-1-1-1-3-2-4h0c0 1 0 2 1 3v1c0 1 0 2 1 4h-1z" class="O"></path><path d="M186 536l-1-1 1-1 1 1v-1l-1-2c-1-2-1-6 0-8l1 7c1 4 3 7 7 9 2 1 4 1 7 1 1-1 1-1 2-1 3-2 5-4 6-8v2c0 2-1 5-2 7-1 0-2 1-2 2h0c-1 1-2 1-3 3-3 1-8 2-11 1h0c-4-1-7-3-9-6-3-3-5-7-5-11l1-2v-1c2 3 3 6 6 8 0 0 1 1 2 1z" class="T"></path><path d="M177 530l1-2v-1c2 3 3 6 6 8 0 0 1 1 2 1 0 0 1 1 1 2h0l-1-1v1c1 2 3 4 6 5 1 1 2 2 3 2h0v1h-2-1c-1 0-2 0-3-1h-1c-2-2-7-8-8-11-1-1-1-2-1-3-1-1-1-1-2-1z" class="I"></path><path d="M148 401c1 3 3 4 4 7 1 2 1 5 3 6v-3h-1v-1c2 3 5 7 6 11 0 0 1 1 2 1-1-2-1-4-2-6v-4-1l1 1c1 0 2 1 2 2l-1-1-1 1 1 1c1 2 1 6 3 7h0l3 3 3 3h-2c1 1 3 1 4 2-2 1-4 1-6 2-1 2-4 2-6 3-6-6-9-18-11-26 0-1 0-3-1-4 0-1-1-2-2-3l1-1z" class="B"></path><path d="M160 416v-4-1l1 1c1 0 2 1 2 2l-1-1-1 1 1 1c1 2 1 6 3 7h0l3 3 3 3h-2c-1 0-4-3-5-4 1 1 2 2 2 3-1 0-3 1-4 1 0-4-2-4-2-7 0 0 1 1 2 1-1-2-1-4-2-6z" class="G"></path><path d="M261 246c5 0 9 0 14 1l1-1c5 1 9 1 14 1 2 0 4 0 6-1h0c-1 2-4 2-6 3h-1 3 1l-3 3c0 1 0 0 1 1 1 0 4-1 5 1h-1c-1 0-1 0-2 1-2-1-3-1-5 0h-7l9-3c-2 0-4 1-7 2h-6c-1 1-2 1-4 1s-7-2-9 0c-2-1-4-2-5-4h-1 0c1-1 2-1 3-2 1 0 1-1 1-2l-2-1h1z" class="H"></path><path d="M263 250h3c2 0 8 0 10 2-3 0-5 0-8-1h-2c-2-1-2-1-3-1z" class="N"></path><path d="M259 251h3 0c2 1 4 1 6 1h1c1 1 2 1 2 1 1 0 2 1 2 1 1 1 3 1 4 0-1 1-2 1-4 1s-7-2-9 0c-2-1-4-2-5-4zm2-5c5 0 9 0 14 1l1-1c5 1 9 1 14 1 2 0 4 0 6-1h0c-1 2-4 2-6 3h-1 3c-3 1-6 1-9 1-2 1-5 1-7 0l-14-3-2-1h1z" class="J"></path><path d="M261 246c5 0 9 0 14 1 1 1 2 1 4 1v1h-2c-2 0-3-1-5-1 1 1 3 1 4 2h0l-14-3-2-1h1z" class="E"></path><path d="M139 379c1-3 1-7 3-11 0-1 2-4 3-5v2 2 3c-3 8-1 17 2 25 1 2 3 5 5 8l2 3h1v-1s0-1 1-1v1l1-1-2-5 2 1 1 8v2-1h1v1h0 1l-1 1c0 1 0 4 1 5 1 2 1 4 2 6-1 0-2-1-2-1-1-4-4-8-6-11v1h1v3c-2-1-2-4-3-6-1-3-3-4-4-7l-1 1c-3-3-5-6-6-11 0-2-1-4-2-6v-6z" class="M"></path><path d="M148 401c-5-5-8-13-7-20 2 6 3 12 6 17 1 2 2 3 4 5h1l2 3h1v-1s0-1 1-1v1l1-1-2-5 2 1 1 8v2-1h1v1h0 1l-1 1c0 1 0 4 1 5 1 2 1 4 2 6-1 0-2-1-2-1-1-4-4-8-6-11v1h1v3c-2-1-2-4-3-6-1-3-3-4-4-7z" class="Q"></path><path d="M155 399l2 1 1 8v2c-1-1-3-2-3-3l-1-1c-1 0-3-3-3-3h1l2 3h1v-1s0-1 1-1v1l1-1-2-5z" class="I"></path><path d="M121 516h0v-2-3 1-1h1c0 3 1 6 1 8 1 2 2 4 2 6h1c1 1 1 3 2 4l-1 1c2 4 5 7 8 12 3 2 5 4 8 6h1 1v1l2 2 2 1h1v1h-1v1l3 2c-5-1-9-3-13-5-4-1-6-3-8-5-1-1-1-2-1-3h-1c-3-4-7-8-8-13v-2c1 0 1 0 1 1-1-3-2-7-2-10h1l-1-1 1-2z" class="D"></path><path d="M130 543c3 2 6 5 9 8-4-1-6-3-8-5-1-1-1-2-1-3zm5-1c3 2 5 4 8 6h1 1v1l2 2 2 1h0-1c-3-1-7-3-10-5v-1c0-1-1-2-2-3 0-1 0 0-1-1h0z" class="C"></path><defs><linearGradient id="b" x1="126.528" y1="536.179" x2="132.78" y2="534.169" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#484749"></stop></linearGradient></defs><path fill="url(#b)" d="M121 516h0v-2-3 1-1h1c0 3 1 6 1 8 1 2 2 4 2 6h1c1 1 1 3 2 4l-1 1c2 4 5 7 8 12h0c1 1 1 0 1 1 1 1 2 2 2 3v1c-1-1-3-2-4-4-1-1-2-2-3-2 0-1-1-1-1-1-1-1-4-4-5-6-1-1-2-4-3-5-1-3-2-7-2-10h1l-1-1 1-2z"></path><path d="M121 516h0v-2-3 1-1h1c0 3 1 6 1 8 1 2 2 4 2 6l1 3h0c-2-3-4-8-5-12z" class="K"></path><path d="M120 519h1c0 1 0 1 1 2 0 1 1 2 1 3 1 2 0 5 2 6v4c-1-1-2-4-3-5-1-3-2-7-2-10z" class="C"></path><path d="M267 564l2-5 1 1h0v1c1 0 1 1 1 2h-1c0 3 0 5-1 8l-1-1c-1 2-1 5-3 7v-2-1c0 1-1 1-2 2-1 2-2 4-3 5-1 2-5 8-8 9v-1h0-2v-1c-5 4-9 8-14 10-11 7-25 12-38 13l-1-1 12-2h1c15-4 30-11 41-23l6-9 10-16v2 2z" class="K"></path><path d="M267 564l2-5 1 1h0v1c1 0 1 1 1 2h-1c0 3 0 5-1 8l-1-1c-1 2-1 5-3 7v-2-1c0 1-1 1-2 2-1 2-2 4-3 5h-1c0-1 1-1 1-2 1-2 4-5 4-7 0-3 2-5 3-7v-1z" class="R"></path><path d="M263 576c1-3 3-5 5-7 1-2 1-4 2-6 0 3 0 5-1 8l-1-1c-1 2-1 5-3 7v-2-1c0 1-1 1-2 2z" class="C"></path><path d="M293 311l15-10c1-1 4-3 5-3s4 1 5 1c-6 7-12 14-19 19-2 2-4 4-6 5-2-2-5-5-6-8 0-1 1-1 1-1 2-1 3-2 5-3z" class="X"></path><path d="M191 370l4 1h0c0 1-1 2-1 3v1c1 1 1 0 2 0l-1 1v1l-1 1c0 1 0 2-1 3s-1 2-2 4h-1 0c0 1-1 1-1 2v-1h-1v-2-1c-1 0-1 1-2 2v-2-1c-2 3-3 6-4 10l-2 2c-1-1-2-4-2-5v-3h-1c0 1 0 2-1 3v1c-1-1-1-5-1-7v-1c0-1 1-2 1-4 0-1 1-2 1-4l1 1h1v-2h4v2h1v-1l1-1h1 2v-1c-1-1-2-1-2-2h2l1 1 2-1z" class="C"></path><path d="M176 378c0-1 1-2 1-4l1 1c1 1 1 1 1 3h-1c-1 0-1 0-1 1l-1-1zm11-1c-1 1-2 3-2 4h-1c0-2 1-3 1-4-1 0-1 1-2 2v-1h0-1l1 1v2l1 1h-1v-1c-1 1-1 2-2 3v-1c-1 0-1 0-1-1v-1c1-1 1-1 1-2s0-1-1-2c0-1 0-1 1-1h0c1 1 1 0 2-1v2h1c0-1 0-1 1-2v2c1-1 1-1 2-1v1z" class="F"></path><path d="M191 370l4 1h0c0 1-1 2-1 3v1c1 1 1 0 2 0l-1 1v1l-1 1c0 1 0 2-1 3s-1 2-2 4h-1 0c0 1-1 1-1 2v-1h-1v-2-1c-1 0-1 1-2 2v-2-1c0-2 3-5 4-8h0c-2 0-2 2-3 3h0v-1l1-2h0-2v-1h2v-1c-1-1-2-1-2-2h2l1 1 2-1z" class="L"></path><path d="M186 370h2l1 1h3l1 1h-1-2c-1 1-1 1-2 1v-1c-1-1-2-1-2-2zm0 13h0c1-1 1-1 1-2 1-3 3-5 4-7h1v1l1 1c-1 2-2 3-3 5 0 1-1 3-1 5h-1v-2-1c-1 0-1 1-2 2v-2z" class="C"></path><defs><linearGradient id="c" x1="304.746" y1="535.505" x2="282.726" y2="546.107" xlink:href="#B"><stop offset="0" stop-color="#363737"></stop><stop offset="1" stop-color="#504e4f"></stop></linearGradient></defs><path fill="url(#c)" d="M299 510c1 2 0 6 0 9 0 6 1 12 0 18 0 3-1 6-1 9-1 2 0 5-1 6l1 1c0 1-1 3-1 4l-1 2c-1 2-1 4-3 6v1c-3-2 0-3-2-5h0c0 2-1 5-2 6-1 0-1 0-2 1v1h-1c0-1 1-2 1-4v-1l2-7c0-1 1-3 1-3v-2h-1l2-5v-2c1-1 1-1 1-2l2-10c0-2 0-4 1-6 0-5 0-10 1-16 1 3 0 6 1 9v4c1-4 1-7 1-10v14l1 1v-1-18z"></path><path d="M287 564h0 1c1-3 3-6 4-9l-1 6c0 2-1 5-2 6-1 0-1 0-2 1v1h-1c0-1 1-2 1-4v-1z" class="T"></path><path d="M296 559c-1 1-1 2-2 2h0v-3c1-2 1-3 1-5v-1l1 1v-3h-1l1-1c0-2 0-2 2-3-1 2 0 5-1 6l1 1c0 1-1 3-1 4l-1 2z" class="I"></path><path d="M196 493h0c0 1-1 1-1 2h1 2 0c0 1-1 1-2 1 0 1-1 1-2 1v1h0c-1 1-3 2-5 4h1v1h2 1l1 1c-7 3-13 10-16 17-2 4-3 8-3 13 0 2 1 4 0 6-1-2-1-4-1-6-1-1-1-2-1-3 1-2 0-3-1-5 1-1 1-1 1-2 1-1 1-2 1-3h0c0-4 0-5-2-7l1-3 2-2c3-3 6-7 9-9 1 0 1-1 1-2h0 0c2-2 4-3 6-3l2-1c1-1 2-1 3-1z" class="K"></path><path d="M196 493h0c0 1-1 1-1 2h1l-6 3 3-3h0v-1c1-1 2-1 3-1z" class="C"></path><path d="M174 521c1-1 1-1 2-1-2 4-2 9-2 14-1-1-1-2-1-3 1-2 0-3-1-5 1-1 1-1 1-2 1-1 1-2 1-3z" class="H"></path><path d="M193 494v1h0l-3 3c-3 2-5 4-7 6 1 0 3-1 4-2 0 1 0 1-1 2-2 2-6 4-7 7-1 1-1 2-1 3-1 1 0 2-1 3 0 1-1 3-1 3-1 0-1 0-2 1h0c0-4 0-5-2-7l1-3 2-2c3-3 6-7 9-9 1 0 1-1 1-2h0 0c2-2 4-3 6-3l2-1z" class="C"></path><path d="M193 494v1h0l-3 3c-3 2-5 4-7 6-2 1-2 2-4 3 1-2 3-3 4-5l4-4c2-1 3-2 4-3l2-1z" class="E"></path><path d="M218 558l4-4c0 2-1 4 0 5l-3 3h0c-2 2-4 4-6 5v1h-3c-2 0-3 1-4 2l-1-1c-9 4-19 6-29 5-4-1-8-1-12-2-6-2-13-5-19-8 3 0 6 2 8 2 1 0 2 0 3 1 3 0 5 1 7 1h2c9 3 21 3 29 1 5-2 9-4 13-6 3-1 5-5 8-5l1 2c1 0 1-1 2-2z" class="P"></path><path d="M218 558l4-4c0 2-1 4 0 5l-3 3h0c-2 2-4 4-6 5v1h-3c-2 0-3 1-4 2l-1-1c1-1 4-2 5-3l6-6c1 0 1-1 2-2z" class="K"></path><path d="M246 620h0l12-5c-1 2-4 4-6 5l-4 2c-1 0-2 0-3 1h4c1 0 2 0 3-1h0c1 0 2-1 3-1h0l-5 3v1c-2 1-7 4-8 4-7 2-14 4-21 5-8 1-17 1-25 1h-15 0 4v-1h-5 0c-2-1-5 0-7-1h-3c-1-1-3-1-4-1h-1v-1c4 0 8 1 12 1 1 1 3 0 4 1 6 1 13-1 18-1 2 0 3 0 4-1h8c9-1 17-4 26-6 2-1 7-2 9-4v-1z" class="F"></path><path d="M180 634h7l20-2h7c1-1 2-1 2-1h1 1c2 0 5-1 7-1v-1h1c2-1 4-2 6-2l2-1h1 2 1c-1 1-13 5-16 6 5 0 9-2 14-3 2 0 3-1 6 0-7 2-14 4-21 5-8 1-17 1-25 1h-15 0 4v-1h-5z" class="K"></path><path d="M263 403c0-1 0-1 1-1 1 2 4 4 6 6h1c1 1 3 2 3 3v1 5l1 1c0 4 0 9-1 12-1 1-1 2-1 3h-1l-1-3c-1 2 0 6 1 8l-4-4-1-2-2-5h0c-1-1-2 0-3 0 0-1-1-3-1-4v-5l-1-3v-1-2c-1-2 0-3 0-5h0c1-1 1-3 1-5 1 0 1 0 2 1z" class="B"></path><path d="M266 417v-6h0l1 1v12c0-1-1-2-1-3h0v-4z" class="F"></path><path d="M271 408c1 1 3 2 3 3v1h0c-2 0-3 1-4 2h0v-1-4l1-1z" class="H"></path><path d="M266 421h0c0 1 1 2 1 3-1 1-1 3-1 4h1c0 1 1 3 1 4l1 1-1 1-1-2-2-5v-1c0-1 0-3 1-5z" class="K"></path><path d="M265 407c1 0 1 1 2 1h1v3l1-1v-1h1v4l-1 2v-1l-2-2-1-1h0v6l-1-7h1c-1-1-1-2-1-3z" class="C"></path><path d="M265 407c1 0 1 1 2 1h1-1v2h-1c-1-1-1-2-1-3z" class="G"></path><path d="M268 432v-4c1-1 1-4 1-5 1 0 1-1 1-2h-2 2v4c0 2 1 4 1 5-1 2 0 6 1 8l-4-4 1-1-1-1z" class="E"></path><path d="M270 421c0-1 0-1 1-2 0 1 1 2 1 3h0 1c0-1 0-3 1-5h0l1 1c0 4 0 9-1 12-1 1-1 2-1 3h-1l-1-3c0-1-1-3-1-5v-4z" class="C"></path><defs><linearGradient id="d" x1="265.338" y1="415.042" x2="262.809" y2="415.221" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#d)" d="M263 403c0-1 0-1 1-1 1 2 4 4 6 6h1l-1 1h-1v1l-1 1v-3h-1c-1 0-1-1-2-1 0 1 0 2 1 3h-1l1 7v4c-1 2-1 4-1 5v1h0c-1-1-2 0-3 0 0-1-1-3-1-4v-5l-1-3v-1-2c-1-2 0-3 0-5h0c1-1 1-3 1-5 1 0 1 0 2 1z"></path><path d="M263 408v12c0 2 0 3 1 4h0 0c1 1 1 2 1 2v1h0c-1-1-2 0-3 0 0-1-1-3-1-4v-5-5c1-2 2-3 2-5z" class="L"></path><defs><linearGradient id="e" x1="261.954" y1="403.654" x2="265.861" y2="414.853" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#e)" d="M263 403c0-1 0-1 1-1 1 2 4 4 6 6h1l-1 1h-1v1l-1 1v-3h-1c-1 0-1-1-2-1v-1c-1 1-1 2-2 2 0 2-1 3-2 5v5l-1-3v-1-2c-1-2 0-3 0-5h0c1-1 1-3 1-5 1 0 1 0 2 1z"></path><path d="M261 402c1 0 1 0 2 1l-3 11v-2c-1-2 0-3 0-5h0c1-1 1-3 1-5z" class="I"></path><path d="M261 302c1-3 2-6 4-9h0l-2 7c1 1 1 2 3 4-1 0-1 0-1 1s1 1 1 2c0 2 0 3 1 4s2 4 2 5h0c2-2 2-6 2-8 0-1 0-2 1-3l1 1v2l-1 5c-1 2 0 4 0 6h0c1 1 1 2 1 3 1 2 1 6 2 7h1v-2h1c0 2 1 5 2 7 2 7 5 13 7 19 0 2 1 5 1 6h-1c-2-8-4-18-9-25 1 3 3 7 3 11-1 0-1-3-2-4 0-2-2-5-3-6-2-2-2-5-3-7h-1c-1-1-1-2-2-4v-1h-1l1 2v2c1 0 1 1 1 2l2 6v2l-2-2c-1-1-1-3-2-4l-1 1c1 2 2 5 2 7h0c0-1-1-3-2-4l-3-6v-2c-1-2-1-3-1-4h0l-1 1h-1c0-1 0-2-1-3l1-1v-7c0-1 1-2 0-4l-1-1c0-1 1-3 1-5v-1z" class="B"></path><path d="M267 320h1c0-1 0-3 1-4 0 2 0 4 1 6 0 2 1 4 2 6h-1c-1-1-1-2-2-4v-1h-1l1 2v2c1 0 1 1 1 2-2-3-3-6-3-9z" class="D"></path><path d="M261 303v-1 3 1c2 2 2 3 2 6l-1 8h-1v-7c0-1 1-2 0-4l-1-1c0-1 1-3 1-5z" class="E"></path><path d="M263 300c1 1 1 2 3 4-1 0-1 0-1 1s1 1 1 2c-1 1-1 2-1 3v1l-1-1c0-4-2-6-1-10z" class="D"></path><path d="M265 310v-3c-1-1-1-2-1-3l1 1c0 1 1 1 1 2-1 1-1 2-1 3z" class="G"></path><path d="M268 331c0-4-2-7-1-11h0 0c0 3 1 6 3 9l2 6v2l-2-2c-1-1-1-3-2-4zm-5-8v-1c1-1 1-7 2-9h0c2 2 0 6 0 8 0 3 1 6 2 9h0v2c1 2 2 5 2 7h0c0-1-1-3-2-4l-3-6v-2c-1-2-1-3-1-4z" class="F"></path><path d="M303 332l-1-1c-4-1-7-2-11-3 6 0 11-1 16-4-5 1-8 2-13 0 5-4 10-8 14-13l8-9c0-1 2-3 2-3h1 1c2 1 4 0 6 0-4 7-8 15-13 22-3 4-6 9-10 12v-1zm68-69h9l1 16-20-4-11-2-1-1c0-2-1-2-2-3v-1c8-3 16-5 24-5z" class="X"></path><defs><linearGradient id="f" x1="195.442" y1="656.293" x2="239.045" y2="621.152" xlink:href="#B"><stop offset="0" stop-color="#232325"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#f)" d="M187 639c5 0 9 1 14 0 6 0 12-2 18-3l12-1c3 0 5-1 8-1 1-1 1-1 2-1 1 1 3 3 5 3 3 0 5 0 8 1h0c-4 1-9 3-13 4-3 0-6 0-9 1v1h0 0c-16 2-33 1-49-1v-1c0-1 4 0 4-1v-1z"></path><path d="M243 564l1-6v1c-2 3-3 6-6 8 1-3 3-6 4-8 4-9 4-19 2-29-2-8-8-15-16-19-2-1-5-2-8-3-7-3-12-4-19-2-3 0-5 2-8 2 3-2 5-3 9-4 2 0 4-1 6-1 9 0 16 3 24 7 2 1 5 2 6 4l8 8c1 2 2 3 3 5 0 1 1 2 1 3v1 1c0 3 1 6 1 9 0 4-1 9-2 13v1c0 2-2 5-3 8 0 1-1 2-2 3 0 0 0 1-1 1l-1 1c0-1 0-3 1-4z" class="M"></path><path d="M245 528v-1-1c-1-1-2-2-2-3h0l1-1 3 5h0l2 8v1c-1-1-1-2-1-3-1-2-2-4-3-5z" class="V"></path><path d="M249 546h0c-1 1-1 3-1 3-1-1 0-2 0-3v-5c-1-1 0-3 0-4-1-1-1-2-1-3s-1-3-1-4c-1-1-1-1-1-2 1 1 2 3 3 5 0 1 0 2 1 3v-1c0 1 0 2 1 4v7h-1z" class="B"></path><path d="M247 527h0c1 2 2 4 3 5 0 3 1 6 1 9 0 4-1 9-2 13v1c0 2-2 5-3 8 0 1-1 2-2 3 0 0 0 1-1 1l-1 1c0-1 0-3 1-4 2-2 4-7 5-10s1-5 1-8h1v-7c-1-2-1-3-1-4l-2-8z" class="G"></path><path d="M302 545h1c2-4 4-32 2-38v-4c-1-2-1-5-1-7-4-15-11-31-23-41 1 3 3 5 5 7 3 5 6 11 8 16 1 2 1 3 2 4l1 3 3 6-1 1c0-1-1-1-1-2l-6-13h0v-2c-1-1-2-2-2-4l-4-7c-2-4-6-7-9-10-1-1-1-2-2-3 0-1-1-2-1-3 7 5 14 12 19 19 2 3 4 6 5 9 2 6 5 12 6 18l2 12c1 7 1 16 1 24-1 4-2 8-2 12l2-8c1-6 1-13 1-19v-9c-1-3 0-5-1-8l-2-7 1-1c2 4 2 9 3 13v14c0 3 1 6 0 8-1 10-2 20-6 29v2 1h-1 0 0c0 1-1 2-1 2-1 4-3 8-4 12-1 1-1 3-3 4h0-1v-4 1c-1 1-1 3-2 4l-1 1c0-1-1-2 0-3 0-1 1-2 1-3 0-3 0-3 2-5v-1c2-2 2-4 3-6l1-2 1 1h0l3-6c0-2 0-5 1-7z" class="B"></path><path d="M297 557l1 1c-1 3-2 7-3 11v1c0 2-1 4-1 5h-1v-4 1c-1 1-1 3-2 4l-1 1c0-1-1-2 0-3 0-1 1-2 1-3 0-3 0-3 2-5v-1c2-2 2-4 3-6l1-2z" class="K"></path><defs><linearGradient id="g" x1="197.177" y1="472.309" x2="173.284" y2="509.501" xlink:href="#B"><stop offset="0" stop-color="#4c4b4b"></stop><stop offset="1" stop-color="#636366"></stop></linearGradient></defs><path fill="url(#g)" d="M188 476v1c2 0 4 0 5-1 4 0 8-1 12 0l13 2c4 0 7 1 10 2v1l-7-2-1 1c1 0 2 0 4 1 1 0 3 1 4 1 1 1 2 1 2 1h0-3s0-1-1-1h0-2l-1-1c-1 0-7-1-8-1 1 1 2 1 2 2h2v1c-2 0-3-1-4-1h-1l-4-1h-8v1c-4 0-8 1-11 2-1 1-2 2-4 2h-2c-1 1-3 2-4 3 0 2-2 2-3 3h0c-2 1-3 3-5 5-1 1-3 3-4 5 0 0 0 2-1 2h-1 0c-1 0-1 1-1 2-1 0 0 0-1 1v1h-1c0-1 1-3 1-4h0-2c1-3 1-5 3-7 4-8 12-17 22-21z"></path><path d="M190 483h-1v-1c8-4 20-2 28 0h2v1c-2 0-3-1-4-1h-1l-4-1h-8v1c-4 0-8 1-11 2l-1-1z" class="C"></path><path d="M190 483s2-1 3-1c3-1 6-1 9-1v1c-4 0-8 1-11 2l-1-1z" class="V"></path><path d="M165 504c0-1 1-1 1-2h0c1-3 5-10 8-11s4-4 7-6v1l-1 1h0c0 1 0 1-1 1v1l1 1 1-1c0 2-2 2-3 3h0c-2 1-3 3-5 5-1 1-3 3-4 5 0 0 0 2-1 2h-1 0c-1 0-1 1-1 2-1 0 0 0-1 1v1h-1c0-1 1-3 1-4z" class="M"></path><path d="M190 281c3-3 7-6 10-9s7-5 9-9l-3 2c-1 1-2 1-4 1 1-2 3-4 4-5 3-4 6-7 9-10l4-4c3-2 5-4 7-7 3-3 7-6 11-9-3 4-6 7-9 10-2 2-5 4-6 6l11-8h0l-1 2 1 1h0v1l-10 10c2 0 3-2 4-3 2-1 4-2 5-4 1 0 2-1 3-2v1l-2 2v1l-7 6h1c-1 1-6 5-6 7h0l-7 7c-2 2-3 4-5 6-1 1-1 2-2 3-1-1-1-2-1-2v-2c-3 1-3 2-5 4h-1-1c-2 1-4 4-7 4l-2 1v-1z" class="B"></path><path d="M192 281l14-12-2 3c-1 2-3 4-5 5s-4 4-7 4z" class="Q"></path><path d="M232 246c1 0 2-1 3-2v1l-2 2v1l-7 6h1c-1 1-6 5-6 7h0l-2-1 14-13-1-1z" class="D"></path><path d="M206 273h1v1h1c1-1 1-2 2-3 3-4 6-8 9-11l2 1-7 7c-2 2-3 4-5 6-1 1-1 2-2 3-1-1-1-2-1-2v-2z" class="G"></path><defs><linearGradient id="h" x1="230.401" y1="240.624" x2="221.714" y2="252.509" xlink:href="#B"><stop offset="0" stop-color="#050504"></stop><stop offset="1" stop-color="#2c2b2e"></stop></linearGradient></defs><path fill="url(#h)" d="M220 254h-1c1-1 1-1 1-2h-1c3-4 9-8 13-11l1 1h0v1l-10 10h0l-6 6h-1l4-5z"></path><path d="M233 242h0v1l-10 10h0l-6 6h-1l4-5 13-12z" class="C"></path><path d="M233 272c1-1 2-2 2-3 1-2 3-4 5-5h0c-1 2-2 4-4 6-2 3-3 7-4 10h0c2-4 4-7 6-11 1-1 2-3 3-3-2 5-5 9-7 14h0c0 1-1 2-1 4l1-1v-1c1-1 1-1 1-2l1 1c-1 1-1 2-2 3-1 2-3 5-3 7v1 2l-1 6c-1 3-1 6-1 9 1 3 1 5 2 7 0 0 0 1 1 1h0v-1c0-1 0-2-1-3h0l1 1h0v-2c1 3 2 6 5 9h-2s-1-1-2-1v1l1 1h0v1h1v1c-2 0-4 1-6 1 0-1-1-1-2-1v-1h0v-1c-1-1-1-2-2-2h-1c-1 0-1-1-2-1v1l-3-3c-1-6 1-15 3-20 0-1 1-3 1-4 2-7 6-14 10-21z" class="I"></path><path d="M227 302c0-6 2-10 4-16l3-6h0c0 1-1 2-1 4l1-1v-1c1-1 1-1 1-2l1 1c-1 1-1 2-2 3-1 2-3 5-3 7v1 2h-1v1c0 2-1 4-3 6v1z" class="H"></path><path d="M227 302v-1c2-2 3-4 3-6v-1h1l-1 6c-1 3-1 6-1 9 1 3 1 5 2 7l-1 1-1-1c-2-5-2-9-2-14z" class="Q"></path><path d="M224 313c0-2 0-3 1-4 1 2 1 5 3 7h1l1 1 1-1s0 1 1 1h0v-1c0-1 0-2-1-3h0l1 1h0v-2c1 3 2 6 5 9h-2s-1-1-2-1v1l1 1h0c-1 0-2 0-4-1-1-2-3-3-4-4-1-2-1-3-2-4z" class="D"></path><path d="M233 272c1-1 2-2 2-3 1-2 3-4 5-5h0c-1 2-2 4-4 6-2 3-3 7-4 10-2 3-4 7-5 10v2c1 3-2 6-2 9v8c-1 1-1 2-1 4 1 1 1 2 2 4-1 0-1 0-2-1 0-1-1-2-1-3-1-5 0-10 1-15 0-3 1-5 1-7-1 2-2 4-2 7l-1-1c0-1 1-3 1-4 2-7 6-14 10-21z" class="M"></path><path d="M227 292c1 3-2 6-2 9v8c-1 1-1 2-1 4v-5c0-6 1-11 3-16z" class="L"></path><defs><linearGradient id="i" x1="218.117" y1="307.278" x2="231.624" y2="317.919" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#2e2d2e"></stop></linearGradient></defs><path fill="url(#i)" d="M223 298c0-3 1-5 2-7 0 2-1 4-1 7-1 5-2 10-1 15 0 1 1 2 1 3 1 1 1 1 2 1 1 1 3 2 4 4 2 1 3 1 4 1v1h1v1c-2 0-4 1-6 1 0-1-1-1-2-1v-1h0v-1c-1-1-1-2-2-2h-1c-1 0-1-1-2-1v1l-3-3c-1-6 1-15 3-20l1 1z"></path><path d="M227 322l1-1c2 0 3 2 6 2h1v1c-2 0-4 1-6 1 0-1-1-1-2-1v-1h0v-1z" class="O"></path><path d="M222 297l1 1c-2 4-4 13-2 17 1 1 1 3 1 4v1l-3-3c-1-6 1-15 3-20z" class="C"></path><path d="M289 195h1v8 2c-5 0-8 0-11 2l8-1 1 1 1 2c2 0 5-1 7 0h1c1 0 3 1 4 2 1 0 1 0 2 1h0l1-1h1 0 0c-1-1-2-1-3-2h0-1c0-1-1-1-2-1s-1-1-1-1c1 0 8 3 8 3l1-1c2 2 5 3 7 4v3c2 2 4 3 5 6l1 4h-2l1 2c0 1-1 1-1 1l-1-1c-2-1-6-4-9-4h0l-1-1c-1 0-1-1-2-1-1-1-2-1-3-2-1 0-1 0-2 1-2-1-4-2-6-2l-8-3c-2-1-3 0-5-2 1-1 6-1 7-1v-2h2l1-1h0-2 0c-1 0-1-1-2-1h-8 0l1-1c-1-1 0-1-1-1h0c1-2 1-5 1-7 1 0 1-1 1-2l1-2h7v-1z" class="O"></path><path d="M283 200h1 0 2c1 0 2 0 2 1v1h-1c-2 0-4 1-6 0v-1l2-1z" class="J"></path><path d="M298 215c1 0 2-1 3 0 6 2 12 4 16 8v2c-1-1-3-2-4-2-5-4-11-5-15-8z" class="N"></path><path d="M289 195h1v8c-1 0-2 0-3-1h1v-1c0-1-1-1-2-1h-2 0-1 0c-1 0-1 0-2-1v-1l1-2h7v-1z" class="H"></path><path d="M283 197h5 1l-1 1h-6l1-1z" class="J"></path><defs><linearGradient id="j" x1="287.631" y1="224.414" x2="307.03" y2="213.985" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#j)" d="M288 213c2 0 3-1 5 0 1 0 3 1 5 2 4 3 10 4 15 8 1 0 3 1 4 2l1 1 1 2c0 1-1 1-1 1l-1-1c-2-1-6-4-9-4h0l-1-1c-1 0-1-1-2-1-1-1-2-1-3-2-1 0-1 0-2 1-2-1-4-2-6-2l-8-3c-2-1-3 0-5-2 1-1 6-1 7-1z"></path><path d="M313 223c1 0 3 1 4 2l1 1 1 2-5-3c-1-1-1-1-1-2z" class="B"></path><defs><linearGradient id="k" x1="169.244" y1="479.227" x2="183.394" y2="445.006" xlink:href="#B"><stop offset="0" stop-color="#363637"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#k)" d="M136 463l1-1c5-4 14-7 20-9l12-3c2 0 4-1 5-1s3 0 4 1c0 0 0 1 1 1h3l11 2c11 1 22 4 33 9l-1 1c-2 0-5-2-7-3h0-1 0c-1 0-2-1-4-2v1h1v1c-3 0-6-1-9-2h-5-1c-2 0-5-1-8-1-11 0-21 1-32 4v-1c-2 0-4 1-6 2s-4 1-5 2c-3 1-5 3-7 4l2-2h0s-1 0-2-1h-2c-1 0-2 1-3 1-1-1-1-2 0-3z"></path><path d="M136 366c1 0 1-1 2-2v1 1h0c-1 0-1 1-1 1h1 0 0 1 0c0 2 0 4-1 5v3-1c1-1 1-1 1-2v-2-1c0-1 1-2 2-3h0c-1 3-3 9-2 13h0v6c1 2 2 4 2 6-1 3 1 5 1 7s-1 4-1 5c-1-1-1-2-1-3 0 5-1 11-1 16v1c-3-3-5-7-7-10h0c0 2 1 3 1 4v1c-1-1-1-2-2-3v-3c0-1-2-4-2-5-1-2-1-5-2-7v3c-1-2-1-3-2-4v-1c-1 2-2 3-4 4l-1-1h1v-1c2-3 4-5 6-8v-1l1-2c0-2 1-3 2-4h1c1-3 2-6 2-9 0-1 0-1 1-2 1 0 1-2 2-2z" class="P"></path><path d="M139 367h0c0 2 0 4-1 5v3-1c1-1 1-1 1-2v-2-1c0-1 1-2 2-3h0c-1 3-3 9-2 13h0v6c1 2 2 4 2 6-1 3 1 5 1 7s-1 4-1 5c-1-1-1-2-1-3s0-3-1-5v6c-1 4-1 8-2 11-1-5 1-11 1-16 0-2-1-4-1-6-1-2-1-4-1-6s-1-5-1-7c1-1 1-3 1-4v-1c0-1 1-4 2-5h0 0 1z" class="C"></path><path d="M138 367h0l-2 17c0-2-1-5-1-7 1-1 1-3 1-4v-1c0-1 1-4 2-5h0z" class="D"></path><path d="M136 366c1 0 1-1 2-2v1 1h0c-1 0-1 1-1 1h1c-1 1-2 4-2 5-3 6-4 13-4 20-1 5-1 10 0 15h0c0 2 1 3 1 4v1c-1-1-1-2-2-3v-3c0-1-2-4-2-5-1-2-1-5-2-7v3c-1-2-1-3-2-4v-1c-1 2-2 3-4 4l-1-1h1v-1c2-3 4-5 6-8v-1l1-2c0-2 1-3 2-4h1c1-3 2-6 2-9 0-1 0-1 1-2 1 0 1-2 2-2z" class="C"></path><path d="M128 383c0-2 1-3 2-4h1c-2 6-3 14-2 21v1h0c-1-2-1-5-2-7v3c-1-2-1-3-2-4v-1c-1 2-2 3-4 4l-1-1h1v-1c2-3 4-5 6-8v-1l1-2z" class="O"></path><path d="M125 392l2-3c0 1 1 3 0 5v3c-1-2-1-3-2-4v-1z" class="E"></path><path d="M147 364h-1c0-2 2-7 2-8 1-1 2-1 3-2v-1c0 1 1 3 1 3l2 6h1l1 1c-1 1-1 2 0 2v1l1 1h1c1 1 1 1 1 2 0 3-2 7-2 10-1 3-1 6-1 9 0 4 0 8 1 12l-2-1 2 5-1 1v-1c-1 0-1 1-1 1v1h-1l-2-3c-2-3-4-6-5-8-3-8-5-17-2-25 0-1 0-2 1-3 1 0 1-2 1-3z" class="P"></path><path d="M151 364h0l2-2c-1 5-3 10-4 14-1-1-1-2-1-3l-1-1v1 1l-1-1v1-5l1 3c0-1 1-2 1-2 1-1 1-3 2-4 0 0 0-1 1-1v-1z" class="B"></path><path d="M146 374v-1l1 1v-1-1l1 1c0 1 0 2 1 3 0 1-1 2-1 3v5c0 3 1 6 1 10-2-7-4-13-3-20z" class="E"></path><path d="M147 364h-1c0-2 2-7 2-8 1-1 2-1 3-2v-1c0 1 1 3 1 3l2 6h-1 0l-2 2h0v1c-1 0-1 1-1 1-1 1-1 3-2 4 0 0-1 1-1 2l-1-3v-2c1 0 1-2 1-3z" class="C"></path><path d="M151 358v1c0 1 1 1 1 2l-1 1c-1 0-1 1-2 2h0c0-1 0-1 1-2l1-4z" class="B"></path><path d="M152 356l2 6h-1 0l-2 2h0v-2l1-1c0-1-1-1-1-2v-1l1-2z" class="J"></path><path d="M147 364h-1c0-2 2-7 2-8 1-1 2-1 3-2v-1c0 1 1 3 1 3l-1 2v-3h0c-1 0-1 1-1 1 0 3-2 6-3 8z" class="L"></path><path d="M155 362l1 1c-1 1-1 2 0 2v1l1 1c-1 2-1 3-2 5v1l-2 5c-1 8-1 18 3 26-1 0-1 1-1 1-5-13-5-25-2-38 0-2 1-3 1-4l1-1z" class="K"></path><path d="M155 362l1 1c-1 1-1 2 0 2v1l1 1c-1 2-1 3-2 5v1l-2 5c0-4 1-7 2-11h-2c0-2 1-3 1-4l1-1z" class="G"></path><path d="M154 363c1 1 1 3 1 4h0-2c0-2 1-3 1-4z" class="F"></path><path d="M157 367h1c1 1 1 1 1 2 0 3-2 7-2 10-1 3-1 6-1 9 0 4 0 8 1 12l-2-1 2 5-1 1v-1c-4-8-4-18-3-26l2-5v-1c1-2 1-3 2-5z" class="H"></path><path d="M155 386v-2l1 4c0 4 0 8 1 12l-2-1c0-5-1-9 0-13z" class="K"></path><path d="M158 367c1 1 1 1 1 2 0 3-2 7-2 10-1 3-1 6-1 9l-1-4v2c0-7 1-12 3-19z" class="F"></path><path d="M498 137h1 2c2 2 4 5 5 7 3 6 6 12 6 18 1 7 2 15-1 21-1 2-3 4-4 6 2 3 5 5 8 8 3 2 5 6 6 10 4 9 4 17 0 26a25.03 25.03 0 0 1-13 12c-5 2-12 3-17 0-4-2-8-5-9-9-2-5-2-9 0-14h0l1-2c1 0-1 4-1 5v7c2 5 5 8 9 10s9 2 14 1c6-3 11-7 14-13s2-15 0-21c-4-10-11-15-20-19 4-3 8-7 10-12 2-10 1-22-4-30-2-4-4-7-6-11h-1z" class="S"></path><path d="M181 173c1 0 1 0 2 1 1 3-3 9-3 13-1 2-1 4-1 6 2-2 4-5 6-7 1-1 6-3 7-4 0-1 0-1 1-1l1 1c1-1 4-1 6-1v1c-2 1-3 1-5 2 2 3 4 7 4 10 1 7 0 13-6 17-2 2-5 3-8 5 0 0-2 1-2 2-1 1 0 3-1 4-1 0-1 0-2-1-1 1-2 2-2 3h0 0 2c1 1 2 2 2 3 0 2 0 5-1 6s-2 1-3 1-2-1-3-2c-1-2-2-4-2-6 1-5 5-10 9-13 3-2 7-3 10-5 3-3 5-6 5-10 0-5-2-9-5-13-4 2-8 4-10 8-1 1-2 3-1 4 0 1 1 3 2 3 1 1 1 1 2 0 3-2 1-4 2-7 1-1 1-1 2-1 1 1 2 1 2 2 1 1 1 3 1 4-1 2-3 4-5 5s-5 2-6 1c-2 0-2-1-3-2l-1-1v-1c-1 4-1 7-1 11-1-1-3-3-4-5-1-3 1-3 2-6 1-1 1-4 1-6 1-1 1-1 2-1v-2-7c1-4 2-7 4-11z" class="U"></path><path d="M177 227c1 0 1 0 2 1s1 1 0 2h-2v-3z" class="P"></path><defs><linearGradient id="l" x1="253.037" y1="581.665" x2="270.407" y2="589.415" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#5b5b5c"></stop></linearGradient></defs><path fill="url(#l)" d="M276 528v2c1 1 1 2 2 3 0 2 0 6 1 7 0 2-1 8-2 9v2c1 1 1 1 1 3 0 6-3 9-4 14 1-1 3-2 3-3 1-2 2-5 3-7h1 1 0 1l-2 5h1 1l1-2h0l1 1c-2 2-4 5-5 8-2 6-6 11-10 16-2 3-4 5-7 7-1 2-3 4-4 5-2 2-4 2-6 3l-9 6h0-1c0-1-1-1-1-1 2-3 6-4 8-6h1l6-6c-1-1 0-1 0-2h0c2-2 2-4 3-7 2-2 4-5 5-8 2-2 2-5 3-7l1 1c1-3 1-5 1-8h1l1-3c1-8 3-17 3-26l1 2v-5-3z"></path><path d="M273 571c1-1 3-2 4-4 1-1 3-4 4-5 0 1 0 1-1 2l2 1c-1 1-2 2-2 3l-1 1v-1h0v-2-1c-1 1-1 2-2 3h0l-5 5 1-2z" class="C"></path><path d="M274 568c1-1 3-2 3-3 1-2 2-5 3-7h1 1 0 1l-2 5h1 1c-1 1-1 1-1 2l-2-1c1-1 1-1 1-2-1 1-3 4-4 5-1 2-3 3-4 4l1-3z" class="I"></path><path d="M276 528v2c1 1 1 2 2 3 0 2 0 6 1 7 0 2-1 8-2 9v2c1 1 1 1 1 3 0 6-3 9-4 14l-1 3-1 2c-2 4-4 7-6 10-1-2 2-10 3-12 1-3 1-5 1-8h1l1-3c1-8 3-17 3-26l1 2v-5-3z" class="J"></path><path d="M276 530c1 1 1 2 2 3 0 2 0 6 1 7 0 2-1 8-2 9l-1-19z" class="F"></path><path d="M275 534l1 2c0 6 0 12-1 17-1 2-2 5-3 8v-1c1-8 3-17 3-26z" class="O"></path><path d="M274 380c0-4-1-9-2-13s-3-9-2-12v-1l1-1v-1l2 2c1 2 2 3 2 5 1 1 1 1 1 2 0 2 2 5 2 7l1 1-1 1 1 1c0-1 0-1 1-2 1 0 1 0 2-1l1 7h1c0-3-1-6-1-8-1-3 0-6 0-9l2 8c1 2 2 5 3 7 0 1-1 2 0 3h1 0l1-1 2 2c0 1 2 2 2 3h0c1 2 5 5 5 6l-7-6v2l-1-2-1 1c1 3 1 5 2 8 0 2 0 6 1 8h-1c-1-1-4-1-6-1-6-2-12-5-16-10v-1c1-1 2-4 2-6h0c1-2 1-4 1-6v1 1 2c1 1 1 2 1 3z" class="D"></path><path d="M272 379h0c1-2 1-4 1-6v1 1 2c1 1 1 2 1 3s0 1 1 2v4h1v-1l1 1h0v2l1-1v-1-1h1v5h-1-1v-2l-2 2c1 0 1 1 2 1s1 1 2 1l5 2c1 1 2 1 4 1v1h-2c-6-2-12-5-16-10v-1c1-1 2-4 2-6z" class="O"></path><path d="M278 368l1 1-1 1 1 1c0-1 0-1 1-2 1 0 1 0 2-1l1 7c0 2 1 5-1 7-1 1-1 3-1 4h-1c-1-2-1-6-2-8h0c0-1 0-2-1-2v-2c-1 0-1-1-1-1-1-1 0-2-1-3l1 1h0c1-1 2-2 2-3z" class="B"></path><path d="M274 380c0-4-1-9-2-13s-3-9-2-12v-1l1-1v-1l2 2c1 2 2 3 2 5 1 1 1 1 1 2 0 2 2 5 2 7 0 1-1 2-2 3h0l-1-1c1 1 0 2 1 3 0 0 0 1 1 1v2c1 0 1 1 1 2h0c-1 2-1 3 0 5v2 2l-1 1v-2h0l-1-1v1h-1v-4c-1-1-1-1-1-2z" class="E"></path><path d="M275 370v-9h1c0 2 2 5 2 7 0 1-1 2-2 3h0l-1-1z" class="C"></path><path d="M235 260v1s0 1-1 1l1 1h0v2l1 1-3 3h1l1 1h-1v1l-1 1c-4 7-8 14-10 21 0 1-1 3-1 4-2 5-4 14-3 20l3 3v-1c1 0 1 1 2 1h1c1 0 1 1 2 2v1h0v1c1 0 2 0 2 1 2 0 2 0 3 1-2 0-3 0-5-1-5-1-8-3-10-6-1 1-1 1-1 2 1 2 4 5 7 7v1l-2-1c-1-1-3-3-5-4v1c-1-1-4-2-4-4-1 0-1 1-1 1h-1c0-1-1-1-2-2v-5-2l2-5c-1 0 0-1 0-2 2-4 4-8 5-12l1-1c0-1 1-1 0-3v1c-1-2 2-7 2-9 1-3 4-6 6-9l11-13z" class="I"></path><path d="M218 317h1l3 3v-1c1 0 1 1 2 1h1c1 0 1 1 2 2v1h0v1c-1 0-2-1-3-1h-1l-1-1h0c-2-1-3-4-4-5z" class="E"></path><path d="M219 291c0-3 2-5 3-7h1c-5 10-10 24-6 35-1 1-1 1-1 2-4-6-2-12-1-19v-2h-1 0l1-1c0-1 1-3 0-5l1-1 1 2v-3s1-1 2-1z" class="O"></path><path d="M216 293l1 2v-3s1-1 2-1l-4 11v-2h-1 0l1-1c0-1 1-3 0-5l1-1z" class="J"></path><path d="M233 269h1l1 1h-1v1l-1 1c-4 7-8 14-10 21 0 1-1 3-1 4-2 5-4 14-3 20h-1 0v-1c-1-3-1-6 0-9 1-10 4-19 9-28 2-3 4-7 6-10z" class="O"></path><path d="M215 294c1 2 0 4 0 5l-1 1h0 1v2c-1 7-3 13 1 19 1 2 4 5 7 7v1l-2-1c-1-1-3-3-5-4v1c-1-1-4-2-4-4-1 0-1 1-1 1h-1c0-1-1-1-2-2v-5-2l2-5c-1 0 0-1 0-2 2-4 4-8 5-12z" class="D"></path><path d="M210 308h0c0 4 0 9 2 13-1 0-1 1-1 1h-1c0-1-1-1-2-2v-5-2l2-5z" class="Q"></path><path d="M214 300h1v2c-1 7-3 13 1 19 1 2 4 5 7 7v1l-2-1c-1-1-3-3-5-4l-1-3-1-1v-1c-1-2-1-4-2-6-1-1-1-2-1-3 0-4 1-6 3-10z" class="E"></path><defs><linearGradient id="m" x1="227.112" y1="279.793" x2="221.856" y2="276.801" xlink:href="#B"><stop offset="0" stop-color="#29292a"></stop><stop offset="1" stop-color="#494848"></stop></linearGradient></defs><path fill="url(#m)" d="M235 260v1s0 1-1 1l1 1h0v2l1 1-3 3c-2 3-4 7-6 10h-1c-1 2-2 4-3 5h-1c-1 2-3 4-3 7-1 0-2 1-2 1v3l-1-2c0-1 1-1 0-3v1c-1-2 2-7 2-9 1-3 4-6 6-9l11-13z"></path><path d="M222 371c0 1 0 1 1 1v3h1 0c1 1 1 1 1 3h0c0 1 1 2 1 4h-1v-1h0-1l-1 5c0-1 0-1-1-1v8c0 3 0 4 1 6 0 1 0 1-1 1v3h-2l-1 2h-1l-1 1-1 2h-1v1c0 1-1 1-1 2v-3-1h1 1l-1-1v-2h1c0-1-1-2-2-3v1h-1v-1h-1-1l-1-2v-2h0 0c-1-1-1-1-2-1 0-1-1-2-1-3v-1h-1 0c-1-2-1-4-1-6v-4c0-2 0-2-1-3v-5h-1v1l-1 2v2c-2-1-2-3-3-5v3h-2v2c0-1-1-1-1-2v5h-1v-1-1s0-1-1-2l1-1v-1l1-1c-1 0-1 1-2 0v-1h0 1c1-1 1-1 2-1h1v-1c1 0 4 0 5 1h0c3-1 7-1 10-1 0 0 2 1 2 0 3 0 5 0 7-1z" class="E"></path><path d="M223 378l1-1h0l1 4h0-1c-1-1-1-2-1-3z" class="I"></path><path d="M222 385c0-3 1-5 1-7 0 1 0 2 1 3l-1 5c0-1 0-1-1-1h0z" class="B"></path><path d="M196 377h0c1-1 2-2 2-4h3c1 0 1 1 1 2v2 2c-2-1-2-3-3-5v3h-2v2c0-1-1-1-1-2z" class="C"></path><defs><linearGradient id="n" x1="221.334" y1="405.144" x2="214.9" y2="379.19" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#n)" d="M220 382v1-5c1 1 0 2 1 3v1 4h1v-1h0v8c0 3 0 4 1 6 0 1 0 1-1 1v3h-2l-1 2h-1l-1 1-1 2h-1v1c0 1-1 1-1 2v-3-1h1 1l-1-1v-2h1c1-1 2-2 2-3v-1h0v-1h3c0-1 0-1-1-2h-1c-1-1-2-2-2-4 0-1 0-5 1-6l1-1c0-1 0-2 1-4z"></path><path d="M217 393c0-1 0-5 1-6l1-1c0 4 0 7 1 11h-1c-1-1-2-2-2-4z" class="M"></path><path d="M213 378l1-1c0-1 0-2 1-3 1 0 2 1 3 2v-2h1v1c0 1 1 1 1 2v5c-1 2-1 3-1 4l-1 1c-1 1-1 5-1 6s-1 1-1 1l1 3v2c-1 0-1-1-2-2v-4c0-1-1-1-2-2 1-3 1-6 0-9v-4z" class="F"></path><path d="M213 378l1-1c0-1 0-2 1-3 1 0 2 1 3 2v1h-1v-1h-1c0 1 0 3 1 4v5h-1v-6h-1c-1 0-1 1-2 3v-4z" class="I"></path><path d="M205 378v-3h1v2c1-1 1-2 1-2h1v2h1v-3h1v1h1l1-1h1v3 1 4c1 3 1 6 0 9 1 1 2 1 2 2v4c1 1 1 2 2 2v-2l-1-3s1 0 1-1c0 2 1 3 2 4h1c1 1 1 1 1 2h-3v1h0v1c0 1-1 2-2 3 0-1-1-2-2-3v1h-1v-1h-1-1l-1-2v-2h0 0c-1-1-1-1-2-1 0-1-1-2-1-3v-1h-1 0c-1-2-1-4-1-6v-4c1-1 0-3 0-4z" class="C"></path><path d="M210 399l1-1v-1h1v2h1v-1c1 1 1 2 1 3v1h-1v-1h-1-1l-1-2z" class="G"></path><path d="M213 377v1 4c1 3 1 6 0 9 0 2-1 4-2 5v-2h1c1-2 1-10 1-13-1 0-1-1-1-1 0-1 0-2 1-3z" class="K"></path><path d="M205 378h1 0c0 2 0 3 1 4s0 4 1 5v-4h1v3h1v5c0 2-1 2 0 4v-1h1c0 1 0 2-1 3h0 0c-1-1-1-1-2-1 0-1-1-2-1-3v-1h-1 0c-1-2-1-4-1-6v-4c1-1 0-3 0-4z" class="L"></path><path d="M205 386c0 1 1 1 1 2s0 3 1 4h-1 0c-1-2-1-4-1-6z" class="G"></path><path d="M207 393l1 1 1-1v3c1 0 1 0 1 1h0c-1-1-1-1-2-1 0-1-1-2-1-3z" class="N"></path><path d="M208 387v-4h1v3 7c-1-2-1-4-1-6z" class="F"></path><path d="M232 374c1-1 1-3 2-4h0 4 1c0 1 0 3-1 4 0 3-1 6 0 9v4 5l4 9c1 3 3 6 5 9l-1 1c-2-2-3-5-5-7 0 1 2 4 2 6h-1v1c1 1 1 2 1 3v3h-1c-2-2-4-2-6-4 0-1-2-3-3-5h-1c0 1-1 2-1 3h-1v-4c-1-1-3-3-3-5-1-1 0-4 0-6-1 2-1 3-2 5h0v-2c-1-1-1-2-2-2 0-1-1-3-1-4v-8c1 0 1 0 1 1l1-5h1 0v1h1c0-2-1-3-1-4v-2h1v1l1-4v-1c1 0 1 0 1-1v-1 1h1v-1c1 1 2 2 2 3v3h1v-2z" class="C"></path><path d="M235 400c1-2 1-3 1-5v1c0 2 0 3 1 5v1c-1-1-1-2-2-2h0z" class="M"></path><path d="M226 391h1v4l-1 1v-1-4z" class="F"></path><defs><linearGradient id="o" x1="229.841" y1="373.271" x2="225.213" y2="377.26" xlink:href="#B"><stop offset="0" stop-color="#151617"></stop><stop offset="1" stop-color="#2e2f2d"></stop></linearGradient></defs><path fill="url(#o)" d="M228 371v-1 1h1c0 1-1 3-1 4v6l-1 1c0-2-1-4-1-5l1-4v-1c1 0 1 0 1-1z"></path><path d="M225 378v-2h1v1c0 1 1 3 1 5h0c1 2-1 6-1 9v4c-1 1-1 1-1 2v2c-1-1-1-2-2-2l2-2v-5-9h0v1h1c0-2-1-3-1-4z" class="B"></path><path d="M224 381h1v9 5l-2 2c0-1-1-3-1-4v-8c1 0 1 0 1 1l1-5z" class="C"></path><path d="M229 370c1 1 2 2 2 3v3h1v-2 11h-1c-2-2-1-7-3-10 0-1 1-3 1-4v-1z" class="G"></path><path d="M238 392l4 9c1 3 3 6 5 9l-1 1c-2-2-3-5-5-7h-1l-2-7c-1-1-1-2-1-3 1 0 1-1 1-1v-1z" class="B"></path><defs><linearGradient id="p" x1="238.358" y1="370.124" x2="231.274" y2="397.7" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#p)" d="M232 374c1-1 1-3 2-4h0 4 1c0 1 0 3-1 4 0 3-1 6 0 9v4c-1 3 0 6-2 8 0 2 0 3-1 5v-6c-1-3-3-5-3-8v-1-11z"></path><path d="M333 297c0 1-1 3-2 4l-8 19c-5 10-9 21-12 32-1 3-2 6-2 10-7-2-11-6-15-11 2 1 5 2 8 3 1 0 5 1 6 0v-1h-1c-5 0-8-2-12-6 3 1 7 3 10 4h3c0-1-2-1-4-2-2 0-5-2-7-4 4 1 9 2 14 1h0c-4 0-9-1-13-2l8-1c-4-1-7 0-10-3-1 0-1 0-1-1h5c1 0 4 1 5 0v-1h-4c-1 0-2 0-3-1 0-1 3-2 4-2 3-2 5-5 7-7 5-6 9-14 13-21l4-6c0-1 1-2 1-2 1-1 4-1 5-2h1z" class="X"></path><path d="M294 219c2 0 4 1 6 2 1-1 1-1 2-1 1 1 2 1 3 2 1 0 1 1 2 1l1 1h0c3 0 7 3 9 4l1 1s1 0 1-1c1 1 2 3 2 5v2h-1 0c0 1 0 0-1 1h0-3 0c1 1 2 1 2 2h0l-6-2c-1 1-2 2-3 2h1v1s1 0 0 1h-1 0c1 1 1 1 2 1v1h0l-2-1c-2 0-4 1-6 1h0l-1 1-1-1h-2l1-1v-1h-1-2 1c-1 0-2-1-3-1-1-1-2-1-3-1-1-1-2 0-3-1h2v-1c-5-2-12-1-16-4v-3c5 0 7-3 10-5 1-1 1-1 1-2 1 0 2 0 2 1l2-1v-1h-2 1v-1c2 0 3 1 5 0h-1l1-1h0z" class="G"></path><path d="M295 239c2 0 4 0 5-1h0 2v1c-1 1-2 1-3 1h0-2 1c-1 0-2-1-3-1z" class="L"></path><path d="M297 229c1 0 2 1 3 1h1v1c-2 0-5 1-6 0 0 0 0-1 1-1l1-1h0z" class="B"></path><path d="M309 235l3 1c-1 1-2 2-3 2h1v1s1 0 0 1h-1 0c1 1 1 1 2 1v1h0l-2-1c-2 0-4 1-6 1h0l-1 1-1-1h-2l1-1v-1h-1 0c1 0 2 0 3-1l5-1c1-1 1-1 1-2l1-1z" class="D"></path><path d="M302 239c1 1 1 1 2 1 1-1 4-1 5-1v1h0c-1 0-6 1-6 2h0 0l-1 1-1-1h-2l1-1v-1h-1 0c1 0 2 0 3-1z" class="G"></path><path d="M298 237c-2 0-3 0-5-1 1-1 2-1 3-2v-1s1 0 1-1h5c1 1 2 1 3 1v-1h1c2 2 6 2 9 3h0s1 0 1 1h0c1 1 2 1 2 2h0l-6-2-3-1c-1 0-1 0-2 1h0-2c-2 1-5 1-7 1z" class="L"></path><path d="M298 237v-1l5-1c1 0 2 0 2 1h0c-2 1-5 1-7 1z" class="C"></path><path d="M286 222c1 0 2 0 2 1l6 1c-1 1-2 1-3 1s-2 0-3 1v1c0 1 1 2 1 2 1 0 1 0 2 1 0 1-1 1-1 1 2 0 3 1 4 1-1 1-2 2-4 2-4 1-11-1-15-2 4-2 7-4 11-7l-1-1c1-1 1-1 1-2z" class="P"></path><path d="M286 222c1 0 2 0 2 1l6 1c-1 1-2 1-3 1s-2 0-3 1v1c0 1 1 2 1 2-1 0-1 0-2-1v-2l1-1-1-1-1 1-1-1c1-1 1-1 1-2z" class="D"></path><path d="M294 219c2 0 4 1 6 2 1-1 1-1 2-1 1 1 2 1 3 2 1 0 1 1 2 1l1 1h0c3 0 7 3 9 4l1 1s1 0 1-1c1 1 2 3 2 5v2h-1 0c0 1 0 0-1 1h0-3c0-1-1-1-1-1h0c-3-1-7-1-9-3h-1c-1 0-2-1-4-1v-1h-1c-1 0-2-1-3-1h-6v1c-1-1-1-1-2-1 0 0-1-1-1-2v-1c1-1 2-1 3-1s2 0 3-1l-6-1 2-1v-1h-2 1v-1c2 0 3 1 5 0h-1l1-1h0z" class="B"></path><path d="M294 219c2 0 4 1 6 2 1 1 2 1 3 2-2 0-6-2-9-3h0-1l1-1h0z" class="H"></path><path d="M300 227c1-1 2-1 3-1 3 1 6 1 9 3 1 1 2 2 4 3 1 0 4 2 4 3h0c0 1 0 0-1 1h0v-1c0-1 0-1-1-1-2-1-3-2-5-3-3 0-7-1-10-2 0-1-1-1-2-2h-1z" class="J"></path><g class="B"><path d="M301 227c4 0 9 1 12 4-3 0-7-1-10-2 0-1-1-1-2-2z"></path><path d="M303 230v-1c3 1 7 2 10 2 2 1 3 2 5 3 1 0 1 0 1 1v1h-3c0-1-1-1-1-1h0c-3-1-7-1-9-3h-1c-1 0-2-1-4-1 1-1 1-1 2-1z"></path></g><path d="M303 230v-1c3 1 7 2 10 2 2 1 3 2 5 3h-2l-13-4z" class="F"></path><path d="M294 224h2c2-1 4 0 6 1l1 1h0c-1 0-2 0-3 1h1c1 1 2 1 2 2v1c-1 0-1 0-2 1v-1h-1c-1 0-2-1-3-1h-6v1c-1-1-1-1-2-1 0 0-1-1-1-2v-1c1-1 2-1 3-1s2 0 3-1z" class="L"></path><path d="M289 229s-1-1-1-2v-1c1-1 2-1 3-1h0v1c0 1 1 1 2 1l-2 1-1 1h1v1c-1-1-1-1-2-1z" class="J"></path><path d="M294 224h2c2-1 4 0 6 1l1 1h0c-1 0-2 0-3 1h0c-1 0-2-1-2-1l-1-1h-6 0c1 0 2 0 3-1z" class="G"></path><path d="M300 227h1c1 1 2 1 2 2v1c-1 0-1 0-2 1v-1h-1c-1 0-2-1-3-1-1-1-3 0-5 0 2-1 3-2 5-2h2 1 0z" class="C"></path><path d="M60 531l1 1v1 3c0-2 0-4 1-5 0-1-1-3-1-4 0-4 0-10 1-14l1-1v2c-1 6-1 14 1 20 0-2 0-4 1-6 1 5 0 10 1 15 0-3 0-5 1-8 1 4 1 7 1 10h2c1 2 1 5 1 7 0 4 2 8 3 12 1 2 0 4 1 5h1c2 1 3 8 3 10 2 7 3 13 6 19 1 4 2 7 4 10 1 1 1 2 1 4-3-3-6-7-9-11l-5-9c-6-10-9-19-12-30-1-3-1-6-2-9-1-7-2-15-2-22z" class="B"></path><path d="M81 596h0c3 4 4 9 7 12h1c1 1 1 2 1 4-3-3-6-7-9-11 1-2 1-3 0-5z" class="J"></path><path d="M71 573h1l3 12v1c1 1 1 2 1 2 0 1 1 1 1 1 0 1 0 1 1 2s2 4 3 5c1 2 1 3 0 5l-5-9v-1c-2-6-4-11-5-18z" class="C"></path><path d="M64 562c0-1 0-2 1-3l3 3c1 1 1 2 1 3l2 8c1 7 3 12 5 18v1c-6-10-9-19-12-30z" class="Q"></path><defs><linearGradient id="q" x1="253.431" y1="382.927" x2="239.82" y2="396.613" xlink:href="#B"><stop offset="0" stop-color="#2f2e2f"></stop><stop offset="1" stop-color="#4f4e4e"></stop></linearGradient></defs><path fill="url(#q)" d="M250 367l1 1v2c0 1 1 2 2 3 1 3 2 7 3 10l1 4h1c1 1 1 1 1 2 1 1 1 2 2 3l2-2c1 0 1 0 2 1v2h0c0 2-1 5-2 7l6 6h1 0v2c-2-2-5-4-6-6-1 0-1 0-1 1-1-1-1-1-2-1v-1h-1v1c-1 0-2-2-3-3h0l1 2h0-1-1c0-1-1-1-2-2v-1 1h0l-1 1c0-1-1-1-1-2h-1c0 2 0 3 1 4 1 2 1 4 2 5-1 1-2 1-2 2v2h0c-1 1-1 1-1 3h-1c-1-1-2-2-3-4-2-3-4-6-5-9l-4-9v-5-4 2c0-2 1-3 1-4h0l1-1 1-6c0-1 1-3 2-4h2c1 1 1 1 1 2h1c1-1 1-2 3-3l-1-1 1-1z"></path><path d="M245 379h1l1-2h0c1 1 1 2 1 3h-1v-1c-1 1 0 3-1 4h-1v-4zm2 13c0-1-1-3-1-4 1-1 2 0 3 0l1 1-3 3z" class="L"></path><path d="M244 402h1c3 1 3 5 6 5h1c0-1 0-2-1-3l1-2c1 2 1 4 2 5-1 1-2 1-2 2l-2-1h-1c-2-1-4-4-5-6z" class="E"></path><path d="M247 392l3-3c0 2 3 9 4 10h0l-1 1c0-1-1-1-1-2h-1c-1 0-1 0-1-1s-1-2-2-3c0-1 0-2-1-2z" class="I"></path><path d="M242 401v-2l1 1 1 2c1 2 3 5 5 6h1l2 1v2h0c-1 1-1 1-1 3h-1c-1-1-2-2-3-4-2-3-4-6-5-9z" class="H"></path><path d="M240 380v-1h1v2c1 2 1 4 0 6v7 3c1 1 2 2 2 3l-1-1v2l-4-9v-5-4 2c0-2 1-3 1-4h0l1-1z" class="F"></path><path d="M239 381c0 4 0 8 1 12 0 1 0 3 1 4s2 2 2 3l-1-1v2l-4-9v-5-4 2c0-2 1-3 1-4h0z" class="G"></path><path d="M250 367l1 1v2l-1 1c0 2 0 5 1 7 0 2-1 3-1 5v2c0 1 1 2 1 3 1 3 2 7 4 11l2 2h-1c0-1-1-1-2-2v-1 1c-1-1-4-8-4-10l-1-1-2-8h1c0-1 0-2-1-3h0l-1 2h-1c0-2-1-5-2-6l-2 1c0-1 1-3 2-4h2c1 1 1 1 1 2h1c1-1 1-2 3-3l-1-1 1-1z" class="D"></path><path d="M257 401l-2-2c-2-4-3-8-4-11 0-1-1-2-1-3v-2c0-2 1-3 1-5-1-2-1-5-1-7l1-1c0 1 1 2 2 3 1 3 2 7 3 10l1 4h1c1 1 1 1 1 2 1 1 1 2 2 3l2-2c1 0 1 0 2 1v2h0c0 2-1 5-2 7l6 6h1 0v2c-2-2-5-4-6-6-1 0-1 0-1 1-1-1-1-1-2-1v-1h-1v1c-1 0-2-2-3-3h0l1 2h0-1z" class="B"></path><path d="M256 383l1 4h1c1 1 1 1 1 2 1 1 1 2 2 3l2-2c1 0 1 0 2 1v2h0c0 2-1 5-2 7l6 6h1 0v2c-2-2-5-4-6-6 0-1-2-3-3-4-2-2-3-5-4-8l-1-1c0-1-1-2-1-3v-3 1l1-1z" class="F"></path><path d="M257 387h1c1 1 1 1 1 2 1 1 1 2 2 3l2-2c1 0 1 0 2 1v2h0c0 2-1 5-2 7-2-4-5-8-6-13z" class="D"></path><path d="M300 373l-3-3h1c4 3 10 5 15 6v-1s-6-2-7-2c-4-2-8-5-11-9 4 3 9 6 13 7 2 0 4 0 5-1h0-2c-6-1-10-4-13-8 3 1 5 2 9 2v1 1h0 0c2-4 5-1 8-2v-1h-6c0-2 1-6 2-8 3-10 6-20 10-29l9-20 3-8c1-3 4-4 5-6l-21 76h0c0 4-1 7-2 11-4 0-8-1-13-1h-1l-3-3-1-2h2 1z" class="X"></path><path d="M317 368h0c0 4-1 7-2 11-4 0-8-1-13-1h-1l-3-3-1-2h2 1l6 4 9 2 2-11z" class="D"></path><path d="M300 373l6 4c-1 0-2 0-3-1h-1c-1 0-1-1-2-1h-2l-1-2h2 1z" class="N"></path><defs><linearGradient id="r" x1="207.055" y1="444.589" x2="211.247" y2="430.071" xlink:href="#B"><stop offset="0" stop-color="#3d3c3d"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#r)" d="M191 425h0c7-3 16-2 23-2 1 1 3 2 4 2h0-3c-2-2-7-1-9-1h5c1 0 3 0 3 1h1l2 2h0c1 0 1 1 2 1 8 2 16 4 23 9l4 2c-1 1-1 1-1 2h1l-2 2h0v2c-1 0-2-1-3-1 1 1 3 3 4 3h-1v1l-5-2 13 11c-2 0-4-2-6-2 1 2 1 2 3 3l1 2h0v1l-5-4c1 0 0 0 1-1-3-2-6-5-8-7-5-5-11-8-17-11-14-6-31-6-46-4l-12 3c2-2 6-3 9-4 5-2 9-5 13-6l6-2z"></path><path d="M221 438h3c3 1 6 3 8 5 1 0 3 1 4 2h-1c2 1 3 2 3 4-5-5-11-8-17-11z" class="F"></path><path d="M191 425h0c7-3 16-2 23-2 1 1 3 2 4 2h0-3c-2-2-7-1-9-1h5c1 0 3 0 3 1h1l2 2h0c1 0 1 1 2 1 8 2 16 4 23 9l4 2c-1 1-1 1-1 2h1l-2 2-3-2c-11-6-22-10-35-12-4 0-8-1-12-1-5 1-9 2-14 2 4-2 8-3 12-4l-1-1z" class="P"></path><path d="M242 437l4 2c-1 1-1 1-1 2h1l-2 2-3-2c1 0 2 0 2-1s-1-2-1-3zm-51-12h0c7-3 16-2 23-2 1 1 3 2 4 2h0-3c-2-2-7-1-9-1h5c1 0 3 0 3 1h1l2 2h0c1 0 1 1 2 1-3 0-5-1-7-1v-1c1 0 1 0 2-1h-1c-7-1-14 0-21 1l-1-1z" class="L"></path><path d="M194 140s-1-1-1-2c11-10 22-18 35-25 25-13 53-17 81-18h39 63 8 2c1 0 1 1 1 1l1-1c1-1 4-1 5-1h15l1 1-1 1v-1h-19c-1 1-1 1-1 2 1 1 4-1 6 0s4 0 6 0h3 6 10c4 1 21-1 22 0-3 0-6 0-9 1h-52-43l-45-1c-14 1-28 1-41 2-35 4-68 17-92 43v-2z" class="U"></path><path d="M244 355h2c2 1 5 4 6 5 0 2 2 3 4 4v1l3 6c2 4 5 11 10 14l1-1v1 1c4 5 10 8 16 10 2 0 5 0 6 1h1c1 0 4-1 4 0-1 1-3 0-4 1v1l1 2s-1 0-1 1v15c0 1 0 3-1 5 0 5-2 10-3 14l-4-19-1-7c0-2-1-5 0-6v-1h0-1l1-1h0 0-1-1c0-1 0-1-1-1h0-1c-1-1-1-1-2-1-1 3-2 5-2 7v1h-1c0-1 0-1-1-2 0 1 0 2-1 3l-3-3h0-1l-6-6c1-2 2-5 2-7h0v-2c-1-1-1-1-2-1-3-3-5-7-7-11h0c-1-1-1-3-1-4l2 2 1 3h0l2 2h1s0-1-1-2c0-1-1-3-2-5-2-3-3-5-5-8 0 0-1 1-2 1l-1-2c-3-2-5-3-6-6-1-1-1-3 0-4v-1z" class="Q"></path><path d="M270 406h1c2-1 2-4 3-5 0 1-1 4 0 5 0 1 0 2-1 3l-3-3h0z" class="H"></path><path d="M274 401c0-1 1-2 2-3h0v3h1l1-1c-1 3-2 5-2 7v1h-1c0-1 0-1-1-2s0-4 0-5z" class="G"></path><path d="M276 401h1l1-1c-1 3-2 5-2 7v1h-1c0-2 0-5 1-7z" class="H"></path><path d="M293 399l1 2s-1 0-1 1v15c0 1 0 3-1 5v-3h0s0 1-1 1v-1c-1 1-1 4-1 6l-4-15c-1-2-1-5 0-7h1l1-1c1 0 1 0 2-1h2 0 1v-2z" class="G"></path><path d="M263 390c-3-3-5-7-7-11h0c-1-1-1-3-1-4l2 2 1 3h0l2 2h1s0-1-1-2c2 2 3 4 5 6 4 5 12 10 19 11h8 1c1 0 4-1 4 0-1 1-3 0-4 1v1 2h-1v-3s-1 0-1-1c1 1 1 1 1 2l-1 1h-3l-15-4c-1 2-2 6-3 8l-1 2-6-6c1-2 2-5 2-7h0v-2c-1-1-1-1-2-1z" class="E"></path><path d="M269 393c1 0 2 1 3 2v1c-1 0-1 0-2-1-1 1-1 1-1 2h-1c0-2 0-3 1-4z" class="D"></path><path d="M265 391c1 1 1 1 2 1l2 1c-1 1-1 2-1 4h1c-1 0-1 0-1 1v-2h-1c0 1-1 2-1 3h0c0 1 0 1-1 1v-3l1-1c0-2 0-2-1-3h0v-2z" class="G"></path><path d="M258 380h0l2 2h1s0-1-1-2c2 2 3 4 5 6 4 5 12 10 19 11h8 1c1 0 4-1 4 0-1 1-3 0-4 1v1 2h-1v-3s-1 0-1-1c1 1 1 1 1 2l-1 1v-1-1h-5c-8-1-18-5-23-11l-3-3c-1-1-1-2-2-4z" class="H"></path><path d="M244 355h2c2 1 5 4 6 5 0 2 2 3 4 4v1l3 6c2 4 5 11 10 14l1-1v1 1c4 5 10 8 16 10 2 0 5 0 6 1h-8c-7-1-15-6-19-11-2-2-3-4-5-6 0-1-1-3-2-5-2-3-3-5-5-8 0 0-1 1-2 1l-1-2c-3-2-5-3-6-6-1-1-1-3 0-4v-1z" class="B"></path><path d="M250 366l1-1c1 1 2 1 2 2 0 0-1 1-2 1l-1-2z" class="E"></path><path d="M244 355h2c2 1 5 4 6 5-1-1-2-1-3-1l-3-3v1c-1 1-1 2-2 3-1-1-1-3 0-4v-1z" class="G"></path><path d="M219 428c-1 0-1-1-2-1h0l-2-2h-1c0-1-2-1-3-1h-5c2 0 7-1 9 1h3l10 2c8 2 15 4 22 8 4 3 7 6 10 9 4 2 7 5 10 8 4 3 7 7 10 10 2 4 5 8 7 12 2 3 3 6 4 8h1c-1-3-3-7-4-10-5-8-10-16-17-22l-6-6h1c1 0 3 2 4 4 8 7 15 16 20 26 3 7 5 14 7 22 1 4 2 9 2 14v18 1l-1-1v-14c0 3 0 6-1 10v-4c-1-3 0-6-1-9-1 6-1 11-1 16-1 2-1 4-1 6l-2 10h-1c1-3 1-7 1-10 1-11 2-22-1-32-3-15-11-30-20-42-1-2-5-5-5-7h-1c-1-1-1-2-2-2-1-1-2-2-3-2s-3-2-3-2c-1-2-2-3-3-3-2-2-5-4-7-5l-1 1-4-2c-7-5-15-7-23-9z" class="C"></path><path d="M271 459c-1-2-5-5-5-7 2 2 4 3 6 5 2 3 4 5 6 8-2-4-6-9-9-12h1c2 1 4 4 6 7 4 5 8 10 11 16 2 3 3 7 5 11 1 3 3 7 4 10 1 6 2 11 2 17 0 3 0 6-1 10v-4c-1-3 0-6-1-9-1 6-1 11-1 16-1 2-1 4-1 6l-2 10h-1c1-3 1-7 1-10 1-11 2-22-1-32-3-15-11-30-20-42z" class="Q"></path><path d="M161 442c19-8 43-6 62 3l6 2c1 1 2 1 3 2h-2c1 1 1 1 2 1l2 1c0 1 0 0 1 1h0l9 6a30.44 30.44 0 0 1 8 8c2 1 3 2 4 4h0c0 2 1 2 2 3 2 3 5 8 7 12h-1 0c-1-1-2-4-4-5v1h0l2 3 1 2 2 3c1 1 1 2 1 3v1c2 5 4 9 6 14s2 10 3 14c0 2 1 5 1 7v3 5l-1-2c-1-17-9-35-19-50l-1 1c-1-1-2-2-3-4l-5-6c-6-7-13-14-22-19-5-3-12-5-19-7v-1l-3-1c-4-2-10-2-14-3h2c-1-1-3-1-4-1-4-1-8 0-12-1 1-1 3-1 4-1l-3-1h2l1-1-7 1-2 1c-3 0-6 1-9 1h0z" class="F"></path><path d="M236 459v-1c-1-1-1-2-2-2-2-1-3-2-4-4 5 2 8 6 11 9 2 0 2 1 2 3h0l-7-5z" class="C"></path><path d="M237 456c-1-1-2-3-3-3l-12-6-6-3c2-1 9 4 12 4l1-1c1 1 2 1 3 2h-2c1 1 1 1 2 1l2 1c0 1 0 0 1 1h0l9 6h-1c-1-1-3-2-4-3-1 0-1 0-2 1z" class="L"></path><path d="M237 456c1-1 1-1 2-1 1 1 3 2 4 3h1a30.44 30.44 0 0 1 8 8c2 1 3 2 4 4h0c0 2 1 2 2 3h-1c-5-5-10-10-16-14-2-1-3-2-4-3z" class="I"></path><path d="M256 484c-2-3-5-7-8-10l-6-6c-2-3-6-5-9-8l-8-6c2 1 4 1 5 1s5 3 6 4l7 5c10 8 17 18 23 29 2 5 4 9 6 14s2 10 3 14c0 2 1 5 1 7v3 5l-1-2c-1-17-9-35-19-50z" class="P"></path><path d="M446 107c4-1 10-1 15 0 4 1 8 5 10 8v1c2-3 3-5 6-6h1c1 0 3 1 4 2s2 1 4 1c3 0 7-3 11-4-3 2-9 7-13 7-1 0-3-1-5-1l-4 4c-1 1-1 3 0 5 1 0 2-1 3-1v1l-9 15c-3 5-7 10-13 11-4 1-8 0-11-2-4-2-7-7-7-10v-1-1c-1-4 0-11 2-14 1-1 3-2 4-2s1 0 2 1c0 1 0 1 1 1s2-1 3-1c2 0 8 1 10 3 2 1 3 3 3 5 0 3-1 5-3 6-1 2-4 3-6 3h-2c-1-1-2-2-2-3s0-3 1-4c0-1 1-1 2-1 3 1 1 3 2 5 1-1 2-1 2-2 1-1 2-3 1-4 0-1-2-2-3-2-2-1-3-1-5 0-2 0-4 3-5 5s-1 6 1 9c1 1 2 3 4 3 4 1 7 0 10-2 4-2 8-8 9-12s0-9-2-12c-1-1-2-2-3-2-1 1-1 2-3 3h-3c0-1 0-1-1-2v-1c-1 1-2 2-4 2v-1s-1-1 0-2c0-1 2-3 3-4h1c-4 0-8-1-12 0-7 1-14 5-20 9 1 1 3 4 3 6-1 1-2 1-3 0-1 0-2-1-2-2l-6-4c0-1 2-3 3-4h1c1-1 2-3 3-4 4 1 10-3 14-4 3-1 5-1 8-1z" class="S"></path><path d="M176 314v3h2v-3h1s1 1 1 2h1l-1 1 1 1c0-1 1-1 1-1v1h0c0 1-1 2-1 3h0c-2 2-3 4-4 5s-2 2-2 3c-1 0-1 0-1-1-2 4-2 7-3 11-1 1-1 2-2 3v6 4c0 1 1 3 1 5l-2-2v2c1 2 3 4 4 5v1c1 0 1 1 2 2l1 1h-1c1 1 3 1 4 1 2 0 4 1 6 1 1 1 3 1 4 2h-2c0 1 1 1 2 2v1h-2-1l-1 1v1h-1v-2h-4c-3 0-6 0-8-1-6-1-11-5-15-9l-1-1h-1l-2-6s-1-2-1-3v1c-1 1-2 1-3 2 0 1-2 6-2 8h1c0 1 0 3-1 3-1 1-1 2-1 3v-3-2-2c-1 1-3 4-3 5-2 4-2 8-3 11h0c-1-4 1-10 2-13h0c-1 1-2 2-2 3v1 2c0 1 0 1-1 2v1-3c1-1 1-3 1-5h0-1 0 0-1s0-1 1-1h0v-1-1c-1 1-1 2-2 2s-1 2-2 2l3-8c1-1 2-2 2-3h0v1h1c2-2 3-6 4-8 1-3 3-5 4-7l9-11h-2v-2l2-1 1-1c1 0 2-2 3-2l7-6 4-3c1-1 3-2 4-3h0z" class="M"></path><path d="M138 367c0-2 0-3 1-4h1 0c0 1-1 3-1 4h-1 0z" class="I"></path><path d="M158 343v2-1c1 1 0 3 1 5l-1 1h-1v4c-1-3 0-7 1-11zm-21 17c1-1 2-2 2-3h0v1h1c-1 3-3 5-4 8-1 0-1 2-2 2l3-8z" class="D"></path><path d="M144 355c2-5 5-9 8-13 1 1 0 3 0 4-1 2-1 3-2 4-1 2-3 4-4 6 0 1-1 1-1 1h-1v-2z" class="H"></path><path d="M163 367l1-1c1 0 3 1 5 2h1c2 1 3 1 5 1 1-1 3-1 5-1 1 0 2 1 3 1l1-1c1 1 3 1 4 2h-2c-4 1-8 1-12 1-2 0-3 0-5-1-2 0-2 0-3-1-1 0-2-1-3-2z" class="L"></path><path d="M166 369c1 0 1-1 2-1 0 1 0 0 1 1 0 1 2 1 3 1 0 0 1 0 2 1-2 0-3 0-5-1-2 0-2 0-3-1z" class="I"></path><path d="M159 359c1 1 1 2 2 3l1-1 2 1h0 1c3 3 5 4 9 4 1 1 3 1 4 1 2 0 4 1 6 1l-1 1c-1 0-2-1-3-1-2 0-4 0-5 1-2 0-3 0-5-1h-1c-2-1-4-2-5-2l-1 1s-1 0-1-1h0c-2 0-2-2-2-3-1-1-1-2-1-4z" class="B"></path><path d="M162 361l2 1h0l1 2v1c-1-1-3-1-4-3l1-1z" class="D"></path><path d="M144 355v2h1s1 0 1-1c1-2 3-4 4-6 1-1 1-2 2-4 0 3 0 5-1 7v1c-1 1-2 1-3 2 0 1-2 6-2 8h1c0 1 0 3-1 3-1 1-1 2-1 3v-3-2-2c-1 1-3 4-3 5-2 4-2 8-3 11h0c-1-4 1-10 2-13v-1-1c1-1 1-1 1-2h0c-1-2 1-5 2-7h0z" class="G"></path><path d="M144 355l-1 3c1 1 1 2 1 4 0 1-1 2-1 3h-2v-1c1-1 1-1 1-2h0c-1-2 1-5 2-7z" class="B"></path><path d="M168 320h1c-2 2-4 4-5 6-2 1-3 2-5 3h1c1 0 2 0 3 1l-1 1 1 1-2 4c-2 2-3 5-4 7v-2c-1 3-2 6-2 10 0 1 1 3 0 5h-1v-1c-2-8 1-16 4-23v-1l-1 1h-2v-2l2-1 1-1c1 0 2-2 3-2l7-6z" class="N"></path><path d="M157 329l2 2-1 1v-1l-1 1h-2v-2l2-1z" class="E"></path><path d="M162 331l1 1-2 4c-2 2-3 5-4 7v-2c1-4 3-7 5-10z" class="K"></path><path d="M176 314v3h2v-3h1s1 1 1 2h1l-1 1 1 1c0-1 1-1 1-1v1h0c0 1-1 2-1 3h0c-2 2-3 4-4 5s-2 2-2 3c-1 0-1 0-1-1-2 4-2 7-3 11-1 1-1 2-2 3v6 4c0 1 1 3 1 5l-2-2v2c1 2 3 4 4 5v1c1 0 1 1 2 2l1 1h-1c-4 0-6-1-9-4h-1 0l-2-1-1 1c-1-1-1-2-2-3-1-2-1-3-2-5v-4h1l1-1c-1-2 0-4-1-5v1-2h-1c1-2 2-5 4-7l2-4-1-1 1-1c-1-1-2-1-3-1h-1c2-1 3-2 5-3 1-2 3-4 5-6h-1l4-3c1-1 3-2 4-3h0z" class="D"></path><path d="M174 328h0c1-3 4-6 6-8l1 1c-2 2-3 4-4 5s-2 2-2 3c-1 0-1 0-1-1z" class="B"></path><path d="M168 355h0c-1-1-2-2-2-4 0-3 1-8 3-11h0v2 6 4c0 1 1 3 1 5l-2-2z" class="Q"></path><path d="M168 335h0l2-3h0c-3 6-5 12-6 18v8c0 1 0 3 1 4h-1 0l-2-1-1-2c1-1 1-2 1-3v-3c1-6 3-12 6-18z" class="C"></path><path d="M162 356v-3c0 2 0 4 1 6 0 1 0 2 1 3l-2-1-1-2c1-1 1-2 1-3z" class="G"></path><path d="M164 358v-1c1 0 4 5 5 6h1l-3-4c-1-2-3-4-2-6h0l3 4c1 2 3 4 4 5v1c1 0 1 1 2 2l1 1h-1c-4 0-6-1-9-4-1-1-1-3-1-4zm3-25l1 2c-3 6-5 12-6 18v3c0 1 0 2-1 3l1 2-1 1c-1-1-1-2-2-3-1-2-1-3-2-5v-4h1l1-1c0-1 1-2 1-3 2-5 4-9 7-13z" class="O"></path><path d="M161 359c-1-2-1-4-1-6v2l1 1v-2l1 2c0 1 0 2-1 3z" class="D"></path><path d="M176 314v3h2v-3h1s1 1 1 2h1l-1 1c-2 3-4 7-7 9 0 1-1 3-2 4h0v-2h0l-4 5c-3 4-5 8-7 13 0 1-1 2-1 3-1-2 0-4-1-5v1-2h-1c1-2 2-5 4-7l2-4-1-1 1-1c-1-1-2-1-3-1h-1c2-1 3-2 5-3 1-2 3-4 5-6h-1l4-3c1-1 3-2 4-3h0z" class="C"></path><path d="M164 326c2 0 2-3 5-3-2 1-4 6-6 7-1-1-2-1-3-1h-1c2-1 3-2 5-3zm-3 10c1 0 2-1 3-2s1-2 2-3c0 2-2 4-3 6-1 1-4 7-4 8l1 1c0 1-1 2-1 3-1-2 0-4-1-5v1-2h-1c1-2 2-5 4-7z" class="L"></path><defs><linearGradient id="s" x1="175.394" y1="315.132" x2="171.736" y2="323.866" xlink:href="#B"><stop offset="0" stop-color="#262525"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#s)" d="M176 314v3h2v-3h1s1 1 1 2h1l-1 1c-2 3-4 7-7 9 1-1 2-2 2-3l1-2v-1h-2c-1 1-1 2-2 2h-1c-1 0-2 0-2 1-3 0-3 3-5 3 1-2 3-4 5-6h-1l4-3c1-1 3-2 4-3h0z"></path><path d="M176 314v3h2v-3h1s1 1 1 2c-1 1-2 1-4 1h0v-3h0z" class="D"></path><path d="M513 112l-1 1v1c5 1 10 2 14 4 19 8 30 25 37 43 3 7 4 15 5 22 1 13 0 25-5 37-2 3-3 7-5 10-7 10-18 21-29 25s-27 6-38 1c-6-3-12-8-15-16-2-5-2-13 0-18 3-5 7-10 12-12 4-1 7-1 10 1 3 1 4 3 5 5 1 4 0 8-2 12-1 1-3 2-5 3-2 0-5 0-7-2-2-1-3-3-3-5-1-1 0-3 1-4 1-2 3-3 5-3 1 0 2 1 3 1 1 2 1 3 1 4s-1 2-2 3h-3-1v1c0 1 1 1 2 1 1 1 3 1 4 0 2-1 3-2 3-4 1-2 1-4 0-6s-4-4-6-4c-2-1-5 0-7 2-4 3-6 7-7 12-2 6 0 12 4 18 3 5 11 9 17 10 13 3 28-2 38-9 3-2 6-4 9-7 1-2 3-4 4-6 3-4 6-8 8-13 3-6 4-12 5-19 1-4 1-8 1-13-1-16-5-32-15-45-7-10-20-20-32-24-4-2-9-2-14-3 2-2 6-4 8-5v1h1z" class="S"></path><path d="M492 220h2v2l-1 1-2-1h-1c1-1 1-2 2-2z" class="P"></path><path d="M122 424l1-4h0c0-1 0-1 1-2v1c0 3 0 6 1 8 1 1 1 2 2 3h0c0 3 1 5 2 7 0 2 1 5 2 7l3 12c0 1 1 2 0 3l-1 1c-1-1-2-3-2-5-1 1-1 3-2 4 1 3 2 7 2 10h0-1v3h0l-1 2c-1-1-1 0-1-1-1 0 0 0-1-1-1 3-2 5-3 8l-2 7c0 3 0 6-1 9v2l-1 4c0 1 0 3-1 4l-1-3c0-5 1-10 2-16v-3h-1c-1 1-1 3-1 5 0-3 0-5-1-7h-1c1 1 1 2 0 3 0-2 0-3-1-4v1c0 1 0 2 1 3v1h0c-1 0-2-2-2-3h0-1c-1-1-1-1-3-1v-2h-1c0 2 1 5 0 7v-1 2l-2-3h0c-1-1-1-3-1-4-1-2-1-5-2-7 0-1 0-3-1-4v-1h0 0c1-1 1-2 2-3l1-2c0-3 1-5 0-7l3-9c0-1 1-2 1-3 1-1 2-3 3-5l3-6h0c2-3 5-6 6-10z" class="B"></path><defs><linearGradient id="t" x1="126.475" y1="430.397" x2="121.068" y2="421.478" xlink:href="#B"><stop offset="0" stop-color="#1d1c1c"></stop><stop offset="1" stop-color="#363637"></stop></linearGradient></defs><path fill="url(#t)" d="M122 424l1-4h0c0-1 0-1 1-2v1c0 3 0 6 1 8v5c1 2 4 9 3 10-1-2-3-5-3-7l-1-1c0-3 0-5-1-8h-1v-1-1z"></path><path d="M127 451v-2l1 2c0 1 1 1 2 2l1 2h0c-1 1-1 3-2 4 1 3 2 7 2 10h0-1v3h0l-1 2c-1-1-1 0-1-1-1 0 0 0-1-1 0 0 1-1 1-2 1-1 1-2 1-3 1-3 0-5-1-8 0 0-1-2-1-3 0-2-1-4-1-7h1v2z" class="C"></path><path d="M127 451v-2l1 2c0 1 1 1 2 2l1 2h0c-1 1-1 3-2 4l-2-8z" class="J"></path><defs><linearGradient id="u" x1="118.535" y1="427.716" x2="125.796" y2="448.98" xlink:href="#B"><stop offset="0" stop-color="#363635"></stop><stop offset="1" stop-color="#4e4d4f"></stop></linearGradient></defs><path fill="url(#u)" d="M122 426h1c1 3 1 5 1 8-1 1-1 2-1 3-1 5-1 9 0 14h-1l-1-1v-1l-1 2c0-4 1-7 1-11 0-1-1-2-1-4v-4l2-6z"></path><path d="M122 424v1 1l-2 6v4c0 2 1 3 1 4 0 4-1 7-1 11l-1 4c0 1 1 3 1 4v2c0-1-1-2-2-2h-1v1h-1c0-2 0-4-1-6v-2c-1-2-1-3-2-4l-1-1c0 1-1 2-1 2v-2l-1-2c1-1 2-3 3-5l3-6h0c2-3 5-6 6-10z" class="G"></path><path d="M111 447v-1h1l1 1v-1c1 0 1-1 2-2 0 1 0 3 1 4s2 7 2 9l-1 1v1 1h-1c0-2 0-4-1-6v-2c-1-2-1-3-2-4l-1-1c0 1-1 2-1 2v-2z" class="B"></path><path d="M122 424v1 1l-2 6h0c-1 2-1 4-2 6s-3 4-5 6h0v-2-2l3-6h0c2-3 5-6 6-10z" class="O"></path><path d="M120 451l1-2v1l1 1h1c2 3 2 6 2 9 0 1 1 2 1 3v-1c1-1 1-2 2-3 1 3 2 5 1 8 0 1 0 2-1 3 0 1-1 2-1 2l-3 8-2 7c0 3 0 6-1 9v2l-1 4c0 1 0 3-1 4l-1-3c0-5 1-10 2-16v-3h-1c-1 1-1 3-1 5 0-3 0-5-1-7v-1-1l-1-1c0-3 0-7 1-10v-5c1-1 0-3 0-4v-1h1c1 0 2 1 2 2v-2c0-1-1-3-1-4l1-4z" class="P"></path><path d="M117 482v-1-1l-1-1c0-3 0-7 1-10v-5c2 3 3 7 4 10v10c0 1 0 2-1 3v-3h-1c-1 1-1 3-1 5 0-3 0-5-1-7z" class="C"></path><path d="M121 474v10l-2-2v-3c0-1 1-1 1-2l1-3z" class="I"></path><path d="M120 451l1-2v1l1 1h1c2 3 2 6 2 9 0 1 1 2 1 3l-2 10-4-11v-1-2c0-1-1-3-1-4l1-4z" class="M"></path><path d="M121 450l1 1c1 3 1 6 1 10-1-2-1-4-1-5 0-2-1-3-1-5v-1z" class="C"></path><defs><linearGradient id="v" x1="119.014" y1="454.127" x2="104.488" y2="468.058" xlink:href="#B"><stop offset="0" stop-color="#414043"></stop><stop offset="1" stop-color="#5d5d5c"></stop></linearGradient></defs><path fill="url(#v)" d="M110 445l1 2v2s1-1 1-2l1 1c1 1 1 2 2 4v2c1 2 1 4 1 6h1c0 1 1 3 0 4v5c-1 3-1 7-1 10l1 1v1 1h-1c1 1 1 2 0 3 0-2 0-3-1-4v1c0 1 0 2 1 3v1h0c-1 0-2-2-2-3h0-1c-1-1-1-1-3-1v-2h-1c0 2 1 5 0 7v-1 2l-2-3h0c-1-1-1-3-1-4-1-2-1-5-2-7 0-1 0-3-1-4v-1h0 0c1-1 1-2 2-3l1-2c0-3 1-5 0-7l3-9c0-1 1-2 1-3z"></path><path d="M113 463c0-1 0-1 1-2 1-2-1-5 0-7h1c1 2 1 4 1 6v4c-1 0-2-1-3-1z" class="C"></path><path d="M113 463c1 0 2 1 3 1-1 3-2 7-2 10 0 2 1 5 1 7v1c0 1 0 2 1 3v1h0c-1 0-2-2-2-3 1-1-1-3-1-5-1-5-1-10 0-15z" class="I"></path><path d="M116 460h1c0 1 1 3 0 4v5c-1 3-1 7-1 10l1 1v1 1h-1c1 1 1 2 0 3 0-2 0-3-1-4 0-2-1-5-1-7 0-3 1-7 2-10v-4z" class="H"></path><path d="M110 445l1 2v2c-2 6-3 10-3 16 0 2 0 4 1 6l1 2s0 1-1 1c0-2-1-1-2-2v-2c0-2-1-4-1-6 0-3 1-5 0-7l3-9c0-1 1-2 1-3z" class="J"></path><path d="M106 464c0 2 1 4 1 6v2c1 1 2 0 2 2 1 0 1-1 1-1 1 3 2 7 4 10h-1c-1-1-1-1-3-1v-2h-1c0 2 1 5 0 7v-1 2l-2-3h0c-1-1-1-3-1-4-1-2-1-5-2-7 0-1 0-3-1-4v-1h0 0c1-1 1-2 2-3l1-2z" class="L"></path><path d="M103 469h0c1-1 1-2 2-3v3l1 1v3s1 1 1 2l2 11v2l-2-3h0c-1-1-1-3-1-4-1-2-1-5-2-7 0-1 0-3-1-4v-1h0z" class="F"></path><path d="M159 461c11-3 21-4 32-4v1c-1 0-2 0-3 1l-5 1h2v1h0l-3 1c-2 1-8 2-9 4l-6 3v-1h-1-1l-6 3c-1 0-1-1-2-2h-1-2 0c-5 1-11 6-14 11-2 3-4 9-6 13-1 3-1 5-2 8l1 14c1 9 3 17 8 24 2 2 4 4 6 7l-1 1 2 1v1h-1s-1-1-2-1h-1-1c-3-2-5-4-8-6-3-5-6-8-8-12l1-1c-1-1-1-3-2-4h-1c0-2-1-4-2-6 0-2-1-5-1-8v-4c-1-1-1-3-1-5h-1l1-4v-2c1-3 1-6 1-9l2-7c1-3 2-5 3-8 1 1 0 1 1 1 0 1 0 0 1 1l1-2h0v-3h1 0c2-1 3-5 5-6-1 1-1 2 0 3 1 0 2-1 3-1h2c1 1 2 1 2 1h0l-2 2c2-1 4-3 7-4 1-1 3-1 5-2s4-2 6-2v1z" class="P"></path><path d="M154 469l7-3-4 3h-1-2 0z" class="I"></path><path d="M128 482c1 0 1-1 2-1l-2 6c-1-2-1-3 0-5z" class="C"></path><path d="M129 478c0-1 1-2 1-4l2 2s-1 1-1 2v1c0 1 0 2-1 2s-1 1-2 1c1-1 1-3 1-4z" class="I"></path><path d="M133 515c0-1 0-2-1-2 0-1-1-1-1-1l-1-1c1-3 1-7 2-10l1 14z" class="G"></path><path d="M183 460h2v1h0l-3 1c-2 1-8 2-9 4l-6 3v-1h-1-1c6-3 12-6 18-8z" class="Z"></path><path d="M141 468c2-1 4-3 7-4 1-1 3-1 5-2s4-2 6-2v1c-10 4-20 9-28 18v-1c0-1 1-2 1-2l2-2 4-4 3-2z" class="V"></path><path d="M139 475h0l1 1c-1 2-3 4-4 6s-2 4-3 7v2c-1 1 0 3-1 3v1 2 1l-1 1c0 1 0 2-1 2v-5-1l-1 2c0 1 0 3-1 5h0c0-3 0-5 1-7 1-3 1-7 2-10 2-3 5-7 8-10z" class="N"></path><path d="M136 463c-1 1-1 2 0 3 1 0 2-1 3-1h2c1 1 2 1 2 1h0l-2 2-3 2-4 4-2 2-2-2h0v-2h0 0v-3h1 0c2-1 3-5 5-6z" class="E"></path><path d="M134 474v-3c2-1 2-1 4-1l-4 4z" class="B"></path><path d="M130 472h0v-3h1l1 1h0c-1 1-1 2-1 3l-1 1v-2h0z" class="M"></path><defs><linearGradient id="w" x1="140.39" y1="536.977" x2="133.356" y2="540.851" xlink:href="#B"><stop offset="0" stop-color="#403f41"></stop><stop offset="1" stop-color="#666"></stop></linearGradient></defs><path fill="url(#w)" d="M125 522l1-1c4 12 11 19 20 26l2 1v1h-1s-1-1-2-1h-1-1c-3-2-5-4-8-6-3-5-6-8-8-12l1-1c-1-1-1-3-2-4l-1-3z"></path><path d="M130 472h0v2h0c0 2-1 3-1 4s0 3-1 4c-1 2-1 3 0 5-4 12-3 24 0 35l4 11c-1-2-2-5-3-7s-1-3-2-5h-1l-1 1 1 3h-1c0-2-1-4-2-6 0-2-1-5-1-8v-4c-1-1-1-3-1-5h-1l1-4v-2c1-3 1-6 1-9l2-7c1-3 2-5 3-8 1 1 0 1 1 1 0 1 0 0 1 1l1-2z" class="K"></path><path d="M122 504h1c0 6 1 12 2 18l1 3h-1c0-2-1-4-2-6 0-2-1-5-1-8v-4-3z" class="H"></path><path d="M122 487c0 1 0 4 1 5 1 2 0 9 0 12h-1v3c-1-1-1-3-1-5h-1l1-4v-2c1-3 1-6 1-9z" class="G"></path><path d="M121 498h0c1 2 1 4 1 6v3c-1-1-1-3-1-5h-1l1-4z" class="E"></path><path d="M130 472h0v2h0c0 2-1 3-1 4h-1c-1 2-2 4-3 5h0l-1 2h0v-5h0l3-8c1 1 0 1 1 1 0 1 0 0 1 1l1-2z" class="V"></path><path d="M125 483h0c0-2 1-3 1-5h1 0 1c-1 2-2 4-3 5h0z" class="R"></path><path d="M556 181v2c-3 5-6 6-11 8l-3-3h0c0 1 0 2 1 3 2 1 3 4 4 7 4 9 4 19 0 28 0 2-3 4-3 6h3v1c1 1 0 2 0 3-1 1-1 1-2 1 0 0-1-1-2-1h-1v1h1c-2 2-4 3-6 5-6 4-12 7-18 9-6 1-11 2-17 1 10-4 20-5 29-11 8-6 13-14 15-24 1-6 1-13-3-19-1-2-2-4-3-5-3 2-7 3-10 4-5-2-12-2-15-6v-1l1-1h0v1h1c1 2 5 3 8 4-3-3-3-4-4-7 1-4-4-8-4-12-1-3 0-6 1-9v-3s0-2-1-2c0-1 0-2-1-4v-1c0-1 2-1 2-2s-1-1-1-1c-1-1-1-1-1-2 1-2 2-1 3-2 0-1-2-1-3-2-6-2-8-4-12-10 3 2 5 4 7 5 3 2 7 2 10 4 1 1 3 2 4 3 0 1 1 2 2 3 0-2-1-3-2-4-1-2-2-4-4-5-4-3-10-4-14-7l-4-4c5 2 11 3 16 6 1 0 3 2 4 2-5-6-11-10-16-17 5 2 11 6 14 10 2 3 4 7 6 10v-1l-6-20c3 2 6 6 7 9 0 3 1 9-1 11v1l2 4c0-5 0-10 2-15h0c1 2 1 5 1 7l-1 12c1-3 1-6 2-8 1-4 4-8 7-11-1 3-3 6-4 8s-2 4-2 6c-1 2 0 5 0 7l2-6c1-2 3-4 5-4 1-1 2-1 3 0 3 1 8 6 9 9 0 1 0 2-1 3h-1l-3-3c-1 1-1 4-2 4-2 1-3 0-4 0-2-2-1-3-2-4v-1l-1 1c-2 3-2 8-2 11l5-1c3-1 4-3 5-5 0-1 0-1 1-1 1 1 1 1 1 2-1 2-2 3-3 4-2 2-5 3-8 2 0 0-1 0-1 1v2h1 2c2 0 4 1 6 1 4 2 7 5 8 8 1 2 1 3 2 4v1z" class="U"></path><g class="P"><path d="M534 190l1-1c1 1 2 1 2 2l-1 2h-2-1l1-3zm-11-5h1c2 0 7 1 8 3v1c-1 2-2 3-3 3s-2 0-3-1-2-2-2-3v1c1 1 2 1 3 2l1-1v-1c-1-1-2-1-3-1h-1l-1-3zm10-6c2 0 4 1 5 2s1 3 1 5l-1 1-1-1c-2-1-4-2-6-4v-1c0-1 1-1 2-2zm-12-22h3c1 1 1 2 2 4 1 1 1 0 2 1l2-2 1-1c2 3 3 6 5 8l-4 2c-4 2-9 4-10 10h0-1l-1-1v-1c-2-4-2-9 0-13 1 1 2 2 3 2 2 1 6 1 9 0 1 0 1 0 1-1v-1c-2 0-4 1-5 1-3 0-5-1-6-2s-2-3-2-4 1-1 1-2z"></path><path d="M540 170c2 0 4 0 6 1 3 2 5 4 6 7 0 1 0 3-1 5s-3 3-5 4c-1 0-3-1-3-1-1-2-1-4-1-6 1 0 2-1 3-1 1 1 1 2 2 3s1 1 2 1c0-1 1-1 1-2s0-2-1-3-3-2-4-2h-1 0c-1 0-2 1-4 2h0-1c-1-1-2-2-4-2h0-3c-2 1-3 2-3 3s0 1-1 2-3 0-5 0c1-1 1-3 2-4 3-4 6-6 11-7 1 1 1 6 2 7 1-2 1-4 1-6l1-1z"></path></g><defs><linearGradient id="x" x1="273.692" y1="463.584" x2="260.83" y2="471.644" xlink:href="#B"><stop offset="0" stop-color="#3a393a"></stop><stop offset="1" stop-color="#565757"></stop></linearGradient></defs><path fill="url(#x)" d="M246 439l1-1c2 1 5 3 7 5 1 0 2 1 3 3 0 0 2 2 3 2s2 1 3 2c1 0 1 1 2 2h1c0 2 4 5 5 7 9 12 17 27 20 42 3 10 2 21 1 32 0 3 0 7-1 10h1c0 1 0 1-1 2v2l-2 5h1l-4 10h-1l-1-1h0l-1 2h-1-1l2-5h-1 0-1-1c-1 2-2 5-3 7 0 1-2 2-3 3 1-5 4-8 4-14 0-2 0-2-1-3v-2c1-1 2-7 2-9-1-1-1-5-1-7-1-1-1-2-2-3v-2c0-2-1-5-1-7-1-4-1-9-3-14s-4-9-6-14v-1c0-1 0-2-1-3l-2-3-1-2-2-3h0v-1c2 1 3 4 4 5h0 1c-2-4-5-9-7-12-1-1-2-1-2-3h0c-1-2-2-3-4-4a30.44 30.44 0 0 0-8-8l-9-6h0c-1-1-1 0-1-1l-2-1c-1 0-1 0-2-1h2c4 2 9 5 13 8l5 4v-1h0l-1-2c-2-1-2-1-3-3 2 0 4 2 6 2l-13-11 5 2v-1h1c-1 0-3-2-4-3 1 0 2 1 3 1v-2h0l2-2h-1c0-1 0-1 1-2z"></path><path d="M244 443c1 1 2 1 2 2 1 0 2 1 3 2h-2l-1-1s-1 0-2-1v-2z" class="T"></path><path d="M246 439l1-1c2 1 5 3 7 5 1 0 2 1 3 3-1-1-2-2-4-3 2 3 4 5 7 7 1 1 4 3 4 4v1h-1 0c-2-1-3-2-4-3v-1c-2-3-7-6-9-9l-1 1c-1 0-1 0-2-1h-1l-1 1 1 1s1 0 1 1h0-1c0-1-1-1-2-2h0l2-2h-1c0-1 0-1 1-2z" class="I"></path><path d="M247 442v-1c1 0 2 1 3 1l-1 1c-1 0-1 0-2-1z" class="M"></path><path d="M263 455h1v-1c0-1-3-3-4-4-3-2-5-4-7-7 2 1 3 2 4 3 0 0 2 2 3 2s2 1 3 2c1 0 1 1 2 2h1c0 2 4 5 5 7v1 1s-1 0-1-1h0-1v1l4 5-1 1-4-5c-1-3-4-5-5-7z" class="E"></path><path d="M260 448c1 0 2 1 3 2 1 0 1 1 2 2h1c0 2 4 5 5 7v1l-4-4c-3-3-5-5-7-8zm26 61c0-2-1-5-2-7v-1-3c-1-1-1-2-1-3v-1c0 1 1 1 1 1 0 2 1 4 1 6 1 1 1 2 1 3v2h0c1 2 1 5 2 6v-2c2 9 1 18 0 27h0 0-1c0-2-1-5-1-7-1-4 0-8 0-12 0-2-1-4-1-7 1 0 1-1 1-2z" class="I"></path><path d="M286 509c1 6 1 12 1 18 0 1 1 2 1 3v7h0-1c0-2-1-5-1-7-1-4 0-8 0-12 0-2-1-4-1-7 1 0 1-1 1-2z" class="M"></path><path d="M288 510l-3-16c1 1 1 2 2 3v-1h0c1 1 1 3 2 5 1-1 1 0 2 0 3 10 2 21 1 32 0 3 0 7-1 10h1c0 1 0 1-1 2v2l-2 5h1l-4 10h-1l-1-1h0l-1 2h-1-1l2-5h0c3-6 5-14 5-21 1-9 2-18 0-27z" class="H"></path><path d="M287 497v-1h0c1 1 1 3 2 5l2 16c1 2 1 3 1 4 1 1 0 6 0 8l-2 1c-1-4 0-9 0-13 0-6-2-14-3-20z" class="T"></path><path d="M289 501c1-1 1 0 2 0 3 10 2 21 1 32 0 3 0 7-1 10h1c0 1 0 1-1 2v2l-2 5h1l-4 10h-1l-1-1h0s1-2 1-3c1-1 1-3 2-5 2-7 3-15 3-23l2-1c0-2 1-7 0-8 0-1 0-2-1-4l-2-16z" class="C"></path><defs><linearGradient id="y" x1="275.488" y1="545.049" x2="285.618" y2="547.594" xlink:href="#B"><stop offset="0" stop-color="#19191a"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#y)" d="M282 498h0c-1-3-3-6-3-9 3 7 5 15 6 22 0 3 1 5 1 7 0 4-1 8 0 12 0 2 1 5 1 7h1 0 0c0 7-2 15-5 21h0-1 0-1-1c-1 2-2 5-3 7 0 1-2 2-3 3 1-5 4-8 4-14 0-2 0-2-1-3v-2c1-1 2-7 2-9v-6c1-4 1-7 1-11v2h1c0 2 0 4 1 5 1-1 0-4 1-6h0c0-2 0-5-1-7v-8l-2-10h1 0l1-1z"></path><path d="M282 541h0v1c-1-2-1-5-1-7v1c0-1 1-2 1-4h0l1 1-1 8z" class="D"></path><path d="M280 499h1 0l1-1 3 18v15c0 2 1 7 0 9h0c-1 0-1 0-1-1v-5h-1c0 2 0 6-1 7h0l1-8h0v-9c0-2 0-5-1-7v-8l-2-10z" class="K"></path><path d="M250 460l-1-2c-2-1-2-1-3-3 2 0 4 2 6 2l-13-11 5 2c5 2 9 7 13 11 9 10 17 21 22 34l1 6 2 10v8c1 2 1 5 1 7h0c-1 2 0 5-1 6-1-1-1-3-1-5h-1v-2c0 4 0 7-1 11v6c-1-1-1-5-1-7-1-1-1-2-2-3v-2c0-2-1-5-1-7-1-4-1-9-3-14s-4-9-6-14v-1c0-1 0-2-1-3l-2-3-1-2-2-3h0v-1c2 1 3 4 4 5h0 1c-2-4-5-9-7-12-1-1-2-1-2-3h0c-1-2-2-3-4-4a30.44 30.44 0 0 0-8-8l-9-6h0c-1-1-1 0-1-1l-2-1c-1 0-1 0-2-1h2c4 2 9 5 13 8l5 4v-1h0z" class="P"></path><path d="M282 509v8c1 2 1 5 1 7h0c-1 2 0 5-1 6-1-1-1-3-1-5v-5c0-1 1-2 1-3v-8z" class="E"></path><path d="M244 458l-9-6h0c-1-1-1 0-1-1l-2-1c-1 0-1 0-2-1h2c4 2 9 5 13 8l5 4v-1h0c4 3 7 6 10 10 3 3 6 6 9 10 4 7 7 15 9 22-2-2-3-4-4-6l-1 1-2-5-2-1-1-1c-1-2-2-4-3-5-2-4-5-9-7-12-1-1-2-1-2-3h0c-1-2-2-3-4-4a30.44 30.44 0 0 0-8-8z" class="F"></path><path d="M271 492v-2c-1-1-1-1-1-2 1 1 5 6 4 8l-1 1-2-5z" class="D"></path><path d="M266 483c1 0 1 1 2 1h0l2 3v1c0 1 0 1 1 2v2l-2-1-1-1c1-2-1-5-2-7z" class="B"></path><path d="M256 470c2 2 5 5 7 8 1 1 2 3 3 5s3 5 2 7c-1-2-2-4-3-5-2-4-5-9-7-12-1-1-2-1-2-3z" class="D"></path><defs><linearGradient id="z" x1="267.228" y1="511.094" x2="280.323" y2="512.709" xlink:href="#B"><stop offset="0" stop-color="#4e4d4f"></stop><stop offset="1" stop-color="#656464"></stop></linearGradient></defs><path fill="url(#z)" d="M266 493v-1c0-1 0-2-1-3l-2-3-1-2-2-3h0v-1c2 1 3 4 4 5h0 1c1 1 2 3 3 5l1 1 2 1 2 5c2 6 3 12 5 17 0 2 0 5 1 6l1 3c0 4 0 7-1 11v6c-1-1-1-5-1-7-1-1-1-2-2-3v-2c0-2-1-5-1-7-1-4-1-9-3-14s-4-9-6-14z"></path><path d="M279 520l1 3c0 4 0 7-1 11 0-1 0-3-1-4 0-4 0-7 1-10z" class="M"></path><path d="M230 483s-1 0-2-1c-1 0-3-1-4-1-2-1-3-1-4-1l1-1 7 2v-1c1 1 2 1 3 1h1c3 1 13 6 15 8 4 4 10 9 13 15 1 1 2 4 3 5-1 1-1 1-1 2s1 1 1 2v1c1 1 1 3 1 4 1 1 1 1 1 2 1 5 2 10 2 15v8s-1 0-1 1c0 2 0 3-1 5 0 2-2 7-4 10l-8 12c-1 2-3 4-4 6h-1v-1c-2 3-5 4-7 6-5 5-12 9-18 12-7 3-14 5-21 6-5 0-11 1-15 1h0c-1-2-6 0-8-2h-4 1v-1h10c1 0 3 1 4 0h-4-10c-1 0-3 0-4-1h1 13c13 0 26-2 37-8 3 0 7-3 9-4 3-2 7-4 9-6s4-5 6-7c2-3 4-6 5-9 3-5 4-12 5-18 0-9-1-18-6-25-2-3-4-5-6-8l-1-1c-1-2-4-3-6-4l2-1-2-2v1c-2-1-3-1-5-2l-10-4h-3l-1-2h-1l-2-1h-6c-2-1-4 0-5 0-2 0-4 1-7 1-1 0-2 1-4 1h0v-1c1 0 2 0 2-1 1 0 2 0 2-1h0-2-1c0-1 1-1 1-2h0c-1 0-2 0-3 1l-2 1c-2 0-4 1-6 3h0 0c0 1 0 2-1 2-3 2-6 6-9 9l-2 2c0-1 0-1-1-2l2-4h0c0-2 1-3 1-4v-1h-1v-1c0-1 0-1 1-2s2-3 3-5h0 0c1-1 3-1 3-3 1-1 3-2 4-3h2c2 0 3-1 4-2 3-1 7-2 11-2v-1h8l4 1h1c1 0 2 1 4 1v-1h-2c0-1-1-1-2-2 1 0 7 1 8 1l1 1h2 0c1 0 1 1 1 1h3 0z" class="T"></path><path d="M235 498c1 1 2 1 3 2l-1 1h-1c0-1-1-1-2-2l1-1z" class="K"></path><path d="M260 546v3h1l-1 5c-1 0-1 1-2 1 1-3 2-6 2-9z" class="J"></path><path d="M258 555c1 0 1-1 2-1v1c-1 4-4 8-6 11 0-1 3-11 4-11z" class="E"></path><path d="M262 534v3c0 4 0 8-1 12h-1v-3-5h0 1v-7h1z" class="O"></path><path d="M262 534v3 6l-1 1v-3-7h1z" class="Q"></path><path d="M248 576c2-6 8-11 12-16 0-1 0-1 1-1l-8 12c-1 2-3 4-4 6h-1v-1z" class="B"></path><path d="M254 511c2 3 3 5 4 8l2 5c0 1 0 2 1 2v1c1 1 1 6 1 7h-1v7h-1c0-6 0-11-1-17-1-4-3-8-5-12v-1z" class="D"></path><path d="M199 493h0c1 0 2 0 3-1 6-1 13 2 19 3 1 0 6 2 7 3v1c-4-2-7-3-11-4s-8-2-12-2c-2 0-4 2-6 2 0 0-1-1-2-1v-1h2z" class="W"></path><path d="M247 489c4 4 10 9 13 15l-1 1c-1-1-2-4-4-5-2-3-5-5-7-7-1 0-2-1-2-2h0 1 0l-1-1 1-1z" class="V"></path><path d="M254 515c-1-2-2-3-3-5l1-1 2 3c2 4 4 8 5 12 1 6 1 11 1 17h0c-2-1 0-11-2-14 0-2-1-4-1-6l-3-6z" class="R"></path><path d="M216 496v-1h1c4 1 7 2 11 4 3 1 5 4 8 4 0 0 1 1 2 1h0v1c-2-1-3-1-5-2l-10-4h-3l-1-2h-1l-2-1z" class="E"></path><path d="M219 497c2 0 3 0 4 1v1h-3l-1-2z" class="I"></path><path d="M243 497l3 3c4 3 6 7 8 11v1l-2-3c-1-1-3-3-5-4-1 0-1-1-2-1l-4-5h1 1v-2z" class="B"></path><path d="M216 490c1-1 1-2 3-1h1c1 1 4 2 6 2 2 1 5 3 8 4l2 2v1l2 1v1c-1-1-2-1-3-2-4-2-8-3-12-5h0c2 0 4 2 6 2v-1c-4-2-8-3-13-4z" class="E"></path><path d="M199 492v1h-2v1c1 0 2 1 2 1 2 0 4-2 6-2 4 0 8 1 12 2h-1v1h-6c-2-1-4 0-5 0-2 0-4 1-7 1-1 0-2 1-4 1h0v-1c1 0 2 0 2-1 1 0 2 0 2-1h0-2-1c0-1 1-1 1-2h0c1-1 2-1 3-1z" class="M"></path><path d="M205 493c4 0 8 1 12 2h-1v1h-6c-2-1-4 0-5 0-1-1-2 0-3 0 2-1 4 0 6-2h-2 0l-1-1zm-15 105c9 0 19-2 28-5 5-2 9-4 14-6-2 2-6 3-8 5h-2c-2 1-4 3-7 4s-6 1-9 2h-3-1l-18 2c-2 0-3 0-5-1h0-4 1v-1h10c1 0 3 1 4 0z" class="B"></path><path d="M264 533v-4-1-4h0v-1c-1-1-1-2-1-3-1-1-1-2-1-3h1v1c0 1 0 1 1 2h0 1c1 5 2 10 2 15v8s-1 0-1 1c0 2 0 3-1 5v-4l-1-1c0 2 0 3-1 4v-1-2c0-1 1-1 1-1 0-1 0-5-1-6v-5h1 0z" class="K"></path><path d="M264 533v-4-1-4h0v-1c-1-1-1-2-1-3-1-1-1-2-1-3h1v1c0 1 0 1 1 2h0v3c1 1 1 1 1 2v15c-1 0 0-1-1-1v-6z" class="R"></path><path d="M236 498c1-1 2-1 2-1 1 1 2 1 2 2h1l4 5c1 0 1 1 2 1 2 1 4 3 5 4l-1 1c1 2 2 3 3 5l3 6c0 2 1 4 1 6-1-1-2-4-2-5 0 0 0-1-1-1l-1-3c-1-2-2-2-3-4 0-1-1-2-2-3 0 0 0-1-1-1v-1c-1-1-3-4-5-5l-1 1-1-1c-1-2-2-3-4-3l1-1v-1l-2-1z" class="F"></path><path d="M247 505c2 1 4 3 5 4l-1 1c1 2 2 3 3 5h0c-1-1-2-2-2-3-2-2-3-4-5-7h0z" class="C"></path><path d="M232 585l1 1-1 1h0c-5 2-9 4-14 6-9 3-19 5-28 5h-4-10c-1 0-3 0-4-1h1 13c13 0 26-2 37-8 3 0 7-3 9-4z" class="W"></path><path d="M216 485c2 0 4 0 6 1 1 0 1 1 2 1 3 2 7 3 10 5 1 0 2 0 2 1 2 1 4 3 6 4v-1h1v1 2h-1-1-1c0-1-1-1-2-2 0 0-1 0-2 1v-1l-2-2c-3-1-6-3-8-4-2 0-5-1-6-2h-1c-2-1-2 0-3 1h0c-2-1-4-1-5-1l-9-1c2-1 3 0 5-1 1-1 3-1 4-1h-1l1-1h5z" class="C"></path><path d="M234 495h2l1 1c0 1-1 1-1 1l-2-2z" class="B"></path><path d="M216 485c2 0 4 0 6 1 1 0 1 1 2 1 3 2 7 3 10 5v1c-1 0-2-1-3-1l-9-4c-1 0-2-1-2-2h-2v1h2v1h-3c-2-1-4-2-6-2h-1l1-1h5z" class="K"></path><path d="M217 482c0-1-1-1-2-2 1 0 7 1 8 1l1 1h2 0c1 0 1 1 1 1h3 0c1 0 2 1 3 1l4 2 1 1h-1-1v1c1 0 2 1 3 2h2c0 1 1 1 1 1 1 1 1 1 2 1v1c1 0 3 2 4 3h0c-1 1-1 1-2 1h0l2 2c0 1-1 1-2 1l-3-3v-1h-1v1c-2-1-4-3-6-4 0-1-1-1-2-1-3-2-7-3-10-5-1 0-1-1-2-1-2-1-4-1-6-1-3-1-6 0-9-1-2-1-3-1-5-1h0c0-1 4-1 6-1h-6v-1h8l4 1h1c1 0 2 1 4 1v-1h-2z" class="M"></path><path d="M208 482l10 2c3 0 5 0 7 1l3 1c2 1 3 2 5 2 2 2 5 3 7 4l3 3c1 0 2 1 2 1l1 1h-1c-1 0-1 0-2-1h-1v1c-2-1-4-3-6-4 0-1-1-1-2-1-3-2-7-3-10-5-1 0-1-1-2-1-2-1-4-1-6-1-3-1-6 0-9-1-2-1-3-1-5-1h0c0-1 4-1 6-1z" class="B"></path><path d="M202 482h6c-2 0-6 0-6 1h0c2 0 3 0 5 1 3 1 6 0 9 1h-5l-1 1h1c-1 0-3 0-4 1-2 1-3 0-5 1-1-1-2-1-3-1v1h2v1c-1 0-3 0-4 1v1h-1c-2 0-2 1-3 2 0 0 1 0 1-1h1c1 0 2-1 3-1h2c0 1-1 1-1 1-1 0-2 0-3 1-1 0-2 0-3 1l-2 1c-2 0-4 1-6 3h0 0c0 1 0 2-1 2-3 2-6 6-9 9l-2 2c0-1 0-1-1-2l2-4h0c0-2 1-3 1-4v-1h-1v-1c0-1 0-1 1-2s2-3 3-5h0 0c1-1 3-1 3-3 1-1 3-2 4-3h2c2 0 3-1 4-2 3-1 7-2 11-2z" class="F"></path><path d="M197 490v1h-1c-2 0-2 1-3 2 0 0 1 0 1-1h1c1 0 2-1 3-1h2c0 1-1 1-1 1-1 0-2 0-3 1-1 0-2 0-3 1l-2 1c-2 0-4 1-6 3h0l-2 1c0-2 4-3 5-5 3-3 6-3 9-4z" class="R"></path><path d="M179 500c1-2 3-3 5-4-1 2-3 4-5 6 1-1 3-2 4-3l2-1h0c0 1 0 2-1 2-3 2-6 6-9 9l-2 2c0-1 0-1-1-2l2-4h0c0-2 1-3 1-4h2c1 0 2-1 2-1z" class="E"></path><path d="M174 505c0-2 1-3 1-4h2v1c0 2-1 3-1 5-1 0-1 1-1 2l-2 2c0-1 0-1-1-2l2-4h0z" class="D"></path><path d="M174 505c0-2 1-3 1-4h2v1l-3 3z" class="M"></path><path d="M202 482h6c-2 0-6 0-6 1h0c-2 1-3 2-4 2-2 0-3 1-5 1h-2c-2 1-4 3-6 4s-5 2-7 4v2h0 1v-1 2h0l1-1v1l-1 1c-1 0-1-1-2-1h-1l-1 1 1 1h2l1 1s-1 1-2 1h-2v-1h-1v-1c0-1 0-1 1-2s2-3 3-5h0 0c1-1 3-1 3-3 1-1 3-2 4-3h2c2 0 3-1 4-2 3-1 7-2 11-2z" class="C"></path><path d="M223 138v1c1 5-3 10-5 14-1 1-2 3-2 5-1 1-3 5-3 6 2 0 3 0 4-1l1 1-1 1 3 3c1 0 1 1 1 1l1 1c1 1 2 3 3 5h0v1l1 2h1c2-1 3-2 4-3v-1c0 1-1 2-1 3h0c-1 0 0-1-1 1h-1v1c0 1-1 1-1 2h0v1 2h0c1 4-1 7 1 11h0v9c-2 9-6 18-13 24-4 5-9 8-12 13h0c2 0 4-3 5-5 1-1 2-2 3-2-1 2-2 5-3 7-1 1 0 1 0 1h2v1c-1 2-2 3-3 4 0 1 0 3-1 3 0 1-5 2-6 2-2 1-4 2-5 3s-2 2-2 3c0 2 1 3 2 5 1 1 2 1 3 1s1 0 1 1l-2 2c-1 0-1 1-2 0-2 0-3-2-5-4l2 11-3 3c-1-4-1-9-1-14 0-4 2-9 5-12l6-6h1 1 2v-1c-1 0-3-1-4-1-1 1-3 3-5 4-3 1-6 2-9 1s-5-3-6-5c-1-1-1-3 0-5s3-5 6-6h1c-1-4-2-8-1-12 0-1 0-1 1-2 2 0 3 0 5 1 1 1 2 1 2 3h0s1 1 1 0c1 0 1-1 2-1 2 0 2 0 3 1v3c0 1 3 2 4 4 0 1 0 1-1 3 0 0-1 1-2 1 0 0-2-1-3-1l1 4c-1 1-2 1-3 2h0c1 1 1 1 3 2 2-1 4-4 6-5 3-3 7-7 9-10h0s-1 1-2 1l-1-1c-9-11-11-25-9-39l2-13v-7c0-2-1-4-1-6h1 1c1 1 1 4 1 6l5-11c2-3 4-6 5-9-2 1-6 2-8 4-1 1-1 4-2 5-3 5-8 6-13 7 1-2 3-4 5-5s3-1 5-2c0-1 1-2 1-3-1-2-2-1-4-2 1-2 7-3 9-4s3-2 4-3c-1-1-1-2-2-3l1-1c2 1 4 2 5 4h0c2-1 4-5 7-5z" class="U"></path><path d="M194 232h0 0c-1 1-1 3-3 3-1 0-1 0-1-1 1-1 2-2 4-2zm15-40c1 0 3 0 4 1s2 2 3 4c0 1 0 1-1 1-2 0-4-2-5-3s-1-2-1-3zm-27 45h1c5 0 2 3 4 5 1 0 2 0 3-1 0 0 0-1 1-1 1 1 2 1 3 2l-1 1c-2 1-5 2-7 2s-4-1-5-3c0 0-1-2 0-3 0-1 0-1 1-2zm31-69c4 2 7 6 9 10 2 5 4 14 2 19l-2 2-2-2c1-4 2-6 4-10 0-1 0-3-1-4-2-1-4-2-6-2-1 1 0 3-1 4-1-2-3-5-6-6 0-1 0-1-1 0 0 1-1 1-1 2v5s1 1 1 2c-1 0-1-1-2-1-2 4-2 9-1 12l2 2c4 5 9 6 14 7-1 5-4 11-7 15h-1c-6-5-9-14-10-21-1-12 2-25 9-34z" class="P"></path><path d="M191 457c3 0 6 1 8 1h1 5c3 1 6 2 9 2v-1h-1v-1c2 1 3 2 4 2h0 1 0c1 2 2 2 3 3 1 0 2 1 2 2l6 3c2 1 5 3 7 4v-1l1 1h0c1 1 1 1 1 2h1c0 1 0 1 1 1 0 1 1 1 1 2h1v2c0 1-1 1-1 2l-2-1c-1 0-1-1-2-1-2 1-3 0-4-1h-1c-2-1-4-2-7-3-9-3-19-3-29-3-2 0-4 1-6 1-5 1-10 3-15 5l-2 1c0 1 0 2-1 2-2 1-4 3-6 4-1 1-1 2-2 3-1 2-2 4-2 6-1 2-1 5-1 8-2 3-2 6-3 9 0 2 0 6-2 7v-1c-1-2-1-2-3-2 1 9 3 20 10 26l4 4 2 2c-1 2-2 2-3 3h-3-1v1s1 1 2 1c0 0 1 1 1 2 3 0 6 1 8 2-6 0-12-2-17-4-3 0-6-2-9-1l-2-2v-1c1 0 2 1 2 1h1v-1l-2-1 1-1c-2-3-4-5-6-7-5-7-7-15-8-24l-1-14c1-3 1-5 2-8 2-4 4-10 6-13 3-5 9-10 14-11h0 2 1c1 1 1 2 2 2l6-3h1 1v1l6-3c1-2 7-3 9-4l3-1h0v-1h-2l5-1c1-1 2-1 3-1v-1z" class="F"></path><path d="M171 477l3-3h1-1 1l-1 3s0 1 1 1l-2 1h-1v-2h-1z" class="E"></path><path d="M187 472c2 0 9-2 11-1 0 1-1 1-2 1-2 0-4 1-6 1l-1-1h-2z" class="V"></path><path d="M210 471c-4 0-8 0-11-1 3-1 8-1 11 0v1z" class="L"></path><path d="M183 463c3-1 5-1 8-1l-1 1h-2c-3 2-7 2-10 2 1-1 3-2 5-2z" class="R"></path><path d="M154 505c0-3 2-6 2-9 1 0 1 0 1-1l1 1h1c0 2-1 3-1 4-1 2-2 4-2 5l-1-1-1 1z" class="I"></path><path d="M171 477h1v2h1c0 1 0 2-1 2-2 1-4 3-6 4-1 1-1 2-2 3 0-1 1-2 1-3 2-3 4-5 6-8z" class="N"></path><path d="M154 505l1-1 1 1-1 3c1 1 1 2 2 3v1c-1 0 0 2-1 3h1v-2-1l1-1c0 2 0 6-2 7v-1c-1-2-1-2-3-2h0c0-4 1-7 1-10z" class="B"></path><path d="M154 505l1-1 1 1-1 3c-1 2-1 3-1 5h2v4c-1-2-1-2-3-2h0c0-4 1-7 1-10z" class="G"></path><defs><linearGradient id="AA" x1="174.349" y1="473.745" x2="187.462" y2="474.938" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#5d5c5d"></stop></linearGradient></defs><path fill="url(#AA)" d="M175 474h1 0c2-1 6-2 8-3 1 0 2 1 3 1h2l1 1c-5 1-10 3-15 5-1 0-1-1-1-1l1-3z"></path><path d="M206 463l-7-1c-1 0-2 0-3-1h4c6 1 12 2 17 4 2 1 4 3 6 4l-11-3h-3c-3 1-5 1-8 1 1-2 4-1 6-1 1-1 2-1 3-1l1-1c-2 0-4 0-5-1z" class="B"></path><path d="M158 481c3-2 6-5 9-5l-4 6h-1l-8 6-1-1c-1 1-2 2-3 4h0c0-1 2-3 2-4l6-6z" class="H"></path><path d="M153 487c2-3 6-5 9-7l1 1-1 1-8 6-1-1z" class="B"></path><path d="M183 460l5-1h3c2 1 3 1 6 1v1h3-4c1 1 2 1 3 1l7 1v1h-3c-4-1-10 0-15-1h2l1-1c-3 0-5 0-8 1l-1-1 3-1h0v-1h-2z" class="K"></path><path d="M210 470c6 0 11 2 16 3l-4-2v-1c4 0 8 4 12 6 2 0 3 1 5 2l1-1c-1-2-3-2-3-4l-1-1v-1l1 1h0c1 1 1 1 1 2h1c0 1 0 1 1 1 0 1 1 1 1 2h1v2c0 1-1 1-1 2l-2-1c-1 0-1-1-2-1-2 1-3 0-4-1h-1v-1c-1-1-3-2-5-2-5-2-11-4-17-4v-1z" class="N"></path><path d="M173 466h0c2 1 4 0 6 0-3 2-6 3-9 5-3 1-5 3-8 5-1 0-2 0-3 1h-1l2-4c-2 0-3 2-5 3l-1-1 3-3 2-1 6-3h1 1v1l6-3z" class="Q"></path><path d="M165 468h1 1v1c-2 1-4 3-7 4-2 0-3 2-5 3l-1-1 3-3 2-1 6-3z" class="R"></path><defs><linearGradient id="AB" x1="148.146" y1="480.698" x2="152.732" y2="487.359" xlink:href="#B"><stop offset="0" stop-color="#515150"></stop><stop offset="1" stop-color="#686769"></stop></linearGradient></defs><path fill="url(#AB)" d="M155 476c2-1 3-3 5-3l-2 4h1c1-1 2-1 3-1-1 2-3 3-4 4v1l-6 6v-1c-4 4-6 9-8 14l-1 3c-1-1 0-2 0-3s0-3 1-4l-1-1c0-1 2-4 2-5 0-3 2-5 4-7l2-2c1 0 1-1 1-1h-1l4-4z"></path><path d="M158 477h1c1-1 2-1 3-1-1 2-3 3-4 4v1l-6 6v-1c-4 4-6 9-8 14l-1 3c-1-1 0-2 0-3s0-3 1-4c1-2 2-4 4-7 2-2 4-5 6-7s2-4 4-5z" class="J"></path><path d="M158 480v1l-6 6v-1c1-1 2-2 3-4 1-1 1-1 3-2z" class="I"></path><defs><linearGradient id="AC" x1="225.471" y1="480.166" x2="219.023" y2="456.138" xlink:href="#B"><stop offset="0" stop-color="#333434"></stop><stop offset="1" stop-color="#5c5a5a"></stop></linearGradient></defs><path fill="url(#AC)" d="M191 457c3 0 6 1 8 1h1 5c3 1 6 2 9 2v-1h-1v-1c2 1 3 2 4 2h0 1 0c1 2 2 2 3 3 1 0 2 1 2 2l6 3c2 1 5 3 7 4l1 1c0 2 2 2 3 4l-1 1c-2-1-3-2-5-2-4-2-8-6-12-6l1-1c-2-1-4-3-6-4-5-2-11-3-17-4h-3v-1c-3 0-4 0-6-1h-3c1-1 2-1 3-1v-1z"></path><path d="M191 457c3 0 6 1 8 1h1 5c3 1 6 2 9 2l-1 1c-1 0-3-1-5-1-1 0-2-1-4-1v1c1 0 2 0 3 1-4 0-7-1-10-1s-4 0-6-1h-3c1-1 2-1 3-1v-1z" class="F"></path><path d="M188 459c1-1 2-1 3-1h1c2 0 5 0 8 1h-1-8-3z" class="R"></path><path d="M162 482h1c-1 2-2 4-1 7 0 2-3 5-3 7h-1l-1-1c0 1 0 1-1 1 0 3-2 6-2 9s-1 6-1 10h0c1 9 3 20 10 26v1c0 2 0 2 1 4h-1c-3-2-5-3-7-6-1 0-1 0-1 1-2-1-3-4-4-5s-1-3-2-4c-1-2-2-5-3-7v-4c-1-1-1-2-1-3v-1c-1 0-1 2-1 3 0-5 0-11 1-16h-1l-1 1v-2l1-3c2-5 4-10 8-14v1c0 1-2 3-2 4h0c1-2 2-3 3-4l1 1 8-6z" class="M"></path><path d="M150 498v1 1h0c1 1 0 2 0 4 0 0-1 1-1 2v-1c-1 1-1 1-1 2v-2l2-7z" class="V"></path><path d="M151 528c-1-1-1-2-1-4h1c0 1 0 2 1 3 0 2 1 5 2 7h0l1-1-1-2c-1-1-1-2-1-3v-1c2 2 3 7 4 9 0 1 1 2 1 3h0l-5-4c-1-2-2-5-2-7z" class="K"></path><path d="M162 482h1c-1 2-2 4-1 7 0 2-3 5-3 7h-1l-1-1 1-3c1-2 1-3 2-5-5 3-8 6-10 11l-2 7c-1 7-2 14 0 20v1c-1-3-1-5-2-8 0-6 0-14 2-20 1-3 3-7 6-10l8-6z" class="O"></path><path d="M152 486v1c0 1-2 3-2 4h0c1-2 2-3 3-4l1 1c-3 3-5 7-6 10-2 6-2 14-2 20 1 3 1 5 2 8v-1l3 3c0 2 1 5 2 7l5 4 1 1h-1c-1 0-1 0-2-1v1c-1 0-1 0-1 1-2-1-3-4-4-5s-1-3-2-4c-1-2-2-5-3-7v-4c-1-1-1-2-1-3v-1c-1 0-1 2-1 3 0-5 0-11 1-16h-1l-1 1v-2l1-3c2-5 4-10 8-14z" class="C"></path><path d="M144 500c1 1 1 2 2 2 0 1 0 1-1 2h-1l-1 1v-2l1-3z" class="K"></path><path d="M148 525l3 3c0 2 1 5 2 7h0-1 0l-2-4v-1c-1 0-1 0-1-1v-1l-1-2v-1z" class="L"></path><defs><linearGradient id="AD" x1="163.052" y1="505.149" x2="132.227" y2="517.097" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#AD)" d="M154 469h0 2 1c1 1 1 2 2 2l-2 1-3 3 1 1-4 4h1s0 1-1 1l-2 2c-2 2-4 4-4 7 0 1-2 4-2 5l1 1c-1 1-1 3-1 4s-1 2 0 3v2l1-1h1c-1 5-1 11-1 16 0-1 0-3 1-3v1c0 1 0 2 1 3v4c1 2 2 5 3 7 1 1 1 3 2 4s2 4 4 5c0-1 0-1 1-1 2 3 4 4 7 6h1c-1-2-1-2-1-4v-1l4 4 2 2c-1 2-2 2-3 3h-3-1v1s1 1 2 1c0 0 1 1 1 2 3 0 6 1 8 2-6 0-12-2-17-4-3 0-6-2-9-1l-2-2v-1c1 0 2 1 2 1h1v-1l-2-1 1-1c-2-3-4-5-6-7-5-7-7-15-8-24l-1-14c1-3 1-5 2-8 2-4 4-10 6-13 3-5 9-10 14-11z"></path><path d="M139 504h-1c0-2 1-6 2-7h1v1 1c-2 1-2 3-2 5z" class="K"></path><path d="M154 469h2l-4 3-2 2-1-1c2-1 3-3 5-4z" class="C"></path><path d="M148 478l2 2h1 1s0 1-1 1l-2 2c-1 0-2-1-3-1 0-1 1-3 2-4z" class="F"></path><path d="M140 530c-1-2-1-4-2-5 0-2 0-4 1-5v-2 3h1c0 3 0 5 1 7l-1 2z" class="C"></path><path d="M154 475h0l1 1-4 4h-1l-2-2h0c1-1 2-2 4-2l2-1z" class="V"></path><path d="M148 478h0c1-1 2-2 4-2-1 1-2 2-2 3l1 1h-1l-2-2z" class="K"></path><path d="M139 504c0-2 0-4 2-5v1c-1 5-1 9-2 14 0-3-1-7 0-10z" class="C"></path><path d="M147 546l9 6c-3 0-6-2-9-1l-2-2v-1c1 0 2 1 2 1h1v-1l-2-1 1-1z" class="N"></path><path d="M156 469h1c1 1 1 2 2 2l-2 1-3 3h0-4 0v-1l2-2 4-3z" class="I"></path><path d="M156 469h1c1 1 1 2 2 2l-2 1c-2-1-3 1-5 0l4-3z" class="O"></path><path d="M141 500l2-5 1 1c-1 1-1 3-1 4s-1 2 0 3v2l1-1h1c-1 5-1 11-1 16 0-1 0-3 1-3v1c0 1 0 2 1 3v4c1 2 2 5 3 7 1 1 1 3 2 4s2 4 4 5c0-1 0-1 1-1 2 3 4 4 7 6h1c-1-2-1-2-1-4v-1l4 4 2 2c-1 2-2 2-3 3h-3-1v1s1 1 2 1c0 0 1 1 1 2-2-1-4-1-6-2h0c-4-2-8-6-11-9-2-2-4-4-5-7l-3-6 1-2c-1-2-1-4-1-7 0-2 0-5-1-7 1-5 1-9 2-14z" class="B"></path><path d="M167 545l2 2c-1 2-2 2-3 3h-3-1v1s1 1 2 1c0 0 1 1 1 2-2-1-4-1-6-2 0-1-1-2-2-3h0 0c1 0 3-1 5 0l1 1 1-1c1 0 2-1 3-1v-3z" class="J"></path><defs><linearGradient id="AE" x1="151.915" y1="531.917" x2="135.214" y2="502.76" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#656565"></stop></linearGradient></defs><path fill="url(#AE)" d="M141 500l2-5 1 1c-1 1-1 3-1 4s-1 2 0 3v2l1-1h1c-1 5-1 11-1 16l1 4c0 2 0 3 1 5l1 1c1 2 2 5 3 7h0c-1-1-3-4-3-5h-1s0 1 1 2c0 1 1 2 1 3h0l-2-2c-2-1-4-5-5-7s-1-4-1-7c0-2 0-5-1-7 1-5 1-9 2-14z"></path><path d="M143 500c0 1-1 2 0 3v2c-1 3-1 6-1 9v-1c-1-1-1-6-1-7l1-3c0-1 0-2 1-3z" class="M"></path><path d="M233 247c2-1 3-3 5-4 2-2 3-4 4-5v2 1l-2 3h-1-1 0c0 2-1 2-2 3v1l2 2h-1c-1 1-2 2-2 3-1 1-2 0-2 1h0c-1 2-2 5-3 6v1c-2 3-5 6-7 10v2c-1 0 0 0-1 1h0l-1 1-1 1h0v1 1l2-3c1-1 1-2 2-2-2 3-5 6-6 9 0 2-3 7-2 9v-1c1 2 0 2 0 3l-1 1c-1 4-3 8-5 12 0 1-1 2 0 2l-2 5v2 5c1 1 2 1 2 2h1s0-1 1-1c0 2 3 3 4 4v-1c2 1 4 3 5 4l2 1c1 0 2 1 3 1v-1h1c1 1 3 1 4 1l3 3 2 6c0 3 2 6 2 9 2 3 2 7 4 10h0c0-1 0-2 1-2h1c-1 1-1 3 0 4 1 3 3 4 6 6l1 2c1 0 2-1 2-1 2 3 3 5 5 8 1 2 2 4 2 5 1 1 1 2 1 2h-1l-2-2h0l-1-3-2-2c0 1 0 3 1 4h0c2 4 4 8 7 11l-2 2c-1-1-1-2-2-3 0-1 0-1-1-2h-1l-1-4c-1-3-2-7-3-10-1-1-2-2-2-3v-2l-1-1-1 1 1 1c-2 1-2 2-3 3h-1c0-1 0-1-1-2h-2c-1 1-2 3-2 4l-1 6-1 1h0c0 1-1 2-1 4v-2c-1-3 0-6 0-9 1-1 1-3 1-4h-1-4 0c-1 1-1 3-2 4v2h-1v-3c0-1-1-2-2-3v1h-1v-1 1c0 1 0 1-1 1v1l-1 4v-1h-1v2h0c0-2 0-2-1-3h0-1v-3c-1 0-1 0-1-1-2 1-4 1-7 1 0 1-2 0-2 0-3 0-7 0-10 1h0c-1-1-4-1-5-1v1h-1c-1 0-1 0-2 1h-1 0c0-1 1-2 1-3h0l-4-1-2 1-1-1c-1-1-3-1-4-2-2 0-4-1-6-1-1 0-3 0-4-1h1l-1-1c-1-1-1-2-2-2v-1c-1-1-3-3-4-5v-2l2 2c0-2-1-4-1-5v-4-6c1-1 1-2 2-3 1-4 1-7 3-11 0 1 0 1 1 1 0-1 1-2 2-3s2-3 4-5h0c0-1 1-2 1-3h0v-1s-1 0-1 1l-1-1 1-1 1-2 2-5h0v-1-3l-1-1c2-2 3-3 4-5v-1c1-1 3-3 5-4v-2h0l1-1h2 1 1l3-3c0-1 0-1-1-1 2-1 3-3 4-4v-1-1h-2c3-1 4-3 6-4 1-1 1-2 2-3 2-2 3-4 5-6l7-7h0c0-2 5-6 6-7h-1l7-6v-1z" class="O"></path><path d="M183 352h0v-2h1l2 2h-2c-1 1-1 0-1 0z" class="D"></path><path d="M203 369c2 0 3-1 5 0l3 2c-3 0-6-1-8-1l-1-1h1z" class="E"></path><path d="M224 370c0-1 1-3 3-3h0v2c-1 1-1 1-1 2h1v-1l1 1c0 1 0 1-1 1h-1v-2h-1s0 1-1 1v-1z" class="D"></path><path d="M175 329c0-1 1-2 2-3 0 3-1 4-1 6-1 2-1 3-2 5 0-1 0-1-1-2l1-3 1-3z" class="G"></path><path d="M180 339l1 3c0 3 2 5 3 8h-1v2h0c-3-4-3-9-3-13z" class="E"></path><path d="M179 328c0-2 1-4 2-5 0-1 1-2 2-3l-2 8c0 2 0 4-1 6v-4h0c-1 2-1 4-1 6l-1-4c1-1 1-1 1-2h0v-2z" class="D"></path><path d="M224 370v1c1 0 1-1 1-1h1v2h1v1l-1 4v-1h-1v2h0c0-2 0-2-1-3h0-1v-3c-1 0-1 0-1-1 0 0 1-1 2-1z" class="N"></path><path d="M224 375v-2h1v1c1 0 1-1 2-1l-1 4v-1h-1v2h0c0-2 0-2-1-3z" class="L"></path><path d="M229 370c0-1 1-1 1-2 1 0 1 1 2 1h1 0 2 0c1 0 2 0 3 1h-4 0c-1 1-1 3-2 4v2h-1v-3c0-1-1-2-2-3z" class="H"></path><path d="M223 362l1 1c0 1-1 2-1 3h0 0c-2-2-6-7-6-10h0l1 1 1 1v-1s1 1 1 2c0 0 1 0 1 1l2 2h0z" class="D"></path><path d="M192 352l1 2 1-1c-2-2-4-5-4-8 1 0 1 2 2 2 1 2 2 4 3 5 0 1 2 2 2 3v1c-1 1-1 1-2 0-2-1-2-1-3-2h0v-2z" class="N"></path><path d="M198 340l3 6c0 1 1 3 1 4v1l6 6h1l3 3-6-3h-1c-5-5-6-10-7-17z" class="B"></path><path d="M189 314l2 1c-3 4-4 9-6 15v-2-2l-2 1v-1c1-5 3-8 6-12z" class="E"></path><path d="M179 328v2h0c0 1 0 1-1 2l1 4c-1 4-1 8 1 13 0 2 1 3 1 5-1-1-2-3-3-5-2-5-3-10-1-15 0-2 1-4 2-6zm28 34l5 1c1 0 2 0 3-1 1 1 2 1 2 1 0-1-1-1 0-2 1 0 1 1 2 1 1 2 2 3 2 4 1 1 1 1 1 2l-1 2-2-1v1c-1 0-3 0-4-1h3 0c-1-2-2-3-3-4h-2c-1-1-3-2-4-2h-1l-1-1z" class="G"></path><path d="M198 335l1-4c1 2 0 4 1 6l3 12c2 2 4 5 7 6h0c2 2 4 3 5 4-5-1-9-4-13-9 0-1-1-3-1-4l-3-6v-5z" class="M"></path><path d="M205 342c1 2 2 4 3 5v1l3 3h1 0l-1 1v1c1 1 2 1 2 2h-1-1-1 0c-3-1-5-4-7-6 1 0 1 0 2-1-1-2-1-3 0-6z" class="D"></path><path d="M205 348c0 1 1 2 2 3 0 1 2 2 3 3l1 1h-1 0c-3-1-5-4-7-6 1 0 1 0 2-1z" class="H"></path><path d="M204 359v-1c2 1 2 2 3 4l1 1h1c1 0 3 1 4 2h2c1 1 2 2 3 4h0-3l-1 1c-2 0-6-6-7-7l-3-3 1 3h-1-2v-1-3h2z" class="B"></path><path d="M200 337c0-4 0-9 2-13v3 8c1 3 2 5 3 7-1 3-1 4 0 6-1 1-1 1-2 1l-3-12z" class="Q"></path><path d="M205 298c0 2-1 3-2 4-1 3-3 6-5 8 0 2-2 4-3 5-2 3-4 8-5 12-2 7-4 15-1 22l3 3v2c-2-2-4-4-4-6-3-8-1-14 1-22 2-5 3-10 6-14 0-1 1-2 1-2 2-3 4-4 5-7l4-5z" class="F"></path><defs><linearGradient id="AF" x1="178.907" y1="335.373" x2="188.216" y2="338.68" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#555454"></stop></linearGradient></defs><path fill="url(#AF)" d="M183 326v1l2-1v2 2c0 5-1 10 0 15 0 1 0 3 1 5 0 0 1 1 1 2h-1l-2-2c-1-3-3-5-3-8l-1-3v-5c1-2 1-4 1-6h1l1-2z"></path><path d="M218 282c0 2-3 7-2 9v-1c1 2 0 2 0 3l-1 1c-1 4-3 8-5 12 0 1-1 2 0 2l-2 5v-1l-1 1h-1v-1c-1 0-1 0-1 1l-2 4c0 1 0 4-1 5v5-3c-2 4-2 9-2 13-1-2 0-4-1-6l-1 4c0-4 1-7 2-10 2-8 3-14 7-21 0-1 1-2 2-3h2 0c2-6 4-13 7-19z" class="B"></path><path d="M207 304c0-1 1-2 2-3h2 0c-1 1-2 4-4 5 0-1 1-1 0-2z" class="J"></path><path d="M208 295h1 1v2 1h-1-1v1c-1 4-5 8-7 12 0 2-1 4-1 6h0-1c-2 1-2 5-3 7s-1 3-1 5v-1c0-1-1-1-1-2v-1h0l-1 2h-1v-2c-1 0 0 0-1 1v2l-1-1c1-4 3-9 5-12 1-1 3-3 3-5 2-2 4-5 5-8 1-1 2-2 2-4l3-3z" class="L"></path><path d="M201 311c0 2-1 4-1 6h0-1c-2 1-2 5-3 7s-1 3-1 5v-1c0-1-1-1-1-2v-1h0c1-1 1-2 1-3h-1v-1c1-2 1-3 2-4h2c0-1 2-2 2-4l1-2z" class="J"></path><path d="M185 355l1-1c1 1 2 1 2 2 2 1 4 2 5 4l1-1-1-1h1c1 1 2 1 3 1h1v1l2-2 1 1v-2h1l2 2h-2v3 1h2 1c0 1 1 2 1 4l2 2c-2-1-3 0-5 0h-1c-3 0-4-2-6-4l-9-6c-1-1-1-2-2-3v-1z" class="E"></path><path d="M197 364l-2-2 1-1c2 2 4 5 6 6h1v-1c1 0 2 1 3 1l2 2c-2-1-3 0-5 0-3-1-5-3-6-5z" class="N"></path><path d="M185 355l1-1c1 1 2 1 2 2v1l3 2v1c1 2 2 2 5 3 0 0 1 0 1 1 1 2 3 4 6 5h-1c-3 0-4-2-6-4l-9-6c-1-1-1-2-2-3v-1z" class="I"></path><path d="M185 355l1-1c1 1 2 1 2 2v1l3 2v1c-2-1-4-4-6-5z" class="D"></path><path d="M186 312c2-1 3-2 4-4v-2h0l6-6c1 0 1 0 2-1h0 1s1-1 2-1l5-4-2 3c-1 2-3 5-3 6h0c-1 3-3 4-5 7 0 0-1 1-1 2 0-1 0-2 1-3v-1l-2 2h0l-1 1c-1 1-2 2-2 4l-2-1c-3 4-5 7-6 12l-1 2h-1l2-8 3-8z" class="C"></path><path d="M189 314c1-1 1-2 2-2h1c0-1 1-1 1-1-1 1-2 2-2 4l-2-1z" class="I"></path><path d="M194 310c1-2 3-4 4-6 1-1 2-3 2-4 2-1 3-2 4-3-1 2-3 5-3 6h0c-1 3-3 4-5 7 0 0-1 1-1 2 0-1 0-2 1-3v-1l-2 2h0z" class="B"></path><path d="M196 324c1-2 1-6 3-7h1l-1 5c0 3-1 6-2 10v1c-1 6-1 11 0 16 0 2 1 3 1 4l1 1h0c-1 0-2-1-3-1-5-7-7-18-5-25v-2c1-1 0-1 1-1v2h1l1-2h0v1c0 1 1 1 1 2v1c0-2 0-3 1-5z" class="C"></path><path d="M196 324c1-2 1-6 3-7h1l-1 5c0 3-1 6-2 10 0-1 0-1-1-2-1 5-1 9-1 14-1-2-1-5-2-7 0-4 0-7 1-11 0 1 1 1 1 2v1c0-2 0-3 1-5z" class="G"></path><path d="M196 324c1-2 1-6 3-7h1l-1 5c-1 1-1 2-2 3s-1 1-1 2h-1l1-3z" class="I"></path><path d="M202 327v-5l1 2c1 1 1 1 1 2 1 1 1 2 1 2 0 3 1 7 2 10l1 2h1v-2c1 2 2 3 3 4l6 5h-1v1c1 2 1 6 3 7 0 0 0 1 1 1l2 6h0l-2-2c0-1-1-1-1-1 0-1-1-2-1-2v-1l-3-3c-1-1-3-2-4-2h-1l-3-3v-1c-1-1-2-3-3-5s-2-4-3-7v-8z" class="G"></path><path d="M205 328c0 3 1 7 2 10l1 2c1 4 3 6 4 9h-1c-1-1-1-3-2-4l-3-8c-1-3-2-6-1-9zm7 17h-1v-3h1l6 5h-1v1c-1 1-1 1-1 2l-1-1c-1-1-2-2-3-4z" class="D"></path><path d="M212 345c1 0 1 1 3 2v2h0c-1-1-2-2-3-4z" class="O"></path><path d="M202 327v-5l1 2c1 1 1 1 1 2 1 1 1 2 1 2-1 3 0 6 1 9-1 0-1 1-1 2 0 3 3 5 3 8-1-1-2-3-3-5s-2-4-3-7v-8z" class="F"></path><path d="M202 322c1-1 1-4 1-5l2-4c0-1 0-1 1-1v1h1l1-1v1 2 5l1 4v2h1v-1c2 2 3 4 4 5h1l3 2v1c1 2 2 4 4 5 1 0 2 0 3 1h-1v1c1 0 1 0 1 1h0c1 2 0 4 2 5l1 8-1-2c0-1-1-1-1-1l-8-4-6-5c-1-1-2-2-3-4v2h-1l-1-2c-1-3-2-7-2-10 0 0 0-1-1-2 0-1 0-1-1-2l-1-2z" class="D"></path><path d="M210 326v-1c2 2 3 4 4 5 1 2 2 3 2 5-3-2-5-6-6-9z" class="E"></path><path d="M206 320c0-1 0-1 1-2 0-1 0-2 1-3v5l1 4v2c2 5 4 9 6 13-1-1-2-2-3-4-2-3-3-6-4-10h0c0-2 0-3-2-5z" class="B"></path><defs><linearGradient id="AG" x1="217.564" y1="341.566" x2="213.395" y2="344.109" xlink:href="#B"><stop offset="0" stop-color="#535253"></stop><stop offset="1" stop-color="#737272"></stop></linearGradient></defs><path fill="url(#AG)" d="M209 338v-3c3 5 7 8 12 11 1 1 2 2 3 2h1c0 1 0 1 1 2v1l-8-4-6-5c-1-1-2-2-3-4z"></path><path d="M214 330h1l3 2v1c1 2 2 4 4 5 1 0 2 0 3 1h-1v1c1 0 1 0 1 1h0c1 2 0 4 2 5l1 8-1-2c0-1-1-1-1-1v-1c-1-1-1-1-1-2s-1-2-2-3c0-1-1-2-2-3-3-2-3-4-5-7h0c0-2-1-3-2-5z" class="H"></path><defs><linearGradient id="AH" x1="208.755" y1="324.204" x2="203.405" y2="325.167" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#AH)" d="M202 322c1-1 1-4 1-5l2-4c0-1 0-1 1-1v1h1l1-1v1 2c-1 1-1 2-1 3-1 1-1 1-1 2 2 2 2 3 2 5h0l-1 2 2 8h0v3 2h-1l-1-2c-1-3-2-7-2-10 0 0 0-1-1-2 0-1 0-1-1-2l-1-2z"></path><path d="M206 320c2 2 2 3 2 5h0l-1 2c-1-2-1-5-1-7z" class="N"></path><path d="M174 328c0 1 0 1 1 1l-1 3-1 3c1 1 1 1 1 2 0 6 1 12 5 17 2 3 6 7 9 8 1 0 1 0 2 1-1-2-2-2-3-3v-1l9 6c2 2 3 4 6 4l1 1h-2 0c1 1 1 1 1 2 3 1 7 0 10 0h3c0 1-2 0-2 0-3 0-7 0-10 1h0c-1-1-4-1-5-1v1h-1c-1 0-1 0-2 1h-1 0c0-1 1-2 1-3h0l-4-1-2 1-1-1c-1-1-3-1-4-2-2 0-4-1-6-1-1 0-3 0-4-1h1l-1-1c-1-1-1-2-2-2v-1c-1-1-3-3-4-5v-2l2 2c0-2-1-4-1-5v-4-6c1-1 1-2 2-3 1-4 1-7 3-11z" class="C"></path><path d="M174 328c0 1 0 1 1 1l-1 3c-2 1-2 7-2 9 0 5 1 11 4 15l-1 1c-1-2-2-5-3-7l-1-1h0c-1-3 0-7 0-10 1-4 1-7 3-11z" class="V"></path><path d="M195 371h6c-1-1-3-2-5-2-4-1-8-3-12-5v-1c-1 0-1 0-2-1h-1l-3-3v-5c4 7 11 11 18 13-2-2-5-3-8-5h0c1 0 1 0 2 1-1-2-2-2-3-3v-1l9 6c2 2 3 4 6 4l1 1h-2 0c1 1 1 1 1 2 3 1 7 0 10 0h3c0 1-2 0-2 0-3 0-7 0-10 1h0c-1-1-4-1-5-1v1h-1c-1 0-1 0-2 1h-1 0c0-1 1-2 1-3z" class="D"></path><path d="M169 342c1-1 1-2 2-3 0 3-1 7 0 10h0l1 1c1 2 2 5 3 7 2 4 5 6 7 9 3 1 6 4 9 4l-2 1-1-1c-1-1-3-1-4-2-2 0-4-1-6-1-1 0-3 0-4-1h1l-1-1c-1-1-1-2-2-2v-1c-1-1-3-3-4-5v-2l2 2c0-2-1-4-1-5v-4-6z" class="N"></path><path d="M173 362l-1-1v-1c-1-1-1-1-1-2l1-1v1c1 1 2 1 2 2 1 2 5 5 7 6h1c3 1 6 4 9 4l-2 1-1-1c-1-1-3-1-4-2-2 0-4-1-6-1-1 0-3 0-4-1h1l-1-1c-1-1-1-2-2-2v-1h1z" class="H"></path><path d="M173 362c1 1 3 4 5 5-1 0-3 0-4-1h1l-1-1c-1-1-1-2-2-2v-1h1z" class="D"></path><path d="M212 321c0 2 3 3 4 4v-1c2 1 4 3 5 4l2 1c1 0 2 1 3 1v-1h1c1 1 3 1 4 1l3 3 2 6c0 3 2 6 2 9 2 3 2 7 4 10h0c0-1 0-2 1-2h1c-1 1-1 3 0 4 1 3 3 4 6 6l1 2c1 0 2-1 2-1 2 3 3 5 5 8 1 2 2 4 2 5 1 1 1 2 1 2h-1l-2-2h0l-1-3-2-2c0 1 0 3 1 4h0c2 4 4 8 7 11l-2 2c-1-1-1-2-2-3 0-1 0-1-1-2h-1l-1-4c-1-3-2-7-3-10-1-1-2-2-2-3v-2l-1-1-1 1c-4-1-7-5-9-8l-1 1h0v2 1c1 1 1 2 2 3-2 0-3-1-5 0-1 0-1 0-2-1h-1c-2 0-3-1-3-3-1-3-2-6-2-9l-1-8c-2-1-1-3-2-5h0c0-1 0-1-1-1v-1h1c-1-1-2-1-3-1-2-1-3-3-4-5v-1l-3-2h-1c-1-1-2-3-4-5v1h-1v-2l-1-4c1 1 2 1 2 2h1s0-1 1-1z" class="Q"></path><path d="M234 351c2 1 6 6 7 8 2 5 4 7 9 8l-1 1c-4-1-7-5-9-8l-1 1h0v2 1l-1 1c0-1-1-1-1-2v-1l-1-3v-1c-1-3-2-5-2-7z" class="G"></path><path d="M236 359l1-1c1 0 2 1 3 2h0l-1 1h0v2 1l-1 1c0-1-1-1-1-2v-1l-1-3zm-24-38c0 2 3 3 4 4v-1c2 1 4 3 5 4l2 1c1 0 2 1 3 1v-1h1v1l2 2h1c3 4 4 9 6 14-3-3-5-6-8-8-1 0-2-1-3-2l-7-4-3-2h-1c-1-1-2-3-4-5v1h-1v-2l-1-4c1 1 2 1 2 2h1s0-1 1-1z" class="N"></path><path d="M230 332c-2 0-2-1-3 0-2 0-5-2-7-3h1v-1h0l2 1c1 0 2 1 3 1v-1h1v1l2 2h1z" class="O"></path><path d="M212 321c0 2 3 3 4 4l3 3v1s1 0 1 1h-1c-1-1-2-1-3-2 0-1-1-2-1-2h-1c-1 0-2-2-2-2-1-1-2-1-3-2v2l-1-4c1 1 2 1 2 2h1s0-1 1-1z" class="H"></path><path d="M227 346c-2-1-1-3-2-5h0c0-1 0-1-1-1v-1h1c1 0 2 1 3 2v-1h1c0 1 1 2 1 3 1 2 4 6 4 8s1 4 2 7v1l1 3v1c0 1 1 1 1 2l1-1c1 1 1 2 2 3-2 0-3-1-5 0-1 0-1 0-2-1h-1c-2 0-3-1-3-3-1-3-2-6-2-9l-1-8z" class="L"></path><path d="M227 346c-2-1-1-3-2-5h0c0-1 0-1-1-1v-1h1c1 0 2 1 3 2v-1h1c0 1 1 2 1 3v1 2l-1 1c-1-1-1-4-2-6-1 0-1-1-2-1 1 1 1 2 1 2 1 1 1 3 1 4z" class="D"></path><defs><linearGradient id="AI" x1="240.024" y1="365.827" x2="228.786" y2="357.451" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#343334"></stop></linearGradient></defs><path fill="url(#AI)" d="M230 363h1c0-1 1-1 2-2 0-1-2-5-2-6l1-1s0 1 1 1c0 0 0 1 1 1l1 3c0 1 1 2 2 4 0 1 1 1 1 2l1-1c1 1 1 2 2 3-2 0-3-1-5 0-1 0-1 0-2-1h-1c-2 0-3-1-3-3z"></path><defs><linearGradient id="AJ" x1="222.803" y1="275.545" x2="207.834" y2="267.647" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#333334"></stop></linearGradient></defs><path fill="url(#AJ)" d="M233 247c2-1 3-3 5-4 2-2 3-4 4-5v2 1l-2 3h-1-1 0c0 2-1 2-2 3v1l2 2h-1c-1 1-2 2-2 3-1 1-2 0-2 1h0c-1 2-2 5-3 6v1c-2 3-5 6-7 10-2 3-5 7-6 11-2 3-3 7-5 11 0 1-1 3-2 4v-2h-1-1l-3 3-4 5h0c0-1 2-4 3-6l2-3-5 4c-1 0-2 1-2 1h-1 0c-1 1-1 1-2 1l-6 6h0v2c-1 2-2 3-4 4l3-6-2-1-3 4h0v-1-3l-1-1c2-2 3-3 4-5v-1c1-1 3-3 5-4v-2h0l1-1h2 1 1l3-3c0-1 0-1-1-1 2-1 3-3 4-4v-1-1h-2c3-1 4-3 6-4 1-1 1-2 2-3 2-2 3-4 5-6l7-7h0c0-2 5-6 6-7h-1l7-6v-1z"></path><path d="M221 261h1c-2 3-4 5-6 8h-1l2-2v-1c-1 1-2 1-2 2h-1l7-7h0z" class="F"></path><path d="M236 247v1l2 2h-1c-1 1-2 2-2 3-1 1-2 0-2 1l-2 1h-1c1-1 1-1 0-2l6-6z" class="J"></path><path d="M214 268h1c0-1 1-1 2-2v1l-2 2h1c-1 1-3 3-3 4-1 2-2 3-3 5-2 2-4 5-5 8h1l-1 1c-1 1-2 2-4 3 0-1 1-2 1-2l-1-1-1 1c0-1 0-1-1-1 2-1 3-3 4-4v-1-1h-2c3-1 4-3 6-4 1-1 1-2 2-3 2-2 3-4 5-6z" class="B"></path><path d="M203 282l2-1h0l-1 4c-1 0-2 1-3 2l-1 1c0-1 0-1-1-1 2-1 3-3 4-4v-1z" class="H"></path><path d="M213 273c-1 2-2 3-3 5-2 2-4 5-5 8h1l-1 1c-1 1-2 2-4 3 0-1 1-2 1-2l-1-1c1-1 2-2 3-2 3-4 5-8 9-12z" class="N"></path><path d="M201 287l1 1s-1 1-1 2c2-1 3-2 4-3v1c-2 3-5 7-9 10-3 2-6 4-9 7l-3 4h0v-1-3l-1-1c2-2 3-3 4-5v-1c1-1 3-3 5-4v-2h0l1-1h2 1 1l3-3 1-1z" class="F"></path><path d="M195 291h1 1c-1 2-3 4-4 5l-6 3v-1c1-1 3-3 5-4v-2h0l1-1h2z" class="D"></path><path d="M193 291h2l-3 3v-2h0l1-1z" class="B"></path><path d="M233 254h0c-1 2-2 5-3 6v1c-2 3-5 6-7 10-2 3-5 7-6 11-2 3-3 7-5 11 0 1-1 3-2 4v-2h-1-1l2-5-1-2c-1 1-1 2-2 2h0c1-3 2-6 4-9 2-5 5-10 8-14 1-1 2-2 4-3h0c1 0 2-1 2-2h0c1-2 4-5 6-7l2-1z" class="I"></path><path d="M209 288v-1l1-1v3 1l-1-2z" class="N"></path><path d="M233 254h0c-1 2-2 5-3 6v-3c0 1-5 5-5 5h0c1-2 4-5 6-7l2-1z" class="E"></path><path d="M219 267h1s1-1 2-1h0v1l1-1c0 2-7 11-9 13-1 1-1 3-2 4h-1v-2c2-5 5-10 8-14z" class="F"></path><path d="M80 468l2-2c0 2 0 3-1 5 0 1 0 1 1 2h0v1c-1 4-3 9-3 14-1 1-1 2-1 4h1v2 1c0 1 0 0 1 1-1 2-1 6-1 8 1 1 1 3 0 5v8c1 6 2 12 4 18 1 4 2 11 5 15h1l3 7 1-1c0 1 1 2 1 3 1 1 2 3 4 4v-1c1 1 2 2 3 2l-3-5v-2c1 2 3 3 3 4 3 4 6 8 10 11 1 1 2 1 3 1h1l4 4 1 1c7 7 16 13 26 17 3 1 7 2 10 3 4 1 7 2 11 2h0c2-2 6-1 8-1h4c2 2 7 0 8 2h0c4 0 10-1 15-1 7-1 14-3 21-6 6-3 13-7 18-12 2-2 5-3 7-6v1c-9 12-22 18-35 22-7 2-13 4-19 5l-8 1s-1 0-2 1h1 0-1c1 0 2 0 2 1 2 1 7 0 9-1 1 1 1 1 1 2 2-1 5-1 7-1 1 0 3 0 5-1 0 1 0 1 1 2l-12 2 1 1c13-1 27-6 38-13 5-2 9-6 14-10v1h2 0v1c3-1 7-7 8-9 1-1 2-3 3-5 1-1 2-1 2-2v1 2c-1 3-3 6-5 8-1 3-1 5-3 7h0c0 1-1 1 0 2l-6 6h-1c-2 2-6 3-8 6 0 0 1 0 1 1h1c-11 8-25 13-39 16-4 2-9 3-14 3h-12c-10 0-20-2-30-4l-14-5c-12-4-24-13-32-22-3-3-6-7-8-11-4-5-8-11-12-18l-2-6c-5-10-9-21-11-33-2-7-2-16-1-23 0-2 1-5 1-7l3-12 1 1c0-2 1-4 2-6 1-4 2-8 4-12z" class="P"></path><path d="M168 616h4l1 1h-3-3 1 0v-1z" class="B"></path><path d="M175 615c1-1 2-1 4-1h0c1 0 2 0 3-1h1l4 1 6-1v1h3c-3 1-6 1-8 1h-13z" class="F"></path><path d="M187 614l6-1v1h3c-3 1-6 1-8 1h-5v-1h4z" class="V"></path><path d="M93 559h1c1 2 3 5 5 5l1 1c0 1 1 1 1 2 1 1 2 2 2 3v2s1 1 1 2h1l-1 1c-2-2-3-4-5-6-2-3-4-6-6-10z" class="W"></path><path d="M172 616c3 0 22 0 23 1h-5c-6 1-13 1-19 1-1 0-1-1-1-1h3l-1-1z" class="E"></path><path d="M109 582c2 1 4 4 7 6l9 8c1 1 1 2 1 2l-7-5c-2-1-4-3-6-5-1-1-3-3-3-4-1-1-1-1-1-2z" class="M"></path><path d="M238 600c1-1 3-2 4-3 3-2 7-5 10-7v1l-3 3c-4 4-9 8-15 11l-3 1c2-1 7-5 7-6zm-5 9c0-1 20-12 22-17 1-3 3-5 5-7-1 3-1 5-3 7h0c0 1-1 1 0 2l-6 6h-1c-2 2-6 3-8 6 0 0 1 0 1 1-2 1-3 2-5 3h-3c-2 1-3 2-4 2h-1l-2 1-2 1v-1c2-1 6-2 8-4h-1z" class="B"></path><path d="M198 611c13-1 27-6 38-13 5-2 9-6 14-10v1h2 0v1h0c-3 2-7 5-10 7-1 1-3 2-4 3-13 7-27 14-42 14h-3v-1l-6 1-4-1h2l13-2z" class="W"></path><path d="M198 611c13-1 27-6 38-13 5-2 9-6 14-10v1c-5 4-9 7-15 11-7 4-14 8-22 10-4 1-8 2-12 2-3 1-5 1-8 1l-6 1-4-1h2l13-2z" class="H"></path><defs><linearGradient id="AK" x1="75.808" y1="499.493" x2="78.407" y2="562.399" xlink:href="#B"><stop offset="0" stop-color="#3d3c3d"></stop><stop offset="1" stop-color="#6b6b6b"></stop></linearGradient></defs><path fill="url(#AK)" d="M80 468l2-2c0 2 0 3-1 5 0 1 0 1 1 2h0v1c-1 4-3 9-3 14-1 1-1 2-1 4l-3 12v9c0 7 0 14 2 20 2 16 9 30 19 43 3 4 6 8 10 11l12 10h-1-1v-1c-3-1-5-3-7-5s-4-4-6-5h-1l-2-1-3-3c1 1 1 2 1 3-5-6-9-12-13-19h-1-1l-2-6c-5-10-9-21-11-33-2-7-2-16-1-23 0-2 1-5 1-7l3-12 1 1c0-2 1-4 2-6 1-4 2-8 4-12z"></path><path d="M81 560c2 2 3 3 4 6h-1-1l-2-6z" class="K"></path><path d="M97 582c-3-4-6-8-8-12-1-2-3-4-3-6v-1 1c2 3 5 6 7 10 3 4 6 8 10 12h-1l-2-1-3-3z" class="O"></path><path d="M80 468l2-2c0 2 0 3-1 5-1 3-3 6-4 9 0 2 0 4-1 6v-1c-2 3-2 6-3 9v1 4h-1v-4l2-9c0-2 1-4 2-6 1-4 2-8 4-12z" class="F"></path><path d="M76 486c1-2 1-4 1-6 1-3 3-6 4-9 0 1 0 1 1 2h0v1c-1 4-3 9-3 14-1 1-1 2-1 4l-3 12v9c-1 2-1 4-1 6h0c0-2-1-5-1-8 1-2 1-4 1-6-1-1-1-2-1-3 0-5 2-11 3-16z" class="K"></path><path d="M83 566h1 1c4 7 8 13 13 19 0-1 0-2-1-3l3 3 2 1h1c2 1 4 3 6 5s4 4 7 5v1h1 1 1c3 2 6 4 10 6 12 7 25 13 39 13v1h0-1v1c1 0 3 0 4 1h10c4 1 8 1 11 1 14-1 29-6 41-11h1c-2 2-6 3-8 4v1l2-1 2-1h1c1 0 2-1 4-2h3c2-1 3-2 5-3h1c-11 8-25 13-39 16-4 2-9 3-14 3h-12c-10 0-20-2-30-4l-14-5c-12-4-24-13-32-22-3-3-6-7-8-11-4-5-8-11-12-18z" class="M"></path><path d="M150 621c1-1 1-1 2 0 5 1 11 3 15 2 3-1 11-2 14-1-3 0-7 0-10 1-2 0-3 1-5 1-5 0-11-2-16-3z" class="I"></path><path d="M110 599c2 1 3 4 6 4 1 1 2 1 3 2l9 6 7 3 6 3c-1 0-2 0-3-1-3 0-5-2-8-3-2-1-4-3-6-3-1 0-2-1-3-2-5-2-8-5-11-9z" class="E"></path><defs><linearGradient id="AL" x1="114.329" y1="591.325" x2="102.338" y2="594.597" xlink:href="#B"><stop offset="0" stop-color="#151613"></stop><stop offset="1" stop-color="#504f54"></stop></linearGradient></defs><path fill="url(#AL)" d="M100 585l2 1h1c2 1 4 3 6 5s4 4 7 5l-1 1c0-1-1-1-2-2l-2-1-3-3-1 1c2 2 6 5 8 7 1 0 3 1 4 2h-2l-1 1c-5-3-9-7-13-12-1 0-2-1-2-2s0-1-1-2v-1z"></path><path d="M116 602l1-1h2c11 7 23 13 36 17h0c-4 0-8-1-12-2-10-3-19-8-27-14z" class="P"></path><path d="M149 622v-1h1c5 1 11 3 16 3 2 0 3-1 5-1 3-1 7-1 10-1s6 1 8 1v1h0 3v1l-1 1h-12c-10 0-20-2-30-4z" class="K"></path><path d="M180 625h-5c-1 0-2 0-3-1h9-3l2 1z" class="F"></path><path d="M181 624h8 3v1h-12l-2-1h3z" class="I"></path><defs><linearGradient id="AM" x1="146.244" y1="610.136" x2="150.317" y2="581.101" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#AM)" d="M88 550h1l3 7 1-1c0 1 1 2 1 3 1 1 2 3 4 4v-1c1 1 2 2 3 2l-3-5v-2c1 2 3 3 3 4 3 4 6 8 10 11 1 1 2 1 3 1h1l4 4 1 1c7 7 16 13 26 17 3 1 7 2 10 3 4 1 7 2 11 2h0c2-2 6-1 8-1h4c2 2 7 0 8 2h0c4 0 10-1 15-1 7-1 14-3 21-6 6-3 13-7 18-12 2-2 5-3 7-6v1c-9 12-22 18-35 22-7 2-13 4-19 5l-8 1s-1 0-2 1h1 0-1c1 0 2 0 2 1 2 1 7 0 9-1 1 1 1 1 1 2 2-1 5-1 7-1 1 0 3 0 5-1 0 1 0 1 1 2l-12 2 1 1-13 2h-2-1c-1 1-2 1-3 1h0c-2 0-3 0-4 1-10-1-21-3-30-7-6-2-13-6-19-10 0 0 0-1-1-2l-9-8c-3-2-5-5-7-6-1-3-3-5-5-7l1-1h-1c0-1-1-2-1-2v-2c0-1-1-2-2-3 0-1-1-1-1-2l-1-1c-2 0-4-3-5-5h-1c-1 0-1-2-2-3l-3-6z"></path><path d="M203 607c1 0 3 0 5-1 0 1 0 1 1 2l-12 2 1 1-13 2v-1h-8c-1 0-2 1-3 0h1v-1h-1v-1c1 0 1 0 2 1h0c5 0 11-1 16-2 1 0 2-1 4-1 2-1 5-1 7-1z" class="N"></path><path d="M196 608c2-1 5-1 7-1-3 2-8 3-11 2 1 0 2-1 4-1zm-20 3v1c7 0 14-1 21-2l1 1-13 2v-1h-8c-1 0-2 1-3 0h1v-1h-1v-1c1 0 1 0 2 1h0zm-88-61h1l3 7 1-1c0 1 1 2 1 3 1 1 2 3 4 4v-1c1 1 2 2 3 2l-3-5v-2c1 2 3 3 3 4 3 4 6 8 10 11 1 1 2 1 3 1h1l4 4-1 1 3 3h0c-1 0-2 0-3-1l-1 1 10 8-1 1c1 1 2 1 2 2l3 1c2 1 4 2 5 3v1c-1 0-1-1-2-1h-1l-6-4-4-3c-1-1-2-1-3-2h-1c-2-2-5-3-7-6l-9-11c0-1-1-2-2-3 0-1-1-1-1-2l-1-1c-2 0-4-3-5-5h-1c-1 0-1-2-2-3l-3-6z" class="F"></path><path d="M106 571c-2-2-4-5-5-8v-2c3 4 6 8 10 11 1 1 2 1 3 1h1l4 4-1 1 3 3h0c-1 0-2 0-3-1l-1 1h0l-2-2c-3-2-6-4-9-8z" class="D"></path><path d="M106 571c1 0 2 0 3 1 2 3 7 4 9 7h-1-2c-3-2-6-4-9-8z" class="N"></path><path d="M248 576v1c-9 12-22 18-35 22-7 2-13 4-19 5l-8 1c-6 0-13 1-19 0-9-1-19-5-27-9-4-2-9-4-13-7l-10-8 1-1c1 1 2 1 3 1h0l-3-3 1-1 1 1c7 7 16 13 26 17 3 1 7 2 10 3 4 1 7 2 11 2h0c2-2 6-1 8-1h4c2 2 7 0 8 2h0c4 0 10-1 15-1 7-1 14-3 21-6 6-3 13-7 18-12 2-2 5-3 7-6z" class="P"></path><path d="M167 600c2-2 6-1 8-1h4c2 2 7 0 8 2h0c-7 1-14 0-20-1z" class="R"></path><path d="M300 627c5 0 10 0 15-1 6-1 11-3 16-5 4-2 8-3 11-6 5-3 10-7 14-11 6-7 11-15 14-25 5-10 8-20 10-30 5-27 3-55 3-82v-94-49l-1-30v-10-4-1-8c1-1 0-3 0-4v-16c0-17 0-35-3-52-2-12-6-23-11-34-1-3-2-3 0-5-1-1-1 0-2 0-1-1-2-3-3-4l-6-9c-5-6-11-12-18-16-10-7-22-10-34-9-10 0-21 3-30 8-7 4-14 10-19 15-12 13-17 33-18 50-1 6-1 11-2 16-1 2-1 4-2 6 0 2-3 4-5 5 6-13 5-27 7-40 2-19 14-38 29-50 6-4 13-7 19-10 15-6 33-6 47 0 17 8 29 21 38 37 3 4 5 9 7 13 2 6 3 11 5 17 4 20 4 41 5 62v29h0-3 0c2 0 2 0 3 1l2 152-3-3c1 2 1 2 2 3 1 5 1 10 1 14v28l-2 48-1 13c-1 14-4 28-8 41-5 12-12 22-19 32-5 6-11 9-18 13-5 2-10 5-16 6-4 2-9 2-14 3 3 1 5 2 9 2 23 2 47 0 71 0l49 1 37-1h30 15c4 0 8 0 11-1h1c-1-1-1-2-2-2-5-2-10-2-15-4-19-7-35-22-45-39-4-6-6-13-8-19-3-10-6-20-7-31-1-13-1-27-2-41l1-112v-99c0-25-1-51 1-76l3-17c1-4 1-9 2-14 1-3 2-6 4-9-3 20-5 40-6 60v67l-1 59v28 68 52c1 19 3 38 6 55 3 13 9 25 17 36 11 16 29 27 47 32 3 1 7 1 10 2 1 0 0 9 0 10l-49-1h-44l-117-1-32-1c-1 0-7 0-8-1 0-1 0-2 1-2h5 9c-3-1-7-1-10-1 0-1 2-2 3-2l4-2z" class="S"></path><path d="M118 394h0l1-1c1 0 2 0 2-1l3-3h0c-1 2-3 3-3 6h-1l1 1c0 2 0 2-1 3v1h1 0l1 1 1-1h0c0 3-2 7-1 11h0l-1 9c0 2 0 3-1 4-1 4-3 6-5 10h1l-3 6c-1 2-2 4-3 5 0 1-1 2-1 3l-3 9c1 2 0 4 0 7l-1 2c-1 1-1 2-2 3h0 0v1c1 1 1 3 1 4 1 2 1 5 2 7 0 1 0 3 1 4h0l2 3v3s0 5-1 5c0 7-2 14-1 22 0 7 4 16 9 22l1-1c1 2 3 5 5 6 2 3 6 5 6 8l2 2c1 1 3 2 4 3 2 1 3 2 4 2 1 1 2 1 3 2h0c1 1 3 2 4 2 6 3 13 6 19 8 4 1 8 1 12 2 10 1 20-1 29-5l1 1c1-1 2-2 4-2h3v-1c2-1 4-3 6-5h0l3-3c4-4 7-11 9-17v3h0v1h0l1-1h1v-3h1l1 1c1 2 0 5 2 7v1h0l1-3h0l2 2v-2l1-1v1c-1 13-7 24-17 31-4 4-10 7-16 9-1 1-3 2-4 2h-1 2c2 0 4-1 6-1 8-1 14-3 20-7l1 1c-3 2-7 4-11 5 1 1 2 1 2 1-11 6-24 8-37 8h-13-1c1 1 3 1 4 1h10 4c-1 1-3 0-4 0h-10v1h-1c-2 0-6-1-8 1h0c-4 0-7-1-11-2-3-1-7-2-10-3-10-4-19-10-26-17l-1-1-4-4h-1c-1 0-2 0-3-1-4-3-7-7-10-11 0-1-2-2-3-4v2l3 5c-1 0-2-1-3-2v1c-2-1-3-3-4-4 0-1-1-2-1-3l-1 1-3-7h-1c-3-4-4-11-5-15-2-6-3-12-4-18v-8c1-2 1-4 0-5 0-2 0-6 1-8-1-1-1 0-1-1v-1-2h-1c0-2 0-3 1-4 0-5 2-10 3-14v-1h0c-1-1-1-1-1-2 1-2 1-3 1-5l-2 2-1-1h0v-7-1h-2l-1-1 1-1h-1c-1 1-2 4-4 4v-4h0c0-2 1-2 2-3l-1-1-1 1h0l1-1c-1-1-1-2-1-4l-1 2v-2c1-12 4-22 12-32 1-2 4-4 4-6h1v2c3-1 7-5 9-7 2-1 4-2 5-3 1 0 2-1 2-1h1c2 0 4-2 5-3 2-2 6-5 8-5z" class="Q"></path><path d="M108 550c-1 0-1-1-2-2 1 0 3 1 4 2v1l-2-1z" class="B"></path><path d="M84 487c1 1 2 1 3 1l-1 5c-1-2-1-4-2-6z" class="L"></path><path d="M113 430l3-3h1c-2 3-4 6-6 8h-1c0-2 2-4 3-5z" class="F"></path><path d="M121 420c0 2 0 3-1 4v-1c-1 2-2 3-3 4h-1l-3 3h-2c2-2 4-4 5-6h0c1-1 2-1 3-2l2-2zm-36 94h0v-4c-1-3-1-10 1-12 1 1 1 5 1 7h0-1c0 4 2 10 1 14l-1-5h0-1z" class="G"></path><path d="M99 450v1c-2 3-4 7-6 10-1 3-1 6-3 9h-1v-1h-1c-1-1 0-2-1-3 0-1 0-1 1-1v1h0c1-1 1-2 2-3 2-4 4-7 6-11 1-1 2-2 3-2z" class="T"></path><path d="M115 434h1l-3 6c-1 2-2 4-3 5 0 1-1 2-1 3s-1 1-1 1c-2 2-3 5-4 7h-1l-2 2h-1c1-4 3-7 5-11 2-3 3-5 6-8l4-5z" class="B"></path><path d="M115 434h1l-3 6c-1 2-2 4-3 5 0 1-1 2-1 3s-1 1-1 1c-2 2-3 5-4 7h-1c2-3 2-6 4-8 2-3 3-6 4-9l4-5z" class="K"></path><path d="M116 424c-1 2-3 4-5 6h2c-1 1-3 3-3 5h1c-3 3-4 5-6 8l-6 8v-1c-1 0-2 1-3 2h0c1-2 2-4 2-5v-1c2-2 4-5 6-7l1-3c1 0 1-1 2-2h0v-1c0-1 3-4 4-5s3-3 5-4z" class="R"></path><path d="M111 430h2c-1 1-3 3-3 5h-1-1v-1c1-1 2-3 3-4z" class="B"></path><path d="M104 439h0l3-2h0c-1 2-2 3-3 4-2 3-4 6-5 9-1 0-2 1-3 2h0c1-2 2-4 2-5v-1c2-2 4-5 6-7z" class="F"></path><defs><linearGradient id="AN" x1="82.515" y1="481.013" x2="88.698" y2="478.218" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#626162"></stop></linearGradient></defs><path fill="url(#AN)" d="M86 467s1 0 1-1c1 1 0 2 1 3h1v1h1l-1 5s0 1-1 2l-1 11c-1 0-2 0-3-1 0-4-2-8 0-13v-1h-1l-1 1v-1l3-6h1z"></path><path d="M86 467s1 0 1-1c1 1 0 2 1 3h1v1h1l-1 5s0 1-1 2v-1c0-1 0-1-1-1v-2h-1v2l-1-2-1 1h0v-1h-1l-1 1v-1l3-6h1z" class="K"></path><path d="M89 475c-1-1 0-1-1-1v-2c-1-1-1-2-1-3h1v2l1-1h1l-1 5z" class="R"></path><path d="M85 467l1 1v2c-1 1-1 2-2 4h0v-1h-1l-1 1v-1l3-6z" class="F"></path><path d="M114 560l1-1c3 3 7 6 10 8 4 3 7 5 11 7 1 1 2 1 3 2 6 3 13 6 20 8 2-1 4 0 6 0h0-1l2 2c2 1 4 1 6 2h3l1 1h-1l-6-1c-3-1-8-2-11-2s-8-3-11-4c-11-6-24-13-33-22z" class="K"></path><path d="M159 584c2-1 4 0 6 0h0-1l2 2c-3 0-5-1-7-2z" class="H"></path><path d="M109 427h1 0s-1 1 0 2l1-1c-1 1-4 4-4 5v1h0c-1 1-1 2-2 2l-1 3c-2 2-4 5-6 7h-2l-1-1v-1h-1l1-1-1-1c1-1 2-2 3-2 0-1 0-1-1-1h-1c0-1 1-1 1-1l4-4h2c2-3 5-4 7-7z" class="I"></path><path d="M100 434h2c-1 1-3 4-4 4h-1-1l4-4z" class="D"></path><path d="M97 440l1-1 3-2c-1 2-3 5-6 6l-1-1c1-1 2-2 3-2z" class="E"></path><path d="M95 445c2-1 4-3 5-4 2-2 3-4 5-5l-1 3c-2 2-4 5-6 7h-2l-1-1z" class="G"></path><path d="M85 514h1 0l1 5c1-4-1-10-1-14h1 0c1 9 3 19 7 27 3 7 7 13 10 19-1-1-3-2-4-4 0 0-1-1-1-2l-1-1c0-1-1-2-2-2h0c-2-2-3-5-4-7-3-7-5-13-7-21z" class="M"></path><path d="M108 550l2 1v-1l3 2 14 10c1 1 3 2 4 3 1 0 2 0 3 1h0c1 0 2 0 3 1h-3-1c1 2 4 4 6 5 1 2 3 3 5 4-2 0-4-1-5 0-1-1-2-1-3-2-4-2-7-4-11-7-3-2-7-5-10-8l-1 1c-2-2-5-7-6-10z" class="B"></path><defs><linearGradient id="AO" x1="127.161" y1="566.961" x2="141.918" y2="572.119" xlink:href="#B"><stop offset="0" stop-color="#000001"></stop><stop offset="1" stop-color="#242424"></stop></linearGradient></defs><path fill="url(#AO)" d="M136 574c0-2-2-3-4-4-2-2-5-4-7-7l5 2h1c1 0 2 0 3 1h0c1 0 2 0 3 1h-3-1c1 2 4 4 6 5 1 2 3 3 5 4-2 0-4-1-5 0-1-1-2-1-3-2z"></path><path d="M108 550l2 1v-1l3 2c0 1 0 2 1 2v2h1c0 1 1 1 1 1l4 3-1 1c2 1 4 3 5 4 1 0 1 1 2 1l-1 1c-3-2-7-5-10-8l-1 1c-2-2-5-7-6-10z" class="E"></path><path d="M110 551v-1l3 2c0 1 0 2 1 2v2h1c0 1 1 1 1 1l4 3-1 1h0c-3-2-7-6-9-10z" class="I"></path><path d="M96 542h0c1 0 2 1 2 2l1 1c0 1 1 2 1 2 1 2 3 3 4 4 2 3 3 5 5 7 2 3 5 5 8 8-2-1-3-1-4-2l-3-3 5 6-1 1h1l1 1h-1-1-1l3 3h1c0 1 1 1 1 1 1 1 4 4 4 5v1c-1 0-1 0-2-1h0l-1-1-4-4h-1c-1 0-2 0-3-1-4-3-7-7-10-11 0-1-2-2-3-4l-1-1h0l-3-3c-1 0-2-2-3-3l1-1h1c-1-1-2-3-1-5v1l1 1v1l2 2c1 1 1 1 1 2l2 2v1l-1 1 2 2h0c1 0 1 0 2 1h0l1-1h1l-3-3v-1c1 1 4 3 4 4 2 1 4 3 6 4l-7-7c-3-4-5-8-7-12z" class="K"></path><path d="M111 572v-2c1 1 3 2 4 3h-1c-1 0-2 0-3-1z" class="M"></path><path d="M100 553c1 1 4 3 4 4s1 1 1 2c1 2 3 2 3 4l-1 1c-2 0-3-2-5-3 0-1-1-2-1-3l1-1h1l-3-3v-1z" class="R"></path><path d="M122 401l1-1h0c0 3-2 7-1 11h0l-1 9-2 2c-1 1-2 1-3 2h0c-2 1-4 3-5 4l-1 1c-1-1 0-2 0-2h0-1c-2 3-5 4-7 7h-2l2-4h-1l-1-1h0c1-1 3-3 3-4h0l4-6 3-3c3-3 6-5 9-9 1-1 2-2 2-3s1-2 1-3z" class="G"></path><path d="M117 416c1-2 1-3 3-4h1c-1 1-1 3-2 4h-2z" class="C"></path><path d="M121 412l1-1-1 9-2 2c-1 1-2 1-3 2h0c-2 1-4 3-5 4l-1 1c-1-1 0-2 0-2h0-1l4-6 4-5h2c1-1 1-3 2-4z" class="H"></path><path d="M117 416h2c-3 4-6 7-9 11h0-1l4-6 4-5z" class="K"></path><path d="M112 418c3-3 5-7 8-9h0l-9 11c1 0 2-1 3-1h0c-1 1-1 1-1 2l-4 6c-2 3-5 4-7 7h-2l2-4h-1l-1-1h0c1-1 3-3 3-4h0l4-6 3-3 1 1h0 0l1 1z" class="M"></path><path d="M107 419l3-3 1 1h0 0l1 1c-1 2-4 3-6 5-1 1-2 1-3 2h0l4-6z" class="B"></path><path d="M111 420c1 0 2-1 3-1h0c-1 1-1 1-1 2l-4 6c-2 3-5 4-7 7h-2l2-4 9-10z" class="H"></path><defs><linearGradient id="AP" x1="93.476" y1="459.29" x2="84.51" y2="457.261" xlink:href="#B"><stop offset="0" stop-color="#3f3f3f"></stop><stop offset="1" stop-color="#5c5b5b"></stop></linearGradient></defs><path fill="url(#AP)" d="M94 442l1 1-1 1h1v1l1 1h2v1c0 1-1 3-2 5h0c-2 4-4 7-6 11-1 1-1 2-2 3h0v-1c-1 0-1 0-1 1s-1 1-1 1h-1l-3 6h0c-1-1-1-1-1-2 1-2 1-3 1-5l-2 2-1-1h0v-7-1h-2l-1-1 1-1h-1l6-5-1-1c2-1 3-2 5-3 1 0 2-1 3-1 0-1 3-3 4-4h0l1-1z"></path><path d="M98 447c0 1-1 3-2 5v-1h0c1-2 0-3 0-5 1 0 2 1 2 1z" class="I"></path><path d="M86 467v-2c0-2 1-2 2-4l2 2c-1 1-1 2-2 3h0v-1c-1 0-1 0-1 1s-1 1-1 1z" class="F"></path><path d="M92 448c-1 2-3 3-4 5s-1 3-4 5c1-2 2-5 3-7 1 0 2 0 3-1 0-1 1-1 2-2z" class="C"></path><path d="M94 442l1 1-1 1h1v1l1 1h2v1s-1-1-2-1-1 1-2 1c-1 1-2 1-2 1-1 1-2 1-2 2-1 1-2 1-3 1 1-1 2-1 2-2v-2c0-1 3-3 4-4h0l1-1z" class="B"></path><path d="M89 447v2c0 1-1 1-2 2-1 2-2 5-3 7l-1 6-1 2-2 2-1-1h0v-7-1h-2l-1-1 1-1h-1l6-5-1-1c2-1 3-2 5-3 1 0 2-1 3-1z" class="C"></path><path d="M81 463c1 0 1 0 2 1l-1 2-2 2-1-1 2-4z" class="D"></path><path d="M77 457c1 0 2-1 4-2 0 2-1 4-2 5h0v-1h-2l-1-1 1-1z" class="N"></path><path d="M89 447v2c0 1-1 1-2 2-1 2-2 5-3 7l-1 6c-1-1-1-1-2-1 1-5 3-9 6-13-2 0-4 1-5 2h0l-1-1c2-1 3-2 5-3 1 0 2-1 3-1z" class="G"></path><defs><linearGradient id="AQ" x1="100.654" y1="520.571" x2="77.91" y2="540.015" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#59595a"></stop></linearGradient></defs><path fill="url(#AQ)" d="M80 496c1 1 1 5 1 6 2 13 3 26 9 37 3 5 7 9 10 14v1l3 3h-1l-1 1h0c-1-1-1-1-2-1h0l-2-2 1-1v-1l-2-2c0-1 0-1-1-2l-2-2v-1l-1-1v-1c-1 2 0 4 1 5h-1l-1 1c1 1 2 3 3 3l3 3h0l1 1v2l3 5c-1 0-2-1-3-2v1c-2-1-3-3-4-4 0-1-1-2-1-3l-1 1-3-7h-1c-3-4-4-11-5-15-2-6-3-12-4-18v-8c1-2 1-4 0-5 0-2 0-6 1-8z"></path><path d="M107 419h0l-4 6h0c0 1-2 3-3 4h0l1 1h1l-2 4-4 4s-1 0-1 1h1c1 0 1 0 1 1-1 0-2 1-3 2l-1 1h0c-1 1-4 3-4 4-1 0-2 1-3 1-2 1-3 2-5 3l1 1-6 5c-1 1-2 4-4 4v-4h0c0-2 1-2 2-3l-1-1-1 1h0l1-1c-1-1-1-2-1-4v-1c2-1 2-2 3-3s1-2 1-2c3-6 7-10 12-13h1c1 1 4-4 6-4v1c1 1 1 1 2 1 4-3 7-6 10-9z" class="F"></path><path d="M93 429l2-2c1 1 1 1 2 1-2 3-4 5-7 8-1-1 3-4 3-5v-2z" class="E"></path><path d="M93 429v2c0 1-4 4-3 5-1 1-3 2-5 4v-2s1-2 2-2c2-3 3-5 6-7z" class="L"></path><path d="M83 448v-1c0-3 4-7 6-9 0-1 1-1 2-2 1 0 1-1 3 0-4 4-7 9-11 12z" class="G"></path><path d="M100 429l1 1h1l-2 4-4 4s-1 0-1 1h1c1 0 1 0 1 1-1 0-2 1-3 2l-1 1h0c-1 1-4 3-4 4-1 0-2 1-3 1-2 1-3 2-5 3l1 1-6 5c-1 1-2 4-4 4v-4h0c0-2 1-2 2-3l7-5c1 0 1 0 2-1 4-3 7-8 11-12h0c0-1 1-1 1-2 1-2 4-4 5-5z" class="V"></path><path d="M72 457c3-3 6-4 9-6l1 1-6 5c-1 1-2 4-4 4v-4z" class="O"></path><path d="M101 430h1l-2 4-4 4s-1 0-1 1h1c1 0 1 0 1 1-1 0-2 1-3 2l-1 1h0v-1c-2 0-4 2-6 3 3-3 5-6 9-9l5-6zm-12 0c1 1 4-4 6-4v1l-2 2c-3 2-4 4-6 7-1 0-2 2-2 2v2c-1 1-2 2-3 4l-1 5-7 5-1-1-1 1h0l1-1c-1-1-1-2-1-4v-1c2-1 2-2 3-3s1-2 1-2c3-6 7-10 12-13h1z" class="F"></path><path d="M85 438v2c-1 1-2 2-3 4v-2l1-1c-1 0-1 1-2 1 1-1 2-3 4-4z" class="J"></path><path d="M81 442c1 0 1-1 2-1l-1 1v2l-1 5-7 5-1-1c2-4 5-7 8-11z" class="Q"></path><path d="M76 443c3-6 7-10 12-13h1l-5 7c-2 2-4 3-5 5-3 3-5 7-6 11-1-1-1-2-1-4v-1c2-1 2-2 3-3s1-2 1-2z" class="D"></path><defs><linearGradient id="AR" x1="140.573" y1="594.771" x2="144.372" y2="573.448" xlink:href="#B"><stop offset="0" stop-color="#444445"></stop><stop offset="1" stop-color="#646364"></stop></linearGradient></defs><path fill="url(#AR)" d="M120 578h0c1 1 1 1 2 1v-1c0-1-3-4-4-5 0 0-1 0-1-1h-1l-3-3h1 1 1l-1-1h-1l1-1 16 12c7 5 17 9 26 12 3 1 7 2 10 3l8 1c2 0 4 0 5 1s4 1 6 1h-13-1c1 1 3 1 4 1h10 4c-1 1-3 0-4 0h-10v1h-1c-2 0-6-1-8 1h0c-4 0-7-1-11-2-3-1-7-2-10-3-10-4-19-10-26-17z"></path><defs><linearGradient id="AS" x1="147.123" y1="592.228" x2="146.33" y2="585.71" xlink:href="#B"><stop offset="0" stop-color="#231f23"></stop><stop offset="1" stop-color="#2b2f2c"></stop></linearGradient></defs><path fill="url(#AS)" d="M134 583c3 0 5 2 7 3l10 4c3 2 7 2 10 4-3 0-7-1-10-2l-8-3c-2-1-4-3-6-4-1 0-2-1-2-1l-1-1z"></path><path d="M104 456c1-2 2-5 4-7 0 0 1 0 1-1l-3 9c1 2 0 4 0 7l-1 2c-1 1-1 2-2 3h0 0c-1 1-1 2-1 3l-1 1c-1 1-1 4-2 6-1 7-3 15-3 22 0 5 1 11 2 15 0 1 0 3 1 4l1-1 1 1c0 3 2 6 3 8s1 4 2 5c1 4 4 7 6 10h1l8 9c0 2 1 3 3 4 1 0 3 2 3 2 2 1 5 3 6 4h0c-9-4-16-10-23-16l-7-7c-3-5-5-10-7-15-6-11-6-23-4-36 0-5 1-10 3-15 1-5 3-10 5-15h1l2-2h1z" class="M"></path><path d="M105 459c1-1 1-1 1-2 1 2 0 4 0 7l-1 2c-1 1-1 2-2 3h0c0-4 1-7 2-10z" class="I"></path><path d="M104 456c1-2 2-5 4-7 0 0 1 0 1-1l-3 9c0 1 0 1-1 2v-1c-1 1-1 3-2 4h0c-1-3 0-4 1-6z" class="C"></path><path d="M99 520l1-1 1 1c0 3 2 6 3 8s1 4 2 5c1 4 4 7 6 10h0c-4-2-9-10-10-14-1-3-1-6-3-9z" class="P"></path><path d="M100 458h1v1 3c-1 2-2 6-3 9l-2 5v2 1h0c-1 1-1 1-1 2v1h0v2c-1 1-1 2-1 3v2c-1 1 0 3-1 4v4 8 3 1h0 0c-1-1 0-2-1-3v-4c0-4 0-9 1-13v-2l3-14h-1c1-5 3-10 5-15z" class="C"></path><path d="M103 469v1c1 1 1 3 1 4 1 2 1 5 2 7 0 1 0 3 1 4h0l2 3v3s0 5-1 5c0 7-2 14-1 22 0 7 4 16 9 22l1-1c1 2 3 5 5 6 2 3 6 5 6 8l2 2c1 1 3 2 4 3h-1l-3-2-5-4v1 1 1c-2-1-3-2-4-3l-8-9h-1c-2-3-5-6-6-10-1-1-1-3-2-5s-3-5-3-8l-1-1-1 1c-1-1-1-3-1-4-1-4-2-10-2-15 0-7 2-15 3-22 1-2 1-5 2-6l1-1c0-1 0-2 1-3z" class="K"></path><path d="M107 485h0l2 3v3s0 5-1 5v-1h-3c1-1 1-2 1-3 1-2 1-5 1-7z" class="G"></path><defs><linearGradient id="AT" x1="112.534" y1="540.369" x2="124.449" y2="553.769" xlink:href="#B"><stop offset="0" stop-color="#040505"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#AT)" d="M113 543c0-1-1-2-2-3-1-2-3-4-4-7-1-1-1-3-1-5 2 4 4 9 7 12l1 1 1-1 4 4-3-4 1-1c1 2 3 5 5 6 2 3 6 5 6 8l2 2c1 1 3 2 4 3h-1l-3-2-5-4v1 1 1c-2-1-3-2-4-3l-8-9z"></path><path d="M117 539c1 2 3 5 5 6 2 3 6 5 6 8l2 2c1 1 3 2 4 3h-1l-3-2-5-4c-4-4-8-7-11-11l1-1 4 4-3-4 1-1z" class="C"></path><path d="M105 495h3v1c0 7-2 14-1 22 0 7 4 16 9 22l3 4-4-4-5-7c-4-6-6-12-7-19v-7c1-2 1-4 1-6s1-4 1-6z" class="P"></path><path d="M103 469v1c0 3 1 5 1 7l1 9c0 1 1 2 1 3h-1c0-1-1-3-1-5 0-1 0-1-1-2h0c-1 3 1 6 1 9h0c-1 0-1-1-1-2h-1l-1 11v6c-1 2-1 5-1 8 1 1 1 2 1 3v3l-1-1-1 1c-1-1-1-3-1-4-1-4-2-10-2-15 0-7 2-15 3-22 1-2 1-5 2-6l1-1c0-1 0-2 1-3z" class="C"></path><path d="M101 517h-1c-1-3-1-5-1-8 0-2 1-4 1-6v-6c0-1 1-1 1-2v-6h1l-1 11v6c-1 2-1 5-1 8 1 1 1 2 1 3z" class="L"></path><defs><linearGradient id="AU" x1="82.847" y1="451.179" x2="86.372" y2="410.602" xlink:href="#B"><stop offset="0" stop-color="#1a1a1b"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#AU)" d="M118 394h0l1-1c1 0 2 0 2-1l3-3h0c-1 2-3 3-3 6h-1l1 1c0 2 0 2-1 3v1h1 0l1 1c0 1-1 2-1 3s-1 2-2 3c-3 4-6 6-9 9l-3 3h0c-3 3-6 6-10 9-1 0-1 0-2-1v-1c-2 0-5 5-6 4h-1c-5 3-9 7-12 13 0 0 0 1-1 2s-1 2-3 3v1l-1 2v-2c1-12 4-22 12-32 1-2 4-4 4-6h1v2c3-1 7-5 9-7 2-1 4-2 5-3 1 0 2-1 2-1h1c2 0 4-2 5-3 2-2 6-5 8-5z"></path><path d="M98 415c2-1 3-2 4-3h2l-10 8c-2 1-3 2-5 3l1-1h0l8-7z" class="T"></path><path d="M110 399c2-2 6-5 8-5-4 5-9 10-15 13-2 1-3 2-5 3s-3 2-5 3h-2-1l15-11c2 0 4-2 5-3z" class="Q"></path><path d="M118 394h0l1-1c1 0 2 0 2-1l3-3h0c-1 2-3 3-3 6h-1c-3 4-6 7-9 11-2 2-4 5-7 6h0-2c-1 1-2 2-4 3l1-1h-1c-3 2-5 4-8 7-1-1-1 0-1-1 2-1 3-2 4-3 2-2 5-4 7-6 1-1 2-3 3-4 6-3 11-8 15-13z" class="M"></path><path d="M111 406c3-4 6-7 9-11l1 1c0 2 0 2-1 3v1h1 0l1 1c0 1-1 2-1 3s-1 2-2 3c-3 4-6 6-9 9l-3 3h0c-3 3-6 6-10 9-1 0-1 0-2-1v-1c-2 0-5 5-6 4h-1c-5 3-9 7-12 13v-1c1-4 5-9 9-11 1-2 4-3 5-5v-1c1 0 1 0 2-1h0l3-3h-1s-1 1-2 1c1-1 2-1 2-2l10-8h0c3-1 5-4 7-6z" class="C"></path><path d="M92 424c1 0 1-1 2-1 2-2 4-4 6-5h0c1-1 2 0 2 0h1l-1 1c-5 1-7 7-12 7v-1c1 0 1 0 2-1z" class="B"></path><defs><linearGradient id="AV" x1="119.689" y1="396.269" x2="113.698" y2="404.632" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#2a2a2b"></stop></linearGradient></defs><path fill="url(#AV)" d="M111 406c3-4 6-7 9-11l1 1c0 2 0 2-1 3-3 3-5 7-8 10 0 0-1 2-3 2v1l-1-1c1-2 5-4 5-6h0l-2 1z"></path><path d="M120 400h1 0l1 1c0 1-1 2-1 3s-1 2-2 3c-3 4-6 6-9 9l-3 3h0c-3 3-6 6-10 9-1 0-1 0-2-1v-1c1 0 2-1 3-2 3-2 5-4 7-7 3-2 5-4 8-7 2-3 5-6 7-10z" class="D"></path><path d="M121 400l1 1c0 1-1 2-1 3s-1 2-2 3c-3 4-6 6-9 9l-3 3h0c3-5 8-9 11-14 1-1 3-3 3-5z" class="K"></path><defs><linearGradient id="AW" x1="142.616" y1="561.589" x2="199.231" y2="586.625" xlink:href="#B"><stop offset="0" stop-color="#363535"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#AW)" d="M121 552c1 1 2 2 4 3v-1-1-1l5 4 3 2h1c2 1 3 2 4 2 1 1 2 1 3 2h0c1 1 3 2 4 2 6 3 13 6 19 8 4 1 8 1 12 2 10 1 20-1 29-5l1 1c1-1 2-2 4-2h3v1l2-2v1l-1 1h1c-1 1-1 1-1 2l1-1h1c1 0 3-2 4-2-1 1-3 2-3 3h0v1c-6 4-11 6-17 8-12 4-23 6-35 4h0c-2 0-4-1-6 0-7-2-14-5-20-8 1-1 3 0 5 0-2-1-4-2-5-4-2-1-5-3-6-5h1 3s1 0 2-1c-2-2-4-2-6-3v-1h0c-1-1-4-3-6-4 0 0-2-2-3-2-2-1-3-2-3-4z"></path><path d="M193 575h2c-1 0-1 1-2 1h-5 1l-2-1h6z" class="C"></path><path d="M213 568v1l-6 3h0c-5 2-8 4-14 4 1 0 1-1 2-1h-2c4-1 7-3 10-4 2 0 2-1 3-1 1-1 2-2 4-2h3z" class="F"></path><path d="M205 569l1 1c-1 0-1 1-3 1-3 1-6 3-10 4h-6c-3 1-5 1-7 1h-5c-2 0-4 1-6 0h2 0-1v-1h3c1-1 2-1 3-1 10 1 20-1 29-5z" class="R"></path><path d="M142 570c1 0 3 1 4 1 2 0 4 0 6 1l8 3h3c1 1 2 1 4 1 1 0 1 0 2-1h1v1h-4c-2 1-3 1-4 1h-1c1 1 2 1 2 1v1l-9-3c-4-1-8-4-12-6h0 0z" class="M"></path><path d="M213 569l2-2v1l-1 1h1c-1 1-1 1-1 2l1-1h1c1 0 3-2 4-2-1 1-3 2-3 3h0v1c-6 4-11 6-17 8-12 4-23 6-35 4h0c-2 0-4-1-6 0-7-2-14-5-20-8 1-1 3 0 5 0 3 2 7 3 11 4a108.75 108.75 0 0 0 36 0c3 0 7-1 9-2 1-1 2-2 3-2h1 0 0v-1h1c0-1 1-1 1-1l1-1v-1h0l6-3z" class="Q"></path><path d="M213 569l2-2v1l-1 1h1c-1 1-1 1-1 2-3 2-10 6-14 7 1-1 2-2 3-2h1 0 0v-1h1c0-1 1-1 1-1l1-1v-1h0l6-3z" class="K"></path><path d="M121 552c1 1 2 2 4 3v-1-1-1l5 4 3 2h1c2 1 3 2 4 2 1 1 2 1 3 2h0c1 1 3 2 4 2 6 3 13 6 19 8 4 1 8 1 12 2-1 0-2 0-3 1h-3-1c-1 1-1 1-2 1-2 0-3 0-4-1h-3c-3-1-5-2-8-3-2-1-4-1-6-1-1 0-3-1-4-1l-1-1c-1 1-2 1-2 2v1c-2-1-5-3-6-5h1 3s1 0 2-1c-2-2-4-2-6-3v-1h0c-1-1-4-3-6-4 0 0-2-2-3-2-2-1-3-2-3-4z" class="C"></path><path d="M139 572c-2-1-5-3-6-5h1c2 1 4 0 6 0 1 0 2 1 3 1 2 1 4 1 7 2v-1c3 0 6 2 9 2 2 1 5 2 7 2-1 1-3 1-4 0-3 0-6-2-9-2l1 1 6 2v1l-8-3c-2-1-4-1-6-1-1 0-3-1-4-1l-1-1c-1 1-2 1-2 2v1z" class="G"></path><path d="M121 552c1 1 2 2 4 3v-1-1-1l5 4 3 2h1c2 1 3 2 4 2 1 1 2 1 3 2h0l-1 1c3 2 6 5 10 6v1c-3-1-5-1-7-2-1 0-2-1-3-1-2 0-4 1-6 0h3s1 0 2-1c-2-2-4-2-6-3v-1h0c-1-1-4-3-6-4 0 0-2-2-3-2-2-1-3-2-3-4z" class="D"></path><path d="M130 556l3 2h1c2 1 3 2 4 2 1 1 2 1 3 2h0l-1 1-7-3c0-1 0-1-1-1 0-1-1-1-2-2v-1z" class="I"></path><path d="M121 552c1 1 2 2 4 3v-1-1-1l5 4v1c1 1 2 1 2 2 1 0 1 0 1 1l-4-2-1-1-1 1s-2-2-3-2c-2-1-3-2-3-4z" class="E"></path><defs><linearGradient id="AX" x1="193.817" y1="558.26" x2="217.824" y2="584.299" xlink:href="#B"><stop offset="0" stop-color="#323131"></stop><stop offset="1" stop-color="#535354"></stop></linearGradient></defs><path fill="url(#AX)" d="M233 545v-3h1l1 1c1 2 0 5 2 7v1h0l1-3h0l2 2v-2l1-1v1c-1 13-7 24-17 31-4 4-10 7-16 9-1 1-3 2-4 2h-1 2c2 0 4-1 6-1 8-1 14-3 20-7l1 1c-3 2-7 4-11 5-5 2-11 3-16 4-3 0-7 0-9 1-3 1-9 2-11 1h-1 0 1c3 0 8-1 12-3-5 1-12 2-17 1h0c-1 0-3 0-4-1-6 0-13-2-18-5 3 0 8 1 11 2l6 1h1l-1-1h-3c-2-1-4-1-6-2l-2-2h1c12 2 23 0 35-4 6-2 11-4 17-8v-1h0c0-1 2-2 3-3-1 0-3 2-4 2h-1l-1 1c0-1 0-1 1-2h-1l1-1v-1l-2 2v-1-1c2-1 4-3 6-5h0l3-3c4-4 7-11 9-17v3h0v1h0l1-1h1z"></path><path d="M172 588c2 0 5-1 7 1h0-2-2 1l-1-1h-3z" class="M"></path><path d="M226 563h0c-1 2-3 3-4 4-2 2-3 4-5 4h0c0-1 2-2 3-3-1 0-3 2-4 2h-1c1-1 2-2 4-2 1-2 2-3 4-4 1 0 2-1 3-1z" class="B"></path><path d="M197 588l-5-2 6-1c1 0 4 0 6-1h1l-1 1h3c-3 1-7 3-10 3z" class="T"></path><path d="M212 583c4-2 7-5 11-8-1 1-1 2-2 2h1l1-1h1l-4 4c-2 2-4 3-7 4l-1-1z" class="L"></path><path d="M217 572h0c-1 3-6 5-8 6-1 1-2 1-4 2-1 1-3 0-4 1-1 0-1 0-1-1 6-2 11-4 17-8z" class="M"></path><path d="M177 589c1 1 3 1 5 1 5 0 10-1 15-2l1 1-10 2c-4 0-9 0-14-1-2-1-3-1-5-1v-1l6 1h2z" class="Q"></path><defs><linearGradient id="AY" x1="203.68" y1="590.091" x2="206.198" y2="582.339" xlink:href="#B"><stop offset="0" stop-color="#0e0a11"></stop><stop offset="1" stop-color="#242722"></stop></linearGradient></defs><path fill="url(#AY)" d="M207 585c2 0 3-1 5-2l1 1c-5 3-9 4-15 5l-1-1h0c3 0 7-2 10-3z"></path><path d="M158 586c3 0 8 1 11 2v1c2 0 3 0 5 1 5 1 10 1 14 1-2 0-6-1-8 1h0c-1 0-3 0-4-1-6 0-13-2-18-5z" class="E"></path><defs><linearGradient id="AZ" x1="228.294" y1="546.09" x2="233.749" y2="554.019" xlink:href="#B"><stop offset="0" stop-color="#3f3e40"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#AZ)" d="M233 545v-3h1l1 1v1c-1 1 0 2 0 3v1 1c-1 2-3 7-5 8l-2 2c-1-3 5-9 5-12v-2z"></path><path d="M222 559c4-4 7-11 9-17v3h0v1h0l1-1h1v2c0 3-6 9-5 12 0 1-2 2-2 4-1 0-2 1-3 1-2 1-3 2-4 4-2 0-3 1-4 2l-1 1c0-1 0-1 1-2h-1l1-1v-1l-2 2v-1-1c2-1 4-3 6-5h0l3-3z" class="M"></path><path d="M215 569c5-4 10-9 13-15h1c0 3-5 7-6 10-2 1-3 2-4 4-2 0-3 1-4 2l-1 1c0-1 0-1 1-2z" class="T"></path><path d="M298 207c-1-1-2-2-3-2-1-2 0-4-1-5 0-8 2-15 5-21l4-12c-1 0-3 0-4 1-4 3-8 7-12 10-5 5-11 8-17 13-3 3-7 7-11 10s-8 5-13 8l-10 7c3-9 2-18 4-27 1-14 6-29 15-40 4-5 9-10 14-13 2-2 5-4 8-6 15-8 33-9 50-4 16 6 32 23 39 38 2 4 3 8 5 11 3 9 6 18 7 27 2 11 2 22 2 34l1 27c-4 0-8-1-12 0-8 0-15 2-22 4-3-3-8-6-10-10-4-5-2-11-2-17 0-3-1-6-2-9 10-7 22-12 33-16-2 0-5 1-8 2-9 3-17 7-25 13h-1c-1-1-1-1-1-2 8-8 18-15 28-19l6-3c1 0 3 0 4-1-4 1-9 2-12 4-8 3-14 8-21 13-2 2-4 5-7 6 2-3 5-6 7-9 7-8 14-15 21-22h0c-5 4-10 9-15 14l-4 4c0 1-1 2-2 2-2 1-4 3-5 5v1c0-2 3-5 3-6 1-1 1-2 1-2v-1c2-3 5-7 8-10 5-7 10-14 16-19-3 0-10 9-12 12l-13 17c-2 3-4 7-7 10l-2-2c0-2 2-7 3-9l12-24 9-14h0c-6 7-10 15-15 24-2 3-8 20-10 22-1-1-1-1-1-2-1-1 0-3 0-5 2-13 6-27 11-39h0c-5 9-9 21-10 32l-2 12c-1-1-3-2-3-3-1-3-1-7-2-10-1-11-3-22-2-33v-2c-1 1-1 4-1 5v12c1 9 2 18 4 28h0l-1 1h0l-2-1h-1v-3c-2-1-5-2-7-4l-1 1s-7-3-8-3z" class="X"></path><path d="M314 213c1 1 3 2 4 3h0l-1 1h0l-2-1h-1v-3z" class="D"></path><path d="M317 368l8-28 11-37 2-8c1-2 1-5 3-7h1c1 3 2 6 4 9 5 12 6 26 7 40 1-3 1-6 0-8 0-10-2-22-6-31-1-2-2-5-3-8-1 0-2-2-2-3l1-2c1 0 1 0 2 1l6 9c8 13 14 25 19 39 1 2 2 5 3 7 0-2-1-5-2-6-2-8-5-14-8-21-5-8-9-16-14-24-1-1-2-3-3-4s-2-1-2-2v-1l1-2h1c8 8 16 19 21 29v-1c-4-8-10-16-16-23l-4-4-1-1v-1l1-1c11 2 20 7 30 13v-1c-6-4-13-8-20-10l-10-3c0-1 1-4 2-5l32 7v10c0 1 0 3-1 3v1l1 1v8 37l1 55v62 65c-1 11-3 23-5 33-1 7-4 13-6 19-5 14-13 29-27 38-12 8-27 9-41 8h-1l1-1c0-1-1-3-1-4 0-2-1-7 0-8 0-1 1-2 1-2-4-4-8-5-13-5l9-20c1-1 2-4 3-5l6-18c2-4 3-8 4-12l-1-1c0-1-1-2-1-4 0-1 1-3 1-4 0 1 0 2 2 3 2-10 3-21 1-31-2-19-10-37-24-50-2-2-4-4-7-5 0-4 1-9 2-13 0-6 0-12-1-19v-4h1l4 19c1-4 3-9 3-14 1-2 1-4 1-5v-15c0-1 1-1 1-1l-1-2v-1c1-1 3 0 4-1 0-1-3 0-4 0-1-2-1-6-1-8-1-3-1-5-2-8l1-1 1 2v-2l7 6c0-1-4-4-5-6h0l3 2c8 4 17 5 25 2h-6c-5-1-9-3-14-6 5 0 9 1 13 1 1-4 2-7 2-11z" class="X"></path><path d="M311 536c0 1 0 2 2 3 0 2-1 4-1 6l-1-1c0-1-1-2-1-4 0-1 1-3 1-4z" class="D"></path><path d="M303 605c2 2 3 4 3 8 0 2-2 4-3 6 0-1-1-3-1-4 0-2-1-7 0-8 0-1 1-2 1-2z" class="L"></path><path d="M444 95h1v-1l78 1v8c0 2 1 3 0 5-5 0-11 2-15 4l-9 6c-9 6-16 13-23 22-2 3-5 6-7 10-6 11-11 24-14 36l-3 25-1 30v101 75l-1 9 1 17v4c-1 1-1 1-1 2 1 2 0 56 1 62 0 10 0 20 1 30 3 20 9 37 20 54 2 4 10 10 9 15-1 3-4 5-6 7 2-1 4-3 6-4l1 1c0 2-1 2-2 3-2 2-4 2-5 3 0 1 1 0 1 0 4 1 7 1 9 5 0 1 0 2-1 3 0-1 0-1-1-1-2-2-8-2-11-2-6 1-12 2-17 4-8 1-16 2-24 1 0-1 1-2 1-3-3-1-6-1-8-1h-10-18v-1l-1 1 1 2-1 2c-1 1-4 0-6 0-1 1-3 0-5 0-5 1-11 1-17 0-3 0-9-2-12-1-2 1-2 3-4 3h-1c0-2-1-3 0-4 2-4 8-1 11-2-2-1-4-1-5-4h0c1-1 2-1 3-1h1 0l-1-6c-1-2-1-3 0-5h1v1h1c2-6 6-11 10-16 4-7 9-15 12-23 2-5 3-10 4-16 5-19 4-40 4-59l-1-70 1-40-1-81v-59l-1-34c0-8-1-17-2-25-3-14-9-27-17-38-5-7-10-13-16-18-5-5-11-9-17-13-3-2-7-5-11-6-1 0-5 2-7 2h-5c4-2 8-3 12-5l-7-4c-1 0-2-1-4-1l1 1c1 2 0 4-1 6 0 1-1 2-2 2 0 1-2 0-3 0v-2c-1-1-1-2 0-3 1 0 1 0 2 1h0l1-1v-1c0-1 0-1-1-2s-4-1-6-1v4c0 2-1 4-3 5-2 2-7 2-10 1h0l1-3 1 1c2 0 5 0 7-1 1 0 2-2 2-3s0-2-1-4c-3 0-7 0-10 1h0l-1-1c-2 1-3 2-4 3l4 4c0 1 0 1-1 1 0 1-6 0-7 1s-1 3-1 4h-1-2c-3-1-5 3-7 5l-3 3c-2 2-5 3-7 2-1 0-2-1-2-2s-1-2 0-3c0-2 2-3 3-3 2 0 2 0 3 1-1 2-2 2-3 4l1 1h1c6-1 7-9 8-13 1-2 2-3 2-5-1 1-5 1-6 2-1 0-2 1-2 2-2 2-4 6-6 7-2 2-6 1-8 3-2 3 0 7 0 10s0 6-2 8h-1c0 1 0 2-1 3h-1c-3-4 0-17 1-21 1-2 3-4 3-7h0c-1 0-2 0-3 1-6 3-2 10-4 15v4c1 4 1 8 0 12-1 2-4 5-6 6-1 0 0-1-1-2l-2 2h-3c-1-1-2-2-2-4 0-1 3-1 4-1 2-1 3-2 4-3 2-3 2-6 1-10-3 2-6 3-10 3v1c3 0 6-1 8-2h1v1l-2 2c-2 2-4 3-7 3s-5-1-7-3l-1-1v-1s-1 0-1-1c0-2 1-4 2-5 1-2 4-4 6-4s3 0 5 1c0 1 1 2 1 2 0 1-1 1-1 2-1 1-2 1-3 1l-2-2c-1 0-1-1-2 0-1 0-2 2-2 3 0 0 0 1 1 2 0 1 2 1 3 1 1-1 2-1 4-2 6-3 8-12 10-19-17 8-34 18-46 32-1 2-2 3-3 4-2 3-4 6-6 10h1c2-1 5-1 7 0 1 1 2 2 2 4 0 3 0 6-2 8-2 1-5 4-8 4-1 0-2 0-3-1 0-2 3-2 4-4l-1-1h-1c-1 0-2 1-3 1s-2-1-3-2c-1-2 1-4 2-6l-1-2c0-1 1-1 2-2l6-9c2-3 4-6 7-8 6-7 13-13 21-18 8-6 18-12 28-15l18-6c13-3 27-4 40-5h9v1c1-1 2-1 2-1h26 47l92 1c7 1 14 0 22 0 2 0 4 0 5 2h0c-3 1-6 1-10 1-6 1-11 1-17 1-8 0-16-1-24-1-13-1-27 0-40 0h-11c-2 0-4-1-5 0 0 0-1 0-1 1-2-1-3-1-4-2 0 0 0 1-1 1 0 0-2-1-3-1h-7-17c-2 0-5 1-7 0v-1c0 1 0 1-1 1v1h-3l-7-1h-27c6 3 11 7 16 10 13 9 24 20 34 33 3 4 6 9 9 14 4 9 6 17 8 27v-6l1-10c1-8 4-16 7-23 1-6 4-13 7-17 2-1 5-3 7-3 1 0 0 0 1-1-1 0-2 0-2-1-1 0-2-1-2-2 1-1 2-2 4-3-4-2-7-5-11-7-7-3-14-5-21-5 2 1 5 2 6 3s1 3 1 4l-2 2c-1-1-2-1-3-2h0l-1 1c0 1 0 1-1 2-2-1-3-3-4-5-1 0-2-1-3 0-2 1-3 3-4 6-2 4-1 10 2 14 1 3 5 6 9 7 2 1 5 1 7 0s3-3 3-5 0-5-2-7c-1-2-2-3-4-3-1 0-3 0-4 1s-1 2-1 3 0 2 1 3 1 1 2 1c1-2 0-3 0-4 1-1 1-1 2-1s1 1 2 1c1 2 1 3 0 4 0 1-1 2-2 2-1 1-3 1-4 0-2-1-3-2-3-4-1-2-1-4 0-6 2-3 4-4 7-4h3c1 0 1-1 1-2 2-1 2 0 3 0s2 1 2 2h1l2-2c1 0 2 1 2 1 1 2 2 4 1 6v1c1 2 1 5 0 7-1 4-5 10-9 12s-8 2-11 0c-7-2-10-8-13-14-3-4-5-7-7-12h0c1 0 2 0 2-1s0-3-1-4c0-1-2-3-3-3-2 0-4 1-6 2-6-1-10-3-14-9l4 3c4 2 8 2 12 1 1-1 2-2 4-2s4 1 5 2c2 1 2 6 2 8 2-3 3-6 6-9 4-4 10-4 16-4 4 0 8 0 12 2 4 1 7 3 11 3h0c2 2 5 5 5 7 0 1 0 1-1 2 1 1 2 0 3 1l2 2c10 9 19 19 25 32 1 3 1 6 2 9 1 2 3 5 3 8 2 5 1 11 1 17 3-11 6-22 11-32 1-3 2-6 4-9 2-4 5-7 8-11 2-2 4-5 6-7 3-4 7-7 11-9 9-7 18-13 29-17v-5l-52-1c3-1 6-1 9-1-1-1-18 1-22 0h-10-6-3c-2 0-4 1-6 0s-5 1-6 0c0-1 0-1 1-2h19v1l1-1z" class="S"></path><g class="P"><path d="M196 168h1c0 2-1 3-2 4h-1c-1-1-1-1-1-2l3-2zm-7-3h0c1-1 3 0 4 0v1l-1 1-1 1c-1 0-1 0-1-1-1 0-1-1-1-2zm74-54h1v1c0 1-1 4-3 5h-4l-1-1c-1-1-1-1-1-2 2-2 5-2 8-3zm123 462h1c5 14-16 20-16 33-1 5 2 11 5 14 4 4 10 6 15 7h1-2 0c-5 0-10-1-15-3-2-1-3-1-4-3 0-2-2-3-2-4-5-11 6-22 11-30 3-4 4-9 6-14zm10 52h0c4-2 7-5 8-9 1-2 1-5 1-8 5 6 10 9 16 13 2 1 5 3 8 4s7 1 10 2h-7c-3-1-6-1-8-1h-10-18v-1zm-10-32h1c3 0 6 1 8 3 3 3 5 6 5 10s-1 9-4 12c-2 2-5 4-9 4-2 0-4-1-5-3-2-2-3-4-3-7 0-2 1-4 3-5 1-2 3-2 4-1 2 0 3 1 4 2 0 1 0 1-1 2 0 1-1 1-1 1l-2-2h0-1c0 2-1 2 0 3s2 1 2 2c1 0 3-1 4-1 1-1 2-3 2-4 0-2 0-3-1-5-2-1-4-3-6-3s-4 0-6 2c-4 3-3 8-4 12-2-4-2-8-1-12 2-5 6-8 11-10z"></path><path d="M421 451h4c5 0 9 3 12 6 12 12 9 49 9 66 1 4 1 7 2 11v13c-1-3-1-7-2-10-1-5-3-10-6-15-1 1-2 4-2 5 0 2 3 5 3 6 4 8 6 17 4 26-1 6-4 10-8 13-1 1-5 4-7 3-1 0-3 0-4-1-1 0-2-2-4-2v1c-1 1-2-1-2-1h-2c0 1 0 1-1 2l-1-1c-3-2-6-4-7-8-1-5 1-9 3-13 2-3 5-5 9-6 2 0 5 0 7 2 2 1 3 3 3 5 1 2 0 4-1 6s-4 4-5 4-3 0-4-1-1-2-1-3 0-2 1-2c1-2 3 1 4 2 1 0 1 0 2-1 0 0 1-1 1-2s-1-2-2-3-3-1-4-1c-2 0-3 1-4 3-1 1-2 4-1 6s2 4 4 5c3 2 7 2 10 1s6-3 7-6c1-1 2-3 1-5-1-4-2-8-6-11-5-3-11-3-16 0-1 0-2 1-3 2l3-4c5-4 11-3 18-2v-3c-2-2-5-2-7-3l2-2 4-4c3-4 6-10 5-16h0c-1 2-4 5-4 7v3c-3 0-4 2-6 4-2 1-3 2-5 2l8-8c5-6 8-15 9-23 2-8 0-14-5-21 1 2 2 5 2 7 2 16-5 28-14 40-2 3-4 6-7 9-3 5-6 11-9 16-1 2-2 5-3 8v-9c1-3 2-10 5-12 2-1 4-1 5-2s1-3 2-4l-1-1c-2 0-5 2-7 2 1-1 2-3 3-4 2-2 4-3 6-5 3-3 6-6 8-9-1 0-3 2-5 3l-7 3c-6 4-8 8-10 14l-1 5h-1v-1h-1v1 4l-1 11c0 18-1 40 11 54 6 7 17 13 26 13 7 1 15 0 21-5 4-3 5-8 6-13 0-5-1-10-5-14-1-2-2-2-3-2h-1c-1-1-2-2-4-2 0 1-1 1-1 2l-2-1c-1-1-1-1-2-1-2 1-1 1-2 2h-1s0-1-1-1c-2 1-3 1-4 2v2c1 1 2 1 2 2-1 0-2 1-2 2-1 2-2 5-1 8 1 2 2 5 5 6 1 1 5 1 6 0 1 0 2-2 3-3 0-1 0-3-1-4s-2-1-4-1 0 3-1 5c-1-1-1-1-2-1-1-1-1-2-1-3 0-2 1-3 3-4s5-2 8-1c2 1 4 3 5 5s1 4 1 6c-2 4-6 7-9 9-7 3-14 3-20 1-10-3-19-14-22-24-1-4-1-8-3-11v-1h0l1 1h0c1-2 1-4 0-7 0-1-1-3-1-4 4 4 9 9 14 10 9 1 15 0 22-5 6-5 8-11 9-18 3 8 5 17 9 25 2 3 4 7 7 11 1 2 4 4 5 7 0 0-1 0-1 1s1 5 1 7c-1 3-3 7-6 9-2 1-5 2-7 3-8 3-16 4-23 3-5-1-8-2-12-4-9-5-15-11-19-21-1-1-2-4-3-6 0-1 0-2-1-3v-1-1-2l-1-6c-1-5-1-10-1-14l1-21 2-29c1-12 1-25 3-37 1-6 3-14 6-19 3-4 7-7 11-8z"></path></g><path d="M426 458h3c3 1 6 3 7 6s0 8-2 10c-1 1-3 2-4 3 1 0 1 0 2 1l1 1c-1 0-1 0-2 1l-7-1 1-5-3-1c-2-1-3-2-4-4s0-5 1-7c1-3 4-4 7-4z" class="S"></path><path d="M428 462c1 0 2 1 3 2 1 0 2 2 1 3 0 1 0 2-1 3h-2c-1 0-2 0-2-1-1-1-1-3-1-4 0-2 0-2 2-3z" class="P"></path><path d="M417 581c4 0 8 0 11 2 2 1 4 4 5 7 0 3 0 6-2 8s-3 2-5 3c-2 0-4-1-6-2-1-1-2-3-2-5 0-1 1-2 2-3h3c1 1 1 2 1 3-1 0-1 0-2 1v1c2 1 2 1 3 1s1 0 2-1 1-2 1-3c0-2-1-4-3-6-1-1-4-1-7-1-1 1-2 1-3 3-2 2-1 5-1 7l1 1c0 1 0 2 1 2l2-1h0v3s-1 1-1 2l3 3c3 2 6 4 9 4l2-2c0-1-1-2-1-4h0 1c1 0 2 1 2 2 1 1 1 3 0 4 0 1-1 1-1 2-3 1-7 0-9-2-6-3-11-8-13-15-1-3-1-6 1-9 1-3 3-4 6-5zm-1-125h1c1 2-2 7-2 9 0 7 1 14 4 20v-1c0-2-1-5 0-7 1-1 2-1 3-2v1h0c-2 6 1 11 3 17l8 15v2c-1 1-1 1-2 1-2-2-4-5-6-7-1 2-1 4-3 6h-1l1-2c-1-1-2-2-2-3-2-1-4-2-6-2-5 1-6 6-7 10 0 2-1 3-2 4 0 1-1 2-2 2 0-3 2-7 3-10 1-4 2-6 6-8h0c-3-3-6-6-7-10 0-4 0-9 2-12h1c0 1 1 2 0 3l-1 3c1 3 2 5 3 8 0-2 0-5 1-7h0c2 3 2 6 2 8 2 5 8 7 12 9-2-3-4-5-5-8l-8-13c-1-2-4-6-4-9-1-2-1-4 0-5 1 0 2 2 3 3 0-2 0-5 1-7 0-3 1-6 4-8z" class="S"></path><path d="M420 134c4 0 8 5 11 8 4 5 7 11 10 17 2 5 4 11 5 17 1 5 1 10 1 16v30 43 92 39 26 25 33c-1-1-1-5-1-6 0-5 1-10 0-14-1-2-1-3-2-5-3-4-8-7-13-8-4-1-10 0-14 2-2 1-4 3-6 4 1-2 2-4 4-6 5-8 12-15 12-25 1-3 0-8-3-10-2-3-5-4-8-4s-7 0-9 2c0 1-1 2-1 3 1 1 1 2 2 3l1-1c2-1 3-2 5-2s3 0 5 1c4 5 2 12-1 17-4 8-9 14-12 22-4 9-5 20-7 30l-1 10-2 28c-1 14-2 27-2 40l1 29h-1 0c-3-1-4-1-7-2 1-3 3-6 3-9s-1-5-3-7h0c0-2 1-5 2-7l3-12c1-5 1-9 2-14l1-56c0-11 0-22 1-33 1-9 4-18 9-26 1 1 3 3 4 3 2 1 4 1 6 0 1-1 2-3 2-4 1-2 1-4-1-6 0-1-1-2-2-2-2 0-2 1-3 2h-1l3 2v3c-1 1-1 1-2 1-2-1-3-2-4-3-2-3-4-8-1-12 1-1 2-2 2-4s0-4-1-5c-2-2-4-2-6-2s-2 0-3 1l1 1h2c1 1 2 1 3 2s1 3 1 5l-2-2v1c-1 0-2 1-2 3-1 4 2 10 4 14-1-1-2-2-3-2-2 2-2 4-3 6-2 5-3 11-4 17-1-3-1-5-1-8l1-20v-48l-1-29c0-2 1-5 1-8v-35l-1-28 1-31c-1-19-2-38-1-57 0-9 2-18 5-27 1-3 3-8 6-9 3-2 5-5 8-7 2-1 5-1 7-2z" class="P"></path><path d="M444 348c0 1-1 2-1 3h-1c0-3-2-5-4-7v-1h1c2 0 3 2 4 3h1v2zm-42 43c1 0 3 0 5 1 2 0 4 0 6 2v1h-4c-3-1-6-1-10 0h0-1c0-1 1-1 1-1 0-2 1-2 2-2l1-1zm7-240h0l6-3c0-1 0-2 1-3 2 0 3-1 4 0l2 2c-1 2-3 3-5 4-3 1-6 1-9 1v-1h1z" class="U"></path><path d="M426 399c1 0 2-1 2-1v1c2 3 3 6 5 9 2 1 4 1 5 3 2 1 3 2 3 4 0 1-1 2-2 3 0 0-1 1-2 1s-2-1-3-1c-1-1-8-17-9-18l1-1z" class="S"></path><path d="M435 413c2-1 2-1 3 0v2l-2 1-1-1v-2z" class="P"></path><path d="M433 350h2c1 1 3 4 3 6 1 5 0 10-4 13l-3 3c0 1-1 3-2 5 0 1-1 3-2 4v-9c-2 0-3 0-5-1 0-1 0-1 1-2l2-1c2-1 2-3 2-4 1-3 1-6-1-8v-1h1c1 0 1 1 2 1 3 4 2 8 1 12l2-3c2-3 3-7 2-10 0-2-1-3-1-5h0z" class="U"></path><g class="S"><path d="M436 421c3 0 5 0 7 2 1 1 2 3 2 4 0 2 0 4-2 5-1 1-2 0-3 0-1-1 2-1 3-3l-2-2c-1-1-3-1-5-1s-3 1-5 3c-1 2-1 4-1 7 0 1 1 2 1 2 1-1 1-2 2-3s2-1 4-1c0 1 1 1 2 2l-2 2c-1 1-1 1-1 2-1 2 0 3 0 4 2 3 6 3 9 4v1c0 1-1 1-1 1-3 1-5-1-8-2-4-3-7-8-8-12s-1-8 1-11 4-4 7-4zm-13-144h1l1 1c-1 4-1 8 0 12 0 3 1 5 1 7s0 5-1 7c1-1 2-2 3-2h1c1 2 0 4-1 6 0 2 0 4-1 5h0 1v-1l2-2c1 2 0 7 0 9h-5l-3 3c-1 0-2-2-3-3-1 0-3 0-4-1l-2-3v-3c1 0 1 1 2 0 0-2-1-3-1-5-1-1-1-2 0-3 0 0 1 1 2 1h0c0-3-1-6-1-9h1c1-1 0-5 1-6 0-4 0-8 1-11 0 7 1 14 3 20 1-4 1-10 1-15 1-2 0-5 1-7zm-14 49h2c2 0 4 1 5 3s1 3 0 5c0 0-1 1-1 2-2 0-2-3-3-3-1 1-1 1-1 2l-1 1c-2 1-4 0-6 0l-2 1c-2 2-3 6-3 8 0 0 1 0 1-1h1c1 0 1 0 2 1h1l-3 2c-2 3-2 8-2 11 0 13 8 21 17 30l4 5c-1 1-2 3-4 4-1-2-3-4-5-5-1-1-3-1-4-1v-3c-1 0-2 0-3-1 0-1 1-2 2-3-2-1-4-2-5-4-1-1-1-1-1-3 1-1-2-5-3-7-2-5-2-11-1-17 0-6-1-12 2-18 3-5 6-8 11-9z"></path><path d="M419 334c1-1 2-1 2 0 2 1 3 3 4 5s3 5 4 7c0 2-1 3-2 4-2 2-3 2-5 2 0 9-2 18-1 28l3 9c-4-4-5-11-6-17v-1c-1 1-2 0-4 0h0v1c0 2 1 3 1 5s-1 2-2 4l-1-1v-3c0-1 0-4-1-6 0 0-3 0-4-1l-3-3h0c-2-3-3-8-2-11 0-3 2-5 4-6h1c0 2-1 3-1 4-1 4-1 8 1 11 0 1 1 2 2 2v-6c2-3 4-4 7-5-1 1-2 2-3 4-1 1-1 3-1 4 1 3 4 4 6 5v-18c-1 1-2 1-4 1-1-1-2-1-2-2-1-1-1-3-1-4 1-2 7-12 8-12z"></path></g><path d="M422 344h1c1 0 2 1 3 1 0 2 0 2-1 3h-1c-1 0-2 0-2-1v-3zm-4-1h1c-1 2-1 4-2 5s-1 0-2 0-1 0-1-1 1-2 1-2h2c0-1 1-2 1-2z" class="P"></path><path d="M444 346h0c-2-5-4-8-7-11-1 1-2 2-4 3h-2-1c-1-1 0-3-1-4l-3 1c-1-1-1-3-1-4s1-3 2-3c1-2 3-2 5-2 4 0 8 3 10 6 4 5 4 12 4 19 0 3 0 7-1 10 0 4-1 7-2 11 0 2-2 6-3 7s-2 2-2 3c-1 1 0 1-1 2s-2 1-3 2v2c-1 1-4 3-5 5 3-2 8-2 11-1 2 1 3 2 3 4 1 2 1 5 0 7-1 1-2 2-4 2-1 1-1 1-2 0-1 0-1-1-2-2 1 0 0 0 1-1 1 0 2-1 3-2 0-1 0-2-1-3s-3-2-4-2c-6-1-11 5-15 8-1 1-2 2-3 2h-3c2-5 7-10 11-14 9-10 17-23 19-37 0-2 2-4 1-6v-2zm-36-194v2 1l3-1c4-1 10 0 13 2 2 1 3 3 3 4 0 3-2 3-4 5-1 2 3 5 3 7l-1 1h-2c-1-1-2-2-2-3-2-4 1-6 2-10 0-1-1-1-2-2 0 0-2-1-3 0 0 1 1 2 2 4l-6-4c-2 1-5 1-8 3-4 4-6 9-7 15 0 7 0 13 4 19 2 3 4 5 6 8 1-3 1-5 1-7 1-3 3-5 6-7 3-1 6-2 8-1 3 1 5 4 6 6 1 3 0 7-1 10h1c1-1 1-3 2-4 0-5-1-10-4-13-4-4-9-5-13-3-2 1-4 3-5 3l-1-1v-3h0 0c-1 2-2 3-3 5h-1c0-1-1-2-1-3s1-2 1-3l-1-1v-2c0-2 2-2 2-3 1-1 1-4 2-5 1 0 1-1 2-1-1-1 0-1 0-2 1-2 4-3 6-4 0 2-1 7 0 9 0 1 1 2 2 2l1-1h0c1 0 2 1 3 2 5 2 9 7 11 11 4 7 3 14 1 21 2-3 3-5 4-9 1-3 1-6 1-9v-2c-1-1-2-3-3-4l1-2c-1 0-2-1-3-2 0-2 1-1 2-3l-1-1h0c-2 0-5-2-6-3l3-3c0-1 0-2-1-3 0-1-1-2-2-2 0 1-1 2-2 3l-2-2c1-2 2-4 4-5 1 0 2 0 3 1 2 2 2 4 3 6 1 3 2 5 4 8 2 4 2 8 2 13l1-1v-4c0-7-1-14-4-21-1-3-4-8-8-9-2-2-5-2-7-3l5-5c1 1 3 1 4 2 2 2 4 5 5 8 2 3 4 5 5 8 3 6 3 12 3 18 1 10-2 21-9 28-2 2-6 4-8 7 1 0 2 0 2 1 1 0 0 1 1 2l1-2c0-1 1-2 3-2v-1c1 0 3 0 4 1 2 0 4 1 5 3l-1 1h-3c-2 1-3 2-4 3-1 2-1 5-1 7l1 1c0 2 2 5 3 7 1 4 3 7 4 11 3 7 3 13 3 20 0 11 0 22-2 32 0 2 0 4-1 6-1 1-2 2-2 3 1 0 1 1 2 1-1 3-3 6-4 9h-1l-1-1c-1 0-2 4-2 5-1 1-1 1-2 0s-1-4-1-5v-15c0-10-3-22-7-32-2 1-4 1-7 1-1 2-2 4-2 5-5 10-6 22-7 32 0 4 1 9 0 13 0 1 0 1-1 2h-1l-2-7h-1c-1 1-2 3-4 3 0-1-1-1-1-2 0-2 2-3 3-5-1-1-2-2-2-3-1-2 0-3-1-5 0-2-2-5-2-7-2-10-2-19-2-29 0-6 0-13 1-20 1-3 3-6 5-9 1-1 2-3 3-5-2-2-5-1-6-3 0-1 0-1 1-1 3-1 5 0 7 1 0-4-1-7 2-11 0 0-1-3-1-4-1-1-1-3 0-5 0-2 3-3 5-4-3-1-7-1-10 0-2 0-3 1-3 3-1 2 0 2 1 4 0 1 0 2-1 2l-1 1v-1c-2-2-2-4-2-7 1-3 2-4 4-6h0c0-1 0-1-1-2v-1c2-1 3-1 4-1l-6-9c-3-9-2-21 3-29 1-3 4-4 5-7 2-3 2-6 3-10 3-5 6-9 11-11-5 5-8 9-10 16h-1v1z" class="S"></path><g class="P"><path d="M438 277h0c1 1 1 1 0 3h-1-1c0-2 0-2 2-3zm-13-35h1l2 2c0 1 0 1-1 2h-1c-1 0-2 0-3-1 1-1 1-2 2-3zm-3 24l1 1c0 1 1 2 1 4 0 0-2 0-3 1 0 0-1 0-1-1h-1c1-2 2-4 3-5zm4-55c0-1 0-1 1 0l-1 2v1c0 1 1 2 2 3-1 1 0 4 1 6h-1l-1-1c0-1-2-3-2-4-1-1 0-3 0-3-1-1-1-2-2-2h0c0-2 2-2 3-2zm0 49h1c1 2 4 4 4 5l-1 2c-1 0-1 1-3 1l-1-5c-1-1 0-2 0-3zm-13-48c2 0 2 0 3 1s-1 1-1 3c0 3 2 4 4 6l-1 1c-3-1-5-2-7-4 0-1-1-2 0-3 0-2 1-3 2-4zm-2 25c1-1 2 0 3 0 0 1 1 2 2 2l2-2h1l3 3c0 2-2 3-2 4v1c0 1 0 2-1 3h0c-1-1-1-3-2-4-2-2-6-5-6-7zm9-44c2 0 4 0 5 1 1 0 2 2 3 3 0 3 0 5-2 7v1h-1c-2-1-3-1-4-1h-1c-1 1-2 2-2 3h0l-1 1-2-3c-1-1-1-4 0-6 0-2 2-4 5-6z"></path><path d="M410 224h0c3 2 5 3 9 2 1 0 3-1 4-1s2 1 3 2h-1v1c-1 0-2 1-3 2 2 2 4 2 6 3s3 2 4 3c2 3 3 6 5 9 1 2 3 4 4 7 2 8 3 17 3 26 0 1-1 10-2 10-1-1 0-2-1-4l-3 2c-1 1-2 1-2 2 0 2 1 2 2 3 1 0 2 1 3 2 0 6-3 12-5 18 0-7 0-14-2-21 0-3-2-7-3-10s-1-6-3-9v-1c3-1 5-2 7-4v-1c1 2 2 4 2 6 1 1 1 3 1 4h-1c-2 0-3 1-4 3 0 1 0 2 1 3 0 1 1 2 3 2s3 0 4-1c2-4-1-10-2-14-1-2-3-5-6-6-1-1-1-1-2-1-1-1-2-1-2-2l1-1c0 1 1 1 2 2 2 0 3 0 4-1s2-2 2-3c1-4-3-10-5-14 0 4-1 9 1 12v1h0l-1 1c-1-1-1-3-1-4-4 2-6 4-8 7-1-1-1-3-2-4 0-3 0-7 1-9 1 2 1 2 2 3h4c1-1 2-2 2-4 1-1 1-3 0-4-2-2-3-3-5-3h-2c0-1-1-2-2-2-1-2-8-3-10-3l-3 2h-1c1-1 1-1 1-2 2-2 5 0 7-1 1 0 1-1 1-2-1-2-4-1-6-1-1-1-1-2-2-2 0-1 0-2 1-3z"></path></g><path d="M423 252h3 0l-1 3h-1c-1-1-1-1-2-3h1z" class="U"></path><path d="M406 239h0l1 1c0 2 1 2 2 3h0c0 1 0 3 1 4 0 1 0 1 1 2 1-1 1-3 2-5 2 3 4 7 5 11 1 1 1 1 0 2 0 1-1 1-2 1-2-1-3-3-4-5-2 1-3 2-4 4-1-1-3-1-4-2s-1-2-1-3h2l1 1h1c1 0 1-1 1-2s0-2-1-3-1-1-2-1c-2 0-3 1-4 2s-1 3-1 5 1 4 2 5c2 1 6 1 8 0-1 3-2 6-1 9 0 0 1 1 1 2 1 0 3 0 4-1 0 0 1-1 1-2l-3-2v-1c0-2 0-3 2-4 1 1 2 3 2 5l3-6 1 1c0 1 0 2-1 3-2 2-3 6-5 8-3 7-6 15-7 23-1 4 0 9-2 13-2 0-1-5-2-7l-1-1v-1c1-2 1-5 1-7 1-2 2-2 3-5-1-1-4-1-5-2-1 4-1 7-1 10-2-7-3-14-3-21 0-12 0-25 9-34z" class="P"></path><path d="M406 264h0c-1 4-4 7-4 12 0 2 0 2 1 3h1v-2c1-1 1-1 2-1s2 1 2 2c1 1 0 1 0 2l-1 2c-1 0-2 1-3 1-2 0-3-1-4-2-1-2-1-3 0-5 0-4 3-9 6-12z" class="U"></path></svg>