<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="55 36 694 656"><!--oldViewBox="0 0 784 752"--><style>.B{fill:#2f2e2e}.C{fill:#1e1d1e}.D{fill:#171717}.E{fill:#535353}.F{fill:#454445}.G{fill:#101010}.H{fill:#383738}.I{fill:#474647}.J{fill:#262526}.K{fill:#0a0909}.L{fill:#656465}.M{fill:#4c4b4c}.N{fill:#787777}.O{fill:#e7e5e6}.P{fill:#8a8889}.Q{fill:#a6a4a5}.R{fill:#fefdfe}.S{fill:#969596}.T{fill:#e6e6e7}.U{fill:#c3c1c3}.V{fill:#b3b2b3}.W{fill:#f8f7f8}</style><path d="M606 367l4 1s0 1-1 1l-1 1h0c-1-1-1-2-2-3z" class="J"></path><path d="M653 368c2-1 3 0 4 0v1h-1c-1 1-2 1-3 1v-2z"></path><path d="M558 312c1-2 3-4 4-5 0 2-1 3-2 5v2c-1-1-1-1-1-2h-1z" class="P"></path><path d="M383 96v-4c0-1 1-1 1-2l1 1 1 3c-1 1-2 1-3 2h0z" class="C"></path><path d="M372 105c2-1 3-1 5-1 2 1 2 1 4 1-2 0-7 2-9 1v-1h0zm191 94s1 1 1 2-1 2-2 3c-1 0-2 0-3-1l3-3c0-1 0-1 1-1z" class="B"></path><path d="M636 373h2l-1 1h1c1 0 1 0 2 1-4 0-8 1-12 0h1 1c2-1 4-1 6-2z" class="I"></path><path d="M575 182v2c-2 1-4 1-7 1h0c1 0 1 0 1-1h0l1-1c1 0 3 0 5-1z" class="J"></path><path d="M551 575l6 2s-1 0-2 1v1l-6-3 2-1z" class="G"></path><path d="M410 99c1 0 6 4 7 5h-5c-1 0-1-3-2-4v-1z"></path><path d="M582 620l3 6c-2 0-3-1-4-1l-1 1-1-1v-1l1-3c1 0 1 0 2-1h0z" class="E"></path><path d="M588 604c1-1 1-1 2 0 0 1-1 3 0 4l3-1h0v1c-1 1-2 1-3 2l-3-3c0-1 0-2 1-3z" class="D"></path><path d="M580 626l1-1c1 0 2 1 4 1 1 2 2 5 3 7-2-1-2-1-3-2l-3-4s-1 0-1-1h-1 0zm-13-458c2-1 4 0 7 0v2c1 1 2 2 2 3-3-1-4-1-6-1 1-1 3-1 4-1-1 0-1-1-2-1s-2 0-3-1c-1 0-2-1-2-1z" class="M"></path><path d="M716 631h1c1 1 2 2 2 4 1 0 2 0 2 1-1 1-1 3-2 4l-3-9z" class="I"></path><path d="M562 180c2 1 4 3 8 3l-1 1h0c0 1 0 1-1 1-2-1-4-1-6-2 0-1-1-1-2-2 1-1 1-1 2-1z" class="C"></path><path d="M266 141v2 1c-2 4-2 7-2 11l-2-4v-1c0-3 2-6 4-9z" class="K"></path><path d="M616 651c2 1 3 1 5 1s6 1 7 2 0 1 0 2-1 1-2 1l-2-2c-2-3-6-3-8-3v-1z" class="C"></path><path d="M536 482c1-1 3 0 4 0-1 3-3 5-5 8 0-2 1-7 1-8z" class="L"></path><path d="M370 597c3 2 7 2 10 4l-3 1v-1h-5l-2 1-1-1c1 0 3-1 3-1 0-1-1-1-2-1v-2z" class="B"></path><path d="M616 306l1 1h0l1 1-1 1c0 3-1 6-3 9h-1c0-4 2-8 3-12z" class="F"></path><path d="M582 367c1 0 1 0 2-1 2-1 5 0 7 0h-2c0 1 2 3 3 3l2 2v1c-4-2-8-4-12-5z" class="C"></path><path d="M659 627h2v1h1 0c1 1 1 2 1 3h1c0 1-1 1-2 2h-3c-1-1 0-3-1-4v-2h1z" class="O"></path><path d="M556 536l6 9h-5c-1-1-1-1-1-3v-6z" class="U"></path><path d="M559 203v-1h-1c-2 1-4 3-6 5-5 5-9 10-13 17h0c4-9 10-14 16-20 2-2 4-4 7-6l1 1c-1 0-1 0-1 1l-3 3z" class="F"></path><path d="M452 72l-2 2c-2 2-3 4-5 6v-1h-2s0-1 1-2v-2-2c3 0 5 0 8-1z" class="Q"></path><path d="M520 479c2-5 3-10 7-15h0v2c-2 5-4 9-5 14l-2-1z"></path><path d="M653 409l2-1h0l-2 16c0-2 0-3-1-4v-7c0-1 1-2 0-3h0l1-1h0z" class="D"></path><path d="M518 231h1c1-1 1-1 3-1 1 0 3-1 5-1-2 3-5 5-7 7l-2-5z" class="K"></path><path d="M616 172c1 1 1 2 2 3 1 2 3 5 4 8l-3 1c-1 0-2 1-3 1v-2c1 0 1-1 2-2 0-4-1-6-2-9z"></path><path d="M656 639c1 1 1 1 2 1l3-1h0v1c-1 0-1 0-2 1h5c-7 0-14 1-21 3v-1l13-4z" class="G"></path><path d="M637 311l2-6h0c1 2 1 4 1 6-1 2-1 4 0 5v2h1v1l-1 1-1-1c-1-2-2-4-1-6 0 0-1-1-1-2z" class="B"></path><path d="M619 189h1c2 0 3 0 5 1 0 1 1 3 1 4 0 2-1 4-3 5h-2v-1c1 0 1-1 2-1v-4c0-1-1-2-2-2-1-1-2-1-3 0h-1v-1l2-1z"></path><path d="M533 572h1l-1 2h0l8-1c1 0 2 1 3 1 2 0 4 0 7 1l-2 1h-1c-6-1-11-2-16 0h-2-1c2-1 3-2 4-4z" class="J"></path><path d="M552 458c4 2 7 3 11 6-3-1-5-1-8 0h-2c-1-1-1-1-1-2l-1 1v-2h1 0v-3z" class="G"></path><path d="M499 556c-3 2-6 5-9 8 3-6 5-10 11-14v1 1l-3 3 1 1h0z" class="E"></path><path d="M551 529c3 2 4 4 5 7v6l-1 5-1 1v-1c-1 0 0-2 0-3v-7h0c-1-3-3-5-3-8z" class="B"></path><defs><linearGradient id="A" x1="610.567" y1="367.741" x2="616.288" y2="371.205" xlink:href="#B"><stop offset="0" stop-color="#312e33"></stop><stop offset="1" stop-color="#484848"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M610 368h2 4v1c1 0 3 1 4 1 0 0-1 1-1 2-1 0-1-1-1 0l1 1h0c-3 0-7-2-10-2h0l-1-1h0l1-1c1 0 1-1 1-1z"></path><path d="M601 319c-1-1-2-1-2-1-1-2-2-6-1-8 3 4 6 8 10 11l-7-2z" class="G"></path><path d="M567 168s1 1 2 1c1 1 2 1 3 1s1 1 2 1c-1 0-3 0-4 1-1 0-2 1-2 2 1 0 1 1 1 2h0-2c-1 0-2-1-2-2-1-1-1-2-1-3l3-3z" class="B"></path><path d="M403 104h-1c-1-1-1-3-2-5-1-3-2-6-4-9-1-1-2-3-4-4l5 3v1h0c1 1 4 3 4 5 1 2 1 7 2 9z" class="L"></path><path d="M427 638l9 3c1 0 4 0 5 1l-12 2-1-1c0-1 0-2-1-3v-2z" class="G"></path><path d="M575 439l7-3c1 1 1 1 1 2v5 1h-4l-1 1v-1l1-1c-1-1-2-1-2-1l1-1c0-1 0-1-1-2h-2z" class="F"></path><path d="M579 194h1c3-1 5-8 7-11h0v3c-1 2-4 7-4 9l1 1c-3 2-7 3-10 4 2-2 3-4 5-6z" class="L"></path><path d="M639 370l9 2c-2 1-5 2-8 3-1-1-1-1-2-1h-1l1-1h-2-5c2-2 6-2 8-3z" class="C"></path><path d="M645 285c3 0 6 0 9-1l1 5c-1 1-3 0-4 1v1l-1-2-1 1v-1l-4-4zM532 491c0-1 0-2-1-2v-6c0-2 1-2 2-3 1 1 1 1 3 2 0 1-1 6-1 8-1 0-2 1-3 1z" class="F"></path><path d="M449 598c1 2-1 6-1 9-3-1-6-1-9-1h4v-1-5l6-2z" class="B"></path><path d="M282 151c3 3 6 5 9 7 0 2-1 2-2 3v3h0v-1c-2-1-3-3-4-5s-3-4-3-7z" class="Q"></path><path d="M526 445l2-20 2 4h-1c0 2 0 4 1 6 0 3 0 6-2 8 0 1-1 1-2 1v1z" class="P"></path><path d="M381 75h3c1 0 1 0 2 1h1c1 1 1 1 2 1l3 2v1c2 0 2 1 3 2 0 1-1 0-2 1l1 1c-1 0-1 0-2 1-1-1-2-3-3-4l-2-2-3-2c-1 0-1 0-1-1-1 0-1 0-2-1z" class="O"></path><path d="M280 145v-1h0l1-1c0 1 1 4 1 4 1 2 3 3 3 4h1c0 1 1 2 2 3h2l1-1c1-1 1-1 2-1v1c0 2 0 3-1 5h-1c-3-2-6-4-9-7l-2-6z" class="C"></path><path d="M641 249h-1c0-3 0-8 1-10l2-2 3 10-5 2z" class="G"></path><path d="M603 612l6 2c-1 1-3 2-4 2-4 2-6 6-7 10 0-5 2-10 5-14z" class="L"></path><path d="M270 135c3-5 8-11 12-15l-10 21 2-8c-1 1-1 2-2 4v-1-3c0 1-1 1-1 2h0-1z" class="K"></path><defs><linearGradient id="C" x1="568.505" y1="439.189" x2="574.178" y2="433.394" xlink:href="#B"><stop offset="0" stop-color="#1b1c1a"></stop><stop offset="1" stop-color="#3b3a3c"></stop></linearGradient></defs><path fill="url(#C)" d="M578 429c0 3-1 3-1 5v2l-6 3c-2 0-5 2-8 2l15-12z"></path><path d="M344 107h0c3 0 6 0 8 2 0 1 1 1 1 2 0 2-2 3-3 4h-2c-1 0-2 0-2-1v-2l2-1v-1c-1-1-4-1-4-3z"></path><path d="M349 109h1v2c1 0 1 1 1 2-1 1-1 1-2 1h-1l-1-1 1-1s1 0 2-1c-1 0-1 0-1-1v-1z" class="O"></path><path d="M560 434c-1 1-3 2-5 2l-2 1h4c2-1 4-1 6-1l-6 2c-7 2-12 1-17-3l-1-1h0c2 0 3 1 5 2 3 1 8 1 10 0s4-2 6-2z" class="C"></path><path d="M543 520c-2-3-6-6-9-8-2-1-4-2-5-4-1-1-1-2-1-4 0-1 1-1 2-2h2 0c1 2 1 3 0 4h0v2c0 1 5 5 6 6l6 6h-1z" class="D"></path><path d="M294 109l13-10v1s-1 0-1 1l-1 1v1h1s1 1 0 2c0 1-2 2-2 3l-8 1h-2z" class="S"></path><defs><linearGradient id="D" x1="561.995" y1="563.254" x2="556.278" y2="566.052" xlink:href="#B"><stop offset="0" stop-color="#787a78"></stop><stop offset="1" stop-color="#979295"></stop></linearGradient></defs><path fill="url(#D)" d="M543 562c2-1 4-1 6 0h1v1c1 1 3 1 4 1h1 0 2 5c2 0 4 0 5 1h1c-2 1-4 1-6 1-3 0-7 0-10-1h-4l-1 1v-1l1-1v-1l-5-1z"></path><path d="M647 439c1 3 1 6 1 10-2-1-4-2-7-2v1-1c0-1 0 0 1-1v-3c1-3 2-3 5-4z" class="J"></path><path d="M628 246v1h0c-1 2-1 4-1 6-1 2-1 5-1 8-1 1 0 3-1 5l-1 1-1-2c0-7 0-14 5-19zm-73 333v-1c1-1 2-1 2-1 5 2 12 6 14 10l-2 2c-2-1-4-4-6-6-2-1-5-3-8-4z"></path><defs><linearGradient id="E" x1="573.834" y1="177.92" x2="579.199" y2="173.24" xlink:href="#B"><stop offset="0" stop-color="#595859"></stop><stop offset="1" stop-color="#706e6e"></stop></linearGradient></defs><path fill="url(#E)" d="M574 168c3 1 4 2 6 5 1 2 1 5 0 7s-2 3-5 4v-2c1-1 2-2 2-4 1-2 0-3-1-5 0-1-1-2-2-3v-2z"></path><defs><linearGradient id="F" x1="550.264" y1="464.86" x2="545.131" y2="463.9" xlink:href="#B"><stop offset="0" stop-color="#262426"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#F)" d="M545 458c2 0 4-1 6-1l1 1v3h0-1v2l1-1c0 1 0 1 1 2h2l-12 4v-1c0-1 1-1 2-2 3-2 1-3 2-6v-1h-1-1z"></path><path d="M561 432h1 1c2-1 3-2 5-1h3 1c-1 2-3 4-5 4-1 1-3 1-4 1-2 0-4 0-6 1h-4l2-1c2 0 4-1 5-2 0-1 0-1 1-2z" class="U"></path><path d="M627 307c1 7 1 15-2 21-1-3-1-6-2-9l1-1v-3c1-2 1-4 2-7h1v-1z" class="J"></path><path d="M596 649h5l9 2v-1c1 0 1-1 2 0h0 1c1 1 2 1 2 1h1v1c-1 0-3 0-4 1-1 0-2-1-2-1h-4 0c-3 0-5-1-8 0l-9 3c-1 0 0 0-1 1v-1c1-1 3-2 5-3 1 0 3 0 4-1h-4-1c2-1 3-1 4-2z" class="V"></path><path d="M530 435c-1-2-1-4-1-6h1c1 3 3 6 4 8l2 2v2c0 1 1 3 2 5h-1c-1-1-2-1-4-2h0l-1-1c0-3-1-5-2-8z" class="B"></path><path d="M534 437l2 2v2c0 1 1 3 2 5h-1c-1-1-2-1-4-2h0c1 0 1 0 2-1v-2c-1-2-1-3-1-4z" class="H"></path><path d="M563 298c7-7 13-12 21-16l2 2c-9 2-15 10-21 16-1 0-1-1-2-2z" class="B"></path><path d="M627 650c5-2 8-3 12-1h0v5 1c-4 0-8-3-12-5h0zm-52 7h1c0 5-5 9-7 13-1 1-2 3-2 4s1 2 2 2c1 1 2 0 3 0 3-1 4-4 6-6-2 3-4 7-7 8-1 0-2 0-3-1s-2-2-2-3c-1-4 2-7 4-10 2-2 4-4 5-7z" class="C"></path><path d="M544 520c2 3 4 6 7 9h0c0 3 2 5 3 8h0c0 1 0 2-1 3h-1c-2 0-3-1-4-2s-1-3-1-4l1-2c0 1 0 2 1 2h0v-5c0-1-1-1-1-2-1-2-3-5-5-7h1z" class="V"></path><path d="M580 543c1 1 1 1 1 2l1 2v1l2 5 1 4 1 1c1 3 4 4 5 7h0c1 1 1 2 1 4l-1 1v-1h0v-2c0-1-1-2-2-3-1 0-1 0-2 1l1 1c0 1 0 1-1 2h0v-1c0-2-1-4-2-6l-3-6c-1-3-2-6-2-9h0v-3zm7 106h9c-1 1-2 1-4 2h1 4c-1 1-3 1-4 1-2 1-4 2-5 3-2 1-3 2-5 3l1-1v-1c1 0 2-2 4-2 1 0 0 0 1-1h0l-1-1h1v-1c-1 0-1 0-2 1h0-1l-2 1c-1 0-1 0-2 1v1c1-1 1-1 2-1v1s-1 1-2 1h0l-3 3h0l1-2v-1h-1 0c1-1 2-3 3-3 1-1 2-2 3-2 1-1 2-1 2-2z" class="O"></path><path d="M587 649h9c-1 1-2 1-4 2h1-2 0-4-1c-1 1-3 2-4 2 1-1 2-2 3-2 1-1 2-1 2-2z" class="U"></path><path d="M259 163c0 2-2 3-2 4h1v-1c1-1 0 0 1 0l2-2c1 0 1-1 2-1v1c0 1-1 1-2 2s-3 3-4 5v1 1h0c-1 1 0 2 0 3 1 2 3 4 5 4 1 0 2 0 3 1h-5c-2 0-4-2-5-4s-2-4-1-7 2-5 5-7z" class="K"></path><path d="M270 135h1 0c0-1 1-1 1-2v3 1c-1 4-1 11-3 14h-1c-1-3-1-5-1-7 0-1 0-1-1-1v-2c1-2 2-4 4-6z" class="D"></path><path d="M601 152c4 3 12 12 13 17h-1v6c-1-4-2-7-3-11h-1l-1 1c-1-1-2-3-3-5-2-2-4-5-7-6 2-1 2-1 3-2z" class="E"></path><path d="M554 575c2-1 5 0 7 0 7 2 15 4 23 2v1l-1 2-3 3c-9-4-17-6-26-8z"></path><path d="M527 464c0 1 0 2 1 3 2-2 3-5 5-8s5-5 7-8c0 3-2 5-3 8l-1 4c-1-1-1-1-2-1l-1 1c0 2-1 5-3 7h-2s-1-1-1-2v-2-2z" class="V"></path><path d="M639 238c1-1 2-2 3-2s1 0 1 1l-2 2c-1 2-1 7-1 10h1l-7 5c1-2 1-4 2-6 0-2 0-6 2-8 1-1 1-1 1-2z" class="F"></path><path d="M370 636c-2 1-3 2-5 3 0 0-1 1-2 1 0 0-1 0-2-1l-7-1 9-6c2 2 5 2 7 4z" class="B"></path><path d="M605 668c2 0 4 1 6 3 1 1 3 4 5 4h0c1 2 2 3 3 5h0c0 2 2 4 3 6l-1 1c-1-1-3 0-5-1-1-3-1-6-2-9-2-4-5-8-9-9z" class="C"></path><path d="M559 468c2 2 4 3 4 5 1 1 1 2 1 2l-3 3h0c-1-1-2 0-3 0-1-1-3-2-3-3-1-1-1-2-1-2h-1c2-2 4-3 6-5z" class="B"></path><path d="M530 435c1 3 2 5 2 8l1 1h0v1 8c-2-2-4-3-7-3l-1-1 1-4v-1c1 0 2 0 2-1 2-2 2-5 2-8z" class="F"></path><path d="M583 286c5-3 12-3 17-2l6 3-1 1v1c-1 0-2 1-3 1-1-1-3-2-4-2-2-1-4-1-6-1l-1-1h-5c-2 1-2 1-3 0z" class="E"></path><path d="M628 246c1-1 3-3 5-4 0 2-2 4-2 7-1 2-1 4-1 6 0 1-1 3 0 4-1 3-3 5-3 8 0-1-1-1-1-2v-4c0-3 0-6 1-8 0-2 0-4 1-6h0v-1z" class="I"></path><path d="M682 671l-1-1c-1-3-4-7-4-10l1-1c4 2 8 4 12 7l-8 5z"></path><path d="M343 592c2-1 3-2 5-4 1-1 3-3 5-4l-1 2c1 2 5 4 7 5-2 1-3 1-4 1-1 1-3 2-4 2-2 1-5 0-7 0l-1-2z" class="N"></path><path d="M559 623h0l-2 1h0c0 1 1 1 1 1 2 0 2 1 3 2 2 1 5 3 7 3 0-1 0-1-1-2h0v-1h1s0 1 1 1c1 1 2 1 3 2 0 0 1 1 2 1h0l1 1 3 1-1 1h-1c-1 1-2 2-2 3-1-1-3-2-4-3l-6-3c-3-2-6-5-9-8h0 3 1z" class="I"></path><path d="M397 89c2 1 5 3 7 5h1 1l1 2 4 8h-8c-1-2-1-7-2-9 0-2-3-4-4-5h0v-1z"></path><path d="M345 92c1 2 1 3 2 5-5-4-8-8-11-13l8-3c1 1 1 4 1 6v3 2z" class="K"></path><defs><linearGradient id="G" x1="533.324" y1="543.707" x2="526.142" y2="535.875" xlink:href="#B"><stop offset="0" stop-color="#2a2a29"></stop><stop offset="1" stop-color="#444345"></stop></linearGradient></defs><path fill="url(#G)" d="M528 542h0 0c1-2 1-4 1-6 1-3 1-5 3-7 2 5 2 11 0 16l-5 8c0-4 0-8 1-11z"></path><path d="M639 238c0 1 0 1-1 2-2 2-2 6-2 8-1 2-1 4-2 6-1 1-3 3-4 5-1-1 0-3 0-4 0-2 0-4 1-6 0-3 2-5 2-7l6-4z"></path><path d="M580 546c0 3 1 6 2 9l3 6c1 2 2 4 2 6v1h0v1c-1 2-2 4-4 5h-3v-1c3-2 4-4 5-7-1-3-2-7-3-10 0 3 1 7 0 10-2 3-5 3-8 4 2-1 6-3 6-5 1-4 0-8 0-11 0-2 0-4-1-5l-1 3v-5l2-1z" class="G"></path><path d="M540 451l1-1h0v7c1-2 3-3 4-5 1 1 1 1 2 1l-1 1h0c-2 2-3 3-4 5-1 1-2 2-2 3-1 2-2 6-3 8-1 0-2 0-2 1s0 2-1 3l-1 1c0-1 1-3 1-4l-1-1c1-2 3-5 3-7l1-4c1-3 3-5 3-8z" class="C"></path><path d="M572 645c-1-1-2-2-2-4 0-1 1-2 2-3h1c1 1 1 3 1 5v1c-1 0-1 0-1 1 1 1 5 2 7 2 6 1 13 0 20 1 0-1-1-1-1-2h0c2 1 4 2 7 3h0c1 0 3 1 4 1v1l-9-2h-5-9c-4 0-9 0-13-2h0c-1-1-1-2-2-2z" class="E"></path><path d="M532 647v-1l1 1h1c1 0 1 1 2 2l1 1c-4 2-6 5-9 8l-6-3c2-3 6-6 9-7 0-1 0-1 1-1z" class="D"></path><path d="M525 516c3 4 5 8 7 13-2 2-2 4-3 7 0 2 0 4-1 6h0 0c0-3-1-5-1-7l1-1-3-6 1-1c-1-4-2-7-1-11z" class="I"></path><defs><linearGradient id="H" x1="558.194" y1="604.321" x2="547.696" y2="600.009" xlink:href="#B"><stop offset="0" stop-color="#313230"></stop><stop offset="1" stop-color="#4f4d50"></stop></linearGradient></defs><path fill="url(#H)" d="M557 620h0c-5-6-6-12-6-20 0-2-1-5 0-8h0c0-2 0-3 1-3h0c0 1 0 1 1 1 1 2 1 5 1 7 0 6 0 12 2 17 0 1 0 3 1 4v2z"></path><path d="M612 368c5 0 11-1 16-3 1-1 2-3 4-4l2 2c1 2 3 2 5 2-3 2-5 3-8 4-3 0-7 0-10 1h1-2c-1 0-3-1-4-1v-1h-4z" class="P"></path><path d="M425 87l10 2h-2c0 5 3 8 1 13l-3-2h-1c-2-4-3-9-5-13z" class="B"></path><path d="M372 74c1 0 3 0 4 1 1 0 2 1 3 1 2 1 5 2 7 4 4 4 7 8 8 13l-1 1c-3-1-4-6-6-8-1-2-3-4-5-5-3-3-7-5-11-6l1-1z"></path><defs><linearGradient id="I" x1="598.473" y1="377.238" x2="592.715" y2="365.264" xlink:href="#B"><stop offset="0" stop-color="#3e3e3f"></stop><stop offset="1" stop-color="#5d5b5b"></stop></linearGradient></defs><path fill="url(#I)" d="M591 366c3 0 6 0 8 2 2 1 2 6 3 9 1 1 1 2 0 4-2-4-5-6-8-9v-1l-2-2c-1 0-3-2-3-3h2z"></path><defs><linearGradient id="J" x1="423.111" y1="608.283" x2="443.356" y2="602.76" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#29292b"></stop></linearGradient></defs><path fill="url(#J)" d="M439 606c-3 0-6 1-9 2-2 0-4 3-5 3-1-2-3-4-5-5 8-1 16-3 23-6v5 1h-4z"></path><path d="M398 606l11 1 1 4c-1 2-1 3-1 5s2 3 0 6c-3-4-5-9-8-13-1-1-1-2-3-2v-1zM259 163c1-1 3-2 5-3 5-4 7-8 10-13 1 0 1 1 1 2h0l1 1c-1 5-5 8-8 12l-5 3h0v2c-1-1-1-1-2-1 1-1 2-1 2-2v-1c-1 0-1 1-2 1l-2 2c-1 0 0-1-1 0v1h-1c0-1 2-2 2-4z" class="B"></path><defs><linearGradient id="K" x1="627.232" y1="368.683" x2="626.775" y2="373.996" xlink:href="#B"><stop offset="0" stop-color="#4e4c4e"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#K)" d="M633 370l4-1c1 0 1 0 2 1h0c-2 1-6 1-8 3h5c-2 1-4 1-6 2h-1-1c-3 0-6-1-9-2h0l-1-1c0-1 0 0 1 0 0-1 1-2 1-2h2-1c3-1 7-1 10-1 0 0 1 1 2 1z"></path><path d="M622 370h-1c3-1 7-1 10-1 0 0 1 1 2 1-4 0-7 1-11 0z" class="W"></path><defs><linearGradient id="L" x1="606.049" y1="559.02" x2="602.23" y2="541.354" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2a2828"></stop></linearGradient></defs><path fill="url(#L)" d="M608 540c2 0 2 0 3 1h1l-1 1-1 2c0 2-2 4-3 6-4 5-7 11-13 15 2-5 5-10 8-14 2-4 4-7 6-11z"></path><path d="M504 548c2-1 2-1 4-1 1 0 2 1 3 1 1 2 1 3 1 5l-1 5-4 1 2-6-10 3h0l-1-1 3-3v-1-1l3-2z" class="K"></path><path d="M547 453h2c1 0 1 0 1 1h-1c-2 0-2 1-4 2v2h1 1v1c-1 3 1 4-2 6-1 1-2 1-2 2v1c-1 0-3 1-4 2h0 3c0 1 0 2-1 2v1s-1-2-2-2h-4c0-1 1-1 2-1 1-2 2-6 3-8 0-1 1-2 2-3 1-2 2-3 4-5h0l1-1z" class="M"></path><path d="M542 554c-1-1-3 0-4-1h0c-4 0-7 1-10 3 4-4 7-6 12-8l1 2h3c0 1 1 3 2 4 2 1 3 2 5 3 1 1 2 3 3 3 3 2 6 1 8 4h-5l-5-2c-1-1-1-2-2-3-1-2-3-2-4-2h-1c-1-1-1-2-1-3-1-1-1 0-2 0z" class="H"></path><path d="M412 627l4 4c-1 3-2 6-1 9v1h0l-1 1c-5-1-10-3-14-5 3-1 6-1 9-3h1 0l1-1v-1c1-2 1-3 1-5z" class="D"></path><path d="M546 500c-2 1-5 5-6 7 0-8 0-15 4-23l2 2v14z" class="C"></path><path d="M498 130l1 1c-5 6-10 13-12 22h0c-2-2-3-4-5-6 4-6 11-13 16-17z"></path><defs><linearGradient id="M" x1="651.536" y1="392.809" x2="659.281" y2="391.123" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#353434"></stop></linearGradient></defs><path fill="url(#M)" d="M653 387h0c1 0 1-2 2-3l3-9-1 23c-1 2 0 4-1 6l-1 1h-1c-2-3-2-11-1-15 0-1 0-1-1-2l1-1z"></path><path d="M656 639c3 0 5-1 7-2s3-2 5-2c1 0 2-1 3-2 4-2 6-4 9-7 1-1 2-2 2-3 1-1 1-2 1-3 1 0 5-1 6-2-3 6-7 12-13 15-3 2-7 3-10 5l-5 1-3 1c-1 0-1 0-2-1z"></path><path d="M564 260c4-2 8-4 13-6l-1 1c-3 3-4 6-6 10v2c1 2 0 4 1 7l-1 1h0-1 0-1v-1c-1-1-1-4-1-5s-2-3-2-4c0-2-1-3-1-5z" class="C"></path><path d="M524 255l2 2c6 9 5 20 4 30l-1 2c-1-2-1-5-1-7l-2-16-2-11z"></path><defs><linearGradient id="N" x1="555.31" y1="304.391" x2="558.559" y2="310.323" xlink:href="#B"><stop offset="0" stop-color="#6f6c6e"></stop><stop offset="1" stop-color="#818181"></stop></linearGradient></defs><path fill="url(#N)" d="M563 298c1 1 1 2 2 2-3 4-7 8-9 13l-4 7c0 1-1 2-1 3-1-1-1-1-1-2v-3l-1-1c0-1 1-2 2-4 3-6 8-10 12-15z"></path><path d="M577 346c5 1 9 4 12 8v1c-5 0-10 0-15 2 1-4 0-7 2-11h1z"></path><path d="M651 290c1 1 4 2 5 4v3l2 12c0 7 1 14 1 21-1-1-1-4-1-5v-4-2-1c-1-1-1-1-1-2h-1v1s-3-10-4-12v-1c1-1 1-3 1-4 1-2 1-5 0-6 0-1-1-2-2-3v-1z" class="H"></path><defs><linearGradient id="O" x1="537.766" y1="427.337" x2="535.575" y2="441.876" xlink:href="#B"><stop offset="0" stop-color="#2d2d2e"></stop><stop offset="1" stop-color="#525151"></stop></linearGradient></defs><path fill="url(#O)" d="M530 413v-1c1 9 2 15 9 21v1h0l1 1c0 2 1 4 2 6v5c-1 0-2 1-3 0h-2 1c-1-2-2-4-2-5v-2h1c-1-3-3-6-4-9-3-5-3-11-3-17z"></path><path d="M536 439h1c0 2 2 4 4 5 1-1 1-2 1-3v5c-1 0-2 1-3 0h-2 1c-1-2-2-4-2-5v-2z" class="N"></path><path d="M559 468c4-2 10-4 15-3h0c1 1 3 0 4 1 0 1-1 2-1 3-5 1-9 3-13 6 0 0 0-1-1-2 0-2-2-3-4-5z" class="K"></path><path d="M376 636h1c6 0 11 4 15 8l-9-1-18-3c3-2 7-3 11-4zm78-535h6l1 1h1-4c1 3 3 4 4 7h0c1 3 1 6 1 9-1 1-2 5-3 5 0-1 1-4 1-5 1-1 1-3 0-5-2-2-5-3-8-3h0l2 2c2 2 4 3 5 5-1 0-2-2-3-2-3-2-4-4-7-5v1c-1 2 0 2 0 3-1 0-1 0-2-1 1-4 0-5-2-7 4 1 7 3 11 3l-1-2h0c0-1-1-1-1-2 0-2 0-2-1-4z"></path><defs><linearGradient id="P" x1="611.789" y1="685.872" x2="607.48" y2="676.344" xlink:href="#B"><stop offset="0" stop-color="#343335"></stop><stop offset="1" stop-color="#575556"></stop></linearGradient></defs><path fill="url(#P)" d="M609 671c2 3 3 5 4 9 1 2 1 4 2 6h-8-1v-2c0-2-1-4-2-6 1-1 1-3 2-5 1 0 2-1 3-1v-1z"></path><path d="M609 671c2 3 3 5 4 9h0-1v-1-1l-1 2h0c0-1 1-2 0-3l-1-1c-2 0-3 0-4 1v2c1 1 1 2 1 3v1h-1v1c0-2-1-4-2-6 1-1 1-3 2-5 1 0 2-1 3-1v-1z" class="F"></path><path d="M556 542c0 2 0 2 1 3h5c0 6 2 12 6 17h0-1c-2 0-3-1-5-2 0-1 0-1-1-2s-1-2-1-3c-1 0-1-1-1-2v-1h-1v2c-3-1-3-4-4-5l-1-1h1l1-1 1-5z" class="S"></path><path d="M558 548l1-1v-1c1 2 1 7 1 9-1 0-1-1-1-2v-1h-1v-4z" class="F"></path><path d="M555 547c1 2 1 1 2 2l1-1v4 2c-3-1-3-4-4-5l-1-1h1l1-1z" class="L"></path><path d="M650 324c1 2 2 5 2 7v1l-2 1c-2 0-10-1-12 0h-1 0c-3 2-5 5-8 7 2-2 3-5 5-6h0l1-1c2-2 3-3 5-4 1 0 2 0 2-1 1 0 4-3 5-4h0 3 0z" class="H"></path><path d="M613 505h0c1 0 2 0 3 1l-1 1v2l1 1c-1 2 0 2-1 4h0c1 1 1 1 1 2l-1 1h1c0 1 0 1-1 1 0 1 1 3 1 4 0 2-1 3-1 5 1 1 2 2 2 3h-1c0-1-1-1-1-2h-2c0 2-1 3 0 4l1 2 1 1c0-1 1-1 1-2h0c0-1 0-1 1-1v-2h1c-1 3-3 6-4 9-1 2-2 4-4 5l1-2 1-1h-1c-1-1-1-1-3-1 1-3 3-6 3-8 3-9 3-18 1-27h1z" class="B"></path><path d="M647 411l6-2h0l-1 1h0c1 1 0 2 0 3v7c1 1 1 2 1 4l-2 11c0 1 0 1-1 1v1l-1-1 1-1-3-24z" class="K"></path><path d="M539 581c2 1 3 2 4 3v2c0 2 0 6-1 8h-2c0 2 1 6 2 8h-2c-2 0-3-1-5-2-1 0-1 0-2-1 2 0 3 1 4 1v-1c0-2 1-5 0-6-3-2-7-3-11-3h-4v-1h-2 0c6-1 10 0 16 2h2c1-2 0-4-1-6v-1l2-1v-2z" class="P"></path><path d="M444 73v2 2c-1 1-1 2-1 2h2v1 1h2c3 0 6-5 7-7 0 2 1 3 2 4v1c-1 1-1 2-2 3 0 1 1 1 1 1h0c-1 2-2 2-3 3h5c-5 1-12 2-17 1 2 0 5 0 6-1h0c0-1-1-2-2-3h-1 0 0c-3-3-1-5-1-9 0 0 1 0 2-1z" class="S"></path><path d="M637 311c0 1 1 2 1 2-1 2 0 4 1 6l1 1c-1 2-3 4-4 6l1 1c1-1 2-1 3-1h1l1 1-2 2c-2 1-3 2-5 4l-1 1h0l-1-1c-2 2-3 3-3 5 1-8 3-16 6-23l1-4z" class="E"></path><path d="M637 311c0 1 1 2 1 2-1 2 0 4 1 6l1 1c-1 2-3 4-4 6l1 1c-1 0-1 0-2 1v-1-1c1-1 0-1 1-2 0-3 2-5 1-8 0-1-1-1-1-1l1-4z" class="I"></path><path d="M635 352c4 0 7 5 11 6h1c-3 3-5 5-8 7-2 0-4 0-5-2l-2-2c2-2 3-5 3-9z"></path><path d="M542 602c-1-2-2-6-2-8h2c1-2 1-6 1-8v-2c2 2 3 3 3 5 1 3 1 7 1 10 0 6 0 12 2 18-3-4-5-10-7-15z" class="B"></path><path d="M483 77c2-1 2-1 4 0h0c-3 4-5 7-8 10-2 0-3 1-5 1s-3 1-5 1c-1 0-5-1-6 0h-4-1l15-6c3-1 7-3 10-6zm94 463h1 1c0 2 0 5 1 6h0l-2 1v5 1c0 5-1 11-5 14-6 5-13 5-19 5-3 1-6 1-9 1 9-2 18-2 26-8 6-5 6-17 6-25z"></path><path d="M710 634c-2-1-2-8-2-10 1-1 2 0 3 0h0 1c5 2 8 5 10 10-1 1-1 1-1 2 0-1-1-1-2-1 0-2-1-3-2-4h-1l-2-1v1c2 4 0 10-2 15l1-5c-1-3-2-5-3-7z" class="E"></path><path d="M710 634c-2-1-2-8-2-10 1-1 2 0 3 0h0l1 1h-1c0 1 0 2 1 3v3l1 8v2c-1-3-2-5-3-7z" class="J"></path><path d="M274 147c5-12 5-26 16-35 1 1 1 2 1 3-4 4-7 8-9 13-1 3-2 7-2 10v2 1 1c-1 1-1 1-1 2v4c0 1-1 2-2 2h0v-3h0c-1 1-1 1-1 2h-1 0c0-1 0-2-1-2z" class="G"></path><defs><linearGradient id="Q" x1="521.25" y1="482.106" x2="526.426" y2="474.083" xlink:href="#B"><stop offset="0" stop-color="#3d3c3d"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#Q)" d="M527 466v2c0 1 1 2 1 2h2c2-2 3-5 3-7l1-1c1 0 1 0 2 1 0 2-2 5-3 7l-11 15-2 2h0v-2h-1l1-6 2 1c1-5 3-9 5-14z"></path><path d="M520 479l2 1c-1 1-1 3 0 4h0v1l-2 2h0v-2h-1l1-6z" class="H"></path><path d="M625 480c5 7 5 13 6 21-3-5-7-11-12-14h-1l-2-1v-1-1c1-1 3-4 5-4 1 0 1 1 1 1 1 0 2 0 3-1z" class="B"></path><defs><linearGradient id="R" x1="532.288" y1="476.589" x2="538.122" y2="477.95" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#434242"></stop></linearGradient></defs><path fill="url(#R)" d="M542 470c2 1 3 1 5 3-1 2-6 5-9 6l-5 1c-1 1-2 1-2 3v6c1 0 1 1 1 2h-3v-8h-1c0-2 4-6 5-8l1-1c1-1 1-2 1-3h4c1 0 2 2 2 2v-1c1 0 1-1 1-2z"></path><path d="M589 509v1c1 0 1 0 1 1 1 0 1 0 1 1 1 2 4 5 3 7v1h0-1c-4 2-7 5-10 9v-1c-1-8 1-13 6-19z" class="B"></path><path d="M548 642h6v1c-4 0-6 1-9 3h-1v2c0 1 0 2-1 3-2 2-5 4-6 6 0 1 0 2-1 4v2l-2-2-3-1s-2-1-3-2c3-3 5-6 9-8 2-2 3-3 5-4s4-2 6-4z" class="L"></path><path d="M537 651c2-1 4-3 7-5v2c0 1 0 2-1 3h0v-2h-1c-1 1-2 2-4 3l-1-1z" class="E"></path><path d="M537 651l1 1c-1 2-1 3-2 4 0 2-1 4-2 5l-3-1c1-3 4-6 6-9z" class="B"></path><path d="M538 652c2-1 3-2 4-3h1v2h0c-2 2-5 4-6 6 0 1 0 2-1 4v2l-2-2c1-1 2-3 2-5 1-1 1-2 2-4z" class="J"></path><path d="M463 89c1-1 5 0 6 0 2 0 3-1 5-1s3-1 5-1c-3 3-5 4-9 5-5 1-9 2-14 1-1 0-2 0-2 1h-1l-2-1c-1-1-2-2-2-4h9 1 4z" class="N"></path><path d="M449 89h9 1c-2 1-5 1-7 2l1 1h-2v1c-1-1-2-2-2-4z" class="L"></path><defs><linearGradient id="S" x1="473.371" y1="90.823" x2="463.208" y2="88.798" xlink:href="#B"><stop offset="0" stop-color="#484849"></stop><stop offset="1" stop-color="#656366"></stop></linearGradient></defs><path fill="url(#S)" d="M463 89c1-1 5 0 6 0 2 0 3-1 5-1s3-1 5-1c-3 3-5 4-9 5-1 0-2 1-2 0-2-1-6 0-8-1 0 0 2-1 3-1h0l1-1h-1z"></path><defs><linearGradient id="T" x1="707.135" y1="648.575" x2="705.824" y2="623.353" xlink:href="#B"><stop offset="0" stop-color="#2b2a2a"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#T)" d="M702 628c1 0 2 0 3-1 0-1 1-2 1-2-1-2-1-2 0-3h1c2 0 3 1 4 1h0v1c-1 0-2-1-3 0 0 2 0 9 2 10-1 2 0 4 0 6s0 4-1 5c-1 4-5 9-9 11 2-2 4-4 5-7 2-3 3-7 2-11-2-3-4-4-7-5h1l1-5z"></path><path d="M431 77c2-1 4-4 6-5h2c-1 2-2 3-1 5 1 0 3-2 4-3 0 4-2 6 1 9h0 0 1c1 1 2 2 2 3h0c-1 1-4 1-6 1l-4-1h-2c-1-3-3-6-3-9z" class="D"></path><path d="M569 370h10c5 1 11 4 14 8 1 2 3 6 2 8 0 4-3 6-5 8l4 1h-1 0c-2 0-4-1-6-1h0c1-1 1 0 1-1 2-3 3-6 2-10s-5-8-9-10c-4-3-10-3-15-2h0l3-1z"></path><path d="M430 72h5 0c-2 1-4 3-5 5v1l1-1c0 3 2 6 3 9h2c-4 0-7 0-11-2 0-1 0-2-1-3l-3-9h9z" class="I"></path><path d="M416 631c3 3 7 5 11 7v2c1 1 1 2 1 3l1 1h0c-7 1-15 1-21 0l7-2h0-1l1-1h0v-1c-1-3 0-6 1-9z" class="E"></path><path d="M584 257c8-3 15-2 22 0-5 2-10 2-15 4-2 0-2 0-3 1 1 1 1 0 1 1h-1c0 2 1 4 2 6l1 1s1 1 1 2 0 2 1 3c0 1 1 1 1 2l1 2v-1h0v-1c1 1 1 2 1 3-1-1-2-1-3-1-2-2-3-4-4-6-1-3-3-6-3-10-1-2-1-4-1-6h-1z" class="B"></path><path d="M699 612c1 0 9-6 11-7-1 2-2 6 0 8v1c1 0 1 0 2-1l1 1c-2 0-4 1-5 3 0 2 0 3-1 5h0-1c-1 1-1 1 0 3 0 0-1 1-1 2-1 1-2 1-3 1l-2-15-1-1z" class="E"></path><path d="M668 654l10 5-1 1c0 3 3 7 4 10l1 1-7 3c-1-4-3-7-5-11-1-2-3-6-3-7s1-2 1-2z" class="C"></path><defs><linearGradient id="U" x1="512.473" y1="645.936" x2="512.355" y2="621.842" xlink:href="#B"><stop offset="0" stop-color="#0e0d0c"></stop><stop offset="1" stop-color="#292929"></stop></linearGradient></defs><path fill="url(#U)" d="M519 621h3c1 0 1 0 2 1-3 0-5 1-8 3-3 3-5 7-6 12 0 3 1 6 3 8 3 3 6 4 10 4-4 1-8 0-11-2-3-1-5-4-6-7-1-4 0-8 3-11 2-4 6-7 10-8z"></path><defs><linearGradient id="V" x1="553.062" y1="640.736" x2="561.151" y2="636.67" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#707070"></stop></linearGradient></defs><path fill="url(#V)" d="M545 635h6c5 1 10 2 14 6h-1c0 1-1 1-2 1h-2c2 1 5 0 5 2-2 0-4 0-6-1h-2c0 2 0 2-1 2h-1v-1l1-1h-1v1l-2 2h-1v-1c-1 0-1 1-2 1h0-1c-1 0-1 1-1 2h-4v-2h1c3-2 5-3 9-3v-1h-6s0-1-1-2c2 0 2 1 3 0l-3-3c-1 0-1-1-1-2h-1z"></path><path d="M545 635h6c0 2 1 3 3 4l5 3h-5-6s0-1-1-2c2 0 2 1 3 0l-3-3c-1 0-1-1-1-2h-1z" class="F"></path><path d="M530 576h2 0c2 1 5-1 6 0 1 0 1 0 2 1h2l1 1h2l2 1s1 0 2 1h0c2 1 3 2 4 3h0-1l-2-2c-1 0-2-1-3-1h0c-2-1-3-1-5-2h-3v1h-1l-1 1 1 1h1v2l-2 1v1c1 2 2 4 1 6h-2v-2-1c-1-1-1-2-3-3h-3l-1-1h-2c-1-2-1-3-1-4 1-2 2-3 4-4z" class="O"></path><path d="M526 580h0l2-1h0 1c2 0 4 1 6 2v2l-6 1h-2c-1-2-1-3-1-4z" class="R"></path><defs><linearGradient id="W" x1="420.154" y1="622.103" x2="409.848" y2="621.861" xlink:href="#B"><stop offset="0" stop-color="#2f2e2e"></stop><stop offset="1" stop-color="#474748"></stop></linearGradient></defs><path fill="url(#W)" d="M410 611l1 2h1v-1-1l1 1c1 1 2 2 4 2 1 3 1 6 2 9 0 2 1 5 2 7 0 1 1 2 1 3-5-2-9-7-13-11 2-3 0-4 0-6s0-3 1-5z"></path><path d="M599 668h2c3 0 5 1 8 3v1c-1 0-2 1-3 1-1 2-1 4-2 5 1 2 2 4 2 6v2l-5-1-1-1c0-1 0-4-1-5 0-1-1-1-1-2l-3-5c1-2 2-3 4-4z" class="K"></path><defs><linearGradient id="X" x1="629.395" y1="631.367" x2="629.313" y2="645.191" xlink:href="#B"><stop offset="0" stop-color="#525155"></stop><stop offset="1" stop-color="#807e7e"></stop></linearGradient></defs><path fill="url(#X)" d="M634 626c1 7 0 14-5 19l-2 2c5-1 11-5 15-8 1-2 3-5 5-6 0 1-1 2-2 3-2 3-4 5-7 7l-5 3c-1 1-4 2-6 3v1h0c-1 0-3 1-4 1h-2c-2-1-3 0-4-1v-1h2v-1h-2 0c3-1 5-1 8-4s6-9 7-14c1 0 1-1 1-1h1v-3z"></path><path d="M419 85l6 2c2 4 3 9 5 13s4 6 6 9c-5-2-10-4-14-8 0-1 1-1 1-1l-4-14v-1z"></path><path d="M417 614c4 2 7 3 11 2h0c1 4-1 9-2 13v5c1 0 1 1 1 2l-5-3c0-1-1-2-1-3-1-2-2-5-2-7-1-3-1-6-2-9z" class="E"></path><defs><linearGradient id="Y" x1="569.286" y1="538.069" x2="574.909" y2="510.824" xlink:href="#B"><stop offset="0" stop-color="#111011"></stop><stop offset="1" stop-color="#413f3f"></stop></linearGradient></defs><path fill="url(#Y)" d="M565 508c1 0 3 2 4 3 2 2 3 5 4 7l3 6c2 2 3 5 3 8 1 3 0 7 1 11v3c-1-1-1-4-1-6h-1-1v-7c-2-6-6-10-11-13h1 0v-1c-2-3-2-8-2-11z"></path><defs><linearGradient id="Z" x1="591.764" y1="179.129" x2="576.498" y2="187.141" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#Z)" d="M582 173h0c2 0 3 0 4-1 1-2 2-3 4-3 2 3 4 5 4 8 1 1 1 1 2 1l1-1-1 3s-1 0-1 1c-1-1-4-5-4-5l-1 1v-1c-1 0-1 0-1-1v2c-1 2-1 4-2 6h0c-2 3-4 10-7 11h-1c3-6 5-14 3-21z"></path><path d="M562 376c4 1 7 3 9 6 2 2 3 4 4 6 3 4 7 6 11 6h1 0c-3 1-5 0-8-1 2 1 3 2 4 3 4 4 3 11 3 16h-1c0-7-2-12-7-17 0-1 0-1-1-1h-1v1h-1c-1-3-2-6-2-8 0-1-1-3-2-4v1h-2c-1-1-1-1-2-1h-1l1 1v1h-1-1l-1-1h0 0l-5-4c-1-1-3-1-4-2l5-1 2-1z" class="C"></path><path d="M560 377c3 0 5 0 7 1l4 4v1 1h-2c-1-1-1-1-2-1h-1l1 1v1h-1-1l-1-1h0 0l-5-4c-1-1-3-1-4-2l5-1z" class="O"></path><path d="M567 378l4 4v1c-1 0-2 0-2-1l-3-3 1-1z" class="T"></path><path d="M599 634c2 3 4 5 8 6 2 1 4 1 6 0h2c-1 0-2 1-3 1 0 2 1 2 2 4 1 1 1 2 3 3h0 2v1h-2v1c1 1 2 0 4 1h2l-2 1c-2 0-3 0-5-1h-1s-1 0-2-1h-1 0c-1-1-1 0-2 0s-3-1-4-1h0c-3-1-5-2-7-3l-3-3v-1-1c1 2 3 3 4 4l2 1 1-1c-1 0-1-1-1-2s0-2-1-3c-1-3-2-4-2-6z" class="N"></path><path d="M604 647v-3l1-1c2 2 4 3 6 5l-7-1z" class="P"></path><path d="M596 641c1 2 3 3 4 4l2 1 2 1 7 1h6 2v1h-2v1c1 1 2 0 4 1h2l-2 1c-2 0-3 0-5-1h-1s-1 0-2-1h-1 0c-1-1-1 0-2 0s-3-1-4-1h0c-3-1-5-2-7-3l-3-3v-1-1z" class="O"></path><path d="M519 521c-3-2-6-4-10-5l3-9c5 2 9 5 13 9-1 4 0 7 1 11l-1 1c-1-3-3-5-6-7z" class="G"></path><path d="M639 414l8-3 3 24-1 1 1 1v-1c1 0 1 0 1-1v4c-2-2-4-5-7-6 0-2-1-4-1-6 0-3-1-6-2-9 0-1-1-3-2-4z" class="B"></path><defs><linearGradient id="a" x1="608.598" y1="629.458" x2="599.838" y2="627.709" xlink:href="#B"><stop offset="0" stop-color="#414040"></stop><stop offset="1" stop-color="#626163"></stop></linearGradient></defs><path fill="url(#a)" d="M607 639c-2-1-5-2-6-4-2-3-2-7-1-10 1-4 4-7 7-9 0 2-1 6 0 7l2 2c2 3 1 5 0 8 0 2-1 3-2 5v1z"></path><path d="M653 651c-1-1-1-2-2-3s-2-1-3-1c-2-1-4-1-6-1-1 1-3 1-4 1h-3c0-1 1-2 2-2 1-1 1-1 2-1s2 0 4-1v1c-2 1-3 1-5 1l-1 1c2 1 7-1 9-1 3 0 8 1 11 2v1c0 4 1 8 1 12l5 19h1c-2 1-3 1-4 2-2-2-2-6-2-8l-3-12c-1-3-1-8-2-10z" class="L"></path><path d="M588 683v-1c-1-4 2-9 0-11-1-1-1-2-2-2 0-2 0-2 1-3l1-1v1l2 1c0 1 1 2 2 3 2 0 2-1 4-2h3c-2 1-3 2-4 4l3 5c0 1 1 1 1 2 1 1 1 4 1 5l1 1h-1l-9-1-3-1z" class="E"></path><path d="M591 684c0-3 0-5 3-7 1 0 2 1 3 2s2 5 3 6l-9-1z" class="S"></path><defs><linearGradient id="b" x1="286.617" y1="173.54" x2="292.093" y2="154.569" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#313031"></stop></linearGradient></defs><path fill="url(#b)" d="M275 149h1c0-1 0-1 1-2h0v3h0c1 0 2-1 2-2v-4c0-1 0-1 1-2v-1-1c0 1 0 2 1 2v1l-1 1h0v1h0c-1 7 1 16 5 22 3 4 7 6 12 7 2 0 4 0 6-1l-1 6c-7-2-16-4-20-11l-3-6v-1 1l-1-1h-1v3-6c-1-2 0-7-1-8l-1-1z"></path><path d="M277 158h0v-7c1 2 1 5 1 8h1v2 1l-1-1h-1v3-6z" class="C"></path><path d="M287 127s1 1 2 1c1 1 2 2 4 2 1 1 3 2 4 3 1 0 2 0 2 1v1h1 3v1c0 1-1 0-2 1v-1c-1 1-1 1-2 1v2c0 1 0 1-1 2-1-1 0-3 0-5h0c-3 0-6-1-9 0-2 1-2 4-3 6v2c-1 0-1-1-2-2l-2-4-1-1-1 1c0-3 1-7 2-10h1l4-1z" class="L"></path><path d="M282 138c1 0 2 0 3 1h0c0 2 0 2-1 3l-2-4z" class="N"></path><path d="M298 136h0c-2-4-9-2-11-5h1c3-1 7 1 10 3l1 1v-1 1h1 3v1c0 1-1 0-2 1v-1c-1 1-1 1-2 1v2c0 1 0 1-1 2-1-1 0-3 0-5h0z" class="O"></path><path d="M397 72c4-1 8 0 12 0 7-1 14-1 21 0h-9l3 9c1 1 1 2 1 3l-6-1c-3-1-7-2-10-3l1-1c-1-2-2-5-2-7-3-1-8 0-11 0z" class="L"></path><path d="M419 83l-3-6c0-2-1-4-1-5h6l3 9c1 1 1 2 1 3l-6-1z" class="K"></path><path d="M564 384h0l1 1h1 1v-1l-1-1h1c1 0 1 0 2 1h2v-1c1 1 2 3 2 4 0 2 1 5 2 8h1c0 1 1 2 0 3-1 2-3 2-5 2h-1c-1 0-1 0-2-1h0c0-1 0-2-1-3-1 1-3 4-4 5v1c-1 0-1-1-1-2l-1-2c1-2 2-5 3-7l2-2-1-3-1-2z" class="L"></path><path d="M564 384h0l1 1h1 1v-1l-1-1h1c1 0 1 0 2 1h2v-1c1 1 2 3 2 4v1l-1 1-1-1c0-1-1-2-2-2h-1c-1 1-2 1-3 0l-1-2z" class="U"></path><path d="M568 399l1-1c1-1 0-3 0-5 1-1 2-1 3-1l1 1c0 1 1 2 2 3v1c-1 1-3 1-4 2v1h-1c-1 0-1 0-2-1h0z" class="H"></path><path d="M566 389s0 1 1 1h1c1 0 2 0 3 1l1 1c-1 0-2 0-3 1 0 2 1 4 0 5l-1 1c0-1 0-2-1-3-1 1-3 4-4 5v1c-1 0-1-1-1-2l-1-2c1-2 2-5 3-7l2-2z" class="M"></path><path d="M502 571v-1c3-4 7-4 12-4l-4 4-1 1 1 1c0 2 0 4 1 7s2 5 2 8v1c-2 1-5 2-8 3 1-1 1-1 1-2-1-1 0-4 0-5-1-1-1-2-1-3v-2c-1 0-1 0-1-1v-2c-1 0 0 0 0-1l-1-1v-2l-1-1h0zm133-219l-1-11c5 0 11 4 14 6 1 2 1 4 3 5-1 2-3 5-4 6h0-1c-4-1-7-6-11-6z" class="L"></path><defs><linearGradient id="c" x1="515.781" y1="597.126" x2="523.389" y2="590.846" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#c)" d="M510 592c1-1 2-1 2-1 3-1 6-2 8-2h0 2v1h4c0 2 0 5 1 7 0 1 1 1 1 1v1h0c-3 0-4 1-5 2l-2 3h-1c-1 1-1 1-2 1-1-2-3-4-4-6s-2-3-3-5v-2h-1z"></path><defs><linearGradient id="d" x1="355.224" y1="103.222" x2="347.933" y2="82.966" xlink:href="#B"><stop offset="0" stop-color="#413f41"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#d)" d="M344 81c2 0 7 2 9 3 0 0 1 1 2 1v3c2 3 1 7 2 11 1 2 1 4 1 5l-1-1-3 3-1-2c-2-3-3-7-5-11-1 0-1-2-2-2l-1 1v-2-3c0-2 0-5-1-6z"></path><path d="M606 652h4s1 1 2 1l4 1c9 4 20 11 23 20 1 3 2 7 1 9 0 1-1 2-2 2-2 1-3 1-3 0-1-1-1-2-1-3l1-1c1-1 2-2 2-4-1-3-3-6-6-8 0 1 1 3 0 5 0 1 0 1-1 1h-1l1-1c0-2 0-4-1-5-2-5-7-10-12-12-1-1-3-1-4-2h0v1 1c-2-2-4-4-6-5h-1z"></path><path d="M534 623c-2-1-4-1-6-2-2-2-2-3-3-6 0-2 0-3 1-5 2-1 4-3 6-3h2l1 1c0-1 1-1 2-1l4 4c0 2-1 6-2 8-1 1-2 2-4 2-1 1-1 1-2 1l1 1h0z" class="C"></path><path d="M531 613c1-1 2-3 3-3l1 1c0 1-1 2-2 2h-2z" class="V"></path><path d="M535 611h0l2 2c0 1-1 1-2 2v1h1c-1 1-1 2-2 2s-2 1-3 0-2-1-2-2c0-2 1-2 2-3h2c1 0 2-1 2-2z" class="Q"></path><path d="M531 618v-2s0-1 1-2h2l1 2h1c-1 1-1 2-2 2s-2 1-3 0z" class="O"></path><path d="M619 279c6 2 10 9 12 15 2 4 3 8 3 12-2-2-8-11-10-11h0-2c-1-2-3-4-4-5-1-4-3-7-2-11h2 1z" class="I"></path><path d="M363 89c2 0 3 1 5 1l12 3 3 3h0c1-1 2-1 3-2v10c0 1-1 0-2 0-1-2-2-5-4-6-1-2-5-3-6-3 3 2 6 6 7 10h0c-2 0-2 0-4-1-2 0-3 0-5 1-3-6-5-11-9-16z" class="K"></path><defs><linearGradient id="e" x1="564.509" y1="165.8" x2="553.976" y2="170.511" xlink:href="#B"><stop offset="0" stop-color="#0d0c09"></stop><stop offset="1" stop-color="#282829"></stop></linearGradient></defs><path fill="url(#e)" d="M560 181c-1 0-1-1-2-2-3-3-4-7-4-12 0-4 2-7 4-10 1-2 2-4 4-6 5-5 12-8 18-13h0 1c-2 2-4 3-6 5h1 2 1l2-1h0 1l-1 1c2 1 7 0 8 0h1c0-1 1-1 1-1v1s-1 1-2 1c-1 1-1 0-2 1h-1c-3 0-6 0-9 1-2 0-5 1-7 2h0v1 1c-6 2-10 8-12 14h0c0 3-1 5 0 8v1c0 2 2 5 4 7-1 0-1 0-2 1h0z"></path><path d="M575 143h1 2 1l2-1h0 1l-1 1c2 1 7 0 8 0h1c0-1 1-1 1-1v1s-1 1-2 1c-1 1-1 0-2 1h-1c-3 0-6 0-9 1-2 0-5 1-7 2h0c-4 2-7 4-11 7 4-5 10-9 16-12z" class="T"></path><path d="M533 470l1 1c0 1-1 3-1 4-1 2-5 6-5 8h1v8c-1 0-2 0-3 1 2 0 4 1 5 3-1 2-8 2-11 2-2 1-3 1-5 2l4-14h1v2h0l2-2 11-15z"></path><path d="M528 483h1v8c-1 0-2 0-3 1v-2c0-3 2-5 2-7z" class="C"></path><path d="M532 647c-3 0-6 0-8-1 5-3 8-5 14-5-3-2-6-5-9-7 3 0 7 1 11 1h5 1c0 1 0 2 1 2l3 3c-1 1-1 0-3 0 1 1 1 2 1 2-2 2-4 3-6 4s-3 2-5 4l-1-1c-1-1-1-2-2-2h-1l-1-1v1z"></path><path d="M521 187c7-7 21-11 30-9 2 1 3 1 5 2 1 0 3 1 4 1h0c1 1 2 1 2 2h-5c-2-1-4-1-7-1h-1c-2-1-5 0-6 0l-2 1-1 1c-1 0-2 1-3 1s-1 1-2 1h0c-1 0-1 1-2 1l-1 1-2 1c0 1 0 0-1 1-1 0-1 1-2 2v-1c-2 0-3-1-4-2l-2-2z" class="I"></path><path d="M277 164v-3h1l1 1c1 9 2 19 10 25 3 1 5 2 8 3-5 0-7 0-12-3l6 6 3 3c-7-2-9-7-13-12l1 7v1c-2-2-3-9-3-12l-2 10h-1c-1 0-1 0-1-1-1-3 0-7 0-10h0c-3 2-5 3-8 4-1 0-2 1-3 1 1-1 2-1 3-2 2-1 7-2 8-4s2-5 2-6v-2-6zm302 465v-2-1h-1 0 1 1 1c0 1 1 1 1 1l3 4c1 1 1 1 3 2 2 3 4 7 8 10l3 3h0c-4-1-7-2-11-3-3 0-6 0-9-2-2-1-3-2-5-4 0-1 1-2 2-3h1l1-1-3-1-1-1h0c-1 0-2-1-2-1v-3c3 0 3 2 5 4h4l-2-2z"></path><defs><linearGradient id="f" x1="582.222" y1="638.039" x2="579.039" y2="641.024" xlink:href="#B"><stop offset="0" stop-color="#121314"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#f)" d="M574 637c0-1 1-2 2-3h1 0l-2 2c1 1 3 1 4 2 2 2 5 2 7 3 1 0 1 1 2 2-3 0-6 0-9-2-2-1-3-2-5-4z"></path><path d="M572 630v-3c3 0 3 2 5 4h4c0 1 1 2 2 3 1 2 3 3 4 4-3-2-6-3-10-4h0l1-1-3-1-1-1h0c-1 0-2-1-2-1zm-2-482h0c2-1 5-2 7-2 3-1 6-1 9-1h1l6 2v1c1 1 1 2 2 2v1c-2 0-5-1-6 0l-1-1c-1 1-1 3-1 4 0 4-1 11-5 12-1 0-2 0-3 1-1-1-2-1-3-2 1-1 3-3 3-5 0-1-1-2-1-3 0 0 1 0 1-1l-1-8c-3 0-6 1-8 2v-1-1z" class="E"></path><path d="M586 145h1l6 2v1c1 1 1 2 2 2v1c-2 0-5-1-6 0l-1-1c-1 1-1 3-1 4 0 4-1 11-5 12 1-2 2-4 2-6l2-12c-1-1-1-1-1-2l1-1z" class="D"></path><path d="M534 623c9 2 20 8 28 14 1 1 3 2 5 4l4 4h1c1 0 1 1 2 2h0v1c1 1 1 2 2 3v6h-1-2c-2 1-3 3-5 4-3 4-7 7-8 12-2 0-4-1-5-1 2-1 2-2 3-3 2-4 5-5 8-7v-2h-1c0-1 0-2 1-2v-2-2h1 0c0-1-1-2-1-2v-3c1-1 1-1 1-2h0l1-1v-1h1l-1-2v1h-1v-1l-1-1c-1-4-9-7-12-9-4-2-8-5-12-6-3-2-6-3-8-4h0z" class="N"></path><path d="M396 609c0-1-1-3 0-4l2 1v1c5 6 9 14 14 20 0 2 0 3-1 5v1l-1 1h0c-3-1-5-1-7-3-3-2-6-5-9-8 2 0 6 2 8 1v-1c-4-3-5-10-6-14z"></path><path d="M396 609l1-1c1 1 1 2 1 3 2 3 4 6 4 10v2c-4-3-5-10-6-14z" class="E"></path><path d="M637 333h1c2-1 10 0 12 0l2-1v-1c1 6 1 11 0 17l-1 4c-2-1-2-3-3-5-3-2-9-6-14-6 1-3 2-5 3-8zM360 87c-2-2-4-3-6-5-2-1-4-1-5-2l12-4 8 5c3 2 6 3 8 6 2 2 2 4 3 6l-12-3c-2 0-3-1-5-1h0l-2-1-1-1z"></path><defs><linearGradient id="g" x1="367.351" y1="83.305" x2="372.936" y2="94.216" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#515152"></stop></linearGradient></defs><path fill="url(#g)" d="M361 88c1-1 1-2 1-2 1-1 2-2 3-2 3 0 8 1 11 3v1l1-1c2 2 2 4 3 6l-12-3c-2 0-3-1-5-1h0l-2-1z"></path><path d="M576 395v-1h1c3 5 5 10 5 15-1 5 0 10-3 14-2 3-4 6-7 8h-1-3c-2-1-3 0-5 1h-1-1l3-1c3-2 5-6 5-9l1-4v-1h1v8c4-5 5-10 6-15 1-2 2-4 2-6s0-2-1-3l-2 1c-2 0-5 1-8 0v-3c1 1 1 1 2 1h1c2 0 4 0 5-2 1-1 0-2 0-3z" class="K"></path><path d="M579 423v-1c-1 0-1 0-2-1 0-1 1-2 1-3s1-1 1-2v-1c1-1 0 0 0-1l1-1c0-2 0-2 2-4-1 5 0 10-3 14z" class="D"></path><path d="M653 651c1 2 1 7 2 10l3 12c0 2 0 6 2 8-3 0-5 1-7 2v-1c-2-3-2-7-3-11l-4-18v-1h0c1 0 4 1 5 0 1 0 1 0 2-1z" class="N"></path><path d="M642 395c3-6 7-12 6-19v-1c1 0 1 0 2-1l1 1c2 2 2 9 2 12h0l-1 1c1 1 1 1 1 2-1 4-1 12 1 15l-8 3c-1-1 0-4 0-5-1-3-2-6-4-8z" class="K"></path><defs><linearGradient id="h" x1="539.396" y1="555.581" x2="540.65" y2="561.168" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#6e6d6d"></stop></linearGradient></defs><path fill="url(#h)" d="M542 554c1 0 1-1 2 0 0 1 0 2 1 3h1c1 0 3 0 4 2 1 1 1 2 2 3l5 2h-2 0-1c-1 0-3 0-4-1v-1h-1c-2-1-4-1-6 0h-1v1c-2 3-6 7-8 9h-1c-1 2-2 3-4 4h-2 0l-1-1-1-6c-1-1-1-2 0-3h5 0l1-1c1-4 7-8 10-10l1-1h0z"></path><path d="M532 565c2-2 3-3 6-3h3l-2 2c-2 2-4 5-6 8-1 2-2 3-4 4h-2 0l-1-1-1-6c-1-1-1-2 0-3h5 0l1-1h1z" class="R"></path><path d="M531 565h1l-5 11-1-1-1-6c-1-1-1-2 0-3h5 0l1-1z" class="G"></path><path d="M598 610h1c1 0 3 1 4 2-3 4-5 9-5 14 0 3 0 5 1 8 0 2 1 3 2 6 1 1 1 2 1 3s0 2 1 2l-1 1-2-1c-1-1-3-2-4-4-4-6-6-12-5-19 1-5 3-9 7-12z"></path><path d="M598 610h1c1 0 3 1 4 2-3 4-5 9-5 14 0 3 0 5 1 8 0 2 1 3 2 6 1 1 1 2 1 3s0 2 1 2l-1 1-2-1h0c1-1-1-5-1-6-1-7-4-15-3-22l3-6-1-1z" class="H"></path><path d="M571 603c1-2 3-5 5-7l2 2 8 4-1 1c-3 3-4 8-4 12l1 5h0c-1 1-1 1-2 1l-1 3v1c-3-3-4-6-6-9-1-1-1-2-2-2-1-2-2-4-3-7 3 3 4 6 6 8 0-3-1-6-2-9 0-1 0-2-1-3z" class="L"></path><path d="M578 598l8 4-1 1c-3 3-4 8-4 12v-2c-1 1-1 3-2 5h0l-1-1v-1c-1-3-1-6-1-10 1-1 1 0 1-1-1-2 0-3 0-4s-1-1-1-3l1 1v-1z" class="N"></path><defs><linearGradient id="i" x1="584.183" y1="444.708" x2="597.636" y2="437.222" xlink:href="#B"><stop offset="0" stop-color="#565657"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#i)" d="M582 436c1 0 15-6 16-6l5 13c1 2 2 4 2 5-2-1-4-2-6-2-5-2-11-2-16-2v-1-5c0-1 0-1-1-2z"></path><path d="M459 73h1v1h1c2-1 4-1 5 0h1c1 1 0 1 0 2h0c2-1 3-2 5-2l1 1v1c1 0 3-1 4-1l1 1h0l2-1c1 1 2 1 2 2-7 4-17 8-25 9h-5c1-1 2-1 3-3h0s-1 0-1-1c1-1 1-2 2-3v-1c-1-1-2-2-2-4l1-1 3-1c0 1 1 1 1 1z"></path><path d="M459 73h1v1h1c-2 3-3 6-6 9 0 0-1 0-1-1 1-1 1-2 2-3v-1c-1-1-2-2-2-4l1-1 3-1c0 1 1 1 1 1z" class="O"></path><path d="M458 72c0 1 1 1 1 1 1 1 0 2-1 3l-2 2c-1-1-2-2-2-4l1-1 3-1zm124 94c4-1 5-8 5-12 0-1 0-3 1-4l1 1 2 1h0c0 1 1 3 1 4 1 1 2 3 3 5 3 5 3 10 2 15v1l-1 1c-1 0-1 0-2-1 0-3-2-5-4-8-2 0-3 1-4 3-1 1-2 1-4 1h0l-3-6c1-1 2-1 3-1z" class="K"></path><path d="M449 598c0-1 5-3 6-3 4-3 8-6 12-10-1 4-2 7-3 11-1 3-2 6-2 10 0 2-1 4-1 6-5-3-8-4-13-5 0-3 2-7 1-9z" class="N"></path><defs><linearGradient id="j" x1="619.096" y1="409.254" x2="634.291" y2="399.12" xlink:href="#B"><stop offset="0" stop-color="#626060"></stop><stop offset="1" stop-color="#7d7c7e"></stop></linearGradient></defs><path fill="url(#j)" d="M622 396c1-1 4-2 5-2l7-4v9c-1 4-6 12-3 15l-13 5v-1c1-4 4-8 5-12 0-2 1-8-1-10z"></path><defs><linearGradient id="k" x1="521.23" y1="203.702" x2="509.602" y2="199.581" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#k)" d="M521 187l2 2c1 1 2 2 4 2v1c-1 1-2 2-2 3-2 2-3 3-4 5-1 1-1 2-2 3v1 1c2-1 2-2 3-3 0 1-1 1-1 2v1c-1 0-1 1-1 1v1l-1 1h0v1c-2 2-2 3-2 5s0 3-1 4h-1l-6-19c3-5 8-9 12-12z"></path><path d="M355 85l4 3 1-1 1 1 2 1h0c4 5 6 10 9 16h0-2v-1-1l-1-2v-1 2l1 1v3 1l-9 4-2 1c0-3-1-5-1-8 0-1 0-3-1-5-1-4 0-8-2-11v-3z" class="G"></path><path d="M360 87l1 1 2 1h0c4 5 6 10 9 16h0-2v-1-1l-1-2v-1 2l1 1v3c-1-2-1-3-2-5-2-5-5-9-9-13h0l1-1z" class="R"></path><path d="M355 85l4 3h0l-1 1c0 5 3 11 3 16 0 1-1 4 0 5v1l-2 1c0-3-1-5-1-8 0-1 0-3-1-5-1-4 0-8-2-11v-3z" class="C"></path><path d="M435 89h2 12c0 2 1 3 2 4l2 1h1 1c1 1 3 2 4 3h0c-3 1-9 0-12 0 1 1 3 2 3 4l-1 1c-2 1-6-1-9-1 1 2 2 3 3 4v1c-4-1-6-2-9-4 2-5-1-8-1-13h2z"></path><path d="M437 89h12c0 2 1 3 2 4l2 1c-3 0-7-2-10-3-1-1-2-1-3-2h-3z" class="F"></path><path d="M276 150c1 1 0 6 1 8v6 6 2-2h0-1c-2 4-5 6-9 7-1-1-3-2-5-2v-1h1v-1h-1 0l-1-1h2v-1c-2 0-4 1-6 2v-1-1c1-2 3-4 4-5 1 0 1 0 2 1v-2h0l5-3c3-4 7-7 8-12z" class="L"></path><path d="M270 170h1c0 1 1 2 0 4h-1 0c-1-2-1-3 0-4z" class="P"></path><path d="M268 162v1c1 1 2 1 3 1 0 0 0 1-1 2v-1h0c-1-1-1-1-2-1l-2 2h0-2l-1-1 5-3z" class="T"></path><path d="M276 150c1 1 0 6 1 8v6 6c-1-1-2-2-3-2s-2-1-2-1v-2c-1 0-1 1-1 1h-1c1-1 1-2 1-2-1 0-2 0-3-1v-1c3-4 7-7 8-12z" class="R"></path><defs><linearGradient id="l" x1="618.091" y1="629.49" x2="628.199" y2="638.329" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272627"></stop></linearGradient></defs><path fill="url(#l)" d="M615 640h0c3-1 6-4 8-7 2-4 2-8 1-12h0c3 0 6 1 8 2s2 2 2 3v3h-1s0 1-1 1c-1 5-4 11-7 14s-5 3-8 4c-2-1-2-2-3-3-1-2-2-2-2-4 1 0 2-1 3-1z"></path><path d="M632 623c2 1 2 2 2 3v3h-1s0 1-1 1v-3-4z" class="B"></path><path d="M570 150c2-1 5-2 8-2l1 8c0 1-1 1-1 1 0 1 1 2 1 3 0 2-2 4-3 5-4 0-6 0-9 1h-2l-1-1c-2-2-3-1-5-1h-1 0c2-6 6-12 12-14z" class="T"></path><path d="M559 164c1-1 1-2 2-2h1l2-1c0-1 1-1 2-1l1-1h1l3-1h1 3c1-1 2-1 3-1 0 1 1 2 1 3 0 2-2 4-3 5-4 0-6 0-9 1h-2l-1-1c-2-2-3-1-5-1z" class="O"></path><path d="M634 390h0c2-1 4-4 5-6 2 5 2 9 1 13h1l1-2c2 2 3 5 4 8 0 1-1 4 0 5l-10 4-5 2c-3-3 2-11 3-15v-9z"></path><path d="M640 397h1l1-2c2 2 3 5 4 8 0 1-1 4 0 5l-10 4v-1c0-4 1-11 4-14z" class="F"></path><path d="M639 649c3 1 5 2 7 3h0v1l4 18c1 4 1 8 3 11v1l-6 1c-3-5-5-10-6-15-1-2-2-5-2-7s0-4 1-6l-1-1v-1-5zm-62-168v-2c0 1 1 1 1 2 1 0 3 1 4 1 1 2 2 4 3 7l5 8c0 2 1 4 1 6-4 3-8 5-11 9v-1-7h0l-4-13v-1h-1 0v-4h0c0-1 0-1 1-2v-1c1-1 1-1 1-2z"></path><path d="M577 481v-2c0 1 1 1 1 2 1 0 3 1 4 1 1 2 2 4 3 7l5 8c0 2 1 4 1 6-4 3-8 5-11 9v-1-7l1 1v4l3-2 1-1 2-1v-1c1-1 1 0 1-1l1 1 1-1v-1c0-1 0-1-1-1 0-1 0-2-1-3 0-3-2-5-4-7-1-2-1-5-2-6l-3-3-1 1c-2 3-2 4-2 7h-1 0v-4h0c0-1 0-1 1-2v-1c1-1 1-1 1-2z" class="D"></path><defs><linearGradient id="m" x1="555.12" y1="608.919" x2="564.362" y2="607.471" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient></defs><path fill="url(#m)" d="M552 589c2 0 7 1 8 4l1 1h1l1-1c1 2 0 3 0 5-1 3-2 7-2 11-1 5 3 12 6 16v1h1c0 1 1 1 1 2-1 0-1-1-1-1h-1v1h0c1 1 1 1 1 2-2 0-5-2-7-3-1-1-1-2-3-2 0 0-1 0-1-1h0l2-1h0l-2-3v-2c-1-1-1-3-1-4-2-5-2-11-2-17 0-2 0-5-1-7-1 0-1 0-1-1h0z"></path><path d="M596 410c1-4 3-8 3-12 1-3 1-5 0-8 4 5 6 9 7 15 1 2 0 3 0 5 0 6 0 8 4 12l-11 4h-1c-1-4-3-11-2-16z"></path><defs><linearGradient id="n" x1="558.727" y1="281.712" x2="549.224" y2="302.795" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#929192"></stop></linearGradient></defs><path fill="url(#n)" d="M551 277c1 0 1 1 1 1l-1 1c3 1 6 1 8 4 1 1 1 3 1 5 1 2 4 5 6 5-1 2-4 5-5 6l-10 12c1-4 1-8-1-12 0-1-1-2-1-3 1-1 0-3 0-4 0-3 0-6 1-9 0-2 1-4 1-6z"></path><path d="M550 299c2 2 2 4 3 6 0-2 1-4 2-6h4 2l-10 12c1-4 1-8-1-12z" class="N"></path><path d="M520 567l5-1c-1 1-1 2 0 3l1 6 1 1h0 2 1c-2 1-3 2-4 4 0 1 0 2 1 4h2l1 1-8 1-9 2v-1c0-3-1-5-2-8s-1-5-1-7l-1-1 1-1v1c2-1 3-2 4-2 2-1 4-2 6-2z" class="P"></path><path d="M520 567l5-1c-1 1-1 2 0 3l1 6 1 1h0 2 1c-2 1-3 2-4 4 0 1 0 2 1 4h2l1 1-8 1c-1-1 0-3 0-4 0-3 0-6-1-9 0-2-1-4-1-6z" class="E"></path><defs><linearGradient id="o" x1="557.001" y1="647.697" x2="562.747" y2="647.616" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#o)" d="M565 641l1 1 1 1v1h1v-1l1 2h-1v1l-1 1h0c0 1 0 1-1 2v3s1 1 1 2h0-1v2 2c-1 0-1 1-1 2h1v2c-3 2-6 3-8 7-1 1-1 2-3 3l-5-3h1c2-4 4-9 6-14l-5-9h1l2-2v-1h1l-1 1v1h1c1 0 1 0 1-2h2c2 1 4 1 6 1 0-2-3-1-5-2h2c1 0 2 0 2-1h1z"></path><path d="M559 643c2 1 4 1 6 1l1 1-7 1h-1l1-3z" class="H"></path><path d="M552 646h1l2-2v-1h1l-1 1v1h1c1 0 1 0 1-2h2l-1 3h-2c0 2 0 2 1 3 1 3 3 5 3 8-1 1-1 2-2 3s-2 3-3 5v6 1l-5-3h1c2-4 4-9 6-14l-5-9z" class="J"></path><path d="M610 616c3 0 9 3 13 4 1 4 1 9-1 13-2 3-5 5-8 6l-3 1-4-1v-1c1-2 2-3 2-5 1-3 2-5 0-8l-2-2c-1-1 0-5 0-7h3z"></path><path d="M607 616h3c0 2 0 3 1 4 2 3 2 9 1 12 0 2-2 3-2 6 0 1 1 1 1 2l-4-1v-1c1-2 2-3 2-5 1-3 2-5 0-8l-2-2c-1-1 0-5 0-7z" class="J"></path><path d="M657 647c4 2 7 4 11 7 0 0-1 1-1 2s2 5 3 7c2 4 4 7 5 11l-11 5h-1l-5-19c0-4-1-8-1-12v-1z"></path><defs><linearGradient id="p" x1="555.213" y1="285.27" x2="540.754" y2="291.163" xlink:href="#B"><stop offset="0" stop-color="#343433"></stop><stop offset="1" stop-color="#646364"></stop></linearGradient></defs><path fill="url(#p)" d="M537 281h1c1 0 1-2 1-3 3-6 6-9 12-12-3 6-5 12-4 19 0 4 1 7 2 11 0 1 1 2 1 3 2 4 2 8 1 12l-4 7c0-4 1-8-1-11-1-2-2-3-3-4 0-1-1-3 0-4v-1c2-6-2-6-4-10h0c-2-3-2-4-2-7z"></path><path d="M544 648h4c0-1 0-2 1-2h1 0c1 0 1-1 2-1v1l5 9c-2 5-4 10-6 14h-1l-14-6v-2c1-2 1-3 1-4 1-2 4-4 6-6 1-1 1-2 1-3z"></path><path d="M578 429l1-1 5-6c4-6 6-12 6-20 2 2 4 3 5 5 0 1 0 2 1 3h0c-1 5 1 12 2 16h1l-22 10v-2c0-2 1-2 1-5z" class="N"></path><path d="M587 183c1-2 1-4 2-6v-2c0 1 0 1 1 1v1l1-1 4 5c0-1 1-1 1-1 0 2 0 2 2 3v1h0l-1 1 1 1v1c0 5-3 8-6 12 0 1-1 2 0 3 0 1 0 1 1 2h1v1s-1 1 0 1c-2 2-3 4-4 5-1 2-3 4-4 5l-1-20v-1l-1 1-1-1c0-2 3-7 4-9v-3z" class="K"></path><path d="M587 183c1-2 1-4 2-6v-2c0 1 0 1 1 1v1l1-1 4 5c0-1 1-1 1-1 0 2 0 2 2 3v1h0l-1 1 1 1c-1 1-1 2-3 3-1 0-2 0-3-1h-3c-1 1-2 2-2 3 0 2 0 4-1 5h-1v-1l-1 1-1-1c0-2 3-7 4-9v-3z" class="E"></path><path d="M586 196l-1-1h-1c0-2 1-4 2-5 0-2 2-4 3-5 1 0 2 0 3 1 2 1 4 0 6 0-1 1-1 2-3 3-1 0-2 0-3-1h-3c-1 1-2 2-2 3 0 2 0 4-1 5z" class="F"></path><path d="M596 280c0-1 0-2-1-3v1h0v1l-1-2c0-1-1-1-1-2-1-1-1-2-1-3s-1-2-1-2l-1-1c-1-2-2-4-2-6h1c0-1 0 0-1-1 1-1 1-1 3-1 4 1 10 0 14 1v7l2 3h0c0 1 1 2 2 3 1 2 2 4 1 7-1 1-2 1-3 1h-1c-3-1-6-3-10-3z" class="H"></path><path d="M640 311c1-2 3-4 4-6 2-3 3-7 2-10-1-6-5-9-10-11l9 1 4 4v1l1-1 1 2c1 1 2 2 2 3 1 1 1 4 0 6 0 1 0 3-1 4v1l-1 3c-2 1-3 4-5 5h-1c0 1-1 1-1 2 3 3 5 6 6 9h0-3 0c-1 1-4 4-5 4 0 1-1 1-2 1l2-2-1-1h-1c-1 0-2 0-3 1l-1-1c1-2 3-4 4-6l1-1v-1h-1v-2c-1-1-1-3 0-5z"></path><path d="M649 290l1-1 1 2c1 1 2 2 2 3 1 1 1 4 0 6 0 1 0 3-1 4v1l-1 3c-2 1-3 4-5 5h-1c2-3 5-6 6-9 1-5 0-10-2-14z" class="W"></path><path d="M583 326l19 5c-3 1-7 1-10 2 1 1 3 1 5 2 3 3 8 9 8 14h0c0 1 1 3 1 4-5-5-12-9-18-12 3 4 4 5 5 10l-6-6c0-1-1-2-2-3h0c-2-2-4-5-4-8 0-2 1-3 1-5 0-1 0-2 1-2v-1z"></path><path d="M597 335c3 3 8 9 8 14h0-1 0v-2s-1-1-1-2h-1c-2-2-5-5-6-7 0-1 1-2 1-3z" class="C"></path><path d="M642 251l5-2 2 13h-3c-2 1-4 3-6 5l-1-1 1-2h-1c-2 1-4 2-5 4 0 1 0 2-1 3h0c0 1 0 4 1 5s2 2 4 2c4 0 9-2 13-3h0c-5 2-13 6-19 4-3-1-6-5-7-8-1-1-1-2-1-4l1-1c1-2 0-4 1-5v4c0 1 1 1 1 2v6-3c1-5 2-8 6-12l6-6c1 0 2-1 3-1z" class="G"></path><path d="M642 251c2 3 0 9 0 12-1-1-1-2-1-4-1-1-1-2-2-3v-4c1 0 2-1 3-1z" class="C"></path><path d="M627 273v-3c1-5 2-8 6-12 0 2-1 3-1 5-1 2-1 5-2 7h0-1-1v4l-1-1z" class="M"></path><path d="M363 632l2-1h0l-5-2c5-1 10-2 15-1l-4-5c7 0 14 0 19 5 3 2 5 5 6 8 0 1 0 1-1 0 0 2 3 7 4 9-1 0-4-1-5-1-2-1-3-4-4-5-6-5-13-6-20-3-2-2-5-2-7-4z"></path><defs><linearGradient id="q" x1="298.839" y1="169.214" x2="302.712" y2="147.216" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#q)" d="M282 147l4 3c1 0 2 0 3-1 1 0 1-1 1-1h3c1 0 2 1 3 1l7-3c0 1 1 3 1 4l8 1c-2 3-5 7-10 8-1 0-3 0-4-1l-1-1h0-1c3 4 9 5 11 9-1 2-3 4-6 4-2 0-6 0-8-2l-2-2-2-2v-3c1-1 2-1 2-3h1c1-2 1-3 1-5v-1c-1 0-1 0-2 1l-1 1h-2c-1-1-2-2-2-3h-1c0-1-2-2-3-4z"></path><defs><linearGradient id="r" x1="299.083" y1="167.184" x2="295.93" y2="159.142" xlink:href="#B"><stop offset="0" stop-color="#212124"></stop><stop offset="1" stop-color="#42413f"></stop></linearGradient></defs><path fill="url(#r)" d="M291 158h1c3 2 10 4 11 9l-1 1c-4-1-6-8-11-5v3l-2-2v-3c1-1 2-1 2-3z"></path><path d="M735 574c5-10 8-21 11-32 1 16 0 34-4 50-1 6-3 11-4 16-1-2-2-3-2-4v-6-8c0-3 1-6 1-9h0l-1-1v-2h0c-1-2-1-2 0-3l-1-1z" class="C"></path><path d="M736 575l2-6h1v2c1-1 1-1 1-2l2-4h0c1 2-1 6-2 8 0 2-1 4-2 5 0 1-1 2-1 3l-1-1v-2h0c-1-2-1-2 0-3z" class="E"></path><path d="M740 573c0 1 1 1 1 2 1 1 0 3 0 4l1 9c-1 1-1 3 0 4-1 6-3 11-4 16-1-2-2-3-2-4v-6-8c0-3 1-6 1-9h0c0-1 1-2 1-3 1-1 2-3 2-5z" class="H"></path><path d="M735 574l1 1c-1 1-1 1 0 3h0v2l1 1h0l-10 19v3c0 2-2 3-4 4 0 1-1 1-2 2l-3 2-1 1c-1 0-1 1-2 1l-1-1-1 2-1-1c-1 1-1 1-2 1v-1c-2-2-1-6 0-8l3-3c0-1 4-4 5-5l2-2 7-8 8-13z" class="I"></path><path d="M720 595c1 2 1 6 1 9 0 1 0 1-1 1-1-2 0-5-2-8l2-2zm-7 7v1c0 1 0 2-1 4v6c-1 1-1 1-2 1v-1c-2-2-1-6 0-8l3-3z" class="J"></path><path d="M727 600v3c0 2-2 3-4 4 0 1-1 1-2 2l-3 2-1 1c-1 0-1 1-2 1l-1-1 2-2c1 0 2-1 2-1 1-1 4-3 5-4v-1c1-1 1-1 1-2 1-1 2-1 3-2z" class="E"></path><path d="M735 574l1 1c-1 1-1 1 0 3h0v2c-3 5-5 11-9 16 0-3 1-7 0-9l8-13z" class="K"></path><path d="M563 441c3 0 6-2 8-2h1l-1 1h1l3-1h2c1 1 1 1 1 2l-1 1s1 0 2 1l-1 1v1c4 0 7 1 9 4-6-1-13-1-19 2h0c-1 2-1 2-3 3h0c-5 2-9 2-14 3-2 0-4 1-6 1v-2c2-1 2-2 4-2h1c0-1 0-1-1-1h-2c-1 0-1 0-2-1l10-5v-2c2-1 4-2 7-3 0-1 0-1 1-1z"></path><path d="M568 451c-1 2-1 2-3 3h0-3c-1 0-3 0-4-1-3 0-5 0-8 1 0-1 0-1-1-1 7-2 13 0 19-2z" class="T"></path><path d="M563 441c3 0 6-2 8-2h1l-1 1h1c-2 1-5 2-7 3-4 1-7 2-10 4v-2c2-1 4-2 7-3 0-1 0-1 1-1z" class="O"></path><path d="M550 454c3-1 5-1 8-1 1 1 3 1 4 1h3c-5 2-9 2-14 3-2 0-4 1-6 1v-2c2-1 2-2 4-2h1zm13 139c1 0 3-1 4-1s1 1 2 1c0 3-1 8 1 9l1 1c1 1 1 2 1 3 1 3 2 6 2 9-2-2-3-5-6-8 1 3 2 5 3 7 1 0 1 1 2 2 2 3 3 6 6 9l1 1h0-1-1 0 1v1 2l2 2h-4c-2-2-2-4-5-4v3c-1-1-2-1-3-2 0-1-1-1-1-2h-1v-1c-3-4-7-11-6-16 0-4 1-8 2-11 0-2 1-3 0-5z"></path><defs><linearGradient id="s" x1="570.9" y1="616.536" x2="572.7" y2="626.376" xlink:href="#B"><stop offset="0" stop-color="#151516"></stop><stop offset="1" stop-color="#3e3d3e"></stop></linearGradient></defs><path fill="url(#s)" d="M569 628h1v-1c-1-3-1-7-1-11h0l2 1c2 1 2 4 5 5l-1-1c0-1-1-2-1-2-1-1-1-2-1-3h0c2 3 3 6 6 9l1 1h0-1-1 0 1v1 2l2 2h-4c-2-2-2-4-5-4v3c-1-1-2-1-3-2z"></path><path d="M573 616c2 3 3 6 6 9l1 1h0-1-1 0 1v1 2c-3-3-6-8-8-12 2 1 2 4 5 5l-1-1c0-1-1-2-1-2-1-1-1-2-1-3h0z" class="D"></path><path d="M520 209c1-2 2-4 4-6 3 3 10 4 14 4h0l-5 11c0 1-1 3-2 4l-1 3c-1 2-2 3-3 4h0c-2 0-4 1-5 1-2 0-2 0-3 1h-1l-3-13h1c1-1 1-2 1-4l1-3c1 0 1-1 1-2h1z" class="J"></path><path d="M530 225c-4-2-10 0-13-2-1-1-1-1-1-2h2c2 0 5 0 8 1 1 0 4-1 5 0l-1 3zm-10-16c1-2 2-4 4-6 3 3 10 4 14 4h0l-5 11c-4-3-9-2-14-3v-2s0-1 1-2v-1-1zm87 193c1-4 2-8 2-13l3 2c3 2 3 5 4 8v1c1 0 5-4 6-4 2 2 1 8 1 10-1 4-4 8-5 12v1h0l-8 3c-4-4-4-6-4-12 0-2 1-3 0-5l1-3z"></path><path d="M607 402h0c2 0 3-1 5 0s4 4 5 7c0 3-1 7-2 10h-1 1 3l-8 3c-4-4-4-6-4-12 0-2 1-3 0-5l1-3z" class="D"></path><path d="M609 403h0c1 0 2 0 3 1 2 1 3 3 4 5 1 4-1 7-3 9l-1 1c-1 0-2 0-3-1-2-2-2-6-2-8s0-5 2-7z" class="B"></path><path d="M609 406c1-1 2-1 3 0s3 3 3 5c0 1-1 3-2 5h0c-1 1-1 1-2 1-1-1-2-2-3-4 0-2 0-5 1-7z" class="H"></path><path d="M610 408l1 1c1 0 2 1 2 2 0 2 0 2-1 3h-2c-1-1-1-2-1-3s0-2 1-3z" class="M"></path><path d="M608 425c2 2 2 3 3 5s4 5 4 8l-1 1 1 1c0 3 1 6 2 8 2 2 4 4 6 5 5 4 10 7 12 13 2 3 2 6 0 8-1 2-2 3-4 3-1 0-2 0-4-1l-1-1h1 0c2 0 4 0 5-2 2-1 2-3 2-4s0-3-1-4c-3-7-11-10-16-15h0 0v3l1 7c-4-5-7-9-13-12 0-1-1-3-2-5l-5-13c4-1 7-3 10-5z"></path><path d="M551 277c3-7 7-12 13-17 0 2 1 3 1 5 0 1 2 3 2 4s0 4 1 5v1h1 0 1 0l1-1v-1 1c1 2 2 6 3 8h-1v-1s0-1-1-1l-1-1h0c0 2 3 6 4 7-3 3-6 5-9 7-2 0-5-3-6-5 0-2 0-4-1-5-2-3-5-3-8-4l1-1s0-1-1-1z" class="K"></path><path d="M627 191l1 2c2 4 4 9 4 13s-2 8-3 12l6-6h0s1 1 0 2c0 2-4 5-5 7l-8 8c3-1 5-3 7-4l5-5 2-1v-3l1 3-29 28c-1 1-2 1-2 2-2 2-3 3-5 4 0-1 1-1 1-2l6-7c4-5 5-11 7-16 1-3 3-6 4-9 1-2 1-5 1-7 0-5-2-9-3-13l3 6c4 7 3 13 0 20 2-2 3-4 4-6 5-9 5-18 3-28zm-36 95l1 1c2 0 4 0 6 1 1 0 3 1 4 2 1 0 2-1 3-1v-1l1-1c3 2 6 5 7 9 3 4 3 9 1 13l-2 7c-5-4-10-12-17-14l1-2c-1-1-1-2-1-4-1-4-3-6-7-9h0l3-1z"></path><path d="M592 287c2 0 4 0 6 1 1 0 3 1 4 2 1 0 2-1 3-1v-1l1-1c3 2 6 5 7 9-1 1 0 5-1 7-1 0-2 0-4-1s-4-3-4-6l-1-1c0-1 0-2-1-3 0 0-1-1-2-1-2-2-5-3-8-4z" class="B"></path><path d="M613 298c0 1 0 1-1 2v1c-3-1-5-3-7-5v-3c0-1 1-1 2-2 1 0 2 0 3 1 1 2 2 3 2 5h1v1z" class="M"></path><path d="M610 292c1 2 2 3 2 5h1v1c-1 0-2 0-2-1-1 0-2-1-2-3 0 0 1-1 1-2z" class="I"></path><path d="M397 72c3 0 8-1 11 0 0 2 1 5 2 7l-1 1-7-4c2 2 5 4 8 5l9 4v1l4 14s-1 0-1 1l-3-2-1 1 1 2-1 1c-1-1-2-1-3-2-3-2-5-5-8-5l-1-2h-1-1l1-1c0-2-2-5-3-8-4-5-9-9-15-12 4-1 7-1 10-1z"></path><defs><linearGradient id="t" x1="403.696" y1="91.029" x2="408.193" y2="85.174" xlink:href="#B"><stop offset="0" stop-color="#212124"></stop><stop offset="1" stop-color="#3c3b39"></stop></linearGradient></defs><path fill="url(#t)" d="M402 85v-1c1-1-1-3 1-4 3 2 4 5 5 8l3 6 1 1h0c1 2 2 3 3 6-3-2-5-5-8-5l-1-2h-1-1l1-1c0-2-2-5-3-8z"></path><path d="M410 81l9 4v1l4 14s-1 0-1 1l-3-2-1 1v-1l1-1c-3-2-4-6-5-8-1-3-3-6-4-9z" class="I"></path><path d="M699 612l1 1 2 15-1 5h-1l-6-1c4 3 6 5 7 10 0 3 0 6-2 9l-2 1v-1c2-2 2-4 2-6-1-3-3-7-5-8-2-2-5-2-7-2 3 3 6 6 7 11 0 2 0 5-2 6-1 2-4 2-5 2-4 0-6-2-9-4-4-5-7-8-14-9h-5c1-1 1-1 2-1v-1h9c7 2 11 6 15 11l3 3c1 0 2-1 3-2 0 0 1-1 1-3 0-5-3-9-6-13v-1c1-4 3-8 5-12 1-2 2-5 4-7 1-1 3-2 4-3z"></path><path d="M304 108h2 1l-1 1 1 1h4 1c0 2 0 4-1 6v1c-1 3-3 5-3 8h2v1c-1-1-4 1-6 1-2 1-4 1-6 1h-9c-1 0-2-1-2-1l-4 1h-1c2-5 5-9 9-13 0-1 0-2-1-3l4-3h2l8-1z" class="W"></path><path d="M300 113c1-1 4-1 6-1v2h1c0 1-1 2-2 3-1 3-4 3-6 4 2-3 5-5 6-8h-5z" class="S"></path><path d="M287 127l9-5c1 2 2 5 2 6h-9c-1 0-2-1-2-1z" class="C"></path><path d="M311 110h1c0 2 0 4-1 6v1c-4 3-8 5-13 6l-1-1 2-1c2-1 5-1 6-4 1-1 2-2 2-3h-1v-2h0l1 1c1-2 3-3 4-3z" class="P"></path><path d="M297 122l1 1c5-1 9-3 13-6-1 3-3 5-3 8h2v1c-1-1-4 1-6 1-2 1-4 1-6 1 0-1-1-4-2-6h1z" class="I"></path><path d="M304 108h2 1l-1 1 1 1h4c-1 0-3 1-4 3l-1-1h0c-2 0-5 0-6 1l-9 2c0-1 0-2-1-3l4-3h2l8-1z" class="F"></path><path d="M562 376c-3 0-5 0-8 1-5 1-7 3-11 6h0c1-2 3-3 4-4 6-5 13-9 20-11v2h2l-3 1h0c5-1 11-1 15 2 4 2 8 6 9 10s0 7-2 10c0 1 0 0-1 1h-1c-4 0-8-2-11-6-1-2-2-4-4-6-2-3-5-5-9-6h0z" class="R"></path><path d="M562 376c-3 0-5 0-8 1-5 1-7 3-11 6h0c1-2 3-3 4-4 6-5 13-9 20-11v2h2l-3 1h0 0c-1 1-3 1-4 1h-1-1v1h8 0v1c2 0 3 0 4 1 1 0 2 0 3 1h0c1 0 1 1 2 1h0l2 3h0 0c-1-1-2-2-3-2h0c-1-1-1-1-2-1v-1c-1 0-3 0-4-1h0-2l-1-1h-10c-1 1-1 1-1 2h6z" class="O"></path><path d="M540 548h1c-1-1-3-2-3-4-1-4-1-8-1-12 0-1 1-2 2-3 1-2 1-6 1-9h-1c-5 0-10-1-13-5 3 1 5 2 8 3 2 1 4 1 6 2 4 1 6 8 8 12l-1 2c0 1 0 3 1 4s2 2 4 2h1c1-1 1-2 1-3v7c0 1-1 3 0 3v1h-1l1 1c1 1 1 4 4 5v-2h1v1c0 1 0 2 1 2 0 1 0 2 1 3s1 1 1 2c2 1 3 2 5 2h1l3 1c-1 1-2 2-3 2h-1c-1-1-3-1-5-1-2-3-5-2-8-4-1 0-2-2-3-3-2-1-3-2-5-3-1-1-2-3-2-4h-3l-1-2z" class="K"></path><path d="M543 536l-3-3c0-1-1-2 0-3h0c1 1 1 2 2 3 2 1 3 1 5 1l-1 1-4-1 1 1v1z" class="D"></path><path d="M543 536v-1l-1-1 4 1v1c-1 1 0 4 0 5-2-1-3-3-3-5z" class="C"></path><path d="M546 541c0-1-1-4 0-5v1c0 4 5 10 7 13 1 2 3 3 4 5h1v-1-2h1v1c0 1 0 2 1 2 0 1 0 2 1 3s1 1 1 2c-1 0-2-2-4-3s-3-2-4-4c-4-3-6-7-8-12z" class="I"></path><path d="M547 534h0c0 1 0 3 1 4s2 2 4 2h1c1-1 1-2 1-3v7c0 1-1 3 0 3v1h-1l1 1c1 1 1 4 4 5v1h-1c-1-2-3-3-4-5-2-3-7-9-7-13v-1-1l1-1z" class="M"></path><path d="M601 197h1c1 3 0 7 0 10 0 6 1 13 1 20 1 2 0 5 1 7h0l1 1-6 7c1-3 1-6 2-10-2 3-3 6-4 8-3 3-7 6-11 8l3-7-1-1-1-1v-10-4c1-2 3-4 4-6 5-6 8-15 10-22z"></path><path d="M591 219c3 2 6 0 7 5 1 2 0 4-1 6-1 1-2 2-3 2h-2l-2-2c-1 1-1 2-1 4 0 1 0 3 1 5-1 0-1 1-1 2l-1-1-1-1v-10-4c1-2 3-4 4-6z" class="C"></path><path d="M590 230c0-2 0-4 1-6 1-1 2-2 3-1 2 0 2 0 3 1v4c-1 2-2 3-4 4h-1l-2-2z" class="B"></path><path d="M593 225h2c1 0 1 0 1 1 0 2-1 2-2 3h0c-2 0-2 0-2-1s-1-1 0-2c0-1 1-1 1-1z" class="H"></path><path d="M588 655v1c-3 2-6 5-7 9 0 1 0 2 1 3 1 0 1 1 2 0s2-3 3-4c2-3 6-4 10-5s11 0 15 3c0 0 0 1 1 2 4 0 9 1 11 5 1 0 1 0 1 1-2 1-6-2-9-2 2 2 5 4 7 7 1 3 2 7 3 10-1 1-1 1-2 1s-2 1-3 1l1-1c-1-2-3-4-3-6h0c-1-2-2-3-3-5h0c-2 0-4-3-5-4-2-2-4-3-6-3-1 0-3-1-4-1v1h-2-3c-2 1-2 2-4 2-1-1-2-2-2-3l-2-1v-1l-1 1c-1 1-1 1-1 3 1 0 1 1 2 2 2 2-1 7 0 11v1c-2-1-4-1-6-3-2-3-3-7-2-11v-1c-1-4 2-7 3-10 2-1 3-2 5-3z"></path><path d="M619 680c1 1 5 4 7 5-1 1-1 1-2 1s-2 1-3 1l1-1c-1-2-3-4-3-6z" class="O"></path><path d="M594 666h7c-3-2-9 0-12-2v-1c4 1 7 1 11 1 7 1 12 6 16 11h0c-2 0-4-3-5-4-2-2-4-3-6-3-1 0-3-1-4-1v1h-2-3c-2 1-2 2-4 2-1-1-2-2-2-3l-2-1v-1l6 1z" class="P"></path><path d="M588 665l6 1 2 1-2 2h-1c-1 0-2-2-3-2l-2-1v-1z" class="Q"></path><path d="M312 100c5-1 9-1 14 2 2 1 3 3 4 4l1 2-1 1c1 3 1 6 0 9h0-1c-1 1-2 3-3 4-3 2-6 3-9 4-2 1-5 2-7 4v-4-1h-2c0-3 2-5 3-8v-1c1-2 1-4 1-6h-1-4l-1-1 1-1h-1-2c0-1 2-2 2-3 1-1 0-2 0-2 2 0 3 0 5-1h2l-1-2z"></path><path d="M316 116h1l-1 3-1 1-2-4c1 1 1 1 2 1l1-1z" class="H"></path><path d="M317 107l2 2c0 1 2 3 2 4s0 1-1 1v2c0 1-1 2-1 2l-3 1 1-3c1-2 1-4 1-5-1-1-1-3-1-4z" class="D"></path><path d="M326 102c2 1 3 3 4 4l1 2-1 1c1 3 1 6 0 9h0-1 0c-1-4-3-11-6-13v-1h0c1-1 2-1 3-2z" class="F"></path><path d="M329 118c2-5-1-9-1-13l2 4c1 3 1 6 0 9h0-1 0z" class="N"></path><defs><linearGradient id="u" x1="316.639" y1="106.639" x2="312.499" y2="110.343" xlink:href="#B"><stop offset="0" stop-color="#363636"></stop><stop offset="1" stop-color="#514f50"></stop></linearGradient></defs><path fill="url(#u)" d="M313 102c2 1 3 3 4 5 0 1 0 3 1 4 0 1 0 3-1 5h-1l-1 1c-1 0-1 0-2-1h0-2c1-2 1-4 1-6h-1-4l-1-1 1-1h-1-2c0-1 2-2 2-3 1-1 0-2 0-2 2 0 3 0 5-1h2z"></path><path d="M314 111l1 1c0 1 0 2 1 3v1l-1 1c-1 0-1 0-2-1h0c0-2 0-3 1-5z" class="E"></path><path d="M306 108c1-2 2-2 2-4h1c1 0 2 1 3 1l-1 1-4 2h-1z" class="M"></path><path d="M311 106h3s1 1 1 2 0 1-1 1v2c-1 2-1 3-1 5h-2c1-2 1-4 1-6h-1-4l-1-1 1-1 4-2z" class="G"></path><defs><linearGradient id="v" x1="560.224" y1="483.196" x2="582.21" y2="492.588" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292829"></stop></linearGradient></defs><path fill="url(#v)" d="M576 491c-1 3 0 8 0 11l-9-4c2 3 7 5 9 8 2 2 3 6 3 9-1-1-1-2-2-3-1-4-4-6-8-7l3 5v1c-3-1-8-6-9-8-4-4-5-10-5-15 1-5 4-10 8-13 6-4 12-5 18-4v1s-1 1-2 1c-3 0-7-1-10 2-1 0-1 0-1 1s0 3 1 3c1 1 2 1 3 1v1h2c0 1 0 1-1 2v1c-1 1-1 1-1 2h0v4h0 1v1z"></path><path d="M516 86c13 5 27 13 39 21 2 1 5 3 7 5-9-3-18-3-27-1-3 1-6 1-9 3-2-2-2-5-3-8-3-6-6-14-7-20z"></path><path d="M652 305c1 2 4 12 4 12v-1h1c0 1 0 1 1 2v1 2 4c0 1 0 4 1 5v30c-4 3-8 6-13 7-2 1-4 3-7 3-1-1-1-1-2-1l-4 1c-1 0-2-1-2-1 3-1 5-2 8-4s5-4 8-7h0c1-1 3-4 4-6l1-4c1-6 1-11 0-17 0-2-1-5-2-7-1-3-3-6-6-9 0-1 1-1 1-2h1c2-1 3-4 5-5l1-3z" class="G"></path><path d="M637 369l9-7h0c-1 2-3 3-4 5-1 0-2 1-2 1v1c1 0 1 0 1-1h2l1-1h2 0 0c-2 1-4 3-7 3-1-1-1-1-2-1z" class="B"></path><path d="M652 305c1 2 4 12 4 12h0c0 2 1 5-1 6v3c1 1 1 2 1 3l-1-1h0c0-2-1-4-1-6-1-4-2-9-2-13 0-1-1-1-1-1l1-3z" class="D"></path><path d="M650 324c-1-3-3-6-6-9 0-1 1-1 1-2h1l-1 1 6 9c4 6 5 16 3 23v2h-2c1-6 1-11 0-17 0-2-1-5-2-7z" class="T"></path><path d="M652 348h2c-1 4-3 7-5 10-1 1-2 3-3 4l-9 7-4 1c-1 0-2-1-2-1 3-1 5-2 8-4s5-4 8-7h0c1-1 3-4 4-6l1-4z" class="R"></path><path d="M737 581c0 3-1 6-1 9v8 6c0 1 1 2 2 4-3 6-6 12-10 19-1 1-2 3-3 5l-1-1-6-6c-2-1-4-2-6-1h-1 0v-1h0c-1 0-2-1-4-1h0c1-2 1-3 1-5 1-2 3-3 5-3l1-2 1 1c1 0 1-1 2-1l1-1 3-2c1-1 2-1 2-2 2-1 4-2 4-4v-3l10-19z" class="K"></path><path d="M727 603l3-4v-1c1 2-1 5-2 7-1 1-4 3-4 4-1 1-1 3-1 4v14l1 1v3l-6-6c-2-1-4-2-6-1h-1 0v-1h0c-1 0-2-1-4-1h0c1-2 1-3 1-5 1-2 3-3 5-3l1-2 1 1c1 0 1-1 2-1l1-1 3-2c1-1 2-1 2-2 2-1 4-2 4-4z" class="N"></path><path d="M714 612l1 1c-1 1-2 1-3 2 1 3 1 5 2 8h0-3 0c-1 0-2-1-4-1h0c1-2 1-3 1-5 1-2 3-3 5-3l1-2z" class="H"></path><path d="M594 503c2 1 3 3 4 6l6 18 3 6 2 2c-1 3-2 6-4 8l-11 19c0-2 1-5 1-7v-19l-5 8c0-1-1-3-1-5-2-7 1-13 5-19h0v-1c1-2-2-5-3-7 0-1 0-1-1-1 0-1 0-1-1-1v-1l5-6z"></path><path d="M574 465v-1c-3-1-6-2-8-4 4-1 7-2 11-3 3-1 6-1 10-1 8 0 15 1 23 5a30.44 30.44 0 0 1 8 8s-1-1-2-1c-3-2-7-2-11-2h0l1 1c2 1 3 1 5 2l-1 1v2c-2 2-4 3-6 3h-2c-1-1-1-1-2 0l-1 1 5 5-1 1h-1s0-1-1-1l-3-3c-6-6-12-9-21-9 0-1 1-2 1-3-1-1-3 0-4-1h0z" class="H"></path><path d="M606 467c2 1 3 1 5 2l-1 1v2c-2 2-4 3-6 3h-2c-1-1-1-1-2 0l-1 1h0c0-1-1-2-2-2h0v-1l1-1h1v1h1 1c2 0 4 1 5 0h2l2-2c-1 0-1-1-1-1v-1l-3-1h0v-1z" class="J"></path><path d="M549 317l1 1v3c0 1 0 1 1 2-3 6-6 14-8 20l1 1 5-13c2 1 3 2 5 3 0 0 1-1 2 0h0c0 1-1 2-2 2 0 2 0 3-1 4h-2c-2 1-3 3-4 4 0 2 0 5 2 7 1 1 2 1 3 1l1 1c1 0 2-1 3-1 1-1 2-1 4 0l2 2c-2 3-7 4-11 6-6 4-9 9-12 16l-7 13c1-6 1-12 2-18 1-15 5-31 11-45l4-9z"></path><defs><linearGradient id="w" x1="549.756" y1="339.847" x2="545.794" y2="335.997" xlink:href="#B"><stop offset="0" stop-color="#1c1c1d"></stop><stop offset="1" stop-color="#373537"></stop></linearGradient></defs><path fill="url(#w)" d="M543 343l1 1 5-13c2 1 3 2 5 3 0 0 1-1 2 0h0c0 1-1 2-2 2 0 2 0 3-1 4h-2c-2 1-3 3-4 4s-3 4-3 6v3l1 1h0l-2 2h-1v-1-2h-1v1h-1c0-2 1-3 1-5l1-2v-2c1 0 1-1 1-2z"></path><defs><linearGradient id="x" x1="560.694" y1="355.572" x2="549.82" y2="354.587" xlink:href="#B"><stop offset="0" stop-color="#28272a"></stop><stop offset="1" stop-color="#4d4c4c"></stop></linearGradient></defs><path fill="url(#x)" d="M545 354l-1-1v-3c0-2 2-5 3-6 0 2 0 5 2 7 1 1 2 1 3 1l1 1c1 0 2-1 3-1 1-1 2-1 4 0l2 2c-2 3-7 4-11 6-1-3-5-3-6-6z"></path><path d="M527 192c1-1 1-2 2-2 1-1 1 0 1-1l2-1 1-1c1 0 1-1 2-1h0c1 0 1-1 2-1s2-1 3-1l1-1 2-1c1 0 4-1 6 0h1c3 0 5 0 7 1h5c2 1 4 1 6 2h0-3-1 0c1 0 2 0 3 1h1l2 1h0l1 1c1 1 3 1 4 2-7 0-13 0-20 2l-7 4c-2 2-4 3-5 5l-5 6h0c-4 0-11-1-14-4-2 2-3 4-4 6h-1c0 1 0 2-1 2l-1 3c0-2 0-3 2-5v-1h0l1-1v-1s0-1 1-1v-1c0-1 1-1 1-2-1 1-1 2-3 3v-1-1c1-1 1-2 2-3 1-2 2-3 4-5 0-1 1-2 2-3z" class="O"></path><path d="M519 203c1 0 2-1 2-1l2-4 3-3c2-2 3-4 5-5h1l4-2v-1l1 1c-9 5-12 13-18 21 0 1 0 2-1 2l-1 3c0-2 0-3 2-5v-1h0l1-1v-1s0-1 1-1v-1c0-1 1-1 1-2-1 1-1 2-3 3v-1-1z" class="G"></path><path d="M527 192c1-1 1-2 2-2 1-1 1 0 1-1l2-1 1-1c1 0 1-1 2-1h0c1 0 1-1 2-1s2-1 3-1l1-1 2-1c1 0 4-1 6 0h1c3 0 5 0 7 1-4 0-10-1-14 1-2 1-4 3-6 4l-1-1v1l-4 2h-1c-2 1-3 3-5 5l-3 3-2 4s-1 1-2 1c1-1 1-2 2-3 1-2 2-3 4-5 0-1 1-2 2-3z" class="L"></path><defs><linearGradient id="y" x1="546.161" y1="180.825" x2="564.361" y2="197.153" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#333"></stop></linearGradient></defs><path fill="url(#y)" d="M537 189l3-1c6-3 14-4 21-3 4 1 7 2 10 3 1 1 3 1 4 2-7 0-13 0-20 2-2-1-3-1-5-1-3 0-5 0-8-1h-3c-1 0-1-1-2-1z"></path><path d="M531 194c2-2 4-4 6-5 1 0 1 1 2 1h3c3 1 5 1 8 1 2 0 3 0 5 1l-7 4c-2 2-4 3-5 5l-5 6h0c-4 0-11-1-14-4 1-1 2-3 3-5 1-1 2-3 4-4z" class="B"></path><path d="M527 198c1-1 2-3 4-4 5 1 11 2 17 1v1c-2 2-4 3-5 5-1 0-2-1-2-1h-4c-2 0-5-1-8-1-1 0-1 0-2-1z"></path><path d="M524 203c1-1 2-3 3-5 1 1 1 1 2 1 3 0 6 1 8 1h4s1 1 2 1l-5 6h0c-4 0-11-1-14-4z" class="B"></path><defs><linearGradient id="z" x1="527.29" y1="539.718" x2="503.586" y2="537.917" xlink:href="#B"><stop offset="0" stop-color="#151414"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#z)" d="M504 548l-1-1c2-3 6-4 9-4-3-1-5 0-8-1-1-1-1-2-1-3h-1v-2-2c0-2 2-5 2-7l4-9h0c4 0 9 3 11 5 1-1 0-2 0-3 3 2 5 4 6 7l3 6-1 1c0 2 1 4 1 7-1 3-1 7-1 11-2 3-4 5-7 8 2-4 3-9 4-13-4 5-7 8-13 10l1-5c0-2 0-3-1-5-1 0-2-1-3-1-2 0-2 0-4 1z"></path><path d="M519 521c3 2 5 4 6 7l3 6-1 1c-2-5-4-8-8-11 1-1 0-2 0-3z" class="R"></path><path d="M323 90l5-2s3 1 3 2c2 1 5 2 6 4 5 5 5 12 5 18 0 9-7 15-12 20-5 4-8 8-12 12v-2c-1-7 6-13 9-18l3-6h0c1-3 1-6 0-9l1-1-1-2c-1-1-2-3-4-4-5-3-9-3-14-2l1 2h-2c-2 1-3 1-5 1h-1v-1l1-1c0-1 1-1 1-1v-1c4-3 11-7 16-9z" class="K"></path><path d="M326 100c3 0 6 2 9 4h0-1c1 2 2 3 2 5v1h-2l-1 1c0-2-1-4-2-5-2-2-3-4-5-6z" class="F"></path><path d="M331 106c1 1 2 3 2 5 2 12-5 20-12 28 0 1-1 3-2 3 0-2 1-3 2-4 4-6 10-12 11-19h0c0-4 0-8-1-11l-1-2h1z" class="U"></path><path d="M307 100c5-2 11-4 16-1l3 1c2 2 3 4 5 6h-1c-1-1-2-3-4-4-5-3-9-3-14-2l1 2h-2c-2 1-3 1-5 1h-1v-1l1-1c0-1 1-1 1-1z" class="O"></path><path d="M309 101l3-1 1 2h-2c-1 0-2 0-2-1z" class="D"></path><path d="M306 101h3c0 1 1 1 2 1-2 1-3 1-5 1h-1v-1l1-1z" class="J"></path><defs><linearGradient id="AA" x1="320.601" y1="126.274" x2="329.804" y2="127.641" xlink:href="#B"><stop offset="0" stop-color="#070506"></stop><stop offset="1" stop-color="#222423"></stop></linearGradient></defs><path fill="url(#AA)" d="M330 109l1-1c1 3 1 7 1 11h0c-1 7-7 13-11 19-1 1-2 2-2 4h-1c-1-7 6-13 9-18l3-6h0c1-3 1-6 0-9z"></path><defs><linearGradient id="AB" x1="333.276" y1="101.007" x2="328.325" y2="87.917" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#AB)" d="M323 90l5-2s3 1 3 2c2 1 5 2 6 4-1 1-1 1 0 2v1c1 1 0 2 0 4h0c-2 0-3-1-4-2-2 0-4-2-6-3-1-1-3-3-3-4s-1-1-1-2h0z"></path><path d="M331 90c2 1 5 2 6 4-1 1-1 1 0 2v1c1 1 0 2 0 4h0c-2 0-3-1-4-2l3 1h1c-1-4-4-7-6-9v-1z" class="D"></path><path d="M611 469c4 2 9 6 12 10l2 1c-1 1-2 1-3 1 0 0 0-1-1-1-2 0-4 3-5 4v1 1l2 1 3 4c3 4 6 8 6 12 1 3 1 6 0 9-3 6-5 12-9 18h-1v2c-1 0-1 0-1 1h0c0 1-1 1-1 2l-1-1-1-2c-1-1 0-2 0-4h2c0 1 1 1 1 2h1c0-1-1-2-2-3 0-2 1-3 1-5 0-1-1-3-1-4 1 0 1 0 1-1h-1l1-1c0-1 0-1-1-2h0c1-2 0-2 1-4l-1-1v-2l1-1c-1-1-2-1-3-1h0l-1-3c-1-7-5-14-10-20h1l1-1-5-5 1-1c1-1 1-1 2 0h2c2 0 4-1 6-3v-2l1-1z"></path><path d="M615 488c0-1 0-2 1-3v1l2 1 3 4c-3 1-4-1-6-3z" class="J"></path><path d="M623 479l2 1c-1 1-2 1-3 1 0 0 0-1-1-1-2 0-4 3-5 4v1c-1 1-1 2-1 3l-3-3c3-2 6-5 10-5h1v-1z" class="D"></path><path d="M627 503c1 3 1 6 0 9-1-1-1-1-2-1l-1-1c-1-1-1-4-1-6h3 1v-1z" class="C"></path><path d="M531 294l5-19 1 6c0 3 0 4 2 7h0c2 4 6 4 4 10v1c-1 1 0 3 0 4 1 1 2 2 3 4 2 3 1 7 1 11-1 2-2 5-3 7-5 12-9 25-11 38l-2-55c-1-4-1-10 0-14z"></path><path d="M531 294h1v1c1 0 2 1 2 2 1 2 0 6-2 8l-1 1v2c-1-4-1-10 0-14z" class="D"></path><defs><linearGradient id="AC" x1="493.681" y1="104.806" x2="517.008" y2="94.933" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#AC)" d="M493 91c3-1 7 0 10 0-7-3-12-4-20-4 3-2 6-4 9-5 2-1 5-1 7-2 1 0 4 1 5 2 4 1 8 2 12 4 1 6 4 14 7 20 1 3 1 6 3 8-8 3-15 7-22 13v-2s1 0 1-1c1 0 1 0 1-1h1c0-1 1-1 1-2-1-4-2-9-4-13s-4-9-5-13c0-1-1-2 0-3h0l-1-1h-1c-1 1-2 1-3 0h-1z"></path><path d="M553 473h1s0 1 1 2c0 1 2 2 3 3 1 0 2-1 3 0-3 5-5 10-4 16s4 10 8 14c0 3 0 8 2 11v1h0-1-1c0 1 1 2 2 3 2 3 4 5 4 8-1 5-4 8-7 11l3-6c0-1 1-3 1-3 0-1-1-3-2-4 0-1-1-2-1-3h0c-1 2-2 4-4 6v-2h0c-1 0-2 2-3 2s-2-1-3-2c2 0 3-1 4-3 1-1 0-2 0-4h-1l-3 2c-1-5-2-9-5-14l-2 8c-4-6-4-11-2-19v-14l-2-2c2-4 5-8 9-11zm13-115c-1 2-1 4-2 7h-1c-1 2-3 3-4 5l6-3c7-4 16-6 25-8l6-3 1 1c-2 3-6 5-9 6 6 0 13 0 18-4 2-2 2-4 3-6s2-4 3-5l1 1c3-2 6-4 7-7 1-2 0-4-1-6h1c0 2 1 3 2 5-1 2-1 3-3 4-2 3-6 4-8 7l-2 2c3-2 6-4 9-4-2 1-5 2-7 4-2 1-2 2-3 4-3 5-7 4-12 6l10 3c1 1 1 2 2 3l1 1h0c2 1 5 3 7 5 3 3 5 8 7 13-6-3-11-8-16-12 0 2 1 5 0 7l-2 2c-1 0-3 0-4-1h0l2-1c0-2 0-2-1-3 1-2 1-3 0-4-1-3-1-8-3-9-2-2-5-2-8-2-2 0-5-1-7 0-1 1-1 1-2 1-5 0-10 0-15 1-7 2-14 6-20 11-1 1-3 2-4 4h0c4-3 6-5 11-6 3-1 5-1 8-1h0l-2 1-5 1c1 1 3 1 4 2-1 2-1 4-2 6h-6c-4 0-7 0-11 3l-2 2h-1c-1 1-1 2-1 4-3 5-5 11-6 17v1c0-12 2-22 9-32 3-3 7-6 11-9s8-6 11-11c1-1 2-4 3-6v-1h1c0 1 1 2 1 4z"></path><path d="M537 391l1-1c3-6 11-10 17-12 1 1 3 1 4 2-1 2-1 4-2 6h-6c-4 0-7 0-11 3l-2 2h-1z" class="R"></path><defs><linearGradient id="AD" x1="611.114" y1="191.999" x2="597.625" y2="192.927" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#AD)" d="M593 147c2 1 3 2 5 3l3 2c-1 1-1 1-3 2l-1-1c1 2 3 3 3 4 10 11 11 26 12 39 1 6 1 12 0 19 0 7-3 14-7 20l-1-1h0c-1-2 0-5-1-7 0-7-1-14-1-20 0-3 1-7 0-10h-1v-2l1-1v-1-3-4c-1 1-1 1-1 3v-10l-1 1v6h-1s0-1-1-2h0v-1c-2-1-2-1-2-3l1-3v-1c1-5 1-10-2-15-1-2-2-4-3-5 0-1-1-3-1-4h0l-2-1c1-1 4 0 6 0v-1c-1 0-1-1-2-2v-1z"></path><path d="M593 147c2 1 3 2 5 3l3 2c-1 1-1 1-3 2l-1-1c1 2 3 3 3 4v1h0c-1-2-3-3-5-5h0l3 3v1c1 1 1 3 2 4 0 1 1 2 1 3l-2-3c0-1-1-2-1-3l-1-1c0-1-2-3-3-4-1 0-2-1-3-1h0l-2-1c1-1 4 0 6 0v-1c-1 0-1-1-2-2v-1z" class="B"></path><path d="M598 150l3 2c-1 1-1 1-3 2l-1-1c-1 0-1 0-2-1h2l1-2z" class="F"></path><path d="M591 152c1 0 2 1 3 1 1 1 3 3 3 4l1 1c0 1 1 2 1 3l2 3v1 3h0l1 1v1 3h0c0 2 0 3 1 4v9h-1c-1 1-1 1-1 3v-10l-1 1v6h-1s0-1-1-2h0v-1c-2-1-2-1-2-3l1-3v-1c1-5 1-10-2-15-1-2-2-4-3-5 0-1-1-3-1-4z" class="I"></path><path d="M597 176c1-1 1-1 1-2h1c1 1 1 4 1 6v6h-1s0-1-1-2h0v-1c-2-1-2-1-2-3l1-3v-1z" class="L"></path><defs><linearGradient id="AE" x1="512.269" y1="144.252" x2="532.222" y2="135.916" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#646464"></stop></linearGradient></defs><path fill="url(#AE)" d="M506 128c1-1 2-2 3-2 5-4 11-8 18-10 1 5 3 11 5 16l4 10c1 1 1 2 2 4-3 9-10 13-18 18 0-1 0-3-1-4l-3-9c-2-4-3-7-5-11l-3-6c-1-2-1-4-2-6z"></path><path d="M510 592h1v2c1 2 2 3 3 5s3 4 4 6c1 0 1 0 2-1h1c-1 2-2 3-3 5-1 3 0 5 1 8l1 2v1c-5-1-9 0-13 2h-1c-2 2-4 4-5 6l-1 2-3 7c-3-2-7-6-10-6-1-1-2 1-3 0 0-3 0-8 1-11 4-12 11-21 22-26 0-1 2-2 3-2z"></path><path d="M500 630c-2-3-5-6-7-9h0c-2-2-3-5-3-8 0-1 1-2 1-3h2c4 1 8 5 11 8l3 4h-1s0-1-1-2c-1 0-2 0-3-1-2-2-5-6-9-6l-1 1c0 4 6 10 8 13l1 1-1 2z" class="D"></path><defs><linearGradient id="AF" x1="494.029" y1="612.403" x2="501.929" y2="624.984" xlink:href="#B"><stop offset="0" stop-color="#232223"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#AF)" d="M501 628l-1-1c-2-3-8-9-8-13l1-1c4 0 7 4 9 6 1 1 2 1 3 1 1 1 1 2 1 2-2 2-4 4-5 6z"></path><path d="M510 592h1v2c1 2 2 3 3 5s3 4 4 6c1 0 1 0 2-1h1c-1 2-2 3-3 5-1 3 0 5 1 8-2 0-2 0-3-1l-11-8c-1-1-3-1-3-3-1-1 0-2 0-4 1-1 2-1 4-2 3 0 5 2 8 4v-1l-3-3c-3-1-3-3-4-5 0-1 2-2 3-2z" class="H"></path><defs><linearGradient id="AG" x1="518.519" y1="612.858" x2="505.918" y2="605.962" xlink:href="#B"><stop offset="0" stop-color="#4e4d4e"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#AG)" d="M516 616c-3-4-6-6-9-9-1-1-2-1-2-2 1-1 2-2 3-2 3 0 6 1 7 3l3 3h0c-1 3 0 5 1 8-2 0-2 0-3-1z"></path><path d="M506 128c1 2 1 4 2 6l3 6c2 4 3 7 5 11l3 9c1 1 1 3 1 4-3 2-7 6-8 9-1 4-1 9 0 12 1 1 1 0 1 1-3-1-5-3-7-5-6-9-6-20-4-30 0-2 1-5 2-8l-1 2c-6 10-6 19-3 30l-11-18c0-3 0-6 1-8 3-8 10-16 16-21z" class="K"></path><path d="M577 260l7-3h1c0 2 0 4 1 6 0 4 2 7 3 10 1 2 2 4 4 6 1 0 2 0 3 1 4 0 7 2 10 3h1c1 0 2 0 3-1 1-3 0-5-1-7-1-1-2-2-2-3h0l-2-3v-7c0 1 3 1 3 2 5 2 10 5 15 9 2 3 5 5 7 7-5-1-9-3-15-4l4 3h-1-2c-1 4 1 7 2 11 1 1 3 3 4 5h2c1 4 3 8 3 12v1h-1c-1 3-1 5-2 7v3l-1 1c-1-4-4-7-5-11l-1-1h0l-1-1c0-3 1-7-1-10-3-6-9-11-15-13h-1c-4-1-9-1-13 1l-2-2 2-1-1-1-10 6c-1-1-4-5-4-7h0l1 1c1 0 1 1 1 1v1h1c-1-2-2-6-3-8v-1 1c-1-3 0-5-1-7v-2c1 0 0 0 1 1 2-3 4-4 6-6z"></path><path d="M605 262c0 1 3 1 3 2h-1c0 5 2 7 4 11 1 2 1 3 2 5v4c-1 1-1 1-2 1-2 0-4-1-5-2h1c1 0 2 0 3-1 1-3 0-5-1-7-1-1-2-2-2-3h0l-2-3v-7z" class="C"></path><path d="M570 265c1 0 0 0 1 1 2-3 4-4 6-6 2 7 4 14 7 20 2 0 4-2 6-1h0c-1 1-1 1-2 1s-1 1-2 1l-1-1-10 6c-1-1-4-5-4-7h0l1 1c1 0 1 1 1 1v1h1c-1-2-2-6-3-8v-1 1c-1-3 0-5-1-7v-2z" class="I"></path><path d="M583 286c1 1 1 1 3 0h5l-3 1h0c4 3 6 5 7 9 0 2 0 3 1 4l-1 2h-2-1c2 2 5 5 6 8-1 2 0 6 1 8 0 0 1 0 2 1-3 0-6-1-8 0h-3l-6 2-3 1c0-1 0-2-1-2-4 0-10 2-14 3h-1-1c-1-3 1-5 0-8 0-1-1-1-1-1l-1-1h0-1l-1-1c1-2 2-3 2-5l5-8 5-5c3-4 7-6 11-8z" class="B"></path><path d="M572 294c0 1 0 2-1 4h0c1 1 0 3-1 4l-2 2-2 2c0-1 1-1 1-2h0v-5l5-5z" class="F"></path><path d="M574 301c-1 1-1 1-2 1h-1l-1 1h0 0c1-1 2-1 2-2 2-2 5-5 7-5 2 1 5 2 6 4 3 4 3 11 4 16 0 1 1 2 1 3l-6 2c0-2 0-2-1-3l-2-2v-1c-2-4-2-11-5-14h-2z"></path><path d="M593 319c-1-1-1-1-1-2l-2-11v-4c0-1-1-1-2-2h0l-1-1 1-1c-1-2-5-2-6-2s-1 0-2-1c0 0-1-1-1-2s1-2 1-3c2-1 3-1 5-1 2-1 3-1 5 1s4 7 3 9c0 2-1 1 0 3h-1c2 2 5 5 6 8-1 2 0 6 1 8 0 0 1 0 2 1-3 0-6-1-8 0z" class="H"></path><path d="M584 291c1-1 2-1 3 0 2 0 2 1 3 3-1 1-1 0-2 1-2 0-4 0-5-1-1 0-1 0-2-1 1-1 2-2 3-2z" class="L"></path><defs><linearGradient id="AH" x1="579.603" y1="310.747" x2="563.42" y2="312.446" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#656464"></stop></linearGradient></defs><path fill="url(#AH)" d="M567 299v5h0c0 1-1 1-1 2l2-2 1 1c1-1 1-1 2-1l2-1 1-2h2c3 3 3 10 5 14v1l2 2c1 1 1 1 1 3l-3 1c0-1 0-2-1-2-4 0-10 2-14 3h-1-1c-1-3 1-5 0-8 0-1-1-1-1-1l-1-1h0-1l-1-1c1-2 2-3 2-5l5-8z"></path><path d="M566 306l2-2 1 1c1-1 1-1 2-1l2-1c-2 2-6 5-9 6v-1l2-2z" class="H"></path><path d="M564 315l1-2c0 1 0 1 1 2h0c0 2-1 4 0 6v1 1h-1-1c-1-3 1-5 0-8z" class="L"></path><defs><linearGradient id="AI" x1="588.671" y1="503.477" x2="608.287" y2="496.222" xlink:href="#B"><stop offset="0" stop-color="#4d4c4c"></stop><stop offset="1" stop-color="#686868"></stop></linearGradient></defs><path fill="url(#AI)" d="M584 471c5 1 10 4 14 7 0 1 2 3 3 3s1 1 1 1c5 6 9 13 10 20l1 3h-1c-1 2 0 6 0 8v8c0 5-2 10-3 14l-2-2-3-6-6-18c-1-3-2-5-4-6 1-1 1-2 1-3l-1 1c-1 1-2 2-3 2 0-2-1-4-1-6l-5-8c-1-3-2-5-3-7-1 0-3-1-4-1 0-1-1-1-1-2v2h-2v-1c-1 0-2 0-3-1-1 0-1-2-1-3s0-1 1-1c3-3 7-2 10-2 1 0 2-1 2-1v-1z"></path><path d="M577 481h-2v-1c-1 0-2 0-3-1-1 0-1-2-1-3s0-1 1-1c3-3 7-2 10-2l-3 5h0c6 3 9 9 12 15l3 6v2c-1 1-2 2-3 2 0-2-1-4-1-6l-5-8c-1-3-2-5-3-7-1 0-3-1-4-1 0-1-1-1-1-2v2z" class="C"></path><defs><linearGradient id="AJ" x1="570.566" y1="314.807" x2="556.635" y2="327.684" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient></defs><path fill="url(#AJ)" d="M558 312h1c0 1 0 1 1 2v-2l1 1h1 0l1 1s1 0 1 1c1 3-1 5 0 8h1 1c4-1 10-3 14-3 1 0 1 1 1 2-1 0-2 0-3 1 0 1 1 1 2 1l1 1 2 1v1c-1 0-1 1-1 2 0 2-1 3-1 5 0 3 2 6 4 8h0c1 1 2 2 2 3h-2c-3 0-6-1-9 0v1h1-1c-2 4-1 7-2 11h-5l-2-1c-1 1-1 1-1 2 0-2-1-3-1-4h-1v1l-1-1h-1l-2-2c-2-1-3-1-4 0-1 0-2 1-3 1l-1-1c-1 0-2 0-3-1-2-2-2-5-2-7 1-1 2-3 4-4h2c1-1 1-2 1-4 1 0 2-1 2-2h0c-1-1-2 0-2 0-2-1-3-2-5-3l2-4c0-1 1-4 2-4l5-11z"></path><path d="M560 312l1 1h1 0l1 1s1 0 1 1c1 3-1 5 0 8h1c-1 1-2 1-4 1 1-2 2-7 1-9-1-1-2 0-2-1v-2z" class="N"></path><path d="M551 327c1 0 2 0 3 1l3 3c4-3 8-5 13-6-2 2-6 4-9 6-1 1-4 3-5 3h0c-1-1-2 0-2 0-2-1-3-2-5-3l2-4z" class="F"></path><path d="M560 352h1c2-2 5-3 7-5v-5l3-8c0-1 0-2-1-2v-1c0-2 0-2 1-4h1c1 1 2 2 3 4 0 3-2 7-3 10-1 2-1 4-1 6-1 4-3 6-2 10l-2-1c-1 1-1 1-1 2 0-2-1-3-1-4h-1v1l-1-1h-1l-2-2z" class="P"></path><defs><linearGradient id="AK" x1="581.267" y1="341.373" x2="574.712" y2="343.289" xlink:href="#B"><stop offset="0" stop-color="#2c2c2d"></stop><stop offset="1" stop-color="#49474a"></stop></linearGradient></defs><path fill="url(#AK)" d="M580 324l1 1 2 1v1c-1 0-1 1-1 2 0 2-1 3-1 5 0 3 2 6 4 8h0c1 1 2 2 2 3h-2c-3 0-6-1-9 0v1h1-1c-2 4-1 7-2 11h-5c-1-4 1-6 2-10 0-2 0-4 1-6 1-3 3-7 3-10 1-1 1-3 2-4l3-3z"></path><path d="M581 325l2 1v1c-1 0-1 1-1 2 0 2-1 3-1 5 0 3 2 6 4 8h0c1 1 2 2 2 3h-2v-1c-3-2-5-4-7-6 0-2 0-4 1-5 1-2 2-4 2-5v-3z" class="D"></path><path d="M561 331c2 0 3-1 4 0h1 0v1c0 1 1 0 1 1v2c-1 3-3 7-3 10v1 1l-3 2v-1c0 1-1 1-1 1 0 1-1 1-2 1h-1c-1-1-2-1-3-1v2h0l2 1c-1 0-2 1-3 1l-1-1c-1 0-2 0-3-1-2-2-2-5-2-7 1-1 2-3 4-4h2c1-1 1-2 1-4 1 0 2-1 2-2 1 0 4-2 5-3z" class="U"></path><path d="M633 416l6-2c1 1 2 3 2 4 1 3 2 6 2 9 0 2 1 4 1 6l3 6c-3 1-4 1-5 4v3c-1 1-1 0-1 1v1c3 4 5 7 4 12l-3 11c-1-4-4-8-7-12l-2-2-6-6-4-2c-2-2-4-4-6-7l-2-2h0l-1-1 1-1c0-3-3-6-4-8s-1-3-3-5l9-3 5-2 11-4z"></path><path d="M622 420l11-4c1 3 3 8 1 11-1 1-2 1-2 1-2 1-4 1-5 0-2-2-4-6-5-8z" class="M"></path><path d="M608 425l9-3h0c1 6 8 12 4 18l-1 1h0-1-1 1l-1-1h-2l1 2h0l-2-2h0l-1-1 1-1c0-3-3-6-4-8s-1-3-3-5z" class="B"></path><path d="M614 425h1c2 2 5 6 5 9 0 2-1 3-2 5-1-2-1-4-2-5-2-3-3-5-3-8l1-1z" class="H"></path><path d="M623 449l8-5v-1c-2-2-2-4-2-7 0-2 1-2 2-3h4c2 2 3 5 3 7s0 4-1 6c0 4 0 8-1 12l-1 1-2-2-6-6-4-2z" class="B"></path><path d="M631 436h2c1 1 2 2 3 4 0 1-1 2-1 3h-1c-2-1-2-2-3-4v-3z" class="F"></path><path d="M627 451c1-2 3-5 6-6v1c1 3 1 7 2 11h-1-1l-6-6z" class="M"></path><path d="M481 113c6-2 13-1 19-2-9-1-20-2-29 1-2 1-3 2-5 4l7-12c6-7 12-12 20-13h1c1 1 2 1 3 0h1l1 1h0c-1 1 0 2 0 3 1 4 3 9 5 13s3 9 4 13c0 1-1 1-1 2h-1c0 1 0 1-1 1 0 1-1 1-1 1v2l-5 4-1-1 1-1h0c-7 4-13 11-18 17-2-2-4-6-6-8-3-4-7-8-11-12-1 0-1 0-1-1h0c1-2 2-3 3-4 5-4 9-6 15-8z" class="G"></path><defs><linearGradient id="AL" x1="483.139" y1="119.719" x2="464.088" y2="119.28" xlink:href="#B"><stop offset="0" stop-color="#2a2a2b"></stop><stop offset="1" stop-color="#575756"></stop></linearGradient></defs><path fill="url(#AL)" d="M464 126c-1 0-1 0-1-1h0c1-2 2-3 3-4 5-4 9-6 15-8v1c1 3 4 5 3 8-2 2-3 3-6 3-3 1-6 1-9 1h-5 0z"></path><path d="M527 116c3-1 7-2 11-3 10-2 21 0 30 4 3 2 7 4 9 7-2 5-5 9-8 12-4 3-7 6-11 9l-6 6c-1 3 0 6-1 9 0-3-1-5-2-7-1 1-2 2-3 4-1 1 0 7-1 7-1 1 0 1-1 1-2 0-5-1-6-2 1-1 3-1 4-3 1-7-1-15-2-22l-2 8c-1-2-1-3-2-4l-4-10c-2-5-4-11-5-16zm73 64l1-1v10c0-2 0-2 1-3v4 3 1l-1 1v2c-2 7-5 16-10 22-1 2-3 4-4 6v4 10l1 1c-3 2-6 5-9 6l-4 2-2 1c-3 1-11 3-14 2v-2h-4 0c-1 1-3 1-5 1-5 0-11-1-16-3l-7-3h0c4 0 8 0 12-1s8-5 12-7c-2-1-5-2-7-3-1-1-3-3-3-4h1c2 0 4-1 6-1l1-1v-1c1-1 3-1 4-1 6-2 10-3 16-3-2-1-5-2-7-4-3-1-5-4-7-5h-1c1-1 3 0 4 0 6-1 10-2 15-5l1-2 11-10 1 20c1-1 3-3 4-5 1-1 2-3 4-5-1 0 0-1 0-1v-1h-1c-1-1-1-1-1-2-1-1 0-2 0-3 3-4 6-7 6-12v-1l-1-1 1-1c1 1 1 2 1 2h1v-6z" class="K"></path><path d="M564 246c2-1 3-2 4-2 1 1 2 1 2 1l1 1h0-7z" class="J"></path><path d="M557 228h1c-2 2-4 5-6 5-1 0-1 0-2-1h1l6-4z" class="H"></path><path d="M564 246h7c1 0 2 1 3 2h1l-2 1-1-2h-2-7l1-1zm-20 0c4 0 11-2 14 1v1h0 1v1h-4 0l1-1h0l1-1h-2-1c-2 1-5 1-6 0-2 0-3 0-4-1z" class="B"></path><path d="M563 247h7 2l1 2c-3 1-11 3-14 2h2c0-2 0-2 1-3l1-1z" class="I"></path><path d="M534 247c4-1 7 0 10-1 1 1 2 1 4 1 1 1 4 1 6 0h1 2l-1 1h0l-1 1h0 0c-1 1-3 1-5 1-5 0-11-1-16-3z" class="H"></path><path d="M577 231h0c0-1-1-1-1-1h-1-1-3c-1 1-2 0-3 1h-1-2c-2 0-3-2-5-3l9-2c3 2 9 1 11 4l-3 1z" class="B"></path><path d="M574 206v1c1 1 1 1 1 2l1 3v2h0c0 1 0 2 1 3 0 2-1 4 0 5h0c0 1 1 1 1 2l-2 2h1 1l1-1s1 0 1-1h1l5-5v3h0 1l-5 6c-1 0-2 1-2 2-2-3-8-2-11-4 2 0 6 0 7-2-1-1 0-2 0-3-1-2-1-3-1-5v-3c-1 0-1-1-1-2v-1c0-1 0-1-1-2l1-2z" class="E"></path><path d="M574 206l11-10 1 20c1-1 3-3 4-5 1-1 2-3 4-5-1 2-1 2-2 3 0 1-1 3-2 4-1 2-2 3-3 5l-1 1-5 5h-1c0 1-1 1-1 1l-1 1h-1-1l2-2c0-1-1-1-1-2h0c-1-1 0-3 0-5-1-1-1-2-1-3h0v-2l-1-3c0-1 0-1-1-2v-1z" class="L"></path><path d="M600 180l1-1v10c0-2 0-2 1-3v4 3 1l-1 1v2c-2 7-5 16-10 22-1 2-3 4-4 6v4 10l1 1c-3 2-6 5-9 6v-1-2l-1-4c0-2 0-5-1-6-5 4-10 7-16 8h0v-1h2c5-3 10-5 14-9l3-1c0-1 1-2 2-2l5-6h-1 0v-3l1-1c1-2 2-3 3-5 1-1 2-3 2-4 1-1 1-1 2-3-1 0 0-1 0-1v-1h-1c-1-1-1-1-1-2-1-1 0-2 0-3 3-4 6-7 6-12v-1l-1-1 1-1c1 1 1 2 1 2h1v-6z" class="N"></path><path d="M600 180l1-1v10 3c-1 1-1 2-1 3l-1 5c0-1-1-2-1-3l2-11v-6z" class="C"></path><defs><linearGradient id="AM" x1="594.936" y1="208.804" x2="591.914" y2="207.825" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232323"></stop></linearGradient></defs><path fill="url(#AM)" d="M598 186l-1-1 1-1c1 1 1 2 1 2h1l-2 11c0 1 1 2 1 3l-4 9c-1 3-3 7-6 10l-2 3h-1 0v-3l1-1c1-2 2-3 3-5 1-1 2-3 2-4 1-1 1-1 2-3-1 0 0-1 0-1v-1h-1c-1-1-1-1-1-2-1-1 0-2 0-3 3-4 6-7 6-12v-1z"></path><path d="M598 186l-1-1 1-1c1 1 1 2 1 2h1l-2 11c-1 2-2 5-3 7l-1-1c0-1 0-2-1-2 0-1 0-2-1-2 3-4 6-7 6-12v-1z" class="M"></path><path d="M559 380l5 4h0l1 2 1 3-2 2c-1 2-2 5-3 7l1 2c0 1 0 2 1 2v-1c1-1 3-4 4-5 1 1 1 2 1 3h0v3c3 1 6 0 8 0l2-1c1 1 1 1 1 3s-1 4-2 6c-1 5-2 10-6 15v-8h-1v1l-1 4c0 3-2 7-5 9l-3 1c-1 1-1 1-1 2-2 0-4 1-6 2s-7 1-10 0c-2-1-3-2-5-2v-1c-7-6-8-12-9-21 1-6 3-12 6-17 0-2 0-3 1-4h1l2-2c4-3 7-3 11-3h6c1-2 1-4 2-6z" class="R"></path><path d="M544 436h2 1c1-1 1-2 2-2s1 0 2-1h1c2-2 4-4 6-7v1s0 1-1 1h0c-1 3-4 5-6 7l6-4h1l-2 2-1 1-1 1h0v1c-2 1-7 1-10 0z" class="O"></path><path d="M558 431c0-1 2-2 2-3 1 1 0 1 1 2h1c2-1 3-3 4-5 1 0 1-1 1-1 1-2 1-2 2-2 0 3-2 7-5 9l-3 1c-1 1-1 1-1 2-2 0-4 1-6 2v-1h0l1-1 1-1 2-2z" class="W"></path><defs><linearGradient id="AN" x1="561.506" y1="407.678" x2="565.176" y2="404.797" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#464745"></stop></linearGradient></defs><path fill="url(#AN)" d="M560 411c0-2 1-4 0-5-1-2-1-5-1-7l2-1 1 2c0 1 0 2 1 2 1 1 3 2 4 3v1c1 1 1 1 1 2v1h-1c1-2 1-2 0-3h-1c-1 1-4 3-4 4s-1 2-1 3h-1v-2z"></path><path d="M541 408c0-2 2-3 3-4 1 0 2-1 3-1h0c-1 3-2 5-3 7v1c1 2 0 4 2 5 1 1 2 0 3-1h0l2 1h1c-2 1-4 4-6 4-1 0-2-1-3-1-3-3-3-8-2-11z" class="Q"></path><path d="M535 405v1c-1 4-1 8 2 12 1 2 3 3 5 5v1c-2 0-4 0-5-1 2 3 4 5 7 6 2 1 4 1 5 1 3-1 4-3 5-5h1c-1 3-3 5-5 6l-6 3-1-1h-2c-3-2-7-6-8-10-1-6 0-12 2-18z"></path><path d="M533 423c1 0 3 1 3 2l1 1c0 1 1 1 1 1 1 1 1 1 2 1 1 1 2 2 3 2h1s1 0 1 1h1 0c-1 1-3 0-3 2-1 0 0 0 0 0h-2c-3-2-7-6-8-10z" class="K"></path><defs><linearGradient id="AO" x1="556.503" y1="399.182" x2="538.989" y2="394.606" xlink:href="#B"><stop offset="0" stop-color="#333233"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#AO)" d="M559 380l5 4h0l1 2 1 3-2 2c-1 2-2 5-3 7l-2 1c0 2 0 5 1 7 1 1 0 3 0 5v2c-2 0-4 0-5 1-1 0-2 2-3 2h-1l-2-1h0c-1 1-2 2-3 1-2-1-1-3-2-5v-1c1-2 2-4 3-7h0c-1 0-2 1-3 1-1 1-3 2-3 4-1-2-2-2-4-3l-2 1v-1h-1c1-3 5-8 7-10 1-1 2-1 3-2v-1c-4 0-5 1-8 3 0-2 0-3 1-4h1l2-2c4-3 7-3 11-3h6c1-2 1-4 2-6z"></path><path d="M537 404h-1 0c1-4 4-8 8-10v5c-1 1-3 3-5 4-1 1-2 1-2 1z" class="T"></path><path d="M545 394c1 1 2 1 2 4v5c-1 0-2 1-3 1-1 1-3 2-3 4-1-2-2-2-4-3v-1s1 0 2-1c2-1 4-3 5-4v-5h1z" class="U"></path><path d="M545 394v-1h0 2c1 1 2 4 2 5v2c0 2 0 3-1 4l1 1c1 0 1-1 2-2h1l-3 3c0 2 0 3 1 5 1 1 0 3 1 4v1l-2-1h0c-1 1-2 2-3 1-2-1-1-3-2-5v-1c1-2 2-4 3-7h0v-5c0-3-1-3-2-4z" class="E"></path><path d="M546 410l2 2v1c-1 0-1 0-2 1l-1-1c0-1 1-2 1-3z" class="H"></path><path d="M548 412h1v3h2v1l-2-1c-1 0-2-1-3-1 1-1 1-1 2-1v-1z" class="B"></path><path d="M546 410l3-4c0 2 0 3 1 5 1 1 0 3 1 4h-2v-3h-1l-2-2z" class="J"></path><path d="M556 399h1v1l-1 1h1v1c0 2 1 3 2 4 1 2 1 3 1 5v2c-2 0-4 0-5 1-1 0-2 2-3 2h-1v-1c-1-1 0-3-1-4-1-2-1-3-1-5l3-3c2-1 3-3 4-4z"></path><path d="M559 380l5 4h0l1 2 1 3-2 2c-1 2-2 5-3 7l-2 1c0 2 0 5 1 7 1 1 0 3 0 5 0-2 0-3-1-5-1-1-2-2-2-4v-1h-1l1-1v-1h-1c1-1 3-3 3-4s0-2-1-2c-2-1-8-3-9-5 0-1 0-1 2-2h6c1-2 1-4 2-6z" class="G"></path><path d="M494 570c2-2 5-5 8-6l-2 7h0 2 0l1 1v2l1 1c0 1-1 1 0 1v2c0 1 0 1 1 1v2c0 1 0 2 1 3 0 1-1 4 0 5 0 1 0 1-1 2-2 1-4 3-6 4l-6 6c-2 2-4 4-5 7l-2 2c-1 2-2 5-3 8l-1 3c-1 4-1 8-1 12h-5c-3-2-4-6-7-8 1 2 1 5 3 7 0 1 1 2 1 3-8 2-17 5-26 5-7 0-14-2-20-4 0-1 0-2-1-2v-5c1-4 3-9 2-13 3-1 5 0 8 0 1-1 2-2 3-4l1-1c1 1 1 1 1 2 2 2 3 4 5 6l4 2h0l11 7c-1-5-3-10-6-14h0c4 0 7 1 11 3l-5-5c0-2 1-4 1-6 0-4 1-7 2-10 1-4 2-7 3-11 1 0 3-2 4-3l3-3c0-1 2-2 2-3v1l1 3h0c1-1 2-3 3-4 1-4 4-6 6-9h0c-1 5-1 9 0 13h0 1c0-4 4-8 7-10z"></path><path d="M480 580l1-2c0-1 0-1 1-2h0v1c-1 2-1 4-1 6v1h-1v3c0 1-1 2-2 3h-3c-1 0-3-2-3-3s1-3 0-4h1v-1h1v-1h-1l-1 1h-1l3-3v3c0 2 0 3 1 5 0 0 1 0 1 1h1 1c1-2 2-6 2-8z" class="D"></path><path d="M487 580c0-4 4-8 7-10 1 1 1 3 1 5l-1 1c-1 2-4 4-6 4h-1z" class="H"></path><path d="M476 577l1 3h0c1-1 2-3 3-4v3l-2 1c0 1 1 2 1 3v-1c0-1 0-2 1-2 0 2-1 6-2 8h-1-1c0-1-1-1-1-1-1-2-1-3-1-5v-3c0-1 2-2 2-3v1z" class="B"></path><path d="M474 579c0-1 2-2 2-3v1 3h0c-1 1-1 2-1 3v1c1 1 1 3 1 4 0-1-1-1-1-1-1-2-1-3-1-5v-3z" class="J"></path><path d="M493 601c-4-2-6-3-8-6-2-1-2-3-2-5 0-1 0-2 1-2h1c1-1 2-2 4-2s6 2 8 4v1c1 1 2 2 2 4l-6 6z" class="L"></path><path d="M439 612l1-1c1 1 1 1 1 2 2 2 3 4 5 6l4 2h0c-1 0-1 1-1 1 0 1 0 2 1 2 0 2 1 5 0 7 0 2-1 3-2 4-3 1-4 0-6-1-2 0-3-1-4-3 0-3 0-5 2-7v-1-7-3s-1 0-1-1z" class="B"></path><path d="M441 613c2 2 3 4 5 6-1 1-2 1-3 2-1-1-2-3-3-5 0-1 1-2 1-3zm2 11h2c1 1 1 2 2 3s1 2 1 4c-1 1-1 2-3 2h-1c-1 0-2 0-2-1-1-1-1-3-1-4 0-2 1-3 2-4z" class="I"></path><path d="M443 627h0c1-1 2 0 2 0 1 1 1 2 1 3h0l1 1-1-1c-1 0-1 1-2 1h0c-1 0-1-1-2-2 0-1 1-1 1-2z" class="M"></path><path d="M482 621c-2-2-4-2-6-4-4-4-6-8-6-14 0-1 1-5 2-6s4-2 5-2c4 1 8 9 11 13l-2 2c-1 2-2 5-3 8l-1 3z" class="D"></path><defs><linearGradient id="AP" x1="483.178" y1="609.284" x2="477.009" y2="609.804" xlink:href="#B"><stop offset="0" stop-color="#49494a"></stop><stop offset="1" stop-color="#646464"></stop></linearGradient></defs><path fill="url(#AP)" d="M483 618c0-1-1-1-1-1-1 0-2-1-3-1-3-2-4-5-5-8 0-2 0-4 1-5s1-2 3-2c2 1 5 5 7 7 0 1 0 1 1 2h0c-1 2-2 5-3 8z"></path><path d="M239 184c0-4-1-12 2-16 0-1 0-1 1-2 2 0 3 1 4 3 3 3 5 8 8 12 3 5 6 8 9 12 4-1 10-1 14 0 2 0 4 1 5 2-1 0-3 0-5 1l11 2c3 0 7 2 9 1 1 0 2-1 3-1 5-4 19-20 25-20l2 2c1 2 1 5 2 7 0 5 0 11-1 16-1 6-3 10-7 15l5 4h-5c2 2 5 4 8 6 4 3 8 8 11 12l-10-4c6 5 12 11 17 17 3 3 5 6 7 10-3-2-5-4-8-5 6 6 11 12 15 19l-8-5c6 8 11 16 14 25l-4-5c2 6 5 11 7 16l-4-5c1 5 5 9 7 14-2-2-3-4-5-6 2 5 6 10 8 15v3l-5-8c1 2 2 5 3 8 3 5 3 11 4 17l-5-10 2 9c1 4 1 7 1 11-2-3-3-8-4-12 0 9 2 18-1 27l-1-13c-1 3-1 6-1 9v10c-1 2-1 5-2 6h-1v-3h0c-1 2-1 8-2 8-2-2-1-5-1-7-1 5-2 10-4 16 0 1-1 3-1 5 0-3 0-8-1-10v10c-1 7-4 14-6 20 0-2 0-4-1-6l-3 19-1-6c-1 5-2 9-6 13 7-3 13-5 21-4h1c-3 0-6 0-9 1 7 0 13 1 18 6l2 3c-3-2-5-3-8-4l2 1c5 4 7 10 8 17v7l-3-8c0 4 1 9 2 13v3l-3-7 1 21-3-6c1 7 2 13 2 20-1-2-1-4-2-6v-3c0 8-1 14-4 21 0-2 0-5-1-8h-1c0 6-1 12-3 18v-8c-1 5-2 10-5 15l1-7-3 9-3 9v-5c-2 3-3 7-5 9v-2c-1 0-1 2-2 3 0 1-1 2-2 4v-1-2c-1 1-1 3-1 4-1 2-2 5-3 7h-1v-3h0c-1 2-1 5-2 7l-1 9c1-2 4-5 6-5-2 7-7 14-14 18-2 1-6 2-8 3h0c1 0 3 0 5-1 7-2 11-7 17-12 0 2-1 4-2 6 4-2 7-4 11-7l4 5-1 1c-3 2-6 5-9 8-2 1-4 3-7 4l-5 2-1 1h1l7-3 6-3 1 2c2 0 5 1 7 0 1 0 3-1 4-2 1 0 2 0 4-1 3 2 6 4 9 5h1l1 1v2c1 0 2 0 2 1 0 0-2 1-3 1l1 1 2-1h5v1l3-1 8 3h0l-19 9c8-2 15-4 22-8-2 3-5 5-8 7-10 6-21 9-32 8-5 0-10-1-15-2 3 1 6 2 8 3 8 2 16 3 24 2-8 4-15 6-24 7-21 3-43-3-60-17-2 1-4 2-6 2-10 2-21-4-29-9l8 9c-7-2-13-9-18-14l3 8c-4-4-7-8-10-12l2 8c-2-2-4-4-5-7-2-2-4-6-5-9 1 6 3 14 1 20-1-3-1-6-2-9 0 5-1 10-2 14-3 11-11 17-20 22 4-4 7-7 9-12-5 6-11 12-19 15-3 1-7 2-11 3 5-3 9-7 13-11l-9 6c-6 3-13 4-20 5l9-5-18 3c-9 2-18 4-27 4 6-2 13-3 18-8-6 2-12 5-17 6-6 1-11 0-16 0 7-2 13-5 19-10-4 2-9 4-14 5-9 2-18 1-26-2 5 0 10-2 15-4-10 1-20 0-29-5 4 0 7 1 10 0 4 0 8-1 11-2-10-1-20-4-29-9-3-2-5-4-8-6 6 2 12 4 19 5-4-2-7-4-10-6-4-3-8-7-11-10l6 3c0-1 0-1-1-1-7-6-10-13-12-21 5 8 11 14 19 19-1-3-4-6-6-8-3-3-5-6-8-9-5-10-7-23-4-34-1 2-1 5 0 8 1 6 4 15 9 18-2-5-5-10-6-16-1-5-1-13-1-18l4 15c-1-11 1-22 2-34l1 15c1-6 2-13 2-19 0-5-1-9-2-14 3 4 4 10 6 15-1-9-3-16-6-25 3 4 5 8 7 12-1-10-6-16-12-22 4 2 6 4 10 7-1-2-2-3-3-5-5-6-12-8-19-9 6-2 10-1 17 1l-8-5c1 0 3 0 4 1 7 1 11 4 15 9-1-2-1-4-2-5-1-2-3-4-5-5 3 1 5 2 8 4 4 4 6 9 8 15 1-4 1-8 0-12l1 2c6 8 8 18 8 27-1 3-1 5-2 7 2-3 4-8 5-12v-6l1 5c1 10 0 18-4 26 4-4 6-10 8-15-1 9-4 17-5 26-2 7-2 14-1 22 1-2 1-5 2-7 1-3 2-8 4-11-1 11 0 22 3 32 1-4-1-9-1-13l5 14c2 3 3 7 4 9l-2-12 14 16-5-8 8 6c10 8 21 11 34 9 5-1 11-2 14-7 7-10 0-24-2-34l-3-17c-1 4-1 8-1 12-2-9-4-19-5-28v5l-1 7c-3-8-5-17-5-26 0-4 0-7-1-11l-1 13c-3-10-2-18-2-28-1 3-2 6-3 8-1-10 1-20 4-30 2-3 4-7 5-11-1 1-2 3-3 4-3 6-5 11-7 17-1-12 3-22 8-32-3 2-5 7-6 10 1-4 1-9 3-13 2-5 6-10 10-15-4 2-8 6-11 10 4-10 11-17 16-26-3 2-6 6-9 9 1-2 2-3 3-5 2-5 5-10 8-14 3-3 6-6 9-10-4 3-8 6-11 9 5-8 11-15 17-23-3 2-7 5-10 8 3-5 7-10 11-14-1 1-3 2-4 3-3 2-5 4-8 5l44-46c-5 3-9 6-13 10 4-8 10-15 17-20 3-2 6-4 9-7l-9 6c7-9 17-14 25-21-3 1-6 3-9 4 2-3 6-4 9-7-2 0-4 1-6 2 2-2 4-3 5-5 1-1 2-2 3-2 1-1 2-1 3-2 1-2 2-4 3-5-4-3-9-4-13-5 0 1 1 3 2 4-1 0-3-2-5-1v1h0c-1-1-2-1-3-1h-1v2c-1 0-3-2-4-2-1 1-1 1-1 3h0l-3-3s0 1-1 1c-2-1-4-3-6-4-3-4-5-5-6-10 0-2-1-3-2-4s-1-2-1-3c1-2 3-5 4-6 1-2 3-4 3-6 3-6 0-11 1-17h0c1-3 3-6 4-9v-1c0 1 0 1 1 2 1-2 2-4 4-6 2-3 4-5 6-7l-3 6 3-3c2-3 4-5 4-10v-10-5z"></path><path d="M234 333s0-1 1-1c1-1 1-1 2-1l-1 3-1-1h-1z" class="K"></path><path d="M269 223l1-1-1 6v-2c-1-1 0-2 0-3zm44 126l1 4c-1 0-1 0-2 1l-1-5h2z" class="I"></path><path d="M295 459h1c1 1 2 1 3 1l1 1c-1 0 0 0-1 1h-1c-1 0-3-1-3-2h0v-1zm19-72c1 2 1 3 1 5h0c-1 0-2-2-2-4h0l1-1z" class="G"></path><path d="M330 597v-1c1 0 1 0 2-1h0-1 0-1 0l3-2 2 2-5 2z" class="C"></path><path d="M293 252l1-1v4l-1 1v2l1 1c-1 1-1 2-2 3h0l1-3v-1-2l-1 1 1-5z" class="G"></path><path d="M273 287c0-2 1-3 2-4 0 0 1 0 1 1v4h0l-2-2-1 1z" class="K"></path><path d="M267 442h0l-2-4c2 0 4 2 6 3-2 1-2 1-3 1h-1z" class="J"></path><path d="M342 465l2-2c0 1 1 2 2 3-1 1-1 2-1 3-1-1-2-2-3-4zm-64 13l2 2c0 2-1 3-2 4l-2-2h0c2-1 2-2 2-4z" class="B"></path><path d="M267 223l1-6c1 1 2 3 2 5l-1 1h-2z" class="J"></path><path d="M309 260c-1 0-2-1-2-2 0-2 0-4 1-5v2l1 1 1 1c-1 1 0 2-1 3z" class="G"></path><path d="M232 266c1 1 2 0 3 0 1-1 1 0 2 0l-1 1c-1 1-2 1-4 1h-1l-3 1 4-3z" class="B"></path><path d="M308 543l-2-8 5 5h-3v3z" class="I"></path><path d="M226 422h1c1 4 1 8 1 12l-1-2-1-10z" class="F"></path><path d="M306 469h0l-2-4c2 1 7 1 8 3-1 1-1 0-3 0v-1h-1l-1 1-1 1z" class="J"></path><path d="M289 491l2-1c2 2 5 4 6 6-3-1-5-3-8-4v-1h0z" class="F"></path><path d="M230 223l1 2c-1 1-1 2-2 3h-2c0-2 2-4 3-5z" class="E"></path><path d="M283 307c0-4 1-8 1-11 0-1 0-3 1-4 0 3 0 9-1 11 0 1 0 3-1 4z" class="D"></path><path d="M317 538c2 3 4 5 5 9h-1c-2-2-3-6-4-9z" class="K"></path><path d="M230 223l6-7c-1 3-3 7-5 9l-1-2zm79 73c2 0 4 2 5 3s2 3 2 4c-2-1-4-3-5-4l-2-1v-2z" class="F"></path><path d="M259 341v-8h1v8c0 1 1 3 1 4l-1-1c-1 1-1 2-1 2v-5z" class="B"></path><path d="M251 448h1l3 3c1 2 2 3 3 5l-1 1-6-9zm57-132c-2-1-5-6-6-8l8 7-2 1z" class="E"></path><path d="M245 304c0 1-2 3-3 5h0c-2 0-2 0-3 1h-1c2-3 4-4 7-6z" class="H"></path><path d="M306 469l1-1 1-1h1v1c1 2 2 2 2 4h-1v-1h-1v1c1 0 1 1 1 2l-4-4v-1z" class="I"></path><path d="M335 312c0 1 1 2 1 3l-1 1-1 1s-1 0-1 1c0 0 0 1 1 1 0 1-1 2 0 3-1 0-2-2-3-3h0c1-2 2-5 4-7z" class="G"></path><path d="M195 375v1l-4 4h0l-3 2-1-1c2-1 2-3 4-4h0c2 0 3-1 4-2z" class="I"></path><path d="M273 226c2 1 1 2 1 3v1l-3 5c-1 2-2 3-3 5 2-5 4-9 5-14z" class="E"></path><path d="M281 229h1 1c-1 5-3 9-5 13 0-4 1-9 3-13z" class="B"></path><path d="M291 488c1 0 2 1 4 1 2 1 3 3 5 5-2-1-4-1-6-2l-4-4h1z" class="F"></path><path d="M274 330v1s0 1 1 1c1 2 1 5 1 7v8c-1-3 0-6-2-9v-8z" class="D"></path><path d="M291 463c1 1 2 2 3 4l-2-1v2c-3-2-4-4-5-6h0 1c1 1 2 1 3 1h0z" class="E"></path><path d="M259 292c1-1 0-3 1-4l1 1v1h-1v5c-1 0-1 1-2 2h-1 0l-1-1v-5c0 1 1 2 1 2l1 1c0-1 1-1 1-2h0z" class="D"></path><path d="M333 593c2-1 5-3 7-3 0 0 1 0 2 1-2 1-4 3-7 4l-2-2z" class="J"></path><path d="M232 266l2-1c2-2 4-3 6-3 0 2-2 3-3 4-1 0-1-1-2 0-1 0-2 1-3 0zm39 205l3 3h0c1 2 3 3 4 4 0 2 0 3-2 4h0l-2-1h1c1 0 1 0 2-1l-2-2c-1 0-2-1-2-2h1v-1l-3-4z" class="H"></path><path d="M342 465h0c-1-1-6-6-6-8 3 1 6 4 8 6l-2 2zm-78-142c1 0 1-2 1-2 2-2 3-5 5-7v1c-1 2-3 5-4 8v5 1-2h-1c-1-1-1-3-1-4z" class="C"></path><path d="M363 456l-1-6c-1 0-1 0-1-1h1c2 2 3 4 4 7h0l-1 1c-1 1 0 1-1 0-1 0 0 0-1-1z" class="G"></path><path d="M228 452v4h1v-4c1 6 1 12 0 18-1-2-1-4-1-6v-12z" class="D"></path><path d="M319 622l5 2 1 1h0c-2 1-3 1-4 1l-7-2c2-1 4-1 5-2z" class="N"></path><path d="M259 346s0-1 1-2l1 1c0 2 1 6 1 7-1 2-1 3-1 5-1-3-1-7-2-11z" class="I"></path><path d="M310 378l1 1h1c1 1 1 2 2 3v5l-1 1c0-1-1-2-1-3s-2-5-2-6v-1z" class="H"></path><path d="M187 381l1 1 3-2-1 3c-1 2-5 4-7 6 1-2 2-6 4-8z" class="D"></path><path d="M300 252c-1 1-1 1-1 2l-1-1v-10h0c0-1 0-1 1-2l1 3c0 2 1 6 0 8z" class="F"></path><path d="M199 364c1 0 1 1 1 2v3 1c-1 2-3 4-5 6v-1c1-3 4-4 4-7 0 0 0-1-1-1l-2 1 3-4z" class="E"></path><path d="M291 520h2l12 4c-5 0-10-1-14-2v-2z" class="L"></path><path d="M280 504c2-1 5 1 7 1l9 3h0c-2 0-7-1-8 0-3-1-6-3-8-4z" class="C"></path><path d="M297 516c1 0 2 1 3 1l-1 1 1 1v1h-4c-1 0-1-1-2 0h-1-2c-1 0-2-1-3-1v-1c1 0 7 1 8 1h1l-2-2 2-1z" class="D"></path><path d="M269 223c0 1-1 2 0 3v2l-1 4c-1 0-1-1-1-1-1-2-1-3-1-4s0-3 1-4h2z" class="H"></path><path d="M303 294l-2-3c-2-2-3-4-3-6 1 2 2 4 4 5 1 2 3 2 4 3l3 3v2l-4-4h-2z" class="E"></path><path d="M361 466l-1-12c1 2 1 3 1 4 2 5 3 8 3 13h-1c-1-2-1-3-2-5z" class="B"></path><path d="M255 451v-1l3 4 2 3c2 1 2 2 2 4h0v1l1 2c-2-2-4-5-6-7l1-1c-1-2-2-3-3-5z" class="G"></path><path d="M330 316c0-1 2-4 2-5-1-1-1-1-1-2l2 2 1-1c0 1 1 1 1 2-2 2-3 5-4 7 0-1-1-2-1-3z" class="C"></path><path d="M346 466c2 4 6 11 5 16l1 1h-1 0c-1-5-3-11-6-14 0-1 0-2 1-3z" class="M"></path><path d="M314 545c0 1 0 3 1 4l-1 1h-1c-1-1-1-1-2 0-1 0-1 1-1 2h0c-1-2-2-3-2-5 2-1 4-1 6-2z" class="G"></path><path d="M268 232c-2 5-3 8-6 12 0-3 2-6 3-9s0-5 1-8c0 1 0 2 1 4 0 0 0 1 1 1z" class="J"></path><path d="M290 496l2 2 3 2c-3 0-7 0-10-2h-1c-1-1-2-2-3-4l4 2v1h1 3l1-1z" class="D"></path><path d="M247 305v1s-1 2-1 3l-8 8c-1 1-3 2-4 3l6-8 7-7z" class="M"></path><path d="M201 415c1-3 3-6 5-8-1 3-2 8-3 12h0c-1 0-2-1-3-2l1-2z" class="B"></path><path d="M201 415c1 1 2 2 2 3v1h0c-1 0-2-1-3-2l1-2z" class="F"></path><path d="M253 242c2-1 3-2 3-3 1-1 2-3 3-4v-2-2h0v-3h1l1 1c-1 1 0 3-1 5v1s-1 1-1 2h0c-1 2-5 7-6 7l-1 1v-2l1-1z" class="D"></path><path d="M257 336c0-3 1-6 3-9l2-3c0 3-1 5-1 8l-1 1h0-1v8l-1-1v-3c0-1 0-1-1-1z" class="G"></path><path d="M342 484h0c1 2 1 3 1 5l1-1v-1l1 1v-3c1 5 1 10 1 15-1-3-1-7-3-9-1-2-1-5-2-7h0 1z" class="C"></path><path d="M314 353c0 3 0 7 1 11 0 4 1 8 0 12h0l-3-22c1-1 1-1 2-1z" class="H"></path><path d="M224 401c0-3 1-7 3-9 1-1 1-2 2-4-1 7-4 13-4 19-1-2 0-4-1-6z" class="J"></path><path d="M363 456c1 1 0 1 1 1 1 1 0 1 1 0l1-1h0c1 5 0 8 0 13h-1v-5h0c-1-1-1-2-1-3l-1-5z" class="D"></path><path d="M277 383c2 5 2 11 3 16-3-2-3-7-4-11 1-1 0-4 1-5z" class="I"></path><path d="M258 427h3c1 1 1 1 3 2l2 2c1 1 1 2 1 3-4-2-7-4-9-7z" class="L"></path><path d="M251 242v1h1v2c-3 4-7 7-11 10 1-2 3-3 4-5l6-8zm-49 108h2c1 0 1 0 2-1h1 2l-1 1h-1c-5 2-8 8-12 12 2-4 5-9 7-12z" class="C"></path><path d="M223 399c0 1 0 2 1 2 1 2 0 4 1 6l2 15h-1c-1-8-3-15-3-23z" class="B"></path><path d="M354 472c1 1 2 3 3 3h1 0c1 4 0 9-1 13l-3-16zm-24 5c-1-2-2-4-3-5 1 0 1 0 2 1h0c0-2-2-4-2-6 2 1 3 3 4 4s1 2 2 3c-1 2-1 3-2 4l-1-1z" class="C"></path><path d="M331 471c1 1 1 2 2 3-1 2-1 3-2 4l-1-1c1-2 0-3 0-4s1-1 1-2z" class="B"></path><path d="M272 414v-1c2 1 3 3 4 4l6 12c-1 0-1 0-2 1h0c-1-2-2-3-2-5-1-3-1-5-3-7-1-2-2-3-3-4z" class="I"></path><path d="M311 242v-1c1 0 1-1 2-2v-1l2-2-1 8v1l-1-1h1v-1-4c-1 1-1 2-2 3v1s0 1-1 1l-1 5-1-6c0-1 0-2-1-3l1-1 1-1v4h1z" class="D"></path><path d="M266 495l9 3 12 5h0c-2 0-4 0-6-1h-1l-1-1h-3c-1-1-1-1-2-1l-1-1h-2 0c-1-1-4-3-5-4z" class="G"></path><path d="M293 318l4 5h0-1c0 4 3 7 5 10l3 9h0c-2-3-4-7-6-11-1-2-2-3-3-5-1-3-1-6-2-8z" class="B"></path><path d="M238 310h1c1-1 1-1 3-1l-2 2c-1 2-3 4-4 5-1 0-2 0-3 1-2 1-3 2-5 3l10-10zm41 151c4 4 8 7 11 12-6-3-9-6-12-12h1zm24-167h2l4 4 2 1c0 2 3 4 5 6l2 2 2 4h-1c-1-1-1-3-3-5-4-4-9-7-13-12z" class="I"></path><path d="M286 454h0c-2 0-3-1-4-2-3-3-7-7-9-11 1 1 4 3 5 4h0 0c3 3 6 6 8 9z" class="B"></path><path d="M297 473h0c1 0 2 0 3 1l1 1v-2h0c2 2 5 5 7 8l-4-3-2-1 4 6c-3-2-8-6-9-10z" class="I"></path><path d="M349 461c-4-3-7-7-11-10 5 1 9 3 12 7-1 1-1 2-1 3z" class="C"></path><path d="M250 414h0c1 2 2 4 3 5 0 2 2 3 3 4h0c0-1-1-2-1-3 1 1 1 1 2 1 2 3 4 5 7 8-2-1-2-1-3-2h-3-1c-3-4-5-8-7-13z" class="E"></path><path d="M251 448c-2-2-3-4-4-6v-1l1 1c2 0 2 1 3 2 1 0 1 1 2 2 0-1 0-2-1-2 0-1-1-2-1-3h0c1 1 2 2 3 4 0 1 0 2 1 3-1 1 0 1 0 2v1l-3-3h-1z" class="H"></path><path d="M283 307c1-1 1-3 1-4 0 6 0 11 1 17v3c0 1 0 2 1 2v2l-1 1v-1c-2-6-2-14-2-20z" class="C"></path><path d="M274 222l1-9 2 8c0 2 0 4-1 6v2c-1 0-1 0-1 1h-1v-1c0-1 1-2-1-3 0-1 1-3 1-4z" class="H"></path><path d="M274 222c1 1 1 1 2 3 0 1-1 3-2 4 0-1 1-2-1-3 0-1 1-3 1-4zm34 321v-3h3c1 1 2 3 3 5-2 1-4 1-6 2 1-1 0-3 0-4z" class="M"></path><path d="M279 223c0-1 0-2 1-2v6 2c-2 7-5 13-10 19l-1-1c3-3 5-6 6-10 4-4 4-9 4-14z" class="F"></path><path d="M227 432l1 2c0 3 1 6 1 10 1 3 0 6 0 8v4h-1v-4l-1-20z" class="E"></path><path d="M312 273c-3-3-4-6-5-9l10 11c2 1 4 3 5 5-2-1-4-2-6-4 0-1-2-3-3-4l-1 1z" class="H"></path><path d="M254 443h1c3 2 4 5 5 8-1 1-1 2-1 3h-1l-3-4c0-1-1-1 0-2-1-1-1-2-1-3l2 2-2-4z" class="M"></path><path d="M345 460h1c0 1 1 2 2 3v1c2 1 3 3 3 5h0v1s0 1 1 2v3h0-1c0-1-1-2-1-3s-1-3-2-4c0-1-1-2-1-2-1-1-1-2-2-2l1-1c0-1-1-2-1-3z" class="K"></path><path d="M266 471c1 0 1-1 1-1h1c0-1-1-1 1-2l2 3 3 4v1h-1c0 1 1 2 2 2l-1 1c-1 0-3-3-5-4l-3-4z" class="N"></path><path d="M269 445v-2c1 1 2 1 2 3 1 1 2 2 2 3h0l2 3h0c1 3 3 6 4 9h-1c-1-3-3-5-4-8-2-2-4-5-5-8zm5 36c-6-4-10-9-14-15 3 2 6 8 9 9 2 1 4 4 5 4l1-1 2 2c-1 1-1 1-2 1h-1z" class="F"></path><path d="M205 437l2 1h0c0 4-3 9-4 14l-1 1c1-4 2-8 2-12l-6 11c0-2 1-3 2-4l3-6v-1c0-1 1-1 1-2h0l1-2z" class="D"></path><path d="M245 304v-1l8-7c0 2 0 5-1 6l-1-1-3 2c0 1-1 1-1 2h0l-7 7v-1l2-2h0c1-2 3-4 3-5z" class="K"></path><path d="M196 434h0c-2 5-4 9-7 13l3-10c-2 2-3 4-4 6 1-4 4-8 6-12 1 1 1 2 2 3zm76-21s-1-1-1-2 1-2 0-3v-3c0 1 0 1 1 2v1-2l-1-2 1-2c1 5 4 9 4 15-1-1-2-3-4-4v1-1z" class="C"></path><path d="M287 293c1 1 1 2 2 3 3 9 4 18 8 26v1l-4-5-6-25z" class="I"></path><path d="M322 310l6 15-1 1-1-3c-1-2-3-5-4-7l-3-5h1l2 2s0-2-1-3h1z" class="P"></path><path d="M204 511c0-4 1-7 3-11-1 7-1 12 0 19v1l-1 1-1-2-1-8z" class="N"></path><path d="M280 227h1v-3-10-1c2 3 3 9 3 13-1 1-1 2-1 3h-1-1-1v-2z" class="D"></path><path d="M309 517c1 0 4 1 5 2l1 1v2c1 1 1 2 2 3-1 1-1 1-3 0s-5-4-6-6h3v-1c-1 0-2 0-2-1z" class="N"></path><path d="M364 471c0 3-1 6-2 9-1-8-2-15-7-21 0-1 0-1-1-2 3 2 5 6 7 9h0c1 2 1 3 2 5h1zm-30-200c2 0 4 3 7 3 1 1 3 4 4 6l-4-4-1 1c0 2 2 6 3 8-4-4-7-9-9-14z" class="C"></path><path d="M266 462c2 3 5 6 7 10l5 4c-1-1-2-1-4-2h0l-3-3-2-3c-2 1-1 1-1 2h-1s0 1-1 1v-1c-1-2-2-3-2-5h1 0l1-1c-1-1 0-1 0-2z" class="L"></path><path d="M266 464c1 1 1 2 1 4v1h0c-1 0-1 0-1 1-1-2-2-3-2-5h1 0l1-1z" class="E"></path><path d="M313 290l5 5c2 1 4 3 5 5h0l-3-3-4-3 4 7c-1-2-3-4-5-5 0 1 0 0 1 1 2 3 5 8 6 13h-1c-1-2-1-5-3-7-1-2-3-4-3-6-1-1-1-1-1-2h1c-1-2-2-3-2-5z" class="B"></path><path d="M333 474l1 4 3 6c0 1 0 4-1 6l-5-12c1-1 1-2 2-4zm-9-1c3 3 5 8 7 12s3 8 4 12c-1-1-2-3-2-4-4-6-7-13-9-20z" class="F"></path><path d="M277 221c0 2 0 3 1 4l1-2c0 5 0 10-4 14v-1l1-1c1-1 0-1 1-1v-1h0c0-1 1-1 1-2l-2 1s-1 1-1 2h-1c-1 1-1 1-3 1l3-5h1c0-1 0-1 1-1v-2c1-2 1-4 1-6z" class="J"></path><path d="M274 230h1c0-1 0-1 1-1 0 1 0 2-1 3 0 1-1 1-1 2-1 1-1 1-3 1l3-5z" class="B"></path><path d="M282 528h1c3 1 5 4 7 7l-2 2-1 1c-1-2-3-3-4-5s-1-3-1-5z" class="P"></path><path d="M196 368l2-1c1 0 1 1 1 1 0 3-3 4-4 7-1 1-2 2-4 2h0v-1c-1 0-3 2-4 3l9-11z" class="L"></path><defs><linearGradient id="AQ" x1="197.665" y1="436.578" x2="199.958" y2="431.764" xlink:href="#B"><stop offset="0" stop-color="#2d2c2d"></stop><stop offset="1" stop-color="#484747"></stop></linearGradient></defs><path fill="url(#AQ)" d="M198 430c0-1 0-1 1-1h0v2h0l2-1c0 1 0 1 1 2 0 1-1 3-2 4 0 1 0 1-1 2l-1 1v1c-1 0 0 0-1 1 0 1-1 1-2 2v-1l3-4v-1l-1-1c0 1 0 1-1 2v1c0 1 0 0-1 1v1l-1 3h0-1c1-3 3-7 4-9 0-2 1-3 1-4v-1z"></path><path d="M324 260c6 4 12 8 17 14-3 0-5-3-7-3-3-4-7-7-11-10l1-1z" class="I"></path><path d="M235 231l1-1h1 0v1c1-1 2-1 4 0l-2 3c-1 2-1 3-3 4l-1-1v-1c-1 0-2 1-2 2h-1l3-7z" class="N"></path><path d="M237 231c1-1 2-1 4 0l-2 3c-1 0-1 1-3 0 0-1 1-1 1-3h0z" class="P"></path><path d="M313 606c11 2 23 4 35 3h0c-2 1-4 1-5 1-2-1-6 0-8 0-7 0-14-1-21-3-1 0-1-1-1-1z" class="B"></path><path d="M225 261c-1 1-1 1 0 3 3 2 6-1 9-1-2 2-6 3-9 4-1 0-2 1-3 0s-2-2-2-3c0-2 1-3 2-5l-1 5 3-3v-1l1 1z" class="J"></path><path d="M296 614c-1-1-2-1-3-2-5-3-10-6-14-10l22 11c-1 1-2 1-3 1 0-1-1-1-1-1l-1 1z" class="E"></path><path d="M264 448l2 4h-1-1c0 1 0 1-1 1-1 1-1 2-1 3h0s0 1 1 1c0 1 1 1 1 2h0c1 1 2 2 2 3s-1 1 0 2l-1 1h0-1l-1-1-1-2v-1h0c0-2 0-3-2-4l1-1-1-1c1-1 1-2 1-2v-1c1-1 2-2 3-4h0z" class="C"></path><path d="M209 349h0c2-2 3-4 5-5h2c-4 4-8 7-10 11-2 2-3 5-4 8 0 1-1 2-1 3v1h1c0 1-1 2-2 3v-1-3c0-1 0-2-1-2l9-14 1-1z" class="H"></path><path d="M298 444l2 3h0l1 1 4 4c-2-1-4-2-6-1h0v1c1 1 4 2 5 3h-1l-6-4c-1 0-1-1-2-1v-1l-1-2h1 0l1-1s0 1 1 1c1-1 0-1 1-3z" class="D"></path><path d="M298 444l2 3h0l1 1-1 1c-1 0-2-1-4-1v1l1 1v1c-1 0-1-1-2-1v-1l-1-2h1 0l1-1s0 1 1 1c1-1 0-1 1-3z" class="J"></path><path d="M223 366v2l-3 28c-1-2-1-2-1-3-1-1 0-3 0-4 1-8 2-16 4-23z" class="B"></path><path d="M264 226h1c0 1-1 4-1 5-1 3-3 8-5 11-1 1-2 2-2 3l-6 6h0l6-9c1-1 2-3 3-4 1-3 2-5 2-8l1-1c0-1 1-2 1-3z" class="C"></path><path d="M336 468c1 0 2 2 3 2 1 1 2 2 2 3 2 4 3 8 4 12v3l-1-1c-1-3-2-7-4-10-1-3-2-6-4-9z" class="M"></path><path d="M253 419c1-1 0-2-1-4 0-2-2-4-1-6 3 9 10 13 17 18l-1 1-2-1v-1c-2 0-2-1-3-2-2-1-4-3-5-4v1c-1 0-1 0-2-1 0 1 1 2 1 3h0c-1-1-3-2-3-4z" class="B"></path><path d="M240 454c3 6 5 12 8 18 1 2 3 4 5 7-4-4-7-8-10-13-1-1-2-3-2-4s-1-3-1-5v-3z" class="L"></path><path d="M229 230h3 1c-1 4-3 7-5 10l-1-1c-1-1 0-3-1-4 1-2 1-4 3-5z" class="O"></path><path d="M226 235c1-2 1-4 3-5 0 2 0 2-1 4l-2 1z" class="T"></path><path d="M290 296c0-1-1-3-1-4v-7-1c0 2 1 3 1 4 1 8 4 15 8 22h0c-1 0-2-1-2-2-1-2-2-3-3-4 0 0-1-2-1-3-1-2-2-3-2-5z" class="H"></path><path d="M293 280l3 9c2 4 5 8 8 11-2-1-4-2-6-4-5-5-5-10-5-16z" class="I"></path><path d="M343 472l3 5h0c1-1-2-6-3-7 3 2 5 6 6 10v1c0 4 2 7 1 10-1 0-1-3-1-4-1-3-3-6-4-9l-1-1v-1-1c-1-1-1-2-2-2l1-1z" class="D"></path><path d="M281 339c1 6 3 12 5 19 1 2 3 5 3 8 0-1-1-2-2-2 0-1-1-1-1-1-2-2-5-21-6-24h1z" class="B"></path><path d="M265 498c-2-3-4-5-7-8 3 2 6 3 8 5 1 1 4 3 5 4v1l4 4c1 1 2 2 3 2-4 0-10-5-13-8z" class="J"></path><path d="M280 430c1-1 1-1 2-1 2 5 5 8 8 12l4 6 1 2v1c-7-5-11-13-15-20z" class="N"></path><path d="M312 273l1-1c1 1 3 3 3 4 2 2 4 3 6 4 4 4 9 9 11 13-7-7-15-12-21-20z" class="E"></path><path d="M252 492c1 1 2 1 3 2 0 1 1 2 1 2v3l12 16-1 1-3-3-1-1c-3-4-5-7-7-11-1-3-3-6-4-9z" class="L"></path><path d="M235 231v-2c-1-1-3-1-4-2 1-3 4-5 7-7l-4 6c2-1 4-3 5-4-1 2-2 4-4 6h0c1 0 2-1 3-2h0c0 2-1 2 0 3 2 0 4-2 5-1 0 1 0 2-1 3h-1c-2-1-3-1-4 0v-1h0-1l-1 1z" class="E"></path><path d="M280 480c2 2 4 3 6 5 2 1 4 1 5 3h-1l-2-1v1c1 0 2 1 3 2l-2 1c-1 0-1-1-1-1-3-3-7-4-10-6 1-1 2-2 2-4z" class="D"></path><defs><linearGradient id="AR" x1="268.038" y1="502.19" x2="282.611" y2="509.797" xlink:href="#B"><stop offset="0" stop-color="#0c0c0d"></stop><stop offset="1" stop-color="#2b2b2b"></stop></linearGradient></defs><path fill="url(#AR)" d="M265 498c3 3 9 8 13 8 3 1 6 3 10 5h-1c-2 0-3-1-6-1-1 0-2 0-3-1l-4-2h0c-3-2-5-4-7-5l-2-3v-1z"></path><path d="M318 620c-1 0-2-1-3-1-1-1-3-2-4-3-2 0-5-1-6-2h-1v-1l-2-1 9 3c1 1 2 1 3 1v-1c-2 0-4-1-6-2 4 0 8 2 11 3l2 2h-1l-1 1 6 3c1 0 1 0 1 1h0l-2-1c-1-1-3-2-5-3l-1 1zm-9-263c3 8 4 17 5 25-1-1-1-2-2-3l-3-10c1-2 0-8-1-11 1 0 1 0 1-1zm40 104c0-1 0-2 1-3 4 5 7 11 8 17h-1c-1 0-2-2-3-3-1-4-3-8-5-11z" class="B"></path><path d="M273 355v-36 4l1 7v8c0 3 1 6 1 9s-1 6-1 9c0 0-1 0-1-1z" class="N"></path><path d="M229 432h1v5 1l1 2h0v3c1 1 1 2 1 3 1 3 0 6 0 9l2 12c-2-4-2-8-3-12 0-3 0-7-1-11v-2-2c-1-3-1-6-1-8z" class="J"></path><defs><linearGradient id="AS" x1="300.481" y1="402.061" x2="302.667" y2="393.072" xlink:href="#B"><stop offset="0" stop-color="#060508"></stop><stop offset="1" stop-color="#2b2a29"></stop></linearGradient></defs><path fill="url(#AS)" d="M297 393l1-2 2 1h0v1l2 2v-1c2 3 4 8 5 12-2-1-3-3-4-4-2-2-5-4-5-6l-1-2h0v-1z"></path><path d="M300 244c1-2 1-3 1-5h-1c0-1 0-1 1-2 0 1 0 1 1 2h0v2l1 3c1 4 0 10-1 13h-1v1c-1 1-1 1-1 2v1c-1-2-1-2 0-3v-1h-1l1-1v-1-3c1-2 0-6 0-8z" class="K"></path><path d="M292 257h0v-1-3c0-1 0-2 1-3l1-5v-3l-1-1v-1 3c-1 1-1 3-1 5v3 2l-1 1v3h-1c3-12 3-26-1-37 1 0 1 1 2 2 2 6 3 12 4 18 0 3 0 5-1 8-1 1-1 2-1 4l-1 5z" class="B"></path><path d="M313 315c-1-1-2-3-3-5l4 4-4-7 10 11 2 2-1 1h0c-2 0-3 0-4 1-1-2-2-5-4-7z" class="L"></path><path d="M298 432s1 1 1 2h0v1h1v1c1 1 1 1 1 2v1 1l-1-1v2h0c0 1-1 1-1 1l2 2v1c1 1 3 2 4 4-2-1-3-2-4-3l-1 1h0l-2-3c-1 0-1-1-2-2-3-4-6-6-8-10 1 0 2 2 2 2 1 2 6 7 8 8l-1-1 1-1h1c-2-2-3-4-3-7 1 1 2 4 3 5h1c0-2-2-4-3-6h1z" class="C"></path><defs><linearGradient id="AT" x1="343.96" y1="497.094" x2="334.248" y2="491.843" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#2e2e30"></stop></linearGradient></defs><path fill="url(#AT)" d="M337 484c1 1 1 2 2 3 2 6 3 13 2 19-1-3-1-6-2-9-1-2-3-6-3-7 1-2 1-5 1-6z"></path><path d="M255 232c0-2 1-4 2-6 0-1-1-2 0-4 1 5 2 11 0 15l-4 5-1 1h-1v-1c1-1 1-3 2-5l2-5z" class="I"></path><path d="M255 232l1 1c0 1-1 2-1 4h-2l2-5z" class="M"></path><path d="M297 340c-3-6-5-13-7-19l-2-10s0-1 1-1v1c0 3 1 5 2 8 1 4 2 8 4 11 1 3 2 5 4 7l3 6h-1c-1 0-2-1-3-1l-1-2zm0-17v-1c3 5 7 8 9 13 3 4 5 9 7 14h-2c-2-10-9-18-14-26h0z" class="E"></path><path d="M226 378c1-2 0-5 1-6 0 1 0 3 1 4h0c0-2 0-3 1-4 0 4-2 9-1 13h1v1 2c-1 2-1 3-2 4-2 2-3 6-3 9-1 0-1-1-1-2v-2 1c0-2 1-6 2-8 1-1 1-2 1-3 1-2 1-4 1-6l-1-3z" class="I"></path><path d="M288 420c4 4 7 8 9 12 1 2 3 4 3 6h-1c-1-1-2-4-3-5 0 3 1 5 3 7h-1c-3-3-4-7-6-10-1-3-3-6-4-9v-1z" class="F"></path><path d="M287 293c0-1-1-1-1-2-1-3 0-9 2-12v-1h0c0-1 1-2 2-2v2h0v10c0-1-1-2-1-4v1 7c0 1 1 3 1 4h-1c-1-1-1-2-2-3z" class="D"></path><path d="M280 512c-2-1-5-3-6-4v-1l4 2c1 1 2 1 3 1 3 0 4 1 6 1h1c1-1 2-1 3-1v-1l7 4-7-2c1 1 6 3 6 3-2 1-4-1-6 0h0-1c-2-1-3-1-4-1-2 0-5-2-6-1zm43-212c4 3 8 6 11 10l-1 1-2-2c0 1 0 1 1 2 0 1-2 4-2 5l-2-4-2-6-3-6h0z" class="I"></path><path d="M326 306v-1c2 1 3 2 4 4 0 1-1 2-2 3l-2-6z" class="M"></path><defs><linearGradient id="AU" x1="282.245" y1="503.582" x2="281.125" y2="508.166" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#AU)" d="M271 499h0c3 2 6 4 9 5 2 1 5 3 8 4l3 1v1c-1 0-2 0-3 1-4-2-7-4-10-5-1 0-2-1-3-2l-4-4v-1z"></path><path d="M323 261l-2-2c-2-1-4-3-5-5h0c-1-1-2-2-2-3 0-2 0-3 1-4 2-4 1-9 1-13 0-2 1-4 1-7h0l-1-1v-3-1c-1-1-1-1-1-2v-1h1c0 1 1 2 1 3h0c1 1 0 2 1 3 0 4-1 7-1 10v8c-1 2-2 5-1 7 0 6 4 7 8 10l-1 1z" class="G"></path><path d="M286 497l-2-2c-3-2-5-4-7-7 2 1 3 2 5 2l7 3s1 0 1 1c2 1 3 2 5 3l-1 1c0-1-1-1-2-1v-1h-2 0l-1 1h-3z" class="C"></path><path d="M336 468c-3-2-5-6-8-8 2 1 4 1 6 2 1 1 2 1 2 1 1 0 3 2 3 2l1 1 1 2 1 1s0 1 1 2v1l-1 1v-1l-1 1c0-1-1-2-2-3-1 0-2-2-3-2z" class="G"></path><defs><linearGradient id="AV" x1="234.357" y1="369.948" x2="225.401" y2="372.863" xlink:href="#B"><stop offset="0" stop-color="#454347"></stop><stop offset="1" stop-color="#636260"></stop></linearGradient></defs><path fill="url(#AV)" d="M232 356h1c0 4-2 8-2 13v6c-1 3-2 7-2 10h-1c-1-4 1-9 1-13 0-6 1-11 3-16z"></path><path d="M297 362s-1-2-2-3l-6-10c-1-2-3-4-4-7-2-3-3-6-4-10v-4h1c0 4 1 9 3 13 3 5 7 9 10 13v1c1 2 1 3 2 4 0 1 2 3 2 3l-1 1-1-1z" class="M"></path><path d="M251 223h0c1 2 2 4 2 6 0 5-3 11-6 15 0-1 0-1 1-2 1-2 1-5 2-7-1 1-1 2-2 3-1 2-3 6-5 7 0-1 1-3 2-4 1-3 3-5 4-7 1-4 2-8 2-11z" class="F"></path><path d="M275 388l1-4v4c1 4 1 9 4 11 1 8 4 15 8 21v1c-1-2-2-4-3-5h-1c-1-1-2-3-2-5-4-7-6-15-7-23z" class="P"></path><path d="M334 478l1-1c0-1-1-3-1-5s-2-5-3-7c3 3 5 6 7 10l3 6 1 3h-1l-4-8h0c0 1 0 2 1 3 1 3 1 5 1 8-1-1-1-2-2-3l-3-6z" class="J"></path><path d="M277 591l8 5c9 4 18 8 28 10 0 0 0 1 1 1-8-1-16-4-23-7-5-3-11-5-16-8l2-1z" class="H"></path><path d="M273 355c0 1 1 1 1 1v6c0 4 0 8 1 11v3c1 1 1 2 2 2v5c-1 1 0 4-1 5v-4l-1 4c0-3-1-6-1-10-1-7-2-13-1-20v-3z" class="F"></path><path d="M273 355c0 1 1 1 1 1v6 3 1h0c-1-3-1-6-1-8v-3z" class="E"></path><path d="M275 373v3c1 1 1 2 2 2v5c-1 1 0 4-1 5v-4s-1-9-1-11z" class="B"></path><path d="M309 239l-3-8v-1c-1-2-2-3-3-5l-5-5 1-1 1 1h0c-2-2-3-4-4-6h1c1 0 2 1 3 2h1c0 1 1 1 1 1l1 1s0 1 1 1v1c1 2 0 3 0 5 1 1 2 2 2 4l3 6c0 1 0 2 1 3l-1 1z" class="C"></path><path d="M295 453l1 1c6 3 11 5 17 7-4 0-9 1-13 0l-1-1c-1 0-2 0-3-1v-1c0-2 0-2-1-4v-1z" class="D"></path><path d="M295 453l1 1c1 2 2 4 4 5l-1 1c-1 0-2 0-3-1v-1c0-2 0-2-1-4v-1z" class="B"></path><path d="M296 614l1-1s1 0 1 1c1 0 2 0 3-1l18 9c-1 1-3 1-5 2-6-3-12-7-18-10z" class="P"></path><path d="M266 431l18 19h2l5 4v3l4 3h0c-3-1-6-3-9-6-2-3-5-6-8-9-3-4-7-7-11-11 0-1 0-2-1-3z" class="I"></path><path d="M284 450h2l5 4v3c-3-2-5-5-7-7z" class="B"></path><path d="M231 414c0 9 1 18 3 28 0 2 1 5 2 7h-1c0-3-2-5-2-8-1-1-1-3-1-5 0-1 0-1-1-2v1h0s0 2-1 3v-1-5h-1v-5c-1-3-1-8 0-10h0v2h1v2c1-1 0-2 0-2 0-2 0-3 1-5z" class="G"></path><defs><linearGradient id="AW" x1="279.237" y1="361.727" x2="270.843" y2="352.386" xlink:href="#B"><stop offset="0" stop-color="#3a3638"></stop><stop offset="1" stop-color="#4f5251"></stop></linearGradient></defs><path fill="url(#AW)" d="M274 338c2 3 1 6 2 9v22c0 3 0 6 1 9-1 0-1-1-2-2v-3c-1-3-1-7-1-11v-6c0-3 1-6 1-9s-1-6-1-9z"></path><path d="M261 523c1-3-5-6-5-8h1c2 2 3 3 4 5 3 4 7 9 11 12 1 2 4 4 5 6-1 0-2-2-3-2-4-3-8-4-11-7l-1-1c1-1 0-2-1-2l1-2-1-1z" class="D"></path><path d="M228 342c0 1 0 1-1 2-2 3-4 8-5 12v1 1h1v-1c0 2-1 7-2 9h0c-1 1-2 6-2 7h-1l1-8c1-1 1 0 0-1l-1 1h0c-2 1-2 3-4 5v-1c0-2 2-3 2-4 2-3 4-6 4-9 2-4 5-9 8-14z" class="J"></path><defs><linearGradient id="AX" x1="218.763" y1="387.947" x2="228.688" y2="383.827" xlink:href="#B"><stop offset="0" stop-color="#454646"></stop><stop offset="1" stop-color="#5e5c5d"></stop></linearGradient></defs><path fill="url(#AX)" d="M224 373v-1h0c1 1 1 2 1 4 0 0 1 1 1 2h0l1 3c0 2 0 4-1 6 0 1 0 2-1 3-1 2-2 6-2 8v-1c-1-8 0-16 1-24z"></path><path d="M226 387h-1 0c0-2 0-4 1-6h1c0 2 0 4-1 6z" class="L"></path><path d="M324 350c-4-8-8-17-13-24-3-6-8-11-12-18 3 3 6 6 8 9 1 2 2 3 3 4h0c4 8 8 16 14 23 1 2 1 3 1 6h-1z" class="N"></path><path d="M267 442h1c1 0 1 0 3-1 5 5 8 11 13 15 1 2 3 3 4 4 3 1 8 4 9 7-2-2-4-4-6-4h0c-1 0-2 0-3-1 0-1-1-2-2-2-3-3-5-6-8-8h-2s0-1-1-1v1h0 0l-2-3h0c0-1-1-2-2-3 0-2-1-2-2-3v2c-1-1-1-2-2-3z" class="I"></path><path d="M273 449c2 0 3 0 4 1l1 2h-2s0-1-1-1v1h0 0l-2-3zm37-192l1 2 12 8 3 3h0l-2-1c0 1 0 1-1 1s-1 0-2-1h0-2v1c-4-3-7-6-10-10 1-1 0-2 1-3z" class="B"></path><path d="M311 259l12 8-1 1v-1l-2 1h0l-4-2c-1-2-4-5-5-7z" class="F"></path><path d="M211 424l1 2h0v3c-1 4-1 8-2 12-1 3-2 7-4 11l1-9c0-2 1-4 1-6h-1v1h0l-2-1v-2c1-1 1-2 2-3v-1h1l1 1h0v-1h1c1-2 1-4 1-7z" class="C"></path><path d="M207 431h1l1 1h0c0 2-2 3-2 5v1h0l-2-1v-2c1-1 1-2 2-3v-1z" class="B"></path><path d="M246 210h0c-3-2 0-6 0-8h-1c0-3 2-7 4-10 0 3-2 7-3 10 1-1 2-3 4-5v7l2-4c0-1 1-1 2-2-1 2-2 5-3 7l-1 1v1c-1 1-1 3-2 3h-2 0z" class="S"></path><path d="M246 210c0-1 0-2 1-3 0-2 0-2 1-3l2 1v1 1c-1 1-1 3-2 3h-2z" class="V"></path><path d="M262 528c-8-7-14-15-18-25h0c2 2 3 4 4 6 2 2 3 3 4 5l9 9 1 1-1 2c1 0 2 1 1 2z" class="F"></path><path d="M202 350c1-1 3-2 4-3s2-3 4-4l7-5v-1h1c1-1 1-1 2-1 0-1 0-1 1-1v-1l1 1-3 3h-1l-1 1c-1 0-2 2-2 3 2 0 3-2 5-3l1-1c1-1 2-2 3-2h0l-8 8h0-2c-2 1-3 3-5 5h0-2-1c-1 1-1 1-2 1h-2z" class="J"></path><path d="M285 328l1-1c2 11 9 18 15 26 3 5 6 10 8 16l3 10h-1l-1-1c0-1 0-2-1-3-2-5-3-11-6-17s-7-9-11-14-6-10-7-16z" class="M"></path><path d="M296 241v-8-1h0c2 2 1 3 1 5l1 1h0 0v1 9 1c0 1-1 3 0 4l-1 1c0 1 0 1-1 2 0-1 0-1-1-2v3 1l-1-2s0 1-1 2v-2l1-1v-4l-1 1c0-2 0-3 1-4 1-3 1-5 1-8 0 1 0 3 1 4v-3z" class="D"></path><path d="M296 241v-3c1 0 1 2 1 2v10c0 2 0 3-1 4v-3c-1-2 0-5 0-7v-3z" class="E"></path><defs><linearGradient id="AY" x1="310.464" y1="278.487" x2="304.332" y2="286.279" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#AY)" d="M314 295c-6-6-11-14-12-22-1-1-1-7-1-8 2 1 1 3 1 4s1 2 1 3c3 7 6 13 10 18 0 2 1 3 2 5h-1z"></path><path d="M264 384c1 1 1 3 2 4 0-1 0-2 1-3 0 2 0 5 2 6l1 1c0 2 1 5 1 7 0 1 1 2 1 3l-1 2 1 2v2-1c-1-1-1-1-1-2v3c1 1 0 2 0 3s1 2 1 2h-1v1s1 1 1 2l-6-8c-1-1-1-1-1-2 1 1 2 1 2 2 1 1 1 0 2 0 0 1 1 1 1 2l1-1c-1-4-2-9-3-13s-4-8-5-11v-1h1z" class="D"></path><defs><linearGradient id="AZ" x1="292.744" y1="570.39" x2="316.713" y2="568.15" xlink:href="#B"><stop offset="0" stop-color="#333234"></stop><stop offset="1" stop-color="#616261"></stop></linearGradient></defs><path fill="url(#AZ)" d="M275 560c2-1 7 3 10 4 5 2 10 4 16 5 5 1 10 2 15 1h8-1l-6 2c-10 2-19-1-27-5-5-2-10-4-15-7z"></path><defs><linearGradient id="Aa" x1="298.584" y1="481.043" x2="286.088" y2="476.46" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#242324"></stop></linearGradient></defs><path fill="url(#Aa)" d="M286 473c3 1 6 3 8 5s4 3 6 5c-2 0-4-1-6-1-4-1-10-3-14-6h0c1-1 2-1 3-1h0c1-1 2-1 3-2z"></path><path d="M319 243v-1l2 3c1 1 1 2 2 2 0-1 0-2-1-3 0-1 0-1 1-2l1 3c1 0 1 1 2 1l2 2 11 9 3 3-8-5c-3 0-5-2-8-2h-1c0-1-1-2-1-2-1-1-3-2-4-3v-1h0c0-1 0-1-1-2v-2z" class="D"></path><path d="M230 413l1-4h0c2 15 2 31 9 45v3c0 2 1 4 1 5-3-3-4-9-5-13-1-2-2-5-2-7-2-10-3-19-3-28l-1-1z" class="Q"></path><path d="M200 417c1 1 2 2 3 2l-2 6c2-2 2-5 3-7l1 1s0 1-1 2c0 4 0 7-2 11-1-1-1-1-1-2l-2 1h0v-2h0c-1 0-1 0-1 1-1 1-2 3-2 4-1-1-1-2-2-3 3-4 5-9 6-14z" class="E"></path><path d="M318 620l1-1c2 1 4 2 5 3l2 1h0c0-1 0-1-1-1l-6-3 1-1h1l16 8h0c-2 2-7 2-10 1l-6-1c1 0 2 0 4-1h0l-1-1h0c-2-2-4-3-6-4z" class="M"></path><path d="M241 263c4-1 7-4 11-7v1c-2 1-4 4-5 6 2-1 4-1 6-2 6-4 11-8 16-14l1 1c-4 5-10 9-16 13l-6 3c-2 2-4 2-6 3-3 1-6 3-9 4v-1l4-2-1-1 1-1c1-1 3-2 3-4 1 0 1 1 1 1z" class="C"></path><path d="M240 262c1 0 1 1 1 1 0 1-1 1-1 1 0 2-1 3-3 4h0l-1-1 1-1c1-1 3-2 3-4z" class="G"></path><defs><linearGradient id="Ab" x1="271.331" y1="387.699" x2="258.099" y2="353.963" xlink:href="#B"><stop offset="0" stop-color="#2c2e2e"></stop><stop offset="1" stop-color="#646364"></stop></linearGradient></defs><path fill="url(#Ab)" d="M261 357c0-2 0-3 1-5l8 40-1-1c-2-1-2-4-2-6l-6-28z"></path><path d="M255 494v-1c4 5 7 9 12 13l2 2c5 5 13 9 19 11 1 0 2 1 3 1v2c-9-3-17-8-24-13l-7-7c-2-2-3-4-4-6 0 0-1-1-1-2z" class="Q"></path><path d="M307 578c2 0 4-1 6-1l1-1c-3 0-6 0-9-1h-2 0l1-1c1 0 1 0 2 1 2 0 7 0 8-1h0 2 2l1-1h2c1-1 2-1 3-2 0 0 1 0 1-1l1 1h0 0c-1 0-1 1-1 1v1l1-1 1 1c-1 2-3 4-5 5l-1 1-1-1h-1c-2 0-5 1-7 0h-5zm30 17c1 1 1 2 2 2 3 0 7-1 10-1 2-1 4-1 6-1 1 0 2 0 3-1v1l-1 1c-3 0-4 1-7 2-4 1-8 1-12 1-4 1-8 2-12 2l-1-1h0-3-1c1-1 2-1 2-1 2-1 4-1 6-1h1l7-3z" class="D"></path><path d="M293 304c1 1 2 2 3 4 0 1 1 2 2 2 0 2 4 7 4 8 4 4 7 10 9 16 1 1 1 2 1 3l-3-3c-2-4-5-7-7-11-3-3-4-7-6-11-1-3-2-5-3-8z" class="C"></path><path d="M309 334l1-1c-1-3-3-4-4-6s-2-3-3-4-2-3-2-4-1-2-1-3c0 1 1 1 1 1v1h1c4 4 7 10 9 16 1 1 1 2 1 3l-3-3z" class="G"></path><path d="M311 242c0-3-1-5-1-8-1-1-1-1-1-2s0-1-1-2v-1-1c1 1 1 2 2 3 0 1 0 2 1 2v-2-1l-1-2v-2c0-1-1-2 0-3h0c1 1 1 2 1 3h1v-3h1c0 1 0 2 1 3v-4c-1-1-1-1 0-2h0c1 2 1 5 2 7h0c0 3-1 6-1 9l-2 2v1c-1 1-1 2-2 2v1z" class="C"></path><path d="M268 427c4 3 8 6 11 9l6 7 1 1h0c1 1 2 1 3 1l6 8v1c1 2 1 2 1 4v1h-1v1l-4-3v-3h1c-4-4-6-8-9-12-4-4-8-8-13-12l-3-2 1-1z" class="E"></path><path d="M292 454l3 5v1l-4-3v-3h1z" class="C"></path><path d="M286 444h0c1 1 2 1 3 1l6 8v1c-1 0-2-1-3-2-2-2-4-5-6-8z" class="H"></path><path d="M230 541c4 7 9 14 14 21 9 12 20 22 33 29l-2 1c-13-8-24-19-34-31-4-5-8-10-10-15l1-1c-1-1-2-2-2-4z" class="E"></path><path d="M266 452l2 2v1c1 1 1 1 1 2 1 0 1-1 1 0v-1c1 1 1 1 1 2 1 2 2 3 3 5h-3-1c0 1 1 2 1 4 0 1 2 3 3 3l-1 2c-2-4-5-7-7-10 0-1-1-2-2-3h0c0-1-1-1-1-2-1 0-1-1-1-1h0c0-1 0-2 1-3 1 0 1 0 1-1h1 1z" class="K"></path><path d="M270 463h0c-1-1-2-4-2-6h1l2 1c1 2 2 3 3 5h-3-1z" class="C"></path><path d="M231 435v-1c1 1 1 1 1 2 0 2 0 4 1 5 0 3 2 5 2 8 1 4 2 8 4 12 2 6 4 12 7 17 3 6 6 10 9 15v1c-1-1-2-1-3-2 0 0-2-4-3-4-2-4-4-7-6-11-5-13-10-28-12-42z" class="S"></path><path d="M247 305h0c0-1 1-1 1-2l3-2 1 1-3 6-2 2c0 1-1 2-1 4l-3 3-1 1c0 1 0 1-1 2v1h-1l-5 5h-1l3-3v-1h0l-1 1v-1l-6 6-3 3-1-1 10-9c1-2 3-3 4-5s4-3 4-5h1c0-1 1-1 1-1v-1c0-1 1-3 1-3v-1z" class="G"></path><path d="M270 463h1 3c3 3 7 7 12 10-1 1-2 1-3 2h0c-1 0-2 0-3 1l-6-6c-1 0-3-2-3-3 0-2-1-3-1-4z" class="N"></path><path d="M274 467v-1c2 1 4 3 5 4s1 2 1 3c-1 0-2 0-2-1-2-1-4-3-4-5z" class="Q"></path><path d="M336 336c2 7-1 12-1 19l-1-1c0-1-1-2-2-3h-1c-1 4-1 7-1 10v4h1v1c-1-1-1 0-1-1 0-3-3-7-4-9h0c2-2 3-6 3-9h0c1 1 1 1 2 1 1-1 2-1 2-2l2-6c1-1 1-3 1-4z" class="G"></path><path d="M191 390c2-3 4-6 6-8l13-16h0c-2 4-5 8-8 12 4-3 5-7 9-9-1 3-3 5-4 7h0c-5 7-10 15-16 20 2-5 7-9 9-14l1-3c-1 1-2 2-3 4l-4 4-3 3h0z" class="M"></path><path d="M269 508h1l3 2h0l3 2h1c0 1 1 1 1 1 1 1 1 1 2 1 0 1 0 1 1 1l1 1c1 0 2 1 3 1s2 1 3 1c-1 0-1-1-1-1l1-1h-1-1-1c-1-1-1-1-2-1 0 0-1-1-2-1l-1-1-1-1c-1 0-3-2-4-3h-1l-3-2v-1c-2-1-3-2-4-4h0c2 1 4 3 7 5h0v1c1 1 4 3 6 4 1-1 4 1 6 1 1 0 2 0 4 1h1l2 1 4 1-2 1 2 2h-1c-1 0-7-1-8-1v1c-6-2-14-6-19-11z" class="B"></path><path d="M280 512c1-1 4 1 6 1 1 0 2 0 4 1h1l2 1s-1 1-1 2c-5-1-9-3-12-5zm10-74c-5-8-9-17-13-26-1-1-2-4-2-6l3 6c1 3 3 6 4 9 5 8 10 13 15 20l1 1c-2-1-7-6-8-8 0 0-1-2-2-2 2 4 5 6 8 10 1 1 1 2 2 2-1 2 0 2-1 3-1 0-1-1-1-1l-1 1h0-1l-4-6h0v-3z" class="L"></path><path d="M290 438l2 2c1 2 1 2 3 3l1-1c1 1 1 2 2 2-1 2 0 2-1 3-1 0-1-1-1-1l-1 1h0-1l-4-6h0v-3z" class="B"></path><path d="M292 440c1 2 1 2 3 3l1-1c1 1 1 2 2 2-1 2 0 2-1 3-1 0-1-1-1-1-2-2-4-3-4-6z" class="I"></path><path d="M268 515h0c3 4 5 8 7 12h0c-3-3-5-5-9-7 1 1 3 4 3 5l-4-3c-1-1-3-4-5-5-1 0-2-2-3-3l-2-1c-1 0-1-1-2-2v-1c2 0 3 2 5 3h0c-1-2-2-3-3-4v-1l4 4h4l1 1 3 3 1-1z" class="C"></path><path d="M263 512l1 1v3c-1 1-3-3-4-3l-1-1h4z" class="G"></path><path d="M211 402h1c-1 4-2 8-2 11 0 1 0 3-1 4l-1 11-1 3v1c-1 1-1 2-2 3v2l-1 2h0v-1h-1c-1 0-2-1-3-2 1-1 2-3 2-4 2-4 2-7 2-11l1 2v-1c0-2 2-5 2-7v6l4-19z" class="I"></path><path d="M202 432c2-4 2-7 2-11l1 2v-1l-2 9c1 1 2 0 2 1h2c-1 1-1 2-2 3v2l-1 2h0v-1h-1c-1 0-2-1-3-2 1-1 2-3 2-4z" class="H"></path><path d="M203 431c1 1 2 0 2 1h2c-1 1-1 2-2 3l-2 1v-1c-1-1 0-3 0-4z" class="M"></path><defs><linearGradient id="Ac" x1="325.341" y1="355.894" x2="310.127" y2="343.65" xlink:href="#B"><stop offset="0" stop-color="#171917"></stop><stop offset="1" stop-color="#4a494c"></stop></linearGradient></defs><path fill="url(#Ac)" d="M298 310h1c2 4 5 8 7 12 9 14 16 31 18 48v1c-1-1-1-1-1-2-1-2-2-5-2-8-3-8-5-16-9-24 0-1 0-2-1-3-2-6-5-12-9-16 0-1-4-6-4-8h0z"></path><defs><linearGradient id="Ad" x1="253.727" y1="212.402" x2="249.288" y2="211.077" xlink:href="#B"><stop offset="0" stop-color="#726f72"></stop><stop offset="1" stop-color="#888786"></stop></linearGradient></defs><path fill="url(#Ad)" d="M251 205h1c1-1 1-3 3-4h0c0 2 0 4-1 6v1c2-2 3-4 4-6h0c0 2-2 6-1 7 1 0 1 0 2 1-3 4-7 7-12 9 0-3 1-5 1-7l-2-2h0 2c1 0 1-2 2-3v-1l1-1z"></path><path d="M246 210h2c1 0 1-2 2-3 1 2 0 4-1 6h-1v-1l-2-2h0z" class="Q"></path><path d="M288 369l4 7c0 1 1 3 2 4l1 2c1 1 1 3 1 4 1 1 0 3 1 4v3 1 2c1 0 1 1 1 1-1 2 0 5 0 7l-3-3v-1c-1-1 0-1 0-2l-1-1 1-1h-1c0-2-1-3-1-4h0c-1-1 0-1 0-2-1-1 0-2-1-3s0-1 0-2l-1-1v-3h0c-1-4-3-8-3-12z" class="D"></path><path d="M293 386c1 0 1 0 1 1 1 1 1 3 2 4v4s1 2 0 2c-2-4-3-7-3-11z" class="F"></path><path d="M294 380l1 2c1 1 1 3 1 4 1 1 0 3 1 4l-1 1c-1-1-1-3-2-4 0-1 0-1-1-1h0l-1-3v-1-1l2 1v-2z" class="B"></path><path d="M292 383h1c1 0 1 1 2 2 0 0 0 1-1 2 0-1 0-1-1-1h0l-1-3z" class="H"></path><path d="M242 231c1 0 1 0 2-1h1c-3 6-5 12-10 17-2 3-5 5-7 8-1 2-2 3-3 5v1l-1-1c2-3 3-6 5-9 2-5 4-9 6-14l1 1c2-1 2-2 3-4l2-3h1z" class="D"></path><path d="M321 579c-1 0-2 1-3 1h-1c0 1 0 1-1 1h-3c-1 1-4 0-6 0-1 1-3 1-4 1-1-1-3 0-4-1h-1-1 0c-1 0-1 0-2-1h-1l-2-1-3-1-2-1-1-1-2-1-1-1-1-1c-1 0-1-1-2-1l-2-2c-1 0-1-1-2-1l-1-1s-1-1-1-2h0l5 4 3 1 2 2s0 1 1 1h1c2 1 4 3 6 3l1 1h1 1c1 1 2 1 3 0s7 0 9 0h5c2 1 5 0 7 0h1l1 1z" class="J"></path><path d="M242 179c0-2 1-3 2-5 1 5 1 10 0 15l2 2c1 2-1 6-2 9h-1c0-1-1-3-2-4l-1-1c0 2 0 3-1 4v-10-5c1-1 2-3 3-5z" class="F"></path><path d="M242 179c1 0 1 0 2 1v3c-1 1-1 3-1 4l-1 1h-1-1l-1 1v-5c1-1 2-3 3-5z" class="S"></path><path d="M207 376c0 1 0 1-1 2v1c-1 1-1 1-1 2h-1c0 1-1 2-1 2-1 1-1 2-2 2-1 1 0 1-1 1l-2 4c-2 2-4 4-6 7h-1c-1 1-2 2-2 4-1 0-1 1-2 1h0v-1c1 0 1-1 2-2h-2v-1 1 1c-2 2-2 3-3 5h-1l-4 8v-1l8-17c1-1 1-2 2-2 0-1 1-2 1-3h1 0l3-3 4-4c1-2 2-3 3-4l-1 3c-2 5-7 9-9 14 6-5 11-13 16-20z" class="G"></path><path d="M343 592l1 2c2 0 5 1 7 0 1 0 3-1 4-2 1 0 2 0 4-1 3 2 6 4 9 5h1l1 1v2l-10 4v-1c-2 1-5 2-7 3v-1c3-1 10-4 12-7v-1s-1 0-1-1h-1 0c0-1-1-1-2-1-2 1-4 3-6 5v-1l2-2 1-1v-1c-1 1-2 1-3 1-2 0-4 0-6 1-3 0-7 1-10 1-1 0-1-1-2-2l6-3z" class="M"></path><path d="M360 602c2-1 3-2 5-3 1-1 2-2 4-3l1 1v2l-10 4v-1z"></path><path d="M298 432l-3-8-2-8v-1l-1-1-3-7-1-2-2-4c-1-2-1-3-2-4v-1-1-1l-1-1v-2c-1-1-1-2-1-3h0v-1h1v2c1 1 1 1 1 2l1 3c0 1 0 1 1 2v1 1l1 1 1 2c0 1 0 2 1 2l3 7v1l1 1v-1-4-3c-1 0-1 0-1-1v-3h1c0 1 0 1 1 2v2c0 2 0 4 1 6 2 9 3 17 6 26h0v-1c-1-1-1-1-2-1h0c0-1-1-2-1-2zm-12-69s1 0 1 1c1 0 2 1 2 2l9 15c0 1 1 2 2 3 1 3 3 6 4 8 0 1 1 2 0 4l-2-4v2 1l-2-2v-1h0l-2-1-1 2v-3c-1-1 0-3-1-4 0-1 0-3-1-4l-1-2c-1-1-2-3-2-4l-4-7-2-6z" class="H"></path><path d="M295 354v-1h0l-2-3h0c-1-1-1-2-2-2-1-1 0 0 0-1-1-1-2-1-2-2l-1-1s0-1-1-1v-1h1 0l2 2 5 7 2 3 1 1v1c1 1 2 1 2 2h3c3 6 4 12 6 17v1c0 1-1 2-1 2l-1-1c-2-4-3-8-6-11l-2-4s-2-2-2-3c-1-1-1-2-2-4v-1z" class="G"></path><path d="M295 355c4 3 5 6 7 9 2 2 3 4 5 7 0 1 1 3 2 5 0 1-1 2-1 2l-1-1c-2-4-3-8-6-11l-2-4s-2-2-2-3c-1-1-1-2-2-4z" class="I"></path><path d="M223 473h0c1 5 3 9 4 13 4 12 10 23 16 34l8 11c-16-15-26-36-28-58z" class="F"></path><path d="M301 353c0-1 0-2-1-3h0c-1-1 0-1-1-2l-1-2-3-4-1-3v-1c1 1 1 1 1 2h1v1l1-1 1 2c1 0 2 1 3 1h1c3 5 5 9 7 14 0 1 0 1-1 1 1 3 2 9 1 11-2-6-5-11-8-16z" class="K"></path><path d="M298 342c1 0 2 1 3 1h1c3 5 5 9 7 14 0 1 0 1-1 1-1-2-1-5-3-8s-5-5-7-8zm-6 58l-1-3c0-1 0-2-1-4l-1-6-1-2-2-5c-1-1-1-2-2-3v-2l-1-1c-2-5-3-11-5-16-2-10-1-21 0-31v-12h1v10c1 5 1 9 2 14h-1l-1-6c-1 6-1 11 0 17 0 3 0 6 1 9 0 1 0 2 1 2l1 7c1 1 1 1 1 2l1 1 1 4 1 2c0 1 1 1 1 2l1 4h1c0 1 0 2 1 3 0 1 0 2 1 3h0v2l1 2v1l1 2v1 3h-1z" class="F"></path><path d="M208 468v-3c1-2 1-5 1-6h1v5c-1 5-2 9-4 14-4 11-10 24-3 36v1c1-1 1-3 1-4l1 8 1 2 1-1v-1c1 5 2 10 4 14 0 1 0 2 1 3-7-10-12-20-14-33 1-5 1-9 2-14 2-4 3-7 4-11l4-10z" class="L"></path><path d="M257 421v-1c1 1 3 3 5 4 1 1 1 2 3 2v1l2 1 3 2c5 4 9 8 13 12 3 4 5 8 9 12h-1l-5-4h-2l-18-19-2-2c-3-3-5-5-7-8z"></path><path d="M308 273c3 2 5 4 7 6 4 5 9 9 13 14 1 1 3 3 4 5h-1c-1-1-2-3-4-4 0 2 0 3 2 4 0 2 2 4 2 6l-2-2c-3-6-9-10-14-15-2-2-4-3-5-6-1-2-1-5-2-8zm-60 111l1 3c0 1 0 2 1 3l1-1c2 9 7 18 12 25 2 2 4 3 5 5 2 1 3 4 4 5v1l-1-1-3-3c-1-1-1 0-2 0 0-1 0-1-1-1l-2-2h0c-1-1-1-1-1-2-1-1-1 0-1-1h0c-1-1-2-2-2-3l-1-1-1-2c-1 0-1-1-1-1v-1h-1v-1-1l-1-1v1l1 1v1c0 1 1 2 1 3h1c-1 1-1 1-1 2-2-2-3-5-4-7 0-1-1-3-2-5-1 0-2-6-2-7-1-2 0-4-1-6h0 0c1-1 0-2 1-3v1h0v-1z" class="C"></path><path d="M250 390l1-1c2 9 7 18 12 25 2 2 4 3 5 5v1h-1c-3-3-6-6-8-9-5-7-7-14-9-21z" class="N"></path><path d="M196 403c2-3 5-7 8-9 1 0 2-2 4-3s4-4 5-6l1 1v1h-1c-3 4-5 9-7 13-2 3-4 5-6 8v1l-3 4h0c-1 1-1 2-2 3-2 2-3 5-6 8h0v-1l1-2 3-3c0-1 0-1 1-2 0-1 1-2 2-3v-2l1-1s1-1 1-2c2-2 3-4 4-6h1v-1l1-1v-1c-1 2-3 3-3 4l-1 1c-1 1-2 2-2 3h-1v1c-1 0-1 0-1 1l-2 2c-1 2-1 3-3 4l-3 3v-1c0-2 1-3 2-5v-1c3-2 5-5 6-7v-1z" class="G"></path><path d="M190 412c5-4 8-10 13-13-4 5-9 10-12 16l-3 3v-1c0-2 1-3 2-5z" class="F"></path><defs><linearGradient id="Ae" x1="248.219" y1="387.955" x2="244.66" y2="388.767" xlink:href="#B"><stop offset="0" stop-color="#111012"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#Ae)" d="M244 391c0-1 0-3-1-4v-2c1 2 1 3 1 5h1c0-1 0-3-1-3v-4-1-1l-1 2v-1h0v-3-1-15c1 2 1 4 2 7l3 14v1h0v-1c-1 1 0 2-1 3h0 0c1 2 0 4 1 6 0 1 1 7 2 7 0 3 0 6 1 9-1 2 1 4 1 6 1 2 2 3 1 4-1-1-2-3-3-5h0c-1-2-2-5-2-7l-4-16z"></path><path d="M259 292v-9l-1-3c1 2 2 3 3 5 1 1 4 4 4 6s1 4 0 5c0 3-1 4-1 6 0 1 0 2 1 2l-1 1c0 1-1 2-2 2 0 1-1 2-1 3-1 1-2 1-2 2l-1 1-1 1s0 1-1 1v1-4c1-3 1-7 1-11v-1-3h0 1c1-1 1-2 2-2v-5h1v-1l-1-1c-1 1 0 3-1 4z" class="G"></path><path d="M264 302h-1c0-3-1-8 1-11h1c0 2 1 4 0 5 0 3-1 4-1 6zm-7-5h0 1c1-1 1-2 2-2v-5h1l1 4c1 2 0 5 0 7l-2 4c0 1-1 1-1 2l-1-2c0-1 0-2-1-4v-1-3z" class="B"></path><path d="M257 300l1 1c1-1 2-2 3-4v3c-1 1-2 2-2 4l1 1c0 1-1 1-1 2l-1-2c0-1 0-2-1-4v-1z" class="C"></path><path d="M243 448h1c0 1 1 1 1 2v1c1 0 1 1 1 1l2 2c0 2 1 3 2 5l3 5 1 2 1 1c0 1 1 1 1 2 1 0 2 1 2 2 0 0 1 1 2 1 1 3 3 4 5 7 1 1 2 3 3 5 2 2 3 3 4 5-4-3-8-5-11-9 0-1-1-1-2-2 0-1-1-2-2-2l-2-3c0-1 0-1-1-2 0 0 0-1-1-1v-1c-1-1-2-2-2-3h0l-1-2-2-2c-1-1-1-3-2-4s-1-3-2-4v1 1l1 3c0 1 0 2 1 3 0 0 0 1 1 2v1l1 1 1 2h-1s-1-1-1-2h-1v-2l-1-1h0v-2c-1-1-1-2-2-3h0v-1-2-1c0-1-1-3-1-4v-1c1 1 1 1 1 2l1 2h1c0-1 0-2-1-3 0-1-1-1-1-2z" class="G"></path><defs><linearGradient id="Af" x1="358.534" y1="613.782" x2="367.82" y2="596.241" xlink:href="#B"><stop offset="0" stop-color="#0b0c0b"></stop><stop offset="1" stop-color="#3f3d40"></stop></linearGradient></defs><path fill="url(#Af)" d="M370 599c1 0 2 0 2 1 0 0-2 1-3 1l1 1 2-1h5v1c-2 2-4 4-6 5-6 2-14 3-21 3h-7 0c1 0 3 0 5-1h0c1-1 2-1 3-1 1-1 2-1 2-2s-1 0-2 0h-2v-1l4-1v1c2-1 5-2 7-3v1l10-4z"></path><path d="M372 601l1 1 1 1c-2 1-4 2-6 2h-4l6-3 2-1z" class="F"></path><path d="M370 599c1 0 2 0 2 1 0 0-2 1-3 1-3 2-9 4-12 4l3-2 10-4z" class="E"></path><path d="M311 317v-3c-1 0-1 0 0 0l2 2v-1c2 2 3 5 4 7 1 0 1 1 2 1h0v2l5 8v1l2 6h-1c-1-2-2-3-3-5v1l2 6v2c-6-7-10-15-14-23h0c-1-1-2-2-3-4h1v-1l2-1 1 2z"></path><path d="M312 323h1v-2c2 0 2 1 3 1v1c0 2 1 2 1 3l-2-2h-2l-1-1z" class="B"></path><path d="M311 317v-3c-1 0-1 0 0 0l2 2v-1c2 2 3 5 4 7 1 0 1 1 2 1h0v2l5 8v1l-8-11v-1c-1 0-1-1-3-1v2h-1l-2-2h0c-1-1-2-2-3-4h1v-1l2-1 1 2z" class="M"></path><path d="M310 315l1 2 3 4h0c-1 0-2-1-3-1l-3-3h0v-1l2-1z" class="N"></path><path d="M237 331l10-15c1-2 2-5 4-6h0c0 1-1 1-1 2h0c-2 5-6 10-9 14-9 14-13 30-14 46-1 1 0 4-1 6h0c0-1-1-2-1-2 0-2 0-3-1-4h0v1-5h-1v-2c0-3 1-6 2-8 1-4 2-8 3-11l2-4c0-3 1-5 2-7l2-3h1l1 1 1-3z" class="M"></path><path d="M234 333h1l1 1c-1 1-1 2-2 3 0 1-1 3-2 4l-4 12c0-1 0-2 1-2v-2-1l1-2v-1-2c0-3 1-5 2-7l2-3z" class="C"></path><path d="M230 343v2 1l-1 2v1 2c-1 0-1 1-1 2 0 5-3 10-4 15h0-1v-2c0-3 1-6 2-8 1-4 2-8 3-11l2-4z" class="J"></path><defs><linearGradient id="Ag" x1="175.456" y1="433.737" x2="185.258" y2="415.007" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient></defs><path fill="url(#Ag)" d="M214 369v1l-29 42-1 1c0 1-1 2-1 2-1 2-4 6-4 8h1 0c1-3 4-6 6-9 1-1 2-2 2-3 3-3 5-6 8-8v1c-1 2-3 5-6 7v1c-1 2-2 3-2 5v1h0c0 2-1 3-2 4-4 4-8 9-13 12v-1c2-7 6-13 9-19 1-1 1-1 1-2 3-6 8-11 12-16l19-27z"></path><path d="M198 503v-3l-1 2h0s0-1-1-2c-1-2-2-4-2-6-3-9-1-18 4-26-1 6-1 11-1 17 3-3 5-7 6-11 1-1 2-4 3-5 0 0 1 0 2-1l-4 10c-1 4-2 7-4 11-1 5-1 9-2 14z" class="J"></path><path d="M257 336c1 0 1 0 1 1v3l1 1v5c1 4 1 8 2 11l6 28c-1 1-1 2-1 3-1-1-1-3-2-4h-1l-5-11c0-1-1-2-1-3l-2-7c0-2 0-4-1-6h1c1 2 1 4 2 6 1-1 0-3 0-4h1v-4l-1-9v-10z"></path><path d="M257 363c1-1 0-3 0-4h1l1 9c-1-1-2-3-2-5z" class="E"></path><path d="M254 357h1c1 2 1 4 2 6 0 2 1 4 2 5l3 11c1 2 1 3 2 5h-1l-5-11c0-1-1-2-1-3l-2-7c0-2 0-4-1-6z" class="L"></path><path d="M273 287l1-1 2 2c0 2-1 3-1 5v1h0c0 1 0 1-1 1v2h0 0l-1 1c0 1 0 2-1 2l-2 4c1 0 2-1 2-1v-1l1-1v-1l1-1v-1l1-1c0-1 0-2 1-2v-1l1-1c1-1 1-2 2-3h0v1h0c0 2-1 3-1 5h1v-2h1 0v1 1 1l-1 3-1 1h0l-1 1h0-1l-1 1v-1c0-1 2-2 1-4l-1-1v2l-2 3c0 1-1 1-1 2l-1 1s-1 1-1 2h-1c-1 2-3 3-4 5l-1 1-2 2-4 7c-1 1-1 2-1 2-1 1-2 3-2 4h-1c1-1 1-2 1-3 1-1 1-1 1-2h1c0-2 0-3 1-4 1-2 1-3 1-5h0v-1l4-4c1-2 1-2 1-4l1-1 2-4v-1c0-1 1-1 1-2s1-2 2-3c0-3 1-5 3-7z" class="D"></path><defs><linearGradient id="Ah" x1="264.14" y1="351.426" x2="269.947" y2="350.993" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#3a3a3a"></stop></linearGradient></defs><path fill="url(#Ah)" d="M263 333c-1-3 0-7 1-10 0 1 0 3 1 4h1v2c2 5 3 10 4 16 1 4 0 8 0 12s1 8 1 12l2 10v4c0-1 0-1-1-2 0-4-2-8-3-13l-4-21c-1-2-2-3-2-5v-9z"></path><path d="M263 333c-1-3 0-7 1-10 0 1 0 3 1 4 0 8 5 15 3 24-1-2-2-3-2-5v-2c-1 0-1-1-1-1v-1c-1-3-1-7-1-10l-1-1v2z" class="K"></path><path d="M255 328c0-1 1-3 2-4 0 0 0-1 1-2l4-7 2-2 1-1c1-2 3-3 4-5h1c0-1 1-2 1-2l1-1c0-1 1-1 1-2l2-3v-2l1 1c1 2-1 3-1 4v1l1-1h1 0l1-1c0 1-1 2-1 3h0c-1 2-3 5-4 7l-3 4v-1-1l-2 2v1c-1 1-2 3-3 5l-3 3-2 3c-2 3-3 6-3 9v10l1 9v4h-1c0 1 1 3 0 4-1-2-1-4-2-6h-1 0v-1-7c0-4 0-7 1-11-1-2-1-3-1-4 0-2 0-3 1-4v-2z" class="J"></path><path d="M255 330h1v-2l1 1v-1c1 2-2 8-2 10-1-2-1-3-1-4 0-2 0-3 1-4z" class="D"></path><path d="M254 356c0-1 0-3 1-4v1c0 1 0 2 1 3h0v-9c1 0 1 0 1-1l1 9v4h-1c0 1 1 3 0 4-1-2-1-4-2-6h-1 0v-1z" class="F"></path><path d="M218 365l1-1c1 1 1 0 0 1l-1 8c-1 2-2 5-3 6l-2 6c-1 2-3 5-5 6s-3 3-4 3c-3 2-6 6-8 9-3 2-5 5-8 8 0 1-1 2-2 3-2 3-5 6-6 9h0-1c0-2 3-6 4-8 0 0 1-1 1-2l1-1 29-42c2-2 2-4 4-5h0z"></path><path d="M218 365l1-1c1 1 1 0 0 1l-1 8c-1 2-2 5-3 6l3-14z" class="E"></path><path d="M297 373h0l-5-11c-1-1-3-4-3-6l7 11h0c0 1 1 1 1 2h0l1 1v-2h1l1 2v-1l-1-1v-1c0-1-1-1-1-2l-1-1v-2l1 1 1-1 2 4c3 3 4 7 6 11l1 1s1-1 1-2v-1c1 1 1 2 1 3v1c0 1 2 5 2 6h-1v-2 1c0 1 0 2-1 3 0 2 2 12 1 14-2-3-3-4-5-7v-1l-2-2 1 3 1 1c0 3 3 6 3 9-1-2-4-6-5-8 1-2 0-3 0-4-1-2-3-5-4-8-1-1-2-2-2-3h0 1c0 1 1 2 1 3h1v2s1 1 1 2l1 1h0 0c-1-1 0-2-1-3v-1-1h0l-1-1-1-3s-1 0-1-1v-1l-1 1h0v-1c0-2-1-3-1-5z" class="C"></path><path d="M309 375c1 1 1 2 1 3v1h-1 0c0 3 0 3-2 5l-1-3-1-2 2-2 1 1s1-1 1-2v-1z" class="B"></path><path d="M307 377l1 1v2c0 1-1 1-2 1l-1-2 2-2z" class="F"></path><path d="M298 363l1-1 2 4c3 3 4 7 6 11l-2 2-7-16z" class="E"></path><path d="M297 373h0l-5-11c-1-1-3-4-3-6l7 11c0 3 3 6 4 8l-2-6c2 2 3 4 4 7 2 3 5 7 5 11v2l-1-1v1c-2-3-4-7-6-10-1-2-1-4-3-6zm25-57c1 2 3 5 4 7l1 3 1-1 3 3c1 1 3 4 3 6l2 2c0 1 0 3-1 4l-2 6c0 1-1 1-2 2-1 0-1 0-2-1h0c0 3-1 7-3 9l-2-6h1c0-3 0-4-1-6v-2l-2-6v-1c1 2 2 3 3 5h1l-2-6v-1l-5-8v-2h0c-1 0-1-1-2-1 1-1 2-1 4-1h0l1-1-2-2c1 0 1-1 2-2z" class="H"></path><path d="M326 340c1-1 1-1 2-1h1l2 4-1 1v-1l-1 1 1 1h-1s-1-1-1-2l-2-3z" class="N"></path><path d="M324 342l-2-6v-1c1 2 2 3 3 5 2 3 3 5 4 8-2-2-4-4-5-6zm4-17l3 3c1 1 3 4 3 6v4l-1 1c-1-2-2-5-3-7l-3-6 1-1z" class="L"></path><path d="M331 328c1 1 3 4 3 6v4c-1-3-4-7-3-10z" class="F"></path><path d="M326 323l1 3 3 6s-1 0-1 1v3h-1l-3-6-1-1v-2l2-4z"></path><path d="M319 323l2 2c0 2 2 4 3 4h0l1 1 3 6h1l1 1h0c-1 1-1 2-1 2h-1c-1 0-1 0-2 1h0l-2-6v-1l-5-8v-2z" class="G"></path><path d="M324 333c1 2 3 4 4 6-1 0-1 0-2 1h0l-2-6v-1z" class="E"></path><path d="M322 316c1 2 3 5 4 7l-2 4v2h0c-1 0-3-2-3-4l-2-2h0c-1 0-1-1-2-1 1-1 2-1 4-1h0l1-1-2-2c1 0 1-1 2-2z" class="D"></path><path d="M322 320c1 1 1 2 1 3h-1c0 2 0 2-1 2l-2-2h0c-1 0-1-1-2-1 1-1 2-1 4-1h0l1-1z" class="E"></path><path d="M231 375c0 8 0 17 3 24 1 4 3 7 4 10l3 6c0 2 2 4 3 6 0 1 1 2 2 3h-1c-4-4-5-8-8-13v-1c0 2 1 4 1 5 4 8 10 14 15 20 2 2 4 4 4 6l2 3 1-1 2 3h1 0 0l1 2h0c-1 2-2 3-3 4v1s0 1-1 2l1 1-1 1-2-3h1c0-1 0-2 1-3-1-3-2-6-5-8h-1 0l-3-6c-4-5-9-9-13-14-3-4-5-10-6-15 0-1 0 0-1-1v2h0 0l-1 4v-5c-1-5 0-11 0-16 0-1 0-4-1-6v-1c0-3 1-7 2-10z" class="B"></path><path d="M263 446v1h-1c0 1-1 2-2 3 0-1-1-3-2-5 0-1 0-1-1-2v-1-1l2 3 1-1 2 3h1 0 0z" class="I"></path><path d="M232 369v-6h1l1-6c1 3-1 6-1 10h0v4l4 23 3 12c1 2 2 4 3 7 2 5 6 9 9 14 5 6 8 12 11 19h0-1l-2-3-1 1-2-3c0-2-2-4-4-6-5-6-11-12-15-20 0-1-1-3-1-5v1c3 5 4 9 8 13h1c-1-1-2-2-2-3-1-2-3-4-3-6l-3-6c-1-3-3-6-4-10-3-7-3-16-3-24v-6h1z"></path><path d="M232 369v-6h1l1-6c1 3-1 6-1 10h0v4l4 23 3 12c1 2 2 4 3 7 2 5 6 9 9 14 5 6 8 12 11 19h0-1l-2-3c-2-3-2-7-4-10-4-5-8-9-11-14l-4-7c-1-1 0-1-1-1v-2c-4-8-4-17-6-24l-2-16z" class="M"></path><defs><linearGradient id="Ai" x1="208.729" y1="473.529" x2="237.702" y2="476.253" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#5c5e5e"></stop></linearGradient></defs><path fill="url(#Ai)" d="M219 393c0 1 0 1 1 3l-6 38c-2 25-2 50 4 74 2 10 5 19 10 28 0 2 2 4 2 5 0 2 1 3 2 4l-1 1-1-1c-2-3-4-8-6-12-8-18-12-37-13-57l1-18c0-9-1-19 1-29h-1v-3h0l-1-2h0c1-1 1-4 2-6 1 0 1-1 1-2l5-23z"></path><path d="M214 416c0 5 0 9-1 13h0-1v-3h0l-1-2h0c1-1 1-4 2-6 1 0 1-1 1-2z" class="D"></path><path d="M228 342v-2l1-1h1c0 1-1 1-1 2v2h0v1l-1 1v1 1c-1 3-2 7-3 11-1 2-2 5-2 8-2 7-3 15-4 23 0 1-1 3 0 4l-5 23c0 1 0 2-1 2-1 2-1 5-2 6h0c0 3 0 5-1 7h-1v1h0l-1-1h-1l1-3 1-11c1-1 1-3 1-4 0-3 1-7 2-11h-1v-2c2-4 2-9 3-13v-1l-1-1 2-6c1-1 2-4 3-6h1c0-1 1-6 2-7h0c1-2 2-7 2-9v1h-1v-1-1c1-4 3-9 5-12 1-1 1-1 1-2z" class="K"></path><path d="M209 417c1 3 1 4 0 7l1-1c0-1 0-2 1-3v-1c0 4-1 8-2 12v1h0l-1-1h-1l1-3 1-11z" class="H"></path><defs><linearGradient id="Aj" x1="210.363" y1="385.957" x2="221.275" y2="383.01" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#3c3d3d"></stop></linearGradient></defs><path fill="url(#Aj)" d="M218 373h1c0-1 1-6 2-7h0c-1 7-2 14-4 20-1 5-4 9-4 13 0 1-1 1-1 2v1h0-1v-2c2-4 2-9 3-13v-1l-1-1 2-6c1-1 2-4 3-6z"></path><path d="M256 316v-1c1 0 1-1 1-1l1-1 1-1c0-1 1-1 2-2 0-1 1-2 1-3 1 0 2-1 2-2 0 2 0 2-1 4l-4 4v1h0c0 2 0 3-1 5-1 1-1 2-1 4h-1c0 1 0 1-1 2 0 1 0 2-1 3h1v2c-1 1-1 2-1 4 0 1 0 2 1 4-1 4-1 7-1 11v7 1h0c1 2 1 4 1 6l2 7c0 1 1 2 1 3l5 11v1c1 3 4 7 5 11s2 9 3 13l-1 1c0-1-1-1-1-2-1 0-1 1-2 0 0-1-1-1-2-2h0l-1-1-3-6-1-2-1-1-5-10-1 1c0 1 0 1 1 2v1c0 1 0 1 1 2v1c1 1 1 1 1 2h0l-2-2c0-2-1-3-2-4 0-1 0-2-1-2h-1l1 2-1 1c-1-1-1-2-1-3l-1-3-3-14c-1-3-1-5-2-7v15 1 3h0v1l1-2v1 1 4c1 0 1 2 1 3h-1c0-2 0-3-1-5v2c1 1 1 3 1 4 0-1-1-1-1-2-1-2-1-4-1-6-1-2-2-4-2-7l-1-5v1 5l-1-1c0-1 0-2-1-3v1c0 5 2 12 0 18v2l-4-23v-4h0c0-4 2-7 1-10l-1 6h-1v6h-1c0-5 2-9 2-13h-1c1-2 1-4 2-6 1-3 1-5 3-8 0 3-2 7-2 10 0 0 1 0 0 1v1l1 1v-2c2-3 2-8 3-11h0v2c-1 4-2 10-1 13 1 1 1 1 1 2v1 3c1-1 1-3 1-4v-1-1-1h0c0-2 0-3 1-5v2c0 1-1 1-1 1h1c0-1 0-2 1-4 1-1 1-3 1-4 1-3 2-5 2-8 1-2 1-4 2-6 1-5 4-9 7-13l1-1c0-1 0-2 1-2z" class="J"></path><path d="M237 371h0v-1-1-2c1-1 1-2 1-3v-3c1 3 0 6 0 10v1l-1-1z" class="B"></path><path d="M239 371h1v-5-8c1-1 1-2 2-3l-1 4v10 7h-1l-1-5z" class="H"></path><path d="M245 343v3c1 1 1 3 1 5 0 1 0 3 1 4 0 3 0 6 1 10 0 3 2 5 2 9-1-1-1-2-1-4-1-2-2-4-2-7v-1c-1-3-1-6-2-9v9-2c-1-2-1-4-1-7s0-6 1-10z" class="D"></path><path d="M250 387h0l-3-14c-1-2-2-4-1-7h0c1 4 2 9 4 12 1 3 3 5 4 8l-1 1c0 1 0 1 1 2v1c0 1 0 1 1 2v1c1 1 1 1 1 2h0l-2-2c0-2-1-3-2-4 0-1 0-2-1-2h-1z" class="G"></path><path d="M254 328h1v2c-1 1-1 2-1 4 0 1 0 2 1 4-1 4-1 7-1 11v7 1 1c-1-3-2-5-3-8-2-8 1-15 3-22z"></path><defs><linearGradient id="Ak" x1="239.689" y1="370.04" x2="233.295" y2="369.139" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#454445"></stop></linearGradient></defs><path fill="url(#Ak)" d="M233 367c1 0 1-1 1-1l3-15 1 1-1 1c1 1 1 4 1 5v3 3c0 1 0 2-1 3v2 1 1h0v3c0 5 2 12 0 18v2l-4-23v-4z"></path><path d="M245 343v-1c2-5 2-10 5-15 0 2-1 4-1 5-3 10-2 22 1 32 2 6 4 15 9 21l-3-9c0-2 1 0 0-2h0c0-1-1-2-1-3h1l1-1v1c0 1 0 1 1 2h0l5 11v1c1 3 4 7 5 11s2 9 3 13l-1 1c0-1-1-1-1-2-1 0-1 1-2 0 0-1-1-1-2-2 0-1 0-2-1-3l-4-7v-2c-1 0-1-1-1-2-2-1-2-4-3-5l-1-1-1-2-2-4v-1l-2-5c0-4-2-6-2-9-1-4-1-7-1-10-1-1-1-3-1-4 0-2 0-4-1-5v-3z" class="K"></path></svg>