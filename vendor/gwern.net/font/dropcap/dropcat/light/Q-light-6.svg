<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="55 34 465 652"><!--oldViewBox="0 0 560 752"--><style>.B{fill:#030303}.C{fill:#282728}.D{fill:#e5e4e5}.E{fill:#f6f5f6}.F{fill:#2f2e30}.G{fill:#efedef}.H{fill:#0d0c0c}.I{fill:#161617}.J{fill:#faf9fa}.K{fill:#686768}.L{fill:#fefefe}.M{fill:#d0d0d1}.N{fill:#4a494a}.O{fill:#272728}.P{fill:#7c7b7c}.Q{fill:#5b5a5b}.R{fill:#c0bfc1}.S{fill:#deddde}.T{fill:#525151}.U{fill:#a1a0a1}.V{fill:#b8b7b9}</style><path d="M141 343h0c1 1 1 2 1 3h0-2c0-1 0-2 1-3z" class="B"></path><path d="M254 287s0-1 1-1h1l1 1v1l1 1c-1 0-2 0-3 1-1-1-1-2-1-3z" class="G"></path><path d="M223 263l2-1c1 0 1 0 2 1l-1 1s-1 1-2 1-2 1-3 1v-1l2-2z" class="B"></path><path d="M109 617h0c0 2 0 4-1 5 0 1-1 1-2 2v-2-1c0-1 2-3 3-4z" class="N"></path><path d="M208 236c1 4 3 7 6 9-1 1-2 2-3 2h0l1-1v-1l-8-5 1-1h2c0-1 0-2 1-3z" class="F"></path><path d="M125 251c0-1 0-2 1-3s2-1 3-1l3 3-2 3h-1c-2-1-3 0-4-2z" class="H"></path><path d="M349 543c3 1 5-2 7 3 1 1 1 4 2 4 0 1 1 1 1 2v1h-1v-1c-1-2-4-5-7-7h-2-1v-1l1-1z" class="I"></path><path d="M80 595c4 1 5 4 8 5l-1 1c-3 0-4 1-7 2 0-1-2-2-2-4l1-1h4c0 1 1 1 1 2h0c1 1 1 0 3 0-2-1-3-3-5-4v1 1c-1-1-1-2-2-3z" class="E"></path><path d="M213 264c5-2 6-5 9-8h1c0 2 0 3-1 5-4 4-7 4-12 5v-1l3-1z" class="B"></path><path d="M98 616c-1 2-3 3-5 4l2-6c2-5 6-8 11-10v1h0c-2 1-3 2-5 3v1c-2 2-3 4-3 6v1z" class="I"></path><path d="M244 277c5 2 7 6 10 10 0 1 0 2 1 3 1-1 2-1 3-1 1 1 1 1 1 2l-1 1h-3c0 1 0 1-1 1-1-1-2-3-3-4-1-5-3-8-7-12z" class="B"></path><path d="M254 293c1 0 1 0 1-1l2 2c3-1 6-3 8-1 1 0 2 1 2 2s-1 2-1 3h-5 0c-1 0-2 0-3-1-2 0-3-2-4-4z" class="I"></path><path d="M336 397c2-1 3-2 5-1 2 0 3 2 4 3 1 2 1 4 1 6-1 3-5 7-7 8s-4 1-5 1h-1 0l2-1c2-1 4-3 6-4l1-1c1 0 1-2 2-2 0-2 0-4-1-6-1-1-3-2-5-2-1 0-1 0-2-1z" class="B"></path><path d="M142 374h1c3-1 6-1 10-2h3v-1-1l4-8h1c2 0 2 0 3 1-1 1-4 2-6 4v3c2-1 5-2 8-2-3 2-5 3-7 4s-3 2-4 2c-3 1-6 1-9 1-1-1-2-1-4-1z" class="N"></path><path d="M147 242h0v3h1c0 1 0 2 1 2 0 2-1 4 0 6v4 4c0 3 1 6-1 8-2-4-3-10-3-15 1-4 2-8 2-12z" class="F"></path><path d="M125 251c1 2 2 1 4 2h1c0 3 0 5 2 8h-4c-1-1-3-2-5-2l-1 1-1-2c0-2 0-3 1-5 1-1 2-1 3-1v-1z" class="N"></path><path d="M125 251c1 2 2 1 4 2-1 1-1 1-3 2-1 0-2 0-4 1l-1 2c0-2 0-3 1-5 1-1 2-1 3-1v-1z" class="Q"></path><path d="M167 367c2 1 2 1 3 2-2 0-6 2-7 3h-1c0 1-1 1-2 2 0 0 1 0 1 1-1 0-1 0-2 1h1-1c-1 0-1 0-2 1h-1 0-8c0 2-1 3-2 4s-3 1-4 1c-2-1-4-3-5-4l4-4h1c2 0 3 0 4 1 3 0 6 0 9-1 1 0 2-1 4-2s4-2 7-4l1-1z" class="H"></path><path d="M207 261c2-3 5-5 6-8 1-1 2-3 2-5h1c1 2 0 4-1 6-1 4-4 6-7 8-2 1-3 2-5 3 6-2 11-5 14-10l2-3s1 1 1 2v3c-2 2-4 5-7 6s-7 3-10 3c-3 1-6 1-9 2-1 0-3 1-4 1-2 1-3 1-4 2 2-1 4-3 7-4 4-2 9-3 14-6h0z" class="B"></path><path d="M135 246c2 0 2 1 3 1v-3h1l5 5v-1l2-1c-2 5-6 9-4 13s5 8 7 11c1 1 2 3 2 4h0c-1-1-2-3-4-4-4-5-12-11-12-18v-1l2-2h0-3l1-1h0v-1h1c0-1 0-2-1-2z" class="O"></path><path d="M338 398c2 0 4 1 5 2 1 2 1 4 1 6-1 0-1 2-2 2l-1 1c-2 1-4 3-6 4l-2 1h0 1v1l-2 1c0-1-1-1-1-1-1 0-1 0-1-1h0c0-1 1-2 1-3v-1c0-2 0-1-1-1l1-2c1-1 1-1 1-2v-1-1l2-2c1 0 1-1 2-2s2-1 2-1z" class="E"></path><path d="M338 400h1c1 1 3 2 3 3 1 0 1 2 1 3-1 1-1 2-2 2h-1c-1 0-1-1-2-2h0c-1 1-2 1-3 1 0-1 3-3 3-4l-4 2c1-1 2-4 4-5z" class="C"></path><path d="M80 595h0c-3-1-5 0-8 0 1-1 11-3 13-3-3-2-7-4-9-7 3 2 6 4 9 4 3 1 5 1 7 1l-6-2h0c1-1 5 1 7 1 1 0 3 2 4 3 1 2 1 4 0 5v1l-2 2h-7c-3-1-4-4-8-5zm228-193c0-6 1-12 4-16 2-3 5-7 9-8 1-1 3-1 5-1-4-1-7 0-10 2l-1-1c1-1 3-1 4-2 4-1 9 0 13 2 5 2 8 7 9 12-1 0-2-2-3-3-2-2-5-4-9-4-5 0-11 2-14 5-4 4-5 9-7 14z" class="B"></path><path d="M192 264v1h1 1c4 0 9-2 12-5l1 1h0c-5 3-10 4-14 6-3 1-5 3-7 4 1-1 2-1 4-2 1 0 3-1 4-1 3-1 6-1 9-2 3 0 7-2 10-3v1l-3 1v1c5-1 8-1 12-5h0c0 1-2 3-3 3v1c1 0 1 0 1-1l3-1-2 2v1c1 0 2-1 3-1 0 0 1 1 2 1h2 1l1 1c0 1 1 2 1 2-6-1-12-3-17-2 10 2 21 3 27 14 4 6 4 14 3 22l-1-2 1-3h-1s-1 1-1 2h-1 0c1-2 3-4 2-7 0-1 0-2-1-3 0 1-1 1-2 1l-1-1h0c0 1 1 1 1 2v2c-1 1-1 1-2 1 0-2 0-5-1-7-1-1-1-2-2-3 2 1 3 3 4 4 1-1 2-1 3-2-1-2-1-4-3-6-3-7-12-10-19-12-14-3-27-2-39 6l-1-1c2-2 4-4 5-6h1c1 0 2-1 3-2l1-1c1 0 1 0 2-1z" class="D"></path><path d="M339 516c3-2 7-3 10-2 1 0 1 0 2 1h-1 0l-1 1c0 1 0 1 1 1 0 1 1 2 2 3l-1 1-1 1v1h0-1c-2-1-4-1-6-2l2 2c2 1 3 2 4 3v1l1 3c-1 0-1 0-2-1s-3-3-5-3-5 1-7 3c-5 4-7 11-9 17l-1-2c4-8 4-17 9-24 1-2 2-3 4-4zM134 229c1 0 3-2 4-1 2 0 5 4 6 6 2 3 3 9 2 13l-2 1v1l-5-5h-1v3c-1 0-1-1-3-1 1 0 1 1 1 2h-1v1h0l-1 1v-1l-3-6c-1-3-1-6 1-9h0c-1-1-1-2-1-4h1c1-1 1-1 2-1z" class="B"></path><path d="M134 242l1 1v3c1 0 1 1 1 2h-1 0c-2-2-1-5-1-6z" class="D"></path><path d="M132 230c1-1 1-1 2-1 2 2 3 3 6 5-2-1-6-1-7-2v-1l-1-1zm2 12c0-2 0-3 2-4h4l4 4v6 1l-5-5h-1v3c-1 0-1-1-3-1v-3l-1-1z" class="M"></path><path d="M148 329c1 0 2 0 3-1v1c5 2 12 4 16 8 1 2 1 4 0 7-1 2-3 6-5 7-3 1-7-1-9-2l-5-3c-1 0-3-2-4-3-1-2 0-4 0-6-1-1-2-1-3-1v-1-2c1-2 5-4 7-4z" class="B"></path><path d="M148 329c1 0 2 0 3-1v1c-1 1-3 1-4 2 0 2 1 3 1 4s0 1 1 2c1 3-1 6-1 9-1 0-3-2-4-3-1-2 0-4 0-6-1-1-2-1-3-1v-1-2c1-2 5-4 7-4z" class="C"></path><path d="M148 329c1 0 2 0 3-1v1c-1 1-3 1-4 2 0 2 1 3 1 4h0c-2 0-3-2-5-2v1l2 2s0 1-1 1c-1-1-2-1-3-1v-1-2c1-2 5-4 7-4z" class="E"></path><path d="M197 243c0-1 2-2 3-3 1 0 1-1 2-1h1l1 1 8 5v1l-1 1h0l-2 1v1l4 1c-3 4-5 6-7 10-3 3-8 5-12 5h-1-1v-1l-4-1c0-2-1-6 0-7v-1c1-2 0-3 0-5h-1c1-2 1-3 2-4 1 0 1 0 2 1l4-4v-1h-1c-1-1-1-1-2-1 0 0 1 0 1-1l2 1h1c0 1 1 1 1 2z" class="B"></path><path d="M205 244v-1h1c1 1 1 1 1 2v1c0 1-1 2-2 3v-5zm-13-3s1 0 1-1l2 1h1c0 1 1 1 1 2l1 2s0 1-1 1c-1-1-2-2-2-3v-1h-1c-1-1-1-1-2-1z" class="G"></path><path d="M197 243c0-1 2-2 3-3 1 0 1-1 2-1h1l1 1 8 5v1l-1 1h0l-2 1v1c-2-2 0-2 0-4l-2 1v-1c0-1 0-1-1-2h-1v1l-1-1c-1 0-2-1-3-1s-2 1-3 3l-1-2z" class="D"></path><defs><linearGradient id="A" x1="146.266" y1="279.349" x2="129.018" y2="298.501" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242324"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M118 282c-2 1-3 1-4 3h-1 0l6-6c-4 2-7 3-11 3 0-2 0-4 1-5 2-3 6-6 10-7 7-2 16 2 23 6s14 11 18 18c1 2 2 5 2 7h1v-1c1 2 1 3 2 4 1 0 1-3 1-4v3h1c0 6-2 11-1 17v1l-1 1h0c-1-2-1-4-2-6v-1c0 1 0 1 1 1 0-5-2-11-5-16s-8-9-13-10c-9-2-16 2-23 7 1-4 3-6 6-9-4 1-7 3-10 6l-1-1 1-2v-1l-2 1h0v-2h0c-1-1 0-2 0-3v-1h-1c0-1 1-2 2-3z"></path><path d="M163 300c1 2 1 3 2 4 1 0 1-3 1-4v3h1c0 6-2 11-1 17h0c-1 0-1-1-1-1-1-6-1-12-3-18h1v-1z" class="U"></path><path d="M118 282s1-1 2-1l1-1h3v1h-1 1c0 1-1 1-2 1v1c0 1-1 1-1 1-1 1 0 2-1 2 0 1-1 1-1 2h1l-1 2h0l-2 1h0v-2h0c-1-1 0-2 0-3v-1h-1c0-1 1-2 2-3z" class="C"></path><path d="M155 217c3-1 5-2 7-3v1l-1 1h0c-1 1-1 2-1 3 2-1 2-2 4-3 1 0 2 0 3 1l1 2c-1 1-1 2-1 3h1c1 1 1 1 2 1 1 1 0 2 0 3 0 2-1 4-3 5 1 0 1 1 2 0 5 1 9 3 12 7 1 2 3 5 4 8v-1c1 0 1-1 1-2l2-2h0l-1 2h0c1 0 2-1 3-2v-1c1-2 4-4 6-5 3-3 5-6 8-9-1 5-8 10-11 13-2 1-3 3-4 4s-3 3-3 4c1 0 5-5 6-6 1 0 1 0 2 1h1v1l-4 4c-1-1-1-1-2-1-1 1-1 2-2 4h1c0 2 1 3 0 5v1c-1 1 0 5 0 7l4 1c-1 1-1 1-2 1l-1 1c-1 1-2 2-3 2h-1c-1 2-3 4-5 6-6 5-13 10-14 18v8c0 1 0 4-1 4-1-1-1-2-2-4v-2l-3-6c-5-9-13-16-23-20-5-2-14-2-17-7-2-3-1-6-1-9h1v2c1 3 4 5 7 6l-5-4 1-1c2 0 4 1 5 2h4l2 1c0-1-1-2-2-3v-4h0 0c2 6 7 11 12 15 3 3 6 5 9 9 4 5 6 10 9 15h1l-3-6-8-13-4-6c2-2 1-5 1-8v-4-4c-1-2 0-4 0-6-1 0-1-1-1-2 1-6 1-13 2-20 1-3 3-5 5-8z" class="B"></path><path d="M176 247v1 2h2l-8 5c0-1 0-2 1-3s0-1 1-2c0 0 0-1 1-1l1 1c1-1 1-1 2-3z" class="D"></path><path d="M163 247c2 0 4 1 6 2v-1c1-1 2-1 3-1l1 1v1c-1 0-1 1-1 1-1 1 0 1-1 2s-1 2-1 3l-1 1h-1c0-1 0-1-1-2-2 1-3 3-3 5-1 0-1 0-1 1l-2-2v1c0 4-1 7-1 11l-1 1c0 2 2 3 3 5v13-1c-3-7-6-13-6-21 0-1-1-3 0-4 0-2 1-3 2-5 0-1 1-2 1-4 1 0 0-1-1-1v-3h1c1 1 0 1 1 2h1c1 0 1 1 2 1l2-2c-1 0-1-1-1-1v-1h1l-1-1h-1 0v-1z" class="E"></path><path d="M149 253c2-5 5-10 11-12 2-1 5-2 7-1s4 3 6 4l2 2c1 0 4 1 5 2s2 3 2 5h0-1l-1 1h1c-1 1-1 1-2 1 0-2-1-4-1-5h-2v-2-1c-1 2-1 2-2 3l-1-1v-1l-1-1c-1 0-2 0-3 1v1c-2-1-4-2-6-2v1h0 1l1 1h-1v1s0 1 1 1l-2 2c-1 0-1-1-2-1h-1c-1-1 0-1-1-2h-1v3c-1-1-3-1-5-1-2 1-3 3-4 5v-4z" class="G"></path><path d="M163 247h0c-3-1-5 0-8 1-2 2-3 4-5 6 0-1 1-2 1-3 2-4 6-7 11-9 3 0 4 0 7 1 2 1 3 3 5 4h1 1c-1 2-1 2-2 3l-1-1v-1l-1-1c-1 0-2 0-3 1v1c-2-1-4-2-6-2z" class="B"></path><path d="M169 231c5 1 9 3 12 7 1 2 3 5 4 8v-1c1 0 1-1 1-2l2-2h0l-1 2h0c1 0 2-1 3-2v-1c1-2 4-4 6-5 3-3 5-6 8-9-1 5-8 10-11 13-2 1-3 3-4 4s-3 3-3 4c1 0 5-5 6-6 1 0 1 0 2 1h1v1l-4 4c-1-1-1-1-2-1-1 1-1 2-2 4h1c0 2 1 3 0 5v1c-1 1 0 5 0 7l4 1c-1 1-1 1-2 1l-1 1c-1 1-2 2-3 2h-1l1-3c1-2 0-6-1-8 0 2 0 4-1 5-1 5-4 11-8 14-3 2-7 4-9 8h0c-3 5-2 11-2 17l-1-1v-6-2c1-5 1-10 1-14 4 0 8-1 12-4 2-2 1-3 2-6 0-1 1-2 1-3 1-1 2-2 2-4 0-1 0-1-1-2l-4 1 2-5c1 0 1 0 2-1h-1l1-1h1 0v2c0 1 1 1 1 2v2c1-2 0-3 1-4 0 0 1-1 2-1h1 0v-2c-1 1-3 2-5 3 1-2 2-4 2-6 0-4-2-7-5-10s-6-6-10-6c-5 0-11 4-15 8-1 1-2 3-4 4 2-4 4-8 7-11h1c3 0 6-2 9-3 1 0 1 1 2 0z" class="J"></path><path d="M155 217c3-1 5-2 7-3v1l-1 1h0c-1 1-1 2-1 3 2-1 2-2 4-3 1 0 2 0 3 1l1 2c-1 1-1 2-1 3h1c1 1 1 1 2 1 1 1 0 2 0 3 0 2-1 4-3 5-3 1-6 3-9 3h-1c-3 3-5 7-7 11 2-1 3-3 4-4 4-4 10-8 15-8 4 0 7 3 10 6s5 6 5 10c0 2-1 4-2 6 2-1 4-2 5-3v2h0-1c-1 0-2 1-2 1-1 1 0 2-1 4v-2c0-1-1-1-1-2v-2c0-2-1-4-2-5s-4-2-5-2l-2-2c-2-1-4-3-6-4s-5 0-7 1c-6 2-9 7-11 12-1-2 0-4 0-6-1 0-1-1-1-2 1-6 1-13 2-20 1-3 3-5 5-8z" class="B"></path><path d="M155 217c3-1 5-2 7-3v1l-1 1h0c-3 1-4 3-6 5-2 1-3 2-4 4h-1c1-3 3-5 5-8z" class="G"></path><path d="M160 219c2-1 2-2 4-3 1 0 2 0 3 1l1 2c-1 1-1 2-1 3h1c1 1 1 1 2 1 1 1 0 2 0 3l-3-1c-3-1-5-4-7-6z" class="C"></path><path d="M150 225h1v1c0 1 1 3 2 4s2 1 2 1c0 2-2 3-2 5-2 3-4 7-4 11-1 0-1-1-1-2 1-6 1-13 2-20z" class="J"></path><path d="M147 242c0-6 0-16-5-20-2-2-4-2-5-4h0c0-1 1-2 2-2 2 0 3 0 4 1 3 2 5 7 5 11v1h1c0-1-1-3-1-4-1-4-3-8-5-12-1-2-1-5-1-6 2-5 4-9 6-13 6-10 13-22 23-27 3-2 7-3 11-4s8-3 11-4c13-5 24-12 35-18-3 7-8 13-12 19-2 2-4 4-5 7 4 2 9 4 12 8 4 3 4 9 7 13 4 4 8 7 11 11 2 1 4 3 4 4 1 4-3 8-5 12-3 6-10 10-17 9-3 0-7-2-10-1h0c-1 0-1 1-2 1-2 1-3 3-4 6h0c0 2 0 4 1 5v1h0c-1 1-1 2-1 3h-2l-1 1-1-1h-1c-1 0-1 1-2 1-1 1-3 2-3 3 0-1-1-1-1-2h-1l-2-1c0 1-1 1-1 1-1 1-5 6-6 6 0-1 2-3 3-4s2-3 4-4c3-3 10-8 11-13-3 3-5 6-8 9-2 1-5 3-6 5v1c-1 1-2 2-3 2h0l1-2h0l-2 2c0 1 0 2-1 2v1c-1-3-3-6-4-8-3-4-7-6-12-7-1 1-1 0-2 0 2-1 3-3 3-5 0-1 1-2 0-3-1 0-1 0-2-1h-1c0-1 0-2 1-3l-1-2c-1-1-2-1-3-1-2 1-2 2-4 3 0-1 0-2 1-3h0l1-1v-1c-2 1-4 2-7 3-2 3-4 5-5 8-1 7-1 14-2 20h-1v-3h0z" class="B"></path><path d="M183 234c-1 1-2 1-3 1s-1 0-1-1l4-2v2z" class="K"></path><path d="M180 217c-2 3-4 4-8 5 2-2 5-4 7-6l1 1zm-6-8l2 2c-1 2-2 3-3 3-2 1-3 1-4 1v-1c3-1 4-3 5-5z" class="P"></path><path d="M174 209c2-2 4-5 6-7 0 2-1 3-2 4-1 2-1 4-2 5l-2-2z" class="K"></path><path d="M181 229c-3 2-6 2-10 2l11-5-1 3z" class="Q"></path><path d="M218 186c1 1 3 5 4 7h0l-4 1v-8z" class="L"></path><path d="M149 205c1 1 1 1 2 1 1-1 3-1 5-1-4 2-8 4-9 7l-1 1h0v-1c0-2 0-4 2-5l1-2z" class="N"></path><path d="M149 205c2-2 5-3 7-4 2 1 4 1 6 2l-6 2c-2 0-4 0-5 1-1 0-1 0-2-1z" class="Q"></path><path d="M164 213h0c3-1 5-2 7-4-1 2-3 3-4 5l2 2c-1 0-1 1-2 1-1-1-2-1-3-1-2 1-2 2-4 3 0-1 0-2 1-3h0l1-1v-1h0l2-1z" class="M"></path><path d="M209 185h1v9l-2-2-4-5 5-2z" class="L"></path><path d="M179 216c1-2 3-3 3-5 1-4 0-8 2-11 1 1 0 3 0 4 0 3 1 6 0 9-1 1-2 3-4 4l-1-1z" class="K"></path><path d="M182 226c3-2 7-4 10-6 2-2 4-4 5-6-3 7-9 12-15 15h-1l1-3zm-17-23h2c-7 4-13 10-18 17 1-6 4-9 8-12 3-2 5-4 8-5zm18 29h1c6-3 10-9 17-12-5 5-11 12-18 14v-2z" class="P"></path><path d="M169 216c1 0 3 2 3 3v2 1s1 1 1 2v2c-1 2-2 4-4 5-1 1-1 0-2 0 2-1 3-3 3-5 0-1 1-2 0-3-1 0-1 0-2-1h-1c0-1 0-2 1-3l-1-2c1 0 1-1 2-1z" class="S"></path><path d="M168 219h2c0 1 1 2 1 2l-1 1h-3c0-1 0-2 1-3zm44-35h4v11h-4v-11z" class="L"></path><path d="M156 201l22-5-11 7h0-2c-1-1-2 0-3 0-2-1-4-1-6-2z" class="K"></path><path d="M173 224l1 1h0c2-1 3-2 5-3 1-1 2-2 3-2s1 0 2-1c4-2 7-5 10-8 1-2 2-4 4-5-2 6-7 12-12 15-4 2-8 3-11 5h-2v-2z" class="Q"></path><path d="M207 230c0 2 0 4 1 5v1h0c-1 1-1 2-1 3h-2l-1 1-1-1h-1c-1 0-1 1-2 1-1 1-3 2-3 3 0-1-1-1-1-2h-1l-2-1c5-2 9-4 12-8l1-1 1-1z" class="V"></path><path d="M207 230c0 2 0 4 1 5v1h0c-1 1-1 2-1 3h-2l-1 1-1-1c0-1 2-2 2-3v-1h0c-1 2-2 3-4 4 1-2 2-3 3-4h1v-3l1-1 1-1z" class="C"></path><path d="M170 183h1c-3 8-15 7-21 11-1 1 0 1-1 1v-1c3-4 9-6 13-8 3-1 5-2 8-3z" class="Q"></path><defs><linearGradient id="C" x1="171.917" y1="209.111" x2="161.103" y2="206.742" xlink:href="#B"><stop offset="0" stop-color="#605e61"></stop><stop offset="1" stop-color="#7e7f7c"></stop></linearGradient></defs><path fill="url(#C)" d="M155 217c1-2 4-4 6-6 3-2 7-5 11-8 3-2 6-4 8-6h1c-2 2-4 3-5 5-4 4-8 7-12 10v1l-2 1h0c-2 1-4 2-7 3z"></path><path d="M176 189c1 4-17 8-20 9-2 1-5 2-7 3-2 2-3 4-4 6v-1c2-5 6-9 12-11 7-2 13-2 19-6z" class="K"></path><path d="M244 303c1-8 1-16-3-22-6-11-17-12-27-14 5-1 11 1 17 2 3 2 6 3 8 5 2 0 2 0 4 1 1 1 1 1 1 2 4 4 6 7 7 12 0 0 0 1-1 1l1 1v2c0 2 0 4 1 5 0 1 2 2 2 2l4 2-1 1h-2c-1 0-1 1-1 1h-2c0 1 0 2-1 3 0 3-1 6-1 10-1 3-2 6-3 8h0c-1 1-2 3-3 5-3 6-8 10-13 14v2c5-5 9-8 13-13l-8 16-9 12c-3 2-6 5-9 7-2 1-4 1-6 2-1 1-2 1-3 1l1 1c-2 1-3 2-4 3h-1c-2 0-3 1-5 0l-1 1h-1l-1-1 1-1v-1l-5 3c-3-1-7-1-10-2-1 0-3 1-4 1 0 1 0 1 1 2-2 1-4 1-6 1l1 1h-10 0c-1-1-2-1-3-1l-6-1h0 1c1-1 1-1 2-1h1-1c1-1 1-1 2-1 0-1-1-1-1-1 1-1 2-1 2-2h1c1-1 5-3 7-3-1-1-1-1-3-2 1 0 1 0 2-1v-3c1-2 4-3 6-4v-1h-10c-3 1-6 2-8 2-1-1-4-3-5-4-3-1-7 0-10 1 4 0 8-2 11 1 1 1 1 1 2 3 0 2-1 4-3 6s-6 4-9 3c-1 0-2-1-3-1-2 0-5-1-7-2l2-3h-1s0 1-1 1c-1 1-1 2 0 3h-1 0 0v-3c0-1-4-2-5-4-1-6 3-7 6-11 1 0 1-1 2-1 3-1 7-1 10 0 3 0 6 1 9 2 6 2 12 3 19 4-4-2-6-3-8-5 1-3 2-5 4-7h0c0 1-1 3-2 5 0 1 1 2 2 2s2 0 2-1c1 0 1-2 2-3v-1h2c-2-3-5-7-8-9v1c-4-4-11-6-16-8v-1c-1 1-2 1-3 1-2 0-6 2-7 4v2 1c0 2 0 4-1 6l-1 1c-1 2-2 2-4 3l-1-1c1-1 2-3 2-4-1 1-2 3-3 5-2 1-3 1-5 1s-4-1-6-2c-1-3-2-6-2-9 0-2 1-4 3-6 1 0 1-1 1-1-2 0-4 3-5 4l-3 3c0-2 1-3 2-4l2-2c-2 0-3 1-4 1v2h-1v-2c-1 0-2-1-3-2v-2h2v-1s-2 0-3-1c0 0-1-1-1-2v-3c1-1 2-1 3-1l1-1h1c-1-1-2-3-3-4-2-2-2-7-1-10 1-4 3-8 7-11l1 1c3-3 6-5 10-6-3 3-5 5-6 9 7-5 14-9 23-7 5 1 10 5 13 10s5 11 5 16c-1 0-1 0-1-1v1c1 2 1 4 2 6h0l1-1v-1c-1-6 1-11 1-17h-1v-3-8c1-8 8-13 14-18l1 1c12-8 25-9 39-6 7 2 16 5 19 12 2 2 2 4 3 6-1 1-2 1-3 2-1-1-2-3-4-4 1 1 1 2 2 3 1 2 1 5 1 7 1 0 1 0 2-1v-2c0-1-1-1-1-2h0l1 1c1 0 2 0 2-1 1 1 1 2 1 3 1 3-1 5-2 7h0 1c0-1 1-2 1-2h1l-1 3 1 2z" class="B"></path><path d="M162 378h5c1 0 2-1 3-1s3 0 4 1l1 1h-10 0c-1-1-2-1-3-1z" class="D"></path><path d="M175 345l5 6h-4l-3-6h2z" class="G"></path><path d="M115 318c4 3 7 5 12 6h1c-1 1-1 1-2 1-3 0-6 0-8-3l-3-3-1-1h1z" class="S"></path><path d="M202 367c2-2 4-4 7-6v6l1 1h-1c-2-1-4-1-7-1z" class="C"></path><path d="M184 374l3-3c1 0 1 1 2 1s2 0 3-1c3-1 5-3 8-3h0v1l1 1-1 1c-1-1-1-1-1-2-2 1-4 1-5 3h0l-3 2h-2-4-1z" class="T"></path><path d="M126 326c1 0 2 0 3-1h5c-4 1-7 3-11 3-3 0-5 1-7-1h0c1-2 7-1 10-1z" class="E"></path><path d="M169 350c1 0 2 0 2-1 1 0 1-2 2-3v-1l3 6 3 3c-4 0-7-1-10-4z" class="K"></path><path d="M212 352h0c7-2 13-4 19-8v2c-4 3-9 6-14 7h-1l1-1c-2 0-5 2-6 1h0 1v-1z" class="P"></path><path d="M176 351h4c8 5 17 6 26 5h1c-3 1-5 1-8 1-7 0-13-1-20-3l-3-3z" class="E"></path><path d="M126 321l7 1h12c1 0 4 1 5 1-5 0-11 1-16 2h-5c-1 1-2 1-3 1v-1c1 0 1 0 2-1h-1l2-1c-1 0-2 0-3-1v-1z" class="P"></path><path d="M126 321l7 1 2 1s-1 0-2 1c-1 0-3-1-4-1s-2 0-3-1v-1z" class="U"></path><path d="M180 274l1 1c-6 5-12 11-13 19l-1 9h-1v-3-8c1-8 8-13 14-18z" class="L"></path><path d="M148 329l-1-1c-1 0-3 0-4 1-5 1-9 6-14 9 3-5 9-10 15-11 8-1 15 2 21 6 1 1 2 2 2 3v1c-4-4-11-6-16-8v-1c-1 1-2 1-3 1z" class="G"></path><path d="M137 321l1-1h0c-1-1-2-1-4-2h0c-1-1-1-1-1-2h-1c0-1-1-3-1-4v-2c1 1 1 2 2 3v1h0c1 1 2 3 3 3h1c1 1 1 1 2 1l1 1h1 1 3c1 1 1 1 2 1h1 1c2 1 6 1 8 0h0c4 3 7 8 11 11 1 1 3 5 4 6v1c-6-7-11-13-20-14l-2-1c-1 0-4-1-5-1l-8-1z" class="R"></path><path d="M175 358c2 0 6 1 7 1 1 1 2 0 4 1l14-1c-3 1-7 2-10 2-2 1-3 2-5 3-4 1-8 1-12 3h0 1l1 1c-2 1-3 1-5 1-1-1-1-1-3-2 1 0 1 0 2-1v-3c1-2 4-3 6-4v-1z" class="J"></path><path d="M175 358c2 0 6 1 7 1 1 1 2 0 4 1-5 0-9 1-14 4-1 1-2 2-3 2v-3c1-2 4-3 6-4v-1z" class="N"></path><path d="M210 368c1-1 2-3 3-3 2-2 6 0 9-2 1 0 2-2 3-3l2 1c-3 2-6 5-9 7-2 1-4 1-6 2-1 1-2 1-3 1l1 1c-2 1-3 2-4 3h-1c-2 0-3 1-5 0l-1 1h-1l-1-1 1-1v-1l-5 3c-3-1-7-1-10-2h1 1 4 2l3-2h0c1-2 3-2 5-3 0 1 0 1 1 2l1-1c0-1 1-2 1-3 3 0 5 0 7 1h1z" class="Q"></path><path d="M199 376c0-2 1-3 1-4 1-1 2-1 3-1 5 0 10-3 15-3-2 1-4 1-6 2-1 1-2 1-3 1l1 1c-2 1-3 2-4 3h-1c-2 0-3 1-5 0l-1 1z" class="P"></path><path d="M200 375c3-2 6-3 9-4l1 1c-2 1-3 2-4 3h-1c-2 0-3 1-5 0z" class="L"></path><path d="M137 321c-2-1-4-1-6-2s-3-3-4-5c-1-4 0-8 1-11 3-4 7-6 11-7 5-2 9-1 13 2 7 4 9 11 11 18 1 2 1 4 2 6l3 9-6-9c0-2-1-3-2-4h-1c1-2 1-5 0-8-2-4-4-7-9-10v1 2h0c-2-2-5-6-8-6-1 1 1 2 0 3-2 0-3-1-4-2-1 0-2 1-2 1-1 1 1 3 1 4-2 1-3 0-5 2 1 1 4 3 5 4h0c-1 1-3 0-3 0 1 3 4 4 6 7-1 0-2-1-4-1h0 0c3 2 8 3 11 5-1 0-1 0-2-1h-3-1-1l-1-1c-1 0-1 0-2-1h-1c-1 0-2-2-3-3h0v-1c-1-1-1-2-2-3v2c0 1 1 3 1 4h1c0 1 0 1 1 2h0c2 1 3 1 4 2h0l-1 1z" class="E"></path><path d="M119 294c3-3 6-5 10-6-3 3-5 5-6 9 7-5 14-9 23-7 5 1 10 5 13 10s5 11 5 16c-1 0-1 0-1-1v1c-2-7-4-14-11-18-4-3-8-4-13-2-4 1-8 3-11 7-1 3-2 7-1 11 1 2 2 4 4 5s4 1 6 2l8 1h-12l-7-1v1c1 1 2 1 3 1l-2 1c-5-1-8-3-12-6-1-1-2-3-3-4-2-2-2-7-1-10 1-4 3-8 7-11l1 1z" class="B"></path><path d="M119 294c3-3 6-5 10-6-3 3-5 5-6 9 7-5 14-9 23-7 5 1 10 5 13 10s5 11 5 16c-1 0-1 0-1-1 0-5-3-13-7-17s-10-6-16-6c-4 0-8 1-11 4s-4 7-6 11v-1-7c-2 1-5 2-6 5-1 2-1 6 0 8 2 5 5 7 9 9v1c1 1 2 1 3 1l-2 1c-5-1-8-3-12-6-1-1-2-3-3-4-2-2-2-7-1-10 1-4 3-8 7-11l1 1z" class="J"></path><path d="M118 293l1 1 1 1c0 1-1 2-2 3-3 3-4 6-4 10 0 2 1 4 2 7 2 3 6 6 10 7 1 1 2 1 3 1l-2 1c-5-1-8-3-12-6-1-1-2-3-3-4-2-2-2-7-1-10 1-4 3-8 7-11z" class="B"></path><path d="M244 303c1-8 1-16-3-22-6-11-17-12-27-14 5-1 11 1 17 2 3 2 6 3 8 5 2 0 2 0 4 1 1 1 1 1 1 2 4 4 6 7 7 12 0 0 0 1-1 1l1 1v2c0 2 0 4 1 5 0 1 2 2 2 2l4 2-1 1h-2c-1 0-1 1-1 1h-2c0 1 0 2-1 3 0 3-1 6-1 10-1 3-2 6-3 8h0c-1 1-2 3-3 5-3 6-8 10-13 14-6 4-12 6-19 8h0v1h-1 0c-4 1-8 1-12 0-8-2-15-6-20-13-3-4-4-11-3-17 1-3 2-6 4-9 1-1 2-3 4-4 1-1 3-1 5-2 3-1 6-2 9-4l-1-5c0-1 1-3 3-4h0 0c2-1 2-1 4-1-1-1-2-3-3-4l-4 2 4-4h0c1 1 3 4 5 5-1-3-3-4-4-7h-1c-1 0-1 1-2 2 0-2 0-4 1-6-3 0-5 0-8 1 0 0 0-1-1-2 0 0-3-1-4-1h-3l-1 1c0 1 0 2 1 2h1v-2h1c1 0 2 0 2 2 0 1-1 2-2 3-2 2-3 3-4 5h0 0c0-2-1-5-2-7v5c-1 2 0 3-1 5l-1-3h-1c-1 1-1 3-1 4 0 3 0 8 1 10l3 3c-5 7-9 16-7 26 1 5 6 10 10 14h0c-8-5-14-15-15-24-1-10 1-28 6-35s12-12 20-13c5-1 11 0 15 3 1 1 2 3 2 5 1 4 0 9 0 13 2-1 4-3 5-5 1 1 1 5 3 6h3c2-1 4-2 4-4v-2c1 1 1 2 2 2 1 1 3 2 4 3 1 0 1 1 2 1s3-2 4-3c1 0 1 0 2-1v-2c0-1-1-1-1-2h0l1 1c1 0 2 0 2-1 1 1 1 2 1 3 1 3-1 5-2 7h0 1c0-1 1-2 1-2h1l-1 3 1 2z" class="E"></path><path d="M172 309c1-1 1-1 2 0v1c0 1-1 2-2 4h-1c-1-1-1-2-1-3 1-1 1-2 2-2z" class="C"></path><path d="M206 286l1 1c2 1 2 4 2 5 0 4 0 8-2 10l-1 1c0-3 2-7 1-10 0 0-1 0 0-1 0-2 0-4-1-6z" class="B"></path><path d="M198 304l-1-5c0-1 1-3 3-4h0 0 3 1 0c-1 2-4 4-5 5 1 1 1 1 2 1l-3 3z" class="I"></path><path d="M200 311v-1l-8 2h-4-1v-1c1-2 3-2 5-3 0-1 2-1 2-1 2-1 6 1 8 2v-1c1 1 2 1 3 2-1 1-2 1-3 1h-2z" class="C"></path><path d="M240 294v-2c0-1-1-1-1-2h0l1 1c1 0 2 0 2-1 1 1 1 2 1 3 1 3-1 5-2 7-1 1-3 2-4 3-2 1-6 1-9 0-1 0-2-1-3-2v-1c3 0 5 1 7 1 3 0 6-1 8-4 1-1 2-3 2-5h0c-1 0-2 1-2 2z" class="H"></path><path d="M241 300h1c0-1 1-2 1-2h1l-1 3c-1 2-2 7-4 8-1 1-1 0-2 0h-1-1 0c0 3-1 3-1 5 0 1-1 2-1 4h1l1 1h-3c-1 0-2 2-3 2-3 2-5 3-9 5 1-2 2-4 3-5v-1h-3l1 1h0v1c-4 2-7 2-11 1s-7-2-9-5c-1-2-2-5-1-7h2c1 0 2 0 3-1-1-1-2-1-3-2 2-2 4-4 7-5s4 0 7 0c4 0 6-1 9 0 2 2 5 3 8 3 4 0 6-3 8-5v-1z" class="B"></path><path d="M230 311c-2-1-4-1-7-1 3-1 6-2 8-1 1 0 1 1 2 2h-3z" class="U"></path><path d="M241 301c0 2-2 3-4 4 0 1-1 1-1 1h0c-2 1-4 1-5 1h0-2c-1-1-4-1-5-2l1-2c2 2 5 3 8 3 4 0 6-3 8-5z" class="H"></path><path d="M230 311h3c0 1 1 2 1 3s-1 2-1 4h1l1 1h-3c-1 0-2 2-3 2h0c-2 1-4 1-6 2 1-2 3-3 4-4 2-1 3-2 5-3l-2-5z" class="R"></path><path d="M244 303c1-8 1-16-3-22-6-11-17-12-27-14 5-1 11 1 17 2 3 2 6 3 8 5 2 0 2 0 4 1 1 1 1 1 1 2 4 4 6 7 7 12 0 0 0 1-1 1l1 1v2c0 2 0 4 1 5 0 1 2 2 2 2l4 2-1 1h-2c-1 0-1 1-1 1h-2c0 1 0 2-1 3 0 3-1 6-1 10-1 3-2 6-3 8h0c-1 1-2 3-3 5-3 6-8 10-13 14-6 4-12 6-19 8h0c-6 0-11 0-17-2-7-4-13-10-16-18-1-2-2-5-1-7h0c1 0 3 0 4 1l1 1-1-8v-4h1c1 0 2 0 3 1h0 0 1v-1c1-1 2-2 3-2 2 0 4-1 6 0l1 2-1 6 1 12c0 3 0 7 2 10s5 4 8 4h1c-2 0-3-1-4-2-5-5-5-13-5-19h0c2 5 7 11 12 13 4 1 8 2 11 0-3-1-4-1-7-2-2-2-3-5-3-8 2-1 6 1 8 1 8 0 15-1 20-6-4 0-7 1-11 0 3-1 5-2 8-3 4-4 6-13 7-18z" class="B"></path><path d="M197 333l-2-2v1c-1 1 0 2-1 4v-1c0-1-1-4 0-5v-4c1 0 1-1 1-2v-1c1-1 1-2 1-2l1 12z" class="H"></path><path d="M239 274c2 0 2 0 4 1 1 1 1 1 1 2 4 4 6 7 7 12 0 0 0 1-1 1l1 1v2l-1 1v-1c-2-7-5-13-11-19z" class="E"></path><path d="M251 293c0 2 0 4 1 5 0 1 2 2 2 2l4 2-1 1h-2c-1 0-1 1-1 1h-2c0 1 0 2-1 3 0 3-1 6-1 10-1 3-2 6-3 8h0c2-11 4-21 3-32v1l1-1z" class="D"></path><path d="M182 319v-4h1c1 0 2 0 3 1h0 0 1v-1c1-1 2-2 3-2 2 0 4-1 6 0l1 2c-3 2-5 6-7 9-1-2-2-5-3-7h-5v2z" class="F"></path><path d="M251 289c1 1 2 3 3 4 1 2 2 4 4 4 1 1 2 1 3 1h0 0-2v3h1 1c3-2 10-1 13 0s5 4 6 7l6 23c0-3 0-7-1-10 0-1-3-9-2-10v-1h4c2 1 3 4 3 5v2l-3 15c0 4 1 11 3 14-1-7-1-13 1-20l1 1h1l1 1c0 1-1 2-2 3v1h0c2-3 5-5 7-7h0c2 3 3 6 2 9-1 4-3 8-2 12 1-5 3-9 6-13-1 5-3 11-4 16 2-4 2-7 8-8 1 0 2-1 3 0 0 2-1 3-2 5h1c3 0 5 2 7 4-4 2-7 4-10 8-1 3-2 7-2 10-1 8-1 16-1 24h0c1-11 2-24 8-33-1-1-1-1-1-3h3 0c0 1-1 1-2 2 1 0 2 1 3 1 1-1 3-3 4-3 1 1 2 2 2 3 0 0 0 1-1 2 2 0 4-1 5-2v-1c1 0 2 1 2 2s-2 2-2 3h1c0 1-1 1-2 2h2v1c-1 1-3 1-4 2 4 0 7 0 9 3 2 2 3 5 3 7-2-3-5-6-9-6-4-1-8 1-11 3-6 5-7 13-8 21l-1 6h2c2-5 3-10 7-14 3-3 9-5 14-5 4 0 7 2 9 4v1c0 1 0 1 1 2h0v2h0c-1 0-1 0-2-1 0-1-2-3-2-3l-1-1h0-3c-2 1-2 1-3 3l1 1c2 0 2-2 4-1 1 1 1 1 1 2l-3 6c1 1 3 0 5-1 1 1 1 1 2 1 0 0-1 0-2 1s-1 2-2 2l-2 2v1 1c0 1 0 1-1 2l-1 2c1 0 1-1 1 1v1c0 1-1 2-1 3h0c0 1 0 1 1 1 0 0 1 0 1 1l2-1c6 0 10 0 14 3 6 5 8 12 9 19 0 7-1 13-5 19 0 0 1 0 2 1l2 1 1-2h0c0 3 1 8-1 10l-3 3h0c2 0 4 1 5 3 1 1 1 1 0 2s-1 1-2 1l-1 1c2 3 2 5 1 8 0 1 0 2-1 2v4c0 3-1 5-2 8-2 4-6 7-10 10-2 2-4 5-6 7 4-2 8-9 13-9v1c0 1-1 2-2 3s-3 2-5 2c-2 1-3 2-4 3v1c-2 1-3 2-4 4-5 7-5 16-9 24l1 2-4 11h1c0 3-1 6-2 9-1 11 4 23 9 32v1c-7 4-14 8-22 11-31 13-68 15-100 6-7-2-14-4-21-7-3-1-5-3-8-4-3-2-6-3-9-6h0c-2 0-5 2-7 4 1-2 4-3 4-5v-2c-1 0-1 0-1-1s0-1-1-2c0 0 1 0 1-1 2-1 4-4 5-6 6-6 13-6 21-7h2c0-1 1-1 1-1h3l5-2h0c-1-1-3-1-4-1-5 0-9 1-13 0h-1-1l-9-6c-1 1-4 2-5 3v2c0 1 1 2 1 3l1 1-3-1v-1c-1-1-1-2-2-3s-2-2-3-2c-2 0-2 1-3 2h0c-2 1-2 0-4 0 1-2 1-2 1-4-4 0-8 1-12 1-2 1-5 2-7 2-3-2-5-5-6-8-8-10-13-21-18-33-11-31-10-70 4-101 7-14 18-25 31-34 4-2 8-3 11-5s7-3 10-5h3l3-3c3-2 7-3 10-4h1l-1-1v-1c2-1 4 0 5 0v-1l5-3v1l-1 1 1 1h1l1-1c2 1 3 0 5 0h1c1-1 2-2 4-3l-1-1c1 0 2 0 3-1 2-1 4-1 6-2 3-2 6-5 9-7l9-12 8-16c-4 5-8 8-13 13v-2c5-4 10-8 13-14 1-2 2-4 3-5h0c1-2 2-5 3-8 0-4 1-7 1-10 1-1 1-2 1-3h2s0-1 1-1h2l1-1-4-2s-2-1-2-2c-1-1-1-3-1-5v-2l-1-1c1 0 1-1 1-1z" class="B"></path><path d="M271 388c0 1 0 2 1 2l-1 1h0c-1 0-2 0-3-1l3-2z" class="V"></path><path d="M262 386l1-1h5 2l-2 1c-2 0-4 1-6 0z" class="H"></path><path d="M292 484l1 1c0 2 1 2 2 4v1c-1 0-1 1-1 1-1 0-1-1-2-2s0-3 0-5z" class="P"></path><path d="M316 510c3-2 5-5 7-8h0c0 1-1 2-1 3-1 0-1 1-1 1-1 3-2 4-4 6l-1-2zm-22-30c-2 1-3 2-5 4h0c0-2 0-3 2-5 1-1 3-1 5-1l-1 1s-1 0-1 1z" class="Q"></path><path d="M271 388c4-2 9-3 14-1-5 0-9 1-13 3-1 0-1-1-1-2z" class="R"></path><path d="M272 485c1 1 2 1 3 2 1 4 1 5 0 9v1c-1 0-1-1-1-2l-1-1c1 0 1 0 1 1l1-1v-1c-1 0-1-1-1-1v-1-1c0-1 0-2-1-3 0 0 0-1-1-2h0z" class="O"></path><path d="M296 478c1 0 2 0 3 1s1 2 1 3-1 1-1 2c-1 1-1 2-2 3v1c-1-3 1-4 1-7-1-1-1-1-2-1h-2 0c0-1 1-1 1-1l1-1z" class="N"></path><path d="M343 436c0-1 0-1 1-1 1 1 2 1 3 3l1 4h-1 0c-2-1-3-4-4-6z" class="G"></path><path d="M296 480c1 0 1 0 2 1 0 3-2 4-1 7h0c0 1 0 2 1 2v1c-1 0-2 1-3 1l-1-1s0-1 1-1c1-2 0-5 1-7l1-1c0-1 0-1-1-2z" class="T"></path><path d="M190 575c1-1 3-1 4-2s2-2 2-3c1 1 1 1 1 2 1 1 1 2 2 2h0l1 1c-5 0-9 1-13 0h3z" class="E"></path><path d="M257 432h4v1c-1 0-3 8-3 10-1-3-1-7-1-11z" class="D"></path><path d="M336 397c1 1 1 1 2 1 0 0-1 0-2 1s-1 2-2 2h-1-6c1-1 2-2 4-3 1 1 3 0 5-1z" class="C"></path><path d="M309 463c5 0 9 1 13 1-1 1-3 0-4 0l-1 1c-1 1-2-1-3 1h0-1c-1 0-1 0-1 1l-1-1c-1-1-2-2-2-3z" class="D"></path><path d="M296 480c1 1 1 1 1 2l-1 1c-1 2 0 5-1 7v-1c-1-2-2-2-2-4l-1-1c0-2 1-3 2-4h2z" class="K"></path><path d="M321 443h5v1l1 3-1 1v-1-1c-2 0-3 1-5 0-1 0-1-1-2-1l1-2h1z" class="C"></path><path d="M321 443h5v1c-1 1-1 1-2 1s-2-1-3-2z" class="N"></path><path d="M271 415c1 0 3-2 3-2v-3-1c0 2 1 4 1 6s-1 3 0 5l-1 3h0v-3h-1v1 1h-1v-3-2h0l-1-2z" class="C"></path><path d="M332 477c3-2 6-2 10-1 1 0 2 0 3 1-1 0-1 1-2 1h-1c-2-1-3-1-5 0-1 1-1 1-2 1v-2h-3z" class="M"></path><path d="M264 495h1c0-1 0-2 1-3 2 2 4 6 5 8h1l-3 1-3-3c-1-1-2-2-2-3z" class="K"></path><path d="M260 388v-1c0-2 1-3 3-5s6-2 9-2h0 0c-3 0-6 2-8 4l-1 1-1 1c-1 1-1 2-1 3 0-1 0-1-1-1z" class="P"></path><path d="M257 374c0-1 1-1 1-2 1 0 1-1 1-2h0c1-2 2-3 4-3h0c1 1 2 2 3 2h2v1c-4 0-8 2-11 4z" class="E"></path><path d="M251 289c1 1 2 3 3 4 1 2 2 4 4 4 1 1 2 1 3 1h0 0-2v3c-2-1-5-2-6-5-1-2-1-3-2-5h0l-1-1c1 0 1-1 1-1z" class="D"></path><path d="M273 494c0-2-1-3-2-5-1-3-3-6-5-10l1 1c2 2 4 3 5 5h0c1 1 1 2 1 2 1 1 1 2 1 3v1 1s0 1 1 1v1l-1 1c0-1 0-1-1-1zm-13-173l2 2v1 1c-1-2-1-2-1-3h-1-1 0c0 1 0 2-1 2-1 2 0 3-2 5h0 1c1 0 1 0 2-1 0 0 2 0 2 1h2c-3 0-6 1-9 1v-2c1-3 2-4 4-7h2z" class="F"></path><path d="M287 471c-2-1-2-3-3-4-2-3-3-4-5-6h0v-1c2 1 4 1 5 2s2 1 2 2c0 2 1 5 1 7z" class="I"></path><path d="M199 574h5c2 0 8-2 9-1l1 1c3 0 7-1 10 0-6 0-11 0-16 2h-4 0c-1-1-3-1-4-1l-1-1z" class="C"></path><path d="M280 493l1 1c-2 1-2 3-3 5-1 1-4 3-5 5l-1 1c0-1 0-1-1-1s-2-1-3-2h0 2l-1-1 3-1c2 0 4-1 6-3 0-1 1-3 2-4z" class="S"></path><path d="M327 467v1h1c-4 6-4 12-5 18v-2c-1-3-1-7-1-10 0-1 1-1 1-2h1c0-2 1-3 3-5z" class="R"></path><path d="M254 487c1 1 2 2 2 3 2-1 3-2 3-4 1 0 1 0 1 1 1 1 2 3 2 4l-1 1-2-1h0c-1 1-1 2-2 2l-1 1h0c-1-1-1-2-2-3 0-1 0-1-1-2h1v-2z" class="E"></path><path d="M224 574c3 1 7 1 10 3-5-1-10-1-16-1-3 0-5 0-8 1v-1h-2c5-2 10-2 16-2z" class="D"></path><path d="M356 458c-2 2-6 3-9 4-4 1-7 2-11 3 4-2 8-3 11-5 2-1 3-2 5-4 0 0 1 0 2 1l2 1z" class="E"></path><path d="M270 385c1 0 2-1 3 0h0 0c-2 1-3 1-4 3l-1 1c-1 1-1 3-2 3h-1c-1 1-1 2-1 2h-1v-3c-1 0-1-1-2-2 0-1 0-2 1-3 2 1 4 0 6 0l2-1z" class="D"></path><path d="M262 386c2 1 4 0 6 0-1 2-3 4-5 5-1 0-1-1-2-2 0-1 0-2 1-3z" class="B"></path><path d="M266 392c1 0 1-2 2-3v1c1 1 2 1 3 1h0 2c0 2-1 2 0 3-1 1-2 2-2 4h2 0c-1 1-2 3-4 4l-1-1c1-2 1-4 1-7h-2s-1-1-1-2z" class="C"></path><path d="M266 392c1 0 1-2 2-3v1c1 1 2 1 3 1-1 1-2 2-4 3h0s-1-1-1-2z" class="R"></path><path d="M264 514l-1 1-1-1c-2-2-3-4-3-7v-2h-3c0-1-1 0-2-1 3-1 4-1 6-1v3c1 1 1 1 2 1 2 0 2 1 4 2h0c-1 1-2 2-2 4v1z" class="I"></path><path d="M203 485l-2 24v1h0l-1 2c-1-8 0-19 3-27z" class="C"></path><path d="M298 368c3 6 5 15 5 22-1 0-1-1-1-1-1-1-1-1-1-2 1-2 0-3 0-4-1-4-3-7-4-11v-3h0c1 0 1-1 1-1z" class="S"></path><path d="M327 493c0 1 0 3 1 3 0-1-1-2 0-3h1l-1 1c0 2 1 3 1 4v1h-1-1 0l1 1s-1 0-1 1l-2 2v1h1c0 2-1 3-2 4-2 0-3 1-4 3h-1c0 1 0 1 1 1-1 0-2 1-3 1h-1l1-1c2-2 3-3 4-6 4-4 5-8 6-13z" class="V"></path><path d="M276 477c-2-7-4-11-10-15 7 3 10 8 12 14l3-3 3 6v1h-1c0-1-1-2-1-3l-1 1h-2c-1 0-1 0-2 1 0-1 0-2-1-2zm-68 99h2v1c-6 1-11 2-15 7-1 2-1 3-2 4-1 0-2 1-3 1 0-1 1-1 0-3 1-3 6-6 9-8l5-2h4z" class="L"></path><path d="M266 316c0-1-1-5 0-6 2 1 3 4 3 5l4-4c1 1 2 1 2 3h0 0v1c0 1-1 2-2 3h-1l-1 1s-1 0-1-1v2l-3-2s-1-1-1-2z" class="H"></path><path d="M254 304s0-1 1-1h2c-1 4-2 8-1 12-1 0-1 1-2 1h0-2v-2l-1-1v-4c1-2 2-3 3-5z" class="G"></path><path d="M278 362c6 4 13 8 16 15h-2c0-1-2-2-2-3-4-3-8-7-13-8l-2-1v-1h1 2v-2z" class="O"></path><path d="M266 316l-1-1c-1 0-1 0-1-1-1-2-1-7 0-9 4 2 6 3 9 6l-4 4c0-1-1-4-3-5-1 1 0 5 0 6z" class="P"></path><path d="M327 447v-4h1l3 2h0c1 1 2 2 2 4h0l-1 1v5c-1 0-2-1-3-2-2-3-4-3-7-4 1-1 2-1 4-2v1l1-1z" class="J"></path><path d="M329 443c2 0 4 1 6 0 3-1 4-3 5-6-3 0-5-1-8-2 4-1 8-2 12 0-1 0-1 0-1 1l-1 2c-1 1-3 4-4 5h0c-1 1-1 2-1 3v2h0-1c-1-2-1-2-3-2l-2-1h0l-3-2h1z" class="M"></path><path d="M268 410l3 3v2l1 2h0-1v-2h-1c1 1 0 2 1 2v2 5h0c0-1 0-3-1-4h0c-1-1-2-2-2-3h-1v1 1h1c0 1-1 2-1 3v3c-1 1 0 3-1 4v-2h-1v1 1c-1-1 0-1 0-2v-1-2 1l-1 1h-1c1 0 1 0 1 1v2h0v2c-1 1 0 2-1 4 0-7 0-11-4-17 2 2 5 4 6 6h1v-1c0-2 1-3 1-5-1-1-1-3-1-4l2 1v-1l-1-1c1-1 1-2 1-3z" class="F"></path><path d="M193 579h2c0-1 1-1 1-1h3c-3 2-8 5-9 8 1 2 0 2 0 3-1 2-3 4-5 5-1 0-1 0-1-1h0c0-1-1-1-1-1l-1-1c1-1 2-1 2-2 2-2 4-5 6-6h0s0-1 1-2h-1c1 0 2-1 3-2z" class="H"></path><path d="M253 372c0-2 2-3 4-4 6-5 11-6 18-4v1c-3-1-9-2-11 0 0 1-1 1-1 2-2 0-3 1-4 3h0c0 1 0 2-1 2 0 1-1 1-1 2s-1 2-2 3c-1 0-1 0-2-1v-3h0v-1z" class="S"></path><path d="M275 408c2 1 4 3 5 5h0c0-2 1-3 2-5h0v4 5c1 3 1 8 0 11v-5-4c-1-1-2 0-3 0h-1l-1 1v-1-1c-1 0-1-3-1-4h0-1 1c-1 1-1 2-1 2l1 1c-1 1-1 2-1 3h0c-1-2 0-3 0-5s-1-4-1-6l1 1v-2z" class="F"></path><defs><linearGradient id="D" x1="275.978" y1="416.017" x2="278.147" y2="410.155" xlink:href="#B"><stop offset="0" stop-color="#39383a"></stop><stop offset="1" stop-color="#565554"></stop></linearGradient></defs><path fill="url(#D)" d="M275 408c2 1 4 3 5 5h0c-1 2-1 3-2 4h0c-1-3-2-5-3-7v-2z"></path><path d="M348 442c1-1 0-1 1-2 1 1 1 3 1 4 0 5-3 9-7 13-5 4-11 6-15 11h-1v-1c5-5 13-8 17-14 1-2 2-3 3-6v-5h0 1z" class="L"></path><path d="M335 426v-1c-1 0-2-1-3-2 0-1-1-1-1-2 1-1 3-1 4-1 0 1 1 1 1 1h1c1-1 6 1 7 1 1 1 2 2 3 2h0 1c1 1 2 3 3 4-1 0-3-1-3-2-1 0-2-2-3-2l-1-1h-1 0c0 1 0 1 1 1v3h0v1l-1-1c-2-1-6 0-8-1z" class="C"></path><path d="M208 583c7-5 16-5 24-3 5 0 11 1 15 3-3 0-6-1-8-1-9-2-16-1-23 5v-1c1-2 3-4 4-6-4 1-8 1-11 4l-1-1z" class="E"></path><path d="M264 404c-1-1-1-1-1-2 1 1 1 1 2 1h0 2 1c0-1 1-1 2-1v3c2 1 4 2 5 3v2l-1-1v1 3s-2 2-3 2v-2l-3-3c-1 0-2 1-3 0v-3h0c-1 0-1-2-1-3z" class="L"></path><path d="M270 405c2 1 4 2 5 3v2l-1-1v1 3s-2 2-3 2v-2c0-1 1-3 0-4 0-1-1-3-3-3h0c1-1 1-1 2-1z" class="T"></path><path d="M263 474c-6-2-14-3-21-2-11 2-20 6-26 15 0 1-1 1-1 1 0-1 3-4 3-5 6-7 13-11 22-12 2-1 5 0 8-1v-1c3 1 5 2 8 2h1l-1-2c1-1 7 4 8 5h-1z" class="G"></path><path d="M251 313l1 1v2h2 0c1 0 1-1 2-1 1 2 2 5 4 6h-2c-2 3-3 4-4 7v2l-4-1h0v-4c1-4 1-8 1-12z" class="E"></path><path d="M258 321c-2 3-3 4-4 7v2l-4-1h0v-4l1 1 1-1c1-2 3-3 6-4z" class="N"></path><defs><linearGradient id="E" x1="207.677" y1="592.4" x2="214.998" y2="580.258" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3b3a3b"></stop></linearGradient></defs><path fill="url(#E)" d="M209 584c3-3 7-3 11-4-1 2-3 4-4 6v1c0 1 0 1-1 2l-6 3c-1 0-2 0-3-1v-1c1-2 1-4 3-6z"></path><path d="M313 481l1 4h0c0 1 1 1 1 2 1 2 0 5 0 8-1 0-1 0-2 1l-1 6-2 3h0v-2c-1 0-1-2-2-3v1c-1 0-2 0-2-1h0l-2-2 2-1c0-1 2-2 3-2 3-4 3-9 3-14h1z" class="G"></path><path d="M314 485h0c0 1 1 1 1 2 1 2 0 5 0 8-1 0-1 0-2 1l1-11z" class="C"></path><path d="M245 492c-1-1-2-2-2-3v-1c2 1 3 1 4 1 1 1 1 1 2 1 1-1 1-1 1-3l-2-2h0 2c-2-2-5-3-7-5 3 1 6 1 8 3h1 0c-2-3-7-4-10-5 4 0 7 1 10 4 2 1 2 3 2 5v2h-1c-1 3-2 5-3 8-2-1-2-1-4-1l-1-4z" class="L"></path><path d="M345 477c2 1 5 2 6 4 2 2 3 3 4 5-1 3-1 7-4 9-2 3-4 5-7 7-1 1-3 3-4 5l-1-1c2-2 4-5 6-6 1-1 3-2 4-3 3-3 2-5 2-8h1v-1l-1 1v-1c-2-2-3-4-5-7h0 0c-1-2-2-2-4-2-1 1-2 1-4 1l-1-2c2-1 3-1 5 0h1c1 0 1-1 2-1z" class="E"></path><path d="M346 481h2l1 1h0c2 1 3 1 3 3 0 1-1 1-1 3-2-2-3-4-5-7z" class="D"></path><path d="M329 453c1 1 2 2 3 2 0 2-1 4-2 6l-4 4c-2 2-2 5-3 7 0 1-1 1-1 2h0c-1 0-1-1-1-2h0c0-1 0-2-1-2-1-3-4-3-6-4 1-2 2 0 3-1l1-1c1 0 3 1 4 0l2-1c0-1 1-3 1-4 0-2-1-2-1-3l-1-1h1 2c1 0 2-1 3-2z" class="E"></path><path d="M264 474c1 0 3 1 4 0-1-1-1-1-1-2 1 0 2 1 3 2 2 1 4 1 6 3 1 0 1 1 1 2 1-1 1-1 2-1h2l1-1c0 1 1 2 1 3h1c1 2 0 4 0 6 0 1-2 3-3 3 0 1-1 1-1 1l-3-6c-3-6-8-8-14-10h1z" class="D"></path><path d="M281 478l1-1c0 1 1 2 1 3h1c1 2 0 4 0 6 0 1-2 3-3 3h-1c0-1-1-3-1-4-1 0-1-2-1-3s2-2 3-4h0z" class="C"></path><path d="M303 475c2 1 3 1 4 3v1c1 0 1 0 1 1v1c0 4-1 8-4 11-1 1-4 1-6 1-1 0-2 0-3-1 1 0 2-1 3-1v-1c-1 0-1-1-1-2h0v-1c1-1 1-2 2-3v2 1h1 1v-1c1-1 1-2 1-2 1-1 0-1 1-2v-3h1c0-1 0-1-1-2v-2z" class="F"></path><path d="M301 486c2-2 3-3 4-5v-1c1 1 1 3 1 4-1 3-2 3-3 4-2 1-3 1-4 1v-1h0c1 0 1 0 2-1v-1z" class="U"></path><path d="M313 475c-2-4-4-8-7-12-4-4-9-7-15-9-5-1-11-1-17-1h0c3 0 7-1 10-1l18 1c0 1 0 2 1 3 1 3 3 5 6 7h0c0 1 1 2 2 3l1 1c0-1 0-1 1-1h1l2 5-1 1c-1 0-1-1-1-2h-1v1 4z" class="E"></path><path d="M262 491v1h0l2 3c0 1 1 2 2 3l3 3 1 1h-2 0c1 1 2 2 3 2s1 0 1 1l1-1c0 1 1 3 1 4h0l-1-1c-1-1-2-2-4-2v1h0l-2-2h0v2h-1c0 1 1 2 0 3h0c-2-1-2-2-4-2 0-1 0-2-1-2v-1h0c1-2 3-3 5-4-3 0-6 1-9 2 0-3 0-6-1-8l1-1c1 0 1-1 2-2h0l2 1 1-1z" class="G"></path><path d="M261 497c1-2 1-3 1-5l2 3c0 1 1 2 2 3h-4s0-1-1-1z" class="E"></path><path d="M262 491v1h0c0 2 0 3-1 5 0 0-1 1-2 1v-2c0-2 1-3 2-4l1-1z" class="K"></path><path d="M253 489c1 1 1 1 1 2 1 1 1 2 2 3h0c1 2 1 5 1 8 3-1 6-2 9-2-2 1-4 2-5 4h0v1c1 0 1 1 1 2-1 0-1 0-2-1v-3s1-1 0-2v1h-1-1 0-2c0-1 0-1-1-2h0c0-2 0-3-1-4v-1c0-1 0-1-1-1v1c-1 2 0 2 0 4l-1 1h-1 0c0 1 0 2-1 4l-2-1h0v-2c0-1-1-1-2-2-5-6-13-8-20-8h4c1 0 1-1 1-1 3-1 6 0 8 1s3 2 5 2l1-1 1 4c2 0 2 0 4 1 1-3 2-5 3-8z" class="F"></path><path d="M246 496c2 0 2 0 4 1v6c-1-2-3-5-4-7z" class="G"></path><path d="M247 525l3 2c0 11-2 22 0 33l-4-8c-1-4-4-8-5-12h1c1-2 1-4 1-7l1-1v-3-2c1 0 2-1 3-2z" class="L"></path><path d="M185 529h-1v-1h-1v1h-1 0v-1c1-2 3-3 5-4 2 0 4 0 6 1h0c6 3 8 7 11 12l8 14c4 5 7 11 13 15h-1c-1-1-3-2-4-4-1 0-2-2-3-2l-6-9c-3-4-5-10-9-13-1-1-1-1-2-1v-1l-1-1c-2-2-7-3-10-3h-2 0v-2l-2-1z" class="R"></path><path d="M185 529c1 0 1-1 3-1v1 1h0 0 3l1 1h0c-2 0-4 0-5 1h0v-2l-2-1z" class="D"></path><path d="M185 529h-1v-1h-1v1h-1 0v-1c1-2 3-3 5-4 2 0 4 0 6 1 3 3 6 5 8 9 1 1 2 2 2 3-3-3-10-10-15-9h0c-2 0-2 1-3 1z" class="C"></path><path d="M176 509c6 0 8 2 13 5 2 1 4 2 6 4h0c1 0 1 1 2 2l-1 1 1 1h0l-1-1c-1 0-1 0-1 1h-1v-1c-1 0-1 0-1-1-1 0-2-1-4-1h0-3 0 1v1c-1 0-3 0-4 1l-2 1c0 1 0 1 1 1 0 0-1 0-1 1h-1c0-1-1 0-2 0-1-1-1-1-1-3 1-1 1-2 2-3l1-1c-3 0-5 0-7-2 0-1-1-2-1-2 0-2 2-3 4-4z" class="H"></path><path d="M179 503h1 0v-1c1-1 1 0 3 0 1 1 2 2 4 2l3 3c1 2 2 3 4 4l3 6 5 9c-1 0-1 0-2 1-2-2-2-5-3-7-1-1-1-2-2-2h0c-2-2-4-3-6-4-5-3-7-5-13-5l-1-1s-2-1-3-1c-1-1-1-2-1-3 1 0 1-1 1-1 2-1 5-1 7 0z" class="B"></path><path d="M175 508c1 0 1-1 1-2 5 1 10 4 14 7 2 1 3 2 5 3l1 1h1l5 9c-1 0-1 0-2 1-2-2-2-5-3-7-1-1-1-2-2-2h0c-2-2-4-3-6-4-5-3-7-5-13-5l-1-1z" class="C"></path><path d="M314 466c2 1 5 1 6 4 1 0 1 1 1 2h0c0 1 0 2 1 2h0c0 3 0 7 1 10v2l-1 4h-1v-3l-1-1-1 4h-2c0 1 0 2-1 3l-1 2h0c0-3 1-6 0-8 0-1-1-1-1-2h0l-1-4h-1c0-2 0-4 1-6v-4-1h1c0 1 0 2 1 2l1-1-2-5h0z" class="H"></path><path d="M318 485h1v-1c0-1 1-1 2-2-1 1-1 3-1 4l-2-1z" class="I"></path><path d="M318 485l2 1h0l-1 4h-2c0-1 1-3 1-5zm-5-10v-4-1h1c0 1 0 2 1 2l1-1v6h-1v-1l-1 1h0c0 1 0 1-1 2v2h-1c0-2 0-4 1-6z" class="F"></path><path d="M321 472c0 1 0 2 1 2h0c0 3 0 7 1 10v2l-1 4h-1v-3l-1-1h0c0-1 0-3 1-4v-10z" class="V"></path><path d="M320 486l1 1v3h1l-1 6c-1 2-2 5-3 8h0c0 3-1 4-3 7-1 0-2 1-2 2v1l1 1-8 5h0l-1-1 1-2h-1c-2 1-7 5-9 5-1 1-1 1-2 1v1h-1v-1h0v-1c2-1 4-2 5-3 3-2 6-4 8-6s3-6 4-8l2-3 1-6c1-1 1-1 2-1h0l1-2c1-1 1-2 1-3h2l1-4z" class="M"></path><path d="M314 509v-3c1-2 2-2 4-2h0c-2 2-3 3-4 5z" class="R"></path><path d="M317 490h2 0l-2 6c0-1 0-2-1-3 1-1 1-2 1-3z" class="C"></path><path d="M320 486l1 1v3h1l-1 6c0-1 0-1-1-1v-3-2h-1 0l1-4z" class="R"></path><path d="M316 493c1 1 1 2 1 3l-3 9c-1-1-1-3-2-3l1-6c1-1 1-1 2-1h0l1-2z" class="F"></path><path d="M312 502c1 0 1 2 2 3-3 4-6 9-11 12-2 2-5 3-7 5h0c-1 1-1 1-2 1v1h-1v-1h0v-1c2-1 4-2 5-3 3-2 6-4 8-6s3-6 4-8l2-3z" class="O"></path><path d="M314 509c1-2 2-3 4-5 0 3-1 4-3 7-1 0-2 1-2 2v1l1 1-8 5h0l-1-1 1-2h-1c0-1 3-2 3-3 2-1 4-3 6-5z" class="F"></path><path d="M286 464l5 6c0-5 0-7-4-11l-2-2c9 3 17 9 21 17 1 2 1 3 1 5v-1c-1-2-2-2-4-3-4-1-10-3-13 0-3 1-4 5-4 8-1 3-1 5 0 7v1h-2c-1 1 0 2 0 4-1 1-2 3-3 4v-5l-1-1v-3s1 0 1-1c1 0 3-2 3-3 0-2 1-4 0-6v-1c2-3 3-5 3-8 0-2-1-5-1-7z" class="E"></path><path d="M342 438l1-2c1 2 2 5 4 6v5c-1 3-2 4-3 6-4 6-12 9-17 14-2 2-3 3-3 5h-1c1-2 1-5 3-7l4-4c1-2 2-4 2-6v-5l1-1h0c0-2-1-3-2-4l2 1c2 0 2 0 3 2h1 0v-2c0-1 0-2 1-3h0c1-1 3-4 4-5z" class="B"></path><path d="M342 438c1 2 1 3 1 5h0v1l-2-3c-1 1 0 2-1 3h0c-1 0-1-1-2-1 1-1 3-4 4-5z" class="H"></path><path d="M342 438l1-2c1 2 2 5 4 6v5h-1l-1 1h-1c0-1 0-3-1-4h0v-1h0c0-2 0-3-1-5z" class="F"></path><path d="M254 403c2-1 3-1 4 0 2 1 3 3 4 4l1-1 1-2c0 1 0 3 1 3h0v3c1 1 2 0 3 0 0 1 0 2-1 3l1 1v1l-2-1c0 1 0 3 1 4 0 2-1 3-1 5v1h-1c-1-2-4-4-6-6 4 6 4 10 4 17l-1 7c-1-1 0-2 0-4s-1-4-1-5v-1h-4l-1-2c0-1 0-2-2-3l-3 1h0c2-1 3-2 4-3l1-1c2-5 3-9 1-14l-3-3c1-1 1-1 2-1h3 0c-2-1-3-2-5-3z" class="E"></path><path d="M254 407c1-1 1-1 2-1h3 0c0 1 1 2 1 3-1 0-2 0-3-1l-1-1v-1c0 1 0 2 1 2v2l-3-3z" class="D"></path><path d="M251 428c2-1 3-2 4-3h1 2c0 1 1 1 1 2s1 2 1 3h0-4c0-1 0-2-2-3l-3 1h0z" class="G"></path><path d="M254 403c2-1 3-1 4 0 2 1 3 3 4 4l1-1 1-2c0 1 0 3 1 3v2c-1 2-1 4-2 6h-3v-5-1c0-1-1-2-1-3-2-1-3-2-5-3z" class="F"></path><path d="M264 404c0 1 0 3 1 3v2c-1 1-1 1-2 1 0 0-1 0-1-1v-2l1-1 1-2z" class="I"></path><path d="M308 402c2-5 3-10 7-14 3-3 9-5 14-5 4 0 7 2 9 4v1c0 1 0 1 1 2h0v2h0c-1 0-1 0-2-1 0-1-2-3-2-3l-1-1h0-3c-2 1-2 1-3 3-2-1-3 0-4 0-5 2-8 6-11 10-1 1-3 3-3 5-1 1-1 4 0 6 0 0 0 1 1 1h-1l-1 1c-3 3-6 7-8 11 1-4 3-7 4-12 1-2 1-4 1-7v-3h2zm-82 89c7 0 15 2 20 8 1 1 2 1 2 2v2l-3-2h-1l1 1-1 1c-1-1-1 0-1-1-1-1 0-1 0-2h-1l1 10-2-2v3c0 3 0 6 2 9 1 1 2 4 4 5-1 1-2 2-3 2v2 3l-1 1c-1-3-1-7-2-10-2-3-3-6-5-10v7h0s0-1-1-2c-1-3-1-6-2-9l-1-2h0l1-1v-2h2c0 1 1 2 2 3h1c0-2 0-3-1-5 0-3-3-6-6-8-2-1-5-1-7-1-6 2-9 8-12 12l3-6c3-5 6-7 11-8zm49-136c3 1 5 2 8 4h1l1 1c5 2 10 8 12 12 1 4 3 7 4 11 0 1 1 2 0 4 0 1 0 1 1 2 0 0 0 1 1 1 1 8 1 16-2 24 0 2-1 5-2 7 0 1 0 1-1 2h0l1-2c2-7 3-15 3-23-1-7-4-14-8-21-3-7-10-11-16-15l-5-1-1-1h-1-1l-3-1c-1 1-2 1-4 1 2-1 4-1 5-1h7c-4-1-7-1-10 0l-1-1v-1c2 0 4-1 6 0l1-1h3l1-1z" class="E"></path><path d="M275 355c3 1 5 2 8 4h1l1 1c5 2 10 8 12 12 1 4 3 7 4 11 0 1 1 2 0 4-1-4-3-9-5-12-5-8-13-14-21-16-4-1-7-1-10 0l-1-1v-1c2 0 4-1 6 0l1-1h3l1-1z" class="B"></path><path d="M275 355c3 1 5 2 8 4h-2-1c-2 0-2 0-3-1-3 0-5-1-7-1l1-1h3l1-1z" class="C"></path><path d="M332 416c-4 1-9 3-12 6-5 5-8 10-13 15-2 2-5 3-7 4-5 3-9 5-14 7-3 1-6 2-9 2 2-1 4-1 5-2 6-3 11-6 14-12 2 1 5 1 7 1 1 0 2-1 3-2s2-3 3-4l8-10c-7 2-12 3-17 9-1 2-2 3-3 4 0-2 2-3 3-5 4-5 9-10 14-15l4 1c5-1 9-2 13-5h0v1c0 1-1 2-1 3h0c0 1 0 1 1 1 0 0 1 0 1 1z" class="J"></path><path d="M320 443c-3-1-7-3-10-1s-6 4-7 7l-12 1c2-1 5-1 7-2 12-5 18-16 26-24 4-4 9-6 15-6 4 1 6 3 8 6h0c-1 0-2-1-3-2-1 0-6-2-7-1h-1s-1 0-1-1c-1 0-3 0-4 1 0 1 1 1 1 2 1 1 2 2 3 2v1c-3 0-5 1-7 2s-3 2-4 5c0 2 0 6 2 8 1 1 2 1 3 2h-1-1v4l-1-3v-1h-5-1z" class="G"></path><path d="M187 532h2c3 0 8 1 10 3l1 1v1c1 0 1 0 2 1 4 3 6 9 9 13l6 9h-1l-7-6h0c-3-2-6-4-8-5-5-2-8-5-10-8h-4c-2-1-2-2-3-3h0-9l5-2c2 0 3 0 5-1v-1c0-2 1-2 2-2h0z" class="H"></path><path d="M191 541c1-1 2-1 3 0 1 0 3 0 4 1l-1 1v1h0-1v1h1c0 1 1 1 2 2 1 0 1 1 2 2-5-2-8-5-10-8zm-2-9c3 0 8 1 10 3l1 1v1c1 0 1 0 2 1h-2c-4-1-6-1-10 0 1-1 2-2 1-4 0-1-1-2-2-2z" class="F"></path><path d="M335 479c1 0 1 0 2-1l1 2c2 0 3 0 4-1 2 0 3 0 4 2h0 0c2 3 3 5 5 7v1l1-1v1h-1c0 3 1 5-2 8-1 1-3 2-4 3-2 1-4 4-6 6l1 1-6 12c1-1 3-3 5-4v1c-2 1-3 2-4 4-5 7-5 16-9 24h0 0l3-13 2-7c1-4 2-7 4-10 2-5 3-11 3-17 0-3-1-6-1-9 0-1 0-1-1-1-1-1 0-5 0-7l-1-1z" class="B"></path><path d="M335 479c1 0 1 0 2-1l1 2c2 0 3 0 4-1 0 2 0 3-1 5v-1c-1-1-2-1-3-2 0 1-1 1-1 1 0 2 0 4 1 6h-1c0-1 0-1-1-1-1-1 0-5 0-7l-1-1z" class="D"></path><path d="M338 488c0 2 0 4 1 6s1 5 1 7c0-2 1-3 1-5 1 0 2 1 2 2h1-1c-1 1-2 2-2 3l-1 1c0 2-3 5-2 6l1-2 1 1-6 12c1-1 3-3 5-4v1c-2 1-3 2-4 4-5 7-5 16-9 24h0 0l3-13 2-7c1-4 2-7 4-10 2-5 3-11 3-17 0-3-1-6-1-9h1z" class="M"></path><path d="M332 477h3v2l1 1c0 2-1 6 0 7 1 0 1 0 1 1 0 3 1 6 1 9 0 6-1 12-3 17-2 3-3 6-4 10h-1 0l-1-2c-1-3 0-6-1-9h1c1 0 1-1 1-1 0-2-1-2-2-3h-3-1v-1c1-1 2-2 2-4h-1v-1l2-2c0-1 1-1 1-1l-1-1h0 1 1v-1c0-1-1-2-1-4l1-1h-1c-1 1 0 2 0 3-1 0-1-2-1-3 1-4 1-10 3-13 0-1 1-2 2-3z" class="B"></path><path d="M326 504h-1v-1l2-2c2 1 3 3 4 4v1c-1-1-2-2-3-2h-2zm4 8c2 2 0 7-1 10-1-3 0-6-1-9h1c1 0 1-1 1-1z" class="M"></path><path d="M332 477h3v2l1 1c0 2-1 6 0 7 0 4 1 8 1 12-1-2-1-4-3-6h0c0 1-1 2-1 3 1 1 1 1 1 2-1-2-3-3-5-5h-1c-1 1 0 2 0 3-1 0-1-2-1-3 1-4 1-10 3-13 0-1 1-2 2-3z" class="J"></path><path d="M260 388c1 0 1 0 1 1 1 1 1 2 2 2v3h1s0-1 1-2h1c0 1 1 2 1 2h2c0 3 0 5-1 7l1 1c2-1 3-3 4-4 1 1 1 3 1 4 1 1 2 2 4 2 1 0 0-2 1-4v-1-1c0-2 0-3 2-4v-1l1 1c-1 1-1 1-1 3 1 2 10 5 9 8h0c-3-2-6-3-9-5v2c1 1 2 1 2 2 1 0 1 0 1 1h1c2 2 3 5 4 7-2-2-3-4-5-5h0c0 2 2 4 3 5 2 3 2 6 2 8 1 3 1 6 0 9 0 1-1 5-1 6l-3 4c-7 12-27 15-41 18 0 0 5-1 5-2 12-3 28-6 35-18 4-7 3-14 1-21 0 5-1 9-2 13h-1v-1c1-3 1-8 0-11v-5-4h0c-1 2-2 3-2 5h0c-1-2-3-4-5-5-1-1-3-2-5-3v-3c-1 0-2 0-2 1h-1-2c1-1 1-2 1-3s0-2-1-2h-1v1c0 1-1 2-2 2-1-1-1-2-2-3h0 0l-1-1h1c1-1 2-1 2-2h-1 0-1c0-2 0-2-1-3h0v-1c1-1 1-1 1-3z" class="E"></path><path d="M233 509l-1 4h-1c-2 0-3-3-4-4h-1s-1 1-1 2l2-1v1c0 1 0 2-1 3h-3c1 1 0 3 0 5-2-1-2-3-4-4-1 1-1 1-1 2l3 3s1 0 0 1c-1 0-2 0-3-1v1c0 2 1 4 2 6h0v1c0 2 1 4 2 5l3 3h-2 0 0c0 1 1 2 2 3h2c2 0 4 2 6 3h5c4 6 6 13 10 19 1 2 3 4 4 7-6-7-13-12-19-17-3-2-4-5-7-7-2-2-4-3-6-5-3-6-5-14-3-21 1-5 4-10 8-13 3-1 6-2 8-1v2l-1 1h0l1 2z" class="L"></path><path d="M292 497l-1-1c3 1 5 2 8 2 1 0 2-1 3 0v1h1l1-2h2l-2 1 2 2h0c0 1 1 1 2 1v-1c1 1 1 3 2 3v2h0c-1 2-2 6-4 8s-5 4-8 6c-1 1-3 2-5 3-5 3-12 4-18 6h0 0c0-2 1-3 2-4h1c5-2 12-5 15-9-1-1-1-2-1-4l-2-2h0v-3-3-1-1l1-1-1-1v-1h0v-1h-1v-1c1 0 2 1 2 2 1 0 1 1 1 1v-2z" class="D"></path><path d="M306 500c0 1 1 1 2 1v-1c1 1 1 3 2 3v2c-2 2-3 5-5 7-2 3-5 5-8 7-5 2-8 4-14 5h0c5-2 11-4 15-9 1-1 2-2 2-3v-1c1 0 1-1 2-1v-1h1c-1-1-1-2-1-3h0v-1h1c1-2 2-3 3-5z" class="I"></path><path d="M303 505c1 0 1 0 2 1l-2 3h0 0c-1-1-1-2-1-3h0v-1h1z" class="S"></path><path d="M306 500c0 1 1 1 2 1l-3 5c-1-1-1-1-2-1 1-2 2-3 3-5z" class="R"></path><path d="M292 497l-1-1c3 1 5 2 8 2 1 0 2-1 3 0v1h1l1-2h2l-2 1 2 2h0c-1 2-2 3-3 5h-1v1h0c0 1 0 2 1 3h-1v1c-1 0-1 1-2 1h-2c-1 3-4 5-6 7 1-2 2-4 2-7h0c0 1-1 2-1 4-1-1-1-2-1-4l-2-2h0v-3-3-1-1l1-1-1-1v-1h0v-1h-1v-1c1 0 2 1 2 2 1 0 1 1 1 1v-2z" class="H"></path><path d="M300 504h2v1 1h0c0 1 0 2 1 3h-1c-1 0-1 0-2-1v-4z" class="C"></path><path d="M290 502c1 1 1 2 2 3v1c1 2 3 2 2 5 0 1-1 2-1 4-1-1-1-2-1-4l-2-2h0v-3-3-1z" class="O"></path><path d="M292 497l-1-1c3 1 5 2 8 2 1 0 2-1 3 0v1h1l1-2h2l-2 1 2 2h0c-1 2-2 3-3 5h-1v-1h-2v-2c-2 0-3-1-3-2h3v-1c-3 0-5-1-8-2z" class="D"></path><path d="M300 502c2 0 3-1 6-2-1 2-2 3-3 5h-1v-1h-2v-2z" class="F"></path><defs><linearGradient id="F" x1="295.711" y1="417.307" x2="277.956" y2="413.799" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272627"></stop></linearGradient></defs><path fill="url(#F)" d="M281 393c2-1 6-1 8-1 3 1 6 4 7 6 4 6 3 16 2 22-1 4-4 12-7 14h0l-2 2 1 1-2 1v-1-1-1c0-1 1-5 1-6 1-3 1-6 0-9 0-2 0-5-2-8-1-1-3-3-3-5h0c2 1 3 3 5 5-1-2-2-5-4-7h-1c0-1 0-1-1-1 0-1-1-1-2-2v-2c3 2 6 3 9 5h0c1-3-8-6-9-8 0-2 0-2 1-3l-1-1z"></path><path d="M281 393c2-1 6-1 8-1 3 1 6 4 7 6 4 6 3 16 2 22-1 4-4 12-7 14 4-7 6-15 6-23-1-6-2-12-7-17h0l1 3h-1c-3-2-5-3-8-3l-1-1z" class="J"></path><path d="M242 374c1-3 2-6 4-8 5-6 11-8 18-9v1l1 1c3-1 6-1 10 0h-7c-1 0-3 0-5 1 2 0 3 0 4-1l3 1h1 1l1 1 5 1v2h-2-1c-7-2-12-1-18 4-2 1-4 2-4 4v1h0v3c1 1 1 1 2 1 2 0 2 0 3-1v1l-1 5h-1c-1 2-2 3-3 5 0 1-1 2-2 2h0l-1 1c-1-1-2-1-2-2-1 1-1 1-2 1v-1c-1-1-1-1-3-1l-2-1v-1-1h1v-4-1-5z" class="S"></path><path d="M242 374c1-3 2-6 4-8 5-6 11-8 18-9v1l1 1c-7 1-14 3-18 9-1 2-2 5-3 8-1 0-1 3-1 3h-1v-5z" class="I"></path><path d="M246 375c1-4 3-8 6-10 6-5 14-5 21-4l5 1v2h-2-1c-7-2-12-1-18 4-2 1-4 2-4 4-1 1-2 2-2 3h-2-3z" class="B"></path><path d="M253 372v1h0v3c1 1 1 1 2 1 2 0 2 0 3-1v1l-1 5h-1c-1 2-2 3-3 5 0 1-1 2-2 2h0l-1 1c-1-1-2-1-2-2-1 1-1 1-2 1v-1c-1-1-1-1-3-1l-2-1v-1-1h1v-4l1 3c1-1 2 0 3-1v-5-2h3 2c0-1 1-2 2-3z" class="E"></path><path d="M251 389c1-2 1-6 3-8l2 1c-1 2-2 3-3 5 0 1-1 2-2 2h0zm-5-12c1 1 3 2 3 3 1 1 1 5 0 5 0 1 0 2-1 3h0c-1 1-1 1-2 1v-1c-1-1-1-1-3-1l-2-1v-1-1h1v-4l1 3c1-1 2 0 3-1v-5z" class="C"></path><path d="M242 384c1 0 1 0 2 1l1-1v2l-2 1-2-1v-1-1h1z" class="E"></path><path d="M320 512c-1 0-1 0-1-1h1c1-2 2-3 4-3v1h1 3c1 1 2 1 2 3 0 0 0 1-1 1h-1c1 3 0 6 1 9l1 2h0 1l-2 7-3 13h0c0 1-1 1-1 1 0 2 0 3-2 4v1h0c0-2 0-3-1-5 0-4 0-8-3-11 0-1-1-2-2-3l-6-6h1c-1-1-1-1-2-1h0l1-1h0 0 0c-2-1-2-3-5-3l8-5-1-1v-1l3-3 1 2-1 1h1c1 0 2-1 3-1z" class="B"></path><path d="M327 518c1-2 1-3 1-4v-1c1 3 0 6 1 9l1 2h0 1l-2 7-1-1v-2-6c-1-1 0-2-1-3v-1z" class="C"></path><path d="M320 512c-1 0-1 0-1-1h1c1-2 2-3 4-3v1h1 3c1 1 2 1 2 3 0 0 0 1-1 1h-1v1c0 1 0 2-1 4v-1c0-1-1-2-2-2v1h-1v-2c-1 0-1-1-2-1h-1-1v-1z" class="S"></path><path d="M313 513l3-3 1 2-1 1h1c1 0 2-1 3-1v1h1l2 4c-1-1-2-2-3-2h-1c1 0 1 1 1 2s0 2 1 3h-1 0c-1-1-2-2-4-2s-3 1-4 2c0 1 4 3 5 4 3 3 4 5 5 8 0 1 1 3 1 4-2-4-3-5-6-7-1-1-2-2-3-2l-2-2c-1-1-1-1-2-1h0l1-1h0 0 0c-2-1-2-3-5-3l8-5-1-1v-1z" class="D"></path><path d="M313 513l3-3 1 2-1 1h1c1 0 2-1 3-1v1c-1 0-3 0-4 1-1 0-1 1-2 1l-1-1v-1z" class="N"></path><path d="M312 525c-1-1-1-1-2-1h0l1-1h0 0l3 1h0c1 0 2 2 3 3l1 1-1 1c-1-1-2-2-3-2l-2-2z" class="J"></path><path d="M250 329l4 1c3 0 6-1 9-1 4 2 8 3 12 5 8 5 15 15 19 24l4 10s0 1-1 1h0c-2-4-5-7-8-10h-1c-1 0-2 0-4-1h0c-4-3-8-5-12-6-2-1-5-1-6-2-3-2-5-5-7-8 0-1-1-2-1-3-1 0-4 0-5-1-2-2-3-6-3-9z" class="L"></path><path d="M267 348c-1 0-1 0-2-1s-1-3 0-4 3-1 5-1c1 2 1 3 2 5h-5v1z" class="Q"></path><path d="M268 339c-2 0-9 0-11-1l1-1c0-2-1-3-2-5 3 0 7 0 10 1-1 1-1 3 0 5h0l2 1z" class="U"></path><defs><linearGradient id="G" x1="281.148" y1="340.145" x2="267.836" y2="337.316" xlink:href="#B"><stop offset="0" stop-color="#3a3838"></stop><stop offset="1" stop-color="#656565"></stop></linearGradient></defs><path fill="url(#G)" d="M266 333c8 2 14 7 19 15h0 0-1 0c-3-5-10-8-16-9l-2-1h0c-1-2-1-4 0-5z"></path><defs><linearGradient id="H" x1="290.136" y1="351.131" x2="271.977" y2="356.302" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#363539"></stop></linearGradient></defs><path fill="url(#H)" d="M270 342l5 1c3 1 6 3 9 5h1c5 6 10 13 12 21h0c-2-4-5-7-8-10-4-5-9-8-15-10-2 0-5 0-7-1v-1h5c-1-2-1-3-2-5z"></path><path d="M270 342l5 1h0c-1 2-1 3-1 4s0 1-1 1l-1-1h0c-1-2-1-3-2-5z" class="F"></path><path d="M247 325c1-2 2-5 3-8 0-4 1-7 1-10 1-1 1-2 1-3h2c-1 2-2 3-3 5v4c0 4 0 8-1 12v4h0c0 3 1 7 3 9 1 1 4 1 5 1 0 1 1 2 1 3 2 3 4 6 7 8 1 1 4 1 6 2 4 1 8 3 12 6h0c2 1 3 1 4 1h1c3 3 6 6 8 10v3c-2-4-7-10-12-12l-1-1h-1c-3-2-5-3-8-4l-1 1h-3l-1 1c-2-1-4 0-6 0-7 1-13 3-18 9-2 2-3 5-4 8v5 1 4h-1v1 1l2 1c2 0 2 0 3 1v1c1 0 1 0 2-1 0 1 1 1 2 2l1-1h0c1 0 2-1 2-2 1-2 2-3 3-5h1l-1 4v4c1 1 2 1 3 1v1h0c1 1 1 1 1 3h1 0 1c0 1-1 1-2 2h-1l1 1h0 0c1 1 1 2 2 3 1 0 2-1 2-2v-1h1c1 0 1 1 1 2s0 2-1 3h0c-1 0-1 0-2-1 0 1 0 1 1 2l-1 2-1 1c-1-1-2-3-4-4-1-1-2-1-4 0 2 1 3 2 5 3h0-3c-1 0-1 0-2 1l3 3c2 5 1 9-1 14l-1 1c-1 1-2 2-4 3-4 1-7 1-11 1-1 1-2 2-3 4h1l-1 2c2 0 4 0 5 1 0 1 1 2 0 3-1 3-4 4-7 6-4-1-6-2-9-5-6-6-7-13-7-21 2 4 3 7 7 9 3 2 8 0 11-1s5 1 8 0 6-4 7-7c1-2 1-4 0-7-3-6-9-8-13-11-5-4-9-9-11-14-3-7-2-14 1-22l-1-1c0-1 1-1 1-2 2-2 3-4 4-7 0-2 2-4 3-6v-1l8-16c-4 5-8 8-13 13v-2c5-4 10-8 13-14 1-2 2-4 3-5h0z" class="B"></path><path d="M252 348l1-3c2 2 3 4 6 5-2 1-3 1-5 0 0-1-1-1-2-1v-1zm2 59c-3-4-8-4-12-7l12 3c2 1 3 2 5 3h0-3c-1 0-1 0-2 1z" class="M"></path><path d="M260 398c1 1 1 2 2 3 1 0 2-1 2-2v-1h1c1 0 1 1 1 2s0 2-1 3h0c-1 0-1 0-2-1 0 1 0 1 1 2l-1 2c-2-2-2-4-5-5-1 0-2 0-3-1s-4-1-5-1h0c3 0 7 1 11 1 0-1 0-1-1-2z" class="C"></path><path d="M238 394c3 2 6 3 9 3 1 1 5 1 6 1 3-1 5-1 7-3h1 0 1c0 1-1 1-2 2h-1l1 1h0 0c1 1 1 1 1 2-4 0-8-1-11-1-6 0-9-1-13-4l1-1z" class="D"></path><path d="M231 344c5-4 10-8 13-14 1-2 2-4 3-5h0c-2 9-4 18-9 25-3 6-7 11-9 16l-1-1c0-1 1-1 1-2 2-2 3-4 4-7 0-2 2-4 3-6v-1l8-16c-4 5-8 8-13 13v-2z" class="M"></path><path d="M252 348v1c1 0 2 0 2 1 2 1 3 1 5 0 5 1 10 2 15 4 2 0 4 1 5 2 2 1 4 2 5 2h0c2 1 3 1 4 1h1c3 3 6 6 8 10v3c-2-4-7-10-12-12l-1-1h-1c-3-2-5-3-8-4-5-1-11-2-16-2-4 0-8 0-12-1l1-1h-1 2s1 0 2-1c1 0 1-1 1-2z" class="G"></path><path d="M237 395c-1-2-3-4-4-6-6-11-2-23 4-32 3-4 6-6 7-10v-1l1 1 3 3 1 1h-2 1l-1 1c4 1 8 1 12 1 5 0 11 1 16 2l-1 1h-3l-1 1c-2-1-4 0-6 0-7 1-13 3-18 9-2 2-3 5-4 8v5 1 4h-1v1 1l2 1c2 0 2 0 3 1v1c1 0 1 0 2-1 0 1 1 1 2 2l1-1h0c1 0 2-1 2-2 1-2 2-3 3-5h1l-1 4v4c1 1 2 1 3 1v1h0c1 1 1 1 1 3-2 2-4 2-7 3-1 0-5 0-6-1-3 0-6-1-9-3l-1 1z" class="S"></path><path d="M241 391c3 1 6 1 9 1 2-1 4-1 6-1-1 0-1 1-1 1s-1 1-2 1l-5 2h0v-1c-1-1-2-1-4-1-1 0-2-1-3-2z" class="F"></path><path d="M235 365v-1h0c0-3 5-9 7-11 1-1 3-1 5-1 4 1 8 1 12 1-1 1-2 1-3 2h0-5l-6 2h-1v1h2-1c-1 1-2 0-3 1-1 0-2 1-3 2h-1s0 1-1 2l-2 2zm3 29c-2-2-3-3-4-5-3-5-3-12-2-17l1-2h0c-1 4-1 8 0 12s4 7 8 9h0c1 1 2 2 3 2 2 0 3 0 4 1v1h0l5-2c1 0 2-1 2-1s0-1 1-1v-1c1 1 2 1 3 1v1h0c1 1 1 1 1 3-2 2-4 2-7 3-1 0-5 0-6-1-3 0-6-1-9-3z" class="H"></path><path d="M256 390c1 1 2 1 3 1v1h0c1 1 1 1 1 3-2 2-4 2-7 3-1 0-5 0-6-1 1 0 1 0 1-1h-4c-2-1-2-1-3-1v-1c1 0 2 0 3 1h4 0l5-2c1 0 2-1 2-1s0-1 1-1v-1z" class="C"></path><path d="M256 390c1 1 2 1 3 1v1h0c-1 2-4 4-7 4 0 0-1 0-2-1h-1-1l5-2c1 0 2-1 2-1s0-1 1-1v-1z" class="M"></path><path d="M259 353c5 0 11 1 16 2l-1 1h-3l-1 1c-2-1-4 0-6 0-7 1-13 3-18 9-2 2-3 5-4 8v5 1 4h-1v1 1l2 1c2 0 2 0 3 1v1c-1 0-3 1-4 0-3 0-5-3-7-5-3-6-1-11-1-17h0c1-1 1-2 1-2l2-2c1-1 1-2 1-2h1c1-1 2-2 3-2 1-1 2 0 3-1h1-2v-1h1l6-2h5 0c1-1 2-1 3-2z" class="B"></path><path d="M240 375c1-1 1-2 1-2l1 1v5 1 4h-1v1 1c-1 0-2-1-3-1l2-1c0-3-1-6 0-9z" class="D"></path><defs><linearGradient id="I" x1="243.662" y1="363.946" x2="246.723" y2="368.635" xlink:href="#B"><stop offset="0" stop-color="#c0c1bf"></stop><stop offset="1" stop-color="#dedadf"></stop></linearGradient></defs><path fill="url(#I)" d="M259 353c5 0 11 1 16 2l-1 1h-3l-1 1c-2-1-4 0-6 0-7 1-13 3-18 9-2 2-3 5-4 8l-1-1s0 1-1 2l1-3c1-6 3-10 9-13h0v-1h1l1-1c-1 0-3 0-4 1h-2 0-2v-1h1l6-2h5 0c1-1 2-1 3-2z"></path><path d="M259 353c5 0 11 1 16 2l-1 1h-3c-5-1-11-1-16 1-1 1-4 2-5 2h0v-1h1l1-1c-1 0-3 0-4 1h-2 0-2v-1h1l6-2h5 0c1-1 2-1 3-2z" class="I"></path><path d="M204 415h1c2-1 5-3 7-4-4 4-8 7-8 13 1 2 2 5 3 7 2 1 4 1 5 2 1-1 2-1 2-2 1-1 1-3 1-4 1-4 3-7 5-10h1-1c0 2 1 4 3 6 1 2 5 3 8 3 2 0 4 0 6-1v-3h1c0 1 0 2 2 3 2 0 4 1 6 0s4-3 5-5 1-4 0-6c0-1 0-1 1-1 1 3 1 5 0 7-1 3-4 6-7 7s-5-1-8 0-8 3-11 1c-4-2-5-5-7-9 0 8 1 15 7 21 3 3 5 4 9 5 3-2 6-3 7-6 1-1 0-2 0-3-1-1-3-1-5-1l1-2h-1c1-2 2-3 3-4 4 0 7 0 11-1h0l-2 1v1c1 4 1 9-1 12l-2 4-1-13 1-1c0 1 1 2 1 3v-1c0-2-1-3-3-5v7c0 6-2 12-6 16-2 2-4 4-7 6 0 1-1 1-2 2 2-2 3-4 5-5 3-3 4-7 6-11-3 2-5 3-8 4l-11 8c-3 2-7 4-9 7-1 2-2 4-3 7-3 5-5 10-6 15-3 8-4 19-3 27 1 4 3 8 3 12l-2-5-2 1c1 2 4 6 4 8l-1-2-5-9-3-6c-2-1-3-2-4-4 0-2-2-4-2-5v-1c1 0 1 1 1 1l1 1h1c-3-7-10-16-18-20h1l-1-2h0c0-1-1-2-2-3h2c1 2 2 3 4 4 0 0 0-1-1-1v-1l1 1 1-1-2-1c-1-1-2-2-2-4v-4l4-4h0l3 2v1h-1l-1-1v3 1h2c1 0 1 2 2 1s1-2 1-2v-2-1h1 0c1-1 1-3 2-4 0-2-1-2-2-3v-1l3-1c0-2 0-5-1-6-1-2-2-3-2-4v-1h1c3-3 5-5 6-9 1-2 1-4 1-6 1-2 0-7 2-9 3-3 7-5 10-9l-1-1h0z" class="J"></path><path d="M187 476v5c2 2 2 5 3 7h0l1 1c-1 1 0 1 0 2 0 2 0 5-1 5-2-6-4-12-3-20z" class="O"></path><path d="M184 472v3 1c0 1 1 3 0 4-2-1-3-2-4-3v-1h1 0l-1-2h-2v2h-1v-1c-1-1 0-3 0-5 0 0 1-1 2-1h0v3 1h2c1 0 1 2 2 1s1-2 1-2z" class="D"></path><path d="M203 421h0c0 6-3 9-7 12v-5c1-3 4-5 7-7zm-12 68h0v-7c0 3 0 5 1 8v3 2h1v-3h0l1-3v8s0 1 1 2v4h0c-1 1-1 3-1 4-2-4-3-6-5-9l1-1 1 1-1-2c1 0 1-3 1-5 0-1-1-1 0-2z" class="C"></path><path d="M178 480c2 1 5 3 7 5 2 4 3 9 5 12l-1 1c-3-6-8-11-12-16 0 0 0-1-1-1v-1l1 1 1-1z" class="I"></path><path d="M195 499v1l2 2c-1-2-1-4 0-5v-1c0 6 1 11 2 16l2 5v2l-2 1c-2-4-4-9-5-13 0-1 0-3 1-4h0v-4z" class="F"></path><defs><linearGradient id="J" x1="197.965" y1="501.671" x2="194.687" y2="483.675" xlink:href="#B"><stop offset="0" stop-color="#242224"></stop><stop offset="1" stop-color="#414242"></stop></linearGradient></defs><path fill="url(#J)" d="M194 489c0-2 2-5 3-7 2-2 4-3 6-4-1 2-3 4-4 6-1 4-2 8-2 12v1c-1 1-1 3 0 5l-2-2v-1c-1-1-1-2-1-2v-8z"></path><path d="M189 464c1-1 1-3 2-5h1c0 1 0 2-1 3 0 1-1 5-1 6v1c1-1 1-2 1-2h1l1-2v2l1 1c-3 7-4 13-4 20h0c-1-2-1-5-3-7v-5c0-4 1-8 2-12z" class="I"></path><path d="M173 478c1 2 2 3 4 4 4 5 9 10 12 16 2 3 3 5 5 9 1 4 3 9 5 13 1 2 4 6 4 8l-1-2-5-9-3-6c-2-1-3-2-4-4 0-2-2-4-2-5v-1c1 0 1 1 1 1l1 1h1c-3-7-10-16-18-20h1l-1-2h0c0-1-1-2-2-3h2z" class="V"></path><path d="M188 501c1 0 1 1 1 1l1 1h1l3 8c-2-1-3-2-4-4 0-2-2-4-2-5v-1z" class="O"></path><path d="M189 464l3-19c0-3 1-7 3-10 3-1 6-4 8-5v-1l3 6-1 1c-6 5-11 12-13 20 0 1-1 2-1 3-1 2-1 4-2 5zm2 18c1-4 2-8 4-11s6-9 10-9c2 0 4 0 5-2 1 0 1 0 1-1h0c0 2-1 3-2 4h-1c-1 1-1 2-1 3v1c1 1 1 2 0 3 0 1-1 3-2 4l-4 4c-7 3-8 7-8 14v3h-1v-2-3c-1-3-1-5-1-8z" class="H"></path><path d="M192 459c1-4 3-8 5-11 4-7 11-12 20-14 1 0 3 2 4 2 3 2 5 5 6 8 0 1 0 3-1 4-1 2-4 3-6 4-10 5-21 6-26 16l-1-1v-2l-1 2h-1s0 1-1 2v-1c0-1 1-5 1-6 1-1 1-2 1-3z" class="B"></path><path d="M221 446c0-1 0-3 1-4v-1c1 2 1 2 1 4 0 0-1 1-2 1z" class="T"></path><defs><linearGradient id="K" x1="205.59" y1="440.725" x2="208.711" y2="457.378" xlink:href="#B"><stop offset="0" stop-color="#222121"></stop><stop offset="1" stop-color="#545354"></stop></linearGradient></defs><path fill="url(#K)" d="M191 467l1-1c0-2 1-4 1-5 3-7 6-12 11-16 1-1 3-2 5-3 2-2 6-4 10-4 2 0 2 2 3 3v1c-1 1-1 3-1 4s-1 1-2 1c-1 1-2 2-3 2l-1-1v1c-1 0-3 1-4 1h-1v1c-2 1-6 3-9 4-4 3-6 5-8 10l-1 2h-1z"></path><path d="M218 445l-1-1c0-1 0-2 1-3s1-1 3-1c-1 2-2 2-2 3v2h-1 0z" class="H"></path><path d="M201 455v-1c1-1 6-4 9-5v2c-2 1-6 3-9 4z" class="K"></path><path d="M210 450h1c0-2 2-3 3-3 2-1 2-2 4-2h0c0 1 0 2 1 2-1 1-2 2-3 2l-1-1v1c-1 0-3 1-4 1h-1z" class="P"></path><path d="M263 522h1v1c-1 3-3 5-5 8-3 4-4 7-6 11 0 3-1 6-1 9v1 5c0 6 2 12 5 18 5 8 12 14 21 16 8 2 19 2 26-2 2-2 5-4 6-6 2-2 4-5 5-8l8-18h1c0 3-1 6-2 9-1 11 4 23 9 32v1c-7 4-14 8-22 11-31 13-68 15-100 6-7-2-14-4-21-7-3-1-5-3-8-4-3-2-6-3-9-6h0c-2 0-5 2-7 4 1-2 4-3 4-5v-2c-1 0-1 0-1-1s0-1-1-2c0 0 1 0 1-1 2-1 4-4 5-6 6-6 13-6 21-7-1 1-2 2-3 2h1c-1 1-1 2-1 2h0c-2 1-4 4-6 6 0 1-1 1-2 2l1 1-1 1c-1 2-3 3-3 5l1 1c1-1 2-1 3-2h0c0 3 0 6-1 8 3-1 7-4 9-7 2-2 4-6 3-9h0c3-1 6-1 9-2 2-1 4-3 5-4l1 1c-2 2-2 4-3 6v1c1 1 2 1 3 1l6-3c-1 3-2 6-3 8l-4 13c3-4 5-8 7-12 2 2 3 4 7 5 2 1 5 1 8 0 1 0 1 1 1 1h1c1-2 1-4 1-6v-1l1-1c2-4 7-7 11-8 3-1 7-1 10 0h0c-5 0-11 0-15 4-2 2-3 6-3 9 0 2 0 4 2 5 1 1 4 2 6 1 2 0 4-2 6-3-2-2-4-3-4-6 0-2 1-4 3-6 1-1 3-1 4-2 2 0 7 0 9 1v1c2 0 3 0 4 1l11 2v-1c1 1 3 1 4 1h0c2 0 3 0 5-1-3 0-6-1-9-1-6-2-12-5-16-10-3-4-7-9-9-13-3-9-2-18-1-28l1-14h1 1c3-1 5-3 8-6z" class="L"></path><path d="M182 591l1 1-1 1h-1l-3 2v1 1c-1 1 0 2-1 3h-1v-1h-2c0-1 1-2 1-3 2-2 5-3 7-5z" class="D"></path><path d="M287 594c4 0 9 0 13-1-3 2-7 3-11 4-1 0-2 1-3 1v-1c-1-1-4-1-5-1h-1c-1 0-2 0-3-1h1v-1c1 1 3 1 4 1h0c2 0 3 0 5-1z" class="F"></path><path d="M254 590c2 0 7 0 9 1v1c2 0 3 0 4 1h-3c-3 0-4 2-6 4-1 1-2 2-3 2s-3 0-4-1-1-1-1-2c1-3 3-4 5-5l-1-1z" class="I"></path><path d="M174 599h-2c0-1 1-2 2-3 0-1-1-3-2-5 1-1 1-3 2-4 4-5 10-5 16-6h1c-1 1-1 2-1 2h0c-2 1-4 4-6 6 0 1-1 1-2 2-2 2-5 3-7 5 0 1-1 2-1 3zm32-9v1c1 1 2 1 3 1-1 0-2 0-3 1 1 2 1 3 1 4 0 3-1 4-3 5v1h0c-1 2-1 3-2 5 0-2 0-3 1-5-3 1-4 2-6 4v-4h-1l-2 2c-1 0-2 1-3 0h0c1-1 3-2 4-3-1-1-2-1-4-1h0c3-3 5-7 7-8 1-1 2-1 3-1 2 0 3 0 4-2h1z" class="B"></path><defs><linearGradient id="L" x1="272.555" y1="594.916" x2="272.364" y2="604.883" xlink:href="#B"><stop offset="0" stop-color="#050404"></stop><stop offset="1" stop-color="#2e2d2f"></stop></linearGradient></defs><path fill="url(#L)" d="M286 598c-1 0-3 0-4 1-4 2-6 7-10 10h0l2-3c1-1 4-6 4-8h-1c-2 0-4 3-5 5-1 1-3 1-4 2l-3 3h-1c0-2 1-2 1-3-3 1-6 2-9 2h-1c2-1 4-1 6-2 1 0 3-1 3-2-1 0-2 1-3 1s-2-1-3-2l2-1-1-1c1-2 3-3 4-4 4-2 10-2 14-1h0c1 1 2 1 3 1h1c1 0 4 0 5 1v1z"></path><path d="M286 490c2 3 3 7 4 11v1 1 3 3h0l2 2c0 2 0 3 1 4-3 4-10 7-15 9h-1c-1 1-2 2-2 4h0 0c6-2 13-3 18-6v1h0v1h1v-1c1 0 1 0 2-1 2 0 7-4 9-5h1l-1 2 1 1h0c3 0 3 2 5 3h0 0 0l-1 1h0c1 0 1 0 2 1h-1l6 6c1 1 2 2 2 3 3 3 3 7 3 11 1 2 1 3 1 5h0v-1c2-1 2-2 2-4 0 0 1 0 1-1h0 0 0l1 2-4 11-8 18c-1 3-3 6-5 8-1 2-4 4-6 6-7 4-18 4-26 2-9-2-16-8-21-16-3-6-5-12-5-18v-5-1c0-3 1-6 1-9 2-4 3-7 6-11 2-3 4-5 5-8v-1h-1c1-2 1-5 1-8v-1c0-2 1-3 2-4s0-2 0-3h1v-2h0l2 2h0v-1c2 0 3 1 4 2l1 1h0c0-1-1-3-1-4 1-2 4-4 5-5 1-2 1-4 3-5v5c1-1 2-3 3-4 0-2-1-3 0-4h2v-1z" class="B"></path><path d="M289 505c-2 0-3 2-4 3l-1-1v-1l3-3h3v3h0l-1-1z" class="G"></path><path d="M289 505l1 1h0v3c-1 3-3 6-6 9 0-2 1-2 1-3h1c-2 1-2 2-4 2h0c4-3 5-7 7-12z" class="D"></path><path d="M280 553h1v1c-2 0-2 0-3 1-3 2-3 2-3 5h1 0c1-2 0-2 2-3h0v2h0c-1 0-1 2-3 3-1-1-1-2-1-3v-2l-2 2-1-1c0-1 2-2 3-3s3-2 4-2h2z" class="O"></path><path d="M269 559h-1v-1c1-1 2-3 3-4 1-2 3-3 6-3 1 1 2 1 3 2h-2c-1 0-3 1-4 2s-3 2-3 3c0 0-1 1-2 1z" class="L"></path><path d="M295 547h5c1 2 2 3 1 5-1 3-2 5-4 7h-2l1-1c1-3 3-5 2-9-1-1-2-1-3-2z" class="F"></path><path d="M306 520c3 0 3 2 5 3h0 0 0l-1 1h0c1 0 1 0 2 1h-1l-1 1-1-1c-2-2-9-2-12-2h2c2-1 4-2 7-3h0z" class="G"></path><path d="M293 557v1h0c-2-1-3-3-4-5-1-1-1-2-1-4v1h2c1 0 1 1 2 0h1 0l2 2v1c-1 0-1 1-1 2h0c-1 1-1 1-1 2z" class="N"></path><path d="M293 550l2 2v1c-1 0-1 1-1 2h0c-1 1-1 1-1 2-1-2-1-3-1-4s0-2 1-3z" class="H"></path><path d="M281 548l3-1c1 0 1-1 2-1 0 0 0 1 1 1h1l-1 2h0l-1-1c-1 0-1 0-2 1v4h-2c0 1 0 1 1 2h-1l-1-1h0v-1h-1c-1-1-2-1-3-2s-1-1-2-1h1c2-1 3-1 5-2z" class="C"></path><path d="M286 490c2 3 3 7 4 11v1 1h-3c1-1 1-1 1-2h0c-2 1-3 3-5 4 1-3 3-6 3-8s0-2-1-3l-1 1c0-2-1-3 0-4h2v-1z" class="D"></path><path d="M272 548v1 1l9-2c-2 1-3 1-5 2h-1c1 0 1 0 2 1-3 0-5 1-6 3-1 1-2 3-3 4v1h1l-1 1c-3-1-1-4-3-6 2-2 5-4 7-6z" class="O"></path><path d="M282 517h0c2 0 2-1 4-2h-1c0 1-1 1-1 3l-7 6c-1 1-2 2-2 4h0 0l-9 5c5-5 8-8 11-14 1 0 3-1 5-2z" class="G"></path><path d="M290 547h5c1 1 2 1 3 2 1 4-1 6-2 9l-1 1c0-2-1-3-1-4h0c0-1 0-2 1-2v-1l-2-2h0-1c-1 1-1 0-2 0h-2v-1l2-2z" class="C"></path><path d="M290 547c2 0 3 0 4 1h0c1 2 3 4 1 6v1h-1c0-1 0-2 1-2v-1l-2-2h0-1c-1 1-1 0-2 0h-2v-1l2-2z" class="U"></path><defs><linearGradient id="M" x1="285.302" y1="511.142" x2="285.344" y2="521.756" xlink:href="#B"><stop offset="0" stop-color="#2e2c2d"></stop><stop offset="1" stop-color="#464749"></stop></linearGradient></defs><path fill="url(#M)" d="M290 509h0l2 2c0 2 0 3 1 4-3 4-10 7-15 9h-1l7-6c3-3 5-6 6-9z"></path><path d="M271 514l3 4c-1 4-3 8-5 11-5 5-10 10-13 15l-3 6h0c0-2 1-4 2-6 2-2 3-5 5-7 3-4 7-8 9-13 1-3 3-6 2-9v-1z" class="J"></path><path d="M296 538c0-1-1-1-1-2 2 0 5 2 6 3 5 4 6 10 7 16 0 3 0 7-2 10-1 2-4 2-6 2-3 1-5-1-8-2-1-1-2-2-2-4 3 3 6 6 9 5 2 0 4-1 5-2 2-4 2-10 1-14s-4-9-6-11h-1-1l-1-1z" class="E"></path><defs><linearGradient id="N" x1="263.603" y1="541.951" x2="260.232" y2="514.469" xlink:href="#B"><stop offset="0" stop-color="#040503"></stop><stop offset="1" stop-color="#3f3e41"></stop></linearGradient></defs><path fill="url(#N)" d="M267 506v-2h0l2 2h0v-1c2 0 3 1 4 2l1 1-1 1h-3c0 2 1 3 1 5v1c1 3-1 6-2 9-2 5-6 9-9 13-2 2-3 5-5 7l-2-2c2-4 3-7 6-11 2-3 4-5 5-8v-1h-1c1-2 1-5 1-8v-1c0-2 1-3 2-4s0-2 0-3h1z"></path><path d="M267 506v-2h0l2 2h0v-1c2 0 3 1 4 2l1 1-1 1h-3c0 2 1 3 1 5v1l-1-3h-1s0 1-1 1c-1 1-1 1-1 2-1 3-1 5-3 8v-1h-1c1-2 1-5 1-8v-1c0-2 1-3 2-4s0-2 0-3h1z" class="Q"></path><path d="M267 506v9c-1 3-1 5-3 8v-1h-1c1-2 1-5 1-8v-1c0-2 1-3 2-4s0-2 0-3h1z" class="E"></path><path d="M265 554c-4 7-4 13-3 21-1-2-2-5-2-8-2-7-1-12 3-18 2-1 4-1 6-2 3-1 5-3 8-4 2-1 4-1 6 0 2 0 2 1 4 1h0c0-1-1-2-1-2-3-3-6-4-10-4-6-1-11 2-16 7 0-1 0-2 1-2 4-5 10-8 16-8 7 0 9 4 13 7-1-2-3-4-4-7 2 2 5 5 8 6 2 0 1-1 1-2l1-1 1 1h1c2 2 3 4 4 7l-4-4-4 1c-2 0-5 1-6 3v1h-1c-1 0-1-1-1-1-1 0-1 1-2 1l-3 1-9 2v-1-1c-2 2-5 4-7 6z" class="J"></path><path d="M272 548l9-3c1 0 3 0 5 1-1 0-1 1-2 1l-3 1-9 2v-1-1z" class="K"></path><path d="M311 525l6 6c1 1 2 2 2 3 3 3 3 7 3 11 1 2 1 3 1 5h0v-1c2-1 2-2 2-4 0 0 1 0 1-1h0 0 0l1 2-4 11-8 18c-1 3-3 6-5 8h-1c-1 0-2 2-4 2-2 2-6 2-9 1-1 0-3-1-4-1-5-3-7-5-10-10h-1c-1 2-1 3 0 4 0 2 3 5 4 6 1 0 1 1 2 1-2 0-4-1-5-2-5-4-6-8-7-15h-1 0c-2 1-3 3-3 6-1-6-2-11 1-16l2-2v2c0 1 0 2 1 3 2-1 2-3 3-3h0v6h1c1 0 2-1 2-2l4-3c2 3 4 5 6 7 5 3 12 3 17 0 1-1 2-2 3-2 0 1-1 2-1 3h1c2-2 4-7 5-9 1-6-1-10-4-15l5 2h0c-1-4-6-7-9-9 1-1 3-2 4-2l-9-5c2 0 4 0 5-1v-1h-1 2l1-2 1-1z" class="E"></path><path d="M283 566l1 1v1c-1 1-1 1-2 1h-1-1l3-3z" class="B"></path><path d="M317 563v3c-1 2-2 3-3 4l-3 2 1-2-1-1 6-6z" class="I"></path><path d="M321 543v7h1 0v2 4c-1 3-3 7-4 11l-1-1v-3c2-4 3-8 3-12h1v-8z" class="N"></path><path d="M320 551c0-7-1-11-4-16-1-2-3-3-5-4h0v-1l2-1c1 0 2 1 3 2 3 3 5 7 5 12v8h-1zm-9 18l1 1-1 2-5 3c-3 1-5 1-7 1l-3-1c1-2 0-2 0-3h2c2 0 4 1 6 0h1c3-1 4-1 6-3z" class="B"></path><path d="M322 545c1 2 1 3 1 5h0v-1c2-1 2-2 2-4 0 0 1 0 1-1h0 0 0l1 2-4 11-8 18-2 1v-1c2-2 4-5 5-8 1-4 3-8 4-11v-4-2-5z" class="H"></path><path d="M317 566l1 1c-1 3-3 6-5 8s-3 3-5 4l-1-1c-2 1-4 1-6 0l-1 1-4-4h0l3 1c2 0 4 0 7-1l5-3 3-2c1-1 2-2 3-4z" class="N"></path><path d="M299 576c2 0 4 0 7-1-1 2-2 2-3 3-2 0-3 0-4-1v-1z" class="C"></path><path d="M317 566l1 1c-1 3-3 6-5 8s-3 3-5 4l-1-1 3-2c2-1 3-4 4-6 1-1 2-2 3-4z" class="K"></path><path d="M290 573c0-1 0-3 1-3l7 2h-2c0 1 1 1 0 3h0l4 4 1-1c2 1 4 1 6 0l1 1c2-1 3-2 5-4v1l2-1c-1 3-3 6-5 8h-1c-1 0-2 2-4 2-2 2-6 2-9 1v-1c-1 0-2-1-3-2-3-3-3-6-3-10z" class="B"></path><path d="M300 579l1-1c2 1 4 1 6 0l1 1c-2 0-4 1-6 1l-2-1z" class="Q"></path><path d="M313 576l2-1c-1 3-3 6-5 8h-1c-1 0-2 2-4 2-2 2-6 2-9 1v-1l4 1c6-1 10-6 13-10z" class="C"></path><path d="M290 573c0-1 0-3 1-3l7 2h-2c0 1 1 1 0 3h0l-1 1h1v1c0 1 3 3 4 3h0c0 1 1 1 1 1 1 0 1 0 1 1h0-1-2c0-1-1-1-1-2-2-2-4-4-6-7h-2z" class="H"></path><path d="M236 349v1c-1 2-3 4-3 6-1 3-2 5-4 7 0 1-1 1-1 2l1 1c-3 8-4 15-1 22 2 5 6 10 11 14 4 3 10 5 13 11-1 0-1 0-1 1 1 2 1 4 0 6s-3 4-5 5-4 0-6 0c-2-1-2-2-2-3h-1v3c-2 1-4 1-6 1-3 0-7-1-8-3-2-2-3-4-3-6h1-1c-2 3-4 6-5 10 0 1 0 3-1 4 0 1-1 1-2 2-1-1-3-1-5-2-1-2-2-5-3-7 0-6 4-9 8-13-2 1-5 3-7 4h-1 0l1 1c-3 4-7 6-10 9-2 2-1 7-2 9 0 2 0 4-1 6-1 4-3 6-6 9h-1v1c0 1 1 2 2 4 1 1 1 4 1 6l-3 1v1c1 1 2 1 2 3-1 1-1 3-2 4h0-1v1 2s0 1-1 2-1-1-2-1h-2v-1-3l1 1h1v-1l-3-2h0l-4 4v4c0 2 1 3 2 4l2 1-1 1-1-1v1c1 0 1 1 1 1-2-1-3-2-4-4h-2c1 1 2 2 2 3h0l1 2h-1c8 4 15 13 18 20h-1l-1-1s0-1-1-1v1c0 1 2 3 2 5l-3-3c-2 0-3-1-4-2-2 0-2-1-3 0v1h0-1c-2-1-5-1-7 0 0 0 0 1-1 1 0 1 0 2 1 3 1 0 3 1 3 1l1 1c-2 1-4 2-4 4 0 0 1 1 1 2 2 2 4 2 7 2l-1 1c-1 1-1 2-2 3 0 2 0 2 1 3 1 0 2-1 2 0v5c2-2 3-5 7-6 2 0 4 0 6 2h0c-2-1-4-1-6-1-2 1-4 2-5 4v1h0 1v-1h1v1h1l2 1v2c-1 0-2 0-2 2v1c-2 1-3 1-5 1l-5 2h9 0c1 1 1 2 3 3h4c2 3 5 6 10 8 2 1 5 3 8 5 4 5 9 10 15 13h-1c-5-2-8-5-12-9-2-2-3-4-6-5-4-3-8-2-12-2 4 1 6 2 9 4-1 1-1 1-2 1v1l2 2c1 1 2 1 2 2 0 2 2 3 2 5h1v-1c1-1 4 0 5 0 4 1 7 3 10 5l8 3c-2 1-5-1-7-1-4-1-9 1-12-1h0c-2-1-4-3-6-4s-6-2-9-2h0v1s1 1 1 2h-1l-3-3c-2 1-3 1-5 1h-4c1 2 2 2 2 4-2 0-5-2-7-2 3 2 7 5 11 7h-3-1-1l-9-6c-1 1-4 2-5 3v2c0 1 1 2 1 3l1 1-3-1v-1c-1-1-1-2-2-3s-2-2-3-2c-2 0-2 1-3 2h0c-2 1-2 0-4 0 1-2 1-2 1-4-4 0-8 1-12 1-2 1-5 2-7 2-3-2-5-5-6-8-8-10-13-21-18-33-11-31-10-70 4-101 7-14 18-25 31-34 4-2 8-3 11-5s7-3 10-5h3l3-3c3-2 7-3 10-4h1l-1-1v-1c2-1 4 0 5 0v-1l5-3v1l-1 1 1 1h1l1-1c2 1 3 0 5 0h1c1-1 2-2 4-3l-1-1c1 0 2 0 3-1 2-1 4-1 6-2 3-2 6-5 9-7l9-12z" class="L"></path><path d="M171 574c-1-1-1-2-2-4v-1c2-1 5 0 7 0-1 1-4 2-5 3v2z" class="O"></path><path d="M184 566l-3-3 2 1h2c1 0 5-1 7 0l1 1c-2 1-3 1-5 1h-4z" class="F"></path><path d="M173 481l-3-2c-6-3-9-7-11-13 1 2 2 4 3 5 3 3 7 5 9 7 1 1 2 2 2 3h0z" class="O"></path><path d="M210 567h1c4-1 6 0 9 3l-1 1-5 1c-1-1-2-2-3-2-1-1-1-2-1-3z" class="B"></path><path d="M172 386h3c-1 0-1 1-1 2 1 1 2 2 3 4v2l1 1h-2c-2-1-4-1-6-2 0-1 0-4 1-5 0 0 1 0 1-2z" class="R"></path><path d="M147 570v-1c-1-2-6-4-8-5 3 1 6 2 9 2 1 0 1 0 1-1 3 0 5 0 8 1 1 1 2 2 2 3-4 0-8 1-12 1z" class="F"></path><path d="M173 494c-3-1-5-4-7-4-1-1-1-1-2-1 1-2 3-2 4-3s-1-1-1-2l1-1h5c8 4 15 13 18 20h-1l-1-1s0-1-1-1v1c0 1 2 3 2 5l-3-3c-2 0-3-1-4-2-2 0-2-1-3 0v1h0-1v-1c-2-1-4-2-7-2h0c-1-1-2-2-2-3l1-1 2-2z" class="I"></path><path d="M173 494c4 0 8 0 12 3 1 1 2 3 3 4v1c0 1 2 3 2 5l-3-3c-2 0-3-1-4-2-2 0-2-1-3 0v1h0-1v-1c-2-1-4-2-7-2h0c-1-1-2-2-2-3l1-1 2-2z" class="C"></path><path d="M173 494c4 0 8 0 12 3 1 1 2 3 3 4v1c-3-3-7-5-11-6-1 0-2 1-3 2 0 1-1 1-1 2h-1 0c-1-1-2-2-2-3l1-1 2-2z" class="G"></path><path d="M204 415l1 1c-3 4-7 6-10 9-2 2-1 7-2 9 0 2 0 4-1 6-1 4-3 6-6 9h-1-1v2h-1c-1-1-1-1-1-2h1v-2c-1-1-3-2-4-3-2-3-1-4-1-7-2 0-3 2-5 2s-3 0-4-1v-2c1-4 8-9 11-11-4 1-8 2-12 2l2-1c2-2 6-2 8-3l13-4c4-1 9-2 13-4h0z" class="B"></path><path d="M183 447l3-6-1 8h-1v2h-1c-1-1-1-1-1-2h1v-2z" class="D"></path><path d="M173 478c-1-1-1-3-1-4s0-3-1-4c0-1-6-1-7-2s-4-5-4-7l2 1h0c1 0 1 0 2-1h0-2l-1-1c-1-1-1-3 0-4 0-2 2-3 3-4s1-2 1-3c1 0 1-1 2-2 2-1 4-1 5-1s1 1 2 1l1-1c1-1 2-1 3 0 2 0 3 1 4 3 0 1 0 1 1 2h1v-2h1v1c0 1 1 2 2 4 1 1 1 4 1 6l-3 1v1c1 1 2 1 2 3-1 1-1 3-2 4h0-1v1 2s0 1-1 2-1-1-2-1h-2v-1-3l1 1h1v-1l-3-2h0l-4 4v4c0 2 1 3 2 4l2 1-1 1-1-1v1c1 0 1 1 1 1-2-1-3-2-4-4zm63-129v1c-1 2-3 4-3 6-1 3-2 5-4 7 0 1-1 1-1 2l1 1c-3 8-4 15-1 22 2 5 6 10 11 14 4 3 10 5 13 11-1 0-1 0-1 1 1 2 1 4 0 6s-3 4-5 5-4 0-6 0c-2-1-2-2-2-3h-1v3c-2 1-4 1-6 1-3 0-7-1-8-3-2-2-3-4-3-6h1-1c-2 3-4 6-5 10 0 1 0 3-1 4 0 1-1 1-2 2-1-1-3-1-5-2-1-2-2-5-3-7 0-6 4-9 8-13-2 1-5 3-7 4h-1c-6 1-12 3-18 4-4 1-7 1-11 2h0v-1c1-3 3-6 7-8l2-1c1-1 1 0 1-1-2 0-4 1-5 3-5 3-8 8-13 11-1-1-2-1-3-2 1-2 2-4 2-5-2 0-6 2-8 1v-1c0-3 3-6 4-8-2 0-5 1-7 0v-2c0-2 2-4 3-5-1 0-3-1-4-2v-1c2-1 7 0 10 0l-6-3c9-2 19 1 28 4l9 3h0l1 1c4 0 9 0 12-2-3 1-6 1-9 1-2-1-4-2-7-3l-9-3c-2-1-3-2-5-2l-1-1v-2c-1-2-2-3-3-4 0-1 0-2 1-2l3-3c3-2 7-3 10-4h1l-1-1v-1c2-1 4 0 5 0v-1l5-3v1l-1 1 1 1h1l1-1c2 1 3 0 5 0h1c1-1 2-2 4-3l-1-1c1 0 2 0 3-1 2-1 4-1 6-2 3-2 6-5 9-7l9-12z" class="B"></path><path d="M179 388s-1 0-1-1h1v-1c-1 0-1 0-1-1v-1 1c2 0 6 1 7 1l-6 2z" class="D"></path><path d="M206 398c2 1 3-1 4 0 1 0 1 1 2 1h-2c-2 1-5 1-7 0h0 0c1 0 2-1 3-1z" class="M"></path><path d="M202 382c2 1 5 1 6 3v1 1c-1 0-3-1-3-1l-3-3v-1z" class="D"></path><path d="M189 379h6c2 1 5 2 7 3v1c-2-1-4-2-6-2-5-1-12 0-16 2l-1 1-1-1c3-2 7-3 10-4h1z" class="V"></path><path d="M193 376l5-3v1l-1 1 1 1h1l1-1c2 1 3 0 5 0h1v1c1 2 4 2 4 6-1-1-1-1-1-2s-1-1-2-2c-3-1-8-1-11-1l-1 1h0c-1-1-1-1-2-1v-1z" class="E"></path><path d="M206 398l1-1c1-2-1-7-1-9 1 0 2 1 3 1h0c1 1 1 1 1 2 0 2 0 4 2 6h0 2l-2 2c-1 0-1-1-2-1-1-1-2 1-4 0z" class="J"></path><path d="M220 373h1v1 5c-4 1-7 1-9 5-2 2-2 5-2 7 0-1 0-1-1-2h0c0-3 2-7 3-8 2-2 4-3 6-4 1-1 2-3 2-4z" class="G"></path><defs><linearGradient id="O" x1="211.469" y1="403.955" x2="203.158" y2="403.472" xlink:href="#B"><stop offset="0" stop-color="#0a0a0c"></stop><stop offset="1" stop-color="#2d2d2b"></stop></linearGradient></defs><path fill="url(#O)" d="M208 402c2 0 4-2 5-2 0 1 0 1-1 1 0 2 1 2-1 4-1 1-8 2-10 2s-4-1-5-2c0-1-1-1-1-2l1 1c4 0 9 0 12-2z"></path><path d="M175 386l3-3 1 1h-1v1c0 1 0 1 1 1v1h-1c0 1 1 1 1 1 4 6 10 6 14 11-6-3-11-4-16-7-1-2-2-3-3-4 0-1 0-2 1-2z" class="J"></path><path d="M236 349v1c-1 2-3 4-3 6-2 2-3 4-5 6-1 2-3 3-4 5-2 1-2 4-3 6h-1l2-6c-3 2-5 4-8 4-2 1-3 1-4 1l-1-1c1 0 2 0 3-1 2-1 4-1 6-2 3-2 6-5 9-7l9-12z" class="D"></path><path d="M228 365l1 1c-3 8-4 15-1 22 2 5 6 10 11 14 4 3 10 5 13 11-1 0-1 0-1 1 1 2 1 4 0 6s-3 4-5 5-4 0-6 0c-2-1-2-2-2-3h-1c-1-2-1-5-1-8 0-4 1-7 1-10v-2c0-1-1-2-2-2-1-2-3-4-5-6-1-1-2-3-2-4-1-1-1-3-2-4 0 0 0-1-1-1h0 0c-4 5-4 12-8 18-3 7-10 9-17 11l-23 6c8-7 19-6 29-9 9-3 12-10 15-19 1-3 2-7 3-10 1-6 1-11 4-17z" class="G"></path><path d="M238 422c-1-1-1-3-1-4l1 1c1 2 3 3 5 3 1 0 2 0 3-1 0 0 1-1 1-2-2 1-2 2-4 2-1 0-2-1-3-2-4-5-3-11-2-16 4 4 10 6 13 11 1 2 1 4 0 6s-3 4-5 5-4 0-6 0c-2-1-2-2-2-3zm111 123c2 2 6 4 7 7v1c0 1 1 2 1 3 4 2 5 9 6 13 13-17 25-34 33-53 11-24 17-48 22-74 9-50 10-102 4-153-4-34-11-68-24-100-11-25-26-49-48-66-9-8-18-14-29-19-28-13-59-16-89-13-45 5-85 28-113 63-27 33-41 75-48 116l-4 20c0 6 0 12-2 17-3-8-3-18-4-26-2-41 5-84 25-120 23-41 63-69 108-81 23-7 49-10 74-10 38 1 77 9 111 28 17 9 32 21 46 35 29 30 48 68 59 108 7 24 11 51 13 76 4 68-5 140-39 200-14 25-32 47-54 66l-14 10c-2 2-6 3-7 5 10 20 25 35 47 42 14 4 31 3 45-4 17-9 27-28 35-45 2-6 4-13 7-19 0 31-6 66-29 89-14 14-32 20-52 20-29-1-55-13-75-33-6-5-10-11-15-17l-3-6c-1 0-1-2-2-2-56 26-125 29-182 8-8-3-16-6-23-10-3-1-6-3-10-5-2-1-3-3-5-4h-2l-1-1c0-1 0-2-1-3l-1-1h-1 0c1-2 5-3 6-5-3 1-5 4-8 5-1 1-3 2-4 3 0 3 1 5-2 7-1 2-3 3-5 4-3 3-5 6-10 8 3-2 6-5 7-9 0-1 0-3-1-4v-1c0-2 1-4 3-6v-1c2-1 3-2 5-3h0v-1c0-1 5-3 5-3h0c-3 1-6 3-9 4-2 1-5 2-6 3-3 2-5 6-7 9-2 4-1 7-6 9-3 2-7 2-11 2-2 0-4 1-6 1 2-1 5-2 7-4 4-4 5-9 10-10 2 0 3-1 5 0 1-1 2-1 2-2v-1h0c-2 1-4 0-6 0h0c-3-2-7-4-10-4h0l6-5c3-1 4-2 7-2l1-1h7l2-2h0c3 0 4-2 6-4l1-1c1-1 3-1 4-1l13-1h-4c-3-1-14-1-16 0v1h-1c-3-2-5-5-7-7-14-17-24-37-28-58-5-33 2-69 21-96 11-16 27-29 44-39 7-3 14-7 21-9 5-2 9-3 14-4h10l-1-1c2 0 4 0 6-1-1-1-1-1-1-2 1 0 3-1 4-1 3 1 7 1 10 2v1c-1 0-3-1-5 0v1l1 1h-1c-3 1-7 2-10 4l-3 3h-3c-3 2-7 3-10 5s-7 3-11 5c-13 9-24 20-31 34-14 31-15 70-4 101 5 12 10 23 18 33 1 3 3 6 6 8 2 0 5-1 7-2 4 0 8-1 12-1 0 2 0 2-1 4 2 0 2 1 4 0h0c1-1 1-2 3-2 1 0 2 1 3 2s1 2 2 3v1l3 1-1-1c0-1-1-2-1-3v-2c1-1 4-2 5-3l9 6h1 1c4 1 8 0 13 0 1 0 3 0 4 1h0l-5 2h-3s-1 0-1 1h-2c-8 1-15 1-21 7-1 2-3 5-5 6 0 1-1 1-1 1 1 1 1 1 1 2s0 1 1 1v2c0 2-3 3-4 5 2-2 5-4 7-4h0c3 3 6 4 9 6 3 1 5 3 8 4 7 3 14 5 21 7 32 9 69 7 100-6 8-3 15-7 22-11v-1c-5-9-10-21-9-32 1-3 2-6 2-9h-1l4-11c2-6 4-13 9-17 2-2 5-3 7-3s4 2 5 3 1 1 2 1v2c-1 0-2 1-2 2v1c1 0 2 0 3 1s1 1 1 2c0 2-1 4-3 5l-1 1v1h1z" class="B"></path><path d="M158 573c2 0 2 1 4 0h0v3c-3-1-5-1-8 0 2-1 3-2 4-3z" class="M"></path><path d="M335 589v1l1 1c1-2 2-2 4-3-1 3-3 5-5 7v-6z" class="C"></path><path d="M349 576h1c0 2-1 4-1 7l-6 5c2-4 5-7 6-12h0z" class="K"></path><path d="M348 560l1-2h1v1c1 2 1 4 2 6 1-2 2-5 3-6v2c-1 2-2 4-3 5h-1l-3-6z" class="M"></path><path d="M330 556h0c-1 1-2 1-2 2l-1-1s2-1 2-2v-1c1-2 4-5 6-6h1c4-3 8-3 12-4v1h-3 0c-4 1-8 3-11 5v1c0 1-1 1-1 3-1 0-2 1-3 1v1zm-170 34v1c-3 4-6 9-11 11 1-1 3-2 3-4l3-6c1-2 2-2 4-2h1z" class="C"></path><path d="M333 554c2-1 5-1 7-1 4 0 7 2 9 5l-1 2c-2-2-4-4-6-5-4-2-9-1-12 1v-1c1 0 2-1 3-1z" class="D"></path><path d="M140 582h0 2 4c0-1 1 0 2-1 3 0 6 0 8-1 1 0 2 0 2 1-4 1-13 5-17 2l-1-1z" class="P"></path><path d="M137 600v1c1 3-4 7-5 9h0c-2-1-3 0-4 0h-1c4-3 6-7 10-10z" class="T"></path><path d="M159 590c2-1 6-2 7-4-3 1-8 2-11 4l-1 1v-1c2-2 6-3 9-3l7-2c-2 2-2 5-5 6h-1c0-1 0-1-1-1h-3-1z" class="D"></path><path d="M352 566c1-1 2-3 3-5 0 1 0 2 1 3v1l1 1-1-1c-1 2-1 4-1 6 1 1 1 3 2 3 0-1 1-1 2-2v-1c0 2-3 4-4 5v-2c-1-1-1-3-1-5v-2l-1-1v1c0 2 0 5-1 7v1 1h-1l1-6v-4z" class="F"></path><path d="M170 576v1l3 1-1-1c3-1 7-1 10-1h0c-4 2-9 2-13 3-4 0-8 2-11 2 0-1-1-1-2-1 1 0 2-1 3-1 0-1 3-1 4-1 3 0 5 0 7-2z" class="K"></path><path d="M116 596c4-3 8-4 13-4h0c3 1 5 0 8 1h-3c-2 1-3 1-5 2-2 0-5 0-7 1-1 0-2-1-4 0h-2 0z" class="O"></path><path d="M140 582c-1 0-1-1-2-1h2c1 0 3 0 5-1 3-2 6-2 9-2h9c-1 0-4 0-4 1-1 0-2 1-3 1-2 1-5 1-8 1-1 1-2 0-2 1h-4-2 0z" class="C"></path><path d="M180 377c-1-1-1-1-1-2 1 0 3-1 4-1 3 1 7 1 10 2v1c-1 0-3-1-5 0v1l1 1h-1l-1-1h-5l-7 1-1-1c2 0 4 0 6-1z" class="P"></path><path d="M180 377c1-1 4-1 6 0 1 0 1 0 2 1-1 0-6-1-6 0l-7 1-1-1c2 0 4 0 6-1z" class="M"></path><path d="M351 566h1v4l-1 6h1v-1-1c1-2 1-5 1-7v-1l1 1v2c0 2 0 4 1 5v2l-3 4-3 3c0-3 1-5 1-7h-1c0-1 1-3 1-4 1-2 1-4 1-6z" class="T"></path><path d="M354 569c0 2 0 4 1 5v2l-3 4c0-2 0-2 1-3 1-2 1-5 1-8z" class="C"></path><defs><linearGradient id="P" x1="340.576" y1="584.476" x2="335.66" y2="581.226" xlink:href="#B"><stop offset="0" stop-color="#282829"></stop><stop offset="1" stop-color="#434242"></stop></linearGradient></defs><path fill="url(#P)" d="M339 575c1-1 1-1 3-1 1 1 1 3 1 5l-1 4-2 5c-2 1-3 1-4 3l-1-1v-1c0-3 0-7 1-9l2-2c0-1 1-2 1-3z"></path><path d="M339 575c1-1 1-1 3-1 1 1 1 3 1 5l-1 4c-1-1-1-2-1-3 1 0 1 0 1-1-1-1-1-3-2-4h-1z" class="O"></path><path d="M120 598c1 0 2 0 2-1h2 2 1c-1 0-1 1-2 1l-5 2c-3 1-6 3-8 5l-3 3h0c-1 0-1 0-2 1 0-1-1-1-2-1-1 1-2 1-4 1v-1c2-1 3-2 5-3h0v-1c0-1 5-3 5-3h0c2-2 4-3 7-4 1 1 1 1 2 1h0z" class="N"></path><path d="M111 601c2-2 4-3 7-4 1 1 1 1 2 1h0s-1 1-2 1-2 1-3 2-3 1-4 3c0 0-1 1-2 1-1 1-2 0-3 0h0v-1c0-1 5-3 5-3h0z" class="C"></path><defs><linearGradient id="Q" x1="339.26" y1="547.567" x2="347.098" y2="558.822" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#565458"></stop></linearGradient></defs><path fill="url(#Q)" d="M334 551c1 0 2-1 3-1 2-1 5-1 8 0l9 3 1 1c-1 2-4 3-5 4h-1c-2-3-5-5-9-5-2 0-5 0-7 1 0-2 1-2 1-3z"></path><path d="M345 550l9 3c-2 0-3 1-4 0h0c-2 0-4-1-5-2v-1zm-7 14h1c3 1 8 6 9 8l1 3c-2-4-5-6-9-8 1 1 3 3 3 6v6c0-2 0-4-1-5-2 0-2 0-3 1 0 1-1 2-1 3v-1c-1-2-2-3-3-4-1 0-2 0-3 1v2 2c-1-1-1-4-1-6 1-3 4-2 6-4 1-2 1-3 1-4z" class="C"></path><path d="M338 564h1c0 3-1 6-2 9-2 0-4-1-6-1 1-3 4-2 6-4 1-2 1-3 1-4z" class="F"></path><path d="M327 546c2-6 4-13 9-17 2-2 5-3 7-3s4 2 5 3 1 1 2 1v2c-1 0-2 1-2 2v1l-2 2h0v-1c0-1 0-3-1-4s-1-1-2-1-1 1-2 1v-2h0c-2 0-2 1-3 2-1 0-1 0-1-1-1 0-3 1-3 2-2 3-3 7-4 10l-6 14h-1l4-11z" class="G"></path><path d="M174 579h1c-1 1-3 2-4 2l-18 6c-3 1-8 3-11 3h-3v1c-1 1-1 1-3 1-1-1-2-1-3-2-4-1-8-3-11-5 5 1 9 4 14 6 1 0 2 0 3-1-3-1-5-4-6-6-2-1-4-2-5-3h-2c4-1 9 3 11 6 1 1 2 2 3 2h3v-1h-1c-1-1-8-7-8-8 3 1 6 5 9 7 6-1 12-3 18-5 4-1 9-1 13-3z" class="T"></path><path d="M103 594l1 1v1 1c1 0 2 0 3-1 1 0 2 0 3-1h3 1c0 1 1 1 1 1h1 0 0c-3 1-6 3-8 4-5 2-10 5-15 6-2-2-4-4-6-5l1-1h7l2-2h0c3 0 4-2 6-4z" class="J"></path><path d="M103 594l1 1v1 1c1 0 2 0 3-1 1 0 2 0 3-1h3 1c0 1 1 1 1 1-6 2-12 4-20 4l2-2h0c3 0 4-2 6-4z" class="C"></path><defs><linearGradient id="R" x1="339.678" y1="579.316" x2="326.892" y2="570.141" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#626162"></stop></linearGradient></defs><path fill="url(#R)" d="M334 559h2c0 2 1 3 2 5 0 1 0 2-1 4-2 2-5 1-6 4 0 2 0 5 1 6l1 1 1 1s0 2-1 3v10c-3-5-5-10-6-16-1-3-1-5-1-8 0-1 0-3 1-4 1-3 4-5 7-6z"></path><path d="M326 569c0-1 0-3 1-4 1-3 4-5 7-6 0 3-1 7-3 9-1 1-2 2-4 2 0 0 0-1-1-1z" class="K"></path></svg>