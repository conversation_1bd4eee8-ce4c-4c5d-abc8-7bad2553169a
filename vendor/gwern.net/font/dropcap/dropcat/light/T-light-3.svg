<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="55 133 464 535"><!--oldViewBox="0 0 564 752"--><style>.B{fill:#333233}.C{fill:#3f3f3f}.D{fill:#2a292a}.E{fill:#4d4d4d}.F{fill:#585858}.G{fill:#3b3a3a}.H{fill:#373637}.I{fill:#474747}.J{fill:#717070}.K{fill:#2e2d2f}.L{fill:#535253}.M{fill:#6b6a6b}.N{fill:#2d2d2d}.O{fill:#161616}.P{fill:#252525}.Q{fill:#5e5d5e}.R{fill:#636263}.S{fill:#1b1a1b}.T{fill:#232223}.U{fill:#181818}.V{fill:#454445}.W{fill:#a09fa0}.X{fill:#7c7b7b}.Y{fill:#131313}.Z{fill:#676767}.a{fill:#898888}.b{fill:#181819}.c{fill:#807e7f}.d{fill:#8d8c8d}.e{fill:#d9d7d8}.f{fill:#777677}.g{fill:#1a1a1a}.h{fill:#d5d4d4}.i{fill:#858484}.j{fill:#b9b8b8}.k{fill:#949393}.l{fill:#afaeaf}.m{fill:#bbbaba}.n{fill:#c7c6c7}.o{fill:#999899}.p{fill:#a9a8a9}.q{fill:#e7e6e7}.r{fill:#efeeef}.s{fill:#f6f5f5}</style><path d="M403 541l3 3c-1 0-1 1-1 1h0l-2-1v-3z" class="r"></path><path d="M289 152v-1l1 1c0 1 1 1 1 2l1 1c-2 0-3-1-4 0 0-1 1-2 1-3z" class="j"></path><path d="M331 120v6c-1-1-2-2-2-4h0v-2h2z" class="L"></path><path d="M293 153h0c1 1 3 1 4 2-1 0-1 1-1 1-1 0-3-1-4-1l-1-1c1-1 1-1 2-1zm-12-8l2 2h1l1 2v1 2h-1c-1-1-1-2-2-4l-1-2v-1z" class="k"></path><path d="M329 114l2 6h-2l-1-1c0-2 0-3-1-4h0s1-1 2-1z" class="I"></path><path d="M90 516l6 3c-1 0-1 1-1 1h-1l-5-2v-1s0-1 1-1z" class="b"></path><path d="M285 150c1 1 2 1 3 2h1c0 1-1 2-1 3h-2c-1-1-1-2-2-3h1v-2z" class="W"></path><path d="M231 71c3 2 5 3 8 4h1 0-3c-1 0-1 0-2 1h-1l-1-2c-1-1-1-2-2-2v-1h0z" class="Y"></path><path d="M194 97l1 1v2l-1 2h-1c-2-1-5 2-6 3h0c0-1 1-2 2-3 2-1 4-3 5-5h0z" class="S"></path><path d="M157 294l3-14c1 0 1 2 2 3-1 0-1 0-2 1-1 2 0 7-2 8h0c0 1-1 1-1 2z" class="g"></path><path d="M336 68c4 0 9 3 11 5l2 2c-5-3-9-5-14-5 1-1 1-1 1-2z" class="B"></path><path d="M125 254c2 0 3-1 4-2h0 2c-2 2-5 4-8 6h-1c-2 1-3 1-5 1 1-1 2-1 3-2 2 0 4-1 5-3z" class="H"></path><path d="M247 155l2 1c-2 0-3 0-4 1s0 1-2 2h0l-4 1c0-1 1-1 2-2h-3 0v-1c1-1 3-1 4-1 1-1 3 0 5-1z" class="q"></path><path d="M238 157c1-1 3-1 4-1-1 0-1 1-2 2 1 0 2-1 3 0v1l-4 1c0-1 1-1 2-2h-3 0v-1z" class="e"></path><path d="M323 114l1-1 2 3h1v-1c1 1 1 2 1 4l1 1v2h0c-2 0-3-2-5-3h0c0-1 1-1 1-2l-2-3z" class="Z"></path><path d="M325 117c1 2 3 3 4 5h0c-2 0-3-2-5-3h0c0-1 1-1 1-2zM107 278c5-2 8-4 12-7 0 2-1 3-2 4v1c-2 2-6 2-9 3l-1-1h0z" class="C"></path><path d="M113 265c-1 0-2 1-3 0-1 0-1 0-1-1 1-2 5-2 7-2 1-1 3-1 4-1v1c-1 0-1 1-2 1h-2 0c0 1-1 2-2 2h-1 0z" class="p"></path><path d="M195 84c0-1 1-3 1-5 0-3-1-7 0-10v1 1 4h1v1c0 2 0 3 1 4v1 3h-1-2z" class="J"></path><path d="M304 163c-2 1-3 1-5 0h-2 0l-1-1v-1h-1l-1-1h-1l-1-1c1-1 2-1 3-2 1 1 1 2 2 3h2 1c1 1 1 1 1 2l3 1z" class="e"></path><path d="M277 454l1-1v1 17 13c-1-3-1-6-1-10 0-1 1-2 0-3h0l-1 2-1-1c0-1 1-2 1-3s1-2 1-3c1-1 1-4 0-6 0-1 0-1-1-1l1-5z" class="K"></path><path d="M193 102h1 2l1 1v1l-1 1c-2 1-3 2-4 3 0 0-1 0-1-1v-1h0c-3 1-4 3-6 4 1-3 6-5 8-8z" class="V"></path><path d="M396 510c0-1 0-1 1 0v-1h4c1 1 0 1 1 2 1 0 2 0 2-1h1v1h-1l-1 1h-4 0-3c-3 1-6 1-9 2h0c0-1 1-2 2-2h0c1 0 2 0 2-1 2 0 3 0 5-1z" class="e"></path><path d="M329 83h1s1 0 1 1c4 0 9 3 11 6 2 2 4 5 5 7v1c-2-4-5-7-9-10-2-1-4-2-7-3-2-1-3-1-5-1h2l1-1z" class="B"></path><path d="M250 158h0c1 0 2 0 3 1h0c0 1-1 1-1 2v1c-1 0-2 1-3 0h0l-1 1c-1-1-2 0-3 0h0l-1-1v-1-1l1-1c1-1 3-1 5-1z" class="r"></path><path d="M198 81c1-2 2-6 4-7l1 1c-3 3-4 7-5 11 0 1-1 2-1 4l-1 1h0c-2 0-4 4-5 5 1-2 2-3 2-5 2-2 2-5 2-7h2 1v-3z" class="d"></path><path d="M190 150h0l-2 1c-1 1-6 5-6 6l-3 3c-1 1-3 4-4 4h-2v-1l3-3h0l3-3 3-3h2v-1l6-3z" class="U"></path><path d="M387 514c3-1 6-1 9-2h3c-5 1-10 3-16 4-2 1-4 1-6 2h-1c0 1 0 1 1 1h-1c-1 1-3 1-5 2l-1 2v-8h0v3c1 0 2 0 2-1l1 1h0c3 0 6-2 8-3 2 0 5 0 6-1z" class="K"></path><path d="M191 106h0v1c0 1 1 1 1 1-1 1-1 1-1 2-2 2-4 3-5 5-3 3-5 7-7 11-1 2-2 4-3 7l1-3c1-7 6-14 10-19 1-2 3-3 4-5z" class="L"></path><path d="M284 147s1 0 2 1h1v-1l1-1 2 1c2 2 4 3 5 5h-2v1h0c-1 0-1 0-2 1 0-1-1-1-1-2l-1-1v1h-1c-1-1-2-1-3-2v-1l-1-2z" class="c"></path><path d="M290 150l3 2v1c-1 0-1 0-2 1 0-1-1-1-1-2v-2z" class="o"></path><path d="M285 149h3v-1h1l1 2v2l-1-1v1h-1c-1-1-2-1-3-2v-1z" class="i"></path><path d="M238 158h3c-1 1-2 1-2 2-1 1-3 1-4 2h4c1 0 3-1 4-1-4 2-7 3-11 3h-3 1l1-1v-1c-1 0-2-1-2-2l1-1h5 0c1 0 2 0 3-1z" class="h"></path><path d="M230 159h5 0l-1 1h-1l-1 1 1 2c0 1-1 1-1 1h-3 1l1-1v-1c-1 0-2-1-2-2l1-1z" class="l"></path><path d="M185 148v3h2c-1 0-3 1-3 2v1h-2l-3 3-3 3h0-1-2 0v-1l3-6v2c0 1-1 1-1 2v1c1-1 2-3 3-4s4-3 5-5l2-1z" class="S"></path><path d="M185 148v3h2c-1 0-3 1-3 2v1h-2c0-1 1-3 1-5l2-1z" class="b"></path><path d="M111 311c2-1 3-1 4-3h1c-5 9-13 18-23 20-1 1-3 1-4 1 3-1 6-2 8-4 3 0 5-3 7-5 2-1 3-2 4-3l3-6h0z" class="I"></path><path d="M68 423l-1 2 1 1c1-2 2-5 4-7 2-3 3-7 4-11h0l-1 9v1 9c0 2 0 4-1 6v-12c1-1 0-1 0-2v4c0 1-1 2-1 3v-7c-3 5-6 10-11 13 2-3 4-6 6-10v1z" class="N"></path><path d="M299 160c2 0 4-1 7-1h1c2 1 2 1 4 1v1c1 1 5 1 6 3h-1-1-6c-2 0-4-1-5-1l-3-1c0-1 0-1-1-2h-1z" class="p"></path><path d="M301 162c2-2 11 0 13 1l3 1h-8c-2 0-4-1-5-1l-3-1z" class="g"></path><path d="M135 562c0 1 0 1 1 2h0c-1 3-2 7-3 10 0 8 1 17 5 24 2 5 9 9 14 12-3 0-7-3-8-4-8-6-11-14-12-23v-13l3-8z" class="S"></path><path d="M387 511h2c1 0 2 0 3-1h4c-2 1-3 1-5 1 0 1-1 1-2 1h0c-1 0-2 1-2 2h0c-1 1-4 1-6 1-2 1-5 3-8 3h0l-1-1c0 1-1 1-2 1v-3h0 1 3l6-2c1-1 3-2 5-2h0 2z" class="j"></path><path d="M374 515h0 4c2-1 4-3 5-2-1 1-2 1-3 1-2 1-5 2-7 4l-1-1c0 1-1 1-2 1v-3h0 1 3z" class="S"></path><path d="M246 307v2c-1 4-2 10-6 12l-1 1h0c-1 0-2 1-2 1v2c-1 0-1 0-2 1l-1-1v-1-1c0-1 1-1 2-2l2-2v-2c1-1 1-2 1-3s0-1-1-1h0l4-3c0 2-1 4-1 6 1 1 2-6 3-7l2-2h0z" class="L"></path><path d="M237 323v2c-1 0-1 0-2 1l-1-1v-1c1-1 2-1 3-1z" class="K"></path><path d="M123 272c0 1 0 1 1 2-2 2-6 2-8 5h1c1 0 2-1 3-1-1 1-3 2-4 3-2-1-3 1-5 0-1 0-2 0-3 1-4-1-6-2-9-5h5c1 0 3 0 3 1h0l1 1c3-1 7-1 9-3 2-1 4-2 6-4z" class="J"></path><path d="M108 282c-4-1-6-2-9-5h5v1h-2 0c1 2 5 2 7 2s2 0 4-1v1c-1 0-1 0-2 1-1 0-2 0-3 1z" class="L"></path><defs><linearGradient id="A" x1="385.168" y1="407.052" x2="382.047" y2="395.883" xlink:href="#B"><stop offset="0" stop-color="#9c9999"></stop><stop offset="1" stop-color="#b6b6b5"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M381 402l-3-8c1 0 2 1 2 2h1c0-2-2-3-2-5-1-2-3-3-4-4-1-2-3-4-4-5h-1l1-1 6 6c2 2 3 4 4 5h1c2 4 4 9 5 13v3 4c-1-1-1-3-2-4v-1c-1-1-2-3-3-5h0-1z"></path><path d="M214 70v1c-1 1-2 1-3 2v1c-3 2-5 3-7 6-3 3-4 7-7 11v-1c0-2 1-3 1-4 1-4 2-8 5-11l1-2c2 0 3 0 4-1 2-1 4-1 6-2z" class="Y"></path><path d="M372 414c3 2 6 4 7 7 1 1 2 2 2 4 1 0 2 1 2 2v3h-1v2c-1 0-1 0-1-1-1-1-1-2-2-3-1-2-4-5-6-6-1 0-2-1-2-1v-2c1-1 5 3 6 3 0-1-1-1-1-1-2-1-5-3-5-5h0v-1l1-1z" class="H"></path><path d="M372 414c3 2 6 4 7 7 1 1 2 2 2 4h-1v-1c0-2-6-7-9-8h0v-1l1-1z" class="h"></path><path d="M382 392c-1-2-2-4-4-6-2-3-7-7-8-10l9 9 3 6c-1-7-6-12-11-16h0c2 1 3 2 5 3 5 6 9 13 11 20l1 2 1 6-1-1c0 1 0 1 1 1 0 1 0 2-1 3h0v1h-1v-2-3c-1-4-3-9-5-13z" class="D"></path><path d="M387 405v-3-1s-1-1-1-2v-1c1 1 1 1 2 1v1l1 6-1-1c0 1 0 1 1 1 0 1 0 2-1 3h0v1h-1v-2-3z" class="U"></path><path d="M390 502c7-5 12-10 14-19v-6h1c1 3 1 8 0 12-3 7-8 12-15 16h0-1l1 1c-2 1-5 2-8 3v-1-1h0 0l8-5z" class="P"></path><path d="M394 518c7-2 21 2 28 6 8 4 17 13 20 22h-1c-6-11-15-20-27-24l-10-2c-3 0-7-2-10-2h0z" class="U"></path><path d="M396 486l1 1c0 1-1 1 0 2 0-1 0-1 1-2 0-2 1-4 2-6h1c0 1-2 4-2 6 0 1-1 2-1 3-1 2-2 3-3 5l-5 5-2 1c-2 1-4 3-6 4l-1-1h0c1-1 2-1 3-2 2-1 2-2 3-4l10-16 1 1c-1 1-1 2-2 3z" class="p"></path><path d="M397 482l1 1c-1 1-1 2-2 3-1 2-3 4-4 7h0c1-1 2-3 3-4v1c-1 4-5 7-7 11-2 1-4 3-6 4l-1-1h0c1-1 2-1 3-2 2-1 2-2 3-4l10-16z" class="J"></path><path d="M162 283l2 4-2 6-4 8s-1 1-2 1c-1 1-2 3-4 4v-1c0-1 1-2 0-3 1-3 3-6 5-8 0-1 1-1 1-2h0c2-1 1-6 2-8 1-1 1-1 2-1z" class="Y"></path><path d="M315 104c5-1 9-1 13 2h1c1 1 7 4 7 5-2 0-6-4-8-3h0v1l-1 2c1 1 1 2 2 3-1 0-2 1-2 1-2-3-4-5-7-6-1 0-4 1-5 0v-1h2 0v-1h-2v-1-1s-1 0-1-1h1z" class="J"></path><path d="M320 109h-2c1-1 2-1 4-1 1 0 2 1 2 2 1 0 1-1 2-1v1l1 1c1 1 1 2 2 3-1 0-2 1-2 1-2-3-4-5-7-6z" class="G"></path><path d="M315 104c5-1 9-1 13 2v1c-1-1-2-1-3-1l1 1h0v1h0c-1 0-3-1-4-1l-7-2v-1z" class="B"></path><path d="M325 135l-5-3c2 0 5 0 7 1h1c3 1 5 3 8 6v-1c0-1-1-2-2-3v-1c4 5 7 8 8 14 0 1 1 2 1 3h0c-2-3-4-6-7-9-1-1-1-1-2-1-3-1-4-2-7-3l-3-3h1z" class="N"></path><path d="M325 135c4 1 8 4 11 7-1-1-1-1-2-1-3-1-4-2-7-3l-3-3h1z" class="a"></path><path d="M333 67h1c1 1 1 1 2 1 0 1 0 1-1 2h-1v2c1 1 4 1 6 2 3 1 7 2 10 4-4 0-8-2-13-3-1-1-3-1-4 0h0v-1l-1-1c-4-1-6-1-10 0l-1-1h-2c0 1 0 0-1 0h0c2-1 2-1 4-1l1-2h0c-1 0-2 0-2 1h-1 0 0c0-1 1-2 2-2h3c3-1 6 0 8-1z" class="K"></path><path d="M322 71l1-2h0c-1 0-2 0-2 1h-1 0 0c0-1 1-2 2-2h3 3 0v1h2l-1 1h-1c-2 0-4-1-6 1z" class="T"></path><path d="M227 160l3-1-1 1c0 1 1 2 2 2v1l-1 1h-1-11-9-12l30-4z" class="d"></path><path d="M227 160l3-1-1 1c0 1 1 2 2 2v1l-1 1c-1-2-3-2-4-3l1-1z" class="W"></path><path d="M209 164h-3c2-1 4-1 6-1 4-1 8-1 11-1h1v1c-1 1-4 1-6 1h-9z" class="U"></path><defs><linearGradient id="C" x1="390.555" y1="601.988" x2="402.824" y2="584.887" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#3b3b3c"></stop></linearGradient></defs><path fill="url(#C)" d="M403 585h1l2-1v1c-3 8-7 15-15 19l-4-4c0-1 1-2 2-2 0-1 2-2 2-3 1 0 2 0 3 1 1 0 2 0 2-1 3-1 6-5 7-8v-1-1h0z"></path><path d="M233 596h2l-1 2 1 1-1 3-7 6h0v1-1h2c-8 4-17 8-26 10-1 0-3 1-4 1-1-1-1-1-2-1 7-1 12-3 19-6 4-2 7-5 10-8 1 0 1 1 2 1 2-3 4-6 5-9z" class="g"></path><path d="M233 596h2l-1 2 1 1-1 3-7 6c-2 0-3 1-5 1 2-1 4-2 5-4h1c2-3 4-6 5-9z" class="f"></path><path d="M260 139h1l1 1 1-2c-1 2-2 4-2 7v1 2c0 1-1 2-1 3-1 2-2 3-3 4s-4 0-5 0c-2 0-2 0-3 1l-2-1c2-3 5-4 6-7l1-1v-1l1-3 1 2 1-1 1-3h0l2-2z" class="X"></path><path d="M255 148l1-2h1l1 2c-1 1-1 2-1 3v1l-1-1c0-1 0-2-1-3z" class="m"></path><path d="M260 139h1l1 1 1-2c-1 2-2 4-2 7l-2 3c0 2-1 2-2 3 0-1 0-2 1-3l1-3 1-6z" class="o"></path><path d="M259 145v2 1c0 2-1 2-2 3 0-1 0-2 1-3l1-3zm-5 2c1 2-1 3-1 5 1-1 2-3 2-4 1 1 1 2 1 3l1 1h-2c-1-1-2 1-2 1l-1 1v1c-2 0-2 0-3 1l-2-1c2-3 5-4 6-7l1-1z" class="l"></path><path d="M261 145v1 2c0 1-1 2-1 3-1 2-2 3-3 4s-4 0-5 0v-1l1-1s1-2 2-1h2v-1c1-1 2-1 2-3l2-3z" class="n"></path><path d="M252 154l2-1c2 1 0 2 3 2-1 1-4 0-5 0v-1z" class="h"></path><path d="M395 567l1-1s0-1 1-2h0v1c0 2 1 4 1 6 1 2 1 5 0 6 0 2-1 4-1 6-2 2-3 5-5 7s-6 5-6 8l-1-1-3-4c3-2 6-4 8-6 5-6 6-13 5-20z" class="Y"></path><path d="M310 68l1 1c0 1-2 3-2 4-1 1-2 1-2 2s-1 1-1 1h-2c-3 3-5 6-7 9 0 1-1 3-3 3h0l-1-4c0-2-2-3-4-5l1-1h2c6-2 12-6 18-10z" class="b"></path><path d="M326 97l-1-1s0-1 1-1h2c0 1 1 1 2 2h1v1l6 3c-1 0-2-1-3-1l1 2v1c1 1 1 2 2 3 6 6 14 12 16 21l-3-5c-2-4-5-7-8-10-1 0-2-1-3-2-2 0-3-1-4-2l-6-3-2-2c0-1 0-1-1-2v-1c-1 0-2 0-2-1-1 0-2 0-3-1h0c1-1 2-1 3 0h1v-1h1z" class="G"></path><path d="M326 97l-1-1s0-1 1-1h2c0 1 1 1 2 2h1v1l6 3c-1 0-2-1-3-1l1 2v1c1 1 1 2 2 3-3-1-6-3-8-4 0-2 0-2 1-3v-1c-1-1-3-1-4-1z" class="m"></path><path d="M331 98l6 3c-1 0-2-1-3-1l1 2v1l-3-3c-1 0-2-1-2-2h1z" class="q"></path><path d="M388 410v-1h0c1-1 1-2 1-3-1 0-1 0-1-1l1 1 1 3c0 5 0 10-1 15l-1 5-1 2v2 2c-1 0-1 1-1 2s0 3-1 4c-1-1-1-3 0-5l-1-7-1-4v-3h0 1c0-1 0-1 1-2v-3h0c1-2 0-6 0-8v-2 1c1 1 1 3 2 4v-4 2h1z" class="b"></path><path d="M389 424h-1v-2c1-4 0-8 1-13h1c0 5 0 10-1 15z" class="O"></path><path d="M387 410h1v9c0 3-2 5-2 8-1 2 0 7-1 9l-1-7-1-4 1 1c3-5 4-11 3-16z" class="C"></path><path d="M383 422h0 1c0-1 0-1 1-2v-3h0c1-2 0-6 0-8v-2 1c1 1 1 3 2 4v-4 2c1 5 0 11-3 16l-1-1v-3z" class="Z"></path><path d="M94 339h2v-1 1c2-1 3-2 4-3 0 0 0 1-1 2-1 2-3 3-4 5l1 1-5 8c-1 2-2 3-3 5 0 1-1 2-2 4h-1l1-2h0 0c-1-1-1-2-2-2l-4 6v-1c-1 0-1 1-2 1 1-1 2-3 3-5l7-13h-1 0c3-1 5-4 7-6z" class="c"></path><path d="M88 348l1-2c1-1 2-2 3-2h0l1-1c1 1 0 2 0 3-1 1-3 2-5 2z" class="a"></path><path d="M95 343l1 1-5 8c-1 2-2 3-3 5 0 1-1 2-2 4h-1l1-2h0 0c-1-1-1-2-2-2 0-1 0-2 1-2l3-7c2 0 4-1 5-2l2-3z" class="W"></path><path d="M85 355v1s0 1 1 1h0v-1h0l2-2c0-1 1-1 2-2h1c-1 2-2 3-3 5 0 1-1 2-2 4h-1l1-2h0 0c-1-1-1-2-2-2 0-1 0-2 1-2z" class="l"></path><path d="M342 154l1 5c4 1 7 1 10 2h1 0l-11-1h0c0 2 1 2 1 3-1 1-21 1-24 1h-3c-1-2-5-2-6-3l4-1h3 0v-1h4 11 1 6l1-1c0 2 0 3 1 5 1-1 0-6 0-8v-1z" class="Y"></path><path d="M334 159h6v3h-1l-1-2v1h-1-1-8c-4 0-9 0-13-1h3 0v-1h4 11 1z" class="o"></path><path d="M120 288l1-1v2s-1 0-1 1c1 0 2 0 3-1 0 1 1 1 2 1l-3 3v1c1 0 2-1 3-1 1-1 1 0 2 0-4 2-7 3-11 4-1 0-2 1-4 1h0-2v1h1v2l1 1c-1 0-1 1-1 1-2 0-7 6-9 8h0l8-10c-2-1-5-1-7-2s-3-3-4-4h3c2 1 4 1 6 1v-1c4-1 5-4 7-6h3l1-1h1z" class="J"></path><path d="M122 294c1 0 2-1 3-1 1-1 1 0 2 0-4 2-7 3-11 4-1 0-2 1-4 1h0v-1c-2 0-5 2-7 1-1 0-2 0-3-1h0 5l2-1 2-1c0 1 1 1 1 1 2 0 6-1 7-3h1v1h2z" class="W"></path><path d="M120 288l1-1v2s-1 0-1 1c1 0 2 0 3-1 0 1 1 1 2 1l-3 3v1h-2v-1h-1c-1 2-5 3-7 3 0 0-1 0-1-1l-2 1-1-1c4-1 5-4 7-6h3l1-1h1z" class="M"></path><path d="M115 289h3l1-1h1c-1 2-3 5-5 6-1 0-3 1-4 1l-2 1-1-1c4-1 5-4 7-6z" class="K"></path><path d="M137 263c-1 0-2 1-2 2h1c2-1 3-3 4-5 2-2 3-4 6-5-1 2-2 4-3 5-3 2-3 3-5 5-1 2-4 4-5 6h-1c0 1 0 3-2 3v1c-2 0-3 2-5 3l-5 4-1-1c-4 2-7 2-11 1 1-1 2-1 3-1 2 1 3-1 5 0 1-1 3-2 4-3-1 0-2 1-3 1h-1c2-3 6-3 8-5-1-1-1-1-1-2 1-1 3-2 4-3l1-1 6-6 1 1s1-1 2 0z" class="W"></path><path d="M128 268l1 1c1-1 2-1 2-2l2-2c0 1-4 6-5 7-2 1-5 5-8 6-1 0-2 1-3 1h-1c2-3 6-3 8-5-1-1-1-1-1-2 1-1 3-2 4-3l1-1z" class="c"></path><path d="M129 273h0v-1c1-1 2-1 3-1 0 1 0 3-2 3v1c-2 0-3 2-5 3l-5 4-1-1c-4 2-7 2-11 1 1-1 2-1 3-1 2 1 3-1 5 0 4 0 9-5 13-8z" class="Q"></path><path d="M129 273h0v-1c1-1 2-1 3-1 0 1 0 3-2 3v1c-2 0-3 2-5 3l-5 4-1-1h0c1-1 3-2 4-2 2-1 5-5 6-6z" class="d"></path><path d="M105 323h0c3 0 3-2 5-3l1-1h2c-2 1-6 4-6 6l-10 11-3 3c-2 2-4 5-7 6h0c-5 4-9 9-13 15 4-12 15-20 23-28-3 0-7 0-10-1-2 0-3-1-5-2 2 0 4 1 6 1 6 1 12-3 17-7zm27-66c1 0 2 0 3-1l1 1v-1l1 1-3 5-6 6-1 1c-1 1-3 2-4 3-2 2-4 3-6 4v-1c1-1 2-2 2-4 1 0 2-1 3-2-5 2-10 3-14 4h0l1-1c4-1 7-4 10-6l-5 1c1-2 8-4 10-4l8-6z" class="Y"></path><path d="M132 257c1 0 2 0 3-1l1 1v-1l1 1-3 5-6 6-1 1c1-3 2-3 3-5 2-2 4-3 5-6v-1c-2 0-4 3-5 4-2 1-4 2-6 2h0l8-6z" class="U"></path><path d="M265 132h2 1c1 1 2 0 3 0 0 0 0 1 1 1 0 0 1-1 1 0l1 1 1 1c1 0 1 0 1-1l1 1v1 1c1 0 1 1 2 1v3l2 3v1 1l1 2c-1-1-2-2-3-4 0-1-1-1-3-1l-1-1h-1c0-1 0 0-1 0h-3v-2h0-1l-1-1v-1h-1v2h-1v-1c-1 1-2 3-2 4-1 1-2 3-3 5v-2-1c0-3 1-5 2-7l-1 2-1-1h-1l-2 2h0c0-2 0-4 1-6h1c0-1 1-2 1-3 1 1 2 1 2 2 1-1 2-1 2-2z" class="m"></path><path d="M269 140v-2l1-1v2c1-1 1-2 2-2v1l1 1c1-1 1-1 2-1l1 1h1s0 1 1 2c0 1 1 2 2 3h0c0 1 1 2 1 2l1 2c-1-1-2-2-3-4 0-1-1-1-3-1l-1-1h-1c0-1 0 0-1 0h-3v-2h0-1z" class="r"></path><path d="M265 132h2 1c1 1 2 0 3 0 0 0 0 1 1 1 0 0 1-1 1 0l1 1 1 1c1 0 1 0 1-1l1 1v1 1c1 0 1 1 2 1v3l-1-2s0-1-1-1v-1h-1v1l-1-1c-1 0-1 0-2-1h-1-2-1v-1l-1 1h-2c-1 0-1 2-1 2h-1v-2l-1 1v1l-1 2-1-1h-1l-2 2h0c0-2 0-4 1-6h1c0-1 1-2 1-3 1 1 2 1 2 2 1-1 2-1 2-2z" class="d"></path><path d="M265 132h2 1c1 1 2 0 3 0 0 0 0 1 1 1 0 0 1-1 1 0l1 1h0c-1 0-2 0-2-1l-1 1-1 1c-1-1-1-1-2-1l-1 1s-1 0-2-1c0 1-1 1-2 1-1 1-1 1-2 1-1 1-1 2-1 3l-2 2h0c0-2 0-4 1-6h1c0-1 1-2 1-3 1 1 2 1 2 2 1-1 2-1 2-2z" class="Z"></path><path d="M377 530h-1v-1h2v-1h-3v-1h2c0-1 1-1 2-1h1c2 1 5 0 7 0l16 3c9 4 16 8 21 17 0 1 1 2 1 4-4-5-6-11-11-14-3-2-6-4-10-5 7 4 11 8 13 16h0c-1-1-1-3-2-5-4-5-11-11-18-13-4-1-8-2-12-2l12 3c7 3 14 7 17 14 2 2 3 6 3 9h0c-1-2-1-3-2-5-1-3-3-6-6-9-6-7-14-8-23-9-1-1-6-1-7 0h0-2z" class="D"></path><path d="M114 299h0l12-3-5 8h0-1c-1 0-3 4-4 4h-1c-1 2-2 2-4 3h0l-3 6c-1 1-2 2-4 3-2 2-4 5-7 5v-1c5-3 7-8 10-14l4-7s0-1 1-1l-1-1v-2h3z" class="M"></path><path d="M111 299h3 0v1c0 1 0 1-1 1 0 1 0 2-1 3 0 2-1 3-3 5v1l-1 1c-1 0-1 0-1-1l4-7s0-1 1-1l-1-1v-2z" class="H"></path><path d="M114 299h0l12-3-5 8h0-1c-1 0-3 4-4 4h-1c-1 2-2 2-4 3 1-3 2-5 4-7l1-1c1 0 1-1 1-1l-1-1c0 1 0 1-1 1 0 0-1 0-2 1l-1 1c1-1 1-2 1-3 1 0 1 0 1-1v-1h0z" class="L"></path><path d="M269 151c3 0 11-1 14 1 1 4-9 7-9 11l1 2h0-3-7l3-1c2 0 2-1 2-2 0-3-6-5-9-7v-3c1-1 6-1 8-1z" class="D"></path><path d="M267 152h10c2 0 3 0 5 1h0c-3 2-5 4-7 6l-2 3-1-5c-3-1-5-1-7-1-1-1-2-1-3-2l1-1c1-1 3-1 4-1z" class="s"></path><path d="M262 262h1c-1 3-1 6-2 9 0 2-1 5 0 7h0v1l-1-1v-4h0l-1 1h0c-1 4-6 5-7 8-3 10-8 18-17 22-10 6-21 6-32 4l2-1h0c-3-1-6-2-9-4-2-1-4-3-6-4s-5-3-6-5c2 0 4 3 6 4 1 0 2 2 4 2l1 1 2 1c1 1 3 2 4 2 1 1 3 2 4 1h1c9 3 16 4 25 0 3-1 9-4 10-7-5 4-12 6-19 6h0c1-1 3-1 5-1 2-1 5-1 7-2 5-2 11-6 14-11 1-1 1-3 2-4v-1c0-1-1-1 0-1v-1c1-1 0-2 0-2l1-1h0c3-2 6-4 7-6 2-4 2-6 2-10l1-1c0-1 1-1 1-2z" class="K"></path><path d="M197 104l6-3 1 1c0 1 0 1-1 1v1h2 1l-1 1h2v1l-1 1c-1 1-1 2-3 3h-1l-2 3-1 1c0 1-2 3-2 3 0-1 0-1-1-1l-1 1h-1c-1 2-3 4-3 6-1 1-1 2-2 3h0v-1-1c-2 0-2 1-4 1l3-6 1-2v-3h0c-1-1-2 0-3 1 1-2 3-3 5-5 0-1 0-1 1-2s2-2 4-3l1-1z" class="d"></path><path d="M194 117l4-4c1-1 1 0 2 0l-1 1c0 1-2 3-2 3 0-1 0-1-1-1l-1 1h-1z" class="f"></path><path d="M188 119c2-1 3-1 4-2 0-1 1-1 1-1-2 3-3 5-4 8-2 0-2 1-4 1l3-6z" class="F"></path><path d="M199 108l1 1-1 1c-1 1-2 1-2 2-1 1-2 3-4 4 0 0-1 0-1 1-1 1-2 1-4 2l1-2c2-2 4-4 6-5v-1c1 0 2-1 3-2l1-1z" class="R"></path><path d="M194 108c2 0 3 0 4 1-1 1-2 2-3 2v1c-2 1-4 3-6 5v-3h0c-1-1-2 0-3 1 1-2 3-3 5-5h1l2-2z" class="m"></path><path d="M194 108c2 0 3 0 4 1-1 1-2 2-3 2v1c-2-1-2 0-3 0l1-2h-1l2-2z" class="W"></path><path d="M197 104l6-3 1 1c0 1 0 1-1 1v1h2 1l-1 1h2v1l-1 1c-1 1-1 2-3 3h-1-1l-1 1-1-1 1-1-1-1-1 1c-1-1-2-1-4-1l-2 2h-1c0-1 0-1 1-2s2-2 4-3l1-1z" class="X"></path><path d="M201 105c1 0 2 1 2 1l-3 3v-1h-1l-1 1c-1-1-2-1-4-1l2-1c1 0 2-1 3-1 1-1 1-1 2-1z" class="a"></path><defs><linearGradient id="D" x1="201.134" y1="101.686" x2="194.141" y2="109.222" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#7e7c7e"></stop></linearGradient></defs><path fill="url(#D)" d="M197 104l6-3 1 1c0 1 0 1-1 1v1h2 1l-1 1h2v1l-1 1-1-1-1 1v-1s1-1 1-2h-1c-1 0-2 0-3 1-1 0-1 0-2 1-1 0-2 1-3 1l-2 1-2 2h-1c0-1 0-1 1-2s2-2 4-3l1-1z"></path><defs><linearGradient id="E" x1="289.266" y1="81.073" x2="278.803" y2="91.497" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#2d2d2e"></stop></linearGradient></defs><path fill="url(#E)" d="M271 77v-1c-2-1-4-2-5-3 4 2 7 3 11 4 3 0 6 0 9 1h1c-2-1-3-1-4-2 1-1 5 1 7 2h0l-1 1c2 2 4 3 4 5l1 4c1 2 0 4-1 6v2h1c-1 1-1 3-2 4l-1 4-1-1-1-3v-4c0-2 0-2-1-3h0v3h-1v-3c-1-2-1-5-2-6s-1 0-2-1c-1-2-2-3-3-4s-2-2-3-1c-1-1-3-3-5-4h-1z"></path><path d="M293 96h1c-1 1-1 3-2 4v-1c-1-1-1-2-1-3h2z" class="G"></path><path d="M286 78h1c-2-1-3-1-4-2 1-1 5 1 7 2h0l-1 1c2 2 4 3 4 5l1 4c1 2 0 4-1 6v2h-2c1-2 1-3 1-4h1l-1-1v-3c0-2 0-4-1-5-1-2-2-4-3-4s-1-1-2-1h0zm-2 7c-2-2-3-3-4-5h0 0c2 1 3 1 4 1 3 3 4 4 5 8 1 2 0 4 0 7 0-2 0-2-1-3h0v3h-1v-3c-1-2-1-5-2-6s-1 0-2-1l1-1z" class="C"></path><path d="M284 85l1 1v-1c-1-1-2-2-2-3 1 0 3 2 4 4v1c0 2 1 4 1 6h0 0v3h-1v-3c-1-2-1-5-2-6s-1 0-2-1l1-1z" class="O"></path><path d="M148 232l1 3c0 2 1 4 1 6v3h1c-1 2-2 3-2 4l-3 7c-3 1-4 3-6 5-1 2-2 4-4 5h-1c0-1 1-2 2-2-1-1-2 0-2 0l-1-1 3-5-1-1v1l-1-1c-1 1-2 1-3 1l2-2c2-2 7-8 7-11 0-2 2-3 2-4l3-6 1-1h1v-1z" class="P"></path><path d="M148 232l1 3c0 2 1 4 1 6l-2 4h-2l1-2-1-1v-6h0v-1h1l-1-1 1-1h1v-1z" class="Y"></path><path d="M149 235c0 2 1 4 1 6l-2 4h-2l1-2c1-3 2-5 2-8z" class="X"></path><path d="M148 245l2-4v3h1c-1 2-2 3-2 4l-3 7c-3 1-4 3-6 5-1 2-2 4-4 5h-1c0-1 1-2 2-2-1-1-2 0-2 0l-1-1 3-5c3-2 4-6 6-8l3-4h2z" class="e"></path><path d="M146 245h2l-3 4v-1l-1 1h-1l3-4z" class="a"></path><defs><linearGradient id="F" x1="138.288" y1="250.256" x2="141.473" y2="259.661" xlink:href="#B"><stop offset="0" stop-color="#848284"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#F)" d="M143 249h1l1-1v1c-1 2-2 4-3 5-2 3-3 6-5 9-1-1-2 0-2 0l-1-1 3-5c3-2 4-6 6-8z"></path><path d="M194 117h1l1-1c1 0 1 0 1 1l-1 1c1 1 0 3-2 5-1 1-1 1-1 4v1c1-1 1-1 1-2h1 0c0 1-1 2-1 3s-1 3-2 4c-1 4-2 7-5 11 0 1-1 2-2 3v1l-2 1c-1 2-4 4-5 5s-2 3-3 4v-1c0-1 1-1 1-2v-2c1-2 4-4 5-7 1-2 1-4 1-7l2-8 1-6c2 0 2-1 4-1v1 1h0c1-1 1-2 2-3 0-2 2-4 3-6z" class="H"></path><path d="M186 133c1-1 2-1 2-3l1 1c-1 2-1 3-2 5l-1-1v-2z" class="L"></path><path d="M194 117h1l1-1c1 0 1 0 1 1l-1 1c-3 3-5 9-7 13l-1-1c0 2-1 2-2 3l-2-2 1-6c2 0 2-1 4-1v1 1h0c1-1 1-2 2-3 0-2 2-4 3-6z" class="X"></path><path d="M185 125c2 0 2-1 4-1v1l-4 7h1c1-2 2-4 3-5 0 1 0 2-1 3 0 2-1 2-2 3l-2-2 1-6z" class="I"></path><path d="M146 255l3-7v3h0l-1 3c0 2-1 3-2 5s-2 5-4 7c-1 3-4 6-5 9-1 0-1 1-2 2h1c-2 2-3 3-5 4-1 1-2 2-3 2-1 1-1 3-3 3-1 1-1 2-1 3h-1c-1 1-2 1-3 1 0-1 1-1 1-1v-2l-1 1h-1l-1 1h-3c1-3 3-5 5-7l5-4c2-1 3-3 5-3v-1c2 0 2-2 2-3h1c1-2 4-4 5-6 2-2 2-3 5-5 1-1 2-3 3-5z" class="a"></path><path d="M131 276h3l-5 4v-1c1-1 1-2 2-3z" class="E"></path><path d="M125 278h1c2 0 3-1 5-3h0 0v1c-1 1-1 2-2 3v1l-8 7-1 1h-1l-1 1h-3c1-3 3-5 5-7l5-4z" class="Y"></path><path d="M146 255l3-7v3h0c-1 0-1 0-1 1-2 7-7 13-11 20-1 1-2 3-3 4h-3v-1h0 0c-2 2-3 3-5 3h-1c2-1 3-3 5-3v-1c2 0 2-2 2-3h1c1-2 4-4 5-6 2-2 2-3 5-5 1-1 2-3 3-5z" class="Q"></path><path d="M133 271c1-2 4-4 5-6 2-2 2-3 5-5 0 2-1 3-2 5l-3 3c0 1-2 3-3 3-1 1-2 0-2 0z" class="J"></path><path d="M379 530h0c1-1 6-1 7 0 9 1 17 2 23 9 3 3 5 6 6 9 1 2 1 3 2 5h0s1 0 0 1v3h0c1 7-1 13-5 19l-1-1c2-3 4-5 4-8 1-3 2-8 1-11h0l-1-1c0-1 0-2-1-4s-1-4-3-5v-1c-2-3-6-5-10-7 1 1 1 2 2 3h0v3l2 1c2 4 6 8 7 14 0 1 0 2 1 2 0 1-1 3 0 4h-1c0-1 0-3-1-4 0-3-4-12-6-12-1-2-3-4-3-6h0l-1-1c-2-1-4-3-6-4-4-3-11-6-16-8z" class="a"></path><path d="M403 544c-2-3-4-5-6-7l-6-3 1-1c3 1 7 3 9 5 1 1 1 2 2 3h0v3z" class="e"></path><path d="M409 539c3 3 5 6 6 9 1 2 1 3 2 5h0s1 0 0 1v3-2c-1-1-1-1-1-2s-1-2-1-3c-2-4-4-7-8-10l2-1z" class="h"></path><path d="M386 530c9 1 17 2 23 9l-2 1c-4-3-9-5-14-7-1-1-3-1-4-2-1 0-2 0-3-1h0z" class="e"></path><path d="M370 535l5 1c6 0 14 5 18 9 2 2 4 5 6 7l-1 1 1 3 2 10s0 1-1 1h-1l-2-2v-1h0c-1 1-1 2-1 2l-1 1c-1-7-2-12-6-17-5-7-11-9-19-10v-5z" class="U"></path><path d="M375 536c6 0 14 5 18 9 2 2 4 5 6 7l-1 1 1 3h-1c0 1 1 4 0 5-1-1-1-3-1-5-1-2-2-4-3-7h0c-4-6-13-10-19-13z" class="V"></path><path d="M377 519c2-1 5-1 7-2 2 0 3-1 5-1 2-1 4-1 7-2h0 0l-1 1c-2 0-4 1-6 1-2 1-3 2-5 2-2 1-4 1-6 1-1 1-2 1-3 2 1 1 6-1 8-1 3-1 7-2 11-2h0c3 0 7 2 10 2h-2c0 1 0 2-1 2h0c-1 1-3 0-4 0-2 0-4 1-6 1 1 1 2 1 3 1 3 1 5 2 7 3 1 1 1 2 2 2-6-1-11-2-16-3-2 0-5 1-7 0h-1c-1 0-2 0-2 1h-2v1h3v1h-2v1h1-2c-1 1-2 0-3 0h0c-1 1-1 0-2 1v-8l1-2c2-1 4-1 5-2h1z" class="S"></path><path d="M401 522c0-1-1-1-1-1-5-1-12 0-17 1-2 0-7 2-8 1l12-3c2-1 5-2 7-2 3 0 7 2 10 2h-2c0 1 0 2-1 2z" class="r"></path><path d="M380 526c1-1 3-1 4-2h0c-2 0-4 1-6 1h0c2-2 5-2 8-3h15 0c-1 1-3 0-4 0-2 0-4 1-6 1 1 1 2 1 3 1 3 1 5 2 7 3 1 1 1 2 2 2-6-1-11-2-16-3-2 0-5 1-7 0z" class="e"></path><path d="M192 153c0 1 0 2-1 2l-3 3h1l40-1 8-1 1 1v1h0c-1 1-2 1-3 1h0-5l-3 1-30 4h-7-4v-1l-1 1h-7c1-2 3-4 4-6l3-2v1 1h0c3-1 5-3 7-5z" class="U"></path><path d="M229 157l8-1 1 1v1h0c-1 1-2 1-3 1h0l-1-1c-4 0-9 1-13 1h-26c-2 0-5 0-7 1-2 0-5 2-6 3v-1c1-2 4-3 6-4h1l40-1z" class="a"></path><path d="M229 157l8-1 1 1v1h0c-1 1-2 1-3 1h0l-1-1c-2-1-5 1-6-1h1z" class="k"></path><path d="M374 399h-1c-1-3-3-6-4-10 4 3 8 10 10 14 1 1 1 1 1 2 1 0 1 1 1 1h1c0-1-1-1-1-2v-2h1 0c1 2 2 4 3 5v2c0 2 1 6 0 8h0v3c-1 1-1 1-1 2h-1 0v3l1 4v3c-1-1-1-1-1-2h0 0 0v-3c0-1-1-2-2-2 0-2-1-3-2-4-1-3-4-5-7-7-1-1-1-1-1-3h0l6 5c-1-2-2-3-3-5-1-1-3-2-3-4-2-2-1-7 0-9l1 3c1 1 2 2 2 3h1 0c0-1-1-1-1-1v-4z" class="U"></path><path d="M374 399c2 6 4 12 5 18-1-3-2-5-3-8s-3-5-4-8c1 1 2 2 2 3h1 0c0-1-1-1-1-1v-4z" class="q"></path><path d="M382 402h0c1 2 2 4 3 5v2c0 2 1 6 0 8h0v3c-1 1-1 1-1 2h-1 0l-1-8c-1-4-2-7-3-11 1 1 1 1 1 2 1 0 1 1 1 1h1c0-1-1-1-1-2v-2h1z" class="c"></path><path d="M381 402h1c0 3 3 8 1 11 0 1 0 1-1 1-1-4-2-7-3-11 1 1 1 1 1 2 1 0 1 1 1 1h1c0-1-1-1-1-2v-2z" class="h"></path><path d="M139 527h3c-1-1-3-1-4-1-4 0-7 0-10-1-5 0-9-2-13-3-7-2-14-6-21-10 2 0 3 1 4 2 2 0 3 0 5 1h0l1-1c2 1 4 1 6 2 1 0 3 1 4 1v-1c4 1 7 4 11 4 2 0 3 1 5 2l13 1c4 1 10 1 15 1h8 5l2 1c-5 1-11 2-16 2h-6c-3 1-9 1-12 0z" class="P"></path><path d="M104 514c2 1 4 1 6 2 1 0 3 1 4 1v-1c4 1 7 4 11 4 2 0 3 1 5 2-5 0-9-1-14-3-4-1-8-3-13-4l1-1z" class="J"></path><path d="M138 290c2 0 4-2 6-3h0c0 1-1 2-2 2l1 1c-1 0-1 0-1 1v-1l-1 1c-2 1-4 2-6 4 0 1-1 1-2 1s-2 2-3 2c-1 1-3 2-3 3 0 0 0 1-1 1l1 1-3 3c0 1 0 1-1 1-2 2-3 4-4 6l-7 9 4-2v1c-1 1-1 2-2 3s-1 2-2 2c0 1-4 8-5 8v-1l-1 2c-1 0-1-1-1-1l-1 1-1-1c-1 0-2 1-3 2s-2 2-4 3v-1 1h-2l3-3 10-11c0-2 4-5 6-6h-2l-1 1c-2 1-2 3-5 3h0c1-1 3-2 4-4 5-4 8-10 12-15h0c2-3 3-5 5-8 1 0 4-2 5-3 2-1 5-2 7-3z" class="T"></path><path d="M126 302l1 1-3 3c0 1 0 1-1 1-2 2-3 4-4 6l-7 9 4-2v1c-1 1-1 2-2 3s-1 2-2 2c0 1-4 8-5 8v-1l-1 2c-1 0-1-1-1-1l-1 1-1-1c-1 0-2 1-3 2s-2 2-4 3v-1 1h-2l3-3 10-11 2-2 3-3 5-6c1-1 2-4 4-6l5-6z" class="M"></path><path d="M97 336l1-1c1 0 2 0 3-1s2-1 3-2c0 1 0 1 1 2l-1 1-1-1c-1 0-2 1-3 2s-2 2-4 3v-1 1h-2l3-3z" class="f"></path><path d="M126 302l1 1-3 3c0 1 0 1-1 1-2 2-3 4-4 6l-7 9 4-2v1c-1 1-1 2-2 3s-1 2-2 2c-2 1-2 3-4 4 0-1 1-2 1-3 1-1 2-2 3-4-2 2-4 5-5 7h-1v-1-1c1-1 3-3 3-5l3-3 5-6c1-1 2-4 4-6l5-6z" class="E"></path><path d="M385 499c1 0 1 0 2-1-1 2-1 3-3 4-1 1-2 1-3 2h0l1 1c2-1 4-3 6-4l2-1v2l-8 5h0 0v1 1l8-3c14-3 28-4 40 4 4 3 8 6 11 10-3-3-6-5-9-7-8-5-18-7-28-6h-2c-5 1-11 2-15 4h-2 0c-2 0-4 1-5 2l-6 2h-3-1v-7h1c2 0 3-3 6-3 2 0 3-3 5-4 0-1 2-2 3-2z" class="O"></path><path d="M385 499c1 0 1 0 2-1-1 2-1 3-3 4-1 1-2 1-3 2h0c-3 3-6 4-9 5 1-1 3-3 5-4 2 0 3-3 5-4 0-1 2-2 3-2z" class="l"></path><path d="M388 501l2-1v2l-8 5h0l-8 3c-1 1-2 1-3 1 1-1 5-2 6-3 2-1 3-2 5-3s4-3 6-4z" class="e"></path><path d="M380 513l-1-1c1-1 2-1 3-1s2-1 3-2c4-2 8-2 13-3 1 1 2 1 4 1-5 1-11 2-15 4h-2 0c-2 0-4 1-5 2z" class="Z"></path><path d="M215 146h1c-1 1-3 1-4 2v1c2-1 3-2 5-3l1 2h1l-1 1c-1 1-2 1-3 2h3c2 1 4 0 6 0 0 1 1 1 1 1l1 1c3 2 7 2 11 2v1l-8 1-40 1h-1l3-3c1 0 1-1 1-2l2-1c1 0 2-1 4-1 1-1 3-1 5-2 2 0 3-1 4-1 2 0 4-1 5-2h3z" class="Y"></path><path d="M215 151h3c2 1 4 0 6 0 0 1 1 1 1 1h-11-2l3-1z" class="X"></path><path d="M192 153l2-1 2 2v1c-2 1-5 2-7 3h-1l3-3c1 0 1-1 1-2z" class="I"></path><path d="M217 146l1 2h1l-1 1c-1 1-2 1-3 2l-3 1h2c-3 1-6 1-9 1h0-1l8-4c2-1 3-2 5-3z" class="U"></path><path d="M215 146h1c-1 1-3 1-4 2v1l-8 4h-1-2l-5 2v-1l-2-2c1 0 2-1 4-1 1-1 3-1 5-2 2 0 3-1 4-1 2 0 4-1 5-2h3z" class="H"></path><path d="M196 154c1 0 1 0 2-1 0-1 1-1 2-2h1c1 1 2 1 2 2h-2l-5 2v-1z" class="C"></path><path d="M201 153l-1-2h1c1 1 2 1 2 2h-2z" class="I"></path><path d="M399 552l1 3 1-1c0 1 0 1 1 2v-1h0c0 1 1 1 1 2 1 2 2 5 2 8v6c1 1 1 4 1 5v2c1 2 0 4 0 6l-2 1h-1 0v1 1c-1 3-4 7-7 8 0 1-1 1-2 1-1-1-2-1-3-1 0 1-2 2-2 3l-1-1c-1 1-1 1-2 1l-1-1 1 1c0-3 4-6 6-8s3-5 5-7c0-2 1-4 1-6 1-1 1-4 0-6 0-2-1-4-1-6l2 2h1c1 0 1-1 1-1l-2-10-1-3 1-1z" class="W"></path><path d="M401 566c1 2 1 6 0 9v-2c0-2-2-4-2-6h1c1 0 1-1 1-1z" class="b"></path><path d="M397 565l2 2c0 2 2 4 2 6 0 5 0 10-3 14v-1c0-1 0-1 1-2 0-3 0-4-1-7 1-1 1-4 0-6 0-2-1-4-1-6z" class="r"></path><path d="M399 552l1 3 1-1c0 1 0 1 1 2v-1h0c0 1 1 1 1 2 1 2 2 5 2 8v6c1 1 1 4 1 5v2c1 2 0 4 0 6l-2 1h-1 0v1 1c-1 3-4 7-7 8 0 1-1 1-2 1-1-1-2-1-3-1 1-2 3-3 4-5l1 1c1-1 1-1 2-1h0c4-3 4-9 5-13 1-2 1-6 0-8v-3c-1-2-1-4-2-5 0-3-1-5-2-7l-1-1 1-1z" class="E"></path><path fill="#fff" d="M400 555l1-1c0 1 0 1 1 2v-1h0c0 1 1 1 1 2 1 2 2 5 2 8v6c1 1 1 4 1 5v2c1 2 0 4 0 6l-2 1h-1 0c-1-1 1-7 1-9 0-8-1-14-4-21z"></path><path d="M405 571c1 1 1 4 1 5v2c1 2 0 4 0 6l-2 1h-1c2-5 2-9 2-14z" class="V"></path><path d="M388 429h1l2-6c2-6 6-9 12-12 7-2 14-2 21 1 5 2 7 6 9 12l-5-5v-1c1 1 1 2 2 2 0-2-1-3-3-4-4-4-9-5-15-5 0 0-1 0-2 1 0 0-1 1-2 1s-1 0-2 1l1 1h-1l-3 3h-1 0v-1h0c-2 1-3 3-4 5 0 0 0 1-1 2 0 1-1 3-1 4h-1c-1 2-2 4-2 6v1c1 1 1 3 2 5v-6c1 1 0 4 2 5-1 1-1 2-1 2v-1h-1v-1l-1 1c0 1 0 2-1 4 0 2 1 4-1 6v-2c-1-2 0-3 0-5-1 1-1 1-1 2h-1v-3h-1v4h-1l-1-2c-1 1-1 3-1 4v1l-1-1c-1-2 0-5 0-7 1-1 1-3 1-4s0-2 1-2v-2-2l1-2z" class="D"></path><path d="M394 421l-1 2c0 2-1 3-1 5v1c-1 2 0 3-1 5 0 2 0 4-1 6h0c-1-5 0-10 1-15 1-1 1-3 2-4h1z" class="W"></path><path d="M393 421c1-2 2-3 3-4 4-4 11-6 16-6 0 0-1 0-2 1 0 0-1 1-2 1s-1 0-2 1l1 1h-1l-3 3h-1 0v-1h0c-2 1-3 3-4 5 0 0 0 1-1 2 0 1-1 3-1 4h-1c1-4 2-7 4-10-4 3-5 10-7 14 0-5 2-11 6-14v-1h0c-1 1-2 2-3 2l-1 2h-1z" class="q"></path><path d="M370 531c1-1 1 0 2-1h0c1 0 2 1 3 0h2 2c5 2 12 5 16 8 2 1 4 3 6 4l1 1h0c0 2 2 4 3 6 0 4 2 9 3 14 2 8 1 15-2 22v-1c0-2 1-4 0-6v-2c0-1 0-4-1-5v-6c0-3-1-6-2-8 0-1-1-1-1-2h0v1c-1-1-1-1-1-2l-1 1-1-3c-2-2-4-5-6-7-4-4-12-9-18-9l-5-1v-1-3z" class="Y"></path><path d="M404 555c4 7 3 16 2 23v-2c0-1 0-4-1-5v-6c0-3-1-6-2-8l1-2z" class="j"></path><path d="M370 534c9 0 17 2 24 8 5 4 8 8 10 13l-1 2c0-1-1-1-1-2h0v1c-1-1-1-1-1-2l-1 1-1-3c-2-2-4-5-6-7-4-4-12-9-18-9l-5-1v-1z" class="q"></path><path d="M293 147h4l9 5h2 0c2 0 3 0 5-1 1 1 1 1 2 1 0 0 2 0 2-1l1 1h0c2-1 4 0 6 0s6-1 8-1c1-1 1-1 1-2h1c0 1 1 1 1 2h0v1h-2v1c0 1-1 1-1 2h2c1 0 2 0 4-1h0l1 1v1h-1-4v1 1 1h-1-11-4v1h0-3l-4 1v-1c-2 0-2 0-4-1h-1c-3 0-5 1-7 1h-2c-1-1-1-2-2-3l1-1s0-1 1-1c-1-1-3-1-4-2v-1h2c-1-2-3-3-5-5h3 0z" class="W"></path><path d="M295 152c1 1 3 2 4 3 2 0 3 1 4 1h0c-1 1-1 1-2 1-1 1-3 1-4 1l1-1h2v-1h0c-1 0-2-1-3-1-1-1-3-1-4-2v-1h2z" class="J"></path><path d="M308 152c2 0 3 0 5-1 1 1 1 1 2 1 0 0 2 0 2-1l1 1h0c2-1 4 0 6 0l-16 3c-1-1-2 0-2 0-1 0-2-1-2-1 0-1 1-1 2-1h1s0-1 1-1h0z" class="B"></path><path d="M293 147h4l9 5h2 0 0c-1 0-1 1-1 1h-1c-1 0-2 0-2 1 0 0 1 1 2 1 0 0 1-1 2 0-2 0-3 1-5 1-1 0-2-1-4-1-1-1-3-2-4-3-1-2-3-3-5-5h3 0z" class="Q"></path><path d="M299 155c1 0 1-1 1-1h4s1 1 2 1c0 0 1-1 2 0-2 0-3 1-5 1-1 0-2-1-4-1z" class="V"></path><path d="M293 147h4l9 5c-2 1-6-2-9-3 0 1 1 1 2 2-1 0-5-3-6-4z" class="C"></path><path d="M304 158c6-2 13-4 20-5 3 0 8-1 11-2v1h-2v1c0 1-1 1-1 2h2c1 0 2 0 4-1h0l1 1v1h-1-4v1 1 1h-1-11-4v1h0-3l-4 1v-1c-2 0-2 0-4-1h-1c-3 0-5 1-7 1 0-1 3-2 5-2z" class="b"></path><path d="M334 155c1 0 2 0 4-1h0l1 1v1h-1-4v1 1 1h-1-11-4v1h0-3l-4 1v-1c-2 0-2 0-4-1h-1c-3 0-5 1-7 1 0-1 3-2 5-2v1c1-1 2-1 2-1 4 1 7 0 11 0l17-3z" class="M"></path><path d="M307 159h7 0s0 1 1 1h-2 0-2c-2 0-2 0-4-1z" class="l"></path><path d="M314 159h4v1h0-3l-4 1v-1h2 0 2c-1 0-1-1-1-1h0z" class="m"></path><path d="M329 157l5-1v1 1 1h-1c-1 0-2-1-3-1h0l-1-1z" class="O"></path><path d="M322 159h0c2-1 5-2 7-2l1 1h0c1 0 2 1 3 1h-11z" class="Y"></path><defs><linearGradient id="G" x1="72.297" y1="462.468" x2="90.221" y2="460.58" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#G)" d="M73 426c0-1 1-2 1-3v-4c0 1 1 1 0 2v12l1 22h1 0c0 1 0 2 1 3l3 13v2c0 1 1 1 1 2l1 3c0 2 2 6 3 7 0 0 1 1 2 1 0 0 0 2 1 2 1 3 3 6 5 9v2l-1 1 2 2v1l1 1h-1-1l-2-1-3-4c-20-22-17-45-15-73z"></path><path d="M75 455h1 0c0 1 0 2 1 3l3 13v2c0 1 1 1 1 2l1 3c-1 1-1 1-2 1-2-8-4-16-5-24z" class="m"></path><path d="M82 478c0 2 2 6 3 7 0 0 1 1 2 1 0 0 0 2 1 2 1 3 3 6 5 9v2l-1 1c-5-6-9-14-12-21 1 0 1 0 2-1z" class="j"></path><path d="M306 138h0v2h1c1 1 3 1 4 1 2-1 4-2 6-2v1h0 4c2 0 3 1 5 2h0v1c2 1 4 3 6 4 0 1 1 2 1 2 0 1 0 1-1 2-2 0-6 1-8 1s-4-1-6 0h0l-1-1c0 1-2 1-2 1-1 0-1 0-2-1-2 1-3 1-5 1h0-2l-9-5h-4 0l-2-2c1 0 2 0 3-1 0-1-1-1-1-2h0l1-1h2c2 0 4-1 6-2h-2v-1c2 0 4 1 6 0z" class="W"></path><path d="M308 150c1-1 1-1 1-2h1c1 0 2 0 3 1h-1 0v2h-2c0-1-1-1-2-1h0z" class="p"></path><path d="M296 141l-1 1h0c2 0 4-1 6-1 0 1-1 1-2 1-1 1-2 1-3 2h-2c1 2 4 2 6 3h1v1c-2-1-3-1-4-1h-4 0l-2-2c1 0 2 0 3-1 0-1-1-1-1-2h0l1-1h2z" class="V"></path><path d="M306 138h0v2h1c1 1 3 1 4 1h1c1 0 3-1 4-1v2h-2c-1 0-1 1-2 2h0-1l1 2c-1 0-2 0-3 1h-2-1s-1 0-2-1l1-1h-1-5l1-1h2c1-1 2-1 3-1-2-1-4 0-6-1 1 0 2 0 2-1-2 0-4 1-6 1h0l1-1c2 0 4-1 6-2h-2v-1c2 0 4 1 6 0z" class="n"></path><path d="M306 144h3v1h-1c-1 0-2 0-2-1zm2-3h1l1 1h4c-1 0-1 1-2 2h0-1c-1-1-3-1-5-1v-1s1 0 1-1h1z" class="e"></path><path d="M306 138h0v2h1c1 1 3 1 4 1h1c1 0 3-1 4-1v2h-2-4l-1-1h-1c-1 0-4-1-5-1 0-1 0-1-1-1h-2v-1c2 0 4 1 6 0z" class="r"></path><path d="M311 141c2-1 4-2 6-2v1h0 4c2 0 3 1 5 2h0v1c-1-1-3-1-5 0h0l1 1h1v1l-5 1h-3-1c1 1 3 1 3 2 0 0-1 1-2 1h-2c-1-1-2-1-3-1h-1c0 1 0 1-1 2-1-1-3-1-5-1v-1h3v-1h1 2c1-1 2-1 3-1l-1-2h1 0c1-1 1-2 2-2h2v-2c-1 0-3 1-4 1h-1z" class="j"></path><path d="M321 140c2 0 3 1 5 2h0v1c-1-1-3-1-5 0h0l1 1c-2 0-4-1-6-2 2 0 3-1 5-1h0v-1z" class="i"></path><path d="M322 144l-1-1h0c2-1 4-1 5 0 2 1 4 3 6 4 0 1 1 2 1 2 0 1 0 1-1 2-2 0-6 1-8 1s-4-1-6 0h0l-1-1c0 1-2 1-2 1-1 0-1 0-2-1h0c0-1 0-1-1-1v-1h1 2c1 0 2-1 2-1 0-1-2-1-3-2h1 3l5-1v-1h-1z" class="P"></path><path d="M328 147h1v1h1c1 1 1 1 1 2h-2c-1-1-2-1-3-1h0c0-1 1-1 2-2z" class="Y"></path><path d="M313 149h2c1 0 2-1 2-1 0-1-2-1-3-2h1 3 1c2 0 4 0 6 1v1c-3-1-3-1-6 0v1h3 0c-2 1-6 2-9 2 0-1 0-1-1-1v-1h1z" class="k"></path><path d="M93 529c-12-6-23-17-29-29-3-5-5-10-6-15-2-4-4-11-2-15 0-2 2-5 4-5 1-1 2-1 3 0 1 0 2 1 2 2v2-1c-1-1-1-1-1-2h-3c-1 0-2 1-2 2-2 2-1 7-1 10 4 18 17 29 32 38-1 0-1 1-1 1v1 1h-2 0c2 2 5 3 7 4v1c-2 0-2 0-3-1h-1c1 1 3 2 4 3h1c1 1 3 2 4 3 2 0 7 2 8 4l-13-5c0 1-1 1-1 1z" class="O"></path><path d="M94 528l-3-2c-4-1-7-4-10-7-9-8-18-19-21-31 2 4 4 9 7 12 6 7 14 13 22 17v1 1h-2 0c2 2 5 3 7 4v1c-2 0-2 0-3-1h-1c1 1 3 2 4 3h1c1 1 3 2 4 3 2 0 7 2 8 4l-13-5z" class="s"></path><path d="M221 139h5c1 0 1-1 2-1h5 0l-2-2v-1h-1-1v-1h2c0 1 0 1 1 1s2 2 2 2c1 1 3 2 4 2v1c-1 0-3 0-3 1h0c1 0 3 0 4 1h-1 4 0c-2 1-4 2-6 2 2 1 4 1 6 0h1l2-1h1 1 0l2 1c1 1 2 1 3 2h2v1l-1 1c-1 3-4 4-6 7-2 1-4 0-5 1-1 0-3 0-4 1l-1-1v-1c-4 0-8 0-11-2l-1-1s-1 0-1-1c-2 0-4 1-6 0h-3c1-1 2-1 3-2l1-1h-1l-1-2c-2 1-3 2-5 3v-1c1-1 3-1 4-2h-1-3 0c2-2 5-2 8-3l-2-2c2 0 3 0 4-1l-1-1z" class="X"></path><path d="M245 143h1 1 0l2 1c1 1 2 1 3 2h2v1l-1 1c-2 1-4 3-7 4h-2c-1 1-2 1-4 1h-2 0c1-1 2-1 3-1l2-3h1c1-1 1-1 1-2-1 0-1-1-2-1 0-1 1-1 1-1l-1-1 2-1z" class="E"></path><path d="M245 147l2 2-6 3 2-3h1c1-1 1-1 1-2z" class="Q"></path><path d="M245 143h1 1 0l2 1c1 1 2 1 3 2v1h-1l-1 1c-1 0-2-1-3-1 0-1 0-1-1-2 0-1 0-1-1-2z" class="B"></path><path d="M247 143l2 1c1 1 2 1 3 2v1h-1c-1 0-2-1-2-2l-2-2z" class="K"></path><path d="M221 146v-1l1 1v1c2 0 5 1 7 0 2 0 5 0 6 1l6-1c0 1-1 1-1 1-1 2-1 3-3 4h-4c-3 1-5 0-7 1l-1-1s-1 0-1-1c-2 0-4 1-6 0h-3c1-1 2-1 3-2l1-1h-1l-1-2h1 3z" class="o"></path><path d="M218 151c2 0 3-1 4-1 2 2 5 1 8 1 1 0 2 1 3 1-3 1-5 0-7 1l-1-1s-1 0-1-1c-2 0-4 1-6 0z" class="c"></path><path d="M221 146v-1l1 1v1c2 0 5 1 7 0 2 0 5 0 6 1-1 0-3 1-4 1l-7 1h-2c-1 0-2 1-4 1h-3c1-1 2-1 3-2l1-1h-1l-1-2h1 3z" class="L"></path><path d="M218 149h2c1 0 1 1 1 1h3-2c-1 0-2 1-4 1h-3c1-1 2-1 3-2z" class="M"></path><path d="M221 146v-1l1 1v1c2 0 5 1 7 0 2 0 5 0 6 1-1 0-3 1-4 1-3-3-6 0-9 0-1 0-1-1-2-1 1-1 1-1 1-2h0z" class="R"></path><path d="M221 139h5c1 0 1-1 2-1h5 0l-2-2v-1h-1-1v-1h2c0 1 0 1 1 1s2 2 2 2c1 1 3 2 4 2v1c-1 0-3 0-3 1h0c1 0 3 0 4 1h-1 4 0c-2 1-4 2-6 2h-1l-1 1 1 1c1 0 3-1 5 0h-1c-3 2-6 0-10 1-2 1-5 0-7 0v-1l-1-1v1h-3-1c-2 1-3 2-5 3v-1c1-1 3-1 4-2h-1-3 0c2-2 5-2 8-3l-2-2c2 0 3 0 4-1l-1-1z" class="o"></path><path d="M224 140c3 0 6 0 8 1h1v1h-3c-1 0-3 1-5 0 0 0 0-1-1-2z" class="j"></path><path d="M223 143h6c-1 1-3 1-4 1-1 1-2 2-3 2l-1-1v1h-3-1c-2 1-3 2-5 3v-1c1-1 3-1 4-2h-1v-1c1 0 3-1 4-1 2-1 2-1 4-1z" class="M"></path><path d="M221 139h5c1 0 1-1 2-1h5 0l-2-2v-1h-1-1v-1h2c0 1 0 1 1 1s2 2 2 2c1 1 3 2 4 2v1c-1 0-3 0-3 1h0-3c-2-1-5-1-8-1-1 0-1 0-2 1v1l1 1c-2 0-2 0-4 1-1 0-3 1-4 1v1h-3 0c2-2 5-2 8-3l-2-2c2 0 3 0 4-1l-1-1z" class="i"></path><defs><linearGradient id="H" x1="74.608" y1="400.48" x2="69.329" y2="387.505" xlink:href="#B"><stop offset="0" stop-color="#343235"></stop><stop offset="1" stop-color="#545253"></stop></linearGradient></defs><path fill="url(#H)" d="M88 357c1 0 1 1 1 1-1 1-1 3-1 4-1 1-1 5-2 7l-2 4v1l-1 3c-1 2-1 4-2 6-1 3-2 7-3 12 0 1-1 2-1 4h1l1 3-1 5v-2h-1s0 2-1 3h0c-1 4-2 8-4 11-2 2-3 5-4 7l-1-1 1-2v-1c0-1 1-3 1-5 1-4 1-8 2-13 0-8 0-17 2-25 1-5 3-10 5-16 1 0 1-1 2-1v1l4-6c1 0 1 1 2 2h0 0l-1 2h1c1-2 2-3 2-4z"></path><path d="M74 382l1 1v3l2 2-2 8h-1c0-3 0-6-1-9 0-2 1-3 1-5z" class="J"></path><path d="M74 382l1 1v3l-1 3h0l-1-2c0-2 1-3 1-5z" class="k"></path><path d="M84 357c1 0 1 1 2 2h0 0l-1 2c-2 1-2 4-3 6l-3 6c0 2-1 3-2 5 0 2-2 3-2 5l-1-1 6-19 4-6z" class="n"></path><path d="M88 357c1 0 1 1 1 1-1 1-1 3-1 4-1 1-1 5-2 7l-2 4v1l-1 3c0-1 0-2-1-3l-2 1h0l-1-2 3-6c1-2 1-5 3-6h1c1-2 2-3 2-4z" class="E"></path><path d="M82 367l1 1-3 7-1-2 3-6z" class="d"></path><path d="M85 361h1 0l-3 7-1-1c1-2 1-5 3-6z" class="o"></path><path d="M88 357c1 0 1 1 1 1-1 1-1 3-1 4-1 1-1 5-2 7l-2 4c0-4 2-6 3-10 0-1 0-1-1-2h0c1-2 2-3 2-4z" class="M"></path><defs><linearGradient id="I" x1="60.456" y1="407.383" x2="90.227" y2="386.011" xlink:href="#B"><stop offset="0" stop-color="#121213"></stop><stop offset="1" stop-color="#373736"></stop></linearGradient></defs><path fill="url(#I)" d="M79 373l1 2h0l2-1c1 1 1 2 1 3-1 2-1 4-2 6-1 3-2 7-3 12 0 1-1 2-1 4h1l1 3-1 5v-2h-1s0 2-1 3h0c-1 4-2 8-4 11-2 2-3 5-4 7l-1-1 1-2v-1c0-1 1-3 1-5 1-4 1-8 2-13 2-2 2-8 2-11 1 1 1 4 1 6h0c1-1 1-2 1-3l2-8-2-2v-3c0-2 2-3 2-5 1-2 2-3 2-5z"></path><path d="M79 373l1 2h0l-3 13-2-2v-3c0-2 2-3 2-5 1-2 2-3 2-5z" class="a"></path><path d="M77 399h1l1 3-1 5v-2h-1s0 2-1 3h0c-1 4-2 8-4 11-2 2-3 5-4 7l-1-1 1-2c2-3 3-6 4-8 2-6 3-11 5-16z" class="d"></path><path d="M79 402c1 2 1 3 1 5 0 3-1 6 0 8l2 15 1 9 1 7c1 0 1 2 1 3s1 3 0 5h0v3l-1 1c0 2 2 4 1 6 0 2 1 7 2 8l1 5-1 1-3-8v3h0l-1-1h-1c0-1 0-3-2-4v2c0 1 0 1 1 1v1l-1 1v-2l-3-13c-1-1-1-2-1-3h0-1l-1-22c1-2 1-4 1-6v-9-1l1-9c1-1 1-3 1-3h1v2l1-5z" class="J"></path><path d="M75 417l1-2h0l1 2c0 2 0 6-1 8v-7h-1v-1z" class="n"></path><path d="M77 443l2 18c0 1 0 3 1 4l-1 1c-1-4-1-8-2-12l-1 1h0v-5c1-2 1-5 1-7z" class="j"></path><path d="M76 455l1-1c1 4 1 8 2 12l1-1c-1-1-1-3-1-4l1-2v2c2 3 4 6 4 9h0v3h0l-1-1h-1c0-1 0-3-2-4v2c0 1 0 1 1 1v1l-1 1v-2l-3-13c-1-1-1-2-1-3z" class="W"></path><path d="M80 461c2 3 4 6 4 9h0v3h0l-1-1c0-1-1-3-1-4-1-2-1-4-2-7z" class="j"></path><path d="M78 451l1-2 1 1c0 2 2 4 2 6l3 8c0 2 1 7 2 8l1 5-1 1-3-8h0c0-3-2-6-4-9v-2c-1-2-1-5-2-8z" class="q"></path><path d="M75 418h1v7l1 18c0 2 0 5-1 7v5h-1l-1-22c1-2 1-4 1-6v-9z" class="h"></path><path d="M79 436h1c0 2 1 3 2 5 0-1 0-1 1-2l1 7c1 0 1 2 1 3s1 3 0 5h0v3l-1 1c0 2 2 4 1 6l-3-8c0-2-2-4-2-6l-1-1-1 2v-1-10-3l1-1h0z" class="p"></path><path d="M79 436h1c0 2 1 3 2 5v2c-1 2 2 8 0 9l-1-2c0-4-2-9-2-14h0z" class="Q"></path><path d="M82 441c0-1 0-1 1-2l1 7c1 0 1 2 1 3s1 3 0 5h0v3l-1 1c0 2 2 4 1 6l-3-8-1-6 1 2c2-1-1-7 0-9v-2z" class="a"></path><path d="M82 441c0-1 0-1 1-2l1 7c1 0 1 2 1 3s1 3 0 5h0c-2-3-2-7-3-11v-2z" class="G"></path><path d="M79 402c1 2 1 3 1 5 0 3-1 6 0 8l2 15 1 9c-1 1-1 1-1 2-1-2-2-3-2-5h-1c0-3 0-8-1-11 0-1-1-2-1-3h0v-5h0l-1-2h0l-1 2 1-9c1-1 1-3 1-3h1v2l1-5z" class="E"></path><path d="M76 408c1-1 1-3 1-3h1v2c-1 3-1 7-1 10h0l-1-2h0l-1 2 1-9z" class="m"></path><path d="M307 97l1-1c6-5 12-6 20-6 1 1 2 1 3 1h0c3 3 6 3 8 7 1 1 2 3 3 4v1l-1-1c-1 0-2 1-2 0l-2-1-6-3v-1h-1c-1-1-2-1-2-2h-2c-1 0-1 1-1 1l1 1h-1v1h-1c-1-1-2-1-3 0h0c1 1 2 1 3 1 0 1 1 1 2 1v1c1 1 1 1 1 2l2 2-1 1c-4-3-8-3-13-2h-1c0 1 1 1 1 1v1c-1 1-2 1-4 1h-2v-1h1c-2 0-2 0-3 1h-1l-1 1c-2 2-4 3-6 4l-2 1h0l-2 1-2 1v2h0l-3 3c-1 0-1 0-2-1l-1 1v-1h0c-1-2-1-3 0-4h0l-1-1v-2c2 0 3 0 4 1v1l1-1c1-2 2-3 3-4v-2l3-3c1 0 1-1 2-1-1 0-1-1-1-1h2c0-1 1-2 1-3 1 0 2 1 2 1 1-1 1-2 3-2l1-1z" class="d"></path><path d="M299 103c-1 0-1 1-1 1-1 1-3 4-4 5v-2l3-3c1 0 1-1 2-1z" class="f"></path><path d="M316 97c3-1 6-1 9 0v1h-1c-1-1-2-1-3 0h0-2v-1h0-3z" class="S"></path><path d="M297 107c1-1 3-2 4-2 0-1 1 0 1 0v1c-1 1-2 2-3 2v1 1c-1 1-1 1-2 1l1-1c0-1 0-2-1-3z" class="F"></path><path d="M291 114c1 1 2 1 2 1v2l-3 3c-1 0-1 0-2-1 1-1 2-3 3-5z" class="N"></path><path d="M286 112c2 0 3 0 4 1v1c-1 1-2 4-3 5-1-2-1-3 0-4h0l-1-1v-2z" class="I"></path><path d="M307 97c2 1 6-3 8-4h3c-1 2-2 3-4 3s-3 1-4 2l-6 3v-1l2-2 1-1z" class="E"></path><path d="M297 107c1 1 1 2 1 3l-1 1-1-1v1l1 1v1l-2 1-2 1v2h0v-2s-1 0-2-1c2-2 4-4 6-7z" class="I"></path><path d="M339 102v-1c-2-1-3-3-4-4h-1c-2-1-4-2-5-3l1-1c2 1 4 1 6 3 1 0 2 2 3 2 1 1 2 3 3 4v1l-1-1c-1 0-2 1-2 0z" class="e"></path><path d="M307 97l1-1c6-5 12-6 20-6 1 1 2 1 3 1h0c3 3 6 3 8 7-1 0-2-2-3-2-2-2-4-2-6-3-4-1-8-1-12 0h-3c-2 1-6 5-8 4z" class="S"></path><path d="M315 97h1 3 0v1h2c1 1 2 1 3 1 0 1 1 1 2 1v1c1 1 1 1 1 2l2 2-1 1c-4-3-8-3-13-2h-1c0 1 1 1 1 1v1c-1 1-2 1-4 1h-2v-1h1c-2 0-2 0-3 1h-1l-1 1c-2 2-4 3-6 4l-2 1h0v-1l-1-1v-1l1 1c1 0 1 0 2-1v-1-1c1 0 2-1 3-2v1h0c0-2 2-3 3-4v-1c1 0 1 0 2 1l2-2v-1h0c2-2 4-3 6-3z" class="k"></path><path d="M299 108l1 1c1 0 2-1 3-1h0 2c-2 2-4 3-6 4l-2 1h0v-1l-1-1v-1l1 1c1 0 1 0 2-1v-1-1zm10-7v-1h0 1 1c2 1 8 0 9 1h0c-1 1-1 0-1 1l-6 1c-1 1-2 1-3 1 0-1 1-1 2-2h0c-1 0-2-1-3-1z" class="X"></path><path d="M315 97h1 3 0v1h2c1 1 2 1 3 1 0 1 1 1 2 1v1h-3c1 1 3 1 3 2-2 0-5-1-7-1 0-1 0 0 1-1h0c-1-1-7 0-9-1h-1-1c2-2 4-3 6-3z" class="P"></path><path d="M315 97h1 3 0v1l-3 1c0-1-1-1-1-2z" class="b"></path><path d="M303 90c1-2 2-5 3-7 2-3 3-6 5-8l9-11c2-2 4-4 6-7 0-1 1-1 2-2 1-2 3-4 5-6 1 0 2 1 3 1v1c0 5-1 11-2 16h-1c-2 1-5 0-8 1h-3c-1 0-2 1-2 2h0 0 1c0-1 1-1 2-1h0l-1 2c-2 0-2 0-4 1h0c1 0 1 1 1 0h2l1 1c4-1 6-1 10 0l1 1v1 1c1 1 3 1 4 2 2 0 3 1 5 2 3 1 6 4 8 6-2-1-3-2-5-3-4-3-8-4-12-5-1 2-1 4-2 6 0-1-1-1-1-1h-1l-1 1h-2-4l-1-1h-2c-3 2-6 2-9 5h-1 0c-3 0-3 4-5 4 0 1 0 1 1 1l-1 1-1-1h0v-3z" class="U"></path><path d="M322 68c2-2 5-2 7-3 2 0 3 0 4 2-2 1-5 0-8 1h-3z" class="W"></path><path d="M324 65c1-2 3-2 5-3 2 0 3 1 4 1v1c-3 0-6 0-9 1h0z" class="J"></path><path d="M322 73c4-1 6-1 10 0l1 1c-5 0-12-1-16 1h0-1 1c1-1 3-2 5-2z" class="l"></path><path d="M329 58h1c1-1 2-1 3 0h0v1c0 1 0 2 1 3 0 1 0 1-1 1 0 0 0-1-1-1-2-1-4-1-6 0 1-2 2-2 3-4z" class="R"></path><path d="M324 75c3 1 5 1 8 3h1c-1 2-1 4-2 6 0-1-1-1-1-1h-1l-1 1h-2-4l-1-1h-2c-3 2-6 2-9 5h-1 0l-1-3c1-1 2-4 4-5 0 0 1-1 2-1 2-1 2-2 4-3 2 0 5 0 6-1z" class="G"></path><path d="M324 75c3 1 5 1 8 3-2 1-6 0-9 0l1-3z" class="l"></path><path d="M312 80s1-1 2-1c2-1 2-2 4-3 2 0 5 0 6-1l-1 3c-3-1-7 1-10 2l-1 1v-1z" class="o"></path><path d="M310 88h-1v-1c3-3 7-5 11-6l-1 2c-3 2-6 2-9 5z" class="j"></path><path d="M321 78c1 0 2 1 3 1h5c1 0 2 1 2 2h-1c-1 0-2-1-3-1h0-8c-1 0-2 1-3 1 0-2 4-2 5-3z" class="W"></path><path d="M320 81h7 1c1 0 1 1 2 1v1h-1l-1 1h-2-4l-1-1h-2l1-2z" class="m"></path><path d="M321 83h8l-1 1h-2-4l-1-1z" class="U"></path><defs><linearGradient id="J" x1="325.813" y1="138.417" x2="320.724" y2="134.068" xlink:href="#B"><stop offset="0" stop-color="#6f7071"></stop><stop offset="1" stop-color="#8a888a"></stop></linearGradient></defs><path fill="url(#J)" d="M324 120v-1c2 1 3 3 5 3 0 2 1 3 2 4 1 3 1 6 3 8v1c1 1 2 2 2 3v1c-3-3-5-5-8-6h-1c-2-1-5-1-7-1l5 3h-1l3 3c3 1 4 2 7 3 2 3 6 7 7 11 0 1 0 2 1 2v1c0 2 1 7 0 8-1-2-1-3-1-5l-1 1h-6v-1-1-1h4 1v-1l-1-1h0c-2 1-3 1-4 1h-2c0-1 1-1 1-2v-1h2v-1h0c0-1-1-1-1-2h-1s-1-1-1-2c-2-1-4-3-6-4v-1h0c-2-1-3-2-5-2h-4 0v-1c-2 0-4 1-6 2-1 0-3 0-4-1h-1v-2h0l-3-1h-1v-1c1-1 2-1 3-3h-1l1-1c-1 0-1 0-2-1v-1l3-1v-1l1-1 1-1 2-1c1 0 2 0 2-1h0-3c1 0 1 0 1-1 0 0-1-1-2-1 2 0 5 1 7 0-1 0-1 0-1-1 3 0 5 0 8 1l-1-1-1-1c2 0 3 1 4 1v-1z"></path><path d="M327 138c3 1 4 2 7 3 2 3 6 7 7 11 0 1 0 2 1 2v1c0 2 1 7 0 8-1-2-1-3-1-5l-1 1h-6v-1-1-1h4 1v-1l-1-1h0c-2 1-3 1-4 1h-2c0-1 1-1 1-2v-1h2v-1h0c1 0 2 1 3 2v-1s-1-1-1-2h0c-1-2-3-3-5-5-1-1-1-2-2-3-2-1-3-2-5-3h0 2v-1z" class="S"></path><path d="M327 138c3 1 4 2 7 3 2 3 6 7 7 11 0 1 0 2 1 2v1c0 2 1 7 0 8-1-2-1-3-1-5l-1-5c-2-5-8-11-13-14v-1z" class="R"></path><defs><linearGradient id="K" x1="311.88" y1="130.125" x2="310.099" y2="140.73" xlink:href="#B"><stop offset="0" stop-color="#cbcaca"></stop><stop offset="1" stop-color="#f6f5f5"></stop></linearGradient></defs><path fill="url(#K)" d="M307 127l1-1c1 1 1 1 2 1l-2 2h1 2c1 1 1 1 2 1 2 0 4 0 6 1h0v1h-3v1h3v1c-1 0-3 1-4 1h-1v1h3c-1 1-1 1-2 1 2 1 4 1 6 2h-4c-2 0-4 1-6 2-1 0-3 0-4-1h-1v-2h0l-3-1h-1v-1c1-1 2-1 3-3h-1l1-1c-1 0-1 0-2-1v-1l3-1v-1l1-1z"></path><path d="M307 127l1-1c1 1 1 1 2 1l-2 2h1 2l-3 1v1c1 0 2 0 2 1-1 0-3 0-4 1h1 1c-1 1-1 1-2 1v1c2 0 3 0 4 1-1 1-6-1-7 1h-1v-1c1-1 2-1 3-3h-1l1-1c-1 0-1 0-2-1v-1l3-1v-1l1-1z" class="F"></path><path d="M303 130l3-1 1 2c-1 1-1 1-2 1s-1 0-2-1v-1z" class="b"></path><defs><linearGradient id="L" x1="330.107" y1="125.748" x2="317.271" y2="129.325" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#L)" d="M324 120v-1c2 1 3 3 5 3 0 2 1 3 2 4 1 3 1 6 3 8v1c-1-1-3-2-5-3h0c-3-1-7-1-10-1-2-1-4-1-6-1-1 0-1 0-2-1h-2-1l2-2c-1 0-1 0-2-1l2-1c1 0 2 0 2-1h0-3c1 0 1 0 1-1 0 0-1-1-2-1 2 0 5 1 7 0-1 0-1 0-1-1 3 0 5 0 8 1l-1-1-1-1c2 0 3 1 4 1v-1z"></path><path d="M314 121c3 0 5 0 8 1 2 0 3 1 4 2l-4-1c-2 0-5 0-7-1-1 0-1 0-1-1zm-4 6c1 1 3-1 4-2 1 1 3 1 5 1 0 0 2 0 2 1-1 0-2 0-4 1-1-1-3 0-4 1h0v1c-1 0-1 0-2-1h-2-1l2-2z" class="m"></path><path d="M308 122c2 0 5 1 7 0 2 1 5 1 7 1-2 0-3 0-5 1v1c1 0 1 0 2 1-2 0-4 0-5-1-1 1-3 3-4 2-1 0-1 0-2-1l2-1c1 0 2 0 2-1h0-3c1 0 1 0 1-1 0 0-1-1-2-1z" class="M"></path><path d="M243 118l3 1 1 1 5 3 1 1 2 3 1 5v5c0 1 1 2 0 3v1 2h-1l-1 3h-2c-1-1-2-1-3-2l-2-1h0-1-1l-2 1h-1c-2 1-4 1-6 0 2 0 4-1 6-2h0-4 1c-1-1-3-1-4-1h0c0-1 2-1 3-1v-1c-1 0-3-1-4-2 0 0-1-2-2-2s-1 0-1-1h1c-1-1-2-1-3-1 0-1 0-1 1-2-1 0-1 0-2-1h1c0-1-1-1-1-2l-3-3 1-1h0-2c-1-1-3 0-4-1l-1 1-1-1h4 1 1l3-2h1c2 0 4-2 5-2h6l1-1h3z" class="Y"></path><path d="M245 127v-2h1c3 1 4 2 5 5l-1 2h0c-2-2-2-4-5-5z" class="k"></path><path d="M243 118l3 1 1 1 5 3 1 1 2 3 1 5v5c-1-3-2-5-2-7-1-2-2-4-3-5-3-4-7-5-12-6l1-1h3zm-9 6v-1c1-1 1-1 2-1l1 2h1s0-1 1-1h2v1h0c-1 2-2 4 0 7l1 1h1l2-5c3 1 3 3 5 5h0l-1 1s-1 1-2 1c-2 1-5 1-8 0-1 0-3-2-4-4v-1c-1-1-1-3-1-5z" class="e"></path><path d="M245 127c3 1 3 3 5 5h0l-1 1-1-1c-2-1-3 0-5 0l2-5z" class="m"></path><path d="M226 124l2 2c1 1 1 2 2 2v-4c1 0 1 0 1-1l1 1v1l2-1c0 2 0 4 1 5v1c1 2 3 4 4 4 3 1 6 1 8 0 1 0 2-1 2-1l1-1 1-2h0v3c1-1 0-1 2-1v-1l1-1c0 2 1 4 2 7 0 1 1 2 0 3v1 2h-1l-1 3h-2c-1-1-2-1-3-2l-2-1h0-1-1l-2 1h-1c-2 1-4 1-6 0 2 0 4-1 6-2h0-4 1c-1-1-3-1-4-1h0c0-1 2-1 3-1v-1c-1 0-3-1-4-2 0 0-1-2-2-2s-1 0-1-1h1c-1-1-2-1-3-1 0-1 0-1 1-2-1 0-1 0-2-1h1c0-1-1-1-1-2l-3-3 1-1z" class="U"></path><path d="M242 142c2-1 4-1 6-1h1c-1 1-2 1-2 2h-1-1l-2 1h-1c-2 1-4 1-6 0 2 0 4-1 6-2h0z" class="K"></path><path d="M232 124v1l1 5c1 2 3 4 5 5s5 2 7 2 4-1 7-1h1s0 1 1 2v1c-2 1-9 0-11-1-1 0-2-1-4-1-2-1-5-3-6-6-1-2-2-4-2-6l1-1z" class="h"></path><path d="M226 124l2 2c1 1 1 2 2 2v-4c1 0 1 0 1-1l1 1-1 1c0 2 1 4 2 6 1 3 4 5 6 6 2 0 3 1 4 1 0 0-1 1-2 0v1h-2c0 1 1 1 1 2 0 0-1 0-1 1-1-1-3-1-4-1h0c0-1 2-1 3-1v-1c-1 0-3-1-4-2 0 0-1-2-2-2s-1 0-1-1h1c-1-1-2-1-3-1 0-1 0-1 1-2-1 0-1 0-2-1h1c0-1-1-1-1-2l-3-3 1-1z" class="O"></path><path d="M151 244v5c0 1 1 2 1 3v3c1 2 1 5 2 7 0 1 0 3 1 4l1 3-1 2c1 2 1 2 0 4l-1 1-2 3h-1l-3 2-4 5v1c-2 1-4 3-6 3-2 1-5 2-7 3-1 1-4 3-5 3l-12 3h0-3-1v-1h2 0c2 0 3-1 4-1 4-1 7-2 11-4-1 0-1-1-2 0-1 0-2 1-3 1v-1l3-3c-1 0-2 0-2-1h1c0-1 0-2 1-3 2 0 2-2 3-3 1 0 2-1 3-2 2-1 3-2 5-4h-1c1-1 1-2 2-2 1-3 4-6 5-9 2-2 3-5 4-7s2-3 2-5l1-3h0v-3c0-1 1-2 2-4z" class="l"></path><path d="M151 244v5c-1 2-1 4-2 6s-2 3-2 5c-3 6-7 11-11 17h-1c1-1 1-2 2-2 1-3 4-6 5-9 2-2 3-5 4-7s2-3 2-5l1-3h0v-3c0-1 1-2 2-4z" class="I"></path><path d="M145 270h1l-5 7-5 5c-1 1-2 2-4 3l-2 1-2 2-3 3v-1c-1 0-2 0-2-1h1c1-1 2-3 4-3 3 0 8-5 10-7l7-9z" class="c"></path><path d="M145 270l3-5c0-1 1-3 1-5 0 1-1 1-1 2-1 3-4 8-7 10 2-3 4-6 5-8s2-5 3-7c1-1 1-3 2-5h1v3c1 2 1 5 2 7l-1 1-1-1v1c-1 0-2 1-3 1l-3 6h-1z" class="L"></path><path d="M152 255c1 2 1 5 2 7l-1 1-1-1v1c-1 0-2 1-3 1 2-3 3-5 3-9z" class="n"></path><path d="M136 282v2h1 0c0 1 0 1 1 2l-9 6c-1 0-1 1-2 1s-1-1-2 0c-1 0-2 1-3 1v-1l3-3v1l3-3 2-2 2-1c2-1 3-2 4-3z" class="J"></path><path d="M130 286l1 1h3c-1 1-2 2-4 3v-1-1h-2 0l2-2z" class="W"></path><path d="M136 282v2h1 0l-3 3h-3l-1-1 2-1c2-1 3-2 4-3z" class="l"></path><path d="M128 288h0 2v1 1s-1 1-1 2c-1 0-1 1-2 1s-1-1-2 0c-1 0-2 1-3 1v-1l3-3v1l3-3z" class="k"></path><path d="M125 291h1c-1 1-2 2-4 2l3-3v1z" class="a"></path><defs><linearGradient id="M" x1="149.416" y1="275.642" x2="141.784" y2="272.943" xlink:href="#B"><stop offset="0" stop-color="#2b2d2c"></stop><stop offset="1" stop-color="#535151"></stop></linearGradient></defs><path fill="url(#M)" d="M153 263l1-1c0 1 0 3 1 4h-1c0 1 0 1-1 1-3 8-8 14-15 19-1-1-1-1-1-2h0-1v-2l5-5 5-7 3-6c1 0 2-1 3-1v-1l1 1z"></path><path d="M136 282l5-5c0 1 0 1 1 2l-5 5h-1v-2z" class="m"></path><path d="M149 264c1 0 2-1 3-1v-1l1 1c-3 6-6 11-11 16-1-1-1-1-1-2l5-7 3-6zm-11 22c7-5 12-11 15-19 1 0 1 0 1-1h1l1 3-1 2c1 2 1 2 0 4l-1 1-2 3h-1l-3 2-4 5v1c-2 1-4 3-6 3-2 1-5 2-7 3-1 1-4 3-5 3l-12 3h0-3-1v-1h2 0c2 0 3-1 4-1 4-1 7-2 11-4 1 0 1-1 2-1l9-6z" class="h"></path><path d="M131 293c1-1 2-3 4-3h1c1-1 1-1 2-1v1c-2 1-5 2-7 3z" class="e"></path><path d="M147 281c2-3 4-6 5-9l1 2-2 5-3 2h-1z" class="E"></path><path d="M138 289c3-1 7-5 9-8h1l-4 5v1c-2 1-4 3-6 3v-1z" class="C"></path><path d="M155 266l1 3-1 2c1 2 1 2 0 4l-1 1-2 3h-1l2-5-1-2c1-1 2-4 2-6h1z" class="S"></path><path d="M155 271c1 2 1 2 0 4l-1-1v-2c0-1 1-1 1-1z" class="Y"></path><path d="M125 250c1-1 1-2 2-4h-1c-1 1-3 1-5 1h0c1-1 3-3 5-4 1-1 2-3 3-4l6-6c3-4 6-8 7-12-1 1-2 2-2 4-5 6-10 12-17 15h0c4-3 7-6 10-10-3 2-6 4-9 5 2-2 5-4 7-6 2-3 4-6 6-10 1-2 2-3 2-5-3 2-5 5-8 8 3-5 6-10 10-14 3 7 5 16 7 24v1h-1l-1 1-3 6c0 1-2 2-2 4 0 3-5 9-7 11l-2 2-8 6c-2 0-9 2-10 4h-2 0l1-1v-1h0 1c1 0 2-1 2-2h0 2c1 0 1-1 2-1v-1l2-1v-1-1h1c3-2 6-4 8-6h-2 0c-1 1-2 2-4 2 0 0-1 0-2-1 1-1 2-2 2-3z" class="U"></path><path d="M133 248h0c3-1 4-3 6-5 1-2 2-3 3-4-2 4-3 8-6 11l-1-1c-1 0-2 0-2-1z" class="M"></path><path d="M135 249l1 1c-4 4-9 9-14 10v-1-1h1c3-2 6-4 8-6h0c2 0 2-2 4-3z" class="p"></path><path d="M141 244c0 3-5 9-7 11l-2 2-8 6c-2 0-9 2-10 4h-2 0l1-1v-1h0 1c1 0 2-1 2-2h0 2c1 0 1-1 2-1 10-3 16-9 21-18z" class="h"></path><path d="M144 222h1c0 2 0 3-1 4-2 4-5 8-8 11l-4 4c-2 0-4 2-5 3l-3 3-1-1 1-1c2-1 3-3 5-5 3-3 6-5 8-8 2-2 4-5 6-8h1v-2z" class="e"></path><path d="M125 250h1c1 0 1-1 2-2h0l1-1c0-2 0-2 2-3h-2l1-1h1c2 0 1-1 1-2 1 0 2-2 3-2l6-7c2-2 3-4 4-6h1c0 2-2 5-3 6-1 2-2 2-3 4-2 2-4 4-6 7-1 0-1 1-2 2s-2 2-3 4c-2 1-3 3-4 4l8-5c0 1 1 1 2 1-2 1-2 3-4 3h0-2 0c-1 1-2 2-4 2 0 0-1 0-2-1 1-1 2-2 2-3z" class="F"></path><path d="M209 124c1 0 3 0 4-1h5l1 1 1-1c1 1 3 0 4 1h2 0l-1 1 3 3c0 1 1 1 1 2h-1c1 1 1 1 2 1-1 1-1 1-1 2 1 0 2 0 3 1h-1-2v1h1 1v1l2 2h0-5c-1 0-1 1-2 1h-5l1 1c-1 1-2 1-4 1l2 2c-3 1-6 1-8 3h0c-1 1-3 2-5 2-1 0-2 1-4 1-2 1-4 1-5 2-2 0-3 1-4 1l-2 1c-2 2-4 4-7 5h0v-1-1l-3 2v-1c0-1 5-5 6-6l2-1h0l-6 3c0-1 2-2 3-2 0-1 1-2 2-2l5-3v-2-1c1-1 2-2 2-3 1-2 3-4 4-6l3-3c1-1 2-1 3-2 0-1 1-2 3-3h0 0c1-1 1-1 3-1h0v-1h-3z" class="W"></path><path d="M217 132c3 0 6-1 9 0v1h-3 0-6v-1z" class="n"></path><path d="M216 126l3-1c1 0 3 1 4 1 1 1 1 2 2 3h-4-1-1c-1-1-1-1-1-2l-2-1z" class="e"></path><path d="M206 132c3-3 5-4 8-5 1-1 1-1 2-1l2 1c0 1 0 1 1 2h1v1l-7 1c-2 0-3 0-5 1h-2z" class="B"></path><path d="M209 124c1 0 3 0 4-1h5l1 1 1-1c1 1 3 0 4 1h2 0l-1 1c-3-1-4-1-6 0l-3 1c-1 0-1 0-2 1-3 1-5 2-8 5h-2s0-1-1-1h0c1-1 2-1 3-2 0-1 1-2 3-3h0 0c1-1 1-1 3-1h0v-1h-3z" class="S"></path><path d="M203 131h0c1 0 1 1 1 1h2 2c2-1 3-1 5-1l1 1h2 1v1h6 0l1 1c1 0 2-1 3 0 0 1-1 1-1 1-2 0-3-1-5 1h6l1 1h-1-5-2c-1 0 0-1-2-1h0c-3 1-5 1-8 2-1 1-1 1-2 1l-1 1h-1c0 1-1 0-1 1-1 0-2 1-3 1-1 1-2 1-3 2-2 1-3 2-5 3-1 0-4 2-4 3l-6 3c0-1 2-2 3-2 0-1 1-2 2-2l5-3v-2-1c1-1 2-2 2-3 1-2 3-4 4-6l3-3z" class="l"></path><path d="M212 134c2 1 5-1 8-1v1c-4 1-11 2-14 5-1 1-2 1-3 2v-2c1-1 2-1 3-2h0c1-2 2-2 4-2l2-1z" class="R"></path><path d="M203 131h0c1 0 1 1 1 1h2 2c2-1 3-1 5-1l1 1h2l-4 2-2 1c-2 0-3 0-4 2h0c-1 1-2 1-3 2v2l-5 3-4 2v-2-1c1-1 2-2 2-3 1-2 3-4 4-6l3-3z" class="D"></path><path d="M203 139c0-2 2-2 2-4h-1v-1c0-1 0-1 1-1v1h1c1 0 3 0 4 1-2 0-3 0-4 2h0c-1 1-2 1-3 2h0z" class="C"></path><path d="M208 132c2-1 3-1 5-1l1 1h2l-4 2-2 1c-1-1-3-1-4-1l2-2z" class="L"></path><path d="M196 140h1l4-4s1 0 2 1l-2 2h2 0v2l-5 3-4 2v-2-1c1-1 2-2 2-3z" class="S"></path><path d="M194 143l5-2-1 3-4 2v-2-1z" class="B"></path><path d="M203 139h0v2l-5 3 1-3c0-1 1-1 2-2h2z" class="V"></path><path d="M218 136c0 2-1 2 1 2l2 1 1 1c-1 1-2 1-4 1l2 2c-3 1-6 1-8 3h0c-1 1-3 2-5 2-1 0-2 1-4 1-2 1-4 1-5 2-2 0-3 1-4 1l-2 1c-2 2-4 4-7 5h0v-1-1l-3 2v-1c0-1 5-5 6-6l2-1h0c0-1 3-3 4-3 2-1 3-2 5-3 1-1 2-1 3-2 1 0 2-1 3-1 0-1 1 0 1-1h1l1-1c1 0 1 0 2-1 3-1 5-1 8-2z" class="c"></path><path d="M185 156c6-6 15-11 23-14h0c-1 2-3 2-5 3s-3 2-4 4c-2 0-3 1-5 2l-9 6v-1z" class="S"></path><path d="M208 142h0c1-1 2-1 3-2 4-1 6-1 10-1l1 1c-1 1-2 1-4 1-5 1-8 4-12 6-2 1-5 2-7 2 1-2 2-3 4-4s4-1 5-3h0z" class="C"></path><path d="M218 141l2 2c-3 1-6 1-8 3h0c-1 1-3 2-5 2-1 0-2 1-4 1-2 1-4 1-5 2-2 0-3 1-4 1l-2 1c-2 2-4 4-7 5h0v-1l9-6c2-1 3-2 5-2s5-1 7-2c4-2 7-5 12-6z" class="M"></path><defs><linearGradient id="N" x1="172.304" y1="502.432" x2="106.461" y2="545.933" xlink:href="#B"><stop offset="0" stop-color="#020202"></stop><stop offset="1" stop-color="#2b2a2c"></stop></linearGradient></defs><path fill="url(#N)" d="M89 518l5 2h1s0-1 1-1c7 2 14 5 22 6 7 1 14 2 21 2 3 1 9 1 12 0h6c5 0 11-1 16-2 4-1 8-1 12-1l1 1h1-5-1l-2 1c-1 1-2 1-3 1-2 1-16 2-17 3l-16 5c-4 1-8 1-12 2-13 1-27-1-38-8 0 0 1 0 1-1l13 5c-1-2-6-4-8-4-1-1-3-2-4-3h-1c-1-1-3-2-4-3h1c1 1 1 1 3 1v-1c-2-1-5-2-7-4h0 2v-1z"></path><path d="M89 518l5 2c5 2 10 5 15 6l19 3h1c3-1 8 1 11 1-3 1-6 2-9 2-1 1-9 0-11 0h0-5-1c2 1 5 1 8 2-3 0-14 0-15-1-1-2-6-4-8-4-1-1-3-2-4-3h-1c-1-1-3-2-4-3h1c1 1 1 1 3 1v-1c-2-1-5-2-7-4h0 2v-1z" class="K"></path><path d="M109 526l19 3v1c-7 0-14-1-20-3 0 0 0-1 1-1zm-15-3h0c2 1 9 5 10 5 3 0 7 2 11 3h-2c-2 0-5-2-7-2 0 0 0 1-1 1s-3-1-4-2c-2 0-4-2-6-2h-1c-1-1-3-2-4-3h1c1 1 1 1 3 1v-1z" class="n"></path><path d="M89 518l5 2c5 2 10 5 15 6-1 0-1 1-1 1-3-1-6-1-9-2l-8-4c-1-1-2-2-4-2h0 2v-1z" class="e"></path><path d="M95 526c2 0 4 2 6 2 1 1 3 2 4 2s1-1 1-1c2 0 5 2 7 2h2c1-1 2 0 3 0h8 1v1c-2 1-5-1-7 0h-5-1c2 1 5 1 8 2-3 0-14 0-15-1-1-2-6-4-8-4-1-1-3-2-4-3z" class="F"></path><path d="M310 68l9-8 9-9c1-1 2-3 4-4h3c1 2 1 2 1 4v-1c-1 0-2-1-3-1-2 2-4 4-5 6-1 1-2 1-2 2-2 3-4 5-6 7l-9 11c-2 2-3 5-5 8-1 2-2 5-3 7v3h0l1 1 1-1c-1 0-1 0-1-1 2 0 2-4 5-4h0 1c3-3 6-3 9-5h2l1 1h4c2 0 3 0 5 1v1 1 1 2 1c-1 0-2 0-3-1-8 0-14 1-20 6l-1 1-1 1c-2 0-2 1-3 2 0 0-1-1-2-1 0 1-1 2-1 3h-2s0 1 1 1c-1 0-1 1-2 1l-3 3v2c-1 1-2 2-3 4l-1 1v-1c-1-1-2-1-4-1v2c-1 1-1 1-2 1h0-1v1c-1 2-2 3-2 5h-1v-3c0-1 1-1 1-2v-3-2h0v-4l-1-1v-2-2h1 1l1 1h0v-4h1v-3l1-1v-4l2 2v3h1v-3h0c1 1 1 1 1 3v4l1 3 1 1 1-4c1-1 1-3 2-4h-1v-2c1-2 2-4 1-6h0c2 0 3-2 3-3 2-3 4-6 7-9h2s1 0 1-1 1-1 2-2c0-1 2-3 2-4l-1-1z" class="R"></path><path d="M319 83h2l1 1c-4 1-6 3-10 4l-3 3c-1 1-2 1-3 2s-1 3-3 3h-1c0-2 1-4 1-6v3h0l1 1 1-1c-1 0-1 0-1-1 2 0 2-4 5-4h0 1c3-3 6-3 9-5z" class="K"></path><path d="M328 90h2l-1-1c-1-1-5 0-7 0s-4 1-6 2c-4 1-5 4-8 5-1 0-1-1-1-1h-1v-1c4-3 7-5 11-7 1 0 2 0 3-1h6 5v1 1 2 1c-1 0-2 0-3-1z" class="D"></path><path d="M310 68l9-8 9-9c1-1 2-3 4-4h3c1 2 1 2 1 4v-1h0c0-1-1-1-1-2h-1c-2 0-5 3-7 5l-3 3-9 13c-2 1-3 2-4 4l-3 4v-1c0-1 1-1 1-2v-1c0-1 2-3 2-4l-1-1z" class="T"></path><path d="M309 73v1c0 1-1 1-1 2v1h0c0 1-1 2-1 2-1 2-2 3-3 5-2 4-2 7-3 11 0 1-1 2-1 3-1 0-2-1-2-2 0 0 0-1-1-1h0l-1 2-2 2s1-3 1-4c0 0-1 0-1-1v2h-1v-2c1-2 2-4 1-6h0c2 0 3-2 3-3 2-3 4-6 7-9h2s1 0 1-1 1-1 2-2z" class="C"></path><path d="M297 85c0 1-1 2 0 3 0 2-3 4-3 6v2h-1v-2c1-2 2-4 1-6h0c2 0 3-2 3-3z" class="K"></path><path d="M308 76v1h0c0 1-1 2-1 2-1 2-2 3-3 5-2 4-2 7-3 11 0 1-1 2-1 3-1 0-2-1-2-2 0 0 0-1-1-1h0l-1 2v-1c-1 0 0-1 0-2v-1c1-2 2-5 4-7l2-2 1-1c0-1 1-2 2-3s2-2 3-4z" class="g"></path><path d="M297 95c1 0 2-1 3 0h1c0 1-1 2-1 3-1 0-2-1-2-2 0 0 0-1-1-1z" class="D"></path><path d="M285 91l2 2v3h1v-3h0c1 1 1 1 1 3v4l1 3 1 1 1-4c1-1 1-3 2-4v-2c0 1 1 1 1 1 0 1-1 4-1 4l2-2 1-2h0c1 0 1 1 1 1 0 1 1 2 2 2-1 1-4 4-5 4v-1c-1 2-2 3-3 4l2 2v2c-1 1-2 2-3 4l-1 1v-1c-1-1-2-1-4-1v2c-1 1-1 1-2 1h0-1v1c-1 2-2 3-2 5h-1v-3c0-1 1-1 1-2v-3-2h0v-4l-1-1v-2-2h1 1l1 1h0v-4h1v-3l1-1v-4z" class="X"></path><path d="M285 102l2-1v3h0c-1 1-1 1-2 3 1-2 0-4 0-5z" class="c"></path><path d="M285 107h0c1 1 1 2 1 3h-2c-1-1-1-1-1-2s0-6 1-6c0-1 1-1 1-2v2c0 1 1 3 0 5z" class="k"></path><path d="M285 91l2 2v3h1v-3 4 1h-1v3h0l-2 1v-2-4-1-4z" class="E"></path><path d="M285 96v2h1v-2h1v2 3h0l-2 1v-2-4z" class="J"></path><path d="M280 104v-2h1 1l1 1c-1 2-1 5 0 6v1c1 1 1 2 1 3l-1 1v-1h-1c0 1 0 2 1 3-1 2-2 3-2 5h-1v-3c0-1 1-1 1-2v-3-2h0v-4l-1-1v-2z" class="k"></path><path d="M288 93c1 1 1 1 1 3v4l1 3 1 1 1-4c1-1 1-3 2-4v-2c0 1 1 1 1 1 0 1-1 4-1 4l2-2 1-2h0c1 0 1 1 1 1 0 1 1 2 2 2-1 1-4 4-5 4v-1c-1 2-2 3-3 4l2 2v2c-1 1-2 2-3 4l-1 1v-1c-1-1-2-1-4-1h0v-2h1s1 0 1-1c1-2 0-6-1-8v-3h1v-1-4h0z" class="F"></path><path d="M286 112c2 0 2 1 3-1 1 0 1 1 2 2l-1 1v-1c-1-1-2-1-4-1h0z" class="E"></path><path d="M289 100l1 3 1 1-1 1h-1 0v-5z" class="H"></path><path d="M294 94c0 1 1 1 1 1 0 1-1 4-1 4l2-2 1-2h0c1 0 1 1 1 1 0 1 1 2 2 2-1 1-4 4-5 4v-1c-1 2-2 3-3 4 0 3-2 5-3 6l1-6 1-1 1-4c1-1 1-3 2-4v-2z" class="a"></path><path d="M297 95c1 0 1 1 1 1 0 1 1 2 2 2-1 1-4 4-5 4v-1c0-1 0-1 1-2s2-2 2-3l-1-1h0z" class="I"></path><path d="M228 68c2 1 2 1 3 3h0v1c1 0 1 1 2 2l1 2h1c1-1 1-1 2-1h3 0 0c2 0 5 0 7 1-1-1-4-2-4-2 2 0 3 0 5 1h-1v1l3 3c0 1 1 2 2 3 0 1 1 2 2 3v1c0 1 1 3 2 4h0 1c0 1 1 2 2 3 1-1 1-1 2-3 0 1 1 2 1 4l-1-1c0 2 2 4 1 6 1 2 1 3 1 5l1 1v-1l1 2v1 1c0 1 0 1 1 2l1 7v6 2 1s-1 1-1 2v3l-1 1c0 1-1 1-2 2 0-1-1-1-2-2 0 1-1 2-1 3h-1c-1 2-1 4-1 6l-1 3-1 1-1-2h1v-2-1c1-1 0-2 0-3v-5l-1-5-2-3s1 0 1-1v-1c0-2-1-5-1-7l-2-7c-1-1-1-3-2-4-1-3-3-4-4-7h1c0-5-8-12-11-16-1-1-2-3-3-4v-1l-4-8z" class="C"></path><path d="M254 111h1v-3c1 2 1 4 2 5v2 4c-1-1-1-4-2-6 0 0-1-1-1-2z" class="J"></path><path d="M251 108h1c1 1 1 2 1 3 0 0 1 1 1 2 0 2 1 3 1 5h-1v-1c0-1-1-1-1-2l-2-7z" class="R"></path><path d="M228 68c2 1 2 1 3 3h0v1c1 0 1 1 2 2l1 2h1c2 1 3 2 4 3 3 1 5 4 6 6h0l-1 1-2-2c0-1-1-1-1-2l-1 1 4 5h-1c-3-3-5-6-8-9 0-1-1-2-1-3l-2 1v-1l-4-8z" class="H"></path><path d="M244 88l-4-5 1-1c0 1 1 1 1 2l2 2 1-1 1 2c2 2 3 5 5 7v-1-1h1c0 2 1 3 2 4 1 0 1 1 2 2-1 0-1 1-2 2v5h1 0v3 3h-1l-1-5-1-1c-1-2-1-4-2-5s-1-2-1-3-1-1-1-2l-1-2c0-2-2-3-2-4s-1-1-1-1z" class="L"></path><path d="M253 106c0-2 0-6-1-8h0-1l2-2c1 1 1 2 1 4v5h1 0v3 3h-1l-1-5z" class="X"></path><path d="M240 75c2 0 5 0 7 1-1-1-4-2-4-2 2 0 3 0 5 1h-1v1l3 3c0 1 1 2 2 3 0 1 1 2 2 3v1l-2-1h0c0 1-1 1-1 1l3 7c-1-1-2-2-3-2v1 1 1c-2-2-3-5-5-7l-1-2h0c-1-2-3-5-6-6-1-1-2-2-4-3 1-1 1-1 2-1h3 0 0z" class="B"></path><defs><linearGradient id="O" x1="237.829" y1="74.315" x2="246.274" y2="86.299" xlink:href="#B"><stop offset="0" stop-color="#020202"></stop><stop offset="1" stop-color="#2b2b2b"></stop></linearGradient></defs><path fill="url(#O)" d="M235 76c1-1 1-1 2-1h3 0 0v1c1 1 2 1 3 2l4 4c0 1 1 1 1 2s1 1 1 2h0-1l-2 1-1-2h0c-1-2-3-5-6-6-1-1-2-2-4-3z"></path><path d="M254 93l-3-7s1 0 1-1h0l2 1c0 1 1 3 2 4h0 1c0 1 1 2 2 3 1-1 1-1 2-3 0 1 1 2 1 4l-1-1c0 2 2 4 1 6 1 2 1 3 1 5l1 1c0 2-1 2-1 4l-1 4-1 4c0-1-1-2-1-3-1 2-1 4-1 5h-1 0c0-2 0-2-1-4v-2c-1-1-1-3-2-5v-3h0-1v-5c1-1 1-2 2-2-1-1-1-2-2-2-1-1-2-2-2-4h-1v-1c1 0 2 1 3 2z" class="f"></path><path d="M261 102h0c1 1 1 1 1 2h1l1 1c0 2-1 2-1 4l-1 4-1 1h0v-1-3c-1-3 0-5-1-7v-1h0 1z" class="c"></path><path d="M261 102h0c1 1 1 1 1 2h1l1 1c0 2-1 2-1 4l-2-7z" class="J"></path><path d="M256 98c0 1 0 2 1 2v-3l1-1c0 1 0 2 1 3v-1-2c-1-1-1-1-1-2 1 0 1 1 1 1 0 2 1 5 0 6v6c1 2 0 3 0 5 0 0-1 0-1 1v6h0c0-2 0-2-1-4v-2c-1-1-1-3-2-5v-3h0-1v-5c1-1 1-2 2-2z" class="Z"></path><path d="M255 105v-4h1c1 2 1 9 1 12h0c-1-1-1-3-2-5v-3z" class="a"></path><path d="M258 113v-13-1l1 2v6c1 2 0 3 0 5 0 0-1 0-1 1z" class="d"></path><path d="M254 93l-3-7s1 0 1-1h0l2 1c0 1 1 3 2 4h0 1c0 1 1 2 2 3 1-1 1-1 2-3 0 1 1 2 1 4l-1-1c0 2 2 4 1 6-1-1-2-3-3-4 0 0 0-1-1-1 0 1 0 1 1 2v2 1c-1-1-1-2-1-3l-1 1v3c-1 0-1-1-1-2-1-1-1-2-2-2-1-1-2-2-2-4h-1v-1c1 0 2 1 3 2z" class="I"></path><path d="M254 93l-3-7s1 0 1-1h0l2 1c0 1 1 3 2 4h0c1 1 1 2 1 4l-1 2h0c-1 0-2-2-2-3z" class="Y"></path><path d="M264 105v-1l1 2v1 1c0 1 0 1 1 2l1 7v6 2 1s-1 1-1 2v3l-1 1c0 1-1 1-2 2 0-1-1-1-2-2 0 1-1 2-1 3h-1c-1 2-1 4-1 6l-1 3-1 1-1-2h1v-2-1c1-1 0-2 0-3v-5l-1-5v-4l1-2s1-1 2-1v-1h0 1c0-1 0-3 1-5 0 1 1 2 1 3l1-4 1-4c0-2 1-2 1-4z" class="B"></path><path d="M258 119h1c0-1 0-3 1-5 0 1 1 2 1 3 0 2 1 4 1 6 0 1-1 3-1 5h-1c0-3 2-5 1-7h-1l-1 2h-1v-3-1h0z" class="I"></path><path d="M255 123l1-2s1-1 2-1v3c2 2 2 5 1 7v1 1h-3 0l-1-5v-4z" class="b"></path><path d="M255 123l1-2s1-1 2-1v3c2 2 2 5 1 7v1c-1-2 0-4-1-6s-1-2-3-2z" class="T"></path><path d="M263 131c-1-6 0-11 1-17 1 2 2 3 2 5v4h1v2 1s-1 1-1 2v3l-1 1c0 1-1 1-2 2 0-1-1-1-2-2 1 0 1-1 2-1z" class="R"></path><path d="M264 130h-1c0-1 1-4 1-5s0-1 1-2l1 1-1 4-1 2z" class="X"></path><path d="M266 119v4h1v2 1s-1 1-1 2l-1 1h0v-1l1-4v-5z" class="H"></path><path d="M265 128v1h0l1-1v3l-1 1c0 1-1 1-2 2 0-1-1-1-2-2 1 0 1-1 2-1h0l1-1 1-2z" class="I"></path><path d="M188 524h4c2 1 4 1 5 2h2c1 1 2 1 2 2h2s1 0 1 1h2c1 1 1 1 2 1s1 0 1 1l1-1h0l1 1 3 1c1 2 2 3 3 4 2 2 4 3 5 4 3 2 5 4 7 6 1 1 1 1 2 1l1 2c4 4 6 9 8 14h-2c-2-1-3-5-4-7s-2-5-4-6l-1 1-5-5c-1-1-2-1-3-2l-1-1v1h0c-1-1-2-2-4-3l-6-3-2-2c-9-3-17-5-27-5h-7c-1 1-3 1-5 2 2 0 4-1 7-1 5-1 12-1 18 1l2 1h0-2c-2-1-5-1-8-1-6 0-13 0-19 2-5 1-9 4-13 6h4c1-1 1-1 2 0l-1 1-4 2c-1 1-3 2-4 2-2 0-3 2-4 3-5 4-8 9-11 15h0c-1-1-1-1-1-2l-3 8v-2l-1-1c1-3 1-8 3-10 1-1 1-1 1-2h-1c-2 3-5 8-6 12-1 1-1 3-2 5v-1-3l3-6c0-2 2-4 2-6l-1-1c-1 2-2 4-4 5h0c3-9 11-17 19-22 2-2 6-3 9-5 2 0 5-1 6-2l-1-1c1-1 15-2 17-3 1 0 2 0 3-1l2-1h1 5-1l-1-1h3z" class="W"></path><defs><linearGradient id="P" x1="146.109" y1="560.17" x2="142.11" y2="544.609" xlink:href="#B"><stop offset="0" stop-color="#030600"></stop><stop offset="1" stop-color="#302f34"></stop></linearGradient></defs><path fill="url(#P)" d="M154 541h4c1-1 1-1 2 0l-1 1-4 2c-1 1-3 2-4 2-2 0-3 2-4 3-5 4-8 9-11 15h0c-1-1-1-1-1-2l1-3c3-8 10-15 18-18z"></path><path d="M188 524h4c2 1 4 1 5 2h2c1 1 2 1 2 2h2s1 0 1 1h2c1 1 1 1 2 1s1 0 1 1l1-1h0l1 1 3 1c1 2 2 3 3 4 2 2 4 3 5 4 3 2 5 4 7 6 1 1 1 1 2 1l1 2c4 4 6 9 8 14h-2c-2-1-3-5-4-7s-2-5-4-6l-1 1-5-5c-1-1-2-1-3-2l-1-1v1h0c-1-1-2-2-4-3l-6-3-2-2v-1c-6-2-13-4-19-5-8 0-15 0-23 1-3 1-7 2-10 3l-1 1-3 1h-2v1c-6 3-12 6-15 12l-3 3-1 1-1 2c-1 2-2 4-4 5h0c3-9 11-17 19-22 2-2 6-3 9-5 2 0 5-1 6-2l-1-1c1-1 15-2 17-3 1 0 2 0 3-1l2-1h1 5-1l-1-1h3z" class="M"></path><path d="M188 524h4c2 1 4 1 5 2h2c1 1 2 1 2 2h2s1 0 1 1h2c1 1 1 1 2 1s1 0 1 1l1-1h0l1 1s-1 1-2 1h0c-3-2-6-3-9-4-4-1-8-2-11-2h-10l2-1h1 5-1l-1-1h3z" class="S"></path><path d="M188 524h4c-2 2-4 0-6 1l-1-1h3z" class="T"></path><path d="M189 530l-10-1h11c5 1 10 2 14 4 7 3 15 7 20 13-1-1-2-1-3-2l-1-1v1h0c-1-1-2-2-4-3l-6-3-2-2v-1c-6-2-13-4-19-5z" class="g"></path><path d="M204 533v-1l-6-2c-1 0-2-1-3-1s-3 0-4-1h0c3 0 8 1 10 2 1 0 2 1 3 1h2c0 1 1 1 1 1h2 0c1 0 2-1 2-1l3 1c1 2 2 3 3 4 2 2 4 3 5 4 3 2 5 4 7 6 1 1 1 1 2 1l1 2c4 4 6 9 8 14h-2c-2-1-3-5-4-7s-2-5-4-6l-1 1-5-5c-5-6-13-10-20-13z" class="F"></path><path d="M211 531l3 1c1 2 2 3 3 4 2 2 4 3 5 4 3 2 5 4 7 6 1 1 1 1 2 1l1 2v1c-1 0-1 0-2-1-2-1-4-4-6-6s-4-3-6-5l-6-3c-1 0-1-1-2-1v-1l-1-1c1 0 2-1 2-1z" class="M"></path><path d="M211 531l3 1c1 2 2 3 3 4-2 0-4-2-7-3l-1-1c1 0 2-1 2-1z" class="H"></path><defs><linearGradient id="Q" x1="179.917" y1="136.355" x2="220.609" y2="122.654" xlink:href="#B"><stop offset="0" stop-color="#040504"></stop><stop offset="1" stop-color="#262526"></stop></linearGradient></defs><path fill="url(#Q)" d="M203 101c4 0 8-2 12-3v1h4l1 2h-2 0c0 1-1 2-2 2h2l1 1c0 1-1 1-2 2h0 3c2 0 7 0 9 1h1-1 2c2 1 3 2 4 3h1 2v1h0 3l2 2c2 2 3 3 4 5l-1 1-3-1h-3l-1 1h-6c-1 0-3 2-5 2h-1l-3 2h-1-1-4-5c-1 1-3 1-4 1h3v1h0c-2 0-2 0-3 1h0 0c-2 1-3 2-3 3-1 1-2 1-3 2l-3 3c-1 2-3 4-4 6 0 1-1 2-2 3v1 2l-5 3c-1 0-2 1-2 2h-2v-3-1c1-1 2-2 2-3 3-4 4-7 5-11 1-1 2-3 2-4s1-2 1-3h0-1c0 1 0 1-1 2v-1c0-3 0-3 1-4 2-2 3-4 2-5l1-1s2-2 2-3l1-1 2-3h1c2-1 2-2 3-3l1-1v-1h-2l1-1h-1-2v-1c1 0 1 0 1-1l-1-1z"></path><path d="M206 110h1l2 1c-2 1-3 1-5 1 1 0 2-1 2-2z" class="N"></path><path d="M189 149c0-1 0-1 1-2h0c0-1 0-1 1-1l3-2v2l-5 3z" class="U"></path><defs><linearGradient id="R" x1="209.275" y1="111.891" x2="215.194" y2="114.664" xlink:href="#B"><stop offset="0" stop-color="#302f2f"></stop><stop offset="1" stop-color="#535254"></stop></linearGradient></defs><path fill="url(#R)" d="M216 111h1c2 1 5 0 6 1h0l-2 1 1 1c-1 1-2 1-3 1h-2-1l-1 1h0l1 1c-1 1-6 1-7 1 0 0 1-1 2-1h0-5 0-1-1c0-1 0-1 1-2 2 0 2-1 4-2 2 0 3-1 5-2h0 2z"></path><path d="M216 111h1c2 1 5 0 6 1h0l-2 1 1 1c-1 1-2 1-3 1h-2-1s1 0 1-1l1-1c1 0 1 0 1-1s-3 0-5 0v-1h2z" class="R"></path><path d="M200 123c1-2 3-3 5-4h0 2v1c3 0 6-1 9 0-2 0-3 0-5 1h0l3 1h-3-2c-1 0-2 2-3 2-2 2-4 3-6 5s-3 5-5 6c1-4 5-9 9-11h-1c-2 1-3 3-5 4l-1-1h1v-2c0-1 1-2 2-2z" class="G"></path><path d="M200 123h1c1-1 2-1 3-1h-1l-5 5v-2c0-1 1-2 2-2z" class="U"></path><path d="M203 101c4 0 8-2 12-3v1h4l1 2h-2 0c0 1-1 2-2 2h2l1 1c0 1-1 1-2 2h0 3c-2 0-4 1-5 2h5l-1 1c-2 0-2 0-3 2h-2 0v-1c1 0 1 0 2-1h-3c-1 1-3 2-4 2l-2-1h-1c0 1-1 2-2 2l-5 2 1-1 2-3h1c2-1 2-2 3-3l1-1v-1h-2l1-1h-1-2v-1c1 0 1 0 1-1l-1-1z" class="I"></path><path d="M212 106h-2v-1c1 0 2-1 3-2 1 0 1 0 2-1h-4-3v-1c1 0 2 0 3-1l1 2c2 0 4-1 5 0 0 1-2 1-2 2h1l-2 1h0l-2 1z" class="K"></path><path d="M215 99h4l1 2h-2 0c0 1-1 2-2 2h2l-2 1h-1c0-1 2-1 2-2-1-1-3 0-5 0l-1-2 4-1z" class="T"></path><path d="M205 104v-1h3c0 1 1 1 1 1v1l-1 1c1 0 1 1 2 1-1 1-2 2-3 2l-1 1c0 1-1 2-2 2l-5 2 1-1 2-3h1c2-1 2-2 3-3l1-1v-1h-2l1-1h-1z" class="L"></path><path d="M218 103l1 1c0 1-1 1-2 2h0 3c-2 0-4 1-5 2h5l-1 1c-2 0-2 0-3 2h-2 0v-1c1 0 1 0 2-1h-3c-1 1-3 2-4 2l-2-1h-1l1-1c1 0 2-1 3-2l2-1 2-1h0l2-1 2-1z" class="G"></path><path d="M214 105h1c0 1-5 4-6 4l-2 1h-1l1-1c1 0 2-1 3-2l2-1 2-1z" class="R"></path><path d="M220 106c2 0 7 0 9 1h1-1 2c2 1 3 2 4 3h1 2v1h0 3l2 2c2 2 3 3 4 5l-1 1-3-1h-3l-1 1h-6c-1 0-3 2-5 2h-1l-3 2h-1-1-4-5c-1 1-3 1-4 1h-3c1 0 2-2 3-2h2 3l-3-1h0c2-1 3-1 5-1-3-1-6 0-9 0v-1l2-1c1 0 6 0 7-1l-1-1h0l1-1h1 2c1 0 2 0 3-1l-1-1 2-1h0c-1-1-4 0-6-1h-1c1-2 1-2 3-2l1-1h-5c1-1 3-2 5-2z" class="p"></path><path d="M233 119h-2c0-1-1-1-2-1v-1h3 6c0 1 1 1 2 1h0l-1 1h-6z" class="n"></path><path d="M216 120c2 0 5-1 8 0h4v1h-1c-4 1-8 0-13 1l-3-1h0c2-1 3-1 5-1zm1-5h2 4c-1 1-2 1-3 1v1c1 0 2 0 3-1h5 0l-3 2c-2 1-5 1-7 0 0-1 0-1 1-1-1-1-1-1-2-1v-1z" class="m"></path><path d="M214 122c5-1 9 0 13-1l-3 2h-1-1-4-5c-1 1-3 1-4 1h-3c1 0 2-2 3-2h2 3z" class="N"></path><path d="M236 110h2v1h0 3l2 2c2 2 3 3 4 5l-1 1-3-1-1-1c-2-1-4-1-6-2h0c-2 0-2 0-3-1-1 0-2 0-4-1h4v-1h2 3v-1c-1 0-2-1-2-1z" class="d"></path><path d="M233 114c1-1 3-1 4-1h1l-1 1-1 1h0 0c-2 0-2 0-3-1z" class="W"></path><path d="M240 115c0-2-1-2-2-4h3l2 2c0 1-1 2-3 2z" class="i"></path><path d="M243 113c2 2 3 3 4 5l-1 1-3-1-1-1c-2-1-4-1-6-2h0l1-1c2 1 2 1 3 1 2 0 3-1 3-2z" class="J"></path><path d="M220 106c2 0 7 0 9 1h1-1 2c2 1 3 2 4 3h1s1 1 2 1v1h-3-2v1h-4-1c-2 0-4 0-5-1h0c-1-1-4 0-6-1h-1c1-2 1-2 3-2l1-1h-5c1-1 3-2 5-2z" class="k"></path><path d="M220 108s1 1 2 1h3v1h-2c-2 0-4 0-6 1h-1c1-2 1-2 3-2l1-1z" class="Q"></path><path d="M220 106c2 0 7 0 9 1-2 0-5-1-7 1v1c-1 0-2-1-2-1h-5c1-1 3-2 5-2z" class="J"></path><path d="M229 107h2c2 1 3 2 4 3h1s1 1 2 1v1h-3-2v1h-4-1 1c-1-1-2-1-3-1h-1s0-1-1-1h0c1-1 4 1 6 0-1-1-2-1-3-2 1 0 1 0 1-1h0l1-1z" class="W"></path><path d="M232 110h2v1c-1 0-2 0-3-1h1z" class="l"></path><path d="M232 110c1-1 2-1 3 0h1s1 1 2 1v1h-3l-1-1v-1h-2z" class="p"></path><path d="M167 535c6-2 13-2 19-2 3 0 6 0 8 1h2 0l-2-1c-6-2-13-2-18-1-3 0-5 1-7 1 2-1 4-1 5-2h7c10 0 18 2 27 5l2 2 6 3c2 1 3 2 4 3h0v-1l1 1c1 1 2 1 3 2l5 5 1-1c2 1 3 4 4 6s2 6 4 7h2v4c0 1 0 1-1 2v1h0l-1 1c1 3 1 6 1 9v4c-1 1-2 1-2 2-1 1-2 4-3 6h0l-1 4c-1 3-3 6-5 9-1 0-1-1-2-1 3-3 4-6 5-10-2 1-3 2-3 4-1 1-2 2-2 3-1 0-1-1-1-1l1-1c0-1 1-3 1-5 0 0 2-3 2-4h0v-1c1-1 1-4 0-5 1-2 1-4 0-6 0-10-5-18-12-24l-4-2 1-2c-2-1-4-1-5-3h0s0-1-1-1h0c-10-7-24-8-36-6-1-1-2-3-3-4-2 1-3 2-5 3-1 1-2 1-4 2h0c-1-1-1-1-2 0h-4c4-2 8-5 13-6z" class="O"></path><path d="M229 551l1-1c2 1 3 4 4 6s2 6 4 7h2v4c0 1 0 1-1 2v1h0l-1 1c-1-8-4-14-9-20z" class="M"></path><path d="M167 535h0 3 1c2 0 4 0 5-1h6 4c1 1 2 0 4 0 1 1 2 1 4 1h1l1 1h-2c-1-1-15-1-17-1h-1c-1 1-2 0-3 1h-4c-2 1-3 2-5 3-1 1-2 1-4 2h0c-1-1-1-1-2 0h-4c4-2 8-5 13-6z" class="K"></path><defs><linearGradient id="S" x1="236.847" y1="587.609" x2="209.703" y2="556.682" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#868685"></stop></linearGradient></defs><path fill="url(#S)" d="M208 546c8 3 17 9 21 17 4 9 6 21 2 30v1c-2 1-3 2-3 4-1 1-2 2-2 3-1 0-1-1-1-1l1-1c0-1 1-3 1-5 0 0 2-3 2-4h0v-1c1-1 1-4 0-5 1-2 1-4 0-6 0-10-5-18-12-24l-4-2 1-2c-2-1-4-1-5-3h0s0-1-1-1z"></path><path d="M214 550l2 2h-1l2 2-4-2 1-2z" class="f"></path><path d="M80 473l1-1v-1c-1 0-1 0-1-1v-2c2 1 2 3 2 4h1l1 1h0v-3l3 8 1-1 1 2c1-1 2-2 2-3l2 3c0-1 0-2 1-3l6 9 1 1 5 5v1c1 0 2 0 2 1 4 4 9 10 13 12l3 1c1 0 2 1 3 2h-1-1c0 1 1 1 2 1v1 1l3 2c1 0 2 1 2 1 3 2 6 3 9 4l-2 1 9 1v1h0-2v1h0-2v1h-1l-13-1c-2-1-3-2-5-2-4 0-7-3-11-4v1c-1 0-3-1-4-1-2-1-4-1-6-2-4-1-9-3-13-6l-2-3c-1 0-2-2-3-3-1 0-1-1-2-1-1-1-1-1-1-2v-1c2 2 4 3 6 5h0 2 0l2 1h1 1l-1-1v-1l-2-2 1-1v-2c-2-3-4-6-5-9-1 0-1-2-1-2-1 0-2-1-2-1-1-1-3-5-3-7l-1-3c0-1-1-1-1-2z" class="H"></path><path d="M130 519c5 1 9 3 14 3v1h-1l-13-1c-2-1-3-2-5-2 2 0 4 1 5-1z" class="j"></path><path d="M117 510l2-1 6 3c1 0 2 1 2 1h1 2c1 0 2 1 2 1 3 2 6 3 9 4l-2 1c-4 0-11-3-15-5-1 0-2 0-3-1h0 1v-1c-1 0-2-1-4-2h-1z" class="b"></path><path d="M97 492l2 2v2c1 2 3 3 4 4 2 2 4 3 6 4 2 2 5 5 8 6h1l-1 1c-2-1-5-2-8-3h0c4 2 8 4 11 6-4-1-8-3-12-5v1h-1c-2-2-5-4-8-6h2l3 2 1 1h1l-1-1h0l3 1h0c-1-2-3-3-5-4h-1l-3-3c-1 0-2-1-2-2l2-1-1-1c0-1-1-2-2-3l1-1z" class="J"></path><path d="M93 497h1c0 2 1 4 3 5l2 2c3 2 6 4 8 6h1c7 3 15 7 22 9-1 2-3 1-5 1-4 0-7-3-11-4v1c-1 0-3-1-4-1-2-1-4-1-6-2-4-1-9-3-13-6l-2-3c-1 0-2-2-3-3-1 0-1-1-2-1-1-1-1-1-1-2v-1c2 2 4 3 6 5h0 2 0l2 1h1 1l-1-1v-1l-2-2 1-1v-2z" class="n"></path><path d="M93 497h1c0 2 1 4 3 5l2 2c3 2 6 4 8 6-2 0-3 0-5-1-3-1-6-3-9-5h1 1l-1-1v-1l-2-2 1-1v-2z" class="m"></path><path d="M93 504h1 1l-1-1v-1l6 5c1 0 2 1 2 2-3-1-6-3-9-5z" class="g"></path><path d="M91 508c3-1 6 1 9 2 5 2 10 4 14 6v1c-1 0-3-1-4-1-2-1-4-1-6-2-4-1-9-3-13-6z" class="T"></path><path d="M80 473l1-1v-1c-1 0-1 0-1-1v-2c2 1 2 3 2 4h1l1 1h0v-3l3 8 1-1 1 2c1 0 1 3 2 3v1c2 2 3 4 4 6 1 1 1 2 2 3l-1 1c1 1 2 2 2 3l1 1-2 1c0 1 1 2 2 2l3 3h1c2 1 4 2 5 4h0l-3-1h0l1 1h-1l-1-1-3-2h-2l-2-2c-2-1-3-3-3-5h-1c-2-3-4-6-5-9-1 0-1-2-1-2-1 0-2-1-2-1-1-1-3-5-3-7l-1-3c0-1-1-1-1-2z" class="L"></path><path d="M88 488h0c1 0 1 0 2 1s1 2 2 3c1 2 3 4 3 6l-1-1h-1c-2-3-4-6-5-9z" class="i"></path><path d="M94 497l1 1c2 2 4 4 6 5s3 2 4 3h0l1 1h-1l-1-1-3-2h-2l-2-2c-2-1-3-3-3-5z" class="W"></path><path d="M88 477l1 2c1 0 1 3 2 3v1c2 2 3 4 4 6 1 1 1 2 2 3l-1 1-3-5-1 1c1 2 4 4 5 7h0c-2-1-4-4-5-6-1-3-3-5-4-8l-1-4 1-1z" class="i"></path><path d="M88 477l1 2c1 0 1 3 2 3v1l1 2h-1l-3-3-1-4 1-1z" class="h"></path><path d="M80 473l1-1v-1c-1 0-1 0-1-1v-2c2 1 2 3 2 4h1l1 1h0c1 1 1 3 2 4 0 2 1 3 1 5s2 4 3 7c-1-1-1-1-2-1h0c-1 0-1-2-1-2-1 0-2-1-2-1-1-1-3-5-3-7l-1-3c0-1-1-1-1-2z" class="p"></path><path d="M87 486c-2-2-1-4-1-5-1-2-3-4-3-5l1-1c0 1 0 2 1 3l1-1c0 2 1 3 1 5s2 4 3 7c-1-1-1-1-2-1h0c-1 0-1-2-1-2z" class="d"></path><path d="M94 476l6 9 1 1 5 5v1c1 0 2 0 2 1 4 4 9 10 13 12l3 1c1 0 2 1 3 2h-1-1c0 1 1 1 2 1v1 1l3 2h-2-1s-1-1-2-1l-6-3-2 1c-3-1-6-4-8-6-2-1-4-2-6-4-1-1-3-2-4-4v-2l-2-2c-1-1-1-2-2-3-1-2-2-4-4-6v-1c-1 0-1-3-2-3 1-1 2-2 2-3l2 3c0-1 0-2 1-3z" class="K"></path><path d="M99 494l4 4v2c-1-1-3-2-4-4v-2z" class="E"></path><path d="M108 498l4 2c1 1 2 3 3 3h1 0c0 1 1 1 1 1 2 0 5 3 6 3l1-1c1 0 2 1 3 2h-1-1c0 1 1 1 2 1v1 1c-1 0-2-1-2-1-3-1-5-2-8-4-1-1-2-1-3-2l-6-3v-3z" class="a"></path><path d="M106 491v1c1 0 2 0 2 1 4 4 9 10 13 12l3 1-1 1c-1 0-4-3-6-3 0 0-1 0-1-1h0-1c-1 0-2-2-3-3l-4-2-4-4 1-1h1v-2z" class="W"></path><path d="M106 491v1s1 1 1 2v1l1 1c2 1 3 2 4 4h0l-4-2-4-4 1-1h1v-2z" class="n"></path><path d="M94 476l6 9 1 1 5 5v2h-1l-1 1 4 4v3c-3-1-9-9-11-12-1-2-4-7-5-8l-1 1c-1 0-1-3-2-3 1-1 2-2 2-3l2 3c0-1 0-2 1-3z" class="F"></path><path d="M98 487l1-1h1 0l-1-1h1l1 1 5 5v2h-1l-1 1-6-7z" class="m"></path><path d="M105 493c-2-2-3-3-4-6v-1l5 5v2h-1z" class="h"></path><path d="M94 476l6 9h-1l1 1h0-1l-1 1-5-8c0-1 0-2 1-3z" class="j"></path><path d="M306 107h1c1-1 1-1 3-1h-1v1h2c2 0 3 0 4-1v1h2v1h0-2v1c1 1 4 0 5 0 3 1 5 3 7 6h0v1h-1l-2-3-1 1 2 3c0 1-1 1-1 2h0v1 1c-1 0-2-1-4-1l1 1 1 1c-3-1-5-1-8-1 0 1 0 1 1 1-2 1-5 0-7 0 1 0 2 1 2 1 0 1 0 1-1 1h3 0c0 1-1 1-2 1l-2 1-1 1-1 1v1l-3 1v1c1 1 1 1 2 1l-1 1h1c-1 2-2 2-3 3v1h1l3 1c-2 1-4 0-6 0v1h2c-2 1-4 2-6 2h-2l-1 1h0c0 1 1 1 1 2-1 1-2 1-3 1l2 2h-3l-2-1-1 1v1h-1c-1-1-2-1-2-1h-1l-2-2v-1l-2-3v-3c-1 0-1-1-2-1v-1-1-2h0v-1c0-2 1-2 2-4l1-3 1-1v-2-1c0-2 1-3 2-5v-1h1 0c1 0 1 0 2-1l1 1h0c-1 1-1 2 0 4h0v1l1-1c1 1 1 1 2 1l3-3h0v-2l2-1 2-1h0l2-1c2-1 4-2 6-4l1-1z" class="T"></path><path d="M307 125h3l-2 1-1 1c-1 0-1 0-1-1s0-1 1-1z" class="Y"></path><path d="M286 143l5 2 2 2h-3l-2-1h0c-1 0-2-1-2-3z" class="E"></path><path d="M291 124l2-1h2v1c-1 0-2 0-2 2-1 1-1 2 0 3l-1 2c-1 0-1 0-2-1s-1-1-1-2h-1v-1l-1-1c1-1 2-1 2-1 1 0 2 0 2-1z" class="Y"></path><path d="M299 121l9 1c1 0 2 1 2 1 0 1 0 1-1 1h3 0c0 1-1 1-2 1h-3v-2c-3-1-6-2-8-1-3 0-6 0-8 2 0 1-1 1-2 1l-2-1c4-2 8-3 12-3z" class="B"></path><path d="M292 140h-4c-2-1-3-1-4-2v-1l3 1c3 0 8 0 10-2 3-2 5-7 6-10v-2l1 1c0 1-1 3-1 5v1c-3 5-6 7-11 9z" class="h"></path><path d="M292 140c5-2 8-4 11-9 1 1 1 1 2 1l-1 1h1c-1 2-2 2-3 3v1h1l3 1c-2 1-4 0-6 0v1h2c-2 1-4 2-6 2h-2c1-1 1-1 1-2-1 1-2 1-3 1z" class="D"></path><path d="M277 136l1-1h1 1c1 3 3 7 6 8 0 2 1 3 2 3h0l-1 1v1h-1c-1-1-2-1-2-1h-1l-2-2v-1l-2-3v-3c-1 0-1-1-2-1v-1z" class="Q"></path><path d="M281 144h0c1 0 1-1 2-1l1 1c0 1 0 2-1 2v1l-2-2v-1z" class="c"></path><path d="M288 128c0 1 0 3 1 4 1 0 1 1 2 1s1-1 2-2 1-3 1-5v-1c1-1 4-1 6-1v4c-1 2-1 3-2 5l-2 1c-2 2-6 2-8 1-1 0-3 0-3-2-1-1-1-2 0-3 0-1 1-2 3-3v1z" class="e"></path><path d="M298 133c-1-1-1-2-2-2v-2c0-1 0-1 1-2 1 0 1 0 2-1h0l1 2c-1 2-1 3-2 5z" class="r"></path><path d="M297 113h0l2-1 1 2h0 1 1l1 1c-1 0-3 1-4 2h1 0c1 0 1 0 2 1h1 1l1 1-1 1h0c-2 0-4 0-5 1-4 0-8 1-12 3-3 4-4 5-5 10h-1-1v1h-1-1l-1 1v-1-2h0v-1c0-2 1-2 2-4l1-3 1-1v-2-1c0-2 1-3 2-5v-1h1 0c1 0 1 0 2-1l1 1h0c-1 1-1 2 0 4h0v1l1-1c1 1 1 1 2 1l3-3h0v-2l2-1 2-1z" class="h"></path><path d="M302 118h1 1l1 1-1 1h0c-1-1-2 0-3-1l1-1z" class="q"></path><path d="M297 113h0l2-1 1 2h0 1 1l1 1c-1 0-3 1-4 2h1 0l-8 3c-1 1-2 1-3 1l1-1 3-3h0v-2l2-1 2-1z" class="c"></path><path d="M295 114c1 1 2 1 3 1-1 2-3 2-5 2v-2l2-1z" class="f"></path><path d="M297 113h0l2-1 1 2h0l-2 1c-1 0-2 0-3-1l2-1z" class="F"></path><path d="M283 116v-1h1 0c1 0 1 0 2-1l1 1h0c-1 1-1 2 0 4h0v1l1-1c1 1 1 1 2 1l-1 1-2 2c-1 1-3 2-4 4l-1 2c-1 2-1 4-1 5h-1v1h-1-1l-1 1v-1-2h0v-1c0-2 1-2 2-4l1-3 1-1v-2-1c0-2 1-3 2-5z" class="S"></path><path d="M277 133h1l4-4c-1 2-1 4-1 5h-1v1h-1-1l-1 1v-1-2h0z" class="D"></path><path d="M283 116v-1h1 0c1 0 1 0 2-1l1 1h0c-1 1-1 2 0 4h0v1l1-1c1 1 1 1 2 1l-1 1-2 2c-1 1-3 2-4 4v-3l2-2h0v-2c-2 0-2 3-4 3v1-2-1c0-2 1-3 2-5z" class="B"></path><path d="M287 123c0-1-1-1-1-2h0c0-1 0-1 1-2v1l1-1c1 1 1 1 2 1l-1 1-2 2z" class="V"></path><path d="M283 116v-1h1 0c1 0 1 0 2-1l-3 6c0 1-1 1-2 2v-1c0-2 1-3 2-5z" class="J"></path><path d="M306 107h1c1-1 1-1 3-1h-1v1h2c2 0 3 0 4-1v1h2v1h0-2v1c1 1 4 0 5 0 3 1 5 3 7 6h0v1h-1l-2-3-1 1 2 3c0 1-1 1-1 2h0v1 1c-1 0-2-1-4-1l1 1 1 1c-3-1-5-1-8-1 0 1 0 1 1 1-2 1-5 0-7 0l-9-1c1-1 3-1 5-1h0l1-1-1-1h-1-1c-1-1-1-1-2-1h0-1c1-1 3-2 4-2l-1-1h-1-1 0l-1-2c2-1 4-2 6-4l1-1z" class="Z"></path><path d="M311 111l2-1c1-1 4 0 5 0h3l3 3-1 1c-1 0-2-1-3-2h-3 3v-1c-2 0-4 0-6 1h0-2c-1 0-1 0-1 1h-1 0l1-2z" class="F"></path><path d="M317 112h3c1 1 2 2 3 2l2 3c0 1-1 1-1 2h0v1l-3-1h-1l-3-1c-2 0-2-1-3-2 0 0-1-1-2-1 1 0 2-1 3-1v1l1 1v-1h4l1-1h0c-2-1-5 0-7 0v-1h3v-1z" class="K"></path><path d="M321 119v-1h2l1 1h0v1l-3-1z" class="T"></path><path d="M321 114c1 1 1 1 1 3h-1-2 1l-1-1h-3v-1h4l1-1z" class="I"></path><path d="M312 115c1 0 2-1 3-1v1l1 1h3l1 1h-1c-1 0-1 0-2 1-2 0-2-1-3-2 0 0-1-1-2-1z" class="a"></path><path d="M306 107h1c1-1 1-1 3-1h-1v1h2c2 0 3 0 4-1v1h2v1h0-2v1c1 1 4 0 5 0 3 1 5 3 7 6h0v1h-1l-2-3-3-3h-3c-1 0-4-1-5 0l-2 1c-2 1-3 2-4 2-1 1-3 1-4 2l-1-1h-1-1 0l-1-2c2-1 4-2 6-4l1-1z" class="i"></path><path d="M301 114l2-3h2c1 0 1-1 2-1s1 0 2 1c-1 0-2 1-3 1h0c-2 0-3 1-4 2h-1z" class="Z"></path><path d="M306 107l1 1 1-1h0c1 0 1 1 2 0v1l-3 2c-1 0-1 1-2 1h-2l-2 3h-1 0l-1-2c2-1 4-2 6-4l1-1z" class="C"></path><path d="M300 117c2-1 5-3 8-2h4c1 0 2 1 2 1 1 1 1 2 3 2l3 1h1l3 1v1c-1 0-2-1-4-1l1 1 1 1c-3-1-5-1-8-1 0 1 0 1 1 1-2 1-5 0-7 0l-9-1c1-1 3-1 5-1h0l1-1-1-1h-1-1c-1-1-1-1-2-1h0z" class="o"></path><path d="M307 116c1 0 3-1 4 0 1 0 2 2 3 3v1l-3 1h-1l1-1c-1-1-2-3-4-4z" class="l"></path><path d="M320 119h1l3 1v1c-1 0-2-1-4-1l1 1 1 1c-3-1-5-1-8-1h-3l3-1c1 0 3-1 5 0 1 0 1-1 1-1z" class="F"></path><path d="M307 116c2 1 3 3 4 4l-1 1h0-2c-1-1-1-1-2-1h-2l1-1-1-1h-1l2-1 2-1z" class="m"></path><path d="M305 117h1c0 1 0 1-1 1 1 1 1 1 2 1h1c1 0 1 1 2 2h-2c-1-1-1-1-2-1h-2l1-1-1-1h-1l2-1z" class="e"></path><path d="M299 121c1-1 3-1 5-1h0 2c1 0 1 0 2 1h2 0 1 3c0 1 0 1 1 1-2 1-5 0-7 0l-9-1z" class="j"></path><path d="M248 75c3 0 7 1 10 0l-5-1c3 0 7 0 10 1l8 2h1c2 1 4 3 5 4 1-1 2 0 3 1s2 2 3 4c1 1 1 0 2 1s1 4 2 6l-2-2v4l-1 1v3h-1v4h0l-1-1h-1-1v2 2l1 1v4h0v2 3c0 1-1 1-1 2v3h1v1 2l-1 1-1 3c-1 2-2 2-2 4v1h0v2l-1-1c0 1 0 1-1 1l-1-1-1-1c0-1-1 0-1 0-1 0-1-1-1-1-1 0-2 1-3 0h-1-2l1-1v-3c0-1 1-2 1-2v-1-2-6l-1-7c-1-1-1-1-1-2v-1-1l-1-2v1l-1-1c0-2 0-3-1-5 1-2-1-4-1-6l1 1c0-2-1-3-1-4-1 2-1 2-2 3-1-1-2-2-2-3h-1 0c-1-1-2-3-2-4v-1c-1-1-2-2-2-3-1-1-2-2-2-3l-3-3v-1h1z" class="b"></path><path d="M262 94l1-1 1 1h1v-1l1-1 1 1c0 1-1 2-1 2v5h-1c0-1 0-3-1-4h0c-1-1-1-2-2-2z" class="N"></path><path d="M277 92s1 0 1-1h1c0-3-2-5-4-8 2 2 3 3 4 5 0 1 2 3 2 4h-1v2h-1c-1 0-2-1-2-2z" class="B"></path><path d="M283 89h0c1 0 1 1 2 2v4l-1 1v3h-1l-1-3c0-1-1-2-2-2v-2h1l1 2h2c0-2-1-3-1-5z" class="F"></path><path d="M277 81c1-1 2 0 3 1s2 2 3 4c1 1 1 0 2 1s1 4 2 6l-2-2c-1-1-1-2-2-2h0c-1-1-1-3-2-4l-1-1-3-3z" class="G"></path><path d="M248 75c3 0 7 1 10 0l-5-1c3 0 7 0 10 1-1 0-2 0-3 1s0 0-1 0h-4c0 1-1 1-1 0h-3-1c0 2 4 4 5 6 1 1 2 2 2 3 2 2 2 3 3 5h1c-1 2-1 2-2 3-1-1-2-2-2-3h-1 0c-1-1-2-3-2-4v-1c-1-1-2-2-2-3-1-1-2-2-2-3l-3-3v-1h1z" class="D"></path><path d="M257 85c2 2 2 3 3 5h-2v-2h0c0-1 0-2-1-3z" class="K"></path><path d="M274 92c-3-5-6-8-9-12h1 2c2 1 4 3 5 5s2 4 4 6v1c0 1 1 2 2 2h1c1 0 2 1 2 2l1 3v4h0l-1-1h-1-1v2 2l1 1h-1c0-1 0-2-1-2h-1c0-1-1-1-2-2 0-3-1-5-2-8v-3z" class="d"></path><path d="M279 103l-1 1h-1c0-1 1-2 1-3l1 1v1z" class="o"></path><path d="M279 99l1 1v4 2l1 1h-1c0-1 0-2-1-2v-2-1-3zm-3-2l1-1h1c1 1 1 2 1 2l-2 2h-1v-2-1z" class="X"></path><path d="M276 97l1-1h1v2h-2v-1z" class="i"></path><path d="M280 94c1 0 2 1 2 2h-1c-1 1-1 3-1 4l-1-1v-1s0-1-1-2h-1l-1 1v-2h1c1 0 1 0 2 1h1v-2z" class="M"></path><path d="M277 91v1c0 1 1 2 2 2h1v2h-1c-1-1-1-1-2-1h-1l-1-3 2-1z" class="V"></path><path d="M280 100c0-1 0-3 1-4h1l1 3v4h0l-1-1h-1-1v2-4z" class="a"></path><path d="M266 80h2c2 1 4 3 5 5s2 4 4 6l-2 1c-2-5-5-8-9-12z" class="K"></path><path d="M274 92v3c1 3 2 5 2 8v5h0c-2 1-2 1-3 2h-1l-1 2-1 3c0 1 0 2-1 4-1 1-1 2-1 3v3h-1v-2-6l-1-7c-1-1-1-1-1-2v-1-1l-1-2v1l-1-1c0-2 0-3-1-5 1-2-1-4-1-6l1 1c1 0 1 1 2 2h0c1 1 1 3 1 4h1v-5s1-1 1-2l1 1h1 1c0 1 2 1 3 2 1-1 0-3 1-4z" class="F"></path><path d="M274 92v3s0 1-1 1c0 2 1 3 0 4h-1c0-1 0-2-1-4v1c-1 1-1 3-1 5l-1-5h-1c0 1 0 2-1 3v-4l-1-1s1-1 1-2l1 1h1 1c0 1 2 1 3 2 1-1 0-3 1-4z" class="B"></path><path d="M262 99c1-2-1-4-1-6l1 1c1 0 1 1 2 2h0c1 1 1 3 1 4v2c1 1 1 1 1 2v4h-1v-1-1l-1-2v1l-1-1c0-2 0-3-1-5z" class="R"></path><path d="M274 95c1 3 2 5 2 8v5h0c-2 1-2 1-3 2h-1l-1 2-1 3c0 1 0 2-1 4-1 1-1 2-1 3v3h-1v-2-6-1h1v-3c-1-1-1-2-1-3h1v-1c-1 0-1-1-1-2s0-2 1-2c1-2-1-2-1-4h1c0-1 0-1 1-2v2l1 1c0 1 1 1 1 2s-1 3 0 5l1-1c0-2 0-3-1-5v-2h1v-1h1c1-1 0-2 0-4 1 0 1-1 1-1z" class="Z"></path><path d="M268 110c0 1 0 2 1 3v-4c0-1-1-1 0-2 0 1 1 2 1 3 0 2-1 4 0 5 0 1 0 2-1 4-1 1-1 2-1 3v3h-1v-2-6-1h1v-3c-1-1-1-2-1-3h1z" class="C"></path><path d="M274 95c1 3 2 5 2 8v5h0c-2 1-2 1-3 2h-1c0-1 1-1 1-2s0-1 1-2v-1c-1-1-1-2-2-4v-1h1c1-1 0-2 0-4 1 0 1-1 1-1z" class="I"></path><path d="M276 103c1 1 2 1 2 2h1c1 0 1 1 1 2h1v4h0v2 3c0 1-1 1-1 2v3h1v1 2l-1 1-1 3c-1 2-2 2-2 4v1h0v2l-1-1c0 1 0 1-1 1l-1-1-1-1c0-1-1 0-1 0-1 0-1-1-1-1-1 0-2 1-3 0h-1-2l1-1v-3c0-1 1-2 1-2v-1h1v-3c0-1 0-2 1-3 1-2 1-3 1-4l1-3 1-2h1c1-1 1-1 3-2h0v-5z" class="C"></path><path d="M273 119l1-4h1v5 1h-1c0-1 0-1-1-2z" class="B"></path><path d="M276 128l1-1s1 0 1 1h1c-1 2-2 2-2 4h-1c-1-1-1-1-2-3l2-1z" class="D"></path><path d="M276 128v4c-1-1-1-1-2-3l2-1z" class="H"></path><path d="M273 119c1 1 1 1 1 2v4c1 1 1 1 1 2l-2 2v1h-1v-4c1-3 1-5 1-7z" class="N"></path><path d="M276 108h0v1l1 1v3 5s0 1 1 1v7h0c0-1-2-2-2-2v-3c0-1-1-3-1-4 0-3 1-6 1-9z" class="L"></path><path d="M271 112h1c1 2 1 6 0 8h0c-1 1-2 3-2 3h-1l-1-1c0-1 0-2 1-3 1-2 1-3 1-4l1-3z" class="D"></path><path d="M277 118v-1c1 0 1-1 1-2 0 0 0-1 1-1h0c0 2 0 3 1 4v3h1v1 2l-1 1-1 3h-1v-2-7c-1 0-1-1-1-1z" class="f"></path><path d="M278 119l1 1c0 1 0 2 1 3v2l-1 3h-1v-2-7zm-2-16c1 1 2 1 2 2h1c1 0 1 1 1 2h1v4h0v2 3c0 1-1 1-1 2-1-1-1-2-1-4h0c-1 0-1 1-1 1 0 1 0 2-1 2v1-5-3l-1-1v-1-5z" class="c"></path><path d="M279 105c1 0 1 1 1 2 0 0-1 1-1 2l-1 1v-1-4h1z" class="W"></path><path d="M280 107h1v4h-1 0-1v-2c0-1 1-2 1-2z" class="f"></path><path d="M268 122l1 1h1s1-2 2-3c0 3 0 4-1 6h1v4h1v-1h1c1 2 1 2 2 3h1v1h0v2l-1-1c0 1 0 1-1 1l-1-1-1-1c0-1-1 0-1 0-1 0-1-1-1-1-1 0-2 1-3 0h-1-2l1-1v-3c0-1 1-2 1-2v-1h1v-3z" class="F"></path><path d="M267 126c1 0 1 2 1 3l-2 2v-3c0-1 1-2 1-2z" class="R"></path><path d="M273 129h1c1 2 1 2 2 3h1v1h0c-2 0-3 0-4-1l-1-1v-1h1v-1z" class="V"></path><path d="M268 122l1 1h1s1-2 2-3c0 3 0 4-1 6l-1 1v-1c-1 0-1 1-1 2h-1v-3-3z" class="B"></path><defs><linearGradient id="T" x1="93.828" y1="434.273" x2="85.209" y2="436.296" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#T)" d="M84 373l2-4c0 1 0 1 1 2v1s-1 1-1 2c1 0 2-2 2-3v3l1-1v-1h2v1l-1 2v6l-1 8v3h0v4h0l1 1v4c-1 7 0 14-1 21v3l1 8v-1h-1v2h0c0 2 1 5 1 7l4 11c3 11 9 22 16 32v1l-1-1-3-3c-1-2-1-4-3-6v1c0 1 1 2 2 4v1h0c0 1 1 1 1 2v1c1 3 3 3 3 6h-1c1 1 1 1 1 2v1h-1c0-1-1-1-2-1v-1l-5-5-1-1-6-9c-1 1-1 2-1 3l-2-3c0 1-1 2-2 3l-1-2-1-5c-1-1-2-6-2-8 1-2-1-4-1-6l1-1v-3h0c1-2 0-4 0-5s0-3-1-3l-1-7-1-9-2-15c-1-2 0-5 0-8 0-2 0-3-1-5l-1-3h-1c0-2 1-3 1-4 1-5 2-9 3-12 1-2 1-4 2-6l1-3v-1z"></path><path d="M87 398l1-3c1 2 1 4 1 7 0 0 0 1-1 1v-1c0-1 0-3-1-4z" class="I"></path><path d="M89 434c-1-3-2-8-1-11h1v2l1 8v-1h-1v2z" class="p"></path><path d="M86 389c2 1 0 2 1 3 0 0 1 1 2 1l-1 2-1 3c-1 2-1 4-1 7v-4c-1-2-1-4-1-6l1-6z" class="J"></path><path d="M86 401v4 18c-1 0 0 1-1 2v1c-1 1 0 3-1 4v-2-3l2-24z" class="o"></path><path d="M84 425c-1-2 0-6 0-8 0-8-1-15 1-22 0 2 0 4 1 6l-2 24z" class="W"></path><defs><linearGradient id="U" x1="93.181" y1="456.513" x2="79.397" y2="430.612" xlink:href="#B"><stop offset="0" stop-color="#c6c5c1"></stop><stop offset="1" stop-color="#fbfbfd"></stop></linearGradient></defs><path fill="url(#U)" d="M82 430c1 0 1-1 2-2v2c1 7 2 15 4 21 2 2 4 5 5 8l-1 2h1c0 1 1 2 1 3h0c-1 0-1-1-2-1l1 3h-1l-3-6c-2-4-3-8-4-11 0-1 0-3-1-3l-1-7-1-9z"></path><path d="M88 451c2 2 4 5 5 8l-1 2h1c0 1 1 2 1 3h0c-1 0-1-1-2-1-1-1-1-2-1-3l-3-9z" class="i"></path><path d="M85 449c1 3 2 7 4 11l3 6h1l-1-3c1 0 1 1 2 1h0c0-1-1-2-1-3h-1l1-2c1 2 2 3 2 5 1 1 1 1 1 2 1 2 2 3 3 5 0 0 0 1 1 1 1 3 2 7 5 9h0c0 1 1 1 1 2v1c1 3 3 3 3 6h-1c1 1 1 1 1 2v1h-1c0-1-1-1-2-1v-1l-5-5-1-1-6-9c-1 1-1 2-1 3l-2-3c0 1-1 2-2 3l-1-2-1-5c-1-1-2-6-2-8 1-2-1-4-1-6l1-1v-3h0c1-2 0-4 0-5z" class="o"></path><path d="M98 477l2-1c2 2 3 6 6 8 1 3 3 3 3 6l-2-2v1h-1c-3-3-6-8-8-12z" class="L"></path><path d="M93 459c1 2 2 3 2 5 1 1 1 1 1 2 1 2 2 3 3 5 0 0 0 1 1 1 1 3 2 7 5 9h0c0 1 1 1 1 2v1c-3-2-4-6-6-8l-2 1c-2-2-3-5-4-8 0-1-1-2-1-3l-1-3c1 0 1 1 2 1h0c0-1-1-2-1-3h-1l1-2z" class="X"></path><path d="M96 466c1 2 2 3 3 5h-1c-1-2-2-3-2-5z" class="J"></path><path d="M94 469v-1c2 3 4 5 6 8l-2 1c-2-2-3-5-4-8z" class="Q"></path><path d="M85 449c1 3 2 7 4 11 1 9 7 17 12 25 2 1 3 4 5 5l3 3h-1c0-1-1-1-2-1v-1l-5-5-1-1-6-9c0-1-1-2-2-3 0-1-1-2-2-3 0-2-2-4-2-6-1-3-2-5-3-7v-3h0c1-2 0-4 0-5z" class="V"></path><path d="M85 464c1-2-1-4-1-6l1-1c1 2 2 4 3 7 0 2 2 4 2 6 1 1 2 2 2 3 1 1 2 2 2 3-1 1-1 2-1 3l-2-3c0 1-1 2-2 3l-1-2-1-5c-1-1-2-6-2-8z" class="W"></path><path d="M87 472h0c1 1 2 3 2 4 1 0 1-1 2-1v1c0 1-1 2-2 3l-1-2-1-5z" class="R"></path><path d="M90 470c1 1 2 2 2 3 1 1 2 2 2 3-1 1-1 2-1 3l-2-3v-1c0-1-1-2-2-4l1-1z" class="p"></path><path fill="#fff" d="M84 373l2-4c0 1 0 1 1 2v1s-1 1-1 2c1 0 2-2 2-3v3l1-1v-1h2v1l-1 2v6l-1 8v3 1c-1 0-2-1-2-1-1-1 1-2-1-3l-1 6c-2 7-1 14-1 22 0 2-1 6 0 8v3c-1 1-1 2-2 2l-2-15c-1-2 0-5 0-8 0-2 0-3-1-5l-1-3h-1c0-2 1-3 1-4 1-5 2-9 3-12 1-2 1-4 2-6l1-3v-1z"></path><path d="M84 373l2-4c0 1 0 1 1 2v1s-1 1-1 2l-2 6c1-2 0-4 0-6v-1z" class="l"></path><path d="M84 374c0 2 1 4 0 6-1 4-1 8-3 12v-5c1-1 0-3 0-4 1-2 1-4 2-6l1-3z" class="W"></path><path d="M81 383c0 1 1 3 0 4v5c-1 8-1 15-1 23-1-2 0-5 0-8 0-2 0-3-1-5l-1-3h-1c0-2 1-3 1-4 1-5 2-9 3-12z" class="i"></path><defs><linearGradient id="V" x1="91.727" y1="387.253" x2="84.864" y2="381.247" xlink:href="#B"><stop offset="0" stop-color="#68696a"></stop><stop offset="1" stop-color="#858283"></stop></linearGradient></defs><path fill="url(#V)" d="M89 373v-1h2v1l-1 2v6l-1 8v3 1c-1 0-2-1-2-1-1-1 1-2-1-3 0-4 1-9 2-13v-2l1-1z"></path><path d="M89 373v-1h2v1l-1 2v1h-2v-2l1-1z" class="W"></path><path d="M370 445c7 2 16 6 20 12 4 5 6 12 7 19 0 1-1 3-1 5v1h1l-10 16c-1 1-1 1-2 1s-3 1-3 2c-2 1-3 4-5 4-3 0-4 3-6 3h-1v7h0 0v-26-19h0c0-1 0-2 1-2h-1c-1-2 0-5 0-6v-2-6-5c1-1 0-1 0-1v-3z" class="Y"></path><path d="M382 470l2 2c1 2 0 3 1 5 0 1 1 1 1 1l-1 1c0 1 0 1 1 1v1l-1-1-1-1c0 2 0 3-1 4h-1v-5h1v-6c-1 0-1-1-1-2z" class="D"></path><path d="M375 461c1 1 2 1 3 2h1 0 1l1 1 1-1 2 2c0 1-1 2-1 3l-2 1 1 1c0 1 0 2 1 2v6h-1c-1-7-4-12-9-17h2z" class="N"></path><path d="M378 463h1l4 5-2 1c-1-2-3-3-3-6z" class="C"></path><path d="M379 463h0 1l1 1 1-1 2 2c0 1-1 2-1 3l-4-5z" class="M"></path><defs><linearGradient id="W" x1="389.89" y1="478.753" x2="384.443" y2="472.604" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#464546"></stop></linearGradient></defs><path fill="url(#W)" d="M384 465c0 1 1 2 1 3h1v1 3c1 0 1-2 1-2l1-1c0 1 0 2 1 3h0c1 1 1 3 1 4l-2 6h-1c0-1-1-1-1-2h0c-1 0-1 0-1-1l1-1s-1 0-1-1c-1-2 0-3-1-5l-2-2-1-1 2-1c0-1 1-2 1-3z"></path><path d="M384 470l-1-2h0 3v1 5c-1-1-1-2-1-3 0 0 0-1-1-1z" class="Z"></path><path d="M384 465c0 1 1 2 1 3h1-3 0l1 2v2l-2-2-1-1 2-1c0-1 1-2 1-3z" class="L"></path><path d="M371 468c3 2 7 7 8 11 1 6-1 11-4 16l-3 3c-1 1-2 1-2 2v2 6 7h0 0v-26 9h0c5-4 6-9 6-15 0-5-1-10-5-14l-1 1h0c0-1 0-2 1-2z" class="Q"></path><path d="M381 456h3l1 1c3 3 5 6 6 9h0c1 3 3 7 2 10-2-1-2-4-3-6-1 2 0 4 0 6 0-1 0-3-1-4h0c-1-1-1-2-1-3l-1 1s0 2-1 2v-3-1h-1c0-1-1-2-1-3l-2-2-2-2-1-2s1-1 2-1c0 1 1 1 2 2h0l-1-2v-1l-1-1z" class="G"></path><path d="M381 456h3l1 1c-1 0-2 1-3 1v-1l-1-1z" class="E"></path><path d="M388 469c0-2 0-4-1-6h-3v-1l2-1c1 1 1 2 2 3v1l2 5c-1 2 0 4 0 6 0-1 0-3-1-4h0c-1-1-1-2-1-3z" class="g"></path><path d="M380 461l-1-2s1-1 2-1c0 1 1 1 2 2h0 1v1c-1 1-1 1 0 2 0 0 0 1 1 1 0 1 1 1 1 2 0 0-1 1-1 2 0-1-1-2-1-3l-2-2-2-2z" class="E"></path><path d="M370 470l1-1c4 4 5 9 5 14 0 6-1 11-6 15h0v-9-19z" class="O"></path><path d="M395 471c-1-6-4-10-7-15v-1l2 2c4 5 6 12 7 19 0 1-1 3-1 5v1h1l-10 16c-1 1-1 1-2 1s-3 1-3 2c-2 1-3 4-5 4-3 0-4 3-6 3 0-2 5-5 7-6 5-4 11-11 13-17 1-3 1-6 2-9s-1-7-2-10l1-1c0 1 1 3 1 4 1 2 1 3 1 5h1v-3z" class="a"></path><path d="M395 471c-1-6-4-10-7-15v-1l2 2c4 5 6 12 7 19 0 1-1 3-1 5v1h1l-10 16c-1 1-1 1-2 1 4-5 7-10 9-16 1-1 0-1 1-2 1-3 0-7 0-10z" class="e"></path><path d="M370 445c7 2 16 6 20 12l-2-2v1c3 5 6 9 7 15v3h-1c0-2 0-3-1-5 0-1-1-3-1-4l-1 1h0c-1-3-3-6-6-9l-1-1h-3l1 1v1l1 2h0c-1-1-2-1-2-2-1 0-2 1-2 1l1 2 2 2-1 1-1-1h-1 0-1c-1-1-2-1-3-2h-2-1 0l-2 1v-2-6-5c1-1 0-1 0-1v-3z" class="F"></path><path d="M371 454c1 0 2 0 3 1 0 1 0 1 1 1l1 1s1 0 2 1h-1l-2-1h0c-1 0-3-1-4-1v1l-1 1v2h0v-6h1z" class="K"></path><path d="M370 449h1l8 3c1 0 1 1 2 1l-1 1h0l-2-2h-1-1v1c1 0 2 1 2 1h0-2c-1 0-2 0-2-1h-2c-1 0-1 0-1 1h-1v-5z" class="L"></path><path d="M371 457v-1c1 0 3 1 4 1h0l2 1h1c0-1 1-1 2-1h1v-1l1 1v1l1 2h0c-1-1-2-1-2-2-1 0-2 1-2 1l1 2 2 2-1 1-1-1h-1 0-1c-1-1-2-1-3-2h-2-1 0l-2 1v-2h0v-2l1-1z" class="F"></path><path d="M372 459l3 2h-2-1v-2z" class="P"></path><path d="M370 460h0v-2l1-1 1 2v2h0l-2 1v-2z" class="S"></path><path d="M378 458c0-1 1-1 2-1h1v-1l1 1v1l1 2h0c-1-1-2-1-2-2-1 0-2 1-2 1l1 2h-1c-1 0-4-2-5-3l1-1 2 1h1z" class="R"></path><path d="M370 445c7 2 16 6 20 12l-2-2v1c3 5 6 9 7 15v3h-1c0-2 0-3-1-5 0-1-1-3-1-4l-1 1h0c-1-3-3-6-6-9l-1-1v-2-1c-1 0-1-1-2-1-2-1-3-3-5-3-2-1-5-2-7-2h0c2 2 5 2 7 3h0c1 0 2 1 2 1v1c-3-1-5-2-8-3h-1c1-1 0-1 0-1v-3z" class="M"></path><defs><linearGradient id="X" x1="378.141" y1="461.125" x2="405.809" y2="426.067" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252626"></stop></linearGradient></defs><path fill="url(#X)" d="M412 411c6 0 11 1 15 5 2 1 3 2 3 4-1 0-1-1-2-2v1c-2-1-4-2-7-2-3-1-6-1-9 1-2 1-3 2-4 4h0c2-1 4-4 6-3-1 0-2 1-3 2-3 3-5 8-6 12l1 1v-2c1-2 3-3 5-4 1-1 3-2 5-3s4-1 6-2h6c2 1 3 2 5 3v1h-2c-1 0-1-1-2-1-5 2-6 4-7 8-2 4-2 9-3 13-1 2-2 5-2 7 1-3 3-11 6-12 0 1 0 3-1 4-1 6-3 13-7 18-2 4-6 8-10 10-3 2-6 3-9 7 0-2 1-4 1-5-1-7-3-14-7-19-4-6-13-10-20-12h0v-4c0-2-1-9 1-10h1l1-1-3-1v-2c2 0 3 1 4 1h0c-1-1-3-2-4-3 0-1 0-3 1-4 0 0 1 1 2 1 2 1 5 4 6 6 1 1 1 2 2 3 0 1 0 1 1 1v-2h1 0 0 0c0 1 0 1 1 2v-3l1 7c-1 2-1 4 0 5 0 2-1 5 0 7l1 1v-1c0-1 0-3 1-4l1 2h1v-4h1v3h1c0-1 0-1 1-2 0 2-1 3 0 5v2c2-2 1-4 1-6 1-2 1-3 1-4l1-1v1h1v1s0-1 1-2c-2-1-1-4-2-5v6c-1-2-1-4-2-5v-1c0-2 1-4 2-6h1c0-1 1-3 1-4 1-1 1-2 1-2 1-2 2-4 4-5h0v1h0 1l3-3h1l-1-1c1-1 1-1 2-1s2-1 2-1c1-1 2-1 2-1z"></path><path d="M380 439c1 2 2 5 1 7 0-1-1-1-1-2l-1-3 1-1v-1z" class="g"></path><path d="M373 430c3 2 4 5 7 7v2 1l-1 1c-1-3-3-7-5-9-1 0-2-1-2-1l1-1z" class="O"></path><path d="M392 450c2-2 1-4 1-6 1-2 1-3 1-4l1-1v1h1v1 1s0 1-1 1v7 1c0 1-1 2-2 3v-1c-1-1-1-2-1-3z" class="b"></path><path d="M395 440h1v1 1s0 1-1 1v7c-1-1-1-5-1-7 1-1 1-2 1-3z" class="B"></path><path d="M373 430l-3-1v-2c2 0 3 1 4 1h0c1 0 2 1 2 1l1 2 1 1c1 2 2 3 2 5-3-2-4-5-7-7z" class="S"></path><path d="M422 424h3 1 3s2 2 2 3c-1 0-1-1-2-1h0c-1-1-3 1-4 2-3 1-4 5-5 7 0 1-1 2-1 3v1h-1c1-2 1-3 1-5 1-1 2-3 2-4 0 0 0-1 1-1v-1l-1-1h0l-1-2 2-1z" class="e"></path><path d="M422 424l2 1h0l-3 2h0l-1-2 2-1z" class="s"></path><path d="M401 457h0c0 2-1 3-2 4-1-1-1-2-2-2v-1c1-1 1-1 1-2s0-1-1-2v-5-2c0-1 1-1 1-2v1c1 1 1 3 1 5h1v-4h1c0 2 0 7-1 8l1 2z" class="g"></path><path d="M417 454c1-3 3-11 6-12 0 1 0 3-1 4-1 6-3 13-7 18v-1l1-2 2-4s0-1 1-1v-2-2c-1 3-3 6-5 9-1 1-1 2-2 3 0-1 0-2 1-2v-1c1-1 2-2 2-3 1-1 1-2 2-3v-1z" class="U"></path><path d="M412 411c6 0 11 1 15 5 2 1 3 2 3 4-1 0-1-1-2-2h0c-2-1-5-2-8-3-5 0-9 2-12 5-1 1-3 3-4 5 1 2 0 3 0 5 0 1-1 4-2 5h0c0 3 1 4 1 7v6c-1 1 0 3-1 4 0 2-1 3-1 5l-1-2c1-1 1-6 1-8h-1v4h-1c0-2 0-4-1-5v-1c0-1 0-2 1-3h0v-2h-1v3l-1-1v-3c-2-1-1-4-2-5v6c-1-2-1-4-2-5v-1c0-2 1-4 2-6h1c0-1 1-3 1-4 1-1 1-2 1-2 1-2 2-4 4-5h0v1h0 1l3-3h1l-1-1c1-1 1-1 2-1s2-1 2-1c1-1 2-1 2-1z" class="o"></path><path d="M412 411c6 0 11 1 15 5 2 1 3 2 3 4-1 0-1-1-2-2h0c-2-1-5-2-8-3h-3c-1 0-1 0-2-1h0c-2 0-3 0-4 1l-6 3c1-2 3-3 5-4h0c-1-1-2 0-3 1l-1-1c1-1 1-1 2-1s2-1 2-1c1-1 2-1 2-1z" class="r"></path><path d="M400 447c1-1 1-3 0-4 0-1 0-4-1-5-1-6 1-14 5-19 0 0-1 4-2 4-2 6-2 9-2 15h1v-5c0-2 1-4 1-5 1-2 2-6 3-6 0 0 0 1-1 2v1c1 2 0 3 0 5 0 1-1 4-2 5h0c0 3 1 4 1 7v6c-1 1 0 3-1 4 0 2-1 3-1 5l-1-2c1-1 1-6 1-8h-1z" class="G"></path><path d="M411 428c1-1 3-2 5-3s4-1 6-2h6c2 1 3 2 5 3v1h-2c0-1-2-3-2-3h-3-1-3l-2 1 1 2h0l1 1v1c-1 0-1 1-1 1-2 2-3 5-4 7v1 6h-1 0v1 4h0c-1 1-1 2-1 3s-1 2-1 3c-1 0-1 0-1 1s-1 2-2 3v1l-1 1c1 1 1 1 0 2h0v1a30.44 30.44 0 0 1-8 8v1l-1-1 1-1h0l-2-1c0-1 1-2 1-3l1-1h0l2-2h1l4-9-2 2h-1c1-1 1-2 1-3h0l-3 3 1-3v-7-6c-1-3-1-5 0-8l1 1v-2c1-2 3-3 5-4z" class="p"></path><path d="M411 428v1h-1s-1 1-1 2h-1c-1 2-2 3-1 5v1 4h-1 0c1 3 0 6 0 8h0l-1-2v-6c-1-3-1-5 0-8l1 1v-2c1-2 3-3 5-4z" class="i"></path><path d="M405 441c-1-3-1-5 0-8l1 1c-1 0-1 1-1 2h1v6h-1v-1z" class="W"></path><path d="M420 425l1 2h0l1 1v1c-1 0-1 1-1 1-2 2-3 5-4 7h0v-3c1-1 1-3 2-4h0 0 0c-1 0-1 2-2 2 0 1 0 1-1 1 0-1 1-2 1-3v-1c-1 1-1 1-1 2-1 1-1 2-2 2v-1c1-1 1-1 1-2h-1l-1 1h0c-1 0-2 1-2 2h-1 0v-1l2-2c1 0 1 0 1-1-1 0-1 0-1-1h0c3-1 4-2 7-3h1z" class="q"></path><path d="M407 445c0 1 1 1 1 2 1-4 0-7 0-10l1-1c2 3 0 8 1 11 1-1 1-7 1-9h1v6h1 0v-6h1v8c0 1 0 2 1 3 1-3 0-8 0-11 1 2 1 4 1 6h0v1 4h0c-1 1-1 2-1 3s-1 2-1 3c-1 0-1 0-1 1s-1 2-2 3v1l-1 1c1 1 1 1 0 2h0v1a30.44 30.44 0 0 1-8 8v1l-1-1 1-1h0l-2-1c0-1 1-2 1-3l1-1h0l2-2h1l4-9-2 2h-1c1-1 1-2 1-3h0l-3 3 1-3v-7l1 2h0c0-2 1-5 0-8h0 1v4z" class="M"></path><path d="M413 444v6h-1v-6h1 0z" class="d"></path><path d="M405 447l1 2h0c0-2 1-5 0-8h0 1v4 8h1c1-4 1-8 1-11h0v7c0 2 0 4 1 5-1 0-1 1-1 1l-2 2h-1c1-1 1-2 1-3h0l-3 3 1-3v-7z" class="D"></path><path d="M415 449c1-3 0-8 0-11 1 2 1 4 1 6h0v1 4h0c-1 1-1 2-1 3s-1 2-1 3c-1 0-1 0-1 1s-1 2-2 3v1l-1 1c1 1 1 1 0 2h0v1a30.44 30.44 0 0 1-8 8v1l-1-1 1-1h0l-2-1c0-1 1-2 1-3l1-1h0l2-2h1 0v-1c1 0 2-2 3-3 1-3 3-5 3-8v-1h2c-1 3-2 4-3 7l-3 6c1 0 1-2 2-3 2-3 4-6 5-10 1-1 1-1 1-2zm-310 32v-1c-1-2-2-3-2-4v-1c2 2 2 4 3 6l3 3 1 1v-1c4 5 8 11 14 15h1l4 4h1c-1-2-5-3-5-5v-1l2 2c0-2-4-5-6-7 1-2-2-4-3-7h0l8 9c2 1 3 3 5 4l9 6v-2a30.44 30.44 0 0 1-8-8v-1c2 2 4 5 7 6 2 0 4 0 6 1h0v1c7 2 14 5 20 9h0c5 2 10 4 16 5h4c1 0 1 0 3 1 7 2 14 4 21 7 1 0 3 1 5 2h1l6 4 8 7h1c0-1 1-1 1-1l5 7 1-1c5 6 8 13 9 21 0 5 1 10 0 15 0 3-1 6-1 9 0 2 0 3-1 4 0 1-1 2-1 2l1 1c-3 3-7 7-10 9l1-3-1-1 1-2h-2l1-4h0c1-2 2-5 3-6 0-1 1-1 2-2v-4c0-3 0-6-1-9l1-1h0v-1c1-1 1-1 1-2v-4c-2-5-4-10-8-14l-1-2c-1 0-1 0-2-1-2-2-4-4-7-6-1-1-3-2-5-4-1-1-2-2-3-4l-3-1-1-1h0l-1 1c0-1 0-1-1-1s-1 0-2-1h-2c0-1-1-1-1-1h-2c0-1-1-1-2-2h-2c-1-1-3-1-5-2h-4-3c-4 0-8 0-12 1l-2-1h-5-8c-5 0-11 0-15-1h1v-1h2 0v-1h2 0v-1l-9-1 2-1c-3-1-6-2-9-4 0 0-1-1-2-1l-3-2v-1-1c-1 0-2 0-2-1h1 1c-1-1-2-2-3-2l-3-1c-4-2-9-8-13-12h1v-1c0-1 0-1-1-2h1c0-3-2-3-3-6v-1c0-1-1-1-1-2h0z" class="Y"></path><path d="M151 521h2c3-1 6-1 9-1-1 1-2 2-2 3l-6-1v-1h-2-1z" class="O"></path><path d="M162 520c3 0 5 0 8 2h0l-10 1c0-1 1-2 2-3z" class="B"></path><path d="M170 522h3v1c-3 1-5 1-7 1h-8c1-1 3 0 5-1h0c-3-1-6 1-9 0v-1l6 1 10-1z" class="R"></path><defs><linearGradient id="Y" x1="238.123" y1="571.73" x2="242.158" y2="577.2" xlink:href="#B"><stop offset="0" stop-color="#7f8180"></stop><stop offset="1" stop-color="#a6a4a8"></stop></linearGradient></defs><path fill="url(#Y)" d="M240 567c1 4 2 8 1 12h0-1l-1 1c0-3 0-6-1-9l1-1h0v-1c1-1 1-1 1-2z"></path><path d="M170 522c6 0 13 0 18 2h-3c-4 0-8 0-12 1l-2-1h-5c2 0 4 0 7-1v-1h-3 0z" class="G"></path><path d="M142 512c3 1 7 2 11 4 1 0 3 1 5 1h0l5 1v1c-2 0-3-1-5-1-2 2-8-1-11-2-2-2-4-2-5-4z" class="N"></path><path d="M148 520l3 1h1 2v1 1c3 1 6-1 9 0h0c-2 1-4 0-5 1-5 0-11 0-15-1h1v-1h2 0v-1h2 0v-1z" class="i"></path><path d="M148 520l3 1h1 2v1 1l-8-1h0v-1h2 0v-1z" class="K"></path><path d="M232 543h1c5 4 8 12 10 18l1-1c1 3 1 5 1 8-1 3 0 7-1 10v-9c0-2-1-3-1-5-2-8-6-14-11-21z" class="I"></path><path d="M239 580l1-1h1 0c-1 5-2 10-4 14-1 2-2 4-2 6l-1-1 1-2h-2l1-4h0c1-2 2-5 3-6 0-1 1-1 2-2v-4z" class="p"></path><path d="M234 592c1 0 2 0 3-1l-2 5h-2l1-4z" class="L"></path><path d="M234 592h0c1-2 2-5 3-6 0-1 1-1 2-2l-2 7c-1 1-2 1-3 1z" class="N"></path><defs><linearGradient id="Z" x1="141.358" y1="508.121" x2="131.07" y2="506.184" xlink:href="#B"><stop offset="0" stop-color="#a4a5a3"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#Z)" d="M125 497l2 2c2 2 5 4 7 6 3 1 5 3 8 5 5 2 10 4 16 7-2 0-4-1-5-1-4-2-8-3-11-4l-7-4-6-4c-2-2-4-3-5-5h1l4 4h1c-1-2-5-3-5-5v-1z"></path><path d="M214 532h0l-1-1c-1 0-1-1-2-1 0-1 0-1 1-1 0 1 1 1 1 1 1 0 1 1 2 1 1 1 3 2 5 3 3 2 9 6 10 10h0c1 1 1 2 1 3-1 0-1 0-2-1-2-2-4-4-7-6-1-1-3-2-5-4-1-1-2-2-3-4z" class="S"></path><path d="M214 525h1l6 4 8 7h1c0-1 1-1 1-1l5 7c4 6 7 11 8 18l-1 1c-2-6-5-14-10-18h-1c-2 0-8-9-10-11l-1-1-2-2-3-2h0c-1 0-2-1-2-2z" class="U"></path><path d="M140 502c2 1 5 2 7 3 1 0 2 1 2 1 1 1 2 2 3 2s2 0 3 1c0 0 2 1 3 1 2 1 5 3 7 4l7 2h1l2 1h1 2c1 1 4 0 6 1 1 0 2-1 3 0l1 1h0 1c2 1 4 1 6 2 5 2 8 3 12 6l2 1c-2 0-3-1-4-2-2-1-5-2-7-3-5-2-11-3-16-4-2 0-5 0-7-1-12-2-24-8-35-14v-2z" class="W"></path><defs><linearGradient id="a" x1="244.772" y1="586.838" x2="237.688" y2="583.077" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#6f6f70"></stop></linearGradient></defs><path fill="url(#a)" d="M237 541c5 6 8 13 9 21 0 5 1 10 0 15 0 3-1 6-1 9 0 2 0 3-1 4 0 1-1 2-1 2l1 1c-3 3-7 7-10 9l1-3c0-2 1-4 2-6l1 1h1c1-1 2-2 2-3 2-5 3-9 3-13 1-3 0-7 1-10 0-3 0-5-1-8-1-7-4-12-8-18l1-1z"></path><path d="M140 502a30.44 30.44 0 0 1-8-8v-1c2 2 4 5 7 6 2 0 4 0 6 1h0v1c7 2 14 5 20 9h0c5 2 10 4 16 5h4c1 0 1 0 3 1v1c4 1 8 2 11 4-2 0-3-1-4-1l-7-1-1-1c-1-1-2 0-3 0-2-1-5 0-6-1h-2-1l-2-1h-1l-7-2c-2-1-5-3-7-4-1 0-3-1-3-1-1-1-2-1-3-1s-2-1-3-2c0 0-1-1-2-1-2-1-5-2-7-3z" class="l"></path><path d="M139 499c2 0 4 0 6 1h0v1l1 1c1 1 5 2 5 4-4-2-8-4-12-7z" class="D"></path><path d="M145 501c7 2 14 5 20 9h0-1l1 1-1 1c-1-1-2-2-4-3 0 0-1 0-2-1-2-1-5 0-7-2 0-2-4-3-5-4l-1-1z" class="k"></path><defs><linearGradient id="b" x1="135.027" y1="517.493" x2="130.774" y2="500.546" xlink:href="#B"><stop offset="0" stop-color="#151517"></stop><stop offset="1" stop-color="#4b4a49"></stop></linearGradient></defs><path fill="url(#b)" d="M105 481v-1c-1-2-2-3-2-4v-1c2 2 2 4 3 6l3 3 1 1v-1c4 5 8 11 14 15 1 2 3 3 5 5l6 4 7 4c1 2 3 2 5 4h-4 1s0 1 1 1l4 2-8-1c-3-1-6-2-9-4 0 0-1-1-2-1l-3-2v-1-1c-1 0-2 0-2-1h1 1c-1-1-2-2-3-2l-3-1c-4-2-9-8-13-12h1v-1c0-1 0-1-1-2h1c0-3-2-3-3-6v-1c0-1-1-1-1-2h0z"></path><path d="M129 504l6 4h-3c-1-1-3-2-5-3l2-1z" class="F"></path><path d="M127 508l11 6c-2 0-3 0-4-1-1 1-1 1-2 1 0 0-1-1-2-1l-3-2v-1-1c-1 0-2 0-2-1h1 1z" class="J"></path><path d="M132 514c1 0 1 0 2-1 1 1 2 1 4 1s5 2 7 3l4 2-8-1c-3-1-6-2-9-4z" class="C"></path><path d="M105 481v-1c-1-2-2-3-2-4v-1c2 2 2 4 3 6l3 3 1 1v-1c4 5 8 11 14 15 1 2 3 3 5 5l-2 1-11-9h0l5 5v1h0l-6-5h0c1 2 3 3 5 5 1 1 1 1 1 2v1c-4-2-9-8-13-12h1v-1c0-1 0-1-1-2h1c0-3-2-3-3-6v-1c0-1-1-1-1-2h0z" class="R"></path><path d="M169 536c1 1 2 3 3 4 12-2 26-1 36 6h0c1 0 1 1 1 1h0v1c-4-2-8-4-12-5h-2l-1 1c-5-1-12-1-17 1-1 0-2 0-3 1 0-1-1-1-2-1l-2 1c-2 0-5 2-6 2-1 1-3 2-3 3l-5 5c-1 0-2 1-2 2l-2 4-1-1c0-1 1-2 2-3v-2c-5 7-7 13-5 21 2 10 9 19 17 24 6 4 14 6 21 4 3-1 5-2 7-3v1h3c0-1 1-1 2-1h3l1 1c1 0 2-1 3-2l1 1c2-1 3-2 4-4h0-1 0l-1-1 4-5 2 1c-1 3-2 5-4 7l1 1 1-1h0l-3 3h1 2c-1 1-1 1 0 1 2 0 4-2 5-3 5-4 8-9 10-15v3c-1 2-1 3-1 5 2-4 2-7 3-10 1 1 1 4 0 5v1h0c0 1-2 4-2 4 0 2-1 4-1 5l-1 1s0 1 1 1c0-1 1-2 2-3 0-2 1-3 3-4-1 4-2 7-5 10s-6 6-10 8c-7 3-12 5-19 6 1 0 1 0 2 1-4 1-9 1-12 1-15-1-30-5-40-15-7-8-10-17-10-27 1-12 5-23 14-32 1 0 3-1 4-2l4-2 1-1h0c2-1 3-1 4-2 2-1 3-2 5-3z" class="O"></path><path d="M167 545c2-1 3-1 5-2v2l-2 1c-1-1-2-1-3-1z" class="h"></path><path d="M210 610v1l-1 1c-1 1-2 1-4 1v-1l5-2z" class="J"></path><path d="M205 604h0c1 1 2 0 3 0l1-1h1l-1 1c-2 2-5 3-7 3h-1c1-1 3-2 4-3z" class="S"></path><path d="M156 547l6-4c0 1 0 1 1 2-1 1-3 1-5 2-2 2-4 4-7 6-1 1-2 3-3 4 2-4 5-8 8-10z" class="q"></path><path d="M172 543c7-1 15-1 23 0l-1 1c-5-1-12-1-17 1-1 0-2 0-3 1 0-1-1-1-2-1v-2z" class="r"></path><path d="M198 602h3l1 1c-6 4-11 5-18 5 4-1 8-3 12-5 0-1 1-1 2-1z" class="M"></path><path d="M169 536c1 1 2 3 3 4-4 0-7 2-10 3l-6 4v-1c0-1 0-1 1-2l2-2 1-1h0c2-1 3-1 4-2 2-1 3-2 5-3z" class="Y"></path><path d="M229 584c1 1 1 4 0 5v1h0c0 1-2 4-2 4 0 1-1 2-1 3-1 2-2 3-3 4-2 5-8 8-13 10v-1c5-2 10-6 13-11 1-1 2-3 3-5 2-4 2-7 3-10z" class="c"></path><path d="M153 556c1-4 6-4 8-7h-1l-6 4c1-2 4-5 6-5 1 0 1-1 2-1l5-2c1 0 2 0 3 1-2 0-5 2-6 2-1 1-3 2-3 3l-5 5c-1 0-2 1-2 2l-2 4-1-1c0-1 1-2 2-3v-2z" class="n"></path><path d="M206 602c2-1 3-2 4-4h0-1 0l-1-1 4-5 2 1c-1 3-2 5-4 7l1 1 1-1h0l-3 3-1 1c-1 0-2 1-3 0h0c-1 1-3 2-4 3l-3 1s-1 0-2 1h-3c-1 1-2 1-3 1h0-4c1-1 1-1 3-1h1l1-1c2 0 5-1 7-2 3-1 6-2 8-4z" class="G"></path><path d="M210 600l1 1 1-1h0l-3 3-1 1c-1 0-2 1-3 0h0l5-4z" class="Y"></path><path d="M228 598c0-2 1-3 3-4-1 4-2 7-5 10s-6 6-10 8v-2c-1 1-2 1-3 2l-1 1c-1 0-2 1-3 1l-1-1s1 0 1-1l1-1c5-2 11-5 13-10 1-1 2-2 3-4 0-1 1-2 1-3 0 2-1 4-1 5l-1 1s0 1 1 1c0-1 1-2 2-3z" class="L"></path><path d="M228 598c0-2 1-3 3-4-1 4-2 7-5 10s-6 6-10 8v-2h0c2-1 4-2 5-3 2-1 4-3 5-5 1-1 2-2 2-4h0z" class="Q"></path><path d="M172 540c12-2 26-1 36 6h0c1 0 1 1 1 1h0v1c-4-2-8-4-12-5-11-3-23-2-34 2-1-1-1-1-1-2 3-1 6-3 10-3z" class="r"></path><defs><linearGradient id="c" x1="189.134" y1="617.709" x2="188.546" y2="610.804" xlink:href="#B"><stop offset="0" stop-color="#505052"></stop><stop offset="1" stop-color="#737272"></stop></linearGradient></defs><path fill="url(#c)" d="M197 618h-11c-2 0-5 0-8-1-12-3-26-9-32-20-1-1-2-3-2-4l4 6c4 5 10 10 16 12 1 1 3 1 4 1-1-1-3-1-4-2-3-1-6-3-8-6 4 2 7 5 11 6-5-3-9-6-12-10 3 3 7 5 10 8 12 7 26 7 40 4v1c2 0 3 0 4-1 0 1-1 1-1 1l1 1c1 0 2-1 3-1l1-1c1-1 2-1 3-2v2c-7 3-12 5-19 6z"></path><path d="M198 52c1-3 2-5 4-6s4-1 5 0c7 2 12 10 16 15 1 2 3 4 4 7h1l4 8v1c1 1 2 3 3 4 3 4 11 11 11 16h-1c1 3 3 4 4 7 1 1 1 3 2 4l2 7c0 2 1 5 1 7v1c0 1-1 1-1 1l-1-1-5-3-1-1 1-1c-1-2-2-3-4-5l-2-2h-3 0v-1h-2-1c-1-1-2-2-4-3h-2 1-1c-2-1-7-1-9-1h-3 0c1-1 2-1 2-2l-1-1h-2c1 0 2-1 2-2h0 2l-1-2h-4v-1c-4 1-8 3-12 3l-6 3v-1l-1-1h-2l1-2v-2l-1-1 2-6 1-1v1c3-4 4-8 7-11 2-3 4-4 7-6v-1c1-1 2-1 3-2v-1c-2 1-4 1-6 2-1 1-2 1-4 1l-1 2-1-1c-2 1-3 5-4 7v-1c-1-1-1-2-1-4v-1h-1v-4-1-1-1h-1 0l1-1v-1c0-4 0-11 2-14z" class="S"></path><path d="M225 80l1-1h1 0c1 0 1 0 1 1h1 0l2 1h0-1c1 1 1 1 2 1 1 1 2 2 3 2 4 4 7 8 10 13 1 3 3 4 4 7l-1-1-1 1h0l-3-5h-1c-1-2-3-4-5-6l-1-1c-1-1-2-3-4-4-1-1-2-2-4-3h0c0-1-1-1-2-2-2 0-3-1-4-1-2 0-2-1-3-2 3 0 6 2 9 1-2 0-3-1-4-1z" class="M"></path><path d="M229 85l6 3v-1c2 0 2 1 3 2 3 3 5 6 6 10h-1c-1-2-3-4-5-6l-1-1c-1-1-2-3-4-4-1-1-2-2-4-3h0z" class="D"></path><path d="M198 52h0l3-4c1-1 2-1 4-1 1 0 2 0 3 1h0l-2 1c0 1 1 1 1 2 2 1 4 1 6 2 0 1-1 1-1 1h-2l3 1c1 1 2 1 3 2l-2 1h0c1 1 1 1 0 1h0c-1 0-1-1-2-1s-7 2-7 2c-2 1-4 2-6 4h-1 0l-2 2c0-4 0-11 2-14z" class="T"></path><path d="M212 54h0c-2-1-7-1-10 0l-2 1h0 0c1-2 1-2 2-3h3c0-1 1-1 2-1 2 1 4 1 6 2 0 1-1 1-1 1z" class="X"></path><path d="M198 52h0l3-4c1-1 2-1 4-1 1 0 2 0 3 1h0l-2 1c-2 0-4 1-6 2-1 2-1 4-2 6v7l-2 2c0-4 0-11 2-14z" class="J"></path><path d="M202 55h0c2-1 4-1 6-1h1 1l3 1c1 1 2 1 3 2l-2 1h0c1 1 1 1 0 1h0c-1 0-1-1-2-1s-7 2-7 2c-2 0-4 1-5 1l-1-1 2-4 1-1z" class="E"></path><path d="M202 55l1 1h1 1 0 3v1h-1v1c-1 0-2 1-3 1l-2 1-1-1 1-2-1-1 1-1z" class="Z"></path><path d="M202 55h0c2-1 4-1 6-1h1 1l3 1c-2 2-4 2-6 3v-1h1v-1h-3 0-1-1l-1-1z" class="J"></path><path d="M208 54h1v1 1h-4v-1c1 0 2-1 3-1z" class="i"></path><path d="M223 67c2 2 3 4 4 6v1h-1v1c1 0 2 1 3 2-1 0-2 0-3-1v1h0v1l3 2h0-1c0-1 0-1-1-1h0-1l-1 1c1 0 2 1 4 1-3 1-6-1-9-1l-4 1 1 2-2 1-3 1c-1 0-1-1-1-1v-1l-2 1-3 2c-4 3-6 6-8 11l-2 2h0v-1h-1l-1-1 2-6 1-1v1c3-4 4-8 7-11 2-3 4-4 7-6v-1c1-1 2-1 3-2v-1h0 4c1 1 1 1 2 1h4c0-2 0-3-1-4z" class="a"></path><path d="M197 90v1c3-4 4-8 7-11h1l-2 3 1 1c-2 2-3 6-5 8-2 1-3 3-3 6h-1l-1-1 2-6 1-1z" class="B"></path><defs><linearGradient id="d" x1="212.403" y1="83.116" x2="219.379" y2="77.309" xlink:href="#B"><stop offset="0" stop-color="#bab5ba"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#d)" d="M206 86v-1c0-1 4-4 5-5h0 1c3-2 9-1 13 0 1 0 2 1 4 1-3 1-6-1-9-1l-4 1 1 2-2 1-3 1c-1 0-1-1-1-1v-1l-2 1-3 2z"></path><path d="M214 81h2l1 2-2 1-3 1c-1 0-1-1-1-1v-1l-2 1c1-2 3-2 5-3z" class="B"></path><path d="M214 81h2l1 2-2 1c0-1-1-1-1-1v-2z" class="E"></path><path d="M223 67c2 2 3 4 4 6v1h-1v1c1 0 2 1 3 2-1 0-2 0-3-1v1h0v1c-5-2-11-2-16 0-3 2-4 4-6 6l-1-1 2-3h-1c2-3 4-4 7-6v-1c1-1 2-1 3-2v-1h0 4c1 1 1 1 2 1h4c0-2 0-3-1-4z" class="D"></path><path d="M211 74h5 0v1c-3 0-8 2-11 5h0-1c2-3 4-4 7-6z" class="W"></path><path d="M216 74c3-1 6 0 10 1 1 0 2 1 3 2-1 0-2 0-3-1-2-1-6-1-10-1v-1h0z" class="k"></path><path d="M223 67c2 2 3 4 4 6v1h-1v1c-4-1-7-2-10-1h-5v-1c1-1 2-1 3-2v-1h0 4c1 1 1 1 2 1h4c0-2 0-3-1-4z" class="G"></path><path d="M216 57c2 1 2 4 3 4 1 1 2 1 3 3h0v3h1c1 1 1 2 1 4h-4c-1 0-1 0-2-1h-4 0c-2 1-4 1-6 2-1 1-2 1-4 1l-1 2-1-1c-2 1-3 5-4 7v-1c-1-1-1-2-1-4v-1h-1v-4-1-1-1h-1 0l1-1v-1l2-2h0 1c2-2 4-3 6-4l7-2c1 0 1 1 2 1h0c1 0 1 0 0-1h0l2-1z" class="Y"></path><path d="M207 66h0l-1 1h1l-3 3-2 1-1-1c2-2 4-3 6-4z" class="G"></path><path d="M202 71l2-1v1c0 1-2 2-2 3-2 1-3 5-4 7v-1c-1-1-1-2-1-4 0 1 0 0 1 1l4-6h0z" class="F"></path><path d="M205 60l7-2c1 0 1 1 2 1h0l3 2v1h-3-3c-5 1-10 3-12 7-2 2-2 4-2 6h-1v-4-1-1-1h-1 0l1-1v-1l2-2h0 1c2-2 4-3 6-4z" class="f"></path><path d="M199 64c1 0 5-2 7-3-3 2-7 4-9 8 0 0 0 1-1 1v-1-1h-1 0l1-1v-1l2-2h0 1z" class="D"></path><path d="M205 60l7-2c1 0 1 1 2 1h0l3 2v1h-3-3c0-1 2-1 2-1h0v-1c-3-1-5 0-7 1s-6 3-7 3c2-2 4-3 6-4z" class="R"></path><path d="M216 57c2 1 2 4 3 4 1 1 2 1 3 3h0v3h1c1 1 1 2 1 4h-4c-1 0-1 0-2-1h-4 0c-2 1-4 1-6 2-1 1-2 1-4 1l-1 2-1-1c0-1 2-2 2-3v-1l3-3h-1l1-1h0c2-2 5-3 7-4h3v-1l-3-2c1 0 1 0 0-1h0l2-1z" class="j"></path><path d="M221 66l1 1h1c1 1 1 2 1 4h-4c-1 0-1 0-2-1h-4 0c-2 1-4 1-6 2-1 1-2 1-4 1h0c4-4 9-4 14-5 1 1 1 1 3 1v-1h-1l1-2z" class="F"></path><path d="M216 57c2 1 2 4 3 4 1 1 2 1 3 3h0v3l-1-1c-5-1-9-1-14 1h-1l1-1h0c2-2 5-3 7-4h3v-1l-3-2c1 0 1 0 0-1h0l2-1z" class="B"></path><path d="M216 81l4-1c1 1 1 2 3 2 1 0 2 1 4 1 1 1 2 1 2 2h0c2 1 3 2 4 3 2 1 3 3 4 4l1 1c2 2 4 4 5 6h1l3 5h0l1-1 1 1c1 1 1 3 2 4l2 7c0 2 1 5 1 7v1c0 1-1 1-1 1l-1-1-5-3-1-1 1-1c-1-2-2-3-4-5l-2-2h-3 0v-1h-2-1c-1-1-2-2-4-3h-2 1-1c-2-1-7-1-9-1h-3 0c1-1 2-1 2-2l-1-1h-2c1 0 2-1 2-2h0 2l-1-2h-4v-1c-4 1-8 3-12 3l-6 3v-1l-1-1h-2l1-2v-2h1v1h0l2-2c2-5 4-8 8-11l3-2 2-1v1s0 1 1 1l3-1 2-1-1-2z" class="f"></path><path d="M237 96l4 4c0 2 1 3 1 4-1 0-1-1-1-2-1-1-2-1-3-2 0-1-1-2-1-2h0v-2z" class="E"></path><path d="M248 117c1 0 1 1 2 1h0c1 2 2 3 2 5l-5-3-1-1 1-1 1-1z" class="L"></path><path d="M248 117c1 0 1 1 2 1h0c-1 1-2 1-2 1-1 0-1 0-1 1l-1-1 1-1 1-1z" class="Q"></path><path d="M244 99l3 5h0c2 4 3 8 5 12l-1 1v-1c-1-2-2-4-3-5 0-2-2-4-2-5 0-2-1-2-1-3 0-2-1-2-1-3l-1-1h1z" class="C"></path><path d="M239 105v1c3 2 6 5 7 8l2 3-1 1c-1-2-2-3-4-5l-2-2c-1 0-1 0-2-1v-1h1c0-1-2-2-3-3l2-1z" class="H"></path><path d="M212 90c-1 0-1 0-2 1v-1l2-2h1 1 1 1c1-1 2-1 3-1 1 1 3 1 4 1s2 1 2 1h2c2 1 3 3 5 2 1 1 2 2 4 3h1v1h-1c0 1 1 1 1 1v2c-2-2-4-3-6-4l-3-2c1 1 2 1 4 1 0 1 1 1 1 1v-1h-1c-3-2-6-2-9-3-2-1-4-1-6-1s-3 1-5 1z" class="D"></path><path d="M216 81l4-1c1 1 1 2 3 2 1 0 2 1 4 1 1 1 2 1 2 2h0c2 1 3 2 4 3 2 1 3 3 4 4l1 1h-1c-1-1-2-1-3-2h-2c-2 1-3-1-5-2h-2s-1-1-2-1-3 0-4-1c-1 0-2 0-3 1h-1-1-1-1l-2 2v1c1-1 1-1 2-1h0c2 1 3 1 5 0l1 1c-2 0-4 1-5 1l-6 3c-1 0-1-1-2 0-1 0-2 1-3 2h-2-2c2-5 4-8 8-11l3-2 2-1v1s0 1 1 1l3-1 2-1-1-2z" class="K"></path><path d="M209 84l2-1v1s0 1 1 1l-3 2c-1 0-1 0-2 1l-4 4c-1 0-1 1-2 2l-1 3h-2c2-5 4-8 8-11l3-2z" class="g"></path><path d="M216 81l4-1c1 1 1 2 3 2 1 0 2 1 4 1 1 1 2 1 2 2h0c2 1 3 2 4 3-1 0-4-2-5-2-3-1-5-2-7-2-3 0-5 1-7 1-2 1-3 2-5 2l3-2 3-1 2-1-1-2z" class="m"></path><path d="M216 81l4-1c1 1 1 2 3 2 1 0 2 1 4 1 1 1 2 1 2 2h0c-4-1-8-3-12-2l-1-2z" class="Q"></path><path d="M202 97c1-1 2-2 3-2 1-1 1 0 2 0l6-3-1 1h1c1 0 2 1 3 1h0v-1h1 0c2 0 4 0 5 1 2 0 4 1 6 1h0c3 1 9 5 11 8v1l1 1-1 1v-1l-2 1c1 1 3 2 3 3h-1v1c1 1 1 1 2 1h-3 0v-1h-2-1c-1-1-2-2-4-3h-2 1-1c-2-1-7-1-9-1h-3 0c1-1 2-1 2-2l-1-1h-2c1 0 2-1 2-2h0 2l-1-2h-4v-1c-4 1-8 3-12 3l-6 3v-1l-1-1h-2l1-2v-2h1v1h0l2-2h2 2z" class="d"></path><path d="M216 94c3 1 7 1 9 3l-11-1c0-1 1-1 2-1h1l-1-1h0z" class="E"></path><path d="M198 97h2 2 2l-6 2c-1 1-2 1-3 1v-2h1v1h0l2-2z" class="F"></path><path d="M202 97c1-1 2-2 3-2 1-1 1 0 2 0l6-3-1 1h1c1 0 2 1 3 1l1 1h-1c-1 0-2 0-2 1-3 0-7 0-10 1h-2z" class="C"></path><path d="M211 97c1 0 3 0 4 1h0c-4 1-8 3-12 3l-6 3v-1l-1-1c5-3 10-4 15-5z" class="T"></path><path d="M211 97c4-1 8 0 12 1h4c3 2 7 4 10 4 1 1 1 2 2 3l-2 1c1 1 3 2 3 3h-1v1c1 1 1 1 2 1h-3 0v-1h-2-1c-1-1-2-2-4-3h-2 1-1c-2-1-7-1-9-1h-3 0c1-1 2-1 2-2l-1-1h-2c1 0 2-1 2-2h0 2l-1-2h-4v-1h0c-1-1-3-1-4-1z" class="Z"></path><path d="M224 100c1 0 2 0 4 1-1 0-2 0-3 1l-1-1v-1z" class="V"></path><path d="M219 99c1 0 3 1 5 1v1h-4l-1-2z" class="G"></path><path d="M229 102c2 0 5 2 7 2 1 0 1 1 1 2 1 1 3 2 3 3h-1v1c1 1 1 1 2 1h-3 0v-1h-2-1c-1-1-2-2-4-3 2 0 1-1 2-2 0-1-3-2-4-3z" class="B"></path><path d="M233 105l6 4v1c1 1 1 1 2 1h-3 0v-1h-2-1c-1-1-2-2-4-3 2 0 1-1 2-2z" class="L"></path><path d="M220 101h4l1 1c1-1 2-1 3-1 0 0 1 0 1 1 1 1 4 2 4 3-1 1 0 2-2 2h-2 1-1c-2-1-7-1-9-1h-3 0c1-1 2-1 2-2l-1-1h-2c1 0 2-1 2-2h0 2z" class="U"></path><path d="M228 101s1 0 1 1c1 1 4 2 4 3-1 1 0 2-2 2h-2 1 2c-1-3-5-4-7-5 1-1 2-1 3-1z" class="E"></path><path fill="#fff" d="M197 543c4 1 8 3 12 5v-1c1 2 3 2 5 3l-1 2 4 2c7 6 12 14 12 24 1 2 1 4 0 6-1 3-1 6-3 10 0-2 0-3 1-5v-3c-2 6-5 11-10 15-1 1-3 3-5 3-1 0-1 0 0-1h-2-1l3-3h0l-1 1-1-1c2-2 3-4 4-7l-2-1-4 5 1 1h0 1 0c-1 2-2 3-4 4l-1-1c-1 1-2 2-3 2l-1-1h-3c-1 0-2 0-2 1h-3v-1c-2 1-4 2-7 3-7 2-15 0-21-4-8-5-15-14-17-24-2-8 0-14 5-21v2c-1 1-2 2-2 3l1 1 2-4c0-1 1-2 2-2l5-5c0-1 2-2 3-3 1 0 4-2 6-2l2-1c1 0 2 0 2 1 1-1 2-1 3-1 5-2 12-2 17-1l1-1h2z"></path><path d="M193 602c6-3 8-7 12-13-1 3-2 7-4 10l-3 3c-1 0-2 0-2 1h-3v-1z" class="U"></path><path d="M201 602c5-3 9-9 11-14 1-2 1-4 2-6l1 1c-1 2-1 6-3 9l-4 5 1 1h0 1 0c-1 2-2 3-4 4l-1-1c-1 1-2 2-3 2l-1-1z" class="B"></path><path d="M151 561l1 1 2-4c0-1 1-2 2-2l5-5c0-1 2-2 3-3l-1 3h0c1 0 2-1 2 0s0 1-1 1l-1 1h0c-1 1-4 3-4 4-2 2-3 3-4 5h-1c0 1-1 2-2 3v-1h-1c0 1-1 3-1 4-1-1-1-1-1-2 1 0 0-1 1-1v-1c0-1 1-2 1-3z" class="h"></path><path d="M187 553c-8 2-15 7-20 14-2 3-4 7-5 10 2-8 6-16 14-21s18-6 26-4c7 2 15 7 19 13l3 6c1 2 1 3 2 4 2 2 1 6 1 8v3c-2 6-5 11-10 15v-1c1-1 4-4 4-6 1-2 1-4 2-5 1-3 1-8 1-11-1-3-2-5-3-8 0-1-1-1-1-2l-1-1-3-3c-1-1-2-1-3-2h0l-1-1c-3-2-8-5-11-4h0c-2-1-4-2-6-2-1 0-3 0-5-1-1 0-2-1-3-1z" class="F"></path><path d="M187 553c7-1 14-1 19 2h0-2c-2 0-4 0-5-1h-1-2l-1 1c-1 0-3 0-5-1-1 0-2-1-3-1z" class="e"></path><path d="M195 555l1-1h2 1c1 1 3 1 5 1h2 0c4 1 8 3 10 6-2-1-5-4-8-4h0c1 1 5 3 5 4v1l-1-1c-3-2-8-5-11-4h0c-2-1-4-2-6-2z" class="h"></path><path d="M201 557c3-1 8 2 11 4l1 1h0c1 1 2 1 3 2l3 3 1 1c0 1 1 1 1 2 1 3 2 5 3 8 0 3 0 8-1 11-1 1-1 3-2 5 0 2-3 5-4 6v1c-1 1-3 3-5 3-1 0-1 0 0-1h-2-1l3-3h0l-1 1-1-1c2-2 3-4 4-7 2-3 3-7 4-11 0-6-1-11-5-16-5-5-11-7-18-8 1-2 9 2 11 1 0-1-4-2-5-2z" class="D"></path><path d="M218 582c1 1 1 3 1 4-1 1 0 3-1 5 0 0 0 1-1 1 0 2-1 3-2 4 0 1-1 2-2 3 0 1 0 1-1 1h0l-1 1-1-1c2-2 3-4 4-7 2-3 3-7 4-11z" class="g"></path><defs><linearGradient id="e" x1="190.274" y1="542.031" x2="214.188" y2="592.768" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#333232"></stop></linearGradient></defs><path fill="url(#e)" d="M197 543c4 1 8 3 12 5v-1c1 2 3 2 5 3l-1 2 4 2c7 6 12 14 12 24 1 2 1 4 0 6-1 3-1 6-3 10 0-2 0-3 1-5v-3-3c0-2 1-6-1-8-1-5-2-9-6-13-7-8-17-12-28-13-7-1-18 0-23 5-2 2-4 5-6 7 1-2 2-3 2-5 0-1 0-1-1-1l-1 1s-1 1-2 1c0 1 0 1-1 2v-1h0c0-1 0-1-1-1 0-1 3-3 4-4h0l1-1c1 0 1 0 1-1s-1 0-2 0h0l1-3c1 0 4-2 6-2l2-1c1 0 2 0 2 1 1-1 2-1 3-1 5-2 12-2 17-1l1-1h2z"></path><path d="M204 547h1c2 0 2 1 4 1v-1c1 2 3 2 5 3l-1 2-9-5z" class="M"></path><path d="M197 543c4 1 8 3 12 5-2 0-2-1-4-1h-1c-4 0-7-2-10-3l1-1h2z" class="I"></path><path d="M174 546c1-1 2-1 3-1h1 11 4v1h-1c-2-1-4 0-7 0-5 1-10 2-14 5-2 1-4 3-6 5 0-1 0-1-1-1l-1 1s-1 1-2 1c0 1 0 1-1 2v-1h0c0-1 0-1-1-1 0-1 3-3 4-4h0l1-1c1 0 1 0 1-1s-1 0-2 0h0l1-3c1 0 4-2 6-2l2-1c1 0 2 0 2 1z" class="q"></path><path d="M170 546l2-1c1 0 2 0 2 1-1 1-2 1-4 3-1 0-1 1-2 1v1c-1 0-1 0-1 1-1 0-3 2-4 2-1 1-1 2-2 3 0 1 0 1-1 2v-1h0c0-1 0-1-1-1 0-1 3-3 4-4h0l1-1c1 0 1 0 1-1s-1 0-2 0h0l1-3c1 0 4-2 6-2z" class="s"></path><path d="M207 296c-3-1-5-3-7-5-7-5-11-13-12-22-1-12 4-23 11-32 13-15 34-22 53-23-1 0-2 2-3 2 0 1-1 2-1 3h-1c-1 1-1 2-2 3-1 2-2 3-2 5h-1-1c0-1 0-1 1-2-2 1-3 4-4 6-1 0-1 1-1 2-1 1-1 1-1 2h0c-1 2-1 4 0 6h0l12 6 2 2 1 1c2 2 4 5 6 8l2 3v1l1 3c0 4 0 6-2 10-1 2-4 4-7 6h0l-1 1s1 1 0 2v1c-1 0 0 0 0 1-2 4-6 7-9 10-3 1-5 2-9 3l-2 1h-7c-2 0-5-1-7-1l-5-1-2-1-2-1z" class="g"></path><path d="M194 265c0 1 0 2 1 3v4c-1 0-1 1-1 2h0l-1-2 1-7z" class="E"></path><path d="M203 242l2-4v2c1-2 1-2 3-3-1 3-3 5-4 7l-1-2zm-8 30v11c-1-4-2-7-2-11l1 2h0c0-1 0-2 1-2z" class="C"></path><path d="M197 256v-1-1h1c0 2-1 4-1 6-1 1-1 3-1 5l-1 3c-1-1-1-2-1-3l3-9z" class="F"></path><path d="M230 240c-1 0-11-2-11-2v-1h4c5 1 9 2 13 4h0-2 0l1 1-5-2z" class="e"></path><path d="M198 267v2l1 1v2l1-1v4l1 8-1-1s-1-1-1-2 0-1-1-1v2h0l-1-1v-7l1-6z" class="D"></path><path d="M198 267v2l1 1v2l-1 7c-1-2 0-4-1-6l1-6z" class="a"></path><path d="M208 237c0-1 0-1 1-2 1-2 3-4 5-6 1-2 4-4 6-4-3 3-4 6-7 9h-1c-1 1-2 3-3 4 0 1-1 2-1 2 0 1 0 1-1 1v1c0-2 1-3 1-5z" class="H"></path><path d="M233 220h1c0 1-1 1-1 2 0 2-2 4-3 6s-1 3-3 5v-1h0v-1s-1 0-1-1h-1l-1 2h0c2-4 5-9 9-12z" class="G"></path><path d="M220 225h0c-2 5-7 8-8 13 0 2-1 3-1 5-1 1 0 1 0 1 0 1-1 2-1 2v4c-1 1-1 3-1 4v1 2h0v-1c0-1 0-2 1-2v-6c1 0 1-1 1-2s1-2 2-3v1h0c-1 1-2 3-2 5-1 1-1 2-1 3l-3 16v-1c0-3 0-5 1-8 0-1-1-1 0-2v-2-1l1-1c0-3 1-5 0-8h0 1v-2c1-2 1-3 1-5l2-4c3-3 4-6 7-9z" class="Y"></path><path d="M203 242l1 2c-3 6-4 13-6 19h0v-3h0c0-2 1-3 1-4h0v-1l1-1v-2-1h0l1-1v-1c-2 3-2 4-2 7-1 1-1 2-1 3-1 2-1 3-2 4v2c0-2 0-4 1-5 0-2 1-4 1-6h-1v1 1l-1-1 1-1v-2h0c0-1 1-1 1-2 1-2 3-6 5-8z" class="B"></path><path d="M208 237c0 2-1 3-1 5-1 1-2 4-2 6h-1c-2 2-4 18-5 22l-1-1v-2-4c2-6 3-13 6-19 1-2 3-4 4-7h0z" class="j"></path><path d="M207 242v-1c1 0 1 0 1-1 0 0 1-1 1-2 1-1 2-3 3-4h1l-2 4c0 2 0 3-1 5l-1 1c-2 3-2 7-3 11-1 2-1 5-2 8 0 2 0 4-1 6v-7c-1-4 2-10 2-14 0-2 1-5 2-6z" class="V"></path><path d="M211 238c0 2 0 3-1 5l-1 1-1-1c1-2 2-4 3-5z" class="T"></path><path d="M210 243v2h-1 0c1 3 0 5 0 8l-1 1v1 2c-1 1 0 1 0 2-1 3-1 5-1 8v1c1 2 1 4 0 6v2-1c-1-2-2-2-2-3-1-1-1-3-1-4v6 1c-1-2-1-4-1-6 1-2 1-4 1-6 1-3 1-6 2-8 1-4 1-8 3-11l1-1z" class="U"></path><path d="M204 275v-1-6c0 1 0 3 1 4 0 1 1 1 2 3v1c0 5 2 10 3 14 1 2 1 4 2 6h1l-1 1h1c-1 0-1 1-2 1l-2-1-2-1v-1c0-1-2-4-3-5s-1-3-2-4l1-1v-3h0c1 1 1 2 1 3 1-1 1-2 1-3-1-2-1-5-1-7z" class="Y"></path><path d="M202 286l1-1v-3h0c1 1 1 2 1 3 1-1 1-2 1-3 1 5 2 10 4 15l-2-1v-1c0-1-2-4-3-5s-1-3-2-4z" class="Z"></path><defs><linearGradient id="f" x1="203.595" y1="273.935" x2="199.21" y2="264.361" xlink:href="#B"><stop offset="0" stop-color="#818280"></stop><stop offset="1" stop-color="#9d989c"></stop></linearGradient></defs><path fill="url(#f)" d="M205 248c0 4-3 10-2 14v7c0 2 0 4 1 6 0 2 0 5 1 7 0 1 0 2-1 3 0-1 0-2-1-3h0v3l-1 1-1-3-1-8v-4l-1 1v-2c1-4 3-20 5-22h1z"></path><path d="M204 248c0 4-2 9-3 13s-1 10-1 14v-4l-1 1v-2c1-4 3-20 5-22z" class="G"></path><path d="M230 240l5 2 3 3h-1c0 2 0 4-1 5v1l-1 1c-1 2-1 4-2 5h0l-1 4c1 1 1 2 1 3v1h-1c0-1-1-3-1-4l-2 1v-5-6l1-9v-2z" class="H"></path><g class="E"><path d="M235 252c0-1-1-2-1-3h-1l1-1h2v2 1l-1 1z"></path><path d="M231 255v-5l1-1c0 4-1 9 0 12 1 1 1 2 1 3v1h-1c0-1-1-3-1-4l-2 1v-5l1-1h0l1-1z"></path></g><path d="M230 256l1-1v5 1l-2 1v-5l1-1h0z" class="I"></path><path d="M230 256l1-1v5l-1-1v-3h0z" class="H"></path><path d="M230 242c1 0 2 0 4 1-1 2-2 4-2 6l-1 1v5l-1 1h0l-1 1v-6l1-9z" class="V"></path><path d="M229 251v-2h1v7h0l-1 1v-6z" class="F"></path><path d="M218 295v-1c-1-2-2-5-3-7v-1-1-1c-1-1-1-2-1-3v-2c-1-2-1-4-1-5-1-2 0-4-1-7 0-1 0-2 1-4 0 1 0 3 1 3-1 2 0 2 0 4v4c1 3 1 7 2 10 0 1 0 2 1 3h0c0 1 0 1 1 1v-2c0-1-1-1-1-1l-2-12c-1-1 0-3 0-5h0c1 1 0 3 2 4 0 1 1 1 1 2s2 6 1 8l3 6c0 1 2 2 3 4s3 3 4 5c1 1 3 1 3 2l-2 1h-7c-2 0-5-1-7-1 0-1 0-1 1-2v-1l1-1z" class="E"></path><path d="M218 295c1 1 1 2 2 3v-3l3 5c-2 0-5-1-7-1 0-1 0-1 1-2v-1l1-1z" class="D"></path><path d="M215 273c-1-1 0-3 0-5h0c1 1 0 3 2 4 0 1 1 1 1 2s2 6 1 8c0 2 1 4 1 5h0c-1-1-1-3-2-4 0-1 0-1-1-2v-1-1-1c0-1 0-1-1-2v-1-3h-1v1z" class="N"></path><path d="M210 252c1 1 1 1 1 2h0c-1 2-1 4-1 5 1 0 1 0 1-1l1-3v-1c0-2 0-4 1-5v-1c1-1 1-1 1-2h0v-1h1 0l-2 6v2c0 1 0 2-1 3v5 1 3c0-1 0-2 1-3v1h0c-1 2-1 3-1 4 1 3 0 5 1 7 0 1 0 3 1 5v2c0 1 0 2 1 3v1 1 1c1 2 2 5 3 7v1l-1 1v1c-1 1-1 1-1 2l-5-1c1 0 1-1 2-1h-1l1-1h-1c-1-2-1-4-2-6-1-4-3-9-3-14v-2c1-2 1-4 0-6l3-16z" class="G"></path><path d="M212 296l1-1c-1-2-1-3-1-5l1-1v1c0 1 1 2 1 3 1 2 2 3 3 4-1 1-1 1-1 2l-5-1c1 0 1-1 2-1h-1l1-1h-1z" class="b"></path><path d="M212 262v3c0-1 0-2 1-3v1h0c-1 2-1 3-1 4 1 3 0 5 1 7 0 1 0 3 1 5v2c0 1 0 2 1 3v1 1 1c1 2 2 5 3 7v1l-1 1v1c-1-1-2-2-3-4l1-1c0-1-1-3-2-4v-2c-1 0 0-2-1-3v-2c-1-1 0-3-1-4 0-2 1-3 0-4 0-2-1-6 0-8v-1s0-1 1-2z" class="K"></path><path d="M222 242h1c0 3-1 5-1 8-1 9 1 18 4 26h1l1 3c1 1 2 3 3 4 0 2 2 3 3 5v-1c2 2 3 4 6 5v1l-1 1h0l2 2c-3 1-5 2-9 3 0-1-2-1-3-2-1-2-3-3-4-5h2c1 1 1 1 2 1 1 1 2 1 3 1l2 2c1 1 1 1 2 1v-2h-2s-1 0-1-1c-1 0-2-1-3-2l-3-3c-3-4-6-8-6-13v-1c0-1 0-2-1-3 0-3-1-5-1-7h0c0 1 1 1 1 2v-1-6c0-6 1-12 2-18z" class="B"></path><path d="M225 280c1 1 1 2 3 2 0 1 1 2 1 3h1c1 0 1 0 1 1s1 1 1 1l2 3h-1v1l-1-1c0-1-2-2-2-3h0l-2-1h0c-2-2-2-4-3-6z" class="E"></path><path d="M222 268l3 10c1 1 2 3 3 4-2 0-2-1-3-2 0-1-2-3-2-4-2-2-2-5-1-8z" class="F"></path><path d="M226 276h1l1 3c1 1 2 3 3 4 0 2 2 3 3 5v-1c2 2 3 4 6 5v1l-1 1h0v1h0c-1 0-2 0-3-1 0-1-1-1-1-2-1 0-1-1-2-2h1l-2-3s-1 0-1-1 0-1-1-1l-4-9z" class="C"></path><defs><linearGradient id="g" x1="219.283" y1="259.579" x2="224.28" y2="260.725" xlink:href="#B"><stop offset="0" stop-color="#6f6d6e"></stop><stop offset="1" stop-color="#8d9091"></stop></linearGradient></defs><path fill="url(#g)" d="M222 242h1c0 3-1 5-1 8-1 9 1 18 4 26l4 9h-1c0-1-1-2-1-3-1-1-2-3-3-4l-3-10c-1-2-2-5-2-8 0-6 1-12 2-18z"></path><path d="M222 242c0-1 1-2 1-2 1 0 1 0 1 1 1 0 1-1 2 0-1 3-2 6-2 10 1 4 2 9 2 13v-1c0-1 0 0 1-1v-2 6h1c0-1 0-3 1-4l2-1c0 1 1 3 1 4h1v-1c0-1 0-2-1-3l1-4h0c1 4 2 8 2 12v1h1s0-1 1-1c0 3 0 6 1 9-1 1-2 2-3 2 0 1 0 1-1 1l2 2c5 2 9 1 14-1 0 0 1 1 0 2v1c-1 0 0 0 0 1-2 4-6 7-9 10l-2-2h0l1-1v-1c-3-1-4-3-6-5v1c-1-2-3-3-3-5-1-1-2-3-3-4l-1-3h-1c-3-8-5-17-4-26 0-3 1-5 1-8h-1z" class="B"></path><path d="M225 262v2c0 1 1 2 1 4h-1c-1-2 0-4 0-6z" class="I"></path><path d="M227 276h0c0-2-1-4-2-6h1v1c2 2 2 5 3 8l5 8v1c-1-2-3-3-3-5-1-1-2-3-3-4l-1-3z" class="Q"></path><defs><linearGradient id="h" x1="229.585" y1="273.5" x2="238.618" y2="264.085" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#h)" d="M232 261l1-4h0c1 4 2 8 2 12v1h1s0-1 1-1c0 3 0 6 1 9-1 1-2 2-3 2s-4-7-4-9l-2-9c2 2 3 5 4 8 1-2 0-5 0-6s0-2-1-3z"></path><path d="M230 278l-2-5c0-1-2-5-1-6 1 0 2 4 3 5 0 0 0-1 1-1 0 2 3 9 4 9 0 1 0 1-1 1l2 2c5 2 9 1 14-1 0 0 1 1 0 2v1c-1 0 0 0 0 1-2 4-6 7-9 10l-2-2h0l1-1v-1c-3-1-4-3-6-5-1-2-3-5-5-8l1-1z" class="g"></path><path d="M230 278l-2-5c0-1-2-5-1-6 1 0 2 4 3 5 0 0 0-1 1-1 0 2 3 9 4 9 0 1 0 1-1 1l2 2h0c0 3 4 4 5 6h0c-3-1-4-3-6-5s-4-3-5-6z" class="I"></path><path d="M235 242l-1-1h0 2l12 6 2 2 1 1c2 2 4 5 6 8l2 3v1l1 3c0 4 0 6-2 10-1 2-4 4-7 6h0l-1 1c-5 2-9 3-14 1l-2-2c1 0 1 0 1-1 1 0 2-1 3-2-1-3-1-6-1-9-1 0-1 1-1 1h-1v-1c0-4-1-8-2-12 1-1 1-3 2-5l1-1v-1c1-1 1-3 1-5h1l-3-3z" class="W"></path><path d="M252 258c1 1 2 1 2 3v2h2v1c1 2 1 3 1 6v-2h-1v1c0 2-1 3-2 5h0c-1 1-2 2-4 3l1-1c1-1 2-3 3-4h0c0-1 1-1 1-1 1-4-1-9-3-13h0z" class="n"></path><path d="M257 263l2-1 1 3c0 4 0 6-2 10-1 2-4 4-7 6h0l-1-1c-1 1-4 2-5 2h0l1-1c1 0 2 0 3-1 2-1 6-3 7-5-1-2 1-4 1-5 0-3 0-4-1-6h1 1l-1-1z" class="c"></path><path d="M257 263l2-1 1 3c0 4 0 6-2 10l-1-1c1-2 2-3 2-6-1 0-1 1-1 2 0 2-1 3-2 5-1-2 1-4 1-5 0-3 0-4-1-6h1 1l-1-1z" class="q"></path><path d="M244 249l4 4 1 1v1c0 2 1 4 2 6h0c1 2 1 3 0 5v2h1v-1c1 1 1 3 0 4-1 3-3 4-5 5 2-2 3-3 4-6 0-1 0-1-1-2l-1 1c0-1 0-1-1-2 1-1 1-2 0-3h0v-4-2c-1-1-1-1-1-2 0-2-1-3-3-4l-1-2 1-1z" class="M"></path><path d="M244 249l4 4v2h0c1 1 1 2 1 3h0v3l2 1v1l-1 1-1-1c-1 0-1 1-1 1h0v-4-2c-1-1-1-1-1-2 0-2-1-3-3-4l-1-2 1-1z" class="F"></path><path d="M249 269l1-1c1 1 1 1 1 2-1 3-2 4-4 6 0 1-1 2-2 2s-2 1-2 1v1c2-1 4-2 6-2l1 1c-2 0-3 1-4 1v1l-1 1h0c1 0 4-1 5-2l1 1-1 1c-5 2-9 3-14 1l-2-2c1 0 1 0 1-1 1 0 2-1 3-2l3-1 4-2s1-1 2-1c0-1 0-2 1-3 0-1 1-1 1-2h0z" class="k"></path><path d="M243 279v1h-1v1l-4 1v-1c1 0 3-1 5-2z" class="m"></path><path d="M235 242l-1-1h0 2l12 6 2 2 1 1c2 2 4 5 6 8l2 3v1l-2 1 1 1h-1-1v-1h-2v-2c0-2-1-2-2-3s-2-3-3-4l-1-1-4-4-6-4-3-3z" class="s"></path><path d="M257 258l2 3v1l-2 1c0-3-1-3 0-5z" class="e"></path><path d="M236 250c1-1 1-3 1-5h1l6 4-1 1 1 2c2 1 3 2 3 4 0 1 0 1 1 2v2 4h0c1 1 1 2 0 3 1 1 1 1 1 2h0c0 1-1 1-1 2-1 1-1 2-1 3-1 0-2 1-2 1l-4 2-3 1c-1-3-1-6-1-9-1 0-1 1-1 1h-1v-1c0-4-1-8-2-12 1-1 1-3 2-5l1-1v-1z" class="U"></path><path d="M248 264h0c1 1 1 2 0 3 1 1 1 1 1 2h0c0 1-1 1-1 2-1 1-1 2-1 3 0-3 0-6 1-9v-1z" class="E"></path><path d="M244 252c2 1 3 2 3 4 0 1 0 1 1 2v2 4 1c-1-2-1-2-2-3s-1-2 0-3h0c0-1-1-2-1-2 0-2 0-4-1-5z" class="B"></path><defs><linearGradient id="i" x1="242.391" y1="270.455" x2="236.756" y2="268.132" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#i)" d="M240 254l1-1 1 1c0 3 2 8 1 11 0 1 1 3 1 4s-1 4-1 5c0 0 0 1 1 1h1l-4 2-3 1c-1-3-1-6-1-9v-1l1-2v-2c2-1 2-4 3-5h0v-2c0-1-1-2-1-3z"></path><path d="M241 264v4h1v-1s1-1 1-2c0 1 1 3 1 4s-1 4-1 5c0 0 0 1 1 1h1l-4 2v-3c1-3 0-7 0-10z" class="G"></path><path d="M240 254l1-1 1 1c0 3 2 8 1 11 0 1-1 2-1 2v1h-1v-4-5-2c0-1-1-2-1-3z" class="E"></path><path d="M236 250c1-1 1-3 1-5h1l6 4-1 1h-1 0v4l-1-1-1 1c0 1 1 2 1 3v2h0c-1 1-1 4-3 5v2l-1 2v1c-1 0-1 1-1 1h-1v-1c0-4-1-8-2-12 1-1 1-3 2-5l1-1v-1z" class="B"></path><path d="M238 255c-1-1-1-2-1-3h1c1 1 1 1 1 2l-1 1h0z" class="I"></path><path d="M239 254h1c0 1 1 2 1 3v2h0c-1 1-1 4-3 5 0-2-2-6-1-7h2c0-2-1-1-1-2h0l1-1z" class="C"></path><path d="M235 252l1-1v5c-1 3 1 10 0 12-1 0 0 0 0 1h-1c0-4-1-8-2-12 1-1 1-3 2-5z" class="V"></path><path d="M252 214l26-1v213c-2 0-3 0-5-1l-2 2-1-1v1h-1-1v1l-1 1v3c-2 0-1-2-1-3 0 0-1-1-2-1 0-2 1-3 2-4 0-1 1-3 1-4 1-2 2-4 3-5s1-1 1-2c1-1 1-2 1-4v-6h-1c0 2 0 5-1 8-1-3 0-6 0-9h0 0v-3l-4-18v-6c-1-1 0-2-1-3v1 2h-1v-5c1-1 1-2 1-3h-1c0 2-1 4-1 6s1 4 1 6-1 5-1 7l1 10 1 10c0 1-1 3 0 4-1 3-1 7-2 10s-3 6-5 10v-1c0-1 1-2 1-4-1 1-1 1-1 2h-1v-1-2 1l-1-2c0 1 0 2-1 3h-1c2-4 2-7 4-10 2-8 0-16 0-24-1 0-1 0-2-1 0-2 1-6 1-8v-4-6-4l-1 1v-1-1h-1v-1c0-2 0-5 1-8l1-1c1-2 2-5 3-7 0-2 2-4 2-7 1-2 2-4 2-7h0c0-2 0-4-1-5v-7c-1-3-2-5-2-7 0-1-1-1-1-1v-1s-1-1-1-2 0-1-1-2c0-1 0-2-1-2v-2c-1-1-1-2-2-3-1-2-1-3-3-4l2-3 3-6 2-5c1-3 1-6 1-9l1 1v-1h0c-1-2 0-5 0-7 1-3 1-6 2-9h-1c0 1-1 1-1 2l-1 1-1-3v-1l-2-3c-2-3-4-6-6-8l-1-1-2-2-12-6h0c-1-2-1-4 0-6h0c0-1 0-1 1-2 0-1 0-2 1-2 1-2 2-5 4-6-1 1-1 1-1 2h1 1c0-2 1-3 2-5 1-1 1-2 2-3h1c0-1 1-2 1-3 1 0 2-2 3-2z" class="Y"></path><path d="M258 223v1c0 1-1 5-1 7v1h-1v-1 1c1-1 1-1 0-2h0c0-3 1-5 2-7z" class="P"></path><path d="M271 315l1-1c0-2-1-4-2-6 0 0-1-1-1-2h0 2c-1 2 1 5 1 6l1 1v1 1 2 1l-2-3z" class="U"></path><path d="M269 348c0-2 1-3 2-5 0-1 0-3 1-4 0 3 0 7-1 10l-2-1z" class="N"></path><path d="M272 374c0-5 1-12 3-17 0 4-1 7-1 11-1 2-1 5-1 7l-1-1z" class="K"></path><path d="M248 239c0-5 3-15 6-18 0 1-2 6-3 7 0 2-1 4-1 6v5h-2z" class="V"></path><path d="M259 240c0-2 0-3 1-4v-2h1 0v-1h0 0c0-1 1-1 1-1 0 1 0 4-1 6h1v-1h0l1 3v-1-1c1 2 1 10 0 12 0-2 1-7 0-9l-1 1h0c-2 0 0-6-1-7-1 0-1 1-1 2v3h-1z" class="b"></path><path d="M272 374l1 1c-2 5-1 11 1 16 0 2 1 4 1 6v3h-1v-5c-2-3-2-7-3-9 0-3-1-6 0-8l1-4z" class="c"></path><path d="M275 397c2 8-1 16-5 23 0-2 1-3 2-5 0-1-1-1-1-2 1-1 1-2 1-4v-3l1 1v2 1c1-3 1-7 1-10h0 1v-3z" class="J"></path><path d="M264 428c0-2 1-3 2-4 0-1 1-3 1-4 1-2 2-4 3-5s1-1 1-2c0 1 1 1 1 2-1 2-2 3-2 5h0c-1 3-2 4-3 6l-1 1c-1 1 0 1 0 2 1 1 0 1 0 2 1-1 1-2 2-3l-1 1v3c-2 0-1-2-1-3 0 0-1-1-2-1z" class="g"></path><path d="M269 241l1 1 6 18c-1-2-2-3-2-4-1-2-1-3-2-5h-1c0 2 1 4 2 6s2 5 3 7h1v2h0l-5-9-3-8v-8z" class="K"></path><path d="M262 242l1-1c1 2 0 7 0 9s0 4 1 5c0 3 2 6 0 9-1 1 0 6 1 7l7 21c-3-5-5-11-7-16-1-3-3-7-2-11 0-1 1-2 1-3s0-3-1-4c-2-5-1-11-1-16z" class="L"></path><path d="M259 287c1-3 1-6 1-9l1 1 1 2c-1 2 0 3 1 5 0 2 2 6 3 7s1 2 1 2c1 1 2 3 2 4 1 0 1 1 1 1 1 3 4 6 5 10v2h0c-1-1-1-1-1-2-1-2-2-4-3-5l-5-10c-1-2-2-4-3-5 0-1 0-2-1-3 0-1 0-1-1-2v1 1s1 1 1 2c0 0 0 1 1 1v2c1 0 1 1 1 1 0 1 1 1 1 1 1 2 2 4 2 6 0-1 0-1-1-1v-1c-1-1-1-2-2-2-1-2-3-6-4-9h-1z" class="H"></path><path d="M271 386c1 2 1 6 3 9v5h0c0 3 0 7-1 10v-1-2l-1-1v3-6h-1c0 2 0 5-1 8-1-3 0-6 0-9h0 0v-3c1-1 1-2 1-3l-1-3v-4c-1 0-1-2-1-3h0 1l1 1v-1z" class="L"></path><path d="M271 386c1 2 1 6 3 9 0 1 0 2-1 3v-3l-1-1v-1 3c1 0 1 1 1 2h-1v-1-1c0-1-1-1 0-2 0-1-1 0-1-1v-2h0c0-1-1-1-1-2-1 0-1-2-1-3h0 1l1 1v-1z" class="C"></path><path d="M259 287h1c1 3 3 7 4 9 1 0 1 1 2 2v1c1 0 1 0 1 1 2 1 3 4 4 6h-2 0c0 1 1 2 1 2 1 2 2 4 2 6l-1 1-6-12c-2-3-4-7-5-10h-1 0l-1-1h-1 0l2-5z" class="D"></path><path d="M259 293h1c1 3 3 7 5 10l6 12 2 3h0c1 6 3 11 4 16 0 4 0 8 1 11-1 1-1 2-2 2l-1-10-2-7c0-2-1-3-2-5 1-2 0-4 0-5v-2l-1-2c0-2-2-4-3-7l-1-2v-1c-1 0-1-1-1-1-2-4-5-8-6-12z" class="Q"></path><path d="M271 325c1-2 0-4 0-5v-2c2 7 4 12 4 19l-2-7c0-2-1-3-2-5z" class="B"></path><path d="M268 272c0-3-1-5-1-8h-1c-1 2 0 6 0 8v-1h0v-1c-1-1-1-5 0-7s-1-7-1-10c-1-6-1-13 1-20v5 8c1 5 2 11 3 15 1 3 1 7 2 10 1 1 1 5 1 7h0c-1 1 1 5 1 6-2-4-5-8-5-12z" class="J"></path><path d="M268 272h1v-1c-1-2-1-4-1-6l-1-1c0-2 0-5-1-7v-3-1l3 13c0 1 1 3 1 5h1c1 1 1 5 1 7h0c-1 1 1 5 1 6-2-4-5-8-5-12z" class="D"></path><path d="M269 348l2 1-1 10 1 1c1 3 0 8-1 11 0 3 0 5 1 7-1 2 0 5 0 8v1l-1-1h-1 0c0 1 0 3 1 3v4l1 3c0 1 0 2-1 3l-4-18v-6c-1-1 0-2-1-3v1 2h-1v-5c1-1 1-2 1-3 0-2 0-5 1-8s2-7 3-11z" class="H"></path><path d="M270 359l1 1c1 3 0 8-1 11 0 3 0 5 1 7-1 2 0 5 0 8v1l-1-1v-1l-1-4v-1-1c0-1 0-1-1-1v-3-3c1-2 0-5 1-6v-2-3c1 0 1-1 1-2z" class="P"></path><path d="M250 239v-5c0-2 1-4 1-6 2 2 1 3 1 5v3 13 1h1v-8c0-3 0-6 1-8h1c0 2 0 3 1 4v5h0v2h1v-1c1-2 0-5 1-7l1 1c-1 0 0 1 0 2h0 1v-3c0-1 0-2 1-2 1 1-1 7 1 7h0c0 5-1 11 1 16v2 2h-1c0 1-1 1-1 2l-1 1-1-3v-1l-2-3c-2-3-4-6-6-8l-1-1-2-2v-2-6h2z" class="G"></path><path d="M250 242h1c0 2-1 5 0 7v1l-1-1v-7z" class="B"></path><path d="M253 242h1c0 2 0 5 1 8v1l-1 1-1-2v-8zm-5-3h2v3 7l-2-2v-2-6z" class="F"></path><path d="M260 237h1v11l-1-3v2c0-1 0-2-1-2h0-1v2c0-1-1-2 0-3v-3h1l1-1v-3z" class="B"></path><path d="M256 243h0v2h1v-1c1-2 0-5 1-7l1 1c-1 0 0 1 0 2h0 1l-1 1h-1v3c-1 1 0 2 0 3v10c-1-3-1-6-1-9-1-2-1-3-1-5z" class="O"></path><path d="M259 261h0c1-3 0-6 0-9 1 0 1 2 1 3 1 2 2 4 2 7 0 1-1 1-1 2l-1 1-1-3v-1z" class="T"></path><path d="M260 237c0-1 0-2 1-2 1 1-1 7 1 7h0c0 5-1 11 1 16v2l-1-5c-1-2-1-5-1-7v-11h-1z" class="U"></path><path d="M257 292h0 1l1 1h0c1 4 4 8 6 12 0 0 0 1 1 1v1l1 2c1 3 3 5 3 7l1 2v2c0 1 1 3 0 5 1 2 2 3 2 5-1 2-1 4-1 6 0 1 1 2 0 3s-1 3-1 4c-1 2-2 3-2 5-1 4-2 8-3 11s-1 6-1 8h-1c0 2-1 4-1 6s1 4 1 6-1 5-1 7l1 10 1 10c0 1-1 3 0 4-1 3-1 7-2 10s-3 6-5 10v-1c0-1 1-2 1-4-1 1-1 1-1 2h-1v-1-2 1l-1-2c0 1 0 2-1 3h-1c2-4 2-7 4-10 2-8 0-16 0-24-1 0-1 0-2-1 0-2 1-6 1-8v-4-6-4l-1 1v-1-1h-1v-1c0-2 0-5 1-8l1-1c1-2 2-5 3-7 0-2 2-4 2-7 1-2 2-4 2-7h0c0-2 0-4-1-5v-7c-1-3-2-5-2-7 0-1-1-1-1-1v-1s-1-1-1-2 0-1-1-2c0-1 0-2-1-2v-2c-1-1-1-2-2-3-1-2-1-3-3-4l2-3 3-6z" class="b"></path><path d="M262 382v-4c1-2 0-4 0-6s1-5 1-7h1v2c0 2-1 4-1 6s1 4 1 6-1 5-1 7v-4h0-1z" class="L"></path><path d="M260 374c0-6 1-11 2-17 1-4 1-6 3-9v2c-1 3-1 6-2 9-2 4-1 9-2 14v-1l-1 2z" class="C"></path><path d="M257 364l1-1v-1c2-2 2-6 3-8l-2 17c0 3-1 5-2 8v-6-4l-1 1v-1-1c0-1 1-2 1-4z" class="F"></path><path d="M257 364c1 2 1 3 1 4v1c-1 1 0 2 0 3v-2h1v1c0 3-1 5-2 8v-6-4l-1 1v-1-1c0-1 1-2 1-4z" class="M"></path><path d="M271 325c-2-8-5-15-9-21-1-2-4-7-4-9h0l2 2 1 2c1 2 2 5 4 6 0 0 0 1 1 1v1l1 2c1 3 3 5 3 7l1 2v2c0 1 1 3 0 5z" class="E"></path><path d="M258 392c0-4 0-9 1-13 0 7 1 13 1 20v9c0 3-1 5-2 8h0c2-8 0-16 0-24z" class="h"></path><path d="M254 298c0 1 1 1 1 2 1 1 2 3 3 5l5 8v1-2h0c1 0 3 3 3 4a35.85 35.85 0 0 1 0 25v1h-1v-1l1-1c0-1 0-2-1-3h-1 0c0-2 0-4-1-5v-7c-1-3-2-5-2-7 0-1-1-1-1-1v-1s-1-1-1-2 0-1-1-2c0-1 0-2-1-2v-2c-1-1-1-2-2-3-1-2-1-3-3-4l2-3z" class="H"></path><path d="M266 341v-3c1-3 0-5 0-8v-5c-1-2-2-6-2-9 1 3 2 4 2 7h0v1c1-3 0-5-1-8h1a35.85 35.85 0 0 1 0 25z" class="c"></path><path d="M260 374l1-2v1c0 5 1 10 1 15h0 0v-6h1 0v4l1 10 1 10c0 1-1 3 0 4-1 3-1 7-2 10s-3 6-5 10v-1c0-1 1-2 1-4-1 1-1 1-1 2h-1v-1-2 1l-1-2c0 1 0 2-1 3h-1c2-4 2-7 4-10h0c1-3 2-5 2-8l1 1-1-35z" class="o"></path><path d="M261 419c-1 1-2 4-2 5 1-2 2-3 2-5 1-2 1-4 2-6v-1 6c-1 3-3 5-4 7-1 1-1 1-1 2h-1v-1-2 1l-1-2c1-2 1-5 3-6 0 0 0 1 1 2h1z" class="U"></path><path d="M258 416h0c1-3 2-5 2-8l1 1v10h-1c-1-1-1-2-1-2-2 1-2 4-3 6 0 1 0 2-1 3h-1c2-4 2-7 4-10z" class="F"></path><path d="M156 269c1 4 4 9 3 12 0 2 0 3-1 4-1 3-1 5-2 8-2 3-4 6-5 9h0 1c1 1 0 2 0 3v1c2-1 3-3 4-4 1 0 2-1 2-1l4-8 2-6 3 5 1 1 3 4 6 7 7 5 5 4h-1 0c-2 3-3 6-6 8 1 0 1 0 2 1l-4 1c-2 0-4 1-6 2h-5l-6 2c-2 1-5 3-7 5-1 1-3 2-4 3l-7 8-1 2c1 0 2 0 2-1l1 1h0c-3 3-5 5-7 8l-1 1c-2 1-3 3-5 5-1 1-2 3-4 4l-1 1h1c-1 2-2 3-3 4-1 2-2 3-3 5l1 1c-1 1-1 1-1 2v1l1 1c0 1 0 2-1 3h1 1l1-1c-1 1-1 1-1 2-3 5-5 12-8 17v-1c-1 1-1 3-2 4v-6l-1 1-1 1-1-1c0 2 0 6-1 8 0 1 0 2 1 3h0c-1 6-1 11 0 16 0 2 1 4 1 6s0 5 1 7c2 3 2 6 3 9 1 1 2 4 3 5l2 5v3l2 5 1 2c1 2 3 3 3 4l-1 1 4 5h0l-2-1h-1c1 1 2 2 2 3l1 1h0l3 3 2 2c0 1 5 6 5 6 1 2 3 4 4 6h-2c2 2 5 4 7 6 2 1 5 2 7 3 9 4 18 8 27 10h-4c-6-1-11-3-16-5h0c-6-4-13-7-20-9v-1h0c-2-1-4-1-6-1-3-1-5-4-7-6v1a30.44 30.44 0 0 0 8 8v2l-9-6c-2-1-3-3-5-4l-8-9h0c1 3 4 5 3 7 2 2 6 5 6 7l-2-2v1c0 2 4 3 5 5h-1l-4-4h-1c-6-4-10-10-14-15-7-10-13-21-16-32l-4-11c0-2-1-5-1-7h0v-2h1v1l-1-8v-3c1-7 0-14 1-21v-4l-1-1h0v-4h0v-3l1-8v-6l1-2v-1h-2v1l-1 1v-3c0 1-1 3-2 3 0-1 1-2 1-2v-1c-1-1-1-1-1-2 1-2 1-6 2-7 0-1 0-3 1-4 0 0 0-1-1-1 1-2 2-3 3-5l5-8-1-1c1-2 3-3 4-5 1-1 1-2 1-2 1-1 2-2 3-2l1 1 1-1s0 1 1 1l1-2v1c1 0 5-7 5-8 1 0 1-1 2-2s1-2 2-3v-1l-4 2 7-9c1-2 2-4 4-6 1 0 1 0 1-1l3-3-1-1c1 0 1-1 1-1 0-1 2-2 3-3 1 0 2-2 3-2s2 0 2-1c2-2 4-3 6-4l1-1v1c0-1 0-1 1-1l-1-1c1 0 2-1 2-2h0v-1l4-5 3-2h1l2-3 1-1c1-2 1-2 0-4l1-2z" class="Y"></path><path d="M138 304c0-1 0-2 1-3v-1h0 1v-3h0c1-1 3-2 4-4-2 4-4 8-6 11z" class="T"></path><path d="M151 279h1c0 1-1 1-1 2l-4 6c0-1 0-2 1-3-1 1-2 2-4 2l4-5 3-2z" class="P"></path><path d="M105 410h0c0-2 0-8 1-9l1 1v-1 7s0 1-1 2v-2l-1 2z" class="E"></path><path d="M104 431h1l1 2c1 2 1 5 2 8 0 1 1 4 1 5h0c-3-5-3-10-5-15z" class="C"></path><path d="M127 332c1-1 1-2 2-2h2c-4 4-7 9-10 13-1 2-2 4-4 5 1-3 5-7 7-10 1-2 2-3 3-5l-2 2h0c0-2 1-2 2-3z" class="O"></path><path d="M105 410l1-2v2c1-1 1-2 1-2v12l-1-2v6h0l-1-1v-13z" class="F"></path><path d="M99 346c1 0 1-1 2-2l1 1-5 12-2-2c1-3 3-6 4-9z" class="j"></path><path d="M142 324c0-1 3-3 4-4h2l-4 6c-1 1-3 4-4 5l-3 4 1-1v-1c1-1 1-2 2-3h0c1-1 1-2 2-3h0l-2 2v-1c0-1 1-1 2-2-1 0-1-1-1-1l1-1z" class="S"></path><path d="M120 463c1 0 1 1 2 2s1 2 2 3 1 2 3 3l1-1v1l4 5h0l-2-1h-1c1 1 2 2 2 3-3-1-5-6-7-9-1 0-1-1-2-2 0-1-1-1-1-2-1-1-1-2-1-2z" class="P"></path><path d="M104 431c-1-6-2-11-2-16v-9h1c1 2 1 4 1 6v14c1 2 1 3 1 5h-1z" class="L"></path><path d="M93 368h1c1 5-1 12-2 18l-1 2v-6l-1-1h0v-6l1-2 2-1v-4z" class="Z"></path><path d="M91 373l2-1c-1 2-1 5-2 7h0c-1 1-1 1-1 2h0v-6l1-2z" class="J"></path><path d="M132 324c1-1 2-2 4-3h0c-1 2-3 3-4 5h2v1l-3 3h-2c-1 0-1 1-2 2l1-2-1-1h0c0 2-2 3-3 5 0 2-1 3-3 4l1-1c1-2 1-3 2-5 1-1 2-1 2-2 1-2 2-3 3-4l2-4c1 1 1 2 1 2z" class="N"></path><path d="M131 322c1 1 1 2 1 2-1 2-2 4-3 5h-1v-1c1-1 1-1 1-2l2-4z" class="G"></path><path d="M105 423l1 1h0v-6l1 2 2 15v2h0l-1 4c-1-3-1-6-2-8l-1-2c0-2 0-3-1-5 1-1 1-2 1-3z" class="B"></path><path d="M126 339c2-4 6-6 8-10 0-1 1-1 1-1h1c-1 1-1 2-2 3l-1 1h2c-1 2-2 3-3 5-2 2-4 4-5 7-1 1-3 5-4 5v1l-1-1c1-3 5-6 5-9h0l-1-1z" class="D"></path><path d="M132 479h0l3 3 2 2c0 1 5 6 5 6 1 2 3 4 4 6h-2c-1 0-2-2-2-2-2-1-5-4-6-6 0 0-1-1-2-1 0-1-1-2-2-3l1-1c1 0 1 0 2 1 0-2-2-4-3-5z" class="U"></path><path d="M109 390c0 3-1 6 0 9v-2h0v-2-1c0-1 1-1 1-2v1l-1 5c-1 7-1 14-1 21 0 4 1 8 1 12 1 2 1 3 0 4l-2-15v-12-7c1-4 1-8 2-11z" class="q"></path><path d="M105 334s0 1 1 1l1-2v1l-4 7-1 4h0l-1-1c-1 1-1 2-2 2l1-3h-1v-1c-1 1-2 1-2 2l-1-1v1l-1-1c1-2 3-3 4-5 1-1 1-2 1-2 1-1 2-2 3-2l1 1 1-1z" class="d"></path><path d="M99 342c1-1 2-3 3-4h1c-1 1-1 2-1 2 1 0 1 1 1 1l-1 4h0l-1-1c-1 1-1 2-2 2l1-3h-1v-1z" class="l"></path><path d="M91 409c0-2 0-4 1-5 1 13 3 26 7 38h-1c-1-2-2-3-2-4-1 0-1 0-1 1l-3-10 1-2c0-1-1-3-1-5 0-4-1-9-1-13z" class="X"></path><defs><linearGradient id="j" x1="92.126" y1="429.053" x2="95.507" y2="436.775" xlink:href="#B"><stop offset="0" stop-color="#343233"></stop><stop offset="1" stop-color="#504e4e"></stop></linearGradient></defs><path fill="url(#j)" d="M93 427l3 11c-1 0-1 0-1 1l-3-10 1-2z"></path><path d="M120 324c0 1-1 2-2 3-3 4-5 8-7 12h0l2-3 1-1c-1 3-3 5-4 8l-2 2v1c-1 2-2 5-3 8-1 1-3 1-3 3 0 1-1 2-1 3l-1 1v1 1c-1 3-1 7-2 10h-1v1c0-3 1-6 1-8v-1-2h0c1 0 1-1 1-1l6-16v1h1v-1c1-1 2-3 3-5l3-6c1-2 2-3 3-5 1-1 3-3 4-5 0 0 0-1 1-1z" class="T"></path><path d="M109 390c0-3 1-6 2-8 2-6 5-13 7-20 2-3 2-6 4-9-2 10-5 20-8 30-1 1-1 3-2 5l-2 5v-1c0 1-1 1-1 2v1 2h0v2c-1-3 0-6 0-9z" class="s"></path><path d="M90 381l1 1v6l1-2v12c0 1 1 4 0 6-1 1-1 3-1 5 0 4 1 9 1 13 0 2 1 4 1 5l-1 2c0 1 0 2-1 3v-3c-1 1 0 3 0 4h-1l-1-8v-3c1-7 0-14 1-21v-4l-1-1h0v-4h0v-3l1-8h0z" class="D"></path><path d="M90 381l1 1v6 6 2 7c-1-3-1-6-1-8v-1-2-5c0 2 1 3 0 5h-1 0v-3l1-8h0z" class="H"></path><path d="M91 388l1-2v12c0 1 1 4 0 6-1 1-1 3-1 5v-6-7-2-6z" class="F"></path><path d="M130 314l2 1c-1 2-1 3-2 4l-7 10c0 1-2 4-3 4 0 1-1 1-1 2-3 6-7 11-9 17v3l-1 1v1c0 1-1 2-1 3-1 2-2 5-3 8l-2 9-2 5v4s-1 0-1 1v4h0c0 1 0 1-1 2v-1-4c1 0 0-2 0-2 1-3 1-5 2-7l3-11v-2c1-5 3-9 5-13s3-7 4-10c1-2 2-3 3-5 4-8 9-16 14-24z" class="Z"></path><path d="M139 306c1-1 1-1 1-2 1 1 1 2 1 2l-1 1c-1 1-1 2-2 3-2 1-4 4-5 6h0s1 0 1 1h0 1 0c-1 1-2 2-2 4l-2 1-2 4c-1 1-2 2-3 4 0 1-1 1-2 2-1 2-1 3-2 5l-1 1c-1 1-2 1-2 2-3 6-5 13-10 17v-1l1-1v-3c2-6 6-11 9-17 0-1 1-1 1-2 1 0 3-3 3-4l7-10c1-1 1-2 2-4l-2-1 4-5 1-1 3-4c0 1 0 1 1 2z" class="Q"></path><path d="M139 306c1-1 1-1 1-2 1 1 1 2 1 2l-1 1c-1 1-1 2-2 3-2 1-4 4-5 6h0s1 0 1 1h0c-1 1-2 3-3 3-1 1-2 3-3 4-3 4-5 7-7 11-1 1-2 4-2 4h-1 0c1-2 2-4 2-6 1 0 3-3 3-4l7-10c1-1 1-2 2-4l-2-1 4-5 1-1 3-4c0 1 0 1 1 2z" class="f"></path><path d="M135 308l3-4c0 1 0 1 1 2 0 1 0 1-1 2h-2v1l-1-1z" class="F"></path><path d="M134 309l1-1 1 1c0 1 0 2-1 2l-3 4-2-1 4-5z" class="Q"></path><path d="M134 309l1-1 1 1c0 1 0 2-1 2l-1-2z" class="L"></path><path d="M97 344c0-1 1-1 2-2v1h1l-1 3c-1 3-3 6-4 9l2 2-1 4-1 4-1 3h-1v4l-2 1v-1h-2v1l-1 1v-3c0 1-1 3-2 3 0-1 1-2 1-2v-1c-1-1-1-1-1-2 1-2 1-6 2-7 0-1 0-3 1-4 0 0 0-1-1-1 1-2 2-3 3-5l5-8v-1l1 1z" class="h"></path><path d="M93 363l2 2-1 3h-1s0-1-1-1v-1l1-3z" class="d"></path><path d="M94 359c1 1 1 1 1 2h1l-1 4-2-2 1-4z" class="o"></path><path d="M95 355l2 2-1 4h-1c0-1 0-1-1-2l1-4z" class="p"></path><path d="M89 373v-1c0-2 1-4 3-6v1l-1 5h-2v1z" class="j"></path><path d="M92 367c1 0 1 1 1 1v4l-2 1v-1l1-5z" class="i"></path><path d="M91 355l5-5c-1 3-2 6-4 9 0 1-1 1-1 1v-2c0-1 1-2 0-3z" class="m"></path><path d="M91 358v2c0 1-1 2-1 3-1 1-1 3-1 4-1 2-1 3-2 5v-1c-1-1-1-1-1-2 1-2 1-6 2-7h0c1-2 1-3 3-4z" class="j"></path><path d="M97 344c0-1 1-1 2-2v1c-1 2-2 5-3 7l-5 5 1-1 1-1v-2l4-7z" class="W"></path><path d="M96 344v-1l1 1-4 7v2l-1 1-1 1c1 1 0 2 0 3-2 1-2 2-3 4h0c0-1 0-3 1-4 0 0 0-1-1-1 1-2 2-3 3-5l5-8zm0 94c0 1 1 2 2 4h1c5 13 11 25 20 36 1 2 2 4 4 5 3 3 7 6 9 10v1a30.44 30.44 0 0 0 8 8v2l-9-6c-2-1-3-3-5-4l-8-9-1-2v-2c-7-8-12-18-17-28l-3-8-1-1-1-5c0-1 0-1 1-1z" class="c"></path><path d="M117 481l2 2c1 1 2 3 4 4l5 6c2 2 3 2 3 5-2-1-3-3-5-4l-8-9-1-2v-2z" class="L"></path><defs><linearGradient id="k" x1="94.28" y1="439.215" x2="100.161" y2="448.023" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#7b7b7c"></stop></linearGradient></defs><path fill="url(#k)" d="M96 438c0 1 1 2 2 4h1c5 13 11 25 20 36l-1 1c0-1-1-2-1-3-2-2-4-4-5-6l-6-9v-1l-1-1v1c0 1 1 3 2 4 2 4 4 7 6 10 1 2 6 7 6 9l-2-2c-7-8-12-18-17-28l-3-8-1-1-1-5c0-1 0-1 1-1z"></path><path d="M152 302c1 1 0 2 0 3v1h0c1 0 1 0 1 1s0 1 1 2l-2 3c-1 1-1 2-2 3 0 1 0 2-1 2 0 1-1 2-1 3h-2c-1 1-4 3-4 4l-3 3c-1 0-2 1-3 2 0 2 0 2-1 3h-2l1-1c1-1 1-2 2-3h-1s-1 0-1 1c-2 4-6 6-8 10-5 5-8 11-12 17v-1c0-2 2-5 3-7 2-1 3-3 4-5 3-4 6-9 10-13l3-3v-1h-2c1-2 3-3 4-5h0c2-3 4-5 5-8s4-6 7-8c0 1-1 1-1 3 1-2 4-4 4-6h1z" class="H"></path><path d="M150 311v2c1 0 2-1 2-1-1 1-1 2-2 3 0 1 0 2-1 2h0l-2 2h0v-1c0-2 2-3 3-5v-2z" class="I"></path><path d="M146 311l1 1c-2 3-4 5-5 7-1 0-1-1-1-1v-1l3-3c0-1 1-2 2-3z" class="D"></path><path d="M136 321l3-3h1 0 0v1l-4 4 1 1c-1 1-2 1-3 2h-2c1-2 3-3 4-5zm16-19c1 1 0 2 0 3v1h0c1 0 1 0 1 1s0 1 1 2l-2 3s-1 1-2 1v-2c1 0 1-1 2-2h0l-1-1c-1 1-3 4-3 5l-1 1c-1 3-4 5-5 7h-1c0-1 0-1 1-2 1-2 3-4 5-7l-1-1h0c1-1 1-2 1-3 1-2 4-4 4-6h1z" class="C"></path><path d="M152 302c1 1 0 2 0 3v1h0c-2 2-3 4-5 6l-1-1h0c1-1 1-2 1-3 1-2 4-4 4-6h1z" class="T"></path><path d="M135 295c2-2 4-3 6-4l1-1v1c-1 1-1 1-1 2-1 2-3 5-4 7-4 6-8 11-11 16-2 2-3 3-4 5 0 1-1 2-2 3-1 0-1 1-1 1-1 2-3 4-4 5-1 2-2 3-3 5l-3 6c-1 2-2 4-3 5v1h-1v-1-1c2-3 4-7 6-10l2-4v-1c0-1 1-1 1-2 1-1 1-2 2-3l4-8-3 3-1 1v-1l-4 2 7-9c1-2 2-4 4-6 1 0 1 0 1-1l3-3-1-1c1 0 1-1 1-1 0-1 2-2 3-3 1 0 2-2 3-2s2 0 2-1z" class="C"></path><path d="M124 312l2-2v-1c2-2 4-3 5-6h1c1-1 2-2 2-4l3-4v1c-1 2-2 3-3 5s-3 4-4 6c0 1-1 1-1 2-1 1-3 4-3 5h-1l-4 6v-2h0c1-1 1-3 2-4 0-1 1-1 1-2z" class="D"></path><path d="M126 302c1 0 1-1 1-1 0-1 2-2 3-3 1 0 2-2 3-2s2 0 2-1l-2 3c-1 3-4 6-6 9-1 1-3 3-3 5 0 1-1 1-1 2-1 1-1 3-2 4h0v2c-1 2-3 4-4 6s-2 4-4 5v-1c0-1 1-1 1-2 1-1 1-2 2-3l4-8-3 3-1 1v-1l-4 2 7-9c1-2 2-4 4-6 1 0 1 0 1-1l3-3-1-1z" class="B"></path><defs><linearGradient id="l" x1="116.293" y1="467.382" x2="123.456" y2="445.19" xlink:href="#B"><stop offset="0" stop-color="#1a191e"></stop><stop offset="1" stop-color="#4e4d4a"></stop></linearGradient></defs><path fill="url(#l)" d="M113 388c-1 2-2 4-2 6-1 5-1 10-2 15v8l1-1c0-4 1-8 2-11 0 1 0 2 1 3h0c-1 6-1 11 0 16 0 2 1 4 1 6s0 5 1 7c2 3 2 6 3 9 1 1 2 4 3 5l2 5v3l2 5 1 2c1 2 3 3 3 4l-1 1v-1l-1 1c-2-1-2-2-3-3s-1-2-2-3-1-2-2-2v-1c-1 0-2-2-2-2v-1c-1-1-1-3-2-4-1-3-3-5-3-7-1-2-2-5-3-7 0-1 0-3-1-4v-2c1-1 1-2 0-4 0-4-1-8-1-12 0-7 0-14 1-21l1-5 2-5h1z"></path><path d="M115 445v-3-3h0 1c0 2 0 4 1 6 0 0 1 0 1 1 1 1 2 4 3 5l2 5v3c-2-3-5-6-5-10-1-2-2-3-3-4z" class="R"></path><path d="M112 405c0 1 0 2 1 3h0c-1 6-1 11 0 16 0 2 1 4 1 6s0 5 1 7c2 3 2 6 3 9 0-1-1-1-1-1-1-2-1-4-1-6h-1 0v3 3c-1-2-1-4-1-6h-1v2h-1c0-3-1-5-1-8-1-5-2-11-2-16l1-1c0-4 1-8 2-11z" class="c"></path><path d="M110 416l2 17h-1 0c-1-5-2-11-2-16l1-1z" class="e"></path><path d="M156 269c1 4 4 9 3 12 0 2 0 3-1 4-1 3-1 5-2 8-2 3-4 6-5 9h0c0 2-3 4-4 6 0-2 1-2 1-3-3 2-6 5-7 8s-3 5-5 8c-2 1-3 2-4 3 0 0 0-1-1-2l2-1c0-2 1-3 2-4h0-1 0c0-1-1-1-1-1h0c1-2 3-5 5-6 1-1 1-2 2-3l1-1s0-1-1-2c0 1 0 1-1 2-1-1-1-1-1-2h0c2-3 4-7 6-11l3-6 4-6c0-1 1-1 1-2l2-3 1-1c1-2 1-2 0-4l1-2z" class="M"></path><path d="M140 307l2 2-1 1-1 1c0 1-1 1-1 2v-2l-1-1c1-1 1-2 2-3z" class="F"></path><path d="M143 305v-1l1 1c0 2-1 3-2 4l-2-2 1-1 2-1z" class="L"></path><path d="M152 290h1v-1h1 0c-1 3-2 6-4 8l-1 2-2-1 1-1c0-1 1-2 2-3l2-4z" class="f"></path><path d="M133 321l3-3c1-2 3-3 5-5-1 3-3 5-5 8-2 1-3 2-4 3 0 0 0-1-1-2l2-1z" class="I"></path><path d="M151 288l2-5h1c0 2-2 5-2 7l-2 4c-1 1-2 2-2 3l-1 1-4 7-2 1s0-1-1-2c2-2 3-5 4-7 1-1 2-3 3-5h2c0-1 0-2 1-2h0c1-1 1-1 1-2z" class="d"></path><path d="M148 297c0-1-1-1-1-1 0-1 1-2 1-2h2c-1 1-2 2-2 3z" class="c"></path><path d="M151 281h0 0v2h1c0 2-2 3-1 5 0 1 0 1-1 2h0c-1 0-1 1-1 2h-2c-1 2-2 4-3 5-1 2-2 5-4 7 0 1 0 1-1 2-1-1-1-1-1-2h0c2-3 4-7 6-11l3-6 4-6z" class="J"></path><path d="M151 283h1c0 2-2 3-1 5 0 1 0 1-1 2h0c-1 0-1 1-1 2h-2v-2c2-2 3-5 4-7z" class="c"></path><path d="M156 269c1 4 4 9 3 12 0 2 0 3-1 4-1 3-1 5-2 8-2 3-4 6-5 9h0c0 2-3 4-4 6 0-2 1-2 1-3s0-2 1-2c1-2 1-3 2-4 2-3 4-6 5-9 0-1 0-2 1-3h0v-2l-1 1c0 1-1 1-1 2-1-1 0-3 1-4v-6c-1 0-1-1-1-1 0-1 0 0-1-1l1-1c1-2 1-2 0-4l1-2z" class="R"></path><path d="M91 433c0-1-1-3 0-4v3c1-1 1-2 1-3l3 10 1 5 1 1 3 8c5 10 10 20 17 28v2l1 2h0c1 3 4 5 3 7 2 2 6 5 6 7l-2-2v1c0 2 4 3 5 5h-1l-4-4h-1c-6-4-10-10-14-15-7-10-13-21-16-32l-4-11c0-2-1-5-1-7h0v-2h1v1h1z" class="D"></path><path d="M91 433c0-1-1-3 0-4v3c1-1 1-2 1-3l3 10 1 5v1-1l-1-1v-2s-1-1-1-2h0v-2h0l-1 1v-1h-1l-1-4z" class="b"></path><path d="M89 434v-2h1v1h1l1 4c4 17 11 33 21 46 3 4 5 7 8 10l4 4v1c0 2 4 3 5 5h-1l-4-4h-1c-6-4-10-10-14-15-7-10-13-21-16-32l-4-11c0-2-1-5-1-7h0z" class="e"></path><path d="M140 331l1 1c0-1 1-1 1-2 1 0 1-1 1-1 1-1 1-1 3-1v1h3 1l1 1 1-1h1c-1 2-3 3-3 4v1c0 1-1 2-2 3v1c2-2 2-2 4-3l-7 8-1 2c1 0 2 0 2-1l1 1h0c-3 3-5 5-7 8l-1 1c-2 1-3 3-5 5-1 1-2 3-4 4l-1 1h1c-1 2-2 3-3 4-1 2-2 3-3 5l1 1c-1 1-1 1-1 2v1l1 1c0 1 0 2-1 3h1 1l1-1c-1 1-1 1-1 2-3 5-5 12-8 17v-1c-1 1-1 3-2 4v-6l-1 1-1 1-1-1c0 2 0 6-1 8-1 3-2 7-2 11l-1 1v-8c1-5 1-10 2-15 0-2 1-4 2-6 1-5 3-11 6-16 5-12 9-23 16-33 0-2 1-3 2-4l3-4z" class="r"></path><path d="M119 381c1 2-3 7-2 9v3c0 1-1 2-1 3l-1 1-1 1-1-1c0-2 1-4 2-6 1-3 3-7 4-10z" class="G"></path><path d="M125 367l3-6c0 1 0 2 1 3h0 1c-1 2-2 3-3 4-1 2-2 3-3 5l1 1c-1 1-1 1-1 2v1l1 1c0 1 0 2-1 3h1 1l1-1c-1 1-1 1-1 2-3 5-5 12-8 17v-1c-1 1-1 3-2 4v-6c0-1 1-2 1-3v-3c-1-2 3-7 2-9 0-1 5-13 5-14h1z" class="W"></path><path d="M125 367l3-6c0 1 0 2 1 3h0 1c-1 2-2 3-3 4-1 2-2 3-3 5 0 0-1 1-1 2h-1c0-2 1-3 2-5l1-3z" class="q"></path><path d="M117 390l4-9c1-1 1-3 2-3l-1 2-2 7c-1 2-1 4-2 6 2-4 4-7 6-11-2 6-5 11-6 16-1 1-1 3-2 4v-6c0-1 1-2 1-3v-3z" class="D"></path><path d="M140 331l1 1c0-1 1-1 1-2 1 0 1-1 1-1 1-1 1-1 3-1v1h3 1l1 1 1-1h1c-1 2-3 3-3 4v1c0 1-1 2-2 3v1c2-2 2-2 4-3l-7 8-1 2c1 0 2 0 2-1l1 1h0c-3 3-5 5-7 8l-1 1c-2 1-3 3-5 5-1 1-2 3-4 4l-1 1h0c-1-1-1-2-1-3l-3 6h-1l12-27-1-1c0-2 1-3 2-4l3-4z" class="r"></path><path d="M140 347l10-14v1c0 1-1 2-2 3v1c2-2 2-2 4-3l-7 8-4 4h-1z" class="H"></path><path d="M140 347h1l4-4-1 2c1 0 2 0 2-1l1 1h0c-3 3-5 5-7 8l-1 1c-2 1-3 3-5 5-1 1-2 3-4 4 2-2 3-4 4-6 2-4 4-7 6-10z" class="l"></path><path d="M140 331l1 1c0-1 1-1 1-2 1 0 1-1 1-1 1-1 1-1 3-1v1h3 1l1 1c-5 4-10 9-13 14-2 2-3 5-4 7l-6 10-3 6h-1l12-27-1-1c0-2 1-3 2-4l3-4z" class="T"></path><path d="M140 331l1 1c0-1 1-1 1-2 1 0 1-1 1-1 1-1 1-1 3-1v1l-1 1c0 1-2 2-3 3-1 2-2 3-3 3l-1 1h0c-1 1-1 2-2 3l-1-1c0-2 1-3 2-4l3-4z" class="L"></path><path d="M145 330c0 1-2 2-3 3-1 2-2 3-3 3l-1 1h0c1-2 2-3 3-5 2 0 3-2 4-2z" class="D"></path><path d="M164 287l3 5 1 1 3 4 6 7 7 5 5 4h-1 0c-2 3-3 6-6 8 1 0 1 0 2 1l-4 1c-2 0-4 1-6 2h-5l-6 2c-2 1-5 3-7 5-1 1-3 2-4 3-2 1-2 1-4 3v-1c1-1 2-2 2-3v-1c0-1 2-2 3-4h-1l-1 1-1-1h-1-3v-1c-2 0-2 0-3 1 0 0 0 1-1 1 0 1-1 1-1 2l-1-1c1-1 3-4 4-5l4-6c0-1 1-2 1-3 1 0 1-1 1-2 1-1 1-2 2-3l2-3c-1-1-1-1-1-2s0-1-1-1h0c2-1 3-3 4-4 1 0 2-1 2-1l4-8 2-6z" class="B"></path><path d="M175 306c1 1 1 2 1 4-1 1-2 3-3 4h-1l-1 2-2-1c0-1 1-2 1-2 2-1 3-3 4-4v-1c1-1 1-1 1-2z" class="D"></path><path d="M167 302h1v-2c-1 0-1 0-1-1s0-4 1-6l3 4h-1c-1-1-1-1-1-2h-1v3h0v3l1 1v-2c1 1 0 3 0 5-1 2-4 8-6 9h-1l5-12z" class="V"></path><path d="M159 321h0l1-1c2-1 3-5 4-6h1v2 1 1 1l4-4 2 1c-1 1-2 2-2 3-2 3-3 5-5 7l-1 1c-2 1-5 3-7 5-1 1-3 2-4 3-2 1-2 1-4 3v-1c1-1 2-2 2-3v-1c0-1 2-2 3-4l1-1 5-7z" class="Y"></path><path d="M177 304l7 5 5 4h-1 0c-2 3-3 6-6 8 1 0 1 0 2 1l-4 1c-2 0-4 1-6 2h-5l-6 2 1-1c2-2 3-4 5-7 0-1 1-2 2-3l1-2h1c1-1 2-3 3-4 0-2 0-3-1-4l2-2z" class="C"></path><path d="M173 322h0l1 1v2h-5c1 0 1-1 2-2 1 0 1 0 2-1z" class="F"></path><path d="M176 313l1-1 1 1v1s-1 1 0 1l1 2c-2 2-4 4-6 5h0-2c0-2 5-5 6-8l-1-1z" class="B"></path><path d="M177 304l7 5v1c-2 3-3 5-5 7l-1-2c-1 0 0-1 0-1v-1l-1-1-1 1c-1 1-2 2-2 3-1 2-2 3-3 4 0-3 1-4 2-6 1-1 2-3 3-4 0-2 0-3-1-4l2-2z" class="b"></path><path d="M184 309l5 4h-1 0c-2 3-3 6-6 8 1 0 1 0 2 1l-4 1c-2 0-4 1-6 2v-2l-1-1c2-1 4-3 6-5s3-4 5-7v-1z" class="M"></path><path d="M184 309l5 4h-1 0-1-1l-1 1c-2 0-3 3-4 4l-2 1h-1c2-3 5-5 6-8v-1-1z" class="C"></path><path d="M180 320h0l-1 1 1 1 2-1c1 0 1 0 2 1l-4 1c-2 0-4 1-6 2v-2l1-1c2-1 3-1 5-2z" class="I"></path><path d="M181 318c1-1 2-4 4-4l1-1h1 1c-2 3-3 6-6 8l-2 1-1-1 1-1h0l1-1v-1z" class="R"></path><defs><linearGradient id="m" x1="157.575" y1="305.961" x2="160.043" y2="311.282" xlink:href="#B"><stop offset="0" stop-color="#686568"></stop><stop offset="1" stop-color="#77797a"></stop></linearGradient></defs><path fill="url(#m)" d="M164 287l3 5 1 1c-1 2-1 5-1 6s0 1 1 1v2h-1l-5 12-3 6v1l-5 7-1 1h-1l-1 1-1-1h-1-3v-1c-2 0-2 0-3 1 0 0 0 1-1 1 0 1-1 1-1 2l-1-1c1-1 3-4 4-5l4-6c0-1 1-2 1-3 1 0 1-1 1-2 1-1 1-2 2-3l2-3c-1-1-1-1-1-2s0-1-1-1h0c2-1 3-3 4-4 1 0 2-1 2-1l4-8 2-6z"></path><path d="M154 323c1 0 1 0 2-1l-3 4v-1h-1l2-2h0z" class="Q"></path><path d="M157 318h0c1 0 1-1 2-1h0l-3 5c-1 1-1 1-2 1l2-4 1-1z" class="M"></path><path d="M163 296c1-1 0-2 1-3 1 1 1 3 1 5 0 1 0 1-1 2h0c-1 2-2 8-4 9v-1-3s1-1 1-2l-2 2v-1c0-1 1-3 2-5l1-1v1c1-1 1-2 1-3z" class="J"></path><path d="M163 296c1-1 0-2 1-3 1 1 1 3 1 5 0 1 0 1-1 2v-2c-1 1-2 4-3 5h0c0-2 1-3 1-4 1-1 1-2 1-3z" class="R"></path><path d="M164 287l3 5 1 1c-1 2-1 5-1 6s0 1 1 1v2h-1c-2 0-1 3-3 4 0-1 2-4 2-6v-1c-1 1-1 1-2 1h0c1-1 1-1 1-2 0-2 0-4-1-5-1 1 0 2-1 3v-2l-1-1 2-6z" class="L"></path><path d="M157 310c0 1 1 1 1 2s-1 3-1 4l4-7c0 4-3 6-4 9l-1 1-2 4h0l-2 2h1v1l-1 3-1 1-1-1h-1-3v-1c-2 0-2 0-3 1 0 0 0 1-1 1 0 1-1 1-1 2l-1-1c1-1 3-4 4-5h1c1-1 1-2 2-3h1 1 0c4-4 5-8 8-12v-1z" class="Q"></path><path d="M146 328c2-2 4-5 7-5 0-1 0-1 1-2h0c1 0 1-1 2-2l-2 4h0l-2 2h1v1l-1 3-1 1-1-1h-1-3v-1z" class="C"></path><path d="M162 293l1 1v2c0 1 0 2-1 3v-1l-1 1c-1 2-2 4-2 5-1 1-1 1-1 2 0 2 0 3-1 4v1c-3 4-4 8-8 12h0-1-1c-1 1-1 2-2 3h-1l4-6c0-1 1-2 1-3 1 0 1-1 1-2 1-1 1-2 2-3l2-3c-1-1-1-1-1-2s0-1-1-1h0c2-1 3-3 4-4 1 0 2-1 2-1l4-8z" class="M"></path><path d="M152 306c2-1 3-3 4-4 1 0 2-1 2-1l-4 8c-1-1-1-1-1-2s0-1-1-1h0z" class="O"></path><path d="M147 323c3-4 5-9 8-14 1-3 3-6 5-9 0-1 0-1 1-1-1 2-2 4-2 5-1 1-1 1-1 2 0 2 0 3-1 4v1c-3 4-4 8-8 12h0-1-1z" class="C"></path><path d="M246 307l6-6c2 1 2 2 3 4 1 1 1 2 2 3v2c1 0 1 1 1 2 1 1 1 1 1 2s1 2 1 2v1s1 0 1 1c0 2 1 4 2 7v7c1 1 1 3 1 5h0c0 3-1 5-2 7 0 3-2 5-2 7-1 2-2 5-3 7l-1 1c-1 3-1 6-1 8v1h1v1 1l1-1v4 6 4c0 2-1 6-1 8 1 1 1 1 2 1 0 8 2 16 0 24-2 3-2 6-4 10h1c1-1 1-2 1-3l1 2v-1 2 1h1c0-1 0-1 1-2 0 2-1 3-1 4v1c2-4 4-7 5-10s1-7 2-10c-1-1 0-3 0-4l-1-10-1-10c0-2 1-5 1-7s-1-4-1-6 1-4 1-6h1c0 1 0 2-1 3v5h1v-2-1c1 1 0 2 1 3v6l4 18v3h0 0c0 3-1 6 0 9 1-3 1-6 1-8h1v6c0 2 0 3-1 4 0 1 0 1-1 2s-2 3-3 5c0 1-1 3-1 4-1 1-2 2-2 4 1 0 2 1 2 1 0 1-1 3 1 3v-3l1-1v-1h1 1v-1l1 1 2-2c2 1 3 1 5 1v19 9-1l-1 1-1 5c1 0 1 0 1 1 1 2 1 5 0 6 0 1-1 2-1 3s-1 2-1 3l1 1 1-2h0c1 1 0 2 0 3 0 4 0 7 1 10l-1 35c-2 26-5 51-25 71-7 7-15 14-23 18h-2v1-1h0l7-6c3-2 7-6 10-9l-1-1s1-1 1-2c1-1 1-2 1-4 0-3 1-6 1-9 1-5 0-10 0-15-1-8-4-15-9-21l-1 1-5-7s-1 0-1 1h-1l-8-7-6-4h-1c-2-1-4-2-5-2-7-3-14-5-21-7-2-1-2-1-3-1-9-2-18-6-27-10-2-1-5-2-7-3-2-2-5-4-7-6h2c-1-2-3-4-4-6 0 0-5-5-5-6l-2-2-3-3h0l-1-1c0-1-1-2-2-3h1l2 1h0l-4-5 1-1c0-1-2-2-3-4l-1-2-2-5v-3l-2-5c-1-1-2-4-3-5-1-3-1-6-3-9-1-2-1-5-1-7s-1-4-1-6c-1-5-1-10 0-16h0c-1-1-1-2-1-3 1-2 1-6 1-8l1 1 1-1 1-1v6c1-1 1-3 2-4v1c3-5 5-12 8-17 0-1 0-1 1-2l-1 1h-1-1c1-1 1-2 1-3l-1-1v-1c0-1 0-1 1-2l-1-1c1-2 2-3 3-5 1-1 2-2 3-4h-1l1-1c2-1 3-3 4-4 2-2 3-4 5-5l1-1c2-3 4-5 7-8h0l-1-1c0 1-1 1-2 1l1-2 7-8c1-1 3-2 4-3 2-2 5-4 7-5l6-2h5c2-1 4-2 6-2l4-1c-1-1-1-1-2-1 3-2 4-5 6-8h0 1c11 6 26 8 37 4 5-1 8-2 12-4 1 0 1 0 1 1s0 2-1 3v2l-2 2c-1 1-2 1-2 2v1 1l1 1c1-1 1-1 2-1v-2s1-1 2-1h0l1-1c4-2 5-8 6-12v-2z" class="Y"></path><path d="M142 469l3 3v2h0c-2-1-2-3-4-5h1z" class="g"></path><path d="M205 419c2 1 3 2 4 5h0-2c-1-2-2-3-2-5z" class="V"></path><path d="M150 413c1 0 1 1 1 1v2 2c-1-1-1-1-2-1h-1l2-4z" class="R"></path><path d="M168 339h4 4v1l-7 1-1-1v-1z" class="h"></path><path d="M123 456l3 5c-1 1-1 2-1 3l-2-5v-3z" class="C"></path><path d="M182 466l1 3c0 1 0 2 1 3l-2 1c-1-2-1-4-1-6l1-1z" class="j"></path><path d="M199 517l2-2 5 2h0-1l1 2h0l-7-2z" class="T"></path><path d="M158 421c1 0 2-1 3-1 1-1 2-1 3-2l-2 2-6 6v-1l1-2c0-1 1-1 1-2z" class="V"></path><path d="M151 493c3 0 6 1 8 3 1 1 1 2 1 3-1-1-2-1-3-2h0c-2-1-4-3-6-4z" class="U"></path><path d="M184 431l8 3h-9c-1-1-1-1-2-1h0c1 0 2-1 3-2z" class="e"></path><path d="M136 418l1-1v3c-1 4-2 7-2 12v-2-7l1-5z" class="C"></path><path d="M126 461c1 2 3 5 4 7l-1 1-3-3-1-2c0-1 0-2 1-3z" class="B"></path><path d="M181 433c1 0 1 0 2 1-3 1-4 2-7 3h0c-1 1-1 1-2 1l-1-1 3-2c1 0 2-1 3-2h2z" class="r"></path><path d="M178 352h7v1h0c0 1 2 1 3 2 1 0 1 0 2-1v1h1l1 1c-1 0-1 0-2 1l-12-5zm-37 57c1 2 0 3 1 4-2 3-4 5-5 7v-3l3-5c0-1 1-2 1-3z" class="B"></path><path d="M199 426h1c2 2 1 2 1 5l-1 1h-2c-1-2 0-4 1-6z" class="S"></path><path d="M180 455h1c0 4 0 8 1 11l-1 1c-2-4-2-8-1-12z" class="n"></path><path d="M188 352h1l4 3c0 2 1 4 2 5 0 1 1 1 1 2l1 1v1h0l-4-3v-1h1c-1-1-3-2-4-3 1-1 1-1 2-1l-1-1h-1v-1h0l-2-2z" class="K"></path><path d="M169 445h3c-2 4-3 8-3 13 0-1-1-2-1-4v-5l1-4z" class="j"></path><path d="M182 473l2-1c1 3 2 6 4 9l1 2c-1 0-1 1-2 1h0c-2-3-3-7-5-11z" class="h"></path><path d="M172 414h1l-1 1v1c-1 1-3 1-5 2-1 1-2 2-3 2h-2l2-2s2-2 3-2c2-1 4-2 5-2z" class="C"></path><path d="M165 410c4-3 8-5 13-6l1 1h-1 2l-2 1-2 1c-2 1-9 4-11 3z" class="n"></path><path d="M178 340h7 6l-1 2h3c-1 1-3 1-4 1l-3-1c-1-1-4-1-5-1-1-1-2-1-3-1z" class="C"></path><path d="M186 342c2-1 2-1 4 0h3c-1 1-3 1-4 1l-3-1z" class="E"></path><path d="M189 352h1l3 1 5 3c0 1 1 2 2 2-1 1 0 1-1 2-2-1-4-3-6-5h0l-4-3h0z" class="C"></path><path d="M208 355v-1l1 1h0c0 2 1 3 2 4h0c0 2 1 5 1 6-1 2-2 5-3 6 0 1-1 3-2 3h0c2-5 4-10 2-15h0c0-1-1-3-1-4z" class="S"></path><path d="M173 387c3 0 5 0 7 1h2-3c-3 0-8 1-12 2l-4 2v-1s1-1 1-2c3-1 6-1 9-2z" class="h"></path><path d="M180 455l2-2v5h0c1-1 2-1 2-2v12l-1 1-1-3c-1-3-1-7-1-11h-1z" class="Z"></path><path d="M166 400c5-3 11-2 17-2h0 2v1h-9c-1 1-3 1-5 1 0 0-2 0-2 1h-1c-1 0-1 0-2-1z" class="m"></path><path d="M172 433c1-1 1-1 2-1 2-1 3-1 5-1h5c-1 1-2 2-3 2h0-2c-1 1-2 2-3 2v-1c-2 0-4 2-5 3 0-1 1-2 2-3l-1-1z" class="l"></path><path d="M179 431h5c-1 1-2 2-3 2h0-2-2-1c1 0 1 0 1-1 1 0 2 0 2-1z" class="j"></path><path d="M147 428l-1-1-1 1h0v-1c1-3 1-7 3-10h1c1 0 1 0 2 1-1 2-2 4-3 7v2c0 1-1 1-1 1z" class="E"></path><path d="M157 411c0 2-2 4-3 5-2 3-4 7-5 11 0 1-1 2-1 4h0v-2c-2 2-2 6-3 8v-2-1c0-1 1-1 1-1 0-1 1-3 1-4v-1s1 0 1-1v-2c1-3 2-5 3-7v-2l6-5z" class="C"></path><path d="M163 386h3 0 1c1 0 2 0 3 1h3c-3 1-6 1-9 2-2 0-6 2-8 3 0-1 1-2 2-3l1-1 4-2z" class="F"></path><path d="M161 474c2 1 3 4 5 6 0 0 0 1 1 1 0 1 1 1 1 2 2 2 4 4 6 5h1l-5-4v-1-1c1 1 1 1 2 1 0 3 4 5 6 6 1 2 2 2 3 4-8-3-17-12-20-19zm27-122h-2c-1-1-3-2-4-2l-2-1h1c1 0 1 0 1-1h-4c4 0 9-1 12 0 2 1 3 1 5 2-1 1-3 1-4 1l-1 1h-1 0-1z" class="G"></path><path d="M190 348c2 1 3 1 5 2-1 1-3 1-4 1l-1 1h-1c-1-1-3-1-4-2 1-1 3-2 5-2z" class="I"></path><path d="M156 426l-3 3c0-1-1-4 0-6h0c0-2 2-4 3-6h1v-1l6-6h2c-3 2-5 4-7 6l-2 3 2 2c0 1-1 1-1 2l-1 2v1z" class="D"></path><path d="M156 419l2 2c0 1-1 1-1 2-1 0-2 1-3 2v-2-1l2-3z" class="F"></path><defs><linearGradient id="n" x1="194.815" y1="342.236" x2="196.533" y2="347.13" xlink:href="#B"><stop offset="0" stop-color="#3b3b3c"></stop><stop offset="1" stop-color="#545353"></stop></linearGradient></defs><path fill="url(#n)" d="M193 342c2 1 6 2 8 4 1 1 3 2 5 3l-1 1c-2 0-4-2-6-2h-1c-2-1-7-3-8-4v-1h-1c1 0 3 0 4-1z"></path><path d="M158 491c-5-4-10-10-10-16 1-1 0-5 0-7 2 1 3 5 4 7h-1 0c0 1 0 1-1 2v2c1 0 1 1 1 2 2 4 5 6 7 9v1z" class="c"></path><path d="M137 455l1 3 2 6c1 2 2 3 2 5h-1l-1-1h0c0 2 1 3 2 4h-1l-1-1c-1-3-3-7-4-10v-5-1h1z" class="P"></path><path d="M137 455l1 3 2 6c-2-1-3-6-4-8v-1h1z" class="F"></path><path d="M163 339c3-2 7-4 10-3-2 1-4 0-6 2l1 1h0v1l1 1c-6 1-11 3-17 6l-1-1c4-3 9-4 12-7z" class="p"></path><path d="M131 457l2 5 6 9 3 6c2 3 4 6 7 9h0l-12-13c-1-1-2-1-2-3l1-1-6-10h0l1 1v-3z" class="K"></path><path d="M151 481c4 4 8 8 12 11 1 1 1 2 2 2s1 1 2 1c3 2 9 4 10 7h0c-2 0-3-1-5-2-4-2-11-5-14-9v-1c-2-3-5-5-7-9z" class="B"></path><path d="M190 352l1-1c1 0 3 0 4-1 4 3 9 7 10 13 1 2 1 5 0 7 0-2-1-4-2-6 0-2-2-4-3-6-1 0-2-1-2-2l-5-3-3-1z" class="F"></path><path d="M193 353c1 0 2-1 2 0h1c2 1 5 3 6 5h0c1 1 1 2 1 3h0c-1-2-2-3-4-5h-1l-5-3z" class="E"></path><path d="M212 384s0 1-1 1h0s0 1-1 1c0 1 0 1-1 2s-1 2-1 4c0 1 0 1 1 1v-2c1 0 1 1 1 2 1 2 3 4 4 6l3 3-1 1-1-1c-1 0-1 0-1 1l-3-2c-3-2-6-6-6-8l1-1v1h1v-2c1-2 2-6 4-7h1 0z" class="g"></path><path d="M174 465v-7c1 3 1 6 2 9 2 6 5 10 9 15 1 2 1 4 3 5v-2c2 3 3 5 6 7l2 2c-3 0-4-2-6-3-2-2-3-4-5-7-2-2-4-3-5-5-3-4-4-9-6-14z" class="a"></path><defs><linearGradient id="o" x1="182.704" y1="426.195" x2="194.366" y2="415.653" xlink:href="#B"><stop offset="0" stop-color="#b9babc"></stop><stop offset="1" stop-color="#edebed"></stop></linearGradient></defs><path fill="url(#o)" d="M178 420c7-2 15-3 23 1 2 1 4 3 5 5v1-1c-4-3-8-5-13-5-1-1-1-1-3-1-4 0-7 1-11 3-1-1-1-1-2-1l1-2z"></path><path d="M171 437c1-1 3-3 5-3v1l-3 2 1 1c1 0 1 0 2-1h0 1 3 5c-6 2-10 3-13 8h-3l-1 4v-3h0l-1-1 1-3c0-2 2-3 3-5z" class="e"></path><path d="M171 437c1-1 3-3 5-3v1l-3 2c-1 1-2 3-2 4-1 1-1 3-2 4l-1 4v-3h0l-1-1 1-3c0-2 2-3 3-5z" class="I"></path><path d="M221 378h1l-1 3c-1 1-1 2-2 3l1 1h0v4l1 1c0 3-1 5-1 8 1 2 2 5 2 7-2-1-3-2-3-4-2-5-3-12-1-17l3-6z" class="H"></path><path d="M190 420c2 0 2 0 3 1-9 1-18 6-24 14v1h0l3-3 1 1c-1 1-2 2-2 3-1 2-3 3-3 5-1 0-1-1-1-2l-1 1c0 2-1 3-1 5l-1-1 2-5c-1 0-1-1-1-1 2-3 3-5 5-8 5-5 12-10 20-10h0v-1z" class="n"></path><path d="M161 437v1 1c-1 1-1 1-1 2v1 1c-1 0 0 1 0 1-1 2-1 4-1 5v6 1c0 1 0 2 1 4 0 0 1 0 1 1v2c1 3 2 6 4 9 1 2 3 3 5 6v2l1 1h-1c0-1-1-1-2-2v-1h-1c0-1-1-2-2-3l-4-8c-4-10-5-21 0-30z" class="e"></path><path d="M160 460s1 0 1 1v2c1 3 2 6 4 9 1 2 3 3 5 6v2l1 1h-1c0-1-1-1-2-2v-1h-1c0-2-1-3-2-5-2-4-4-9-5-13z" class="B"></path><path d="M172 359h4l3 1c1 0 1-1 2-1 4 2 8 5 12 8 2 1 2 3 4 4 0 0 1 1 1 2-1 0-1 1-1 1-4-5-9-8-15-10v-1h1l-1-1h-2c-3-2-5-2-8-2l-9-1h9z" class="S"></path><path d="M181 359c4 2 8 5 12 8v1c-4-4-9-6-14-8 1 0 1-1 2-1z" class="e"></path><path d="M152 475l3 3-2-4h0l2 2h0v1c1 2 2 3 3 4h0c2 1 4 3 4 4v1c-1 0-3-2-3-2h0c0 1 1 1 1 3v1l-2-2h-1l6 6h0c-4-3-8-7-12-11 0-1 0-2-1-2v-2c1-1 1-1 1-2h0 1z" class="d"></path><path d="M190 420v1h0c-8 0-15 5-20 10-2 3-3 5-5 8 0-1-1-2-1-4 1-1 2-4 3-5h1c1 0 1-1 2-2h-1c2-3 5-5 8-6 1 0 1 0 2 1 4-2 7-3 11-3z" class="J"></path><path d="M177 422c1 0 1 0 2 1-4 1-6 3-9 5h-1c2-3 5-5 8-6z" class="e"></path><path d="M135 430v2l3 26-1-3h-1c-2-5-3-10-4-16v-1c1-2 1-4 1-6 1 1 0 3 1 4v-2c1-1 1-3 1-4z" class="I"></path><path d="M133 432c1 1 0 3 1 4h0c0 1 0 1-1 1 0 1 0 3 1 4 0 5 0 9 3 13v1h-1c-2-5-3-10-4-16v-1c1-2 1-4 1-6z" class="Z"></path><path d="M209 405c0-1 1-1 0-2l-3-3-3-6 1-1c1 1 1 3 2 5 2 4 6 7 10 9 1 0 2 0 3 1 0 0 1 1 2 1l1-1v-3c1 1 1 2 2 2s1 0 1 1 1 1 1 1c1 1 1 1 2 1v1c-2 1-2 2-3 2h0l-1-1s-1-1-2-1c1 2 0 2 0 3v1l-4-4-9-6z" class="H"></path><path d="M222 405c1 1 1 2 2 2s1 0 1 1 1 1 1 1c-1 1-2 1-3 1h0l-2-1v1h-1c-1 0-1-1-1-2 0 0 1 1 2 1l1-1v-3z" class="g"></path><defs><linearGradient id="p" x1="151.01" y1="410.051" x2="162.851" y2="408.499" xlink:href="#B"><stop offset="0" stop-color="#6f6f70"></stop><stop offset="1" stop-color="#a09d9f"></stop></linearGradient></defs><path fill="url(#p)" d="M161 402c2-1 3-2 5-2 1 1 1 1 2 1h1c0-1 2-1 2-1-1 1-2 2-3 2h0c-4 2-8 5-10 8l-1 1-6 5v-2s0-1-1-1l11-11z"></path><path d="M161 402c2-1 3-2 5-2 1 1 1 1 2 1h1c0-1 2-1 2-1-1 1-2 2-3 2h0-1-2c-1 1-2 1-3 1l-1-1z" class="l"></path><path d="M198 348h1c2 0 4 2 6 2l1-1c3 2 6 4 8 7 3 3 4 8 5 12v8c-1 4-3 6-7 8h0c1-2 2-3 4-4 2-3 2-7 2-11-1-5-5-11-9-14l-1-1v1c-2-3-7-5-10-7z" class="m"></path><path d="M206 517c2 1 5 1 7 2 10 6 18 12 24 22l-1 1-5-7s-1 0-1 1h-1l-8-7v-1c-1-1-2-2-4-3-2-2-5-4-8-5h-1l-2-1h0l-1-2h1z" class="e"></path><path d="M206 517c2 1 5 1 7 2h-2c1 2 4 3 5 4 6 4 11 8 15 12 0 0-1 0-1 1h-1l-8-7v-1c-1-1-2-2-4-3-2-2-5-4-8-5h-1l-2-1h0l-1-2h1z" class="J"></path><path d="M176 407h1 3 0c-1 0-2 1-2 1h-1s-1 0-1 1c-2 0-4 2-6 3v1c2-1 2-1 4 0l-2 1c-1 0-3 1-5 2-1 0-3 2-3 2-1 1-2 1-3 2-1 0-2 1-3 1l-2-2 2-3c2-2 4-4 7-6h0c2 1 9-2 11-3z" class="d"></path><path d="M158 416v1c2-1 3-3 5-4l1 1c0 1-2 4-3 5 1-1 2-3 4-3h2c-1 0-3 2-3 2-1 1-2 1-3 2-1 0-2 1-3 1l-2-2 2-3z" class="M"></path><path d="M152 340c3-2 5-4 8-5v2h0c-2 1-3 2-4 3h0l2-1c2-1 3-2 5-3 0 1 0 2-1 2v1h1c-3 3-8 4-12 7l1 1c-1 1-4 3-6 3l-3 3c-2 1-3 3-4 4h0l-1-1 4-3v-2c-1 1-1 2-2 2 2-3 4-5 7-8h0l5-5z" class="n"></path><path d="M146 350h0c0-1 1-1 1-1h0c1-2 3-3 4-3l1 1c-1 1-4 3-6 3z" class="j"></path><path d="M147 345h1c-2 4-5 5-5 8-2 1-3 3-4 4h0l-1-1 4-3v-2c-1 1-1 2-2 2 2-3 4-5 7-8z" class="e"></path><path d="M152 340c3-2 5-4 8-5v2h0c-2 1-3 2-4 3s-2 1-3 2c0 1-2 2-3 3l-1-1 3-3v-1z" class="l"></path><path d="M204 345c6 3 12 8 15 14v2c1 0 1 0 1-1 2 3-1 10 3 12l-1 1h0c0 2-1 3 0 5h-1v-1c-1-1-1-1-2-1v-8c-1-4-2-9-5-12-2-3-5-5-8-7-2-1-4-2-5-3 1 0 2 0 3-1z" class="D"></path><path d="M236 529h0c1 1 1 2 2 2l2 4c2 3 5 7 6 11 1 3 1 7 3 10 1 2 0 4 1 6v3c0 5-1 9-1 14-1 2-2 5-2 8l2-5 1 1c-1 3-4 8-6 10l-1-1s1-1 1-2c1-1 1-2 1-4 0-3 1-6 1-9 1-5 0-10 0-15 1 1 1 3 1 4v4 4 5-2l1 1c0-1 1-3 1-4v-4c0-2 1-6 0-8h0c0 1-1 2 0 3 0 1 0 3-1 4h0c0-1-1-2 0-3v-1c0-4-1-9-2-13-2-7-5-14-10-20 0-1 0-1-1-2h0l1-1z" class="G"></path><path d="M178 404c3 0 6-1 9-1s6-1 9-1c5 0 9 2 13 3l9 6-1 1c-5-5-11-6-18-7h-3v1h0c-5 0-9 0-13 1h-2c-1 1-1 1-2 1-1 1-1 1-2 1v1c-1 1-2 1-3 2v1c-2-1-2-1-4 0v-1c2-1 4-3 6-3 0-1 1-1 1-1h1s1-1 2-1h0-3-1l2-1 2-1h-2 1l-1-1z" class="j"></path><path d="M178 406c3 0 4 0 6-1 4-1 11-1 15 0h-3v1h0c-5 0-9 0-13 1h-2c-1 1-1 1-2 1-1 1-1 1-2 1v1c-1 1-2 1-3 2v1c-2-1-2-1-4 0v-1c2-1 4-3 6-3 0-1 1-1 1-1h1s1-1 2-1h0-3-1l2-1z" class="W"></path><path d="M160 488v-1c0-2-1-2-1-3h0s2 2 3 2v-1l2 2c2 1 4 3 6 5v1c3 3 7 5 10 7 3 1 6 3 9 3 2 1 3 1 4 2l2 1h-3l-3-1c-1-1-2-1-2-1-3 0-7-1-10-2-1-3-7-5-10-7-1 0-1-1-2-1s-1-1-2-2h0l-6-6h1l2 2z" class="C"></path><path d="M160 488v-1c0-2-1-2-1-3h0s2 2 3 2v-1l2 2c2 1 4 3 6 5v1l-1 1c-1-1-2-2-3-2-2-2-4-2-6-4z" class="M"></path><defs><linearGradient id="q" x1="141.61" y1="469.105" x2="125.583" y2="433.368" xlink:href="#B"><stop offset="0" stop-color="#0f0f0e"></stop><stop offset="1" stop-color="#373637"></stop></linearGradient></defs><path fill="url(#q)" d="M128 429v1c0 1 1 2 0 3v3h1v-3c0-1 0-1 1-2h0c1 2 0 3 1 4v1 2c1 0 1 0 1 1 1 6 2 11 4 16v1 5c1 3 3 7 4 10h-1l-6-9c1-2 0-4 0-5-1-4-2-8-3-11-1-2-2-3-2-5-1-3-2-6-2-8 1-1 1-2 2-4z"></path><path d="M183 398c3-1 6 0 9 1 1 0 2 0 3 1h0c-2 1-3 0-5 0-4 0-8 1-12 2s-8 2-12 4c-5 3-9 8-13 14-2 3-3 8-4 13v-2h-1c0-2 1-3 1-4 1-4 3-8 5-11 1-1 3-3 3-5l1-1c2-3 6-6 10-8h0c1 0 2-1 3-2 2 0 4 0 5-1h9v-1h-2 0z" class="f"></path><path d="M183 469l1-1c1 4 2 8 4 11 2 2 4 4 5 6 2 2 5 3 7 4l5 4v2l-1 1h1l2 1-1 1c-1 0-2 0-3 1h0l-5-3-2-2-2-2c-3-2-4-4-6-7l-1-1c1 0 1-1 2-1l-1-2c-2-3-3-6-4-9-1-1-1-2-1-3z" class="C"></path><path d="M197 492l6 3c-1 0-1 1-2 1l-1-1c-1-1-2-1-3-3h0z" class="h"></path><path d="M194 492h3c1 2 2 2 3 3l1 1h-3l-2-2-2-2z" class="n"></path><path d="M203 495l1 1h1l2 1-1 1c-1 0-2 0-3 1h0l-5-3h3c1 0 1-1 2-1z" class="m"></path><path d="M189 483c2 3 5 6 8 9h0-3c-3-2-4-4-6-7l-1-1c1 0 1-1 2-1z" class="e"></path><defs><linearGradient id="r" x1="144.087" y1="353.149" x2="182.914" y2="357.278" xlink:href="#B"><stop offset="0" stop-color="#a6a5a7"></stop><stop offset="1" stop-color="#d7d7d5"></stop></linearGradient></defs><path fill="url(#r)" d="M141 359l3-2c9-6 23-8 34-5l12 5c1 1 3 2 4 3h-1 0c-3-2-6-3-9-4-2-1-6-2-9-2-7-1-15 0-22 2-2 1-6 1-8 2v1l-2 1c-1-1-1-1-2-1z"></path><path d="M126 466l3 3 1-1c4 7 8 13 14 18l6 6c1 0 1 0 1 1 2 1 4 3 6 4h0c1 1 2 1 3 2l2 1c2 1 4 2 5 3 2 0 3 0 4 2h0-1c-3-1-6-3-9-4-3-2-5-3-7-4-1-1-2-1-3-2-2-1-5-3-7-4l-1-1h-1s-5-5-5-6l-2-2-3-3h0l-1-1c0-1-1-2-2-3h1l2 1h0l-4-5 1-1c0-1-2-2-3-4z" class="D"></path><path d="M128 471l1-1c3 3 5 6 7 9 3 6 9 10 14 14-2-1-3-2-5-2 0 0 0-1-1 0l-1-1h-1s-5-5-5-6l-2-2-3-3h0l-1-1c0-1-1-2-2-3h1l2 1h0l-4-5z" class="b"></path><path d="M145 359v-1c2-1 6-1 8-2 7-2 15-3 22-2 3 0 7 1 9 2v1c-1 1-2 0-3 1v1c-1 0-1 1-2 1l-3-1h-4c-2-1-4 0-7-1-4-1-7-1-11 1-3 1-5 2-8 4l-1-1v-1c-1 0-1 0-2 1l-1-1c1-1 2-1 3-2z" class="j"></path><path d="M177 357c1 1 3 1 4 1v1c-1 0-1 1-2 1l-3-1h1 1v-1h-1v-1z" class="h"></path><path d="M175 354c3 0 7 1 9 2v1c-1 1-2 0-3 1-1 0-3 0-4-1s-3-1-5-2c2 0 2 0 3-1z" class="k"></path><path d="M145 359v-1c2-1 6-1 8-2 7-2 15-3 22-2-1 1-1 1-3 1-4 0-10 0-14 1-3 1-7 3-9 4-1 0-3 1-4 1s-1 0-2 1l-1-1c1-1 2-1 3-2z" class="f"></path><path d="M144 496h2c6 5 14 10 22 12 3 1 6 1 9 2 7 2 15 4 22 7l7 2 2 1h1c3 1 6 3 8 5 2 1 3 2 4 3v1l-6-4h-1c-2-1-4-2-5-2-7-3-14-5-21-7-2-1-2-1-3-1-9-2-18-6-27-10-2-1-5-2-7-3-2-2-5-4-7-6z" class="h"></path><path d="M184 456c1-4 2-7 4-11 2-3 4-5 7-6-1 1-2 3-3 4v1c-1 1-3 3-3 5 0 1-1 1-1 2h0c-1 2-1 4-1 5v7c1 2 1 3 2 5 0 2 0 4 1 6s6 7 6 9c-1-1-1-1-2 0 2 2 5 4 6 6-2-1-5-2-7-4-1-2-3-4-5-6-2-3-3-7-4-11v-12z" class="e"></path><path d="M193 485l1-1c-1-2-2-3-3-4l-3-3v-2c1 1 4 6 6 7v1c2 2 5 4 6 6-2-1-5-2-7-4z" class="a"></path><path d="M175 379c3 0 5 0 7 2 4 1 10 2 13 5v1h-1c-9-5-19-4-28-1h-3l-4 2-1 1c-1 1-2 2-2 3l-4 3v-1h0v-2l-2 1h-2l1-1v-2h-2c3-3 7-6 10-8 4-1 9-2 13-3h0 5z" class="r"></path><path d="M157 387c2-1 4-1 6-1l-4 2c-1 0-1-1-2-1z" class="h"></path><path d="M175 379c3 0 5 0 7 2-9 0-15 1-23 3h-1c4-2 8-3 12-4h3v-1h-2-1 0 5z" class="Z"></path><path d="M157 387c1 0 1 1 2 1l-1 1c-1 1-2 2-2 3l-4 3v-1h0v-2l-2 1h-2l1-1c2-2 5-4 8-5z" class="n"></path><path d="M152 392l6-3c-1 1-2 2-2 3l-4 3v-1h0v-2z" class="R"></path><path d="M170 379h1 2v1h-3c-4 1-8 2-12 4h1c-1 1-1 1-2 1-3 2-5 4-8 5h-2c3-3 7-6 10-8 4-1 9-2 13-3z" class="n"></path><path d="M161 437c0-1 0-2 1-3 3-7 9-12 16-14l-1 2c-3 1-6 3-8 6h1c-1 1-1 2-2 2h-1c-1 1-2 4-3 5 0 2 1 3 1 4 0 0 0 1 1 1l-2 5 1 1c0-2 1-3 1-5l1-1c0 1 0 2 1 2l-1 3c0 1-1 2-1 3v2h-1c-1 2-1 4-1 7v-1c-1 1-1 2-1 3 1 2 3 5 2 7l1 3c0 2 2 3 2 4l-1 1v-1c0-1-1-1-1-2l-1 1c-2-3-3-6-4-9v-2c0-1-1-1-1-1-1-2-1-3-1-4v-1-6c0-1 0-3 1-5 0 0-1-1 0-1v-1-1c0-1 0-1 1-2v-1-1z" class="W"></path><path d="M166 471c-2-3-3-6-4-9 0-2-1-3-1-4 0-6 2-13 4-19-2 1-2 3-3 4-1-2 2-6 2-8 0 2 1 3 1 4 0 0 0 1 1 1l-2 5c-1 4-3 12-2 15l3 6 1 3c0 2 2 3 2 4l-1 1v-1c0-1-1-1-1-2z" class="f"></path><path d="M164 445l1 1c0-2 1-3 1-5l1-1c0 1 0 2 1 2l-1 3c0 1-1 2-1 3v2h-1c-1 2-1 4-1 7v-1c-1 1-1 2-1 3 1 2 3 5 2 7l-3-6c-1-3 1-11 2-15z" class="W"></path><path d="M177 410c5-1 10-2 14-1 9 1 15 3 22 8v2 3l1 1h0c0 1 0 1 1 2-1 2-1 3-3 6 0-1 0-2-1-3v-1-2c-1 0-1-1-2-1-1-3-2-4-4-5-6-4-14-6-22-6-3 1-7 1-11 3v-1l1-1h-1l2-1v-1c1-1 2-1 3-2z" class="d"></path><path d="M174 412l11-1h0c-1 1-4 1-5 2h0 3c-3 1-7 1-11 3v-1l1-1h-1l2-1v-1z" class="F"></path><defs><linearGradient id="s" x1="214.671" y1="425.162" x2="193.61" y2="410.53" xlink:href="#B"><stop offset="0" stop-color="#0a0a0a"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#s)" d="M191 409c9 1 15 3 22 8v2 3l1 1h0c0 1 0 1 1 2-1 2-1 3-3 6 0-1 0-2-1-3v-1-2c-1-2-2-5-4-7-4-6-15-8-22-9h6z"></path><path d="M180 371c2 1 5 2 7 2h2c5 2 7 6 9 10 1 0 1 0 2-1 0 1 0 2 1 3v-1l1 1-2 4c1 2 1 5-1 7h0v-3c-1-2-2-3-3-5 0 0-1 0-1-1h0v-1c-3-3-9-4-13-5-2-2-4-2-7-2l-3-1v-2l2-1h1c1 0 2 1 3 0h-1c-1-1-2-1-3-2h1l-2-2h4 3z" class="E"></path><path d="M180 371c2 1 5 2 7 2v2c-3-1-7-3-10-4h3z" class="c"></path><path d="M189 373c5 2 7 6 9 10 1 0 1 0 2-1 0 1 0 2 1 3v-1l1 1-2 4c1 2 1 5-1 7h0v-3c0-2 0-4-1-6-1-5-6-10-11-12v-2h2z" class="h"></path><path d="M200 382c0 1 0 2 1 3v-1l1 1-2 4c0-2-1-4-2-6 1 0 1 0 2-1z" class="T"></path><path d="M175 373c4 1 9 2 12 5 3 1 5 3 7 6l1 1 1 1h-1 0c-3-3-9-4-13-5-2-2-4-2-7-2l-3-1v-2l2-1h1c1 0 2 1 3 0h-1c-1-1-2-1-3-2h1z" class="O"></path><path d="M156 392c2-1 6-3 8-3 0 1-1 2-1 2v1l4-2c1 1 2 2 3 2 2 0 4-1 6 0l-6 2c-4 1-7 2-11 4h-1l-5 5c-5 5-9 10-12 16l-1 1c0-1 0-2 1-3 0-1 2-4 2-5-1 0-1 1-1 1-1-1 0-2-1-4v-1l2-2 1-2 1-1c-1-1-1-1-1-2h0c0-2 2-4 3-5l-1-2c1 0 2-1 2-1h2l2-1v2h0v1l4-3z" class="G"></path><path d="M145 404l1-1c0-1 1-2 1-3 1 1 1 2 2 2l-5 5h-1l2-3z" class="M"></path><path d="M147 400c1-1 3-4 5-5v4l-3 3c-1 0-1-1-2-2z" class="X"></path><path d="M152 394h0v1c-2 1-4 4-5 5 0 1-1 2-1 3l-1 1h-1l1-1c-1-1-1-1-1-2h0c1-1 1-1 2-1l6-6z" class="L"></path><path d="M152 392v2l-6 6c-1 0-1 0-2 1 0-2 2-4 3-5l-1-2c1 0 2-1 2-1h2l2-1z" class="J"></path><path d="M148 393h2l-3 3-1-2c1 0 2-1 2-1z" class="j"></path><defs><linearGradient id="t" x1="152.041" y1="394.901" x2="162.16" y2="393.65" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#c0bfc1"></stop></linearGradient></defs><path fill="url(#t)" d="M156 392c2-1 6-3 8-3 0 1-1 2-1 2v1l-11 7v-4l4-3z"></path><path d="M159 364c8-2 15-2 23 0 6 2 11 5 15 10 2 3 4 7 4 10v1c-1-1-1-2-1-3-1 1-1 1-2 1-2-4-4-8-9-10h-2c-2 0-5-1-7-2l-2-1c-1 0-1-1-2-1l4-1h-3-2 0v1 1h-3c-1-1-5 0-6 0-3-1-7 0-9 1l-9 4v-1c1-1 1-1 2-1 3-2 6-3 9-4v-1c1 0 2 0 3-1-1 0-3 0-4-1l1-2z" class="B"></path><path d="M180 368c3 1 6 3 9 5h-2c-2 0-5-1-7-2l-2-1c-1 0-1-1-2-1l4-1z" class="e"></path><path d="M159 369c5-2 13-2 18-1h-2 0v1 1h-3c-1-1-5 0-6 0-3-1-7 0-9 1l-9 4v-1c1-1 1-1 2-1 3-2 6-3 9-4z" class="m"></path><path d="M159 364c8-2 15-2 23 0 6 2 11 5 15 10 2 3 4 7 4 10v1c-1-1-1-2-1-3-2-5-6-9-11-12-8-4-18-5-27-3-1 0-3 0-4-1l1-2z" class="h"></path><defs><linearGradient id="u" x1="169.273" y1="469.264" x2="177.439" y2="466.233" xlink:href="#B"><stop offset="0" stop-color="#0b0b0c"></stop><stop offset="1" stop-color="#363536"></stop></linearGradient></defs><path fill="url(#u)" d="M167 445l1 1h0v3 5c0 2 1 3 1 4 1 1 0 2 1 3v1c1 1 1 1 1 2v2l1 1c0 1 1 2 1 3h0v1s1 0 1 1v-1-1-1h0c1-1 0-3 0-4 2 5 3 10 6 14 1 2 3 3 5 5 2 3 3 5 5 7h-1-1l1 2h0l-3-1-1-1c-1-1-1-1-2-1h-1c-4-1-7-4-10-7-1 0-1 0-2-1h-1c-1 0-2-2-2-3-1-1-2-2-2-4 1 1 2 2 2 3h1v1c1 1 2 1 2 2h1l-1-1v-2c-2-3-4-4-5-6l1-1c0 1 1 1 1 2v1l1-1c0-1-2-2-2-4l-1-3c1-2-1-5-2-7 0-1 0-2 1-3v1c0-3 0-5 1-7h1v-2c0-1 1-2 1-3z"></path><path d="M167 445l1 1h0v3 5c-1-1 0-2-1-4v-1-1h-1c0-1 1-2 1-3z" class="N"></path><path d="M164 457c0-3 0-5 1-7h1c0 4-1 7 0 11v1h-1c0-2-1-3-1-5z" class="l"></path><path d="M165 466c1-2-1-5-2-7 0-1 0-2 1-3v1c0 2 1 3 1 5h1v-1c2 9 6 18 14 23 3 2 7 4 9 7h-1l1 2h0l-3-1-1-1c-1-1-1-1-2-1h-1c-4-1-7-4-10-7-1 0-1 0-2-1h-1c-1 0-2-2-2-3-1-1-2-2-2-4 1 1 2 2 2 3h1v1c1 1 2 1 2 2h1l-1-1v-2c-2-3-4-4-5-6l1-1c0 1 1 1 1 2v1l1-1c0-1-2-2-2-4l-1-3z" class="d"></path><path d="M170 478v-3l1 2c3 5 7 8 12 11 1 1 3 2 5 3l1 2h0l-3-1-1-1c-1-1-1-1-2-1h-1c-4-1-7-4-10-7-1 0-1 0-2-1h-1c-1 0-2-2-2-3-1-1-2-2-2-4 1 1 2 2 2 3h1v1c1 1 2 1 2 2h1l-1-1v-2z" class="I"></path><path d="M116 396v6 2 3l1 9h1v-4c0 4 0 8 1 12l-1 1 1 9 1 4c0-1 0-3 1-4h0l3 12 1-1c0 2 1 4 2 5h0l4 7v3l-1-1h0l6 10-1 1c0 2 1 2 2 3h-1c-1-1-2-2-3-4-3-4-5-8-8-12 0 1 1 3 2 4-2-1-4-7-6-10h0c-1-1-2-4-3-5-1-3-1-6-3-9-1-2-1-5-1-7s-1-4-1-6c-1-5-1-10 0-16h0c-1-1-1-2-1-3 1-2 1-6 1-8l1 1 1-1 1-1z" class="S"></path><path d="M114 430l2 2v1c0 2 0 3-1 4-1-2-1-5-1-7z" class="N"></path><path d="M112 405c1-2 1-6 1-8l1 1 1-1c0 4-1 8-2 11h0 0c-1-1-1-2-1-3z" class="V"></path><path d="M120 438c0-1 0-3 1-4h0l3 12 1-1c0 2 1 4 2 5h0l4 7v3l-1-1h0l6 10-1 1c-7-9-11-21-15-32z" class="R"></path><path d="M125 445c0 2 1 4 2 5h0l4 7v3l-1-1h0c-1 0-1-2-1-2-2-4-4-7-5-11l1-1z" class="H"></path><path d="M116 404v3l1 9h1v-4c0 4 0 8 1 12l-1 1 1 9c-1-1-1-2-1-3l-2-2c-1-3-1-6-2-8v-8c1-2 0-5 1-6 0-1 1-2 1-3z" class="K"></path><path d="M118 425c-1-2-1-5-1-7-1-2-2-3-2-5s1-4 1-6l1 9h1v-4c0 4 0 8 1 12l-1 1z" class="I"></path><path d="M147 390h2v2l-1 1s-1 1-2 1l1 2c-1 1-3 3-3 5h0c0 1 0 1 1 2l-1 1-1 2-2 2v1c0 1-1 2-1 3l-3 5-1 1-1 5v7c0 1 0 3-1 4v2c-1-1 0-3-1-4 0 2 0 4-1 6v1c0-1 0-1-1-1v-2-1c-1-1 0-2-1-4h0c-1 1-1 1-1 2v3h-1v-3c1-1 0-2 0-3v-1c-1 2-1 3-2 4v-2c0-5-1-10 1-15 0-1 0-3 1-5l1-3 2-7 1-1c0-1 0-1 1-2l1 1c1-1 1-2 1-3l2 1-1 1h2l3-3c0-1 1-3 2-3h1v2h0l3-4z" class="F"></path><path d="M130 424h0l2-6v6 2h0c-1-1-1-1-1-2v1l-1-1z" class="I"></path><path d="M135 396l2 1-1 1c0 2-1 4-3 5 0-2 1-3 1-4 1-1 1-2 1-3z" class="k"></path><path d="M132 400c0-1 0-1 1-2l1 1c0 1-1 2-1 4 0 1-1 2-1 4l-1-1c1-2 1-4 1-6z" class="l"></path><path d="M132 400c0 2 0 4-1 6l1 1-1 5c-1 1-1 2-2 4v1l-1 5c0-2 0-4-1-6h0c0-1 0-3 1-5l1-3 2-7 1-1z" class="a"></path><path d="M128 411h0l2-4h1c-1 2-3 6-2 9v1l-1 5c0-2 0-4-1-6h0c0-1 0-3 1-5z" class="R"></path><path d="M133 412c1 2 1 3 1 4v1c0 2 0 4 1 6v7c0 1 0 3-1 4v2c-1-1 0-3-1-4 0-2-1-4-1-6v-2c1-3-1-7 1-10v-2z" class="M"></path><path d="M127 416h0c1 2 1 4 1 6l1-5c1 1 1 3 1 5v-1 2 1l1 1v-1c0 1 0 1 1 2h0c0 2 1 4 1 6s0 4-1 6v1c0-1 0-1-1-1v-2-1c-1-1 0-2-1-4h0c-1 1-1 1-1 2v3h-1v-3c1-1 0-2 0-3v-1c-1 2-1 3-2 4v-2c0-5-1-10 1-15z" class="E"></path><path d="M128 429c-1-2 0-4 1-6v8c1-2 1-4 2-6v-1c0 1 0 1 1 2h0c0 2 1 4 1 6s0 4-1 6v1c0-1 0-1-1-1v-2-1c-1-1 0-2-1-4h0c-1 1-1 1-1 2v3h-1v-3c1-1 0-2 0-3v-1z" class="G"></path><path d="M132 426h0c0 2 1 4 1 6s0 4-1 6v1c0-1 0-1-1-1v-2-1l1-9z" class="L"></path><path d="M147 390h2v2l-1 1s-1 1-2 1l1 2c-1 1-3 3-3 5h0c0 1 0 1 1 2l-1 1-1 2-2 2v1c0 1-1 2-1 3l-3 5-1 1-1 5c-1-2-1-4-1-6v-1c0-1 0-2-1-4 1-1 1-3 2-4 0-1 1-2 1-4s1-4 2-6l3-3c0-1 1-3 2-3h1v2h0l3-4z" class="m"></path><path d="M141 405l3-4h0c0 1 0 1 1 2l-1 1-1 2-2-1z" class="R"></path><path d="M147 390h2v2l-1 1s-1 1-2 1c0 1-1 2-2 3l-1-1c1 0 1-1 1-2l3-4z" class="h"></path><path d="M138 403h1c1-1 1-2 2-3h0l-5 11-1 1s0-1 1-2h0c1-1 0-3 0-4s1-2 2-3z" class="W"></path><path d="M141 395c0-1 1-3 2-3h1v2l-4 4c0 2-1 4-2 5s-2 2-2 3 1 3 0 4h0c-1 1-1 2-1 2 0 2-1 4-1 5v-1c0-1 0-2-1-4 1-1 1-3 2-4 0-1 1-2 1-4s1-4 2-6l3-3z" class="c"></path><path d="M141 405l2 1-2 2v1c0 1-1 2-1 3l-3 5-1 1-1 5c-1-2-1-4-1-6 0-1 1-3 1-5l1-1 2 1 3-7z" class="L"></path><path d="M136 411l2 1c-1 2-2 4-2 6l-1 5c-1-2-1-4-1-6 0-1 1-3 1-5l1-1z" class="p"></path><defs><linearGradient id="v" x1="141.923" y1="457.279" x2="119.81" y2="403.372" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#3b3a3b"></stop></linearGradient></defs><path fill="url(#v)" d="M130 390l1-2v3c1-1 2-2 3-2v2 1l-1 2c0 2-1 3-2 4v3l-2 7-1 3c-1 2-1 4-1 5-2 5-1 10-1 15v2c0 2 1 5 2 8 0 2 1 3 2 5 1 3 2 7 3 11 0 1 1 3 0 5l-2-5-4-7h0c-1-1-2-3-2-5l-1 1-3-12h0c-1 1-1 3-1 4l-1-4-1-9 1-1c-1-4-1-8-1-12 1-5 1-10 3-15l3-6h0c1 1 0 1 1 1v1c-1 1-1 2-1 4 2-3 3-5 4-8v1l1-1 1 1z"></path><path d="M129 395c1 0 1-1 1-1 1-1 1-1 2-1 0 1-1 1-1 2-1 1-1 2-2 3 0-1-1-2 0-3z" class="Q"></path><path d="M130 390l1-2v3c1-1 2-2 3-2v2l-2 2c-1 0-1 0-2 1 0 0 0 1-1 1 0-2 1-4 1-5z" class="X"></path><path d="M126 431c-1-4 0-7 0-10s-1-5-1-8h1c0-1 1-1 1-2v-2l2-1-1 3c-1 2-1 4-1 5-2 5-1 10-1 15z" class="G"></path><path d="M128 390l1-1 1 1c0 1-1 3-1 5-1 1 0 2 0 3 0 2-1 4-2 5-1-1-1-2-2-3l3-10z" class="E"></path><path d="M128 389v1l-3 10c1 1 1 2 2 3h-1c0 1-1 2-1 3h-1 0l-1 5-1 1h0-1l3-15c2-3 3-5 4-8z" class="J"></path><path d="M125 400c1 1 1 2 2 3h-1c0 1-1 2-1 3h-1 0c0-2 0-4 1-6z" class="V"></path><path d="M123 411c-2 13 0 26 4 39h0c-1-1-2-3-2-5-2-3-2-7-3-10-1-7-2-15-1-23h1 0l1-1z" class="M"></path><path d="M124 391c1 1 0 1 1 1v1c-1 1-1 2-1 4l-3 15c-1 8 0 16 1 23 1 3 1 7 3 10l-1 1-3-12h0c-1 1-1 3-1 4l-1-4-1-9 1-1c-1-4-1-8-1-12 1-5 1-10 3-15l3-6h0z" class="g"></path><path d="M119 424c0 2 0 3 1 5 0 2 1 4 1 5h0c-1 1-1 3-1 4l-1-4-1-9 1-1z" class="L"></path><path d="M177 368h3l-4 1c1 0 1 1 2 1l2 1h-3-4l2 2h-1c1 1 2 1 3 2h1c-1 1-2 0-3 0h-1l-2 1v2l3 1h-5 0c-4 1-9 2-13 3-3 2-7 5-10 8l-3 4h0v-2h-1c-1 0-2 2-2 3l-3 3h-2l1-1-2-1c0 1 0 2-1 3l-1-1c-1 1-1 1-1 2l-1 1v-3c1-1 2-2 2-4l1-2v-1-2l2-2v-2c0-1 3-3 4-4h0l8-6 9-4c2-1 6-2 9-1 1 0 5-1 6 0h3v-1-1h0 2z" class="j"></path><path d="M135 396c1-1 1-1 1-3h0c1 0 1-1 2-1h0l-1 2v1 2l-2-1z" class="l"></path><path d="M139 393l2 2-3 3h-2l1-1v-2l2-2zm4-11c2-2 4-4 6-5 1 1 1 1 2 1l-4 3c-3 1-5 3-7 5v-3c1 0 2-1 3-1z" class="J"></path><path d="M162 379l10-3v2l3 1h-5 0c-4 1-9 2-13 3h0c1-1 3-1 4-2 2 0 6 0 7-2-1-1-4 1-6 1h0z" class="U"></path><path d="M146 387c3-2 6-4 11-5h0c-3 2-7 5-10 8l-3 4h0v-2h-1c-1 0-2 2-2 3l-2-2 1-1c1-2 3-4 6-5z" class="d"></path><path d="M140 392c1-2 3-4 6-5-1 2-1 3-2 4h-1c-1 0-2 1-3 1z" class="W"></path><path d="M173 371h0l2 2h-1c1 1 2 1 3 2h1c-1 1-2 0-3 0h-1l-2 1-10 3c-3-1-8 1-11 3-1 0-1 1-2 1s-2 1-3 2h0-1 0v-1l6-4h0c-1 1-3 2-4 1h0l4-3c-1 0-1 0-2-1 7-3 12-4 19-6h5z" class="n"></path><path d="M173 371h0l2 2h-1c1 1 2 1 3 2h1c-1 1-2 0-3 0h-1l-1-1c-2 0-5 2-8 1l3-1h0c0-1 1-1 1-1h2v-1l-3-1h5z" class="B"></path><path d="M149 377c7-3 12-4 19-6l3 1v1h-2s-1 0-1 1c-6 1-12 2-17 4h0c-1 0-1 0-2-1z" class="D"></path><path d="M177 368h3l-4 1c1 0 1 1 2 1l2 1h-3-4 0-5c-7 2-12 3-19 6-2 1-4 3-6 5-1 0-2 1-3 1v3l-1 2c-1 1-2 2-3 4 0 1-1 2-2 3 0 1-1 2-1 3-1 1-1 1-1 2l-1 1v-3c1-1 2-2 2-4l1-2v-1-2l2-2v-2c0-1 3-3 4-4h0l8-6 9-4c2-1 6-2 9-1 1 0 5-1 6 0h3v-1-1h0 2z" class="c"></path><path d="M136 387v1l-1 4h-1v-1-2l2-2z" class="R"></path><path d="M139 388c-1-1-1-2-1-3 1-2 2-3 4-4l1 1c-1 0-2 1-3 1v3l-1 2z" class="a"></path><path d="M177 368h3l-4 1c1 0 1 1 2 1l2 1h-3-4 0-5c-7 2-12 3-19 6-2 1-4 3-6 5l-1-1c5-4 11-8 17-10 2-1 4-1 7-1 1 0 5-1 6 0h3v-1-1h0 2z" class="e"></path><path d="M177 368h3l-4 1c1 0 1 1 2 1l2 1h-3-4 0 0c0-1-1-1-1-1-1 0-2 1-3 0h3 0 3v-1-1h0 2z" class="h"></path><defs><linearGradient id="w" x1="191.417" y1="342.229" x2="192.725" y2="334.638" xlink:href="#B"><stop offset="0" stop-color="#d0cece"></stop><stop offset="1" stop-color="#f7f6f7"></stop></linearGradient></defs><path fill="url(#w)" d="M166 332c8-3 16-3 24-1h1 1c2 0 2 1 3 1 2 1 3 0 5 1l3 2 2 1 2 1 3 1v-1c3 1 5 3 7 6 1 0 2 1 2 1l-1 1h0c1 2 0 2 1 4v2 1c1 1 1 2 1 3h0l-1 1 1 4c0 1 0 1-1 1v-2c-3-6-9-11-15-14-1 1-2 1-3 1-2-2-6-3-8-4h-3l1-2h-6-7-2v-1h-4-4 0l-1-1c2-2 4-1 6-2-3-1-7 1-10 3h-1v-1c1 0 1-1 1-2-2 1-3 2-5 3l-2 1h0c1-1 2-2 4-3h0v-2l6-3z"></path><path d="M205 336l2 1c-1 0-1 0-2 1l-2-1c0-1 1-1 2-1z" class="B"></path><path d="M210 337c3 1 5 3 7 6 1 0 2 1 2 1l-1 1h0c1 2 0 2 1 4v2l-1-3c-4-5-8-7-13-10 1-1 1-1 2-1l3 1v-1z" class="C"></path><path d="M210 337c3 1 5 3 7 6 1 0 2 1 2 1l-1 1h0c1 2 0 2 1 4v2l-1-3v-3c-2-3-6-5-8-7v-1z" class="S"></path><path d="M190 331h1 1c2 0 2 1 3 1 2 1 3 0 5 1l3 2 2 1c-1 0-2 0-2 1-10-4-24-6-34-3 4-2 11-3 15-3 2 0 4 1 6 0z" class="D"></path><path d="M166 332c8-3 16-3 24-1-2 1-4 0-6 0-4 0-11 1-15 3-1 1-1 1-2 1h-1 0c-1 1-1 0-1 0-1 1-1 1-2 1h0c-2 1-3 2-5 3l-2 1h0c1-1 2-2 4-3h0v-2l6-3z" class="e"></path><path d="M168 339l8-3h9c9 1 17 3 25 8 2 1 4 3 6 5 1 0 2 3 3 3 1 1 1 2 1 3h0l-1 1c-1-3-2-6-5-8-8-7-19-11-31-10v1h2v1h-7-2v-1h-4-4 0z" class="f"></path><path d="M172 339l11-1v1h2v1h-7-2v-1h-4z" class="m"></path><path d="M183 338c12-1 23 3 31 10 3 2 4 5 5 8l1 4c0 1 0 1-1 1v-2c-3-6-9-11-15-14-1 1-2 1-3 1-2-2-6-3-8-4h-3l1-2h-6v-1h-2v-1z" class="s"></path><path d="M191 340c4 1 9 3 13 5-1 1-2 1-3 1-2-2-6-3-8-4h-3l1-2z" class="g"></path><path d="M148 431h1v2c-2 15 3 29 12 41h0c3 7 12 16 20 19l5 4h1c5 3 12 5 17 7 13 5 24 14 32 25l-1 1c-2-3-4-5-7-8-3 1-4-1-6-2-2 0-2-1-3-2l-5-3c-5-2-9-4-13-6l-6-3-2-1c-1-1-2-1-4-2-3 0-6-2-9-3-3-2-7-4-10-7v-1c-2-2-4-4-6-5l-2-2c0-1-2-3-4-4-3-7-6-13-8-21-3-10-3-19-2-29h0z" class="q"></path><path d="M182 498l6 3c1 1 2 1 3 1 1 1 2 2 4 2l1 1h-3c-1-1-2-1-4-2-3 0-6-2-9-3l2-2z" class="W"></path><path d="M170 492v-2c4 4 8 6 12 8l-2 2c-3-2-7-4-10-7v-1z" class="a"></path><path d="M150 460v1l1-1c2 4 3 9 5 12s6 6 6 9c0 2 3 4 5 5 1 1 2 3 3 4v2c-2-2-4-4-6-5l-2-2c0-1-2-3-4-4-3-7-6-13-8-21z" class="j"></path><defs><linearGradient id="x" x1="211.831" y1="515.333" x2="212.954" y2="512.552" xlink:href="#B"><stop offset="0" stop-color="#2f2f2f"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#x)" d="M193 505h3c12 5 22 9 32 17-3 1-4-1-6-2-2 0-2-1-3-2l-5-3c-5-2-9-4-13-6l-6-3-2-1z"></path><path d="M178 489c-2-1-6-3-6-6 3 3 6 6 10 7h1c1 0 1 0 2 1l1 1 3 1h0l-1-2h1 1c2 1 3 3 6 3l2 2 5 3h0c1-1 2-1 3-1l1-1c1 1 2 2 3 2l3 2 5 3c1 1 3 1 3 2l5 5c1 1 2 1 4 1h0 1 0c3 3 7 6 9 10 1 1 2 1 3 2v1l-1 1c1 0 1 1 2 1 1 3 2 6 4 8 0 1 1 3 1 3 5 12 5 26 2 38 0 2-1 5-2 6l-2 5c0-3 1-6 2-8 0-5 1-9 1-14v-3c-1-2 0-4-1-6-2-3-2-7-3-10-1-4-4-8-6-11l-2-4c-1 0-1-1-2-2h0c-8-11-19-20-32-25-5-2-12-4-17-7h-1l-5-4c-1-2-2-2-3-4z" class="b"></path><path d="M198 498h1c1 0 1 1 1 1h3 0c1 0 2 0 2 1l1 2c-3-1-5-2-8-4z" class="R"></path><path d="M178 489l4 3c1 0 1 0 2 1l-1 1h1c1 1 2 1 2 3l-5-4c-1-2-2-2-3-4z" class="B"></path><path d="M182 490h1c1 0 1 0 2 1l1 1 3 1h0l-1-2h1 1c2 1 3 3 6 3l2 2 5 3h-3s0-1-1-1h-1c-1 0-3-1-4-2-4-1-8-3-12-6z" class="F"></path><defs><linearGradient id="y" x1="217.612" y1="509.803" x2="209.22" y2="498.376" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#y)" d="M207 497c1 1 2 2 3 2l3 2 5 3c1 1 3 1 3 2l-1 1c1 1 2 2 2 3l-16-8-1-2c0-1-1-1-2-1 1-1 2-1 3-1l1-1z"></path><path d="M207 497c1 1 2 2 3 2l3 2c-2 1-5-1-8-1 0-1-1-1-2-1 1-1 2-1 3-1l1-1z" class="k"></path><path d="M236 529c2 1 3 1 4 2v1c0 1 1 1 2 2h0v-2h0c2 2 3 4 4 6v3l1 1c1 2 1 3 1 5 1 0 1 1 1 2s1 4 1 5h0c-1 1-1 1-1 2-2-3-2-7-3-10-1-4-4-8-6-11l-2-4c-1 0-1-1-2-2z" class="S"></path><defs><linearGradient id="z" x1="240.58" y1="525.383" x2="228.894" y2="511.842" xlink:href="#B"><stop offset="0" stop-color="#373637"></stop><stop offset="1" stop-color="#5a5a5a"></stop></linearGradient></defs><path fill="url(#z)" d="M221 506l5 5c1 1 2 1 4 1h0 1 0c3 3 7 6 9 10 1 1 2 1 3 2v1l-1 1c1 0 1 1 2 1l-1 2c-1 0-2-2-2-2-2-2-3-4-5-6-4-4-8-8-14-11 0-1-1-2-2-3l1-1z"></path><defs><linearGradient id="AA" x1="149.672" y1="362.921" x2="155.147" y2="384.592" xlink:href="#B"><stop offset="0" stop-color="#0c0c0b"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#AA)" d="M140 353c1 0 1-1 2-2v2l-4 3 1 1h0c0 1 1 1 1 2h1 0c1 0 1 0 2 1l2-1c-1 1-2 1-3 2l1 1c1-1 1-1 2-1v1l1 1c3-2 5-3 8-4 4-2 7-2 11-1 3 1 5 0 7 1h-9l9 1c3 0 5 0 8 2h2l1 1h-1v1c-8-2-15-2-23 0l-1 2c1 1 3 1 4 1-1 1-2 1-3 1v1c-3 1-6 2-9 4-1 0-1 0-2 1v1l-8 6h0c-1 1-4 3-4 4v2l-2 2c-1 0-2 1-3 2v-3l-1 2-1-1-1 1v-1c-1 3-2 5-4 8 0-2 0-3 1-4v-1c-1 0 0 0-1-1h0l-3 6c-2 5-2 10-3 15v4h-1l-1-9v-3-2c1-1 1-3 2-4v1c3-5 5-12 8-17 0-1 0-1 1-2l-1 1h-1-1c1-1 1-2 1-3l-1-1v-1c0-1 0-1 1-2l-1-1c1-2 2-3 3-5 1-1 2-2 3-4h-1l1-1c2-1 3-3 4-4 2-2 3-4 5-5l1-1z"></path><path d="M128 389c1-2 4-6 5-8v3c0 1-1 3-2 4l-1 2-1-1-1 1v-1z" class="F"></path><path d="M154 359c4-2 7-2 11-1 3 1 5 0 7 1h-9c-2 1-5 1-7 2-9 2-18 10-25 17-2 4-6 8-7 13h0-1c0-1 0-1 1-2h0c1-4 4-8 6-11 7-8 14-14 24-18v-1z" class="W"></path><path d="M154 359v1c-10 4-17 10-24 18-2 3-5 7-6 11h0c-1 1-1 1-1 2h1l-3 6c-2 5-2 10-3 15v4h-1l-1-9v-3-2c1-1 1-3 2-4v1c3-5 5-12 8-17 0-1 0-1 1-2l-1 1h-1l10-15h1l1-1c0 1 3-2 4-2h2c0-1 1-1 2-1l1 1c3-2 5-3 8-4z" class="Q"></path><path d="M143 363c0-1 1-1 2-1l1 1c-6 4-11 8-16 14h-1c1-3 11-12 14-14z" class="p"></path><path d="M140 353c1 0 1-1 2-2v2l-4 3 1 1h0c0 1 1 1 1 2h1 0c1 0 1 0 2 1l2-1c-1 1-2 1-3 2l1 1c1-1 1-1 2-1v1c-1 0-2 0-2 1h-2c-1 0-4 3-4 2l-1 1h-1l-10 15h-1c1-1 1-2 1-3l-1-1v-1c0-1 0-1 1-2l-1-1c1-2 2-3 3-5 1-1 2-2 3-4h-1l1-1c2-1 3-3 4-4 2-2 3-4 5-5l1-1z" class="l"></path><path d="M139 357h0c0 1 1 1 1 2h1c-3 3-6 5-9 8v-1c2-2 5-6 7-9z" class="F"></path><path d="M145 359c-1 1-2 1-3 2l1 1c1-1 1-1 2-1v1c-1 0-2 0-2 1h-2c-1 0-4 3-4 2l-1 1h-1c2-2 5-4 8-6l2-1z" class="R"></path><path d="M140 353c1 0 1-1 2-2v2l-4 3 1 1c-2 3-5 7-7 9-1 0-1 2-3 1h1c-1-1-1-1 0-2v-1h-1l1-1c2-1 3-3 4-4 2-2 3-4 5-5l1-1z" class="n"></path><path d="M150 368c3-2 6-3 9-4l-1 2c1 1 3 1 4 1-1 1-2 1-3 1v1c-3 1-6 2-9 4-1 0-1 0-2 1v1l-8 6h0c-1 1-4 3-4 4v2l-2 2c-1 0-2 1-3 2v-3c1-1 2-3 2-4v-3l3-3c4-4 8-8 14-10z" class="W"></path><path d="M150 368c-1 2-2 2-4 4-1 0-1 1-2 2-2 2-5 4-7 6h-1v-2c4-4 8-8 14-10z" class="X"></path><path d="M137 380v1c2-1 4-3 6-4h0c-1 1-3 2-3 4h0c-1 1-4 3-4 4v2l-2 2c-1 0-2 1-3 2v-3c1-1 2-3 2-4v-3l3-3v2h1z" class="d"></path><path d="M133 381l3-3v2c-1 2-2 3-3 4v-3z" class="Z"></path><path d="M199 405c7 1 13 2 18 7l1-1 4 4v-1c0-1 1-1 0-3 1 0 2 1 2 1l1 1h0l1 1s0-1 1-1h0v2 1l-1 1h-1 0v1s0 1-1 2h1c0 1 0 2-1 3v2l1 2c0 1 0 3-1 5l1 2-1 4h0 0c-2 1-2 2-3 4h0c-3 6-9 8-11 15h0c0 10 6 20 13 27l21 18-2 1h0v2l6 6c2 2 4 5 6 7 0 2 1 3 2 4 1 3 3 6 3 9 1 2 1 4 2 7h0v7c0-2 0-4-1-5-1 2-1 3-1 5l-1-8c0 1 0 2-1 3h0v5h-1c0-1 1-3 0-4 0-2 0-3-1-5 0-1-1-2-1-4l-2-3c0-1 0-2-1-3 0-1-1-2-1-2 0-1-1-1-1-2l-1 1v-1 2c1 0 1 1 1 1v1c-2-2-4-4-5-7-6-5-12-11-18-14-1-1-2-1-3-2-1 0-2 1-3 1h-2l-5-3-3-2c-1 0-2-1-3-2l-2-1h-1l1-1v-2l-5-4c-1-2-4-4-6-6 1-1 1-1 2 0 0-2-5-7-6-9s-1-4-1-6c-1-2-1-3-2-5v-7c0-1 0-3 1-5h0c0-1 1-1 1-2 0-2 2-4 3-5v-1c1-1 2-3 3-4h0c5-4 9-5 14-7h0c1 0 2-1 3-1 2-3 2-4 3-6-1-1-1-1-1-2h0l-1-1v-3-2c-7-5-13-7-22-8-4-1-9 0-14 1v-1c1 0 1 0 2-1 1 0 1 0 2-1h2c4-1 8-1 13-1h0v-1h3z" class="Y"></path><path d="M209 457c1-6 8-9 11-14 2-3 3-8 4-11l1 2-1 4h0 0c-2 1-2 2-3 4h0c-3 6-9 8-11 15h0-1z" class="E"></path><path d="M215 419v1c2 1 2 3 4 5-1-2-1-4-1-6 2 5 3 9 2 14-1-1-1-1-1-2h-1v1h-1v-7l-1-1v-1l-1-4z" class="L"></path><path d="M217 432h1v-1h1c0 1 0 1 1 2 0 1-1 2-1 3-2 4-7 10-11 12 1-1 3-3 3-5 1-1 2-3 3-4 1-2 2-4 3-7h0z" class="E"></path><path d="M218 411l4 4v-1c0-1 1-1 0-3 1 0 2 1 2 1l1 1h0l1 1s0-1 1-1h0v2 1l-1 1h-1 0v1s0 1-1 2h1c0 1 0 2-1 3v2c0-1 0-2-1-3s-1-2-1-3l-3-3c0-2-1-3-2-4l1-1z" class="I"></path><path d="M225 413h0l1 1s0-1 1-1h0v2 1l-1 1c-1-1-1-2-2-4h1zm-2 9l1-4c0-1 0-2-1-2l1-1 1 2h0v1s0 1-1 2h1c0 1 0 2-1 3v2c0-1 0-2-1-3z" class="F"></path><path d="M215 425v3c1 3 0 5-1 7-2 3-4 7-7 10-1-1 0-1-1-1s-1 1-2 1h0c1-3 3-5 5-7h0v-1c1-1 1-2 1-3 0 0-1 0-1-1h1l-1-1h0c1 0 2-1 3-1 2-3 2-4 3-6z" class="T"></path><path d="M215 425v3c0 1 0 3-1 4-2 1-2 2-3 4h0c0 1-1 1-2 2h0v-1c1-1 1-2 1-3 0 0-1 0-1-1h1l-1-1h0c1 0 2-1 3-1 2-3 2-4 3-6z" class="B"></path><path d="M196 406h2c6 1 13 4 17 9 2 1 2 3 3 4 0 2 0 4 1 6-2-2-2-4-4-5v-1h-2v-2c-7-5-13-7-22-8-4-1-9 0-14 1v-1c1 0 1 0 2-1 1 0 1 0 2-1h2c4-1 8-1 13-1h0z" class="p"></path><path d="M200 409c3 0 9 3 12 5 1 0 2 1 3 1 2 1 2 3 3 4 0 2 0 4 1 6-2-2-2-4-4-5v-1h-2v-2h0c-3-4-9-6-13-8z" class="S"></path><path d="M196 406h2v1h-4 0c0 1 0 1 1 1h2l2 1h1 0c4 2 10 4 13 8h0c-7-5-13-7-22-8-4-1-9 0-14 1v-1c1 0 1 0 2-1 1 0 1 0 2-1h2c4-1 8-1 13-1h0z" class="m"></path><path d="M209 457h1c0 10 6 20 13 27l21 18-2 1h0v2c-4-3-8-5-12-8 1-2 0-2 0-4-1-2-13-6-15-8l-1-1-4-3c-5-6-9-14-9-22 0 2 1 3 1 4 2 8 10 18 18 22-1-1-1-2-2-3l-4-5c-4-6-6-13-5-20z" class="r"></path><path d="M230 493l6 5c2 2 4 3 6 5v2c-4-3-8-5-12-8 1-2 0-2 0-4z" class="W"></path><defs><linearGradient id="AB" x1="257.533" y1="521.903" x2="238.538" y2="521.265" xlink:href="#B"><stop offset="0" stop-color="#151617"></stop><stop offset="1" stop-color="#383839"></stop></linearGradient></defs><path fill="url(#AB)" d="M202 478h0c-1-2-3-4-4-6 5 5 8 10 15 13-2-1-4-3-5-4 2 0 4 3 6 3l1 1c2 2 14 6 15 8 0 2 1 2 0 4 4 3 8 5 12 8l6 6c2 2 4 5 6 7 0 2 1 3 2 4 1 3 3 6 3 9 1 2 1 4 2 7h0v7c0-2 0-4-1-5-1 2-1 3-1 5l-1-8c0 1 0 2-1 3h0v5h-1c0-1 1-3 0-4 0-2 0-3-1-5 0-1-1-2-1-4l-2-3c0-1 0-2-1-3 0-1-1-2-1-2 0-1-1-1-1-2 1-1 0-1 0-1h-1v-1h0c0-1-1-1-1-2-2-3-6-6-9-10-1 0-2-2-3-2-1-1-1-2-2-3s-3-1-4-2c-3-2-14-9-15-10l1-1c-6-2-10-7-13-12z"></path><path d="M258 537c0-2-1-4-1-5-2-6-4-11-7-16-1-1-2-2-3-4l-6-6h0c1 0 2 1 3 2 2 1 2 3 4 3 2 2 4 5 6 7 0 2 1 3 2 4 1 3 3 6 3 9 1 2 1 4 2 7h0v7c0-2 0-4-1-5-1 2-1 3-1 5l-1-8z" class="M"></path><path d="M259 531c1 2 1 4 2 7-1 0-1 1-1 2h-1v-9z" class="R"></path><path d="M202 478h0c-1-2-3-4-4-6 5 5 8 10 15 13-2-1-4-3-5-4 2 0 4 3 6 3l1 1c2 2 14 6 15 8 0 2 1 2 0 4l-5-4v1l12 9c-1 0-1 0-2-1-3-1-5-3-8-5-4-3-8-5-12-7-6-2-10-7-13-12z" class="n"></path><path d="M209 432l1 1h-1c-7 3-11 6-15 12-1 2-2 4-2 7l-1 9 1 7h0c1 0 2 0 2 1 1 1 1 3 2 4 0 1 1 1 1 3 0 1 1 3 2 3l1 1h1l-1 1c1 1 1 2 2 3 1-1 1-1 2 0h1c0-1-1-2-2-3h-1v-3c3 5 7 10 13 12l-1 1c1 1 12 8 15 10 1 1 3 1 4 2s1 2 2 3c1 0 2 2 3 2 3 4 7 7 9 10 0 1 1 1 1 2h0v1h1s1 0 0 1l-1 1v-1 2c1 0 1 1 1 1v1c-2-2-4-4-5-7-6-5-12-11-18-14-1-1-2-1-3-2-1 0-2 1-3 1h-2l-5-3-3-2c-1 0-2-1-3-2l-2-1h-1l1-1v-2l-5-4c-1-2-4-4-6-6 1-1 1-1 2 0 0-2-5-7-6-9s-1-4-1-6c-1-2-1-3-2-5v-7c0-1 0-3 1-5h0c0-1 1-1 1-2 0-2 2-4 3-5v-1c1-1 2-3 3-4h0c5-4 9-5 14-7z" class="q"></path><path d="M190 452c0-1 0-3 1-5 1-1 2-2 3-2-1 2-2 4-2 7h-2z" class="e"></path><path d="M190 452h2l-1 9v1c-1 2 0 3-1 5-1-5-1-10 0-15z" class="W"></path><path d="M190 467c1-2 0-3 1-5v-1l1 7h0c1 4 3 7 5 10h-2c-1-1-1-1-1-2-2-3-3-6-4-9z" class="h"></path><path d="M190 467c1-2 0-3 1-5v-1l1 7-1-1c0 3 3 6 3 9-2-3-3-6-4-9z" class="l"></path><path d="M205 493l18 10c-1 0-2 1-3 1h-2l-5-3-3-2c-1 0-2-1-3-2l-2-1h-1l1-1v-2z" class="E"></path><path d="M205 496h3c1 1 2 1 3 2 0 1-1 1-1 1-1 0-2-1-3-2l-2-1z" class="C"></path><path d="M192 468c1 0 2 0 2 1 1 1 1 3 2 4 0 1 1 1 1 3 0 1 1 3 2 3l1 1h1l-1 1c1 1 1 2 2 3 1-1 1-1 2 0h1c0-1-1-2-2-3h-1v-3c3 5 7 10 13 12l-1 1c1 1 12 8 15 10 1 1 3 1 4 2s1 2 2 3c1 0 2 2 3 2 3 4 7 7 9 10 0 1 1 1 1 2h0v1h1s1 0 0 1l-1 1v-1 2c1 0 1 1 1 1v1c-2-2-4-4-5-7h0c-1-1-3-3-3-4-8-8-17-14-26-20-3-1-6-4-9-6l-4-4-5-7c-2-3-4-6-5-10z" class="P"></path><path d="M202 485c3 0 5 1 7 3l-3 1-4-4z" class="I"></path><path d="M209 488c3 2 6 4 6 7-3-1-6-4-9-6l3-1z" class="Q"></path><path d="M252 367v-3l1 2s0 1 1 2v2h1 0l1-1v1l1-1v4 6 4c0 2-1 6-1 8 1 1 1 1 2 1 0 8 2 16 0 24-2 3-2 6-4 10h1c1-1 1-2 1-3l1 2v-1 2 1h1c0-1 0-1 1-2 0 2-1 3-1 4v1c0 2-2 3-3 5v3c1 2 0 3 1 5v5 1c0 3-1 6-1 9h0l1-1h0c0 1 0 2-1 3 0 1-2 2-2 3-1 2-1 4-3 5h0c-3 4-3 8-5 11h0l-1 1c0 2-1 3-1 4 1 2 1 4 1 6 0 1 1 3 1 5l1-4 1 1h0l1-2v4l1 6c3 5 6 10 8 15v2c1 0 1 1 1 1v2 1c1 0 1 1 1 2-3-2-3-5-5-8v2c1 1 2 4 2 5-1-1-2-2-2-4-2-2-4-5-6-7l-6-6v-2h0l2-1-21-18c-7-7-13-17-13-27h0c2-7 8-9 11-15h0c1-2 1-3 3-4h0 0l1-4-1-2c1-2 1-4 1-5l-1-2v-2c1-1 1-2 1-3h-1c1-1 1-2 1-2v-1h0 1l1-1v-1-2h0c-1 0-1 1-1 1l-1-1c1 0 1-1 3-2v-1c-1 0-1 0-2-1 0 0-1 0-1-1s0-1-1-1v-2-1c0-1 1-1 2-2 0 2 1 3 1 4 1-4-1-7-1-11l1 1v-3l1-1h0s1 0 0 1v3l1 1c1 1 1 2 1 2 1 2 1 5 2 6s1 2 2 3l2-1 1-2c0 1 1 1 1 2v-1h1c0 1 0 2 1 3 0 1 2 3 3 4v-2c-1-2-1-4-2-5-1-2-1-2-1-4h1v-5h1c1-2 2-3 3-4l3-6 1-3c1-1 1-2 1-3l-1-1c1 0 1-1 1-2v-2l1-1v-2c0-2 0-4 1-6z" class="Y"></path><path d="M226 476l-1-5 3 3c0 1 0 2-1 2h-1 0z" class="G"></path><path d="M249 429c2 1 2 1 2 2 1 3 1 4 0 7l-2-9z" class="N"></path><path d="M229 447c1-2 2-4 3-5h0l1 1c-1 1-1 2-1 2h1l-2 4-2-2z" class="C"></path><path d="M243 411h0c1 3 6 8 5 12v2l-5-12v-2z" class="U"></path><path d="M240 471l6-12 1 3-1 1c-1 1-1 2-2 4 0 0-1 1-1 2s-1 2-1 2h-1l-1 3-1 2v-2h0l1-3z" class="B"></path><path d="M227 467c1-1 1-3 3-4-1 1-1 2-1 4h1l-1 4c0 1 0 2-1 3l-3-3c1-1 1-3 2-4z" class="V"></path><path d="M237 423h0c0 1 0 2 1 2 1 2 0 7 0 9l-1 1v3c-1 1-1 3-3 4 0-3 1-5 2-8 0-3 0-6 1-9v-2z" class="G"></path><path d="M237 483l1-3c-1 3 0 6 1 10 0 1 1 3 1 4h-1c0-1-1-2-1-4-2-2-3-3-4-6h0v-3h2v1l1 1z" class="K"></path><path d="M234 481h2v1l1 1v2h0c-1-1-1-1-3-1h0v-3z" class="C"></path><path d="M225 446c1-1 2-2 2-3 1-3 3-5 3-8 1 2 1 3 1 5-1 2-2 4-4 6 0 1-1 2-2 3 0 1 0 2-1 2v2s-1-1 0-2c-1 1-1 2-2 2 0 1-2 3-3 3h-1c3-3 5-6 7-10z" class="S"></path><path d="M230 474h0c1 0 1 0 2 1l-1 1 1 1 1 1v2c0 1-1 2-1 2h-1v2l-1 1-1-1c-1-1-1-2-2-3 1-2 1-3 2-4 0-1 0-2 1-3z" class="b"></path><path d="M231 476l1 1 1 1v2c0 1-1 2-1 2-1-2-1-4-1-6z" class="N"></path><path d="M227 481c1-2 1-3 2-4 0-1 0-2 1-3-1 2 0 4 0 6 0 1 1 3 0 4h-1c-1-1-1-2-2-3z" class="P"></path><path d="M229 447l2 2c-1 1-3 3-3 4v1c-1 2-3 3-4 5-2 3-3 5-4 8 1-4 2-8 4-11h1v-3c1-1 3-4 4-6h0z" class="L"></path><path d="M231 457c1 1 1 1 1 2-1 2-1 3-2 4-2 1-2 3-3 4v-3h-1l-2 6h-1c0-2 1-4 1-6 0-1 1-2 2-3 1 0 4-3 5-4z" class="B"></path><path d="M237 438v-3c0 2 1 3 1 5 1 0 1 0 1 1s-1 2-1 3v-1c-1 1-2 3-3 4l-2 2c-2 1-3 3-5 4 0-1 2-3 3-4l2-4 1-3c2-1 2-3 3-4z" class="Q"></path><path d="M237 438v-3c0 2 1 3 1 5 1 0 1 0 1 1s-1 2-1 3v-1-1c-1-1-1-3-1-4z" class="E"></path><path d="M238 443v1h0v1c0 2-1 4-1 5-1 1-1 2-1 3-2 1-3 2-5 4-1 1-4 4-5 4l2-2v-4l1-1h-1v-1c2-1 3-3 5-4l2-2c1-1 2-3 3-4z" class="V"></path><path d="M232 454c0-2 0-4 2-5l1 1c0 2-2 3-3 4z" class="F"></path><path d="M238 444v1c0 2-1 4-1 5-1 1-1 2-1 3-2 1-3 2-5 4-1 1-4 4-5 4l2-2c1-2 2-4 4-5 1-1 3-2 3-4l3-6z" class="c"></path><defs><linearGradient id="AC" x1="251.059" y1="391.233" x2="256.957" y2="393.741" xlink:href="#B"><stop offset="0" stop-color="#d4d5d5"></stop><stop offset="1" stop-color="#faf8fa"></stop></linearGradient></defs><path fill="url(#AC)" d="M255 381h1l1 2c0 2-1 6-1 8 0 4 0 7-1 10-2 4-5 8-7 12 0-3 2-5 3-7 1-3 1-5 2-8v-8c1-3 1-6 2-9z"></path><path d="M225 434l1-1c1 2 1 4 1 6-1 2-2 5-2 7-2 4-4 7-7 10l-3 3 3-4v-4-1l3-3h0c1-1 1-2 1-4 0-1 0-1-1-1h0c1-2 1-3 3-4h0 0l1-4z" class="a"></path><path d="M221 442h0c1-2 1-3 3-4h0c-1 7-1 12-6 17v-4-1l3-3h0c1-1 1-2 1-4 0-1 0-1-1-1z" class="T"></path><path d="M250 468c0-1 1-3 1-4h0v-1-1l1-1c-1-1 0-2 0-3v-1-1-2c0 2-1 5-1 6v1 1c-1 2-2 2-4 3-2 2-2 6-4 7h0c0-1 2-5 2-6 2-1 3-4 4-6 0-1 1-3 1-4 1-1 0-1 1-2v-6c1-1 2-2 2-3v-1c1-3 1-8 2-11 1-2 2-3 3-4v1c0 2-2 3-3 5v3c1 2 0 3 1 5v5 1c0 3-1 6-1 9h0l1-1h0c0 1 0 2-1 3 0 1-2 2-2 3-1 2-1 4-3 5h0z" class="N"></path><path d="M255 438c1 2 0 3 1 5v5c-1-3-1-6-1-10z" class="O"></path><path d="M252 367v-3l1 2s0 1 1 2v2h1 0l1-1v1l1-1v4 6 4l-1-2h-1c-1 3-1 6-2 9v8c0-1-1-2-1-3 1-1 1-2 0-4 0 2 0 4-2 6l-2 5c0-4 1-9 2-13 0-1 0-1-2-2l1-3c1-1 1-2 1-3l-1-1c1 0 1-1 1-2v-2l1-1v-2c0-2 0-4 1-6z" class="G"></path><path d="M256 370l1-1v4 6 4l-1-2h-1l1-11z" class="j"></path><path d="M250 381l1-3v-1l1-1v-3h0l1-1v2h1c0 8-2 16-4 23l-2 5c0-4 1-9 2-13 0-1 0-1-2-2l1-3c1-1 1-2 1-3z" class="e"></path><path d="M250 381l1-3v-1l1-1v-3h0l1-1v2l-3 15c0-1 0-1-2-2l1-3c1-1 1-2 1-3z" class="D"></path><path d="M227 413l1 2h2c1 2 2 5 2 7l1 5-3 8c0 3-2 5-3 8 0 1-1 2-2 3 0-2 1-5 2-7 0-2 0-4-1-6l-1 1-1-2c1-2 1-4 1-5l-1-2v-2c1-1 1-2 1-3h-1c1-1 1-2 1-2v-1h0 1l1-1v-1-2z" class="Q"></path><path d="M225 427v-3l1-2c1 2 1 6 1 7 1 2 1 3 1 5h0c0 2-1 4-1 5 0-2 0-4-1-6l-1 1-1-2c1-2 1-4 1-5z" class="J"></path><path d="M232 422v4c-1 0-2-1-3-1v1c0 1 0 1 1 1v5h-1l-1-2c0-2-1-6 0-8 1-1 1-2 1-3h0c-1-1-2-3-2-4h1 2c1 2 2 5 2 7z" class="Z"></path><path d="M243 484c1 2 1 4 1 6 0 1 1 3 1 5l1-4 1 1h0l1-2v4l1 6c3 5 6 10 8 15v2c1 0 1 1 1 1v2 1c1 0 1 1 1 2-3-2-3-5-5-8v2c1 1 2 4 2 5-1-1-2-2-2-4-2-2-4-5-6-7l-6-6v-2h0l2-1 7 7h0l-3-4c-4-6-6-13-5-21z" class="e"></path><path d="M242 503h0c2 1 3 2 4 3 2 2 4 5 6 7l2 2v2c1 1 2 4 2 5-1-1-2-2-2-4-2-2-4-5-6-7l-6-6v-2z" class="p"></path><path d="M246 491l1 1h0l1-2v4l1 6c3 5 6 10 8 15-2-2-3-4-4-6-3-5-6-9-8-14l1-4z" class="T"></path><path d="M227 393l1-1h0s1 0 0 1v3l1 1c1 1 1 2 1 2 1 2 1 5 2 6s1 2 2 3l6 6c2 2 3 4 4 6l1 2c0 1 1 1 1 2h-1c0 2 1 4 1 6l-1-1h0 0c-1-2-2-3-3-4l-2-1v3l-1 2v1 3c0 2 0 5-1 7 0-2-1-3-1-5l1-1c0-2 1-7 0-9-1 0-1-1-1-2h0v2c-1 0-1-1-2-2v-1h-1c0 2-1 3-1 5l-1-5c0-2-1-5-2-7h-2l-1-2h0c-1 0-1 1-1 1l-1-1c1 0 1-1 3-2v-1c-1 0-1 0-2-1 0 0-1 0-1-1s0-1-1-1v-2-1c0-1 1-1 2-2 0 2 1 3 1 4 1-4-1-7-1-11l1 1v-3z" class="E"></path><path d="M228 402c0-1 0-1 1-2l1 3v1c1 1 1 3 1 4h0c-2-2-2-4-3-6z" class="L"></path><path d="M226 395l1 1v-3c0 3 0 6 1 9 1 2 1 4 3 6v1c-2 0-3-2-4-3 1-4-1-7-1-11z" class="K"></path><path d="M231 410c3 1 5 4 6 6 1 1 2 2 2 3h1c-1 0-2-1-3-2 0-1-1-2-2-2v-1l-1 1 8 10-2-1-7-9c0-2-1-4-2-5z" class="D"></path><path d="M224 405v-1c0-1 1-1 2-2 0 2 1 3 1 4 1 1 2 3 4 3v1c1 1 2 3 2 5l-2-2h0l-3-3c-1 0-1 0-2-1 0 0-1 0-1-1s0-1-1-1v-2z" class="Y"></path><path d="M231 413l2 2 7 9v3l-1 2v1 3c0 2 0 5-1 7 0-2-1-3-1-5l1-1c0-2 1-7 0-9-1 0-1-1-1-2h0c-1-2-2-4-3-5-1-2-2-3-3-5z" class="M"></path><path d="M228 410l3 3h0c1 2 2 3 3 5 1 1 2 3 3 5v2c-1 0-1-1-2-2v-1h-1c0 2-1 3-1 5l-1-5c0-2-1-5-2-7h-2l-1-2h0c-1 0-1 1-1 1l-1-1c1 0 1-1 3-2v-1z" class="S"></path><path d="M225 413c1 0 1-1 3-2 1 1 1 3 2 4h-2l-1-2h0c-1 0-1 1-1 1l-1-1z" class="E"></path><path d="M240 424l2 1c1 1 2 2 3 4h0 0l1 1c1 3 3 8 2 12l-1 1v1h-1v-2c-1 4-1 8-3 12 0 1-1 2-1 2h-1 0l-1 1c-1 2-3 4-5 7 0 1-1 2-1 3-1 2-2 3-3 5v1h1v1 1c-1-1-1-1-2-1h0c-1 1-1 2-1 3-1 1-1 2-2 4 0-2-1-3-1-5h0 1c1 0 1-1 1-2 1-1 1-2 1-3l1-4h-1c0-2 0-3 1-4s1-2 2-4c0-1 0-1-1-2 2-2 3-3 5-4 0-1 0-2 1-3 0-1 1-3 1-5v-1h0c0-1 1-2 1-3s0-1-1-1c1-2 1-5 1-7v-3-1l1-2v-3z" class="N"></path><path d="M240 424l2 1c1 1 2 2 3 4h0 0c-1 0-1 0-2 1-1-1 0-1-1-2v2h-1v-1s0-1-1-2v-3z" class="G"></path><path d="M243 431c1 3 1 7 1 10 0 2-1 4-1 5l-1-3v-5c1-2 1-4 1-7z" class="C"></path><path d="M238 456v1 1l-2 2v1c-1 1-1 1-1 2l-3 5-2 4h-1v-1l1-4 3-6c2-1 4-3 5-5z" class="O"></path><path d="M240 427c1 1 1 2 1 2v1h1l1 1c0 3 0 5-1 7v5l1 3-2 4c0 2-1 3-2 5l-1 1c-1 2-3 4-5 5l-3 6h-1c0-2 0-3 1-4s1-2 2-4c0-1 0-1-1-2 2-2 3-3 5-4 0-1 0-2 1-3 0-1 1-3 1-5v-1h0c0-1 1-2 1-3s0-1-1-1c1-2 1-5 1-7v-3-1l1-2z" class="L"></path><path d="M239 430h1v3 4c0-2-1-3-1-4v-3z" class="E"></path><path d="M236 453h0l-1 1c0 2-2 4-3 5 0-1 0-1-1-2 2-2 3-3 5-4z" class="V"></path><path d="M233 461c2-4 4-8 8-11 0 2-1 3-2 5l-1 1c-1 2-3 4-5 5z" class="G"></path><path d="M240 427c1 1 1 2 1 2v1h1l1 1c0 3 0 5-1 7v5c-1 2-2 4-2 5l-1 1-1-1c1-1 2-2 2-3s0-2 1-3v-1-2-2c0-1-1-3-1-4v-3h-1v-1l1-2z" class="Q"></path><path d="M237 405c0 1 1 1 1 2v-1h1c0 1 0 2 1 3 0 1 2 3 3 4l5 12 1 4 2 9c0 5-1 10-2 15l-3 6-6 12-1 3h0v2l-1 4-1 3-1-1v-1h-2v3l-1-1c1-2 0-4 0-6v1l-1-1-1-1 1-1v-1-1h-1v-1c1-2 2-3 3-5 0-1 1-2 1-3 2-3 4-5 5-7l1-1h0 1s1-1 1-2c2-4 2-8 3-12v2h1v-1l1-1c1-4-1-9-2-12 0-2-1-4-1-6h1c0-1-1-1-1-2l-1-2c-1-2-2-4-4-6l-6-6 2-1 1-2z" class="X"></path><path d="M237 475c0-2 1-3 1-4h0c1-1 1-2 2-2v2l-1 3h0l-2 1z" class="Z"></path><path d="M235 471h1c1-2 2-5 4-7h0c0 1-1 2-1 3-2 3-3 7-3 10v1s0 1-1 1c-1-1 0-1 0-2h-1c0-2 0-4 1-6z" class="R"></path><path d="M237 475l2-1v2l-1 4-1 3-1-1v-1h-2v-4h1c0 1-1 1 0 2 1 0 1-1 1-1l1-3z" class="L"></path><path d="M241 456h1s1-1 1-2c2-4 2-8 3-12v2h1v-1l1-1c0 6-2 15-7 19h-1 0c0-2 1-3 1-5z" class="G"></path><path d="M237 405c0 1 1 1 1 2 2 5 6 9 7 14 1 2 2 4 2 5 2 6 4 13 2 20 0 1 0 2-1 3h0l1-4c0-4 0-9-1-13-1-2-1-4-2-7v-1c0-1-1-1-1-2l-1-2c-1-2-2-4-4-6l-6-6 2-1 1-2z" class="B"></path><path d="M232 475v-1-1h-1v-1c1-2 2-3 3-5 0-1 1-2 1-3 2-3 4-5 5-7l1-1h0c0 2-1 3-1 5h0c-2 3-4 7-5 10-1 2-1 4-1 6v4 3l-1-1c1-2 0-4 0-6v1l-1-1-1-1 1-1z" class="P"></path><path d="M246 307l6-6c2 1 2 2 3 4 1 1 1 2 2 3v2c1 0 1 1 1 2 1 1 1 1 1 2s1 2 1 2v1s1 0 1 1c0 2 1 4 2 7v7c1 1 1 3 1 5h0c0 3-1 5-2 7 0 3-2 5-2 7-1 2-2 5-3 7l-1 1c-1 3-1 6-1 8v1h1v1l-1 1h0-1v-2c-1-1-1-2-1-2l-1-2v3c-1 2-1 4-1 6v2l-1 1v2c0 1 0 2-1 2l1 1c0 1 0 2-1 3l-1 3-3 6c-1 1-2 2-3 4h-1v5h-1c0 2 0 2 1 4 1 1 1 3 2 5v2c-1-1-3-3-3-4-1-1-1-2-1-3h-1v1c0-1-1-1-1-2l-1 2-2 1c-1-1-1-2-2-3s-1-4-2-6c0 0 0-1-1-2l-1-1v-3c1-1 0-1 0-1h0l-1 1v3l-1-1c0 4 2 7 1 11 0-1-1-2-1-4-1 1-2 1-2 2v1 2c-1 0-1-1-2-2h0c0-2-1-5-2-7 0-3 1-5 1-8l-1-1v-4h0l-1-1c1-1 1-2 2-3l1-3c-1-2 0-3 0-5h0l1-1c-4-2-1-9-3-12l-1-4 1-1h0c0-1 0-2-1-3v-1-2c-1-2 0-2-1-4h0l1-1s-1-1-2-1c-2-3-4-5-7-6v1l-3-1-2-1-2-1-3-2c-2-1-3 0-5-1-1 0-1-1-3-1h-1-1c-8-2-16-2-24 1l-6 3c-3 1-5 3-8 5l-5 5-1-1c0 1-1 1-2 1l1-2 7-8c1-1 3-2 4-3 2-2 5-4 7-5l6-2h5c2-1 4-2 6-2l4-1c-1-1-1-1-2-1 3-2 4-5 6-8h0 1c11 6 26 8 37 4 5-1 8-2 12-4 1 0 1 0 1 1s0 2-1 3v2l-2 2c-1 1-2 1-2 2v1 1l1 1c1-1 1-1 2-1v-2s1-1 2-1h0l1-1c4-2 5-8 6-12v-2z" class="Y"></path><path d="M231 386l3-3 1 1-6 8-1-1c1-2 2-3 3-5h0z" class="E"></path><path d="M234 401c1 1 1 2 2 3v1h1l-1 2-2 1c-1-1-1-2-2-3 1 0 2 0 2-1v-1-2z" class="P"></path><path d="M236 385h0v2c-2 3-3 7-4 10 0 0 0-1-1-1v-1c0-2 2-4 2-7 1 0 0 0 1-1 0-1 1-2 1-2h1z" class="O"></path><path d="M237 375v3h1 0c0-1 0-2 1-2l2-1c-1 4-3 7-5 10h-1c1-2 2-3 2-5v1l-1-1c1-1 1-2 1-3v-1-1z" class="J"></path><path d="M228 386l1 1h1l1-1h0 0c-1 2-2 3-3 5l1 1v1c0 1 1 1 1 1v5s0-1-1-2l-1-1v-3c1-1 0-1 0-1h0l-1 1v3l-1-1c0-2 1-5 2-7v-2z" class="S"></path><path d="M250 367c1-3 1-5 2-8 0-2 2-4 2-7v-1l1-1v-2h1v-2c1 0 1-1 1-1h0c0 2-2 5-2 7v1-1h1l-4 12v3h-2z" class="O"></path><path d="M234 401v-2c0-3 2-9 4-12l1 1c-1 0-1 1-1 2v4 2l1-1c-1 1-3 3-3 4v2c1 2 0 2 0 4v-1c-1-1-1-2-2-3z" class="K"></path><path d="M184 322c8-3 18-2 26 0v1c-2 0-3-1-5-1-3 0-5 1-8 1-5 0-10-1-15 0h-2l4-1z" class="d"></path><path d="M250 335c0-1 0-3 1-4v-3c0-1 1-1 1-1 0-1 0-1 1-2l1 1v2 6c-1 3-1 6-2 9v3c-1 2-1 4-3 5l-1 1h0l-1 1c0 1 0 2-1 2-1-1-1-1 0-2 2-4 2-7 3-12v-1c1-2 1-3 1-5z" class="J"></path><path d="M252 343v3c-1 2-1 4-3 5l-1 1h0l4-9z" class="K"></path><path d="M250 335c0-1 0-3 1-4v-3c0-1 1-1 1-1 0-1 0-1 1-2l1 1v2 6-3l-1-1c-2 3-1 7-2 10h0c0-2 0-3-1-5z" class="E"></path><path d="M210 322c2 1 4 2 6 2 4 2 7 3 10 5v1h0c2 2 5 4 7 6-2-1-4-2-6-4h0c-1-1-1-1-2-1h-1 0c-1 0-2-1-2-1-1 1-1 1-1 2h0c-4-2-8-3-12-5v-1c-2-1-5-1-6-2h-3-3v-1c3 0 5-1 8-1 2 0 3 1 5 1v-1z" class="l"></path><path d="M210 322c2 1 4 2 6 2 4 2 7 3 10 5v1h0c2 2 5 4 7 6-2-1-4-2-6-4h0c-1-1-1-1-2-1-4-3-10-5-15-7-2 0-4-1-5-1-2 0-4 1-5 1h-3v-1c3 0 5-1 8-1 2 0 3 1 5 1v-1z" class="Q"></path><path d="M250 367h2c-1 2-1 4-1 6v2l-1 1v2c0 1 0 2-1 2l1 1c0 1 0 2-1 3l-1 3-3 6c-1 1-2 2-3 4h-1v5h-1c0 2 0 2 1 4 1 1 1 3 2 5v2c-1-1-3-3-3-4-1-1-1-2-1-3h-1v1c0-1-1-1-1-2h-1c0-2 1-2 0-4v-2c0-1 2-3 3-4 5-6 7-13 10-20l1-8z" class="E"></path><path d="M241 397v5h-1l-1 1v2c-1-1-1-2-1-3 0-2 2-3 3-5z" class="D"></path><path d="M250 367h2c-1 2-1 4-1 6v2l-1 1v2c0 1 0 2-1 2 0 1 0 2-1 3 0 1 0 1-1 2 0 3-3 5-4 7 0 1 0 1-1 1 0-1 1-2 2-3 1-2 3-5 3-7 1-1 1-2 1-3h1v-2-1-1-1l1-8z" class="B"></path><path d="M238 313c1 0 1 0 1 1s0 2-1 3v2l-2 2c-1 1-2 1-2 2v1 1l1 1c1-1 1-1 2-1h2 1c1 2 2 3 3 5v2l1 4v5c-1 1-1 1-2 1l-3-8c-2-1-4-3-5-4-1-2-3-3-4-5 0 0-1 0-1-1 0 0 1-1 1-2s0-2 1-2v-1l-1-1h-1c-1 0-2 0-3-1 5-1 8-2 12-4z" class="H"></path><path d="M237 325h2 1c1 2 2 3 3 5v2l1 4v5c-1 1-1 1-2 1l-3-8c0-2-1-3-2-5h0l-3-3v-1l1 1c1-1 1-1 2-1z" class="F"></path><path d="M243 330v2l1 4c-1 0-1 1-1 1-1-1-1-3-2-4v-1c1 0 1-1 2-2z" class="M"></path><path d="M237 325h2 1c1 2 2 3 3 5-1 1-1 2-2 2v1 1c-1-2-1-3-2-5h-2 0l-3-3v-1l1 1c1-1 1-1 2-1z" class="C"></path><path d="M240 325c1 2 2 3 3 5-1 1-1 2-2 2 0-1-2-6-1-7z" class="J"></path><path d="M226 330c4 2 9 5 12 9s6 10 6 15c2 6 1 11-1 16 0 1-1 5-2 5l-2 1c-1 0-1 1-1 2h0-1v-3l1-1c0-3 1-5 1-7 0-5-1-10-3-15l-1-2-1-3c-1-3-3-5-5-7h3l-3-2c-2-2-3-3-5-3l-3-3h0c0-1 0-1 1-2 0 0 1 1 2 1h0 1c1 0 1 0 2 1h0c2 2 4 3 6 4-2-2-5-4-7-6h0z" class="d"></path><path d="M244 354c2 6 1 11-1 16h0c-1 0-1 0 0-1 1-5 0-9 0-14h1 0v-1z" class="n"></path><path d="M238 339c3 4 6 10 6 15v1h0-1c-1-3-2-5-3-8s-2-5-4-7l2-1z" class="h"></path><path d="M236 344l1-1 4 10c1 6 2 14 0 19l-2 2c0-2 2-6 2-9-1-4-1-10-3-14 0-2-1-4-2-7z" class="q"></path><path d="M236 352h0c1-2-1-4-1-6l-1-2v-2c1 0 2 1 2 2 1 3 2 5 2 7 2 4 2 10 3 14 0 3-2 7-2 9v1h-1v-1c0-3 1-5 1-7 0-5-1-10-3-15z" class="k"></path><path d="M226 330c4 2 9 5 12 9l-2 1-2-2h0c0 1 2 4 3 5l-1 1c0-1-1-2-2-2v2l1 2c0 2 2 4 1 6h0l-1-2-1-3c-1-3-3-5-5-7h3l-3-2c-2-2-3-3-5-3l-3-3h0c0-1 0-1 1-2 0 0 1 1 2 1h0 1c1 0 1 0 2 1h0c2 2 4 3 6 4-2-2-5-4-7-6h0z" class="l"></path><path d="M225 331c1 0 1 0 2 1h0v1c2 2 6 5 7 7v1l-1-1-3-3-1 1c-2-2-3-3-5-3l-3-3h0c0-1 0-1 1-2 0 0 1 1 2 1h0 1z" class="W"></path><path d="M228 343l2 2h0 1c1 1 2 2 3 2l1 3 1 2c2 5 3 10 3 15 0 2-1 4-1 7l-1 1v1 1c0 1 0 2-1 3l1 1-2 3-1-1-3 3h0l-1 1h-1l-1-1v2c-1 2-2 5-2 7 0 4 2 7 1 11 0-1-1-2-1-4-1 1-2 1-2 2v1 2c-1 0-1-1-2-2h0c0-2-1-5-2-7 0-3 1-5 1-8l-1-1v-4h0l-1-1c1-1 1-2 2-3l1-3c-1-2 0-3 0-5h0l1-1c1-2 2-3 3-5l1-1 1-3c0-1 0-1-1-1 0-1 0-2 1-3 0-4-2-8-3-11h2l2 3c0-2 0-3-1-4l1-1c0-1-1-2-1-2v-1z" class="Y"></path><path d="M234 383c1-3 1-5 3-7v1c0 1 0 2-1 3l1 1-2 3-1-1z" class="F"></path><path d="M230 345h1c1 1 2 2 3 2l1 3h-1c-1 0-2-1-2-2h0-1c-1 0-1-2-1-3z" class="V"></path><path d="M227 366c1 0 1-1 2-2h1l1 1c-3 4-5 6-9 8h0l1-1c1-2 2-3 3-5l1-1z" class="o"></path><path d="M230 379c1-1 1-2 2-3h1c0 1-2 3-2 4l-3 6v2c-1-1-2-1-3-1 1-2 1-3 2-5v-1c0-1 1-3 2-3l1 1h0z" class="L"></path><path d="M227 381c0-1 1-3 2-3l1 1h0c0 2-1 4-2 5h-1c0-1 1-1 0-2v-1z" class="G"></path><path d="M221 381c0-1 1-1 1-1 1-2 1-3 2-4h1c-1 2-1 3-1 4v1h3v1c-1 2-1 3-2 5h0c0 1-1 1-1 2-1-2 0-1-2-2l1-4v-1l-1 1c0 1-1 3 0 4l-1 3-1-1v-4h0l-1-1c1-1 1-2 2-3z" class="N"></path><path d="M228 343l2 2h0c0 1 0 3 1 3 1 2 1 4 2 6 0 4-1 8-2 11l-1-1h-1c-1 1-1 2-2 2l1-3c0-1 0-1-1-1 0-1 0-2 1-3 0-4-2-8-3-11h2l2 3c0-2 0-3-1-4l1-1c0-1-1-2-1-2v-1z" class="a"></path><path d="M228 363c0-1 0-1-1-1 0-1 0-2 1-3l2 2v2h-2z" class="J"></path><path d="M228 352l1 1v-1h1v4l-1 2c0-2-1-4-1-6z" class="o"></path><g class="W"><path d="M228 343l2 2h0c0 1 0 3 1 3 1 2 1 4 2 6l-1 1c-2-2-2-5-2-7-1-1-1-1-1-2s-1-2-1-2v-1z"></path><path d="M225 348h2l2 3c-1 0-1 1-1 1 0 2 1 4 1 6 1 1 1 1 1 3l-2-2c0-4-2-8-3-11z"></path></g><path d="M222 387c-1-1 0-3 0-4l1-1v1l-1 4c2 1 1 0 2 2 0-1 1-1 1-2h0c1 0 2 0 3 1-1 2-2 5-2 7 0 4 2 7 1 11 0-1-1-2-1-4-1 1-2 1-2 2v1 2c-1 0-1-1-2-2h0c0-2-1-5-2-7 0-3 1-5 1-8l1-3z" class="F"></path><path d="M224 389c0-1 1-1 1-2 0 2 1 3 0 4-1 2-1 3-1 5 0-2 0-4-1-5l1-2z" class="I"></path><path d="M224 396c0-2 0-3 1-5v8c0 1 1 2 1 3-1 1-2 1-2 2v1l-1-1c0-1 1-1 1-1h0v-7z" class="C"></path><path d="M222 387c-1-1 0-3 0-4l1-1v1l-1 4c2 1 1 0 2 2l-1 2c0 1 0 8-1 8v-8-4z" class="B"></path><path d="M225 387c1 0 2 0 3 1-1 2-2 5-2 7 0 4 2 7 1 11 0-1-1-2-1-4 0-1-1-2-1-3v-8c1-1 0-2 0-4h0z" class="F"></path><path d="M246 307l6-6c2 1 2 2 3 4 1 1 1 2 2 3v2c1 0 1 1 1 2 1 1 1 1 1 2s1 2 1 2v1s1 0 1 1c0 2 1 4 2 7v7c1 1 1 3 1 5h0c0 3-1 5-2 7 0 3-2 5-2 7-1 2-2 5-3 7l-1 1c-1 3-1 6-1 8v1h1v1l-1 1h0-1v-2c-1-1-1-2-1-2l-1-2 4-12h-1v1-1c0-2 2-5 2-7h0v-1h1v-2c-1-1-1-1-1-3-1 0-1 0-1 1v2c0 3-3 8-4 11v1h-1c0-2 0-3 1-4v-1-1-2-3c1-3 1-6 2-9v-6-2l-1-1c-1 1-1 1-1 2 0 0-1 0-1 1v3c-1 1-1 3-1 4 0 2 0 3-1 5v1h0c-1-1-2-7-4-7 0 1 0 3-1 4v7l-2-3c1 0 1 0 2-1v-5l-1-4v-2c-1-2-2-3-3-5h-1-2v-2s1-1 2-1h0l1-1c4-2 5-8 6-12v-2z" class="N"></path><path d="M246 320h1v2h1c0 1-1 2-1 3-1 0-1-2-1-3v-2z" class="O"></path><path d="M243 326c1 1 1 2 1 4h1v-2h1c0 2-1 3-1 4h-2c1-2 0-4 0-6z" class="B"></path><path d="M240 325h0c1 0 1-1 2-1 1 1 1 1 1 2 0 2 1 4 0 6v-2c-1-2-2-3-3-5z" class="G"></path><path d="M260 317s1 0 1 1c0 2 1 4 2 7v7c1 1 1 3 1 5h0c0 3-1 5-2 7 0-1 1-2 1-4v-7l-3-16h0z" class="M"></path><path d="M258 318c-1-1-2-3-1-4h0c1 1 1 1 1 2 2 4 2 9 3 13v6c0 3 1 5 0 8s-2 6-4 9c-2 5-3 9-4 14l-1-2 4-12c1-1 3-4 2-6 0-2 1-5 1-7 1-4 1-8 0-12 0-1 0-3-1-4v-2-3z" class="E"></path><path d="M246 309l1-1c1-1 1-1 2-1 1 1 1 2 1 3v3l-2 9h-1v-2h-1s-2 1-2 2 1 2 1 3c-1 2 0 4-1 5 0-2 0-3-1-4 0-1 0-1-1-2-1 0-1 1-2 1h0-1-2v-2s1-1 2-1h0l1-1c4-2 5-8 6-12z" class="b"></path><path d="M245 332v1h1 0c0-1 1-2 0-2v-2c1 1 1 3 1 4 0 2 0 2 1 3 0-2-1-6 0-8h1c0 1 1 2 1 3 0-4 0-9 1-13 0-2 1-3 1-4v-5h1l1 2c0-1 0-2-1-4h1c1 2 1 6 2 8v3l1 9h-1v-1-2h0c-1-1 0-2-1-4-1 3-1 6-1 8v-2l-1-1c-1 1-1 1-1 2 0 0-1 0-1 1v3c-1 1-1 3-1 4 0 2 0 3-1 5v1h0c-1-1-2-7-4-7 0 1 0 3-1 4v7l-2-3c1 0 1 0 2-1v-5l-1-4h2z" class="V"></path><path d="M258 318v3 2c1 1 1 3 1 4 1 4 1 8 0 12 0 2-1 5-1 7 1 2-1 5-2 6h-1v1-1c0-2 2-5 2-7h0v-1h1v-2c-1-1-1-1-1-3-1 0-1 0-1 1v2c0 3-3 8-4 11v1h-1c0-2 0-3 1-4v-1-1-2-3c1-3 1-6 2-9v-6c0-2 0-5 1-8 1 2 0 3 1 4h0v2 1h1l-1-9h2z" class="H"></path><path d="M254 328c0-2 0-5 1-8 1 2 0 3 1 4h0v2h0v2 5 6c-1 2-1 3-2 5 0 2-1 4-2 6v-1-1-2-3c1-3 1-6 2-9v-6z" class="S"></path><path d="M182 323c5-1 10 0 15 0v1h3 3c1 1 4 1 6 2v1c4 2 8 3 12 5l3 3c2 0 3 1 5 3l3 2h-3c2 2 4 4 5 7-1 0-2-1-3-2h-1 0l-2-2v1s1 1 1 2l-1 1c1 1 1 2 1 4l-2-3h-2c1 3 3 7 3 11-1 1-1 2-1 3 1 0 1 0 1 1l-1 3-1 1c-1 2-2 3-3 5-4-2-1-9-3-12l-1-4 1-1h0c0-1 0-2-1-3v-1-2c-1-2 0-2-1-4h0l1-1s-1-1-2-1c-2-3-4-5-7-6v1l-3-1-2-1-2-1-3-2c-2-1-3 0-5-1-1 0-1-1-3-1h-1-1c-8-2-16-2-24 1l-6 3c-3 1-5 3-8 5l-5 5-1-1c0 1-1 1-2 1l1-2 7-8c1-1 3-2 4-3 2-2 5-4 7-5l6-2h5c2-1 4-2 6-2h2z" class="b"></path><path d="M179 326h11 0c2 1 4 0 5 1l-1 1h-12c-2 0-5 1-7 1v-2l3-1h1z" class="F"></path><path d="M182 323c5-1 10 0 15 0v1h3 3c1 1 4 1 6 2v1l-7-1v2l-7-1c-1-1-3 0-5-1h0-11l1-1c-1 0-1 0-2-1l4-1z" class="j"></path><path d="M190 326h12v2l-7-1c-1-1-3 0-5-1h0z" class="D"></path><path d="M182 323c5-1 10 0 15 0v1c-6 0-11 1-16 1h-1c-1 0-1 0-2-1l4-1z" class="B"></path><path d="M180 323h2l-4 1c1 1 1 1 2 1l-1 1h-1l-3 1v2c-3 0-6 1-9 3h0l-6 3c-3 1-5 3-8 5l-5 5-1-1c0 1-1 1-2 1l1-2 7-8c1-1 3-2 4-3 2-2 5-4 7-5l6-2h5c2-1 4-2 6-2z" class="e"></path><path d="M146 344c9-8 18-17 32-18l-3 1v2c-3 0-6 1-9 3h0l-6 3c-3 1-5 3-8 5l-5 5-1-1z" class="c"></path><defs><linearGradient id="AD" x1="202.393" y1="325.513" x2="219.311" y2="333.627" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#5e5e5e"></stop></linearGradient></defs><path fill="url(#AD)" d="M202 326l7 1c4 2 8 3 12 5l3 3c2 0 3 1 5 3l3 2h-3c2 2 4 4 5 7-1 0-2-1-3-2h-1 0l-2-2v1s1 1 1 2l-1 1c1 1 1 2 1 4l-2-3h-2l-3-5c-1-3-5-6-7-8h-1 0c-1-1-2-1-2-2v-1c-2-1-3-1-5-2-4-1-9-2-13-2l1-1 7 1v-2z"></path><path d="M216 333c1 0 2-1 2 0l2 1c3 2 5 5 7 6l-4-5h1c2 0 3 1 5 3l3 2h-3c2 2 4 4 5 7-1 0-2-1-3-2h-1 0l-2-2c-3-4-7-7-12-10z" class="E"></path><path d="M224 335c2 0 3 1 5 3l3 2h-3 0c-2-2-4-3-5-5z" class="J"></path><path d="M195 327l7 1c5 1 9 3 14 5 5 3 9 6 12 10v1s1 1 1 2l-1 1c1 1 1 2 1 4l-2-3h-2l-3-5c-1-3-5-6-7-8h-1 0c-1-1-2-1-2-2v-1c-2-1-3-1-5-2-4-1-9-2-13-2l1-1z" class="X"></path><path d="M214 335h2c2 2 6 3 7 6l-1 2c-1-3-5-6-7-8h-1z" class="W"></path><path d="M223 341c2 2 4 4 5 6 1 1 1 2 1 4l-2-3h-2l-3-5 1-2z" class="l"></path><path d="M207 330c2 1 3 1 5 2v1c0 1 1 1 2 2h0 1c2 2 6 5 7 8l3 5c1 3 3 7 3 11-1 1-1 2-1 3 1 0 1 0 1 1l-1 3-1 1c-1 2-2 3-3 5-4-2-1-9-3-12l-1-4 1-1h0c0-1 0-2-1-3v-1-2c-1-2 0-2-1-4h0l1-1s-1-1-2-1c-2-3-4-5-7-6v1l-3-1-2-1-2-1 1-1 1-1 2 1c0-1 1-1 1-1h0l-1-1c-1 0-1 0-2-1h2c0 1 1 1 1 1v-1s-1 0-1-1z" class="E"></path><path d="M207 330c2 1 3 1 5 2v1c0 1 1 1 2 2h0 1l-1 1c0 1 2 2 2 3l3 3h0c-3-2-5-5-9-6h0v1 1l-3-1-2-1-2-1 1-1 1-1 2 1c0-1 1-1 1-1h0l-1-1c-1 0-1 0-2-1h2c0 1 1 1 1 1v-1s-1 0-1-1z" class="P"></path><path d="M204 334c2 1 4 2 6 2v1 1l-3-1-2-1-2-1 1-1z" class="Y"></path><path d="M219 344l1 1c1 1 2 3 3 5s2 5 2 7c1 3 1 7 1 10-1 2-2 3-3 5-4-2-1-9-3-12l-1-4 1-1h0c0-1 0-2-1-3v-1-2c-1-2 0-2-1-4h0l1-1z" class="b"></path><path d="M220 345c1 1 2 3 3 5s2 5 2 7c-2-1-2-4-4-6 0-1-1-1 0-2h0v-1c0-1-1-2-1-3z" class="T"></path><defs><linearGradient id="AE" x1="249.455" y1="497.233" x2="273.542" y2="497.633" xlink:href="#B"><stop offset="0" stop-color="#050505"></stop><stop offset="1" stop-color="#272627"></stop></linearGradient></defs><path fill="url(#AE)" d="M264 367h1c0 1 0 2-1 3v5h1v-2-1c1 1 0 2 1 3v6l4 18v3h0 0c0 3-1 6 0 9 1-3 1-6 1-8h1v6c0 2 0 3-1 4 0 1 0 1-1 2s-2 3-3 5c0 1-1 3-1 4-1 1-2 2-2 4 1 0 2 1 2 1 0 1-1 3 1 3v-3l1-1v-1h1 1v-1l1 1 2-2c2 1 3 1 5 1v19 9-1l-1 1-1 5c1 0 1 0 1 1 1 2 1 5 0 6 0 1-1 2-1 3s-1 2-1 3l1 1 1-2h0c1 1 0 2 0 3 0 4 0 7 1 10l-1 35c-2 26-5 51-25 71-7 7-15 14-23 18h-2v1-1h0l7-6c3-2 7-6 10-9 2-2 5-7 6-10l-1-1c1-1 2-4 2-6 3-12 3-26-2-38 0 0-1-2-1-3-2-2-3-5-4-8-1 0-1-1-2-1l1-1v-1c-1-1-2-1-3-2-2-4-6-7-9-10h0-1 0c-2 0-3 0-4-1l-5-5c0-1-2-1-3-2h2c1 0 2-1 3-1 1 1 2 1 3 2 6 3 12 9 18 14 1 3 3 5 5 7v-1s0-1-1-1v-2 1l1-1c0 1 1 1 1 2 0 0 1 1 1 2 1 1 1 2 1 3l2 3c0 2 1 3 1 4 1 2 1 3 1 5 1 1 0 3 0 4h1v-5h0c1-1 1-2 1-3l1 8c0-2 0-3 1-5 1 1 1 3 1 5v-7h0c-1-3-1-5-2-7 0-3-2-6-3-9 0-1-1-4-2-5v-2c2 3 2 6 5 8 0-1 0-2-1-2v-1-2s0-1-1-1v-2c-2-5-5-10-8-15l-1-6v-4l-1 2h0l-1-1-1 4c0-2-1-4-1-5 0-2 0-4-1-6 0-1 1-2 1-4l1-1h0c2-3 2-7 5-11h0c2-1 2-3 3-5 0-1 2-2 2-3 1-1 1-2 1-3h0l-1 1h0c0-3 1-6 1-9v-1-5c-1-2 0-3-1-5v-3c1-2 3-3 3-5 2-4 4-7 5-10s1-7 2-10c-1-1 0-3 0-4l-1-10-1-10c0-2 1-5 1-7s-1-4-1-6 1-4 1-6z"></path><path d="M266 474v4h1l-3 6c0-2 0-3 1-4 0-2 1-4 1-6z" class="b"></path><path d="M266 534v9c0 2 0 4 2 6l-2 4h-1v-4l1-15z" class="o"></path><path d="M270 505h0c-1 0-1-1-1-1-2-5 0-14 1-18 1-2 1-4 3-5-3 8-4 16-3 24z" class="H"></path><path d="M267 478c1-3 2-5 3-7 2-2 2-4 3-6h1l-6 15-3 6h-1v-2l3-6z" class="G"></path><path d="M249 484l2 2 1-1c-1 4-1 7-1 11l-1-2v1 2c-1 1-1 2-1 3l-1-6v-4c1-1 1-4 1-6z" class="e"></path><path d="M249 484l2 2c-1 2-1 6-3 8v-4c1-1 1-4 1-6z" class="H"></path><path d="M267 447v-10l1 7v5h0c1 0 1 1 1 2-1 6-2 13-5 19 0-2 1-5 1-7 1-6 2-11 2-16z" class="k"></path><path d="M276 459c1 0 1 0 1 1 1 2 1 5 0 6 0 1-1 2-1 3s-1 2-1 3c-1 2-2 3-4 4-1 1-1 2-2 3l-1 1 6-15c0-2 2-4 2-6z" class="g"></path><path d="M270 427v-1l1 1v1h0 1c0 1-1 2-1 3v2 1c0 2 0 3 1 5h0v1c1 0 1 0 1 1 1 0 0 1 0 2h1c0 1-1 2 0 3v2 3c-1 2-1 3-1 5l-1 2v2h0l-3 8h-1c0-1 0-2 1-2 1-1 1-4 2-6v-2h1c0-1-1-2 0-2v-6c1-2 1-6 1-8-1 1 0 2-1 3l-2-2v3l-1 5c0-1 0-2-1-2h0v-5c1-1 1-3 1-5v-7c0-2 1-3 1-5z" class="N"></path><path d="M269 432l1 1v13l-1 5c0-1 0-2-1-2h0v-5c1-1 1-3 1-5v-7z" class="c"></path><path d="M249 500c0-1 0-2 1-3v-2-1l1 2c1 4 3 7 5 11 3 5 5 10 6 15s1 10 1 15l-1-1c0-5-2-9-3-13 0-1 0-2-1-2v-1-2s0-1-1-1v-2c-2-5-5-10-8-15z" class="q"></path><path d="M266 534c-1-2 0-5-1-7 0-6-2-12-4-18-2-5-4-11-5-16 2 3 3 7 4 11 1 0 2 1 2 1 1 1 2 3 2 5h0l3 8c1 3 3 5 3 8 0 1 1 3 0 4h0c-1 1-1 2-1 4l1 7c-2 2-2 5-2 8-2-2-2-4-2-6v-9z" class="r"></path><path d="M260 504c1 0 2 1 2 1 1 1 2 3 2 5h0v3l-1 1-3-10z" class="S"></path><path d="M268 534h1l1 7c-2 2-2 5-2 8-2-2-2-4-2-6h1c2-2 1-6 1-9z" class="j"></path><defs><linearGradient id="AF" x1="265.659" y1="527.499" x2="269.526" y2="523.583" xlink:href="#B"><stop offset="0" stop-color="#17171c"></stop><stop offset="1" stop-color="#383737"></stop></linearGradient></defs><path fill="url(#AF)" d="M264 510l3 8c1 3 3 5 3 8 0 1 1 3 0 4h0c-1 1-1 2-1 4h-1c0-7-3-13-4-20h-1l1-1v-3z"></path><defs><linearGradient id="AG" x1="264.2" y1="452.184" x2="278.831" y2="451.228" xlink:href="#B"><stop offset="0" stop-color="#141415"></stop><stop offset="1" stop-color="#3c3b3b"></stop></linearGradient></defs><path fill="url(#AG)" d="M271 427l2-2c2 1 3 1 5 1v19 9-1l-1 1-1 5c0 2-2 4-2 6h-1c-1 2-1 4-3 6-1 2-2 4-3 7h-1v-4c0-1 0 0 1-1l2-5c1-2 2-5 3-8h0v-2l1-2c0-2 0-3 1-5v-3-2c-1-1 0-2 0-3h-1c0-1 1-2 0-2 0-1 0-1-1-1v-1h0c-1-2-1-3-1-5v-1-2c0-1 1-2 1-3h-1 0v-1z"></path><path d="M272 428v-1c1 1 1 2 1 3v1 2-2c1 1 1 2 0 3v1 2c0 1-1 1-1 1v2-1h0c-1-2-1-3-1-5v-1-2c0-1 1-2 1-3z" class="H"></path><path d="M273 465l1-1c1-5 2-11 2-16v3h1v3l-1 5c0 2-2 4-2 6h-1z" class="F"></path><defs><linearGradient id="AH" x1="250.161" y1="414.093" x2="274.839" y2="447.483" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#AH)" d="M264 367h1c0 1 0 2-1 3v5h1v-2-1c1 1 0 2 1 3v6l4 18v3h0 0c0 3-1 6 0 9 1-3 1-6 1-8h1v6c0 2 0 3-1 4 0 1 0 1-1 2s-2 3-3 5c0 1-1 3-1 4-1 1-2 2-2 4 1 0 2 1 2 1 0 1-1 3 1 3v-3l1-1v-1h1 1c0 2-1 3-1 5v7c0 2 0 4-1 5l-1-7v10c0 5-1 10-2 16v-1c0-2-1-3 0-5-1 1-1 2-1 3s-1 1-1 2-2 4-2 5c-2 5-6 9-9 13-1 1-1 3-2 4h1c1 0 3-3 4-5l1 1c-1 1-3 4-3 5h-1l-1 1-2-2c0 2 0 5-1 6l-1 2h0l-1-1-1 4c0-2-1-4-1-5 0-2 0-4-1-6 0-1 1-2 1-4l1-1h0c2-3 2-7 5-11h0c2-1 2-3 3-5 0-1 2-2 2-3 1-1 1-2 1-3h0l-1 1h0c0-3 1-6 1-9v-1-5c-1-2 0-3-1-5v-3c1-2 3-3 3-5 2-4 4-7 5-10s1-7 2-10c-1-1 0-3 0-4l-1-10-1-10c0-2 1-5 1-7s-1-4-1-6 1-4 1-6z"></path><path d="M263 433c-1-1-1-2-1-3l1-1c0 2 1 3 0 5v-1z" class="g"></path><path d="M263 433v1c0 1 1 2 1 3l-1 2v-6zm-6 6c2-3 2-6 4-9l-1 11-1-3c-1 0-1 0-2 1z" class="B"></path><path d="M264 437v9c0 2 0 5-1 6v-3c-1-3-1-6 0-9v-1l1-2z" class="I"></path><path d="M268 428v-1h1 1c0 2-1 3-1 5v7c0 2 0 4-1 5l-1-7v10c-1-2 0-4-1-5v-2l-2-12c1 0 2 1 2 1 0 1-1 3 1 3v-3l1-1z" class="N"></path><path d="M268 428v-1h1 1c0 2-1 3-1 5v7c-2-3 0-7-2-10l1-1z" class="U"></path><path d="M257 439c1-1 1-1 2-1l1 3c-1 1-1 3-1 5 0 3-1 6-1 8h-1 0v-1h-1v1 3l-1 1h0c0-3 1-6 1-9v-1-5l1-4z" class="C"></path><path d="M258 458h0c0-1 1-1 1-2v-1c1-1 1-2 1-2v-2l1-1h0c0-1 0-1 1-2 0-1-1-3 0-4v-3l1-1c-1 3-1 6 0 9-1 2-1 4-2 6-1 4-2 7-4 11 0 0-1 1-1 2-2 0-2 3-3 4h-2c1-3 3-5 4-8l3-6z" class="H"></path><path d="M256 468c0-1 1-2 1-2 2-4 3-7 4-11 1-2 1-4 2-6v3c1 2 1 3 0 4 0 2 0 2-1 4h0c0 2-2 5-2 7h1c-2 5-6 9-9 13-1 1-1 3-2 4h1c1 0 3-3 4-5l1 1c-1 1-3 4-3 5h-1l-1 1-2-2c0-1 0-2 1-2 0-2 1-2 1-4h0l-1 2c-1-1-1-2-1-3l2-5h2c1-1 1-4 3-4z" class="D"></path><path d="M251 472h2c1-1 1-4 3-4 0 2-2 3-2 5s-1 2-2 3c0 1 0 1-1 2h-1l-1-1 2-5z" class="C"></path><defs><linearGradient id="AI" x1="256.527" y1="487.77" x2="245.786" y2="465.082" xlink:href="#B"><stop offset="0" stop-color="#030402"></stop><stop offset="1" stop-color="#272728"></stop></linearGradient></defs><path fill="url(#AI)" d="M256 457v1 2s1-1 1-2h0 1l-3 6c-1 3-3 5-4 8l-2 5c0 1 0 2 1 3l1-2h0c0 2-1 2-1 4-1 0-1 1-1 2 0 2 0 5-1 6l-1 2h0l-1-1-1 4c0-2-1-4-1-5 0-2 0-4-1-6 0-1 1-2 1-4l1-1h0c2-3 2-7 5-11h0c2-1 2-3 3-5 0-1 2-2 2-3 1-1 1-2 1-3z"></path><path d="M245 479v3c0 1 1 2 1 2v6s-1 0-1-1v-1-1c-1-2 0-5-1-7l1-1z" class="U"></path><path d="M244 480c1 2 0 5 1 7v1 1c0 1 1 1 1 1v1l-1 4c0-2-1-4-1-5 0-2 0-4-1-6 0-1 1-2 1-4z" class="D"></path><path d="M264 367h1c0 1 0 2-1 3v5h1v-2-1c1 1 0 2 1 3v6l4 18v3h0 0c-1-3-2-10-4-12 2 7 3 14 1 21 0 1-1 2-1 4v-1-4h-1c-1-1 0-3 0-4l-1-10-1-10c0-2 1-5 1-7s-1-4-1-6 1-4 1-6z" class="W"></path><path d="M266 392v8c1 4 1 7 0 10h0-1c-1-1 0-3 0-4l-1-10 1-1 1-3z" class="g"></path><path d="M264 367h1c0 1 0 2-1 3v5h1v8l1 3c-1 0-1 1-1 1 0 1 1 3 1 5l-1 3-1 1-1-10c0-2 1-5 1-7s-1-4-1-6 1-4 1-6z" class="O"></path><defs><linearGradient id="AJ" x1="273.822" y1="544.298" x2="217.267" y2="529.998" xlink:href="#B"><stop offset="0" stop-color="#050505"></stop><stop offset="1" stop-color="#29292a"></stop></linearGradient></defs><path fill="url(#AJ)" d="M276 473l1-2h0c1 1 0 2 0 3 0 4 0 7 1 10l-1 35c-2 26-5 51-25 71-7 7-15 14-23 18h-2v1-1h0l7-6c3-2 7-6 10-9 2-2 5-7 6-10l-1-1c1-1 2-4 2-6 3-12 3-26-2-38 0 0-1-2-1-3-2-2-3-5-4-8-1 0-1-1-2-1l1-1v-1c-1-1-2-1-3-2-2-4-6-7-9-10h0-1 0c-2 0-3 0-4-1l-5-5c0-1-2-1-3-2h2c1 0 2-1 3-1 1 1 2 1 3 2 6 3 12 9 18 14 1 3 3 5 5 7v-1s0-1-1-1v-2 1l1-1c0 1 1 1 1 2 0 0 1 1 1 2 1 1 1 2 1 3l2 3c0 2 1 3 1 4 1 2 1 3 1 5 1 1 0 3 0 4h1v-5h0c1-1 1-2 1-3l1 8c0-2 0-3 1-5 1 1 1 3 1 5v-7h0c-1-3-1-5-2-7 0-3-2-6-3-9 0-1-1-4-2-5v-2c2 3 2 6 5 8 1 4 3 8 3 13l1 1c1 2 0 4 0 6 0 5 0 10-1 14v1c0 2 0 4-1 5 0 5-3 9-4 14h1c1-4 3-7 4-11 0-2 1-4 1-6 0-1 0-3 1-5 0-1 1-2 1-3v-3 4h1l2-4c0-3 0-6 2-8l-1-7c0-2 0-3 1-4h0c1-1 0-3 0-4s1-3 0-4c0-3-1-5 0-8 1-2 1-7 0-9-1-8 0-16 3-24v-2l3-6z"></path><path d="M223 503c1 1 2 1 3 2l-3-1v1c3 2 6 4 8 7h0-1 0c-2 0-3 0-4-1l-5-5c0-1-2-1-3-2h2c1 0 2-1 3-1z" class="E"></path><path d="M223 503c1 1 2 1 3 2l-3-1v1h-2c0 1 1 1 1 1 1 2 3 2 4 4v1l-5-5c0-1-2-1-3-2h2c1 0 2-1 3-1z" class="V"></path><path d="M268 549c0-3 0-6 2-8 0 7-2 13-3 20l-2-2c-1-2 0-5 0-7v-3 4h1l2-4z" class="f"></path><path d="M265 553h1v4h-1v-4z" class="c"></path><path d="M258 577c1-4 3-7 4-11 0-2 1-4 1-6 0-1 0-3 1-5 0-1 1-2 1-3 0 2-1 5 0 7l2 2-3 8c-1 1-1 3-2 4 0-2 1-4 1-6 0-1 1-1 1-2h-1v2c-2 4-3 7-4 10h-1z" class="Q"></path><path d="M243 525c2 2 3 4 4 6 4 8 7 15 7 24 1 10-1 19-4 28l-1-1c1-1 2-4 2-6 3-12 3-26-2-38 0 0-1-2-1-3-2-2-3-5-4-8-1 0-1-1-2-1l1-1z" class="F"></path><path d="M254 515c2 3 2 6 5 8 1 4 3 8 3 13l1 1c1 2 0 4 0 6 0 5 0 10-1 14v1c0 2 0 4-1 5 0 5-3 9-4 14h1 1c-3 5-6 9-10 13l4-11c1 0 1-2 1-2 2-6 3-11 5-16 1-5 0-11 0-16 0-2 0-3 1-5 1 1 1 3 1 5v-7h0c-1-3-1-5-2-7 0-3-2-6-3-9 0-1-1-4-2-5v-2z" class="C"></path><path d="M259 545c0-2 0-3 1-5 1 1 1 3 1 5l-1 11c0 2 0 3-1 5 1-5 0-11 0-16z" class="f"></path><path d="M254 515c2 3 2 6 5 8 1 4 3 8 3 13l1 1c1 2 0 4 0 6 0 5 0 10-1 14v1l-1-1v-3c1-1 1-3 1-4v-13h-1v1h0c-1-3-1-5-2-7 0-3-2-6-3-9 0-1-1-4-2-5v-2z" class="E"></path><path d="M166 270c-1-2-2-5-3-8l-3-16c-3-16-5-32-13-47-2-6-5-11-9-16-2-1-3-3-5-4 0 0-2-1-2-2v-5h379v5c-9 10-16 22-21 34-7 18-10 38-13 57l-6 42c-1 8-1 15-2 23l-13-1-1-26c-1-26-8-52-27-71-14-14-35-23-54-28l-12-3-1 27v39 126 98c0 24-1 48 3 71l1 6c3 12 7 23 14 33 2 3 4 5 6 8 21 21 46 22 75 22v7H174v-7h8c29 0 56-5 77-26 13-13 20-29 24-47 3-16 3-32 4-48v-34-66-210h-12c-14 0-28 1-41 4l-12 3c-14 6-31 16-37 30s-9 28-3 43c4 11 13 18 24 23h-1c-1 1-3 0-4-1-1 0-3-1-4-2l-2-1-1-1c-2 0-3-2-4-2-2-1-4-4-6-4 1 2 4 4 6 5s4 3 6 4c3 2 6 3 9 4h0l-2 1-8-3c-14-7-24-22-29-36z" class="Y"></path><path d="M296 370v2h-1 0v-1l1-1z" class="O"></path><path d="M357 598h2v2l-1 1-1-1v-2z" class="C"></path><path d="M232 635h1c0 1 0 1 1 2h0l1 1h-2c-1-1-1-2-1-3z" class="V"></path><path d="M288 599h3 1l-1 2h-1l-2-1v-1z" class="E"></path><path d="M267 613c1 1 2 1 3 2 0-1 0-1 1-1h1v1h-2c0 1-1 1-2 1-1-1-1-1-1-3z" class="S"></path><path d="M481 225c0 2-1 4-1 6v-3l-1 1v-1-1h0l2-2z" class="V"></path><path d="M248 627v5l-2 1v-1-2h1c0-1 0-2 1-3z" class="C"></path><path d="M363 565l1 6c-1-1-2-3-3-5 0-1 1-1 2-1z" class="g"></path><path d="M290 502h1l1 2v2l-1 1-1 1v-6z" class="f"></path><path d="M147 177c-1-1-2-1-3-1v-1h3c1 1 2 1 3 1l1 1c-2 0-3 0-4 1v-1z" class="X"></path><path d="M230 633c-1 0-1-1-2-1 2-2 6-2 8-2h0v1c-1 0-3 0-4 1v1h-2z" class="I"></path><path d="M393 634c1 0 1-1 2-1s1 0 2-1c0 0 1 0 1 1h1l1-1v1s-1 0-1 1h-2c-1 1-3 2-4 3v-3z" class="O"></path><path d="M479 229l1-1v3l-2 5-1-1v-2-1c1 0 1-1 2-1l-1-1 1-1z" class="Q"></path><path d="M229 203c1-1 3-2 4-2 0 1 1 1 1 1 1-1 1 0 1 0l1 1-9 2c0-2 1-2 2-2z" class="M"></path><path d="M277 611h0 0c1 1 2 2 2 3h-1v2h1v1h-2c0-1 0-1-1-2-1 0-1 0-1-1s1-2 2-3h0z" class="C"></path><path d="M250 629v-2l1-1c1 1 0 2 1 2l1-1h1v1c0 1-1 2-1 2h0c-1 0-1 1-1 1l-2 2v-1-3z" class="D"></path><path d="M229 199v-1h1l-1 1v1h1l1 1c1 0 0 0 1-1h2 0 1l1 1 1 1v1h1-2 0l-1-1s0-1-1 0c0 0-1 0-1-1-1 0-3 1-4 2l-1-1c1-1 1-2 1-2v-1z" class="N"></path><path d="M156 175h16 0c-1 1-1 1-2 1s-2 0-3 1v1l-1-1c-1 0-1 0-1 1l-1-1h-2-1v-1c-1-1-4 0-5-1z" class="E"></path><path d="M292 572h0c0 1-1 1-1 1l2 3v1h0-1 0l-1 1c-1-1-2-3-3-4 1-1 2-2 4-2z" class="P"></path><path d="M291 573l2 3v1h0-1 0c0-1-1-1-2-2l1-2z" class="g"></path><path d="M147 175h9c1 1 4 0 5 1-1 1-2 1-3 1h-3-4 0l-1-1c-1 0-2 0-3-1z" class="J"></path><path d="M353 478l1-1c0-1-1-3 1-4v8h0 0c0 1 0 1-1 1v1h1l1 1c-1 0-1 0-1 1-1 1 0 2-2 2h0c0-2-1-4 0-6h1v-2h-1v-1z" class="M"></path><path d="M233 635l1-1 1 1v1h1 0 2v1c1 0 1-1 2-1h1 0 4v1h0-4c-2 1-5 1-6 1l-1-1h0c-1-1-1-1-1-2z" class="E"></path><path d="M223 635v-2h1c2 1 1 2 3 3h0 2l1-1c1 1 1 1 1 2-1 1-5 0-6 1h-1 0-1c-1-1 0-2 0-3z" class="R"></path><path d="M223 635h1v3h-1c-1-1 0-2 0-3z" class="M"></path><path d="M292 371l1 1 1 1v4l-1 1h-1v2h0c-2-1 0-4-1-5v-2h0c0-1-1-2 0-3v1h1z" class="K"></path><path d="M296 365l1-1-1-1c0-2-1-5 1-7v1 1 2h0 1v-3 12 2c-2-2 0-4-2-6z" class="b"></path><path d="M289 521c1 0 2 0 2 1 1 0 1 0 1-1h1l1 2h-1c-1 0-2 0-2-1l-1 1h0c1 1 1 1 1 2-1 1 0 2 0 3s0 2-1 4l1 1c-1 1-1 1 0 2-1 0-1 0-2-1v-1-12z" class="V"></path><path d="M291 370v-6-8-4c-1-2 0-3 0-4v-1h0c0 1 1 1 1 1 1 2-1 2-1 4h1v-1h1v1c0 1-1 1-1 1 0 1 1 1 1 2 0 0-1 0-1 1 1 1 1 6 0 7h0c1 3 0 6 0 8h-1v-1z" class="i"></path><path d="M255 617c1-2 3-4 5-4 1 0 1-1 2-1v-1l1 1s-1 2-2 2-2 1-3 1c0 1-1 1-1 2s0 2-1 3v1h-1-2-1c-1-1-1-1-1-2l1-1h0l1 1h1v-1l1-1z" class="D"></path><path d="M472 276c-1 2-1 8-3 10h1c0 2-1 7-1 10 0 1 0 3-1 5l-1 1v-2c2-2 1-5 1-7v-5-5c0-1 0-1 1-2 0-1 0-1 1-2v-2c1 0 1-1 2-1z" class="W"></path><path d="M355 216h1v-1h-1l-1-1 2-1v-1h-2v-1h1 1v-1h-1v-1l2-1h0c0 2 1 6-1 9 1 1 1 1 0 2l-1 1v1c1 0 1 0 1-1 1 1 1 4 1 5-1-1-2-1-2-1v-2l-1 1h0 0c0-2 0-4 1-7z" class="F"></path><path d="M291 535h0l1 2 1 1c0 1-1 5-1 5h0-1-2v1h-1v-4c1-2 0-5 1-7v1c1 1 1 1 2 1z" class="S"></path><path d="M293 213h1c1 0 1 0 2-1v1h0v1c0 2 1 2 2 4l-1 1-1-1-1 1v-2h-2c0-1 0-1-1-1v1 1 1c-1 3 1 7 0 9-1 0 0-1-1-2v-1c0-2 1-3 0-5 0-1 0-2-1-3l1-1 1-1h0c0-1 0-1 1-2z" class="g"></path><path d="M293 213h1c1 0 1 0 2-1v1h0v1 1c-1 0-1 0-1 1h-1s-1 0-1-1h-1 0c0-1 0-1 1-2z" class="Y"></path><path d="M291 406v-8-2-7-3-2-1-1-1h1 0v4 1c1 1 0 7 0 9h0c0 1 0 2 1 2v1c-1 2 0 3 0 5v3h-1-1z" class="J"></path><path d="M298 330h0c-2-1-1-3-1-4-1 2 0 4-1 6h0l-1-1v-9-3c1 0 1 0 1 1h0 1c0 1 0 2 1 2h1c-1 2 1 5-1 7v1z" class="T"></path><path d="M255 617v-1l1-1 1-1 2-2h1l1-1h0c1 0 1-1 2-2h0c0-1 3-3 3-4s2-2 3-3l-1 1 1 1c1 1 2 2 2 3v1l-3-2h0l-2 5h0c-1 1-1 1-2 1h-1l-1-1v1c-1 0-1 1-2 1-2 0-4 2-5 4z" class="S"></path><path d="M268 603l1 1c1 1 2 2 2 3v1l-3-2h0l-2 5-2-2 1-1c1-2 2-3 3-5z" class="C"></path><path d="M355 557v-3-2l2-2c0 1 0 1 1 1 0 2 0 4 1 5v4 3h0c-1 0-1-1-1-1 0-1-1-1-1-1h-1 0-1v-4z" class="U"></path><path d="M272 597l2-3s1-1 1-2h0c0-1 2-3 2-4l1-1v1h0c0 1 0 2-1 3l1 1c2 0 3 2 4 3 2 1 4 3 6 4v1c-4-1-7-5-10-7h-2v2h1l-1 1h-2v3c0-1-1-1-2-2z" class="B"></path><path d="M298 306h2c1 0 1 0 2-1v3h-2v3 6h0c-1 2-1 3-1 5h-1c-1 0-1-1-1-2v-3c0-1 1-1 1-2v-1c1-2-1-5 0-8z" class="C"></path><path d="M299 311h1v6l-2-2c1-1 1-3 1-4z" class="p"></path><path d="M298 306h2c1 0 1 0 2-1v3h-2v3h-1l-1-5z" class="l"></path><path d="M300 306c1 0 1 0 2-1v3h-2v-2z" class="O"></path><path d="M296 365c2 2 0 4 2 6v-2 13c0 1 1 3 0 5h-1v-9h-2v-6h1v-2h-1c0-2 1-4 1-5z" class="D"></path><path d="M296 365c2 2 0 4 2 6v5h-1v-1-3h-1v-2h-1c0-2 1-4 1-5z" class="T"></path><path d="M253 630h1 0l1-1h1 0v1h-1c0 1-1 1-1 2 1 1 1 1 2 1h0c0-1 1-1 1-1h0 2c-1 1-2 2-3 2l-1 1v2h0 0c-1 1-2 1-3 0h1c-1-1-1-2-2-3h-5c-1 0-1 1-2 1l-1-1c0-2 0-2 1-3v-1h1l-1 1v1 2h2c1-1 3 0 4 0v-5 3 1l2-2s0-1 1-1h0z" class="U"></path><path d="M145 187c-1-1-1-2-2-2-2-3-4-6-7-8v-1c2 1 5-1 6 0v1c1 2 1 2 2 3h1v1 1c0 1 2 1 2 2v2 1h-1v-1-1c-1-1-1-2-2-3 0 0-1-1-1-2h-1v2s1 1 2 1c0 2 0 2 1 3v1z" class="R"></path><path d="M256 194v1c0 2-1 3-2 5l-8 1c-1 1-2 1-3 2h-3v-1l2-2c1 1 2 1 3 0 0-1 1-1 1-2 1 0 1-1 1-1h2 2s1 1 1 2c1 0 1 0 2-1 0 0 0-1 1-1v-3h1z" class="H"></path><path d="M256 194v1c0 2-1 3-2 5l-8 1h0l2-1v-2c1 0 2 1 4 1h0c1 0 1 0 2-1 0 0 0-1 1-1v-3h1z" class="E"></path><path d="M482 216c2-4 3-12 7-15-2 8-6 16-8 24l-2 2h0v1h-1l-2-1 1-2c0-1 1-1 2-2h0l1-1c-1 0-1 0-1-1h1l1 1v-1c1-2 0-2-1-4l2-1z" class="F"></path><path d="M480 222v2c-1 1-2 1-3 1 0-1 1-1 2-2h0l1-1z" class="E"></path><defs><linearGradient id="AK" x1="466.166" y1="309.274" x2="465.021" y2="315.679" xlink:href="#B"><stop offset="0" stop-color="#767777"></stop><stop offset="1" stop-color="#8c888e"></stop></linearGradient></defs><path fill="url(#AK)" d="M467 302l1-1-3 27-1 1v-1-4c1-6 0-12 1-17v-4l2 1v-2z"></path><path d="M290 459l2-1v6c-1 3 0 6 0 9v15 2 2l-2-1h0v-10-22z" class="c"></path><path d="M290 491c1-1 0-6 1-6l1 3v2 2l-2-1h0z" class="i"></path><path d="M145 187v-1c-1-1-1-1-1-3-1 0-2-1-2-1v-2h1c0 1 1 2 1 2 1 1 1 2 2 3v1 1h1v-1h1 1v-2h0 1c0 1 0 1 1 1l2 1c0 1-1 1-1 2h-1v-1h-1v1 1h2c1 0 1 2 0 2 0 1-1 2-2 2v-1c-1 0-1 0-2 1l-3-6z" class="L"></path><path d="M241 630c0-1 1 0 3 0v1c-1 1-1 1-1 3l1 1c1 0 1-1 2-1h5c1 1 1 2 2 3h-1-7 0v-1h-4 0-1c-1 0-1 1-2 1v-1-1c0-1 1-1 1-2v-2h1l1-1z" class="G"></path><path d="M292 488v-15c0-3-1-6 0-9v8c0 1 1 1 1 2s0 2 1 2h0 0c1 0 2 1 3 2l1-1v1h0v2 4 1l-2-2-2-2c-1 1-1 2-1 3l1 1-2 2v3-2z" class="K"></path><path d="M292 472c0 1 1 1 1 2s0 2 1 2h0 0c1 0 2 1 3 2l1-1v1h0v2 4 1l-2-2-2-2v-1c1 0 1 0 1-1-1-1-1-2-2-3-1 0-1-3-1-4z" class="g"></path><path d="M296 483c1-1 0-2 1-3h1v4 1l-2-2z" class="T"></path><path d="M285 175l31 1h-4 0l1 1h-2c-1-1-5 0-6 0h-12 0-2s-1 1 0 2v1c-1 0-2 0-3-1h0l1-1h-2l-1 1c-1 0-1 0-2-1v1c0-1 0-2-1-3v-1h2z" class="F"></path><path d="M286 179c-1 0 0-1 0-1l-2-1v-1l7 1s-1 1 0 2v1c-1 0-2 0-3-1h0l1-1h-2l-1 1z" class="D"></path><path d="M294 202c1 1 2 2 2 4v2c0 1 0 1 1 2s1 1 1 2v1h-2 0v-1c-1 1-1 1-2 1h-1c-1 1-1 1-1 2h0l-1 1c-1-2-1-5-1-7 1-2 1-3 1-5h0l1 2v-3h0l1-1h1z" class="b"></path><path d="M294 202c1 1 2 2 2 4-1 0-2 0-3 1s-1 2-1 3v-4-3h0l1-1h1z" class="P"></path><path d="M290 209c1-2 1-3 1-5h0l1 2v4h1c0 1-1 1-1 2l1 1c-1 1-1 1-1 2h0l-1 1c-1-2-1-5-1-7z" class="k"></path><path d="M355 481c2-2 0-10 2-11 1 1 0 8 0 10 2 6 1 13 0 19h0l-1-1v-2h1v-1-1c-1 1-1 1-2 1v-1h1c0-2 0-1-1-2l-1-2-1-1h1v-1l-1-1c2 0 1-1 2-2 0-1 0-1 1-1l-1-1h-1v-1c1 0 1 0 1-1z" class="K"></path><path d="M354 490c1-1 1-1 3-1h0c0 2-1 2-2 3l-1-2z" class="P"></path><path d="M299 341c1 1 1 2 1 3l-1 13v3c0 1 0 3 1 4v1c-1 2 0 4 0 6v12 5s-1 0-1 1h-1v-2c1-2 0-4 0-5v-13-12c1-1 0-9 0-11s0-3 1-5z" class="W"></path><path d="M299 269l1 1h2v12h-2v17 1 5h-1c-1-2 0-3 0-5v-15-9-1-6z" class="n"></path><path d="M300 270h2v12h-2v-4-2-1-5z" class="B"></path><path d="M161 236h3v2h1c0-1 0-1 1-1h0c0 2 0 3 1 5v1c-1 1-1 1-1 2v1c0 1 0 1-1 2 0 3 1 4 1 6 1 1 0 2 1 3 0 2 1 5 1 7v1h0c-1 0-1-2-1-2v-1c-1-1-1-3-2-4v-2s-1-1-1-2v-2c-1-3-2-7-2-10 0-1 0-2-1-3h0c1-1 1-1 2-1v-1l-2-1z" class="T"></path><path d="M246 186h1l1 1v1c1 0 2 0 2 1h1v1h-1v1l2 1v1l1-1 1 1 1-1 1 2h-1v3c-1 0-1 1-1 1-1 1-1 1-2 1 0-1-1-2-1-2h-2-2c-1 0-1 0-1-1-1 0-1-1 0-1 1-1 1 0 2 0h0c0-1-1-1-1-2l-2-1c0-1 1-1 2-3h0-2 0c0-1 1-1 1-2v-1z" class="I"></path><path d="M252 193l1-1 1 1v4c-1 0-1-1-2-1v-3z" class="G"></path><path d="M352 202l2 2 2-2c1-1 2-2 3-2h1c1 0 1 0 2 1-1 0-2 0-3 1 0 1-1 2-1 3s0 2-1 3h0l-2 1v1h1v1h-1-1v1h2v1l-2 1 1 1h1v1h-1c-2 0-2 0-3 1h0v-2c0-1 0-2-1-3v-1c1-1 1-2 1-3s0-2-1-3h0c0-1 0 0 1-1v-2z" class="T"></path><path d="M354 431h1 0 2 1l-2 1c1 1 1 1 2 1l-1 1h0-1c0 1 1 2 1 4h-1v1h1c0 2 0 3-1 5h-1c0 1 0 2 1 3h1v5 16h0c-1-1-1-4-1-5v-1-4c0-1 0-5-1-6l-1 1h-1c1-2 1-2 2-3h-1c0-1 0-2 1-3 0-1-1-1-2-3h1v-4h0l-1-1c0-1 0-1 1-2h1 0v-1-3h0c-1 1-1 2-2 2h0c0-2 0-3 1-4z" class="L"></path><path d="M236 631h-1v2h1c1 0 1-1 1-1v-1c1 0 2 0 2-1h-2v-1h1v-1c0-1 0-2 1-3 1 0 2-1 3-1 2-1 4-3 6-4 1 1 0 2 1 3h0c-1 1-2 3-3 3h-1c-1 1-2 1-2 2h-1 0c-1 0-1 1-1 2h0l-1 1h-1v2c0 1-1 1-1 2v1h-2 0-1v-1l-1-1-1 1h-1v-2h-2 2v-1c1-1 3-1 4-1z" class="H"></path><path d="M284 572h0 1c0 2 0 2 1 3l-1 1c2 1 2 2 4 3 0 1 1 1 1 1l-1 1-1-1h0c-1 0-2-1-2-2h0-1v2l-1 1h1v1h-1c-1 2-2 3-3 4l2 2c1 1 2 2 4 3v1s-1 1-2 1-3 0-4-1l1-1c0-2 0-2-2-4v-1s-1 0-1-1c0-2 2-5 3-7v-2h0c1-1 1-2 1-3 0 0 0-1 1-1z" class="T"></path><path d="M472 259v-2l-2-2c1-1 1-3 1-4s0-1 1-1c1 1 0 3 0 4h1v-3c2-1 2-1 3 0 0 0 0 2-1 2v5l-1 5c0 1-1 3-1 5v2c0 1-1 4-1 6-1 0-1 1-2 1v2c-1 1-1 1-1 2 0-1-1-3 0-5v-2l2 1v-1h-1c-1-2 1-2 1-3h-1v-3-1l2-1c-1 0-1 0-1-1s1-1 1-1c0-1-1-1-1-2s0-2 1-3z" class="F"></path><path d="M473 258c0-2 1-3 2-5v5h-2z" class="L"></path><path d="M473 258h2l-1 5-1-1v-4z" class="V"></path><path d="M381 627h2 1c-1 1-1 1-1 2 1 0 2 0 2 1v1c0 1 1 1 1 2v-1c1 0 1 1 2 1h0l1 1c1 0 1-1 1-1l1-1 1-1c1 1 1 1 1 3v3h-2 0l-2-2-1 1v1c-1 0-1 1-2 1l-1-1h-6v-1c1-1 0-1 1-1 1-1 1-1 1-3h-1-1v-1c1 0 1-1 2-1h0l1-1-1-2z" class="T"></path><path d="M279 175h6 0-2v1c1 1 1 2 1 3h-3l-3 1h-2-1-2l-1-2c-1 0-1 1-2 1s-3 0-4-1v-1-1h1v-1h12z" class="K"></path><path d="M279 175h3l-1 1h-3c0 1-1 0-1 0l-1 1c-2 0-4-1-6-1 0 1 0 1-1 2h0c0-1 0-2-1-2h0v-1h11z" class="L"></path><path d="M279 175h6 0-2v1c1 1 1 2 1 3h-3l-3 1h-2c1 0 1-1 2-1h-2v-1-1h0l1-1s1 1 1 0h3l1-1h-3 0z" class="S"></path><path d="M276 177l1-1s1 1 1 0h3l1 2h0-3v-1h-1v2h-2v-1-1h0z" class="C"></path><path d="M356 416h1c0-1 0-1 1-2v3c-1 0 0 1-1 2v2 1 1 3h0l1 1v1 2 1h-1-2 0-1c-1-1-1-1-1-2 1-1 2-2 2-3h0l-1-1v1c-2 0-2-1-4-1v-3c1-1 1-1 2-1v-4l1-1c0 1 0 1 1 1v-2h1l1 2v-1z" class="U"></path><path d="M352 421c1 1 0 1 2 1v-1h0s-1 0-1-1l1-1h1 0v1l1-1c1 1 1 1 1 2l-1 1c1 1 1 2 1 3-1 0-1 0-2 1l-1-1v1c-2 0-2-1-4-1v-3c1-1 1-1 2-1z" class="P"></path><path d="M346 468v-2h1 2c0 1 1 2 1 3v2 2c1 1 1 1 1 2 1-1 1-1 1-2h0l1-1v4 1h-1v-1h-1v4h1v-1l1-1v1h1v2h-1c-1 2 0 4 0 6-1-1-1-1-2-1v-1h-1v2l-1-1v-2-5s0-3 1-3c0-1 0-2-1-3h-3l-1 1-1 1v-2-4-1h2z" class="D"></path><path d="M351 480h1v4h-1v-4z" class="U"></path><path d="M344 468h2v1c0 1 0 2 1 3 0 0 0 1-1 1l-1 1-1 1v-2-4-1z" class="S"></path><path d="M346 468v-2h1 2c0 1 1 2 1 3v2h-1-2c-1-1-1-2-1-2v-1z" class="H"></path><path d="M459 317h0c1 0 2 1 2 0v-1-1h0c0-1 0-1 1-2h0c-1-1-1-1 0-2h0c1-1 1-1 1-2 0-2 1-3 1-4 1 0 1 1 1 2-1 5 0 11-1 17v4h-2c-1 0-2 0-3-1v-10z" class="S"></path><path d="M291 280h1v1 1 6 6 3c0 3 1 7 0 10-1 1 0 2 1 4 0 3-2 7-1 10 1 2 0 4 0 6v17c1 1 1 2 0 3h-1v-1-30-36z" class="d"></path><path d="M282 195v-1h1 0c0 1 0 1 1 1h2c1 0 0-2 2-2v1l1 1c1 1 1 3 2 4l2-1h0c0 1 0 2 1 2v2h-1l-1 1h0v3l-1-2h0c0 2 0 3-1 5v-3c0-2 1-4-1-6h-13 0l1-3h2 2v-1l1-1z" class="V"></path><path d="M281 197v-1l1-1c1 0 1 1 1 1 1 1 2 1 2 2l1 2h-5-5l1-3h2 2z" class="a"></path><path d="M281 197v-1l1-1c1 0 1 1 1 1v3h0-2v-2z" class="P"></path><path d="M298 218h0s1 1 1 2c-1 2-1 4 0 6v1l-1 4v2h1c0 1-1 1 0 2-1 0-1 1-2 0v2l1 1s-1 1-1 2 0 1-1 2h1v1 3 5l-1 19c0 3 1 6 0 8h-1v-4c1-2 0-5 0-7s1-3 1-5v-15-6c-1-5 0-11 0-15v-5c-1-1-1-2-1-2l1-1 1 1 1-1z" class="M"></path><path d="M299 226v1l-2 1v-2h2z" class="U"></path><path d="M297 228l2-1-1 4v2h1c0 1-1 1 0 2-1 0-1 1-2 0v-7zm1-10h0s1 1 1 2c-1 2-1 4 0 6h-2c0-2 0-5-1-8l1 1 1-1z" class="O"></path><path d="M352 217h0c1-1 1-1 3-1-1 3-1 5-1 7h0 0l1-1v2s1 0 2 1h-2c1 1 2 1 2 2s-1 2-1 2c1 1 0 2 0 3v1c1 1 1 3 1 4l-1-1-1-1c-1 1-1 1-1 2l-1 1h0l1 1c0 2-1 2 0 4l-1 1h-2v-13-6h0 0c1-1 0-1 0-1 0-2 1-4 0-6l1-1z" class="b"></path><path d="M354 223h0l1-1v2s1 0 2 1h-2c1 1 2 1 2 2s-1 2-1 2c1 1 0 2 0 3v1c1 1 1 3 1 4l-1-1v-1c0-1-1-2-2-3 0-1-1-2 0-3v-1h1v-1h-1v-4z" class="Q"></path><path d="M173 220h1l-1 2-1 2s-1 0-1 1c0 0-1 1-1 2l1 1v-1h1v1 1c-1 2 0 1 0 3 0 0-1 0-1 1v1c-1 1-1 1-1 3v2c-1 1-1 2-1 3h-1v-1h-1v1 1-1c-1-2-1-3-1-5h0c-1 0-1 0-1 1h-1v-2h-3 0-1v-2c1 0 2-1 3-1v1h1c0-2 1-3 1-4l2-1c0-1 1-1 1-2 0-2 1-3 2-4v-2l3-1z" class="b"></path><path d="M255 629v-3h1c0-1 0-1 1-1 0-1 1-2 1-2h3l1 1h0 2 0c1 0 1-1 2-1-1-1-2-1-2-2 0 0 0-1-1-1l1-1h0-1 0c1-2 0-1-1-3h2v-1-1l1 1v1h-1v1c1 0 2 1 3 2v-1h2v2h1c2 1 5 0 7 1h-1l1 1v1c-2 0-4 1-6 1h0c0-1 0-2 1-2s3 1 3-1h-5c0 1 0 1-1 1l1 2c-2 1-2 0-4 0h0c-1 1-3 1-4 1h-1c-1 0-1 1-1 1 0 1 1 1 1 1l2-1v1c-1 0-1 1-2 1v1h0 1 1v1h-1c-1 0-2 1-3 2h-2 0s-1 0-1 1h0c-1 0-1 0-2-1 0-1 1-1 1-2h1v-1h0-1z" class="P"></path><path d="M256 629v-3h2 0c0 1-1 1-1 2h0 0 3c-1 1-2 2-3 2l-1-1h0z" class="Q"></path><path d="M459 297h1v1s0 1 1 1c1-1 0-1 0-2 1-1 2-3 3-3h0 0l2-2s-1-1-1-2l2-1v1h0c0 1 0 1 1 1v-2-1 5c0 2 1 5-1 7v2 2l-2-1v4c0-1 0-2-1-2 0 1-1 2-1 4 0 1 0 1-1 2h0c-1 1-1 1 0 2h0c-1 1-1 1-1 2h0v1 1c0 1-1 0-2 0h0v-11c0-1-1-3 0-5v-4z" class="O"></path><path d="M274 599v-3h2l1-1h-1v-2h2c3 2 6 6 10 7l2 1-2 2c-1 0-1 1-2 2h2v1h2c1 0 1 0 1 1-1-1-1-1-2 0 0 0-1 0-1 1h0-5c0-1-1-1-1-2l-1-2c-2 0-2-1-3-2l-2 1h-2v-3c-1 0-1-1-2-2v-1c1 1 2 1 2 2z" class="Y"></path><path d="M272 597c1 1 2 1 2 2 1 1 3 2 5 3 1 1 1 2 3 2l1 1h3 2v1h2c1 0 1 0 1 1-1-1-1-1-2 0 0 0-1 0-1 1h0-5c0-1-1-1-1-2l-1-2c-2 0-2-1-3-2l-2 1h-2v-3c-1 0-1-1-2-2v-1z" class="E"></path><path d="M274 600l2 2h2l-2 1h-2v-3z" class="S"></path><path d="M283 605h3 2v1h2c1 0 1 0 1 1-1-1-1-1-2 0 0 0-1 0-1 1h0 0c-1-1-2-1-2-2h-1-2v-1z" class="F"></path><path d="M434 175h45-1l-2 1s-1 1-2 1h-2c-2-1-4-1-6-1l-1 1v3h-3-4c-2 0-2 0-3 1h-1-1l-2-2v2h-1l-1-1-2 1v-1h-4v2h0c-3 0-6 1-8 1v-2h1l1 1c1 0 2-1 3-1h0c2 0 2 0 3-1h0c0-1 1 0 1-1h2c-1-1-1-1-2-1l1-2h-3-5-2l-1-1z" class="B"></path><path d="M466 176h0c1-1 2-1 3-1h9l-2 1s-1 1-2 1h-2c-2-1-4-1-6-1z" class="R"></path><path d="M435 183c2 0 5-1 8-1h0v-2h4v1l2-1 1 1h1v-2l2 2h1 1c1-1 1-1 3-1h4v1c-1 0-1 0-2 1v3c2-1 2-1 4-1 1 1 2 0 3 1-1 0-1 1-1 1-2 0-3 0-4 1h-1c0-1-1-1-2-1s-3 1-4 0v-2h-1v2c-1 1-3 0-4 0-1 1-4 0-5 1-1 0-1-1-1-1h-2-10v-2h-2c1-1 2-1 2-1h2 1z" class="C"></path><path d="M435 183c2 0 5-1 8-1h0v-2h4v1l2-1 1 1h1v-2l2 2c-1 1-1 1-3 1h-1c-1 1-3-1-3 0 1 1 1 1 2 1h1l1 1h-1-5-8-4-2c1-1 2-1 2-1h2 1z" class="f"></path><path d="M455 181c1-1 1-1 3-1h4v1c-1 0-1 0-2 1v3c2-1 2-1 4-1 1 1 2 0 3 1-1 0-1 1-1 1-2 0-3 0-4 1h-1c0-1-1-1-2-1s-3 1-4 0v-2h-1v2c-1 1-3 0-4 0l1-1h2v-1h-2v-1h0 4v1l4-1v-1c-2-1-2 0-3 0l-1-1z" class="Q"></path><path d="M425 223l4 3h-2l-1-1c3 3 5 5 7 9v1l1 1h0c1 2 3 3 4 4s0 0 0 1 2 3 2 4c4 4 7 10 9 16l2 4 1 2c1 2 1 3 2 5l3 13v2h0c0 1 1 1 1 1l-1 1v1c1 1 1 4 1 5h1 1c-1 1-1 1-1 2v4c-1 2 0 4 0 5v11 10c1 1 2 1 3 1v1c-2 0-3 0-4-1l-2-38c0-5-2-10-3-15s-3-11-5-16c-3-6-7-12-11-17-1-3-4-5-6-7l-3-3-4-4c0-1 1-1 0-2 0-1 0-2-1-3h1 1z" class="Z"></path><path d="M167 178c1-1 3-1 4-1s0 1 2 1v-1l-1-1c2 0 2 1 2 1 2 0 2-1 4 0 0 1 0 1-1 2h-2v3h-4c-1 0-3 0-4 1l-1-1c-1 0-1 0-2 1h0c-1-1-1 0-2-1 0 0-1 0-1-1h-1l-1 1v1-1l-1 1h-1l1-1-1-1h-1-2-2v1c0 1-2 2-2 2h-1 0v2h-1-1v-2c0-1-2-1-2-2v-1-1h-1c0-1 1-1 1-1v-1c1 0 2-1 2-1v1c1-1 2-1 4-1h0 4 3c1 0 2 0 3-1v1h1 2l1 1c0-1 0-1 1-1l1 1z" class="b"></path><path d="M161 177h1 2l1 1c-1 0-1 0-1 1h-3l-1-1 1-1z" class="Y"></path><path d="M161 181h6 5l1-1c1 0 2-1 2-1v3h-4c-1 0-3 0-4 1l-1-1c-1 0-1 0-2 1h0c-1-1-1 0-2-1 0 0-1 0-1-1z" class="F"></path><path d="M145 181h2 2 0 1v1h1c0-1 1-1 1-1v1c0 1-2 2-2 2h-1 0v2h-1-1v-2c0-1-2-1-2-2v-1z" class="G"></path><path d="M233 183l2 1h0l4 1h2v2c0 1-1 0-1 0l1 1-1 1 2 1v3h0c1 1 1 2 1 3v2c-1 1-2 1-3 1h-2 0c0-1 0-1-1-1h-1-2 0l-2 2-1-1-1-1h-1v1h-2-1l1-2c1 0 1-1 2-2h0 2l1 1c0-1 1-1 1-1h1v-1h-1c-1-1-1-1-1-2s-1-1-1-1c1-1 1-1 1-2h0-3v-1-2h-2 0l1-1h0 3 0l2-2z" class="B"></path><path d="M235 184l4 1 1 1h1v1c-3 0-6 0-8-1h0c0-1 1-2 2-2z" class="E"></path><path d="M233 186c1-1 2-1 3-1l4 2v-1h1v1c-3 0-6 0-8-1z" class="G"></path><path d="M242 190v3h0c1 1 1 2 1 3l-1 1h-1v-2c-1 0-2 1-3 2h-2s-1-1-2-1v-2h5c0-1 1-1 2-2h0v-1l-2-1-1 1h-3c1 1 2 1 3 1v1c-2 0-3 1-4 0v-2l1-1h6 1z" class="F"></path><path d="M234 196c1 0 2 0 3-1v1 1h1-2s-1-1-2-1z" class="C"></path><path d="M427 220c1 0 1 0 2 1 0 0 2 1 2 2l2-1c1 1 1 2 1 3h1c1-1 1-1 2-1v-2c1 2 3 3 3 5 0 1 0 1 1 1 1 1 1 3 1 5 1 0 1 0 2 1h-1l1 1-1 1h-1v1c1 0 1 0 2 1 0 1-1 1-1 2s0 2-1 3l-1 1-1 1c0-1-2-3-2-4s1 0 0-1-3-2-4-4h0l-1-1v-1c-2-4-4-6-7-9l1 1h2l-4-3v-1h1 2l-1-1v-1z" class="B"></path><path d="M434 236h1l3 3v1c-1-1-3-2-4-4z" class="O"></path><path d="M438 236l1-1-1-1h-1c0-1 0-1 1-2l-1-1h1c1 1 1 1 2 3h0 2c0 1-1 2-1 2h-1c0 1 0 1-1 1 0 0 0-1-1-1z" class="S"></path><path d="M427 220c1 0 1 0 2 1 0 0 2 1 2 2h0c1 1 1 2 1 2 0 1 0 1-1 1v1c-1 0-1 0-2-1l-4-3v-1h1 2l-1-1v-1z" class="D"></path><path d="M427 220c1 0 1 0 2 1 0 0 2 1 2 2h0c1 1 1 2 1 2 0 1 0 1-1 1 0 0-1-1-2-1v-2l-1-1-1-1v-1z" class="I"></path><path d="M437 222c1 2 3 3 3 5 0 1 0 1 1 1 1 1 1 3 1 5 1 0 1 0 2 1h-1l1 1-1 1h-1v1c1 0 1 0 2 1 0 1-1 1-1 2s0 2-1 3l-1 1-1 1c0-1-2-3-2-4s1 0 0-1v-1l1-2c-1 0-1 0-1-1h0 0c1 0 1 1 1 1 1 0 1 0 1-1h1s1-1 1-2h-2 0v-1l1-1c0-1-1-1-1-2v-1h-1v1l-1-1h-2l-2-2v-2h1c1-1 1-1 2-1v-2z" class="E"></path><path d="M437 226h0l2 2h-1c-1 0-1 0-2-1l1-1z" class="D"></path><path d="M438 239l1-2c-1 0-1 0-1-1h0 0c1 0 1 1 1 1l3 3h-2v1l2 1c0-1 0-1 1-2 0 1 0 2-1 3l-1 1-1 1c0-1-2-3-2-4s1 0 0-1v-1z" class="Y"></path><path d="M294 481l2 2 2 2v-1c0 3 0 9 1 11 0 1-1 1-1 2 0 2 0 4 1 6 0 0-1 1-1 2l-1-1c-1 1-1 1-1 2 0 2 0 4 1 6l-1 1c-1 0-1 0-2-1h0v1h-1c-1 0-1 0-2-1h-1v-4l1-1 1-1v-2l-1-2h-1v-1-3-7l2 1v-2-3l2-2-1-1c0-1 0-2 1-3z" class="O"></path><path d="M290 498c1-1 1-1 2-1 0 1 0 2-1 4h-1v-3z" class="i"></path><path d="M290 491l2 1v2 3c-1 0-1 0-2 1v-7z" class="c"></path><path d="M294 485l1-2c1 1 1 1 1 2v2 4 1 1l-1-1-1-1c-1 0-1 0-1-1h0l2-1c0-1-1-3-1-4z" class="Y"></path><path d="M294 481l2 2 2 2v-1c0 3 0 9 1 11h-1c-1-1-1-1-2-1v-2-1-4-2c0-1 0-1-1-2l-1 2h0l-1-1c0-1 0-2 1-3z" class="G"></path><path d="M296 487l2 1v2c-1 0-1 1-2 1v-4z" class="B"></path><path d="M292 504c0-2 1-3 2-5l2-1v1c-1 1-1 2-2 2l1 1c1 2 1 2 1 4h0c0 2 0 4 1 6l-1 1c-1 0-1 0-2-1h0v1h-1c-1 0-1 0-2-1h-1v-4l1-1 1-1v-2z" class="U"></path><path d="M290 508l1-1 1-1c0 2 0 5 2 6v1h-1c-1 0-1 0-2-1h-1v-4z" class="c"></path><path d="M350 425c2 0 2 1 4 1v-1l1 1h0c0 1-1 2-2 3 0 1 0 1 1 2-1 1-1 2-1 4h0c1 0 1-1 2-2h0v3 1h0-1c-1 1-1 1-1 2l1 1h0v4h-1c1 2 2 2 2 3-1 1-1 2-1 3h1c-1 1-1 1-2 3h1l1-1c1 1 1 5 1 6v4 1h-1v5h0-2 0v1 1 1l-2-2h-1c0-1-1-2-1-3h-2v-1c1 0 2-1 2-2h1c0-1 1-1 1-2v-1h-1c1-1 0-2 1-2l-2-2c0-1 1-2 1-3s0-1-1-2c0 1-1 1-1 1h-1v-4h0v-1c1 0 1 0 2 1 1-1 0-2 1-4h1v-4-3c0-3 1-5 1-8h-1c0 2 0 5-1 7h-1c1-2 1-3 1-5h0v-6z" class="Y"></path><path d="M351 440c1 3 1 6 1 9v2h0l-1 1-1 1c0-1 0-1-1-2 0 1-1 1-1 1h-1v-4h0v-1c1 0 1 0 2 1 1-1 0-2 1-4h1v-4zm0 21v1 1c-1 1 0 3-1 5h1 1v-6h0v-2c1-1 1-1 2-1v-4l1-1c1 2 0 5 0 8h1v1h-1v5h0-2 0v1 1 1l-2-2h-1c0-1-1-2-1-3h-2v-1c1 0 2-1 2-2h1c0-1 1-1 1-2z" class="O"></path><path d="M300 317c1 3 0 7 0 9l1 1h0v4l1 1h0l2-1c0 1 1 1 1 2 0 0 0 1 1 1v1 15c0 4 1 8 0 11l-1-1c0-1-1-2-1-2-1 0-1 1-2 2h-1c0-1 0-1-1-2 0 0 0-1-1-1l1-13c0-1 0-2-1-3h0c-1-1-1-4 0-5v-1c-1-2 0-3-1-5v-1c2-2 0-5 1-7 0-2 0-3 1-5z" class="P"></path><path d="M300 317c1 3 0 7 0 9l1 1c-1 1-2 2-1 3v5c-1 2 0 4 0 6h-1 0c-1-1-1-4 0-5v-1c-1-2 0-3-1-5v-1c2-2 0-5 1-7 0-2 0-3 1-5z" class="j"></path><path d="M301 350c0-2 1-4 0-6v-5c0-2 1-3 1-4h-1c1-1 1-2 1-2l1 1v2c0 1 1 2 0 4h0v1h0v2l1 1h0c-1 1-1 1-1 3-2 1 0 4-2 6v-3z" class="X"></path><path d="M303 340h1c1 0 1 0 1-1v-1c0-1-1-2 0-3h1v15c0 4 1 8 0 11l-1-1c0-1-1-2-1-2-1 0-1 1-2 2h-1c0-1 0-1-1-2 0 0 0-1-1-1l1-13c1 2 1 3 1 5v1h0v3c2-2 0-5 2-6 0-2 0-2 1-3h0l-1-1v-2h0v-1z" class="C"></path><path d="M300 344c1 2 1 3 1 5v1 5c0 1 0 1 1 2h1c0-1 1-2 1-3h1s0 2-1 2v2c-1 0-1 1-2 2h-1c0-1 0-1-1-2 0 0 0-1-1-1l1-13z" class="U"></path><path d="M293 398h1c0 1 0 1 1 2h1 0c0 1 0 1 1 2-1 2-2 2-1 5h1v1c1 0 2 0 3-1v3c0 2 1 4 0 5v1 2 5c-1 1-1 1-2 1 1 1 1 1 1 2h-4v1h1c-1 1 0 1-1 2-1-1-1-1-1-2h-1v1l-1-1h-1c0-2 0-2-1-3v-2c1-1 1-5 0-6v-2c1-2 0-6 1-8h1 1v-3c0-2-1-3 0-5z" class="T"></path><path d="M293 420h2v1c-1 1-1 1-2 1-1-1 0-2 0-2z" class="b"></path><path d="M293 422l-1 1h-1v-4h1l1 1s-1 1 0 2z" class="Q"></path><path d="M291 414c1 0 1-1 2-2 1 1 1 1 1 3l-1 1h-1l-1-1v-1z" class="K"></path><path d="M291 414v-7h1c1 2 1 3 1 5-1 1-1 2-2 2z" class="J"></path><path d="M300 407v3c0 2 1 4 0 5v1 2 5c-1 1-1 1-2 1l-1-1c0-1-1-1-1-2s1-1 1-1l1-1c-1-1-2-2-2-3s1-2 1-2c-1-1-1-1-1-2-1-1 0-2 0-2l1-1v-1c1 0 2 0 3-1z" class="R"></path><path d="M300 418v5c-1 1-1 1-2 1l-1-1 2-3h-1l1-1 1-1z" class="o"></path><path d="M479 175l13 1c1 0 2-1 3-1s3 0 3 1l1 1c1-1 1-2 2-2h3c1 0 1 0 1 1h1c-2 2-4 5-5 7-1 1-2 1-2 2l-1 1c-1 0-1-1-2-1v-2h-1v-1l-2-2v1h-8c-1-1-1 0-1 0-1 0-1-1-1-1h-4-6-1-4c0-1 0 0-1 0h-2v-3l1-1c2 0 4 0 6 1h2c1 0 2-1 2-1l2-1h1z" class="g"></path><path d="M479 175l13 1c1 0 2-1 3-1s3 0 3 1l1 1c1-1 1-2 2-2 0 1-1 2-2 3l-2-1c-1-1-1-1-2-1h0l-1 1-5 1c-1-1-1 0-2 0l-1-1h-2-2-4s0 1-1 1c0-1-1-1-1-2l2-1h1z" class="F"></path><path d="M489 178c0-1 0-1 1-2h5l-1 1-5 1z" class="d"></path><path d="M347 448v4h1s1 0 1-1c1 1 1 1 1 2s-1 2-1 3l2 2c-1 0 0 1-1 2h1v1c0 1-1 1-1 2h-1c0 1-1 2-2 2v1h-1v2h-2v1h-1 0l-1-1c-1 1-1 1-2 1h-1-1 0v-4h-1l-1-1h-1c0-1-1-1-1-2l-1-1v-3c0-1-1-2-1-2 0-1 1-1 1-1v-4c1 1 1 2 2 3h0 3c1 1 1 2 2 2l1 1v1l1-1v-4c1 1 1 1 1 2l1-1v-1l2-2v-1-1l1-1z" class="G"></path><path d="M339 463v-1h3 0c1 1 1 1 2 1l1-1c1 0 1 0 1 1s1 2 1 2v1h-1v2h-2v1h-1 0l-1-1c-1 1-1 1-2 1h-1-1 0v-4-1l1-1z" class="D"></path><path d="M346 463c0 1 1 2 1 2v1h-1v2h-2v1h-1 0v-3l1-1c1-1 2-1 2-2z" class="B"></path><path d="M344 465l1 1c0 1-1 2-1 2v1h-1 0v-3l1-1z" class="F"></path><path d="M338 465v-1l1-1v1l1 1 1-1v1 2l2-1v3l-1-1c-1 1-1 1-2 1h-1-1 0v-4z" class="V"></path><path d="M339 464l1 1 1-1v1 2l-1 1-1-1v-3z" class="G"></path><path d="M333 451c1 1 1 2 2 3h0 3c1 1 1 2 2 2l1 1v1h1 1c1 1 1 1 1 2l-2 1-2-2-1-1h-1v4 1 2h-1l-1-1h-1c0-1-1-1-1-2l-1-1v-3c0-1-1-2-1-2 0-1 1-1 1-1v-4z" class="K"></path><defs><linearGradient id="AL" x1="488.767" y1="191.264" x2="469.989" y2="173.597" xlink:href="#B"><stop offset="0" stop-color="#19171b"></stop><stop offset="1" stop-color="#31322f"></stop></linearGradient></defs><path fill="url(#AL)" d="M467 180c1 0 1-1 1 0h4 1 6 4s0 1 1 1c0 0 0-1 1 0h8v-1l2 2v1h1v2c1 0 1 1 2 1-1 1 0 1-1 1v1c-1 1 0 0-1 0-1 1-1 2-2 2l-1-1h0v-1h-1v-1l-1-1h-1c-1 0-1-1-2-1h-2c1 2 1 2 0 3-2-2-6-1-9-1h-6v-1h-5s0-1 1-1c-1-1-2 0-3-1-2 0-2 0-4 1v-3c1-1 1-1 2-1v-1h3 2z"></path><path d="M467 180h1c-1 1-2 1-4 1h-2 0c1 1 2 2 3 2h5c1 0 2 0 3 1v1 1h2c1-1 5 0 7 0 1 0 3 0 4-1 1 2 1 2 0 3-2-2-6-1-9-1h-6v-1h-5s0-1 1-1c-1-1-2 0-3-1-2 0-2 0-4 1v-3c1-1 1-1 2-1v-1h3 2z" class="I"></path><path d="M164 207h1c1 0 0 1 2 1l-2 2 1 1h1l1 3v-1c1 1 2 1 3 2-1 0-2 1-2 2h1l1-1v2c0 2-1 2-1 3v2c-1 1-2 2-2 4 0 1-1 1-1 2l-2 1c0 1-1 2-1 4h-1v-1c-1 0-2 1-3 1l-1-10-2-7 1-1c0-1 0-3 1-4 0-1 1-2 2-2l1 2h1c-1-1-1-2-1-3 1 0 1-1 2-2z" class="g"></path><path d="M163 212l1 1h0l-1 1v2c-1-1-1-3-2-4h1 1z" class="K"></path><path d="M158 216c0 2 1 4 2 5l1 1c0 1-1 1-1 2h-1l-2-7 1-1z" class="X"></path><path d="M159 224h1c0 2 1 4 1 5 1 0 2 1 3 2 0-2 1-3 1-5h1 0c0 1 0 2-1 4 0 1-1 2-1 4h-1v-1c-1 0-2 1-3 1l-1-10z" class="M"></path><path d="M168 214v-1c1 1 2 1 3 2-1 0-2 1-2 2h1l1-1v2c0 2-1 2-1 3v2c-1 1-2 2-2 4 0 1-1 1-1 2l-2 1c1-2 1-3 1-4h0-1v-1-1-1-3-2c1-1 2-2 3-2v-2z" class="K"></path><path d="M166 220v-1c1 0 2 1 2 1 0 2-1 3-1 5-1 0-1 0-2-1v-1-3h1z" class="E"></path><path d="M165 220h1l1 1c0 1 0 1-1 2h-1v-3z" class="I"></path><path d="M343 432v-2h1v3h0 1c1 2-1 7 1 9 0 1 0 5 1 6h0l-1 1v1 1l-2 2v1l-1 1c0-1 0-1-1-2v4l-1 1v-1l-1-1c-1 0-1-1-2-2h-3 0c-1-1-1-2-2-3v-1c0-3 0-5 1-8v-1l-2 1v-3h2s0-1 1-2h2v-2-1l2 1c1-1 1-2 2-3v1h2v-1z" class="G"></path><path d="M341 437l1-1h0l1 3h-1c-1-1-1-2-1-2z" class="M"></path><path d="M343 437l1 1v2s0 1-1 1h0v-4z" class="U"></path><path d="M333 450h1 1 0v4c-1-1-1-2-2-3v-1z" class="B"></path><path d="M343 432v-2h1v3 5l-1-1v-4-1z" class="T"></path><path d="M337 438c0 1 1 2 1 3v1h0c-1 1-1 2-2 2h-1l1-2h-1c0-2 2-2 2-3v-1z" class="F"></path><path d="M338 444h1v2 1h0v1c0 1 0 1-1 2v-1-2h-1l-2 1h0v-3h1l1 1c1-1 1-1 1-2z" class="L"></path><path d="M341 444h2c0 3 0 6-1 9v4l-1 1v-1-7-3c0-1 0-1 1-2l-1-1z" class="F"></path><path d="M341 447h1v3h-1v-3z" class="Z"></path><path d="M341 437s0 1 1 2h1v2h0v1 2h-2c-1-1 0-3-1-4l-2 2v-1c0-1-1-2-1-3l2-1h1 1z" class="R"></path><path d="M344 433h0 1c1 2-1 7 1 9 0 1 0 5 1 6h0l-1 1v1 1l-2 2v1l-1 1c0-1 0-1-1-2 1-3 1-6 1-9v-2-1c1 0 1-1 1-1v-2-5z" class="F"></path><path d="M343 441c1 0 1-1 1-1v5c1 1 0 2 0 2h-1v-5-1z" class="T"></path><path d="M343 444v-2 5h1v2c1 0 1 0 1 1l1 1-2 2v1l-1 1c0-1 0-1-1-2 1-3 1-6 1-9z" class="D"></path><path d="M344 449c1 0 1 0 1 1l1 1-2 2v-4z" class="R"></path><path d="M366 623h0l1 1 3-1h0l1 1h2v1l2 1 2-1h0c1-1 1-1 2 0 1 0 1 1 1 2h0 1l1 2-1 1h0c-1 0-1 1-2 1v1h1 1c0 2 0 2-1 3-1 0 0 0-1 1v1h-6v1c-2 0-2-1-3-1h-4 0-3-2c0-1 0-1-1-2h0v-4c1-2 1-1 3-2l1-1h-1c-1 0-1-1-2-1h0c-1 1-1 1-2 1v-1c1 0 1-1 2-1-1-1-1-1-2-1l2-2h0c1 1 2 1 3 1s1 0 1-1h1z" class="N"></path><path d="M360 631h2v1 2c0 1 0 2 1 3h-2c0-1 0-1-1-2h0v-4z" class="T"></path><path d="M366 623h0l1 1 3-1h0l1 1h2v1l2 1 2-1h0c1-1 1-1 2 0 1 0 1 1 1 2h0 1l1 2-1 1h-2l1-2c-1 0-2 1-2 0h-1v1 2h-1c-1 0-3 0-4-1-1 0-1-1-2-1 0 1 0 1-1 1-1-1-1-1-1-2h-4-1c-1 0-1-1-2-1h0c-1 1-1 1-2 1v-1c1 0 1-1 2-1-1-1-1-1-2-1l2-2h0c1 1 2 1 3 1s1 0 1-1h1z" class="Q"></path><path d="M361 627l1-1h4l1 1h1v1h-4-1c-1 0-1-1-2-1z" class="P"></path><path d="M371 624h2v1l2 1h0-3v1h4v1s-1 1-2 1c-2-1-3-2-4-3 0-1 0-1 1-2z" class="M"></path><path d="M375 626l2-1h0c1-1 1-1 2 0 1 0 1 1 1 2h0 1l1 2-1 1h-2l1-2c-1 0-2 1-2 0h-1v1l-1-1v-1h-4v-1h3 0z" class="H"></path><path d="M259 632c1-1 2-2 3-2h1v-1h-1-1 0v-1c1 0 1-1 2-1v-1l-2 1s-1 0-1-1c0 0 0-1 1-1h1c1 0 3 0 4-1h0c2 0 2 1 4 0l-1-2c1 0 1 0 1-1h5c0 2-2 1-3 1s-1 1-1 2h0c2 0 4-1 6-1v-1l-1-1h1c1 1 2 1 3 1h0 3c1-1 4-1 5 0 2 0 4 0 5-1h1v1h8c1-1 2-1 3-1v2h-2 0-2-1-5c2 1 5 1 7 1h-3c-1 1-2 1-2 2v1h0l-1 1h-1v-2l-1-1-2 2c-1 0-1 0-1 1-2 0-2 0-4 1h0c-2 0-5 1-7 0l-1-1c-1 0-1 1-2 1-1 1-2 1-3 1l-1-1h-1v-1h0v-1h-2c-1 1-1 1-1 2l-1 1h1v1h-2l-1 1c0 1-1 1-1 2v-1c-1 0-1 0-2 1-1 0-2 1-3 2-1 0-2-1-3 0h-2v1-2l1-1c1 0 2-1 3-2z" class="G"></path><path d="M294 625h-1l1-1h5c-1 1-2 1-2 2v1h0l-1 1h-1v-2l-1-1z" class="c"></path><path d="M273 629h0v-1s1 0 1-1c1 0 1 0 1-1l1-1s1 0 1-1h0 2 0 1c1 0 5 0 5 1-1 1-1 0-2 1l-1 1h-2l-1 1c-1 0-1 1-2 1-1 1-2 1-3 1l-1-1z" class="E"></path><path d="M256 634h3c1-1 2-2 3-2 1-1 2-1 2-1v-3l-1-1h1c1-1 1-1 2-1l1 1 1 1c-1 0-2 1-2 1v1h2 1v1h-2l-1 1c0 1-1 1-1 2v-1c-1 0-1 0-2 1-1 0-2 1-3 2-1 0-2-1-3 0h-2v1-2l1-1z" class="O"></path><path d="M344 469v4 2l1-1 1-1h3c1 1 1 2 1 3-1 0-1 3-1 3v5 2l1 1v-2h1v1c1 0 1 0 2 1h0l1 1v1h-1c-1 1-1 2-3 2v2-2h-1c-1 1-1 2-1 3s1 1 1 1v4 1 1l-1 1 1 1h0c0 2-1 2-1 3l-1 1h-1 0-1c-1-1-1-3-1-4l1-1v-1l-1-1h-1c0-1-1-1-1-1v-3c-1 0-1 1-2 1-1-1-1-1-1-2v-3-2-2c0-1 0-1 1-2l1-1h0v-1-1-6-4c0-2 1-2 2-4h1z" class="I"></path><path d="M343 500v-3-3h1v1c0 1 0 2 1 3 0 1 0 2-1 2h-1z" class="Z"></path><path d="M346 491l1 4v1h-1l1 2h1v-2l1-1v4 1 1l-1 1 1 1h0c0 2-1 2-1 3l-1 1h-1 0-1c-1-1-1-3-1-4l1-1v-1l-1-1c1 0 1-1 1-2-1-1-1-2-1-3 1 0 1 0 2-1v-3z" class="C"></path><path d="M346 501c0-1 0-2 1-3 0 1 1 1 2 1h0v1c-1 0-2 0-3 1z" class="Q"></path><path d="M346 501c1-1 2-1 3-1v1l-1 1 1 1h-3v3c-1-1 0-4 0-5z" class="I"></path><path d="M346 506v-3h3 0c0 2-1 2-1 3l-1 1h-1v-1z" class="X"></path><path d="M347 484l-1-1c0-1 0-3 1-4h0 0c1 0 1-1 1-1h0 1v1 5 2l1 1v-2h1v1c1 0 1 0 2 1h0l1 1v1h-1c-1 1-1 2-3 2v2-2h-1c-1 1-1 2-1 3s1 1 1 1l-1 1v2h-1l-1-2h1v-1l-1-4c0-1 0-3 1-4v-3z" class="M"></path><path d="M351 486c1 0 1 0 2 1h0l1 1v1h-1c-1 1-1 2-3 2v2-2h-1l1-1c0-1-1-1-1-2h1l1-2z" class="T"></path><path d="M347 484l-1-1c0-1 0-3 1-4h0 0c1 0 1-1 1-1h0 1v1 5c-1 1-2 3-1 4 0 2-1 5-1 7l-1-4c0-1 0-3 1-4v-3z" class="G"></path><path d="M344 469v4 2l1-1 1-1h3c1 1 1 2 1 3-1 0-1 3-1 3v-1h-1 0s0 1-1 1h0 0c-1 1-1 3-1 4l1 1c-1 0-1 1-2 1v-5c-1 1 0 4-1 5l-2-1v1h-1v-1-1-6-4c0-2 1-2 2-4h1z" class="H"></path><path d="M344 469v4h-1c-1 1-1 6-1 8v2h-1v-6-4c0-2 1-2 2-4h1z" class="N"></path><path d="M466 258c0-1 0-1 1-1h1l1 1h0c1 0 1 0 1-1 1 1 1 1 1 2h1c-1 1-1 2-1 3s1 1 1 2c0 0-1 0-1 1s0 1 1 1l-2 1v1 3h1c0 1-2 1-1 3h1v1l-2-1v2c-1 2 0 4 0 5-1 1-1 1-1 2v5 1 2c-1 0-1 0-1-1h0v-1l-2 1c0 1 1 2 1 2l-2 2h0 0c-1 0-2 2-3 3 0 1 1 1 0 2-1 0-1-1-1-1v-1h-1c0-1 0-1 1-2h-1-1c0-1 0-4-1-5v-1l1-1s-1 0-1-1h0v-1l3-3h0l1 1c0-1 1-1 1-1h-1v-3c1 0 1-1 2-1 0 1 1 2 1 3h1c0-1 1-2 1-3v-1h-1c0-1-1-1-1-2 0-2-1-3-1-5v-2c-1 0-1-1-1-2 0-2-1-4-2-7 1 0 1-1 2-1h1v-1h2v1h0l1-1z" class="S"></path><path d="M469 258h0c1 0 1 0 1-1 1 1 1 1 1 2 0 0-1 1-1 2v2h-1c0-2-1-2-1-3l1-2z" class="R"></path><path d="M463 259v-1h2v1h0c1 2 0 4 1 7v4 1c-1 0-1-1-2-1 0-1 0-1 1-2-1-2-1-3-1-4l-2-5h1z" class="f"></path><path d="M460 260c1 0 1-1 2-1l2 5c0 1 0 2 1 4-1 1-1 1-1 2h0l-1 1v-2c-1 0-1-1-1-2 0-2-1-4-2-7z" class="Y"></path><path d="M370 616l1-1h-1-2v-1c1 0 1-1 1-1l1-1v1l2-2c-1 0-1-1-1-2h0c1 0 2 1 2 1v1h1c1 1 1 2 3 3h0 0 1c1 0 2 1 2 1h2 0c3 1 4 3 7 5 0 1 1 1 1 1 1 1 2 2 3 2l1 1 10 5c1 1 4 2 5 3h-1c-2 0-5-2-7-3-1 0-1 0-2 1-1-1-3-2-5-2v1l1 1-1 1h0-2 0l-1 1-1 1s0 1-1 1l-1-1h0c-1 0-1-1-2-1v1c0-1-1-1-1-2v-1c0-1-1-1-2-1 0-1 0-1 1-2h-1-2-1 0c0-1 0-2-1-2-1-1-1-1-2 0h0l-2 1-2-1v-1h-2l-1-1c1 0 1-1 2-1v-1-1-2s-2-1-2-2z" class="b"></path><path d="M372 615h0l3 3c1 0 1 0 1 1h-1c-2-1-3-1-3-4z" class="P"></path><path d="M372 621c2 0 3 0 5 1h1c1 0 1 1 1 1 1 0 2 0 3 1 0 1 1 1 1 2h0-2-1v1h0c0-1 0-2-1-2-1-1-1-1-2 0h0l-2 1-2-1v-1h-2l-1-1c1 0 1-1 2-1v-1z" class="D"></path><path d="M372 621c2 0 3 0 5 1v1h-2c-1-1-1-1-2-1v1h1 0s0 1-1 1h-2l-1-1c1 0 1-1 2-1v-1z" class="B"></path><path d="M382 615c3 1 4 3 7 5 0 1 1 1 1 1 1 1 2 2 3 2l1 1 10 5c1 1 4 2 5 3h-1c-2 0-5-2-7-3l-1-1-4-2-1-1c-1 0-1 0-2-1h0c-2-1-2-1-3-1h0c1 2 2 2 3 3h0v2h-2c0-1-1-1-1-2l-1-1c-2-1-3-2-4-3-1 0-2-2-3-2-1-1-2-1-3-2h-1-1c1-1 3 0 4-1 0 0 1 0 1-1v-1z" class="O"></path><path d="M360 608c1 0 2 0 2 1v-1h2c1 1 0 2 1 2s2 0 2-1-1-2 0-3 1-1 1-2v-1h-1l-1-1 1-1 2 2h0c0 1 0 3 1 3v1h0 0 2v1l2 2h1l-1 1h-1v-1s-1-1-2-1h0c0 1 0 2 1 2l-2 2v-1l-1 1s0 1-1 1v1h2 1l-1 1c0 1 2 2 2 2v2 1 1c-1 0-1 1-2 1h0l-3 1-1-1h0-1c0 1 0 1-1 1s-2 0-3-1h0-1v-1l1-1h-1 0-1l-1-2c-1-1-3-1-4-1h2 0l-3-1h-3c-1-1-2-1-3-1v-1h2c0-1-1-1-1-2h0v-1l-1-1h-1l-1-1 1-1h0c1-1 2-2 2-4h-1-2l-1-2h2s0 1 1 1h1l1 1c1 0 0-2 2 0h0c0 1 1 1 1 2s-1 1-1 3c1 1 1 1 0 2 1-1 2-2 2-3s1-2 2-3l1 1c2 0 1 1 2 2h0l1-1h1z" class="T"></path><path d="M359 614v1h0c1 1 3 2 3 3l-1 2c-1 0-1 0-1 1h0-1l-1-2c-1-1-3-1-4-1h2 0l-3-1h1 1c1 0 2-1 2-2v-1h2z" class="F"></path><path d="M359 614v1s-1 1-1 2 1 2 2 2v2h-1l-1-2c-1-1-3-1-4-1h2 0l-3-1h1 1c1 0 2-1 2-2v-1h2z" class="G"></path><path d="M351 612c1-1 2-2 2-3s1-2 2-3l1 1c2 0 1 1 2 2h0l1-1h1l3 3h0l-1 1h-1c-1 1-2 0-3 0l-1 1h-1v-1-1-1h-1 0c1 1 0 2 0 3 1 1 1 1 1 2v-1h1 2-2v1h-4v-2h-1-1v-1z" class="B"></path><path d="M345 605l-1-2h2s0 1 1 1h1l1 1c1 0 0-2 2 0h0c0 1 1 1 1 2s-1 1-1 3c1 1 1 1 0 2v1h1 1v2h4c0 1-1 2-2 2h-1-1-3c-1-1-2-1-3-1v-1h2c0-1-1-1-1-2h0v-1l-1-1h-1l-1-1 1-1h0c1-1 2-2 2-4h-1-2z" class="S"></path><path d="M348 605c1 1 1 1 1 3-1 0-2 1-3 1 1-1 2-2 2-4z" class="B"></path><path d="M366 617h1l1-2h1l1 1c0 1 2 2 2 2v2 1 1c-1 0-1 1-2 1h0l-3 1-1-1h0-1c0 1 0 1-1 1s-2 0-3-1h0-1v-1l1-1h-1c0-1 0-1 1-1h0c1 0 2-1 2-2h0c1 0 1 0 2-1h1z" class="K"></path><path d="M366 621c1-1 2-1 3-2 0 1 2 1 3 1v1 1-1c-2 0-3 0-5 1h0l-1-1z" class="F"></path><path d="M366 623h2v-1h-2v-1l1 1h0c2-1 3-1 5-1v1c-1 0-1 1-2 1h0l-3 1-1-1h0z" class="H"></path><path d="M361 620h0c1 0 2-1 2-2h0c1 0 1 0 2-1h1s0 1-1 2v3l-1 1h0c-1-1-2-1-3-2h-1c0-1 0-1 1-1z" class="E"></path><path d="M404 175h22 6 2l1 1h2 5 3l-1 2c1 0 1 0 2 1h-2c0 1-1 0-1 1h0c-1 1-1 1-3 1h0c-1 0-2 1-3 1l-1-1h-1v2h-1-2s-1 0-2 1h2v2c-2 0-6-1-7 0-2 0-4 1-5 0l-1-1h-1-6v-1l1-2h0-3c-1 0-1 0-2 1l-1-2c-1 0-1 1-2 0v-2h-3v-1s-1 0-1-1 0-1 1-2h2z" class="B"></path><path d="M427 179h1c2-1 2-3 4-2l1 1-1 1h-1v-1c-1 1-1 2-1 2v1c2-1 2-1 4-1l6-1 1 1h2c-1 1-1 1-3 1h0c-1 0-2 1-3 1l-1-1h-1v2h-1-2s-1 0-2 1c-2 0-5-1-7-1l-1 1h0c-1 0-1-1-1-1-1 0-1 1-2 0h-1v1h-1v-1c1-1 2-1 3-2h0c2 1 4 1 5 1h1v-1-2h1z" class="E"></path><path d="M426 175h6 2l1 1h2 5 3l-1 2c1 0 1 0 2 1h-2c0 1-1 0-1 1h0-2l-1-1c-1 0-6 1-6 1-2 0-2 0-4 1v-1s0-1 1-2v1h1l1-1-1-1c-2-1-2 1-4 2h-1v-3h-1v-1z" class="D"></path><path d="M404 175h22v1h1v3h-1 0c-1 1-1 1-3 1h-6c-3 1-7 2-10 1-1 0-1 1-2 0v-2h-3v-1s-1 0-1-1 0-1 1-2h2z" class="K"></path><path d="M426 176h1v3h-1 0c-5 0-8 0-13 1 0 0-1 0-1-1h2c1 0 1-1 2-1h4 0 1c1 1 3 0 4 0s1-2 1-2z" class="b"></path><path d="M413 180c5-1 8-1 13-1-1 1-1 1-3 1h-6c-3 1-7 2-10 1 2-2 4 0 6-1z" class="M"></path><path d="M262 175h5v1h-1v1 1c1 1 3 1 4 1s1-1 2-1l1 2h2 1 2v1h-2-2-1c0 1 0 2-1 3 1 1 1 3 2 5h0c1 1 1 2 2 2v2 1h3c0 1-1 1-1 1v1l1 1h-2l-1 3h0-2-9c-3 0-6 0-9 1 1-1 1-2 1-3 1 0 1 0 1-1 1-1 1-2 1-3h2v1-1c1 0 1 0 2-1l1 1h1l-1-1h-1c-1-2-1-3-1-4l-1-2c1 0 1 0 2-1-2 0-3 0-4-1v-2-1c-1 0-1 0-1-1 1 0 1 0 2-1l-2-1v-1-1-1c1-1 1-1 3-1h0 1z" class="H"></path><path d="M266 199l3-1c1-1 1-1 0-2h0 4c0 1 0 2 1 3v1h-9c0-1 0-1 1-2v1z" class="L"></path><path d="M261 194c1 0 1 0 2-1l1 1v1c1-1 1-1 2-1v1l-1 1v1c1-1 1-1 2-1h1 0c0 1-1 1-1 2l-1 1v-1c-1 1-1 1-1 2-3 0-6 0-9 1 1-1 1-2 1-3 1 0 1 0 1-1 1-1 1-2 1-3h2v1-1z" class="V"></path><path d="M261 194c1 0 1 0 2-1l1 1v1h0l-1 1-2-2z" class="Q"></path><path d="M273 196v-2l-1-1c-2 0-2 0-3 1-1 0-1-1-2-1h-1c-1-2 0-2 0-4h-1c0-1-1-2-1-3s-1-1-1-1c0-1-1-2-1-2 1-1 1-1 2-1l1 1h0l2 2v1h1v1 1l1 1c1 1 0 2 1 3 1 0 2-1 2-2 1-1 1-1 2-1 1 1 1 2 2 2v2 1h3c0 1-1 1-1 1v1l1 1h-2l-1 3h0-2v-1c-1-1-1-2-1-3z" class="T"></path><path d="M265 183h0l2 2v1l-2-1h-1l1-2zm9 16c1-1 2-1 3-2l-1 3h0-2v-1z" class="B"></path><path d="M262 175h5v1h-1v1 1c1 1 3 1 4 1s1-1 2-1l1 2h2 1 2v1h-2-2-1c0 1 0 2-1 3 1 1 1 3 2 5h0c-1 0-1 0-2 1 0 1-1 2-2 2-1-1 0-2-1-3l-1-1v-1-1h-1v-1l-2-2h0l-1-1h-1c0-2-1-2-2-3s-1-1-2-1c0 1 1 1 2 2v1c-1 1-1 1-2 1s-1 0-1-1c1 0 1 0 2-1l-2-1v-1-1-1c1-1 1-1 3-1h0 1z" class="F"></path><path d="M262 175h5v1h-1v1 1c-1 0-2 0-2 1h-1c-1-1-2-2-3-2h-1l1-1h1v-1h0 1z" class="H"></path><path d="M268 186l1-1s-1-1-1-2c-2-1-3-1-4-3h0c2 0 3 0 4 1h-1v1l3 1h0c-1-1-1-1-1-2l2-1c0 1 1 1 2 1 0 1 0 2-1 3 1 1 1 3 2 5h0c-1 0-1 0-2 1 0 1-1 2-2 2-1-1 0-2-1-3l-1-1v-1-1z" class="K"></path><path d="M268 187c1-1 2-1 3 0h1v1h-2v1c1 0 1 1 2 1 0 1-1 2-2 2-1-1 0-2-1-3l-1-1v-1z" class="R"></path><path d="M354 237c0-1 0-1 1-2l1 1 1 1-1 28v3 1l-1-1h-1 0c0 1 1 2 0 2h1 0c1 0 1 0 1 1 1 2 0 3 1 5v1l-1 1v7l1 13v2c-1-1-2-1-3-2l-1 1h0 1v1s0 1 1 2l-2 2v-1-1h-1l-1 1v1h-1v-6-4-1-6-23c0-3-1-7 0-10l1-1v-6-1s0-1 1-1h1 1 0v-1-1c-1-2 0-2 0-4l-1-1h0l1-1z" class="g"></path><path d="M350 287h0c1-3 0-6 1-8v18 6 1h-1v-6-4-1-6z" class="X"></path><path d="M350 287v-23c0-3-1-7 0-10l1-1v21 5c-1 2 0 5-1 8h0z" class="i"></path><path d="M357 298l-2-1v-4c-1 0-1 2-1 3l-1-1v-1c0-1 1-1 1-2h0l-1-1v-1-2h1c0-1 0-1-1-1 0-2 0-1 1-2 1 0 1-1 1-1 0-2 0-4-1-5 1-1 1-1 2-1v7l1 13zm-3-61c0-1 0-1 1-2l1 1 1 1-1 28v3 1l-1-1h-1 0c0 1 1 2 0 2h1 0c1 0 1 0 1 1s1 3 0 5c-1-1-2 0-3-1v-7-7c0-2 0-4 1-5v4 3 3h1v-10-4c1-2 0-5 0-7h-1 0c-1 1-1 1-2 1 0 1-1 1-1 1v-1s0-1 1-1h1 1 0v-1-1c-1-2 0-2 0-4l-1-1h0l1-1z" class="I"></path><path d="M354 243c-1-2 0-2 0-4l-1-1h0l1-1h1c0 1 1 1 1 2l-1 1v1 1 3h-1 0c-1 1-1 1-2 1 0 1-1 1-1 1v-1s0-1 1-1h1 1 0v-1-1z" class="P"></path><path d="M332 442l2-1v1c-1 3-1 5-1 8v1 4s-1 0-1 1c0 0 1 1 1 2v3l1 1c0 1 1 1 1 2h1l1 1h1v4h0 1 1c1 0 1 0 2-1l1 1h0c-1 2-2 2-2 4v4 6 1 1h0v1c-1 0-1-1-2-1h0c-1 0-1 0-2-1l-1 1v2 1h0 0-3c0-1 1-1 0-2h-1v-1h-2l-1 1c-1-1-1-1-2-1h0c0-2 0-3-1-4l1-2 1-1-1-1v-5-2-2h0v-4h0v-1l1-1v-1s-1-1-1-2v-4-1h2v-2h0l-1-1h1l-1-1v-1-1-1c1-1 1-2 1-3h0c2 0 1 1 2 1v-3h1 0z" class="O"></path><path d="M327 455v-1h2v5h1 0 1s0 1-1 1l-1 1h-1s-1-1-1-2v-4z" class="L"></path><path d="M328 447c1-1 1-2 1-3h0c2 0 1 1 2 1v-3h1c-1 2 0 3-1 5 0 1 0 3 1 4l-1 1h-1v-2h0c0-1-1-1-1-1h0-1v-1-1z" class="B"></path><path d="M330 460v3 2h0v5c1 1 0 3 0 4h-1l1 2c1 0 2 0 3-1v-6c1 2 0 6 0 8h1v1 2h-1-1v1 1 1c-1 0-2 0-2 1v1l-1 1c-1-1-1-1-2-1h0c0-2 0-3-1-4l1-2 1-1-1-1v-5-2-2h0v-4h0v-1l1-1v-1h1l1-1z" class="C"></path><path d="M330 476h-1c-1 0-1 0-2-1 0-1 0-1 1-2h1v1l1 2z" class="a"></path><path d="M330 460v3 2h0c0-1 0-1-1-2l-1 1c0 1 0 1 1 2l-2 2v-4h0v-1l1-1v-1h1l1-1z" class="N"></path><path d="M328 478l1 1h1v-2c2 0 1 1 3 1v2h-1v1l-1 1h-1v-1c-1-1-2-2-3-2l1-1z" class="G"></path><path d="M327 479c1 0 2 1 3 2v1h1l1-1v1 1c-1 0-2 0-2 1v1l-1 1c-1-1-1-1-2-1h0c0-2 0-3-1-4l1-2z" class="E"></path><path d="M334 462c0 1 1 1 1 2h1l1 1h1v4h0 1 1c1 0 1 0 2-1l1 1h0c-1 2-2 2-2 4v4 6 1 1h0v1c-1 0-1-1-2-1h0c-1 0-1 0-2-1l-1 1v2 1h0 0-3c0-1 1-1 0-2h-1v-1h-2v-1c0-1 1-1 2-1v-1-1-1h1 1v-2-1h-1c0-2 1-6 0-8l-1-1 1-2v-1c0-1 0-2 1-3z" class="G"></path><path d="M338 475v-1c-1-1 0-3 0-3h1c0 1 1 1 1 2h1v4c-1 0-1-1-2-2h-1z" class="I"></path><path d="M333 466h1v1 3h-1 0c1 1 1 1 1 2h1v-2h1c1 1 0 5 1 5h1c-1 2 0 2 0 3-2 0-3 1-4 2v-2-1h-1c0-2 1-6 0-8l-1-1 1-2z" class="L"></path><path d="M338 475h1c1 1 1 2 2 2v6 1 1h0v1c-1 0-1-1-2-1h0c-1 0-1 0-2-1l-1 1v2 1h0 0-3c0-1 1-1 0-2h-1v-1h-2v-1c0-1 1-1 2-1v-1-1-1h1 1c1-1 2-2 4-2 0-1-1-1 0-3h0z" class="Q"></path><path d="M332 480l2 1h0c0 1-1 1-1 1h-1v-1-1z" class="f"></path><path d="M332 483c1 1 1 1 1 2h-1-2v-1c0-1 1-1 2-1z" class="C"></path><path d="M335 482h0v-1c0-1 1-1 2-1l1 1v1h-3z" class="E"></path><path d="M335 482h3c0 1 1 1 1 1 1 1 1 1 2 1v1h0v1c-1 0-1-1-2-1h0c-1 0-1 0-2-1l-1 1v2 1h0 0c-1-1-1-1-2-1v-2h-1c1-1 1-2 1-2h1v-1z" class="J"></path><path d="M338 475h1c1 1 1 2 2 2v6 1c-1 0-1 0-2-1v-3l-1-1v-1c0-1-1-1 0-3h0z" class="E"></path><path d="M338 475h1v5l-1-1v-1c0-1-1-1 0-3h0z" class="X"></path><path d="M256 175h1 0l-1 1v2 1h0 0v1h0l1 2c0-1 1-1 1-2l-2-1 1-1h0v-2l2-2c1 1 2 1 2 1h1-1 0c-2 0-2 0-3 1v1 1 1l2 1c-1 1-1 1-2 1 0 1 0 1 1 1v1 2c1 1 2 1 4 1-1 1-1 1-2 1l1 2c0 1 0 2 1 4h1l1 1h-1l-1-1c-1 1-1 1-2 1v1-1h-2c0 1 0 2-1 3 0 1 0 1-1 1 0 1 0 2-1 3-1 0-1-1-2-1 1-2 2-3 2-5v-1l-1-2-1 1-1-1-1 1v-1l-2-1v-1h1v-1h-1c0-1-1-1-2-1v-1l-1-1h-1v1c0 1-1 1-1 2h0l-2-1-1-1v-1c1 0 1-1 1-1-1-1-2 0-2 0h-2l-4-1h0l-2-1s-2-1-2-2c-1-1-3 0-4-1-1 0-1 0-1-1h0 3l-2-3-1-1h19 11z" class="P"></path><path d="M259 192c0-1 0-1-1-2v-1h1c1 1 1 1 1 2l-1 1z" class="D"></path><path d="M260 191c1 1 1 2 1 3h-2v-2l1-1z" class="H"></path><path d="M253 185c1 0 2-1 3-1 0 2 0 2 1 2l1-1v2h-1c-2 0-3-1-4-2z" class="D"></path><path d="M256 184l-2-2v-1l1-1 2 3h0c0 1 0 1 1 2h0l-1 1c-1 0-1 0-1-2z" class="B"></path><path d="M250 189h1l-1-1v-1c2 0 2 0 3 1l2 2c-1 1 0 1 0 2l-1 1-1-1-1 1v-1l-2-1v-1h1v-1h-1z" class="C"></path><path d="M253 179c1 0 1 0 2-1v1 1l-1 1v1l2 2c-1 0-2 1-3 1l-1-2h-2v-1c-2-1-3-2-5-3 0 0 1 0 1-1l3 1c1 1 2 1 3 1h1v-1zm-16 1h5 0c0 1-1 1-1 2h2c2 0 4 3 6 3 1 0 1-1 2 0-1 1-2 1-3 2l-1-1h-1c-1 1-2 1-3 1h0c0-1 0-1 1-1v-1c-1-1-1-1-3-2 0-1-1-1-2-1l-3-1v-1h1z" class="E"></path><path d="M231 181h2 3l3 1c1 0 2 0 2 1 2 1 2 1 3 2v1c-1 0-1 0-1 1h0c1 0 2 0 3-1v1c0 1-1 1-1 2h0l-2-1-1-1v-1c1 0 1-1 1-1-1-1-2 0-2 0h-2l-4-1h0l-2-1s-2-1-2-2z" class="S"></path><path d="M245 175h11v1l-1 2c-1 1-1 1-2 1v1h-1c-1 0-2 0-3-1l-3-1c0 1-1 1-1 1h0-1l-2 1h0 0-5-1v1h-3-2c-1-1-3 0-4-1-1 0-1 0-1-1h0 3l-2-3-1-1h19z" class="B"></path><path d="M236 177c2 0 4 0 6-1v1c-1 0-1 1-2 1h-3c0-1-1 0-2 0 1-1 1 0 2-1h-2 1z" class="K"></path><path d="M237 180c-1 0-1 0-1-1 1-1 6 0 8 0l-2 1h0 0-5z" class="D"></path><path d="M250 177v-1c2 0 4-1 6 0l-1 2c-1 1-1 1-2 1v1h-1c-1 0-2 0-3-1 0 0 0-1-1-1 1-1 1-1 2-1z" class="L"></path><path d="M250 177h2v1h-1l-1 1h3v1h-1c-1 0-2 0-3-1 0 0 0-1-1-1 1-1 1-1 2-1z" class="D"></path><path d="M226 175h19l-1 2h-2v-1c-2 1-4 1-6 1v-1c-2 0-4 1-7 0h-2l-1-1z" class="L"></path><path d="M269 602c1-1 2-2 3-4 1 1 1 2 2 2v3h2l2-1c1 1 1 2 3 2l1 2c0 1 1 1 1 2h5 0l1 2h2v1h-5c3 1 5 0 8 0 1 0 3 1 4 0h9 6 0v1l-1 1h0v1h2 3c-1 1-1 1-2 1-2 1-2 1-3 1v1c0 1 0 1 1 1v1l-1 1h-2-3 0-4-1-2c0 1-1 1-1 1l-1-1c-1 0-3 0-3 1h-4c-1-1-2-1-3-1l-1 1h0-1c0-1-1-1-2-1v1c-1 0-2 0-3-1-2 0-3-1-5-1 0-1-1-1-1-1v-1h2 0 2v-1h-1v-2h1c0-1-1-2-2-3h0 0c-1 0-1 0-1-1h-1c-1-1-2-2-4-2v-1c0-1-1-2-2-3l-1-1 1-1z" class="O"></path><path d="M286 617l2-2c-1 0-2-1-2-2-1 0-1-1-1-1v-1h0c1 0 1 1 1 1v1l5 1h0l-1 1v1c-1 0-2 1-3 1h-1z" class="c"></path><path d="M269 602c1-1 2-2 3-4 1 1 1 2 2 2v3l-1 2c-1-1-2-1-2-2l1-1c0 1 0 1 1 1h0c-1-1-1-2-2-2 0 1-1 2-2 3l-1-1 1-1z" class="B"></path><path d="M279 614l2-1 1 1c0 1-1 3-2 4v1h-1c-2-1-3-1-4-1v-1h2 0 2v-1h-1v-2h1z" class="L"></path><path d="M290 615c1 1 1 1 3 1l-2 1-1 1v1h-1l-8 1c2-1 3-2 4-3h1 1c1 0 2-1 3-1v-1z" class="X"></path><path d="M290 615c1 1 1 1 3 1l-2 1-1 1c-2 0-3 0-5-1h1 1c1 0 2-1 3-1v-1z" class="E"></path><path d="M293 616h4v1h-2v1c1 1 2 0 2 0 1 0 1 0 2-1v1h4v1l-9 1c-2 0-4 0-5-1h1v-1l1-1 2-1h0z" class="M"></path><path d="M291 617l3 2h0c-1 0-3 1-4 0v-1l1-1z" class="c"></path><path d="M278 602c1 1 1 2 3 2l1 2c0 1 1 1 1 2v1c-1 1-2 1-4 0v1c-2 1-3-1-4-2-2 0-2-1-3-2h-1l1-1h1l1-2h2l2-1z" class="Y"></path><defs><linearGradient id="AM" x1="299.068" y1="611.981" x2="299.524" y2="615.797" xlink:href="#B"><stop offset="0" stop-color="#1a181a"></stop><stop offset="1" stop-color="#343435"></stop></linearGradient></defs><path fill="url(#AM)" d="M307 611h6 0v1l-1 1h0v1h2 3c-1 1-1 1-2 1-2 1-2 1-3 1l-1-1c0 1-1 1-1 2v2h-1-6v-1h-4v-1c-1 1-1 1-2 1 0 0-1 1-2 0v-1h2v-1h-4 0c-2 0-2 0-3-1l1-1h0l-5-1v-1s0-1-1-1h1c3 1 5 0 8 0 1 0 3 1 4 0h9z"></path><path d="M295 614c2 0 6-1 7 0-1 1-2 1-4 1v1h5c-1 0-2 0-2 1h2v1h-4v-1c-1 1-1 1-2 1 0 0-1 1-2 0v-1h2v-1h-4l2-2z" class="I"></path><path d="M307 611h6 0v1l-1 1-8-1h-3c-3 1-6 0-9 1h0c1 1 2 1 3 1l-2 2h0c-2 0-2 0-3-1l1-1h0l-5-1v-1s0-1-1-1h1c3 1 5 0 8 0 1 0 3 1 4 0h9z" class="d"></path><path d="M286 612c5 0 11-1 15 0-3 1-6 0-9 1h0c1 1 2 1 3 1l-2 2h0c-2 0-2 0-3-1l1-1h0l-5-1v-1z" class="H"></path><path d="M304 612l8 1h0v1h2 3c-1 1-1 1-2 1-2 1-2 1-3 1l-1-1c0 1-1 1-1 2v2h-1-6v-1-1h-2c0-1 1-1 2-1h3c1-1 2-1 2-2h-3v-1h0l-1-1z" class="F"></path><path d="M305 613c1 0 5-1 6 0v1h-3-3v-1h0z" class="D"></path><path d="M345 605h2 1c0 2-1 3-2 4h0l-1 1 1 1h1l1 1v1h0c0 1 1 1 1 2h-2v1c-1 0-1 1-2 1h0-3l-1 1h-2-2-1v-1l-2 1h0-2-1l-2-1c1-1 1-1 1-3h-1c0 1 0 1-1 1h-4 0-2c-1-1-2-1-4-1h-1-3-2v-1h0l1-1v-1h0-6-9c-1 1-3 0-4 0-3 0-5 1-8 0h5v-1h-2l-1-2c0-1 1-1 1-1 1-1 1-1 2 0h4v-2c2 0 1 1 2 1 2 1 6 0 7 0 2 0 4 1 5 0h3 14l-1 2h8v-3c2 1 4 0 6 0h6z" class="i"></path><path d="M329 614l-1-1h0c2-1 4-1 5-1l1 1v1l-1 1h-1l-2-1h-1z" class="B"></path><path d="M313 612c3 1 7 1 10 0l2 1h1l-2 2h-2c-1-1-2-1-4-1h-1-3-2v-1h0l1-1z" class="D"></path><path d="M310 610h1v-1-1h1v1c0 1 1 1 1 1 1-1 3 0 4 0h3 10v1h-17 0-6c1 0 1-1 2-1h1z" class="T"></path><path d="M346 611h1l1 1v1h0c0 1 1 1 1 2h-2v1c-1 0-1 1-2 1h0-3l-1 1h-2-2-1v-1l-2 1h0-2-1l-2-1c1-1 1-1 1-3l2 1h1l1-1v-1l-1-1h1c1 0 3 0 5 1h0l2-2h5z" class="f"></path><path d="M347 611l1 1c0 1-1 2-2 2h-2-1 2v-2h2v-1z" class="Z"></path><path d="M334 618c0-1 1-2 1-3h0 1c1 0 1 0 2-1 0 0 1 0 1-1l1 1v2h-3l-1 1h0c1 0 1 0 1 1h-1v-1l-2 1h0z" class="G"></path><path d="M330 614l2 1h1l1-1v2c-1 0-1 1-2 2h-1l-2-1c1-1 1-1 1-3z" class="E"></path><path d="M340 616c1 0 3-1 4 0l1 1h0-3l-1 1h-2-2c0-1 0-1-1-1h0l1-1h3z" class="X"></path><path d="M348 612v1h0c0 1 1 1 1 2h-2v1c-1 0-1 1-2 1l-1-1c-1-1-3 0-4 0v-2h2v-1h0 1v1h1 2c1 0 2-1 2-2z" class="V"></path><path d="M345 605h2 1c0 2-1 3-2 4h0l-1 1 1 1h-5-11v-1h-10-3l1-1c-1 0-2-1-2-1v-1c2 1 5 1 7 1h2 8v-3c2 1 4 0 6 0h6z" class="C"></path><path d="M341 609l2 1h2l1 1h-5-11v-1h5 3 1 0c1-1 2-1 2-1h0z" class="D"></path><path d="M345 605h2 1c0 2-1 3-2 4h0l-1 1h-2l-2-1h0v-1-2c-2 1-2 0-3 1-2 1-3 1-5 1v-3c2 1 4 0 6 0h6z" class="F"></path><path d="M295 605c2 0 1 1 2 1 2 1 6 0 7 0 2 0 4 1 5 0h3 14l-1 2h-2c-2 0-5 0-7-1v1s1 1 2 1l-1 1c-1 0-3-1-4 0 0 0-1 0-1-1v-1h-1v1 1h-1-1c-1 0-1 1-2 1h-9c-1 1-3 0-4 0-3 0-5 1-8 0h5v-1h-2l-1-2c0-1 1-1 1-1 1-1 1-1 2 0h4v-2z" class="Q"></path><path d="M301 610h9-1c-1 0-1 1-2 1h-9c1-1 2-1 3-1z" class="O"></path><path d="M295 605c2 0 1 1 2 1 2 1 6 0 7 0v1l1 1h-2c-2 1-5 1-8 1v1h6c-1 0-2 0-3 1s-3 0-4 0c-3 0-5 1-8 0h5v-1h-2l-1-2c0-1 1-1 1-1 1-1 1-1 2 0h4v-2z" class="B"></path><path d="M295 605c2 0 1 1 2 1 2 1 6 0 7 0v1c-4 2-11 1-16 1 0-1 1-1 1-1 1-1 1-1 2 0h4v-2z" class="R"></path><path d="M198 218c1-1 2-3 4-3h1l-1 1c-1 1-3 2-4 3-6 5-12 10-14 17h0c0 1 0 1-1 2 0 1-1 2-1 3 0 0 0 1-1 1v1h0v1h0c-1 1-1 2-2 3v3s0 1-1 2v2 1l-1 2v3c-1 1 0 3 0 4h0v2h0c1 1 0 2 0 2v1l-1-1c-1 1-1 1-1 2-1-1-1-1-2-1v1h2v2h1v3h0v1 1l1 1v2 1h0l1 1v1c-1-1-1-3-2-4v-1c-1 1 0 1 0 3-1 0-1-1-1-1-1 0-1 0-1-1h0c-1-1-2-2-2-3h-1c0 1 1 1 1 2l2 3c1 1 1 1 1 2l3 4c0 1 2 3 3 4 0 0 2 2 2 3l1 1h0c1 2 4 4 6 5s4 3 6 4c3 2 6 3 9 4h0l-2 1-8-3c-14-7-24-22-29-36l1-1h0v1 1 1h1v1h0c0 1 1 1 1 2h0v1h0c1 0 1 1 1 1v1s0 1 1 1h0v1l1 1c0 2 3 6 5 8h0c1 1 1 2 2 2 3 5 8 9 13 12 0 1 2 1 2 2h2c-1-1-3-2-4-2l-3-3c-1 0-1-1-2-2h-1 0l-4-4c0-2-2-3-3-4-1-2-2-3-3-5s-2-4-4-5c0-1 0-2-1-3l-1-1v-2c0-1-1-1-1-2s0-1-1-2v-1l1 1h0v-1-2h0c0-3 0-4 1-7 0 0 0-1 1-1-1-1-1-3-1-4s-1-2-1-2h-1l1-1v1l1-1h1v1l2 1v-2-1-1c-1 1-1 1-3 1 0-1 0-1 1-2v-2c2-3 3-6 4-9 0-1 1-2 1-3s0-2 1-2c1-1 1-2 1-3 1 1 1 1 2 1v1 2c1-1 2-3 2-3 1 0 2-1 2-1h1 1c2-4 5-6 7-9 1 0 1-1 2-1s2-1 3-2z" class="U"></path><path d="M170 260s0-1 1-1c-1-1-1-3-1-4s-1-2-1-2h-1l1-1v1l1-1h1v1l2 1s-1 1-1 2h0v3l-1 1v1h1s1 0 0 1c-1 2 1 3 0 5-1-1-1-3-1-4v-1l-1-2z" class="L"></path><path d="M178 230c1 1 1 1 2 1v1 2c-1 2-2 5-3 7 0 1-1 3-2 4-1-1-1-2-1-3l1-1c0-1 0-2 1-2l-1-1h0c0-1 1-2 1-3s0-2 1-2c1-1 1-2 1-3z" class="Y"></path><path d="M198 218c1-1 2-3 4-3h1l-1 1c-1 1-3 2-4 3-6 5-12 10-14 17-1 1-1 2-2 3h0v1l-3 6c-1 2-1 4-2 7 0 2 0 5-1 6-1 0-1 0-2-1 1-1 1-1 1-2h-1c0-1 1-1 1-1 0-1 1-3 1-4 1-3 3-6 4-9 0-1 0-2 1-3h0v-1l5-8c2-4 5-6 7-9 1 0 1-1 2-1s2-1 3-2z" class="V"></path><path d="M188 175c1-1 4 0 5 0h13 5c2 0 5 0 6-1h0c2 1 3 0 5 1h2 2 0l1 1 2 3h-3 0c0 1 0 1 1 1 1 1 3 0 4 1 0 1 2 2 2 2l-2 2h0-3 0l-3-1h-1-3-2-5-1-1c-1-1-1-1-2-1l-13 1h-7-3-1c-1 0-2 1-3 2h0c-1-1-1-2-2-3l-1 1h-1-4v-2h0v-3h2c1-1 1-1 1-2-2-1-2 0-4 0 0 0 0-1-2-1l1 1v1c-2 0-1-1-2-1s-3 0-4 1v-1c1-1 2-1 3-1s1 0 2-1h0 16z" class="L"></path><path d="M180 179h2v1l-3 1-1-1h0c0-1 1-1 2-1z" class="P"></path><path d="M180 179c0-1 1-1 1-2 1 0 2 0 4 1-1 0-2 1-3 1h-2z" class="N"></path><path d="M182 180h4v1h1-3c-1 0-1 0-2 1h0l-1 1-1 1-2-1 1-1-1-1h1l3-1z" class="F"></path><path d="M182 182h0l-1 1-1 1-2-1 1-1h3z" class="K"></path><path d="M215 178h0-4l-1-1h5 0 3v1c1 0 1 1 2 2v1h0c-2 1-2 1-3 0l1-1c-1 0-1 0-1-1-1 0-1 0-2-1z" class="B"></path><path d="M203 180c1-1 2-1 3-3h1 2 0 0v1l-1 1h1 4c0 1-1 1-2 1h-3-1c-1 0-1 1-1 0-2 0-2 0-3 1v-1z" class="D"></path><path d="M215 178c1 1 1 1 2 1 0 1 0 1 1 1l-1 1-1 1c1 2 2 2 3 2h-5-1-1c-1-1-1-1-2-1 1 0 1-1 1-1v-1c-1 0-1 0-1 1h-2v-2h3c1 0 2 0 2-1h1l1-1z" class="E"></path><path d="M215 178c1 1 1 1 2 1 0 1 0 1 1 1l-1 1-1 1h0c-1 0-1 0-2-1v-2l1-1z" class="D"></path><path d="M185 178c1 0 2 0 4-1 0 1 1 1 2 1v-1c1 0 1 0 2 1h1c1 0 1-1 2 0h3 1l1-1c1 0 1 1 2 1 0 0 0 1-1 1l-1 1v-1h-1-2v3h0c-1 0-2 0-3 1v-1-1c-2-1-6 0-8 0h-1v-1h-4v-1c1 0 2-1 3-1z" class="U"></path><path d="M186 180c3-1 8-1 12-1v3h0c-1 0-2 0-3 1v-1-1c-2-1-6 0-8 0h-1v-1z" class="R"></path><path d="M198 179h2 1v1l1-1 1 1v1c1-1 1-1 3-1 0 1 0 0 1 0h1v2h2c0-1 0-1 1-1v1s0 1-1 1l-13 1h-7-3-1c-1 0-2 1-3 2h0c-1-1-1-2-2-3l1-1h0c1-1 1-1 2-1h3c2 0 6-1 8 0v1 1c1-1 2-1 3-1h0v-3z" class="D"></path><path d="M189 183c2-1 4-1 6-1v1c-2 0-4 1-6 0z" class="Q"></path><path d="M201 180l1-1 1 1v1c1-1 1-1 3-1 0 1 0 0 1 0h1v2c-3 0-7 1-9 0v-1c1 0 2-1 2-1z" class="R"></path><path d="M187 181c2 0 6-1 8 0v1c-2 0-4 0-6 1h-1s-1 0-1 1h-1c-1 0-2 1-3 2h0c-1-1-1-2-2-3l1-1h0c1-1 1-1 2-1h3z" class="C"></path><path d="M182 182h2v1c1 0 1 1 2 1-1 0-2 1-3 2h0c-1-1-1-2-2-3l1-1z" class="E"></path><path d="M188 175c1-1 4 0 5 0h13 5c2 0 5 0 6-1h0c2 1 3 0 5 1h2 2 0l1 1 2 3h-3 0c0 1 0 1 1 1 1 1 3 0 4 1 0 1 2 2 2 2l-2 2h0-3 0l-3-1h-1-3-2c-1 0-2 0-3-2l1-1c1 1 1 1 3 0h0v-1c-1-1-1-2-2-2v-1h-3 0v-2h-27z" class="P"></path><path d="M218 177c0-1 0-1-1-1 1-1 1-1 2-1l1 1h0l-1 1c1 1 1 1 2 1h2v1h-1c0 1-1 2-2 3h1l-1 1v-2h0v-1c-1-1-1-2-2-2v-1z" class="E"></path><path d="M217 181c1 1 1 1 3 0v2l1-1h1c2 1 3 0 5 1h1c1 1 4 0 5 0l-2 2h0-3 0l-3-1h-1-3-2c-1 0-2 0-3-2l1-1z" class="Q"></path><path d="M302 624h1 3 1 3c0 1 0 1-1 2v2 1c0 1 0 2 1 3 0 0 0 1-1 2h4c1 0 1-1 1-1v-1l1-1h1v2c0 1 1 1 1 2l-1-1h-1v1h1c2 1 4 0 6 1h4v-2-1c1 0 2 0 3 1h0-2l-1 1c1 1 3 1 4 1h1 12v1h-22-49c-4 0-9 1-13 0h-4 0v-1h2c1-1 2 0 3 0 1-1 2-2 3-2 1-1 1-1 2-1v1c0-1 1-1 1-2l1-1h2v-1h-1l1-1c0-1 0-1 1-2h2v1h0v1h1l1 1c1 0 2 0 3-1 1 0 1-1 2-1l1 1c2 1 5 0 7 0h0c2-1 2-1 4-1 0-1 0-1 1-1l2-2 1 1v2h1l1-1h0v-1c0-1 1-1 2-2h3z" class="a"></path><path d="M294 625l1 1v2h-4c0-1 0-1 1-1l2-2z" class="D"></path><path d="M287 635c1 0 1-1 2-1 0-1-1-2-1-2l1-1h1l1 2c0 1-1 1-2 2l-2 1v-1z" class="O"></path><path d="M314 633v3h-6v-6s0-1 1-1c0 1 0 2 1 3 0 0 0 1-1 2h4c1 0 1-1 1-1z" class="S"></path><path d="M265 634h1s1-1 1-2h2v4h-9c1-1 2-2 3-2 1-1 1-1 2-1v1z" class="U"></path><path d="M294 630c2-1 4-1 6-1l1 1h0c-1 2-2 4-4 6h-7v-1c2 0 2 0 3-2l1-2h0v-1z" class="T"></path><path d="M302 624h1 3 1 3c0 1 0 1-1 2v2h-3s-1 1-2 1h0-1l-1-1h-2-4l1-1h0v-1c0-1 1-1 2-2h3z" class="f"></path><path d="M268 630l1-1c0-1 0-1 1-2h2v1h0v1h1l1 1c1 0 2 0 3-1 1 0 1-1 2-1l1 1c2 1 5 0 7 0h0 6l1 1v1h0-1-3-1l-1 1s1 1 1 2c-1 0-1 1-2 1v1c-3 0-7 0-9-1h0c-1 1-1 1-2 1s-1 0-2-1c1-1 1-2 2-3h-1c-1 1-2 2-2 3l1 1h-1 0-3-1 0v-4h-2c0 1-1 2-1 2h-1c0-1 1-1 1-2l1-1h2v-1h-1z" class="B"></path><path d="M276 632h1l2 1h0c1 0 1-1 2-1 0 0 1 0 1 1h1v-1-1h1l1 1c0 1 1 0 2 0v2 1 1c-3 0-7 0-9-1h0c-1 1-1 1-2 1s-1 0-2-1c1-1 1-2 2-3z" class="S"></path><path d="M342 385l1 1 1 1c1 1 2 1 3 2 1 0 1 1 1 2v3l1-1v-1h0 1l1 2c0 1-1 1-1 2h0v3h2 1 0l1 1v7 2c1 1 1 1 2 1v1c1 2 1 3 0 5v1l-1-2h-1v2c-1 0-1 0-1-1l-1 1v4c-1 0-1 0-2 1v3 6h0c0 2 0 3-1 5h1c1-2 1-5 1-7h1c0 3-1 5-1 8v3 4h-1c-1 2 0 3-1 4-1-1-1-1-2-1v1c-1-1-1-5-1-6-2-2 0-7-1-9h-1 0v-3h-1v2-5h-1l1-1c0-1 0-2-1-3 1-2 1-6 0-9v-1l1-3c-1-1 0-2 0-3-1-1-1-1-1-3v-2c-1-1-1-2-1-3-1-1-2-1-2-2 1-1 1-2 1-4h1v-1c0-1 0-1 1-2h0c1-1 1-2 0-3v-2z" class="H"></path><path d="M350 414h2v2h-1v1h-1v-3z" class="T"></path><path d="M352 409v4 1h-2v-4l2-1z" class="D"></path><path d="M347 430h-2c0 1 0 1-1 0 0-1 0-1 1-2l2-1 1 1c-1 1-1 1-1 2h0z" class="W"></path><path d="M344 416h1c0 1 1 3 0 4v1h1l1-1 1-1c0 2-1 4-1 6 1 0 1 0 1 1h-1l-1-2c-1 0-1 1-1 1v1h-1v-10z" class="d"></path><path d="M347 430h0c0-1 0-1 1-2l-1 6c0 3-1 8 0 11h1v1l-1 1v1c-1-1-1-5-1-6 0-4 0-8 1-12z" class="c"></path><path d="M352 399h1 0v8 2c1 1 0 3 0 4h-1v-4-2c-1 0-1-1-2-1v-1l1-1c0-1 0-1-1-1 1-2 1-3 2-4z" class="U"></path><path d="M353 399l1 1v7 2c1 1 1 1 2 1v1c1 2 1 3 0 5v1l-1-2h-1v2c-1 0-1 0-1-1l-1 1v-1-2-1h1c0-1 1-3 0-4v-2-8z" class="C"></path><path d="M352 413h1v3l-1 1v-1-2-1z" class="Y"></path><path d="M355 415v-1-4l1 1c1 2 1 3 0 5v1l-1-2z" class="M"></path><path d="M347 434v3h1c0-2 2-8 1-10l-1-1 1-1c2 1 1 4 1 6h0c0 2 0 3-1 5h1c1-2 1-5 1-7h1c0 3-1 5-1 8v3 4h-1c-1 2 0 3-1 4-1-1-1-1-2-1l1-1v-1h-1c-1-3 0-8 0-11z" class="F"></path><path d="M342 385l1 1 1 1c1 1 2 1 3 2 1 0 1 1 1 2v4c0 2 0 5-1 7h0c2 4 0 8 1 12v5l-1 1-1 1h-1v-1c1-1 0-3 0-4h-1v-1-2c0-1 0-2-1-3s0-2 0-3c-1-1-1-1-1-3v-2c-1-1-1-2-1-3-1-1-2-1-2-2 1-1 1-2 1-4h1v-1c0-1 0-1 1-2h0c1-1 1-2 0-3v-2z" class="i"></path><path d="M342 385l1 1 1 1v1h0c1 1 1 2 1 3h1c0 1 0 1-1 2h0 0c-1-1-1-1-1-2l-1 1-1-2c1-1 1-2 0-3v-2z" class="C"></path><path d="M343 392l1-1c0 1 0 1 1 2h0c-1 1-1 1-1 2h2v1h0c-1 0-2 1-2 2h-1c-1-1 0-2 0-4-1 0-1 0-1-1l1-1z" class="B"></path><path d="M341 392c0-1 0-1 1-2h0l1 2-1 1c0 1 0 1 1 1 0 2-1 3 0 4 0 1-1 3-1 4-1-1-1-2-1-3-1-1-2-1-2-2 1-1 1-2 1-4h1v-1z" class="W"></path><path d="M344 398h2 0v3c-1 0-1-1-1-1h-1v1c1 1 1 1 1 2l1 2s1 0 1 1h0l-2 2h-1 0c0 1 0 1 1 1 1 1 1 2 1 3h0c-1 0-1-1-1-1l-1 1 1 1h1v3h-1-1v-1-2c0-1 0-2-1-3s0-2 0-3c-1-1-1-1-1-3v-2c0-1 1-3 1-4h1z" class="G"></path><path d="M342 404l1-1h1c0 1 0 2 1 4h-2c-1-1-1-1-1-3zm-43 99h1c1 0 2 0 3 2l1 1h-1c2 2 2 3 2 5v1c1 1 2 1 3 2h1v3h0v2 2l-1 2h1c1 1 1 2 1 2 1 1 1 2 2 3h0-2l-1 1v6 2 3h-1l1-2c-1-1-2-1-3-2-1 1-1 1-1 2l-1 1h1l-1 1h-1-1v-1c0-1-1-1-2-2l-1 2h-1 0c-2 0-2 0-3-1s-1-2-1-3v-1c-1 0-2-1-3-1h0l-1-1c1-2 1-3 1-4s-1-2 0-3c0-1 0-1-1-2h0l1-1c0 1 1 1 2 1h1l-1-2h-1c0 1 0 1-1 1 0-1-1-1-2-1 0-2 1-5 1-7v-2h1c1 1 1 1 2 1h1v-1h0c1 1 1 1 2 1l1-1c-1-2-1-4-1-6 0-1 0-1 1-2l1 1c0-1 1-2 1-2z" class="N"></path><path d="M304 525h3v2l-4-1h0c0-1 0-1 1-1z" class="M"></path><path d="M297 518v-2h1c1 1 1 1 2 3v4h-1l-1-1c0-2 0-2-1-4z" class="a"></path><path d="M303 526l4 1h0v2h0l-2 1 1 1 1-1 1 1-1 1c-1 1 0 1-2 2h0c0-1 0-1-1-2v-1l-1-1c1 0 1 0 1-1h0c-1-1-1-2-1-3z" class="F"></path><path d="M290 514h0c1 1 1 1 1 2h1 1v2c1 1 1 1 0 3h-1c0 1 0 1-1 1 0-1-1-1-2-1 0-2 1-5 1-7z" class="K"></path><path d="M293 518l1-1h2l1 1c1 2 1 2 1 4v2h-1c-1 2-1 3-1 4 0 0-1 0-1-1s0-3-1-4l-1-2c1-2 1-2 0-3z" class="b"></path><path d="M296 517l1 1c1 2 1 2 1 4v2h-1v-1h-1c-1-1 0-2 0-3l-1-1c0-1 0-1 1-2z" class="P"></path><path d="M299 503h1c1 0 2 0 3 2l1 1h-1c2 2 2 3 2 5v1c0 1 0 1-1 2 0-1 0-1-1-1l1-2h0-1c-1 0-2 0-2-1l-1-1h0v2h-1l-1-1v-1h0c1-1 0-3 0-4s1-2 1-2z" class="Q"></path><path d="M300 503c1 0 2 0 3 2l1 1h-1v2h-1c0-2-1-2-2-3s0-2 0-2z" class="N"></path><path d="M303 506c2 2 2 3 2 5v1c0 1 0 1-1 2 0-1 0-1-1-1l1-2h0-1c-1 0-2 0-2-1l1-1 1 1v-1-1-2z" class="J"></path><path d="M298 522l1 1h1v1c1-1 1-1 2-1 0 1 1 1 2 2-1 0-1 0-1 1h0c0 1 0 2 1 3h0c0 1 0 1-1 1l1 1v1h0-2v-1l-1 1h0c-1 0-1 0-1-1h1l-2-2v2l-1-1c0-1-1-1-2-2 0-1 0-2 1-4h1v-2z" class="E"></path><path d="M303 526h0c0 1 0 2 1 3-1 1-2 1-2 1-1-1-1-1-1-2l2-2z" class="P"></path><path d="M298 522l1 1h1v1h-1v2c-1 1-1 2-1 3v1c0-1-1-1-2-2 0-1 0-2 1-4h1v-2z" class="G"></path><path d="M300 524c1-1 1-1 2-1 0 1 1 1 2 2-1 0-1 0-1 1l-2 2c-1-1-1-1-1-2h0-1v-2h1z" class="V"></path><path d="M305 512c1 1 2 1 3 2h1v3h0v2 2l-1 2-1-1c-1 1-1 0-2 0h0l-1 1h0l-1-1v-2-1c-1 0-1 0-1-1l1-1c0-1 0-2-1-3 1 0 1-1 1-1 1 0 1 0 1 1 1-1 1-1 1-2z" class="i"></path><path d="M308 514h1v3h0v2 2l-1 2-1-1c-1 1-1 0-2 0h0l-1 1h0l-1-1v-2c1 0 1 0 1-1h0l2 1h0v-1c0-1 0-1 1-1v-1c-1 0-1 0-1-1h1 1v-2z" class="c"></path><path d="M308 514h1v3h0v2 2l-1 2-1-1c1-1 0-1 0-2h1v-1l-1-2c-1 0-1 0-1-1h1 1v-2z" class="H"></path><path d="M291 525c0-1 0-1-1-2h0l1-1c0 1 1 1 2 1h1c1 1 1 3 1 4s1 1 1 1c1 1 2 1 2 2l1 1v-2l2 2h-1c0 1 0 1 1 1h0l1-1v1h2 0c1 1 1 1 1 2h0l1 1c-1 0-1 1-1 1-1 0-1 0-2 1h-1 0c1 1 1 1 1 3h-1v-1c0-1-1-1-2-2l-1 2h-1 0c-2 0-2 0-3-1s-1-2-1-3v-1c-1 0-2-1-3-1h0l-1-1c1-2 1-3 1-4s-1-2 0-3z" class="P"></path><path d="M295 531l1 2h-1-2v-1c0-1 1-1 2-1z" class="b"></path><path d="M291 525c0-1 0-1-1-2h0l1-1c0 1 1 1 2 1h1c1 1 1 3 1 4s1 1 1 1c1 1 2 1 2 2l1 1v-2l2 2h-1c0 1 0 1 1 1h0v3c-1 0-1 0-2 1h0-2-1l1-1c0-1 0-2-1-2l-1-2-2-2-2-1c0-1-1-2 0-3z" class="B"></path><path d="M291 525c0-1 0-1-1-2h0l1-1c0 1 1 1 2 1h1c1 1 1 3 1 4h-1v1l-2-1s0-1 1-1c1-1 1-1 1-2h0c-2 0-2 0-3 1z" class="O"></path><path d="M347 246h1 2 1v1 6l-1 1c-1 3 0 7 0 10v23 6 1 4 6h1v1l1 1c0 1-1 2 0 3v2c-1 2 0 4-1 6 0 2 0 10 1 12h-1v1h0v1c1 1 0 2 0 3h-1 0-2v-5-3c0-1 1-4 0-6l-1-1h1c0-2 0-2-1-3l1-1c0-2 1-5 0-7v-1h-1l-1-1-1-1h-1v-1c-1 0-1 1-1 1 0-4 0-8-1-12 1-2 0-4 0-6l1-15-1-8 1-9v-1c1 0 1 1 1 1 0-2-1-6 1-8h1l1-1z" class="G"></path><path d="M347 246l1 1h0l1 22-1 23v13 2h-1l-1-1-1-1h-1v-1-3l2 2h0v-4c1-6 0-11 0-17v-17c0-4 0-7 1-11l-1-7 1-1z" class="o"></path><path d="M344 301l2 2h0v-4c0 2 0 5 1 7v1l-1-1-1-1h-1v-1-3z" class="Q"></path><path d="M347 246l1 1h0c-1 4 0 9-1 12v-2-2h0v4c-1-1 0-3 0-5l-1-7 1-1z" class="l"></path><path d="M344 255c0-2-1-6 1-8h1l1 7c-1 4-1 7-1 11v17c0 6 1 11 0 17v4h0l-2-2v3c-1 0-1 1-1 1 0-4 0-8-1-12 1-2 0-4 0-6l1-15-1-8 1-9v-1c1 0 1 1 1 1z" class="I"></path><path d="M345 283v12h-1v-11l1-1z" class="M"></path><path d="M344 262h1l1 1c-1 6-1 13-1 20l-1 1v-22z" class="f"></path><path d="M343 255v-1c1 0 1 1 1 1v7 22 11 2 4 3c-1 0-1 1-1 1 0-4 0-8-1-12 1-2 0-4 0-6l1-15-1-8 1-9z" class="T"></path><path d="M352 555c2 0 2 0 2 1 0 2 1 3 1 5v-4 4h1 0 1s1 0 1 1c0 0 0 1 1 1h0c1 1 1 1 1 2s-1 4 0 5c0 1 1 2 1 3l1 6c1 1 1 2 1 3v1h0s0-1-1 0h-1l-2 1v1h1 0c0 1 0 1-1 1l1 1h0c1 1 1 1 2 1v1h-1 0-1-1c1 2 3 3 3 5-1 1-2 1-3 1h0-1 1v1 2h-2v2l-1-1c1-1 1-1 1-2-1 0-1-1-1-1v-1 4c0 1-1 1 0 2v1h3v2c1 1 1 1 2 1v1h0c-1 0-1 1-2 2l-1 1h0c-1-1 0-2-2-2l-1-1c-1 1-2 2-2 3s-1 2-2 3c1-1 1-1 0-2 0-2 1-2 1-3s-1-1-1-2h0v-1c1 1 1 1 2 1v-1h-1l-1-2c0-1 1-2 1-3v-2-1-1l-1 1c-1 1-1 1-2 3l-1 1c-1-1-1-3-2-4h-1v-1-3-4h0 1v-1h1l-1-1c0-1-1-1-2-1h0l1-2v-1c0-1 0-1 1-1v-2l-1-2h0l1-1h1 1 1 1 0l-2-2v1h-1v-1c1-2 1-3 2-5h1c-1 0-1-1-1-1 0-1 1-3 2-4h0l1-1v-4-3-1z" class="S"></path><path d="M362 579c-1 0-2-1-3-1v2h-1-1v-1-2-1c1-1 1-1 1-2l1-6v-1-2h1c0 1-1 4 0 5 0 1 1 2 1 3l1 6z" class="D"></path><path d="M347 574c1-2 1-3 2-5h1c-1 0-1-1-1-1 0-1 1-3 2-4h0c1 1 1 2 0 3 1 1 1 2 1 3v1c-1 2 0 5-1 7-1 0-1 1-1 2h-1c0-1-1-1-1-2-1 0-1 1-2 1l-1-2h0l1-1h1 1 1 1 0l-2-2v1h-1v-1z" class="F"></path><path d="M351 596v-1h-1v-2c1-1 0-3 0-4s-1-1-1-2l1-1v-3h1v1 2h0c2 1 2 1 3 1l1 1h0c2 2 0 4 1 6l1 1 2 1v2h-2v2l-1-1c1-1 1-1 1-2-1 0-1-1-1-1v-1 4c0 1-1 1 0 2v1h3v2c1 1 1 1 2 1v1h0c-1 0-1 1-2 2l-1 1h0c-1-1 0-2-2-2l-1-1c-1 1-2 2-2 3s-1 2-2 3c1-1 1-1 0-2 0-2 1-2 1-3s-1-1-1-2h0v-1c1 1 1 1 2 1v-1h-1l-1-2c0-1 1-2 1-3v-2-1-1l-1 1z" class="N"></path><path d="M357 595l2 1v2h-2v-3z" class="E"></path><path d="M352 599c1 2 1 2 1 4l-1 1-1-2c0-1 1-2 1-3z" class="H"></path><path d="M354 595h1v5h-1v-5z" class="F"></path><path d="M294 592h1v1c1 0 1 1 2 1-1 0-1 0-1 1l1 1h6 11l-1 1h-1l-1 1c2 0 2 0 3 2h2 3 1l1-1v-1-1l1 1h1c1 0 2 0 3-1 1 0 1 1 2 1h1c1-1 2-2 2-3h1c0 1 1 1 2 1 2 0 3 0 5-1h4l1 1h0 1 1c1 1 1 3 2 4l1-1c1-2 1-2 2-3l1-1v1 1 2c0 1-1 2-1 3l1 2h1v1c-1 0-1 0-2-1v1c-2-2-1 0-2 0l-1-1h-1c-1 0-1-1-1-1h-2l1 2h-6c-2 0-4 1-6 0v3h-8l1-2h-14-3c-1 1-3 0-5 0-1 0-5 1-7 0-1 0 0-1-2-1v2h-4c0-1 0-1-1-1h-2v-1h-2c1-1 1-2 2-2l2-2h1l1-2h-1 1c1-1 1-2 2-4-1 0-2 0-2-1l2-2z" class="b"></path><path d="M309 602c2-1 5 0 8 0h-2v1c0 1-2 1-2 0l-1-1h-3z" class="T"></path><path d="M338 600h1l-1 1 1 1v1h-1 0-1v-2h-1c1 0 1-1 2-1z" class="S"></path><path d="M351 596l1-1v1 1h0c-1 2-1 2-1 4-1 1-2 0-4 0l2-1h-1l1-1c1-2 1-2 2-3z" class="G"></path><path d="M326 606h5c0-1 1-1 2-1v3h-8l1-2z" class="Z"></path><path d="M338 601h2l1-1h2c1-1 2-2 3-4 1 1 1 3 2 4h1l-2 1c-1 0-2 0-3 1h-1l-1-1v1h-3l-1-1z" class="E"></path><path d="M334 596c2 0 3 0 5-1h4l1 1h0 1 1c-1 2-2 3-3 4h-2l-1 1h-2l1-1h-1c-1 0-1 1-2 1l-1-1h0v-1-1c-1 0-2-1-3 0h0c-1 0-1 1-2 2-1 0-1 0-2-1 1 0 1-1 1-1 1-1 2-2 2-3h1c0 1 1 1 2 1z" class="D"></path><path d="M340 597h1v1c-1 1-2 1-3 2-1 0-1 1-2 1l-1-1h0v-1-1h1 3l1-1z" class="G"></path><defs><linearGradient id="AN" x1="335.268" y1="594.539" x2="332.779" y2="600.346" xlink:href="#B"><stop offset="0" stop-color="#212020"></stop><stop offset="1" stop-color="#3a393a"></stop></linearGradient></defs><path fill="url(#AN)" d="M331 595h1c0 1 1 1 2 1h5l1 1-1 1h-3-1c-1 0-2-1-3 0h0c-1 0-1 1-2 2-1 0-1 0-2-1 1 0 1-1 1-1 1-1 2-2 2-3z"></path><path d="M294 592h1v1c1 0 1 1 2 1-1 0-1 0-1 1l1 1h6 11l-1 1h-1l-1 1c2 0 2 0 3 2h2 3 1l1-1v-1-1l1 1h1c1 0 2 0 3-1 1 0 1 1 2 1h1s0 1-1 1c1 1 1 1 2 1 1-1 1-2 2-2h0c1-1 2 0 3 0v1 1h0c-2 1-2 1-4 1l-14 1c-3 0-6-1-8 0h-2v2c-1 0-3 0-4 1h-1 0 3c1 1 4 0 4 1-1 1-3 0-5 0-1 0-5 1-7 0-1 0 0-1-2-1v2h-4c0-1 0-1-1-1h-2v-1h-2c1-1 1-2 2-2l2-2h1l1-2h-1 1c1-1 1-2 2-4-1 0-2 0-2-1l2-2z" class="F"></path><path d="M332 598c1-1 2 0 3 0v1 1h0c-2 1-2 1-4 1h1c0-1 0-2 1-2v-1h-1z" class="V"></path><path d="M322 598h1c1 0 2 0 3-1 1 0 1 1 2 1h1s0 1-1 1l-1 1h-1 0c-1 0-1-1-2-1-2 1-2 1-4 1h0l1-1v-1-1l1 1z" class="H"></path><path d="M290 601h1 3c1 0 2 0 3 1h1 5 0v2h0c1 0 1 0 1-1 1 0 1-1 1-1s1-1 2 0v2c-1 0-3 0-4 1h-1 0 3c1 1 4 0 4 1-1 1-3 0-5 0-1 0-5 1-7 0-1 0 0-1-2-1v2h-4c0-1 0-1-1-1h-2v-1h-2c1-1 1-2 2-2l2-2z" class="K"></path><path d="M297 602h1l-1 1h-2c-1 0-1 0-1-1h0 3zm-11 3c1-1 1-2 2-2l2 1h0c0 1-1 1-2 1h0-2z" class="P"></path><path d="M288 605h3c1 1 0 1 2 1l1-1h1v2h-4c0-1 0-1-1-1h-2v-1h0z" class="E"></path><path d="M294 592h1v1c1 0 1 1 2 1-1 0-1 0-1 1l1 1h6 11l-1 1h-1l-1 1c2 0 2 0 3 2h-3-7-1c-4 0-7 0-11-1h-1 1c1-1 1-2 2-4-1 0-2 0-2-1l2-2z" class="N"></path><path d="M311 600l-1-1v-1h1c2 0 2 0 3 2h-3z" class="T"></path><path d="M299 598c1-1 3-1 4-1s1 1 2 1v1l-1 1h-1v-2c-1 0 0 0-1 1h-2l-1-1z" class="C"></path><path d="M294 592h1v1c1 0 1 1 2 1-1 0-1 0-1 1l1 1h-1c1 1 1 1 2 1l1 1 1 1h2c1-1 0-1 1-1v2c-4 0-7 0-11-1h-1 1c1-1 1-2 2-4-1 0-2 0-2-1l2-2z" class="B"></path><path d="M298 597l1 1 1 1h0c-1 1-1 1-2 0v-1-1z" class="T"></path><path d="M294 592h1v1c1 0 1 1 2 1-1 0-1 0-1 1l1 1h-1c1 1 1 1 2 1v1h0-1-2l-1-1 1-1-1-1c-1 0-2 0-2-1l2-2z" class="E"></path><path d="M455 230c1 1 1 1 1 2h1c1 2 1 5 2 7v2 2l2 5 1 6c1 1 1 3 1 4v1h-1c-1 0-1 1-2 1 1 3 2 5 2 7 0 1 0 2 1 2v2c0 2 1 3 1 5 0 1 1 1 1 2h1v1c0 1-1 2-1 3h-1c0-1-1-2-1-3-1 0-1 1-2 1v3h1s-1 0-1 1l-1-1h0l-3 3v1-2l-3-13c-1-2-1-3-2-5l-1-2-2-4c-2-6-5-12-9-16l1-1 1-1c1-1 1-2 1-3s1-1 1-2c-1-1-1-1-2-1v-1h1l1-1-1-1h1 1c1 1 1 1 1 2s1 1 1 2c1 2 1 3 1 5 0 0 0 1 1 2s0 2 2 3h0l2-1h0l-3-6h1l1-1h0c1-1 2-2 2-3 0 0 0-1-1-2 1 1 1 1 2 1 0-1-1-2-1-3h0v-2h0l1-1z" class="g"></path><path d="M451 261c1 0 1-1 1-1 0 1 1 1 2 1h0c0 1-1 2-2 2l-1-2z" class="Y"></path><path d="M449 261l2 1v-1h0l1 2v2h-1l-2-4z" class="B"></path><path d="M457 285v-2l1-1c1 0 1 0 2 1l-3 3v1-2z" class="U"></path><path d="M454 261v-1h-1l-3-3v-3h0v-1c0-1-1-1-1-3h1 0c0 1 0 1 1 1 0 0-1-1 0-1s1 1 1 2c1 1 1 2 1 3 1 0 1 1 1 1h0c0 1 0 2 1 3 0 1 1 3 1 5 1 1 1 2 2 4h0v2l1 1v2h0c-1 0-1-1-1-1 0-1-1-2-1-2 0-2-1-3-1-4s-1-2-2-2h-1v-1c0 2 0 3-1 4h0l-1-2h1v-2c1 0 2-1 2-2z" class="P"></path><path d="M455 230c1 1 1 1 1 2h1c1 2 1 5 2 7v2 2l2 5 1 6c1 1 1 3 1 4v1h-1c-1 0-1 1-2 1 1 3 2 5 2 7 0 1 0 2 1 2v2c0 2 1 3 1 5 0 1 1 1 1 2h1v1c0 1-1 2-1 3h-1c0-1-1-2-1-3l-1-8c0-2-1-4-2-6 0-3-1-7-3-10-1-3-2-5-4-8h0l-3-6h1l1-1h0c1-1 2-2 2-3 0 0 0-1-1-2 1 1 1 1 2 1 0-1-1-2-1-3h0v-2h0l1-1z" class="W"></path><path d="M456 244c2 0 1 0 2 1h-1s-1 1-1 2l-1 2c-1-2-1-3-2-5 1 1 2 1 3 1v-1z" class="N"></path><path d="M455 249l1-2c0-1 1-2 1-2 1 2 2 2 4 3l1 6c1 1 1 3 1 4v1h-1c-1 0-1 1-2 1v-1c0-1 0-1-1-1l-2-5-1-2-1-2z" class="b"></path><path d="M463 258v1h-1c-1 0-1 1-2 1v-1-1h3z" class="O"></path><path d="M456 251c1-1 2-1 2-3 1 1 2 2 2 3l-1 1c-1 0-1 0-2 1l-1-2z" class="P"></path><path d="M455 230c1 1 1 1 1 2h1c1 2 1 5 2 7v2 2l2 5c-2-1-3-1-4-3h1c-1-1 0-1-2-1v1c-1 0-2 0-3-1s-1-2-2-3l1-1h0c1-1 2-2 2-3 0 0 0-1-1-2 1 1 1 1 2 1 0-1-1-2-1-3h0v-2h0l1-1z" class="O"></path><path d="M453 235c1 1 1 1 2 1v2c0 1 1 2 1 2 0 1 0 1 1 2 0 0 0 1-1 2v1c-1 0-2 0-3-1s-1-2-2-3l1-1h0c1-1 2-2 2-3 0 0 0-1-1-2z" class="b"></path><path d="M353 304l2-2c-1-1-1-2-1-2v-1h-1 0l1-1c1 1 2 1 3 2v10 1 1l-1 2c1 1 1 2 1 3h0v1 1 2h0 0v2h0v1h0l2 2c0 1 0 2-1 2 1 1 1 1 1 2h-1v4c-1 1-1 5-1 7h0c-1 2 0 4-1 6v7 10c0 2 1 4 0 6v1 1 4 7c1 1 0 6 0 8-1 2 1 13-1 14h0c-1 1-1 2-1 2v-7l-1-1h0-1-2v-3h0c0-1 1-1 1-2s0-1 1-1v-1c0-1 0-5-1-6v-2c1-1 1 0 1-1-1-3 0-6 0-9l-2-1v-2c-1-2 0-3 0-6v-6c0-2 1-4 0-5v-3-4c0-1-1-1-2-1 0-1-1-1-1-2l1-1c0-3-1-6 0-9h2 0 1c0-1 1-2 0-3v-1h0v-1h1c-1-2-1-10-1-12 1-2 0-4 1-6v-2c-1-1 0-2 0-3l-1-1v-1-1l1-1h1v1 1z" class="P"></path><path d="M353 314l2 1v4l-2-2v-3z" class="H"></path><path d="M353 314v-2h2c0-1 1-1 2-1v1h-1c-1 1-1 2-1 3l-2-1z" class="C"></path><path d="M350 347l1-1v8h-1v-3-4z" class="Q"></path><path d="M355 315c0-1 0-2 1-3h1l-1 2h0c0 2 1 4 0 5h-1v-4z" class="L"></path><path d="M356 371c-1 0-1 0-1-1v-1l-1-1c1-2 1-3 1-5h0 1v7 1z" class="Q"></path><path d="M353 355h2 1c0 2 0 6-1 7l-2-2v-5z" class="N"></path><path d="M356 354h0l-1-1c-1-1-2-3-2-5 0-1 0-1 1-2h2v1 7z" class="G"></path><path d="M350 354h1v16c1 1 1 2 1 4l-2-1v-2c-1-2 0-3 0-6v-6c0-2 1-4 0-5z" class="F"></path><path d="M353 375v-1l2-1c0-1 0-1 1-1v4 7 1c-1-1-2-1-2-2h-1v-2h1v-1-1c-1-1 0-1 0-2l-1-1z" class="N"></path><path d="M353 375v-1l2-1c0-1 0-1 1-1v4 4h-1v-1-3l-2-1z" class="B"></path><path d="M353 304l2-2c-1-1-1-2-1-2v-1h-1 0l1-1c1 1 2 1 3 2v10c-1 0-2 0-2-1h0l-2 1h0l2-2c0-1 1-2 1-2l-1-1c0 1 0 1-1 2v-1l-1-2z" class="C"></path><path d="M353 324v-1h1 1c0-1 0-2 1-2h1l-1 1v2l2 2c-1 2-1 3-2 5v1h0-1c0-1-1-1-1 0l-1-1v-2-2-3z" class="Q"></path><path d="M356 324l2 2c-1 2-1 3-2 5v-7zm-3 0c1 0 1 0 2 1v5c-1 0-1 0-2 1v-2-2-3z" class="G"></path><path d="M356 331c1-2 1-3 2-5v2c1 1 1 1 1 2h-1v4c-1 1-1 5-1 7h0c-1-1-1-1-1-2h-1c0 1 0 1-1 2-1-1 0-3 0-5l-1-1c0-1 2-2 2-3h1 0v-1z" class="K"></path><path d="M348 343c0-3-1-6 0-9h2 0 1v1 11l-1 1c0-1-1-1-2-1 0-1-1-1-1-2l1-1z" class="B"></path><path d="M348 343v2h2v-3-6l1-1v11l-1 1c0-1-1-1-2-1 0-1-1-1-1-2l1-1z" class="C"></path><path d="M338 383h1s1 1 2 1c0-1 1-1 1-1v2 2c1 1 1 2 0 3h0c-1 1-1 1-1 2v1h-1c0 2 0 3-1 4 0 1 1 1 2 2 0 1 0 2 1 3v2c0 2 0 2 1 3 0 1-1 2 0 3l-1 3v1c1 3 1 7 0 9 1 1 1 2 1 3l-1 1h1v5 1h-2v-1c-1 1-1 2-2 3l-2-1v1 2h-2c-1 1-1 2-1 2h-2v3h0-1v3c-1 0 0-1-2-1h0c0 1 0 2-1 3v-6-4-4-5h0c-1-1-1-1 0-1v-2c-1 0-1-1-1-1h-1l1-1c1-1 1-2 1-4h0v-1-3-2-6-1h0l2-1h0v-4-1h-2v-2-1l-1-1 1-3v-2c0-1 0-1 2-1l1 1v4h1c0-1 0-1 1-2 0-2 0-2-1-3h1 1v-3c-1-1-1-1 0-2s2-1 4-2h0z" class="Z"></path><path d="M341 409h1v4h-1v-4z" class="d"></path><path d="M335 405h2v1s0 1 1 1v1l-1 1-1-1c-1-1-1-2-1-3z" class="V"></path><path d="M334 416v-1c1-1 2-1 3-1h1l-1 2h0-2-1z" class="k"></path><path d="M334 428l2-1c-1 2-1 3-1 4-1 1-1 1-1 2l-1-1v-1h0c1-1 1-2 1-3z" class="E"></path><path d="M335 431l3-1c0 1 1 1 1 2l-1 1h-3-1c0-1 0-1 1-2zm2-15h1c0 1 0 1-1 2 0 0 1 0 1 1s-1 1-1 3c-1 0-1 0-2-1v1l-1-1h1c1-1 1-2 2-3v-1-1h0z" class="G"></path><path d="M333 409h3s1 0 2 1c-1 0-1 0-2 1-1 0-1 1-2 1h-1l1-1v-1l-1-1z" class="d"></path><path d="M342 423h-1v-2-2-3c0-1 0-1 1-2 1 3 1 7 0 9z" class="a"></path><path d="M339 406h1v3 1h0v4c-1-1-1-1-2-1l1-1c0-1-1-1-1-2l1-1-1-2 1-1z" class="k"></path><path d="M328 428l2-2h3 3 0l1-1h0c1 1 2 1 3 1h0l1 1c-1 1-2 1-3 1s-1 0-2-1l-2 1c-1 0-3 0-4 1l-1 1v-1l-1-1h0z" class="B"></path><path d="M332 413h1c0 1 0 2 1 3h0 1v1h-1l-1 3h0-4 0v-2-1h0l1-1-1-1v-1-1h3z" class="V"></path><path d="M329 415l2-1h0c1 1 1 2 0 3-1 0-1 0-2 1v-1h0l1-1-1-1z" class="X"></path><path d="M330 405c0 2 0 1-1 3v3h1 0c1-1 2-1 2-2v4h-3v1 1l1 1-1 1h0v1 2h0c1 1 1 2 1 2-1 1-1 1-2 1h0v-3-1h0v-1-3-2-6-1h0l2-1z" class="a"></path><path d="M328 428l1 1v1l1-1c1-1 3-1 4-1 0 1 0 2-1 3h0v1l1 1h1 3l1-1v1h-2v1 1 2h-2c-1 1-1 2-1 2h-2v3h0-1v3c-1 0 0-1-2-1h0c0 1 0 2-1 3v-6-4-4-5z" class="Q"></path><path d="M329 434h1l3-3h0v1l-1 2c1 0 1 0 1 1v1h-1l-1-1h-2v-1z" class="I"></path><path d="M328 433v-2h1v3 1 1 3 2 3c0 1 0 1-1 1h0v-4-4-4z" class="a"></path><path d="M328 428l1 1v1l1-1c1-1 3-1 4-1 0 1 0 2-1 3h0 0l-3 3h-1v-3h-1v2-5z" class="J"></path><path d="M338 383h1s1 1 2 1c0-1 1-1 1-1v2 2c1 1 1 2 0 3h0c-1 1-1 1-1 2v1h-1c0 2 0 3-1 4 0 1 1 1 2 2h-1 0c0 2 1 5 0 7 0 1 1 2 0 3v-3h-1l-1 1c-1 0-1-1-1-1v-1h-2 0l-1-1-1 1h0c1 2 1 2 1 3v1h-1v1l-1-1c0 1-1 1-2 2h0-1v-3c1-2 1-1 1-3h0v-4-1h-2v-2-1l-1-1 1-3v-2c0-1 0-1 2-1l1 1v4h1c0-1 0-1 1-2 0-2 0-2-1-3h1 1v-3c-1-1-1-1 0-2s2-1 4-2h0z" class="H"></path><path d="M328 393c1 0 1 0 2 1v6h0-2v-2-1l-1-1 1-3z" class="f"></path><path d="M333 405l-1 2h-1c1-2 0-4 1-5 1 0 1 0 2 1v-1l2 1c1-1 1-1 2-1 0 1 0 1 1 2v2l-1 1c-1 0-1-1-1-1v-1h-2 0l-1-1-1 1h0z" class="c"></path><path d="M339 397c0 1 1 1 2 2h-1 0c0 2 1 5 0 7 0 1 1 2 0 3v-3h-1v-2c-1-1-1-1-1-2-1 0-1 0-2 1l-2-1v-1h1c0-1 1-1 1-2l2 1c0-1 1-2 1-3z" class="i"></path><path d="M338 383h1s1 1 2 1c0-1 1-1 1-1v2 2c1 1 1 2 0 3h0c-1 1-1 1-1 2v1h-1-1c0 1-1 2-2 3l-2-1v1c0 1-1 2-2 3l-1-1c-1-1-1-1-1-2h2c0-1 0-1 1-1 0-2-1-3-1-5h1v-3c-1-1-1-1 0-2s2-1 4-2h0z" class="J"></path><path d="M338 386h1s1 1 1 2l-1 1-1-2c-1 0-2 1-3 1v-1h1v-1h2 0z" class="V"></path><path d="M342 387c1 1 1 2 0 3h0c-1 1-1 1-1 2-1-1-2-1-3-1 1-1 1-2 2-2 1-1 1-2 2-2z" class="a"></path><path d="M334 395h1c0-1-1-2-1-2 1-1 1 0 2 1h1l1-1h0c-1-1-2-1-3-2h1 1 1 0c1 0 2 0 3 1v1h-1-1c0 1-1 2-2 3l-2-1v1c0 1-1 2-2 3l-1-1c-1-1-1-1-1-2h2c0-1 0-1 1-1z" class="c"></path><path d="M341 293h1c1 4 1 8 1 12 0 0 0-1 1-1v1h1l1 1 1 1h1v1c1 2 0 5 0 7l-1 1c1 1 1 1 1 3h-1l1 1c1 2 0 5 0 6v3 5c-1 3 0 6 0 9l-1 1c0 1 1 1 1 2 1 0 2 0 2 1v4 3c1 1 0 3 0 5v6c0 3-1 4 0 6v2l2 1c0 3-1 6 0 9 0 1 0 0-1 1v2c1 1 1 5 1 6v1c-1 0-1 0-1 1l-1-2h-1 0v1l-1 1v-3c0-1 0-2-1-2-1-1-2-1-3-2l-1-1-1-1v-2c1-1 0-3 0-4v-3c1-1 1-3 0-4v-6-6-4-8h-1v-7-1c-1 0-2 1-3 1v-2h0c0-2 0-3 1-4h0c-1-1 0-3 0-4v-2-1c0-3-1-6 0-9-1 0-1-1-1-2l1-2h0c-1-1 0-5 0-6l1-1v-1c1-2 0-4 1-6v-2-6z" class="a"></path><path d="M342 318l2-1v2h0 2v1c-1 0-2 0-2 1v1l1 1h1l1 2h-1l1 2-1 1c0-1-1-1-1-1h-1-2c1-1 0-2 0-4s1-3 0-5z" class="K"></path><path d="M343 346h1v3 4 3 6h1v-2h1v4l-2-1v2l1 1-1 1v1l1 1c0 2-1 2-1 3h-1c-1-4 0-8-1-12v-4-8-1l1-1z" class="S"></path><path d="M344 338c0-1 1-1 1-1v-8h1c1 3 0 6 0 9v9c0 2-1 6 0 8v4h-1v-2l-1-1v-3-4-3-8z" class="K"></path><path d="M342 327h2c1 3 0 5 0 8v3 8h-1l-1 1v1h-1v-7-1c-1 0-2 1-3 1v-2h0c1-1 1-2 3-3v-6h1v-3z" class="a"></path><path d="M338 339c1-1 1-2 3-3 0 1 0 2-1 3h-2 0z" class="I"></path><path d="M341 341h1v6 1h-1v-7z" class="k"></path><path d="M342 327h2c1 3 0 5 0 8v3 8h-1c-1-5 0-11-1-16v-3z" class="P"></path><path d="M343 305s0-1 1-1v1h1l1 1 1 1h1v1c1 2 0 5 0 7l-1 1c1 1 1 1 1 3h-1 0c-1 1-1 2-1 3h-2v-1c0-1 1-1 2-1v-1h-2 0v-2l-2 1c0-1 0-2 1-3h0c-1-3-1-7 0-10z" class="V"></path><path d="M343 315h1v2l-2 1c0-1 0-2 1-3z" class="P"></path><path d="M348 308c1 2 0 5 0 7l-1 1c1 1 1 1 1 3h-1 0c-1-1-1-3 0-4v-1c-1-1-1-3-1-4s1-2 2-2z" class="k"></path><path d="M343 305s0-1 1-1v1h1l1 1 1 1-1 1h-1 0-1v2h1c-1 1-1 2-1 4-1 0-1 0-1 1-1-3-1-7 0-10z" class="K"></path><path d="M341 293h1c1 4 1 8 1 12-1 3-1 7 0 10h0c-1 1-1 2-1 3 1 2 0 3 0 5s1 3 0 4v3h-1v6c-2 1-2 2-3 3 0-2 0-3 1-4h0c-1-1 0-3 0-4v-2-1c0-3-1-6 0-9-1 0-1-1-1-2l1-2h0c-1-1 0-5 0-6l1-1v-1c1-2 0-4 1-6v-2-6z" class="X"></path><path d="M340 308v-1c1-2 0-4 1-6v11 5 2h0 0-2 0c-1 0-1-1-1-2l1-2h0c-1-1 0-5 0-6l1-1z" class="L"></path><path d="M339 315v1c1 0 1 0 1 1h1v2h0 0-2 0c-1 0-1-1-1-2l1-2z" class="J"></path><path d="M339 319h0 2 0 0v3c-1 2 0 3 0 5l1 1h-1v2 6c-2 1-2 2-3 3 0-2 0-3 1-4h0c-1-1 0-3 0-4v-2-1c0-3-1-6 0-9z" class="R"></path><path d="M339 328l1 1v4h-1v-4-1z" class="X"></path><path d="M348 346c1 0 2 0 2 1v4 3c1 1 0 3 0 5v6c0 3-1 4 0 6v2l2 1c0 3-1 6 0 9 0 1 0 0-1 1v2c1 1 1 5 1 6v1c-1 0-1 0-1 1l-1-2h-1 0v1l-1 1v-3c0-1 0-2-1-2-1-1-2-1-3-2l-1-1-1-1v-2c1-1 0-3 0-4v-3c1-1 1-3 0-4v-6-6c1 4 0 8 1 12h1c0-1 1-1 1-3l-1-1v-1l1 1 1-1c0 2-1 2-1 3 1 1 1 2 2 2h0v-2-3-8-4c0-1 1-4 1-5-1-2 0-3 0-4z" class="N"></path><path d="M344 376v3h-2v-3h2z" class="U"></path><path d="M345 381h0c1 0 2 0 3 1 0 1 0 2-1 3l-1-1h-2c0-1 0-2 1-3z" class="R"></path><path d="M344 376v-1h1c0 1 1 1 1 1h1l-1-1h0c0-1 0-1 1-2 0 3 1 4 0 7h-1 0-2v-1-3z" class="L"></path><path d="M172 199c1 0 1-1 1-1 1 0 2 0 3-1h1c1 1 1 1 2 0 2 1 2 1 3 2h-1l2 1h2c1 1 2 1 4 1h1v1l-1 1c1 0 1 0 2 1h2 2c2 0 3 0 5-1h2v2 1 1c-1 1-2 1-4 2v1l2 1v1h0v2h0c-1 0-1 1-2 1 0 1-1 2 0 3-1 1-2 2-3 2s-1 1-2 1c-2 3-5 5-7 9h-1-1s-1 1-2 1c0 0-1 2-2 3v-2-1c-1 0-1 0-2-1 0 1 0 2-1 3-1 0-1 1-1 2s-1 2-1 3c-1 3-2 6-4 9v-3-2l1-1v-2c1-1 0-2 1-3l-1-1-2 2c0-2 0-2 1-3v-1c0-1 1-1 1-1 0-2-1-1 0-3v-1-1h-1v1l-1-1c0-1 1-2 1-2 0-1 1-1 1-1l1-2 1-2h-1l-3 1c0-1 1-1 1-3v-2l-1 1h-1c0-1 1-2 2-2-1-1-2-1-3-2v1l-1-3h-1l-1-1 2-2c-2 0-1-1-2-1h-1 0-2c-1 1 0 0-1 0 1 0 1-1 2-1 1-1 2-1 3-2v-1c1-1 3-2 4-3l2-1z" class="P"></path><path d="M171 208h1v1h-1-1v-1h1z" class="S"></path><path d="M170 207l3-1h3v1 1c-1 0-2 0-3-1h0l-2 1h-1v-1h0z" class="H"></path><path d="M184 209h1c0 1 1 1 2 1l1 1-1 1h-2c0-1-1-2-1-3zm-17 2h3v-1h1v3h-3v1l-1-3z" class="G"></path><path d="M180 202l2 2c-1 1 0 1 0 2l1 1c-2 0-2 0-4-1v-2h0l1-1v-1z" class="D"></path><path d="M177 210h1l-1-2h1 2l1 1h0-1c1 0 1 1 2 1l-1 2h-1 0-1v1c0-1 0-1-1-1h0c-1-1-1-1-1-2z" class="N"></path><path d="M180 209c1 0 1 1 2 1l-1 2h-1 0l-1-2 1-1z" class="b"></path><path d="M187 206c3 0 5 1 8 2v1h-1v-1l-1 1h0v1h-1-1-1c1 0 1 0 2-1h0 0v-1l-1 1h-3v-1c0-1-1-1-1-2z" class="R"></path><path d="M189 203c1 0 1 0 2 1l-4 1-1 1h-2v1h-1l-1-1c0-1-1-1 0-2 2 0 5 0 7-1z" class="H"></path><path d="M181 225l-1-1 3-4s1-1 1-2h0 1 1v-1l1 1h1l1 1v1h-1-2l-1 1h-1l-2 2c0 1-1 1-1 2z" class="N"></path><path d="M183 200h2c1 1 2 1 4 1h1v1l-1 1c-2 1-5 1-7 1l-2-2c1-1 2-1 3-2z" class="b"></path><path d="M191 210h1 1v-1h0l1-1v1c0 1 1 1 1 2l-2 2h-1c0 1-1 1 0 2l1 1c-2 1-4 2-4 3l-1-1h-1l-1-1v-1c2 0 2-1 2-2 2-1 2-1 3-2v-2z" class="J"></path><path d="M192 213c0 1-1 1 0 2l1 1c-2 1-4 2-4 3l-1-1c0-1 1-2 2-2 1-1 1-2 2-3z" class="B"></path><path d="M171 215h0 2l1-1h0 2 0c1-1 1-2 0-2v-1-1h1c0 1 0 1 1 2h0c1 0 1 0 1 1v-1h1 0 1c0 1-1 1-1 2h0c0 1 0 1-1 2s-1 1-3 1h0v-1c-1 0-1 1-1 1-1 1-1 1-2 1v2l-3 1c0-1 1-1 1-3v-2l-1 1h-1c0-1 1-2 2-2z" class="H"></path><path d="M180 212h0 1c0 1-1 1-1 2h0c-1 1-1 1-2 1 0-1 0-2 1-2v-1h1z" class="O"></path><path d="M182 210h0 1c0 1 0 2 1 2l1 1-8 10-4 8c-1 2-1 2-2 3v-1c0-1 1-1 1-1 0-2-1-1 0-3h0c1-1 2-2 3-4 0-1 2-4 3-5s1-2 1-4c1-1 1-1 1-2h0c0-1 1-1 1-2l1-2z" class="c"></path><path d="M182 210h0 1c0 1 0 2 1 2-2 1-2 2-4 2h0c0-1 1-1 1-2l1-2z" class="N"></path><path d="M173 220v-2c1 0 1 0 2-1 0 0 0-1 1-1v1h0c2 0 2 0 3-1 0 2 0 3-1 4s-3 4-3 5c-1 2-2 3-3 4h0v-1-1h-1v1l-1-1c0-1 1-2 1-2 0-1 1-1 1-1l1-2 1-2h-1z" class="T"></path><path d="M200 203h2v2 1 1c-1 1-2 1-4 2v1l2 1v1h0l-2 1c-1-1-1-1-2-1h0l-1-1h0c0-1-1-1-1-2h1v-1c-3-1-5-2-8-2v-1l4-1h2 2c2 0 3 0 5-1zm-28-4c1 0 1-1 1-1 1 0 2 0 3-1h1c1 1 1 1 2 0 2 1 2 1 3 2h-1l2 1c-1 1-2 1-3 2v1l-1 1h-3-1l-1-1h0c-1 1-2 1-3 1h0c-1 0-1 0-2 1h0v1l1 1h0 0-2c0-2-1-2-2-4 1-1 3-2 4-3l2-1z" class="S"></path><path d="M181 199l2 1c-1 1-2 1-3 2v1l-1 1h-3-1l-1-1h2 2c0-1 0-1 1-2 1 0 2-1 2-2z" class="I"></path><path d="M172 199c1 0 1-1 1-1 1 0 2 0 3-1h1c1 1 1 1 2 0-1 2-2 3-4 3h-2c-1 0-1 0-1-1z" class="P"></path><path d="M195 211h0l1 1h0c1 0 1 0 2 1l2-1v2h0c-1 0-1 1-2 1 0 1-1 2 0 3-1 1-2 2-3 2s-1 1-2 1c-2 3-5 5-7 9h-1-1s-1 1-2 1c0 0-1 2-2 3v-2-1c-1 0-1 0-2-1l1-1c0-1 1-2 1-2 1-1 1-1 1-2s1-1 1-2l2-2h1l1-1h2 1v-1c0-1 2-2 4-3l-1-1c-1-1 0-1 0-2h1l2-2z" class="S"></path><path d="M195 211h0l1 1h0c1 0 1 0 2 1l2-1v2h0c-1 0-1 1-2 1 0 1-1 2 0 3-1 1-2 2-3 2s-1 1-2 1v-2c1 0 2-1 2-2s0-1 1-2h0c-1-1-2-2-3-2l2-2z" class="D"></path><path d="M193 216h1c-2 3-5 6-7 9h-1v-1h-1v1h0l1 1c0 1-1 2-1 2-1 1-2 1-3 1-1-3 3-6 3-8l1-1h2 1v-1c0-1 2-2 4-3z" class="F"></path><path d="M471 208c1 0 1 0 2-1v3c1-1 2-1 4-1v2l1 1h1l2 2c1 0 1 1 1 2l-2 1c1 2 2 2 1 4v1l-1-1h-1c0 1 0 1 1 1l-1 1h0c-1 1-2 1-2 2l-1 2 2 1h1v1l-1 1 1 1c-1 0-1 1-2 1v1 2l1 1v1 2 1c-1 3-1 5-1 8l-1 2v1c-1-1-1-1-3 0v3h-1c0-1 1-3 0-4-1 0-1 0-1 1s0 3-1 4l2 2v2h-1c0-1 0-1-1-2 0 1 0 1-1 1h0l-1-1h-1c-1 0-1 0-1 1l-1 1h0v-1h-2v1-1c0-1 0-3-1-4l-1-6-2-5v-2-2c-1-2-1-5-2-7h-1c0-1 0-1-1-2h0c0-2-1-4-2-5s-1-2-1-3v-1l-1-1c0-1 1-2 1-3 1 0 3-2 3-3h0v-1l1-1 3-1v-1-1h1c2 0 3 1 5 0h3l-1-1h3 1z" class="T"></path><path d="M468 244h1v3h-1v-3z" class="U"></path><path d="M464 242c0-2 0-2 2-3h0c0 1 0 1-1 2v1h1v-2c1-1 1-2 1-2h1v1c0 2-1 3-1 5l-1 1c0 1 0 2 1 4h0-1c-1-1-2-6-2-7z" class="Q"></path><path d="M459 243h1 1c1 4 1 8 2 11h1 1 1l-1 2c0 1 0 1 1 2l-1 1h0v-1h-2v1-1c0-1 0-3-1-4l-1-6-2-5z" class="M"></path><path d="M465 232h1c1-1 2-2 4-2 0 2 1 5 0 6-1 2-2 1-2 3h0v-1h-1s0 1-1 2v2h-1v-1c1-1 1-1 1-2h0c-2 1-2 1-2 3-1-2 0-2 0-4v-2-2l1-2z" class="I"></path><path d="M465 232c1 1 2 1 2 1-1 2-2 1-2 3h1v-1l1 1v1l-1 1c-1 0-1-1-2-2v-2l1-2z" class="R"></path><path d="M461 232h2c1-2 4-3 5-5l1 1c1 0 1 1 2 1h0l1-1c0 1 1 2 2 2h0c1 0 1 0 2 1h0c-1-1-1-2-1-3 1 0 1 1 2 1l1-1h1v1l-1 1 1 1c-1 0-1 1-2 1v1 2h-1l-1-1h-1-1c-1-1-1-2-1-3s-1-1-2-1c-2 0-3 1-4 2h-1l-1 2c-1-1-1-1-3-2z" class="B"></path><path d="M476 231h1v2h-1l-2-3c1 0 1 0 2 1h0z" class="N"></path><path d="M452 222h1c0 1 1 2 1 3h2c0 1 0 1 1 1l1-2v1c1-1 1-1 1-2h1v1h2v1c-1 0-1 1-1 1l-1 1v-1h-1v1h0c1 1 1 1 2 1h1v1h-1v1 2h0c2 1 2 1 3 2v2 2h-1c-1-1-1-2-2-3h-1c0 3 0 6 1 8h-1-1v-2-2c-1-2-1-5-2-7h-1c0-1 0-1-1-2h0c0-2-1-4-2-5s-1-2-1-3z" class="Q"></path><path d="M459 223h1v1h2v1c-1 0-1 1-1 1l-1 1v-1h-1v1h0c0 1 0 2-1 3-1-1-1-2-2-3h-1v-1l1-1c0 1 0 1 1 1l1-2v1c1-1 1-1 1-2z" class="G"></path><path d="M459 227c1 1 1 1 2 1h1v1h-1v1 2h0c2 1 2 1 3 2v2 2h-1c-1-1-1-2-2-3h-1c-1 0-1-1-1-1 0-1 0-3-1-4 1-1 1-2 1-3z" class="D"></path><path d="M474 234h1l1 1h1l1 1v1 2 1c-1 3-1 5-1 8l-1 2v1c-1-1-1-1-3 0v3h-1c0-1 1-3 0-4-1 0-1 0-1 1s0 3-1 4l2 2v2h-1c0-1 0-1-1-2 0 1 0 1-1 1h0l-1-1h-1c-1 0-1 0-1 1-1-1-1-1-1-2l1-2v-1s0-1 1-2v-1h1c1 0 0 2 0 3l1 1 1-1v-2-1c0-1 0-1 1-1l-1-2s1 0 1-1 0-3 1-4h0l-1-1h-1c0-1 0-2 1-3l1 1h0c1 0 0-1 1-2 0 0 1 0 1-1v-2z" class="g"></path><path d="M466 253h2v4h-1c-1 0-1 0-1 1-1-1-1-1-1-2l1-2v-1z" class="Y"></path><path d="M473 242c1 0 1 0 1 1 1 1 2 0 3 2v3l-1 2h-1c-2-2-1-2-3-2v-1c1-2 1-4 1-5z" class="Z"></path><path d="M476 235h1l1 1v1 2 1c-1 3-1 5-1 8v-3c-1-2-2-1-3-2 0-1 0-1-1-1 1-1 1-1 2-1v-1h-1l-1-1 3-3v-1z" class="C"></path><path d="M476 235h1l1 1v1l-1 1c-1-1-1-1-1-2v-1z" class="L"></path><path d="M471 208c1 0 1 0 2-1v3c1-1 2-1 4-1v2l1 1h1l2 2c1 0 1 1 1 2l-2 1c1 2 2 2 1 4v1l-1-1h-1c0 1 0 1 1 1l-1 1h0c-1 1-2 1-2 2l-1 2 2 1-1 1c-1 0-1-1-2-1 0 1 0 2 1 3h0c-1-1-1-1-2-1h0c-1 0-2-1-2-2l-1 1h0c-1 0-1-1-2-1l-1-1c-1 2-4 3-5 5h-2 0v-2-1h1v-1h-1c-1 0-1 0-2-1h0v-1h1v1l1-1s0-1 1-1v-1h-2v-1h-1c0 1 0 1-1 2v-1l-1 2c-1 0-1 0-1-1h-2c0-1-1-2-1-3h-1v-1l-1-1c0-1 1-2 1-3 1 0 3-2 3-3h0v-1l1-1 3-1v-1-1h1c2 0 3 1 5 0h3l-1-1h3 1z" class="O"></path><path d="M461 213l2-1c1 1 1 1 1 2h-1l-2-1z" class="N"></path><path d="M463 214h1 0c1 1 2 1 3 1v1l-1 2h1l-1 1c-1-1 0-1 0-1-1-1-3-2-3-3v-1z" class="B"></path><path d="M471 208c1 0 1 0 2-1v3h-1l-2 2v-1c-1 1-1 1-1 2l-1-1c-1-1-3-1-3-3h3l-1-1h3 1z" class="S"></path><path d="M467 208h3 1 0c-1 1-1 1-2 1 0 0 0 1-1 1v-1l-1-1z" class="P"></path><path d="M471 209l1 1-2 2v-1l1-2z" class="N"></path><path d="M471 208c1 0 1 0 2-1v3h-1l-1-1v-1h0z" class="G"></path><path d="M463 215c0 1 2 2 3 3 0 0-1 0 0 1l1-1 1-1h1l-2 2v1l2 1h-2c-1 0 0-1-1-1v2l-3 3-1 1v1l-1-1s0-1 1-1v-1h-2v-1h-1 0c-1-1 1-2 2-3s1-1 1-2l1-1-1-1 1-1z" class="I"></path><path d="M467 218l1-1h1l-2 2v1l2 1h-2c-1 0 0-1-1-1v2l-3 3h-1c0-1 1-1 1-1v-1c1 0 1-1 2-1l-1-2h-1l-1-1 1-1h1l1 1h1l1-1z" class="L"></path><path d="M463 225l3-3v-2c1 0 0 1 1 1h2l2 1v1h-1c1 1 1 2 3 3-1 1-1 1-1 2l-1 1h0c-1 0-1-1-2-1l-1-1c-1 2-4 3-5 5h-2 0v-2-1h1v-1h-1c-1 0-1 0-2-1h0v-1h1v1l1-1 1 1v-1l1-1zm-7-13l3-1v1c1 0 2 0 2 1l2 1v1l-1 1 1 1-1 1c0 1 0 1-1 2s-3 2-2 3h0c0 1 0 1-1 2v-1l-1 2c-1 0-1 0-1-1h-2c0-1-1-2-1-3h-1v-1l-1-1c0-1 1-2 1-3 1 0 3-2 3-3h0v-1l1-1z" class="C"></path><path d="M461 220l-1-1c-1 0-1 1-2 0l2-2c1 0 1 1 1 1h1c0 1 0 1-1 2z" class="Q"></path><path d="M453 222l1-1 1-1c0 1 1 1 1 2v1h2v1l-1 2c-1 0-1 0-1-1h-2c0-1-1-2-1-3z" class="B"></path><path d="M456 212l3-1v1 1h0v1h-1l1 1h1c0 1-1 2-2 3s-1 1-2 1v-1c1 0 1-1 2-2h-1l-2 1h0v-1c-1 0-1 0-1 1s-1 2-2 3h-1c0-1 1-2 1-3 1 0 3-2 3-3h0v-1l1-1z" class="L"></path><path d="M456 212l3-1v1 1h0-4l1-1z" class="H"></path><path d="M473 210c1-1 2-1 4-1v2l1 1h1l2 2c1 0 1 1 1 2l-2 1c1 2 2 2 1 4v1l-1-1h-1c0 1 0 1 1 1l-1 1h0c-1 1-2 1-2 2l-1 2 2 1-1 1c-1 0-1-1-2-1 0 1 0 2 1 3h0c-1-1-1-1-2-1h0c-1 0-2-1-2-2s0-1 1-2v-1c-1 0-1-1-2-1v-2l-1-1c1 0 2-1 2-1 0-1 0-2-1-3l-1-1v-4l2-2h1z" class="S"></path><path d="M479 223c-1-2-1-2-2-3-1 0-1 1-2 2-1-1-1-1-1-2l1-1c1 0 1-1 1-1 1-1 1-1 2-1v1l1 1c1 1 1 1 1 2h-1c0 1 0 1 1 1l-1 1h0z" class="K"></path><path d="M477 211l1 1h1l2 2c1 0 1 1 1 2l-2 1v-2l-1 1c-2-1-1-2-1-3l-2 2h-2c0-2 2-2 3-3v-1z" class="B"></path><path d="M317 614h1c2 0 3 0 4 1h2 0 4c1 0 1 0 1-1h1c0 2 0 2-1 3l2 1h1 2 0l2-1v1h1 2 2l1-1h3 0c1 0 1-1 2-1s2 0 3 1h3l3 1h0-2c1 0 3 0 4 1l1 2h1 0 1l-1 1v1h1l-2 2c1 0 1 0 2 1-1 0-1 1-2 1v1c1 0 1 0 2-1h0c1 0 1 1 2 1h1l-1 1c-2 1-2 0-3 2v4h0c1 1 1 1 1 2h-1-2-11-3-1v-1h-12-1c-1 0-3 0-4-1l1-1h2 0c-1-1-2-1-3-1v1 2h-4c-2-1-4 0-6-1h-1v-1h1l1 1c0-1-1-1-1-2v-2h-1l-1 1v1s0 1-1 1h-4c1-1 1-2 1-2-1-1-1-2-1-3v-1-2c1-1 1-1 1-2h-3-1-3-1c-2 0-5 0-7-1h5 1 2 0 2v-2c1 1 5 1 6 0h-4 0l3-1h2l1-1v-1c-1 0-1 0-1-1v-1c1 0 1 0 3-1 1 0 1 0 2-1z" class="g"></path><path d="M338 631h3l1 2h-2-2v-2z" class="T"></path><path d="M310 632l1-1h1c1 0 1-1 1-1h1v2 1s0 1-1 1h-4c1-1 1-2 1-2z" class="Y"></path><path d="M326 633l1-1h-1v-1h2c1 1 7-1 7 1l-4 4h-1c-1 0-3 0-4-1l1-1h2 0c-1-1-2-1-3-1z" class="D"></path><path d="M345 617c1 0 1-1 2-1s2 0 3 1h3l3 1h0-2c-2 0-4 1-6 1h-6-5l2-1h2l1-1h3 0z" class="L"></path><path d="M341 618h4l1-1c0 1 1 1 2 1v1h-6-5l2-1h2z" class="k"></path><path d="M315 625v-2c1-2 2 0 4-1h4 13 1 4c2 0 4-1 6-1-1 1-1 1-2 1s-1 0-1 1h-2 0-2v1h0c0 1-3 1-4 0 0 0-1 0-2 1-1 0-2-1-3-1h0c-1 1-3 0-4 0-2 0-4 0-6 1-1 0 0-1-2 0l-1-1c-1 0-2 1-3 1z" class="I"></path><path d="M317 614h1c2 0 3 0 4 1h2 0 4c1 0 1 0 1-1h1c0 2 0 2-1 3l2 1h1 2 0l2-1v1h1 2l-2 1h5c-3 1-7 0-11 1h-12-5l-1-1v-1c-1 0-1 0-1-1v-1c1 0 1 0 3-1 1 0 1 0 2-1z" class="c"></path><path d="M325 618h2v1c-1 0-4 1-4 0h0s1 0 2-1z" class="d"></path><path d="M334 618l2-1v1h1 2l-2 1h-3v-1z" class="a"></path><path d="M322 617c1 0 1-1 2 0h0v1h1c-1 1-2 1-2 1h-2c-1 0-2 0-3-1l1-1h3z" class="L"></path><path d="M318 618l1-1h3l-1 1h-3z" class="B"></path><path d="M317 614h1l1 2h-1s-1 0-1 1c-1 1-2 1-3 2v1l-1-1v-1c-1 0-1 0-1-1v-1c1 0 1 0 3-1 1 0 1 0 2-1z" class="C"></path><path d="M329 614h1c0 2 0 2-1 3s-1 1-2 1h-2-1v-1h0c-1-1-1 0-2 0h-3l1-1c1 0 2-1 2-1h2 0 4c1 0 1 0 1-1z" class="I"></path><path d="M327 624c1 0 3 1 4 0h0c1 0 2 1 3 1 1-1 2-1 2-1 1 1 4 1 4 0 1 0 2 0 3 1h0c1 0 1-1 2-1h1 0 3l1 1-1 1-1 2v1h1c1 0 1-1 1-1 1 0 2 1 3 2l-1 1-1-1h-2l-1-1-1 1h0-1 0c-1 1-1 4-1 6v-1c0-1 0-2-1-2h0-2l-1-2h-3c0-1 0-2-1-3v1h-2c-1-1-1-1-2-1l-1 1h0l-1 1c-1-1-2 0-3-1h-1-1c-1-1-1 0-2 0h-1c-1 0-1 0-2-1 0 1 0 1-1 1h0-1-1 0-4v-3l1-1c1 0 2-1 3-1l1 1c2-1 1 0 2 0 2-1 4-1 6-1z" class="D"></path><path d="M315 625c1 0 2-1 3-1l1 1c2-1 1 0 2 0 2-1 4-1 6-1v2h-7v-1c-1 1-2 1-2 1-1 0-1 0-1-1 0 0-2 1-3 1l1-1z" class="k"></path><path d="M332 629h-1c0-1 0-1 1-2h3c1 1 2 0 3 1h4v1c-1 0-2 0-3 1h4 1 0c-1 2 0 2 0 3h-2l-1-2h-3c0-1 0-2-1-3v1h-2c-1-1-1-1-2-1l-1 1h0z" class="L"></path><path d="M327 624c1 0 3 1 4 0h0c1 0 2 1 3 1 1-1 2-1 2-1 1 1 4 1 4 0 1 0 2 0 3 1h0c1 0 1-1 2-1h1 0 3l1 1-1 1h-22v-2z" class="o"></path><path d="M359 621h1 0 1l-1 1v1h1l-2 2c1 0 1 0 2 1-1 0-1 1-2 1v1c1 0 1 0 2-1h0c1 0 1 1 2 1h1l-1 1c-2 1-2 0-3 2v4h0c1 1 1 1 1 2h-1-2-11-3l1-1c0-2 0-5 1-6h0 1 0l1-1 1 1h2l1 1 1-1c-1-1-2-2-3-2 0 0 0 1-1 1h-1v-1l1-2 1-1-1-1h-3 0-1c-1 0-1 1-2 1h0c-1-1-2-1-3-1h0v-1h2 0 2c0-1 0-1 1-1s1 0 2-1h9 3z" class="E"></path><path d="M359 621h1 0 1l-1 1v1h1l-2 2v2h-3c1-1 0-1 1-1h0c0-1-1-2-1-3h0-1l-1 1c-1 0-1 0-1 1h0-3l-1-1h-3 0-1c-1 0-1 1-2 1h0c-1-1-2-1-3-1h0v-1h2 0 2c0-1 0-1 1-1s1 0 2-1h9 3z" class="O"></path><path d="M353 630c0-1 0-1 1-1h0l-1 1h1c1 0 1 0 1 1 1 0 2 0 2 1h2v1c-1 0-2 1-2 2-1 0-1 0-2 1v-1l-1 1h-8c0-2 1-4 1-6h0l1-1 1 1h2l1 1 1-1z" class="b"></path><path d="M357 632c0 1 0 1-1 2v1h-1c0-1-1-2-2-3v-2h1c1 0 1 0 1 1 1 0 2 0 2 1z" class="F"></path><path d="M450 186c1 0 3 1 4 0v-2h1v2c1 1 3 0 4 0s2 0 2 1h1c1-1 2-1 4-1h5v1h6c3 0 7-1 9 1-1 1-1 2-1 3l1 2 1 1c2 1 3 2 4 4l-1 3h-1c-4 3-5 11-7 15 0-1 0-2-1-2l-2-2h-1l-1-1v-2c-2 0-3 0-4 1v-3c-1 1-1 1-2 1h-1-3l1 1h-3c-2 1-3 0-5 0h-1v1 1l-3 1-1 1v1h0-2c0-1 0-1-1-2v-1c1-1 2-1 2-3v-1c-2-1-3-1-5 0-1 0-2 1-3 1v1c-1-1-1-2-2-2l1-1h2 1l1-1h-1c0-1 0-2 1-3h0-1c-1 0-2-1-3-1h-3c0-1 0-1 1-1 0-1 1-2 1-3-1 0-4 0-5 1h-3v-1h-2c-1 1-1 1-2 1h0l1-2h1l1-1h0c0-1 0-1 1-1l1-1c0-1 1-2 1-3l-1-1h-1c1-1 1 0 2-1 1 0 1-1 2-2h2 2s0 1 1 1c1-1 4 0 5-1z" class="S"></path><path d="M471 205h-1l1-1c1 0 1 0 2 1 1 0 1 0 1 1-2 0-2 0-3-1z" class="H"></path><path d="M458 198h1c0 1 0 1 1 2h0l-1 1c-1 0-1 0-2-1 1-1 1-1 1-2h0z" class="N"></path><path d="M477 204c0 1 0 1 1 2h0c1-1 1-1 2-1l1 1c-1 0-2 0-2 1l-1 1v-2h-1-1v-1l1-1z" class="D"></path><path d="M461 199c2 0 3 0 5 1h0v1h1c-1 1-1 1-2 0h-2s-1 0-1-1l-1-1z" class="T"></path><path d="M471 205c1 1 1 1 3 1l2 1h-3c-1 1-1 1-2 1h-1v-1l-1-1c1 0 1 0 2-1z" class="N"></path><path d="M475 201h-1v-1h1l1 1h1v-2h1v1c1 0 3 0 4-1v1 2h-2s-1-1-2-1c0 0-1 1-2 1l-1-1z" class="F"></path><path d="M449 202h4l1 2h0l-1 1h-4-1c0-1 0-2 1-3h0z" class="E"></path><path d="M449 202h4l-2 2c-1 0-2-1-2-2h0z" class="N"></path><path d="M482 200l1 2h-1v2s-1 1-1 2h0l-1-1c-1 0-1 0-2 1h0c-1-1-1-1-1-2l-2-1v-2h0l1 1c1 0 2-1 2-1 1 0 2 1 2 1h2v-2z" class="B"></path><path d="M483 202l1 1c0 1 0 2-1 3h-1c0 2-1 3-2 4h-1v2h-1l-1-1v-2c-2 0-3 0-4 1v-3h3c1 1 1 2 2 3 1-1 0-1 0-2l1-1c0-1 1-1 2-1h0c0-1 1-2 1-2v-2h1z" class="V"></path><path d="M474 194c2-1 4-1 6-1h1c2 1 2 0 4 1v1h-2v1l-2-1v2h-1c-1-1 0-1-1-1l-2 1c-1 1-1 1-2 1 0-1-1-1-1-1v-2-1z" class="R"></path><path d="M474 195h3v2c-1 1-1 1-2 1 0-1-1-1-1-1v-2z" class="D"></path><path d="M449 205h4v1c1 1 3 0 5 0 1 1 0 2 1 2l1-1v-1h5c1 0 2-1 2-1h1l1 1 1 1v1h-3l1 1h-3c-2 1-3 0-5 0h-1v1 1l-3 1-1 1v1h0-2c0-1 0-1-1-2v-1c1-1 2-1 2-3v-1c-2-1-3-1-5 0-1 0-2 1-3 1v1c-1-1-1-2-2-2l1-1h2 1l1-1z" class="N"></path><path d="M468 205l1 1 1 1v1h-3v-1h-2v-1h2l1-1z" class="H"></path><path d="M454 207h0 4 0c-1 1-2 1-2 2 1 0 1 1 2 1h1v1l-3 1-1 1v1h0-2c0-1 0-1-1-2v-1c1-1 2-1 2-3v-1z" class="V"></path><path d="M452 212c2 0 3-1 4 0l-1 1v1h0-2c0-1 0-1-1-2z" class="Q"></path><path d="M450 186c1 0 3 1 4 0v-2h1v2c1 1 3 0 4 0s2 0 2 1h1c1-1 2-1 4-1h5v1h6c3 0 7-1 9 1-1 1-1 2-1 3l1 2 1 1c0 1 0 1-1 1h-1v-1c-2-1-2 0-4-1h-1c-2 0-4 0-6 1v1 2s1 0 1 1h-2l-1-1h0-2-1c-1 0-1 0-2 1v1c1 1 2 1 1 2h-1-1v-1h0c-2-1-3-1-5-1v-1h-2-1l-1-1-2 1v-1h0-1c-1 1-2 1-3 1h0l-3 1c-1 1-1 1-1 2h0s1 1 2 1h-1c-1 0-2-1-3-1h-3c0-1 0-1 1-1 0-1 1-2 1-3-1 0-4 0-5 1h-3v-1h-2c-1 1-1 1-2 1h0l1-2h1l1-1h0c0-1 0-1 1-1l1-1c0-1 1-2 1-3l-1-1h-1c1-1 1 0 2-1 1 0 1-1 2-2h2 2s0 1 1 1c1-1 4 0 5-1z" class="P"></path><path d="M444 197h2v2c-1 1-2 1-3 1 0-1 1-2 1-3z" class="D"></path><path d="M448 199c-1-1-1-1-1-2 1 0 1-1 2-1h1l1 2-3 1z" class="I"></path><path d="M440 193h5 3c1 0 2 0 2 1l-1 1h-3c-1-1-2-1-2-1h-1c-1 0-2 0-3 1v-1h-1v1l-1-1v-1h2z" class="G"></path><path d="M454 197h-1v-3h-1v-1h3s0 1 1 1l1-1h2v3h-1 0-1v1l-2 1v-1h0-1z" class="N"></path><path d="M462 193h0c1-1 2-1 2-1 1 0 1 1 1 2h1c0-1 1-1 1-1l1 1c1-1 1-1 2-1v1h1c1-1 2-1 3 0v1 2s1 0 1 1h-2l-1-1h0-2-1v-2h0-1-3l-1-1h-1l-1-1z" class="B"></path><path d="M459 193h3l1 1h1l1 1h3 1 0v2c-1 0-1 0-2 1v1c1 1 2 1 1 2h-1-1v-1h0c-2-1-3-1-5-1v-1h-2-1l-1-1v-1h1 0 1v-3z" class="D"></path><path d="M461 198c1-1 3-1 5-1l1 1c-1 1-1 1-1 2-2-1-3-1-5-1v-1z" class="L"></path><path d="M459 193h3l1 1h1l1 1c-1 1-2 0-4 1-1 0-1 1-2 2h-1l-1-1v-1h1 0 1v-3z" class="G"></path><path d="M466 186h5v1h6c3 0 7-1 9 1-1 1-1 2-1 3l1 2c-2 0-2-1-3-1-2-1-4-2-6-1-1 0-1 0-2-1-1 0-2 1-3 1h-6c-1-1-1-1-2 0s-2-1-4 0h-3l4-4h1c1-1 2-1 4-1z" class="F"></path><path d="M477 187c3 0 7-1 9 1-1 1-1 2-1 3-1-1-3-1-5-1h-1v-2c-1 0-1 1-1 1h-3c0-1 0-1-1-2h3z" class="P"></path><path d="M466 186h5v1h6-3c1 1 1 1 1 2h-3c-2 1-3 0-4 1v-2h-2v3c-1-1-1-1-2 0s-2-1-4 0h-3l4-4h1c1-1 2-1 4-1z" class="B"></path><path d="M450 186c1 0 3 1 4 0v-2h1v2c1 1 3 0 4 0s2 0 2 1l-4 4h-2v-1c-1 0-1 1-2 1h-1-1v-1h-1v1h-1-5 0c-1 0-3 0-4 1v1h-2v1l1 1v-1h1v1h-3-1c-1 0-1 1-2 1l1-1h0c0-1 0-1 1-1l1-1c0-1 1-2 1-3l-1-1h-1c1-1 1 0 2-1 1 0 1-1 2-2h2 2s0 1 1 1c1-1 4 0 5-1z" class="D"></path><path d="M152 181h2 2 1l1 1-1 1h1l1-1v1-1l1-1h1c0 1 1 1 1 1 1 1 1 0 2 1h0c1-1 1-1 2-1l1 1c1-1 3-1 4-1h4 0v2h4 1l1-1c1 1 1 2 2 3h0c1-1 2-2 3-2h1 3 7l13-1c1 0 1 0 2 1h1 1 5 2 3 1l3 1-1 1h0 2v2 1h3 0c0 1 0 1-1 2 0 0 1 0 1 1s0 1 1 2h1v1h-1s-1 0-1 1l-1-1h-2 0c-1 1-1 2-2 2l-1 2h1 2v1s0 1-1 2l1 1c-1 0-2 0-2 2h0l-4 1c-1 1-3 2-5 2h-1l-5 2-1 1c-3 1-6 3-8 4h-1c-2 0-3 2-4 3-1-1 0-2 0-3 1 0 1-1 2-1h0v-2h0v-1l-2-1v-1c2-1 3-1 4-2v-1-1-2h-2c-2 1-3 1-5 1h-2-2c-1-1-1-1-2-1l1-1v-1h-1c-2 0-3 0-4-1h-2l-2-1h1c-1-1-1-1-3-2-1 1-1 1-2 0h-1c-1 1-2 1-3 1 0 0 0 1-1 1l-2 1c-1 1-3 2-4 3v1c-1 1-2 1-3 2-1 0-1 1-2 1 1 0 0 1 1 0h2 0c-1 1-1 2-2 2 0 1 0 2 1 3h-1l-1-2c-1 0-2 1-2 2-1 1-1 3-1 4l-1 1c-1-2-1-4-1-6-1-2-2-4-3-7l-3-8c-1 0-1-3-2-3 1-1 1-1 2-1v1c1 0 2-1 2-2 1 0 1-2 0-2h-2v-1-1h1v1h1c0-1 1-1 1-2l-2-1c-1 0-1 0-1-1 0 0 2-1 2-2v-1z" class="T"></path><path d="M157 196c1 0 1 0 2 1 0 1 0 1-1 2h-1v-3z" class="C"></path><path d="M195 194s-1 0-1-1c1 0 1 0 2-1l2 1-1 1h-2z" class="V"></path><path d="M201 191h2c0 2-1 2-3 3-1 0-1 0-2 1h-2l-1-1h2l1-1c1 0 2-1 3-2z" class="K"></path><path d="M168 197h0c-1 0-2 0-3 1h-2c0 1 0 1-1 1v-1-3l-1-2h1 1 0v3c1 0 2-1 3-1 0 1 0 1 1 1l1-1v2z" class="F"></path><path d="M166 182l1 1c1-1 3-1 4-1v1c0 1 1 2 0 2v1 1l-1 1-1-1h-2l-1 1c0-1-1 0-2-1 0 0 0-1 1-2h1v-3z" class="D"></path><path d="M166 185h0c1 1 1 1 2 1v-1s-1 0-1-1h2 0v1 2h-2l-1 1c0-1-1 0-2-1 0 0 0-1 1-2h1z" class="F"></path><path d="M221 184h3 1l3 1-1 1h0 2v2h-1-5s0-1-1-1h-2 0-2c-1 0-2 0-3-1v-1h5l1-1z" class="G"></path><path d="M225 184l3 1-1 1c-1 0-2 0-2 1h0l-1-1c0-1 0-1 1-2z" class="D"></path><path d="M184 191l2-2h1 4c2 0 4 1 6 0 1 0 1 1 2 0 1 0 2 1 4 0l1 1s0-1 1-1c1 1 1 1 2 0 1 1 1 1 2 1l1-1c2 0 3 0 5 1 0 0-1 0-1 1h-11-2-9-2-6z" class="R"></path><path d="M202 195c2 0 3 0 4 1l1-1c0-1 0-1 1-2h4c1 0 1 0 2 1h0c-1 1-2 1-3 1h-2-1c0 1 0 1 1 1h3c1 0 1 0 1 1l-2 1c-1-1-1-1-2-1s-1 1-2 2l1 1-2 2c-1-1-1-1-3-1-1 1-1 1-1 2h0-2 0l-1-1v-1h3v-2c-1 1-1 1-2 1 0-1 1-2 2-2l1-1c0-1 0-1-1-2z" class="C"></path><path d="M209 196h3c1 0 1 0 1 1l-2 1c-1-1-1-1-2-1s-1 1-2 2c0 0 0 1-1 1h-1-1v1h-1v-3c1 0 3-2 4-2h2z" class="L"></path><path d="M194 200s1 0 1-1c0 0 1-1 1-2v-1c1 0 4-1 5-1h1c1 1 1 1 1 2l-1 1c-1 0-2 1-2 2 1 0 1 0 2-1v2h-3v1l1 1h0c-2 1-3 1-5 1h-2-2c-1-1-1-1-2-1l1-1v-1h-1v-1h1 1 1l1 1h1v-1z" class="B"></path><path d="M195 204v-2h4l1 1h0c-2 1-3 1-5 1z" class="E"></path><path d="M189 201v-1h1 1l-1 1c1 1 1 1 2 1 0 1 0 1 1 2h-2c-1-1-1-1-2-1l1-1v-1h-1z" class="O"></path><path d="M199 201c-1 0-1 0-1-1h-1v-1l1-2h2v-1h0l2 2c-1 0-2 1-2 2 1 0 1 0 2-1v2h-3z" class="L"></path><path d="M179 193h12l1 1h0c-1 1-1 1-1 2l1 1c1 1 2 2 2 3v1h-1l-1-1h-1-1-1v1c-2 0-3 0-4-1h-2l-2-1h1c-1-1-1-1-3-2-1 1-1 1-2 0h3l1-1h-1-2l2-2h1v-1h-2z" class="B"></path><path d="M182 199h0c1 0 1 0 2-1h1 0 3 0v1h-1-2v1h-2l-2-1h1z" class="D"></path><path d="M190 200c1-1 1-1 1-2h-2v-1h3c1 1 2 2 2 3v1h-1l-1-1h-1-1z" class="C"></path><path d="M187 184h3 7l13-1c1 0 1 0 2 1h1 1 5 2l-1 1h-5v1s-1 1-2 1c-2 0-2-1-5 0h0c-1-1-3 0-5-1v1l-2-1-1 1h0v-1c-1 0-1 0-1-1l-1 2-1-1v-1h-1v1h-1v-1c-1 1 0 1-2 2l-1-1h0c-1 1-2 1-3 1h-1v-1h-1l-1 1c-1 0-2-1-3-1 1-1 2-2 3-2h1z" class="Z"></path><path d="M212 184h1 1 5 2l-1 1h-5-10l-2-1c2 0 6 1 9 0h0z" class="D"></path><path d="M175 182h0v2h4 1l1-1c1 1 1 2 2 3h0c1 0 2 1 3 1l1-1h1v1h1c1 1 2 1 2 2h-4-1l-2 2-1-1h-1-2l-1 1c-1 1-3 0-4 0l-1-1h0l-1 1h-2 0c-1 0-1 0-1-1-1 1-1 3-1 4-1 0-1-1-2-1l-1 1h-1v-1-2c-1 0-1 0-2-1h0c1-1 3-1 4-1 0 1 0 1 1 1 0-1 1-1 1-2h1l1-1v-1-1c1 0 0-1 0-2v-1h4z" class="B"></path><path d="M165 193v-2c-1 0-1 0-2-1h0c1-1 3-1 4-1v3c1 0 1-1 1-1h1v1l-1 1h-1c-1 0-1 1-2 0z" class="I"></path><path d="M171 183h2v1 2h1v1h0c-1 1-1 1-1 2h-1c-1-1-1-1-1-2v-1-1c1 0 0-1 0-2z" class="R"></path><path d="M168 195h0 0l1-1v1h3v-1c1-2 4-1 6-2h0l1 1h2v1h-1l-2 2h2 1l-1 1h-3-1c-1 1-2 1-3 1 0 0 0 1-1 1l-2 1c-1 1-3 2-4 3v1c-1 1-2 1-3 2-1 0-1 1-2 1 1 0 0 1 1 0h2 0c-1 1-1 2-2 2 0 1 0 2 1 3h-1l-1-2c-1 0-2 1-2 2-1 1-1 3-1 4l-1 1c-1-2-1-4-1-6-1-2-2-4-3-7l-3-8c1 0 2 2 3 3 0 1 0 1 1 2 1 0 1 1 1 2v2h2l1-2c-1-1-1-1-1-2h1c2 1 0 3 1 5 1-1 1-1 2-1v1c1-1 2-2 3-2 1-1 2-2 3-4h0 2c1-1 1-1 1-2-1-1-1-1-2-1v-2z" class="C"></path><path d="M175 194c0 1 1 2 2 2h-4v-1l1-1h0 1z" class="H"></path><path d="M175 194h3 2l-2 2h-1c-1 0-2-1-2-2z" class="P"></path><path d="M158 203v1c0 1 0 2 1 4 0 1 0 2-1 3h-1 0v-2l-1-1c0-1-1-2-1-2v-1h2l1-2zm61-9h3c1 1 1 1 2 1h1l1 2h1l-1 2h1 2v1s0 1-1 2l1 1c-1 0-2 0-2 2h0l-4 1c-1 1-3 2-5 2h-1v-1l-2-1v-1h0c-1 0-3-1-4-1v-2c0-2 1-1 2-3-1-1-1 0-2 0v-1l2-1c1 0 1 0 1-1 1-1 3 0 5-2z" class="D"></path><path d="M217 207v-3h3v1c-1 1-1 2-2 3h-1v-1z" class="O"></path><path d="M223 205v1c-1 1-3 2-5 2 1-1 1-2 2-3h3z" class="M"></path><path d="M227 199h2v1s0 1-1 2l1 1c-1 0-2 0-2 2h0l-4 1v-1-1c1-1 3-1 4-1 1-1 0 0 0-1v-2-1z" class="C"></path><path d="M223 204h3l1 1-4 1v-1-1zm-10-7c2 1 2 0 4 0l-1 1h1l1 1v1 1c0 1-1 1-2 1 0-1 0-1-1-1v1 1h-2v-1h-2c0-2 1-1 2-3-1-1-1 0-2 0v-1l2-1z" class="J"></path><path d="M217 198l1 1v1 1c0 1-1 1-2 1 0-1 0-1-1-1l1-1h-1-1v-1h2l1-1z" class="F"></path><path d="M219 194h3c1 1 1 1 2 1h1l1 2c-1 1-2 1-4 2-1 1-2 1-4 1v-1l-1-1h-1l1-1c-2 0-2 1-4 0 1 0 1 0 1-1 1-1 3 0 5-2z" class="Z"></path><path d="M217 197h3l1 1c-1 0-2 0-2 2-1 0-1-1-1-1l-1-1h-1l1-1h0z" class="d"></path><path d="M224 195h1l1 2c-1 1-2 1-4 2l-1-2v-1h2l1-1z" class="i"></path><path d="M213 197c1 0 1 0 1-1 1-1 3 0 5-2v1l2 1v1h-4 0c-2 0-2 1-4 0z" class="F"></path><path d="M207 199c1-1 1-2 2-2s1 0 2 1v1c1 0 1-1 2 0-1 2-2 1-2 3v2c1 0 3 1 4 1h0v1l2 1v1l-5 2-1 1c-3 1-6 3-8 4h-1c-2 0-3 2-4 3-1-1 0-2 0-3 1 0 1-1 2-1h0v-2h0v-1l-2-1v-1c2-1 3-1 4-2v-1-1-2h0c0-1 0-1 1-2 2 0 2 0 3 1l2-2-1-1z" class="T"></path><path d="M209 202c0 1 1 2 1 3-1 1-2 1-2 2-1-1 0-2 0-3s1-1 1-2z" class="J"></path><path d="M208 200c0 1 0 2 1 2 0 1-1 1-1 2h-3c0-1 1-1 1-2l2-2z" class="Q"></path><path d="M215 206l2 1v1l-5 2s0-1 1-1c0-1 1-3 2-3z" class="W"></path><path d="M202 214c1-2 3-4 5-5l1-1 1-1 1 1 1 1v2c-3 1-6 3-8 4h-1v-1z" class="C"></path><path d="M202 214c1 0 1 0 2-1 1 0 3-1 4-2s1-2 3-2v2c-3 1-6 3-8 4h-1v-1z" class="W"></path><path d="M202 203h0c0-1 0-1 1-2 2 0 2 0 3 1 0 1-1 1-1 2h3c0 1-1 2 0 3-2 1-3 3-5 4l-3 3v-2h0v-1l-2-1v-1c2-1 3-1 4-2v-1-1-2z" class="B"></path><path d="M202 203h0c0-1 0-1 1-2 2 0 2 0 3 1 0 1-1 1-1 2s0 1-1 2h0l-1-1c1 0 1 0 1-1l-1-1h0l-1 2v-2z" class="D"></path><path d="M202 206l2 1h0c-1 2-3 2-4 4l-2-1v-1c2-1 3-1 4-2v-1zm114-30c6-1 12-1 18-1h32 12c1 0 1 0 2 1h4c0-1 1-1 2-1h5c-1 1-1 1-1 2l1 1c-1 1-2 1-3 1h-1c-1 1-3 1-4 2h-4v1h1v1h2-2l-1 1-2 1-4 1h-1 0c-2 0-2 1-3 2v1c-1 0-1 1-1 1l1 1c-1 0-1 1-2 1v1 1c1 0 1 0 1 1h-1-2-1l1 1h1c1 0 1 0 1 1v1c-1 0-1 0-2 1v1 1h-3c-1-1-1-1-2-1h-1c-1 0-2 1-3 2l-2 2-2-2v2c-1 1-1 0-1 1h0c1 1 1 2 1 3s0 2-1 3v1c1 1 1 2 1 3v2l-1 1c-1 0-1-1-1-2v-1h-1v1h-1c1-1 1-1 1-2h-2c0-1 0-2-1-2v6l-1-1s-1 0-1 1c0 2 1 4 0 6h-1v-2h-3v-1c0-1 1-2 1-3l-1-1v-1-4-2-1c0-2 0-3-1-5l-1-1c0-1 0-1 1-2v-1c-1-1-1-2-1-3h0c-1 0-2 0-2-1-1 0-1 0-1-1h-2l-2-1-2 2c-1 0-1 0-2-1h-1l-1 1v-1-1h-2 0c-3 1-6 0-8 1l-2-1c0-1 0-1 1-2-1 0-1 0-1-1h-1v-2h1c0-1-1-1-1-2h0v-1h-2-2-9s0-1-1-1h1 1 1 1 5v-2h-6l-1-1h7-3v-2h-3 0-6c0-2-1-2-2-3h12c1 0 5-1 6 0h2l-1-1h0 4z" class="Q"></path><path d="M357 176h6v1h-1-1-1c0 1 1 1 1 2h1 0l-1 1h0l-1-1c-1 0-1-1-1-1-1 0-1 1-2 1v-3z" class="O"></path><path d="M349 189l3-2h0c-1 1-1 1-1 2v1l1-1 1-2h2l-2 2 1 1c1-1 2-1 2-2h1c0-1 1-2 2-2-1 1-4 4-5 4-2 0-3 1-4 1 0 0-1-1-1-2z" class="B"></path><path d="M338 192c-1-1-2-1-2-1-2 0-3 1-4 1v-1l3-1c1 0 2 0 3-1h2 1 1c1 1 1 2 1 2h-4l-1 1h0z" class="N"></path><path d="M342 189c1 0 1 0 2-1h2v1s-1 0-1 1v1h2v-1l1-1v1l1-1c0 1 1 2 1 2-1 0-1 1-2 1s-2 0-3 1l-1-1c1 0 0 0 1-1l-1 1-1-1s0-1-1-2z" class="V"></path><path d="M366 183h-1v-1h-1c1-1 1-2 2-2h1c1 0 2-1 3-2h-2c1-2 1-2 2-2h1 1c1 0 2 0 2 1-1 1-1 2-3 2s-3 2-5 3v1z" class="U"></path><path d="M338 192c1 0 2 1 3 1 0 1-1 2-1 3l-2 1h0c-1 0-2 0-2-1-1 0-1 0-1-1h-2l-2-1h-4s0-1 1-1h1c3 0 7 0 9-1h0z" class="P"></path><path d="M323 185v-1c1 0 1-1 1-1h1v2h0 1 5 12v1h-4-1c-2-1-6 1-8 0h-1-3c0 1-1 1-2 2 0-1 0-1-1-2h-10v1h0 2c1 1 1 1 2 1l-1 1c-1-1-1 0-2 0h0-1c0-1-1-1-1-2h0v-1h-2-2-9s0-1-1-1h1 1 1 1 5 5 7 4z" class="k"></path><path d="M314 189h0c1 0 1-1 2 0l1-1c-1 0-1 0-2-1h-2 0v-1h10v2h1c2 1 4 2 6 0h0c1 1 2 1 3 1-1 1-2 1-4 1h-3c-1-1-2 0-3 1h0c1 0 1 0 2 1h-1s-1 0-1 1h3v1h-1-2 0c-3 1-6 0-8 1l-2-1c0-1 0-1 1-2-1 0-1 0-1-1h-1v-2h1 1z" class="K"></path><path d="M319 187h3v1l-1 1h-2l-1-1 1-1z" class="J"></path><path d="M313 189h1 1c-1 1-1 1-1 2h1c0 1 0 1 1 2 0 1 1 1 1 1h1c0-1 0-1 1-1h1v-2h1v2h2 3v1h-1-2 0c-3 1-6 0-8 1l-2-1c0-1 0-1 1-2-1 0-1 0-1-1h-1v-2h1z" class="T"></path><path d="M328 176c6-1 12 0 18 0h5 2 4v3c1 0 1-1 2-1 0 0 0 1 1 1l-1 1h-1v-1h-1l-1 1h0c-2-1-5 0-7 0-1-1-1 0-2 0h-1v1h1c2-1 5 0 8-1h0c0 1-1 2-2 2h-4-2-3l1-1v-1h-2v1h-1v-1h0-1v2h-2-1c-1 0-1 0-2-1l1-1h0-2v1c-1 1-2 1-4 1v-1h1v-1h-2l1 1c-1 1-2 1-3 1l2-2c-1 0-2 1-3 1v1h-2c-1-1 0-3 0-4v-2h3z" class="b"></path><path d="M325 178v-2h3l2 2-1 1h-4v3c-1-1 0-3 0-4z" class="D"></path><path d="M305 177c1 0 5-1 6 0h2c1 0 1 1 2 1h0l1-1h5 2 1 0l1 1c0 1-1 3 0 4h2v-1c1 0 2-1 3-1l-2 2c1 0 2 0 3-1l-1-1h2v1h-1v1c2 0 3 0 4-1v-1h2 0l-1 1c1 1 1 1 2 1h1 2v-2h1 0v1h1v-1h2v1l-1 1h3 2l-1 1h-1 0 1c-1 2-2 2-4 2-2-1-11 0-13 0h0-5-1 0v-2h-1s0 1-1 1v1h-4-7-5v-2h-6l-1-1h7-3v-2h-3 0-6c0-2-1-2-2-3h12z" class="J"></path><path d="M321 177h2 1c-1 1-1 3-1 3v3c-2 0-3 0-4-1h0 2 0c0-1-1-2-2-2l1-1c1-1 1-1 1-2z" class="Y"></path><path d="M307 183h6c2 0 3 1 4 1h1 2 3v1h-4-7-5v-2z" class="G"></path><path d="M326 185h-1v-1l1-1c1 1 2 1 2 1 2 0 4 0 5-1h1s1 0 1 1c1 0 2 0 3-1h9 1c-1 2-2 2-4 2-2-1-11 0-13 0h0-5z" class="D"></path><path d="M305 177c1 0 5-1 6 0h2c1 0 1 1 2 1h0l1-1h5c0 1 0 1-1 2l-1 1v-1h-1c-1 1-1 2-1 3h0-1-1-1v-1l-1 1h-5c0-1 0-1-1-2h-1c0 1 1 1 1 2h-3v-2h-3 0-6c0-2-1-2-2-3h12z" class="P"></path><path d="M315 178l1-1h5c0 1 0 1-1 2l-1 1v-1h-1c-1 1-1 2-1 3h0-1-1-1v-1l-1-1-1 2v-1c0-1 1-1 1-2 1 0 2 0 3 1h1v-1c-1-1-1-1-2-1zm-22-1h12c1 0 5 0 6 1v1h-1-1 0c-2 1-5 1-8 1h0-6c0-2-1-2-2-3z" class="b"></path><path d="M384 176c0-1 1-1 2-1h5c-1 1-1 1-1 2l1 1c-1 1-2 1-3 1h-1c-1 1-3 1-4 2h-4v1h1v1h2-2l-1 1-2 1-4 1h-1 0c-2 0-2 1-3 2v1c-1 0-1 1-1 1l1 1c-1 0-1 1-2 1v1 1c1 0 1 0 1 1h-1-2-1l1 1h1c1 0 1 0 1 1v1c-1 0-1 0-2 1v1 1h-3c-1-1-1-1-2-1h-1c-1 0-2 1-3 2l-2 2-2-2v2c-1 1-1 0-1 1h0c1 1 1 2 1 3s0 2-1 3v1c1 1 1 2 1 3v2l-1 1c-1 0-1-1-1-2v-1h-1v1h-1c1-1 1-1 1-2h-2c0-1 0-2-1-2v6l-1-1s-1 0-1 1c0 2 1 4 0 6h-1v-2h-3v-1c0-1 1-2 1-3l-1-1v-1-4-2-1c0-2 0-3-1-5l-1-1c0-1 0-1 1-2v-1c-1-1-1-2-1-3l2-1c0-1 1-2 1-3-1 0-2-1-3-1l1-1h4l1 1 1-1c-1 1 0 1-1 1l1 1c1-1 2-1 3-1s1-1 2-1 2-1 4-1c1 0 4-3 5-4 1 0 2 0 3-1 2-1 3-2 5-2h-1v-1h1c1 0 2-1 4-1 1-1 1-1 2-1h0c2-1 2-3 4-4 0 0 1 0 1-1 1 0 1 0 2 1h4z" class="M"></path><path d="M378 175c1 0 1 0 2 1h4c-1 0-1 1-1 2h0 0c-1 0-1 1-1 1h-3c-2 1-3 1-4 2-3 0-5 1-8 2h-1v-1h1c1 0 2-1 4-1 1-1 1-1 2-1h0c2-1 2-3 4-4 0 0 1 0 1-1z" class="P"></path><path d="M384 176c0-1 1-1 2-1h5c-1 1-1 1-1 2l1 1c-1 1-2 1-3 1h-1c-1 1-3 1-4 2h-4v1h-2-1-2c3-1 8-2 10-3l-1-1h0c0-1 0-2 1-2z" class="U"></path><path d="M338 192l1-1h4l1 1 1-1c-1 1 0 1-1 1l1 1c1-1 2-1 3-1l1 1c1-1 1-1 2-1-1 1-2 3-4 4-1 0-2 1-3 2-1 0-2 0-3 1h0-1v-3c0-1 1-2 1-3-1 0-2-1-3-1z" class="H"></path><path d="M345 193c1-1 2-1 3-1l1 1h-2c-1 0 0 1-1 1l-1-1z" class="F"></path><path d="M338 192l1-1h4l1 1 1-1c-1 1 0 1-1 1 0 2 0 3-1 4h-2v1c1 0 2 1 3 1-1 0-2 0-3 1h0-1v-3c0-1 1-2 1-3-1 0-2-1-3-1z" class="Q"></path><path d="M364 185l1 1c0 1 0 1-1 1-1 1-2 1-2 2h0l-2 2h2l-3 2v2l-1-1v1c-1 1-1 2-2 2-1-1-1-2-2-2h-1 0l-1-1 1-2c1-1 5-3 6-4 2-1 4-2 5-3z" class="V"></path><path d="M353 192c1 1 1 1 1 3h-1 0l-1-1 1-2z" class="B"></path><path d="M362 189h0l-2 2h2l-3 2h-3v-1c1 0 1 0 2-1l4-2z" class="K"></path><path d="M379 182h1v1h2-2l-1 1-2 1-4 1h-1 0c-2 0-2 1-3 2v1c-1 0-1 1-1 1l1 1c-1 0-1 1-2 1v1-1h-2c-1 0-1 0-2-1h-1-2l2-2h0c0-1 1-1 2-2 1 0 1 0 1-1l-1-1 8-3h2 2 1 2z" class="H"></path><path d="M362 191h1c1 1 1 1 2 1h2v1 1c1 0 1 0 1 1h-1-2-1l1 1h1c1 0 1 0 1 1v1c-1 0-1 0-2 1v1 1h-3c-1-1-1-1-2-1h-1c-1 0-2 1-3 2l-2 2-2-2h0c0-1 1-1 1-2-1 0-1 0-2-1l-1 1h-1c1-1 2-2 3-2v-1c1 0 1-1 1-2h1c1 0 1 1 2 2 1 0 1-1 2-2v-1l1 1v-2l3-2z" class="G"></path><path d="M362 191h1c1 1 1 1 2 1h2v1 1c1 0 1 0 1 1h-1-2-1-1c-1-1 0-1-1-2h-1l-1 2h-1v-2l3-2z" class="E"></path><path d="M365 192h2v1 1h-3v-1l1-1z" class="N"></path><path d="M346 198c2-2 4-3 6-4l1 1h0c0 1 0 2-1 2v1c-1 0-2 1-3 2h1l1-1c1 1 1 1 2 1 0 1-1 1-1 2h0v2c-1 1-1 0-1 1h0c1 1 1 2 1 3s0 2-1 3v1c1 1 1 2 1 3v2l-1 1c-1 0-1-1-1-2v-1h-1v1h-1c1-1 1-1 1-2h-2c0-1 0-2-1-2v6l-1-1s-1 0-1 1c0 2 1 4 0 6h-1v-2h-3v-1c0-1 1-2 1-3l-1-1v-1-4-2-1c0-2 0-3-1-5l-1-1h2v-2c1-1 1-1 3-1 0-1 1-2 2-2h1z" class="L"></path><path d="M346 204l2-2h0c1 1 0 2 0 3-1 0-1 0-1 1-1 0-1-1-1-1v-1z" class="X"></path><path d="M345 198c0 1 1 2 1 2 0 1-1 3-2 4l-1-1h1v-2h-1v-1c0-1 1-2 2-2z" class="C"></path><path d="M349 213c0-2 0-4 1-6 0 0 0 1 1 1 0 2 0 4-1 6v2-1h-1v1h-1c1-1 1-1 1-2v-1z" class="B"></path><path d="M351 208h1c0 1 0 2-1 3v1c1 1 1 2 1 3v2l-1 1c-1 0-1-1-1-2v-2c1-2 1-4 1-6z" class="f"></path><path d="M346 198c2-2 4-3 6-4l1 1c-2 0-1 1-3 2h-2v2 2l-1 1v-3l-1-1z" class="C"></path><path d="M346 205s0 1 1 1v5h1 0c0-1 0-1 1-2v4 1h-2c0-1 0-2-1-2v6l-1-1c0-4 1-7 1-10l-1 1h-1v-2l2-1z" class="M"></path><path d="M343 200v1h1v2h-1l1 1h2v1l-2 1-1 1v-1h0v2l-1-1-2 2c0-2 0-3-1-5l-1-1h2v-2c1-1 1-1 3-1z" class="E"></path><path d="M343 203l1 1h2v1l-2 1-1 1v-1-3z" class="B"></path><path d="M340 203h2v1s0 1-1 1v1h2v2l-1-1-2 2c0-2 0-3-1-5l-1-1h2z" class="I"></path><defs><linearGradient id="AO" x1="340.601" y1="218.708" x2="347.508" y2="214.573" xlink:href="#B"><stop offset="0" stop-color="#242525"></stop><stop offset="1" stop-color="#3e3d3e"></stop></linearGradient></defs><path fill="url(#AO)" d="M343 206v1l1-1v2h1l1-1c0 3-1 6-1 10 0 0-1 0-1 1 0 2 1 4 0 6h-1v-2h-3v-1c0-1 1-2 1-3l-1-1v-1-4-2-1l2-2 1 1v-2h0z"></path><path d="M342 214c0-1 1-1 1-2h0 1l-1 3h0v7h-3v-1c0-1 1-2 1-3l-1-1c1-1 1-1 1-2l1-1z" class="I"></path><path d="M341 215l1-1c1 1 1 2 0 3 0 1-1 1-1 1l-1-1c1-1 1-1 1-2z" class="F"></path><path d="M340 209l2-2 1 1c0 1 0 1 1 2v2h-1 0c0 1-1 1-1 2l-1 1c0 1 0 1-1 2v-1-4-2-1z" class="J"></path><path d="M340 210l2 2v1l-1 2c0 1 0 1-1 2v-1-4-2z" class="E"></path><path d="M346 218v-6c1 0 1 1 1 2h2c0 1 0 1-1 2h1v-1h1v1c0 1 0 2 1 2 1 2 0 4 0 6 0 0 1 0 0 1h0 0v6 13h2l1-1v1 1h0-1-1c-1 0-1 1-1 1h-1-2-1l-1 1h-1c-2 2-1 6-1 8 0 0 0-1-1-1v1l-1 9 1 8-1 15c0 2 1 4 0 6h-1v6 2c-1 2 0 4-1 6v1c-1-2-1-1-2-2 0-1 0-2-1-3h0c1-1 1-2 0-3l1-1h0c1-1 1-2 1-3h-2l-1 1c-1 0-2-1-3-1l1 1c-1 1-2 1-4 1h-1v-1l-1-1s-1-1-2-1v1 1c-1 0-2 0-2 1h-2v-2c-1 0-2-1-3-2h-1c-1 4 0 8 0 12-1 0-1 0-1-1v-1c-1-3-1-6-1-9h0v1c-2-2-2-2-4-2 0-2 0-3 1-5h0v-1-1-4-4c0-1-1-2-2-3l-1-2-1-1 1-2v-1l11-10v-2h0v-8h0-2v-1h1c1-1 0-2 1-3l1 1c0-1 1-1 2-2h0l1-1h0l1 1c1-1 1-1 3-2 0-1-1-2-1-4v-2c1 0 2-1 3-1h0v-3h2 0v-1-1h-1l1-1h-1 1c1-1 2-1 3-1 1-1 2-1 2-2 1-1 1-2 0-3v-1h1l1-1h3v2h1c1-2 0-4 0-6 0-1 1-1 1-1l1 1z" class="d"></path><path d="M340 222h3v2c-1 1-1 2-1 3l-1 1v4h-2v-2h1l-1-1c0-1 0-1-1-2 1-1 1-2 0-3v-1h1l1-1z" class="C"></path><path d="M338 224h1c1 0 1 1 1 2h0v3 1l-1-1c0-1 0-1-1-2 1-1 1-2 0-3z" class="i"></path><path d="M339 232h2v5 3 2c-1 0-1 0-2 1h1 0 2l-1 1s-1 0-1 1h1c0 1-1 1-2 1h0-2 0 0v-1l1-1h-1v-2-1c0-1 1-1 1-2v-1h1l1-1h-2c0-2 0-3 1-5z" class="F"></path><path d="M336 229c1-1 2-1 2-2 1 1 1 1 1 2l1 1h-1v2c-1 2-1 3-1 5h2l-1 1h-1v1c0 1-1 1-1 2-1 0-2 0-3-1-1 0-2-1-3-2h-2l-1 1v-2c1 0 2-1 3-1h0v-3h2 0v-1-1h-1l1-1h-1 1c1-1 2-1 3-1z" class="U"></path><path d="M328 239v-2c1 0 2-1 3-1 0 1 1 1 2 2 0 0 1 0 2-1v1c0 1-1 2-1 2-1 0-2-1-3-2h-2l-1 1z" class="D"></path><path d="M336 232h1s0-1 1-2h1v2c-1 2-1 3-1 5-1-1-2-1-2-1v-2h1v-1l-1-1z" class="B"></path><path d="M336 229c1-1 2-1 2-2 1 1 1 1 1 2l1 1h-1-1c-1 1-1 2-1 2h-1-1l-2 1h0v-1-1h-1l1-1h-1 1c1-1 2-1 3-1z" class="I"></path><path d="M328 239l1-1h2c1 1 2 2 3 2 1 1 2 1 3 1v1 2h1l-1 1v1h0l2 2h1 1v1 7h-3v5c-1 0-3 0-3-1-1 0-1-2-2-1s-1 2-1 3l-1-10c0-3-1-6-2-9 0-1-1-2-1-4z" class="U"></path><path d="M337 246h0l2 2h1 1v1 7h-3v-4l-1 1c-1-1-1-2-1-3-1 0-1 0-1-1 1-1 1-1 2-3z" class="I"></path><path d="M337 246h0l2 2c0 1 0 1-1 2s-1 0-2 0-1 0-1-1c1-1 1-1 2-3z" class="P"></path><path d="M328 239l1-1h2c1 1 2 2 3 2 1 1 2 1 3 1v1 2h1l-1 1c-1 0-2 0-2 1-1 1-1 2-1 3h-1c-1 1-1 1-1 2l1 1v1 2l-1-1-1-2c0-3-1-6-2-9 0-1-1-2-1-4z" class="Y"></path><path d="M333 249v-2c0-1 1-2 1-2 0-1 0-2 1-3h2 0v2h1l-1 1c-1 0-2 0-2 1-1 1-1 2-1 3h-1z" class="O"></path><path d="M326 245c1-1 1-1 3-2 1 3 2 6 2 9l1 10c-1 1 0 3 0 4h-1c-1-1-1-2-3-3-1 0-1 0-2 1h-1v-1c-1-1-2-2-2-3v-1c-1 0-1 0-2 1v-2h0v-8h0-2v-1h1c1-1 0-2 1-3l1 1c0-1 1-1 2-2h0l1-1h0l1 1z" class="Z"></path><path d="M328 256v1h1 1c1 2 0 3 0 5h-1v-1c-1-1-2-2-3-2v-3h2z" class="D"></path><path d="M327 246l2 1v1 2 1h0c1 1 0 3 1 5h0c-1 0-1 0-2-1v1h-2v-1c0-1 0-1 1-1v-1l-1-1v-1c1-2 1-3 1-5z" class="N"></path><path d="M325 244h0l1 1h1l-1 1h1c0 2 0 3-1 5v1c-1 0-1 1-2 1v-1c-1-1-1-1-3-2h0-2v-1h1c1-1 0-2 1-3l1 1c0-1 1-1 2-2h0l1-1z" class="G"></path><path d="M322 247c0-1 1-1 2-2v4 1l-1-1c-1-1-1-1-1-2z" class="R"></path><path d="M325 244h0l1 1h1l-1 1c0 1 0 2-2 3v-4h0l1-1z" class="o"></path><path d="M321 250c2 1 2 1 3 2v1c1 0 1-1 2-1l1 1v1c-1 0-1 0-1 1v1 3c1 0 2 1 3 2l-1 1v1c-1 0-1 0-2 1h-1v-1c-1-1-2-2-2-3v-1c-1 0-1 0-2 1v-2h0v-8z" class="N"></path><path d="M327 262h1v1c-1 0-1 0-2 1h-1v-1c1-1 1-1 2-1z" class="O"></path><path d="M326 252l1 1v1c-1 0-1 0-1 1v1 3c1 0 2 1 3 2l-1 1h-1c-1-2-2-3-3-4v-2-1-2c1 0 1-1 2-1z" class="d"></path><path d="M346 218v-6c1 0 1 1 1 2h2c0 1 0 1-1 2h1v-1h1v1c0 1 0 2 1 2 1 2 0 4 0 6 0 0 1 0 0 1h0 0v6 13h2l1-1v1 1h0-1-1c-1 0-1 1-1 1h-1-2-1l-1 1h-1c-2 2-1 6-1 8 0 0 0-1-1-1v1c0-4 0-8-1-12 0-1 1-4 1-6v-4c0-1 1-3 0-4v-5h1c1-2 0-4 0-6 0-1 1-1 1-1l1 1z" class="K"></path><path d="M346 218v-6c1 0 1 1 1 2h2c0 1 0 1-1 2h0c0 1 0 0 1 1v5 1c-1 1 0 3 0 4v5c-1 2 0 5 0 7l-2-1v-5l1-1c0-1-1-1-1-2l-1-1c-1-1 0-3 0-5h-1c1-2 1-4 1-6z" class="i"></path><path d="M349 227l-2-2v-1-1c0-1-1-6 0-8l1 1c0 1 0 0 1 1v5 1c-1 1 0 3 0 4z" class="J"></path><path d="M343 224h1c1-2 0-4 0-6 0-1 1-1 1-1l1 1c0 2 0 4-1 6h1c0 2-1 4 0 5l1 1c0 1 1 1 1 2l-1 1v5l2 1c-1 2 0 4-1 5-1 0-2-1-2-1-1-1-1-1-1-2h-1v-3l-1-1v-4c0-1 1-3 0-4v-5z" class="C"></path><path d="M346 236c-1 0-1 0-1 1h0c-1 0-1-1-1-1 0-2 0-3 2-4v4z" class="E"></path><path d="M346 229l1 1c0 1 1 1 1 2l-1 1v5l2 1c-1 2 0 4-1 5-1 0-2-1-2-1v-7-4-3z" class="d"></path><path d="M338 256h3v1 2 1 1-2h1c1 1 0 3 0 5l1 8-1 15c0 2 1 4 0 6h-1v6 2c-1 2 0 4-1 6v1c-1-2-1-1-2-2 0-1 0-2-1-3h0c1-1 1-2 0-3l1-1h0c1-1 1-2 1-3h-2l-1 1c-1 0-2-1-3-1l1 1c-1 1-2 1-4 1h-1v-1l-1-1s-1-1-2-1v1-2s-1-1 0-2v-2l1-1h2v-6c1-2 1-4 1-6 1-1 1-1 1-2v-1l-1-1v-2l1-2c1-1 1-2 1-3s-1-3 0-4c0-1 0-2 1-3s1 1 2 1c0 1 2 1 3 1v-5z" class="b"></path><path d="M326 294h0c1 0 1 0 1-1 1 0 1-1 1-1h0c0 1 1 3 1 5l-1-1s-1-1-2-1v1-2z" class="I"></path><path d="M326 290l1-1h2l-1 3h0s0 1-1 1c0 1 0 1-1 1h0s-1-1 0-2v-2z" class="E"></path><path d="M338 277h3v8h-4 0c-1 0-1 0-1-1-1-1-1-1-1-2h2v-1c1-1 1-1 1-2h0v-1h1v-1h-1z" class="K"></path><path d="M333 296l-1-1c1 0 1-1 1-2h-1c-1-1-1-1-1-2v-1c1 1 1 1 2 1h0c0-1 1-1 1-1 0-1 0-1-1-2h0v-1h1v1l2 1v-1l1 1 2-1v1h-1c0 1 1 1 1 2-1 0-2 0-3 1l1 1 1-1 1 1c-1 1 0 2 0 3h-2l-1 1c-1 0-2-1-3-1z" class="B"></path><path d="M341 273h1l1-1-1 15c0 2 1 4 0 6h-1v-4h-1c0 1 0 1-1 2 0-1-1-1-1-2h1v-1l-2 1-1-1c1-1 2-1 2-2l-1-1h4v-8c0-1-1-2 0-3v-1z" class="a"></path><path d="M341 277c0-1-1-2 0-3v12 3h-1c0 1 0 1-1 2 0-1-1-1-1-2h1v-1l-2 1-1-1c1-1 2-1 2-2l-1-1h4v-8z" class="F"></path><path d="M340 289v-1c-1-1-1-1 0-1l1-1v3h-1z" class="C"></path><path d="M341 289v4 6 2c-1 2 0 4-1 6v1c-1-2-1-1-2-2 0-1 0-2-1-3h0c1-1 1-2 0-3l1-1h0c1-1 1-2 1-3s-1-2 0-3l-1-1-1 1-1-1c1-1 2-1 3-1 1-1 1-1 1-2h1z" class="E"></path><path d="M337 303h2 1v-1l-1-2c1-1 1-1 2-1v2c-1 2 0 4-1 6v1c-1-2-1-1-2-2 0-1 0-2-1-3z" class="C"></path><path d="M338 256h3v1 2 1 1-2h1c1 1 0 3 0 5l1 8-1 1h-1v1c-1 1 0 2 0 3h-3v-4h0c0-1-1-2-1-3s1-2 1-2l1-1c-1 0-1 0-1-1s-1-2 0-3v-2-5z" class="E"></path><path d="M341 261v-2h1c1 1 0 3 0 5l1 8-1 1h-1v-1c1-2 0-5 0-7v-2-2z" class="W"></path><path d="M321 260c1-1 1-1 2-1v1c0 1 1 2 2 3v1h1c1-1 1-1 2-1 2 1 2 2 3 3h1c0 1 0 2-1 3l-1 2v2l1 1v1c0 1 0 1-1 2 0 2 0 4-1 6v6h-2l-1 1v2c-1 1 0 2 0 2v2 1c-1 0-2 0-2 1h-2v-2c-1 0-2-1-3-2h-1c-1 4 0 8 0 12-1 0-1 0-1-1v-1c-1-3-1-6-1-9h0v1c-2-2-2-2-4-2 0-2 0-3 1-5h0v-1-1-4-4c0-1-1-2-2-3l-1-2-1-1 1-2v-1l11-10z" class="D"></path><path d="M321 284v-1h-1c0-1 0-2 1-4h0c1 1 1 2 1 3h2v1c-1 0-1 1-1 1v1c-1 0-2-1-2-1z" class="H"></path><path d="M325 278l1 1c-1 1 0 2 0 3v4h1 0 0c-1 0-2-1-3 0h-4v-1l1-1s1 1 2 1v-1s0-1 1-1v-1-3h0l1-1z" class="I"></path><path d="M315 287l1 1v7h0v1c-2-2-2-2-4-2 0-2 0-3 1-5l2-2z" class="Q"></path><path d="M316 277c1 1 2 1 3 2v7 1l-2-2v1h-1c0 1-1 1-1 1h0l-2 2h0v-1-1-4c0 1 0 1 1 1h1c0-2 1-2 2-4h-1l-1 1h0c0-2 0-2 1-4z" class="C"></path><path d="M323 272c1 1 1 1 2 1h2 2 1l1 1v1c0 1 0 1-1 2 0 2 0 4-1 6v6h-2l-1 1c0-2 0-2 1-4h0 0-1v-4c0-1-1-2 0-3l-1-1-2-2c-1-1-1 1-2 1l-2-3h1l2-1 1-1z" class="M"></path><path d="M327 277h1 1v1l-1 1h-1v-2z" class="G"></path><path d="M326 279h1c0 1 0 2-1 3 0-1-1-2 0-3z" class="J"></path><path d="M327 273h2c0 1 1 1 0 3h0v1h-1-1c-1 0-1-1-1-1h0-1c1-2 1-2 2-3z" class="C"></path><path d="M326 276l1-1c1 0 1 0 1 1v1h-1c-1 0-1-1-1-1z" class="Q"></path><path d="M323 272c1 1 1 1 2 1h2c-1 1-1 1-2 3h1 0s0 1 1 1v2h-1l-1-1-2-2c-1-1-1 1-2 1l-2-3h1l2-1 1-1z" class="E"></path><path d="M323 272c1 1 1 1 2 1l-1 1-1 1h-2l-1-1 2-1 1-1z" class="H"></path><path d="M321 260c1-1 1-1 2-1v1c0 1 1 2 2 3v1h1c1-1 1-1 2-1 2 1 2 2 3 3h1c0 1 0 2-1 3l-1 2v2h-1-2-2c-1 0-1 0-2-1l-1 1-2 1h-1c0 1-1 2-2 3h-1c-1 2-1 2-1 4h0l1-1h1c-1 2-2 2-2 4h-1c-1 0-1 0-1-1v-4c0-1-1-2-2-3l-1-2-1-1 1-2v-1l11-10z" class="X"></path><path d="M315 268v-1l1-1 2 2-2 2c0-1-1-2-1-2z" class="D"></path><path d="M325 270c1 0 1-1 2-1 1 1 2 1 2 2v1h-2l-2-2h0z" class="H"></path><path d="M322 267l1 1c1 0 2-1 2 1l-2 2c-1-1-2-1-3-2 1-1 1-2 2-2z" class="C"></path><path d="M325 270h0l2 2h2v-1h1v2h-1-2-2c-1 0-1 0-2-1l2-2z" class="B"></path><path d="M328 263c2 1 2 2 3 3h1c0 1 0 2-1 3-1-1-2-1-3-1v-1l-2-2h1l-1-1c1-1 1-1 2-1zm-13 5s1 1 1 2h-1l1 2-1 1-1 1c0-1-1-1-2-2l-1 1h-1v1l-1-1 1-2c1 0 2-2 4-3h0 1z" class="K"></path><path d="M320 269l-4-3c1-1 4-5 6-5v1 2h1l2 3h0c-1-1-2-1-4-1l1 1c-1 0-1 1-2 2z" class="O"></path><path d="M318 268l3 3c0 1 1 1 1 2l-2 1h-1c0 1-1 2-2 3h-1c-1 2-1 2-1 4h0l1-1h1c-1 2-2 2-2 4h-1c-1 0-1 0-1-1v-4c0-1-1-2-2-3l-1-2v-1h1l1-1c1 1 2 1 2 2l1-1 1-1-1-2h1l2-2z" class="J"></path><path d="M318 268l3 3v1c-1 1-2 1-3 1-1-1-2-1-2-2l-1-1h0 1l2-2zm-8 6v-1h1l1-1c0 2 0 2 2 3 1 0 2-2 4-1-1 1-1 1-1 2h-2s0 1-1 2c0 1-1 0-1 1 0-1-1-2-2-3l-1-2z" class="G"></path><path d="M336 488h0v-1-2l1-1c1 1 1 1 2 1h0c1 0 1 1 2 1v-1l-1 1c-1 1-1 1-1 2v2 2 3c0 1 0 1 1 2 1 0 1-1 2-1v3s1 0 1 1h1l1 1v1l-1 1c0 1 0 3 1 4h1 0 1l1-1c0-1 1-1 1-3h0l-1-1 1-1v-1-1-4s-1 0-1-1 0-2 1-3h1v2-2c2 0 2-1 3-2l1 1 1 2c1 1 1 0 1 2h-1v1c1 0 1 0 2-1v1 1h-1v2l1 1h0c1 1 1 8 1 10h-1v23c0 1 1 1 1 1 0 2 1 7 0 9v6c-2 1-2 1-3 1h-2s-1 1-2 1h0v1h1c1 1 1 2 0 4h0v1 3 4l-1 1h0c0-1-2-2-3-3v1l-1-1v1h-1-1-1l-1 1v-2h-2v2l-2 1h-1v1l-1 1v4c-1 1-1 2-2 2 0-1-1-2-1-3h-1l-1 1c0-1 0-1-1-2v-1h-1v-1c1-2 0-4 0-6l-1-2-1-1-2-1h0l-1-2h-1c0 1-1 1-1 2h-1s-1-1-1-2l-1-3h1c1 0 2 2 3 2v-2c1-1 1-1 1-2h1v-2l1-1v-1l-1-1c0-1 0-3 1-5h1c-1 0-1-1-1-2l-1-1c-1-1-1 0-2-1v-1l1-1v1c1 0 1-1 2-2l1 1s0-1 1-1h0v1h1v-4h-1v-3h0l-1 1v-1c-1-1-1-1-2-1v-1c-1 0-2 0-3-1 0 1 0 1-1 2h0-1c0-1-1-1-1-2s0-1 1-2h0l-1-1h0l-2-2h0c1-1 1-2 1-2l-1-1v-1c1-1 0-1 1-3v-6c-1-2 0-3 0-4s1-1 2-1v-3 1l1-1 1 1v2l1-1c0-1 0-2-1-3l-1-1 1-1h3l1-2v-1h1v1l2-1c1 0 1 0 1-1v-1h2v-1h3 0z" class="K"></path><path d="M354 536v-2h1v3h-1v-1z" class="V"></path><path d="M331 547h1v1h0c-1 0-2 1-2 1s-1-1-1-2h2z" class="C"></path><path d="M329 547c0 1 1 2 1 2-1 1-2 1-3 1h-1c2-1 1-2 3-3z" class="H"></path><path d="M338 543h1l1 1v2h-2v-3z" class="C"></path><path d="M343 548c2 0 3-1 4 0v2h-2-2v-2z" class="J"></path><path d="M327 546h4v1h-2c-2 1-1 2-3 3v-1-2l1-1z" class="E"></path><path d="M334 543v-1l2 1 1-1v1 1h1l-1 2h-3v1l-2-2c1 0 1-2 2-2z" class="C"></path><path d="M334 537l3-1c1 0 2 1 2 1l-1 2c-1 0-1 0-2-1 0 1-1 1-1 2l-2 1v-2s1-1 1-2z" class="F"></path><path d="M349 545c1 0 2-1 2-1l1-1c2 2 1 2 4 3h0v1c-1 0-2 1-3 1h0c-1-1-2 0-4-1h0-1l1-1v-1z" class="S"></path><path d="M344 543v-2s-1 0-2-1h0v-3h0c1-1 2-1 2-2l1-1v5h1c0 2 0 2-1 4h0-1z" class="B"></path><path d="M326 552c1-1 2-1 3-1s1 0 2-1v1c0 1-1 1-2 1 0 1 1 1 1 2h0l1 1c-1 0-1 0-2 1v2l-1-1-2-1h0l-1-2v-1l1-1z" class="H"></path><path d="M326 552l1 1v2l-1 1-1-2v-1l1-1z" class="E"></path><path d="M356 546l-1-1c0-2-1-3 0-5 0 0-1 0-1-1 1-1 2 0 3 0l1 3v6c-2 1-2 1-3 1l1-2v-1h0z" class="L"></path><path d="M327 537l1 1c1 1 1 2 2 3 1 0 1 0 2-1l1-1v2h-1l-1 1 1 1v2l-1 1h-4v-1l-1-1c0-1 0-3 1-5h1c-1 0-1-1-1-2z" class="H"></path><path d="M346 507h1l1-1h0 0 1 0v3 3 3c-1 0-1-1-3-1l-1 1v-1-1-1l-1-3v-1l1-1h1 0z" class="M"></path><path d="M345 507h1 0v7h0l-1 1v-1-1-1l-1-3v-1l1-1z" class="N"></path><path d="M330 527c1-1 1-1 2-1v1h0c1 0 2 0 3 1 1 0 1 0 2 1l1 1s0 1 1 2h1l-2 1 1 1h1l1 1c-1 1-1 2-2 2 0 0-1-1-2-1l-3 1v-1h1v-2h0v-1l-1-1c0-1 0-1 1-2h-4c0-1 0-2-1-3z" class="C"></path><path d="M330 527c1-1 1-1 2-1v1h0c0 1 0 2 1 2h2 0v1h-4c0-1 0-2-1-3z" class="M"></path><path d="M354 526h1 1l1 1v5h0c0 1 1 1 1 1-1 1-2 1-3 1h-1v2h-1v1c-1 0-1-1-1-1-1 0-1 0-1 1h-1v-1-2c0-1 1-1 1-2v-1h0l2 1h1v-6z" class="G"></path><path d="M353 536v-2h-1 0v-1h1l1-1h3 0 0c0 1 1 1 1 1-1 1-2 1-3 1h-1v2h-1z" class="S"></path><path d="M352 510s1 0 1-1c2 2 1 3 1 4 1 1 1 2 1 3h1v-4c1 0 0-2 0-3h1v23h0v-5l-1-1h-1-1v-1-2-1-1c0-1 0-3-1-4v-4-1l-1 1h-1v-2l1-1z" class="B"></path><path d="M346 539h0v-2-2c-1-1-1-1-1-2 1-1 1-3 2-4 0 0 0 1 1 1v-1l1-1c0 1 1 2 0 4v3 1c1 1 0 2 0 3 0 2 1 4 0 6v1h-1-1c0-1-1-1-1-1 0-1 0-1-1-1v1h-1 0v-2h1 0c1-2 1-2 1-4z" class="C"></path><path d="M331 530h4c-1 1-1 1-1 2l1 1v1h0v2h-1v1c0 1-1 2-1 2l-1 1c-1 1-1 1-2 1-1-1-1-2-2-3l-1-1-1-1c-1-1-1 0-2-1v-1l1-1v1c1 0 1-1 2-2l1 1s0-1 1-1h0v2h1l2-1-1-1v-2z" class="E"></path><path d="M326 536h4l1 1h1l-1 2-2-2c0 1 0 1-1 1l-1-1-1-1z" class="X"></path><path d="M324 534l1-1v1c1 0 1-1 2-2l1 1s0-1 1-1h0v2h1l2-1 1 1v1l-2 2-1-1h-4c-1-1-1 0-2-1v-1zm23 14h2v1c0 1-1 2-1 3h0c-1 0-1 1-1 1 0 1 0 1 1 2v-1h0c1-1 1-1 1-2h1v3l1-1v-1-1c1 1 1 4 1 4v3 4l-1 1h0c0-1-2-2-3-3v1l-1-1v1h-1-1-1l-1 1v-2-1-2c-1 0-1 0-1-1v-1h1c2-1 1-3 1-4s1-1 1-2h2v-2z" class="B"></path><path d="M348 561c0-1 0-3 1-4h1v3h1v-1h1v4l-1 1h0c0-1-2-2-3-3zm0-9h0c-1 0-1 1-1 1 0 1 0 1 1 2v-1h0c1-1 1-1 1-2h1v3h-1-1c-1 0-1 1-1 2-1 1 0 3-2 4h0v-4h1v-1c-1-1-1-3 0-4h2z" class="E"></path><path d="M339 513h1 0v3l2 2 1-1c-1 1 0 2 0 2v5 1h1c0-1 0-1 1-2h1l3-3v3c0 1 0 2-1 3l-1-1c0 1 1 2 0 3l-2-1c0 1 0 3-1 4h-2l-2 1h-1c-1-1-1-2-1-2l-1-1c0-1 1-1 2-2h0v-1l-1-1h0 1 0v-7c-1-1 0-4 0-5z" class="I"></path><path d="M338 530c1-2 1-1 3-2 0 1 1 2 0 3h-1 0c0-1 0-1-1-1 0 1 0 1 1 2h-1c-1-1-1-2-1-2z" class="Z"></path><path d="M331 555c1-1 1-2 1-4h1c0 1 0 1 1 2h0c1-1 0-2 1-3 0-1 1-2 1-2h3c1 1 1 1 2 1s2-1 2-1v2h2c0 1-1 1-1 2s1 3-1 4h-1v1c0 1 0 1 1 1v2 1h-2c0-1 0-1 1-2 0 0 0-1-1-1v-5 1c-1 0-1 1-1 2h-2l-2-2-1 2v3h-1c-1-1-1-1-2 0l-2 1-1-2v-2c1-1 1-1 2-1z" class="V"></path><path d="M329 556c2 1 1 2 3 2v1l-2 1-1-2v-2z" class="E"></path><path d="M332 558v-1c0-1 0-1 1-2 1 0 1 1 2 1v3h-1c-1-1-1-1-2 0v-1zm4-4h0 1c1-1 1-2 2-4 1 0 2 0 3 1-1 1-1 1-1 2v1c-1 0-1 1-1 2h-2l-2-2z" class="B"></path><path d="M341 553v5c1 0 1 1 1 1-1 1-1 1-1 2v2l-2 1h-1v1l-1 1v4c-1 1-1 2-2 2 0-1-1-2-1-3h-1l-1 1c0-1 0-1-1-2v-1h-1v-1c1-2 0-4 0-6l2-1c1-1 1-1 2 0h1v-3l1-2 2 2h2c0-1 0-2 1-2v-1z" class="D"></path><path d="M334 563v-2-1c1 1 0 1 1 2 1 0 1-1 2-2h0 1v-2h1c1 1 1 1 1 2h-1l1 2h-1-1v1l-1 1h-2v-1h-1z" class="G"></path><path d="M335 563h0 2v1h-2v-1z" class="I"></path><path d="M330 560l2-1c0 1 0 3-1 4 0 1 1 1 2 2v-2h1 1v1h2l1-1v1 1l-1 1v4c-1 1-1 2-2 2 0-1-1-2-1-3h-1l-1 1c0-1 0-1-1-2v-1h-1v-1c1-2 0-4 0-6z" class="C"></path><path d="M333 565v-2h1 1v1c0 2 1 2 1 4-1 0-2 0-2 1h-1l-1 1c0-1 0-1-1-2v-1l1-1 2 2h0v-3h-1z" class="N"></path><path d="M338 563v1 1l-1 1v4c-1 1-1 2-2 2 0-1-1-2-1-3s1-1 2-1c0-2-1-2-1-4h2l1-1z" class="G"></path><path d="M338 563v1 1l-1 1v-2l1-1z" class="O"></path><path d="M353 489l1 1 1 2c1 1 1 0 1 2h-1v1c1 0 1 0 2-1v1 1h-1v2l1 1h0c1 1 1 8 1 10h-1-1c0 1 1 3 0 3v4h-1c0-1 0-2-1-3 0-1 1-2-1-4 0 1-1 1-1 1s0-1-1-1l2-2v-1-1l-1-1v-2c-1 0-1 0-2 1l-1-2v-1-1-4s-1 0-1-1 0-2 1-3h1v2-2c2 0 2-1 3-2z" class="U"></path><path d="M353 489l1 1 1 2c1 1 1 0 1 2h-1v1c1 0 1 0 2-1v1 1h-1-2v2h-1l-1-1h0v2h-1c-1 0-1 0-1-1s1-2 1-2h2v-2-1h0-1-2v-2c2 0 2-1 3-2z" class="C"></path><path d="M342 503h1 1c0 1 0 3 1 4l-1 1v1l1 3v1 1 1l1-1c2 0 2 1 3 1 0 2 1 3 0 5h0l-3 3h-1c-1 1-1 1-1 2h-1v-1-5s-1-1 0-2l-1 1-2-2v-3h0-1l-3-2c-1 1-1 2-2 2v-3l-1-1h-1v-1h3l1 1h1v-2c2 0 1-2 2-3h0 1l1-1h1z" class="G"></path><path d="M343 517l-1 1-2-2v-3l1 1c1 1 1 1 2 1 0 1-1 1-1 2h1 0z" class="J"></path><path d="M342 503h1 1c0 1 0 3 1 4l-1 1v1l1 3c0 1 0 1-1 2-2 0-2-1-4-2 0-1 0-2-1-3-1 0-2 1-3 0h0 1v-2c2 0 1-2 2-3h0 1l1-1h1z" class="Z"></path><path d="M337 507c2 0 1-2 2-3h0 1v2c0 1 0 2 1 3-1 1-1 2-1 3 0-1 0-2-1-3-1 0-2 1-3 0h0 1v-2zm5-4h1 1c0 1 0 3 1 4l-1 1v1h-1c0 2 1 2 0 4h-1v-4c-1-2 0-4 0-6z" class="V"></path><path d="M343 503h1c0 1 0 3 1 4l-1 1h-1v-5z" class="M"></path><path d="M336 488h0v-1-2l1-1c1 1 1 1 2 1h0c1 0 1 1 2 1v-1l-1 1c-1 1-1 1-1 2v2 2 3c0 1 0 1 1 2 1 0 1-1 2-1v3s1 0 1 1h1l1 1v1l-1 1h-1-1-1l-1 1h-1 0c-1 1 0 3-2 3v2h-1l-1-1h-3v1h1l1 1v3c1 0 1-1 2-2l3 2c0 1-1 4 0 5v7h0-1 0l1 1v1h0c-1 1-2 1-2 2-1-1-1-1-2-1-1-1-2-1-3-1h0v-1c-1 0-1 0-2 1 1 1 1 2 1 3v2l1 1-2 1h-1v-2 1h1v-4h-1v-3h0l-1 1v-1c-1-1-1-1-2-1v-1c-1 0-2 0-3-1 0 1 0 1-1 2h0-1c0-1-1-1-1-2s0-1 1-2h0l-1-1h0l-2-2h0c1-1 1-2 1-2l-1-1v-1c1-1 0-1 1-3v-6c-1-2 0-3 0-4s1-1 2-1v-3 1l1-1 1 1v2l1-1c0-1 0-2-1-3l-1-1 1-1h3l1-2v-1h1v1l2-1c1 0 1 0 1-1v-1h2v-1h3 0z" class="G"></path><path d="M323 519h1v1c1 0 0 1 0 2h-1v-3z" class="B"></path><path d="M334 522h1l1-1h1c1 1 1 2 1 3-1-1-1-1-2-1l-1 1c-1 0-1 0-2-1l1-1z" class="F"></path><path d="M329 516l-1 2-2 1v-1h1c-2-1-2-2-2-4h1 1l1 1 1 1z" class="N"></path><path d="M320 505l1-1c1 2 1 6 1 6h0c-1 1-1 2-2 2v-5c-1-1-1-2 0-2z" class="i"></path><path d="M330 527h0c0-1 0-1-1-2 1-1 1-1 2-1s1 1 1 1l1 1 2-1v1 1h1c1 0 1 0 2-2l1 1v1h0c-1 1-2 1-2 2-1-1-1-1-2-1-1-1-2-1-3-1h0v-1c-1 0-1 0-2 1z" class="R"></path><path d="M334 513c1 0 1-1 2-2l3 2c0 1-1 4 0 5v7h0-1v-1c0-1 0-2-1-3h-1l-1 1h-1c-1-1-2-1-3-1l-1 1-1-1v-1h1c0-1-1-1-1-2h2v-1-1l2-1h0l1-2z" class="X"></path><path d="M333 515h0c1 1 1 2 1 3v2h0l-2-2s0-1-1-1v-1l2-1z" class="i"></path><path d="M321 497v1l1-1 1 1v2l1-1c0-1 0-2-1-3l-1-1 1-1h3l1 1v2h0v2 1 2 6c-1 1 0 2-1 4h-1-1v1 1c0 1 0 1-1 2h-1v1l-1-1c0-1 0-2 1-3 0-1 1-1 1-2l-1-1s0-4-1-6l-1 1h-1c-1-2 0-3 0-4s1-1 2-1v-3z" class="D"></path><path d="M321 497v1l1-1 1 1v2l1-1c0-1 0-2-1-3l-1-1 1-1h3l1 1h-2 0v7h-1v1 1l-1-1v-2c-1-1-1-1-2-1v-3z" class="G"></path><path d="M336 488h0v-1-2l1-1c1 1 1 1 2 1h0c1 0 1 1 2 1v-1l-1 1c-1 1-1 1-1 2v2 2 3c0 1 0 1 1 2 1 0 1-1 2-1v3s1 0 1 1h1l1 1v1l-1 1h-1-1-1l-1 1h-1 0c-1 1 0 3-2 3v2h-1l-1-1h-3v1h1l1 1v3l-1 2h0l-2 1v1 1h-2s0-1 1-1l-1-1-1-1-1-1v-1-1h-1c1-2 0-3 1-4v-6-2-1-2h0v-2l-1-1 1-2v-1h1v1l2-1c1 0 1 0 1-1v-1h2v-1h3 0z" class="L"></path><path d="M339 490c-1 0-1 0-2-1 0-1 0-1 1-2l1 1v2zm0 5c-1 0-1-1-2-3h1 1v3z" class="k"></path><path d="M338 497l1 1v2l-1 2-1-2 1-3z" class="a"></path><path d="M335 497l1-1v1h1 1l-1 3h-1 0-2c-1-2 0-1 1-3z" class="F"></path><path d="M333 496c1 0 1 0 2 1-1 2-2 1-1 3h2 0c0 1-1 2-1 3h-3v-1h0 1v-6z" class="Z"></path><path d="M334 500h2 0c0 1-1 2-1 3l-1-1 1-1-1-1z" class="R"></path><path d="M330 491c1 0 1 0 1-1 0 2 0 2 2 3h2v1c-1 0-2 1-2 2h0v6h-1 0l-1-1c1-2 1-2 0-4h0v-4c-1-1-1-1-1-2z" class="I"></path><path d="M336 488h0v2 3l-1 1v-1h-2c-2-1-2-1-2-3v-1h2v-1h3z" class="M"></path><path d="M336 488h0v2l-1 1h-1c-1-1-1-1-1-2v-1h3z" class="f"></path><path d="M336 500h1l1 2h-2 0 0c-1 1-1 2-2 2 0 1 0 1 1 1h-2l-1 1c-1 0-2 1-2 1h-1-1l1-4h0l1-1 1-1 1 1v1h3c0-1 1-2 1-3z" class="E"></path><path d="M331 501l1 1v1l-1 1h-1l-1-1 1-1 1-1z" class="a"></path><path d="M342 499s1 0 1 1h1l1 1v1l-1 1h-1-1-1l-1 1h-1 0c-1 1 0 3-2 3l-1-2h-1c-1 0-1 0-1-1 1 0 1-1 2-2h0 0 2l1-2h1l2-1z" class="C"></path><path d="M342 499s1 0 1 1h1l1 1v1l-1 1h-1-1-1-1v-3l2-1z" class="B"></path><path d="M327 491h1v1l2-1c0 1 0 1 1 2v4h0c1 2 1 2 0 4l-1 1c0-1 0-1-1-1l-2 1v-2-1-2h0v-2l-1-1 1-2v-1z" class="R"></path><path d="M328 495l1 1h0c-1 1-1 2-2 3v-2h0l1-2z" class="J"></path><path d="M330 497h1c1 2 1 2 0 4l-1 1c0-1 0-1-1-1l-2 1v-2c1 0 2 0 3-1v-2z" class="X"></path><path d="M327 491h1v1l2-1c0 1 0 1 1 2v4h0-1c0-1 0-1-1-1h0l-1-1-1 2v-2l-1-1 1-2v-1z" class="Z"></path><g class="f"><path d="M328 495h2c1 0 1 0 1 2h0-1c0-1 0-1-1-1h0l-1-1z"></path><path d="M327 491h1v1h2c0 1-1 2-2 2 0 1-1 1-1 1v-3-1z"></path></g><path d="M327 502l2-1c1 0 1 0 1 1l-1 1h0l-1 4h1 1s1-1 2-1l1-1h2 1l1 2v2h-1l-1-1h-3v1h1l1 1v3l-1 2h0l-2 1v1 1h-2s0-1 1-1l-1-1-1-1-1-1v-1-1h-1c1-2 0-3 1-4v-6z" class="Q"></path><path d="M336 505l1 2v2h-1l-1-1c0-1 0-2 1-3z" class="d"></path><path d="M333 509l1 1v3l-1 2h0c-1-1-1-1-1-2 1-1 1-2 1-4z" class="a"></path><path d="M330 514v-1h1c1 1 1 2 0 3v1 1h-2s0-1 1-1l-1-1-1-1c1 0 1-1 2-1z" class="I"></path><path d="M327 502l2-1c1 0 1 0 1 1l-1 1h0l-1 4 1 1c-1 2 0 2 0 3s-1 1-1 2h0c1 0 1 0 2 1-1 0-1 1-2 1l-1-1v-1-1h-1c1-2 0-3 1-4v-6z" class="M"></path><path d="M391 175h0 3c3-1 7 0 10 0h-2c-1 1-1 1-1 2s1 1 1 1v1h3v2c1 1 1 0 2 0l1 2c1-1 1-1 2-1h3 0l-1 2v1h6 1l1 1c1 1 3 0 5 0 1-1 5 0 7 0h10-2c-1 1-1 2-2 2-1 1-1 0-2 1h1l1 1c0 1-1 2-1 3l-1 1c-1 0-1 0-1 1h0l-1 1h-1l-1 2h0c1 0 1 0 2-1h2v1h3c1-1 4-1 5-1 0 1-1 2-1 3-1 0-1 0-1 1h3c1 0 2 1 3 1h1 0c-1 1-1 2-1 3h1l-1 1h-1-2l-1 1c1 0 1 1 2 2v-1c1 0 2-1 3-1 2-1 3-1 5 0v1c0 2-1 2-2 3v1c1 1 1 1 1 2h2c0 1-2 3-3 3 0 1-1 2-1 3l1 1v1c0 1 0 2 1 3s2 3 2 5h0l-1 1h0v2h0c0 1 1 2 1 3-1 0-1 0-2-1 1 1 1 2 1 2 0 1-1 2-2 3h0l-1 1h-1l3 6h0l-2 1h0c-2-1-1-2-2-3s-1-2-1-2c0-2 0-3-1-5 0-1-1-1-1-2s0-1-1-2h-1c-1-1-1-1-2-1 0-2 0-4-1-5-1 0-1 0-1-1 0-2-2-3-3-5v2c-1 0-1 0-2 1h-1c0-1 0-2-1-3l-2 1c0-1-2-2-2-2-1-1-1-1-2-1v1l1 1h-2-1v1h-1-1c1 1 1 2 1 3-1-1-1 0-2 0h-1c0-1 0-1-1-1v-1c-1 0-1-1-2-1-1-1-1-1-2-1-2-1-3-3-5-4-1 0-1-1-2-1-3-2-5-3-8-4l-2-1-2-1h-1-2c-2-1-3-2-5-2 0-1-2-1-2-1-2 0-2 0-4-1-1 0-1 0-2-1h-2l-3-2-10-3h-1v-1-1c1-1 1-1 2-1v-1c0-1 0-1-1-1h-1l-1-1h1 2 1c0-1 0-1-1-1v-1-1c1 0 1-1 2-1l-1-1s0-1 1-1v-1c1-1 1-2 3-2h0 1l4-1 2-1 1-1h2-2v-1h-1v-1h4c1-1 3-1 4-2h1c1 0 2 0 3-1l-1-1c0-1 0-1 1-2z" class="P"></path><path d="M437 189l1 1c0 1-1 2-1 3l-1 1c-1 0-1 0-1 1h0-3c-1-1-2-1-3-2h6c1 0 1-1 1-1v-1h-1 0-3 0l1-1h2c1 0 1 0 2-1z" class="G"></path><path d="M400 199h1c1-1 2-1 2-2 1-1 0-1 0-1 1-1 5-3 6-4-2 2-3 4-5 6v3h2v1c0 1 0 1 1 2h0c-2 0-2 0-3-1s-2-2-3-2c0-1 0-2-1-2z" class="M"></path><path d="M410 196h0-1v-1h0c0-1 0-1 1-1h1c2-1 3-1 5-1 0 1 0 1 1 2v-1h2l1 1h4v1h-2 0-5c-1 0-1 1-2 1h0l-1 1v-1h-1c-1 1-2 1-3 0v-1z" class="H"></path><path d="M410 196l5-1h1l-2 2h0-1c-1 1-2 1-3 0v-1z" class="X"></path><path d="M432 186h10-2c-1 1-1 2-2 2-1 1-1 0-2 1h1c-1 1-1 1-2 1h-2l-1 1h-1-3c-1-1-1 0-2 0h-3 0v-1c1 0 2 0 3-1 0 0 0-1 1-1h1v1h1s2-1 2-2c1 0 1 0 1-1z" class="B"></path><path d="M408 183c1-1 1-1 2-1h3 0l-1 2v1h6 1l1 1c-3 0-6 0-10 1h-2-1c1 1 2 1 2 2h-1v1h7 0v1h-6c0 1 0 1-1 1l-1-1h-3l-1-1v1c-1 0-2 1-2 1h-3v1c2 0 3 1 4 0h0l1 1c0 1-1 1-2 2-1 0-1 0-1-1l-1 2h-1c0-1 0-2-1-2l-1-1h-3v-2h-1l-3 3v1c-1 0-2 1-2 1 0 1 0 1-1 2-1 0-1 0-2-1h0 2l-1-1c0-1 1-1 1-2v-1h1v-1l-1-1c2-2 4-1 6-1l1-1v1h1s1-1 2-1v1h3 1l1-1h2 0v-1l-1-2h-3c1 0 1-1 1-1h2v-1c-1 0-2 0-3-1 2-1 3-1 5-1h0 4z" class="H"></path><path d="M393 191h1s1-1 2-1v1 2h-1c0-1-1-1-2-2z" class="T"></path><path d="M399 187h3l1 2v1h0-2l-1 1h-1-3v-1c-1 0-2 1-2 1h-1v-1l-1 1c-2 0-4-1-6 1l1 1v1h-1v1c0 1-1 1-1 2l1 1h-2 0c1 1 1 1 2 1 1-1 1-1 1-2 0 0 1-1 2-1v-1l3-3h1v2h3l1 1c1 0 1 1 1 2h1l-1 1v1h1l-3 1 1 2c-1 0-1 0-2 1 0 2 1 3 1 5 0 1 0 2 1 3h-1-2c-2-1-3-2-5-2 0-1-2-1-2-1-2 0-2 0-4-1-1 0-1 0-2-1h-2l1-2h0v-3-3-1c1-1 2 0 3-1v-1h-1c0-1 1-1 1-1v-1h-1c-1 0-1 0-2 1l-1-1v-1c3 0 3-1 5-2l3-2v2c1 0 2 0 2-1 1 1 1 1 2 1 1-1 3-1 4-2h0 1 1 1c0-1 1-1 1-1z" class="D"></path><path d="M385 202v-2h0 2v1h2c0 1-1 1-1 1h-3z" class="B"></path><path d="M392 196h1c0 1 0 2 1 2h1l1 2c-2 0-3 0-4-1v-3z" class="X"></path><path d="M380 204h2l1-1h0v1c0 1 0 1 1 2h-3-2l1-2h0z" class="C"></path><path d="M391 202c1 0 2 1 3 2v1l-2 2c0-1-1-1-1-1v-1-1-2z" class="B"></path><path d="M394 205v1c0 1 0 2-1 3l2 1-1 1c-2-1-3-2-5-2l2-1c-1-1-1-1-1-2h1s1 0 1 1l2-2zm-1-11h3l1 1c1 0 1 1 1 2h1l-1 1v1h1l-3 1h0l-1-2h-1c-1 0-1-1-1-2h-1l1-2z" class="Z"></path><path d="M397 195c1 0 1 1 1 2-1 0-2 0-3-1v-1h2 0z" class="E"></path><path d="M389 201v-1l1 1h0c1 0 1 1 1 1v2 1 1h-1c0 1 0 1 1 2l-2 1c0-1-2-1-2-1-1-1-1-1-1-2 0 0 1-1 1-2s-1 0-2-1v-1h3s1 0 1-1z" class="G"></path><path d="M391 175h0 3c3-1 7 0 10 0h-2c-1 1-1 1-1 2s1 1 1 1v1h3v2c1 1 1 0 2 0l1 2h-4 0c-2 0-3 0-5 1 1 1 2 1 3 1v1h-2s0 1-1 1c0 0-1 0-1 1h-1-1-1 0c-1 1-3 1-4 2-1 0-1 0-2-1 0 1-1 1-2 1v-2l-3 2c-2 1-2 2-5 2v1l1 1c1-1 1-1 2-1h1v1s-1 0-1 1h1v1c-1 1-2 0-3 1v1 3 3h0l-1 2-3-2-10-3h-1v-1-1c1-1 1-1 2-1v-1c0-1 0-1-1-1h-1l-1-1h1 2 1c0-1 0-1-1-1v-1-1c1 0 1-1 2-1l-1-1s0-1 1-1v-1c1-1 1-2 3-2h0 1l4-1 2-1 1-1h2-2v-1h-1v-1h4c1-1 3-1 4-2h1c1 0 2 0 3-1l-1-1c0-1 0-1 1-2z" class="K"></path><path d="M374 193l1 1c0 1 0 1-1 2h1l1-1h1l-1 1-1 1s0 1 1 1l-1 2h-2 0v-3-1c0-1 0-2 1-3z" class="J"></path><path d="M391 175h0 3c3-1 7 0 10 0h-2c-1 1-1 1-1 2s1 1 1 1v1h3v2c1 1 1 0 2 0l1 2h-4 0c-2 0-3 0-5 1h0-3-1 0v-1h3c2-2 1-3 1-5h0c-1 0-1-1-1-1-1 0-2 0-2 1h0-3 0-2 0l-1-1c0-1 0-1 1-2z" class="P"></path><path d="M374 193l1-2h1v-1c1-1 1-1 2-1 0 0 0 1 1 1s1-1 1-1c0-1 1-1 2-1 0-1 1-1 2-1l1-1 1 1c1 0 2 0 3-1l1 1h0c-1 2-2 1-3 1h0l-3 2c-2 1-2 2-5 2v1l1 1c1-1 1-1 2-1h1v1s-1 0-1 1h1v1c-1 1-2 0-3 1h-2c-1-1-1-1-2-1l1-1h-1l-1 1h-1c1-1 1-1 1-2l-1-1z" class="C"></path><path d="M364 195h1 2 1s0 1 1 2l1-1h1v1c0 1-1 1-1 2l2 2h1l1-1h-1 2l1-2c-1 0-1-1-1-1l1-1c1 0 1 0 2 1h2v1 3 3h0l-1 2-3-2-10-3h-1v-1-1c1-1 1-1 2-1v-1c0-1 0-1-1-1h-1l-1-1z" class="Q"></path><path d="M377 200h1s0 1 1 1h1v3h0l-1 2-3-2c1 0 1 0 1-1l-1-1v-1l1-1z" class="I"></path><path d="M380 201v3h0c-1 0-1-1-2-1 0-1 0-1 1-2h1z" class="J"></path><path d="M364 195h1 2 1s0 1 1 2 0 2 0 4h-1v-2h-1c-1 1-1 1-1 2h-1v-1-1c1-1 1-1 2-1v-1c0-1 0-1-1-1h-1l-1-1z" class="D"></path><path d="M375 200l1-2c-1 0-1-1-1-1l1-1c1 0 1 0 2 1h2v1 3h-1c-1 0-1-1-1-1h-1-2z" class="H"></path><path d="M378 200s0-1 1-2h0 1v3h-1c-1 0-1-1-1-1z" class="X"></path><path d="M382 183h1s1 0 1-1c2-1 5-1 7-1h0c0 1 0 1-1 1h-2c-2 1-3 1-4 2h0c1 0 2 0 3-1h3v-1h1c0 1 1 1 1 1-1 1-4 1-5 2 0 0 0-1-1 0-2 1-4 1-6 2h-1c-1 0-2 1-3 1v1h-2c0 1-1 1-1 2h0c-1 0-1 1-1 2-1 0-1 1-1 2h-1 0v-1c-1-1-2-1-3-1v-1c1 0 1-1 2-1l-1-1s0-1 1-1v-1c1-1 1-2 3-2h0 1l4-1 2-1 1-1h2z" class="V"></path><path d="M369 189h1v1c0 1 0 1-1 1l-1-1s0-1 1-1z" class="B"></path><path d="M424 195v-1c1-1 4-1 5-1 1 1 2 1 3 2-2 0-3 0-4 2-2-1-2-2-4-1l-2 2s-1 1-1 2c-1 0-1 0-2 1v1h0c1 0 2 0 3-1l1 1v3l-1 1c1 0 1 1 1 2 2-1 2-2 3-3l1 1h1 2l1 1c-1 1-2 2-2 3h-1 0l2 1h0v1c-1 0-1 1-1 1 1 1 2 1 2 2h1l1 2h1c0 1 0 2 1 2v1 1h1l1 1v2c-1 0-1 0-2 1h-1c0-1 0-2-1-3l-2 1c0-1-2-2-2-2-1-1-1-1-2-1v1l1 1h-2-1v1h-1-1c1 1 1 2 1 3-1-1-1 0-2 0h-1c0-1 0-1-1-1v-1c-1 0-1-1-2-1-1-1-1-1-2-1-2-1-3-3-5-4-1 0-1-1-2-1-3-2-5-3-8-4l-2-1-2-1c-1-1-1-2-1-3 0-2-1-3-1-5 1-1 1-1 2-1l-1-2 3-1h-1v-1c1 0 2 0 2 1 1 0 1 1 1 2 1 0 2 1 3 2s1 1 3 1h0l1-1c1 0 1 1 2 1v-1-2l1-1h0c1-1 2 0 3 0 0-1 1-1 0-2l1-1h0c1 0 1-1 2-1h5 0 2v-1z" class="N"></path><path d="M401 201c1 0 2 1 3 2-2 0-2 1-2 1-1-1-1-2-1-3z" class="C"></path><path d="M414 198l1-1c0 1 1 2 2 3l-1 1h-1l-1-1c0-1 1-1 0-2z" class="G"></path><path d="M404 206c1 0 1 0 2-1 1 1 1 3 2 5-1 0-2-1-3-2 0-1-1-1-1-2z" class="Z"></path><path d="M415 205c-1 0-1 1-2 1 0-1-1-1-1-1h-1v-1h1c0-1 1-1 1-1 1 0 1 0 2-1h1c-1 2-1 2-1 3z" class="Q"></path><path d="M419 215c0-1 1-1 2-2l1 3-1 2s-1-1-2-1v-2z" class="R"></path><path d="M408 210v-1h3v1c-1 0-1 0-2 1 1 1 1 1 1 2v2 1h-1v-1-1h0l-1-2v-2z" class="S"></path><path d="M415 197h0c1 0 1-1 2-1h5c-2 1-3 2-5 4-1-1-2-2-2-3z" class="d"></path><path d="M412 207c1-1 2 0 3 0v5c0 1 1 2 1 3h-1c-1-1-2-2-3-4h1l-1-1v-3z" class="J"></path><path d="M421 206v3 4c-1 1-2 1-2 2 0-1-1-1-1-2v-3c-1-1-1-1-1-3h1 2l1-1z" class="E"></path><path d="M411 218h1 1c1 1 2 2 3 2 0-1-1-1-1-2h-1 1 0 2s0 1 1 1c1 1 2 1 3 2h2c0-1 1-1 1-1h2c1 0 1-1 2-1 0 1 0 1-1 1v1l1 1h-2-1v1h-1-1c1 1 1 2 1 3-1-1-1 0-2 0h-1c0-1 0-1-1-1v-1c-1 0-1-1-2-1-1-1-1-1-2-1-2-1-3-3-5-4z" class="B"></path><path d="M399 199c1 1 1 2 1 3v1l1 1-1 2h0c1 0 1 1 1 1v1h1c-1 1-1 1-1 2l1 1c0 1-1 2-1 2l-2-1-2-1c-1-1-1-2-1-3 0-2-1-3-1-5 1-1 1-1 2-1l-1-2 3-1z" class="O"></path><path d="M396 208c1 0 1 0 2-1v3c0 1 1 1 1 2l-2-1c-1-1-1-2-1-3z" class="c"></path><path d="M399 199c1 1 1 2 1 3v1l-2-1h-1c0 1 0 1 1 2v3c-1 1-1 1-2 1 0-2-1-3-1-5 1-1 1-1 2-1l-1-2 3-1z" class="F"></path><path d="M422 201l1 1v3l-1 1c1 0 1 1 1 2 2-1 2-2 3-3l1 1h1 2l1 1c-1 1-2 2-2 3h-1 0c0 1 0 1-1 2h1l-1 1h0l-1 1h-1v1h2v1h-1l-2-1c0-1-1-2-1-2-1-1 0-2-1-3l-1-1v-3l-1 1h-2-1s0-1 1-2l-2-1-1 1c0-1 0-1 1-3 1 1 1 1 2 1 0 0 0-1 1-1h0c1 0 2 0 3-1z" class="C"></path><path d="M418 205h0l1-1c1 0 1 1 2 1v1l-1 1h-2-1s0-1 1-2z" class="H"></path><path d="M428 206h2l1 1c-1 1-2 2-2 3-1-1-1-1-2-1 0-1 0-2 1-3z" class="F"></path><path d="M423 213v-4c1 0 2 0 3 1v1s-1 0-1 1h0l2 1-1 1h-1v1h2v1h-1l-2-1c0-1-1-2-1-2z" class="a"></path><path d="M428 210l2 1h0v1c-1 0-1 1-1 1 1 1 2 1 2 2h1l1 2h1c0 1 0 2 1 2v1 1h1l1 1v2c-1 0-1 0-2 1h-1c0-1 0-2-1-3l-2 1c0-1-2-2-2-2-1-1-1-1-2-1 1 0 1 0 1-1-1 0-1 1-2 1h-2v-1-4l2 1h1v-1h-2v-1h1l1-1h0l1-1h-1c1-1 1-1 1-2z" class="C"></path><path d="M432 215l1 2h-2c0 1 1 2 1 3h0 2v1h-1v1l-2 1c0-1-2-2-2-2 0-1 0-1 1-1h0l1-1h-1c0-1 0-2-1-2v-1l1-1c0 1 1 1 1 1l1-1z" class="H"></path><path d="M428 210l2 1h0v1c-1 0-1 1-1 1 0 1-1 2-1 2 0 1 0 1 1 1l-2 2v1h-1 0c0-1-1-1-1-1v-1h1v-1h1v-1h-2v-1h1l1-1h0l1-1h-1c1-1 1-1 1-2z" class="E"></path><path d="M433 217h1c0 1 0 2 1 2v1 1h1l1 1v2c-1 0-1 0-2 1h-1c0-1 0-2-1-3v-1h1v-1h-2 0c0-1-1-2-1-3h2z" class="F"></path><defs><linearGradient id="AP" x1="436.593" y1="233.158" x2="441.543" y2="199.311" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#AP)" d="M432 195h3l-1 1h-1l-1 2h0c1 0 1 0 2-1h2v1h3c1-1 4-1 5-1 0 1-1 2-1 3-1 0-1 0-1 1h3c1 0 2 1 3 1h1 0c-1 1-1 2-1 3h1l-1 1h-1-2l-1 1c1 0 1 1 2 2v-1c1 0 2-1 3-1 2-1 3-1 5 0v1c0 2-1 2-2 3v1c1 1 1 1 1 2h2c0 1-2 3-3 3 0 1-1 2-1 3l1 1v1c0 1 0 2 1 3s2 3 2 5h0l-1 1h0v2h0c0 1 1 2 1 3-1 0-1 0-2-1 1 1 1 2 1 2 0 1-1 2-2 3h0l-1 1h-1l3 6h0l-2 1h0c-2-1-1-2-2-3s-1-2-1-2c0-2 0-3-1-5 0-1-1-1-1-2s0-1-1-2h-1c-1-1-1-1-2-1 0-2 0-4-1-5-1 0-1 0-1-1 0-2-2-3-3-5l-1-1h-1v-1-1c-1 0-1-1-1-2h-1l-1-2h-1c0-1-1-1-2-2 0 0 0-1 1-1v-1h0l-2-1h0 1c0-1 1-2 2-3l-1-1h-2-1l-1-1c-1 1-1 2-3 3 0-1 0-2-1-2l1-1v-3l-1-1c-1 1-2 1-3 1h0v-1c1-1 1-1 2-1 0-1 1-2 1-2l2-2c2-1 2 0 4 1 1-2 2-2 4-2z"></path><path d="M436 211c1 1 1 1 0 2h1l1 1 2 2c2 1 2 0 3 1h-1l-1 1c1 0 1 0 1 1h-2v-2c-2 0-2 0-2 1l-1-1h-1l1-1h-1c0-1 0-1-1-2l1-1v-1-1z" class="G"></path><path d="M423 205h2v-1h-1l1-1h4l1 1v2h-2-1l-1-1c-1 1-1 2-3 3 0-1 0-2-1-2l1-1z" class="K"></path><path d="M433 211h0c0-2 1-1 1-3h-1 0c1-1 2-2 2-3 1 1 1 2 2 2s1 0 1 1l-1 2-1 1v1 1l-1 1c-1-1-1-2-2-3z" class="F"></path><path d="M433 211h1 1v-1s1-1 2-1v1l-1 1v1 1l-1 1c-1-1-1-2-2-3z" class="Q"></path><path d="M442 201h3c1 0 2 1 3 1 0 1-1 1-2 1h0-1l1 1-1 1h0c-2 0-2 1-3 2v-3h-2-1c-1 0-1 0-2-1h1c2-1 2-2 4-2zm-12-1c0-1 0-1 1-2h0l-1-1-1 1c-1 1-1 1-2 1v-1c2-1 3-2 6-2l-1 2h0c1 0 1 0 2-1h2v1h3v1s0 1-1 1h-1 0l-1-1v2h0l-1-1c-1 1-2 1-3 2h-1v-1l-1-1zm17 20c1 0 2-1 3-2s1-1 2-1h0c0 1-1 2-1 3l1 1v1c0 1 0 2 1 3s2 3 2 5h0l-1 1c-1-1-1-2-2-2-1-1-1-3-2-4l-2-4-1-1z" class="H"></path><path d="M448 221c1-1 1-1 2-1 0 1 1 2 1 4l-1 1-2-4z" class="Z"></path><path d="M432 195h3l-1 1h-1c-3 0-4 1-6 2v1c1 0 1 0 2-1l1-1 1 1h0c-1 1-1 1-1 2-1 0-2 1-2 1-2-1-3-1-4-1-1 1-1 1-1 2l-1-1c-1 1-2 1-3 1h0v-1c1-1 1-1 2-1 0-1 1-2 1-2l2-2c2-1 2 0 4 1 1-2 2-2 4-2z" class="F"></path><path d="M422 201c0-1 0-2 1-3h2l-1 1v1c-1 1-1 1-1 2l-1-1z" class="G"></path><path d="M438 218c0-1 0-1 2-1v2h2c0-1 0-1-1-1l1-1h1v2c1 1 2 2 2 3-1 0-1 0-1 1h0 0 2c0 1-1 2-1 3h1c0 2 0 2-1 3v1h-1c0-1 0-1-1-2h0v-1c-1-2-2-3-3-5v-1c-1-1-2-2-2-3z" class="V"></path><path d="M434 217c1 0 2 0 3-1l-1 1h1l1 1c0 1 1 2 2 3v1c1 2 2 3 3 5v1h0c1 1 1 1 1 2v1c1 0 1 0 1 1v1h0v1h-1c-1-1-1-1-2-1 0-2 0-4-1-5-1 0-1 0-1-1 0-2-2-3-3-5l-1-1h-1v-1-1c-1 0-1-1-1-2z" class="P"></path><path d="M448 202h1 0c-1 1-1 2-1 3h1l-1 1h-1-2l-1 1c1 0 1 1 2 2-1 0-2 1-2 1v2 2c-1 0-1 0-1 1-1 0-1 0-2 1h0-1c0-2-1-2-1-3 0-2 0-2 1-4h0c1-1 1-2 2-2 1-1 1-2 3-2h0l1-1-1-1h1 0c1 0 2 0 2-1z" class="C"></path><path d="M446 209v-1c1 0 2-1 3-1 2-1 3-1 5 0v1c0 2-1 2-2 3v1c1 1 1 1 1 2h2c0 1-2 3-3 3h0c-1 0-1 0-2 1s-2 2-3 2c0-1-2-2-2-3 0 0 0-1-1-1l-1-1c0-1 0-1 1-1v-2-2s1-1 2-1z" class="E"></path><path d="M444 212l3-3h1v2h-1c0 1-1 1-1 2l-1 1h-1v-2z" class="H"></path><path d="M444 216c1-1 2-2 3-2v-1l1-1c2-1 2-2 4-3 0 2-4 3-5 6h1l4-4v1c1 1 1 1 1 2h2c0 1-2 3-3 3h0c-1 0-1 0-2 1s-2 2-3 2c0-1-2-2-2-3 0 0 0-1-1-1z" class="C"></path><path d="M446 223c0 1 1 2 1 3h1c1 1 1 4 1 5h2c1 1 1 2 1 3h1v1c1 1 1 2 1 2 0 1-1 2-2 3h0l-1 1h-1l3 6h0l-2 1h0c-2-1-1-2-2-3s-1-2-1-2c0-2 0-3-1-5 0-1-1-1-1-2s0-1-1-2v-1h0v-1c0-1 0-1-1-1v-1h1v-1c1-1 1-1 1-3h-1c0-1 1-2 1-3z" class="B"></path><path d="M451 248c0-1-1-3-1-4h-1l1-2c-1 0-1 0-1-1h1l3 6h0l-2 1z" class="T"></path><path d="M449 231h2c1 1 1 2 1 3h1v1c1 1 1 2 1 2 0 1-1 2-2 3h0v-2c0-1-1-1-1-2v-2c-1 0-1 0-1-1v-1s-1 0-1-1h0z" class="O"></path><path d="M316 518v-2c0-1 2-1 2-1l1 1s0 1-1 2h0l2 2h0l1 1h0c-1 1-1 1-1 2s1 1 1 2h1 0c1-1 1-1 1-2 1 1 2 1 3 1v1c1 0 1 0 2 1v1l1-1h0v3h1v4h-1v-1h0c-1 0-1 1-1 1l-1-1c-1 1-1 2-2 2v-1l-1 1v1c1 1 1 0 2 1l1 1c0 1 0 2 1 2h-1c-1 2-1 4-1 5l1 1v1l-1 1v2h-1c0 1 0 1-1 2v2c-1 0-2-2-3-2h-1l1 3c0 1 1 2 1 2h1c0-1 1-1 1-2h1l1 2h0l2 1 1 1 1 2c0 2 1 4 0 6v1h1v1c1 1 1 1 1 2l1-1h1c0 1 1 2 1 3 1 0 1-1 2-2v-4l1-1v-1h1l2-1v-2h2v2l1-1h1 1 1v-1l1 1v-1c1 1 3 2 3 3-1 1-2 3-2 4 0 0 0 1 1 1h-1c-1 2-1 3-2 5v1h1v-1l2 2h0-1-1-1-1l-1 1h0l1 2v2c-1 0-1 0-1 1v1l-1 2h0c1 0 2 0 2 1l1 1h-1v1h-1 0v4 3 1h-1 0l-1-1h-4c-2 1-3 1-5 1-1 0-2 0-2-1h-1c0 1-1 2-2 3h-1c-1 0-1-1-2-1-1 1-2 1-3 1h-1l-1-1v1 1l-1 1h-1-3-2c-1-2-1-2-3-2l1-1h1l1-1h-11-6l-1-1c0-1 0-1 1-1-1 0-1-1-2-1v-1h-1v-4h0l1-2h1c0-1 0-1-1-1 1-1 1-1 1-2h-1c0-2-1-2-1-3v-1s1 0 1-1l-2-1h0v-1l-2-3s1 0 1-1h0c-1-1-1-1-1-2v-1h-1l-1-1c1-1 1-2 2-3l1 1v-1h0c0-2 0-2-1-3v-3h-1l-1 1h1v2h-1v2h0c-1 0-1 0-1-1v-1h-1v1c-1 0-1 1-1 1h-1v-2c1-1 0-2 1-3 0-2 0-5 1-8l1-4v-2c1 0 2 1 2 1h0 4s1 0 2 1l1-1v-1-1-1c1 0 1-1 1-2h-1 0-1 0v-1h2l1-1 1-2c1 1 2 1 2 2v1h1 1l1-1h-1l1-1c0-1 0-1 1-2 1 1 2 1 3 2l-1 2h1v-3-2-6l1-1h2 0c-1-1-1-2-2-3 0 0 0-1-1-2h-1l1-2v-2-2c1 1 2 1 3 1h1v-1c0-1 0-1 1-1h0v1l2 1z" class="U"></path><path d="M323 588h-1 0v-3l2 1v1s0 1-1 1z" class="C"></path><path d="M325 591l-1 1c-1-1-2 0-3-1v-1h2 2v1z" class="L"></path><path d="M317 567c1 0 1 0 2 1v1h-3c-1-1-1-1-1-2h0 1 1z" class="D"></path><path d="M304 556h1c0 1 1 1 2 2-2 0-1 1-2 1s-2-1-2-2l1-1z" class="O"></path><path d="M306 576h-2v-2-2l2 1v3z" class="H"></path><path d="M312 549h0 2v1h-2v1l1 1h-1l-1-1s-1-1 0-2h1z" class="D"></path><path d="M308 540h1l1 1 1 1h0c-2 1-3-1-4-1h-1v-1c1-1 1 0 2 0z" class="P"></path><path d="M314 560c1-1 1-1 0-2 1-1 1-1 1-2 1 1 1 1 1 2s1 1 1 2h0c-1 1-1 1-2 1l-1-1z" class="I"></path><path d="M304 556c0-1 0-2-1-3v-1c1 0 1 0 2 1l1 1v1c-1 0-1 0-1 1h-1z" class="C"></path><path d="M314 560l1 1c1 0 1 0 2-1 0 1 0 1-1 2v1 1l-2-1v1l-1-1-1-2s0-1 1-1v1h1v-1z" class="G"></path><path d="M321 566s1 0 1 1h1l1 1h-1c-1 0-1-1-2-1l-1 1v1h-1v-1c-1-1-1-1-2-1v-1h3 1z" class="B"></path><path d="M311 598l1-1h1c0 1 0 1 1 2h1l1-1h2 1v2h-3-2c-1-2-1-2-3-2z" class="C"></path><path d="M330 585c0-1 0-1-1-2h-1v-1h0c2 0 2 1 3 2l1 1c-1 1-1 1-2 1l1 2h-1l-2-2 2-1z" class="H"></path><path d="M332 579h2v2s1 0 1 1c0 0-1 0 0 1v1c0 1 1 1-1 2 0 0 0-1-1-1l1-2-2-1v-3z" class="I"></path><path d="M306 573h1c1 0 2 1 2 1 2 1 2 2 4 2v1h-2c0-1-1-1-1-1-1 1-2 0-3 0h-1v-3z" class="G"></path><path d="M306 573h1v1h1l-1 2h-1v-3z" class="N"></path><path d="M308 559c0 1 2 2 2 3h-3v1l1 1h1v1h-1c-1 0-2 0-3-1-1 0-1 0-1-1s0-1 1-2h1 1l1-2z" class="T"></path><path d="M326 556l2 1c-1 1-1 2-2 3v-1c-1 0-1 1-2 2h0c0 1 0 1-1 2-1 0-1-1-2-2h0v-2c0-1 3-1 4-2 1 0 1-1 1-1z" class="D"></path><path d="M321 561c1 0 2-1 2-1l1 1c0 1 0 1-1 2-1 0-1-1-2-2h0z" class="E"></path><path d="M292 572c-1-1-1-1-1-2v-1h-1l-1-1c1-1 1-2 2-3 0 2 1 3 1 5 0 1 1 1 1 2l1 1-1 2h3l1-1v1c-1 0-2 1-2 1l-2 1v-1l-2-3s1 0 1-1h0z" class="H"></path><path d="M303 595h2c0-1 1-2 2-2l1 1c1 1 1 0 2 1h4c0-1 1-1 1-1 1 0 1 0 2 1l1-1v1s-1 1-2 1h-2-11v-1z" class="Q"></path><path d="M305 553l1-1c0-1 0-2-2-3v-1-2c2 0 2 0 4 1v4h2c-1 0-1 1-1 1 0 1-1 1-1 2h-2l-1-1z" class="D"></path><path d="M317 532h0c-1 2-1 3-1 4l2 2c1 0 1 0 1 1h-1c-2-1-4 0-6 0v-1c0-1 0-1 1-1v-1h0c0-1 0-1 1-2h0v-1-1c1 0 1 1 2 1l1-1z" class="K"></path><path d="M316 579l-2 2c-1 0-2 0-3-1l-1-1v1c-1 0-3 0-3-1l-1-1-1 1v-1h0l1-1h2v1c2 0 3-1 3-1h2v1h1 2 0v1z" class="B"></path><path d="M319 596v-2c2 0 6-1 8 0h1c-1 1-3 1-4 2h-2v2l-1-1v1 1l-1 1h-1v-2h-1c1-1 1-2 1-2z" class="I"></path><path d="M319 596h1c1 0 1 0 1-1l1 1v2l-1-1v1 1l-1 1h-1v-2h-1c1-1 1-2 1-2z" class="K"></path><path d="M317 560l2-1h0l-1 2c1 1 1 1 2 1 0 0 1 0 1-1 1 1 1 2 2 2v1 1h0c-1 0-2 0-2 1h-1-3v-1-1-2l-1 1v-1c1-1 1-1 1-2h0zm-3 18v-1h1c0-1 1-1 2-1 0 0 1 0 2-1h2l1-1h2l-2 2h-1 0-1c-1 0-2 1-2 2 1 0 1 1 2 1 0 2 0 2-1 4h-1v-2h-1c0-1 0-1-1-1v-1-1h0-2z" class="D"></path><path d="M299 539l1-2c1 1 2 1 2 2v1h-2l-1 1h0v3c0 1-1 2 0 3 0 1 0 1 1 1l1-1v1 1h-3v1c-1 0-1 0-2-1v-1l-1-1h0 1l1-1v-1-1-1c1 0 1-1 1-2h-1 0-1 0v-1h2l1-1z" class="B"></path><path d="M293 577l2-1c0 1 1 1 2 1h1c1 1 1 1 1 2l-1 1v1h0c0 1 1 2 1 2v1h-2c0 2 1 3 2 5l-4-3h1c0-1 0-1-1-1 1-1 1-1 1-2h-1c0-2-1-2-1-3v-1s1 0 1-1l-2-1h0z" class="D"></path><path d="M297 577h1c1 1 1 1 1 2l-1 1c-1-1-2-1-3-2 1-1 1-1 2-1z" class="G"></path><path d="M310 551c0 1 1 2 0 3v1 1h1v1c-1 1-1 1 0 2h1l1 1c-1 0-1 1-1 1l-1-1v1 1h-1c0-1-2-2-2-3l-1-1c-1-1-2-1-2-2s0-1 1-1v-1h2c0-1 1-1 1-2 0 0 0-1 1-1z" class="B"></path><path d="M305 556c0-1 0-1 1-1l2 2c1 2 1 2 2 3 1 0 1 1 1 1v1h-1c0-1-2-2-2-3l-1-1c-1-1-2-1-2-2z" class="L"></path><path d="M310 589l1-2c-1 0-1-1-2-1s-2 1-3-1h1 0 0 1 5 3v1 1c-1 0-2 0-3-1v2 1h1 0 6 1 0v1s-1 1-1 2h-3c-1-1-1-1-2-1l-1-1h-4v-1z" class="B"></path><path d="M328 557l1 1 1 2c0 2 1 4 0 6v1h1v1c1 1 1 1 1 2v4h0l-3-3h-1v-1h1v-1h-2v-2h1c-1-1-1-3-2-4-2 1-3 2-4 4 0-1-1-1-1-1 0-1 1-1 2-1h0v-1-1c1-1 1-1 1-2h0c1-1 1-2 2-2v1c1-1 1-2 2-3z" class="G"></path><path d="M328 557l1 1 1 2c0 2 1 4 0 6h-1c-1-1-2-2-2-3v-1h-1-1l-1-1c1-1 1-2 2-2v1c1-1 1-2 2-3z" class="U"></path><path d="M315 547v-1c-1-1-2-1-2-3 1-1 1-1 1-2l-1-1h1c1 0 1 0 1 1h1c0-1 1-1 2 0h0v2h1c1 0 1-1 1 0h1c1 2 1 6 1 7-1 0-1 0-1 1h0-1-1v1h-2c-1 0 0 0 0-2 0 0-1 0-1-1h1v-1l-2-1z" class="P"></path><path d="M315 547l2-2h1c0 2-1 2-1 3l-2-1z" class="R"></path><path d="M332 570l1-1h1c0 1 1 2 1 3 1 0 1-1 2-2 0 1 0 2 1 3l1 4c-1 1-1 1-2 1 0 1 0 1 1 1 0 0 0 1 1 1 0 1 0 1-1 1l-1 1v1h-2c-1-1 0-1 0-1 0-1-1-1-1-1v-2h-2l1-1-1-1c-1 0-1-1-1-2h1c-1-1-3-2-3-3-1-1-2-1-2-3h2v1h-1v1h1l3 3h0v-4z" class="T"></path><path d="M337 570c0 1 0 2 1 3l1 4c-1 1-1 1-2 1l-1-1-1 1h-1c-1 0-2-1-2-2v-1h2c1-1 1-2 1-3 1 0 1-1 2-2z" class="I"></path><path d="M315 525l1-1v1h3v2h-1 0l-1 1v1h-1c0 1 0 2 1 3l-1 1c-1 0-1-1-2-1v1 1h0c-1 1-1 1-1 2h-2v3l1 1-1 2-1-1-1-1v-3-2-6l1-1h2 0c-1-1-1-2-2-3h1l1 1v-1c1 1 1 1 1 2l2-2z" class="B"></path><path d="M315 525l1-1v1h3v2h-1 0l-1 1v1h-1c0 1 0 2 1 3l-1 1c-1 0-1-1-2-1v-1l1-1v-1c-1 0-2 0-2 1-1-1-1-1-1-2-1-1-1-2-2-3h1l1 1v-1c1 1 1 1 1 2l2-2z" class="V"></path><path d="M315 525l1-1v1 3h-3v-1l2-2z" class="B"></path><path d="M316 518v-2c0-1 2-1 2-1l1 1s0 1-1 2h0l2 2-2 1h-1c2 2 2 1 2 4h-3v-1l-1 1-2 2c0-1 0-1-1-2v1l-1-1h-1s0-1-1-2h-1l1-2v-2-2c1 1 2 1 3 1h1v-1c0-1 0-1 1-1h0v1l2 1z" class="H"></path><path d="M309 521c1 0 2 0 2 1s0 1-2 1h-1l1-2z" class="Z"></path><path d="M312 525v-2l1 1h2v1l-2 2c0-1 0-1-1-2z" class="I"></path><path d="M309 517c1 1 2 1 3 1h1v3c-1 1-1 1-2 1 0-1-1-1-2-1v-2-2z" class="E"></path><path d="M309 519c1 0 2 0 3 1v1h1c-1 1-1 1-2 1 0-1-1-1-2-1v-2z" class="C"></path><path d="M316 518v-2c0-1 2-1 2-1l1 1s0 1-1 2h0l2 2-2 1h-1c2 2 2 1 2 4h-3v-1l-1 1v-1c-1-1-1-2-1-3 1-1 0-1 0-1v-3l2 1z" class="X"></path><path d="M314 517l2 1 1 3h-1c0 1 1 2 2 3h0-1l-1 1v-1l-1 1v-1c-1-1-1-2-1-3 1-1 0-1 0-1v-3z" class="C"></path><path d="M295 586l4 3v1c1 0 2 0 3-1h2l1 1h3c1 0 1-1 2-1v1h4l1 1h-1c-1 1-2 0-4 0h0c-1 1-1 1-2 1v2l-1-1c-1 0-2 1-2 2h-2v1h-6l-1-1c0-1 0-1 1-1-1 0-1-1-2-1v-1h-1v-4h0l1-2z" class="E"></path><path d="M299 589v1 1c1 1 2 0 3 2h0c-1 1-1 0-2 0l-2-1h-2v-1h2v-2h1z" class="G"></path><path d="M298 592l2 1-1 1c2 1 3 0 4 1v1h-6l-1-1c0-1 0-1 1-1l1-2z" class="F"></path><path d="M295 586l4 3h-1v2h-2v1h2l-1 2c-1 0-1-1-2-1v-1h-1v-4h0l1-2z" class="C"></path><path d="M317 533l1 1h1v-2h1l1 2h2 1v1c1 1 1 0 2 1l1 1c0 1 0 2 1 2h-1c-1 2-1 4-1 5l1 1v1l-1 1v2h-1c0 1 0 1-1 2l-1-2c0-2 0-6-1-8h-1-1c-1-1-1-1-1-2s0-1-1-1l-2-2c0-1 0-2 1-4v1z" class="O"></path><path d="M321 534h2 1v1c0 2 0 3-1 4h0l-1-1c-1-1-1-1-1-3 1 0 0-1 0-1z" class="C"></path><path d="M322 541l1-1c1 0 1 0 1 1h0l1 1v1h-1v1c1 1 1 2 1 3v1l1-1v2h-1c0 1 0 1-1 2l-1-2c0-2 0-6-1-8z" class="B"></path><path d="M320 520h0l1 1h0c-1 1-1 1-1 2s1 1 1 2h1 0c1-1 1-1 1-2 1 1 2 1 3 1v1c1 0 1 0 2 1v1l1-1h0v3h1v4h-1v-1h0c-1 0-1 1-1 1l-1-1c-1 1-1 2-2 2v-1l-1 1h-1-2l-1-2h-1v2h-1l-1-1v-1h0c-1-1-1-2-1-3h1v-1l1-1h0 1v-2c0-3 0-2-2-4h1l2-1z" class="E"></path><path d="M317 529c0 1 1 1 1 2s-1 1-1 2v-1h0c-1-1-1-2-1-3h1z" class="B"></path><path d="M328 527l1-1h0v3l-2-1c0 1-1 1-1 1v-2h2zm-8-7h0l1 1h0c-1 1-1 1-1 2s1 1 1 2h1 0c1-1 1-1 1-2 1 1 2 1 3 1v1c-1 1-2 1-2 2s0 2 1 2c0 1 0 1-1 2v1c-1-1-2 0-2-1h-1-2v-4h0v-2c0-3 0-2-2-4h1l2-1z" class="H"></path><path d="M321 531l-1-3h1 1l1 1-1 2h-1z" class="Z"></path><path d="M341 561h2v2l1-1h1 1 1v-1l1 1v-1c1 1 3 2 3 3-1 1-2 3-2 4 0 0 0 1 1 1h-1c-1 2-1 3-2 5v1h1v-1l2 2h0-1-1-1-1l-1 1h0l1 2v2c-1 0-1 0-1 1v1l-1 2h0c1 0 2 0 2 1l1 1h-1v1h-1 0v4 3 1h-1 0l-1-1h-4c-2 1-3 1-5 1-1 0-2 0-2-1h-1v-1h-1v-1c1-1 1-1 1-2-2 0-2 0-3 1-1-1-2-1-2-1h-1v-1h-2l1-1v-1h-1c1 0 1-1 1-1v-1c1-1 1 0 2 0 1-1 2-1 4-1l-2 1 2 2h1c1 0 2 0 4-1l-1-1c2-1 1-1 1-2v-1h2v-1l1-1c1 0 1 0 1-1-1 0-1-1-1-1-1 0-1 0-1-1 1 0 1 0 2-1l-1-4c-1-1-1-2-1-3v-4l1-1v-1h1l2-1v-2z" class="D"></path><path d="M338 591h2v2c-1 0-1 0-2-1v-1z" class="S"></path><path d="M335 583h2v-1l1-1 2 2-1 1h0c-1 0-1 0-2 1-1-1-1-1-2-1h0v-1z" class="H"></path><path d="M330 593l1 1 1-1c1-1 0-1 1-1s2 1 2 1c0 1-1 1-2 1l-1 1h-1v-1h-1v-1z" class="P"></path><path d="M343 585h0c1 0 1 1 1 1h2l1 1h-1v1h-1c-2 0-2 1-3 2h-1v-1h-2 0l2-2 1-1v-1h1 0z" class="C"></path><path d="M346 588h-2v-1h0 2v1z" class="I"></path><path d="M326 591l1-1h0 3c1-1 1-1 1-2 2 0 4 2 6 1v1c-1 0-1 1-3 1h0-1 0-2c-2 0-2 0-3 1-1-1-2-1-2-1z" class="H"></path><path d="M340 593c1 0 4 1 5 1v1 1h-1 0l-1-1h-4c-2 1-3 1-5 1-1 0-2 0-2-1l1-1h2c1 0 4 0 5-1zm1-22h1c0 1 1 1 1 1 1 1 1 1 1 2v1c0 1-1 2-2 3v1h2 0c0 1-1 1-2 2l1 2h0v2h0-1c-1 0-1 0 0-1 0-1-1-2-1-3v-3-5-2z" class="F"></path><path d="M343 574l1 1c0 1-1 2-2 3 0-2 0-2 1-4z" class="E"></path><path d="M341 571h1c0 1 1 1 1 1 1 1 1 1 1 2v1l-1-1c-1 0-1-1-2-1v-2z" class="C"></path><path d="M347 572v2 1h1v-1l2 2h0-1-1-1-1l-1 1h0l1 2v2c-1 0-1 0-1 1v1l-1 2h0c1 0 2 0 2 1h-2s0-1-1-1h0v-2h0l-1-2c1-1 2-1 2-2h0-2v-1c1-1 2-2 2-3v-1c1-1 2-1 3-2z" class="N"></path><path d="M341 561h2v2l1-1h1 1 1v-1l1 1v-1c1 1 3 2 3 3-1 1-2 3-2 4 0 0 0 1 1 1h-1c-1 2-1 3-2 5v-2c-1 1-2 1-3 2 0-1 0-1-1-2 0 0-1 0-1-1h-1c-1-1-1-1-1-3v-1l-2-2v-1h1l2-1v-2z" class="G"></path><path d="M343 564h1v3h-1v-2-1z" class="S"></path><path d="M343 563l1-1h1l-1 2h-1v-1z" class="O"></path><path d="M343 565v2h1v1 2c0 1 0 2-1 2 0 0-1 0-1-1h-1c-1-1-1-1-1-3 1 0 2-1 2-1l1-2z" class="D"></path><path d="M342 567c1 1 1 2 0 4h-1c-1-1-1-1-1-3 1 0 2-1 2-1z" class="F"></path><path d="M341 561h2v2 1 1l-1 2s-1 1-2 1v-1l-2-2v-1h1l2-1v-2z" class="E"></path><path d="M348 561c1 1 3 2 3 3-1 1-2 3-2 4 0 0 0 1 1 1h-1c-1 2-1 3-2 5v-2c0-1 0-1-1-2h1v-3s0-1 1-1v-4-1z" class="O"></path><path d="M293 177h0c1 1 2 1 2 3h6 0 3v2h3-7l1 1h6v2h-5-1-1-1-1c1 0 1 1 1 1h9 2 2v1h0c0 1 1 1 1 2h-1v2h1c0 1 0 1 1 1-1 1-1 1-1 2l2 1c2-1 5 0 8-1h0 2v1 1l1-1h1c1 1 1 1 2 1l2-2 2 1h2c0 1 0 1 1 1 0 1 1 1 2 1h0c0 1 0 2 1 3v1c-1 1-1 1-1 2l1 1c1 2 1 3 1 5v1 2 4 1l1 1c0 1-1 2-1 3v1l-1 1h-1v1c1 1 1 2 0 3 0 1-1 1-2 2-1 0-2 0-3 1h-1 1l-1 1h1v1 1h0-2v3h0c-1 0-2 1-3 1v2c0 2 1 3 1 4-2 1-2 1-3 2l-1-1h0l-1 1h0c-1 1-2 1-2 2l-1-1c-1 1 0 2-1 3h-1v1h2 0v8h0v2l-11 10v1l-1 2 1 1 1 2c1 1 2 2 2 3v4 4 1 1h0c-1 2-1 3-1 5v2 3h0l-1 3c0 2 0 3-1 4l2-1v1l1 2-1 1h-1v1l-1 1v-1c-1 0-1-1-1-2-1 1-1 2-2 2h-1c-1-1-1-2-1-3 0 1-1 2-2 2v-2h0l-1 1v-3c-1 1-1 1-2 1h-2l1-1h1v-5-1-17h2v-12h-2l-1-1c-1-1 0-12 0-14-1 0 0-3-1-4h-1v-5-3-1h-1c1-1 1-1 1-2s1-2 1-2l-1-1v-2c1 1 1 0 2 0-1-1 0-1 0-2h-1v-2l1-4v-1c-1-2-1-4 0-6 0-1-1-2-1-2h0c-1-2-2-2-2-4v-1h2v-1c0-1 0-1-1-2s-1-1-1-2v-2c0-2-1-3-2-4v-2c-1 0-1-1-1-2h0l-2 1c-1-1-1-3-2-4l-1-1v-1c-2 0-1 2-2 2h-2c-1 0-1 0-1-1h0-1v1l-1 1v1h-2l-1-1v-1s1 0 1-1h-3v-1-2c-1 0-1-1-2-2h0c-1-2-1-4-2-5 1-1 1-2 1-3h1 2 2v-1l3-1h3v-1c1 1 1 1 2 1l1-1h2l-1 1h0c1 1 2 1 3 1v-1c-1-1 0-2 0-2h2z" class="N"></path><path d="M299 227h0c1 1 1 2 1 4h-2l1-4z" class="o"></path><path d="M322 200h1v1l1 1-1 1h-2v-3h1z" class="F"></path><path d="M310 209v-2h1c1 0 2 0 2 2v1h0c-1 1-1 1-2 0 0 0 0-1-1-1h0z" class="P"></path><path d="M316 200c1-1 2-1 2-1l1 1v1c0 1-1 1-1 2-1 0-2-1-3-1 0-2 0-2 1-2z" class="I"></path><path d="M296 196c1 0 1 0 1 1l1-1h0 0v1c1 1 1 0 2 0 1 1 1 2 1 3h-1-1v1h-1c0-2-1-4-2-5z" class="O"></path><path d="M302 212v-3h-1c1-2 1-3 1-4v-1h2v6l1 1c-1 1-2 1-3 2v-1h0z" class="c"></path><path d="M302 212v-2h2l1 1c-1 1-2 1-3 2v-1h0z" class="d"></path><path d="M293 198h-1c0-2-1-3-2-4l1-1 1 1c1 1 1 3 2 4v2c2 2 2 3 3 6h-1c0-2-1-3-2-4v-2c-1 0-1-1-1-2h0z" class="c"></path><path d="M299 215v-1l1 1v7c0 1 1 1 1 2l-2 2h0c-1-2-1-4 0-6 0-1-1-2-1-2 1-1 0-2 1-3z" class="i"></path><path d="M288 189v-1c-1 0-1-1-2-1l1-1h1c1 1 2 1 3 1h1s1 1 2 1h0s1 0 1 1 1 1 1 2h0l-1-1c-1-1-2-1-3-2l-1 1 2 2h0c-1 0-2 0-2-1h-1v1l1 1h-1-1c0-1-1-2-1-2v-1z" class="J"></path><path d="M296 208l1-1s1-1 1-2h1c1 1 0 2 0 2v1c1 2 1 4 0 5l-1 1 1 1c-1 1 0 2-1 3h0c-1-2-2-2-2-4v-1h2v-1c0-1 0-1-1-2s-1-1-1-2z" class="f"></path><path d="M311 200v-2c0-1-1-2-1-3l1-1c1 1 2 1 4 1 2-1 5 0 8-1l1 1-1 1v1 1h-3c-1 0-1-2-2-1v1h-1l-1-1c-1-1-1-1-2-1v2c0 1-2 2-3 2h0z" class="L"></path><path d="M311 200h-1 0l-1-1v-2-1h0c-1 0-1 0-1 1h-1c0-1-1-1-1-3v-2h0c-1-2-1-1-2-2v-2h1c2 0 3 1 5 1 1-1 1-1 1-2h1c0 1 1 1 1 2h-1v2h1c0 1 0 1 1 1-1 1-1 1-1 2l2 1c-2 0-3 0-4-1l-1 1c0 1 1 2 1 3v2zm5 11v-2c0-1-1-1-1-2h2v2c1-1 2-1 3-1h0l1-1c-1-1-1-2 0-3l1-1 2 1v1l1 1h1v-2h2 0v3h0v1 1c1 1 1 1 2 1h0c0 1 0 2-1 2-1 1 0 2 0 3v1l-1 1c0-1-1-1-1-1h-1-1v1l-1 1-1-1h-1v-1h-1-3v1h0v-2h-1 0c-1 0-2 1-2 1h-1 0l-2-1h0l-1-1c0-1 0-2-1-3s-1-2-1-3h1v1h0c1 0 1 1 1 1 1 1 1 1 2 0h0 1l1 1v-1l1 1z" class="P"></path><path d="M321 213h1c0-2 0-2 1-3 1 1 0 4 0 6v1h-1v-1h-1v-3z" class="K"></path><path d="M328 209c1 1 1 1 2 1h0c0 1 0 2-1 2-1 1 0 2 0 3v1l-1 1c0-1-1-1-1-1h-1-1v-3h1l1-1c1-1 1-1 1-2v-1z" class="G"></path><path d="M309 208h1v1h0c1 0 1 1 1 1 1 1 1 1 2 0h0 1l1 1v-1l1 1v2c1 0 1-1 2-1l-1-2h1 3v3 3h-3v1h0v-2h-1 0c-1 0-2 1-2 1h-1 0l-2-1h0l-1-1c0-1 0-2-1-3s-1-2-1-3z" class="B"></path><path d="M314 216v-1c1-1 1-2 2-2l1 1 1 1h-1 0c-1 0-2 1-2 1h-1 0z" class="M"></path><path d="M333 195h2c0 1 0 1 1 1 0 1 1 1 2 1h0c0 1 0 2 1 3v1c-1 1-1 1-1 2l1 1-1 3h0c-1 0-2 0-3-1v3c-2 3-2 4-5 7h0-1v-1c0-1-1-2 0-3 1 0 1-1 1-2h0c-1 0-1 0-2-1v-1-1h0v-1c1-1 1-1 2-1v-1h-2v-1h-2-1c0-1 0-2 1-3l-1-1h0c0-1 1-2 1-3 1 0 1 0 2 1l1-1c1 1 2 1 3 0 1 0 1-1 1-1h0z" class="F"></path><path d="M331 208h1c1 1 0 1 0 2h-1v-2h0z" class="Z"></path><path d="M335 205c-1 0-1 0-1 1h-1c-1-1-1-1-1-2l2-2c1 0 1 1 2 1l-1 1v1z" class="C"></path><path d="M338 197h0c0 1 0 2 1 3v1c-1 1-1 1-1 2l1 1-1 3h0c-1 0-2 0-3-1v-1-1l1-1v-1-1l2-4z" class="D"></path><path d="M325 199h1c1 0 2 0 3-1h1 6v1h0c-1 0-1 0-1 1-1 0-2 0-2-1l-1 1v1 1s-1 0-1 1h0v2l1 2-1 1h0v2h-1c-1 0-1 0-2-1v-1-1h0v-1c1-1 1-1 2-1v-1h-2v-1h-2-1c0-1 0-2 1-3l-1-1z" class="G"></path><path d="M328 208h3v2h-1c-1 0-1 0-2-1v-1z" class="Q"></path><path d="M339 204c1 2 1 3 1 5v1 2 4 1l1 1c0 1-1 2-1 3v1l-1 1h-1v1c1 1 1 2 0 3 0 1-1 1-2 2-1 0-2 0-3 1h-1l-2-2v-1-2-1l1-1h-1v-1c1-1 1-1 1-2v-1l-1-1-2 1v-2h0l1-1h1 0c3-3 3-4 5-7v-3c1 1 2 1 3 1h0l1-3z" class="B"></path><path d="M334 218l1-1v1c0 1 0 2 1 3v1h-1s-1 0-1-1v-3z" class="U"></path><path d="M339 204c1 2 1 3 1 5v1 2c-1 1-2 1-2 2v-6-1l1-3z" class="X"></path><path d="M334 218v3c-1 1-1 2-1 4h-2c0 1 0 2-1 2v-2-1l1-1h-1v-1c1-1 1-1 1-2h2l1-2h0z" class="N"></path><path d="M330 227c1 0 1-1 1-2h2s1 0 1 1h2v3c-1 0-2 0-3 1h-1l-2-2v-1z" class="P"></path><path d="M338 214c0-1 1-1 2-2v4 1l1 1c0 1-1 2-1 3v1l-1 1h-1c-1-2 0-7 0-9z" class="a"></path><path d="M340 216v1l1 1c0 1-1 2-1 3l-1-2 1-3z" class="f"></path><path d="M293 177h0c1 1 2 1 2 3h6 0 3v2h3-7l1 1h6v2h-5-1-1-1-1c1 0 1 1 1 1h-1v2l1 1h0l-2-1s0 1-1 0h-1v1c0-1-1-1-1-1h0c-1 0-2-1-2-1h-1c-1 0-2 0-3-1h-1l-1 1c1 0 1 1 2 1v1 1s1 1 1 2h0 0c-2 0-2 0-3-1h-3-1c-1-1 0-1-1-2-1 0-1 1-2 1h-1c0 1 0 1 1 1l-1 1c-1 0-1 0-2-1-1 0-1-1-2-2h0c-1-2-1-4-2-5 1-1 1-2 1-3h1 2 2v-1l3-1h3v-1c1 1 1 1 2 1l1-1h2l-1 1h0c1 1 2 1 3 1v-1c-1-1 0-2 0-2h2z" class="E"></path><path d="M276 185h5v1h0c-2 0-2 1-3 1h-1s-1-1-1-2z" class="P"></path><path d="M274 181h2l1 1c-1 1-1 1-2 1v1h-1-2c1-1 1-2 1-3h1z" class="G"></path><path d="M274 181h2l1 1c-1 1-1 1-2 1h0c0-1-1-1-1-2z" class="O"></path><path d="M278 180l3-1c1 1 2 2 2 4l-1 1h-3c0-1 0-1-1-2h-1l-1-1h2v-1z" class="H"></path><path d="M286 179l1-1h2l-1 1h0c1 1 2 1 3 1v1h-5-1-1v1l-1 1c0-2-1-3-2-4h3v-1c1 1 1 1 2 1z" class="J"></path><path d="M284 185c0-1 0-1 1-2s2 0 4 0l-1 1c2 0 3-1 5 1h-1 1l2 1v1l-1 1h0c-1 0-2-1-2-1h-1c-1 0-2 0-3-1h-1l-1 1c1 0 1 1 2 1v1c0-1-1-1-1-1-1 0-2-1-3-2h0v-1z" class="K"></path><path d="M284 185c0-1 0-1 1-2s2 0 4 0l-1 1c2 0 3-1 5 1h-1 0-8z" class="V"></path><path d="M274 189c1-1 1-1 1-2s-1-1-1-2h1 1c0 1 1 2 1 2h1c1 0 1-1 3-1v1c1 1 2 1 3 1 1 1 1 2 2 3h-3-1c-1-1 0-1-1-2-1 0-1 1-2 1h-1c0 1 0 1 1 1l-1 1c-1 0-1 0-2-1-1 0-1-1-2-2h0z" class="C"></path><path d="M293 177h0c1 1 2 1 2 3h6 0 3v2h3-7-4l-1-1c-1 0-1 1-1 1 0 1 0 1 1 2h-1s0 1-1 1h-1 1c-2-2-3-1-5-1l1-1v-1c1 1 2 1 3 1l2-2-1-1-1 1h-1v-1-1c-1-1 0-2 0-2h2z" class="H"></path><path d="M291 177h2v1c1 1 1 1 1 2h-1l-1 1h-1v-1-1c-1-1 0-2 0-2z" class="S"></path><path d="M293 185c1 0 1-1 1-1h1c-1-1-1-1-1-2 0 0 0-1 1-1l1 1h4l1 1h6v2h-5-1-1-1-1c1 0 1 1 1 1h-1v2l1 1h0l-2-1s0 1-1 0h-1v1c0-1-1-1-1-1l1-1v-1l-2-1z" class="C"></path><path d="M304 204h-1v-1c2 0 2 0 3 1 1-1 0-2 0-4h2l1 5v3c0 1 0 2 1 3s1 2 1 3l1 1h0l2 1h0 1s1-1 2-1h0 1v2h0v-1h3 1v1h1l1 1 1-1v-1h1 1s1 0 1 1h0v2l2-1 1 1v1c0 1 0 1-1 2v1h1l-1 1v1 2 1l2 2h1l-1 1h1v1 1h0-2v3h0c-1 0-2 1-3 1v2c0 2 1 3 1 4-2 1-2 1-3 2l-1-1h0l-1 1h0c-1 1-2 1-2 2l-1-1c-1 1 0 2-1 3h-1v-3c-2 0-3 1-4 1v-2-4c0-1-1-1-1-1-1-1-1-2-2-3 0 1 0 2-1 2 0-1-1-2-1-3l-1 1v1h-1-1v-1-1l1-2c-1-1-1-2-1-3l-1-1s1 0 1-1v-3h-1 0c-1 0 0 0 0-1v-1c1 0 1-1 1-2h-1v-3c1 0 1 0 0-1 0-1 0 0-1 0s-1 1-1 2c0-1-1-2-1-3l-1 1c0-2-1-4 0-6v1c1-1 2-1 3-2l-1-1v-6z" class="P"></path><path d="M310 236v-1c1-1 2 0 2-1l1 1c-1 1 0 1 0 2h-1c0 1 0 2-1 2 0-1-1-2-1-3zm5 9l2-1v1c2-1 1-1 2-2v-2h1v3l1 1 1-1 1 1h1 0c-1 1-2 1-2 2l-1-1c-1 1 0 2-1 3h-1v-3c-2 0-3 1-4 1v-2z" class="X"></path><path d="M310 226v-1c1 0 1 1 1 2h1c1 0 1 0 1-1l2-3 1 1c0 3-2 3-2 5l2-1v2 1 2c0 1 0 2-1 2h0-1v-1l1-2c-1 0-1 0-2-1 0 1-1 1-1 2v1h0c-1-1-2-1-2-1v-7z" class="B"></path><path d="M304 204c0 2 1 3 1 4h1c1 1 1 1 1 2 0 3 1 4 0 7v1l1 1h0c-1 1-1 2-1 3h-1v-3c1 0 1 0 0-1 0-1 0 0-1 0s-1 1-1 2c0-1-1-2-1-3l-1 1c0-2-1-4 0-6v1c1-1 2-1 3-2l-1-1v-6z" class="D"></path><path d="M302 212v1c1 0 1 1 1 1 1 1 1 1 1 2h1 1 0v-2h1v3 1l1 1h0c-1 1-1 2-1 3h-1v-3c1 0 1 0 0-1 0-1 0 0-1 0s-1 1-1 2c0-1-1-2-1-3l-1 1c0-2-1-4 0-6z" class="V"></path><path d="M316 224l1-1h-1v-1h1 0l1-1v1l1 1v3l2 1v1c-1 0-1 0-2-1v5s1 0 0 1v3h0v2c0-1-1-1-1-1v-1h-2v4h-1v-1-1c0-1 0-1-1-1v-2h1 0c1 0 1-1 1-2v-2-1-2l-2 1c0-2 2-2 2-5z" class="D"></path><path d="M318 222l1 1v3l2 1v1c-1 0-1 0-2-1-1 0-1-1-2-1 0-2 1-3 1-4z" class="E"></path><defs><linearGradient id="AQ" x1="317.037" y1="238.083" x2="325.942" y2="231.811" xlink:href="#B"><stop offset="0" stop-color="#0e0f0f"></stop><stop offset="1" stop-color="#2e2c2d"></stop></linearGradient></defs><path fill="url(#AQ)" d="M321 226l1 1 1-1c0 1 0 1 1 2l1 1s0 1-1 2h1l-1 2v1c0 1 1 2 1 3v4l-1 1 1 2-1 1h-1l-1-1-1 1-1-1v-3h0c0-2 0-2-1-3v-2h0v-3c1-1 0-1 0-1v-5c1 1 1 1 2 1v-1-1z"></path><path d="M324 228l1 1s0 1-1 2h1l-1 2v1c0 1 1 2 1 3v4l-1 1 1 2-1 1h-1l-1-1c1-1 1-1 1-2l-1-1 1-1-1-2 1-1c-1 0-1-1-2-1l1-1c1-1 1-1 0-2v-3c1 0 1-1 2-2h0z" class="I"></path><defs><linearGradient id="AR" x1="310.858" y1="226.643" x2="316.356" y2="216.44" xlink:href="#B"><stop offset="0" stop-color="#232223"></stop><stop offset="1" stop-color="#3d3c3c"></stop></linearGradient></defs><path fill="url(#AR)" d="M311 214l1 1h0l2 1h0 1s1-1 2-1h0 1v2h0c1 0 2 1 3 2 0 0 1 1 1 2h0 0c-1 1-1 1-2 1h-1v1l-1-1v-1l-1 1h0-1v1h1l-1 1-1-1-2 3c0 1 0 1-1 1h-1c0-1 0-2-1-2v1h-1c-1-2 1-2 1-4v-1-1h-1v-3c0-1-1-2 0-2h1v2h1v-3z"></path><path d="M315 216s1-1 2-1h0v4h-1l-1-3z" class="X"></path><path d="M312 215l2 1h0 1l1 3h-3-1v-1-3z" class="C"></path><path d="M312 215l2 1h0v1l-2 1v-3z" class="D"></path><path d="M325 216h1 1s1 0 1 1h0v2l2-1 1 1v1c0 1 0 1-1 2v1h1l-1 1h-2c0 1 0 2-1 2-1 1-1 3-2 3l-1-1c-1-1-1-1-1-2l-1 1-1-1v1l-2-1v-3-1h1c1 0 1 0 2-1h0 0c0-1-1-2-1-2-1-1-2-2-3-2v-1h3 1v1h1l1 1 1-1v-1z" class="G"></path><path d="M327 216s1 0 1 1h0v2h-1v-3z" class="Q"></path><path d="M325 216h1 1v3h-2v-2-1z" class="I"></path><path d="M326 225l1-1v-2h0c0-1 0-1 1-2l1 1c0 1 0 2-1 3 0 1 0 2-1 2 0-1 0-1-1-1z" class="D"></path><path d="M324 225h1 1c1 0 1 0 1 1-1 1-1 3-2 3l-1-1c-1-1-1-1-1-2l1-1z" class="P"></path><path d="M322 224l2-1v-1h1v1l-1 2-1 1-1 1-1-1-1-1h2v-1z" class="M"></path><path d="M330 218l1 1v1c0 1 0 1-1 2v1h1l-1 1h-2c1-1 1-2 1-3s0-2 1-3z" class="J"></path><path d="M318 217v-1h3 1v1c0 1 1 2 2 3 0 1-1 2-1 2l-1-1h0c0-1-1-2-1-2-1-1-2-2-3-2z" class="P"></path><path d="M322 221l1 1-1 2v1h-2l1 1v1l-2-1v-3-1h1c1 0 1 0 2-1h0z" class="D"></path><path d="M328 224h2v1 2 1l2 2h1l-1 1h1v1 1h0-2v3h0c-1 0-2 1-3 1v2c0 2 1 3 1 4-2 1-2 1-3 2l-1-1h0l-1-2 1-1v-4c0-1-1-2-1-3v-1l1-2h-1c1-1 1-2 1-2 1 0 1-2 2-3 1 0 1-1 1-2z" class="E"></path><path d="M326 234h1c0 2-1 2-1 4l-1-1c0-1-1-2-1-3v1h1l1-1z" class="K"></path><path d="M325 231c0 1 0 1 1 2v1l-1 1h-1v-1-1l1-2z" class="T"></path><path d="M332 231h1v1 1h0-2v3-1c-1-1-1-1-2-1h-1v-1l1-1h0 1 1l1-1z" class="B"></path><path d="M325 237l1 1h1v1c-1 2 0 3-1 5h-1 0l-1-2 1-1v-4z" class="g"></path><path d="M328 224h2v1 2 1l2 2h1l-1 1-1 1h-1v-2c-1-1-1 0-2 1h-1v1l-1 1c-1-1-1-1-1-2h-1c1-1 1-2 1-2 1 0 1-2 2-3 1 0 1-1 1-2z" class="Z"></path><path d="M325 229c1 0 1-2 2-3 0 1 1 3 1 3-1 1-1 1-1 2v1l-1 1c-1-1-1-1-1-2h-1c1-1 1-2 1-2z" class="K"></path><path d="M302 218l1-1c0 1 1 2 1 3 0-1 0-2 1-2s1-1 1 0c1 1 1 1 0 1v3h1c0 1 0 2-1 2v1c0 1-1 1 0 1h0 1v3c0 1-1 1-1 1l1 1c0 1 0 2 1 3l-1 2v1 1h1 1v-1l1-1c0 1 1 2 1 3 1 0 1-1 1-2 1 1 1 2 2 3 0 0 1 0 1 1v4 2c1 0 2-1 4-1v3 1h2 0v8h0v2l-11 10v1l-1 2 1 1 1 2c1 1 2 2 2 3v4 4 1 1h0c-1 2-1 3-1 5v2 3h0l-1 3c0 2 0 3-1 4l2-1v1l1 2-1 1h-1v1l-1 1v-1c-1 0-1-1-1-2-1 1-1 2-2 2h-1c-1-1-1-2-1-3 0 1-1 2-2 2v-2h0l-1 1v-3c-1 1-1 1-2 1h-2l1-1h1v-5-1-17h2v-12h-2l-1-1c-1-1 0-12 0-14-1 0 0-3-1-4h-1v-5-3-1h-1c1-1 1-1 1-2s1-2 1-2l-1-1v-2c1 1 1 0 2 0v1l-1 1h1v2c-1 1 0 3 0 4h0c1-1 1-3 1-5v-1l1-1c2-1 1-5 1-7 1-1 0-1 0-1v-6-4z" class="b"></path><path d="M309 293h1 0v2c1 2 0 5 1 7 0 2 0 3-1 4l2-1v1l1 2-1 1h-1v1l-1 1v-1c-1 0-1-1-1-2l1-1c-1-1-1-4-1-5v-1-1-1c1-2 0-3 0-6z" class="J"></path><path d="M310 310h0c0-1 1-2 0-3v-1l2-1v1l1 2-1 1h-1v1l-1 1v-1z" class="G"></path><path d="M311 276c1 1 2 2 2 3v4 4 1 1h0c-1 2-1 3-1 5v2 3h0l-1 3c-1-2 0-5-1-7v-2h0-1v-1h0c0-1 0-3 1-4h0l-1-1c0-1 0-2 1-3 0-1-1-1 0-2v-1c1 0 2-1 2-1v-1l-1-1h-1l1-2z" class="D"></path><path d="M314 257c1 0 1 0 2 1v1l1-1 2-1 2 1v2l-11 10v1h0c0-2-1-3 0-4v-2s1-1 0-2v-4l1-1c1 1 0 5 1 7h1v-1c-1-1-1-3-1-4 1-1 2-2 2-3z" class="P"></path><path d="M300 282h2c0 4-1 9 0 13h1c1-1 0-1 0-2l1-7h0c1-1 0-3 1-3v-1h0c2 1 1 15 1 18v6 4c-1-1-1-2-1-3 0 1-1 2-2 2v-2h0l-1 1v-3c-1 1-1 1-2 1h-2l1-1h1v-5-1-17z" class="H"></path><path d="M302 305v-2-1-2h0c0-2 0-2 1-3 1 1 0 1 0 3l1 1 1-1h0 1v6 4c-1-1-1-2-1-3 0 1-1 2-2 2v-2h0l-1 1v-3z" class="E"></path><path d="M312 237c1 1 1 2 2 3 0 0 1 0 1 1v4 2c1 0 2-1 4-1v3 1h2 0v8h0l-2-1-2 1-1 1v-1c-1-1-1-1-2-1v-1h0-2 0v-7c-1 0-1 0-2-1v-5c1-1 0-2 1-3v-1c1 0 1-1 1-2z" class="U"></path><path d="M315 247c1 0 2-1 4-1v3 1h2 0v8h0l-2-1-2 1-1 1v-1-7c-1-1 0-1 0-2l-1-1v-1z" class="N"></path><path d="M319 250h2 0v8h0l-2-1c0-1 0-1 1-2 0-1-1-1-1-2v-1-2z" class="X"></path><path d="M302 218l1-1c0 1 1 2 1 3 0-1 0-2 1-2s1-1 1 0c1 1 1 1 0 1v3h1c0 1 0 2-1 2v1c0 1-1 1 0 1h0 1v3c0 1-1 1-1 1l1 1c0 1 0 2 1 3l-1 2v1 1 2 1 1c0 1-1 3 0 4h-1c0-1 0-1-1-1h-1c-1 1-1 1-1 2h2l-2 2v2 1 5l1 29-1 7c0 1 1 1 0 2h-1c-1-4 0-9 0-13v-12h-2l-1-1c-1-1 0-12 0-14-1 0 0-3-1-4h-1v-5-3-1h-1c1-1 1-1 1-2s1-2 1-2l-1-1v-2c1 1 1 0 2 0v1l-1 1h1v2c-1 1 0 3 0 4h0c1-1 1-3 1-5v-1l1-1c2-1 1-5 1-7 1-1 0-1 0-1v-6-4z" class="h"></path><path d="M303 241v-4h2v2l-2 2z" class="K"></path><path d="M305 237l1-1c0 1 0 1 1 1h0v1 2c-1 0-1 0-1-1h0-1v-2z" class="O"></path><path d="M305 239h1 0c0 1 0 1 1 1v1 1h-1v1h-2-1v-2l2-2z" class="g"></path><path d="M303 229h2v-1l2-2v3c0 1-1 1-1 1l1 1c0 1 0 2 1 3l-1 2-1-1c-1 0-1 0-2 1h0l-1-1c1-2 0-4 0-6z" class="H"></path><path d="M304 220c0-1 0-2 1-2s1-1 1 0c1 1 1 1 0 1v3h1c0 1 0 2-1 2v1c0 1-1 1 0 1h0 1l-2 2v1h-2c0-1 0-2 1-2v-2c-1-1-1-2-1-3 1-1 0-1 0-2h1z" class="K"></path><path d="M299 255v-1h1v1h2v8c0 1 0 2-1 3 0 1 0 2 1 2v2h-2l-1-1c-1-1 0-12 0-14z" class="T"></path><path d="M299 235v1l-1 1h1v2c-1 1 0 3 0 4h0c1-1 1-3 1-5v-1l1-1c1 2 1 4 1 6v13h-2v-1h-1v1c-1 0 0-3-1-4h-1v-5-3-1h-1c1-1 1-1 1-2s1-2 1-2l-1-1v-2c1 1 1 0 2 0zm22 163c0-1 0-1-1-2v-1l1-1c1 0 1 0 2-1h0c1 0 1 0 2 1h0l-2 1v2c1 0 1 0 2-1l1 1 1-1c0 1 0 1 1 2h0v2h2v1 4h0l-2 1h0v1 6 2 3 1h0c0 2 0 3-1 4l-1 1h1s0 1 1 1v2c-1 0-1 0 0 1h0v5 4 4 6 1 1 1l1 1h-1l1 1h0v2h-2v1 4c0 1 1 2 1 2v1l-1 1v1h0v4h0v2 2 5l1 1-1 1-1 2c1 1 1 2 1 4h0c1 0 1 0 2 1l1-1h2v1h1c1 1 0 1 0 2v1h-2v1c0 1 0 1-1 1l-2 1v-1h-1v1l-1 2h-3l-1 1 1 1c1 1 1 2 1 3l-1 1v-2l-1-1-1 1v-1 3c-1 0-2 0-2 1s-1 2 0 4v6c-1 2 0 2-1 3v1s-2 0-2 1v2l-2-1v-1h0c-1 0-1 0-1 1v1h-1c-1 0-2 0-3-1h0v-3h-1c-1-1-2-1-3-2v-1c0-2 0-3-2-5h1l-1-1c-1-2-2-2-3-2h-1c-1-2-1-4-1-6 0-1 1-1 1-2-1-2-1-8-1-11v-4-2h0v-1l-1 1c-1-1-2-2-3-2h0 0c-1 0-1-1-1-2s-1-1-1-2v-8-6l-2 1c0-3 1-5 0-8v-2c1-2 0-6 0-9v-10-3h1 1l1 1v-1h1c0 1 0 1 1 2 1-1 0-1 1-2h-1v-1h4c0-1 0-1-1-2 1 0 1 0 2-1v-5-2-1c1-1 0-3 0-5v-3-2-1-4-3h1v-1l2 1c0 1 0 1-1 2h1c1-1 1-1 1-2h1v1h0v-1l1-1 1 2 1-1c0 1 0 1 1 2l1 1c1-1 1-2 2-3 3 1 2 0 4-1l1 1h1c-1 2 0 2 0 3l2-1 1-1z" class="U"></path><path d="M326 452h0c1-1 2 0 3 0v2h-2v1h-1v-3z" class="K"></path><path d="M290 451c1 0 1 0 2 1v1c-1 1 0 4 0 5l-2 1c0-3 1-5 0-8z" class="M"></path><path d="M316 448h2v4h0-1-1v-3h0-1c0-1 1-1 1-1z" class="I"></path><path d="M298 437v6h-2v-1c-1-2 0-4 0-5h2z" class="N"></path><path d="M302 459l1 1h1s0 1-1 1v1c1 0 1 1 1 2v1c0 1 0 2-1 3-1 0-1 0-3-1l1-1c1-1 1-2 1-4 0 0-1-1-1-2l1-1z" class="C"></path><path d="M309 452l1 1v-1l1 1h0c0 1-1 2-1 3 1 0 2-1 2-1h1v1c-1 1-2 1-2 2l1 1-1 1v2h-1c-1-1-1-1-1-2 1 0 1 0 1-1s-1-2-1-3v-4z" class="N"></path><path d="M299 426h1c0 1 0 2-1 3h-1l1 1h1v1c-1 0-1 0-1 1l-1 1h-1v1h0-1c0-2 0-2 1-3-1-1-1-1 0-2v-1l-1-1h-1v-1h4z" class="c"></path><path d="M299 463v-1h1c1 0 0-3 0-5l1 1 1 1-1 1c0 1 1 2 1 2 0 2 0 3-1 4l-1 1v2h-1 0v-6z" class="O"></path><path d="M298 451l2 1v3l1 1c1 0 2 1 3 1v2 1h-1l-1-1-1-1-1-1c0 2 1 5 0 5h-1v1c-1-2 0-5-1-7v-5z" class="V"></path><path d="M300 448l1 1c0 2 1 3 2 5v1h1 0 1v1h0c-1 1-1 2-1 3v-2c-1 0-2-1-3-1l-1-1v-3l-2-1 1-1h0l1-2zm-6 28l2-2c0-1-1-1-1-1v-1-1l1 1h2l2-1c0 2-1 4-1 6l-1 1v-1l-1 1c-1-1-2-2-3-2h0z" class="N"></path><path d="M296 443h2c0 1 0 1 1 2 0 1-1 2-1 3s0 1 1 2h0l-1 1v5-5-1-2c-1 1-1 2-1 3h0v2c0 1 0 1-1 1h-1v-8-1l1-1h0v-1z" class="T"></path><path d="M296 444c1 2 1 4 1 7h0-1l-1-5v-1l1-1z" class="B"></path><path d="M290 430h3v1h-1c0 1 0 1 1 1l-1 1v3h1v1c-1 0-1 1-2 1l1 1c0 1-1 1-1 1 0 1 1 1 1 2v1l-1-1v1c1 1 1 5 1 7h-1s-1 0-1-1c1-2 0-6 0-9v-10z" class="J"></path><path d="M309 442c1 1 3 1 3 3h1v3 3l-1 1h-2v1l-1-1c0-1 1-2 1-3h-1v-3-4z" class="C"></path><path d="M309 449c1 0 2-1 2-1h1 0v2s0 1 1 1l-1 1h-2v1l-1-1c0-1 1-2 1-3h-1z" class="H"></path><path d="M301 471c0-1 0-1 1-2l1 1v1h0c2 1 1 1 3 1v-1h0 1 0c-1 1-1 1-1 2 0 0 1 1 1 2-1 1-1 1-1 2v-2l-1 1v2h-2c-1 0-1-1-2-2 1 0 1-1 1-1 0-1-1-1-1-2v-2zm16-26h0c2-1 1-3 2-4v1h2c0 1 1 2 1 3 0 0 0 1-1 2l2 1v1h-2 0l-1 1c-1 1-2 1-2 2v-4h-2l1-3z" class="C"></path><path d="M319 442h2c0 1 1 2 1 3h-2v-1c-1-1-1-1-1-2z" class="B"></path><path d="M318 448v-1l1-1 1 1h0c-1 1 0 2 0 3-1 1-2 1-2 2v-4z" class="P"></path><path d="M300 426h1v3h1s1 1 1 2h0l-1 1h-1v1 2h0v10 1h1c0 1 0 1 1 1v2l-1-1c-1 0-1 0-1 1l-1-1-1 2h0 0c-1-1-1-1-1-2s1-2 1-3c-1-1-1-1-1-2v-6-4l1-1c0-1 0-1 1-1v-1h-1l-1-1h1c1-1 1-2 1-3z" class="B"></path><path d="M299 432h1v7 4c-1 1-1 2-1 3h0c1 0 1 1 1 2l-1 2h0 0c-1-1-1-1-1-2s1-2 1-3c-1-1-1-1-1-2v-6-4l1-1z" class="d"></path><path d="M300 471h1v2c0 1 1 1 1 2 0 0 0 1-1 1 1 1 1 2 2 2h2v-2l1-1v2s1 1 0 1c0 1 1 3 1 4v4h0c-1 2 0 5 0 7h-1c-1 0-1-1-1-2h-1l1 2c-1 1-1 2-2 2l-1-1s-1-1-1-2h1c0-1 0-2-1-3l-1 1v-3-8l-2-1h0l1-1c0-2 1-4 1-6z" class="D"></path><path d="M305 478v-2l1-1v2s1 1 0 1c0 1 1 3 1 4v4c-1 0-1-1-1-1v-1h-1 0 0v-6z" class="a"></path><path d="M302 492l1 1v-2-2h1v1h1v-3h-1v1h0c-1-1-1-2-1-3v-1l1 1 1-1h1v1s0 1 1 1h0c-1 2 0 5 0 7h-1c-1 0-1-1-1-2h-1l1 2c-1 1-1 2-2 2l-1-1s-1-1-1-2h1z" class="d"></path><path d="M298 478l2 1v8 3l1-1c1 1 1 2 1 3h-1c0 1 1 2 1 2l1 1c1 0 1-1 2-2h0c1 1 1 1 2 1 1 1 0 3 0 4v1s0 1 1 2c-1 0-1 1-1 1 0 2 0 3 1 5-1 1-1 2-1 2-1 1-1 2-2 2 0-2 0-3-2-5h1l-1-1c-1-2-2-2-3-2h-1c-1-2-1-4-1-6 0-1 1-1 1-2-1-2-1-8-1-11v-4-2z" class="J"></path><path d="M304 506h2v1h0v2h1c-1 1-1 2-2 2 0-2 0-3-2-5h1z" class="E"></path><path d="M303 497c0 1 0 1 1 1h0v-2c0 2 1 3 0 5h0l-2 1v-2c0-1-1 0-2 0v-1-2c1 0 1-1 2 0h1z" class="G"></path><path d="M300 497c1 0 1-1 2 0h1l-2 2h-1v-2z" class="K"></path><path d="M300 490l1-1c1 1 1 2 1 3h-1c0 1 1 2 1 2l1 1h1v1 2h0c-1 0-1 0-1-1h-1c-1-1-1 0-2 0-1-2 0-6 0-7z" class="B"></path><path d="M305 493h0c1 1 1 1 2 1 1 1 0 3 0 4v1s0 1 1 2c-1 0-1 1-1 1l-1 2h-1l-1-3h0c1-2 0-3 0-5v-1h-1c1 0 1-1 2-2z" class="I"></path><path d="M304 423h0l1 1c1 1 1 4 1 6h0v1 2c-1 3 0 6 0 8s0 4 1 6v2c-1 1 0 3 0 4v2c0 1-1 1-1 2l-1-1v-1h-1 0-1v-1c-1-2-2-3-2-5 0-1 0-1 1-1l1 1v-2c-1 0-1 0-1-1h-1v-1-10h0v-2-1h1l1-1h0c0-1-1-2-1-2h-1v-3h-1-1c0-1 0-1-1-2 1 0 1 0 2-1v2h1v-1l2 1v-1l1-1z" class="G"></path><path d="M301 435h0v-2-1h1l2 2h0v2 2l-1 1c-1-1 0-1-1-2 0 0-1-1-1-2z" class="J"></path><path d="M301 435c0 1 1 2 1 2 1 1 0 1 1 2l1-1v6 7c0 1 0 2-1 3-1-2-2-3-2-5 0-1 0-1 1-1l1 1v-2c-1 0-1 0-1-1h-1v-1-10z" class="M"></path><path d="M301 444l1-1 1-1 1 1v1c-1 1-2 0-3 0z" class="a"></path><path d="M301 444c1 0 2 1 3 0 0 2-1 5 0 7 0 1 0 2-1 3-1-2-2-3-2-5 0-1 0-1 1-1l1 1v-2c-1 0-1 0-1-1h-1v-1-1z" class="X"></path><path d="M318 463c1 0 0-3 1-3l1 1v-1h1c0-1 0-2 1-2v-1h-2c0-1 0-2 1-3 0-1 0-1 1-1h1c1 0 1-2 2-2l1 1v3h1v4c0 1 1 2 1 2v1l-1 1v1h0v4h0v2 2 5l1 1-1 1-1 2c1 1 1 2 1 4h0c1 0 1 0 2 1l1-1h2v1h1c1 1 0 1 0 2v1h-2v1c0 1 0 1-1 1l-2 1v-1h-1v1l-1 2h-3l-1 1 1 1c1 1 1 2 1 3l-1 1v-2l-1-1-1 1v-1 3c-1 0-2 0-2 1s-1 2 0 4v6c-1 2 0 2-1 3v1s-2 0-2 1v2l-2-1v-1h0c-1 0-1 0-1 1v1h-1c-1 0-2 0-3-1h0v-3h-1c-1-1-2-1-3-2v-1c1 0 1-1 2-2 0 0 0-1 1-2-1-2-1-3-1-5 0 0 0-1 1-1-1-1-1-2-1-2v-1c0-1 1-3 0-4-1 0-1 0-2-1h0l-1-2h1c0 1 0 2 1 2h1c0-2-1-5 0-7h0v-4l1 2h0c1-1 1-2 1-4h0c0-2 0-4 1-5v-1l-1-1c0-1 0-1 1-1v-1l-1-1c-1-2 0-3 0-5 1 0 1 0 1-1v-1h2l1 1h1c0-1 0-1-1-2 1-1 1-1 2-1h1v1h0c-1 1-1 1-1 2v1 2h2v-3l1-1z" class="O"></path><path d="M310 475v1 1h2c1 1 1 1 1 2h-1 0l-1-1c-1 1-1 1-1 2h-1 0c0-2 0-4 1-5z" class="L"></path><path d="M315 488h0c1-1 1-1 1-2l2 2h0v2 1c0 1 0 2 1 2h0c-1 3 0 4 0 6h-2-1-1 0v1l2 2s-1 2-2 2c-1-2-1-8 0-11h0l-1-1c0-1 0-2 1-4z" class="F"></path><path d="M318 490v1c0 1 0 2 1 2h0c-1 3 0 4 0 6h-2l-1-1c0-1 0-1 1-1v-5h0c0-1 0-2 1-2z" class="l"></path><path d="M317 499h2c-1 1-1 1 0 2 0 1-1 2 0 4v6c-1 2 0 2-1 3v1s-2 0-2 1v2l-2-1v-1h0v-3-2-2h1c-1-1-1-3-1-4l1-1h0c1 0 2-2 2-2l-2-2v-1h0 1 1z" class="X"></path><path d="M314 511v-2l1 2h0l1 1v2h2v1s-2 0-2 1v2l-2-1v-1h0v-3-2z" class="E"></path><path d="M316 514h2v1s-2 0-2 1v2l-2-1v-1-2c1 0 1 1 2 1v-1z" class="F"></path><path d="M317 499h2c-1 1-1 1 0 2 0 1-1 2 0 4v6c-2 0-2 0-3-1v-1h1v-1h-1c1-1 1-2 2-2-1-1-1-1-1-2l-1 1c0 1 0 2-1 2v-3h0c1 0 2-2 2-2l-2-2v-1h0 1 1z" class="k"></path><path d="M312 463l1 1h1c0-1 0-1-1-2 1-1 1-1 2-1h1v1h0c-1 1-1 1-1 2v1 2h2v-3l1-1c1 1 1 2 1 4h1 2v2l-1 1h-1c-1 1-1 2-1 3h1 1c1 1 1 1 1 2l-1 1v2c-1 1-2 0-1 2h1v2c-1 1-1 0-2 0 0 1-1 2-1 3 1 0 1 1 1 2l-1 1h0l-2-2c0 1 0 1-1 2h0l-1-1v-3-1-2l2-1h0c1-1 0-3 1-4v-1c-1-1-1-2 0-4v-1h-2v1h-1l-1-1-1 1-2-1v1l-1-1c-1-2 0-3 0-5 1 0 1 0 1-1v-1h2z" class="K"></path><path d="M310 463h2v2h-1l-1-1v-1z" class="H"></path><path d="M319 473h1v3c-1 0-1 0-1-1v-2z" class="E"></path><path d="M320 473h1c1 1 1 1 1 2l-1 1h-1v-3z" class="C"></path><path d="M311 466c1 0 1 0 2 1v3l-1 1-2-1 2-1c0-1-1-2-1-3z" class="U"></path><path d="M309 465c1 1 2 1 2 1 0 1 1 2 1 3l-2 1v1l-1-1c-1-2 0-3 0-5z" class="B"></path><path d="M317 475v-3h0 1v4c0 2 0 4 1 6l2-2v2c-1 1-1 0-2 0 0 1-1 2-1 3 1 0 1 1 1 2l-1 1h0l-2-2c0 1 0 1-1 2h0l-1-1v-3-1-2l2-1h0c1-1 0-3 1-4v-1z" class="M"></path><path d="M314 484v-1-2 1h1 1c0 1 1 2 0 2h-2z" class="G"></path><path d="M327 477l1 1-1 1-1 2c1 1 1 2 1 4h0c1 0 1 0 2 1l1-1h2v1h1c1 1 0 1 0 2v1h-2v1c0 1 0 1-1 1l-2 1v-1h-1v1l-1 2h-3l-1 1 1 1c1 1 1 2 1 3l-1 1v-2l-1-1-1 1v-1 3c-1 0-2 0-2 1-1-1-1-1 0-2 0-2-1-3 0-6h0c-1 0-1-1-1-2v-1-2l1-1c0-1 0-2-1-2 0-1 1-2 1-3 1 0 1 1 2 0v-2h0c0 1 1 2 1 2h2c1 0 2-1 2-1v-2c1-1 1-2 1-2z" class="D"></path><path d="M318 485h2c1 1 1 1 1 2l-1 1 1 1c-1 0-2 1-3 1v-2l1-1c0-1 0-2-1-2z" class="G"></path><path d="M327 489h-1c-1 0-1 0-3 1v-1c0-1 1-2 1-2v-2h2v-1-3c1 1 1 2 1 4h0v2 2z" class="B"></path><path d="M318 490c1 0 2-1 3-1 0 2 1 5 0 6l-1 1c0 1 0 1 1 1v3c-1 0-2 0-2 1-1-1-1-1 0-2 0-2-1-3 0-6h0c-1 0-1-1-1-2v-1z" class="E"></path><path d="M330 485h2v1h1c1 1 0 1 0 2v1h-2v1c0 1 0 1-1 1l-2 1v-1h-1v-2h0v-2-2c1 0 1 0 2 1l1-1z" class="C"></path><path d="M330 485h2v1c-2 2-3 1-5 3h0 0v-2-2c1 0 1 0 2 1l1-1z" class="f"></path><path d="M318 463c1 0 0-3 1-3l1 1v-1h1c0-1 0-2 1-2v-1h-2c0-1 0-2 1-3 0-1 0-1 1-1h1c1 0 1-2 2-2l1 1v3h1v4c0 1 1 2 1 2v1l-1 1v1h0v4h0v2 2 5s0 1-1 2v2s-1 1-2 1h-2s-1-1-1-2h0-1c-1-2 0-1 1-2v-2l1-1c0-1 0-1-1-2h-1-1c0-1 0-2 1-3h1l1-1v-2h-2-1c0-2 0-3-1-4z" class="Y"></path><path d="M323 454h1 0v1h-1v-1z" class="O"></path><path d="M326 455h1v4c0 1 1 2 1 2v1l-1 1c0-1 0-1-1-2h-1c-1-1-1-2-1-4h0 0 1 1v-2z" class="U"></path><path d="M307 486h1l1 1v1l1-1s1 0 1 1h1v1l1 1v2 3 10h1c0 1 0 3 1 4h-1v2 2 3c-1 0-1 0-1 1v1h-1c-1 0-2 0-3-1h0v-3h-1c-1-1-2-1-3-2v-1c1 0 1-1 2-2 0 0 0-1 1-2-1-2-1-3-1-5 0 0 0-1 1-1-1-1-1-2-1-2v-1c0-1 1-3 0-4-1 0-1 0-2-1h0l-1-2h1c0 1 0 2 1 2h1c0-2-1-5 0-7z" class="K"></path><path d="M310 505h3 0 1c0 1 0 3 1 4h-1v2 2 3c-1 0-1 0-1 1v1h-1c-1 0-2 0-3-1h0v-3l2-1h0v-2-1c-1-1-2-3-1-5z" class="C"></path><path d="M313 505h1c0 1 0 3 1 4h-1v2c-1 1-1 1-2 1v-1l1-2h0v-4z" class="H"></path><path d="M311 513l1 1v1s1 0 1 1v1 1h-1c-1 0-2 0-3-1h0v-3l2-1z" class="I"></path><path d="M311 513l1 1-3 3v-3l2-1z" class="M"></path><path d="M310 505h3 0v4h0l-1-1h-1c0 1 0 1 1 2 0 0-1 0-1 1v-1c-1-1-2-3-1-5z" class="Z"></path><path d="M312 489l1 1v2 3 10h0-3v-1h0v-1l-1-1c0-2 0-2 1-3l-1-1c1-1 0-2 0-3s0-1 1-2c0-1-1-2-1-3l1-1c1 1 1 1 1 2h0c1-1 1-1 1-2z" class="E"></path><path d="M310 493l1 1v4 3h0v1l-1 1-1-1c0-2 0-2 1-3l-1-1c1-1 0-2 0-3s0-1 1-2z" class="Z"></path><path d="M321 398c0-1 0-1-1-2v-1l1-1c1 0 1 0 2-1h0c1 0 1 0 2 1h0l-2 1v2c1 0 1 0 2-1l1 1 1-1c0 1 0 1 1 2h0v2h2v1 4h0l-2 1h0v1 6 2 3 1h0c0 2 0 3-1 4l-1 1h1s0 1 1 1v2c-1 0-1 0 0 1h0v5 4c0-1-1-1-1-2v-1c0-1 0-1-1-1l1-1v-1h-2c0-1 0-1-1-1s-1-1-1-1h-1v2c-1 0-1 0-2 1h1 2l1 1c0 1 0 1-1 2 0 1 1 2 1 3h-1s-1 0-1-1h-1c0 1 0 1-1 1l-1 1h0c2 1 2 1 3 1l-1 2h-2v-1c-1 1 0 3-2 4h0l-1 3s-1 0-1 1h1l-1 1h-1v-3l-1-1v2-3h-1c0-2-2-2-3-3l1-1c-1 0-1-1 0-2v-2h-2c-1-1-1-1-1-2 1 0 2-1 2-2l-1-1c0 1-1 1-1 1h-1v-2-1h0c0-2 0-5-1-6l-1-1h0l-1 1v1l-2-1v1h-1v-2-5-2-1c1-1 0-3 0-5v-3-2-1-4-3h1v-1l2 1c0 1 0 1-1 2h1c1-1 1-1 1-2h1v1h0v-1l1-1 1 2 1-1c0 1 0 1 1 2l1 1c1-1 1-2 2-3 3 1 2 0 4-1l1 1h1c-1 2 0 2 0 3l2-1 1-1z" class="D"></path><path d="M325 418l1 1c0 1 0 1-1 1h-1l-1-1 2-1z" class="O"></path><path d="M304 404h0l1-1v2c1 1 2 0 2 1v2c-1-1-2-1-2-1-1-1-1-2-1-2v-1z" class="d"></path><path d="M311 423v1h-1l1 1v1c-1 1-1 1-3 1v-3c1 0 2 0 3-1z" class="Q"></path><path d="M319 427c0 1 0 2 1 2l-1 1h-1 0v1l-1 1h-1s0-1-1-1l4-4z" class="J"></path><path d="M305 398h0v-1l1-1 1 2c0 1 0 1 1 2h2-2c-1 1 0 2-1 2s-1 0-2 1v-5z" class="B"></path><path d="M310 417l1 1v4 1c-1 1-2 1-3 1-1-2-1-3 0-5l2-2z" class="C"></path><path d="M310 417l1 1v4c-2-1-2-2-3-3l2-2z" class="M"></path><path d="M317 445c0-1-1-2-1-2v-1h0c1-1 0-2 0-3h1c1 0 1 0 2 1h0v-1c2 1 2 1 3 1l-1 2h-2v-1c-1 1 0 3-2 4h0z" class="V"></path><path d="M321 424v-4h0l1-1h1l1 1c-1 1-1 2-1 2 1 0 1 0 1-1l1 1c0 1-1 1-2 2h2v1h-2c0 1 0 1-1 1s0-1-1-2z" class="R"></path><path d="M323 416v2c1 0 1-1 2-1 0 0 0-1 1-1 0-1 1-1 2-1v3 1l-1-1h-1-1l-2 1h-1l-1 1h0v4c-1-2 0-3-1-5 0-1 0-1 1-2l1-1h0 1z" class="I"></path><path d="M310 437c-1-1-1-3 0-4l1-1v-1c-1 0-2 0-2-1s1-1 1-2c1 1 2 1 2 2v3c0 1 1 1 1 2l1 1v2 2c0 1 0 1-1 1l-3-2v-2z" class="R"></path><path d="M314 440c-1 0-1-1-2-1l1-1h1v2z" class="M"></path><path d="M315 417l1 1h2v-1c1 1 1 1 1 2s-1 2 0 3c-1 0-1 1-1 2l1 1v2l-4 4c-1 0-1 0-1-1-1 0-1-1 0-2v-2l-3-1h0l-1-1h1v-1-1-4h0c1 1 1 1 2 1v1l1-1c1 0 1-1 1-2z" class="C"></path><path d="M314 423l-2-1v-2l1-1v1c1 0 2 0 2 1s-1 1-1 2z" class="J"></path><path d="M311 418c1 1 1 1 2 1l-1 1v2l2 1c-1 0-1 1-2 1l-1 1h0l-1-1h1v-1-1-4h0z" class="E"></path><path d="M319 425c-1 1-2 1-2 2h0-1v-3c-1-1 0-3 0-4h1 1l-1 1v1h2c-1 0-1 1-1 2l1 1z" class="X"></path><path d="M301 396l2 1c0 1 0 1-1 2h1c1-1 1-1 1-2h1v1 5l-1 1h0v1s0 1 1 2c0 0 1 0 2 1h-1 0v4 1c0 1-1 1 0 1v9c-1-1-1-1-1-2-1 1-1 1-1 2l-1 1v1l-2-1v1h-1v-2-5-2-1c1-1 0-3 0-5v-3-2-1-4-3h1v-1z" class="K"></path><path d="M301 408c1 0 2 1 3 2 1 0 1 0 1-1 1 2 0 1 1 3v1l-2-2v3s-2 2-3 2c0-1 0-2 1-2 0-2-1-4-1-6z" class="W"></path><path d="M301 400l2 2h0l-1-1c-1 2 0 3 0 5 0-1 1-1 2-2v1s0 1 1 2c0 0 1 0 2 1h-1 0v4c-1-2 0-1-1-3 0 1 0 1-1 1-1-1-2-2-3-2 1-3-1-5 0-8z" class="R"></path><path d="M301 396l2 1c0 1 0 1-1 2h1c1-1 1-1 1-2h1v1 5l-1 1h0c-1 1-2 1-2 2 0-2-1-3 0-5l1 1h0l-2-2v-3-1z" class="W"></path><path d="M301 416c1 0 3-2 3-2v-3l2 2c0 1-1 1 0 1v9c-1-1-1-1-1-2-1 1-1 1-1 2l-1 1v1l-2-1c0-2 0-6 1-7h0l-1-1z" class="d"></path><path d="M309 399l1 1c1-1 1-2 2-3 3 1 2 0 4-1l1 1h1c-1 2 0 2 0 3l2-1v3 2h-1l-2 1v1c1 1 1 2 1 3l1 1c-1 0-1 1-1 1 1 1 1 1 1 2l-1 1h0s1 0 1 1c0 0 0 1-1 1v1 1h-2l-1-1c0 1 0 2-1 2l-1 1v-1c-1 0-1 0-2-1h0l-1-1h0v-2h0v-1c-1-1 0-3 0-5h1v-1h-1c-1-2-1-3-1-5h1v-3h-2c-1-1-1-1-1-2l1-1c0 1 0 1 1 2z" class="f"></path><path d="M315 405h1 1v1 1h-2l-1-1 1-1z" class="C"></path><path d="M316 418v-1c1-1 1-2 2-2v1 1 1h-2z" class="k"></path><path d="M310 408c3-2 1-3 2-6h1v3 1c0 1 1 1 1 2h-1c0 1 1 2 1 3v1 5h1c0 1 0 2-1 2l-1 1v-1c-1 0-1 0-2-1h0l-1-1h0v-2h0v-1c-1-1 0-3 0-5h1v-1h-1z" class="Q"></path><path d="M310 415c1 0 2 1 4 1h0l-1 1c-1 1-1 1-2 1h0l-1-1h0v-2h0z" class="C"></path><path d="M311 418c1 0 1 0 2-1l1-1v1h1c0 1 0 2-1 2l-1 1v-1c-1 0-1 0-2-1z" class="D"></path><path d="M310 408c3-2 1-3 2-6h1v3 1c0 1 1 1 1 2h-1c0 1 1 2 1 3v1s0 1-1 1h-1l-1 1v-1l1-1c-1 0-1-1-1-2 0 0 0-1-1-1h1v-1h-1z" class="V"></path><path d="M309 399l1 1c1-1 1-2 2-3 3 1 2 0 4-1l1 1h1c-1 2 0 2 0 3l2-1v3 2h-1l-2 1h-1-1l-1 1-1-1v-3h-1c-1 3 1 4-2 6-1-2-1-3-1-5h1v-3h-2c-1-1-1-1-1-2l1-1c0 1 0 1 1 2z" class="F"></path><path d="M316 398l1 1c0 1-1 2-1 3h-2c0-1 0-1-1-2 2 0 2 0 3-2z" class="d"></path><path d="M317 397h1c-1 2 0 2 0 3l2-1v3 2h-1l-2 1h-1-1c-1-1 0-2-1-3h2c0-1 1-2 1-3l-1-1 1-1z" class="i"></path><g class="C"><path d="M318 400l2-1v3 2h-1c-1-1-1-2-1-4z"></path><path d="M321 398c0-1 0-1-1-2v-1l1-1c1 0 1 0 2-1h0c1 0 1 0 2 1h0l-2 1v2c1 0 1 0 2-1l1 1 1-1c0 1 0 1 1 2h0v2h2v1 4h0l-2 1h0v1 6 2c-1 0-2 0-2 1-1 0-1 1-1 1-1 0-1 1-2 1v-2h-1 0l-1 1c-1 1-1 1-1 2h-1c0-1 0-1-1-2v-1c1 0 1-1 1-1 0-1-1-1-1-1h0l1-1c0-1 0-1-1-2 0 0 0-1 1-1l-1-1c0-1 0-2-1-3v-1l2-1h1v-2-3l1-1z"></path></g><path d="M326 397l1-1c0 1 0 1 1 2-1 1-1 1-1 2h-1v-3z" class="D"></path><path d="M328 407l-1 1h-2v-1c1-1 1-2 3-2v2z" class="O"></path><path d="M321 398c1 0 2 1 2 2-1 1 0 1 0 2h-3v-3l1-1z" class="J"></path><path d="M328 405v-5h2v1 4h0l-2 1h0v1-2z" class="c"></path><path d="M323 416v-1h2 0 0v-3s1 0 1-1l2 2v2c-1 0-2 0-2 1-1 0-1 1-1 1-1 0-1 1-2 1v-2zm0-16v-1h1c1 0 1 1 1 2l2 1v2h0c-1-1-1-1-2-1v2h-1-1v-2-1c0-1-1-1 0-2z" class="D"></path><path d="M320 402h3v1 2h1l-2 1-1 1h2v1l-1 1c0 1 1 1 1 2-1 0-1 0-1 1h1v4h-1 0l-1 1c-1 1-1 1-1 2h-1c0-1 0-1-1-2v-1c1 0 1-1 1-1 0-1-1-1-1-1h0l1-1c0-1 0-1-1-2 0 0 0-1 1-1l-1-1c0-1 0-2-1-3v-1l2-1h1v-2z" class="B"></path><path d="M320 402h3v1 2l-1-1h-2v-2zm1 5h2v1l-1 1c0 1 1 1 1 2-1 0-1 0-1 1h1v4h-1l-1-1c0-1 0-1 1-2-1 0-1 0-2-1 1-2 1-3 0-5h0 1z" class="Z"></path><path d="M312 294c2 0 2 0 4 2v-1h0c0 3 0 6 1 9v1c0 1 0 1 1 1 0-4-1-8 0-12h1c1 1 2 2 3 2v2h2c0-1 1-1 2-1v-1-1c1 0 2 1 2 1l1 1v1h1c2 0 3 0 4-1l-1-1c1 0 2 1 3 1l1-1h2c0 1 0 2-1 3h0l-1 1c1 1 1 2 0 3h0c1 1 1 2 1 3 1 1 1 0 2 2l-1 1c0 1-1 5 0 6h0l-1 2c0 1 0 2 1 2-1 3 0 6 0 9v1 2c0 1-1 3 0 4h0c-1 1-1 2-1 4h0v2c1 0 2-1 3-1v1 7h1v8 4 6 6c1 1 1 3 0 4v3c0 1 1 3 0 4 0 0-1 0-1 1-1 0-2-1-2-1h-1 0c-2 1-3 1-4 2s-1 1 0 2v3h-1-1c1 1 1 1 1 3-1 1-1 1-1 2h-1v-4l-1-1c-2 0-2 0-2 1v2l-1 3 1 1v1h0c-1-1-1-1-1-2l-1 1-1-1c-1 1-1 1-2 1v-2l2-1h0c-1-1-1-1-2-1h0c-1 1-1 1-2 1l-1 1v1c1 1 1 1 1 2l-1 1-2 1c0-1-1-1 0-3h-1l-1-1c-2 1-1 2-4 1-1 1-1 2-2 3l-1-1c-1-1-1-1-1-2l-1 1-1-2-1 1v1h0v-1h-1c0 1 0 1-1 2h-1c1-1 1-1 1-2l-2-1v1h-1v3 4 1 2c-1 1-2 1-3 1v-1h-1c-1-3 0-3 1-5-1-1-1-1-1-2h0v-3h-1l1-1c0-2-1-3-2-5 1-1 1-2 1-2l1-1v-3-3h0c-1-2-1-3-1-4h2v9h1v2h1c0-1 1-1 1-1v-5-12c0-2-1-4 0-6v-1c-1-1-1-3-1-4v-3c1 0 1 1 1 1 1 1 1 1 1 2h1c1-1 1-2 2-2 0 0 1 1 1 2l1 1c1-3 0-7 0-11v-15-1c-1 0-1-1-1-1 0-1-1-1-1-2l-2 1h0l-1-1v-4h0l-1-1c0-2 1-6 0-9h0v-6-3h2l1-1h0v2c1 0 2-1 2-2 0 1 0 2 1 3h1c1 0 1-1 2-2 0 1 0 2 1 2v1l1-1v-1h1l1-1-1-2v-1l-2 1c1-1 1-2 1-4l1-3h0v-3-2z" class="T"></path><path d="M336 361h-1v-2h1v2h0zm-11 2h0c1 0 1 1 2 2h0l-1 1h-1v-3z" class="U"></path><path d="M334 345h-1c0-2 0-2 1-3h0l1 4-1-1z" class="E"></path><path d="M329 336h0c1 1 1 2 1 3h0-2c0-1 0-2 1-3z" class="J"></path><path d="M334 345l1 1c0 1 0 3-1 4h0c-1-2-1-1 0-3v-2z" class="I"></path><path d="M328 339h2 1c1 1 1 2 1 3-1-1-1-1-2-1v1l-1-1v1h-1v-3z" class="V"></path><path d="M336 361h1v4h-1l-1-1v-3h1 0z" class="G"></path><path d="M329 336c1-1 1-1 2-1h0v-2c1 0 2 1 3 1v1l-3 2-1-1v2 1c0-1 0-2-1-3z" class="R"></path><path d="M328 368h0v-1s1-1 2-1v1h0v3 4l-1-1c0-1 0-3-1-5z" class="C"></path><path d="M330 342v-1c1 0 1 0 2 1v3c0 1-2 2-2 2v-5z" class="B"></path><path d="M329 357l1 1c0 1-1 2-1 3 0 2 1 3 1 4h-1-1c0-2-1-5 0-6l1-2z" class="Q"></path><path d="M330 318l2-2c0 2-1 3-1 5s0 2-1 3h-1v-1c0-1-1-1-1-2 1-1 1-2 2-3z" class="I"></path><path d="M328 368c1 2 1 4 1 5l-1 1v1 2h-2 0c-1-1-2-1-2-3 1 0 1-1 1-2h1 1v1c2-1 1-3 1-5z" class="G"></path><path d="M329 342v-1l1 1v5 6c-1 1 0 3 0 5l-1-1-1-1c1-1 1-2 0-3 0-2 0-3 1-4v-7z" class="Z"></path><path d="M329 336v-2c-1 0-1-1-1-2 1-1 0-1 1-2h1c0-1 0-1 1-2h0 1v3c0 1 1 1 2 1v3-1c-1 0-2-1-3-1v2h0c-1 0-1 0-2 1h0z" class="E"></path><path d="M333 369l1-1-2-1c0-2 1-4 2-5l1-1v3 3h1 1v1 3c-1 0-1 2-1 2-1-1-2-1-3-2v-2h0z" class="C"></path><path d="M336 367h1v1 3c-1 0-1 2-1 2-1-1-2-1-3-2v-2h0c1 1 1 1 1 2l1-1v-3h1z" class="H"></path><path d="M335 367h1v1 2 1s-1 0-1-1v-3z" class="B"></path><path d="M336 319c1 1 1 5 1 7h-1c-1 0-1 0-1-1-1 0-1 0-1 1h-3c-1 0-1 0-1 1h-2c0-1-1-2 0-3h1 1c1-1 1-1 1-3h1l-1 1 1 1c1-1 1-1 3 0v-3c0-1 1-1 1-1z" class="G"></path><path d="M323 348l2 1v1h0c0 2 0 3 1 5-1 1-1 1-1 2 0 2 1 2 1 4l-3 1-1-1v-1c0-1 1-1 1-2v-1l1-1c-1-1-2-1-2-2v-2c1-1 1-1 1-2h-1c0-1 0-2 1-2z" class="b"></path><path d="M318 340l1 1 1 1h1 1v1c1 0 1 0 2 1h0l2 4-1 1-2-1c-1 0-1 1-1 2h-1l-1-1h-1v-6h-1v-3z" class="G"></path><path d="M321 342h1v1c0 2 0 3 1 4v1c-1 0-1 1-1 2h-1l-1-1c0-2 1-4 1-7z" class="Z"></path><path d="M322 343c1 0 1 0 2 1h0l2 4-1 1-2-1v-1c-1-1-1-2-1-4z" class="H"></path><path d="M323 331v-3h2v1c0 1 0 1-1 2 1 0 1 0 2 1v4c0 2 1 3-1 5 0 1-1 1-1 1v2h0c-1-1-1-1-2-1v-1-2c1-3 1-5 1-9z" class="O"></path><path d="M320 330l1 1v1 2h0c1-2 1-2 2-3 0 4 0 6-1 9v2h-1-1l-1-1-1-1h0v-3c1-1 2-1 2-3h-2v-1l1-1v1l1-1h-1c0-1 0-1 1-2z" class="C"></path><path d="M318 340h1c1-1 0-2 1-3l1 1c0 1 0 1 1 2v2h-1-1l-1-1-1-1h0z" class="N"></path><path d="M333 369v2c1 1 2 1 3 2l2 2h0 0v4l-1-1c-1 0-1 0-2-1l-1 1v-1l-1 1h-3v-4h0v-4c1 0 2 0 3-1z" class="C"></path><path d="M335 377l3-2v4l-1-1c-1 0-1 0-2-1z" class="M"></path><path d="M333 369v2c-1 2-1 4 0 6h1l-1 1h-3v-4h0v-4c1 0 2 0 3-1z" class="N"></path><path d="M316 326v-2h1l2 2c0 2-1 2 1 4-1 1-1 1-1 2h1l-1 1v-1l-1 1v1h2c0 2-1 2-2 3v3h0v3h0l-1 1h-1v-3h-1c0 1 0 1-1 2 0-1 0-2-1-4 1 1 1 1 2 0v-1l1-1-1-2-1-1h1c-1-1-1-2-1-3h-1c1-1 1-2 1-2 1-1 0-1 0-2l1-1h1z" class="D"></path><path d="M314 327l1-1c0 2 1 5 1 7v1 3l-1-2-1-1h1c-1-1-1-2-1-3h-1c1-1 1-2 1-2 1-1 0-1 0-2z" class="c"></path><path d="M336 343v-10c1-1 1-1 3-2 0 1-1 3 0 4h0c-1 1-1 2-1 4h0v2c1 0 2-1 3-1v1 7 7h0l-1-1h-1c-1 1-1 2-1 3v-4s-1-1-2-1c0-2-1-7 0-9z" class="D"></path><path d="M336 343c2 2 1 6 1 9h-1c0-2-1-7 0-9z" class="C"></path><path d="M338 341c1 0 2-1 3-1v1 7 7h0l-1-1h-1c-1-3-1-5 0-8l-1-1v-4z" class="L"></path><path d="M340 349h1v3c-1 0-1-1-2-2l1-1zm-2-4c1-1 1-1 2-1v2h-1l-1-1z" class="I"></path><path d="M320 349l1 1h1 1c0 1 0 1-1 2v2c0 1 1 1 2 2l-1 1v1c0 1-1 1-1 2v1l1 1 2 1v1c-1 1-2 0-2 2v2l1-1h1c0 1-1 1-1 2v2l1 1c0 1 0 2-1 2 0 2 1 2 2 3 0 1 0 1-1 2l-1-1-1 1h0c-1-1-1 0-2 0-1-1-1-1-1-2h-1l-1-1v-3-2-5-1-3c1-1 1-6 1-7v-6h1z" class="C"></path><path d="M321 369h0v1c-1 1-1 1-1 3h-2v-2h2c0-1 0-1 1-2z" class="D"></path><path d="M323 374h1c0 2 1 2 2 3 0 1 0 1-1 2l-1-1c-1 0-1-1-1-1 0-1 0-1-1-2l1-1z" class="O"></path><path d="M318 373h2c1 1 1 2 1 3l-1 1h-1l-1-1v-3z" class="T"></path><path d="M323 368l1-1h1c0 1-1 1-1 2v2l1 1c0 1 0 2-1 2h-1c-1-2 0-4 0-6z" class="S"></path><path d="M320 349l1 1h1 1c0 1 0 1-1 2v2c-1 1-1 1-1 2-1 1-1 7 0 8v1c0 1-1 1-1 3 0 0 1 0 1 1-1 1-1 1-1 2h-2v-5-1-3c1-1 1-6 1-7v-6h1z" class="U"></path><path d="M314 343c1-1 1-1 1-2h1v3h1l1-1h0 1v6 6c0 1 0 6-1 7-1 0-1 0-2 1-1 0-1 0-2 1h0 0c-1 0-1-1-1-1-2-3 0-6-1-9 0-1-1-1-1-1-1-1-1-4-1-5v-2h1 1 0l1 1c0-1 0-1 1-2v-2z" class="C"></path><path d="M313 363c1-3 0-9 0-12h1v4 7 2h0 0c-1 0-1-1-1-1z" class="T"></path><path d="M314 355v-4h1c1 2 0 3 0 5v5c0 1 0 1-1 1v-7z" class="R"></path><path d="M314 343c1-1 1-1 1-2h1v3h1l1-1v5l-1 1s-1 0-1 1h0c0-2 1-3 0-5h-1v1c-1 1 0 1 0 1v1h-2v-1c0-1 0-1 1-2v-2z" class="B"></path><path d="M318 343h1v6 6h-1l-1 1c1 0 1 1 1 1l-1 1-1-1v-2c0-1-1-3 0-4l1-2 1-1v-5h0z" class="M"></path><path d="M316 351h2v1l-1 1-1 1v-3z" class="J"></path><path d="M341 348h1v8 4 6 6c1 1 1 3 0 4v3c0 1 1 3 0 4 0 0-1 0-1 1-1 0-2-1-2-1h-1 0-2v-1c-1 0-1-1-2-1l1-1c-1-1-2-1-2-2l1-1v1l1-1c1 1 1 1 2 1l1 1v-4h0 0l-2-2s0-2 1-2v-3c1 0 1-1 1-2 1-1 0-3 0-4v-5c0-1 0-2 1-3h1l1 1h0v-7z" class="I"></path><path d="M337 368c1 0 1-1 1-2v8 1l-2-2s0-2 1-2v-3z" class="K"></path><path d="M341 365l1 1v6 4h0-1c0-1-1-2 0-3v-1c-1-1-1-2 0-4-1-1-1-2 0-3z" class="f"></path><path d="M341 348h1v8 4 6l-1-1v-10h0v-7z" class="a"></path><path d="M338 375h1v4l-1 2 1 1c0 1 0 1-1 1h0-2v-1c-1 0-1-1-2-1l1-1c-1-1-2-1-2-2l1-1v1l1-1c1 1 1 1 2 1l1 1v-4h0z" class="R"></path><path d="M338 375h1v4h0-1v-4h0z" class="X"></path><path d="M335 380l1-1 2 2 1 1c0 1 0 1-1 1h0-2v-1c-1 0-1-1-2-1l1-1z" class="H"></path><path d="M311 353s1 0 1 1c1 3-1 6 1 9 0 0 0 1 1 1h0 0c1-1 1-1 2-1 1-1 1-1 2-1v3 1 5 2 3l1 1c-1 2-1 3-1 5h-2 0c-1 1-1 1-1 0l-1 1-1-1c-1 1-1 1-2 1h-1 0l-2-2v-2l1-1c0-1-1-1-1-1 0-1 1-2 1-3h-1v-4c1-1 1-2 1-3l1 1v-1-5l1-9z" class="B"></path><path d="M314 364c1-1 1-1 2-1 0 1 1 1 1 2h-1c-1 0-1 0-2-1h0z" class="J"></path><path d="M316 363c1-1 1-1 2-1v3h-1c0-1-1-1-1-2z" class="X"></path><path d="M309 374c1 0 2 1 3 1 1 1 1 3 1 5h-1 0v1h1 0v1c-1 1-1 1-2 1h-1 0l-2-2v-2l1-1c0-1-1-1-1-1 0-1 1-2 1-3z" class="I"></path><path d="M308 381c1-1 2-2 3-2v2h2 0v1c-1 1-1 1-2 1h-1 0l-2-2z" class="i"></path><path d="M315 382h0v-2l-1-1v-8c0-1 0-2 1-3v-1h2v-1h1v5 2 3l1 1c-1 2-1 3-1 5h-2 0c-1 1-1 1-1 0z" class="E"></path><path d="M329 373l1 1h0v4h3c0 1 1 1 2 2l-1 1c1 0 1 1 2 1v1h2c-2 1-3 1-4 2s-1 1 0 2v3h-1-1c1 1 1 1 1 3-1 1-1 1-1 2h-1v-4l-1-1c-2 0-2 0-2 1v2l-1 3 1 1v1h0c-1-1-1-1-1-2l-1 1-1-1c-1 1-1 1-2 1v-2l2-1h0c-1-1-1-1-2-1h0c-1 1-1 1-2 1l-1 1v1c1 1 1 1 1 2l-1 1-2 1c0-1-1-1 0-3h0v-6-1h0c0-2 1-2 1-4h1 0-1v-2c-1-1-1-1-1-2 0-2 0-3 1-5h1c0 1 0 1 1 2 1 0 1-1 2 0h0l1-1 1 1c1-1 1-1 1-2h0 2v-2-1l1-1z" class="B"></path><path d="M328 384c0-1 0-1 1-2h1 0s-1 2 0 2c0 1 0 2 1 3h-1c-1 0-1 0-2-1l1-2h-1z" class="k"></path><path d="M326 377h0l2 2s0 1 1 1h1v2h-1c-1 1-1 1-1 2l-1-1c0-1-1-1-2-1v-2h1 0l-1-1h0c1-1 1-1 1-2z" class="C"></path><path d="M330 382h1l1 1 1-1c1 1 1 0 3 0v1h2c-2 1-3 1-4 2s-1 1 0 2h-3c-1-1-1-2-1-3-1 0 0-2 0-2z" class="G"></path><path d="M320 386c1 0 2 0 2 1h3v1h0c-1 0-1 1-1 1l-1 1h-1v1 1h-1-1v-2h-1-1 0c0-2 1-2 1-4h1z" class="I"></path><path d="M329 373l1 1h0v4h3c0 1 1 1 2 2l-1 1c1 0 1 1 2 1-2 0-2 1-3 0l-1 1-1-1h-1 0v-2h-1c-1 0-1-1-1-1l-2-2h2v-2-1l1-1z" class="F"></path><path d="M329 373l1 1h0v3h-1c-1-1-1-1-1-2v-1l1-1z" class="M"></path><path d="M330 380l1-1h1l1 1c-1 1-1 2-2 2h-1 0v-2z" class="N"></path><path d="M319 377h1c0 1 0 1 1 2 1 0 1-1 2 0h0l1-1 1 1h0l1 1h0-1v2c-1 1-1 1-1 2h1 1c0 1-1 1-1 2v1h-3c0-1-1-1-2-1h0-1v-2c-1-1-1-1-1-2 0-2 0-3 1-5z" class="B"></path><path d="M320 384c0-1 0-2 1-3h1c1 1 1 2 0 3h-2z" class="X"></path><path d="M322 381c1-1 1-2 2-1h1 0v2c-1 1-1 1-1 2v1h-1l-1-1c1-1 1-2 0-3z" class="D"></path><path d="M324 384h1 1c0 1-1 1-1 2v1h-3c0-1-1-1-2-1h0v-2h2l1 1h1v-1z" class="C"></path><path d="M309 332h1l1-1h1l1 1v-1h1c0 1 0 2 1 3h-1l1 1 1 2-1 1v1c-1 1-1 1-2 0 1 2 1 3 1 4v2c-1 1-1 1-1 2l-1-1h0-1-1v2c0 1 0 4 1 5l-1 9v5 1l-1-1c0 1 0 2-1 3v4h1c0 1-1 2-1 3 0 0 1 0 1 1l-1 1c-1-1-1-2-1-3-1 1-1 2-1 4h0l1 1v1c-1 1-1 1-2 1v1-1c-1 0-1 0-2-1 0 0 0-1-1-1h-1c1 0 1-1 1-2 0 0-1-1-1-2h0-1v-6c0-2-1-4 0-6v-1c-1-1-1-3-1-4v-3c1 0 1 1 1 1 1 1 1 1 1 2h1c1-1 1-2 2-2 0 0 1 1 1 2l1 1c1-3 0-7 0-11v-15-1-1h1s1-1 2-1h0z" class="U"></path><path d="M309 348c0-2 0-3 1-4h1c-1 1-1 1-1 2h0v2c0 1 0 4 1 5l-1 9v5 1l-1-1v-7-4h0v-8z" class="R"></path><path d="M309 332h1l1-1h1l1 1v-1h1c0 1 0 2 1 3h-1l1 1 1 2-1 1v1c-1 1-1 1-2 0 1 2 1 3 1 4v2c-1 1-1 1-1 2l-1-1h0-1-1 0c0-1 0-1 1-2h-1c-1 1-1 2-1 4v-7-1-4c1-1 0-1 0-2v-2h0z" class="I"></path><path d="M315 338h-1 0-1v-3l1-1 1 1 1 2-1 1z" class="a"></path><path d="M309 341l2-2h1c1 2 0 4 0 5v2h-1-1 0c0-1 0-1 1-2h-1c-1 1-1 2-1 4v-7z" class="K"></path><path d="M299 357c1 0 1 1 1 1 1 1 1 1 1 2h1c1-1 1-2 2-2 0 0 1 1 1 2l1 1h0c0 1 0 1-1 2h0l-1-2h-1l-2 3v1h1l1-1c1 0 1 1 2 1 0 1 0 2-1 3l1 1c1 0 1 0 1-1h1v2h-1-1v1l2 1v3 1c-1 1-1 2-1 4h0l1 1v1c-1 1-1 1-2 1v1-1c-1 0-1 0-2-1 0 0 0-1-1-1h-1c1 0 1-1 1-2 0 0-1-1-1-2h0-1v-6c0-2-1-4 0-6v-1c-1-1-1-3-1-4v-3z" class="H"></path><path d="M302 372h2v2c1 0 1 1 1 2h-1c1 1 1 2 1 3h-1c0 1 1 1 1 2 1 0 1 0 1-1l1 1v1c-1 1-1 1-2 1v1-1c-1 0-1 0-2-1 0 0 0-1-1-1h-1c1 0 1-1 1-2 0 0-1-1-1-2h0l1-1h-1v-1l1-2v-1z" class="M"></path><path d="M302 373h1v1c0 1 0 1 1 2l-1 2h0c-1-1-1-1-1-2h-1v-1l1-2z" class="S"></path><path d="M299 357c1 0 1 1 1 1 1 1 1 1 1 2h1c1-1 1-2 2-2 0 0 1 1 1 2l1 1h0c0 1 0 1-1 2h0l-1-2h-1l-2 3v1h1 0l-1 1v1h1l1 1h0c0 2 0 3-1 4v1l-1 2v1h1l-1 1h-1v-6c0-2-1-4 0-6v-1c-1-1-1-3-1-4v-3z" class="D"></path><path d="M302 367l1 1h0c0 2 0 3-1 4h-1 0s-1 0-1-1 1-2 2-4z" class="g"></path><path d="M300 371v6h1 0c0 1 1 2 1 2 0 1 0 2-1 2h1c1 0 1 1 1 1 1 1 1 1 2 1v1-1c1 0 1 0 2-1v-1l-1-1h0c0-2 0-3 1-4 0 1 0 2 1 3v2l2 2h0 1c1 0 1 0 2-1l1 1 1-1c0 1 0 1 1 0h0 2c0 1 0 1 1 2v2h1 0-1c0 2-1 2-1 4h0v1 6h0-1l-1-1c-2 1-1 2-4 1-1 1-1 2-2 3l-1-1c-1-1-1-1-1-2l-1 1-1-2-1 1v1h0v-1h-1c0 1 0 1-1 2h-1c1-1 1-1 1-2l-2-1v1h-1v3 4 1 2c-1 1-2 1-3 1v-1h-1c-1-3 0-3 1-5-1-1-1-1-1-2h0v-3h-1l1-1c0-2-1-3-2-5 1-1 1-2 1-2l1-1v-3-3h0c-1-2-1-3-1-4h2v9h1v2h1c0-1 1-1 1-1v-5-12z" class="H"></path><path d="M298 390l1 1c1 1 1 1 1 3h-1c-1 0-1 0-2-1 0-1 1-2 1-3z" class="a"></path><path d="M294 391h2v-2h1l1 1c0 1-1 2-1 3v2l-1 1c0-2-1-3-2-5z" class="O"></path><path d="M301 381h1c1 0 1 1 1 1 1 1 1 1 2 1v1 2c0 1-1 1-1 1h0-2c-2-1 0-2 0-3s-1-2-1-3z" class="f"></path><path d="M296 396l1-1h2 1v1c-1 1 0 3 0 4v4 1 2c-1 1-2 1-3 1v-1h-1c-1-3 0-3 1-5-1-1-1-1-1-2h0v-3h-1l1-1z" class="L"></path><path d="M297 407c0-1 1-1 2-2h1v2c-1 1-2 1-3 1v-1zm0-12h2 1v1c-1 1 0 3 0 4v4c-1 0-2 0-3-1v-1c1 0 1 0 2 1v-1l-2-2c1-1 1-2 2-3l-2-1v-1z" class="W"></path><path d="M302 395c-1-1-1-1-1-2v-3s1 0 1-1c1 1 2 1 3 2l1 1 1 1h0 2 2c0 1-1 1-1 1v1l-1 4c-1-1-1-1-1-2l-1 1-1-2-1 1v1h0v-1h-1c0 1 0 1-1 2h-1c1-1 1-1 1-2l-2-1 1-1z" class="X"></path><path d="M309 393h2c0 1-1 1-1 1v1l-1 4c-1-1-1-1-1-2-1-1-1-3-1-4h2z" class="N"></path><path d="M302 395c-1-1-1-1-1-2v-3s1 0 1-1c1 1 2 1 3 2v3 1l-1-1v-1h-1v1 1h-1z" class="W"></path><path d="M306 380h0c0-2 0-3 1-4 0 1 0 2 1 3v2l2 2h0 1c1 0 1 0 2-1l1 1 1-1c0 1 0 1 1 0h0 2c0 1 0 1 1 2v2h1 0-1c0 2-1 2-1 4h0v1 6h0-1l-1-1c-2 1-1 2-4 1-1 1-1 2-2 3l-1-1 1-4v-1s1 0 1-1h-2c0-2-1-2-2-3 0-1 0-2-1-2 0-1-1-1-2-1 0 0 1 0 1-1v-2-1c1 0 1 0 2-1v-1l-1-1z" class="Z"></path><path d="M314 385h2v1l2-1s0 1 1 1c0 2-1 2-1 4h-1c0-1 0-2-1-3h-1s-1-1-1-2z" class="o"></path><path d="M309 393l2-1c0-1 1-2 2-3 0 0 0-1 1-1v3l1 2-1 1v2h-1v-1c-1 0-1 1-1 1h-1l-1-1v-1s1 0 1-1h-2z" class="I"></path><path d="M314 394s-1 0-1 1l-1-1v-1l2-2 1 2-1 1z" class="H"></path><path d="M315 393h1 1v-2h1v6h0-1l-1-1c-2 1-1 2-4 1-1 1-1 2-2 3l-1-1 1-4 1 1h1s0-1 1-1v1h1v-2l1-1z" class="X"></path><path d="M306 380h0c0-2 0-3 1-4 0 1 0 2 1 3v2l2 2h0 1c1 0 1 0 2-1l1 1 1-1c0 1 0 1 1 0h0 2c0 1 0 1 1 2v2h1 0-1c-1 0-1-1-1-1l-2 1v-1h-2v1l-1-1c-2 0-3 0-4 1h0c0 1-1 3-2 4 0-1 0-2-1-2 0-1-1-1-2-1 0 0 1 0 1-1v-2-1c1 0 1 0 2-1v-1l-1-1z" class="G"></path><path d="M305 384h2v2h2c0 1-1 3-2 4 0-1 0-2-1-2 0-1-1-1-2-1 0 0 1 0 1-1v-2z" class="I"></path><path d="M312 294c2 0 2 0 4 2v-1h0c0 3 0 6 1 9v1c0 1 0 1 1 1 0-4-1-8 0-12h1c1 1 2 2 3 2v2h2c0-1 1-1 2-1v-1-1c1 0 2 1 2 1l1 1v1h1c2 0 3 0 4-1l-1-1c1 0 2 1 3 1l1-1h2c0 1 0 2-1 3h0l-1 1c1 1 1 2 0 3h0c1 1 1 2 1 3 1 1 1 0 2 2l-1 1c0 1-1 5 0 6h0l-1 2v-2l-1-1-1 2c-1 1-1 0-2 1l1 1 1 1s-1 0-1 1v3c-2-1-2-1-3 0l-1-1 1-1h-1c0-2 1-3 1-5l-2 2-1-1s-1 0-1-1h-1l-1-2-1 1c0 1 1 2 1 3v6 4l1 1s0 1-1 1v2c-1-1-1-1-2-1 1-1 1-1 1-2v-1h-2v3c-1 1-1 1-2 3h0v-2-1l-1-1c-2-2-1-2-1-4l-2-2h-1v2h-1l-1 1c0 1 1 1 0 2 0 0 0 1-1 2v1l-1-1h-1l-1 1h-1 0c-1 0-2 1-2 1h-1v1c-1 0-1-1-1-1 0-1-1-1-1-2l-2 1h0l-1-1v-4h0l-1-1c0-2 1-6 0-9h0v-6-3h2l1-1h0v2c1 0 2-1 2-2 0 1 0 2 1 3h1c1 0 1-1 2-2 0 1 0 2 1 2v1l1-1v-1h1l1-1-1-2v-1l-2 1c1-1 1-2 1-4l1-3h0v-3-2z" class="B"></path><path d="M328 304v-1h1 0v2h-1v-1z" class="K"></path><path d="M324 306v-4h1v1 3l-1-1v1z" class="G"></path><path d="M326 314h0c1-1 1-1 1-2h0c1 1 1 2 1 3l-1 1-1-2z" class="E"></path><path d="M332 314c-1 1-2 1-3 1 0-1 0-1 1-3h1c0 1 1 1 1 2z" class="F"></path><path d="M331 299h0 0c0 2-1 4-2 4h0v-4h2zm-9 7l1 1c0 2 0 4-1 6 0-2-1-2-1-4h0c0-1 0-2 1-3z" class="R"></path><path d="M312 305h2c0 1-1 2-1 4v4h0v3h-1c0-1-1-2 0-4v-3l1-1-1-2v-1z" class="E"></path><path d="M321 299c1 0 1 0 2 1v6 1l-1-1h0c-1 0-1 0-2-1v-1-1c1-1 1-1 1-3v-1z" class="L"></path><path d="M333 296c1 0 2 1 3 1l1-1h2c0 1 0 2-1 3h0-4c-1-1-2-1-3 0h-2v-1h1c2 0 3 0 4-1l-1-1z" class="O"></path><path d="M326 297v1c0 2 1 3 0 5h-1v-1h-1v4h-1v-6c-1-1-1-1-2-1h1 0c1 0 1-1 2-1 0-1 1-1 2-1z" class="N"></path><path d="M326 297v1l-1 1v2c-1-1-1-1-2-1-1-1-1-1-2-1h1 0c1 0 1-1 2-1 0-1 1-1 2-1z" class="H"></path><path d="M326 296v-1c1 0 2 1 2 1l1 1v1 1 4h-1v1h0c-1 1-1 1-2 1v-2c1-2 0-3 0-5v-1-1z" class="F"></path><path d="M329 297v1 1 4h-1v1h0c0-1-1-3-1-5l1-1h-1l1-2 1 1z" class="G"></path><path d="M312 294c2 0 2 0 4 2v-1h0c0 3 0 6 1 9v1l-1 1h0c-2-1-1-3-1-5h-1v1c-1 1-1 1-2 1v-4h0v-3-2z" class="C"></path><path d="M312 296h1c0 1 0 3 1 4v2c-1 1-1 1-2 1v-4h0v-3z" class="J"></path><defs><linearGradient id="AS" x1="305.774" y1="319.403" x2="313.6" y2="318.373" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#363536"></stop></linearGradient></defs><path fill="url(#AS)" d="M306 310h1c1 0 1-1 2-2 0 1 0 2 1 2v1l1-1v-1h1v3c-1 2 0 3 0 4h1v-3c0 1 0 2 1 3v1c0-2 0-2 1-4h2v4l-2 2-1-1v1h-1v-1h-1v2 1c-1-1-1-1-1-2h-1l-1 2s0 1 1 1c-1 1-1 1-1 2v1 1c-1 0-2 1-3 1h0v-6-5-1-5z"></path><path d="M306 310h1c1 0 1-1 2-2 0 1 0 2 1 2v1c-1 1-1 1-1 2 0 0-1 0-1-1h-2v3-5z" class="P"></path><path d="M310 311l1-1v-1h1v3c-1 2 0 3 0 4-1 0-2 1-3 1l-1-1 1-3c0-1 0-1 1-2z" class="I"></path><path d="M310 311l1-1v-1h1v3h-2l-1 1c0-1 0-1 1-2z" class="B"></path><path d="M309 326v-1-1c0-1 0-1 1-2-1 0-1-1-1-1l1-2h1c0 1 0 1 1 2v-1-2h1v1 3h1v-1h1v1c0 2 1 3 1 4h-1l-1 1c0 1 1 1 0 2 0 0 0 1-1 2v1l-1-1h-1l-1 1h-1 0c-1 0-2 1-2 1h-1v1c-1 0-1-1-1-1 0-1-1-1-1-2l-2 1h0l-1-1c1-1 1-2 1-3h0c1-1 1-2 1-2 1 0 2 0 3 1h0 0c1 0 2-1 3-1z" class="C"></path><path d="M306 327c1 0 2-1 3-1h0 2c-1 1-1 1-2 1s-1 1-1 1l-1 1c-1-1 0-1-1-2z" class="D"></path><path d="M314 321h1v1c0 2 1 3 1 4h-1l-1 1v-4-2z" class="i"></path><path d="M302 328c1-1 1-2 1-2 1 0 2 0 3 1h0 0c1 1 0 1 1 2l1-1s0-1 1-1v3c0 1 1 1 0 2h0c-1 0-2 1-2 1h-1v1c-1 0-1-1-1-1 0-1-1-1-1-2l-2 1h0l-1-1c1-1 1-2 1-3h0z" class="T"></path><path d="M306 331h0v-2c2 0 2 0 3 1 0 1 1 1 0 2h0c-1 0-2 1-2 1h-1v-2z" class="b"></path><path d="M302 328l2 2c1-1 1-1 2 0v1 2 1c-1 0-1-1-1-1 0-1-1-1-1-2l-2 1h0l-1-1c1-1 1-2 1-3z" class="p"></path><path d="M331 312l-1-1v1c0-1 0-2 1-3 0 0-1-1-1-2h5v-1-1h-1 0c-1-1-1-1-1-2 1-1 2-1 2-1l2-2v3h0c1 1 1 2 1 3 1 1 1 0 2 2l-1 1c0 1-1 5 0 6h0l-1 2v-2l-1-1-1 2c-1 1-1 0-2 1l1 1 1 1s-1 0-1 1v3c-2-1-2-1-3 0l-1-1 1-1h-1c0-2 1-3 1-5h-1l1-2c0-1-1-1-1-2z" class="N"></path><path d="M336 312h1v2h-2c0-1 0-1 1-2z" class="F"></path><path d="M333 315c0-1 0-2 1-3s1-1 2 0c-1 1-1 1-1 2-1 1-1 1-2 1z" class="H"></path><path d="M333 315c1 0 1 0 2-1h2l-1 2c-1 1-1 0-2 1l1 1c-1 1-1 1-2 0 0-1 0-1 1-1 0-1-1-1-1-1v-1z" class="C"></path><path d="M302 308l1-1h0v2c1 0 2-1 2-2 0 1 0 2 1 3v5 1 5 6h0c-1-1-2-1-3-1 0 0 0 1-1 2h0c0 1 0 2-1 3v-4h0l-1-1c0-2 1-6 0-9h0v-6-3h2z" class="X"></path><path d="M306 321v-3c-1 0-1 1-3 1h0c0-2 0-3 1-4 1 0 1 1 2 1v5z" class="F"></path><path d="M302 308v6 1c-1 2 0 3 0 5v1c-1 2-1 5 0 6v1h0c0 1 0 2-1 3v-4h0l-1-1c0-2 1-6 0-9h0v-6-3h2z" class="H"></path></svg>